--
-- 表名: GossipData后处理
--

local TopData = {
    data = {
        [20000000] = {{['Conditions'] = {{['TaskID'] = 99000901, ['kind'] = 'IsQuestActivated', }, }, ['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023935488'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 2, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023935744'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 3, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023936000'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 4, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023936256'),['Voice'] = '', }, 
        },
        [20000001] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000001, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023936512'),['Voice'] = '', }, 
        },
        [20000002] = {{['Conditions'] = {{['kind'] = 'AllRandom', ['maxValue'] = 100, ['value'] = 80, }, }, ['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000002, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023936768'),['Voice'] = '', }, {['Conditions'] = {{['kind'] = 'AllRandom', ['maxValue'] = 20, ['value'] = 20, }, }, ['Delay'] = 10, ['Duration'] = 5, ['ID'] = 20000002, ['Kind'] = 0, ['Order'] = 2, ['TalkerID'] = 7240016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023937024'),['Voice'] = '', }, 
        },
        [20000003] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000003, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023937280'),['Voice'] = '', }, 
        },
        [20000004] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000004, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7215069, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023937536'),['Voice'] = '', }, 
        },
        [20000010] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211141, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023937792'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211142, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023938048'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211141, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023938304'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211142, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023938560'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211141, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023938816'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211143, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023939072'),['Voice'] = '', }, 
        },
        [20000011] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211149, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023939328'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211148, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023939584'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211149, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023939840'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211148, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023940096'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211149, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023940352'),['Voice'] = '', }, 
        },
        [20000012] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211150, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023940608'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211151, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023940864'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211150, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023941120'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211151, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023941376'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211150, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023941632'),['Voice'] = '', }, 
        },
        [20000013] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000013, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211153, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023941888'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000013, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211153, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023942144'),['Voice'] = '', }, 
        },
        [20000014] = {{['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000014, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211154, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023942400'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000014, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211154, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023942656'),['Voice'] = '', }, 
        },
        [20000015] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211161, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023942912'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023943168'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211162, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023943424'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023943680'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211161, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023943936'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023944192'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7211161, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023944448'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023944704'),['Voice'] = '', }, 
        },
        [20000016] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211163, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023944960'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211164, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023945216'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211163, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023945472'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211165, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023945728'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211166, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023945984'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211163, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023946240'),['Voice'] = '', }, 
        },
        [20000017] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023946496'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023946752'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023947008'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023947264'),['Voice'] = '', }, 
        },
        [20000018] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000018, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023947520'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000018, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023947776'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000018, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023948032'),['Voice'] = '', }, 
        },
        [20000019] = {{['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211171, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023948288'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211129, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023948544'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211170, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023948800'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211169, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023949056'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211171, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023949312'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211129, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023949568'),['Voice'] = '', }, 
        },
        [20000020] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000020, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023949824'),['Voice'] = '', }, 
        },
        [20000021] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000021, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023949824'),['Voice'] = '', }, 
        },
        [20000022] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000022, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023950336'),['Voice'] = '', }, 
        },
        [20000023] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000023, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023950592'),['Voice'] = '', }, 
        },
        [20000024] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000024, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023950848'),['Voice'] = '', }, 
        },
        [20000025] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000025, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240107, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023950336'),['Voice'] = '', }, 
        },
        [20000026] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000026, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023951360'),['Voice'] = '', }, 
        },
        [20000027] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000027, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023951616'),['Voice'] = '', }, 
        },
        [20000028] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000028, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250092, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023951872'),['Voice'] = '', }, 
        },
        [20000029] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000029, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023952128'),['Voice'] = '', }, 
        },
        [20000030] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000030, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023952384'),['Voice'] = '', }, 
        },
        [20000031] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000031, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023952640'),['Voice'] = '', }, 
        },
        [20000032] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000032, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023952896'),['Voice'] = '', }, 
        },
        [20000033] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000033, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023953152'),['Voice'] = '', }, 
        },
        [20000034] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000034, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023953408'),['Voice'] = '', }, 
        },
        [20000035] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000035, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023953664'),['Voice'] = '', }, 
        },
        [20000036] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000036, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203047, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023953920'),['Voice'] = '', }, 
        },
        [20000037] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000037, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023954176'),['Voice'] = '', }, 
        },
        [20000038] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000038, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023954432'),['Voice'] = '', }, 
        },
        [20000039] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000039, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023954688'),['Voice'] = '', }, 
        },
        [20000040] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000040, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023954944'),['Voice'] = '', }, 
        },
        [20000041] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000041, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023955200'),['Voice'] = '', }, 
        },
        [20000042] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000042, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203030, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023955456'),['Voice'] = '', }, 
        },
        [20000043] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000043, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203068, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023955712'),['Voice'] = '', }, 
        },
        [20000044] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000044, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203069, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023955968'),['Voice'] = '', }, 
        },
        [20000045] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000045, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023956224'),['Voice'] = '', }, 
        },
        [20000046] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000046, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023956480'),['Voice'] = '', }, 
        },
        [20000047] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000047, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203074, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023956736'),['Voice'] = '', }, 
        },
        [20000048] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000048, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203031, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023956992'),['Voice'] = '', }, 
        },
        [20000049] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000049, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203074, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023957248'),['Voice'] = '', }, 
        },
        [20000050] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000050, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023957504'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000050, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7203075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023957760'),['Voice'] = '', }, 
        },
        [20000051] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000051, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023958016'),['Voice'] = '', }, 
        },
        [20000052] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000052, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203077, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023958272'),['Voice'] = '', }, 
        },
        [20000053] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000053, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023958528'),['Voice'] = '', }, 
        },
        [20000054] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000054, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023958784'),['Voice'] = '', }, 
        },
        [20000055] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023959040'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7203081, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023959296'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7203082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023959552'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7203083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023959808'),['Voice'] = '', }, 
        },
        [20000056] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000056, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023960064'),['Voice'] = '', }, 
        },
        [20000057] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000057, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203090, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023960320'),['Voice'] = '', }, 
        },
        [20000058] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000058, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023960576'),['Voice'] = '', }, 
        },
        [20000059] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000059, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023960832'),['Voice'] = '', }, 
        },
        [20000060] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000060, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203092, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023961088'),['Voice'] = '', }, 
        },
        [20000061] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000061, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023961344'),['Voice'] = '', }, 
        },
        [20000062] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000062, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203094, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023961600'),['Voice'] = '', }, 
        },
        [20000063] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000063, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203099, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023961856'),['Voice'] = '', }, 
        },
        [20000064] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000064, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203102, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023962112'),['Voice'] = '', }, 
        },
        [20000065] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000065, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203105, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023962368'),['Voice'] = '', }, 
        },
        [20000066] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000066, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203101, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023962624'),['Voice'] = '', }, 
        },
        [20000067] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000067, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203100, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023962880'),['Voice'] = '', }, 
        },
        [20000068] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000068, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023963136'),['Voice'] = '', }, 
        },
        [20000069] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000069, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023963392'),['Voice'] = '', }, 
        },
        [20000070] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000070, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023963648'),['Voice'] = '', }, 
        },
        [20000071] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000071, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023963904'),['Voice'] = '', }, 
        },
        [20000072] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000072, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023964160'),['Voice'] = '', }, 
        },
        [20000073] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000073, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023964416'),['Voice'] = '', }, 
        },
        [20000074] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000074, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211013, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023964672'),['Voice'] = '', }, 
        },
        [20000075] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000075, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023964928'),['Voice'] = '', }, 
        },
        [20000076] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000076, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023965184'),['Voice'] = '', }, 
        },
        [20000077] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000077, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023965440'),['Voice'] = '', }, 
        },
        [20000078] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000078, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211019, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023965696'),['Voice'] = '', }, 
        },
        [20000079] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000079, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023965952'),['Voice'] = '', }, 
        },
        [20000080] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000080, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211022, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023966208'),['Voice'] = '', }, 
        },
        [20000081] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000081, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023966464'),['Voice'] = '', }, 
        },
        [20000082] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000082, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023966720'),['Voice'] = '', }, 
        },
        [20000083] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000083, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023966976'),['Voice'] = '', }, 
        },
        [20000084] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000084, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023967232'),['Voice'] = '', }, 
        },
        [20000085] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000085, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211027, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023967488'),['Voice'] = '', }, 
        },
        [20000086] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000086, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211097, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023967744'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000086, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211097, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023968000'),['Voice'] = '', }, 
        },
        [20000087] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000087, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211098, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023968256'),['Voice'] = '', }, 
        },
        [20000088] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000088, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211099, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023968512'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000088, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211099, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023968768'),['Voice'] = '', }, 
        },
        [20000089] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000089, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211100, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023969024'),['Voice'] = '', }, {['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000089, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211100, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023969280'),['Voice'] = '', }, {['Delay'] = 5.555, ['Duration'] = 5, ['ID'] = 20000089, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211101, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023969536'),['Voice'] = '', }, 
        },
        [20000090] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211103, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023969792'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211107, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023970048'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211103, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023970304'),['Voice'] = '', }, {['Delay'] = 6.5, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211107, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023970560'),['Voice'] = '', }, {['Delay'] = 8.6, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211103, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023970816'),['Voice'] = '', }, 
        },
        [20000091] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000091, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211104, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023971072'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000091, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211104, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023971328'),['Voice'] = '', }, 
        },
        [20000092] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000092, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211105, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023971584'),['Voice'] = '', }, 
        },
        [20000093] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000093, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211106, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023971840'),['Voice'] = '', }, 
        },
        [20000094] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000094, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211108, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023972096'),['Voice'] = '', }, 
        },
        [20000095] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023972352'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023972608'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023972864'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023973120'),['Voice'] = '', }, 
        },
        [20000096] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000096, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211110, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023973376'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000096, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211110, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023973632'),['Voice'] = '', }, 
        },
        [20000097] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000097, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211111, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023973888'),['Voice'] = '', }, 
        },
        [20000098] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000098, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211112, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023974144'),['Voice'] = '', }, 
        },
        [20000099] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000099, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211113, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023974400'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000099, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211113, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023974656'),['Voice'] = '', }, 
        },
        [20000100] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000100, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211114, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023974912'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000100, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211114, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023975168'),['Voice'] = '', }, 
        },
        [20000101] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000101, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211115, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023975424'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000101, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211115, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023975680'),['Voice'] = '', }, 
        },
        [20000102] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000102, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211097, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023975936'),['Voice'] = '', }, 
        },
        [20000103] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000103, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211105, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023976192'),['Voice'] = '', }, 
        },
        [20000104] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000104, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211117, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023976448'),['Voice'] = '', }, 
        },
        [20000105] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000105, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211118, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023976704'),['Voice'] = '', }, 
        },
        [20000106] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000106, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211119, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023976960'),['Voice'] = '', }, 
        },
        [20000107] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000107, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023977216'),['Voice'] = '', }, 
        },
        [20000108] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000108, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023977472'),['Voice'] = '', }, 
        },
        [20000109] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000109, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211035, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023977728'),['Voice'] = '', }, 
        },
        [20000110] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000110, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211035, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023977984'),['Voice'] = '', }, 
        },
        [20000111] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000111, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023978240'),['Voice'] = '', }, 
        },
        [20000112] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000112, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023978496'),['Voice'] = '', }, 
        },
        [20000113] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000113, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023978752'),['Voice'] = '', }, 
        },
        [20000114] = {{['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000114, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023979008'),['Voice'] = '', }, 
        },
        [20000115] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000115, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023979264'),['Voice'] = '', }, 
        },
        [20000116] = {{['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000116, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023979520'),['Voice'] = '', }, 
        },
        [20000117] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211125, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023979776'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211126, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023980032'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211126, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023980288'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211125, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023980544'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211125, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023980800'),['Voice'] = '', }, 
        },
        [20000122] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000122, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023981056'),['Voice'] = '', }, 
        },
        [20000123] = {{['Delay'] = 2.5, ['Duration'] = 2, ['ID'] = 20000123, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023981312'),['Voice'] = '', }, 
        },
        [20000124] = {{['Delay'] = 4.5, ['Duration'] = 2, ['ID'] = 20000124, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023981568'),['Voice'] = '', }, 
        },
        [20000125] = {{['Delay'] = 6.5, ['Duration'] = 2, ['ID'] = 20000125, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023981824'),['Voice'] = '', }, 
        },
        [20000126] = {{['Delay'] = 8.5, ['Duration'] = 3, ['ID'] = 20000126, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023982080'),['Voice'] = '', }, 
        },
        [20000127] = {{['Delay'] = 11.5, ['Duration'] = 3, ['ID'] = 20000127, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023982336'),['Voice'] = '', }, 
        },
        [20000128] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000128, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211129, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023982592'),['Voice'] = '', }, 
        },
        [20000129] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000129, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211130, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023982848'),['Voice'] = '', }, 
        },
        [20000130] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000130, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211131, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023983104'),['Voice'] = '', }, 
        },
        [20000131] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000131, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211132, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023983360'),['Voice'] = '', }, 
        },
        [20000132] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000132, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211133, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023983616'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000132, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211133, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023983872'),['Voice'] = '', }, 
        },
        [20000133] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000133, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023984128'),['Voice'] = '', }, 
        },
        [20000134] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000134, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023984384'),['Voice'] = '', }, 
        },
        [20000135] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000135, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211136, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023985408'),['Voice'] = '', }, 
        },
        [20000136] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000136, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211137, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023984640'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000136, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211137, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023984896'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000136, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211137, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023985152'),['Voice'] = '', }, 
        },
        [20000137] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000137, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211138, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023985664'),['Voice'] = '', }, 
        },
        [20000138] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000138, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211138, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023985920'),['Voice'] = '', }, 
        },
        [20000139] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000139, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7216018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023986176'),['Voice'] = '', }, 
        },
        [20000140] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000140, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023986432'),['Voice'] = '', }, 
        },
        [20000141] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000141, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023986688'),['Voice'] = '', }, 
        },
        [20000142] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000142, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023986944'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000142, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023987200'),['Voice'] = '', }, 
        },
        [20000143] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000143, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023987456'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000143, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023987712'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000143, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023987968'),['Voice'] = '', }, 
        },
        [20000144] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000144, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023988224'),['Voice'] = '', }, 
        },
        [20000145] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000145, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023988480'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000145, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023988736'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000145, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023988992'),['Voice'] = '', }, 
        },
        [20000146] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000146, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023989248'),['Voice'] = '', }, 
        },
        [20000147] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000147, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023989504'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000147, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023989760'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000147, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023990016'),['Voice'] = '', }, 
        },
        [20000148] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000148, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211042, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023990272'),['Voice'] = '', }, 
        },
        [20000149] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000149, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211043, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023990528'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000149, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211043, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023990784'),['Voice'] = '', }, 
        },
        [20000150] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000150, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023991040'),['Voice'] = '', }, 
        },
        [20000151] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000151, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023991296'),['Voice'] = '', }, 
        },
        [20000152] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000152, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211046, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023991552'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000152, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211046, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023991808'),['Voice'] = '', }, 
        },
        [20000153] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000153, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211047, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023992064'),['Voice'] = '', }, 
        },
        [20000154] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000154, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023992320'),['Voice'] = '', }, 
        },
        [20000155] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000155, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023992576'),['Voice'] = '', }, 
        },
        [20000156] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000156, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023992832'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000156, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023993088'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000156, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023993344'),['Voice'] = '', }, 
        },
        [20000157] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000157, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023993600'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000157, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023993856'),['Voice'] = '', }, 
        },
        [20000158] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000158, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023994112'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000158, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023994368'),['Voice'] = '', }, 
        },
        [20000159] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000159, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023994624'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000159, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023994880'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000159, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023989248'),['Voice'] = '', }, 
        },
        [20000160] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000160, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023995392'),['Voice'] = '', }, 
        },
        [20000161] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000161, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023995648'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000161, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023995904'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000161, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023996160'),['Voice'] = '', }, 
        },
        [20000162] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000162, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211057, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023996416'),['Voice'] = '', }, 
        },
        [20000163] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000163, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211058, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023996672'),['Voice'] = '', }, 
        },
        [20000164] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000164, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211059, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023996928'),['Voice'] = '', }, 
        },
        [20000165] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000165, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023997184'),['Voice'] = '', }, 
        },
        [20000166] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000166, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211061, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023997440'),['Voice'] = '', }, 
        },
        [20000167] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000167, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023997696'),['Voice'] = '', }, 
        },
        [20000168] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000168, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023997952'),['Voice'] = '', }, 
        },
        [20000169] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000169, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023998208'),['Voice'] = '', }, 
        },
        [20000170] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000170, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023998464'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000170, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023998720'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000170, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023998976'),['Voice'] = '', }, 
        },
        [20000171] = {{['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000171, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023998720'),['Voice'] = '', }, 
        },
        [20000172] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000172, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023999488'),['Voice'] = '', }, 
        },
        [20000173] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000173, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023999744'),['Voice'] = '', }, 
        },
        [20000174] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000174, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024000000'),['Voice'] = '', }, 
        },
        [20000175] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000175, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024000256'),['Voice'] = '', }, 
        },
        [20000176] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000176, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207114, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024000512'),['Voice'] = '', }, 
        },
        [20000177] = {{['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000177, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024000768'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000177, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024001024'),['Voice'] = '', }, 
        },
        [20000178] = {{['Delay'] = 8, ['Duration'] = 5, ['ID'] = 20000178, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024001280'),['Voice'] = '', }, 
        },
        [20000179] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000179, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024001536'),['Voice'] = '', }, 
        },
        [20000180] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000180, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 0, ['Text'] = '', ['Voice'] = '', }, 
        },
        [20000181] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000181, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024002048'),['Voice'] = '', }, 
        },
        [20000182] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000182, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024002304'),['Voice'] = '', }, 
        },
        [20000183] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000183, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024002560'),['Voice'] = '', }, 
        },
        [20000184] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000184, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024002816'),['Voice'] = '', }, 
        },
        [20000185] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000185, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207115, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024003072'),['Voice'] = '', }, 
        },
        [20000186] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000186, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024003328'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000186, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024003584'),['Voice'] = '', }, 
        },
        [20000187] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000187, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024003840'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000187, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024004096'),['Voice'] = '', }, 
        },
        [20000188] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000188, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024004352'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000188, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024004608'),['Voice'] = '', }, 
        },
        [20000189] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000189, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024004352'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000189, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024004608'),['Voice'] = '', }, 
        },
        [20000190] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000190, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207027, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024005376'),['Voice'] = '', }, 
        },
        [20000191] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000191, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024005632'),['Voice'] = '', }, 
        },
        [20000192] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000192, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207031, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024005888'),['Voice'] = '', }, 
        },
        [20000193] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000193, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024006144'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000193, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024006400'),['Voice'] = '', }, 
        },
        [20000194] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000194, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024006656'),['Voice'] = '', }, 
        },
        [20000195] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000195, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024006912'),['Voice'] = '', }, 
        },
        [20000196] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000196, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207035, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024007168'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000196, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207035, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024007424'),['Voice'] = '', }, 
        },
        [20000197] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000197, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024007680'),['Voice'] = '', }, 
        },
        [20000198] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000198, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024007936'),['Voice'] = '', }, 
        },
        [20000199] = {{['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000199, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024008192'),['Voice'] = '', }, 
        },
        [20000200] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000200, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207042, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024008448'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000200, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207042, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024008704'),['Voice'] = '', }, 
        },
        [20000201] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000201, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024008960'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000201, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024009216'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 2, ['ID'] = 20000201, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024009472'),['Voice'] = '', }, 
        },
        [20000202] = {{['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000202, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207046, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024009728'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 2, ['ID'] = 20000202, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207046, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024009984'),['Voice'] = '', }, 
        },
        [20000203] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000203, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207047, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024010240'),['Voice'] = '', }, {['Delay'] = 8.5, ['Duration'] = 2, ['ID'] = 20000203, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207047, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024010496'),['Voice'] = '', }, 
        },
        [20000204] = {{['Delay'] = 2.5, ['Duration'] = 2, ['ID'] = 20000204, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024010752'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000204, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024011008'),['Voice'] = '', }, 
        },
        [20000205] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000205, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024011264'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000205, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024011520'),['Voice'] = '', }, 
        },
        [20000206] = {{['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000206, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024011776'),['Voice'] = '', }, 
        },
        [20000207] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000207, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024012032'),['Voice'] = '', }, 
        },
        [20000208] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000208, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024012288'),['Voice'] = '', }, 
        },
        [20000209] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000209, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207069, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024012544'),['Voice'] = '', }, 
        },
        [20000210] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000210, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024012800'),['Voice'] = '', }, 
        },
        [20000211] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000211, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024013056'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000211, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024013312'),['Voice'] = '', }, 
        },
        [20000212] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000212, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024013568'),['Voice'] = '', }, 
        },
        [20000213] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207078, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024013824'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024014080'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024014336'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7207081, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024014592'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7207082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024014848'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7207083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024015104'),['Voice'] = '', }, 
        },
        [20000214] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000214, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024015360'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000214, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024015616'),['Voice'] = '', }, 
        },
        [20000215] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207090, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024015872'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207090, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024016128'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207086, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024016384'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7207087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024016640'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7207088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024016896'),['Voice'] = '', }, 
        },
        [20000216] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000216, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024017152'),['Voice'] = '', }, 
        },
        [20000217] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000217, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207100, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024017408'),['Voice'] = '', }, 
        },
        [20000218] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000218, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207106, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024017664'),['Voice'] = '', }, 
        },
        [20000219] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000219, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207112, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024017920'),['Voice'] = '', }, 
        },
        [20000220] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000220, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207110, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024018176'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000220, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207110, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024018432'),['Voice'] = '', }, 
        },
        [20000221] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000221, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207117, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024018688'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000221, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207117, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024018944'),['Voice'] = '', }, 
        },
        [20000222] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000222, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207111, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024019200'),['Voice'] = '', }, 
        },
        [20000223] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000223, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207118, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024019456'),['Voice'] = '', }, 
        },
        [20000224] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000224, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207119, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024019712'),['Voice'] = '', }, 
        },
        [20000225] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000225, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7217007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024019968'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 1.5, ['ID'] = 20000225, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7217008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024020224'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 1.5, ['ID'] = 20000225, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7217009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024020480'),['Voice'] = '', }, 
        },
        [20000226] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000226, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7217006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024020736'),['Voice'] = '', }, 
        },
        [20000227] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000227, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7214001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024020992'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000227, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7214001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024021248'),['Voice'] = '', }, 
        },
        [20000228] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024021504'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024021760'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024022016'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024022272'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024022528'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024022784'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024023040'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024023296'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024023552'),['Voice'] = '', }, 
        },
        [20000229] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000229, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210854, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024023808'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000229, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210854, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024024064'),['Voice'] = '', }, 
        },
        [20000230] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000230, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210831, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024024320'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000230, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210831, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024024576'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000230, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210832, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024024832'),['Voice'] = '', }, 
        },
        [20000231] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000231, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210833, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024025088'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000231, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210833, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024025344'),['Voice'] = '', }, 
        },
        [20000232] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000232, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210834, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024025600'),['Voice'] = '', }, 
        },
        [20000233] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000233, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210835, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024025856'),['Voice'] = '', }, 
        },
        [20000234] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000234, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210836, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024026112'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000234, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210837, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024026112'),['Voice'] = '', }, 
        },
        [20000235] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000235, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210838, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024026624'),['Voice'] = '', }, 
        },
        [20000236] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000236, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210839, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024026880'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000236, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210840, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024027136'),['Voice'] = '', }, 
        },
        [20000237] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000237, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210842, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024027392'),['Voice'] = '', }, 
        },
        [20000238] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024027648'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210844, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024027904'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024028160'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210844, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024028416'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024028672'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210844, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024028928'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024029184'),['Voice'] = '', }, 
        },
        [20000239] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210845, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024029440'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210846, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024029696'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210845, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024029952'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210846, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024030208'),['Voice'] = '', }, 
        },
        [20000240] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210824, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024030464'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210848, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024030720'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210824, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024030976'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210848, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024031232'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210824, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024031488'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210848, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024031744'),['Voice'] = '', }, 
        },
        [20000241] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210849, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024032000'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210850, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024032256'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210849, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024032512'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210850, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024032768'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210849, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024033024'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210851, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024033280'),['Voice'] = '', }, 
        },
        [20000242] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000242, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210852, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024033536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000242, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210853, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024033792'),['Voice'] = '', }, 
        },
        [20000243] = {{['Delay'] = 0, ['Duration'] = 3.5, ['ID'] = 20000243, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024034048'),['Voice'] = '', }, 
        },
        [20000244] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000244, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024034304'),['Voice'] = '', }, 
        },
        [20000245] = {{['Delay'] = 5, ['Duration'] = 2.5, ['ID'] = 20000245, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024034560'),['Voice'] = '', }, 
        },
        [20000246] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000246, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215010, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024034816'),['Voice'] = '', }, 
        },
        [20000247] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000247, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024035072'),['Voice'] = '', }, 
        },
        [20000248] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000248, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024035328'),['Voice'] = '', }, 
        },
        [20000249] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000249, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024035584'),['Voice'] = '', }, 
        },
        [20000250] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000250, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024035840'),['Voice'] = '', }, 
        },
        [20000251] = {{['Delay'] = 0, ['Duration'] = 3.5, ['ID'] = 20000251, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215074, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024036096'),['Voice'] = '', }, 
        },
        [20000252] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000252, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024036352'),['Voice'] = '', }, 
        },
        [20000253] = {{['Delay'] = 0.5, ['Duration'] = 30, ['ID'] = 20000253, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024036608'),['Voice'] = '', }, 
        },
        [20000254] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000254, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024036864'),['Voice'] = '', }, 
        },
        [20000255] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000255, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024037120'),['Voice'] = '', }, 
        },
        [20000256] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000256, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024037376'),['Voice'] = '', }, 
        },
        [20000257] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000257, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024037632'),['Voice'] = '', }, 
        },
        [20000258] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000258, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024037888'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000258, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024038144'),['Voice'] = '', }, 
        },
        [20000259] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000259, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024038400'),['Voice'] = '', }, 
        },
        [20000260] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000260, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024038656'),['Voice'] = '', }, 
        },
        [20000261] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000261, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024038912'),['Voice'] = '', }, 
        },
        [20000262] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000262, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024039168'),['Voice'] = '', }, 
        },
        [20000263] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000263, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024039424'),['Voice'] = '', }, 
        },
        [20000264] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000264, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024039680'),['Voice'] = '', }, 
        },
        [20000265] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000265, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219010, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024039936'),['Voice'] = '', }, 
        },
        [20000266] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000266, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024040192'),['Voice'] = '', }, 
        },
        [20000267] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000267, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024040448'),['Voice'] = '', }, 
        },
        [20000268] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000268, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024040704'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000268, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024040960'),['Voice'] = '', }, 
        },
        [20000269] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000269, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219031, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024041216'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000269, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024041472'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000269, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024041728'),['Voice'] = '', }, 
        },
        [20000270] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000270, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024041984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000270, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024042240'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000270, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024042496'),['Voice'] = '', }, 
        },
        [20000271] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000271, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024042752'),['Voice'] = '', }, 
        },
        [20000272] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000272, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219035, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024043008'),['Voice'] = '', }, 
        },
        [20000273] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000273, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024043264'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000273, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024043520'),['Voice'] = '', }, 
        },
        [20000274] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000274, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024043776'),['Voice'] = '', }, 
        },
        [20000275] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000275, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024044032'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000275, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024044288'),['Voice'] = '', }, 
        },
        [20000276] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000276, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024044544'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000276, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024044800'),['Voice'] = '', }, 
        },
        [20000277] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000277, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219042, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024045056'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000277, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219043, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024045312'),['Voice'] = '', }, 
        },
        [20000278] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000278, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024045568'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000278, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024045824'),['Voice'] = '', }, 
        },
        [20000279] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000279, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024046080'),['Voice'] = '', }, 
        },
        [20000280] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000280, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219046, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024046336'),['Voice'] = '', }, 
        },
        [20000281] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000281, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219047, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024046592'),['Voice'] = '', }, 
        },
        [20000282] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000282, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024046848'),['Voice'] = '', }, 
        },
        [20000283] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000283, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024047104'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000283, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024047360'),['Voice'] = '', }, 
        },
        [20000284] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000284, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024047616'),['Voice'] = '', }, 
        },
        [20000285] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000285, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024047872'),['Voice'] = '', }, 
        },
        [20000286] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000286, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024048128'),['Voice'] = '', }, 
        },
        [20000287] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000287, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024048384'),['Voice'] = '', }, 
        },
        [20000288] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000288, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219057, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024048640'),['Voice'] = '', }, 
        },
        [20000289] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024048896'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024049152'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024049408'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024049664'),['Voice'] = '', }, 
        },
        [20000290] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000290, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024049920'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000290, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024050176'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000290, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024050432'),['Voice'] = '', }, 
        },
        [20000291] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000291, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223010, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024050688'),['Voice'] = '', }, 
        },
        [20000292] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000292, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024050944'),['Voice'] = '', }, 
        },
        [20000293] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000293, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223013, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024051200'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000293, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223013, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024051456'),['Voice'] = '', }, 
        },
        [20000294] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000294, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024051712'),['Voice'] = '', }, 
        },
        [20000295] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000295, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024051968'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000295, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250252, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024052224'),['Voice'] = '', }, 
        },
        [20000296] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000296, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024052480'),['Voice'] = '', }, 
        },
        [20000297] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000297, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024052736'),['Voice'] = '', }, 
        },
        [20000298] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000298, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024052992'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000298, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024053248'),['Voice'] = '', }, 
        },
        [20000299] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000299, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024053504'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000299, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024053760'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000299, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024054016'),['Voice'] = '', }, 
        },
        [20000300] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000300, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024054272'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000300, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024054528'),['Voice'] = '', }, 
        },
        [20000301] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000301, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024054784'),['Voice'] = '', }, 
        },
        [20000302] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000302, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223023, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024055040'),['Voice'] = '', }, 
        },
        [20000303] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000303, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024055296'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000303, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024055552'),['Voice'] = '', }, 
        },
        [20000304] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000304, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223027, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024055808'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000304, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024056064'),['Voice'] = '', }, 
        },
        [20000305] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000305, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223031, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024056320'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000305, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223030, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024056576'),['Voice'] = '', }, 
        },
        [20000306] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024056832'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024057088'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024057344'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024057600'),['Voice'] = '', }, 
        },
        [20000307] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000307, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024057856'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000307, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223042, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024058112'),['Voice'] = '', }, 
        },
        [20000308] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000308, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024058368'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000308, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024058624'),['Voice'] = '', }, 
        },
        [20000309] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000309, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024058880'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000309, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024059136'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000309, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024059392'),['Voice'] = '', }, 
        },
        [20000310] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000310, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024059648'),['Voice'] = '', }, 
        },
        [20000311] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000311, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024059904'),['Voice'] = '', }, 
        },
        [20000312] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024060160'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024060416'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024060672'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024060928'),['Voice'] = '', }, 
        },
        [20000313] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000313, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223023, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024061184'),['Voice'] = '', }, 
        },
        [20000314] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000314, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024061440'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000314, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024061696'),['Voice'] = '', }, 
        },
        [20000315] = {{['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024061952'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7201012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024062208'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7201013, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024062464'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7201013, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024062720'),['Voice'] = '', }, 
        },
        [20000316] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024062976'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7201014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024063232'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7201014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024063488'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7201016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024063744'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7201017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024064000'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7201018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024064256'),['Voice'] = '', }, 
        },
        [20000317] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000317, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7225001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024064512'),['Voice'] = '', }, 
        },
        [20000318] = {{['Delay'] = 0.5, ['Duration'] = 6, ['ID'] = 20000318, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7225005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024064768'),['Voice'] = '', }, 
        },
        [20000319] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000319, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024065024'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000319, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024065280'),['Voice'] = '', }, 
        },
        [20000320] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000320, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024065536'),['Voice'] = '', }, 
        },
        [20000321] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000321, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024065792'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000321, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024066048'),['Voice'] = '', }, 
        },
        [20000322] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000322, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024066304'),['Voice'] = '', }, 
        },
        [20000323] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000323, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024067072'),['Voice'] = '', }, 
        },
        [20000324] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000324, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231010, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024067328'),['Voice'] = '', }, 
        },
        [20000325] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000325, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024067584'),['Voice'] = '', }, 
        },
        [20000326] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000326, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024067840'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000326, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231013, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024068096'),['Voice'] = '', }, 
        },
        [20000327] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000327, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024068352'),['Voice'] = '', }, 
        },
        [20000328] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000328, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024068608'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000328, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024068864'),['Voice'] = '', }, 
        },
        [20000329] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000329, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024069120'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000329, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231081, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024069376'),['Voice'] = '', }, 
        },
        [20000330] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000330, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024069632'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000330, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024069888'),['Voice'] = '', }, 
        },
        [20000331] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000331, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024070144'),['Voice'] = '', }, 
        },
        [20000332] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000332, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024070400'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000332, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024070656'),['Voice'] = '', }, 
        },
        [20000333] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000333, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231019, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024070912'),['Voice'] = '', }, 
        },
        [20000334] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000334, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024071168'),['Voice'] = '', }, 
        },
        [20000335] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000335, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231023, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024071424'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000335, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024071680'),['Voice'] = '', }, 
        },
        [20000336] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000336, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024071936'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000336, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231081, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024072192'),['Voice'] = '', }, 
        },
        [20000337] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000337, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024072448'),['Voice'] = '', }, 
        },
        [20000338] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000338, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024072704'),['Voice'] = '', }, 
        },
        [20000339] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000339, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231027, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024072960'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000339, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231027, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024073216'),['Voice'] = '', }, 
        },
        [20000340] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000340, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024073472'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000340, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024073728'),['Voice'] = '', }, 
        },
        [20000341] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000341, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024073984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000341, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024074240'),['Voice'] = '', }, 
        },
        [20000342] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000342, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231031, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024074496'),['Voice'] = '', }, 
        },
        [20000343] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000343, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024074752'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000343, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024075008'),['Voice'] = '', }, 
        },
        [20000344] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000344, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024075264'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000344, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024075520'),['Voice'] = '', }, 
        },
        [20000345] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000345, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024075776'),['Voice'] = '', }, 
        },
        [20000346] = {{['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000346, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231035, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024076032'),['Voice'] = '', }, 
        },
        [20000347] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000347, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024076288'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000347, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024076544'),['Voice'] = '', }, 
        },
        [20000348] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000348, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024059648'),['Voice'] = '', }, 
        },
        [20000349] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000349, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024077056'),['Voice'] = '', }, 
        },
        [20000350] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000350, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024077312'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000350, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024077568'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000350, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7239017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024077824'),['Voice'] = '', }, 
        },
        [20000351] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000351, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024078080'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000351, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024078336'),['Voice'] = '', }, 
        },
        [20000352] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000352, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231133, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024078592'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000352, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231117, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024078848'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000352, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231117, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024079104'),['Voice'] = '', }, 
        },
        [20000353] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000353, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231043, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024079360'),['Voice'] = '', }, 
        },
        [20000354] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000354, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231046, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024079616'),['Voice'] = '', }, 
        },
        [20000355] = {{['Delay'] = 10, ['Duration'] = 5, ['ID'] = 20000355, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231047, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024079872'),['Voice'] = '', }, 
        },
        [20000356] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000356, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024080128'),['Voice'] = '', }, 
        },
        [20000357] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000357, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024080384'),['Voice'] = '', }, 
        },
        [20000358] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000358, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024080640'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000358, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024080896'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000358, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024081152'),['Voice'] = '', }, 
        },
        [20000359] = {{['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000359, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024081408'),['Voice'] = '', }, 
        },
        [20000360] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000360, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024081664'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000360, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024081920'),['Voice'] = '', }, 
        },
        [20000361] = {{['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000361, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024082176'),['Voice'] = '', }, 
        },
        [20000362] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000362, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024082432'),['Voice'] = '', }, 
        },
        [20000363] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000363, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024082688'),['Voice'] = '', }, 
        },
        [20000364] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000364, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024082944'),['Voice'] = '', }, 
        },
        [20000365] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000365, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231057, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024083200'),['Voice'] = '', }, 
        },
        [20000366] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000366, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231058, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024083456'),['Voice'] = '', }, 
        },
        [20000367] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000367, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231059, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024083712'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000367, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231059, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024083968'),['Voice'] = '', }, 
        },
        [20000368] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000368, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024084224'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000368, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024084480'),['Voice'] = '', }, 
        },
        [20000369] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000369, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231061, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024084736'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000369, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024084992'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000369, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024085248'),['Voice'] = '', }, 
        },
        [20000370] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000370, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024085504'),['Voice'] = '', }, 
        },
        [20000371] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000371, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024085760'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000371, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024086016'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000371, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024086272'),['Voice'] = '', }, 
        },
        [20000372] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000372, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024086528'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000372, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024086784'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000372, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231065, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024087040'),['Voice'] = '', }, 
        },
        [20000373] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000373, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024087296'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000373, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024087552'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000373, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024087808'),['Voice'] = '', }, 
        },
        [20000374] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000374, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024085504'),['Voice'] = '', }, 
        },
        [20000375] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231068, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024088320'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231068, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167042049'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231069, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167042305'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7231069, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167042561'),['Voice'] = '', }, 
        },
        [20000376] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000376, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231130, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167042817'),['Voice'] = '', }, 
        },
        [20000377] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000377, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167043073'),['Voice'] = '', }, 
        },
        [20000378] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000378, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231071, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167043329'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000378, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231071, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167043585'),['Voice'] = '', }, 
        },
        [20000379] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000379, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231072, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167043841'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000379, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231072, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167044097'),['Voice'] = '', }, 
        },
        [20000380] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000380, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231073, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167044353'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000380, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231073, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167044609'),['Voice'] = '', }, 
        },
        [20000381] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000381, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167044865'),['Voice'] = '', }, 
        },
        [20000382] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000382, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231075, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167045121'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000382, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231075, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167045377'),['Voice'] = '', }, 
        },
        [20000383] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000383, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231076, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167045633'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000383, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231076, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167045889'),['Voice'] = '', }, 
        },
        [20000384] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000384, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231077, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167046145'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000384, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231077, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167046401'),['Voice'] = '', }, 
        },
        [20000385] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000385, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231078, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167046657'),['Voice'] = '', }, 
        },
        [20000386] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000386, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231079, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167046913'),['Voice'] = '', }, 
        },
        [20000387] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000387, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231086, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024093696'),['Voice'] = '', }, 
        },
        [20000388] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000388, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231087, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167047425'),['Voice'] = '', }, 
        },
        [20000389] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000389, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231088, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167047681'),['Voice'] = '', }, 
        },
        [20000390] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000390, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231089, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167047937'),['Voice'] = '', }, 
        },
        [20000391] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000391, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231090, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167048193'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000391, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231090, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167048449'),['Voice'] = '', }, 
        },
        [20000392] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000392, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231091, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167048705'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000392, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231091, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167048961'),['Voice'] = '', }, 
        },
        [20000393] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000393, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231092, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167049217'),['Voice'] = '', }, 
        },
        [20000394] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000394, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231093, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167049473'),['Voice'] = '', }, 
        },
        [20000395] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000395, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231094, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167049729'),['Voice'] = '', }, 
        },
        [20000396] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000396, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231095, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167049985'),['Voice'] = '', }, 
        },
        [20000397] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000397, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231096, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167050241'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000397, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231096, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167050497'),['Voice'] = '', }, 
        },
        [20000398] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000398, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231097, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167050753'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000398, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231097, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167051009'),['Voice'] = '', }, 
        },
        [20000399] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000399, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231098, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167051265'),['Voice'] = '', }, 
        },
        [20000400] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000400, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231101, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167051521'),['Voice'] = '', }, 
        },
        [20000401] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000401, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231102, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167051777'),['Voice'] = '', }, {['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000401, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231102, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024098560'),['Voice'] = '', }, 
        },
        [20000402] = {{['Delay'] = 1.5, ['Duration'] = 3, ['ID'] = 20000402, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231103, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024098816'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000402, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231125, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024099072'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 0, ['ID'] = 20000402, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231103, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024099328'),['Voice'] = '', }, 
        },
        [20000403] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000403, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231104, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024099584'),['Voice'] = '', }, 
        },
        [20000404] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000404, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231105, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024099840'),['Voice'] = '', }, 
        },
        [20000405] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000405, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231106, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167053313'),['Voice'] = '', }, 
        },
        [20000406] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000406, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231107, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024100352'),['Voice'] = '', }, 
        },
        [20000407] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000407, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231132, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167053825'),['Voice'] = '', }, 
        },
        [20000408] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000408, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231111, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167054081'),['Voice'] = '', }, 
        },
        [20000409] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000409, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231112, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167054337'),['Voice'] = '', }, 
        },
        [20000410] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000410, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231113, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167054593'),['Voice'] = '', }, 
        },
        [20000411] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000411, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231114, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167054849'),['Voice'] = '', }, 
        },
        [20000412] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000412, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231115, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024101888'),['Voice'] = '', }, 
        },
        [20000413] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000413, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167055617'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000413, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167055873'),['Voice'] = '', }, 
        },
        [20000414] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000414, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167056129'),['Voice'] = '', }, 
        },
        [20000415] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000415, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231118, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167056385'),['Voice'] = '', }, 
        },
        [20000416] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000416, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231119, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024103168'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000416, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231119, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024103424'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000416, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231119, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024103680'),['Voice'] = '', }, 
        },
        [20000417] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000417, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231120, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167057153'),['Voice'] = '', }, 
        },
        [20000418] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000418, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231121, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167057409'),['Voice'] = '', }, 
        },
        [20000419] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000419, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231122, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167057665'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000419, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231122, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167057921'),['Voice'] = '', }, 
        },
        [20000420] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000420, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231123, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167058177'),['Voice'] = '', }, 
        },
        [20000421] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000421, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231124, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024105216'),['Voice'] = '', }, 
        },
        [20000422] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000422, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231126, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167058689'),['Voice'] = '', }, 
        },
        [20000423] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000423, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231127, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167058945'),['Voice'] = '', }, 
        },
        [20000424] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000424, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231128, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167059201'),['Voice'] = '', }, 
        },
        [20000425] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000425, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231129, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024106240'),['Voice'] = '', }, 
        },
        [20000426] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167064577'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167064833'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167065089'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167065345'),['Voice'] = '', }, 
        },
        [20000427] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000427, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212008, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167066369'),['Voice'] = '', }, 
        },
        [20000428] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000428, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212007, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167066625'),['Voice'] = '', }, 
        },
        [20000429] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000429, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212006, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167066881'),['Voice'] = '', }, 
        },
        [20000430] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000430, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212012, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167074561'),['Voice'] = '', }, 
        },
        [20000431] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000431, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212013, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167074817'),['Voice'] = '', }, 
        },
        [20000432] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212014, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167075073'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212015, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167075329'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212014, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167075585'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212015, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167075841'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212016, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167076097'),['Voice'] = '', }, 
        },
        [20000433] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000433, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024110080'),['Voice'] = '', }, 
        },
        [20000434] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167079681'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212020, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167079937'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167080193'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212020, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167080449'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167080705'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212020, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167080961'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167081217'),['Voice'] = '', }, 
        },
        [20000435] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212022, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167081729'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212023, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167081985'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212022, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167082241'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212023, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167082497'),['Voice'] = '', }, 
        },
        [20000436] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000436, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212024, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167083265'),['Voice'] = '', }, 
        },
        [20000437] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000437, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212025, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167083521'),['Voice'] = '', }, 
        },
        [20000438] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000438, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212026, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167084289'),['Voice'] = '', }, 
        },
        [20000439] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000439, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212027, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167084545'),['Voice'] = '', }, 
        },
        [20000440] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212028, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167084801'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212028, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167085057'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212029, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167085313'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212029, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167085569'),['Voice'] = '', }, 
        },
        [20000441] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000441, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212035, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167088385'),['Voice'] = '', }, 
        },
        [20000442] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000442, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212037, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167088897'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000442, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212037, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167089153'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000442, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212037, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167089409'),['Voice'] = '', }, 
        },
        [20000443] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167089665'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167089921'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167090177'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167090433'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167090689'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167090945'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167091201'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167091457'),['Voice'] = '', }, 
        },
        [20000444] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212039, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167091713'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212040, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167091969'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212039, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167092225'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212041, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167092481'),['Voice'] = '', }, 
        },
        [20000445] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000445, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212042, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167092737'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000445, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212042, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167092993'),['Voice'] = '', }, 
        },
        [20000446] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000446, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212043, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024119808'),['Voice'] = '', }, 
        },
        [20000447] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000447, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024120064'),['Voice'] = '', }, 
        },
        [20000448] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000448, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212048, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167096833'),['Voice'] = '', }, 
        },
        [20000449] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000449, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212050, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167097345'),['Voice'] = '', }, 
        },
        [20000450] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000450, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212051, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167097601'),['Voice'] = '', }, 
        },
        [20000451] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000451, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212052, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167097857'),['Voice'] = '', }, 
        },
        [20000452] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000452, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212030, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167098113'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000452, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212031, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167098369'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000452, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212032, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167098625'),['Voice'] = '', }, 
        },
        [20000453] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000453, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212053, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167098881'),['Voice'] = '', }, 
        },
        [20000454] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000454, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212054, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167099137'),['Voice'] = '', }, 
        },
        [20000455] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000455, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212076, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167099393'),['Voice'] = '', }, 
        },
        [20000456] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000456, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212081, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024122880'),['Voice'] = '', }, 
        },
        [20000457] = {{['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000457, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212084, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167100929'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000457, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212084, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167101185'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000457, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212084, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167101441'),['Voice'] = '', }, 
        },
        [20000458] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167101697'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167101953'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167102209'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167102465'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167102721'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167102977'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167103233'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167103489'),['Voice'] = '', }, 
        },
        [20000459] = {{['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000459, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212088, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167104769'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000459, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212088, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167105025'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000459, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212088, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167105281'),['Voice'] = '', }, 
        },
        [20000460] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167105537'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167105793'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167106049'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024127488'),['Voice'] = '', }, 
        },
        [20000461] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000461, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212078, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167106561'),['Voice'] = '', }, 
        },
        [20000462] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000462, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212101, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023975936'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000462, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212101, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024128256'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000462, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212101, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167107329'),['Voice'] = '', }, 
        },
        [20000463] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212097, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167107585'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212098, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167107841'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212099, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167107841'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212100, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167108353'),['Voice'] = '', }, 
        },
        [20000464] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000464, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212066, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167108609'),['Voice'] = '', }, 
        },
        [20000465] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000465, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212067, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167108865'),['Voice'] = '', }, 
        },
        [20000466] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000466, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212087, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167109121'),['Voice'] = '', }, 
        },
        [20000467] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000467, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024130560'),['Voice'] = '', }, 
        },
        [20000468] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000468, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212086, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167109633'),['Voice'] = '', }, 
        },
        [20000469] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000469, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212094, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167109889'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000469, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212094, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167110145'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000469, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212094, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167110401'),['Voice'] = '', }, 
        },
        [20000470] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000470, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212090, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167110657'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000470, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212090, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167110913'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000470, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212090, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167111169'),['Voice'] = '', }, 
        },
        [20000471] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000471, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212073, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167111425'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000471, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212073, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167111681'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000471, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212073, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167111937'),['Voice'] = '', }, 
        },
        [20000472] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167112193'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212075, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167112449'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167112705'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167112961'),['Voice'] = '', }, 
        },
        [20000473] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000473, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212068, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167113217'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000473, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212068, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167113473'),['Voice'] = '', }, 
        },
        [20000474] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212110, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167113729'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212109, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167113985'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212109, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167114241'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212109, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167114497'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212110, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167114753'),['Voice'] = '', }, 
        },
        [20000475] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000475, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212112, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167115009'),['Voice'] = '', }, 
        },
        [20000476] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000476, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212114, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167115265'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000476, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212114, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167115521'),['Voice'] = '', }, 
        },
        [20000477] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000477, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212115, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167115777'),['Voice'] = '', }, 
        },
        [20000478] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000478, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212106, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167116033'),['Voice'] = '', }, 
        },
        [20000479] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000479, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212105, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167116289'),['Voice'] = '', }, 
        },
        [20000480] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000480, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212107, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167116545'),['Voice'] = '', }, 
        },
        [20000481] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167116801'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167117057'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167117313'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167117569'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167117825'),['Voice'] = '', }, 
        },
        [20000482] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000482, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212080, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167118081'),['Voice'] = '', }, 
        },
        [20000483] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000483, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212108, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167118337'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000483, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212108, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167118593'),['Voice'] = '', }, 
        },
        [20000484] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167118849'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119105'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119361'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119617'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119873'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120129'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120385'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120641'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120897'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 10, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167121153'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 11, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167121409'),['Voice'] = '', }, 
        },
        [20000485] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000485, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024142848'),['Voice'] = '', }, 
        },
        [20000486] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000486, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213002, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167123201'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000486, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213002, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167123457'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000486, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213002, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167123713'),['Voice'] = '', }, 
        },
        [20000487] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000487, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213003, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167123969'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000487, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213003, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167124225'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000487, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213003, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167124481'),['Voice'] = '', }, 
        },
        [20000488] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213004, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167124737'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213005, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167124993'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213006, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167125249'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213007, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167125505'),['Voice'] = '', }, 
        },
        [20000489] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167126273'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167126529'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167126785'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167127041'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167127297'),['Voice'] = '', }, 
        },
        [20000490] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000490, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024146944'),['Voice'] = '', }, 
        },
        [20000491] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000491, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024147200'),['Voice'] = '', }, 
        },
        [20000492] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213018, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167129345'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213019, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167129601'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213020, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167129857'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213021, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167130113'),['Voice'] = '', }, 
        },
        [20000493] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000493, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213022, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167130369'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000493, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213022, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167130625'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000493, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213022, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167130881'),['Voice'] = '', }, 
        },
        [20000494] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213025, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167131649'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213023, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167131137'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213023, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167131393'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024150016'),['Voice'] = '', }, 
        },
        [20000495] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000495, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213026, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167132161'),['Voice'] = '', }, 
        },
        [20000496] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000496, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024150528'),['Voice'] = '', }, 
        },
        [20000497] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000497, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024150784'),['Voice'] = '', }, 
        },
        [20000498] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024151040'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167134465'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167134721'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167134977'),['Voice'] = '', }, 
        },
        [20000499] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000499, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213035, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167135233'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000499, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213035, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167135489'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000499, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213035, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167135745'),['Voice'] = '', }, 
        },
        [20000500] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000500, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024152832'),['Voice'] = '', }, 
        },
        [20000501] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000501, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213040, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167136769'),['Voice'] = '', }, 
        },
        [20000502] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000502, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213042, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167137025'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000502, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213042, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167137281'),['Voice'] = '', }, 
        },
        [20000503] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000503, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213043, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167137537'),['Voice'] = '', }, 
        },
        [20000505] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213046, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167138817'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213045, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167139073'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213046, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167139329'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213045, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167139585'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7213046, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167139841'),['Voice'] = '', }, 
        },
        [20000506] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000506, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213052, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167140097'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000506, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213052, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167140353'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000506, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213052, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167140609'),['Voice'] = '', }, 
        },
        [20000507] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000507, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213049, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167140865'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000507, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213048, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167141121'),['Voice'] = '', }, 
        },
        [20000508] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000508, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213050, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167141377'),['Voice'] = '', }, 
        },
        [20000509] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000509, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213051, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167141633'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000509, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213051, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167141889'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000509, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213051, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167142145'),['Voice'] = '', }, 
        },
        [20000510] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000510, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213057, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167142401'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000510, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213057, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167142657'),['Voice'] = '', }, 
        },
        [20000511] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000511, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213058, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167142913'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000511, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213058, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167143169'),['Voice'] = '', }, 
        },
        [20000512] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167144449'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167144961'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167144705'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167145217'),['Voice'] = '', }, 
        },
        [20000513] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213059, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167145473'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213060, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167145729'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213059, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167145985'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213060, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167146241'),['Voice'] = '', }, 
        },
        [20000514] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213063, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167146497'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213062, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167146753'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213063, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167147009'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213062, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167147265'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7213063, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167147521'),['Voice'] = '', }, 
        },
        [20000515] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167147777'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167148033'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167148289'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167148545'),['Voice'] = '', }, 
        },
        [20000516] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000516, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213069, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024163328'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000516, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213069, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167149057'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000516, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213069, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024163840'),['Voice'] = '', }, 
        },
        [20000517] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000517, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167149569'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000517, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167149825'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000517, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167150081'),['Voice'] = '', }, 
        },
        [20000518] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167150337'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167150593'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167150849'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167151105'),['Voice'] = '', }, 
        },
        [20000519] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167151361'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167151617'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167151873'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167152129'),['Voice'] = '', }, 
        },
        [20000520] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167152385'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167152641'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167152897'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167153153'),['Voice'] = '', }, 
        },
        [20000521] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000521, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213061, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167153409'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000521, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213061, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167153665'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000521, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213061, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167153921'),['Voice'] = '', }, 
        },
        [20000522] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000522, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213071, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167154177'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000522, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213072, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167154433'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000522, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213073, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167154689'),['Voice'] = '', }, 
        },
        [20000523] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000523, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167154945'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000523, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167155201'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000523, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167155457'),['Voice'] = '', }, 
        },
        [20000524] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000524, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213087, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167155713'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000524, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213087, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167155969'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000524, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024170752'),['Voice'] = '', }, 
        },
        [20000525] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024171008'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024171264'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024171520'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024171776'),['Voice'] = '', }, 
        },
        [20000526] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000526, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024172032'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000526, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024172288'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000526, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024172544'),['Voice'] = '', }, 
        },
        [20000527] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000527, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024172800'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000527, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024173056'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000527, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024173312'),['Voice'] = '', }, 
        },
        [20000528] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000528, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213078, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024173568'),['Voice'] = '', }, 
        },
        [20000529] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000529, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213077, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024173824'),['Voice'] = '', }, 
        },
        [20000530] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000530, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7233008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024174080'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000530, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7233005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024174336'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 3, ['ID'] = 20000530, ['Kind'] = 4, ['Order'] = 3, ['TalkerID'] = 7233006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024174592'),['Voice'] = '', }, 
        },
        [20000531] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000531, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7233009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024174848'),['Voice'] = '', }, 
        },
        [20000532] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000532, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7233003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024175104'),['Voice'] = '', }, 
        },
        [20000533] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000533, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024175360'),['Voice'] = '', }, 
        },
        [20000534] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000534, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024175616'),['Voice'] = '', }, 
        },
        [20000535] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000535, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024175872'),['Voice'] = '', }, 
        },
        [20000536] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000536, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235021, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024176128'),['Voice'] = '', }, 
        },
        [20000537] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000537, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235022, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024176384'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000537, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235022, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024176640'),['Voice'] = '', }, 
        },
        [20000538] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000538, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235023, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024176896'),['Voice'] = '', }, 
        },
        [20000539] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000539, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024177152'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000539, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024177408'),['Voice'] = '', }, 
        },
        [20000540] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000540, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024177664'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000540, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024177920'),['Voice'] = '', }, 
        },
        [20000541] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000541, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024178176'),['Voice'] = '', }, 
        },
        [20000542] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000542, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024178432'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000542, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235027, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024178688'),['Voice'] = '', }, 
        },
        [20000543] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000543, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024178944'),['Voice'] = '', }, 
        },
        [20000544] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000544, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235031, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024179200'),['Voice'] = '', }, 
        },
        [20000545] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000545, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024179456'),['Voice'] = '', }, 
        },
        [20000546] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000546, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024179712'),['Voice'] = '', }, 
        },
        [20000547] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000547, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024179968'),['Voice'] = '', }, 
        },
        [20000548] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000548, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024180224'),['Voice'] = '', }, 
        },
        [20000549] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024180480'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024180736'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024180992'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7235038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024181248'),['Voice'] = '', }, 
        },
        [20000550] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000550, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024181504'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000550, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024181760'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000550, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024182016'),['Voice'] = '', }, 
        },
        [20000551] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000551, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024182272'),['Voice'] = '', }, 
        },
        [20000552] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000552, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235043, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024182528'),['Voice'] = '', }, 
        },
        [20000553] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000553, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024182784'),['Voice'] = '', }, 
        },
        [20000554] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000554, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024183040'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000554, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024183296'),['Voice'] = '', }, 
        },
        [20000555] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000555, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235048, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024183552'),['Voice'] = '', }, 
        },
        [20000556] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000556, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024183808'),['Voice'] = '', }, 
        },
        [20000557] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000557, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024184064'),['Voice'] = '', }, 
        },
        [20000558] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000558, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235057, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024184320'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000558, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235057, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024184576'),['Voice'] = '', }, 
        },
        [20000559] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000559, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024184832'),['Voice'] = '', }, 
        },
        [20000560] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000560, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235059, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024185088'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000560, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235059, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024185344'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000560, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235059, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024185600'),['Voice'] = '', }, 
        },
        [20000561] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000561, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024185856'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000561, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024186112'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000561, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024186368'),['Voice'] = '', }, 
        },
        [20000562] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000562, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024186624'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000562, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024186880'),['Voice'] = '', }, 
        },
        [20000563] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000563, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024187136'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000563, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024187392'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000563, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235065, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024187648'),['Voice'] = '', }, 
        },
        [20000564] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000564, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024187904'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000564, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024188160'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000564, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024188416'),['Voice'] = '', }, 
        },
        [20000565] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000565, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235067, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024188672'),['Voice'] = '', }, 
        },
        [20000566] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000566, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235068, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024188928'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000566, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235068, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024189184'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000566, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235068, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024189440'),['Voice'] = '', }, 
        },
        [20000567] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000567, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024189696'),['Voice'] = '', }, 
        },
        [20000568] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024189952'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024190208'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024190464'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7235074, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024190720'),['Voice'] = '', }, 
        },
        [20000569] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000569, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024190976'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000569, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024191232'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000569, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235090, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024191488'),['Voice'] = '', }, 
        },
        [20000570] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024191744'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024192000'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024192256'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7235093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024192512'),['Voice'] = '', }, 
        },
        [20000571] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000571, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024192768'),['Voice'] = '', }, 
        },
        [20000572] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000572, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024193024'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000572, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024193280'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000572, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024193536'),['Voice'] = '', }, 
        },
        [20000573] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000573, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024193792'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000573, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024194048'),['Voice'] = '', }, 
        },
        [20000574] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000574, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235077, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024194048'),['Voice'] = '', }, 
        },
        [20000575] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000575, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235102, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024194560'),['Voice'] = '', }, 
        },
        [20000576] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000576, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024194816'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000576, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024195072'),['Voice'] = '', }, 
        },
        [20000577] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000577, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024195328'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000577, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024195584'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000577, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024195840'),['Voice'] = '', }, 
        },
        [20000578] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000578, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7239021, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024196096'),['Voice'] = '', }, 
        },
        [20000579] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000579, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7239016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024196352'),['Voice'] = '', }, 
        },
        [20000580] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000580, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024196608'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000580, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024196864'),['Voice'] = '', }, 
        },
        [20000581] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000581, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024197120'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000581, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024197376'),['Voice'] = '', }, 
        },
        [20000582] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000582, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231022, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024197632'),['Voice'] = '', }, 
        },
        [20000583] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000583, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7239023, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024197888'),['Voice'] = '', }, 
        },
        [20000584] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000584, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024198144'),['Voice'] = '', }, 
        },
        [20000585] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024198400'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7241005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024198656'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7241004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024198912'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7241004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024199168'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7241005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024199424'),['Voice'] = '', }, 
        },
        [20000586] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000586, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024199680'),['Voice'] = '', }, 
        },
        [20000587] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000587, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024199936'),['Voice'] = '', }, 
        },
        [20000588] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000588, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024200192'),['Voice'] = '', }, 
        },
        [20000589] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000589, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024200448'),['Voice'] = '', }, 
        },
        [20000590] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000590, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024200704'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000590, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024200960'),['Voice'] = '', }, 
        },
        [20000591] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000591, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024201216'),['Voice'] = '', }, 
        },
        [20000592] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000592, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024201472'),['Voice'] = '', }, 
        },
        [20000593] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000593, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242010, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024201728'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000593, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024201984'),['Voice'] = '', }, 
        },
        [20000594] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000594, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024202240'),['Voice'] = '', }, 
        },
        [20000595] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000595, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242013, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024202496'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000595, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024202752'),['Voice'] = '', }, 
        },
        [20000596] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000596, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024203008'),['Voice'] = '', }, 
        },
        [20000597] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000597, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024203264'),['Voice'] = '', }, 
        },
        [20000598] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000598, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242019, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024203520'),['Voice'] = '', }, 
        },
        [20000599] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000599, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024203776'),['Voice'] = '', }, 
        },
        [20000600] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000600, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242022, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024204032'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000600, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242023, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024204288'),['Voice'] = '', }, 
        },
        [20000601] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000601, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024204544'),['Voice'] = '', }, 
        },
        [20000602] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000602, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167149569'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000602, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167149825'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000602, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167150081'),['Voice'] = '', }, 
        },
        [20000603] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167118849'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119105'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119361'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119617'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167119873'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120129'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120385'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120641'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167120897'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 10, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167121153'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 11, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167121409'),['Voice'] = '', }, 
        },
        [20000604] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000604, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211101, ['Text'] = Game.TableDataManager:GetLangStr('str_56764166879489'),['Voice'] = '', }, {['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000604, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211102, ['Text'] = Game.TableDataManager:GetLangStr('str_56764166879745'),['Voice'] = '', }, 
        },
        [20000605] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000605, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212005, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167065601'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000605, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212005, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167065857'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000605, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212005, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167066113'),['Voice'] = '', }, 
        },
        [20000606] = {{['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167103745'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167104001'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167104257'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167104513'),['Voice'] = '', }, 
        },
        [20000607] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000607, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024210688'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000607, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024210944'),['Voice'] = '', }, 
        },
        [20000608] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000608, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265004, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024211200'),['Voice'] = '', }, 
        },
        [20000609] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024211456'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024211712'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024211968'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024212224'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024212480'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024212736'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7265006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024212992'),['Voice'] = '', }, 
        },
        [20000610] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000610, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024213248'),['Voice'] = '', }, 
        },
        [20000611] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000611, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024213504'),['Voice'] = '', }, 
        },
        [20000612] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000612, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024213760'),['Voice'] = '', }, 
        },
        [20000613] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000613, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265011, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024178432'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000613, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024178688'),['Voice'] = '', }, 
        },
        [20000614] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024214528'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024214784'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024215040'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265015, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024215296'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024215552'),['Voice'] = '', }, 
        },
        [20000615] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265021, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024215808'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265021, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024216064'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265022, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024216320'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265021, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024216576'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265022, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024216832'),['Voice'] = '', }, 
        },
        [20000616] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000616, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024217088'),['Voice'] = '', }, 
        },
        [20000617] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000617, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024217344'),['Voice'] = '', }, 
        },
        [20000618] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000618, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265023, ['Text'] = Game.TableDataManager:GetLangStr('str_54632789379584'),['Voice'] = '', }, 
        },
        [20000619] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000619, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024217856'),['Voice'] = '', }, 
        },
        [20000620] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000620, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265034, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024218112'),['Voice'] = '', }, 
        },
        [20000621] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000621, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024181504'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000621, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024218624'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000621, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265028, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024218880'),['Voice'] = '', }, 
        },
        [20000622] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000622, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024219136'),['Voice'] = '', }, 
        },
        [20000623] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000623, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265031, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024219392'),['Voice'] = '', }, 
        },
        [20000624] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000624, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265035, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024219648'),['Voice'] = '', }, 
        },
        [20000625] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000625, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024219904'),['Voice'] = '', }, 
        },
        [20000626] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000626, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265037, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024182784'),['Voice'] = '', }, 
        },
        [20000627] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000627, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265038, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024220416'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000627, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024220672'),['Voice'] = '', }, 
        },
        [20000628] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000628, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024220928'),['Voice'] = '', }, 
        },
        [20000629] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000629, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024221184'),['Voice'] = '', }, 
        },
        [20000630] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000630, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024221440'),['Voice'] = '', }, 
        },
        [20000631] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000631, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024221696'),['Voice'] = '', }, 
        },
        [20000632] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000632, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024221952'),['Voice'] = '', }, 
        },
        [20000633] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000633, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024222208'),['Voice'] = '', }, 
        },
        [20000634] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000634, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024222464'),['Voice'] = '', }, 
        },
        [20000635] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000635, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024222720'),['Voice'] = '', }, 
        },
        [20000636] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000636, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024222976'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000636, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265057, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024223232'),['Voice'] = '', }, 
        },
        [20000637] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000637, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265059, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024223488'),['Voice'] = '', }, 
        },
        [20000638] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000638, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024223744'),['Voice'] = '', }, 
        },
        [20000639] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000639, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265094, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024224000'),['Voice'] = '', }, 
        },
        [20000640] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000640, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265061, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024224256'),['Voice'] = '', }, 
        },
        [20000641] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000641, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024224512'),['Voice'] = '', }, 
        },
        [20000642] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000642, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024224768'),['Voice'] = '', }, 
        },
        [20000643] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000643, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024225024'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000643, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024225280'),['Voice'] = '', }, 
        },
        [20000644] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000644, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265068, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024225536'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000644, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265068, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024225792'),['Voice'] = '', }, 
        },
        [20000645] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000645, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265067, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024226048'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000645, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265067, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024226304'),['Voice'] = '', }, 
        },
        [20000646] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000646, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024226560'),['Voice'] = '', }, 
        },
        [20000647] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000647, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024185856'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000647, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024186112'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000647, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024186368'),['Voice'] = '', }, 
        },
        [20000648] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000648, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265074, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024227584'),['Voice'] = '', }, 
        },
        [20000649] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000649, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024227840'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000649, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024228096'),['Voice'] = '', }, 
        },
        [20000650] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000650, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024228352'),['Voice'] = '', }, 
        },
        [20000651] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000651, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265078, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024176896'),['Voice'] = '', }, 
        },
        [20000652] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265081, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024228864'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024229120'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024229376'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024229632'),['Voice'] = '', }, 
        },
        [20000653] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000653, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024229888'),['Voice'] = '', }, 
        },
        [20000654] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000654, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024230144'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000654, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024186880'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000654, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024230656'),['Voice'] = '', }, 
        },
        [20000655] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000655, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024187904'),['Voice'] = '', }, 
        },
        [20000656] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000656, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265086, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024231168'),['Voice'] = '', }, 
        },
        [20000657] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000657, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024231424'),['Voice'] = '', }, 
        },
        [20000658] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000658, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024231680'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000658, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024231936'),['Voice'] = '', }, 
        },
        [20000659] = {{['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024232192'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024232448'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024232704'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024232960'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024233216'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024233472'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024233728'),['Voice'] = '', }, 
        },
        [20000660] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000660, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265090, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024233984'),['Voice'] = '', }, 
        },
        [20000661] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000661, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024234240'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000661, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024234496'),['Voice'] = '', }, 
        },
        [20000662] = {{['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000662, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024234752'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000662, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7241007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024235008'),['Voice'] = '', }, 
        },
        [20000663] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764166965505'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764166965761'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764166966017'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250117, ['Text'] = Game.TableDataManager:GetLangStr('str_56764166966273'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250116, ['Text'] = Game.TableDataManager:GetLangStr('str_56764166966529'),['Voice'] = '', }, 
        },
        [20000664] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024236544'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250146, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024236800'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250146, ['Text'] = Game.TableDataManager:GetLangStr('str_58139093249280'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_58139093249536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_58139093249792'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250146, ['Text'] = Game.TableDataManager:GetLangStr('str_58139093250048'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024238080'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024238336'),['Voice'] = '', }, 
        },
        [20000665] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000665, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250152, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024238592'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000665, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250153, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024238848'),['Voice'] = '', }, 
        },
        [20000666] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000666, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250154, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024239104'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000666, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250155, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024239360'),['Voice'] = '', }, 
        },
        [20000667] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000667, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250156, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024239616'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000667, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250157, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024239872'),['Voice'] = '', }, 
        },
        [20000668] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000668, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250160, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024240128'),['Voice'] = '', }, 
        },
        [20000669] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000669, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250164, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024240384'),['Voice'] = '', }, 
        },
        [20000670] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000670, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250165, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024240640'),['Voice'] = '', }, 
        },
        [20000671] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000671, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250140, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024240896'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000671, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250140, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024241152'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000671, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250140, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024241408'),['Voice'] = '', }, 
        },
        [20000672] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000672, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024241664'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000672, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219019, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024241920'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000672, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024242176'),['Voice'] = '', }, 
        },
        [20000673] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000673, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265047, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024242432'),['Voice'] = '', }, 
        },
        [20000674] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000674, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255008, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024242688'),['Voice'] = '', }, 
        },
        [20000675] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000675, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024242944'),['Voice'] = '', }, 
        },
        [20000676] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000676, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255007, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024243200'),['Voice'] = '', }, 
        },
        [20000677] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000677, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219024, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024243456'),['Voice'] = '', }, 
        },
        [20000678] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265093, ['Text'] = Game.TableDataManager:GetLangStr('str_58481348450560'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024243968'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265092, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024244224'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024244480'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265092, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024244736'),['Voice'] = '', }, 
        },
        [20000679] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000679, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250178, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024244992'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000679, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250180, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024245248'),['Voice'] = '', }, 
        },
        [20000680] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000680, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250182, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024245504'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000680, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250181, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024245760'),['Voice'] = '', }, 
        },
        [20000681] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000681, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250183, ['Text'] = Game.TableDataManager:GetLangStr('str_39103529937664'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000681, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250184, ['Text'] = Game.TableDataManager:GetLangStr('str_39103529937920'),['Voice'] = '', }, 
        },
        [20000682] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000682, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210852, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024033536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000682, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210853, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024033792'),['Voice'] = '', }, 
        },
        [20000683] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000683, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250185, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024247040'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000683, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250185, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024247296'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000683, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250185, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024247552'),['Voice'] = '', }, 
        },
        [20000684] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000684, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250200, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024247808'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000684, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250200, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024248064'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000684, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250201, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024248320'),['Voice'] = '', }, 
        },
        [20000685] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000685, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250222, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024248576'),['Voice'] = '', }, 
        },
        [20000686] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000686, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207100, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024248832'),['Voice'] = '', }, 
        },
        [20000687] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000687, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024249088'),['Voice'] = '', }, 
        },
        [20000688] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000688, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235102, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024249344'),['Voice'] = '', }, 
        },
        [20000689] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000689, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235098, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024249600'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000689, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235098, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024249856'),['Voice'] = '', }, 
        },
        [20000690] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000690, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211045, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024250112'),['Voice'] = '', }, 
        },
        [20000691] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000691, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219044, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024250368'),['Voice'] = '', }, 
        },
        [20000692] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000692, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024250624'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000692, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024250880'),['Voice'] = '', }, 
        },
        [20000693] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000693, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024251136'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000693, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024251392'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000693, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024251648'),['Voice'] = '', }, 
        },
        [20000694] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000694, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024251904'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000694, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7830268, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024252160'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000694, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024252416'),['Voice'] = '', }, 
        },
        [20000695] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000695, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024252672'),['Voice'] = '', }, 
        },
        [20000696] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000696, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250126, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024252928'),['Voice'] = '', }, 
        },
        [20000697] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000697, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024253184'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000697, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235090, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024253440'),['Voice'] = '', }, 
        },
        [20000698] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000698, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024253696'),['Voice'] = '', }, 
        },
        [20000699] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000699, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024253952'),['Voice'] = '', }, 
        },
        [20000700] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000700, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024254208'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000700, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024254464'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000700, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024254720'),['Voice'] = '', }, 
        },
        [20000701] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000701, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024254976'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000701, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7241001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024255232'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000701, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7241009, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024255488'),['Voice'] = '', }, 
        },
        [20000702] = {{['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000702, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250230, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024255744'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000702, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250231, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024256000'),['Voice'] = '', }, 
        },
        [20000703] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000703, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235095, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024256256'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000703, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235096, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024256512'),['Voice'] = '', }, 
        },
        [20000704] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000704, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242118, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024256768'),['Voice'] = '', }, 
        },
        [20000705] = {{['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024257024'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250232, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024257280'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250233, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024257280'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250234, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024257792'),['Voice'] = '', }, 
        },
        [20000706] = {{['Delay'] = 0.5, ['Duration'] = 3.5, ['ID'] = 20000706, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250235, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024258048'),['Voice'] = '', }, 
        },
        [20000707] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024258304'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024258560'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024258816'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242053, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024259072'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023961088'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024259584'),['Voice'] = '', }, 
        },
        [20000708] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000708, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024259840'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000708, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024260096'),['Voice'] = '', }, 
        },
        [20000709] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000709, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024260352'),['Voice'] = '', }, 
        },
        [20000710] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024260608'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242061, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024260864'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242060, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024261120'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242061, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024261376'),['Voice'] = '', }, 
        },
        [20000711] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024261632'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024261888'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242063, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024262144'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242062, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024262400'),['Voice'] = '', }, 
        },
        [20000712] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000712, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024262656'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000712, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024262912'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000712, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024263168'),['Voice'] = '', }, 
        },
        [20000713] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000713, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242065, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024263424'),['Voice'] = '', }, 
        },
        [20000714] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000714, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024263680'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000714, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024263936'),['Voice'] = '', }, 
        },
        [20000715] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000715, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024264192'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000715, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024264448'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000715, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242072, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024264704'),['Voice'] = '', }, 
        },
        [20000716] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000716, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242074, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024264960'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000716, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242074, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024265216'),['Voice'] = '', }, 
        },
        [20000717] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000717, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024265472'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000717, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024265728'),['Voice'] = '', }, 
        },
        [20000718] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000718, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024265984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000718, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242077, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024266240'),['Voice'] = '', }, 
        },
        [20000719] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000719, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242078, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024266496'),['Voice'] = '', }, 
        },
        [20000720] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000720, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024266752'),['Voice'] = '', }, 
        },
        [20000721] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000721, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024267008'),['Voice'] = '', }, 
        },
        [20000722] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000722, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242081, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024267264'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000722, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024267520'),['Voice'] = '', }, 
        },
        [20000723] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000723, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024267776'),['Voice'] = '', }, 
        },
        [20000724] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000724, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242086, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024268032'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000724, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024268288'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000724, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242086, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024268544'),['Voice'] = '', }, 
        },
        [20000725] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000725, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024268800'),['Voice'] = '', }, 
        },
        [20000726] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000726, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024269056'),['Voice'] = '', }, 
        },
        [20000727] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000727, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242090, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024269312'),['Voice'] = '', }, 
        },
        [20000728] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000728, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024269568'),['Voice'] = '', }, 
        },
        [20000729] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024269824'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024270080'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024270336'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024270592'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024270848'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024271104'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024271360'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024271616'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024271872'),['Voice'] = '', }, 
        },
        [20000730] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024272128'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024272384'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024272640'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024272896'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242050, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024273152'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242051, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024273408'),['Voice'] = '', }, 
        },
        [20000731] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000731, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242052, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024273664'),['Voice'] = '', }, 
        },
        [20000732] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000732, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250236, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024273920'),['Voice'] = '', }, 
        },
        [20000733] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250239, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024274176'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250240, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024274432'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250238, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024274688'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250241, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024274944'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250239, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024275200'),['Voice'] = '', }, 
        },
        [20000734] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000734, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250242, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024275456'),['Voice'] = '', }, 
        },
        [20000735] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000735, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250245, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024275712'),['Voice'] = '', }, 
        },
        [20000736] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000736, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250249, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024275968'),['Voice'] = '', }, 
        },
        [20000737] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000737, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223018, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024276224'),['Voice'] = '', }, 
        },
        [20000738] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000738, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250253, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024276480'),['Voice'] = '', }, 
        },
        [20000739] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000739, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250256, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024276736'),['Voice'] = '', }, 
        },
        [20000740] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000740, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250186, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024276992'),['Voice'] = '', }, 
        },
        [20000741] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000741, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250189, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024277248'),['Voice'] = '', }, 
        },
        [20000742] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000742, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250258, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024277504'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000742, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7250259, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024277760'),['Voice'] = '', }, 
        },
        [20000743] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000743, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024278016'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000743, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024278272'),['Voice'] = '', }, 
        },
        [20000744] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000744, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024279040'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 5, ['ID'] = 20000744, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024279296'),['Voice'] = '', }, 
        },
        [20000745] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000745, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024279552'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000745, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024279808'),['Voice'] = '', }, {['Delay'] = 6.5, ['Duration'] = 4, ['ID'] = 20000745, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024280064'),['Voice'] = '', }, 
        },
        [20000746] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000746, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024280320'),['Voice'] = '', }, 
        },
        [20000747] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000747, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250304, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024280576'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000747, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250304, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024280832'),['Voice'] = '', }, 
        },
        [20000748] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000748, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024281088'),['Voice'] = '', }, 
        },
        [20000749] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000749, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211040, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024281344'),['Voice'] = '', }, 
        },
        [20000750] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000750, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250305, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024281600'),['Voice'] = '', }, 
        },
        [20000751] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000751, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024281856'),['Voice'] = '', }, 
        },
        [20000752] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000752, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024282112'),['Voice'] = '', }, 
        },
        [20000753] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000753, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212119, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024282368'),['Voice'] = '', }, 
        },
        [20000754] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000754, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212119, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024282624'),['Voice'] = '', }, 
        },
        [20000755] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000755, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7200001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024282880'),['Voice'] = '', }, 
        },
        [20000756] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000756, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7200001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024283136'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000756, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7220006, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024283392'),['Voice'] = '', }, 
        },
        [20000757] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000757, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250346, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024002304'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000757, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250347, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167053569'),['Voice'] = '', }, 
        },
        [20000758] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000758, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250345, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167054849'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000758, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250344, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024284416'),['Voice'] = '', }, 
        },
        [20000759] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000759, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250349, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167054849'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000759, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250348, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024284416'),['Voice'] = '', }, 
        },
        [20000760] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250352, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024249600'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250351, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024249856'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250350, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023955200'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 5, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250353, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024001280'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 2, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250354, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024007424'),['Voice'] = '', }, {['Delay'] = 9, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250357, ['Text'] = Game.TableDataManager:GetLangStr('str_56764167054849'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7250358, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024286720'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7250359, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024286976'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7250355, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024287232'),['Voice'] = '', }, 
        },
        [20000761] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000761, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024287488'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000761, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024287744'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000761, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024288000'),['Voice'] = '', }, 
        },
        [20000762] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000762, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024288256'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000762, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024288512'),['Voice'] = '', }, 
        },
        [20000763] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000763, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212105, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024288768'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 3, ['ID'] = 20000763, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212105, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024289024'),['Voice'] = '', }, 
        },
        [20000764] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000764, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024289280'),['Voice'] = '', }, 
        },
        [20000765] = {{['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000765, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024289536'),['Voice'] = '', }, 
        },
        [20000766] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000766, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250342, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024289792'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000766, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250342, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024290048'),['Voice'] = '', }, 
        },
        [20000767] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024290304'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 5, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024290560'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 5, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024290816'),['Voice'] = '', }, {['Delay'] = 13, ['Duration'] = 5, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024291072'),['Voice'] = '', }, {['Delay'] = 18, ['Duration'] = 6, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024291328'),['Voice'] = '', }, 
        },
        [20000768] = {{['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000768, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024278528'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000768, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024278784'),['Voice'] = '', }, 
        },
        [20000769] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000769, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250363, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024291584'),['Voice'] = '', }, 
        },
        [20000770] = {{['Delay'] = 1, ['Duration'] = 6, ['ID'] = 20000770, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250363, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024291840'),['Voice'] = '', }, 
        },
        [20000771] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000771, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250363, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024292096'),['Voice'] = '', }, 
        },
        [20000772] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000772, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024292352'),['Voice'] = '', }, 
        },
        [20000773] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000773, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212064, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024292608'),['Voice'] = '', }, 
        },
        [20000774] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000774, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242175, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024292864'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 5, ['ID'] = 20000774, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024293120'),['Voice'] = '', }, {['Delay'] = 10, ['Duration'] = 5, ['ID'] = 20000774, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219036, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024293376'),['Voice'] = '', }, 
        },
        [20000775] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000775, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250375, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024293632'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000775, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7250375, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024293888'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000775, ['Kind'] = 4, ['Order'] = 3, ['TalkerID'] = 7250375, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024294144'),['Voice'] = '', }, 
        },
        [20000776] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000776, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250376, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024294400'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000776, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250376, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024294656'),['Voice'] = '', }, 
        },
        [20000777] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000777, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250379, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024294912'),['Voice'] = '', }, 
        },
        [20000778] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000778, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250381, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024295168'),['Voice'] = '', }, 
        },
        [20000779] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000779, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250383, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024295424'),['Voice'] = '', }, 
        },
        [20000780] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000780, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250388, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024295680'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000780, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250394, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024295936'),['Voice'] = '', }, 
        },
        [20000781] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000781, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250391, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024296192'),['Voice'] = '', }, 
        },
        [20000782] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000782, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250387, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024296448'),['Voice'] = '', }, 
        },
        [20000783] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000783, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250398, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024296704'),['Voice'] = '', }, 
        },
        [20000784] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000784, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250389, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024296960'),['Voice'] = 'Play_MX_Gameplay_Subplot_AsYouWish_Theme_MusicBox.Play_MX_Gameplay_Subplot_AsYouWish_Theme_MusicBox', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000784, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250389, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024297216'),['Voice'] = '', }, 
        },
        [20000785] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000785, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250386, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024297472'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000785, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250386, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024297728'),['Voice'] = '', }, 
        },
        [20000786] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000786, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250392, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024297984'),['Voice'] = '', }, 
        },
        [20000787] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000787, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250393, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024298240'),['Voice'] = '', }, 
        },
        [20000788] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024298496'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024298752'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024299008'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024299264'),['Voice'] = '', }, 
        },
        [20000789] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000789, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250413, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024299520'),['Voice'] = '', }, 
        },
        [20000790] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000790, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265289, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024299776'),['Voice'] = '', }, 
        },
        [20000791] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000791, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250415, ['Text'] = Game.TableDataManager:GetLangStr('str_39104603633152'),['Voice'] = '', }, 
        },
        [20000792] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000792, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250416, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024300288'),['Voice'] = '', }, 
        },
        [20000793] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000793, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250417, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024300544'),['Voice'] = '', }, 
        },
        [20000794] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000794, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024300800'),['Voice'] = '', }, 
        },
        [20000795] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000795, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250419, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024301056'),['Voice'] = '', }, 
        },
        [20000796] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265292, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024301312'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265293, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024301568'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265294, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024301824'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265292, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024302080'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265292, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024302336'),['Voice'] = '', }, 
        },
        [20000797] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000797, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7227005, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024302592'),['Voice'] = '', }, 
        },
        [20000798] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000798, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250422, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024302848'),['Voice'] = '', }, 
        },
        [20000799] = {{['Delay'] = 2, ['Duration'] = 2, ['ID'] = 20000799, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250423, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024303104'),['Voice'] = '', }, 
        },
        [20000800] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000800, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265325, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024303360'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000800, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265325, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024303616'),['Voice'] = '', }, {['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000800, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265325, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024303872'),['Voice'] = '', }, 
        },
        [20000801] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000801, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250427, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024304128'),['Voice'] = '', }, 
        },
        [20000802] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000802, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250429, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024304384'),['Voice'] = '', }, 
        },
        [20000803] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000803, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242191, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024304640'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 6, ['ID'] = 20000803, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024304896'),['Voice'] = '', }, 
        },
        [20000804] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000804, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024305152'),['Voice'] = '', }, 
        },
        [20000805] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000805, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242191, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024305408'),['Voice'] = '', }, {['Delay'] = 5.5, ['Duration'] = 5, ['ID'] = 20000805, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024305664'),['Voice'] = '', }, 
        },
        [20000806] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000806, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242192, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024305920'),['Voice'] = '', }, 
        },
        [20000807] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000807, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242192, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024306176'),['Voice'] = '', }, 
        },
        [20000808] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000808, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242192, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024306432'),['Voice'] = '', }, 
        },
        [20000809] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000809, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250430, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024306688'),['Voice'] = '', }, 
        },
        [20000810] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000810, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242193, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024306944'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000810, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242193, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024307200'),['Voice'] = '', }, 
        },
        [20000811] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000811, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242193, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024307456'),['Voice'] = '', }, 
        },
        [20000812] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000812, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242195, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024307712'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 6, ['ID'] = 20000812, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242195, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024307968'),['Voice'] = '', }, {['Delay'] = 13, ['Duration'] = 9, ['ID'] = 20000812, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242195, ['Text'] = Game.TableDataManager:GetLangStr('str_29757412517888'),['Voice'] = '', }, 
        },
        [20000813] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000813, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242196, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024308480'),['Voice'] = '', }, 
        },
        [20000814] = {{['Delay'] = 2, ['Duration'] = 8, ['ID'] = 20000814, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242197, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024308736'),['Voice'] = '', }, 
        },
        [20000815] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000815, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213030, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024308992'),['Voice'] = '', }, 
        },
        [20000816] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000816, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213019, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024309248'),['Voice'] = '', }, 
        },
        [20000817] = {{['Delay'] = 1.5, ['Duration'] = 3, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 1, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024142848'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 2, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 2, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024309760'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 2, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 3, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024310016'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 2, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 4, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024310272'),['Voice'] = '', }, 
        },
        [20000818] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024142848'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024309760'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024310016'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 2, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024310272'),['Voice'] = '', }, 
        },
        [20000819] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000819, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250435, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024311552'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000819, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7250435, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024311808'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000819, ['Kind'] = 4, ['Order'] = 3, ['TalkerID'] = 7250435, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024312064'),['Voice'] = '', }, 
        },
        [20000820] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024312320'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 4, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024312576'),['Voice'] = '', }, {['Delay'] = 9, ['Duration'] = 4, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024312832'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 5, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024313088'),['Voice'] = '', }, 
        },
        [20000821] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000821, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250436, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024313344'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 4, ['ID'] = 20000821, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250437, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024313600'),['Voice'] = '', }, 
        },
        [20000822] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000822, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250438, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024313856'),['Voice'] = '', }, {['Delay'] = 5.5, ['Duration'] = 4, ['ID'] = 20000822, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250439, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024314112'),['Voice'] = '', }, 
        },
        [20000823] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250440, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024314368'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 4, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250441, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024314624'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250440, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024314880'),['Voice'] = '', }, {['Delay'] = 12.5, ['Duration'] = 4, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250441, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024315136'),['Voice'] = '', }, 
        },
        [20000824] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250442, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024315392'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3.5, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250443, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024315648'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 4, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250442, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024315904'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 2.5, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250443, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024316160'),['Voice'] = '', }, 
        },
        [20000825] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265338, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024316416'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265339, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024316672'),['Voice'] = '', }, {['Delay'] = 10, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265339, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024316928'),['Voice'] = '', }, {['Delay'] = 13.5, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265338, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024317184'),['Voice'] = '', }, 
        },
        [20000826] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000826, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265336, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024317440'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 2, ['ID'] = 20000826, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265337, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024317696'),['Voice'] = '', }, 
        },
        [20000827] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000827, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265334, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024317952'),['Voice'] = '', }, 
        },
        [20000828] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000828, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265333, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024318208'),['Voice'] = '', }, 
        },
        [20000829] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000829, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265332, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024318464'),['Voice'] = '', }, 
        },
        [20000830] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000830, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250445, ['Text'] = Game.TableDataManager:GetLangStr('str_39103529906688'),['Voice'] = '', }, 
        },
        [20000831] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000831, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250447, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024318976'),['Voice'] = '', }, 
        },
        [20000832] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000832, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024319232'),['Voice'] = '', }, 
        },
        [20000833] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000833, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255070, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024059648'),['Voice'] = '', }, 
        },
        [20000834] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000834, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255071, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024319744'),['Voice'] = '', }, 
        },
        [20000835] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000835, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024320000'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000835, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255073, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024320256'),['Voice'] = '', }, 
        },
        [20000836] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000836, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255075, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024320512'),['Voice'] = '', }, 
        },
        [20000837] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024320768'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255077, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024321024'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024321280'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024321536'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7255077, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024321792'),['Voice'] = '', }, 
        },
        [20000838] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000838, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255078, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024322048'),['Voice'] = '', }, 
        },
        [20000839] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000839, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255080, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024059648'),['Voice'] = '', }, 
        },
        [20000840] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000840, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255081, ['Text'] = Game.TableDataManager:GetLangStr('str_39103529750528'),['Voice'] = '', }, 
        },
        [20000841] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000841, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024322816'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000841, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024323072'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000841, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255082, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024323328'),['Voice'] = '', }, 
        },
        [20000842] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000842, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024323584'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000842, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024323840'),['Voice'] = '', }, 
        },
        [20000843] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000843, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024324096'),['Voice'] = '', }, 
        },
        [20000844] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000844, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023967744'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000844, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024324608'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000844, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255085, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024324864'),['Voice'] = '', }, 
        },
        [20000845] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000845, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255086, ['Text'] = Game.TableDataManager:GetLangStr('str_38758590283264'),['Voice'] = '', }, 
        },
        [20000846] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000846, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255087, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024325376'),['Voice'] = '', }, 
        },
        [20000847] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000847, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255088, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024325632'),['Voice'] = '', }, 
        },
        [20000848] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000848, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255089, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024325888'),['Voice'] = '', }, 
        },
        [20000849] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000849, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255090, ['Text'] = Game.TableDataManager:GetLangStr('str_38758590283264'),['Voice'] = '', }, 
        },
        [20000850] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000850, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024326400'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000850, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024326656'),['Voice'] = '', }, 
        },
        [20000851] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000851, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255092, ['Text'] = Game.TableDataManager:GetLangStr('str_38758590283264'),['Voice'] = '', }, 
        },
        [20000852] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000852, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255093, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024327168'),['Voice'] = '', }, 
        },
        [20000853] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000853, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255094, ['Text'] = Game.TableDataManager:GetLangStr('str_38758590283264'),['Voice'] = '', }, 
        },
        [20000854] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000854, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255095, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024327680'),['Voice'] = '', }, 
        },
        [20000855] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000855, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255096, ['Text'] = Game.TableDataManager:GetLangStr('str_38758590283264'),['Voice'] = '', }, 
        },
        [20000856] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000856, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255097, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024328192'),['Voice'] = '', }, 
        },
        [20000857] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000857, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255098, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024328448'),['Voice'] = '', }, 
        },
        [20000858] = {{['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000858, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255099, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024328704'),['Voice'] = '', }, 
        },
        [20000859] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000859, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255100, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024328960'),['Voice'] = '', }, 
        },
        [20000860] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255101, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024329216'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255102, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024329472'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255101, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024329728'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255102, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024329984'),['Voice'] = '', }, 
        },
        [20000861] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000861, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255103, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024330240'),['Voice'] = '', }, 
        },
        [20000862] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000862, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255103, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024330496'),['Voice'] = '', }, 
        },
        [20000863] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000863, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255104, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024330752'),['Voice'] = '', }, 
        },
        [20000864] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000864, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255106, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024331008'),['Voice'] = '', }, 
        },
        [20000865] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000865, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255107, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024331264'),['Voice'] = '', }, 
        },
        [20000866] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000866, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255108, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024331520'),['Voice'] = '', }, 
        },
        [20000867] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000867, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255109, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024331776'),['Voice'] = '', }, 
        },
        [20000868] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000868, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255110, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024332032'),['Voice'] = '', }, 
        },
        [20000869] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000869, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255111, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024332288'),['Voice'] = '', }, 
        },
        [20000870] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000870, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255112, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024332544'),['Voice'] = '', }, 
        },
        [20000871] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000871, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255113, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024332800'),['Voice'] = '', }, 
        },
        [20000872] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000872, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255114, ['Text'] = Game.TableDataManager:GetLangStr('str_58139093292032'),['Voice'] = '', }, 
        },
        [20000873] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000873, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255118, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024333312'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000873, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255118, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024333568'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000873, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255118, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024333824'),['Voice'] = '', }, 
        },
        [20000874] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000874, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255120, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024334080'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000874, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255120, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024334336'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000874, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255120, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024334592'),['Voice'] = '', }, 
        },
        [20000875] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000875, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255121, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024334848'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000875, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255121, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024335104'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000875, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255121, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024335360'),['Voice'] = '', }, 
        },
        [20000876] = {{['Delay'] = 0, ['Duration'] = 3.5, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250455, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024335616'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250456, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024335872'),['Voice'] = '', }, {['Delay'] = 8.5, ['Duration'] = 4, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250455, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024336128'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 3, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250456, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024336384'),['Voice'] = '', }, 
        },
        [20000877] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000877, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250453, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024336640'),['Voice'] = '', }, 
        },
        [20000878] = {{['Delay'] = 0, ['Duration'] = 8, ['ID'] = 20000878, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265363, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024336896'),['Voice'] = '', }, 
        },
        [20000879] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000879, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265366, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024337152'),['Voice'] = '', }, 
        },
        [20000880] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000880, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265367, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024337408'),['Voice'] = '', }, 
        },
        [20000881] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250461, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024337664'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250460, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024337920'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250461, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024338176'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250460, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024338432'),['Voice'] = '', }, {['Delay'] = 16, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250460, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024338688'),['Voice'] = '', }, {['Delay'] = 20, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250461, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024338944'),['Voice'] = '', }, 
        },
        [20000883] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000883, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250457, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024339200'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000883, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250457, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024339456'),['Voice'] = '', }, 
        },
        [20000884] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000884, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250462, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024339712'),['Voice'] = '', }, 
        },
        [20000885] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000885, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250457, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024339968'),['Voice'] = '', }, 
        },
        [20000886] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000886, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250459, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024340224'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000886, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250459, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024340480'),['Voice'] = '', }, 
        },
        [20000887] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000887, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250465, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024340736'),['Voice'] = '', }, 
        },
        [20000888] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000888, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255122, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024340992'),['Voice'] = '', }, 
        },
        [20000889] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000889, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250468, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024341248'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000889, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250468, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024341504'),['Voice'] = '', }, 
        },
        [20000890] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000890, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250590, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024341760'),['Voice'] = '', }, 
        },
        [20000891] = {{['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000891, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250470, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024342016'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 3, ['ID'] = 20000891, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250470, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024342272'),['Voice'] = '', }, 
        },
        [20000892] = {{['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000892, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250591, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024342528'),['Voice'] = '', }, 
        },
        [20000893] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000893, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207083, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024342784'),['Voice'] = '', }, 
        },
        [20000894] = {{['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000894, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250592, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024343040'),['Voice'] = '', }, 
        },
        [20000895] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000895, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250374, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024343296'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000895, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250374, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024343552'),['Voice'] = '', }, {['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000895, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250374, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024343808'),['Voice'] = '', }, 
        },
        [20000896] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000896, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211067, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024344064'),['Voice'] = '', }, 
        },
        [20000897] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000897, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024344320'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 4, ['ID'] = 20000897, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024344576'),['Voice'] = '', }, 
        },
        [20000898] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000898, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024344832'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 4, ['ID'] = 20000898, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024345088'),['Voice'] = '', }, 
        },
        [20000899] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000899, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211014, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024345344'),['Voice'] = '', }, 
        },
        [20000900] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000900, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211084, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024345600'),['Voice'] = '', }, 
        },
        [20000901] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000901, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265329, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024345856'),['Voice'] = '', }, 
        },
        [20000902] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000902, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265331, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024346112'),['Voice'] = '', }, 
        },
        [20000903] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000903, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265343, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024346368'),['Voice'] = '', }, 
        },
        [20000904] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000904, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265340, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024346624'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000904, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265340, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024346880'),['Voice'] = '', }, 
        },
        [20000905] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000905, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265341, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024347136'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000905, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265341, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024347392'),['Voice'] = '', }, 
        },
        [20000906] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000906, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024347648'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000906, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024347904'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 4, ['ID'] = 20000906, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024348160'),['Voice'] = '', }, 
        },
        [20000907] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000907, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250498, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024348416'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000907, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250498, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024348672'),['Voice'] = '', }, 
        },
        [20000908] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000908, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250499, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024348928'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000908, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250499, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024349184'),['Voice'] = '', }, 
        },
        [20000909] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000909, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211102, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024349440'),['Voice'] = '', }, 
        },
        [20000910] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000910, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024349696'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000910, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223055, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024349952'),['Voice'] = '', }, 
        },
        [20000911] = {{['Delay'] = 6, ['Duration'] = 4, ['ID'] = 20000911, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213076, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024350208'),['Voice'] = '', }, 
        },
        [20000912] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000912, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213078, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024350464'),['Voice'] = '', }, 
        },
        [20000913] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000913, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024350720'),['Voice'] = '', }, 
        },
        [20000914] = {{['Delay'] = 9, ['Duration'] = 3, ['ID'] = 20000914, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211057, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024350976'),['Voice'] = '', }, 
        },
        [20000915] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000915, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223054, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024351232'),['Voice'] = '', }, 
        },
        [20000916] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024351488'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207030, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024351744'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024352000'),['Voice'] = '', }, {['Delay'] = 10.5, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7207030, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024352256'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7207029, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024352512'),['Voice'] = '', }, 
        },
        [20000917] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000917, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250512, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024352768'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000917, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250513, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024353024'),['Voice'] = '', }, 
        },
        [20000918] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000918, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250514, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024353280'),['Voice'] = '', }, 
        },
        [20000919] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000919, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250515, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024353536'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 5.5, ['ID'] = 20000919, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250516, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024353792'),['Voice'] = '', }, 
        },
        [20000920] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000920, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250517, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024354048'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000920, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250517, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024354304'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 4, ['ID'] = 20000920, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250517, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024354560'),['Voice'] = '', }, 
        },
        [20000921] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250518, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024354816'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250519, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024355072'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 5, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250520, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024355328'),['Voice'] = '', }, {['Delay'] = 20, ['Duration'] = 5, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250521, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024355584'),['Voice'] = '', }, 
        },
        [20000922] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250491, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024355840'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250493, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024356096'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250495, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024356352'),['Voice'] = '', }, {['Delay'] = 10.5, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250494, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024356608'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250491, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024356864'),['Voice'] = '', }, 
        },
        [20000923] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000923, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250490, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024357120'),['Voice'] = '', }, 
        },
        [20000924] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000924, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250486, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024357376'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000924, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250486, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024357632'),['Voice'] = '', }, {['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000924, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250486, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024357888'),['Voice'] = '', }, 
        },
        [20000925] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000925, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250487, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024358144'),['Voice'] = '', }, {['Delay'] = 8.5, ['Duration'] = 3, ['ID'] = 20000925, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250487, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024358400'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 3, ['ID'] = 20000925, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250487, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024358656'),['Voice'] = '', }, 
        },
        [20000926] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000926, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250488, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024358912'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 3, ['ID'] = 20000926, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250488, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024359168'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000926, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250488, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024359424'),['Voice'] = '', }, 
        },
        [20000927] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000927, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250489, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024359680'),['Voice'] = '', }, 
        },
        [20000928] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000928, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242091, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024359936'),['Voice'] = '', }, 
        },
        [20000929] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000929, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212086, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024360192'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000929, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212086, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024360448'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000929, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242236, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024360704'),['Voice'] = '', }, 
        },
        [20000930] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000930, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250526, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024360960'),['Voice'] = '', }, 
        },
        [20000931] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000931, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250525, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024360960'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000931, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250524, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024361472'),['Voice'] = '', }, 
        },
        [20000932] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000932, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250543, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024361728'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000932, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250544, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024361984'),['Voice'] = '', }, 
        },
        [20000933] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000933, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211120, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024362240'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000933, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211120, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024362496'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000933, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211120, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024362752'),['Voice'] = '', }, 
        },
        [20000934] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000934, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231105, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024363008'),['Voice'] = '', }, 
        },
        [20000935] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000935, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242151, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024363264'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000935, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242151, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024363520'),['Voice'] = '', }, 
        },
        [20000936] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000936, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242155, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024363776'),['Voice'] = '', }, 
        },
        [20000937] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000937, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242180, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024364032'),['Voice'] = '', }, 
        },
        [20000938] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024364288'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024364544'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242160, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024364800'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024365056'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024365312'),['Voice'] = '', }, 
        },
        [20000939] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000939, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242144, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024365568'),['Voice'] = '', }, 
        },
        [20000940] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000940, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242142, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024365824'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000940, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242142, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024366080'),['Voice'] = '', }, 
        },
        [20000941] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000941, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024366336'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000941, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024366592'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000941, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024366848'),['Voice'] = '', }, 
        },
        [20000942] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000942, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250531, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024367104'),['Voice'] = '', }, 
        },
        [20000943] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000943, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250532, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024367360'),['Voice'] = '', }, 
        },
        [20000944] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000944, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250533, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024367616'),['Voice'] = '', }, 
        },
        [20000945] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000945, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250536, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024367872'),['Voice'] = '', }, 
        },
        [20000946] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000946, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250537, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024368128'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000946, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250537, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024368384'),['Voice'] = '', }, 
        },
        [20000947] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000947, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250538, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024368640'),['Voice'] = '', }, 
        },
        [20000948] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000948, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250546, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024368896'),['Voice'] = '', }, 
        },
        [20000949] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000949, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250547, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024369152'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 3, ['ID'] = 20000949, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250547, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024369408'),['Voice'] = '', }, 
        },
        [20000950] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000950, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255129, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024369664'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 4, ['ID'] = 20000950, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255129, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024369920'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000950, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255129, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024370176'),['Voice'] = '', }, 
        },
        [20000951] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255133, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024370432'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 3, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255134, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024370688'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255133, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024370944'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 4, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255134, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024371200'),['Voice'] = '', }, 
        },
        [20000952] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255144, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024371456'),['Voice'] = '', }, {['Delay'] = 9, ['Duration'] = 4, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255145, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024371712'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 4, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255144, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024371968'),['Voice'] = '', }, {['Delay'] = 19, ['Duration'] = 3, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255145, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024372224'),['Voice'] = '', }, 
        },
        [20000953] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024372480'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024372736'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 4, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255139, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024372992'),['Voice'] = '', }, {['Delay'] = 17, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255139, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024373248'),['Voice'] = '', }, {['Delay'] = 21, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024373504'),['Voice'] = '', }, {['Delay'] = 25, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024373760'),['Voice'] = '', }, 
        },
        [20000954] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000954, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255141, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024374016'),['Voice'] = '', }, 
        },
        [20000955] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000955, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255130, ['Text'] = Game.TableDataManager:GetLangStr('str_39103529796096'),['Voice'] = '', }, 
        },
        [20000956] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000956, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255142, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024374528'),['Voice'] = '', }, 
        },
        [20000957] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000957, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250496, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024374784'),['Voice'] = '', }, 
        },
        [20000958] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000958, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250194, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024375040'),['Voice'] = '', }, 
        },
        [20000959] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000959, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255135, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024375296'),['Voice'] = '', }, 
        },
        [20000960] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000960, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210820, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024375552'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000960, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210820, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024375808'),['Voice'] = '', }, 
        },
        [20000961] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000961, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255132, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024376064'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 4, ['ID'] = 20000961, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255131, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024376320'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 4, ['ID'] = 20000961, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255132, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024376576'),['Voice'] = '', }, 
        },
        [20000962] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000962, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250508, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024376832'),['Voice'] = '', }, 
        },
        [20000963] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000963, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242184, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024377088'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000963, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242184, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024377344'),['Voice'] = '', }, 
        },
        [20000964] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000964, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250508, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024377600'),['Voice'] = '', }, 
        },
        [20000965] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000965, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242156, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024377856'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000965, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242156, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024378112'),['Voice'] = '', }, 
        },
        [20000966] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000966, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242101, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024378368'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000966, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242100, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024378624'),['Voice'] = '', }, 
        },
        [20000967] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000967, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265326, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024378880'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000967, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265326, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024379136'),['Voice'] = '', }, 
        },
        [20000968] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000968, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024379904'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000968, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024380160'),['Voice'] = '', }, 
        },
        [20000969] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000969, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242240, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024380416'),['Voice'] = '', }, 
        },
        [20000970] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000970, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242241, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024380672'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000970, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242242, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024380928'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000970, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242242, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024381184'),['Voice'] = '', }, 
        },
        [20000971] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000971, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242243, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024381440'),['Voice'] = '', }, 
        },
        [20000972] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000972, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242244, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024381696'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000972, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242244, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024381952'),['Voice'] = '', }, 
        },
        [20000973] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000973, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242247, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024382208'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000973, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242247, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024382464'),['Voice'] = '', }, 
        },
        [20000974] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242251, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024382720'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242252, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024382976'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242253, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024383232'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242248, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024383488'),['Voice'] = '', }, 
        },
        [20000975] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000975, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242250, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024383744'),['Voice'] = '', }, 
        },
        [20000976] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000976, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242239, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024379392'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 0, ['ID'] = 20000976, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242239, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024379648'),['Voice'] = '', }, 
        },
        [20000977] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000977, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242223, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024384000'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000977, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242223, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024384256'),['Voice'] = '', }, 
        },
        [20000978] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000978, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7227002, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024384512'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000978, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7227001, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024384768'),['Voice'] = '', }, 
        },
        [20000980] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024385024'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024385280'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024385536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024385792'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242016, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024386048'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024386304'),['Voice'] = '', }, 
        },
        [20000981] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000981, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250556, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024386560'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000981, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250556, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024386816'),['Voice'] = '', }, 
        },
        [20000982] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000982, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250558, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024387072'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000982, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250558, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024387328'),['Voice'] = '', }, 
        },
        [20000983] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000983, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242111, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024387584'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000983, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242111, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024387840'),['Voice'] = '', }, 
        },
        [20000984] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000984, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210825, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024388096'),['Voice'] = '', }, 
        },
        [20000985] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000985, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250572, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024388352'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000985, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250569, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024388608'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000985, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250577, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024388864'),['Voice'] = '', }, 
        },
        [20000986] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000986, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210825, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024389120'),['Voice'] = '', }, 
        },
        [20000987] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000987, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242179, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024389376'),['Voice'] = '', }, 
        },
        [20000988] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000988, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242148, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024389632'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000988, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242149, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024389888'),['Voice'] = '', }, 
        },
        [20000989] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000989, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242147, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024390144'),['Voice'] = '', }, 
        },
        [20000990] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000990, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242113, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024390400'),['Voice'] = '', }, 
        },
        [20000991] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000991, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242209, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024390656'),['Voice'] = '', }, 
        },
        [20000992] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000992, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242212, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024390912'),['Voice'] = '', }, 
        },
        [20000993] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000993, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242211, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024391168'),['Voice'] = '', }, 
        },
        [20000994] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000994, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242205, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024391424'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000994, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242211, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024391680'),['Voice'] = '', }, 
        },
        [20000995] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000995, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219012, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024391936'),['Voice'] = '', }, 
        },
        [20000996] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000996, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250597, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024392192'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000996, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250597, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024392448'),['Voice'] = '', }, 
        },
        [20000997] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000997, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250578, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024392704'),['Voice'] = '', }, 
        },
        [20000998] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000998, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250594, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024392960'),['Voice'] = '', }, 
        },
        [20000999] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000999, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242163, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024393216'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000999, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242162, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024393472'),['Voice'] = '', }, 
        },
        [20001000] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001000, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242164, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024393728'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20001000, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242164, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024393984'),['Voice'] = '', }, 
        },
        [20001001] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265019, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024394240'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024394496'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265019, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024394752'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024395008'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265020, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024395264'),['Voice'] = '', }, 
        },
        [20001002] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001002, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024395520'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001002, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265003, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024395776'),['Voice'] = '', }, 
        },
        [20001003] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242224, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024396032'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242225, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024396288'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242226, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024396544'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242227, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024396800'),['Voice'] = '', }, 
        },
        [20001004] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024397056'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024397312'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024397568'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_56832081032704'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024398080'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024398336'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024398592'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024398848'),['Voice'] = '', }, 
        },
        [20001005] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20001005, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242260, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024399104'),['Voice'] = '', }, 
        },
        [20001006] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001006, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250641, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024399360'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001006, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250642, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024399616'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001006, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250642, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024399872'),['Voice'] = '', }, 
        },
        [20001007] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001007, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250658, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024400128'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001007, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250657, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024400384'),['Voice'] = '', }, 
        },
        [20001008] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001008, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250669, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024400640'),['Voice'] = '', }, 
        },
        [20001009] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001009, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250671, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024400896'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001009, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250672, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024401152'),['Voice'] = '', }, 
        },
        [20001010] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001010, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250673, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024401408'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001010, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250673, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024401664'),['Voice'] = '', }, 
        },
        [20001011] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001011, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250674, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024401920'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001011, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250675, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024402176'),['Voice'] = '', }, 
        },
        [20001012] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001012, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250676, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024402432'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001012, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250676, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024402688'),['Voice'] = '', }, 
        },
        [20001013] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001013, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250677, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024402944'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001013, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250678, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024403200'),['Voice'] = '', }, 
        },
        [20001014] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001014, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250679, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024403456'),['Voice'] = '', }, 
        },
        [20001015] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001015, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250680, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024403712'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001015, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250680, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024403968'),['Voice'] = '', }, 
        },
        [20001016] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001016, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250681, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024404224'),['Voice'] = '', }, 
        },
        [20001017] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001017, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250682, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024404480'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001017, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250682, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024404736'),['Voice'] = '', }, 
        },
        [20001018] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001018, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250683, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024404992'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001018, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250684, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024405248'),['Voice'] = '', }, 
        },
        [20001019] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001019, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250685, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024405504'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001019, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250686, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024405760'),['Voice'] = '', }, 
        },
        [20001020] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001020, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250687, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024406016'),['Voice'] = '', }, 
        },
        [20001021] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001021, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250688, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024406272'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001021, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250689, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024406528'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001021, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250688, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024406784'),['Voice'] = '', }, 
        },
        [20001022] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001022, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250691, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024407040'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001022, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250691, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024407296'),['Voice'] = '', }, 
        },
        [20001023] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001023, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250690, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024407552'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001023, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250690, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024407808'),['Voice'] = '', }, 
        },
        [20001024] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001024, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250692, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024408064'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001024, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250692, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024408320'),['Voice'] = '', }, 
        },
        [20001025] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001025, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250693, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024408576'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001025, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250694, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024408832'),['Voice'] = '', }, 
        },
        [20001026] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001026, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250695, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024409088'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001026, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250696, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024409344'),['Voice'] = '', }, 
        },
        [20001027] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001027, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250697, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024409600'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001027, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250698, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024409856'),['Voice'] = '', }, 
        },
        [20001028] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001028, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250699, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024410112'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001028, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250699, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024410368'),['Voice'] = '', }, 
        },
        [20001029] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001029, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250701, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024410624'),['Voice'] = '', }, 
        },
        [20001030] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001030, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250702, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024410880'),['Voice'] = '', }, 
        },
        [20001031] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001031, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250705, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024411136'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001031, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250705, ['Text'] = Game.TableDataManager:GetLangStr('str_26734024411392'),['Voice'] = '', }, 
        },
    }
}
return TopData