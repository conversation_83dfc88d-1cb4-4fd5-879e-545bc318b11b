--
-- 表名: GossipData后处理
--

local TopData = {
    data = {
        [20000000] = {{['Conditions'] = {{['TaskID'] = 99000901, ['kind'] = 'IsQuestActivated', }, }, ['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901842432'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 2, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901842688'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 3, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901842944'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000000, ['Kind'] = 0, ['Order'] = 4, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901843200'),['Voice'] = '', }, 
        },
        [20000001] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000001, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901843456'),['Voice'] = '', }, 
        },
        [20000002] = {{['Conditions'] = {{['kind'] = 'AllRandom', ['maxValue'] = 100, ['value'] = 80, }, }, ['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000002, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901843712'),['Voice'] = '', }, {['Conditions'] = {{['kind'] = 'AllRandom', ['maxValue'] = 20, ['value'] = 20, }, }, ['Delay'] = 10, ['Duration'] = 5, ['ID'] = 20000002, ['Kind'] = 0, ['Order'] = 2, ['TalkerID'] = 7240016, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901843968'),['Voice'] = '', }, 
        },
        [20000003] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000003, ['Kind'] = 0, ['Order'] = 1, ['TalkerID'] = 7240017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901844224'),['Voice'] = '', }, 
        },
        [20000004] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000004, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7215069, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901844480'),['Voice'] = '', }, 
        },
        [20000010] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211141, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901844736'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211142, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901844992'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211141, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901845248'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211142, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901845504'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211141, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901845760'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000010, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211143, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901846016'),['Voice'] = '', }, 
        },
        [20000011] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211149, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901846272'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211148, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901846528'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211149, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901846784'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211148, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901847040'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000011, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211149, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901847296'),['Voice'] = '', }, 
        },
        [20000012] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211150, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901847552'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211151, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901847808'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211150, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901848064'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211151, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901848320'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000012, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211150, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901848576'),['Voice'] = '', }, 
        },
        [20000013] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000013, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211153, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901848832'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000013, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211153, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901849088'),['Voice'] = '', }, 
        },
        [20000014] = {{['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000014, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211154, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901849344'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000014, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211154, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901849600'),['Voice'] = '', }, 
        },
        [20000015] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211161, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901849856'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901850112'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211162, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901850368'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901850624'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211161, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901850880'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901851136'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7211161, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901851392'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000015, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7211160, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901851648'),['Voice'] = '', }, 
        },
        [20000016] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211163, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901851904'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211164, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901852160'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211163, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901852416'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211165, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901852672'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211166, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901852928'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000016, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211163, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901853184'),['Voice'] = '', }, 
        },
        [20000017] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901853440'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901853696'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901853952'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000017, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901854208'),['Voice'] = '', }, 
        },
        [20000018] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000018, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901854464'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000018, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901854720'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000018, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901854976'),['Voice'] = '', }, 
        },
        [20000019] = {{['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211171, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901855232'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211129, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901855488'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211170, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901855744'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211169, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901856000'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211171, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901856256'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000019, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7211129, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901856512'),['Voice'] = '', }, 
        },
        [20000020] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000020, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325278465'),['Voice'] = '', }, 
        },
        [20000021] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000021, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325278465'),['Voice'] = '', }, 
        },
        [20000022] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000022, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325278977'),['Voice'] = '', }, 
        },
        [20000023] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000023, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325279745'),['Voice'] = '', }, 
        },
        [20000024] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000024, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325280001'),['Voice'] = '', }, 
        },
        [20000025] = {{['Delay'] = 3, ['Duration'] = 10, ['ID'] = 20000025, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240107, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325278977'),['Voice'] = '', }, 
        },
        [20000026] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000026, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7240003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325286657'),['Voice'] = '', }, 
        },
        [20000027] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000027, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250091, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325288193'),['Voice'] = '', }, 
        },
        [20000028] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000028, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250092, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325288449'),['Voice'] = '', }, 
        },
        [20000029] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000029, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325288705'),['Voice'] = '', }, 
        },
        [20000030] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000030, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205004, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901859328'),['Voice'] = '', }, 
        },
        [20000031] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000031, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205036, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325289217'),['Voice'] = '', }, 
        },
        [20000032] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000032, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205037, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901859840'),['Voice'] = '', }, 
        },
        [20000033] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000033, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7205038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325289729'),['Voice'] = '', }, 
        },
        [20000034] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000034, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901860352'),['Voice'] = '', }, 
        },
        [20000035] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000035, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325291265'),['Voice'] = '', }, 
        },
        [20000036] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000036, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203047, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325291521'),['Voice'] = '', }, 
        },
        [20000037] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000037, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325291777'),['Voice'] = '', }, 
        },
        [20000038] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000038, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325292033'),['Voice'] = '', }, 
        },
        [20000039] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000039, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325292289'),['Voice'] = '', }, 
        },
        [20000040] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000040, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203051, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325292545'),['Voice'] = '', }, 
        },
        [20000041] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000041, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325292801'),['Voice'] = '', }, 
        },
        [20000042] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000042, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203030, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901862400'),['Voice'] = '', }, 
        },
        [20000043] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000043, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203068, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325294081'),['Voice'] = '', }, 
        },
        [20000044] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000044, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203069, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325294337'),['Voice'] = '', }, 
        },
        [20000045] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000045, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325294593'),['Voice'] = '', }, 
        },
        [20000046] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000046, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203072, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901863424'),['Voice'] = '', }, 
        },
        [20000047] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000047, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203074, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901863680'),['Voice'] = '', }, 
        },
        [20000048] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000048, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203031, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901863936'),['Voice'] = '', }, 
        },
        [20000049] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000049, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203074, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901864192'),['Voice'] = '', }, 
        },
        [20000050] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000050, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325295873'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000050, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7203075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325296129'),['Voice'] = '', }, 
        },
        [20000051] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000051, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203076, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325296385'),['Voice'] = '', }, 
        },
        [20000052] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000052, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325296641'),['Voice'] = '', }, 
        },
        [20000053] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000053, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203079, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325296897'),['Voice'] = '', }, 
        },
        [20000054] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000054, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325297153'),['Voice'] = '', }, 
        },
        [20000055] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203080, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325297409'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7203081, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325297665'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7203082, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325297921'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000055, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7203083, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325298177'),['Voice'] = '', }, 
        },
        [20000056] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000056, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325298689'),['Voice'] = '', }, 
        },
        [20000057] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000057, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325298945'),['Voice'] = '', }, 
        },
        [20000058] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000058, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325299201'),['Voice'] = '', }, 
        },
        [20000059] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000059, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203091, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325299457'),['Voice'] = '', }, 
        },
        [20000060] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000060, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203092, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325299713'),['Voice'] = '', }, 
        },
        [20000061] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000061, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325299969'),['Voice'] = '', }, 
        },
        [20000062] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000062, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203094, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325300225'),['Voice'] = '', }, 
        },
        [20000063] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000063, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203099, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325300481'),['Voice'] = '', }, 
        },
        [20000064] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000064, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203102, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325300737'),['Voice'] = '', }, 
        },
        [20000065] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000065, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203105, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325300993'),['Voice'] = '', }, 
        },
        [20000066] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000066, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203101, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325301249'),['Voice'] = '', }, 
        },
        [20000067] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000067, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7203100, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325301505'),['Voice'] = '', }, 
        },
        [20000068] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000068, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325302529'),['Voice'] = '', }, 
        },
        [20000069] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000069, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325302785'),['Voice'] = '', }, 
        },
        [20000070] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000070, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211006, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901870592'),['Voice'] = '', }, 
        },
        [20000071] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000071, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211008, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325303297'),['Voice'] = '', }, 
        },
        [20000072] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000072, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325303553'),['Voice'] = '', }, 
        },
        [20000073] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000073, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325303809'),['Voice'] = '', }, 
        },
        [20000074] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000074, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325304065'),['Voice'] = '', }, 
        },
        [20000075] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000075, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325304321'),['Voice'] = '', }, 
        },
        [20000076] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000076, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325304577'),['Voice'] = '', }, 
        },
        [20000077] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000077, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325304833'),['Voice'] = '', }, 
        },
        [20000078] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000078, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325305089'),['Voice'] = '', }, 
        },
        [20000079] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000079, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325305345'),['Voice'] = '', }, 
        },
        [20000080] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000080, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325305601'),['Voice'] = '', }, 
        },
        [20000081] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000081, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325306113'),['Voice'] = '', }, 
        },
        [20000082] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000082, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325306369'),['Voice'] = '', }, 
        },
        [20000083] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000083, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325306625'),['Voice'] = '', }, 
        },
        [20000084] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000084, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211028, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325306881'),['Voice'] = '', }, 
        },
        [20000085] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000085, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211027, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325307137'),['Voice'] = '', }, 
        },
        [20000086] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000086, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211097, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901874688'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000086, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211097, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325307649'),['Voice'] = '', }, 
        },
        [20000087] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000087, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211098, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901875200'),['Voice'] = '', }, 
        },
        [20000088] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000088, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211099, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325308417'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000088, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211099, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325308673'),['Voice'] = '', }, 
        },
        [20000089] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000089, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211100, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325308929'),['Voice'] = '', }, {['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000089, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211100, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325309185'),['Voice'] = '', }, {['Delay'] = 5.555, ['Duration'] = 5, ['ID'] = 20000089, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211101, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901876480'),['Voice'] = '', }, 
        },
        [20000090] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211103, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325310209'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211107, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325310465'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211103, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325310721'),['Voice'] = '', }, {['Delay'] = 6.5, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211107, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325310977'),['Voice'] = '', }, {['Delay'] = 8.6, ['Duration'] = 5, ['ID'] = 20000090, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211103, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325311233'),['Voice'] = '', }, 
        },
        [20000091] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000091, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211104, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325311489'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000091, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211104, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325311745'),['Voice'] = '', }, 
        },
        [20000092] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000092, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211105, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325312001'),['Voice'] = '', }, 
        },
        [20000093] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000093, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211106, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325312257'),['Voice'] = '', }, 
        },
        [20000094] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000094, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211108, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901879040'),['Voice'] = '', }, 
        },
        [20000095] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901879296'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901879552'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901879808'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000095, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211109, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901880064'),['Voice'] = '', }, 
        },
        [20000096] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000096, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211110, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325313793'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000096, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211110, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901880576'),['Voice'] = '', }, 
        },
        [20000097] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000097, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211111, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325314049'),['Voice'] = '', }, 
        },
        [20000098] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000098, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211112, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325314305'),['Voice'] = '', }, 
        },
        [20000099] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000099, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211113, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325314561'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000099, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211113, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325314817'),['Voice'] = '', }, 
        },
        [20000100] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000100, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211114, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325315073'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000100, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211114, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325315329'),['Voice'] = '', }, 
        },
        [20000101] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000101, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211115, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325315585'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000101, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211115, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325315841'),['Voice'] = '', }, 
        },
        [20000102] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000102, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211097, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325316097'),['Voice'] = '', }, 
        },
        [20000103] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000103, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211105, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325316353'),['Voice'] = '', }, 
        },
        [20000104] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000104, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325316609'),['Voice'] = '', }, 
        },
        [20000105] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000105, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211118, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325316865'),['Voice'] = '', }, 
        },
        [20000106] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000106, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211119, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325317121'),['Voice'] = '', }, 
        },
        [20000107] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000107, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211051, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901884160'),['Voice'] = '', }, 
        },
        [20000108] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000108, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211051, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325318145'),['Voice'] = '', }, 
        },
        [20000109] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000109, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325318401'),['Voice'] = '', }, 
        },
        [20000110] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000110, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325318657'),['Voice'] = '', }, 
        },
        [20000111] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000111, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325318913'),['Voice'] = '', }, 
        },
        [20000112] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000112, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325319169'),['Voice'] = '', }, 
        },
        [20000113] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000113, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211036, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325319425'),['Voice'] = '', }, 
        },
        [20000114] = {{['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000114, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211036, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325319681'),['Voice'] = '', }, 
        },
        [20000115] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000115, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325319937'),['Voice'] = '', }, 
        },
        [20000116] = {{['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000116, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325320193'),['Voice'] = '', }, 
        },
        [20000117] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211125, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325320449'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211126, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901886976'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211126, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901887232'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7211125, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901887488'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 5, ['ID'] = 20000117, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7211125, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901887744'),['Voice'] = '', }, 
        },
        [20000122] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000122, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325321729'),['Voice'] = '', }, 
        },
        [20000123] = {{['Delay'] = 2.5, ['Duration'] = 2, ['ID'] = 20000123, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325321985'),['Voice'] = '', }, 
        },
        [20000124] = {{['Delay'] = 4.5, ['Duration'] = 2, ['ID'] = 20000124, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325322241'),['Voice'] = '', }, 
        },
        [20000125] = {{['Delay'] = 6.5, ['Duration'] = 2, ['ID'] = 20000125, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325322497'),['Voice'] = '', }, 
        },
        [20000126] = {{['Delay'] = 8.5, ['Duration'] = 3, ['ID'] = 20000126, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325322753'),['Voice'] = '', }, 
        },
        [20000127] = {{['Delay'] = 11.5, ['Duration'] = 3, ['ID'] = 20000127, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325323009'),['Voice'] = '', }, 
        },
        [20000128] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000128, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211129, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325323265'),['Voice'] = '', }, 
        },
        [20000129] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000129, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211130, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325323521'),['Voice'] = '', }, 
        },
        [20000130] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000130, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211131, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325323777'),['Voice'] = '', }, 
        },
        [20000131] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000131, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211132, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901890304'),['Voice'] = '', }, 
        },
        [20000132] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000132, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211133, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901890560'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000132, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211133, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901890816'),['Voice'] = '', }, 
        },
        [20000133] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000133, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211134, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325324545'),['Voice'] = '', }, 
        },
        [20000134] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000134, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211135, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325324801'),['Voice'] = '', }, 
        },
        [20000135] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000135, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211136, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901892352'),['Voice'] = '', }, 
        },
        [20000136] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000136, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211137, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901891584'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000136, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211137, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901891840'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000136, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211137, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901892096'),['Voice'] = '', }, 
        },
        [20000137] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000137, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211138, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325325569'),['Voice'] = '', }, 
        },
        [20000138] = {{['Delay'] = 1.5, ['Duration'] = 5, ['ID'] = 20000138, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211138, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325325825'),['Voice'] = '', }, 
        },
        [20000139] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000139, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7216018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325326337'),['Voice'] = '', }, 
        },
        [20000140] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000140, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211002, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901893376'),['Voice'] = '', }, 
        },
        [20000141] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000141, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901893632'),['Voice'] = '', }, 
        },
        [20000142] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000142, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325328385'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000142, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325328641'),['Voice'] = '', }, 
        },
        [20000143] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000143, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211037, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901894400'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000143, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211037, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901894656'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000143, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211037, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901894912'),['Voice'] = '', }, 
        },
        [20000144] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000144, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211038, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901895168'),['Voice'] = '', }, 
        },
        [20000145] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000145, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325330433'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000145, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325330689'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000145, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325330945'),['Voice'] = '', }, 
        },
        [20000146] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000146, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325331201'),['Voice'] = '', }, 
        },
        [20000147] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000147, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325331457'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000147, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325331713'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000147, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325331969'),['Voice'] = '', }, 
        },
        [20000148] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000148, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211042, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325332225'),['Voice'] = '', }, 
        },
        [20000149] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000149, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211043, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325332481'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000149, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211043, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325332737'),['Voice'] = '', }, 
        },
        [20000150] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000150, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211044, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325332993'),['Voice'] = '', }, 
        },
        [20000151] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000151, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325333249'),['Voice'] = '', }, 
        },
        [20000152] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000152, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325333505'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000152, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325333761'),['Voice'] = '', }, 
        },
        [20000153] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000153, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211047, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325334017'),['Voice'] = '', }, 
        },
        [20000154] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000154, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325334273'),['Voice'] = '', }, 
        },
        [20000155] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000155, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325334529'),['Voice'] = '', }, 
        },
        [20000156] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000156, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325334785'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000156, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325335041'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000156, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325335297'),['Voice'] = '', }, 
        },
        [20000157] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000157, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211052, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901900544'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000157, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325336321'),['Voice'] = '', }, 
        },
        [20000158] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000158, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211053, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325336577'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000158, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211053, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901901312'),['Voice'] = '', }, 
        },
        [20000159] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000159, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325337089'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000159, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325337345'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000159, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325331201'),['Voice'] = '', }, 
        },
        [20000160] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000160, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901902336'),['Voice'] = '', }, 
        },
        [20000161] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000161, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325338113'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000161, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325338369'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000161, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325338625'),['Voice'] = '', }, 
        },
        [20000162] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000162, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211057, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325338881'),['Voice'] = '', }, 
        },
        [20000163] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000163, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211058, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325339137'),['Voice'] = '', }, 
        },
        [20000164] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000164, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325339393'),['Voice'] = '', }, 
        },
        [20000165] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000165, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211060, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901904128'),['Voice'] = '', }, 
        },
        [20000166] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000166, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211061, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325340161'),['Voice'] = '', }, 
        },
        [20000167] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000167, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211062, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325340417'),['Voice'] = '', }, 
        },
        [20000168] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000168, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325340673'),['Voice'] = '', }, 
        },
        [20000169] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000169, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325340929'),['Voice'] = '', }, 
        },
        [20000170] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000170, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325341185'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000170, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325341697'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000170, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325341441'),['Voice'] = '', }, 
        },
        [20000171] = {{['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000171, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325341697'),['Voice'] = '', }, 
        },
        [20000172] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000172, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207006, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901906432'),['Voice'] = '', }, 
        },
        [20000173] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000173, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325342209'),['Voice'] = '', }, 
        },
        [20000174] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000174, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207008, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325342465'),['Voice'] = '', }, 
        },
        [20000175] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000175, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325342721'),['Voice'] = '', }, 
        },
        [20000176] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000176, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207114, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325342977'),['Voice'] = '', }, 
        },
        [20000177] = {{['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000177, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325343233'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000177, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325343489'),['Voice'] = '', }, 
        },
        [20000178] = {{['Delay'] = 8, ['Duration'] = 5, ['ID'] = 20000178, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207012, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325343745'),['Voice'] = '', }, 
        },
        [20000179] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000179, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325344001'),['Voice'] = '', }, 
        },
        [20000180] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000180, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 0, ['Text'] = '', ['Voice'] = '', }, 
        },
        [20000181] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000181, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325344513'),['Voice'] = '', }, 
        },
        [20000182] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000182, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325344769'),['Voice'] = '', }, 
        },
        [20000183] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000183, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325345025'),['Voice'] = '', }, 
        },
        [20000184] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000184, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325345281'),['Voice'] = '', }, 
        },
        [20000185] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000185, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207115, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325345537'),['Voice'] = '', }, 
        },
        [20000186] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000186, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325345793'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000186, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325346049'),['Voice'] = '', }, 
        },
        [20000187] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000187, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325347073'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000187, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325347329'),['Voice'] = '', }, 
        },
        [20000188] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000188, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325347585'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000188, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325347841'),['Voice'] = '', }, 
        },
        [20000189] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000189, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325347585'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000189, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325347841'),['Voice'] = '', }, 
        },
        [20000190] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000190, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207027, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325348609'),['Voice'] = '', }, 
        },
        [20000191] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000191, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207028, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325348865'),['Voice'] = '', }, 
        },
        [20000192] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000192, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207031, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901912832'),['Voice'] = '', }, 
        },
        [20000193] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000193, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325349889'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000193, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325350145'),['Voice'] = '', }, 
        },
        [20000194] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000194, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325350401'),['Voice'] = '', }, 
        },
        [20000195] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000195, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325350657'),['Voice'] = '', }, 
        },
        [20000196] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000196, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325350913'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000196, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325351169'),['Voice'] = '', }, 
        },
        [20000197] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000197, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207036, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325351425'),['Voice'] = '', }, 
        },
        [20000198] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000198, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207037, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325351681'),['Voice'] = '', }, 
        },
        [20000199] = {{['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000199, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325353217'),['Voice'] = '', }, 
        },
        [20000200] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000200, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207042, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901915392'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000200, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207042, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901915648'),['Voice'] = '', }, 
        },
        [20000201] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000201, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325354241'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000201, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325354497'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 2, ['ID'] = 20000201, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325354753'),['Voice'] = '', }, 
        },
        [20000202] = {{['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000202, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325355009'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 2, ['ID'] = 20000202, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325355265'),['Voice'] = '', }, 
        },
        [20000203] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000203, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207047, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325355521'),['Voice'] = '', }, {['Delay'] = 8.5, ['Duration'] = 2, ['ID'] = 20000203, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207047, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325355777'),['Voice'] = '', }, 
        },
        [20000204] = {{['Delay'] = 2.5, ['Duration'] = 2, ['ID'] = 20000204, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325356033'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000204, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325356289'),['Voice'] = '', }, 
        },
        [20000205] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000205, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325356545'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000205, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901918464'),['Voice'] = '', }, 
        },
        [20000206] = {{['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000206, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325357057'),['Voice'] = '', }, 
        },
        [20000207] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000207, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325357313'),['Voice'] = '', }, 
        },
        [20000208] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000208, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207064, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325357569'),['Voice'] = '', }, 
        },
        [20000209] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000209, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207069, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325357825'),['Voice'] = '', }, 
        },
        [20000210] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000210, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207071, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901919744'),['Voice'] = '', }, 
        },
        [20000211] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000211, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325358593'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000211, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325358849'),['Voice'] = '', }, 
        },
        [20000212] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000212, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207076, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325359105'),['Voice'] = '', }, 
        },
        [20000213] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207078, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325360129'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207079, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325360385'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207080, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325360641'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7207081, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325360897'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7207082, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325361153'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000213, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7207083, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325361409'),['Voice'] = '', }, 
        },
        [20000214] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000214, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325361665'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000214, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207085, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325361921'),['Voice'] = '', }, 
        },
        [20000215] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325362177'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325362433'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207086, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325362689'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7207087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325362945'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000215, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7207088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325363201'),['Voice'] = '', }, 
        },
        [20000216] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000216, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325364225'),['Voice'] = '', }, 
        },
        [20000217] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000217, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207100, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325364993'),['Voice'] = '', }, 
        },
        [20000218] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000218, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207106, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325366017'),['Voice'] = '', }, 
        },
        [20000219] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000219, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207112, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325366273'),['Voice'] = '', }, 
        },
        [20000220] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000220, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207110, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325366529'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000220, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207110, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325366785'),['Voice'] = '', }, 
        },
        [20000221] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000221, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207117, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901925632'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000221, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207117, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901925888'),['Voice'] = '', }, 
        },
        [20000222] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000222, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207111, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325367553'),['Voice'] = '', }, 
        },
        [20000223] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000223, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207118, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325367809'),['Voice'] = '', }, 
        },
        [20000224] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000224, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207119, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325368065'),['Voice'] = '', }, 
        },
        [20000225] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000225, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7217007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325371137'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 1.5, ['ID'] = 20000225, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7217008, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325371393'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 1.5, ['ID'] = 20000225, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7217009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325371649'),['Voice'] = '', }, 
        },
        [20000226] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000226, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7217006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325372673'),['Voice'] = '', }, 
        },
        [20000227] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000227, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7214001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325373953'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000227, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7214001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325374209'),['Voice'] = '', }, 
        },
        [20000228] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901928448'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325380865'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901928960'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325381377'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901929472'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901929728'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901929984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7210830, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325382401'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000228, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7210829, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901930496'),['Voice'] = '', }, 
        },
        [20000229] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000229, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210854, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325382913'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000229, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210854, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325383169'),['Voice'] = '', }, 
        },
        [20000230] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000230, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210831, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325383425'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000230, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210831, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325383681'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000230, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210832, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901931776'),['Voice'] = '', }, 
        },
        [20000231] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000231, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210833, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325384193'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000231, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210833, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325384449'),['Voice'] = '', }, 
        },
        [20000232] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000232, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210834, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325384705'),['Voice'] = '', }, 
        },
        [20000233] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000233, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210835, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901932800'),['Voice'] = '', }, 
        },
        [20000234] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000234, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210836, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325385729'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000234, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210837, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325385729'),['Voice'] = '', }, 
        },
        [20000235] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000235, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210838, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325388289'),['Voice'] = '', }, 
        },
        [20000236] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000236, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210839, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325388545'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000236, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210840, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325388801'),['Voice'] = '', }, 
        },
        [20000237] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000237, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210842, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325389057'),['Voice'] = '', }, 
        },
        [20000238] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325389313'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210844, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325389569'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325389825'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210844, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325390081'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325390337'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210844, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325390593'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000238, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7210843, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325390849'),['Voice'] = '', }, 
        },
        [20000239] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210845, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325391105'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210846, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325391361'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210845, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325391617'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000239, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210846, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901937152'),['Voice'] = '', }, 
        },
        [20000240] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210824, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325392129'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210848, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325392385'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210824, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325392641'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210848, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325392897'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210824, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325393153'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000240, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210848, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325393409'),['Voice'] = '', }, 
        },
        [20000241] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210849, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901938944'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210850, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325393921'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7210849, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325394177'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7210850, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325394433'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7210849, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325394689'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000241, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7210851, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901940224'),['Voice'] = '', }, 
        },
        [20000242] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000242, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210852, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325394945'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000242, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210853, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325395201'),['Voice'] = '', }, 
        },
        [20000243] = {{['Delay'] = 0, ['Duration'] = 3.5, ['ID'] = 20000243, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325407745'),['Voice'] = '', }, 
        },
        [20000244] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000244, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325408257'),['Voice'] = '', }, 
        },
        [20000245] = {{['Delay'] = 5, ['Duration'] = 2.5, ['ID'] = 20000245, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325408513'),['Voice'] = '', }, 
        },
        [20000246] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000246, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215010, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325408769'),['Voice'] = '', }, 
        },
        [20000247] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000247, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325409025'),['Voice'] = '', }, 
        },
        [20000248] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000248, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325409281'),['Voice'] = '', }, 
        },
        [20000249] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000249, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325398529'),['Voice'] = '', }, 
        },
        [20000250] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000250, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325400833'),['Voice'] = '', }, 
        },
        [20000251] = {{['Delay'] = 0, ['Duration'] = 3.5, ['ID'] = 20000251, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7215074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325401089'),['Voice'] = '', }, 
        },
        [20000252] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000252, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325412865'),['Voice'] = '', }, 
        },
        [20000253] = {{['Delay'] = 0.5, ['Duration'] = 30, ['ID'] = 20000253, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325413121'),['Voice'] = '', }, 
        },
        [20000254] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000254, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901943808'),['Voice'] = '', }, 
        },
        [20000255] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000255, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325413633'),['Voice'] = '', }, 
        },
        [20000256] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000256, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325413889'),['Voice'] = '', }, 
        },
        [20000257] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000257, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325414145'),['Voice'] = '', }, 
        },
        [20000258] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000258, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325414401'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000258, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325414657'),['Voice'] = '', }, 
        },
        [20000259] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000259, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325414913'),['Voice'] = '', }, 
        },
        [20000260] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000260, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325415425'),['Voice'] = '', }, 
        },
        [20000261] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000261, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219012, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325415681'),['Voice'] = '', }, 
        },
        [20000262] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000262, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325415937'),['Voice'] = '', }, 
        },
        [20000263] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000263, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325416193'),['Voice'] = '', }, 
        },
        [20000264] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000264, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325416449'),['Voice'] = '', }, 
        },
        [20000265] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000265, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219010, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325416705'),['Voice'] = '', }, 
        },
        [20000266] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000266, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325417217'),['Voice'] = '', }, 
        },
        [20000267] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000267, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325417473'),['Voice'] = '', }, 
        },
        [20000268] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000268, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325417729'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000268, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325417985'),['Voice'] = '', }, 
        },
        [20000269] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000269, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219031, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325418241'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000269, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325418497'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000269, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325418753'),['Voice'] = '', }, 
        },
        [20000270] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000270, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325419009'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000270, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325419265'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000270, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325419521'),['Voice'] = '', }, 
        },
        [20000271] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000271, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325419777'),['Voice'] = '', }, 
        },
        [20000272] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000272, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325420033'),['Voice'] = '', }, 
        },
        [20000273] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000273, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325420289'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000273, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325420545'),['Voice'] = '', }, 
        },
        [20000274] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000274, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325420801'),['Voice'] = '', }, 
        },
        [20000275] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000275, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325421057'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000275, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325421313'),['Voice'] = '', }, 
        },
        [20000276] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000276, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325421569'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000276, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325421825'),['Voice'] = '', }, 
        },
        [20000277] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000277, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219042, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325422081'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000277, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219043, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325422337'),['Voice'] = '', }, 
        },
        [20000278] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000278, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219044, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325422593'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000278, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219044, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325422849'),['Voice'] = '', }, 
        },
        [20000279] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000279, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325423105'),['Voice'] = '', }, 
        },
        [20000280] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000280, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325423361'),['Voice'] = '', }, 
        },
        [20000281] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000281, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219047, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325423617'),['Voice'] = '', }, 
        },
        [20000282] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000282, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325423873'),['Voice'] = '', }, 
        },
        [20000283] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000283, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325424129'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000283, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325424385'),['Voice'] = '', }, 
        },
        [20000284] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000284, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325424641'),['Voice'] = '', }, 
        },
        [20000285] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000285, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219053, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325424897'),['Voice'] = '', }, 
        },
        [20000286] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000286, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325425153'),['Voice'] = '', }, 
        },
        [20000287] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000287, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219055, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325425409'),['Voice'] = '', }, 
        },
        [20000288] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000288, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219057, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325425665'),['Voice'] = '', }, 
        },
        [20000289] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901955840'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223002, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901956096'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901956352'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000289, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901956608'),['Voice'] = '', }, 
        },
        [20000290] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000290, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325426945'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000290, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325427201'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000290, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325427457'),['Voice'] = '', }, 
        },
        [20000291] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000291, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223010, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325427713'),['Voice'] = '', }, 
        },
        [20000292] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000292, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325427969'),['Voice'] = '', }, 
        },
        [20000293] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000293, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325428481'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000293, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325428737'),['Voice'] = '', }, 
        },
        [20000294] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000294, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223020, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901958656'),['Voice'] = '', }, 
        },
        [20000295] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000295, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223012, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901958912'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000295, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250252, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901959168'),['Voice'] = '', }, 
        },
        [20000296] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000296, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223014, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901959424'),['Voice'] = '', }, 
        },
        [20000297] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000297, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223015, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901959680'),['Voice'] = '', }, 
        },
        [20000298] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000298, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325430529'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000298, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325430785'),['Voice'] = '', }, 
        },
        [20000299] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000299, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325432065'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000299, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325432321'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000299, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325432577'),['Voice'] = '', }, 
        },
        [20000300] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000300, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223034, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901961216'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000300, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223033, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901961472'),['Voice'] = '', }, 
        },
        [20000301] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000301, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223024, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901961728'),['Voice'] = '', }, 
        },
        [20000302] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000302, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325433857'),['Voice'] = '', }, 
        },
        [20000303] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000303, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223028, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901962240'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000303, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223029, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901962496'),['Voice'] = '', }, 
        },
        [20000304] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000304, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223027, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325434625'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000304, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901963008'),['Voice'] = '', }, 
        },
        [20000305] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000305, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223031, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325435137'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000305, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223030, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325435393'),['Voice'] = '', }, 
        },
        [20000306] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901963776'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223040, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901964032'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901964288'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000306, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325436417'),['Voice'] = '', }, 
        },
        [20000307] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000307, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223041, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901964800'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000307, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223042, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901965056'),['Voice'] = '', }, 
        },
        [20000308] = {{['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000308, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325437185'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000308, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223038, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901965568'),['Voice'] = '', }, 
        },
        [20000309] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000309, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325437697'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000309, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325437953'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000309, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325438209'),['Voice'] = '', }, 
        },
        [20000310] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000310, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223050, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956642816'),['Voice'] = '', }, 
        },
        [20000311] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000311, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223044, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325438721'),['Voice'] = '', }, 
        },
        [20000312] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325438977'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223053, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325439233'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325439489'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000312, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223053, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325439745'),['Voice'] = '', }, 
        },
        [20000313] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000313, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325440001'),['Voice'] = '', }, 
        },
        [20000314] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000314, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325440257'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000314, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223055, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325440513'),['Voice'] = '', }, 
        },
        [20000315] = {{['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325440769'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7201012, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325441025'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7201013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325441281'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000315, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7201013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325441537'),['Voice'] = '', }, 
        },
        [20000316] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7201014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325441793'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7201014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325442049'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7201014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325442305'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7201016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325442561'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7201017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325442817'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000316, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7201018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325443073'),['Voice'] = '', }, 
        },
        [20000317] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000317, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7225001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901971456'),['Voice'] = '', }, 
        },
        [20000318] = {{['Delay'] = 0.5, ['Duration'] = 6, ['ID'] = 20000318, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7225005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325448705'),['Voice'] = '', }, 
        },
        [20000319] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000319, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901971968'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000319, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325449473'),['Voice'] = '', }, 
        },
        [20000320] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000320, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325449729'),['Voice'] = '', }, 
        },
        [20000321] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000321, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901972736'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000321, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901972992'),['Voice'] = '', }, 
        },
        [20000322] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000322, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231006, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920472320'),['Voice'] = '', }, 
        },
        [20000323] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000323, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231008, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325451265'),['Voice'] = '', }, 
        },
        [20000324] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000324, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231010, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901974272'),['Voice'] = '', }, 
        },
        [20000325] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000325, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231011, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901974528'),['Voice'] = '', }, 
        },
        [20000326] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000326, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231012, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325452033'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000326, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325452289'),['Voice'] = '', }, 
        },
        [20000327] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000327, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231080, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325452545'),['Voice'] = '', }, 
        },
        [20000328] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000328, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325452801'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000328, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325453057'),['Voice'] = '', }, 
        },
        [20000329] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000329, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231080, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325453313'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000329, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231081, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325453569'),['Voice'] = '', }, 
        },
        [20000330] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000330, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325453825'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000330, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325454081'),['Voice'] = '', }, 
        },
        [20000331] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000331, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325454337'),['Voice'] = '', }, 
        },
        [20000332] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000332, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325454593'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000332, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325454849'),['Voice'] = '', }, 
        },
        [20000333] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000333, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325455105'),['Voice'] = '', }, 
        },
        [20000334] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000334, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231020, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901978112'),['Voice'] = '', }, 
        },
        [20000335] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000335, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325455617'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000335, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325455873'),['Voice'] = '', }, 
        },
        [20000336] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000336, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231080, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325456129'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000336, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231081, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325456385'),['Voice'] = '', }, 
        },
        [20000337] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000337, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325456641'),['Voice'] = '', }, 
        },
        [20000338] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000338, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901979648'),['Voice'] = '', }, 
        },
        [20000339] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000339, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231027, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325457153'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000339, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231027, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325457409'),['Voice'] = '', }, 
        },
        [20000340] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000340, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231028, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901980416'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000340, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231028, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901980672'),['Voice'] = '', }, 
        },
        [20000341] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000341, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231029, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325457921'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000341, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231029, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325458177'),['Voice'] = '', }, 
        },
        [20000342] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000342, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231031, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325458433'),['Voice'] = '', }, 
        },
        [20000343] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000343, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325458689'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000343, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325458945'),['Voice'] = '', }, 
        },
        [20000344] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000344, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325459201'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000344, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325459457'),['Voice'] = '', }, 
        },
        [20000345] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000345, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325459713'),['Voice'] = '', }, 
        },
        [20000346] = {{['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000346, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325459969'),['Voice'] = '', }, 
        },
        [20000347] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000347, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231036, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325460225'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000347, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231037, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325460481'),['Voice'] = '', }, 
        },
        [20000348] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000348, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231082, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956642816'),['Voice'] = '', }, 
        },
        [20000349] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000349, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325460993'),['Voice'] = '', }, 
        },
        [20000350] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000350, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325461249'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000350, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901984512'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000350, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7239017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325461761'),['Voice'] = '', }, 
        },
        [20000351] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000351, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325462017'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000351, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325462273'),['Voice'] = '', }, 
        },
        [20000352] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000352, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231133, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325462529'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000352, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231117, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901985792'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000352, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231117, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901986048'),['Voice'] = '', }, 
        },
        [20000353] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000353, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231043, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325462785'),['Voice'] = '', }, 
        },
        [20000354] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000354, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325463041'),['Voice'] = '', }, 
        },
        [20000355] = {{['Delay'] = 10, ['Duration'] = 5, ['ID'] = 20000355, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231047, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325463297'),['Voice'] = '', }, 
        },
        [20000356] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000356, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325463553'),['Voice'] = '', }, 
        },
        [20000357] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000357, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325463809'),['Voice'] = '', }, 
        },
        [20000358] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000358, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325464065'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000358, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325464321'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000358, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325464577'),['Voice'] = '', }, 
        },
        [20000359] = {{['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000359, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231051, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325464833'),['Voice'] = '', }, 
        },
        [20000360] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000360, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325465089'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000360, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325465345'),['Voice'] = '', }, 
        },
        [20000361] = {{['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000361, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231053, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325465601'),['Voice'] = '', }, 
        },
        [20000362] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000362, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325465857'),['Voice'] = '', }, 
        },
        [20000363] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000363, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231055, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325466113'),['Voice'] = '', }, 
        },
        [20000364] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000364, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231056, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325466369'),['Voice'] = '', }, 
        },
        [20000365] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000365, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231057, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325466625'),['Voice'] = '', }, 
        },
        [20000366] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000366, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231058, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325466881'),['Voice'] = '', }, 
        },
        [20000367] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000367, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325467137'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000367, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325467393'),['Voice'] = '', }, 
        },
        [20000368] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000368, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231060, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325467649'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000368, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231060, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325467905'),['Voice'] = '', }, 
        },
        [20000369] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000369, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231061, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325468161'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000369, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231062, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325468417'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000369, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231062, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325468673'),['Voice'] = '', }, 
        },
        [20000370] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000370, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231083, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325468929'),['Voice'] = '', }, 
        },
        [20000371] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000371, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325469185'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000371, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325469441'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000371, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325469697'),['Voice'] = '', }, 
        },
        [20000372] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000372, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231064, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325469953'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000372, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901993728'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000372, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231065, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901993984'),['Voice'] = '', }, 
        },
        [20000373] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000373, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325470977'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000373, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325471233'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000373, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325471489'),['Voice'] = '', }, 
        },
        [20000374] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000374, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325468929'),['Voice'] = '', }, 
        },
        [20000375] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231068, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901995264'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231068, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325472257'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231069, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325472513'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000375, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7231069, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325472769'),['Voice'] = '', }, 
        },
        [20000376] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000376, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231130, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325473025'),['Voice'] = '', }, 
        },
        [20000377] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000377, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325473281'),['Voice'] = '', }, 
        },
        [20000378] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000378, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325473537'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000378, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325473793'),['Voice'] = '', }, 
        },
        [20000379] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000379, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325474049'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000379, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325474305'),['Voice'] = '', }, 
        },
        [20000380] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000380, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325474561'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000380, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325474817'),['Voice'] = '', }, 
        },
        [20000381] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000381, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325475073'),['Voice'] = '', }, 
        },
        [20000382] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000382, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325475329'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000382, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325475585'),['Voice'] = '', }, 
        },
        [20000383] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000383, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231076, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325475841'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000383, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231076, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325476097'),['Voice'] = '', }, 
        },
        [20000384] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000384, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325476353'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000384, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325476609'),['Voice'] = '', }, 
        },
        [20000385] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000385, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231078, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325476865'),['Voice'] = '', }, 
        },
        [20000386] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000386, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231079, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325477121'),['Voice'] = '', }, 
        },
        [20000387] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000387, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231086, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902000640'),['Voice'] = '', }, 
        },
        [20000388] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000388, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325477633'),['Voice'] = '', }, 
        },
        [20000389] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000389, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325477889'),['Voice'] = '', }, 
        },
        [20000390] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000390, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231089, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325478145'),['Voice'] = '', }, 
        },
        [20000391] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000391, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325478401'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000391, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325478657'),['Voice'] = '', }, 
        },
        [20000392] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000392, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231091, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325478913'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000392, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231091, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325479169'),['Voice'] = '', }, 
        },
        [20000393] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000393, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231092, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325479425'),['Voice'] = '', }, 
        },
        [20000394] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000394, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325479681'),['Voice'] = '', }, 
        },
        [20000395] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000395, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231094, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325479937'),['Voice'] = '', }, 
        },
        [20000396] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000396, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231095, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325480193'),['Voice'] = '', }, 
        },
        [20000397] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000397, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231096, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325480449'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000397, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231096, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325480705'),['Voice'] = '', }, 
        },
        [20000398] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000398, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231097, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325480961'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000398, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231097, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325481217'),['Voice'] = '', }, 
        },
        [20000399] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000399, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231098, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325481473'),['Voice'] = '', }, 
        },
        [20000400] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000400, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231101, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325481729'),['Voice'] = '', }, 
        },
        [20000401] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000401, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231102, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325481985'),['Voice'] = '', }, {['Delay'] = 0.7, ['Duration'] = 5, ['ID'] = 20000401, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231102, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902005504'),['Voice'] = '', }, 
        },
        [20000402] = {{['Delay'] = 1.5, ['Duration'] = 3, ['ID'] = 20000402, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231103, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902005760'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000402, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231125, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902006016'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 0, ['ID'] = 20000402, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231103, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902006272'),['Voice'] = '', }, 
        },
        [20000403] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000403, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231104, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902006528'),['Voice'] = '', }, 
        },
        [20000404] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000404, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231105, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902006784'),['Voice'] = '', }, 
        },
        [20000405] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000405, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231106, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325483521'),['Voice'] = '', }, 
        },
        [20000406] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000406, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231107, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902007296'),['Voice'] = '', }, 
        },
        [20000407] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000407, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231132, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325484033'),['Voice'] = '', }, 
        },
        [20000408] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000408, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231111, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325484289'),['Voice'] = '', }, 
        },
        [20000409] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000409, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231112, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325484545'),['Voice'] = '', }, 
        },
        [20000410] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000410, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231113, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325484801'),['Voice'] = '', }, 
        },
        [20000411] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000411, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231114, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325485057'),['Voice'] = '', }, 
        },
        [20000412] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000412, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231115, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902008832'),['Voice'] = '', }, 
        },
        [20000413] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000413, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325485825'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000413, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325486081'),['Voice'] = '', }, 
        },
        [20000414] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000414, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325486337'),['Voice'] = '', }, 
        },
        [20000415] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000415, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231118, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325486593'),['Voice'] = '', }, 
        },
        [20000416] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000416, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231119, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902010112'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000416, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231119, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902010368'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000416, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7231119, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902010624'),['Voice'] = '', }, 
        },
        [20000417] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000417, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231120, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325487361'),['Voice'] = '', }, 
        },
        [20000418] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000418, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231121, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325487617'),['Voice'] = '', }, 
        },
        [20000419] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000419, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231122, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325487873'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000419, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231122, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325488129'),['Voice'] = '', }, 
        },
        [20000420] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000420, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231123, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325488385'),['Voice'] = '', }, 
        },
        [20000421] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000421, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231124, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902012160'),['Voice'] = '', }, 
        },
        [20000422] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000422, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231126, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325488897'),['Voice'] = '', }, 
        },
        [20000423] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000423, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231127, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325489153'),['Voice'] = '', }, 
        },
        [20000424] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000424, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231128, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325489409'),['Voice'] = '', }, 
        },
        [20000425] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000425, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231129, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902013184'),['Voice'] = '', }, 
        },
        [20000426] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325494785'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325495041'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325495297'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000426, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325495553'),['Voice'] = '', }, 
        },
        [20000427] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000427, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212008, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325496577'),['Voice'] = '', }, 
        },
        [20000428] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000428, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325496833'),['Voice'] = '', }, 
        },
        [20000429] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000429, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325497089'),['Voice'] = '', }, 
        },
        [20000430] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000430, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212012, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325504769'),['Voice'] = '', }, 
        },
        [20000431] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000431, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325505025'),['Voice'] = '', }, 
        },
        [20000432] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325505281'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325505537'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325505793'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325506049'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000432, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325506305'),['Voice'] = '', }, 
        },
        [20000433] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000433, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902017024'),['Voice'] = '', }, 
        },
        [20000434] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325509889'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325510145'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325510401'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325510657'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325510913'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325511169'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000434, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325511425'),['Voice'] = '', }, 
        },
        [20000435] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325511937'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325512193'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325512449'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000435, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325512705'),['Voice'] = '', }, 
        },
        [20000436] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000436, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325513473'),['Voice'] = '', }, 
        },
        [20000437] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000437, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325513729'),['Voice'] = '', }, 
        },
        [20000438] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000438, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325514497'),['Voice'] = '', }, 
        },
        [20000439] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000439, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212027, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325514753'),['Voice'] = '', }, 
        },
        [20000440] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212028, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325515009'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212028, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325515265'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212029, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325515521'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000440, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212029, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325515777'),['Voice'] = '', }, 
        },
        [20000441] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000441, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325518593'),['Voice'] = '', }, 
        },
        [20000442] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000442, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212037, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325519105'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000442, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212037, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325519361'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000442, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212037, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325519617'),['Voice'] = '', }, 
        },
        [20000443] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325519873'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325520129'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325520385'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325520641'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325520897'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325521153'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325521409'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000443, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325521665'),['Voice'] = '', }, 
        },
        [20000444] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325521921'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325522177'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325522433'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000444, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325522689'),['Voice'] = '', }, 
        },
        [20000445] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000445, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212042, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325522945'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000445, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212042, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325523201'),['Voice'] = '', }, 
        },
        [20000446] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000446, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212043, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902026752'),['Voice'] = '', }, 
        },
        [20000447] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000447, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212044, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902027008'),['Voice'] = '', }, 
        },
        [20000448] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000448, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325527041'),['Voice'] = '', }, 
        },
        [20000449] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000449, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325527553'),['Voice'] = '', }, 
        },
        [20000450] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000450, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212051, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325527809'),['Voice'] = '', }, 
        },
        [20000451] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000451, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325528065'),['Voice'] = '', }, 
        },
        [20000452] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000452, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212030, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325528321'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000452, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212031, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325528577'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000452, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325528833'),['Voice'] = '', }, 
        },
        [20000453] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000453, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212053, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325529089'),['Voice'] = '', }, 
        },
        [20000454] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000454, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325529345'),['Voice'] = '', }, 
        },
        [20000455] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000455, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212076, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325529601'),['Voice'] = '', }, 
        },
        [20000456] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000456, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212081, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902029824'),['Voice'] = '', }, 
        },
        [20000457] = {{['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000457, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325531137'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000457, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325531393'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000457, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325531649'),['Voice'] = '', }, 
        },
        [20000458] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325531905'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325532161'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325532417'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325532673'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325532929'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325533185'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212092, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325533441'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000458, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325533697'),['Voice'] = '', }, 
        },
        [20000459] = {{['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000459, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325534977'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000459, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325535233'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2.5, ['ID'] = 20000459, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325535489'),['Voice'] = '', }, 
        },
        [20000460] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325535745'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325536001'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325536257'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000460, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212071, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902034432'),['Voice'] = '', }, 
        },
        [20000461] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000461, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212078, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325536769'),['Voice'] = '', }, 
        },
        [20000462] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000462, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212101, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325316097'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000462, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212101, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902035200'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000462, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212101, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325537537'),['Voice'] = '', }, 
        },
        [20000463] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212097, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325537793'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212098, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325538049'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212099, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325538049'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000463, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212100, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325538561'),['Voice'] = '', }, 
        },
        [20000464] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000464, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325538817'),['Voice'] = '', }, 
        },
        [20000465] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000465, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212067, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325539073'),['Voice'] = '', }, 
        },
        [20000466] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000466, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325539329'),['Voice'] = '', }, 
        },
        [20000467] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000467, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902037504'),['Voice'] = '', }, 
        },
        [20000468] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000468, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212086, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325539841'),['Voice'] = '', }, 
        },
        [20000469] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000469, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212094, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325540097'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000469, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212094, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325540353'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000469, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212094, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325540609'),['Voice'] = '', }, 
        },
        [20000470] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000470, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325540865'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000470, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325541121'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000470, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325541377'),['Voice'] = '', }, 
        },
        [20000471] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000471, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325541633'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000471, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325541889'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3.5, ['ID'] = 20000471, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325542145'),['Voice'] = '', }, 
        },
        [20000472] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325542401'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325542657'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325542913'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000472, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325543169'),['Voice'] = '', }, 
        },
        [20000473] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000473, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212068, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325543425'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000473, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212068, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325543681'),['Voice'] = '', }, 
        },
        [20000474] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212110, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325543937'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212109, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325544193'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212109, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325544449'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212109, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325544705'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000474, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212110, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325544961'),['Voice'] = '', }, 
        },
        [20000475] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000475, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212112, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325545217'),['Voice'] = '', }, 
        },
        [20000476] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000476, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212114, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325545473'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000476, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212114, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325545729'),['Voice'] = '', }, 
        },
        [20000477] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000477, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212115, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325545985'),['Voice'] = '', }, 
        },
        [20000478] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000478, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212106, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325546241'),['Voice'] = '', }, 
        },
        [20000479] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000479, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212105, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325546497'),['Voice'] = '', }, 
        },
        [20000480] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000480, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212107, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325546753'),['Voice'] = '', }, 
        },
        [20000481] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325547009'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325547265'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325547521'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325547777'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000481, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325548033'),['Voice'] = '', }, 
        },
        [20000482] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000482, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212080, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325548289'),['Voice'] = '', }, 
        },
        [20000483] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000483, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212108, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325548545'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000483, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212108, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325548801'),['Voice'] = '', }, 
        },
        [20000484] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549057'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549313'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549569'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549825'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550081'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550337'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550593'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550849'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325551105'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 10, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325551361'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000484, ['Kind'] = 2, ['Order'] = 11, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325551617'),['Voice'] = '', }, 
        },
        [20000485] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000485, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902049792'),['Voice'] = '', }, 
        },
        [20000486] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000486, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325553409'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000486, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325553665'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000486, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325553921'),['Voice'] = '', }, 
        },
        [20000487] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000487, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325554177'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000487, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325554433'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000487, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325554689'),['Voice'] = '', }, 
        },
        [20000488] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325554945'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325555201'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325555457'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000488, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325555713'),['Voice'] = '', }, 
        },
        [20000489] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325556481'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325556737'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325556993'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325557249'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000489, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7213009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325557505'),['Voice'] = '', }, 
        },
        [20000490] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000490, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213015, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902053888'),['Voice'] = '', }, 
        },
        [20000491] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000491, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213016, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902054144'),['Voice'] = '', }, 
        },
        [20000492] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325559553'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325559809'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325560065'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000492, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213021, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325560321'),['Voice'] = '', }, 
        },
        [20000493] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000493, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325560577'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000493, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325560833'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000493, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325561089'),['Voice'] = '', }, 
        },
        [20000494] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325561857'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325561345'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325561601'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000494, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213024, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902056960'),['Voice'] = '', }, 
        },
        [20000495] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000495, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325562369'),['Voice'] = '', }, 
        },
        [20000496] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000496, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213028, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902057472'),['Voice'] = '', }, 
        },
        [20000497] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000497, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213029, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902057728'),['Voice'] = '', }, 
        },
        [20000498] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902057984'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325564673'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325564929'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000498, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213034, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325565185'),['Voice'] = '', }, 
        },
        [20000499] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000499, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325565441'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000499, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325565697'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000499, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213035, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325565953'),['Voice'] = '', }, 
        },
        [20000500] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000500, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213038, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902059776'),['Voice'] = '', }, 
        },
        [20000501] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000501, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325566977'),['Voice'] = '', }, 
        },
        [20000502] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000502, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213042, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325567233'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000502, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213042, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325567489'),['Voice'] = '', }, 
        },
        [20000503] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000503, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213043, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325567745'),['Voice'] = '', }, 
        },
        [20000505] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325569025'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325569281'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325569537'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325569793'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000505, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7213046, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325570049'),['Voice'] = '', }, 
        },
        [20000506] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000506, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325570305'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000506, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325570561'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000506, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213052, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325570817'),['Voice'] = '', }, 
        },
        [20000507] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000507, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213049, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325571073'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000507, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325571329'),['Voice'] = '', }, 
        },
        [20000508] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000508, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213050, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325571585'),['Voice'] = '', }, 
        },
        [20000509] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000509, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213051, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325571841'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000509, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213051, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325572097'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000509, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213051, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325572353'),['Voice'] = '', }, 
        },
        [20000510] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000510, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213057, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325572609'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000510, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213057, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325572865'),['Voice'] = '', }, 
        },
        [20000511] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000511, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213058, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325573121'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000511, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213058, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325573377'),['Voice'] = '', }, 
        },
        [20000512] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325574657'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325575169'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325574913'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000512, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325575425'),['Voice'] = '', }, 
        },
        [20000513] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325575681'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213060, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325575937'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325576193'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000513, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213060, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325576449'),['Voice'] = '', }, 
        },
        [20000514] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325576705'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213062, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325576961'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325577217'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213062, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325577473'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000514, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7213063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325577729'),['Voice'] = '', }, 
        },
        [20000515] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325577985'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325578241'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325578497'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000515, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213065, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325578753'),['Voice'] = '', }, 
        },
        [20000516] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000516, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213069, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902070272'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000516, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213069, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325579265'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000516, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213069, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902070784'),['Voice'] = '', }, 
        },
        [20000517] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000517, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325579777'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000517, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325580033'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000517, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325580289'),['Voice'] = '', }, 
        },
        [20000518] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325580545'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325580801'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325581057'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000518, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325581313'),['Voice'] = '', }, 
        },
        [20000519] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325581569'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325581825'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325582081'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000519, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325582337'),['Voice'] = '', }, 
        },
        [20000520] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325582593'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325582849'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325583105'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000520, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325583361'),['Voice'] = '', }, 
        },
        [20000521] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000521, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213061, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325583617'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000521, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213061, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325583873'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000521, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213061, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325584129'),['Voice'] = '', }, 
        },
        [20000522] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000522, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325584385'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000522, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325584641'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000522, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325584897'),['Voice'] = '', }, 
        },
        [20000523] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000523, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325585153'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000523, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325585409'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000523, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325585665'),['Voice'] = '', }, 
        },
        [20000524] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000524, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325585921'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000524, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325586177'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000524, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325586433'),['Voice'] = '', }, 
        },
        [20000525] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325586689'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325586945'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325587201'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000525, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325587457'),['Voice'] = '', }, 
        },
        [20000526] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000526, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325588481'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000526, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325588737'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000526, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325588993'),['Voice'] = '', }, 
        },
        [20000527] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000527, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213089, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325589249'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000527, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213089, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325589505'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000527, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213089, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325589761'),['Voice'] = '', }, 
        },
        [20000528] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000528, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213078, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325590017'),['Voice'] = '', }, 
        },
        [20000529] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000529, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213077, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902080768'),['Voice'] = '', }, 
        },
        [20000530] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000530, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7233008, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325592065'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000530, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7233005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325592321'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 3, ['ID'] = 20000530, ['Kind'] = 4, ['Order'] = 3, ['TalkerID'] = 7233006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325592577'),['Voice'] = '', }, 
        },
        [20000531] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000531, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7233009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325592833'),['Voice'] = '', }, 
        },
        [20000532] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000532, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7233003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325593089'),['Voice'] = '', }, 
        },
        [20000533] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000533, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235008, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325593345'),['Voice'] = '', }, 
        },
        [20000534] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000534, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235009, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325593601'),['Voice'] = '', }, 
        },
        [20000535] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000535, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325593857'),['Voice'] = '', }, 
        },
        [20000536] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000536, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235021, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325594113'),['Voice'] = '', }, 
        },
        [20000537] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000537, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325594369'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000537, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325594625'),['Voice'] = '', }, 
        },
        [20000538] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000538, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325594881'),['Voice'] = '', }, 
        },
        [20000539] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000539, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325595137'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000539, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235024, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325595393'),['Voice'] = '', }, 
        },
        [20000540] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000540, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325595649'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000540, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325595905'),['Voice'] = '', }, 
        },
        [20000541] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000541, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235017, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325596161'),['Voice'] = '', }, 
        },
        [20000542] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000542, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235026, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325596417'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000542, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235027, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325596673'),['Voice'] = '', }, 
        },
        [20000543] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000543, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235029, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325596929'),['Voice'] = '', }, 
        },
        [20000544] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000544, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235031, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325597185'),['Voice'] = '', }, 
        },
        [20000545] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000545, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235032, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325597441'),['Voice'] = '', }, 
        },
        [20000546] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000546, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235033, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325597697'),['Voice'] = '', }, 
        },
        [20000547] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000547, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235036, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325597953'),['Voice'] = '', }, 
        },
        [20000548] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000548, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235037, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325598209'),['Voice'] = '', }, 
        },
        [20000549] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325598465'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325598721'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235039, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325598977'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000549, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7235038, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325599233'),['Voice'] = '', }, 
        },
        [20000550] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000550, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325599489'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000550, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325599745'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000550, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235040, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325600001'),['Voice'] = '', }, 
        },
        [20000551] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000551, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235041, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325600257'),['Voice'] = '', }, 
        },
        [20000552] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000552, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235043, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325600513'),['Voice'] = '', }, 
        },
        [20000553] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000553, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235044, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325600769'),['Voice'] = '', }, 
        },
        [20000554] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000554, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325601025'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000554, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235045, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325601281'),['Voice'] = '', }, 
        },
        [20000555] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000555, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235048, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325601537'),['Voice'] = '', }, 
        },
        [20000556] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000556, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235053, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325601793'),['Voice'] = '', }, 
        },
        [20000557] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000557, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235055, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325602049'),['Voice'] = '', }, 
        },
        [20000558] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000558, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235057, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325602305'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000558, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235057, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325602561'),['Voice'] = '', }, 
        },
        [20000559] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000559, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235056, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325602817'),['Voice'] = '', }, 
        },
        [20000560] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000560, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325603073'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000560, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325603329'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000560, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235059, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325603585'),['Voice'] = '', }, 
        },
        [20000561] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000561, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235060, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325603841'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000561, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235060, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325604097'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000561, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235060, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325604353'),['Voice'] = '', }, 
        },
        [20000562] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000562, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235062, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325604609'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000562, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235062, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325604865'),['Voice'] = '', }, 
        },
        [20000563] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000563, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235063, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325605121'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000563, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235064, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325605377'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000563, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235065, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325605633'),['Voice'] = '', }, 
        },
        [20000564] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000564, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325605889'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000564, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325606145'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000564, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235066, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325606401'),['Voice'] = '', }, 
        },
        [20000565] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000565, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235067, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902095616'),['Voice'] = '', }, 
        },
        [20000566] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000566, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235068, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325606913'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000566, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235068, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325607169'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000566, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235068, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325607425'),['Voice'] = '', }, 
        },
        [20000567] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000567, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235082, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325607681'),['Voice'] = '', }, 
        },
        [20000568] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325608193'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235073, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325608449'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235075, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325608705'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000568, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7235074, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325608961'),['Voice'] = '', }, 
        },
        [20000569] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000569, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235088, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325609217'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000569, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235089, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325609473'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000569, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235090, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325609729'),['Voice'] = '', }, 
        },
        [20000570] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325609985'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325610241'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235087, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325610497'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000570, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7235093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325610753'),['Voice'] = '', }, 
        },
        [20000571] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000571, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235076, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325611009'),['Voice'] = '', }, 
        },
        [20000572] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000572, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325611265'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000572, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325611521'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000572, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325611777'),['Voice'] = '', }, 
        },
        [20000573] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000573, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325612033'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000573, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235071, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325612289'),['Voice'] = '', }, 
        },
        [20000574] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000574, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235077, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325612289'),['Voice'] = '', }, 
        },
        [20000575] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000575, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235102, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325612801'),['Voice'] = '', }, 
        },
        [20000576] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000576, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235091, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902101760'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000576, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235091, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325613313'),['Voice'] = '', }, 
        },
        [20000577] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000577, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902102272'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000577, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902102528'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000577, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902102784'),['Voice'] = '', }, 
        },
        [20000578] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000578, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7239021, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325617409'),['Voice'] = '', }, 
        },
        [20000579] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000579, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7239016, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325619201'),['Voice'] = '', }, 
        },
        [20000580] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000580, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325619457'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000580, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231005, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902103808'),['Voice'] = '', }, 
        },
        [20000581] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000581, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325619969'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000581, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7231007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325620225'),['Voice'] = '', }, 
        },
        [20000582] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000582, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325620481'),['Voice'] = '', }, 
        },
        [20000583] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000583, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7239023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325620737'),['Voice'] = '', }, 
        },
        [20000584] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000584, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241003, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325622529'),['Voice'] = '', }, 
        },
        [20000585] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325623297'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7241005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325623553'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7241004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325623809'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7241004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325624065'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000585, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7241005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325624321'),['Voice'] = '', }, 
        },
        [20000586] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000586, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242001, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325624577'),['Voice'] = '', }, 
        },
        [20000587] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000587, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242002, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325624833'),['Voice'] = '', }, 
        },
        [20000588] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000588, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242004, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325625089'),['Voice'] = '', }, 
        },
        [20000589] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000589, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242005, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902107392'),['Voice'] = '', }, 
        },
        [20000590] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000590, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325625601'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000590, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242006, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325625857'),['Voice'] = '', }, 
        },
        [20000591] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000591, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325626113'),['Voice'] = '', }, 
        },
        [20000592] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000592, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242008, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902108416'),['Voice'] = '', }, 
        },
        [20000593] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000593, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242010, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325626625'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000593, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325626881'),['Voice'] = '', }, 
        },
        [20000594] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000594, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242012, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325627137'),['Voice'] = '', }, 
        },
        [20000595] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000595, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242013, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325627393'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000595, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242014, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325627649'),['Voice'] = '', }, 
        },
        [20000596] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000596, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242015, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325627905'),['Voice'] = '', }, 
        },
        [20000597] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000597, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242018, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325628161'),['Voice'] = '', }, 
        },
        [20000598] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000598, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242019, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325628417'),['Voice'] = '', }, 
        },
        [20000599] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000599, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242020, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325628673'),['Voice'] = '', }, 
        },
        [20000600] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000600, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242022, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325629185'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000600, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242023, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325629441'),['Voice'] = '', }, 
        },
        [20000601] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000601, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242025, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325629697'),['Voice'] = '', }, 
        },
        [20000602] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000602, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325579777'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000602, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325580033'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000602, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213070, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325580289'),['Voice'] = '', }, 
        },
        [20000603] = {{['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549057'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549313'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549569'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325549825'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550081'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550337'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550593'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325550849'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7212116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325551105'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 10, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325551361'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2.5, ['ID'] = 20000603, ['Kind'] = 2, ['Order'] = 11, ['TalkerID'] = 7212117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325551617'),['Voice'] = '', }, 
        },
        [20000604] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000604, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211101, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325309697'),['Voice'] = '', }, {['Delay'] = 2.5, ['Duration'] = 5, ['ID'] = 20000604, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211102, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325309953'),['Voice'] = '', }, 
        },
        [20000605] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000605, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325495809'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000605, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325496065'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000605, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325496321'),['Voice'] = '', }, 
        },
        [20000606] = {{['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325533953'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325534209'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325534465'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2.5, ['ID'] = 20000606, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7212095, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325534721'),['Voice'] = '', }, 
        },
        [20000607] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000607, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902117632'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000607, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265002, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902117888'),['Voice'] = '', }, 
        },
        [20000608] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000608, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265004, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902118144'),['Voice'] = '', }, 
        },
        [20000609] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265006, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078709504'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078709760'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265006, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078710016'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902119168'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902119424'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7265005, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078710528'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000609, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7265006, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902119936'),['Voice'] = '', }, 
        },
        [20000610] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000610, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265007, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902120192'),['Voice'] = '', }, 
        },
        [20000611] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000611, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265008, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902120448'),['Voice'] = '', }, 
        },
        [20000612] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000612, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265009, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902120704'),['Voice'] = '', }, 
        },
        [20000613] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000613, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265011, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325596417'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000613, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265012, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325596673'),['Voice'] = '', }, 
        },
        [20000614] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265015, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902121472'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265014, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078719488'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265015, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902121984'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265015, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902122240'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000614, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265014, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078721024'),['Voice'] = '', }, 
        },
        [20000615] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265021, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902122752'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265021, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078721280'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265022, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078721536'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265021, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078721792'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 5, ['ID'] = 20000615, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265022, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078722048'),['Voice'] = '', }, 
        },
        [20000616] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000616, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902124032'),['Voice'] = '', }, 
        },
        [20000617] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000617, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265018, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902124288'),['Voice'] = '', }, 
        },
        [20000618] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000618, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265023, ['Text'] = Game.TableDataManager:GetLangStr('str_58345251669504'),['Voice'] = '', }, 
        },
        [20000619] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000619, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265024, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902124800'),['Voice'] = '', }, 
        },
        [20000620] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000620, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265034, ['Text'] = Game.TableDataManager:GetLangStr('str_38830263137536'),['Voice'] = '', }, 
        },
        [20000621] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000621, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265028, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325599489'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000621, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265028, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902125568'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000621, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265028, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902125824'),['Voice'] = '', }, 
        },
        [20000622] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000622, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265029, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902126080'),['Voice'] = '', }, 
        },
        [20000623] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000623, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265031, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902126336'),['Voice'] = '', }, 
        },
        [20000624] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000624, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265035, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902126592'),['Voice'] = '', }, 
        },
        [20000625] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000625, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265036, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902126848'),['Voice'] = '', }, 
        },
        [20000626] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000626, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265037, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325600769'),['Voice'] = '', }, 
        },
        [20000627] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000627, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265038, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902127360'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000627, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902127616'),['Voice'] = '', }, 
        },
        [20000628] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000628, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265040, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902127872'),['Voice'] = '', }, 
        },
        [20000629] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000629, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265044, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902128128'),['Voice'] = '', }, 
        },
        [20000630] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000630, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265041, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902128384'),['Voice'] = '', }, 
        },
        [20000631] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000631, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902128640'),['Voice'] = '', }, 
        },
        [20000632] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000632, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265051, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902128896'),['Voice'] = '', }, 
        },
        [20000633] = {{['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000633, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265053, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902129152'),['Voice'] = '', }, 
        },
        [20000634] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000634, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265054, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902129408'),['Voice'] = '', }, 
        },
        [20000635] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000635, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902129664'),['Voice'] = '', }, 
        },
        [20000636] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000636, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265056, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902129920'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 3, ['ID'] = 20000636, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265057, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902130176'),['Voice'] = '', }, 
        },
        [20000637] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000637, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265059, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902130432'),['Voice'] = '', }, 
        },
        [20000638] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000638, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265060, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902130688'),['Voice'] = '', }, 
        },
        [20000639] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000639, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265094, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902130944'),['Voice'] = '', }, 
        },
        [20000640] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000640, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265061, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902131200'),['Voice'] = '', }, 
        },
        [20000641] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000641, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265062, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902131456'),['Voice'] = '', }, 
        },
        [20000642] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000642, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265066, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902131712'),['Voice'] = '', }, 
        },
        [20000643] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000643, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265070, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902131968'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000643, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265070, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902132224'),['Voice'] = '', }, 
        },
        [20000644] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000644, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265068, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902132480'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000644, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265068, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902132736'),['Voice'] = '', }, 
        },
        [20000645] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000645, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265067, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902132992'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000645, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265067, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902133248'),['Voice'] = '', }, 
        },
        [20000646] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000646, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265073, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920625152'),['Voice'] = '', }, 
        },
        [20000647] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000647, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325603841'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000647, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325604097'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000647, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265072, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325604353'),['Voice'] = '', }, 
        },
        [20000648] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000648, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265074, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902134528'),['Voice'] = '', }, 
        },
        [20000649] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000649, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265075, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902134784'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000649, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265075, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902135040'),['Voice'] = '', }, 
        },
        [20000650] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000650, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265076, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902135296'),['Voice'] = '', }, 
        },
        [20000651] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000651, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265078, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325594881'),['Voice'] = '', }, 
        },
        [20000652] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265081, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902135808'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265082, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902136064'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265082, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902136320'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000652, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265083, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902136576'),['Voice'] = '', }, 
        },
        [20000653] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000653, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265079, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902136832'),['Voice'] = '', }, 
        },
        [20000654] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000654, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265080, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902137088'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000654, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265080, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325604865'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000654, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902137600'),['Voice'] = '', }, 
        },
        [20000655] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000655, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265084, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325605889'),['Voice'] = '', }, 
        },
        [20000656] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000656, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265086, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902138112'),['Voice'] = '', }, 
        },
        [20000657] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000657, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265087, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902138368'),['Voice'] = '', }, 
        },
        [20000658] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000658, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265088, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902138624'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000658, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265088, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902138880'),['Voice'] = '', }, 
        },
        [20000659] = {{['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902139136'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902139392'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902139648'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902139904'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902140160'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902140416'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000659, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7265089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902140672'),['Voice'] = '', }, 
        },
        [20000660] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000660, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265090, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902140928'),['Voice'] = '', }, 
        },
        [20000661] = {{['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000661, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265091, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902141184'),['Voice'] = '', }, {['Delay'] = 0.3, ['Duration'] = 2, ['ID'] = 20000661, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265091, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902141440'),['Voice'] = '', }, 
        },
        [20000662] = {{['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000662, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241007, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902141696'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 2, ['ID'] = 20000662, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7241007, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325623041'),['Voice'] = '', }, 
        },
        [20000663] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325395713'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325395969'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325396225'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250117, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325396481'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000663, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250116, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325396737'),['Voice'] = '', }, 
        },
        [20000664] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902143488'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250146, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902143744'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250146, ['Text'] = Game.TableDataManager:GetLangStr('str_58345251679488'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_58345251679744'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_58345251680000'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250146, ['Text'] = Game.TableDataManager:GetLangStr('str_58345251680256'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902145024'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000664, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7250145, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902145280'),['Voice'] = '', }, 
        },
        [20000665] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000665, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250152, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902145536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000665, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250153, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902145792'),['Voice'] = '', }, 
        },
        [20000666] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000666, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250154, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902146048'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000666, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250155, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902146304'),['Voice'] = '', }, 
        },
        [20000667] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000667, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250156, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902146560'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000667, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250157, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902146816'),['Voice'] = '', }, 
        },
        [20000668] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000668, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250160, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902147072'),['Voice'] = '', }, 
        },
        [20000669] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000669, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250164, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902147328'),['Voice'] = '', }, 
        },
        [20000670] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000670, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250165, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902147584'),['Voice'] = '', }, 
        },
        [20000671] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000671, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250140, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902147840'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000671, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250140, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902148096'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000671, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250140, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902148352'),['Voice'] = '', }, 
        },
        [20000672] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000672, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219020, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902148608'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000672, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219019, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902148864'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000672, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219020, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902149120'),['Voice'] = '', }, 
        },
        [20000673] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000673, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265047, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902149376'),['Voice'] = '', }, 
        },
        [20000674] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000674, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255008, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902149632'),['Voice'] = '', }, 
        },
        [20000675] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000675, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255006, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902149888'),['Voice'] = '', }, 
        },
        [20000676] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000676, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255007, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902150144'),['Voice'] = '', }, 
        },
        [20000677] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000677, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219024, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902150400'),['Voice'] = '', }, 
        },
        [20000678] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265093, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325301761'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265093, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078761984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265092, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078762240'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265093, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078762496'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000678, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265092, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078762752'),['Voice'] = '', }, 
        },
        [20000679] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000679, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250178, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902151936'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000679, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250180, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902152192'),['Voice'] = '', }, 
        },
        [20000680] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000680, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250182, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902152448'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000680, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250181, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902152704'),['Voice'] = '', }, 
        },
        [20000681] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000681, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250183, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956803328'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000681, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250184, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956803584'),['Voice'] = '', }, 
        },
        [20000682] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000682, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210852, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325394945'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000682, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210853, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325395201'),['Voice'] = '', }, 
        },
        [20000683] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000683, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250185, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902153984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000683, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250185, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902154240'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000683, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250185, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902154496'),['Voice'] = '', }, 
        },
        [20000684] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000684, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250200, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902154752'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000684, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250200, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902155008'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000684, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250201, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902155264'),['Voice'] = '', }, 
        },
        [20000685] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000685, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250222, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902155520'),['Voice'] = '', }, 
        },
        [20000686] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000686, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207100, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902155776'),['Voice'] = '', }, 
        },
        [20000687] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000687, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219040, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902156032'),['Voice'] = '', }, 
        },
        [20000688] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000688, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235102, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902156288'),['Voice'] = '', }, 
        },
        [20000689] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000689, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235098, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902156544'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000689, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235098, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902156800'),['Voice'] = '', }, 
        },
        [20000690] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000690, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211045, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902157056'),['Voice'] = '', }, 
        },
        [20000691] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000691, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219044, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902157312'),['Voice'] = '', }, 
        },
        [20000692] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000692, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902157568'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000692, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902157824'),['Voice'] = '', }, 
        },
        [20000693] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000693, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235070, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902158080'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000693, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235070, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902158336'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000693, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235070, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902158592'),['Voice'] = '', }, 
        },
        [20000694] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000694, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902158848'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000694, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7830268, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902159104'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000694, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7235084, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902159360'),['Voice'] = '', }, 
        },
        [20000695] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000695, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250128, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902159616'),['Voice'] = '', }, 
        },
        [20000696] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000696, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250126, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902159872'),['Voice'] = '', }, 
        },
        [20000697] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000697, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235088, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902160128'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000697, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235090, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902160384'),['Voice'] = '', }, 
        },
        [20000698] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000698, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235083, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902160640'),['Voice'] = '', }, 
        },
        [20000699] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000699, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235076, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902160896'),['Voice'] = '', }, 
        },
        [20000700] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000700, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902161152'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000700, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902161408'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000700, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219041, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902161664'),['Voice'] = '', }, 
        },
        [20000701] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000701, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7241009, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902161920'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000701, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7241001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902162176'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000701, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7241009, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902162432'),['Voice'] = '', }, 
        },
        [20000702] = {{['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000702, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250230, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902162688'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000702, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250231, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902162944'),['Voice'] = '', }, 
        },
        [20000703] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000703, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7235095, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902163200'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000703, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7235096, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902163456'),['Voice'] = '', }, 
        },
        [20000704] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000704, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242118, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902163712'),['Voice'] = '', }, 
        },
        [20000705] = {{['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902163968'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250232, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902164224'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250233, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902164224'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 1.5, ['ID'] = 20000705, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250234, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902164736'),['Voice'] = '', }, 
        },
        [20000706] = {{['Delay'] = 0.5, ['Duration'] = 3.5, ['ID'] = 20000706, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250235, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902164992'),['Voice'] = '', }, 
        },
        [20000707] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902165248'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242053, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902165504'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902165760'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242053, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902166016'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325299713'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000707, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242054, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902166528'),['Voice'] = '', }, 
        },
        [20000708] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000708, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902166784'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000708, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902167040'),['Voice'] = '', }, 
        },
        [20000709] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000709, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242056, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902167296'),['Voice'] = '', }, 
        },
        [20000710] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242060, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902167552'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242061, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902167808'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242060, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902168064'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000710, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242061, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902168320'),['Voice'] = '', }, 
        },
        [20000711] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242063, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902168576'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242062, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902168832'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242063, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902169088'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000711, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242062, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902169344'),['Voice'] = '', }, 
        },
        [20000712] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000712, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902169600'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000712, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902169856'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000712, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902170112'),['Voice'] = '', }, 
        },
        [20000713] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000713, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242065, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902170368'),['Voice'] = '', }, 
        },
        [20000714] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000714, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242071, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902170624'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000714, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242071, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902170880'),['Voice'] = '', }, 
        },
        [20000715] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000715, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242072, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902171136'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000715, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242073, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902171392'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000715, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242072, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902171648'),['Voice'] = '', }, 
        },
        [20000716] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000716, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242074, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902171904'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000716, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242074, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902172160'),['Voice'] = '', }, 
        },
        [20000717] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000717, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242075, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902172416'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000717, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242075, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902172672'),['Voice'] = '', }, 
        },
        [20000718] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000718, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242076, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902172928'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000718, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242077, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902173184'),['Voice'] = '', }, 
        },
        [20000719] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000719, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242078, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902173440'),['Voice'] = '', }, 
        },
        [20000720] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000720, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242079, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902173696'),['Voice'] = '', }, 
        },
        [20000721] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000721, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242080, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902173952'),['Voice'] = '', }, 
        },
        [20000722] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000722, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242081, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902174208'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000722, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242082, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902174464'),['Voice'] = '', }, 
        },
        [20000723] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000723, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902174720'),['Voice'] = '', }, 
        },
        [20000724] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000724, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242086, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902174976'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000724, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242087, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902175232'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000724, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242086, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902175488'),['Voice'] = '', }, 
        },
        [20000725] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000725, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242088, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902175744'),['Voice'] = '', }, 
        },
        [20000726] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000726, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902176000'),['Voice'] = '', }, 
        },
        [20000727] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000727, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242090, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902176256'),['Voice'] = '', }, 
        },
        [20000728] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000728, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242091, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902176512'),['Voice'] = '', }, 
        },
        [20000729] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902176768'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902177024'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902177280'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902177536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902177792'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902178048'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902178304'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902178560'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000729, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7242049, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902178816'),['Voice'] = '', }, 
        },
        [20000730] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242050, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902179072'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242051, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902179328'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242050, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902179584'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242051, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902179840'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242050, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902180096'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000730, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242051, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902180352'),['Voice'] = '', }, 
        },
        [20000731] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000731, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242052, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902180608'),['Voice'] = '', }, 
        },
        [20000732] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000732, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250236, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902180864'),['Voice'] = '', }, 
        },
        [20000733] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250239, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902181120'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250240, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902181376'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250238, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902181632'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250241, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902181888'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000733, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250239, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902182144'),['Voice'] = '', }, 
        },
        [20000734] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000734, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250242, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902182400'),['Voice'] = '', }, 
        },
        [20000735] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000735, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250245, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902182656'),['Voice'] = '', }, 
        },
        [20000736] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000736, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250249, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902182912'),['Voice'] = '', }, 
        },
        [20000737] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000737, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223018, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902183168'),['Voice'] = '', }, 
        },
        [20000738] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000738, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250253, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902183424'),['Voice'] = '', }, 
        },
        [20000739] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000739, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250256, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902183680'),['Voice'] = '', }, 
        },
        [20000740] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000740, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250186, ['Text'] = Game.TableDataManager:GetLangStr('str_57794958984705'),['Voice'] = '', }, 
        },
        [20000741] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000741, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250189, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902184192'),['Voice'] = '', }, 
        },
        [20000742] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000742, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250258, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902184448'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000742, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7250259, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902184704'),['Voice'] = '', }, 
        },
        [20000743] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000743, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902184960'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000743, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902185216'),['Voice'] = '', }, 
        },
        [20000744] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000744, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902185984'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 5, ['ID'] = 20000744, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902186240'),['Voice'] = '', }, 
        },
        [20000745] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000745, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902186496'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000745, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902186752'),['Voice'] = '', }, {['Delay'] = 6.5, ['Duration'] = 4, ['ID'] = 20000745, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902187008'),['Voice'] = '', }, 
        },
        [20000746] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000746, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207070, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902187264'),['Voice'] = '', }, 
        },
        [20000747] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000747, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250304, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902187520'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000747, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250304, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902187776'),['Voice'] = '', }, 
        },
        [20000748] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000748, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211040, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902188032'),['Voice'] = '', }, 
        },
        [20000749] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000749, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211040, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902188288'),['Voice'] = '', }, 
        },
        [20000750] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000750, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250305, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902188544'),['Voice'] = '', }, 
        },
        [20000751] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000751, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902188800'),['Voice'] = '', }, 
        },
        [20000752] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000752, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902189056'),['Voice'] = '', }, 
        },
        [20000753] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000753, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212119, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902189312'),['Voice'] = '', }, 
        },
        [20000754] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000754, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212119, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902189568'),['Voice'] = '', }, 
        },
        [20000755] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000755, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7200001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902189824'),['Voice'] = '', }, 
        },
        [20000756] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000756, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7200001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902190080'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000756, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7220006, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902190336'),['Voice'] = '', }, 
        },
        [20000757] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000757, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250346, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325344769'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000757, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250347, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325483777'),['Voice'] = '', }, 
        },
        [20000758] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000758, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250345, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325485057'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000758, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250344, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902191360'),['Voice'] = '', }, 
        },
        [20000759] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000759, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250349, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325485057'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000759, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250348, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902191360'),['Voice'] = '', }, 
        },
        [20000760] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250352, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902156544'),['Voice'] = '', }, {['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250351, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902156800'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250350, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325292801'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 5, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250353, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325343745'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 2, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250354, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325351169'),['Voice'] = '', }, {['Delay'] = 9, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250357, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325485057'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7250358, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902193664'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7250359, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902193920'),['Voice'] = '', }, {['Delay'] = 1.5, ['Duration'] = 3, ['ID'] = 20000760, ['Kind'] = 2, ['Order'] = 9, ['TalkerID'] = 7250355, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902194176'),['Voice'] = '', }, 
        },
        [20000761] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000761, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902194432'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000761, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902194688'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000761, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902194944'),['Voice'] = '', }, 
        },
        [20000762] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000762, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902195200'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000762, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902195456'),['Voice'] = '', }, 
        },
        [20000763] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000763, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212105, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902195712'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 3, ['ID'] = 20000763, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212105, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902195968'),['Voice'] = '', }, 
        },
        [20000764] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000764, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902196224'),['Voice'] = '', }, 
        },
        [20000765] = {{['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000765, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219033, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902196480'),['Voice'] = '', }, 
        },
        [20000766] = {{['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20000766, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250342, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902196736'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000766, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250342, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902196992'),['Voice'] = '', }, 
        },
        [20000767] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902197248'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 5, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902197504'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 5, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902197760'),['Voice'] = '', }, {['Delay'] = 13, ['Duration'] = 5, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902198016'),['Voice'] = '', }, {['Delay'] = 18, ['Duration'] = 6, ['ID'] = 20000767, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7219026, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902198272'),['Voice'] = '', }, 
        },
        [20000768] = {{['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000768, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213083, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902185472'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000768, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207025, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902185728'),['Voice'] = '', }, 
        },
        [20000769] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000769, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250363, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902198528'),['Voice'] = '', }, 
        },
        [20000770] = {{['Delay'] = 1, ['Duration'] = 6, ['ID'] = 20000770, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250363, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902198784'),['Voice'] = '', }, 
        },
        [20000771] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000771, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250363, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902199040'),['Voice'] = '', }, 
        },
        [20000772] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000772, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902199296'),['Voice'] = '', }, 
        },
        [20000773] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000773, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212064, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902199552'),['Voice'] = '', }, 
        },
        [20000774] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000774, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242175, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902199808'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 5, ['ID'] = 20000774, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7219036, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902200064'),['Voice'] = '', }, {['Delay'] = 10, ['Duration'] = 5, ['ID'] = 20000774, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219036, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902200320'),['Voice'] = '', }, 
        },
        [20000775] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000775, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250375, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902200576'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000775, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7250375, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902200832'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000775, ['Kind'] = 4, ['Order'] = 3, ['TalkerID'] = 7250375, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902201088'),['Voice'] = '', }, 
        },
        [20000776] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000776, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250376, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902201344'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000776, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250376, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902201600'),['Voice'] = '', }, 
        },
        [20000777] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000777, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250379, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902201856'),['Voice'] = '', }, 
        },
        [20000778] = {{['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000778, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250381, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902202112'),['Voice'] = '', }, 
        },
        [20000779] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000779, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250383, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902202368'),['Voice'] = '', }, 
        },
        [20000780] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000780, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250388, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902202624'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000780, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250394, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902202880'),['Voice'] = '', }, 
        },
        [20000781] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000781, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250391, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902203136'),['Voice'] = '', }, 
        },
        [20000782] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000782, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250387, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902203392'),['Voice'] = '', }, 
        },
        [20000783] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000783, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250398, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902203648'),['Voice'] = '', }, 
        },
        [20000784] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000784, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250389, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902203904'),['Voice'] = 'Play_MX_Gameplay_Subplot_AsYouWish_Theme_MusicBox.Play_MX_Gameplay_Subplot_AsYouWish_Theme_MusicBox', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000784, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250389, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902204160'),['Voice'] = '', }, 
        },
        [20000785] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000785, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250386, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902204416'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000785, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250386, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902204672'),['Voice'] = '', }, 
        },
        [20000786] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000786, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250392, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902204928'),['Voice'] = '', }, 
        },
        [20000787] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000787, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250393, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902205184'),['Voice'] = '', }, 
        },
        [20000788] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902205440'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223002, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902205696'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7223003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902205952'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000788, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7223003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902206208'),['Voice'] = '', }, 
        },
        [20000789] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000789, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250413, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902206464'),['Voice'] = '', }, 
        },
        [20000790] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000790, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265289, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902206720'),['Voice'] = '', }, 
        },
        [20000791] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000791, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250415, ['Text'] = Game.TableDataManager:GetLangStr('str_39311030498816'),['Voice'] = '', }, 
        },
        [20000792] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000792, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250416, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902207232'),['Voice'] = '', }, 
        },
        [20000793] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000793, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250417, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902207488'),['Voice'] = '', }, 
        },
        [20000794] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000794, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213080, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902207744'),['Voice'] = '', }, 
        },
        [20000795] = {{['Delay'] = 1, ['Duration'] = 2, ['ID'] = 20000795, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250419, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902208000'),['Voice'] = '', }, 
        },
        [20000796] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265292, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902208256'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265293, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902208512'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265294, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902208768'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265292, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902209024'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 5, ['ID'] = 20000796, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265292, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902209280'),['Voice'] = '', }, 
        },
        [20000797] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000797, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7227005, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325615105'),['Voice'] = '', }, 
        },
        [20000798] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000798, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250422, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902209792'),['Voice'] = '', }, 
        },
        [20000799] = {{['Delay'] = 2, ['Duration'] = 2, ['ID'] = 20000799, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250423, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902210048'),['Voice'] = '', }, 
        },
        [20000800] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000800, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265325, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902210304'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000800, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265325, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902210560'),['Voice'] = '', }, {['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000800, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265325, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902210816'),['Voice'] = '', }, 
        },
        [20000801] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000801, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250427, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902211072'),['Voice'] = '', }, 
        },
        [20000802] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000802, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250429, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902211328'),['Voice'] = '', }, 
        },
        [20000803] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000803, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242191, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902211584'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 6, ['ID'] = 20000803, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902211840'),['Voice'] = '', }, 
        },
        [20000804] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000804, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902212096'),['Voice'] = '', }, 
        },
        [20000805] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000805, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242191, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902212352'),['Voice'] = '', }, {['Delay'] = 5.5, ['Duration'] = 5, ['ID'] = 20000805, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211066, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902212608'),['Voice'] = '', }, 
        },
        [20000806] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000806, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242192, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902212864'),['Voice'] = '', }, 
        },
        [20000807] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000807, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242192, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902213120'),['Voice'] = '', }, 
        },
        [20000808] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000808, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242192, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902213376'),['Voice'] = '', }, 
        },
        [20000809] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000809, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250430, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902213632'),['Voice'] = '', }, 
        },
        [20000810] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000810, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242193, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920886272'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 4, ['ID'] = 20000810, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242193, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920886784'),['Voice'] = '', }, 
        },
        [20000811] = {{['Delay'] = 2, ['Duration'] = 5, ['ID'] = 20000811, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242193, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902214400'),['Voice'] = '', }, 
        },
        [20000812] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000812, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242195, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902214656'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 6, ['ID'] = 20000812, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242195, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902214912'),['Voice'] = '', }, {['Delay'] = 13, ['Duration'] = 9, ['ID'] = 20000812, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242195, ['Text'] = Game.TableDataManager:GetLangStr('str_30032290424832'),['Voice'] = '', }, 
        },
        [20000813] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000813, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242196, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902215424'),['Voice'] = '', }, 
        },
        [20000814] = {{['Delay'] = 2, ['Duration'] = 8, ['ID'] = 20000814, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242197, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902215680'),['Voice'] = '', }, 
        },
        [20000815] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000815, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213030, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902215936'),['Voice'] = '', }, 
        },
        [20000816] = {{['Delay'] = 2, ['Duration'] = 4, ['ID'] = 20000816, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213019, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902216192'),['Voice'] = '', }, 
        },
        [20000817] = {{['Delay'] = 1.5, ['Duration'] = 3, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 1, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902049792'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 2, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 2, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902216704'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 2, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 3, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902216960'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 2, ['ID'] = 20000817, ['Kind'] = 1, ['Order'] = 4, ['TalkerID'] = 7213039, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902217216'),['Voice'] = '', }, 
        },
        [20000818] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902049792'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 2, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902216704'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 2, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902216960'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 2, ['ID'] = 20000818, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902217216'),['Voice'] = '', }, 
        },
        [20000819] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000819, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7250435, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902218496'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000819, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7250435, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902218752'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000819, ['Kind'] = 4, ['Order'] = 3, ['TalkerID'] = 7250435, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902219008'),['Voice'] = '', }, 
        },
        [20000820] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902219264'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 4, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902219520'),['Voice'] = '', }, {['Delay'] = 9, ['Duration'] = 4, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7219032, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902219776'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 5, ['ID'] = 20000820, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7213079, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902220032'),['Voice'] = '', }, 
        },
        [20000821] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000821, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250436, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902220288'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 4, ['ID'] = 20000821, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250437, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902220544'),['Voice'] = '', }, 
        },
        [20000822] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000822, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250438, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902220800'),['Voice'] = '', }, {['Delay'] = 5.5, ['Duration'] = 4, ['ID'] = 20000822, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250439, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902221056'),['Voice'] = '', }, 
        },
        [20000823] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250440, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902221312'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 4, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250441, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902221568'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250440, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902221824'),['Voice'] = '', }, {['Delay'] = 12.5, ['Duration'] = 4, ['ID'] = 20000823, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250441, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902222080'),['Voice'] = '', }, 
        },
        [20000824] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250442, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902222336'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3.5, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250443, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902222592'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 4, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250442, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902222848'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 2.5, ['ID'] = 20000824, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250443, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902223104'),['Voice'] = '', }, 
        },
        [20000825] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265338, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902223360'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265339, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902223616'),['Voice'] = '', }, {['Delay'] = 10, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265339, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902223872'),['Voice'] = '', }, {['Delay'] = 13.5, ['Duration'] = 3, ['ID'] = 20000825, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265338, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902224128'),['Voice'] = '', }, 
        },
        [20000826] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000826, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265336, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902224384'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 2, ['ID'] = 20000826, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265337, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902224640'),['Voice'] = '', }, 
        },
        [20000827] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000827, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265334, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902224896'),['Voice'] = '', }, 
        },
        [20000828] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000828, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265333, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902225152'),['Voice'] = '', }, 
        },
        [20000829] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000829, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265332, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902225408'),['Voice'] = '', }, 
        },
        [20000830] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000830, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250445, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956772352'),['Voice'] = '', }, 
        },
        [20000831] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000831, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250447, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902225920'),['Voice'] = '', }, 
        },
        [20000832] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000832, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902226176'),['Voice'] = '', }, 
        },
        [20000833] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000833, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255070, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956642816'),['Voice'] = '', }, 
        },
        [20000834] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000834, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255071, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902226688'),['Voice'] = '', }, 
        },
        [20000835] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000835, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255073, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902226944'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000835, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255073, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902227200'),['Voice'] = '', }, 
        },
        [20000836] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000836, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255075, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902227456'),['Voice'] = '', }, 
        },
        [20000837] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255076, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902227712'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255077, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902227968'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255076, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902228224'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255076, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902228480'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000837, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7255077, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902228736'),['Voice'] = '', }, 
        },
        [20000838] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000838, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255078, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902228992'),['Voice'] = '', }, 
        },
        [20000839] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000839, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255080, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956642816'),['Voice'] = '', }, 
        },
        [20000840] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000840, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255081, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956616192'),['Voice'] = '', }, 
        },
        [20000841] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000841, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255082, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902229760'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000841, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255082, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902230016'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000841, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255082, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902230272'),['Voice'] = '', }, 
        },
        [20000842] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000842, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255083, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902230528'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000842, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255083, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902230784'),['Voice'] = '', }, 
        },
        [20000843] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000843, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255084, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902231040'),['Voice'] = '', }, 
        },
        [20000844] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000844, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008901874688'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000844, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902231552'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000844, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255085, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902231808'),['Voice'] = '', }, 
        },
        [20000845] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000845, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255086, ['Text'] = Game.TableDataManager:GetLangStr('str_59443152832000'),['Voice'] = '', }, 
        },
        [20000846] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000846, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255087, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902232320'),['Voice'] = '', }, 
        },
        [20000847] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000847, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255088, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902232576'),['Voice'] = '', }, 
        },
        [20000848] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000848, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255089, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902232832'),['Voice'] = '', }, 
        },
        [20000849] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000849, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255090, ['Text'] = Game.TableDataManager:GetLangStr('str_59443152832000'),['Voice'] = '', }, 
        },
        [20000850] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000850, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255091, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902233344'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000850, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255091, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902233600'),['Voice'] = '', }, 
        },
        [20000851] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000851, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255092, ['Text'] = Game.TableDataManager:GetLangStr('str_59443152832000'),['Voice'] = '', }, 
        },
        [20000852] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000852, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255093, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902234112'),['Voice'] = '', }, 
        },
        [20000853] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000853, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255094, ['Text'] = Game.TableDataManager:GetLangStr('str_59443152832000'),['Voice'] = '', }, 
        },
        [20000854] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000854, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255095, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902234624'),['Voice'] = '', }, 
        },
        [20000855] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000855, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255096, ['Text'] = Game.TableDataManager:GetLangStr('str_59443152832000'),['Voice'] = '', }, 
        },
        [20000856] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000856, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255097, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902235136'),['Voice'] = '', }, 
        },
        [20000857] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000857, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255098, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902235392'),['Voice'] = '', }, 
        },
        [20000858] = {{['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000858, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255099, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902235648'),['Voice'] = '', }, 
        },
        [20000859] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000859, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255100, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902235904'),['Voice'] = '', }, 
        },
        [20000860] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255101, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902236160'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255102, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902236416'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255101, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902236672'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000860, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255102, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902236928'),['Voice'] = '', }, 
        },
        [20000861] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000861, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255103, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902237184'),['Voice'] = '', }, 
        },
        [20000862] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000862, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255103, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902237440'),['Voice'] = '', }, 
        },
        [20000863] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000863, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255104, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902237696'),['Voice'] = '', }, 
        },
        [20000864] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000864, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255106, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902237952'),['Voice'] = '', }, 
        },
        [20000865] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000865, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255107, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902238208'),['Voice'] = '', }, 
        },
        [20000866] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000866, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255108, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902238464'),['Voice'] = '', }, 
        },
        [20000867] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000867, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255109, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920536832'),['Voice'] = '', }, 
        },
        [20000868] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000868, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255110, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902238976'),['Voice'] = '', }, 
        },
        [20000869] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000869, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255111, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902239232'),['Voice'] = '', }, 
        },
        [20000870] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000870, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255112, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902239488'),['Voice'] = '', }, 
        },
        [20000871] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000871, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255113, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902239744'),['Voice'] = '', }, 
        },
        [20000872] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000872, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255114, ['Text'] = Game.TableDataManager:GetLangStr('str_57726507950336'),['Voice'] = '', }, 
        },
        [20000873] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000873, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255118, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902240256'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000873, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255118, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902240512'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000873, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255118, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902240768'),['Voice'] = '', }, 
        },
        [20000874] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000874, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255120, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902241024'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000874, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255120, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902241280'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000874, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255120, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902241536'),['Voice'] = '', }, 
        },
        [20000875] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000875, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255121, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902241792'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000875, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255121, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902242048'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000875, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255121, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902242304'),['Voice'] = '', }, 
        },
        [20000876] = {{['Delay'] = 0, ['Duration'] = 3.5, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250455, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902242560'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250456, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902242816'),['Voice'] = '', }, {['Delay'] = 8.5, ['Duration'] = 4, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250455, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902243072'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 3, ['ID'] = 20000876, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250456, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902243328'),['Voice'] = '', }, 
        },
        [20000877] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20000877, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250453, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902243584'),['Voice'] = '', }, 
        },
        [20000878] = {{['Delay'] = 0, ['Duration'] = 8, ['ID'] = 20000878, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265363, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902243840'),['Voice'] = '', }, 
        },
        [20000879] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000879, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265366, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902244096'),['Voice'] = '', }, 
        },
        [20000880] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000880, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265367, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902244352'),['Voice'] = '', }, 
        },
        [20000881] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250461, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902244608'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250460, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902244864'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250461, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902245120'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250460, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902245376'),['Voice'] = '', }, {['Delay'] = 16, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250460, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902245632'),['Voice'] = '', }, {['Delay'] = 20, ['Duration'] = 4, ['ID'] = 20000881, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250461, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902245888'),['Voice'] = '', }, 
        },
        [20000883] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000883, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250457, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902246144'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000883, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250457, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902246400'),['Voice'] = '', }, 
        },
        [20000884] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000884, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250462, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902246656'),['Voice'] = '', }, 
        },
        [20000885] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000885, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250457, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902246912'),['Voice'] = '', }, 
        },
        [20000886] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000886, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250459, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902247168'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000886, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250459, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902247424'),['Voice'] = '', }, 
        },
        [20000887] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000887, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250465, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902247680'),['Voice'] = '', }, 
        },
        [20000888] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000888, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255122, ['Text'] = Game.TableDataManager:GetLangStr('str_59169885593344'),['Voice'] = '', }, 
        },
        [20000889] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000889, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250468, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902248192'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000889, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250468, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902248448'),['Voice'] = '', }, 
        },
        [20000890] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000890, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250590, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902248704'),['Voice'] = '', }, 
        },
        [20000891] = {{['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000891, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250470, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902248960'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 3, ['ID'] = 20000891, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250470, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902249216'),['Voice'] = '', }, 
        },
        [20000892] = {{['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000892, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250591, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902249472'),['Voice'] = '', }, 
        },
        [20000893] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000893, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207083, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902249728'),['Voice'] = '', }, 
        },
        [20000894] = {{['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000894, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250592, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902249984'),['Voice'] = '', }, 
        },
        [20000895] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000895, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250374, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902250240'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000895, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250374, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902250496'),['Voice'] = '', }, {['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000895, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250374, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902250752'),['Voice'] = '', }, 
        },
        [20000896] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000896, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211067, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902251008'),['Voice'] = '', }, 
        },
        [20000897] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000897, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902251264'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 4, ['ID'] = 20000897, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902251520'),['Voice'] = '', }, 
        },
        [20000898] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000898, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902251776'),['Voice'] = '', }, {['Delay'] = 5, ['Duration'] = 4, ['ID'] = 20000898, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250472, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902252032'),['Voice'] = '', }, 
        },
        [20000899] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000899, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211014, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920335872'),['Voice'] = '', }, 
        },
        [20000900] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000900, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211084, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920409088'),['Voice'] = '', }, 
        },
        [20000901] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000901, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265329, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902252800'),['Voice'] = '', }, 
        },
        [20000902] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000902, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265331, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078809344'),['Voice'] = '', }, 
        },
        [20000903] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000903, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265343, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902253312'),['Voice'] = '', }, 
        },
        [20000904] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000904, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265340, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902253568'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000904, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265340, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902253824'),['Voice'] = '', }, 
        },
        [20000905] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000905, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265341, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902254080'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000905, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265341, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902254336'),['Voice'] = '', }, 
        },
        [20000906] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000906, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902254592'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000906, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7213055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902254848'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 4, ['ID'] = 20000906, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7213055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902255104'),['Voice'] = '', }, 
        },
        [20000907] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000907, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250498, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902255360'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000907, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250498, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902255616'),['Voice'] = '', }, 
        },
        [20000908] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000908, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250499, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902255872'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000908, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250499, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902256128'),['Voice'] = '', }, 
        },
        [20000909] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000909, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211102, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902256384'),['Voice'] = '', }, 
        },
        [20000910] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000910, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902256640'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000910, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7223055, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902256896'),['Voice'] = '', }, 
        },
        [20000911] = {{['Delay'] = 6, ['Duration'] = 4, ['ID'] = 20000911, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213076, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902257152'),['Voice'] = '', }, 
        },
        [20000912] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000912, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7213078, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902257408'),['Voice'] = '', }, 
        },
        [20000913] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000913, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211056, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902257664'),['Voice'] = '', }, 
        },
        [20000914] = {{['Delay'] = 9, ['Duration'] = 3, ['ID'] = 20000914, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211057, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902257920'),['Voice'] = '', }, 
        },
        [20000915] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000915, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7223054, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902258176'),['Voice'] = '', }, 
        },
        [20000916] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7207029, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902258432'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7207030, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902258688'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7207029, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902258944'),['Voice'] = '', }, {['Delay'] = 10.5, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7207030, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902259200'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 3, ['ID'] = 20000916, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7207029, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902259456'),['Voice'] = '', }, 
        },
        [20000917] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000917, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250512, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902259712'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000917, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250513, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902259968'),['Voice'] = '', }, 
        },
        [20000918] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000918, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250514, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902260224'),['Voice'] = '', }, 
        },
        [20000919] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000919, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250515, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902260480'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 5.5, ['ID'] = 20000919, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250516, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902260736'),['Voice'] = '', }, 
        },
        [20000920] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000920, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250517, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902260992'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000920, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250517, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902261248'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 4, ['ID'] = 20000920, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250517, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902261504'),['Voice'] = '', }, 
        },
        [20000921] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250518, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902261760'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250519, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902262016'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 5, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250520, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902262272'),['Voice'] = '', }, {['Delay'] = 20, ['Duration'] = 5, ['ID'] = 20000921, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250521, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902262528'),['Voice'] = '', }, 
        },
        [20000922] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250491, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902262784'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250493, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902263040'),['Voice'] = '', }, {['Delay'] = 7, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250495, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902263296'),['Voice'] = '', }, {['Delay'] = 10.5, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250494, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902263552'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 3, ['ID'] = 20000922, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250491, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902263808'),['Voice'] = '', }, 
        },
        [20000923] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000923, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250490, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902264064'),['Voice'] = '', }, 
        },
        [20000924] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000924, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250486, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902264320'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000924, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250486, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902264576'),['Voice'] = '', }, {['Delay'] = 7.5, ['Duration'] = 3, ['ID'] = 20000924, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250486, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902264832'),['Voice'] = '', }, 
        },
        [20000925] = {{['Delay'] = 5, ['Duration'] = 3, ['ID'] = 20000925, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250487, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902265088'),['Voice'] = '', }, {['Delay'] = 8.5, ['Duration'] = 3, ['ID'] = 20000925, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250487, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902265344'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 3, ['ID'] = 20000925, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250487, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902265600'),['Voice'] = '', }, 
        },
        [20000926] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000926, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250488, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902265856'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 3, ['ID'] = 20000926, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250488, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902266112'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000926, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250488, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902266368'),['Voice'] = '', }, 
        },
        [20000927] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000927, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250489, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902266624'),['Voice'] = '', }, 
        },
        [20000928] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000928, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242091, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902266880'),['Voice'] = '', }, 
        },
        [20000929] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000929, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7212086, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325638913'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000929, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7212086, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325639169'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000929, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242236, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325639425'),['Voice'] = '', }, 
        },
        [20000930] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000930, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250526, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902267904'),['Voice'] = '', }, 
        },
        [20000931] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000931, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250525, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902267904'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000931, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250524, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902268416'),['Voice'] = '', }, 
        },
        [20000932] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000932, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250543, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902268672'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000932, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250544, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902268928'),['Voice'] = '', }, 
        },
        [20000933] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000933, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211120, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902269184'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000933, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211120, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902269440'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000933, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7211120, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902269696'),['Voice'] = '', }, 
        },
        [20000934] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000934, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7231105, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902269952'),['Voice'] = '', }, 
        },
        [20000935] = {{['Delay'] = 2, ['Duration'] = 3, ['ID'] = 20000935, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242151, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902270208'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000935, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242151, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902270464'),['Voice'] = '', }, 
        },
        [20000936] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000936, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242155, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902270720'),['Voice'] = '', }, 
        },
        [20000937] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000937, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242180, ['Text'] = Game.TableDataManager:GetLangStr('str_38828920932608'),['Voice'] = '', }, 
        },
        [20000938] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902271232'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902271488'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242160, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902271744'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902272000'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000938, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242159, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902272256'),['Voice'] = '', }, 
        },
        [20000939] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000939, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242144, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902272512'),['Voice'] = '', }, 
        },
        [20000940] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000940, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242142, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902272768'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000940, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242142, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902273024'),['Voice'] = '', }, 
        },
        [20000941] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000941, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255128, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902273280'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000941, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255128, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902273536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000941, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255128, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902273792'),['Voice'] = '', }, 
        },
        [20000942] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000942, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250531, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902274048'),['Voice'] = '', }, 
        },
        [20000943] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000943, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250532, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902274304'),['Voice'] = '', }, 
        },
        [20000944] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000944, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250533, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902274560'),['Voice'] = '', }, 
        },
        [20000945] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000945, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250536, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902274816'),['Voice'] = '', }, 
        },
        [20000946] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000946, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250537, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902275072'),['Voice'] = '', }, {['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000946, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250537, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902275328'),['Voice'] = '', }, 
        },
        [20000947] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000947, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250538, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902275584'),['Voice'] = '', }, 
        },
        [20000948] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000948, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250546, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902275840'),['Voice'] = '', }, 
        },
        [20000949] = {{['Delay'] = 3, ['Duration'] = 3, ['ID'] = 20000949, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250547, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902276096'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 3, ['ID'] = 20000949, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250547, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902276352'),['Voice'] = '', }, 
        },
        [20000950] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000950, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255129, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902276608'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 4, ['ID'] = 20000950, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255129, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902276864'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000950, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255129, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902277120'),['Voice'] = '', }, 
        },
        [20000951] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255133, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902277376'),['Voice'] = '', }, {['Delay'] = 4.5, ['Duration'] = 3, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255134, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902277632'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255133, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902277888'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 4, ['ID'] = 20000951, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255134, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902278144'),['Voice'] = '', }, 
        },
        [20000952] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255144, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902278400'),['Voice'] = '', }, {['Delay'] = 9, ['Duration'] = 4, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255145, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902278656'),['Voice'] = '', }, {['Delay'] = 14, ['Duration'] = 4, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255144, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902278912'),['Voice'] = '', }, {['Delay'] = 19, ['Duration'] = 3, ['ID'] = 20000952, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255145, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902279168'),['Voice'] = '', }, 
        },
        [20000953] = {{['Delay'] = 0, ['Duration'] = 4, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902279424'),['Voice'] = '', }, {['Delay'] = 8, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902279680'),['Voice'] = '', }, {['Delay'] = 12, ['Duration'] = 4, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255139, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902279936'),['Voice'] = '', }, {['Delay'] = 17, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7255139, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902280192'),['Voice'] = '', }, {['Delay'] = 21, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902280448'),['Voice'] = '', }, {['Delay'] = 25, ['Duration'] = 3, ['ID'] = 20000953, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7255140, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902280704'),['Voice'] = '', }, 
        },
        [20000954] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000954, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255141, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902280960'),['Voice'] = '', }, 
        },
        [20000955] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000955, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255130, ['Text'] = Game.TableDataManager:GetLangStr('str_39309956661760'),['Voice'] = '', }, 
        },
        [20000956] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000956, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255142, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902281472'),['Voice'] = '', }, 
        },
        [20000957] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000957, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250496, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902281728'),['Voice'] = '', }, 
        },
        [20000958] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000958, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250194, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902281984'),['Voice'] = '', }, 
        },
        [20000959] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000959, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255135, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902282240'),['Voice'] = '', }, 
        },
        [20000960] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000960, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210820, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902282496'),['Voice'] = '', }, {['Delay'] = 3.5, ['Duration'] = 3, ['ID'] = 20000960, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7210820, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902282752'),['Voice'] = '', }, 
        },
        [20000961] = {{['Delay'] = 0, ['Duration'] = 5, ['ID'] = 20000961, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7255132, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902283008'),['Voice'] = '', }, {['Delay'] = 6, ['Duration'] = 4, ['ID'] = 20000961, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7255131, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902283264'),['Voice'] = '', }, {['Delay'] = 11, ['Duration'] = 4, ['ID'] = 20000961, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7255132, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902283520'),['Voice'] = '', }, 
        },
        [20000962] = {{['Delay'] = 0, ['Duration'] = 2, ['ID'] = 20000962, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250508, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902283776'),['Voice'] = '', }, 
        },
        [20000963] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000963, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242184, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902284032'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000963, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242184, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902284288'),['Voice'] = '', }, 
        },
        [20000964] = {{['Delay'] = 0, ['Duration'] = 3, ['ID'] = 20000964, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250508, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902284544'),['Voice'] = '', }, 
        },
        [20000965] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000965, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242156, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902284800'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000965, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242156, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902285056'),['Voice'] = '', }, 
        },
        [20000966] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000966, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242101, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902285312'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000966, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242100, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902285568'),['Voice'] = '', }, 
        },
        [20000967] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000967, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265326, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902285824'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000967, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265326, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902286080'),['Voice'] = '', }, 
        },
        [20000968] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000968, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7211127, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902286848'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000968, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7211128, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902287104'),['Voice'] = '', }, 
        },
        [20000969] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000969, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242240, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902287360'),['Voice'] = '', }, 
        },
        [20000970] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000970, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242241, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902287616'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000970, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242242, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902287872'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000970, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242242, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902288128'),['Voice'] = '', }, 
        },
        [20000971] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000971, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242243, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902288384'),['Voice'] = '', }, 
        },
        [20000972] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000972, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242244, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902288640'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000972, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242244, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902288896'),['Voice'] = '', }, 
        },
        [20000973] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000973, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242247, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902289152'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000973, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242247, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902289408'),['Voice'] = '', }, 
        },
        [20000974] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242251, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902289664'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242252, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902289920'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242253, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902290176'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000974, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242248, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902290432'),['Voice'] = '', }, 
        },
        [20000975] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000975, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242250, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902290688'),['Voice'] = '', }, 
        },
        [20000976] = {{['Delay'] = 1, ['Duration'] = 4, ['ID'] = 20000976, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242239, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902286336'),['Voice'] = '', }, {['Delay'] = 0, ['Duration'] = 0, ['ID'] = 20000976, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242239, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902286592'),['Voice'] = '', }, 
        },
        [20000977] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000977, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242223, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902290944'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000977, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242223, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902291200'),['Voice'] = '', }, 
        },
        [20000978] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000978, ['Kind'] = 4, ['Order'] = 1, ['TalkerID'] = 7227002, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902291456'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000978, ['Kind'] = 4, ['Order'] = 2, ['TalkerID'] = 7227001, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902291712'),['Voice'] = '', }, 
        },
        [20000980] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242016, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902291968'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902292224'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902292480'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902292736'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7242016, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902292992'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000980, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7242017, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902293248'),['Voice'] = '', }, 
        },
        [20000981] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000981, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250556, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902293504'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000981, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250556, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902293760'),['Voice'] = '', }, 
        },
        [20000982] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000982, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250558, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902294016'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000982, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250558, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902294272'),['Voice'] = '', }, 
        },
        [20000983] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000983, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242111, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902294528'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000983, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242111, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902294784'),['Voice'] = '', }, 
        },
        [20000984] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000984, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210825, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902295040'),['Voice'] = '', }, 
        },
        [20000985] = {{['Delay'] = 0.5, ['Duration'] = 5, ['ID'] = 20000985, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250572, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902295296'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000985, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250569, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902295552'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000985, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250577, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902295808'),['Voice'] = '', }, 
        },
        [20000986] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000986, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7210825, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902296064'),['Voice'] = '', }, 
        },
        [20000987] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000987, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242179, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902296320'),['Voice'] = '', }, 
        },
        [20000988] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000988, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242148, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902296576'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000988, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242149, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902296832'),['Voice'] = '', }, 
        },
        [20000989] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000989, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242147, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902297088'),['Voice'] = '', }, 
        },
        [20000990] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000990, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242113, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902297344'),['Voice'] = '', }, 
        },
        [20000991] = {{['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20000991, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242209, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902297600'),['Voice'] = '', }, 
        },
        [20000992] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000992, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242212, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902297856'),['Voice'] = '', }, 
        },
        [20000993] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000993, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242211, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902298112'),['Voice'] = '', }, 
        },
        [20000994] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000994, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242205, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902298368'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000994, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242211, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902298624'),['Voice'] = '', }, 
        },
        [20000995] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000995, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7219012, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902298880'),['Voice'] = '', }, 
        },
        [20000996] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000996, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250597, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902299136'),['Voice'] = '', }, {['Delay'] = 4, ['Duration'] = 3, ['ID'] = 20000996, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250597, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902299392'),['Voice'] = '', }, 
        },
        [20000997] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000997, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250578, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902299648'),['Voice'] = '', }, 
        },
        [20000998] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20000998, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250594, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902299904'),['Voice'] = '', }, 
        },
        [20000999] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000999, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242163, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902300160'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20000999, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242162, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902300416'),['Voice'] = '', }, 
        },
        [20001000] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001000, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242164, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902300672'),['Voice'] = '', }, {['Delay'] = 1, ['Duration'] = 3, ['ID'] = 20001000, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242164, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902300928'),['Voice'] = '', }, 
        },
        [20001001] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265019, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078729216'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265020, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078729472'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7265019, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078729728'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7265020, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078729984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001001, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7265020, ['Text'] = Game.TableDataManager:GetLangStr('str_39035078730240'),['Voice'] = '', }, 
        },
        [20001002] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001002, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7265003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902302464'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001002, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7265003, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902302720'),['Voice'] = '', }, 
        },
        [20001003] = {{['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242224, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902302976'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7242225, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902303232'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7242226, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902303488'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001003, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7242227, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902303744'),['Voice'] = '', }, 
        },
        [20001004] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902304000'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902304256'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 3, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902304512'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 2, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 4, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_57862873201408'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 5, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902305024'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 6, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902305280'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 7, ['TalkerID'] = 7250586, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902305536'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001004, ['Kind'] = 2, ['Order'] = 8, ['TalkerID'] = 7250589, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902305792'),['Voice'] = '', }, 
        },
        [20001005] = {{['Delay'] = 0, ['Duration'] = 2.5, ['ID'] = 20001005, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7242260, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902306048'),['Voice'] = '', }, 
        },
        [20001006] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001006, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250641, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902306304'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001006, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250642, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902306560'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001006, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250642, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902306816'),['Voice'] = '', }, 
        },
        [20001007] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001007, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250658, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902307072'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001007, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250657, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902307328'),['Voice'] = '', }, 
        },
        [20001008] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001008, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250669, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902307584'),['Voice'] = '', }, 
        },
        [20001009] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001009, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250671, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902307840'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001009, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250672, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902308096'),['Voice'] = '', }, 
        },
        [20001010] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001010, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250673, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902308352'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001010, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250673, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902308608'),['Voice'] = '', }, 
        },
        [20001011] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001011, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250674, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902308864'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001011, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250675, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902309120'),['Voice'] = '', }, 
        },
        [20001012] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001012, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250676, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902309376'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001012, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250676, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902309632'),['Voice'] = '', }, 
        },
        [20001013] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001013, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250677, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902309888'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001013, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250678, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902310144'),['Voice'] = '', }, 
        },
        [20001014] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001014, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250679, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902310400'),['Voice'] = '', }, 
        },
        [20001015] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001015, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250680, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902310656'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001015, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250680, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902310912'),['Voice'] = '', }, 
        },
        [20001016] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001016, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250681, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902311168'),['Voice'] = '', }, 
        },
        [20001017] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001017, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250682, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902311424'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001017, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250682, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902311680'),['Voice'] = '', }, 
        },
        [20001018] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001018, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250683, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902311936'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001018, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250684, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902312192'),['Voice'] = '', }, 
        },
        [20001019] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001019, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250685, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902312448'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001019, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250686, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902312704'),['Voice'] = '', }, 
        },
        [20001020] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001020, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250687, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902312960'),['Voice'] = '', }, 
        },
        [20001021] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001021, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250688, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902313216'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001021, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250689, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902313472'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001021, ['Kind'] = 2, ['Order'] = 3, ['TalkerID'] = 7250688, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902313728'),['Voice'] = '', }, 
        },
        [20001022] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001022, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250691, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902313984'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001022, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250691, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902314240'),['Voice'] = '', }, 
        },
        [20001023] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001023, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250690, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902314496'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001023, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250690, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902314752'),['Voice'] = '', }, 
        },
        [20001024] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001024, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250692, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902315008'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001024, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250692, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902315264'),['Voice'] = '', }, 
        },
        [20001025] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001025, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250693, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902315520'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001025, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250694, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902315776'),['Voice'] = '', }, 
        },
        [20001026] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001026, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250695, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902316032'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001026, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250696, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902316288'),['Voice'] = '', }, 
        },
        [20001027] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001027, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250697, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902316544'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001027, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250698, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902316800'),['Voice'] = '', }, 
        },
        [20001028] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001028, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250699, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902317056'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001028, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250699, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902317312'),['Voice'] = '', }, 
        },
        [20001029] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001029, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250701, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902317568'),['Voice'] = '', }, 
        },
        [20001030] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001030, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250702, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902317824'),['Voice'] = '', }, 
        },
        [20001031] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001031, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250705, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902318080'),['Voice'] = '', }, {['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001031, ['Kind'] = 2, ['Order'] = 2, ['TalkerID'] = 7250705, ['Text'] = Game.TableDataManager:GetLangStr('str_27008902318336'),['Voice'] = '', }, 
        },
        [20001032] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001032, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250706, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325417217'),['Voice'] = '', }, 
        },
        [20001033] = {{['Delay'] = 0.5, ['Duration'] = 4, ['ID'] = 20001033, ['Kind'] = 2, ['Order'] = 1, ['TalkerID'] = 7250707, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325345537'),['Voice'] = '', }, 
        },
    }
}
return TopData