--
-- 表名: $Guild_公会.xlsx  页名：$ClubActivity_公会活动
--

local TopData = {
	data = {
		[1] = {
			["Id"] = 1,
			["Sort"] = 1,
			["ActivityID"] = 1013,
			["Scale"] = 2,
			["ActivityTimeDisplay"] = Game.TableDataManager:GetLangStr('str_27833267127808'),
			["info"] = 6427221,
			["JumpType"] = 2,
			["JumpPara"] = 5200021,
			["ButtonStatus"] = {1, 2},
			["ShowPicture"] = "/Game/Arts/UI_2/Resource/Guild_2/NotAtlas/UI_Guild_Img_Inset01.UI_Guild_Img_Inset01",
			["WordPicture"] = "/Game/Arts/UI_2/Resource/Guild_2/NotAtlas/UI_Guild_Img_Title_Txt01.UI_Guild_Img_Title_Txt01",
		},
		[2] = {
			["Id"] = 2,
			["Sort"] = 2,
			["ActivityID"] = 1105,
			["Scale"] = 2,
			["ActivityTimeDisplay"] = Game.TableDataManager:GetLangStr('str_27833267128064'),
			["info"] = 6427222,
			["JumpType"] = 1,
			["JumpPara"] = 1250063,
			["ButtonStatus"] = {1, 2},
			["ShowPicture"] = "/Game/Arts/UI_2/Resource/Guild_2/NotAtlas/UI_Guild_Img_Inset03.UI_Guild_Img_Inset03",
			["WordPicture"] = "/Game/Arts/UI_2/Resource/Guild_2/NotAtlas/UI_Guild_Img_Title_Txt02.UI_Guild_Img_Title_Txt02",
		},
	},
}

return TopData
