--
-- 表名: $ExplorationProgress_探索度表.xlsx  页名：$FirstLevelArea
--

local TopData = {
	data = {
		[1001] = {
			["ID"] = 1001,
			["Name"] = Game.TableDataManager:GetLangStr('str_34983850824960'),
			["ExploreTypeList"] = {"MAIN_EVENT", "CITY_EVENT", "DOUNGEN", "SUB_PUZZLE", "COLLECTION", "FENGWU_TINGGEN"},
			["RewardExplore"] = {125, 250, 375, 500, 625},
			["MapID"] = 3,
			["OrderID"] = 1,
		},
		[1002] = {
			["ID"] = 1002,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574642604544'),
			["ExploreTypeList"] = {"BOX", "SOUL_DARK", "SOUL_STORM", "PUZZ<PERSON>", "STELE", "EVENT", "LANDMARK", "LOSECONTROL"},
			["RewardExplore"] = {125, 250, 375, 500, 625},
			["MapID"] = 32,
			["OrderID"] = 2,
		},
	},
}

return TopData
