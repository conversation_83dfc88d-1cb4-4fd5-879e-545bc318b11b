--
-- 表名: $GuildLeague_公会联赛表.xlsx  页名：$ConstInt_整型常量表
--

local TopData = {
	data = {
		[10007] = {
			["ID"] = 10007,
			["Const"] = "MULTI_PVP_CAMP_A_ID",
			["Value"] = 1400007,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925024256'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193459712'),
		},
		[10008] = {
			["ID"] = 10008,
			["Const"] = "MULTI_PVP_CAMP_B_ID",
			["Value"] = 1400008,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925024512'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193459712'),
		},
		[10013] = {
			["ID"] = 10013,
			["Const"] = "GUILD_BATTLEFIELD_RESOURCE_ITEM",
			["Value"] = 2000289,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925024768'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10014] = {
			["ID"] = 10014,
			["Const"] = "GUILD_LEAGUE_INIT_RESOURCE",
			["Value"] = 500,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925025024'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10015] = {
			["ID"] = 10015,
			["Const"] = "GUILD_LEAGUE_RESOURCE_MAX_LIMIT",
			["Value"] = 10000,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925025280'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10016] = {
			["ID"] = 10016,
			["Const"] = "GUILD_LEAGUE_RESOURCE_DROP_DESTROY_TIME",
			["Value"] = 300,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925025536'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10017] = {
			["ID"] = 10017,
			["Const"] = "GUILD_LEAGUE_RESOURCE_STOP_REFRESH_TIME",
			["Value"] = 900,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925025792'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10018] = {
			["ID"] = 10018,
			["Const"] = "GUILD_LEAGUE_KILL_MONSTER_RESOURCE",
			["Value"] = 100,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925026048'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10019] = {
			["ID"] = 10019,
			["Const"] = "GUILD_LEAGUE_AVATAR_TAKE_LIMIT",
			["Value"] = 500,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925026304'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10020] = {
			["ID"] = 10020,
			["Const"] = "GUILD_LEAGUE_TOWER_DIFF_FOR_EXTRA_RESOURCE",
			["Value"] = 3,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925026560'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10021] = {
			["ID"] = 10021,
			["Const"] = "GUILD_LEAGUE_TOWER_EXTRA_RESOURCE",
			["Value"] = 1000,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925026816'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10022] = {
			["ID"] = 10022,
			["Const"] = "GUILD_LEAGUE_PREPARE_TIME",
			["Value"] = 120,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925027072'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10023] = {
			["ID"] = 10023,
			["Const"] = "GUILD_LEAGUE_TOTAL_TIME",
			["Value"] = 1500,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925027328'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10024] = {
			["ID"] = 10024,
			["Const"] = "GUILD_LEAGUE_CAN_KEEP_TIME",
			["Value"] = 180,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925027584'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10025] = {
			["ID"] = 10025,
			["Const"] = "GUILD_LEAGUE_MAX_PLAYER_NUM",
			["Value"] = 150,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925027840'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10026] = {
			["ID"] = 10026,
			["Const"] = "GUILD_LEAGUE_TOWER_BUFF",
			["Value"] = 84001301,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925028096'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10027] = {
			["ID"] = 10027,
			["Const"] = "GUILD_LEAGUE_TOWER_AOI_RADIUS",
			["Value"] = 3000,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925028352'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10028] = {
			["ID"] = 10028,
			["Const"] = "GUILD_LEAGUE_TELEPORTPOINT_INSTANCEID_CAMP_A",
			["Value"] = 1548860495,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925028608'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10029] = {
			["ID"] = 10029,
			["Const"] = "GUILD_LEAGUE_TELEPORTPOINT_INSTANCEID_CAMP_B",
			["Value"] = 2485231767,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925028864'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10030] = {
			["ID"] = 10030,
			["Const"] = "GUILD_LEAGUE_SUPPORT_MONSTER_CAMP_A",
			["Value"] = 7100034,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925029120'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10031] = {
			["ID"] = 10031,
			["Const"] = "GUILD_LEAGUE_SUPPORT_MONSTER_CAMP_B",
			["Value"] = 7100035,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925029376'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10032] = {
			["ID"] = 10032,
			["Const"] = "GUILD_LEAGUE_SUCCESS_OCCUPY_GAIN_RESOURCE",
			["Value"] = 600,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925029632'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10033] = {
			["ID"] = 10033,
			["Const"] = "GUILD_LEAGUE_ACTIVE_OCCUPY_AREA_A_POINT",
			["Value"] = 150,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925029888'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10034] = {
			["ID"] = 10034,
			["Const"] = "GUILD_LEAGUE_ACTIVE_OCCUPY_AREA_B_POINT",
			["Value"] = 200,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925030144'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10035] = {
			["ID"] = 10035,
			["Const"] = "GUILD_LEAGUE_SUMMON_MONSTER_REQUIRE_POINT",
			["Value"] = 300,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925030400'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10036] = {
			["ID"] = 10036,
			["Const"] = "GUILD_LEAGUE_TESTING_RESPAWN_POINT",
			["Value"] = 3453746067,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925030656'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10037] = {
			["ID"] = 10037,
			["Const"] = "GUILD_LEAGUE_SUMMON_MONSTER_MIN_HP",
			["Value"] = 20,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925030912'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10038] = {
			["ID"] = 10038,
			["Const"] = "GUILD_LEAGUE_OCCUPY_AREA_START_TIME",
			["Value"] = 480,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925031168'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10039] = {
			["ID"] = 10039,
			["Const"] = "GUILD_LEAGUE_OCCUPY_AREA_OPEN_COUNT_DOWN",
			["Value"] = 30,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925031424'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10040] = {
			["ID"] = 10040,
			["Const"] = "GUILD_LEAGUE_OCCUPY_AREA_OPEN_CALCULATE_FORMULA",
			["Value"] = 1612326,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925031680'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10041] = {
			["ID"] = 10041,
			["Const"] = "GUILD_LEAGUE_BACK_TO_FIGHT_TIME_CALCULATE_FORMULA",
			["Value"] = 1612325,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925031936'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10042] = {
			["ID"] = 10042,
			["Const"] = "GUILD_LEAGUE_ENTRANCE_NPC_ID",
			["Value"] = 7290001,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925032192'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10043] = {
			["ID"] = 10043,
			["Const"] = "GUILD_LEAGUE_ASSIST_CALCULATE_TIME",
			["Value"] = 30,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925032448'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10044] = {
			["ID"] = 10044,
			["Const"] = "GUILD_LEAGUE_PLAYER_SUMMARIZE_NUM",
			["Value"] = 5,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925032704'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10045] = {
			["ID"] = 10045,
			["Const"] = "GUILD_LEAGUE_PLAYER_SUMMARIZE_DISTANCE",
			["Value"] = 2500,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925032960'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10046] = {
			["ID"] = 10046,
			["Const"] = "GUILD_LEAGUE_LOSE_OBTAIN_WIN_REWARD_TIME_REQUIRE",
			["Value"] = 900,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925033216'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10047] = {
			["ID"] = 10047,
			["Const"] = "GUILD_LEAGUE_START_NO_QUALIFICATION_BID_MAIL_ID",
			["Value"] = 6260032,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925033472'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10048] = {
			["ID"] = 10048,
			["Const"] = "GUILD_LEAGUE_START_HAVE_QUALIFICATION_BID_MAIL_ID",
			["Value"] = 6260033,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925033728'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10049] = {
			["ID"] = 10049,
			["Const"] = "GUILD_LEAGUE_JOIN_LIVEVALUE_THRESHOLD",
			["Value"] = 0,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925033984'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10050] = {
			["ID"] = 10050,
			["Const"] = "GUILD_LEAGUE_HIGH_ZHANLI_THRESHOLD",
			["Value"] = 0,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925034240'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10051] = {
			["ID"] = 10051,
			["Const"] = "GUILD_LEAGUE_MAX_FIX_GROUP_COUNT",
			["Value"] = 10,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925034496'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10052] = {
			["ID"] = 10052,
			["Const"] = "GUILD_LEAGUE_MAX_GUILD_COUNT_PER_ZONE",
			["Value"] = 400,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925034752'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10053] = {
			["ID"] = 10053,
			["Const"] = "GUILD_LEAGUE_ENTER_GUILD_TIME_LIMIT",
			["Value"] = 0,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925035008'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10054] = {
			["ID"] = 10054,
			["Const"] = "GUILD_LEAGUE_ESTABLISH_GUILD_TIME_LIMIT",
			["Value"] = 0,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925035264'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10055] = {
			["ID"] = 10055,
			["Const"] = "GUILD_LEAGUE_SCHOOL_LIMIT_NUM",
			["Value"] = 50,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925035520'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10056] = {
			["ID"] = 10056,
			["Const"] = "GUILD_LEAGUE_START_BID_INTERVAL",
			["Value"] = 180,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925035776'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10057] = {
			["ID"] = 10057,
			["Const"] = "GUILD_LEAGUE_CAMP_A_RESPAWN_POINT",
			["Value"] = 561092123,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925036032'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10058] = {
			["ID"] = 10058,
			["Const"] = "GUILD_LEAGUE_CAMP_B_RESPAWN_POINT",
			["Value"] = 172436250,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925036288'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10059] = {
			["ID"] = 10059,
			["Const"] = "GUILD_LEAGUE_GET_REWARD_MIN_JOIN_TIME",
			["Value"] = 180,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925036544'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10060] = {
			["ID"] = 10060,
			["Const"] = "GUILD_LEAGUE_BASIC_RESOURCE_CALCULATE_PER_SECOND",
			["Value"] = 15,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925036800'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10061] = {
			["ID"] = 10061,
			["Const"] = "GUILD_LEAGUE_BASIC_RESOURCE_CALCULATE_ADD_NUM",
			["Value"] = 30,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925037056'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10062] = {
			["ID"] = 10062,
			["Const"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_MAX_OPEN_ROUND",
			["Value"] = 3,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925037312'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10063] = {
			["ID"] = 10063,
			["Const"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_OPEN_INTERVAL",
			["Value"] = 360,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925037568'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10064] = {
			["ID"] = 10064,
			["Const"] = "GUILD_LEAGUE_BEFORE_OCCUPY_DETECT_AREA_ACTIVE_TIME",
			["Value"] = 33,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925037824'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10065] = {
			["ID"] = 10065,
			["Const"] = "MAX_GUILD_COMMANDER_NUM",
			["Value"] = 5,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925038080'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193473536'),
		},
		[10066] = {
			["ID"] = 10066,
			["Const"] = "COMMAND_SYSTEM_MAX_GROUP_NUM",
			["Value"] = 5,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925038336'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193473536'),
		},
		[10067] = {
			["ID"] = 10067,
			["Const"] = "GUILD_LEAGUE_JOIN_PLAYER_LEVEL_LIMIT",
			["Value"] = 30,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925038592'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10068] = {
			["ID"] = 10068,
			["Const"] = "GUILD_LEAGUE_MONSTER_SKILL_ID",
			["Value"] = 80009140,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925038848'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10069] = {
			["ID"] = 10069,
			["Const"] = "GUILD_LEAGUE_OCCUPY_AREA_ATTACK_TIME",
			["Value"] = 5,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925039104'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10070] = {
			["ID"] = 10070,
			["Const"] = "GUILD_LEAGUE_OCCUPY_AREA_ATTACK_INTERVAL",
			["Value"] = 5,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925039360'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10071] = {
			["ID"] = 10071,
			["Const"] = "GUILD_LEAGUE_TAG_NAME_MAX_COUNT",
			["Value"] = 4,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925039616'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193473536'),
		},
		[10072] = {
			["ID"] = 10072,
			["Const"] = "GUILD_LEAGUE_MAX_GROUP_NAME_LENGTH",
			["Value"] = 3,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925039872'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193473536'),
		},
		[10073] = {
			["ID"] = 10073,
			["Const"] = "GUILD_COMMAND_BEAM_ZOOM",
			["Value"] = 3,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925040128'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193473536'),
		},
		[10074] = {
			["ID"] = 10074,
			["Const"] = "GUILD_COMMAND_BEAM_BASE_MAX_HEIGHT",
			["Value"] = 2500,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925040384'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193473536'),
		},
		[10075] = {
			["ID"] = 10075,
			["Const"] = "GUILD_COMMAND_BEAM_BASE_MIN_HEIGHT",
			["Value"] = 400,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925040640'),
			["Type"] = Game.TableDataManager:GetLangStr('str_28520193473536'),
		},
		[10076] = {
			["ID"] = 10076,
			["Const"] = "GUILD_LEAGUE_TIMED_RESOURCE_VALUE",
			["Value"] = 1000,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925040896'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10077] = {
			["ID"] = 10077,
			["Const"] = "GUILD_LEAGUE_TIMED_RESOURCE_INTERVAL_MIN",
			["Value"] = 1,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925041152'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10078] = {
			["ID"] = 10078,
			["Const"] = "GUILD_LEAGUE_VEHICLE_POSITION_OFFSET",
			["Value"] = 10,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925041408'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10079] = {
			["ID"] = 10079,
			["Const"] = "GUILD_LEAGUE_CAMP1_VEHICLE_INSTANCEID",
			["Value"] = 5860009,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925041664'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10080] = {
			["ID"] = 10080,
			["Const"] = "GUILD_LEAGUE_CAMP2_VEHICLE_INSTANCEID",
			["Value"] = 5860010,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925041920'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10081] = {
			["ID"] = 10081,
			["Const"] = "MAX_NUMBER_RESERVED_SWSSIONS",
			["Value"] = 3,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_28519925042176'),
			["Type"] = Game.TableDataManager:GetLangStr('str_1031865896704'),
		},
		[10082] = {
			["ID"] = 10082,
			["Const"] = "",
			["Value"] = 0,
			["EnumDesc"] = "",
			["Type"] = "",
		},
	},
}

return TopData
