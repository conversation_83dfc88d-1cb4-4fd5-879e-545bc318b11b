--
-- 表名: 家家园升级表后处理
--

local TopData = {
    data = {
        [1] = {
            ['ID'] = 1, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 3600, 3600}, 
            ['buildFloorLimit'] = 3, 
            ['getLimit'] = 30000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 700, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324673'),Game.TableDataManager:GetLangStr('str_34636495324674'),Game.TableDataManager:GetLangStr('str_34636495324675'),Game.TableDataManager:GetLangStr('str_34636495324676'),}, 
            ['putScore'] = 1000, 
            ['roleLevelLimit'] = 45, 
            ['storageLimit'] = 25000, 
            ['totalScore'] = 2000, 
        },
        [2] = {
            ['ID'] = 2, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 5400, 5400}, 
            ['buildFloorLimit'] = 4, 
            ['getLimit'] = 60000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 2100, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 1200, 
            ['roleLevelLimit'] = 50, 
            ['storageLimit'] = 50000, 
            ['totalScore'] = 2500, 
            ['workShopLevel'] = {{10, 10}, {11, 12}, }, 
        },
        [3] = {
            ['ID'] = 3, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 5, 
            ['getLimit'] = 100000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 5700, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 1400, 
            ['roleLevelLimit'] = 55, 
            ['storageLimit'] = 75000, 
            ['totalScore'] = 3000, 
        },
        [4] = {
            ['ID'] = 4, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 6, 
            ['getLimit'] = 130000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 11500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 1600, 
            ['roleLevelLimit'] = 60, 
            ['storageLimit'] = 100000, 
            ['totalScore'] = 3500, 
        },
        [5] = {
            ['ID'] = 5, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 180000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 27000, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 1800, 
            ['roleLevelLimit'] = 65, 
            ['storageLimit'] = 150000, 
            ['totalScore'] = 4000, 
        },
        [6] = {
            ['ID'] = 6, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 230000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 49500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 2000, 
            ['roleLevelLimit'] = 70, 
            ['storageLimit'] = 200000, 
            ['totalScore'] = 4500, 
        },
        [7] = {
            ['ID'] = 7, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 280000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 83500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 2200, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 250000, 
            ['totalScore'] = 5000, 
        },
        [8] = {
            ['ID'] = 8, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 340000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 128500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 2400, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 300000, 
            ['totalScore'] = 5500, 
        },
        [9] = {
            ['ID'] = 9, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 400000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 171500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 2600, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 350000, 
            ['totalScore'] = 6000, 
        },
        [10] = {
            ['ID'] = 10, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 470000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 228500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 2800, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 400000, 
            ['totalScore'] = 6500, 
        },
        [11] = {
            ['ID'] = 11, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 540000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 337500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 3000, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 450000, 
            ['totalScore'] = 6500, 
        },
        [12] = {
            ['ID'] = 12, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 620000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 438500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 3200, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 500000, 
            ['totalScore'] = 6500, 
        },
        [13] = {
            ['ID'] = 13, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 700000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 550000, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 3400, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 600000, 
            ['totalScore'] = 6500, 
        },
        [14] = {
            ['ID'] = 14, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 800000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 628500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 3600, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 700000, 
            ['totalScore'] = 6500, 
        },
        [15] = {
            ['ID'] = 15, 
            ['buildAreaBlock'] = {2, 3, 4}, 
            ['buildAreaLocation'] = {{-590, -12010, 3550}, 6000, 6000}, 
            ['buildFloorLimit'] = 7, 
            ['getLimit'] = 900000, 
            ['levelUpConditionText'] = {Game.TableDataManager:GetLangStr('str_34636226889217'),}, 
            ['levelUpConsume'] = 628500, 
            ['levelUpEffectText'] = {Game.TableDataManager:GetLangStr('str_34636495324929'),Game.TableDataManager:GetLangStr('str_34636495324930'),Game.TableDataManager:GetLangStr('str_34636495324931'),}, 
            ['putScore'] = 3800, 
            ['roleLevelLimit'] = 75, 
            ['storageLimit'] = 800000, 
            ['totalScore'] = 6500, 
        },
    }
}
return TopData