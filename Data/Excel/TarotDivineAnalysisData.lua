--
-- 表名: $TarotDivine_塔罗占卜.xlsx  页名：$TarotAnalysis_塔罗解析
--

local TopData = {
	data = {
		[1] = {
			["Key"] = 1,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861987328'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130422784'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398858240'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667293696'),
		},
		[2] = {
			["Key"] = 2,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861987584'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130423040'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398858496'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667293952'),
		},
		[3] = {
			["Key"] = 3,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861987840'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130423296'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398858752'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667294208'),
		},
		[4] = {
			["Key"] = 4,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861988096'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130423552'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398859008'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667294464'),
		},
		[5] = {
			["Key"] = 5,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861988352'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130423808'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398859264'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667294720'),
		},
		[6] = {
			["Key"] = 6,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861988608'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130424064'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398859520'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667294976'),
		},
		[7] = {
			["Key"] = 7,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861988864'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130424320'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398859776'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667295232'),
		},
		[8] = {
			["Key"] = 8,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861989120'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130424576'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398860032'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667295488'),
		},
		[9] = {
			["Key"] = 9,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861989376'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130424832'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398860288'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667295744'),
		},
		[10] = {
			["Key"] = 10,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861989632'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130425088'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398860544'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667296000'),
		},
		[11] = {
			["Key"] = 11,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861989888'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130425344'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398860800'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667296256'),
		},
		[12] = {
			["Key"] = 12,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861990144'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130425600'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398861056'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667296512'),
		},
		[13] = {
			["Key"] = 13,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861990400'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130425856'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398861312'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667296768'),
		},
		[14] = {
			["Key"] = 14,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861990656'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130426112'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398861568'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667297024'),
		},
		[15] = {
			["Key"] = 15,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861990912'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130426368'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398861824'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667297280'),
		},
		[16] = {
			["Key"] = 16,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861991168'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130426624'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398862080'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667297536'),
		},
		[17] = {
			["Key"] = 17,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861991424'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130426880'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398862336'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667297792'),
		},
		[18] = {
			["Key"] = 18,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861991680'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130427136'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398862592'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667298048'),
		},
		[19] = {
			["Key"] = 19,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861991936'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130427392'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398862848'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667298304'),
		},
		[20] = {
			["Key"] = 20,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861992192'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130427648'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398863104'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667298560'),
		},
		[21] = {
			["Key"] = 21,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861992448'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130427904'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398863360'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667298816'),
		},
		[22] = {
			["Key"] = 22,
			["UpTitle"] = Game.TableDataManager:GetLangStr('str_55594861992704'),
			["UpAnalysis"] = Game.TableDataManager:GetLangStr('str_55595130428160'),
			["DownTitle"] = Game.TableDataManager:GetLangStr('str_55595398863616'),
			["DownAnalysis"] = Game.TableDataManager:GetLangStr('str_55595667299072'),
		},
	},
}

return TopData
