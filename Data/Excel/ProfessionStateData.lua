--
-- 表名: $RoleSkill_技能管理.xlsx  页名：$ProfessionState_形态
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["ProfessionID"] = 1200006,
			["StateType"] = "TANK",
			["StateTypeName"] = Game.TableDataManager:GetLangStr('str_54632789318144'),
			["StateIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SkillForm/Warrior/UI_Character_Icon_SkillForm_Warrior02.UI_Character_Icon_SkillForm_Warrior02",
			["IsDefault"] = true,
			["SwitchSkillPose"] = 86065020,
			["StateWeapon"] = "",
			["StatePet"] = "",
		},
		[2] = {
			["ID"] = 2,
			["ProfessionID"] = 1200006,
			["StateType"] = "DPS",
			["StateTypeName"] = Game.TableDataManager:GetLangStr('str_48105244329728'),
			["StateIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SkillForm/Warrior/UI_Character_Icon_SkillForm_Warrior01.UI_Character_Icon_SkillForm_Warrior01",
			["IsDefault"] = false,
			["SwitchSkillPose"] = 86065030,
			["StateWeapon"] = "",
			["StatePet"] = "",
		},
		[3] = {
			["ID"] = 3,
			["ProfessionID"] = 1200002,
			["StateType"] = "HEAL",
			["StateTypeName"] = Game.TableDataManager:GetLangStr('str_54632789318912'),
			["StateIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SkillForm/Visionary/UI_Character_Icon_SkillForm_Visionary02.UI_Character_Icon_SkillForm_Visionary02",
			["IsDefault"] = true,
			["SwitchSkillPose"] = 86025030,
			["StateWeapon"] = "Weapon_3001002",
			["StatePet"] = "NPC_7102015",
		},
		[4] = {
			["ID"] = 4,
			["ProfessionID"] = 1200002,
			["StateType"] = "DPS",
			["StateTypeName"] = Game.TableDataManager:GetLangStr('str_48105244329728'),
			["StateIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SkillForm/Visionary/UI_Character_Icon_SkillForm_Visionary01.UI_Character_Icon_SkillForm_Visionary01",
			["IsDefault"] = false,
			["SwitchSkillPose"] = 86025040,
			["StateWeapon"] = "Weapon_3001003",
			["StatePet"] = "NPC_7102016",
		},
	},
}

return TopData
