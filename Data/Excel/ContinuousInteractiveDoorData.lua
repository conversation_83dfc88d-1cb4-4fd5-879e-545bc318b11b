--
-- 表名: $SceneActor.xlsx  页名：$Door
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 0,
			["InitialSubState"] = 0,
			["BPName"] = "BP_DragonBarTrapDoor",
			["Duration"] = 1,
			["BoxExtent"] = {50, 150, 200},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 500,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "/Game/Arts/SceneActorBP/Door/DragonBarDoorOpenCurve.DragonBarDoorOpenCurve",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "/Game/Arts/SceneActorBP/Door/DragonBarDoorCloseCurve.DragonBarDoorCloseCurve",
			["Audio"] = "Play_Plot_Common_3D_DoorOpen",
		},
		[2] = {
			["ID"] = 2,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 0,
			["InitialSubState"] = 1,
			["BPName"] = "BP_OpenDragonBarTrapDoor",
			["Duration"] = 1,
			["BoxExtent"] = {50, 150, 200},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 500,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "/Game/Arts/SceneActorBP/Door/DragonBarDoorOpenCurve.DragonBarDoorOpenCurve",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "/Game/Arts/SceneActorBP/Door/DragonBarDoorCloseCurve.DragonBarDoorCloseCurve",
			["Audio"] = "Play_Plot_Common_3D_DoorOpen",
		},
		[3] = {
			["ID"] = 3,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 0,
			["BPName"] = "BP_BlackThorns_Door",
			["Duration"] = 1,
			["BoxExtent"] = {85, 10, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "",
			["bInwardOpen"] = true,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 90,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_3D_DoorOpen",
		},
		[4] = {
			["ID"] = 4,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 0,
			["BPName"] = "BP_Ferlanqi_Door",
			["Duration"] = 1,
			["BoxExtent"] = {120, 15, 195},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_3D_DoorOpen",
		},
		[5] = {
			["ID"] = 5,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 0,
			["BPName"] = "BP_Ferlanqi_Door",
			["Duration"] = 1,
			["BoxExtent"] = {120, 15, 195},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_3D_DoorOpen",
		},
		[6] = {
			["ID"] = 6,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Queen_Shop005_Door_B_Right",
			["Duration"] = 1,
			["BoxExtent"] = {80, 8, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_4D_DoorOpen",
		},
		[7] = {
			["ID"] = 7,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Queen_Shop006_Door_D_Right",
			["Duration"] = 1,
			["BoxExtent"] = {80, 10, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 0,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = -90,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_5D_DoorOpen",
		},
		[8] = {
			["ID"] = 8,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Queen_Shop007_Door_B_Right",
			["Duration"] = 1,
			["BoxExtent"] = {50, 5, 140},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_6D_DoorOpen",
		},
		[9] = {
			["ID"] = 9,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Restaurant001_Corner001_Door001_Right",
			["Duration"] = 1,
			["BoxExtent"] = {115, 8, 250},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_7D_DoorOpen",
		},
		[10] = {
			["ID"] = 10,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Bookstore_Door001_Right",
			["Duration"] = 1,
			["BoxExtent"] = {70, 8, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_8D_DoorOpen",
		},
		[11] = {
			["ID"] = 11,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Hostel_Door001_Right",
			["Duration"] = 1,
			["BoxExtent"] = {30, 8, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_9D_DoorOpen",
		},
		[12] = {
			["ID"] = 12,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_ShuiXian_Villa_Door001A_Left",
			["Duration"] = 1,
			["BoxExtent"] = {45, 10, 140},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_9D_DoorOpen",
		},
		[13] = {
			["ID"] = 13,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_ShuiXian_Villa_Door001A_Right",
			["Duration"] = 1,
			["BoxExtent"] = {45, 10, 140},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_9D_DoorOpen",
		},
		[14] = {
			["ID"] = 14,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Queen_Shop005_Door_B_Left",
			["Duration"] = 1,
			["BoxExtent"] = {80, 8, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_10D_DoorOpen",
		},
		[15] = {
			["ID"] = 15,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Queen_Shop006_Door_D_Left",
			["Duration"] = 1,
			["BoxExtent"] = {80, 10, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = 0,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 90,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_11D_DoorOpen",
		},
		[16] = {
			["ID"] = 16,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Queen_Shop007_Door_B_Left",
			["Duration"] = 1,
			["BoxExtent"] = {50, 5, 140},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_12D_DoorOpen",
		},
		[17] = {
			["ID"] = 17,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Backlund_Restaurant001_Corner001_Door001_Left",
			["Duration"] = 1,
			["BoxExtent"] = {115, 8, 250},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_13D_DoorOpen",
		},
		[18] = {
			["ID"] = 18,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Bookstore_Door001_Left",
			["Duration"] = 1,
			["BoxExtent"] = {70, 8, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_14D_DoorOpen",
		},
		[19] = {
			["ID"] = 19,
			["DoorBehavior"] = 29,
			["BelongType"] = 0,
			["InitialState"] = 1,
			["InitialSubState"] = 1,
			["BPName"] = "BP_Hostel_Door001_Left",
			["Duration"] = 1,
			["BoxExtent"] = {30, 8, 160},
			["CollisionRule"] = 0,
			["InteractorRadius"] = 300,
			["OpenUITemplateID"] = 1110,
			["OpenYaw"] = -90,
			["OpenCurve"] = "",
			["bInwardOpen"] = false,
			["OpenInwardUITemplateID"] = 1111,
			["OpenInwardYaw"] = 0,
			["OpenInwardCurve"] = "",
			["CloseUITemplateID"] = 1111,
			["CloseYaw"] = 0,
			["CloseCurve"] = "",
			["Audio"] = "Play_Plot_Common_15D_DoorOpen",
		},
	},
}

return TopData
