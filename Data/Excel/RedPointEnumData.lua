--
-- 表名: RedPointEnumData后处理
--

local TopData = {
    RedPointConfigToEnumMap = {
        ['AppearanceItem'] = 'REDPOINT_FASHION_ITEM', 
        ['DungeonItem'] = 'REDPOINT_DUNGEON_UNLOCK', 
        ['EquipBodyEnhanceSlotList'] = 'REDPOINT_EQUIP_BODY_ENHANCE_SLOT', 
        ['EquipInventoryList'] = 'REDPOINT_EQUIP_INVENTORY', 
        ['FellowAssistCombatList'] = 'REDPOINT_ASSIST_COMBAT', 
        ['FellowBuildList'] = 'REDPOINT_FELLOW_LIST', 
        ['FellowJoinCombatList'] = 'REDPOINT_JOIN_COMBAT', 
        ['RedPointRolePlayIdentity'] = 'REDPOINT_ROLEPLAY_IDENTITY', 
        ['SealedUnlockSlotList'] = 'REDPOINT_SEALED_SLOT', 
        ['SkillCustomizerBuildSkill'] = 'REDPOINT_SKILL_LIST', 
        ['SocialActionItemList'] = 'REDPOINT_SOCIALACTION_ITEM', 
    },
    data = {
        [10001] = {
            ['Const'] = 'REDPOINT_DUNGEON_UNLOCK', 
            ['ID'] = 10001, 
            ['Name'] = 'DungeonItem', 
            ['Unlock'] = 'MODULE_LOCK_DUNGEON', 
        },
        [20001] = {
            ['Const'] = 'REDPOINT_SKILL_LIST', 
            ['ID'] = 20001, 
            ['Name'] = 'SkillCustomizerBuildSkill', 
            ['Unlock'] = 'MODULE_LOCK_SKILL_CUSTOM', 
        },
        [30001] = {
            ['Const'] = 'REDPOINT_FELLOW_LIST', 
            ['ID'] = 30001, 
            ['Name'] = 'FellowBuildList', 
            ['Unlock'] = 'MODULE_LOCK_FELLOW', 
        },
        [30002] = {
            ['Const'] = 'REDPOINT_JOIN_COMBAT', 
            ['ID'] = 30002, 
            ['Name'] = 'FellowJoinCombatList', 
            ['Unlock'] = 'MODULE_LOCK_FELLOW', 
        },
        [30003] = {
            ['Const'] = 'REDPOINT_ASSIST_COMBAT', 
            ['ID'] = 30003, 
            ['Name'] = 'FellowAssistCombatList', 
            ['Unlock'] = 'MODULE_LOCK_FELLOW', 
        },
        [40001] = {
            ['Const'] = 'REDPOINT_SEALED_SLOT', 
            ['ID'] = 40001, 
            ['Name'] = 'SealedUnlockSlotList', 
            ['Unlock'] = 'MODULE_LOCK_SEALED', 
        },
        [50001] = {
            ['Const'] = 'REDPOINT_EQUIP_INVENTORY', 
            ['ID'] = 50001, 
            ['Name'] = 'EquipInventoryList', 
            ['Unlock'] = 'MODULE_LOCK_EQUIP', 
        },
        [50002] = {
            ['Const'] = 'REDPOINT_EQUIP_BODY_ENHANCE_SLOT', 
            ['ID'] = 50002, 
            ['Name'] = 'EquipBodyEnhanceSlotList', 
            ['Unlock'] = 'MODULE_LOCK_EQUIP', 
        },
        [60001] = {
            ['Const'] = 'REDPOINT_SOCIALACTION_ITEM', 
            ['ID'] = 60001, 
            ['Name'] = 'SocialActionItemList', 
            ['Unlock'] = 'MODULE_LOCK_SOCIAL_ACTION', 
        },
        [70001] = {
            ['Const'] = 'REDPOINT_FASHION_ITEM', 
            ['ID'] = 70001, 
            ['Name'] = 'AppearanceItem', 
            ['Unlock'] = 'MODULE_LOCK_FASHION_SHOP', 
        },
        [80001] = {
            ['Const'] = 'REDPOINT_ROLEPLAY_IDENTITY', 
            ['ID'] = 80001, 
            ['Name'] = 'RedPointRolePlayIdentity', 
            ['Unlock'] = 'MODULE_LOCK_ROLEPLAY', 
        },
    }
}
return TopData