--
-- 表名: $Mount_坐骑表.xlsx  页名：$AttachmentConfig_配饰挂接参数
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["SocketName"] = {"Bike_Bell"},
			["MountPosition"] = Game.TableDataManager:GetLangStr('str_37109859616256'),
			["ScaleDefault"] = 1,
			["ScaleMin"] = 0.6,
			["ScaleMax"] = 1.5,
			["LocationDefault"] = {0, 0, 0},
			["LocationMin"] = {-10, -10, -10},
			["LocationMax"] = {10, 10, 10},
			["RotationDefault"] = {0, 0, 0},
			["RotationMin"] = {-90, -90, -90},
			["RotationMax"] = {90, 90, 90},
		},
	},
}

return TopData
