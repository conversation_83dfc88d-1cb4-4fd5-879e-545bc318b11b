--
-- 表名: $Schedule.xlsx  页名：$ScheduleConst
--

local TopData = {
	data = {
		["SCHEDULE_DAILY_TASK_NUM"] = 8,
		["SCHEDULE_FATE_REVELATION_APPEAR_PROBABILITY"] = 3000,
		["SCHEDULE_TORCH_SHOP"] = 1250101,
		["TO_BE_COMPLETED"] = Game.TableDataManager:GetLangStr('str_51059644958976'),
		["AVAILABLE"] = Game.TableDataManager:GetLangStr('str_51059644959232'),
		["COMPLETED"] = Game.TableDataManager:GetLangStr('str_54632789674496'),
		["FULL_ACTIVITY"] = Game.TableDataManager:GetLangStr('str_51059644959744'),
		["REWARD_TIPS"] = Game.TableDataManager:GetLangStr('str_51059644960000'),
		["ACTIVITY_STARTTIME"] = Game.TableDataManager:GetLangStr('str_54632789313536'),
		["ACTIVITY_ENDTIME"] = Game.TableDataManager:GetLangStr('str_51059644960512'),
		["REWARD_END"] = Game.TableDataManager:GetLangStr('str_54632789722624'),
		["SCHEDULE_TITLE"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
		["GUIDE_DAILY_REWARD"] = Game.TableDataManager:GetLangStr('str_51059644961280'),
		["GUIDE_WEEKLY_REWARD"] = Game.TableDataManager:GetLangStr('str_51059644961536'),
		["TORCH_WEEKLY_REWARD"] = Game.TableDataManager:GetLangStr('str_51059644961792'),
		["TORCH_TOTAL"] = Game.TableDataManager:GetLangStr('str_51059644962048'),
		["TORCH_SHOP"] = Game.TableDataManager:GetLangStr('str_51059644962304'),
		["SCHEDULE_REWARD_TIME"] = {"DAY", 1, 1, 1},
	},
}

return TopData
