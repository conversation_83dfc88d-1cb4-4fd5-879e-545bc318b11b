--
-- 表名: StateConflictData后处理
--

local TopData = {
    StateConflictMap = {
        [2] = {[3] = 2, [9] = 2, [13] = 2, [23] = 2, [25] = 2, [33] = 2, [35] = 2, [39] = 2, [52] = 2, }, 
        [3] = {[3] = 2, [8] = 2, [9] = 2, [13] = 1, [21] = 1, [23] = 1, [25] = 2, [29] = 1, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [39] = 2, [40] = 1, [43] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [4] = {[3] = 1, [8] = 2, [9] = 2, [13] = 1, [35] = 2, [36] = 2, [37] = 2, [38] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [52] = 2, }, 
        [5] = {[3] = 2, [13] = 2, [21] = 1, [23] = 2, [25] = 2, [29] = 1, [33] = 2, [35] = 2, [36] = 2, [40] = 1, [43] = 1, [47] = 1, [52] = 2, }, 
        [6] = {[3] = 2, [8] = 1, [9] = 2, [13] = 2, [21] = 1, [23] = 2, [25] = 2, [29] = 1, [33] = 2, [35] = 2, [36] = 2, [40] = 1, [43] = 1, [47] = 1, [52] = 2, }, 
        [7] = {[3] = 2, [4] = 2, [8] = 1, [9] = 2, [13] = 2, [21] = 1, [23] = 2, [25] = 2, [29] = 1, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [40] = 1, [41] = 1, [43] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [8] = {[3] = 1, [6] = 1, [7] = 1, [8] = 1, [9] = 2, [13] = 1, [16] = 1, [21] = 1, [23] = 1, [25] = 1, [29] = 1, [33] = 1, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [9] = {[3] = 1, [4] = 1, [6] = 1, [7] = 1, [8] = 2, [9] = 2, [13] = 2, [16] = 1, [18] = 1, [20] = 1, [21] = 1, [23] = 2, [25] = 2, [29] = 1, [33] = 2, [34] = 1, [35] = 2, [36] = 2, [37] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [10] = {[10] = 1, [20] = 1, [30] = 1, [38] = 2, [39] = 2, [41] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [13] = {[3] = 2, [4] = 2, [5] = 2, [6] = 1, [7] = 1, [8] = 1, [9] = 2, [13] = 1, [16] = 1, [21] = 1, [23] = 2, [25] = 2, [29] = 1, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [38] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [16] = {[3] = 2, [4] = 2, [6] = 1, [9] = 1, [13] = 1, [16] = 2, [21] = 1, [23] = 2, [25] = 2, [31] = 2, [33] = 2, [34] = 2, [36] = 2, [37] = 2, [39] = 2, [41] = 1, [51] = 2, [52] = 2, }, 
        [18] = {[4] = 2, [21] = 1, [29] = 1, [35] = 2, [36] = 2, [37] = 2, [38] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [52] = 2, }, 
        [19] = {[3] = 2, [4] = 2, [6] = 1, [7] = 1, [8] = 1, [9] = 2, [21] = 1, [25] = 2, [29] = 1, [33] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [20] = {[9] = 2, [23] = 2, [33] = 2, [35] = 2, [36] = 2, [37] = 2, [41] = 1, [43] = 2, [52] = 2, }, 
        [21] = {[9] = 2, [23] = 2, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [39] = 2, [43] = 2, [51] = 2, [52] = 2, }, 
        [23] = {[3] = 2, [4] = 2, [6] = 1, [7] = 1, [8] = 1, [9] = 2, [20] = 1, [21] = 1, [25] = 1, [29] = 1, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [39] = 2, [40] = 1, [41] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [25] = {[3] = 2, [4] = 2, [6] = 1, [7] = 1, [8] = 1, [9] = 2, [21] = 1, [25] = 2, [29] = 1, [31] = 2, [33] = 2, [34] = 2, [36] = 2, [37] = 2, [38] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [26] = {[20] = 1, [21] = 1, [29] = 1, [39] = 2, [41] = 1, [43] = 1, }, 
        [29] = {[3] = 2, [4] = 2, [7] = 2, [8] = 2, [9] = 2, [18] = 2, [25] = 2, [31] = 2, [33] = 2, [34] = 2, [39] = 2, [41] = 1, [43] = 2, [51] = 2, [52] = 2, }, 
        [30] = {[10] = 1, [20] = 1, [21] = 1, [28] = 1, [29] = 1, [35] = 1, [38] = 2, [39] = 2, [41] = 1, [45] = 1, [47] = 1, }, 
        [31] = {[3] = 2, [6] = 1, [7] = 1, [9] = 2, [13] = 1, [16] = 1, [20] = 1, [21] = 1, [23] = 1, [25] = 2, [29] = 1, [33] = 2, [34] = 1, [35] = 2, [36] = 2, [37] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [51] = 1, [52] = 2, }, 
        [32] = {[3] = 1, [4] = 1, [13] = 1, [20] = 1, [21] = 1, [23] = 1, [29] = 1, [33] = 2, [35] = 2, [36] = 2, [37] = 2, [38] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 2, [45] = 1, [47] = 1, [52] = 2, }, 
        [33] = {[3] = 2, [4] = 2, [6] = 1, [7] = 1, [8] = 1, [9] = 1, [18] = 2, [20] = 1, [21] = 1, [25] = 2, [29] = 1, [31] = 2, [33] = 2, [34] = 1, [37] = 1, [38] = 1, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [34] = {[3] = 2, [7] = 1, [9] = 2, [13] = 1, [16] = 1, [20] = 1, [21] = 1, [23] = 1, [25] = 2, [29] = 1, [31] = 2, [33] = 2, [35] = 2, [36] = 2, [37] = 2, [39] = 2, [41] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [35] = {[3] = 2, [4] = 2, [5] = 2, [6] = 1, [7] = 1, [9] = 2, [13] = 1, [18] = 2, [20] = 1, [21] = 1, [23] = 2, [25] = 2, [30] = 1, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [38] = 1, [39] = 2, [41] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [36] = {[3] = 2, [4] = 2, [5] = 2, [6] = 1, [7] = 1, [9] = 1, [18] = 2, [21] = 1, [30] = 1, [36] = 2, [39] = 2, [41] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [37] = {[3] = 2, [4] = 2, [7] = 1, [9] = 1, [18] = 2, [21] = 1, [30] = 1, [37] = 2, [38] = 2, [39] = 2, [41] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [38] = {[4] = 2, [10] = 2, [30] = 1, [34] = 1, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [52] = 2, }, 
        [39] = {[3] = 2, [4] = 2, [9] = 2, [10] = 1, [13] = 1, [16] = 1, [18] = 1, [21] = 1, [23] = 2, [29] = 1, [30] = 1, [31] = 1, [33] = 2, [34] = 1, [35] = 1, [36] = 1, [37] = 1, [40] = 1, [41] = 1, [43] = 1, [51] = 1, [52] = 2, }, 
        [40] = {[3] = 2, [4] = 2, [5] = 2, [6] = 2, [7] = 2, [9] = 2, [18] = 2, [25] = 2, [33] = 2, [38] = 2, [39] = 2, [41] = 1, [52] = 2, }, 
        [41] = {[4] = 1, [8] = 1, [9] = 2, [10] = 2, [13] = 2, [16] = 2, [18] = 1, [21] = 1, [23] = 1, [25] = 2, [30] = 1, [31] = 2, [33] = 2, [34] = 2, [35] = 1, [36] = 1, [37] = 1, [38] = 2, [39] = 2, [40] = 1, [43] = 1, [51] = 2, [52] = 2, }, 
        [42] = {[21] = 1, [23] = 1, [25] = 1, [40] = 1, [43] = 1, [52] = 2, }, 
        [43] = {[3] = 2, [4] = 2, [5] = 2, [6] = 2, [7] = 1, [8] = 2, [9] = 2, [18] = 2, [21] = 1, [25] = 2, [29] = 1, [33] = 2, [38] = 2, [39] = 2, [40] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [44] = {[3] = 1, [18] = 1, [20] = 1, [21] = 1, [29] = 1, [38] = 1, [40] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [45] = {[9] = 2, [23] = 2, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [41] = 1, [43] = 2, [47] = 1, [51] = 2, [52] = 2, }, 
        [47] = {[9] = 2, [23] = 2, [31] = 2, [33] = 2, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [41] = 1, [43] = 2, [51] = 2, [52] = 2, }, 
        [48] = {[3] = 2, [4] = 2, [5] = 2, [6] = 2, [7] = 1, [8] = 1, [9] = 2, [13] = 2, [16] = 1, [18] = 2, [20] = 1, [21] = 1, [23] = 1, [25] = 1, [29] = 1, [31] = 2, [33] = 1, [34] = 2, [35] = 2, [36] = 2, [37] = 2, [38] = 1, [39] = 1, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [51] = 2, [52] = 2, }, 
        [49] = {[3] = 2, [6] = 1, [7] = 1, [9] = 2, [13] = 1, [16] = 1, [21] = 1, [23] = 1, [25] = 2, [29] = 1, [31] = 2, [33] = 2, [35] = 2, [36] = 2, [37] = 2, [38] = 1, [39] = 1, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [52] = 2, }, 
        [51] = {[3] = 2, [6] = 1, [7] = 1, [9] = 2, [13] = 1, [16] = 1, [20] = 1, [21] = 1, [23] = 1, [25] = 2, [29] = 1, [31] = 1, [33] = 2, [34] = 1, [35] = 2, [36] = 2, [37] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 1, [47] = 1, [51] = 1, [52] = 2, }, 
        [52] = {[3] = 2, [4] = 2, [5] = 2, [6] = 1, [7] = 1, [8] = 1, [9] = 2, [10] = 1, [13] = 2, [18] = 2, [21] = 1, [23] = 1, [25] = 1, [29] = 1, [30] = 1, [31] = 2, [33] = 1, [34] = 2, [35] = 1, [36] = 1, [37] = 1, [38] = 2, [39] = 2, [40] = 1, [41] = 1, [43] = 1, [45] = 2, [47] = 2, [51] = 2, }, 
    },
    StateConflictReminderData = {
        [10] = {[10] = 6405790, [20] = 6405822, }, 
        [26] = {[20] = 6400504, [21] = 6400510, [29] = 6400511, }, 
    },
    StateID2BlockMap = {
        [5] = {[21] = 1, [29] = 1, [40] = 1, [43] = 1, [47] = 1, }, 
        [6] = {[8] = 1, [21] = 1, [29] = 1, [40] = 1, [43] = 1, [47] = 1, }, 
        [7] = {[8] = 1, [21] = 1, [29] = 1, [40] = 1, [41] = 1, [43] = 1, [47] = 1, }, 
    },
    data = {
        [2] = {
            ['ActionEnum'] = 'Teleport', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338271744'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 0, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 2, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 0, 
            ['QuestControl'] = 0, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [3] = {
            ['ActionEnum'] = 'TeleportSpell', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618795008'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 2, 
            ['Follow'] = 0, 
            ['ID'] = 3, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 0, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [4] = {
            ['ActionEnum'] = 'Follow', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618795264'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 0, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 0, 
            ['FashionSystem'] = 2, 
            ['Follow'] = 0, 
            ['ID'] = 4, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 1, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [5] = {
            ['ActionEnum'] = 'Move', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338272512'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 5, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 0, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [6] = {
            ['ActionEnum'] = 'Jump', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618795776'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 0, 
            ['ID'] = 6, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 0, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [7] = {
            ['ActionEnum'] = 'CastCombatSkill', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618796032'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 7, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [8] = {
            ['ActionEnum'] = 'FashionSystem', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54426630884352'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 0, 
            ['ID'] = 8, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 1, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 1, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 1, 
        },
        [9] = {
            ['ActionEnum'] = 'SocialActionSystem', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618796544'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 2, 
            ['Follow'] = 1, 
            ['ID'] = 9, 
            ['InBattle'] = 1, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 1, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 0, 
            ['Navigate'] = 1, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 1, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [10] = {
            ['ActionEnum'] = 'Match', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54426630884864'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 0, 
            ['Dancing'] = 0, 
            ['DialogControl'] = 0, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 10, 
            ['InBattle'] = 6405822, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 6405790, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [13] = {
            ['ActionEnum'] = 'InVehicle', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618797056'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 13, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 2, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [16] = {
            ['ActionEnum'] = 'Morph', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618797312'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 0, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 16, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 2, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 1, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [18] = {
            ['ActionEnum'] = 'Navigate', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618797568'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 18, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [19] = {
            ['ActionEnum'] = 'Interact', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338274816'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 19, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [20] = {
            ['ActionEnum'] = 'InBattle', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618797824'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 2, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 20, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [21] = {
            ['ActionEnum'] = 'Die', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338275328'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 2, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 21, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 0, 
            ['QuestControl'] = 0, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [23] = {
            ['ActionEnum'] = 'ContinuousInteract', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338278912'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 23, 
            ['InBattle'] = 1, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 1, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [25] = {
            ['ActionEnum'] = 'InteractSpell', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618798848'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 25, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [26] = {
            ['ActionEnum'] = 'EquipChange', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338276608'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 6400511, 
            ['Dancing'] = 0, 
            ['DialogControl'] = 1, 
            ['Die'] = 6400510, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 26, 
            ['InBattle'] = 6400504, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 0, 
            ['UseItemSpell'] = 0, 
        },
        [29] = {
            ['ActionEnum'] = 'CutScene', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338277376'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 2, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 2, 
            ['Die'] = 0, 
            ['FashionSystem'] = 2, 
            ['Follow'] = 2, 
            ['ID'] = 29, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [30] = {
            ['ActionEnum'] = 'TarotTeamBuildingProcess', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618799616'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 0, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 30, 
            ['InBattle'] = 1, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 1, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 1, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 1, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 0, 
            ['UseItemSpell'] = 0, 
        },
        [31] = {
            ['ActionEnum'] = 'InMount', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618799872'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 31, 
            ['InBattle'] = 1, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 1, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 1, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [32] = {
            ['ActionEnum'] = 'EnterHome', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54426630888448'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 2, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 1, 
            ['ID'] = 32, 
            ['InBattle'] = 1, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 1, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [33] = {
            ['ActionEnum'] = 'UseItemSpell', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618800384'),
            ['AutoFarming'] = 1, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 33, 
            ['InBattle'] = 1, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 1, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 1, 
            ['SocialActionSystem'] = 1, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [34] = {
            ['ActionEnum'] = 'InWaterWalk', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338278656'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 34, 
            ['InBattle'] = 1, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [35] = {
            ['ActionEnum'] = 'Seat', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54426630889472'),
            ['AutoFarming'] = 1, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 0, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 35, 
            ['InBattle'] = 1, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 2, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [36] = {
            ['ActionEnum'] = 'SetAnimation', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338279168'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 0, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 36, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 2, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 1, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [37] = {
            ['ActionEnum'] = 'SetUpperAnimation', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338279424'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 0, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 37, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 1, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [38] = {
            ['ActionEnum'] = 'AutoFarming', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618801664'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 0, 
            ['Dancing'] = 0, 
            ['DialogControl'] = 1, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 38, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 1, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 2, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [39] = {
            ['ActionEnum'] = 'AutoSkill', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618801920'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 39, 
            ['InBattle'] = 0, 
            ['InMount'] = 1, 
            ['InOtherMount'] = 1, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 1, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 1, 
            ['Morph'] = 1, 
            ['Move'] = 0, 
            ['Navigate'] = 1, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 1, 
            ['SetAnimation'] = 1, 
            ['SetUpperAnimation'] = 1, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [40] = {
            ['ActionEnum'] = 'QuestControl', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338280192'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 2, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 0, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 2, 
            ['ID'] = 40, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 2, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 2, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [41] = {
            ['ActionEnum'] = 'Observer', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338280448'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 1, 
            ['ID'] = 41, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 0, 
            ['Match'] = 2, 
            ['Morph'] = 2, 
            ['Move'] = 0, 
            ['Navigate'] = 1, 
            ['Observer'] = 0, 
            ['QuestControl'] = 1, 
            ['Seat'] = 1, 
            ['SetAnimation'] = 1, 
            ['SetUpperAnimation'] = 1, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [42] = {
            ['ActionEnum'] = 'CommonInteractEnterArea', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338280704'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 0, 
            ['Dancing'] = 0, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 42, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 1, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 0, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [43] = {
            ['ActionEnum'] = 'DialogControl', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338280960'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 2, 
            ['Follow'] = 2, 
            ['ID'] = 43, 
            ['InBattle'] = 0, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 2, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 2, 
            ['Navigate'] = 2, 
            ['Observer'] = 0, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [44] = {
            ['ActionEnum'] = 'Photograph', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338281216'),
            ['AutoFarming'] = 1, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 0, 
            ['CutScene'] = 1, 
            ['Dancing'] = 0, 
            ['DialogControl'] = 0, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 44, 
            ['InBattle'] = 1, 
            ['InMount'] = 0, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 1, 
            ['Observer'] = 0, 
            ['QuestControl'] = 1, 
            ['Seat'] = 0, 
            ['SetAnimation'] = 0, 
            ['SetUpperAnimation'] = 0, 
            ['SocialActionSystem'] = 0, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 1, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 0, 
        },
        [45] = {
            ['ActionEnum'] = 'StrongControl', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54426630887168'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 2, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 45, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [47] = {
            ['ActionEnum'] = 'BossMechanism', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54426630887424'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 0, 
            ['BossMechanism'] = 0, 
            ['Camera'] = 2, 
            ['CastCombatSkill'] = 0, 
            ['ContinuousInteract'] = 2, 
            ['CutScene'] = 0, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 2, 
            ['Die'] = 0, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 47, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 0, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 0, 
            ['Jump'] = 0, 
            ['Match'] = 0, 
            ['Morph'] = 0, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 0, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 0, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 0, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [48] = {
            ['ActionEnum'] = 'StartDance', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338281984'),
            ['AutoFarming'] = 1, 
            ['AutoSkill'] = 1, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 0, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 48, 
            ['InBattle'] = 1, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 1, 
            ['Jump'] = 2, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 2, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 1, 
        },
        [49] = {
            ['ActionEnum'] = 'GetoffMount', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54564338282240'),
            ['AutoFarming'] = 1, 
            ['AutoSkill'] = 1, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 2, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 49, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 0, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 0, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [51] = {
            ['ActionEnum'] = 'InOtherMount', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54426630892544'),
            ['AutoFarming'] = 0, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 1, 
            ['Camera'] = 0, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 0, 
            ['Follow'] = 0, 
            ['ID'] = 51, 
            ['InBattle'] = 1, 
            ['InMount'] = 1, 
            ['InOtherMount'] = 1, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 0, 
            ['InPVPPrepare'] = 0, 
            ['InVehicle'] = 1, 
            ['InWaterWalk'] = 1, 
            ['InteractSpell'] = 2, 
            ['Jump'] = 1, 
            ['Match'] = 0, 
            ['Morph'] = 1, 
            ['Move'] = 0, 
            ['Navigate'] = 0, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 2, 
            ['SetAnimation'] = 2, 
            ['SetUpperAnimation'] = 2, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 1, 
            ['TarotTeamBuildingProcess'] = 0, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 2, 
            ['UseItemSpell'] = 2, 
        },
        [52] = {
            ['ActionEnum'] = 'UnStucking', 
            ['ActionName'] = Game.TableDataManager:GetLangStr('str_54495618804736'),
            ['AutoFarming'] = 2, 
            ['AutoSkill'] = 2, 
            ['BossMechanism'] = 2, 
            ['Camera'] = 1, 
            ['CastCombatSkill'] = 1, 
            ['ContinuousInteract'] = 1, 
            ['CutScene'] = 1, 
            ['Dancing'] = 1, 
            ['DialogControl'] = 1, 
            ['Die'] = 1, 
            ['FashionSystem'] = 1, 
            ['Follow'] = 2, 
            ['ID'] = 52, 
            ['InBattle'] = 0, 
            ['InMount'] = 2, 
            ['InOtherMount'] = 2, 
            ['InPVEScene'] = 0, 
            ['InPVPFighting'] = 1, 
            ['InPVPPrepare'] = 1, 
            ['InVehicle'] = 2, 
            ['InWaterWalk'] = 2, 
            ['InteractSpell'] = 1, 
            ['Jump'] = 1, 
            ['Match'] = 1, 
            ['Morph'] = 0, 
            ['Move'] = 2, 
            ['Navigate'] = 2, 
            ['Observer'] = 1, 
            ['QuestControl'] = 1, 
            ['Seat'] = 1, 
            ['SetAnimation'] = 1, 
            ['SetUpperAnimation'] = 1, 
            ['SocialActionSystem'] = 2, 
            ['StrongControl'] = 2, 
            ['TarotTeamBuildingProcess'] = 1, 
            ['TeleportSpell'] = 2, 
            ['UnStucking'] = 0, 
            ['UseItemSpell'] = 1, 
        },
    }
}
return TopData