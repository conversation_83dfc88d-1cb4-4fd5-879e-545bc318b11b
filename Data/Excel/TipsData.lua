--
-- 表名: $UITips_界面提醒.xlsx  页名：$Tips_界面说明
--

local TopData = {
	data = {
		[6420001] = {
			["Id"] = 6420001,
			["Enum"] = "ROLE_DISPLAY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774391296'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042826753')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420002] = {
			["Id"] = 6420002,
			["Enum"] = "SKILL_CUSTOMIZER",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505956096'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042827009')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_34910836364800'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579697921')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_61025848133376'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_61026116568833')},
			["SubTitle4"] = Game.TableDataManager:GetLangStr('str_61026385004288'),
			["Description4"] = {Game.TableDataManager:GetLangStr('str_61026653439745')},
		},
		[6420003] = {
			["Id"] = 6420003,
			["Enum"] = "TIPS_3V3",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505956352'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042827265'), Game.TableDataManager:GetLangStr('str_61025042827266'), Game.TableDataManager:GetLangStr('str_61025042827267'), Game.TableDataManager:GetLangStr('str_61025042827268')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420005] = {
			["Id"] = 6420005,
			["Enum"] = "TASK",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505956864'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024505956864'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042827777')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420006] = {
			["Id"] = 6420006,
			["Enum"] = "EQUIP_REFORGE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505957120'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774392576'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042828033')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61024505957120'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579698945')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420007] = {
			["Id"] = 6420007,
			["Enum"] = "TEAM",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505957376'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774392832'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042828289')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311263744'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579699201')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420008] = {
			["Id"] = 6420008,
			["Enum"] = "MAIL_COLLECT_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505957632'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024505957632'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042828545')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420009] = {
			["Id"] = 6420009,
			["Enum"] = "ROLE_SCORE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774393344'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042828801'), Game.TableDataManager:GetLangStr('str_61025042828802'), Game.TableDataManager:GetLangStr('str_61025042828803'), Game.TableDataManager:GetLangStr('str_61025042828804'), Game.TableDataManager:GetLangStr('str_61025042828805')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420010] = {
			["Id"] = 6420010,
			["Enum"] = "DUNGEON_ASSIGNMENT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774393600'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042829057')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311264512'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579699969')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420011] = {
			["Id"] = 6420011,
			["Enum"] = "DUNGEON_AUCTION",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774393856'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042829313')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420012] = {
			["Id"] = 6420012,
			["Enum"] = "DUNGEON_DAILY",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505958656'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042829569')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311265024'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579700481')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420013] = {
			["Id"] = 6420013,
			["Enum"] = "DUNGEON_WEEKLY",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505958912'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042829825')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420014] = {
			["Id"] = 6420014,
			["Enum"] = "DUNGEON_TEST",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505959168'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042829825')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420015] = {
			["Id"] = 6420015,
			["Enum"] = "ROLE_SELECT",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505959424'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042830337')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420016] = {
			["Id"] = 6420016,
			["Enum"] = "ROLE_CREATE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505959680'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042830593')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420017] = {
			["Id"] = 6420017,
			["Enum"] = "FELLOW_STAR_UP_1PROP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774395392'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042830849')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420018] = {
			["Id"] = 6420018,
			["Enum"] = "FELLOW_STAR_UP_2PROP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774395392'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042831105'), Game.TableDataManager:GetLangStr('str_61025042830849')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420019] = {
			["Id"] = 6420019,
			["Enum"] = "FELLOW_SYSTEM_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505960448'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042831361')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311266816'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579702273')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_61025848137728'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_61026116573185')},
			["SubTitle4"] = Game.TableDataManager:GetLangStr('str_61026385008640'),
			["Description4"] = {Game.TableDataManager:GetLangStr('str_61026653444097')},
		},
		[6420020] = {
			["Id"] = 6420020,
			["Enum"] = "GACHA_SYSTEM_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505960704'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042831617')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420021] = {
			["Id"] = 6420021,
			["Enum"] = "GACHA_SHOP_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505960960'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042831873')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420022] = {
			["Id"] = 6420022,
			["Enum"] = "GACHA_SHOP_REFRESH_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774396672'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042832129')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420023] = {
			["Id"] = 6420023,
			["Enum"] = "FELLOW_CE_ADDITION_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774396928'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042832385')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311267840'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579703297')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_61025848138752'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_61025579703297')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420024] = {
			["Id"] = 6420024,
			["Enum"] = "SEALED_SYSTEM_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505956096'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774397184'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042832641')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_58343641060864'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579703553')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420025] = {
			["Id"] = 6420025,
			["Enum"] = "SEFIROT_CORE_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505961984'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774397440'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042832897')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61024774397184'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579703809')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420040] = {
			["Id"] = 6420040,
			["Enum"] = "SEALED_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505962240'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774397696'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042833153')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_53602265604608'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579704065')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_61025848139520'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_61026116574977')},
			["SubTitle4"] = Game.TableDataManager:GetLangStr('str_61026385010432'),
			["Description4"] = {Game.TableDataManager:GetLangStr('str_61026653445889')},
		},
		[6420041] = {
			["Id"] = 6420041,
			["Enum"] = "SEALED_SYSTEM_CE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774397952'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042833409')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420042] = {
			["Id"] = 6420042,
			["Enum"] = "SEALED_SYSTEM_RESPON_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774398208'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042833665')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420043] = {
			["Id"] = 6420043,
			["Enum"] = "SEALED_RESPON_TIPS_DOWN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774398464'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042833921')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420044] = {
			["Id"] = 6420044,
			["Enum"] = "SEALED_RESPON_TIPS_UP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774398720'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042834177')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420045] = {
			["Id"] = 6420045,
			["Enum"] = "SEALED_REFINE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774398976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042834433')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420046] = {
			["Id"] = 6420046,
			["Enum"] = "SEALED_REFINE_LUCK_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774399232'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042834689')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420047] = {
			["Id"] = 6420047,
			["Enum"] = "SKILL_CE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774397952'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042834945')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420048] = {
			["Id"] = 6420048,
			["Enum"] = "SKILL_PRESET_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774399744'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042835201')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420049] = {
			["Id"] = 6420049,
			["Enum"] = "SKILL_FELLOW_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774400000'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042835457')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420026] = {
			["Id"] = 6420026,
			["Enum"] = "EQUIP_FIXED",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_53602265601024'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_25839328561664'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042835713')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311271168'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579706625')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420027] = {
			["Id"] = 6420027,
			["Enum"] = "EQUIPENHANCE_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505965056'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774400512'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042835969')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311271424'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579706881')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420028] = {
			["Id"] = 6420028,
			["Enum"] = "EQUIP_ADVANCE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505965312'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19793356785152'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042836225')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61024505965312'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579707137')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420029] = {
			["Id"] = 6420029,
			["Enum"] = "DEFEQUIP_ADVANCE_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505965568'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042836481')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420031] = {
			["Id"] = 6420031,
			["Enum"] = "SEALED_CE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774401536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042836993')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311272448'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579707905')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_61025848143360'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_61025579707905')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420032] = {
			["Id"] = 6420032,
			["Enum"] = "DUNGEON_DAILY_NEWS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505966336'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042837249')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420033] = {
			["Id"] = 6420033,
			["Enum"] = "STAR_MYSTERY_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774402048'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042837505')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420034] = {
			["Id"] = 6420034,
			["Enum"] = "CASHMARKET_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505966848'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042837761')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420035] = {
			["Id"] = 6420035,
			["Enum"] = "EQUIP_BASICTIP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774402560'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042838017')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420036] = {
			["Id"] = 6420036,
			["Enum"] = "EQUIP_ENHANCESUITE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774402816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042838273')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311273728'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579709185')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420037] = {
			["Id"] = 6420037,
			["Enum"] = "EQUIP_WORD",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774403072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042838529')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420038] = {
			["Id"] = 6420038,
			["Enum"] = "EQUIP_ENHANCESUITE_NONE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774402816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042838785')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311273728'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579709185')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420039] = {
			["Id"] = 6420039,
			["Enum"] = "EQUIP_ENHANCESUITE_FULL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774402816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042839041')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421001] = {
			["Id"] = 6421001,
			["Enum"] = "HP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_27832461821696'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042839297')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421002] = {
			["Id"] = 6421002,
			["Enum"] = "HP_REGEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774404096'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042839553')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421003] = {
			["Id"] = 6421003,
			["Enum"] = "WALK_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774404352'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042839809')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421004] = {
			["Id"] = 6421004,
			["Enum"] = "RUN_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774404608'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042840065')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421005] = {
			["Id"] = 6421005,
			["Enum"] = "SPRINT_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774404864'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042840321')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421006] = {
			["Id"] = 6421006,
			["Enum"] = "PHY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_53121497695744'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042840577')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421007] = {
			["Id"] = 6421007,
			["Enum"] = "WIL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_53121497696000'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042840833')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421008] = {
			["Id"] = 6421008,
			["Enum"] = "STR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_53121497696256'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042841089')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421009] = {
			["Id"] = 6421009,
			["Enum"] = "INT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_20342038857984'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042841345')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421010] = {
			["Id"] = 6421010,
			["Enum"] = "AGL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_20342038858240'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042841601')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421011] = {
			["Id"] = 6421011,
			["Enum"] = "LUCK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774406400'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042841857')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421012] = {
			["Id"] = 6421012,
			["Enum"] = "ATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662551553'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042842113')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421013] = {
			["Id"] = 6421013,
			["Enum"] = "MATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662552065'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042842369')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421014] = {
			["Id"] = 6421014,
			["Enum"] = "ATK_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774407168'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042842625')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421015] = {
			["Id"] = 6421015,
			["Enum"] = "ATK_RANGE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774407424'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042842881')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421016] = {
			["Id"] = 6421016,
			["Enum"] = "DEX",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662551554'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843137')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421017] = {
			["Id"] = 6421017,
			["Enum"] = "MDEX",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662552066'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843393')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421018] = {
			["Id"] = 6421018,
			["Enum"] = "CRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662556162'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843649')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421019] = {
			["Id"] = 6421019,
			["Enum"] = "MCRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662556418'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843905')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421020] = {
			["Id"] = 6421020,
			["Enum"] = "CDB",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774408704'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042844161')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421021] = {
			["Id"] = 6421021,
			["Enum"] = "MCDB",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774408960'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042844417')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421022] = {
			["Id"] = 6421022,
			["Enum"] = "DMG_BONUS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774409216'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042844673')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421023] = {
			["Id"] = 6421023,
			["Enum"] = "M_DMG_BONUS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774409472'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042844929')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421024] = {
			["Id"] = 6421024,
			["Enum"] = "ELE1_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774409728'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421025] = {
			["Id"] = 6421025,
			["Enum"] = "ELE1_DB",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774409984'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421026] = {
			["Id"] = 6421026,
			["Enum"] = "IGNORE_ELE1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774410240'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421027] = {
			["Id"] = 6421027,
			["Enum"] = "ENHANCE_CTRL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774410496'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421028] = {
			["Id"] = 6421028,
			["Enum"] = "CAREER_DMG_UP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774410752'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421029] = {
			["Id"] = 6421029,
			["Enum"] = "SPECIES_DMG_UP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774411008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421030] = {
			["Id"] = 6421030,
			["Enum"] = "AR_PEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774411264'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421031] = {
			["Id"] = 6421031,
			["Enum"] = "AP_PEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774411520'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421032] = {
			["Id"] = 6421032,
			["Enum"] = "DEF",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_27832461821952'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042847233')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421033] = {
			["Id"] = 6421033,
			["Enum"] = "RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_27832461822208'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042847489')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421034] = {
			["Id"] = 6421034,
			["Enum"] = "MISS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774412288'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042847745')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421035] = {
			["Id"] = 6421035,
			["Enum"] = "M_MISS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774412544'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042848001')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421036] = {
			["Id"] = 6421036,
			["Enum"] = "CRIT_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_27832461822720'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042848257')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421037] = {
			["Id"] = 6421037,
			["Enum"] = "M_CRIT_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_27832461822976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042848513')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421038] = {
			["Id"] = 6421038,
			["Enum"] = "CRIT_DMG_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774413312'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042848769')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421039] = {
			["Id"] = 6421039,
			["Enum"] = "M_CRIT_DMG_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774413568'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042849025')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421040] = {
			["Id"] = 6421040,
			["Enum"] = "ELE_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774413824'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421041] = {
			["Id"] = 6421041,
			["Enum"] = "ELE1_DW",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774414080'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421042] = {
			["Id"] = 6421042,
			["Enum"] = "CAREER_DW",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774414336'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421043] = {
			["Id"] = 6421043,
			["Enum"] = "SPECIES_DW",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774414592'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042845185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421044] = {
			["Id"] = 6421044,
			["Enum"] = "MATK_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774414848'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042842625')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421045] = {
			["Id"] = 6421045,
			["Enum"] = "ELE_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774415104'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042850561')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421046] = {
			["Id"] = 6421046,
			["Enum"] = "ELE1_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774415360'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042850817')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421047] = {
			["Id"] = 6421047,
			["Enum"] = "ELE2_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774415616'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042851073')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421048] = {
			["Id"] = 6421048,
			["Enum"] = "ELE2_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774415872'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042851329')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421049] = {
			["Id"] = 6421049,
			["Enum"] = "ELE3_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774416128'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042851585')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421050] = {
			["Id"] = 6421050,
			["Enum"] = "ELE3_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774416384'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042851841')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421051] = {
			["Id"] = 6421051,
			["Enum"] = "ELE4_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774416640'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042852097')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421052] = {
			["Id"] = 6421052,
			["Enum"] = "ELE4_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774416896'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042852353')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421053] = {
			["Id"] = 6421053,
			["Enum"] = "ELE5_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774417152'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042852609')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421054] = {
			["Id"] = 6421054,
			["Enum"] = "ELE5_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774417408'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042852865')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421055] = {
			["Id"] = 6421055,
			["Enum"] = "ELE6_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774417664'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042853121')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421056] = {
			["Id"] = 6421056,
			["Enum"] = "ELE6_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774417920'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042853377')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421057] = {
			["Id"] = 6421057,
			["Enum"] = "ELE7_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774418176'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042853633')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421058] = {
			["Id"] = 6421058,
			["Enum"] = "ELE7_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774418432'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042853889')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421059] = {
			["Id"] = 6421059,
			["Enum"] = "ELE8_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774418688'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042854145')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421060] = {
			["Id"] = 6421060,
			["Enum"] = "ELE8_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774418944'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042854401')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421061] = {
			["Id"] = 6421061,
			["Enum"] = "ELE9_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774419200'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042854657')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421062] = {
			["Id"] = 6421062,
			["Enum"] = "ELE9_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774419456'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042854913')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421063] = {
			["Id"] = 6421063,
			["Enum"] = "ELE1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774419712'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042855169')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421064] = {
			["Id"] = 6421064,
			["Enum"] = "ELE2_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774419968'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042855425')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421065] = {
			["Id"] = 6421065,
			["Enum"] = "ELE3_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774420224'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042855681')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421066] = {
			["Id"] = 6421066,
			["Enum"] = "ELE4_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774420480'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042855937')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421067] = {
			["Id"] = 6421067,
			["Enum"] = "ELE5_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774420736'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042856193')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421068] = {
			["Id"] = 6421068,
			["Enum"] = "ELE6_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774420992'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042856449')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421069] = {
			["Id"] = 6421069,
			["Enum"] = "ELE7_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774421248'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042856705')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421070] = {
			["Id"] = 6421070,
			["Enum"] = "ELE8_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774421504'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042856961')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421071] = {
			["Id"] = 6421071,
			["Enum"] = "ELE9_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774421760'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042857217')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421072] = {
			["Id"] = 6421072,
			["Enum"] = "CTL1_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774422016'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042857473')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421073] = {
			["Id"] = 6421073,
			["Enum"] = "CTL1_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774422272'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042857729')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421074] = {
			["Id"] = 6421074,
			["Enum"] = "CTL2_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774422528'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042857985')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421075] = {
			["Id"] = 6421075,
			["Enum"] = "CTL2_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774422784'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042858241')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421076] = {
			["Id"] = 6421076,
			["Enum"] = "CTL3_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774423040'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042858497')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421077] = {
			["Id"] = 6421077,
			["Enum"] = "CTL3_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774423296'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042858753')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421078] = {
			["Id"] = 6421078,
			["Enum"] = "CTL4_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774423552'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042859009')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421079] = {
			["Id"] = 6421079,
			["Enum"] = "CTL4_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774423808'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042859265')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421080] = {
			["Id"] = 6421080,
			["Enum"] = "CTL5_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774424064'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042859521')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421081] = {
			["Id"] = 6421081,
			["Enum"] = "CTL5_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774424320'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042859777')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421082] = {
			["Id"] = 6421082,
			["Enum"] = "CTL6_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774424576'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042860033')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421083] = {
			["Id"] = 6421083,
			["Enum"] = "CTL6_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774424832'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042860289')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421084] = {
			["Id"] = 6421084,
			["Enum"] = "CTL7_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774425088'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042860545')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421085] = {
			["Id"] = 6421085,
			["Enum"] = "CTL7_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774425344'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042860801')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421086] = {
			["Id"] = 6421086,
			["Enum"] = "CTL8_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774425600'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042861057')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421087] = {
			["Id"] = 6421087,
			["Enum"] = "CTL8_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774425856'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042861313')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421088] = {
			["Id"] = 6421088,
			["Enum"] = "CTL9_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774426112'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042861569')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421089] = {
			["Id"] = 6421089,
			["Enum"] = "CTL9_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774426368'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042861825')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421090] = {
			["Id"] = 6421090,
			["Enum"] = "CTL1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774426624'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042862081')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421091] = {
			["Id"] = 6421091,
			["Enum"] = "CTL2_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774426880'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042862337')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421092] = {
			["Id"] = 6421092,
			["Enum"] = "CTL3_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774427136'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042862593')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421093] = {
			["Id"] = 6421093,
			["Enum"] = "CTL4_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774427392'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042862849')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421094] = {
			["Id"] = 6421094,
			["Enum"] = "CTL5_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774427648'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042863105')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421095] = {
			["Id"] = 6421095,
			["Enum"] = "CTL6_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774427904'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042863361')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421096] = {
			["Id"] = 6421096,
			["Enum"] = "CTL7_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774428160'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042863617')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421097] = {
			["Id"] = 6421097,
			["Enum"] = "CTL8_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774428416'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042863873')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421098] = {
			["Id"] = 6421098,
			["Enum"] = "CTL9_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774428672'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042864129')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421099] = {
			["Id"] = 6421099,
			["Enum"] = "CLASS1_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774428928'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042864385')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421100] = {
			["Id"] = 6421100,
			["Enum"] = "CLASS1_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774428928'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042864641')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421101] = {
			["Id"] = 6421101,
			["Enum"] = "CLASS2_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774429440'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042864897')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421102] = {
			["Id"] = 6421102,
			["Enum"] = "CLASS2_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774429440'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042865153')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421103] = {
			["Id"] = 6421103,
			["Enum"] = "CLASS3_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774429952'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042865409')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421104] = {
			["Id"] = 6421104,
			["Enum"] = "CLASS3_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774429952'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042865665')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421105] = {
			["Id"] = 6421105,
			["Enum"] = "CLASS4_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774430464'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042865921')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421106] = {
			["Id"] = 6421106,
			["Enum"] = "CLASS4_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774430464'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042866177')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421107] = {
			["Id"] = 6421107,
			["Enum"] = "BOSS_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774430976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042866433')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421108] = {
			["Id"] = 6421108,
			["Enum"] = "BOSS_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774430976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042866689')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421109] = {
			["Id"] = 6421109,
			["Enum"] = "CLASS1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774431488'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042866945')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421110] = {
			["Id"] = 6421110,
			["Enum"] = "CLASS1_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774431488'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042867201')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421111] = {
			["Id"] = 6421111,
			["Enum"] = "CLASS2_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774432000'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042867457')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421112] = {
			["Id"] = 6421112,
			["Enum"] = "CLASS2_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774432000'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042867713')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421113] = {
			["Id"] = 6421113,
			["Enum"] = "CLASS3_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774432512'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042867969')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421114] = {
			["Id"] = 6421114,
			["Enum"] = "CLASS3_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774432512'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042868225')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421115] = {
			["Id"] = 6421115,
			["Enum"] = "CLASS4_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774433024'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042868481')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421116] = {
			["Id"] = 6421116,
			["Enum"] = "CLASS4_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774433024'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042868737')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421117] = {
			["Id"] = 6421117,
			["Enum"] = "BOSS_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774433536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042868993')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421118] = {
			["Id"] = 6421118,
			["Enum"] = "BOSS_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774433536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042869249')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421119] = {
			["Id"] = 6421119,
			["Enum"] = "CLASS5_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774434048'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042869505')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421120] = {
			["Id"] = 6421120,
			["Enum"] = "CLASS5_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774434048'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042869761')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421121] = {
			["Id"] = 6421121,
			["Enum"] = "CLASS6_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774434560'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042870017')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421122] = {
			["Id"] = 6421122,
			["Enum"] = "CLASS6_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774434560'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042870273')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421123] = {
			["Id"] = 6421123,
			["Enum"] = "CLASS5_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774435072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042870529')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421124] = {
			["Id"] = 6421124,
			["Enum"] = "CLASS5_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774435072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042870785')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421125] = {
			["Id"] = 6421125,
			["Enum"] = "CLASS6_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774435584'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042871041')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421126] = {
			["Id"] = 6421126,
			["Enum"] = "CLASS6_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774435584'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042871297')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421201] = {
			["Id"] = 6421201,
			["Enum"] = "BRIEF_HP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_27832461821696'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042839297')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421202] = {
			["Id"] = 6421202,
			["Enum"] = "BRIEF_PHY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_53121497695744'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042840577')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421203] = {
			["Id"] = 6421203,
			["Enum"] = "BRIEF_WIL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_53121497696000'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042840833')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421204] = {
			["Id"] = 6421204,
			["Enum"] = "BRIEF_STR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_53121497696256'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042841089')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421205] = {
			["Id"] = 6421205,
			["Enum"] = "BRIEF_INT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_20342038857984'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042841345')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421206] = {
			["Id"] = 6421206,
			["Enum"] = "BRIEF_AGL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_20342038858240'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042841601')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421207] = {
			["Id"] = 6421207,
			["Enum"] = "BRIEF_PATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662551553'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042842113')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421208] = {
			["Id"] = 6421208,
			["Enum"] = "BRIEF_PPIERCE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662551554'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843137')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421209] = {
			["Id"] = 6421209,
			["Enum"] = "BRIEF_PCRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662556162'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843649')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421210] = {
			["Id"] = 6421210,
			["Enum"] = "BRIEF_PCRITHURT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774408704'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042844161')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421211] = {
			["Id"] = 6421211,
			["Enum"] = "BRIEF_ELEMENT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774438656'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042874113')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579745025')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421212] = {
			["Id"] = 6421212,
			["Enum"] = "BRIEF_MATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662552065'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042842369')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421213] = {
			["Id"] = 6421213,
			["Enum"] = "BRIEF_MPIERCE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662552066'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843393')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421214] = {
			["Id"] = 6421214,
			["Enum"] = "BRIEF_MCRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18625662556418'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042843905')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421215] = {
			["Id"] = 6421215,
			["Enum"] = "BRIEF_MCRITHURT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774408960'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042844417')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421216] = {
			["Id"] = 6421216,
			["Enum"] = "BRIEF_DEF",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_48311402759680'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042875393')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421217] = {
			["Id"] = 6421217,
			["Enum"] = "BRIEF_BLOCK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774440192'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042875649')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710209')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421218] = {
			["Id"] = 6421218,
			["Enum"] = "BRIEF_CRITANTI",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774440448'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042875905')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421219] = {
			["Id"] = 6421219,
			["Enum"] = "BRIEF_CRITDEF",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774440704'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042876161')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311274752'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579710721')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422001] = {
			["Id"] = 6422001,
			["Enum"] = "MAGIC_MIRROR",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_31543045131520'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042876417')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422002] = {
			["Id"] = 6422002,
			["Enum"] = "SHOPPINGMALL",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_27421755574016'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042876673')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422003] = {
			["Id"] = 6422003,
			["Enum"] = "TOPUP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506006016'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042876929')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422004] = {
			["Id"] = 6422004,
			["Enum"] = "BULK_PURCHASE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774441728'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042877185')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423001] = {
			["Id"] = 6423001,
			["Enum"] = "SETTING",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506006528'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506006528'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042877441')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423002] = {
			["Id"] = 6423002,
			["Enum"] = "IMAGE_QUAITY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774442240'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042877697')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423003] = {
			["Id"] = 6423003,
			["Enum"] = "SCREEN_PLAYER_NUM",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774442496'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042877953')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423004] = {
			["Id"] = 6423004,
			["Enum"] = "SCREEN_NPC_NUM",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774442752'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042878209')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423005] = {
			["Id"] = 6423005,
			["Enum"] = "PROTECTSCREEN_LEVEL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042878465')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423006] = {
			["Id"] = 6423006,
			["Enum"] = "PROTECTSCREEN_GREEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042878721')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423007] = {
			["Id"] = 6423007,
			["Enum"] = "PROTECTSCREEN_NONHOSTILITY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042878977')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423008] = {
			["Id"] = 6423008,
			["Enum"] = "PROTECTSCREEN_ASSOCIATION",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042879233')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423009] = {
			["Id"] = 6423009,
			["Enum"] = "TURN_TO_TARGET_WHEN_LOCK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042879489')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423010] = {
			["Id"] = 6423010,
			["Enum"] = "AUTO_CORRECT_CAMERA",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042879745')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423011] = {
			["Id"] = 6423011,
			["Enum"] = "OPERATOR_MODE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042880001')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424001] = {
			["Id"] = 6424001,
			["Enum"] = "CHAT_LOUDSPEAKER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774444800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042880257')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424002] = {
			["Id"] = 6424002,
			["Enum"] = "FRIEND",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_26114206475520'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042880513')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424003] = {
			["Id"] = 6424003,
			["Enum"] = "GUILD",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_32849520497408'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042880769')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424004] = {
			["Id"] = 6424004,
			["Enum"] = "TEAM_GROUP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506010112'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506010112'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042881025')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424005] = {
			["Id"] = 6424005,
			["Enum"] = "GUILD_TARGET",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506010368'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774445824'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042881281')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311316736'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579752193')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424006] = {
			["Id"] = 6424006,
			["Enum"] = "GUILD_PRACTICE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328578048'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042881537')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424007] = {
			["Id"] = 6424007,
			["Enum"] = "GUILD_PARTY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774446336'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042881793')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6425001] = {
			["Id"] = 6425001,
			["Enum"] = "WANTED_DEGREE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042882049'), Game.TableDataManager:GetLangStr('str_61025042882050'), Game.TableDataManager:GetLangStr('str_61025042882051'), Game.TableDataManager:GetLangStr('str_61025042882052'), Game.TableDataManager:GetLangStr('str_61025042882053'), Game.TableDataManager:GetLangStr('str_61025042882054')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6425005] = {
			["Id"] = 6425005,
			["Enum"] = "SERVER_LEVEL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774446848'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042882305'), Game.TableDataManager:GetLangStr('str_61025042882306'), Game.TableDataManager:GetLangStr('str_61025042882307'), Game.TableDataManager:GetLangStr('str_61025042882308'), Game.TableDataManager:GetLangStr('str_61025042882309')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426001] = {
			["Id"] = 6426001,
			["Enum"] = "SKILL_DESC_PRO1_SKILL1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774447104'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042882561')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426002] = {
			["Id"] = 6426002,
			["Enum"] = "SKILL_DESC_PRO1_SKILL2",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774447360'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042882817')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426003] = {
			["Id"] = 6426003,
			["Enum"] = "SKILL_DESC_COMMON1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774447616'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042883073')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426004] = {
			["Id"] = 6426004,
			["Enum"] = "SKILL_DESC_PRO1_SKILL3",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774447872'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042883329')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426005] = {
			["Id"] = 6426005,
			["Enum"] = "SKILL_DESC_PRO1_SKILL4",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774448128'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042883585')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426006] = {
			["Id"] = 6426006,
			["Enum"] = "SKILL_DESC_PRO2_SKILL1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774448384'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042883841')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426007] = {
			["Id"] = 6426007,
			["Enum"] = "SKILL_DESC_COMMON1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774448640'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042884097')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426011] = {
			["Id"] = 6426011,
			["Enum"] = "FELLOW_TOWER_CLIMB_DESC",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774449664'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042885121')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426012] = {
			["Id"] = 6426012,
			["Enum"] = "DUNGEON_TOWER_CLIMB_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506014464'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32849520500992'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042885377')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311320832'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579756289')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426013] = {
			["Id"] = 6426013,
			["Enum"] = "DIFFICULTY_TOWER_CLIMB_DESC",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774450176'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042885633')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426014] = {
			["Id"] = 6426014,
			["Enum"] = "WORLD_QUIZ",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774450432'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042885889')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426015] = {
			["Id"] = 6426015,
			["Enum"] = "RANKINGLIST",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506015232'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042886145')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426016] = {
			["Id"] = 6426016,
			["Enum"] = "GUILD_REMATERIAL_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506015488'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042886401'), Game.TableDataManager:GetLangStr('str_61025042886402'), Game.TableDataManager:GetLangStr('str_61025042886403'), Game.TableDataManager:GetLangStr('str_61025042886404')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426017] = {
			["Id"] = 6426017,
			["Enum"] = "SEQUENCE_PROMOTION",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042886657')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426018] = {
			["Id"] = 6426018,
			["Enum"] = "SCHEDULE_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206472704'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042886913')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311322368'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579757825')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_61025848193280'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_61026116628737')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426019] = {
			["Id"] = 6426019,
			["Enum"] = "PUZZLE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774451712'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042887169')},
			["SubTitle2"] = "",
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579758081')},
			["SubTitle3"] = "",
			["Description3"] = {Game.TableDataManager:GetLangStr('str_61026116628993')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426020] = {
			["Id"] = 6426020,
			["Enum"] = "RED_NAME_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774451968'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042887425')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579758337')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426021] = {
			["Id"] = 6426021,
			["Enum"] = "SEQUENCE_PROMOTION2",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506016768'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506016768'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042886657')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426022] = {
			["Id"] = 6426022,
			["Enum"] = "SEQUENCE_PROMOTION3",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506017024'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506017024'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042886657')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426023] = {
			["Id"] = 6426023,
			["Enum"] = "GOD_WAY_ACT_RULE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506017280'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506017280'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042888193')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426024] = {
			["Id"] = 6426024,
			["Enum"] = "MIX_POTION",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506017536'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506017536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042888449')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427101] = {
			["Id"] = 6427101,
			["Enum"] = "MAKE_MEDICINE_MAIN_UI",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506017792'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042888705')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427102] = {
			["Id"] = 6427102,
			["Enum"] = "MAKE_MEDICINE_OPERATION_UI",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506018048'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042888961')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427003] = {
			["Id"] = 6427003,
			["Enum"] = "ROLE_PLAY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206472960'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774453760'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042889217')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427004] = {
			["Id"] = 6427004,
			["Enum"] = "ROLE_PLAY_IDENTITY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206472960'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774454016'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042889473')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427005] = {
			["Id"] = 6427005,
			["Enum"] = "ROLE_PLAY_SKILL_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206472960'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774454272'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042889729')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427201] = {
			["Id"] = 6427201,
			["Enum"] = "WORLD_BOSS_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506019072'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774454528'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042889985')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311325440'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579760897')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427202] = {
			["Id"] = 6427202,
			["Enum"] = "PVP_PROTECT_SCORE_DESC",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774454784'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042890241')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427203] = {
			["Id"] = 6427203,
			["Enum"] = "PVP_3V3_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024505956352'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024505956352'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042890497')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427204] = {
			["Id"] = 6427204,
			["Enum"] = "PVP_3V3_WIN_SCORE_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506019840'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774455296'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042890753')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61024774454784'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579761665')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427205] = {
			["Id"] = 6427205,
			["Enum"] = "EXPLORE_PROGRESS_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506020096'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774455552'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042891009')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427206] = {
			["Id"] = 6427206,
			["Enum"] = "ROLE_PLAY_SECURITY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206472960'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774453760'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042891265')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427207] = {
			["Id"] = 6427207,
			["Enum"] = "COLLECTIBLESMAIN_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506020608'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774456064'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042891521')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427208] = {
			["Id"] = 6427208,
			["Enum"] = "ACHIEVEMENTTAB_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_756987987456'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774456320'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042891521')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427209] = {
			["Id"] = 6427209,
			["Enum"] = "SERVER_LEVEL_WITHOUT_SEQUENCE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774446848'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042882306'), Game.TableDataManager:GetLangStr('str_61025042882307'), Game.TableDataManager:GetLangStr('str_61025042882308'), Game.TableDataManager:GetLangStr('str_61025042882309')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427210] = {
			["Id"] = 6427210,
			["Enum"] = "RED_PACKET_SYSTEM_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042892289')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427211] = {
			["Id"] = 6427211,
			["Enum"] = "LUCKY_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042892545')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427212] = {
			["Id"] = 6427212,
			["Enum"] = "SECRET_WORD_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042892801')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427213] = {
			["Id"] = 6427213,
			["Enum"] = "PASSWORD_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042893057')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427214] = {
			["Id"] = 6427214,
			["Enum"] = "GOLD_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042892289')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427215] = {
			["Id"] = 6427215,
			["Enum"] = "FASHION_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042893569')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427216] = {
			["Id"] = 6427216,
			["Enum"] = "CHOOSE_FASHION_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042893825')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427217] = {
			["Id"] = 6427217,
			["Enum"] = "FRIEND_RANGE_SETTING_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042894081')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427218] = {
			["Id"] = 6427218,
			["Enum"] = "PLAYCARD_FASHION_TIPS",
			["Tip"] = 1,
			["Title"] = "%s",
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042894337'), "<Grey>%d/%d</>"},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427219] = {
			["Id"] = 6427219,
			["Enum"] = "CHATROOM_ANNOUNMENT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506023680'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506023680'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042894593')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427220] = {
			["Id"] = 6427220,
			["Enum"] = "CHATROOM_USER_GUIDE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042894849')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427221] = {
			["Id"] = 6427221,
			["Enum"] = "CHATROOM_ORDER_MIC_GUIDE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042895105')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427222] = {
			["Id"] = 6427222,
			["Enum"] = "GUILD_LEAGUE_ACTIVITY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_32849520506112'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042895361')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427223] = {
			["Id"] = 6427223,
			["Enum"] = "CHAT_ANOY_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042895617')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427224] = {
			["Id"] = 6427224,
			["Enum"] = "CLUB_FUNDS_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506024960'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506024960'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042895873')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427225] = {
			["Id"] = 6427225,
			["Enum"] = "CLUB_ACTIVITY_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506025216'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506025216'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042896129')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427226] = {
			["Id"] = 6427226,
			["Enum"] = "CLUB_BUILDINGUP_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506025472'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506025472'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042896385')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427227] = {
			["Id"] = 6427227,
			["Enum"] = "TEAM_SUPPORT_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506025728'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506025728'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042896641')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427228] = {
			["Id"] = 6427228,
			["Enum"] = "PVP_ENTRANCE_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774461440'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042896897')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427229] = {
			["Id"] = 6427229,
			["Enum"] = "CLUB_POSITION_ADJUSTMENT",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042897153')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427230] = {
			["Id"] = 6427230,
			["Enum"] = "WORLD_PVP_FIGHT_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026496'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506026496'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042897409')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427231] = {
			["Id"] = 6427231,
			["Enum"] = "CHATROOM_AMBIENT_SOUND",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042897665')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427232] = {
			["Id"] = 6427232,
			["Enum"] = "TITLE_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506027008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506027008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042897921')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427233] = {
			["Id"] = 6427233,
			["Enum"] = "HONORIFIC_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506027264'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506027264'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042898177')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427234] = {
			["Id"] = 6427234,
			["Enum"] = "CHATROOM_HELP_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774462976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042898433')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311333888'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579769345')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427235] = {
			["Id"] = 6427235,
			["Enum"] = "TAROT_TEAM_HELP_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506027776'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774463232'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042898689')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61025311334144'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579769601')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427236] = {
			["Id"] = 6427236,
			["Enum"] = "ELEMENT_MAIN_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506028032'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042898945')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427237] = {
			["Id"] = 6427237,
			["Enum"] = "ELEMENT_LV_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506028288'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042899201')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427238] = {
			["Id"] = 6427238,
			["Enum"] = "ELEMENT_TREE_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506028544'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042899457')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427239] = {
			["Id"] = 6427239,
			["Enum"] = "ELEMENT_ELEMENT_TYPE_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506028800'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042899713')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427240] = {
			["Id"] = 6427240,
			["Enum"] = "FASHION_DYEING_TIP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774464512'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61024774464512')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427241] = {
			["Id"] = 6427241,
			["Enum"] = "LOGIN_QUEUE_TIP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506029312'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774464768'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042900225')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_61024774464768'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_61025579771137')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427242] = {
			["Id"] = 6427242,
			["Enum"] = "LOGIN_CIRCUIT_BREAKER_TIP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506029568'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042900481')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427243] = {
			["Id"] = 6427243,
			["Enum"] = "PLOT_RECAP_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774465280'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042900737')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427244] = {
			["Id"] = 6427244,
			["Enum"] = "ELEMENT_REWARD_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506030080'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042900993')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427245] = {
			["Id"] = 6427245,
			["Enum"] = "PVP_3V3_Award",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506030336'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506030336'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042901249')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427246] = {
			["Id"] = 6427246,
			["Enum"] = "PVP_12V12_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506030592'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506030592'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042901505')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427247] = {
			["Id"] = 6427247,
			["Enum"] = "PVP_CHAMPION_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506030848'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506030848'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042901761')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427248] = {
			["Id"] = 6427248,
			["Enum"] = "CHAMPION_MATCH_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506031104'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024506031104'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042902017')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427249] = {
			["Id"] = 6427249,
			["Enum"] = "CHATROOM_USER_GUIDE_DATE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042902273')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427250] = {
			["Id"] = 6427250,
			["Enum"] = "CHATROOM_USER_GUIDE_CONCERT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042902529')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427251] = {
			["Id"] = 6427251,
			["Enum"] = "CHATROOM_USER_GUIDE_BEGINNER",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042902785')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427252] = {
			["Id"] = 6427252,
			["Enum"] = "CHATROOM_USER_GUIDE_READ",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042903041')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427253] = {
			["Id"] = 6427253,
			["Enum"] = "CHATROOM_PARTY_TYPE_GUIDE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042903297')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427254] = {
			["Id"] = 6427254,
			["Enum"] = "GUILD_PERMISSION_PRESIDENT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_27144998618624'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_27147951408640')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427255] = {
			["Id"] = 6427255,
			["Enum"] = "GUILD_PERMISSION_VICE_PRESIDENT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_27144998618880'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_27147951408896')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427256] = {
			["Id"] = 6427256,
			["Enum"] = "GUILD_PERMISSION_DIRECTOR",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_27144998619392'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_27147951409408')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427257] = {
			["Id"] = 6427257,
			["Enum"] = "GUILD_PERMISSION_GROUP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506033408'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_27147951410176')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427258] = {
			["Id"] = 6427258,
			["Enum"] = "GUILD_PERMISSION_STAR",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_27144998619136'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_27147951409152')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427259] = {
			["Id"] = 6427259,
			["Enum"] = "GUILD_PERMISSION_MEMBER",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506033920'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042904833')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427260] = {
			["Id"] = 6427260,
			["Enum"] = "GUILD_PERMISSION_SUBSTITUTE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506034176'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042905089')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427261] = {
			["Id"] = 6427261,
			["Enum"] = "CHATROOM_USER_GUIDE_COURT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042905345')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427271] = {
			["Id"] = 6427271,
			["Enum"] = "MANOR_TOTAL_SCORE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042905601')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427272] = {
			["Id"] = 6427272,
			["Enum"] = "MANOR_CURRENCY_1",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042905601')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427273] = {
			["Id"] = 6427273,
			["Enum"] = "MANOR_CURRENCY_2",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042905601')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427274] = {
			["Id"] = 6427274,
			["Enum"] = "GUILD_LEAGUE_MORALE_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_61024774443008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_61025042906369')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
	},
}

return TopData
