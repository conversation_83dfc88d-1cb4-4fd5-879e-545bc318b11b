--
-- 表名: $UITips_界面提醒.xlsx  页名：$Tips_界面说明
--

local TopData = {
	data = {
		[6420001] = {
			["Id"] = 6420001,
			["Enum"] = "ROLE_DISPLAY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615961088'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884396545')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420002] = {
			["Id"] = 6420002,
			["Enum"] = "SKILL_CUSTOMIZER",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347525888'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884396801')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_34567238981120'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421267713')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_60819689703168'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_60819958138625')},
			["SubTitle4"] = Game.TableDataManager:GetLangStr('str_60820226574080'),
			["Description4"] = {Game.TableDataManager:GetLangStr('str_60820495009537')},
		},
		[6420003] = {
			["Id"] = 6420003,
			["Enum"] = "TIPS_3V3",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347526144'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884397057'), Game.TableDataManager:GetLangStr('str_60818884397058'), Game.TableDataManager:GetLangStr('str_60818884397059'), Game.TableDataManager:GetLangStr('str_60818884397060')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420005] = {
			["Id"] = 6420005,
			["Enum"] = "TASK",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347526656'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347526656'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884397569')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420006] = {
			["Id"] = 6420006,
			["Enum"] = "EQUIP_REFORGE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789672704'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615962368'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884397825')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_54632789672704'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421268737')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420007] = {
			["Id"] = 6420007,
			["Enum"] = "TEAM",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347527168'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615962624'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884398081')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152833536'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421268993')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420008] = {
			["Id"] = 6420008,
			["Enum"] = "MAIL_COLLECT_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_5360924497664'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_5360924497664'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884398337')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420009] = {
			["Id"] = 6420009,
			["Enum"] = "ROLE_SCORE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615963136'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884398593'), Game.TableDataManager:GetLangStr('str_60818884398594'), Game.TableDataManager:GetLangStr('str_60818884398595'), Game.TableDataManager:GetLangStr('str_60818884398596'), Game.TableDataManager:GetLangStr('str_60818884398597')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420010] = {
			["Id"] = 6420010,
			["Enum"] = "DUNGEON_ASSIGNMENT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615963392'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884398849')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152834304'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421269761')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420011] = {
			["Id"] = 6420011,
			["Enum"] = "DUNGEON_AUCTION",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615963648'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884399105')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420012] = {
			["Id"] = 6420012,
			["Enum"] = "DUNGEON_DAILY",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347528448'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884399361')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152834816'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421270273')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420013] = {
			["Id"] = 6420013,
			["Enum"] = "DUNGEON_WEEKLY",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347528704'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884399617')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420014] = {
			["Id"] = 6420014,
			["Enum"] = "DUNGEON_TEST",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347528960'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884399617')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420015] = {
			["Id"] = 6420015,
			["Enum"] = "ROLE_SELECT",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347529216'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884400129')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420016] = {
			["Id"] = 6420016,
			["Enum"] = "ROLE_CREATE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347529472'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884400385')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420017] = {
			["Id"] = 6420017,
			["Enum"] = "FELLOW_STAR_UP_1PROP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615965184'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884400641')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420018] = {
			["Id"] = 6420018,
			["Enum"] = "FELLOW_STAR_UP_2PROP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615965184'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884400897'), Game.TableDataManager:GetLangStr('str_60818884400641')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420019] = {
			["Id"] = 6420019,
			["Enum"] = "FELLOW_SYSTEM_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347530240'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884401153')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152836608'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421272065')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_60819689707520'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_60819958142977')},
			["SubTitle4"] = Game.TableDataManager:GetLangStr('str_60820226578432'),
			["Description4"] = {Game.TableDataManager:GetLangStr('str_60820495013889')},
		},
		[6420020] = {
			["Id"] = 6420020,
			["Enum"] = "GACHA_SYSTEM_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347530496'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884401409')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420021] = {
			["Id"] = 6420021,
			["Enum"] = "GACHA_SHOP_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347530752'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884401665')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420022] = {
			["Id"] = 6420022,
			["Enum"] = "GACHA_SHOP_REFRESH_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615966464'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884401921')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420023] = {
			["Id"] = 6420023,
			["Enum"] = "FELLOW_CE_ADDITION_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615966720'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884402177')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152837632'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421273089')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_60819689708544'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_60819421273089')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420024] = {
			["Id"] = 6420024,
			["Enum"] = "SEALED_SYSTEM_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347525888'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615966976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884402433')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421273345')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420025] = {
			["Id"] = 6420025,
			["Enum"] = "SEFIROT_CORE_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347531776'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789491968'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884402689')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60818615966976'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421273601')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420040] = {
			["Id"] = 6420040,
			["Enum"] = "SEALED_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347532032'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615967488'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884402945')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_53396107174400'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421273857')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_54632789480192'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_60819958144769')},
			["SubTitle4"] = Game.TableDataManager:GetLangStr('str_54632789480704'),
			["Description4"] = {Game.TableDataManager:GetLangStr('str_60820495015681')},
		},
		[6420041] = {
			["Id"] = 6420041,
			["Enum"] = "SEALED_SYSTEM_CE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789469184'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884403201')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420042] = {
			["Id"] = 6420042,
			["Enum"] = "SEALED_SYSTEM_RESPON_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615968000'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884403457')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420043] = {
			["Id"] = 6420043,
			["Enum"] = "SEALED_RESPON_TIPS_DOWN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615968256'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884403713')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420044] = {
			["Id"] = 6420044,
			["Enum"] = "SEALED_RESPON_TIPS_UP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615968512'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884403969')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420045] = {
			["Id"] = 6420045,
			["Enum"] = "SEALED_REFINE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615968768'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884404225')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420046] = {
			["Id"] = 6420046,
			["Enum"] = "SEALED_REFINE_LUCK_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789502976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884404481')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420047] = {
			["Id"] = 6420047,
			["Enum"] = "SKILL_CE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789469184'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884404737')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420048] = {
			["Id"] = 6420048,
			["Enum"] = "SKILL_PRESET_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615969536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884404993')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420049] = {
			["Id"] = 6420049,
			["Enum"] = "SKILL_FELLOW_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615969792'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884405249')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420026] = {
			["Id"] = 6420026,
			["Enum"] = "EQUIP_FIXED",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_53396107170816'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_25564450654720'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884405505')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152840960'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421276417')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420027] = {
			["Id"] = 6420027,
			["Enum"] = "EQUIPENHANCE_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789672960'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615970304'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884405761')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_54632789678080'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421276673')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420028] = {
			["Id"] = 6420028,
			["Enum"] = "EQUIP_ADVANCE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789681408'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18487686727168'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884406017')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_54632789681408'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421276929')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420029] = {
			["Id"] = 6420029,
			["Enum"] = "DEFEQUIP_ADVANCE_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347535360'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884406273')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420031] = {
			["Id"] = 6420031,
			["Enum"] = "SEALED_CE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615971328'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884406785')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152842240'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421277697')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_60819689713152'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_60819421277697')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420032] = {
			["Id"] = 6420032,
			["Enum"] = "DUNGEON_DAILY_NEWS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347536128'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884407041')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420033] = {
			["Id"] = 6420033,
			["Enum"] = "STAR_MYSTERY_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615971840'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884407297')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420034] = {
			["Id"] = 6420034,
			["Enum"] = "CASHMARKET_TIPS",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789434880'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884407553')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420035] = {
			["Id"] = 6420035,
			["Enum"] = "EQUIP_BASICTIP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789670144'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884407809')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420036] = {
			["Id"] = 6420036,
			["Enum"] = "EQUIP_ENHANCESUITE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615972608'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884408065')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152843520'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421278977')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420037] = {
			["Id"] = 6420037,
			["Enum"] = "EQUIP_WORD",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615972864'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884408321')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420038] = {
			["Id"] = 6420038,
			["Enum"] = "EQUIP_ENHANCESUITE_NONE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615972608'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_54632789805312')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152843520'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421278977')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6420039] = {
			["Id"] = 6420039,
			["Enum"] = "EQUIP_ENHANCESUITE_FULL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615972608'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884408833')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421001] = {
			["Id"] = 6421001,
			["Enum"] = "HP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789317376'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884409089')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421002] = {
			["Id"] = 6421002,
			["Enum"] = "HP_REGEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329557760'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884409345')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421003] = {
			["Id"] = 6421003,
			["Enum"] = "WALK_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615974144'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884409601')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421004] = {
			["Id"] = 6421004,
			["Enum"] = "RUN_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615974400'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884409857')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421005] = {
			["Id"] = 6421005,
			["Enum"] = "SPRINT_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615974656'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884410113')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421006] = {
			["Id"] = 6421006,
			["Enum"] = "PHY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329554432'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884410369')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421007] = {
			["Id"] = 6421007,
			["Enum"] = "WIL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329554944'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884410625')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421008] = {
			["Id"] = 6421008,
			["Enum"] = "STR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_46043391592192'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884410881')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421009] = {
			["Id"] = 6421009,
			["Enum"] = "INT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329555968'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884411137')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421010] = {
			["Id"] = 6421010,
			["Enum"] = "AGL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329556480'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884411393')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421011] = {
			["Id"] = 6421011,
			["Enum"] = "LUCK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_46523891062784'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884411649')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421012] = {
			["Id"] = 6421012,
			["Enum"] = "ATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992493569'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884411905')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421013] = {
			["Id"] = 6421013,
			["Enum"] = "MATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992494081'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884412161')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421014] = {
			["Id"] = 6421014,
			["Enum"] = "ATK_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329567488'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884412417')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421015] = {
			["Id"] = 6421015,
			["Enum"] = "ATK_RANGE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615977216'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884412673')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421016] = {
			["Id"] = 6421016,
			["Enum"] = "DEX",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992493570'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884412929')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421017] = {
			["Id"] = 6421017,
			["Enum"] = "MDEX",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992494082'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421018] = {
			["Id"] = 6421018,
			["Enum"] = "CRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992498178'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413441')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421019] = {
			["Id"] = 6421019,
			["Enum"] = "MCRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992498434'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413697')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421020] = {
			["Id"] = 6421020,
			["Enum"] = "CDB",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210377216'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413953')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421021] = {
			["Id"] = 6421021,
			["Enum"] = "MCDB",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210377472'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414209')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421022] = {
			["Id"] = 6421022,
			["Enum"] = "DMG_BONUS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329571328'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414465')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421023] = {
			["Id"] = 6421023,
			["Enum"] = "M_DMG_BONUS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329578752'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414721')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421024] = {
			["Id"] = 6421024,
			["Enum"] = "ELE1_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615979520'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421025] = {
			["Id"] = 6421025,
			["Enum"] = "ELE1_DB",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615979776'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421026] = {
			["Id"] = 6421026,
			["Enum"] = "IGNORE_ELE1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615980032'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421027] = {
			["Id"] = 6421027,
			["Enum"] = "ENHANCE_CTRL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615980288'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421028] = {
			["Id"] = 6421028,
			["Enum"] = "CAREER_DMG_UP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615980544'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421029] = {
			["Id"] = 6421029,
			["Enum"] = "SPECIES_DMG_UP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615980800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421030] = {
			["Id"] = 6421030,
			["Enum"] = "AR_PEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615981056'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421031] = {
			["Id"] = 6421031,
			["Enum"] = "AP_PEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615981312'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421032] = {
			["Id"] = 6421032,
			["Enum"] = "DEF",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789314816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884417025')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421033] = {
			["Id"] = 6421033,
			["Enum"] = "RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789315072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884417281')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421034] = {
			["Id"] = 6421034,
			["Enum"] = "MISS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329572864'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884417537')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421035] = {
			["Id"] = 6421035,
			["Enum"] = "M_MISS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329580288'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884417793')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421036] = {
			["Id"] = 6421036,
			["Enum"] = "CRIT_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368807936'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884418049')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421037] = {
			["Id"] = 6421037,
			["Enum"] = "M_CRIT_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368808192'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884418305')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421038] = {
			["Id"] = 6421038,
			["Enum"] = "CRIT_DMG_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368808960'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884418561')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421039] = {
			["Id"] = 6421039,
			["Enum"] = "M_CRIT_DMG_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368809216'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884418817')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421040] = {
			["Id"] = 6421040,
			["Enum"] = "ELE_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615983616'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421041] = {
			["Id"] = 6421041,
			["Enum"] = "ELE1_DW",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615983872'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421042] = {
			["Id"] = 6421042,
			["Enum"] = "CAREER_DW",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615984128'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421043] = {
			["Id"] = 6421043,
			["Enum"] = "SPECIES_DW",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818615984384'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414977')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421044] = {
			["Id"] = 6421044,
			["Enum"] = "MATK_SPEED",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329574912'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884412417')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421045] = {
			["Id"] = 6421045,
			["Enum"] = "ELE_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210370816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884420353')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421046] = {
			["Id"] = 6421046,
			["Enum"] = "ELE1_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329582336'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884420609')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421047] = {
			["Id"] = 6421047,
			["Enum"] = "ELE2_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210371072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884420865')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421048] = {
			["Id"] = 6421048,
			["Enum"] = "ELE2_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329585152'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884421121')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421049] = {
			["Id"] = 6421049,
			["Enum"] = "ELE3_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210371328'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884421377')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421050] = {
			["Id"] = 6421050,
			["Enum"] = "ELE3_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329587968'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884421633')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421051] = {
			["Id"] = 6421051,
			["Enum"] = "ELE4_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210371584'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884421889')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421052] = {
			["Id"] = 6421052,
			["Enum"] = "ELE4_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329590784'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884422145')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421053] = {
			["Id"] = 6421053,
			["Enum"] = "ELE5_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210371840'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884422401')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421054] = {
			["Id"] = 6421054,
			["Enum"] = "ELE5_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329593600'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884422657')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421055] = {
			["Id"] = 6421055,
			["Enum"] = "ELE6_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210372096'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884422913')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421056] = {
			["Id"] = 6421056,
			["Enum"] = "ELE6_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329596416'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884423169')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421057] = {
			["Id"] = 6421057,
			["Enum"] = "ELE7_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210372352'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884423425')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421058] = {
			["Id"] = 6421058,
			["Enum"] = "ELE7_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329599232'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884423681')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421059] = {
			["Id"] = 6421059,
			["Enum"] = "ELE8_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210372608'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884423937')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421060] = {
			["Id"] = 6421060,
			["Enum"] = "ELE8_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329602048'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884424193')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421061] = {
			["Id"] = 6421061,
			["Enum"] = "ELE9_DMG",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210372864'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884424449')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421062] = {
			["Id"] = 6421062,
			["Enum"] = "ELE9_IGN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329604864'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884424705')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421063] = {
			["Id"] = 6421063,
			["Enum"] = "ELE1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329581824'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884424961')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421064] = {
			["Id"] = 6421064,
			["Enum"] = "ELE2_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329584640'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884425217')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421065] = {
			["Id"] = 6421065,
			["Enum"] = "ELE3_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329587456'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884425473')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421066] = {
			["Id"] = 6421066,
			["Enum"] = "ELE4_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329590272'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884425729')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421067] = {
			["Id"] = 6421067,
			["Enum"] = "ELE5_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329593088'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884425985')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421068] = {
			["Id"] = 6421068,
			["Enum"] = "ELE6_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329595904'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884426241')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421069] = {
			["Id"] = 6421069,
			["Enum"] = "ELE7_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329598720'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884426497')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421070] = {
			["Id"] = 6421070,
			["Enum"] = "ELE8_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329601536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884426753')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421071] = {
			["Id"] = 6421071,
			["Enum"] = "ELE9_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329604352'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884427009')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421072] = {
			["Id"] = 6421072,
			["Enum"] = "CTL1_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368821248'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884427265')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421073] = {
			["Id"] = 6421073,
			["Enum"] = "CTL1_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329607168'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884427521')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421074] = {
			["Id"] = 6421074,
			["Enum"] = "CTL2_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368821504'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884427777')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421075] = {
			["Id"] = 6421075,
			["Enum"] = "CTL2_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329608448'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884428033')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421076] = {
			["Id"] = 6421076,
			["Enum"] = "CTL3_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368821760'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884428289')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421077] = {
			["Id"] = 6421077,
			["Enum"] = "CTL3_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329609728'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884428545')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421078] = {
			["Id"] = 6421078,
			["Enum"] = "CTL4_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368822016'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884428801')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421079] = {
			["Id"] = 6421079,
			["Enum"] = "CTL4_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329611008'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884429057')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421080] = {
			["Id"] = 6421080,
			["Enum"] = "CTL5_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368822272'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884429313')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421081] = {
			["Id"] = 6421081,
			["Enum"] = "CTL5_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329612288'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884429569')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421082] = {
			["Id"] = 6421082,
			["Enum"] = "CTL6_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368822528'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884429825')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421083] = {
			["Id"] = 6421083,
			["Enum"] = "CTL6_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329613568'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884430081')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421084] = {
			["Id"] = 6421084,
			["Enum"] = "CTL7_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368822784'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884430337')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421085] = {
			["Id"] = 6421085,
			["Enum"] = "CTL7_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329614848'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884430593')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421086] = {
			["Id"] = 6421086,
			["Enum"] = "CTL8_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368823040'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884430849')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421087] = {
			["Id"] = 6421087,
			["Enum"] = "CTL8_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329616128'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884431105')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421088] = {
			["Id"] = 6421088,
			["Enum"] = "CTL9_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_19036368823296'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884431361')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421089] = {
			["Id"] = 6421089,
			["Enum"] = "CTL9_HTR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329617408'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884431617')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421090] = {
			["Id"] = 6421090,
			["Enum"] = "CTL1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329606144'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884431873')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421091] = {
			["Id"] = 6421091,
			["Enum"] = "CTL2_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329607424'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884432129')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421092] = {
			["Id"] = 6421092,
			["Enum"] = "CTL3_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329608704'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884432385')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421093] = {
			["Id"] = 6421093,
			["Enum"] = "CTL4_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329609984'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884432641')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421094] = {
			["Id"] = 6421094,
			["Enum"] = "CTL5_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329611264'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884432897')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421095] = {
			["Id"] = 6421095,
			["Enum"] = "CTL6_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329612544'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884433153')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421096] = {
			["Id"] = 6421096,
			["Enum"] = "CTL7_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329613824'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884433409')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421097] = {
			["Id"] = 6421097,
			["Enum"] = "CTL8_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329615104'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884433665')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421098] = {
			["Id"] = 6421098,
			["Enum"] = "CTL9_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329616384'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884433921')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421099] = {
			["Id"] = 6421099,
			["Enum"] = "CLASS1_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610080256'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884434177')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421100] = {
			["Id"] = 6421100,
			["Enum"] = "CLASS1_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610080256'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884434433')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421101] = {
			["Id"] = 6421101,
			["Enum"] = "CLASS2_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610080512'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884434689')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421102] = {
			["Id"] = 6421102,
			["Enum"] = "CLASS2_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610080512'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884434945')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421103] = {
			["Id"] = 6421103,
			["Enum"] = "CLASS3_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610080768'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884435201')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421104] = {
			["Id"] = 6421104,
			["Enum"] = "CLASS3_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610080768'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884435457')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421105] = {
			["Id"] = 6421105,
			["Enum"] = "CLASS4_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610081024'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884435713')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421106] = {
			["Id"] = 6421106,
			["Enum"] = "CLASS4_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610081024'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884435969')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421107] = {
			["Id"] = 6421107,
			["Enum"] = "BOSS_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610078464'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884436225')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421108] = {
			["Id"] = 6421108,
			["Enum"] = "BOSS_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610078464'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884436481')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421109] = {
			["Id"] = 6421109,
			["Enum"] = "CLASS1_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610082816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884436737')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421110] = {
			["Id"] = 6421110,
			["Enum"] = "CLASS1_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610082816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884436993')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421111] = {
			["Id"] = 6421111,
			["Enum"] = "CLASS2_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884437249')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421112] = {
			["Id"] = 6421112,
			["Enum"] = "CLASS2_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884437505')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421113] = {
			["Id"] = 6421113,
			["Enum"] = "CLASS3_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083328'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884437761')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421114] = {
			["Id"] = 6421114,
			["Enum"] = "CLASS3_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083328'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884438017')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421115] = {
			["Id"] = 6421115,
			["Enum"] = "CLASS4_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083584'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884438273')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421116] = {
			["Id"] = 6421116,
			["Enum"] = "CLASS4_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083584'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884438529')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421117] = {
			["Id"] = 6421117,
			["Enum"] = "BOSS_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610079744'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884438785')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421118] = {
			["Id"] = 6421118,
			["Enum"] = "BOSS_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610079744'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884439041')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421119] = {
			["Id"] = 6421119,
			["Enum"] = "CLASS5_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610081280'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884439297')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421120] = {
			["Id"] = 6421120,
			["Enum"] = "CLASS5_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610081280'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884439553')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421121] = {
			["Id"] = 6421121,
			["Enum"] = "CLASS6_ENH",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610081536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884439809')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421122] = {
			["Id"] = 6421122,
			["Enum"] = "CLASS6_ENH_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610081536'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884440065')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421123] = {
			["Id"] = 6421123,
			["Enum"] = "CLASS5_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083840'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884440321')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421124] = {
			["Id"] = 6421124,
			["Enum"] = "CLASS5_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610083840'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884440577')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421125] = {
			["Id"] = 6421125,
			["Enum"] = "CLASS6_RES",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610084096'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884440833')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421126] = {
			["Id"] = 6421126,
			["Enum"] = "CLASS6_RES_PER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24121610084096'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884441089')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421201] = {
			["Id"] = 6421201,
			["Enum"] = "BRIEF_HP",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789317376'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884409089')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421202] = {
			["Id"] = 6421202,
			["Enum"] = "BRIEF_PHY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329554432'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884410369')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421203] = {
			["Id"] = 6421203,
			["Enum"] = "BRIEF_WIL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329554944'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884410625')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421204] = {
			["Id"] = 6421204,
			["Enum"] = "BRIEF_STR",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_46043391592192'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884410881')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421205] = {
			["Id"] = 6421205,
			["Enum"] = "BRIEF_INT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329555968'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884411137')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421206] = {
			["Id"] = 6421206,
			["Enum"] = "BRIEF_AGL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_24190329556480'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884411393')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421207] = {
			["Id"] = 6421207,
			["Enum"] = "BRIEF_PATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992493569'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884411905')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421208] = {
			["Id"] = 6421208,
			["Enum"] = "BRIEF_PPIERCE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992493570'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884412929')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421209] = {
			["Id"] = 6421209,
			["Enum"] = "BRIEF_PCRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992498178'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413441')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421210] = {
			["Id"] = 6421210,
			["Enum"] = "BRIEF_PCRITHURT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210377216'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413953')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421211] = {
			["Id"] = 6421211,
			["Enum"] = "BRIEF_ELEMENT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616008448'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884443905')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421314817')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421212] = {
			["Id"] = 6421212,
			["Enum"] = "BRIEF_MATK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992494081'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884412161')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421213] = {
			["Id"] = 6421213,
			["Enum"] = "BRIEF_MPIERCE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992494082'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421214] = {
			["Id"] = 6421214,
			["Enum"] = "BRIEF_MCRIT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_17319992498434'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884413697')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421215] = {
			["Id"] = 6421215,
			["Enum"] = "BRIEF_MCRITHURT",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210377472'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884414209')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421216] = {
			["Id"] = 6421216,
			["Enum"] = "BRIEF_DEF",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789318144'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884445185')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421217] = {
			["Id"] = 6421217,
			["Enum"] = "BRIEF_BLOCK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210374144'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884445441')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280001')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421218] = {
			["Id"] = 6421218,
			["Enum"] = "BRIEF_CRITANTI",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210376960'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884445697')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6421219] = {
			["Id"] = 6421219,
			["Enum"] = "BRIEF_CRITDEF",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_18830210377984'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884445953')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152844544'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421280513')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422001] = {
			["Id"] = 6422001,
			["Enum"] = "MAGIC_MIRROR",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_31268167224576'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884446209')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422002] = {
			["Id"] = 6422002,
			["Enum"] = "SHOPPINGMALL",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_27146877667072'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884446465')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422003] = {
			["Id"] = 6422003,
			["Enum"] = "TOPUP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347575808'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884446721')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6422004] = {
			["Id"] = 6422004,
			["Enum"] = "BULK_PURCHASE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616011520'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884446977')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423001] = {
			["Id"] = 6423001,
			["Enum"] = "SETTING",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347576320'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347576320'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884447233')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423002] = {
			["Id"] = 6423002,
			["Enum"] = "IMAGE_QUAITY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012032'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884447489')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423003] = {
			["Id"] = 6423003,
			["Enum"] = "SCREEN_PLAYER_NUM",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012288'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884447745')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423004] = {
			["Id"] = 6423004,
			["Enum"] = "SCREEN_NPC_NUM",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012544'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884448001')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423005] = {
			["Id"] = 6423005,
			["Enum"] = "PROTECTSCREEN_LEVEL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884448257')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423006] = {
			["Id"] = 6423006,
			["Enum"] = "PROTECTSCREEN_GREEN",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884448513')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423007] = {
			["Id"] = 6423007,
			["Enum"] = "PROTECTSCREEN_NONHOSTILITY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884448769')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423008] = {
			["Id"] = 6423008,
			["Enum"] = "PROTECTSCREEN_ASSOCIATION",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884449025')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423009] = {
			["Id"] = 6423009,
			["Enum"] = "TURN_TO_TARGET_WHEN_LOCK",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884449281')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423010] = {
			["Id"] = 6423010,
			["Enum"] = "AUTO_CORRECT_CAMERA",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884449537')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6423011] = {
			["Id"] = 6423011,
			["Enum"] = "OPERATOR_MODE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884449793')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424001] = {
			["Id"] = 6424001,
			["Enum"] = "CHAT_LOUDSPEAKER",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616014592'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884450049')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424002] = {
			["Id"] = 6424002,
			["Enum"] = "FRIEND",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884450305')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424003] = {
			["Id"] = 6424003,
			["Enum"] = "GUILD",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_32574642590464'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884450561')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424004] = {
			["Id"] = 6424004,
			["Enum"] = "TEAM_GROUP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_4811168680704'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_4811168680704'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884450817')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424005] = {
			["Id"] = 6424005,
			["Enum"] = "GUILD_TARGET",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789835776'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616015616'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884451073')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152886528'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421321985')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424006] = {
			["Id"] = 6424006,
			["Enum"] = "GUILD_PRACTICE",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_38553505506304'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884451329')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6424007] = {
			["Id"] = 6424007,
			["Enum"] = "GUILD_PARTY",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_1031865896448'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884451585')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6425001] = {
			["Id"] = 6425001,
			["Enum"] = "WANTED_DEGREE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884451841'), Game.TableDataManager:GetLangStr('str_60818884451842'), Game.TableDataManager:GetLangStr('str_60818884451843'), Game.TableDataManager:GetLangStr('str_60818884451844'), Game.TableDataManager:GetLangStr('str_60818884451845'), Game.TableDataManager:GetLangStr('str_60818884451846')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6425005] = {
			["Id"] = 6425005,
			["Enum"] = "SERVER_LEVEL",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789903104'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884452097'), Game.TableDataManager:GetLangStr('str_60818884452098'), Game.TableDataManager:GetLangStr('str_60818884452099'), Game.TableDataManager:GetLangStr('str_60818884452100'), Game.TableDataManager:GetLangStr('str_60818884452101')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426001] = {
			["Id"] = 6426001,
			["Enum"] = "SKILL_DESC_PRO1_SKILL1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616016896'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884452353')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426002] = {
			["Id"] = 6426002,
			["Enum"] = "SKILL_DESC_PRO1_SKILL2",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616017152'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884452609')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426003] = {
			["Id"] = 6426003,
			["Enum"] = "SKILL_DESC_COMMON1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616017408'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884452865')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426004] = {
			["Id"] = 6426004,
			["Enum"] = "SKILL_DESC_PRO1_SKILL3",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616017664'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884453121')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426005] = {
			["Id"] = 6426005,
			["Enum"] = "SKILL_DESC_PRO1_SKILL4",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616017920'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884453377')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426006] = {
			["Id"] = 6426006,
			["Enum"] = "SKILL_DESC_PRO2_SKILL1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616018176'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884453633')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426007] = {
			["Id"] = 6426007,
			["Enum"] = "SKILL_DESC_COMMON1",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616018432'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884453889')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426011] = {
			["Id"] = 6426011,
			["Enum"] = "FELLOW_TOWER_CLIMB_DESC",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616019456'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884454913')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426012] = {
			["Id"] = 6426012,
			["Enum"] = "DUNGEON_TOWER_CLIMB_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347584256'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32574642594048'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884455169')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152890624'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421326081')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426013] = {
			["Id"] = 6426013,
			["Enum"] = "DIFFICULTY_TOWER_CLIMB_DESC",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616019968'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884455425')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426014] = {
			["Id"] = 6426014,
			["Enum"] = "WORLD_QUIZ",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_1031865894656'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884455681')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426015] = {
			["Id"] = 6426015,
			["Enum"] = "RANKINGLIST",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347585024'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884455937')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426016] = {
			["Id"] = 6426016,
			["Enum"] = "GUILD_REMATERIAL_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347585280'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884456193'), Game.TableDataManager:GetLangStr('str_60818884456194'), Game.TableDataManager:GetLangStr('str_60818884456195'), Game.TableDataManager:GetLangStr('str_60818884456196')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426017] = {
			["Id"] = 6426017,
			["Enum"] = "SEQUENCE_PROMOTION",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566272'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_25839328566272'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884456449')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426018] = {
			["Id"] = 6426018,
			["Enum"] = "SCHEDULE_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884456705')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_51128095999488'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421327617')},
			["SubTitle3"] = Game.TableDataManager:GetLangStr('str_51128095999744'),
			["Description3"] = {Game.TableDataManager:GetLangStr('str_60819958198529')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426019] = {
			["Id"] = 6426019,
			["Enum"] = "PUZZLE_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616021504'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884456961')},
			["SubTitle2"] = "",
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421327873')},
			["SubTitle3"] = "",
			["Description3"] = {Game.TableDataManager:GetLangStr('str_60819958198785')},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426020] = {
			["Id"] = 6426020,
			["Enum"] = "RED_NAME_TIPS",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789716224'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884457217')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421328129')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426021] = {
			["Id"] = 6426021,
			["Enum"] = "SEQUENCE_PROMOTION2",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347586560'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347586560'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884456449')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426022] = {
			["Id"] = 6426022,
			["Enum"] = "SEQUENCE_PROMOTION3",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347586816'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347586816'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884456449')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426023] = {
			["Id"] = 6426023,
			["Enum"] = "GOD_WAY_ACT_RULE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347587072'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347587072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884457985')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6426024] = {
			["Id"] = 6426024,
			["Enum"] = "MIX_POTION",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347587328'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347587328'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884458241')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427101] = {
			["Id"] = 6427101,
			["Enum"] = "MAKE_MEDICINE_MAIN_UI",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347587584'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884458497')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427102] = {
			["Id"] = 6427102,
			["Enum"] = "MAKE_MEDICINE_OPERATION_UI",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347587840'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884458753')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427003] = {
			["Id"] = 6427003,
			["Enum"] = "ROLE_PLAY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616023552'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884459009')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427004] = {
			["Id"] = 6427004,
			["Enum"] = "ROLE_PLAY_IDENTITY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616023808'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884459265')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427005] = {
			["Id"] = 6427005,
			["Enum"] = "ROLE_PLAY_SKILL_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789737728'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884459521')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427201] = {
			["Id"] = 6427201,
			["Enum"] = "WORLD_BOSS_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_34983582389504'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616024320'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884459777')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152895232'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421330689')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427202] = {
			["Id"] = 6427202,
			["Enum"] = "PVP_PROTECT_SCORE_DESC",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616024576'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884460033')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427203] = {
			["Id"] = 6427203,
			["Enum"] = "PVP_3V3_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347526144'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347526144'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884460289')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427204] = {
			["Id"] = 6427204,
			["Enum"] = "PVP_3V3_WIN_SCORE_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347589632'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616025088'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884460545')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60818616024576'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421331457')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427205] = {
			["Id"] = 6427205,
			["Enum"] = "EXPLORE_PROGRESS_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347589888'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616025344'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884460801')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427206] = {
			["Id"] = 6427206,
			["Enum"] = "ROLE_PLAY_SECURITY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616023552'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884461057')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427207] = {
			["Id"] = 6427207,
			["Enum"] = "COLLECTIBLESMAIN_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_6735582468864'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616025856'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884461313')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427208] = {
			["Id"] = 6427208,
			["Enum"] = "ACHIEVEMENTTAB_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_35666213736704'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616026112'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884461313')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427209] = {
			["Id"] = 6427209,
			["Enum"] = "SERVER_LEVEL_WITHOUT_SEQUENCE",
			["Tip"] = 1,
			["Title"] = "",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789903104'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884452098'), Game.TableDataManager:GetLangStr('str_60818884452099'), Game.TableDataManager:GetLangStr('str_60818884452100'), Game.TableDataManager:GetLangStr('str_60818884452101')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427210] = {
			["Id"] = 6427210,
			["Enum"] = "RED_PACKET_SYSTEM_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884462081')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427211] = {
			["Id"] = 6427211,
			["Enum"] = "LUCKY_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884462337')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427212] = {
			["Id"] = 6427212,
			["Enum"] = "SECRET_WORD_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884462593')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427213] = {
			["Id"] = 6427213,
			["Enum"] = "PASSWORD_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884462849')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427214] = {
			["Id"] = 6427214,
			["Enum"] = "GOLD_RED_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884462081')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427215] = {
			["Id"] = 6427215,
			["Enum"] = "FASHION_PACKET_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884463361')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427216] = {
			["Id"] = 6427216,
			["Enum"] = "CHOOSE_FASHION_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884463617')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427217] = {
			["Id"] = 6427217,
			["Enum"] = "FRIEND_RANGE_SETTING_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884463873')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427218] = {
			["Id"] = 6427218,
			["Enum"] = "PLAYCARD_FASHION_TIPS",
			["Tip"] = 1,
			["Title"] = "%s",
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884464129'), "<Grey>%d/%d</>"},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427219] = {
			["Id"] = 6427219,
			["Enum"] = "CHATROOM_ANNOUNMENT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789512704'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789512704'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884464385')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427220] = {
			["Id"] = 6427220,
			["Enum"] = "CHATROOM_USER_GUIDE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884464641')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427221] = {
			["Id"] = 6427221,
			["Enum"] = "CHATROOM_ORDER_MIC_GUIDE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884464897')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427222] = {
			["Id"] = 6427222,
			["Enum"] = "GUILD_LEAGUE_ACTIVITY_DESC",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_32574642599168'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884465153')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427223] = {
			["Id"] = 6427223,
			["Enum"] = "CHAT_ANOY_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884465409')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427224] = {
			["Id"] = 6427224,
			["Enum"] = "CLUB_FUNDS_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789833984'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789833984'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884465665')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427225] = {
			["Id"] = 6427225,
			["Enum"] = "CLUB_ACTIVITY_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_44601087887616'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_44601087887616'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884465921')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427226] = {
			["Id"] = 6427226,
			["Enum"] = "CLUB_BUILDINGUP_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789869312'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789869312'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884466177')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427227] = {
			["Id"] = 6427227,
			["Enum"] = "TEAM_SUPPORT_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347595520'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347595520'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884466433')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427228] = {
			["Id"] = 6427228,
			["Enum"] = "PVP_ENTRANCE_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616031232'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884466689')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427229] = {
			["Id"] = 6427229,
			["Enum"] = "CLUB_POSITION_ADJUSTMENT",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884466945')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427230] = {
			["Id"] = 6427230,
			["Enum"] = "WORLD_PVP_FIGHT_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347596288'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347596288'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884467201')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427231] = {
			["Id"] = 6427231,
			["Enum"] = "CHATROOM_AMBIENT_SOUND",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884467457')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427232] = {
			["Id"] = 6427232,
			["Enum"] = "TITLE_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347596800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347596800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884467713')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427233] = {
			["Id"] = 6427233,
			["Enum"] = "HONORIFIC_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347597056'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347597056'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884467969')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427234] = {
			["Id"] = 6427234,
			["Enum"] = "CHATROOM_HELP_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616032768'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884468225')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152903680'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421339137')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427235] = {
			["Id"] = 6427235,
			["Enum"] = "TAROT_TEAM_HELP_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347597568'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616033024'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884468481')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60819152903936'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421339393')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427236] = {
			["Id"] = 6427236,
			["Enum"] = "ELEMENT_MAIN_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347597824'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884468737')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427237] = {
			["Id"] = 6427237,
			["Enum"] = "ELEMENT_LV_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347598080'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884468993')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427238] = {
			["Id"] = 6427238,
			["Enum"] = "ELEMENT_TREE_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347598336'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884469249')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427239] = {
			["Id"] = 6427239,
			["Enum"] = "ELEMENT_ELEMENT_TYPE_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_16906333456640'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884469505')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427240] = {
			["Id"] = 6427240,
			["Enum"] = "FASHION_DYEING_TIP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_54701508830976'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_54701508830976')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427241] = {
			["Id"] = 6427241,
			["Enum"] = "LOGIN_QUEUE_TIP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347599104'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616034560'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884470017')},
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_60818616034560'),
			["Description2"] = {Game.TableDataManager:GetLangStr('str_60819421340929')},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427242] = {
			["Id"] = 6427242,
			["Enum"] = "LOGIN_CIRCUIT_BREAKER_TIP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347599360'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884470273')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427243] = {
			["Id"] = 6427243,
			["Enum"] = "PLOT_RECAP_TIPS",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616035072'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884470529')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427244] = {
			["Id"] = 6427244,
			["Enum"] = "ELEMENT_REWARD_TIP",
			["Tip"] = 2,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347599872'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884470785')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427245] = {
			["Id"] = 6427245,
			["Enum"] = "PVP_3V3_Award",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347600128'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347600128'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884471041')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427246] = {
			["Id"] = 6427246,
			["Enum"] = "PVP_12V12_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347600384'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347600384'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884471297')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427247] = {
			["Id"] = 6427247,
			["Enum"] = "PVP_CHAMPION_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347600640'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347600640'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884471553')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427248] = {
			["Id"] = 6427248,
			["Enum"] = "CHAMPION_MATCH_DESC",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347600896'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818347600896'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884471809')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427249] = {
			["Id"] = 6427249,
			["Enum"] = "CHATROOM_USER_GUIDE_DATE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884472065')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427250] = {
			["Id"] = 6427250,
			["Enum"] = "CHATROOM_USER_GUIDE_CONCERT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884472321')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427251] = {
			["Id"] = 6427251,
			["Enum"] = "CHATROOM_USER_GUIDE_BEGINNER",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884472577')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427252] = {
			["Id"] = 6427252,
			["Enum"] = "CHATROOM_USER_GUIDE_READ",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884472833')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427253] = {
			["Id"] = 6427253,
			["Enum"] = "CHATROOM_PARTY_TYPE_GUIDE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884473089')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427254] = {
			["Id"] = 6427254,
			["Enum"] = "GUILD_PERMISSION_PRESIDENT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_26870120711680'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_26873073501696')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427255] = {
			["Id"] = 6427255,
			["Enum"] = "GUILD_PERMISSION_VICE_PRESIDENT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_26870120711936'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_26873073501952')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427256] = {
			["Id"] = 6427256,
			["Enum"] = "GUILD_PERMISSION_DIRECTOR",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_26870120712448'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_26873073502464')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427257] = {
			["Id"] = 6427257,
			["Enum"] = "GUILD_PERMISSION_GROUP",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_44601087888640'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_26873073503232')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427258] = {
			["Id"] = 6427258,
			["Enum"] = "GUILD_PERMISSION_STAR",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_26870120712192'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_26873073502208')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427259] = {
			["Id"] = 6427259,
			["Enum"] = "GUILD_PERMISSION_MEMBER",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347603712'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884474625')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427260] = {
			["Id"] = 6427260,
			["Enum"] = "GUILD_PERMISSION_SUBSTITUTE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818347603968'),
			["SubTitle1"] = "",
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884474881')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427261] = {
			["Id"] = 6427261,
			["Enum"] = "CHATROOM_USER_GUIDE_COURT",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884475137')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427271] = {
			["Id"] = 6427271,
			["Enum"] = "MANOR_TOTAL_SCORE",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884475393')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427272] = {
			["Id"] = 6427272,
			["Enum"] = "MANOR_CURRENCY_1",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884475393')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
		[6427273] = {
			["Id"] = 6427273,
			["Enum"] = "MANOR_CURRENCY_2",
			["Tip"] = 1,
			["Title"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_60818616012800'),
			["Description1"] = {Game.TableDataManager:GetLangStr('str_60818884475393')},
			["SubTitle2"] = "",
			["Description2"] = {},
			["SubTitle3"] = "",
			["Description3"] = {},
			["SubTitle4"] = "",
			["Description4"] = {},
		},
	},
}

return TopData
