--
-- 表名: SkillPresetData(后处理)
--

local TopData = {
    classInitSkillSchemeMap = {
        [1200001] = 10001, 
        [1200002] = 20001, 
        [1200003] = 30001, 
        [1200004] = 40001, 
        [1200005] = 50001, 
        [1200006] = 60001, 
        [1200007] = 70001, 
    },
    professionSkillPresetList = {
        [1200001] = {[10001] = true, }, 
        [1200002] = {[20001] = true, [20002] = true, [20003] = true, [20004] = true, [20005] = true, }, 
        [1200003] = {[30001] = true, [30002] = true, [30003] = true, [30004] = true, [30005] = true, [30006] = true, }, 
        [1200004] = {[40001] = true, [40002] = true, [40003] = true, [40004] = true, [40005] = true, [40006] = true, }, 
        [1200005] = {[50001] = true, [50002] = true, [50003] = true, }, 
        [1200006] = {[60001] = true, [60002] = true, [60003] = true, }, 
        [1200007] = {[70001] = true, [70002] = true, [70003] = true, [70004] = true, [70005] = true, [70006] = true, }, 
    },
    data = {
        [10001] = {
            ['AttackSkillID'] = 89001116, 
            ['ClassID'] = 1200001, 
            ['CoreType'] = 3, 
            ['DeControlSkillID'] = 89001116, 
            ['ExtraordinarySkillID'] = 0, 
            ['ID'] = 10001, 
            ['IsDefautSkill'] = 1, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {}, 
            ['TalentTreeID'] = 1010301, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110074880'),
        },
        [20001] = {
            ['AttackSkillID'] = 86020040, 
            ['ClassID'] = 1200002, 
            ['CoreType'] = 3, 
            ['DeControlSkillID'] = 86022010, 
            ['ExtraordinarySkillID'] = 86024010, 
            ['ID'] = 20001, 
            ['IsDefautSkill'] = 1, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 86025010, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86021030, 86021100, 86021110, 86021080}, 
            ['TalentTreeID'] = 1020301, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110074880'),
        },
        [20002] = {
            ['AttackSkillID'] = 86020010, 
            ['ClassID'] = 1200002, 
            ['CoreType'] = 3, 
            ['DeControlSkillID'] = 86022010, 
            ['ExtraordinarySkillID'] = 86024010, 
            ['ID'] = 20002, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 86025010, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86021030, 86021100, 86021110, 86021080}, 
            ['TalentTreeID'] = 1020600, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076672'),
        },
        [20003] = {
            ['AttackSkillID'] = 86020040, 
            ['ClassID'] = 1200002, 
            ['CoreType'] = 3, 
            ['DeControlSkillID'] = 86022010, 
            ['ExtraordinarySkillID'] = 86024010, 
            ['ID'] = 20003, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 86025010, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86021030, 86021100, 86021110, 86021080}, 
            ['TalentTreeID'] = 1020301, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076928'),
        },
        [20004] = {
            ['AttackSkillID'] = 86020040, 
            ['ClassID'] = 1200002, 
            ['CoreType'] = 4, 
            ['DeControlSkillID'] = 86022010, 
            ['ExtraordinarySkillID'] = 86024010, 
            ['ID'] = 20004, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 86025010, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86021030, 86021100, 86021110, 86021080}, 
            ['TalentTreeID'] = 1020600, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077184'),
        },
        [20005] = {
            ['AttackSkillID'] = 86020010, 
            ['ClassID'] = 1200002, 
            ['CoreType'] = 4, 
            ['DeControlSkillID'] = 86022010, 
            ['ExtraordinarySkillID'] = 86024010, 
            ['ID'] = 20005, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 86025010, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86021030, 86021100, 86021110, 86021080}, 
            ['TalentTreeID'] = 1020301, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077440'),
        },
        [30001] = {
            ['AttackSkillID'] = 86030010, 
            ['ClassID'] = 1200003, 
            ['CoreType'] = 1, 
            ['DeControlSkillID'] = 86032010, 
            ['ExtraordinarySkillID'] = 86034010, 
            ['ID'] = 30001, 
            ['IsDefautSkill'] = 1, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86031010, 86031020, 86031080, 86031060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110074880'),
        },
        [30002] = {
            ['AttackSkillID'] = 86030010, 
            ['ClassID'] = 1200003, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86032010, 
            ['ExtraordinarySkillID'] = 86034010, 
            ['ID'] = 30002, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86031010, 86031020, 86031080, 86031060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076672'),
        },
        [30003] = {
            ['AttackSkillID'] = 86030010, 
            ['ClassID'] = 1200003, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86032010, 
            ['ExtraordinarySkillID'] = 86034010, 
            ['ID'] = 30003, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86031010, 86031020, 86031080, 86031060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076928'),
        },
        [30004] = {
            ['AttackSkillID'] = 86030010, 
            ['ClassID'] = 1200003, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86032010, 
            ['ExtraordinarySkillID'] = 86034010, 
            ['ID'] = 30004, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86031010, 86031020, 86031080, 86031060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077184'),
        },
        [30005] = {
            ['AttackSkillID'] = 86030010, 
            ['ClassID'] = 1200003, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86032010, 
            ['ExtraordinarySkillID'] = 86034010, 
            ['ID'] = 30005, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86031010, 86031020, 86031080, 86031060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077440'),
        },
        [30006] = {
            ['AttackSkillID'] = 86030010, 
            ['ClassID'] = 1200003, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86032010, 
            ['ExtraordinarySkillID'] = 86034010, 
            ['ID'] = 30006, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86031010, 86031020, 86031080, 86031060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110078976'),
        },
        [40001] = {
            ['AttackSkillID'] = 86040001, 
            ['ClassID'] = 1200004, 
            ['CoreType'] = 2, 
            ['DeControlSkillID'] = 86040005, 
            ['ExtraordinarySkillID'] = 0, 
            ['ID'] = 40001, 
            ['IsDefautSkill'] = 1, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86040006, 86040007, 86040009, 86040008, 86040013}, 
            ['TalentTreeID'] = 1040901, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110074880'),
        },
        [40002] = {
            ['AttackSkillID'] = 86040001, 
            ['ClassID'] = 1200004, 
            ['CoreType'] = 5, 
            ['DeControlSkillID'] = 86040005, 
            ['ExtraordinarySkillID'] = 0, 
            ['ID'] = 40002, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86040006, 86040007, 86040009, 86040008, 86040013}, 
            ['TalentTreeID'] = 1040600, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076672'),
        },
        [40003] = {
            ['AttackSkillID'] = 86040001, 
            ['ClassID'] = 1200004, 
            ['CoreType'] = 5, 
            ['DeControlSkillID'] = 86040005, 
            ['ExtraordinarySkillID'] = 0, 
            ['ID'] = 40003, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86040006, 86040007, 86040009, 86040008, 86040013}, 
            ['TalentTreeID'] = 1040901, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076928'),
        },
        [40004] = {
            ['AttackSkillID'] = 86040001, 
            ['ClassID'] = 1200004, 
            ['CoreType'] = 5, 
            ['DeControlSkillID'] = 86040005, 
            ['ExtraordinarySkillID'] = 0, 
            ['ID'] = 40004, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86040006, 86040007, 86040009, 86040008, 86040013}, 
            ['TalentTreeID'] = 1040600, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077184'),
        },
        [40005] = {
            ['AttackSkillID'] = 86040001, 
            ['ClassID'] = 1200004, 
            ['CoreType'] = 5, 
            ['DeControlSkillID'] = 86040005, 
            ['ExtraordinarySkillID'] = 0, 
            ['ID'] = 40005, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86040006, 86040007, 86040009, 86040008, 86040013}, 
            ['TalentTreeID'] = 1040901, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077440'),
        },
        [40006] = {
            ['AttackSkillID'] = 86040001, 
            ['ClassID'] = 1200004, 
            ['CoreType'] = 5, 
            ['DeControlSkillID'] = 86040005, 
            ['ExtraordinarySkillID'] = 0, 
            ['ID'] = 40006, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86040006, 86040007, 86040009, 86040008, 86040013}, 
            ['TalentTreeID'] = 1040600, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110078976'),
        },
        [50001] = {
            ['AttackSkillID'] = 86050010, 
            ['ClassID'] = 1200005, 
            ['CoreType'] = 1, 
            ['DeControlSkillID'] = 86052010, 
            ['ExtraordinarySkillID'] = 86054010, 
            ['ID'] = 50001, 
            ['IsDefautSkill'] = 1, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378516224'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000003, 4001001, 4001003, 4001004}, 
            ['SequenceSkillIDList'] = {86051030, 86051050, 86051010, 86051020}, 
            ['TalentTreeID'] = 1050101, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110080768'),
        },
        [50002] = {
            ['AttackSkillID'] = 86050010, 
            ['ClassID'] = 1200005, 
            ['CoreType'] = 1, 
            ['DeControlSkillID'] = 86052010, 
            ['ExtraordinarySkillID'] = 86054010, 
            ['ID'] = 50002, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378516480'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000003, 4001001, 4001003, 4001004}, 
            ['SequenceSkillIDList'] = {86051030, 86051050, 86051010, 86051020}, 
            ['TalentTreeID'] = 1050200, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110081024'),
        },
        [50003] = {
            ['AttackSkillID'] = 86050010, 
            ['ClassID'] = 1200005, 
            ['CoreType'] = 1, 
            ['DeControlSkillID'] = 86052010, 
            ['ExtraordinarySkillID'] = 86054010, 
            ['ID'] = 50003, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378516736'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000003, 4001001, 4001003, 4001004}, 
            ['SequenceSkillIDList'] = {86051030, 86051050, 86051010, 86051020}, 
            ['TalentTreeID'] = 1050101, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110081280'),
        },
        [60001] = {
            ['AttackSkillID'] = 86060010, 
            ['ClassID'] = 1200006, 
            ['CoreType'] = 7, 
            ['DeControlSkillID'] = 86062010, 
            ['ExtraordinarySkillID'] = 86064010, 
            ['ID'] = 60001, 
            ['IsDefautSkill'] = 1, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86061010, 86061030, 86061080, 86063010}, 
            ['TalentTreeID'] = 1060701, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110074880'),
        },
        [60002] = {
            ['AttackSkillID'] = 86060010, 
            ['ClassID'] = 1200006, 
            ['CoreType'] = 7, 
            ['DeControlSkillID'] = 86062010, 
            ['ExtraordinarySkillID'] = 86064010, 
            ['ID'] = 60002, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86061010, 86061030, 86061080, 86063010}, 
            ['TalentTreeID'] = 1060400, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076672'),
        },
        [60003] = {
            ['AttackSkillID'] = 86060010, 
            ['ClassID'] = 1200006, 
            ['CoreType'] = 7, 
            ['DeControlSkillID'] = 86062010, 
            ['ExtraordinarySkillID'] = 86064010, 
            ['ID'] = 60003, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86061010, 86061030, 86061080, 86063010}, 
            ['TalentTreeID'] = 1060701, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076928'),
        },
        [70001] = {
            ['AttackSkillID'] = 86070010, 
            ['ClassID'] = 1200007, 
            ['CoreType'] = 1, 
            ['DeControlSkillID'] = 86072010, 
            ['ExtraordinarySkillID'] = 86074010, 
            ['ID'] = 70001, 
            ['IsDefautSkill'] = 1, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86071010, 86071020, 86071030, 86071060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110074880'),
        },
        [70002] = {
            ['AttackSkillID'] = 86070010, 
            ['ClassID'] = 1200007, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86072010, 
            ['ExtraordinarySkillID'] = 86074010, 
            ['ID'] = 70002, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86071010, 86071020, 86071030, 86071060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076672'),
        },
        [70003] = {
            ['AttackSkillID'] = 86070010, 
            ['ClassID'] = 1200007, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86072010, 
            ['ExtraordinarySkillID'] = 86074010, 
            ['ID'] = 70003, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86071010, 86071020, 86071030, 86071060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110076928'),
        },
        [70004] = {
            ['AttackSkillID'] = 86070010, 
            ['ClassID'] = 1200007, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86072010, 
            ['ExtraordinarySkillID'] = 86074010, 
            ['ID'] = 70004, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86071010, 86071020, 86071030, 86071060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077184'),
        },
        [70005] = {
            ['AttackSkillID'] = 86070010, 
            ['ClassID'] = 1200007, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86072010, 
            ['ExtraordinarySkillID'] = 86074010, 
            ['ID'] = 70005, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86071010, 86071020, 86071030, 86071060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110077440'),
        },
        [70006] = {
            ['AttackSkillID'] = 86070010, 
            ['ClassID'] = 1200007, 
            ['CoreType'] = 8, 
            ['DeControlSkillID'] = 86072010, 
            ['ExtraordinarySkillID'] = 86074010, 
            ['ID'] = 70006, 
            ['IsDefautSkill'] = 0, 
            ['PartnerSkillIDList'] = {}, 
            ['PresetDescr'] = Game.TableDataManager:GetLangStr('str_47761378510336'),
            ['ReviveSkillID'] = 0, 
            ['SealedList'] = {4000001, 4000002, 4000003, 4000004}, 
            ['SequenceSkillIDList'] = {86071010, 86071020, 86071030, 86071060}, 
            ['TalentTreeID'] = 0, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_47761110078976'),
        },
    }
}
return TopData