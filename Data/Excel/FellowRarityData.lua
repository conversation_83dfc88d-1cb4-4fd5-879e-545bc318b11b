--
-- 表名: $Fellow.xlsx  页名：$FellowRarity
--

local TopData = {
	data = {
		[1] = {
			["RarityId"] = 1,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_30649691932416'),
			["LvExp"] = "",
			["LvUpCE"] = "",
		},
		[2] = {
			["RarityId"] = 2,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_56282056755456'),
			["LvExp"] = "",
			["LvUpCE"] = "",
		},
		[3] = {
			["RarityId"] = 3,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_30649691932928'),
			["LvExp"] = "RExp",
			["LvUpCE"] = "RLvUpCE",
		},
		[4] = {
			["RarityId"] = 4,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_30649691933184'),
			["LvExp"] = "SRExp",
			["LvUpCE"] = "SRLvUpCE",
		},
		[5] = {
			["RarityId"] = 5,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_30649691933440'),
			["LvExp"] = "SSRExp",
			["LvUpCE"] = "SSRLvUpCE",
		},
		[6] = {
			["RarityId"] = 6,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_30649691933696'),
			["LvExp"] = "URExp",
			["LvUpCE"] = "URLvUpCE",
		},
	},
}

return TopData
