--
-- 表名: $Fellow.xlsx  页名：$FellowRarity
--

local TopData = {
	data = {
		[1] = {
			["RarityId"] = 1,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_22472074200576'),
			["LvExp"] = "",
			["LvUpCE"] = "",
		},
		[2] = {
			["RarityId"] = 2,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_22472074200832'),
			["LvExp"] = "",
			["LvUpCE"] = "",
		},
		[3] = {
			["RarityId"] = 3,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_22472074201088'),
			["LvExp"] = "RExp",
			["LvUpCE"] = "RLvUpCE",
		},
		[4] = {
			["RarityId"] = 4,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_22472074201344'),
			["LvExp"] = "SRExp",
			["LvUpCE"] = "SRLvUpCE",
		},
		[5] = {
			["RarityId"] = 5,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_22472074201600'),
			["LvExp"] = "SSRExp",
			["LvUpCE"] = "SSRLvUpCE",
		},
		[6] = {
			["RarityId"] = 6,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_22472074201856'),
			["LvExp"] = "URExp",
			["LvUpCE"] = "URLvUpCE",
		},
	},
}

return TopData
