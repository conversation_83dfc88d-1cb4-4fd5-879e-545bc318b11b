--
-- 表名: $Fellow.xlsx  页名：$FellowRarity
--

local TopData = {
	data = {
		[1] = {
			["RarityId"] = 1,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_31336886699776'),
			["LvExp"] = "",
			["LvUpCE"] = "",
		},
		[2] = {
			["RarityId"] = 2,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_31336886700032'),
			["LvExp"] = "",
			["LvUpCE"] = "",
		},
		[3] = {
			["RarityId"] = 3,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_31336886700288'),
			["LvExp"] = "RExp",
			["LvUpCE"] = "RLvUpCE",
		},
		[4] = {
			["RarityId"] = 4,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_31336886700544'),
			["LvExp"] = "SRExp",
			["LvUpCE"] = "SRLvUpCE",
		},
		[5] = {
			["RarityId"] = 5,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_31336886700800'),
			["LvExp"] = "SSRExp",
			["LvUpCE"] = "SSRLvUpCE",
		},
		[6] = {
			["RarityId"] = 6,
			["RarityName"] = Game.TableDataManager:GetLangStr('str_31336886701056'),
			["LvExp"] = "URExp",
			["LvUpCE"] = "URLvUpCE",
		},
	},
}

return TopData
