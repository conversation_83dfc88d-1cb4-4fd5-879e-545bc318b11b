--
-- 表名: SequenceData后处理
--

local TopData = {
    ActRuleIDToClassSeqIdMap = {
        [1] = {[1200001] = 9, }, 
        [2] = {[1200001] = 9, }, 
        [3] = {[1200001] = 9, }, 
        [4] = {[1200001] = 8, }, 
        [5] = {[1200001] = 8, }, 
        [6] = {[1200001] = 8, }, 
        [7] = {[1200001] = 7, }, 
        [8] = {[1200001] = 7, }, 
        [9] = {[1200001] = 7, }, 
        [10] = {[1200001] = 6, }, 
        [11] = {[1200001] = 6, }, 
        [12] = {[1200001] = 6, }, 
        [13] = {[1200001] = 5, }, 
        [14] = {[1200001] = 5, }, 
        [15] = {[1200001] = 5, }, 
        [16] = {[1200001] = 4, }, 
        [17] = {[1200001] = 4, }, 
        [18] = {[1200001] = 4, }, 
        [19] = {[1200002] = 9, }, 
        [20] = {[1200002] = 9, }, 
        [21] = {[1200002] = 9, }, 
        [22] = {[1200002] = 8, }, 
        [23] = {[1200002] = 8, }, 
        [24] = {[1200002] = 8, }, 
        [25] = {[1200002] = 7, }, 
        [26] = {[1200002] = 7, }, 
        [27] = {[1200002] = 7, }, 
        [28] = {[1200002] = 6, }, 
        [29] = {[1200002] = 6, [1200003] = 9, }, 
        [30] = {[1200002] = 6, }, 
        [31] = {[1200002] = 5, }, 
        [32] = {[1200002] = 5, }, 
        [33] = {[1200002] = 5, }, 
        [34] = {[1200002] = 4, }, 
        [35] = {[1200002] = 4, }, 
        [36] = {[1200002] = 4, }, 
        [37] = {[1200003] = 9, }, 
        [38] = {[1200003] = 9, }, 
        [40] = {[1200003] = 8, }, 
        [41] = {[1200003] = 8, }, 
        [43] = {[1200003] = 7, }, 
        [44] = {[1200003] = 7, }, 
        [45] = {[1200003] = 7, }, 
        [46] = {[1200003] = 7, }, 
        [47] = {[1200003] = 6, }, 
        [48] = {[1200003] = 6, }, 
        [49] = {[1200003] = 5, }, 
        [50] = {[1200003] = 5, }, 
        [51] = {[1200003] = 5, }, 
        [52] = {[1200003] = 4, }, 
        [53] = {[1200004] = 9, }, 
        [54] = {[1200004] = 9, }, 
        [55] = {[1200004] = 9, }, 
        [56] = {[1200004] = 8, }, 
        [57] = {[1200004] = 8, }, 
        [58] = {[1200004] = 8, }, 
        [59] = {[1200004] = 7, }, 
        [60] = {[1200004] = 7, }, 
        [61] = {[1200004] = 7, }, 
        [62] = {[1200004] = 6, }, 
        [63] = {[1200004] = 6, }, 
        [64] = {[1200004] = 6, }, 
        [65] = {[1200004] = 5, }, 
        [66] = {[1200004] = 5, }, 
        [67] = {[1200004] = 5, }, 
        [68] = {[1200004] = 4, }, 
        [69] = {[1200004] = 4, }, 
        [70] = {[1200004] = 4, }, 
        [71] = {[1200005] = 9, }, 
        [72] = {[1200005] = 9, }, 
        [73] = {[1200005] = 9, }, 
        [74] = {[1200005] = 8, }, 
        [75] = {[1200005] = 8, }, 
        [76] = {[1200005] = 8, }, 
        [77] = {[1200005] = 7, }, 
        [78] = {[1200005] = 7, }, 
        [79] = {[1200005] = 7, }, 
        [80] = {[1200005] = 6, }, 
        [81] = {[1200005] = 6, }, 
        [82] = {[1200005] = 6, }, 
        [83] = {[1200005] = 5, }, 
        [84] = {[1200005] = 5, }, 
        [85] = {[1200005] = 5, }, 
        [86] = {[1200005] = 5, }, 
        [87] = {[1200005] = 4, }, 
        [88] = {[1200005] = 4, }, 
        [89] = {[1200005] = 4, }, 
        [90] = {[1200006] = 9, }, 
        [91] = {[1200006] = 9, }, 
        [92] = {[1200006] = 9, }, 
        [93] = {[1200006] = 8, }, 
        [94] = {[1200006] = 8, }, 
        [95] = {[1200006] = 8, }, 
        [96] = {[1200006] = 7, }, 
        [97] = {[1200006] = 7, }, 
        [98] = {[1200006] = 7, }, 
        [99] = {[1200006] = 6, }, 
        [100] = {[1200006] = 6, }, 
        [101] = {[1200006] = 6, }, 
        [102] = {[1200006] = 5, }, 
        [103] = {[1200006] = 5, }, 
        [104] = {[1200006] = 5, }, 
        [105] = {[1200006] = 4, }, 
        [106] = {[1200006] = 4, }, 
        [107] = {[1200006] = 4, }, 
    },
    ClassIDToActRuleMap = {
        [1200001] = {{1, 2, 3}, {4, 5, 6}, {7, 8, 9}, {10, 11, 12}, {13, 14, 15}, {16, 17, 18}, {}, {}, {}, {}, }, 
        [1200002] = {[11] = {19, 20, 21}, [12] = {22, 23, 24}, [13] = {25, 26, 27}, [14] = {28, 29, 30}, [15] = {31, 32, 33}, [16] = {34, 35, 36}, [17] = {}, [18] = {}, [19] = {}, [20] = {}, }, 
        [1200003] = {[21] = {37, 38, 29}, [22] = {40, 41}, [23] = {41, 43, 44, 45, 46}, [24] = {47, 48}, [25] = {49, 50, 51}, [26] = {52}, [27] = {}, [28] = {}, [29] = {}, [30] = {}, }, 
        [1200004] = {[31] = {53, 54, 55}, [32] = {56, 57, 58}, [33] = {59, 60, 61}, [34] = {62, 63, 64}, [35] = {65, 66, 67}, [36] = {68, 69, 70}, [37] = {}, [38] = {}, [39] = {}, [40] = {}, }, 
        [1200005] = {[41] = {71, 72, 73}, [42] = {74, 75, 76}, [43] = {77, 78, 79}, [44] = {80, 81, 82}, [45] = {83, 84, 85, 86}, [46] = {87, 88, 89}, [47] = {}, [48] = {}, [49] = {}, [50] = {}, }, 
        [1200006] = {[51] = {90, 91, 92}, [52] = {93, 94, 95}, [53] = {96, 97, 98}, [54] = {99, 100, 101}, [55] = {102, 103, 104}, [56] = {105, 106, 107}, [57] = {}, [58] = {}, [59] = {}, [60] = {}, }, 
    },
    DigestionId2SequenceIdMap = {
        [1200001] = {[1] = 1, [2] = 1, [3] = 1, [4] = 1, [11] = 2, [12] = 2, [13] = 2, [21] = 3, [22] = 3, [23] = 3, [24] = 3, [25] = 3, [26] = 3, [31] = 4, [32] = 4, [33] = 4, [34] = 4, [35] = 4, [41] = 5, [42] = 5, [43] = 5, [51] = 6, [52] = 6, [53] = 6, [61] = 7, [62] = 7, [63] = 7, [71] = 8, [72] = 8, [73] = 8, [81] = 9, [82] = 9, [83] = 9, [91] = 10, [92] = 10, [93] = 10, }, 
        [1200002] = {[101] = 11, [102] = 11, [103] = 11, [104] = 11, [111] = 12, [112] = 12, [113] = 12, [121] = 13, [122] = 13, [123] = 13, [124] = 13, [125] = 13, [126] = 13, [131] = 14, [132] = 14, [133] = 14, [134] = 14, [135] = 14, [141] = 15, [142] = 15, [143] = 15, [151] = 16, [152] = 16, [153] = 16, [161] = 17, [162] = 17, [163] = 17, [171] = 18, [172] = 18, [173] = 18, [181] = 19, [182] = 19, [183] = 19, [191] = 20, [192] = 20, [193] = 20, }, 
        [1200003] = {[201] = 21, [202] = 21, [203] = 21, [204] = 21, [211] = 22, [212] = 22, [213] = 22, [221] = 23, [222] = 23, [223] = 23, [224] = 23, [225] = 23, [226] = 23, [231] = 24, [232] = 24, [233] = 24, [234] = 24, [235] = 24, [241] = 25, [242] = 25, [243] = 25, [251] = 26, [252] = 26, [253] = 26, [261] = 27, [262] = 27, [263] = 27, [271] = 28, [272] = 28, [273] = 28, [281] = 29, [282] = 29, [283] = 29, [291] = 30, [292] = 30, [293] = 30, }, 
        [1200004] = {[301] = 31, [302] = 31, [303] = 31, [304] = 31, [311] = 32, [312] = 32, [313] = 32, [321] = 33, [322] = 33, [323] = 33, [324] = 33, [325] = 33, [326] = 33, [331] = 34, [332] = 34, [333] = 34, [334] = 34, [335] = 34, [341] = 35, [342] = 35, [343] = 35, [351] = 36, [352] = 36, [353] = 36, [361] = 37, [362] = 37, [363] = 37, [371] = 38, [372] = 38, [373] = 38, [381] = 39, [382] = 39, [383] = 39, [391] = 40, [392] = 40, [393] = 40, }, 
        [1200005] = {[401] = 41, [402] = 41, [403] = 41, [404] = 41, [411] = 42, [412] = 42, [413] = 42, [421] = 43, [422] = 43, [423] = 43, [424] = 43, [425] = 43, [426] = 43, [431] = 44, [432] = 44, [433] = 44, [434] = 44, [435] = 44, [441] = 45, [442] = 45, [443] = 45, [451] = 46, [452] = 46, [453] = 46, [461] = 47, [462] = 47, [463] = 47, [471] = 48, [472] = 48, [473] = 48, [481] = 49, [482] = 49, [483] = 49, [491] = 50, [492] = 50, [493] = 50, }, 
        [1200006] = {[501] = 51, [502] = 51, [503] = 51, [504] = 51, [511] = 52, [512] = 52, [513] = 52, [521] = 53, [522] = 53, [523] = 53, [524] = 53, [525] = 53, [526] = 53, [531] = 54, [532] = 54, [533] = 54, [534] = 54, [535] = 54, [541] = 55, [542] = 55, [543] = 55, [551] = 56, [552] = 56, [553] = 56, [561] = 57, [562] = 57, [563] = 57, [571] = 58, [572] = 58, [573] = 58, [581] = 59, [582] = 59, [583] = 59, [591] = 60, [592] = 60, [593] = 60, }, 
    },
    RingID2SequenceIdMap = {
        [1200001] = {[0] = 1, [960001] = 2, [960002] = 3, [960003] = 4, [960004] = 5, [960005] = 6, [960006] = 7, [960007] = 8, [960008] = 9, [960009] = 10, }, 
        [1200002] = {[0] = 11, [960011] = 12, [960012] = 13, [960013] = 14, [960014] = 15, [960015] = 16, [960016] = 17, [960017] = 18, [960018] = 19, [960019] = 20, }, 
        [1200003] = {[0] = 21, [960021] = 22, [960022] = 23, [960023] = 24, [960024] = 25, [960025] = 26, [960026] = 27, [960027] = 28, [960028] = 29, [960029] = 30, }, 
        [1200004] = {[0] = 31, [960031] = 32, [960032] = 33, [960033] = 34, [960034] = 35, [960035] = 36, [960036] = 37, [960037] = 38, [960038] = 39, [960039] = 40, }, 
        [1200005] = {[960040] = 41, [960041] = 42, [960042] = 43, [960043] = 44, [960044] = 45, [960045] = 46, [960046] = 47, [960047] = 48, [960048] = 49, [960049] = 50, }, 
        [1200006] = {[0] = 51, [960051] = 52, [960052] = 53, [960053] = 54, [960054] = 55, [960055] = 56, [960056] = 57, [960057] = 58, [960058] = 59, [960059] = 60, }, 
    },
    SeqIDIndexMap = {
        [1200001] = {11, 12, 13, 21, 22, 23, 24, 25, 26, 31, 32, 33, 34, 35, 41, 42, 43, 51, 52, 53, 61, 62, 63, 71, 72, 73, 91, 92, 93, 81, 82, 83, 1, 2, 3, 4}, 
        [1200002] = {111, 112, 113, 131, 132, 133, 134, 135, 151, 152, 153, 191, 192, 193, 161, 162, 163, 101, 102, 103, 104, 121, 122, 123, 124, 125, 126, 141, 142, 143, 171, 172, 173, 181, 182, 183}, 
        [1200003] = {261, 262, 263, 231, 232, 233, 234, 235, 271, 272, 273, 201, 202, 203, 204, 241, 242, 243, 281, 282, 283, 211, 212, 213, 251, 252, 253, 291, 292, 293, 221, 222, 223, 224, 225, 226}, 
        [1200004] = {371, 372, 373, 311, 312, 313, 391, 392, 393, 321, 322, 323, 324, 325, 326, 331, 332, 333, 334, 335, 341, 342, 343, 351, 352, 353, 381, 382, 383, 361, 362, 363, 301, 302, 303, 304}, 
        [1200005] = {471, 472, 473, 401, 402, 403, 404, 481, 482, 483, 411, 412, 413, 491, 492, 493, 431, 432, 433, 434, 435, 461, 462, 463, 451, 452, 453, 421, 422, 423, 424, 425, 426, 441, 442, 443}, 
        [1200006] = {531, 532, 533, 534, 535, 551, 552, 553, 561, 562, 563, 571, 572, 573, 501, 502, 503, 504, 581, 582, 583, 541, 542, 543, 511, 512, 513, 591, 592, 593, 521, 522, 523, 524, 525, 526}, 
    },
    SequenceName2ClassIDMap = {
        ['Apprentice'] = 1200005, 
        ['Arbiter'] = 1200004, 
        ['Astrologer'] = 1200005, 
        ['Attendant of Mysteries'] = 1200003, 
        ['Author'] = 1200002, 
        ['Balancer'] = 1200004, 
        ['Bard'] = 1200001, 
        ['Bizarro Sorcerer'] = 1200003, 
        ['Chaos Hunter'] = 1200004, 
        ['Clown'] = 1200003, 
        ['Dawn Paladin'] = 1200006, 
        ['Demon Hunter'] = 1200006, 
        ['Discerner'] = 1200002, 
        ['Disciplinary Paladin'] = 1200004, 
        ['Door'] = 1200005, 
        ['Dreamwalker'] = 1200002, 
        ['Dreamweaver'] = 1200002, 
        ['Faceless'] = 1200003, 
        ['Glory'] = 1200006, 
        ['Guardian'] = 1200006, 
        ['Hand of God'] = 1200006, 
        ['Hand of Order'] = 1200004, 
        ['Hypnotist'] = 1200002, 
        ['Imperative Mage'] = 1200004, 
        ['Interrogator'] = 1200004, 
        ['Judge'] = 1200004, 
        ['Justice Mentor'] = 1200001, 
        ['Justiciar'] = 1200004, 
        ['Key of Stars'] = 1200005, 
        ['Light Suppliant'] = 1200001, 
        ['Lightseeker'] = 1200001, 
        ['Magician'] = 1200003, 
        ['Manipulator'] = 1200002, 
        ['Marionettist'] = 1200003, 
        ['Miracle Invoker'] = 1200003, 
        ['Notary'] = 1200001, 
        ['Planeswalker'] = 1200005, 
        ['Priest of Light'] = 1200001, 
        ['Psychiatrist'] = 1200002, 
        ['Pugilist'] = 1200006, 
        ['Scholar of Yore'] = 1200003, 
        ['Scribe'] = 1200005, 
        ['Secrets Sorcerer'] = 1200005, 
        ['Seer'] = 1200003, 
        ['Sheriff'] = 1200004, 
        ['Silver Knight'] = 1200006, 
        ['Solar High Priest'] = 1200001, 
        ['Spectator'] = 1200002, 
        ['Sun'] = 1200001, 
        ['Telepathist'] = 1200002, 
        ['The Fool'] = 1200003, 
        ['Traveler'] = 1200005, 
        ['Trickmaster'] = 1200005, 
        ['Twilight Giant'] = 1200006, 
        ['Unshadowed'] = 1200001, 
        ['Visionary'] = 1200002, 
        ['Wanderer'] = 1200005, 
        ['Warrior'] = 1200006, 
        ['Weapon Master'] = 1200006, 
        ['White Angel'] = 1200001, 
    },
    data = {
        [1] = {
            ['ActRule'] = {1, 2, 3}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {1, 2, 3, 4}, 
            ['DigestionNum'] = 4, 
            ['EnglishName'] = 'Bard', 
            ['ID'] = 1, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 2, 
            ['PropIncr'] = {}, 
            ['RecipeID'] = 0, 
            ['RingID'] = 0, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377737728'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431360'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_40477382411776'),
            ['SequenceStage'] = 9, 
        },
        [2] = {
            ['ActRule'] = {4, 5, 6}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {11, 12, 13}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Light Suppliant', 
            ['ID'] = 2, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 3, 
            ['PropIncr'] = {['Atk_N'] = 11, }, 
            ['RecipeID'] = 2, 
            ['RingID'] = 960001, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377737984'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431616'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589803776'),
            ['SequenceStage'] = 8, 
        },
        [3] = {
            ['ActRule'] = {7, 8, 9}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {21, 22, 23, 24, 25, 26}, 
            ['DigestionNum'] = 6, 
            ['EnglishName'] = 'Solar High Priest', 
            ['ID'] = 3, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 4, 
            ['PropIncr'] = {['Atk_N'] = 12, }, 
            ['RecipeID'] = 3, 
            ['RingID'] = 960002, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377738240'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431872'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589804032'),
            ['SequenceStage'] = 7, 
        },
        [4] = {
            ['ActRule'] = {10, 11, 12}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {31, 32, 33, 34, 35}, 
            ['DigestionNum'] = 5, 
            ['EnglishName'] = 'Notary', 
            ['ID'] = 4, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 5, 
            ['PropIncr'] = {['Atk_N'] = 13, }, 
            ['RecipeID'] = 4, 
            ['RingID'] = 960003, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377738496'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432128'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589804288'),
            ['SequenceStage'] = 6, 
        },
        [5] = {
            ['ActRule'] = {13, 14, 15}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {41, 42, 43}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Priest of Light', 
            ['ID'] = 5, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 6, 
            ['PropIncr'] = {['Atk_N'] = 14, }, 
            ['RecipeID'] = 5, 
            ['RingID'] = 960004, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377738752'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432384'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589804544'),
            ['SequenceStage'] = 5, 
        },
        [6] = {
            ['ActRule'] = {16, 17, 18}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {51, 52, 53}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Unshadowed', 
            ['ID'] = 6, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 7, 
            ['PropIncr'] = {['Atk_N'] = 15, }, 
            ['RecipeID'] = 6, 
            ['RingID'] = 960005, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739008'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432640'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589804800'),
            ['SequenceStage'] = 4, 
        },
        [7] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {61, 62, 63}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Justice Mentor', 
            ['ID'] = 7, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 8, 
            ['PropIncr'] = {['Atk_N'] = 16, }, 
            ['RecipeID'] = 7, 
            ['RingID'] = 960006, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432896'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589805056'),
            ['SequenceStage'] = 3, 
        },
        [8] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {71, 72, 73}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Lightseeker', 
            ['ID'] = 8, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 9, 
            ['PropIncr'] = {['Atk_N'] = 17, }, 
            ['RecipeID'] = 8, 
            ['RingID'] = 960007, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433152'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589805312'),
            ['SequenceStage'] = 2, 
        },
        [9] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {81, 82, 83}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'White Angel', 
            ['ID'] = 9, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 10, 
            ['PropIncr'] = {['Atk_N'] = 18, }, 
            ['RecipeID'] = 9, 
            ['RingID'] = 960008, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433408'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589805568'),
            ['SequenceStage'] = 1, 
        },
        [10] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200001, 
            ['DigestionIDList'] = {91, 92, 93}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Sun', 
            ['ID'] = 10, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 0, 
            ['PropIncr'] = {['Atk_N'] = 19, }, 
            ['RecipeID'] = 10, 
            ['RingID'] = 960009, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433664'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589805824'),
            ['SequenceStage'] = 0, 
        },
        [11] = {
            ['ActRule'] = {19, 20, 21}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {101, 102, 103, 104}, 
            ['DigestionNum'] = 4, 
            ['EnglishName'] = 'Spectator', 
            ['ID'] = 11, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 12, 
            ['PropIncr'] = {}, 
            ['RecipeID'] = 11, 
            ['RingID'] = 0, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377740288'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_AudienceLogo.NS_AudienceLogo', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X11_Sprite.UI_Promotion_Icon_X11_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431360'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_40477382412288'),
            ['SequenceStage'] = 9, 
        },
        [12] = {
            ['ActRule'] = {22, 23, 24}, 
            ['BridgeTaskID'] = 6431106, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {111, 112, 113}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Telepathist', 
            ['ID'] = 12, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 13, 
            ['PropIncr'] = {['MaxHp_N'] = 88, }, 
            ['RecipeID'] = 12, 
            ['RingID'] = 960011, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377740544'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_MindReader_Log01.NS_MindReader_Log01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X17_Sprite.UI_Promotion_Icon_X17_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431616'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589806336'),
            ['SequenceStage'] = 8, 
        },
        [13] = {
            ['ActRule'] = {25, 26, 27}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {121, 122, 123, 124, 125, 126}, 
            ['DigestionNum'] = 6, 
            ['EnglishName'] = 'Psychiatrist', 
            ['ID'] = 13, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 14, 
            ['PropIncr'] = {['MaxHp_N'] = 132, }, 
            ['RecipeID'] = 13, 
            ['RingID'] = 960012, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377740800'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_Psychologist_Log01.NS_Psychologist_Log01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X9_Sprite.UI_Promotion_Icon_X9_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431872'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589806592'),
            ['SequenceStage'] = 7, 
        },
        [14] = {
            ['ActRule'] = {28, 29, 30}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {131, 132, 133, 134, 135}, 
            ['DigestionNum'] = 5, 
            ['EnglishName'] = 'Hypnotist', 
            ['ID'] = 14, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 15, 
            ['PropIncr'] = {['MaxHp_N'] = 264, }, 
            ['RecipeID'] = 14, 
            ['RingID'] = 960013, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377741056'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_HypnotistLog01.NS_HypnotistLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X3_Sprite.UI_Promotion_Icon_X3_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432128'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589806848'),
            ['SequenceStage'] = 6, 
        },
        [15] = {
            ['ActRule'] = {31, 32, 33}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {141, 142, 143}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Dreamwalker', 
            ['ID'] = 15, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 16, 
            ['PropIncr'] = {['MaxHp_N'] = 528, }, 
            ['RecipeID'] = 15, 
            ['RingID'] = 960014, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377741312'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432384'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_38759663929344'),
            ['SequenceStage'] = 5, 
        },
        [16] = {
            ['ActRule'] = {34, 35, 36}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {151, 152, 153}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Manipulator', 
            ['ID'] = 16, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 17, 
            ['PropIncr'] = {['MaxHp_N'] = 1056, }, 
            ['RecipeID'] = 16, 
            ['RingID'] = 960015, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377741568'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432640'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589807360'),
            ['SequenceStage'] = 4, 
        },
        [17] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {161, 162, 163}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Dreamweaver', 
            ['ID'] = 17, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 18, 
            ['PropIncr'] = {['MaxHp_N'] = 1689, }, 
            ['RecipeID'] = 17, 
            ['RingID'] = 960016, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432896'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589807616'),
            ['SequenceStage'] = 3, 
        },
        [18] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {171, 172, 173}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Discerner', 
            ['ID'] = 18, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 19, 
            ['PropIncr'] = {['MaxHp_N'] = 2702, }, 
            ['RecipeID'] = 18, 
            ['RingID'] = 960017, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433152'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589807872'),
            ['SequenceStage'] = 2, 
        },
        [19] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {181, 182, 183}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Author', 
            ['ID'] = 19, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 20, 
            ['PropIncr'] = {['MaxHp_N'] = 4323, }, 
            ['RecipeID'] = 19, 
            ['RingID'] = 960018, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433408'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589808128'),
            ['SequenceStage'] = 1, 
        },
        [20] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200002, 
            ['DigestionIDList'] = {191, 192, 193}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Visionary', 
            ['ID'] = 20, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 0, 
            ['PropIncr'] = {['MaxHp_N'] = 6196, }, 
            ['RecipeID'] = 20, 
            ['RingID'] = 960019, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433664'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_59787018507776'),
            ['SequenceStage'] = 0, 
        },
        [21] = {
            ['ActRule'] = {37, 38, 29}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {201, 202, 203, 204}, 
            ['DigestionNum'] = 4, 
            ['EnglishName'] = 'Seer', 
            ['ID'] = 21, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 22, 
            ['PropIncr'] = {}, 
            ['RecipeID'] = 0, 
            ['RingID'] = 0, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377742848'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_FortuneTeller_Log01.NS_FortuneTeller_Log01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X6_Sprite.UI_Promotion_Icon_X6_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431360'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_40477382412800'),
            ['SequenceStage'] = 9, 
        },
        [22] = {
            ['ActRule'] = {40, 41}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {211, 212, 213}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Clown', 
            ['ID'] = 22, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 23, 
            ['PropIncr'] = {['Atk_N'] = 11, }, 
            ['RecipeID'] = 22, 
            ['RingID'] = 960021, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377743104'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_Clown_Log01.NS_Clown_Log01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X8_Sprite.UI_Promotion_Icon_X8_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431616'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_38071663864576'),
            ['SequenceStage'] = 8, 
        },
        [23] = {
            ['ActRule'] = {41, 43, 44, 45, 46}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {221, 222, 223, 224, 225, 226}, 
            ['DigestionNum'] = 6, 
            ['EnglishName'] = 'Magician', 
            ['ID'] = 23, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 24, 
            ['PropIncr'] = {['Atk_N'] = 12, }, 
            ['RecipeID'] = 23, 
            ['RingID'] = 960022, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377743360'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X1_Sprite.UI_Promotion_Icon_X1_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431872'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_38071663866368'),
            ['SequenceStage'] = 7, 
        },
        [24] = {
            ['ActRule'] = {47, 48}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {231, 232, 233, 234, 235}, 
            ['DigestionNum'] = 5, 
            ['EnglishName'] = 'Faceless', 
            ['ID'] = 24, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 25, 
            ['PropIncr'] = {['Atk_N'] = 13, }, 
            ['RecipeID'] = 24, 
            ['RingID'] = 960023, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377743616'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_NoFaceLogo.NS_NoFaceLogo', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X14_Sprite.UI_Promotion_Icon_X14_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432128'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589809408'),
            ['SequenceStage'] = 6, 
        },
        [25] = {
            ['ActRule'] = {49, 50, 51}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {241, 242, 243}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Marionettist', 
            ['ID'] = 25, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 26, 
            ['PropIncr'] = {['Atk_N'] = 14, }, 
            ['RecipeID'] = 25, 
            ['RingID'] = 960024, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377743872'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432384'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589809664'),
            ['SequenceStage'] = 5, 
        },
        [26] = {
            ['ActRule'] = {52}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {251, 252, 253}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Bizarro Sorcerer', 
            ['ID'] = 26, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 27, 
            ['PropIncr'] = {['Atk_N'] = 15, }, 
            ['RecipeID'] = 26, 
            ['RingID'] = 960025, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377744128'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432640'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589809920'),
            ['SequenceStage'] = 4, 
        },
        [27] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {261, 262, 263}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Scholar of Yore', 
            ['ID'] = 27, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 28, 
            ['PropIncr'] = {['Atk_N'] = 16, }, 
            ['RecipeID'] = 27, 
            ['RingID'] = 960026, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432896'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589810176'),
            ['SequenceStage'] = 3, 
        },
        [28] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {271, 272, 273}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Miracle Invoker', 
            ['ID'] = 28, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 29, 
            ['PropIncr'] = {['Atk_N'] = 17, }, 
            ['RecipeID'] = 28, 
            ['RingID'] = 960027, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433152'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589810432'),
            ['SequenceStage'] = 2, 
        },
        [29] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {281, 282, 283}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Attendant of Mysteries', 
            ['ID'] = 29, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 30, 
            ['PropIncr'] = {['Atk_N'] = 18, }, 
            ['RecipeID'] = 29, 
            ['RingID'] = 960028, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433408'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589810688'),
            ['SequenceStage'] = 1, 
        },
        [30] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200003, 
            ['DigestionIDList'] = {291, 292, 293}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'The Fool', 
            ['ID'] = 30, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 0, 
            ['PropIncr'] = {['Atk_N'] = 19, }, 
            ['RecipeID'] = 30, 
            ['RingID'] = 960029, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433664'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589810944'),
            ['SequenceStage'] = 0, 
        },
        [31] = {
            ['ActRule'] = {53, 54, 55}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {301, 302, 303, 304}, 
            ['DigestionNum'] = 4, 
            ['EnglishName'] = 'Arbiter', 
            ['ID'] = 31, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 32, 
            ['PropIncr'] = {}, 
            ['RecipeID'] = 0, 
            ['RingID'] = 0, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377745408'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431360'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_40477382413312'),
            ['SequenceStage'] = 9, 
        },
        [32] = {
            ['ActRule'] = {56, 57, 58}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {311, 312, 313}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Sheriff', 
            ['ID'] = 32, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 33, 
            ['PropIncr'] = {['Atk_N'] = 11, }, 
            ['RecipeID'] = 32, 
            ['RingID'] = 960031, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377745664'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431616'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589811456'),
            ['SequenceStage'] = 8, 
        },
        [33] = {
            ['ActRule'] = {59, 60, 61}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {321, 322, 323, 324, 325, 326}, 
            ['DigestionNum'] = 6, 
            ['EnglishName'] = 'Interrogator', 
            ['ID'] = 33, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 34, 
            ['PropIncr'] = {['Atk_N'] = 12, }, 
            ['RecipeID'] = 33, 
            ['RingID'] = 960032, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377745920'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431872'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589811712'),
            ['SequenceStage'] = 7, 
        },
        [34] = {
            ['ActRule'] = {62, 63, 64}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {331, 332, 333, 334, 335}, 
            ['DigestionNum'] = 5, 
            ['EnglishName'] = 'Judge', 
            ['ID'] = 34, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 35, 
            ['PropIncr'] = {['Atk_N'] = 13, }, 
            ['RecipeID'] = 34, 
            ['RingID'] = 960033, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377746176'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432128'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589811968'),
            ['SequenceStage'] = 6, 
        },
        [35] = {
            ['ActRule'] = {65, 66, 67}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {341, 342, 343}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Disciplinary Paladin', 
            ['ID'] = 35, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 36, 
            ['PropIncr'] = {['Atk_N'] = 14, }, 
            ['RecipeID'] = 35, 
            ['RingID'] = 960034, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377746432'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432384'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589812224'),
            ['SequenceStage'] = 5, 
        },
        [36] = {
            ['ActRule'] = {68, 69, 70}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {351, 352, 353}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Imperative Mage', 
            ['ID'] = 36, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 37, 
            ['PropIncr'] = {['Atk_N'] = 15, }, 
            ['RecipeID'] = 36, 
            ['RingID'] = 960035, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377746688'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432640'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589812480'),
            ['SequenceStage'] = 4, 
        },
        [37] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {361, 362, 363}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Chaos Hunter', 
            ['ID'] = 37, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 38, 
            ['PropIncr'] = {['Atk_N'] = 16, }, 
            ['RecipeID'] = 37, 
            ['RingID'] = 960036, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432896'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589812736'),
            ['SequenceStage'] = 3, 
        },
        [38] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {371, 372, 373}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Balancer', 
            ['ID'] = 38, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 39, 
            ['PropIncr'] = {['Atk_N'] = 17, }, 
            ['RecipeID'] = 38, 
            ['RingID'] = 960037, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433152'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589812992'),
            ['SequenceStage'] = 2, 
        },
        [39] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {381, 382, 383}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Hand of Order', 
            ['ID'] = 39, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 40, 
            ['PropIncr'] = {['Atk_N'] = 18, }, 
            ['RecipeID'] = 39, 
            ['RingID'] = 960038, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433408'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589813248'),
            ['SequenceStage'] = 1, 
        },
        [40] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200004, 
            ['DigestionIDList'] = {391, 392, 393}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Justiciar', 
            ['ID'] = 40, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 0, 
            ['PropIncr'] = {['Atk_N'] = 19, }, 
            ['RecipeID'] = 40, 
            ['RingID'] = 960039, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433664'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589813504'),
            ['SequenceStage'] = 0, 
        },
        [41] = {
            ['ActRule'] = {71, 72, 73}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {401, 402, 403, 404}, 
            ['DigestionNum'] = 4, 
            ['EnglishName'] = 'Apprentice', 
            ['ID'] = 41, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 42, 
            ['PropIncr'] = {}, 
            ['RecipeID'] = 41, 
            ['RingID'] = 960040, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377747968'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_ApprenticeLogo.NS_ApprenticeLogo', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X15_Sprite.UI_Promotion_Icon_X15_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431360'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_40477382413824'),
            ['SequenceStage'] = 9, 
        },
        [42] = {
            ['ActRule'] = {74, 75, 76}, 
            ['BridgeTaskID'] = 6434106, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {411, 412, 413}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Trickmaster', 
            ['ID'] = 42, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 43, 
            ['PropIncr'] = {['MaxHp_N'] = 88, }, 
            ['RecipeID'] = 42, 
            ['RingID'] = 960041, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377748224'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_MasterOfTricks_Log01.NS_MasterOfTricks_Log01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X5_Sprite.UI_Promotion_Icon_X5_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431616'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589814016'),
            ['SequenceStage'] = 8, 
        },
        [43] = {
            ['ActRule'] = {77, 78, 79}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {421, 422, 423, 424, 425, 426}, 
            ['DigestionNum'] = 6, 
            ['EnglishName'] = 'Astrologer', 
            ['ID'] = 43, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 44, 
            ['PropIncr'] = {['MaxHp_N'] = 132, }, 
            ['RecipeID'] = 43, 
            ['RingID'] = 960042, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377748480'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_Astrologer_Log01.NS_Astrologer_Log01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X7_Sprite.UI_Promotion_Icon_X7_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431872'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589814272'),
            ['SequenceStage'] = 7, 
        },
        [44] = {
            ['ActRule'] = {80, 81, 82}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {431, 432, 433, 434, 435}, 
            ['DigestionNum'] = 5, 
            ['EnglishName'] = 'Scribe', 
            ['ID'] = 44, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 45, 
            ['PropIncr'] = {['MaxHp_N'] = 264, }, 
            ['RecipeID'] = 44, 
            ['RingID'] = 960043, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377748736'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_RecorderLogo.NS_RecorderLogo', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X12_Sprite.UI_Promotion_Icon_X12_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432128'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589814528'),
            ['SequenceStage'] = 6, 
        },
        [45] = {
            ['ActRule'] = {83, 84, 85, 86}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {441, 442, 443}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Traveler', 
            ['ID'] = 45, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 46, 
            ['PropIncr'] = {['MaxHp_N'] = 528, }, 
            ['RecipeID'] = 45, 
            ['RingID'] = 960044, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377748992'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432384'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_38071663866112'),
            ['SequenceStage'] = 5, 
        },
        [46] = {
            ['ActRule'] = {87, 88, 89}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {451, 452, 453}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Secrets Sorcerer', 
            ['ID'] = 46, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 47, 
            ['PropIncr'] = {['MaxHp_N'] = 1056, }, 
            ['RecipeID'] = 46, 
            ['RingID'] = 960045, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377749248'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432640'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589815040'),
            ['SequenceStage'] = 4, 
        },
        [47] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {461, 462, 463}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Wanderer', 
            ['ID'] = 47, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 48, 
            ['PropIncr'] = {['MaxHp_N'] = 1689, }, 
            ['RecipeID'] = 47, 
            ['RingID'] = 960046, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432896'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589815296'),
            ['SequenceStage'] = 3, 
        },
        [48] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {471, 472, 473}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Planeswalker', 
            ['ID'] = 48, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 49, 
            ['PropIncr'] = {['MaxHp_N'] = 2702, }, 
            ['RecipeID'] = 48, 
            ['RingID'] = 960047, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433152'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589815552'),
            ['SequenceStage'] = 2, 
        },
        [49] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {481, 482, 483}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Key of Stars', 
            ['ID'] = 49, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 50, 
            ['PropIncr'] = {['MaxHp_N'] = 4323, }, 
            ['RecipeID'] = 49, 
            ['RingID'] = 960048, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433408'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589815808'),
            ['SequenceStage'] = 1, 
        },
        [50] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200005, 
            ['DigestionIDList'] = {491, 492, 493}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Door', 
            ['ID'] = 50, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 0, 
            ['PropIncr'] = {['MaxHp_N'] = 6196, }, 
            ['RecipeID'] = 50, 
            ['RingID'] = 960049, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433664'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589816064'),
            ['SequenceStage'] = 0, 
        },
        [51] = {
            ['ActRule'] = {90, 91, 92}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {501, 502, 503, 504}, 
            ['DigestionNum'] = 4, 
            ['EnglishName'] = 'Warrior', 
            ['ID'] = 51, 
            ['IsLock'] = false, 
            ['NextSeqID'] = 52, 
            ['PropIncr'] = {}, 
            ['RecipeID'] = 51, 
            ['RingID'] = 0, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377750528'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_Warrior_Log01.NS_Warrior_Log01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X10_Sprite.UI_Promotion_Icon_X10_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431360'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_40477382414336'),
            ['SequenceStage'] = 9, 
        },
        [52] = {
            ['ActRule'] = {93, 94, 95}, 
            ['BridgeTaskID'] = 6435106, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {511, 512, 513}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Pugilist', 
            ['ID'] = 52, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 53, 
            ['PropIncr'] = {['MaxHp_N'] = 88, }, 
            ['RecipeID'] = 52, 
            ['RingID'] = 960051, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377750784'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_GladiatorLogo.NS_GladiatorLogo', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X16_Sprite.UI_Promotion_Icon_X16_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431616'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589816576'),
            ['SequenceStage'] = 8, 
        },
        [53] = {
            ['ActRule'] = {96, 97, 98}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {521, 522, 523, 524, 525, 526}, 
            ['DigestionNum'] = 6, 
            ['EnglishName'] = 'Weapon Master', 
            ['ID'] = 53, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 54, 
            ['PropIncr'] = {['MaxHp_N'] = 132, }, 
            ['RecipeID'] = 53, 
            ['RingID'] = 960052, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377751040'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_MasterOfArmsLog01.NS_MasterOfArmsLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X4_Sprite.UI_Promotion_Icon_X4_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572431872'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589816832'),
            ['SequenceStage'] = 7, 
        },
        [54] = {
            ['ActRule'] = {99, 100, 101}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {531, 532, 533, 534, 535}, 
            ['DigestionNum'] = 5, 
            ['EnglishName'] = 'Dawn Paladin', 
            ['ID'] = 54, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 55, 
            ['PropIncr'] = {['MaxHp_N'] = 264, }, 
            ['RecipeID'] = 54, 
            ['RingID'] = 960053, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377751296'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_DawnKnightLogo.NS_DawnKnightLogo', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion_2/Atlas/Sprite01/UI_Promotion_Icon_X13_Sprite.UI_Promotion_Icon_X13_Sprite', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432128'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589817088'),
            ['SequenceStage'] = 6, 
        },
        [55] = {
            ['ActRule'] = {102, 103, 104}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {541, 542, 543}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Guardian', 
            ['ID'] = 55, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 56, 
            ['PropIncr'] = {['MaxHp_N'] = 528, }, 
            ['RecipeID'] = 55, 
            ['RingID'] = 960054, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377751552'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432384'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_38071663866880'),
            ['SequenceStage'] = 5, 
        },
        [56] = {
            ['ActRule'] = {105, 106, 107}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {551, 552, 553}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Demon Hunter', 
            ['ID'] = 56, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 57, 
            ['PropIncr'] = {['MaxHp_N'] = 1056, }, 
            ['RecipeID'] = 56, 
            ['RingID'] = 960055, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377751808'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432640'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589817600'),
            ['SequenceStage'] = 4, 
        },
        [57] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {561, 562, 563}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Silver Knight', 
            ['ID'] = 57, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 58, 
            ['PropIncr'] = {['MaxHp_N'] = 1689, }, 
            ['RecipeID'] = 57, 
            ['RingID'] = 960056, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572432896'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589817856'),
            ['SequenceStage'] = 3, 
        },
        [58] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {571, 572, 573}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Glory', 
            ['ID'] = 58, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 59, 
            ['PropIncr'] = {['MaxHp_N'] = 2702, }, 
            ['RecipeID'] = 58, 
            ['RingID'] = 960057, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433152'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589818112'),
            ['SequenceStage'] = 2, 
        },
        [59] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {581, 582, 583}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Hand of God', 
            ['ID'] = 59, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 60, 
            ['PropIncr'] = {['MaxHp_N'] = 4323, }, 
            ['RecipeID'] = 59, 
            ['RingID'] = 960058, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog01.NS_PromotionLog01', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433408'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_16975589818368'),
            ['SequenceStage'] = 1, 
        },
        [60] = {
            ['ActRule'] = {}, 
            ['BridgeTaskID'] = 0, 
            ['ClassID'] = 1200006, 
            ['DigestionIDList'] = {591, 592, 593}, 
            ['DigestionNum'] = 3, 
            ['EnglishName'] = 'Twilight Giant', 
            ['ID'] = 60, 
            ['IsLock'] = true, 
            ['NextSeqID'] = 0, 
            ['PropIncr'] = {['MaxHp_N'] = 6196, }, 
            ['RecipeID'] = 60, 
            ['RingID'] = 960059, 
            ['SequenceDesc'] = Game.TableDataManager:GetLangStr('str_51474377739264'),
            ['SequenceEffectLogo'] = '/Game/Arts/Effects/System/promotion/NS_PromotionLog02.NS_PromotionLog02', 
            ['SequenceIcon'] = '/Game/Arts/UI_2/Resource/Promotion/Atlas/Texture01/UI_Promotion_Icon_Iconlinshi.UI_Promotion_Icon_Iconlinshi', 
            ['SequenceIndex'] = Game.TableDataManager:GetLangStr('str_51473572433664'),
            ['SequenceName'] = Game.TableDataManager:GetLangStr('str_59787018507520'),
            ['SequenceStage'] = 0, 
        },
    }
}
return TopData