--
-- 表名: $MiniGame_迷你游戏.xlsx  页名：$JigsawPuzzle_圆盘解密
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Texture"] = "/Game/Arts/UI_2/Resource/MiniGame/NotAtlas/UI_MiniGame_Img_Ring_Picture03.UI_MiniGame_Img_Ring_Picture03",
			["InitialRotate"] = {270, 180, 225},
			["Hint"] = Game.TableDataManager:GetLangStr('str_35872909035008'),
		},
		[2] = {
			["ID"] = 2,
			["Texture"] = "/Game/Arts/UI_2/Resource/MiniGame/NotAtlas/UI_MiniGame_Img_Ring_Picture02.UI_MiniGame_Img_Ring_Picture02",
			["InitialRotate"] = {270, 180, 225},
			["Hint"] = Game.TableDataManager:GetLangStr('str_35872909035008'),
		},
		[3] = {
			["ID"] = 3,
			["Texture"] = "/Game/Arts/UI_2/Resource/MiniGame/NotAtlas/UI_MiniGame_Img_Ring_Picture01.UI_MiniGame_Img_Ring_Picture01",
			["InitialRotate"] = {270, 180, 225},
			["Hint"] = Game.TableDataManager:GetLangStr('str_35872909035008'),
		},
		[4] = {
			["ID"] = 4,
			["Texture"] = "/Game/Arts/UI_2/Resource/MiniGame/NotAtlas/UI_MiniGame_Img_Ring_Picture04.UI_MiniGame_Img_Ring_Picture04",
			["InitialRotate"] = {270, 180, 225},
			["Hint"] = Game.TableDataManager:GetLangStr('str_35872909035008'),
		},
	},
}

return TopData
