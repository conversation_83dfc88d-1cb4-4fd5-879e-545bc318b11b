--
-- 表名: $Mount_坐骑表.xlsx  页名：$MountTypeEnum_坐骑大类枚举
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["SubtypeEnum"] = "Vehicle",
			["SubtypeValue"] = 1,
			["DisplayId"] = 0,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume01_Sprite.UI_Fashion_Icon_Costume01_Sprite",
			["ButtonName"] = Game.TableDataManager:GetLangStr('str_59236994273280'),
		},
		[2] = {
			["ID"] = 2,
			["SubtypeEnum"] = "spectacle",
			["SubtypeValue"] = 2,
			["DisplayId"] = 0,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume01_Sprite.UI_Fashion_Icon_Costume01_Sprite",
			["ButtonName"] = Game.TableDataManager:GetLangStr('str_37179115964160'),
		},
	},
}

return TopData
