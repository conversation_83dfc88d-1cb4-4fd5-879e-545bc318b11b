--
-- 表名: EleTalentTreeNode后处理
--

local TopData = {
    data = {
        [10502001] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 10502001, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = '', 
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = -1, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [101030101] = {
            ['ChildNode'] = {101030102}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 101030101, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [101030102] = {
            ['ChildNode'] = {101030103, 101030105, 101030111}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 101030102, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 101030101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2}, 
        },
        [101030103] = {
            ['ChildNode'] = {101030104, 101030110, 101030116}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 101030103, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 101030102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [101030104] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 101030104, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 101030103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [101030105] = {
            ['ChildNode'] = {101030106}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 101030105, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 101030102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [101030106] = {
            ['ChildNode'] = {101030107}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 101030106, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 101030105, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [101030107] = {
            ['ChildNode'] = {101030108}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 101030107, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 101030106, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [101030108] = {
            ['ChildNode'] = {101030109}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 101030108, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 101030107, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [101030109] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 101030109, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 101030108, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [101030110] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 101030110, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 101030103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [101030111] = {
            ['ChildNode'] = {101030112}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 101030111, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 101030102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101030112] = {
            ['ChildNode'] = {101030113}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 101030112, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 101030111, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101030113] = {
            ['ChildNode'] = {101030114}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 101030113, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 101030112, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101030114] = {
            ['ChildNode'] = {101030115}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 101030114, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 101030113, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101030115] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 101030115, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 101030114, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101030116] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 101030116, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 101030103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101030117] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 101030117, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009101', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [101030118] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 101030118, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009102', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [101030119] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 101030119, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009103', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1010301, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [101080001] = {
            ['ChildNode'] = {101080002}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 101080001, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [101080002] = {
            ['ChildNode'] = {101080003, 101080005, 101080011}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 101080002, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 101080001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2}, 
        },
        [101080003] = {
            ['ChildNode'] = {101080004, 101080010, 101080016}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 101080003, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 101080002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [101080004] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 101080004, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 101080003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [101080005] = {
            ['ChildNode'] = {101080006}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 101080005, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 101080002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [101080006] = {
            ['ChildNode'] = {101080007}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 101080006, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 101080005, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [101080007] = {
            ['ChildNode'] = {101080008}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 101080007, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 101080006, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [101080008] = {
            ['ChildNode'] = {101080009}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 101080008, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 101080007, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [101080009] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 101080009, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 101080008, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [101080010] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 101080010, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 101080003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [101080011] = {
            ['ChildNode'] = {101080012}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 101080011, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 101080002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101080012] = {
            ['ChildNode'] = {101080013}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 101080012, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 101080011, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101080013] = {
            ['ChildNode'] = {101080014}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 101080013, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 101080012, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101080014] = {
            ['ChildNode'] = {101080015}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 101080014, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 101080013, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101080015] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 101080015, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 101080014, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101080016] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 101080016, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 101080003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [101080017] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 101080017, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009104', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [101080018] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 101080018, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009105', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [101080019] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 101080019, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009106', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1010800, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [102030101] = {
            ['ChildNode'] = {102030102, 102030111, 102030114}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 102030101, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [102030102] = {
            ['ChildNode'] = {102030103, 102030115}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 102030102, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 102030101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2}, 
        },
        [102030103] = {
            ['ChildNode'] = {102030104, 102030105}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 102030103, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 102030102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [102030104] = {
            ['ChildNode'] = {102030108}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 102030104, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 102030103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [102030105] = {
            ['ChildNode'] = {102030106}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 102030105, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 102030103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [102030106] = {
            ['ChildNode'] = {102030107}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 102030106, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 102030105, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [102030107] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 102030107, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 102030106, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [102030108] = {
            ['ChildNode'] = {102030109}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 102030108, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 102030104, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [102030109] = {
            ['ChildNode'] = {102030110}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 102030109, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 102030108, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [102030110] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 102030110, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 102030109, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [102030111] = {
            ['ChildNode'] = {102030112}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 102030111, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 102030101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102030112] = {
            ['ChildNode'] = {102030113}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 102030112, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 102030111, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102030113] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 102030113, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 102030112, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102030114] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 102030114, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 102030101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102030115] = {
            ['ChildNode'] = {102030116}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 102030115, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 102030102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102030116] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 102030116, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 102030115, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102030117] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 102030117, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009101', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [102030118] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 102030118, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009102', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [102030119] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 102030119, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009103', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1020301, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [102060001] = {
            ['ChildNode'] = {102060002, 102060011, 102060014}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 102060001, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [102060002] = {
            ['ChildNode'] = {102060003, 102060015}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 102060002, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 102060001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [102060003] = {
            ['ChildNode'] = {102060004, 102060005}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 102060003, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 102060002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [102060004] = {
            ['ChildNode'] = {102060008}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 102060004, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 102060003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [102060005] = {
            ['ChildNode'] = {102060006}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 102060005, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 102060003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [102060006] = {
            ['ChildNode'] = {102060007}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 102060006, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 102060005, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [102060007] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 102060007, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 102060006, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [102060008] = {
            ['ChildNode'] = {102060009}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 102060008, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 102060004, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [102060009] = {
            ['ChildNode'] = {102060010}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 102060009, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 102060008, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [102060010] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 102060010, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 102060009, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [102060011] = {
            ['ChildNode'] = {102060012}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 102060011, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 102060001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102060012] = {
            ['ChildNode'] = {102060013}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 102060012, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 102060011, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102060013] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 102060013, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 102060012, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102060014] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 102060014, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 102060001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102060015] = {
            ['ChildNode'] = {102060016}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 102060015, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 102060002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102060016] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 102060016, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 102060015, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [102060017] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 102060017, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009104', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [102060018] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 102060018, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009105', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [102060019] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 102060019, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009106', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1020600, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [103010101] = {
            ['ChildNode'] = {103010102, 103010105, 103010111}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 103010101, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [103010102] = {
            ['ChildNode'] = {103010103, 103010106, 103010112}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 103010102, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 103010101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2}, 
        },
        [103010103] = {
            ['ChildNode'] = {103010104}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 103010103, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 103010102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [103010104] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 103010104, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 103010103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [103010105] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 103010105, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 103010101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [103010106] = {
            ['ChildNode'] = {103010107, 103010109}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 103010106, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 103010102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [103010107] = {
            ['ChildNode'] = {103010108}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 103010107, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 103010106, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [103010108] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 103010108, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 103010107, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [103010109] = {
            ['ChildNode'] = {103010110}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 103010109, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 103010106, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [103010110] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 103010110, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 103010109, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [103010111] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 103010111, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 103010101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103010112] = {
            ['ChildNode'] = {103010113}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 103010112, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 103010102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103010113] = {
            ['ChildNode'] = {103010114}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 103010113, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 103010112, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103010114] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 103010114, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 103010113, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103010115] = {
            ['ChildNode'] = {103010116}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 103010115, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103010116] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 103010116, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 103010115, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103010117] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 103010117, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009101', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [103010118] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 103010118, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009102', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [103010119] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 103010119, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009103', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1030101, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [103050001] = {
            ['ChildNode'] = {103050002, 103050005, 103050011}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 103050001, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [103050002] = {
            ['ChildNode'] = {103050003, 103050006, 103050012}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 103050002, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 103050001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [103050003] = {
            ['ChildNode'] = {103050004}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 103050003, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 103050002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [103050004] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 103050004, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 103050003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [103050005] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 103050005, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 103050001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [103050006] = {
            ['ChildNode'] = {103050007, 103050009}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 103050006, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 103050002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [103050007] = {
            ['ChildNode'] = {103050008}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 103050007, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 103050006, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [103050008] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 103050008, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 103050007, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [103050009] = {
            ['ChildNode'] = {103050010}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 103050009, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 103050006, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [103050010] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 103050010, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 103050009, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [103050011] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 103050011, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 103050001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103050012] = {
            ['ChildNode'] = {103050013, 103050015}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 103050012, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 103050002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103050013] = {
            ['ChildNode'] = {103050014}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 103050013, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 103050012, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103050014] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 103050014, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 103050013, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103050015] = {
            ['ChildNode'] = {103050016}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 103050015, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 103050012, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103050016] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 103050016, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 103050015, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [103050017] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 103050017, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009104', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [103050018] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 103050018, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009105', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [103050019] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 103050019, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009106', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1030500, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [105010101] = {
            ['ChildNode'] = {105010102}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 105010101, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [105010102] = {
            ['ChildNode'] = {105010103}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 105010102, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 105010101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2}, 
        },
        [105010103] = {
            ['ChildNode'] = {105010104}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 105010103, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 105010102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [105010104] = {
            ['ChildNode'] = {105010105, 105010111}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 105010104, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 105010103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [105010105] = {
            ['ChildNode'] = {105010106}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 105010105, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 105010104, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [105010106] = {
            ['ChildNode'] = {105010107}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 105010106, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 105010105, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [105010107] = {
            ['ChildNode'] = {105010108}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 105010107, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 105010106, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [105010108] = {
            ['ChildNode'] = {105010109}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 105010108, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 105010107, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [105010109] = {
            ['ChildNode'] = {105010110}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 105010109, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 105010108, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [105010110] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 105010110, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 105010109, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [105010111] = {
            ['ChildNode'] = {105010112}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 105010111, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 105010104, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105010112] = {
            ['ChildNode'] = {105010113, 105010115}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 105010112, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 105010111, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105010113] = {
            ['ChildNode'] = {105010114}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 105010113, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 105010112, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105010114] = {
            ['ChildNode'] = {105010115}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 105010114, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 105010113, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105010115] = {
            ['ChildNode'] = {105010116}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 105010115, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 105010114, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105010116] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 105010116, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 105010115, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105010117] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 105010117, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009101', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [105010118] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 105010118, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009102', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [105010119] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 105010119, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009103', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1050101, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [105020001] = {
            ['ChildNode'] = {105020002}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 105020001, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = -1, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [105020002] = {
            ['ChildNode'] = {105020003}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 105020002, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 105020001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 0, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [105020003] = {
            ['ChildNode'] = {105020004}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 105020003, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 105020002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 0, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [105020004] = {
            ['ChildNode'] = {105020005, 105020011}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 105020004, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 105020003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 1, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [105020005] = {
            ['ChildNode'] = {105020006}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 105020005, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 105020004, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 1, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [105020006] = {
            ['ChildNode'] = {105020007}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 105020006, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 105020005, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 1, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [105020007] = {
            ['ChildNode'] = {105020008}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 105020007, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 105020006, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [105020008] = {
            ['ChildNode'] = {105020009}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 105020008, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 105020007, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [105020009] = {
            ['ChildNode'] = {105020010}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 105020009, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 105020008, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [105020010] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 105020010, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 105020009, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 2}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [105020011] = {
            ['ChildNode'] = {105020012}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 105020011, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 105020004, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105020012] = {
            ['ChildNode'] = {105020013}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 105020012, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 105020011, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105020013] = {
            ['ChildNode'] = {105020014}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 105020013, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 105020012, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105020014] = {
            ['ChildNode'] = {105020015}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 105020014, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 105020013, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105020015] = {
            ['ChildNode'] = {105020016}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 105020015, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 105020014, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105020016] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 105020016, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 105020015, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 3}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [105020017] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 105020017, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009104', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [105020018] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 105020018, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009105', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [105020019] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 105020019, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {}, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009106', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1050200, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [106040001] = {
            ['ChildNode'] = {106040002, 106040005, 106040011}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 106040001, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [106040002] = {
            ['ChildNode'] = {106040003, 106040008, 106040014}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 106040002, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 106040001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106040003] = {
            ['ChildNode'] = {106040004}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 106040003, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 106040002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106040004] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 106040004, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 106040003, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [106040005] = {
            ['ChildNode'] = {106040006}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 106040005, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 106040001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [106040006] = {
            ['ChildNode'] = {106040007}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 106040006, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 106040005, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [106040007] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 106040007, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 106040006, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [106040008] = {
            ['ChildNode'] = {106040009}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 106040008, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 106040002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106040009] = {
            ['ChildNode'] = {106040010}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 106040009, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 106040008, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106040010] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 106040010, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 106040009, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [106040011] = {
            ['ChildNode'] = {106040012}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 106040011, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 106040001, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106040012] = {
            ['ChildNode'] = {106040013}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 106040012, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 106040011, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106040013] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 106040013, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 106040012, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106040014] = {
            ['ChildNode'] = {106040015}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 106040014, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 106040002, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106040015] = {
            ['ChildNode'] = {106040016}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 106040015, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 106040014, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106040016] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 106040016, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 106040015, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106040017] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 106040017, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009101', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [106040018] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 106040018, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009102', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [106040019] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 106040019, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009103', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1060400, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [106070101] = {
            ['ChildNode'] = {106070102, 106070105, 106070111}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 1, 
            ['Icon'] = 'TalentPoint_Crit', 
            ['Key'] = 106070101, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603648'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054073856'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500057}, 
            ['UpgradeCost'] = {1}, 
        },
        [106070102] = {
            ['ChildNode'] = {106070103, 106070108, 106070114}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 2, 
            ['Icon'] = 'TalentPoint_Def', 
            ['Key'] = 106070102, 
            ['MaxLevel'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264603904'),
            ['ParentNode'] = 106070101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074112'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010031, 1}, {1010031, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500058}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106070103] = {
            ['ChildNode'] = {106070104}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 3, 
            ['Icon'] = 'TalentPoint_Pierce', 
            ['Key'] = 106070103, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604160'),
            ['ParentNode'] = 106070102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074368'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010011, 1}, {1010011, 1}, {1010011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500059}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106070104] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 4, 
            ['Icon'] = 'TalentPoint_Atk', 
            ['Key'] = 106070104, 
            ['MaxLevel'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604416'),
            ['ParentNode'] = 106070103, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074624'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010021, 1}, {1010021, 1}, {1010021, 1}, {1010021, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {{2001000, 1}, {2001000, 2}, {2001000, 3}, {2001000, 4}, }, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500060}, 
            ['UpgradeCost'] = {1, 2, 3, 4}, 
        },
        [106070105] = {
            ['ChildNode'] = {106070106}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 5, 
            ['Icon'] = 'TalentPoint_HP', 
            ['Key'] = 106070105, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604672'),
            ['ParentNode'] = 106070101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054074880'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, {1010041, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500061}, 
            ['UpgradeCost'] = {1, 2, 3, 4, 5}, 
        },
        [106070106] = {
            ['ChildNode'] = {106070107}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 6, 
            ['Icon'] = 'TalentPoint_ElementRate', 
            ['Key'] = 106070106, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264604928'),
            ['ParentNode'] = 106070105, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075136'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011001, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500062}, 
            ['UpgradeCost'] = {1}, 
        },
        [106070107] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 7, 
            ['Icon'] = 'TalentPoint_ElementDef', 
            ['Key'] = 106070107, 
            ['MaxLevel'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_55251264605184'),
            ['ParentNode'] = 106070106, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075392'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1011011, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500063}, 
            ['UpgradeCost'] = {1}, 
        },
        [106070108] = {
            ['ChildNode'] = {106070109}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 8, 
            ['Icon'] = 'TalentPoint_IgnoreElementDef', 
            ['Key'] = 106070108, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156544'),
            ['ParentNode'] = 106070102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075648'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106070109] = {
            ['ChildNode'] = {106070110}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 9, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 106070109, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638156800'),
            ['ParentNode'] = 106070108, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054075904'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 1}, {1016101, 1}, {1016101, 1}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 3}, 
        },
        [106070110] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 10, 
            ['Icon'] = 'TalentPoint_ElementEffect1', 
            ['Key'] = 106070110, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157056'),
            ['ParentNode'] = 106070109, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076160'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 2}, {1016101, 2}, {1016101, 2}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 4}, 
        },
        [106070111] = {
            ['ChildNode'] = {106070112}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 11, 
            ['Icon'] = 'TalentPoint_ElementEffect2', 
            ['Key'] = 106070111, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157312'),
            ['ParentNode'] = 106070101, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076416'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106070112] = {
            ['ChildNode'] = {106070113}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 12, 
            ['Icon'] = 'TalentPoint_ElementEffect3', 
            ['Key'] = 106070112, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157568'),
            ['ParentNode'] = 106070111, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076672'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 2, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106070113] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 13, 
            ['Icon'] = 'TalentPoint_ElementEffect4', 
            ['Key'] = 106070113, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638157824'),
            ['ParentNode'] = 106070112, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054076928'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106070114] = {
            ['ChildNode'] = {106070115}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 14, 
            ['Icon'] = 'TalentPoint_ElementEffect5', 
            ['Key'] = 106070114, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158080'),
            ['ParentNode'] = 106070102, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077184'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106070115] = {
            ['ChildNode'] = {106070116}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 15, 
            ['Icon'] = 'TalentPoint_ElementEffect6', 
            ['Key'] = 106070115, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158336'),
            ['ParentNode'] = 106070114, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077440'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106070116] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 0, 
            ['DisplayOrder'] = 16, 
            ['Icon'] = 'TalentPoint_ElementAtk', 
            ['Key'] = 106070116, 
            ['MaxLevel'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158592'),
            ['ParentNode'] = 106070115, 
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077696'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {{1016101, 3}, {1016101, 3}, {1016101, 3}, }, 
            ['PointSkill'] = '', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {6500064}, 
            ['UpgradeCost'] = {1, 2, 5}, 
        },
        [106070117] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 17, 
            ['Icon'] = 'ElementSkill_Type', 
            ['Key'] = 106070117, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638158848'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054077952'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009101', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '1', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [106070118] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 18, 
            ['Icon'] = 'ElementSkill_Normal', 
            ['Key'] = 106070118, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159104'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078208'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009102', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '2', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
        [106070119] = {
            ['ChildNode'] = {}, 
            ['DefaultLevel'] = 1, 
            ['DisplayOrder'] = 19, 
            ['Icon'] = 'ElementSkill_Ctritick', 
            ['Key'] = 106070119, 
            ['MaxLevel'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_16699638159360'),
            ['PointDesc'] = Game.TableDataManager:GetLangStr('str_16702054078464'),
            ['PointItemReward'] = {{2001000, 1}, }, 
            ['PointPropReward'] = {}, 
            ['PointSkill'] = '80009103', 
            ['ShowType'] = 2, 
            ['Size'] = 1, 
            ['SpecialCost'] = {}, 
            ['SpecialPointID'] = '3', 
            ['TreeID'] = 1060701, 
            ['UnlockCondition'] = {}, 
            ['UpgradeCost'] = {0, 2, 3, 4, 5}, 
        },
    }
}
return TopData