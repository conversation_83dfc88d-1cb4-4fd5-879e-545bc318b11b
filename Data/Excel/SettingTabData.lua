--
-- 表名: $Setting_设置表.xlsx  页名：$Tab_设置页签
--

local TopData = {
	data = {
		[1] = {
			["OrderID"] = 1,
			["Const"] = "BASICS",
			["ParentTab"] = "",
			["Name"] = Game.TableDataManager:GetLangStr('str_54632789317632'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = false,
		},
		[2] = {
			["OrderID"] = 2,
			["Const"] = "IMAGE",
			["ParentTab"] = "",
			["Name"] = Game.TableDataManager:GetLangStr('str_52296863975168'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = true,
		},
		[3] = {
			["OrderID"] = 3,
			["Const"] = "COMBAT",
			["ParentTab"] = "",
			["Name"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = true,
		},
		[4] = {
			["OrderID"] = 4,
			["Const"] = "CONTROL",
			["ParentTab"] = "COMBAT",
			["Name"] = Game.TableDataManager:GetLangStr('str_54632789440256'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = false,
		},
		[5] = {
			["OrderID"] = 5,
			["Const"] = "DISPLAY",
			["ParentTab"] = "COMBAT",
			["Name"] = Game.TableDataManager:GetLangStr('str_52296863975936'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = false,
		},
		[6] = {
			["OrderID"] = 6,
			["Const"] = "WHEEL",
			["ParentTab"] = "COMBAT",
			["Name"] = Game.TableDataManager:GetLangStr('str_52296863976192'),
			["IsPCShow"] = false,
			["IsMobileShow"] = true,
			["TabReset"] = false,
		},
		[7] = {
			["OrderID"] = 7,
			["Const"] = "VIEW",
			["ParentTab"] = "",
			["Name"] = Game.TableDataManager:GetLangStr('str_52296863976448'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = true,
		},
		[8] = {
			["OrderID"] = 8,
			["Const"] = "SOCIAL",
			["ParentTab"] = "",
			["Name"] = Game.TableDataManager:GetLangStr('str_52296863976704'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = false,
		},
		[9] = {
			["OrderID"] = 9,
			["Const"] = "ACCOUNT",
			["ParentTab"] = "",
			["Name"] = Game.TableDataManager:GetLangStr('str_52296863976960'),
			["IsPCShow"] = true,
			["IsMobileShow"] = true,
			["TabReset"] = false,
		},
	},
}

return TopData
