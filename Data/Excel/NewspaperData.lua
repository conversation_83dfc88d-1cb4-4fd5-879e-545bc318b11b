--
-- 表名: $LetterText_书信表.xlsx  页名：$Newspaper_报纸
--

local TopData = {
	data = {
		[10000] = {
			["Key"] = 10000,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.06.28",
			["HeaderTitle"] = "<EmptyLeon>The Daily News</>",
			["HeaderContent"] = "<EmptyLeon>In today\'s Tingen City, residents were greeted with a sunny morning. People stepped out of their homes to start a busy day. Many chose to stop by Bakery to try iced tea. Meanwhile, the History Department of Tingen University is conducting a new round of faculty interviews, attracting applicants from various places. </>",
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01",
			["SubTitle1"] = "<EmptyLeon>Bakery\'s New Sweet Selling Fast</>",
			["SubContent1"] = "<EmptyLeon>Mrs. <PERSON> from the Sling Bakery recently launched a new sweet iced tea, which has been well-received by customers. The tea flavor is stronger and offers a refreshing sensation that effectively dispels the heat brought on by the blazing sun.</>",
			["SubTitle2"] = "<EmptyLeon>Urban Transportation Changes</>",
			["SubContent2"] = "<EmptyLeon>In recent years, the transportation system in Tingen City has undergone significant changes, driving urban development and enhancing the convenience of residents\' lives.Tingen\'s transport infrastructure has continuously upgraded.</>",
			["NotifyClose"] = 1,
		},
		[10001] = {
			["Key"] = 10001,
			["Type"] = 0,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.06.28",
			["HeaderTitle"] = "<EmptyLeon>The Daily News</>",
			["HeaderContent"] = "<EmptyLeon>In today\'s Tingen City, residents were greeted with a sunny morning. People stepped out of their homes to start a busy day. </>",
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01",
			["SubTitle1"] = "<EmptyLeon>GoodNews</>",
			["SubContent1"] = "<EmptyLeon>Mrs. Wendy from the Sling Bakery recently launched a new sweet iced tea, which has been well-received by customers.</>",
			["SubTitle2"] = "<EmptyLeon>Transportation</>",
			["SubContent2"] = "<EmptyLeon>In recent years, the transportation system in Tingen City has undergone significant changes.</>",
			["NotifyClose"] = 1,
		},
		[10002] = {
			["Key"] = 10002,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.06.28",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32849788930048'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057365504'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32850594236416'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862671872'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32851131107328'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32851399542784'),
			["NotifyClose"] = 1,
		},
		[10003] = {
			["Key"] = 10003,
			["Type"] = 0,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.06.28",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32849788930048'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057365760'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset02.UI_Newspaper_Img_Inset02",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32850594236416'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862672128'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32851131107328'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32851399543040'),
			["NotifyClose"] = 1,
		},
		[10004] = {
			["Key"] = 10004,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.07.28",
			["HeaderTitle"] = "<EmptyLeon>The Daily News</>",
			["HeaderContent"] = "<EmptyLeon>Women suspected of possession by the devil, hematemesis can be free, and can use the mouth with any part of the body suction out of blood! After the reporter desperate investigation, was because women suffering from oral ulcers!</>",
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32850594236928'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862672384'),
			["SubTitle2"] = "<EmptyLeon>Urban Transportation Changes</>",
			["SubContent2"] = "<EmptyLeon>In recent years, the transportation system in Tingen City has undergone significant changes, driving urban development and enhancing the convenience of residents\' lives.Tingen\'s transport infrastructure has continuously upgraded.</>",
			["NotifyClose"] = 1,
		},
		[10005] = {
			["Key"] = 10005,
			["Type"] = 0,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.07.01",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32849788930816'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057366272'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset02.UI_Newspaper_Img_Inset02",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32850594237184'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862672640'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32851131108096'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32851399543552'),
			["NotifyClose"] = 1,
		},
		[10006] = {
			["Key"] = 10006,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32848983624704'),
			["RunesName"] = "Khoy University Gazette",
			["Date"] = "1354.04.28",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32849788931072'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057366528'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32574911027200'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862672896'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32851131108352'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32851399543808'),
			["NotifyClose"] = 1,
		},
		[10007] = {
			["Key"] = 10007,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.06.28",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32849788931328'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057366784'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32850594237696'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862673152'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32851131107328'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32851399543040'),
			["NotifyClose"] = 1,
		},
		[10008] = {
			["Key"] = 10008,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32848983625216'),
			["RunesName"] = "Tiengen Daily",
			["Date"] = "1349.06.29",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32849788930048'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057367040'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Newspaper/UI_ConfigIcon_Newspaper_Img_L01.UI_ConfigIcon_Newspaper_Img_L01",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32850594237696'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862673408'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32851131108864'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32851399544320'),
		},
		[10009] = {
			["Key"] = 10009,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1354.06.28",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32851131107328'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057367296'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32850594238208'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32850862673664'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32850594236416'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32850862672128'),
			["NotifyClose"] = 1,
		},
		[10010] = {
			["Key"] = 10010,
			["Type"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574911026432'),
			["RunesName"] = "Tiengen City Honest Paper",
			["Date"] = "1349.06.28",
			["HeaderTitle"] = Game.TableDataManager:GetLangStr('str_32849788932096'),
			["HeaderContent"] = Game.TableDataManager:GetLangStr('str_32850057367552'),
			["HeaderPic"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Newspaper/UI_ConfigIcon_Newspaper_Img_L01.UI_ConfigIcon_Newspaper_Img_L01",
			["SubTitle1"] = Game.TableDataManager:GetLangStr('str_32851131108864'),
			["SubContent1"] = Game.TableDataManager:GetLangStr('str_32851399544320'),
			["SubTitle2"] = Game.TableDataManager:GetLangStr('str_32851131107328'),
			["SubContent2"] = Game.TableDataManager:GetLangStr('str_32851399542784'),
			["NotifyClose"] = 1,
		},
	},
}

return TopData
