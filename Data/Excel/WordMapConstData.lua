--
-- 表名: WordMapConstData后处理
--

local TopData = {
    data = {
        [1] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520420352'),
            ['HermesName'] = 'Jidihai', 
        },
        [2] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_44531294764544'),
            ['HermesName'] = 'Beihai', 
        },
        [3] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520420864'),
            ['HermesName'] = 'Miwuhai', 
        },
        [4] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520421120'),
            ['HermesName'] = 'Suniyahai', 
        },
        [5] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_55938459380736'),
            ['HermesName'] = 'Kuangbaohai', 
        },
        [6] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_55938459379712'),
            ['HermesName'] = 'Yindisi', 
        },
        [7] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520421888'),
            ['HermesName'] = 'Zhongnanzhuguo', 
        },
        [8] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520422144'),
            ['HermesName'] = 'Luenwangguo', 
        },
        [9] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520422400'),
            ['HermesName'] = 'Feineibotewangguo', 
        },
        [10] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520422656'),
            ['HermesName'] = 'Nandalu', 
        },
        [11] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520422912'),
            ['HermesName'] = 'Fusakediguo', 
        },
        [12] = {
            ['AreaName'] = Game.TableDataManager:GetLangStr('str_32161520423168'),
            ['HermesName'] = 'Suniyadao', 
        },
    }
}
return TopData