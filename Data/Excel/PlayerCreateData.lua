--
-- 表名: $Player_角色.xlsx  页名：$PlayerCreate_创角
--

local TopData = {
	data = {
		[1200001] = {
			[0] = {
				["ClassID"] = 1200001,
				["Sex"] = 0,
				["DefaultSex"] = true,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382411776'),
				["ClassEngName"] = "Sun",
				["IsLock"] = true,
				["DifficultyDegree"] = 0,
				["DefenceDegree"] = 0,
				["MovementDegree"] = 0,
				["AssistanceDegree"] = 0,
				["RangeDegree"] = 0,
				["DamageDegree"] = 0,
				["ClassTitle"] = "",
				["ClassDescribe"] = "",
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Eulogist_Sprite.UI_CreateRole_Icon_Eulogist_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type04_Sprite.UI_CreateRole_Icon_Type04_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType02.UI_CreateRole_Icon_HalfType02",
				["CustomRoleFacade"] = 7730301,
				["CreateRoleFacade"] = 7700000,
				["WholeModel"] = 7710000,
				["WholeModelSelect"] = 7740000,
			},
			[1] = {
				["ClassID"] = 1200001,
				["Sex"] = 1,
				["DefaultSex"] = false,
				["IsSexOpen"] = 2,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382411776'),
				["ClassEngName"] = "Sun",
				["IsLock"] = true,
				["DifficultyDegree"] = 0,
				["DefenceDegree"] = 0,
				["MovementDegree"] = 0,
				["AssistanceDegree"] = 0,
				["RangeDegree"] = 0,
				["DamageDegree"] = 0,
				["ClassTitle"] = "",
				["ClassDescribe"] = "",
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Eulogist_Sprite.UI_CreateRole_Icon_Eulogist_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type04_Sprite.UI_CreateRole_Icon_Type04_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType02.UI_CreateRole_Icon_HalfType02",
				["CustomRoleFacade"] = 7730301,
				["CreateRoleFacade"] = 7700000,
				["WholeModel"] = 7710000,
				["WholeModelSelect"] = 7740000,
			},
		},
		[1200002] = {
			[0] = {
				["ClassID"] = 1200002,
				["Sex"] = 0,
				["DefaultSex"] = false,
				["IsSexOpen"] = 2,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["ClassEngName"] = "Visionary",
				["IsLock"] = false,
				["DifficultyDegree"] = 1,
				["DefenceDegree"] = 4,
				["MovementDegree"] = 2,
				["AssistanceDegree"] = 5,
				["RangeDegree"] = 5,
				["DamageDegree"] = 2,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798331392'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066766848'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Audience_Sprite.UI_CreateRole_Icon_Audience_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type05_Sprite.UI_CreateRole_Icon_Type05_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType03.UI_CreateRole_Icon_HalfType03",
				["CustomRoleFacade"] = 7730202,
				["CreateRoleFacade"] = 7700001,
				["WholeModel"] = 7740009,
				["WholeModelSelect"] = 7740009,
			},
			[1] = {
				["ClassID"] = 1200002,
				["Sex"] = 1,
				["DefaultSex"] = true,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["ClassEngName"] = "Visionary",
				["IsLock"] = false,
				["DifficultyDegree"] = 1,
				["DefenceDegree"] = 4,
				["MovementDegree"] = 2,
				["AssistanceDegree"] = 5,
				["RangeDegree"] = 5,
				["DamageDegree"] = 2,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798331392'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066766848'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Audience_Sprite.UI_CreateRole_Icon_Audience_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type05_Sprite.UI_CreateRole_Icon_Type05_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType03.UI_CreateRole_Icon_HalfType03",
				["CustomRoleFacade"] = 7730201,
				["CreateRoleFacade"] = 7700001,
				["WholeModel"] = 7710001,
				["WholeModelSelect"] = 7740001,
			},
		},
		[1200003] = {
			[0] = {
				["ClassID"] = 1200003,
				["Sex"] = 0,
				["DefaultSex"] = true,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382412800'),
				["ClassEngName"] = "Fool",
				["IsLock"] = false,
				["DifficultyDegree"] = 3,
				["DefenceDegree"] = 1,
				["MovementDegree"] = 3,
				["AssistanceDegree"] = 1,
				["RangeDegree"] = 5,
				["DamageDegree"] = 4,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798331904'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066767360'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_FortuneTeller_Sprite.UI_CreateRole_Icon_FortuneTeller_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type02_Sprite.UI_CreateRole_Icon_Type02_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType05.UI_CreateRole_Icon_HalfType05",
				["CustomRoleFacade"] = 7730401,
				["CreateRoleFacade"] = 7700002,
				["WholeModel"] = 7710002,
				["WholeModelSelect"] = 7740002,
			},
			[1] = {
				["ClassID"] = 1200003,
				["Sex"] = 1,
				["DefaultSex"] = false,
				["IsSexOpen"] = 2,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382412800'),
				["ClassEngName"] = "Fool",
				["IsLock"] = false,
				["DifficultyDegree"] = 3,
				["DefenceDegree"] = 1,
				["MovementDegree"] = 3,
				["AssistanceDegree"] = 1,
				["RangeDegree"] = 5,
				["DamageDegree"] = 4,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798331904'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066767360'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_FortuneTeller_Sprite.UI_CreateRole_Icon_FortuneTeller_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type02_Sprite.UI_CreateRole_Icon_Type02_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType05.UI_CreateRole_Icon_HalfType05",
				["CustomRoleFacade"] = 7730010,
				["CreateRoleFacade"] = 7700002,
				["WholeModel"] = 7740006,
				["WholeModelSelect"] = 7740006,
			},
		},
		[1200004] = {
			[0] = {
				["ClassID"] = 1200004,
				["Sex"] = 0,
				["DefaultSex"] = false,
				["IsSexOpen"] = 2,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382413312'),
				["ClassEngName"] = "Arbiter",
				["IsLock"] = true,
				["DifficultyDegree"] = 0,
				["DefenceDegree"] = 0,
				["MovementDegree"] = 0,
				["AssistanceDegree"] = 0,
				["RangeDegree"] = 0,
				["DamageDegree"] = 0,
				["ClassTitle"] = "",
				["ClassDescribe"] = "",
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Arbitrator_Sprite.UI_CreateRole_Icon_Arbitrator_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type03_Sprite.UI_CreateRole_Icon_Type03_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType04.UI_CreateRole_Icon_HalfType04",
				["CustomRoleFacade"] = 7730101,
				["CreateRoleFacade"] = 7700003,
				["WholeModel"] = 7710003,
				["WholeModelSelect"] = 7740003,
			},
			[1] = {
				["ClassID"] = 1200004,
				["Sex"] = 1,
				["DefaultSex"] = true,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382413312'),
				["ClassEngName"] = "Arbiter",
				["IsLock"] = true,
				["DifficultyDegree"] = 0,
				["DefenceDegree"] = 0,
				["MovementDegree"] = 0,
				["AssistanceDegree"] = 0,
				["RangeDegree"] = 0,
				["DamageDegree"] = 0,
				["ClassTitle"] = "",
				["ClassDescribe"] = "",
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Arbitrator_Sprite.UI_CreateRole_Icon_Arbitrator_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type03_Sprite.UI_CreateRole_Icon_Type03_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType04.UI_CreateRole_Icon_HalfType04",
				["CustomRoleFacade"] = 7730101,
				["CreateRoleFacade"] = 7700003,
				["WholeModel"] = 7710003,
				["WholeModelSelect"] = 7740003,
			},
		},
		[1200005] = {
			[0] = {
				["ClassID"] = 1200005,
				["Sex"] = 0,
				["DefaultSex"] = false,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382413824'),
				["ClassEngName"] = "Apprentice",
				["IsLock"] = false,
				["DifficultyDegree"] = 5,
				["DefenceDegree"] = 1,
				["MovementDegree"] = 5,
				["AssistanceDegree"] = 1,
				["RangeDegree"] = 2,
				["DamageDegree"] = 5,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798332928'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066768384'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Apprentice_Sprite.UI_CreateRole_Icon_Apprentice_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type06_Sprite.UI_CreateRole_Icon_Type06_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType06.UI_CreateRole_Icon_HalfType06",
				["CustomRoleFacade"] = 7730009,
				["CreateRoleFacade"] = 7700004,
				["WholeModel"] = 7740007,
				["WholeModelSelect"] = 7740007,
			},
			[1] = {
				["ClassID"] = 1200005,
				["Sex"] = 1,
				["DefaultSex"] = true,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382413824'),
				["ClassEngName"] = "Apprentice",
				["IsLock"] = false,
				["DifficultyDegree"] = 5,
				["DefenceDegree"] = 1,
				["MovementDegree"] = 5,
				["AssistanceDegree"] = 1,
				["RangeDegree"] = 2,
				["DamageDegree"] = 5,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798332928'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066768384'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Apprentice_Sprite.UI_CreateRole_Icon_Apprentice_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type06_Sprite.UI_CreateRole_Icon_Type06_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType06.UI_CreateRole_Icon_HalfType06",
				["CustomRoleFacade"] = 7730005,
				["CreateRoleFacade"] = 7700004,
				["WholeModel"] = 7710004,
				["WholeModelSelect"] = 7740004,
			},
		},
		[1200006] = {
			[0] = {
				["ClassID"] = 1200006,
				["Sex"] = 0,
				["DefaultSex"] = true,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382414336'),
				["ClassEngName"] = "Warrior",
				["IsLock"] = false,
				["DifficultyDegree"] = 2,
				["DefenceDegree"] = 5,
				["MovementDegree"] = 2,
				["AssistanceDegree"] = 3,
				["RangeDegree"] = 1,
				["DamageDegree"] = 3,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798333440'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066768896'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Warrior_Sprite.UI_CreateRole_Icon_Warrior_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type01_Sprite.UI_CreateRole_Icon_Type01_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType01.UI_CreateRole_Icon_HalfType01",
				["CustomRoleFacade"] = 7730007,
				["CreateRoleFacade"] = 7700005,
				["WholeModel"] = 7710005,
				["WholeModelSelect"] = 7740005,
			},
			[1] = {
				["ClassID"] = 1200006,
				["Sex"] = 1,
				["DefaultSex"] = false,
				["IsSexOpen"] = 2,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382414336'),
				["ClassEngName"] = "Warrior",
				["IsLock"] = false,
				["DifficultyDegree"] = 2,
				["DefenceDegree"] = 5,
				["MovementDegree"] = 2,
				["AssistanceDegree"] = 3,
				["RangeDegree"] = 1,
				["DamageDegree"] = 3,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798333440'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066768896'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Warrior_Sprite.UI_CreateRole_Icon_Warrior_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type01_Sprite.UI_CreateRole_Icon_Type01_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType01.UI_CreateRole_Icon_HalfType01",
				["CustomRoleFacade"] = 7730501,
				["CreateRoleFacade"] = 7700006,
				["WholeModel"] = 7710008,
				["WholeModelSelect"] = 7740008,
			},
		},
		[1200007] = {
			[0] = {
				["ClassID"] = 1200007,
				["Sex"] = 0,
				["DefaultSex"] = false,
				["IsSexOpen"] = 2,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382414848'),
				["ClassEngName"] = "Seer",
				["IsLock"] = true,
				["DifficultyDegree"] = 3,
				["DefenceDegree"] = 2,
				["MovementDegree"] = 2,
				["AssistanceDegree"] = 2,
				["RangeDegree"] = 5,
				["DamageDegree"] = 4,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798333952'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066769408'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Seer_Sprite.UI_CreateRole_Icon_Seer_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type07_Sprite.UI_CreateRole_Icon_Type07_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType02.UI_CreateRole_Icon_HalfType02",
				["CustomRoleFacade"] = 7730301,
				["CreateRoleFacade"] = 7700000,
				["WholeModel"] = 7710010,
				["WholeModelSelect"] = 7710010,
			},
			[1] = {
				["ClassID"] = 1200007,
				["Sex"] = 1,
				["DefaultSex"] = true,
				["IsSexOpen"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40477382414848'),
				["ClassEngName"] = "Seer",
				["IsLock"] = true,
				["DifficultyDegree"] = 3,
				["DefenceDegree"] = 2,
				["MovementDegree"] = 2,
				["AssistanceDegree"] = 2,
				["RangeDegree"] = 5,
				["DamageDegree"] = 4,
				["ClassTitle"] = Game.TableDataManager:GetLangStr('str_40479798333952'),
				["ClassDescribe"] = Game.TableDataManager:GetLangStr('str_40480066769408'),
				["ClassLogoSelected"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Seer_Sprite.UI_CreateRole_Icon_Seer_Sprite",
				["ClassLogoLock"] = "/Game/Arts/UI_2/Resource/CreateRole_2/Atlas/Sprite01/UI_CreateRole_Icon_Type07_Sprite.UI_CreateRole_Icon_Type07_Sprite",
				["ClassLogoStyle"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_HalfType02.UI_CreateRole_Icon_HalfType02",
				["CustomRoleFacade"] = 7730301,
				["CreateRoleFacade"] = 7700000,
				["WholeModel"] = 7710011,
				["WholeModelSelect"] = 7710011,
			},
		},
	},
}

return TopData
