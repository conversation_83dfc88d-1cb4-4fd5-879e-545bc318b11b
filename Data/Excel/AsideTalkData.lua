--
-- 表名: $Task/Dialogue_旧对话总表_Design.xlsx  页名：$AsideTalk_画外音
--

local TopData = {
	data = {
		[39000001] = {
			[1] = {
				["ID"] = 39000001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408543744'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715792384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1500,
			},
		},
		[39000002] = {
			[1] = {
				["ID"] = 39000002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715792640'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1500,
			},
		},
		[39000003] = {
			[1] = {
				["ID"] = 39000003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715792896'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1500,
			},
			[2] = {
				["ID"] = 39000003,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715793152'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 3000,
			},
		},
		[39000004] = {
			[1] = {
				["ID"] = 39000004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715793408'),
				["VoiceAsset"] = "",
				["Duration"] = 1200,
				["Delay"] = 3000,
			},
		},
		[39000100] = {
			[1] = {
				["ID"] = 39000100,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715793664'),
				["VoiceAsset"] = "",
				["Duration"] = 1200,
				["Delay"] = 3000,
			},
		},
		[39000101] = {
			[1] = {
				["ID"] = 39000101,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715793920'),
				["VoiceAsset"] = "",
				["Duration"] = 1200,
				["Delay"] = 3000,
			},
		},
		[39000102] = {
			[1] = {
				["ID"] = 39000102,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715794176'),
				["VoiceAsset"] = "",
				["Duration"] = 1200,
				["Delay"] = 3000,
			},
		},
		[39000201] = {
			[1] = {
				["ID"] = 39000201,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715794432'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000202] = {
			[1] = {
				["ID"] = 39000202,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715794688'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000203] = {
			[1] = {
				["ID"] = 39000203,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715794944'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000204] = {
			[1] = {
				["ID"] = 39000204,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715795200'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000205] = {
			[1] = {
				["ID"] = 39000205,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715795456'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000206] = {
			[1] = {
				["ID"] = 39000206,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715795712'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000207] = {
			[1] = {
				["ID"] = 39000207,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715795968'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000207,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715796224'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000208] = {
			[1] = {
				["ID"] = 39000208,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715796480'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000208,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715796736'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000209] = {
			[1] = {
				["ID"] = 39000209,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715796992'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000210] = {
			[1] = {
				["ID"] = 39000210,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715797248'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000211] = {
			[1] = {
				["ID"] = 39000211,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715797504'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000212] = {
			[1] = {
				["ID"] = 39000212,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715797760'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000213] = {
			[1] = {
				["ID"] = 39000213,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715798016'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000214] = {
			[1] = {
				["ID"] = 39000214,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715798272'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000215] = {
			[1] = {
				["ID"] = 39000215,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715798528'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000216] = {
			[1] = {
				["ID"] = 39000216,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715798784'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000217] = {
			[1] = {
				["ID"] = 39000217,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715799040'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000218] = {
			[1] = {
				["ID"] = 39000218,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715799296'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000219] = {
			[1] = {
				["ID"] = 39000219,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715799552'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000220] = {
			[1] = {
				["ID"] = 39000220,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715799808'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000221] = {
			[1] = {
				["ID"] = 39000221,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715800064'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000222] = {
			[1] = {
				["ID"] = 39000222,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715800320'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000223] = {
			[1] = {
				["ID"] = 39000223,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715800576'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000224] = {
			[1] = {
				["ID"] = 39000224,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715800832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000225] = {
			[1] = {
				["ID"] = 39000225,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715801088'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000226] = {
			[1] = {
				["ID"] = 39000226,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715801344'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000227] = {
			[1] = {
				["ID"] = 39000227,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447366144'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715801600'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000228] = {
			[1] = {
				["ID"] = 39000228,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715801856'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000229] = {
			[1] = {
				["ID"] = 39000229,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447366656'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715802112'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000230] = {
			[1] = {
				["ID"] = 39000230,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715802368'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000230,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715802624'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000231] = {
			[1] = {
				["ID"] = 39000231,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715802880'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000231,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715803136'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000231,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715803392'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000232] = {
			[1] = {
				["ID"] = 39000232,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715803648'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000232,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715803904'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000232,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715804160'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000233] = {
			[1] = {
				["ID"] = 39000233,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715804416'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000233,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715804672'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000234] = {
			[1] = {
				["ID"] = 39000234,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715804928'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000234,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715805184'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000235] = {
			[1] = {
				["ID"] = 39000235,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715805440'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000235,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715805696'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000236] = {
			[1] = {
				["ID"] = 39000236,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715805952'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000236,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715806208'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000237] = {
			[1] = {
				["ID"] = 39000237,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715806464'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000238] = {
			[1] = {
				["ID"] = 39000238,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715806720'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000239] = {
			[1] = {
				["ID"] = 39000239,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715806976'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000240] = {
			[1] = {
				["ID"] = 39000240,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715807232'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000241] = {
			[1] = {
				["ID"] = 39000241,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715807488'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000242] = {
			[1] = {
				["ID"] = 39000242,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715807744'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000243] = {
			[1] = {
				["ID"] = 39000243,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715808000'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000244] = {
			[1] = {
				["ID"] = 39000244,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715808256'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000245] = {
			[1] = {
				["ID"] = 39000245,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715808512'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000245,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715808768'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000246] = {
			[1] = {
				["ID"] = 39000246,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715809024'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000246,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715809280'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000247] = {
			[1] = {
				["ID"] = 39000247,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715809536'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000248] = {
			[1] = {
				["ID"] = 39000248,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715809792'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1500,
			},
		},
		[39000249] = {
			[1] = {
				["ID"] = 39000249,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715810048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000250] = {
			[1] = {
				["ID"] = 39000250,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715810304'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000251] = {
			[1] = {
				["ID"] = 39000251,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715810560'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000252] = {
			[1] = {
				["ID"] = 39000252,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715810816'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000253] = {
			[1] = {
				["ID"] = 39000253,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715811072'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000254] = {
			[1] = {
				["ID"] = 39000254,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715811328'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000255] = {
			[1] = {
				["ID"] = 39000255,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715811584'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000256] = {
			[1] = {
				["ID"] = 39000256,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715811840'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000257] = {
			[1] = {
				["ID"] = 39000257,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447376640'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715812096'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000258] = {
			[1] = {
				["ID"] = 39000258,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715812352'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000259] = {
			[1] = {
				["ID"] = 39000259,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "?",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715812608'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000260] = {
			[1] = {
				["ID"] = 39000260,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715812864'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000260,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715813120'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000261] = {
			[1] = {
				["ID"] = 39000261,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715813376'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000262] = {
			[1] = {
				["ID"] = 39000262,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715813632'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000263] = {
			[1] = {
				["ID"] = 39000263,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715813888'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000264] = {
			[1] = {
				["ID"] = 39000264,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715814144'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000265] = {
			[1] = {
				["ID"] = 39000265,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715814400'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000266] = {
			[1] = {
				["ID"] = 39000266,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715814656'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000267] = {
			[1] = {
				["ID"] = 39000267,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447377408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715814912'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000268] = {
			[1] = {
				["ID"] = 39000268,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715815168'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000269] = {
			[1] = {
				["ID"] = 39000269,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715815424'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000270] = {
			[1] = {
				["ID"] = 39000270,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "???",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715815680'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000270,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715815936'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000271] = {
			[1] = {
				["ID"] = 39000271,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "???",
				["Text"] = Game.TableDataManager:GetLangStr('str_56764167202305'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000271,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715816448'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000273] = {
			[1] = {
				["ID"] = 39000273,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715816704'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000273,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "???",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715816960'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000273,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715817216'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 39000273,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "???",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715817472'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 39000273,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = "???",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715817728'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000274] = {
			[1] = {
				["ID"] = 39000274,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "???",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715817984'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000275] = {
			[1] = {
				["ID"] = 39000275,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590189312'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715818240'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000276] = {
			[1] = {
				["ID"] = 39000276,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715818496'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000277] = {
			[1] = {
				["ID"] = 39000277,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715818752'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000278] = {
			[1] = {
				["ID"] = 39000278,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715819008'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000301] = {
			[1] = {
				["ID"] = 39000301,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715819264'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000301_1",
				["Duration"] = 7500,
				["Delay"] = 0,
			},
		},
		[39000302] = {
			[1] = {
				["ID"] = 39000302,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715819520'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000302_1",
				["Duration"] = 5500,
				["Delay"] = 0,
			},
		},
		[39000303] = {
			[1] = {
				["ID"] = 39000303,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715819776'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000303_1",
				["Duration"] = 6500,
				["Delay"] = 0,
			},
		},
		[39000304] = {
			[1] = {
				["ID"] = 39000304,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715820032'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000304_1",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[39000305] = {
			[1] = {
				["ID"] = 39000305,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715820288'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000305_1",
				["Duration"] = 7500,
				["Delay"] = 0,
			},
		},
		[39000306] = {
			[1] = {
				["ID"] = 39000306,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715820544'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000306_1",
				["Duration"] = 8500,
				["Delay"] = 0,
			},
		},
		[39000307] = {
			[1] = {
				["ID"] = 39000307,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715820800'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000307_1",
				["Duration"] = 6500,
				["Delay"] = 0,
			},
		},
		[39000308] = {
			[1] = {
				["ID"] = 39000308,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715821056'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000308_1",
				["Duration"] = 7500,
				["Delay"] = 0,
			},
		},
		[39000309] = {
			[1] = {
				["ID"] = 39000309,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715821312'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000309_1",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000310] = {
			[1] = {
				["ID"] = 39000310,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715821568'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000310_1",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000311] = {
			[1] = {
				["ID"] = 39000311,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715821824'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000311_1",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000311,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715822080'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000311_2",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000313] = {
			[1] = {
				["ID"] = 39000313,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715822336'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000313_1",
				["Duration"] = 8000,
				["Delay"] = 0,
			},
		},
		[39000314] = {
			[1] = {
				["ID"] = 39000314,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715822592'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000314_1",
				["Duration"] = 9000,
				["Delay"] = 0,
			},
		},
		[39000315] = {
			[1] = {
				["ID"] = 39000315,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715822848'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000315_1",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
		},
		[39000316] = {
			[1] = {
				["ID"] = 39000316,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715823104'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000316_1",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000317] = {
			[1] = {
				["ID"] = 39000317,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715823360'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000317_1",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000318] = {
			[1] = {
				["ID"] = 39000318,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715823616'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000318_1",
				["Duration"] = 8000,
				["Delay"] = 0,
			},
		},
		[39000319] = {
			[1] = {
				["ID"] = 39000319,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715823872'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000319_1",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000320] = {
			[1] = {
				["ID"] = 39000320,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715824128'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000320_1",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000320,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715824384'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000320_2",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000320,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715824640'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000320_3",
				["Duration"] = 6600,
				["Delay"] = 0,
			},
		},
		[39000323] = {
			[1] = {
				["ID"] = 39000323,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715824896'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000323_1",
				["Duration"] = 5500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000323,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715825152'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000323_2",
				["Duration"] = 5500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000323,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715825408'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000323_3",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000326] = {
			[1] = {
				["ID"] = 39000326,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715825664'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000326_1",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000327] = {
			[1] = {
				["ID"] = 39000327,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715825920'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000327_1",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000327,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715826176'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000327_2",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
		},
		[39000328] = {
			[1] = {
				["ID"] = 39000328,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715826432'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000328_1",
				["Duration"] = 5500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000328,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715826688'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000328_2",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
		},
		[39000331] = {
			[1] = {
				["ID"] = 39000331,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715826944'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000331_1",
				["Duration"] = 6700,
				["Delay"] = 0,
			},
		},
		[39000332] = {
			[1] = {
				["ID"] = 39000332,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715827200'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000332_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000333] = {
			[1] = {
				["ID"] = 39000333,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715827456'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000333_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000334] = {
			[1] = {
				["ID"] = 39000334,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715827712'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000334_1",
				["Duration"] = 5600,
				["Delay"] = 0,
			},
		},
		[39000335] = {
			[1] = {
				["ID"] = 39000335,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715827968'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000335_1",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000336] = {
			[1] = {
				["ID"] = 39000336,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715828224'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000336_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000337] = {
			[1] = {
				["ID"] = 39000337,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715828480'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000337_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000337,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715828736'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000337_2",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000337,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715828992'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000337_3",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 39000337,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715829248'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000337_4",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000341] = {
			[1] = {
				["ID"] = 39000341,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715829504'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000341_1",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
		},
		[39000342] = {
			[1] = {
				["ID"] = 39000342,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715829760'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000342_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000343] = {
			[1] = {
				["ID"] = 39000343,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715830016'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000343_1",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000344] = {
			[1] = {
				["ID"] = 39000344,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715830272'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000344_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000345] = {
			[1] = {
				["ID"] = 39000345,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715830528'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000345_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000346] = {
			[1] = {
				["ID"] = 39000346,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715830784'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000346_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000347] = {
			[1] = {
				["ID"] = 39000347,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715831040'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000347_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000348] = {
			[1] = {
				["ID"] = 39000348,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715831296'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000348_1",
				["Duration"] = 4600,
				["Delay"] = 0,
			},
		},
		[39000349] = {
			[1] = {
				["ID"] = 39000349,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715831552'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_RayBiber_39000349_1",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[39000350] = {
			[1] = {
				["ID"] = 39000350,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715831808'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000350_1",
				["Duration"] = 7200,
				["Delay"] = 0,
			},
		},
		[39000351] = {
			[1] = {
				["ID"] = 39000351,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715832064'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000351_1",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000353] = {
			[1] = {
				["ID"] = 39000353,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715832320'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000353_1",
				["Duration"] = 2600,
				["Delay"] = 0,
			},
		},
		[39000352] = {
			[1] = {
				["ID"] = 39000352,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715832576'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000352_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000352,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715832832'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000352_2",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000355] = {
			[1] = {
				["ID"] = 39000355,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715833088'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000355_1",
				["Duration"] = 5200,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000355,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715833344'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000355_2",
				["Duration"] = 1500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000355,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715833600'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000355_3",
				["Duration"] = 6300,
				["Delay"] = 0,
			},
		},
		[39000358] = {
			[1] = {
				["ID"] = 39000358,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715833856'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000358_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000359] = {
			[1] = {
				["ID"] = 39000359,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715834112'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Clown_39000359_1",
				["Duration"] = 6200,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000359,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715834368'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000359_2",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
		},
		[39000361] = {
			[1] = {
				["ID"] = 39000361,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715834624'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000361_1",
				["Duration"] = 6500,
				["Delay"] = 0,
			},
		},
		[39000362] = {
			[1] = {
				["ID"] = 39000362,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715834880'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000362_1",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000362,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715835136'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000362_2",
				["Duration"] = 7800,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000362,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715835392'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000362_3",
				["Duration"] = 5300,
				["Delay"] = 0,
			},
		},
		[39000365] = {
			[1] = {
				["ID"] = 39000365,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715835648'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000366] = {
			[1] = {
				["ID"] = 39000366,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715835904'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000367] = {
			[1] = {
				["ID"] = 39000367,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715836160'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000368] = {
			[1] = {
				["ID"] = 39000368,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715836416'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000369] = {
			[1] = {
				["ID"] = 39000369,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715836672'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000370] = {
			[1] = {
				["ID"] = 39000370,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715835904'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000371] = {
			[1] = {
				["ID"] = 39000371,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715837184'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000371,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715824640'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Dunn_39000371_2",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000371,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715837696'),
				["VoiceAsset"] = "",
				["Duration"] = 6600,
				["Delay"] = 0,
			},
		},
		[39000372] = {
			[1] = {
				["ID"] = 39000372,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715837952'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000373] = {
			[1] = {
				["ID"] = 39000373,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715838208'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000374] = {
			[1] = {
				["ID"] = 39000374,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715838464'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000375] = {
			[1] = {
				["ID"] = 39000375,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715838720'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000376] = {
			[1] = {
				["ID"] = 39000376,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715838976'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000377] = {
			[1] = {
				["ID"] = 39000377,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715839232'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000378] = {
			[1] = {
				["ID"] = 39000378,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715839488'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000379] = {
			[1] = {
				["ID"] = 39000379,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715839744'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000379,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715840000'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000380] = {
			[1] = {
				["ID"] = 39000380,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715840256'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000381] = {
			[1] = {
				["ID"] = 39000381,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715840512'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000382] = {
			[1] = {
				["ID"] = 39000382,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570370304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715840768'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000383] = {
			[1] = {
				["ID"] = 39000383,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "2-049",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715841024'),
				["VoiceAsset"] = "",
				["Duration"] = 7500,
				["Delay"] = 0,
			},
		},
		[39000400] = {
			[1] = {
				["ID"] = 39000400,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715841280'),
				["VoiceAsset"] = "",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000400,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715841536'),
				["VoiceAsset"] = "",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000400,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = "IA! IA! Cthulhu Fhtagn!",
				["VoiceAsset"] = "",
				["Duration"] = 30000,
				["Delay"] = 0,
			},
		},
		[39000401] = {
			[1] = {
				["ID"] = 39000401,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715842048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000402] = {
			[1] = {
				["ID"] = 39000402,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715842304'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000402,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715842560'),
				["VoiceAsset"] = "",
				["Duration"] = 7000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000402,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715842816'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000501] = {
			[1] = {
				["ID"] = 39000501,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715843072'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000502] = {
			[1] = {
				["ID"] = 39000502,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715843328'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000502,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715843584'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000503] = {
			[1] = {
				["ID"] = 39000503,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715843840'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000504] = {
			[1] = {
				["ID"] = 39000504,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715844096'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000504,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715844352'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000505] = {
			[1] = {
				["ID"] = 39000505,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715844608'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000506] = {
			[1] = {
				["ID"] = 39000506,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715844864'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000507] = {
			[1] = {
				["ID"] = 39000507,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715845120'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000507,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447409920'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715845376'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000508] = {
			[1] = {
				["ID"] = 39000508,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715845632'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000509] = {
			[1] = {
				["ID"] = 39000509,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447409920'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715845888'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000510] = {
			[1] = {
				["ID"] = 39000510,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715846144'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000511] = {
			[1] = {
				["ID"] = 39000511,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715846400'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000512] = {
			[1] = {
				["ID"] = 39000512,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715846656'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000601] = {
			[1] = {
				["ID"] = 39000601,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715846912'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000601,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715847168'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000602] = {
			[1] = {
				["ID"] = 39000602,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408568576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715847424'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000602,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715847680'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000603] = {
			[1] = {
				["ID"] = 39000603,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408568576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715847936'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000603,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715848192'),
				["VoiceAsset"] = "",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
		},
		[39000604] = {
			[1] = {
				["ID"] = 39000604,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715848448'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[39000605] = {
			[1] = {
				["ID"] = 39000605,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408569344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715848704'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000605,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715848960'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000606] = {
			[1] = {
				["ID"] = 39000606,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408569344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715849216'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000606,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715849472'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000606,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408568064'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715849728'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 39000606,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715849984'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000607] = {
			[1] = {
				["ID"] = 39000607,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715850240'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000608] = {
			[1] = {
				["ID"] = 39000608,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715850496'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000609] = {
			[1] = {
				["ID"] = 39000609,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715850752'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000609,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715851008'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000610] = {
			[1] = {
				["ID"] = 39000610,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715851264'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000611] = {
			[1] = {
				["ID"] = 39000611,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715851520'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000612] = {
			[1] = {
				["ID"] = 39000612,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715851776'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000613] = {
			[1] = {
				["ID"] = 39000613,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715852032'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000614] = {
			[1] = {
				["ID"] = 39000614,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715852288'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000615] = {
			[1] = {
				["ID"] = 39000615,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = "test01",
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000616] = {
			[1] = {
				["ID"] = 39000616,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715852800'),
				["VoiceAsset"] = "",
				["Duration"] = 2900,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000616,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715853056'),
				["VoiceAsset"] = "",
				["Duration"] = 2900,
				["Delay"] = 0,
			},
		},
		[39000617] = {
			[1] = {
				["ID"] = 39000617,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715853312'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000617_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000618] = {
			[1] = {
				["ID"] = 39000618,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715853568'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000618_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000619] = {
			[1] = {
				["ID"] = 39000619,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715853824'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000619_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000620] = {
			[1] = {
				["ID"] = 39000620,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715854080'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000620_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000621] = {
			[1] = {
				["ID"] = 39000621,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715854336'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000621_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000622] = {
			[1] = {
				["ID"] = 39000622,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715854592'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000622_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000623] = {
			[1] = {
				["ID"] = 39000623,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715854848'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000623_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000624] = {
			[1] = {
				["ID"] = 39000624,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715855104'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000624_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000625] = {
			[1] = {
				["ID"] = 39000625,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715855360'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000625_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000626] = {
			[1] = {
				["ID"] = 39000626,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715855616'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000626_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000627] = {
			[1] = {
				["ID"] = 39000627,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715855872'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000627_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000628] = {
			[1] = {
				["ID"] = 39000628,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715856128'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000628_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000629] = {
			[1] = {
				["ID"] = 39000629,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715856384'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000629_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000630] = {
			[1] = {
				["ID"] = 39000630,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715856640'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000630_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000631] = {
			[1] = {
				["ID"] = 39000631,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447415040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715856896'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Goulu_39000631_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000632] = {
			[1] = {
				["ID"] = 39000632,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715857152'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000632_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000633] = {
			[1] = {
				["ID"] = 39000633,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715857408'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000633_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000634] = {
			[1] = {
				["ID"] = 39000634,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715857664'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000634_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000635] = {
			[1] = {
				["ID"] = 39000635,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715857920'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000635_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000636] = {
			[1] = {
				["ID"] = 39000636,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715858176'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000636_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000637] = {
			[1] = {
				["ID"] = 39000637,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715858432'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000637_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000638] = {
			[1] = {
				["ID"] = 39000638,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715858688'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000638_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000639] = {
			[1] = {
				["ID"] = 39000639,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715858944'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000639_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000640] = {
			[1] = {
				["ID"] = 39000640,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715859200'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000640_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000641] = {
			[1] = {
				["ID"] = 39000641,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715859456'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000641_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000642] = {
			[1] = {
				["ID"] = 39000642,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715859712'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000642_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000643] = {
			[1] = {
				["ID"] = 39000643,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715859968'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000643_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000644] = {
			[1] = {
				["ID"] = 39000644,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715860224'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000644_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000645] = {
			[1] = {
				["ID"] = 39000645,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715860480'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000645_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000646] = {
			[1] = {
				["ID"] = 39000646,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715860736'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000646_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000647] = {
			[1] = {
				["ID"] = 39000647,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715860992'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000647_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000648] = {
			[1] = {
				["ID"] = 39000648,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377984'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715861248'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Milgongen_39000648_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000701] = {
			[1] = {
				["ID"] = 39000701,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715861504'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000701_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000701,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715861760'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Gelman_39000701_2",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39000701,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715862016'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Gelman_39000701_3",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 39000701,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715862272'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Gelman_39000701_4",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000702] = {
			[1] = {
				["ID"] = 39000702,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715862528'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Gelman_39000702_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000702,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715862784'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Gelman_39000702_2",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000703] = {
			[1] = {
				["ID"] = 39000703,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715863040'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000703_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000703,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715863296'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Gelman_39000703_2",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000704] = {
			[1] = {
				["ID"] = 39000704,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715863552'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Gelman_39000704_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000705] = {
			[1] = {
				["ID"] = 39000705,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715863808'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000705_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000706] = {
			[1] = {
				["ID"] = 39000706,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715864064'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000706_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000707] = {
			[1] = {
				["ID"] = 39000707,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715864320'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39000707_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[39000800] = {
			[1] = {
				["ID"] = 39000800,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715864576'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000801] = {
			[1] = {
				["ID"] = 39000801,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715864832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000802] = {
			[1] = {
				["ID"] = 39000802,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715865088'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000803] = {
			[1] = {
				["ID"] = 39000803,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715865344'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000804] = {
			[1] = {
				["ID"] = 39000804,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715865600'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000805] = {
			[1] = {
				["ID"] = 39000805,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715865856'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000806] = {
			[1] = {
				["ID"] = 39000806,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413312'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715866112'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000807] = {
			[1] = {
				["ID"] = 39000807,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413312'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715866368'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000808] = {
			[1] = {
				["ID"] = 39000808,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715866624'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000809] = {
			[1] = {
				["ID"] = 39000809,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715866880'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000810] = {
			[1] = {
				["ID"] = 39000810,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715867136'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000811] = {
			[1] = {
				["ID"] = 39000811,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413312'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715867392'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[39000901] = {
			[1] = {
				["ID"] = 39000901,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715867648'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000901_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39000901,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715867904'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000901_2",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39000904] = {
			[1] = {
				["ID"] = 39000904,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715868160'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000904_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000905] = {
			[1] = {
				["ID"] = 39000905,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715868416'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000905_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000906] = {
			[1] = {
				["ID"] = 39000906,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715868672'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000906_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000907] = {
			[1] = {
				["ID"] = 39000907,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715868928'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000907_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000908] = {
			[1] = {
				["ID"] = 39000908,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715869184'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000908_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000909] = {
			[1] = {
				["ID"] = 39000909,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715869440'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000909_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000910] = {
			[1] = {
				["ID"] = 39000910,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715869696'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000910_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000911] = {
			[1] = {
				["ID"] = 39000911,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715869952'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000911_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000912] = {
			[1] = {
				["ID"] = 39000912,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715870208'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000912_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000913] = {
			[1] = {
				["ID"] = 39000913,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715870464'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000913_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000914] = {
			[1] = {
				["ID"] = 39000914,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715870720'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000914_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39000915] = {
			[1] = {
				["ID"] = 39000915,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570378240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715870976'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Sasriel_39000915_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001001] = {
			[1] = {
				["ID"] = 39001001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715871232'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39001001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715871488'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001002] = {
			[1] = {
				["ID"] = 39001002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715871744'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001003] = {
			[1] = {
				["ID"] = 39001003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715872000'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001004] = {
			[1] = {
				["ID"] = 39001004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715872256'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001005] = {
			[1] = {
				["ID"] = 39001005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715872512'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001006] = {
			[1] = {
				["ID"] = 39001006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715872768'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001007] = {
			[1] = {
				["ID"] = 39001007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715873024'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001008] = {
			[1] = {
				["ID"] = 39001008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715873280'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001009] = {
			[1] = {
				["ID"] = 39001009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715873536'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001010] = {
			[1] = {
				["ID"] = 39001010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715873792'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001011] = {
			[1] = {
				["ID"] = 39001011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715874048'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39001011,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715874304'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001012] = {
			[1] = {
				["ID"] = 39001012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715874560'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001013] = {
			[1] = {
				["ID"] = 39001013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715874816'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001014] = {
			[1] = {
				["ID"] = 39001014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715875072'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001015] = {
			[1] = {
				["ID"] = 39001015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715875328'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001016] = {
			[1] = {
				["ID"] = 39001016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715875584'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001017] = {
			[1] = {
				["ID"] = 39001017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715875840'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001018] = {
			[1] = {
				["ID"] = 39001018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715876096'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001019] = {
			[1] = {
				["ID"] = 39001019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570379776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715876352'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001020] = {
			[1] = {
				["ID"] = 39001020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715876608'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39001021] = {
			[1] = {
				["ID"] = 39001021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715876864'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39001022] = {
			[1] = {
				["ID"] = 39001022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715877120'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[39001101] = {
			[1] = {
				["ID"] = 39001101,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715877376'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001101_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 39001101,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715877632'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 39001101,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715877888'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001103] = {
			[1] = {
				["ID"] = 39001103,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715878144'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001103_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001104] = {
			[1] = {
				["ID"] = 39001104,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715878400'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001104_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001105] = {
			[1] = {
				["ID"] = 39001105,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715878656'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001105_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001106] = {
			[1] = {
				["ID"] = 39001106,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715878912'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001107] = {
			[1] = {
				["ID"] = 39001107,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715879168'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001107_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001108] = {
			[1] = {
				["ID"] = 39001108,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715879424'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001108_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001109] = {
			[1] = {
				["ID"] = 39001109,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715879680'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001109_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001110] = {
			[1] = {
				["ID"] = 39001110,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715879936'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001110_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001111] = {
			[1] = {
				["ID"] = 39001111,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715880192'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001112] = {
			[1] = {
				["ID"] = 39001112,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715880448'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001113] = {
			[1] = {
				["ID"] = 39001113,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715880704'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001113_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001114] = {
			[1] = {
				["ID"] = 39001114,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715880960'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001114_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001115] = {
			[1] = {
				["ID"] = 39001115,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715881216'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_39001115_1",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[39001116] = {
			[1] = {
				["ID"] = 39001116,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715881472'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
		},
		[30000001] = {
			[1] = {
				["ID"] = 30000001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715881728'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715881984'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000002] = {
			[1] = {
				["ID"] = 30000002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142691328'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000025] = {
			[1] = {
				["ID"] = 30000025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715882496'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30000003] = {
			[1] = {
				["ID"] = 30000003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715882752'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30000004] = {
			[1] = {
				["ID"] = 30000004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715883008'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715883264'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30000004,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715883520'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000005] = {
			[1] = {
				["ID"] = 30000005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715883776'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000006] = {
			[1] = {
				["ID"] = 30000006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715884032'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000007] = {
			[1] = {
				["ID"] = 30000007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715884288'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000007,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715884544'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30000007,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447449344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715884800'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000008] = {
			[1] = {
				["ID"] = 30000008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715885056'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000009] = {
			[1] = {
				["ID"] = 30000009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715885312'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000010] = {
			[1] = {
				["ID"] = 30000010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715885568'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000010,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715885824'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30000010,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715886080'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000011] = {
			[1] = {
				["ID"] = 30000011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715886336'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 2000,
			},
			[2] = {
				["ID"] = 30000011,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715886592'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000012] = {
			[1] = {
				["ID"] = 30000012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715886848'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000013] = {
			[1] = {
				["ID"] = 30000013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715887104'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000013,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715887360'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30000013,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715887616'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000014] = {
			[1] = {
				["ID"] = 30000014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715887872'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000015] = {
			[1] = {
				["ID"] = 30000015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715888128'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000015,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715888384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30000015,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715888640'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30000015,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715888896'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30000015,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_13815299191040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715889152'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000016] = {
			[1] = {
				["ID"] = 30000016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852651009'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000017] = {
			[1] = {
				["ID"] = 30000017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715889664'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000017,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715889920'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30000017,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715890176'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30000017,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715890432'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000018] = {
			[1] = {
				["ID"] = 30000018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715890688'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000019] = {
			[1] = {
				["ID"] = 30000019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715890944'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000020] = {
			[1] = {
				["ID"] = 30000020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715891200'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000021] = {
			[1] = {
				["ID"] = 30000021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852659201'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30000021,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852659203'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30000021,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715891968'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000022] = {
			[1] = {
				["ID"] = 30000022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715892224'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000023] = {
			[1] = {
				["ID"] = 30000023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715892480'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30000024] = {
			[1] = {
				["ID"] = 30000024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663882240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734023951616'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30002018] = {
			[1] = {
				["ID"] = 30002018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715892992'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002018,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715893248'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002019] = {
			[1] = {
				["ID"] = 30002019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715893504'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002019,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715893760'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002020] = {
			[1] = {
				["ID"] = 30002020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715894016'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002021] = {
			[1] = {
				["ID"] = 30002021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431823616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715894272'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002021,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431823616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715894528'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002021,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431823616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715894784'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002021,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715895040'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002022] = {
			[1] = {
				["ID"] = 30002022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715895296'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002023] = {
			[1] = {
				["ID"] = 30002023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715895552'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002024] = {
			[1] = {
				["ID"] = 30002024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389502977'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002025] = {
			[1] = {
				["ID"] = 30002025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431846656'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715896064'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002025,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663901184'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715896320'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002025,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715896576'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002025,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715896832'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30002026] = {
			[1] = {
				["ID"] = 30002026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715897088'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002027] = {
			[1] = {
				["ID"] = 30002027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715897344'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002027,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590239232'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715897600'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002028] = {
			[1] = {
				["ID"] = 30002028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715897856'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002029] = {
			[1] = {
				["ID"] = 30002029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715898112'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002030] = {
			[1] = {
				["ID"] = 30002030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715898368'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002031] = {
			[1] = {
				["ID"] = 30002031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715898624'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002032] = {
			[1] = {
				["ID"] = 30002032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389553665'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002032,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389553666'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002033] = {
			[1] = {
				["ID"] = 30002033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715899392'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002034] = {
			[1] = {
				["ID"] = 30002034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715899648'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002035] = {
			[1] = {
				["ID"] = 30002035,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715899904'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002036] = {
			[1] = {
				["ID"] = 30002036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826176'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142735616'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002036,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431825664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142735872'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002036,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663878912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142736128'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002036,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826176'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58551410325248'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30002036,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715901184'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002037] = {
			[1] = {
				["ID"] = 30002037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852683009'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002038] = {
			[1] = {
				["ID"] = 30002038,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389553921'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002039] = {
			[1] = {
				["ID"] = 30002039,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715901952'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 2000,
			},
		},
		[30002040] = {
			[1] = {
				["ID"] = 30002040,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715902208'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002040,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715902464'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002041] = {
			[1] = {
				["ID"] = 30002041,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715902720'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002041,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715902976'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002042] = {
			[1] = {
				["ID"] = 30002042,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715903232'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002043] = {
			[1] = {
				["ID"] = 30002043,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715903488'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002043,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715903744'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002044] = {
			[1] = {
				["ID"] = 30002044,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715904000'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002045] = {
			[1] = {
				["ID"] = 30002045,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715904256'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002046] = {
			[1] = {
				["ID"] = 30002046,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715904512'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002046,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715904768'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002047] = {
			[1] = {
				["ID"] = 30002047,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715905024'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002048] = {
			[1] = {
				["ID"] = 30002048,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715905280'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002049] = {
			[1] = {
				["ID"] = 30002049,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142840064'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 3000,
			},
			[2] = {
				["ID"] = 30002049,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715905792'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002049,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715906048'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30002050] = {
			[1] = {
				["ID"] = 30002050,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715906304'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002050,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715906560'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002050,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447471360'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715906816'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002050,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447471360'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715907072'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002051] = {
			[1] = {
				["ID"] = 30002051,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715907328'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002051,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715907584'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002051,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715907840'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002052] = {
			[1] = {
				["ID"] = 30002052,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852699393'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002053] = {
			[1] = {
				["ID"] = 30002053,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389570305'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002054] = {
			[1] = {
				["ID"] = 30002054,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715908608'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002054,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715908864'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002054,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447473664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715909120'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002054,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447473664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715909376'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002055] = {
			[1] = {
				["ID"] = 30002055,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715909632'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002059] = {
			[1] = {
				["ID"] = 30002059,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715909888'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002059,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715910144'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30002056] = {
			[1] = {
				["ID"] = 30002056,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715910400'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002056,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715910656'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002056,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715910912'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002056,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447473664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715911168'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002057] = {
			[1] = {
				["ID"] = 30002057,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715911424'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002057,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715911680'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002058] = {
			[1] = {
				["ID"] = 30002058,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852687361'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002058,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468228608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852687362'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002068] = {
			[1] = {
				["ID"] = 30002068,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715912448'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002060] = {
			[1] = {
				["ID"] = 30002060,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715912704'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002060,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715912960'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002060,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715913216'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002061] = {
			[1] = {
				["ID"] = 30002061,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715912704'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002061,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715913728'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002061,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715913984'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002062] = {
			[1] = {
				["ID"] = 30002062,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715914240'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002062,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715914496'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002062,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715914752'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002062,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715915008'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30002062,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715915264'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002063] = {
			[1] = {
				["ID"] = 30002063,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715915520'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002063,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715915776'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002063,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715916032'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002063,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715916288'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002064] = {
			[1] = {
				["ID"] = 30002064,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58551410338048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002064,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715916800'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002064,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58551410338560'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002064,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715917312'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30002064,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58551410339072'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[6] = {
				["ID"] = 30002064,
				["Order"] = 6,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58551410339328'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002065] = {
			[1] = {
				["ID"] = 30002065,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_29756607273472'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002065,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715918336'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002065,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715918592'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30002066] = {
			[1] = {
				["ID"] = 30002066,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715918848'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002066,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715919104'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002066,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715919360'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002067] = {
			[1] = {
				["ID"] = 30002067,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715919616'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002067,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715919872'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002067,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715920128'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002067,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715920384'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30002067,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142808832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002069] = {
			[1] = {
				["ID"] = 30002069,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142823424'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002069,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142823680'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002069,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827968'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715921408'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002069,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431825664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715921664'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30002069,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715921920'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002070] = {
			[1] = {
				["ID"] = 30002070,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715922176'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002071] = {
			[1] = {
				["ID"] = 30002071,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715922432'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002072] = {
			[1] = {
				["ID"] = 30002072,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715922688'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002073] = {
			[1] = {
				["ID"] = 30002073,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715922944'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002074] = {
			[1] = {
				["ID"] = 30002074,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715923200'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 2000,
			},
		},
		[30002075] = {
			[1] = {
				["ID"] = 30002075,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715923456'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002075,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431825664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715923712'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002076] = {
			[1] = {
				["ID"] = 30002076,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166862849'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002077] = {
			[1] = {
				["ID"] = 30002077,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715924224'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002078] = {
			[1] = {
				["ID"] = 30002078,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142759424'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002078,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142759680'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002078,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715924992'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002079] = {
			[1] = {
				["ID"] = 30002079,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715925248'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002080] = {
			[1] = {
				["ID"] = 30002080,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715925504'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002081] = {
			[1] = {
				["ID"] = 30002081,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715925760'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002081,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715926016'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002081,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715926272'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30002082] = {
			[1] = {
				["ID"] = 30002082,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590243584'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715926528'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 500,
			},
		},
		[30002083] = {
			[1] = {
				["ID"] = 30002083,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715926784'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002084] = {
			[1] = {
				["ID"] = 30002084,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715927040'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 5000,
			},
		},
		[30002085] = {
			[1] = {
				["ID"] = 30002085,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715927296'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002086] = {
			[1] = {
				["ID"] = 30002086,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827200'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715927552'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002086,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715927808'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002086,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827200'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715928064'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002086,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468228608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715928320'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30002086,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715928576'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30002087] = {
			[1] = {
				["ID"] = 30002087,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431827456'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715928832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002088] = {
			[1] = {
				["ID"] = 30002088,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715929088'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002089] = {
			[1] = {
				["ID"] = 30002089,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715929344'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002089,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715929600'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30002089,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715929856'),
				["VoiceAsset"] = "",
				["Duration"] = 1500,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30002089,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715930112'),
				["VoiceAsset"] = "",
				["Duration"] = 1500,
				["Delay"] = 0,
			},
		},
		[30002090] = {
			[1] = {
				["ID"] = 30002090,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715930368'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002090,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715930624'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002091] = {
			[1] = {
				["ID"] = 30002091,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715930880'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002092] = {
			[1] = {
				["ID"] = 30002092,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_32025423652608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715931136'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002093] = {
			[1] = {
				["ID"] = 30002093,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715904000'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30002093,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715931648'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30002094] = {
			[1] = {
				["ID"] = 30002094,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715931904'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002095] = {
			[1] = {
				["ID"] = 30002095,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715932160'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002096] = {
			[1] = {
				["ID"] = 30002096,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431826432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715932416'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30002097] = {
			[1] = {
				["ID"] = 30002097,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715932672'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30002098] = {
			[1] = {
				["ID"] = 30002098,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715932928'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30002099] = {
			[1] = {
				["ID"] = 30002099,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715933184'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30006001] = {
			[1] = {
				["ID"] = 30006001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715933440'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006002] = {
			[1] = {
				["ID"] = 30006002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142882560'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142882816'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006003] = {
			[1] = {
				["ID"] = 30006003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715934208'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006003,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715934464'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006004] = {
			[1] = {
				["ID"] = 30006004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142883072'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142883328'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006005] = {
			[1] = {
				["ID"] = 30006005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715935232'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006006] = {
			[1] = {
				["ID"] = 30006006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715935488'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006007] = {
			[1] = {
				["ID"] = 30006007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715935744'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006008] = {
			[1] = {
				["ID"] = 30006008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715936000'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006009] = {
			[1] = {
				["ID"] = 30006009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715936256'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006009,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715936512'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006010] = {
			[1] = {
				["ID"] = 30006010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715936768'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006010,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715937024'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006010,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715937280'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006010,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715937536'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006011] = {
			[1] = {
				["ID"] = 30006011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715937792'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006011,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715938048'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006012] = {
			[1] = {
				["ID"] = 30006012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715938304'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006012,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715938560'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006013] = {
			[1] = {
				["ID"] = 30006013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715938816'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006013,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715939072'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006013,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715939328'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006013,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715939584'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006014] = {
			[1] = {
				["ID"] = 30006014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715939840'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006014,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715940096'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006014,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006015] = {
			[1] = {
				["ID"] = 30006015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715940608'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006015,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715940864'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006015,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715941120'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006016] = {
			[1] = {
				["ID"] = 30006016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715941376'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006016,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715941632'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006017] = {
			[1] = {
				["ID"] = 30006017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715941888'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006018] = {
			[1] = {
				["ID"] = 30006018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715942144'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006019] = {
			[1] = {
				["ID"] = 30006019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715942400'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006019,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715942656'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006020] = {
			[1] = {
				["ID"] = 30006020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715942912'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006021] = {
			[1] = {
				["ID"] = 30006021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715943168'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006021,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431800576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715943424'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006022] = {
			[1] = {
				["ID"] = 30006022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715943680'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006023] = {
			[1] = {
				["ID"] = 30006023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715943936'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006023,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431800576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715944192'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006023,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715944448'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006024] = {
			[1] = {
				["ID"] = 30006024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715944704'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006024,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715944960'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006024,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715945216'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006024,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715945472'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006025] = {
			[1] = {
				["ID"] = 30006025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715945728'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006026] = {
			[1] = {
				["ID"] = 30006026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715945984'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006026,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715946240'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006026,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715946496'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006027] = {
			[1] = {
				["ID"] = 30006027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715946752'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006027,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715947008'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006028] = {
			[1] = {
				["ID"] = 30006028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715947264'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006028,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715947520'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006029] = {
			[1] = {
				["ID"] = 30006029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715947776'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006030] = {
			[1] = {
				["ID"] = 30006030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447512576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715948032'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006030,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447512576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715948288'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006030,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447512576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715948544'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006030,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447512576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715948800'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006031] = {
			[1] = {
				["ID"] = 30006031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715949056'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006032] = {
			[1] = {
				["ID"] = 30006032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006032,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715949568'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006032,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715949824'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006033] = {
			[1] = {
				["ID"] = 30006033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006033,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715950336'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006033,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715950592'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006034] = {
			[1] = {
				["ID"] = 30006034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006034,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715951104'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006034,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715951360'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006034,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56902142928384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006035] = {
			[1] = {
				["ID"] = 30006035,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715951872'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006035,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715952128'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006035,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715952384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006035,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715952640'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006036] = {
			[1] = {
				["ID"] = 30006036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56832081140480'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006036,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715953152'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006036,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58481348450560'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006036,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715953664'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006037] = {
			[1] = {
				["ID"] = 30006037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389591297'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006037,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389591299'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006038] = {
			[1] = {
				["ID"] = 30006038,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715954432'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006038,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715954688'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006038,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715954944'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006039] = {
			[1] = {
				["ID"] = 30006039,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006039,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715955456'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006039,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715955712'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006039,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715955968'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006040] = {
			[1] = {
				["ID"] = 30006040,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715956224'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006040,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715956480'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006041] = {
			[1] = {
				["ID"] = 30006041,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715956736'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006042] = {
			[1] = {
				["ID"] = 30006042,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715956992'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006042,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715957248'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006043] = {
			[1] = {
				["ID"] = 30006043,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715957504'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006043,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715957760'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006044] = {
			[1] = {
				["ID"] = 30006044,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715958016'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006045] = {
			[1] = {
				["ID"] = 30006045,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715958272'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006045,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715958528'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006046] = {
			[1] = {
				["ID"] = 30006046,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715958784'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006046,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715959040'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006047] = {
			[1] = {
				["ID"] = 30006047,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715959296'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006047,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715959552'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006047,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715959808'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006048] = {
			[1] = {
				["ID"] = 30006048,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715960064'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006049] = {
			[1] = {
				["ID"] = 30006049,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715960320'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006050] = {
			[1] = {
				["ID"] = 30006050,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715960576'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006050,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715960832'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006051] = {
			[1] = {
				["ID"] = 30006051,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715961088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006051,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715961344'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006052] = {
			[1] = {
				["ID"] = 30006052,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715961600'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006053] = {
			[1] = {
				["ID"] = 30006053,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715961856'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006054] = {
			[1] = {
				["ID"] = 30006054,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715962112'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006055] = {
			[1] = {
				["ID"] = 30006055,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715962368'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006056] = {
			[1] = {
				["ID"] = 30006056,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715962624'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006057] = {
			[1] = {
				["ID"] = 30006057,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715962880'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006058] = {
			[1] = {
				["ID"] = 30006058,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715963136'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006059] = {
			[1] = {
				["ID"] = 30006059,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715963392'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006060] = {
			[1] = {
				["ID"] = 30006060,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715963648'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006060,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715963904'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006060,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715964160'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006061] = {
			[1] = {
				["ID"] = 30006061,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715964416'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006062] = {
			[1] = {
				["ID"] = 30006062,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715964672'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006063] = {
			[1] = {
				["ID"] = 30006063,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715964928'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006064] = {
			[1] = {
				["ID"] = 30006064,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715965184'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006064,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715965440'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006065] = {
			[1] = {
				["ID"] = 30006065,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715965696'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006066] = {
			[1] = {
				["ID"] = 30006066,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715965952'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006066,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715966208'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006067] = {
			[1] = {
				["ID"] = 30006067,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715966464'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006068] = {
			[1] = {
				["ID"] = 30006068,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715966720'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006069] = {
			[1] = {
				["ID"] = 30006069,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715966976'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006070] = {
			[1] = {
				["ID"] = 30006070,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715967232'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006071] = {
			[1] = {
				["ID"] = 30006071,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590224896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715967488'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006072] = {
			[1] = {
				["ID"] = 30006072,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715967744'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006073] = {
			[1] = {
				["ID"] = 30006073,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715968000'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006073,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715968256'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006073,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715968512'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006074] = {
			[1] = {
				["ID"] = 30006074,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431971840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715968768'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006074,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715969024'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006075] = {
			[1] = {
				["ID"] = 30006075,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715969280'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006075,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715969536'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30006075,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715969792'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30006075,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715970048'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006076] = {
			[1] = {
				["ID"] = 30006076,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715970304'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30006076,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715970560'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30006077] = {
			[1] = {
				["ID"] = 30006077,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715970816'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30004001] = {
			[1] = {
				["ID"] = 30004001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447535616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715971072'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 10,
			},
			[2] = {
				["ID"] = 30004001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447535616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715971328'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 10,
			},
		},
		[30004002] = {
			[1] = {
				["ID"] = 30004002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447535616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715971584'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 10,
			},
			[2] = {
				["ID"] = 30004002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447535616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715971840'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 10,
			},
		},
		[30004005] = {
			[1] = {
				["ID"] = 30004005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447537664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_32024886778368'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 10,
			},
			[2] = {
				["ID"] = 30004005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447537664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715973376'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 10,
			},
			[3] = {
				["ID"] = 30004005,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447537664'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715973632'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 10,
			},
		},
		[30004006] = {
			[1] = {
				["ID"] = 30004006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715973888'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 2000,
			},
			[2] = {
				["ID"] = 30004006,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715974144'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1000,
			},
			[3] = {
				["ID"] = 30004006,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468258560'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715974400'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1000,
			},
			[4] = {
				["ID"] = 30004006,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715974656'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1000,
			},
		},
		[30004007] = {
			[1] = {
				["ID"] = 30004007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715974912'),
				["VoiceAsset"] = "",
				["Duration"] = 1000,
				["Delay"] = 1000,
			},
		},
		[30004008] = {
			[1] = {
				["ID"] = 30004008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468258560'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166935041'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1000,
			},
		},
		[30004009] = {
			[1] = {
				["ID"] = 30004009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431917056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166935553'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1000,
			},
		},
		[30004010] = {
			[1] = {
				["ID"] = 30004010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447540224'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715975680'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1000,
			},
		},
		[30004011] = {
			[1] = {
				["ID"] = 30004011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59246389554178'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389554177'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1000,
			},
			[2] = {
				["ID"] = 30004011,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59246389554178'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715976192'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1000,
			},
		},
		[50200000] = {
			[1] = {
				["ID"] = 50200000,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715976448'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[50200001] = {
			[1] = {
				["ID"] = 50200001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590255104'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715976704'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 50200001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590255104'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715976960'),
				["VoiceAsset"] = "",
				["Duration"] = 2250,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 50200001,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715977216'),
				["VoiceAsset"] = "",
				["Duration"] = 2250,
				["Delay"] = 0,
			},
		},
		[50200002] = {
			[1] = {
				["ID"] = 50200002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715977472'),
				["VoiceAsset"] = "",
				["Duration"] = 2250,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 50200002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715977728'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 50200002,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590255104'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715977984'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 50200002,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590255104'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715978240'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 50200002,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715978496'),
				["VoiceAsset"] = "",
				["Duration"] = 2250,
				["Delay"] = 0,
			},
		},
		[50200003] = {
			[1] = {
				["ID"] = 50200003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715978752'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[50200004] = {
			[1] = {
				["ID"] = 50200004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715979008'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 50200004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715979264'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[50200005] = {
			[1] = {
				["ID"] = 50200005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715979520'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[50200006] = {
			[1] = {
				["ID"] = 50200006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389498113'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[50200007] = {
			[1] = {
				["ID"] = 50200007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389498369'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[50200008] = {
			[1] = {
				["ID"] = 50200008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389498881'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[50200009] = {
			[1] = {
				["ID"] = 50200009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389499137'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[50200010] = {
			[1] = {
				["ID"] = 50200010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715980800'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[50200011] = {
			[1] = {
				["ID"] = 50200011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715981056'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[50200012] = {
			[1] = {
				["ID"] = 50200012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715981312'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[50200013] = {
			[1] = {
				["ID"] = 50200013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389499905'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[50200014] = {
			[1] = {
				["ID"] = 50200014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389500161'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[50200015] = {
			[1] = {
				["ID"] = 50200015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389500417'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[50200016] = {
			[1] = {
				["ID"] = 50200016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715982336'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[50200017] = {
			[1] = {
				["ID"] = 50200017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715982592'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016001] = {
			[1] = {
				["ID"] = 30016001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715982848'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715983104'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30016001,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715983360'),
				["VoiceAsset"] = "",
				["Duration"] = 4500,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30016001,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715983616'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30016001,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715983872'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30016002] = {
			[1] = {
				["ID"] = 30016002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715984128'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715984384'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30016002,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715984640'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30016003] = {
			[1] = {
				["ID"] = 30016003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715984896'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016004] = {
			[1] = {
				["ID"] = 30016004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715985152'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016005] = {
			[1] = {
				["ID"] = 30016005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715985408'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016006] = {
			[1] = {
				["ID"] = 30016006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715985664'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016007] = {
			[1] = {
				["ID"] = 30016007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715985920'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016008] = {
			[1] = {
				["ID"] = 30016008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715986176'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016009] = {
			[1] = {
				["ID"] = 30016009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715986432'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016010] = {
			[1] = {
				["ID"] = 30016010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715986688'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016010,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590187520'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715986944'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016011] = {
			[1] = {
				["ID"] = 30016011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715987200'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016011,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715987456'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30016011,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590187520'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715987712'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016012] = {
			[1] = {
				["ID"] = 30016012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590242816'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715987968'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016013] = {
			[1] = {
				["ID"] = 30016013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715988224'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016013,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715988480'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016014] = {
			[1] = {
				["ID"] = 30016014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715988736'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016015] = {
			[1] = {
				["ID"] = 30016015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715988992'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016015,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715989248'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016016] = {
			[1] = {
				["ID"] = 30016016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715989504'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1000,
			},
		},
		[30016017] = {
			[1] = {
				["ID"] = 30016017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590243584'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715989760'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016018] = {
			[1] = {
				["ID"] = 30016018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590243584'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715990016'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016018,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590243072'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715990272'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016019] = {
			[1] = {
				["ID"] = 30016019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590241792'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715990528'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016020] = {
			[1] = {
				["ID"] = 30016020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715990784'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016021] = {
			[1] = {
				["ID"] = 30016021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590243840'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715991040'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016022] = {
			[1] = {
				["ID"] = 30016022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715991296'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016023] = {
			[1] = {
				["ID"] = 30016023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715991552'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016023,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715991808'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30016023,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715992064'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016024] = {
			[1] = {
				["ID"] = 30016024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715992320'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016025] = {
			[1] = {
				["ID"] = 30016025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715992576'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016026] = {
			[1] = {
				["ID"] = 30016026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715992832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016027] = {
			[1] = {
				["ID"] = 30016027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715993088'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016028] = {
			[1] = {
				["ID"] = 30016028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715993344'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016029] = {
			[1] = {
				["ID"] = 30016029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715993600'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016030] = {
			[1] = {
				["ID"] = 30016030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715993856'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016031] = {
			[1] = {
				["ID"] = 30016031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715994112'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016032] = {
			[1] = {
				["ID"] = 30016032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166854913'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016033] = {
			[1] = {
				["ID"] = 30016033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715994624'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016034] = {
			[1] = {
				["ID"] = 30016034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715994880'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016035] = {
			[1] = {
				["ID"] = 30016035,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431815936'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715995136'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016035,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431815936'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715995392'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016036] = {
			[1] = {
				["ID"] = 30016036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715995648'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016036,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431815936'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715995904'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016037] = {
			[1] = {
				["ID"] = 30016037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715996160'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016038] = {
			[1] = {
				["ID"] = 30016038,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431815936'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715996416'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016038,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715996672'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30016038,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431815936'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715996928'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016039] = {
			[1] = {
				["ID"] = 30016039,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590187520'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715997184'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016040] = {
			[1] = {
				["ID"] = 30016040,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715997440'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016041] = {
			[1] = {
				["ID"] = 30016041,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715997696'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016042] = {
			[1] = {
				["ID"] = 30016042,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715997952'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016043] = {
			[1] = {
				["ID"] = 30016043,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715998208'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016044] = {
			[1] = {
				["ID"] = 30016044,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715998464'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016045] = {
			[1] = {
				["ID"] = 30016045,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715998720'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016045,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715998976'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30016045,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715999232'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016046] = {
			[1] = {
				["ID"] = 30016046,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715999488'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016047] = {
			[1] = {
				["ID"] = 30016047,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5154766116864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715999744'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016047,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716000000'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016048] = {
			[1] = {
				["ID"] = 30016048,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716000256'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016049] = {
			[1] = {
				["ID"] = 30016049,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716000512'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016050] = {
			[1] = {
				["ID"] = 30016050,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716000768'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016051] = {
			[1] = {
				["ID"] = 30016051,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716001024'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016052] = {
			[1] = {
				["ID"] = 30016052,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5154766116864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715999744'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016052,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716001536'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30016052,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716001792'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016053] = {
			[1] = {
				["ID"] = 30016053,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716002048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016054] = {
			[1] = {
				["ID"] = 30016054,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716002304'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016055] = {
			[1] = {
				["ID"] = 30016055,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716002560'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016055,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716002816'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016056] = {
			[1] = {
				["ID"] = 30016056,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716003072'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016057] = {
			[1] = {
				["ID"] = 30016057,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716003328'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016058] = {
			[1] = {
				["ID"] = 30016058,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716003584'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016059] = {
			[1] = {
				["ID"] = 30016059,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716003840'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016060] = {
			[1] = {
				["ID"] = 30016060,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716004096'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016061] = {
			[1] = {
				["ID"] = 30016061,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716004352'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30016062] = {
			[1] = {
				["ID"] = 30016062,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716004608'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1500,
			},
		},
		[30016063] = {
			[1] = {
				["ID"] = 30016063,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716004864'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30016063,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716005120'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30016064] = {
			[1] = {
				["ID"] = 30016064,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716005376'),
				["VoiceAsset"] = "",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
		},
		[30008001] = {
			[1] = {
				["ID"] = 30008001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716005632'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30008001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716005888'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30008002] = {
			[1] = {
				["ID"] = 30008002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389507329'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30008003] = {
			[1] = {
				["ID"] = 30008003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389507585'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30008004] = {
			[1] = {
				["ID"] = 30008004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716006656'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30008005] = {
			[1] = {
				["ID"] = 30008005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716006912'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30008006] = {
			[1] = {
				["ID"] = 30008006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716007168'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30008007] = {
			[1] = {
				["ID"] = 30008007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716007424'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001001] = {
			[1] = {
				["ID"] = 30001001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716007680'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716007936'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001002] = {
			[1] = {
				["ID"] = 30001002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716008192'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001003] = {
			[1] = {
				["ID"] = 30001003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663880192'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716008448'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001004] = {
			[1] = {
				["ID"] = 30001004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431932928'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716008704'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001005] = {
			[1] = {
				["ID"] = 30001005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716008960'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716009216'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001006] = {
			[1] = {
				["ID"] = 30001006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716009472'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001007] = {
			[1] = {
				["ID"] = 30001007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716009728'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001007,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5154766110464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716009984'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001008] = {
			[1] = {
				["ID"] = 30001008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5154766110464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716010240'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716010496'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001009] = {
			[1] = {
				["ID"] = 30001009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716010752'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001009,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716011008'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001010] = {
			[1] = {
				["ID"] = 30001010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716011264'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001010,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716011520'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001011] = {
			[1] = {
				["ID"] = 30001011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716011776'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001011,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590225152'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716012032'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001012] = {
			[1] = {
				["ID"] = 30001012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_29757412568832'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001012,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716012544'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001014] = {
			[1] = {
				["ID"] = 30001014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716012800'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001015] = {
			[1] = {
				["ID"] = 30001015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716013056'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001015,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716013312'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001016] = {
			[1] = {
				["ID"] = 30001016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716013568'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001016,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716013824'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001016,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716014080'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30001016,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716014336'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001017] = {
			[1] = {
				["ID"] = 30001017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716014592'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001018] = {
			[1] = {
				["ID"] = 30001018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716014848'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001019] = {
			[1] = {
				["ID"] = 30001019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716015104'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001020] = {
			[1] = {
				["ID"] = 30001020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716015360'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001021] = {
			[1] = {
				["ID"] = 30001021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716015616'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001022] = {
			[1] = {
				["ID"] = 30001022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716015872'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001023] = {
			[1] = {
				["ID"] = 30001023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716016128'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001023,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716016384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001024] = {
			[1] = {
				["ID"] = 30001024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716016640'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001024,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716016896'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001024,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716017152'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001025] = {
			[1] = {
				["ID"] = 30001025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716017408'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001025,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716017664'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001025,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716017920'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001026] = {
			[1] = {
				["ID"] = 30001026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716018176'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001027] = {
			[1] = {
				["ID"] = 30001027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716018432'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001028] = {
			[1] = {
				["ID"] = 30001028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716018688'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001029] = {
			[1] = {
				["ID"] = 30001029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716018944'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001030] = {
			[1] = {
				["ID"] = 30001030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716019200'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001031] = {
			[1] = {
				["ID"] = 30001031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716019456'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001032] = {
			[1] = {
				["ID"] = 30001032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716019712'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001033] = {
			[1] = {
				["ID"] = 30001033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716019968'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001033,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716020224'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001033,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716020480'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30001033,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716020736'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001034] = {
			[1] = {
				["ID"] = 30001034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716020992'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001034,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716021248'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001035] = {
			[1] = {
				["ID"] = 30001035,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716021504'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001036] = {
			[1] = {
				["ID"] = 30001036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716021760'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001037] = {
			[1] = {
				["ID"] = 30001037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716022016'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001038] = {
			[1] = {
				["ID"] = 30001038,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716022272'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001039] = {
			[1] = {
				["ID"] = 30001039,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716022528'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001040] = {
			[1] = {
				["ID"] = 30001040,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716022784'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001041] = {
			[1] = {
				["ID"] = 30001041,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716023040'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001042] = {
			[1] = {
				["ID"] = 30001042,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716023296'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001042,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716023552'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001044] = {
			[1] = {
				["ID"] = 30001044,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716023808'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001045] = {
			[1] = {
				["ID"] = 30001045,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716024064'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001046] = {
			[1] = {
				["ID"] = 30001046,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716024320'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001047] = {
			[1] = {
				["ID"] = 30001047,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716024576'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001048] = {
			[1] = {
				["ID"] = 30001048,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716024832'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001049] = {
			[1] = {
				["ID"] = 30001049,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716025088'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001049,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590295040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716025344'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001051] = {
			[1] = {
				["ID"] = 30001051,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590295040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716025600'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001052] = {
			[1] = {
				["ID"] = 30001052,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716025856'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001053] = {
			[1] = {
				["ID"] = 30001053,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716026112'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001054] = {
			[1] = {
				["ID"] = 30001054,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716026368'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001055] = {
			[1] = {
				["ID"] = 30001055,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716026624'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001056] = {
			[1] = {
				["ID"] = 30001056,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716026880'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001057] = {
			[1] = {
				["ID"] = 30001057,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716027136'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001058] = {
			[1] = {
				["ID"] = 30001058,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "???",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716023040'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001059] = {
			[1] = {
				["ID"] = 30001059,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716023040'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001059,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716027904'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001060] = {
			[1] = {
				["ID"] = 30001060,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716028160'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001060,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716028416'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001061] = {
			[1] = {
				["ID"] = 30001061,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716028672'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001062] = {
			[1] = {
				["ID"] = 30001062,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716028928'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001063] = {
			[1] = {
				["ID"] = 30001063,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5498363448832'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716029184'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001063,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5498363448832'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716029440'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001063,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5498363448832'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716029696'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30001064] = {
			[1] = {
				["ID"] = 30001064,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590187520'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716029952'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001065] = {
			[1] = {
				["ID"] = 30001065,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716030208'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001066] = {
			[1] = {
				["ID"] = 30001066,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716030464'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001067] = {
			[1] = {
				["ID"] = 30001067,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716030720'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001067,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716030976'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30001068] = {
			[1] = {
				["ID"] = 30001068,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716031232'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001068,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716031488'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001069] = {
			[1] = {
				["ID"] = 30001069,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590187520'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716031744'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001070] = {
			[1] = {
				["ID"] = 30001070,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716032000'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30001071] = {
			[1] = {
				["ID"] = 30001071,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_5498363448832'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716032256'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005001] = {
			[1] = {
				["ID"] = 30005001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166966785'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005002] = {
			[1] = {
				["ID"] = 30005002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716032768'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 500,
			},
		},
		[30005003] = {
			[1] = {
				["ID"] = 30005003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389506305'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005004] = {
			[1] = {
				["ID"] = 30005004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389506561'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005005] = {
			[1] = {
				["ID"] = 30005005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413312'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716033536'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30005005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431944192'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716033792'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005006] = {
			[1] = {
				["ID"] = 30005006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716034048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30005006,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716034304'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 2000,
			},
		},
		[30005007] = {
			[1] = {
				["ID"] = 30005007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716034560'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005008] = {
			[1] = {
				["ID"] = 30005008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716034816'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005009] = {
			[1] = {
				["ID"] = 30005009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716035072'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005010] = {
			[1] = {
				["ID"] = 30005010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716035328'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005011] = {
			[1] = {
				["ID"] = 30005011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716035584'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005012] = {
			[1] = {
				["ID"] = 30005012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716035840'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005013] = {
			[1] = {
				["ID"] = 30005013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716036096'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005014] = {
			[1] = {
				["ID"] = 30005014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431946240'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734024036096'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005015] = {
			[1] = {
				["ID"] = 30005015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_13816104500992'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005016] = {
			[1] = {
				["ID"] = 30005016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716036864'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005017] = {
			[1] = {
				["ID"] = 30005017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716037120'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005018] = {
			[1] = {
				["ID"] = 30005018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716037376'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005019] = {
			[1] = {
				["ID"] = 30005019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716037632'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005020] = {
			[1] = {
				["ID"] = 30005020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431926272'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166972161'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005021] = {
			[1] = {
				["ID"] = 30005021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431926272'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716038144'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30005021,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431926272'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166980609'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005022] = {
			[1] = {
				["ID"] = 30005022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716038656'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005023] = {
			[1] = {
				["ID"] = 30005023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_13816104502016'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005024] = {
			[1] = {
				["ID"] = 30005024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716039168'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005025] = {
			[1] = {
				["ID"] = 30005025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716039424'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005026] = {
			[1] = {
				["ID"] = 30005026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190848'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716039680'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30005027] = {
			[1] = {
				["ID"] = 30005027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716039936'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005028] = {
			[1] = {
				["ID"] = 30005028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716040192'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005029] = {
			[1] = {
				["ID"] = 30005029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716040448'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005030] = {
			[1] = {
				["ID"] = 30005030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716040704'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005031] = {
			[1] = {
				["ID"] = 30005031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716040960'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005032] = {
			[1] = {
				["ID"] = 30005032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716041216'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005033] = {
			[1] = {
				["ID"] = 30005033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389506817'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005034] = {
			[1] = {
				["ID"] = 30005034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716041728'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 500,
			},
		},
		[30005035] = {
			[1] = {
				["ID"] = 30005035,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716041984'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005036] = {
			[1] = {
				["ID"] = 30005036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716042240'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005037] = {
			[1] = {
				["ID"] = 30005037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716042496'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005038] = {
			[1] = {
				["ID"] = 30005038,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716042752'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 1000,
			},
		},
		[30005039] = {
			[1] = {
				["ID"] = 30005039,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716043008'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 500,
			},
		},
		[30005040] = {
			[1] = {
				["ID"] = 30005040,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716043264'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005041] = {
			[1] = {
				["ID"] = 30005041,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716043520'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005042] = {
			[1] = {
				["ID"] = 30005042,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716043776'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005043] = {
			[1] = {
				["ID"] = 30005043,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716044032'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005044] = {
			[1] = {
				["ID"] = 30005044,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716044288'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005045] = {
			[1] = {
				["ID"] = 30005045,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716044544'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005046] = {
			[1] = {
				["ID"] = 30005046,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716044800'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005047] = {
			[1] = {
				["ID"] = 30005047,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166981889'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 500,
			},
		},
		[30005048] = {
			[1] = {
				["ID"] = 30005048,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716045312'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005049] = {
			[1] = {
				["ID"] = 30005049,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716045568'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005050] = {
			[1] = {
				["ID"] = 30005050,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716045824'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 500,
			},
		},
		[30005051] = {
			[1] = {
				["ID"] = 30005051,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716046080'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005052] = {
			[1] = {
				["ID"] = 30005052,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716046336'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005053] = {
			[1] = {
				["ID"] = 30005053,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716046592'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005054] = {
			[1] = {
				["ID"] = 30005054,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716046848'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005055] = {
			[1] = {
				["ID"] = 30005055,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716047104'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005056] = {
			[1] = {
				["ID"] = 30005056,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716047360'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005057] = {
			[1] = {
				["ID"] = 30005057,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716037120'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005058] = {
			[1] = {
				["ID"] = 30005058,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716047872'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005059] = {
			[1] = {
				["ID"] = 30005059,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716048128'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005060] = {
			[1] = {
				["ID"] = 30005060,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716048384'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005061] = {
			[1] = {
				["ID"] = 30005061,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716048640'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005062] = {
			[1] = {
				["ID"] = 30005062,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716048896'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 500,
			},
		},
		[30005063] = {
			[1] = {
				["ID"] = 30005063,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716049152'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005064] = {
			[1] = {
				["ID"] = 30005064,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716049408'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 1000,
			},
		},
		[30005065] = {
			[1] = {
				["ID"] = 30005065,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716049664'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005066] = {
			[1] = {
				["ID"] = 30005066,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716049920'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005067] = {
			[1] = {
				["ID"] = 30005067,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716050176'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005068] = {
			[1] = {
				["ID"] = 30005068,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413568'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716050432'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005069] = {
			[1] = {
				["ID"] = 30005069,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716050688'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005070] = {
			[1] = {
				["ID"] = 30005070,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716050944'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005071] = {
			[1] = {
				["ID"] = 30005071,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716051200'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005072] = {
			[1] = {
				["ID"] = 30005072,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716051456'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005073] = {
			[1] = {
				["ID"] = 30005073,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716051712'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005074] = {
			[1] = {
				["ID"] = 30005074,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716051968'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30005075] = {
			[1] = {
				["ID"] = 30005075,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716052224'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005076] = {
			[1] = {
				["ID"] = 30005076,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716052480'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005077] = {
			[1] = {
				["ID"] = 30005077,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716052736'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005078] = {
			[1] = {
				["ID"] = 30005078,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716052992'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005079] = {
			[1] = {
				["ID"] = 30005079,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716053248'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005080] = {
			[1] = {
				["ID"] = 30005080,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716053504'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005081] = {
			[1] = {
				["ID"] = 30005081,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716053760'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005082] = {
			[1] = {
				["ID"] = 30005082,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716054016'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005083] = {
			[1] = {
				["ID"] = 30005083,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716054272'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005084] = {
			[1] = {
				["ID"] = 30005084,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716054528'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005085] = {
			[1] = {
				["ID"] = 30005085,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716054784'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005086] = {
			[1] = {
				["ID"] = 30005086,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716055040'),
				["VoiceAsset"] = "",
				["Duration"] = 1500,
				["Delay"] = 0,
			},
		},
		[30005087] = {
			[1] = {
				["ID"] = 30005087,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716055296'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005088] = {
			[1] = {
				["ID"] = 30005088,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716055552'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005089] = {
			[1] = {
				["ID"] = 30005089,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716055808'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005090] = {
			[1] = {
				["ID"] = 30005090,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716056064'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005091] = {
			[1] = {
				["ID"] = 30005091,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716056320'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005092] = {
			[1] = {
				["ID"] = 30005092,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590189824'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166981377'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005093] = {
			[1] = {
				["ID"] = 30005093,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716056832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005094] = {
			[1] = {
				["ID"] = 30005094,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190848'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716057088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005095] = {
			[1] = {
				["ID"] = 30005095,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190848'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716057344'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005096] = {
			[1] = {
				["ID"] = 30005096,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716057600'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005097] = {
			[1] = {
				["ID"] = 30005097,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716055040'),
				["VoiceAsset"] = "",
				["Duration"] = 1500,
				["Delay"] = 0,
			},
		},
		[30005098] = {
			[1] = {
				["ID"] = 30005098,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716058112'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005099] = {
			[1] = {
				["ID"] = 30005099,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190848'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716058368'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005100] = {
			[1] = {
				["ID"] = 30005100,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413312'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716058624'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005101] = {
			[1] = {
				["ID"] = 30005101,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716058880'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005102] = {
			[1] = {
				["ID"] = 30005102,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716059136'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30005103] = {
			[1] = {
				["ID"] = 30005103,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716059392'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005104] = {
			[1] = {
				["ID"] = 30005104,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716059648'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005105] = {
			[1] = {
				["ID"] = 30005105,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590190848'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716059904'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005106] = {
			[1] = {
				["ID"] = 30005106,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286413312'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716060160'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005107] = {
			[1] = {
				["ID"] = 30005107,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716060416'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005108] = {
			[1] = {
				["ID"] = 30005108,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716060672'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005109] = {
			[1] = {
				["ID"] = 30005109,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716060928'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005110] = {
			[1] = {
				["ID"] = 30005110,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716061184'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005111] = {
			[1] = {
				["ID"] = 30005111,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716061440'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005112] = {
			[1] = {
				["ID"] = 30005112,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716061696'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005113] = {
			[1] = {
				["ID"] = 30005113,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468120320'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716061952'),
				["VoiceAsset"] = "",
				["Duration"] = 1500,
				["Delay"] = 0,
			},
		},
		[30005114] = {
			[1] = {
				["ID"] = 30005114,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716062208'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005115] = {
			[1] = {
				["ID"] = 30005115,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716062464'),
				["VoiceAsset"] = "",
				["Duration"] = 2250,
				["Delay"] = 0,
			},
		},
		[30005116] = {
			[1] = {
				["ID"] = 30005116,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716048128'),
				["VoiceAsset"] = "",
				["Duration"] = 2250,
				["Delay"] = 0,
			},
		},
		[30005117] = {
			[1] = {
				["ID"] = 30005117,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716062976'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005118] = {
			[1] = {
				["ID"] = 30005118,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716063232'),
				["VoiceAsset"] = "",
				["Duration"] = 3500,
				["Delay"] = 0,
			},
		},
		[30005119] = {
			[1] = {
				["ID"] = 30005119,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716063488'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30005120] = {
			[1] = {
				["ID"] = 30005120,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716063744'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30005121] = {
			[1] = {
				["ID"] = 30005121,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716064000'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30005122] = {
			[1] = {
				["ID"] = 30005122,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716064256'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003001] = {
			[1] = {
				["ID"] = 30003001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716064512'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
			[2] = {
				["ID"] = 30003001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716064768'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003002] = {
			[1] = {
				["ID"] = 30003002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716065024'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
			[2] = {
				["ID"] = 30003002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716065280'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003003] = {
			[1] = {
				["ID"] = 30003003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431968256'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716065536'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
			[2] = {
				["ID"] = 30003003,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431968256'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716065792'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003004] = {
			[1] = {
				["ID"] = 30003004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852625153'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003005] = {
			[1] = {
				["ID"] = 30003005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716066304'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
			[2] = {
				["ID"] = 30003005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716066560'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003006] = {
			[1] = {
				["ID"] = 30003006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852625409'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003007] = {
			[1] = {
				["ID"] = 30003007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852630017'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003008] = {
			[1] = {
				["ID"] = 30003008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716067328'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
			[2] = {
				["ID"] = 30003008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716067584'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003010] = {
			[1] = {
				["ID"] = 30003010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716067840'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003011] = {
			[1] = {
				["ID"] = 30003011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716068096'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003012] = {
			[1] = {
				["ID"] = 30003012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468161024'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716068352'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003013] = {
			[1] = {
				["ID"] = 30003013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716068608'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003014] = {
			[1] = {
				["ID"] = 30003014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716068864'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
			[2] = {
				["ID"] = 30003014,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716069120'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003009] = {
			[1] = {
				["ID"] = 30003009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716069376'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
			[2] = {
				["ID"] = 30003009,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716069632'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003015] = {
			[1] = {
				["ID"] = 30003015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716069888'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003016] = {
			[1] = {
				["ID"] = 30003016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716070144'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003032] = {
			[1] = {
				["ID"] = 30003032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716070400'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003033] = {
			[1] = {
				["ID"] = 30003033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716070656'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003034] = {
			[1] = {
				["ID"] = 30003034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716070912'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30003037] = {
			[1] = {
				["ID"] = 30003037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716071168'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 800,
			},
		},
		[30015001] = {
			[1] = {
				["ID"] = 30015001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389551105'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30015001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468122112'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389551617'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015002] = {
			[1] = {
				["ID"] = 30015002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716071936'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30015002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468122112'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389551361'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30015002,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468122112'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716072448'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015003] = {
			[1] = {
				["ID"] = 30015003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716072704'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015004] = {
			[1] = {
				["ID"] = 30015004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389552641'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30015004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389552642'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015005] = {
			[1] = {
				["ID"] = 30015005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389552897'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015006] = {
			[1] = {
				["ID"] = 30015006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389553153'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015007] = {
			[1] = {
				["ID"] = 30015007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468122112'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56764166986753'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015008] = {
			[1] = {
				["ID"] = 30015008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716074240'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30015008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468122112'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716074496'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30015008,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716074752'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30015009] = {
			[1] = {
				["ID"] = 30015009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389552385'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30015009,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716075264'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30015009,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389552386'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30015009,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716075776'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30011001] = {
			[1] = {
				["ID"] = 30011001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389519617'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011002] = {
			[1] = {
				["ID"] = 30011002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389519873'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 300,
			},
			[2] = {
				["ID"] = 30011002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389519874'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011004] = {
			[1] = {
				["ID"] = 30011004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389520129'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 300,
			},
			[2] = {
				["ID"] = 30011004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389520130'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 300,
			},
			[3] = {
				["ID"] = 30011004,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389520131'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011005] = {
			[1] = {
				["ID"] = 30011005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389520385'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 300,
			},
			[2] = {
				["ID"] = 30011005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389520386'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011006] = {
			[1] = {
				["ID"] = 30011006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389520641'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30011007] = {
			[1] = {
				["ID"] = 30011007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389520897'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011008] = {
			[1] = {
				["ID"] = 30011008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389521153'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011009] = {
			[1] = {
				["ID"] = 30011009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389521409'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011010] = {
			[1] = {
				["ID"] = 30011010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389521665'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011011] = {
			[1] = {
				["ID"] = 30011011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716079360'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011012] = {
			[1] = {
				["ID"] = 30011012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716079616'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011013] = {
			[1] = {
				["ID"] = 30011013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716079872'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 300,
			},
			[2] = {
				["ID"] = 30011013,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716080128'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 300,
			},
		},
		[30011014] = {
			[1] = {
				["ID"] = 30011014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716080384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 300,
			},
			[2] = {
				["ID"] = 30011014,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716080640'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30011015] = {
			[1] = {
				["ID"] = 30011015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716080896'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30011015,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716081152'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011016] = {
			[1] = {
				["ID"] = 30011016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716081408'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30011016,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025600'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716081664'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 100,
			},
			[3] = {
				["ID"] = 30011016,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716081920'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 100,
			},
			[4] = {
				["ID"] = 30011016,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025600'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716082176'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30011016,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716082432'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011017] = {
			[1] = {
				["ID"] = 30011017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716082688'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 500,
			},
			[2] = {
				["ID"] = 30011017,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716082944'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 500,
			},
			[3] = {
				["ID"] = 30011017,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716083200'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 500,
			},
			[4] = {
				["ID"] = 30011017,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716083456'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 1000,
			},
			[5] = {
				["ID"] = 30011017,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432025344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716083712'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 500,
			},
		},
		[30011018] = {
			[1] = {
				["ID"] = 30011018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59246389562882'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389562881'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011019] = {
			[1] = {
				["ID"] = 30011019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54632789334784'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389577473'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011020] = {
			[1] = {
				["ID"] = 30011020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54632789334784'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389577729'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011021] = {
			[1] = {
				["ID"] = 30011021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54632789334784'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389577985'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30011022] = {
			[1] = {
				["ID"] = 30011022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59246389580802'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389580801'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003017] = {
			[1] = {
				["ID"] = 30003017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716085248'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003018] = {
			[1] = {
				["ID"] = 30003018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716085504'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003019] = {
			[1] = {
				["ID"] = 30003019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447650304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716085760'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003019,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468228608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716086016'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003020] = {
			[1] = {
				["ID"] = 30003020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716086272'),
				["VoiceAsset"] = "",
				["Duration"] = 1500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003020,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716086528'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30003020,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716086784'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003023] = {
			[1] = {
				["ID"] = 30003023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716087040'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003024] = {
			[1] = {
				["ID"] = 30003024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716087296'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003025] = {
			[1] = {
				["ID"] = 30003025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716087552'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003026] = {
			[1] = {
				["ID"] = 30003026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716087808'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003027] = {
			[1] = {
				["ID"] = 30003027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468102400'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716088064'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003028] = {
			[1] = {
				["ID"] = 30003028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468102400'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716088320'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003028,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716088576'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003029] = {
			[1] = {
				["ID"] = 30003029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716088832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003030] = {
			[1] = {
				["ID"] = 30003030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716089088'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003030,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716089344'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003031] = {
			[1] = {
				["ID"] = 30003031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716089600'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1800,
			},
		},
		[30003021] = {
			[1] = {
				["ID"] = 30003021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716089856'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1000,
			},
		},
		[30003022] = {
			[1] = {
				["ID"] = 30003022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56832081140480'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003035] = {
			[1] = {
				["ID"] = 30003035,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468228608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716090368'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003035,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663904000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716090624'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003036] = {
			[1] = {
				["ID"] = 30003036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663876864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716090880'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003036,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716091136'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003040] = {
			[1] = {
				["ID"] = 30003040,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432044032'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716091392'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003040,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432043776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716091648'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003041] = {
			[1] = {
				["ID"] = 30003041,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432043776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716091904'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003041,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432044032'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716092160'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003042] = {
			[1] = {
				["ID"] = 30003042,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447656960'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716092416'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003043] = {
			[1] = {
				["ID"] = 30003043,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447656960'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716092672'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003044] = {
			[1] = {
				["ID"] = 30003044,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_58551410120960'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003045] = {
			[1] = {
				["ID"] = 30003045,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716093184'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003046] = {
			[1] = {
				["ID"] = 30003046,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468094464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716093440'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003047] = {
			[1] = {
				["ID"] = 30003047,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716093696'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003047,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716093952'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003048] = {
			[1] = {
				["ID"] = 30003048,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716094208'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003048,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716094464'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003049] = {
			[1] = {
				["ID"] = 30003049,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716094720'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003050] = {
			[1] = {
				["ID"] = 30003050,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716094976'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003050,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716095232'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003051] = {
			[1] = {
				["ID"] = 30003051,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432044032'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716095488'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003051,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432043776'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716095744'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003052] = {
			[1] = {
				["ID"] = 30003052,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716096000'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003053] = {
			[1] = {
				["ID"] = 30003053,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716096256'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 500,
			},
		},
		[30003054] = {
			[1] = {
				["ID"] = 30003054,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716096512'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003054,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716096768'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003055] = {
			[1] = {
				["ID"] = 30003055,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716097024'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003055,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716097280'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003056] = {
			[1] = {
				["ID"] = 30003056,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286415616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716097536'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 1000,
			},
			[2] = {
				["ID"] = 30003056,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286415616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716097792'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003057] = {
			[1] = {
				["ID"] = 30003057,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286415616'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716098048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 1000,
			},
		},
		[30003058] = {
			[1] = {
				["ID"] = 30003058,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716098304'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003058,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716098560'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003059] = {
			[1] = {
				["ID"] = 30003059,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468094464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716098816'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003060] = {
			[1] = {
				["ID"] = 30003060,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716099072'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1000,
			},
			[2] = {
				["ID"] = 30003060,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716099328'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30003060,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_40477382412288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716099584'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003061] = {
			[1] = {
				["ID"] = 30003061,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716099840'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 500,
			},
		},
		[30003062] = {
			[1] = {
				["ID"] = 30003062,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468094464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716100096'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003062,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716100352'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003063] = {
			[1] = {
				["ID"] = 30003063,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716100608'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003063,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716100864'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003064] = {
			[1] = {
				["ID"] = 30003064,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716101120'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003065] = {
			[1] = {
				["ID"] = 30003065,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716101376'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003065,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716101632'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30003066] = {
			[1] = {
				["ID"] = 30003066,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468094464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716101888'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003067] = {
			[1] = {
				["ID"] = 30003067,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468094464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716102144'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 500,
			},
		},
		[30003068] = {
			[1] = {
				["ID"] = 30003068,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447666944'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716102400'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 1000,
			},
		},
		[30003069] = {
			[1] = {
				["ID"] = 30003069,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716102656'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003069,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716102912'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30003069,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716103168'),
				["VoiceAsset"] = "",
				["Duration"] = 1000,
				["Delay"] = 0,
			},
		},
		[30003070] = {
			[1] = {
				["ID"] = 30003070,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432046080'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716103424'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003071] = {
			[1] = {
				["ID"] = 30003071,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432046080'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58551410173440'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30003072] = {
			[1] = {
				["ID"] = 30003072,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468161024'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716103936'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 1000,
			},
		},
		[30003073] = {
			[1] = {
				["ID"] = 30003073,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468094464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716104192'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 2000,
			},
		},
		[30003074] = {
			[1] = {
				["ID"] = 30003074,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716104448'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 1000,
			},
			[2] = {
				["ID"] = 30003074,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716104704'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003075] = {
			[1] = {
				["ID"] = 30003075,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716104960'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 500,
			},
		},
		[30003076] = {
			[1] = {
				["ID"] = 30003076,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432046080'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716105216'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30003076,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432046080'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716105472'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30003077] = {
			[1] = {
				["ID"] = 30003077,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468100608'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716105728'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 1000,
			},
		},
		[30003078] = {
			[1] = {
				["ID"] = 30003078,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716105984'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 500,
			},
			[2] = {
				["ID"] = 30003078,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716106240'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30003078,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432046080'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716106496'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003079] = {
			[1] = {
				["ID"] = 30003079,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716106752'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30003080] = {
			[1] = {
				["ID"] = 30003080,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432046080'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716107008'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30010001] = {
			[1] = {
				["ID"] = 30010001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716107264'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010002] = {
			[1] = {
				["ID"] = 30010002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716107520'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010003] = {
			[1] = {
				["ID"] = 30010003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716107776'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010003,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716108032'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010004] = {
			[1] = {
				["ID"] = 30010004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716108288'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716108544'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010005] = {
			[1] = {
				["ID"] = 30010005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716108800'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716109056'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010006] = {
			[1] = {
				["ID"] = 30010006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716109312'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010007] = {
			[1] = {
				["ID"] = 30010007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716109568'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010007,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716109824'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010008] = {
			[1] = {
				["ID"] = 30010008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716110080'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 2500,
			},
			[2] = {
				["ID"] = 30010008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716110336'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30010008,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716110592'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30010008,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716110848'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010009] = {
			[1] = {
				["ID"] = 30010009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716111104'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010009,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716111360'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30010009,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716111616'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010010] = {
			[1] = {
				["ID"] = 30010010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716111872'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010010,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716112128'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010011] = {
			[1] = {
				["ID"] = 30010011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716112384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010012] = {
			[1] = {
				["ID"] = 30010012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716112640'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010013] = {
			[1] = {
				["ID"] = 30010013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716112896'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010014] = {
			[1] = {
				["ID"] = 30010014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432064256'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716113152'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010014,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432064256'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716113408'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30010014,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432064256'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716113664'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010017] = {
			[1] = {
				["ID"] = 30010017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716113920'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010018] = {
			[1] = {
				["ID"] = 30010018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716114176'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010018,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716114432'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010019] = {
			[1] = {
				["ID"] = 30010019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432064512'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716114688'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30010020] = {
			[1] = {
				["ID"] = 30010020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716114944'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010020,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716115200'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010022] = {
			[1] = {
				["ID"] = 30010022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432064512'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716115456'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30010023] = {
			[1] = {
				["ID"] = 30010023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716115712'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30010023,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716115968'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30014001] = {
			[1] = {
				["ID"] = 30014001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716116224'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716116480'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014002] = {
			[1] = {
				["ID"] = 30014002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_13264201224960'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716116992'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30014003] = {
			[1] = {
				["ID"] = 30014003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716117248'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014003,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716117504'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30014004] = {
			[1] = {
				["ID"] = 30014004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716117760'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716118016'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30014005] = {
			[1] = {
				["ID"] = 30014005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716118272'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447683072'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716118528'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30014005,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716118784'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014006] = {
			[1] = {
				["ID"] = 30014006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447683072'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716119040'),
				["VoiceAsset"] = "",
				["Duration"] = 15000,
				["Delay"] = 0,
			},
		},
		[30014007] = {
			[1] = {
				["ID"] = 30014007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447683072'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716119296'),
				["VoiceAsset"] = "",
				["Duration"] = 10000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014007,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447683072'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716119552'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30014007,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_13815299191040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716119808'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30014008] = {
			[1] = {
				["ID"] = 30014008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716120064'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590189824'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716120320'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30014008,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447685120'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716120576'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30014009] = {
			[1] = {
				["ID"] = 30014009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716120832'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014010] = {
			[1] = {
				["ID"] = 30014010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716121088'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014011] = {
			[1] = {
				["ID"] = 30014011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716121344'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014012] = {
			[1] = {
				["ID"] = 30014012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590189824'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716121600'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014013] = {
			[1] = {
				["ID"] = 30014013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716121856'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014014] = {
			[1] = {
				["ID"] = 30014014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716122112'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014015] = {
			[1] = {
				["ID"] = 30014015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716122368'),
				["VoiceAsset"] = "",
				["Duration"] = 6000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014015,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716122624'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30014015,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716122880'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30014016] = {
			[1] = {
				["ID"] = 30014016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716123136'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014017] = {
			[1] = {
				["ID"] = 30014017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716123392'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014018] = {
			[1] = {
				["ID"] = 30014018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716123648'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014019] = {
			[1] = {
				["ID"] = 30014019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716123904'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30014020] = {
			[1] = {
				["ID"] = 30014020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716124160'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014021] = {
			[1] = {
				["ID"] = 30014021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716124416'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014021,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716124672'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014022] = {
			[1] = {
				["ID"] = 30014022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716124928'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30014022,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716125184'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30014023] = {
			[1] = {
				["ID"] = 30014023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716125440'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014024] = {
			[1] = {
				["ID"] = 30014024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716125696'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30014025] = {
			[1] = {
				["ID"] = 30014025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716125952'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30012001] = {
			[1] = {
				["ID"] = 30012001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716126208'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30012002] = {
			[1] = {
				["ID"] = 30012002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389528065'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30012003] = {
			[1] = {
				["ID"] = 30012003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389528321'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30012004] = {
			[1] = {
				["ID"] = 30012004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389528577'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30012005] = {
			[1] = {
				["ID"] = 30012005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389529857'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30017001] = {
			[1] = {
				["ID"] = 30017001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59245852686338'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852686337'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 300,
			},
		},
		[30020001] = {
			[1] = {
				["ID"] = 30020001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389575681'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020002] = {
			[1] = {
				["ID"] = 30020002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_29756607255040'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389581313'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020003] = {
			[1] = {
				["ID"] = 30020003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389581825'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020004] = {
			[1] = {
				["ID"] = 30020004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447693056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716128512'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020004,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716128768'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30020004,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716129024'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30020005] = {
			[1] = {
				["ID"] = 30020005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590229760'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716129280'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432052992'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716129536'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30020005,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432052992'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716129792'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30020005,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590229760'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716130048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30020005,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432052992'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716130304'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[6] = {
				["ID"] = 30020005,
				["Order"] = 6,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590229760'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734024325632'),
				["VoiceAsset"] = "",
				["Duration"] = 1000,
				["Delay"] = 0,
			},
		},
		[30020006] = {
			[1] = {
				["ID"] = 30020006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716130816'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020007] = {
			[1] = {
				["ID"] = 30020007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716131072'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020008] = {
			[1] = {
				["ID"] = 30020008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447695872'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716131328'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447695872'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716131584'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30020008,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432056320'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716131840'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30020009] = {
			[1] = {
				["ID"] = 30020009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432056320'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716132096'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020009,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716132352'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30020009,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432056576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716132608'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30020009,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56832081032704'),
				["VoiceAsset"] = "",
				["Duration"] = 1000,
				["Delay"] = 0,
			},
		},
		[30020010] = {
			[1] = {
				["ID"] = 30020010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716133120'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020011] = {
			[1] = {
				["ID"] = 30020011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432057344'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716133376'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020011,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716133632'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020012] = {
			[1] = {
				["ID"] = 30020012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716133888'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020012,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716134144'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020013] = {
			[1] = {
				["ID"] = 30020013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716134400'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020013,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716134656'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30020013,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716134912'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020014] = {
			[1] = {
				["ID"] = 30020014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716135168'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020014,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716135424'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020015] = {
			[1] = {
				["ID"] = 30020015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432057600'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716135680'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020016] = {
			[1] = {
				["ID"] = 30020016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716135936'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020017] = {
			[1] = {
				["ID"] = 30020017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716136192'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020017,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716136448'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30020017,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716136704'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020018] = {
			[1] = {
				["ID"] = 30020018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716136960'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020019] = {
			[1] = {
				["ID"] = 30020019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389599233'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020020] = {
			[1] = {
				["ID"] = 30020020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716137472'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020021] = {
			[1] = {
				["ID"] = 30020021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716137728'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020021,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716137984'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020022] = {
			[1] = {
				["ID"] = 30020022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716138240'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020023] = {
			[1] = {
				["ID"] = 30020023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716138496'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020024] = {
			[1] = {
				["ID"] = 30020024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716138752'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020025] = {
			[1] = {
				["ID"] = 30020025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716139008'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30020026] = {
			[1] = {
				["ID"] = 30020026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716139264'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020027] = {
			[1] = {
				["ID"] = 30020027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716139520'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020028] = {
			[1] = {
				["ID"] = 30020028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432057600'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58139093277952'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30020028,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716140032'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30020028,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716140288'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30020028,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716140544'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30020028,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716140800'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020029] = {
			[1] = {
				["ID"] = 30020029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389598209'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020030] = {
			[1] = {
				["ID"] = 30020030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716141312'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020031] = {
			[1] = {
				["ID"] = 30020031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716141568'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020032] = {
			[1] = {
				["ID"] = 30020032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716141824'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020033] = {
			[1] = {
				["ID"] = 30020033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716142080'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020034] = {
			[1] = {
				["ID"] = 30020034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716142336'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020035] = {
			[1] = {
				["ID"] = 30020035,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716142592'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020036] = {
			[1] = {
				["ID"] = 30020036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716142848'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020037] = {
			[1] = {
				["ID"] = 30020037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716143104'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30020038] = {
			[1] = {
				["ID"] = 30020038,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716143360'),
				["VoiceAsset"] = "",
				["Duration"] = 2500,
				["Delay"] = 0,
			},
		},
		[30019001] = {
			[1] = {
				["ID"] = 30019001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59245852708865'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 1000,
			},
		},
		[30019002] = {
			[1] = {
				["ID"] = 30019002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389579777'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 1000,
			},
		},
		[30018001] = {
			[1] = {
				["ID"] = 30018001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389563905'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30018002] = {
			[1] = {
				["ID"] = 30018002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389564161'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30018003] = {
			[1] = {
				["ID"] = 30018003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716144640'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30018004] = {
			[1] = {
				["ID"] = 30018004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716144896'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30018005] = {
			[1] = {
				["ID"] = 30018005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389564673'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30018005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389564674'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30018006] = {
			[1] = {
				["ID"] = 30018006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389564929'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30018006,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389564930'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30018006,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389564932'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30018006,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389564933'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30018007] = {
			[1] = {
				["ID"] = 30018007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389565185'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30018007,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389565186'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30018007,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389565188'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30018007,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716147456'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30018008] = {
			[1] = {
				["ID"] = 30018008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716147712'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30018008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716147968'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30018008,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58139093248512'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30018009] = {
			[1] = {
				["ID"] = 30018009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716148480'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30018010] = {
			[1] = {
				["ID"] = 30018010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716148736'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30018011] = {
			[1] = {
				["ID"] = 30018011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389569281'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30018012] = {
			[1] = {
				["ID"] = 30018012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716149248'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30018013] = {
			[1] = {
				["ID"] = 30018013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590229760'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716149504'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30018013,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590229760'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716149760'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30018013,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716150016'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30018014] = {
			[1] = {
				["ID"] = 30018014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716150272'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30018014,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716150528'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30018015] = {
			[1] = {
				["ID"] = 30018015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716150784'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30018016] = {
			[1] = {
				["ID"] = 30018016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716151040'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30018017] = {
			[1] = {
				["ID"] = 30018017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716150784'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30018018] = {
			[1] = {
				["ID"] = 30018018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663914496'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716151552'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30030001] = {
			[1] = {
				["ID"] = 30030001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389575937'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30030001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_59246389575938'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30030002] = {
			[1] = {
				["ID"] = 30030002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447716864'),
				["Text"] = Game.TableDataManager:GetLangStr('str_32024886790400'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30013001] = {
			[1] = {
				["ID"] = 30013001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468120320'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716152576'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30013002] = {
			[1] = {
				["ID"] = 30013002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431858432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716152832'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30013003] = {
			[1] = {
				["ID"] = 30013003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431858432'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716153088'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30013004] = {
			[1] = {
				["ID"] = 30013004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432108800'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716153344'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30013005] = {
			[1] = {
				["ID"] = 30013005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432108800'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716153600'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30013005,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552432108800'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716153856'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30013006] = {
			[1] = {
				["ID"] = 30013006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716154112'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30013007] = {
			[1] = {
				["ID"] = 30013007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716154368'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30013008] = {
			[1] = {
				["ID"] = 30013008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716154624'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30050001] = {
			[1] = {
				["ID"] = 30050001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663889408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716154880'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30050001,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663885056'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716155136'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30050002] = {
			[1] = {
				["ID"] = 30050002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716155392'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30050003] = {
			[1] = {
				["ID"] = 30050003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716155648'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30050003,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716155904'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30050004] = {
			[1] = {
				["ID"] = 30050004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716156160'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30050005] = {
			[1] = {
				["ID"] = 30050005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716156416'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30050006] = {
			[1] = {
				["ID"] = 30050006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716156672'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30050007] = {
			[1] = {
				["ID"] = 30050007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431852288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716156928'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30040001] = {
			[1] = {
				["ID"] = 30040001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716157184'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040002] = {
			[1] = {
				["ID"] = 30040002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716157440'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040003] = {
			[1] = {
				["ID"] = 30040003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590205696'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716157696'),
				["VoiceAsset"] = "",
				["Duration"] = 3200,
				["Delay"] = 0,
			},
		},
		[30040004] = {
			[1] = {
				["ID"] = 30040004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716157952'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040005] = {
			[1] = {
				["ID"] = 30040005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716158208'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040006] = {
			[1] = {
				["ID"] = 30040006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716158464'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040007] = {
			[1] = {
				["ID"] = 30040007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716157952'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040008] = {
			[1] = {
				["ID"] = 30040008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590205696'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716158976'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040009] = {
			[1] = {
				["ID"] = 30040009,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590205696'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716159232'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040010] = {
			[1] = {
				["ID"] = 30040010,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716159488'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040011] = {
			[1] = {
				["ID"] = 30040011,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716159744'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30040012] = {
			[1] = {
				["ID"] = 30040012,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716160000'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040013] = {
			[1] = {
				["ID"] = 30040013,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716160256'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040014] = {
			[1] = {
				["ID"] = 30040014,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716160512'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040015] = {
			[1] = {
				["ID"] = 30040015,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716160768'),
				["VoiceAsset"] = "",
				["Duration"] = 3200,
				["Delay"] = 0,
			},
		},
		[30040016] = {
			[1] = {
				["ID"] = 30040016,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716161024'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040017] = {
			[1] = {
				["ID"] = 30040017,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716161280'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040017,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286416896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716161536'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30040017,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716161792'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30040017,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36628286416896'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716162048'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040018] = {
			[1] = {
				["ID"] = 30040018,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431833600'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716162304'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040019] = {
			[1] = {
				["ID"] = 30040019,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716162560'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040020] = {
			[1] = {
				["ID"] = 30040020,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716162816'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040021] = {
			[1] = {
				["ID"] = 30040021,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716163072'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040022] = {
			[1] = {
				["ID"] = 30040022,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408519680'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716163328'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040022,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408519680'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716163584'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30040022,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408519680'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716163840'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30040022,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408519680'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716164096'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30040023] = {
			[1] = {
				["ID"] = 30040023,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408519680'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716164352'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040023,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716164608'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30040023,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408519680'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716164864'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30040024] = {
			[1] = {
				["ID"] = 30040024,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408519680'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716165120'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30040025] = {
			[1] = {
				["ID"] = 30040025,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716165376'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040025_1",
				["Duration"] = 8000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040025,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716165632'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040025_2",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30040025,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716165888'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040025_3",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30040025,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716166144'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040025_4",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30040026] = {
			[1] = {
				["ID"] = 30040026,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716166400'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040026_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040026,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716166656'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040026_2",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040027] = {
			[1] = {
				["ID"] = 30040027,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716166912'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040027_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30040028] = {
			[1] = {
				["ID"] = 30040028,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716167168'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040028_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30040029] = {
			[1] = {
				["ID"] = 30040029,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716167424'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040029_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30040030] = {
			[1] = {
				["ID"] = 30040030,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716167680'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040030_1",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040030,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716167936'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Player_30040030_2",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30040030,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716168192'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040030_3",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30040030,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716168448'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040030_4",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30040031] = {
			[1] = {
				["ID"] = 30040031,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716168704'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040031_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040031,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716168960'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040031_2",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040032] = {
			[1] = {
				["ID"] = 30040032,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_56695447426304'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716169216'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040032_1",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30040032,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716169472'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040032_2",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30040032,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377472'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716169728'),
				["VoiceAsset"] = "Play_Vo_AsideTalk_Cotard_30040032_3",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040033] = {
			[1] = {
				["ID"] = 30040033,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716169984'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30040034] = {
			[1] = {
				["ID"] = 30040034,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716170240'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040035] = {
			[2] = {
				["ID"] = 30040035,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716170496'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040036] = {
			[1] = {
				["ID"] = 30040036,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716170752'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30040037] = {
			[1] = {
				["ID"] = 30040037,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716171008'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040038] = {
			[1] = {
				["ID"] = 30040038,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716171264'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040039] = {
			[1] = {
				["ID"] = 30040039,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716171520'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040040] = {
			[1] = {
				["ID"] = 30040040,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716171776'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040041] = {
			[1] = {
				["ID"] = 30040041,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716172032'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040042] = {
			[1] = {
				["ID"] = 30040042,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716172288'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040043] = {
			[1] = {
				["ID"] = 30040043,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716172544'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040044] = {
			[1] = {
				["ID"] = 30040044,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716172800'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040045] = {
			[1] = {
				["ID"] = 30040045,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716173056'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30040046] = {
			[1] = {
				["ID"] = 30040046,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_54083570377216'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716173312'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041000] = {
			[1] = {
				["ID"] = 30041000,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716173568'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30041001] = {
			[1] = {
				["ID"] = 30041001,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716173824'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30041002] = {
			[1] = {
				["ID"] = 30041002,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716174080'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30041002,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716174336'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041003] = {
			[1] = {
				["ID"] = 30041003,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468126464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716174592'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041004] = {
			[1] = {
				["ID"] = 30041004,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716174848'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041005] = {
			[1] = {
				["ID"] = 30041005,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716175104'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041006] = {
			[1] = {
				["ID"] = 30041006,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716175360'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041007] = {
			[1] = {
				["ID"] = 30041007,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716175616'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041008] = {
			[1] = {
				["ID"] = 30041008,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716175872'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30041008,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716176128'),
				["VoiceAsset"] = "",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041101] = {
			[1] = {
				["ID"] = 30041101,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716176384'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30041102] = {
			[1] = {
				["ID"] = 30041102,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716176640'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30041103] = {
			[1] = {
				["ID"] = 30041103,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716176896'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30041104] = {
			[1] = {
				["ID"] = 30041104,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716177152'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30041104,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716177408'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30041106] = {
			[1] = {
				["ID"] = 30041106,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716177664'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30041106,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716177920'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30041108] = {
			[1] = {
				["ID"] = 30041108,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716178176'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30041108,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_26734023961088'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30041109] = {
			[1] = {
				["ID"] = 30041109,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431974912'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716178688'),
				["VoiceAsset"] = "",
				["Duration"] = 5000,
				["Delay"] = 0,
			},
		},
		[30041110] = {
			[1] = {
				["ID"] = 30041110,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = "-1",
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716178944'),
				["VoiceAsset"] = "",
				["Duration"] = 4000,
				["Delay"] = 0,
			},
		},
		[30041201] = {
			[1] = {
				["ID"] = 30041201,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716179200'),
				["VoiceAsset"] = "Play_AsideTalk_Sqgs_Skarner_30041201_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041202] = {
			[1] = {
				["ID"] = 30041202,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716179456'),
				["VoiceAsset"] = "Play_AsideTalk_Sqgs_Skarner_30041202_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30041203] = {
			[1] = {
				["ID"] = 30041203,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590218752'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695716179712'),
				["VoiceAsset"] = "Play_AsideTalk_Sqgs_Skarner_30041203_1",
				["Duration"] = 3000,
				["Delay"] = 0,
			},
		},
		[30001100] = {
			[1] = {
				["ID"] = 30001100,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349513216'),
				["VoiceAsset"] = "",
				["Duration"] = 2,
				["Delay"] = 0,
			},
		},
		[30001101] = {
			[1] = {
				["ID"] = 30001101,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349513472'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001101,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349513728'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001102] = {
			[1] = {
				["ID"] = 30001102,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349513984'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001102,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349514240'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001103] = {
			[1] = {
				["ID"] = 30001103,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349514496'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001103,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349514752'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001104] = {
			[1] = {
				["ID"] = 30001104,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349515008'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001105] = {
			[1] = {
				["ID"] = 30001105,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349515264'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001105,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349515520'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001106] = {
			[1] = {
				["ID"] = 30001106,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349515776'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001106,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349516032'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001106,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349516288'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001108] = {
			[1] = {
				["ID"] = 30001108,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349516544'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001109] = {
			[1] = {
				["ID"] = 30001109,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349516800'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001109,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349517056'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001109,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349517312'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001110] = {
			[1] = {
				["ID"] = 30001110,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349517568'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001111] = {
			[1] = {
				["ID"] = 30001111,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349517824'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001112] = {
			[1] = {
				["ID"] = 30001112,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349518080'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001112,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349518336'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001113] = {
			[1] = {
				["ID"] = 30001113,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38552431761408'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349518592'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001113,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349518848'),
				["VoiceAsset"] = "",
				["Duration"] = 0,
				["Delay"] = 0,
			},
		},
		[30001114] = {
			[1] = {
				["ID"] = 30001114,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349519104'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001114,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349519360'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001115] = {
			[1] = {
				["ID"] = 30001115,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_57520081084160'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349519616'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001115,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349519872'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001115,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_57520081084160'),
				["Text"] = Game.TableDataManager:GetLangStr('str_58139093292032'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001116] = {
			[1] = {
				["ID"] = 30001116,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349520384'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001116,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349520640'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001116,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349520896'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30001116,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349521152'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001117] = {
			[1] = {
				["ID"] = 30001117,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349521408'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001118] = {
			[1] = {
				["ID"] = 30001118,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349521664'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001119] = {
			[1] = {
				["ID"] = 30001119,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_56695715989760'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001119,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349522176'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001119,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349522432'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30001119,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349522688'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001120] = {
			[1] = {
				["ID"] = 30001120,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349522944'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001121] = {
			[1] = {
				["ID"] = 30001121,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349523200'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001121,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_59236994281728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349523456'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001122] = {
			[1] = {
				["ID"] = 30001122,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349523712'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001122,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408521728'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349523968'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001123] = {
			[1] = {
				["ID"] = 30001123,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349524224'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001124] = {
			[1] = {
				["ID"] = 30001124,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349524480'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001124,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349524736'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001125] = {
			[1] = {
				["ID"] = 30001125,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349524992'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001126] = {
			[1] = {
				["ID"] = 30001126,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349525248'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001126,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349525504'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001127] = {
			[1] = {
				["ID"] = 30001127,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27902255040000'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349525760'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001128] = {
			[1] = {
				["ID"] = 30001128,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349526016'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001128,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349526272'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001128,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_38622493422336'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001129] = {
			[1] = {
				["ID"] = 30001129,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349526784'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001130] = {
			[1] = {
				["ID"] = 30001130,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349527040'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001130,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349527296'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001131] = {
			[1] = {
				["ID"] = 30001131,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349527552'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001132] = {
			[1] = {
				["ID"] = 30001132,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349527808'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001132,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468107264'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349528064'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001133] = {
			[1] = {
				["ID"] = 30001133,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349528320'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001133,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349528576'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001134] = {
			[1] = {
				["ID"] = 30001134,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349528832'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001134,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349529088'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001135] = {
			[1] = {
				["ID"] = 30001135,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349529344'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001135,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590251008'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349529600'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001136] = {
			[1] = {
				["ID"] = 30001136,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349529856'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001136,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349530112'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001137] = {
			[1] = {
				["ID"] = 30001137,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590251008'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349530368'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001138] = {
			[1] = {
				["ID"] = 30001138,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590238464'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349530624'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001138,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349530880'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001139] = {
			[1] = {
				["ID"] = 30001139,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349531136'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001139,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349531392'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001139,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349531648'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30001139,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349531904'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[5] = {
				["ID"] = 30001139,
				["Order"] = 5,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349532160'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001140] = {
			[1] = {
				["ID"] = 30001140,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349532416'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001140,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349532672'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001141] = {
			[1] = {
				["ID"] = 30001141,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349532928'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001142] = {
			[1] = {
				["ID"] = 30001142,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_36353408502272'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349533184'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001142,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349533440'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[3] = {
				["ID"] = 30001142,
				["Order"] = 3,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349533696'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[4] = {
				["ID"] = 30001142,
				["Order"] = 4,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349533952'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001143] = {
			[1] = {
				["ID"] = 30001143,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349534208'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001143,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349534464'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001144] = {
			[1] = {
				["ID"] = 30001144,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349534720'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001145] = {
			[1] = {
				["ID"] = 30001145,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349534976'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001146] = {
			[1] = {
				["ID"] = 30001146,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_39033468104448'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349535232'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001146,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349535488'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001147] = {
			[1] = {
				["ID"] = 30001147,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_57520081100288'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349535744'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001148] = {
			[1] = {
				["ID"] = 30001148,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349536000'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001149] = {
			[1] = {
				["ID"] = 30001149,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349536256'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001149,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349536512'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001150] = {
			[1] = {
				["ID"] = 30001150,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349536768'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
			[2] = {
				["ID"] = 30001150,
				["Order"] = 2,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_27901718169088'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349537024'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
		[30001151] = {
			[1] = {
				["ID"] = 30001151,
				["Order"] = 1,
				["TalkerTitle"] = "",
				["TalkerName"] = Game.TableDataManager:GetLangStr('str_38758590239232'),
				["Text"] = Game.TableDataManager:GetLangStr('str_57520349537280'),
				["VoiceAsset"] = "",
				["Duration"] = 2000,
				["Delay"] = 0,
			},
		},
	},
}

return TopData
