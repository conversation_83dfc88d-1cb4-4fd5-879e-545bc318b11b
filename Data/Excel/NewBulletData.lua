--
-- 表名: NEW Inputs: (NewBullet.xlsx, NewBullet); Outputs: NewBulletData; Def:data_post_export_new_bullet.lua
--

local TopData = {
    data = {
        [1] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581469696'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 1, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1000', 
            ['Offset'] = {100, 0, 100}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [2] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581469952'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 2, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {100, 0, 100}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [3] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581470208'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 3, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {100, 0, 100}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [4] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581470464'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 4, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {100, 0, 100}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [5] = {
            ['Acceleration'] = 700, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 8, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581470720'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 5, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 0, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Parabola', 
            ['Offset'] = {100, 0, 100}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX/Attack_New/NS_Visionary_Attack01_Trail.NS_Visionary_Attack01_Trail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [6] = {
            ['Acceleration'] = 980, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 8, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581470976'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 6, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Parabola', 
            ['Offset'] = {100, 0, 100}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [7] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 7, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581471232'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 10000001, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 7, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 1, 
            ['MoveCurveID'] = 10000001, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Curve,10000001', 
            ['Offset'] = {100, 0, 100}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 999, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [16] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581473536'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 10, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 16, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 10, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,10', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 200, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [17] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581473792'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 17, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 10, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 200, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [18] = {
            ['Acceleration'] = 500, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 9, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581474048'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Missile.NS_WizardAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 18, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 0, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'ParabolaWithTrack,300,3', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TargetPointMoveSpeed'] = 300, 
            ['TotalHitTime'] = 0, 
            ['TrackDuration'] = 3, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [88099355] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 300, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581479936'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Theil/Skill04_FlippingOver/NS_A_Boss_Theil_Skill04_FlippingOver_Attack_03_Bullet.NS_A_Boss_Theil_Skill04_FlippingOver_Attack_03_Bullet', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3600, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 88099355, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1.2, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3600, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3600', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {-35, 0, 0}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 6, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [89000103] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581475328'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Shooter_FX/Attack/NS_Shooter_Attack01_Fly.NS_Shooter_Attack01_Fly', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1600, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 89000103, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1600, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1600', 
            ['Offset'] = {115, 0, 57}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [89000107] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581475584'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/New/NS_WizardAttack_Fly.NS_WizardAttack_Fly', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/NS_WizardAttack_Hit.NS_WizardAttack_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_MobsMage_Attack_Hit', 
            ['ID'] = 89000107, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.77*Lv', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [89000112] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581475840'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Energyball/NS_Energyball_01.NS_Energyball_01', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1600, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Monster_MobsInvisible_EmitLoop', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 89000112, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 20, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1600, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1600', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 200, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [89000129] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'weapon_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581476096'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boomber_FX/Skill02/NS_Boomber_skill02_Missile.NS_Boomber_skill02_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 89000129, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.77*Lv', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [89000130] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 190, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581476352'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Energyball/NS_Energyball_Huge.NS_Energyball_Huge', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1600, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Monster_MobsBigInvisible_EmitLoop', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 89000130, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 30, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1600, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1600', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 100, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [89001393] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581511424'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 89001393, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.77*Lv', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [800091401] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581521920'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moon_FX/Skill10_New/NS_BossMoon_Skill01_Ray.NS_BossMoon_Skill01_Ray', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 800091401, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 90, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '40', ['FuncName'] = 'MagAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860200011] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581480192'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_VisionaryAttack_New_Hit.NS_VisionaryAttack_New_Hit', 
            ['HitEffectPlayMode'] = 2, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Hit_L', 
            ['ID'] = 860200011, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.377', ['2'] = '0.377', ['3'] = '67*Lv*0.377/3', ['FuncName'] = 'MagAtk_Vision', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_Visionary_Attack01_new_B.NS_Visionary_Attack01_new_B', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860200012] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581480448'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_VisionaryAttack_New_Hit.NS_VisionaryAttack_New_Hit', 
            ['HitEffectPlayMode'] = 2, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Hit_L', 
            ['ID'] = 860200012, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 1, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.377', ['2'] = '0.377', ['3'] = '67*Lv*0.377/3', ['FuncName'] = 'MagAtk_Vision', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {0.7, 0.7, 0.7}, 
            ['Scale_HitEffect'] = {0.7, 0.7, 0.7}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_Visionary_Attack01_new_B.NS_Visionary_Attack01_new_B', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860200021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581480704'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_VisionaryAttack_New_Hit.NS_VisionaryAttack_New_Hit', 
            ['HitEffectPlayMode'] = 2, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Hit_L', 
            ['ID'] = 860200021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.377', ['2'] = '0.377', ['3'] = '67*Lv*0.377/3', ['FuncName'] = 'MagAtk_Vision', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_Visionary_Attack01_new_B.NS_Visionary_Attack01_new_B', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860200031] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581480960'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_VisionaryAttack_New_Hit.NS_VisionaryAttack_New_Hit', 
            ['HitEffectPlayMode'] = 2, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Hit_H', 
            ['ID'] = 860200031, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.377', ['2'] = '0.377', ['3'] = '67*Lv*0.377/3', ['FuncName'] = 'MagAtk_Vision', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_Visionary_Attack01_new_B3.NS_Visionary_Attack01_new_B3', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860200041] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 10, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581481216'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 2000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Visionary_Skill_Psychological_Madness_Ball', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 860200041, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 999, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 2000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,2000', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860200371] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581483008'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 860200371, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['FuncName'] = 'AddField', ['ID'] = 860200371, ['Target'] = 'tar', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill09_New/NS_VisionarySkill09_T.NS_VisionarySkill09_T', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860200372] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581483264'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 860200372, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill09_New/NS_VisionarySkill09_T.NS_VisionarySkill09_T', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860210601] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0.2, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581481472'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_03.NS_VisionarySkill04_T_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_Hit.NS_VisionarySkill04_T_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Heal', 
            ['ID'] = 860210601, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.3, 
            ['MinVelocity'] = 2000, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 50}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['2'] = '2.04', ['3'] = '(3.495*Lv*Lv+37.126*Lv+134.953)*2.04', ['FuncName'] = 'AddHP_Player', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = true, 
            ['Velocity'] = 5000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860210602] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0.2, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581481728'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_03.NS_VisionarySkill04_T_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_Hit.NS_VisionarySkill04_T_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Heal', 
            ['ID'] = 860210602, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.3, 
            ['MinVelocity'] = 2000, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 50}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['2'] = '1.63', ['3'] = '(3.495*Lv*Lv+37.126*Lv+134.953)*1.63', ['FuncName'] = 'AddHP_Player', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = true, 
            ['Velocity'] = 5000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860210603] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0.2, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581481984'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_03.NS_VisionarySkill04_T_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_Hit.NS_VisionarySkill04_T_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Heal', 
            ['ID'] = 860210603, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.3, 
            ['MinVelocity'] = 2000, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 50}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['2'] = '1.31', ['3'] = '(3.495*Lv*Lv+37.126*Lv+134.953)*1.31', ['FuncName'] = 'AddHP_Player', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = true, 
            ['Velocity'] = 5000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860210604] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0.2, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581482240'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_03.NS_VisionarySkill04_T_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill04_New/NS_VisionarySkill04_T_Hit.NS_VisionarySkill04_T_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Heal', 
            ['ID'] = 860210604, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.3, 
            ['MinVelocity'] = 2000, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '*d', 
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 50}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['2'] = '1.04', ['3'] = '(3.495*Lv*Lv+37.126*Lv+134.953)*1.04', ['FuncName'] = 'AddHP_Player', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = true, 
            ['Velocity'] = 5000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860300101] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581483520'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact01.NS_FoolAttack_Impact01', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttackMissile_01.NS_FoolAttackMissile_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact01.NS_FoolAttack_Impact01', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Featherwit_Common_Hit_L', 
            ['ID'] = 860300101, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.75, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {130, 0, 60}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.27', ['2'] = '0.27', ['3'] = '0', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, ['3'] = {['1'] = {['FuncName'] = 'AddBuff', ['ID'] = 101, ['Level'] = '5', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_DestroyEffect'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0.2, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860300201] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581483776'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact01.NS_FoolAttack_Impact01', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttackMissile_01.NS_FoolAttackMissile_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact01.NS_FoolAttack_Impact01', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Featherwit_Common_Hit_L', 
            ['ID'] = 860300201, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.75, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {140, 0, 60}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.34', ['2'] = '0.34', ['3'] = '0', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, ['3'] = {['1'] = {['FuncName'] = 'AddBuff', ['ID'] = 101, ['Level'] = '5', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_DestroyEffect'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0.2, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860300301] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581484032'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact01.NS_FoolAttack_Impact01', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttackMissile_01.NS_FoolAttackMissile_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact01.NS_FoolAttack_Impact01', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Featherwit_Common_Hit_L', 
            ['ID'] = 860300301, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.75, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {130, 0, 60}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.45', ['2'] = '0.45', ['3'] = '0', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, ['3'] = {['1'] = {['FuncName'] = 'AddBuff', ['ID'] = 101, ['Level'] = '5', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_DestroyEffect'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0.2, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860300401] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581484288'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact02.NS_FoolAttack_Impact02', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttackMissile_02.NS_FoolAttackMissile_02', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Attack/AttackNew/NS_FoolAttack_Impact02.NS_FoolAttack_Impact02', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Featherwit_Common_Hit_H', 
            ['ID'] = 860300401, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.75, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {140, 0, 60}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.01', ['2'] = '1.01', ['3'] = '0', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, ['3'] = {['1'] = {['FuncName'] = 'AddBuff', ['ID'] = 101, ['Level'] = '5', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_DestroyEffect'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale_DestroyEffect'] = {2, 2, 2}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0.2, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860310201] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581484544'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Skill03New/New/NS_FoolSkill03_Top.NS_FoolSkill03_Top', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Featherwit_Common_Hit_L', 
            ['ID'] = 860310201, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.55, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {77, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0.2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Skill03New/NEW02/NS_FoolSkill03_Trail1.NS_FoolSkill03_Trail1', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860310202] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581484544'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 860310202, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.55, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860310301] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581485056'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Skill03New_New/NS_FoolSkill11_New2.NS_FoolSkill11_New2', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Featherwit_Skill11_AirBomb_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Fool_FX/Skill03New_New/NS_FoolSkill11_New3.NS_FoolSkill11_New3', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Featherwit_Common_Hit_L', 
            ['ID'] = 860310301, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 2, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {90, 0, 60}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.02', ['2'] = '1.02', ['3'] = '0', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 0}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860510301] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'root', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581486080'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 860510301, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, 0, 0}, 
            ['Offset_TrailEffect'] = {0, 0, 115}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {950, 0}, ['BasePointType'] = 'selfoffset', ['PosNum'] = 1, ['ShapeArgs'] = {1}, ['ShapeType'] = 'circle', }, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 4, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Apprentice_FX/Skill05/NS_ApprenticeSkill05_Tuowei.NS_ApprenticeSkill05_Tuowei', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 8000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860510302] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'root', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581486336'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 860510302, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, 0, 0}, 
            ['Offset_TrailEffect'] = {0, 0, 115}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {-400, 0}, ['BasePointType'] = 'tartoself', ['PosNum'] = 1, ['ShapeArgs'] = {1}, ['ShapeType'] = 'circle', }, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 4, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Apprentice_FX/Skill05/NS_ApprenticeSkill05_Tuowei.NS_ApprenticeSkill05_Tuowei', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 8000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [860600251] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581486848'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill11/NS_WarriorSkill11_Sword_Missle01.NS_WarriorSkill11_Sword_Missle01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Warrior_Skill11_SilverSword_Buff_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill11/NS_WarriorSkill11_Hit01.NS_WarriorSkill11_Hit01', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = 'Play_Warrior_Comon_Pike_Hit', 
            ['ID'] = 860600251, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = Game.TableDataManager:GetLangStr('str_8385118357760'),
            ['NewBulletDiscParam'] = 'OnHitAction_Desc,1,1,FuncValue', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['Offset_Effect'] = {-30, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.15', ['2'] = '0.15', ['3'] = '78*Lv*0.15', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [869000161] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581521408'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_PetrifiedSnake_002_FX/Attack/NS_Monster_PetrifledSnake_002_Track.NS_Monster_PetrifledSnake_002_Track', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_PetrifiedSnake_002_FX/Attack/NS_Monster_PetrifledSnake_002_Hit.NS_Monster_PetrifledSnake_002_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = '', 
            ['ID'] = 869000161, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {100, 0, 280}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [869000201] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_9072313124608'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_DefenseTower_FX/Attack02/NS_Monster_DefenseTower_Attack02_Track.NS_Monster_DefenseTower_Attack02_Track', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_DefenseTower_FX/Attack01/NS_Monster_DefenseTower_Attack01_Hit.NS_Monster_DefenseTower_Attack01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = '', 
            ['ID'] = 869000201, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880012021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581488384'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 880012021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '3', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880012031] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_l', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581488640'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 880012031, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '3', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880012261] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581488896'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 2500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880012261, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 6, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 2500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,2500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 99, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880022021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581489152'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 880022021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '3', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880022031] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_l', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581489408'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 880022031, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '3', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880022261] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581489664'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 2500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880022261, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 6, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 2500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,2500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 99, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880052011] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581491456'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_Boss_Amon_Attack_TrackHit.NS_Boss_Amon_Attack_TrackHit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_BossAmon_Attack_Track.NS_BossAmon_Attack_Track', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Amon_FX/Skill02/NS_AmonSkill02_Hit.NS_AmonSkill02_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Amon_Atk_Hit', 
            ['ID'] = 880052011, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.8', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880052081] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581491712'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Skill07/NS_Boss_Amon_Skill_07_Bullet_1.NS_Boss_Amon_Skill_07_Bullet_1', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880052081, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 150}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880052082] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581491968'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Skill07/NS_Boss_Amon_Skill_07_Bullet_1.NS_Boss_Amon_Skill_07_Bullet_1', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880052082, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 150}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880052083] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581492224'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Skill07/NS_Boss_Amon_Skill_07_Bullet_1.NS_Boss_Amon_Skill_07_Bullet_1', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880052083, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 150}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880052084] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581492480'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Skill07/NS_Boss_Amon_Skill_07_Bullet_1.NS_Boss_Amon_Skill_07_Bullet_1', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880052084, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 150}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880052085] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581492736'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Skill07/NS_Boss_Amon_Skill_07_Bullet_1.NS_Boss_Amon_Skill_07_Bullet_1', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880052085, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 150}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['FuncName'] = 'AddBuff', ['ID'] = 85005206, ['Level'] = '1', ['LifeTime'] = 30, ['Target'] = 'tar', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1800, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880054021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581493248'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Attack02/NS_Boss_Medici_Attack02_Bullet.NS_Boss_Medici_Attack02_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Medici_Atk02_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Attack02/NS_Boss_Medici_Attack_Hit.NS_Boss_Medici_Attack_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Medici_Atk_Hit', 
            ['ID'] = 880054021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 4000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880054041] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 200, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581493504'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Skill02/NS_Medici_Skill02_Bullet_02.NS_Medici_Skill02_Bullet_02', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 6000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Medici_Skill02_SpearBarrage_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Attack02/NS_Boss_Medici_Attack_Hit.NS_Boss_Medici_Attack_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Medici_Atk_Hit', 
            ['ID'] = 880054041, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 8, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 6000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,6000', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {1.7, 1.7, 1.7}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 99, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880055051] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 150, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581493760'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_Loop.NS_Boss_Milgonge_Skill03_Track_Loop', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 2500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Milgongen_Skill03_SilverStrand_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880055051, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 2500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,2500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 20, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880055052] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 150, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581494016'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_Loop.NS_Boss_Milgonge_Skill03_Track_Loop', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 2500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Milgongen_Skill03_SilverStrand_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880055052, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 2500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,2500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 20, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880055053] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 150, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581494272'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_Loop.NS_Boss_Milgonge_Skill03_Track_Loop', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 2500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Milgongen_Skill03_SilverStrand_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880055053, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 2500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,2500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 20, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880055054] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 125, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581494528'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_Loop.NS_Boss_Milgonge_Skill03_Track_Loop', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 2500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Milgongen_Skill03_SilverStrand_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880055054, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 2500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,2500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 20, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 5000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880056021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = false, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581494784'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Attack_02/NS_Sasriel_Attack_02_02.NS_Sasriel_Attack_02_02', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Attack_02/NS_Sasriel_Attack_02_03.NS_Sasriel_Attack_02_03', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Sasriel_Skill04_CorrosiveSea_Hit', 
            ['ID'] = 880056021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {370.4, 18.5, 86.6}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.25', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Attack_02/NS_Sasriel_Attack_02_04.NS_Sasriel_Attack_02_04', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1900, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880056051] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581495040'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Attack_02/NS_Sasriel_Attack_02_03.NS_Sasriel_Attack_02_03', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill07/NS_Boss_SasrielSkill07_Bullet.NS_Boss_SasrielSkill07_Bullet', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3500, 
            ['FadeAudioID'] = 'Play_Boss_Sasriel_Skill07_EnergyBall_Start', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Attack_02/NS_Sasriel_Attack_02_03.NS_Sasriel_Attack_02_03', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880056051, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 50, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 200, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880056111] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581495552'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill03/NS_Boss_Sasriel_Skill03_FireBall_04.NS_Boss_Sasriel_Skill03_FireBall_04', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Sasriel_Skill03_FallenFire_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880056111, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 20, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880056121] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581495808'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill04/NS_Boss_Sasriel_Skill04_Hit_01.NS_Boss_Sasriel_Skill04_Hit_01', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill04/NS_Boss_Sasriel_Skill04_03.NS_Boss_Sasriel_Skill04_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill04/NS_Boss_Sasriel_Skill04_Hit_01.NS_Boss_Sasriel_Skill04_Hit_01', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880056121, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['FuncName'] = 'AddBuff', ['ID'] = 84005604, ['Level'] = '1', ['Target'] = 'tar', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880056311] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581496064'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Attack_02/NS_Sasriel_Attack_02_03.NS_Sasriel_Attack_02_03', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Envrinment/ForsakenLand_GiantTing_P/GlowSphere/NS_ForsakenLand_GiantTing_GlowSphere_Loop.NS_ForsakenLand_GiantTing_GlowSphere_Loop', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3500, 
            ['FadeAudioID'] = 'Play_Boss_Sasriel_Skill07_EnergyBall_Start', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Attack_02/NS_Sasriel_Attack_02_03.NS_Sasriel_Attack_02_03', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880056311, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 50, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3500', 
            ['Offset'] = {0, 0, 0}, 
            ['Offset_Effect'] = {0, 0, -70}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {0.7, 0.7, 0.7}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 200, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880071061] = {
            ['Acceleration'] = 1500, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 8, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581522944'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill04/New_Materials/NS_BossSheriff_Skill04_Bullet_Start.NS_BossSheriff_Skill04_Bullet_Start', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Sheriff_Skill06_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880071061, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Parabola', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {-631, 7896, -339}, ['BasePointType'] = 'worldpos', ['PosNum'] = 12, ['ShapeArgs'] = {2300}, ['ShapeType'] = 'circle', }, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880071081] = {
            ['Acceleration'] = 3000, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 8, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581523200'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill06/NS_BossSheriff_Skill06_zidan.NS_BossSheriff_Skill06_zidan', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880071081, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Parabola', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880072021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 60, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581496320'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/skill12/Bullet/NS_Boss_ZJFR_Skill12_bullet.NS_Boss_ZJFR_Skill12_bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/Skill12/Bullet/NS_Boss_ZJFR_Skill12_bullet_Hit.NS_Boss_ZJFR_Skill12_bullet_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880072021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 20, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '4', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {1}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/skill12/Bullet/NS_Boss_ZJFR_Skill12_Hand_trail.NS_Boss_ZJFR_Skill12_Hand_trail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880072071] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 120, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 150, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581496576'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/Skill07/NS_Boss_ZJFR_Skill07_bullet_Hit.NS_Boss_ZJFR_Skill07_bullet_Hit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/Skill07/NS_Boss_ZJFR_Skill07_bullet.NS_Boss_ZJFR_Skill07_bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/Skill07/NS_Boss_ZJFR_Skill07_bullet_Hit.NS_Boss_ZJFR_Skill07_bullet_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880072071, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 20, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['FuncName'] = 'AddField', ['ID'] = 880072071, ['RotationLock'] = true, ['Target'] = 'tar', }, }, ['2'] = {['1'] = {['ByID'] = 88007207, ['FuncName'] = 'DelBuff', ['Layer'] = 99, ['TagNums'] = {1}, ['Target'] = 'tar', ['bAddedByMe'] = false, }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {1.3, 1.3, 1.3}, 
            ['Scale_DestroyEffect'] = {4, 4, 4}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 900, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880072221] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 60, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581497088'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/skill12/Bullet/NS_Boss_ZJFR_Skill12_bullet.NS_Boss_ZJFR_Skill12_bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/Skill12/Bullet/NS_Boss_ZJFR_Skill12_bullet_Hit.NS_Boss_ZJFR_Skill12_bullet_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880072221, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 20, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {1}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/skill12/Bullet/NS_Boss_ZJFR_Skill12_Hand_trail.NS_Boss_ZJFR_Skill12_Hand_trail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880072261] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 120, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 85, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581497344'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/ZhiNianTi/Bullet/NS_ZhiNianTi_Bullet_Hit1.NS_ZhiNianTi_Bullet_Hit1', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/ZhiNianTi/Bullet/NS_ZhiNianTi_Bullet.NS_ZhiNianTi_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/ZhiNianTi/Bullet/NS_ZhiNianTi_Bullet_Hit1.NS_ZhiNianTi_Bullet_Hit1', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880072261, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 20, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880072781] = {
            ['Acceleration'] = 2500, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 8, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 1, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581496832'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/Skill08/NS_Boss_ZJFR_Skill08_bullet_smoke.NS_Boss_ZJFR_Skill08_bullet_smoke', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/Skill08/NS_Boss_ZJFR_Skill08_bullet.NS_Boss_ZJFR_Skill08_bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880072781, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Parabola', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {}, ['BasePointType'] = 'self', ['PosNum'] = 1, ['ShapeArgs'] = {2000}, ['ShapeType'] = 'circle', }, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {0.3, 0.3, 0.3}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880075261] = {
            ['Acceleration'] = 200, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 9, 
            ['CollisionRadius'] = 150, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581525248'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill08/NS_Boss_YHXT01_Skill08_Hit.NS_Boss_YHXT01_Skill08_Hit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill08/NS_Boss_YHXT01_Skill08_Bullet.NS_Boss_YHXT01_Skill08_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 2, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_Hainasi_FX/Attack01/NS_Hainasi_Attack_Hit.NS_Hainasi_Attack_Hit', 
            ['HitEffectPlayMode'] = 2, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880075261, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'ParabolaWithTrack,300,3', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {1.5, 1.5, 1.5}, 
            ['ShakeScale'] = 1, 
            ['TargetPointMoveSpeed'] = 300, 
            ['TotalHitTime'] = 0, 
            ['TrackDuration'] = 3, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880080211] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 1, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7903813640448'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill02/NS_BOSS_SHGJ_Skill02_Imapct.NS_BOSS_SHGJ_Skill02_Imapct', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill02/NS_BOSS_SHGJ_Skill02_Attack.NS_BOSS_SHGJ_Skill02_Attack', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 10000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880080211, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 2, 
            ['MinVelocity'] = 150, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 10000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,10000', 
            ['Offset'] = {0, 0, 144}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 5000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880080601] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 150, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581523456'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_SHGJ/skill06/NS_SHGJ_stoneDD.NS_SHGJ_stoneDD', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Chamberlain_Skill_WXTY_Stone_Exp', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 2, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = 'Play_Boss_Chamberlain_Skill_WXTY_Hit', 
            ['ID'] = 880080601, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 2, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, 0, 1000}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880086520] = {
            ['Acceleration'] = 200, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 9, 
            ['CollisionRadius'] = 150, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581525504'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Common/Monument_Main_Darknight/NS_Dialogue_ConsecrationLevelUp_Night001.NS_Dialogue_ConsecrationLevelUp_Night001', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Steven_FX/Attack/NS_StevenAttack_Bullet.NS_StevenAttack_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 2, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Common/Monument_Main_Darknight/NS_Dialogue_ConsecrationLevelUp_Night001.NS_Dialogue_ConsecrationLevelUp_Night001', 
            ['HitEffectPlayMode'] = 2, 
            ['HitSoundPath'] = '', 
            ['ID'] = 880086520, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'ParabolaWithTrack,300,3', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {1.5, 1.5, 1.5}, 
            ['ShakeScale'] = 1, 
            ['TargetPointMoveSpeed'] = 300, 
            ['TotalHitTime'] = 0, 
            ['TrackDuration'] = 3, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [880096001] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581522176'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_VisionaryAttack_New_Hit.NS_VisionaryAttack_New_Hit', 
            ['HitEffectPlayMode'] = 2, 
            ['HitSoundPath'] = 'Play_Visionary_Common_Hit_L', 
            ['ID'] = 880096001, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 80}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Visionary_FX_New/Atk_01_02_03_New/NS_Visionary_Attack01_new_B.NS_Visionary_Attack01_new_B', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [881053371] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand01_l', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581525760'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 881053371, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [881053381] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand01_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581526016'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 881053381, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [881053391] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand01_l', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581526272'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 881053391, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '3', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [881053401] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand01_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581526528'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 881053401, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '3', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890001371] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581497600'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 890001371, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890001372] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_l', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581497856'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 890001372, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890002371] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581498112'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 890002371, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890002372] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_l', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581498368'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Impact.NS_Clown_Attack_01_Impact', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Joker_Attack_Hit', 
            ['ID'] = 890002372, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890002871] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581498624'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890002871, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890002872] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_l', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581498880'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Clown_FX_New/Attack01/NS_Clown_Attack_01_Missile.NS_Clown_Attack_01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890002872, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890010021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 120, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581505280'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/SchoolMentor/Merlin/Attack/NS_MerlinAttackMissile_01.NS_MerlinAttackMissile_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Thug_FX/Attack/NS_Thug_Attack01_Hit.NS_Thug_Attack01_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890010021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890010081] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 200, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581505536'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Skill02/NS_Medici_Skill02_Bullet_02.NS_Medici_Skill02_Bullet_02', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 6000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Medici_Skill02_SpearBarrage_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Attack02/NS_Boss_Medici_Attack_Hit.NS_Boss_Medici_Attack_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Boss_Medici_Atk_Hit', 
            ['ID'] = 890010081, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 8, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 6000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,6000', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {1.7, 1.7, 1.7}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 99, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890011441] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 60, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581503744'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Hit.NS_MoonwomanSkill01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_TreeWoman_Hit', 
            ['ID'] = 890011441, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890011501] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 60, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581504000'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Hit.NS_MoonwomanSkill01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_TreeWoman_Hit', 
            ['ID'] = 890011501, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 7, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890011511] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581504256'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3600, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Hit.NS_MoonwomanSkill01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_TreeWoman_Hit', 
            ['ID'] = 890011511, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3600, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3600', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890011601] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 5, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581504512'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_PlantMonster_001/Skill02/NS_Monster_PlantMonster_001_Skill02_Bullet.NS_Monster_PlantMonster_001_Skill02_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PlantMonster_FX/A_Monster_PM_Release/NS_Monster_Hit01.NS_Monster_Hit01', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_Plant_Hit', 
            ['ID'] = 890011601, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 50}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890011671] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 120, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581505792'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/SchoolMentor/Merlin/Attack/NS_MerlinAttackMissile_01.NS_MerlinAttackMissile_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Thug_FX/Attack/NS_Thug_Attack01_Hit.NS_Thug_Attack01_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890011671, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890011701] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581504768'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_Loop.NS_Boss_Milgonge_Skill03_Track_Loop', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 4000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_Milgongen_Skill03_SilverStrand_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill03/NS_Boss_Milgonge_Skill03_Track_End.NS_Boss_Milgonge_Skill03_Track_End', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890011701, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 2, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 4000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,4000', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 20, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013051] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581507072'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_hit.NS_FHYZ_skill02_hit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_attack_01.NS_FHYZ_skill02_attack_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_FHYZ_Skill_Flower_Birth_Loop', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_hit.NS_FHYZ_skill02_hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013051, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, 300, 300}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {-100, 0}, ['BasePointType'] = 'tartoself', ['PosNum'] = 1, }, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_attack_01_trail.NS_FHYZ_skill02_attack_01_trail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 1, 
            ['WaterWaveRadius'] = 30, 
        },
        [890013052] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581507328'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_hit.NS_FHYZ_skill02_hit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_attack_01.NS_FHYZ_skill02_attack_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_FHYZ_Skill_Flower_Birth_Loop', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_hit.NS_FHYZ_skill02_hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013052, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, 0, 400}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {-100, 0}, ['BasePointType'] = 'tartoself', ['PosNum'] = 1, }, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_attack_01_trail.NS_FHYZ_skill02_attack_01_trail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 1, 
            ['WaterWaveRadius'] = 30, 
        },
        [890013053] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581507584'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_hit.NS_FHYZ_skill02_hit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_attack_01.NS_FHYZ_skill02_attack_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_FHYZ_Skill_Flower_Birth_Loop', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_hit.NS_FHYZ_skill02_hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013053, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, -300, 300}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {-100, 0}, ['BasePointType'] = 'tartoself', ['PosNum'] = 1, }, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_attack_01_trail.NS_FHYZ_skill02_attack_01_trail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 1, 
            ['WaterWaveRadius'] = 30, 
        },
        [890013054] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581507840'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_active.NS_FHYZ_skill02_active', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_FHYZ_Skill_Flower_Shoot', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013054, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 2, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1000', 
            ['Offset'] = {0, 300, 300}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013055] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581508096'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_active.NS_FHYZ_skill02_active', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_FHYZ_Skill_Flower_Shoot', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013055, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 2, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1000', 
            ['Offset'] = {0, 0, 400}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013056] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581508352'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill02/NS_FHYZ_skill02_active.NS_FHYZ_skill02_active', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Boss_FHYZ_Skill_Flower_Shoot', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013056, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 2, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1000', 
            ['Offset'] = {0, -300, 300}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013081] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581508608'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_03.NS_Base_Combat_PollutionSkill_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Common_Skill_Pollute_Flower_Shoot', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013081, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 700}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013082] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581508608'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_03.NS_Base_Combat_PollutionSkill_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Common_Skill_Pollute_Flower_Shoot', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013082, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 700}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013083] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581508608'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_03.NS_Base_Combat_PollutionSkill_03', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Common_Skill_Pollute_Flower_Shoot', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013083, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 700}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013091] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581509376'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Common_Skill_Pollute_Flower_Shoot', 
            ['FlashWhite'] = true, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013091, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {-1100, 0, 700}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.065', ['FuncName'] = 'ReduceHP_per', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, ['3'] = {['1'] = {['ChangeValue'] = 1000, ['FuncName'] = 'BreakDefense', ['Target'] = 'tar', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_03.NS_Base_Combat_PollutionSkill_03', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013092] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581509632'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Common_Skill_Pollute_Flower_Shoot', 
            ['FlashWhite'] = true, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013092, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {-450, -750, 350}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.065', ['FuncName'] = 'ReduceHP_per', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, ['3'] = {['1'] = {['ChangeValue'] = 1000, ['FuncName'] = 'BreakDefense', ['Target'] = 'tar', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_03.NS_Base_Combat_PollutionSkill_03', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013093] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581509888'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Common_Skill_Pollute_Flower_Shoot', 
            ['FlashWhite'] = true, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_04.NS_Base_Combat_PollutionSkill_04', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890013093, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {-430, 640, 270}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0.065', ['FuncName'] = 'ReduceHP_per', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, ['3'] = {['1'] = {['ChangeValue'] = 1000, ['FuncName'] = 'BreakDefense', ['Target'] = 'tar', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_TrailEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 2, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/PollutionSkill_FX/NS_Base_Combat_PollutionSkill_03.NS_Base_Combat_PollutionSkill_03', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013601] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581510144'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Shooter_FX/Attack/NS_Shooter_Attack01_Fly.NS_Shooter_Attack01_Fly', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/MeiMeng_Duun/Attack_01/NS_MeiMeng_Duun_Attack_01_03.NS_MeiMeng_Duun_Attack_01_03', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Transform_Dunn_Hit', 
            ['ID'] = 890013601, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {115, 0, 57}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['2'] = '0', ['3'] = '200', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 4000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013611] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581510400'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Shooter_FX/Attack/NS_Shooter_Attack01_Fly.NS_Shooter_Attack01_Fly', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/MeiMeng_Duun/Attack_01/NS_MeiMeng_Duun_Attack_01_03.NS_MeiMeng_Duun_Attack_01_03', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Transform_Dunn_Hit', 
            ['ID'] = 890013611, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {115, 0, 57}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['2'] = '0', ['3'] = '250', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 4000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890013621] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581510656'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Shooter_FX/Attack/NS_Shooter_Attack01_Fly.NS_Shooter_Attack01_Fly', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/MeiMeng_Duun/Attack_01/NS_MeiMeng_Duun_Attack_01_03.NS_MeiMeng_Duun_Attack_01_03', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Transform_Dunn_Hit', 
            ['ID'] = 890013621, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {115, 0, 57}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['2'] = '0', ['3'] = '500', ['FuncName'] = 'PhyAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 4000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890015181] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581512192'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_Redhat_Priest_FX/Attack01/NS_Monster_RedHat_Priest_AttackTrack.NS_Monster_RedHat_Priest_AttackTrack', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890015181, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 0}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890015211] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581512448'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890015211, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890017030] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581522432'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Shizhi/Skill04/NS_Monster_Shizhi_Skill_04_bullte.NS_Monster_Shizhi_Skill_04_bullte', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Shizhi/Skill04/Ns_Monster_Shizhi_Skill_04_poison_Hit.Ns_Monster_Shizhi_Skill_04_poison_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_TheDemented_Hit', 
            ['ID'] = 890017030, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 100}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890017200] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581522688'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_NanPu/Skill01/NS_NanPu_Skill01_Hit.NS_NanPu_Skill01_Hit', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Monster_NanPu_Skill_FingerSnapBolt_Emit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_NanPu/Skill01/NS_NanPu_Skill01_Hit.NS_NanPu_Skill01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_NanPu_Attack_Hit', 
            ['ID'] = 890017200, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_NanPu/Skill01/NS_NanPu_Skill01_Bullet.NS_NanPu_Skill01_Bullet', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890017701] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581524480'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_SaiLinNa_FX/Skill05/NS_Monster_SaiLinNa_Skill05_MirrorBulletHit.NS_Monster_SaiLinNa_Skill05_MirrorBulletHit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_SaiLinNa_FX/Skill05/NS_Monster_SaiLinNa_Skill05_MirrorBullet.NS_Monster_SaiLinNa_Skill05_MirrorBullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890017701, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, 0, -100}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.0', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890018001] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581524736'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_Hainasi_FX/Attack01/NS_Hainasi_Attack_Bullet.NS_Hainasi_Attack_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_Hainasi_FX/Attack01/NS_Hainasi_Attack_Hit.NS_Hainasi_Attack_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890018001, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890018041] = {
            ['Acceleration'] = 2000, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 8, 
            ['CollisionRadius'] = 50, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581524992'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_Hainasi_FX/Skill02/NS_Monster_Hainasi_Skill02_Bullet.NS_Monster_Hainasi_Skill02_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_Hainasi_FX/Attack01/NS_Hainasi_Attack_Hit.NS_Hainasi_Attack_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890018041, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Parabola', 
            ['Offset'] = {0, 0, 200}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {['BasePointArgs'] = {1}, ['BasePointType'] = 'poslist', ['PosNum'] = 1, }, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_Hainasi_FX/Skill02/NS_Monster_Hainasi_Skill02_Bullet_Trail.NS_Monster_Hainasi_Skill02_Bullet_Trail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 0, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020051] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8865617892096'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/New/NS_WizardAttack_Fly.NS_WizardAttack_Fly', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020051, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020071] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581513216'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonfish_FX/Attack/NS_MoonFish_Attack_Missile.NS_MoonFish_Attack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020071, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020081] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 100, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581513472'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonfish_FX/Skill01/NS_MoonFishWave.NS_MoonFishWave', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020081, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3000', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 12, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020091] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581513728'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boomber_FX/Skill02/NS_Boomber_skill02_Missile.NS_Boomber_skill02_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020091, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020101] = {
            ['Acceleration'] = 780, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 8, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581513984'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boomber_FX/Skill02/NS_Boomber_skill02_Missile.NS_Boomber_skill02_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020101, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = true, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 2, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Parabola', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['FuncName'] = 'CreateField', ['ID'] = 890020102, ['MaxDistance'] = 10000, ['Pos'] = {['BasePointArgs'] = {}, ['BasePointType'] = 'self', ['PosNum'] = 1, }, }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, 90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 850, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020450] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581515008'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020450, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020451] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 2, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0.5, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581515264'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020451, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Line', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020452] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581515520'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3000, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020452, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3000, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3000', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020453] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'Bone_Jaw', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581515776'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Shizhi/Skill04/NS_Monster_Shizhi_Skill_04_bullte.NS_Monster_Shizhi_Skill_04_bullte', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020453, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {10, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['FuncName'] = 'CreateField', ['ID'] = 890020822, ['MaxDistance'] = 10000, ['Pos'] = {['BasePointArgs'] = {}, ['BasePointType'] = 'self', ['PosNum'] = 1, }, }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020454] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8865617896192'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/PlantMonster_FX/A_Monster_PM_Release/NS_Monster_PM_Attack_Track.NS_Monster_PM_Attack_Track', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020454, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/PlantMonster_FX/A_Monster_PM_Release/NS_Monster_PM_Attack_CastTrail.NS_Monster_PM_Attack_CastTrail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020455] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8865617897472'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boomber_FX/Skill02/NS_Boomber_skill02_Missile.NS_Boomber_skill02_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020455, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 3000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890020456] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581516544'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 0, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890020456, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1.5', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890021031] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = false, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581518080'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Eudora_FX/Attack03/NS_Boss_Eudora_Attack03_Track.NS_Boss_Eudora_Attack03_Track', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = 'Play_Boss_Eudora_Attack03_Shoot_Hit', 
            ['ID'] = 890021031, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 7, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 200}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {1.5, 1.5, 1.5}, 
            ['Scale_TrailEffect'] = {1.5, 1.5, 1.5}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Eudora_FX/Attack03/NS_Boss_Eudora_Attack03_TrackTrail.NS_Boss_Eudora_Attack03_TrackTrail', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890022111] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581518336'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Sun_FX/Skill08New/NS_Sunskill08_02.NS_Sunskill08_02', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Sun_FX/Skill08New/NS_Sunskill08_01.NS_Sunskill08_01', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Sun_FX/Skill08New/NS_Sunskill08_02.NS_Sunskill08_02', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890022111, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {0.2, 0.2, 0.2}, 
            ['Scale_DestroyEffect'] = {0.2, 0.2, 0.2}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890022171] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581518592'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/New/NS_WizardAttack_Fly.NS_WizardAttack_Fly', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890022171, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2500, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890022191] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 30, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581518848'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Wizard_FX/Attack/New/NS_WizardAttack_Fly.NS_WizardAttack_Fly', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 500, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890022191, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 0.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 500, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,500', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890022221] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581519104'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_ClockBlade/Attack/NS_Monster_ClockBlade_Hit.NS_Monster_ClockBlade_Hit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_ClockBlade/Attack/NS_Monster_ClockBlade_Attack02.NS_Monster_ClockBlade_Attack02', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_ClockBlade/Attack/NS_Monster_ClockBlade_Hit.NS_Monster_ClockBlade_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890022221, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 10, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_DestroyEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890022271] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581519360'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_ClockBlade/Bullet/NS_Monster_ClockBlade_Bullet01_Hit.NS_Monster_ClockBlade_Bullet01_Hit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_ClockBlade/Bullet/NS_Monster_ClockBlade_Bullet01.NS_Monster_ClockBlade_Bullet01', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 1800, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_ClockBlade/Bullet/NS_Monster_ClockBlade_Bullet01_Hit.NS_Monster_ClockBlade_Bullet01_Hit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890022271, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 1, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 1800, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,1800', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '1', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['Rotation_DestroyEffect'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1700, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890022281] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 10, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581519616'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/CinematicsFX/XUZHANG_05_HSJP/NS_XuZhang_05_HSJP_Trail_Hand.NS_XuZhang_05_HSJP_Trail_Hand', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890022281, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {}, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {5, 5, 5}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890023021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581520896'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_Boss_Amon_Attack_TrackHit.NS_Boss_Amon_Attack_TrackHit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_BossAmon_Attack_Track.NS_BossAmon_Attack_Track', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_Boss_Amon_Attack_TrackHit.NS_Boss_Amon_Attack_TrackHit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890023021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '40', ['FuncName'] = 'MagAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890023022] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = false, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581521152'),
            ['DestroyEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_Boss_Amon_Attack_TrackHit.NS_Boss_Amon_Attack_TrackHit', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_BossAmon_Attack_Track.NS_BossAmon_Attack_Track', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Boss_Amon/Attack/NS_Boss_Amon_Attack_TrackHit.NS_Boss_Amon_Attack_TrackHit', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 890023022, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '38', ['FuncName'] = 'MagAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {3, 3, 3}, 
            ['Scale_TrailEffect'] = {1.5, 1.5, 1.5}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890033021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 5, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581523968'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_PlantMonster_001/Skill02/NS_Monster_PlantMonster_001_Skill02_Bullet.NS_Monster_PlantMonster_001_Skill02_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PlantMonster_FX/A_Monster_PM_Release/NS_Monster_Hit01.NS_Monster_Hit01', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_Plant_Hit', 
            ['ID'] = 890033021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 50}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [890033261] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 5, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581524224'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Monster_PlantMonster_001/Skill02/NS_Monster_PlantMonster_001_Skill02_Bullet.NS_Monster_PlantMonster_001_Skill02_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 1, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/PlantMonster_FX/A_Monster_PM_Release/NS_Monster_Hit01.NS_Monster_Hit01', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_Plant_Hit', 
            ['ID'] = 890033261, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 50}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 0, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [891032021] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8865617904640'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Monster_TreeWoman_Hit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Hit.NS_MoonwomanSkill01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_TreeWoman_Hit', 
            ['ID'] = 891032021, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 3.5, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [891032022] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 1, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581520128'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['ExtraParam1'] = 3600, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Monster_TreeWoman_Hit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '', 
            ['HitEffectPlayMode'] = 0, 
            ['HitSoundPath'] = '', 
            ['ID'] = 891032022, 
            ['IsHomingTarget'] = false, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = false, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['MoveDist'] = 3600, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Direction,3600', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 1000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [891032023] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = true, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 3, 
            ['CollisionRadius'] = 20, 
            ['DeathInherit'] = false, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581520384'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Missile.NS_MoonwomanSkill01_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = 'Play_Monster_TreeWoman_Hit', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Moonwoman_FX/Skill01/NS_MoonwomanSkill01_Hit.NS_MoonwomanSkill01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_TreeWoman_Hit', 
            ['ID'] = 891032023, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = false, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 7, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'Missile', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '2', ['FuncName'] = 'PhyAtk', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [891033311] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = '', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581510912'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/MeiMeng_Dally/Attack01/NS_MeiMeng_Attack01_Bullet.NS_MeiMeng_Attack01_Bullet', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/MeiMeng_Dally/Attack01/NS_MeiMeng_Attack01_Hit.NS_MeiMeng_Attack01_Hit', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Transform_Dally_Attack1_Hit', 
            ['ID'] = 891033311, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {77, 0, 45}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['2'] = '0', ['3'] = '300', ['FuncName'] = 'MagAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
        [891033351] = {
            ['Acceleration'] = 0, 
            ['AngularVelocity'] = 0, 
            ['BFollowScale'] = false, 
            ['BFollowScale_DestroyEffect'] = true, 
            ['BFollowScale_HitEffect'] = true, 
            ['BFollowScale_TrailEffect'] = true, 
            ['Bone'] = 'hand_r', 
            ['BulletTypeInt'] = 4, 
            ['CollisionRadius'] = 0, 
            ['DeathInherit'] = true, 
            ['DelayDestroy'] = 0, 
            ['DelayTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8384581511168'),
            ['DestroyEffectPath'] = '', 
            ['EffectPath'] = '/Game/Arts/Effects/FX_Character/Spectre_FX/Attack/NS_SpectreAttack_Missile.NS_SpectreAttack_Missile', 
            ['EffectPriority'] = 2, 
            ['FadeAudioID'] = '', 
            ['FireAudioID'] = '', 
            ['FlashWhite'] = false, 
            ['FollowType'] = 1, 
            ['FollowType_TrailEffect'] = 0, 
            ['HitEffectPath'] = '/Game/Arts/Effects/FX_Character/Spectre_FX/Attack/NS_SpectreAttack_Burst.NS_SpectreAttack_Burst', 
            ['HitEffectPlayMode'] = 1, 
            ['HitSoundPath'] = 'Play_Monster_youying_Hit', 
            ['ID'] = 891033351, 
            ['IsHomingTarget'] = true, 
            ['IsMustHit'] = true, 
            ['IsNeedTarget'] = true, 
            ['IsParabola'] = false, 
            ['LauncherType'] = 0, 
            ['MaxLevel'] = 40, 
            ['MaxLifeTime'] = 4, 
            ['MinVelocity'] = 0, 
            ['Model'] = '', 
            ['MotionType'] = 0, 
            ['Name'] = '', 
            ['NewBulletDisc'] = '', 
            ['NewBulletDiscParam'] = '', 
            ['NewBulletType'] = 'MissileNoMiss', 
            ['Offset'] = {0, 0, 0}, 
            ['OnHitAction_Desc'] = {['1'] = {['1'] = {['1'] = '0', ['2'] = '0', ['3'] = '600', ['FuncName'] = 'MagAtk_Player', }, }, ['2'] = {['1'] = {['FuncName'] = 'HitAction', ['HitType'] = 'LightHit', }, }, }, 
            ['OnHitFxSoundRotation'] = {}, 
            ['OnHitShake'] = '', 
            ['PositionSelectionRule'] = {}, 
            ['Rotation'] = {0, 0, -90}, 
            ['RoundCenterCoordinates'] = {}, 
            ['RoundStartCoordinates'] = {}, 
            ['Scale'] = {0.3, 0.3, 0.3}, 
            ['ShakeScale'] = 1, 
            ['TotalHitTime'] = 1, 
            ['TrailEffectDelayDestroyTime'] = 0, 
            ['TrailEffectPath'] = '', 
            ['UseMaxLifeTimeAsHitTime'] = false, 
            ['Velocity'] = 2000, 
            ['WaterWaveParamID'] = 0, 
            ['WaterWaveRadius'] = 0, 
        },
    }
}
return TopData