--
-- 表名: $Talent_天赋系统.xlsx  页名：$TalentTree_天赋树
--

local TopData = {
	data = {
		[101] = {
			["Key"] = 101,
			["Profession"] = 1200001,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession01.UI_Character_Img_Profession01",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession01_VX.UI_Character_Img_Profession01_VX",
			["Name"] = "",
			["Tag"] = "",
			["Description"] = "",
		},
		[102] = {
			["Key"] = 102,
			["Profession"] = 1200001,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession01.UI_Character_Img_Profession01",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession01_VX.UI_Character_Img_Profession01_VX",
			["Name"] = "",
			["Tag"] = "",
			["Description"] = "",
		},
		[201] = {
			["Key"] = 201,
			["Profession"] = 1200002,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession02.UI_Character_Img_Profession02",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession02_VX.UI_Character_Img_Profession02_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618869248'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887304704'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155740160'),
		},
		[202] = {
			["Key"] = 202,
			["Profession"] = 1200002,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession02.UI_Character_Img_Profession02",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession02_VX.UI_Character_Img_Profession02_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618869504'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887304960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155740416'),
		},
		[301] = {
			["Key"] = 301,
			["Profession"] = 1200003,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession04.UI_Character_Img_Profession04",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession04_VX.UI_Character_Img_Profession04_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618869760'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887304960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155740416'),
		},
		[302] = {
			["Key"] = 302,
			["Profession"] = 1200003,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession04.UI_Character_Img_Profession04",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession04_VX.UI_Character_Img_Profession04_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618870016'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887305472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155740928'),
		},
		[401] = {
			["Key"] = 401,
			["Profession"] = 1200004,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession06.UI_Character_Img_Profession06",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession06_VX.UI_Character_Img_Profession06_VX",
			["Name"] = "",
			["Tag"] = "",
			["Description"] = "",
		},
		[402] = {
			["Key"] = 402,
			["Profession"] = 1200004,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession06.UI_Character_Img_Profession06",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession06_VX.UI_Character_Img_Profession06_VX",
			["Name"] = "",
			["Tag"] = "",
			["Description"] = "",
		},
		[501] = {
			["Key"] = 501,
			["Profession"] = 1200005,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession03.UI_Character_Img_Profession03",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession03_VX.UI_Character_Img_Profession03_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618870784'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887306240'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155741696'),
		},
		[502] = {
			["Key"] = 502,
			["Profession"] = 1200005,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession03.UI_Character_Img_Profession03",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession03_VX.UI_Character_Img_Profession03_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618871040'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887306496'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155741952'),
		},
		[601] = {
			["Key"] = 601,
			["Profession"] = 1200006,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession05.UI_Character_Img_Profession05",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession05_VX.UI_Character_Img_Profession05_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618871296'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887304960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155740416'),
		},
		[602] = {
			["Key"] = 602,
			["Profession"] = 1200006,
			["BackgroundIMG"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/ElementCore/UI_Character_Img_Profession05.UI_Character_Img_Profession05",
			["BackgroundLine"] = "/Game/Arts/UI_2/Resource/ElementCore/VX/UI_Character_Img_Profession05_VX.UI_Character_Img_Profession05_VX",
			["Name"] = Game.TableDataManager:GetLangStr('str_55183618871552'),
			["Tag"] = Game.TableDataManager:GetLangStr('str_55183887305472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_55184155740928'),
		},
	},
}

return TopData
