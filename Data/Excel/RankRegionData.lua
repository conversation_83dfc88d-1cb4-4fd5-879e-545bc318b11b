--
-- 表名: RankRegionData后处理
--

local TopData = {
    RankCityIndex = {
        ['七台河'] = 251, 
        ['万宁'] = 7, 
        ['三亚'] = 20, 
        ['三明'] = 260, 
        ['三沙'] = 311, 
        ['三门峡'] = 350, 
        ['上海'] = 258, 
        ['上饶'] = 318, 
        ['东方'] = 343, 
        ['东莞'] = 238, 
        ['东营'] = 154, 
        ['中卫'] = 101, 
        ['中山'] = 47, 
        ['临夏回族自治州'] = 314, 
        ['临汾'] = 297, 
        ['临沂'] = 46, 
        ['临沧'] = 132, 
        ['临高县'] = 296, 
        ['丹东'] = 299, 
        ['丽水'] = 36, 
        ['丽江'] = 56, 
        ['乌兰察布'] = 225, 
        ['乌海'] = 138, 
        ['乌鲁木齐'] = 348, 
        ['乐东黎族自治县'] = 113, 
        ['乐山'] = 64, 
        ['九江'] = 236, 
        ['云林县'] = 45, 
        ['云浮'] = 51, 
        ['五家渠'] = 43, 
        ['五指山'] = 292, 
        ['亳州'] = 359, 
        ['仙桃'] = 273, 
        ['伊春'] = 228, 
        ['伊犁哈萨克自治州'] = 176, 
        ['佛山'] = 14, 
        ['佳木斯'] = 317, 
        ['保亭黎族苗族自治县'] = 301, 
        ['保定'] = 246, 
        ['保山'] = 197, 
        ['信阳'] = 173, 
        ['儋州'] = 300, 
        ['克孜勒苏柯尔克孜自治州'] = 293, 
        ['克拉玛依'] = 33, 
        ['六安'] = 204, 
        ['六盘水'] = 81, 
        ['兰州'] = 26, 
        ['兴安盟'] = 298, 
        ['内江'] = 326, 
        ['凉山彝族自治州'] = 272, 
        ['包头'] = 96, 
        ['北京'] = 320, 
        ['北屯'] = 128, 
        ['北海'] = 349, 
        ['十堰'] = 180, 
        ['南京'] = 284, 
        ['南充'] = 13, 
        ['南宁'] = 72, 
        ['南平'] = 127, 
        ['南投县'] = 97, 
        ['南昌'] = 366, 
        ['南通'] = 114, 
        ['南阳'] = 362, 
        ['博尔塔拉蒙古自治州'] = 6, 
        ['厦门'] = 53, 
        ['双河'] = 357, 
        ['双鸭山'] = 312, 
        ['可克达拉'] = 105, 
        ['台东县'] = 200, 
        ['台中市'] = 59, 
        ['台北市'] = 305, 
        ['台南市'] = 255, 
        ['台州'] = 280, 
        ['合肥'] = 365, 
        ['吉安'] = 230, 
        ['吉林市'] = 386, 
        ['吐鲁番'] = 218, 
        ['吕梁'] = 42, 
        ['吴忠'] = 355, 
        ['周口'] = 160, 
        ['呼伦贝尔'] = 264, 
        ['呼和浩特'] = 147, 
        ['和田地区'] = 254, 
        ['咸宁'] = 16, 
        ['咸阳'] = 124, 
        ['哈密'] = 257, 
        ['哈尔滨'] = 9, 
        ['唐山'] = 354, 
        ['商丘'] = 40, 
        ['商洛'] = 177, 
        ['喀什地区'] = 214, 
        ['嘉义县'] = 291, 
        ['嘉义市'] = 316, 
        ['嘉兴'] = 193, 
        ['嘉峪关'] = 148, 
        ['四平'] = 95, 
        ['固原'] = 275, 
        ['图木舒克'] = 277, 
        ['基隆市'] = 126, 
        ['塔城地区'] = 84, 
        ['大兴安岭地区'] = 285, 
        ['大同'] = 17, 
        ['大庆'] = 353, 
        ['大理白族自治州'] = 276, 
        ['大连'] = 166, 
        ['天水'] = 213, 
        ['天津'] = 189, 
        ['天门'] = 164, 
        ['太原'] = 283, 
        ['威海'] = 219, 
        ['娄底'] = 165, 
        ['孝感'] = 119, 
        ['宁德'] = 309, 
        ['宁波'] = 338, 
        ['安庆'] = 91, 
        ['安康'] = 370, 
        ['安阳'] = 211, 
        ['安顺'] = 79, 
        ['定安县'] = 279, 
        ['定西'] = 70, 
        ['宜兰县'] = 58, 
        ['宜宾'] = 368, 
        ['宜昌'] = 199, 
        ['宜春'] = 382, 
        ['宝鸡'] = 98, 
        ['宣城'] = 87, 
        ['宿州'] = 376, 
        ['宿迁'] = 66, 
        ['屏东县'] = 60, 
        ['屯昌县'] = 28, 
        ['山南'] = 38, 
        ['岳阳'] = 224, 
        ['崇左'] = 78, 
        ['巴中'] = 12, 
        ['巴彦淖尔'] = 121, 
        ['巴音郭楞蒙古自治州'] = 22, 
        ['常州'] = 226, 
        ['常德'] = 133, 
        ['平凉'] = 151, 
        ['平顶山'] = 80, 
        ['广元'] = 107, 
        ['广安'] = 108, 
        ['广州'] = 323, 
        ['庆阳'] = 308, 
        ['廊坊'] = 99, 
        ['延安'] = 18, 
        ['延边朝鲜族自治州'] = 194, 
        ['开封'] = 158, 
        ['张家口'] = 192, 
        ['张家界'] = 267, 
        ['张掖'] = 342, 
        ['彰化县'] = 3, 
        ['徐州'] = 361, 
        ['德宏傣族景颇族自治州'] = 19, 
        ['德州'] = 123, 
        ['德阳'] = 351, 
        ['忻州'] = 15, 
        ['怀化'] = 198, 
        ['怒江傈僳族自治州'] = 217, 
        ['恩施土家族苗族自治州'] = 163, 
        ['惠州'] = 67, 
        ['成都'] = 61, 
        ['扬州'] = 32, 
        ['承德'] = 242, 
        ['抚州'] = 231, 
        ['抚顺'] = 290, 
        ['拉萨'] = 115, 
        ['揭阳'] = 259, 
        ['攀枝花'] = 5, 
        ['文山壮族苗族自治州'] = 89, 
        ['文昌'] = 340, 
        ['新乡'] = 104, 
        ['新余'] = 201, 
        ['新北市'] = 146, 
        ['新竹县'] = 322, 
        ['新竹市'] = 244, 
        ['无锡'] = 2, 
        ['日喀则'] = 131, 
        ['日照'] = 223, 
        ['昆明'] = 346, 
        ['昆玉'] = 30, 
        ['昌吉回族自治州'] = 167, 
        ['昌江黎族自治县'] = 212, 
        ['昌都'] = 29, 
        ['昭通'] = 157, 
        ['晋中'] = 245, 
        ['晋城'] = 155, 
        ['普洱'] = 35, 
        ['景德镇'] = 248, 
        ['曲靖'] = 347, 
        ['朔州'] = 129, 
        ['朝阳'] = 102, 
        ['本溪'] = 303, 
        ['来宾'] = 34, 
        ['杭州'] = 110, 
        ['松原'] = 306, 
        ['林芝'] = 1, 
        ['果洛藏族自治州'] = 137, 
        ['枣庄'] = 380, 
        ['柳州'] = 387, 
        ['株洲'] = 263, 
        ['桂林'] = 141, 
        ['桃园市'] = 179, 
        ['梅州'] = 134, 
        ['梧州'] = 243, 
        ['楚雄彝族自治州'] = 27, 
        ['榆林'] = 184, 
        ['武威'] = 336, 
        ['武汉'] = 321, 
        ['毕节'] = 256, 
        ['永州'] = 122, 
        ['汉中'] = 207, 
        ['汕头'] = 86, 
        ['汕尾'] = 221, 
        ['江门'] = 324, 
        ['池州'] = 220, 
        ['沈阳'] = 278, 
        ['沧州'] = 241, 
        ['河池'] = 287, 
        ['河源'] = 191, 
        ['泉州'] = 100, 
        ['泰安'] = 371, 
        ['泰州'] = 75, 
        ['泸州'] = 187, 
        ['洛阳'] = 54, 
        ['济南'] = 315, 
        ['济宁'] = 169, 
        ['济源'] = 232, 
        ['海东'] = 205, 
        ['海北藏族自治州'] = 313, 
        ['海南藏族自治州'] = 334, 
        ['海口'] = 270, 
        ['海西蒙古族藏族自治州'] = 337, 
        ['淄博'] = 294, 
        ['淮北'] = 240, 
        ['淮南'] = 235, 
        ['淮安'] = 234, 
        ['深圳'] = 249, 
        ['清远'] = 227, 
        ['温州'] = 117, 
        ['渭南'] = 171, 
        ['湖州'] = 116, 
        ['湘潭'] = 11, 
        ['湘西土家族苗族自治州'] = 237, 
        ['湛江'] = 170, 
        ['滁州'] = 52, 
        ['滨州'] = 282, 
        ['漯河'] = 139, 
        ['漳州'] = 156, 
        ['潍坊'] = 135, 
        ['潜江'] = 73, 
        ['潮州'] = 385, 
        ['澄迈县'] = 21, 
        ['澎湖县'] = 302, 
        ['濮阳'] = 281, 
        ['烟台'] = 203, 
        ['焦作'] = 339, 
        ['牡丹江'] = 208, 
        ['玉林'] = 215, 
        ['玉树藏族自治州'] = 71, 
        ['玉溪'] = 289, 
        ['珠海'] = 252, 
        ['琼中黎族苗族自治县'] = 130, 
        ['琼海'] = 381, 
        ['甘南藏族自治州'] = 377, 
        ['甘孜藏族自治州'] = 367, 
        ['白城'] = 229, 
        ['白山'] = 149, 
        ['白沙黎族自治县'] = 125, 
        ['白银'] = 111, 
        ['百色'] = 159, 
        ['益阳'] = 369, 
        ['盐城'] = 265, 
        ['盘锦'] = 183, 
        ['眉山'] = 144, 
        ['石嘴山'] = 57, 
        ['石家庄'] = 41, 
        ['石河子'] = 175, 
        ['神农架林区'] = 112, 
        ['福州'] = 319, 
        ['秦皇岛'] = 93, 
        ['红河哈尼族彝族自治州'] = 335, 
        ['绍兴'] = 181, 
        ['绥化'] = 185, 
        ['绵阳'] = 186, 
        ['聊城'] = 76, 
        ['肇庆'] = 332, 
        ['自贡'] = 307, 
        ['舟山'] = 31, 
        ['芜湖'] = 327, 
        ['花莲县'] = 206, 
        ['苏州'] = 37, 
        ['苗栗县'] = 190, 
        ['茂名'] = 360, 
        ['荆州'] = 330, 
        ['荆门'] = 92, 
        ['莆田'] = 172, 
        ['菏泽'] = 103, 
        ['萍乡'] = 384, 
        ['营口'] = 383, 
        ['葫芦岛'] = 364, 
        ['蚌埠'] = 188, 
        ['衡水'] = 378, 
        ['衡阳'] = 261, 
        ['衢州'] = 333, 
        ['襄阳'] = 49, 
        ['西双版纳傣族自治州'] = 345, 
        ['西宁'] = 379, 
        ['西安'] = 142, 
        ['许昌'] = 196, 
        ['贵港'] = 69, 
        ['贵阳'] = 162, 
        ['贺州'] = 94, 
        ['资阳'] = 222, 
        ['赣州'] = 195, 
        ['赤峰'] = 55, 
        ['辽源'] = 150, 
        ['辽阳'] = 65, 
        ['达州'] = 274, 
        ['运城'] = 363, 
        ['连云港'] = 233, 
        ['连江县'] = 23, 
        ['迪庆藏族自治州'] = 352, 
        ['通化'] = 239, 
        ['通辽'] = 85, 
        ['遂宁'] = 120, 
        ['遵义'] = 358, 
        ['邢台'] = 178, 
        ['那曲'] = 374, 
        ['邯郸'] = 266, 
        ['邵阳'] = 82, 
        ['郑州'] = 8, 
        ['郴州'] = 271, 
        ['鄂尔多斯'] = 4, 
        ['鄂州'] = 77, 
        ['酒泉'] = 344, 
        ['重庆'] = 375, 
        ['金华'] = 286, 
        ['金昌'] = 250, 
        ['金门县'] = 10, 
        ['钦州'] = 295, 
        ['铁岭'] = 304, 
        ['铁门关'] = 328, 
        ['铜仁'] = 50, 
        ['铜川'] = 288, 
        ['铜陵'] = 88, 
        ['银川'] = 373, 
        ['锡林郭勒盟'] = 74, 
        ['锦州'] = 216, 
        ['镇江'] = 145, 
        ['长春'] = 48, 
        ['长沙'] = 106, 
        ['长治'] = 140, 
        ['阜新'] = 153, 
        ['阜阳'] = 331, 
        ['防城港'] = 39, 
        ['阳江'] = 202, 
        ['阳泉'] = 24, 
        ['阿克苏地区'] = 63, 
        ['阿勒泰地区'] = 136, 
        ['阿坝藏族羌族自治州'] = 152, 
        ['阿拉善盟'] = 253, 
        ['阿拉尔'] = 174, 
        ['阿里地区'] = 388, 
        ['陇南'] = 168, 
        ['陵水黎族自治县'] = 25, 
        ['随州'] = 209, 
        ['雅安'] = 341, 
        ['青岛'] = 182, 
        ['鞍山'] = 68, 
        ['韶关'] = 109, 
        ['马鞍山'] = 83, 
        ['驻马店'] = 143, 
        ['高雄市'] = 325, 
        ['鸡西'] = 372, 
        ['鹤壁'] = 310, 
        ['鹤岗'] = 161, 
        ['鹰潭'] = 262, 
        ['黄冈'] = 356, 
        ['黄南藏族自治州'] = 329, 
        ['黄山'] = 90, 
        ['黄石'] = 269, 
        ['黑河'] = 268, 
        ['黔东南苗族侗族自治州'] = 44, 
        ['黔南布依族苗族自治州'] = 118, 
        ['黔西南布依族苗族自治州'] = 62, 
        ['齐齐哈尔'] = 247, 
        ['龙岩'] = 210, 
    },
    RankClientCities = {Game.TableDataManager:GetLangStr('str_44531294668288'),Game.TableDataManager:GetLangStr('str_44531294668544'),Game.TableDataManager:GetLangStr('str_44531294668800'),Game.TableDataManager:GetLangStr('str_44531294669056'),Game.TableDataManager:GetLangStr('str_44531294669312'),Game.TableDataManager:GetLangStr('str_44531294669568'),Game.TableDataManager:GetLangStr('str_44531294669824'),Game.TableDataManager:GetLangStr('str_44531294670080'),Game.TableDataManager:GetLangStr('str_44531294670336'),Game.TableDataManager:GetLangStr('str_44531294670592'),Game.TableDataManager:GetLangStr('str_44531294670848'),Game.TableDataManager:GetLangStr('str_44531294671360'),Game.TableDataManager:GetLangStr('str_44531294671616'),Game.TableDataManager:GetLangStr('str_44531294671872'),Game.TableDataManager:GetLangStr('str_44531294672128'),Game.TableDataManager:GetLangStr('str_44531294672384'),Game.TableDataManager:GetLangStr('str_44531294672640'),Game.TableDataManager:GetLangStr('str_44531294672896'),Game.TableDataManager:GetLangStr('str_44531294673152'),Game.TableDataManager:GetLangStr('str_44531294673408'),Game.TableDataManager:GetLangStr('str_44531294673664'),Game.TableDataManager:GetLangStr('str_44531294673920'),Game.TableDataManager:GetLangStr('str_44531294674176'),Game.TableDataManager:GetLangStr('str_44531294674432'),Game.TableDataManager:GetLangStr('str_44531294674688'),Game.TableDataManager:GetLangStr('str_44531294674944'),Game.TableDataManager:GetLangStr('str_44531294675456'),Game.TableDataManager:GetLangStr('str_44531294675712'),Game.TableDataManager:GetLangStr('str_44531294675968'),Game.TableDataManager:GetLangStr('str_44531294676224'),Game.TableDataManager:GetLangStr('str_44531294676480'),Game.TableDataManager:GetLangStr('str_44531294676736'),Game.TableDataManager:GetLangStr('str_44531294676992'),Game.TableDataManager:GetLangStr('str_44531294677248'),Game.TableDataManager:GetLangStr('str_44531294677504'),Game.TableDataManager:GetLangStr('str_44531294677760'),Game.TableDataManager:GetLangStr('str_44531294678016'),Game.TableDataManager:GetLangStr('str_44531294678272'),Game.TableDataManager:GetLangStr('str_44531294678528'),Game.TableDataManager:GetLangStr('str_44531294678784'),Game.TableDataManager:GetLangStr('str_44531294679296'),Game.TableDataManager:GetLangStr('str_44531294679552'),Game.TableDataManager:GetLangStr('str_44531294679808'),Game.TableDataManager:GetLangStr('str_44531294680064'),Game.TableDataManager:GetLangStr('str_44531294680320'),Game.TableDataManager:GetLangStr('str_44531294680576'),Game.TableDataManager:GetLangStr('str_44531294680832'),Game.TableDataManager:GetLangStr('str_44531294681088'),Game.TableDataManager:GetLangStr('str_44531294681344'),Game.TableDataManager:GetLangStr('str_44531294681600'),Game.TableDataManager:GetLangStr('str_44531294681856'),Game.TableDataManager:GetLangStr('str_44531294682112'),Game.TableDataManager:GetLangStr('str_44531294682368'),Game.TableDataManager:GetLangStr('str_44531294682624'),Game.TableDataManager:GetLangStr('str_44531294682880'),Game.TableDataManager:GetLangStr('str_44531294683136'),Game.TableDataManager:GetLangStr('str_44531294683392'),Game.TableDataManager:GetLangStr('str_44531294683904'),Game.TableDataManager:GetLangStr('str_44531294684416'),Game.TableDataManager:GetLangStr('str_44531294684672'),Game.TableDataManager:GetLangStr('str_44531294684928'),Game.TableDataManager:GetLangStr('str_44531294685184'),Game.TableDataManager:GetLangStr('str_44531294685440'),Game.TableDataManager:GetLangStr('str_44531294685696'),Game.TableDataManager:GetLangStr('str_44531294686208'),Game.TableDataManager:GetLangStr('str_44531294686464'),Game.TableDataManager:GetLangStr('str_44531294686720'),Game.TableDataManager:GetLangStr('str_44531294686976'),Game.TableDataManager:GetLangStr('str_44531294687232'),Game.TableDataManager:GetLangStr('str_44531294687488'),Game.TableDataManager:GetLangStr('str_44531294687744'),Game.TableDataManager:GetLangStr('str_44531294688000'),Game.TableDataManager:GetLangStr('str_44531294688256'),Game.TableDataManager:GetLangStr('str_44531294688512'),Game.TableDataManager:GetLangStr('str_44531294688768'),Game.TableDataManager:GetLangStr('str_44531294689024'),Game.TableDataManager:GetLangStr('str_44531294689280'),Game.TableDataManager:GetLangStr('str_44531294689536'),Game.TableDataManager:GetLangStr('str_44531294689792'),Game.TableDataManager:GetLangStr('str_44531294690048'),Game.TableDataManager:GetLangStr('str_44531294690304'),Game.TableDataManager:GetLangStr('str_44531294690560'),Game.TableDataManager:GetLangStr('str_44531294690816'),Game.TableDataManager:GetLangStr('str_44531294691072'),Game.TableDataManager:GetLangStr('str_44531294691328'),Game.TableDataManager:GetLangStr('str_44531294691584'),Game.TableDataManager:GetLangStr('str_44531294691840'),Game.TableDataManager:GetLangStr('str_44531294692096'),Game.TableDataManager:GetLangStr('str_44531294692352'),Game.TableDataManager:GetLangStr('str_44531294692608'),Game.TableDataManager:GetLangStr('str_44531294692864'),Game.TableDataManager:GetLangStr('str_44531294693120'),Game.TableDataManager:GetLangStr('str_44531294693376'),Game.TableDataManager:GetLangStr('str_44531294693632'),Game.TableDataManager:GetLangStr('str_44531294693888'),Game.TableDataManager:GetLangStr('str_44531294694144'),Game.TableDataManager:GetLangStr('str_44531294694656'),Game.TableDataManager:GetLangStr('str_44531294694912'),Game.TableDataManager:GetLangStr('str_44531294695168'),Game.TableDataManager:GetLangStr('str_44531294695424'),Game.TableDataManager:GetLangStr('str_44531294695680'),Game.TableDataManager:GetLangStr('str_44531294695936'),Game.TableDataManager:GetLangStr('str_44531294696192'),Game.TableDataManager:GetLangStr('str_44531294696448'),Game.TableDataManager:GetLangStr('str_44531294696704'),Game.TableDataManager:GetLangStr('str_44531294696960'),Game.TableDataManager:GetLangStr('str_44531294697216'),Game.TableDataManager:GetLangStr('str_44531294697728'),Game.TableDataManager:GetLangStr('str_44531294697984'),Game.TableDataManager:GetLangStr('str_44531294698240'),Game.TableDataManager:GetLangStr('str_44531294698496'),Game.TableDataManager:GetLangStr('str_44531294698752'),Game.TableDataManager:GetLangStr('str_44531294699008'),Game.TableDataManager:GetLangStr('str_44531294699264'),Game.TableDataManager:GetLangStr('str_44531294699520'),Game.TableDataManager:GetLangStr('str_44531294700032'),Game.TableDataManager:GetLangStr('str_44531294700288'),Game.TableDataManager:GetLangStr('str_44531294700544'),Game.TableDataManager:GetLangStr('str_44531294700800'),Game.TableDataManager:GetLangStr('str_44531294701056'),Game.TableDataManager:GetLangStr('str_44531294701312'),Game.TableDataManager:GetLangStr('str_44531294701568'),Game.TableDataManager:GetLangStr('str_44531294701824'),Game.TableDataManager:GetLangStr('str_44531294702336'),Game.TableDataManager:GetLangStr('str_44531294702592'),Game.TableDataManager:GetLangStr('str_44531294702848'),Game.TableDataManager:GetLangStr('str_44531294703104'),Game.TableDataManager:GetLangStr('str_44531294703360'),Game.TableDataManager:GetLangStr('str_44531294703616'),Game.TableDataManager:GetLangStr('str_44531294703872'),Game.TableDataManager:GetLangStr('str_44531294704128'),Game.TableDataManager:GetLangStr('str_44531294704384'),Game.TableDataManager:GetLangStr('str_44531294704640'),Game.TableDataManager:GetLangStr('str_44531294704896'),Game.TableDataManager:GetLangStr('str_44531294705152'),Game.TableDataManager:GetLangStr('str_44531294705408'),Game.TableDataManager:GetLangStr('str_44531294705664'),Game.TableDataManager:GetLangStr('str_44531294705920'),Game.TableDataManager:GetLangStr('str_44531294706176'),Game.TableDataManager:GetLangStr('str_44531294706432'),Game.TableDataManager:GetLangStr('str_44531294706688'),Game.TableDataManager:GetLangStr('str_44531294706944'),Game.TableDataManager:GetLangStr('str_44531294707200'),Game.TableDataManager:GetLangStr('str_44531294707456'),Game.TableDataManager:GetLangStr('str_44531294707712'),Game.TableDataManager:GetLangStr('str_44531294707968'),Game.TableDataManager:GetLangStr('str_44531294708480'),Game.TableDataManager:GetLangStr('str_44531294708992'),Game.TableDataManager:GetLangStr('str_44531294709248'),Game.TableDataManager:GetLangStr('str_44531294709504'),Game.TableDataManager:GetLangStr('str_44531294709760'),Game.TableDataManager:GetLangStr('str_44531294710016'),Game.TableDataManager:GetLangStr('str_44531294710272'),Game.TableDataManager:GetLangStr('str_44531294710528'),Game.TableDataManager:GetLangStr('str_44531294710784'),Game.TableDataManager:GetLangStr('str_44531294711040'),Game.TableDataManager:GetLangStr('str_44531294711296'),Game.TableDataManager:GetLangStr('str_44531294711552'),Game.TableDataManager:GetLangStr('str_44531294712064'),Game.TableDataManager:GetLangStr('str_44531294712320'),Game.TableDataManager:GetLangStr('str_44531294712576'),Game.TableDataManager:GetLangStr('str_44531294712832'),Game.TableDataManager:GetLangStr('str_44531294713088'),Game.TableDataManager:GetLangStr('str_44531294713344'),Game.TableDataManager:GetLangStr('str_44531294713600'),Game.TableDataManager:GetLangStr('str_44531294713856'),Game.TableDataManager:GetLangStr('str_44531294714112'),Game.TableDataManager:GetLangStr('str_44531294714368'),Game.TableDataManager:GetLangStr('str_44531294714624'),Game.TableDataManager:GetLangStr('str_44531294714880'),Game.TableDataManager:GetLangStr('str_44531294715136'),Game.TableDataManager:GetLangStr('str_44531294715904'),Game.TableDataManager:GetLangStr('str_44531294716160'),Game.TableDataManager:GetLangStr('str_44531294716416'),Game.TableDataManager:GetLangStr('str_44531294716672'),Game.TableDataManager:GetLangStr('str_44531294716928'),Game.TableDataManager:GetLangStr('str_44531294717440'),Game.TableDataManager:GetLangStr('str_44531294717952'),Game.TableDataManager:GetLangStr('str_44531294718208'),Game.TableDataManager:GetLangStr('str_44531294718464'),Game.TableDataManager:GetLangStr('str_44531294718720'),Game.TableDataManager:GetLangStr('str_44531294718976'),Game.TableDataManager:GetLangStr('str_44531294719232'),Game.TableDataManager:GetLangStr('str_44531294719488'),Game.TableDataManager:GetLangStr('str_44531294719744'),Game.TableDataManager:GetLangStr('str_44531294720000'),Game.TableDataManager:GetLangStr('str_44531294720256'),Game.TableDataManager:GetLangStr('str_44531294720512'),Game.TableDataManager:GetLangStr('str_44531026285312'),Game.TableDataManager:GetLangStr('str_44531294721024'),Game.TableDataManager:GetLangStr('str_44531294721280'),Game.TableDataManager:GetLangStr('str_44531294721536'),Game.TableDataManager:GetLangStr('str_44531294721792'),Game.TableDataManager:GetLangStr('str_44531294722048'),Game.TableDataManager:GetLangStr('str_44531294722304'),Game.TableDataManager:GetLangStr('str_44531294722560'),Game.TableDataManager:GetLangStr('str_44531294723072'),Game.TableDataManager:GetLangStr('str_44531294723328'),Game.TableDataManager:GetLangStr('str_44531294723584'),Game.TableDataManager:GetLangStr('str_44531294723840'),Game.TableDataManager:GetLangStr('str_44531294724608'),Game.TableDataManager:GetLangStr('str_44531294724864'),Game.TableDataManager:GetLangStr('str_44531294725120'),Game.TableDataManager:GetLangStr('str_44531294725376'),Game.TableDataManager:GetLangStr('str_44531294725632'),Game.TableDataManager:GetLangStr('str_44531294725888'),Game.TableDataManager:GetLangStr('str_44531294726144'),Game.TableDataManager:GetLangStr('str_44531294726400'),Game.TableDataManager:GetLangStr('str_44531294726656'),Game.TableDataManager:GetLangStr('str_44531294726912'),Game.TableDataManager:GetLangStr('str_44531294727168'),Game.TableDataManager:GetLangStr('str_44531294727680'),Game.TableDataManager:GetLangStr('str_44531294727936'),Game.TableDataManager:GetLangStr('str_44531294728192'),Game.TableDataManager:GetLangStr('str_44531294728448'),Game.TableDataManager:GetLangStr('str_44531294728960'),Game.TableDataManager:GetLangStr('str_44531294729216'),Game.TableDataManager:GetLangStr('str_44531294729472'),Game.TableDataManager:GetLangStr('str_44531294729728'),Game.TableDataManager:GetLangStr('str_44531294729984'),Game.TableDataManager:GetLangStr('str_44531294730240'),Game.TableDataManager:GetLangStr('str_44531294730496'),Game.TableDataManager:GetLangStr('str_44531294730752'),Game.TableDataManager:GetLangStr('str_44531294731264'),Game.TableDataManager:GetLangStr('str_44531294731520'),Game.TableDataManager:GetLangStr('str_44531294731776'),Game.TableDataManager:GetLangStr('str_44531294732032'),Game.TableDataManager:GetLangStr('str_44531294732288'),Game.TableDataManager:GetLangStr('str_44531294732544'),Game.TableDataManager:GetLangStr('str_44531294732800'),Game.TableDataManager:GetLangStr('str_44531294733056'),Game.TableDataManager:GetLangStr('str_44531294733312'),Game.TableDataManager:GetLangStr('str_44531294733824'),Game.TableDataManager:GetLangStr('str_44531294734080'),Game.TableDataManager:GetLangStr('str_44531294734336'),Game.TableDataManager:GetLangStr('str_44531294734592'),Game.TableDataManager:GetLangStr('str_44531294734848'),Game.TableDataManager:GetLangStr('str_44531294735104'),Game.TableDataManager:GetLangStr('str_44531294735360'),Game.TableDataManager:GetLangStr('str_44531294735616'),Game.TableDataManager:GetLangStr('str_44531294735872'),Game.TableDataManager:GetLangStr('str_44531294736128'),Game.TableDataManager:GetLangStr('str_44531294736384'),Game.TableDataManager:GetLangStr('str_44531294736640'),Game.TableDataManager:GetLangStr('str_44531294736896'),Game.TableDataManager:GetLangStr('str_44531294737152'),Game.TableDataManager:GetLangStr('str_44531294737408'),Game.TableDataManager:GetLangStr('str_44531294737664'),Game.TableDataManager:GetLangStr('str_44531294737920'),Game.TableDataManager:GetLangStr('str_44531294738176'),Game.TableDataManager:GetLangStr('str_44531294738432'),Game.TableDataManager:GetLangStr('str_44531294738688'),Game.TableDataManager:GetLangStr('str_44531294738944'),Game.TableDataManager:GetLangStr('str_44531294739200'),Game.TableDataManager:GetLangStr('str_44531294739456'),Game.TableDataManager:GetLangStr('str_44531294739712'),Game.TableDataManager:GetLangStr('str_44531294739968'),Game.TableDataManager:GetLangStr('str_44531026304768'),Game.TableDataManager:GetLangStr('str_44531294740480'),Game.TableDataManager:GetLangStr('str_44531294740736'),Game.TableDataManager:GetLangStr('str_44531294740992'),Game.TableDataManager:GetLangStr('str_44531294741248'),Game.TableDataManager:GetLangStr('str_44531294741504'),Game.TableDataManager:GetLangStr('str_44531294741760'),Game.TableDataManager:GetLangStr('str_44531294742272'),Game.TableDataManager:GetLangStr('str_44531294742528'),Game.TableDataManager:GetLangStr('str_44531294742784'),Game.TableDataManager:GetLangStr('str_44531294743040'),Game.TableDataManager:GetLangStr('str_44531294743296'),Game.TableDataManager:GetLangStr('str_44531294743552'),Game.TableDataManager:GetLangStr('str_44531294743808'),Game.TableDataManager:GetLangStr('str_44531294744064'),Game.TableDataManager:GetLangStr('str_44531294744320'),Game.TableDataManager:GetLangStr('str_44531294744576'),Game.TableDataManager:GetLangStr('str_44531294744832'),Game.TableDataManager:GetLangStr('str_44531294745088'),Game.TableDataManager:GetLangStr('str_44531294745344'),Game.TableDataManager:GetLangStr('str_44531294745600'),Game.TableDataManager:GetLangStr('str_44531294745856'),Game.TableDataManager:GetLangStr('str_44531294746112'),Game.TableDataManager:GetLangStr('str_44531294746368'),Game.TableDataManager:GetLangStr('str_44531294746624'),Game.TableDataManager:GetLangStr('str_44531294746880'),Game.TableDataManager:GetLangStr('str_44531294747136'),Game.TableDataManager:GetLangStr('str_44531294747392'),Game.TableDataManager:GetLangStr('str_44531294747648'),Game.TableDataManager:GetLangStr('str_44531294747904'),Game.TableDataManager:GetLangStr('str_44531294748160'),Game.TableDataManager:GetLangStr('str_44531294748416'),Game.TableDataManager:GetLangStr('str_44531294748672'),Game.TableDataManager:GetLangStr('str_44531294748928'),Game.TableDataManager:GetLangStr('str_44531294749184'),Game.TableDataManager:GetLangStr('str_44531294749440'),Game.TableDataManager:GetLangStr('str_44531294749696'),Game.TableDataManager:GetLangStr('str_44531294749952'),Game.TableDataManager:GetLangStr('str_44531294750208'),Game.TableDataManager:GetLangStr('str_44531294750464'),Game.TableDataManager:GetLangStr('str_44531294750976'),Game.TableDataManager:GetLangStr('str_44531294751232'),Game.TableDataManager:GetLangStr('str_44531294751488'),Game.TableDataManager:GetLangStr('str_44531294752000'),Game.TableDataManager:GetLangStr('str_44531294752256'),Game.TableDataManager:GetLangStr('str_44531294752512'),Game.TableDataManager:GetLangStr('str_44531294752768'),Game.TableDataManager:GetLangStr('str_44531294753024'),Game.TableDataManager:GetLangStr('str_44531294753280'),Game.TableDataManager:GetLangStr('str_44531294753536'),Game.TableDataManager:GetLangStr('str_44531294753792'),Game.TableDataManager:GetLangStr('str_44531294754048'),Game.TableDataManager:GetLangStr('str_44531294754304'),Game.TableDataManager:GetLangStr('str_44531294754560'),Game.TableDataManager:GetLangStr('str_44531294754816'),Game.TableDataManager:GetLangStr('str_44531294755072'),Game.TableDataManager:GetLangStr('str_44531294755328'),Game.TableDataManager:GetLangStr('str_44531294755584'),Game.TableDataManager:GetLangStr('str_44531294755840'),Game.TableDataManager:GetLangStr('str_44531294756096'),Game.TableDataManager:GetLangStr('str_44531294756352'),Game.TableDataManager:GetLangStr('str_44531294756608'),Game.TableDataManager:GetLangStr('str_44531026321408'),Game.TableDataManager:GetLangStr('str_44531294757120'),Game.TableDataManager:GetLangStr('str_44531294757376'),Game.TableDataManager:GetLangStr('str_44531294757632'),Game.TableDataManager:GetLangStr('str_44531294757888'),Game.TableDataManager:GetLangStr('str_44531294758144'),Game.TableDataManager:GetLangStr('str_44531294758400'),Game.TableDataManager:GetLangStr('str_44531294758656'),Game.TableDataManager:GetLangStr('str_44531294758912'),Game.TableDataManager:GetLangStr('str_44531294759424'),Game.TableDataManager:GetLangStr('str_44531294759680'),Game.TableDataManager:GetLangStr('str_44531294759936'),Game.TableDataManager:GetLangStr('str_44531294760192'),Game.TableDataManager:GetLangStr('str_44531294760448'),Game.TableDataManager:GetLangStr('str_44531294760704'),Game.TableDataManager:GetLangStr('str_44531294760960'),Game.TableDataManager:GetLangStr('str_44531294761216'),Game.TableDataManager:GetLangStr('str_44531294761472'),Game.TableDataManager:GetLangStr('str_44531294761728'),Game.TableDataManager:GetLangStr('str_44531294761984'),Game.TableDataManager:GetLangStr('str_44531294762240'),Game.TableDataManager:GetLangStr('str_44531294762496'),Game.TableDataManager:GetLangStr('str_44531294762752'),Game.TableDataManager:GetLangStr('str_44531294763008'),Game.TableDataManager:GetLangStr('str_44531294763264'),Game.TableDataManager:GetLangStr('str_44531294763520'),Game.TableDataManager:GetLangStr('str_44531294763776'),Game.TableDataManager:GetLangStr('str_44531294764032'),Game.TableDataManager:GetLangStr('str_44531294764288'),Game.TableDataManager:GetLangStr('str_44531294764544'),Game.TableDataManager:GetLangStr('str_44531294764800'),Game.TableDataManager:GetLangStr('str_44531294765056'),Game.TableDataManager:GetLangStr('str_44531294765312'),Game.TableDataManager:GetLangStr('str_44531294765568'),Game.TableDataManager:GetLangStr('str_44531294765824'),Game.TableDataManager:GetLangStr('str_44531294766080'),Game.TableDataManager:GetLangStr('str_44531294766336'),Game.TableDataManager:GetLangStr('str_44531294766592'),Game.TableDataManager:GetLangStr('str_44531294766848'),Game.TableDataManager:GetLangStr('str_44531294767104'),Game.TableDataManager:GetLangStr('str_44531294767616'),Game.TableDataManager:GetLangStr('str_44531294767872'),Game.TableDataManager:GetLangStr('str_44531294768128'),Game.TableDataManager:GetLangStr('str_44531294768384'),Game.TableDataManager:GetLangStr('str_44531294768640'),Game.TableDataManager:GetLangStr('str_44531294768896'),Game.TableDataManager:GetLangStr('str_44531294769152'),Game.TableDataManager:GetLangStr('str_44531294769408'),Game.TableDataManager:GetLangStr('str_44531294769664'),Game.TableDataManager:GetLangStr('str_44531294769920'),Game.TableDataManager:GetLangStr('str_44531294770176'),Game.TableDataManager:GetLangStr('str_44531294770432'),Game.TableDataManager:GetLangStr('str_44531294770688'),Game.TableDataManager:GetLangStr('str_44531294770944'),Game.TableDataManager:GetLangStr('str_44531294771200'),Game.TableDataManager:GetLangStr('str_44531026336000'),Game.TableDataManager:GetLangStr('str_44531294771712'),Game.TableDataManager:GetLangStr('str_44531294771968'),Game.TableDataManager:GetLangStr('str_44531294772224'),Game.TableDataManager:GetLangStr('str_44531294772480'),Game.TableDataManager:GetLangStr('str_44531294772736'),Game.TableDataManager:GetLangStr('str_44531294772992'),Game.TableDataManager:GetLangStr('str_44531294773248'),Game.TableDataManager:GetLangStr('str_44531294773504'),Game.TableDataManager:GetLangStr('str_44531294773760'),Game.TableDataManager:GetLangStr('str_44531294774016'),Game.TableDataManager:GetLangStr('str_44531294774272'),Game.TableDataManager:GetLangStr('str_44531294774784'),Game.TableDataManager:GetLangStr('str_44531294775040'),
    },
    RankClientProvinces = {Game.TableDataManager:GetLangStr('str_44531026232832'),Game.TableDataManager:GetLangStr('str_44531026233088'),Game.TableDataManager:GetLangStr('str_44531026233344'),Game.TableDataManager:GetLangStr('str_44531026233600'),Game.TableDataManager:GetLangStr('str_44531026233856'),Game.TableDataManager:GetLangStr('str_44531026234112'),Game.TableDataManager:GetLangStr('str_44531026234368'),Game.TableDataManager:GetLangStr('str_44531026234624'),Game.TableDataManager:GetLangStr('str_44531026234880'),Game.TableDataManager:GetLangStr('str_44531026235392'),Game.TableDataManager:GetLangStr('str_44531026235648'),Game.TableDataManager:GetLangStr('str_44531026236416'),Game.TableDataManager:GetLangStr('str_44531026236672'),Game.TableDataManager:GetLangStr('str_44531026236928'),Game.TableDataManager:GetLangStr('str_44531026237440'),Game.TableDataManager:GetLangStr('str_44531026237696'),Game.TableDataManager:GetLangStr('str_44531026239488'),Game.TableDataManager:GetLangStr('str_44531026241024'),Game.TableDataManager:GetLangStr('str_44531026241792'),Game.TableDataManager:GetLangStr('str_44531026243584'),Game.TableDataManager:GetLangStr('str_44531026244608'),Game.TableDataManager:GetLangStr('str_44531026245632'),Game.TableDataManager:GetLangStr('str_44531026246656'),Game.TableDataManager:GetLangStr('str_44531026246912'),Game.TableDataManager:GetLangStr('str_44531026247936'),Game.TableDataManager:GetLangStr('str_44531026250752'),Game.TableDataManager:GetLangStr('str_44531026252288'),Game.TableDataManager:GetLangStr('str_44531026266624'),Game.TableDataManager:GetLangStr('str_44531026281728'),Game.TableDataManager:GetLangStr('str_44531026285312'),Game.TableDataManager:GetLangStr('str_44531026291968'),Game.TableDataManager:GetLangStr('str_44531026304768'),Game.TableDataManager:GetLangStr('str_44531026321408'),Game.TableDataManager:GetLangStr('str_44531026336000'),Game.TableDataManager:GetLangStr('str_44531026264320'),
    },
    RankCountryIndex = {
        ['中国'] = 35, 
    },
    RankIgnoreCities = {
        [189] = true, 
        [258] = true, 
        [320] = true, 
        [375] = true, 
    },
    RankIgnoreProvinces = {
        [35] = true, 
    },
    RankProvinceIndex = {
        ['上海'] = 32, 
        ['中国'] = 35, 
        ['云南'] = 16, 
        ['内蒙古'] = 4, 
        ['北京'] = 33, 
        ['台湾'] = 3, 
        ['吉林'] = 22, 
        ['四川'] = 5, 
        ['天津'] = 30, 
        ['宁夏'] = 25, 
        ['安徽'] = 23, 
        ['山东'] = 11, 
        ['山西'] = 13, 
        ['广东'] = 12, 
        ['广西'] = 19, 
        ['新疆'] = 6, 
        ['江苏'] = 2, 
        ['江西'] = 29, 
        ['河北'] = 20, 
        ['河南'] = 8, 
        ['浙江'] = 18, 
        ['海南'] = 7, 
        ['湖北'] = 14, 
        ['湖南'] = 10, 
        ['澳门'] = 28, 
        ['甘肃'] = 17, 
        ['福建'] = 24, 
        ['西藏'] = 1, 
        ['贵州'] = 21, 
        ['辽宁'] = 26, 
        ['重庆'] = 34, 
        ['陕西'] = 15, 
        ['青海'] = 27, 
        ['香港'] = 31, 
        ['黑龙江'] = 9, 
    },
    RankRegions = {{[1] = true, [29] = true, [38] = true, [115] = true, [131] = true, [374] = true, [388] = true, }, {[2] = true, [32] = true, [37] = true, [66] = true, [75] = true, [114] = true, [145] = true, [226] = true, [233] = true, [234] = true, [265] = true, [284] = true, [361] = true, }, {[3] = true, [10] = true, [23] = true, [45] = true, [58] = true, [59] = true, [60] = true, [97] = true, [126] = true, [146] = true, [179] = true, [190] = true, [200] = true, [206] = true, [244] = true, [255] = true, [291] = true, [302] = true, [305] = true, [316] = true, [322] = true, [325] = true, }, {[4] = true, [55] = true, [74] = true, [85] = true, [96] = true, [121] = true, [138] = true, [147] = true, [225] = true, [253] = true, [264] = true, [298] = true, }, {[5] = true, [12] = true, [13] = true, [61] = true, [64] = true, [107] = true, [108] = true, [120] = true, [144] = true, [152] = true, [186] = true, [187] = true, [222] = true, [272] = true, [274] = true, [307] = true, [326] = true, [341] = true, [351] = true, [367] = true, [368] = true, }, {[6] = true, [22] = true, [30] = true, [33] = true, [43] = true, [63] = true, [84] = true, [105] = true, [128] = true, [136] = true, [167] = true, [174] = true, [175] = true, [176] = true, [214] = true, [218] = true, [254] = true, [257] = true, [277] = true, [293] = true, [328] = true, [348] = true, [357] = true, }, {[7] = true, [20] = true, [21] = true, [25] = true, [28] = true, [113] = true, [125] = true, [130] = true, [212] = true, [270] = true, [279] = true, [292] = true, [296] = true, [300] = true, [301] = true, [311] = true, [340] = true, [343] = true, [381] = true, }, {[8] = true, [40] = true, [54] = true, [80] = true, [104] = true, [139] = true, [143] = true, [158] = true, [160] = true, [173] = true, [196] = true, [211] = true, [232] = true, [281] = true, [310] = true, [339] = true, [350] = true, [362] = true, }, {[9] = true, [161] = true, [185] = true, [208] = true, [228] = true, [247] = true, [251] = true, [268] = true, [285] = true, [312] = true, [317] = true, [353] = true, [372] = true, }, {[11] = true, [82] = true, [106] = true, [122] = true, [133] = true, [165] = true, [198] = true, [224] = true, [237] = true, [261] = true, [263] = true, [267] = true, [271] = true, [369] = true, }, {[46] = true, [76] = true, [103] = true, [123] = true, [135] = true, [154] = true, [169] = true, [182] = true, [203] = true, [219] = true, [223] = true, [282] = true, [294] = true, [315] = true, [371] = true, [380] = true, }, {[14] = true, [47] = true, [51] = true, [67] = true, [86] = true, [109] = true, [134] = true, [170] = true, [191] = true, [202] = true, [221] = true, [227] = true, [238] = true, [249] = true, [252] = true, [259] = true, [323] = true, [324] = true, [332] = true, [360] = true, [385] = true, }, {[15] = true, [17] = true, [24] = true, [42] = true, [129] = true, [140] = true, [155] = true, [245] = true, [283] = true, [297] = true, [363] = true, }, {[16] = true, [49] = true, [73] = true, [77] = true, [92] = true, [112] = true, [119] = true, [163] = true, [164] = true, [180] = true, [199] = true, [209] = true, [269] = true, [273] = true, [321] = true, [330] = true, [356] = true, }, {[18] = true, [98] = true, [124] = true, [142] = true, [171] = true, [177] = true, [184] = true, [207] = true, [288] = true, [370] = true, }, {[19] = true, [27] = true, [35] = true, [56] = true, [89] = true, [132] = true, [157] = true, [197] = true, [217] = true, [276] = true, [289] = true, [335] = true, [345] = true, [346] = true, [347] = true, [352] = true, }, {[26] = true, [70] = true, [111] = true, [148] = true, [151] = true, [168] = true, [213] = true, [250] = true, [308] = true, [314] = true, [336] = true, [342] = true, [344] = true, [377] = true, }, {[31] = true, [36] = true, [110] = true, [116] = true, [117] = true, [181] = true, [193] = true, [280] = true, [286] = true, [333] = true, [338] = true, }, {[34] = true, [39] = true, [69] = true, [72] = true, [78] = true, [94] = true, [141] = true, [159] = true, [215] = true, [243] = true, [287] = true, [295] = true, [349] = true, [387] = true, }, {[41] = true, [93] = true, [99] = true, [178] = true, [192] = true, [241] = true, [242] = true, [246] = true, [266] = true, [354] = true, [378] = true, }, {[44] = true, [50] = true, [62] = true, [79] = true, [81] = true, [118] = true, [162] = true, [256] = true, [358] = true, }, {[48] = true, [95] = true, [149] = true, [150] = true, [194] = true, [229] = true, [239] = true, [306] = true, [386] = true, }, {[52] = true, [83] = true, [87] = true, [88] = true, [90] = true, [91] = true, [188] = true, [204] = true, [220] = true, [235] = true, [240] = true, [327] = true, [331] = true, [359] = true, [365] = true, [376] = true, }, {[53] = true, [100] = true, [127] = true, [156] = true, [172] = true, [210] = true, [260] = true, [309] = true, [319] = true, }, {[57] = true, [101] = true, [275] = true, [355] = true, [373] = true, }, {[65] = true, [68] = true, [102] = true, [153] = true, [166] = true, [183] = true, [216] = true, [278] = true, [290] = true, [299] = true, [303] = true, [304] = true, [364] = true, [383] = true, }, {[71] = true, [137] = true, [205] = true, [313] = true, [329] = true, [334] = true, [337] = true, [379] = true, }, {}, {[195] = true, [201] = true, [230] = true, [231] = true, [236] = true, [248] = true, [262] = true, [318] = true, [366] = true, [382] = true, [384] = true, }, {[189] = true, }, {}, {[258] = true, }, {[320] = true, }, {[375] = true, }, {}, 
    },
    data = {
    }
}
return TopData