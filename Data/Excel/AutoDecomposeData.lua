--
-- 表名: $Inventory_背包页签.xlsx  页名：$AutoDecompose_自动分解
--

local TopData = {
	data = {
		[1] = {
			["RuleID"] = 1,
			["RuleDes"] = Game.TableDataManager:GetLangStr('str_30649691932160'),
			["TypeId"] = {2},
			["itemRareFilterType"] = {1, 2, 3, 4, 5},
			["markFilter"] = 0,
		},
		[2] = {
			["RuleID"] = 2,
			["RuleDes"] = Game.TableDataManager:GetLangStr('str_30649691932416'),
			["TypeId"] = {0},
			["itemRareFilterType"] = {1, 2, 3, 4, 5, 0},
			["markFilter"] = 0,
		},
		[3] = {
			["RuleID"] = 3,
			["RuleDes"] = Game.TableDataManager:GetLangStr('str_30649691932672'),
			["TypeId"] = {0},
			["itemRareFilterType"] = {},
			["markFilter"] = 1,
		},
	},
}

return TopData
