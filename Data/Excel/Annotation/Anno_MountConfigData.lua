---@class _MountConfigDataRow
---@field MountConfigID number
---@field AttachmentName string
---@field AnimGroup string
---@field CallMountAnimName string
---@field GetInAnimName string
---@field GetInAnimNameRight string
---@field MoveGetInAnimName string
---@field MoveGetInAnimNameRight string
---@field GetOutAnimName string
---@field MoveGetOutAnimName string
---@field MountGetInAnimName string
---@field MountGetInAnimNameRight string
---@field MountMoveGetInAnimName string
---@field MountMoveGetInAnimNameRight string
---@field MountGetOutAnimName string
---@field MountMoveGetOutAnimName string
---@field MaxDeltaYaw number
---@field DeltaYawScale number
---@field RidingMinSpeedRate number
---@field DashMaxDeltaYaw number
---@field Dash<PERSON>eltaYawScale number
---@field DashRidingMinSpeedRate number
---@field FrontMountLength number
---@field EndMountLength number
---@field LeftMountLength number
---@field RightMountLength number
---@field FrontWheelRadius number
---@field EndWheelRadius number
---@field DirChangeHalfTimeCurve string
---@field PitchHalfTime number
---@field RollHalfTime number
---@field RootOffsetHalfTime number
---@field MountType number
---@field FWheelName string
---@field FWheelNameRight string
---@field RWheelName string
---@field RWheelNameRight string
---@field RiderSocket string
---@field bEnableHandIK boolean
---@field bEnableFootIK boolean
---@field FadeInDissolveEffectID number
---@field FadeOutDissolveEffectID number
---@field AnimCrossFadeTime number
---@field bAllowDrift boolean
---@field DriftForceDecayHalfTimeCurve string
---@field DriftForceYawHalfTimeCurve string
---@field MaxDriftSpeedCurve string
---@field MaxJumpNumber number

---@class _MountConfigData
---@field data _MountConfigDataRow[]
