---@class _ItemNewDataRow
---@field ID number
---@field itemName string
---@field funcRep string
---@field itemDes string
---@field funcSimpleRep string
---@field EnglishName string
---@field quality 
---@field type number
---@field subType number
---@field Order number
---@field invId number
---@field icon string
---@field DropTemplateID number
---@field AchievePath string
---@field operationGroupType 
---@field lvReq number
---@field lvTop number
---@field classlimit table
---@field canReuseTimes number
---@field sharingUseGroupTag number
---@field cdGroupId number
---@field quickUseBtnName string
---@field conditionBombBox table
---@field autoBombBox number
---@field sceneWhiteList table
---@field UseSkillTime number
---@field UseSkillName string
---@field InteractiveAnim table
---@field InteractiveFinishAnim table
---@field beginChantSystemActionEnum table
---@field stopChantSystemActionEnum table
---@field SystemActionEnum table
---@field DropActionEnum table
---@field PickChest table
---@field FashionID number
---@field bindType number
---@field holdMax number
---@field discardAfterFull boolean
---@field mwrap number
---@field ttlType table
---@field ttlExpireTypeStr string
---@field resolvePoint boolean
---@field decomposeItem table
---@field decomposeType 
---@field RecycleWhenLeavePlane table
---@field rarely boolean
---@field TeamRewardAllot number
---@field AuctionMoneyType 
---@field AuctionMinPrice number
---@field AuctionMaxPrice number
---@field DialogueTime number
---@field DialogueId table
---@field TaskIDLimit table
---@field DestroyLimit table
---@field PlayerRotationY string
---@field PlayerRotationSpawner string
---@field UsePlace table
---@field UseNear table
---@field rotateToTarget boolean
---@field tipConsecrationSpecial boolean
---@field IsCheckReward boolean
---@field hrefType number
---@field loopOrBatch boolean
---@field lvPreview number
---@field specialType number
---@field freshFlag boolean
---@field cdServerGroupId number
---@field useAwakenId string
---@field preciousInstanceCriterion number
---@field shopApproachJumpItem number
---@field shopType number
---@field shopItemIndex number
---@field skillTypeReq number
---@field skillLvReq number
---@field socialLvReq number
---@field socProfessionReq number
---@field socProfessionLvReq number
---@field socSkillIdReq number
---@field socSkillLvReq number
---@field itemValue number
---@field itemValue1 number
---@field tipFirstDrop string

---@class _ItemNewData
---@field data _ItemNewDataRow[]
