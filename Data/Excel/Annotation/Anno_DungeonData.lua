---@class _DungeonDataRow
---@field MarkId number
---@field Type 
---@field Name string
---@field EnterPreviewBoss string
---@field EnterPreviewBg string
---@field AtmoPath string
---@field BackGroundPath string
---@field DLvlRule number
---@field MinLvlLimit number
---@field MaxLvlLimit number
---@field RecommendPower number
---@field Desc string
---@field DisplayNumber number
---@field Page number
---@field PagePath string
---@field SelectedPagePath string
---@field RewardDisplay number
---@field AllowSingleMode boolean
---@field StageList table
---@field SingleStagePersonalReward table
---@field StagePersonalReward table
---@field StageTeamReward table
---@field StageRewardPosition table
---@field SingleDungeonPersonalReward table
---@field DungeonPersonalReward table
---@field DungeonTeamReward table
---@field DungeonRewardPosition string
---@field SingleDungeonRewardDisplay table
---@field DungeonRewardDisplay table
---@field RewardDisplayType number
---@field FirstFinishDrop table
---@field SingleDungeonBuffId table
---@field TeamDungeonBuffId table
---@field SettlementInterruptStage number
---@field SingleStartTime string
---@field SingleEndTime string
---@field TeamStartTime string
---@field TeamEndTime string
---@field StartTime number
---@field EndTime number
---@field DurationLimit number
---@field WorldID table
---@field RandomDungeon table
---@field MinPlayerLimit number
---@field MaxPlayerLimit number
---@field TeamLimit table
---@field FirstFinishTeam number
---@field ExitID number
---@field ExitPosition table
---@field ExitScope number
---@field BackToInitial boolean
---@field ExitCountdown number
---@field SettleCountdown number
---@field GameModeID number
---@field GameModeType number
---@field bAllowTemporaryLeave boolean
---@field Target 
---@field DisplaySequence string
---@field FacadeControlID number
---@field NiagaraAssets table

---@class _DungeonData
---@field data _DungeonDataRow[]
