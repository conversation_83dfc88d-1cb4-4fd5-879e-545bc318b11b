---@class _SceneActorTaskCollectDataRow
---@field TemplateID number
---@field ShowName string
---@field TitleName string
---@field HeadInfoOffset number
---@field Mesh string
---@field SkeletalMesh string
---@field MeshScale number
---@field MeshOffsetZ number
---@field NiagaraAsset string
---@field NiagaraAssetScale number
---@field GlowAsset string
---@field GlowAssetScale number
---@field ShowNameRule number
---@field ShowParticleRule number
---@field ShowModelRule number
---@field InitialState number
---@field LevelRequire number
---@field InteractMaxTimes 
---@field InteractCD 
---@field RefreshInterval 
---@field InteractPeriod number
---@field InnerRadius number
---@field InteractRadius number
---@field BtnContent string
---@field BtnIconPath string
---@field ProgressBarTitle string
---@field FaceAngle number
---@field bTurn boolean
---@field TurnAngle number
---@field StartAnimFeature table
---@field EndAnimFeature table
---@field BeginAction table
---@field InterruptAction table
---@field SuccessAction table
---@field InteractorBornAnim string
---@field InteractorInteractAnim string
---@field InteractorSuccessAnim string
---@field SpiritualVisionShowRule number
---@field SpiritualVisionShader number
---@field SpiritualVisionIconType number

---@class _SceneActorTaskCollectData
---@field data _SceneActorTaskCollectDataRow[]
