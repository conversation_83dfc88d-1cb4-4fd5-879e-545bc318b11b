---@class _ExploreRewardDataRow
---@field ID number
---@field InstanceID string
---@field ExploreGroup number
---@field Name string
---@field ExploreClass 
---@field BindJinBang number
---@field BindSuLe number
---@field Reward table
---@field ExploreValue number
---@field RingID number
---@field ExploreAreaID number
---@field IsAbandoned boolean

---@class _ExploreRewardData
---@field data _ExploreRewardDataRow[]
---@field ExploreArea2CollectType2ID ExploreArea2CollectType2ID
---@field ExploreInstanceRewardMap ExploreInstanceRewardMap
---@field ExploreRingRewardMap ExploreRingRewardMap
---@field AbandonedExploreInfo AbandonedExploreInfo
---@field ExploreRewardSumMap ExploreRewardSumMap

---@class ExploreArea2CollectType2ID
---@class ExploreInstanceRewardMap
---@class ExploreRingRewardMap
---@class AbandonedExploreInfo
---@class ExploreRewardSumMap
