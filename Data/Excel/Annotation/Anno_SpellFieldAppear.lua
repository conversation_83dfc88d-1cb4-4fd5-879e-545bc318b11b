---@class _SpellFieldAppearRow
---@field ID number
---@field EffectPath string
---@field FollowType number
---@field BFollowScale boolean
---@field BoneDock string
---@field Offset table
---@field Scale table
---@field Rotation table
---@field EffectPath_EndEffect string
---@field FollowType_EndEffect number
---@field BFollowScale_EndEffect boolean
---@field BoneDock_EndEffect string
---@field Offset_EndEffect table
---@field Scale_EndEffect table
---@field Rotation_EndEffect table
---@field DecalAssetPath string
---@field Rotation_WarnDecal table
---@field Offset__WarnDecal table
---@field DynamicMaterialParams table
---@field DecalSize table
---@field bNeedAttach boolean
---@field BoneDock_WarnDecal string
---@field bAttachRotation_WarnDecal boolean
---@field bAttachScale_WarnDecal boolean
---@field OriginSpecialName string
---@field Translation_WarnDecal table
---@field bNeedCheckGround boolean
---@field GroundCheckObjectTypes table
---@field ExtraGroundMsg table
---@field HitEffectPlayMode number
---@field OnHitFxSoundRotation table
---@field HitEffectPath string
---@field HitSoundPath string
---@field FlashWhite boolean
---@field OnHitShake string
---@field ShakeScale number
---@field IsPlayHealEffect boolean

---@class _SpellFieldAppear
---@field data _SpellFieldAppearRow[]
