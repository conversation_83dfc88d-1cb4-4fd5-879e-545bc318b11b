---@class _MapDataRow
---@field MapID number
---@field LevelID number
---@field PlaneID 
---@field bOpenMap number
---@field LayerID number
---@field OutdoorMapID number
---@field ChangeMapResource string
---@field ChangeMapName string
---@field PlayerPosOff table
---@field bZoomWorldmap boolean
---@field bIndex boolean
---@field MapTexture string
---@field BgTexture string
---@field SubWayTexture string
---@field CamLoc table
---@field CamRot table
---@field CamOrthoWidth number
---@field CamAspectRatio number
---@field MapMaxScale number
---@field MapMinScale number
---@field MiniMapScale number
---@field MapDefaultScale number
---@field MapWidgetSize table
---@field InteractableRegion table
---@field MapBlockPath string
---@field MapBlockSize number
---@field CustomTagNumMax number
---@field ShowTagList table
---@field LayerSwitchHeight number
---@field ShowExploreRate boolean
---@field MainLayerName string
---@field LayersTriggerVolumes_1 string
---@field LayerTexture_1 string
---@field LayerTextureBlocks_1 string
---@field LayerName_1 string
---@field LayersTriggerVolumes_B1 string
---@field LayerTexture_B1 string
---@field LayerTextureBlocks_B1 string
---@field LayerName_B1 string

---@class _MapData
---@field data _MapDataRow[]
