---@class _3V3RankDataRow
---@field ID number
---@field PreRankID number
---@field MajorRan<PERSON> number
---@field SubRank number
---@field Name string
---@field SubName string
---@field SmallRankIconSource string
---@field RequireRank number
---@field RequireRoleRank number
---@field MinScore number
---@field MaxScore number
---@field WinScore number
---@field LoseScore number
---@field WinGainProtectScore number
---@field LoseGainProtectScore number
---@field IsLockScore boolean
---@field MvpExtraScore table
---@field MaxExtraScore number
---@field LimitReward table
---@field LargeRankIconSource string
---@field MatchBotProbability number

---@class _3V3RankData
---@field data _3V3RankDataRow[]
---@field Team33MinRankID Team33MinRankID
---@field Team3v3RankIdToStarMax Team3v3RankIdToStarMax
---@field Max3v3RankPoints Max3v3RankPoints

---@class Team33MinRankID
---@class Team3v3RankIdToStarMax
---@class Max3v3RankPoints
