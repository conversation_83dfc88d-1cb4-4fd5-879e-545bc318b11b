---@class _GossipGroupDataRow
---@field ID number
---@field GroupCondition table
---@field GossipID1 
---@field Kind1 number
---@field Trigger1 string
---@field Condition1 table
---@field CD1 number
---@field GossipID2 
---@field Kind2 number
---@field Trigger2 string
---@field Condition2 table
---@field CD2 number
---@field GossipID3 
---@field Kind3 number
---@field Trigger3 string
---@field Condition3 table
---@field CD3 number

---@class _GossipGroupData
---@field data _GossipGroupDataRow[]
