---@class _EquipmentWordInitRandomGroupDataRow
---@field ID number
---@field Des string
---@field Rarity 
---@field MaxRepeatTime number
---@field Type1_1 number
---@field Type1_2 number
---@field Type1_3 number
---@field Type1_4 number
---@field Type1_5 number
---@field Type1_6 number
---@field Type2_1 number
---@field Type3_1 number
---@field Type3_2 number
---@field Type4_1 number
---@field Type5_1 number
---@field Type5_2 number
---@field Type6_1 number
---@field Type7_1 number
---@field Set_1 number
---@field Set_2 number
---@field Set_3 number
---@field Set_4 number
---@field Set_5 number
---@field Set_6 number
---@field Set_7 number
---@field Set_8 number
---@field Set_9 number
---@field Set_10 number

---@class _EquipmentWordInitRandomGroupData
---@field data _EquipmentWordInitRandomGroupDataRow[]
