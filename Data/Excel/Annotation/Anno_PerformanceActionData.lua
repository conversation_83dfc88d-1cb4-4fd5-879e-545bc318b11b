---@class _PerformanceActionDataRow
---@field ActionID number
---@field Type string
---@field name string
---@field ActorSlot number
---@field Animation table
---@field BackSwingAnimation table
---@field MoveAnimOnLoop string
---@field AttachSocketOnLoop string
---@field AttachActorSlotOnLoop number
---@field UpperBlendID number
---@field bPilot boolean
---@field PilotBiasPos table
---@field PilotBiasYaw number
---@field LoopMode string
---@field bHideWeapon boolean
---@field NaviCmd_bEnableLookAt boolean
---@field NaviCmd_bLockMove number
---@field NaviCmd_bLockJump boolean
---@field NaviCmd_bLockSkill boolean
---@field NaviCmd_bCanBlend boolean
---@field NaviCmd_SingleCarSharingDis number
---@field NaviCmd_NaviMode string
---@field StartCmd_bEnableLookAt boolean
---@field StartCmd_bLockMove number
---@field StartCmd_bLockJump boolean
---@field StartCmd_bLockSkill boolean
---@field StartCmd_bCanBlend boolean
---@field StartCmd_DriveMode string
---@field StartCmd_NaviMode string
---@field LoopCmd_bEnableLookAt boolean
---@field LoopCmd_bLockMove number
---@field LoopCmd_bLockJump boolean
---@field LoopCmd_bLockSkill boolean
---@field LoopCmd_bCanBlend boolean
---@field LoopCmd_DriveMode string
---@field EndCmd_bEnableLookAt boolean
---@field EndCmd_bLockMove number
---@field EndCmd_bLockJump boolean
---@field EndCmd_bLockSkill boolean
---@field EndCmd_bCanBlend boolean
---@field EndCmd_DriveMode string
---@field EndCmd_NaviMode string

---@class _PerformanceActionData
---@field data _PerformanceActionDataRow[]
