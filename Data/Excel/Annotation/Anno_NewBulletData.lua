---@class _NewBulletDataRow
---@field ID number
---@field Desc string
---@field Name string
---@field NewBulletDisc string
---@field NewBulletDiscParam string
---@field MaxLevel number
---@field NewBulletType string
---@field TotalHitTime number
---@field DelayD<PERSON>roy number
---@field PositionSelectionRule table
---@field <PERSON> string
---@field Offset table
---@field LauncherType number
---@field AngularVelocity number
---@field DelayTime number
---@field Velocity number
---@field Acceleration number
---@field MaxLifeTime number
---@field UseMaxLifeTimeAsHitTime boolean
---@field MinVelocity number
---@field CollisionRadius number
---@field DeathInherit boolean
---@field RoundCenterCoordinates table
---@field RoundStartCoordinates table
---@field EffectPath string
---@field FollowType number
---@field BFollowScale boolean
---@field Offset_Effect table
---@field Scale table
---@field Rotation table
---@field TrailEffectPath string
---@field FollowType_TrailEffect number
---@field BFollowScale_TrailEffect boolean
---@field Offset_TrailEffect table
---@field Scale_TrailEffect table
---@field Rotation_TrailEffect table
---@field TrailEffectDelayDestroyTime number
---@field DestroyEffectPath string
---@field BFollowScale_DestroyEffect boolean
---@field Offset_DestroyEffect table
---@field Scale_DestroyEffect table
---@field Rotation_DestroyEffect table
---@field FireAudioID string
---@field FadeAudioID string
---@field BFollowScale_HitEffect boolean
---@field Offset_HitEffect table
---@field Scale_HitEffect table
---@field Rotation_HitEffect table
---@field Model string
---@field HitEffectPlayMode number
---@field OnHitFxSoundRotation table
---@field HitEffectPath string
---@field HitSoundPath string
---@field FlashWhite boolean
---@field OnHitShake string
---@field ShakeScale number
---@field WaterWaveRadius number
---@field WaterWaveParamID number
---@field EffectPriority number

---@class _NewBulletData
---@field data _NewBulletDataRow[]
