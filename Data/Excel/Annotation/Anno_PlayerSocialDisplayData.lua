---@class _PlayerSocialDisplayDataRow
---@field ClassID number
---@field Sex number
---@field ClassName string
---@field PositionType table
---@field ClassLogoHud string
---@field ClassLogo string
---@field ClassLogoBG string
---@field SoloHeadIcon string
---@field <PERSON>HeadIcon string
---@field <PERSON><PERSON>agePath string
---@field FriendRecommend string
---@field HeadIcon string
---@field HeadColor string
---@field SkillIconColor string
---@field StatColor string
---@field Drawing string
---@field InitialSceneCustomIcon string
---@field FriendList string
---@field Card string
---@field SocialSmallIcon string
---@field RolePlayIdleAnim string
---@field NeedArmRotateOffset boolean

---@class _PlayerSocialDisplayData
---@field data _PlayerSocialDisplayDataRow[]
