---@class _NpcInfoDataRow
---@field ID number
---@field Name string
---@field State table
---@field Title string
---@field FunctionID number
---@field Camp 
---@field MonsterID 
---@field FacadeControlID 
---@field FCPath table
---@field DialogueNoticeFC boolean
---@field DialogueBreakLogic boolean
---@field CanInteract number
---@field InteractTitle string
---@field InteractDistance number
---@field TriggerShape number
---@field PlayerInterPos table
---@field TurnToPlayer boolean
---@field FacePlayer boolean
---@field SPInteract table
---@field GossipGroupID 
---@field TalkGroupID number
---@field HeadinfoOffset number
---@field NpcHUDTopDisplay boolean
---@field NpcHideHUDDistance number
---@field NPCHideHUDBlockJudgment number
---@field HideNpcHUDList table
---@field bBlockMainPlayer boolean
---@field CollisionType number
---@field CollisionParam table
---@field Sex number
---@field EnterSkill number
---@field LeaveSkill number
---@field WalkSpeed number
---@field InitialSpeed number
---@field RunSpeed number
---@field SprintSpeed number
---@field ElementEffect table
---@field BornTrap table
---@field TrialNpc number
---@field InitialMood number
---@field BT string
---@field ModelID string
---@field MorphIDList table
---@field SpiritualState table
---@field LocalEntityType string
---@field PossessID 
---@field NpcTalkDistance number

---@class _NpcInfoData
---@field data _NpcInfoDataRow[]
