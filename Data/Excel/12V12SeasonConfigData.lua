--
-- 表名: 12V12SeasonConfigData后处理
--

local TopData = {
    data = {
        [1] = {
            ['ID'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_28176327640576'),
            ['NextSeaonID'] = 2, 
            ['SeasonEndTime'] = '2026.09.12.00.00.00', 
            ['SeasonStartTime'] = '2024.06.11.09.00.00', 
            ['endTimeStamp'] = 1789142400, 
            ['startTimeStamp'] = 1718067600, 
        },
        [2] = {
            ['ID'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_28176327640832'),
            ['NextSeaonID'] = 0, 
            ['SeasonEndTime'] = '2026.12.12.00.00.00', 
            ['SeasonStartTime'] = '2026.09.12.09.00.00', 
            ['endTimeStamp'] = 1797004800, 
            ['startTimeStamp'] = 1789174800, 
        },
    }
}
return TopData