--
-- 表名: CommonInteractorActionData 后处理
--

local TopData = {
    ButtonActionList = {
        [10001040101] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 0, [3] = 700, [4] = Game.TableDataManager:GetLangStr('str_31822218014976'),[9] = '1', }, }, 
        [10001040102] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 300, [3] = 1000, [4] = Game.TableDataManager:GetLangStr('str_31822218015232'),[9] = '2', }, }, 
        [10018010101] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_31822218014976'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', [10] = 1, }, }, 
        [10018010102] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_31822218015232'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', [10] = 2, }, }, 
        [10018010103] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_31822218015488'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', [10] = 3, }, }, 
        [10019010101] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9484361532928'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', }, }, 
        [10028010101] = {['Action'] = 'AddMirrorInteractBtn', ['Param'] = {[1] = 50, [2] = 50, [3] = 50, [4] = 50, [5] = Game.TableDataManager:GetLangStr('str_9691862150145'),[8] = 'front', }, }, 
        [10028010102] = {['Action'] = 'AddMirrorInteractBtn', ['Param'] = {[1] = 50, [2] = 50, [3] = 50, [4] = -50, [5] = Game.TableDataManager:GetLangStr('str_9691862150146'),[8] = 'back', }, }, 
        [40002010101] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 0, [2] = 0, [3] = 400, [4] = Game.TableDataManager:GetLangStr('str_9691862160129'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [7] = {'Alert', 'Start'}, [9] = '2', }, }, 
        [40003010101] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 0, [2] = 0, [3] = 400, [4] = Game.TableDataManager:GetLangStr('str_9691862160641'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [7] = {'Alert', 'Start'}, [9] = '1', }, }, 
        [50001010103] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 0, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862162689'),[9] = '1', }, }, 
        [50001010104] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 0, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862162690'),[9] = '2', }, }, 
        [50001010105] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 0, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_39036957942528'),[9] = '3', }, }, 
        [50002010101] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862163201'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = '1', }, }, 
        [50002010102] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862163203'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = '2', }, }, 
        [50003010104] = {['Action'] = 'AddInteractButton', ['Param'] = {[1] = 0, [2] = 0, [3] = 400, [4] = Game.TableDataManager:GetLangStr('str_9691862163969'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [7] = {'Alert', 'Start'}, [9] = '1', }, }, 
    },
    data = {
        [100010101] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'ShowHeadIcon', ['Param'] = {'/Game/Arts/UI_2/Resource/Trace_2/NotAtlas/UI_Trace_Icon_In.UI_Trace_Icon_In'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10001010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100010101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {85206004, 1, 1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [100010201] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'OpenReminder', ['Param'] = {6440222}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'RemoveHeadIcon', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 20, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP() >= 20', }, 
            ['ConditionEvent'] = {{['EventID'] = 10001020102, ['EventType'] = 10005, }, }, 
            ['ConditionID'] = 100010201, 
            ['ConditionStr'] = 'LEVELUP() >= 20', 
            ['EventList'] = {{['EventID'] = 10001020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100010201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[3] = {['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100010301] = {
            ['ActionTargetList'] = {'P', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 50, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP() >= 50', }, 
            ['ConditionEvent'] = {{['EventID'] = 10001030102, ['EventType'] = 10005, }, }, 
            ['ConditionID'] = 100010301, 
            ['ConditionStr'] = 'LEVELUP() >= 50', 
            ['EventList'] = {{['EventID'] = 10001030101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10004, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100010301, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {85206004, 1, 1}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'FinishInteract', ['Param'] = {1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [100010401] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100010401, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 10001040101, ['Param'] = {[1] = 2, [2] = 0, [3] = 700, [4] = Game.TableDataManager:GetLangStr('str_31822218014976'),[9] = '1', }, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 10001040102, ['Param'] = {[1] = 2, [2] = 300, [3] = 1000, [4] = Game.TableDataManager:GetLangStr('str_31822218015232'),[9] = '2', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100010501] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayPostProcess', ['Param'] = {4000020, 0, 0, 0, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10001050101, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100010501, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100010601] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopPostProcess', ['Param'] = {4000020}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10001060101, ['EventParams'] = {'2'}, ['EventTargetParams'] = {'2'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100010601, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100060101] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {['ConditionInfoList'] = {{['ConditionCmp'] = '==', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'SPIRITUAL_VISION', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, }, {['ConditionCmp'] = '==', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {85206004}, ['FuncName'] = 'HAS_BUFF', ['FuncParamInfos'] = {['buffID'] = 85206004, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, }, }, ['ConditionOp'] = 'And', ['RawInput'] = 'And(SPIRITUAL_VISION()==1, HAS_BUFF(85206004)==1)', }, 
            ['ConditionEvent'] = {{['EventID'] = 10006010102, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, {['EventID'] = 10006010103, ['EventParams'] = 85206004, ['EventTargetParams'] = 85206004, ['EventType'] = 10008, }, }, 
            ['ConditionID'] = 100060101, 
            ['ConditionStr'] = 'AND(SPIRITUAL_VISION()==1, HAS_BUFF(85206004)==1)', 
            ['EventList'] = {{['EventID'] = 10006010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100060101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {89001725, 1, 1}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'testMsg'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100080101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayMusicSound', ['Param'] = {'Play_GamePlay_Trace_Warning'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10008010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100080101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100080102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopSound', ['Param'] = {'Play_GamePlay_Trace_Warning'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10008010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100080102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100090101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000010, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10009010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100090101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100090102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000010}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10009010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100090102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100100101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ReplaceNiagara', ['Param'] = {6000010, 6000009, 0, false}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10010010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100100101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100110101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'AddMeshCollision', ['Param'] = {'10003'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10011010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100110101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100120101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'SetMeshVisible', ['Param'] = {'69000003', false}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10012010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100120101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100120102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'SetMeshVisible', ['Param'] = {'69000003', true}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10012010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100120102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100130101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ReplaceMesh', ['Param'] = {'69000003', 69000004}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10013010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100130101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100140101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000001}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10014010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100140101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100140102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopCommonInteractorAnim', ['Param'] = {10001}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10014010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100140102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100150101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {['ConditionInfoList'] = {{['ConditionInfoList'] = {{['ConditionCmp'] = '==', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'SPIRITUAL_VISION', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, }, {['ConditionCmp'] = '==', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {85206004}, ['FuncName'] = 'HAS_BUFF', ['FuncParamInfos'] = {['buffID'] = 85206004, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, }, }, ['ConditionOp'] = 'And', }, {['ConditionInfoList'] = {{['ConditionCmp'] = '==', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'SPIRITUAL_VISION', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, }, {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 20, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, }, }, ['ConditionOp'] = 'And', }, }, ['ConditionOp'] = 'Or', ['RawInput'] = 'Or(And(SPIRITUAL_VISION()==1,HAS_BUFF(85206004)==1),And(SPIRITUAL_VISION()==1,LEVELUP() >= 20))', }, 
            ['ConditionEvent'] = {{['EventID'] = 10015010102, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, {['EventID'] = 10015010103, ['EventParams'] = 85206004, ['EventTargetParams'] = 85206004, ['EventType'] = 10008, }, {['EventID'] = 10015010104, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, {['EventID'] = 10015010105, ['EventType'] = 10005, }, }, 
            ['ConditionID'] = 100150101, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10015010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100150101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100160101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ShowHeadIcon', ['Param'] = {'/Game/Arts/UI_2/Resource/Trace/Atlas/Texture01/UI_Trace_Icon_Box.UI_Trace_Icon_Box'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10016010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100160101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100160102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'RemoveHeadIcon', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10016010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100160102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100170101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862147073'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10017010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100170101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100180101] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10018010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100180101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 10018010101, ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_31822218014976'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', [10] = 1, }, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 10018010102, ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_31822218015232'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', [10] = 2, }, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 10018010103, ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_31822218015488'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', [10] = 3, }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100190101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10019010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100190101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 10019010101, ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9484361532928'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = 'tag1', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100190102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10019010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100190102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RemoveInteractButton', ['Param'] = {'tag1'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100200101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {5, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100200101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100200102] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ReplaceNiagara', ['Param'] = {5, 6, 0, true}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862148353'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10020010201, ['EventType'] = 1011, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100200102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100200201] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000002}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10020020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10001, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100200201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[4] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 2.8, true}, }, 
        },
        [100210101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10021010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100210101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100220101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10022010101, ['EventParams'] = 'monster', ['EventTargetParams'] = 'monster', ['EventType'] = 10015, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100220101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100230101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'SetClimate', ['Param'] = {2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10023010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100230101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100240101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StartCameraShake', ['Param'] = {71000001, 1, 5}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10024010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100240101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100260101] = {
            ['ActionTargetList'] = {'I', 'P'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000007, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10026010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10017, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100260101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[2] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Gaoyang'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [100280101] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100280101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 2, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddMirrorInteractBtn', ['ButtonID'] = 10028010101, ['Param'] = {[1] = 50, [2] = 50, [3] = 50, [4] = 50, [5] = Game.TableDataManager:GetLangStr('str_9691862150145'),[8] = 'front', }, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'AddMirrorInteractBtn', ['ButtonID'] = 10028010102, ['Param'] = {[1] = 50, [2] = 50, [3] = 50, [4] = -50, [5] = Game.TableDataManager:GetLangStr('str_9691862150146'),[8] = 'back', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100280102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10028010201, ['EventParams'] = {'front'}, ['EventTargetParams'] = {'front'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100280102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'MoveMirror', ['Param'] = {false, 0.75}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100280103] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10028010301, ['EventParams'] = {'back'}, ['EventTargetParams'] = {'back'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100280103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'MoveMirror', ['Param'] = {true, 0.75}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100290101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10029010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100290101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'CompleteWorldActivity', ['Param'] = {5240002}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [100300101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StartDissolve', ['Param'] = {5000004}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10030010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100300101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100310101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10031010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100310101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100320101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {['ConditionCmp'] = '==', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {'3538107852', 1003101, '100310101'}, ['FuncName'] = 'INSTANCE_STATE_BEHAVIOR', ['FuncParamInfos'] = {['instanceActionID'] = '100310101', ['instanceID'] = '3538107852', ['instanceState'] = 1003101, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'INSTANCE_STATE_BEHAVIOR(\"3538107852\",1003101,\"100310101\") == 1', }, 
            ['ConditionEvent'] = {}, 
            ['ConditionID'] = 100320101, 
            ['ConditionStr'] = 'INSTANCE_STATE_BEHAVIOR(3538107852,1003101,100310101) == 1', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100320101, 
            ['IsLoop'] = true, 
            ['NeedConditionTimer'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100330101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'DiceCheckRequst', ['Param'] = {3}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10033010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100330101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100330102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10033010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10013, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100330102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100330103] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10033010301, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10014, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100330103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {360, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100330104] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {1, 3}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10033010401, ['EventParams'] = {'2'}, ['EventTargetParams'] = {'2'}, ['EventType'] = 10014, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100330104, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100340101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'UpdateWonderFlowerState', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100340101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100350101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 100, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {1001}, ['FuncName'] = 'FIRST_LEVEL_AREA_EXPLORATION_PROGRESS', ['FuncParamInfos'] = {['levelId'] = 1001, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'FIRST_LEVEL_AREA_EXPLORATION_PROGRESS(1001) >= 100', }, 
            ['ConditionEvent'] = {{['EventID'] = 10035010101, ['EventType'] = 10012, }, }, 
            ['ConditionID'] = 100350101, 
            ['ConditionStr'] = 'FIRST_LEVEL_AREA_EXPLORATION_PROGRESS(1001) >= 100', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100350101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100360101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10036010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690519976193'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690519976193'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100360101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [100380101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'Open2DGameplay', ['Param'] = {'ShuXin', 6273001}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10038010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100380101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100390101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'Open2DGameplay', ['Param'] = {'XingZuoJieMi', 1008}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10039010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100390101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100400101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'Open2DGameplay', ['Param'] = {'PinTuJieMi', 1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10040010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100400101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [100410101] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000024, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 10041010101, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 100, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 10041010101}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 100410101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[2] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [120010101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ShowHeadIcon', ['Param'] = {'/Game/Arts/UI_2/Resource/Trace/Atlas/Texture01/UI_Trace_Icon_Box.UI_Trace_Icon_Box'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 120010101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [120010102] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'RemoveHeadIcon', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000002}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 12001010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 120010102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[4] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 3, true}, }, 
        },
        [121010101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 12101010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 121010101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'TeleportToPos', ['Param'] = {2460, -2780, 100}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [121010201] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 12101020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 121010201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'Teleport', ['Param'] = {5208001}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [121050101] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000028}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000029}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000027, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 12105010101, ['EventParams'] = {['EnterRangeType'] = 2, ['Radius'] = 500, ['ShapeType'] = 0, }, ['EventTargetParams'] = {2, 12105010101}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 121050101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [121050102] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000027}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000028, 1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 12105010201, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 500, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 12105010201}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 121050102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [121050103] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000028}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000029, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 12105010301, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 500, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 12105010301}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 121050103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [121050104] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'JumpUI', ['Param'] = {1250017}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 12105010401, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 121050104, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200010102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ReplaceNiagara', ['Param'] = {6000008, 6000009, 0, false}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20001010201, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200010102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200010103] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ReplaceNiagara', ['Param'] = {6000009, 6000008, 0, false}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20001010301, ['EventParams'] = {'0'}, ['EventTargetParams'] = {'0'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200010103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200030102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ReplaceNiagara', ['Param'] = {6000010, 6000009, 0, false}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20003010201, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200030102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200030103] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ReplaceNiagara', ['Param'] = {6000009, 6000010, 0, false}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20003010301, ['EventParams'] = {'0'}, ['EventTargetParams'] = {'0'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200030103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200040101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000012, 3}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20004010101, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 75, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 20004010101}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200040101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200040102] = {
            ['ActionTargetList'] = {'P', 'I', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20004010201, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 75, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 20004010201}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200040102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {82040009, 1, 1}, }, ['TargetIsInteractor'] = false, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'TeleportToStartCell', ['Param'] = {}, }, ['TargetIsInteractor'] = false, }, }, 2, true}, }, 
        },
        [200040103] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000010}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20004010301, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200040103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200040104] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000010, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20004010401, ['EventParams'] = {'0'}, ['EventTargetParams'] = {'0'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200040104, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200050101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'OpenLetter', ['Param'] = {'PaintingScratch', 1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20005010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200050101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [200050102] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 20005010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10016, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 200050102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [300010201] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000003}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 300010201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[3] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 6, true}, }, 
        },
        [300020201] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000005}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 300020201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [300020202] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000007}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 300020202, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [300030101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000006}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 300030101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [300030102] = {
            ['ActionTargetList'] = {}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 30003010201, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690519982081'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690519982081'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 300030102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [300030201] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000011}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000019, 0}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ChangeMaterial', ['Param'] = {[1] = 1, [3] = 0, [5] = '/Game/Arts/Effects/FX_Special/FX_GrassGrow/MI_FlowerpotGrow.MI_FlowerpotGrow', }, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ChangeMaterialParam', ['Param'] = {[1] = 1, [3] = 0, [5] = 1, }, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[6] = {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000012}, }, ['TargetIsInteractor'] = true, }, }, 3.5, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 300030201, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [301000001] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeMaterial', ['Param'] = {[1] = 1, [3] = 0, [5] = '/Game/Arts/Environment/Mesh/Building/SM_MirrorWorld/Material/MI_SemiTransparent01_Inst.MI_SemiTransparent01_Inst', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 301000001, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [400010101] = {
            ['ActionTargetList'] = {'P', 'I', 'P', 'I', 'I'}, 
            ['ClientAction'] = {{{[3] = {['Action'] = {['Action'] = 'OpenPOIPhoto', ['Param'] = {100001}, }, ['TargetIsInteractor'] = false, }, }, 2, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 40001010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400010101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'CameraLookAtPosition', ['Param'] = {{-1819, 4672, 670}, 1, 2, true, false}, }, ['TargetIsInteractor'] = false, }, }, 0, false}, {{[5] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 4, true}, }, 
        },
        [400020101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400020101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 40002010101, ['Param'] = {[1] = 0, [2] = 0, [3] = 400, [4] = Game.TableDataManager:GetLangStr('str_9691862160129'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [7] = {'Alert', 'Start'}, [9] = '2', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [400020102] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeMaterialParam', ['Param'] = {[1] = 1, [3] = 0, [5] = 2, }, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000019, -1}, }, ['TargetIsInteractor'] = true, }, }, 3, false}, {{[5] = {['Action'] = {['Action'] = 'ReplaceMesh', ['Param'] = {'69000024', 69000023}, }, ['TargetIsInteractor'] = true, }, [6] = {['Action'] = {['Action'] = 'ChangeMaterialParam', ['Param'] = {[1] = 1, [3] = 0, [5] = 3, }, }, ['TargetIsInteractor'] = true, }, }, 5, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 40002010201, ['EventParams'] = {'2'}, ['EventTargetParams'] = {'2'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400020102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{}, 8, true}, }, 
        },
        [400030101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400030101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 40003010101, ['Param'] = {[1] = 0, [2] = 0, [3] = 400, [4] = Game.TableDataManager:GetLangStr('str_9691862160641'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [7] = {'Alert', 'Start'}, [9] = '1', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [400030102] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeMaterialParam', ['Param'] = {[1] = 1, [3] = 0, [5] = 2, }, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000020, -1}, }, ['TargetIsInteractor'] = true, }, }, 3, false}, {{[5] = {['Action'] = {['Action'] = 'ReplaceMesh', ['Param'] = {'69000023', 69000024}, }, ['TargetIsInteractor'] = true, }, [6] = {['Action'] = {['Action'] = 'ChangeMaterialParam', ['Param'] = {[1] = 1, [3] = 0, [5] = 3, }, }, ['TargetIsInteractor'] = true, }, }, 5, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 40003010201, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400030102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{}, 8, true}, }, 
        },
        [400040101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400040101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'PlayLevelSequenceAction', ['Param'] = {1000058, false, 1, 0, 2}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [400040102] = {
            ['ActionTargetList'] = {}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 40004010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400040102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [400040201] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400040201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'PlayLevelSequenceAction', ['Param'] = {1000058, false, 0, 2, 1800}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [400050101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400050101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'PlayLevelSequenceAction', ['Param'] = {1000059, false, 1, 0, 2}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [400050102] = {
            ['ActionTargetList'] = {}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 40005010201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400050102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [400050201] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 400050201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'PlayLevelSequenceAction', ['Param'] = {1000059, false, 0, 2, 270}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [500010101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000004}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ChangeMaterial', ['Param'] = {[1] = 1, [3] = 0, [5] = '/Game/Arts/Effects/FX_Character/Boss_ZJFR/ZhiNianTi/Material/MI_ZJFR_ZNT_glow.MI_ZJFR_ZNT_glow', }, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500010101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[3] = {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 50001010103, ['Param'] = {[1] = 2, [2] = 0, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862162689'),[9] = '1', }, }, ['TargetIsInteractor'] = true, }, [4] = {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 50001010104, ['Param'] = {[1] = 2, [2] = 0, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862162690'),[9] = '2', }, }, ['TargetIsInteractor'] = true, }, [5] = {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 50001010105, ['Param'] = {[1] = 2, [2] = 0, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_39036957942528'),[9] = '3', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [500010102] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500010102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {89001725, 1, 1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [500020101] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500020101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 50002010101, ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862163201'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = '1', }, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 50002010102, ['Param'] = {[1] = 2, [2] = 100, [3] = 500, [4] = Game.TableDataManager:GetLangStr('str_9691862163203'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [6] = Game.TableDataManager:GetLangStr('str_9691862147330'),[7] = {'Alert', 'Start'}, [9] = '2', }, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [500020102] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50002010201, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500020102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'Say', ['Param'] = {2, Game.TableDataManager:GetLangStr('str_9691862163457'),5, 'nil', 'nil', 'nil'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [500020103] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50002010301, ['EventParams'] = {'2'}, ['EventTargetParams'] = {'2'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 3, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500020103, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {89001725, 1, 10}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [500030101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000008}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[5] = {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000009}, }, ['TargetIsInteractor'] = true, }, }, 2.5, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500030101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Head'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[4] = {['Action'] = {['Action'] = 'AddInteractButton', ['ButtonID'] = 50003010104, ['Param'] = {[1] = 0, [2] = 0, [3] = 400, [4] = Game.TableDataManager:GetLangStr('str_9691862163969'),[5] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_MComplete_Sprite.UI_NPC_Icon_MComplete_Sprite', [7] = {'Alert', 'Start'}, [9] = '1', }, }, ['TargetIsInteractor'] = true, }, }, 2.5, true}, }, 
        },
        [500030102] = {
            ['ActionTargetList'] = {}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50003010201, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500030102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [500030103] = {
            ['ActionTargetList'] = {}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50003010301, ['EventParams'] = {'2'}, ['EventTargetParams'] = {'2'}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500030103, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [500030201] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'P', 'I', 'I'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000010}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[4] = {['Action'] = {['Action'] = 'PlayPostProcess', ['Param'] = {4000022, 0, 0, -1, 0}, }, ['TargetIsInteractor'] = false, }, }, 0.6, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500030201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'GameStart'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[6] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 1.23, true}, }, 
        },
        [500030301] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500030301, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Treasure'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [500040101] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50004010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500040101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Morph'}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [500050101] = {
            ['ActionTargetList'] = {'I', 'I', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50005010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500050101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 1}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Statue1Rotate'}, }, ['TargetIsInteractor'] = false, }, }, 1, true}, }, 
        },
        [500060101] = {
            ['ActionTargetList'] = {'I', 'I', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50006010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500060101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {-180, 1}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Statue2Rotate'}, }, ['TargetIsInteractor'] = false, }, }, 1, true}, }, 
        },
        [500070101] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50007010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500070101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Eat'}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [500080101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000013}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500080101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [500080201] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500080201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [500090101] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopPostProcess', ['Param'] = {4000022}, }, ['TargetIsInteractor'] = false, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50009010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 500090101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[2] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Finish'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [501000101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'SetMeshVisible', ['Param'] = {'69000017', false}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50100010101, ['EventParams'] = {'0'}, ['EventTargetParams'] = {'0'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501000101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501000102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'SetMeshVisible', ['Param'] = {'69000017', true}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50100010201, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501000102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501000103] = {
            ['ActionTargetList'] = {'P', 'I', 'P'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'SetMeshVisible', ['Param'] = {'false'}, }, ['TargetIsInteractor'] = true, }, [3] = {['Action'] = {['Action'] = 'OpenReminder', ['Param'] = {6406562}, }, ['TargetIsInteractor'] = false, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50100010301, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501000103, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddSkillToSlot', ['Param'] = {80009142, 11}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [501100101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000012, 3}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501100101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501100102] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000012}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862171905'),2, 20}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ShowHeadIcon', ['Param'] = {'/Game/Arts/UI_2/Resource/Trace/Atlas/Texture01/UI_Trace_Icon_Box.UI_Trace_Icon_Box'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50110010201, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501100102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501100201] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000002}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'RemoveHeadIcon', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50110020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501100201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501110101] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862172417'),2, 20}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ShowHeadIcon', ['Param'] = {'/Game/Arts/UI_2/Resource/Trace/Atlas/Texture01/UI_Trace_Icon_Box.UI_Trace_Icon_Box'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50111010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501110101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501120101] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000012}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'ShowHeadIcon', ['Param'] = {'/Game/Arts/UI_2/Resource/Trace/Atlas/Texture01/UI_Trace_Icon_Box.UI_Trace_Icon_Box'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50112010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501120101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501120201] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000002}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50112020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501120201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[3] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 3, true}, }, 
        },
        [501120202] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000012, 0}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'RemoveHeadIcon', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50112020201, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9552812599552'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9552812599552'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501120202, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501130101] = {
            ['ActionTargetList'] = {'I', 'I', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50113010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501130101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 1}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Statue3Rotate'}, }, ['TargetIsInteractor'] = false, }, }, 1, true}, }, 
        },
        [501140101] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000010, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50114010101, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 75, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 50114010101}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501140101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Enter1'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [501140102] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000010}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50114010201, ['EventParams'] = {['EnterRangeType'] = 2, ['Radius'] = 75, ['ShapeType'] = 0, }, ['EventTargetParams'] = {2, 50114010201}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501140102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Leave1'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [501140103] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000009, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50114010301, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501140103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501140104] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000009}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50114010401, ['EventParams'] = {'0'}, ['EventTargetParams'] = {'0'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501140104, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501150101] = {
            ['ActionTargetList'] = {'P', 'I', 'I', 'P'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000003}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50115010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501150101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Destroy'}, }, ['TargetIsInteractor'] = false, }, }, 0, false}, {{[4] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Repair'}, }, ['TargetIsInteractor'] = false, }, }, 5.5, true}, }, 
        },
        [501160101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000014}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50116010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501160101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501170101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50117010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690519997953'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690519997953'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501170101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {180, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [501170102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50117010201, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690519998209'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690519998209'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501170102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {0, 2}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [501180101] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'SetMeshVisible', ['Param'] = {'69000004', false}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'RemoveMeshCollision', ['Param'] = {'69000004'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_53396107173376'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_53396107173376'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501180102] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000010, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118010201, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 75, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 50118010201}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180102, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Enter1'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [501180103] = {
            ['ActionTargetList'] = {'P', 'I'}, 
            ['ClientAction'] = {{{[2] = {['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000010}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118010301, ['EventParams'] = {['EnterRangeType'] = 2, ['Radius'] = 75, ['ShapeType'] = 0, }, ['EventTargetParams'] = {2, 50118010301}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180103, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Leave1'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [501180104] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000009, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118010401, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180104, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501180105] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000009}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118010501, ['EventParams'] = {'0'}, ['EventTargetParams'] = {'0'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180105, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501180201] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'SetMeshVisible', ['Param'] = {'69000004', true}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'AddMeshCollision', ['Param'] = {'69000004'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118020101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520002049'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520002049'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180201, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501180202] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000009, -1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118020201, ['EventParams'] = {'1'}, ['EventTargetParams'] = {'1'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180202, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [501180203] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000009}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50118020301, ['EventParams'] = {'0'}, ['EventTargetParams'] = {'0'}, ['EventType'] = 10007, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 501180203, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [502000101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50200010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502000101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [502030101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'RemoveHeadIcon', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000002}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50203010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502030101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[4] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 3, true}, }, 
        },
        [502040101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50204010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_29756607198208'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_29756607198208'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502040101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [502050101] = {
            ['ActionTargetList'] = {'I', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50205010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520006401'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520006401'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502050101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayLevelSequenceAction', ['Param'] = {1000040, true, 1, 0, 20}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [502060101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50206010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502060101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'TeleportToActor', ['Param'] = {'1596207878', false, false, 0}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [502070101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50207010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502070101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'TeleportToActor', ['Param'] = {'2279678905', false, false, 0}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [502080101] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50208010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502080101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'Picked'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0.2, true}, }, 
        },
        [502090101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50209010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502090101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'unlock'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [502100101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000026, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50210010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502100101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[2] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'playseq1'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[4] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0.2, true}, }, 
        },
        [502110101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000026, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50211010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502110101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[2] = {['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'playseq2'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[4] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0.2, true}, }, 
        },
        [502120101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50212010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502120101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'pipeStage2'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [502120201] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50212020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502120201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'pipeStage3'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [502120301] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50212030101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502120301, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'pipeBroken'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0.5, true}, }, 
        },
        [502130101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ShowHeadIcon', ['Param'] = {'/Game/Arts/UI_2/Resource/Trace/Atlas/Texture01/UI_Trace_Icon_Box.UI_Trace_Icon_Box'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50213010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_34292092641280'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502130101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [502130201] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'RemoveHeadIcon', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayCommonInteractorAnim', ['Param'] = {70000002}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50213020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502130201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[4] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 3, true}, }, 
        },
        [502140101] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50214010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502140101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'BoxDrop'}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, {{[3] = {['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0.5, true}, }, 
        },
        [502150101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50215010101, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9488656505088'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9488656505088'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502150101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'RotateCommonInteractor', ['Param'] = {110, 1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [502160101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50216010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502160101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'TeleportToActor', ['Param'] = {'1596207878', false, false, 0}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [502170101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 50217010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 502170101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'TeleportToActor', ['Param'] = {'2279678905', false, false, 0}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600010101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60001010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600010101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'245820511'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600020101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60002010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600020101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'1106127979'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600030101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60003010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600030101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'3389014679'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600040101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60004010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600040101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'2545715413'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600050101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60005010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600050101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'789348375'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600060101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60006010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600060101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'3023151543'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600070101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60007010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600070101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'%s'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600080101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60008010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600080101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'GuildLeagueTeleport', ['Param'] = {'%s'}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600090101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60009010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600090101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'StartMorph', ['Param'] = {2809103}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [600100101] = {
            ['ActionTargetList'] = {'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60010010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 600100101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'StartMorph', ['Param'] = {2809103}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [601100101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000025, 8}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000036, 8}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000037, 8}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000038, 8}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60110010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 601100101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [601110101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000032, 3.42}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000039, 3.42}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000040, 3.42}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000041, 3.42}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60111010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 601110101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [601120101] = {
            ['ActionTargetList'] = {'I', 'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000033, 4}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000042, 4}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000043, 4}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000044, 4}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60112010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 601120101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [601130101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000034, 1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60113010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 601130101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [601140101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000035, 10}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60114010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 601140101, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602010101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60201010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602010101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'StartExplore'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [602020101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60202010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602020101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'CloseSource'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [602030101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'ReceiverTrigger'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [602030102] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862180865'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203010201, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520003585'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520003585'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030102, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030103] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862181121'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203010301, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520003841'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520003841'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030103, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030104] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862181377'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203010401, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520004097'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520004097'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030104, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030201] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203020101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030201, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'ReceiverTrigger'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [602030202] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862181121'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203020201, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520003841'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520003841'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030202, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030203] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862181377'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203020301, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520004097'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520004097'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030203, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030204] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000021, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030204, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030301] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203030101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030301, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'SendFlowchartMessageForAll', ['Param'] = {'ReceiverTrigger'}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [602030302] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'ChangeDisplayName', ['Param'] = {Game.TableDataManager:GetLangStr('str_9691862181377'),2, 20}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 60203030201, ['EventParams'] = {Game.TableDataManager:GetLangStr('str_9690520004097'),}, ['EventTargetParams'] = {Game.TableDataManager:GetLangStr('str_9690520004097'),}, ['EventType'] = 1009, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030302, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030303] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000022, 0}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000021}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030303, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [602030401] = {
            ['ActionTargetList'] = {'I', 'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {6000023, 0}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000022}, }, ['TargetIsInteractor'] = true, }, {['Action'] = {['Action'] = 'StopNiagara', ['Param'] = {6000021}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {}, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 602030401, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {}, 
        },
        [610010101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 61001010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10004, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 610010101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [610020101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 61002010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10004, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 610020101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [610030101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 61003010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10004, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 610030101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [610040101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 61004010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10004, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 610040101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [610050101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 61005010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10004, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 610050101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [610060101] = {
            ['ActionTargetList'] = {'I'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 61006010101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10004, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 610060101, 
            ['IsLoop'] = false, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DestroyCommonInteractor', ['Param'] = {}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [999910001] = {
            ['ActionTargetList'] = {'P', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 99991000101, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 6442070, 
            ['ID'] = 999910001, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {43, 1, 1}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'FinishInteract', ['Param'] = {1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [999910002] = {
            ['ActionTargetList'] = {'P', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 99991000201, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 999910002, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DelBuff', ['Param'] = {82030006, 1}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'FinishInteract', ['Param'] = {1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [999910003] = {
            ['ActionTargetList'] = {'I', 'I'}, 
            ['ClientAction'] = {{{{['Action'] = {['Action'] = 'PlayNiagara', ['Param'] = {1000009, 0}, }, ['TargetIsInteractor'] = true, }, }, 0, false}, }, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 99991000301, ['EventParams'] = {}, ['EventTargetParams'] = {}, ['EventType'] = 10003, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 999910003, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{[2] = {['Action'] = {['Action'] = 'FinishInteract', ['Param'] = {1}, }, ['TargetIsInteractor'] = true, }, }, 0, true}, }, 
        },
        [999910004] = {
            ['ActionTargetList'] = {'P', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 99991000401, ['EventParams'] = {['EnterRangeType'] = 1, ['Radius'] = 500, ['ShapeType'] = 0, }, ['EventTargetParams'] = {1, 99991000401}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 999910004, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'AddBuff', ['Param'] = {85000109, 1, 1}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'FinishInteract', ['Param'] = {1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
        [999910005] = {
            ['ActionTargetList'] = {'P', 'P'}, 
            ['ClientAction'] = {}, 
            ['ConditionAst'] = {}, 
            ['ConditionStr'] = '', 
            ['EventList'] = {{['EventID'] = 99991000501, ['EventParams'] = {['EnterRangeType'] = 2, ['Radius'] = 500, ['ShapeType'] = 0, }, ['EventTargetParams'] = {2, 99991000501}, ['EventType'] = 10006, }, }, 
            ['FailedCD'] = 0, 
            ['FailedReminder'] = 0, 
            ['ID'] = 999910005, 
            ['IsLoop'] = true, 
            ['RecoverType'] = 0, 
            ['ServerAction'] = {{{{['Action'] = {['Action'] = 'DelBuff', ['Param'] = {85000109, 1}, }, ['TargetIsInteractor'] = false, }, {['Action'] = {['Action'] = 'FinishInteract', ['Param'] = {1}, }, ['TargetIsInteractor'] = false, }, }, 0, true}, }, 
        },
    }
}
return TopData