--
-- 表名: $PVPBase_PVP玩法配置表.xlsx  页名：$Setting_12V12_12V12常量
--

local TopData = {
	data = {
		["OccupyAreaScore"] = 80,
		["MatchPunishBaseTime"] = 30,
		["MatchPunishScale"] = 10,
		["ContinusKillMinNum"] = 3,
		["ContinusKillShowTime"] = 1500,
		["ReminderKillNumPath0"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp0_Sprite.UI_HUD_Com_Img_Pvp0_Sprite",
		["ReminderKillNumPath1"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp1_Sprite.UI_HUD_Com_Img_Pvp1_Sprite",
		["ReminderKillNumPath2"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp2_Sprite.UI_HUD_Com_Img_Pvp2_Sprite",
		["ReminderKillNumPath3"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp3_Sprite.UI_HUD_Com_Img_Pvp3_Sprite",
		["ReminderKillNumPath4"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp4_Sprite.UI_HUD_Com_Img_Pvp4_Sprite",
		["ReminderKillNumPath5"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp5_Sprite.UI_HUD_Com_Img_Pvp5_Sprite",
		["ReminderKillNumPath6"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp6_Sprite.UI_HUD_Com_Img_Pvp6_Sprite",
		["ReminderKillNumPath7"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp7_Sprite.UI_HUD_Com_Img_Pvp7_Sprite",
		["ReminderKillNumPath8"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp8_Sprite.UI_HUD_Com_Img_Pvp8_Sprite",
		["ReminderKillNumPath9"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Com_Img_Pvp9_Sprite.UI_HUD_Com_Img_Pvp9_Sprite",
		["BattleStatsTab1"] = Game.TableDataManager:GetLangStr('str_42332271417600'),
		["BattleStatsTab2"] = Game.TableDataManager:GetLangStr('str_42332271417856'),
		["AnimationSpeed"] = 100,
		["KillAnimationSpeed"] = 200,
		["BonusPointTimePeriod"] = {{{12, 0}, {14, 0}}, {{20, 0}, {22, 0}}},
		["BonusPointTimeRate"] = 0.1,
		["AutoStartNextGame"] = 10,
		["OccupyIconHeight"] = 400,
		["OccupyShapeTriggerMaxProgress"] = 100,
		["OccupyShapeTriggerScore"] = 15,
		["OccupyShapeTriggerBuff"] = 89001108,
		["SettleTitleName"] = {Game.TableDataManager:GetLangStr('str_42332271429377'), Game.TableDataManager:GetLangStr('str_42332271429378')},
	},
}

return TopData
