--
-- 表名: $RedName_红名表.xlsx  页名：$CrimeConstString_字符串常量
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Enum"] = "PEACE_MODE",
			["Name"] = Game.TableDataManager:GetLangStr('str_40957881879041'),
			["Color"] = {},
			["Desc"] = Game.TableDataManager:GetLangStr('str_44944148399616'),
			["TagIcon1"] = "/HUD/Atlas/HUD_BattleMode/Sprite01/UI_HUD_BattleMode_Icon_SanityModeComColor_Sprite.UI_HUD_BattleMode_Icon_SanityModeComColor_Sprite",
			["TagIcon2"] = "/HUD/Atlas/HUD_BattleMode/Sprite01/UI_HUD_BattleMode_Icon_SanityMode_Sprite.UI_HUD_BattleMode_Icon_SanityMode_Sprite",
		},
		[2] = {
			["ID"] = 2,
			["Enum"] = "TRIAL_MODE",
			["Name"] = Game.TableDataManager:GetLangStr('str_44943611528960'),
			["Color"] = {},
			["Desc"] = Game.TableDataManager:GetLangStr('str_44944148399872'),
			["TagIcon1"] = "/HUD/Atlas/HUD_BattleMode/Sprite01/UI_HUD_BattleMode_Icon_HuntModeComColor_Sprite.UI_HUD_BattleMode_Icon_HuntModeComColor_Sprite",
			["TagIcon2"] = "/HUD/Atlas/HUD_BattleMode/Sprite01/UI_HUD_BattleMode_Icon_HuntMode_Sprite.UI_HUD_BattleMode_Icon_HuntMode_Sprite",
		},
		[3] = {
			["ID"] = 3,
			["Enum"] = "FIGHT_MODE",
			["Name"] = Game.TableDataManager:GetLangStr('str_40957881879043'),
			["Color"] = {},
			["Desc"] = Game.TableDataManager:GetLangStr('str_44944148400128'),
			["TagIcon1"] = "/HUD/Atlas/HUD_BattleMode/Sprite01/UI_HUD_BattleMode_Icon_CrazyModeComColor_Sprite.UI_HUD_BattleMode_Icon_CrazyModeComColor_Sprite",
			["TagIcon2"] = "/HUD/Atlas/HUD_BattleMode/Sprite01/UI_HUD_BattleMode_Icon_CrazyMode_Sprite.UI_HUD_BattleMode_Icon_CrazyMode_Sprite",
		},
	},
}

return TopData
