BSS = BSS or {}


-- 用于储存碰撞信息
BSS.HitResult = {
    __call = function(_)
        local Table = setmetatable({ComponentGID = 0, ActorGID = 0, Location = M3D.Vec3(), Type = "HitResult"}, BSS.HitResult)
        
        return Table
    end,
  
    __tostring = function(V)
        return string.format("HitLocation=(%.3f, %.3f, %.3f)", Location.X, Location.Y, Location.Z)
    end,

    __eq = function(HR1, HR2)
        if (HR1.Type ~= "HitResult") or (HR2.Type ~= "HitResult") then
            Log.Debug("HitResult Equal Failed, Invalid Parameter !")
            return
        end

        return (HR1.ComponentGID == HR2.ComponentGID) and (HR1.Location == HR2.Location)
    end,

    __index = {
        Reset = function(HR)
            if (HR.Type ~= "HitResult") then
                Log.Debug("HitResult Reset Failed, Invalid Parameter !")
                return
            end

            HR.ActorGID = 0
            HR.ComponentGID = 0
            HR.Location:Reset(LX, LY, LZ)
        end
    }
}
setmetatable(BSS.HitResult, BSS.HitResult)
