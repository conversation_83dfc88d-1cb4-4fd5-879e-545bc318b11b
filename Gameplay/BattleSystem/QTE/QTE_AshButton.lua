--
-- DESCRIPTION
--
-- @COMPANY **
-- @AUTHOR **
-- @DATE ${date} ${time}
--

-- 防止重复创建同名类
if (_G.QTE_AshButton ~= nil) then
    return _G.QTE_AshButton
end

--处理疯狂点击的Qte玩法
local QTE_AshButton = DefineClass("QTE_AshButton", QTE_Base)

-- region Important
function QTE_AshButton:QTEInit(InData)
    QTE_Base.QTEInit(self, InData)

    -- 成功完成QTE需要消除按钮的个数
    -- self.Data.SuccessPressBtnNum

    -- 当前已完成的按钮个数
    self.CurrentFinishedBtnNum = 0
end

function QTE_AshButton:Start()
    QTE_Base.Start(self)
    self.TimeOutTimer = Game.TimerManager:CreateTimerAndStart(function() self:Failed()  end, self.Data.Duration * 1000, 1)
end

function QTE_AshButton:Update(InDeltaTime)
    QTE_Base.Update(self, InDeltaTime)

    --if (self.RunningTime > self.Data.Duration) then
    --    self:Failed()
    --end
end

function QTE_AshButton:Reset()
    QTE_Base.Reset(self)

    self.CurrentFinishedBtnNum = 0
end

function QTE_AshButton:End()
    QTE_Base.End(self)

    if self.TimeOutTimer then
        Game.TimerManager:StopTimerAndKill(self.TimeOutTimer)
        self.TimeOutTimer = nil
    end

    self.CurrentFinishedBtnNum = nil
end
-- endregion Important



-- region API
function QTE_AshButton:Sucessed()
    if (self.bActive == false) then
        return
    end

    self.CurrentFinishedBtnNum = self.CurrentFinishedBtnNum + 1
    if self.CurrentFinishedBtnNum >= self.Data.SuccessPressBtnNum then
        self.QTESucessed:Broadcast(0)
    end

    if self:NeedFinish() then
        Game.BSQTEManager:DestroyQTEObject(self)
    end
end

function QTE_AshButton:Failed()
    self.QTEFailed:Broadcast()

    self:Reset()

    if self:NeedFinish() then
        Game.BSQTEManager:DestroyQTEObject(self)
    end
end
-- endregion API



return QTE_AshButton