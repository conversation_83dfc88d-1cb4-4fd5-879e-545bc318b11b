local EPropertyClass = import("EPropertyClass")

require("Data.Config.BattleSystem.ExportedEnum")
require("Gameplay.BattleSystem.Editor.E_ExportFunctions")



local E_SimpleGameInstance = {}

function E_SimpleGameInstance:OnLuaBegin(InWorld)
    Game = {}
    Game.GameInstance = self
    Game.WorldContext = InWorld
    _G.ContextObject = InWorld

    --require("Tools.LuaPanda").start("127.0.0.1", 8818)

    -- 加载环境
    require("Editor.EditorLuaEnvironment")

    -- 技能后处理
    Game.AbilityPostExport = require("Tools.AbilityPostExport.loader")
end

function E_SimpleGameInstance:OnLuaEnd()
end

function E_SimpleGameInstance:ConvertJsonToLuaTable(InJson, InID)
    return SerializeJson(InJson, InID)
end

function E_SimpleGameInstance:ExtractFightProp(InString, Name)
    local TableFunc, msg = load(InString)

    local Table = TableFunc()

    if (Table ~= nil) and (Table.data ~= nil) then
        local OutRes = slua.Array(EPropertyClass.Str)
        for _, V in pairs(Table.data) do
            if (V[Name] ~= nil) then
                OutRes:Add(Name .. "." .. V[Name])
            end
        end

        return OutRes
    end

    return slua.Array(EPropertyClass.Str)
end

return Class(nil, nil, E_SimpleGameInstance)
