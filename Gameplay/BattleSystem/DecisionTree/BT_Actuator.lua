-- 防止重复创建同名类
if (_G.BT_Actuator ~= nil) then
    return _G.BT_Actuator
end

local BT_Actuator = DefineClass("BT_Actuator")

function BT_Actuator:GetBestTreeNode(InStData, InDyData)
    local Result = nil

	if (InStData == nil) or (InDyData == nil) then
		return Result
    end

	self.StData = InStData
	self.DyData = InDyData

	for I, V in pairs(InStData.RootNodes) do
		local CurNode = InStData.Nodes[V]
		Result = self:TravelDecisionTree(CurNode)
		-- 找到结果，中断循环
		if (Result ~= nil) then
			break
		end
	end

	return Result
end

function BT_Actuator:TravelDecisionTree(InNode)
	if (InNode == nil) then
		return nil
	end

	-- 节点的条件检查
	if (BSFunc.ExeBSACondition(InNode.ConditionGroup, self.DyData) == false) then
		return nil
	end

	-- 遍历该节点的出边
	for I, V in pairs(InNode.OutEdges) do
		local CurEdge = self.StData.Edges[V]
		-- 检查边的条件
		if (BSFunc.ExeBSACondition(CurEdge.ConditionGroup, self.DyData) == true) then
			local Result = self:TravelDecisionTree(self.StData.Nodes[CurEdge.EndNode])
			if (Result ~= nil) then
				return Result
			end
		end
	end

	return InNode
end

return BT_Actuator
