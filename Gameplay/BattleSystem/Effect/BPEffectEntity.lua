-- local EAttachmentRule = import("EAttachmentRule")
-- local ActorUtil = import("KGActorUtil")
local UBSFunc = import("BSFunctionLibrary")
local EARKeepRelative = import("EAttachmentRule").KeepRelative


local BPEffectEntity = DefineLocalEntity("BPEffectEntity", LocalEntityBase, {
	NewViewControlMaterialComponent,
	ViewControlBaseComponent,
	})

BPEffectEntity.INIT_MATERIAL_CACHE_USE_MATERIAL_ON_COMPONENT = true

BPEffectEntity:Register("BPEffectEntity")

function BPEffectEntity:ctor()
	self.spawnID = -1
	self.blueprintName = nil

	self.bStickGround = nil
	self.Transform = M3D.Transform()

	self.bNeedAttach = nil
	self.AttachSocket = nil

	self.identifiedName = nil
end

function BPEffectEntity:Init(BulletConfig)
	self.spawnID = BulletConfig.spawnID
	self.blueprintName = BulletConfig.blueprintName

	self.bStickGround = BulletConfig.bStickGround
	self.Transform = BulletConfig.transform

	self.bNeedAttach = BulletConfig.bNeedAttach or false
	self.AttachSocket = BulletConfig.attachSocket

	self.identifiedName = BulletConfig.identifiedName
end

function BPEffectEntity:GetActorBPClassPath()
	return self.blueprintName
end

function BPEffectEntity:GetCreateActorLocation()
	local rotation = M3D.Rotator()
	if self.bNeedAttach then
		self.Transform.Rotation:ToRotator(rotation)
		return self.Transform.Translation, rotation
	end
	local spawner = Game.EntityManager:getEntity(self.spawnID)
	local spawnerTrans = M3D.Transform()
	spawnerTrans:Pack(spawner:GetTransform_P())
	-- 计算相对坐标
	spawnerTrans:Mul(self.Transform, spawnerTrans)
	local translation = spawnerTrans.Translation
	spawnerTrans.Rotation:ToRotator(rotation)
	-- 等接口支持后替换
	if self.bStickGround then
		local FindGround, X, Y, Z = UBSFunc.FindGroundLocation_P(spawner.CharacterID, spawner.CharacterID, translation.X, translation.Y,
			translation.Z, 1, -100.0, 1000.0, 0.4, M3D.Fill3())
		if (FindGround == true) then
			translation.X = X
			translation.Y = Y
			translation.Z = Z
		end
	end
	return translation, rotation
end


function BPEffectEntity:AfterEnterWorld()
	LocalEntityBase.AfterEnterWorld(self)
	if self.bNeedAttach then
		local spawner = Game.EntityManager:getEntity(self.spawnID)
		local rootCompID = spawner.CppEntity:KAPI_Actor_GetRootComponent()
		self.CppEntity:KAPI_Actor_AttachToComponent(rootCompID, self.AttachSocket, EARKeepRelative, EARKeepRelative, EARKeepRelative, true)
	end
end


function BPEffectEntity:BeforeExitWorld()
	if self.bNeedAttach then
		self.CppEntity:KAPI_Actor_K2_DetachFromActor(EARKeepRelative, EARKeepRelative, EARKeepRelative)
	end
	LocalEntityBase.BeforeExitWorld(self)
end

return BPEffectEntity