local AbilityConst = kg_require("Shared.Const.AbilityConst")
local ENewBulletType = AbilityConst.ENewBulletType
local LUC = kg_require("Shared.Const.LogicUnitConst")
local EBulletOffsetMode = AbilityConst.EBulletOffsetMode
local EBulletLauncherType = AbilityConst.EBulletLauncherType

-- luacheck: push ignore
local EBulletTypeInverseMap = {}
local GenMapFunc = function()
	for k, v in pairs(ENewBulletType) do
		EBulletTypeInverseMap[v] = k
	end
end
GenMapFunc()
-- luacheck: pop

local BulletEntity = DefineLocalEntity("BulletEntity", LocalEntityBase, {
	ViewControlFxComponent,
	ViewControlAudioComponent,
	ViewControlBaseComponent,
	})

BulletEntity:Register("BulletEntity")

function BulletEntity:ctor()
	self.LifeTimerId = nil
	self.soundPlayingID = nil

	self.TargetPosFromServer =  M3D.ToVec3(self.TargetPosFromServer)
	self.StartPos =  M3D.ToVec3(self.StartPos)

	if self.ServerRotOffset then
		self.ServerRotOffset = M3D.ToRotator(self.ServerRotOffset)
	else
		self.ServerRotOffset = M3D.Rotator()
	end

	if self.ServerPosOffset then
		self.ServerPosOffset = M3D.ToVec3(self.ServerPosOffset)
	else
		self.ServerPosOffset = M3D.Vec3()
	end

	self.hasTarget = false
end

function BulletEntity:Init()
	local bulletData = Game.TableData.GetNewBulletDataRow(self.BulletID)
	-- for key, value in ksbcpairs(bulletData) do
	-- 	self[key] = value
	-- end
	if self.LifeTime == nil or self.LifeTime <= 0 then
		self.LifeTime = bulletData.MaxLifeTime
	end

	self.LauncherType = bulletData.LauncherType
	self.BulletRotation = bulletData.Rotation
    self.BulletScale = bulletData.Scale

	self.BulletTypeInt = bulletData.BulletTypeInt

	self.Bone = bulletData.Bone
	self.Offset = bulletData.Offset
	self.MaxLifeTime = bulletData.MaxLifeTime
	self.DelayTime = bulletData.DelayTime
	self.Velocity = bulletData.Velocity
	self.MoveDist = bulletData.MoveDist
	self.Acceleration = bulletData.Acceleration
	self.AngularVelocity = bulletData.AngularVelocity
	self.ParabolaHeight = bulletData.ParabolaHeight
	self.TargetPointMoveSpeed = bulletData.TargetPointMoveSpeed
	self.TrackDuration = bulletData.TrackDuration


	self.EffectPath = bulletData.EffectPath
	self.FollowType = bulletData.FollowType
	self.BFollowScale = bulletData.BFollowScale
	self.Offset_Effect = bulletData.Offset_Effect


	self.TrailEffectPath = bulletData.TrailEffectPath
	self.FollowType_TrailEffect = bulletData.FollowType_TrailEffect
	self.BFollowScale_TrailEffect = bulletData.BFollowScale_TrailEffect
	self.Offset_TrailEffect = bulletData.Offset_TrailEffect
	self.Rotation_TrailEffect = bulletData.Rotation_TrailEffect
	self.Scale_TrailEffect = bulletData.Scale_TrailEffect


	self.HitEffectPath = bulletData.HitEffectPath
	self.BFollowScale_HitEffect = bulletData.BFollowScale_HitEffect
	self.Offset_HitEffect = bulletData.Offset_HitEffect
	self.Rotation_HitEffect = bulletData.Rotation_HitEffect
	self.Scale_HitEffect = bulletData.Scale_HitEffect


	self.DestroyEffectPath = bulletData.DestroyEffectPath
	self.BFollowScale_DestroyEffect = bulletData.BFollowScale_DestroyEffect
	self.Offset_DestroyEffect = bulletData.Offset_DestroyEffect
	self.Rotation_DestroyEffect = bulletData.Rotation_DestroyEffect
	self.Scale_DestroyEffect = bulletData.Scale_DestroyEffect


	self.FireAudioID = bulletData.FireAudioID
	self.WaterWaveRadius = bulletData.WaterWaveRadius
	self.WaterWaveParamID = bulletData.WaterWaveParamID
	self.FadeAudioID = bulletData.FadeAudioID

	self.TargetPointMoveSpeed = bulletData.TargetPointMoveSpeed
	self.TrackDuration = bulletData.TrackDuration

	self.RoundStartCoordinates = bulletData.RoundStartCoordinates
	self.RoundCenterCoordinates = bulletData.RoundCenterCoordinates

	self.DelayDestroy = bulletData.DelayDestroy
	self.EffectPriority = bulletData.EffectPriority

end

function BulletEntity:GetCreateActorLocation()

	if self.BulletTypeInt == ENewBulletType.MissileNoMiss then
		local StartTrans = M3D.Transform()
		local Entity = Game.EntityManager:GetEntityByIntID(self.LauncherID)
		if self.LauncherType == EBulletLauncherType.PetOfOriginLauncher then
			Entity = Entity:GetPerformPetMinDragonEntity()
		end
		
		if not Entity or self.LauncherID == 0 then
			return self.StartPos, {self.StartPitch,  self.StartYaw, 0.0}
		end
		if not Entity.bInWorld then
			return Entity:GetPosition(), Entity:GetRotation()
		end
		if self.OffsetMode == EBulletOffsetMode.Target then
			local TargetEntity = Game.EntityManager:GetEntityByIntID(self.TargetEID)
			Entity = TargetEntity or Entity
		end
		
		if self.Bone and self.Bone ~= "" then
			local TargetMainMeshId = Entity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
			local TTransform = Entity.CppEntity:KAPI_SceneID_GetSocketTransform(TargetMainMeshId, self.Bone, 0)
			M3D.ToTransform(TTransform, StartTrans)
		else
			StartTrans:Pack(Entity:GetTransform_P())
		end

		local StartOffset = M3D.Vec3()
		StartOffset:Pack(self.Offset[1], self.Offset[2], self.Offset[3])
		StartOffset:Add(self.ServerPosOffset, StartOffset)
		StartTrans:TransformPosition(StartOffset, StartOffset)
		StartTrans.Translation:Reset(StartOffset)

		local StartRot = M3D.Rotator()
		StartRot:Pack(Entity:GetRotation_P())
		StartRot:Add(self.ServerRotOffset, StartRot)
		self.StartPos = StartTrans.Translation
		self.StartPitch = StartRot.Pitch
		self.StartYaw = StartRot.Yaw
		return StartTrans.Translation, StartRot
	end

	return self.StartPos, {self.StartPitch,  self.StartYaw, 0.0}
end

function BulletEntity:getTargetPos(TargetEntity)
	if not TargetEntity then
		return self.TargetPosFromServer
	end
	
	local TargetLoc = M3D.Vec3()
	TargetLoc:Pack(TargetEntity:GetPosition_P())
	return TargetLoc
end

function BulletEntity.Direction(self)
	local Dur = self.MaxLifeTime
	local CalcedDis = self.Velocity * self.MaxLifeTime
	-- local Dis = CalcedDis
	if self.MoveDist and CalcedDis > self.MoveDist then
		Dur = self.MoveDist / self.Velocity
		-- Dis = self.MoveDist
	end
	-- 更新下生命时长
	self.LifeTime = Dur

	self.CppEntity:KAPI_SimpleMovement_AddMoveVariableSpeed(
		self.StartPos.X, self.StartPos.Y, self.StartPos.Z,
		self.TargetPosFromServer.X, self.TargetPosFromServer.Y, self.TargetPosFromServer.Z,
		self.Velocity, self.Acceleration, self.LifeTime
	)
end

function BulletEntity.Line(self)
	self.CppEntity:KAPI_SimpleMovement_AddMoveVariableSpeed(self.StartPos.X, self.StartPos.Y, self.StartPos.Z,
	 self.TargetPosFromServer.X, self.TargetPosFromServer.Y, self.TargetPosFromServer.Z,
	 self.Velocity, self.Acceleration, self.LifeTime) 

end

function BulletEntity:getRotateSpeed()
	local RotSpeed = self.AngularVelocity
	if RotSpeed <= 0 then
		RotSpeed = math.max(self.StartPitch,self.StartYaw) * 3.0
	end
	RotSpeed = math.max(RotSpeed, 60.0)
	return RotSpeed
end

function BulletEntity.Missile(self)
	local TargetEntity = Game.EntityManager:GetEntityByIntID(self.TargetEID)
	if TargetEntity and TargetEntity.CharacterID ~= 0 then
		local RotSpeed = self:getRotateSpeed()
		self.CppEntity:KAPI_SimpleMovement_AddMoveTrackTarget(TargetEntity.CharacterID, self.Velocity, self.Acceleration, RotSpeed, AbilityConst.MaxHomingAngle, self.LifeTime)
	else
		-- local Dir = M3D.Vec3()
		-- local TargetPos = self.TargetPosFromServer

		-- TargetPos:Sub(self.StartPos, Dir)
		-- Dir:Normalize()
		-- Dir:Mul(self.Velocity * self.LifeTime, TargetPos)
		-- TargetPos:Add(self.StartPos, TargetPos)
		-- self.TargetPosFromServer = TargetPos
				-- local KSL = import("KismetSystemLibrary")ApplyRootMotionParaCurve
        -- KSL.DrawDebugArrow(
		-- 	_G.GetContextObject(),
		-- 	M3D.ToFVector(self.StartPos),
		-- 	M3D.ToFVector( self.TargetPosFromServer),
		-- 	5,
		-- 	FLinearColor(1,0,0, 1.0),
		-- 	5,
		-- 	2
		-- )
		self.Line(self)
	end
end

function BulletEntity.MissileNoMiss(self)
	local TargetEntity = Game.EntityManager:GetEntityByIntID(self.TargetEID)
	if TargetEntity and TargetEntity.CharacterID ~= 0 then
		self.hasTarget = true
		self.CppEntity:KAPI_SimpleMovement_AddMoveMustHitTarget(TargetEntity.CharacterID, self.LifeTime)
	else
		self.Line(self)
	end
end

function BulletEntity.ParabolaTarget(self)
	self:ParabolaPos()
end

function BulletEntity.ParabolaPos(self)
	self.CppEntity:KAPI_SimpleMovement_AddMoveParabola(
			self.StartPos.X, self.StartPos.Y, self.StartPos.Z, self.TargetPosFromServer.X, self.TargetPosFromServer.Y, self.TargetPosFromServer.Z, self.LifeTime, self.ParabolaHeight)
end


function BulletEntity.Parabola(self)
	if self.Velocity == 0 and self.MaxLifeTime == 0 then
		Log.ErrorFormat("%s can not Velocity== 0 and MaxLifeTime == 0", self.ID)
		return
	end
	if self.Velocity == 0 then
		self.CppEntity:KAPI_SimpleMovement_AddMoveParabolaWithDuration(
		self.StartPos.X, self.StartPos.Y, self.StartPos.Z, 
		self.TargetPosFromServer.X, self.TargetPosFromServer.Y, self.TargetPosFromServer.Z,
		self.MaxLifeTime,
		self.Acceleration)
	end

	if self.MaxLifeTime == 0 then
		self.CppEntity:KAPI_SimpleMovement_AddMoveParabolaWithHorizontalVelocity(
		self.StartPos.X, self.StartPos.Y, self.StartPos.Z, 
		self.TargetPosFromServer.X, self.TargetPosFromServer.Y, self.TargetPosFromServer.Z,
		self.Velocity,
		self.Acceleration)
	end
end


function BulletEntity.ParabolaWithTrack(self)
	local TargetEntity = Game.EntityManager:GetEntityByIntID(self.TargetEID)
	if TargetEntity and TargetEntity.CharacterID ~= 0 then
		self.CppEntity:KAPI_SimpleMovement_AddMoveParabolaWithTrack(TargetEntity.CharacterID, self.TargetPointMoveSpeed, self.TrackDuration, self.MaxLifeTime, self.Acceleration)
	else
		self.Line(self)
	end
end

function BulletEntity.Round(self)
	local startPos = self.RoundStartCoordinates
	local centerPos = self.RoundCenterCoordinates
	self.CppEntity:KAPI_SimpleMovement_AddMoveRound(startPos[1], startPos[2], startPos[3], centerPos[1], centerPos[2], centerPos[3], self.MaxLifeTime, self.AngularVelocity)
end

-- luacheck: push ignore
function BulletEntity:debugDrawTrajectory()
	self.debugDrawTimerID = Game.TimerManager:CreateTimerAndStart(function ()
		local Dir = M3D.Vec3()
		local Rot = M3D.Rotator()
		Rot:Pack(self:GetRotation_P())
		Rot:ToVec3(Dir)
		Dir:Mul(20, Dir)
		local StartPos = M3D.Vec3()
		StartPos:Pack(self:GetPosition_P())
		local EndPos = M3D.Vec3()
		StartPos:Add(Dir, EndPos)

		local UKismetSystemLibrary = import("KismetSystemLibrary")
		-- UKismetSystemLibrary.DrawDebugPoint(
		-- _G.GetContextObject(), 
		-- self:GetPosition(), 
		-- 30.0, 
		-- FLinearColor(0.0, 1.0, 0.0),
		-- 20.0)

		UKismetSystemLibrary.DrawDebugArrow(
            _G.GetContextObject(),
            M3D.ToFVector(StartPos),
            M3D.ToFVector(EndPos),
            5,
            FLinearColor(0,1,0, 1.0),
            10,
            2
		)
	end, 0.05, -1)
end

-- luacheck: pop ignore

function BulletEntity:handleBulletTrajectory(BulletTypeName)
	local TrajectoryFunc = self[BulletTypeName]
	if TrajectoryFunc then
		TrajectoryFunc(self)
		-- self:debugDrawTrajectory()
	end
end

function BulletEntity:bulletPerformance()
	local BulletTypeName = EBulletTypeInverseMap[self.BulletTypeInt]
	self:handleBulletTrajectory(BulletTypeName)
	if self.LifeTime + self.DelayDestroy > 0 then
		self.LifeTimerId = Game.TimerManager:CreateTimerAndStart(function()
			self:destroy()
		end, (self.LifeTime + self.DelayDestroy)* 1000, 1)
	end
end


function BulletEntity:reCalcLifetime()
	if self.BulletTypeInt == ENewBulletType.Parabola then
		if self.MaxLifeTime == 0 then
			local Diff = M3D.Vec3()
			self.StartPos:Sub(self.TargetPosFromServer, Diff)
			self.LifeTime = Diff:Size2D()  / self.Velocity
		end
	end
end

function BulletEntity:AfterEnterWorld()
	-- 处理子弹单位生成之后的效果
	-- local AttachCompId = Game.ObjectActorManager:GetIDByObject(ActorRootComp)

	if self.BulletTypeInt == ENewBulletType.Parabola then
		if self.Velocity == 0 and self.MaxLifeTime == 0 then
			Log.ErrorFormat("%s can not Velocity== 0 and MaxLifeTime == 0", self.ID)
			return
		end
	end

	self:reCalcLifetime()
	-- 子弹特效
	self:PlayEffectTableNiagara(self.EffectPath,self.FollowType,self.BFollowScale, nil, self.LifeTime + 1.0, true, nil, false,self.Offset_Effect,self.BulletRotation,self.BulletScale, nil, nil, nil, self.EffectPriority)

	-- 拖尾特效
	self.ProjectileTrailNiagaraID = self:PlayEffectTableNiagara(
            self.TrailEffectPath,self.FollowType_TrailEffect,self.BFollowScale_TrailEffect, nil, self.LifeTime + 1.0, true, 
			nil, false, self.Offset_TrailEffect, self.Rotation_TrailEffect, self.Scale_TrailEffect, true, nil, nil, self.EffectPriority)

	-- 设置单位销毁时Ak组件不销毁
	self:SetAudioStopWhenOwnerDestroyed(false)

	-- 播放音效
	self:playAudio(self.FireAudioID)

	-- 子弹水波纹效果 @hujianglong
	if self.WaterWaveRadius and self.WaterWaveParamID and self.WaterWaveRadius > 0 then
		local BulletWaterWaveParamData = Game.TableData.GetBulletWaterWaveParamDataRow(self.WaterWaveParamID)
		if BulletWaterWaveParamData ~= nil then
			self.CppEntity:KAPI_SimpleMovement_AddWaterWavDetectParam(true, BulletWaterWaveParamData.DepthThreshold, BulletWaterWaveParamData.DistanceThreshold, self.WaterWaveRadius)
			self.CppEntity:KAPI_SimpleMovement_AddWaterWaveMoveParam(BulletWaterWaveParamData.WaveMotorTextureID, BulletWaterWaveParamData.ScaleX, BulletWaterWaveParamData.ScaleY, BulletWaterWaveParamData.MaxHeight, BulletWaterWaveParamData.FoamScale)
		end
	end

	if self.DelayTime and self.DelayTime > 0 then
		self.DelayPerformanceTimerID = Game.TimerManager:CreateTimerAndStart(function()
			self:bulletPerformance()
		end, self.DelayTime * 1000, 1)
		return
	end
	self:bulletPerformance()
end

function BulletEntity:BeforeExitWorld()

	-- 通知停止播放声音
	if self.soundPlayingID then
		self:AkStopEvent(self.soundPlayingID, 600, nil, true)
		self.soundPlayingID = nil
	end

	-- 播放消散音效
	self:playDestroyAudio(self.FadeAudioID)

	if self.debugDrawTimerID ~= nil then
		Game.TimerManager:StopTimerAndKill(self.debugDrawTimerID)
		self.LifeTimerId = nil
	end
	-- 通知Niagara销毁
	-- self:DestroyAllNiagaras()

	-- 播放销毁特效
	-- 销毁特效只能走类型0不跟随，后续需要拓展类型可以再加
	if (self.DestroyReason and self.DestroyReason == LUC.BULLET_DESTROY_REASON.HIT_TARGET and self.HitEffectPath and self.HitEffectPath ~= "") or (self.BulletTypeInt == ENewBulletType.MissileNoMiss and self.hasTarget) then
		-- self:PlayEffectTableNiagara(self.HitEffectPath,0,self.BFollowScale_HitEffect, nil, 1, false, nil, false, self.Offset_HitEffect, self.Rotation_HitEffect, self.Scale_HitEffect, true)
	else
		self:PlayEffectTableNiagara(self.DestroyEffectPath,0,self.BFollowScale_DestroyEffect, nil, 1, false, nil, false, self.Offset_DestroyEffect,self.Rotation_DestroyEffect, self.Scale_DestroyEffect, true, nil, nil, self.EffectPriority)
	end
	if self.LifeTimerId ~= nil then
		Game.TimerManager:StopTimerAndKill(self.LifeTimerId)
		self.LifeTimerId = nil
	end

	if self.DelayPerformanceTimerID  ~= nil then
		Game.TimerManager:StopTimerAndKill(self.DelayPerformanceTimerID)
		self.DelayPerformanceTimerID = nil
	end

	if self.ProjectileTrailNiagaraID ~= nil then
		if self.TrailEffectDelayDestroyTime and self.TrailEffectDelayDestroyTime > 0 then
			-- 走解绑 根据特效自身的生命周期来销毁
			self:SetNiagaraDelayDestroy(self.ProjectileTrailNiagaraID, self.TrailEffectDelayDestroyTime * 1000)
		else
			self:DestroyNiagaraSystem(self.ProjectileTrailNiagaraID)
		end
	end

    -- Game.ObjectActorManager:DestroyActorByID(self.CharacterID)
end

function BulletEntity:playAudio(audioEvent)
	if (audioEvent == nil) or (audioEvent == "") then
		return
	end

	local IEntity = Game.EntityManager:getEntity(self.FinalOwnerID)
	if not IEntity then
		return
	end

	if not Game.AkAudioManager:CanBattleSystemPostEvent(Enum.BattleAudioType.ATTACK, IEntity) then
		return
	end

	-- local token = Game.WorldManager.ViewBudgetMgr:TryRequestViewFrequency_ATTACK_SOUND(IEntity)
	-- if not token then
	-- 	return
	-- end

	local realAudioEvent = BSFunc.GetRealAudioPath(audioEvent, IEntity)
	-- self.soundToken = token
	self.soundPlayingID = self:AkPostEventOnActor(realAudioEvent, true)
end


function BulletEntity:playDestroyAudio(audioEvent)
	if (audioEvent == nil) or (audioEvent == "") then
		return
	end

	local IEntity = Game.EntityManager:getEntity(self.FinalOwnerID)
	if not IEntity then
		return
	end

	if not Game.AkAudioManager:CanBattleSystemPostEvent(Enum.BattleAudioType.ATTACK, IEntity) then
		return
	end

	-- local token = Game.WorldManager.ViewBudgetMgr:TryRequestViewFrequency_ATTACK_SOUND(IEntity)
	-- if not token then
	-- 	return
	-- end

	local realAudioEvent = BSFunc.GetRealAudioPath(audioEvent, IEntity)
	-- self.soundToken = token
	self.soundPlayingID = self:AkPostEvent3D(realAudioEvent, self:GetPosition(), true)
end


function BulletEntity:LoseTarget()
	if self.BulletTypeInt == ENewBulletType.Missile or self.BulletTypeInt == ENewBulletType.ParabolaWithTrack then
		self.CppEntity:KAPI_SimpleMovement_MustMoveInLine()
	end
end



return BulletEntity