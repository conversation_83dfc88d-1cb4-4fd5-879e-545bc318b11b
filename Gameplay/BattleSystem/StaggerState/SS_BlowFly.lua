-- 防止重复创建同名类
if (_G.SS_BlowFly ~= nil) then
    return _G.SS_BlowFly
end

local SS_BlowFly = DefineClass("SS_BlowFly", SS_Base)
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")

-- region Important
function SS_BlowFly:ctor(InComp)
    SS_BlowFly.super.ctor(self, InComp)

    -- 缓存RootMotion执行ID
    self.RMID = 0

    self.AnimationNames = {
        ["Main"] = "Hit_Air_Height01",
        [0] = "Hit_Air_Height01_0", 
        [1] = "Hit_Air_Height01_1", 
        [2] = "Hit_Air_Height01_2", 
        [3] = "Hit_Air_Height01_3",
        [4] = "Hit_Air_Height01_4",
        [5] = "Hit_Air_Height01_5",
        [6] = "Hit_Air_Height01_6",
        [7] = "Hit_Air_Height01_7",
        [9] = "Hit_Air_Height01_8",
    }

    self.StopMontageWhenLeave = true
end

-- 退出状态
function SS_BlowFly:LeaveState()
    -- 终止当前的RootMotion
    self:InternalTerminateRootMotion(self.RMID)
    self.RMID = 0

    SS_BlowFly.super.LeaveState(self)
end

-- 重置状态
function SS_BlowFly:ResetState(InLife, InData)
    -- 终止上次的RootMotion
    self:InternalTerminateRootMotion(self.RMID)
    self.RMID = 0

    -- 停止旧动画播放
    self.Entity:StopAnimLibMontage()

    local HFData = InData.HFData
    if (HFData ~= nil) then
        -- 击飞受击
        if (HFData.HitFeedbackType == ETE.EBSHitFeedbackType.HFT_BlowFly) then
            -- 旋转
            self:InternalExecuteRotation(InData.Yaw)

            -- 执行新的RootMotion
            self.RMID = self:InternalExecuteRootMotion(HFData.HitFeedbackID, HFData.MotionCurve, InLife, InData)

            -- 播放动画
            local TargetAnim = self.Entity:GetRandomHitAnimDataID(self.AnimationNames.Main)
            if TargetAnim and not BSFunc.CheckIsDead(self.Entity) then
                self.Entity:PlayAnimLibMontageCustomTag(ViewAnimConst.EAnimPlayReqTag.FullHit, TargetAnim)
            end
        end
    end

    SS_BlowFly.super.ResetState(self, InLife, InData)
end

function SS_BlowFly:SwitchState()
    self.SwitchData.SyncSign = self.SyncSign
    self.Entity:ChangeStaggerState(ETE.EBSStaggerState.SS_LieDown, 1.5, self.Entity:GetModifyStaggerKey(), self.SwitchData)
    table.clear(self.SwitchData)
end
-- endregion Important

return SS_BlowFly