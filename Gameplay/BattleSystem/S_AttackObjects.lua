local ATDT = Game.TableData.GetAttackTypeDataTable()
local ASDT = Game.TableData.GetAttackSlomoDataTable()



local BSAttackData = DefineClass("BSAttackData")

-- 构造函数
function BSAttackData:ctor()
    -- 必中
    self.bMustHit = false
    -- 播放受击效果
    self.bHitPerformance = true
    -- 显示受击数字
    self.bShowDamageUI = true

    -- 技能/Buff最初的原始目标ID
    self.OTEID = 0
    -- 目标数量(0代表无限制)
	self.AttackTargetNumber = 0
    -- 单一目标受击数量上限(0代表无限制)
    self.TargetOnAttackLimit = 0

    -- 造成攻击的资源类型(0为技能,1为BUFF)
    self.AssetType = 0
    -- 造成攻击的资源ID
	self.AssetID = 0
    -- 造成攻击的ActionID
	self.ActionID = 0
    -- 造成攻击的实例ID
    self.InstanceID = 0
    -- 造成攻击的实例等级
	self.InstanceLevel = 0
    -- 攻击类型
	self.AttackType = ETE.EBSAttackType.AT_Light
    -- 攻击力度
    self.AttackForce = 0
    -- 硬直时长
	self.StaggerDuration = 1.0
    -- 硬直距离
	self.StaggerDistance = 0.0
	-- 附加BUFF
	self.AttackBuffs = {}

    -- 攻击效果索引
	self.AttackEffectIndex = 0
    -- 攻击效果相对朝向
	self.EffectRelativeRotation = M3D.Rotator()

	-- 攻击闪白
    self.FlashWhite = false

    -- 攻击顿帧缩放
    self.AttackSlomo = 1.0
    -- 攻击顿帧时长
    self.AttackSlomoDuration = 0.0

    -- 是否检查攻击者存活
    self.NeedInstigatorAlive = false

    -- 其他信息
    self.ExtraData = {}
end

-- 重置数据
function BSAttackData:Reset()
    -- 必中
    self.bMustHit = false
    -- 播放受击效果
    self.bHitPerformance = true
    -- 显示受击数字
    self.bShowDamageUI = true

    -- 技能/Buff最初的原始目标ID
    self.OTEID = 0
    -- 目标数量(0代表无限制)
	self.AttackTargetNumber = 0
    -- 单一目标受击数量上限(0代表无限制)
    self.TargetOnAttackLimit = 0

    -- 造成攻击的资源类型(0为技能,1为BUFF)
    self.AssetType = 0
    -- 造成攻击的资源ID
	self.AssetID = 0
    -- 造成攻击的ActionID
	self.ActionID = 0
    -- 造成攻击的实例ID
    self.InstanceID = 0
    -- 造成攻击的实例等级
	self.InstanceLevel = 0
    -- 攻击类型
	self.AttackType = ETE.EBSAttackType.AT_Light
    -- 攻击力度
    self.AttackForce = 0
    -- 硬直时长
	self.StaggerDuration = 1.0
    -- 硬直距离
	self.StaggerDistance = 0.0
	-- 附加BUFF
	table.clear(self.AttackBuffs)

    -- 攻击效果索引
	self.AttackEffectIndex = 0
    -- 攻击效果相对朝向
	self.EffectRelativeRotation.Roll = 0.0
    self.EffectRelativeRotation.Pitch = 0.0
    self.EffectRelativeRotation.Yaw = 0.0

	-- 攻击闪白
    self.FlashWhite = false

    -- 攻击顿帧缩放
    self.AttackSlomo = 1.0
    -- 攻击顿帧时长
    self.AttackSlomoDuration = 0.0

    -- 是否检查攻击者存活
    self.NeedInstigatorAlive = false

    -- 其他信息
    table.clear(self.ExtraData)
end

-- 填充受击数据
function BSAttackData:FillBeatenData(AttackLogicID)
    -- 默认攻击等级为2
    self.AttackForce = 2
    -- 默认攻击类型为轻攻击
    self.AttackType = ETE.EBSAttackType.AT_Light

    -- 填充硬直信息
    local AttackLogic = ATDT[AttackLogicID]
    if (AttackLogic ~= nil) then
        self.AttackType = AttackLogic.AttackType
        -- 挑空时间已经变为常数
        if (self.AttackType == ETE.EBSAttackType.AT_LiftUp) then
            self.StaggerDuration = 0.0
        -- 挑空追击时间已经变为常数
        elseif (self.AttackType == ETE.EBSAttackType.AT_LiftUpChase) then
            self.StaggerDuration = 0.0
        -- 击倒追击存在一个时间数据
        elseif (self.AttackType == ETE.EBSAttackType.AT_HitDownChase) then
            self.StaggerDuration = AttackLogic.AttackParameter[1]
        -- 拖拽存在一个时间数据
        elseif (self.AttackType == ETE.EBSAttackType.AT_Drag) then
            self.StaggerDuration = AttackLogic.AttackParameter[1]
        -- 击退、击倒、击飞、击落存在一个时间数据、一个位移数据
        else
            self.StaggerDuration = AttackLogic.AttackParameter[1]
            if (AttackLogic.AttackParameter[2] ~= nil) then
                self.StaggerDistance = AttackLogic.AttackParameter[2]
            end
        end
    end
end

-- 填充SkillAction的数据
function BSAttackData:FillSkillActionData(InActionData)
    self.NeedInstigatorAlive = InActionData.NeedInstigatorAlive

    -- 受击信息
    self:FillBeatenData(InActionData.AttackLogic)

    -- 想要添加的BUFF信息
    for Index, ID in ipairs(InActionData.AddBuffID) do
        local BuffInfo = {}
        BuffInfo.BuffID = ID
        if (InActionData.BuffLayers[Index] ~= nil) then
            BuffInfo.ChangeLayer = InActionData.BuffLayers[Index]
        else
            BuffInfo.ChangeLayer = 1
        end
        table.insert(self.AttackBuffs, BuffInfo)
    end

    -- 效果信息
    self.AttackEffectIndex = InActionData.AttackEffectIndex

    -- 闪白信息
    self.FlashWhite = InActionData.FlashWhite

    -- 顿帧信息
    self.AttackSlomo = 1.0
    self.AttackSlomoDuration = 0.0
    local DataSlomo = InActionData.AttackSlomo
    if (DataSlomo ~= nil) and (DataSlomo > 0) then
        local SlomoInfo = ASDT[DataSlomo]
        if (SlomoInfo ~= nil) and (SlomoInfo.SlomoEffect ~= nil) then
            self.AttackSlomo = SlomoInfo.SlomoEffect[1]
            self.AttackSlomoDuration = SlomoInfo.SlomoEffect[2]
        end
    end
end

-- 填充BUFF攻击数据
function BSAttackData:FillDataByBuffAction(InBuffIns, InActionID)
    -- 数据重置
    self:Reset()

    if (InBuffIns == nil) then
        return false
    end

    local InBuffID = BSFunc.GetAssetID(InBuffIns)
    local InBuffLayer = InBuffIns:GetCurrentLayer()
    local BuffAction = nil
    if (BuffAction == nil) then
        Log.Debug("Damage Fail Can't Find Buff Action, BuffID:", InBuffID)
        return false
    end

    local ActionID = BuffAction[InActionID].UniqueId
    if (ActionID == nil) then
        Log.Debug("Damage Fail Can't Find Buff Action ID, BuffID:", InBuffID)
        return false
    end

    local ActionData = nil
    if (ActionData == nil) then
        Log.Debug("Damage Fail Can't Find Buff Action Data, BuffID:", InBuffID, ",ActionID:", ActionID)
        return false
    end

    --攻击信息
    self.AssetType = 1
    self.AssetID = InBuffID
    self.ActionID = InActionID
    self.InstanceID = InBuffIns.InsID
    self.InstanceLevel = InBuffIns.Level
    self.bMustHit = true
    self.bHitPerformance = false
    self.NeedInstigatorAlive = ActionData.NeedInstigatorAlive

    -- 其他信息
    self.ExtraData.BuffLayer = InBuffLayer

    -- 效果信息
    self.AttackEffectIndex = ActionData.AttackEffectIndex

    -- 存在攻击逻辑
    if (ActionData.AttackLogic > 0) then
        self.bMustHit = false
        self.bHitPerformance = true

        -- 受击信息
        self:FillBeatenData(ActionData.AttackLogic)
    end

    return true
end






local BSAttackMessage = DefineClass("BSAttackMessage")

-- 构造函数
function BSAttackMessage:ctor()
	-- 攻击者
    self.AttackerEID = 0
	-- 攻击始作俑者
	self.AttackInstigatorEID = 0
	-- 受击者
    self.DefenderEID = 0

	-- 攻击起始点
	self.ImpulseOrigin = M3D.Vec3()
	-- 攻击冲击方向
	self.ImpulseForward = M3D.Vec3()
	-- 攻击冲击方向的垂直方向
	self.ImpulseUp = M3D.Vec3()
	-- 攻击碰撞结果
	self.HitResult = BSS.HitResult()

	-- 其他信息
	self.ExtraData = {}
end

-- 重置数据
function BSAttackMessage:Reset()
	-- 攻击者
    self.AttackerEID = 0
	-- 攻击始作俑者
	self.AttackInstigatorEID = 0
	-- 受击者
    self.DefenderEID = 0

	-- 攻击起始点
	self.ImpulseOrigin.X = 0.0
	self.ImpulseOrigin.Y = 0.0
	self.ImpulseOrigin.Z = 0.0
	-- 攻击冲击方向
	self.ImpulseForward.X = 0.0
	self.ImpulseForward.Y = 0.0
	self.ImpulseForward.Z = 0.0
	-- 攻击冲击方向的垂直方向
	self.ImpulseUp.X = 0.0
	self.ImpulseUp.Y = 0.0
	self.ImpulseUp.Z = 1.0
	-- 攻击碰撞结果
	self.HitResult:Reset()

	-- 其他信息
	table.clear(self.ExtraData)
end






local BSAttackResult = DefineClass("BSAttackResult")

-- 构造函数
function BSAttackResult:ctor()
	-- 是否命中
	self.bHit = false
	-- 是否暴击
	self.bCrit = false
	-- 是否治疗
	self.bHeal = false
	-- 是否击杀
	self.bDead = false

	-- 攻击类型
	self.AttackType = ETE.EBSAttackType.AT_Light
	-- 攻击力度
	self.Force = 0
	-- 受击者朝向
	self.DefenderRotation = M3D.Rotator()
	-- 攻击者相对于受击者的方向
	self.RelationType = ETE.EBSAttackerLocationType.ALT_None

	-- 服务器下发的伤害信息
	self.DamageResults = {}
end

-- 重置数据
function BSAttackResult:Reset()
	-- 是否命中
	self.bHit = false
	-- 是否暴击
	self.bCrit = false
	-- 是否治疗
	self.bHeal = false
	-- 是否击杀
	self.bDead = false

	-- 攻击类型
	self.AttackType = ETE.EBSAttackType.AT_Light
	-- 攻击力度
	self.Force = 0
	-- 受击者朝向
	self.DefenderRotation.Roll = 0.0
    self.DefenderRotation.Pitch = 0.0
    self.DefenderRotation.Yaw = 0.0
	-- 攻击者相对于受击者的方向
	self.RelationType = ETE.EBSAttackerLocationType.ALT_None

	-- 服务器下发的伤害信息
	table.clear(self.DamageResults)
end
