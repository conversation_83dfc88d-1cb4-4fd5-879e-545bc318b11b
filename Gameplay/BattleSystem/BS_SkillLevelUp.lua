SLUFuncs = SLUFuncs or {}

local SLvlUpDT = Game.TableData.GetSkillLevelUpDataTable()

function SLUFuncs.GetSkillLevelUpDeltaValue(lvlUpTid, lvl)
    local lvlUpTemplateRow = SLvlUpDT[lvlUpTid]
    if lvlUpTemplateRow == nil then
        return nil
    end

    local rowName = "DeltaLv" .. lvl
    return lvlUpTemplateRow[rowName]
end

function SLUFuncs.GetSkillLevelUpFinalValue(initValue, lvlUpTid, lvl, lvlScale, lvlUpDeltaIdx)
    local finalValue = initValue

    local lvlUpDeltaList = SLUFuncs.GetSkillLevelUpDeltaValue(lvlUpTid, lvl)
    if lvlUpDeltaList ~= nil and #lvlUpDeltaList >= lvlUpDeltaIdx then
        finalValue = finalValue + lvlScale * lvlUpDeltaList[lvlUpDeltaIdx]
    end

    return finalValue
end