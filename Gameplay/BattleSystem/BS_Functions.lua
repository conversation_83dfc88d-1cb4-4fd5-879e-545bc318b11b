require("Gameplay.BattleSystem.BS_Enums")
require("Gameplay.BattleSystem.BS_Structs")
-- require("Gameplay.BattleSystem.BS_Conditions")
-- require("Gameplay.BattleSystem.BSLC_Base")
-- require("Gameplay.BattleSystem.BSLC_Lock")
-- require("Gameplay.BattleSystem.Profession.BSLC_Profession")
-- require("Gameplay.BattleSystem.Profession.BSLC_Utopian")

-- local UBSFunc = import("BSFunctionLibrary")
local FCollisionChannelInfo = import("CollisionChannelInfo")
local FChangeCollisionMessage = import("ChangeCollisionMessage")
local FBSCollisionFilter = import("BSCollisionFilter")
local FBSMeshCreater = import("BSMeshCreater")
local EPropertyClass = import("EPropertyClass")
local EWActorType = kg_require("Shared.WorldActorDefine").EWActorType
local NetworkManager = require("Framework.DoraSDK.NetworkManager")

local SelectU = kg_require("Gameplay.Combat.Selector.SelectionUtils")
local ETargetActorType = kg_require("Shared.Const.AbilityConst").ETargetActorType
local EFactionType = kg_require("Shared.Const.AbilityConst").EFactionType
local EValidTargetCheckType = kg_require("Shared.Const.AbilityConst").EValidTargetCheckType
local bit = require("Framework.Utils.bit")
-- local gameclientdata = require("Framework.Utils.GameClientData")
-- local targetselectionruledata = require("Data.Excel.TargetSelectionRuleData")

local battleUtils = kg_require("Shared.Utils.BattleUtils")
local BuffStateConst = kg_require("Shared.Const.BuffStateConst")

BSFunc = BSFunc or {}

-- 位掩码是否包含该枚举
function BSFunc.CheckEnumWithBitMask(InEnum, InBitMask)
    if (InEnum == nil) or (InBitMask == nil) then
        return false
    end

    local EnumMask = 1 << InEnum
    return (InBitMask & EnumMask) > 0
end

-- 枚举数组转位掩码
function BSFunc.EnumsToBitMask(InTable)
    local Result = 0

    for _, V in pairs(InTable) do
        Result = Result + (1 << V)
    end

    return Result
end

-- 位掩码转枚举数组
function BSFunc.BitMaskToEnums(InBitMask)
    local CurMask = InBitMask
    local Result = {}

    local Bit = 0
    while (CurMask > 0) do
        if (CurMask % 2 == 1) then
            table.insert(Result, Bit)
        end

        Bit = Bit + 1
        CurMask = CurMask // 2
    end

    return Result
end

function BSFunc.GetAnimationFromLibrary(InEntity, InID)
    if (InEntity == nil) then
        return nil
    end

    return InEntity:GetAnimFeatureData(InID)
end

-- 获取当前平均网络延迟的一半
function BSFunc.GetHalfNetLag()
    local LocalAvatar = NetworkManager.GetLocalAvatarActor()
    if (LocalAvatar ~= nil) then
        return math.floor(LocalAvatar.__averageDelay)
    end

    return 0
end

-- 获取服务器时间戳
function BSFunc.GetServerTimestamp()
    local CurClientTS = BSFunc.GetClientTimestamp()

    local LocalAvatar = NetworkManager.GetLocalAvatarActor()
    if (LocalAvatar ~= nil) then
        return math.floor(CurClientTS + LocalAvatar:GetNetworkDelta())
    end

    return CurClientTS
end

-- 获取客户端时间戳
function BSFunc.GetClientTimestamp()
    return Game.BSManager:GetFrameMillisecond()
end
-- endregion Misc






-- region Struct
-- 组装碰撞筛选器
function BSFunc.AssembleCollisionFilter(InTable, InCFilter)
    local Res = InCFilter
    if (Res == nil) then
        Res = FBSCollisionFilter()
    end

    Res.bIgnoreSelf = InTable.bIgnoreSelf

    Res.AllowedDeltaHeight.X = InTable.AllowedDeltaHeight.X
    Res.AllowedDeltaHeight.Y = InTable.AllowedDeltaHeight.Y

    if (InTable.ClassTypes ~= nil) then
        for I, V in ipairs(InTable.ClassTypes) do
            Res.ClassTypes:Add(V)
        end
    end

    if (InTable.IgnoreClassTypes ~= nil) then
        for I, V in ipairs(InTable.IgnoreClassTypes) do
            Res.IgnoreClassTypes:Add(V)
        end
    end

    if (InTable.ActorTags ~= nil) then
        for I, V in ipairs(InTable.ActorTags) do
            Res.ActorTags:Add(V)
        end
    end

    if (InTable.IgnoreActorTags ~= nil) then
        for I, V in ipairs(InTable.IgnoreActorTags) do
            Res.IgnoreActorTags:Add(V)
        end
    end

    if (InTable.ComponentTags ~= nil) then
        for I, V in ipairs(InTable.ComponentTags) do
            Res.ComponentTags:Add(V)
        end
    end

    if (InTable.IgnoreComponentTags ~= nil) then
        for I, V in ipairs(InTable.IgnoreComponentTags) do
            Res.IgnoreComponentTags:Add(V)
        end
    end

    return Res
end

-- 组装FCollisionChannelInfo
function BSFunc.AssembleFCollisionChannelInfo(InTable)
    local ColChannelInfo = FCollisionChannelInfo()
    ColChannelInfo.NewCollisionChannel = InTable.NewCollisionChannel
    ColChannelInfo.NewCollisionResponse = InTable.NewCollisionResponse

    return ColChannelInfo
end

-- 组装FChangeCollisionMessage
function BSFunc.AssembleFChangeCollisionMessage(InTable, OutStruct)
    local ChangeColMsg = OutStruct
    if (ChangeColMsg == nil) then
        ChangeColMsg = FChangeCollisionMessage()
    end
    ChangeColMsg.ComponentVarName = InTable.ComponentVarName

    ChangeColMsg.bOverrideCollisionBoxSize = InTable.bOverrideCollisionBoxSize
    ChangeColMsg.CollisionBoxSize = M3D.ToFVector(InTable.CollisionBoxSize)
    ChangeColMsg.NewCollisionChannelInfos:Clear()
    for I, V in ipairs(InTable.NewCollisionChannelInfos) do
        ChangeColMsg.NewCollisionChannelInfos:Add(BSFunc.AssembleFCollisionChannelInfo(V))
    end
    ChangeColMsg.bNeedChangeObjectType = InTable.bNeedChangeObjectType
    ChangeColMsg.NewObjectType = InTable.NewObjectType
    ChangeColMsg.bNeedChangeCollisionState = InTable.bNeedChangeCollisionState
    ChangeColMsg.NewCollisionState = InTable.NewCollisionState

    return ChangeColMsg
end

-- 组装FBSMeshCreater
function BSFunc.AssembleMeshCreater(InTable, OutStruct)
    local MeshCreater = OutStruct
    if (MeshCreater == nil) then
        MeshCreater = FBSMeshCreater()
    end
    MeshCreater.MeshName = InTable.MeshName
    MeshCreater.bUseStaticMesh = InTable.bUseStaticMesh
    MeshCreater.StaticMesh = InTable.StaticMesh
    MeshCreater.SkeletalMesh = InTable.SkeletalMesh
    MeshCreater.AnimSequence = InTable.AnimSequence
    MeshCreater.bNeedLoop = InTable.bNeedLoop

    MeshCreater.RelativeTransform = M3D.ToFTransform(InTable.RelativeTransform)
    MeshCreater.bCollisionEnable = InTable.bCollisionEnable

    MeshCreater.bRenderInMainPass = InTable.bRenderInMainPass
    MeshCreater.bUseCustomDepth = InTable.bUseCustomDepth
    MeshCreater.CustomDepthStencilWriteMask = InTable.CustomDepthStencilWriteMask
    MeshCreater.CustomDepthStencilValue = InTable.CustomDepthStencilValue

    MeshCreater.bReceivesDecals = InTable.bReceivesDecals

    return MeshCreater
end

-- 组装碰撞类型数组
function BSFunc.AssembleObjectTypes(InTable, OutArray)
    local ObjectTypes = OutArray
    if (ObjectTypes == nil) then
        ObjectTypes = slua.Array(EPropertyClass.Int)
    else
        ObjectTypes:Clear()
    end

    for K, V in pairs(InTable) do
        ObjectTypes:Add(V)
    end

    return ObjectTypes
end
-- endregion Struct






-- region Math
-- 四舍五入到整数
function BSFunc.Int(InValue)
    return math.floor(InValue + 0.5)
end

-- endregion Math






-- region Array
-- 将Src数组拼接到Dest数组后
function BSFunc.ArrayAppend(InSrc, InDest)
    if (#InSrc <= 0) or (#InDest <= 0) or (type(InSrc[1]) ~= type(InDest[1])) then
        --Log.Debug("[BSLOG] Array : Different Element Type")
        return
    end

    for Index, Value in ipairs(InSrc) do
        table.insert(InDest, Value)
    end
end

-- 获取元素在数组中的索引
function BSFunc.ArrayFind(InArray, InElement)
    if (#InArray <= 0) or (type(InArray[1]) ~= type(InElement)) then
        return 0
    end

    for Index, Value in ipairs(InArray) do
        if (Value == InElement) then
            return Index
        end
    end

    return 0
end

-- 判断数组中是否含有某项
function BSFunc.ArrayContains(InArray, InElement)
    if (InArray == nil) or (#InArray <= 0) or (type(InArray[1]) ~= type(InElement)) then
        return false
    end

    for Index, Value in ipairs(InArray) do
        if (Value == InElement) then
            return true
        end
    end

    return false
end

-- 数组添加元素（检查是否重复）
function BSFunc.ArrayAddUnique(InArray, InElement)
    local Size = #InArray
    if (Size > 0) and (type(InArray[1]) ~= type(InElement)) then
        return
    end

    for i = 1, Size do
        if (InArray[i] == InElement) then
            return
        end
    end

    InArray[Size + 1] = InElement
end
-- endregion Array





-- region Asset
-- 尝试获取技能/Buff的ID
function BSFunc.GetAssetID(InInstance)
    if (type(InInstance) == "table") and (InInstance.StaticData ~= nil) and (InInstance.StaticData.ID ~= nil) then
        return InInstance.StaticData.ID
    end

    return -1
end

-- 根据技能ID获取其子技能列表
function BSFunc.GetSubSkillList(SkillID)
    local Result = {}

	-- 兼容新技能
	local SkillGroup = Game.TableData.Get_parentSkill2SubSkillListNew()
	if SkillGroup and SkillGroup[SkillID] then
		return SkillGroup[SkillID]
	end
	return Result
end

-- 根据怪物ID获取技能ID列表
function BSFunc.GetSkillIDsByMonsterID(InMonsterID)
    local Result = {}

    return Result
end

---@param IEntity table 发起者,用于计算关系
---@param TEntity table 目标,用于计算关系
---@param player table 实际是谁播,用于计算性别后缀
function BSFunc.GetRealAudioPath(InOriginPath, IEntity, TEntity, player)
    -- 编辑态下,可以切换预览模式
    if (Game.BSManager.bIsInEditor == true) and (Game.BSManager:IsUseOriginalAudioPreviewMode() == true) then
        return InOriginPath
    end

    local FinalPath = InOriginPath

    if (FinalPath == nil) or (FinalPath == "") then
        return FinalPath
    end

    if not IEntity then
        return FinalPath
    end

    -- 分别获取始作俑者
    IEntity = BSFunc.GetActorInstigator(IEntity)
    TEntity = BSFunc.GetActorInstigator(TEntity)

    if not player then
        player = IEntity
    end

    -- 是否来自P1
    local bFromLocal = IEntity == Game.me
    -- 是否打向P1
    local bHitLocal = TEntity == Game.me

    -- todo@shijingzhe:编辑态逻辑兼容
    if Game.BSManager.bIsInEditor then
        local editorAudioName = InOriginPath

        -- 发起方是玩家角色,走_1P,其他情况都是_3P
        if (bFromLocal == true) and (IEntity.NpcType == nil) then
            editorAudioName = editorAudioName .. Game.AkAudioManager.EPlayerSlotSuffix.P1
        end

        return editorAudioName
    end

    -- 首先区分有无命中,没目标肯定没命中,发起者和目标是同一个单位,也是没命中
    if (TEntity == nil) or (IEntity == TEntity) then
        if bFromLocal then
            -- 我产生的是1P
            FinalPath = Game.AkAudioManager:GetRealSkillEventName(InOriginPath, Game.AkAudioManager.EPlayerSlotSuffix.P1)
        else
            -- 别人产生的是3P
            FinalPath = Game.AkAudioManager:GetRealSkillEventName(InOriginPath, Game.AkAudioManager.EPlayerSlotSuffix.P3)
        end
    else
        if bFromLocal then
            -- 我命中别人是1P
            FinalPath = Game.AkAudioManager:GetRealSkillEventName(InOriginPath, Game.AkAudioManager.EPlayerSlotSuffix.P1)
        elseif (bFromLocal == false) and (bHitLocal == true) then
            -- 别人命中我是3P
            FinalPath = Game.AkAudioManager:GetRealSkillEventName(InOriginPath, Game.AkAudioManager.EPlayerSlotSuffix.P3)
        elseif (bFromLocal == false) and (bHitLocal == false) then
            -- 别人命中别人是2P
            FinalPath = Game.AkAudioManager:GetRealSkillEventName(InOriginPath, Game.AkAudioManager.EPlayerSlotSuffix.P2)
        end
    end
    
    return FinalPath
end
-- endregion Asset






-- region Attribute
-- 执行属性计算器
function BSFunc.ExePropCalculator(InCalculator, InEntity)
    if (InEntity ~= nil) then
        local CurValue = InEntity[InCalculator.Molecular]
        if (CurValue ~= nil) then
            if (type(CurValue) == "boolean") then
                CurValue = (CurValue == true) and 1 or 0
            end
            if (InCalculator.Denominator and InCalculator.Denominator ~= "") and (InCalculator.Denominator ~= "None") then
                local Denominator = InEntity[InCalculator.Denominator]
                if (type(Denominator) == "boolean") then
                    Denominator = (Denominator == true) and 1 or 0
                end
                if (Denominator ~= nil) and (Denominator ~= 0.0) then
                    CurValue = CurValue / Denominator
                end
            end

            return CurValue * InCalculator.Proportion
        end
    end

    return 0.0
end

-- 执行属性比较器
function BSFunc.ExePropComparator(InComparator, InEntity)
    local PropValue = BSFunc.ExePropCalculator(InComparator.AttributeValue, InEntity)

    if (InComparator.Sign == 0) then
        return PropValue < InComparator.Value
    elseif (InComparator.Sign == 1) then
        return PropValue <= InComparator.Value
    elseif (InComparator.Sign == 2) then
        return PropValue == InComparator.Value
    elseif (InComparator.Sign == 3) then
        return PropValue >= InComparator.Value
    elseif (InComparator.Sign == 4) then
        return PropValue > InComparator.Value
    end

    return false
end
-- endregion Attribute






-- region Character
-- 获取角色的始作俑者
function BSFunc.GetActorInstigator(InEntity)
    if (InEntity == nil) then
        return nil
    end

	local ownerID = InEntity.FinalOwnerID and InEntity.FinalOwnerID or InEntity.IEID
	
    if (InEntity.eid == ownerID) then
        return InEntity
    end

    local Instigator = Game.EntityManager:getEntity(ownerID)
    if (Instigator ~= nil) then
        return Instigator
    end

    return InEntity
end


local __InvalidActorType__ = -1 -- luacheck: ignore

-- @shijingzhe:创生怪物没有被识别,这里先保留旧的逻辑,有问题再排查
function BSFunc.GetActorTypeNew(InEntity)
    if (InEntity == nil) then
        return __InvalidActorType__
    end

    local actorType = InEntity.ActorType
    if not actorType then
        return __InvalidActorType__
    end

    if actorType == EWActorType.PLAYER then
        return ETargetActorType.Player

    elseif (actorType == EWActorType.NPC) or
            ((EWActorType.NPC_MIN <= actorType) and (actorType <= EWActorType.NPC_MAX)) then
        if InEntity.BossType == Enum.EBossType.CREEP then
            return ETargetActorType.NormalMonster
        elseif InEntity.BossType == Enum.EBossType.Elite then
            return ETargetActorType.EliteMonster
        elseif InEntity.BossType == Enum.EBossType.BOSS or InEntity.BossType == Enum.EBossType.Building then
            return ETargetActorType.Boss
        elseif InEntity.BossType == Enum.EBossType.Create then
            return ETargetActorType.CreateMonster
        else
            return __InvalidActorType__
        end

    elseif (EWActorType.SCENEACTOR_MIN <= actorType) and (actorType <= EWActorType.SCENEACTOR_MAX) then
        return ETargetActorType.Interactor
    end

    return __InvalidActorType__
end

-- 查询目标entity是否为主机所有
function BSFunc.IsLocalOwned(InEntity)
    return InEntity ~= nil and InEntity.FinalOwnerID == Game.me.eid
end

-- 查询角色是否是本地操控角色
function BSFunc.IsLocalControl(InEntity, bCheckInstigator)
    if (InEntity == nil) then
        return false
    end

    -- Game.me不应该不存在，这里先默认为true
    if (Game.me == nil) or (Game.me.eid == InEntity.eid) then
        return true
    end

    if (bCheckInstigator == true) then
        return (Game.me.eid == InEntity.FinalOwnerID)
    end

    return false
end

-- 查询角色是否死亡
function BSFunc.CheckIsDead(InEntity)
    if (InEntity == nil) then
        return false
    end

    return InEntity.IsDead or false
end

-- 检查目标是否隐身
function BSFunc.CheckTargetInvisible(InTEntity)
    if not InTEntity or not InTEntity.bInWorld then
        return false
    end

	if (InTEntity.IsBuffStateActivated == nil) then
		Log.WarningFormat("BSFunc.CheckTargetInvisible Entity do not has IsBuffStateActivated: %s", InTEntity.__cname)
		return false
	end
	local result = InTEntity:IsBuffStateActivated(BuffStateConst.BuffStateType.Stealth) or  InTEntity:IsBuffStateActivated(BuffStateConst.BuffStateType.CharacterStealth)

    return result
end

-- 检查目标是否可命中
function BSFunc.CheckTargetCanBeHit(InOEntity, InTEntity)
    if (InOEntity == nil) or (InTEntity == nil) then
        return false
    end

    if (Game.BSManager.bIsInEditor == true) then
        return true
    end

    local EOwnerI = BSFunc.GetActorInstigator(InOEntity)
    if (EOwnerI == nil) then
        return false
    end

    -- 检查两者是否在同一个战斗区域
    local OBattleZoneID = EOwnerI.BattleZoneID
    local TBattleZoneID = InTEntity.BattleZoneID
    if (OBattleZoneID ~= nil) and (TBattleZoneID ~= nil) and (OBattleZoneID ~= TBattleZoneID) then
        return false
    end

    if (InTEntity.IsHitLimit ~= nil) and (InTEntity.IsHitLimit > 0) then
        -- 不可命中只对中立和敌方生效
        if (BSFunc.CheckTargetCamp(EOwnerI, InTEntity, InTEntity.HitLimitCamp) == true) then
            return false
        end
    end

    return true
end

-- TODO: 使用 CheckTargetNew
-- 检查目标
function BSFunc.CheckTarget(InOEntity, InTEntity, Type, Camp)
    if (InOEntity == nil) or (InTEntity == nil) then
        return false
    end

    if (Game.BSManager.bIsInEditor == false) then
        -- 检查两者是否在同一个战斗区域
        local OEntityI = BSFunc.GetActorInstigator(InOEntity)
        if (OEntityI.BattleZoneID ~= InTEntity.BattleZoneID) then
            return false
        end

        -- 检查Owner是否活着
        if (BSFunc.CheckIsDead(OEntityI) == true) then
            return false
        end

        -- 检查目标是否活着
        if (BSFunc.CheckIsDead(InTEntity) == true) then
            return false
        end

        -- 排除观察者状态
        local ObserverType = InTEntity.ObserverType
        if (ObserverType == nil) or (ObserverType > 0) then
            return false
        end

        --检查目标类型
        if (BSFunc.CheckTargetType(InTEntity, Type) == false) then
            return false
        end

        --检查目标阵营
        if (BSFunc.CheckTargetCamp(OEntityI, InTEntity, Camp) == false) then
            return false
        end
    else
        if ((Camp ~= 0) and ((Camp & 1) <= 0)) and ((InOEntity == InTEntity) or (BSFunc.GetActorInstigator(InOEntity) == InTEntity)) then
            return false
        end
    end

    return true
end

-- TODO: 已与服务器检查逻辑有不同, 部分需保持统一
-- 新检查目标
function BSFunc.CheckTargetNew(InOEntity, InTEntity, Types, Camp, InIgnoreCheckMask)
    if (InOEntity == nil) or (InTEntity == nil) then
        return false
    end

    if (Game.BSManager.bIsInEditor == false) then
        local IgnoreCheckMask = InIgnoreCheckMask or 0

        -- 检查两者是否在同一个战斗区域
        local OEntityI = BSFunc.GetActorInstigator(InOEntity)
        local OBZID = OEntityI.BattleZoneID or ""
        local IBZID = InTEntity.BattleZoneID or ""
        if (bit.band(EValidTargetCheckType.BattleZone, IgnoreCheckMask) == 0) and OBZID ~= IBZID then
            return false
        end

        -- 检查Owner是否活着
        if (BSFunc.CheckIsDead(OEntityI) == true) then
            return false
        end

        -- 检查目标是否活着
        if (bit.band(EValidTargetCheckType.Dead, IgnoreCheckMask) == 0) and BSFunc.CheckIsDead(InTEntity) == true then
            return false
        end

        -- 排除观察者状态
        local ObserverType = InTEntity.ObserverType
        if (bit.band(EValidTargetCheckType.Observer, IgnoreCheckMask) == 0) and (ObserverType ~= nil) and (ObserverType > 0) then
            return false
        end

        --检查目标类型
        if (BSFunc.CheckTargetTypeNew(InTEntity, Types) == false) then
            return false
        end

        --检查目标阵营
        if (BSFunc.CheckTargetCampNew(InOEntity, InTEntity, Camp) == false) then
            return false
        end

        -- 检查目标是否无敌
        if (bit.band(EValidTargetCheckType.HitLimit, IgnoreCheckMask) == 0) and BSFunc.CheckTargetCanBeHit(OEntityI, InTEntity) == false then
            return false
        end
    else
        if ((Camp ~= 0) and ((Camp & 1) <= 0)) and ((InOEntity == InTEntity) or (BSFunc.GetActorInstigator(InOEntity) == InTEntity)) then
            return false
        end
    end

    return true
end


-- 新检查目标类型
function BSFunc.CheckTargetTypeNew(InTEntity, Types)
    if (InTEntity == nil) then
        return false
    end
	-- 小龙不可选
	if (Game.EntityManager:IsLocalPerformPetEntity(InTEntity)) then
		return false
	end
	
    if (Types <= 0) then
        return true
    end
    local TargetType = BSFunc.GetActorTypeNew(InTEntity)
    if TargetType < 0 then
        return false
    end
    if Types >= TargetType and bit.band(Types, TargetType) > 0 then
        return true
    end
    return false
end

-- 检查目标类型
function BSFunc.CheckTargetType(InTEntity, Types)
    if (InTEntity == nil) then
        return false
    end

    if (Types <= 0) then
        return true
    end

	-- 如果使用了gm，那么直接突破限制
	if Game.QuestSystem.GMSelectLimit then
		return true
	end

    --再检查目标的角色类型
    local CurIt = 0
    while (Types > 0) do
        if (Types % 2 == 1) then
            --检查是否是玩家角色
            if (CurIt == ETE.EBSCharacterType.CT_Player) then
                if (InTEntity.isAvatar) then
                    return true
                end
            --检查是否是创生怪物 
            elseif (CurIt == ETE.EBSCharacterType.CT_CreateMonster) then
                if (InTEntity.isNpc) and (InTEntity.BossType == 1) then
                    return true
                end
            --检查是否是普通怪物
            elseif (CurIt == ETE.EBSCharacterType.CT_NormalMonster) then
                if (InTEntity.isNpc) and (InTEntity.BossType == 0) then
                    return true
                end
            --检查是否是精英怪物
            elseif (CurIt == ETE.EBSCharacterType.CT_EliteMonster) then
                if (InTEntity.isNpc) and (InTEntity.BossType == 2) then
                    return true
                end
            --检查是否是BOSS
            elseif (CurIt == ETE.EBSCharacterType.CT_BOSS) then
                if (InTEntity.isNpc) and (InTEntity.BossType == 3 or InTEntity.BossType == 4) then
                    return true
                end
            -- 检查是否是任务NPC
            elseif (CurIt == ETE.EBSCharacterType.CT_TaskNPC) then
                if (InTEntity.isNpc) and (InTEntity.NpcType == Enum.ENpcTypeData.Task) then
                    return true
                end
            end
        end

        Types = Types >> 1
        CurIt = CurIt + 1
    end

    return false
end


--检查目标是否受设置保护
function BSFunc.CheckProtected(InOEntity, InTEntity)
    local SettingManager = Game.SettingsManager

    if (SettingManager:GetIniData(Enum.ESettingDataEnum.OpenProtection) == 0) then
        return false
    end

    --等级保护
    local levelLimit = SettingManager:GetIniData(Enum.ESettingDataEnum.LevelProtection)
    if (InTEntity.Level <= levelLimit) then
        return true
    end

    --绿名保护
    if (SettingManager:GetIniData(Enum.ESettingDataEnum.GreenProtection) == 1) then
        local targetBounty = InTEntity.Bounty or 0
        local redNameStageTable = Game.TableData.GetRedNameStageDataTable()
        local greenCfg = redNameStageTable["GREEN"]

        if (targetBounty >= greenCfg.NumericalDuration[1]) and (targetBounty <= greenCfg.NumericalDuration[2]) then
            local yellowNameTime = InTEntity.YellowNameTime
            
            if (yellowNameTime ~= nil) and (yellowNameTime > 0) then
                local now = math.floor(_G._now() / 1000)
                if ((now - yellowNameTime) >= Enum.ERedNameConstIntData.YELLOW_DURATIONTIME) then
                    return true
                end
            else
                return true
            end
        end
    end

    --非敌对公会保护 TODO
    --[[if SettingManager:GetIniData(Enum.ESettingDataEnum.HostileProtection) == 1 then
    
    end]]

    --公会保护
    if (SettingManager:GetIniData(Enum.ESettingDataEnum.GuildProtection) == 1) and (InOEntity.guildId == InTEntity.guildId) and (InOEntity.guildId ~= "") and (InTEntity.guildId ~= "") then
        return true
    end

    return false
end



-- 检查是否是关卡怪
function BSFunc.IsLevelFlowMonster(InEntity)
    if (InEntity == nil) then
        return false
    end

    return (InEntity.TemplateID == Game.TableData.GetConstDataRow("LEVEL_FLOW_SKILL_AGENT_ID"))
end


-- 改变角色的战斗能力
function BSFunc.ChangeBattleAbility(InEntity, bOpen)
    if (InEntity == nil) then
        return
    end

    if (bOpen == true) then
        -- 本地角色
        if (Game.me and Game.me.eid == InEntity.eid) then
            -- 创建并激活锁定组件
            -- if (InEntity.LockComp == nil) then
            --     BSLC_Lock.new(InEntity, "LockComp")
            -- end
            -- InEntity.LockComp:ActivateBattleLogic(InEntity)
            InEntity:ActiveLockTargetComponent(true)
            
            -- if (InEntity.ProfessionComp == nil) then
            --     if InEntity.Profession == 1200002 then
            --         -- BSLC_Utopian.new(InEntity, "ProfessionComp")
            --        
            --     else
            --         -- BSLC_Profession.new(InEntity, "ProfessionComp")
            --     end
            -- end
            -- InEntity.ProfessionComp:ActivateBattleLogic(InEntity)
        end

        -- 重建属性
        Game.me.remote.ReqRebuildAttr((InEntity ~= Game.me), InEntity.eid)
    else

        -- 本地角色
        if (Game.me and Game.me.eid == InEntity.eid) then
            -- 失活职业组件
            -- if (InEntity.ProfessionComp ~= nil) then
            --     InEntity.ProfessionComp:DeActivateBattleLogic()
            -- end

            -- -- 失活锁定组件
            -- -- if (InEntity.LockComp ~= nil) then
            -- --     InEntity.LockComp:DeActivateBattleLogic()
            -- -- end
            InEntity:ActiveLockTargetComponent(false)
        end
    end
end

-- endregion Character

-- region Camp
-- TODO: Deprecated 检查目标阵营
function BSFunc.CheckTargetCamp(InOEntity, InTEntity, Camps)
    if (InOEntity == nil) or (InTEntity == nil) then
        return false
    end

    if (Camps <= 0) then
        return true
    end
    
    -- 获取Owner的阵营
    local OwnerEID = InOEntity.FinalOwnerID
    local OwnerTID = InOEntity.ITeamID or InOEntity.teamID
    local OwnerGID = InOEntity.IgroupID or InOEntity.groupID
    local OwnerGTID = InOEntity.groupTeamID

    -- 获取目标的阵营
    local TargetEID = InTEntity.FinalOwnerID
    local TargetTID = InTEntity.ITeamID or InTEntity.teamID
    local TargetGID = InTEntity.IgroupID or InTEntity.groupID
    local TargetGTID = InTEntity.groupTeamID

    -- 获取阵营关系
	local OEntityI = BSFunc.GetActorInstigator(InOEntity)
	local OTEntityI = BSFunc.GetActorInstigator(InTEntity)
    local CampRelation = battleUtils.GetTargetRealCampRelation(OEntityI, OTEntityI)

    --检查目标的阵营类型
    local CurIt = 0
    while (Camps > 0) do
        if (Camps % 2 == 1) then
            --检查是否是自己
            if (CurIt == ETE.EBSTargetCampType.TCT_Self) then
                if (OwnerEID == TargetEID) then
                    return true
                end
            --检查是否是小队队友
            elseif (CurIt == ETE.EBSTargetCampType.TCT_SquadMate) then
                if (OwnerEID ~= TargetEID) and (CampRelation == Enum.ECampEnumData.Friendly) then
                    if (OwnerTID and OwnerTID ~= 0 and OwnerTID == TargetTID) or (OwnerGTID and OwnerGTID ~= 0 and OwnerGTID == TargetGTID) then
                        return true
                    end
                end
            --检查是否是团队队友
            elseif (CurIt == ETE.EBSTargetCampType.TCT_TeamMate) then
                if (OwnerEID ~= TargetEID) and (CampRelation == Enum.ECampEnumData.Friendly)
                    and (OwnerGID ~= 0 and OwnerGID == TargetGID) and OwnerGTID ~= TargetGTID then
                    return true
                end
            --检查是否是友好
            elseif (CurIt == ETE.EBSTargetCampType.TCT_Allies) then
                if (OwnerEID ~= TargetEID) and (CampRelation == Enum.ECampEnumData.Friendly) then
                    return true
                end
            --检查是否中立
            elseif (CurIt == ETE.EBSTargetCampType.TCT_Neutral) then
                if (OwnerEID ~= TargetEID) and (CampRelation == Enum.ECampEnumData.Neutral) then 
                    return true
                end
            --检查是否敌对
            elseif (CurIt == ETE.EBSTargetCampType.TCT_Enemy) then
                if (OwnerEID ~= TargetEID) and (CampRelation == Enum.ECampEnumData.Enemy) then
                    return true
                end
            end
        end

        Camps = Camps >> 1
        CurIt = CurIt + 1
    end

    return false
end

-- TODO: 这里与服务器的判断逻辑不一致, 需统一
function BSFunc.CheckTargetCampNew(InOEntity, InTEntity, Camps)
    local OEntityI = BSFunc.GetActorInstigator(InOEntity)
    if (OEntityI == nil) or (InTEntity == nil) then
        return false
    end

    -- no check
    if not Camps or (Camps <= 0) then
        return true
    end

    if not BSFunc.CheckSelf(InOEntity, InTEntity, Camps) then
        return false
    end
    Camps = bit.band(Camps, bit.bnot(EFactionType.SelfExclusive))

    if not BSFunc.CheckFaction(InOEntity, InTEntity, Camps) then
		local OEntityT = BSFunc.GetActorInstigator(InTEntity)
		if OEntityT == InTEntity or not BSFunc.CheckFaction(OEntityI, OEntityT, Camps) then
			return false
		end
    end

    return true
end

---@private
function BSFunc.CheckSelf(InOEntity, InTEntity, FactionMask)
    if FactionMask == 0 then
        return true
    end

    if bit.band(EFactionType.SelfExclusive, FactionMask) > 0 and (InOEntity.eid ~= InTEntity.eid) then
        return false
    end

    return true
end

---@private
function BSFunc.CheckFaction(InOEntity, InTEntity, FactionMask)
    -- not check
    if FactionMask == 0 then
        return true
    end

    -- 检查层次关系
    if BSFunc.CheckHierarchyRelation(InOEntity, InTEntity, FactionMask) then
        return true
    end

    -- 获取阵营关系
	if BSFunc.CheckValidFaction(InOEntity, InTEntity, FactionMask) then
		return true
	end

    return false
end

---@pirvate
function BSFunc.CheckHierarchyRelation(InOEntity, InTEntity, FactionMask)
    if bit.band(EFactionType.Self, FactionMask) > 0 and (InOEntity.FinalOwnerID == InTEntity.FinalOwnerID) then
        return true
    end

    -- TODO: SelfSibling

    return false
end

---@pirvate
function BSFunc.CheckValidFaction(InOEntity, InTEntity, FactionMask)
    local HierarchyMask = bit.bor(EFactionType.Self, EFactionType.SelfSibling)
    FactionMask = bit.band(FactionMask, bit.bnot(HierarchyMask))

    local CampRelation = BSFunc.GetFinalCampRelationNew(InOEntity, InTEntity)
    return (FactionMask >= CampRelation and bit.band(CampRelation, FactionMask) > 0)
end

function BSFunc.GetFinalCampRelationNew(InOEntity, InTEntity)
    local EOwnerI = BSFunc.GetActorInstigator(InOEntity)

    -- 找不到Entity的情况，暂时使用中立关系
    if (EOwnerI == nil) then
        return EFactionType.Neutral
    end

    -- 找不到Entity的情况，暂时使用中立关系
    if (InTEntity == nil) then
        return EFactionType.Neutral
    end
    -- 精简的阵营关系（0友好，1敌对，2中立）
    local SimplifyCampRelation = BSFunc.GetFinalCampRelationByEntity(EOwnerI, InTEntity)
    -- 获取Owner的阵营
    local OwnerTID = InOEntity.ITeamID or InOEntity.teamID
    local OwnerGID = InOEntity.IgroupID or InOEntity.groupID

    -- 获取目标的阵营
    local TargetTID = InTEntity.ITeamID or InTEntity.teamID
    local TargetGID = InTEntity.IgroupID or InTEntity.groupID

    if (SimplifyCampRelation == Enum.ECampEnumData.Enemy) then
        return EFactionType.Enemy
    elseif (SimplifyCampRelation == Enum.ECampEnumData.Neutral) then
        return EFactionType.Neutral
    else
        if (OwnerTID ~= nil and TargetTID ~= nil and OwnerTID == TargetTID) then
            return EFactionType.TeamMate
        elseif (OwnerGID ~= nil and TargetGID ~= nil and OwnerGID == TargetGID) then
            return EFactionType.GroupMate
        else
            return EFactionType.Allies
        end
    end
end

-- 获取目标间的各类CampID的阵营关系信息（0友好，1敌对，2中立）
function BSFunc.GetFinalCampRelation(InOEntity, InTEntity)
    local EOwnerI = BSFunc.GetActorInstigator(InOEntity)
    return BSFunc.GetFinalCampRelationByEntity(EOwnerI, InTEntity)
end

function BSFunc.GetFinalCampRelationByEntity(EOwnerI, InTEntity)
    -- 找不到Entity的情况，暂时使用友好关系
    if (EOwnerI == nil) then
        return Enum.ECampEnumData.Friendly
    end

    -- 找不到Entity的情况，暂时使用友好关系
    if (InTEntity == nil) then
        return Enum.ECampEnumData.Friendly
    end

	if (InTEntity == EOwnerI) then
		return Enum.ECampEnumData.Friendly
	end
	
    return battleUtils.GetTargetRealCampRelation(EOwnerI, InTEntity)
end

-- -- 获取角色阵营
function BSFunc.GetActorCamp(InEntity)
    if (InEntity == nil) then
        return 0
    end

    if (InEntity.ICamp ~= nil) then
        return InEntity.ICamp
    end

    return InEntity.Camp
end

-- -- 获取角色TeamCamp相关信息（teamid，teamcamp）
-- function BSFunc.GetActorTeamCampInfo(InEntity)
--     if (InEntity == nil) then
--         return "", 0
--     end

--     if (InEntity.ITeamID ~= nil) then
--         return InEntity.ITeamID or "", InEntity.ITeamCamp or 0
--     end

--     return InEntity.teamID, InEntity.TeamCamp
-- end

-- -- 获取角色GroupCamp相关信息（GroupTeamUid，GroupTeamCamp, groupID, GroupCamp）
-- function BSFunc.GetActorGroupCampInfo(InEntity)
--     if (InEntity == nil) then
--         return "", 0, "", 0
--     end


-- 获取CampOverride信息(IntCampOverrideList, StringCampOverrideList)
function BSFunc.GetActorCampOverrideInfo(InEntity)
    if (InEntity == nil) then
        return {}, {}
    end

    if (InEntity.IIntCampOverrideList ~= nil) then
        return InEntity.IIntCampOverrideList or {}, InEntity.IStringCampOverrideList or {}
    end

    return InEntity.IntCampOverrideList, InEntity.StringCampOverrideList
end

function BSFunc.AddCampCache(InOwnerEntityId, InTargetEntityId, InRelation)
    local OwnerEntity = Game.EntityManager:getEntity(InOwnerEntityId)
    local TargetEntity = Game.EntityManager:getEntity(InTargetEntityId)
    if not OwnerEntity or not TargetEntity then
        return
    end
    OwnerEntity.OwnerToTargetCampCache[InTargetEntityId] = InRelation
    TargetEntity.CampCacheBeRelatedMap[InOwnerEntityId] = true
end

function BSFunc.RemoveCampCache(InOwnerEntityId)
    if InOwnerEntityId == nil then
        return
    end
    local OwnerEntity = Game.EntityManager:getEntity(InOwnerEntityId)
    if OwnerEntity then
        for TargetEntityID, Relation in pairs(OwnerEntity.OwnerToTargetCampCache) do
            local TargetEntity = Game.EntityManager:getEntity(TargetEntityID)
            if TargetEntity then
                TargetEntity.CampCacheBeRelatedMap[InOwnerEntityId] = nil
            end
        end
        for RelatedEntityID, _ in pairs(OwnerEntity.CampCacheBeRelatedMap) do
            local RelatedEntity = Game.EntityManager:getEntity(RelatedEntityID)
            if RelatedEntity then
                RelatedEntity.OwnerToTargetCampCache[InOwnerEntityId] = nil
            end
            
        end
        table.clear(OwnerEntity.OwnerToTargetCampCache)
        table.clear(OwnerEntity.CampCacheBeRelatedMap)
    end
end

function BSFunc.CheckCampCache(InOwnerEntityId, InTargetEntityId)
    if InOwnerEntityId == nil or InTargetEntityId == nil then
        return nil
    end
    local OwnerEntity = Game.EntityManager:getEntity(InOwnerEntityId)
    local TargetEntity = Game.EntityManager:getEntity(InTargetEntityId)
    if OwnerEntity and TargetEntity then
        return OwnerEntity.OwnerToTargetCampCache[InTargetEntityId] 
    end
    return nil
end


function BSFunc.ReceiveProtectionSettingChanged()
    BSFunc.RemoveCampCache(GetMainPlayerEID())
end

function BSFunc.ReceiveCharacterLevelChanged(InEntityID)
    BSFunc.RemoveCampCache(InEntityID)
end

function BSFunc.ReceiveCharacterCampInfoChanged(InEntityID)
    BSFunc.RemoveCampCache(InEntityID)
end

-- endregion Camp






-- region Attack Redistribution
-- 默认攻击分配
function BSFunc.DefaultAttack(TargetEIDs, AttackData, OutResult)
    table.clear(OutResult)

    local Size = #TargetEIDs
    if (Size <= 0) then
        return
    end

    if (AttackData.AttackTargetNumber > 0) then
        Size = math.min(AttackData.AttackTargetNumber, Size)
    end

    for i = 1, Size do
        OutResult[i] = TargetEIDs[i]
    end
end

-- 平均攻击分配
function BSFunc.AverageAttack(TargetEIDs, AttackData, OutResult)
    table.clear(OutResult)

    local Size = #TargetEIDs
    if (Size <= 0) then
        return
    end

    local Index = 0
    local SingleAttackMinTimes = AttackData.AttackTargetNumber / Size
    local AttackTargetNumber = AttackData.AttackTargetNumber - Size * SingleAttackMinTimes
    -- 先平均分配
    for i = 1, SingleAttackMinTimes do
        for j = 1, Size do
            Index = Index + 1
            OutResult[Index] = TargetEIDs[j]
        end
    end

    -- 剩余次数继续添加
    for i = 1, AttackTargetNumber do
        Index = Index + 1
        OutResult[Index] = TargetEIDs[i]
    end
end

-- 随机攻击分配
function BSFunc.RandomAttack(TargetEIDs, AttackData, OutResult)
    table.clear(OutResult)

    local Size = #TargetEIDs
    if (Size <= 0) then
        return
    end

    local Index = 0
    for i = 1, AttackData.AttackTargetNumber do
        local Random = math.floor(math.random(1, Size) + 0.5)
        Index = Index + 1
        OutResult[Index] = TargetEIDs[Random]
    end
end

-- 有限制的随机攻击分配
local LT_TempTEIDs = {}                    -- luacheck: ignore
function BSFunc.RandomAttackWithLimit(TargetEIDs, AttackData, OutResult)
    table.clear(OutResult)

    local Size = #TargetEIDs
    if (Size <= 0) then
        return
    end

    local Index = 0
    table.clear(LT_TempTEIDs)
    for i = 1, Size do
        LT_TempTEIDs[i] = TargetEIDs[i]
    end

    if (Size > 0) then
        local AllowAttackTime = {}
        for i = 1, Size do
            AllowAttackTime[i] = AttackData.TargetOnAttackLimit
        end

        for i = 1, AttackData.AttackTargetNumber do
            if (Size <= 0) then
                break
            end

            local Random = math.floor(math.random(1, Size) + 0.5)
            AllowAttackTime[Random] = AllowAttackTime[Random] - 1

            -- 添加到结果列表中
            Index = Index + 1
            OutResult[Index] = LT_TempTEIDs[Random]

            -- 超过限制,从列表中移除
            if (AllowAttackTime[Random] <= 0) then
                Size = Size - 1
                table.remove(LT_TempTEIDs, Random)
            end
        end
    end
end

function BSFunc.GenRandomPosInAnnulus(posNumber, minRadius, maxRadius)
	local posResult = {}
	local angleUnit = math.rad(360 / posNumber)
    local randomRotateAngle = math.random() * angleUnit
    local preAngle  = 0
    local minRadiusSquare = minRadius * minRadius
    local maxRadiusSquare = maxRadius * maxRadius

	for id = 1, posNumber do
		local randomRadius = math.random() * (maxRadiusSquare - minRadiusSquare) + minRadiusSquare
        randomRadius = math.sqrt(randomRadius) 
		local randomAngle = randomRotateAngle + preAngle
		local cartePos = BSFunc.TransformPolar2Carte(randomRadius, randomAngle)
        table.insert(posResult, cartePos)

        preAngle = preAngle + angleUnit
	end

	return posResult
end

function BSFunc.TransformPolar2Carte(radius, angle)
    local x = radius * math.cos(angle)
    local y = radius * math.sin(angle)
    return {X = x, Y = y}
end
-- endregion Attack Redistribution






-- endregion OperateStack






-- region SkillCommand
function BSFunc.MakeSkillCommand(InSkillSlot, ExtraData)
	---@class SkillCommand
    local outSkillCommand = {
        SlotType = InSkillSlot,   -- 按键类型EBSSkillSlot
        SkillID = -1,             -- 技能ID
        InstigatorUID = 0,        -- 技能释放者
        LockTargetUID = 0,        -- 锁定对象EID
        PathFollowUID = 0,        -- 寻路目标
        Transform = M3D.Transform(), -- 技能坐标信息
		ComboState = nil,         -- 连招状态
        TriggerUID = 0,           -- 技能触发者
        bHasExecuted = false,     -- 该指令是否已经执行过
    }

    if type(ExtraData) == "table" then
        for name, Value in pairs(ExtraData) do
            outSkillCommand[name] = Value
        end
    end

    return outSkillCommand
end

function BSFunc.ResetSkillCommand(InSkillCommand, InSkillSlot, ExtraData)
    InSkillCommand.SlotType = InSkillSlot
    InSkillCommand.bCancelSkill = false
    InSkillCommand.SkillID = -1
    InSkillCommand.InstigatorUID = 0
    InSkillCommand.LockTargetUID = 0
    InSkillCommand.PathFollowUID = 0
	InSkillCommand.Transform:Reset()
	InSkillCommand.ComboState = nil
    InSkillCommand.TriggerUID = 0
    InSkillCommand.bHasExecuted = false

    if type(ExtraData) == "table" then
        for name, Value in pairs(ExtraData) do
            InSkillCommand[name] = Value
        end
    end

    return InSkillCommand
end
-- endregion SkillCommand






-- region AbilityLevelRelateData
function BSFunc.GetSkillLvlRelatedDataValue(skillId, skillLvl, fieldName)
    --local dataRow = SLDT[skillId]
    --if not dataRow then
    --    return nil
    --end
    --
    --local fieldData = dataRow[fieldName]
    --if fieldData == nil then
    --    return nil
    --end
    --
    --local dataFillType = fieldData[1]
    --if dataFillType == LDE.EDataFillType.Number then
    --    return fieldData[2]
    --elseif dataFillType == LDE.EDataFillType.LvlValueArr then
    --    return fieldData[2][skillLvl]
    --elseif dataFillType == LDE.EDataFillType.Formula then
    --    local formulaName = fieldData[2]
    --
    --    -- lvl as the first param
    --    local status, value = FormulaManager.CallFormula(formulaName, skillLvl)
    --    if status == true then
    --        return value
    --    end
    --end

    return nil
end

function BSFunc.GetSkillCDValue(skillId, skillLvl)
	local Val = BSFunc.GetSkillLvlRelatedDataValue(skillId, skillLvl, "CD")
	if not Val then
		local SkillData = Game.TableData.GetSkillDataNewRow(skillId)
		if SkillData then
			Val = SkillData.CD
		end
	end
    return Val or 0.0
end

function BSFunc.GetSkillCastTimesValue(skillId, skillLvl)
    return math.floor(BSFunc.GetSkillLvlRelatedDataValue(skillId, skillLvl, "CastTimes") or 0)
end

function BSFunc.GetBuffDispelLevelValue(skillID, skillLvl)
    return BSFunc.GetSkillLvlRelatedDataValue(skillID, skillLvl, "DispelValue") or -1
end
-- endregion AbilityLevelRelateData






-- region WorkProxy
function BSFunc.FillWPCollisionData(InStrategy, InWPCD)
    if (InStrategy == nil) or (InWPCD == nil) then
        return
    end

    -- 填充碰撞通道
    InWPCD.ObjectTypes:Clear()
    for _, Type in ipairs(InStrategy.ObjectTypes) do
        InWPCD.ObjectTypes:Add(Type)
    end

    -- 填充是否只检测根组件位置
    InWPCD.bOnlyRoot = InStrategy.bOnlyRoot

    -- 填充碰撞尺寸、类型
    if (InStrategy.CircleSize ~= nil) then
        InWPCD.CollisionType = 0
        InWPCD.CollisionSize.X = InStrategy.CircleSize.X
        InWPCD.CollisionSize.Y = InStrategy.CircleSize.Y
    elseif (InStrategy.RectSize ~= nil) then
        InWPCD.CollisionType = 1
        InWPCD.CollisionSize.X = InStrategy.RectSize.X
        InWPCD.CollisionSize.Y = InStrategy.RectSize.Y
    elseif (InStrategy.Fan2DSize ~= nil) then
        InWPCD.CollisionType = 2
        InWPCD.CollisionSize.X = InStrategy.Fan2DSize.X
        InWPCD.CollisionSize.Y = InStrategy.Fan2DSize.Y
        InWPCD.CollisionSize.Z = InStrategy.Fan2DSize.Z
    end

    -- 填充筛选参数
    BSFunc.AssembleCollisionFilter(InStrategy.Filter, InWPCD.CollisionFilter)
end
-- endregion WorkProxy






-- region Movement
function BSFunc.ExecuteMovementTable(InTable, InEntity1, InEntity2, InLocation, InSyncSign, InRate, InNeedRPC, InAnimRootMotionCurveID, CurveGUID, StaticBlackBoard, InScale)
    if (InTable == nil) then
        return 0
    end

    if (InTable.DestType == 4) or (InTable.DestType == 5) or (InTable.DestType == 6) or (InTable.DestType == 7) or (InTable.DestType == 8) then
        --目标移动客户端不先行，这边先注释了
    else
        if (InEntity1 ~= nil) and (InEntity1.ExecuteMovementTable ~= nil) then
            return InEntity1:ExecuteMovementTable(InTable, InEntity2, InLocation, InSyncSign, InRate, InNeedRPC, InAnimRootMotionCurveID, CurveGUID, StaticBlackBoard, InScale), InEntity1
        end
    end

    return 0, nil
end
-- endregion Movement

--- 检查目标是否在释放技能的范围内
--- @param inTargetUID integer 目标UID
--- @param inSkillID integer 技能ID
--- @param inFurtherDist number 额外距离
--- @return boolean 是否在范围内
function BSFunc.CheckTargetInReleaseRange(inTargetUID, inSkillID, inFurtherDist)
	if inTargetUID == nil or inTargetUID <= 0 then
		return false
	end

	local SkillData = Game.TableData.GetSkillDataNewRow(inSkillID)
	local baseDist = SkillData["Dist"]
	if type(baseDist) ~= "number" then
		return true
	end

	local targetEntity = Game.EntityManager:getEntity(inTargetUID)
	if not targetEntity then
		return false
	end

	local targetPos = M3D.Vec3()
	targetPos:Pack(targetEntity:GetPosition_P())
	local playerPos = M3D.Vec3()
	playerPos:Pack(Game.me:GetPosition_P())

	local distance = M3D.GetDistance3D(targetPos, playerPos)  -- 改为直接计算距离

	if SkillData["IsDistanceIncludesCollisionRadius"] then
		local CapRadius = SelectU.GetCapsuleRadius(targetEntity, 0)
		baseDist = baseDist + CapRadius
	end

	local maxDistance = baseDist + inFurtherDist
	return distance <= maxDistance
end