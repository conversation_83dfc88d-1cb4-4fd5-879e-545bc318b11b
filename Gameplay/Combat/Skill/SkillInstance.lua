-- luacheck: push ignore
local bit = require("Framework.Utils.bit")
local AbilityConst = kg_require("Shared.Const.AbilityConst")
local ESkillType = AbilityConst.ESkillType
local EReleaseSkillResult = kg_require("Shared.Const.AbilityConst").EReleaseSkillResult
local SwitchUtils = kg_require("Shared.SwitchUtils")

---@class SkillInstance
SkillInstance = DefineClass("SkillInstance")

-- 最大进入后摇时间
MAX_ENTER_BACK_SWING_TIME = 10

--- 单个buff管理
function SkillInstance:ctor()
    self.skillID = 0
    self.level = 0
    self.entity = nil
    self.isRunning = false
	-- 是否客户端先行
	self.isClientPreCast = false

    self.isBackSwing = true
    self.startTimestamp = 0
    self.backSwingSkillList = nil
    self.backSwingSkillTypes = 0
    self.backSwingSkillTags = nil
	
	self.disableLocoMove = 0
	self.disableLocoJump = 0
	self.disableLocoDodge = 0
	self.disableLocoStart = 0
	self.disableLocoRotate = 0
end

function SkillInstance:dtor()
    self.entity = nil
	table.clear(self.locoMoveDisableCache)
end

function SkillInstance:GetTableData()
    return Game.TableData.GetSkillDataNewRow(self.skillID)
end

function SkillInstance:Init(skillID, skillLevel, entity)
    self.skillID = skillID
    self.level = skillLevel
    self.entity = entity
    self.rootSkillID = skillID
    self.rootBuffID = nil
	self.recordedActionType = nil
end

function SkillInstance:Start(lockTarget, inputPos, inputDir, stateID, insID, startTimestamp)
	self.FinishReason = ETE.EBSAFinishReason.FR_EndOfLife
    self.startTimestamp = startTimestamp or _G._now()
    self.backSwingSkillList = nil
    self.backSwingSkillTypes = 0
    self.backSwingSkillTags = nil
    self.lockTarget = lockTarget
	if Game.BSManager.bIsInEditor == true then
		self.instigatorID = lockTarget
	else
		self.instigatorID = self.entity.FinalOwnerID or self.entity:uid()
	end
    self.inputPos = inputPos
    self.inputDir = inputDir
    self.isRunning = true
	if not insID then
		insID = self.entity:GetCombatInsID()
		self.isClientPreCast = true
	end
	self.insID = insID
	self.EffectPriority = nil
	local ConfigData = self:GetTableData()
	if ConfigData then
		self.EffectPriority = ConfigData.EffectPriority
	end
	self.entity:StartAbilityRunner(self.skillID, self, nil, stateID, startTimestamp)
	self:SetIsBackSwing(false)
	Game.BSManager.runningSkillCount = Game.BSManager.runningSkillCount + 1
end

function SkillInstance:Stop(reason)
    self.FinishReason = reason
    self:SetIsBackSwing(true)
    self.backSwingSkillList = nil
    self.backSwingSkillTypes = 0
    self.backSwingSkillTags = nil
	self.insID = 0
    self.isRunning = false
	self.entity:StopAbilityRunner(self.skillID)
    if self.entity.AnimSkillID == self.skillID then
        self.entity:StopSkillAnimation()
        self.entity.AnimSkillID = nil
    end
	Game.BSManager.runningSkillCount = Game.BSManager.runningSkillCount - 1
end

function SkillInstance:OnAbilityEnd()
    self.entity:StopSkillInstance(self.skillID, ETE.EBSAFinishReason.FR_EndOfLife)
end

function SkillInstance:OnAbilityChangeState(stateID)
    self:SetIsBackSwing(false)
	--todo 优化数据导出
	if self.entity == Game.me then
		local skillData = self:GetTableData()
		if skillData.ComboSkill and skillData.ComboSkill > 0 then
			self.entity:OnComboWindowSkillStart(self.skillID, self.comboTreeNode, {StartTime = skillData.ComboPoint, ComboDuration = skillData.ComboInRecover, ShowCountDown = false, ShowHighLight = false})
		end
	end
end

function SkillInstance:CheckLocomotionDisable()
	local skillData = self:GetTableData()
	if skillData and skillData.CanCastParallel then
		return
	end
	if self.isBackSwing then
		-- 进入后摇
		self:EnableLocoJump(true)
		self:EnableLocoDodge(true)
		self:EnableLocoMove(true)
		self:EnableLocoMoveByLocoStart(true)
		self:EnableLocoRotate(true)
	else
		-- 非后摇期间
		self:EnableLocoJump(false)
		self:EnableLocoDodge(false)
		self:EnableLocoMove(false)
		self:EnableLocoMoveByLocoStart(false)
		self:EnableLocoRotate(false)
	end
end

function SkillInstance:EnableLocoMove(isEnable)
	if self.entity.bUnit or self.entity:GetEnableLocoControlSwitch() == false then
		return
	end
	if isEnable then
		self.disableLocoMove = self.disableLocoMove - 1
		if self.disableLocoMove == 0 then
			self.entity:DisableLocoMove(Enum.ELocoControlTag.Skill, false, false)
		end
	else
		self.disableLocoMove = self.disableLocoMove + 1
		if self.disableLocoMove == 1 then
			self.entity:DisableLocoMove(Enum.ELocoControlTag.Skill, true, false)
		end
	end
end

function SkillInstance:EnableLocoMoveByLocoStart(isEnable)
	if self.entity.bUnit or self.entity:GetEnableLocoControlSwitch() == false then
		return
	end
	if isEnable then
		self.disableLocoStart = self.disableLocoStart - 1
		if self.disableLocoStart == 0 then
			self.entity:DisableLocoMoveByLocoStart(Enum.ELocoControlTag.Skill, false, false)
		end
	else
		self.disableLocoStart = self.disableLocoStart + 1
		if self.disableLocoStart == 1 then
			self.entity:DisableLocoMoveByLocoStart(Enum.ELocoControlTag.Skill, true, false)
		end
	end
end

function SkillInstance:EnableLocoRotate(isEnable)
	if self.entity.bUnit or self.entity:GetEnableLocoControlSwitch() == false then
		return
	end
	if isEnable then
		self.disableLocoRotate = self.disableLocoRotate - 1
		if self.disableLocoRotate == 0 then
			self.entity:DisableLocoRotate(Enum.ELocoControlTag.Skill, false, false)
		end
	else
		self.disableLocoRotate = self.disableLocoRotate + 1
		if self.disableLocoRotate == 1 then
			self.entity:DisableLocoRotate(Enum.ELocoControlTag.Skill, true, false)
		end
	end
end

function SkillInstance:EnableLocoJump(isEnable)
	if self.entity.bUnit or self.entity:GetEnableLocoControlSwitch() == false then
		return
	end
	if isEnable then
		self.disableLocoJump = self.disableLocoJump - 1
		if self.disableLocoJump == 0 then
			self.entity:DisableLocoJump(Enum.ELocoControlTag.Skill, false, false)
		end
	else
		self.disableLocoJump = self.disableLocoJump + 1
		if self.disableLocoJump == 1 then
			self.entity:DisableLocoJump(Enum.ELocoControlTag.Skill, true, false)
		end
	end
end

function SkillInstance:EnableLocoDodge(isEnable)
	if self.entity.bUnit or self.entity:GetEnableLocoControlSwitch() == false then
		return
	end
	if isEnable then
		self.disableLocoDodge = self.disableLocoDodge - 1
		if self.disableLocoDodge == 0 then
			self.entity:DisableLocoDodge(Enum.ELocoControlTag.Skill, false, false)
		end
	else
		self.disableLocoDodge = self.disableLocoDodge + 1
		if self.disableLocoDodge == 1 then
			self.entity:DisableLocoDodge(Enum.ELocoControlTag.Skill, true, false)
		end
	end
end

-- 设置后摇开始和结束
function SkillInstance:SetIsBackSwing(isBackSwing)
    if self.isBackSwing == isBackSwing then
        return
    end
	
    self.isBackSwing = isBackSwing
	self:CheckLocomotionDisable()
end

function SkillInstance:AddBackSwingSkillList(SkillList, SkillTypes, SkillTags)
    if SkillList and #SkillList > 0 then
        if not self.backSwingSkillList then
            self.backSwingSkillList = {}
        end
        local backSwingSkillList = self.backSwingSkillList
        for _, skillID in ipairs(SkillList) do
            backSwingSkillList[#backSwingSkillList + 1] = skillID
        end
    end
    if SkillTypes and SkillTypes > 0 then
        self.backSwingSkillTypes = self.backSwingSkillTypes | SkillTypes
		if bit.band(ESkillType.JumpSkill, self.backSwingSkillTypes) > 0 then
			self:EnableLocoJump(true)
		end
		if bit.band(ESkillType.DodgeSkill, self.backSwingSkillTypes) > 0 then
			self:EnableLocoDodge(true)
		end
    end

    if SkillTags and SkillTags ~= 0 and ksbcnext(SkillTags) then
        if not self.backSwingSkillTags then
            self.backSwingSkillTags = {}
        end
        for _, tag in ipairs(SkillTags) do
            self.backSwingSkillTags[tag] = true
        end
    end
end

-- 技能能被打断
function SkillInstance:CanBeInterrupt(skillTableData)
    local skillID = skillTableData.ID
    if (_G._now() - self.startTimestamp) / 1000.0 > MAX_ENTER_BACK_SWING_TIME then
        return true
    end
    if self.isBackSwing then
        return true
    end
    if self.backSwingSkillList and table.isInArray(self.backSwingSkillList, skillID) then
        return true
    end
    if self.backSwingSkillTypes > 0 and bit.band(self.backSwingSkillTypes, skillTableData.Type) > 0 then
        return true
    end
    if self.backSwingSkillTags and next(self.backSwingSkillTags) and skillTableData.Tags then
        for _, tag in ipairs(skillTableData.Tags) do 
            if self.backSwingSkillTags[tag] then
                return true
            end
        end
    end
    return false
end