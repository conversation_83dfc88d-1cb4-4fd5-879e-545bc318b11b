local CombatEffectUtils = kg_require("Gameplay.Combat.CombatEffect.CombatEffectUtils")


EffectConditionFactory = EffectConditionFactory or {}
EffectConditionFactory.conditionMap = {}
EffectConditionFactory.conditionToEventMap = {}    -- 保存条件关联的事件，当事件变化时候，条件需要重新判定

function EffectConditionFactory.CheckCondition(conditionType, entity, configData, staticBlackboard, targetEID)
    local checkFunc = EffectConditionFactory.conditionMap[conditionType]
    if checkFunc then
		local result = checkFunc(entity, configData, staticBlackboard, targetEID)
        if configData and configData["ConditionNotFlag"] then
            return not result
        else
            return result
        end
		--LOG_ERROR_FMT("Condition conditionMap xpcall failed, conditionType: %s", conditionType)
    else
        if Game.BSManager.bIsInEditor then
            return true
        end
        
        LOG_ERROR_FMT("Condition Not Exist, conditionType: %s", conditionType)
    end
    return true
end

function EffectConditionFactory.CheckConditionWithTargetType(conditionType, conditionTargetType, entity, configData, staticBlackboard, eventType, ...)
    local conditionTargetID, conditionTargetIDList = CombatEffectUtils.GetConditionTargetID(entity, conditionTargetType, staticBlackboard, eventType, ...)
    if not EffectConditionFactory.CheckCondition(conditionType, entity, configData,
            staticBlackboard, conditionTargetID, conditionTargetIDList) then
        return false
    end
    return true
end

-- 获取条件列表中相关联的事件
function EffectConditionFactory.GetEventByConditionList(conditionList)
    if not conditionList then
        return nil
    end
    for _, condition in ipairs(conditionList) do
        local eventType = EffectConditionFactory.conditionToEventMap[condition.ConditionType]
        if eventType then
            return eventType
        end
    end
end

function EffectConditionFactory.Register(conditionType, condition, eventType)
    if conditionType == nil then
        Log.Error("Register conditionType is nil")
        return
    end

    EffectConditionFactory.conditionMap[conditionType] = condition.CheckCondition
    EffectConditionFactory.conditionToEventMap[conditionType] = eventType
end

