local sharedConst = kg_require("Shared.Const")
local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE


-----------------------------------------------------------
--- 提供统一的Task执行接口
-----------------------------------------------------------
EffectTaskFactory = EffectTaskFactory or {}

-- 保存所有Task执行的接口
EffectTaskFactory.taskExecuteMap = {}
-- 保存所有Task执行结束的接口
EffectTaskFactory.taskEndMap = {}
-- 保存所有Task Layer变化的接口
EffectTaskFactory.taskOnLayerChangeMap = {}
-- 保存所有Task Speed变化的接口
EffectTaskFactory.taskOnSpeedChangeMap = {}
-- 保存所有Task 状态数据类型
EffectTaskFactory.taskStateDataTypeMap = {}


function EffectTaskFactory.ExecuteTask(taskType, entity, configData, staticBlackboard, targetEID, targetEIDList, dynamicBlackBoard)
    local execFunc = EffectTaskFactory.taskExecuteMap[taskType]
    if execFunc then
        xpcall(execFunc, _G.CallBackError, entity, configData, staticBlackboard, targetEID, targetEIDList, dynamicBlackBoard)
    end
end

function EffectTaskFactory.ExecuteTaskEnd(taskType, entity, configData, staticBlackboard, targetEID, targetEIDList, dynamicBlackBoard)
    local endFunc = EffectTaskFactory.taskEndMap[taskType]
    if endFunc then
        xpcall(endFunc, _G.CallBackError, entity, configData, staticBlackboard, targetEID, targetEIDList, dynamicBlackBoard)
    end
end

function EffectTaskFactory.ExecuteTaskLayerChange(taskType, entity, configData, staticBlackboard, oldLayer, newLayer,
                                                  targetEID, taskEIDList, dynamicBlackboard)
    local layerChangeFunc = EffectTaskFactory.taskOnLayerChangeMap[taskType]
    if layerChangeFunc then
        xpcall(layerChangeFunc, _G.CallBackError, entity, configData, staticBlackboard, oldLayer, newLayer, targetEID, taskEIDList, dynamicBlackboard)
    end
end

function EffectTaskFactory.ExecuteTaskSpeedChange(taskType, entity, configData, staticBlackboard, targetEID, taskEIDList, dynamicBlackboard)
    local speedChangeFunc = EffectTaskFactory.taskOnSpeedChangeMap[taskType]
    if speedChangeFunc then
        xpcall(speedChangeFunc, _G.CallBackError, entity, configData, staticBlackboard, targetEID, taskEIDList, dynamicBlackboard)
    end
end

function EffectTaskFactory.HasTask(taskType)
    if EffectTaskFactory.taskExecuteMap[taskType] ~= nil then
        return true
    end
    return false
end

function EffectTaskFactory.IsSpanTask(taskType)
    if EffectTaskFactory.taskEndMap[taskType] then
        return true
    end
    return false
end

function EffectTaskFactory.IsTaskWithState(taskType)
    return EffectTaskFactory.taskStateDataTypeMap[taskType] == TASK_STATE_DATA_TYPE.WITH_STATE
end

function EffectTaskFactory.Register(taskType, task, taskStateDataType)
    if taskType == nil then
        LOG_ERROR_FMT("EffectTaskFactory Register taskType is nil")
        return
    end

    if EffectTaskFactory.taskExecuteMap[taskType] ~= nil and not Game.RefreshScript and Game.BSManager.bIsInEditor == false then
        LOG_ERROR_FMT("EffectTaskFactory Register Repeat, taskType:%s", taskType)
        LOG_ERROR_FMT("EffectTaskFactory Register Repeat, trace{%s}", debug.traceback())
    end

    EffectTaskFactory.taskExecuteMap[taskType] = task.Execute
    EffectTaskFactory.taskEndMap[taskType] = task.End
    EffectTaskFactory.taskOnLayerChangeMap[taskType] = task.OnLayerChange
    EffectTaskFactory.taskOnSpeedChangeMap[taskType] = task.OnSpeedChange
    EffectTaskFactory.taskStateDataTypeMap[taskType] = taskStateDataType
end