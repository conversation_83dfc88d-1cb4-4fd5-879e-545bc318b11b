local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
--local Enum = Enum

--------------------------------------------------------
--- 瞬发类Task，需要实现Excute
--------------------------------------------------------
SpawnIceField = {}  -- luacheck: ignore
function SpawnIceField.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    entity:SpawnIceField(targetEID, configData.Translation, configData.Rotation, configData.Radius, configData.LastTime, 
            configData.DisappearEffect, configData.EffectTransform, 
            configData.DisappearAk, configData.AkTransform, configData.bNeedCheckGround)
end

SpawnLocalWind = {}  -- luacheck: ignore
function SpawnLocalWind.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

	if not entity:IsViewBudgetTagDownGradingBudgetPermit(WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.LOCO_ENVIRONMENT_INTERACT) and not Game.BSManager.bIsInEditor then
		return
	end
	
    entity:SpawnLocalWind(configData.WindType, configData.OffsetX, configData.OffsetY, configData.ScaleX, configData.ScaleY, 
            configData.Strength, configData.FanCenterAngle)
end

function RegisterTask()
    --EffectTaskFactory.Register(Enum.EffectTaskType.AddDecal, AddDecalTask)
    EffectTaskFactory.Register(Enum.EffectTaskType.SpawnIceField, SpawnIceField)
    EffectTaskFactory.Register(Enum.EffectTaskType.SpawnLocalWind, SpawnLocalWind)
end