UE = kg_require("Framework.KGFramework.KGCore.UE")

local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local TargetSelectorEx = kg_require("Shared.Combat.Selector.TargetSelectorEx")


local AbilityConst = kg_require("Shared.Const.AbilityConst")
local SharedConst = kg_require("Shared.Const")

local CombatEffectUtils = kg_require("Gameplay.Combat.CombatEffect.CombatEffectUtils")
local Enum = Enum
local DebugFlag = require("Gameplay.Debug.DebugFlag")
local LuaDebugProxy = require("Gameplay.Debug.LuaDebugProxy")
local DebugConst = kg_require("Gameplay.Debug.DebugConst")
local ABILITY_MONIOTOR_OPERATION = DebugConst.ABILITY_MONIOTOR_OPERATION
local MONITOR_COLOR = DebugConst.MONITOR_COLOR


local STATIC_BLACK_BOARD_KEY_TYPE = SharedConst.STATIC_BLACK_BOARD_KEY_TYPE
local EUseInputTransType = AbilityConst.EUseInputTransType

--------------------------------------------------------
--- 瞬发类Task，需要实现Execute
--------------------------------------------------------
-- 技能内的Attack，用于技能内调用，固定TaskType = 1
InnerAttackTaskType = 1  -- luacheck: ignore
InnerAttack = {}         -- luacheck: ignore
function InnerAttack.Execute(entity, configData, staticBlackBoard, targetEID, taskEIDList)
	if entity ~= Game.me then
		return
	end
	local skillID = configData.BindSkillID
	local skillData = Game.TableData.GetSkillDataNewRow(skillID)
	if not skillID then
		return
	end
	local _, OtherTargetList = TargetSelectorEx.QueryEntitiesByRule(
		entity, nil, skillData.AttackSelectorRuleData, staticBlackBoard)
	if OtherTargetList then
		for _, entityID in ipairs(OtherTargetList) do
			-- 触发交互物受到攻击事件
			local targetEntity = Game.EntityManager:getEntity(entityID)
			if targetEntity.OnAttacked then
                targetEntity:OnAttacked(entity)
			end

			-- 触发mass 交互
			if Game.MassCharacterManager and Game.MassCharacterManager:bMassLocalEntity(entityID) then
				targetEntity:OnAttackByPlayer()
			end
		end
	end
end

BackSwing = {} -- luacheck: ignore
function BackSwing.Execute(Entity, ConfigData, StaticBlackBoard)
    local skillIns = StaticBlackBoard
    if not skillIns.SetIsBackSwing then
        return
    end

    
    local SkillList = ConfigData.SkillList
    local SkillListJudge = (SkillList and #SkillList > 0) or (ConfigData.SkillTypes and ConfigData.SkillTypes > 0) or (ConfigData.SkillTags and ksbcnext(ConfigData.SkillTags))

    -- 向技能监视器发送消息
    if DebugFlag.OpenAbilityMonitor then
        LuaDebugProxy.AddSkillRunningInfo(Entity.eid, ABILITY_MONIOTOR_OPERATION.SET_BACKSWING, 
        {AbilityID=skillIns.skillID, IsNormal=ConfigData.IsNormal, SkillListJudge=SkillListJudge},
        MONITOR_COLOR.WHITE)
    end

    if ConfigData.IsNormal then
        skillIns:SetIsBackSwing(true)
        return
    end

    if  SkillListJudge then
        skillIns:AddBackSwingSkillList(SkillList, ConfigData.SkillTypes, ConfigData.SkillTags)
        return
    end

    
    skillIns:SetIsBackSwing(true)
end

AddComboWindow = AddComboWindow or {}
function AddComboWindow.Execute(Entity, ConfigData, StaticBlackBoard)
    Entity:AddSkillComboWindowRaw(
        ConfigData.SkillID, ConfigData.ComboDuration, nil, ConfigData.ShowCountDown, ConfigData.ShowHighLight, false)
end

ExploreElement = ExploreElement or {}
ExploreElement.__offset = M3D.Vec3()
ExploreElement.__centerPos = M3D.Vec3()
ExploreElement.__rotator = M3D.Rotator()

function ExploreElement.Execute(Entity, ConfigData, StaticBlackBoard, targetEID, targetEIDList)
    local offset = ConfigData.Offset or ExploreElement.__offset
    -- local ignoreActorTags = GameConst.EXPLORE_ELEMENT_IGNORE_COMPONENT_TAGS -- TODO: 已废弃, 待确认是否还有使用场景

    local targetIDList
    if offset.X == 0 and offset.Y == 0 and offset.Z == 0 then
        targetIDList = TargetSelectorEx.QueryEntitiesByRule(Entity, ConfigData.RuleID, nil, StaticBlackBoard)
    else
        local centerPos = ExploreElement.__centerPos
        centerPos:Pack(Entity:GetPosition_P())
        if IsValidID(Entity.CppEntity:KAPI_Actor_GetCapsuleComponent()) then
            local capHalfHeight = Entity.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()
            centerPos.Z = centerPos.Z - capHalfHeight
        end
        local yaw = Entity:GetRotation().Yaw
        local rotator = ExploreElement.__rotator
        rotator:Pack(0.0, yaw, 0.0)
        rotator:RotateVector(offset, offset)
        centerPos:Add(offset, centerPos)
        local taskTargetEntity = Game.EntityManager:GetEntityByIntID(targetEID)
        targetIDList = TargetSelectorEx.QueryEntitiesByRuleAtSpace(
            Entity, taskTargetEntity, StaticBlackBoard, EUseInputTransType.CenterTrans, centerPos, yaw, ConfigData.RuleID)
    end

    if (targetIDList == nil) or (#targetIDList == 0) then
        return
    end

    local exploreElementID = ConfigData.ExploreElementID
    local elementDuration = ConfigData.ElementDuration

    for _, targetID in ipairs(targetIDList) do
        local targetEnt = Game.EntityManager:GetEntityByIntID(targetID)
        if (targetEnt ~= nil) and (targetEnt.bCanClientElementReact == true) then
            targetEnt:DoElementReact(exploreElementID, elementDuration)
        end
    end
end


--region Attack
-- note@shijingzhe, 这个task目前是boss检查玩家是否有振刀buff以及被振表现用的, 现阶段还不存在玩家被boss振的情况,所以先不实现预表现,后续有需求了再放开
---@deprecated TODO: 如果后续要使用, 需要改动
AttackFilterByHero = AttackFilterByHero or {}

AttackFilterByHero.__attack_src_ctx = {}
AttackFilterByHero.__CenterPos = M3D.Vec3()
AttackFilterByHero.__HeroPos = M3D.Vec3()
AttackFilterByHero.__TargetPos = M3D.Vec3()

function AttackFilterByHero.FilterBlockTargetIDList(targetIDList, heroEnt, centerPos, cenYaw, rotateDir, skillAttackData)
    Log.ErrorFormat("AttackData is deprecated")

    local filterTargetList = {}
    local targetNum = 1

    local heroViewPos = heroEnt:GetPosition()
    local heroPos = AttackFilterByHero.__HeroPos
    heroPos.X, heroPos.Y, heroPos.Z = heroViewPos.X, heroViewPos.Y, heroViewPos.Z
    heroPos:Sub(centerPos, heroPos)

    local heroYaw = math.atan(heroPos.X, heroPos.Y) * 180 / math.pi
    local dirFromHero2Cen = cenYaw - heroYaw
    if dirFromHero2Cen < 0 then
        dirFromHero2Cen = dirFromHero2Cen + 360
    end

    local targetPos = AttackFilterByHero.__TargetPos
    for _, targetID in ipairs(targetIDList) do
        if targetID == heroEnt:uid() then
            goto continue
        end

        local target = Game.EntityManager:GetEntityByIntID(targetID)
        if not target then
            goto continue
        end

        local targetViewPos = target:GetPosition()
        targetPos.X, targetPos.Y, targetPos.Z = targetViewPos.X, targetViewPos.Y, targetViewPos.Z
        local targetYaw = math.atan(targetPos.X, targetPos.Y) * 180 / math.pi
        local dirFromTarget2Hero = heroYaw - targetYaw
        if dirFromTarget2Hero < 0 then
            dirFromTarget2Hero = dirFromTarget2Hero + 360
        end
        local dirFromTarget2Cen = cenYaw - targetYaw
        if dirFromTarget2Cen < 0 then
            dirFromTarget2Cen = dirFromTarget2Cen + 360
        end

        if dirFromHero2Cen < 180 then
            -- 振刀玩家在中心轴顺时针方向
            if rotateDir and (dirFromTarget2Hero < 180 or dirFromTarget2Cen < 180) then
                filterTargetList[targetNum] = target
                targetNum = targetNum + 1
            elseif (not rotateDir) and dirFromTarget2Hero > 180 then
                filterTargetList[targetNum] = target
                targetNum = targetNum + 1
            end
        elseif dirFromHero2Cen > 180 then
            -- 振刀玩家在中心轴逆时针方向
            if rotateDir and dirFromTarget2Hero < 180 then
                filterTargetList[targetNum] = target
                targetNum = targetNum + 1
            elseif (not rotateDir) and (dirFromTarget2Hero > 180 or dirFromTarget2Cen > 180) then
                filterTargetList[targetNum] = target
                targetNum = targetNum + 1
            end
        elseif dirFromHero2Cen == 0 then
            -- 振刀玩家在中心轴上
            if rotateDir and dirFromTarget2Hero < 180 then
                filterTargetList[targetNum] = target
                targetNum = targetNum + 1
            elseif (not rotateDir) and dirFromTarget2Hero > 180 then
                filterTargetList[targetNum] = target
                targetNum = targetNum + 1
            end
        end

        :: continue ::
    end

    return filterTargetList
end

AttackFilterByHero.__OriginPos = M3D.Vec3()
function AttackFilterByHero.Execute(Entity, ConfigData, StaticBlackBoard, targetEID, targetEIDList)
    -- 主角发起的攻击才会执行预表现
    if Entity ~= Game.me then
        return
    end

    -- 向监视器发送消息
    if DebugFlag.OpenAbilityMonitor then
        LuaDebugProxy.AddSkillRunningInfo(Entity.eid, ABILITY_MONIOTOR_OPERATION.TASK_EXE_AT_CLIENT .. "AttackFilterByHero",
            { ConfigData = ConfigData, },
            MONITOR_COLOR.ORANGE)
    end

    local attackID = ConfigData.SkillAttackID
    local skillData
    skillData = Game.TableData.GetSkillDataNewRow(attackID)
    if skillData == nil then
        Log.WarningFormat("AttackFilterByHero, SkillAttackID: %s invalid", attackID)
        return
    end

    local centerPos = AttackFilterByHero.__CenterPos
    centerPos = Attack.CalcPos(Entity, StaticBlackBoard, ConfigData.BasePosition, ConfigData.Offset, ConfigData.MaxDistance, AttackFilterByHero.__OriginPos)
    if not centerPos then
        Log.ErrorFormat("[AttackFilterByHero.Execute] calc targetPos failed, skillID:%s", StaticBlackBoard.skillID)
        return
    end

    local yaw = Attack.CalcYaw(Entity, ConfigData, StaticBlackBoard, ConfigData.DeltaYaw)

    local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
    local targetIDList, otherTargetEIDs = TargetSelectorEx.QueryEntitiesByRuleAtSpace(Entity, TEntity, StaticBlackBoard,
        EUseInputTransType.CenterTrans, centerPos, yaw, nil, skillData.AttackSelectorRuleData)
    if otherTargetEIDs then
        for _, otherTargetEID in ipairs(otherTargetEIDs) do
            --if Game.MassCharacterManager and Game.MassCharacterManager:bMassLocalEntity(otherTargetEID) then
            --    Game.EventSystem:PublishForUniqueID(_G.EEventTypes.SKILL_HIT_MASS_ENTITY, otherTargetEID)
            --end
        end
    end
    if (targetIDList == nil) or (#targetIDList == 0) then
        return
    end

    -- 检查是否有振刀玩家, note@shijingzhe: 策划通过机制保证只筛选到一名玩家
    local heroBuffID = ConfigData.HeroBuffID
    local heroEnt = nil
    for _, targetID in ipairs(targetIDList) do
        local targetEnt = Game.EntityManager:GetEntityByIntID(targetID)
        if not targetEnt then
            goto continue
        end

        local buffList = targetEnt:GetBuffInstanceByBuffIDNew(heroBuffID)
        if #buffList > 0 then
            heroEnt = targetEnt
            break
        end

        :: continue ::
    end

    if heroEnt then
        -- 过滤被挡刀玩家
        local filterTargetIDList = AttackFilterByHero.FilterBlockTargetIDList(targetIDList, heroEnt, centerPos, yaw, ConfigData.RotateDir, skillData)
        if (filterTargetIDList == nil) or (#filterTargetIDList == 0) then
            return
        end

        -- CombatEffectUtils.ProcessSkillAttackAction
    else
        -- CombatEffectUtils.ProcessSkillAttackAction
    end
end


--endregion Attack


--region PerformBullet


CreatePerformBullet = CreatePerformBullet or {}

function CreatePerformBullet.Execute(Entity, ConfigData, StaticBlackBoard, targetEID)
    local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
    if (not TEntity and ConfigData.TaskTargetType == 3) or (not Entity and ConfigData.TaskTargetType == 0) then
        Log.WarningFormat("[CreatePerformBullet] get %s target entity failed in skill %s", targetEID, StaticBlackBoard.skillID)
        return
    end

    CombatEffectUtils.CreateOneBullet(Entity, ConfigData, StaticBlackBoard, TEntity, ConfigData.OriginalYaw, ConfigData.OriginalPitch, ConfigData.PosOffset)
end

CreatePerformBulletGroup = CreatePerformBulletGroup or {}

function CreatePerformBulletGroup.Execute(Entity, ConfigData, StaticBlackBoard, targetEID)
    local cnt = ConfigData.Count
    if cnt <= 0 then
        Log.WarningFormat("[CreatePerformBulletGroup] config bullet num %s invalid in skill %s", cnt, StaticBlackBoard.skillID)
        return
    end

    local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
    if not TEntity then
        Log.WarningFormat("[CreatePerformBullet] get %s target entity failed in skill %s", targetEID, StaticBlackBoard.skillID)
        return
    end

    local oriYawList = ConfigData.OriginalYaw
    local oriPitchList = ConfigData.OriginalPitch
    local posOffsetList = ConfigData.PosOffset

	local randomIdxSet = nil
	
	if ConfigData.RandomMode and ConfigData.RandomCount < ConfigData.Count then
		if ConfigData.RandomCount == 0 then
			return
		end
		
		local tmp = {}
		for i = 1, ConfigData.Count do
			table.insert(tmp, i)
		end

		-- Fisher-Yates shuffle algorithm
		for i = ConfigData.Count, 2, -1 do
			local j = math.random(i)
			tmp[i], tmp[j] = tmp[j], tmp[i]
		end
		
		randomIdxSet = {}
		for i = 1, ConfigData.RandomCount do
			randomIdxSet[tmp[i]] = true
		end
	end

    for idx = 1, cnt do
		if ConfigData.RandomMode then
			if randomIdxSet == nil then
				break
			end
			if not randomIdxSet[idx] then
				goto continue
			end
		end
        CombatEffectUtils.CreateOneBullet(Entity, ConfigData, StaticBlackBoard, TEntity, oriYawList[idx], oriPitchList[idx], posOffsetList[idx])
		::continue::
    end
end


--endregion PerformBullet


--region Search

__Debug_Draw_Radius = __Debug_Draw_Radius or 30 -- 绘制半径
__Debug_Draw_Color = __Debug_Draw_Color or FLinearColor.Red -- 绘制颜色
__Debug_Draw_Duration = __Debug_Draw_Duration or 3.0 -- 绘制时长
__Debug_Draw_Thickness = __Debug_Draw_Thickness or 2.0 -- 绘制线条粗细

SearchTarget = SearchTarget or {}
function SearchTarget.Execute(entity, configData, staticBlackBoard)
    -- 主要是服务预表现的,非主角不执行
    if entity ~= Game.me then
        return
    end

    local targetIDList = TargetSelectorEx.QueryEntitiesByRule(
        entity, configData.TargetSelectionRuleID, nil, staticBlackBoard)
    if (targetIDList ~= nil) and (next(targetIDList) ~= nil) then
        local targetIDListCopy = {}
        for _, targetID in ipairs(targetIDList) do
            table.insert(targetIDListCopy, targetID)
        end
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = targetIDListCopy
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = #targetIDListCopy
    else
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = nil
    end
end

SearchTargetPos = SearchTargetPos or {}
function SearchTargetPos.Execute(entity, configData, staticBlackBoard)
    -- 主要是服务预表现的,非主角不执行
    if entity ~= Game.me then
        return
    end

    local targetIDList = TargetSelectorEx.QueryEntitiesByRule(
        entity, configData.TargetSelectionRuleID, nil, staticBlackBoard)
    if (targetIDList ~= nil) and (next(targetIDList) ~= nil) then
        local targetIDListCopy = {}
        local outPosList = {}
        for _, targetID in ipairs(targetIDList) do
            local target = Game.EntityManager:GetEntityByIntID(targetID)
            if not target then
                goto continue
            end

            table.insert(targetIDListCopy, targetID)
            table.insert(outPosList, target:GetPosition())

            :: continue ::
        end

        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = targetIDListCopy
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = #targetIDListCopy
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetNotEntity] = false
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchPosList] = outPosList
    else
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetNotEntity] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchPosList] = nil
    end
end


ChangeLockTargetFromOwnerToSelf = ChangeLockTargetFromOwnerToSelf or {}
function ChangeLockTargetFromOwnerToSelf.Execute(entity, configData, staticBlackBoard)
	if not BSFunc.CheckTargetCamp(Game.me, entity, 32) then
		return
	end
    local TargetUID = Game.me:GetLockTargetUID(ETE.EBSTargetType.TT_Skill)
    local TargetEntity = Game.EntityManager:GetEntityByIntID(TargetUID)
    if not TargetEntity and Game.me.CachedSkillLockUID then
        TargetEntity = Game.EntityManager:GetEntityByIntID(Game.me.CachedSkillLockUID)
    end
    if TargetEntity then
        if TargetEntity.eid == entity.FinalOwnerID then
            Game.me:ChangeLockTargetByUID(ETE.EBSTargetType.TT_Skill, entity:uid())
        end
    end
end
--endregion Search


function RegisterTask()
	EffectTaskFactory.Register(InnerAttackTaskType, InnerAttack)
    EffectTaskFactory.Register(Enum.EffectTaskType.BackSwing, BackSwing)
    EffectTaskFactory.Register(Enum.EffectTaskType.AddComboWindow, AddComboWindow)
    EffectTaskFactory.Register(Enum.EffectTaskType.ExploreElement, ExploreElement)

    -- attack
    --EffectTaskFactory.Register(Enum.EffectTaskType.AttackFilterByHero, AttackFilterByHero)

    -- bullet
    EffectTaskFactory.Register(Enum.EffectTaskType.CreatePerformBullet, CreatePerformBullet)
    EffectTaskFactory.Register(Enum.EffectTaskType.CreatePerformBulletGroup, CreatePerformBulletGroup)

    --更改锁定目标
    EffectTaskFactory.Register(Enum.EffectTaskType.ChangeLockTargetFromOwnerToSelf, ChangeLockTargetFromOwnerToSelf)
    -- searchPos @shijingzhe:考虑到性能,客户端运行时暂不执行对应的几个Task
    --EffectTaskFactory.Register(Enum.EffectTaskType.SearchTarget, SearchTarget)
    --EffectTaskFactory.Register(Enum.EffectTaskType.SearchTargetPos, SearchTargetPos)
end
