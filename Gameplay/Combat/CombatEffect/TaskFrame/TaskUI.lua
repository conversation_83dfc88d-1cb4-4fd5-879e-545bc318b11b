local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local Enum = Enum

--------------------------------------------------------
--- 瞬发类Task，需要实现Excute
--------------------------------------------------------

ChangeIconTask = {}  -- luacheck: ignore
function ChangeIconTask.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    if (configData.IconPath == nil) or (configData.IconPath == "None") or (configData.IconPath == "") then
        entity:RestoreIcon(staticBlackBoard.skillID)
        return
    end
    entity:ChangeIcon(staticBlackBoard.skillID, configData.IconPath)
end


function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.ChangeIcon, ChangeIconTask)
end