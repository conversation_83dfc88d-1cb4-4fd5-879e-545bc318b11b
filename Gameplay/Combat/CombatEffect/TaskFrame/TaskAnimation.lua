local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local sharedConst = kg_require("Shared.Const")
local Enum = Enum
local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE

--------------------------------------------------------
--- 瞬发类Task，需要实现Excute
--------------------------------------------------------

AnimInstCrossFade = {} -- luacheck: ignore
function AnimInstCrossFade.Execute(InExecutor, InTaskData, InContext, InTEID, InTEIDList)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if (InTaskData.NewAnimStateName == nil) or (InTaskData.NewAnimStateName == "") then
        return
    end

    if InExecutor and InExecutor.LocomotionCrossfadeInFixedTime then
        InExecutor:LocomotionCrossfadeInFixedTime(InTaskData.NewAnimStateName, InTaskData.TransitionTime)
    end
end

function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.AnimInstCrossFade, AnimInstCrossFade, TASK_STATE_DATA_TYPE.None)
end