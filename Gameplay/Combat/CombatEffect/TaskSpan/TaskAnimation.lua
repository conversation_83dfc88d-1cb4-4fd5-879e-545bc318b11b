--------------------------------------------------------
--- 持续类Task，需要实现Excute和End
--------------------------------------------------------



local sharedConst = kg_require("Shared.Const")
local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local ULLFunc = import("LowLevelFunctions")

local Enum = Enum
local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE
local TASK_DYNAMIC_KEY_TYPE = sharedConst.TASK_DYNAMIC_KEY_TYPE

local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")
local EActionBlendRule = ViewAnimConst.EActionBlendRule

MoveByAnim = {} -- luacheck: ignore
MoveByAnim.StartLoc = { X = 0, Y = 0, Z = 0 } -- 起始位置预制变量
MoveByAnim.EndLoc = { X = 0, Y = 0, Z = 0 } -- 结束位置预制变量
function MoveByAnim.Execute(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if InExecutor ~= Game.me or not staticBlackBoard.isClientPreCast then
        return
    end

    local moveByAnimGUID = InTaskData.MoveByAnimGUID
    if (moveByAnimGUID == nil) or (moveByAnimGUID == 0) then
        return
    end
    
    local rootMotionCurve = InTaskData.RootMotionCurveLinearColor
    if (rootMotionCurve == nil) or (rootMotionCurve == "") then
        return
    end

    local moveByAnimData = Game.CombatDataManager:GetAbilityMoveByAnimDT(staticBlackBoard.skillID, moveByAnimGUID)
    if not moveByAnimData then
        return
    end
    
    local pointNum = #moveByAnimData.Curve
    if pointNum <= 0 then
        return
    end
    
    local startLoc = InExecutor:GetPosition()
    MoveByAnim.StartLoc.X = startLoc.X
    MoveByAnim.StartLoc.Y = startLoc.Y
    MoveByAnim.StartLoc.Z = startLoc.Z
    
    local startRot = InExecutor:GetRotation()
    local endPoint = moveByAnimData.Curve[pointNum]
    
    local sinVal = math.sin(math.rad(startRot.Yaw))
    local cosVal = math.cos(math.rad(startRot.Yaw))

    MoveByAnim.EndLoc.X = startLoc.X + endPoint.X * cosVal - endPoint.Y * sinVal
    MoveByAnim.EndLoc.Y = startLoc.Y + endPoint.X * sinVal + endPoint.Y * cosVal
    MoveByAnim.EndLoc.Z = startLoc.Z + endPoint.Z
    
    local syncSign = (staticBlackBoard and staticBlackBoard.skillID) or 0
	if InTaskData.AimTargetDistance and InTaskData.AimTargetDistance > 0 and InTEID ~= nil then
		InExecutor:SetEnableImpetus(InExecutor:uid(), true)

		-- 添加额外推挤距离
		InExecutor:ChangeExtraImpetusDistance(InExecutor:uid(), InTaskData.AimTargetDistance)
		InExecutor:ChangeImpetusTargetByEID(InTEID, InExecutor:uid(), true)
	elseif InTaskData.IgnoreImpetus then
		InExecutor:SetEnableImpetus(InExecutor:uid(), false)
	end
	local InScale = nil
	if InTaskData.ModelScaleAdjust then
		InScale = InExecutor:GetScale_P()
	end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = InExecutor:RequestRootMotion(
        MoveByAnim.StartLoc,
        MoveByAnim.EndLoc,
        moveByAnimData.TotalTime,
		rootMotionCurve,
        syncSign,
        InTaskData.StickGround,
        true,
        InTaskData.MoveByAnimGUID,
		InScale,
		true
    )
end

function MoveByAnim.End(InExecutor, InTaskData, InContext, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if InExecutor ~= Game.me then
        return
    end
    
    local MoveByAnimGUID = InTaskData.MoveByAnimGUID
    if not MoveByAnimGUID or MoveByAnimGUID == 0 then
        return
    end

	if InTaskData.AimTargetDistance and InTaskData.AimTargetDistance > 0 and InTEID ~= nil then
		InExecutor:SetEnableImpetus(InExecutor:uid(), false)

		-- 添加额外推挤距离
		InExecutor:ChangeExtraImpetusDistance(InExecutor:uid(), nil)
		InExecutor:ChangeImpetusTargetByEID(InTEID, InExecutor:uid(), false)
	elseif InTaskData.IgnoreImpetus then
		InExecutor:SetEnableImpetus(InExecutor:uid(), true)
	end
    
    if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.bTaskEndFrameReached] then
        return
    end

    if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] ~= nil then
        InExecutor:CancelRootMotion(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID], true)
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
    end
end

-- lixiao 交接的task，先上传，如后续不需要删除 by zhangfeng15
ReplaceAnimByAnimLib = {} -- luacheck: ignore
function ReplaceAnimByAnimLib.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    if configData.TagMapping == nil then
        return
    end

    entity:SwitchLocoAnimByAnimLibAssetAsyncBatch(configData.TagMapping)
end

function ReplaceAnimByAnimLib.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    if configData.TagMapping == nil then
        return
    end
    entity:RecoverLocoAnimByAnimLibAssetBatch(configData.TagMapping)
end


PlayWeaponAnimation = {} -- luacheck: ignore
function PlayWeaponAnimation.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

    if (configData.MontageAsset == nil) or (configData.MontageAsset == "") then
        return
    end

    -- 先不考虑给目标的武器播放动画
    local GID = ULLFunc.GetGlobalUniqueID()
    entity:PlayWeaponAnimationByPath(configData.MontageAsset, configData.ModelID, configData.PlayRate, GID, configData.bAutoSearchWeapon, configData.bPropagateToOwner)
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = GID
end

function PlayWeaponAnimation.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    entity:StopWeaponAnimation(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID], configData.bFinishMontageWhenTaskEnd, configData.ModelID, 0.2, configData.bAutoSearchWeapon, configData.bPropagateToOwner)
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PlayPerformPetAnimation = {} -- luacheck: ignore
function PlayPerformPetAnimation.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (entity == nil) or (configData == nil) then
		return
	end

	if (configData.SequenceAsset == nil or configData.SequenceAsset == "") and
		(configData.FemaleSequenceAsset == nil or configData.FemaleSequenceAsset == "")
	then
		return
	end
	local sequenceAsset = configData.SequenceAsset
	if configData.bGenderAnim and entity.Sex == Enum.ESex.FEMALE then
		sequenceAsset = configData.FemaleSequenceAsset
	end
	entity:PlayPerformPetAnimationByPath(sequenceAsset, configData.PlayRate)
end

function PlayPerformPetAnimation.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (entity == nil) or (configData == nil) then
		return
	end
	entity:StopPerformPetAnimation(configData.bFinishMontageWhenTaskEnd, 0.2)
end

PlayAnimationWithMotionWarp = {} -- luacheck: ignore
function PlayAnimationWithMotionWarp.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    -- MotionWrap要使用动画中的输出, 不能禁用Montage的RootMotion
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = entity:PlaySkillAnimationByPath(
            configData.MontageAsset, 0, configData.PlayRate, 0, 0, 0, true, false)
end

function PlayAnimationWithMotionWarp.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    if (dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] == nil) then
        return
    end
    local handleToken = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
    if staticBlackBoard and staticBlackBoard.FinishReason == ETE.EBSAFinishReason.FR_Interrupt then
        entity:StopSkillAnimation(0.1, handleToken)
    else
        entity:StopSkillAnimation(0.1, handleToken)
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PlayLevelSequence = {} -- luacheck: ignore
function PlayLevelSequence.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
	-- 只有p1播放
	if entity ~= Game.me then
		return
	end
    local GID = ULLFunc.GetGlobalUniqueID()
    local PlaybackSetting = PlayLevelSequence.InitPBS(configData)
    entity:PlayLevelSequenceForSkill(configData.LevelSequence, PlaybackSetting, configData.bReplaceCharacter, GID)
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = GID
end

function PlayLevelSequence.InitPBS(configData)
    local PlaybackSetting = {}
    local PBS = configData.PlaybackSettings
    PlaybackSetting.bAutoPlay = PBS.bAutoPlay
    PlaybackSetting.PlayRate = PBS.PlayRate
    PlaybackSetting.StartTime = PBS.StartTime
    PlaybackSetting.bRandomStartTime = PBS.bRandomStartTime
    PlaybackSetting.FinishCompletionStateOverride = PBS.FinishCompletionStateOverride
    PlaybackSetting.bDisableMovementInput = PBS.bDisableMovementInput
    PlaybackSetting.bDisableLookAtInput = PBS.bDisableLookAtInput
    PlaybackSetting.bHidePlayer = PBS.bHidePlayer
    PlaybackSetting.bHideHud = PBS.bHideHud
    PlaybackSetting.bDisableCameraCuts = PBS.bDisableCameraCuts
    PlaybackSetting.bPauseAtEnd = PBS.bPauseAtEnd
    PlaybackSetting.bInheritTickIntervalFromOwner = PBS.bInheritTickIntervalFromOwner
    PlaybackSetting.bDynamicWeighting = PBS.bDynamicWeighting
    return PlaybackSetting
end

function PlayLevelSequence.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    entity:StopLevelSequenceForSkill(configData.FinishOffsetTransform, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PlayMontageNew = {} -- luacheck: ignore
function PlayMontageNew.Execute(InExecutor, InTaskData, InContext, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if ((InTaskData.SequenceAsset == nil) or (InTaskData.SequenceAsset == "")) and
		((InTaskData.FemaleSequenceAsset == nil) or (InTaskData.FemaleSequenceAsset == "")) and
            (InTaskData.LibAssetID == nil or InTaskData.LibAssetID == "" or InTaskData.LibAssetID.AssetID == "") then
        return
    end

    if (InTaskData.bNotInterrupt) then
        -- 低优先级表演动作相关
        -- 如果目前有其他动作在播，不处理
        if InExecutor:IsPlayingActionAnimation() then
            return
        end

        -- 如果目前处于持武状态（一般是攻击后摇），不处理
        if InExecutor.HoldWeaponState == true then
            return
        end
    end

    local NotUpdateBoundMeshComp = {}
	-- 等待CPP处理接口
    if InTaskData.UpdateBound then
		InExecutor:SetUpdateBoundWithFirstSkinnedBone(true)
        --local SKComps = Game.ObjectActorManager:GetObject(Game.me.CharacterID):K2_GetComponentsByClass(SkeletalMeshComponent)
        --for j = 0, SKComps:Num()-1 do
        --    local SKComp = SKComps:Get(j)
        --    if SKComp and not SKComp:GetUpdateBoundWithFirstSkinnedBone() then
        --        table.insert(NotUpdateBoundMeshComp, SKComp)
        --        SKComp:SetUpdateBoundWithFirstSkinnedBone(true)
        --    end
        --end
    end
    
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.NotUpdateBoundsSKComps] = NotUpdateBoundMeshComp

    if (InTaskData.AnimType == ETE.EBSAAnimType.AT_Blank) then
		local sequenceAsset = InTaskData.SequenceAsset
		if InTaskData.bGenderAnim and InExecutor.Sex == Enum.ESex.FEMALE then
			sequenceAsset = InTaskData.FemaleSequenceAsset
		end
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = InExecutor:PlaySkillAnimationByPath(
                sequenceAsset, InTaskData.FullBodyMask, InTaskData.PlayRate, InTaskData.BlendInTime / 30 / InTaskData.PlayRate,
                InTaskData.BlendOutWhenInterrupt / 30 / InTaskData.PlayRate, InTaskData.StartFrame / 30, true, true, InTaskData.bAnimLoop) --InTaskData.bStopAllAnimation)
    elseif (InTaskData.AnimType == ETE.EBSAAnimType.AT_AnimLib) then
        local assetID = InTaskData.LibAssetID.AssetID or InTaskData.LibAssetID
        local HandleID = InExecutor:PlaySkillAnimationByAnimLib(
                assetID, InTaskData.FullBodyMask, InTaskData.PlayRate, InTaskData.BlendInTime / 30 / InTaskData.PlayRate,
				InTaskData.BlendOutWhenInterrupt / 30 / InTaskData.PlayRate, InTaskData.StartFrame / 30, true, true, InTaskData.bAnimLoop)
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = HandleID
    end
end

function PlayMontageNew.End(InExecutor, InTaskData, InContext, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if ((InTaskData.SequenceAsset == nil) or (InTaskData.SequenceAsset == "")) and
		((InTaskData.FemaleSequenceAsset == nil) or (InTaskData.FemaleSequenceAsset == "")) and
            (InTaskData.LibAssetID == nil or InTaskData.LibAssetID == "" or InTaskData.LibAssetID.AssetID == "") then
        return
    end

    --local BoundingBoxRecord = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.NotUpdateBoundsSKComps]
    --if BoundingBoxRecord then
    --    for _, SKComp in pairs(BoundingBoxRecord) do
    --        SKComp:SetUpdateBoundWithFirstSkinnedBone(false)
    --    end
    --end
	if InTaskData.UpdateBound then
		InExecutor:SetUpdateBoundWithFirstSkinnedBone(false)
	end

    if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] ~= nil then
        if InContext and InContext.FinishReason == ETE.EBSAFinishReason.FR_Interrupt then
            InExecutor:StopSkillAnimation(InTaskData.BlendOutWhenInterrupt / 30 / InTaskData.PlayRate, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
        else
            InExecutor:StopSkillAnimation(InTaskData.BlendOutTime / 30 / InTaskData.PlayRate, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
        end
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
    end
end

PlayJumpAnimation = {} -- luacheck: ignore
PlayJumpAnimation.PlayMode = {}
function PlayJumpAnimation.Execute(InExecutor, InTaskData, InContext, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if (InTaskData.IdleMontageAsset == nil) or (InTaskData.IdleMontageAsset == "") then
        if (InTaskData.LocoInputMontageAsset == nil) or (InTaskData.LocoInputMontageAsset == "") then
            return
        end
    end
	InExecutor.CppEntity:KAPI_Movement_SetHasForceLocoGroundSupport("PlayJumpAnimation", true) --先特殊技能里处理
	local MontageToPlay = nil
    local bPlayLocoInput = false
    if InExecutor.HasLocoInput ~= nil and  InExecutor:HasLocoInput() == true then
        MontageToPlay = InTaskData.LocoInputMontageAsset ~= "" and InTaskData.LocoInputMontageAsset or InTaskData.IdleMontageAsset
        if (InTaskData.ClientTimelineIDForDash ~= nil) and (InTaskData.ClientTimelineIDForDash ~= 0) then
            InExecutor:StartAbilityRunner(InTaskData.ClientTimelineIDForDash)
        end
        bPlayLocoInput = true
    else
        MontageToPlay = InTaskData.IdleMontageAsset ~= "" and InTaskData.IdleMontageAsset or InTaskData.LocoInputMontageAsset
        if (InTaskData.ClientTimelineIDForIdle ~= nil) and (InTaskData.ClientTimelineIDForIdle ~= 0) then
            InExecutor:StartAbilityRunner(InTaskData.ClientTimelineIDForIdle)
        end
    end

    if MontageToPlay ~= nil and MontageToPlay ~= "" then
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = InExecutor:PlaySkillAnimationByPath(MontageToPlay, EActionBlendRule.FullBodyOverride, InTaskData.PlayRate, InTaskData.BlendInTime / 30 / InTaskData.PlayRate, InTaskData.InterruptBlendOut / 30 / InTaskData.PlayRate, 0, true) --InTaskData.bStopAllAnimation)
		PlayJumpAnimation.PlayMode[dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]] = bPlayLocoInput
	end
end

function PlayJumpAnimation.End(InExecutor, InTaskData, InContext, InTEID, InTEIDList, dynamicBlackBoard)
     if (InExecutor == nil) or (InTaskData == nil) then
        return
    end
	if(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] == nil) then
		return
	end
	InExecutor.CppEntity:KAPI_Movement_SetHasForceLocoGroundSupport("PlayJumpAnimation", false)
    if (InTaskData.IdleMontageAsset == nil) or (InTaskData.IdleMontageAsset == "") then
        if (InTaskData.LocoInputMontageAsset == nil) or (InTaskData.LocoInputMontageAsset == "") then
            return
        end
    end
    if (PlayJumpAnimation.PlayMode[dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]] == false) then
        if (InTaskData.ClientTimelineIDForIdle ~= nil) and (InTaskData.ClientTimelineIDForIdle ~= 0) then
            InExecutor:StopAbilityRunner(InTaskData.ClientTimelineIDForIdle)
        end
    else
        if (InTaskData.ClientTimelineIDForDash ~= nil) and (InTaskData.ClientTimelineIDForDash ~= 0) then
            InExecutor:StopAbilityRunner(InTaskData.ClientTimelineIDForDash)
        end
    end
    PlayJumpAnimation.PlayMode[dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]] = nil
    if InTaskData.bFinishMontageWhenTaskEnd then
        if InContext.FinishReason == ETE.EBSAFinishReason.FR_Interrupt then
            InExecutor:StopSkillAnimation(InTaskData.InterruptBlendOut / 30 / InTaskData.PlayRate, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
        else
            InExecutor:StopSkillAnimation(InTaskData.BlendOutTime / 30 / InTaskData.PlayRate, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
        end 
    end

    --if (InExecutor.AttachOnSKillAnimWillEnd ~= nil) then
    --    InExecutor:AttachOnSKillAnimWillEnd(InContext.FinishReason, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ResourceHandle])
    --end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
    --dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ResourceHandle] = nil
end



PlayAnimationWithLRFoot = {} -- luacheck: ignore
function PlayAnimationWithLRFoot.Execute(InExecutor, InTaskData, InContext, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if (InTaskData.SequenceAsset == nil) or (InTaskData.SequenceAsset == "") then
        return
    end

    local SequenceAsset = PlayAnimationWithLRFoot.GetBlankAnimationAssetName(InExecutor, InTaskData.SequenceAsset, InTaskData.MontageAssetRightFoot, InTaskData.bDistinguishLeftRightFoot)

    if (InTaskData.AnimType == ETE.EBSAAnimType.AT_Blank) then
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = InExecutor:PlaySkillAnimationByPath(
                SequenceAsset, InTaskData.FullBodyMask, InTaskData.PlayRate, InTaskData.BlendInTime / 30 / InTaskData.PlayRate,
                InTaskData.BlendOutWhenInterrupt / 30 / InTaskData.PlayRate, InTaskData.StartFrame / 30, true, true) --InTaskData.bStopAllAnimation)
    end
end

function PlayAnimationWithLRFoot.GetBlankAnimationAssetName(InExecutor, MontageAsset, MontageAssetRightFoot, bDistinguishLeftRightFoot)
    local MontageAssetName = MontageAsset
    if (bDistinguishLeftRightFoot == true) then
        if (InExecutor ~= nil) and (InExecutor:IsLeftFootState() == false) then
            MontageAssetName = MontageAssetRightFoot
        end
    end
    return MontageAssetName
end

function PlayAnimationWithLRFoot.End(InExecutor, InTaskData, InContext, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end

    if (InTaskData.SequenceAsset == nil) or (InTaskData.SequenceAsset == "") then
        return
    end

    if InContext and InContext.FinishReason == ETE.EBSAFinishReason.FR_Interrupt then
        InExecutor:StopSkillAnimation(InTaskData.BlendOutWhenInterrupt / 30 / InTaskData.PlayRate, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    else
        InExecutor:StopSkillAnimation(InTaskData.BlendOutTime / 30 / InTaskData.PlayRate, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil

    --if (InExecutor.AttachOnSKillAnimWillEnd ~= nil) then
    --    InExecutor:AttachOnSKillAnimWillEnd(InContext.FinishReason, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ResourceHandle])
    --end
    --dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ResourceHandle] = nil
end

-- 跳跃走状态机了，这边不需要处理了
--ListenMoveModeChanged = {} -- luacheck: ignore
--function ListenMoveModeChanged.Execute(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList, dynamicBlackBoard)
--    if (InExecutor == nil) or (InTaskData == nil) then
--        return
--    end
--    
--    if Game.ObjectActorManager:GetObject(InExecutor.CharacterID) then
--        local Delegate = Game.ObjectActorManager:GetObject(InExecutor.CharacterID).MovementModeChangedDelegate
--        if Delegate then
--            ListenMoveModeChanged.OnMovementModeChanged = function(InCharacter, PrevMovementMode, PrevCustomMode)
--                if slua.isValid(InCharacter) and slua.isValid(InCharacter:GetCharacterMoveComp()) then
--                    local CurMovementMode = InCharacter:GetCharacterMoveComp().MovementMode
--                    if CurMovementMode == InTaskData.ListenMovementMode then
--                        -- todo: 下面这行代码会卡顿预览，原因是预览时，无法转发给服务器节点相关 @zhangfeng15
--                        InExecutor:TriggerToGroundEvent(Enum.EffectEventType.OnToGround, staticBlackBoard.skillID)
--                        --if InTaskData.bTerminateWhenTrigger == true then
--                        --    ListenMoveModeChanged.End(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList, dynamicBlackBoard)
--                        --end
--                    end
--                end
--            end
--
--            local DelegateHandler = Delegate:Add(ListenMoveModeChanged.OnMovementModeChanged)
--            dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = {
--                DelegateHandler = DelegateHandler,
--                Delegate = Delegate
--            }
--            -- 第一次触发时就检查一次
--            ListenMoveModeChanged.OnMovementModeChanged(Game.ObjectActorManager:GetObject(InExecutor.CharacterID), nil, nil)
--        end
--    end
--end
--
--function ListenMoveModeChanged.End(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList, dynamicBlackBoard)
--    if (InTaskData == nil) then
--        return
--    end
--    local DelegateInfos = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
--    if DelegateInfos ~= nil  and slua.isValid(DelegateInfos.Delegate) then
--        DelegateInfos.Delegate:Remove(DelegateInfos.DelegateHandler)
--    end
--    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
--end

ListenMoveModeChangedNew = {} -- luacheck: ignore
function ListenMoveModeChangedNew.Execute(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList, dynamicBlackBoard)
    if (InExecutor == nil) or (InTaskData == nil) then
        return
    end
    --InExecutor:doSwitchGroundDetect(200)
	InExecutor.CppEntity:KAPI_Movement_SetHasForceLocoGroundSupport("ListenMoveModeChangedNew", true) --先特殊技能里处理
    InExecutor.CppEntity:KAPI_Movement_SetNeedGroundDistanceThresholdChangedNotify(true, InTaskData.EnterThreshold or 0, InTaskData.LeaveThreshold or 0)
    --触发task时直接检测触发一次
    if InExecutor.CppEntity:KAPI_Movement_GetCurGroundDist() <= InTaskData.EnterThreshold then
        InExecutor:KCB_NotifyGroundDistanceThresholdEnterOrLeaveChanged(true, false)
    end
end

function ListenMoveModeChangedNew.End(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList, dynamicBlackBoard)
    if (InTaskData == nil) then
        return
    end
    --InExecutor:doSwitchGroundDetect(0)
    InExecutor.CppEntity:KAPI_Movement_SetNeedGroundDistanceThresholdChangedNotify(true, 0, 0)
	InExecutor.CppEntity:KAPI_Movement_SetHasForceLocoGroundSupport("ListenMoveModeChangedNew",false)
end

DizzinessMove = {} -- luacheck: ignore
function DizzinessMove.Execute(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList)
    InExecutor:enterDizzinessLocoState()
end

function DizzinessMove.End(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList)
    InExecutor:exitDizznessLocoState()
end

CameraDirMove = {} -- luacheck: ignore
function CameraDirMove.Execute(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList)
	InExecutor:enterCameraDirMoveState()
end

function CameraDirMove.End(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList)
	InExecutor:exitCameraDirMoveState()
end

AnimationFreezeFrame = {} -- luacheck: ignore
function AnimationFreezeFrame.Execute(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList)
	if (InExecutor == nil) or (InTaskData == nil) then
		return
	end
	InExecutor:SetGlobalAnimRateScale(0) -- 定格
	InExecutor:DisableChaosClothAndKawaii()
end

function AnimationFreezeFrame.End(InExecutor, InTaskData, staticBlackBoard, InTEID, InTEIDList)
	if (InExecutor == nil) or (InTaskData == nil) then
		return
	end
	InExecutor:SetGlobalAnimRateScale(1) -- 恢复
	InExecutor:UpdateChaosClothAndKawaii()
end

function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.MoveByAnim, MoveByAnim, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayWeaponAnimation, PlayWeaponAnimation, TASK_STATE_DATA_TYPE.WITH_STATE)
	EffectTaskFactory.Register(Enum.EffectTaskType.PlayPerformPetAnimation, PlayPerformPetAnimation, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayAnimationWithMotionWarp, PlayAnimationWithMotionWarp, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayLevelSequence, PlayLevelSequence, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayMontageNew, PlayMontageNew, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayJumpAnimation, PlayJumpAnimation, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayAnimationWithLRFoot, PlayAnimationWithLRFoot, TASK_STATE_DATA_TYPE.WITH_STATE)
    --EffectTaskFactory.Register(Enum.EffectTaskType.ListenMoveModeChanged, ListenMoveModeChanged, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.ListenMoveModeChangedNew, ListenMoveModeChangedNew, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.DizzinessMove, DizzinessMove)
	EffectTaskFactory.Register(Enum.EffectTaskType.AnimFreezeFrame, AnimationFreezeFrame)
	EffectTaskFactory.Register(Enum.EffectTaskType.CameraDirMove, CameraDirMove)
end