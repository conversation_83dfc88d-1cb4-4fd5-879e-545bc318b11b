local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory

local Enum = Enum
local ERootWarpMode = import("ERootWarpMode")
local ULLFunc = import("LowLevelFunctions")
--local KML = import("KismetMathLibrary")
local EAttachmentRule = import("EAttachmentRule")
local EDetachmentRule = import("EDetachmentRule")
local EMoveCorrectorObtainPriority = import("EMoveCorrectorObtainPriority")

local sharedConst = kg_require("Shared.Const")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")
local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")


local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
local ChangeMaterialRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialRequestTemplate
local ChangeMaterialParamRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialParamRequestTemplate
local MATERIAL_EFFECT_TYPE = MaterialEffectParamTemplate.MATERIAL_EFFECT_TYPE
local SEARCH_MESH_TYPE = MaterialEffectParamTemplate.SEARCH_MESH_TYPE
local SEARCH_MATERIAL_TYPE = MaterialEffectParamTemplate.SEARCH_MATERIAL_TYPE

local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE
local TASK_DYNAMIC_KEY_TYPE = sharedConst.TASK_DYNAMIC_KEY_TYPE
local TASK_INTERRUPT_TYPE = sharedConst.TASK_INTERRUPT_TYPE

local EEffectiveClientType = kg_require("Shared.Const.AbilityConst").EEffectiveClientType


--------------------------------------------------------
--- 持续类Task，需要实现Excute和End
--------------------------------------------------------

MeshVisibilityTask = {}  -- luacheck: ignore
function MeshVisibilityTask.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local GID = ULLFunc.GetGlobalUniqueID()
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = GID
    entity:MeshVisibility(configData, GID)
end

function MeshVisibilityTask.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] and (configData.BRecover == nil or configData.BRecover == true) then
        entity:MeshVisibilityEnd(configData.BHiddenHeadInfo, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
    end
end

AddMesh = AddMesh or {}

function AddMesh.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local targetEntity = Game.EntityManager:getEntity(targetEID)
    if not targetEntity then
        Log.WarningFormat("[AddMesh.Execute] target entity not found in skill:%s", targetEID, staticBlackBoard.skillID)
        return
    end

    if type(targetEntity.AddDynamicMesh) ~= "function" then
        Log.ErrorFormat("[AddMesh.Execute] %s has no ViewControlDynamicMeshComponent", targetEntity.__cname)
        return
    end
	
	if configData.bOnlySelf then
		-- 只对自己生效
		if not (Game.me == entity or entity.IEID == Game.me:uid()) then
			return
		end
	end
	
	if configData.bAttachToCamera and targetEntity ~= Game.me then
		-- 挂在相机上, 但目标不是主角则认为无效
		return
	end

    local meshPath
    if configData.bUseStaticMesh then
        meshPath = configData.StaticMesh
    else
        meshPath = configData.SkeletalMesh
    end
	local attachTime = configData.AttachTime
	local destroyTime = -1
	if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
		destroyTime = configData.Duration or -1
	end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = targetEntity:AddDynamicMesh(
            configData.bUseStaticMesh, configData.MeshName, meshPath,
            configData.NeedAttach, attachTime, configData.bAttachToCamera, configData.AttachSocket,
            configData.Translation, configData.Rotation, configData.Scale3D, configData.bStickGround or false,
            configData.NeedLoop, configData.AnimSequence, configData.BPAnimPath, configData.bInherit or false, configData.bFollowParentBound, configData.EnableLight,
			configData.UseLOD0, destroyTime, configData.bCopyCurFrameAnim
    )
end

function AddMesh.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local targetEntity = Game.EntityManager:getEntity(targetEID)
    if not targetEntity then
        Log.WarningFormat("[AddMesh.End] target entity not found in skill:%s", targetEID, staticBlackBoard.skillID)
        return
    end
	if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
		return
	end
	if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] == nil then
		return
	end
    local uniqueID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
    targetEntity:RemoveDynamicMesh(uniqueID)
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

SetCollision = {} -- luacheck: ignore
function SetCollision.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if Game.me and Game.me ~= entity then
		Game.me:ChangeImpetusTargetByEID(entity.CharacterID, entity.eid, false)
	end
end

--function SetCollision.InitData(configData, OutTable)
--    local data = {}
--    data.CollisionBoxSize = configData.CollisionBoxSize
--    data.ComponentVarName = configData.ComponentVarName
--    data.NewCollisionChannelInfos =configData.NewCollisionChannelInfos
--    data.NewObjectType = configData.NewObjectType
--    data.NewCollisionState = configData.NewCollisionState
--    data.bNeedChangeCollisionState = configData.bChangeCollisionState
--    data.bNeedChangeObjectType = configData.bChangeObjectType
--    data.bOverrideCollisionBoxSize = configData.bOverrideCollisionBoxSize
--    -- 组装C++可以使用的结构体
--    BSFunc.AssembleFChangeCollisionMessage(data, OutTable)
--end

function SetCollision.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if Game.me and Game.me ~= entity then
		Game.me:ChangeImpetusTargetByEID(entity.CharacterID, entity.eid, true)
	end
end

HideWeapon = {} -- luacheck: ignore
function HideWeapon.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if entity == nil then
        return
    end
    if entity.SetWeaponVisibilityOnActorComplete ~= nil then
        entity:SetWeaponVisibilityOnActorComplete(false, Enum.EInVisibleReasons.SkillTaskControl)
    end
end

function HideWeapon.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if entity == nil then
        return
    end
    if entity.SetWeaponVisibilityOnActorComplete ~= nil then
        entity:SetWeaponVisibilityOnActorComplete(true, Enum.EInVisibleReasons.SkillTaskControl)
    end
end

MeshFollowBone = {} -- luacheck: ignore
MeshFollowBone.TEMP_ROT = M3D.Rotator()
MeshFollowBone.TEMP_SCALE = M3D.Vec3(1, 1, 1)

function MeshFollowBone.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    if (targetEID == nil) then
        return
    end
    local BoneName = configData.BoneName
	
    if not entity.bInWorld then
        return
    end

    -- 自身的骨骼
    local TCompID = entity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
    if TCompID <= 0 then
        return
    end

    local targetEntity = Game.EntityManager:GetEntityByIntID(targetEID)
	if not targetEntity.bInWorld then
		return
	end

    local GID = ULLFunc.GetGlobalUniqueID()
    -- 关闭第三方模型位置预测
    --UBSFunc.CloseActorMovement_P(entityCharacter, 8, GID, true)

    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = GID

    if configData.ShouldAttachRoot then
        -- 这里是临时处理先用attach的方案能让玩家位置跟随boss
        -- ULLFunc.ComponentAttachToComponent(Game.WorldContext, BCompGID, TCompGID, BoneName)
		targetEntity.CppEntity:KAPI_Actor_AttachToComponent(
			TCompID,
			BoneName,
			EAttachmentRule.SnapToTarget,
			EAttachmentRule.SnapToTarget,
			EAttachmentRule.SnapToTarget,
			false
		)
		local Entity = Game.EntityManager:GetEntityByIntID(targetEID)
		Entity:ChangeAttachMode(true)
		--Entity:SetUsingLateUpdate(false)
		--dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.OriginMoveDriveMode] = Entity.moveDriveMode
		--Entity:doSetMoveDriveMode(EMoveDriveMode.NoDrive)
		--Entity:SetDeadDetachListener(true)
    end
end

function MeshFollowBone.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    if (targetEID == nil) then
        return
    end
    local Entity = Game.EntityManager:GetEntityByIntID(targetEID)
    Entity:ChangeAttachMode(false)

    if configData.ShouldAttachRoot then
		Entity.CppEntity:KAPI_Actor_DetachFromAny(EDetachmentRule.KeepRelative)
    end
end

-- todo@shijingzhe:挂接点在Task里配置,Timer时间在Task中配置,终点位置在Task中配置
ThrowingSkill = {} -- luacheck: ignore

local __NameNone__ = "None" -- luacheck: ignore

function ThrowingSkill.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	local targetEntity = Game.EntityManager:getEntity(targetEID)
	local bossEntity = configData.PerformanceActionSlotID == 1 and entity or targetEntity
	local playerEntity = configData.PerformanceActionSlotID == 1 and targetEntity or entity

	-- 开启镜头缓动
	if playerEntity == Game.me then
		Game.me:DisableAllSkillType(ETE.EDisabledSkillReason.ThrowingSkill)
		if configData.bEnterCameraSlowMoveMode then
			ThrowingSkill.StartCameraEase(configData, dynamicBlackBoard)
		end
	end

	playerEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.ThrowingSkillControl)

	-- 旋转Mesh
	--local playerMeshCompID = playerEntity.CppEntity:KAPI_Actor_GetMainMesh()
	--playerEntity.CppEntity:KAPI_SceneID_SetRelativeRotation(playerMeshCompID, -90, 0, 0)

	-- 挂接
	local bossMeshCompID = bossEntity.CppEntity:KAPI_Actor_GetMainMesh()
	playerEntity.CppEntity:KAPI_Actor_AttachToComponent(bossMeshCompID, "hand_l", EAttachmentRule.SnapToTarget, EAttachmentRule.SnapToTarget, EAttachmentRule.KeepWorld, false)

	local playerHalfHeight = playerEntity.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()
	playerEntity.CppEntity:KAPI_SetRelativeLocation_P(playerHalfHeight, 0, 0)
	playerEntity.CppEntity:KAPI_SetRelativeRotation_P(-90, 0, 0)

	-- 开始播动作
	playerEntity:PlayAnimLibMontage("GrabSkill_RightBack", nil, nil, nil, 0, true)

    -- 其他玩家看向被投玩家
	if (configData.bOtherPlayerFocus == true) and (playerEntity ~= Game.me) and (Game.BSManager.bIsInEditor == false) then
		Game.me:StartGazeActor(Enum.EGazeTypeMap.THIRD_CAMERA,  playerEntity.CharacterID, __NameNone__)
	end

	local throwOutTimerID = Game.TimerManager:CreateTimerAndStart(function()
		ThrowingSkill.OnThrowOut(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	end, 2300, 1)
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ThrowOutTimerID] = throwOutTimerID
end

---被扔出时间点
function ThrowingSkill.OnThrowOut(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ThrowOutTimerID] = nil

	local targetEntity = Game.EntityManager:getEntity(targetEID)
	local bossEntity = configData.PerformanceActionSlotID == 1 and entity or targetEntity
	local playerEntity = configData.PerformanceActionSlotID == 1 and targetEntity or entity

	-- 取消挂接
	local attachRule = EAttachmentRule.KeepWorld
	playerEntity.CppEntity:KAPI_Actor_K2_DetachFromActor(attachRule, attachRule, attachRule)

	-- 切换LocoControl
	playerEntity.LocoControlOperator:UnDoUpperControlLogic(Enum.LocoControlUpperControlLogic.ThrowingSkillControl)
	playerEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.ThrowingSkillOut)

	-- 执行motionWrap
	local playerPosition = playerEntity:GetPosition()
	local bossPosition = bossEntity:GetPosition()
	local bossRotation = bossEntity:GetRotation()
	local bossHalfHeight = bossEntity.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()
	local landLocation = bossPosition + bossRotation:RotateVector(FVector(300, -300, 0))
	landLocation.Z = landLocation.Z - bossHalfHeight + playerEntity.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()

	playerEntity:SetRotation_P(0, 0, 0)
	playerEntity.CppEntity:KAPI_Movement_AddMoveLinearSpeed(playerPosition.X, playerPosition.Y, playerPosition.Z, landLocation.X, landLocation.Y, landLocation.Z, 0.1, false)

	--local motionWarpToken = playerEntity:ObtainDestSmoothCorrector(nil, 2, nil, playerEntity:GetPosition(), landLocation, playerEntity:GetRotation().Yaw, 0)
	--local motionWarpToken = playerEntity:ObtainMotionWarpForTranslationAndRotationWithFixMode(EMoveCorrectorObtainPriority.Action, landLocation, ERootWarpMode.KeepRoot, 0, 0, 0)
	--dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ThrowOutMotionWarpToken] = motionWarpToken
end

function ThrowingSkill.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	local throwOutTimerID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ThrowOutTimerID]
	if throwOutTimerID then
		Game.TimerManager:StopTimerAndKill(throwOutTimerID, true)
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ThrowOutTimerID] = nil
	end

	local targetEntity = Game.EntityManager:getEntity(targetEID)
	--local bossEntity = configData.PerformanceActionSlotID == 1 and entity or targetEntity
	local playerEntity = configData.PerformanceActionSlotID == 1 and targetEntity or entity

	-- 关闭镜头缓动
	if playerEntity == Game.me then
		Game.me:EnableAllSkillType(ETE.EDisabledSkillReason.ThrowingSkill)
		if configData.bEnterCameraSlowMoveMode then
			ThrowingSkill.StopCameraEase(dynamicBlackBoard)
		end
	end

	playerEntity.LocoControlOperator:UnDoUpperControlLogic(Enum.LocoControlUpperControlLogic.ThrowingSkillOut)

	-- motionWarp释放
	local motionWarpToken = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ThrowOutMotionWarpToken]
	if motionWarpToken then
		--playerEntity:ReleaseDestSmoothCorrector(motionWarpToken)
		--playerEntity:ReleaseMotionWarp(motionWarpToken)
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ThrowOutMotionWarpToken] = nil
	end

	-- 强制贴地防止穿帮
	playerEntity.CppEntity:KAPI_Character_SimpleSetActorStickGround()

	-- 播放起身动作
	playerEntity:PlayAnimLibMontage("Grab_StandUp", nil, nil, nil, nil, true)

	-- 取消其他玩家看向被投玩家
	if (configData.bOtherPlayerFocus == true) and (playerEntity ~= Game.me) and (Game.BSManager.bIsInEditor == false) then
		Game.me:UnGaze(Enum.EGazeTypeMap.THIRD_CAMERA)
	end
end

function ThrowingSkill.StartCameraEase(configData, dynamicBlackBoard)
	local lag = configData.CameraEaseLag
	local radius = configData.CameraSoftRadius

	local commandType = Game.CameraManager.ThirdModifierSetCommandType
	local xoyLagSetID = Game.CameraManager:ExecuteSetThirdCameraCommand(commandType.SetThirdCameraXOYLag, lag, 0, -1, 0)
	local zLagSetID =Game.CameraManager:ExecuteSetThirdCameraCommand(commandType.SetThirdCameraZLag, lag, 0, -1, 0)
	local xoyRadiusSetID =Game.CameraManager:ExecuteSetThirdCameraCommand(commandType.SetThirdCameraXOYSoftRadius, radius, 0, -1, 0)
	local zRadiusSetID =Game.CameraManager:ExecuteSetThirdCameraCommand(commandType.SetThirdCameraZSoftRadius, radius, 0, -1, 0)

	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraXOYLagSetID] = xoyLagSetID
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraZLagSetID] = zLagSetID
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraXOYRadiusSetID] = xoyRadiusSetID
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraZRadiusSetID] = zRadiusSetID
end

function ThrowingSkill.StopCameraEase(dynamicBlackBoard)
	local xoyLagSetID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraXOYLagSetID]
	local zLagSetID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraZLagSetID]
	local xoyRadiusSetID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraXOYRadiusSetID]
	local zRadiusSetID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.CameraZRadiusSetID]

	local commandType = Game.CameraManager.ThirdModifierSetCommandType
	Game.CameraManager:ExecuteStopSetThirdCameraCommand(commandType.SetThirdCameraXOYLag, xoyLagSetID)
	Game.CameraManager:ExecuteStopSetThirdCameraCommand(commandType.SetThirdCameraZLag, zLagSetID)
	Game.CameraManager:ExecuteStopSetThirdCameraCommand(commandType.SetThirdCameraXOYSoftRadius, xoyRadiusSetID)
	Game.CameraManager:ExecuteStopSetThirdCameraCommand(commandType.SetThirdCameraZSoftRadius, zRadiusSetID)
end

HidePet = {} -- luacheck: ignore
function HidePet.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)

    if configData == nil or entity == nil then
		return
    end

	if entity.RefreshPerformPetVisibility == nil then
		entity = Game.EntityManager:getEntity(entity.FinalOwnerID)
	end
	if entity == nil or entity.RefreshPerformPetVisibility == nil then
		return
	end

    entity:RefreshPerformPetVisibility(false, Enum.EInVisibleReasons.SkillTaskControl)
end

function HidePet.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if configData == nil or entity == nil then
        return
    end

	if entity.RefreshPerformPetVisibility == nil then
		entity = Game.EntityManager:getEntity(entity.FinalOwnerID)
	end
	if entity == nil or entity.RefreshPerformPetVisibility == nil then
		return
	end

    entity:RefreshPerformPetVisibility(true, Enum.EInVisibleReasons.SkillTaskControl)
end

MaterialEffect = {} -- luacheck: ignore
-- 目前支持的材质表现类型
MaterialEffect.MaterialEffectType = {
	Dissolve = 0,
	Edge = 1,
	Blacken = 2,
	ModifyMaterial = 3,
	Opacity = 4,
	Fresnel = 5,
	Gray = 6,
	Surface = 7,
    Petrify = 8, -- 石化
    Emissive = 9, -- 自发光 
}
-- 目前支持的材质参数更新类型
MaterialEffect.ParamUpdateType = {
	ConstVal = 0,
	LinearSample = 1,
	Curve = 2
}

-- 目前支持的材质查找方式
MaterialEffect.DissolveEffectType = {
	NormalDissolve = 0,
	BlackDissolve = 1,
	DitherDissolve = 2,
	NoiseDissolve = 3,
	FxDissolve = 4,
	VisionaryDissolve = 5,
}

MaterialEffect.AttachEntityType = {
	WorldViewConst.ATTACH_REASON.Weapon,
}

MaterialEffect.EffectivePartType = {
    Character = 0,
    CharacterAllWeapons = 1,
	BPEffect = 2,
}

function MaterialEffect.ProcessFloatMaterialParam(paramName, paramConfig, changeMaterialParamReq, taskConfig)
	if paramConfig.ParamUpdateType == MaterialEffect.ParamUpdateType.ConstVal then
		if changeMaterialParamReq.ScalarParams == nil then
			changeMaterialParamReq.ScalarParams = {}
		end

		changeMaterialParamReq.ScalarParams[paramName] = paramConfig.ConstParamValue
	elseif paramConfig.ParamUpdateType == MaterialEffect.ParamUpdateType.LinearSample then
		if changeMaterialParamReq.ScalarLinearSampleParams == nil then
			changeMaterialParamReq.ScalarLinearSampleParams = {}
		end

		changeMaterialParamReq.ScalarLinearSampleParams[paramName] = {
			StartVal = paramConfig.StartVal,
			EndVal = paramConfig.EndVal,
			Duration = paramConfig.Duration,
		}
	elseif paramConfig.ParamUpdateType == MaterialEffect.ParamUpdateType.Curve then
		if changeMaterialParamReq.FloatCurveParams == nil then
			changeMaterialParamReq.FloatCurveParams = {}
		end

		changeMaterialParamReq.FloatCurveParams[paramName] = {
			AssetPath = paramConfig.Curve.CurveAsset,
			bEnableLoop = paramConfig.bEnableLoop,
			RemapTime = taskConfig.Duration,
		}
	end
end

function MaterialEffect.ProcessVectorOrLinearColorMaterialParam(paramName, paramConfig, changeMaterialParamReq, taskConfig, bLinearColor)
    if paramConfig.ParamUpdateType == MaterialEffect.ParamUpdateType.ConstVal then
        if changeMaterialParamReq.VectorParams == nil then
            changeMaterialParamReq.VectorParams = {}
        end

        changeMaterialParamReq.VectorParams[paramName] = {
            R = paramConfig.ConstParamValue.R,
            G = paramConfig.ConstParamValue.G,
            B = paramConfig.ConstParamValue.B,
            A = paramConfig.ConstParamValue.A,
        }
    elseif paramConfig.ParamUpdateType == MaterialEffect.ParamUpdateType.LinearSample then
        if changeMaterialParamReq.VectorLinearSampleParams == nil then
            changeMaterialParamReq.VectorLinearSampleParams = {}
        end

        local StartVal = paramConfig.StartVal
        local EndVal = paramConfig.EndVal
        changeMaterialParamReq.VectorLinearSampleParams[paramName] = {
            StartR = StartVal.R, StartG = StartVal.G, StartB = StartVal.B, StartA = StartVal.A,
            EndR = EndVal.R, EndG = EndVal.G, EndB = EndVal.B, EndA = EndVal.A,
            Duration = paramConfig.Duration,
        }
    elseif paramConfig.ParamUpdateType == MaterialEffect.ParamUpdateType.Curve then
        if bLinearColor then
            if changeMaterialParamReq.LinearColorCurveParams == nil then
                changeMaterialParamReq.LinearColorCurveParams = {}
            end

            changeMaterialParamReq.LinearColorCurveParams[paramName] = {
                AssetPath = paramConfig.Curve.CurveAsset,
                bEnableLoop = paramConfig.bEnableLoop,
                RemapTime = taskConfig.Duration,
            }
        else
            if changeMaterialParamReq.VectorCurveParams == nil then
                changeMaterialParamReq.VectorCurveParams = {}
            end

            changeMaterialParamReq.VectorCurveParams[paramName] = {
                AssetPath = paramConfig.Curve.CurveAsset,
                bEnableLoop = paramConfig.bEnableLoop,
                RemapTime = taskConfig.Duration,
            }
        end

    end
end

function MaterialEffect.ProcessVectorMaterialParam(paramName, paramConfig, changeMaterialParamReq, taskConfig)
    MaterialEffect.ProcessVectorOrLinearColorMaterialParam(paramName, paramConfig, changeMaterialParamReq, taskConfig, false)
end

function MaterialEffect.ProcessLinearColorMaterialParam(paramName, paramConfig, changeMaterialParamReq, taskConfig)
    MaterialEffect.ProcessVectorOrLinearColorMaterialParam(paramName, paramConfig, changeMaterialParamReq, taskConfig, true)
end

function MaterialEffect.ApplySearchMaterialParams(materialReq, searchParams)
	materialReq.SearchMeshType = searchParams.SearchMeshType
	materialReq.SearchMeshName = searchParams.SearchMeshName
    materialReq.SearchMaterialType = searchParams.SearchMaterialType
	if searchParams.SearchMaterialType == SEARCH_MATERIAL_TYPE.SearchMaterialBySlots then
		materialReq.MaterialSlotNames = searchParams.MaterialSlotNames
	end
end

function MaterialEffect.ProcessDissolveEffect(configData)
    local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
    local ChangeMaterialReq

    local DissolveParams = configData.DissolveMaterialParams
    if DissolveParams.DissolveType == MaterialEffect.DissolveEffectType.NormalDissolve then
		ChangeMaterialParamReq.bUseOLMDissolve = true
        MaterialEffect.ProcessFloatMaterialParam("_DissolveAlpha", DissolveParams.DissolveAlpha, ChangeMaterialParamReq, configData)

        if DissolveParams.bOverrideDissolveEdgeColor then
            MaterialEffect.ProcessLinearColorMaterialParam("DissolveEdgeColor", DissolveParams.DissolveEdgeColor, ChangeMaterialParamReq, configData)
        end
        if DissolveParams.bOverrideDissolveEdgeWidth then
            MaterialEffect.ProcessFloatMaterialParam("DissolveEdgeWidth", DissolveParams.DissolveEdgeWidth, ChangeMaterialParamReq, configData)
        end
        if DissolveParams.bOverrideDissolveNoiseIntensity then
            MaterialEffect.ProcessFloatMaterialParam("DissolveNoiseIntensity", DissolveParams.DissolveNoiseIntensity, ChangeMaterialParamReq, configData)
        end

    elseif DissolveParams.DissolveType == MaterialEffect.DissolveEffectType.NoiseDissolve then
		ChangeMaterialParamReq.bUseOLMDissolve = true
        MaterialEffect.ProcessFloatMaterialParam("_DissolveAlpha", DissolveParams.DissolveAlpha, ChangeMaterialParamReq, configData)
        ChangeMaterialParamReq.VectorParams = {}
        ChangeMaterialParamReq.VectorParams["DissolveDirection"] = { R = 0, G = 0, B = 1, A = 0 }

    elseif DissolveParams.DissolveType == MaterialEffect.DissolveEffectType.BlackDissolve then
        MaterialEffect.ProcessFloatMaterialParam("_DissolveAlpha", DissolveParams.DissolveAlpha, ChangeMaterialParamReq, configData)

        ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
        ChangeMaterialReq.MaterialPath = DissolveParams.MaterialPath

    elseif DissolveParams.DissolveType == MaterialEffect.DissolveEffectType.DitherDissolve then
		ChangeMaterialParamReq.bUseOLMDissolve = true
        MaterialEffect.ProcessFloatMaterialParam("_DitherAlpha", DissolveParams.DitherAlpha, ChangeMaterialParamReq, configData)

    elseif DissolveParams.DissolveType == MaterialEffect.DissolveEffectType.FxDissolve then
        if DissolveParams.bOverrideDissolveProgress then
            MaterialEffect.ProcessFloatMaterialParam("Dissolve Progress", DissolveParams.DissolveProgress, ChangeMaterialParamReq, configData)
        end
        if DissolveParams.bOverrideDissolve then
            MaterialEffect.ProcessFloatMaterialParam("Dissolve", DissolveParams.Dissolve, ChangeMaterialParamReq, configData)
        end

    elseif DissolveParams.DissolveType == MaterialEffect.DissolveEffectType.VisionaryDissolve then
        MaterialEffect.ProcessFloatMaterialParam("Dissolve_Move", DissolveParams.DissolveMove, ChangeMaterialParamReq, configData)

    end

    -- 为每个slot设置不同的溶解参数的情况下, 暂不归于dissolve类型管理
    if DissolveParams.SearchMaterialType ~= SEARCH_MATERIAL_TYPE.SearchMaterialBySlots then
        ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Dissolve
    end

    MaterialEffect.ApplySearchMaterialParams(ChangeMaterialParamReq, DissolveParams)
    if ChangeMaterialReq then
        MaterialEffect.ApplySearchMaterialParams(ChangeMaterialReq, DissolveParams)
    end

    return ChangeMaterialReq, ChangeMaterialParamReq
end

function MaterialEffect.ProcessOpacityEffect(configData)
	local ChangeMaterialReq
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Opacity
	local OpacityMaterialParams = configData.OpacityMaterialParams

	if OpacityMaterialParams.MaterialPath ~= "" then
		ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)

		ChangeMaterialReq.MaterialPath = OpacityMaterialParams.MaterialPath
        MaterialEffect.ApplySearchMaterialParams(ChangeMaterialReq, OpacityMaterialParams)
	end
    
    MaterialEffect.ApplySearchMaterialParams(ChangeMaterialParamReq, OpacityMaterialParams)
	MaterialEffect.ProcessFloatMaterialParam("Opacity", OpacityMaterialParams.Opacity, ChangeMaterialParamReq, configData)

	return ChangeMaterialReq, ChangeMaterialParamReq
end

function MaterialEffect.ProcessFresnelEffect(configData)
	local ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Fresnel
	local MaterialParams = configData.FresnelMaterialParams

	ChangeMaterialReq.MaterialPath = MaterialParams.MaterialPath
	ChangeMaterialReq.SearchMaterialType = SEARCH_MATERIAL_TYPE.SearchOverlayMaterial
	ChangeMaterialReq.SearchMeshType = MaterialParams.SearchMeshType
	ChangeMaterialReq.SearchMeshName = MaterialParams.SearchMeshName
    
    ChangeMaterialParamReq.SearchMaterialType = SEARCH_MATERIAL_TYPE.SearchOverlayMaterial
	ChangeMaterialParamReq.SearchMeshType = MaterialParams.SearchMeshType
	ChangeMaterialParamReq.SearchMeshName = MaterialParams.SearchMeshName
	MaterialEffect.ProcessFloatMaterialParam("Fresnel Intensity", MaterialParams.FresnelIntensity, ChangeMaterialParamReq, configData)

	return ChangeMaterialReq, ChangeMaterialParamReq
end

function MaterialEffect.ProcessBlackenEffect(configData)
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Blacken
	local BlackenMaterialParams = configData.BlackenMaterialParams
	MaterialEffect.ProcessFloatMaterialParam("_BlackenAlpha", BlackenMaterialParams.BlackenAlpha, ChangeMaterialParamReq, configData)

	return nil, ChangeMaterialParamReq
end

function MaterialEffect.ProcessEdgeEffect(configData)
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Edge
	local EdgeMaterialParams = configData.EdgeMaterialParams
	if EdgeMaterialParams.bOverrideEdgeIntensity then
		MaterialEffect.ProcessFloatMaterialParam("Edge Intensity", EdgeMaterialParams.EdgeIntensity, ChangeMaterialParamReq, configData)
	end
	if EdgeMaterialParams.bOverrideEdgePow then
		MaterialEffect.ProcessFloatMaterialParam("Edge Pow", EdgeMaterialParams.EdgePow, ChangeMaterialParamReq, configData)
	end
	if EdgeMaterialParams.bOverrideEdgeColor then
		MaterialEffect.ProcessLinearColorMaterialParam("Edge Color", EdgeMaterialParams.EdgeColor, ChangeMaterialParamReq, configData)
	end
	MaterialEffect.ApplySearchMaterialParams(ChangeMaterialParamReq, EdgeMaterialParams)

	return nil, ChangeMaterialParamReq
end

function MaterialEffect.ProcessModifyMaterialEffect(configData)
	local ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
	local ModifyMaterialParams = configData.ModifyMaterialParams

	ChangeMaterialReq.MaterialPath = ModifyMaterialParams.MaterialPath
	MaterialEffect.ApplySearchMaterialParams(ChangeMaterialReq, ModifyMaterialParams.SearchMaterialParams)

	return ChangeMaterialReq, nil
end

function MaterialEffect.ProcessGrayEffect(configData)
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Gray
	local GrayMaterialParams = configData.GrayMaterialParams
	if GrayMaterialParams.bOverrideWidth then
		MaterialEffect.ProcessFloatMaterialParam("GraySoftnessWidth", GrayMaterialParams.Width, ChangeMaterialParamReq, configData)
	end
	if GrayMaterialParams.bOverrideHeight then
		MaterialEffect.ProcessFloatMaterialParam("GrayHeight", GrayMaterialParams.Height, ChangeMaterialParamReq, configData)
	end
	ChangeMaterialParamReq.SearchMeshType = GrayMaterialParams.SearchMeshType
	ChangeMaterialParamReq.SearchMeshName = GrayMaterialParams.SearchMeshName

	return nil, ChangeMaterialParamReq
end

function MaterialEffect.ProcessSurfaceEffect(configData)
    local ChangeMaterialReq
    local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
    ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Surface
    local MaterialParams = configData.SurfaceMaterialParams

    if MaterialParams.MaterialPath ~= "" then
        ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
        ChangeMaterialReq.MaterialPath = MaterialParams.MaterialPath
        MaterialEffect.ApplySearchMaterialParams(ChangeMaterialReq, MaterialParams)
    end
    
    MaterialEffect.ApplySearchMaterialParams(ChangeMaterialParamReq, MaterialParams)
    if MaterialParams.bOverrideSurfaceIntensity then
        MaterialEffect.ProcessFloatMaterialParam("SurfaceIntensity", MaterialParams.SurfaceIntensity, ChangeMaterialParamReq, configData)
    end
    if MaterialParams.bOverrideMainTexColor then
        MaterialEffect.ProcessLinearColorMaterialParam("MainTex Color", MaterialParams.MainTexColor, ChangeMaterialParamReq, configData)
    end
    if MaterialParams.bOverrideMainTexColorIntensity then
        MaterialEffect.ProcessFloatMaterialParam("MainTex Color Intensity", MaterialParams.MainTexColorIntensity, ChangeMaterialParamReq, configData)
    end
    if MaterialParams.bOverrideRadius then
        MaterialEffect.ProcessFloatMaterialParam("Radius", MaterialParams.Radius, ChangeMaterialParamReq, configData)
    end

    return ChangeMaterialReq, ChangeMaterialParamReq
end

function MaterialEffect.ProcessPetrifyEffect(configData)
	local ChangeMaterialReq
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Petrify
	local MaterialParams = configData.PetrifyMaterialParams

	if MaterialParams.MaterialPath ~= "" then
		ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
		ChangeMaterialReq.MaterialPath = MaterialParams.MaterialPath
		MaterialEffect.ApplySearchMaterialParams(ChangeMaterialReq, MaterialParams.SearchMaterialParams)
	end

	MaterialEffect.ApplySearchMaterialParams(ChangeMaterialParamReq, MaterialParams.SearchMaterialParams)
	MaterialEffect.ProcessFloatMaterialParam("Dissolve_Move", MaterialParams.DissolveMove, ChangeMaterialParamReq, configData)

	return ChangeMaterialReq, ChangeMaterialParamReq
end

function MaterialEffect.ProcessEmissiveEffect(configData)
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Emissive
	local MaterialParams = configData.EmissiveParams

	MaterialEffect.ApplySearchMaterialParams(ChangeMaterialParamReq, MaterialParams.SearchMaterialParams)
	if MaterialParams.bOverrideEmissiveColor then
		MaterialEffect.ProcessLinearColorMaterialParam("EmissiveColor", MaterialParams.EmissiveColor, ChangeMaterialParamReq, configData)
	end

	return nil, ChangeMaterialParamReq
end

MaterialEffect.ProcessEffectFuncMapping = {}
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Dissolve] = MaterialEffect.ProcessDissolveEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Blacken] = MaterialEffect.ProcessBlackenEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Edge] = MaterialEffect.ProcessEdgeEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Opacity] = MaterialEffect.ProcessOpacityEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Fresnel] = MaterialEffect.ProcessFresnelEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Gray] = MaterialEffect.ProcessGrayEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.ModifyMaterial] = MaterialEffect.ProcessModifyMaterialEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Surface] = MaterialEffect.ProcessSurfaceEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Petrify] = MaterialEffect.ProcessPetrifyEffect
MaterialEffect.ProcessEffectFuncMapping[MaterialEffect.MaterialEffectType.Emissive] = MaterialEffect.ProcessEmissiveEffect

function MaterialEffect.DoChangeMaterial(entity, ChangeMaterialReq, ChangeMaterialParamReq, dynamicBlackBoard)
    -- 应用在dynamic mesh上的材质效果 effect type直接置空
    if ChangeMaterialParamReq ~= nil and ChangeMaterialParamReq.SearchMeshType == SEARCH_MESH_TYPE.SearchMeshByName and
        entity:HasDynamicMesh(ChangeMaterialParamReq.SearchMeshName) then
        ChangeMaterialParamReq.EffectType = nil
        ChangeMaterialParamReq.bIgnoreExcludedMeshComp = true
    end

    if ChangeMaterialReq ~= nil and ChangeMaterialReq.SearchMeshType == SEARCH_MESH_TYPE.SearchMeshByName and
        entity:HasDynamicMesh(ChangeMaterialReq.SearchMeshName) then
        ChangeMaterialReq.bIgnoreExcludedMeshComp = true
    end

    if ChangeMaterialReq ~= nil and ChangeMaterialParamReq ~= nil then
        local handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
        local reqID = entity:ChangeMaterialAndMaterialParam(ChangeMaterialReq, ChangeMaterialParamReq)
        if handleIDs == nil then
            dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = {}
            handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
        end
        handleIDs[entity.eid] = reqID

    elseif ChangeMaterialReq ~= nil then
        local handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialReqId]
        local reqID = entity:ChangeMaterial(ChangeMaterialReq)
        if handleIDs == nil then
            dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialReqId] = {}
            handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialReqId]
        end
        handleIDs[entity.eid] = reqID

    elseif ChangeMaterialParamReq ~= nil then
        local handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialParamReqId]
        local reqID = entity:ChangeMaterialParam(ChangeMaterialParamReq)
        if handleIDs == nil then
            dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialParamReqId] = {}
            handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialParamReqId]
        end
        handleIDs[entity.eid] = reqID
    else
        Log.Error("no valid change material request found")
    end
end

function MaterialEffect.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)

	local InstigatorEntity = entity
	if staticBlackBoard.instigatorID then
		local instigator = Game.EntityManager:getEntity(staticBlackBoard.instigatorID)
		if instigator then
			InstigatorEntity = instigator
		end
	end

	local EffectiveClient = configData.EffectiveClient
	if EffectiveClient == EEffectiveClientType.P3 then
		if Game.me == InstigatorEntity then 
			return
		end
	end

	if EffectiveClient == EEffectiveClientType.P1 then
		if Game.me ~= InstigatorEntity then 
			return
		end
	end

	local token = Game.WorldManager.ViewBudgetMgr:TryRequestViewFrequency_MATERIAL(entity)
    if not token then
        return
    end

    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.MaterialToken] = token
    local ProcessFunc = MaterialEffect.ProcessEffectFuncMapping[configData.MaterialEffectType]

	if configData.EffectivePartType == MaterialEffect.EffectivePartType.CharacterAllWeapons then
		local attachEntityIds = entity:GetAttachEntitiesByAttachReason(WorldViewConst.ATTACH_REASON.Weapon)
		if attachEntityIds ~= nil then
			for _, attachEntityId in ipairs(attachEntityIds) do
				local ChangeMaterialReq, ChangeMaterialParamReq = ProcessFunc(configData)
				local attachEntity = Game.EntityManager:getEntity(attachEntityId)
				MaterialEffect.DoChangeMaterial(attachEntity, ChangeMaterialReq, ChangeMaterialParamReq, dynamicBlackBoard)
			end
			
		end
	elseif configData.EffectivePartType == MaterialEffect.EffectivePartType.BPEffect then
		local BPEntity = entity:GetBPEffectByName(configData.BPName)
		if BPEntity then
			local ChangeMaterialReq, ChangeMaterialParamReq = ProcessFunc(configData)
			if configData.InterruptMode ~= TASK_INTERRUPT_TYPE.ONLY_BIND_SKILL then
				if ChangeMaterialReq then
					ChangeMaterialReq.TotalLifeMs = configData.Duration * 1000
				end
				if ChangeMaterialParamReq then
					ChangeMaterialParamReq.TotalLifeMs = configData.Duration * 1000
				end
			end
			MaterialEffect.DoChangeMaterial(BPEntity, ChangeMaterialReq, ChangeMaterialParamReq, dynamicBlackBoard)
		end
	else
		local ChangeMaterialReq, ChangeMaterialParamReq = ProcessFunc(configData)
		if configData.InterruptMode ~= TASK_INTERRUPT_TYPE.ONLY_BIND_SKILL then
			if ChangeMaterialReq then
				ChangeMaterialReq.TotalLifeMs = configData.Duration * 1000
			end
			if ChangeMaterialParamReq then
				ChangeMaterialParamReq.TotalLifeMs = configData.Duration * 1000
			end
		end
		MaterialEffect.DoChangeMaterial(entity, ChangeMaterialReq, ChangeMaterialParamReq, dynamicBlackBoard)
	end
end

function MaterialEffect.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	local EffectiveClient = configData.EffectiveClient
	if EffectiveClient == EEffectiveClientType.P3 then
		if Game.me == entity then 
			return
		end
	end

	if EffectiveClient == EEffectiveClientType.P1 then
		if Game.me ~= entity then 
			return
		end
	end
    local token = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.MaterialToken]
    if token then
        Game.WorldManager.ViewBudgetMgr:ReleaseViewFrequencyToken(token)
    else
        return
    end

    -- 配置了NotInterrupt就不再恢复了 目前技能需求如此
    if configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
        return
    end

    local handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
    if handleIDs ~= nil then
        for eid, handleID in pairs(handleIDs) do
            local targetEntity = Game.EntityManager:getEntity(eid)
            if targetEntity then
                targetEntity:RevertMaterialAndMaterialParam(handleID)
            end
        end
    else
        handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialReqId]
        if handleIDs ~= nil then
            for eid, handleID in pairs(handleIDs) do
                local targetEntity = Game.EntityManager:getEntity(eid)
                if targetEntity then
                    targetEntity:RevertMaterial(handleID)
                end
            end
        else
            handleIDs = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.ChangeMaterialParamReqId]
			if handleIDs then
				for eid, handleID in pairs(handleIDs) do
					local targetEntity = Game.EntityManager:getEntity(eid)
					if targetEntity then
						targetEntity:RevertMaterialParam(handleID)
					end
				end
			end
        end
    end
end


BoneTrackActorTask = {} -- luacheck: ignore
function BoneTrackActorTask.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	local targetEntity = Game.EntityManager:getEntity(targetEID)
    if not targetEntity then
        return
    end 
	entity.CppEntity:KAPI_Animation_SetBoneTrackActorInfo(configData.BoneName, targetEntity.CharacterID, configData.Speed, M3D.ToFVector(configData.TargetPosOffset), configData.TrackDuration)
	entity.CppEntity:KAPI_Animation_SetAnimInsBoolProperty("bBoneTrackOtherActor", true)
end

function BoneTrackActorTask.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    entity.CppEntity:KAPI_Animation_DelBoneTrackActorInfo(configData.BoneName, false)
	entity.CppEntity:KAPI_Animation_SetAnimInsBoolProperty("bBoneTrackOtherActor", false)
end



HideBoneTask = {} -- luacheck: ignore
function HideBoneTask.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	local Meshid = entity.CppEntity:KAPI_Actor_GetMainMesh()
	entity.CppEntity:KAPI_SkeletalMeshID_HideBoneByName(Meshid, configData.BoneName, 0)
end

function HideBoneTask.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
   local Meshid = entity.CppEntity:KAPI_Actor_GetMainMesh()
	entity.CppEntity:KAPI_SkeletalMeshID_UnHideBoneByName(Meshid, configData.BoneName)
end


SetMoLangInvisible = {} -- luacheck: ignore
function SetMoLangInvisible.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if configData == nil then
		return
	end
	if staticBlackBoard.buffID == nil then
		return
	end


	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)

	ChangeMaterialParamReq.EffectType = nil
	ChangeMaterialParamReq.ScalarLinearSampleParams = {}
	ChangeMaterialParamReq.ScalarLinearSampleParams["tranparent"] = {
		StartVal = 0.7, EndVal = 0, Duration = 1
	}
	ChangeMaterialParamReq.ScalarLinearSampleParams["OpacityIntensity"] = {
		StartVal = 1, EndVal = 0, Duration = 1
	}

	entity:ChangeMaterialParam(ChangeMaterialParamReq)
	if entity.WillFadeoutEffectID then
		entity:UpdateLinearSampleScalarNiagaraParamTargetVal(entity.WillFadeoutEffectID, "Progress", 0, true, 1)
	end
end

function SetMoLangInvisible.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if configData == nil then
		return
	end
	if staticBlackBoard.buffID == nil then
		return
	end

	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)

	ChangeMaterialParamReq.EffectType = nil
	ChangeMaterialParamReq.ScalarLinearSampleParams = {}
	ChangeMaterialParamReq.ScalarLinearSampleParams["tranparent"] = {
		StartVal = 0, EndVal = 0.7, Duration = 1
	}
	ChangeMaterialParamReq.ScalarLinearSampleParams["OpacityIntensity"] = {
		StartVal = 0, EndVal = 1, Duration = 1
	}

	entity:ChangeMaterialParam(ChangeMaterialParamReq)
	if entity.WillFadeoutEffectID and Game.EffectManager:IsValidNiagaraEffectId(entity.WillFadeoutEffectID) then
		entity:UpdateLinearSampleScalarNiagaraParamTargetVal(entity.WillFadeoutEffectID, "Progress", 1, true, 0)
	end
end


function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.AddMesh, AddMesh, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.MeshVisibility, MeshVisibilityTask, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.SetCollision, SetCollision, TASK_STATE_DATA_TYPE.WITH_STATE)
    --EffectTaskFactory.Register(Enum.EffectTaskType.ModelScaleChange, ModelScaleChange, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.HideWeapon, HideWeapon, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.MeshFollowBone, MeshFollowBone, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.MaterialEffect, MaterialEffect, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.HidePet, HidePet)
	EffectTaskFactory.Register(Enum.EffectTaskType.ThrowingSkill, ThrowingSkill, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.BoneTrackActor, BoneTrackActorTask)
    EffectTaskFactory.Register(Enum.EffectTaskType.HideBone, HideBoneTask)
	EffectTaskFactory.Register(Enum.EffectTaskType.SetMoLangInvisible, SetMoLangInvisible, TASK_STATE_DATA_TYPE.WITH_STATE)
end