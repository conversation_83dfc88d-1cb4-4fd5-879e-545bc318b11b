--------------------------------------------------------
--- 持续类Task，需要实现Excute和End
--------------------------------------------------------



local UBSFunc = import("BSFunctionLibrary")
local KismetMathLibrary = import("KismetMathLibrary")
local sharedConst = kg_require("Shared.Const")
local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local NiagaraEffectParamTemplate = kg_require("GamePlay.Effect.NiagaraEffectParamTemplate")
local NiagaraEffectConst = kg_require("Gameplay.Effect.NiagaraEffectConst")
local NIAGARA_FACE_TO_ACTOR_TYPE = NiagaraEffectConst.NIAGARA_FACE_TO_ACTOR_TYPE

local Enum = Enum
local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE
local TASK_DYNAMIC_KEY_TYPE = sharedConst.TASK_DYNAMIC_KEY_TYPE
local STATIC_BLACK_BOARD_KEY_TYPE = sharedConst.STATIC_BLACK_BOARD_KEY_TYPE
local const = kg_require("Shared.Const")
local TASK_INTERRUPT_TYPE = const.TASK_INTERRUPT_TYPE
local EFFECT_INIT_ORIENT = NiagaraEffectConst.EFFECT_INIT_ORIENT
local NIAGARA_SOURCE_TYPE = NiagaraEffectConst.NIAGARA_SOURCE_TYPE
local NIAGARA_ATTACH_COMPONENT_TYPE = NiagaraEffectConst.NIAGARA_ATTACH_COMPONENT_TYPE
local TASK_TARGET_TYPE = const.COMBAT_EFFECT_TASK_TARGET_TYPE
local LINK_AGENT_TYPE = NiagaraEffectConst.LINK_NIAGARA_AGENT_TYPE
local NIAGARA_ATTACH_TYPE = NiagaraEffectConst.NIAGARA_ATTACH_TYPE
local NIAGARA_FOLLOW_MODE = NiagaraEffectConst.NIAGARA_FOLLOW_MODE
local LINK_NIAGARA_SKMESH_TYPE = NiagaraEffectConst.LINK_NIAGARA_SKMESH_TYPE
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local LUC = kg_require("Shared.Const.LogicUnitConst")
local ERelativeTransformSpace = import("ERelativeTransformSpace")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

ASTPlayNiagara = {}  -- luacheck: ignore
ASTPlayNiagara.LTrans_Actor = M3D.Transform()
ASTPlayNiagara.LR_Offset = M3D.Rotator()
-- 特效task配置的优先级类型
ASTPlayNiagara.NiagaraEffectPriority = {
	Ignorable = 0,
	Low = 1,
	Medium = 2,
	High = 3,
	Vital = 4,
	InheritOwner = 5
}
function ASTPlayNiagara.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (targetEID == nil) or (configData == nil) then
        return
    end

	if not configData.bPlayAtLast then
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = ASTPlayNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID)
	else
		dynamicBlackBoard.PlayUntilEnd = true
	end
end


-- todo 直接放到播放特效的底层接口  @王磊
function ASTPlayNiagara.TryFitGround(currTransform, configData, targetEID)
     -- 检测地面
     if (configData.bNeedCheckGround == true) then
        local Offset = configData.ExtraGroundMsg
        local ObjectTypes = BSFunc.EnumsToBitMask(configData.GroundCheckObjectTypes)
        local WLocation = currTransform.Translation
        local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
        if TEntity and TEntity.CharacterID then
            local FindGround, X, Y, Z = UBSFunc.FindGroundLocation_P(TEntity.CharacterID, TEntity.CharacterID, WLocation.X, WLocation.Y, WLocation.Z, ObjectTypes, Offset.X, Offset.Y, Offset.Z, M3D.Fill3())
            if (FindGround == true) then
                WLocation.X = X
                WLocation.Y = Y
                WLocation.Z = Z
            end
        end
    end
end

-- luacheck: ignore
function ASTPlayNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID, ExtraPos, extraNiagara)
    local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
    if (TEntity == nil) then
        return
    end
	-- 实际挂接的对象
	TEntity = ASTPlayNiagara.GetAttachEntity(TEntity, configData.AttachTargetType, staticBlackBoard)
	if TEntity == nil then
		return
	end
	--------------------------------------------------申请budget----------------------------------------------------------
	-- 角色类型由始作俑者来定, 申请budget则由真正特效的创建者申请
	local InstigatorEntity = entity
	if staticBlackBoard.instigatorID then
		local instigator = Game.EntityManager:getEntity(staticBlackBoard.instigatorID)
		if instigator then
			InstigatorEntity = instigator
		end
	end
    
    -- 只有特定类型的角色、特定的特效类型才会参与特效裁剪, 因此没有ViewControlBaseComponent的情况下也可以播放特效
	local CharacterTypeForViewBudget = InstigatorEntity.GetCharacterTypeForViewBudget and InstigatorEntity:GetCharacterTypeForViewBudget() or nil
	local EffectPriority = staticBlackBoard.EffectPriority
    local ForceEffectPriority
	if configData.EffectPriority and configData.EffectPriority ~= ASTPlayNiagara.NiagaraEffectPriority.InheritOwner then
		EffectPriority = configData.EffectPriority
		if configData.EffectPriority == ASTPlayNiagara.NiagaraEffectPriority.Vital then
			ForceEffectPriority = ASTPlayNiagara.NiagaraEffectPriority.Vital
		end
	end

	local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
			CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL, TEntity.CharacterID, 
			EffectPriority, ForceEffectPriority)
	if not bCanPlayNiagara then
		return
	end
	--------------------------------------------------申请budget结束----------------------------------------------------------

	--------------------------------------------------设置NiagaraEffectParam-------------------------------------------------
    local NiagaraEffectParam = ASTPlayNiagara.AssembleInitData(configData, staticBlackBoard, extraNiagara)
	NiagaraEffectParam.NiagaraBudgetToken = BudgetToken
	NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL
	NiagaraEffectParam.CustomNiagaraPriority = EffectPriority

	if Game.EffectManager:IsCheckNiagaraEffectTypeEnabled() then
		if staticBlackBoard.rootSkillID ~= nil and staticBlackBoard.rootSkillID ~= 0 then
			NiagaraEffectParam.SourceSkillIdDebugUse = staticBlackBoard.rootSkillID
		elseif staticBlackBoard.RootAbilityID ~= nil and staticBlackBoard.RootAbilityID ~= 0 then
			NiagaraEffectParam.SourceSkillIdDebugUse = staticBlackBoard.RootAbilityID
		end
	end
	
	-- todo 这块能不能直接额外接口， 有参数的时候, 直接送原值, 底下c++ 做？@王磊
    local Transform = NiagaraEffectParam.SpawnTrans
    local Rotator = ASTPlayNiagara.LR_Offset
    ASTPlayNiagara.ConvertOffset(configData.PosOffset, Transform.Translation)
    if configData.RotationQuat then
        -- 离线计算少转一次
        ASTPlayNiagara.ConvertOffset(configData.RotationQuat, Transform.Rotation)
    else
        Rotator:Pack(configData.RotOffset.Pitch, configData.RotOffset.Yaw, configData.RotOffset.Roll)
        Rotator:ToQuat(Transform.Rotation)
    end

    if NiagaraEffectParam.bAbsoluteScale then
        ASTPlayNiagara.ConvertOffset(configData.ScaleOffset, Transform.Scale3D)
    end
	-- 额外偏移
    if ExtraPos ~= nil then
        Transform.Translation.X = Transform.Translation.X + (ExtraPos.X or 0)
        Transform.Translation.Y = Transform.Translation.Y + (ExtraPos.Y or 0)
        Transform.Translation.Z = Transform.Translation.Z + (ExtraPos.Z or 0)
    end
	-- 蓄力时特效尺寸
	if configData.maxGazeDuration and configData.maxGazeDuration > 0 then
		local realScaleTimes = math.min((staticBlackBoard.chargingTime or 0) / configData.maxGazeDuration, 1)
		Transform.Scale3D:Mul(realScaleTimes, Transform.Scale3D)
	end

	--------------------------------------------------初步设置NiagaraEffectParam结束----------------------------------------------
	-- todo 这里这块, 感觉也可以底下直接做  @王磊
	--- 计算角色坐标 只有模式Not和INITIAL才需要
	local BoneName = ASTPlayNiagara.CheckTOPLOGOSocket(TEntity, configData.DockBone)
	local ActorTransform = ASTPlayNiagara.LTrans_Actor
    if configData.FollowMode == NIAGARA_FOLLOW_MODE.NOT then
		if configData.bWorldTransform == true then
			ActorTransform:Reset()
		else
			local LX, LY, LZ, RX, RY, RZ, RW, SX, SY, SZ = ASTPlayNiagara.CalculateCasterTransform(TEntity, entity, configData, targetEID)
			if NiagaraEffectParam.bAbsoluteScale then
				SX = 1
				SY = 1
				SZ = 1
			end
			ActorTransform:Pack(LX, LY, LZ, RX, RY, RZ, RW, SX, SY, SZ)
		end
        ASTPlayNiagara.ForcibleRot(BoneName, TEntity.eid, ActorTransform.Rotation)
        Transform:Mul(ActorTransform, Transform)
        ASTPlayNiagara.TryFitGround(Transform, configData, targetEID)
		if (configData.bNeedCheckGround == true) then
			-- 贴地计算完成后重新添加偏移，只需累加z轴就行 
			local offsetZ = configData.PosOffset.Z or configData.PosOffset[3]
			Transform.Translation.Z = Transform.Translation.Z + (offsetZ or 0)
		end
		if configData.bFadeoutWhenBorn then
			-- 特效需要做渐隐，暂时不开放Duration填写，因为目前只给魔狼使用，魔狼特效渐隐还有特殊的逻辑
			NiagaraEffectParam.UserVals_LinearSampleFloat = {["Progress"] = { StartVal=1, EndVal=0, Duration=9999}}
			local EffectID = entity:PlayNiagaraEffect(NiagaraEffectParam)
			entity.WillFadeoutEffectID = EffectID
			return EffectID
		end
		return entity:PlayNiagaraEffect(NiagaraEffectParam)
        
    else
        --模式1不是以世界坐标时设置初始角度
        if (configData.FollowMode == NIAGARA_FOLLOW_MODE.INITIAL and configData.bUseWorldCoordinate == false) then
			local _, _, _, RX, RY, RZ, RW, _, _, _ = ASTPlayNiagara.CalculateCasterTransform(TEntity, entity, configData, targetEID)
            ActorTransform:Pack(0, 0, 0, RX, RY, RZ, RW, 1, 1, 1)
            Transform:Mul(ActorTransform, Transform)
        end

        NiagaraEffectParam.bNeedAttach = true
        NiagaraEffectParam.AttachPointName = BoneName
        NiagaraEffectParam.AttachComponentName = configData.AttachComponentName
        if NiagaraEffectParam.AttachComponentName ~= "" and NiagaraEffectParam.AttachComponentName ~= nil then
            NiagaraEffectParam.NiagaraAttachComponentType = NIAGARA_ATTACH_COMPONENT_TYPE.FIND_ATTACH_COMPONENT_BY_COMPONENT_NAME
        else
            NiagaraEffectParam.NiagaraAttachComponentType = NIAGARA_ATTACH_COMPONENT_TYPE.FIND_ATTACH_COMPONENT_BY_SOCKET_NAME
        end
		if configData.AttachTargetType == NIAGARA_ATTACH_TYPE.GHOST and not TEntity.bInWorld then
			-- 还没进入场景的幽灵实体，等进入场景了自己去播放销毁特效
			NiagaraEffectParam.CustomEffectID = Game.EffectManager:GenerateEffectId()
			NiagaraEffectParam.TotalLifeMs = configData.Duration * 1000.0
			TEntity:AddStartNiagaraEffectParam(NiagaraEffectParam)
			return NiagaraEffectParam.CustomEffectID
		end

		if configData.bFadeoutWhenBorn then
			-- 特效需要做渐隐，暂时不开放Duration填写，因为目前只给魔狼使用，魔狼特效渐隐还有特殊的逻辑，如果需要的话可以再开放
			NiagaraEffectParam.UserVals_LinearSampleFloat = {["Progress"] = { StartVal=1, EndVal=0, Duration=9999}}
			local EffectID = TEntity:PlayNiagaraEffect(NiagaraEffectParam)
			entity.WillFadeoutEffectID = EffectID
			return EffectID
		end
		
		return TEntity:PlayNiagaraEffect(NiagaraEffectParam)
    end
end

--- 补丁，没有TOPLOGO挂点则挂到head上
function ASTPlayNiagara.CheckTOPLOGOSocket(targetId, SocketName)
	local targetEntity = Game.EntityManager:getEntity(targetId)
	if targetEntity then
		if (SocketName == "TOP_LOGO" and LuaScriptAPI.ActorHasSocket(targetEntity.CharacterID, SocketName) == false) then
			return "head"
		end
	end
	return SocketName
end

function ASTPlayNiagara.CalculateCasterTransform(tEntity, entity, configData, targetEID)
	local CasterTransform = M3D.Transform()
	local BoneName = ASTPlayNiagara.CheckTOPLOGOSocket(tEntity, configData.DockBone)
	if not tEntity.bInWorld then
		-- 还没进入场景的实体，直接使用其Transform,可能位置会不对
		CasterTransform:Pack(tEntity:GetTransform_P())
	else
		local entityComponentId = tEntity.CppEntity:KAPI_Actor_FxGetComponentBySocket(BoneName)
		local socketTrans = tEntity.CppEntity:KAPI_SceneID_GetSocketTransform(entityComponentId, BoneName, ERelativeTransformSpace.RTS_World)

		if socketTrans ~= nil then
			M3D.ToTransform(socketTrans, CasterTransform)
		end
	end
	local LX, LY, LZ, RX, RY, RZ, RW, SX, SY, SZ = CasterTransform:Unpack()
	local entityEID = entity:uid()
	--- 计算朝向
	local dir = ASTPlayNiagara.CalculateInitialOrientation(targetEID, entityEID, configData.InitialOrientation)
	if dir ~= nil then
		RX = dir.X or RX
		RY = dir.Y or RY
		RZ = dir.Z or RZ
		RW = dir.W or RW
	end
	return LX, LY, LZ, RX, RY, RZ, RW, SX, SY, SZ
end

function ASTPlayNiagara.GetAttachEntity(entity, type, staticBlackBoard)
	if type == NIAGARA_ATTACH_TYPE.SELF then
		return entity
	elseif type == NIAGARA_ATTACH_TYPE.WEAPON then
		local curWeapons = entity:GetWeaponEntities()
		if not curWeapons then return nil end
		-- 跟@zhangfeng确认过先返回第一把武器,后续如果要指定武器则需要武器系统这边先迭代
		return curWeapons[1]
	elseif type == NIAGARA_ATTACH_TYPE.DRAGON then
		return entity:GetPerformPetMinDragonEntity()
	elseif type == NIAGARA_ATTACH_TYPE.ADDED_MESH then
		return entity -- 直接返回实体,在NiagaraEffectParam.AttachComponentName处理
	elseif type == NIAGARA_ATTACH_TYPE.GHOST then
		local GhostUID = staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.LinkTarget]
		if GhostUID then
			return Game.EntityManager:getEntity(GhostUID)
		end
	end
end

function ASTPlayNiagara.ForcibleRot(SocketName, targetEID, InRot)
    --临时处理，在SocketName为Root，但没找到root节点时强制旋转90度，处理特效方向和角色方向不一致问题
    local targetEntity = Game.EntityManager:getEntity(targetEID)
    if targetEntity then
		if (SocketName == "root" and LuaScriptAPI.ActorHasSocket(targetEntity.CharacterID, SocketName) == false) then
			local rot = M3D.FRotToQuat(M3D.Rotator(0, -90, 0))
			InRot:Mul(rot, InRot)
		end
	end
end

function ASTPlayNiagara.CalculateInitialOrientation(TargetEID, OwnerEID, Mode)
	if Mode == EFFECT_INIT_ORIENT.ZERO then
		return M3D.Quat(0, 0, 0, 1)
	end
    if (TargetEID == OwnerEID) then
        return nil
    end
    -- Target类型时，不需要计算，因为后续运算就是基于target的，这里计算出的结果替换后续Target计算的方向
    local OwnerEntity = Game.EntityManager:GetEntityByIntID(OwnerEID)
    if (Mode == EFFECT_INIT_ORIENT.SELF) then
        local Rotator = M3D.Rotator()
		Rotator:Pack(OwnerEntity:GetRotation_P())
        return M3D.FRotToQuat(Rotator)
    elseif (Mode == EFFECT_INIT_ORIENT.CONNECT_LINE) then
        local OwnerLoc = M3D.Vec3()
		OwnerLoc:Pack(OwnerEntity:GetPosition_P())
        local TargetLoc = M3D.Vec3()
        local TargetEntity = Game.EntityManager:GetEntityByIntID(TargetEID)
		TargetLoc:Pack(TargetEntity:GetPosition_P())
        -- 特效以目标为基准，因此这里，释放者减去目标，得到方向；先不考虑Z轴
        local Direction = {X = OwnerLoc.X - TargetLoc.X, Y = OwnerLoc.Y - TargetLoc.Y, Z = 0} 
        if(Direction.X == 0 and Direction.Y == 0) then
            return nil
        end
        return M3D.GetRotationByDir(Direction)
    end
    return nil
end

function ASTPlayNiagara.GetFitNiagaraPath(configData, extraNiagara)
	if extraNiagara ~= nil and extraNiagara ~= "" then
		return extraNiagara
	end
    if PlatformUtil.IsMobilePlatform() and configData.NiagaraMobile ~= "" then
        -- 移动平台如配置则优先使用
        return configData.NiagaraMobile
    end
    return configData.Niagara
end

function ASTPlayNiagara.AssembleInitData(configData, staticBlackBoard, extraNiagara)
    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()

    NiagaraEffectParam.NiagaraEffectPath = ASTPlayNiagara.GetFitNiagaraPath(configData, extraNiagara)
    if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
        -- 20s保底限制
        NiagaraEffectParam.TotalLifeMs = (configData.Duration + 20) * 1000.0
    else
        NiagaraEffectParam.TotalLifeMs = -1.0
    end

    --支持延迟销毁
    NiagaraEffectParam.DelayDestroyMs = (configData.FadeOut and configData.FadeOut > 0 and configData.FadeOut * 1000.0) or 100
    -- 受发射者的时间减缓影响
    NiagaraEffectParam.bFollowSlomo = true
    -- 挂接时跟随被挂接者的显示、隐藏
    --InitData.bFollowHidden = true
    -- 创建裁剪
    -- InitData.bCreationCulling = configData.bCreationCulling
    -- 引擎裁剪
    NiagaraEffectParam.bEngineCulling = configData.bEngineCulling
    -- 播放速率
    NiagaraEffectParam.EffectPlayRate = configData.PlayRate or 1.0
    -- 透明度缩放
    NiagaraEffectParam.TransparencyScale = configData.Alpha or 1.0
    -- 其他
    NiagaraEffectParam.bAbsoluteRotation = (configData.FollowMode == NIAGARA_FOLLOW_MODE.INITIAL)
    NiagaraEffectParam.bAbsoluteScale = not configData.bAttachScale
	NiagaraEffectParam.bBenefitEffect = configData.bBenefitEffect
	NiagaraEffectParam.InstigatorEntityId = staticBlackBoard.instigatorID
	--NiagaraEffectParam.bIs3DFx = configData.Is3DFx
    if configData.Is3DFx then
        NiagaraEffectParam.bEnableCustomDepth = true
        NiagaraEffectParam.CustomDepthStencilValue = 1
        NiagaraEffectParam.bRenderInMainPass = true
    end
	NiagaraEffectParam.TranslucencySortPriority = configData.TranslucencySortPriority
	NiagaraEffectParam.TranslucencySortDistanceOffset = configData.TranslucencySortDistanceOffset
	NiagaraEffectParam.bIgnoreRelationCommonTags = configData.bIgnoreRelationCommonTags

    NiagaraEffectParam.SourceType = NIAGARA_SOURCE_TYPE.BATTLE
    -- 这里只有一个两个参数在用 需要先在特效制作上做修改 后面处理下
    -- InitData.NLMD = configData.NiagaraData

	if configData.bTowardInstigator then
		local instigator = Game.EntityManager:getEntity(staticBlackBoard.instigatorID)
		if instigator then
			NiagaraEffectParam.FaceToActorMode = NIAGARA_FACE_TO_ACTOR_TYPE.FaceToCustomActor
			NiagaraEffectParam.CustomFacingTargetActorId = instigator.CharacterID
		end
	end
	
	if configData.bFaceToActor then
		-- 目前只处理朝向相机，朝向其他需要CharacterID，等需求在加
		if configData.FaceToActorType == NIAGARA_FACE_TO_ACTOR_TYPE.FaceToCamera then
			NiagaraEffectParam.FaceToActorMode = NIAGARA_FACE_TO_ACTOR_TYPE.FaceToCamera
		--elseif configData.FaceToActorType == NIAGARA_FACE_TO_ACTOR_TYPE.FaceToCustomActor then
		--	local instigator = Game.EntityManager:getEntity(staticBlackBoard.instigatorID)
		--	NiagaraEffectParam.FaceToActorMode = NIAGARA_FACE_TO_ACTOR_TYPE.FaceToCustomActor
		--	NiagaraEffectParam.CustomFacingTargetActorId = instigator.CharacterID
		end
		NiagaraEffectParam.FaceToActorRotationType = configData.FaceToActorRotationType
	end
	
	if configData.bSpiritualVision then
		NiagaraEffectParam.bSpiritualVision = true
		NiagaraEffectParam.SpiritualVisionMeshColor = configData.MeshColor
	end
	
	if configData.FloatCurve and ksbcnext(configData.FloatCurve) then
		NiagaraEffectParam.UserVals_FloatCurves = {}
		NiagaraEffectParam.UserVals_FloatCurveRemapTime = {}
		for ParamName, ParamVal in ksbcpairs(configData.FloatCurve) do
			NiagaraEffectParam.UserVals_FloatCurves[ParamName] = ParamVal.CurveAsset
			NiagaraEffectParam.UserVals_FloatCurveRemapTime[ParamName] = configData.Duration
		end
	end
	
	-- 连接特效使用
	if configData.SkeletalFilteredBones and ksbcnext(configData.SkeletalFilteredBones) then
		NiagaraEffectParam.UserVals_SkeletalMeshCompFilterBones = {}
		for ParamName, ParamVal in ksbcpairs(configData.SkeletalFilteredBones) do
			if #ParamVal.List > 0 then
				local Bones = {}
				for _, FilterBoneName in ksbcipairs(ParamVal.List) do
					table.insert(Bones, FilterBoneName)
				end
				NiagaraEffectParam.UserVals_SkeletalMeshCompFilterBones[ParamName] = Bones
			end
		end
	end

    if configData.ParticleColorScaleCurve and configData.ParticleColorScaleCurve.CurveAsset ~= "" then
        NiagaraEffectParam.ParticleColorScaleUpdateCurve = configData.ParticleColorScaleCurve.CurveAsset
        NiagaraEffectParam.ParticleColorScaleCurveTime = configData.Duration
    end
    return NiagaraEffectParam
end

function ASTPlayNiagara.FillSkeletalMeshUserVars(TargetEntity, ConfigParams, NiagaraEffectParam)
    if not TargetEntity or not ConfigParams then
        return
    end

	if NiagaraEffectParam.UserVals_MeshCompIds == nil then
		NiagaraEffectParam.UserVals_MeshCompIds = {}
	end

    for ParamName, ParamVal in ksbcpairs(ConfigParams) do
		local CompID = TargetEntity.CppEntity:KAPI_Actor_FxGetComponentByName(ParamVal)
        if CompID > 0 then
            NiagaraEffectParam.UserVals_MeshCompIds[ParamName] = CompID
        end
    end
end

function ASTPlayNiagara.ConvertOffset(Offset, InTable)
    --兼容数组和向量
    if Offset[1] ~= nil then
        InTable:Pack(Offset[1] or 0.0, Offset[2] or 0.0, Offset[3] or 0.0)
    elseif Offset.W then
        InTable:Pack(Offset.X or 0.0, Offset.Y or 0.0, Offset.Z or 0.0, Offset.W or 0.0)
    elseif Offset.X then
        InTable:Pack(Offset.X or 0.0, Offset.Y or 0.0, Offset.Z or 0.0)
    else
        InTable:Pack(Offset.Roll or 0.0, Offset.Pitch or 0.0, Offset.Yaw or 0.0)
    end
end

function ASTPlayNiagara.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (targetEID == nil) or (configData == nil) then
        return
    end

	if not configData.bPlayAtLast then
		if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
			return
		end

		local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
		if (TEntity == nil) then
			return
		end
		TEntity = ASTPlayNiagara.GetAttachEntity(TEntity, configData.AttachTargetType, staticBlackBoard)
		if TEntity == nil then
			return
		end
		if configData.FadeOut == 0 then
			-- 立即销毁
			TEntity:DestroyNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		else
			TEntity:DeactivateNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		end

		--TEntity:UnCacheNiagaraParamBefore(configData.AttachComponentName)
	elseif dynamicBlackBoard.PlayUntilEnd then
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = ASTPlayNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID)
	end

    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end


PlayRandomNiagaraInTable = {}  -- luacheck: ignore
function PlayRandomNiagaraInTable.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
	-- 因为没特效表了作废
    --local niagaraIDs = configData.NiagaraID
    --local randoms = configData.RandomRate
    --local niagaraID = PlayRandomNiagaraInTable.getRandomIndex(niagaraIDs, randoms)
    --dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = entity:PlayEffectTableNiagara(niagaraID, -1.0, true)
end

function PlayRandomNiagaraInTable.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
	-- 因为没特效表了作废
    --entity:DeactivateNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    --dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

function PlayRandomNiagaraInTable.getRandomIndex(intList, probList)
    -- 概率表为空，纯随机
    if #probList == 0 then
        local index = KismetMathLibrary.RandomIntegerInRange(1, #intList)
        return intList[index]
    end
    local randNum = KismetMathLibrary.RandomFloatInRange(0.0, 1.0)
    --归一化处理
    local sum = 0
    local len = math.min(#intList, #probList)
    for i = 1, len do
        sum = sum + probList[i]
    end
    local sumProb = 0
    for i = 1, len do
        sumProb = sumProb + probList[i]/sum
        if randNum <= sumProb then
            return intList[i]
        end
    end
end

PlayDecal = {}  -- luacheck: ignore
function PlayDecal.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    local newTaskData = PlayDecal.ConvertTaskData(configData)
    local target = Game.EntityManager:GetEntityByIntID(targetEID)
    if target == nil then
        target = entity
    end
	if not target.bInWorld then
		return
	end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = target:SpawnDecalToTarget(newTaskData)
end

function PlayDecal.ConvertTaskData(configData, OutTaskData)
    if (OutTaskData == nil) then
        OutTaskData = configData
    end

    if configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
        OutTaskData.LifeTimeMs = math.min(configData.Duration or 0, 20) * 1000.0
    end
    return OutTaskData
end

function PlayDecal.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    local target = Game.EntityManager:GetEntityByIntID(targetEID)
    if target == nil then
        target = entity
    end
	if not target.bInWorld then
		return
	end
	local decalEntityID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
    if configData.InterruptMode ~= TASK_INTERRUPT_TYPE.NOT_INTERRUPT and decalEntityID then
        target:DestroyDecalEntity(decalEntityID)
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PlayAudio = PlayAudio or {}

PlayAudio.LP_WorldTrans = M3D.Transform()
PlayAudio.LP_Relation = M3D.Vec3()

function PlayAudio.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

    if not configData.bPlayAtLast then
        local targetEnt = Game.EntityManager:getEntity(targetEID)
        if not Game.AkAudioManager:CanBattleSystemPostEvent(Enum.BattleAudioType.ATTACK, entity, targetEnt) then
            return
        end

        PlayAudio.PlayAudio(entity, targetEnt, configData, dynamicBlackBoard, staticBlackBoard)
    end
end

---@param IEntity table 执行task的Entity
---@param TEntity table 当前task的目标Entity
function PlayAudio.PlayAudio(IEntity, TEntity, configData, dynamicBlackBoard, staticBlackBoard)
    -- 实际要播放音频的Entity
    local audioPlayer = nil
    if (configData.TaskTargetType == nil) or (configData.TaskTargetType == TASK_TARGET_TYPE.OWNER) then
        audioPlayer = IEntity
    elseif configData.TaskTargetType == TASK_TARGET_TYPE.TARGET then
        audioPlayer = TEntity
    end

    if not audioPlayer then
        Log.WarningFormat("[PlayAudio] no audio player in skill:%s, targetType=%s, eventName=%s",
                staticBlackBoard.skillID, configData.TaskTargetType, configData.Audio)
        return
    end
	
	-- todo location计算, 一样的, 释放着的UID是有的, 需要的基础信息其实都可以拿到 @史惊哲
    -- todo@shijingzhe:支持导出为Event名,节省字符串split开销
    local strArr = string.split(configData.Audio,".")
    local sourceEventName = strArr[#strArr]
    local finalEventName = BSFunc.GetRealAudioPath(sourceEventName, IEntity, TEntity, audioPlayer)

	local playingID = 0
	if (configData.bNeedAttach == true) then
		playingID = audioPlayer:AkPostEventOnActor(finalEventName, true)
	else
        local postLoc = nil
		local Offset = configData.RelativeLocation or M3D.Vec3()
		if configData.bSetWorldLocation then
            postLoc = M3D.ToFVector(Offset)
		else
            PlayAudio.LP_WorldTrans:Reset()
            PlayAudio.LP_WorldTrans:Pack(audioPlayer:GetTransform_P())
            PlayAudio.LP_Relation:Reset()
            PlayAudio.LP_Relation:Pack(Offset.X or Offset[1], Offset.Y or Offset[2], Offset.Z or Offset[3])
            PlayAudio.LP_WorldTrans:TransformPosition(PlayAudio.LP_Relation, PlayAudio.LP_Relation)
            postLoc = M3D.ToFVector(PlayAudio.LP_Relation)
		end

        playingID = audioPlayer:AkPostEvent3D(finalEventName, postLoc, true)
	end

    if playingID ~= 0 then
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = playingID
    end
end

function PlayAudio.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

	if not configData.bPlayAtLast then
		local playingID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
		if (playingID ~= nil) and (playingID ~= 0) and (configData.InterruptMode ~= TASK_INTERRUPT_TYPE.NOT_INTERRUPT) then
			entity:AkStopEvent(playingID, math.floor((configData.FadeOut or 0.1)* 1000), configData.BlendOutType, true)
		end
	else
        local targetEnt = Game.EntityManager:getEntity(targetEID)
        if not Game.AkAudioManager:CanBattleSystemPostEvent(Enum.BattleAudioType.ATTACK, entity, targetEnt) then
            return
        end

        PlayAudio.PlayAudio(entity, targetEnt, configData, dynamicBlackBoard, staticBlackBoard)
	end
end

AddGhost = {}  -- luacheck: ignore
function AddGhost.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local entityOwner = entity
	if (entity == nil) or (configData == nil) then
		return
	end

    if entity.bUnit then
        entityOwner = Game.EntityManager:GetEntityByIntID(entity.instigatorID or entity.IEID)
        if (entityOwner == nil) then
            return
        end
    end
    
    ---如果是法术场创建，把创建的GhostEntity外观复制为玩家的
    local duplicateTargetEID
	if configData.TaskTargetType == 3 then
		duplicateTargetEID = entityOwner.IEID
	end
	if not duplicateTargetEID then
		duplicateTargetEID = entityOwner:uid()
	end
    local ghostIDs = {}
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.GhostTimer] = Game.TimerManager:CreateTimerAndStart(function()
        if entityOwner == nil or entityOwner.IsDead == true then
            return
        end

        local GhostActor = entityOwner:NewCommonSpawnGhost(duplicateTargetEID, configData, targetEID)
        if GhostActor then
            table.insert(ghostIDs, GhostActor:uid())
			staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.LinkTarget] = GhostActor:uid()
		else
			Log.ErrorFormat("AddGhost.Execute: GhostActor is nil, entity=%s, skillID=%s", entityOwner:uid(), staticBlackBoard.skillID)	
        end
    end, configData.GhostSpawnFrequency * 1000, -1, nil, nil, true)
	
    -- @shijingzhe:预算暂时注释,重构后恢复
    -- 目前这里是通过先修改本体 然后通过ghost copy本地mesh来实现材质表现的继承 这会导致无关的材质表现也被继承
    --if InitData.ChangeMaterialReq then
    --	local token = Game.WorldManager.ViewBudgetMgr:TryRequestViewFrequency_MATERIAL(entity)
    --	if not token then
    --		return
    --	end
    --	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.GhostToken] = token
    --	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.GhostMaterialReqId] = entity:ChangeMaterial(InitData.ChangeMaterialReq)
    --end

    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.GhostActorIds] = ghostIDs
end

function AddGhost.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if configData == nil then
        return
    end
	
    local token = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.GhostToken]
    if token then
        Game.WorldManager.ViewBudgetMgr:ReleaseViewFrequencyToken(token)
    end
	
	local Timer = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.GhostTimer]
	if Timer then
		Game.TimerManager:StopTimerAndKill(Timer, false)
	end
	
	local GhostActorIds = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.GhostActorIds]
	if GhostActorIds then
		for _, ghostID in pairs(GhostActorIds) do
			entity:NewEndGhost(ghostID, configData.bTerminateGhostWhenTaskEnd)
		end
	end
	staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.LinkTarget] = nil
end

ClientTimeline = {} -- luacheck: ignore
function ClientTimeline.Execute(entity, configData, staticBlackBoard)
    entity:StartAbilityRunner(configData.ClientTimelineID)
end

function ClientTimeline.End(entity, configData, staticBlackBoard)
    entity:StopAbilityRunner(configData.ClientTimelineID)
end

AddAttachment = {} -- luacheck: ignore
function AddAttachment.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if configData.AttachReason == nil then
        return
    end

    local AttachmentName = configData.AttachmentName
    if AttachmentName == nil or AttachmentName == "" then
        return
    end

    local AttachItem = entity:AddAttachment_V2(WorldViewConst.ATTACH_ITEM_TYPE.Skill, AttachmentName)
    if AttachItem then
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = AttachItem
        AttachItem:SetAttachToByModelCfgSocket(entity:uid(),WorldViewConst.ATTACH_REASON.Skill)
		AttachItem:ApplyAttach()
    end
end

function AddAttachment.OnLayerChange(entity, configData, staticBlackboard, oldLayer, newLayer, targetEID, taskEIDList, dynamicBlackBoard)
    if (not dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]) or (configData == nil)then
        return
    end

    local AttachItem = Game.EntityManager:GetEntityByIntID(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID].eid)
    if (AttachItem == nil) then
        return
    end

    if configData.Layer2Tag[newLayer] then
        if configData.Layer2Tag[oldLayer] then
            AttachItem:ClearAttachEffects("bysystemcall", tostring(configData.Layer2Tag[oldLayer]))
        end
        AttachItem:PlayAttachEffectByTag("bysystemcall", tostring(configData.Layer2Tag[newLayer]))
    end
end

function AddAttachment.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (not dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]) or (configData == nil)then
        return
    end

    entity:RemoveAttachItemsByEntityID_V2(WorldViewConst.ATTACH_ITEM_TYPE.Skill, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID].eid)
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

SpawnWaterWave = {} -- luacheck: ignore
function SpawnWaterWave.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (configData == nil) then
        return
    end
	
	if not entity:IsViewBudgetTagDownGradingBudgetPermit(WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.LOCO_ENVIRONMENT_INTERACT) and not Game.BSManager.bIsInEditor then
		return
	end
	
    if configData.KeepTrigger then
        -- 保底最低的触发间隔为0.033s,避免过高频率的水波纹触发
        if configData.TimeGap <= 0.033 then
			configData.TimeGap = 0.033
        end
    end
	local res, handleID = SpawnWaterWave.RequestWaterWaveOnce(configData, entity)
	if res then
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = handleID
	end
end

function SpawnWaterWave.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] then
        Game.WorldManager:UnRegisterWaterWaveTick(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
    end
end


-- todo 往下放, 接口里算 @胡江龙
function SpawnWaterWave.RequestWaterWaveOnce(configData, entity)
    local Trans = M3D.Transform()
    local Rotator = M3D.Rotator()
    local OffSet = M3D.Vec3(configData.OffsetX, configData.OffsetY, 0.0)
    Trans:Pack(entity:GetTransform_P())
    Rotator:Pack(entity:GetRotation_P())
    Rotator:RotateVector(OffSet, OffSet) --XY偏移转到世界空间
	local StartPos = M3D.Vec3(Trans.Translation.X + OffSet.X, Trans.Translation.Y + OffSet.Y, 0.0)
	local angle_rad = math.rad(configData.MoveDirAngle) --角度转弧度
	local moveDirX = math.cos(angle_rad)
	local moveDirY = math.sin(angle_rad)
	local MoveDir = M3D.Vec3(moveDirX, moveDirY, 0.0)
	local StartYaw = M3D.atan2(-OffSet.Y, OffSet.X)
	if configData.MoveSpeed > 1 then
		MoveDir:Normalize()
		if MoveDir:IsNearlyZero() == false then
			Rotator:RotateVector(MoveDir, MoveDir) --MoveDir偏移转到世界空间
			StartYaw = M3D.atan2(-MoveDir.Y, MoveDir.X)
		end
	end

	if StartYaw == nil then
		StartYaw = 0
	end
	
    return Game.WorldManager:RequestDWWMotorForOnce(configData.MotorTextureId,
			StartPos.X,
			StartPos.Y, 
			StartYaw,
            configData.ScaleX,
            configData.ScaleY,
            configData.WaveMaxHeight,
            configData.FoamScale,
			configData.TimeGap,
			MoveDir.X,MoveDir.Y,configData.MoveSpeed
    )
end

PlayRandomNiagara = {} -- luacheck: ignore
function PlayRandomNiagara.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    -- 随机时间延迟执行
    if (configData.TimeInfos == nil) then
        return
    end
    local timeList = PlayRandomNiagara.CalcPlayTimeline(configData.TimeInfos, configData.bEqualInterval)
    local timerIDs = {}
    local subDynamicBlackBoards = {}
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.AttackRandomNiagaraToken] = {}
    for i = 1, #timeList do
        local time = timeList[i]
        local subDynamicBlackBoard = {}
        local timerID = Game.TimerManager:CreateTimerAndStart(function()
            -- 随机位置权重处理（在旧的偏移位置上再加上偏移）
            local randomPos = PlayRandomNiagara.CalcRandomPosition(configData.RandomPosList)
            local gid = ASTPlayNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID, randomPos)
            subDynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = gid
        end, time * 1000, 1, nil, nil, false)
        table.insert(timerIDs, timerID)
        table.insert(subDynamicBlackBoards, subDynamicBlackBoard)
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = {}
    for i = 1, #timeList do
        if (timerIDs[i]) then
            dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID][timerIDs[i]] = subDynamicBlackBoards[i]
        end
    end
end

function PlayRandomNiagara.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

    local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
    if (TEntity == nil) then
        return
    end
    
    if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] then
        for id, sub in pairs(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]) do
            Game.TimerManager:StopTimerAndKill(id, false)
            if sub then
                if configData.InterruptMode ~= nil and configData.InterruptMode ~= TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
                    TEntity:DeactivateNiagaraSystem(sub[TASK_DYNAMIC_KEY_TYPE.HandleID])
                end
            end
        end
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
    end
end

function PlayRandomNiagara.CalcRandomPosition(posList)
   -- posList 四维向量集合，前三位是位置，第四位是权重
    local totalWeight = 0
    for i = 1, #posList do
        totalWeight = totalWeight + posList[i].W
    end
    if totalWeight == 0 then
        return M3D.Vec4(0, 0, 0, 0)
    end
    local randNum = KismetMathLibrary.RandomFloatInRange(0.0, totalWeight)
    local sumWeight = 0
    for i = 1, #posList do
        sumWeight = sumWeight + posList[i].W
        if randNum <= sumWeight then
            return posList[i]
        end
    end
    return M3D.Vec4(0, 0, 0, 0)
end

function PlayRandomNiagara.CalcPlayTimeline(timeInfos, bEqualInterval)
    -- timeInfos 三维向量，时间跨度、执行次数、最短间隔。 随机生成时间点
    local timeSpan = timeInfos.X
    local playCount = timeInfos.Y
    local minInterval = timeInfos.Z
    local timeList = PlayRandomNiagara.getTimeline(0, timeSpan, minInterval, playCount, bEqualInterval)
    return timeList
end

function PlayRandomNiagara.getTimeline(startTime, endTime, minInterval, count, bEqualInterval)
    local result = {}
    --取整
    math.floor(count)
    if (bEqualInterval == true) then
        --等间隔算法
        for i = 1, count do
            local time = startTime + minInterval * (i - 1)
            if time - endTime >= 0.0001 then
                break
            end
            table.insert(result, time)
        end
    else
        --最简单的随机算法
        for i = 1, count do
            local lastTime = endTime - (count - i) * minInterval
            local time = KismetMathLibrary.RandomFloatInRange(startTime, lastTime)
            startTime = time + minInterval
            table.insert(result, time)
        end
    end
    return result
    --if (startTime > endTime)then
    --    return
    --end
    --if(type(result)~="table") or (#result >= count) then
    --    return
    --end
    --local time = KismetMathLibrary.RandomFloatInRange(startTime, endTime)
    --table.insert(result, time)
    --local rightOrLeft = KismetMathLibrary.RandomBoolWithWeight(0.50)
    --PlayRandomNiagara.getTimeline(startTime, time - minInterval, result, minInterval)
    --PlayRandomNiagara.getTimeline(time + minInterval, endTime, result, minInterval)
end




PlayNiagaraInSpellField = {} -- luacheck: ignore
function PlayNiagaraInSpellField.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (entity == nil) or (configData == nil) then
		return
	end
	local ownerID = staticBlackBoard.instigatorID
	local ownerEntity = Game.EntityManager:GetEntityByIntID(ownerID)
	if ownerEntity == nil then
		return
	end

	--查找法术场
	local spellFields = {}
	ownerEntity:FindSelfCreateUnit(LUC.LOGIC_UNIT_TYPE.SpellField, configData.SpellFieldID, nil, spellFields)
	
	if #spellFields == 0 then
		return
	end
	local spellField = spellFields[1]
	--计算与target的相对位置，并执行特效
	local selfLoc = M3D.Vec3()
	selfLoc:Pack(entity:GetPosition_P())
	local spellFieldLoc = M3D.Vec3()
	spellFieldLoc:Pack(spellField:GetPosition_P())
	local spellFieldRot = M3D.Rotator()
	spellFieldRot:Pack(spellField:GetRotation_P())
	local extraPos = M3D.Vec3()
	selfLoc:Sub(spellFieldLoc, extraPos)
	-- 计算extraPos沿Z轴旋转yaw角度后的坐标
	local yaw = -spellFieldRot.Yaw
	M3D.RotateVectorByYaw(extraPos, yaw)
	local niagaraSystemId = ASTPlayNiagara.PlayNiagara(spellField, configData, staticBlackBoard, spellField:uid())
	if niagaraSystemId == nil then
		return
	end
	Game.EffectManager:UpdateNiagaraVec3Param(niagaraSystemId, "Position", extraPos:Unpack())
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = niagaraSystemId
end

function PlayNiagaraInSpellField.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (entity == nil) or (configData == nil) then
		return
	end

	if not configData.bPlayAtLast then
		if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
			return
		end

		local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
		if (TEntity == nil) then
			return
		end

		if configData.FadeOut == 0 then
			-- 立即销毁
			TEntity:DestroyNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		else
			TEntity:DeactivateNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		end

		--TEntity:UnCacheNiagaraParamBefore(configData.AttachComponentName)
	elseif dynamicBlackBoard.PlayUntilEnd then
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = ASTPlayNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID)
	end

	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PlayLinkNiagara = {} -- luacheck: ignore
function PlayLinkNiagara.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (targetEID == nil) or (configData == nil) then
		return
	end
	local LinkAgent = PlayLinkNiagara.GetLinkAgent(configData, staticBlackBoard, targetEID)
	if LinkAgent == nil then
		return
	end	

	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = PlayLinkNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID, LinkAgent)
end

function PlayLinkNiagara.SetLinkNiagaraParams(entity, targetEID, linkAgent, configData, staticBlackBoard, NiagaraEffectParam)
	local type = configData.SKMeshTargetType
	local targetEntity = nil
	if type == LINK_NIAGARA_SKMESH_TYPE.SPAWNER then
		targetEntity = entity
	elseif type == LINK_NIAGARA_SKMESH_TYPE.TARGET then
		local TargetId = entity:uid()
		if targetEID == entity:uid() then
			-- 优先使用buff连线目标，否则使用锁定目标
			TargetId = staticBlackBoard.linkedTargetId or staticBlackBoard.lockTarget
		end
		if TargetId == nil or TargetId == 0 then
			TargetId = entity:uid()
		end
		targetEntity = Game.EntityManager:getEntity(TargetId)
	elseif type == LINK_NIAGARA_SKMESH_TYPE.ATTACH_PARENT then
		targetEntity = linkAgent
	end

	ASTPlayNiagara.FillSkeletalMeshUserVars(targetEntity, configData.NiagaraMeshParam, NiagaraEffectParam)
end

function PlayLinkNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID, LinkAgent)
	local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
	if (TEntity == nil) then
		return
	end
	-- todo wangxu31 EffectPriorityCulling 对于buff来说, 这里entity实际上是buff持有者, 而非buff来源的entity, 实际策划需求是来源entity
    -- 只有特定类型的角色、特定的特效类型才会参与特效裁剪, 因此没有ViewControlBaseComponent的情况下也可以播放特效
	local CharacterTypeForViewBudget = entity.GetCharacterTypeForViewBudget and entity:GetCharacterTypeForViewBudget() or nil
	local EffectPriority = staticBlackBoard.EffectPriority
	local ForceEffectPriority
	if configData.EffectPriority and configData.EffectPriority ~= ASTPlayNiagara.NiagaraEffectPriority.InheritOwner then
		EffectPriority = configData.EffectPriority
		if configData.EffectPriority == ASTPlayNiagara.NiagaraEffectPriority.Vital then
			ForceEffectPriority = ASTPlayNiagara.NiagaraEffectPriority.Vital
		end
	end
	local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
			CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL, TEntity.CharacterID, 
			EffectPriority, ForceEffectPriority)
	if not bCanPlayNiagara then
		return
	end
	local BoneName = configData.DockBone
	local NiagaraEffectParam = ASTPlayNiagara.AssembleInitData(configData, staticBlackBoard)
	NiagaraEffectParam.NiagaraBudgetToken = BudgetToken
	NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL
	NiagaraEffectParam.CustomNiagaraPriority = EffectPriority
	
	-- 设置LinkNiagara参数
	PlayLinkNiagara.SetLinkNiagaraParams(entity, targetEID, LinkAgent, configData, staticBlackBoard, NiagaraEffectParam)

	local Transform = NiagaraEffectParam.SpawnTrans
	ASTPlayNiagara.ConvertOffset(configData.PosOffset, Transform.Translation)

	if NiagaraEffectParam.bAbsoluteScale then
		ASTPlayNiagara.ConvertOffset(configData.ScaleOffset, Transform.Scale3D)
	end
	
	local bNeedCache = false
	local AttachComponentId = 0
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.AttachPointName = BoneName
	if configData.LinkAgentType == LINK_AGENT_TYPE.SPELL_FIELD_MESH then
		local mesh = LinkAgent:GetDynamicMesh(LinkAgent.AddMeshUniqueID)
		if mesh == nil then
			bNeedCache = true
		else
			AttachComponentId = mesh.meshCompID
			NiagaraEffectParam.AttachComponentId = AttachComponentId
			if AttachComponentId == 0 then
				bNeedCache = true
			end
		end
	else
		NiagaraEffectParam.AttachComponentName = configData.AttachComponentName
		if NiagaraEffectParam.AttachComponentName ~= "" and NiagaraEffectParam.AttachComponentName ~= nil then
			NiagaraEffectParam.NiagaraAttachComponentType = NIAGARA_ATTACH_COMPONENT_TYPE.FIND_ATTACH_COMPONENT_BY_COMPONENT_NAME
		else
			NiagaraEffectParam.NiagaraAttachComponentType = NIAGARA_ATTACH_COMPONENT_TYPE.FIND_ATTACH_COMPONENT_BY_SOCKET_NAME
		end

	end
	
	if bNeedCache then
		NiagaraEffectParam.CustomEffectID = Game.EffectManager:GenerateEffectId()
		-- 法术场等连线目标来缓存
		LinkAgent:CacheNiagaraParamBefore(NiagaraEffectParam, configData.AttachComponentName, true)
		return NiagaraEffectParam.CustomEffectID
	else
		if configData.LinkAgentType == LINK_AGENT_TYPE.GHOST and not LinkAgent.bInWorld then
			-- 还没进入场景的幽灵实体，等进入场景了自己去播放销毁特效
			NiagaraEffectParam.CustomEffectID = Game.EffectManager:GenerateEffectId()
			NiagaraEffectParam.TotalLifeMs = configData.Duration * 1000.0
			LinkAgent:AddStartNiagaraEffectParam(NiagaraEffectParam)
			return NiagaraEffectParam.CustomEffectID
		end
		return LinkAgent:PlayNiagaraEffect(NiagaraEffectParam)
	end
end

function PlayLinkNiagara.GetLinkAgent(configData, staticBlackBoard, targetEID)
	local LinkAgent = nil
	if configData.LinkAgentType == LINK_AGENT_TYPE.GHOST then
		local GhostUID = staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.LinkTarget]
		if (GhostUID == nil) then
			return
		end
		LinkAgent = Game.EntityManager:getEntity(GhostUID)
	elseif configData.LinkAgentType == LINK_AGENT_TYPE.SPELL_FIELD_MESH then
		local ownerID = staticBlackBoard.instigatorID --buff始作俑者的法术场
		local ownerEntity = Game.EntityManager:GetEntityByIntID(ownerID)
		if ownerEntity == nil then
			return
		end
		local spellFields = {}
		ownerEntity:FindSelfCreateUnit(LUC.LOGIC_UNIT_TYPE.SpellField, configData.SpellFieldID, 0, spellFields)
		if #spellFields == 0 then
			return
		end
		local spellField = spellFields[1]
		LinkAgent = spellField
	else
		LinkAgent = Game.EntityManager:GetEntityByIntID(targetEID)
	end
	
	return LinkAgent
end

function PlayLinkNiagara.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (targetEID == nil) or (configData == nil) then
		return
	end
	if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] == nil then
		return
	end
	if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
		return
	end
	local LinkAgent = PlayLinkNiagara.GetLinkAgent(configData, staticBlackBoard, targetEID)
	if LinkAgent then
		if configData.LinkAgentType == LINK_AGENT_TYPE.SPELL_FIELD_MESH then
			LinkAgent:UnCacheNiagaraParamBefore(configData.AttachComponentName)
		end
		if configData.FadeOut == 0 then
			-- 立即销毁
			LinkAgent:DestroyNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		else
			LinkAgent:DeactivateNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		end
	end
	
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

CreateBPEffect = {} -- luacheck: ignore
function CreateBPEffect.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	local targetEntity = Game.EntityManager:getEntity(targetEID)
	if not targetEntity then
		Log.WarningFormat("[CreateBPEffect.Execute] target entity not found in skill:%s", targetEID, staticBlackBoard.skillID)
		return
	end
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = targetEntity:AddBlueprint(configData.BPPath, configData.bNeedAttach,
		configData.AttachSocket, configData.Translation, configData.Rotation, configData.Scale3D, configData.bNeedGround, configData.BPName)
end

function CreateBPEffect.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	local targetEntity = Game.EntityManager:getEntity(targetEID)
	if not targetEntity then
		Log.WarningFormat("[CreateBPEffect.End] target entity not found in skill:%s", targetEID, staticBlackBoard.skillID)
		return
	end
	if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
		return
	end
	local uniqueID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
	if uniqueID == nil then
		return
	end
	targetEntity:RemoveBlueprint(uniqueID)
	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PlayNiagaraByBuffLayer = {} -- luacheck: ignore
function PlayNiagaraByBuffLayer.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (targetEID == nil) or (configData == nil) then
		return
	end
	if staticBlackBoard.buffID == nil then
		return
	end
	local targetEntity = Game.EntityManager:GetEntityByIntID(targetEID) or entity
	local niagara = PlayNiagaraByBuffLayer.GetNiagaraByBuffLayer(targetEntity, staticBlackBoard.buffID, staticBlackBoard.instigatorID, configData.NiagaraList)
	if niagara == nil then
		return
	end
	if not configData.bPlayAtLast then
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = ASTPlayNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID, nil, niagara)
	else
		dynamicBlackBoard.PlayUntilEnd = true
	end
end

function PlayNiagaraByBuffLayer.GetNiagaraByBuffLayer(entity, buffID, instigatorID, niagaraList)
	local buffLayer = entity:GetBuffLayerNew(buffID, instigatorID)
	Log.DebugFormat("PlayNiagaraByBuffLayer.GetNiagaraByBuffLayer buffID:%s, buffLayer:%s", buffID, buffLayer)
	if buffLayer == nil or buffLayer == 0 then
		return nil
	end
	local niagara = niagaraList[buffLayer] or niagaraList[#niagaraList]
	Log.DebugFormat("PlayNiagaraByBuffLayer.GetNiagaraByBuffLayer niagara:%s", niagara)
	return niagara
end

function PlayNiagaraByBuffLayer.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
	if (targetEID == nil) or (configData == nil) then
		return
	end
	if dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] == nil then
		return
	end
	if staticBlackBoard.buffID == nil then
		return
	end
	if not configData.bPlayAtLast then
		if configData.InterruptMode ~= nil and configData.InterruptMode == TASK_INTERRUPT_TYPE.NOT_INTERRUPT then
			return
		end

		local TEntity = Game.EntityManager:GetEntityByIntID(targetEID)
		if (TEntity == nil) then
			return
		end

		if configData.FadeOut == 0 then
			-- 立即销毁
			TEntity:DestroyNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		else
			TEntity:DeactivateNiagaraSystem(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
		end

		--TEntity:UnCacheNiagaraParamBefore(configData.AttachComponentName)
	elseif dynamicBlackBoard.PlayUntilEnd then
		local targetEntity = Game.EntityManager:GetEntityByIntID(targetEID) or entity
		local niagara = PlayNiagaraByBuffLayer.GetNiagaraByBuffLayer(targetEntity, staticBlackBoard.buffID, configData.NiagaraList)
		if niagara == nil then
			return
		end
		dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = ASTPlayNiagara.PlayNiagara(entity, configData, staticBlackBoard, targetEID, niagara)
	end

	dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayNiagara, ASTPlayNiagara, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.RandomNiagara, PlayRandomNiagaraInTable, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayDecal, PlayDecal, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayAudio, PlayAudio, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.AddGhost, AddGhost, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.ClientTimeline, ClientTimeline)
    EffectTaskFactory.Register(Enum.EffectTaskType.AddAttachment, AddAttachment, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.SpawnWaterWave, SpawnWaterWave, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayRandomNiagara, PlayRandomNiagara, TASK_STATE_DATA_TYPE.WITH_STATE)
	EffectTaskFactory.Register(Enum.EffectTaskType.PlayNiagaraInSpellField, PlayNiagaraInSpellField, TASK_STATE_DATA_TYPE.WITH_STATE)
	EffectTaskFactory.Register(Enum.EffectTaskType.PlayLinkNiagara, PlayLinkNiagara, TASK_STATE_DATA_TYPE.WITH_STATE)
	EffectTaskFactory.Register(Enum.EffectTaskType.CreateBPEffect, CreateBPEffect, TASK_STATE_DATA_TYPE.WITH_STATE)
	EffectTaskFactory.Register(Enum.EffectTaskType.PlayNiagaraByBuffLayer, PlayNiagaraByBuffLayer, TASK_STATE_DATA_TYPE.WITH_STATE)
end