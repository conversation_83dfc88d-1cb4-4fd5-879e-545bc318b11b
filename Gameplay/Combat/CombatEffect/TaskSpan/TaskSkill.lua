local Enum = Enum
local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local sharedConst = kg_require("Shared.Const")
local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE
local TASK_DYNAMIC_KEY_TYPE = sharedConst.TASK_DYNAMIC_KEY_TYPE


DisableSkill = {} -- luacheck: ignore
function DisableSkill.setDisableSkillTypesAndTags(entity, configData, disableReason)
    if configData.bDisableAllTypeSkill then
        entity:DisableAllSkillType(disableReason)
        return
    end

    if configData.SkillTypes then
        for _, skillType in ksbcipairs(configData.SkillTypes) do
            entity:AddDisableSkillType(skillType, disableReason)
        end
    end

    if configData.SkillTags then
        for _, skillTag in ksbcipairs(configData.SkillTags) do
            entity:AddDisableSkillTag(skillTag, disableReason)
        end
    end
end

function DisableSkill.setDisableSkillIDs(entity, configData, disableReason)
    if not configData.SkillIDs or not ksbcnext(configData.SkillIDs) then
        return
    end

    for _, skillID in ksbcipairs(configData.SkillIDs) do
        entity:AddDisableSkillID(skillID, disableReason)
    end
end

function DisableSkill.setDisableSkillIDWhiteList(entity, configData, disableReason)
    if not configData.WhiteListSkillIDs or not ksbcnext(configData.WhiteListSkillIDs) then
        return
    end

    for _, skillID in ksbcipairs(configData.WhiteListSkillIDs) do
        entity:AddDisableSkillIDWhiteList(skillID, disableReason)
    end
end

function DisableSkill.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local disableReason = ETE.EDisabledSkillReason.Temporary
    if staticBlackBoard.buffID then
        disableReason = ETE.EDisabledSkillReason.BUFF
    end

    DisableSkill.setDisableSkillTypesAndTags(entity, configData, disableReason)
    DisableSkill.setDisableSkillIDs(entity, configData, disableReason)
    DisableSkill.setDisableSkillIDWhiteList(entity, configData, disableReason)
    --清空技能缓存，避免出现Cache中有数据，Task刚结束就放了个之前缓存的技能
    if not Game.BSManager.bIsInEditor and entity == Game.me and Game.PlayerController.BattleSystemInputProcessor then
        Game.PlayerController.BattleSystemInputProcessor.InputCache:ClearCacheTick()
    end
end

function DisableSkill.delDisableSkillTypesAndTags(entity, configData, disableReason)
    if configData.bDisableAllTypeSkill then
        entity:EnableAllSkillType(disableReason)
        return
    end

    if configData.SkillTypes then
        for _, skillType in ksbcipairs(configData.SkillTypes) do
            entity:DelDisableSkillType(skillType, disableReason)
        end
    end

    if configData.SkillTags then
        for _, skillTag in ksbcipairs(configData.SkillTags) do
            entity:DelDisableSkillTag(skillTag, disableReason)
        end
    end
end

function DisableSkill.delDisableSkillIDs(entity, configData, disableReason)
    if not configData.SkillIDs or not ksbcnext(configData.SkillIDs) then
        return
    end

    for _, skillID in ksbcipairs(configData.SkillIDs) do
        entity:DelDisableSkillID(skillID, disableReason)
    end
end

function DisableSkill.delDisableSkillIDWhiteList(entity, configData, disableReason)
    if not configData.WhiteListSkillIDs or not ksbcnext(configData.WhiteListSkillIDs) then
        return
    end

    for _, skillID in ksbcipairs(configData.WhiteListSkillIDs) do
        entity:DelDisableSkillIDWhiteList(skillID, disableReason)
    end
end

function DisableSkill.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local disableReason = ETE.EDisabledSkillReason.Temporary
    if staticBlackBoard.buffID then
        disableReason = ETE.EDisabledSkillReason.BUFF
    end

    DisableSkill.delDisableSkillTypesAndTags(entity, configData, disableReason)
    DisableSkill.delDisableSkillIDs(entity, configData, disableReason)
    DisableSkill.delDisableSkillIDWhiteList(entity, configData, disableReason)
end


DisableBattleAbility = {} -- luacheck: ignore
function DisableBattleAbility.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

    -- 1.禁止移动相关逻辑
    local reason = ETE.EDisabledSkillReason.Temporary
    if (staticBlackBoard.buffID ~= nil) then
        reason = ETE.EDisabledSkillReason.BUFF
    end
    -- 禁用移动行为
    if (configData.bDisableMovement == true) then
        entity:DisableLocoMoveByLocoStart(Enum.ELocoControlTag.Skill, true, false)
        entity:DisableLocoMove(Enum.ELocoControlTag.Skill, true, false)
    end

    if (configData.bDisableRotation == true) then
        entity:DisableLocoRotate(Enum.ELocoControlTag.Skill, true, false)
    end
    if (configData.bDisableJump == true) then
        entity:DisableLocoJump(Enum.ELocoControlTag.Skill, true, false, reason)
    end
    if (configData.bDisableDodge == true) then
        entity:DisableLocoDodge(Enum.ELocoControlTag.Skill, true, false)
    end
    --清空案件缓存，避免出现玩家禁止移动时，缓存中还是冲刺/二段跳技能缓存
    if entity == Game.me and not Game.CombatDataManager.bIsInEditor then
        Game.PlayerController.BattleSystemInputProcessor.InputCache:ClearCacheTick()
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.DisableReason] = reason

    -- 2.禁止技能相关逻辑
    DisableSkill.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
end

function DisableBattleAbility.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

    -- 1.恢复禁止移动相关逻辑
    if (configData.bDisableMovement == true) then
        entity:DisableLocoMoveByLocoStart(Enum.ELocoControlTag.Skill, false, false)
        entity:DisableLocoMove(Enum.ELocoControlTag.Skill, false, false)
    end

    if (configData.bDisableRotation == true) then
        entity:DisableLocoRotate(Enum.ELocoControlTag.Skill, false, false)
    end
    if (configData.bDisableJump == true) then
        entity:DisableLocoJump(Enum.ELocoControlTag.Skill, false, false, dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.DisableReason])
    end
    if (configData.bDisableDodge == true) then
        entity:DisableLocoDodge(Enum.ELocoControlTag.Skill, false, false)
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.DisableReason] = nil

    -- 2.恢复禁止技能相关逻辑
    DisableSkill.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
end


SetTimeDilation = {} -- luacheck: ignore
function SetTimeDilation.Execute(Entity, ConfigData, StaticBlackBoard, targetEID, targetEIDList)
    if ConfigData == nil then
        return
    end
    UE.GameplayStatics.SetGlobalTimeDilation(GetContextObject(), ConfigData.DilationValue)
end

function SetTimeDilation.End(entity, configData, staticBlackBoard, targetEID, targetEIDList)
    UE.GameplayStatics.SetGlobalTimeDilation(GetContextObject(), 1)
end

function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.DisableSkill, DisableSkill)
    EffectTaskFactory.Register(Enum.EffectTaskType.DisableBattleAbility, DisableBattleAbility, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.SetTimeDilation, SetTimeDilation)
end