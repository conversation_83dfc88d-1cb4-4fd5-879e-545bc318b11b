local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local Enum = Enum
local sharedConst = kg_require("Shared.Const")
local PostProcessConst = kg_require("Gameplay.Effect.PostProcessConst")
local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE
local TASK_DYNAMIC_KEY_TYPE = sharedConst.TASK_DYNAMIC_KEY_TYPE

--------------------------------------------------------
--- 持续类Task，需要实现Excute和End
--------------------------------------------------------
PP_Grayscale = {}  -- luacheck: ignore
function PP_Grayscale.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = Game.PostProcessManager:EnableColorAdjust(Enum.EPostProcessLayers.Skill, configData.FadeIn, configData.FadeOut,nil,
            5, configData.Contrast,nil, configData.Desaturate,nil)
end

function PP_Grayscale.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    Game.PostProcessManager:RemovePP(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PP_InvertColor = {}  -- luacheck: ignore
function PP_InvertColor.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = Game.PostProcessManager:EnableColorAdjust(Enum.EPostProcessLayers.Skill, configData.FadeIn, configData.FadeOut,nil,
            5, nil,nil, nil,nil, configData.InvertColorR,nil, configData.InvertColorG,nil, configData.InvertColorB,nil)
end

function PP_InvertColor.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    Game.PostProcessManager:RemovePP(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PP_Vignette= {}  -- luacheck: ignore
function PP_Vignette.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    local Center = FLinearColor(configData.Center.X, configData.Center.Y, configData.Center.X, configData.Center.Y)
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = Game.PostProcessManager:EnableVignette(Enum.EPostProcessLayers.Skill, configData.FadeIn, configData.FadeOut,nil,
            5, configData.Intensity,nil,nil,nil, configData.Radius,nil, configData.Softness,nil,Center,nil)
end

function PP_Vignette.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    Game.PostProcessManager:RemovePP(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PP_ColorFringing = {}  -- luacheck: ignore
function PP_ColorFringing.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = Game.PostProcessManager:EnableChromaticAberration(Enum.EPostProcessLayers.Skill, configData.FadeIn, configData.FadeOut,nil,
            5, configData.Intensity, configData.StartOffset)
end

function PP_ColorFringing.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end
    Game.PostProcessManager:RemovePP(dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID])
    dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
end

PP_SetInteractiveFog = {}  -- luacheck: ignore
function PP_SetInteractiveFog.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

    -- 仅buff持有者为主角时生效
    if entity ~= Game.me then
        return
    end

    local color = {}
    for i, v in ksbcipairs(configData.FogColor) do
        color[i] = v / 255.0 -- 转换为0-1范围
    end
    local FogOpacity = configData.FogOpacity ~= nil and configData.FogOpacity or 1
    
    Game.PostProcessManager:EnableOrUpdateFog(
            Enum.EPostProcessLayers.World, 
            configData.BlendTime ~= nil and configData.BlendTime or 1, 
            nil,
            FogOpacity,
            configData.MaxDist ~= nil and (configData.MaxDist / 100) or 2,
            color, 
            configData.SmoothDist ~= nil and (configData.SmoothDist / 100) or 2,
            configData.HeadInfoHideDist ~= nil and (configData.HeadInfoHideDist / 100) or 2,
            configData.bOverlayClimate == nil and false or (not configData.bOverlayClimate))
end

function PP_SetInteractiveFog.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    if (entity == nil) or (configData == nil) then
        return
    end

    if entity ~= Game.me then
        return
    end

    if configData.bStopFogOnEnd == false then
        return
    end

    Game.PostProcessManager:RemoveFog(configData.BlendTime or 1)
end

PlayPostProcess = {}  -- luacheck: ignore
function PlayPostProcess.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local postProcessParams = configData.PostProcessParams
    if postProcessParams.bUsePostProcessLibrary then
        local newSpeed = configData.Duration / PostProcessConst.PP_TEMPLATE_LIBRARY_DURATION_SECONDS
        local libraryParams = postProcessParams.LibraryParams
        dynamicBlackBoard.CustomPostProcessPriority = libraryParams.Priority
        dynamicBlackBoard.ForceTimeScale = newSpeed
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = entity:StartClientTimeline(libraryParams.TemplateID, dynamicBlackBoard, nil, newSpeed)
    else
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = Game.NewPostProcessManager:StartPPEffect(
                configData.PostProcessParams.RawParams, configData.Duration, entity, staticBlackBoard.CustomPostProcessPriority,
                staticBlackBoard.ForceTimeScale or 1.0)
    end
end

function PlayPostProcess.End(entity, configData, staticBlackBoard, targetEID, targetEIDList, dynamicBlackBoard)
    local ReqID = dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID]
    if ReqID ~= nil then
        local postProcessParams = configData.PostProcessParams
        if postProcessParams.bUsePostProcessLibrary then
            entity:StopClientTimeline(ReqID)
        else
            Game.NewPostProcessManager:StopPPEffect(configData.PostProcessParams.RawParams, ReqID)
        end
        dynamicBlackBoard[TASK_DYNAMIC_KEY_TYPE.HandleID] = nil
    end
end

function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.PP_Grayscale, PP_Grayscale, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PP_InvertColor, PP_InvertColor, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PP_Vignette, PP_Vignette, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PP_ColorFringing, PP_ColorFringing, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PP_SetInteractiveFog, PP_SetInteractiveFog, TASK_STATE_DATA_TYPE.WITH_STATE)
    EffectTaskFactory.Register(Enum.EffectTaskType.PlayPostProcess, PlayPostProcess, TASK_STATE_DATA_TYPE.WITH_STATE)
end