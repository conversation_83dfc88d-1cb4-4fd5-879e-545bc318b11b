
function PlayDecalCheck(TaskData)
	--return "错误测试"
end

TaskCheckDict = {}
TaskCheckDict[Enum.EffectTaskType.PlayDecal] = PlayDecalCheck

function SkillDataCheck(skillID, SkillDataErrors)
	local skillData = Game.CombatDataManager:GetAbilityDT(skillID, true)
	if skillData then
		for stateID, stateData in pairs(skillData.StateMap) do
			for TaskID, TaskData in pairs(stateData.TaskMap) do
				local checkFunc = TaskCheckDict[TaskData.TaskType]
				if checkFunc then
					local errorStr = checkFunc(TaskData)
					if errorStr then
						SkillDataErrors:Add(string.format("SkillID: %s, StateID:%s, TaskID:%s, Config Error: %s", skillID, stateID, TaskID, errorStr))
					end
				end
			end
		end
	end
end