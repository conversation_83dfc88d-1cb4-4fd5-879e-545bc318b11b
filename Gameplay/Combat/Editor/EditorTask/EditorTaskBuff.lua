local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local AbilityConst = kg_require("Shared.Const.AbilityConst")

local EBuffLineLinkMode = AbilityConst.EBuffLineLinkMode

AddBuffNew = {}

function AddBuffNew.TimerRemoveBuff(targetEID, buffID, instigatorID)
    Log.DebugFormat("Remove Buff:%s,instigatorID:%s", buffID, instigatorID)

    local target = Game.EntityManager:GetEntityByIntID(targetEID)
    if target then
        target:OnMsgRemoveBuffNew(buffID, instigatorID)
    else
        Log.WarningFormat("Remove Buff:%s,instigatorID:%s, but targetEID: %s not exist", buffID, instigatorID, targetEID)
    end
end

function AddBuffNew.Execute(entity, configData, staticBlackBoard, targetEID, targetEIDList)
    local buffID = configData.ID
    local level = configData.Level
    -- 后面level要改成string(支持数值或者等级表达式，先兼容下)
    level = tonumber(level) or 0
    if level <= 0 then
        level = staticBlackBoard.level or 1
    end

    local layer = configData.Layer
    local timeLife = configData.LifeTime or 0

    local NowTime = _G._now()
    local buffTableData = Game.TableData.GetBuffDataNewRow(buffID)
    if timeLife == 0 then
        timeLife = buffTableData.Duration
    end
    local maxLayer = buffTableData.MaxLayers
    if targetEID then
        local target = Game.EntityManager:GetEntityByIntID(targetEID)
        target:OnMsgAddBuffNew(
            buffID,
            staticBlackBoard.instigatorID or entity:uid(),
            staticBlackBoard.triggerID or entity:uid(),
            level,
            layer,
            maxLayer,
            timeLife,
            NowTime
        )

        Game.TimerManager:CreateTimerAndStart(function()
            AddBuffNew.TimerRemoveBuff(targetEID, buffID, staticBlackBoard.instigatorID or entity:uid())
        end, timeLife * 1000, 1)
        Log.DebugFormat("Add Buff:%s,Level:%s,Layer:%s", buffID, level, layer)
    elseif targetEIDList then
        for _, eid in pairs(targetEIDList) do
            local target = Game.EntityManager:GetEntityByIntID(eid)
            target:OnMsgAddBuffNew(
                buffID,
                staticBlackBoard.instigatorID or entity:uid(),
                staticBlackBoard.triggerID or entity:uid(),
                level,
                layer,
                maxLayer,
                timeLife,
                NowTime
            )

            Game.TimerManager:CreateTimerAndStart(function()
                AddBuffNew.TimerRemoveBuff(eid, buffID, staticBlackBoard.instigatorID or entity:uid())
            end, timeLife * 1000, 1)
            Log.DebugFormat("Add Buff:%s,Level:%s,Layer:%s", buffID, level, layer)
        end
    end
end


AddLineBuff = {}
function AddLineBuff.AddLineBuffToTarget(entity, configData, staticBlackBoard, targetEntity, linkedTarget)
    if not targetEntity then
        return
    end

    local buffID = configData.BuffID
    local buffTableData = Game.TableData.GetBuffDataNewRow(buffID)
    if buffTableData == nil then
        Log.DebugFormat("[AddLineBuff] Entity: %s, buffId: %s invalid", entity:uid(), buffID)
        return
    end

    local timeLife = configData.Time
    local buffLayer = configData.Layer or 1
    if timeLife == 0 then
        timeLife = buffTableData.Duration
    end
    local maxLayer = buffTableData.MaxLayers
    local buffLevel = configData.Level

    -- 后面level要改成string(支持数值或者等级表达式，先兼容下)
    buffLevel = tonumber(buffLevel) or 0
    if buffLevel <= 0 then
        buffLevel = staticBlackBoard.level or 1
    end

    local nowTime = _G._now()
    targetEntity:OnMsgAddBuffNew(
        buffID,
        staticBlackBoard.instigatorID or entity:uid(),
        staticBlackBoard.triggerID or entity:uid(),
        buffLevel,
        buffLayer,
        maxLayer,
        timeLife,
        nowTime,
        linkedTarget:uid())

    Log.DebugFormat("[AddLineBuff] Entity: %s, TargetId: %s, BuffId:%s, Level:%s, Layer: %s, Time: %s, linkedTargetId: %s",
        entity:uid(), targetEntity:uid(), buffID, buffLevel, buffLayer, buffTime, linkedTarget:uid())
end

function AddLineBuff.AddLineBuffByOne2MultiMode(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    -- target link to the entity
    if targetEID ~= nil then
        local target = Game.EntityManager:GetEntityByIntID(targetEID)
        if target == nil then
            return
        end
        AddLineBuff.AddLineBuffToTarget(entity, configData, staticBlackBoard, target, entity)
    elseif taskEIDList ~= nil then
        local targetCount = math.min(configData.TargetCount, #taskEIDList)
        for idx = 1, targetCount do
            local targetEId = taskEIDList[idx]
            local target = Game.EntityManager:GetEntityByIntID(targetEId)
            if target then
                AddLineBuff.AddLineBuffToTarget(entity, configData, staticBlackBoard, target, entity)
            end
        end
    end
end

function AddLineBuff.AddLineBuffByPairWiseMode(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    if taskEIDList == nil then
        Log.DebugFormat("AddLineBuffByPairWiseMode, entity: %s, taskEIDList is nil, configData[%v]",
            entity:uid(), CommonUtils.tprint(configData))
        return
    end

    local targetCount = configData.TargetCount
    if targetCount % 2 ~= 0 then
        Log.DebugFormat("AddLineBuffByPairWiseMode, entity: %s, targetCount: %s invalid, configData[%v]",
            entity:uid(), targetCount, CommonUtils.tprint(configData))
        return
    end

    local targetListLen = #taskEIDList
    local neareastEvenNum = math.floor(targetListLen / 2) * 2
    local finalTargetCount = math.min(targetCount, neareastEvenNum)

    for idx = 1, finalTargetCount do
        if idx % 2 == 0 then
            local targetId = taskEIDList[idx]
            local target = Game.EntityManager:GetEntityByIntID(targetId)
            if target == nil then
                Log.DebugFormat("AddLineBuffByPairWiseMode, entityId: %s, targetId: %s not exist, configData[%v]",
                    entity:uid(), targetId, CommonUtils.tprint(configData))
                goto continue
            end

            local linkTargetId = taskEIDList[idx - 1]
            local linkTarget = Game.EntityManager:GetEntityByIntID(linkTargetId)
            if linkTarget == nil then
                Log.DebugFormat("AddLineBuffByPairWiseMode, entityId: %s, targetId: %s's linkTargetId: %s not exist, configData[%v]",
                    entity:uid(), targetId, linkTargetId, CommonUtils.tprint(configData))
                goto continue
            end

            AddLineBuff.AddLineBuffToTarget(entity, configData, staticBlackBoard, target, linkTarget)

            :: continue ::
        end
    end
end

function AddLineBuff.Execute(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    local lineMode = configData.LineMode
    if lineMode == EBuffLineLinkMode.One2Multi then
        AddLineBuff.AddLineBuffByOne2MultiMode(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    elseif lineMode == EBuffLineLinkMode.PairWise then
        AddLineBuff.AddLineBuffByPairWiseMode(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    else
        Log.DebugFormat("AddLineBuff.Execute, Entity: %s, lineMode: %s invalid, configData[%v]",
            entity:uid(), lineMode, CommonUtils.tprint(configData))
    end
end

DelBuff = {}
function DelBuff.Execute(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    local buffID = configData.ByID
    local buffLayer = configData.Layer or 0
    --local tags = configData.Tags
    --local nums = configData.TagsRandomNums
    if targetEID then
        local target = Game.EntityManager:GetEntityByIntID(targetEID)
        if buffLayer <= 0 then
            local buffInstList = target.buffInstListNew[buffID]
            if buffInstList then
                for instigatorID, _ in pairs(buffInstList) do
                    target:OnMsgRemoveBuffNew(buffID, instigatorID)
                    Log.DebugFormat("Remove Buff:%s,instigatorID:%s", buffID, instigatorID)
                end
            end
        else
            local buffInstList = target.buffInstListNew[buffID]
            if buffInstList then
                for instigatorID, buffInst in pairs(buffInstList) do
                    if buffInst.layer <= buffLayer then
                        target:OnMsgRemoveBuffNew(buffID, instigatorID)
                        Log.DebugFormat("Remove Buff:%s,instigatorID:%s", buffID, instigatorID)
                    else
                        Log.DebugFormat("RemoveLayer Buff:%s,instigatorID:%s,OldLayer:%s,NewLayer:%s", buffID,
                            instigatorID, buffInst.layer,
                            buffInst.layer - buffLayer)
                        target:OnMsgBuffTotalLifeChangeNew(buffID, instigatorID, buffInst.layer,
                            buffInst.layer - buffLayer, buffInst.totalStartTimeStamp)
                    end
                end
            end
        end
    end
end

KeepBuff = {}
function KeepBuff.Execute(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    local buffID = configData.ID
    local layer = configData.Layer
    local level = configData.Level
    -- 后面level要改成string(支持数值或者等级表达式，先兼容下)
    level = tonumber(level) or 0
    if level <= 0 then
        level = staticBlackBoard.level or 1
    end

    local buffTableData = Game.TableData.GetBuffDataNewRow(buffID)
    local maxLayer = buffTableData.MaxLayers
    local timeLife = -1.0                    -- 时间直接用本task的时长
    if configData.Duration ~= nil then
        timeLife = configData.Duration + 2.0 -- 不确定task end和buff失效哪个会先执行到，所以额外加2s delay确保task end 先与buff失效 触发
    end
    local NowTime = _G._now()
    if targetEID then
        local target = Game.EntityManager:GetEntityByIntID(targetEID)
        target:OnMsgAddBuffNew(
            buffID,
            staticBlackBoard.instigatorID or entity:uid(),
            staticBlackBoard.triggerID or entity:uid(),
            level,
            layer,
            maxLayer,
            timeLife,
            NowTime
        )

        Log.DebugFormat("[KeepBuff] Add Buff:%s,Level:%s,Layer:%s", buffID, level, layer)
    end
end

function KeepBuff.End(entity, configData, staticBlackBoard, targetEID, taskEIDList, dynamicBlackBoard)
    local buffID = configData.ID
    local layer = configData.Layer

    if targetEID then
        local target = Game.EntityManager:GetEntityByIntID(targetEID)
        if layer <= 0 then
            local buffInstList = target.buffInstListNew[buffID]
            if buffInstList then
                for instigatorID, _ in pairs(buffInstList) do
                    target:OnMsgRemoveBuffNew(buffID, instigatorID)
                    Log.DebugFormat("[KeepBuff] Remove Buff:%s,instigatorID:%s", buffID, instigatorID)
                end
            end
        else
            local buffInstList = target.buffInstListNew[buffID]
            if buffInstList then
                for instigatorID, buffInst in pairs(buffInstList) do
                    if buffInst.layer <= layer then
                        target:OnMsgRemoveBuffNew(buffID, instigatorID)
                        Log.DebugFormat("[KeepBuff] Remove Buff:%s,instigatorID:%s", buffID, instigatorID)
                    else
                        Log.DebugFormat("[KeepBuff] RemoveLayer Buff:%s,instigatorID:%s,OldLayer:%s,NewLayer:%s", buffID,
                            instigatorID, buffInst.layer,
                            buffInst.layer - layer)
                        target:OnMsgBuffTotalLifeChangeNew(buffID, instigatorID, buffInst.layer,
                            buffInst.layer - layer, buffInst.totalStartTimeStamp)
                    end
                end
            end
        end
    end
end

function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.AddBuff, AddBuffNew)
    EffectTaskFactory.Register(Enum.EffectTaskType.AddLineBuff, AddLineBuff)
    EffectTaskFactory.Register(Enum.EffectTaskType.DelBuff, DelBuff)
    EffectTaskFactory.Register(Enum.EffectTaskType.KeepBuff, KeepBuff)
end
