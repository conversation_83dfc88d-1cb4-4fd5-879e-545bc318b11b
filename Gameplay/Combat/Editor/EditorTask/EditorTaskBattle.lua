local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
local TargetSelectorEx = kg_require("Shared.Combat.Selector.TargetSelectorEx")
local AbilityConst = kg_require("Shared.Const.AbilityConst")
local sharedConst = kg_require("Shared.Const")

local UKSLib = import("KismetSystemLibrary")

local Enum = Enum

local STATIC_BLACK_BOARD_KEY_TYPE = sharedConst.STATIC_BLACK_BOARD_KEY_TYPE

--------------------------------------------------------
--- 瞬发类Task，需要实现Execute，只供预览使用，模拟服务器实现调用客户端rpc接口
--------------------------------------------------------

---------------------------------- search pos -------------------------------------------------------
SHOW_SEARCH_POS_DURATION = 3.0
SEARCH_POS_COLOR = FLinearColor.Red
SEARCH_POS_DRAW_THICKNESS = 2.0

function ShowSearchedPos(pos)
    local showRadius = 30
    local showPos = FVector(pos.X, pos.Y, pos.Z)
    UKSLib.DrawDebugSphere(
        _G.GetContextObject(),
        showPos,
        showRadius,
        30,
        SEARCH_POS_COLOR,
        SHOW_SEARCH_POS_DURATION,
        SEARCH_POS_DRAW_THICKNESS)
end

---------------------------------- search target -------------------------------------------------------
EditorSearchTarget = {}
function EditorSearchTarget.Execute(entity, configData, staticBlackBoard)
    Log.DebugFormat("----> SearchTarget.Execute, Entity: %s, ConfigData: {%s}",
        entity.eid, CommonUtils.tprint(configData))

    local targetIdList = nil
    targetIdList = TargetSelectorEx.QueryEntitiesByRule(
        entity, configData.TargetSelectionRuleID, nil, staticBlackBoard)


    Log.DebugFormat("----> SearchTarget.Execute, Entity: %s, Result:{%s}",
        entity.eid, CommonUtils.tprint(targetIdList))

    if targetIdList ~= nil and next(targetIdList) ~= nil then
        local targetIdListCp = {}
        for idx, entId in ipairs(targetIdList) do
            targetIdListCp[idx] = entId
        end
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = targetIdListCp
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = #targetIdListCp
    else
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = nil
    end
end

---------------------------------------------- search target and pos --------------------------------------------
EditorSearchTargetPos = EditorSearchTargetPos or {}
function EditorSearchTargetPos.Execute(entity, configData, staticBlackBoard)
    Log.DebugFormat("----> EditorSearchTargetPos.Execute, Entity: %s, ConfigData: {%s}",
        entity.eid, CommonUtils.tprint(configData))

    local targetIDList = TargetSelectorEx.QueryEntitiesByRule(
        entity, configData.TargetSelectionRuleID, nil, staticBlackBoard)

    Log.DebugFormat("----> EditorSearchTargetPos.Execute, Entity: %s, Result:{%s}",
        entity.eid, CommonUtils.tprint(targetIDList))

    if (targetIDList ~= nil) and (next(targetIDList) ~= nil) then
        local targetIDListCopy = {}
        local outPosList = {}
        for _, targetID in ipairs(targetIDList) do
            local target = Game.EntityManager:GetEntityByIntID(targetID)
            if not target then
                goto continue
            end

            table.insert(targetIDListCopy, targetID)
            table.insert(outPosList, target:GetPosition())

            :: continue ::
        end

        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = targetIDListCopy
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = #targetIDListCopy
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetNotEntity] = false
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchPosList] = outPosList
    else
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetList] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetListLength] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchTargetNotEntity] = nil
        staticBlackBoard[STATIC_BLACK_BOARD_KEY_TYPE.searchPosList] = nil
    end
end


---------------------------------------------- SearchSelfCreatedEntityPos ---------------------------------------
EditorSearchSelfCreatedEntityPos = {}
EditorSearchSelfCreatedEntityPos.__SelfUnitList = {}
function EditorSearchSelfCreatedEntityPos.Execute(entity, configData, staticBlackBoard)
    Log.DebugFormat("----> EditorSearchSelfCreatedEntityPos.Execute, Entity: %s, ConfigData: {%s}",
        entity.eid, CommonUtils.tprint(configData))

    local outUnitList = EditorSearchSelfCreatedEntityPos.__SelfUnitList
    table.clear(outUnitList)

    if entity.FindSelfCreateUnit then
        outUnitList = entity:FindSelfCreateUnit(configData.Type,
            configData.ID, configData.TimeOrder, outUnitList)
    end

    if outUnitList and next(outUnitList) then
        local outPosList = {}
        for _, unit in ipairs(outUnitList) do
            table.insert(outPosList, unit:GetPosition())
        end

        staticBlackBoard.searchPosList = outPosList
    else
        staticBlackBoard.searchPosList = nil
    end

    Log.DebugFormat("----> EditorSearchSelfCreatedEntityPos.Execute, Entity: %s, Result{%s}",
        entity.eid, CommonUtils.tprint(staticBlackBoard.searchPosList))

    -- clear(not hold the entities)
    table.clear(outUnitList)
end

-------------------------------------------- EditorSearchTargetCreatedEntityPos --------------------------------------
EditorSearchTargetCreatedEntityPos = {}
EditorSearchTargetCreatedEntityPos.__TargetUnitList = {}
function EditorSearchTargetCreatedEntityPos.Execute(entity, configData, staticBlackBoard, targetEID, taskEIDList)
    Log.DebugFormat("----> EditorSearchTargetCreatedEntityPos.Execute, Entity: %s, targetEID: %s, ConfigData: {%s}",
            entity.eid, targetEID, CommonUtils.tprint(configData))

    local target = Game.EntityManager:GetEntityByIntID(targetEID)
    if target == nil then
        Log.DebugFormat("----> EditorSearchTargetCreatedEntityPos.Execute, Entity: %s, targetEID: %s not exist, ConfigData: {%s}",
            entity.eid, targetEID, CommonUtils.tprint(configData))
        return
    end

    local outUnitList = EditorSearchTargetCreatedEntityPos.__TargetUnitList
    table.clear(outUnitList)

    if target.FindSelfCreateUnit then
        outUnitList = target:FindSelfCreateUnit(configData.Type,
                configData.ID, configData.TimeOrder, outUnitList)
    end

    if outUnitList and next(outUnitList) then
        local outPosList = {}
        for _, unit in ipairs(outUnitList) do
            table.insert(outPosList, unit:GetPosition())
        end

        staticBlackBoard.searchPosList = outPosList
    else
        staticBlackBoard.searchPosList = nil
    end

    Log.DebugFormat("----> EditorSearchTargetCreatedEntityPos.Execute, Entity: %s, targetEID: %s, Result{%s}",
            entity.eid, targetEID, CommonUtils.tprint(staticBlackBoard.searchPosList))

    -- clear(not hold the entities)
    table.clear(outUnitList)
end

------------------------------------------------------------------------------------------------------------------

function RegisterTask()
    EffectTaskFactory.Register(Enum.EffectTaskType.SearchTarget, EditorSearchTarget)
    EffectTaskFactory.Register(Enum.EffectTaskType.SearchTargetPos, EditorSearchTargetPos)
    EffectTaskFactory.Register(Enum.EffectTaskType.SearchMyEntityPos, EditorSearchSelfCreatedEntityPos)
    EffectTaskFactory.Register(Enum.EffectTaskType.SearchTargetEntityPos, EditorSearchTargetCreatedEntityPos)
end
