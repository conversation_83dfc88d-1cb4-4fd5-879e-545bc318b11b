-- TODO: 所有逻辑全部放到 Shared 下

local AbilityConst = kg_require("Shared.Const.AbilityConst")
-- local SharedConst = kg_require("Shared.Const")
-- local TargetSelectorEx = kg_require("Shared.Combat.Selector.TargetSelectorEx")
-- local lume = kg_require("Shared.lualibs.lume")
local PositionSelectorUtils = kg_require("Shared.Utils.PositionSelectorUtils")
local UBSFunc = import("BSFunctionLibrary")
local SelectU = kg_require("Gameplay.Combat.Selector.SelectionUtils")

local EPositionSelectShapeType = AbilityConst.EPositionSelectShapeType
local EPositionSelectorRandomType = AbilityConst.EPositionSelectorRandomType
local EBaseCoordinatePointType = AbilityConst.EBaseCoordinatePointType

---@class FVector
---@field X number
---@field Y number
---@field Z number

---@brief 位置筛选参数结构（编辑器和导表后处理
---@class PosSelectRuleData
---@field BasePointType         integer;      基准坐标系类型枚举（EBaseCoordinatePointType）
---@field BasePointArgs         FVector;      位置偏移量/世界坐标/基准点最大选点数量
---@field ClockwiseRotation     number;       朝向偏移量
---@field ShapeType             integer;      筛选范围形状类型枚举(EPositionSelectShapeType)         
---@field ShapeArgs             number[];     筛选范围参数(圆形:半径； 扇形:半径,圆心角; 扇环:半径,内圈半径,圆心角)
---@field PosNum                integer;      筛选位置数量

__CenterPosList = {} -- luacheck: ignore
__CenterDirList = {} -- luacheck: ignore
__CenterPos = M3D.Vec3()
function getRandPositionByRuleData(BasedActor, TargetActor, RuleData, StaticBlackboard, PosRes)
    if BasedActor == nil then
        Log.ErrorFormat("getRandPositionByRuleData, invalid BasedActor, skillID or BuffID:%s",
            StaticBlackboard.skillID or StaticBlackboard.buffID)
        return
    end
    
    local basePointType = RuleData.BasePointType
    local basePointArgs = RuleData.BasePointArgs
    local clockwiseRotation = RuleData.ClockwiseRotation
    local posNum = RuleData.PosNum
    local originPosList, directionList = __CenterPosList, __CenterDirList
    table.clear(originPosList)
    table.clear(directionList)

    if basePointType == EBaseCoordinatePointType.TargetList or basePointType == EBaseCoordinatePointType.PosList then
        local basePointNum = basePointArgs.X or 1
        if basePointNum == -1 then
            local listNum
            if basePointType == EBaseCoordinatePointType.PosList then
                local posList = StaticBlackboard and StaticBlackboard.searchPosList
                listNum = posList and #posList or 0
            else
                local tarList = StaticBlackboard and StaticBlackboard.searchTargetList
                listNum = tarList and #tarList or 0
            end
            basePointNum = listNum or 0
        end

        for index = 1, basePointNum do
            local originPos = M3D.Vec3()
            originPosList[index], directionList[index] = SelectU.GetPosAndYawByCoordinatePointType(BasedActor, TargetActor,
                StaticBlackboard, basePointType, nil, clockwiseRotation, originPos, index)
            if not originPosList[index] then
                LOG_WARN_FMT("getRandPositionByRuleData, invalid originPos, index:%s", index)
                break
            end
        end
    else
        local originPos = __CenterPos
        originPosList[1], directionList[1] = SelectU.GetPosAndYawByCoordinatePointType(BasedActor, TargetActor,
            StaticBlackboard, basePointType, basePointArgs, clockwiseRotation, originPos)
    end

    local rangeType = EPositionSelectShapeType[RuleData.ShapeType] or RuleData.ShapeType       --- 范围类型
    local rangeArgs = RuleData.ShapeArgs       --- 范围参数

    local result = PosRes or {}
    local rangeFunc
    if rangeType == EPositionSelectShapeType.circle then
        rangeFunc = getRandPositionInCircle
    elseif rangeType == EPositionSelectShapeType.fan then
        rangeFunc = getRandPositionInSector
    elseif rangeType == EPositionSelectShapeType.ringcut then
        rangeFunc = getRandPositionInAnnularSector
    elseif rangeType == EPositionSelectShapeType.none then
        result[1] = originPosList[1]
        return result
    else
        Log.WarningFormat("getRandPositionByRuleData, RangeType: %s invalid, skillID or BuffID:%s",
            rangeType, StaticBlackboard.skillID or StaticBlackboard.buffID)
        return
    end

    for i, pos in pairs(originPosList) do
        local dir = directionList[i]
        if pos and dir then
            -- 执行选取随机点
            local singleResult = nil
            singleResult = rangeFunc(posNum, 0, pos, dir, rangeArgs)
            table.move(singleResult, 1, #singleResult, #result + 1, result) --- TODO: 优化 #result
        end
    end

    return result
end

---@param RandomNumber int
---@param OriginPos TVector3
---@param Yaw number 角度
---@param RangeArgs table
function getRandPositionInCircle(RandomNumber, RandomType, OriginPos, Yaw, RangeArgs)
    local radius = RangeArgs[1] --- 由后处理保证一定有值
    Yaw = Yaw / 180 * math.pi

    if RandomType == EPositionSelectorRandomType.Random then
        return PositionSelectorUtils.GenRandPositionInAnnularSector(RandomNumber, 0, radius, 2 * math.pi, OriginPos, Yaw)
    elseif RandomType == EPositionSelectorRandomType.UniformRandom then
        return PositionSelectorUtils.GenUniformRandPosInAnnulusSector(RandomNumber, 0, radius, 2 * math.pi, OriginPos, Yaw)
    end 
end

---@param RandomNumber int
---@param OriginPos TVector3
---@param Yaw number 角度
---@param RangeArgs table
function getRandPositionInSector(RandomNumber, RandomType, OriginPos, Yaw, RangeArgs)
    local radius = RangeArgs[1] --- 由后处理保证一定有值
    local centralAngle = RangeArgs[2] / 180.0 * math.pi
    Yaw = Yaw / 180 * math.pi

    if RandomType == EPositionSelectorRandomType.Random then
        return PositionSelectorUtils.GenRandPositionInAnnularSector(RandomNumber, 0, radius, centralAngle, OriginPos, Yaw)
    elseif RandomType == EPositionSelectorRandomType.UniformRandom then
        return PositionSelectorUtils.GenUniformRandPosInAnnulusSector(RandomNumber, 0, radius, centralAngle, OriginPos, Yaw)
    end 
end

---@param RandomNumber int
---@param OriginPos TVector3
---@param Yaw number 角度
---@param RangeArgs table
function getRandPositionInAnnularSector(RandomNumber, RandomType, OriginPos, Yaw, RangeArgs)
    local radius = RangeArgs[1] --- 由后处理保证一定有值
    local innerRadius = RangeArgs[2]
    local centralAngle = RangeArgs[3] / 180.0 * math.pi
    Yaw = Yaw / 180 * math.pi

    if RandomType == EPositionSelectorRandomType.Random then
        return PositionSelectorUtils.GenRandPositionInAnnularSector(RandomNumber, innerRadius, radius, centralAngle, OriginPos, Yaw)
    elseif RandomType == EPositionSelectorRandomType.UniformRandom then
        return PositionSelectorUtils.GenUniformRandPosInAnnulusSector(RandomNumber, innerRadius, radius, centralAngle, OriginPos, Yaw)
    end
end

---@brief 得到位置筛选的点的地板高度
---@param BasedActor ActorBase 位置筛选的 actor
---@param Position TVector3 筛选出的位置
---@return number
function getPositionFloor(BasedActor, Position)
    local _, _, _, z = UBSFunc.FindGroundLocation_P(BasedActor.CharacterID, BasedActor.CharacterID, Position.X, Position.Y, Position.Z, 0, 0, 0, 0, M3D.Fill3())
    return z
end

---@brief 通过位置筛选规则筛选出指定范围随机点
---@param BasedActor ActorBase 基于该actor来获取原点
---@param StaticBlackboard table 外部传入的黑板, 用以筛选 searchTargetList 或者 lockTarget
---@param bNotFloorPoint boolean|nil 筛选的点高度是否不为地面高度, 不是则为筛选出来的目标的高度
---@param PosRes TVector3[]|nil 用于保存坐标结果的列表，nil则新建一个table返回
---@param RuleData PosSelectRuleData 位置筛选条件数据
---@param TargetActor ActorBase? 目标对象(默认为 lockTarget)
---@return TVector3[]|nil 返回坐标列表, 可能是空表, nil 表示失败
function GetRandPositionByRule(BasedActor, StaticBlackboard, bNotFloorPoint, PosRes, RuleData, TargetActor)
    if RuleData and ksbcnext(RuleData) then
        -- 兼容预览
        local selectedPoints = getRandPositionByRuleData(BasedActor, TargetActor, RuleData, StaticBlackboard, PosRes)
        if not bNotFloorPoint and selectedPoints then
            for i = 1, #selectedPoints do
                selectedPoints[i].Z = getPositionFloor(BasedActor, selectedPoints[i])
            end
        end

        return selectedPoints
    else
        Log.WarningFormat("GetRandPositionByRule, RuleData invalid, skillID or BuffID:%s",
            StaticBlackboard.skillID or StaticBlackboard.buffID)
    end
end
