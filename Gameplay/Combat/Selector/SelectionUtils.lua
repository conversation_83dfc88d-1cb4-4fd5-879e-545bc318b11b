local CollisionConst = kg_require("Shared.Const.CollisionConst")
local bit = require("Framework.Utils.bit")
local AbilityConst = kg_require("Shared.Const.AbilityConst")
local UKSLib = import("KismetSystemLibrary")
local DebugDrawUtils = kg_require("Misc.DebugDrawUtils")
local ULLFunc = import("LowLevelFunctions")
local KML = import("KismetMathLibrary")
local const = kg_require("Shared.Const")
local SelectorUtilsEx = kg_require("Shared.Combat.Selector.SelectorUtilsEx")

local ETargetActorType = AbilityConst.ETargetActorType
local ESelectorShapeType = AbilityConst.ESelectorShapeType
local ETargetSortStrategy = AbilityConst.ETargetSortStrategy
local AOE_MAX_NUM_PVP = AbilityConst.AOE_MAX_NUM_PVP
local AOE_MAX_NUM = AbilityConst.AOE_MAX_NUM


function GetCapsuleRadius(Entity, DefaultValue)
    if IsValidID(Entity.CppEntity:KAPI_Actor_GetCapsuleComponent()) then
        return Entity.CppEntity:KAPI_Capsule_GetScaledCapsuleRadius()
    end

    return DefaultValue
end

function GetCapsuleHalfHeight(Entity, DefaultValue)
    if IsValidID(Entity.CppEntity:KAPI_Actor_GetCapsuleComponent()) then
        return Entity.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()
    end

    return DefaultValue
end

function GetEntityFloorPosition(Entity, OutPos)
    OutPos:Pack(Entity:GetPosition_P())
    local CapHalfHeight = GetCapsuleHalfHeight(Entity, 45)
    OutPos.Z = OutPos.Z - CapHalfHeight

    return OutPos
end

function CalcRealZ(BasedEntity, OriginZ, HalfHeight, bPositionBaseEntity)
    local RealZ = OriginZ
    if bPositionBaseEntity then
        local CapHalfHeight = GetCapsuleHalfHeight(BasedEntity, 45)
        -- floor z
        RealZ = RealZ - CapHalfHeight
    end

    -- floor + half_height
    -- shape center
    RealZ = RealZ + HalfHeight

    return RealZ
end

function GetRealMaxNum(MaxNum)
    if not MaxNum then return 0 end
    
    if not Game.NetworkManager then return MaxNum end

    MaxNum = math.min(MaxNum, AOE_MAX_NUM)

    return MaxNum
end

function QueryEntitiesInSingleRangeImpl(BaseEntity,
                                        ShapeType, ShapeArgs, CenterPos, Rotation,
                                        SearchTarget, MaxNum, ExtraOption)
    local TargetList = {}
    local OtherTargetList = {}
    local bPositionBaseEntity = false

    if not (Game.me and Game.me.bInWorld) then
        return TargetList, OtherTargetList
    end

    local IgnoreCheckMask = SelectorUtilsEx.GetIgnoreCheckMask(SearchTarget)
    if ShapeType == ESelectorShapeType.Cuboid then
        if #ShapeArgs < 3 then
            return TargetList
        end
        CenterPos.Z = CalcRealZ(BaseEntity, CenterPos.Z, ShapeArgs[3] * 0.5, bPositionBaseEntity)

        local BoxSize = FVector(ShapeArgs[1] * 0.5, ShapeArgs[2] * 0.5, ShapeArgs[3] * 0.5)
        TargetList, OtherTargetList = QueryTargetListInCuboid(BaseEntity, BoxSize, CenterPos, Rotation,
            SearchTarget.TargetType, SearchTarget.Faction, IgnoreCheckMask,
            SearchTarget.bIgnoreSelf or false, ExtraOption.bOnlyRoot or false)
    elseif ShapeType == ESelectorShapeType.Cylinder then
        if #ShapeArgs < 2 then
            return TargetList
        end
        CenterPos.Z = CalcRealZ(BaseEntity, CenterPos.Z, ShapeArgs[2] * 0.5, bPositionBaseEntity)

        local CylinderSize = FVector2D(ShapeArgs[1], ShapeArgs[2])
        TargetList, OtherTargetList = QueryTargetListInCylinder(BaseEntity, CylinderSize, CenterPos, Rotation,
            SearchTarget.TargetType, SearchTarget.Faction, IgnoreCheckMask,
            SearchTarget.bIgnoreSelf or false, ExtraOption.bOnlyRoot or false)
    elseif ShapeType == ESelectorShapeType.Fan3d then
        if #ShapeArgs < 4 then
            return TargetList
        end
        CenterPos.Z = CalcRealZ(BaseEntity, CenterPos.Z, ShapeArgs[3] * 0.5, bPositionBaseEntity)

        local Fan3dSize = FVector4(ShapeArgs[1], ShapeArgs[2], ShapeArgs[3], ShapeArgs[4] * 0.5)
        TargetList, OtherTargetList = QueryTargetListInFan3d(BaseEntity, Fan3dSize, CenterPos, Rotation,
            SearchTarget.TargetType, SearchTarget.Faction, IgnoreCheckMask,
            SearchTarget.bIgnoreSelf or false, ExtraOption.bOnlyRoot or false)
    elseif ShapeType == ESelectorShapeType.Sphere then
        if #ShapeArgs < 1 then
            return TargetList
        end
        CenterPos.Z = CalcRealZ(BaseEntity, CenterPos.Z, 0.0, bPositionBaseEntity)

        local SphereSize = ShapeArgs[1]
        TargetList, OtherTargetList = QueryTargetListInSphere(BaseEntity, SphereSize, CenterPos, Rotation,
            SearchTarget.TargetType, SearchTarget.Faction, IgnoreCheckMask,
            SearchTarget.bIgnoreSelf or false, ExtraOption.bOnlyRoot or false)
    elseif ShapeType == ESelectorShapeType.Annular3d then
        if #ShapeArgs < 3 then
            return TargetList
        end
        CenterPos.Z = CalcRealZ(BaseEntity, CenterPos.Z, ShapeArgs[3] * 0.5, bPositionBaseEntity)

        local Annular3dSize = FVector4(ShapeArgs[1], ShapeArgs[2], ShapeArgs[3], 180)
        TargetList, OtherTargetList = QueryTargetListInAnnular3d(BaseEntity, Annular3dSize, CenterPos, Rotation,
            SearchTarget.TargetType, SearchTarget.Faction, IgnoreCheckMask,
            SearchTarget.bIgnoreSelf or false, ExtraOption.bOnlyRoot or false)
    end

    return
        SortTargets(BaseEntity, CenterPos, ExtraOption.SortType, ExtraOption.SortParameter, GetRealMaxNum(MaxNum),
            TargetList),
        OtherTargetList
end


function getPosition(actor)
    local pos = M3D.Vec3()
    pos:Pack(ULLFunc.GetActorLocation_P(actor, false, M3D.Fill3()))
    return pos
end

---@deprecated
function GetTargetListInCuboid(BaseEntity, position, yaw, ShapeArgs, Faction, TargetType)
    local TargetList = {}
    local entities = Game.EntityManager.entities
    yaw = yaw * math.pi / 180
    for id, entity in pairs(entities) do
        if (BSFunc.CheckTargetNew(BaseEntity, entity, TargetType, Faction, 0)) then
            local entityPos3d = getPosition(entity)
            if not IsValidID(entity.CppEntity:KAPI_Actor_GetCapsuleComponent()) then
                goto continue
            end
            local SelfRadius =  GetCapsuleRadius(entity)
            local entityPos = {x = entityPos3d.X - position.X, y = entityPos3d.Y - position.Y}
            local center = {x = entityPos.x * math.cos(yaw) - entityPos.y * math.sin(yaw),
                            y = entityPos.x * math.sin(yaw) + entityPos.y * math.cos(yaw)}
            local l = ShapeArgs[1]
            local w = ShapeArgs[2]
            -- local h = ShapeArgs[3]
            local min = {x = -l/2, y = -w/2}
            local max = {x = l/2, y = w/2}

            if (intersectCircleAABB(center,SelfRadius,min,max)) then
                table.insert(TargetList, id)
                -- 先不考虑高度
            end

            ::continue::
        end
    end
    return TargetList
end

---@deprecated
function  intersectCircleAABB(center, radius, min, max)
    local closest = {x = math.max(min.x, math.min(center.x, max.x)),
                     y = math.max(min.y, math.min(center.y, max.y))}
    local distance = {x = center.x - closest.x, y = center.y - closest.y}
    if distance.x * distance.x + distance.y * distance.y <= radius * radius then
        return true
    end
    return false
end

---@deprecated
function GetTargetListInCylinder(BaseEntity, position, yaw, ShapeArgs, Faction, TargetType)
    local TargetList = {}
    local entities = Game.EntityManager.entities
    for id, entity in pairs(entities) do
        if (BSFunc.CheckTargetNew(BaseEntity, entity, TargetType, Faction, 0)) then
            local entityPos3d = getPosition(entity)
            if not IsValidID(entity.CppEntity:KAPI_Actor_GetCapsuleComponent()) then
                goto continue
            end
            local SelfRadius =  GetCapsuleRadius(entity)
            local r = ShapeArgs[1]
            -- local h = ShapeArgs[2]
            local distance = math.sqrt((entityPos3d.X - position.X)^2 + (entityPos3d.Y - position.Y)^2)
            --先不考虑高度 and entityPos3d.Y >= position.Y-h/2 and entityPos3d.Y <= position.Y+h/2
            if distance <= r + SelfRadius then
                table.insert(TargetList, id)
            end
            ::continue::
        end
    end
    return TargetList
end

---@deprecated
function GetTargetListInFan3d(BaseEntity, position, yaw, ShapeArgs, Faction, TargetType)
    local TargetList = {}
    local entities = Game.EntityManager.entities
    yaw = yaw * math.pi / 180
    --扇形，参数为内半径，外半径，高度，角度（非弧度）
    for id, entity in pairs(entities) do
        if (BSFunc.CheckTargetNew(BaseEntity, entity, TargetType, Faction, 0)) then
            local entityPos3d = getPosition(entity)
            --转换坐标系
            local entityPos = {x = entityPos3d.X - position.X, y = entityPos3d.Y - position.Y}
            local center = {x = entityPos.x * math.cos(yaw) - entityPos.y * math.sin(yaw),
                            y = entityPos.x * math.sin(yaw) + entityPos.y * math.cos(yaw)}
            if not IsValidID(entity.CppEntity:KAPI_Actor_GetCapsuleComponent()) then
                goto continue
            end
            local SelfRadius = GetCapsuleRadius(entity)
            local r1 = ShapeArgs[1]
            local r2 = ShapeArgs[2]
            -- local h = ShapeArgs[3]
            local angle = ShapeArgs[4]
            --判断是否在一个朝向
            local angle1 = math.atan(center.y, center.x) * 180 / math.pi

            if math.abs(angle1) <= angle/2 then
                local distance = math.sqrt((entityPos3d.X - position.X)^2 + (entityPos3d.Y - position.Y)^2)
                if distance <= r2+SelfRadius and distance >= r1-SelfRadius then
                    table.insert(TargetList, id)
                end
            else
                local distance = math.sqrt((entityPos3d.X - position.X)^2 + (entityPos3d.Y - position.Y)^2)
                local delta1 = (math.abs(angle1) - angle/2)* math.pi / 180
                local len1 = distance * math.cos(delta1)
                local dis1 = distance * math.sin(delta1)
                if len1 <= r2 and len1 >= r1 and dis1 <= SelfRadius then
                    table.insert(TargetList, id)
                end
            end
            ::continue::
        end
    end
    return TargetList
end

---@deprecated
function GetTargetListInSphere(BaseEntity, position, yaw, ShapeArgs, Faction, TargetType)
    local TargetList = {}
    local entities = Game.EntityManager.entities
    for id, entity in pairs(entities) do
        if (BSFunc.CheckTargetNew(BaseEntity, entity, TargetType, Faction, 0)) then
            local entityPos3d = getPosition(entity)
            if entity.CppEntity:KAPI_Actor_GetCapsuleComponent() == KG_INVALID_ID then
                goto continue
            end
            local SelfRadius = GetCapsuleRadius(entity)
            local r = ShapeArgs[1]
            local distance = math.sqrt((entityPos3d.X - position.X)^2 + (entityPos3d.Y - position.Y)^2) --+ (entityPos3d.Y - position.Y)^2)
            if distance <= r+SelfRadius then
                table.insert(TargetList, id)
            end
            ::continue::
        end
    end
    return TargetList
end

---@deprecated
function GetTargetListInAnnular3d(BaseEntity, position, yaw, ShapeArgs, Faction, TargetType)
    local TargetList = {}
    local entities = Game.EntityManager.entities
    for id, entity in pairs(entities) do
        if (BSFunc.CheckTargetNew(BaseEntity, entity, TargetType, Faction, 0)) then
            local entityPos3d = getPosition(entity)
            if entity.CppEntity:KAPI_Actor_GetCapsuleComponent() == KG_INVALID_ID then
                goto continue
            end
            local SelfRadius = GetCapsuleRadius(entity)
            local r1 = ShapeArgs[1]
            local r2 = ShapeArgs[2]
            -- local h = ShapeArgs[3]
            local distance = math.sqrt((entityPos3d.X - position.X)^2 + (entityPos3d.Y - position.Y)^2)
            if distance <= r2+SelfRadius and distance >= r1-SelfRadius then
                --and entityPos3d.Y >= position.Y-h/2 and entityPos3d.Y <= position.Y+h/2 then
                table.insert(TargetList, id)
            end
            ::continue::
        end
    end
    return TargetList
end

function SortTargets(BaseEntity, Pos, SortType, SortParams, MaxCount, TargetList)
    if SortType == ETargetSortStrategy.None then
		if const.DEFAULT_SELECT_SORT_SWITCH and Game.BSManager.bIsInEditor == false then
			TargetList = DefaultSort(TargetList)
		end
		
        if MaxCount > 0 then
            TargetList = table.sub(TargetList, 1, MaxCount)
        end
    elseif SortType == ETargetSortStrategy.ByProp then
		TargetList = SortByProp(SortParams[1], MaxCount, SortParams[2] or 0, TargetList)
    elseif SortType == ETargetSortStrategy.ByDistance  then
        local order = SortParams[1]
        --根据TargetList中entitiesId得到实体的位置信息，然后根据位置信息计算距离，先不考虑高度
        table.sort(TargetList, function(a, b)
            local aEntity = Game.EntityManager:GetEntityByIntID(a)
            local aPos = aEntity:GetPosition()
            local bEntity = Game.EntityManager:GetEntityByIntID(b)
            local bPos = bEntity:GetPosition()
            local aCapCompId = aEntity.CppEntity:KAPI_Actor_GetCapsuleComponent()
            local bCapCompId = bEntity.CppEntity:KAPI_Actor_GetCapsuleComponent()
            local aDistance = math.sqrt((aPos.X - Pos.X)^2 + (aPos.Y - Pos.Y)^2)
            local bDistance = math.sqrt((bPos.X - Pos.X)^2 + (bPos.Y - Pos.Y)^2)
            if IsValidID(aCapCompId) and IsValidID(bCapCompId) then
                local aRadius =  GetCapsuleRadius(aEntity)
                local bRadius =  GetCapsuleRadius(bEntity)
                aDistance = aDistance - aRadius
                bDistance = bDistance - bRadius
            end
            if order == 0 then
                return aDistance < bDistance
            else
                return aDistance > bDistance
            end
        end)
        if MaxCount > 0 then
            TargetList = table.sub(TargetList, 1, MaxCount)
        end
    elseif SortType == ETargetSortStrategy.Random  then
        --随机打乱table的顺序取前MaxCount个
        -- 没有shuffle接口
        local count = #TargetList
        if count == nil or count == 0 then
            return TargetList
        end
        for i = 1, count do
            local randomIndex = math.random(1, count)
            TargetList[i], TargetList[randomIndex] = TargetList[randomIndex], TargetList[i]
        end
        if MaxCount > 0 then
            TargetList = table.sub(TargetList, 1, MaxCount)
        end
    elseif SortType == ETargetSortStrategy.EnterOrder  then
        --todo:光环根据进出顺序排序
	elseif SortType == ETargetSortStrategy.ByHpRate then
		TargetList = SortByHpRate(MaxCount, SortParams[1] or 0, TargetList)
    elseif SortType == ETargetSortStrategy.BySortMethod then
        local sortMethod = SortParams[1]
        local order = SortParams[2]
        --0为升序，1为降序
        table.sort(TargetList, function(a,b)
            local aEntity = Game.EntityManager:GetEntityByIntID(a)
            local bEntity = Game.EntityManager:GetEntityByIntID(b)
            local methodValueA = aEntity.GetSelectorSortMethodValue and aEntity:GetSelectorSortMethodValue(sortMethod) or 0
            local methodVAlueB = bEntity.GetSelectorSortMethodValue and bEntity:GetSelectorSortMethodValue(sortMethod) or 0
            if order == 0 then
                return methodValueA < methodVAlueB
            else
                return methodValueA > methodVAlueB
            end
        end)
        if MaxCount > 0 then
            TargetList = table.sub(TargetList, 1, MaxCount)
        end
    elseif  SortType == ETargetSortStrategy.ByDistanceDivide then
		TargetList = SortByDistanceDivide(BaseEntity, SortParams, Pos, MaxCount, TargetList)
	elseif  SortType == ETargetSortStrategy.ByCameraDir then
		TargetList = SortByCameraDir(BaseEntity, Pos, MaxCount, TargetList)
    end
    return TargetList
end

function SortByProp(AttributeID, MaxCount, Order, TargetList)
	local attributeID = AttributeID
	local attribute = GetAttributeNameByID(attributeID)
	if attribute then
		--0为升序，1为降序
		local filterList = {}
		for _, entityID in ipairs(TargetList) do
			local entity = Game.EntityManager:GetEntityByIntID(entityID)
			if entity[attribute] then
				table.insert(filterList, entityID)
			end
		end
		table.sort(filterList, function(a,b)
			local aEntity = Game.EntityManager:GetEntityByIntID(a)
			local bEntity = Game.EntityManager:GetEntityByIntID(b)
			local aProp = aEntity[attribute] or 0
			local bProp = bEntity[attribute] or 0
			if Order == 0 then
				return aProp < bProp
			else
				return aProp > bProp
			end
		end)
        if MaxCount > 0 then
            TargetList = table.sub(TargetList, 1, MaxCount)
        end
	else
		if MaxCount > 0 then
            TargetList = table.sub(TargetList, 1, MaxCount)
        end
	end
	return TargetList
end

function SortByHpRate(MaxCount, Order, TargetList)
	local filterList = {}
	for _, entityID in ipairs(TargetList) do
		local entity = Game.EntityManager:GetEntityByIntID(entityID)
		if entity.Hp and entity.CurrentMaxHp and entity.CurrentMaxHp ~= 0 then
			table.insert(filterList, entityID)
		end
	end
	table.sort(filterList, function(a,b)
		local aEntity = Game.EntityManager:GetEntityByIntID(a)
		local bEntity = Game.EntityManager:GetEntityByIntID(b)
		local aPercent = aEntity.Hp / aEntity.CurrentMaxHp
		local bPercent = bEntity.Hp / bEntity.CurrentMaxHp
		if Order == 0 then
			return aPercent < bPercent
		else
			return aPercent > bPercent
		end
	end)
	TargetList = table.sub(filterList, 1, MaxCount)
	return TargetList
end

function SortByDistanceDivide(BaseEntity, SortParams, Pos, MaxCount, TargetList)
    local order = SortParams[1] or 0
    local orderData = {}
    local Trans = M3D.Transform()
    Trans:Pack(BaseEntity:GetTransform_P())
    for _, entityID in ipairs(TargetList) do
		local entity = Game.EntityManager:GetEntityByIntID(entityID)
        local entityPos = entity:GetPosition()
        local distance = math.sqrt((entityPos.X - Pos.X)^2 + (entityPos.Y - Pos.Y)^2)
        --如果是自己身上的组件，将距离平方设置为-1保证优先选择自己
        if BaseEntity:uid() == entityID then
            distance = -1.0
        end
        local disDivide = #DISTANCE_DIVIDE
        for i = 1, #DISTANCE_DIVIDE do
            if distance < DISTANCE_DIVIDE[i] then
                disDivide = i
                break
            end
        end
        local inversePos = M3D.Vec3()
        Trans:InverseTransformPosition(entityPos, inversePos)
        inversePos:GetNormal2D(inversePos)
        local cosin = inversePos:Dot(M3D.Vec3(1, 0, 0))
        if BaseEntity:uid() == entityID then
            cosin = 2.0
        end
        orderData[entityID] = {DisDivide = disDivide, Distance = distance, Cosin = cosin}
    end
	if order == 0 then
		table.sort(TargetList, function(a, b)
			local aData = orderData[a]
			local bData = orderData[b]
			if (aData.DisDivide == bData.DisDivide) then
				if (aData.DisDivide == #DISTANCE_DIVIDE) then
					return aData.Distance < bData.Distance
				else
					return aData.Cosin > bData.Cosin
				end
			else
				return aData.DisDivide < bData.DisDivide
			end
		end)
	else
		table.sort(TargetList, function(a, b)
			local aData = orderData[a]
			local bData = orderData[b]
			if (aData.DisDivide == bData.DisDivide) then
				if (aData.DisDivide == #DISTANCE_DIVIDE) then
					return aData.Distance > bData.Distance
				else
					return aData.Cosin < bData.Cosin
				end
			else
				return aData.DisDivide > bData.DisDivide
			end
		end)
	end
    if MaxCount > 0 then
        TargetList = table.sub(TargetList, 1, MaxCount)
    end
	return TargetList
end

-- 镜头优先排序（按角度优先，角度相同时按距离排序）
-- @param BaseEntity        : 基准实体（暂未使用）
-- @param Pos               : 基准位置（通常是玩家位置）
-- @param MaxCount          : 最多返回多少个目标（<=0 表示不限制）
-- @param TargetList        : 所有待排序的目标UID列表
-- @return                  : 按照视角优先排好序的 UID 列表（最多 MaxCount 个）

function SortByCameraDir(BaseEntity, Pos, MaxCount, TargetList)
	local Results = {}
	local EntityManager = Game.EntityManager

	-- 获取相机方向并单位化
	local cameraForwardVec = KML.GetForwardVector(Game.CameraManager.CameraCachePrivate.POV.Rotation)
	local forwardLen = math.sqrt(cameraForwardVec.X^2 + cameraForwardVec.Y^2 + cameraForwardVec.Z^2)
	cameraForwardVec.X = cameraForwardVec.X / forwardLen
	cameraForwardVec.Y = cameraForwardVec.Y / forwardLen
	cameraForwardVec.Z = cameraForwardVec.Z / forwardLen

	local CombinedSortList = {}

	for _, UID in ipairs(TargetList) do
		local TEntity = EntityManager:GetEntityByIntID(UID)
		if not TEntity then goto continue end

		-- 目标位置向量
		local targetPosX, targetPosY, targetPosZ = TEntity:GetPosition_P()
		local dx = targetPosX - Pos.X
		local dy = targetPosY - Pos.Y
		local dz = targetPosZ - Pos.Z
		local Distance = math.sqrt(dx * dx + dy * dy + dz * dz)

		-- 防止除以 0
		if Distance < 0.0001 then
			table.insert(CombinedSortList, {
				TUID = UID,
				Angle = 0,       -- 或者 180，根据逻辑是否希望“完全贴脸目标”视为最优或最劣
				Distance = 0,
			})
			goto continue
		end

		-- 单位化方向
		local dirX = dx / Distance
		local dirY = dy / Distance
		local dirZ = dz / Distance

		-- 相机方向与目标方向夹角（单位：度）
		local CosTheta = cameraForwardVec.X * dirX + cameraForwardVec.Y * dirY + cameraForwardVec.Z * dirZ
		local Angle = math.deg(math.acos(math.min(math.max(CosTheta, -1), 1))) -- 防止 acos 越界

		table.insert(CombinedSortList, {
			TUID = UID,
			Angle = Angle,
			Distance = Distance,
		})

		::continue::
	end

	-- 排序：先按角度升序，角度相同时按距离升序
	table.sort(CombinedSortList, function(a, b)
		if math.abs(a.Angle - b.Angle) > 0.01 then
			return a.Angle < b.Angle
		else
			return a.Distance < b.Distance
		end
	end)

	-- 填充结果
	for i, entry in ipairs(CombinedSortList) do
		Results[i] = entry.TUID
	end

	-- 限制返回数量
	if MaxCount > 0 then
		Results = table.sub(Results, 1, MaxCount)
	end

	return Results
end

function DefaultSort(TargetList)
	local CurrentCameraDir = KML.GetForwardVector(Game.PlayerController:GetControlRotation())
	local orderData = {}
	local selfPos = M3D.Vec3()
    selfPos:Pack(Game.me:GetPosition_P())
	
	for _, entityID in ipairs(TargetList) do
		local entity = Game.EntityManager:GetEntityByIntID(entityID)
		local entityPos = M3D.Vec3()
        entityPos:Pack(entity:GetPosition_P())
		local distance = M3D.GetDistance(entityPos, selfPos)

		entityPos:Sub(selfPos, entityPos)
		
		local CosTheta = (CurrentCameraDir.X * entityPos.X + CurrentCameraDir.Y * entityPos.Y + CurrentCameraDir.Z * entityPos.Z)
			/ (math.sqrt(CurrentCameraDir.X ^ 2 + CurrentCameraDir.Y ^ 2 + CurrentCameraDir.Z ^ 2) * (math.sqrt(entityPos.X ^ 2 + entityPos.Y ^ 2 + entityPos.Z ^ 2)))
		local angle = math.deg(math.acos(CosTheta))
		orderData[entityID] = {Distance = distance, Angle = angle}
	end
	
	table.sort(TargetList, function(a, b)
		local aData = orderData[a]
		local bData = orderData[b]
		if a == b then
			return false
		end
		if (aData.Distance < bData.Distance) then
			if bData.Distance < Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_DISTANCE") or aData.Distance >= Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_DISTANCE") then
				-- 都在第一范围内或外,通过角度判断
				return bData.Angle >= Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_ANGLE") or aData.Angle < Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_ANGLE")
			else
				return true
			end
		else
			if aData.Distance < Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_DISTANCE") or bData.Distance >= Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_DISTANCE") then
				-- 都在第一范围内或外,通过角度判断
				return aData.Angle < Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_ANGLE") and bData.Angle >= Game.TableData.GetConstDataRow("ATTACK_SELECT_DEFAULT_ANGLE")
			else
				return false
			end
		end
	end)
	
	return TargetList
end

function GetAttributeNameByID(AttributeID)
    local attributeName = Game.TableData.GetPropDataRow(AttributeID)
    if not attributeName then
        Log.ErrorFormat("[GetAttributeNameByID] AttributeID %s not found", AttributeID)
        return
    end

    return attributeName
end

-- luacheck: push ignore
SHOW_ATTACK_BOX_DURATION = 2.0
ATTACK_BOX_COLOR = FLinearColor.Red
ATTACK_BOX_DRAW_THICKNESS = 2.0
-- 距离分级
DISTANCE_DIVIDE = {500.0, 1000.0, 2000.0}
-- luacheck: pop

function ShowAttackBox()
    return _G.UE_EDITOR and Game.BSManager and Game.BSManager:IsShowCollision()
end

function ScriptShowAttackBox()
    if not _G.UE_EDITOR then
        return false
    end

    local BSManager = Game.BSManager
    return BSManager and BSManager:IsShowCollision() and (not BSManager:IsNativeShowCollision())
end

function GetExtraObjectQueryType(targetType)
    if targetType == 0 then
        return CollisionConst.QUERY_BY_OBJECTTYPES.ABILITY_WITH_SCENE_ACTOR
    elseif (targetType >= ETargetActorType.Interactor) and (bit.band(targetType, ETargetActorType.Interactor) > 0) then
        return CollisionConst.QUERY_BY_OBJECTTYPES.ABILITY_WITH_SCENE_ACTOR
    else
        return CollisionConst.QUERY_BY_OBJECTTYPES.ABILITY_DEFAULT
    end
end

function QueryTargetListInSphere(BaseEntity, SphereSize, pos, dir, TargetType, Faction, IgnoreCheckMask, bIgnoreSelf, bOnlyRoot)

    -- 1. check show attack box
    if ScriptShowAttackBox() then
        UKSLib.DrawDebugSphere(
            _G.GetContextObject(),
            pos,
            SphereSize,
            30,
            ATTACK_BOX_COLOR,
            SHOW_ATTACK_BOX_DURATION,
            ATTACK_BOX_DRAW_THICKNESS)
    end

    -- 2. do query
    local extraQueryTypes = GetExtraObjectQueryType(TargetType)
    local TargetList = {} --luacheck: ignore
    local OtherTargetList = {} --luacheck: ignore
	local result = BaseEntity.CppEntity:KAPI_Collision_SphereCheck(SphereSize, FVector(pos.X, pos.Y, pos.Z), FRotator(dir.Pitch, dir.Yaw, dir.Roll), extraQueryTypes, bIgnoreSelf)
    CheckTarget(BaseEntity, result, TargetType, Faction, IgnoreCheckMask, TargetList, OtherTargetList)
    return TargetList, OtherTargetList
end

function QueryTargetListInCuboid(BaseEntity, BoxSize, pos, dir, TargetType, Faction, IgnoreCheckMask, bIgnoreSelf, bOnlyRoot)

    -- 1. check show attack box
    if ScriptShowAttackBox() then
        UKSLib.DrawDebugBox(
            _G.GetContextObject(),
            pos,
            BoxSize,
            ATTACK_BOX_COLOR,
            dir,
            SHOW_ATTACK_BOX_DURATION,
            ATTACK_BOX_DRAW_THICKNESS
        )
    end

    -- 2. do query
    local extraQueryTypes = GetExtraObjectQueryType(TargetType)
    local TargetList = {} --luacheck: ignore
    local OtherTargetList = {} --luacheck: ignore
	local result = BaseEntity.CppEntity:KAPI_Collision_BoxCheck(BoxSize, FVector(pos.X, pos.Y, pos.Z), FRotator(dir.Pitch, dir.Yaw, dir.Roll), extraQueryTypes, bIgnoreSelf)
    CheckTarget(BaseEntity, result, TargetType, Faction, IgnoreCheckMask, TargetList, OtherTargetList)
    return TargetList, OtherTargetList
end

function QueryTargetListInCylinder(BaseEntity, CylinderSize, pos, dir, TargetType, Faction, IgnoreCheckMask, bIgnoreSelf, bOnlyRoot)
    -- 1. check show attack box
    if ScriptShowAttackBox() then
        local radius = CylinderSize.X
        local halfHeight = CylinderSize.Y * 0.5

        local startPos = FVector(pos.X, pos.Y, pos.Z - halfHeight)
        local endPos = FVector(pos.X, pos.Y, pos.Z + halfHeight)
        UKSLib.DrawDebugCylinder(
            _G.GetContextObject(),
            startPos,
            endPos,
            radius,
            30,
            ATTACK_BOX_COLOR,
            SHOW_ATTACK_BOX_DURATION,
            ATTACK_BOX_DRAW_THICKNESS
        )
    end

    -- 2. do query
    local extraQueryTypes = GetExtraObjectQueryType(TargetType)
    local TargetList = {} --luacheck: ignore
    local OtherTargetList = {} --luacheck: ignore
	local posF = FVector(pos.X, pos.Y, pos.Z)
	local rotF = FRotator(dir.Pitch, dir.Yaw, dir.Roll)
	local result = BaseEntity.CppEntity:KAPI_Collision_CapsuleCheck(CylinderSize, posF, rotF, extraQueryTypes, bIgnoreSelf)
    CheckTarget(BaseEntity, result, TargetType, Faction, IgnoreCheckMask, TargetList, OtherTargetList)
    return TargetList, OtherTargetList
end

function QueryTargetListInFan3d(BaseEntity, Fan3dSize, pos, dir, TargetType, Faction, IgnoreCheckMask, bIgnoreSelf, bOnlyRoot)
    -- 1. check show attack box
    if ScriptShowAttackBox() then
        DebugDrawUtils.DrawDebugFan3d(
            pos,
            Fan3dSize.X,
            Fan3dSize.Y,
            30,
            dir,
            Fan3dSize.W,
            Fan3dSize.Z,
            ATTACK_BOX_COLOR,
            SHOW_ATTACK_BOX_DURATION,
            ATTACK_BOX_DRAW_THICKNESS
        )
    end

    -- 2. do query
    local extraQueryTypes = GetExtraObjectQueryType(TargetType)
    local TargetList = {} --luacheck: ignore
    local OtherTargetList = {} --luacheck: ignore
	local result = BaseEntity.CppEntity:KAPI_Collision_FanCheck(Fan3dSize, FVector(pos.X, pos.Y, pos.Z), FRotator(dir.Pitch, dir.Yaw, dir.Roll), extraQueryTypes, bIgnoreSelf)
    CheckTarget(BaseEntity, result, TargetType, Faction, IgnoreCheckMask, TargetList, OtherTargetList)
    return TargetList, OtherTargetList
end

-- @shijingzhe:这里传入的是FVector,但cpp接受的是FVector2D,目测存在不正确的类型检查和自动转换,后续由@wanghuihui排查
function QueryTargetListInAnnular3d(BaseEntity, Annular3dSize, pos, dir, TargetType, Faction, IgnoreCheckMask, bIgnoreSelf, bOnlyRoot)
    -- 1. check show attack box
    if ScriptShowAttackBox() then
        local innerRadius = Annular3dSize.X
        local outerRadius = Annular3dSize.Y
        local halfHeight = Annular3dSize.Z * 0.5

        local startPos = FVector(pos.X, pos.Y, pos.Z - halfHeight)
        local endPos = FVector(pos.X, pos.Y, pos.Z + halfHeight)

        -- inner circle
        if innerRadius > 1.0 then
            UKSLib.DrawDebugCylinder(
                _G.GetContextObject(),
                startPos,
                endPos,
                innerRadius,
                30,
                ATTACK_BOX_COLOR,
                SHOW_ATTACK_BOX_DURATION,
                ATTACK_BOX_DRAW_THICKNESS
            )
        end

        -- outer circle
        UKSLib.DrawDebugCylinder(
            _G.GetContextObject(),
            startPos,
            endPos,
            outerRadius,
            30,
            ATTACK_BOX_COLOR,
            SHOW_ATTACK_BOX_DURATION,
            ATTACK_BOX_DRAW_THICKNESS
        )
    end

    -- 2. do query
    local extraQueryTypes = GetExtraObjectQueryType(TargetType)
    local TargetList = {} --luacheck: ignore
    local OtherTargetList = {} --luacheck: ignore
	local result = BaseEntity.CppEntity:KAPI_Collision_FanCheck(Annular3dSize, FVector(pos.X, pos.Y, pos.Z), FRotator(dir.Pitch, dir.Yaw, dir.Roll), extraQueryTypes, bIgnoreSelf)
    CheckTarget(BaseEntity, result, TargetType, Faction, IgnoreCheckMask, TargetList, OtherTargetList)
    return TargetList, OtherTargetList
end

__Temp_Target_Map = __Temp_Target_Map or {}

function CheckTarget(BaseEntity, result, TargetType, Faction, IgnoreCheckMask, InTargetList, InOtherTargetList)
    table.clear(__Temp_Target_Map)
    
    for _, targetUID in ipairs(result:ToTable()) do
		if not __Temp_Target_Map[targetUID] then
			local entity = Game.EntityManager:GetEntityByIntID(targetUID)
			if entity ~= nil and BSFunc.CheckTargetNew(BaseEntity, entity, TargetType, Faction, IgnoreCheckMask) then
				__Temp_Target_Map[targetUID] = true
				table.insert(InTargetList, targetUID)
			elseif entity ~= nil and InOtherTargetList ~= nil then
				table.insert(InOtherTargetList, targetUID)
			end
		end
    end
    
    return InTargetList, InOtherTargetList
end

---@deprecated
-- 处理 Attack 相关蓝图的攻击范围数据
__AttackShapeArgOverrides = __AttackShapeArgOverrides or { 0, 0, 0, 0 }
function GetAttackShapeOverride(attackShapeData)
    local attackShapeType = attackShapeData and attackShapeData.AttackShapeType

    table.clear(__AttackShapeArgOverrides)
    local shapeArgOverrides = __AttackShapeArgOverrides

    if attackShapeType == ESelectorShapeType.Cuboid then
        shapeArgOverrides[1] = attackShapeData.Length
        shapeArgOverrides[2] = attackShapeData.Width
        shapeArgOverrides[3] = attackShapeData.Height
    elseif attackShapeType == ESelectorShapeType.Cylinder then
        shapeArgOverrides[1] = attackShapeData.Radius
        shapeArgOverrides[2] = attackShapeData.Height
    elseif attackShapeType == ESelectorShapeType.Fan3d then
        shapeArgOverrides[1] = attackShapeData.InnerRadius
        shapeArgOverrides[2] = attackShapeData.OuterRadius
        shapeArgOverrides[3] = attackShapeData.Height
        shapeArgOverrides[4] = attackShapeData.Angle
    elseif attackShapeType == ESelectorShapeType.Sphere then
        shapeArgOverrides[1] = attackShapeData.Radius
    elseif attackShapeType == ESelectorShapeType.Annular3d then
        shapeArgOverrides[1] = attackShapeData.InnerRadius
        shapeArgOverrides[2] = attackShapeData.OuterRadius
        shapeArgOverrides[3] = attackShapeData.Height
    else
        return nil
    end

    return shapeArgOverrides
end

---@public
---@param entityUID number
---@param actors userdata[]
---@param selectionRuleID number
---@return number[]
function CheckAndSortTargets(entityUID, actors, selectionRuleID, bNeedSort)
    local targets = {}

    local entity = Game.EntityManager:GetEntityByIntID(entityUID)
    if not entity then
        return targets
    end

    local selectRuleDataRow = Game.TableData.GetTargetSelectionRuleDataRow(selectionRuleID)

    local searchRange = selectRuleDataRow.Range
    local rangeDataList = searchRange and searchRange.RangeDataList
    local singleSearchRange = rangeDataList and rangeDataList[1] -- 目前暂仅支持非组合规则形状

    local searchTarget = selectRuleDataRow.Target
    local maxNum = selectRuleDataRow.MaxNum or 0

    if not singleSearchRange then
        return targets
    end

    local ignoreCheckMask = SelectorUtilsEx.GetIgnoreCheckMask(searchTarget)

    targets = CheckTarget(entity, actors, searchTarget.TargetType, searchTarget.Faction, ignoreCheckMask, targets, nil)
	
    if not bNeedSort then
        return targets
    end
	
    return SortTargets(entity, entity:GetPosition(),
        selectRuleDataRow.SortType, selectRuleDataRow.SortParameter, maxNum, targets)
end

-- luacheck: push ignore
__query_entities_self_pos = M3D.Vec3()
__query_entities_target_pos = M3D.Vec3()
__query_entities_dir = M3D.Vec3()
__query_entities_offset = M3D.Vec3()
__query_entities_rot_based = M3D.Rotator()
__query_entities_rot_tmp = M3D.Rotator()
---@param baseEntity Entity: 基准单位(self)
---@param targetEntity Entity: 目标单位(target), 基准位置计算的时候，可能会用到
---@param staticBlackboard table: 外部传入的黑板
---@param coordinatePointType number(EBaseCoordinatePointType): 基准坐标点类型
---@param coordinateOffset number[]: 坐标点偏移
---@param clockwiseRotation number: 顺时针旋转角度(先旋转，再偏移)
---@param listIndex number:取黑板值list中数据索引
function GetPosAndYawByCoordinatePointType(basedActor, inTargetActor, staticBlackboard,
                                           coordinatePointType, coordinateOffset, clockwiseRotation, outPos, listIndex,
                                           inSelfPos, inSelfYaw)
    return SelectorUtilsEx.GetPosAndYawByCoordinatePointType(basedActor, inTargetActor, staticBlackboard,
        coordinatePointType, coordinateOffset, clockwiseRotation, outPos, listIndex, inSelfPos, inSelfYaw)
end


-- luacheck: pop