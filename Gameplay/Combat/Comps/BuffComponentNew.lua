local Buff = kg_require("Gameplay.Combat.Buff.Buff")
local ETE = kg_require("Data.Config.BattleSystem.ExportedEnum").ETE
local BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID = kg_require("Shared.Const").BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID
local UBSFunc = import("BSFunctionLibrary")
local EBSAFinishReason = ETE.EBSAFinishReason
                                                                               
BuffComponentNew = DefineComponent("BuffComponentNew")

function BuffComponentNew:ctor()
    -- buffId,instigatorID 两层嵌套，和服务器保持一致
    self.buffInstListNew = {}

    self.OnAddBuffRecord = LuaMulticastDelegate.new()
	self.isRebuilding = false
end

function BuffComponentNew:__component_EnterWorld__()
	local PlayerInfoManager = Game.PlayerInfoManager
    for _, buffDict in pairs(self.buffInstListNew) do
        for _, buff in pairs(buffDict) do
            if not buff.isActive then
                buff:OnAdd()
				-- 目前看下来没有全局publish的需求
				Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.BATTLE_BUFF_START, buff)
				if self.OnBattleBuffLayerChangedForFool then
					self:OnBattleBuffLayerChangedForFool(buff)
				end
				if self.OnBuffChangedForSceneActorCondition then
					self:OnBuffChangedForSceneActorCondition(buff)
				end
				if Game.SpiritualVisionSystem then
					Game.SpiritualVisionSystem:onBuffAdd(buff)
				end
				if PlayerInfoManager then
					PlayerInfoManager:OnAddOrRemoveBuff(buff)
				end
            end
        end
    end
end

function BuffComponentNew:__component_ExitWorld__()
	local PlayerInfoManager = Game.PlayerInfoManager
    for _, buffDict in pairs(self.buffInstListNew) do
        for _, buff in pairs(buffDict) do
            if buff.isActive then
                buff:OnRemove(EBSAFinishReason.FR_Destroy)
				Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.BATTLE_BUFF_START, buff)
				if self.OnBattleBuffLayerChangedForFool then
					self:OnBattleBuffLayerChangedForFool(buff)
				end
				if self.OnBuffChangedForSceneActorCondition then
					self:OnBuffChangedForSceneActorCondition(buff)
				end
				if Game.SpiritualVisionSystem then
					Game.SpiritualVisionSystem:onBuffAdd(buff)
				end
				if PlayerInfoManager then
					PlayerInfoManager:OnAddOrRemoveBuff(buff)
				end
            end
        end
    end
end

---@param rootSkillID number 目前仅在特效NiagaraEffectType检查GM打开的情况下才会下发, 默认情况下这个值是没有的
function BuffComponentNew:AddBuffNew(buffID, instigatorID, triggerID, level, layer, maxLayer, totalLife, totalStartTimeStamp, linkedTargetId, rootSkillID)
        --如果还未加载，则等到Actor加载出来，再通过回调添加Buff
    if not self.bInWorld then return end
    local buffInstListIndex = BuffComponentNew.getBuffIDSecondaryIndex(buffID, instigatorID)
    if not self.buffInstListNew[buffID] then
        self.buffInstListNew[buffID] = {}
    end
    if self.buffInstListNew[buffID][buffInstListIndex] then
        return
    end
    local buff = Buff.new()
    buff:init(self, buffID, instigatorID, triggerID, level, layer, maxLayer, totalLife, totalStartTimeStamp, linkedTargetId, rootSkillID, self.FinalOwnerID)
    self.buffInstListNew[buffID][buffInstListIndex] = buff
    self.buffInstListNew[buffID][buffInstListIndex]:OnAdd()
	Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.BATTLE_BUFF_START, buff)
	if self.OnBattleBuffLayerChangedForFool then
		self:OnBattleBuffLayerChangedForFool(buff)
	end
	if self.OnBuffChangedForSceneActorCondition then
		self:OnBuffChangedForSceneActorCondition(buff)
	end
	if Game.SpiritualVisionSystem then
		Game.SpiritualVisionSystem:onBuffAdd(buff)
	end
	if Game.PlayerInfoManager then
		Game.PlayerInfoManager:OnAddOrRemoveBuff(buff)
	end
    if self.eid == Game.me.eid and Game.CommonInteractorManager then
        Game.CommonInteractorManager:TriggerCommonInteractorEvent(Enum.CommonInteractorEventType.BUFF_START, buffID)
    end
end

function BuffComponentNew:OnMsgAddBuffNew(buffID, instigatorID, triggerID, level, layer, maxLayer, totalLife, totalStartTimeStamp, linkedTargetId, rootSkillID)
    -- self:DebugFmt("OnMsgAddBuffNew, entity:%s, buffId:%s, instigatorID:%s, layer:%s, level:%s, linkedTargetId: %s,rootSkillID:%s", self.eid, buffID,
    --     instigatorID, level, layer, linkedTargetId,rootSkillID)
    self:AddBuffNew(buffID, instigatorID, triggerID, level, layer, maxLayer, totalLife, totalStartTimeStamp, linkedTargetId, rootSkillID)
end

function BuffComponentNew:OnMsgRemoveBuffNew(buffID, instigatorID, reason)
    -- self:DebugFmt("OnMsgRemoveBuffNew, entity:%s, instigatorID:%s, buffID:%s", self.eid, instigatorID, buffID)
    local buffs = self.buffInstListNew[buffID]
    if not buffs then
        --self:ErrorFmt("RemoveBuffSC error, buff not exist, instigatorID:%s, buffId:%s", instigatorID, buffID)
        return
    end
    local buffInstListIndex = BuffComponentNew.getBuffIDSecondaryIndex(buffID, instigatorID)
    local buff = buffs[buffInstListIndex]
    if not buff then
        --self:ErrorFmt("RemoveBuffSC error, buff not exist, sourceUid:%s, buffId:%s", instigatorID, buffID)
        return
    end
    buff:OnRemove(reason)
	-- 目前看下来没有全局publish的需求
	Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.BATTLE_BUFF_END, buff)
	if self.OnBuffChangedForSceneActorCondition then
		self:OnBuffChangedForSceneActorCondition(buff)
	end
	if Game.PlayerInfoManager then
		Game.PlayerInfoManager:OnAddOrRemoveBuff(buff)
	end
    buff:dtor()
    buffs[buffInstListIndex] = nil
end

function BuffComponentNew:OnMsgBuffLayerChangeNew(buffID, instigatorID, oldValue, newValue)
    local buffs = self.buffInstListNew[buffID]
    if not buffs then
        --self:ErrorFmt("OnMsgBuffLayerChangeNew error, buff not exist, instigatorID:%s, buffId:%s", instigatorID, buffID)
        return
    end
    local buffInstListIndex = BuffComponentNew.getBuffIDSecondaryIndex(buffID, instigatorID)
    local buff = buffs[buffInstListIndex]
    if not buff then
        --self:ErrorFmt("OnMsgBuffLayerChangeNew error, buff not exist, sourceUid:%s, buffId:%s", instigatorID, buffID)
        return
    end
    if buffInstListIndex == BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID then
        buff:ResetInstigatorID(instigatorID)
    end
    buff:OnLayerChange(newValue)
	Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.BATTLE_BUFF_LAYER_CHANGED, buff)
	if self.OnBattleBuffLayerChangedForFool then
		self:OnBattleBuffLayerChangedForFool(buff)
	end
end

function BuffComponentNew:OnMsgBuffTotalLifeChangeNew(buffID, instigatorID, oldValue, newValue, totalStartTimeStamp)
    local buffs = self.buffInstListNew[buffID]
    if not buffs then
        --self:ErrorFmt("OnMsgBuffTotalLifeChangeNew error, buff not exist, instigatorID:%s, buffId:%s", instigatorID, buffID)
        return
    end
    local buffInstListIndex = BuffComponentNew.getBuffIDSecondaryIndex(buffID, instigatorID)
    local buff = buffs[buffInstListIndex]
    if not buff then
        --self:ErrorFmt("OnMsgBuffTotalLifeChangeNew error, buff not exist, sourceUid:%s, buffId:%s", instigatorID, buffID)
        return
    end
    if buffInstListIndex == BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID then
        buff:ResetInstigatorID(instigatorID)
    end
    buff:OnTotalLifeChange(newValue, totalStartTimeStamp)
	Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.BATTLE_BUFF_TOTALLIFE_CHANGED, buff)
end

function BuffComponentNew:OnMsgBuffChangeLinkedTarget(buffID, instigatorID, linkTargetID)
    self:DebugFmt("OnMsgBuffChangeLinkedTarget, buffID: %s, instigatorID: %s, linkTargetID: %s",
    buffID, instigatorID)
    local buffs = self.buffInstListNew[buffID]
    if not buffs then
        return
    end
    local buffInstListIndex = BuffComponentNew.getBuffIDSecondaryIndex(buffID, instigatorID)
    local buff = buffs[buffInstListIndex]
    if buff == nil then
        return
    end
    if buffInstListIndex == BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID then
        buff:ResetInstigatorID(instigatorID)
    end
    buff:OnChangeLinkedTarget(linkTargetID)
end

function BuffComponentNew:OnMsgBuffLevelChange(buffID, instigatorID, oldValue, newValue)
	
end

function BuffComponentNew:GetBuffInstanceByBuffIDAndInstigatorID(buffID, instigatorID)
    local buffInst = self.buffInstListNew[buffID]
    if buffInst then
        local ins = buffInst[instigatorID]
        if ins then
            return ins
        end

        for key, val in pairs(buffInst) do
            if val.instigatorID == instigatorID then
                return val
            end
        end
    end
    
    return nil
end

function BuffComponentNew:GetBuffInstanceByBuffIDNew(buffID)
    local buffInst = self.buffInstListNew[buffID]
    return buffInst and buffInst[BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID]
    
end

function BuffComponentNew:GetBuffLayerNew(buffID, instigatorID)
    local buff = nil
    if instigatorID == nil then
        buff = self:GetBuffInstanceByBuffIDNew(buffID)
    else
        buff = self:GetBuffInstanceByBuffIDAndInstigatorID(buffID, instigatorID)
    end
    if buff then
        return buff:GetCurrentLayer()
    end
    return 0
end

function BuffComponentNew:GetBuffTotalLayer(buffID)
	local buffInstList = self.buffInstListNew[buffID]
	if not buffInstList then
		return 0
	end
	local layer = 0
	for _, buff in pairs(buffInstList) do
		layer = layer + buff:GetCurrentLayer()
	end
	return layer
end

function BuffComponentNew:GetBuffRunningLifeByInstigatorID(buffID, instigatorID)
    local buff = not instigatorID and self:GetBuffInstanceByBuffIDNew(buffID) or
        self:GetBuffInstanceByBuffIDAndInstigatorID(buffID, instigatorID)

    if not buff then
        return nil, nil
    end

    local buffTotalLife, buffRunningTime = buff:GetLifeMessage()
    if not (buffTotalLife and buffTotalLife > 0 and buffRunningTime and buffRunningTime > 0) then
        return nil, nil
    end

    buffRunningTime = math.max(math.min(buffRunningTime, buffTotalLife))
    return buffRunningTime, buffTotalLife - buffRunningTime
end

function BuffComponentNew:__component_RebuildAttr__(rebuildAttrInfo)
    if not rebuildAttrInfo.buffInfo then
        return
    end
	self.isRebuilding = true
    for _, buffInfo in ipairs(rebuildAttrInfo.buffInfo) do
        self:AddBuffNew(buffInfo.buffID, buffInfo.instigatorID, buffInfo.triggerID, buffInfo.level, buffInfo.layer, 
            buffInfo.maxLayer, buffInfo.totalLife, buffInfo.totalStartTimeStamp, buffInfo.linkedTargetId, buffInfo.rootSkillID)
    end
	self.isRebuilding = false
end

function BuffComponentNew:__component_AppendGamePlayDebugInfo__(debugInfos)
    -- BuffComponentNew
    table.insert(debugInfos, "<Title_Red>[BuffComponentNew]</>")
    if (self.buffInstListNew ~= nil) then
        for buffID, buffs in pairs(self.buffInstListNew) do
            for _, buff in pairs(buffs) do
                table.insert(debugInfos, string.format("<Text>BuffID: %s, CurBuffLayer: %s, MaxBuffLayer: %s, ToTalLife: %s, CurrentLife: %s </>",
                    buff.StaticData.ID, buff.layer, buff.maxLayer, buff:GetLifeMessage()))
            end
        end
    end
    table.insert(debugInfos, "")
end

---@public
---@param buffID number
---@return boolean
function BuffComponentNew:HasBuff(buffID)
    -- 可能是空table
    local buffs = self.buffInstListNew[buffID]
    return (buffs ~= nil) and (next(buffs) ~= nil)
end

function BuffComponentNew.getBuffIDSecondaryIndex(buffID, instigatorID)
    local buffTableData = Game.TableData.GetBuffDataNewRow(buffID)
	if not buffTableData then
		self:ErrorFmt("Buff TableData not exist, buffID: %s", buffID)
		return instigatorID
	end
    if buffTableData.IsCheckSource ~= true then
        return BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID
    end
    return instigatorID
end

function BuffComponentNew:HasBuffTag(tags)
	if tags == nil then
		return false
	end
	local hasBuffTagSet = {}
	for _, v in ksbcipairs(tags) do
		hasBuffTagSet[v] = true
	end
	for _, buffs in pairs(self.buffInstListNew) do
		if buffs and next(buffs) then
			local _, buffInst = next(buffs)
			local buffTableData = buffInst:GetBuffData()
			local buffTags = buffTableData.Tags

			if buffTags ~= nil then
				for _, v in ksbcipairs(buffTags) do
					if hasBuffTagSet[v] then
						return true -- 找到相同的元素
					end
				end
			end
		end
	end
	return false
end

function BuffComponentNew:OnMsgSyncTargetBuffDebugInfo(buffDebugInfoList)
    self.buffDebugInfoList = buffDebugInfoList
end

-- 拍照检测物体类型
BuffComponentNew.CheckContainObjType = BuffComponentNew.CheckContainObjType or {
	Monster = 1,
	Npc = 2,
	SceneActor = 3,
}

function BuffComponentNew:StartCheckContainObj(objType, objID, socketName)
	self.checkContainObjType = objType
	self.checkContainObjID = objID
	self.checkContainObjSocketName = socketName
	Game.ScreenShotUtil:RegisterExtraOnScreenCapturedCallback(self, "CheckContainObjOnScreenShot")
end

function BuffComponentNew:CheckContainObjOnScreenShot()
	if self.checkContainObjType == nil or self.checkContainObjID == nil then
		return
	end

	local contain = false
	if self.checkContainObjType == BuffComponentNew.CheckContainObjType.Monster then
		local NpcActors = Game.EntityManager:getEntitiesByType(EEntityType.NpcActor)
		if NpcActors then
			for key, entity in pairs(NpcActors) do
				if entity.NpcType == Enum.ENpcTypeData.Monster and entity.TemplateID == self.checkContainObjID then
					contain = UBSFunc.IsActorVisibleToCamera(entity.CharacterID, self.checkContainObjSocketName)
					if contain then
						break
					end
				end
			end
		end
	elseif self.checkContainObjType == BuffComponentNew.CheckContainObjType.Npc then
		local NpcActors = Game.EntityManager:getEntitiesByType(EEntityType.NpcActor)
		if NpcActors then
			for key, entity in pairs(NpcActors) do
				if entity.NpcType ~= Enum.ENpcTypeData.Monster and entity.TemplateID == self.checkContainObjID then
					contain = UBSFunc.IsActorVisibleToCamera(entity.CharacterID, self.checkContainObjSocketName)
					if contain then
						break
					end
				end
			end
		end
	elseif self.checkContainObjType == BuffComponentNew.CheckContainObjType.SceneActor then
		local sceneEntity = Game.EntityManager:getEntityByInsID(self.checkContainObjID)
		if sceneEntity then
			contain = UBSFunc.IsActorVisibleToCamera(sceneEntity.CharacterID, self.checkContainObjSocketName)
		end
	end

	if contain then
		self.remote:ReqCameraShotContainObj()
	end
end

function BuffComponentNew:StopCheckContainObj()
	Game.ScreenShotUtil:UnregisterExtraOnScreenCapturedCallback(Game.PlayerController, "CheckContainObjOnScreenShot")
end
