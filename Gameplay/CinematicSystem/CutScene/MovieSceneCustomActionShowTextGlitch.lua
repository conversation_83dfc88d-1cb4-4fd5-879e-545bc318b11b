local MovieSceneCustomActionBase = kg_require("Gameplay.CinematicSystem.CutScene.MovieSceneCustomActionBase")
---@class MovieSceneCustionShowTextGlitch:MovieSceneCustomActionBase
local MovieSceneCustionShowTextGlitch = DefineClass("MovieSceneCustionShowTextGlitch", MovieSceneCustomActionBase)

function MovieSceneCustionShowTextGlitch:SetUp(section)
    
end

function MovieSceneCustionShowTextGlitch:Execute(section, actor, hasExecuted, currentTime)
    if not self.bShow then
        local customData = section.CustomData
        if _G.StoryEditor then
            UI.ShowUIInEditor("BlackSubtitles_Panel")
        else
            UI.ShowUI("BlackSubtitles_Panel")
        end
        self.bShow = true
    end
end

function MovieSceneCustionShowTextGlitch:TearDown(section)
    if not self.bShow then return end
    UI.HideUI("BlackSubtitles_Panel")
    self.bShow = false
end

return MovieSceneCustionShowTextGlitch