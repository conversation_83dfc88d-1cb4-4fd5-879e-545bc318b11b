local UIComMediaPlayer = kg_require("Framework.KGFramework.KGUI.Component.MediaPlayer.UIComMediaPlayer")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESlateVisibility = import("ESlateVisibility")

---@class CutscenePlayVideoPanel : UIPanel
---@field mediaPath string bk2 url
---@field bInPreRoll boolean Pre Roll 阶段加载UI并且隐藏, 正式播放时需要显示UI
local CutscenePlayVideoPanel = DefineClass("CutscenePlayVideoPanel", UIPanel)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CutscenePlayVideoPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CutscenePlayVideoPanel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CutscenePlayVideoPanel:InitUIComponent()
    ---@type UIComMediaPlayer
    self.WBP_ComMediaPlayerCom = self:CreateComponent(self.view.WBP_ComMediaPlayer, UIComMediaPlayer)
end

---UI事件在这里注册，此处为自动生成
function CutscenePlayVideoPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CutscenePlayVideoPanel:InitUIView()
end

function CutscenePlayVideoPanel:OnRefresh(mediaPath, looping, currentTime, rate, bPreRoll)
	self.mediaPath = mediaPath

	self.WBP_ComMediaPlayerCom:Refresh(mediaPath, looping)
	self.WBP_ComMediaPlayerCom:SeekByTime(currentTime)
	self.WBP_ComMediaPlayerCom:SetRate(rate)
	
	if bPreRoll then
		self:SetVisible(false)
		self.bInPreRoll = true
	end

	if _G.StoryEditor then
		self.view.Bg_Black:SetVisibility(ESlateVisibility.Visible)
	end
end

function CutscenePlayVideoPanel:SeekByTime(time)
	if self.bInPreRoll then
		self:SetVisible(true)
		self.bInPreRoll = false
	end
	self.WBP_ComMediaPlayerCom:SeekByTime(time)
end

function CutscenePlayVideoPanel:SetRate(rate)
	if self.bInPreRoll then
		self:SetVisible(true)
		self.bInPreRoll = false
	end
	return self.WBP_ComMediaPlayerCom:SetRate(rate)
end

function CutscenePlayVideoPanel:Play()
	if self.bInPreRoll then
		self:SetVisible(true)
		self.bInPreRoll = false
	end
	return self.WBP_ComMediaPlayerCom:Play()
end

function CutscenePlayVideoPanel:Pause()
	if self.bInPreRoll then
		self:SetVisible(true)
		self.bInPreRoll = false
	end
	return self.WBP_ComMediaPlayerCom:Pause()
end

return CutscenePlayVideoPanel
