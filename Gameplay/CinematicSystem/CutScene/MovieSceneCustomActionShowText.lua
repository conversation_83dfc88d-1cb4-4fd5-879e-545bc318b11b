local MovieSceneCustomActionBase = kg_require("Gameplay.CinematicSystem.CutScene.MovieSceneCustomActionBase")
---@class MovieSceneCustomActionShowText : MovieSceneCustomActionBase
local MovieSceneCustomActionShowText = DefineClass("MovieSceneCustomActionShowText", MovieSceneCustomActionBase)
local StringConst = require("Data.Config.StringConst.StringConst")
function MovieSceneCustomActionShowText:SetUp(section)
end

function MovieSceneCustomActionShowText:Execute(section, actor, hasExecuted, currentTime)
    if not self.bShow then
        local customData = section.CustomData
        if _G.StoryEditor then
            UI.ShowUIInEditor("CutsceneShowText", customData.TextInEditor)
        else
            local Str = StringConst.Get(customData.StrConstID)
            UI.ShowUI("CutsceneShowText", Str)
        end
        self.bShow = true
    end
end

function MovieSceneCustomActionShowText:TearDown(section)
    if not self.bShow then return end
    UI.HideUI("CutsceneShowText")
    self.bShow = false
end

return MovieSceneCustomActionShowText
