---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/7/4 20:55
---
local MovieSceneCustomActionBase = kg_require("Gameplay.CinematicSystem.CutScene.MovieSceneCustomActionBase")

---播放带位置的音频
---@class MovieSceneCustomActionPlayAkAudio : MovieSceneCustomActionBase
local MovieSceneCustomActionPlayAkAudio = DefineClass("MovieSceneCustomActionPlayAkAudio", MovieSceneCustomActionBase)

function MovieSceneCustomActionPlayAkAudio:SetUp(section)
    self.playingID = 0
end

function MovieSceneCustomActionPlayAkAudio:Execute(section, actor, hasExecuted, currentTime)
    if hasExecuted then
        return
    end

    local akEvent = section.CustomData.AkEvent
    if akEvent then
        self.playingID = Game.AkAudioManager:PostEventOnActor(akEvent:GetN<PERSON>(), actor)
    end
end

function MovieSceneCustomActionPlayAkAudio:Tear<PERSON>own(section, actor)
    if self.playingID == 0 then
        return
    end

    Game.AkAudioManager:StopEvent(self.playingID, math.floor(section.CustomData.BlendTime * 1000))
end

return MovieSceneCustomActionPlayAkAudio
