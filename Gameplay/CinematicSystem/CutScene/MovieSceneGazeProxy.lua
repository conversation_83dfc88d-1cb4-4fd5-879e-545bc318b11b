local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")

local MovieSceneGazeProxy = {}

MovieSceneGazeProxy.GazeReason = "CutsceneGaze"
Enum.EGazeTypeMap.CUTSCENE = 99

function MovieSceneGazeProxy:Setup()
	self.GazeQueue = {}
	self.TempEntities = {} --- @type table<ActorBase>
end

function MovieSceneGazeProxy:StartCutsceneGaze(OwnerActorID, TargetActorID, BoneName, Offset)
	table.insert(self.GazeQueue, {OwnerActorID, TargetActorID, BoneName, Offset})
	if (#self.GazeQueue == 1) then
		local OwnerEntity = self:GetOrCreateEntityFromActorID(OwnerActorID)
		OwnerEntity.featureLayerLinkedTags[ViewAnimConst.ANIM_FEATURE_LAYER_TAGS] = nil
		OwnerEntity:StartGazeActor(Enum.EGazeTypeMap.DIALOGUE, TargetActorID, BoneName, Offset)
	end
end

function MovieSceneGazeProxy:StopCutsceneGaze(OwnerActorID, TargetActorID, BoneName, Offset)
	local GazeNumber = #self.GazeQueue
	for i = 1, GazeNumber do
		local Gaze = self.GazeQueue[i]
		if (Gaze[1] == OwnerActorID and Gaze[2] == TargetActorID and Gaze[3] == BoneName) then
			table.remove(self.GazeQueue, i)
			break
		end
	end

	if #self.GazeQueue == 0 then
		local OwnerEntity = self:GetOrCreateEntityFromActorID(OwnerActorID)
		OwnerEntity:UnGaze(Enum.EGazeTypeMap.DIALOGUE)
		for _, Entity in pairs(self.TempEntities) do
			Entity:destroy()
		end
		self.TempEntities = {}
	else
		local OwnerEntity = self:GetOrCreateEntityFromActorID(OwnerActorID)
		OwnerEntity.featureLayerLinkedTags[ViewAnimConst.ANIM_FEATURE_LAYER_TAGS] = nil
		OwnerEntity:StartGazeActor(Enum.EGazeTypeMap.DIALOGUE, TargetActorID, BoneName, Offset)
	end
end

---@param InActorID number
---@return ActorBase
function MovieSceneGazeProxy:GetOrCreateEntityFromActorID(InActorID)
	local Entity = Game.UEActorManager:GetLuaEntityByActorID(InActorID) ---@type ActorBase
	if not Entity or not Entity.bInWorld then
		Entity = Game.EntityManager:CreateLocalEntity(
			"LocalCutSceneWrapActor", {
				PreCreateCharacterID = InActorID,
			})
		table.insert(self.TempEntities, Entity)
	end
	
	if Entity.ViewRoleImportance ~= nil and not Entity.ViewDowngradingTokenMapping then
		local downgradingBudgetPreset = WorldViewBudgetConst.DOWNGRADING_PRESET_FROM_IMPORTANCE[Entity.ViewRoleImportance]
		Entity:TryRequestViewDownGradingBudgetTokenBatch(downgradingBudgetPreset[1], downgradingBudgetPreset[2])
	end
	
	return Entity
end

return Class(nil,nil,MovieSceneGazeProxy)
