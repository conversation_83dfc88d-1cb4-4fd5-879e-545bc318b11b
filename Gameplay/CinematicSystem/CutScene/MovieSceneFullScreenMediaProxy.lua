---@class MovieSceneFullScreenMediaProxy 
local MovieSceneFullScreenMediaProxy = {}

function MovieSceneFullScreenMediaProxy:SetUp(url, bLooping)
	self.url = url:gsub("\\", "/"):match("Movies/.*") 
	--Log.Debug("MovieSceneFullScreenMediaProxy:SetUp", url, bLooping)
	self.bLooping = bLooping
	self.rate = 1
	
	--- Pre Roll 用于预热. 加载UI并且隐藏, 同时Seek至0的位置, Pause.
	self.bInPreRoll = false
end

function MovieSceneFullScreenMediaProxy:TearDown()
	--Log.Debug("MovieSceneFullScreenMediaProxy:TearDown", self.url)
	UI.HideUI("CutscenePlayVideoPanel")
	self.VideoPlayerUI = nil
	self.bInPreRoll = false
end

function MovieSceneFullScreenMediaProxy:Seek(currentTime)
	--Log.Debug("MovieSceneFullScreenMediaProxy:Seek", self.url, currentTime)
	self.bInPreRoll = false
    if not self.VideoPlayerUI or self.VideoPlayerUI.isDestroyed then
		self.VideoPlayerUI = UI.GetUI("CutscenePlayVideoPanel")
		if not self.VideoPlayerUI then
			UI.ShowUI("CutscenePlayVideoPanel", self.url, self.bLooping, currentTime, self.rate, false)
			self.VideoPlayerUI = UI.GetUI("CutscenePlayVideoPanel")
		end
    end

	if self.VideoPlayerUI and not self.VideoPlayerUI.isDestroyed then
		self.VideoPlayerUI:SeekByTime(currentTime)
	end
end

function MovieSceneFullScreenMediaProxy:SetRate(rate)
	--Log.Debug("MovieSceneFullScreenMediaProxy:SetRate", self.url, rate)
	self.rate = rate
	if not self.VideoPlayerUI or self.VideoPlayerUI.isDestroyed then
		self.VideoPlayerUI = UI.GetUI("CutscenePlayVideoPanel")
	end
	if self.VideoPlayerUI and not self.VideoPlayerUI.isDestroyed then
		self.VideoPlayerUI:SetRate(rate)
	end
end

function MovieSceneFullScreenMediaProxy:PreRoll()
	--- 只需要在PreRoll的第一帧预热
	if self.bInPreRoll then
		return
	end
	
	self.bInPreRoll = true
	if not self.VideoPlayerUI or self.VideoPlayerUI.isDestroyed then
		self.VideoPlayerUI = UI.GetUI("CutscenePlayVideoPanel")
		if not self.VideoPlayerUI then
			UI.ShowUI("CutscenePlayVideoPanel", self.url, self.bLooping, 0, 0, true)
		end
	end

	--- Pre Roll阶段并不会Tear Down, 所以需要手动隐藏一下UI
	if _G.StoryEditor then
		if self.VideoPlayerUI then
			if self.VideoPlayerUI.mediaPath == self.url then
				UI.HideUI("CutscenePlayVideoPanel")
				self.bInPreRoll = false
				self.VideoPlayerUI = nil
			end
		end
	end
end

return Class(nil, nil, MovieSceneFullScreenMediaProxy)
