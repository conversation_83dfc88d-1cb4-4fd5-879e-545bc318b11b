local CutsceneQTEManager = DefineClass("CutsceneQTEManager")

function CutsceneQTEManager:c<PERSON>(Director)
    self.Director = Director
    self.QTEMgr = nil
    self.QTEObject = nil
    self.QteResult = false
    self.FailedJumpFrame = 0
    self.ExtraUIPath = nil
    self.ExtraUI = nil
    self.JustStopWhenSectionEnd = nil
    self.QTEBlkBdValChanged = LuaMulticastDelegate.new()
    self.QTEEndEvent = LuaMulticastDelegate.new()
end

function CutsceneQTEManager:dtor()
    self.QTEBlkBdValChanged:Clear()
    self.QTEEndEvent:Clear()
end

function CutsceneQTEManager:StartQTESection(QTEData, FailedJumpFrame, ExtraUIPath, ExtraData, JustStopWhenSectionEnd)
    self.QTEMgr = Game.BSQTEManager
    self.FailedJumpFrame = FailedJumpFrame
    self.JustStopWhenSectionEnd = JustStopWhenSectionEnd
    if (self.QTEMgr == nil) then
        Log.DebugError("StartQTESection Failed when Get UBSQTEManager!!!")
        return
    end
    self.QteResult = false
    self.QTEObject = self.QTEMgr:AddNewQTE(QTEData)
    if (self.QTEObject ~= nil) and (self.QTEObject.bActive == true) then
        self.QTEObject.QTESucessed:Add(self, "OnQTESucessed")
        self.QTEObject.QTEFailed:Add(self, "OnQTEFailed")
        self.QTEObject.QTEBlkBdValChanged:Add(self, "OnQTEBlkBdValChanged")
    end
    if ExtraUIPath ~= "" then
        self.ExtraUIPath = ExtraUIPath
        self.ExtraUI = UI.ShowUI(ExtraUIPath, ExtraData:ToTable())
    end
end

function CutsceneQTEManager:StopQTESection()
    if not self.Director then
        return
    end
    self:OnQteEnd()
    if self.FailedJumpFrame > 0 then
        if self.QteResult then
			Game.CinematicManager.CutsceneManager:SetFrameRange(0, self.FailedJumpFrame)
        else
			Game.CinematicManager.CutsceneManager:CutSceneJumpToFrame(self.FailedJumpFrame)
        end
    end
end

function CutsceneQTEManager:OnQteEnd()
    self.QTEEndEvent:Broadcast()
    self:CloseQteUI()
end

function CutsceneQTEManager:CloseQteUI()
    if (self.QTEObject ~= nil) and (self.QTEObject.bActive == true) then
        self.QTEObject.QTESucessed:Clear()
        self.QTEObject.QTEFailed:Clear()
        self.QTEObject.QTEBlkBdValChanged:Clear()

        if (self.QTEMgr ~= nil) then
            self.QTEMgr:RemoveQTE(self.QTEObject)
        end
    end
    if self.ExtraUIPath and self.ExtraUIPath ~= "" then
        UI.HideUI(self.ExtraUIPath)
        self.ExtraUIPath = nil
        self.ExtraUI = nil
    end
end

function CutsceneQTEManager:OnQTESucessed()
    Log.Debug("MovieSceneQTETrack OnQTESucessed ")
    self.QteResult = true
    if not self.JustStopWhenSectionEnd then
        self:OnQteEnd()
    end
end

function CutsceneQTEManager:OnQTEFailed()
    self.QteResult = false
    Log.Debug("MovieSceneQTETrack OnQTEFailed")
    if not self.JustStopWhenSectionEnd then
        self:OnQteEnd()
    end
end

function CutsceneQTEManager:GetBlackBoardValue(InValueName)
    if self.QTEObject == nil then
        return
    end
    if InValueName == nil then
        InValueName = "DefaultValue"
    end
    return self.QTEObject:GetBlackBoardRecentValue(InValueName)
end

function CutsceneQTEManager:OnQTEBlkBdValChanged(name, value)
    self.QTEBlkBdValChanged:Broadcast(name, value)
end

return CutsceneQTEManager
