local LevelSequenceManager = DefineClass("LevelSequenceManager")
local UC7FunctionLibrary = import("C7FunctionLibrary")
local EPropertyClass = import("EPropertyClass")
local EMoveDriveMode = import("EMoveDriveMode")
local WEAK_FORCE_CONTROL_REASON_TAGS = kg_require("Shared.Const.ParallelBehaviorControlConst").WEAK_FORCE_CONTROL_REASON_TAGS
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")

function LevelSequenceManager:ctor()
    self.CurrentLevelID = 0
	self.currentTaskHandle = 0
	self.CurrentPlaneID = 0
end

function LevelSequenceManager:dtor()

end

function LevelSequenceManager:Init()
    self:RegisterEvent()
end

function LevelSequenceManager:RegisterEvent()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_LEVEL_LOAD_START, "OnLevelLoadStart", self)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.PLANE_ON_CHANGE, "OnPlaneChange", self)
end

function LevelSequenceManager:UnInit()
    self:UnRegisterEvent()
end

function LevelSequenceManager:UnRegisterEvent()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function LevelSequenceManager:OnLevelLoadStart()
	local levelSequenceTasks = Game.SequenceManager:GetPlayTasksByCinematicType(Enum.CinematicType.LevelSequence)
	for _, task in pairs(levelSequenceTasks) do
		if task then
			Game.SequenceManager:Terminate(task:GetLoadHandleID())
		end
	end
	
    self.CurrentLevelID = self.CurrentLevelID + 1
end

function LevelSequenceManager:OnPlaneChange()
	Log.Debug("OnPlaneChange OnPlaneChange")
	local levelSequenceTasks = Game.SequenceManager:GetPlayTasksByCinematicType(Enum.CinematicType.LevelSequence)
	for _, task in pairs(levelSequenceTasks) do
		if task then
			Game.SequenceManager:Terminate(task:GetLoadHandleID())
		end
	end
	self.CurrentPlaneID = self.CurrentPlaneID + 1
end


function LevelSequenceManager:PlayLevelSequence(Params)
    local SequenceData = Game.TableData.GetLevelSequenceDataRow(Params.AssetID)
    if not SequenceData then
        Log.WarningFormat("[Enter] no LevelSequenceData for %s", Params.AssetID)
        if Params.OnCinematicFinished then
            Params.OnCinematicFinished()
        end
        return
    end
	local TargetTask = Game.SequenceManager:FindPlayTaskByJobID(Params.JobID)
	if TargetTask then
		if Params.AssetID ~= TargetTask:GetAssetID() then
			Game.SequenceManager:Terminate(TargetTask:GetLoadHandleID())
		else
			TargetTask:SetPlayParams(Params)
			self:SetPlaybackParams(TargetTask:GetLoadHandleID(), Params)
			return
		end
	end
	Params.levelID = self.CurrentLevelID
	Params.planeID = self.CurrentPlaneID
	Params.CinematicType = Enum.CinematicType.LevelSequence
	Params.autoOffsetMode = SequenceData.AutoOffsetMode
	Params.bDisableCameraCuts = true
	--Params.bPauseAtEnd = true
	Params.OnSequencePlayCallBack = function(task)
		self:OnPlayerPlayCallback(task)
	end
	Params.OnSequencePauseCallBack = function(loadHandleID)
		self:OnPlayerPauseCallback(loadHandleID)
	end
	Params.OnSequenceFinishedCallBack = function(task)
		self:OnPlayerFinishedCallback(task)
	end
	Params.OnStartPlayInternalCallBack = function(loadHandleID)
		self:StartPlayInternal(loadHandleID)
	end
	Params.OnLogicStopCallBack = function(loadHandleID)
		self:OnPlayerStopCallback(loadHandleID)
	end
	Params.OnLogicFinished = function(loadHandleID)
		self:OnLogicFinished(loadHandleID)
	end
	Params.OnSubSequencePlayCallBack = function(task, sequenceid)
		self:OnSubSequencePlay(task, sequenceid)
	end

	self.currentTaskHandle = Game.SequenceManager:PlaySequence(Params)
	
end

function LevelSequenceManager:StopLevelSequence(Params)
    local StopTask
    if Params.JobID then
        StopTask = Game.SequenceManager:FindPlayTaskByJobID(Params.JobID)
    else
        StopTask = Game.SequenceManager:FindPlayTaskByTypeAndAssetID(Enum.CinematicType.LevelSequence, Params.AssetID)
    end
    if StopTask then
        Game.SequenceManager:Terminate(StopTask:GetLoadHandleID())
    end
end

function LevelSequenceManager:OnPlayerPlayCallback(Task)
	Log.Debug("LevelSequenceManager OnPlayerPlayCallback")
	if Task then
		local playParams = Task:GetPlayParams()
		if playParams and playParams.OnCinematicPlay then
			playParams.OnCinematicPlay()
		end
	end
end

function LevelSequenceManager:OnPlayerFinishedCallback(Task)
	Log.Debug("LevelSequenceManager OnPlayerFinishedCallback")
	if Task then
		local playParams = Task:GetPlayParams()
		if playParams and playParams.OnCinematicFinished then
			playParams.OnCinematicFinished()
		end
	end
	self.currentTaskHandle = 0
end

function LevelSequenceManager:OnLogicFinished(loadHandleID)
	Log.Debug("LevelSequenceManager OnLogicFinished")
	self.currentTaskHandle = 0
end

function LevelSequenceManager:OnSubSequencePlay(task, sequenceId)
	Log.Debug("[LevelSequenceManager] OnSubSequencePlay sequenceId= ", sequenceId)
end

function LevelSequenceManager:OnPlayerPauseCallback(loadHandleID)
	local currenTask = Game.SequenceManager:GetPlayTaskByLoadID(loadHandleID)
	if not currenTask then
		Log.Warning("[LevelSequenceManager] OnPlayerPauseCallback Failed Task Is nil")
		return
	end

	--- CutScene LevelSequence层的回调
	local playParams = currenTask:GetPlayParams()
	if playParams and playParams.OnCinematicPause then
		playParams.OnCinematicPause()
	end
end

function LevelSequenceManager:OnPlayerStopCallback(loadHandleID)
	Log.Debug("LevelSequenceManager OnPlayerStopCallback loadHandleID: ", loadHandleID)
	self.currentTaskHandle = 0
end


function LevelSequenceManager:StartPlayInternal(loadHandleID)
	Log.Debug("LevelSequenceManager OnPlayerStopCallback loadHandleID: ", loadHandleID)
	local task = Game.SequenceManager:GetPlayTaskByLoadID(loadHandleID)
	if not task then
		Log.Warning("LevelSequenceManager OnPlayerStopCallback Invalid Task loadHandleID: ", loadHandleID)
		return
	end
	local levelID = task.levelID
	if levelID ~= self.CurrentLevelID then
		Log.WarningFormat("[LevelSequenceManager] StartPlayInternal level sequence load in pre level but play in next level")
		Game.SequenceManager:Skip(loadHandleID)
		return
	end
	
	local PlayParams = task:GetPlayParams()
	if (PlayParams.StartMark) == nil or (PlayParams.EndMark == nil) then
		PlayParams.StartFrame = self:getRealStartFrame(loadHandleID)
	end
	self:SetPlaybackParams(loadHandleID, PlayParams)
	if not (task:IsLevelSequenceAutoOffset() or task:IsLevelSequenceServerSetTransform()) then
		self:UpdateLevelSequenceActorPostion(loadHandleID)
		self:ChangeSequenceTransformOriginActor(loadHandleID)
	end
end

-- 计算实际开始播放的时间,暂时不支持通过Mark控制的情况
---@param asset table
---@param playParams table
function LevelSequenceManager:getRealStartFrame(loadHandleID)
	local Task = Game.SequenceManager:GetPlayTaskByLoadID(loadHandleID)
	if not Task then
		Log.WarningFormat("[LevelSequenceManager] getRealStartFrame Invalid Task HandleID: ", loadHandleID)
		return 0
	end
	local playParams = Task:GetPlayParams()
	local rootAsset = Task:GetRootSequenceAsset()
	if not rootAsset or rootAsset == 0 then
		return playParams.StartFrame
	end
	
    if not playParams.StartTimeStamp then
        return playParams.StartFrame
    end

    if (playParams.StartFrame == nil) or (playParams.EndFrame == nil) then
        return playParams.StartFrame
    end

    local cTimestamp = _G._now(1)
    local timePast = cTimestamp - playParams.StartTimeStamp
    local frameRate = Game.SequenceManager:KAPI_GetMovieSceneDisplayRate(loadHandleID)
    local playRate = playParams.PlayRate or 1
    local realStartFrame = math.floor(timePast * frameRate * playRate)
    if realStartFrame >= playParams.EndFrame then
        realStartFrame = playParams.EndFrame
    end
    return realStartFrame
end

function LevelSequenceManager:SetPlaybackParams(loadHandleID, PlayParams)
	if PlayParams.StartFrame then
		Log.Debug("[LevelSequenceManager] SetPlaybackParams PlayParams.StartFrame: ", PlayParams.StartFrame)
		Game.SequenceManager:KAPI_LevelSequenceJumpToFrame(loadHandleID, PlayParams.StartFrame)
	elseif PlayParams.StartMark then
		Log.Debug("[LevelSequenceManager] SetPlaybackParams PlayParams.StartMark: ", PlayParams.StartMark)
		Game.SequenceManager:KAPI_LevelSequenceJumpToMark(loadHandleID, PlayParams.StartMark)
	else
		Game.SequenceManager:KAPI_LevelSequenceJumpToStart(loadHandleID)
	end
	if PlayParams.EndFrame then
		Log.Debug("[LevelSequenceManager] SetPlaybackParams PlayParams.EndFrame: ", PlayParams.EndFrame)
		Game.SequenceManager:KAPI_LevelSequencePlayToFrame(loadHandleID, PlayParams.EndFrame)
	elseif PlayParams.EndMark then
		Log.Debug("[LevelSequenceManager] SetPlaybackParams PlayParams.EndMark: ", PlayParams.EndMark)
		Game.SequenceManager:KAPI_LevelSequencePlayToMark(loadHandleID, PlayParams.EndMark)
	else
		Game.SequenceManager:KAPI_LevelSequencePlayToEnd(loadHandleID)
	end
	Log.Debug("[LevelSequenceManager] SetPlaybackParams SetPlayRate ", PlayParams.PlayRate or 1)
	Game.SequenceManager:KAPI_SetPlayRate(loadHandleID, PlayParams.PlayRate or 1)
end

function LevelSequenceManager:UpdateLevelSequenceActorPostion(LoadID)
	local Task = Game.SequenceManager:GetPlayTaskByLoadID(LoadID)
	if not Task then
		return
	end
    local PlayParams = Task:GetPlayParams()
    local Transform = PlayParams and PlayParams.Transform
    local Position = Transform and Transform.Position
    local Rotator = Transform and Transform.Rotator
	if not Position then
		Position = FVector()
	end
	if not Rotator then
		Rotator = FRotator()
	end
	LuaScriptAPI.KAPI_Actor_SetLocation(Task:GetDirector(), Position)
	LuaScriptAPI.KAPI_Actor_SetRotation(Task:GetDirector(), Rotator)
end

function LevelSequenceManager:ChangeSequenceTransformOriginActor(LoadID)
	local Task = Game.SequenceManager:GetPlayTaskByLoadID(LoadID)
	if not Task then
		return
	end
	local PlayParams = Task:GetPlayParams()
    if PlayParams.bUseTransformOriginActor == true then
		Game.SequenceManager:KAPI_ChangeSequenceTransformOriginActor(LoadID, Task:GetDirector())
    end
end

return LevelSequenceManager
