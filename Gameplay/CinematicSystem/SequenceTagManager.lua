---@class SequenceTagManager
local SequenceTagManager = DefineClass("SequenceTagManager")
local EntityUtils = kg_require("Gameplay.NetEntities.EntityUtils")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")

function SequenceTagManager:ctor(loadHandle, params)

end

function SequenceTagManager:dtor()

end

--- 执行绑定Tag逻辑
---1. 分析所有需要绑定的Tag 区分类型 真假绑定 获取InsID
---（1） 分析需要自定义绑定的Tag注册
---（2） 分析需要走默认绑定的Tag注册

---2. 准备演员阶段 创建成功或者失败都要通知回Task
---@param task SequenceLoadTask
function SequenceTagManager:ExecutePrepareBindingTags(task)
	-- @lizhang, todo, 这里需要明确知道Custom阶段，执行过哪些绑定，怎么做？这里 return了，默认的tag处理不是都失效了？@mengdi
	local CustomTagManager = task:GetCustomTagManager()
	if CustomTagManager then
		Log.Debug("[SequenceTagManager] Task CustomTagManager ExcutePrepareTags")
		CustomTagManager:CustomExcutePrepareTags(task)
		return
	end
	local bindingObjectTemplateArray = task:GetBindingObjectTemplateArray()
	if not bindingObjectTemplateArray then
		Log.Warning("[SequenceTagManager] AnalyzeTag End Invalid BindingObjectTemplateArray")
		return
	end

	local PlayerBindingTemplate = {}
	local NPCBindingTemplate = {}
	local ObjectBindingTemplate = {}
	local AttachmentBindingTemplate = {}
	local CacheBindingIDTable = {}		-- 同个轨道的tag
	---------------------------Tag分析开始--------------------------------
	--- 开始自定义Tag绑定信息注册
	self:CustomRegisterBinding(task)
	
	--- Sequence没有Tag标签则直接跳过绑定流程走检查到下一步
	if bindingObjectTemplateArray and #bindingObjectTemplateArray == 0 then
		task:CheckTagCharactorReady()
		Log.Warning("[SequenceTagManager] bindingObjectTemplateArray is Invalid Or Num is 0 ")
		return
	end
	-- 先按照轨道区分Tag
	for _, BindingTemplate in pairs(bindingObjectTemplateArray) do
		local CacheTableKey = string.format("%d_%d", BindingTemplate.SequenceID, BindingTemplate.Guid.A)
		if not CacheBindingIDTable[CacheTableKey] then
			CacheBindingIDTable[CacheTableKey] = {}
		end
		Log.DebugFormat("[SequenceTagManager] Sequence Source Tag:  %s ", BindingTemplate.BindingTag)
		table.insert(CacheBindingIDTable[CacheTableKey], BindingTemplate)
	end
	--同个轨道的取优先级最高的tag
	for _, BindingTemplateLst in pairs(CacheBindingIDTable)do
		local CacheBindingTemplate = self:GetHighestLevelTagInSameTrack(BindingTemplateLst)
		local tag = string.lower(CacheBindingTemplate.BindingTag)
		if tag ~= "texture_prestream" then
			if not self:IsBindingTemplateAlreadyInCustomBindTable(task, CacheBindingTemplate) then
				local TagType, _, _ = self:AnalyzeTag(tag)
				if TagType == Enum.SequenceBindActorType.Player then
					task:RegisterBindingTemplate(CacheBindingTemplate)
					table.insert(PlayerBindingTemplate, CacheBindingTemplate)
				elseif TagType == Enum.SequenceBindActorType.NPC then
					task:RegisterBindingTemplate(CacheBindingTemplate)
					table.insert(NPCBindingTemplate, CacheBindingTemplate)
				elseif TagType == Enum.SequenceBindActorType.Object then
					task:RegisterBindingTemplate(CacheBindingTemplate)
					table.insert(ObjectBindingTemplate, CacheBindingTemplate)
				elseif TagType == Enum.SequenceBindActorType.PlayerAttachment then
					if self:IsTagOppositeWithPlayer(tag) then
						task:RegisterBindingTemplate(CacheBindingTemplate, true)
						table.insert(AttachmentBindingTemplate, CacheBindingTemplate)
					end
				elseif self:TagIsAnchor(tag) then
					if task:IsLevelSequenceServerSetTransform() and task:GetServerSetModeIsAnchor() then
						self:DoServerSequenceActorAnchorOffsetPostion(task, CacheBindingTemplate.Guid)
					end
				end
			end

			self:SetPreStreamTextureBindingStatus(task, CacheBindingTemplate, Enum.SequencePreStreamTextureStatus.WaitForBinding)
		end
	end
	if task:GetRegisterTabNum() <= 0 then
		task:CheckTagCharactorReady()
		return
	end
	---------------------------Tag分析结束--------------------------------
	
	--------------------------- 准备表演Actor开始--------------------------------
	--- 开始通用绑定Actor创建
	for _,playerBindingTemplate in pairs(PlayerBindingTemplate)do
		Log.DebugFormat("[SequenceTagManager] Index PlayerBindingTemplate Tag:  %s ", playerBindingTemplate.BindingTag)
		self:ExcuteBindPlayer(task, playerBindingTemplate, function(EntityID, Tag, BindingID)
			return self:LoadActorFinish(EntityID, Tag, playerBindingTemplate, task)
		end)
	end
	for _,npcBindingTemplate in pairs(NPCBindingTemplate)do
		Log.DebugFormat("[SequenceTagManager] Index NPCBindingTemplate Tag:  %s ", npcBindingTemplate.BindingTag)
		self:ExcuteBindNPC(task, npcBindingTemplate, function(EntityID, Tag, BindingID)
			return self:LoadActorFinish(EntityID, Tag, npcBindingTemplate, task)
		end)
	end
	for _,objectBindingTemplate in pairs(ObjectBindingTemplate)do
		Log.DebugFormat("[SequenceTagManager] Index ObjectBindingTemplate Tag:  %s ", objectBindingTemplate.BindingTag)
		self:ExcuteBindObject(task, objectBindingTemplate, function(EntityID, Tag, BindingID)
			return self:LoadActorFinish(EntityID, Tag, objectBindingTemplate, task)
		end)
	end
	for _,objectBindingTemplate in pairs(AttachmentBindingTemplate)do
		Log.DebugFormat("[SequenceTagManager] Index AttachmentBindingTemplate Tag:  %s ", objectBindingTemplate.BindingTag)
		self:ExecuteBindPlayerAttachment(task, objectBindingTemplate, function(EntityID, Tag, BindingID)
			return self:LoadActorFinish(EntityID, Tag, objectBindingTemplate, task)
		end)
	end
	--- 开始自定义绑定Actor创建
	self:CustomExcuteBindObject(task)
	--------------------------- 准备表演Actor结束--------------------------------
end

-- @lizhang, 设置binding的prestreamtexture状态，
-- 如果status是TryPreStream，则立即尝试设置actor的prestream，
-- 如果此时entityID有值，则设置此entity，如无，则尝试通过sequence寻找绑定对象，找不到则不切换状态
function SequenceTagManager:SetPreStreamTextureBindingStatus(task, bindingTemplate, status, objectID)
	local PreStreamTextureBindings = task.PreStreamTextureBindings
	if PreStreamTextureBindings[bindingTemplate] and 
		( PreStreamTextureBindings[bindingTemplate] ~= Enum.SequencePreStreamTextureStatus.PreStreamed ) then

		PreStreamTextureBindings[bindingTemplate] = status 
		if status == Enum.SequencePreStreamTextureStatus.TryPreStream then
			if not objectID then
				objectID = Game.SequenceManager:KAPI_GetBindingObjectIDByBindingID(task:GetLoadHandleID(), bindingTemplate.Guid)
			end

			if objectID ~= KG_INVALID_ID then
				LuaScriptAPI.KAPI_Actor_PreStreamTextures(objectID, Game.SequenceManager.PRE_STREAM_TEXTURES_SECONDS, true, 0)
				PreStreamTextureBindings[bindingTemplate] = Enum.SequencePreStreamTextureStatus.PreStreamed
			end
		end
	end
end

-- @lizhang, 处理完binding tag之后，处理另外的底层tag（eg. texture_prestream)
function SequenceTagManager:ExecutePrepareLowLevelTags(task)
	for bindingTemplate, status in pairs(task.PreStreamTextureBindings) do
		-- all tagged binding are handled in ExecutePrepareBindingTags phase, will check later in load actor finish or fail
		-- so only need set status from Unhandled to TryPreStream here
		if status == Enum.SequencePreStreamTextureStatus.Unhandled then
			self:SetPreStreamTextureBindingStatus(task, bindingTemplate, Enum.SequencePreStreamTextureStatus.TryPreStream)
		end
	end
end

function SequenceTagManager:TryPreStreamTexturesForAllUnhandledBindings(task)
	for bindingTemplate, status in pairs(task.PreStreamTextureBindings) do
		if status ~= Enum.SequencePreStreamTextureStatus.PreStreamed then
			self:SetPreStreamTextureBindingStatus(task, bindingTemplate, Enum.SequencePreStreamTextureStatus.TryPreStream)
		end
	end
end

-- @lizhang, 总的PrepareTags阶段，先分拆成一个binding tag处理，一个lowlevel tag处理
function SequenceTagManager:ExcutePrepareTags(task)
	self:ExecutePrepareBindingTags(task)
	self:ExecutePrepareLowLevelTags(task)
end

--- 支持上层业务自定义绑定标签注册
---@param SequenceLoadTask
function SequenceTagManager:CustomRegisterBinding(task)
	local CustomTagTable = task:GetCustomTagTable()
	if not CustomTagTable then
		return
	end
	for tag, _ in pairs(CustomTagTable) do
		local BindingTemplate = task:GetBindingTemplateByTag(tag)
		if BindingTemplate then
			task:RegisterBindingTemplate(BindingTemplate, true)
		end
	end
end

--- 支持上层业务自定义绑定标签注册
function SequenceTagManager:IsBindingTemplateAlreadyInCustomBindTable(Task, CacheBindingTemplate)
	local CustomTagTable = Task:GetCustomTagTable()
	if not CustomTagTable then
		return false
	end

	if CustomTagTable[CacheBindingTemplate.BindingTag] ~= nil then
		return true
	end
	
	for tag, _ in pairs(CustomTagTable) do
		if string.lower(tag) == string.lower(CacheBindingTemplate.BindingTag) then
			return true
		end
	end
	
	return false
end

--- 支持上层业务自定义绑定Sequence对象
function SequenceTagManager:CustomExcuteBindObject(task)
	local CustomTagTable = task:GetCustomTagTable()
	if not CustomTagTable then
		return
	end

	for tag, InfoTable in pairs(CustomTagTable) do
		local BindingTemplate = task:GetBindingTemplateByTag(tag)
		if BindingTemplate then
			local Entity = Game.EntityManager:getEntity(InfoTable.EntityID)
			if Entity and Entity.bInWorld then
				task:OnTagEntityCreateFinish(BindingTemplate, true, InfoTable.EntityID)
				task:OnTagActorLoadFinish(BindingTemplate, Entity.CharacterID)
			else
				if InfoTable.CharacterID and InfoTable.CharacterID ~= 0 then
					task:OnTagEntityCreateFinish(BindingTemplate, true, InfoTable.EntityID)
					task:OnTagActorLoadFinish(BindingTemplate, InfoTable.CharacterID)
				else
					task:OnTagActorLoadFailed(BindingTemplate)
				end
			end
		end
	end
end

--- 在所有Actor都创建完成后 开始绑定每一个Actor到Sequence
---@param task SequenceLoadTask
function SequenceTagManager:ExcuteBindTags(task)
	if not task or task:GetDirector() == nil then
		return
	end
	for _, subSequenceArry in pairs(task:GetBindTagActorTable()) do
		for _, tagInfo in pairs(subSequenceArry) do
			if tagInfo then
				local Entity = Game.EntityManager:getEntity(tagInfo.EntityID)
				local tag = string.lower(tagInfo.BindingTemplate.BindingTag)
				local TagType, IsRealBind, _ = self:AnalyzeTag(tag)
				if Entity and Entity.bInWorld then
					if IsRealBind then
						self:OnInstanceEntityEnterSequence(TagType, Entity)
					end
					Game.SequenceManager:KAPI_SetBinding(task:GetLoadHandleID(), tagInfo.CharacterID, tagInfo.BindingTemplate.Guid, tagInfo.BindingTemplate.SequenceID)
					Log.DebugFormat("[SequenceTagManager] ExcuteBindTags BindingID: %s BindingTag: %s Charactor: %s",tagInfo.BindingTemplate.Guid.A, tagInfo.BindingTemplate.BindingTag, tagInfo.CharacterID)
				else
					if tagInfo.EntityID and tagInfo.EntityID == task:GetOtherGenderTag() then
						Game.SequenceManager:KAPI_SetBinding(task:GetLoadHandleID(), task:GetOtherGenderEmptyActorID(), tagInfo.BindingTemplate.Guid, tagInfo.BindingTemplate.SequenceID)
					elseif tagInfo.IsCustomBind and tagInfo.CharacterID and tagInfo.CharacterID ~= 0 then
						Game.SequenceManager:KAPI_SetBinding(task:GetLoadHandleID(), tagInfo.CharacterID, tagInfo.BindingTemplate.Guid, tagInfo.BindingTemplate.SequenceID)
					end
				end
			end
		end
	end
end

--- 播放结束后 解绑各个Actor
function SequenceTagManager:ExcuteUnBindTags(task)
	if not task  then
		return
	end
	for _, subSequenceArry in pairs(task.tagActorTable) do
		for _, tagInfo in pairs(subSequenceArry) do
			if tagInfo and tagInfo.CharacterID and tagInfo.CharacterID ~= 0 then
				local tag = string.lower(tagInfo.BindingTemplate.BindingTag)
				local TagType, IsRealBind, _ = self:AnalyzeTag(tag)
				local Entity = Game.EntityManager:getEntity(tagInfo.EntityID)
				if Entity and Entity.bInWorld then
					if IsRealBind then
						self:OnInstanceEntityExitSequence(TagType, Entity)
					end
					Game.SequenceManager:KAPI_RemoveBinding(task:GetLoadHandleID(), tagInfo.CharacterID, tagInfo.BindingTemplate.Guid, tagInfo.BindingTemplate.SequenceID)
				end
			end
		end
	end
end

--- 通用Tag规则解析 区分绑定类型 真假绑定 InsID
function SequenceTagManager:AnalyzeTag(tag)
	tag = string.lower(tag)
	local TagType = nil
	local IsRealBind = false
	local MutableID = nil
	if self:TagIsAttachment(tag) then
		TagType = Enum.SequenceBindActorType.PlayerAttachment
		MutableID = self:GetTagBindingObjectRelatedID(tag)
	elseif self:IsTagBindingPlayer(tag) then
		if Game.BSManager and Game.BSManager.bIsInEditor then
			return nil, false, nil
		end
		TagType = Enum.SequenceBindActorType.Player
		IsRealBind = self:IsTagBindingRealPlayer(tag)
		if IsRealBind and Game.me then
			MutableID = Game.me.CharacterID
		end
	elseif self:IsTagBindingRealNpc(tag) then
		TagType = Enum.SequenceBindActorType.NPC
		local InstanceID = self:GetTagBindingObjectRelatedID(tag)
		if InstanceID then
			MutableID = InstanceID
			IsRealBind = true
		end
	elseif self:IsTagBindingFakeNpc(tag) then
		TagType = Enum.SequenceBindActorType.NPC
		local FacadeID = self:GetTagBindingObjectRelatedID(tag)
		if FacadeID then
			MutableID = FacadeID
			IsRealBind = false
		end
	elseif self:IsTagBindingObject(tag) then
		TagType =  Enum.SequenceBindActorType.Object
		local InstanceID = self:GetTagBindingObjectRelatedID(tag)
		if InstanceID then
			MutableID = InstanceID
			IsRealBind = true
		end
	end
	return TagType, IsRealBind, MutableID
end

--- 执行准备玩家Actor绑定对象
---@param task SequenceLoadTask
function SequenceTagManager:ExcuteBindPlayer(task, BindingTemplate, callback)
	local tag = string.lower(BindingTemplate.BindingTag)
	local _, IsRealBind, _ = self:AnalyzeTag(tag)
	if self:IsTagOppositeWithPlayer(tag) then
		task:SetOtherGenderTag(tag)
		task:OnTagEntityCreateFinish(BindingTemplate, false, tag)
		task:OnTagActorLoadFinish(BindingTemplate, 0)
		return
	end
	-- 如果设置了需要根据玩家当前位置做偏移则这里 计算偏移后设置到LevelSequenceActor位置
	if task:IsLevelSequenceAutoOffset() and not task:IsLevelSequenceServerSetTransform() then
		self:DoLevelSequenceActorOffsetPostion(task, BindingTemplate.Guid)
	end

	if IsRealBind then
		task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, Game.me.eid)
		task:OnTagActorLoadFinish(BindingTemplate, Game.me.CharacterID)
	else
		--local EntityUtils = kg_require("Gameplay.NetEntities.EntityUtils")
		
		local bindEntity = EntityUtils.CreateLocalAvatarEntityByAvatarEntityID("LocalCutSceneActor", {
			FacadeData = Game.me:GetConfigFacadeControlData(),
			Tag = BindingTemplate.BindingTag,
			NotResetMeshLoc = false, --- 主角都是清空位移的.
			Callback = callback }, Game.me:uid())
		task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, bindEntity.eid)
	end
end

--- 执行准备NPC绑定对象
function SequenceTagManager:ExcuteBindNPC(task, BindingTemplate, callback)
	local tag = string.lower(BindingTemplate.BindingTag)
	local _, IsRealBind, BindID = self:AnalyzeTag(tag)
	if IsRealBind then
		local eid = Game.WorldManager:GetNpcByInstance(BindID)
		local NPC = Game.EntityManager:getEntity(eid)
		if NPC then
			if NPC.bInWorld then
				task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, eid)
				task:OnTagActorLoadFinish(BindingTemplate, tonumber(NPC.CharacterID))
			else
				task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, 0)
				task:OnTagActorLoadFinish(BindingTemplate, 0)
			end
		else
			task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, 0)
			task:OnTagActorLoadFinish(BindingTemplate, 0)
		end
	else
		local facadeData = Game.TableData.GetFacadeControlDataRow(tonumber(BindID))
		if not facadeData then
			task:OnTagActorLoadFailed(BindingTemplate)
		else

			local BindingObjectID = Game.SequenceManager:KAPI_GetSpawnableObjectIDByBindingID(task:GetLoadHandleID(), BindingTemplate.Guid)
			local bKeepTransform = false
			if BindingObjectID ~= 0 then
				bKeepTransform = LuaScriptAPI.KAPI_GetPropertyValueByName(BindingObjectID, "bKeepTransform")
			end
			local bindEntity = Game.EntityManager:CreateLocalEntity("LocalCutSceneActor", {
				FacadeData = facadeData,
				Tag = tag,
				NotResetMeshLoc = bKeepTransform and true or false,
				Callback = callback })
			task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, bindEntity.eid)
		end
	end 
end

--- 执行准备Object绑定对象
function SequenceTagManager:ExcuteBindObject(task, BindingTemplate)
	local tag = string.lower(BindingTemplate.BindingTag)
	local _, IsRealBind, BindID = self:AnalyzeTag(tag)
	if IsRealBind then
		if BindID then
			local Object = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(BindID)
			if Object and Object.bInWorld then
				task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, Object:uid())
				task:OnTagActorLoadFinish(BindingTemplate, tonumber(Object.CharacterID))
			else
				task:OnTagEntityCreateFinish(BindingTemplate, IsRealBind, 0)
				task:OnTagActorLoadFinish(BindingTemplate, 0)
			end
		end
	else
		task:OnTagActorLoadFailed(BindingTemplate)
	end
end

--- 根据性别判断是否绑定空对象
---@param task SequenceLoadTask
function SequenceTagManager:ExecuteBindPlayerAttachment(task, BindingTemplate, callback)
	local tag = string.lower(BindingTemplate.BindingTag)
	task:OnTagEntityCreateFinish(BindingTemplate, false, self:GetAttachmentOwnerTag(tag))	
	task:OnTagActorLoadFinish(BindingTemplate, 0)
end

-- 同一个轨道多个Tag选择的规则
function SequenceTagManager:GetHighestLevelTagInSameTrack(BindingTemplateList)
	local PlayerAnalayseTagLst = {}
	local NpcAnalayseTagLst = {}
	local ObjectAnalayseTagLst = {}
	for index, BindingTemplate in pairs(BindingTemplateList) do
		local tag = string.lower(BindingTemplate.BindingTag)
		local TagType, isRealBind, InstanceID = self:AnalyzeTag(tag)
		local AnalayseData = {["TagType"] = TagType,
							  ["isRealBind"] = isRealBind,
							  ["InstanceID"] = InstanceID,
							  ["tag"] = tag,
							  ["index"] = index}
		if TagType == Enum.SequenceBindActorType.Player then
			table.insert(PlayerAnalayseTagLst, AnalayseData)
		elseif TagType == Enum.SequenceBindActorType.Object then
			table.insert(ObjectAnalayseTagLst, AnalayseData)
		elseif TagType == Enum.SequenceBindActorType.NPC then
			table.insert(NpcAnalayseTagLst, AnalayseData)
		end
	end
	-- 优先返回真绑定玩家tag 没有的话player类型比其他类型优先级高 优先返回其他Player标签
	for index, table in pairs(PlayerAnalayseTagLst) do
		if self:IsTagBindingRealPlayer(table.tag) then
			return BindingTemplateList[table.index]
		end
		if index == #PlayerAnalayseTagLst then
			return BindingTemplateList[table.index]
		end
	end

	for _, table in pairs(NpcAnalayseTagLst) do
		if table.isRealBind then
			return BindingTemplateList[table.index]
		end
		if self:IsTagBindingFakeNpc(table.tag) then
			return BindingTemplateList[table.index]
		end
	end

	for _, table in pairs(ObjectAnalayseTagLst) do
		local Object = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(table.InstanceID)
		if Object then
			return BindingTemplateList[table.index]
		end
	end

	return BindingTemplateList[#BindingTemplateList]
end

function SequenceTagManager:IsTagBindingPlayer(tag)
	return self:TagIsWoman(tag) or self:TagIsMan(tag) or self:TagIsPlayer(tag)
end

--- 绑定创建的Actor创建完成回调 控制绑定Tag以及假绑Actor全加载完控制播放
function SequenceTagManager:LoadActorFinish(EntityID, Tag, BindingTemplate, Task)
	if EntityID ~= 0 then
		local Entity = Game.EntityManager:getEntity(EntityID)
		if Entity and Entity.bInWorld then
			Task:OnTagActorLoadFinish(BindingTemplate, tonumber(Entity.CharacterID))
		else
			Task:OnTagActorLoadFailed(BindingTemplate)
		end
	else
		Task:OnTagActorLoadFailed(BindingTemplate)
	end
end

function SequenceTagManager:DoLevelSequenceActorOffsetPostion(task, guid)
	if not Game.me then
		return
	end
	local TrackPostion = task:GetPlayerTransformTrackPosition(guid)
	if task:IsPlayerTransformTrackPositionValid() then
		local CurrentPlayerPostion = Game.me.CppEntity:KAPI_GetLocation()
		local offsetPosition = CurrentPlayerPostion - TrackPostion
		LuaScriptAPI.KAPI_Actor_SetLocation(task:GetDirector(), offsetPosition)
		Game.SequenceManager:KAPI_ChangeSequenceTransformOriginActor(task:GetLoadHandleID(), task:GetDirector())
	end
end

function SequenceTagManager:DoServerSequenceActorAnchorOffsetPostion(task, guid)
	local AnchorPostion = task:GetPlayerTransformTrackPosition(guid)
	if task:IsPlayerTransformTrackPositionValid() then
		local serverSetPostion = task:GetServerSetPosition()
		if serverSetPostion then
			local offsetPosition = serverSetPostion - AnchorPostion
			LuaScriptAPI.KAPI_Actor_SetLocation(task:GetDirector(), offsetPosition)
		end
		local serverSetYaw = task:GetServerSetYaw()
		if serverSetYaw then
			local serverRotation = FRotator(0, serverSetYaw, 0)
			LuaScriptAPI.KAPI_Actor_SetRotation(task:GetDirector(), serverRotation)
		end
		Game.SequenceManager:KAPI_ChangeSequenceTransformOriginActor(task:GetLoadHandleID(), task:GetDirector())
	end
end

function SequenceTagManager:TagIsWoman(tag)
	return string.startsWith(tag, "woman") 
end

function SequenceTagManager:TagIsMan(tag)
	return string.startsWith(tag, "man")
end

function SequenceTagManager:TagIsPlayer(tag)
	return string.startsWith(tag, "player")
end

function SequenceTagManager:TagIsAnchor(tag)
	return string.startsWith(tag, "anchor")
end

function SequenceTagManager:TagIsAttachment(tag)
	return string.endsWith(tag, "_attachment")
end

function SequenceTagManager:GetAttachmentOwnerTag(tag)
	return string.sub(tag, 1, #tag - 11)
end

function SequenceTagManager:IsTagBindingRealNpc(tag)
	if Game.BSManager and Game.BSManager.bIsInEditor then
		return false
	end
	return string.startsWith(tag, "npc") 
end

function SequenceTagManager:IsTagBindingFakeNpc(tag)
	return string.startsWith(tag, "model") or self:IsPureNumber(tag)
end

function SequenceTagManager:IsTagBindingObject(tag)
	return string.startsWith(tag, "object")
end

function SequenceTagManager:IsTagBindingRealPlayer(tag)
	if Game.BSManager and Game.BSManager.bIsInEditor then
		return false
	end
	return string.endsWith(tag, "_real") 
end

function SequenceTagManager:IsTagEqualPlayerGender(tag)
	return (self:TagIsMan(tag) and  self:GetPlayerGender() == 0) or (self:TagIsWoman(tag) and self:GetPlayerGender() == 1) or self:TagIsPlayer(tag)
end

function SequenceTagManager:IsTagOppositeWithPlayer(tag)
	local Gender = self:GetPlayerGender()
	return (Gender == 0 and self:TagIsWoman(tag)) or (Gender == 1 and self:TagIsMan(tag))
end

function SequenceTagManager:GetTagBindingObjectRelatedID(tag)
	if self:IsPureNumber(tag) then
		return tag
	end
	local InstanceID = string.split(tag, "_")[2]
	if self:IsPureNumber(InstanceID) then
		return InstanceID
	elseif self:IsPureNumber(tag) then
		return tag
	end
	return nil
end

--- 待定 可能会出现一个Sequence多个同Tag的轨道
function SequenceTagManager:IsSameTagInSubSequence(BindingTemplateA, BindingTemplateB)
	return BindingTemplateA.BindingTag == BindingTemplateB.BindingTag and BindingTemplateA.SequenceID == BindingTemplateB.SequenceID
end

function SequenceTagManager:IsPureNumber(str)
	return tonumber(str) ~= nil and tostring(tonumber(str)) == str
end

function SequenceTagManager:IsGuidSame(GuidA, GuidB)
	return GuidA.A == GuidB.A and  GuidA.B == GuidB.B and  GuidA.C == GuidB.C and  GuidA.D == GuidB.D 
end

-- 目前性别似乎没什么地方用到，没有统一获取玩家性别的接口
function SequenceTagManager:GetPlayerDefaultBindTag(Sex)
	if Sex == 0 then
		return "man"
	end
	return "woman"
end

-- 目前性别似乎没什么地方用到，没有统一获取玩家性别的接口
function SequenceTagManager:GetPlayerGender()
	if not Game.me then
		return 0
	end

	return Game.me.Sex
end

--- 真绑定对象进入Sequence设置
function SequenceTagManager:OnInstanceEntityEnterSequence(type, entity)
	if not entity then
		return
	end
	if type == Enum.SequenceBindActorType.Player then
		Game.me:SetIsGravityOn(ViewControlConst.DISABLE_GRAVITY_TAG.LEVEL_SEQUENCE, false, false)
	--if type == Enum.SequenceBindActorType.NPC then
	--	entity.MoveDriveModeFWB:SetForceValue(EMoveDriveMode.DriveLocally, WEAK_FORCE_CONTROL_REASON_TAGS.LevelSequenceBinded)
	--	entity:SetIsGravityOn(ViewControlConst.DISABLE_GRAVITY_TAG.LEVEL_SEQUENCE, false, false)
	end
end

--- 真绑定对象退出Sequence设置
function SequenceTagManager:OnInstanceEntityExitSequence(type, entity)
	if not entity then
		return
	end
	if type == Enum.SequenceBindActorType.Player then
		Game.me:SetIsGravityOn(ViewControlConst.DISABLE_GRAVITY_TAG.LEVEL_SEQUENCE, true, false)
	--if type == Enum.SequenceBindActorType.NPC then
	--	entity.MoveDriveModeFWB:SetForceValue(EMoveDriveMode.DriveByNet, WEAK_FORCE_CONTROL_REASON_TAGS.LevelSequenceBinded)
	--	entity:SetIsGravityOn(ViewControlConst.DISABLE_GRAVITY_TAG.LEVEL_SEQUENCE, true, false)
	end
end


return SequenceTagManager
