local const = kg_require("Shared.Const")
local ATTACH_LOCATION_TYPE = const.ATTACH_LOCATION_TYPE
local NIAGARA_SOURCE_TYPE = const.NIAGARA_SOURCE_TYPE
local NIAGARA_ATTACH_COMPONENT_TYPE = const.NIAGARA_ATTACH_COMPONENT_TYPE

---@class NiagaraEffectParamTemplate
NiagaraEffectParamTemplate = DefineClass("NiagaraEffectParamTemplate")

function NiagaraEffectParamTemplate:ctor()
    self.SpawnTrans = M3D.Transform()

    self:on_recycle_to_pool()
end

function NiagaraEffectParamTemplate:on_recycle_to_pool() -- luacheck: ignore
    -- niagara资产路径
    self.NiagaraEffectPath = nil

	-- niagara priority
	self.NiagaraBudgetToken = nil
	self.NiagaraEffectType = nil
	self.CustomNiagaraPriority = nil
	
    self.bNeedAttach = false
    self.LocationType = ATTACH_LOCATION_TYPE.KEEP_RELATIVE_OFFSET
    self.AttachPointName = ""
	-- 指定以后, 如果对应的mesh是dynamic mesh, 且尚未加载好, 会将特效播放请求缓存在dynamic mesh component中, 等dynamic mesh component加载好以后再尝试播放
    self.AttachComponentName = nil
    self.AttachComponentId = 0
	self.NiagaraAttachComponentType = NIAGARA_ATTACH_COMPONENT_TYPE.FIND_ATTACH_COMPONENT_BY_SOCKET_NAME
    self.bAbsoluteScale = false
    self.bAbsoluteRotation = false

    self.SpawnerEntityId = 0
    self.SpawnerId = 0
	-- 默认情况下 特效创建者exit world时会销毁所有创建的特效, 对于子弹的销毁特效来说, 希望在子弹销毁时, 不要立刻销毁对应特效, 此时可以设置bDestroyWhenSpawnerExitWorld未false
	-- 对于attach的特效来说, spawner销毁时会detach掉对应的特效
    self.bDestroyWhenSpawnerExitWorld = true
	self.InstigatorEntityId = 0
    self.OwnerEntityID = 0
    self.SpawnTrans:Reset()

    self.SourceType = NIAGARA_SOURCE_TYPE.DEFAULT

    self.bFollowHidden = true
    self.bFollowSlomo = false
    self.bFollowCameraFOV = false
    self.EffectPlayRate = 1.0
    self.bActivateImmediately = false
	self.bBenefitEffect = false

    -- total life < 0表示不去控制特效的时长
    self.TotalLifeMs = -1
    -- 当外部调用deactivate niagara system以后, 会开启timer, 如果DelayDestroy时间以后内部niagara不结束, 则会在 effect manager中
    -- 主动发起 destroy niagara system
    self.DelayDestroyMs = 2000.0

    self.ComponentTags = nil
    self.EffectTags = nil
	-- 设置后, 特效tag默认无阵营相关tag(TEAMMATE/TEAMMATE_POSITIVE/ENEMY), 战斗设置中的特效筛选不会导致该特效被隐藏
    self.bIgnoreRelationCommonTags = false
    self.TransparencyScale = 1.0
    self.QualityLevelOffset = 0

	-- 传入参数的特效不会很多, 为了性能考虑, 这里不再提前分配table, 需要时分配, 回池即清理
    -- val type: M3D:Vec3
	self.UserVals_Float = nil
    -- val type: M3D:Vec3
	self.UserVals_Vec3 = nil
    -- val type: M3D:Vec2
	self.UserVals_Vec2 = nil
    -- val type: bone list
	self.UserVals_SkeletalMeshCompFilterBones = nil
    -- val type: float curve path
	self.UserVals_FloatCurves = nil
	-- val type: { StartVal=x, EndVal=x, Duration=x }
    self.UserVals_LinearSampleFloat = nil
    -- val type: float
	self.UserVals_FloatCurveRemapTime = nil
	-- { ParamName: {StartX=x, StartY=x, StartZ=x, EndX=x, EndY=x, EndZ=x, Duration=x}}
    self.UserVals_LinearSampleVectors = nil
	-- val type: comp id
    self.UserVals_MeshCompIds = nil
	-- 用于武器消散特效, 特效需要传入武器的mesh component作为特效参数, 用于确定特效的位置和整体的轮廓信息
	-- 此时需要创建一份武器mesh component的拷贝供特效使用, 并在特效结束后清理对应的mesh component, 这些逻辑放在特效管理器中维护
	self.bUseCloneMeshCompOnSystemUserVariableStaticMeshComponent = nil
	
	-- 设置特效朝向固定位置
	self.bForceFaceToLocation = false
	-- 这个用的很少, 需要时再创建, 类型为M3D.Vec2, 后续根据需要扩充是否需要朝向3D空间位置
	self.FacingTargetLocation = nil

	self.bForceFaceToTargetActor = false
	self.FacingTargetActorId = nil
	
	self.ParticleColorScaleUpdateCurve = nil
	self.ParticleColorScaleCurveTime = nil
    
	self.bEnableCustomDepth = nil
    self.bRenderInMainPass = nil
	self.CustomDepthStencilValue = nil
    self.TranslucencySortPriority = nil
    self.TranslucencySortDistanceOffset = nil
	
	self.bSpiritualVision = nil
    self.SpiritualVisionMeshColor = nil
    
	self.bEngineCulling = true
	-- 用于特效来源类型检查
	self.SourceSkillIdDebugUse = nil
	
    ------------------------------------------------ internal usage --------------------------------------------
    -- 指令缓存使用，外部禁止调用
    self.CustomEffectID = 0
end

NiagaraEffectParamTemplate.__PoolSize = 64
-- todo 特效优化完成后这里再做调整
NiagaraEffectParamTemplate.__PoolSizeWarningThreshold = 512
function NiagaraEffectParamTemplate.AllocFromPool()
	return Game.ObjectPoolManager:AllocateObject(NiagaraEffectParamTemplate)
end

function NiagaraEffectParamTemplate.RecycleToPool(Item)
	Game.ObjectPoolManager:ReleaseObject(Item)
end

return NiagaraEffectParamTemplate