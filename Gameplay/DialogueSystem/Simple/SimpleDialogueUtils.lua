---
--- 动态填充简易Dialogue数据
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/1/16 16:56
---
local SimpleDialogueTemplate = kg_require("Gameplay.DialogueSystem.Simple.SimpleDialogueTemplate")
---@class SimpleDialogueUtils
local SimpleDialogueUtils = DefineClass("SimpleDialogueUtils")

-- 默认距离
---@private
SimpleDialogueUtils.DEFAULT_DISTANCE = 200

-- 缓存数据
---@private
SimpleDialogueUtils.InUsingDialogue = nil

-- 旁白名称
local ASIDE_TAG = "Asider" -- luacheck: ignore
local COMMON_NPC_TAG = "CommonNpc" -- luacheck: ignore

-- 将数据填充到模板中
---@public
---@param dialogueID number
---@return table 实际生效的模板数据
function SimpleDialogueUtils.FillDialogueData(dialogueID, _, npcUID, distance)
    if SimpleDialogueUtils.InUsingDialogue then
        return SimpleDialogueUtils.InUsingDialogue
    end

    local playerTag, npcTag = SimpleDialogueUtils.getActorTag(dialogueID, npcUID)
    if (playerTag == nil) and (npcTag == nil) then
        Log.WarningFormat("[FillDialogueData] get performer tag failed in %s", dialogueID)
        return
    end

    local dialogueTemplate = SimpleDialogueTemplate.GetEmptyDialogueTemplate()
    local dialogueTalkData = Game.TableData.GetDialogueTalkDataRow(tostring(dialogueID))
    if not dialogueTalkData then
        Log.WarningFormat("[FillDialogueData] %s has no dialogue talk data", dialogueID)
        return
    end

    Log.DebugFormat("[FillDialogueData] start fill %s", dialogueID)

    dialogueTemplate.ObjectName = tostring(dialogueID)

    -- 小段信息
    for episodeID, episodeData in ksbcipairs(dialogueTalkData) do
        local newEpisode = SimpleDialogueUtils.fillEpisodeData(dialogueID, episodeID, episodeData, playerTag)
        table.insert(dialogueTemplate.Episodes, newEpisode)
    end

    -- 演员信息
    SimpleDialogueUtils.fillPerformerData(dialogueTemplate, npcUID, distance, playerTag, npcTag)

    -- LookAt信息(如有),必须在小段信息组装完成后执行
    SimpleDialogueUtils.fillLookAtData(dialogueTemplate, npcUID)

    SimpleDialogueUtils.InUsingDialogue = dialogueTemplate

    Log.DebugFormat("[FillDialogueData] finish fill %s", dialogueID)
    return dialogueTemplate
end

-- 清理缓存
---@public
function SimpleDialogueUtils.ClearDialogueTemplateData()
    SimpleDialogueTemplate.ClearTemplateData()
    SimpleDialogueUtils.InUsingDialogue = nil
end

-- luacheck: push ignore
---@private
---@param episodeID number
---@param episodeData DialogueTalkData[]
---@param playerTag string
function SimpleDialogueUtils.fillEpisodeData(dialogueID, episodeID, episodeData, playerTag)
    local dialogueAssetData = Game.TableData.GetDialogueAssetDataRow(dialogueID)
    if not dialogueAssetData then
        return
    end

    local tag2Name = {}
    for _, actorInfo in ksbcipairs(dialogueAssetData.Actors) do
        tag2Name[actorInfo.tag] = actorInfo.name
    end

    local newEpisode = SimpleDialogueTemplate.NewEpisode()

    local totalDuration = 0
    for dialogueIdx, dialogueData in ksbcipairs(episodeData) do
        local newDialogueSection = SimpleDialogueTemplate.NewDialogueSection()
        newDialogueSection.Enable = true
        newDialogueSection.Duration = Game.DialogueManager:CalculateContentDuration(nil, dialogueData.Text)
        newDialogueSection.EpisodeID = episodeID
        newDialogueSection.OwnedEpisodeID = episodeID
        newDialogueSection.FromLineIndex = dialogueIdx - 1
        newDialogueSection.CanSkip = true
        newDialogueSection.ContentIndex = dialogueData.DialogueIndex
        newDialogueSection.DynamicFlag = dialogueData.DynamicFlag
        newDialogueSection.StartTime = totalDuration

        -- 简易对话说人和UI样式匹配
        if dialogueData.Talker == playerTag then
            newDialogueSection.Talker = playerTag
            newDialogueSection.TalkerName = ""
        elseif dialogueData.Talker == ASIDE_TAG then
            newDialogueSection.Talker = ASIDE_TAG
            newDialogueSection.TalkerName = ""
        else
            newDialogueSection.Talker = COMMON_NPC_TAG
            newDialogueSection.TalkerName = tag2Name[dialogueData.Talker] or ""
        end

        -- dialogue从0开始,stateControl从0+dialogueDuration开始
        totalDuration = totalDuration + newDialogueSection.Duration

        local newStateControlSection = SimpleDialogueTemplate.NewStateControlSection()
        newStateControlSection.Enable = true
        newStateControlSection.FromLineIndex = dialogueIdx - 1
        newStateControlSection.OwnedEpisodeID = episodeID
        newStateControlSection.StartTime = totalDuration

        table.insert(newEpisode.TrackList[3].ActionSections, newStateControlSection)
        table.insert(newEpisode.TrackList[4].ActionSections, newDialogueSection)
    end

    newEpisode.EpisodeID = episodeID
    newEpisode.Duration = totalDuration

    local lastDialogueData = episodeData[#episodeData]
    if #lastDialogueData.Options < 0 then
        Log.DebugFormat("[fillEpisodeData] episode %s has no options", episodeID)
        return
    end

    newEpisode.OptionType = lastDialogueData.OptionType
    newEpisode.TimeLimit = lastDialogueData.TimeLimit
    newEpisode.TimeOutDefaultChoice = lastDialogueData.TimeOutDefaultChoice

    -- 填充选项
    for idx, optionConf in ksbcipairs(lastDialogueData.Options) do
        local strArr = string.split(optionConf, "$")
        if #strArr ~= 2 then
            Log.WarningFormat("[fillEpisodeData] episode %s optionIdx %s config error", episodeID, idx)
            goto continue
        end

        local optionID = tonumber(strArr[1])
        local targetEpisode = tonumber(strArr[2])

        local optionData = Game.TableData.GetDialogueOptionTextRow(optionID)
        if not optionData then
            Log.WarningFormat("[fillEpisodeData] episode %s option %s data not found", episodeID, optionID)
            goto continue
        end

        local newOption = SimpleDialogueTemplate.NewOption()
        newOption.Condition = lastDialogueData.OptionsCondition[idx] or ""
        newOption.DialogueID = optionID -- todo@shijingzhe:这里的DialogueID是错误的语义,实际上是OptionTextID
        newOption.DialogueLineIndex = episodeID
        newOption.DialogueText = optionData.Text
        newOption.EpisodeID = targetEpisode -- 这里是目标小段而不是所在小段
        -- newOption.Item.AssetName = optionData.AssetName
        newOption.Item.ExtraAction = optionData.ExtraAction
        newOption.Item.ID = optionID
        newOption.Item.LockText = optionData.LockText
        newOption.Item.LockVisible  = optionData.LockVisible == 1 and true or false
        newOption.Item.ReportServer = optionData.ReportServer
        newOption.Item.SelectHide = optionData.SelectHide == 1 and true or false
        newOption.Item.Text = optionData.Text
        newOption.Item.bClose = optionData.bClose

        table.insert(newEpisode.Options, newOption)
        :: continue ::
    end

    return newEpisode
end
-- luacheck: pop

-- PerformerInfo根据配表决定
---@private
---@param dialogueTemplate table 动态模板
---@param npcUID number
---@param distance number 玩家和npc的距离
---@param
function SimpleDialogueUtils.fillPerformerData(dialogueTemplate, npcUID, distance, playerTag, npcTag)
    local npcEntity = Game.EntityManager:GetEntityByIntID(npcUID)

    local playerPerformer = SimpleDialogueTemplate.NewPerformer()
    playerPerformer.AppearanceID = Game.me.Profession
    playerPerformer.TrackName = playerTag
    playerPerformer.UseSceneActor = false
    playerPerformer.bIsPlayer = true
    playerPerformer.Parent = npcTag or ""

    table.insert(dialogueTemplate.PerformerList, playerPerformer)

    if npcTag then
        if (distance == nil) or (distance == 0) then
            local npcData = npcEntity:GetEntityConfigData()
            if npcData then
                distance = npcData.InteractDistance
            end
        end

        if (distance == nil) or (distance == 0) then
            distance = SimpleDialogueUtils.DEFAULT_DISTANCE
        end

        local npcPerformer = SimpleDialogueTemplate.NewPerformer()
        npcPerformer.AppearanceID = npcEntity.TemplateID
        npcPerformer.TrackName = npcTag
        npcPerformer.UseSceneActor = true
        npcPerformer.bIsPlayer = false
        npcPerformer.InsID = npcEntity.InstanceID

        -- 获取玩家半高
        local halfHeight = Game.me.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()

        -- 有Npc的情况下,玩家面向锚点(npc)
        local playerQuat = FRotator(0, 180, 0):ToQuat()
        -- 根据Npc当前朝向旋转
        local playerLoc = FVector(distance, 0, halfHeight)

        if npcEntity then
            local anchorTrans = Game.DialogueManager.DialogueUtil.GetAnchorInsIDTransform(npcEntity.InstanceID)
            local anchorRot = anchorTrans:GetRotation()
            local finalRot = anchorRot:Inverse() * npcEntity:GetRotation():ToQuat()
            playerLoc = finalRot:RotateVector(playerLoc)
            playerQuat = finalRot * playerQuat
        end

        playerPerformer.SpawnTransform.Translation.X = playerLoc.X
        playerPerformer.SpawnTransform.Translation.Y = playerLoc.Y
        playerPerformer.SpawnTransform.Translation.Z = playerLoc.Z
        playerPerformer.SpawnTransform.Rotation.W = playerQuat.W
        playerPerformer.SpawnTransform.Rotation.X = playerQuat.X
        playerPerformer.SpawnTransform.Rotation.Y = playerQuat.Y
        playerPerformer.SpawnTransform.Rotation.Z = playerQuat.Z

        table.insert(dialogueTemplate.PerformerList, npcPerformer)
    end

    for _, actorInfo in ipairs(dialogueTemplate.ActorInfos) do
        if actorInfo.PerformerName == playerTag then
            actorInfo.AppearanceID = Game.me.Profession
        elseif actorInfo.PerformerName == npcTag then
            actorInfo.AppearanceID = npcEntity.TemplateID
            actorInfo.InsID = npcEntity.InstanceID
        end
    end

    -- 锚点设置
    if npcTag then
        dialogueTemplate.AnchorType = 2
        dialogueTemplate.AnchorNpc = npcTag
        dialogueTemplate.AnchorID = npcEntity.InstanceID
    else
        dialogueTemplate.AnchorType = 1
        dialogueTemplate.AnchorNpc = ""
        dialogueTemplate.AnchorID = ""
    end

    -- CameraMode设置
    for _, episode in ipairs(dialogueTemplate.Episodes) do
        local cameraModeSection = episode.TrackList[6].ActionSections[1]
        cameraModeSection.Duration = episode.Duration
        cameraModeSection.PrimaryActor = playerTag
        cameraModeSection.SecondaryActor = npcTag
    end
end

---@private
---@param dialogueTemplate table 动态模板
---@param npcUID number
function SimpleDialogueUtils.fillLookAtData(dialogueTemplate, npcUID)
    -- 有Npc的情况下才会有LookAt
    local npcEntity = Game.EntityManager:GetEntityByIntID(npcUID)
    if not npcEntity then
        return
    end

    for _, episode in ipairs(dialogueTemplate.Episodes) do
        local newLookAtSection = SimpleDialogueTemplate.NewLookAtSection()
        newLookAtSection.OwnedEpisodeID = episode.EpisodeID
        newLookAtSection.Duration = episode.Duration
        table.insert(episode.TrackList[5].ActionSections, newLookAtSection)
    end
end

---@private
---@param dialogueID number
---@param npcUID number
---@return string, string
function SimpleDialogueUtils.getActorTag(dialogueID, npcUID)
    local dialogueAssetData = Game.TableData.GetDialogueAssetDataRow(dialogueID)
    if not dialogueAssetData then
        return
    end

    -- 决定Actor1和Actor2的分配
    local playerTag, npcTag
    local npcEntity = Game.EntityManager:GetEntityByIntID(npcUID)

    for _, actorInfo in ksbcipairs(dialogueAssetData.Actors) do
        if actorInfo.isPlayer then
            playerTag = actorInfo.tag
        elseif (npcEntity ~= nil) and (npcEntity.TemplateID == actorInfo.npcID) then
            npcTag = actorInfo.tag
        end
    end

    return playerTag, npcTag
end

return SimpleDialogueUtils
