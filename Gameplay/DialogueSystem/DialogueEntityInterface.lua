--用于处理不同类型的DialogueEntity，封装常用接口

local AnimCommonUtility = import("AnimCommonUtility")
local EMontagePlayReturnType = import("EMontagePlayReturnType")
local AnimMontage = import("AnimMontage")
local DynamicMontageParam = import("DynamicMontageParam")

---@class DialogueEntityInterface
local DialogueEntityInterface = DefineClass("DialogueEntityInterface")


--------------------------------Entity Interface------------------------------------------
function DialogueEntityInterface.GetAnimInstance(DialogueGameEntity)
    local Actor = DialogueGameEntity:GetActor()
    if not Actor or not Actor:GetMainMesh() then
        return nil
    end
    return Actor:GetMainMesh():GetAnimInstance()
end

function DialogueEntityInterface.GetSex(DialogueGameEntity)
    local Entity = DialogueGameEntity:GetEntity()
    if Entity then
        return Entity.Sex
    end
    return 0
end

---@param AnimLibAssetID string
---@param bLoop boolean
---@param BlendInTime number
---@param BlendOutTime number
---@param stateName string|nil
function DialogueEntityInterface.InnerPlayAnimation(DialogueGameEntity, AnimLibAssetID, bLoop, BlendInTime, BlendOutTime, stateName)
    if not DialogueGameEntity then
        return
    end
    
    local Entity = DialogueGameEntity:GetEntity()
    if not Entity then
        return 0 
    end 

    if not AnimLibAssetID or AnimLibAssetID == "" then
        Log.WarningFormat("Has no AnimLibAsset!")
        return 0
    end

    local _, MontageDuration = Entity:PlayAnimLibMontage(AnimLibAssetID, stateName, bLoop, BlendInTime, BlendOutTime, true)

    Entity.DialoguePreAnimLibAssetID = AnimLibAssetID
    return MontageDuration
end


function DialogueEntityInterface.IsPlayer(DialogueGameEntity)
    return DialogueGameEntity.DialogueEntityTable.bIsPlayer
end

function DialogueEntityInterface.SetVisible(DialogueGameEntity, Visible)
    local EntityActor = DialogueGameEntity:GetActor()
    if not IsValid_L(EntityActor) then
        Log.WarningFormat("Entity %s SetVisible Failed, has no EntityActor !", DialogueGameEntity.DialogueEntityTable.TrackName)
        return
    end
    EntityActor:K2_GetRootComponent():SetVisibility(Visible, true)
    EntityActor:SetActorHiddenInGame(not Visible)
end

--Asset:AnimationAsset or AnimMontage
--PreMontageBlendOutTime:该Slot之前的montage的淡出时间
function DialogueEntityInterface.PlayMontageByAsset(DialogueGameEntity, SlotName, Asset, BlendInTime, BlendOutTime, LoopCount, InPlayRate, InTimeToStartMontageAt)
    if not Asset then
        Log.WarningFormat("Entity %s Asset is nil", DialogueGameEntity.DialogueEntityTable.TrackName)
        return
    end

    local AnimInstance = DialogueEntityInterface.GetAnimInstance(DialogueGameEntity)
    if not AnimInstance then
        Log.WarningFormat("Entity %s AnimInstance is nil", DialogueGameEntity.DialogueEntityTable.TrackName)
        return
    end

    local Montage = nil
    if Asset:IsA(AnimMontage) then
        --montage资源，直接使用
        Montage = Asset
    else
        local MontageParam = DynamicMontageParam()
        MontageParam.BlendInTime = BlendInTime
        MontageParam.BlendOutTime = BlendOutTime
        MontageParam.SlotNodeName = SlotName
        MontageParam.Asset = Asset
        MontageParam.LoopCount = LoopCount or 99999
        Montage = AnimCommonUtility.CreateDynamicMontage(MontageParam)
    end

    AnimInstance:StopSlotAnimation(BlendInTime, SlotName)

    if Montage then
        AnimInstance:Montage_Play(Montage, InPlayRate or 1.0, EMontagePlayReturnType.MontageLength, InTimeToStartMontageAt or 0, false)
    end
end

function DialogueEntityInterface.PlayIdle(DialogueGameEntity, BlendInTime)
    if not DialogueGameEntity then
        return
    end
    local DialogueEntityTable = DialogueGameEntity.DialogueEntityTable 
    if not DialogueEntityTable then
        return
    end

    local DialogueManager = Game.DialogueManager 

    if not DialogueManager:DialogueEntityTableIsA(DialogueEntityTable, DialogueManager.AssetEntityType.Performer) then
        return
    end
    
    BlendInTime = BlendInTime or 0.8
    local DialogueIdle = DialogueGameEntity.DialogueEntityTable.IdleAnimLibAssetID.AssetID 
    if DialogueIdle ~= "" then
        Log.DebugFormat("try to play idle %s", DialogueIdle)
        DialogueEntityInterface.InnerPlayAnimation(DialogueGameEntity, DialogueIdle, true, BlendInTime, 0.8)
    else
        Log.Debug("no default idle")
    end
end

return DialogueEntityInterface
