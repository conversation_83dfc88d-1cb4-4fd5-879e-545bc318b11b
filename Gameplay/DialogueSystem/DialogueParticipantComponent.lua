---@class DialogueParticipantComponent
local DialogueParticipantComponent = DefineComponent("DialogueParticipantComponent")


function DialogueParticipantComponent:__component_AfterLoadActor__()
    if (Game.DialogueManager.EditorManager ~= nil) then
        local rootComponentID = self.CppEntity:KAPI_Actor_GetRootComponent()
        self.CppEntity:KAPI_Movement_SetUpdatedComponent(rootComponentID)
    end
end

return DialogueParticipantComponent
