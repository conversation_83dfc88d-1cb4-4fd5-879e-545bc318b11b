--本文件用于辅助DialogueManager处理DialogueTable相关接口

local DialogueTableAssist = DefineClass("DialogueTableAssist")
function DialogueTableAssist:ctor()
end

--检测对话是否是以完全黑屏开始的， 如果是的话，则在完全黑屏的时候就开始对话，而不是等黑屏淡出的时候再开始
function DialogueTableAssist:CheckIsFadeInStart(DialogueTable)
    if DialogueTable == nil then
        return false
    end
    local FadeInOutTrack = self:FindTrackByName(1, "黑屏", DialogueTable)

    if (FadeInOutTrack) then
        --找到了黑屏Track
        local sectionsCount = #FadeInOutTrack.ActionSections
        if sectionsCount > 0 then
            local firstSection = FadeInOutTrack.ActionSections[1]
            if firstSection.StartTime < 0.1 then
                --一开始就有黑屏Track，则认为是以黑屏开始
                Log.Debug("dialogue begin with fade in")
                return true
            end
        end
    end

    return false
end

--检测对话是否是以黑屏结束的，如果是的话，则在结束对话的时候，不再进行闪白处理
function DialogueTableAssist:CheckIsEndWithBlackScreen(DialogueTable)
    if DialogueTable == nil then
        return false
    end

    local EpisodeLength = #DialogueTable.Episodes
    local EpisodeRef = DialogueTable.Episodes[EpisodeLength]
    local FadeInOutTrack = self:FindTrackByName(EpisodeLength, "黑屏",DialogueTable)

    if (FadeInOutTrack) then
        --找到了黑屏Track
        local sectionsCount = #FadeInOutTrack.ActionSections
        if sectionsCount > 0 then
            local lastSection = FadeInOutTrack.ActionSections[sectionsCount]
            if lastSection.StartTime + lastSection.Duration >= EpisodeRef.Duration then
                --以黑屏结束
                Log.Debug("dialogue end with fade out")
                return true
            end
        end
    end

    return false
end

--判断DialogueEntityTable类型
function DialogueTableAssist:DialogueEntityTableIsA(DialogueEntityTable, AssetEntityType)
    if AssetEntityType == nil or DialogueEntityTable == nil or DialogueEntityTable.ActorClass == nil then
        return false
    end

    if string.find(DialogueEntityTable.ObjectClass, AssetEntityType) then
        return true
    end
    return false
end

--实现TArray<UDialogueTrackBase*> FDialogueEpisode::GetAllTracks()
function DialogueTableAssist:FindDialogueEntity(TrackName, DialogueTable)
    if DialogueTable == nil then
        Log.WarningFormat("has no dialogue table")
        return nil
    end
    local AllEntities = self:GetAllEntities(DialogueTable)
    for k, v in ipairs(AllEntities) do
        if v.TrackName == TrackName then
            return v
        end
    end
    return nil
end

--实现UDialogueTrackBase* UDialogueBaseAsset::FindTrackByName(int32 EpisodeIndex, FName TrackName)
function DialogueTableAssist:FindTrackByName(EpisodeIndex, TrackName, DialogueTable)
    if DialogueTable == nil then
        return nil
    end

    local EpisodeRef = DialogueTable.Episodes[EpisodeIndex]
    local AllTracks = self:GetAllEpisodeTracksByRef(EpisodeRef)
    for k, v in ipairs(AllTracks) do
        if v.TrackName == TrackName then
            return v
        end
    end
    return nil
end

--实现TArray<UDialogueTrackBase*> FDialogueEpisode::GetAllTracks()
--bIncludeActions 是否包含Action类型的Track
function DialogueTableAssist:GetAllEpisodeTracksByRef(EpisodeRef, bIncludeActions)
    local OutTrackList = {}
    local Queue = {}
    for k, v in ipairs(EpisodeRef.TrackList) do
        table.insert(Queue, v)
    end
    while (#Queue ~= 0) do
        local QueueTrack = Queue[1]
        table.remove(Queue, 1)
        table.insert(OutTrackList, QueueTrack)
        if QueueTrack.TrackName == nil then
            Log.DebugFormat("GetAllEpisodeTracksByRef Track %s", table.tostring(QueueTrack))
        end
        for k, v in ipairs(QueueTrack.Childs) do
            table.insert(Queue, v)
        end
        if bIncludeActions then
            for k, v in ipairs(QueueTrack.Actions) do
                table.insert(Queue, v)
            end
        end
    end
    return OutTrackList
end

function DialogueTableAssist:GetEpisodeDialogueTrack(EpisodeRef)
    if not EpisodeRef then
        return nil 
    end

    local AllTracks = self:GetAllEpisodeTracksByRef(EpisodeRef)
    for k, v in ipairs(AllTracks) do
        if v.TrackName == "Dialogue" then
            return v
        end
    end
    return nil
end

--获取DialogueTable用到的所有DialogueEntity
function DialogueTableAssist:GetAllEntities(DialogueTable)
    if not DialogueTable then
        return nil
    end
    if DialogueTable.AllEntities ~= nil then
        return DialogueTable.AllEntities
    end
    DialogueTable.AllEntities = {}
    for k, v in ipairs(DialogueTable.PerformerList) do
        table.insert(DialogueTable.AllEntities, v)
    end
    for k, v in ipairs(DialogueTable.CameraList) do
        table.insert(DialogueTable.AllEntities, v)
    end
    for k, v in ipairs(DialogueTable.RoutePointList) do
        table.insert(DialogueTable.AllEntities, v)
    end
    for k, v in ipairs(DialogueTable.NewEntityList) do
        table.insert(DialogueTable.AllEntities, v)
    end
    return DialogueTable.AllEntities
end

function DialogueTableAssist:GetEpisodeByIndex(DialogueTable, EpisodeIndex)
    if not DialogueTable or not EpisodeIndex then
        return nil
    end
    
    return DialogueTable.Episodes[EpisodeIndex]
end


function DialogueTableAssist:GetRootEntity(DialogueTable)
    local DialogueTable = DialogueTable or Game.DialogueManager.DialogueTable
    local Ret = {}
    if DialogueTable == nil then
        return Ret
    end

    if #DialogueTable.Episodes > 0 then
        local EpisodeRef = DialogueTable.Episodes[1]
        for k, v in ipairs(EpisodeRef.TrackList) do
            if v.DialogueEntity then
                table.insert(Ret, self:FindDialogueEntity(v.DialogueEntity,DialogueTable))
            end
        end
    end
    return Ret
end

--------------------------------Entity系列辅助函数------------------------------------------------
function DialogueTableAssist:GetEntitySpawnWorldTransform(DialogueEntity,DialogueTable)
    local UETransform = Game.DialogueManager.DialogueUtil.TableToFTransform(DialogueEntity.SpawnTransform)
    local ParentTransform = self:GetEntityParentTransform(DialogueEntity,DialogueTable)
    return UETransform * ParentTransform
end

function DialogueTableAssist:GetEntityParentTransform(DialogueEntity,DialogueTable)
    local ret = FTransform()
    local ParentEntity = self:FindDialogueEntity(DialogueEntity.Parent,DialogueTable)
    if ParentEntity == nil then
        return ret
    end
    if ParentEntity.EntityActor then
        return ParentEntity.EntityActor:GetTransform()
    end
    return Game.DialogueManager.DialogueUtil.TableToFTransform(ParentEntity.SpawnTransform)
end

return DialogueTableAssist


