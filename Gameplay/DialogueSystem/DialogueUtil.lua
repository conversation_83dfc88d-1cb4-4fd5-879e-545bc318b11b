--[[
对话模块相关辅助函数写在这里
]]
local KismetSystemLibrary = import("KismetSystemLibrary")
local KismetMathLibrary = import("KismetMathLibrary")
local ConstColor = kg_require("Gameplay.CommonDefines.ConstColor")
local EPropertyClass = import("EPropertyClass")
local EDrawDebugTrace = import("EDrawDebugTrace")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local ESplinePointType = import("ESplinePointType")
local equipUtils = kg_require("Shared.Utils.EquipUtils")

--local AnimLayerClass = slua.loadClass("/Game/Blueprint/3C/Animation/AnimPublic/HumanCommon/DialogueAnimLayer/ABP_AL_Dialogue_HumanCommon.ABP_AL_Dialogue_HumanCommon_C")
local SimpleDialogueUtils = kg_require("Gameplay.DialogueSystem.Simple.SimpleDialogueUtils")

---@class DialogueUtil
local DialogueUtil = DefineClass("DialogueUtil")
local math = math

local VectorConst = kg_require("Gameplay.CommonDefines.VectorConst")
local DialogueClassCDO = require("Data.Config.Dialogue.DialogueClassCDO")
local ZERO_VECTOR = VectorConst.ZERO_VECTOR

function DialogueUtil.CalculateTwoVectorsDegree(Vector1, Vector2)
    local TargetAngleCos = KismetMathLibrary.Vector_CosineAngle2D(Vector1, Vector2)
    local TargetAngleRadian = math.acos(TargetAngleCos)
    local TargetAngle = TargetAngleRadian * 180 / math.pi
    local TargetCross = KismetMathLibrary.Cross_VectorVector(Vector1, Vector2)
    if TargetCross.Z < 0 then
        --逆时针
        TargetAngle = TargetAngle * -1
    end
    return TargetAngle
end

--检测一个DialogueTable是否含有真实玩家参与
function DialogueUtil.CheckDialogueTableHasRealPlayer(DialogueTable)
    if not DialogueTable then
        return false
    end
    for _, DialogueEntityTable in ipairs(DialogueTable.PerformerList) do
        if DialogueEntityTable.bIsPlayer and DialogueEntityTable.UseSceneActor then
            return true
        end
    end

    return false
end

--检测一个DialogueTable是否含有玩家参与(不管是不是真实玩家)
function DialogueUtil.CheckDialogueTableHasPlayer(DialogueTable)
    if not DialogueTable then
        return false
    end
    for _, DialogueEntityTable in ipairs(DialogueTable.PerformerList) do
        if DialogueEntityTable.bIsPlayer then
            return true
        end
    end

    return false
end


function DialogueUtil.CopyCameraFOV(DialogueEntity, Actor)
    local CameraComponent = Actor.CameraComponent
    --FOV
    local DialogueEntityFov = DialogueEntity.FOV

    CameraComponent.FieldOfView = DialogueEntityFov

    if Actor.SubCameraAttacher then
        local SubCameraComponents = slua.Array(EPropertyClass.Object, import("SceneComponent"))
        Actor.SubCameraAttacher:GetChildrenComponents(false, SubCameraComponents)
        for i = 0, SubCameraComponents:Num() - 1 do
            SubCameraComponents:Get(i).FieldOfView = DialogueEntityFov
        end
    end
end

function DialogueUtil.CopyCameraEntityDataToActor(DialogueTable, DialogueEntity, Actor)
    if not DialogueTable or not DialogueEntity or not IsValid_L(Actor) then
        return
    end
    
    local CameraComponent = Actor.CameraComponent
    if not IsValid_L(CameraComponent) then
        return
    end

    local DialogueEntityFOV = DialogueEntity.FOV
    local TrackName = DialogueEntity.TrackName
    --FOV
    CameraComponent.FieldOfView = DialogueEntityFOV

    local bOverride_DepthOfField = (DialogueEntity.bOverride_DepthOfField == 1 or DialogueEntity.bOverride_DepthOfField == true) and true or false;
    if DialogueTable.EnableDOF == false then
        bOverride_DepthOfField = false
    end
    --视距
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalDistance = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance = DialogueEntity.DepthOfFieldFocalDistance;
    if DialogueEntity.DepthOfFieldFocusActor and DialogueEntity.DepthOfFieldFocusActor ~= "" then
        local TargetActorDistance = DialogueUtil.GetFocusActorDistance(DialogueTable, DialogueEntity.DepthOfFieldFocusActor, Actor)
        if TargetActorDistance then
            CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance = TargetActorDistance
        end
    end
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFstop = bOverride_DepthOfField
    CameraComponent.PostProcessSettings.DepthOfFieldFstop = DialogueEntity.DepthOfFieldFStop

    --PC设置
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldSensorWidth = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.DepthOfFieldSensorWidth = DialogueEntity.DepthOfFieldSensorWidth;

    --Mobile设置
    --Focal Region
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalRegion = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.DepthOfFieldFocalRegion = DialogueEntity.DepthOfFieldFocalRegion;

    --NearTransitionRegion
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldNearTransitionRegion = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.DepthOfFieldNearTransitionRegion = DialogueEntity.DepthOfFieldNearTransitionRegion;

    --FarTransitionRegioin
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFarTransitionRegion = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.DepthOfFieldFarTransitionRegion = DialogueEntity.DepthOfFieldFarTransitionRegion;

    CameraComponent.PostProcessSettings.bMobileHQGaussian = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldScale = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldNearBlurSize = bOverride_DepthOfField;
    CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFarBlurSize = bOverride_DepthOfField;

    local SplineComponent = Actor.Spline

    --传入的Camera有可能是外部的Camera，需要进行判断
    if Actor.ClearSubCameraComponent then
        Actor:ClearSubCameraComponent()  --编辑器状态下清空旧的SubCameraComponent
    end
    if SplineComponent then
        local curves = DialogueEntity.SplineCurves
        local points = curves.Position.Points
        local CustomSplinePointsCount = #points
        if DialogLog then
            Log.DebugFormat("SubCameraAttacher TrackName %s DialogueEntity.SplineCurves Count: %d", TrackName, CustomSplinePointsCount)
        end

        if CustomSplinePointsCount > 0 then
            if DialogLog then
                Log.DebugFormat("%s ClearSplintPoints", TrackName)
            end
            SplineComponent:ClearSplinePoints(true)
            local SplintPoint = import("SplinePoint")()
            for i = 1, CustomSplinePointsCount do
                local PointData = points[i]
                SplintPoint.InputKey = PointData.InVal
                SplintPoint.Position = PointData.OutVal
                SplintPoint.ArriveTangent = PointData.ArriveTangent
                if SplintPoint.ArriveTangent.Y == 0 then
                    SplintPoint.ArriveTangent.Y = -130
                end
                SplintPoint.LeaveTangent = PointData.LeaveTangent
                if SplintPoint.LeaveTangent.Y == 0 then
                    SplintPoint.LeaveTangent.Y = -130
                end

                --local pointMode      = PointData.InterpMode
                SplintPoint.Type = ESplinePointType.CurveCustomTangent -- self:ConvertInterpCurveModeToSplinePointType(pointMode)

                SplintPoint.Rotation = curves.Rotation.Points[i].OutVal
                SplintPoint.Scale = curves.Scale.Points[i].OutVal

                local UEPosition = SplintPoint.Position
                local UERotator = Game.DialogueManager.DialogueUtil.TableToFRotator(DialogueEntity.SubCameraRotations[i])

                Actor:AddSubCameraComponent(UEPosition, UERotator)

                if DialogLog then
                    Log.DebugFormat("%s Add NewSplinePoint %.2f %.2f %.2f", TrackName,
                            SplintPoint.Position.X, SplintPoint.Position.Y, SplintPoint.Position.Z)
                end
                SplineComponent:AddPoint(SplintPoint, false)
            end
            SplineComponent:UpdateSpline()

            --notify editor to upate visual
            SplineComponent.bSplineHasBeenEdited = true

            if Game.DialogueManager.EditorManager then
                --只在编辑器下才需要这个Debug功能
                local DialogueAsset = Game.DialogueManager.EditorManager:GetCurrentAsset()
                local DialogueAssetEntity = DialogueAsset:FindAssetEntity(DialogueEntity.TrackName)
                if DialogueAssetEntity and DialogueAssetEntity:GetSplineRatio() > 0 then
                    self:UpdateCameraComponent2SplinePos(Actor, DialogueAssetEntity.SplineCurves.Position.Points:Num(), DialogueAssetEntity:GetSplineRatio(), DialogueAssetEntity.LookAt)
                end
            end
        end
    end
    
    if Actor.SubCameraAttacher then
        if DialogLog then
            local SubCameraComponents = slua.Array(EPropertyClass.Object, import("SceneComponent"))
            Actor.SubCameraAttacher:GetChildrenComponents(false, SubCameraComponents)
            Log.DebugFormat("SubCameraAttacher TrackName %s SubCameraAttacher Count: %d", TrackName, SubCameraComponents:Num())
        end
    end
    --FOV
    DialogueUtil.CopyCameraFOV(DialogueEntity, Actor)
end

function DialogueUtil.CopyModelEntityDataToActor(DialogueEntity, Actor)
    if DialogLog then
        Log.DebugFormat("CopyModelEntityDataToActor")
    end
    if DialogueEntity.StaticMesh then
        Actor.StaticMesh:SetStaticMesh(slua.loadObject(DialogueEntity.StaticMesh))
    end
    if DialogueEntity.SkeletalMesh and Actor.GetMainMesh then
        Actor:GetMainMesh():SetSkeletalMeshAsset(slua.loadObject(DialogueEntity.SkeletalMesh))
    end
end


function DialogueUtil.DrawLine(startPoint, endPoint, color, duration)
    local Show = DialogueUtil.shouldDrawDebug()
    if not Show then
        return
    end
    KismetSystemLibrary.DrawDebugLine(
            Game.DialogueManager:GetDialogueWorld(),
            startPoint,
            endPoint,
            color,
            3,
            1
    )
end

function DialogueUtil.DrawSphere(Location, color, duration)
    local Show = DialogueUtil.shouldDrawDebug()
    if not Show then
        return
    end
    KismetSystemLibrary.DrawDebugSphere(
        Game.DialogueManager:GetDialogueWorld(),
            Location,
            10, --radius
            10, --segments
            color or FLinearColor(1, 0, 0),
            3,
            1 --thickness
    )
end

-- 为了不影响上面接口 自动镜头的绘制， 剧情编辑器内通过开关控制
function DialogueUtil.DrawCameraDebugSphere(LocationList, Color, Radius, Duration)
    KismetSystemLibrary.FlushPersistentDebugLines(_G.StoryEditorWorld)
    for k, v in pairs(LocationList) do
        if v then
            KismetSystemLibrary.DrawDebugSphere(
                Game.DialogueManager:GetDialogueWorld(),
                    v,
                    Radius, --radius
                    10, --segments
                    Color,
                    Duration,
                    1 --thickness
            )
        end
    end
end

--向DialogueTable里面填入动态数据
--DialogueTable，对话资产lua表
--EpisodeID，当前剧集ID，可以为nil，为空则当做是当前剧集
--DynamicData，动态Section数据
--DynamicOptionData 动态选项数据，如果没有，可以为nil
function DialogueUtil.FillDialogueData(DialogueTable, EpisodeID, DynamicData, DynamicOptionData)
    Log.DebugFormat("DialogueUtil.FillDialogueData")
    if not DialogueTable then
        return
    end

    if not EpisodeID then
        EpisodeID = DialogueTable.Episodes[1].EpisodeID
    end

    local EpisodeTable = DialogueUtil.GetEpisodeByID(DialogueTable, EpisodeID)

    if not EpisodeTable then
        return
    end

    if DynamicData then
        local DialogueTableAssist = Game.DialogueManager.DialogueTableAssist 
        local AllTracks = DialogueTableAssist:GetAllEpisodeTracksByRef(EpisodeTable, true)
        for _, TrackInfo in ipairs(AllTracks) do
            if TrackInfo.ActionSections then
                for SectionIndex, SectionData in ipairs(TrackInfo.ActionSections) do
                    if SectionData.DynamicFlag and SectionData.DynamicFlag ~= "" then
                        local SectionDynamicData = DynamicData[SectionData.DynamicFlag] or {}
                        local SectionClass = DialogueUtil.GetSectionLuaClass(SectionData)
                        if SectionClass and SectionClass.CopyDynamicData then
                            SectionClass.CopyDynamicData(SectionData, SectionDynamicData)
                        else
                            Log.WarningFormat("Cant not find Section %s CopyDynamicData Func", SectionData.ObjectClass)
                        end
                    end
                end
            end
        end
    end

    if DynamicOptionData then
        EpisodeTable.Options = DynamicOptionData.Values or {}
        EpisodeTable.OptionType = DynamicOptionData.Type or 0
        Log.DebugFormat("DialogueUtil.FillDialogueOptionData")
    end
end

--获取对话资产发生时的锚点
function DialogueUtil.GetAssetAnchorTransform(DialogueTable)
    if DialogueTable == nil then
        return FTransform()
    end

    local DialogueAnchorType = Game.DialogueManager.DialogueAnchorType
    if DialogueTable.AnchorType == DialogueAnchorType.NpcSpawner
        or DialogueTable.AnchorType == DialogueAnchorType.Trigger
        or DialogueTable.AnchorType == DialogueAnchorType.Interactor
    then
        return DialogueUtil.GetAnchorInsIDTransform(DialogueTable.AnchorID)
    end
    
    if DialogueTable.AnchorType == DialogueAnchorType.Player then
        if Game.me  then
            return Game.me.CppEntity:KAPI_GetActorTransform()
        end
    end
    
    return FTransform()
end

function DialogueUtil.GetAnchorInsIDTransform(InsID)
    local Ret = FTransform()
    local Pos, Rotator
    if _G.StoryEditor then
        Pos, Rotator = Game.WorldDataManager:Editor_GetSceneActorRootPos(InsID)
    else
        Pos, Rotator = Game.WorldDataManager:GetSceneActorRootPos(InsID)
    end
    if Pos and Rotator then
        Ret:SetRotation(Rotator:ToQuat())
        Ret:SetTranslation(Pos)
    else
        Log.WarningFormat("Can not find AnchorID %s", tostring(InsID))
    end
    return Ret
end

function DialogueUtil.GetActorTargetPosition(Actor)
    if not IsValid_L(Actor) then
        return
    end

    local USkeletalMeshComponent 
    if Actor.GetMainMesh then
        USkeletalMeshComponent = Actor:GetMainMesh()
    end

    if not USkeletalMeshComponent then
        return ZERO_VECTOR
    end
    
    local FaceLocation = USkeletalMeshComponent:GetSocketLocation("head")
    local z = Actor:GetTransform():GetLocation().Z
    if FaceLocation.Z < z then
        FaceLocation = USkeletalMeshComponent:GetSocketLocation("Bip001-Head")
    end
    if FaceLocation.Z < z then
        return ZERO_VECTOR
    end
    Log.DebugWarningFormat("Face Position: %s %s %s", FaceLocation.X, FaceLocation.Y, FaceLocation.Z)
    return FVector(FaceLocation.X, FaceLocation.Y, FaceLocation.Z)
end

function DialogueUtil.GetDataFormat(digit)
    if math.abs(digit) < 0.01 then
        return "0.00"
    end

    return string.format("%.2f", digit)
end

--获取本轮对话所有资产路径table
function DialogueUtil.GetDialoguePathList(DialogueID, DialoguePath)
    local ret = {}
    if DialogueID then
        table.insert(ret, tostring(DialogueID))
    end

    return ret
end

--获取DialogueTableList需要异步加载的资源列表
--返回两个列表，一个是普通的资源列表，一个是音效列表
function DialogueUtil.GetDialogueTableListAsyncPathList(DialogueTableList)
    local ResourcePathList = {}
    for k, DialogueTable in ipairs(DialogueTableList) do
        if DialogueTable then
            if DialogueTable.PreLoadArray then
                for k,v in pairs(DialogueTable.PreLoadArray) do
                    table.insert(ResourcePathList,v )
                    Log.DebugFormat("try async load resource: %s", v)
                end
            end
        end
    end
    return ResourcePathList
end
---@return Transform
function DialogueUtil.GetEntitySpawnTransform(DialogueTable, SpawnTransform, DialogueEntityTable)
    if not DialogueEntityTable or not DialogueTable then
        return FTransform()
    end

    local DialogueEntityParent = Game.DialogueManager:FindDialogueEntity(DialogueEntityTable.Parent, DialogueTable)
    if DialogueEntityParent then
        --有父Entity
        local EntityUETransform = DialogueUtil.TableToFTransform(DialogueEntityTable.SpawnTransform)
        local ParentSpawnTransform = DialogueUtil.GetEntitySpawnTransform(DialogueTable, SpawnTransform,
            DialogueEntityParent)
        return EntityUETransform * ParentSpawnTransform
    end

    --没有父Entity，说明是锚点
    --SpawnTransform是具体调用者传入的位置，可以为nil
    if SpawnTransform then
        --如果外界指定了特殊位置，则以这个位置为锚点的位置
        Log.DebugFormat("Dialogue Specify SpawnTransform")
        return SpawnTransform
    end

    --如果外界未指定特殊位置
    local DialogueAnchorType = Game.DialogueManager.DialogueAnchorType
    --最新规则，使用了绝对位置的情况下，使用自身的SpawnTransform
    if DialogueTable.AnchorType == DialogueAnchorType.Zero then
        --使用绝对坐标
        return Game.DialogueManager.DialogueUtil.TableToFTransform(DialogueEntityTable.SpawnTransform)
    end

    --未使用绝对坐标，直接返回目标（比如NpcSpawner的位置，不再考虑锚点本身的偏移
    Log.DebugFormat("Dialogue Anchor is not Zero, anchor entity SpawnTransform as 0")
    return DialogueUtil.GetAssetAnchorTransform(DialogueTable)
end

---@param DialogueTable DialogueTable
---@return Transform
function DialogueUtil.GetEntityTransformRelativeToAnchor(DialogueTable, DialogueEntity)
    if not DialogueEntity or not DialogueTable then
        return FTransform()
    end

    local dialogueMgr = Game.DialogueManager
    local entityParent = dialogueMgr:FindDialogueEntity(DialogueEntity.Parent, DialogueTable)
    if entityParent then
        --有父Entity
        local EntityUETransform = DialogueUtil.TableToFTransform(DialogueEntity.SpawnTransform)
        local ParentSpawnTransform = DialogueUtil.GetEntityTransformRelativeToAnchor(DialogueTable, entityParent)
        return EntityUETransform * ParentSpawnTransform
    end

    --没有父Entity，说明是锚点
    return FTransform()
end


--DialogueEntity是资产中的UObject，专门用于编辑器
function DialogueUtil.GetEntityObjectSpawnTransform(DialogueTable, DialogueEntity)
    if not DialogueEntity then
        return FTransform()
    end

    local spawnTrans = DialogueUtil.EditorGetSpawnTransWithoutOffsetZ(DialogueTable, DialogueEntity)

    if DialogueEntity.bEnableLookAt then
        local targetPerformer = Game.DialogueManager:FindDialogueEntity(DialogueEntity.LookAtTarget.PerformerName, DialogueTable)
        if (targetPerformer ~= nil) and (targetPerformer.EntityActor ~= nil) then
            local targetHeadLoc = DialogueUtil.GetActorTargetPosition(targetPerformer.EntityActor)
            local spawnLoc = spawnTrans:GetTranslation()
            spawnLoc.Z = targetHeadLoc.Z + DialogueEntity.OffsetZ
            spawnTrans:SetTranslation(spawnLoc)
        end
    end

    return spawnTrans
end

function DialogueUtil.EditorGetSpawnTransWithoutOffsetZ(dialogueConfig, dialogueEntityObj)
    local dialogueEntityParent = dialogueEntityObj.Parent
    if dialogueEntityParent then
        return dialogueEntityObj.SpawnTransform * DialogueUtil.EditorGetSpawnTransWithoutOffsetZ(dialogueConfig, dialogueEntityParent)
    else
        local anchorTransform = DialogueUtil.GetAssetAnchorTransform(dialogueConfig)
        return dialogueEntityObj.SpawnTransform * anchorTransform
    end
end

function DialogueUtil.GetEpisodeByID(DialogueTable, EpisodeID)
    if not DialogueTable or not EpisodeID then
        return nil
    end
    
    for i = 1, #DialogueTable.Episodes do
        local EpisodeRef = DialogueTable.Episodes[i]
        if(EpisodeRef.EpisodeID == EpisodeID) then
            return EpisodeRef
        end
    end
    return nil
end

function DialogueUtil.GetFocusActorDistance(DialogueTable, FocusActorName, CameraActor)
    local TargetActor
    if type(FocusActorName) == "string" then
        if FocusActorName ~= "" then
            local Actor = Game.DialogueManager:FindDialogueEntity(FocusActorName, DialogueTable)
            if not Actor then
                return
            end
            TargetActor = Actor.EntityActor
        end
    else
        if FocusActorName.PerformerName ~= "" then
            local Actor = Game.DialogueManager:FindDialogueEntity(FocusActorName.PerformerName,DialogueTable)
            if not Actor then
                return
            end
            TargetActor = Actor.EntityActor
        end
    end

    if not IsValid_L(TargetActor) then
        return
    end
    
    local FocusPosition = DialogueUtil.GetActorTargetPosition(TargetActor)
    local CameraPosition = CameraActor:GetTransform():GetLocation()
    local Distance = KismetMathLibrary.Vector_Distance(CameraPosition, FocusPosition)
    return Distance
end

--将对话配置路径转为纯粹的资源名
--InAssetPath类似于Citystory/007/City_007_6200715_001,变为City_007_6200715_001
function DialogueUtil.GetDialoguePathRealName(InAssetPath)
    local ConfigAssetPath = InAssetPath
    local FindIndex = 1
    local EndPos = nil
    --查找最后1个/
    while (string.find(ConfigAssetPath, "/", FindIndex)) do
        EndPos = string.find(ConfigAssetPath, "/", FindIndex)
        FindIndex = EndPos + 1
    end

    local AssetName = ConfigAssetPath
    if (EndPos ~= nil) then
        AssetName = string.sub(ConfigAssetPath, EndPos + 1)
    end
    return AssetName
end

function DialogueUtil.GetClassName(name)
    local s, e = string.find(name, ".", 1, true)
    if s then
        return string.sub(name, s + 1)
    else
        return name
    end
end

---@param AssetName string
---@param ForceReload boolean
---@return DialogueTable|nil
function DialogueUtil.GetDialogueTable(AssetName, ForceReload)
    if AssetName == nil then
        return nil
    end

    local TablePath = "Data.Config.Dialogue." .. AssetName
    if ForceReload then
        Game.loaded[TablePath] = nil
    end

    local Success, DialogueTable = pcall(kg_require, TablePath)
    if Success == false then
        Log.WarningFormat("require dialogue table %s failed!", AssetName)
        return nil
    end

    if DialogueTable and DialogueUtil.DialogueClassCDO then
        local DialogueClassCDO = DialogueUtil.DialogueClassCDO

        local insert = table.insert
        local remove = table.remove
        local needSetCDO = {}
        local tables = {DialogueTable}
        while #tables > 0 do
            local t = tables[1]
            if t.ObjectClass then
                needSetCDO[t] = t.ObjectClass
            end

            for k, v in pairs(t) do
                if type(v) == "table" then
                    insert(tables, v)
                end
            end
            remove(tables, 1)
        end

        local getmetatable = getmetatable
        local setmetatable = setmetatable
        for tbl, class in pairs(needSetCDO) do
            local origMetaTable = getmetatable(tbl)
            if origMetaTable and origMetaTable.__tag == "DialogueCDO" then
                goto continue
            end

            local origIndex = origMetaTable and origMetaTable.__index or nil
            local className = DialogueUtil.GetClassName(class)
            local CDO = DialogueClassCDO[className]
            if not CDO then
                Log.WarningFormat("DialogueCDO not found for class %s", className)
                goto continue
            end

            setmetatable(tbl, {
                __index = function(tbl, key)
                    local val
                    if origIndex then
                        val = origIndex(tbl, key)
                    end

                    if val ~= nil then
                        return val
                    end

                    if CDO then
                        return CDO[key]
                    end

                    return nil
                end,
                __tag = "DialogueCDO",
            })

            ::continue::
        end
    end

    return DialogueTable
end

-- 开启对话时，我们获取一下具体的Asset列表
---@param dialogueID number
---@param dialoguePathStr string
---@param npcUID number
---@param distance number
---@return DialogueTable[]
function DialogueUtil.GetDialogueTableList(dialogueID, dialoguePathStr, npcUID, distance)
    local ret = {}
    local bSimpleDialogue = DialogueUtil.IsSimpleDialogue(dialogueID)
    local dialoguePathList = DialogueUtil.GetDialoguePathList(dialogueID, dialoguePathStr)

    for _, dialoguePath in ipairs(dialoguePathList) do
        local dialogueTable
        if bSimpleDialogue then
            dialogueTable = DialogueUtil.GetSimpleDialogueTableFromTemplate(dialogueID, dialoguePath, npcUID, distance)
        else
            dialogueTable = DialogueUtil.GetDialogueTable(dialoguePath)
        end

        if dialogueTable then
            table.insert(ret, dialogueTable)
        end
    end

    return ret
end

---@public
---@param dialogueID number
function DialogueUtil.IsSimpleDialogue(dialogueID)
    local dialogueAssetData = Game.TableData.GetDialogueAssetDataRow(dialogueID)
    if not dialogueAssetData then
        return false
    end

    return dialogueAssetData.bSimpleDialogue
end

---@public
function DialogueUtil.GetSimpleDialogueTableFromTemplate(dialogueID, dialoguePath, npcUID, distance)
    return SimpleDialogueUtils.FillDialogueData(dialogueID, dialoguePath, npcUID, distance)
end

--获取某个DialogueID里面使用的实际NPC InsID 列表
--返回 table
function DialogueUtil.GetDialogueNpcInsIDTable(DialogueID)
    local ret = {}
    local DialoguePathList = DialogueUtil.GetDialoguePathList(DialogueID, nil)
    for k, v in ipairs(DialoguePathList) do
        local DialogueTable = DialogueUtil.GetDialogueTable(v)
        DialogueUtil.GetDialogueNpcInsIDTableByDialogueTable(DialogueTable, ret)
    end
    return ret
end

--获取某个DialogueTable里面使用的实际NPC InsID 列表
--RetTable:In&Out 
function DialogueUtil.GetDialogueNpcInsIDTableByDialogueTable(DialogueTable, RetTable)
    if not DialogueTable then
        return
    end
    for _, DialogueEntityTable in ipairs(DialogueTable.PerformerList) do
        if not DialogueEntityTable.bIsPlayer and DialogueEntityTable.UseSceneActor
           and DialogueEntityTable.InsID ~= "" then
            table.insert(RetTable, DialogueEntityTable.InsID)
        end
    end
end

--根据传入的AppearanceID查找表格里面的PlayerBattleData数据
--AppearanceID支持新旧类型配置
function DialogueUtil.GetPlayerBattleDataByAppearanceID(AppearanceID)
    local ExactPlayerData = equipUtils.GetPlayerBattleDataRow(AppearanceID, 0)
    if ExactPlayerData then
        --如果旧的AppearanceID查找到了玩家数据，说明是旧的7位数配置，默认性别为0
        return ExactPlayerData
    else
        --尝试用8位数解析出ProfessionID和Sex
        local ProfessionID = math.floor(AppearanceID / 10)
        local Gender = AppearanceID % 10
        return equipUtils.GetPlayerBattleDataRow(ProfessionID, Gender)
    end
end

function DialogueUtil.GetSectionLuaClass(SectionData)
    if not SectionData then
        return nil
    end
    --SectinoName like BPS_Dialogue_C
    local SectionName = DialogueUtil.GetClassName(SectionData.ObjectClass)
    local SubIndex = string.find(SectionName, "_")
    --like Dialogue_C
    local luaClassName = string.sub(SectionName, SubIndex+1)
    SubIndex = string.find(luaClassName, "_")
    if SubIndex then
        --like Dialogue
        luaClassName = string.sub(luaClassName, 0, SubIndex-1)
    end
    local InstanceClassPath =  "Gameplay.DialogueSystem.Section.ASI_" .. luaClassName
    local Success, InstanceClass = pcall(kg_require,InstanceClassPath) 
    if Success then
        return InstanceClass
    else
        Log.WarningFormat(InstanceClass)
    end
    return nil
end

function DialogueUtil.GetVectorFormat(vector)
    return string.format("%s %s %s", DialogueUtil.GetDataFormat(vector.X), DialogueUtil.GetDataFormat(vector.Y), DialogueUtil.GetDataFormat(vector.Z))
end

--传入DialogueID或者DialoguePath，判断是否是有效对话
function DialogueUtil.IsValidDialogue(DialogueID, DialoguePath)
    local DialogueTableList = DialogueUtil.GetDialogueTableList(DialogueID, DialoguePath)
    if not DialogueTableList or #DialogueTableList == 0 then
        Log.ErrorFormat("Dialogue InValid ID %d, Path %s", DialogueID or 0, DialoguePath or "")
        return false
    end

    return true
end

--传入DialogueID或者DialoguePath，判断是否是独占式对话
--目前不允许连续多段的非独占对话
function DialogueUtil.IsUniqueDialogue(DialogueID, DialoguePath)
    local DialogueTableList = DialogueUtil.GetDialogueTableList(DialogueID, DialoguePath)
    if not DialogueTableList or #DialogueTableList == 0 then
        Log.ErrorFormat("Dialogue InValid ID %d, Path %s", DialogueID or 0, DialoguePath or "")
        return false
    end

    if #DialogueTableList > 1 then
        --连续多段对话,必须独占
        return true
    else
        local IsUnique = DialogueTableList[1].Unique
        if IsUnique == nil then
            --旧的未导出的对话，一律认为是独占
            return true
        end
        return IsUnique
    end
end

function DialogueUtil.IsUniqueDialogueTable(DialogueTable)
    if not DialogueTable then
        return false
    end
    if DialogueTable.Unique == nil then
        --旧的对话尚未导出lua，为nil，一律认为是独占
        return true
    end
    return DialogueTable.Unique
end

function DialogueUtil.Lerp(Min, Max, Ratio)
    Ratio = math.clamp(Ratio, 0, 1)
    local Ret = Min + Ratio * (Max - Min)
    return Ret 
end

function DialogueUtil.ScreenMsg(msg, time)
    Log.DebugFormat(msg)
    KismetSystemLibrary.PrintString(GetContextObject(), msg, true, true, ConstColor.CONST_COLOR_GREEN , time)

end

function DialogueUtil.shouldDrawDebug()
    if Game.DialogueManager.EditorManager then
        return Game.DialogueManager.EditorManager:GetDialogueEditorSettings().EnableLookAtDrawDebug
    end
    return Game.DialogueManager.DialogueDrawDebug
end

function DialogueUtil.TableToFTransform(Transform)
    if Transform == nil then
        return FTransform()
    end
    local Translation = Transform.Translation
    local Rotation = Transform.Rotation
    local Scale3D = Transform.Scale3D
    return FTransform(FQuat(Rotation.X, Rotation.Y, Rotation.Z, Rotation.W),
            DialogueUtil.TableToFVector(Translation), DialogueUtil.TableToScale(Scale3D))
end

function DialogueUtil.TableToFVector(Vector)
    if Vector == nil then
        return FVector(0, 0, 0)
    end
    return FVector(Vector.X, Vector.Y, Vector.Z)
end

function DialogueUtil.TableToScale(Vector)
    if Vector == nil then
        return FVector(1, 1, 1)
    end
    return FVector(Vector.X, Vector.Y, Vector.Z)
end

function DialogueUtil.TableToFRotator(Vector)
    if Vector == nil then
        return FRotator(0, 0, 0)
    end
    return FRotator(Vector.Pitch, Vector.Yaw, Vector.Roll)
end

function DialogueUtil.TraceObject(InActor, startPos, endPos, colorDuration, traceColor, hitColor, IgnoreActors)
    local UEIgnoreActors = slua.Array(EPropertyClass.Object, import("Actor"))
    for k, v in pairs(IgnoreActors or {}) do
        UEIgnoreActors:Add(v)
    end

    local bTrace, HitResult = KismetSystemLibrary.LineTraceSingle(
            InActor,
            startPos,
            endPos,
            CollisionConst.COLLISION_TRACE_TYPE_BY_NAME.Visibility,
            false, --TraceComplex
            UEIgnoreActors, --ActorsToIgnore
            colorDuration ~= 0 and EDrawDebugTrace.ForDuration or 0, --0
            nil, --HitResult
            true, --IgnoreSelf
            traceColor or FLinearColor(1, 0, 0), --TraceColor
            hitColor or FLinearColor(0, 1, 0), --HitColor
            colorDuration or 5 --duration
    )
    if bTrace then
        local HitActor = HitResult.HitObjectHandle.ReferenceObject 
        if HitActor then
            local ShouldIgnore = false
            Log.WarningFormat(" encount Object: %s", HitActor:GetName())
            if HitActor.bHidden then
                Log.WarningFormat(" encount hidden actor, continue trace ", HitActor:GetName())
                ShouldIgnore = true
            elseif HitActor:IsA(import("SkeletalMeshActor")) or 
                   HitActor:IsA(import("Pawn") )
            then
                Log.WarningFormat(" encount character, continue trace ", HitActor:GetName())
                ShouldIgnore = true
            end
            if ShouldIgnore then
                table.insert(IgnoreActors, HitActor)
                return DialogueUtil.TraceObject(InActor, startPos, endPos, colorDuration, traceColor, hitColor, IgnoreActors)
            else
                Log.DebugFormat("DialogueManager:TraceObject type of(HitResult.Location) is %s, value is %s X:%.2f Y:%.2f Z:%.2f",
                         type(HitResult.Location), tostring(HitResult.Location),HitResult.Location.X,HitResult.Location.Y,HitResult.Location.Z)
                return FVector(HitResult.Location.X, HitResult.Location.Y, HitResult.Location.Z)
            end 
        end
    end
    return nil
end

return DialogueUtil