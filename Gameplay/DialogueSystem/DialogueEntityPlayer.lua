--[[
对话Runtime下实际玩家对应的GameEntity
]]

local DialogueEntityInterface = kg_require("Gameplay.DialogueSystem.DialogueEntityInterface")
local DialogueEntityPlayer = DefineClass("DialogueEntityPlayer")

local EMoveDriveMode = import("EMoveDriveMode")
local WEAK_FORCE_CONTROL_REASON_TAGS = kg_require("Shared.Const.ParallelBehaviorControlConst").WEAK_FORCE_CONTROL_REASON_TAGS


function DialogueEntityPlayer:ctor()
    self.EntityType = Game.DialogueManager.EntityType.RuntimePlayer

    self.AdjustTransform = true --是否调整Transform到剧编位置
end

function DialogueEntityPlayer:Create(DialogueDirector, DialogueEntityTable, SpawnTransform)
    self.DialogueDirector = DialogueDirector
    self.DialogueEntityTable = DialogueEntityTable
    self.SpawnTransform = SpawnTransform

    self:CompleteReady()
end

function DialogueEntityPlayer:GetEntity()
    return Game.me
end


-- function DialogueEntityPlayer:MoveWithSplineInDialogue(SplineData, MaxVelocity)
--     if self:GetEntity() and self:GetEntity().MoveWithSpline ~= nil then
--         self:GetEntity():MoveWithSpline(SplineData, MaxVelocity)
--     end
-- end

-- function DialogueEntityPlayer:StopMoveWithSplineInDialogue()
--     if self:GetEntity() and self:GetEntity().StopSplineMove ~= nil then
--         self:GetEntity():StopSplineMove()
--     end
-- end

function DialogueEntityPlayer:Tick(DeltaTime)
end

function DialogueEntityPlayer:DestroyEntity()
    if self.AdjustTransform then
        --释放Entity的时候，重新设置回原来的位置
        local Actor = self:GetActor()
        if Actor then
            Actor:K2_SetActorTransform(self.OldTransform, false, nil, false)
        end
    end

    local RuntimeEntity = self:GetEntity()
    if RuntimeEntity then
        RuntimeEntity:StopAnimLibMontage(nil, 1)
        RuntimeEntity.MoveDriveModeFWB:ClearForceValue(WEAK_FORCE_CONTROL_REASON_TAGS.DialogueBinded)
        RuntimeEntity:SetGlobalAnimRateScale(1)
    end

    Game.me:LockInputInTask(false)
end

function DialogueEntityPlayer:dtor()
end

function DialogueEntityPlayer:uid()
    local RuntimeEntity = self:GetEntity()
    if RuntimeEntity then
        return RuntimeEntity:uid()
    end
    return nil
end

function DialogueEntityPlayer:GetActor()
	-- @lizhang, DialogueDirector,Manager,Section都在用，暂时保留，等相关迭代
	local Ret = Game.ObjectActorManager:GetObjectByID(Game.me.CharacterID)
    if IsValid_L(Ret) then
        return Ret
    end
    return nil
end

function DialogueEntityPlayer:CompleteReady()
    local Actor = self:GetActor()
    if self.AdjustTransform then
        self.OldTransform = Actor:GetTransform()
        --暂时注掉Runtime Entity设置位置的功能
        Actor:K2_SetActorTransform(self.SpawnTransform, false, nil, false)
    end

    --接管Entity的客户端控制权
    local RuntimeEntity = self:GetEntity()
    if RuntimeEntity then
        RuntimeEntity.MoveDriveModeFWB:SetForceValue(EMoveDriveMode.DriveLocally, WEAK_FORCE_CONTROL_REASON_TAGS.DialogueBinded)
    end

    self.Ready = true
    DialogueEntityInterface.PlayIdle(self, 0)
    self.DialogueDirector:OnEntityReady(self)

    Game.me:LockInputInTask(true)
end

return DialogueEntityPlayer
