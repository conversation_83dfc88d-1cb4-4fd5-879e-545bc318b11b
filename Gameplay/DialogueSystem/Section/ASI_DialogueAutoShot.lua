local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
---@class ASI_DialogueAutoShot : ASI_Base
---@field private _transformer table<CameraBreathType, fun(autoCameraCut:ASI_DialogueAutoShot, sectionData:table, deltaTime:number):void>
local ASI_DialogueAutoShot = DefineClass("ASI_DialogueAutoShot", ASI_Base)
local EViewTargetBlendFunction = import("EViewTargetBlendFunction")
local KismetMathLibrary = import("KismetMathLibrary")
local VectorConst = kg_require("Gameplay.CommonDefines.VectorConst")


---@type CameraBreathType
local CameraBreathType = Game.DialogueManager.CameraBreathType

local CONST_VEC_UP = VectorConst.UP_VECTOR
local CONST_VEC_DOWN = VectorConst.DOWN_VECTOR

---@param breathOffsetDis number
---@param sectionData table
---@param deltaTime number
function ASI_DialogueAutoShot:_InternalMove(direction, sectionData, deltaTime)
    if not IsValid_L(self.Actor) then
        return
    end

    local NewLocation = self.Actor:K2_GetActorLocation() +
        direction * sectionData.BreathSpeed * deltaTime
    self.Actor:K2_SetActorLocation(NewLocation, false, self.HitResult, false)
end

---@param breathFocusDistance number
---@param breathSpeed number
---@param axis FVector
---@param deltaTime number
function ASI_DialogueAutoShot:_InternalRotate(breathFocusDistance, breathSpeed, axis, deltaTime)
    local actor = self.Actor
    assert(actor and IsValid_L(actor), "[ASI_DialogueAutoShot]_InternalRotate failed:actor is nil or invalid.")
    local KismetMath = KismetMathLibrary
    local NewDirection = KismetMath.RotateAngleAxis(
        actor:K2_GetRootComponent():GetForwardVector(), breathSpeed * deltaTime, axis)
    local NewLocation = self.RotateTargetPos - NewDirection * breathFocusDistance
    local NewRotation = KismetMath.FindLookAtRotation(NewLocation, self.RotateTargetPos)
    actor:K2_SetActorLocationAndRotation(NewLocation, NewRotation, false, self.HitResult, false)
end

function ASI_DialogueAutoShot:MoveLeft(sectionData, deltaTime)
    if self.RightVector then
        self:_InternalMove(self.RightVector * -1, sectionData, deltaTime)
    end
end

function ASI_DialogueAutoShot:MoveRight(sectionData, deltaTime)
    if self.RightVector then
        self:_InternalMove(self.RightVector, sectionData, deltaTime)
    end
end

function ASI_DialogueAutoShot:MoveTop(sectionData, deltaTime)
    self:_InternalMove(CONST_VEC_UP, sectionData, deltaTime)
end

function ASI_DialogueAutoShot:MoveBottom(sectionData, deltaTime)
    self:_InternalMove(CONST_VEC_DOWN, sectionData, deltaTime)
end

function ASI_DialogueAutoShot:MoveForward(sectionData, deltaTime)
    if self.ForwardVector then
        self:_InternalMove(self.ForwardVector, sectionData, deltaTime)
    end
end

function ASI_DialogueAutoShot:MoveBackward(sectionData, deltaTime)
    if self.ForwardVector then
        self:_InternalMove(self.ForwardVector * -1, sectionData, deltaTime)
    end
end

function ASI_DialogueAutoShot:RotateLeft(sectionData, deltaTime)
    self:_InternalRotate(sectionData.BreathFocusDistance, sectionData.BreathSpeed, self.UpVector,
        deltaTime)
end

function ASI_DialogueAutoShot:RotateRight(sectionData, deltaTime)
    self:_InternalRotate(sectionData.BreathFocusDistance, sectionData.BreathSpeed * -1, self
        .UpVector, deltaTime)
end

function ASI_DialogueAutoShot:RotateTop(sectionData, deltaTime)
    self:_InternalRotate(sectionData.BreathFocusDistance, sectionData.BreathSpeed, self.RightVector,
        deltaTime)
end

function ASI_DialogueAutoShot:RotateBottom(sectionData, deltaTime)
    self:_InternalRotate(sectionData.BreathFocusDistance, sectionData.BreathSpeed * -1,
        self.RightVector, deltaTime)
end

--- camera break processor
---@type table<CameraBreathType, fun(autoCameraCut:ASI_DialogueAutoShot, sectionData:table, deltaTime:number):void>
ASI_DialogueAutoShot._transformer = {
    [CameraBreathType.Left] = ASI_DialogueAutoShot.MoveLeft,
    [CameraBreathType.Right] = ASI_DialogueAutoShot.MoveRight,
    [CameraBreathType.Top] = ASI_DialogueAutoShot.MoveTop,
    [CameraBreathType.Bottom] = ASI_DialogueAutoShot.MoveBottom,
    [CameraBreathType.Front] = ASI_DialogueAutoShot.MoveForward,
    [CameraBreathType.Back] = ASI_DialogueAutoShot.MoveBackward,
    [CameraBreathType.RotateLeft] = ASI_DialogueAutoShot.RotateLeft,
    [CameraBreathType.RotateRight] = ASI_DialogueAutoShot.RotateRight,
    [CameraBreathType.RotateTop] = ASI_DialogueAutoShot.RotateTop,
    [CameraBreathType.RotateBottom] = ASI_DialogueAutoShot.RotateBottom,
}

---@param cameraBreathType CameraBreathType
---@return fun(autoCameraCut:ASI_DialogueAutoShot, sectionData:table, deltaTime:number):void
function ASI_DialogueAutoShot:GetTransformer(cameraBreathType)
    return self._transformer[cameraBreathType]
end

function ASI_DialogueAutoShot:Init(InSectionData, InController)
    self.DrawNpcCenter = nil
    self.DrawActorCenter = nil
    self.FocusActorDisatance = nil
    self.CurrentCameraComponent = nil
    self.TargetCamera = nil
end

function ASI_DialogueAutoShot:Start()
    local SectionData = self.SectionData

    local TargetCameraGameEntity = self:GetDialogueDirector():FindDialogueGameEntity(SectionData.CameraName)
    if not TargetCameraGameEntity then
        Log.WarningFormat("has no valid camera %s", SectionData.CameraName)
        return
    end
    self.TargetCamera = TargetCameraGameEntity:GetActor()
    --需要在自动相机设置前记录一下之前的相机参数 否则自动相机设置的TargetCamera可能跟CurrentCamera是同一个
	
	Log.Debug( self.TargetCamera)
	if not _G.StoryEditor and self.DialogueManager.NeedBlendInCamera and self.SectionData == self.ActionTrack.ActionSections[1] then
		--需要BlendIn，则忽略第一个机位

		Game.CameraManager.PlayerController:SetViewTargetWithBlend(self.TargetCamera,
			1, EViewTargetBlendFunction.VTBlend_EaseInOut, 3, true)
	else
		if not self.SectionData.ImmediateCut  and  self.SectionData.Duration then
			Game.CameraManager.PlayerController:SetViewTargetWithBlend(self.TargetCamera,
				self.SectionData.Duration, EViewTargetBlendFunction.VTBlend_EaseInOut, 3, true)
		else
			Game.CameraManager.PlayerController:SetViewTargetWithBlend(self.TargetCamera, 0, 0, 0, false)
		end
	end


	--self.TargetTransform = self.TargetCamera:GetTransform()
	
  
    --自动相机的位置计算到TargetCamera
    --self:HandleDefaultActor()
    local AutoCameraManager = Game.DialogueManager:GetAutoCameraManager()
    AutoCameraManager:CalculateCameraParams(self, self.TargetCamera, SectionData)
	
    self:HandleCameraParam()

    --预处理呼吸的一些参数
    self:PreDealBreath()
end

function ASI_DialogueAutoShot:HandleCameraParam()
    if not IsValid_L(self.TargetCamera) then
        return
    end
    
    local TargetCameraCameraComponent = self.TargetCamera:GetComponentByClass(import("CameraComponent"))
    assert(TargetCameraCameraComponent, "TargetCameraCameraComponent is nil")

    local OpenDOF = self.SectionData.bOpenDOF
    if OpenDOF then
        self.TargetCamera.bOverride_DepthOfField = 1
        TargetCameraCameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalDistance = true
        TargetCameraCameraComponent.PostProcessSettings.bOverride_DepthOfFieldSensorWidth = true
        TargetCameraCameraComponent.PostProcessSettings.bOverride_DepthOfFieldFstop = true
    else
        self.TargetCamera.bOverride_DepthOfField = 0
        TargetCameraCameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalDistance = false
        TargetCameraCameraComponent.PostProcessSettings.bOverride_DepthOfFieldSensorWidth = false
        TargetCameraCameraComponent.PostProcessSettings.bOverride_DepthOfFieldFstop = false
    end
	
	
    if (self.SectionData.ImmediateCut == true) then
        --顺切，直接设置目标相机
        --Game.DialogueManager:SetCameraProxy(self.TargetCamera)
        if self.SectionData.bOpenDOF then
            local ppSettings = TargetCameraCameraComponent.PostProcessSettings
            if self.FocusActorDisatance then
                ppSettings.DepthOfFieldFocalDistance = self.FocusActorDisatance
            else
                ppSettings.DepthOfFieldFocalDistance = self.SectionData.DepthOfFieldFocalDistance
            end
            ppSettings.DepthOfFieldSensorWidth = self.SectionData.DepthOfFieldSensorWidth
            ppSettings.DepthOfFieldFstop = self.SectionData.DepthOfFieldFStop
        end
    else
        --self.TargetTransform = self.TargetCamera:GetTransform()
        self.TargetFOV = TargetCameraCameraComponent.FieldOfView
        if OpenDOF then
            self.TargetDepthOfFieldSensorWidth = self.SectionData.DepthOfFieldSensorWidth
            self.TargetDepthOfFieldFStop = self.SectionData.DepthOfFieldFStop
            if self.SectionData.DepthOfFieldFocalDistance > 0 then
                self.TargetDepthOfFieldFocalDistance = self.SectionData.DepthOfFieldFocalDistance
            else
                self.TargetDepthOfFieldFocalDistance = self.FocusActorDisatance
            end
        else
            local ppSettings = TargetCameraCameraComponent.PostProcessSettings
            self.TargetDepthOfFieldFocalDistance = ppSettings.DepthOfFieldFocalDistance
            self.TargetDepthOfFieldSensorWidth = ppSettings.DepthOfFieldSensorWidth
            self.TargetDepthOfFieldFStop = ppSettings.DepthOfFieldFstop
        end

        --DOF的开启必须立即设置
        local currentCameraComponent = self.CurrentCameraComponent
        if currentCameraComponent then
            currentCameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalDistance = OpenDOF
            currentCameraComponent.PostProcessSettings.bOverride_DepthOfFieldSensorWidth = OpenDOF
        end
		
    end
end

function ASI_DialogueAutoShot:Tick(DeltaTime)
end

function ASI_DialogueAutoShot:End()
end

function ASI_DialogueAutoShot:Pause(Pause)
end


function ASI_DialogueAutoShot:PreDealBreath()
    local CamBreathType = CameraBreathType
    local sectionData = self.SectionData
    if sectionData.CameraBreathType ~= CamBreathType.None then
        --self.Actor = Game.DialogueManager:GetCameraProxy()
		self.Actor = self.TargetCamera
        self.HitResult = import("HitResult")()

        local actor = self.Actor
        if sectionData.CameraBreathType == CamBreathType.RotateLeft
            or sectionData.CameraBreathType == CamBreathType.RotateRight
            or sectionData.CameraBreathType == CamBreathType.RotateTop
            or sectionData.CameraBreathType == CamBreathType.RotateBottom
        then
            self.RotateTargetPos = actor:K2_GetActorLocation() +
                actor:K2_GetRootComponent():GetForwardVector() * self.SectionData.BreathFocusDistance

            if Game.DialogueManager.EditorManager then
                --只在编辑器下才开启Debug视点绘制
                import("KismetSystemLibrary").DrawDebugSphere(
                    _G.GetContextObject(),
                    self.RotateTargetPos,
                    0.01, --radius
                    1,    --segments
                    FLinearColor(255, 0, 0),
                    self.SectionData.Duration,
                    1
                )
            end
        end
        self.UpVector = FVector(0, 0, 1)
        self.RightVector = actor:K2_GetRootComponent():GetRightVector()
        self.ForwardVector = actor:K2_GetRootComponent():GetForwardVector()
    end
end

function ASI_DialogueAutoShot:OnActionNoticeToSectionInstance(Param, InObject)
    if InObject then
        self.SectionData = InObject
    end

    local AutoCameraManager = Game.DialogueManager:GetAutoCameraManager()
    if Param == Enum.EAutoCameraNoticeLuaEvent.UpdateAutoCamera then
        AutoCameraManager:CalculateCameraParams(self, self.TargetCamera, self.SectionData)
        self:HandleCameraParam()
    elseif Param == Enum.EAutoCameraNoticeLuaEvent.UpdateSectionParams then
        AutoCameraManager:TransformToOneActor(self, self.TargetCamera,
            Game.DialogueManager.DialogueDirector.GameEntitiesList)
    elseif Param == Enum.EAutoCameraNoticeLuaEvent.UpdateTableParams then
        if self.SectionData.AutoCameraType == Enum.EAutoCameraType.OneActor then
            self:OnUseTemplateOnePerson(AutoCameraManager)
        elseif self.SectionData.AutoCameraType == Enum.EAutoCameraType.TwoActorTypeOne then
            self:OnUseTemplateTwoPersonTypeOne(AutoCameraManager)
        elseif self.SectionData.AutoCameraType == Enum.EAutoCameraType.TwoActorTypeTwo then
            self:OnUseTemplateTwoPersonTypeTwo(AutoCameraManager)
        end
    elseif Param == Enum.EAutoCameraNoticeLuaEvent.UpdateDoF then
        AutoCameraManager:SetFocusActorPostion(self.SectionData.DepthOfFieldFocusActor,
            self.TargetCamera, Game.DialogueManager.DialogueDirector.DialogueActors)
        self:HandleCameraParam()
    end
end

function ASI_DialogueAutoShot:OnUseTemplateOnePerson(AutoCameraManager)
    local Data = Game.TableData.GetDialogueAutoCameraOnePDataRow(
        self.SectionData.OnePersonTemplate + Enum.EAutoCameraTypeStartIndex.OneActor)

    local sectionData = self.SectionData
    sectionData.ActorFocusH = Data.H
    sectionData.ActorFocusV = Data.V
    sectionData.CameraFovX = Data.HFov
    sectionData.ActorFocusDist = Data.Dist
    sectionData.CameraYaw = Data.theta
    sectionData.CameraPitch = Data.Pitch

    AutoCameraManager:CalculateCameraParams(self, self.TargetCamera, sectionData)
    sectionData.SectionName = Data.AutoCameraCutDesc
    self:HandleCameraParam()
end

function ASI_DialogueAutoShot:OnUseTemplateTwoPersonTypeOne(AutoCameraManager)
    local Data = Game.TableData.GetDialogueAutoCameraTwoPTypeOneDataRow(
        self.SectionData.TwoPersonTypeOneTemplate + Enum.EAutoCameraTypeStartIndex.TwoActorTypeOne)

    local sectionData = self.SectionData
    sectionData.ActorFocusH = Data.H1
    sectionData.ActorFocusV = Data.V1
    sectionData.ActorBackendH = Data.H2
    sectionData.ActorBackendV = Data.V2
    sectionData.CameraFovX = Data.HFov
    sectionData.CameraYaw = Data.theta
    sectionData.SectionName = Data.AutoCameraCutDesc

    AutoCameraManager:CalculateCameraParams(self, self.TargetCamera, sectionData)
    self:HandleCameraParam()
end

function ASI_DialogueAutoShot:OnUseTemplateTwoPersonTypeTwo(AutoCameraManager)
    local Data = Game.TableData.GetDialogueAutoCameraTwoPTypeTwoDataRow(
        self.SectionData.TwoPersonTypeTwoTemplate + Enum.EAutoCameraTypeStartIndex.TwoActorTypeTwo)

    local sectionData = self.SectionData
    sectionData.ActorFocusH = Data.H1
    sectionData.ActorFocusV = Data.V1
    sectionData.ActorBackendH = Data.H2
    sectionData.CameraFovX = Data.HFov
    sectionData.CameraYaw = Data.theta
    sectionData.CameraPitch = Data.Pitch
    sectionData.SectionName = Data.AutoCameraCutDesc

    AutoCameraManager:CalculateCameraParams(self, self.TargetCamera, sectionData)
    self:HandleCameraParam()
end

function ASI_DialogueAutoShot.IsOnlyUnique()
    return true
end

return ASI_DialogueAutoShot
