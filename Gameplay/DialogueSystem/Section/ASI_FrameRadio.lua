
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_FrameRadio = DefineClass("ASI_FrameRadio", ASI_Base)


local screenx, screeny
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")
local EAspectRatioAxisConstraint = import("EAspectRatioAxisConstraint")


function ASI_FrameRadio:Init(InSectionData, InController)
end

function ASI_FrameRadio:ConstrainCurrentCamera(bVertical)
    if self.DialogueManager.EditorManager then
        --local CameraProxy = self.DialogueManager:GetCameraProxy()
        self.DialogueManager.EditorManager:AddCameraFrameRadio(bVertical)
    else
        local CameraComponent = self.DialogueManager.GameDialogueCamera:GetComponentByClass(import("CameraComponent"))
        CameraComponent.bOverrideAspectRatioAxisConstraint = true
        if bVertical then
            CameraComponent:SetAspectRatioAxisConstraint(EAspectRatioAxisConstraint.AspectRatio_MaintainYFOV)
        else
            CameraComponent:SetAspectRatioAxisConstraint(EAspectRatioAxisConstraint.AspectRatio_MaintainXFOV)
        end
    end  
end

function ASI_FrameRadio:UnConstrainCurrentCamera()
    if self.DialogueManager.EditorManager then
        --local CameraProxy = self.DialogueManager:GetCameraProxy()
        self.DialogueManager.EditorManager:RemoveCameraFrameRadio()
        -- self.DialogueManager.EditorManager:AddCameraShake(CameraShakeClass, CameraProxy.CameraShakeSource)
    else
        local CameraComponent = self.DialogueManager.GameDialogueCamera:GetComponentByClass(import("CameraComponent"))
        CameraComponent.bOverrideAspectRatioAxisConstraint = false
    end  
end

function ASI_FrameRadio:OnViewportResize(x, y)
    screenx = x
    screeny = y
    local width = x
    local height = y
    local newwidth = self.constrainWidth
    local newHeight = self.constrainHeight
    local scalex = width/newwidth
    local scaley = height/newHeight
    local name
    local director = Game.CinematicManager and Game.CinematicManager.CutsceneManager:GetDirector() or nil
    local world = _G.StoryEditor and _G.ContextObject or slua.getWorld()
    if self.Kind == Enum.EMovieFrameRadio.CIRCLE then
        name = "P_NPCCSBg_Circle"
    else
        if scalex > scaley then
            name = "P_NPCCSBg_Vertically"
            self:ConstrainCurrentCamera(true, world, director)
        else
            name = "P_NPCCSBg"
            self:ConstrainCurrentCamera(false, world, director)
        end
    end
    if self.uiname == name then
        if name == "P_NPCCSBg_Circle" then
            UI.Invoke("P_NPCCSBg_Circle", "OnViewportResize", screenx, screeny, self.scale)
        end
        return
    end
    UI.HideUI(self.uiname)
    if _G.StoryEditor then
        UI.ShowUIInEditor(name, self.Kind, self.Btransition, self.Color, self.cWidth, self.cHeight, self.scale)
    else
        UI.ShowUI(name, self.Kind, self.Btransition, self.Color, self.cWidth, self.cHeight, self.scale)
    end
    self.uiname = name
    self.width = width
    self.height = height
end

function ASI_FrameRadio:Start()
    if not self.bshow then
        Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_VIEWPORT_RESIZED, "OnViewportResize", self)
        local customData = self.SectionData
        local name
        local world = _G.StoryEditor and _G.ContextObject or slua.getWorld()
        local size = _G.StoryEditor and Game.GameInstance:GetViewportSize() or WidgetLayoutLibrary.GetViewportSize(world)
        local width = screenx or size.X
        local height = screeny or size.Y
        local newwidth = customData.Width
        local newHeight = customData.Height 
        local scalex = width/newwidth
        local scaley = height/newHeight
        local director = Game.CinematicManager and Game.CinematicManager.CutsceneManager:GetDirector() or nil
        if customData.Kind == Enum.EMovieFrameRadio.CIRCLE then
            name = "P_NPCCSBg_Circle"
        else
            if scalex > scaley then
                name = "P_NPCCSBg_Vertically"
                self:ConstrainCurrentCamera(true, world, director)
            else
                name = "P_NPCCSBg"
                self:ConstrainCurrentCamera(false, world, director)
            end
        end
        
        if self.uiname == name then 
            return
        end
        if _G.StoryEditor then
            if name == "P_NPCCSBg_Circle" then
                UI.ShowUIInEditor(name, customData.Kind, customData.Btransition, customData.Color, width, height, customData.Scale)
                self.scale = customData.Scale
            else
                UI.ShowUIInEditor(name, customData.Kind, customData.Btransition, customData.Color, customData.Width, customData.Height)
            end
        else
            if name == "P_NPCCSBg_Circle" then
                UI.ShowUI(name, customData.Kind, customData.Btransition, customData.Color, width, height, customData.Scale)
                self.scale = customData.Scale
            else
                UI.ShowUI(name, customData.Kind, customData.Btransition, customData.Color, customData.Width, customData.Height)
            end
        end
        self.uiname = name
        self.width = width
        self.height = height
        self.constrainWidth = newwidth
        self.constrainHeight = newHeight
        self.Kind = customData.Kind
        self.Btransition = customData.Btransition
        self.Color = customData.Color
        self.cWidth = customData.Width
        self.cHeight = customData.Height
        -- self.section = section
        self.bshow = true
    end
end

function ASI_FrameRadio:End()
    if not self.bshow then return end
        Game.GlobalEventSystem:RemoveListener(EEventTypesV2.ON_VIEWPORT_RESIZED, "OnViewportResize", self)
        if not self.uiname then return end
        UI.Invoke(self.uiname, "StartPlayCloseAnim")
        self.uiname = nil
        self.bshow = false
        self:UnConstrainCurrentCamera()
end

function ASI_FrameRadio:Pause(Pause)
end

function ASI_FrameRadio.IsOnlyUnique()
    return true
end

return ASI_FrameRadio