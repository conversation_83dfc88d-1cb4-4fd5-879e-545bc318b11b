local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_QTE = DefineClass("ASI_QTE", ASI_Base)
local GameplayStatics = import("GameplayStatics")
local KismetMathLibrary = import("KismetMathLibrary")
local ULLFunc = import("LowLevelFunctions")

function ASI_QTE:Init(InSectionData, InController)
end


function ASI_QTE:Start()
    Log.DebugFormat("ASI_QTE:Start")

    self.SecondCheck = 0 --1秒1次
    self.QtePostProcessIndex = self.DialogueManager.QTEMaterialIndex 

    self.PPFadeInOutTime = 0.01
    self.PPFadeInOutCurrent = 0

    self.PPDensityStart = 0   
    self.PPDensityCurrent = 0
    self.PPDensityTarget = 1.5
    self.PPLerp = 0

    self.DialogueManager:SetDialoguePause(true)

    self.DialogueManager:ClearQTEClickData()
    self.DialogueManager:SetQTESuccess(false)
    
    self.DialogueManager:AddCameraPostProcess(self.QtePostProcessIndex, 0)
    --self.DialogueManager.P_Dialogue:SpecialCanvasFadeInOut(true)
    self.DialogueManager.P_Dialogue:ShowQTE()

    self.CameraManager = Game.CameraManager
    if(self.CameraManager) then
        --Runtime要打开相机后处理效果
        self.CameraManager:SetEnableCameraModifier(true)
    end

    self.DialogueManager.QTETaskPtr = self
end

function ASI_QTE:Tick(DeltaTime)

end

function ASI_QTE:LerpPP(DeltaTime)
    if self.PPDensityCurrent ~= self.PPDensityTarget then
        self.PPLerp = math.clamp(self.PPLerp + DeltaTime, 0, 1)
        self.PPDensityCurrent = KismetMathLibrary.Lerp(self.PPDensityStart, self.PPDensityTarget, self.PPLerp)
        self.DialogueManager:SetCameraPostProcessParamter(self.QtePostProcessIndex, "Density", self.PPDensityCurrent)
        if self.PPDensityCurrent == self.PPDensityTarget then
            --Lerp到目标PP以后，在没有外界的继续点击情况下，需要自动复原
            self:SetTargetPPDensity(1.5)
        end
    end
end

function ASI_QTE:SetTargetPPDensity(Density)
    self.PPDensityStart = self.PPDensityCurrent
    self.PPDensityTarget = Density
    self.PPLerp = 0
end

--查询过去N秒内的点击数
function ASI_QTE:getLastNSecondClickTimes(second)
    --获取最后一次点击的时间戳
    local newestClickTime = ULLFunc.GetUtcMillisecond()

    local lastNSecondClickCount = 0

    local DataLength = #self.DialogueManager.QTEClickData 

    for i = DataLength, 1, -1 do
        local currentClickTime = self.DialogueManager.QTEClickData[i]
        local offsetTime = newestClickTime - currentClickTime  
        if  offsetTime < second then
            --本次点击时间和最后一次相差在N秒以内
            lastNSecondClickCount = lastNSecondClickCount + 1
        else
            break
        end
    end
    return lastNSecondClickCount
end

function ASI_QTE:PauseTick(DeltaTime)
    if self.PPFadeInOutCurrent ~= self.PPFadeInOutTime then
        --有淡入时间
        self.PPFadeInOutCurrent = math.clamp(self.PPFadeInOutCurrent + DeltaTime, 0, self.PPFadeInOutTime)
        local Weight = self.PPFadeInOutCurrent / self.PPFadeInOutTime

        Game.DialogueManager:SetCameraPostProcessWeight(self.QtePostProcessIndex, Weight)
    end

    self:LerpPP(DeltaTime)
    self:CheckSuccess(DeltaTime)
end

function ASI_QTE:CheckSuccess(DeltaTime)
    self.SecondCheck = self.SecondCheck + DeltaTime
    if self.SecondCheck >= self.SectionData.InValidTime then
        Log.DebugFormat("qte time up ,you fail")            
        self.DialogueManager:SetQTESuccess(false)
        self:endQTE()
        return
    end

    --获取从当前秒到之前一秒内的点击数
    local lastSecondClickCount = self:getLastNSecondClickTimes(1000)
    if lastSecondClickCount >= self.SectionData.LastSecondClickTimes then
        --向前搜寻n秒，计算累计次数
        local lastNSecondClickCount = self:getLastNSecondClickTimes(1000*self.SectionData.ConstantClickTime)
        if lastNSecondClickCount >= self.SectionData.ClickTotalTimes then
            Log.DebugFormat("qte success,last second Click Count is %d, last %d seconds click count is %d", 
                    lastSecondClickCount, self.SectionData.ConstantClickTime, lastNSecondClickCount)
            self.DialogueManager:SetQTESuccess(true)        
            self:endQTE()
        end
    end
end

function ASI_QTE:endQTE()
    self.DialogueManager:RemoveCameraPostProcess(self.QtePostProcessIndex)  
    self.DialogueManager.P_Dialogue:RemoveQTE()
    self.DialogueManager:SetDialoguePause(false)
end

function ASI_QTE:TryShakeCamera()
    --获取过去0.3秒内的点击数
    local LastTimes = self:getLastNSecondClickTimes(1000)

    Log.DebugFormat("Last Second Click Times %d", LastTimes)
    local CameraShakeClass = nil
    if LastTimes >= 6 then
        CameraShakeClass = self.SectionData.CameraShake[3]
        self:SetTargetPPDensity(5)
    elseif LastTimes >= 3 then
        CameraShakeClass = self.SectionData.CameraShake[2]
        self:SetTargetPPDensity(3)
    elseif LastTimes >= 1 then
        CameraShakeClass = self.SectionData.CameraShake[1]
        self:SetTargetPPDensity(1.5)
    end
    if CameraShakeClass then
        self:ShakeCamera(CameraShakeClass)   
    end
end

function ASI_QTE:ShakeCamera(AssetPath)
	
	Game.CameraManager:StartCameraShakeByAssetPath(AssetPath, 1.0,0)
    --if self.DialogueManager.EditorManager then
        --local CameraProxy = self.DialogueManager:GetCameraProxy()
        --self.DialogueManager.EditorManager:AddCameraShake(CameraShakeClass, CameraProxy.CameraShakeSource)
    --else
    --    if self.DialogueManager.GameDialogueCamera.CameraShakeSource then
    --        self.DialogueManager.GameDialogueCamera.CameraShakeSource:StartCameraShake(CameraShakeClass, 1.0, 0, FRotator())
    --    end
    --end    
end

function ASI_QTE:End()
    Log.DebugFormat("ASI_QTE:End")

    --为什么成功或者失败的时候已经调用endQTE了，这里还要调一次？这是为了防止对话意外终结，成功或者失败的endQTE都未被调用，但End接口一定会被调用
    --endQTE调用的接口都做了二次调用保护
    self:endQTE()
    if(self.CameraManager) then
        --Runtime要打开相机后处理效果
        self.CameraManager:SetEnableCameraModifier(false)
    end
end

function ASI_QTE:Pause(Pause)
end

function ASI_QTE.IsOnlyUnique()
    return true
end

return ASI_QTE