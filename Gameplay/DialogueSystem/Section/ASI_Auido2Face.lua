local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_Auido2Face = DefineClass("ASI_Auido2Face", ASI_Base)
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

function ASI_Auido2Face:Init(InSectionData, InController)
    self.FaceAnimCom = nil
end


function ASI_Auido2Face:Start()
    local SectionData = self.SectionData
    local Actor = self:GetTrackActor()
    if Actor == nil or Actor:GetMainMesh() == nil then
        return 
    end

    self.Sex = 2
    local SelfEntity = self:GetDialogueEntity()
    if not SelfEntity then
        Log.WarningFormat("entity invalid!")
        return
    end

    if SelfEntity and SelfEntity.bIsPlayer then
        local GameEntity = self:GetGameEntity()
        if GameEntity and GameEntity.Sex then
            self.Sex = GameEntity.Sex
        end
    end

    local FaceAnimID = SectionData.FaceAnimID

    -- todo 巍巍后面统一FaceAnimComponent的生命周期后, 进行清理 @巍巍 20240910
    local FaceAnimClass  = slua.loadClass(ViewResourceConst.FACE_ANIM_COMPONENT_BP_PATH)
    if import("KismetSystemLibrary").IsValidClass(FaceAnimClass) then
        self.FaceAnimCom =  Actor:GetComponentByClass(FaceAnimClass)
        if self.FaceAnimCom == nil then
            self.FaceAnimCom = FaceAnimClass(Actor)
            if self.FaceAnimCom then
                import("LuaHelper").RegisterComponent(self.FaceAnimCom)
            end
        end

        if IsValid_L(self.FaceAnimCom) then
            self.FaceAnimCom:PlayLipFaceAnim(Actor:GetMainMesh(), FaceAnimID, SectionData.FixTime, self.Sex)
        end
    end
end

function ASI_Auido2Face:Tick(DeltaTime)
    if IsValid_L(self.FaceAnimCom) then
        self.FaceAnimCom:SetLipBlend(self.SectionData.LipBlend)
    end
end

function ASI_Auido2Face:End()
    self.FaceAnimCom = nil
end

function ASI_Auido2Face:Pause(Pause)
end

return ASI_Auido2Face