---
--- 可以进入简易对话相机Mode的Section, 做法比较Trick,
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/3/3 11:52
---
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")

---@class ASI_SimpleCameraMode : ASI_Base
local ASI_SimpleCameraMode = DefineClass("ASI_SimpleCameraMode", ASI_Base)

function ASI_SimpleCameraMode:Init(InSectionData, InController)

end

function ASI_SimpleCameraMode:Start()
    local director = self:GetDialogueDirector()
    if (director == nil) or (director.DialogueTable == nil) then
        Log.Warning("dialogue data invalid")
        return
    end

    if Game.DialogueManager.bSimpleDialogue then
        -- 简易对话模式下,不是第一段不执行该Section
        local curEpisodeIdx = director:GetCurrentEpisodeIdx()
        if curEpisodeIdx ~= 1 then
            return
        end
    end

    local primaryEntity = director:FindDialogueGameEntity(self.SectionData.PrimaryActor)
    local secondaryEntity = director:FindDialogueGameEntity(self.SectionData.SecondaryActor)
    Game.CameraManager:EnableSimpleDialogueCamera(true, primaryEntity, secondaryEntity)
end

function ASI_SimpleCameraMode:End()
    -- 简易对话模式下, 跟着对话一起退出
    if self.SectionData.bExitOnEnd then
        Game.CameraManager:EnableSimpleDialogueCamera(false)
    end
end

function ASI_SimpleCameraMode:Pause(Pause)

end

return ASI_SimpleCameraMode
