local KismetMathLibrary = import("KismetMathLibrary")
local DialogueEntityInterface = kg_require("Gameplay.DialogueSystem.DialogueEntityInterface")
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")

-- 尽量减少对该Section的维护,普通移动尽量走ASI_NewActorMovement
---@class ASI_Transform : ASI_Base
local ASI_Transform = DefineClass("ASI_Transform", ASI_Base)

function ASI_Transform:Init(InSectionData, InController)
    self.HitResult = import("HitResult")()
end

function ASI_Transform:Start()
    local DialogueDirector = self:GetDialogueDirector()
    if not DialogueDirector then
        return
    end

    local selfGameEntity = self:GetGameEntity()

    if not selfGameEntity then
        Log.WarningFormat("will not move , because has no self dialogue entity ")
        return 
    end
    
    self.TrackActor = self:GetTrackActor()
    if not IsValid_L(self.TrackActor) then  
        Log.WarningFormat("%s will not move , because has not actor ")
        return
    end

    --优先使用MoveTarget设置
    local MoveTarget = self.SectionData.MoveTarget
    if MoveTarget == "" or MoveTarget == "None" then
        --如果为空，则使用旧的TargetActor，为了兼容
        MoveTarget = self.SectionData.TargetActor
    end
    
    self.TargetGameEntity = DialogueDirector:FindDialogueGameEntity(MoveTarget)
    if not self.TargetGameEntity then
        Log.WarningFormat("%s will not move , because has no target game entity", selfGameEntity.DialogueEntityTable.TrackName)
        return
    end
    local TargetActor = self.TargetGameEntity:GetActor()
    if not IsValid_L(TargetActor) then
        Log.WarningFormat("%s will not move , because has no target actor", selfGameEntity.DialogueEntityTable.TrackName)
        return
    end

    local bFixedRotation = self.SectionData.bFixedRotation or false
    self.TargetTransform = TargetActor:GetTransform()
    self.TargetLocation = TargetActor:K2_GetActorLocation()

    if (self.SectionData.Immediate == true) then
        --立即移动，不需要再进行走路动画
        --瞬移需要使用目标的旋转信息
        self:SetActorNewLocation(self.TargetTransform:GetTranslation())
        if not bFixedRotation then
            self.TrackActor:K2_SetActorRotation(self.TargetTransform:Rotator(), false)
        end
    else
        self.OriginLocation = self.TrackActor:K2_GetActorLocation()
        --平滑移动，不需要使用目标点的旋转信息，直接设置走路时的朝向
        if string.find(self:GetDialogueEntity().ObjectClass,"DialogueActor") then
            --说明这个移动是属于角色的，则需要处理走路动画

            if not bFixedRotation then
                --移动前就设置好朝向
                local newRotation = KismetMathLibrary.FindLookAtRotation(self.TrackActor:K2_GetActorLocation(), TargetActor:K2_GetActorLocation())
                local TargetRot = FRotator(0.0, newRotation.Yaw, 0.0)

                self.TrackActor:K2_SetActorRotation(TargetRot,false)
            end

            local CachedMeshComp = self.TrackActor:GetComponentByClass(import("SkeletalMeshComponent"))
            if CachedMeshComp then
                self:GetDialogueDirector():EntityLookAtEntityRequest(selfGameEntity, nil, 0, nil, false)

                -- 获取要播放的AnimAssetID
                local animAssetID = ""
                if self.SectionData.SpecificAnim and self.SectionData.SpecificAnim.AssetID ~= "" then
                    animAssetID = self.SectionData.SpecificAnim.AssetID
                elseif self.SectionData.Run then
                    animAssetID = "Run"
                else
                    animAssetID = "Walk"
                end

                local realGameEntity = self:GetRealGameEntity()
                if (realGameEntity ~= nil) and (realGameEntity.SetMoveByThrusterWithoutAnimRM ~= nil) then
                    -- 禁用rootmotion中的根位移量累加
                    realGameEntity:SetMoveByThrusterWithoutAnimRM()
                end

                -- TODO:use 3C interface
                DialogueEntityInterface.InnerPlayAnimation(self:GetGameEntity(), animAssetID, true, 0.1, 0)
            end
        end
    end
end

function ASI_Transform:Tick(DeltaTime)
    if(self.SectionData.Immediate == false and self.TargetGameEntity) then
        --非顺切，平滑过度，计算系数
        local Ratio = self.RunningTime / self.SectionData.Duration
        if(Ratio > 1)then
             Ratio = 1
        end

        if self.OriginLocation and self.TargetLocation then
            local newLocation = KismetMathLibrary.VLerp(self.OriginLocation, self.TargetLocation, Ratio)
            self:SetActorNewLocation(newLocation)
        end
    end
end

function ASI_Transform:SetActorNewLocation(newLocation)
    if not IsValid_L(self.TrackActor) then  
        return
    end

    if self.SectionData.StickGround == true then
        local startPos = newLocation + FVector(0,0,200)
        local endPos = newLocation + FVector(0,0,-200)
        local Location = Game.DialogueManager.DialogueUtil.TraceObject(self.TrackActor, startPos, endPos, 0, nil, nil, {self.TrackActor})
        if Location then
            local HalfHeight = Game.EntityManager:getEntity(self.TrackActor:GetEntityUID()).CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()

            Location.Z = Location.Z + HalfHeight
            newLocation = Location
        else
            Log.WarningFormat("Can Not Find Ground Under Actor %s", self.TrackActor:GetName())
        end
    end
    self.TrackActor:K2_SetActorLocation(newLocation, false, self.HitResult, false)
end

function ASI_Transform:End()
    DialogueEntityInterface.PlayIdle(self:GetGameEntity(), 0.3)

    local realGameEntity = self:GetRealGameEntity()
    if (realGameEntity ~= nil) and (realGameEntity.CancelSetLocoSourceModeToAnimRootMotion ~= nil) then
        realGameEntity:CancelSetLocoSourceModeToAnimRootMotion()
    end
end

function ASI_Transform:Pause(Pause)
end

return ASI_Transform
