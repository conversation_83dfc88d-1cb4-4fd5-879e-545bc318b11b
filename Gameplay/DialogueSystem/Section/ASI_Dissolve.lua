local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local MaterialEffectConst = kg_require("Gameplay.Effect.MaterialEffectConst")
local DISSOLVE_MATERIAL_EFFECT_TYPE = MaterialEffectConst.DISSOLVE_MATERIAL_EFFECT_TYPE

local ASI_Dissolve = DefineClass("ASI_Dissolve", ASI_Base)

function ASI_Dissolve:Init(InSectionData, InController)
end


function ASI_Dissolve:Start()
    local SectionData = self.SectionData
    local Entity = self:GetRealGameEntity()
    if Entity == nil then
        return 
    end

    Entity:StartDissolveMaterialEffect(DISSOLVE_MATERIAL_EFFECT_TYPE.CHARACTER_NOISE_OUT, SectionData.DissolveTime)
end

function ASI_Dissolve:Tick(DeltaTime)

end

function ASI_Dissolve:End()

end

function ASI_Dissolve:Pause(Pause)
end

return ASI_Dissolve