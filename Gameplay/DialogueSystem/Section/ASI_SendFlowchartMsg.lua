---
--- Created by shijin<PERSON><PERSON><PERSON>
--- DateTime: 2024/7/9 11:13
---
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")

local ASI_SendFlowchartMsg = DefineClass("ASI_SendFlowchartMsg", ASI_Base)

function ASI_SendFlowchartMsg:Init(InSectionData, InController)

end

function ASI_SendFlowchartMsg:Start()
    if self.SectionData.bSendAtFinish then
        self.DialogueManager:CacheFlowchartMsg(self.SectionData.Msg)
        return
    end

    self.DialogueManager:RealSendFlowchartMsg(self.SectionData.Msg)
end

function ASI_SendFlowchartMsg:Pause()
    -- do nothing
end

function ASI_SendFlowchartMsg:End()
    -- do nothing
end

function ASI_SendFlowchartMsg.IsOnlyUnique()
    return true
end

return ASI_SendFlowchartMsg
