
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_FadeInOutPureColor = DefineClass("ASI_FadeInOutPureColor", ASI_Base)


function ASI_FadeInOutPureColor:Init(InSectionData, InController)
end


function ASI_FadeInOutPureColor:Start()
    self.DialogueManager:SetPanelPureFadeColor(self.SectionData.Color)

    --FadeInTime为0，不透明度立即设置为1
    if(self.SectionData.FadeInTime <= 0) then
        self.DialogueManager:SetPanelPureFadeAlpha(1)
    end
end

function ASI_FadeInOutPureColor:Tick(DeltaTime)
    if self.SectionData.FadeInTime > 0 and self.RunningTime <= self.SectionData.FadeInTime then
        --正在淡入
        local Alpha = self.RunningTime / self.SectionData.FadeInTime
        self.DialogueManager:SetPanelPureFadeAlpha(Alpha)
    elseif( self.SectionData.FadeOutTime > 0 and self.RunningTime >= (self.SectionData.Duration - self.SectionData.FadeOutTime)) then
        --开始淡出
        local Alpha = (self.SectionData.Duration - self.RunningTime) / self.SectionData.FadeOutTime
        self.DialogueManager:SetPanelPureFadeAlpha(Alpha)
    else
        self.DialogueManager:SetPanelPureFadeAlpha(1)
    end
end

function ASI_FadeInOutPureColor:End()
    self.DialogueManager:SetPanelPureFadeAlpha(0)
end

function ASI_FadeInOutPureColor:Pause(Pause)
end

function ASI_FadeInOutPureColor.IsOnlyUnique()
    return true
end

return ASI_FadeInOutPureColor