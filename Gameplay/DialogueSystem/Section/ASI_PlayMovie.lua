
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_PlayMovie = DefineClass("ASI_PlayMovie", ASI_Base)


function ASI_PlayMovie:Init(InSectionData, InController)
end


function ASI_PlayMovie:Start()
    Game.DialogueManager:PlayUIMovie(self.SectionData.MoviePath, self.SectionData.CanSkip)

    --播放Movie的时候，对话要暂停
    Game.DialogueManager:SetDialoguePause(true)
end

function ASI_PlayMovie:End()
end

function ASI_PlayMovie:Pause(Pause)
end

function ASI_PlayMovie.IsOnlyUnique()
    return true
end

return ASI_PlayMovie