
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_EpisodeDuration = DefineClass("ASI_EpisodeDuration", ASI_Base)


function ASI_EpisodeDuration:Init(InSectionData, InController)
end


function ASI_EpisodeDuration:Start()
end

function ASI_EpisodeDuration:Tick(DeltaTime)
end


function ASI_EpisodeDuration:End()
end

function ASI_EpisodeDuration:Pause(Pause)
end

function ASI_EpisodeDuration.IsOnlyUnique()
    return false
end

return ASI_EpisodeDuration