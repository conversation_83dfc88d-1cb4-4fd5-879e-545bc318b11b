---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/2/14 17:41
---
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")

---@class ASI_NewAttachNiagara : ASI_Base
local ASI_NewAttachNiagara = DefineClass("ASI_NewAttachNiagara", ASI_Base)


function ASI_NewAttachNiagara:Init(InSectionData, InController)
    self.niagaraID = 0
end

function ASI_NewAttachNiagara:Start()
    if string.isEmpty(self.SectionData.Niagara) then
        return
    end

    local ownerEntity = self:GetRealGameEntity()
    if not ownerEntity then
        return
    end

    local sectionData = self.SectionData
    local pos = sectionData.Translation
    local rot = sectionData.Rotator
    local scale = sectionData.Scale
    local m3dTransform = M3D.Transform(
        M3D.Vec3(pos.X, pos.Y, pos.Z),
        M3D.Rotator(rot.Pitch, rot.Yaw, rot.Roll),
        M3D.Vec3(scale.X, scale.Y, scale.Z)
    )

    local effectParam = NiagaraEffectParamTemplate.AllocFromPool()
    effectParam.bFollowHidden = sectionData.bFollowHidden
    effectParam.NiagaraEffectPath = sectionData.Niagara
    effectParam.bNeedAttach = true
    effectParam.AttachPointName = sectionData.AttachPoint or ""
    effectParam.bAbsoluteRotation = false
    effectParam.bAbsoluteScale = false
    M3D.ToTransform(m3dTransform, effectParam.SpawnTrans)

    self.niagaraID = ownerEntity:PlayNiagaraEffect(effectParam)
end

function ASI_NewAttachNiagara:End()
    if self.niagaraID == 0 then
        return
    end

    local ownerEntity = self:GetRealGameEntity()
    if not ownerEntity then
        return
    end

    ownerEntity:DestroyNiagaraSystem(self.niagaraID)
    self.niagaraID = 0
end

function ASI_NewAttachNiagara:Tick(DeltaTime)

end

function ASI_NewAttachNiagara:Pause(Pause)

end

return ASI_NewAttachNiagara
