local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_SetMaterialTexture = DefineClass("ASI_SetMaterialTexture", ASI_Base)
local SkeletalMeshComponent = import("SkeletalMeshComponent")
local MaterialInstanceDynamic = import("MaterialInstanceDynamic")
local KismetMaterialLibrary = import("KismetMaterialLibrary")
local EMIDCreationFlags = import("EMIDCreationFlags")


function ASI_SetMaterialTexture:Init(InSectionData, InController)
end


function ASI_SetMaterialTexture:Start()
    local Actor = self:GetTrackActor()
    if not IsValid_L(Actor) then
        Log.WarningFormat("Actor is nil ")
        return
    end
    local SkelMesh = Actor:GetComponentByClass(SkeletalMeshComponent)
    if SkelMesh then
        self.CachedSkelComp = SkelMesh

        self.CachedNewMaterial = nil
        local MaterialIdxID = SkelMesh:GetMaterialIndex(self.SectionData.MaterialSlotName)
        if MaterialIdxID>0 then
            self.CachedNewMaterial = SkelMesh:GetMaterial(MaterialIdxID)
        end

        if not self.CachedNewMaterial then
            return
        end

        --切换对应贴图

        local Texture = slua.loadObject(self.SectionData.TextureAsset)
        if self.SectionData.TextureParamName ~= "" and Texture then
            self.MDCache = self.CachedNewMaterial:Cast(MaterialInstanceDynamic)
            if self.MDCache ==nil then
                --如果不是动态材质，尝试创建一个
                self.MDCache = KismetMaterialLibrary.CreateDynamicMaterialInstance(GetContextObject(), self.CachedNewMaterial, "", EMIDCreationFlags.Transient)
                if self.MDCache then
                    self.CachedSkelComp:SetMaterial(MaterialIdxID,self.MDCache)
                end
            end


            if self.MDCache then
                self.OldTexture = self.MDCache:K2_GetTextureParameterValue(self.SectionData.TextureParamName)
                self.OldAlpha = self.MDCache:K2_GetScalarParameterValue(self.SectionData.FadeParamName)
                self.MDCache:SetTextureParameterValue(self.SectionData.TextureParamName,Texture)
            end
        end

        if self.SectionData.FadeInTime>0 and  self.MDCache then
            self.MDCache:SetScalarParameterValue(self.SectionData.FadeParamName,0)
        end

        if self.SectionData then
            self.FadeOutStartTime = self.SectionData.Duration -  self.SectionData.FadeOutTime
        end
    end
end

function ASI_SetMaterialTexture:Tick(DeltaTime)
    --Log.Debug("ASI_SetMaterialTexture Tick",self.RunningTime)
    self.bFading = true
    if self.MDCache  then
        if self.RunningTime < self.SectionData.FadeInTime then
            local AlphaValue = math.min(self.RunningTime/ self.SectionData.FadeInTime,1)
            self.MDCache:SetScalarParameterValue(self.SectionData.FadeParamName,AlphaValue)
            --Log.Debug("FadeIn",AlphaValue)
            self.bFading = true
        elseif self.RunningTime > self.FadeOutStartTime then
            local AlphaValue = math.max(1 - (self.RunningTime - self.FadeOutStartTime)/ self.SectionData.FadeInTime,0)
            self.MDCache:SetScalarParameterValue(self.SectionData.FadeParamName,AlphaValue)
            --Log.Debug("FadeOut",AlphaValue)
            self.bFading = true
        elseif self.bFading then
            self.MDCache:SetScalarParameterValue(self.SectionData.FadeParamName,1)
            self.bFading = false
        end
    end

end


function ASI_SetMaterialTexture:End()
    if self.CachedSkelComp then
        if self.OldTexture and self.MDCache then
            self.MDCache:SetTextureParameterValue(self.SectionData.TextureParamName,self.OldTexture)
        end
        if self.OldAlpha and self.MDCache then
            self.MDCache:SetScalarParameterValue(self.SectionData.FadeParamName,self.OldAlpha)
        end

    end
end

function ASI_SetMaterialTexture:Pause(Pause)
end

function ASI_SetMaterialTexture:PlayAnimation(Animation)
end

return ASI_SetMaterialTexture