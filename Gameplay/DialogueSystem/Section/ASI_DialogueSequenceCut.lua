local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
---@class ASI_DialogueSequenceCut : ASI_Base

local ASI_DialogueSequenceCut = DefineClass("ASI_DialogueSequenceCut", ASI_Base)

function ASI_DialogueSequenceCut:Init(InSectionData, InController)
	self.LoadHandelID = 0
end

function ASI_DialogueSequenceCut:Start()
    self.PlayerTrackName  = ""
    local SectionData = self.SectionData
    self.SequenceTableData = Game.TableData.GetDilaogueSeuqenceDataRow(SectionData.SequenceAssetID)
    if not self.SequenceTableData then
        return 
    end

    self.PlayParams = {
        AssetID = SectionData.SequenceAssetID,
        bBindCamera = self.SectionData.UseSequenceCamera,
    }
    local DialogueSequenceManager = Game.DialogueManager:GetDialogueSequenceManager()
	self.LoadHandelID =  DialogueSequenceManager:FindTaskLoadHandleIDByAssetID(SectionData.SequenceAssetID)
    -- 如果已经加载好了就直接播放 没有加载好就重新加载
    if self.LoadHandelID then
        DialogueSequenceManager:LevelSequencePlay(self.LoadHandelID)
    else
        DialogueSequenceManager:PreLoadLevelSequence(self.PlayParams, true)
    end
end



function ASI_DialogueSequenceCut:Tick(DeltaTime)

end

function ASI_DialogueSequenceCut:End()
    --self:ClearSequence()
    local DialogueSequenceManager = Game.DialogueManager:GetDialogueSequenceManager()
    DialogueSequenceManager:StopLevelSequence(self.LoadHandelID)
    --local PlayerEntity =  Game.DialogueManager.DialogueDirector:FindDialogueGameEntity(self.PlayerTrackName)
    -- if PlayerEntity then
    --     DialogueEntityInterface.SetVisible(PlayerEntity, true)
    -- end
end


function ASI_DialogueSequenceCut:Pause(Pause)
end



return ASI_DialogueSequenceCut
