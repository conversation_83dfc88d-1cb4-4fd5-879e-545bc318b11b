local UC7FunctionLibrary = import("C7FunctionLibrary")
local USkeletalMeshComponent = import("SkeletalMeshComponent")
local KismetMathLibrary = import("KismetMathLibrary")
local EViewTargetBlendFunction = import("EViewTargetBlendFunction")

local DialogueUtil = kg_require("Gameplay.DialogueSystem.DialogueUtil")
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")

---@class ASI_CameraCut : ASI_Base
local ASI_CameraCut = DefineClass("ASI_CameraCut", ASI_Base)

function ASI_CameraCut:Init(InSectionData, InController)
    self.DrawCenter = nil
    self.CurrentCameraComponent = nil
    self.TargetCamera = nil
    self.IgnorePause = true
    self.TargetActorDistance = nil
end

function ASI_CameraCut:Start()
    local SectionData = self.SectionData

    local CameraName = SectionData.TargetCamera
    if not CameraName or CameraName == "" then
        --兼容旧数据
        CameraName = SectionData.CameraName
    end

    local director = self:GetDialogueDirector()

    local CameraGameEntity = director:FindDialogueGameEntity(CameraName)
    if not CameraGameEntity then
        Log.WarningFormat("Can not Find Camera Entity: %s", CameraName) 
        return
    end

    self.TargetCamera = CameraGameEntity:GetActor()
    if not IsValid_L(self.TargetCamera) then
        Log.WarningFormat("Target Camera is nil: %s", CameraName) 
        return
    end

    local CameraEntityTable = CameraGameEntity.DialogueEntityTable

    self:tryResetCameraTransform(CameraEntityTable, self.TargetCamera)

    if CameraEntityTable.DepthOfFieldFocusActor and CameraEntityTable.DepthOfFieldFocusActor ~= "" and CameraEntityTable.DepthOfFieldFocusActor ~= "None" then
        self.TargetActorDistance = self:GetFocusActorDistance(CameraEntityTable.DepthOfFieldFocusActor, self.TargetCamera)
    end
    
    --需要在自动相机设置前记录一下之前的相机参数 否则自动相机设置的TargetCamera可能跟CurrentCamera是同一个
    local CurrentCamera = self.TargetCamera
	if(CurrentCamera ~= nil) then
		if not _G.StoryEditor and self.DialogueManager.NeedBlendInCamera and self.SectionData == self.ActionTrack.ActionSections[1] then
			--需要BlendIn，则忽略第一个机位

			Game.CameraManager.PlayerController:SetViewTargetWithBlend(self.TargetCamera,
				1, EViewTargetBlendFunction.VTBlend_Linear, 3, true)
		else
			if not self.SectionData.ImmediateCut  and  self.SectionData.Duration then
				Game.CameraManager.PlayerController:SetViewTargetWithBlend(self.TargetCamera,
					self.SectionData.Duration, EViewTargetBlendFunction.VTBlend_Linear, 3, true)
			else
				Game.CameraManager.PlayerController:SetViewTargetWithBlend(self.TargetCamera, 0, 0, 0, false)
			end
		end

		if self.TargetActorDistance then
			local TargetCameraCameraComponent = self.TargetCamera:GetComponentByClass(import("CameraComponent"))
			TargetCameraCameraComponent.PostProcessSettings.DepthOfFieldFocalDistance = self.TargetActorDistance
		end
		--预处理呼吸的一些参数
		self:PreDealBreath()
	end


end

function ASI_CameraCut:HandleCameraParam()
 
end

function ASI_CameraCut:Tick(DeltaTime)
    if not IsValid_L(self.TargetCamera) then
        return
    end


	if self.SectionData.CameraBreathType ~= Game.DialogueManager.CameraBreathType.None and IsValid_L(self.TargetCamera) then
		if self.RunningTime > self.SectionData.BreathDelay then
			--可以开始呼吸效果
			if (self.SectionData.Duration -  self.RunningTime) >0 and
				(self.SectionData.Duration -  self.RunningTime) < self.SectionData.BreathAttenuation
			then
				--呼吸效果需要衰减
				DeltaTime = DeltaTime * (self.SectionData.Duration -  self.RunningTime) / self.SectionData.BreathAttenuation
			end

			if self.SectionData.CameraBreathType >= Game.DialogueManager.CameraBreathType.Left and self.SectionData.CameraBreathType <= Game.DialogueManager.CameraBreathType.Back then
				local MoveVector
				if     self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.Left then MoveVector = self.RightVector * -1  --Left
				elseif self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.Right then MoveVector = self.RightVector       --Right
				elseif self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.Top then MoveVector = FVector(0,0,1)       --Top
				elseif self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.Bottom then MoveVector = FVector(0,0,-1)      --Bottom
				elseif self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.Front then MoveVector = self.ForwardVector      --Front
				elseif self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.Back then MoveVector =  self.ForwardVector * -1  --Back
				end
				local NewLocation = self.TargetCamera:K2_GetActorLocation() + MoveVector * self.SectionData.BreathSpeed * DeltaTime
				self.TargetCamera:K2_SetActorLocation(NewLocation, false, nil, false)
			elseif self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateLeft or self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateRight then
				local BreathSpeed = self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateLeft and (self.SectionData.BreathSpeed)
					or (self.SectionData.BreathSpeed*-1)
				local NewDirection = KismetMathLibrary.RotateAngleAxis(self.TargetCamera:K2_GetRootComponent():GetForwardVector(),
					BreathSpeed * DeltaTime, self.UpVector)
				local NewLocation = self.RotateTargetPos - NewDirection * self.SectionData.BreathFocusDistance
				local NewRotation = KismetMathLibrary.FindLookAtRotation(NewLocation, self.RotateTargetPos)
				self.TargetCamera:K2_SetActorLocationAndRotation(NewLocation, NewRotation, false, nil, false)
			elseif self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateTop or self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateBottom then
				local BreathSpeed = self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateTop and (self.SectionData.BreathSpeed)
					or (self.SectionData.BreathSpeed*-1)
				local NewDirection = KismetMathLibrary.RotateAngleAxis(self.TargetCamera:K2_GetRootComponent():GetForwardVector(),
					BreathSpeed * DeltaTime, self.RightVector)
				local NewLocation = self.RotateTargetPos - NewDirection * self.SectionData.BreathFocusDistance
				local NewRotation = KismetMathLibrary.FindLookAtRotation(NewLocation, self.RotateTargetPos)
				self.TargetCamera:K2_SetActorLocationAndRotation(NewLocation, NewRotation, false, nil, false)
			end
		end
	end
end

function ASI_CameraCut:End()
end

function ASI_CameraCut:Pause(Pause)
end


function ASI_CameraCut:SetCameraParams(Transform,FOV,DOF,DOFSensorWidth)
end

function ASI_CameraCut:PreDealBreath()
    if self.SectionData.CameraBreathType ~= Game.DialogueManager.CameraBreathType.None then
        if self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateLeft or self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateRight or
           self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateTop or self.SectionData.CameraBreathType == Game.DialogueManager.CameraBreathType.RotateBottom then
            self.RotateTargetPos = self.TargetCamera:K2_GetActorLocation() + 
                self.TargetCamera:K2_GetRootComponent():GetForwardVector() * self.SectionData.BreathFocusDistance
        
            if Game.DialogueManager.EditorManager then
                --只在编辑器下才开启Debug视点绘制
                import("KismetSystemLibrary").DrawDebugSphere(
                    _G.GetContextObject(),
                    self.RotateTargetPos,
                    0.01,--radius
                    1,--segments
                    FLinearColor(255,0,0),
                    self.SectionData.Duration,
                    1
                )
            end
        end
        self.UpVector = FVector(0,0,1)
        self.RightVector = self.TargetCamera:K2_GetRootComponent():GetRightVector()
        self.ForwardVector = self.TargetCamera:K2_GetRootComponent():GetForwardVector()
    end
end

function ASI_CameraCut:GetFocusActorDistance(FocusActorName, CameraCut)
    local FocusGameEntity = nil
    if type(FocusActorName) == "string" then
        FocusGameEntity = self:GetDialogueDirector():FindDialogueGameEntity(FocusActorName)
    else
        FocusGameEntity = self:GetDialogueDirector():FindDialogueGameEntity(FocusActorName.PerformerName)
    end
    local TargetActor
    if FocusGameEntity then
        TargetActor = FocusGameEntity:GetActor()
    end

    if not TargetActor then
        return
    end
    local FocusPosition = self:GetActorTargetPosition(TargetActor)
    local CameraPosition = CameraCut:GetTransform():GetLocation()
    local Distance = math.sqrt((CameraPosition.X - FocusPosition.X)^2 + (CameraPosition.Y - FocusPosition.Y)^2 + (CameraPosition.Z - FocusPosition.Z)^2)
    return Distance
end

function ASI_CameraCut:GetActorTargetPosition(Actor)
    if not IsValid_L(Actor) then
        return
    end
    local skComp = Actor:GetMainMesh()
    local FaceLocation = skComp:GetSocketLocation("head")
    if FaceLocation.Z < Actor:GetTransform():GetLocation().Z then
        FaceLocation = skComp:GetSocketLocation("Bip001-Head")
    end
    if FaceLocation.Z < Actor:GetTransform():GetLocation().Z then
        return FVector(0, 0, 0)
    end
    Log.DebugWarningFormat("Face Position: %s %s %s", FaceLocation.X, FaceLocation.Y, FaceLocation.Z)
    return FVector(FaceLocation.X, FaceLocation.Y, FaceLocation.Z)
end

function ASI_CameraCut.IsOnlyUnique()
    return true
end

-- 分镜开始时,尝试根据当前对话状态(主要是演员的位置)重新设置
---@private
function ASI_CameraCut:tryResetCameraTransform(cameraConfig, cameraActor)
    local playerController = Game.CameraManager.PlayerController
    if not playerController then
        return
    end

    local curViewTarget = playerController:GetViewTarget()
    if curViewTarget == cameraActor then
        Log.Debug("[tryResetCameraTransform] same view target, will not reset")
        return
    end

    local director = self:GetDialogueDirector()

    local parentEntity = director:FindDialogueGameEntity(cameraConfig.Parent)
    if not parentEntity then
        Log.WarningFormat("[tryResetCameraTransform] parent entity %s not exist", cameraConfig.Parent)
        return
    end

    local parentActor = parentEntity:GetActor()
    if not parentEntity then
        Log.WarningFormat("[tryResetCameraTransform] parent entity %s no actor", cameraConfig.Parent)
        return
    end

    local parentTrans = parentActor:GetTransform()
    local spawnTrans = DialogueUtil.TableToFTransform(cameraConfig.SpawnTransform)
    local newTrans = spawnTrans * parentTrans

    -- 处理锁头 (本质上骨骼也可以锁)
    if cameraConfig.bEnableLookAt then
        local targetGameEntity = director:FindDialogueGameEntity(cameraConfig.LookAtTarget)
        local targetActor = targetGameEntity and targetGameEntity:GetActor()
        local skComp = targetActor and targetActor:GetComponentByClass(USkeletalMeshComponent)
        if skComp then
            local boneIndex = skComp:GetBoneIndex(cameraConfig.BoneName)
            if boneIndex ~= -1 then
                local boneLoc = UC7FunctionLibrary.GetBoneLocation(skComp, cameraConfig.BoneName)
                local newLoc = newTrans:GetTranslation()
                -- Z轴高度是骨骼实际Z轴高度再叠加偏移
                newLoc.Z = boneLoc.Z + cameraConfig.OffsetZ
                newTrans:SetTranslation(newLoc)
            end
        end
    end

    cameraActor:K2_SetActorTransform(newTrans, false, nil, false)
    Log.DebugFormat("camera transform on cut begin: %s", newTrans:ToString())
end

return ASI_CameraCut
