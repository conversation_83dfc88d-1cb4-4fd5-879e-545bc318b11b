local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_Particle = DefineClass("ASI_Particle", ASI_Base)
local EAttachmentRule = import("EAttachmentRule")
local NiagaraFunctionLibrary = import("NiagaraFunctionLibrary")
local HitResult = import("HitResult")
local KismetMathLibrary = import("KismetMathLibrary")


function ASI_Particle:Init(InSectionData, InController)
end


function ASI_Particle:Start()
    local SectionData = self.SectionData
    self.OriginLocation = nil
    
    local entity = self:GetDialogueDirector():FindDialogueGameEntity(self.SectionData.AttachActor)
    if entity then
        local entityActor = entity:GetActor()
        if entityActor then
            local attachComponent = entityActor:GetMainMesh() or entityActor:K2_GetRootComponent() -- :K2_GetRootComponent()
            if SectionData.ModifyEffectName ~= "" then
                --操作的是已有特效,这种情况下一般必然是追随Follow
                local targetEmitter =  self.DialogueInstance.EffectArray[SectionData.ModifyEffectName]
                if targetEmitter then
                    if SectionData.Immediate then
                        Log.Debug("current effect Immediate snap to target")
                        local attachMethod = EAttachmentRule.SnapToTarget
                        local ret = targetEmitter:K2_AttachToComponent(attachComponent, SectionData.BoneName, attachMethod, attachMethod, attachMethod, false)
                    else
                        Log.Debug("current effect lerp to target")
                        self.OriginLocation = targetEmitter:K2_GetComponentLocation()
                        if SectionData.BoneName ~= "" then
                            self.TargetLocation = attachComponent:GetSocketLocation(SectionData.BoneName)
                        else
                            self.TargetLocation = attachComponent:K2_GetComponentLocation()
                        end
                        targetEmitter:K2_AttachToComponent(attachComponent, SectionData.BoneName, EAttachmentRule.KeepWorld)
                    end
                    Log.DebugFormat("%s snap to %s bone:%s", SectionData.ModifyEffectName, SectionData.AttachActor, SectionData.BoneName)
                else
                    Log.Warning("operate Effect is not illegal")
                end
            else
                --新添加特效
                local Particle = SectionData.Effect:LoadSynchronous()
                if Particle then
                    if SectionData.Follow then
                        self.Emitter = NiagaraFunctionLibrary.SpawnSystemAttached(Particle, attachComponent, SectionData.BoneName,  SectionData.Offset, FRotator(1), 3, false)
                    else
                        self.Emitter = NiagaraFunctionLibrary.SpawnSystemAtLocation(entityActor, Particle, 
                            entityActor:K2_GetActorLocation(), entityActor:K2_GetActorRotation())
                        local attachMethod = EAttachmentRule.SnapToTarget
                        local ret = self.Emitter:K2_AttachToComponent(attachComponent, SectionData.BoneName, attachMethod, attachMethod, attachMethod, false)

                    end
                    --编辑器大世界窗口播放时可能会被裁掉导致看不见，这里必须调用这个函数处理一下
                    self.Emitter:SetForceLocalPlayerEffect(true)


                    self.Emitter:SetVariableInt("EmitterLoopCount", 100)
                    if SectionData.EffectName ~= "" then
                        self.DialogueInstance.EffectArray[SectionData.EffectName] = self.Emitter
                    end
                else
                    Log.Warning("particle effect is null")
                end 
                self.Emitter:SetWorldScale3D(SectionData.Scale)
                if SectionData.OverrideRotation then
                    local TempHitResult = HitResult()
                    self.Emitter:K2_SetWorldRotation(SectionData.Rotation, false, TempHitResult, false)
                end
            end
        else
            Log.Warning("entity is null ")
        end
    else
        Log.WarningFormat("can not find attach Actor %s",self.SectionData.AttachActor)
    end

    Log.Debug("Start Particle")
    
end

function ASI_Particle:Tick(DeltaTime)
    if self.SectionData.ModifyEffectName ~= "" and self.SectionData.Immediate == false then 
        --转接时平移
        local Ratio = self.RunningTime / self.SectionData.Duration
        local targetEmitter =  self.DialogueInstance.EffectArray[self.SectionData.ModifyEffectName]
        if targetEmitter then
            local TempHitResult = HitResult()
            local newLocation = KismetMathLibrary.VLerp(self.OriginLocation, self.TargetLocation, Ratio)
            Log.DebugFormat("Ration%.2f  Origin:%.1f %.1f %.1f newLoc %.1f %.1f %.1f", Ratio, 
                        self.OriginLocation.X, self.OriginLocation.Y, self.OriginLocation.Z, 
                        self.TargetLocation.X, self.TargetLocation.Y, self.TargetLocation.Z)
            targetEmitter:K2_SetWorldLocation(newLocation, false, TempHitResult, false)
        else
            Log.Warning("the effect is destroyed when moving")
        end
    end
end


function ASI_Particle:End()
    Log.Debug("End Particle")
    if self.SectionData.EffectName then
        self.DialogueInstance.EffectArray[self.SectionData.EffectName] = nil
    end
    if IsValid_L(self.Emitter) then
        self.Emitter:K2_DestroyComponent()
    end
end

function ASI_Particle:Pause(Pause)
end

return ASI_Particle