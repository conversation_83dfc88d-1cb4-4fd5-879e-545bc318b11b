local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_CameraDOF = DefineClass("ASI_CameraDOF", ASI_Base)

function ASI_CameraDOF:Init(InSectionData, InController)
end


function ASI_CameraDOF:Start()
    local Camera = self:GetTrackActor()
    if not Camera then
        return
    end

    local CameraComponent = Camera.CameraComponent
    if not CameraComponent then
        return
    end

    self.StartDOF = CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance
    self.TargetDOF = self.SectionData.DOF
end

function ASI_CameraDOF:Tick(DeltaTime)

    local Camera = self:GetTrackActor()
    if not Camera then
        return
    end

    local CameraComponent = Camera.CameraComponent
    if not CameraComponent then
        return
    end

    local Ratio = self.RunningTime / self.SectionData.Duration

    local CurrentDOF = self.DialogueManager.DialogueUtil.Lerp(self.StartDOF, self.TargetDOF, Ratio)

    CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance = CurrentDOF
end

function ASI_CameraDOF:End()
end

function ASI_CameraDOF:Pause(Pause)
end

function ASI_CameraDOF.IsOnlyUnique()
    return true
end

return ASI_CameraDOF