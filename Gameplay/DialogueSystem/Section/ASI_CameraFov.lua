local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_CameraFov = DefineClass("ASI_CameraFov", ASI_Base)

function ASI_CameraFov:Init(InSectionData, InController)
end


function ASI_CameraFov:Start()
    local Camera = self:GetTrackActor()
    if Camera then
        local CameraComponent = Camera.CameraComponent;
        CameraComponent:SetFieldOfView(self.SectionData.Fov);
    end
end

function ASI_CameraFov:Tick(DeltaTime)

end

function ASI_CameraFov:End()
end

function ASI_CameraFov:Pause(Pause)
end

function ASI_CameraFov.IsOnlyUnique()
    return true
end

return ASI_CameraFov