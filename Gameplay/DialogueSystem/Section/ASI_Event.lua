local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_Event = DefineClass("ASI_Event", ASI_Base)

function ASI_Event:Init(InSectionData, InController)
end

function ASI_Event:Start()
    local EventString = _G.EEventTypes[self.SectionData.EventName]
    if EventString then
        Log.DebugFormat("Publish Event %s", EventString)
        Game.EventSystem:Publish(EventString)
    else
        Log.WarningFormat("Cant not find Event %s in EventTypes.lua", self.SectionData.EventName)
    end
end

function ASI_Event:End()
end

function ASI_Event:Pause(Pause)
end

return ASI_Event
