local EMIDCreationFlags = import("EMIDCreationFlags")
local USkeletalMeshComponent = import("SkeletalMeshComponent")
local UMaterialInstanceDynamic = import("MaterialInstanceDynamic")
local KismetMaterialLibrary = import("KismetMaterialLibrary")

local ArrodesMaterialSlotName = "FACE"
local ArrodesEmojiAlphaParamName = "Alpha"
local ArrodesEmoji0ParamName = "EmotionIndex0"
local ArrodesEmoji1ParamName = "EmotionIndex1"
local ArrodesEmojiShakeParamName = "ShakeIntensity"

local ETransitionStage= {
    UnInit = -1,
    None = 0 ,
    TransitionIn = 1,
    TransitionOut = 3,
}

local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_ArrodesEmoji = DefineClass("ASI_ArrodesEmoji", ASI_Base)
ASI_ArrodesEmoji.ArrodesEyeMaterialSlotNames = {"Mirror_eye", "Mirror_Eyeball"}
ASI_ArrodesEmoji.ArrodesEysVisibleParamName = "_DitherAlpha"

function ASI_ArrodesEmoji:Init(InSectionData, InController)
    self.CurrentStage = ETransitionStage.UnInit
    self.DynamicMaterialCache = {}
end

function ASI_ArrodesEmoji:GetMaterial(materialSlot)
    if IsValid_L(self.DynamicMaterialCache[materialSlot]) then
        return self.DynamicMaterialCache[materialSlot]
    end
    local Actor =  self:GetTrackActor()
    local SkelMesh = Actor:GetComponentByClass(USkeletalMeshComponent)

    if SkelMesh then
        self.CachedSkelComp = SkelMesh

        local Material = nil
        local MaterialIdxID = SkelMesh:GetMaterialIndex(materialSlot)
        if MaterialIdxID > 0 then
            Material = SkelMesh:GetMaterial(MaterialIdxID)
        end

        if not Material then
            return nil
        end

        --切换对应贴图
        local DynamicMaterial = Material:Cast(UMaterialInstanceDynamic)
        if DynamicMaterial == nil then
            --如果不是动态材质，尝试创建一个
            DynamicMaterial = KismetMaterialLibrary.CreateDynamicMaterialInstance(GetContextObject(), Material, "", EMIDCreationFlags.Transient)
            if DynamicMaterial then
                self.CachedSkelComp:SetMaterial(MaterialIdxID, DynamicMaterial)
            end
            self.DynamicMaterialCache[materialSlot] = DynamicMaterial
        end
        return DynamicMaterial
    end
end

function ASI_ArrodesEmoji:Start()
    self:SetTransitionStage(ETransitionStage.TransitionIn)
end

function ASI_ArrodesEmoji:SetTransitionStage(Stage)
    self.CurrentStage = Stage
    if self.CurrentStage == ETransitionStage.TransitionIn then
        local DM = self:GetMaterial(ArrodesMaterialSlotName)
        local Data = self.SectionData
        if DM and Data then
            DM:SetScalarParameterValue(ArrodesEmojiAlphaParamName, 0)
            DM:SetScalarParameterValue(ArrodesEmoji0ParamName, Data.InEmoji)
            DM:SetScalarParameterValue(ArrodesEmoji1ParamName, Data.MainEmoji)
            DM:SetScalarParameterValue(ArrodesEmojiShakeParamName, Data.bInShake and 1 or 0)
        end

        local transTime = 0.033
        if self.SectionData.InTime > 0 then
            transTime = self.SectionData.InTime
        end
        for _, slotName in pairs(ASI_ArrodesEmoji.ArrodesEyeMaterialSlotNames) do
            DM = self:GetMaterial(slotName)
            local MaterialID = Game.ObjectActorManager:GetIDByObject(DM)
            Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID(ASI_ArrodesEmoji.ArrodesEysVisibleParamName, MaterialID, 0, 1, transTime)
        end
    elseif self.CurrentStage == ETransitionStage.TransitionOut then
        local DM = self:GetMaterial(ArrodesMaterialSlotName)
        local Data = self.SectionData
        if DM and Data then
            DM:SetScalarParameterValue(ArrodesEmojiAlphaParamName, 0)
            DM:SetScalarParameterValue(ArrodesEmoji0ParamName, Data.MainEmoji)
            DM:SetScalarParameterValue(ArrodesEmoji1ParamName, Data.OutEmoji)
            DM:SetScalarParameterValue(ArrodesEmojiShakeParamName, 0)
        end

        local transTime = 0.033
        if self.SectionData.OutTime > 0 then
            transTime = self.SectionData.OutTime
        end
        for _, slotName in pairs(ASI_ArrodesEmoji.ArrodesEyeMaterialSlotNames) do
            DM = self:GetMaterial(slotName)
            local MaterialID = Game.ObjectActorManager:GetIDByObject(DM)
            Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID(ASI_ArrodesEmoji.ArrodesEysVisibleParamName, MaterialID, 1, 0, transTime)
        end
    else
        local DM = self:GetMaterial(ArrodesMaterialSlotName)
        local Data = self.SectionData
        if DM and Data then
            DM:SetScalarParameterValue(ArrodesEmojiAlphaParamName, 0)
            DM:SetScalarParameterValue(ArrodesEmoji0ParamName, Data.MainEmoji)
            DM:SetScalarParameterValue(ArrodesEmoji1ParamName, Data.MainEmoji)
            DM:SetScalarParameterValue(ArrodesEmojiShakeParamName, 0)
        end
    end
end

function ASI_ArrodesEmoji:UpdateAlpha(Alpha)
    local DM = self:GetMaterial(ArrodesMaterialSlotName)
    if DM then
        DM:SetScalarParameterValue(ArrodesEmojiAlphaParamName, Alpha)
    end
end

function ASI_ArrodesEmoji:Tick(DeltaTime)

    if (self.SectionData.Duration - self.RunningTime) < self.SectionData.OutTime then
        if self.CurrentStage ~= ETransitionStage.TransitionOut then
            self:SetTransitionStage(ETransitionStage.TransitionOut)
        end

        if self.SectionData.OutTime > 0 then
            self:UpdateAlpha(1 - math.min((self.SectionData.Duration - self.RunningTime) / self.SectionData.OutTime, 1))
        else
            self:UpdateAlpha(1)
        end
    elseif self.RunningTime < self.SectionData.InTime then
        if self.CurrentStage ~= ETransitionStage.TransitionIn then
            self:SetTransitionStage(ETransitionStage.TransitionIn)
        end

        if self.SectionData.InTime > 0 then
            self:UpdateAlpha(math.min(self.RunningTime / self.SectionData.InTime, 1))
        else
            self:UpdateAlpha(1)
        end
    else
        if self.CurrentStage ~= ETransitionStage.None then
            self:SetTransitionStage(ETransitionStage.None)
            self:UpdateAlpha(1)
        end
    end
end

function ASI_ArrodesEmoji:End()
    local DM = self:GetMaterial(ArrodesMaterialSlotName)
    if DM then
        if self.CurrentStage ~= ETransitionStage.TransitionOut then
            self:SetTransitionStage(ETransitionStage.TransitionOut)
        end
        self:UpdateAlpha(1)
    end
end

function ASI_ArrodesEmoji:Pause(Pause)
	
end

function ASI_ArrodesEmoji.CopyDynamicData(SectionData, TableData)
    if not SectionData or not TableData or not next(TableData) then
        return
    end
    SectionData.MainEmoji = TableData.MainEmoji
    SectionData.InEmoji = TableData.InEmoji
    SectionData.OutEmoji = TableData.OutEmoji

    Log.DebugFormat("ASI_ArrodesEmoji.CopyDynamicData:MainEmoji %s, InEmoji %s, OutEmoji %s", 
        TableData.MainEmoji, TableData.InEmoji, TableData.OutEmoji)
end

return ASI_ArrodesEmoji
