local KismetMathLibrary = import("KismetMathLibrary")
local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_CameraFloat = DefineClass("ASI_CameraFloat", ASI_Base)


function ASI_CameraFloat:Init(InSectionData, InController)
end


function ASI_CameraFloat:Start()
	
	---@daiwei 这个功能先临时关闭,看引用没有使用， 后面改为CameraModifier
	
	
    --self.FadeCurve = slua.loadObject("/Game/Blueprint/DialogueSystem/CameraFloatCurve.CameraFloatCurve")
    --self.Actor = Game.DialogueManager:GetCameraProxy()
    --self.Valid = false
    --if self.Actor == nil then
    --    Log.WarningFormat("Can not find Operate Camera!")
    --    return
    --end
    --local CameraLocation = self.Actor:K2_GetActorLocation()
    ----旋转凝视目标距离
    --self.TargetDistance = self.SectionData.LookAtDistance
    --self.RotateTargetPos = CameraLocation + self.Actor:K2_GetRootComponent():GetForwardVector() * self.TargetDistance
	--
    --self.PulseTime = self.SectionData.PulseTime --每次添加冲力后的摇晃时间
    --self.TargetAngleStep = self.SectionData.TargetAngleStep --每次摇晃的角度
    --self.AngleLimit = self.SectionData.AngleLimit --左右摇晃的角度限制，不能和初始角度大于多少度
	--
    --self.TimeLeft = 0  --旋转的剩余时间
    --self.TargetAngle = 0 
    --self.UpVector = FVector(0,0,1)
    --self.SectionStartDirection = self.Actor:K2_GetRootComponent():GetForwardVector()
	--
	--
    --Game.DialogueManager.ASI_CameraFloat = self
	--
    --Game.DialogueManager.P_Dialogue:SetRotateCameraVisible(true)
	--
    --self.Valid = true

end

function ASI_CameraFloat:Tick(DeltaTime)

end

function ASI_CameraFloat:PauseTick(DeltaTime)
    --if self.Valid == false then
    --    return
    --end
    --if self.TimeLeft > 0 then
    --    self.TimeLeft = self.TimeLeft - DeltaTime
    --    if self.TimeLeft < 0 then
    --        self.TimeLeft = 0
    --    end
	--
    --    local Ratio = 1 - self.TimeLeft / self.PulseTime
	--
    --    local CurrentValue = self.FadeCurve:GetFloatValue(Ratio)
	--
    --    --根据当前进度，计算得角度,先线性
    --    local Angle = self.TargetAngle * CurrentValue
	--
    --    Log.DebugFormat("Ratio is %.2f FloatValue is %.2f ", Ratio, CurrentValue)
	--
    --    local DegreeOffset = self:CheckCalculateCurrentAngleOffset()
    --    if DegreeOffset > self.AngleLimit then
    --       --已经超过正向最大角度
    --       if self.TargetAngle > 0 then
    --            --且正在正向旋转
    --            local msg = string.format("正向旋转过程中，超过最大角度%d，停止", self.AngleLimit)
    --            Log.WarningFormat(msg)
    --            Game.DialogueManager.DialogueUtil.ScreenMsg(msg,2)
	--
    --            self.TimeLeft = 0
    --            return
    --       end
    --    end
    --    if DegreeOffset < (self.AngleLimit*-1) then
    --        --已经超过负向最大角度
    --        if self.TargetAngle < 0 then
    --             --且正在负向旋转
    --            local msg = string.format("负向旋转过程中，超过最大角度%d，停止", self.AngleLimit)
    --            Log.WarningFormat(msg)
    --            Game.DialogueManager.DialogueUtil.ScreenMsg(msg,2)
	--
    --            self.TimeLeft = 0
    --            return
    --        end
    --     end
	--
    --    local NewDirection = KismetMathLibrary.RotateAngleAxis(self.StartDirection, Angle, self.UpVector)
    --    local NewLocation = self.RotateTargetPos - NewDirection * self.TargetDistance
    --    local NewRotation = KismetMathLibrary.FindLookAtRotation(NewLocation, self.RotateTargetPos)
    --    self.Actor:K2_SetActorLocationAndRotation(NewLocation, NewRotation, false, nil, false)
	--
    --end
end

function ASI_CameraFloat:End()
    --Game.DialogueManager.ASI_CameraFloat = nil
    --Game.DialogueManager.P_Dialogue:SetRotateCameraVisible(false)
end

function ASI_CameraFloat:Pause(Pause)
end

function ASI_CameraFloat:CheckCalculateCurrentAngleOffset()
    local CurrentDirection = self.Actor:K2_GetRootComponent():GetForwardVector()

    local Degree = Game.DialogueManager.DialogueUtil.CalculateTwoVectorsDegree(self.SectionStartDirection, CurrentDirection)
    Log.DebugFormat("Current Angle %.2f", Degree)
    return Degree
end

--添加旋转冲力
--left：true 左旋 fale 右旋
function ASI_CameraFloat:AddPulse(left)
    if self.TimeLeft > 0 and self.TimeLeft < self.PulseTime/2 then
        --旋转的前一半时间，不允许连续发起旋转
        Log.DebugFormat("camera floating, please wait")
        return
    end
    self.StartDirection = self.Actor:K2_GetRootComponent():GetForwardVector()
    self.TimeLeft = self.PulseTime
    
    self.TargetAngle = self.TargetAngleStep 
    if left == false then
        self.TargetAngle = self.TargetAngleStep * -1
    end


    Game.DialogueManager.DialogueUtil.DrawSphere(self.RotateTargetPos)
    Log.DebugFormat("RotateTargetPos is %s", tostring(self.RotateTargetPos))
end

function ASI_CameraFloat.IsOnlyUnique()
    return true
end

return ASI_CameraFloat