local ASI_Base = kg_require("Gameplay.DialogueSystem.Section.ASI_Base")
local ASI_ParticleActive = DefineClass("ASI_ParticleActive", ASI_Base)
local NiagaraFunctionLibrary = import("NiagaraFunctionLibrary")
local ENCPoolMethod = import("ENCPoolMethod")

function ASI_ParticleActive:Init(InSectionData, InController)
end

function ASI_ParticleActive:Start()
    Log.Debug("ASI_ParticleActive:Start()")
    self.TrackActor = self:GetTrackActor()

    if not IsValid_L(self.TrackActor) then
        Log.WarningFormat("Particle Track Actor is invalid")
        return
    end

    if self.TrackActor.Effect == nil then
        local DialogueEntity = self:GetDialogueEntity()
        local Particle = slua.loadObject(DialogueEntity.Effect)
        if Particle then
            self.TrackActor.Effect = NiagaraFunctionLibrary.SpawnSystemAttached(Particle, 
                    self.TrackActor:K2_GetRootComponent(), DialogueEntity.BoneName,  DialogueEntity.Offset, FRotator(1), 3, false, true, 
                    ENCPoolMethod.None, true)
            --编辑器大世界窗口播放时可能会被裁掉导致看不见，这里必须调用这个函数处理一下
            self.TrackActor.Effect:SetForceLocalPlayerEffect(true)
            self.TrackActor.Effect:SetVariableInt("EmitterLoopCount", 100)
        end
    else
        self.TrackActor.Effect:SetActive(self.SectionData.Active,true)
    end
end


function ASI_ParticleActive:End()
end

function ASI_ParticleActive:Pause(Pause)
end

return ASI_ParticleActive