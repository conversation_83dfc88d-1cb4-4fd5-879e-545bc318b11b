---@class WBP_DialogueCameraSelectView : WBP_DialogueCameraSelect_C
---@field public WidgetRoot WBP_DialogueCameraSelect_C


---@class P_DialogueCameraSelectView : WBP_DialogueCameraSelectView
---@field public controller P_DialogueCameraSelect
local P_DialogueCameraSelectView = DefineClass("P_DialogueCameraSelectView", UIView)

function P_DialogueCameraSelectView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

function P_DialogueCameraSelectView:OnDestroy()
end

return P_DialogueCameraSelectView
