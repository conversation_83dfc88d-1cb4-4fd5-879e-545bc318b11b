------------------------------------------------------------------------------------
-- UObject形式
------------------------------------------------------------------------------------

---@class DAInternalUObject
---@field private DialogueAsset DialogueAsset
local DAInternalUObject = DefineClass("DAInternalUObject")

function DAInternalUObject:ctor(dialogueAsset)
    self.DialogueAsset = dialogueAsset
end

function DAInternalUObject:dtor()
    self.DialogueAsset = nil
end

---@param indexEpisode number
---@return KGSLDialogueEpisode | nil
function DAInternalUObject:GetEpisode(indexEpisode)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")
    return da:GetDialogueEpisode(indexEpisode)
end

---@param episodeID number
---@return KGSLDialogueEpisode | nil
function DAInternalUObject:GetEpisodeByEpisodeID(episodeID)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")
    return da:GetDialogueEpisodeByEpisodeID(episodeID)
end

---@param episodeID
---@param indexLine number
---@return KGSLDialogueLine | nil
function DAInternalUObject:GetLine(episodeID, indexLine)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")
    return da:GetDialogueLine(episodeID, indexLine)
end

---@param episodeID
---@param lineGUID number
---@return KGSLDialogueLine | nil
function DAInternalUObject:GetLineByGUID(episodeID, lineGUID)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")
    return da:GetDialogueLineByGUID(episodeID, lineGUID)
end

---@param indexEpisode number
function DAInternalUObject:RemoveEpisode(indexEpisode)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")

    --TODO 为了解决旧资产无法正确删除Episode的问题，等木南流程修改后，再改回去
    --da:RemoveDialogueEpisode(indexEpisode)
    da.Episodes:Remove(indexEpisode)
end

---@param episodeID number
function DAInternalUObject:RemoveEpisodeByEpisodeID(episodeID)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")
    da:RemoveDialogueEpisodeByEpisodeID(episodeID)
end

function DAInternalUObject:ClearEpisodes()
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")
    da:ClearEpisodesList()
end

---@param episodeID number
---@return KGSLDialogueEpisode
function DAInternalUObject:InstanceEpisode(episodeID)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")

    return da:InstanceDialogueEpisode(episodeID)
end

---@param episodeIndex number|nil @ -1 or nil means that the episode created will be add at last.
---@return KGSLDialogueEpisode
function DAInternalUObject:AddEpisode(episodeIndex)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")

    episodeIndex = episodeIndex or -1
    return da:AddDialogueEpisode(episodeIndex, -1)
end

---@param episodeID number
---@param strContent string
---@param guid string
---@return KGSLDialogueLine
function DAInternalUObject:InstanceLine(episodeID, strContent, guid)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")
    -- assert(not string.isEmpty(guid), "guid is nil or empty")
    
    strContent = strContent or ""
    local line = da:InstanceDialogueLine(episodeID, strContent, guid)
    if not line then
        line = import("KGSLDialogueLine")(da)
        line:Initialize(strContent)
    end

    return line
end

---@param episodeID number
---@param line KGSLDialogueLine
---@param lineIndex number @ -1 or nil means that the line created will be add at last.
---@return KGSLDialogueLine
function DAInternalUObject:AddLine(episodeID, line, lineIndex)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")

    local episode = da:GetDialogueEpisode(episodeID)
    if episode then
        lineIndex = lineIndex or -1
        return episode:AddLine(line, lineIndex)
    end

    return nil
end

function DAInternalUObject:GetEpisodesList()
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")

    return da.EpisodesList
end

function DAInternalUObject:SetEpisodeLines(episodeID, DialogueEpisodeLines)
    local Episode = self:GetEpisodeByEpisodeID(episodeID)
    assert(Episode, "Episode is nil")
    Episode.DialogueLines = DialogueEpisodeLines
end

---@param episodeID number
---@return KGSLDialogueOption
function DAInternalUObject:InstanceOption(episodeID)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")

    local option = da:InstanceDialogueOption(episodeID)
    if not option then
        option = import("KGSLDialogueOption")(da)
    end

    return option
end

---@param episodeID number
---@param option KGSLDialogueOption
---@param optionIndex number @ -1 or nil means that the option will be added at last
function DAInternalUObject:AddOption(episodeID, option, optionIndex)
    local da = self.DialogueAsset
    assert(da, "dialogue asset is nil")

    local episode = da:GetDialogueEpisode(episodeID)
    if episode then
        optionIndex = optionIndex or -1
        return episode:AddOption(option, optionIndex)
    end

    return nil
end

---@param episodeID number
---@param episodeRef DialogueEpisode
function DAInternalUObject:CopyOptions(episodeID, episodeRef)
    local dialogueAsset = self.DialogueAsset
    assert(dialogueAsset, "dialogue asset is nil")

    local episode = self:GetEpisodeByEpisodeID(episodeID)
    if episode then
        episode:CopyOptionsToEpisodeData(episodeRef)
    end
end

return DAInternalUObject
