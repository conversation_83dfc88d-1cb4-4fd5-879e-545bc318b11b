local EditorDefine = {}

--C++ class
EditorDefine.UDialogueBaseAsset = import("DialogueBaseAsset")
EditorDefine.UDialogueAsset = import("DialogueAsset")
EditorDefine.UDialogueTemplateAsset = import("DialogueTemplateAsset")

EditorDefine.UDialogueActionSection = import("DialogueActionBase")
EditorDefine.UDialogueSpawnableTrack = import("DialogueSpawnableTrack")
EditorDefine.UDialogueStateControlAction = import("DialogueStateControlTrack")
EditorDefine.UDialogueDialogue = import("DialogueDialogue")  --this is section
EditorDefine.UDialogueDialogueAction = import("DialogueDialogueTrack") 
EditorDefine.UDialogueActor = import("DialogueActor")
EditorDefine.UDialogueCamera = import("DialogueCamera")


--SceneActor class
EditorDefine.BP_RoutePointActor = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C") 

--entity class
EditorDefine.BP_DialogueRoutePoint = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C") 
EditorDefine.BP_DialogueEntityEffect = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C") 
EditorDefine.BP_DialogueEntityLight = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityLight.BP_DialogueEntityLight_C") 
EditorDefine.BP_DialogueEntityModel = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C") 

--BP Track
EditorDefine.BP_DialogueTrackAnimation = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C")
EditorDefine.BP_DialogueTrackAside = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAside.BP_DialogueTrackAside_C")
EditorDefine.BP_DialogueTrackCameraFloat = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraFloat.BP_DialogueTrackCameraFloat_C")
EditorDefine.BP_DialogueTrackFaceAnim = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFaceAnim.BP_DialogueTrackFaceAnim_C")
EditorDefine.BP_DialogueTrackFadeInOut = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOut.BP_DialogueTrackFadeInOut_C")
EditorDefine.BP_DialogueTrackLookAt = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C")
EditorDefine.BP_DialogueTrackSound = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackSound.BP_DialogueTrackSound_C")
EditorDefine.BP_DialogueTrackSoundEffect = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackSoundEffect.BP_DialogueTrackSoundEffect_C")
EditorDefine.BP_DialogueTrackTransform = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C")
EditorDefine.BP_DialogueTrackNewActorMovement = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_NewActorMovement.BP_NewActorMovement_C")
EditorDefine.BP_EpisodeDuration = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Track/BP_EpisodeDuration.BP_EpisodeDuration_C")

--BP Section
EditorDefine.BPS_ActorDescription = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_ActorDescription.BPS_ActorDescription_C")
EditorDefine.BPS_AsideSection = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_AsideSection.BPS_AsideSection_C")
EditorDefine.BPS_AutoCameraCut = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_AutoCameraCut.BPS_AutoCameraCut_C")
EditorDefine.BPS_CameraCut = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C")
EditorDefine.BPS_Dialogue = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C")
EditorDefine.BPS_EpisodeDuration = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_EpisodeDuration.BPS_EpisodeDuration_C")
EditorDefine.BPS_FaceAnimation = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_FaceAnim.BPS_FaceAnim_C")
EditorDefine.BPS_FadeInOutSection = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutSection.BPS_FadeInOutSection_C")
EditorDefine.BPS_LookAt = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C")
EditorDefine.BPS_PlayAnimation = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C")
EditorDefine.BPS_StateControl = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C")
EditorDefine.BPS_Sound = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_Sound.BPS_Sound_C")
EditorDefine.BPS_Transform = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C")
EditorDefine.BPS_NewActorTransform = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/Section/BPS_NewActorMovement.BPS_NewActorMovement_C")

--Other
EditorDefine.BP_DLExtensionData = slua.EditorLoadClass("/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C")
EditorDefine.DialogueAnchorType = slua.EditorLoadObject("/Game/Blueprint/DialogueSystem/Enum/DialogueAnchorType.DialogueAnchorType")

return EditorDefine