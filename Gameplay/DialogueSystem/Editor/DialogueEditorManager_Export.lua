--[[
本文件专门处理Export相关代码
]]

local KismetSystemLibrary = import("KismetSystemLibrary")
local LowLevelFunctions = import("LowLevelFunctions")


--检测某个OptionID是否是点击后立即关闭
function DialogueEditorMgr:CheckOptionIsSelectClose(OptionID)
    local OptionTextData = Game.TableData.GetDialogueOptionTextRow(OptionID)
    if OptionTextData and OptionTextData.bClose then
        return true
    end
    return false
end

function DialogueEditorMgr:ExportAsLuaTableEx()
    self:ExportAsLuaTable(self:GetCurrentAsset())
end

function DialogueEditorMgr:ExportAllAssetToLuaTable()
    --遍历所有资产，执行导出操作
    local AllAssets = self:GetAllAsset(EditorDefine.UDialogueBaseAsset)

    self:BeginBatch(AllAssets:Num(), "自动导出所有对话资产到lua table ...")
    for k,v in ipairs(AllAssets:ToTable()) do
        self:IncreaseProgress()
        Log.Debug("Export Lua " .. v:GetName())
        self:PreDeal(v) --旧资产很多DialogueEntity.Spline的点数为0，必须提升为1
        self:ExportAsLuaTable(v)
    end
    self:EndBatch()
end

----导出OptionText到中间CSV文件
function DialogueEditorMgr:ExportOptionText(DialogueAsset, bHideExportConfirmWnd)
    --self:ExportJson(DialogueAsset, "G:/Package/" .. DialogueAsset.DialogueTemplate:GetName() .. ".json")
    Log.Debug("OptionText Click Export!")

    local ProjectDir = KismetSystemLibrary.GetProjectDirectory();
    Log.Debug("ProjectDir: " ,ProjectDir)

    local CSVPath = string.gsub(ProjectDir, "Client/", "Tools/ExportKGSLOption")
    Log.Debug("Dialogue CSVPath: " ,CSVPath)


    local EpisodeCount = DialogueAsset:GetDialogueEpisodesCount()
    if( EpisodeCount == 0) then
        Log.WarningFormat("DialogueAsset:GetDialogueEpisodesCount() == 0 ! ")
        return;
    end

    local FileHandler = assert(io.open(CSVPath .."/LuaCSV.csv", 'w'))
    local AssetName = DialogueAsset:GetName()
    local AssetNote = DialogueAsset.Note

    Log.Debug("Dialogue:Now Start Write CSV Content ")

    FileHandler:write(DialogueAsset.ExcelName .. "\n")
    local SplitChar = "`"
    local LineStr = ""
    LineStr = LineStr .. "int" .. SplitChar --ID
    LineStr = LineStr .. "str" .. SplitChar --是否导表
    LineStr = LineStr .. "str" .. SplitChar --资产名
    LineStr = LineStr .. "str" .. SplitChar --文本内容
    LineStr = LineStr .. "int" .. SplitChar --选项锁定时是否显示
    LineStr = LineStr .. "str" .. SplitChar --选项锁定时文本
    LineStr = LineStr .. "int" .. SplitChar --是否关闭 
    LineStr = LineStr .. "int" .. SplitChar --选择后是否隐藏
    LineStr = LineStr .. "str" .. SplitChar --额外行为
    LineStr = LineStr .. "int" .. SplitChar --通知服务器
    LineStr = LineStr .. "str\n"            --图标
    FileHandler:write(LineStr)
    Log.Debug(LineStr)


    ---@type DialogueAssetProxy
    local proxy <close> = DialogueAssetProxy.new(DialogueAsset)

    local NeedImportWarningText = ""
    
    local hasOption = false
    local allOptions = {}
    for EpisodeIndex = 0, EpisodeCount-1 do
        local EpisodeLines = proxy:GetEpisode(EpisodeIndex)
        for OptionIndex = 0, EpisodeLines.Options:Num() - 1 do
            local ItemRef = EpisodeLines.Options:Get(OptionIndex).Item
            if ItemRef.ID ~= 0 and not allOptions[ItemRef.ID] then
                allOptions[ItemRef.ID] = true
                if not hasOption then
                    hasOption = true 
                end
                --==ID
                LineStr = tostring(ItemRef.ID) .. SplitChar    
                --==是否导表    
                LineStr = LineStr .. "是" .. SplitChar
                --==资产名    
                LineStr = LineStr .. AssetName .. SplitChar
                --==文本内容
                if ItemRef.Text == "" then
                    if NeedImportWarningText ~= "" then
                        NeedImportWarningText = NeedImportWarningText .. "," 
                    end
                    NeedImportWarningText = NeedImportWarningText .. tostring(OptionIndex+1)
                end
                LineStr = LineStr .. ItemRef.Text .. SplitChar 
                --==选项锁定时是否显示    
                LineStr = LineStr .. (ItemRef.LockVisible and "1" or "") .. SplitChar   
                --==选项锁定时文本
                LineStr = LineStr .. ItemRef.LockText .. SplitChar
                --==是否关闭
                LineStr = LineStr .. (ItemRef.bClose and "1" or "") .. SplitChar 
                --==选择后是否隐藏
                LineStr = LineStr .. (ItemRef.SelectHide and "1" or "") .. SplitChar

                --==额外行为
                local num = ItemRef.ExtraAction:Num()
                if(num ~= 0) then
                    for ActionIndex = 0, num - 1 do
                        local ActionData = ItemRef.ExtraAction:Get(ActionIndex)
                        LineStr = LineStr .. ActionData
                        --行为之间用,分隔
                        if(ActionIndex ~= num-1 ) then
                            LineStr = LineStr .. ","
                        end
                    end    
                end
                LineStr = LineStr .. SplitChar
                --==通知服务器
                LineStr = LineStr .. (ItemRef.ReportServer and "1" or "") .. SplitChar

                -- icon
                LineStr = LineStr .. tostring(ItemRef.Icon.Path)

                LineStr = LineStr .. "\n"
                FileHandler:write(LineStr)
                Log.Debug(LineStr)
            end
        end
    end

    
    if NeedImportWarningText ~= "" then
        local WarningMsg = string.format("这些选项 %s 文本内容为空 , 如果非故意设置为空且此弹窗过多，请重新Import一下", NeedImportWarningText)
        Log.WarningFormat(WarningMsg)
        self:ShowMessageBox(WarningMsg)
    end

    FileHandler:close()

    if not hasOption then 
        -- self:ShowMessageBoxForce("Export 成功")
        self:PushNotification("选项导出成功", 1)
        return true
    end

    local ExportDialogueExePath = CSVPath .. "/ExportDialogue.exe"
    Log.Debug("ExportDialogueExePath: " .. ExportDialogueExePath)

    Log.Debug("Write CSV Complete, Now Start Convert Tool ")

    --写入内容到csv文件后，启动exe工具，将csv文件转换为excel文件
    FileHandler = assert(io.popen(ExportDialogueExePath, 'r'))

    local exportRet = false
    if( FileHandler ~= nil) then
        local ln = FileHandler:read("*all")
        Log.Debug(ln)
        FileHandler:close()
        if string.find(ln, "modified") then
            exportRet = true
        end
    end

    if not bHideExportConfirmWnd then
        if exportRet then
            -- self:ShowMessageBoxForce("Export 成功")
            self:PushNotification("选项导出成功", 1)
        else
            self:ShowMessageBoxForce("Export 失败，请重试，可以查看log获取详细信息")
        end
    end

    return exportRet    
end



--最多保留两位小数，针对时间类参数，如Duration Delay
function DialogueEditorMgr:GetFloatStr(FloatValue)
    local Temp = math.floor(FloatValue * 100 + 0.5) 
    Temp = Temp / 100
    return tostring(Temp)
end


function DialogueEditorMgr:FindStrIndex(Str, SubStr)
	local i = 1
	while i do
		if utf8.contains(Str, i, SubStr) then
            local realIndex = utf8.char_index(Str, i)
            return realIndex
        else
			i = utf8.next(Str, i)
		end
	end
	return nil
end

--台本导回Excel的时候，需要保留\n信息
function DialogueEditorMgr:GetExportDialogueContent(Text)
    local NewLineIndex = self:FindStrIndex(Text,"\n")
    if NewLineIndex then
        local SrcLen = utf8.len(Text)
        local Str1 = utf8.sub(Text, 1, NewLineIndex)
        local Str2 = utf8.sub(Text, NewLineIndex+1, SrcLen)
        return Str1 .. "\\n" .. Str2
    end
    return Text
end


function DialogueEditorMgr:ForceExportLuaTable(InDialogueAsset)
    local DialogueAsset = InDialogueAsset or self:GetCurrentAsset()
    Log.DebugFormat("ForceExportLuaTable %s", DialogueAsset.StoryLineID)
    
    self:UpdatePreLoadRes(DialogueAsset)

    local StartTime = LowLevelFunctions.GetUtcMillisecond()
    local Success = self:ExportAsLuaTable(DialogueAsset, self.bUseCommandlet)
	if not Success then
		return false
	end
    local EndTime = LowLevelFunctions.GetUtcMillisecond()
    Log.DebugFormat("ExportAsLuaTable Time %s", EndTime - StartTime)
    self:GetCurrentAssetTable(true)
    self:SyncAssetEntityActor2TableInfo()
	return true
end

function DialogueEditorMgr:ForceExportLuaTableAndGetEntity(EntityName)
    self:ForceExportLuaTable()

    local TableAllEntities = Game.DialogueManager:GetAllEntities(self:GetCurrentAssetTable())
    if TableAllEntities then
        for TableEntityIndex, TableEntity in ipairs(TableAllEntities) do
            if TableEntity.TrackName == EntityName then
                return TableEntity
            end
        end
    end
    return nil
end

--导出Dialogue到Excel的时候，进行一些数据合法检测，如果检测失败，则不允许导出
function DialogueEditorMgr:ExportDialogueCheck(DialogueAsset)
    local CheckOption = self:ExportDialogueCheck_Option(DialogueAsset)
    if not CheckOption then
        return false
    end
    return true 
end

--导出时，检测选项
function DialogueEditorMgr:ExportDialogueCheck_Option(DialogueAsset)
    if not DialogueAsset then
        return false
    end
    local EpisodesList = DialogueAsset.EpisodesList
    if not EpisodesList then
        return false
    end

    --如果一个选项的文本ID不为0 ，意味着一般要点击之后执行某个小段，我们要确保后续EpisodeID存在
    for i = 0 , EpisodesList:Num()-1 do
        local DialogueEpisode = EpisodesList:Get(i)
        if not DialogueEpisode then
            goto continue
        end
        local Options = DialogueEpisode.Options
        if not Options then
            goto continue
        end
        for j = 0,  Options:Num()-1 do
            local KGSLDialogueOption = Options:Get(j)
            if KGSLDialogueOption then
                if KGSLDialogueOption.DialogueID ~= 0 then
                    local SelectHide = self:CheckOptionIsSelectClose(KGSLDialogueOption.DialogueID)
                    if not SelectHide then
                        --我们判断这个选项是否是执行后立即关闭的（这种情况不需要接小段）
                        --否则我们判断是否存在对应的EpisodeID
                        local FindEpisode = DialogueAsset:GetDialogueEpisodeByEpisodeID(KGSLDialogueOption.EpisodeID)
                        if not FindEpisode then
                            self:ShowMessageBoxForce(string.format("导出失败，OptionID %d SelectHide == false,因此需要下一小段 %d，该段未找到，请检查", 
                                                KGSLDialogueOption.DialogueID, KGSLDialogueOption.EpisodeID))
                            return false
                        end
                    end
                end
            end
        end
        ::continue::
    end

    return true
end
