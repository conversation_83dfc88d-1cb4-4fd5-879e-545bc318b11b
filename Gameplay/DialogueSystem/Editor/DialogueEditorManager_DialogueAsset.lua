
--[[
    本文件专门处理DialogueAsset的辅助接口
]]

local EditorDefine = kg_require("Gameplay.DialogueSystem.Editor.DialogueEditorManager_Define")

--预加载策略
--后续的Entity&Section有资源要预加载的，按需在此添加
local PreLoadStrategy = 
{
    --entity
    { cls = EditorDefine.BP_DialogueEntityEffect, method = "UpdatePreLoadRes_BP_DialogueEntityEffect"},

    --section
    {
        cls = EditorDefine.BPS_Dialogue, method = "UpdatePreLoadRes_BPS_Dialogue",
    }
}

function DialogueEditorMgr:GetEntityActor(EntityName)
    local GameEntity = self.DialogueManager.DialogueDirector:FindDialogueGameEntity(EntityName)
    if not GameEntity then
        return nil
    end
    return GameEntity:GetActor()
end

--导出Lua的时候，扫描对话资产用到的所有资源，加入到一个TArray里面，TArray导出到lua的时候是一个table，Runtime的时候Loop一次进行预加载
function DialogueEditorMgr:UpdatePreLoadRes(DialogueAsset)
    if not DialogueAsset then
        return
    end

    local PreLoadArray = DialogueAsset.PreLoadArray
    local PreLoadBanks = DialogueAsset.PreLoadBanks

    --模板没有这个参数
    if not PreLoadArray or not PreLoadBanks then
        return
    end
    PreLoadArray:Clear()
    PreLoadBanks:Clear()


    local AllEntities = DialogueAsset:GetAllEntities()

    local BankTable = {}
    local PathTable = {}
    --DialogueEntity Strategy
    for i = 0, AllEntities:Num()-1 do
        local DialogueEntity = AllEntities:Get(i)

        local Path = self:GetUClassPath(DialogueEntity.ActorClass)
        PathTable[Path] = 1
        self:TryUpdatePreloadObject(PathTable, DialogueEntity, DialogueAsset)
    end

    --Track Sections
    for i = 0, DialogueAsset.Episodes:Num()-1 do
        local EpisodeRef = DialogueAsset.Episodes:Get(i)
        if EpisodeRef then
            local AllTracks = DialogueAsset:GetAllTacksByEpisodeID(EpisodeRef.EpisodeID, true)

            for TrackIndex = 0 , AllTracks:Num()-1 do
                local TrackRef = AllTracks:Get(TrackIndex)

                --Section Resource
                if TrackRef.ActionSections then
                    for SectionIndex = 0, TrackRef.ActionSections:Num()-1 do
                        local SectionRef = TrackRef.ActionSections:Get(SectionIndex)
                        if SectionRef then
                            self:TryUpdatePreloadObject(PathTable, SectionRef, DialogueAsset)
                            --处理音效Bank
                            if (SectionRef.Sound) then
                                local eventName = SectionRef.Sound:GetName()
                                local requiredBank = Game.AkAudioManager:GetEventRequiredBank(eventName)
                                if requiredBank ~= "" then
                                    BankTable[requiredBank] = 1
                                end
                            end
                        else
                            Log.WarningFormat("Action %s SectionIndex %d is nil", TrackRef.TrackName, SectionIndex)
                        end
                    end
                end
            end
        end
    end
    
    local paths = table.keys(PathTable)
    table.sort(paths)
    for k,v in ipairs(paths) do
        PreLoadArray:Add(v)
    end

    local banks = table.keys(BankTable)
    table.sort(banks)
    for k,v in ipairs(banks) do
        PreLoadBanks:Add(v)
    end
end

function DialogueEditorMgr:TryUpdatePreloadObject(PathTable, Object, DialogueAsset)
    if not Object then
        return
    end
    for k, v in ipairs(PreLoadStrategy) do
        if v.cls == Object:GetClass() then
            self[v.method](self,PathTable, Object, DialogueAsset)
            break
        end
    end
end

------------------------------Entity获取要预加载的资源路径---------------------------------------------
function DialogueEditorMgr:UpdatePreLoadRes_BP_DialogueEntityEffect(PathTable, Object)
    PathTable[Object.Effect:ToString()] = 1
end 

function DialogueEditorMgr:UpdatePreLoadRes_BPS_Dialogue(PathTable, Object, DialogueAsset)
    local contentUI = Object.ContentUI
    if DialogueAsset.bUseCSStyle then
        contentUI = Game.DialogueManager.DialogueContentUIType.CSStyle
    end

    if contentUI then
        local path = Game.DialogueManager.ContentUIList[contentUI]
        if not string.isEmpty(path) then
            PathTable[path] = 1
        end
    end
end


--获取某个Episode里面的某个Track
function DialogueEditorMgr:GetSomeTrackByEpisodeID(DialogueAsset, EpisodeID, TrackClass)
    if not DialogueAsset then
        return nil
    end

    --Track Sections
    local AllTracks = DialogueAsset:GetAllTacksByEpisodeID(EpisodeID, false)

    for TrackIndex = 0 , AllTracks:Num()-1 do
        local TrackRef = AllTracks:Get(TrackIndex)
        if TrackRef and TrackRef:IsA(TrackClass)then
            return TrackRef
        end
    end

    return nil
end


--获取某个Episode的台本Track
function DialogueEditorMgr:GetDialogueTrackByEpisodeID(DialogueAsset, EpisodeID)
    return self:GetSomeTrackByEpisodeID(DialogueAsset, EpisodeID, EditorDefine.UDialogueDialogueAction)
end

--获取某个DialogueAsset的某个Episode(注意，当前返回的是FDialogueEpisode)
function DialogueEditorMgr:GetDialogueEpisode(DialogueAsset, EpisodeID)
    if not DialogueAsset or not EpisodeID then
        return nil
    end

    for i = 0, DialogueAsset.Episodes:Num()-1 do
        local Episode = DialogueAsset.Episodes:Get(i)

        if Episode and Episode.EpisodeID == EpisodeID then
            return Episode
        end
    end

    return nil
end

--获取LinkedGUID的台本Section
function DialogueEditorMgr:GetDialogueSectionByGUID(DialogueAsset, LineGUID)
    if not DialogueAsset or not LineGUID then
        return nil
    end

    --Track Sections
    for i = 0, DialogueAsset.Episodes:Num()-1 do
        local EpisodeRef = DialogueAsset.Episodes:Get(i)

        if EpisodeRef then
            local AllTracks = DialogueAsset:GetAllTacksByEpisodeID(EpisodeRef.EpisodeID, true)

            for TrackIndex = 0 , AllTracks:Num()-1 do
                local TrackRef = AllTracks:Get(TrackIndex)
                local ActionSections = TrackRef.ActionSections 
                if ActionSections and ActionSections:Num() > 0 and ActionSections:Get(0):IsA(EditorDefine.UDialogueDialogue) then
                    for SectionIndex = 0, TrackRef.ActionSections:Num()-1 do
                        local SectionRef = TrackRef.ActionSections:Get(SectionIndex)
                        if SectionRef.LineUniqueIDLinked == LineGUID then
                            return SectionRef
                        end
                    end
                end
            end
        end
    end

    return nil
end

--获取DialogueAsset里面关联了Line GUID的所有Sections
--IncludeDialogueSection是否将DialogueSection包括在内
function DialogueEditorMgr:GetLinkedSections(DialogueAsset, LineGUID, IncludeDialogueSection)
    local Ret = slua.Array(import("EPropertyClass").Object, EditorDefine.UDialogueActionSection)

    if not DialogueAsset or not LineGUID then
        return Ret
    end

    --Track Sections
    for i = 0, DialogueAsset.Episodes:Num()-1 do
        local EpisodeRef = DialogueAsset.Episodes:Get(i)

        if EpisodeRef then
            local AllTracks = DialogueAsset:GetAllTacksByEpisodeID(EpisodeRef.EpisodeID, true)

            for TrackIndex = 0 , AllTracks:Num()-1 do
                local TrackRef = AllTracks:Get(TrackIndex)

                if TrackRef.ActionSections then
                --Action Track
                    for SectionIndex = 0, TrackRef.ActionSections:Num()-1 do
                        local SectionRef = TrackRef.ActionSections:Get(SectionIndex)
                        if SectionRef.LineUniqueIDLinked == LineGUID then
                            if SectionRef:IsA(EditorDefine.UDialogueDialogue) then
                                if IncludeDialogueSection then
                                    if Ret:Num() > 0 then
                                        Ret:Insert(0, SectionRef)
                                    else
                                        Ret:Add(SectionRef)
                                    end
                                end 
                            else
                                Ret:Add(SectionRef)
                            end
                        end
                    end
                end
            end
        end
    end

    return Ret
end

--获取DialogueAsset里面某个Episode的最后的Section EndTime
function DialogueEditorMgr:GetMaxEndTime(DialogueAsset, EpisodeID)
    local AllTracks = DialogueAsset:GetAllTacksByEpisodeID(EpisodeID, true)
    local MaxEndSection = nil
    for TrackIndex = 0 , AllTracks:Num()-1 do
        local TrackRef = AllTracks:Get(TrackIndex)

        if TrackRef.ActionSections then
        --Action Track
            for SectionIndex = 0, TrackRef.ActionSections:Num()-1 do
                local SectionRef = TrackRef.ActionSections:Get(SectionIndex)
                if MaxEndSection == nil or SectionRef:GetEndTime() >= MaxEndSection:GetEndTime() then
                    MaxEndSection = SectionRef
                end
            end
        end
    end

    if MaxEndSection then
        Log.DebugFormat("MaxEndTime is %s, EndTime is %.2f", MaxEndSection.SectionName, MaxEndSection:GetEndTime())
        return MaxEndSection:GetEndTime()
    end
    return 0
end

--获取对话资产里面的根Root Entity
function DialogueEditorMgr:GetRootEntity(DialogueAsset)
    local Ret = slua.Array(import("EPropertyClass").Object, import("DialogueEntity"))
    if DialogueAsset == nil then
        return Ret
    end

    if DialogueAsset.Episodes:Num() > 0 then
        local EpisodeRef = DialogueAsset.Episodes:Get(0)
        for k,v in ipairs(EpisodeRef.TrackList:ToTable()) do
            if v.DialogueEntity then
                Ret:Add(v.DialogueEntity)
            end
        end 
    end
    return Ret
end

--获取对话资产里面的根Root Track
function DialogueEditorMgr:GetRootEntityTrack(DialogueAsset)
    if not DialogueAsset then
        return nil 
    end

    --只需要获取第一个Episode里面的根Track即可
    if DialogueAsset.Episodes:Num() == 0 then
        return 0
    end
    
    local FirstEpisode = DialogueAsset.Episodes:Get(0)
    local EpisodeTrackList = FirstEpisode.TrackList

    for i = 0 , EpisodeTrackList:Num()-1 do
        local Track = EpisodeTrackList:Get(i)
        if Track:IsA(EditorDefine.UDialogueSpawnableTrack) then
            return Track
        end
    end

    return nil
end
