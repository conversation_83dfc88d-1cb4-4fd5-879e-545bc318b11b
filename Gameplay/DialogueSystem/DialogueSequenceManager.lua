---@class DialogueSequenceManager
local DialogueSequenceManager = DefineClass("DialogueSequenceManager")

function DialogueSequenceManager:ctor()
	self.CurrentLevelID = 0
	self.currentTaskHandle = 0
	self.CurrentSequenceCameraID = -1
end

function DialogueSequenceManager:dtor()
	self.CurrentLevelID = 0
	self.currentTaskHandle = 0
	self.CurrentSequenceCameraID = -1
end

function DialogueSequenceManager:Init()
	self:RegisterEvent()
end

function DialogueSequenceManager:RegisterEvent()
	Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_LEVEL_LOAD_START, "OnLevelLoadStart", self)
end

function DialogueSequenceManager:UnInit()
	self:UnRegisterEvent()
end

function DialogueSequenceManager:UnRegisterEvent()
	Game.GlobalEventSystem:RemoveListener(EEventTypesV2.LEVEL_ON_LEVEL_LOAD_START, "OnLevelLoadStart", self)
end

function DialogueSequenceManager:OnLevelLoadStart()
	local levelSequenceTasks = Game.SequenceManager:GetPlayTasksByCinematicType(Enum.CinematicType.DialogueSequence)
	for _, task in pairs(levelSequenceTasks) do
		if task then
			task:Terminate()
		end
	end

	self.CurrentLevelID = self.CurrentLevelID + 1
end

function DialogueSequenceManager:PreLoadLevelSequence(Params, autoPlay)
	local SequenceData = Game.TableData.GetDilaogueSeuqenceDataRow(Params.AssetID)
	if not SequenceData then
		Log.WarningFormat("[Enter] no LevelSequenceData for %s", Params.AssetID)
		if Params.OnCinematicFinished then
			Params.OnCinematicFinished()
		end
		return
	end

	Params.levelID = self.CurrentLevelID
	--Params.bPauseAtEnd = true
	Params.bAutoPlay = false
	Params.CinematicType = Enum.CinematicType.DialogueSequence
	Params.bDisableCharacterLight = SequenceData.DisableCharacterLight
	Params.OnSequencePlayCallBack = function(task)
		self:OnPlayerPlayCallback(task)
	end

	Params.OnSequenceFinishedCallBack = function(task)
		self:OnPlayerFinishedCallback(task)
	end
	Params.OnStartPlayInternalCallBack = function(loadHandleID)
		self:StartPlayInternal(loadHandleID)
	end
	
	Params.OnSequenceCameraCutUpdate = function(task, cameraID)
		self:OnSequenceCameraCut(task, cameraID)
	end
	if not autoPlay then
		return Game.SequenceManager:PreLoadSequence(Params)
	else
		return Game.SequenceManager:PlaySequence(Params)
	end
end

function DialogueSequenceManager:StopLevelSequence(loadHandleID)
	local StopTask = Game.SequenceManager:GetPlayTaskByLoadID(loadHandleID)
	if StopTask then
		Game.SequenceManager:Terminate(StopTask:GetLoadHandleID())
		
		local bInEditor = Game.BSManager and Game.BSManager.bIsInEditor
		if not bInEditor  then
			Game.CameraManager:EnableCutSceneCamera(false)
		end
		self.CurrentSequenceCameraID = -1
	end
end

function DialogueSequenceManager:OnPlayerPlayCallback(Task)
	Log.Debug("DialogueSequenceManager OnPlayerPlayCallback")
	if Task then
		local playParams = Task:GetPlayParams()
		if playParams and playParams.OnCinematicPlay then
			playParams.OnCinematicPlay()
		end
	end
end

function DialogueSequenceManager:OnPlayerFinishedCallback(Task, LoadID)
	Log.Debug("DialogueSequenceManager OnPlayerFinishedCallback")
	if Task then
		local playParams = Task:GetPlayParams()
		if playParams and playParams.OnCinematicFinished then
			playParams.OnCinematicFinished()
		end
		local bInEditor = Game.BSManager and Game.BSManager.bIsInEditor
		if not bInEditor  then
			Game.CameraManager:EnableCutSceneCamera(false)
		end
	end
	self.currentTaskHandle = 0
end

function DialogueSequenceManager:StartPlayInternal(loadHandleID)
	Log.Debug("DialogueSequenceManager OnPlayerStopCallback loadHandleID: ", loadHandleID)
	local task = Game.SequenceManager:GetPlayTaskByLoadID(loadHandleID)
	if not task then
		Log.Warning("DialogueSequenceManager OnPlayerStopCallback Invalid Task loadHandleID: ", loadHandleID)
		return
	end
	self:SetPlaybackParams(loadHandleID, task:GetPlayParams())
	self:UpdateLevelSequenceActorPostion()
	self:ChangeSequenceTransformOriginActor()
end

function DialogueSequenceManager:SetPlaybackParams(loadHandleID, PlayParams)
	if PlayParams.StartFrame then
		Game.SequenceManager:KAPI_LevelSequenceJumpToFrame(loadHandleID, PlayParams.StartFrame)
	elseif PlayParams.StartMark then
		Game.SequenceManager:KAPI_LevelSequenceJumpToMark(loadHandleID, PlayParams.StartMark)
	else
		Game.SequenceManager:KAPI_LevelSequenceJumpToStart(loadHandleID)
	end
	if PlayParams.EndFrame then
		Game.SequenceManager:KAPI_LevelSequencePlayToFrame(loadHandleID, PlayParams.EndFrame)
	elseif PlayParams.EndMark then
		Game.SequenceManager:KAPI_LevelSequencePlayToMark(loadHandleID, PlayParams.EndMark)
	end
	Game.SequenceManager:KAPI_LevelSequenceJumpToStart(loadHandleID)
	Game.SequenceManager:KAPI_SetPlayRate(loadHandleID, PlayParams.PlayRate or 1)
end

function DialogueSequenceManager:UpdateLevelSequenceActorPostion(LoadID)
	local Task = Game.SequenceManager:GetPlayTaskByLoadID(LoadID)
	if not Task then
		return
	end
	local PlayParams = Task:GetPlayParams()
	local Transform = PlayParams and PlayParams.Transform
	local Position = Transform and Transform.Position
	local Rotator = Transform and Transform.Rotator
	if not Position then
		Position = FVector()
	end
	if not Rotator then
		Rotator = FRotator()
	end
	LuaScriptAPI.KAPI_Actor_SetLocation(Task:GetDirector(), Position)
	LuaScriptAPI.KAPI_Actor_SetRotation(Task:GetDirector(), Rotator)
end

function DialogueSequenceManager:ChangeSequenceTransformOriginActor(LoadID)
	local Task = Game.SequenceManager:GetPlayTaskByLoadID(LoadID)
	if not Task then
		return
	end
	local PlayParams = Task:GetPlayParams()
	if PlayParams.bUseTransformOriginActor == true then
		Game.SequenceManager:KAPI_ChangeSequenceTransformOriginActor(LoadID, Task:GetDirector())
	end
end

function DialogueSequenceManager:OnSequenceCameraCut(Task, CameraID)
	if not Task then
		return
	end
	if Game.BSManager and Game.BSManager.bIsInEditor then
		-- todo 编辑器下特殊处理 去Actor等俊杰那边来处理
		if CameraID then
			if  self.CurrentSequenceCameraID ~= CameraID then
				local CameraOwner = Game.ObjectActorManager:GetObjectByID(CameraID):GetOwner()
				Game.CameraManager.PlayerController:SetViewTargetWithBlend(CameraOwner, 0, 0, 0, false)
				self.CurrentSequenceCameraID = CameraID
			end
		end
	end
end

function DialogueSequenceManager:LevelSequencePlay(LoadID)
	local Task = Game.SequenceManager:GetPlayTaskByLoadID(LoadID)
	if Task then
		local PlayParams = Task:GetPlayParams()
		local bInEditor = Game.BSManager and Game.BSManager.bIsInEditor
		if not bInEditor  then
			Game.CameraManager:EnableCutSceneCamera(true, Task:GetDirector(), false, PlayParams.bDisableCharacterLight)
		end
		Game.SequenceManager:PlaySequenceWithHandleID(LoadID)
	end
end

function DialogueSequenceManager:FindPlayTaskByAssetID(AssetID)
	return Game.SequenceManager:FindPlayTaskByTypeAndAssetID(Enum.CinematicType.DialogueSequence, AssetID)
end

function DialogueSequenceManager:FindTaskLoadHandleIDByAssetID(AssetID)
	local Task = self:FindPlayTaskByAssetID(AssetID)
	if Task then
		return Task:GetLoadHandleID()
	end
	return nil
end

return DialogueSequenceManager
