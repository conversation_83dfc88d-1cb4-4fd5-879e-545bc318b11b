---@class WorldManager : 游戏世界管理器
---负责管理游戏世界的全局逻辑，包括但不限于：
---1. 场景生命周期管理（加载/卸载）
---2. 同屏人数控制与视椎裁剪（AOI分级机制）
---3. NPC/玩家显隐控制与过场动画处理
---4. 场景物体创建与九宫格管理
---5. 风场/水场物理效果控制
---6. 空间分割与触发器系统
---7. 物理破碎特效管理
---8. 客户端性能预算管理
---
---主要功能模块：
---• 场景管理：处理地图加载流程、场景物件绑定、空间分割
---• AOI系统：实现分级更新机制，控制实体可见性与LOD
---• 显隐控制：提供类型化显隐接口，支持过场动画特殊处理
---• 物理环境：管理动态水面波纹、局部风场等环境效果
---• 特效系统：处理物理破碎效果的创建与播放
---• 性能管理：通过WorldViewBudgetManager控制局内表现资源
kg_require("Framework.C7Common.CppManagerBase")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local ULLFunc = import("LowLevelFunctions")
local sceneUtils = kg_require("Shared.Utils.SceneUtils")
local EPropertyClass = import("EPropertyClass")
local WorldViewBudgetManager = require("Gameplay.WorldManager.WorldViewBudgetManager")
local AnimationManager = kg_require("Gameplay.Managers.AnimationManager")
require("Shared.WorldActorDefine")

local EC7ShapeCollisionType = import("EC7ShapeCollisionType")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local EWActorType = kg_require("Shared.WorldActorDefine").EWActorType
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")
local KGUECompositeOperateLibrary = import("KGUECompositeOperateLib")
local AnimationBudgetBlueprintLibrary = import('AnimationBudgetBlueprintLibrary')
local DebugFlag = kg_require("Gameplay.Debug.DebugFlag")

local WorldManager = DefineClass("WorldManager", CppManagerBase)

function WorldManager:ctor()
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.WORLD_NOT_READY_TO_PLAY
    --Level
    self.PlayEnterLevelID = 0
	self.LogicSpaceEidCorrespondingToCurrentMap = nil

    self.cppMgr = nil

    self.EnableBriefAoiLevel = false
    self.AoiLevel_SecondaryList = {}
    self.AoiLevel_PrimaryList = {}
	self.AoiPriorityQueue = {}
	
    self.AOIUpdateDistancePrecision = 500; ---AOI分级更新的距离精度5m 默认
    self.AoiLevelBatchModifyUpdateTimer = nil --分级修改,大于ReqBatchChangeEntityAoiLevel的CD时间保证能回包
    self.AoiLevelBatchModifyTime = 1100 --分级修改间隔
    self.AoiLevelBatchModifyRequestID = 0 --分级修改请求ID
    
    self.AoiLevelCheckTime = 500 --分级确认修改间隔 特定情况(索敌调用) 延迟低一些
    self.AoiLevelCheckUpdateTimer = nil

    self.AoiLevelLoopCheckTime = 3000 --分级确认修改间隔 固定更新方式 不频繁
    self.AoiLevelLoopCheckUpdateTimer = nil
    self.AoiMaxBriefChangedCountOnce = 3 --升级Primary单次行为最大修改次数，多了客户端会卡的

    self.AoiLevelAoiPlayerNumTimer = nil
    
    self.ClipFilterNumMap = nil
    self.MaxPrimaryPlayerCount = 0 --最大一级AOIPlayer数量
    self.BriefAoiMode = 0 --次级Aoi显示模式
    self.AvatarActorEntityNum = 0
    self.LockBriefSelectActorID = nil


    --Temp
    self.SceneFieldCheckHandler = nil
    --当前位置的地图区域类型
    self.SceneFieldFieldTypeMap = nil
    
    -- ====================地图地形探知==================================
    -- 先默认都有, 后面按需进行配置, 有助于避免无效的水体探测开销
    self.HasWaterArea = true
    -- 先默认都有, 后面按需进行配置, 有助于避免无效的风场计算开销
    self.HasWindField = true
	-- 先默认都有, 后面按需进行配置,
	self.HasDeformableLandscape = true
	
    --==================== DynamicWaterWave机制==========================
    self.IsDynamicWaterWaveEnabled = false

    --==================== LocalWindField机制==========================
    self.IsEnableLocalWindField = false

	self.NeedWaterWind = true


    -- 动画辅助结构
    self.MontageToPlayLength = {}
    self.MontageToBlendInTime = {}
    self.MontageToBlendOutTime = {}

	self.ContinuousHiddenTriggerID = nil

	-- =================隐藏控制机制=======================
	self.NpcHiddenByConfigIDForDialogue = {}
	self.NpcHiddenTriggerElementIDForDialogue = nil
	
	--隐藏表
	self.VisSetConfigMap = {}
	self.VisActorStateMap = {}
	self.HideAllWhiteListForCinematic = {}
	-- 类型: 计数
	self.HideEntityCategoryWhiteListCountMap = {}
	-- 唯一标识: 计数
	self.HideSpecificEntityWhiteListCountMap = {}
	-- =================隐藏控制机制  END=======================

    --场景美术物件绑定
    self.CurSceneActorBindingUniqueID = 0
    -- table<CellX, table<CellY, BindingInfoUniqueId>>
    self.SceneActorCellInfos = {}
    -- table<BindingInfoUniqueId, BindingInfoUniqueId>
    self.SceneActorBindingMapInfos = {}
    -- key: unique id, val: table<SceneActorSoftPath, CallbackObj, CallbackName>
    self.SceneActorBindingInfos = {}

    --CacheTempData
    self.FSDSceneElement_Temp = import("SDSceneElement")()
    self.FSDBodyShape_Temp = import("SDBodyShape")()
    self.FSDAOIRadiusShape_Temp = import("SDBodyShape")()
	-- uid: entityprops, 场景对象管理数据集合
	self.SceneObjectClientAOIManaged = {}
	
	self.NpcInsIDMap = {}
	self.NpcSpawner_EntityIDMap = {}
	
    -- 局内表现投放预算管理
    self.ViewBudgetMgr = WorldViewBudgetManager.new()

	-- 全局动画管理
	self.AnimationManager = AnimationManager.new()
	
end

function WorldManager:dtor()
    if self.ViewBudgetMgr ~= nil then
        self.ViewBudgetMgr:delete()
        self.ViewBudgetMgr = nil
    end

	if self.AnimationManager ~= nil then
		self.AnimationManager:delete()
		self.AnimationManager = nil
	end
    self:EnableDynamicWaterWave(false)
    self:EnableLocalWindField(false)
end

function WorldManager:Init()
	-- 初始化C++管理器
	self:CreateCppManager("WorldManager")
	
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.WORLD_NOT_READY_TO_PLAY
	self.ViewBudgetMgr:Reset()
	self.AnimationManager:Init()
	
	if self.NeedWaterWind then
		self.cppMgr:SetGameInstance()
	end
	
    -- 初始化地形材质数据到cpp
    self:initTerrainDataToCpp()
	
    self:ResetSpaceDivisionParams()

    local LODList = slua.Array(EPropertyClass.Int)
    LODList:Add(WorldViewBudgetConst.ACTOR_LOD_0_DIST_CM)
    LODList:Add(WorldViewBudgetConst.ACTOR_LOD_1_DIST_CM)
    LODList:Add(WorldViewBudgetConst.ACTOR_LOD_2_DIST_CM)
    self.cppMgr:SetLODRangeConfigList(LODList)
    self.cppMgr.MaxNotifyClipActorNum = 1
    self.cppMgr.MaxNotifyActorLODNum = 1
    self.cppMgr.ClipDistancePrecision = 10
	
    self.ClipFilterNumMap = {}
    --分级修改间隔(毫秒)
    self.AoiLevelBatchModifyTime = 1100
    --分级确认修改间隔(毫秒)
    self.AoiLevelCheckTime = 500
    --分级确认修改间隔Loop(毫秒)
    self.AoiLevelLoopCheckTime = 3000
    -- --单帧最大修改数量
    -- self.AoiModifyFrameMaxNum = 10

    self:ResetBriefsAoiData()

    Game.EventSystem:AddListener(_G.EEventTypes.CINEMATIC_ON_START, self, self.Receive_CINEMATIC_ON_START)
    Game.EventSystem:AddListener(_G.EEventTypes.CINEMATIC_ON_END, self, self.Receive_CINEMATIC_ON_END)
	
	Game.GlobalEventSystem:AddListener(EEventTypesV2.BATTLE_LOCKTARGET_CHANGE, "Receive_BATTLE_LOCKTARGET_CHANGE", self)
    
	Game.GlobalEventSystem:AddListener(EEventTypesV2.ENTITY_COUNT_CHANGED, "OnEntitysCountChanged", self)

	Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_TELEPORT_ARRIVED, "OnPlayerTeleportArrived", self)

    --裁剪初始
	self:SetAoiPlayerLimitNum(Game.SettingsManager:GetIniData(Game.PlatformScalabilitySettings.PlayerMaximumKeyStr))
    self:SetNpcLimitNum(Game.SettingsManager:GetIniData(Game.PlatformScalabilitySettings.NPCMaximumKeyStr))
    self:SetBriefAoiMode(Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.AOI_DISPLAY_MODE))
	
	if self.NeedWaterWind then
		--编辑器预览水场风场效果需要
		self:SetEditorPreviewWorld(self.PreviewWorldID)
		--初始化加载所有的WaterWave的MotorTexture资源 @hujianglong
		self:InitDynamicWaterWave()
	end
end

function WorldManager:PreUnInit()
	self:HandleWorldCleanup()
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.WORLD_NOT_READY_TO_PLAY
	Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function WorldManager:UnInit()
	self:DestroyCppManager()
end

function WorldManager:SetNeedWaterWind(value)
	self.NeedWaterWind = value
end

function WorldManager:GetCurState()
	return self.WorldManageState
end

-- 因为CPP层使用uint8,所以最多支持到255
WorldManager.__MaxMaterialIndex__ = 1 << 8

function WorldManager:initTerrainDataToCpp()
    self.cppMgr:SetQueryTerrainObjectTypes(CollisionConst.QUERY_BY_OBJECTTYPES.COMMON_WORLD_STATIC)

    local terrainDatas = Game.TableData.GetTerrainPhysicalMaterialDataTable()
    local terrain2Suffix = slua.Map(EPropertyClass.Str, EPropertyClass.Str)
    for terrainName, terrainData in ksbcpairs(terrainDatas) do
        terrain2Suffix:Add(terrainName, terrainData.Name)
    end

    self.cppMgr:InnerInitTerrainData(terrain2Suffix)

    local materialMaskDatas = Game.TableData.GetTerrainMaterialMaskDataTable()
    local materialIndex2TerrainName = slua.Map(EPropertyClass.Byte, EPropertyClass.Str)
    for materialIndex, materialMaskData in ksbcpairs(materialMaskDatas) do
        if (materialIndex < 0) or (self.__MaxMaterialIndex__ <= materialIndex) then
            Log.WarningFormat("[initTerrainDataToCpp] illegal material index %s", materialIndex)
            goto continue
        end

        materialIndex2TerrainName:Add(materialIndex, materialMaskData.TerrainName)
        :: continue ::
    end

    self.cppMgr:InnerInitMaterialMaskData(materialIndex2TerrainName)
end

-- 增量更新地面材质数据数据到cpp
---@public
---@param terrainName string
---@param suffix number
function WorldManager:UpdateTerrainDataToCpp(terrainName, suffix)
    self.cppMgr:InnerUpdateTerrainData(terrainName, suffix)
end

---@public
---@param materialIndex number[0,255]
---@param terrainName string
function WorldManager:UpdateMaterialIndexDataToCpp(materialIndex, terrainName)
    if (materialIndex < 0) or (self.__MaxMaterialIndex__ <= materialIndex) then
        Log.WarningFormat("[UpdateMaterialIndexDataToCpp] illegal material index %s", materialIndex)
        return
    end

    self.cppMgr:InnerUpdateMaterialMaskData(materialIndex, terrainName)
end

---有一些依赖主角的清理,要在ExitWorld前进行
function WorldManager:HandleWorldCleanupBeforeMainPlayerExit()
    Game.DialogueManagerV2:TerminateAllDialogue()
end

function WorldManager:HandleWorldCleanup(onlyRefreshMap)

	-- 清理顺序, 有强依赖, 请慎重维护:
	
	-- 1. 清理局内各种逻辑, 依赖WorldManager
	if Game.CinematicManager and Game.CinematicManager.CutsceneManager then
		Game.CinematicManager.CutsceneManager:OnLevelLoadStart()
	end
	Game.TestNpcManager:ClearAll()
	Game.LSceneActorEntityManager:ClearAllLSceneActor()
	Game.CommonInteractorManager:ClearAllInteractors()
	Game.SceneObjectManager:ClearAll()
	Game.PostProcessManager:RemoveAllPPInstancesOnLeaveSpace()
	Game.WorldDataManager:ClearAllSpaceStaticDataAction()
    
	-- 2. 进行局内逻辑保底清理, 依赖WorldManager
	if not onlyRefreshMap then
		Game.EntityManager:ClearLocalEntity()
		Game.UEActorManager:OnWorldMapBeginLoad()
		-- 清理动画缓存
		Game.RoleCompositeMgr:EmptyMontageCacheAll()
		self.AnimationManager:ClearAnimationSyncInfo()
		--清理除主角之外的所有UEActor
		Game.UEActorManager:ClearAllUEActor()
		self:EnableDynamicWaterWave(false)
		self:EnableLocalWindField(false)
	end

	-- 3. 最后, 进行WorldManager自身的清理
	self:SpaceDivision_ClearAllSceneElement()
	self.NpcHiddenByConfigIDForDialogue = {}
	self.ContinuousHiddenTriggerID = nil
	--隐藏表
	self.VisSetConfigMap = {}
	self.VisActorStateMap = {}
	self.HideAllWhiteListForCinematic = {}
	-- 类型: 计数
	self.HideEntityCategoryWhiteListCountMap = {}
	-- 唯一标识: 计数
	self.HideSpecificEntityWhiteListCountMap = {}

	--Space切换时 清理场景生成信息
	self.BatchSceneActor = {}
	table.clear(self.SceneActorCellInfos)
	table.clear(self.SceneActorBindingMapInfos)
	table.clear(self.SceneActorBindingInfos)
	
	--清理场景物体
	self.SceneObjectClientAOIManaged = {}

end

function WorldManager:OnMemoryWarning()
	Log.Debug("WorldManager:OnMemoryWarning MemoryWarning.")
	
	-- 清理动画缓存
	Game.RoleCompositeMgr:EmptyMontageCacheAll()
end


---重要流程事件 Begin------------------
-- ============================场景切换逻辑==========================================================
-- 玩家离场:
-- [1] space切换->MainPlayer OnLeaveSpace-> Entity AOI 离场/Entity摧毁
-- [2] 开始切换地图加载,进行一步回调等待触发[4], 并且同步执行[3]; 
-- [3] 同步执行，OnWorldMapBeginLoad->本地单位离场摧毁->主玩家不摧毁,但ExitWorld
-- [4] 地图加载完毕, OnLuaMapReady->LevelManager::OnMapLoaded-> 如果是WorldPartition, 则进行streaming source 添加, 并且等待streaming结束->OnFinalMapLoaded-> OnWorldMapLoadComplete
--                              -> 否则直接进行OnFinalMapLoaded->OnWorldMapLoadComplete
-- [5] 新地图准备完毕, 让主角进行LoadActor(异步)->AfterEnterWorld->Game.GameLoopManagerV2:OnMainPlayerEnterWorld--> WorldManager:AfterPlayerInit 开始调度其他单位入场
--                                                                                                        

function WorldManager:OnBeforeWorldMapBeginLoad()
	-- 临时,提前修改stage，用于Loading界面百分比显示
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_MAP
end

-- 这里已经换了space了
function WorldManager:OnWorldMapBeginLoad(spaceEid, levelId, onlyRefreshMap)
	Log.DebugFormat("[WorldManager-LifeTimeStage][WorldManager:OnWorldMapBeginLoad] levelId: %s", levelId)
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_MAP
	-- loading过程不再响应操作, 这里可能会造成位面切换的时候移动不连续, 后面位面整理的时候一起处理(包括下面的Game.me:ExitWorld也不合理)
	EnableMoveInput(false)
	EnableSkillInput(false)
	
	self.PlayEnterLevelID = 0
	self.LogicSpaceEidCorrespondingToCurrentMap = spaceEid

	if Game.me ~= nil then
        self:HandleWorldCleanupBeforeMainPlayerExit()

		---临时兼容现在的逻辑, 后续Entity完善后移除
		Game.me:ExitWorld()
		Game.ActorAppearanceManager:OnDestoryActor(Game.me:uid())
		Log.DebugFormat("[LogicUnit-LifeTimeStage][WorldManager:OnMainPlayer_ExitWorld] ")
		if self.SceneFieldCheckHandler then
			Game.TimerManager:StopTimerAndKill(self.SceneFieldCheckHandler)
			self.SceneFieldCheckHandler = nil
		end
		self:ResetBriefsAoiData()
	end

	self:HandleWorldCleanup(onlyRefreshMap)

	local space = Game.NetworkManager.GetLocalSpace()
    if not space then
        return
    end

	Game.WorldDataManager:OnPreLoadMap(levelId, space.planeID)
end

-- 注意: 这个函数不是Map PersistentLevel加载后立即调用的:
-- [1]如果有Streaming流程, 要等streaming
function WorldManager:OnWorldMapLoadComplete(levelId)
	Log.DebugFormat("[WorldManager-LifeTimeStage][WorldManager:OnWorldMapLoadComplete] ", levelId)
	
	-- todo 这里有些可能要细分下, 理论上是可以不等着map加载完毕的, 可以同时加载资源; 也是可以跟着后面的主角加载、单位加载同时做的, 只要在WORLD_BEGIN_PLAY之前结束即可
	-- todo 资源预加载还未补全 @胡江龙
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_PRELOAD_RESOURCE
    self.PlayEnterLevelID = levelId
	---- 新局内逻辑开始前重置一次, 不要在结束的时候, 避免entity摧毁和World切换不保序导致的Token归还污染数据冉静
	self.ViewBudgetMgr:Reset()
	-- 玩家因为会hold住当前角色, 所以之前申请的会在这里被刷掉; 所以, 玩家的申请是在这里Reset后再次申请
	-- 注意: 这个地方只是为了恢复申请预算数据, 因为主角实际上是没有销毁的, 所以主角自身上的能力开关数据仍然与之前一致

    self:EnableDynamicWaterWave(true)
    self:EnableLocalWindField(true)
	
	-- 需要每次場景切換的時候都要刷一遍, 預算allocator是跟著World走的
	self:EnableAnimationBudget(Game.PlatformScalabilitySettings.AnimationBudget.UseAnimationBudget)
	
	if Game.me ~= nil then
		Game.me:TryRequestViewDownGradingBudgetTokenBatch(WorldViewBudgetConst.FIRST_ROLE_BUDGET_FLAGS_VALUE, WorldViewBudgetConst.FIRST_ROLE_BUDGET_TAGS)
		-- 异步加载玩家角色, 加载完毕后, 通过OnMainPlayerEnterWorld 回调到WorldManager:AfterPlayerInit

		self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_MAINPLAYER
		Game.me:MainPlayerLoadActor()
	else
		-- 没有Game.me时, 也需要考虑cutscene
		-- todo 这里和后面world stage再一起梳理掉
		self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_CINEMATIC
		if Game.NetworkManager and Game.NetworkManager.GetLocalSpace() ~= nil then
			Game.NetworkManager.GetLocalSpace():onLevelSequenceRebuild()
		end

		if Game.CinematicManager and Game.CinematicManager.CutsceneManager then
			Game.CinematicManager.CutsceneManager:OnLevelLoaded()
		end
		self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.WORLD_BEGIN_PLAY	
	end
	
end

function WorldManager:AfterPlayerInit()
	Log.DebugFormat("[WorldManager-LifeTimeStage][WorldManager:AfterPlayerInit] ")

	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_LOCAL_ENTITIES
	
    --场景物
    Game.LSceneActorEntityManager:OnMapLoaded(self.PlayEnterLevelID)
    -- 执行静态导出场景数据的默认行为
    Game.WorldDataManager:ExecuteAllSpaceStaticDataAction()
    
	--分帧逻辑开启 <EMAIL>
	Game.UEActorManager:EnableFrameLimit(true)
	Game.UEActorManager:SetFrameLimitParam(0.001,0.001,10,1)

	-- 这里不太准, 因为这里可能会hold住需要进行binding的本地对象
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_AOI_NET_ENTITIES
    Game.UEActorManager:OnWorldMapLoaded()
    
    self:FlushMapSceneActorBindingInfos()

	--暂时脚本场景区域初版测试
	if self.SceneFieldCheckHandler then
		Game.TimerManager:StopTimerAndKill(self.SceneFieldCheckHandler)
		self.SceneFieldCheckHandler = nil
	end

	self.SceneFieldFieldTypeMap = {["Default"]=-1, ["Map"]=-1}

	local bHasSceneField = nil
	for _LayerName, v in pairs(self.SceneFieldFieldTypeMap) do
		local _bhasFieldData = Game.WorldDataManager:HasSceneFieldData(_LayerName)
		if _bhasFieldData then
			bHasSceneField = true
		end
		self.SceneFieldFieldTypeMap[_LayerName] = _bhasFieldData and -1 or nil
	end

	if bHasSceneField then
		self.SceneFieldCheckHandler = Game.TimerManager:CreateTimerAndStart(function() self:CheckSceneFieldPos() end, 3000, -1, nil, nil, true)
	end
	
	-- todo 这里应该不准确, 这里看起来是直接播放levelsequence了; 在MainPlayerLoadStage流程里, 也有一个播放LevelSequence的流程 @孟迪
	-- todo 这里也没有所谓的预加载控制
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.LOADING_CINEMATIC
	-- 在一些编辑器环境下, 不一定有对应Manager
	if Game.NetworkManager and Game.NetworkManager.GetLocalSpace() ~= nil then
		Game.NetworkManager.GetLocalSpace():onLevelSequenceRebuild()
	end

	if Game.CinematicManager and Game.CinematicManager.CutsceneManager then
		Game.CinematicManager.CutsceneManager:OnLevelLoaded()
	end
	
	EnableMoveInput(true)
	EnableSkillInput(true)
	self.WorldManageState = WorldViewConst.WORLD_MANAGE_STAGE.WORLD_BEGIN_PLAY
end

function WorldManager:CheckSceneFieldPos()
    if Game.me then
        local Pos = Game.me:GetPosition()
        if Pos then
            for _LayerName, _CurPosSceneFieldType in pairs(self.SceneFieldFieldTypeMap) do
                local _NewSceneFieldType = Game.WorldDataManager:GetSceneFieldTypeWithEdgeTolerance(Pos, _LayerName, nil, _CurPosSceneFieldType, 2000,5)
                if _NewSceneFieldType ~= _CurPosSceneFieldType then
                    self.SceneFieldFieldTypeMap[_LayerName] = _NewSceneFieldType
                    xpcall(Game.WorldManager.OnSceneFieldTypeChanged, _G.CallBackError, Game.WorldManager, _NewSceneFieldType, _LayerName)
                end
            end
        end
    end
end

function WorldManager:OnPlayerTeleportArrived()
    if self.SceneFieldFieldTypeMap and self.SceneFieldFieldTypeMap[sceneUtils.ESceneFieldType.Map] then
        self.SceneFieldFieldTypeMap[sceneUtils.ESceneFieldType.Map] = -1
        Game.ReminderManager:ResetReminderCDById(Enum.EReminderTextData.EXPLORE_AREA)
    end
end

function WorldManager:OnSceneFieldTypeChanged(newType, InLayerName)
    --Log.DebugFormat("[OnSceneFieldTypeChanged] newType=%s", newType)
    if InLayerName == sceneUtils.ESceneFieldType.Map then
        Game.DiscoverySystem:SecondLevelEnterRemind(newType)
    elseif InLayerName == sceneUtils.ESceneFieldType.Default then
        Game.AkAudioManager:OnSceneFieldTypeChanged(newType)
    end
end

function WorldManager:OnRoleHitMoveConstraintBounds(HitEntity, BeHitEntityUID)
	local Entity = Game.EntityManager:getEntity(BeHitEntityUID)
	if Entity then
		if Entity and Entity.bInWorld then
			Game.LSceneActorEntityManager:CallFunc(Entity.InsID, "OnRoleHitMoveConstraintBounds", HitEntity)
		end
	end
end

function WorldManager:OnDestroyEntity(NetEntityID)
	Log.DebugFormat("[LogicUnit-LifeTimeStage][WorldManager:OnDestroyEntity] EntityID:%s", NetEntityID)
    Game.UEActorManager:OnDestroyEntity(NetEntityID)
end

function WorldManager:IsWorldMapLoadComplete()
    return self.PlayEnterLevelID > 0
end

function WorldManager:OnSceneActorRegister(ActorID, ActorInstanceID, ActorType, PosX, PosY, PosZ, Pitch, Yaw, Roll)
    self:TryHideSceneActorOnRegister(ActorID, ActorInstanceID, ActorType)
    Game.UEActorManager:OnSceneActorRegister(ActorID, ActorInstanceID, ActorType, PosX, PosY, PosZ, Pitch, Yaw, Roll)
end

function WorldManager:OnSceneActorUnRegister(ActorInstanceID, ActorType)
    Game.UEActorManager:OnSceneActorUnRegister(ActorInstanceID, ActorType)
end

function WorldManager:FindSceneActor(InstanceID)
    if InstanceID == nil then
        return nil
    end

    return self.cppMgr:FindSceneActor(InstanceID)
end

---重要流程事件 End------------------


---AOI Brief，同屏人数，视椎裁剪 Begin------------------

function WorldManager:RegisterLODSignActor(InActor, InTagType)
    local TagType = InTagType
    if TagType == nil then
        TagType = "WorldActor"
    end

    return self.cppMgr:RegisterLODSignActor(InActor, TagType)
end

function WorldManager:UnRegisterLODSignActor(InActor)
    self.cppMgr:UnRegisterLODSignActor(InActor)
end

function WorldManager:RegisterClipObserver(InActor, FilterType)
    self.cppMgr:RegisterClipObserver(InActor, FilterType)
end

function WorldManager:ModifyClipPriority(InActorID, Entity)
    if self.cppMgr.ModifyClipPriority and Game.me then
        local ClipShowPriority = Game.me:GetPlayerClipShowPriority(Entity, self.BriefAoiMode)
        local ClipDistancePriority = Game.me:GetPlayerClipDistancePriority(self.BriefAoiMode)
        self.cppMgr:ModifyClipPriority(InActorID, ClipShowPriority, ClipDistancePriority)
    end
end

function WorldManager:UnRegisterClipObserver(InActor)
    self.cppMgr:UnRegisterClipObserver(InActor)
end

function WorldManager:SetClipFilter(FilterType, Num)
    self.cppMgr:SetClipFilterConfig(FilterType, Num)
    self.ClipFilterNumMap[FilterType] = Num
end

function WorldManager:HideAllPlayer(enable)
    self.cppMgr:HideAllPlayer(enable)
end

function WorldManager:ModifyClipParams(InActorID, bIgnore)
    self.cppMgr:ModifyClipParams(InActorID, bIgnore)
end

function WorldManager:OnActorLodChanged(EntityUID, NewLOD)
    local entity = Game.EntityManager:GetEntityByIntID(EntityUID)
    if entity then
        if entity.SetActorViewLod and entity.bInWorld then
            entity:SetActorViewLod(NewLOD, false)
        end
    end
end

function WorldManager:OnActorVisibleChanged(EntityUID, FilterType, ClipState)
	local Entity = Game.EntityManager:getEntity(EntityUID)
	if Entity then
		if (ClipState == 0) then
			Entity:SetActorVisible(Enum.EInVisibleReasons.Clip)
		else
			Entity:SetActorInVisible(Enum.EInVisibleReasons.Clip)
		end
	end
end

--设置AOI玩家同屏显示数量
function WorldManager:SetAoiPlayerLimitNum(InNum)
    xpcall(self.SetClipFilter, _G.CallBackError, self, EWActorType.PLAYER, InNum)
	self:UpdateAoiLevelSetting()
end

--设置NPC同屏显示数量（不对玩家开放）SettingsManager.NPCMaximumArr
--氛围Npc数量设置倍数处理
function WorldManager:SetNpcLimitNum(InNum)
    xpcall(self.SetClipFilter, _G.CallBackError, self, EWActorType.NPC_TASK, InNum)
	xpcall(self.SetClipFilter, _G.CallBackError, self, EWActorType.NPC_CROWD, InNum * Game.PlatformScalabilitySettings.NpcCrowdNumFactor)
end

--同屏显示模式修改，主动触发AOI更新
function WorldManager:SetBriefAoiMode(InMode)
    self.BriefAoiMode = InMode

    if self.EnableBriefAoiLevel then
        self:StartUpdateBriefsAoi()
    end
	
    --调整客户端裁剪相关的优先级
    self:ResetAvatarModelDisplayPriority()
end

--给索敌回调使用，重新开始更新AOI
function WorldManager:ReStartUpdateBriefsAoiForLockTarget(NewSelectActorID)
    if not self.EnableBriefAoiLevel then
        return
    end

    if self.LockBriefSelectActorID ~= NewSelectActorID then
        self.LockBriefSelectActorID = NewSelectActorID
        self:StartUpdateBriefsAoi()
    end
end

--画质修改
function WorldManager:OnSetOverallScalability(InOverallScalability)
    self:UpdateAoiLevelSetting()
	self:EnableDynamicWaterWave(true)
	self:EnableLocalWindField(true)
end

--开启Brief机制
function WorldManager:SetEnableBriefAoiLevel(bEnable)
    if self.EnableBriefAoiLevel ~= bEnable then
        self.EnableBriefAoiLevel = bEnable
        self:ResetBriefsAoiData()
    end
    
    self:UpdateAoiLevelSetting()
end

--修改PrimaryPlayer最大数量
function WorldManager:SetmaxPrimaryPlayerCount(InMaxPrimaryPlayerCount)
    self.MaxPrimaryPlayerCount = InMaxPrimaryPlayerCount

    if self.EnableBriefAoiLevel then
        self:StartUpdateBriefsAoi()
    end

    self:StartLoopUpdateBriefsAoi(self.EnableBriefAoiLevel)
end

function WorldManager:NotifyEntity_To_Brief(oldEnt, newEnt)
end

function WorldManager:NotifyBrief_To_Entity(oldEnt, newEnt)
end


--AvatarActor数量变化，来自EntityManager.AddEntity，已经关闭掉 @hujianglong
function WorldManager:OnEntitysCountChanged(EType, NewNum)
    if EType == EEntityType.AvatarActor then
        self.AvatarActorEntityNum = NewNum --总数的变化需要记录一下
    end
end


--主动更新的入口，设置被修改，锁定目标, AvatarLod更新(距离变化)，Avatar数量变化,好友关系变化，阵营变化
function WorldManager:StartUpdateBriefsAoi()
    Log.Debug("[AOI Brief]--------WorldManager:StartUpdateBriefsAoi Try!")
    if self.AoiLevelCheckUpdateTimer == nil then
        Log.Debug("[AOI Brief]--------WorldManager:StartUpdateBriefsAoi CreateTimerAndStart")
        self.AoiLevelCheckUpdateTimer = Game.TimerManager:CreateTimerAndStart(
                function()
                    self:UpdateAvatarAoiLevel()
                end,
        self.AoiLevelCheckTime, 1)
    end
end

--这里是设置后请求服务器回包后再开启LoopChecker功能 @hujianglong
function WorldManager:StartLoopUpdateBriefsAoi(enable)
    if not enable and self.AoiLevelLoopCheckUpdateTimer then
        Game.TimerManager:StopTimerAndKill(self.AoiLevelLoopCheckUpdateTimer)
        self.AoiLevelLoopCheckUpdateTimer = nil
        return
    end
    
    if enable and self.AoiLevelLoopCheckUpdateTimer == nil then
        Log.Debug("[AOI Brief]--------WorldManager:StartLoopUpdateBriefsAoi Start!")
        self.AoiLevelLoopCheckUpdateTimer = Game.TimerManager:CreateTimerAndStart(
                function()
                    self:UpdateAvatarAoiLevel()
                end,
        self.AoiLevelLoopCheckTime, -1)
    end
end

--上层调整LoopChecker的时间间隔(需要大于100ms)
function WorldManager:SetAoiLevelLoopCheckTime(time)
    if time < 100 then
        time = 100
    end

    if self.AoiLevelLoopCheckTime ~= time then
        self:StartLoopUpdateBriefsAoi(false)
        self.AoiLevelLoopCheckTime = time
        self:StartLoopUpdateBriefsAoi(true)
    end
end


--上报服务器，更新AOI Brief配置
function WorldManager:UpdateAoiLevelSetting()
    local maxPlayerCount = self.ClipFilterNumMap[EWActorType.PLAYER]
    local maxNonPlayerCount = self.ClipFilterNumMap[EWActorType.NPC_TASK]

    --非玩家不处理
    maxNonPlayerCount = 10000

    if Game.me and maxPlayerCount and maxNonPlayerCount then
        Log.Debug("[AOI Brief]--------WorldManager:UpdateAoiLevelSetting maxPlayerCount:", maxPlayerCount)
        local OverallScalability = Game.SettingsManager:GetOverallScalability()
        Game.me:ReqReportCliAoiLoadUplimit(maxPlayerCount, maxNonPlayerCount, OverallScalability)
    end
end

function WorldManager:TryBatchChangeEntityAoiLevel()
    if not Game.TimerManager:IsTimerExist(self.AoiLevelBatchModifyUpdateTimer) then
        self.AoiLevelBatchModifyUpdateTimer = nil
    end

    if self.AoiLevelBatchModifyUpdateTimer == nil then
        self.AoiLevelBatchModifyUpdateTimer = Game.TimerManager:CreateTimerAndStart(
                function()
                    self:BatchChangeEntityAoiLevel()
                end,
        self.AoiLevelBatchModifyTime, 1)
    end
end

--主动上报服务器，更新Entity的AOI等级，服务器也会Check，需要频率控制
function WorldManager:BatchChangeEntityAoiLevel()
    if self.AoiLevelBatchModifyUpdateTimer then
        Game.TimerManager:StopTimerAndKill(self.AoiLevelBatchModifyUpdateTimer)
        self.AoiLevelBatchModifyUpdateTimer = nil
    end

	if not Game.me then
		return
	end
	
    if #self.AoiLevel_PrimaryList > 0 or #self.AoiLevel_SecondaryList > 0 then
        self.AoiLevelBatchModifyRequestID = self.AoiLevelBatchModifyRequestID + 1
        Log.DebugFormat("[AOI Brief]--------WorldManager:RetBatchChangeEntityAoiLevel excute start. AoiLevelBatchModifyRequestID:%s  AoiLevel_PrimaryList:%s AoiLevel_SecondaryList:%s", 
        self.AoiLevelBatchModifyRequestID,#self.AoiLevel_PrimaryList,#self.AoiLevel_SecondaryList)
        Game.me:ReqBatchChangeEntityAoiLevel(self.AoiLevel_SecondaryList, self.AoiLevel_PrimaryList, self.AoiLevelBatchModifyRequestID)
    end
end

--服务器回包处理
function WorldManager:RetBatchChangeEntityAoiLevel(seqId)
    Log.Debug("[AOI Brief]--------WorldManager:RetBatchChangeEntityAoiLevel excute suceesed. AoiLevelBatchModifyRequestID: ", seqId)
end


--客户端计算Entity的AOI等级，最关键的一段代码
function WorldManager:UpdateAvatarAoiLevel()
    -- 清理定时器
    if self.AoiLevelCheckUpdateTimer then
        Game.TimerManager:StopTimerAndKill(self.AoiLevelCheckUpdateTimer)
        self.AoiLevelCheckUpdateTimer = nil
    end
    -- 前置条件检查
    if not self.EnableBriefAoiLevel or not Game.me then
        return
    end
    
    local realAvatarActorCount = Game.EntityManager:GetEntitiesCountByType(EEntityType.AvatarActor)
	local briefActorCount = self.AvatarActorEntityNum - realAvatarActorCount
	
    -- 仅当超过阈值时或Brief个数>0处理
    if realAvatarActorCount > self.MaxPrimaryPlayerCount or briefActorCount > 0 then

        -- 获取必要数据
        local MainPlayerPos = Game.me:GetPosition()
        local AvatarActorsWithBrief = Game.EntityManager:GetAllAvatarsWithBrief()

        -- 生成带权重的优先级列表
		self.AoiPriorityQueue = self.AoiPriorityQueue or {}
		table.clear(self.AoiPriorityQueue)
        for _, entity in pairs(AvatarActorsWithBrief) do
            local priorityData = self:GetBriefAoiPriorityData(MainPlayerPos, entity)
            table.insert(self.AoiPriorityQueue, priorityData)
        end

        -- 使用快速排序(O(n log n))
        table.sort(self.AoiPriorityQueue, function(a, b)
            if a.Priority ~= b.Priority then
                return a.Priority < b.Priority  -- 数值越小优先级越高
            end
            if a.Distance == b.Distance then -- 距离一样用uid排序
                return a.entity:uid() < b.entity:uid()
            end
            return a.Distance < b.Distance
        end)

        -- 分离不同AOI等级实体
        table.clear(self.AoiLevel_PrimaryList)
        table.clear(self.AoiLevel_SecondaryList)
        
        -- 敌方保底策略
        local minEnemyCount = Game.me:GetMinEnemyCount(self.BriefAoiMode)
        local curEnemyCount = 0
        local RealAvatarActors = Game.EntityManager:getEntitiesByType(EEntityType.AvatarActor)
        if RealAvatarActors then
            for _, entity in pairs(RealAvatarActors) do
                if Game.me:IsEmemy(entity) then
                    curEnemyCount = curEnemyCount + 1
                end
            end
        end
        Log.Debug("[AOI Brief]--------WorldManager:UpdateAvatarAoiLevel curEnemyCount: ", curEnemyCount)

        local maxPrimary = self.MaxPrimaryPlayerCount
        for i = 1, #self.AoiPriorityQueue do
            local data = self.AoiPriorityQueue[i]
            local isEnemy = false
            if Game.me:IsEmemy(data.entity) then
                isEnemy = true
            end
            if i <= maxPrimary or data.Priority <= PLAYER_SHOW_PRIORITY_MIN then
                if data.entity.isBriefEntity then
                    table.insert(self.AoiLevel_PrimaryList, data.entity:uid())
                end
            else
                if not data.entity.isBriefEntity then
                    local canBrief = false
                    if not isEnemy then
                        canBrief = true
                    elseif minEnemyCount < curEnemyCount then
                        canBrief = true
                    end

                    if canBrief then
                        table.insert(self.AoiLevel_SecondaryList, data.entity:uid())
                        if isEnemy then
                            curEnemyCount = curEnemyCount - 1
                        end
                    end
                elseif data.entity.isBriefEntity and isEnemy and curEnemyCount < minEnemyCount then --这里确保保底敌人能够升级到Primary
                    table.insert(self.AoiLevel_PrimaryList, data.entity:uid())
                    curEnemyCount = curEnemyCount + 1
                end
            end
        end
        Log.Debug("[AOI Brief]--------WorldManager:UpdateAvatarAoiLevel curEnemyCount: ", curEnemyCount)
        self:TryBatchChangeEntityAoiLevel()
    end
end

--Brief机制的优先级计算，关系到业务侧的需求，配表决定
function WorldManager:GetBriefAoiPriorityData(MainPlayerPos, entity)
    if entity and entity:uid() == self.LockBriefSelectActorID then
        return {entity = entity, Priority = 0, Distance = 0}
    end
    local AvatarPos = entity:GetPosition()
    local _Distance = math.floor((MainPlayerPos - AvatarPos):Size()/self.AOIUpdateDistancePrecision + 0.5) --距离的影响控制精度在5m @hujianglong
    local _Priority = Game.me:GetPlayerShowPriority(entity, self.BriefAoiMode)
    return {entity = entity, Priority = _Priority, Distance = _Distance}
end

function WorldManager:ResetBriefsAoiData()
    self.AoiLevel_SecondaryList = {}
    self.AoiLevel_PrimaryList = {}
    if self.AoiLevelBatchModifyUpdateTimer then
        Game.TimerManager:StopTimerAndKill(self.AoiLevelBatchModifyUpdateTimer)
        self.AoiLevelBatchModifyUpdateTimer = nil
    end
    if self.AoiLevelLoopCheckUpdateTimer then
        Game.TimerManager:StopTimerAndKill(self.AoiLevelLoopCheckUpdateTimer)
        self.AoiLevelLoopCheckUpdateTimer = nil
    end
    if self.AoiLevelCheckUpdateTimer then
        Game.TimerManager:StopTimerAndKill(self.AoiLevelCheckUpdateTimer)
        self.AoiLevelCheckUpdateTimer = nil
    end
    if self.AoiLevelAoiPlayerNumTimer then
        Game.TimerManager:StopTimerAndKill(self.AoiLevelAoiPlayerNumTimer)
        self.AoiLevelAoiPlayerNumTimer = nil
    end
    self.LockBriefSelectActorID = nil
    self.AoiLevelBatchModifyRequestID = 0
end

--修改客户端裁剪的优先级
function WorldManager:ResetAvatarModelDisplayPriority()
    local AvatarActorActors = Game.EntityManager:getEntitiesByType(EEntityType.AvatarActor)
    if AvatarActorActors then
        for _, entity in pairs(AvatarActorActors) do
            if entity ~= Game.me then
                if entity.CharacterID and entity.CharacterID > 0 and not entity.isBriefEntity and entity.isAvatar then
					Game.WorldManager:ModifyClipPriority(entity.CharacterID, entity)
                end
            end
        end
    end
end


-- 客户端AOI分级参数调整 AoiLevelLoopCheckTime=3000ms checkTime=500ms batchModifyTime=1100ms maxBriefChangedCountOnce=3 distancePrecision=500
function WorldManager:SetClientAOIParams(loopCheckTime, checkTime, batchModifyTime, maxBriefChangedCountOnce, distancePrecision)
    self:SetAoiLevelLoopCheckTime(loopCheckTime)
    self.AoiLevelCheckTime = checkTime
    self.AoiLevelBatchModifyTime = batchModifyTime
    self.AoiMaxBriefChangedCountOnce = maxBriefChangedCountOnce
    self.AOIUpdateDistancePrecision = distancePrecision
end

--锁定目标，Brief如果被锁定要切到Primary
function WorldManager:Receive_BATTLE_LOCKTARGET_CHANGE(InTargetType, targetuid)

	local targetEntity = Game.EntityManager:getEntityWithBrief(targetuid)
    local NewSelectActorID = nil
	
    if targetEntity == nil then
        self:ModifyClipParams(0, false)
        NewSelectActorID = nil
    else
        if targetEntity.isAvatar ~= true then
            self:ModifyClipParams(0, false)
            NewSelectActorID = nil
        else
            self:ModifyClipParams(0, false) --先清理全部的Ignore属性
            if not targetEntity.isBriefEntity and targetEntity.isAvatar == true then
                self:ModifyClipParams(targetEntity.CharacterID, true)
            end
            NewSelectActorID = targetEntity:uid()
        end
    end

    if self.LockBriefSelectActorID ~= NewSelectActorID then
        self.LockBriefSelectActorID = NewSelectActorID
        self:StartUpdateBriefsAoi()
    end
end

--功能开关，方便排查问题 @hujianglong
function WorldManager:EnableActorLimitClip(enable)
    self.cppMgr:EnableActorLimitClip(enable)
end

function WorldManager:EnableActorLod(enable)
    self.cppMgr:EnableActorLod(enable)
end

function WorldManager:EnableActorViewMatrixClip(enable)
    self.cppMgr:EnableActorViewMatrixClip(enable)
end

---AOI Brief，同屏人数，视椎裁剪 End------------------


---风场水场 Begin------------------
function WorldManager:GetHasWaterArea()
    return self.HasWaterArea
end

function WorldManager:GetHasWindField()
    return self.HasWindField
end

function WorldManager:GetHasDeformableLandscape()
	return self.HasDeformableLandscape
end

-- ==========================Dynamic Water Wave =====================================

function WorldManager:SetEditorPreviewWorld(InWorldID)
    Log.Debug("WaterWind: SetEditorPreviewWorld for Water or Wind!")
	self.PreviewWorldID = InWorldID
	if self.cppMgr and self.PreviewWorldID then
		self.cppMgr:SetPreviewWorld(InWorldID)
	end
end

---RequestDWWMotorForOnce
---@param motorTextureId int 水波纹样式图片武警
---@param posX float 位置 X
---@param posY float 位置 Y
---@param faceYaw float 朝向 yaw
---@param scaleX float 波纹缩放大小 X , 基准由波纹图片确定, 需要进行调试确认效果
---@param scaleY float 波纹缩放大小 Y , 基准由波纹图片确定, 需要进行调试确认效果
---@param waveMaxHeight float 波纹起伏最大高度, 基准由波纹图片确定, 需要进行调试确认效果
---@param foamScale float 泡面效果, 需要调试确认效果
function WorldManager:RequestDWWMotorForOnce(motorTextureId, posX, posY, faceYaw, scaleX, scaleY, waveMaxHeight, foamScale, timeGap , moveDirX, moveDirY, moveSpeed)
    if self.IsDynamicWaterWaveEnabled == false then
        return false, 0
    end
    
    if foamScale == nil then
        foamScale = 1.0
    end

	if timeGap == nil then
		timeGap = 0
	end

    local requestId = self:GetGlobalWaterWaveRequestId()
	
	self.cppMgr:AddMotorForDynamicWaterWave(requestId, motorTextureId, posX, posY, faceYaw, scaleX, scaleY, waveMaxHeight, foamScale, timeGap, moveDirX, moveDirY, moveSpeed)

	return true, requestId
end

function WorldManager:GetGlobalWaterWaveRequestId()
	local requestId = self.cppMgr:GetGlobalWaterWaveRequestId()
    return requestId
end

--初始化加载所有的WaterWave的MotorTexture资源，底层统一控制是否开启，上层最多是空调用 @hujianglong
function WorldManager:InitDynamicWaterWave()
    local tabledata = Game.TableData.GetWaterWaveMotorTextureDataTable()
    local pathList = slua.Array(EPropertyClass.Str)
    for _, v in ksbcpairs(tabledata) do
        pathList:Add(v.WaveTexturePath)
    end
    self.cppMgr:SetupDynamicWaterWaveMotorTextures(pathList)
    Log.Debug("WaterWind: InitDynamicWaterWave for Water or Wind!")
end

--设置回调需要刷新开关，这里只是负责逻辑层的开关
function WorldManager:EnableDynamicWaterWave(enable)
    if self.cppMgr:IsEnabledDynamicWaterWave() ~= true then
        enable = false
    end
    if self:GetHasWaterArea() ~= true then
        enable = false
    end
    self.IsDynamicWaterWaveEnabled = enable
    Log.DebugFormat("WaterWind: EnableDynamicWaterWave %s", tostring(enable))
end

function WorldManager:SetPivotActorForDynamicWaterWave(entityUid)
    Log.Debug("WaterWind: SetPivotActorForDynamicWaterWave for Water or Wind!")
    self.cppMgr:SetPivotActorForDynamicWaterWave(entityUid)
end

function WorldManager:CleanupPivotActorForDynamicWaterWave()
    self.cppMgr:CleanupPivotActorForDynamicWaterWave()
end

function WorldManager:UnRegisterWaterWaveTick(HandleID)
	self.cppMgr:UnRegisterWaterWaveTick(HandleID)
end
-- ==========================Dynamic Water Wave End=====================================

-- ==========================Local Wind Field =====================================
---RequestLWFMotorForOnce
---@param windType enum 风场类型
---@param posX float 位置 X
---@param posY float 位置 Y
---@param faceYaw float 朝向 yaw
---@param scaleX float 波纹缩放大小 X , 基准由波纹图片确定, 需要进行调试确认效果
---@param scaleY float 波纹缩放大小 Y , 基准由波纹图片确定, 需要进行调试确认效果
---@param strength float 风场强度
---@param fanCenterAngle float 扇形中心角
function WorldManager:RequestLWFMotorForOnce(windType, posX, posY, faceYaw, scaleX, scaleY, strength, fanCenterAngle)
    if self.IsEnableLocalWindField == false then
        return false
    end

    self.cppMgr:AddMotorForLocalWindField(windType, posX, posY, faceYaw, scaleX, scaleY, strength, fanCenterAngle)
    return true
end

function WorldManager:BindRoleMovementDynamicWaterWaveAndWindField(CharacterID, bBind)
	self.cppMgr:BindRoleMovementDynamicWaterWave(CharacterID, bBind)
	self.cppMgr:BindRoleMovementWindField(CharacterID, bBind)
end

--设置回调需要刷新开关，这里只是负责逻辑层的开关
function WorldManager:EnableLocalWindField(enable)
    if self.cppMgr:IsEnabledLocalWindField() ~= true then
        enable = false
    end
    if self:GetHasWindField() ~= true then
        enable = false
    end
    self.IsEnableLocalWindField = enable
end

function WorldManager:SetPivotActorForLocalWindField(entityUid)
    self.cppMgr:SetPivotActorForLocalWindField(entityUid)
end

function WorldManager:CleanupPivotActorForLocalWindField()
    self.cppMgr:CleanupPivotActorForLocalWindField()
end

-- ==========================Local Wind Field End=====================================

-- ==========================DeformableLandscape=====================================
function WorldManager:SetPivotActorForDeformableLandscape(entityUid)
	self.cppMgr:SetPivotActorForDeformableLandscape(entityUid)
end

function WorldManager:CleanupPivotActorForDeformableLandscape()
	self.cppMgr:CleanupPivotActorForDeformableLandscape()
end

function WorldManager:EnableDeformableLandscapeCaptureMode(entityUid, enable)
	self.cppMgr:EnableDeformableLandscapeCaptureMode(entityUid, enable)
end

function WorldManager:EnableDeformableLandscapeSimpleMode(entityUid, socketName, enable)
	self.cppMgr:EnableDeformableLandscapeSimpleMode(entityUid ,socketName ,enable)
end
-- ==========================DeformableLandscape End=====================================

---风场水场 End------------------

---KGEngineCore Begin------------------
function WorldManager:SetKGEngineCorePlayerActor(entityUid)
	self.cppMgr:SetKGEngineCorePlayerActor(entityUid)
end

function WorldManager:CleanupKGEngineCorePlayerActor()
	self.cppMgr:CleanupKGEngineCorePlayerActor()
end
---KGEngineCore End------------------


---显示隐藏业务 Begin------------------


function WorldManager:SetTaskNpcCategoryVisible(bVisible)
	self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Task, bVisible)
end

--隐藏Npc、P3玩家、氛围Npc接口
function WorldManager:SetAllEntityCategoryVisible(bVisible)
    self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.AoiPlayer, bVisible)
    self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Task, bVisible)
    self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Monster, bVisible)
    self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.PasserBy, bVisible)
    self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.MassAI, bVisible)
end

function WorldManager:SetEntityVisibleByCategory(HideActorType, bVisible, bForce)
    local HideConfig = self.VisSetConfigMap[HideActorType]
    if HideConfig == nil then
        HideConfig = 0
    end

    local bChanged = false
    if HideConfig > 0 and bVisible then
        HideConfig = HideConfig - 1
        bChanged = true
    elseif not bVisible then
        HideConfig = HideConfig + 1
        bChanged = true
    end

    self.VisSetConfigMap[HideActorType] = HideConfig

    if bChanged or bForce then
        self:refreshActorVisible(HideActorType, HideConfig <= 0, bForce)
    end
end

function WorldManager:IsEntityHiddenByEntityCategoryRule(Entity)
	-- 加速判断一下, 没有值就直接返回
	if not next(self.VisActorStateMap) then
		return false
	end
	
	-- 在hidden 白名单中, 不进行隐藏控制
	if self:IsHideAllActorWhiteListForCinematic(Entity) then
		return false 
	end
	
	return self:IsEntityHideByEntityCategory(Entity)
end

function WorldManager:GetEntityCategoryHiddenState(HideActorType)
    local LastVisible = self.VisActorStateMap[HideActorType]
    if LastVisible == nil then
        return false
    end

	if not self:IsHideAllTypeWhiteListForCinematic(HideActorType) then
		return not LastVisible
	else
		return false
	end
end

function WorldManager:IsHideAllTypeWhiteListForCinematic(HideType)
	local isWhiteCount = self.HideEntityCategoryWhiteListCountMap[HideType]
	if isWhiteCount == nil then
		return false
	end
	
	return isWhiteCount > 0
end

function WorldManager:IsHideAllActorWhiteListForCinematic(Entity)
	-- 这里获取唯一标识很trick, 要和外面送参数的地方强耦合
	local InstanceID = Entity.InstanceID or Entity.InsID
	if InstanceID == nil then
		return false
	end
	
	local isWhiteCount = self.HideSpecificEntityWhiteListCountMap[InstanceID]
	if isWhiteCount == nil then
		return false
	end
	
	return isWhiteCount > 0
end

function WorldManager:AddHideAllWhiteListForCinematic(TypeWhiteList,ActorWhiteList)
	local Token = ULLFunc.GetGlobalUniqueID()
	self.HideAllWhiteListForCinematic[Token] = {TypeWhiteList = TypeWhiteList,ActorWhiteList = ActorWhiteList}
	
	if TypeWhiteList ~= nil then
		for HideType, Value in pairs(TypeWhiteList) do
			if self.HideEntityCategoryWhiteListCountMap[HideType] == nil then
				self.HideEntityCategoryWhiteListCountMap[HideType] = 1
			else
				self.HideEntityCategoryWhiteListCountMap[HideType] = self.HideEntityCategoryWhiteListCountMap[HideType] + 1
			end
		end
	end

	if ActorWhiteList ~= nil then
		for UniqueID, _ in pairs(ActorWhiteList) do
			if self.HideSpecificEntityWhiteListCountMap[UniqueID] == nil then
				self.HideSpecificEntityWhiteListCountMap[UniqueID] = 1
			else
				self.HideSpecificEntityWhiteListCountMap[UniqueID] = self.HideSpecificEntityWhiteListCountMap[UniqueID] + 1
			end
		end
	end
	
	return Token
end

function WorldManager:RemoveHideAllWhiteListForCinematic(Token)
	local WhiteListData = self.HideAllWhiteListForCinematic[Token]
	if WhiteListData == nil then
		return 
	end

	if WhiteListData.TypeWhiteList ~= nil then
		for HideType, Value in pairs(WhiteListData.TypeWhiteList) do
			if self.HideEntityCategoryWhiteListCountMap[HideType] ~= nil then
				self.HideEntityCategoryWhiteListCountMap[HideType] = self.HideEntityCategoryWhiteListCountMap[HideType] - 1

				-- 做一个容错, 避免后续逻辑一直不生效
				if self.HideEntityCategoryWhiteListCountMap[HideType] < 0 then
					self.HideEntityCategoryWhiteListCountMap[HideType] = 0
				end
			end
		end
	end

	if WhiteListData.ActorWhiteList ~= nil then
		for UniqueID, _ in pairs(WhiteListData.ActorWhiteList) do
			if self.HideSpecificEntityWhiteListCountMap[UniqueID] ~= nil then
				self.HideSpecificEntityWhiteListCountMap[UniqueID] = self.HideSpecificEntityWhiteListCountMap[UniqueID] - 1

				-- 做一个容错, 避免后续逻辑一直不生效
				if self.HideSpecificEntityWhiteListCountMap[UniqueID] < 0 then
					self.HideSpecificEntityWhiteListCountMap[UniqueID] = 0
				end
			end
		end
	end
	
	self.HideAllWhiteListForCinematic[Token] = nil
end

ENTITY_CATEGORY_TO_TYPE_MAPPING = ENTITY_CATEGORY_TO_TYPE_MAPPING or {
	[Enum.EHideEntityCategory.Player] = 'MainPlayer',
	[Enum.EHideEntityCategory.AoiPlayer] = 'AvatarActor',
	[Enum.EHideEntityCategory.Task] = 'NpcActor',
	[Enum.EHideEntityCategory.Monster] = 'NpcActor',
	[Enum.EHideEntityCategory.PasserBy] = 'NpcActor',
	[Enum.EHideEntityCategory.PerformPet] = 'LocalPerformPetEntity',
	
	-- 下面开始就枚举和类型一致, 容器直接用EntityType进行查询
	[Enum.EHideEntityCategory.LSAE_MeshCarrier] = 'LSAE_MeshCarrier',
	[Enum.EHideEntityCategory.LSAE_TaskCollect] = 'LSAE_TaskCollect'
}

function WorldManager:IsEntityHideByEntityCategory(Entity)
	local EntityCategory = nil
	-- MassAI 不需要处理CrowdNpc, 那个底层subsystem 如果对mass npc隐藏， 它会直接摧毁脚本的entity
	if Game.EntityManager:IsNpc(Entity) then
		if Entity.NpcType == Enum.ENpcTypeData.Monster then
			EntityCategory = Enum.EHideEntityCategory.Monster
		elseif Entity.NpcType == Enum.ENpcTypeData.Task then
			EntityCategory = Enum.EHideEntityCategory.Task
		elseif Entity.NpcType == Enum.ENpcTypeData.PasserBy then
			EntityCategory = Enum.EHideEntityCategory.PasserBy
		end
	elseif Game.EntityManager:IsAOIAvatar(Entity) then
		EntityCategory = Enum.EHideEntityCategory.AoiPlayer
	elseif Game.EntityManager:IsLocalPerformPetEntity(Entity) then
		EntityCategory = Enum.EHideEntityCategory.PerformPet
	elseif Game.me == Entity then
		EntityCategory = Enum.EHideEntityCategory.Player
	else
		-- 根据Class Name进行类型映射
		EntityCategory = Enum.EHideEntityCategory[Entity.__cname]
	end

	if EntityCategory == nil then
		return false
	end
	
	return self:GetEntityCategoryHiddenState(EntityCategory)
	-- MassAI不再这里的控制范围内
end

function WorldManager:refreshActorVisible(HideActorType, bVisible, bForce)
    local LastVisible = self.VisActorStateMap[HideActorType]
    if LastVisible == nil then
        LastVisible = true
    end

    self.VisActorStateMap[HideActorType] = bVisible
	
	-- todo 再看是否还能能够细分处理 @孙亚
	if LastVisible ~= bVisible or bForce then
		if HideActorType == Enum.EHideEntityCategory.MassAI then
			if Game.MassCharacterManager then
				Game.MassCharacterManager:SetVisibleActors(bVisible)
				return
			end
		end

		local EntityType = ENTITY_CATEGORY_TO_TYPE_MAPPING[HideActorType]
		if EntityType == nil then
			Log.ErrorFormat("[WorldManager:refreshActorVisible]  EntityCategory:%s  Should Register To ENTITY_CATEGORY_TO_TYPE_MAPPING", HideActorType)
			return
		end

		local Entities = Game.EntityManager:getEntitiesByType(EntityType)
		if Entities == nil or not next(Entities) then
			return
		end

		local Invisible = not bVisible
		if EntityType ~= 'NpcActor' then
			for Eid, Entity in pairs(Entities) do
				-- 如果在白名单中, 不处理
				if not self:IsHideAllActorWhiteListForCinematic(Entity) then
					Entity:SetInvisibleByEntityCategory(Invisible)
				end
			end
		else
			-- NpcActor需要细分
			if HideActorType == Enum.EHideEntityCategory.Task then
				for Eid, Entity in pairs(Entities) do
					if Entity.NpcType == Enum.ENpcTypeData.Task then
						if not self:IsHideAllActorWhiteListForCinematic(Entity) then
							Entity:SetInvisibleByEntityCategory(Invisible)
						end
					end
				end
			elseif HideActorType == Enum.EHideEntityCategory.Monster then
				for Eid,Entity in pairs(Entities) do
					if Entity.NpcType == Enum.ENpcTypeData.Monster then
						if not self:IsHideAllActorWhiteListForCinematic(Entity) then
							Entity:SetInvisibleByEntityCategory(Invisible)
						end
					end
				end
			elseif HideActorType == Enum.EHideEntityCategory.PerformPet then
				for Eid,Entity in pairs(Entities) do
					if Game.EntityManager:IsLocalPerformPetEntity(Entity) then
						if not self:IsHideAllActorWhiteListForCinematic(Entity) then
							Entity:SetInvisibleByEntityCategory(Invisible)
						end
					end
				end
			elseif HideActorType == Enum.EHideEntityCategory.PasserBy then
				for Eid, Entity in pairs(Entities) do
					if Entity.NpcType == Enum.ENpcTypeData.Passerby then
						if not self:IsHideAllActorWhiteListForCinematic(Entity) then
							Entity:SetInvisibleByEntityCategory(Invisible)
						end
					end
				end
			end
		end
    end
end


function WorldManager:ClaimContinuousHiddenByDis(Center, Radius)
	if Radius >= 30 * 100 then
		Log.Error("ClaimContinuousHiddenByDis: Radius too Large! Try use SetCinematicHiddenByDis")
		return -1
	end

	if self.ContinuousHiddenTriggerID ~= nil then
		Log.Error("ClaimContinuousHiddenByDis: Prev Continuous Hidden Control Need to be Release :%s", self.ContinuousHiddenTriggerID)
	end

	local TriggerID = ULLFunc.GetGlobalUniqueID()
	
	Game.me:RegisterGridLogicTriggerWithPosition(TriggerID, Center.X, Center.Y, Center.Z, Radius, WorldViewConst.WORLD_GRID_TRIGGER_LOGIC_ENUM.DIALOGUE_CONTINUOUS_HIDDEN)
	
	self.ContinuousHiddenTriggerID = TriggerID
	-- 这里只是注册一下就行了, 具体的隐藏逻辑, 会随着Tirgger 回调上来进行处理
	local Entities = Game.EntityManager:getEntitiesByType("NpcActor")
	if Entities then
		for _, Ent in pairs(Entities) do
			if Ent.NpcType == Enum.ENpcTypeData.Task then
				Ent:RegisterGridLogicDetector(
					true,
					WorldViewConst.DEFAULT_WORLD_GRID_DETECTOR_RADIUS,
					WorldViewConst.WORLD_GRID_TRIGGER_LOGIC_ENUM.DIALOGUE_CONTINUOUS_HIDDEN
				)
			end
		end
	end
	
	-- notify Mass
	Game.MassCharacterManager:RegTexturePoint(Center, Radius)
	return TriggerID
end


function WorldManager:ReleaseContinuousHiddenByDis(TriggerID)
	if self.ContinuousHiddenTriggerID == nil or self.ContinuousHiddenTriggerID ~= TriggerID then
		Log.Error("ReleaseContinuousHiddenByDis: Unexpected Trigger ID, ContinuousHiddenTriggerID:%s   TiggerID Param:%s", 
			self.ContinuousHiddenTriggerID, TriggerID
		)
		return
	end
	
	-- Entity的显隐变化, 会在unregister的时候, 进行回调回来
	Game.me:UnregisterGridLogicTriggerWithPosition(TriggerID, true)
	local Entities = Game.EntityManager:getEntitiesByType("NpcActor")
	if Entities then
		for _, Ent in pairs(Entities) do
			if Ent.NpcType == Enum.ENpcTypeData.Task then
				Ent:UnregisterGridLogicDetector(
					WorldViewConst.WORLD_GRID_TRIGGER_LOGIC_ENUM.DIALOGUE_CONTINUOUS_HIDDEN, 
					true
				)
			end
		end
	end

	-- notify Mass
	Game.MassCharacterManager:UnRegTexturePoint(TriggerID)
	self.ContinuousHiddenTriggerID = nil
	return
end

function WorldManager:Receive_CINEMATIC_ON_START(Type, AssetID,TypeWhiteList, ActorsWhiteList)
	Log.Debug("Receive_CINEMATIC_ON_START Receive_CINEMATIC_ON_START Receive_CINEMATIC_ON_START ", AssetID)
	if self.CinematicHideAllWhiteListToken then
		self:RemoveHideAllWhiteListForCinematic(self.CinematicHideAllWhiteListToken)
		self.CinematicHideAllWhiteListToken = nil
	end
	self.CinematicHideAllWhiteListToken = self:AddHideAllWhiteListForCinematic(TypeWhiteList, ActorsWhiteList)
	
    if Type == Enum.CinematicType.Dialogue then

		if TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.AoiPlayer] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.AoiPlayer, false)
		end

		if  TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.Monster] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Monster, false)
		end

		if TypeWhiteList == nil or  not TypeWhiteList[Enum.EHideEntityCategory.PerformPet] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.PerformPet, false)
		end
		
        --G_InputControlDispatcher:SetEnableActiveOperateInputs(false)

    elseif Type == Enum.CinematicType.Cutscene then
		if  TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.Player] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Player, false)
		end

		if  TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.AoiPlayer] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.AoiPlayer, false)
		end

		if  TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.Monster] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Monster, false)
		end

        if  TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.Task] then
            self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Task, false)
        end

		if  TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.PasserBy] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.PasserBy, false)
		end

		if  TypeWhiteList == nil or not TypeWhiteList[Enum.EHideEntityCategory.MassAI] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.MassAI, false)
		end

		if TypeWhiteList == nil or  not TypeWhiteList[Enum.EHideEntityCategory.PerformPet] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.PerformPet, false)
		end

		if TypeWhiteList == nil or  not TypeWhiteList[Enum.EHideEntityCategory.LSAE_MeshCarrier] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.LSAE_MeshCarrier, false)
		end

		if TypeWhiteList == nil or  not TypeWhiteList[Enum.EHideEntityCategory.LSAE_TaskCollect] then
			self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.LSAE_TaskCollect, false)
		end
		
        G_InputControlDispatcher:SetEnableActiveOperateInputs(false)
    end
end

function WorldManager:Receive_CINEMATIC_ON_END(Type, AssetID)
	Log.Debug("Receive_CINEMATIC_ON_END Receive_CINEMATIC_ON_END Receive_CINEMATIC_ON_END ", AssetID)
	if self.CinematicHideAllWhiteListToken then
		self:RemoveHideAllWhiteListForCinematic(self.CinematicHideAllWhiteListToken)
		self.CinematicHideAllWhiteListToken = nil
	end
	
    if Type == Enum.CinematicType.Dialogue then
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.AoiPlayer, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Monster, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.PerformPet, true)
        G_InputControlDispatcher:SetEnableActiveOperateInputs(true)
    elseif Type == Enum.CinematicType.Cutscene then
		-- 临时帮忙修复瑞尔比伯切副本模型不显示问题
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Player, true, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.AoiPlayer, true, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Task, true, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Monster, true, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.PasserBy, true, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.MassAI, true, true)
        self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.PerformPet, true, true)
		self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.LSAE_MeshCarrier, true, true)
		self:SetEntityVisibleByCategory(Enum.EHideEntityCategory.LSAE_TaskCollect, true, true)
        G_InputControlDispatcher:SetEnableActiveOperateInputs(true)
    end
end

-- 按照配置进行指定Npc的隐藏显隐控制
function WorldManager:SetNpcInvisibleControlByConfigIDForDialogue(Invisible, ConfigID)
	if Invisible then
		self.NpcHiddenByConfigIDForDialogue[ConfigID] = true
	else
		self.NpcHiddenByConfigIDForDialogue[ConfigID] = nil
	end

	local Entities = Game.EntityManager:getEntitiesByType("NpcActor")

	if Entities then
		for key, Ent in pairs(Entities) do
			if Ent.TemplateID == ConfigID then
				Ent:SetInvisibleByDialogueRule(Invisible)
			end
		end
	end
end

function WorldManager:TryInitInvisibleByDialogueWithConfigID(Entity)
	if self.NpcHiddenByConfigIDForDialogue[Entity.TemplateID] == true then
		Entity:SetInvisibleByDialogueRule(true)
	end
end

function WorldManager:TryHideSceneActorOnRegister(ActorID, ActorInstanceID, ActorType)
	if DEFAULT_HIDDEN_ACTOR_TYPES[ActorType] then
		LuaScriptAPI.SetActorHiddenInGame(ActorID, true)
	end
end

function WorldManager:RefreshEntityVisibleByPhotograph()
	self:innerRefreshEntityVisibleByPhotograph(Game.EntityManager:getEntitiesByType("NpcActor"))
	self:innerRefreshEntityVisibleByPhotograph(Game.EntityManager:getEntitiesByType("LocalDisplayChar"))
	self:innerRefreshEntityVisibleByPhotograph(Game.EntityManager:getEntitiesByType("MainPlayer"))
	self:innerRefreshEntityVisibleByPhotograph(Game.EntityManager:getEntitiesByType("AvatarActor"))
	self:innerRefreshEntityVisibleByPhotograph(Game.EntityManager:getEntitiesByType("CrowdNpc"))
end

function WorldManager:innerRefreshEntityVisibleByPhotograph(entities)
	if entities then
		for key, entity in pairs(entities) do
			local visibility = Game.PhotographSystem:CheckRoleEntityShow(entity:uid())
			entity:SetInvisibleByPhotographRule(not visibility)
		end
	end
end

function WorldManager:TryInitInvisibleByPhotograph(Entity)
	if not Game.BSManager.bIsInEditor and not Game.PhotographSystem:CheckRoleEntityShow(Entity:uid()) then
		Entity:SetInvisibleByPhotographRule(true)
	end
end

---显示隐藏业务 End------------------

---场景分区推送 Begin------------------

function WorldManager:OnAOIStateChanged(UID, bEnterSpace)
	Game.SceneObjectManager:OnSceneObjectAoiStateChanged(UID, bEnterSpace)
	Game.LSceneActorEntityManager:OnPrivateSceneActorAoiChanged(UID, bEnterSpace)
end

function WorldManager:ResetSpaceDivisionParams()

    self.FSDBodyShape_Temp.ShapeType = import("ESDBodyShapeType").Sphere
	self.FSDBodyShape_Temp.Radius = 0;

    self.FSDAOIRadiusShape_Temp.ShapeType = import("ESDBodyShapeType").Sphere
	
    if self.cppMgr and self.cppMgr.SpaceDivision then
		local spaceDivision = self.cppMgr.SpaceDivision
		-- 内外圈是用来做激活和失活处理, outer比inner 大, 避免在边界处来回变动造成的反复aoi state变动
		spaceDivision.AOI_OuterCircleRadius = WorldViewConst.AOI_DEACTIVATE_RADIUS
		-- 玩家用来探测的客户端AOI
		spaceDivision.AOI_ActivedRadius = WorldViewConst.AOI_ACTIVATE_RADIUS
		-- 不使用Z的分割
		spaceDivision.UsingZGrid = WorldViewConst.WORLD_GRID_USING_Z
		-- 玩家移动2m后再进行AOI check
		spaceDivision.AOI_CheckIntervalDis =  WorldViewConst.AOI_CHECK_DISTANCE_INTERNAL
		-- 格子大小为10m
		spaceDivision.DivisionGridSize = FVector(WorldViewConst.WORLD_GRID_SIZE, WorldViewConst.WORLD_GRID_SIZE, WorldViewConst.WORLD_GRID_SIZE)
		spaceDivision.DivisionMinGridSize = WorldViewConst.WORLD_GRID_SIZE
    end
end

function WorldManager:AddStaticSceneObjectToClientAOI(uid, x, y, z, entitySphereRadius)
	if self.SceneObjectClientAOIManaged[uid] ~= nil then
		Log.ErrorFormat("[WorldManager:AddStaticSceneObjectToClientAOI] Already Added, uid:%d", uid)
	end
	
	self.SceneObjectClientAOIManaged[uid] = 1
	
	-- 一定尾调用, 因为会立即触发一次判断, 如果满足范围条件, 立即回调
	self.cppMgr:AddAOISceneElement(uid, x, y, z, entitySphereRadius)
end

function WorldManager:RemoveStaticSceneObjectToClientAOI(uid)
	if self.SceneObjectClientAOIManaged[uid] == nil then
		return false
	end
	self.cppMgr:RemoveAOISceneElement(uid)
	self.SceneObjectClientAOIManaged[uid] = nil
	return true
end


function WorldManager:ConsumeSceneElementID()
	return self.cppMgr:ConsumeSceneElementID()
end

-- isMovableEntity: 如果是会运动的, 要天true, 这样位置更新的时候才会进行检测刷新
-- Result: 返回一个注册token, 取消的时候要拿着这个Token来取消
function WorldManager:RegisterGridLogicDetector(isMovableEntity, Entity, detectRadius, TriggerLogicItem)
	local uid = Entity:uid()
	local x, y, z = Entity:GetPosition_P()
	local TriggerType, DetectCBName = TriggerLogicItem[1], TriggerLogicItem[2]

	return self.cppMgr:RegisterGridLogicDetector(isMovableEntity, TriggerType, uid, x, y, z, detectRadius, DetectCBName)
end

--CallLeaveCBWhenAlreadyInTrigger: 按需填写, 默认为false; 填true, 则如果detector已经在trigger范围内了, 那么在注销的时候也会回调一次。 在单位ExitWorld清理逻辑时, 建议填false
function WorldManager:UnregisterGridLogicDetector(DetectorToken, CallLeaveCBWhenAlreadyInTrigger)
	if CallLeaveCBWhenAlreadyInTrigger == nil then
		CallLeaveCBWhenAlreadyInTrigger = false
	end
	
	self.cppMgr:UnregisterGridLogicDetector(DetectorToken, CallLeaveCBWhenAlreadyInTrigger)
end

-- Trigger可能是一个单位, 也可能是一个固定点
function WorldManager:RegisterGridLogicTriggerWithEntity(isMovableEntity, Entity, detectRadius, TriggerLogicItem)
	local uid = Entity:uid()
	local x, y, z = Entity:GetPosition_P()
	local TriggerType, TriggerCBName = TriggerLogicItem[1], TriggerLogicItem[3]

	return self.cppMgr:RegisterGridLogicTrigger(isMovableEntity, TriggerType, uid, x, y, z, detectRadius, TriggerCBName)
end

function WorldManager:RegisterGridLogicTriggerWithPosition(x, y, z, detectRadius, TriggerLogicItem)
	local TriggerType = TriggerLogicItem[1]

	-- 点的检测的trigger, 对于trigger是没有回调这么一说的
	return self.cppMgr:RegisterGridLogicTrigger(false, TriggerType, KG_INVALID_ENTITY_ID, x, y, z, detectRadius, "")
end

--CallLeaveCBWhenAlreadyInTrigger: 按需填写, 默认为false; 填true, 则如果detector已经在trigger范围内了, 那么在注销的时候也会回调一次。 在单位ExitWorld清理逻辑时, 建议填false
function WorldManager:UnregisterGridLogicTrigger(TriggerToken, CallLeaveCBWhenAlreadyInTrigger )
	if CallLeaveCBWhenAlreadyInTrigger == nil then
		CallLeaveCBWhenAlreadyInTrigger = false
	end

	self.cppMgr:UnregisterGridLogicTrigger(TriggerToken, CallLeaveCBWhenAlreadyInTrigger)
end

function WorldManager:SetTriggerElementExitTolerantDistance(TriggerToken , Distance)
	self.cppMgr:SetTriggerElementExitTolerantDistance(TriggerToken, Distance)
end


function WorldManager:SpaceDivision_ClearAllSceneElement()
    if self.cppMgr then
        self.cppMgr:ClearAllElement()
    end
end

function WorldManager:UpdateSceneElement_Transform(UID, InPos, InDir)
    if self.cppMgr then
        self.cppMgr:UpdateSceneElement_Transform(UID, InPos, InDir)
    end
end

---场景分区推送 End-----------------------


---场景美术动态绑定 Begin------------------

function WorldManager:RegisterSceneActorBinding(WorldContextObjectID, SoftPath, CellX, CellY, CallbackObj, CallbackName)
    if self.PlayEnterLevelID == 0 then
        local bindingID, _ = self:InternalAddSceneActorBindingInfo(WorldContextObjectID, SoftPath, CellX, CellY, CallbackObj, CallbackName)
        self.SceneActorBindingMapInfos[bindingID] = bindingID
        return bindingID
    end

	local actorID = LuaScriptAPI.GetActorIDFromSoftPath(WorldContextObjectID, SoftPath)
	if actorID ~= KG_INVALID_ID then
		Log.DebugFormat("RegisterSceneActorBinding ActorID:%s SoftPath:%s", actorID, SoftPath)
		CallbackObj[CallbackName](CallbackObj, SoftPath, actorID)
    elseif CellX == nil or CellY == nil then
        Log.Error("RegisterSceneActorBinding, invalid scene actor binding info", CallbackObj.InsID, SoftPath)
	else
		local bindingID, _ = self:InternalAddSceneActorBindingInfo(WorldContextObjectID, SoftPath, CellX, CellY, CallbackObj, CallbackName)

		local XBind = self.SceneActorCellInfos[CellX]
		if XBind == nil then
			XBind = {}
			self.SceneActorCellInfos[CellX] = XBind
		end

		local YBind = XBind[CellY]
		if YBind == nil then
			YBind = {}
			XBind[CellY] = YBind
		end

		YBind[bindingID] = bindingID
		return bindingID
	end
end

function WorldManager:UnregisterSceneActorBinding(BindingID)
    local bindingInfo = self.SceneActorBindingInfos[BindingID]
    if bindingInfo == nil then
        return
    end

    self.SceneActorCellInfos[bindingInfo.CellX][bindingInfo.CellY][BindingID] = nil
    self.SceneActorBindingMapInfos[BindingID] = nil
    self.SceneActorBindingInfos[BindingID] = nil
end

function WorldManager:InternalAddSceneActorBindingInfo(WorldContextObjectID, SoftPath, CellX, CellY, CallbackObj, CallbackName)
    local sceneActorBindingInfo = {
		WorldContextObjectID = WorldContextObjectID,
        SceneActorSoftPath = SoftPath,
        CallbackObj = CallbackObj,
        CallbackName = CallbackName,
        CellX = CellX,
        CellY = CellY,
    }

    self.CurSceneActorBindingUniqueID = self.CurSceneActorBindingUniqueID + 1
    if self.CurSceneActorBindingUniqueID > 0x7fffffff then
        self.CurSceneActorBindingUniqueID = 1
    end

    self.SceneActorBindingInfos[self.CurSceneActorBindingUniqueID] = sceneActorBindingInfo
    return self.CurSceneActorBindingUniqueID, sceneActorBindingInfo
end

function WorldManager:FlushMapSceneActorBindingInfos()
    for bindingID, _ in pairs(self.SceneActorBindingMapInfos) do
        local bindingInfo = self.SceneActorBindingInfos[bindingID]

		local actorID = LuaScriptAPI.GetActorIDFromSoftPath(bindingInfo.WorldContextObjectID, bindingInfo.SceneActorSoftPath)
		if actorID ~= KG_INVALID_ID then
			Log.Debug("FlushMapSceneActorBindingInfos, found binding actor", bindingInfo.SceneActorSoftPath)
			bindingInfo.CallbackObj[bindingInfo.CallbackName](bindingInfo.CallbackObj, bindingInfo.SceneActorSoftPath, actorID)
		elseif bindingInfo.CellX == nil or bindingInfo.CellY == nil then
            Log.Error("FlushMapSceneActorBindingInfos, invalid scene actor binding info", bindingInfo.CallbackObj.InsID, bindingInfo.SceneActorSoftPath)
        else
			local XBind = self.SceneActorCellInfos[bindingInfo.CellX]
			if XBind == nil then
				XBind = {}
				self.SceneActorCellInfos[bindingInfo.CellX] = XBind
			end

			local YBind = XBind[bindingInfo.CellY]
			if YBind == nil then
				YBind = {}
				XBind[bindingInfo.CellY] = YBind
			end

			YBind[bindingID] = bindingID
		end
    end

    table.clear(self.SceneActorBindingMapInfos)
end

function WorldManager:OnLuaWpCellLoaded(World, Added, X, Y, Lv, Bounds)
    local XBind = self.SceneActorCellInfos[X]
    if XBind then
        local YBind = XBind[Y]
        if YBind then
            for _, bindingID in pairs(YBind) do
                local bindingInfo = self.SceneActorBindingInfos[bindingID]

				local actorID = LuaScriptAPI.GetActorIDFromSoftPath(bindingInfo.WorldContextObjectID, bindingInfo.SceneActorSoftPath)
				if actorID ~= KG_INVALID_ID then
					Log.Debug("OnLuaWpCellLoaded, found binding actor", bindingInfo.SceneActorSoftPath)
					bindingInfo.CallbackObj[bindingInfo.CallbackName](bindingInfo.CallbackObj, bindingInfo.SceneActorSoftPath, actorID)
				end
            end
        end
    end
end

---场景美术动态绑定End------------------

---NpcEntity引用关系 Start------------------

function WorldManager:AddNpcRecord(Entity)
	if Entity.InstanceID then
		self.NpcInsIDMap[Entity.InstanceID] = Entity:uid()
	end
	if Entity.spawnerID then
		if not self.NpcSpawner_EntityIDMap[Entity.spawnerID] then
			self.NpcSpawner_EntityIDMap[Entity.spawnerID] = {}
		end
		self.NpcSpawner_EntityIDMap[Entity.spawnerID][Entity:uid()] = true
	end
end

function WorldManager:RemoveNpcRecord(Entity)
	if Entity.InstanceID then
		self.NpcInsIDMap[Entity.InstanceID] = nil
	end
	if Entity.spawnerID then
		if self.NpcSpawner_EntityIDMap[Entity.spawnerID] then
			if self.NpcSpawner_EntityIDMap[Entity.spawnerID][Entity:uid()] then
				self.NpcSpawner_EntityIDMap[Entity.spawnerID][Entity:uid()] = nil
				if not next(self.NpcSpawner_EntityIDMap[Entity.spawnerID]) then
					self.NpcSpawner_EntityIDMap[Entity.spawnerID] = nil
				end
			end
		end
	end
end

function WorldManager:GetNpcByInstance(InstanceID)
	return self.NpcInsIDMap[InstanceID]
end

function WorldManager:GetNpcBySpawnerID(SpawnerID)
	local Npcs
	if self.NpcSpawner_EntityIDMap[SpawnerID] then
		for EntityID, _ in pairs(self.NpcSpawner_EntityIDMap[SpawnerID]) do
			if Npcs == nil then
				Npcs = {}
			end
			table.insert(Npcs, EntityID)
		end
	end
	return Npcs
end


---NpcEntity引用关系 End------------------

---TOD路灯 Start------------------

function WorldManager:TurningTODStreetlight(Duration, bOff)
    self.cppMgr:TurningTODStreetlight(Duration, bOff)
end

function WorldManager:SetupStreetlightControlByComponentTag(ControlParams, StartTime, EndTime)
    self.cppMgr:SetupStreetlightControlByComponentTag(ControlParams, StartTime, EndTime)
end

function WorldManager:UpdateStreetlightControlByComponentTag(CurrentTime)
    self.cppMgr:UpdateStreetlightControlByComponentTag(CurrentTime)
end

function WorldManager:EndStreetlightControlByComponentTag(bCurrentTurnOff)
    self.cppMgr:EndStreetlightControlByComponentTag(bCurrentTurnOff)
end


---TOD路灯 End------------------

---通用触发器 Start------------------

function WorldManager:GetShapeTriggerProperty(Position, Rotator, InnerRadius, Radius, BoxExtent, ListenObj, EnterCB, LeaveCB, ToAttachEntityUID)
    local Shape = EC7ShapeCollisionType.Sphere
    if BoxExtent then
        Shape = EC7ShapeCollisionType.Box
    end
    local Rotation
    if Rotator then
        Rotation = { Rotator.Pitch, Rotator.Roll, Rotator.Yaw}
    else
        Rotation = {0, 0, 0}
    end
    local NewData = {
        TemplateID = 0,
        Position = {Position.X, Position.Y, Position.Z},
        Rotation = Rotation,
        Shape = Shape,
        InnerRadius = InnerRadius,
        Radius = Radius,
        BoxExtent = BoxExtent,
        ListenInfo = { ListenObj = ListenObj, EnterCB=EnterCB, LeaveCB=LeaveCB},
        ToAttachEntityUID = ToAttachEntityUID
    }
    return NewData
end

-- 添加指定位置的圆形触发圈
---@param Position FVector
---@param InnerRadius number | nil
---@param Radius number
---@param ListenObj table
---@param EnterCB string 入参1进入的EntityUID，入参2TriggerInsID, 入参3触发位置，
---@param LeaveCB string 入参1离开的EntityUID，入参2TriggerInsID
---@param ToAttachEntityUID number|nil 跟随移动的EntityUID
---@return number|nil TriggerEntityUID
function WorldManager:AddSphereTrigger(Position, InnerRadius, Radius, ListenObj, EnterCB, LeaveCB, ToAttachEntityUID)
    local NewData = self:GetShapeTriggerProperty(Position, nil, InnerRadius, Radius, nil, ListenObj, EnterCB, LeaveCB, ToAttachEntityUID)
    local ExtraTriggerEntity = Game.EntityManager:CreateLocalEntity(
            "ShapeTrigger",
            NewData
    )
    return ExtraTriggerEntity:uid()
end

-- 添加指定位置的方形触发圈
---@param Position FVector
---@param Rotator FRotator
---@param BoxExtent FVector
---@param ListenObj table
---@param EnterCB string 入参1进入的EntityUID, 入参2TriggerInsID,入参3触发位置
---@param LeaveCB string 入参1离开的EntityUID, 入参2TriggerInsID
---@param ToAttachEntityUID number|nil 跟随移动的EntityUID
---@return number|nil
function WorldManager:AddBoxTrigger(Position, Rotator, BoxExtent, ListenObj, EnterCB, LeaveCB, ToAttachEntityUID)
	local NewData = self:GetShapeTriggerProperty(Position, Rotator, nil, nil, BoxExtent, ListenObj, EnterCB, LeaveCB, ToAttachEntityUID)
    local ExtraTriggerEntity = Game.EntityManager:CreateLocalEntity(
            "ShapeTrigger",
            NewData
    )
    return ExtraTriggerEntity:uid()
end

--移除交互触发圈
---@param UID number
function WorldManager:RemoveTrigger(EntityUID)
	local Entity = Game.EntityManager:getEntity(EntityUID)
    if Entity then
        Entity:destroy()
    end
end

---通用触发器 End------------------

-- 任务创建本地传送门
function WorldManager:AddLocalPlanePortal(InsID, Position, Radius, bShowEffect)
	if InsID then
		local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(InsID)
		if SceneActorData then
			Position = SceneActorData.Transform.Position
		else
			Log.WarningFormat("AddLocalPlanePortal can not found instance %s", InsID)
			return nil
		end
	else
		InsID = import("LowLevelFunctions").GetGlobalUniqueID()
	end
	local NewData = {
		ActorType = EWActorType.TASK_PLANE_PORTAL,
		TemplateID = 0,
		InsID = InsID,
		Position = {Position.X, Position.Y, Position.Z},
		Rotation = {0, 0, 0},
		InteractRadius = Radius,
		bShowEffect = bShowEffect
	}
	Game.LSceneActorEntityManager:AddLocalPrivateSceneActorData(NewData)
	return NewData.InsID
end

--移除本地传送门
---@param UID number
function WorldManager:RemoveLocalPlanePortal(InsID)
	Game.LSceneActorEntityManager:DeleteLocalPrivateSceneActorData(InsID)
end

function WorldManager:CreateTeamMark(Index, Position, bGuildType)
	local InsID = ULLFunc.GetGlobalUniqueID()
	local NewData = {
		InsID = InsID,
		ActorType = EWActorType.TEAM_MARK,
		TemplateID = Index,
		Position = {Position.X, Position.Y, Position.Z},
		Rotation = {0, 0, 0},
		bGuildType = bGuildType
	}
	Game.LSceneActorEntityManager:AddLocalPrivateSceneActorData(NewData)
	return InsID
end

function WorldManager:RemoveTeamMark(InsID)
	Game.LSceneActorEntityManager:DeleteLocalPrivateSceneActorData(InsID)
end

--region 显示展示场景时控制大世界的某些效果
--[[
	1、显示展示场景时，如果world是TOD，直接控制MainSkyPerformer开关
	2、显示展示场景时，如果world不是TOD，通过一系列接口控制当前world的效果显隐
]]
---@public method 开关MainSkyPerformer
function WorldManager:EnableMainSkyPerformer(visible)
	self.cppMgr:EnableMainSkyPerformer(visible)
end

---@public method 开关平行光
function WorldManager:EnableDirectionalLight(visible)
	self.cppMgr:EnableDirectionalLight(visible)
end

---@public method 开关雾
function WorldManager:EnableFogs(visible)
	self.cppMgr:EnableFogs(visible)
end
--endregion

-- region AnimationBudget
---EnableAnimationBudget
---详见 https://dev.epicgames.com/documentation/en-us/unreal-engine/animation-budget-allocator-in-unreal-engine
---@param enable bool
---@param budgetTime 预算时间, 预算数量=预算时间/估算的每个SkeletalCom的TickComponent用时, 
---@param MinimumFullTickNum 最小满帧tick动画组件数量,  最终的满帧数量=min(MinimumFullTickNum, 预算数量)
---@param UseQuadraticAlpha float  使用x+x*x的值(x < 1.0), 进行单位降频处理, 简单可认为’距离越远, 被降频的越厉害'
function WorldManager:EnableAnimationBudget(enable)
	KGUECompositeOperateLibrary.UseSkeletalMeshComponentBudgeted(enable)
	local AnimationBudget = Game.PlatformScalabilitySettings.AnimationBudget
	self.cppMgr:EnableAnimationBudget( enable, AnimationBudget.BudgetTime, AnimationBudget.MiniFullTickNum, AnimationBudget.UseQuadraticAlpha)
	AnimationBudgetBlueprintLibrary.EnableAnimationBudget(Game.WorldContext, enable)
end

--endregion

--region MassNPC创建和销毁
function WorldManager:KCB_OnCrowdNPCRegister(actorID, suitLibKey, bodyType)
	Game.MassCharacterManager:OnCrowdNPCEnterISM(false, actorID, suitLibKey, bodyType)
end

function WorldManager:KCB_OnCrowdNPCUnRegister(actorID, suitLibKey, bodyType)
	Game.MassCharacterManager:OnCrowdNPCEnterISM(true, actorID, suitLibKey, bodyType)
end
--endregion

return WorldManager
