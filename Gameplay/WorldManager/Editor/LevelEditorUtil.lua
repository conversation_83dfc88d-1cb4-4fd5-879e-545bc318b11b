--require("Tools.LuaPanda").start("127.0.0.1", 8818)
local SceneUtils = kg_require("Shared.Utils.SceneUtils").SceneUtils
local worldConst = kg_require("Shared.Const.WorldConst")
function LevelEditor_GetGameLevelData()
    
    local LevelType2Name = {
        [worldConst.WORLD_TYPE.BIGWORLD] = "大世界",
        [worldConst.WORLD_TYPE.PLANE] = "文案副本",
        [worldConst.WORLD_TYPE.DUNGEON] = "关卡副本",
        -- Others = "其他副本"
    }
    
    local GameLevelData = slua.Array(import("EPropertyClass").Struct, import("GPGameLevelData"))
    -- 待plane表删除之后就不用记录了
    local PlaneAsLevel = {}
    --LevelMap表
    for _, _LevelData in ksbcpairs(Game.TableData.GetLevelMapDataTable()) do
        Log.DebugFormat("LevelEditor_GetGameLevelData level id %s", _LevelData.ID)
        local _GLData = import("GPGameLevelData")()
        _GLData.ID = tostring(_LevelData.ID)
        _GLData.LevelType = _LevelData.Type
        _GLData.Name = _LevelData.Name or ""
        _GLData.LevelPath = _LevelData.LevelPath
        _GLData.LevelTypeName = LevelType2Name[_GLData.LevelType] or "其他副本"
        if _GLData.LevelType == worldConst.WORLD_TYPE.PLANE then
            PlaneAsLevel[_LevelData.ID] = true
        end
        if _LevelData.LevelData then
            --local _GroupsMap = slua.Map(EPropertyClass.Str, EPropertyClass.Str)
            for k, _GroupName in ksbcpairs(_LevelData.LevelData) do
                _GLData.LevelData:Add(_GroupName, _GLData.ID)
            end
        end

        GameLevelData:Add(_GLData)
    end

    return GameLevelData
end

function InnerGetInstanceIDSearchData(InsID)
    
    local _GPInstanceIDSearchData = import("GPInstanceIDSearchData")()

    local NodeLevelInfo = require("Data.Config.MapData.NodeLevelInfo")
    if NodeLevelInfo then
        local LevelInfo = NodeLevelInfo[InsID]
        if LevelInfo then
            local LevelMapID = nil
            local Levels = LevelInfo.Levels
            if Levels then
                for mapID, _ in pairs(Levels) do
                    LevelMapID = mapID
                    _GPInstanceIDSearchData.LevelIDs:Add(tostring(mapID))
                end
            end
        
            if LevelMapID then
                local levelMapData = Game.TableData.GetLevelMapDataRow(LevelMapID)
                if not levelMapData then
                    return _GPInstanceIDSearchData
                end
        
                _GPInstanceIDSearchData.ID = InsID
                

                return _GPInstanceIDSearchData
            end
        end
    end
	return _GPInstanceIDSearchData
end

local function DumpLuaStr(InKey, InValue, Indent, bFromArray)
    local BaseIndent = "  "
    local function GetIndentString(Num)
        local RetString = ""
        for Index=1, Num do
            RetString = RetString .. BaseIndent
        end
        return RetString
    end

    local KeyStr = ""
    local KeyType = type(InKey)
    if KeyType == "number" and math.type(InKey) == "integer" and bFromArray then
        KeyStr = string.format("[%d]", tonumber(InKey))
    else
        KeyStr = string.format("[\"%s\"]", InKey)
    end

    local ValueStr = ""
    local ValueType = type(InValue)
    if ValueType == "string" then
        ValueStr = string.format("\"%s\",", InValue)
    elseif ValueType == "boolean" then
        if InValue then
            ValueStr = "true,"
        else
            ValueStr = "false,"
        end
    elseif ValueType == "number" then
        if math.type(InValue) == "integer" then
            ValueStr = string.format("%d,", InValue)
        else
            ValueStr = string.format("%f,", InValue)
        end
    elseif ValueType == "userdata" then
        if InValue.__name == "FVector" then
            ValueStr = string.format("FVector(%f,%f,%f),", InValue.X, InValue.Y, InValue.Z)
        elseif InValue.__name == "FRotator" then
            ValueStr = string.format("FRotator(%f,%f,%f),", InValue.Pitch, InValue.Yaw, InValue.Roll)
        elseif InValue.__name == "FTransform" then
            ValueStr = string.format("FTransform(FQuat(0.000000,0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(1.000000,1.000000,1.000000)),")
        elseif InValue.__name == "FVector2D" then
            ValueStr = string.format("FVector2D(%f,%f),", InValue.X, InValue.Y) --Slua暂时解决lua加密问题
        else
            ValueStr = "{},"
        end
    elseif ValueType == "table" then
        if next(InValue) == nil then
            ValueStr = "{},"
        elseif #InValue > 0 then
            ValueStr = ValueStr .. "{\n"
            for Index, Elem in ipairs(InValue) do
                ValueStr = ValueStr .. DumpLuaStr(Index, Elem, Indent+1, true)
            end
            ValueStr = ValueStr .. GetIndentString(Indent) .. "},"
        else
            ValueStr = ValueStr .. "{\n"
            for ElemKey, Elem in pairs(InValue) do
                ValueStr = ValueStr .. DumpLuaStr(ElemKey, Elem, Indent+1, false)
            end
            ValueStr = ValueStr .. GetIndentString(Indent) .. "},"
        end
    else
        ValueStr = nil
    end

    return string.format("%s%s = %s\n", GetIndentString(Indent), KeyStr, ValueStr);
end

local function DumpArray(Array)
    if #Array == 0 then
        return "return {\n}"
    else
        local LuaRet = "return {\n"
        for Index, WayData in ipairs(Array) do
            LuaRet = LuaRet .. DumpLuaStr(Index, WayData, 0, true)
        end
        return LuaRet .. "}"
    end
end


local function FindElemNotInAnother(SourceArray, TargetArray)
    for _, Group in pairs(SourceArray) do
        if not table.contains(TargetArray, Group) then
            return Group
        end
    end
    return nil
end

local function GetLevelName(LevelOrPlaneId)
    local levelMapData = Game.TableData.GetLevelMapDataRow(LevelOrPlaneId)
    if not levelMapData then
        return
    end
    local args = string.split(levelMapData.LevelPath, "/")
    if #args == 0 then
        return nil
    end
    return args[#args]
end

local MapDataRoot = import("KismetSystemLibrary").GetProjectDirectory() .. "Content/Script/Data/Config/MapData/"
local GameLevelConfigPath =  MapDataRoot .. "GameLevelConfig/"

function MoveLevelGroupToAnotherLevel(SourceId, TargetID, ToMoveGroups)
    if #ToMoveGroups == 0 then
        return "目标Groups为空"
    end
    local NewSourceGroupData, NewTargetGroupData = {}, {}
    local ret, SourceGroupsData = xpcall(require, _G.CallBackError, string.format("Data.Config.MapData.GameLevelConfig.%s", SourceId))
    if not ret or not SourceGroupsData then
        return string.format("源地图:%s配置不存在", SourceId)
    end
    for _, Group in ipairs(SourceGroupsData) do
        if not table.contains(ToMoveGroups, Group) then
            table.insert(NewSourceGroupData, Group)
        end
    end
    local NotExistSourceGroup = FindElemNotInAnother(ToMoveGroups,  SourceGroupsData)
    if NotExistSourceGroup ~= nil  then
        return string.format("源地图:%s配置中Group:%s不存在", SourceId, NotExistSourceGroup)
    end
    
    local retTarget, TargetGroupsData = xpcall(require, _G.CallBackError, string.format("Data.Config.MapData.GameLevelConfig.%s", TargetID))
    if not retTarget or not TargetGroupsData then
        return string.format("目标地图:%s配置不存在", TargetID)
    end
    for _, Group in ipairs(TargetGroupsData) do
        if table.contains(ToMoveGroups, Group) then
            return string.format("目标地图:%s配置中Group:%s已存在", TargetID, Group)
        else
            table.insert(NewTargetGroupData, Group)
        end
    end
    for _, Group in ipairs(ToMoveGroups) do
        if table.contains(NewTargetGroupData, Group) then
            return string.format("目标地图:%s配置中Group:%s已存在", TargetID, Group)
        else
            table.insert(NewTargetGroupData, Group)
        end
    end

    local SourceLevelName = GetLevelName(SourceId)
    local TargetLevelName = GetLevelName(TargetID)
    if not SourceLevelName then
        return string.format("源地图:%s配置文件夹不存在", SourceId)
    end
    if not TargetLevelName then
        return string.format("目标地图:%s配置文件夹不存在", TargetID)
    end

    local ULuaFunctionLibrary = import("LuaFunctionLibrary")
    local GamePlayEditorModeLib = import("FGamePlayEditorModeLib")
    if SourceLevelName ~= TargetLevelName then
        for _, Group in ipairs(ToMoveGroups) do
            local SourceGroupPath = string.format("%s%s/%s/", MapDataRoot, SourceLevelName, Group)
            local TargetGroupPath = string.format("%s%s/%s/", MapDataRoot, TargetLevelName, Group)
            local bFound, FilesTmp, Files
            bFound, Files = import("LuaFunctionLibrary").FindFiles(SourceGroupPath, FilesTmp)
            if bFound then
                for _, File in pairs(Files:ToTable()) do
                    local EPName = string.sub(File, 1, -5)
                    local SourceFilePath = SourceGroupPath .. File
                    local LuaStr = ULuaFunctionLibrary.LoadFile(SourceFilePath)
                    if LuaStr then
                        local NodeData = require(string.format("Data.Config.MapData.%s.%s.%s", SourceLevelName, Group, EPName))
                        local NodeID = NodeData.ID
                        local OldFileName = NodeData.Name
                        local NewName = string.format("%s_%s", NodeData.Class, NodeID)
                        local Pat1 = '%["Name"%]%s*=%s*' .. string.format('\"%s\"', OldFileName)
                        LuaStr = string.gsub(LuaStr, Pat1, string.format("[\"Name\"] = \"%s\"", NewName))
                        local TargetFilePath = TargetGroupPath .. NewName .. '.lua'
                        GamePlayEditorModeLib.SaveConvertedNodeStr(TargetFilePath, LuaStr)
                    end
                    GamePlayEditorModeLib.DeleteNodeFile(SourceFilePath)
                end
            end
        end
    end
    
    local SourceGroupDataStr = DumpArray(NewSourceGroupData)
    GamePlayEditorModeLib.SaveConvertedNodeStr(string.format("%s%s.lua", GameLevelConfigPath, SourceId), SourceGroupDataStr)
    local TargetGroupDataStr = DumpArray(NewTargetGroupData)
    GamePlayEditorModeLib.SaveConvertedNodeStr(string.format("%s%s.lua", GameLevelConfigPath, TargetID), TargetGroupDataStr)
    return ""
end
