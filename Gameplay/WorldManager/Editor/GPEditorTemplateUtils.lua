local EPropertyClass = import("EPropertyClass")

local GPEditorTemplateUtils = {}

function GPEditorTemplateUtils.TemplateProcess_Position(Position, ActorTransform)
    local ActorPosition = ActorTransform:GetLocation()
    return FVector(Position.X-ActorPosition.X, Position.Y-ActorPosition.Y, Position.Z-ActorPosition.Z)
end

function GPEditorTemplateUtils.TemplateProcess_Rotator(Rotator, ActorTransform)
    local ActorQuatInverse = ActorTransform:GetRotation():Inverse()
    local ChildQuat = Rotator:ToQuat() * ActorQuatInverse
    return ChildQuat:Rotator()
end

local OneVector = FVector(1, 1, 1)
function GPEditorTemplateUtils.TemplateProcess_NpcSpawnerMember(PropertyActor, ActorTransform, OutChildIndexMap)
    local bSingleSpawner = PropertyActor.ActorType == EWActorType.NPC_SINGLE_SPAWNER
    local NewMemberTable = {}
    for i = 0, PropertyActor.GroupMember:Num()-1 do
        local Member = PropertyActor.GroupMember:Get(i)
        if i==0 and bSingleSpawner then
            local NewId = PropertyActor.ID
            Member.InstanceID = NewId
        else
            local NewId = string.format("%s%02d", PropertyActor.ID, i+1)
            OutChildIndexMap:Add(Member.InstanceID, NewId)
            Member.InstanceID = NewId
        end
        table.insert(NewMemberTable, Member)
    end
    PropertyActor.GroupMember:Clear()
    for _, Member in ipairs(NewMemberTable) do
        PropertyActor.GroupMember:Add(Member)
    end
end

function GPEditorTemplateUtils.ProcessTemplatizedChildProperty(PropertyActor, ActorTransform)
    local OutMap = slua.Map(EPropertyClass.Str, EPropertyClass.Str)
    if PropertyActor.ActorType == EWActorType.NPC_SINGLE_SPAWNER or
            PropertyActor.ActorType == EWActorType.NPC_SPAWNER then
        GPEditorTemplateUtils.TemplateProcess_NpcSpawnerMember(PropertyActor, ActorTransform, OutMap)
    end
    return OutMap
end


function GPEditorTemplateUtils.UpdateTemplateChild_NpcSpawnerMember(PropertyActor, ActorTransform, ChildData)
    local GroupChildCustomTransform = import("GroupChildCustomTransform")()
    for i = 0, PropertyActor.GroupMember:Num()-1 do
        local Member = PropertyActor.GroupMember:Get(i)
        Member.Position = GPEditorTemplateUtils.TemplateProcess_Position(Member.Position, ActorTransform)
        Member.Rotator = GPEditorTemplateUtils.TemplateProcess_Rotator(Member.Rotator, ActorTransform)
        local RelTransform = FTransform(Member.Rotator:ToQuat(), Member.Position, OneVector)
        GroupChildCustomTransform.Transforms:Add(RelTransform)
    end
    ChildData.CustomTransform:Add("GroupMember", GroupChildCustomTransform)
    return ChildData
end

function GPEditorTemplateUtils.UpdateTemplateChild_WayPath(PropertyActor, ActorTransform, ChildData)
    local GroupChildCustomTransform = import("GroupChildCustomTransform")()
    for i = 0, PropertyActor.WayPointListV2:Num()-1 do
        local WayPoint = PropertyActor.WayPointListV2:Get(i)
        WayPoint.Position = GPEditorTemplateUtils.TemplateProcess_Position(WayPoint.Position, ActorTransform)
        WayPoint.Rotator = GPEditorTemplateUtils.TemplateProcess_Rotator(WayPoint.Rotator, ActorTransform)
        local RelTransform = FTransform(WayPoint.Rotator:ToQuat(), WayPoint.Position, OneVector)
        GroupChildCustomTransform.Transforms:Add(RelTransform)
    end
    ChildData.CustomTransform:Add("WayPointListV2", GroupChildCustomTransform)
    return ChildData
end

function GPEditorTemplateUtils.UpdateTemplatizedChildData(PropertyActor, ActorTransform, ChildData) 
    if PropertyActor.ActorType == EWActorType.NPC_SPAWNER then
        ChildData = GPEditorTemplateUtils.UpdateTemplateChild_NpcSpawnerMember(PropertyActor, ActorTransform, ChildData)
        ChildData = GPEditorTemplateUtils.UpdateTemplateChild_WayPath(PropertyActor, ActorTransform, ChildData)
    elseif PropertyActor.ActorType == EWActorType.WAY_POINT_PATH then
        ChildData = GPEditorTemplateUtils.UpdateTemplateChild_WayPath(PropertyActor, ActorTransform, ChildData)
    end
    return ChildData
end


function GPEditorTemplateUtils.InstanceProcess_Position(ActorPosition, DeltaPosition)
    return FVector(DeltaPosition.X + ActorPosition.X, DeltaPosition.Y + ActorPosition.Y, DeltaPosition.Z + ActorPosition.Z)
end

function GPEditorTemplateUtils.InstanceProcess_Rotator(ActorRotator, DeltaRotator)
    local ChildQuat = DeltaRotator:ToQuat() * ActorRotator:ToQuat()
    return ChildQuat:Rotator()
end

function GPEditorTemplateUtils.InstanceProcess_NpcSpawnerMember(ChildActor, RelativeTransform, ChildData)
    local bSingleSpawner = ChildActor.ActorType == EWActorType.NPC_SINGLE_SPAWNER
    local NewMemberTable = {}
    
    local GroupMemberTransform = ChildData.CustomTransform:Get("GroupMember")
    for i = 0, ChildActor.GroupMember:Num()-1 do
        local Member = ChildActor.GroupMember:Get(i)
        -- 从相对还原成绝对
        if GroupMemberTransform and i<GroupMemberTransform.Transforms:Num() then
            local GroupTransform = GroupMemberTransform.Transforms:Get(i)
            Member.Position = GPEditorTemplateUtils.InstanceProcess_Position(ChildActor:K2_GetActorLocation(), GroupTransform:GetLocation())
            Member.Rotator = GPEditorTemplateUtils.InstanceProcess_Rotator(ChildActor:K2_GetActorRotation(), GroupTransform:GetRotation():Rotator())
        else
            -- 没有的话，就是父节点的位置
            Member.Position = ChildActor:K2_GetActorLocation()
            Member.Rotator = ChildActor:K2_GetActorRotation()
        end
        if i==0 and bSingleSpawner then
            Member.InstanceID = ChildActor.ID
        else
            local NewId = string.format("%s%02d", ChildActor.ID, i+1)
            Member.InstanceID = NewId
        end
        table.insert(NewMemberTable, Member)
    end

    ChildActor.GroupMember:Clear()
    for _, Member in ipairs(NewMemberTable) do
        ChildActor.GroupMember:Add(Member)
    end
end

function GPEditorTemplateUtils.InstanceProcess_WayPath(ChildActor, RelativeTransform, ChildData)
    local NewWayPathTable = {}
    local WayPointListV2Transform = ChildData.CustomTransform:Get("WayPointListV2")
    for i = 0, ChildActor.WayPointListV2:Num()-1 do
        local WayPoint = ChildActor.WayPointListV2:Get(i)
        -- 从相对还原成绝对
        if WayPointListV2Transform and i<WayPointListV2Transform.Transforms:Num() then
            local PointTransform = WayPointListV2Transform.Transforms:Get(i)
            WayPoint.Position = GPEditorTemplateUtils.InstanceProcess_Position(ChildActor:K2_GetActorLocation(), PointTransform:GetLocation())
            WayPoint.Rotator = GPEditorTemplateUtils.InstanceProcess_Rotator(ChildActor:K2_GetActorRotation(), PointTransform:GetRotation():Rotator())
        else
            -- 没有的话，就是父节点的位置
            WayPoint.Position = ChildActor:K2_GetActorLocation()
            WayPoint.Rotator = ChildActor:K2_GetActorRotation()
        end
        table.insert(NewWayPathTable, WayPoint)
    end
    ChildActor.WayPointListV2:Clear()
    for _, WayPoint in ipairs(NewWayPathTable) do
        ChildActor.WayPointListV2:Add(WayPoint)
    end
end

function GPEditorTemplateUtils.ProcessInstantialChildProperty(ChildActor, RelativeTransform, ChildData)
    if ChildActor.ActorType == EWActorType.NPC_SINGLE_SPAWNER or
            ChildActor.ActorType == EWActorType.NPC_SPAWNER then
        GPEditorTemplateUtils.InstanceProcess_NpcSpawnerMember(ChildActor, RelativeTransform, ChildData)
        GPEditorTemplateUtils.InstanceProcess_WayPath(ChildActor, RelativeTransform, ChildData)
    elseif ChildActor.ActorType == EWActorType.WAY_POINT_PATH then
        GPEditorTemplateUtils.InstanceProcess_WayPath(ChildActor, RelativeTransform, ChildData)
    end
end

return GPEditorTemplateUtils