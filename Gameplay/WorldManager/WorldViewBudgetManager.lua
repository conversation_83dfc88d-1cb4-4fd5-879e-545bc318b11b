---@class WorldViewBudgetManager
local WorldViewBudgetManager = DefineClass("WorldViewBudgetManager")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local bit = require("Framework.Utils.bit")
local DebugFlag = require("Gameplay.Debug.DebugFlag")

--详见 https://docs.corp.kuaishou.com/k/home/<USER>/fcABMyC6tEyDX1HdWDVvxwLSK

--luacheck: push ignore
INVALID_IMPORTANCE_CACHE_TIME = 5000 -- ms
local VIEW_CREATE_LIMIT_TAG = WorldViewBudgetConst.VIEW_CREATE_LIMIT_TAG
local VIEW_FREQUENCY_LIMIT_TAG = WorldViewBudgetConst.VIEW_FREQUENCY_LIMIT_TAG
local VIEW_DOWNGRADING_TAG = WorldViewBudgetConst.VIEW_DOWNGRADING_TAG

FIRST_ROLE_IMPORTANCE = WorldViewBudgetConst.FIRST_ROLE_IMPORTANCE
SECOND_ROLE_IMPORTANCE = WorldViewBudgetConst.SECOND_ROLE_IMPORTANCE
THIRD_ROLE_IMPORTANCE = WorldViewBudgetConst.THIRD_ROLE_IMPORTANCE

local VIEW_TAG_TO_FLAG_MAPPING = WorldViewBudgetConst.VIEW_TAG_TO_FLAG_MAPPING
ROLE_IMPORTANCE_MASK = WorldViewBudgetConst.ROLE_IMPORTANCE_MASK 
VIEW_TAG_MASK = WorldViewBudgetConst.VIEW_TAG_MASK

local VIEW_VALUE_LIMIT_TAG = WorldViewBudgetConst.VIEW_VALUE_LIMIT_TAG
local VIEW_CREATE_FLAG_TO_TAG_MAPPING = WorldViewBudgetConst.VIEW_CREATE_FLAG_TO_TAG_MAPPING
local VIEW_FREQUENCY_FLAG_TO_TAG_MAPPING = WorldViewBudgetConst.VIEW_FREQUENCY_FLAG_TO_TAG_MAPPING
local VIEW_DOWNGRADING_FLAG_TO_TAG_MAPPING = WorldViewBudgetConst.VIEW_DOWNGRADING_FLAG_TO_TAG_MAPPING
local VIEW_FREQUENCY_TRIGGER_TIME_LIMIT_FOR_EACH_ENTITY = WorldViewBudgetConst.VIEW_FREQUENCY_TRIGGER_TIME_LIMIT_FOR_EACH_ENTITY
local VIEW_BUDGET_FORBIDDEN_BY_ACTOR_LOD = WorldViewBudgetConst.VIEW_BUDGET_FORBIDDEN_BY_ACTOR_LOD

--luacheck: pop ignore
function WorldViewBudgetManager:ctor(platformLevel)
    
    self.ValueLimitMapping = {}
    
    self.CreateTotalBudgetMapping = {}
    self.CreateUsingBudgetMapping = {}

    self.FrequencyTotalBudgetMapping = {}
    self.FrequencyUsingBudgetMapping = {}
    self.FrequencyUsedWithAutoTimestampMapping = {} -- 申请使用, 按照时间自动回复投放, 事件戳
    self.FrequencyUsedWithAutoMapping = {} -- 申请使用, 自动归还
    self.FrequencyUsedWithTokenMapping = {}  -- 手动申请管理与归还
    self.FrequencyReleaseAccumulateMapping = {} -- 记录每个Tag每次释放后剩余的预算数量
    self.DefaultFrequencyCoolingOneItemTimeMS = 200
    -- 单位表现重入限频
    self.FrequencyTimeLimitForEntityMapping = {}

    self.DownGradingTotalBudgetMapping = {}
    self.DownGradingUsingBudgetMapping = {}
    
    self.NeedClearForImportance = {
        self.CreateUsingBudgetMapping,
        self.FrequencyUsingBudgetMapping,
        self.FrequencyUsedWithAutoMapping,
        self.FrequencyUsedWithTokenMapping,
        self.DownGradingUsingBudgetMapping
    }

    for _, mapping in ipairs(self.NeedClearForImportance) do
        mapping[FIRST_ROLE_IMPORTANCE] = {}
        mapping[SECOND_ROLE_IMPORTANCE] = {}
        mapping[THIRD_ROLE_IMPORTANCE] = {}
    end
    
    -- uid:重要度
    self.DynamicRoleImportanceCache = {}
    self.DynamicRoleImportanceCleanTimerHandle = Game.TimerManager:CreateTimerAndStart(
            function() self:InvalidDynamicRoleImportanceCache() end,
            INVALID_IMPORTANCE_CACHE_TIME, -1, nil, nil, true)
    
    self.CurrentUsingCpuLevel = WorldViewBudgetConst.VB_DEFAULT_CPU_LEVEL
    
    --debug
    self._debugStartRecordTime = 0
    self._failedRequestViewTag = {}
    self._successfulRequestViewTag = {}
    self._statisticsTimer = 0
    self._totalRecordInfo = {}

    --self:Reset()
end

function WorldViewBudgetManager:dtor()
    if self.DynamicRoleImportanceCleanTimerHandle then
        Game.TimerManager:StopTimerAndKill(self.DynamicRoleImportanceCleanTimerHandle)
        self.DynamicRoleImportanceCleanTimerHandle = nil
    end
    
end

function WorldViewBudgetManager:InvalidDynamicRoleImportanceCache()
    table.clear(self.DynamicRoleImportanceCache)
    table.clear(self.FrequencyTimeLimitForEntityMapping)
end

function WorldViewBudgetManager:Reset()
    self.CurrentUsingCpuLevel = WorldViewBudgetConst.VB_DEFAULT_CPU_LEVEL
    if Game.WorldManager ~= nil then
		self.CurrentUsingCpuLevel = Game.WorldManager:GetGameplayScability() + 1
    end
    self.DefaultFrequencyCoolingOneItemTimeMS = WorldViewBudgetConst.DEFAULT_VIEW_FREQUENCY_LIMIT_RECOVER_TIME[self.CurrentUsingCpuLevel]
    if  self.DefaultFrequencyCoolingOneItemTimeMS == nil then
        LOG_ERROR_FMT("[WorldViewBudgetManager:Reset] Unexpected CurrentUsingCpuLevel:%s ", self.CurrentUsingCpuLevel)
    end
    
    table.clear(self.CreateTotalBudgetMapping)
    table.clear(self.FrequencyTotalBudgetMapping)
    table.clear(self.FrequencyUsedWithAutoTimestampMapping)
    table.clear(self.DownGradingTotalBudgetMapping)

    for _, mapping in ipairs(self.NeedClearForImportance) do
        table.clear(mapping[FIRST_ROLE_IMPORTANCE])
        table.clear(mapping[SECOND_ROLE_IMPORTANCE])
        table.clear(mapping[THIRD_ROLE_IMPORTANCE])
    end
    
    self:SetBudgetLimitThresholdByCpuLevel(self.CurrentUsingCpuLevel, VIEW_CREATE_LIMIT_TAG, self.CreateTotalBudgetMapping, self.CreateUsingBudgetMapping)
    self:SetBudgetLimitThresholdByCpuLevel(self.CurrentUsingCpuLevel, VIEW_FREQUENCY_LIMIT_TAG, self.FrequencyTotalBudgetMapping, self.FrequencyUsingBudgetMapping)
    self:SetBudgetLimitThresholdByCpuLevel(self.CurrentUsingCpuLevel, VIEW_DOWNGRADING_TAG, self.DownGradingTotalBudgetMapping, self.DownGradingUsingBudgetMapping)
    
    self:SetValueLimitByCpulevel(self.CurrentUsingCpuLevel, self.ValueLimitMapping)
end


function WorldViewBudgetManager:SetValueLimitByCpulevel(cpuLevel, finalValueLimitMapping)
    if cpuLevel < WorldViewBudgetConst.VB_MINIMUM_CPU_LEVEL then
        cpuLevel = WorldViewBudgetConst.VB_MINIMUM_CPU_LEVEL
    elseif cpuLevel > WorldViewBudgetConst.VB_MAX_CPU_LEVEL then
        cpuLevel = WorldViewBudgetConst.VB_MAX_CPU_LEVEL
    end

    local realLevelIndex = cpuLevel + 1
    for valueLimitTag, valueList in pairs(WorldViewBudgetConst.VIEW_VALUE_LIMIT_UNDER_CPU_LEVEL) do
        finalValueLimitMapping[valueLimitTag] = valueList[realLevelIndex]
    end
    
end 

function WorldViewBudgetManager:SetBudgetLimitThresholdByCpuLevel(cpuLevel, configDataSourceKeys, finalTotalMapping, reinitUsingBudgetMapping)
    if cpuLevel < WorldViewBudgetConst.VB_MINIMUM_CPU_LEVEL then
        cpuLevel = WorldViewBudgetConst.VB_MINIMUM_CPU_LEVEL
    elseif cpuLevel > WorldViewBudgetConst.VB_MAX_CPU_LEVEL then
        cpuLevel = WorldViewBudgetConst.VB_MAX_CPU_LEVEL
    end
    
    local realLevelIndex = cpuLevel + 1
    local viewBudgetUnderCpuLevel = WorldViewBudgetConst.VIEW_BUDGET_UNDER_CPU_LEVEL
    
    for _, key in pairs(configDataSourceKeys) do
        if viewBudgetUnderCpuLevel[key] ~= nil then

            local configValue = viewBudgetUnderCpuLevel[key][realLevelIndex]

            -- 二级数量不可超出总预算的一定比例
            if configValue < 0  then
                LOG_ERROR_FMT("[WorldViewBudgetManager:SetBudgetLimitThresholdByCpuLevel] Unexpected negative value:%s for key:%s", configValue, key)
            end

            finalTotalMapping[key] = math.max(configValue, 0)
            if reinitUsingBudgetMapping ~= nil then
                reinitUsingBudgetMapping[FIRST_ROLE_IMPORTANCE][key] = 0
                reinitUsingBudgetMapping[SECOND_ROLE_IMPORTANCE][key] = 0
                reinitUsingBudgetMapping[THIRD_ROLE_IMPORTANCE][key] = 0
            end
            
        end
        
    end
    
end


function WorldViewBudgetManager:GetFinalBudgetImportance(requestEntity, targetEntity, forceRequestReason)
    local roleImportance = WorldViewBudgetConst.IMPORTANCE_REASON_TO_ROLE[forceRequestReason]

    if roleImportance == nil then
        roleImportance = requestEntity.ViewRoleImportance
        
        -- 只有requestEntity才进行importance动态计算
        if roleImportance == WorldViewBudgetConst.DYNAMIC_ROLE_IMPORTANCE then
            if Game.me == nil then
                return WorldViewBudgetConst.THIRD_ROLE_IMPORTANCE
            end
            
            local uid = requestEntity:uid()
            roleImportance = self.DynamicRoleImportanceCache[uid]
            if roleImportance == nil then
                roleImportance = Game.me:GetOtherPlayerViewRoleImportance(requestEntity)
                self.DynamicRoleImportanceCache[uid] = roleImportance
            end
        end
    end
    
    -- 两者选高者进行处理
    if targetEntity ~= nil then
        local targetImportance = targetEntity.ViewRoleImportance
        if targetImportance ~= WorldViewBudgetConst.DYNAMIC_ROLE_IMPORTANCE and targetImportance < roleImportance then
            roleImportance = WorldViewBudgetConst.SECOND_ROLE_IMPORTANCE
        end
    end
    
    return roleImportance
end

function WorldViewBudgetManager:TryConsumeViewBudget(totalBudgetMapping, usingBudgetMapping, viewTag, actorLod, roleImportance,  requestCount)
    local limitActorLod = VIEW_BUDGET_FORBIDDEN_BY_ACTOR_LOD[viewTag]
    if limitActorLod ~= nil and actorLod ~= nil and actorLod >= limitActorLod then 
        return 0
    end
        
    if requestCount == nil then
        requestCount = 1
    end
    
    -- 第一重要度, 不做限制
    local firstUsingCount = usingBudgetMapping[FIRST_ROLE_IMPORTANCE][viewTag]
    if roleImportance == FIRST_ROLE_IMPORTANCE then
        
        local maxBudgetCount = totalBudgetMapping[viewTag]
        local leftBudget = maxBudgetCount - firstUsingCount
       
        if leftBudget <= 0 then
            return 0
        end
        
        local satisfiedCount = requestCount - leftBudget
        if satisfiedCount >= 0 then
            satisfiedCount = leftBudget
        else
            satisfiedCount = requestCount
        end
       
        usingBudgetMapping[FIRST_ROLE_IMPORTANCE][viewTag] = firstUsingCount + satisfiedCount
        return satisfiedCount
    end
    
    local secondUsingCount = usingBudgetMapping[SECOND_ROLE_IMPORTANCE][viewTag]
    -- 第二重要度, 可以占用第三重要度限制
    if roleImportance == SECOND_ROLE_IMPORTANCE then
      
        local maxBudgetCount = totalBudgetMapping[viewTag]
        local leftBudget = maxBudgetCount - secondUsingCount
        if leftBudget <= 0 then
            return 0
        end

        local satisfiedCount = requestCount - leftBudget
        if satisfiedCount >= 0 then
            satisfiedCount = leftBudget
        else
            satisfiedCount = requestCount
        end

        usingBudgetMapping[SECOND_ROLE_IMPORTANCE][viewTag] = secondUsingCount + satisfiedCount
        return satisfiedCount
    end
    
    -- 三级重要度, 不能超过实际预算
    local thirdUsingCount = usingBudgetMapping[THIRD_ROLE_IMPORTANCE][viewTag]
    local usedBudgetCount = thirdUsingCount + firstUsingCount + secondUsingCount
    local maxBudgetCount = totalBudgetMapping[viewTag]
    local leftBudget = maxBudgetCount - usedBudgetCount
    if leftBudget <= 0 then
        return 0
    end

    local satisfiedCount = requestCount - leftBudget
    if satisfiedCount >= 0 then
        satisfiedCount = leftBudget
    else
        satisfiedCount = requestCount
    end


    usingBudgetMapping[THIRD_ROLE_IMPORTANCE][viewTag] = thirdUsingCount + satisfiedCount
    return satisfiedCount
end

function WorldViewBudgetManager:TryProduceViewBudget(usingBudgetMapping, roleImportance, viewTag)
    
    if roleImportance == FIRST_ROLE_IMPORTANCE then
        local firstUsingCount = usingBudgetMapping[FIRST_ROLE_IMPORTANCE][viewTag]
        if firstUsingCount > 0 then
            usingBudgetMapping[FIRST_ROLE_IMPORTANCE][viewTag] = firstUsingCount - 1
        end
        return true
    end

    if roleImportance == SECOND_ROLE_IMPORTANCE then
        local secondUsingCount = usingBudgetMapping[SECOND_ROLE_IMPORTANCE][viewTag]
        if secondUsingCount > 0 then
            usingBudgetMapping[SECOND_ROLE_IMPORTANCE][viewTag] = secondUsingCount - 1
        end
        return true
    end
    
    local thirdUsingCount = usingBudgetMapping[THIRD_ROLE_IMPORTANCE][viewTag]
    if thirdUsingCount > 0 then
        usingBudgetMapping[THIRD_ROLE_IMPORTANCE][viewTag] = thirdUsingCount - 1
    end
    
    return false
end

-- ===================================Value Limit获取接口=========================================
function WorldViewBudgetManager:GetValue_TENTACLE_NUM_SIMULATE()
    return self.ValueLimitMapping[VIEW_VALUE_LIMIT_TAG.TENTACLE_NUM_SIMULATE]
end

function WorldViewBudgetManager:GetCurrentFxValueLimit()
	return self.ValueLimitMapping[VIEW_VALUE_LIMIT_TAG.FX]
end

-- ===================================表现单位控制限制=================================================
--function WorldViewBudgetManager:CreateWeapon(propData, requestEntity, forceRequestReason)
--    return self:RequestViewCreateToken(VIEW_CREATE_LIMIT_TAG.WEAPON,  requestEntity, nil, forceRequestReason, 'AttachItem', propData)
--end

-- 请求创建表现子弹的预算
---@public
function WorldViewBudgetManager:RequestPerformBulletToken(requestEntity, forceRequestReason)
    local viewCreateTag = VIEW_CREATE_LIMIT_TAG.PERFORM_BULLET
    local roleImportance = self:GetFinalBudgetImportance(requestEntity, nil, forceRequestReason)
    local actorViewLod = requestEntity and requestEntity.ActorViewLod or nil
    if self:TryConsumeViewBudget(self.CreateTotalBudgetMapping, self.CreateUsingBudgetMapping, viewCreateTag, actorViewLod, roleImportance, 1) <= 0 then
        return nil
    end

    return bit.bor(roleImportance, VIEW_TAG_TO_FLAG_MAPPING[viewCreateTag])
end

function WorldViewBudgetManager:RequestViewCreateToken(viewCreateTag, requestEntity, targetEntity, forceRequestReason, entityClsName, propData)
   local roleImportance = self:GetFinalBudgetImportance(requestEntity, targetEntity, forceRequestReason)

    if self:TryConsumeViewBudget(self.CreateTotalBudgetMapping, self.CreateUsingBudgetMapping, viewCreateTag, requestEntity and requestEntity.ActorViewLod or nil, roleImportance,1) <= 0 then
        return nil
    end

    local createdEntity = Game.EntityManager:CreateLocalEntity(entityClsName, propData)
    createdEntity:ObtainViewCreateBudgetToken(bit.bor(roleImportance, VIEW_TAG_TO_FLAG_MAPPING[viewCreateTag]))
    return createdEntity
end

function WorldViewBudgetManager:ReleaseViewCreateToken(requestToken)
    local roleImportance = bit.band(requestToken, ROLE_IMPORTANCE_MASK)
    local viewTagFlag = bit.band(requestToken, VIEW_TAG_MASK)
    self:TryProduceViewBudget(self.CreateUsingBudgetMapping, roleImportance, VIEW_CREATE_FLAG_TO_TAG_MAPPING[viewTagFlag])
end


-- ===================================表现频率与次数限制=========================================

function WorldViewBudgetManager:TryConsumeViewFrequency_HIT_PERFORM(requestEntity, targetEntity)
	if requestEntity.ViewRoleImportance == nil then
		return false
	end
	
    local reqRes = self:ConsumeViewFrequencyPermissionWithAutoProduce(VIEW_FREQUENCY_LIMIT_TAG.HIT_ADDITIVE_PERFORM, 1, requestEntity, targetEntity)
    if reqRes > 0 then
        self:_RecordRequestSuccess(VIEW_FREQUENCY_LIMIT_TAG.HIT_ADDITIVE_PERFORM)
    else
        self:_RecordRequestFail(VIEW_FREQUENCY_LIMIT_TAG.HIT_ADDITIVE_PERFORM)
    end
    return reqRes > 0
end
function WorldViewBudgetManager:TryConsumeViewFrequency_ADDITIVE_PERFORM(requestEntity, targetEntity)
	if requestEntity.ViewRoleImportance == nil then
		return false
	end
    local reqRes = self:ConsumeViewFrequencyPermissionWithAutoProduce(VIEW_FREQUENCY_LIMIT_TAG.HIT_ADDITIVE_PERFORM, 1, requestEntity, targetEntity)
    if reqRes > 0 then
        self:_RecordRequestSuccess(VIEW_FREQUENCY_LIMIT_TAG.HIT_ADDITIVE_PERFORM)
    else
        self:_RecordRequestFail(VIEW_FREQUENCY_LIMIT_TAG.HIT_ADDITIVE_PERFORM)
    end
    return reqRes > 0
end

function WorldViewBudgetManager:TryConsumeViewFrequency_HIT_FLASH_AND_SOUND(requestEntity, targetEntity)
	if requestEntity.ViewRoleImportance == nil then
		return false
	end
    local reqRes = self:ConsumeViewFrequencyPermissionWithAutoProduce(VIEW_FREQUENCY_LIMIT_TAG.HIT_FLASH_AND_SOUND, 1, requestEntity, targetEntity)
    if reqRes > 0 then
        self:_RecordRequestSuccess(VIEW_FREQUENCY_LIMIT_TAG.HIT_FLASH_AND_SOUND)
    else
        self:_RecordRequestFail(VIEW_FREQUENCY_LIMIT_TAG.HIT_FLASH_AND_SOUND)
    end
    return reqRes > 0
end


-- 材质切换
function WorldViewBudgetManager:TryRequestViewFrequency_MATERIAL(requestEntity)
	if requestEntity.ViewRoleImportance == nil then
		return false
	end
    return self:RequestViewFrequencyToken(VIEW_FREQUENCY_LIMIT_TAG.CHANGE_MATERIAL, requestEntity, nil, nil)
 end
 
--  function WorldViewBudgetManager:TryConsumeViewFrequency_MATERIAL(requestEntity, targetEntity)
--      return self:ConsumeViewFrequencyPermissionWithAutoProduce(VIEW_FREQUENCY_LIMIT_TAG.CHANGE_MATERIAL, 1, requestEntity, targetEntity) > 0
--  end

-- 3C音效播放请求接口,单个Entity不进行频率限制
---@public
---@return boolean
function WorldViewBudgetManager:TryConsumeViewFrequency_LOCO_SOUND(requestEntity)
	if requestEntity.ViewRoleImportance == nil then
		return false
	end
    local reqRes = self:ConsumeViewFrequencyPermissionWithAutoProduce(VIEW_FREQUENCY_LIMIT_TAG.LOCO_SOUND, 1, requestEntity)
    if reqRes > 0 then
        self:_RecordRequestSuccess(VIEW_FREQUENCY_LIMIT_TAG.LOCO_SOUND)
    else
        self:_RecordRequestFail(VIEW_FREQUENCY_LIMIT_TAG.LOCO_SOUND)
    end
    return reqRes > 0
end

-- 战斗音效播放请求接口,单个Entity不进行频率限制,预算自动释放
---@public
---@return boolean
function WorldViewBudgetManager:TryConsumeViewFrequency_ATTACK_SOUND(requestEntity, targetEntity)
	if requestEntity.ViewRoleImportance == nil then
		return false
	end
    local reqRes = self:ConsumeViewFrequencyPermissionWithAutoProduce(VIEW_FREQUENCY_LIMIT_TAG.ATTACK_SOUND, 1, requestEntity, targetEntity)
    if reqRes > 0 then
        self:_RecordRequestSuccess(VIEW_FREQUENCY_LIMIT_TAG.ATTACK_SOUND)
    else
        self:_RecordRequestFail(VIEW_FREQUENCY_LIMIT_TAG.ATTACK_SOUND)
    end
    return reqRes > 0
end

-- 战斗音效播放请求接口,单个Entity不进行频率限制,token需要手动释放,主要针Loop音效
---@public
---@return number
function WorldViewBudgetManager:TryRequestViewFrequency_ATTACK_SOUND(requestEntity, targetEntity)
	if requestEntity.ViewRoleImportance == nil then
		return false
	end
    return self:RequestViewFrequencyToken(VIEW_FREQUENCY_LIMIT_TAG.ATTACK_SOUND, requestEntity, targetEntity)
end

--  外部机制是一次性触发且不需要显式的进行严格生命周期操作, 可以用此接口进行投放权获取。 例如: 受击一次性特效、受击轻受击动作、跳字...
---ConsumeViewFrequencyPermissionWithAutoProduce
---@param frequencyTag table 业务tag类别
---@param requestCount table 申请数量
---@param requestEntity table  申请者, 例如: A向B释放一个子弹, reqeustEntity为A、 targetEntity为B
---@param targetEntity table   目标者, 辅助用于预算参与者重要度修正
---@param frequencyLimitEntity table   不为Nil时, 会以传入的Entity:uid() 作为key, 进行频率限制timestamp 记录
function WorldViewBudgetManager:ConsumeViewFrequencyPermissionWithAutoProduce(frequencyTag, requestCount, requestEntity, targetEntity, frequencyLimitEntity)
    if frequencyLimitEntity ~= nil and self:tryLimitByFrequencyTime(frequencyTag, frequencyLimitEntity) then
        return 0
    end

    local nowTime = _G._now()
    local roleImportance = self:GetFinalBudgetImportance(requestEntity, targetEntity)

    local actorViewLod = requestEntity and requestEntity.ActorViewLod or nil
    local satisfiedCount = self:TryConsumeViewBudget(self.FrequencyTotalBudgetMapping, self.FrequencyUsingBudgetMapping, frequencyTag, actorViewLod,  roleImportance, requestCount)
    
    if satisfiedCount > 0 then
    
        -- 首次使用, 记录一个timestamp, 用于后续的自动produce归还
        if self.FrequencyUsedWithAutoTimestampMapping[frequencyTag] == nil then
            self.FrequencyUsedWithAutoTimestampMapping[frequencyTag] = nowTime
        end
        
        local curUsed = self.FrequencyUsedWithAutoMapping[roleImportance][frequencyTag] or 0
        self.FrequencyUsedWithAutoMapping[roleImportance][frequencyTag] = curUsed + satisfiedCount
    end
    
    -- 查看是否还有剩余需要申请的内容
    requestCount = requestCount - satisfiedCount

    if requestCount <= 0 then
        return satisfiedCount
    end
    
    -- 没有次数了, 尝试进行预算归还; 一次性尝试flush三级别的归还
    local recoverCount = self:tryGetRecoverTokenNum(frequencyTag, satisfiedCount, nowTime)
    if recoverCount < 1 then
        return 0
    end
    
    self.FrequencyUsedWithAutoTimestampMapping[frequencyTag] = nowTime

    -- 先释放高优先级, 让重要度角色部分优先享用
    local leftRecover = self:recoverAutoFrequencyForRoleImportance(FIRST_ROLE_IMPORTANCE, frequencyTag, recoverCount)
    local lowestRecoverImportance = FIRST_ROLE_IMPORTANCE
    
    -- 归还二级
    if leftRecover > 0 then
        leftRecover = self:recoverAutoFrequencyForRoleImportance(SECOND_ROLE_IMPORTANCE, frequencyTag, recoverCount)
        lowestRecoverImportance = SECOND_ROLE_IMPORTANCE
    end

    if leftRecover > 0 then
        leftRecover = self:recoverAutoFrequencyForRoleImportance(THIRD_ROLE_IMPORTANCE, frequencyTag, recoverCount)
        lowestRecoverImportance = THIRD_ROLE_IMPORTANCE
    end
    
    -- 如果当前归还等级满足当前申请者, 再次进行申请尝试
    if roleImportance <= lowestRecoverImportance then
        local satisfiedCountAgain = self:TryConsumeViewBudget(self.FrequencyTotalBudgetMapping, self.FrequencyUsingBudgetMapping, frequencyTag, actorViewLod, roleImportance, requestCount)
        if satisfiedCountAgain > 0 then
            if self.FrequencyUsedWithAutoTimestampMapping[frequencyTag] == nil then
                self.FrequencyUsedWithAutoTimestampMapping[frequencyTag] = nowTime
            end

            local curUsedAgain = self.FrequencyUsedWithAutoMapping[roleImportance][frequencyTag] or 0
            self.FrequencyUsedWithAutoMapping[roleImportance][frequencyTag] = curUsedAgain + satisfiedCountAgain
        end
        -- 最终返回的是2次结果和
        return satisfiedCountAgain + satisfiedCount
    end
    
    return 0
end

-- 请求预算为空时,计算本次能够归还的预算数量
---@private
---@param frequencyTag string
---@param satisfiedCount number 已经请求到的数量
---@param nowTime number
---@return number 本次归还的次数
function WorldViewBudgetManager:tryGetRecoverTokenNum(frequencyTag, satisfiedCount, nowTime)
    local recoverCount = 0

    -- 当前Tag的总预算
    local totalBudget = self.FrequencyTotalBudgetMapping[frequencyTag]
    if not totalBudget then
        return recoverCount
    end

    local lastUsedTime = self.FrequencyUsedWithAutoTimestampMapping[frequencyTag]
    if not lastUsedTime then
        return recoverCount
    end

    recoverCount = (nowTime - lastUsedTime) / self.DefaultFrequencyCoolingOneItemTimeMS

    -- 如果recoverCount大于配置的系数,则直接返回
    if recoverCount >= totalBudget * WorldViewBudgetConst.VIEW_BUDGET_RELEASE_FACTOR then
        return math.ceil(recoverCount)
    end

    -- 否则进行计算
    local accumulateQueue = self.FrequencyReleaseAccumulateMapping[frequencyTag]
    if not accumulateQueue then
        accumulateQueue = {}
        self.FrequencyReleaseAccumulateMapping[frequencyTag] = accumulateQueue
    end

    local alreadyAutoUsed =
    (self.FrequencyUsedWithAutoMapping[FIRST_ROLE_IMPORTANCE][frequencyTag] or 0) +
            (self.FrequencyUsedWithAutoMapping[SECOND_ROLE_IMPORTANCE][frequencyTag] or 0) +
            (self.FrequencyUsedWithAutoMapping[THIRD_ROLE_IMPORTANCE][frequencyTag] or 0)

    table.insert(accumulateQueue, alreadyAutoUsed)

    -- 如果队列已满,则进行出队
    if #accumulateQueue > totalBudget * WorldViewBudgetConst.VIEW_BUDGET_ACCUMULATE_FACTOR then
        table.remove(accumulateQueue, 1)
    end

    local totalAccumulate = 0
    for _, accumulateCnt in ipairs(accumulateQueue) do
        totalAccumulate = totalAccumulate + accumulateCnt
    end

    -- 本次实际要释放的预算数量
    recoverCount = totalAccumulate / #accumulateQueue * WorldViewBudgetConst.VIEW_BUDGET_RELEASE_FACTOR

    -- 最大不能超过设置的上限, 且不能计入才加入的预算部分
    recoverCount = math.min(recoverCount, alreadyAutoUsed - satisfiedCount)
    return math.ceil(recoverCount)
end

function WorldViewBudgetManager:recoverAutoFrequencyForRoleImportance(roleImportance, frequencyTag, recoverCount)
    local curUsed = self.FrequencyUsedWithAutoMapping[roleImportance][frequencyTag] or 0

    if curUsed == 0 then
        return recoverCount
    end
    
    local realRecoverCount = recoverCount
    local leftRecover = recoverCount - curUsed
    if leftRecover >= 0 then
        realRecoverCount = curUsed
    end
    
    self.FrequencyUsedWithAutoMapping[roleImportance][frequencyTag] = curUsed - realRecoverCount
    local firstTotalUsed = self.FrequencyUsingBudgetMapping[roleImportance][frequencyTag]
    self.FrequencyUsingBudgetMapping[roleImportance][frequencyTag] = firstTotalUsed - realRecoverCount
    if self.FrequencyUsingBudgetMapping[roleImportance][frequencyTag] < 0 then
        self.FrequencyUsingBudgetMapping[roleImportance][frequencyTag] = 0
    end
    
    return leftRecover
end

function WorldViewBudgetManager:tryLimitByFrequencyTime(frequencyTag, frequencyLimitEntity)
    if VIEW_FREQUENCY_TRIGGER_TIME_LIMIT_FOR_EACH_ENTITY[frequencyTag] ~= nil  and frequencyLimitEntity ~= nil then
        local uid = frequencyLimitEntity:uid()
        local lastTS = self.FrequencyTimeLimitForEntityMapping[uid]
        local nowTs = _G._now()
        if  lastTS ~= nil and nowTs - lastTS < VIEW_FREQUENCY_TRIGGER_TIME_LIMIT_FOR_EACH_ENTITY[frequencyTag][self.CurrentUsingCpuLevel] then
            return true
        end

        self.FrequencyTimeLimitForEntityMapping[uid] = nowTs
    end
    
    return false
end

-- 如果是申请Token式的控制, 一次申请数量只能为1
function WorldViewBudgetManager:RequestViewFrequencyToken(frequencyTag,  requestEntity, targetEntity, frequencyLimitEntity)
    if frequencyLimitEntity ~= nil and self:tryLimitByFrequencyTime(frequencyTag, frequencyLimitEntity) then
        self:_RecordRequestFail(frequencyTag)
        return nil
    end

    local roleImportance = self:GetFinalBudgetImportance(requestEntity, targetEntity)

    local actorViewLod = requestEntity and requestEntity.ActorViewLod or nil
    local satisfiedCount = self:TryConsumeViewBudget(self.FrequencyTotalBudgetMapping, self.FrequencyUsingBudgetMapping, frequencyTag, actorViewLod, roleImportance, 1)

    if satisfiedCount == 0 then
        self:_RecordRequestFail(frequencyTag)
        return nil
    end

    self:_RecordRequestSuccess(frequencyTag)
    return bit.bor(roleImportance, VIEW_TAG_TO_FLAG_MAPPING[frequencyTag])
end

function WorldViewBudgetManager:ReleaseViewFrequencyToken(requestToken)
    local roleImportance = bit.band(requestToken, ROLE_IMPORTANCE_MASK)
    local viewTagFlag = bit.band(requestToken, VIEW_TAG_MASK)
    self:TryProduceViewBudget(self.FrequencyUsingBudgetMapping, roleImportance, VIEW_FREQUENCY_FLAG_TO_TAG_MAPPING[viewTagFlag])
end

-- ===================================表现降级限制==============================================

function WorldViewBudgetManager:RequestViewDownGradingToken(viewDownGradeTag, requestEntity, forceRequestReason)
    
end

function WorldViewBudgetManager:ReleaseViewDownGradingToken(requestToken)
    local roleImportance = bit.band(requestToken, ROLE_IMPORTANCE_MASK)
    local viewTagFlag = bit.band(requestToken, VIEW_TAG_MASK)
    self:TryProduceViewBudget(self.DownGradingUsingBudgetMapping, roleImportance, VIEW_DOWNGRADING_FLAG_TO_TAG_MAPPING[viewTagFlag])
end

function WorldViewBudgetManager:ReleaseViewDownGradingTokenBatch(tokenMapping)
    for _, token in pairs(tokenMapping) do
        local roleImportance = bit.band(token, ROLE_IMPORTANCE_MASK)
        local viewTagFlag = bit.band(token, VIEW_TAG_MASK)
        self:TryProduceViewBudget(self.DownGradingUsingBudgetMapping, roleImportance, VIEW_DOWNGRADING_FLAG_TO_TAG_MAPPING[viewTagFlag])
    end
    
    table.clear(tokenMapping)
end

function WorldViewBudgetManager:TryRequestViewDownGradingBudgetTokenBatch(requestDownGradingTags, outFlagsValue, outTokenMapping, requestEntity)
    local roleImportance = self:GetFinalBudgetImportance(requestEntity, nil, nil)
    
    for _, viewDownGradingtag in ipairs(requestDownGradingTags) do
        -- 无效tag或者外部已经有申请过了, 就不再处理了
        if viewDownGradingtag ~= nil and  outTokenMapping[viewDownGradingtag] == nil then
            local statisfiedCount = self:TryConsumeViewBudget(
                    self.DownGradingTotalBudgetMapping, self.DownGradingUsingBudgetMapping, viewDownGradingtag, requestEntity and requestEntity.ActorViewLod or nil, roleImportance, 1
            )
            if statisfiedCount > 0 then
                outTokenMapping[viewDownGradingtag] = bit.bor(roleImportance, VIEW_TAG_TO_FLAG_MAPPING[viewDownGradingtag])
                outFlagsValue = WorldViewBudgetConst.AddViewBudgetFlagByTag(viewDownGradingtag, outFlagsValue)
            end
                
        end
    end
    
    return outFlagsValue
end

-- ===============debug记录信息======================================

function WorldViewBudgetManager:_RecordRequestSuccess(tag)
    --if not DebugFlag.RecordViewBudgetInfo then
    --    return
    --end
    if not self._successfulRequestViewTag[tag] then
        self._successfulRequestViewTag[tag] = 0
    end
    self._successfulRequestViewTag[tag] = self._successfulRequestViewTag[tag] + 1
end

function WorldViewBudgetManager:ClearRequestSuccess(tag)
	self._successfulRequestViewTag[tag] = 0
end

function WorldViewBudgetManager:GetRequestSuccessCount(tag)
	if not self._successfulRequestViewTag[tag] then
		return 0
	end
	return self._successfulRequestViewTag[tag]
end

function WorldViewBudgetManager:_RecordRequestFail(tag)
    if not DebugFlag.RecordViewBudgetInfo then
        return
    end
    if not self._failedRequestViewTag[tag] then
        self._failedRequestViewTag[tag] = 0
    end
    self._failedRequestViewTag[tag] = self._failedRequestViewTag[tag] + 1
end


function WorldViewBudgetManager:StartDebugRecord(needCreateTimer)
    if DebugFlag.RecordViewBudgetInfo then
        return
    end
    DebugFlag.RecordViewBudgetInfo = true
    self._debugStartRecordTime = _G._now()
    self._failedRequestViewTag = {}
    self._successfulRequestViewTag = {}
    if needCreateTimer then
        self._statisticsTimer =  Game.TimerManager:CreateTimerAndStart(function()
            self:StopDebugRecord(false)
            self:StartDebugRecord(false)
        end, 2 * 1000, -1)
    end
end

function WorldViewBudgetManager:StopDebugRecord(needShutTimer)
    if not DebugFlag.RecordViewBudgetInfo then
        return
    end
    DebugFlag.RecordViewBudgetInfo = false

    local head = "=============WorldViewBudgetManager debug info ====================\n "
    local outStr = string.format('duration: %f\n  starttime: %f, endtime: %f\n', _G._now() - self._debugStartRecordTime, self._debugStartRecordTime,_G._now() )
    for tag, count in pairs(self._successfulRequestViewTag) do
        local total = count + (self._failedRequestViewTag[tag] or 0)
        outStr = outStr..string.format("tag: %s, success: %d, total: %s, proportion: %f\n", tag, count, total, count/total)
    end
    table.insert(self._totalRecordInfo, outStr)
    Log.Info(head..outStr)
    if needShutTimer and self._statisticsTimer then
        Game.TimerManager:StopTimerAndKill(self._statisticsTimer, false)
        self._statisticsTimer = 0
        self._totalRecordInfo = {}
    end

end


function WorldViewBudgetManager:AppendGamePlayDebugInfo()
    local debugInfos = {}
    table.insert(debugInfos, "<Title_Red>=============[WorldViewBudgetManager]===============</>")
  

    table.insert(debugInfos, string.format("<Text>CurrentUsingCpuLevel: %s </>", self.CurrentUsingCpuLevel))
    -- uid:重要度
    Game.me:DebugDumpTable(debugInfos, "DynamicRoleImportanceCache", self.DynamicRoleImportanceCache, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "FrequencyTimeLimitForEntityMapping", self.FrequencyTimeLimitForEntityMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "CreateTotalBudgetMapping", self.CreateTotalBudgetMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "CreateUsingBudgetMapping", self.CreateUsingBudgetMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "FrequencyTotalBudgetMapping", self.FrequencyTotalBudgetMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "FrequencyUsingBudgetMapping", self.FrequencyUsingBudgetMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "FrequencyUsedWithAutoMapping", self.FrequencyUsedWithAutoMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "FrequencyUsedWithTokenMapping", self.FrequencyUsedWithTokenMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "FrequencyUsedWithTokenMapping", self.FrequencyUsedWithTokenMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "DownGradingTotalBudgetMapping", self.DownGradingTotalBudgetMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "DownGradingUsingBudgetMapping", self.DownGradingUsingBudgetMapping, 0, 5)
    Game.me:DebugDumpTable(debugInfos, "RecordInfo", self._successfulRequestViewTag, 0, 5)
    -- todo 添加打印
    
    return table.concat(debugInfos, '\n')
end

return WorldViewBudgetManager