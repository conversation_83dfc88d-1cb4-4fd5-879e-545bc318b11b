
local QuestCheckRules = {

    --------------------------- Chapter Check ---------------------------
    ["ChapterRules"] = {
        --- 1. Chapter名字不能为空
        [1] = {
            bEnable = 1, -- 是否生效
            rule = [[Data.ChapterName ~= "" and Data.ChapterName ~= "NewChapter" and Data.ChapterName ~= "New Chapter" and Data.ChapterName ~= "新增章节"]], -- 配置规则
            msg = [[章节<%s>没有填写章节名称, 请补全后再做尝试]], -- 报错字段
            msgParams = {"ChapterID"}, -- 报错字段format 参数名,
            bCustomMsg = false, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
        },
        --- 2. 章节下至少需要有一个Ring节点
        [2] = {
            bEnable = 1,
            rule = [[Checker:ChapterCheck2(Data, Rule)]],
            msg = [[章节<%s>下没有创建任务节点, 请补全后再做尝试。]],
            msgParams = {"ChapterID"},
            bCustomMsg = false,
        },
        --- 3. 章节下不能有循环Ring
        [3] = {
            bEnable = 1,
            rule = [[Checker:ChapterCheck3(Data, Rule)]],
            msg = [[章节<%s>下的任务之间存在循环链接的情况, 请调整后再做尝试。]],
            msgParams = {"ChapterID"},
            bCustomMsg = true,
        },
        --- 4. 章节下不能有离散Ring
        [4] = {
            bEnable = 0,
            rule = [[Checker:ChapterCheck4(Data, Rule)]],
            msg = [[章节<%s>的任务<%s>可能为零散节点, 请与其他节点连接后再做尝试]],
            msgParams = {"ChapterID", "RingID"},
            bCustomMsg = true,
        },
        --- 5. 章节下任意的Ring, 有多个同级子节点的时候, 所有的子节点都得有跳转条件
        [5] = {
            bEnable = 1,
            rule = [[Checker:ChapterCheck5(Data, Rule)]],
            msg = [[任务<%s>后存在多任务并联的情况, 但是与任务<%s>的连线上没有设置条件判定, 请设置好判定条件后再做尝试。]],
            msgParams = {"RingID", "RingID"},
            bCustomMsg = true,
        },
        --- Ring遍历, 任意两个Ring之间存在链接关系, 后面的Ring必须存储前一个Ring的PreRingID
        [6] = {
            bEnable = 1,
            rule = [[Checker:ChapterCheck6(Data, Rule)]],
            msg = [[任务<%s>与任务<%s>有前后连接关系, 但是任务<%s>未设置前置任务, 请设置后再做尝试。]],
            msgParams = {"RingID","RingID","RingID"},
            bCustomMsg = true,
        },
        --- 1.任何Ring的任务领取方式不能为空。 2.有前后连接关系的Ring, 后者Ring的任务领取方式不能为空，且必须是任务跳转领取任务
        [7] = {
            bEnable = 1,
            rule = [[Checker:ChapterCheck7(Data, Rule)]],
            -- msg = [[---]],
            -- msgParams = {},
            bCustomMsg = true,
        }
    },

    -------------------------- Rings Check -----------------------------
    ["RingRules"] = {
        --- Ring至少要有一个Quest
        [1] = {
            bEnable = 1,
            rule = [[Data.Children and #(Data.Children:ToTable()) > 0]],
            msg = [[任务<%s>下没有创建任务步骤节点, 请补全后再做尝试。]],
            msgParams = {"RingID"},
            bCustomMsg = false,
        },
        --- Ring的ID不为空
        [2] = {
            bEnable = 1,
            rule = [[Data.RingID ~= ""]],
            msg = [[发现章节<%s>下存在没有任务ID的任务, 请补全后再做尝试。]],
            msgParams = {"RingID"},
            bCustomMsg = false,
        },
        --- Ring的名称不为空
        [3] = {
            bEnable = 1,
            rule = [[Data.RingName ~= ""]],
            msg = [[发现任务<%s>下没有设置任务名称, 请补全后再做尝试。]],
            msgParams = {"RingID"},
            bCustomMsg = false,
        },
        --- Ring的描述不为空
        [4] = {
            bEnable = 1,
            rule = [[Data.RingDescription ~= ""]],
            msg = [[发现任务<%s>下没有设置任务描述, 请补全后再做尝试。]],
            msgParams = {"RingID"},
            bCustomMsg = false,
        },
        --- Ring的Children不能存在环
        [5] = {
            bEnable = 1,
            rule = [[Checker:RingCheck1(Data, Rule)]],
            msg = [[任务<%s>下的任务步骤之间存在循环链接的情况, 请调整后再做尝试。]],
            msgParams = {"RingID"},
            bCustomMsg = true,
        },
        --- Ring的Children不能存在离散点
        [6] = {
            bEnable = 1,
            rule = [[Checker:RingCheck2(Data, Rule)]],
            msg = [[任务<%s>的任务步骤<%s>为零散节点, 请与其他节点连接后再做尝试。]],
            msgParams ={"RingID", "QuestID"},
            bCustomMsg = true,
        },
        --- Ring下存在多个同级子Quest时, 每个Quest都得有跳转条件
        [7] = {
            bEnable = 1,
            rule = [[Checker:RingCheck3(Data, Rule)]],
            msg = [[任务步骤<%s>后存在多任务步骤并联的情况, 但是与任务<%s>的连线上没有设置条件判定, 请设置好判定条件后再做尝试。]],
            msgParams = {"QuestID", "QuestID"},
            bCustomMsg = true,
        },
        --- Ring下任意的Quest为存档步骤, Ring的RelatedPlanes不能为空
        [8] = {
            bEnable = 1,
            rule = [[Checker:RingCheck4(Data, Rule)]],
            msg = [[任务<%s>下的任务步骤<%s>设置了存档步骤, 但是关联位面列表没有设置任何位面ID, 请补全后再做尝试。]],
            msgParams = {"RingID", "QuestID"},
            bCustomMsg = true,
        },
        --- Ring下任意的Quest为回退步骤, Ring的RelatedPlanes不能为空
        [9] = {
            bEnable = 1,
            rule = [[Checker:RingCheck5(Data, Rule)]],
            msg = [[任务<%s>下的任务步骤<%s>设置了回退步骤, 但是关联位面列表没有设置任何位面ID, 请补全后再做尝试。]],
            msgParams = {"RingID", "QuestID"},
            bCustomMsg = true,
        },
        --- 任务领取方式为Npc对话领取任务, 那么它的NpcID和剧情对话ID不能为空
        [10] = {
            bEnable = 1,
            rule = [[Checker:RingCheck6(Data, Rule)]],
            msg = [[任务<%s>的任务领取方式为Npc对话领取任务, 但是缺少关键配置参数(NpcID或者剧情对话ID), 请补全后再做尝试。]],
            msgParams = {"RingID"},
            bCustomMsg = true,
        },
        --- 使用道具领取任务, 必须填写道具ID
        [11] = {
            bEnable = 1,
            rule = [[Checker:RingCheck7(Data, Rule)]],
            msg = [[任务<%s>的任务领取方式为使用道具领取任务, 但是缺少关键配置参数(道具ID),请补全后再做尝试。]],
            msgParams = {"RingID"},
            bCustomMsg = true,
        },
        --- 任务步骤Quest为存档步骤的话, 该Ring的关联位面得填写数据
        [12] = {
            bEnable = 1,
            rule = [[Checker:RingCheck8(Data, Rule)]],
            msg = [[任务步骤<%s>为存档位面<%s>未出现在任务<%s>的关联位面列表内, 请补全后再做尝试。]],
            msgParams = {"QuestID","PlaneID", "RingID"},
            bCustomMsg = true,
        },
        --- StartNode不能为回退步骤
        [13] = {
            bEnable = 1,
            rule = [[Checker:RingCheck9(Data, Rule)]],
            msg = [[任务步骤<%s>是整个任务的第一个步骤，不能被设置为回退步骤，请修改后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = true,
        },
        --- 场景类型不为6的场景不能出现在关联位面列表内
        [14] = {
            bEnable = 1,
            rule = [[Checker:RingCheck10(Data, Rule)]],
            msg = [[场景[%s]的场景类型不是位面，不能被设置在任务[%s]的关联位面列表内，请移除后再做尝试。]],
            msgParams = {"PlaneID", "RingID"},
            bCustomMsg = true,
        },
    },


    ------------------------- Quest Check --------------------------------
    ["QuestRules"] = {
        --- 任务步骤的ID不能为空
        [1] = {
            bEnable = 1,
            rule = [[Data.QuestID ~= ""]],
            msg = [[发现任务步骤<%s>没有设置任务步骤ID, 请补全后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = false,
        },
        --- 任务步骤的存档类型为存档步骤时, 存档位面类型不能为0
        [2] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck1(Data, Rule)]],
            msg = [[任务步骤<%s>为存档步骤，它的存档位面ID为0, 请补全后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = true,
        },
        --- 存档步骤, 激活Action(BeginAction)不可以设置
        [3] = {
            bEnable = 0,
            rule = [[Checker:QuestCheck2(Data, Rule)]],
            msg = [[任务步骤<%s>为存档步骤, 其激活Action将不能设置任何功能, 请调整后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = true,
        },
        --- 任务步骤目标描述不能为空
        [4] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck3(Data, Rule)]],
            msg = [[任务步骤<%s>下的任务目标描述为空, 请设置后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = true,
        },
        --- 任务子目标描述不能为空
        [5] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck4(Data, Rule)]],
            msg = [[任务步骤<%s>下的任务子目标描述为空, 请设置后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = true,
        },
        --- 任务步骤的子目标数量不能超过9个
        [6] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck5(Data, Rule)]],
            msg = [[任务步骤<%s>的子目标数量已达上限, 请调整后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = true,
        },
        -- 任务目标属性判空检查(包含MainTarget和SubTarget检查)。这里是一个检查合集, 配置TaskType, 检查对应的必需属性是否为空, 如果为空, 则抛出异常
        [7] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck6(Data, Rule)]],
            msg1 = [[任务步骤<%s>的主目标中<%s>属性为空, 违反了任务目标属性检查规则, 请处理后再做尝试]],
            msg2 = [[任务步骤<%s>的子目标中<%s>属性为空, 违反了任务目标属性检查规则, 请处理后再做尝试]],
            msgParams = {"QuestID", "[PropertyName]"},
            -- Key: TaskType, Value: Check Property List
            CheckList = {
                --- NPC对话进入位面(QT_NPCTALK_ENTER_PLANE)
                [116] = {"PlaneID", "NPCID.ID", "DialogID.ID"},
                --- 与NPC进行对话
                [144] = {"NPCID.ID", "DialogID.ID"},
                --- 与NPC进行闲谈
                [145] = {"NPCID.ID", "DialogID.ID"},
                --- 指定位面入口交互并进入位面
                [142] = {"TargetPlaneID", "ID"},
                --- 前往指定Trigger位置
                [106] = {"TriggerID"},
                --- 前往指定坐标交互并进入位面
                [143] = {"TargetPlaneID", "Pos"},
                --- 前往指定坐标位置
                [107] = {"Position", "LevelMapID"},
                --- 卜杖寻路指定对象（InstanceID)
                [157] = {"InstanceID"},
                --- 完成当前步骤的必要子目标和必要次目标(定制逻辑)
                -- [155] = {""}
                --- 完成拼图解谜
                [119] = {"PuzzleID"},
                --- 完成指定任务(定制逻辑)

                --- 完成指定任务步骤(定制逻辑)

                --- 寻找指定TemplateID的NPC并开启剧情对话(定制逻辑)

                --- 指定地点附近使用任务道具
                [129] = {"ItemID", "Count", "Position", "MapID"},
                --- 指定对象附近使用任务道具
                [130] = {"ItemID", "Count", "Position", "InstanceIDList"},
                --- 提交道具(提交模式选择 默认就有选中: 只提交道具不播放Dialogue, 无需额外再检查)
                [124] = {"NPCID.ID", "ItemSubmitID.ID", }
            },
            bCustomMsg = true,
        },
        --- 一些定制判空逻辑
        -- 任务目标判空检查：完成指定任务(定制逻辑)，它的任务目标列表和完成次数都不能为空
        [8] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck7(Data, Rule)]],
            msg1 = [[任务步骤<%s>的主目标任务类型为<完成指定任务>,但是它第<%s>项的必要子目标ID为0,请修改后重试。]],
            msg2 = [[任务步骤<%s>的主目标任务类型为<完成指定任务>,但是它的任务完成次数为0,请修改后重试。]],
            msg3 = [[任务步骤<%s>的次目标列表中存在任务类型为<完成指定任务>的子目标,但是它第<%s>项的必要子目标ID为0,请修改后重试。]],
            msg4 = [[任务步骤<%s>的次目标列表中存在任务类型为<完成指定任务>的子目标,但是它的任务完成次数为0,请修改后重试。]],
            msgParams = {"QuestID","index"},
            bCustomMsg = true,
        },
        --- 任务目标判空检查：完成指定任务步骤(定制逻辑)
        [9] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck8(Data, Rule)]],
            msg1 = [[任务步骤<%s>的主目标任务类型为<完成指定任务步骤>,但是它第<%s>项的必要子目标ID为0,请修改后重试。]],
            msg2 = [[任务步骤<%s>的主目标任务类型为<完成指定任务步骤>,但是它的任务完成次数为0,请修改后重试。]],
            msg3 = [[任务步骤<%s>的次目标列表中存在任务类型为<完成指定任务步骤>的子目标,但是它第<%s>项的必要子目标ID为0,请修改后重试。]],
            msg4 = [[任务步骤<%s>的次目标列表中存在任务类型为<完成指定任务步骤>的子目标,但是它的任务完成次数为0,请修改后重试。]],
            msgParams = {"QuestID","index"},
            bCustomMsg = true,
        },
        --- 任务目标判空检查：寻找指定TemplateID的NPC并开启剧情对话(定制逻辑)
        [10] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck9(Data, Rule)]],
            msg1 = [[任务步骤<%s>的主目标任务类型为<寻找指定TemplateID的NPC并开启剧情对话>, 但是它第<%s>项的NPCID为空, 请修改后重试。]],
            msg2 = [[任务步骤<%s>的主目标任务类型为<寻找指定TemplateID的NPC并开启剧情对话>, 但是它第<%s>项的DialogID为空, 请修改后重试。]],
            msg3 = [[任务步骤<%s>的主目标任务类型为<寻找指定TemplateID的NPC并开启剧情对话>, 但是它的对话完成数量为0, 请修改后重试。]],
            msg4 = [[任务步骤<%s>的次目标列表中存在任务类型为<寻找指定TemplateID的NPC并开启剧情对话>的子目标, 但是它第<%s>项的NPCID为空, 请修改后重试。]],
            msg5 = [[任务步骤<%s>的次目标列表中存在任务类型为<寻找指定TemplateID的NPC并开启剧情对话>的子目标, 但是它第<%s>项的DialogID为空, 请修改后重试。]],
            msg6 = [[任务步骤<%s>的次目标列表中存在任务类型为<寻找指定TemplateID的NPC并开启剧情对话>的子目标, 但是它的对话完成数量为0, 请修改后重试。]],
            msg7 = [[任务步骤<%s>的主目标任务类型为<寻找指定TemplateID的NPC并开启剧情对话>, 但是它的对话设置列表为空, 请修改后重试。]],
            msg8 = [[任务步骤<%s>的次目标列表中存在任务类型为<寻找指定TemplateID的NPC并开启剧情对话>的子目标, 但是它的对话设置列表为空, 请修改后重试。]],
            msgParams = {"QuestID","index"},
            bCustomMsg = true,
        },
        --- 任务目标可存档性检查, 检查合集
        [11] = {
            bEnable = 0,
            rule = [[Checker:QuestCheck10(Data, Rule)]],
            msg = [[任务步骤<%s>下存在不该被设置为存档步骤的目标，请设置后再做尝试。]],
            msgParams = {"QuestID"},
            -- 禁设存档的任务目标Type Table
            CheckList = {
                -- 卜杖寻路到指定坐标位置
                [159] = 1,
                -- 卜杖寻路指定对象(InstanceID)
                [157] = 1,
                -- 卜杖寻路指定对象(TemplateID)
                [158] = 1,
                -- 完成工会材料回收
                [115]  = 1,
                -- 完成多按钮QTE
                [146] = 1,
                -- 完成多次连击QTE
                [148] = 1,
                -- 完成当前步骤一定数量的子目标
                [154] = 1,
                -- 完成当前步骤的必要子目标和必要次目标
                [155] = 1,
                -- 完成当前步骤的所有子目标
                [153] = 1,
                -- 完成拼图解密
                [119] = 1,
                -- 完成指定任务步骤
                [138] = 1,
                -- 完成指定任务
                [137] = 1,
                -- 完成星座解谜
                [117] = 1,
                -- 完成滑动QTE
                [147] = 1,
                -- 完成灵性收束
                [121] = 1,
                -- 开启灵视
                [151] = 1,
                -- 打开UI指定页面
                [118] = 1,
                -- 释放指定技能
                [134] = 1,
                -- 玩家在聊天频道发送指定文本
                [126] = 1,
                -- 玩家坐到任意座位
                [156] = 1,
                -- 玩家通关指定副本
                [136] = 1,
                -- 监听关闭阅读界面
                [114] = 1,
                -- 空步骤
                [112] = 1,
                -- 等待时间
                [111] = 1,
                -- 获得指定道具
                [139] = 1,
                -- 装备指定装备
                [109] = 1,
                -- 购买指定商品
                [113] = 1,
                -- 跟工会成员通关指定副本
                [115] = 1,
                -- 达到指定等级
                [105] = 1,
            },
            bCustomMsg = true,
        },
        -- 任务步骤Action属性判空检查(包含BeginAction, FailedAction, EndAction)
        [12] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck11(Data, Rule)]],
            msg1 = [[任务步骤<%s>的BeginAction中<%s>属性为空, 违反了任务Action属性检查规则, 请处理后再做尝试]],
            msg2 = [[任务步骤<%s>的FailedAction中<%s>属性为空, 违反了任务Action属性检查规则, 请处理后再做尝试]],
            msg3 = [[任务步骤<%s>的EndAction中<%s>属性为空, 违反了任务Action属性检查规则, 请处理后再做尝试]],
            msgParams = {"QuestID", "[PropertyName]"},
            -- Key: TaskType, Value: Check Property List
            CheckList = {
                -- 以NPC为锚点播放无镜对话表现
                [172] = {"InstanceID", "DialogID.ID"},
                -- 以指定坐标位置为锚点播放无镜对话表现
                [173] = {"DialogID.ID", "Position", "Rotation"},
                -- 切换当前场景时间
                [169] = {"Hour", "Minute"},
                -- 发放任务道具(定制逻辑)

                -- 发送位面事件
                [122] = {"InstanceID", "Message"},
                -- 发送全场景事件
                [121] = {"InstanceID", "Message"},
                -- 发送对象事件
                [120] = {"InstanceID", "Message", "TemplateID", "Radius"},
                -- 可交互迷雾控制
                [140] = {"bEnabled"},
                -- 指定对象添加Buff
                [132] = {"InstanceID", "TargetInstanceID", "BuffID", "BuffTime"},
                -- 指定对象结束看向表现
                [131] = {"InstanceID"},
                -- 指定对象说话(Type是一个Enum, 默认为画外音, 不用额外判断了)
                [165] = {"InstanceID", "Text", "PlayTime"},
                -- 玩家视角锁定指定坐标
                [138] = {"Position"},
                -- 玩家视角锁定指定对象
                [139] = {"TargetInstanceID"},
                -- 移除指定玩家身上BUFF
                [133] = {"InstanceID", "BuffID"},
            },
            bCustomMsg = true,
        },
        --- 完成当前步骤的必要子目标和必要次目标, 必要子目标目标ID列表不能为空,且里面的所有字段都不能为0
        [13] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck12(Data, Rule)]],
            msg1 = [[任务步骤<%s>的主目标任务类型为<完成当前步骤的必要子目标和必要次目标>,但是它第<%s>项的必要子目标ID为0,请修改后重试。]],
            msg2 = [[任务步骤<%s>的次目标列表中存在任务类型为<完成当前步骤的必要子目标和必要次目标>的子目标,但是它第<%s>项的必要子目标ID为0,请修改后重试。]],
            msgParams = {"QuestID","index"},
            bCustomMsg = true,
        },
        -- 任务步骤Action判空检查: 发放任务道具
        [14] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck13(Data, Rule)]],
            msg1 = [[任务步骤<%s>的BeginAction任务类型为<发放任务道具>,但是它的任务道具列表第<%s>项的<任务道具ID>字段为空,请修改后重试。]],
            msg2 = [[任务步骤<%s>的BeginAction任务类型为<发放任务道具>,但是它的任务道具列表第<%s>项的<数量>字段为空,请修改后重试。]],
            msg3 = [[任务步骤<%s>的FailAction任务类型为<发放任务道具>,但是它的任务道具列表第<%s>项的<任务道具ID>字段为空,请修改后重试。]],
            msg4 = [[任务步骤<%s>的FailAction任务类型为<发放任务道具>,但是它的任务道具列表第<%s>项的<数量>字段为空,请修改后重试。]],
            msg5 = [[任务步骤<%s>的EndAction任务类型为<发放任务道具>,但是它的任务道具列表第<%s>项的<任务道具ID>字段为空,请修改后重试。]],
            msg6 = [[任务步骤<%s>的EndAction任务类型为<发放任务道具>,但是它的任务道具列表第<%s>项的<数量>字段为空,请修改后重试。]],
            msgParams = {"QuestID","index"},
            bCustomMsg = true,
        },
        --- 任务步骤下的主目标或者子目标都不能为None
        [15] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck14(Data, Rule)]],
            msg = [[任务步骤<%s>下有任务目标被设为None，请设置后再做尝试。]],
            msgParams = {"QuestID"},
            bCustomMsg = true,
        },
        --- QuestID的前缀ID必须与所在任务的RingID一致
        [16] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck15(Data, Rule)]],
            msg = [[任务步骤<%s>的前缀ID<%s>与任务ID<%s>不一致，请修改后再做尝试。]],
            msgParams = {"QuestID", "QuestID", "RingID"},
            bCustomMsg = true,
        },
		--- 任务步骤失败条件设置后，没有设置任务步骤的失败模式
		--- 任务步骤失败模式设置后，没有设置任务步骤的失败条件
		[17] = {
			bEnable = 1,
			--rule = [[Data.FailConditions ~= nil and Data.FailedFallbackType == nil]],
			rule = [[Checker:QuestCheck16(Data, Rule)]],
			msg1 = [[任务步骤<%s>下设置了任务步骤失败条件，但是没有设置失败模式，请设置后再做尝试。]],
			msg2 = [[任务步骤<%s>下设置了任务步骤失败模式，但是没有设置失败条件，请设置后再做尝试。]],
			msgParams = {"QuestID"},
			bCustomMsg = true,
		},
        --- 点击保存的时候，需要把被保存Ring的关联位面列表里填写的位面ID和这个Ring内存档步骤或者回退步骤下用到的所有位面ID进行对照。如果这些步骤里用到的位面ID出现没有设置到关联位面列表内的情况，则弹出提示
        [18] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck17(Data, Rule)]],
            msg1 = [[位面[%s]出现在存档步骤[%s]的配置中，但是任务[%s]的关联位面列表内却没有设置过该位面，请将该位面ID补充到关联位面列表内再做尝试。]],
            msg2 = [[位面[%s]出现在回退步骤[%s]的配置中，但是任务[%s]的关联位面列表内却没有设置过该位面，请将该位面ID补充到关联位面列表内再做尝试。]],
            msgParams = {"PlaneID", "QuestID", "RingID"},
            bCustomMsg = true,
        },
        --- 当以下任务目标出现在任务步骤的主目标内，如果该任务步骤为普通步骤，它后面不能接回退步骤，否则报错
        --- 空步骤、等待时间、达到指定等级
        [19] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck18(Data, Rule)]],
            msg = [[任务步骤[%s]为普通步骤，它的任务主目标配置使它不支持后面衔接回退步骤，请调整后再做尝试。]],
            msgParams = {"QuestID"},
            -- 任务目标Type Table
            CheckList = {
                -- 空步骤
                [112] = 1,
                -- 等待时间
                [111] = 1,
                -- 达到指定等级
                [105] = 1,
            },
            bCustomMsg = true,
        },
        --- 三个任务目标，只能配置到任务主目标内，不能配置到任务子目标内
        [20] = {
            bEnable = 1,
            rule = [[Checker:QuestCheck19(Data, Rule)]],
            msg = [[任务步骤[%s]下的任务子目标配置不当，【完成当前步骤一定数量的子目标】、【完成当前步骤的必要子目标和次要子目标】以及【完成当前步骤的所有子目标】只能配置到主目标内，请调整后再做尝试。]],
            msgParams = {"QuestID"},
            -- 任务目标Type Table
            CheckList = {
                -- 完成当前步骤一定数量的子目标
                [154] = 1,
                -- 完成当前步骤的必要子目标和必要次目标
                [155] = 1,
                -- 完成当前步骤的所有子目标
                [153] = 1,
            },
            bCustomMsg = true,
        },
		--- 存档步骤的任务目标被设置成了【Npc对话进入位面】、【与指定位面入口交互并进入位面】或者【前往指定坐标交互并进入位面】的时候需要判断，这些任务目标里的目标位面ID是否与存档步骤的存档位面ID一致，如果一致则报错。
		[21] = {
			bEnable = 1,
			rule = [[Checker:QuestCheck20(Data, Rule)]],
			msg = [[任务步骤[%s]的任务目标是进入指定位面，并且它被设置为存档步骤，但是它的目标位面ID与存档位面ID是一样的，这会导致任务流程卡死，请根据任务的实际情况将该任务步骤的步骤类型调整为普通步骤或回退步骤之后再做尝试。]],
			msgParams = {"QuestID"},
			CheckList = {
				--- NPC对话进入位面
				[116] = 1,
				--- 指定位面入口交互并进入位面
				[142] = 1,
				--- 前往指定坐标交互并进入位面
				[143] = 1,
			},
			bCustomMsg = true,
		},
		--- 存档步骤的失败时Action、完成时Action以及任务步骤完成时离开位面设置里配置了如图1的三个Action或者是如图2的这两个配置，需要判断指定的位面或者场景ID是否与存档位面ID一致，如果一致则报错。
		[22] = {
			bEnable = 1,
			rule = [[Checker:QuestCheck21(Data, Rule)]],
			msg = [[任务步骤[%s]为存档步骤，其调用的传送接口或者离开位面设置里调用的场景ID与存档位面ID一样，这会导致任务流程卡死，请根据任务的实际情况将该任务步骤的步骤类型调整为普通步骤或回退步骤，或者调整传送接口或离开位面设置里的场景ID后再做尝试。]],
			msgParams = {"QuestID"},
			CheckList1 = {
				--- 玩家跨场景传送到指定坐标
				[104] = 1,
				--- 玩家跨场景传送到指定Trigger
				[106] = 1,
				--- 玩家传送到指定位面
				[179] = 1,
			},
			CheckList2 = {
				--- 传送到指定场景内的Trigger位置
				[1] = 1,
				--- 传送到指定场景的坐标位置
				[2] = 1,
			},
			bCustomMsg = true,
		},
    },
}


return QuestCheckRules