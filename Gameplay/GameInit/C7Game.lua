local EPropertyClass = import("EPropertyClass")
local UBaseAnimInstance = import("BaseAnimInstance")

--region Framework相关
function Game:InitFramework()
	ReleaseLog("[GameInstance-LifeTimeStage] [Game:InitFramework]")
    self:initFrameworkMisc()
    self:initFrameworkManagers()
end

---@private initFrameworkMisc 初始化下除Manager外其他框架全局脚本
function Game:initFrameworkMisc()
	ReleaseLog("[GameInstance-LifeTimeStage] [Game:initFrameworkMisc]")
    Game.GameSDK = _script.newGame()
    DefineClass = DefineClass or require("Framework.DoraSDK.DefineClass")
    unpack = table.unpack
    require("Framework.Utils.StringExtend")
    require("Framework.Utils.GlobalFunctions")
    require "Gameplay.GameInit.Switch"
    require "Framework.Init"
    require("Framework.Manager.ManagerBase")
    require("Framework.SystemBase.SystemModelBase")
    require("Framework.SystemBase.SystemReceiverBase")
    require("Framework.SystemBase.SystemSenderBase")
    require("Framework.SystemBase.SystemBase")
    require("Framework.Library.Math3D") -- 纯LUA的3D数学库 Add By XieWenzhao
    require("Framework.Library.cache")

    require("Framework.Utils.ip")
    require("Framework.Utils.utf8")
    require("Framework.Utils.Functions")
    require("Framework.Utils.PlatformUtil")
    Game.bit = require("Framework.Utils.bit")
    -- require("Shared.C7")
    Game.TableData = Game.TableData or require("Data.Excel.TableData")
    
    require("Framework.Library.time")
    ---@type TimeManager
    Game.TimeManager = require("Framework.C7Common.TimeManager").new()
    Game.TimeManager:Init()
    
    ---@type TimerManager
    Game.TimerManager = kg_require("Framework.KGFramework.KGCore.TimerManager.TimerManager").new()
    Game.HotfixUtils = require "Framework.DoraSDK.HotfixUtils"

    Game.ErrorCodeConst = kg_require("Shared.Const.ErrorCodeConst")
    Game.SharedAPI = kg_require("Shared.SharedAPI").SharedAPI
end

---@private initFrameworkManagers
function Game:initFrameworkManagers()
	Log.Info("[GameInstance-LifeTimeStage] [Game:initFrameworkManagers]")
    _script.setLuaServer()
    _script.enableEntityMessageFallback(true)

    ---@type EntityManager
	Game.EntityManager = Game.EntityManager or require "Framework.Entity.EntityManager"
    Game.TableDataManager = Game.TableDataManager or require "Framework.Utils.LuaCommon.Managers.TableDataManager".new()

    require("Framework.Ksbc.KsbcReader")
    require("Framework.Library.TableExtend")
    Game.KsbcMgr = Game.KsbcMgr or require("Framework.Ksbc.KsbcMgr")
    Game.KsbcMgr:Init()

    Game.SceneUtils = kg_require("Shared.Utils.SceneUtils").SceneUtils
end

function Game:UnInitFramework()
	Log.Info("[GameInstance-LifeTimeStage] [Game:UnInitFramework]")
    self:unInitFrameworkManagers()
    self:unInitFrameworkMisc()
end

---@private unInitFrameworkMisc
function Game:unInitFrameworkMisc()
	Log.Info("[GameInstance-LifeTimeStage] [Game:unInitFrameworkMisc]")
    Game.TimeManager:UnInit()
end

---@private unInitFrameworkManagers
function Game:unInitFrameworkManagers()
	Log.Info("[GameInstance-LifeTimeStage] [Game:unInitFrameworkManagers]")
end

--endregion Framework相关

--region Gameplay相关

function Game:InitGamePlay()
	Log.Info("[GameInstance-LifeTimeStage] [Game:InitGamePlay]")
    self:initGamePlayMisc()
    self.initGameplayManagers()
end

---@private initGamePlayMisc
function Game:initGamePlayMisc()
	Log.Info("[GameInstance-LifeTimeStage] [Game:initGamePlayMisc]")
    language = language or "zhs"
	
	require("Gameplay.3C.ClientCharacterHelper")
    require "Data.Excel.SingleExcelEnum"
    kg_require("Data.Excel.ExcelEnum")
    require "Shared.LogicEnum"
    require "Gameplay.CommonDefines.Enum"
    require "Gameplay.NetEntities.Module"
    require"Framework.KGFramework.Require"
    Game.UIConfig = Game.UIConfig or require("Data.Config.UI.UIConfig")
    Game.UIIconUtils = Game.UIIconUtils or require('Gameplay.LogicSystem.Utils.UIIconUtils')
	Game.UITipsPosAutoFollowUtils = Game.UITipsPosAutoFollowUtils or require('Gameplay.LogicSystem.Utils.UITipsPosAutoFollowUtils')
    Game.TimeUtils = Game.TimeUtils or _G.TimeUtils
    Game.ProfilerInstrumentation = require("Tools.ProfilerInstrumentation.ProfilerInstrumentation").new()
    Game.LuaProfiler = nil      --LuaProfiler默认不初始化
    Game.PlayerController = nil -- 会严格跟着Begin/End的生命周期走
	Game.PlatformScalabilitySettings = Game.PlatformScalabilitySettings or kg_require("Gameplay.CommonDefines.PlatformScalabilitySettings")
end

---@private addGameplayManager
---@param mgr table
function Game:addGameplayManager(mgr)
    table.insert(Game.GameplayManagers, mgr)
end

---@private initAllGamePlayManagers 初始化所有GamePlayManager
function Game:initAllGamePlayManagers()
	Log.Info("[GameInstance-LifeTimeStage] [Game:initAllGamePlayManagers]")
    for i = 1, #Game.GameplayManagers, 1 do
        if (Game.GameplayManagers[i] ~= nil) then
            local typeName = i
            if (Game.GameplayManagers[i].__cname) then
                typeName = Game.GameplayManagers[i].__cname
            end

            if (Game.GameplayManagers[i]["Init"] == nil) then
                Log.WarningFormat("Manager %s does not have Init function", typeName)
            else
                Log.DebugFormat("Manager %s begin to init", typeName);
                xpcall(Game.GameplayManagers[i].Init, _G.CallBackError, Game.GameplayManagers[i])
                Log.DebugFormat("Manager %s end to init", typeName);
            end
        end
    end
end

-- luacheck: push ignore
---@private initGameplayManagers
function Game:initGameplayManagers()
	Log.Info("[GameInstance-LifeTimeStage] [Game:initGameplayManagers]")
    Game.GameplayManagers = {}

    -- 标准样例
    --Game.SampleManager = Game.SampleManager or require("Framework.C7Common.SampleManager").new()
    --Game:addGameplayManager(Game.SampleManager)
    ---@type GameClientData
    Game.GameClientData = Game.GameClientData or require("Framework.Utils.GameClientData").new()
    Game:addGameplayManager(Game.GameClientData)

    -- 事件管理器
    ---@type EventSystem
    Game.EventSystem = Game.EventSystem or require("Framework.EventSystem.EventSystem").new()
    Game:addGameplayManager(Game.EventSystem)

	---新版的事件管理器
	---@type EventSystemV2
	Game.GlobalEventSystem = Game.GlobalEventSystem or kg_require("Framework.EventSystem.V2.EventSystemV2").new("GlobalEventSystem")
	Game:addGameplayManager(Game.GlobalEventSystem)

	---@type UniqEventSystemMgr
	Game.UniqEventSystemMgr = Game.UniqEventSystemMgr or kg_require("Framework.EventSystem.V2.UniqEventSystemMgr").new()

    -- 公式管理器
    Game.FormulaManager = Game.FormulaManager or require("Gameplay.LogicSystem.Formula.FormulaManager")
    Game:addGameplayManager(Game.FormulaManager)
	
    -- 一些C++管理器的创建
    if (Game.WorldContext ~= nil) and (Game.GameInstance ~= nil) then
        -- 本地化管理器
        Game.LocalizationManager = require("Gameplay.Managers.LocalizationManager").new()
        Game:addGameplayManager(Game.LocalizationManager)
		
		-- 家园建造管理器
		Game.C7ManorBuildingManager = import("C7ManorBuildingManager")(Game.WorldContext)
		Game.GameInstance:CacheManager(Game.C7ManorBuildingManager)
		Game:addGameplayManager(Game.C7ManorBuildingManager)

        Game.AssetManager = require("Framework.C7Common.AssetManager").new()
        Game:addGameplayManager(Game.AssetManager)

		Game.CppAssetManager = require("Framework.C7Common.CppAssetManager").new()
		Game:addGameplayManager(Game.CppAssetManager)

        Game.ObjectActorManager = require("Framework.C7Common.ObjectActorManager").new()
        Game:addGameplayManager(Game.ObjectActorManager)

        Game.ObjectPoolManager = require("Framework.C7Common.ObjectPoolManager").new()
        Game:addGameplayManager(Game.ObjectPoolManager)
        
		-- 材质管理器
		Game.MaterialManager = require("Gameplay.Managers.MaterialManager").new()
		Game:addGameplayManager(Game.MaterialManager)

        -- UEActor管理器
        ---@type UEActorManager
        Game.UEActorManager = require("Framework.C7Common.UEActorManager").new()
        Game:addGameplayManager(Game.UEActorManager)

        -- 战斗管理器
		---@type BSManager
        Game.BSManager = require("Gameplay.BattleSystem.S_Manager").new()
        Game:addGameplayManager(Game.BSManager)

        -- 战斗QTE管理器
        Game.BSQTEManager = Game.BSQTEManager or require("Gameplay.BattleSystem.S_QTEManager")
        Game:addGameplayManager(Game.BSQTEManager)

        -- WidgetComponentTick组件管理器
		Game.KGTickableWidgetComponentManager = require("Gameplay.Managers.KGTickableWidgetComponentManager").new()
		Game:addGameplayManager(Game.KGTickableWidgetComponentManager)
		
        -- 音频管理器
        ---@type AkAudioManager
        Game.AkAudioManager = Game.AkAudioManager or require("Gameplay.Managers.AkAudioManager")
        Game:addGameplayManager(Game.AkAudioManager)
    end

	---@type ActorAppearanceManager
    Game.ActorAppearanceManager = Game.ActorAppearanceManager or kg_require("Gameplay.Managers.ActorAppearanceManager.ActorAppearanceManager").new()
    Game:addGameplayManager(Game.ActorAppearanceManager)


	--todo delelte @huangjinbao
    Game.WorldWidgetManager = import("WorldWidgetManager")(Game.WorldContext)
    if _G.StoryEditor == nil then --对话编辑器没有GameInstance，这里加个判断
        Game.WorldWidgetManager:Init(Game.WorldContext)
    end

    --网络对接管理
    Game.NetworkManager = Game.NetworkManager or require("Framework.DoraSDK.NetworkManager")
    Game:addGameplayManager(Game.NetworkManager)

    --游戏主循环
    Game.GameLoopManagerV2 = Game.GameLoopManagerV2 or require("Gameplay.GameLoopV2.GameLoopManagerV2").new()
    Game:addGameplayManager(Game.GameLoopManagerV2)

    Game.SettingsManager = Game.SettingsManager or kg_require("Gameplay.LogicSystem.Settings.SettingsManager").new()
    Game:addGameplayManager(Game.SettingsManager)

    ---@type TimerManager
    Game.TimerManager = Game.TimerManager or kg_require("Framework.KGFramework.KGCore.TimerManager.TimerManager").new()
    Game:addGameplayManager(Game.TimerManager)
	
	---@type URoleCompositeMgr
	Game.RoleCompositeMgr =  Game.RoleCompositeMgr or kg_require("Gameplay.3C.RoleComposite.RoleCompositeManager").new()
	Game:addGameplayManager(Game.RoleCompositeMgr)

    ---@type PostProcessManager
    Game.PostProcessManager = import("PPManager")(Game.WorldContext)
    Game.GameInstance:CacheManager(Game.PostProcessManager)
    Game:addGameplayManager(Game.PostProcessManager)

    -- 数据暂未完成迁移 功能暂不启用
    --Game.NewPostProcessManager = require("Gameplay.3C.PostProcessNew.NewPostProcessManager").new()
    --Game:addGameplayManager(Game.NewPostProcessManager)
    
    Game.WorldDataManager = Game.WorldDataManager or require("Gameplay.WorldManager.WorldDataManager").new()
    Game:addGameplayManager(Game.WorldDataManager)

    Game.WorldWidgetManager2 = Game.WorldWidgetManager2 or kg_require("Gameplay.LogicSystem.WorldWidget2.WorldWidgetManager2").new()
    Game:addGameplayManager(Game.WorldWidgetManager2)
    
    Game.WorldManager = Game.WorldManager or require("Gameplay.WorldManager.WorldManager").new()
    Game:addGameplayManager(Game.WorldManager)

	-- 展示场景管理器
	---@type SceneDisplayManager
	Game.SceneDisplayManager = Game.SceneDisplayManager or require("Gameplay.LogicSystem.SceneDisplay.SceneDisplayManager").new()
	Game:addGameplayManager(Game.SceneDisplayManager)

    -- 效果管理器
    -- 1, EffectManager需要在WorldManager之后创建, 因为特效数量限制目前依赖WorldManager中的 ViewBudgetMgr
    -- 2, 与1中冲突, EffectManager要在 WorldManager之前创建, 因为销毁时, WorldManager会调用LocalEntity的销毁从而走到特效的销毁, 
    -- 此时cppMgr不能为空, 这里先在 DestroyNiagarasBySpawnerId 中加个cppMgr的判空解决该问题
    Game.EffectManager = require("Gameplay.Managers.EffectManager").new()
    Game:addGameplayManager(Game.EffectManager)

    Game.MediaManager = require("Gameplay.Managers.MediaManager").new()
    Game:addGameplayManager(Game.MediaManager)
    
    -- 战斗数据管理器
    Game.CombatDataManager = require("Gameplay.BattleSystem.CombatDataManager").new()
    Game:addGameplayManager(Game.CombatDataManager)

	---@type NewUIManager
	Game.NewUIManager = kg_require("Framework.KGFramework.KGUI.Core.UIManager").new()
	Game:addGameplayManager(Game.NewUIManager)
	
	require("Framework.UI.UIManager")
	---@type UIManager
	Game.UIManager = UIManager:GetInstance()
	Game:addGameplayManager(Game.UIManager)

    Game.LSceneActorEntityManager = Game.LSceneActorEntityManager or kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityManager").new()
    Game:addGameplayManager(Game.LSceneActorEntityManager)

    Game.SceneObjectManager = Game.SceneObjectManager or kg_require("Gameplay.NetEntities.SceneActor.SceneObjectManager").new()
    Game:addGameplayManager(Game.SceneObjectManager)

	---@type CommonInteractorManager
    Game.CommonInteractorManager = kg_require("Gameplay.CommonInteractor.CommonInteractorManager").new()
    Game:addGameplayManager(Game.CommonInteractorManager)
	
    Game.ColorManager = Game.ColorManager or require("Gameplay.UI.ColorTable.ColorManager").new()
    Game:addGameplayManager(Game.ColorManager)

    ---@type GMManager
    Game.GMManager = Game.GMManager or kg_require("Gameplay.LogicSystem.GM.GMManager").new()
    Game:addGameplayManager(Game.GMManager)

    Game.CinematicManager = Game.CinematicManager or require("Gameplay.CinematicSystem.CinematicManager")
    Game:addGameplayManager(Game.CinematicManager)

    Game.DialogueManager = Game.DialogueManager or kg_require("Gameplay.DialogueSystem.DialogueManager").new()
    Game:addGameplayManager(Game.DialogueManager)

    ---@type DialogueManagerV2
    Game.DialogueManagerV2 = Game.DialogueManagerV2 or kg_require("Gameplay.DialogueV2.DialogueManagerV2").new()
    Game:addGameplayManager(Game.DialogueManagerV2)
	
    ---@type HeadInfoManager
    Game.HeadInfoManager = Game.HeadInfoManager or kg_require("Gameplay.LogicSystem.HeadInfo.HeadInfoManager").new()
    Game:addGameplayManager(Game.HeadInfoManager)

    Game.SkillCustomeManager = Game.SkillCustomeManager or
        kg_require("Gameplay.LogicSystem.SkillCustomizer.SkillCustomeManager")
    Game:addGameplayManager(Game.SkillCustomeManager)
	
    --MassAI的管理器
    Game.MassCharacterManager = Game.MassCharacterManager or require("Gameplay.3C.MassCharacterManager").new()
    Game:addGameplayManager(Game.MassCharacterManager)

    Game.CurrencyUtils = Game.CurrencyUtils or kg_require('Gameplay.LogicSystem.Utils.CurrencyUtils')
    Game.addGameplayManager(Game.CurrencyUtils)

    Game.ScreenInputManager = Game.ScreenInputManager or
        kg_require("Gameplay.LogicSystem.ScreenInputSystem.ScreenInputManager").new()
    Game:addGameplayManager(Game.ScreenInputManager)

    Game.CameraInputManager = Game.CameraInputManager or
        require("Gameplay.LogicSystem.ScreenInputSystem.CameraInput").new()
    Game:addGameplayManager(Game.CameraInputManager)

    --Game.UIJumpSystem = Game.UIJumpSystem or kg_require("Gameplay.LogicSystem.UIJump.UIJumpSystem").new()
    --Game:addGameplayManager(Game.UIJumpSystem)

    Game.PlayerInfoManager = Game.PlayerInfoManager or
        kg_require("Gameplay.LogicSystem.HUD.HUD_PlayerInfo.PlayerInfoManager")
    Game:addGameplayManager(Game.PlayerInfoManager)

    Game.CursorManager = Game.CursorManager or require("Gameplay.LogicSystem.Cursor.CursorManager").new()
    Game:addGameplayManager(Game.CursorManager)

    --Game.DamageEffectSystem = Game.DamageEffectSystem or
    --    kg_require("Gameplay.LogicSystem.DamageEffect.DamageEffectSystem").new()
    --Game:addGameplayManager(Game.DamageEffectSystem)

    Game.HUDInteractManager = Game.HUDInteractManager or
        kg_require("Gameplay.LogicSystem.HUD.HUD_Interact.HUDInteractManager").new()
    Game:addGameplayManager(Game.HUDInteractManager)

    Game.LevelManager = Game.LevelManager or require("Gameplay.LevelSystem.LevelManager").new()
    Game:addGameplayManager(Game.LevelManager)

	---@type NPCManager
    Game.NPCManager = Game.NPCManager or kg_require("Gameplay.LogicSystem.NPC.System.NPCManager").new()
    Game:addGameplayManager(Game.NPCManager)

	Game.TestNpcManager = Game.TestNpcManager or require("Gameplay.3C.TestNpcManager").new()
	Game:addGameplayManager(Game.TestNpcManager)

    Game.TeleportManager = Game.TeleportManager or kg_require("Gameplay.3C.Teleport.TeleportManager")
    Game:addGameplayManager(Game.TeleportManager)

    ---@type UIInputProcessorManager
    Game.UIInputProcessorManager = Game.UIInputProcessorManager or kg_require("Gameplay.Managers.UIInputProcessorManager").new()
    Game:addGameplayManager(Game.UIInputProcessorManager)

    require("Framework.UI.TabClose")
    ---@type TabClose
    Game.TabClose = TabClose:GetInstance()
    Game:addGameplayManager(Game.TabClose)

	local AllInSDKManagerBase = require("Framework.AllInSDK.AllInSdkManagerBase").new()
	Game.AllInSdkManager = AllInSDKManagerBase.GetNeedSdk() and require("Framework.AllInSDK.AllInSdkManager").new() or AllInSDKManagerBase.new()
    Game:addGameplayManager(Game.AllInSdkManager)

    Game.FriendManager = Game.FriendManager or kg_require("Gameplay.LogicSystem.Friend.FriendManager").new()
    Game:addGameplayManager(Game.FriendManager)

    Game.SystemManager = Game.SystemManager or kg_require("Gameplay.SystemManager.SystemManager").new()
    Game:addGameplayManager(Game.SystemManager)

	Game.SequenceManager = Game.SequenceManager or kg_require("Gameplay.CinematicSystem.SequenceManager").new()
	Game:addGameplayManager(Game.SequenceManager)
	
    Game.ScreenShotUtil = Game.ScreenShotUtil or require("Gameplay.LogicSystem.Screenshot.ScreenShotUtil").new()
    Game:addGameplayManager(Game.ScreenShotUtil)


	Game.CharacterLightManager =  Game.CharacterLightManager or require("Gameplay.3C.CharacterLightManager").new()
	Game:addGameplayManager(Game.CharacterLightManager)

	if _G.GetContextObject():IsPIE() then
		Game.SceneRuntimeEditorManager =  Game.SceneRuntimeEditorManager or require("Gameplay.Managers.SceneRuntimeEditorManager").new()
		Game:addGameplayManager(Game.SceneRuntimeEditorManager)
	end
	
    ---@type DLCManager
    Game.DLCManager = Game.DLCManager or require("Gameplay.Managers.DLCManager").new()
    Game:addGameplayManager(Game.DLCManager)

    Game.SystemActionManager = Game.SystemActionManager or
        kg_require("Gameplay.Managers.SystemActionManager.SystemActionManager").new()
    Game:addGameplayManager(Game.SystemActionManager)

    -- step 2 : init mgrs
    Game:initAllGamePlayManagers()
end
-- luacheck: pop

function Game:UnInitGamePlay()
	Log.Info("[GameInstance-LifeTimeStage] [Game:UnInitGamePlay]")
    self:unInitGameplayManagers()
    self:unInitGameplayMisc()
end

---@private unInitGameplayMisc
function Game:unInitGameplayMisc()
	Log.Info("[GameInstance-LifeTimeStage] [Game:unInitGameplayMisc]")
    if Game.LuaProfiler ~= nil then
        Game.LuaProfiler:Stop(50)
        Game.LuaProfiler:C7RemoveFromRoot()
        Game.LuaProfiler = nil
    end

    Game.ProfilerInstrumentation:CloseCounter()

    UI.ClearCfgMeta()

    --释放时间计算
    Game.TimeUtils = nil

    --销毁Icon全局方法
    Game.UIIconUtils = nil
    -- 释放TableData
    Game.TableData = nil
    -- 释放UI程序配置表
    Game.UIConfig = nil
end

---@private unInitAllGameplayManagers
function Game:unInitAllGameplayManagers()
	Log.Info("[GameInstance-LifeTimeStage] [Game:PreUnInit AllGameplayManagers]")
	for i = #Game.GameplayManagers, 1, -1 do
		if (Game.GameplayManagers[i] ~= nil) then
			local typeName = i
			if (Game.GameplayManagers[i].__cname) then
				typeName = Game.GameplayManagers[i].__cname
			end

			if Game.GameplayManagers[i]["PreUnInit"] ~= nil then
				Log.DebugFormat("Manager %s begin to PreUnInit", typeName);
				xpcall(Game.GameplayManagers[i].PreUnInit, _G.CallBackError, Game.GameplayManagers[i])
				Log.DebugFormat("Manager %s end to PreUnInit", typeName);
			end
		end
	end
	
	Log.Info("[GameInstance-LifeTimeStage] [Game:UnInit AllGameplayManagers]")
    for i = #Game.GameplayManagers, 1, -1 do
        if (Game.GameplayManagers[i] ~= nil) then
            local typeName = i
            if (Game.GameplayManagers[i].__cname) then
                typeName = Game.GameplayManagers[i].__cname
            end
			
            if (Game.GameplayManagers[i]["UnInit"] == nil) then
                Log.WarningFormat("Manager %s does not have UnInit function", typeName)
            else
                Log.DebugFormat("Manager %s begin to UnInit", typeName);
                xpcall(Game.GameplayManagers[i].UnInit, _G.CallBackError, Game.GameplayManagers[i])
                -- 业务侧在uninit以后直接置空, 业务uninit阶段访问manager需要判空
                Game.GameplayManagers[i] = nil
                Log.DebugFormat("Manager %s end to UnInit", typeName);
            end
        end
    end

    Game.GameplayManagers = {}
end

---@private unInitGameplayManagers
function Game:unInitGameplayManagers()
	Log.Info("[GameInstance-LifeTimeStage] [Game:unInitGameplayManagers]")
	xpcall(Game.NetworkManager.Stop, _G.CallBackError, Game.NetworkManager, nil, true)

    if Game.WorldWidgetManager then
		xpcall(Game.WorldWidgetManager.Shutdown, _G.CallBackError, Game.WorldWidgetManager)
        Game.WorldWidgetManager = nil
    end

	if Game.CameraManager then
		xpcall(Game.CameraManager.UnInit, _G.CallBackError, Game.CameraManager)
		Game.CameraManager = NewDummyObject("Game.CameraManager")
	end

    -- LUA管理器的销毁
    Game:unInitAllGameplayManagers()
end

--endregion Gameplay相关

function Game:InitGameGlobals()
	Log.Info("[GameInstance-LifeTimeStage] [Game:InitGameGlobals]")
    local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
    if not UBaseAnimInstance.IsLocomotionStateTypeMapsInitialized() then
        local StateNameToStateType = slua.Map(EPropertyClass.Name, EPropertyClass.Int8)
        local StateTypeToStateName = slua.Map(EPropertyClass.Int8, EPropertyClass.Name)

        for name, type in pairs(ViewControlConst.LocoAnimStateNameToStateTypeMap) do
            StateNameToStateType:Add(name, type)
        end

        for type, name in pairs(ViewControlConst.LocoStateTypeToAnimStateNameMap) do
            StateTypeToStateName:Add(type, name)
        end

        UBaseAnimInstance.InitLocomotionStateTypeMaps(StateNameToStateType, StateTypeToStateName)
    end
end

function Game:UnInitGameGlobals()
	Log.Info("[GameInstance-LifeTimeStage] [Game:UnInitGameGlobals]")
end

--region 游戏状态变更时通知各个Manager

function Game:OnLogin()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnLogin]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnLogin then
            xpcall(Game.GameplayManagers[i].OnLogin, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnBackToLogin()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnBackToLogin]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnBackToLogin then
            xpcall(Game.GameplayManagers[i].OnBackToLogin, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnBackToSelectRole()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnBackToSelectRole]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnBackToSelectRole then
            xpcall(Game.GameplayManagers[i].OnBackToSelectRole, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnReLogin()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnReLogin]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnReLogin then
            xpcall(Game.GameplayManagers[i].OnReLogin, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnNetConnected()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnNetConnected]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnNetConnected then
            xpcall(Game.GameplayManagers[i].OnNetConnected, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnNetDisconnected()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnNetDisconnected]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnNetDisconnected then
            xpcall(Game.GameplayManagers[i].OnNetDisconnected, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnMemoryWarning()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnMemoryWarning]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnMemoryWarning then
            xpcall(Game.GameplayManagers[i].OnMemoryWarning, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnObjectCountNearlyExceed(currentObjectCount)
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnObjectCountNearlyExceed]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnObjectCountNearlyExceed then
            xpcall(Game.GameplayManagers[i].OnObjectCountNearlyExceed, _G.CallBackError, Game.GameplayManagers[i], currentObjectCount)
        end
    end
end

function Game:AfterPlayerInit()
	Log.Info("[GameInstance-LifeTimeStage] [Game:AfterPlayerInit]")
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].AfterPlayerInit then
            xpcall(Game.GameplayManagers[i].AfterPlayerInit, _G.CallBackError, Game.GameplayManagers[i])
        end
    end
end

function Game:OnWorldMapLoadComplete(levelId)
	Log.InfoFormat("[GameInstance-LifeTimeStage] [Game:OnWorldMapLoadComplete] levelId:%s", levelId)
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnWorldMapLoadComplete then
            xpcall(Game.GameplayManagers[i].OnWorldMapLoadComplete, _G.CallBackError, Game.GameplayManagers[i], levelId)
        end
    end
end

function Game:OnWorldMapDestroy(levelId)
	Log.InfoFormat("[GameInstance-LifeTimeStage] [Game:OnWorldMapDestroy] levelId:%s", levelId)
    for i = 1, #Game.GameplayManagers, 1 do
        if Game.GameplayManagers[i].OnWorldMapDestroy then
            xpcall(Game.GameplayManagers[i].OnWorldMapDestroy, _G.CallBackError, Game.GameplayManagers[i], levelId)
        end
    end
end

function Game:OnMainPlayerCreate()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnMainPlayerCreate]" )
	for i = 1, #Game.GameplayManagers, 1 do
		if Game.GameplayManagers[i].OnMainPlayerCreate then
			xpcall(Game.GameplayManagers[i].OnMainPlayerCreate, _G.CallBackError, Game.GameplayManagers[i])
		end
	end
end

function Game:OnMainPlayerDestroy()
	Log.Info("[GameInstance-LifeTimeStage] [Game:OnMainPlayerDestroy]" )
	for i = 1, #Game.GameplayManagers, 1 do
		if Game.GameplayManagers[i].OnMainPlayerDestroy then
			xpcall(Game.GameplayManagers[i].OnMainPlayerDestroy, _G.CallBackError, Game.GameplayManagers[i])
		end
	end
end



--endregion 游戏状态变更时通知各个Manager
