--初始平台处理
local GameLoopBaseStage = require("Gameplay.GameLoopV2.GameLoopBaseStage")
local PlatformStage = DefineClass("PlatformStage", GameLoopBaseStage)

function PlatformStage:ctor()
end

function PlatformStage:dtor()
end

function PlatformStage:ShowUI()
    --GM界面
    Game.GMManager.OnGameloopStartup()
    Game.ReminderManager:ShowReminderRoot()
    Game.WorldWidgetManager2:OpenWorldWidgetPanel()
	Game.WorldWidgetManager:EnableWWCellPool()
end

function PlatformStage:EnterStage()
    --Game.TimerManager:AppAddListener()
    --界面
    UIManager:GetInstance():StartUp()
    UIManager:DestroyAllPanel()
    Game.NewbieGuideSystem:StartUp()
    
    self:ShowUI()

    --目前平台阶段无处理
    self.gameLoopMgr:SwitchGameStage(self.gameLoopMgr.EGameStageType.Login)

    Game.CursorManager:BeginSetCursorStyle()
end

function PlatformStage:LeaveStage()

end

return PlatformStage
