local DoraSDK = import("DoraSDK")
local GameLoopBaseStage = require("Gameplay.GameLoopV2.GameLoopBaseStage")

---@class LoginStage:GameLoopBaseStage 登录阶段
local LoginStage = DefineClass("LoginStage", GameLoopBaseStage)


LoginStage.EPlatformDataStage = {
    NONE = 0,
    PAY_PRODUCT = 1,    --充值商品列表获取
    NOTICE = 2,         --公告列表获取
    GET_ROLE_LIST = 3,  --获取已有角色信息
    LOCATION = 4,       --归属地信息
	QUERY_GAME_ACCOUNT_INFO = 5, --获取sdk账号上报信息
	QUERY_GAME_SERVER_INFO = 6,	--获取服务器上报信息
    END = 7,
}

LoginStage.TryGetLimitOfTimes = 2  --尝试获取平台信息次数上限

function LoginStage:ctor()
    self.curPlatformDataStage = nil  --获取平台信息流程，当前处于哪个阶段
    self.getPlatformDataTryCount = {}
    self.getPlatformNecessaryDataSuccess = function()
        self:GetPlatformNecessaryDataSuccess()
    end
    self.getPlatformNecessaryDataFailed = function()
        self:GetPlatformNecessaryDataFailed()
    end
end

function LoginStage:ShowUI()
    if Game.AllInSdkManager.GetNeedSdk() and Game.AllInSdkManager.__cname == "AllInSdkManager" then
        if Game.LoginSystem:CheckSDKHadLogin() then
            self.curPlatformDataStage = LoginStage.EPlatformDataStage.NONE
            for i, v in pairs(LoginStage.EPlatformDataStage) do
                self.getPlatformDataTryCount[v] = 0
            end
            self:GetPlatformNecessaryDataFlow()
        end
		Game.NewUIManager:OpenPanel("LoginPanel")
    else
		Game.NewUIManager:OpenPanel("DebugLogin_Panel")
    end
end

function LoginStage:onEnterStage()
	Game.LoginSystem:OnServerQueueUpdate(true)		--创角/选角阶段可能处在排队状态，这里保底停止下更新sdk服务器信息

	Game.GameEntranceManager:PlayEntranceVideo()
	
	DoraSDK.SetEnableStepEpoll(false) --保底策略，每次到登录阶段时关闭DoraSDK消息阻塞功能
	DoraSDK.SetEnableEpoll(true)
	
    UIManager:GetInstance():ClearCache(true)
    self:ShowUI()
	
    -- if _G.bUIAutomationProfile then
    --     require("Tools.UIAutomationProfile.UIAutomationProfile")
    --     UIAutomationProfile:Init(false)
    -- end

    Game.ReminderManager:OnBackToLogin()
end

---GetSDKNecessaryData 在进入游戏前，获取必要的平台信息
function LoginStage:GetPlatformNecessaryDataFlow()
    if self.curPlatformDataStage == LoginStage.EPlatformDataStage.NONE then
        Game.NewUIManager:OpenPanel(UIPanelConfig.ReconnectLoading_Panel, "LOADING")
        self:GetPlatformNecessaryDataSuccess()
    elseif self.curPlatformDataStage == LoginStage.EPlatformDataStage.PAY_PRODUCT then
        Game.AllInSdkManager:QueryProductDetails(self.getPlatformNecessaryDataSuccess, self.getPlatformNecessaryDataFailed)
    elseif self.curPlatformDataStage == LoginStage.EPlatformDataStage.NOTICE then
        Game.AllInSdkManager:GetGameNotice(self.getPlatformNecessaryDataSuccess, self.getPlatformNecessaryDataFailed)
    elseif self.curPlatformDataStage == LoginStage.EPlatformDataStage.GET_ROLE_LIST then
        Game.AllInSdkManager:GetRoleList(self.getPlatformNecessaryDataSuccess, self.getPlatformNecessaryDataFailed)
    elseif self.curPlatformDataStage == LoginStage.EPlatformDataStage.LOCATION then
            Game.AllInSdkManager:GetLocation(self.getPlatformNecessaryDataSuccess, self.getPlatformNecessaryDataFailed)
    elseif self.curPlatformDataStage == LoginStage.EPlatformDataStage.QUERY_GAME_ACCOUNT_INFO then
            Game.AllInSdkManager:QueryGameAccountInfo(self.getPlatformNecessaryDataSuccess, self.getPlatformNecessaryDataFailed)
    elseif self.curPlatformDataStage == LoginStage.EPlatformDataStage.QUERY_GAME_SERVER_INFO then
            Game.AllInSdkManager:QueryGameServerInfo(self.getPlatformNecessaryDataSuccess, self.getPlatformNecessaryDataFailed)
    elseif self.curPlatformDataStage == LoginStage.EPlatformDataStage.END then
		Game.NewUIManager:ClosePanel(UIPanelConfig.ReconnectLoading_Panel)
        Game.EventSystem:Publish(_G.EEventTypes.PLATFORM_NECESSARY_DATA_GET)
    end
end

---GetPlatformNecessaryDataFailed 获取平台必要信息成功
function LoginStage:GetPlatformNecessaryDataSuccess()
    self.curPlatformDataStage = self.curPlatformDataStage + 1
    self:GetPlatformNecessaryDataFlow()
end

---GetPlatformNecessaryDataFailed 获取平台必要信息失败
function LoginStage:GetPlatformNecessaryDataFailed()
    self.getPlatformDataTryCount[self.curPlatformDataStage] = self.getPlatformDataTryCount[self.curPlatformDataStage] + 1
    if self.getPlatformDataTryCount[self.curPlatformDataStage] > LoginStage.TryGetLimitOfTimes then --尝试次数大于2次就跳过，避免卡死玩家流程
        self.curPlatformDataStage = self.curPlatformDataStage + 1
    end
    self:GetPlatformNecessaryDataFlow()
end

function LoginStage:onLeaveStage()
	Game.LoginSystem:CheckRefreshSdkServerInfo()
	Game.GameEntranceManager:StopEntranceVideo()
end

function LoginStage:dtor()
end

return LoginStage
