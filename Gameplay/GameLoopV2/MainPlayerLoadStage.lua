local GameplayStatics = import("GameplayStatics")
local GameLoopBaseStage = require("Gameplay.GameLoopV2.GameLoopBaseStage")
local MainPlayerLoadStage = DefineClass("MainPlayerLoadStage", GameLoopBaseStage)

function MainPlayerLoadStage:ctor()
    self.LoadSwitchLevelCinematicTimer = nil
end

function MainPlayerLoadStage:CheckCanDestroyAllPanel()
	return false
end

function MainPlayerLoadStage:onEnterStage()
end

function MainPlayerLoadStage:onLeaveStage()
    local me = Game.me
    local gameLoopMgr = self.gameLoopMgr
    if me then
        --通知服务器加载完毕
        Game.me:ReqClientLoaded()
        Game.GlobalEventSystem:Publish(EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED)
        Game:AfterPlayerInit()

        local SwitchLevelCinematicData = me and me.SwitchLevelCinematicData
        local cinematicID_2 = SwitchLevelCinematicData and SwitchLevelCinematicData.cinematicID_2
		local CinematicType = SwitchLevelCinematicData and SwitchLevelCinematicData.CinematicType 
		if not cinematicID_2 then
			if Game.CinematicManager and Game.CinematicManager.CutsceneManager then
				local AfterLoadingData = Game.CinematicManager.CutsceneManager:GetPlayAfterLoadingCutSceneData()
				local spaceEntity = Game.NetworkManager and Game.NetworkManager.GetLocalSpace()
				if AfterLoadingData and spaceEntity and (
					spaceEntity.eid == AfterLoadingData.SpaceEntityID or
					spaceEntity.mapID == AfterLoadingData.MapID
				 ) then
					cinematicID_2 = AfterLoadingData.cinematicID
					CinematicType = Enum.CinematicType.Cutscene
				end
			end
		end
		Log.DebugFormat("[MainPlayerLoadStage] onLeaveStage cinematicID_2: %s ", cinematicID_2)
        -- 出场 cutscene, 要保证播放错误也能回调，不阻塞流程 <EMAIL>
        if cinematicID_2 then
            gameLoopMgr:LoadingUITimeoutProtectTimer()

            me:PlaySwitchLevelCinematic(cinematicID_2, CinematicType, 
				function()
					if self.LoadSwitchLevelCinematicTimer then
						Game.TimerManager:StopTimerAndKill(self.LoadSwitchLevelCinematicTimer)
						self.LoadSwitchLevelCinematicTimer = nil
					end

					GameplayStatics.SetEnableWorldRendering(_G.GetContextObject(), true)

					Game.TimerManager:CreateTimerAndStart(function()
						gameLoopMgr:EnableLoadingUI(false)
					end, 100, 1) 
				end,
				nil, nil
			)

            me.SwitchLevelCinematicData = nil
        else
            gameLoopMgr:EnableLoadingUI(false)
        end
    else
        gameLoopMgr:EnableLoadingUI(false)
		--地图切换的时候触发的断线也会走到这里
		if not Game.IsGameExiting then
			Log.Warning("MainPlayerLoadStage onLeaveStage can not find Game.me")
		end
    end
end

function MainPlayerLoadStage:dtor()
end

return MainPlayerLoadStage