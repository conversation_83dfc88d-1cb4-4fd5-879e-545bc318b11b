--游戏主流程控制管理
-- 主流程控制多个子阶段
--

local GameLoopManagerV2 = DefineClass("GameLoopManagerV2", ManagerBase)
local DoraSDK = import("DoraSDK")
local json = require "Framework.Library.json"
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local StringConst = require("Data.Config.StringConst.StringConst")
local UCrashSightBlueprintLibrary = import("CrashSightBlueprintLibrary")

GameLoopManagerV2.EGameStageType = {
    Platform = 0, -- HotPath等登陆前的阶段
    Login = 1,
    CreateRole = 2,
    SelectRole = 3,
    CustomRole = 4,
	CreateRoleName = 5,
    MapLoad = 6,
    MainPlayerLoad = 7,
    InGame = 8,
}

FAKE_LOAD_MAP_AUTO_CANCEL_TIME = FAKE_LOAD_MAP_AUTO_CANCEL_TIME or 10 * 1000 -- 最多等待10秒, 否则直接取消单load

--游戏gameloop stage 描述
GameLoopManagerV2.GameStageDes = {}
for k, v in pairs(GameLoopManagerV2.EGameStageType) do
    GameLoopManagerV2.GameStageDes[v] = tostring(k)
end

GameLoopManagerV2.EEnterGameState = {
    Disconnect = 0,		--网络未连接
    Connecting = 1,		--连接中
    NetConnected = 2, 	--网络连接成功，但还未获取Entity
	LoginSuccess = 3, 	--登录成功
	EnterRole = 4,		--进入创角|选角阶段
	MainPlayerCreate = 5, 		--主角Entity创建
}

GameLoopManagerV2.RoleMapID = Enum.ELevelMapData.LV_CharacterCreation
GameLoopManagerV2.NewCreateRoleMapID = Enum.ELevelMapData.LV_Showroom
GameLoopManagerV2.LoginMapID = Enum.ELevelMapData.LV_Login
GameLoopManagerV2.CustomRoleMapID = Enum.ELevelMapData.LV_CustomRole
GameLoopManagerV2.CreateRoleNameMapID = Enum.ELevelMapData.LV_Tiengen_Lake_ToName

--主流程状态数据重置 定义出主流程所有需要的状态数据
function GameLoopManagerV2:dateReset()
    --当前游戏所处阶段
    self.curGameStageType = self.EGameStageType.Platform
    self.lastGameStageType = -1

	self:UpdateEnterGameState(self.EEnterGameState.Disconnect)
    
    --是否在重连状态下
    self.bReconnecting = false

    --是否在重登情况下
    self.bReLogin = false
    --重连数据
    self.ReconnectData = { UI_P = nil, TimerHandler = nil }
    --KickOut状态记录
    self.bKickOut = nil
	--是否在切换服务器
	self.bSwitchServer = false
end

function GameLoopManagerV2:onCtor()
    self.GameStageMap = nil
    self.reconnectTimeoutTimer = nil
    self.reconnectTimer = nil
    self.loginSuccessCallback = nil
    self.hasPreKGRequire = nil
    self:dateReset()
    self:resetLoadingTimeData()
	
	-- 用于自动取消fake loading, 例如: 无缝切换cutscene, 如果本地播放完了cutscene, 则会调起fake loading等着服务器进行真正切换场景
	-- 如果超时, 则自动取消loading界面
	self.cancelFakeLoadMapTimerHandle = nil
	self.fakeLoadingMapID = nil
	self.fakeOverrideLoadID = nil
end

function GameLoopManagerV2:onDtor()
    self:dateReset()
    self.GameStageMap = nil
    self.curGameStageType = nil
end

function GameLoopManagerV2:onInit()
    self.GameStageMap = {}
    self.curGameStageType = self.EGameStageType.Platform
    self:dateReset()
    
    Game.EventSystem:AddListener(_G.EEventTypes.GAME_MAINPLAYER_ENTITY_CREATE_FAILED, self, "OnMainPlayerCreateFailed")

    self:initGameStage()
end

function GameLoopManagerV2:onPreUnInit()
	--退出的时候，执行一次LeaveStage，保证退出逻辑执行 <EMAIL>
	local tarGameStage = self:getGameStage(self.curGameStageType)
	if tarGameStage then
		tarGameStage:LeaveStage()
	end
end

function GameLoopManagerV2:onUnInit()
    Game.EventSystem:RemoveObjListeners(self)
    self.GameStageMap = {}
    self.curGameStageType = self.EGameStageType.Platform
end

function GameLoopManagerV2:GameBegin()
    --初始到平台阶段
    self:SwitchGameStage(self.EGameStageType.Platform)
end

------------------------------------------------------- Stage 相关函数 Start -------------------------------------------------------
-- 游戏阶段注册
function GameLoopManagerV2:RegisterGameStage(StageType, LuaPath)
    local StageClass = require(LuaPath)
    local StageIns = StageClass.new(self, StageType)
    self.GameStageMap[StageType] = StageIns
end

function GameLoopManagerV2:initGameStage()
    local EGameStageType = self.EGameStageType
    self:RegisterGameStage(EGameStageType.Platform, "Gameplay.GameLoopV2.PlatformStage")
    self:RegisterGameStage(EGameStageType.Login, "Gameplay.GameLoopV2.LoginStage")
    self:RegisterGameStage(EGameStageType.CreateRole, "Gameplay.GameLoopV2.CreateRoleStage")
    self:RegisterGameStage(EGameStageType.SelectRole, "Gameplay.GameLoopV2.SelectRoleStage")
    self:RegisterGameStage(EGameStageType.CustomRole, "Gameplay.GameLoopV2.CustomRoleStage")
    self:RegisterGameStage(EGameStageType.CreateRoleName, "Gameplay.GameLoopV2.CreateRoleNameStage")
    self:RegisterGameStage(EGameStageType.MapLoad, "Gameplay.GameLoopV2.MapLoadStage")
    self:RegisterGameStage(EGameStageType.MainPlayerLoad, "Gameplay.GameLoopV2.MainPlayerLoadStage")
    self:RegisterGameStage(EGameStageType.InGame, "Gameplay.GameLoopV2.InGameStage")
end
------------------------------------------------------- Stage 相关函数 End -------------------------------------------------------



------------------------------------------------------- 对外接口 相关函数 Start -------------------------------------------------------
-- 获取游戏阶段实例
function GameLoopManagerV2:getGameStage(StageType)
    return self.GameStageMap[StageType]
end

-- 切换游戏阶段
function GameLoopManagerV2:SwitchGameStage(tarStageType, nextStageType, mapID, spaceID, planeID)
    local curStageType = self.curGameStageType
    local curGameStage = self:getGameStage(curStageType)
    if curStageType == tarStageType then
        if not curGameStage:CheckCanEnterSameStage(nextStageType, mapID, spaceID, planeID) then
            return
        end
    end

    Log.DebugFormat("[GameLoop-LifeTimeStage]  GameLoopManagerV2:SwitchGameStage: From %s %s To %s %s",
            GameLoopManagerV2.GameStageDes[self.curGameStageType], self.curGameStageType,
            GameLoopManagerV2.GameStageDes[tarStageType], tarStageType)

    if curGameStage then
        xpcall(curGameStage.LeaveStage, _G.CallBackError, curGameStage, curStageType, tarStageType) --容易报错直接卡流程，保护处理
    end

    self.lastGameStageType = self.curGameStageType
    self.curGameStageType = tarStageType
	self.nextStageType = nextStageType

    -- 进入下个阶段
    local tarGameStage = self:getGameStage(tarStageType)
    if tarGameStage then
        tarGameStage:EnterStage(nextStageType, mapID, spaceID, planeID)
    end
end

-- 切图之后再切换到目标阶段
function GameLoopManagerV2:LoadMapAndSwitchGameStage(tarStageType, mapID, spaceID, planeID)
    local EGameStageType = self.EGameStageType
    self:SwitchGameStage(EGameStageType.MapLoad, tarStageType, spaceID or "", mapID, planeID)
end

-- 当前所处阶段
function GameLoopManagerV2:GetCurGameLoopStage()
    return self.curGameStageType
end

-- 当前网络状态
function GameLoopManagerV2:GetCurNetworkStage()
    return self.curEnterGameState
end

-- 当前地图ID
function GameLoopManagerV2:GetCurMapDataID()
    local mapLoadGameStage = self:getGameStage(self.EGameStageType.MapLoad)
    return mapLoadGameStage.LoadMapData.mapID
end

-- 是否处于Loading状态
function GameLoopManagerV2:IsInLoadingStage()
    local curGameStageType = self.curGameStageType
    local EGameStageType = self.EGameStageType
	return curGameStageType == EGameStageType.MapLoad or curGameStageType == EGameStageType.MainPlayerLoad
end

------------------------------------------------------- 对外接口 相关函数 End -------------------------------------------------------





-------------------------------------------------------  登录登出相关代码 Start -------------------------------------------------------
--- 1.1 请求登录，将网络状态切为Connecting
function GameLoopManagerV2:LoginServer()
	Log.Info("[GameLoop-LifeTimeStage] GameLoopManagerV2:LoginServer")
    Game.AllInSdkManager:Track(Enum.EOperatorTrackType.Game_Server_Login, { result = "1", errorMsg = "" }, 0)
    
    local serverLoginData = Game.LoginSystem.model.serverLoginData
    local ret = string.split(serverLoginData.URL, ":")
    if #ret ~= 2 then
		if SHIPPING_MODE then
			Log.ErrorFormat("ReqLogin URL Error:%s", serverLoginData.URL)
		else
			Log.WarningFormat("ReqLogin URL Error:%s", serverLoginData.URL)
		end
        return
    end

    Log.InfoFormat("[GameLoop-LifeTimeStage] GameLoopManagerV2:LoginServer URL:%s  ClientVersion:%s", serverLoginData.URL, serverLoginData.ClientVersion)
    local ip,port = ret[1],tonumber(ret[2])
	self:UpdateEnterGameState(self.EEnterGameState.Connecting)

	if not self.bReconnecting then
		Game.NewUIManager:OpenPanel(UIPanelConfig.ReconnectLoading_Panel, "SERVER_CONNECTING")
	end
    Game.NetworkManager:ConnectServer(ip, port)
end

-- 2.1 登录服务器回报  RetLogin 返回登录结果，根据角色数量选择进入创角还是选角流程
function GameLoopManagerV2:OnLoginServerCompleted(errCode, uid, roles, serveVersion, _, _, _, lastLoginRoleId, processName, bEnterGame, creatingRoleInfo)
	Log.InfoFormat("[GameLoop-LifeTimeStage]  GameLoopManagerV2:OnLoginServerCompleted result%s, uid:%s, roles:%s, serveVersion:%s", errCode, uid, roles, serveVersion)

	-- receive server version now, take a record!
	UCrashSightBlueprintLibrary.SetUserValueString("ServerVersion", tostring(serveVersion))
	
	Game.NewUIManager:ClosePanel(UIPanelConfig.ReconnectLoading_Panel)
	if self.bReconnecting and (self.curGameStageType <= self.EGameStageType.SelectRole or not bEnterGame) then
        self:OnReconnected()
    end
    --登录错误
    if errCode ~= Game.NetworkManager.ErrCodes.NO_ERR then
        if self.curGameStageType ~= self.EGameStageType.Login then
            --关闭与服务器的连接
            Game.NetworkManager:Disconnect()
            --弹错误提示
            local ErrCodeName = Game.NetworkManager.GetErrCodeName(errCode)
            local ErrCodeDesc = Game.NetworkManager.GetErrCodeDesc(errCode)
            Log.DebugFormat("LoginFailed, ERROR : %s - %s", ErrCodeName, ErrCodeDesc)

            local ReminderEnum = Enum.EReminderTextData
            Game.ReminderManager:AddReminderById(ReminderEnum.LOGIN_FAILED, { { ErrCodeDesc } })
        end
		self:UpdateEnterGameState(self.EEnterGameState.Disconnect)
        return
    end
	Game.CreateRoleSystem:SetRoles(roles)
	Game.CustomRoleSystem:SetRoles(roles)
    self:checkIsReLogin()
	self:UpdateEnterGameState(self.EEnterGameState.LoginSuccess)
    Game.AllInSdkManager:Track(Enum.EOperatorTrackType.Game_Server_Login_Success, { result = "1", errorMsg = "" }, 0)
	if not bEnterGame then
		self.creatingRoleInfo = creatingRoleInfo --可能不是在当前堆栈内立即用的，需要暂存起来
		if self.curGameStageType == self.EGameStageType.CreateRole or self.curGameStageType == self.EGameStageType.CustomRole then --如果当前在创角或者捏脸，不重载场景
			return
		end
		if (#roles > 0 or not Game.NetworkManager.GetAccountEntity().bInServerQueue) then
			self:OnLoginEnterRoleStage(roles)
		else
			if Game.AllInSdkManager:CheckUsingSdk() then		--开了sdk登录才能显示排队信息
				Game.NewUIManager:ClosePanel("LoginPanel")
				--先打开排队面板
				Game.NewUIManager:OpenPanel("ServerQueuePanel", true)
			else
				Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.IN_QUEUE_HINT_USE_SDK_LOGIN)
			end
		end
	end
end

-- 玩家手动点登出
function GameLoopManagerV2:PlayerReqLogout()
	if Game.me then
		Log.InfoFormat("Player ReqLogout : %s", Game.me:uid())
		Game.me:ReqLogout()
		Game.AllInSdkManager:UpdateRoleData("3")
	end
    --切换到登录阶段
    self:backToLogin()
end

-- 玩家手动点登出的回调，RetLogout服务器回包调用
function GameLoopManagerV2:ServerOnRetLogout(errCode)
    Log.InfoFormat("[GameLoop-LifeTimeStage] ServerOnRetLogout %d", errCode)

    --登出错误
    if errCode ~= Game.NetworkManager.ErrCodes.NO_ERR then
		self.bSwitchServer = false
        local ErrCodeName = NetworkManager.GetErrCodeName(errCode)
        local ErrCodeDesc = NetworkManager.GetErrCodeDesc(errCode)
        Log.DebugFormat("Logout Err : %s - %s", ErrCodeName, ErrCodeDesc)
        return
    end
	if self.bSwitchServer then
		Game.EventSystem:Publish(_G.EEventTypes.LOGIN_SWITCH_SERVER)
		Game.NetworkManager:Disconnect()
	else
		self:backToLogin()		--切换到登录阶段
	end
end


-- 连接服务器成功  C7Client:on_connect调用过来
function GameLoopManagerV2:ServerOnConnect(errCode)
	Log.InfoFormat("[GameLoop-LifeTimeStage]  GameLoopManagerV2:ServerOnConnect errCode:%s", errCode)
    if errCode ~= Game.NetworkManager.ErrCodes.NO_ERR then
		self:UpdateEnterGameState(self.EEnterGameState.Disconnect)
        if self.curGameStageType == self.EGameStageType.Login then
            return
        end

        if self.reconnectTimer == nil then
            self.reconnectTimer = Game.TimerManager:CreateTimerAndStart(function()
                self:TryReconnect()
            end, 1000, 1)
        end
        return
    end
	self:UpdateEnterGameState(self.EEnterGameState.NetConnected)
end


-- 客户端主动断开连接，重置登录状态
function GameLoopManagerV2:ReqLogout()
	Log.Info("[GameLoop-LifeTimeStage]  GameLoopManagerV2:ReqLogout" )
    local Account = Game.NetworkManager.GetAccountEntity()
    if Account then
        Account:ReqLogout()
    else
        Game.NetworkManager:Disconnect()
    end
end


-- 用来处理不同登出方式的逻辑和表现
function GameLoopManagerV2:dealSpecialKickout(code, msg)
    local EErrCodeData = Game.ErrorCodeConst
    local dialogId, confirmCallback, popupTitle, popupContent
    if code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_SERVER 
            or code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_LOGIN 
            or code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_UPDATE 
            or code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_SERVER_BY_BAN_ROLE
            or code == EErrCodeData.KICKOUT_REASON_OTHER_CLIENT_LOGIN 
            or code == EErrCodeData.KICKOUT_REASON_OTHER_AVATAR_LOGIN then
        self.bKickOut = true
        if code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_SERVER then
            dialogId = Enum.EDialogPopUpData.KICK_OUT_SERVER_SELECT
            confirmCallback = function()
                self:backToLogin()
            end
        elseif code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_LOGIN then
            dialogId = Enum.EDialogPopUpData.KICK_OUT_LOGIN
            confirmCallback = function()
                Game.AllInSdkManager:Logout()
                self:backToLogin()
            end
        elseif code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_UPDATE then
            dialogId = Enum.EDialogPopUpData.KICK_OUT_HOTFIX
            confirmCallback = function()
                import("C7FunctionLibrary").QuitC7Game(_G.GetContextObject())
            end
        elseif code == EErrCodeData.KICKOUT_REASON_KDIP_KILL_OUT_SERVER_BY_BAN_ROLE then
            dialogId = Enum.EDialogPopUpData.ACCOUNT_BAN_KICK_OUT
            confirmCallback = function() 
                self:backToLogin()
            end
        elseif code == EErrCodeData.KICKOUT_REASON_OTHER_CLIENT_LOGIN or code == EErrCodeData.KICKOUT_REASON_OTHER_AVATAR_LOGIN then
            dialogId = Enum.EDialogPopUpData.REPLACE_FORCED_OFFLINE
            confirmCallback = function()
                self:backToLogin()
            end
        end
        if string.notNilOrEmpty(msg) and msg ~= "{}" then --尝试解析下下发的msg，如果是json串说明是GM平台下发的额外信息
			--这里不需要xpcall,允许解析失败不抛错
            local ok, result = pcall(json.decode, msg) -- luacheck: ignore
            if ok and result and result.title and result.content then
                dialogId = Enum.EDialogPopUpData.COMMON_EMPTY_ENSURE_DIALOG
                popupTitle = {result.title}
                popupContent = {result.content}
            end
        end
        
	    Game.EventSystem:Publish(_G.EEventTypes.GAMELOOP_ON_SPECIAL_KICK_OUT)
        Game.MessageBoxSystem:AddPopupByConfig(dialogId, function()
            self.bKickOut = false
            confirmCallback()
        end, nil, popupContent, popupTitle)
        return true
    end
    
    return false
end

-- OnMsgKickoutClient，服务器通知客户端被T
function GameLoopManagerV2:ServerOnKickoutClient(code, msg)
    Log.InfoFormat("ServerOnKickoutClient[ %s ][ %s ]", code, msg)
    --收到消息断开连接, 执行网络清理
    Game.NetworkManager:StopSendRemote()
    Game.NetworkManager:Stop(true)
    if self:dealSpecialKickout(code, msg) then
        return
    end
    --切换到登录阶段
    self:backToLogin()
end

-- 客户端断线的回调  C7_Client:on_disconnect
function GameLoopManagerV2:ServeOnDisconnect()
    Game.NetworkManager:StopSendRemote()
    Log.Info("[GameLoop-LifeTimeStage]  GameLoopManagerV2:ServeOnDisconnect")
    if self.bReconnecting == true then
        return
    end

    --临时兼容下
    Game.EventSystem:Publish(_G.EEventTypes.GAMELOOP_ON_LOSE_CONNECTION)
    G_InputControlDispatcher:SetEnableActiveOperateInputs(false)

    --在登录阶段
    if self.curGameStageType == self.EGameStageType.Login and not self.bSwitchServer then
        Game.NetworkManager:ClearAllEntity()
        return
    end

    --除踢号流程外走重连
    if not self.bKickOut then
        self:TryReconnect()
    end

    Game:OnNetDisconnected()
end

--- 在登录状态下直接切换服务器（走断线重连逻辑）
function GameLoopManagerV2:SwitchServer()
	self.bSwitchServer = true
	self:ReqLogout()
end
-------------------------------------------------------  登录登出相关代码 End -------------------------------------------------------





------------------------------------------------------- 选角创角相关代码 Start -------------------------------------------------------
-- 根据角色数量选择走 创角还是选角
function GameLoopManagerV2:OnLoginEnterRoleStage(roles)
	Log.Info("[GameLoop-LifeTimeStage]  GameLoopManagerV2:OnLoginEnterRoleStage")
    local hasRole = roles and #roles > 0
	Game.CreateRoleSystem:LoadClientCreateRoleData()
    if string.isEmpty(self.creatingRoleInfo.customFaceData) then
        Game.CustomRoleSystem:LoadClientCustomData()
    else
        self:setCustomRoleInfo(roles, self.creatingRoleInfo)
    end

    --当没有角色，并且有上次未完成捏脸时，直接进入捏脸
    if not hasRole and (Game.CustomRoleSystem:IsHasLastCustomRoleInfo() or Game.CreateRoleSystem:GetNowSelectProfessionID() ~= 0) then
        Game.CustomRoleSystem:ClearClientCustomData()
		self:UpdateEnterGameState(self.EEnterGameState.EnterRole)
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CUSTOMROLE_CONTINUELAST,
            function()
				if Game.CustomRoleSystem:IsHasLastCustomRoleInfo() then
					Game.CreateRoleSystem:SyncCustomSelectProfession()
					self:LoadMapAndSwitchGameStage(self.EGameStageType.CustomRole, Enum.ELevelMapData.LV_CustomRole)
				else
					self:LoadMapAndSwitchGameStage(self.EGameStageType.CreateRole, Enum.ELevelMapData.LV_Showroom)
				end
            end,
            function()
                self:enterRoleStage(roles, true)
            end)
    else
        self:enterRoleStage(roles)
    end
end

-- 3.1 选择角色
function GameLoopManagerV2:ReqStartGame(roleID)
    Log.InfoFormat("ReqStartGame roleID:%s", roleID)
    local account = NetworkManager.GetAccountEntity()
    if roleID and account then
        account:ReqStartGame(roleID)
    end
end

-- 服务器返回选角结果
function GameLoopManagerV2:ServerOnStartGame(errCode, roleID)
    Log.InfoFormat("[GameLoop-LifeTimeStage]ServerOnStartGame %s %s", errCode, roleID)
    if errCode ~= Game.NetworkManager.ErrCodes.NO_ERR then
        local ErrCodeName = NetworkManager.GetErrCodeName(errCode)
        local ErrCodeDesc = NetworkManager.GetErrCodeDesc(errCode)
        Log.ErrorFormat("on_StartGame Error : %s - %s \nTrace: %s", ErrCodeName, ErrCodeDesc)
        local ReminderEnum = Enum.EReminderTextData[ErrCodeName]
        if ReminderEnum then
            Game.ReminderManager:AddReminderById(ReminderEnum)
        end
        
        if self:dealSpecialKickout(errCode) or self.bReconnecting == true then
            return
        end

        --返回到登录
        self:backToLogin()
        return
    end

    --进入游戏成功
    Log.InfoFormat("StartGame Success! %s", roleID)

    if self.bReconnecting == true then
        self:OnReconnected()
        return
    end
    Game.AllInSdkManager:UpdateRoleData("0")
    Game.CreateRoleSystem:SetRoleID(roleID)
end

--- 4.1 服务器通知打开地图，走的是MainPlayer的OnEnterSpace
function GameLoopManagerV2:ServerOnOpenMap(spaceEntity, mapID)
    local spaceID = spaceEntity.eid
	Log.InfoFormat("[GameLoop-LifeTimeStage]  GameLoopManagerV2:ServerOnOpenMap   SpaceEntityId:%s   mapId:%s", spaceID, mapID)
    if Game.me == nil then --切地图的时候Game.me还为空，说明流程出现问题（可能协议版本不一致），退回到登录
        self:OnMainPlayerCreateFailed()
    end

    --跨场景cutscene
    local SwitchLevelCinematicData = Game.me.SwitchLevelCinematicData
    if SwitchLevelCinematicData and SwitchLevelCinematicData.cinematicID_1
        and SwitchLevelCinematicData.cinematicID_1 > 0 then
        if SwitchLevelCinematicData.MapID == mapID then
            SwitchLevelCinematicData.NextLevelID = mapID
        else
			Log.Info("[GameLoop-LifeTimeStage]  GameLoopManagerV2: Game.me.SwitchLevelCinematicData = nil")
            --其他地图直接清理
            Game.me.SwitchLevelCinematicData = nil
        end
    end

    Log.InfoFormat("ServerOnOpenMap Success! %s %s", mapID, spaceID)
    self:LoadMapAndSwitchGameStage(self.EGameStageType.InGame, mapID, spaceID, spaceEntity.planeID)
end

function GameLoopManagerV2:enterRoleStage(roles, bClearData)
	Log.Info("[GameLoop-LifeTimeStage]  GameLoopManagerV2:enterRoleStage")
	if bClearData then
		Game.CustomRoleSystem:ReqClearLastCustomRoleInfo()
		Game.CreateRoleSystem:SetNowSelectProfessionID(0)
	end
	self:UpdateEnterGameState(self.EEnterGameState.EnterRole)
	--如果当前已在创角阶段，就直接进入创角阶段
	if self.curGameStageType == self.EGameStageType.CreateRole or #roles == 0 then
		self:LoadMapAndSwitchGameStage(self.EGameStageType.CreateRole, Enum.ELevelMapData.LV_Showroom)
	else
		self:LoadMapAndSwitchGameStage(self.EGameStageType.SelectRole, Enum.ELevelMapData.LV_CharacterCreation)
	end
	-- if not string.isEmpty(self.RoleData.lastLoginRoleId) then --todo 后续应该会用上
	--     --直接请求使用当前角色, 进入游戏
	--     self:ReqStartGame(self.RoleData.lastLoginRoleId)
	--     return
	-- end
end

function GameLoopManagerV2:ActivateLoginSuccessCB()
    if self.loginSuccessCallback then
        self.loginSuccessCallback()
        self.loginSuccessCallback = nil
    end
end

---@private checkIsReLogin 检查是第一次登陆还是重登，走不同的事件
function GameLoopManagerV2:checkIsReLogin()
    if self.bReLogin then
        Game:OnReLogin()
        self.bReLogin = false
    else
        Game:OnLogin()
    end
end

-- 请求返回选择角色
function GameLoopManagerV2:PlayerReqBackToSelectRoleUI()
    Log.Info("Player ReqBackToSelectRoleUI")
	Game.me:ReqBackToSelectRoleUI()
end

-- 返回选角界面，接收返回选角消息
function GameLoopManagerV2:ServerOnBackToSelectRoleUI(errCode, roles)
	Log.Info("[GameLoop-LifeTimeStage]  GameLoopManagerV2:ServerOnBackToSelectRoleUI")
    if errCode ~= Game.NetworkManager.ErrCodes.NO_ERR then
        --关闭与服务器的连接
        Game.NetworkManager:Disconnect()

        --弹错误提示
        local ErrCodeName = Game.NetworkManager.GetErrCodeName(errCode)
        local ErrCodeDesc = Game.NetworkManager.GetErrCodeDesc(errCode)

        Log.DebugFormat("LoginFailed, ERROR : %s - %s", ErrCodeName, ErrCodeDesc)

        local ReminderEnum = Enum.EReminderTextData
        Game.ReminderManager:AddReminderById(ReminderEnum.LOGIN_FAILED, { { ErrCodeDesc } })
        self:backToLogin()
        return
    end
	Game.CreateRoleSystem:SetRoles(roles)
    --兼容下

    self:LoadMapAndSwitchGameStage(self.EGameStageType.SelectRole, Enum.ELevelMapData['LV_CharacterCreation'], "", nil)

    Game:OnBackToSelectRole()
end

-- 记录捏脸数据
function GameLoopManagerV2:setCustomRoleInfo(roles, creatingRoleInfo)
    Game.CustomRoleSystem:SetRoles(roles)
    Game.CustomRoleSystem:SetCustomRoleInfo(creatingRoleInfo)
end

---OnMainPlayerCreateFailed 主角Entity创建失败
function GameLoopManagerV2:OnMainPlayerCreateFailed()
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.MAIN_PLAYER_ENTITY_CREATE_FAILED,function()
        self:backToLogin()
    end)
    return
end

function GameLoopManagerV2:OnMainPlayerCreate()
	if self.bReconnecting == true then
		self:OnReconnected()
	end
	self:UpdateEnterGameState(self.EEnterGameState.MainPlayerCreate)
end

function GameLoopManagerV2:UpdateEnterGameState(state)
	if self.curEnterGameState == state then
		return
	end
	self.curEnterGameState = state
	Game.EventSystem:Publish(_G.EEventTypes.GAMELOOP_ENTER_GAME_STATE_UPDATE, state)
end

function GameLoopManagerV2:backToLogin()
	self:UpdateEnterGameState(self.EEnterGameState.Disconnect)
    Game.NetworkManager:Stop()

    local EGameStageType = self.EGameStageType
    self:SwitchGameStage(EGameStageType.MapLoad, EGameStageType.Login, "", self.LoginMapID, nil)

    if self.curGameStageType ~= self.EGameStageType.Login then
        Game:OnBackToLogin()
    end
end
------------------------------------------------------- 选角创角相关代码 End -------------------------------------------------------






-------------------------------------------------------  一些资源加载的回调 Start -------------------------------------------------------
-- 地图资源开始加载
function GameLoopManagerV2:Loading_OnMapBeginLoad(NewMapID, OldMapID)
    if OldMapID then
        Game:OnWorldMapDestroy(OldMapID)
    end
end

-- LevelManager调用，地图已经加载好的回调，会触发MapLoadStage相关流程
function GameLoopManagerV2:NotifyMapLoaded(mapID)
    Log.InfoFormat("[GameLoop-LifeTimeStage] NotifyMapLoaded: %s", mapID)
    local EGameStageType = self.EGameStageType
    --第一次地图加载完毕, 比较特殊，目前是GameEntranceManger调用过来的
    local StartupLevel = Game.TableData.GetLevelMapDataRow(_G.Enum.ELevelMapData.LV_Login)
    if StartupLevel.ID == mapID and self.curGameStageType == EGameStageType.Platform then
        self:GameBegin()
        Game:OnWorldMapLoadComplete(mapID)
        return
    end

    local mapLoadStage = self:getGameStage(EGameStageType.MapLoad)
    if not mapLoadStage:CheckCanSwitchNextStage() then
        return
    end

    if self.bReconnecting then
        Log.DebugFormat("NotifyMapLoaded: bReconnecting %s", mapID)
        return
    end

    if mapID ~= self.RoleMapID and mapID ~= self.LoginMapID and mapID ~= self.NewCreateRoleMapID and mapID ~= self.CustomRoleMapID and mapID ~= self.CreateRoleNameMapID and Game.me == nil then  --这里不该为空，加个log排查下
        Log.Error("NotifyMapLoaded: Game.me is nil")
        self:backToLogin()
        return
    end

    mapLoadStage:switchNextStage(mapID, false)

    local config = Game.TableData.GetLevelMapDataRow(mapID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.LEVEL_ON_LEVEL_LOADED, config)
end

-- 地图打开错误
function GameLoopManagerV2:OnMapLoadError(mapID, ErrMsg)
    Log.ErrorFormat("OnMapLoadError: %s %s", mapID, ErrMsg)

    --正在重连
    if self.bReconnecting then
        --停止重连
        self:StopReconnect()
    end
    
    --切换到登录阶段
    self:backToLogin()
end

-- 客户端MainPlayerEnterWorld，再OnLoadActorFinish之后调用
function GameLoopManagerV2:OnMainPlayerEnterWorld()
    Log.Info("[GameLoop-LifeTimeStage] GameLoopManagerV2:OnMainPlayerEnterWorld")
    
    self:SwitchGameStage(self.EGameStageType.InGame)
end
-------------------------------------------------------  一些资源加载的回调 End -------------------------------------------------------





-------------------------------------------------------  重连相关代码 Start -------------------------------------------------------
-- 尝试重连
function GameLoopManagerV2:TryReconnect()
	Log.Info("[GameLoop-LifeTimeStage] GameLoopManagerV2:TryReconnect")
    self.reconnectTimer = nil
	
    --执行网络清理
    Game.NetworkManager:Stop(true)

	--设置重连状态, 根据状态显示重连UI
	self:SetReconnectState(true)

    --重连服务器
    self:LoginServer()

    if not self.reconnectTimeoutTimer then
        self.reconnectTimeoutTimer = Game.TimerManager:CreateTimerAndStart(function()
            self:TryReconnectTimeout()
            self.reconnectTimeoutTimer = nil
        end, Game.TableData.GetConstDataRow("RECONNECT_TIME_OUT"), 1)
    end
end

-- 重连计时结束
function GameLoopManagerV2:TryReconnectTimeout()
	Log.Info("[GameLoop-LifeTimeStage] GameLoopManagerV2:TryReconnectTimeout")
    if self.bReconnecting then
        --弹窗是否再次重连
		self.disconnectDialogUniqueID = Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.DISCONNECT_CONFIRM, function()
			if self.reconnectTimer == nil then
				self:TryReconnect()
			end
		end,function()
			if Game.CreateRoleSystem:IsInCreateRole() then
				Game.CreateRoleSystem:ExitSelectRoleScene()
			end
			G_InputControlDispatcher:SetEnableActiveOperateInputs(true)
			self:StopReconnect()
			--切换到登录阶段
			self:backToLogin()
		end)
    end
end

-- 停止重连
function GameLoopManagerV2:StopReconnect()
	Log.Info("[GameLoop-LifeTimeStage] GameLoopManagerV2:StopReconnect")
    if self.reconnectTimeoutTimer then
        Game.TimerManager:StopTimerAndKill(self.reconnectTimeoutTimer)
        self.reconnectTimeoutTimer = nil
    end

    if self.reconnectTimer then
        Game.TimerManager:StopTimerAndKill(self.reconnectTimer)
        self.reconnectTimer = nil
    end

    --清理重连状态
    self:SetReconnectState(false)
    self.bReLogin = false
end

-- 重连成功
function GameLoopManagerV2:OnReconnected()
	Log.Info("[GameLoop-LifeTimeStage] GameLoopManagerV2:OnReconnected")

    self:SetReconnectState(false)
    self.bReLogin = true

	--如果重连成功，关闭重连弹窗
	if self.disconnectDialogUniqueID then
		Game.MessageBoxSystem:InterruptPopupById(self.disconnectDialogUniqueID)
		self.disconnectDialogUniqueID = nil
	end
	
    if self.reconnectTimeoutTimer then
        Game.TimerManager:StopTimerAndKill(self.reconnectTimeoutTimer)
        self.reconnectTimeoutTimer = nil
    end

    if self.reconnectTimer then
        Game.TimerManager:StopTimerAndKill(self.reconnectTimer)
        self.reconnectTimer = nil
    end

    Game.EventSystem:Publish(_G.EEventTypes.GAMELOOP_ON_RECONNECT_SUCC)
    G_InputControlDispatcher:SetEnableActiveOperateInputs(true)
end

-- 设置重连状态
function GameLoopManagerV2:SetReconnectState(bReconnecting)
    Log.InfoFormat("[GameLoop-LifeTimeStage] SetReconnectState Old: %s  New: %s", self.bReconnecting, bReconnecting)

    self.bReconnecting = bReconnecting

    if self.bReconnecting then
        --显示重连界面
		local loadingTips = self.bSwitchServer and StringConst.Get("LOGIN_SWITCH_SERVER_TIPS") or nil
		Game.NewUIManager:OpenPanel(UIPanelConfig.ReconnectLoading_Panel, loadingTips)
    else
		Game.NewUIManager:ClosePanel(UIPanelConfig.ReconnectLoading_Panel)
		self.bSwitchServer = false --不管是否在切换服务器流程中，重连成功后都重置下状态
    end
end
-------------------------------------------------------  重连相关代码 End -------------------------------------------------------





-------------------------------------------------------  Loading相关代码 Start -------------------------------------------------------
function GameLoopManagerV2:resetLoadingTimeData()
	self.curLoadingId = nil --当前加载LoadingUI id
    self.curLoadingMapID = nil -- 当前加载Loading图的mapID
    self.timeOfStartLoading = nil 	--当前LoadingUI开始显示的时间
	self.curLoadingTimeLeast = nil --当前LoadingUI 保底加载时长（时间没到的话需要等待
    self:CancelLoadingUITimeoutProtectTimer()
end

function GameLoopManagerV2:EnableLoadingUI(bShow, mapID, planeID)
	-- 进行了真正的loading, 要做一次fake的保底清理, 控制权要交给真正的Loading流程
	self:EndFakeMapLoadingUI(false)
	
    mapID = mapID or self.curLoadingMapID
    local config = Game.TableData.GetLevelMapDataRow(mapID)
    if bShow then
        if not Game.LoadingSystem:HavePreprocessLoading() then
            local me = Game.me
            local LoadingPresetOverride = me and me.SwitchLevelCinematicData and me.SwitchLevelCinematicData.LoadingID or Game.TeleportManager:GetTeleportLoadingConfig()

            DoraSDK.SetEnableStepEpoll(true) -- 开启单条执行协议直到DisableEpoll的协议，防止后面的协议多处理了
            Log.Info("MapLoadStage StartLoadingScreen")
            local loadingId, loadingTimeLeast = Game.LoadingSystem:StartLoadingScreen(config, LoadingPresetOverride, planeID)
            self.curLoadingMapID = mapID
            if loadingId ~= self.curLoadingId then
                self.curLoadingId = loadingId
                self.curLoadingTimeLeast = loadingTimeLeast
                self.timeOfStartLoading = _G._now(1)
            end
        end
    else
        if not mapID then
            return
        end
    
        local curLoadingTimeLeast = self.curLoadingTimeLeast
        local leftTime = curLoadingTimeLeast and curLoadingTimeLeast > 0 and curLoadingTimeLeast - (_G._now(1) - self.timeOfStartLoading)
        if leftTime and leftTime > 0 then
            Game.TimerManager:CreateTimerAndStart(
                function() --LoadingUI保底播放时长未到，起个Timer等待
                    self:EnableLoadingUI(false)
                end, leftTime * 1000, 1)
            return
        end

        DoraSDK.SetEnableStepEpoll(false) --Loading结束时关闭DoraSDK消息阻塞功能
	    DoraSDK.SetEnableEpoll(true)

        if not self.hasPreKGRequire then
            self.hasPreKGRequire = true
            kg_require("Data.Config.UIConfig.PreLoadKGRequire")
        end

        -- 场景切换结束，重置下当前所有有效的NiagaraSystemWidget，解决Niagara节点跨场景粒子丢失的问题
        Game.NewUIManager:ResetNiagaraSystemWidgets()

        Game.LoadingSystem:NotifyLevelLoaded(config)
        self:resetLoadingTimeData()
    end
	WidgetBlueprintLibrary.SetFocusToGameViewport() --开关loading界面时强制捕获下焦点
end

function GameLoopManagerV2:CancelLoadingUITimeoutProtectTimer()
    local loadingTimeoutProtectTimer = self.loadingTimeoutProtectTimer
    if loadingTimeoutProtectTimer then
        Game.TimerManager:StopTimerAndKill(loadingTimeoutProtectTimer)
        self.loadingTimeoutProtectTimer = nil
    end
end

function GameLoopManagerV2:LoadingUITimeoutProtectTimer()
    self:CancelLoadingUITimeoutProtectTimer()
    self.loadingTimeoutProtectTimer = Game.TimerManager:CreateTimerAndStart(function ()
        self.loadingTimeoutProtectTimer = nil
        self:EnableLoadingUI(false)
    end, 10000, 1, nil, "timeoutProtectTimer")
end


function GameLoopManagerV2:StartFakeMapLoadingUIForWaitServerControl(MapID, OverrideLoadID)
	if self.cancelFakeLoadMapTimerHandle ~= nil then
		Game.TimerManager:StopTimerAndKill(self.cancelFakeLoadMapTimerHandle)
		self.cancelFakeLoadMapTimerHandle = nil
	end
	
	-- 在真正的loading中, 不处理
	if self.curLoadingId ~= nil then
		return false
	end

	local config = Game.TableData.GetLevelMapDataRow(MapID)
	if config == nil then
		return false
	end

	Log.Info("[GameLoopManagerV2:StartFakeMapLoadingUIForWaitServerControl] %s, %s", MapID, OverrideLoadID)
	local loadingId, _ = Game.LoadingSystem:StartLoadingScreen(config, OverrideLoadID, nil, true)
	if loadingId == nil then
		return false
	end

	self.fakeLoadingMapID = MapID
	self.fakeOverrideLoadID = OverrideLoadID

	self.cancelFakeLoadMapTimerHandle = Game.TimerManager:CreateTimerAndStart(
		function ()
			self:EndFakeMapLoadingUI(true)
		end
		, FAKE_LOAD_MAP_AUTO_CANCEL_TIME, 1)

	EnableMoveInput(false)
	EnableSkillInput(false)
	
	return true
end

function GameLoopManagerV2:EndFakeMapLoadingUI(bNotifyLevelLoaded)
	bNotifyLevelLoaded = bNotifyLevelLoaded or false
	if self.cancelFakeLoadMapTimerHandle ~= nil then
		Game.TimerManager:StopTimerAndKill(self.cancelFakeLoadMapTimerHandle)
		self.cancelFakeLoadMapTimerHandle = nil
	end

	if self.fakeLoadingMapID ~= nil then
		local config = Game.TableData.GetLevelMapDataRow(self.fakeLoadingMapID)
		if bNotifyLevelLoaded and config ~= nil then
			-- 如果开始真loading则不通知
			Game.LoadingSystem:NotifyLevelLoaded(config)
		end
		EnableMoveInput(true)
		EnableSkillInput(true)
	end
	
	self.fakeLoadingMapID = nil
	self.fakeOverrideLoadID = nil
end
-------------------------------------------------------  Loading相关代码 End -------------------------------------------------------


return GameLoopManagerV2