local GameLoopBaseStage = require("Gameplay.GameLoopV2.GameLoopBaseStage")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

local MapLoadStage = DefineClass("MapLoadStage", GameLoopBaseStage)

MapLoadStage.LoadMapType = {
    Level = 0, --普通关卡
    Plane = 1, --位面切换地图
    Plane_Refresh = 2, --位面刷新(直接使用当前地图)
    Level_Refresh = 3, --关卡刷新(直接使用当前地图)
}

function MapLoadStage:ctor()
    self.nextStage = nil --下一个目标流程
    self.LoadMapData = {}  -- 当前加载的地图数据
    self.LatestMapData = {} -- 最新下发的地图数据
    self.LoadingMapType = self.LoadMapType.Level -- 切换地图的类型
    self.MapTerm = 0 -- 第几次同步打开
end

function MapLoadStage:dtor()
    
end

function MapLoadStage:CheckCanEnterSameStage(nextStage, spaceID, mapID, planeID)
    local mapTerm = self.MapTerm + 1
    self.MapTerm = mapTerm
    self:SetMapData(self.LatestMapData, spaceID, mapID, planeID, mapTerm)
    local LoadMapData = self.LoadMapData
    Log.InfoFormat("[GameLoop-LifeTimeStage] MapLoadStage Check EnterSameStage NewMapID: %s", mapID)
	
	--loadEndTime为 0表示没有加载完地图，需要等待地图加载完再继续处理 <EMAIL>
    if LoadMapData.loadEndTime == 0 then
        self:SetNextStage(nextStage)
        Log.InfoFormat("[GameLoop-LifeTimeStage] MapLoadStage EnterSameStage NewMapID: %s", mapID)
        return false
    end
	
    return true
end

function MapLoadStage:CheckCanDestroyAllPanel()
	--todo 完善地图之间切换不关闭UI的接口实现 <EMAIL>
	--if self.gameLoopMgr.lastGameStageType == Game.GameLoopManagerV2.EGameStageType.InGame 
		--and self.gameLoopMgr.nextStageType == Game.GameLoopManagerV2.EGameStageType.InGame then
		--return false
	--end
	return true
end

function MapLoadStage:onEnterStage(nextStage, spaceID, mapID, planeID)
    self:SetNextStage(nextStage)

    local mapTerm = self.MapTerm + 1
    self.MapTerm = mapTerm
    self:SetMapData(self.LatestMapData, spaceID, mapID, planeID, mapTerm)

    local LoadMapData = self.LoadMapData
 
    self:SetLoadMapType(mapID, planeID, LoadMapData.mapID)

    self:SetMapData(LoadMapData, spaceID, mapID, planeID, mapTerm)
    LoadMapData.loadBeginTime = os.time()
    LoadMapData.loadEndTime = 0

    local onlyRefreshMap = self:OnlyRefreshMap()
	if onlyRefreshMap then
        Game.LevelManager.SetCurrentLevel(mapID)
		--不重新加载地图资源，WorldManager也需要清场 @hujianglong
		xpcall(Game.WorldManager.OnWorldMapBeginLoad, _G.CallBackError, Game.WorldManager, spaceID, mapID, onlyRefreshMap)

		--WP分区判断是否加载完成的逻辑，没加载完走加载。非WP分区已经加载完成
		if Game.LevelManager.IsWorldPartitioned() then
			if Game.LevelManager:IsRadiusStreamingCompleted(Game.me:GetPosition(), true) then
				Log.InfoFormat("[GameLoop-LifeTimeStage] MapLoadStage IsRadiusStreamingCompleted Map: %s", mapID)
				self:switchNextStage(mapID, true)
			else
				Log.InfoFormat("[GameLoop-LifeTimeStage] MapLoadStage Wait Streaming Loading Map: %s", mapID)
				self.gameLoopMgr:EnableLoadingUI(true, mapID, planeID)
				self.streamingCheckTickTimer = Game.TimerManager:CreateTimerAndStart(function()
					if Game.LevelManager:IsRadiusStreamingCompleted(Game.me:GetPosition(), true) then
						self.gameLoopMgr:EnableLoadingUI(false)
						self:switchNextStage(mapID, true)
						Game.TimerManager:StopTimerAndKill(self.streamingCheckTickTimer)
						self.streamingCheckTickTimer = nil
						Log.DebugFormat("[GameLoop-LifeTimeStage] MapLoadStage IsRadiusStreamingCompleted Map: %s", mapID)
					else
						Log.DebugFormat("[GameLoop-LifeTimeStage] MapLoadStage Streaming Loading ... Map: %s", mapID)
					end
				end, 100, -1)
			end
		else
			self:switchNextStage(mapID, true)
		end
	else
		Log.InfoFormat("[GameLoop-LifeTimeStage] MapLoadStage OpenMapByID OpenMap: %s", mapID)
		xpcall(Game.WorldManager.OnBeforeWorldMapBeginLoad, _G.CallBackError, Game.WorldManager)
		self.gameLoopMgr:EnableLoadingUI(true, mapID, planeID)
		Game.UEActorManager:EnableFrameLimit(false)
		Game.LevelManager.OpenMapByID(mapID)
		--LEVEL_ON_LEVEL_LOAD_START之后再执行WorldManager的清理退场 @hujianglong
		xpcall(Game.WorldManager.OnWorldMapBeginLoad, _G.CallBackError, Game.WorldManager, spaceID, mapID, onlyRefreshMap)
	end
end

--离开Loading
function MapLoadStage:onLeaveStage(tarStageType)
    local nextStage = self.nextStage
    local gameLoopMgr = self.gameLoopMgr
    local EGameStageType = gameLoopMgr.EGameStageType
    local onlyRefreshMap = self:OnlyRefreshMap()

	--位面切换效果
    if onlyRefreshMap and gameLoopMgr.lastGameStageType == EGameStageType.InGame then
        -- game loop manager unit阶段会走到这里, 访问其他管理器需要先判空
        if Game.PostProcessManager ~= nil then
            Game.PostProcessManager:EnableCustomMaterialPP(Enum.EPostProcessLayers.PlaneRefresh, 1, 0, 1, 1,
                    ViewResourceConst.PLANE_REFRESH_EFFECT_PATH,
                    {["Radius"] = {Max = 1 ,Min = 0}} )
        end
    end

    if nextStage == EGameStageType.CreateRole or nextStage == EGameStageType.SelectRole then
		gameLoopMgr:EnableLoadingUI(false)
    elseif tarStageType ~= EGameStageType.MainPlayerLoad then
        gameLoopMgr:EnableLoadingUI(false)
    end

	self.LoadingMapType = nil
    self.nextStage = nil
end

function MapLoadStage:CheckCanSwitchNextStage()
    local LatestMapData = self.LatestMapData
    local LoadMapData = self.LoadMapData

	--如果加载中发现有新地图需要加载，则继续加载新地图，但这里服务器应该会保证不会中途加载新地图 <EMAIL>
    local LatestMapID = LatestMapData.mapID
    if LatestMapID ~= LoadMapData.mapID and LatestMapData.term ~= LoadMapData.term then
        self:setMapData(LoadMapData, LatestMapData.spaceID, LatestMapID, LatestMapData.planeID, LatestMapData.term)
        self:EnableLoadingUI(true)
        Game.LevelManager.OpenMapByID(LatestMapID)
        return false
    end

    local now = os.time()
    LoadMapData.loadEndTime = now
    LatestMapData.loadEndTime = now
    
    return true
end

function MapLoadStage:switchNextStage(mapID, onlyRefreshMap)
    Log.InfoFormat("[GameLoop-LifeTimeStage] MapLoadStage switchNextStage %s %s", mapID, onlyRefreshMap)
    
    Game:OnWorldMapLoadComplete(mapID)  -- 无论是否走场景加载都通知

    local gameLoopMgr = self.gameLoopMgr
    local nextStage = self.nextStage
    if nextStage == gameLoopMgr.EGameStageType.InGame then
        -- 加载地图并且要进入Game流程才加载主角
        gameLoopMgr:SwitchGameStage(gameLoopMgr.EGameStageType.MainPlayerLoad)
    else
        gameLoopMgr:SwitchGameStage(nextStage)
    end
end

------------------------------------ 一些逻辑方法 Start ------------------------------------
function MapLoadStage:SetNextStage(nextStage)
    self.nextStage = nextStage
end

function MapLoadStage:SetMapData(mapData, spaceID, mapID, planeID, mapTerm)
    mapData.spaceID = spaceID
    mapData.mapID = mapID
    mapData.planeID = planeID
    mapData.term = mapTerm
end

function MapLoadStage:SetLoadMapType(newMapID, newPlaneID, curMapID)
	--通过美术资源路径判断
	if Game.SceneUtils.IsSameLevelPath(curMapID, newMapID) then
		self.LoadingMapType = (newPlaneID and newPlaneID > 0) and self.LoadMapType.Plane_Refresh or self.LoadMapType.Level_Refresh
	else
		self.LoadingMapType = (newPlaneID and newPlaneID > 0) and self.LoadMapType.Plane or self.LoadMapType.Level
	end
end

-- 同一个地图资源，根据策划需求，不重新加载地图资源
function MapLoadStage:OnlyRefreshMap()
    local LoadingMapType = self.LoadingMapType
    local LoadMapType = self.LoadMapType
    return LoadingMapType == LoadMapType.Plane_Refresh or LoadingMapType == LoadMapType.Level_Refresh
end

------------------------------------ 一些逻辑方法 相关 End ------------------------------------

return MapLoadStage