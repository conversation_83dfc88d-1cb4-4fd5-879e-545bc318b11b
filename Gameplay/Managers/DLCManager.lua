---@class DLCManager
local DLCManager = DefineClass("DLCManager")
local pakUpdateSubSystem = import("SubsystemBlueprintLibrary").GetEngineSubsystem(import("PakUpdateSubsystem"))
local UC7FunctionLibrary = import("C7FunctionLibrary")
local SwitchEnum = kg_require("Shared.ModuleSwitch.SwitchEnum")
local SwitchUtils = kg_require("Shared.SwitchUtils")

function DLCManager:Init()
    self.minusRequestOverCountWrap = function()
        self:OnMinusRequestOverCount()
    end
    pakUpdateSubSystem.OnMinusRequestOverCountEvent:Add(self.minusRequestOverCountWrap)
    pakUpdateSubSystem:RuntimeUpdateStart(5)

    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_CLIENT_MODULE_SWITCH_CHANGED, "OnClientModuleSwitchChanged", self)
end

function DLCManager:UnInit()
	Game.GlobalEventSystem:RemoveListener(EEventTypesV2.ON_CLIENT_MODULE_SWITCH_CHANGED, "OnClientModuleSwitchChanged", self)
	pakUpdateSubSystem.OnMinusRequestOverCountEvent:Clear()
end

-- luacheck: push ignore
function DLCManager:OnMinusRequestOverCount()
    Log.Debug("DLCManager:OnMinusRequestOverCount Called")

    Game.MessageBoxSystem:AddPopupByInputContent(
        Game.GameEntranceManager:GetString("HOT_UPDATE_HINT"),
        string.format(Game.GameEntranceManager:GetString("RESOURCE_RUNTIME_DOWNLOAD_OVERCOUNT")),
        function()
                -- 重启更新
                DebugLog('OnMinusRequestOverCount Call Confirm')
                pakUpdateSubSystem:ResetPreUpdateIoStore(true, false)
                UC7FunctionLibrary.QuitC7Game(_G.GetContextObject())
        end,
        function()
                -- 不处理
                DebugLog('OnMinusRequestOverCount Call Cancel')
                pakUpdateSubSystem:ResetPreUpdateIoStore(false, false)
        end, 
        Game.GameEntranceManager:GetString("CONFIRM"), 
        Game.GameEntranceManager:GetString("CANCEL"), 
        0)
end
-- luacheck: pop

function DLCManager:OnClientModuleSwitchChanged(Id2Val, logicServerID)
    if Id2Val[SwitchEnum.ENABLE_ASSET_COLLECT] ~= nil then
        local bOpen = SwitchUtils.CheckSwitchValueIsTrue("EnableAssetCollect", nil, logicServerID)
        pakUpdateSubSystem:SwitchAssetStatistics(bOpen)
    end

end


return DLCManager
