local AGS = import("AkGameplayStatics")
local EPropertyClass = import("EPropertyClass")
local BlendTypeLinear = import("EAkCurveInterpolation").Linear
local EAudioListenerMode = import("EAudioListenerMode")

local AK_INVALID_PLAYING_ID = 0 -- luacheck: ignore
local NAME_NONE = "None" -- luacheck: ignore
local __PlayingIDArray__ = slua.Array(EPropertyClass.Int) -- luacheck: ignore


--region UEClassDefine


---@class FKGGroupState
---@field Group string
---@field State string


--endregion UEClassDefine


---@class AkAudioManager
AkAudioManager = DefineClass("AkAudioManager")

-- 战斗音效数量限制
AkAudioManager.BattleLimit = tonumber(Enum.EAudioConstData.BATTLE_MAX_PLAYBACK_LIMIT)

-- lru支持的最大bank内存,暂定20(mb)
AkAudioManager.Lru_Memory_Limit = 20

-- 玩家位次后缀
AkAudioManager.EPlayerSlotSuffix = {
    P1 = "_1P",
    P2 = "_2P",
    P3 = "", -- 占位用
}


--region Core


---@private
function AkAudioManager:initMembers()
    ---@type table<number, string> UI事件对应的event类型
    self.UIEventMap = {}

    ---@type boolean PC端下游戏窗口是否是当前聚焦
    self.bLostFocus = true

    ---@type table<number, string> 局内播放的音频ID列表,此列表内所有音频会在单局生命周期结束后调用Stop
    self.autoStopIDList = {}

    ---@type table<string, string> 局内group和rtpc
    self.groupStates = {}

    ---@type table<string, number>
    self.rtpcs = {}

    -- 三层Table,event->gender->playerSlot
    self.skillEventCache = {}

    ---@type table<string, number>
    self.serverPostEvents = {}

    ---@type table<string, string>
    self.serverSetGroupState = {}

    ---@type table<string, number>
    self.serverSetRtpcValue = {}

    -- 当前被锁定的敌方目标
    self.lockTargetUID = 0

    -- 带优先级的GroupState队列
    self.priorityVolumeQueue = {}

    -- 当前生效的Volume
    self.curVolumeUniqueID = ""

    -- 当前生效的Volume对应的GroupState
    self.curVolumeGroupStates = nil

    -- 阶段切换的标记
    self.bNeedUnloadStageBank = false

    -- 区域标记
    self.sceneFieldType = 0

    -- 处于战斗状态的Actor的数量
    self.inBattleActorNum = 0

    -- 记录Actor的战斗状态
    self.inBattleActorMap = {}

    ---控制Listener的开关(迭代期用,后续删除)
    self.bEnableAudioListenerSwitch = false
end

function AkAudioManager:Init()
    -- 初始化大部分成员变量
    self:initMembers()

    -- 初始化C++管理器
    self.cppMgr = import("KGAkAudioManager")(Game.WorldContext)
    Game.GameInstance:CacheManager(self.cppMgr)
    self.cppMgr:NativeInit()
    self.cppMgr:SetLruCapacity(AkAudioManager.Lru_Memory_Limit)
    -- 初始化语言
    self.cppMgr:SetAudioCulture("CN")
    self:initAudioDataToCpp()

    if EUIEventTypes then
        self.UIEventMap[EUIEventTypes.CLICK] = "OnClicked"
        self.UIEventMap[EUIEventTypes.CheckStateChanged] = "OnCheckStateChange"
    end

    self.cppMgr:SetBattleEventLimitInfo(Enum.EAudioConstData.Skill_Battle_Playback_RTPC, self.BattleLimit)

    -- 加载AutoLoad的Bank
    self:loadAutoLoadBanks()

    -- 事件监听
    Game.EventSystem:AddListener(EEventTypes.LEVEL_ON_LEVEL_LOAD_START, self, "Receive_LEVEL_ON_LEVEL_LOAD_START")
    Game.EventSystem:AddListener(EEventTypes.LEVEL_ON_LEVEL_LOADED, self, "Receive_LEVEL_ON_LEVEL_LOADED")
    Game.EventSystem:AddListener(EEventTypes.LEVEL_ON_ROLE_LOAD_COMPLETED, self, "Receive_LEVEL_ON_ROLE_LOAD_COMPLETED")
    Game.EventSystem:AddListener(EEventTypes.ROLE_ON_DESTROY, self, "Receive_ROLE_ON_DESTORY")
    Game.EventSystem:AddListener(EEventTypes.CINEMATIC_ON_START, self, "OnCinematicStart")
    Game.EventSystem:AddListener(EEventTypes.CINEMATIC_ON_END, self, "OnCinematicEnd")

    -- 初始化地图音频相关
    self:initMapAudioTagParam()

    -- todo@shijingzhe:起一个timer检查玩家高度和地面高度的差值,目前处于功能验证阶段,正式的会放到C++里
    self.altitudeSetTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:SetPlayerAltitude()
    end, 1000, -1)
end

function AkAudioManager:UnInit()
    if self.altitudeSetTimer then
        Game.TimerManager:StopTimerAndKill(self.altitudeSetTimer)
        self.altitudeSetTimer = nil
    end

    self:uninitMapAudioTagParam()
    Game.EventSystem:RemoveObjListeners(self)

    self.cppMgr:NativeUninit()
end

---@private
---@param nextLevelMapData LevelMapData
function AkAudioManager:Receive_LEVEL_ON_LEVEL_LOAD_START(nextLevelMapData)
    self.inBattleActorNum = 0
    self.inBattleActorMap = {}
    self:ResetGroupState(Enum.EAudioConstData.OTHER_BATTLE_STATE_GROUP)

    self:ResetRtpcValue(Enum.EAudioConstData.Skill_Battle_Playback_RTPC)
    self:mapAudioTag_onLevelLoadStart(nextLevelMapData)
    self:resetServerAudio()
    self:OnSceneFieldTypeChanged(0)
end

-- 回收局内产生的音频事件,设置的GroupState,Rtpc等
---@private
function AkAudioManager:Receive_LEVEL_ON_LEVEL_LOADED(levelMapData)
    self:mapAudioTag_onLevelLoadEnd(levelMapData)

    -- 回收所有被标记为需要清理的声音
    __PlayingIDArray__:Clear()
    for playingID, _ in pairs(self.autoStopIDList) do
        __PlayingIDArray__:Add(playingID)
    end

    self.cppMgr:StopAllByPlayingIDs(__PlayingIDArray__)

    -- 重置高度RTPC
    self:ResetRtpcValue(Enum.EAudioConstData.Player_Height)
end

-- 获取调用方的信息,知道是哪个模块调用的即可
---@private
---@return string
function AkAudioManager:getOwnerName(owner)
    if not owner then
        return ""
    elseif (owner.GetName ~= nil) and (type(owner.GetName) == "function") then
        return owner:GetName()
    else
        return owner.__cname or ""
    end
end

---@public
---@param eventName string
---@return number 返回小于0为没有对应数据
function AkAudioManager:GetEventDuration(eventName)
    local AED = Game.TableData.GetAkAudioEventDataRow(eventName)
    return AED and AED.Duration or -1
end

---@public
---@param eventName string
---@return string
function AkAudioManager:GetEventRequiredBank(eventName)
    local AED = Game.TableData.GetAkAudioEventDataRow(eventName)
    return AED and AED.RequiredBank or ""
end

---@private
---@param eventName string
---@return boolean
function AkAudioManager:hasBank(eventName)
    return (Game.TableData.GetAkAudioEventDataRow(eventName) ~= nil)
end


--endregion Core


--region BattleSystem


-- 获取技能实际要播放的eventName并缓存
---@public
---@param srcEventName string
---@param playerSlotSuffix string
function AkAudioManager:GetRealSkillEventName(srcEventName, playerSlotSuffix)
    local realEventName = srcEventName

    local eventAsKey = self.skillEventCache[srcEventName]
    if not eventAsKey then
        self.skillEventCache[srcEventName] = {}
        eventAsKey = self.skillEventCache[srcEventName]
    end

    local slotAsKey = eventAsKey[playerSlotSuffix]
    if not slotAsKey then
        realEventName = srcEventName .. playerSlotSuffix
        eventAsKey[playerSlotSuffix] = realEventName
    else
        realEventName = eventAsKey[playerSlotSuffix]
    end

    return realEventName
end

-- 锁定目标更新时,记录ID并触发音效
---@public
---@param newTargetUID integer
function AkAudioManager:OnLockTargetChanged(newTargetUID)
    if newTargetUID and newTargetUID > 0 then
        self.lockTargetUID = newTargetUID
        self.cppMgr:SetLockTargetUID(newTargetUID)
        local lockTargetEnt = Game.EntityManager:GetEntityByIntID(self.lockTargetUID)
        if lockTargetEnt then
            self:PostEvent3D(Enum.EUIAudioEvent.Play_UI_Battle_Lock, lockTargetEnt:GetPosition())
        end
    else
        self.lockTargetUID = 0
    end
end

-- 判断一个localEntity是否是音频系统关注的主要目标
---@private
---@param instigator
function AkAudioManager:isMajorUnit(instigator)
    if not instigator then
        return false
    end

    local bIsMainPlayer = instigator == Game.me
    local bIsBoss = instigator.BossType == Enum.EBossType.BOSS
    local bIsLockTarget = instigator:uid() == self.lockTargetUID

    return (bIsMainPlayer == true) or (bIsBoss == true) or (bIsLockTarget == true)
end

-- 战斗侧播放音频时,根据当前声音总共的数量和播放者类型决定是否可以播放
---@public
---@param type number
---@param instigator table
---@param target table
---@return boolean
function AkAudioManager:CanBattleSystemPostEvent(type, instigator, target)
    -- 获取始作俑者
    instigator = BSFunc.GetActorInstigator(instigator)
    target = BSFunc.GetActorInstigator(target)

    if not self:isMajorUnit(instigator) then
        -- 非主角,boos,锁定目标尝试播放,根据当前数量进行限制
        local battleEventNum = self.cppMgr:GetBattleEventNum()
        if battleEventNum >= self.BattleLimit then
            return false
        end

        -- 尝试获取预算
        if type == Enum.BattleAudioType.ATTACK then
            return Game.WorldManager.ViewBudgetMgr:TryConsumeViewFrequency_ATTACK_SOUND(instigator, target)
        elseif type == Enum.BattleAudioType.BEATEN then
            return Game.WorldManager.ViewBudgetMgr:TryConsumeViewFrequency_HIT_FLASH_AND_SOUND(instigator, target)
        else
            Log.WarningFormat("[CanBattleSystemPostEvent] invalid battle audio type %s", type)
            return false
        end
    else
        -- 其他情况都允许播放
        return true
    end
end

-- 战斗侧播放出来的声音,记录结束时间
---@public
---@param eventName string
---@param playingID number
function AkAudioManager:RecordBattleAudioEndTime(eventName, playingID)
    if playingID == AK_INVALID_PLAYING_ID then
        return
    end

    local eventDuration = self:GetEventDuration(eventName)
    if eventDuration <= 0 then
        return
    end

    self.cppMgr:AddBattleEventRecord(playingID, eventDuration)
end

-- 战斗侧主动减少计数
---@public
---@param playingID number
function AkAudioManager:RemoveBattleAudioEndTime(playingID)
    self.cppMgr:DelBattleEventRecord(playingID)
end

-- 出生时必然会调用set_InBattle,所以不监听出生事件
---@public
---@param entity ActorBase
---@param bInBattle boolean
function AkAudioManager:OnActorBattleStateChange(entity, bInBattle)
    if (entity == nil) or (entity == Game.me) then
        return
    end

    local entityUID = entity:uid()
    if not self.inBattleActorMap[entityUID] then
        self.inBattleActorMap[entityUID] = 1
        if bInBattle then
            self.inBattleActorNum = self.inBattleActorNum + 1
        end
    else
        if bInBattle then
            self.inBattleActorNum = self.inBattleActorNum + 1
        else
            self.inBattleActorNum = self.inBattleActorNum - 1
        end
    end

    self:processOtherBattleState()
end

---@private
function AkAudioManager:Receive_ROLE_ON_DESTORY(_, entityUID)
    local entity = Game.EntityManager:GetEntityByIntID(entityUID)
    if (entity == nil) or (entity == Game.me) then
        return
    end

    -- 销毁时,如果在战斗状态中,则认为需要-1,否则认为已经减过了
    if (self.inBattleActorMap[entityUID] ~= nil) and (entity.InBattle == true) then
        self.inBattleActorNum = self.inBattleActorNum - 1
    end

    self.inBattleActorMap[entityUID] = nil
    self:processOtherBattleState()
end

---@private
function AkAudioManager:processOtherBattleState()
    if self.inBattleActorNum < 0 then
        Log.WarningFormat("[processOtherBattleState] unexpected inBattleActorNum %s", self.inBattleActorNum)
    end

    if self.inBattleActorNum > 0 then
        self:SetGroupState(Enum.EAudioConstData.OTHER_BATTLE_STATE_GROUP, Enum.EAudioConstData.IN_OTHER_BATTLE_STATE)
    else
        self:ResetGroupState(Enum.EAudioConstData.OTHER_BATTLE_STATE_GROUP)
    end
end


--endregion BattleSystem


--region PostEvent


local __ZeroVector__ = FVector() -- luacheck: ignore

-- 播放一个2D音频
---@public
---@param eventName string
---@param bNoNeedAutoStop boolean 默认不传该参数,则该音频会在切换地图时自动Stop,如果传了true,则需要手动Stop
---@return number, string
function AkAudioManager:PostEvent2D(eventName, bNoNeedAutoStop)
    return self:PostEvent3D(eventName, __ZeroVector__, bNoNeedAutoStop)
end

-- 在指定位置播放一个3D音频
---@public
---@param eventName string
---@param location PVector3
---@param bNoNeedAutoStop boolean 默认不传该参数,则该音频会在切换地图时自动Stop,如果传了true,则需要手动Stop
---@return number
function AkAudioManager:PostEvent3D(eventName, location, bNoNeedAutoStop)
    if not self:hasBank(eventName) then
        Log.WarningFormat("[PostEvent3D] %s has no bank", eventName)
        return AK_INVALID_PLAYING_ID
    end

    local playingID = self.cppMgr:InnerPostEvent3D(eventName, location)
    if playingID == AK_INVALID_PLAYING_ID then
        Log.WarningFormat("[PostEvent3D] eventName=%s post failed", eventName)
        return AK_INVALID_PLAYING_ID
    end

    if not bNoNeedAutoStop then
        self.autoStopIDList[playingID] = 1
    end

    return playingID
end

-- 在Actor上播放一个跟随Actor位置的音频
--      理论上这个接口要废弃的,因为lua层不能有纯Actor,但目前马车传送里还有纯Actor(TeleportManager:OnSeqStartPlay)所以暂时保留
--      等这块迭代的时候,做一个新的轨道,用轨道来播,或者用Notify来播都可以
---@public
---@param eventName string
---@param actor table UEActor
function AkAudioManager:PostEventOnActor(eventName, actor)
    if not actor then
        Log.WarningFormat("[PostEventOnActor] actor invalid, post %s failed", eventName)
        return AK_INVALID_PLAYING_ID
    end

    return AGS.PostEventAttached(nil, actor, NAME_NONE, true, eventName)
end

-- 通过ID停止播放
---@public
---@param playingID number
---@param blendTime number
---@param blendType number
function AkAudioManager:StopEvent(playingID, blendTime, blendType)
    if playingID == AK_INVALID_PLAYING_ID then
        return
    end

    blendTime = blendTime or 0
    blendType = blendType or BlendTypeLinear
    self.cppMgr:InnerStopEventByPlayingID(playingID, blendTime, blendType)
end

---@public
---@param playingID number
---@param blendTime number
---@param blendType number
function AkAudioManager:PauseEvent(playingID, blendTime, blendType)
    if playingID == AK_INVALID_PLAYING_ID then
        return
    end

    blendTime = blendTime or 0
    blendType = blendType or BlendTypeLinear
    self.cppMgr:InnerPauseEventByPlayingID(playingID, blendTime, blendType)
end

---@public
---@param playingID number
---@param blendTime number
---@param blendType number
function AkAudioManager:ResumeEvent(playingID, blendTime, blendType)
    if playingID == AK_INVALID_PLAYING_ID then
        return
    end

    blendTime = blendTime or 0
    blendType = blendType or BlendTypeLinear
    self.cppMgr:InnerResumeEventByPlayingID(playingID, blendTime, blendType)
end


--endregion PostEvent


--region AUDIO_PARAM


local NoneState = "None" -- luacheck: ignore

---@public
---@param groupName string
---@param state string
function AkAudioManager:SetGroupState(groupName, state)
    --local oldState = self.groupStates[groupName] or NoneState
    --Log.DebugFormat("[SetGroupState] groupName=%s, oldState=%s, newState=%s", groupName, oldState, state)

    -- 值相同,不重复调用
    if self.groupStates[groupName] == state then
        return
    end

    self.groupStates[groupName] = state
    self.cppMgr:InnerSetGroupState(groupName, state)
end

---@public
---@param groupName string
function AkAudioManager:ResetGroupState(groupName)
    --local oldState = self.groupStates[groupName] or NoneState
    --Log.DebugFormat("[ResetGroupState] groupName=%s, oldState=%s", groupName, oldState)

    -- 不重复调用
    if self.groupStates[groupName] == NoneState then
        return
    end

    self.groupStates[groupName] = NoneState
    self.cppMgr:InnerResetGroupState(groupName, NoneState)
end

---@public
---@param rtpcName string
---@param rtpcValue number
function AkAudioManager:SetRtpcValue(rtpcName, rtpcValue)
    --local oldValue = self.rtpcs[rtpcName]
    --Log.DebugFormat("[SetRtpcValue] rtpcName=%s, oldValue=%s, newValue=%s", rtpcName, oldValue, rtpcValue)

    -- 值相同,不重复调用
    if self.rtpcs[rtpcName] == rtpcValue then
        return
    end

    self.rtpcs[rtpcName] = rtpcValue
    self.cppMgr:InnerSetRtpcValue(rtpcName, rtpcValue)
end

---@public
---@param rtpcName string
function AkAudioManager:ResetRtpcValue(rtpcName)
    --Log.DebugFormat("[ResetRtpcValue] rtpcName=%s", rtpcName)

    self.rtpcs[rtpcName] = nil
    self.cppMgr:InnerResetRtpcValue(rtpcName)
end

-- 排序方法
---@private
function AkAudioManager.priorityCompare(a, b)
    return a[3] > b[3]
end

---@private
---@param uniqueID string
---@param groupStates FKGGroupState[]
---@param priority number
function AkAudioManager:OnSetGroupStateWithPriority(uniqueID, groupStates, priority)
    -- -1直接设置
    if priority < 0 then
        for _, groupState in pairs(groupStates) do
            self:SetGroupState(groupState.Group, groupState.State)
        end
        return
    end

    local _, topGroupStates, topPriority = self:getTopVolumeInfo()

    -- 如果优先级更高,则reset当前top, 并设置新的
    if priority >= topPriority then
        for _, topGroupState in pairs(topGroupStates) do
            self:ResetGroupState(topGroupState.Group)
        end

        for _, groupState in pairs(groupStates) do
            self:SetGroupState(groupState.Group, groupState.State)
        end

        -- 记录当前生效的
        self.curVolumeUniqueID = uniqueID
        self.curVolumeGroupStates = groupStates
    end

    -- 无论如何都入队并排序
    table.insert(self.priorityVolumeQueue, { uniqueID, groupStates, priority })
    table.sort(self.priorityVolumeQueue, self.priorityCompare)
end

---@private
---@param uniqueID string
---@param groupStates FKGGroupState[]
---@param priority number
function AkAudioManager:OnResetGroupStateWithPriority(uniqueID, groupStates, priority)
    -- -1直接设置
    if priority < 0 then
        for _, groupState in pairs(groupStates) do
            self:ResetGroupState(groupState.Group)
        end
        return
    end

    -- 无论如何都出队
    self:removeVolumeInfo(uniqueID)

    -- 如果被reset的是cur, 则将cur重置后, 再把Top设置新的
    if uniqueID == self.curVolumeUniqueID then
        for _, curGroupState in pairs(self.curVolumeGroupStates) do
            self:ResetGroupState(curGroupState.Group)
        end

        self.curVolumeUniqueID = ""
        self.curVolumeGroupStates = nil

        local newTopUniqueID, newTopGroupStates = self:getTopVolumeInfo()
        if newTopUniqueID ~= "" then
            for _, newTopGroupState in pairs(newTopGroupStates) do
                self:SetGroupState(newTopGroupState.Group, newTopGroupState.State)
            end

            -- 记录当前生效的
            self.curVolumeUniqueID = newTopUniqueID
            self.curVolumeGroupStates = newTopGroupStates
        end
    end
end

-- uniqueID, groupStates, priority
AkAudioManager.EMPTY_QUEUE_ITEM = { 0, {}, -1 }

---@private
---@return string, FKGGroupState, number
function AkAudioManager:getTopVolumeInfo()
    local topItem = self.priorityVolumeQueue[1] or self.EMPTY_QUEUE_ITEM
    return topItem[1], topItem[2], topItem[3]
end

---@private
---@param uniqueID string
function AkAudioManager:removeVolumeInfo(uniqueID)
    for idx = #self.priorityVolumeQueue, 1, -1 do
        if self.priorityVolumeQueue[idx][1] == uniqueID then
            table.remove(self.priorityVolumeQueue, idx)
            return
        end
    end
end


--endregion AUDIO_PARAM


--region Bank


local __BankNameLuaList__ = {} -- luacheck: ignore

-- 同步加载所有需要自动加载的bank,允许在初始化时同步加载,其他case都必须异步加载
-- 此函数生命周期内只调用一次
---@private
function AkAudioManager:loadAutoLoadBanks()
    table.clear(__BankNameLuaList__)
    local autoLoadBankData = Game.TableData.GetAutoLoadBankDataTable()
    for bankName, _ in ksbcpairs(autoLoadBankData) do
        table.insert(__BankNameLuaList__, bankName)
    end

    self:SyncLoadBankList(__BankNameLuaList__, self)
end

-- 同步加载一个Bank
---@public
---@param bankName string
---@param owner table
function AkAudioManager:SyncLoadBank(bankName, owner)
    table.clear(__BankNameLuaList__)
    table.insert(__BankNameLuaList__, bankName)
    self:SyncLoadBankList(__BankNameLuaList__, owner)
end

local __BankNameArray__ = slua.Array(EPropertyClass.Str) -- luacheck: ignore

-- 同步加载多个Bank
---@public
---@param bankNameList string[]
---@param owner table
function AkAudioManager:SyncLoadBankList(bankNameList, owner)
    local ownerName = self:getOwnerName(owner)
    if ownerName == "" then
        Log.Error("[SyncLoadBankList] invalid owner")
        return
    end

    if #bankNameList == 0 then
        Log.DebugFormat("[SyncLoadBankList] empty bankNameList, ownerName=%s", ownerName)
        return
    end

    __BankNameArray__:Clear()
    for _, bankName in ksbcipairs(bankNameList) do
        if not Game.TableData.GetAkAudioBankDataRow(bankName) then
            Log.WarningFormat("[SyncLoadBankList] try load non exist bank %s", bankName)
            goto continue
        end

        __BankNameArray__:Add(bankName)
        :: continue ::
    end

    self.cppMgr:InnerSyncLoadBankList(__BankNameArray__)
end

-- 同步卸载一个Bank
---@public
---@param bankName string
---@param owner table
function AkAudioManager:SyncUnloadBank(bankName, owner)
    table.clear(__BankNameLuaList__)
    table.insert(__BankNameLuaList__, bankName)
    self:SyncUnloadBankList(__BankNameLuaList__, owner)
end

-- 同步卸载多个Bank
---@public
---@param bankNameList string[]
---@param owner table
---@param bForce boolean 不关心是否在static中,强制卸载
function AkAudioManager:SyncUnloadBankList(bankNameList, owner, bForce)
    local ownerName = self:getOwnerName(owner)
    if ownerName == "" then
        Log.Error("[SyncUnloadBankList] invalid owner")
        return
    end

    if #bankNameList == 0 then
        Log.DebugFormat("[SyncUnloadBankList] empty bankNameList, ownerName=%s", ownerName)
        return
    end

    local autoLoadBankData = Game.TableData.GetAutoLoadBankDataTable()

    __BankNameArray__:Clear()
    for _, bankName in ipairs(bankNameList) do
        if (autoLoadBankData[bankName] == nil) or (bForce == true) then
            __BankNameArray__:Add(bankName)
        end
    end

    if __BankNameArray__:Num() == 0 then
        return
    end

    self.cppMgr:InnerSyncUnloadBankList(__BankNameArray__)
end

-- 获取Bank内存占用(实际上是磁盘文件占用,所以这里拿到的是个大概值)
---@private
---@param bankName
---@return number
function AkAudioManager:getBankSize(bankName)
    local ABD = Game.TableData.GetAkAudioBankDataRow(bankName)
    if not ABD then
        Log.WarningFormat("[getBankSize] %s not exist", bankName)
        return 0
    end

    return ABD.Size
end


--endregion Bank


--region Map


---@private
function AkAudioManager:initMapAudioTagParam()
    self.curMapAudioTag = ""

    ---@type number[]
    self.mapAudioPlayingIDs = {}

    ---@type table<string, string>
    self.mapAudioGroupStates = {}

    ---@type table<string, number>
    self.mapAudioRtpcs = {}
end

---@private
function AkAudioManager:uninitMapAudioTagParam()
    self.curMapAudioTag = ""
    self.mapAudioPlayingIDs = {}
    self.mapAudioGroupStates = {}
    self.mapAudioRtpcs = {}
end

---@private
---@param tag string
function AkAudioManager:getMapRequiredBanks(tag)
    local bankList = {}
    local mapAudioData = Game.TableData.GetLevelMapAudioDataRow(tag)
    if not mapAudioData then
        return bankList
    end

    local bankMap = {}
    for _, event in ksbcipairs(mapAudioData.EnterAkEvents) do
        local requiredBank = self:GetEventRequiredBank(event)
        if requiredBank ~= "" then
            bankMap[requiredBank] = 1
        end
    end
    for _, event in ksbcipairs(mapAudioData.LeaveAkEvents) do
        local requiredBank = self:GetEventRequiredBank(event)
        if requiredBank ~= "" then
            bankMap[requiredBank] = 1
        end
    end

    for bank, _ in pairs(bankMap) do
        table.insert(bankList, bank)
    end

    return bankList
end

---@private
---@param nextLevelMapData LevelMapData
function AkAudioManager:mapAudioTag_onLevelLoadStart(nextLevelMapData)
    if Game.GameLoopManagerV2:GetCurGameLoopStage() == Game.GameLoopManagerV2.EGameStageType.Login then
        return
    end

    local nextMapAudioTag = self:getNextMapAudioTag(nextLevelMapData)
    if nextMapAudioTag == self.curMapAudioTag then
        -- 同tag不做任何处理
        return
    end

    local nextRequiredBanks = self:getMapRequiredBanks(nextMapAudioTag)
    if #nextRequiredBanks > 0 then
        self:SyncLoadBankList(nextRequiredBanks, self)
    end

    self:leaveMapAudioTag(self.curMapAudioTag)
end

---@private
function AkAudioManager:mapAudioTag_onLevelLoadEnd(nextLevelMapData)
    local nextMapAudioTag = self:getNextMapAudioTag(nextLevelMapData)
    if nextMapAudioTag == self.curMapAudioTag then
        -- 同tag不做任何处理
        return
    end

    -- 下个Map的Bank
    local banksToCulling = {}
    local nextRequiredBanks = self:getMapRequiredBanks(nextMapAudioTag)
    for _, bankName in ipairs(nextRequiredBanks) do
        banksToCulling[bankName] = 1
    end

    -- 当前Map的Bank
    local banksToUnload = {}
    local curRequiredBanks = self:getMapRequiredBanks(self.curMapAudioTag)
    for _, bankName in ipairs(curRequiredBanks) do
        if banksToCulling[bankName] == nil then
            table.insert(banksToUnload, bankName)
        end
    end

    if #banksToUnload > 0 then
        self:SyncUnloadBankList(banksToUnload, self)
    end

    self:enterMapAudioTag(nextMapAudioTag)
end

-- 离开一个AudioTag,只播放Stop事件
---@private
---@param tag string
function AkAudioManager:leaveMapAudioTag(tag)
    local mapAudioData = Game.TableData.GetLevelMapAudioDataRow(tag)
    if not mapAudioData then
        return
    end

    for _, eventName in ksbcipairs(mapAudioData.LeaveAkEvents) do
        self:PostEvent2D(eventName, true)
    end
end

-- 进入一个AudioTag
---@private
---@param tag string
function AkAudioManager:enterMapAudioTag(tag)
    self:ResetCurMapAudioTag()
    self.curMapAudioTag = tag

    local mapAudioData = Game.TableData.GetLevelMapAudioDataRow(tag)
    if not mapAudioData then
        return
    end

    for _, eventName in ksbcipairs(mapAudioData.EnterAkEvents) do
        local playingID = self:PostEvent2D(eventName, true)
        table.insert(self.mapAudioPlayingIDs, playingID)
    end

    for groupName, state in ksbcpairs(mapAudioData.StateGroups) do
        self:SetGroupState(groupName, state)
        self.mapAudioGroupStates[groupName] = state
    end

    for rtpcName, rtpcValue in ksbcpairs(mapAudioData.RtpcValue) do
        self:SetRtpcValue(rtpcName, rtpcValue)
        self.mapAudioRtpcs[rtpcName] = rtpcValue
    end
end

-- 重置当前tag下的所有数据
---@public
function AkAudioManager:ResetCurMapAudioTag()
    -- 停止playing event
    __PlayingIDArray__:Clear()
    for _, playingID in pairs(self.mapAudioPlayingIDs) do
        __PlayingIDArray__:Add(playingID)
    end
    self.cppMgr:StopAllByPlayingIDs(__PlayingIDArray__)
    self.mapAudioPlayingIDs = {}

    -- 重置GroupState
    for groupName, _ in pairs(self.mapAudioGroupStates) do
        self:ResetGroupState(groupName)
    end
    self.mapAudioGroupStates = {}

    -- 重置Rtpc
    for rtpcName, _ in pairs(self.mapAudioRtpcs) do
        self:ResetRtpcValue(rtpcName)
    end
    self.mapAudioRtpcs = {}

    self.curMapAudioTag = ""
end

-- 脱离MapTag控制
---@public
function AkAudioManager:OutMapAudioTagControl()
    local curMapAudioTag = self.curMapAudioTag
    self:ResetCurMapAudioTag()

    local curRequiredBanks = self:getMapRequiredBanks(curMapAudioTag)
    if #curRequiredBanks > 0 then
        self:SyncUnloadBankList(curRequiredBanks, self)
    end
end

-- 获取新的MapAudioTag,基于当前map和位面ID
---@private
-- @param nextLevelMapData LevelMapData
function AkAudioManager:getNextMapAudioTag(nextLevelMapData)
    ---@type Space
    local space = Game.NetworkManager.GetLocalSpace()
    if not space then
        return ""
    end

    return nextLevelMapData.MapAudioTag
end

---@public
---@param newType number
function AkAudioManager:OnSceneFieldTypeChanged(newType)
    if newType == -1 then
        return
    end

    Log.DebugFormat("[OnSceneFieldTypeChanged] newType=%s", newType)

    --local bNeedStat = self.sceneFieldType ~= 0

    -- 停止上个type的
    local lastSceneFiledAudioData = Game.TableData.GetSceneFieldAudioDataRow(self.sceneFieldType)
    if lastSceneFiledAudioData then
        for _, eventName in ksbcipairs(lastSceneFiledAudioData.LeaveAkEvents) do
            self:PostEvent2D(eventName)
        end

        -- 如果是切到0,说明是切地图,要把state和rtpc重置
        if newType == 0 then
            for groupName, _ in ksbcpairs(lastSceneFiledAudioData.StateGroups) do
                self:ResetGroupState(groupName)
            end

            for rtpcName, _ in ksbcpairs(lastSceneFiledAudioData.RtpcValue) do
                self:ResetRtpcValue(rtpcName)
            end
        end
    end

    -- 执行新type的
    self.sceneFieldType = newType
    local newSceneFiledAudioData = Game.TableData.GetSceneFieldAudioDataRow(self.sceneFieldType)
    if newSceneFiledAudioData then
        for _, eventName in ksbcipairs(newSceneFiledAudioData.EnterAkEvents) do
            self:PostEvent2D(eventName, true)
        end

        for groupName, state in ksbcpairs(newSceneFiledAudioData.StateGroups) do
            self:SetGroupState(groupName, state)
        end

        for rtpcName, rtpcValue in ksbcpairs(newSceneFiledAudioData.RtpcValue) do
            self:SetRtpcValue(rtpcName, rtpcValue)
        end
    end

    --if bNeedStat then
    --    local music, duration
    --
    --    if lastSceneFiledAudioData then
    --        for _, state in ksbcpairs(lastSceneFiledAudioData.StateGroups) do
    --            music = self.__SceneFieldMusic__[state]
    --        end
    --    end
    --
    --    duration = _now(1) - self.lastChangeTime
    --
    --    self:PrintTraceLog(music, duration)
    --end

    --self.lastChangeTime = _now(1)
end

---获取玩家和当前场景高低空区分基准高度的差值
function AkAudioManager:SetPlayerAltitude()
    if (Game.me == nil) or (Game.me.isDestroyed == true) or (Game.me.bInWorld == false) then
        return
    end

    if not Game.NetworkManager then
        return
    end

    -- todo@shijingzhe:目前只处理廷根
    local space = Game.NetworkManager.GetLocalSpace()
    if (space == nil) or (space.mapID ~= Enum.ELevelMapData.LV_Tiengen_P) then
        return
    end

    local _, _, playerZ = Game.me:GetPosition_P()
    local baseLineZ = Game.TableData.GetConstDataRow("TIENGEN_ENVIRONMENT_SOUNDEFFECT_LIMIT_HEIGHT") or playerZ

    local relativeAltitude = math.floor(playerZ - baseLineZ)
    return self:SetRtpcValue(Enum.EAudioConstData.Player_Height, relativeAltitude)
end

---临时数据,统计用
--AkAudioManager.__SceneFieldMusic__ = {
--    MX_Scene_Area_A = "MX_Scene_Tingen_Area_A_Rich",
--    MX_Scene_Area_B = "MX_Scene_Tingen_Area_B_Poor",
--    MX_Scene_Area_C = "MX_Scene_Tingen_Area_C_Normal",
--    MX_Scene_Area_D = "MX_Scene_Tingen_Area_D_Factory",
--    MX_Scene_Area_E = "MX_Scene_Tingen_Area_E_Cemetery",
--}

--endregion Map


--region UI


-- 触发一个UI音频事件(根据UI事件自动触发的,不要手动调用)
---@public
---@param wbp table
---@param uiEventType number
---@return number
function AkAudioManager:OnUIPostEvent(wbp, uiEventType)
    local playingID = AK_INVALID_PLAYING_ID

    local uiAudioEventType = self.UIEventMap[uiEventType]
    if uiAudioEventType == nil then
        return playingID
    end

    if (IsValid_L(wbp) == false) or (wbp.Audio == nil) then
        return playingID
    end

    local uiAudioConfig = Game.TableData.GetUIAudioDataRow(wbp.Audio)
    if uiAudioConfig == nil then
        return playingID
    end

    local eventName = uiAudioConfig.Audios[uiAudioEventType]
    if (eventName == nil) or (eventName == "") then
        return playingID
    end

    return self:PostEvent2D(eventName)
end


--endregion UI


--region Server


---@private
function AkAudioManager:resetServerAudio()
    for eventName, playingID in pairs(self.serverPostEvents) do
        self:StopEvent(playingID)
        self.serverPostEvents[eventName] = nil
    end

    for group, _ in pairs(self.serverSetGroupState) do
        self:ResetGroupState(group)
        self.serverSetGroupState[group] = nil
    end

    for name, _ in pairs(self.serverSetRtpcValue) do
        self:ResetRtpcValue(name)
        self.serverSetRtpcValue[name] = nil
    end
end

-- 角色加载完成时根据服务器属性刷新音频相关表现
---@private
function AkAudioManager:Receive_LEVEL_ON_ROLE_LOAD_COMPLETED()
    self:RefreshServerPostedEvent()
    self:RefreshServerSetGroupState()
    self:RefreshServerSetRtpcValue()
end

local __Server_Audio_Blend = 500 -- luacheck: ignore

-- 服务器控制播放音频,局内清除,要求服务器有记录
---@public
---@param eventNameList string[]
function AkAudioManager:OnServerPostEvent(eventNameList)
    if not Game.me.bInWorld then
        return
    end

    for _, eventName in ipairs(eventNameList) do
        local inPlayingID = self.serverPostEvents[eventName]
        if inPlayingID then
            self:StopEvent(inPlayingID, __Server_Audio_Blend)
            Log.DebugFormat("[OnServerPostEvent] inPlayingID=%s stopped", inPlayingID)
        end

        local playingID = self:PostEvent2D(eventName)
        self.serverPostEvents[eventName] = playingID
        Log.DebugFormat("[OnServerPostEvent] eventName=%s", eventName)
    end
end

-- 重连时根据服务器属性刷新播放
function AkAudioManager:RefreshServerPostedEvent()
    for eventName, playingID in pairs(self.serverPostEvents) do
        self:StopEvent(playingID)
        self.serverPostEvents[eventName] = nil
    end

    local space = Game.NetworkManager.GetLocalSpace()
    if not space then
        Log.Warning("[RefreshServerPostedEvent] get space entity failed")
        return
    end

    self:OnServerPostEvent(space.AkEventList or {})
end

-- 服务器设置GroupState,要求服务器记录属性
---@public
---@param groupState table<string, string>
function AkAudioManager:OnServerSetGroupState(groupState)
    if not Game.me.bInWorld then
        return
    end

    for group, state in pairs(groupState) do
        self:SetGroupState(group, state)
        self.serverSetGroupState[group] = state
    end
end

-- 重连时根据服务器属性处理
function AkAudioManager:RefreshServerSetGroupState()
    for group, _ in pairs(self.serverSetGroupState) do
        self:ResetGroupState(group)
        self.serverSetGroupState[group] = nil
    end

    local space = Game.NetworkManager.GetLocalSpace()
    if not space then
        Log.Warning("[RefreshServerSetGroupState] get space entity failed")
        return
    end

    self:OnServerSetGroupState(space.AkGroupState or {})
end

-- 服务器设置Rtpc,要求服务器记录属性
---@public
---@param rtpcValue table<string, number>
function AkAudioManager:OnServerSetRtpcValue(rtpcValue)
    if not Game.me.bInWorld then
        return
    end

    for name, Value in pairs(rtpcValue) do
        self:SetRtpcValue(name, Value)
        self.serverSetRtpcValue[name] = Value
    end
end

-- 重连时根据服务器属性处理
function AkAudioManager:RefreshServerSetRtpcValue()
    for name, _ in pairs(self.serverSetRtpcValue) do
        self:ResetRtpcValue(name)
        self.serverSetRtpcValue[name] = nil
    end

    local space = Game.NetworkManager.GetLocalSpace()
    if not space then
        Log.Warning("[RefreshServerSetRtpcValue] get space entity failed")
        return
    end

    self:OnServerSetRtpcValue(space.AkRtpcValue or {})
end


--endregion Server


--region Setting


-- Windows下窗口失去焦点时,根据设置控制音频播放
---@public
---@param bLostFocus boolean
function AkAudioManager:OnWindowFocusChanged(bLostFocus)
    if self.bLostFocus == bLostFocus then
        return
    end

    self.bLostFocus = bLostFocus

    -- Patch&登录阶段有按钮处理
    local EGameStageType = Game.GameLoopManagerV2.EGameStageType
    local curStage = Game.GameLoopManagerV2:GetCurGameLoopStage()
    if (curStage == EGameStageType.Login) or (curStage == EGameStageType.Platform) then
        return
    end

    local bPlayBackGroundAudio = Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.PlayBackGroundSFX)
    if (self.bLostFocus == true) and (bPlayBackGroundAudio == 0) then
        Game.SettingsManager:SetStopBackEndSoundEffect()
    else
        Game.SettingsManager:SetPlayFontEndSoundEffect()
    end
end


--endregion Setting


--region Stage


-- 阶段切换时加卸载Bank
---@public
---@param lastStage number
---@param newStage number
function AkAudioManager:SwitchStageAudio(lastStage, newStage)
    -- 过滤游戏启动时的情况
    if lastStage == newStage then
        return
    end

    local EGameStageType = Game.GameLoopManagerV2.EGameStageType

    if lastStage == EGameStageType.Platform then
        -- 从平台阶段切走的,加载LoginBank
        self:SyncLoadBank(Enum.EAudioConstData.LOGIN_STAGE_BANK, self)
    elseif (lastStage == EGameStageType.InGame) or (lastStage == EGameStageType.MapLoad) or (lastStage == EGameStageType.MainPlayerLoad) then
        -- 从游戏阶段回到登录/创角/选角阶段的
        if (newStage == EGameStageType.Login)
                or (newStage == EGameStageType.CreateRole)
                or (newStage == EGameStageType.SelectRole)
                or (newStage == EGameStageType.Platform) then
            self:OutMapAudioTagControl()
            self:SyncLoadBank(Enum.EAudioConstData.LOGIN_STAGE_BANK, self)
        end
    elseif newStage == EGameStageType.MapLoad or newStage == EGameStageType.MainPlayerLoad then
        -- 从登录/创角/选角阶段进Loading的,标记一下,等InGame阶段时再卸载,执行当前Tag的清理
        if (lastStage == EGameStageType.Login)
                or (lastStage == EGameStageType.CreateRole)
                or (lastStage == EGameStageType.SelectRole)
                or (lastStage == EGameStageType.Platform) then
            self:ResetCurMapAudioTag()
            self.bNeedUnloadStageBank = true
        end
    elseif (newStage == EGameStageType.InGame) and (self.bNeedUnloadStageBank == true) then
        -- 进入到InGame阶段且有标记,进行卸载
        self:SyncUnloadBank(Enum.EAudioConstData.LOGIN_STAGE_BANK, self)
        self.bNeedUnloadStageBank = false
    end
end


--endregion Stage


--region Profile


-- 打印当前的bank信息
---@public
function AkAudioManager:PrintCurrentBankInfo()
    Log.Debug("[PrintCurrentBankInfo] ==============================")
    -- todo@shijingzhe:封装cpp接口
    Log.Debug("[PrintCurrentBankInfo] ==============================")
end

-- 开始Ak性能分析
---@public
---@param fileName string
function AkAudioManager:StartAkProfilerCapture(fileName)
    fileName = fileName or tostring(os.time())
    Log.DebugFormat("[StartAkProfilerCapture] fileName=%s", fileName)
    AGS.StartProfilerCapture(fileName)
end

-- 停止Ak性能分析
---@public
function AkAudioManager:StopAkProfilerCapture()
    Log.Debug("[StopAkProfilerCapture]")
    AGS.StopProfilerCapture()
end


--endregion Profile


--region 填充数据到cpp


-- 静态导出的Event和Bank信息全量设置到cpp,reload时可以调这个
---@private
function AkAudioManager:initAudioDataToCpp()
    self.cppMgr:SetNotifyEventLimit(tonumber(Enum.EAudioConstData.FOOTSTEP_N_ACTION_LIMIT))

    local eventDatas = Game.TableData.GetAkAudioEventDataTable()
    local event2RequiredBank = slua.Map(EPropertyClass.Str, EPropertyClass.Str)
    local event2Duration = slua.Map(EPropertyClass.Str, EPropertyClass.Double)
    for eventName, eventData in ksbcpairs(eventDatas) do
        event2RequiredBank:Add(eventName, eventData.RequiredBank)
        event2Duration:Add(eventName, eventData.Duration)
    end

    self.cppMgr:InnerInitEventData(event2RequiredBank, event2Duration)

    local bankDatas = Game.TableData.GetAkAudioBankDataTable()
    local bank2Size = slua.Map(EPropertyClass.Str, EPropertyClass.Double)
    for bankName, bankData in ksbcpairs(bankDatas) do
        bank2Size:Add(bankName, bankData.Size)
    end

    self.cppMgr:InnerInitBankData(bank2Size)
end

-- 增量更新event关联bank数据到cpp
---@public
---@param eventName string
---@param requiredBank string
function AkAudioManager:UpdateEventDataToCpp(eventName, requiredBank, duration)
    self.cppMgr:InnerUpdateEventData(eventName, requiredBank, duration)
end

-- 增量更新bank大小数据数据到cpp
---@public
---@param bankName string
---@param size number
function AkAudioManager:UpdateBankDataToCpp(bankName, size)
    self.cppMgr:InnerUpdateBankData(bankName, size)
end


--endregion 填充数据到cpp

--region Listener


function AkAudioManager:OnCinematicStart()
    if (self.bEnableAudioListenerSwitch == false) or (Game.me == nil) then
        return
    end

    -- 进入剧情表演,Listener回到相机上
    Game.me.CppEntity:KAPI_Audio_SwitchAudioListenerMode(EAudioListenerMode.Camera)
end

function AkAudioManager:OnCinematicEnd()
    if (self.bEnableAudioListenerSwitch == false) or (Game.me == nil) then
        return
    end

    -- 结束剧情表演,Listener回到人身上
    Game.me.CppEntity:KAPI_Audio_SwitchAudioListenerMode(EAudioListenerMode.Player)
end


--endregion Listener

-- 打印一条音频埋点记录
-- TODO: 开发期统计，上线前移除 @lizhang
--function AkAudioManager:PrintTraceLog(music, duration)
--    Log.DebugFormat("[PrintTraceLog] music=%s, duration=%s", music, duration)
--    Game.AllInSdkManager:Track(Enum.EOperatorTrackType.Game_Audio_Stats, {
--        role_id = Game.me and Game.me.eid or "",
--        music = music or "",
--        duration = duration or 0,
--    }, 0)
--end

return AkAudioManager
