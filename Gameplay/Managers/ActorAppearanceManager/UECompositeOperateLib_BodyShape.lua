local EPropertyClass = import("EPropertyClass")
local FaceControlComponent = import("FaceControlComponent")

--预设体型
---@class BodyShape_OperateData : UEOperateDataBase
local BodyShape_OperateData = DefineClass("BodyShape_OperateData", UEOperateDataBase)
BodyShape_OperateData.UECompositeType = Enum.UECompositeType.BodyShape
function BodyShape_OperateData:ctor()
    self.ProfileName = nil --预设体型
    self.FaceCompactData = nil --骨骼自定义形变参数, 预设
	self.BodyCompactData = nil --骨骼自定义形变参数, 预设
	self.OverrideAllBodyShapeData = nil  -- 捏脸、捏骨都是放在一起的
end

function BodyShape_OperateData:ExecuteOverrideBodyShape(OverrideBodyShapeData)
	self.OverrideAllBodyShapeData = OverrideBodyShapeData
	BodyShape_CompositeOperate.Execute(nil, self)
	return true
end

---@class BodyShape_CompositeOperate: CompositeOperateBase
local BodyShape_CompositeOperate = DefineClass("BodyShape_CompositeOperate", CompositeOperateBase)
BodyShape_CompositeOperate.OperateType = Enum.UECompositeType.BodyShape
BodyShape_CompositeOperate.UEOperateDataCls = BodyShape_OperateData

function BodyShape_CompositeOperate:GetData(InData)
    if InData == nil then
        InData = BodyShape_CompositeOperate.UEOperateDataCls:GetFreeData()
    end
    return InData
end

function BodyShape_CompositeOperate:CollectAssetPath(InData, OutPaths)
	if InData.FaceCompactDataAssetPath then
		table.insert(OutPaths, InData.FaceCompactDataAssetPath)
	end
	if InData.BodyCompactDataAssetPath then
		table.insert(OutPaths, InData.BodyCompactDataAssetPath)
	end
end


---@param InData BodyShape_OperateData
function BodyShape_CompositeOperate:Execute(InData)
	local actorScript = InData:GetActorScript()
	local FaceComponentID = InData:GetActorScript():KAPI_Actor_GetComponentByClass(FaceControlComponent)
	if not IsValidID(FaceComponentID) then
		return false
	end

	local EnableAsset = true --功能开关
	if InData.FaceCompactDataAssetPath and EnableAsset then
		BodyShape_CompositeOperate.ExecuteFaceDataByAssetPath(actorScript, FaceComponentID, InData.ProfileName, InData.FaceCompactDataAssetPath)
	else
		BodyShape_CompositeOperate.ExecuteFaceData(actorScript, FaceComponentID, InData.ProfileName, InData.FaceCompactData)
	end

	if InData.BodyCompactDataAssetPath and EnableAsset then
		BodyShape_CompositeOperate.ExecuteFaceDataByAssetPath(actorScript, FaceComponentID, InData.ProfileName, InData.BodyCompactDataAssetPath)
	else
		BodyShape_CompositeOperate.ExecuteFaceData(actorScript, FaceComponentID, InData.ProfileName, InData.BodyCompactData)
	end

	BodyShape_CompositeOperate.ExecuteFaceData(actorScript, FaceComponentID, InData.ProfileName, InData.OverrideAllBodyShapeData)
end

---@param actorScript
---@param faceComponentID number
---@param profileName string
---@param faceData string|table
function BodyShape_CompositeOperate.ExecuteFaceData(actorScript, faceComponentID, profileName, faceData)
	if type(faceData) == "string" then
		local arrayBuffer = Game.FashionSystem:UnpackStringToArrayBuffer(faceData)
		actorScript:KAPI_FaceControlID_SetFaceDataRuntimeDiffByBuffer(faceComponentID, profileName, arrayBuffer)
	elseif type(faceData) == "table" then
		local map = slua.Map(EPropertyClass.int, EPropertyClass.double)
		map:AppendTable(faceData)
		actorScript:KAPI_FaceControlID_SetFaceDataRuntimeDiff(faceComponentID, profileName, map)
	end
end

function BodyShape_CompositeOperate.ExecuteFaceDataByAssetPath(actorScript, faceComponentID, profileName, faceDataAssetPath)
	actorScript:KAPI_FaceControlID_SetFaceDataRuntimeDiffByAssetPath(faceComponentID, profileName, faceDataAssetPath)
end

return BodyShape_CompositeOperate