local EAttachmentRule = import("EAttachmentRule")
local ActorComponent = import("ActorComponent")
local MeshComponentClass = import("MeshComponent")
local SkeletalMeshComponentClass = import("SkeletalMeshComponent")
local FaceControlComponent = import("FaceControlComponent")
local KGUECompositeOperateLibrary = import("KGUECompositeOperateLib")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")

--模型身体数据
---@class SkeletalMesh_OperateData : UEOperateDataBase
local SkeletalMesh_OperateData = DefineClass("SkeletalMesh_OperateData", UEOperateDataBase)
SkeletalMesh_OperateData.UECompositeType = Enum.UECompositeType.SkeletalMeshCom
function SkeletalMesh_OperateData:ctor()
    self.UID = nil
	self.ModelPartID = nil
	self.ModelID = nil
    self.SkeletalMesh = nil
    self.StaticMesh = nil
	self.bNoCreateMesh = nil ---@type boolean
    self.Offset = nil
    
    self.Tag = nil --如果TargetID没指定, 通过Tag来找,默认没有Tag的新创建
	
	--=====================SocketName、 LeaderPoseComTag、AttachToRootComponent 是互斥的============
    self.SocketName = nil --对象是BodyUpper的, 插槽名称
	self.LeaderPoseComTag = nil --跟随的MeshTag名
	self.AttachToRootComponentSocketName = nil  --挂接到RootComponent下; 用于整体是一个Statck/SkeletalMeshComponent
	
    self.BodyMeshSocketName = nil --对象是MeshCom的, 插槽名称 主Mesh下的  --todo 删除 无逻辑
	self.OverrideMaterials = nil -- 覆盖材质, 配置复用Mesh
	self.MaterialFollowerTags = nil -- 材质跟随, 丝袜等内容要求不同Part之间用同一个某某材质

    self.bReceivesDecals = nil --是否接受贴花
    self.bRenderCustomDepthPass = nil --是否开启自定义深度
    self.bActive = true --是否激活
    self.bVisibility = false --初始是否显示
    
    --OP
    self.bEnableMeshOptimization = false --是否开启SK优化
	self.bEnableAnimTickOptimization = false --是否开启SK动画优化
    self.bUseAttachParentBound = false --使用父类Bound(优化用, 一般false)
    self.bActiveTick = true --是否允许更新

    --TA
    self.bCastInsetShadow = nil --阴影
    self.bSingleSampleShadowFromStationaryLights = nil --物体中心位置去算一个固态阴影的遮蔽值
    self.MaterialOverlay = nil
    self.bOverwriteLighting = true
	self.bEnableCharacterLighting = true
	self.TranslucencySortDistanceOffset = nil
	self:ResetTable()
end

function SkeletalMesh_OperateData:ResetTable()
	self.DecorationComponents = self.DecorationComponents or {} --额外挂接的特殊例外的组件
	self.ParamNames = self.ParamNames or {}
	self.TypeValues = self.TypeValues or {}
	self.ParamValues = self.ParamValues or {}
	table.clear(self.DecorationComponents)
	table.clear(self.ParamNames)
	table.clear(self.TypeValues)
	table.clear(self.ParamValues)
end

function SkeletalMesh_OperateData:SetParam(ParamName, Value)
	if ParamName and Value ~= nil then
		table.insert(self.ParamNames, ParamName)
		table.insert(self.TypeValues, type(Value))
		table.insert(self.ParamValues, tostring(Value))
	end
end

local SkeletalMesh_CompositeOperate = DefineClass("SkeletalMesh_CompositeOperate", CompositeOperateBase)
SkeletalMesh_CompositeOperate.OperateType = Enum.UECompositeType.SkeletalMeshCom
SkeletalMesh_CompositeOperate.UEOperateDataCls = SkeletalMesh_OperateData

function SkeletalMesh_CompositeOperate:ctor()
end

function SkeletalMesh_CompositeOperate:GetData(SKData)
    if SKData == nil then
        SKData = SkeletalMesh_CompositeOperate.UEOperateDataCls:GetFreeData()
    end
    
    --公用优化配置
    SKData.bActive = true --是否激活
    
    --OP
    SKData.bUseAttachParentBound = false --使用父类Bound(优化用, 一般true)，任何疑问 @hujianglong

    --TA
    SKData.bCastInsetShadow = false --阴影
    SKData.bSingleSampleShadowFromStationaryLights = true --物体中心位置去算一个固态阴影的遮蔽值
    SKData.bOverwriteLighting = true
	SKData.bEnableCharacterLighting = true

    return SKData
end

---@param InSKData SkeletalMesh_OperateData
---@param OutPaths string[]
function SkeletalMesh_CompositeOperate:CollectAssetPath(InSKData, OutPaths)
    if StringValid(InSKData.SkeletalMesh) then
        table.insert(OutPaths, InSKData.SkeletalMesh)
    end

    if StringValid(InSKData.StaticMesh) then
        table.insert(OutPaths, InSKData.StaticMesh)
    end

    if StringValid(InSKData.MaterialOverlay) then
        table.insert(OutPaths, InSKData.MaterialOverlay)
    end

	if StringValid(InSKData.GroomComponent) then
		table.insert(OutPaths, InSKData.GroomComponent)
	end

	if InSKData.OverrideMaterials then
		for _, materialPath in pairs(InSKData.OverrideMaterials) do
			table.insert(OutPaths, materialPath)
		end
	end

	if InSKData.DecorationComponents and #InSKData.DecorationComponents > 0 then
		for _, clsPath in pairs(InSKData.DecorationComponents) do
			if StringValid(clsPath) then
				table.insert(OutPaths, clsPath)
			end
		end
	end
end

SkeletalMesh_CompositeOperate.SkComFuncType =
{
	--Bool
	["AddInstanceComponent"] = "AddInstanceComponent",
    ["bUpdateOverlapsOnAnimationFinalize"] = "bUpdateOverlapsOnAnimationFinalize",
    ["SetGenerateOverlapEvents"] = "SetGenerateOverlapEvents",
    ["SetComponentTickEnabled"] = "SetComponentTickEnabled",
    ["SetReceivesDecals"] = "SetReceivesDecals",
    ["SetRenderCustomDepth"] = "SetRenderCustomDepth",
    ["SetActive"] = "SetActive",
    ["SetHiddenInGame"] = "SetHiddenInGame",
    ["SetCastInsetShadow"] = "SetCastInsetShadow",
    ["SetSingleSampleShadowFromStationaryLights"] = "SetSingleSampleShadowFromStationaryLights",
    ["EnableSKMeshOptimization"] = "EnableSKMeshOptimization",
	["EnableAnimTickOptimization"] = "EnableAnimTickOptimization",
    ["bUseAttachParentBound"] = "bUseAttachParentBound",
    ["SetOverwriteLighting"] = "SetOverwriteLighting",
	["UseParentCapsuleComponentBound"] = "UseParentCapsuleComponentBound",
	["SetEnableCharacterLighting"] = "SetEnableCharacterLighting",

	--FString
    ["K2_AttachToComponent"] = "K2_AttachToComponent",
    ["SetLeaderPoseComponent"] = "SetLeaderPoseComponent",
	["AttachToRootComponent"] = "AttachToRootComponent",
    ["SetSkeletalMeshAsset"] = "SetSkeletalMeshAsset",
    ["SetStaticMeshAsset"] = "SetStaticMeshAsset",
    ["SetOverlayMaterial"] = "SetOverlayMaterial",
	["SetOverrideMaterial"] = "SetOverrideMaterial",

	--Int32
    ["SetShadowPhysicsAssetForSkeletalMesh"] = "SetShadowPhysicsAssetForSkeletalMesh",
    ["SetForcedLOD"] = "SetForcedLOD",
	["TranslucencySortDistanceOffset"] = "TranslucencySortDistanceOffset",
};

---@param InSKData SkeletalMesh_OperateData 
function SkeletalMesh_CompositeOperate:Execute(InSKData)
	local AttachOperateCount = 0
	if InSKData.SocketName then
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.K2_AttachToComponent,InSKData.SocketName)
		AttachOperateCount = AttachOperateCount + 1
	end

	if InSKData.LeaderPoseComTag then
		-- 进行LeaderComponent关联时, 一定要做自己Attach到LeaderComponent下
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.K2_AttachToComponent,ViewControlConst.ATTACH_ROOT_SOCKET_NAME)
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.SetLeaderPoseComponent,InSKData.LeaderPoseComTag)
		AttachOperateCount = AttachOperateCount + 1
	end

	if InSKData.AttachToRootComponentSocketName then
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.AttachToRootComponent,InSKData.AttachToRootComponentSocketName)
		AttachOperateCount = AttachOperateCount + 1
	end

	if AttachOperateCount > 1 then
		Log.ErrorFormat(
			"[SkeletalMesh_CompositeOperate:Execute]  Attach Operate Greater Than Once, SocketName:%s  LeaderPoseComTag:%s   AttachToRootComponentSocketName:%s   UID:%s  ModelID:%s ModelPartID:%s",
			InSKData.SocketName, InSKData.LeaderPoseComTag, InSKData.AttachToRootComponentSocketName, InSKData.UID, InSKData.ModelID, InSKData.ModelPartID
		)
	end

	if _G.UE_EDITOR then
		if InSKData.Tag ~= Enum.UECompositeComTag.Mesh then -- 只有创建出来的Component才需要AddInstance. ACharacter自带一个主Mesh. 如果不是ACharacter, 救不了.
			InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.AddInstanceComponent,true)
		end
	end

	local MeshCreateCount = 0
	if InSKData.SkeletalMesh then
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.SetSkeletalMeshAsset,InSKData.SkeletalMesh)
		MeshCreateCount = MeshCreateCount + 1
	end

	if InSKData.StaticMesh then
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.SetStaticMeshAsset,InSKData.StaticMesh)
		MeshCreateCount = MeshCreateCount + 1
	end

	if MeshCreateCount ~= 1 and not InSKData.bNoCreateMesh then
		Log.ErrorFormat(
			"[SkeletalMesh_CompositeOperate:Execute]   MeshCreate Need To Be 1, MeshCreateCount:%s , StaticMesh:%s,   SkeletalMesh:%s   UID:%s  ModelID:%s  ModelPartID:%s",
			MeshCreateCount, InSKData.SkeletalMesh, InSKData.StaticMesh, InSKData.UID, InSKData.ModelID, InSKData.ModelPartID
		)
	end

	if InSKData.TranslucencySortDistanceOffset then
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.TranslucencySortDistanceOffset, InSKData.TranslucencySortDistanceOffset * 100)
	end

	if InSKData.MaterialOverlay then
		InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.SetOverlayMaterial,InSKData.MaterialOverlay)
	end
	
	if InSKData.OverrideMaterials then
		for slotName, materialPath in pairs(InSKData.OverrideMaterials) do
			InSKData:SetParam(SkeletalMesh_CompositeOperate.SkComFuncType.SetOverrideMaterial, slotName .. ";" .. materialPath)
		end
	end
	
	local actorScript = InSKData:GetActorScript()
	if not ensure(actorScript) then
		return
	end
	
    local _, OutMeshModifyEvents = KGUECompositeOperateLibrary.SetSkeletalMeshComParamsInOrder(
		InSKData.UID, _G.GetContextObject(), InSKData.OwnerActorID, 
		InSKData.Tag, 0, InSKData.ParamNames, InSKData.TypeValues, InSKData.ParamValues
	)

	InSKData.bActiveTick = InSKData.bActiveTick or false
	InSKData.bReceivesDecals = InSKData.bReceivesDecals or false
	InSKData.bRenderCustomDepthPass = InSKData.bRenderCustomDepthPass or false
	InSKData.bActive = InSKData.bActive or false
	InSKData.bVisibility = InSKData.bVisibility or false
	InSKData.bCastInsetShadow = InSKData.bCastInsetShadow or false
	InSKData.bSingleSampleShadowFromStationaryLights = InSKData.bSingleSampleShadowFromStationaryLights or false
	InSKData.bEnableMeshOptimization = InSKData.bEnableMeshOptimization or false
	InSKData.bEnableAnimTickOptimization = InSKData.bEnableAnimTickOptimization or false
	InSKData.bUseParentCapsuleComponentBound = InSKData.bUseParentCapsuleComponentBound or false
	InSKData.bUseAttachParentBound = InSKData.bUseAttachParentBound or false
	InSKData.bOverwriteLighting = InSKData.bOverwriteLighting or false
	if InSKData.bEnableCharacterLighting == nil then
		InSKData.bEnableCharacterLighting = true
	end

	KGUECompositeOperateLibrary.SetSkeletalMeshComParamFast(_G.GetContextObject(), InSKData.OwnerActorID, InSKData.Tag,
		InSKData.bActive, false, InSKData.bActiveTick, InSKData.bReceivesDecals, InSKData.bRenderCustomDepthPass, not InSKData.bVisibility, InSKData.bCastInsetShadow,
		InSKData.bSingleSampleShadowFromStationaryLights, InSKData.bUseAttachParentBound, InSKData.bOverwriteLighting, InSKData.bEnableCharacterLighting,
		false, InSKData.bEnableAnimTickOptimization, InSKData.bEnableMeshOptimization, InSKData.bUseParentCapsuleComponentBound)
	
    -- end1 = os.clock()
    -- Log.Debug("AAOG_AvatarTest3 MeshSK",tostring(end1 - start), tostring(InSKData.Tag))

	local FaceComponentID = actorScript:KAPI_Actor_GetComponentByClass(FaceControlComponent) 
	if IsValidID(FaceComponentID) then
		--- 肤色同步机制
		--- bForce = true, 换Mesh必须刷新
		KGUECompositeOperateLibrary.ApplyOtherPartAndSlots(InSKData.UID, _G.ContextObject, InSKData.OwnerActorID, InSKData.Tag, true)

		--给头部设置Groom组件
		if StringValid(InSKData.GroomComponent) then
			local bpAssetID = Game.RoleCompositeMgr:GetActorAppearanceAssetID(InSKData.UID, InSKData.GroomComponent)
			actorScript:KAPI_FaceControlID_SetGroomComponentBPByID(FaceComponentID, bpAssetID)
		end
	end

	--todo 这里SkeletonInstance有点问题，PreviewWorld不生效+DynamicMaterialInstance不生效 暂时不能开启 <EMAIL>
	local SkeletalMeshIDs = actorScript:KAPI_Actor_GetComponentsByTag(SkeletalMeshComponentClass, InSKData.Tag)
	local SkeletalMeshID = SkeletalMeshIDs:Num() > 0 and SkeletalMeshIDs:Get(0) or 0
	if IsValidID(SkeletalMeshID) then
		--头部有Groom的效果，所以在Groom存在的平台不能开启，这里的做法也需要确定下
		local IsHead = Enum.EAvatarBodyPartTypeName[Enum.EAvatarBodyPartType.Head] == InSKData.Tag
		if not IsHead and InSKData.bRenderSkeletonInstance then
			--actorScript:KAPI_SkeletalMeshID_SetRenderSkeletonInstance(MeshComID, true)
		else
			--actorScript:KAPI_SkeletalMeshID_SetRenderSkeletonInstance(MeshComID, false)
		end
	end

	local MeshComIDs = actorScript:KAPI_Actor_GetComponentsByTag(MeshComponentClass, InSKData.Tag)
	local MeshComID = MeshComIDs:Num() > 0 and MeshComIDs:Get(0) or 0
    if IsValidID(MeshComID) then
        if InSKData.Offset then
			local location = InSKData.Offset:GetLocation()
			local rotation = InSKData.Offset:GetRotation():Rotator()
			local scale = InSKData.Offset:GetScale3D()
			actorScript:KAPI_SceneID_SetRelativeTransform(MeshComID, location.X, location.Y, location.Z, rotation.Pitch, rotation.Roll, rotation.Yaw, scale.X, scale.Y, scale.Z)
        end
		--- DecorationComponents, 额外挂接的特殊组件, 比如GFur等.
		--- 1. 组件打上指定Tag
		--- 2. 先销毁旧的, 再挂接新的
		local DecorationTagPrefix = "Decoration_"
		local DecorationTag = DecorationTagPrefix .. InSKData.Tag
		local OldComponentIDs = actorScript:KAPI_Actor_GetComponentsByTag(ActorComponent, DecorationTag)
		local OldComponentsNum = OldComponentIDs:Num()
		for i = 0, OldComponentsNum - 1 do
			local OldComponentID = OldComponentIDs:Get(i)
			actorScript:KAPI_Actor_DestroyComponent(OldComponentID)
		end
		
		if InSKData.DecorationComponents and #InSKData.DecorationComponents > 0 then
			for _, clsPath in pairs(InSKData.DecorationComponents) do
				if StringValid(clsPath) then
					local componentClsID = Game.RoleCompositeMgr:GetActorAppearanceAssetID(InSKData.UID, clsPath)
					if IsValidID(componentClsID) then
						local componentID = actorScript:KAPI_Actor_AddComponentByClassID(componentClsID)
						if IsValidID(componentID) then
							actorScript:KAPI_SceneID_AttachToComponent(componentID, MeshComID, "",
								EAttachmentRule.SnapToTarget, EAttachmentRule.SnapToTarget, EAttachmentRule.SnapToTarget, true)
							actorScript:KAPI_Component_AddComponentTag(componentID, DecorationTag)
						end
					else
						Log.ErrorFormat("SkeletalMesh_CompositeOperate.DecorationComponents Invalid component cls path: %s", clsPath)
					end
				end
			end
		end

        if OutMeshModifyEvents then
            
            if Enum.UEActorMeshCBType.MeshChanged & OutMeshModifyEvents == Enum.UEActorMeshCBType.MeshChanged then
                --2次合并成一个
                if Enum.UEActorMeshCBType.MeshComCreate & OutMeshModifyEvents == Enum.UEActorMeshCBType.MeshComCreate then
					Game.MaterialManager:RefreshAllMaterialCaches(InSKData.OwnerActorID)
                else
					Game.MaterialManager:RefreshAllMaterialCaches(InSKData.OwnerActorID)
                end
            else
                if Enum.UEActorMeshCBType.MeshComCreate & OutMeshModifyEvents == Enum.UEActorMeshCBType.MeshComCreate then
					Game.MaterialManager:RefreshAllMaterialCaches(InSKData.OwnerActorID)
                end
            end
        end
		
		-- material follow, 对于丝袜黑丝白丝之类的材质, 脚和鞋子需要跟随下半身的材质. 
		local UseLowerBodySkinTag = "UseLowerBodySkin"
		if actorScript:KAPI_Actor_ActorHasTag(UseLowerBodySkinTag) then
			-- 移除, 如果有旧的Tag, 需要移除.
			if actorScript:KAPI_Component_ComponentHasTag(MeshComID, UseLowerBodySkinTag) then
				actorScript:KAPI_Component_RemoveComponentTag(MeshComID, UseLowerBodySkinTag)
				actorScript:KAPI_Actor_RemoveActorTag(UseLowerBodySkinTag)
				local data = Game.ActorAppearanceManager.AvatarProfileLib.MaterialFollowers[UseLowerBodySkinTag]
				for i = 1, #data.FollowerParts do
					local followerMeshComIDs = actorScript:KAPI_Actor_GetComponentsByTag(MeshComponentClass, data.FollowerParts[i])
					local followerMeshComID = followerMeshComIDs:Num() > 0 and followerMeshComIDs:Get(0) or KG_INVALID_ID
					if IsValidID(followerMeshComID) then
						local slotIndex = actorScript:KAPI_SkeletalMeshID_GetMaterialIndex(MeshComID, data.SlotNames[i])
						if slotIndex >= 0 then
							Game.MaterialManager:ChangeDefaultMaterialInstanceByID(followerMeshComID, slotIndex, false, false, 0)
						end
					end
				end
			else
				-- 跟随, 如果Actor上有Tag, 判断是否需要跟随设置材质.
				local data = Game.ActorAppearanceManager.AvatarProfileLib.MaterialFollowers[UseLowerBodySkinTag]
				local leaderMeshComID = KG_INVALID_ID
				for i = 1, #data.FollowerParts do
					if data.FollowerParts[i] == InSKData.Tag then
						if not IsValidID(leaderMeshComID) then
							local IDs = actorScript:KAPI_Actor_GetComponentsByTag(MeshComponentClass, data.LeaderPart)
							if IDs:Num() > 0 then
								leaderMeshComID = IDs:Get(0)
							end
							if not IsValidID(leaderMeshComID) then
								break
							end
						end
						local leaderSlotIndex = actorScript:KAPI_SkeletalMeshID_GetMaterialIndex(leaderMeshComID, data.SlotNames[i])
						if leaderSlotIndex == -1 then
							break
						end
						local slotIndex = actorScript:KAPI_SkeletalMeshID_GetMaterialIndex(MeshComID, data.SlotNames[i])
						if slotIndex ~= -1 then
							local materialInstanceID = Game.MaterialManager:GetDefaultMaterialInstanceID(leaderMeshComID, leaderSlotIndex, false, false)
							--materialInstance = materialInstance or Game:ObjectActorManager.GetObjectByID(actorScript:KAPI_PrimitiveID_GetMaterial(leaderMeshComID, leaderSlotIndex))
							Game.MaterialManager:ChangeDefaultMaterialInstanceByID(MeshComID, slotIndex, false, false, materialInstanceID)
						end
					end
				end
			end
		end
		-- 添加, 当前这个Mesh需要其他Part的材质跟随. 目前只有一种Tag, 使用量很少.
		if InSKData.MaterialFollowerTags then
			actorScript:KAPI_Component_AddComponentTag(MeshComID, UseLowerBodySkinTag)
			actorScript:KAPI_Actor_AddActorTag(UseLowerBodySkinTag)
			local data = Game.ActorAppearanceManager.AvatarProfileLib.MaterialFollowers[UseLowerBodySkinTag]
			for i = 1, #data.FollowerParts do
				local IDs = actorScript:KAPI_Actor_GetComponentsByTag(MeshComponentClass, data.FollowerParts[i])
				local followerMeshComID = IDs:Num() > 0 and IDs:Get(0) or KG_INVALID_ID
				if IsValidID(followerMeshComID) then
					local slotIndex = actorScript:KAPI_SkeletalMeshID_GetMaterialIndex(MeshComID, data.SlotNames[i])
					local followerSlotIndex = actorScript:KAPI_SkeletalMeshID_GetMaterialIndex(followerMeshComID, data.SlotNames[i])
					if slotIndex ~= -1 and followerSlotIndex ~= -1 then
						local materialInstanceID = Game.MaterialManager:GetDefaultMaterialInstanceID(MeshComID, slotIndex, false, false)
						Game.MaterialManager:ChangeDefaultMaterialInstanceByID(followerMeshComID, followerSlotIndex, false, false, materialInstanceID)
					end
				end
			end
		end
	end
end

--SK归滚删除
---@param InData SkeletalMesh_OperateData
---@param isNeedDelete boolean
function SkeletalMesh_CompositeOperate:Rollback(InData, isNeedDelete)
	local actorScript = InData and InData:GetActorScript()
    if ensure(actorScript) and InData.Tag ~= Enum.UECompositeComTag.Mesh then
        local ActorComIDs = actorScript:KAPI_Actor_GetComponentsByTag(MeshComponentClass, InData.Tag)
        if ActorComIDs:Length() > 0 then
            local SKComID = ActorComIDs:Get(0)
            if IsValidID(SKComID) then
				if isNeedDelete == true then
					actorScript:KAPI_Actor_DestroyComponent(SKComID)
					Game.MaterialManager:RefreshAllMaterialCaches(InData.OwnerActorID)
				end
            end
        end
    end
end

return SkeletalMesh_CompositeOperate