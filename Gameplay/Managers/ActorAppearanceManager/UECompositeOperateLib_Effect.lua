local MeshComponentClass = import("MeshComponent")
local NiagaraEffectConst = kg_require("Gameplay.Effect.NiagaraEffectConst")
local NIAGARA_SOURCE_TYPE = NiagaraEffectConst.NIAGARA_SOURCE_TYPE
local NIAGARA_EFFECT_TAG = NiagaraEffectConst.NIAGARA_EFFECT_TAG


---@class Effect_OperateData : UEOperateDataBase
local Effect_OperateData = DefineClass("Effect_OperateData", UEOperateDataBase)
Effect_OperateData.UECompositeType = Enum.UECompositeType.Effect
Effect_OperateData.CompositeEffectTag = "CompositeEffectTag"
function Effect_OperateData:ctor()
    self.UID = nil
    self.Tag = Enum.UECompositeComTag.Mesh
	
	self.EffectDatas = self.EffectDatas or {}
	table.clear(self.EffectDatas)
end

local Effect_CompositeOperate = DefineClass("Effect_CompositeOperate", CompositeOperateBase)
Effect_CompositeOperate.OperateType = Enum.UECompositeType.Effect
Effect_CompositeOperate.UEOperateDataCls = Effect_OperateData

---@param InData Effect_OperateData
function Effect_CompositeOperate:GetData(InData)
    if InData == nil then
        InData = Effect_CompositeOperate.UEOperateDataCls:GetFreeData()
    end
    return InData
end

---@param InData Effect_OperateData
function Effect_CompositeOperate:CollectAssetPath(InData, OutPaths)
    for _, EffectData in pairs(InData.EffectDatas) do
        if StringValid(EffectData.NS_Effect) then
            table.insert(OutPaths, #OutPaths + 1, EffectData.NS_Effect)
        end
    end
end

---@param InData Effect_OperateData
function Effect_CompositeOperate:Execute(InData)
	local actorScript = InData:GetActorScript()
	local ActorComIDs = actorScript:KAPI_Actor_GetComponentsByTag(MeshComponentClass, InData.Tag)
	if ActorComIDs:Num() <= 0 then
		return
	end

	local MeshCompId = ActorComIDs:Get(0)
	if not IsValidID(MeshCompId) then
		return
	end

	--c++ 后续考虑固化
	for key, EffectData in pairs(InData.EffectDatas) do
		if StringValid(EffectData.NS_Effect) then
				-- 组装流程不一定都有entity, 没有的不用走culling流程
				local NiagaraBudgetToken
				local EffectPriority = EffectData.Priority and EffectData.Priority or 2
				if InData.UID then
					local Entity = Game.EntityManager:getEntity(InData.UID)
					if Entity then
						local CharacterTypeForViewBudget = Entity:GetCharacterTypeForViewBudget()
						-- 外观特效优先级默认按medium来
						local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
								CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.APPEARANCE, InData.OwnerActorID, EffectPriority)
						if not bCanPlayNiagara then
							goto continue
						end
						NiagaraBudgetToken = BudgetToken
					end
				end

				-- todo 这里没有entity id 使用连线特效可能会出问题
				local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
				NiagaraEffectParam.NiagaraEffectPath = EffectData.NS_Effect
				NiagaraEffectParam.AttachPointName = EffectData.Socket
				NiagaraEffectParam.bNeedAttach = true
				NiagaraEffectParam.SpawnerId = InData.OwnerActorID
				NiagaraEffectParam.AttachComponentId = MeshCompId
				NiagaraEffectParam.bActivateImmediately = true
				NiagaraEffectParam.NiagaraBudgetToken = NiagaraBudgetToken
				NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.APPEARANCE
				NiagaraEffectParam.CustomNiagaraPriority = EffectPriority
				NiagaraEffectParam.ComponentTags = {Effect_OperateData.CompositeEffectTag}
				table.insert(NiagaraEffectParam.ComponentTags, Enum.UECompositeComTag.Mesh)
				NiagaraEffectParam.EffectTags = { NIAGARA_EFFECT_TAG.APPEARANCE }

				-- 确认下是否组装特效要用battle
				NiagaraEffectParam.SourceType = NIAGARA_SOURCE_TYPE.BATTLE

				if EffectData.Offset then
					M3D.ToTransform(EffectData.Offset, NiagaraEffectParam.SpawnTrans)
				end

				if EffectData.FilteredBones and next(EffectData.FilteredBones) ~= nil then
					NiagaraEffectParam.UserVals_MeshCompIds = {}
					NiagaraEffectParam.UserVals_SkeletalMeshCompFilterBones = {}
					NiagaraEffectParam.UserVals_MeshCompIds["SKMesh"] = MeshCompId
					NiagaraEffectParam.UserVals_SkeletalMeshCompFilterBones["SKMesh"] = EffectData.FilteredBones
				end

				Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)

				::continue::
        end
    end
end

return Effect_CompositeOperate