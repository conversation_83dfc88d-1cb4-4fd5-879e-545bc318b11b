require("Gameplay.Managers.ActorAppearanceManager.ActorAppearanceDefine")
local UECompositeOperateLibClass = kg_require("Gameplay.Managers.ActorAppearanceManager.UECompositeOperateLib")
local AppearanceConst = kg_require("Gameplay.CommonDefines.AppearanceConst")
local ActorAppearanceData = kg_require("Gameplay.Managers.ActorAppearanceManager.ActorAppearanceData")

---@class ActorAppearanceManager
---@field AvatarModelLib table<string, AvatarModelData>
local ActorAppearanceManager = DefineClass("ActorAppearanceManager")

function ActorAppearanceManager:ctor()
    self.UECompositeOperateLib = UECompositeOperateLibClass.new()
	
	---@type table<string, AvatarModelData>
	self.AvatarModelLib = require "Data.Config.Model.AvatarModelLib"
	
	self.AvatarProfileLib = kg_require("Data.Config.Model.AvatarProfileLib")

	---@type table<string, table<string, AvatarPartData>>
	self.AvatarModelPartLib = kg_require("Data.Config.Model.AvatarModelPartLib")
	
	---@type table<string, table<string, AvatarPresetData>>
	self.AvatarPresetLib = kg_require("Data.Config.Model.AvatarPresetLib")
	self.NPCSuitLib = kg_require("Data.Config.Model.NPCSuitLib")
	self.NPCSuitLibMakeupData = kg_require("Data.Config.Model.NPCSuitLibMakeupData")
	self.AnimLibAttach = require "Data.Config.Anim.AnimLibAttach"

	-- PartData异步执行的开关
	self.EnableExecuteInOrder = true
	-- 无用SK组件不Tick
	self.EnableDisableTickSKComp = true
end

function ActorAppearanceManager:dtor()
    self.UECompositeOperateLib = nil
end

function ActorAppearanceManager:Reset()
end

function ActorAppearanceManager:Init()
	
    self.UECompositeParamPool = ActorAppearanceData.UECompositeParams

    --数据分配池
    self.ActorAppearanceDataMemoryAlloc = ActorAppearanceData.ActorAppearanceDataMemoryAlloc.new()

    --修改请求操作参数
    UECompositeParams.MemoryAlloc = self.ActorAppearanceDataMemoryAlloc
    self.ActorAppearanceDataMemoryAlloc:AssignPoolSize(ActorAppearanceData.UECompositeParams, 20)

    --组件操作数据
    self.UECompositeOperateLib:Init(self.ActorAppearanceDataMemoryAlloc)

    --模型数据
    ActorAppearanceDataBase.MemoryAlloc = self.ActorAppearanceDataMemoryAlloc

    --各外观部件数据分配对象池(在下列统一定池大小)
    self.ActorAppearanceDataMemoryAlloc:AssignPoolSize(ActorAppearanceData.ActorAppearance_PartData_Body, 100)
    self.ActorAppearanceDataMemoryAlloc:AssignPoolSize(ActorAppearanceData.ActorAppearance_PartData_Model, 600)
	self.ActorAppearanceDataMemoryAlloc:AssignPoolSize(ActorAppearanceData.ActorAppearance_PartData_MultiModel, 600)
    self.ActorAppearanceDataMemoryAlloc:AssignPoolSize(ActorAppearanceData.ActorAppearance_PartData_MeshCustom, 100)
	self.ActorAppearanceDataMemoryAlloc:AssignPoolSize(ActorAppearanceData.ActorAppearance_PartData_BakedBP, 100)

    --各外观数据
    self.ActorAppearanceDataMemoryAlloc:AssignPoolSize(ActorAppearanceData.ActorAppearance_AvatarData, 100)
    
    self.ActorAppearance_DataMap = {}
	self.AllNeedDirtyParts = {}

    self.AvatarFactory = AvatarFactory.new()

	--读表缓存配置数据，线上不会调整这个值
	self.AvatarPresetLevel = {}
	for _,v in pairs(Enum.AvatarPresetLevel) do
		local lodLevel = "AVATAR_HEAD_LOD_" .. v
		local lodValues = Game.TableData.GetConstDataRow(lodLevel)
		if ensure(lodValues and table.getn(lodValues) == 3) then
			self.AvatarPresetLevel[v] = {}
			self.AvatarPresetLevel[v][1] = lodValues[1]
			self.AvatarPresetLevel[v][2] = lodValues[2]
			self.AvatarPresetLevel[v][3] = lodValues[3]
		end
	end
end

function ActorAppearanceManager:UnInit()
	self.ActorAppearance_DataMap = {}
	self.ActorAppearanceDataMemoryAlloc:CheckPoolLeak()
	self.ActorAppearanceDataMemoryAlloc:Reset()
	self.ActorAppearanceDataMemoryAlloc = nil
end

---@return ActorAppearance_AvatarData|nil
function ActorAppearanceManager:GetActorAppearanceData(UID)
    return self.ActorAppearance_DataMap[UID]
end

function ActorAppearanceManager:GetOperateByType(OperateType)
    return self.UECompositeOperateLib:GetOperateByType(OperateType)
end

function ActorAppearanceManager:OnDestoryActor(UID)
	Log.DebugFormat("[LogicUnit-LifeTimeStage][ActorAppearanceManager:OnDestoryActor] UID:%s", UID)
    --清理资源索引
    Game.RoleCompositeMgr:RemoveActorAppearanceAssetRef(UID)

    local _AppearanceData = self.ActorAppearance_DataMap[UID]
    if _AppearanceData then
		for LoadID, Loaded in pairs(_AppearanceData.LoadIDMap) do
			if not Loaded then
				Game.AssetManager:CancelLoadAsset(LoadID)
			else
				Game.AssetManager:RemoveAssetReferenceByLoadID(LoadID)
			end
		end
        _AppearanceData:ReleaseData()
        self.ActorAppearance_DataMap[UID] = nil
    end
end

--刷新角色外观
-- 角色外观数据的Override的流程:  预设数据  + 运行时Override数据
-- 预设数据主要包含如下:
-- [1] BodyPart外观预设数据: {ID:部件模型预设ID,  MakeUpID:部件材质参数预设ID}
-- [2] 脸部、身体骨骼数据:  FaceCompactList --> {[1]=脸部骨骼数据, [2]=身体骨骼数据}

--  运行时数据(主要来自于捏脸流程, 详见DeSerializationCustomRoleData), 主要包含: 
--  [1] 每个角色的预设模板ID
--  [2] 头发: {部件预设ID, 部件预设MakeUpID}
--  [3] 骨骼数据: 脸型 + 体型
--  [4] Head染色数据:  所有材质相关的, 注意需要在ApplyHeadMakeupData之前设置好参数
--  todo [5] 部件的挑染:  还未支持  @汲滕飞

-- 执行流程:
--  [1] 根据模板预设确定所有bodypart部件集合
--  [2] 每个bodypart根据override的规则, 进行override数据关联
--  [3] 收集所有资源, 进行一次异步加载
--  [4] 异步加载后进行每个bodypart的Execute处理,  bodypart间有执行依赖:   除头以外的bodypart -> 头 bodypart(要处理肤色同步、 bodyshape逻辑部分) -> 剩余的ModelLib额外功能(挂接、特效)

-- 已完成 [1] 部分part(例如挂饰) 是支持多个实例的, 这个逻辑并未完善, 需要完善处理; 饰品部分和策划确认, 还没有正式推进, 推进时再和@汲滕飞定协议流程
-- 复数个部件的BodyType数据协议: 
--[[
{
	["Type"] = Enum.EAvatarBodyPartType.EarAccessory,
	["PartsValue"] = {
		["EarAccessory_1"] = {
			["ID"] = "011300001",
			["Offset"] =  FTransform(FQuat(0, 0, 0, 1), FVector(0, 0, 0), FVector(1, 1, 1)),
			["SocketName"]= "lf_ear_03_Slot"
		},
		["EarAccessory_2"] = {
			["ID"] = "011300025",
			["Offset"] =  FTransform(FQuat(0, 0, 0, 1), FVector(0, 0, 0), FVector(1, 1, -50)),
			["SocketName"]= "rf_ear_03_Slot"
		}
	}
}]]--

-- 已完成 [2] override数据部分的材质设置, 将服务器字符串直接传到c++, c++进行反序列化、批量处理。 包括材质、体型数据。 注意: head部分和非head是有设置接口差异, 需要处理 @刘瑞林
-- 已完成 [3] c++ 肤色同步流程支持。 因为[2]在c++中做的, 后续如果进行某个非头部部件替换, 需要在c++侧支持Head的材质参数查询功能  @刘瑞林

---@brief 刷新角色外观
---@param InOwnerActorID number
---@param InUECompositeParams UECompositeParams
---@return boolean starting composite success
function ActorAppearanceManager:Refresh_Avatar(InOwnerActorID, InUECompositeParams)
	if not ensure(IsValidID(InOwnerActorID)) then
		return false
	end
	Log.DebugFormat("[LogicUnit-LifeTimeStage][ActorAppearanceManager:Refresh_Avatar] UID:%s, ActorID: %s", InUECompositeParams.UID, InOwnerActorID)

    local UID = InUECompositeParams.UID

    --转换数据
    local AvatarData = self:GetActorAppearanceData(UID)
	if AvatarData ~= nil and AvatarData:IsExecuteDone() == false then
		--- 上一个操作还没搞完, 或者卡Bug了!
		Log.ErrorFormat("[ActorAppearanceManager:Refresh_Avatar] Cannot Modify Parts When Parts Are Loading, UID:%s ", UID)
		return false
	end
	
    --AvatarFactory
    AvatarData = self.AvatarFactory:ConvertData(AvatarData, InOwnerActorID, InUECompositeParams)
	self.ActorAppearance_DataMap[UID] = AvatarData

	if self.EnableExecuteInOrder then
		self.AvatarFactory:ResetExecuteInOrder(AvatarData)
		self.AvatarFactory:ExecuteInOrder(AvatarData, true)
	else
		AvatarData:OnConvertData(self.AvatarFactory, InOwnerActorID, UID)
		--收集资源
		self.AvatarFactory:CollectAssetPath(AvatarData, true)
		--执行修改
		self.AvatarFactory:Execute(AvatarData)
	end
	
	-- todo 暂时先放这里吧, 存在逻辑数据和拼装出来角色scale不一致的情况(如果是局内, 要走变身的流程, 那边要走逻辑数据一致的兜底) @孙亚
	local Entity = Game.EntityManager:GetEntityByIntID(UID)
	if Entity ~= nil then
		if InUECompositeParams.FacadeScaleValue then
			Entity:SetScale_P(InUECompositeParams.FacadeScaleValue, InUECompositeParams.FacadeScaleValue, InUECompositeParams.FacadeScaleValue)
		end
	end

	--回收请求参数(待全部替换, 方法检测移除)
	if InUECompositeParams.ReleaseData then
		InUECompositeParams:ReleaseData()
	end
	
	return true
end

function ActorAppearanceManager:GetOverrideModelLibData(ModelID)
	local ModelData = nil
	local ModelData_BodyPartList = nil
	local ModelData_ProfileName = nil
	local AvatarPresetLevelValue = nil
	
	if ModelID then
		ModelData = self.AvatarModelLib[ModelID]

		if not ModelData then
			Log.ErrorFormat("GetOverrideModelLibData nil,  ModelID = %s", ModelID)
		end
		
		if ModelData.AvatarPresetLevel then
			AvatarPresetLevelValue = Enum.AvatarPresetLevel[ModelData.AvatarPresetLevel]
		end

		local NpcSuitData = Game.ActorAppearanceManager.NPCSuitLib.NPCSuitLib.SuitLibMap[ModelData.NPCSuit]
		-- 有数据先从NPCSuit
		if ModelData.NPCSuit and NpcSuitData then
			ModelData_BodyPartList = NpcSuitData.BodyPartList
			ModelData_ProfileName = NpcSuitData.ProfileName
			-- NPC用了捏脸的头部设置头部LOD
			if NpcSuitData.FaceCompactList[1] and not string.isEmpty(NpcSuitData.FaceCompactList[1].ID) then
				AvatarPresetLevelValue = Enum.AvatarPresetLevel.B --B级NPC的统一头部LOD设置 @hujianglong
			end
		else
			ModelData_BodyPartList = ModelData.BodyPartList
			ModelData_ProfileName = ModelData.ProfileName
		end
	end

	return ModelData, ModelData_BodyPartList, ModelData_ProfileName, AvatarPresetLevelValue
end

function ActorAppearanceManager:GetHeadBodyPartDataByModelID(modelID)
	local modelData = self.AvatarModelLib[modelID]
	for _, partData in pairs(modelData.BodyPartList) do
		if partData.Type == Enum.EAvatarBodyPartType.Head  then
			return partData
		end
	end
	return nil
end

---获取头部、身体骨骼数据（有HeadModelID时，会优先使用其头部骨骼数据）
---@param ModelId number 全身ModelID
---@param HeadModelID number 头部ModelID
function ActorAppearanceManager:GetFaceAndBodyShapeCompactData(ModelId, HeadModelID)
	local ModelData = self.AvatarModelLib[ModelId]
	local FaceAndBodyCompactList = nil
	local FaceCompactList = nil
	-- 有数据先从NPCSuit
	if ModelData.NPCSuit then
		local NpcSuitData = self.NPCSuitLib.NPCSuitLib.SuitLibMap[ModelData.NPCSuit]
		FaceAndBodyCompactList = NpcSuitData["FaceCompactList"]
	elseif ModelData.AvatarPreset then
		FaceAndBodyCompactList = ModelData.AvatarPreset["FaceCompactList"]
		FaceCompactList = HeadModelID and self.AvatarModelLib[HeadModelID].AvatarPreset["FaceCompactList"]
	end

	-- 后面这个判断只是为了兼容现在逻辑， 没有数据， 是可以不导出
	if FaceAndBodyCompactList == nil or #FaceAndBodyCompactList == 0 then
		return nil, nil
	end

	-- 脸部、身体
	local CompactData = self.AvatarModelPartLib["FaceCompactDataV2"]
	-- 增加判空处理后续要迭代导出数据不用List
	local facePart = nil
	local facePartAssetPath = nil
	if FaceCompactList then
		facePart = FaceCompactList[1] and CompactData[FaceCompactList[1].ID] or nil
		facePartAssetPath = FaceCompactList[1] and CompactData[FaceCompactList[1].ID .. "_AssetPath"] or nil
	else
		facePart = FaceAndBodyCompactList[1] and CompactData[FaceAndBodyCompactList[1].ID] or nil
		facePartAssetPath = FaceAndBodyCompactList[1] and CompactData[FaceAndBodyCompactList[1].ID .. "_AssetPath"] or nil
	end
	
	local bodyPart = FaceAndBodyCompactList[2] and CompactData[FaceAndBodyCompactList[2].ID] or nil
	local bodyPartAssetPath = FaceAndBodyCompactList[2] and CompactData[FaceAndBodyCompactList[2].ID .. "_AssetPath"] or nil
	return facePart, bodyPart, facePartAssetPath, bodyPartAssetPath
end

function ActorAppearanceManager:IsNpcSuitAppearance(ModelID)
	return  self.AvatarModelLib[ModelID].NPCSuit ~= nil
end

function ActorAppearanceManager:GetAppearanceProfileName(ModelID)
	local ModelData = self.AvatarModelLib[ModelID]
	if not ModelData then
		Log.ErrorFormat("GetAppearanceProfileName Error, Check  ModelID = %s", ModelID)
		return nil
	end

	-- 有数据先从NPCSuit
	if ModelData.NPCSuit then
		local NpcSuitData = self.NPCSuitLib.NPCSuitLib.SuitLibMap[ModelData.NPCSuit]
		return NpcSuitData.ProfileName
	end

	return ModelData.ProfileName
end

function ActorAppearanceManager:GetModelPartLibData(ProfileName, PartMeshID)
	local PartDataLib = Game.ActorAppearanceManager.AvatarModelPartLib[ProfileName]
	if PartDataLib then
		return PartDataLib[PartMeshID]
	end
	return nil
end

-- 有一个配置规则, 如果FacadeControlData里面配置了就用其
-- 如果没有, 则使用ModelLib配置
-- 与策划确认的一个规则机制 @媛媛
function ActorAppearanceManager:GetAnimAssetIDByFacadeControlID(FacadeControlID, HintConfigID, NoNeedErrorReport)
	local FacadeControlData = Game.TableData.GetFacadeControlDataRow(FacadeControlID)
	if not FacadeControlData then
		if not NoNeedErrorReport then
			Log.ErrorFormat("[ActorAppearanceManager:GetAnimAssetIDByFacadeControlID] Character:%s has wrong FacadeControlID:%s to get AnimAssetID.", HintConfigID, FacadeControlID)
		end
		return nil
	end
	
	local FacadeAnimID = FacadeControlData.AnimAssetID
	if FacadeAnimID and FacadeAnimID ~= "" then
		return FacadeAnimID
	end

	local ModelData = Game.ActorAppearanceManager:GetModelLibData(FacadeControlData.ModelID)
	if ModelData then
		return ModelData.AnimFlag
	end
	return nil
end

function ActorAppearanceManager:GetModelIDByFacadeControlID(FacadeControlID, HintConfigID)
	local FacadeControlData = Game.TableData.GetFacadeControlDataRow(FacadeControlID)
	if not FacadeControlData then
		Log.ErrorFormat("[ActorAppearanceManager:GetModelIDByFacadeControlID] Character:%s has wrong FacadeControlID:%s to get ModelID.", HintConfigID, FacadeControlID)
		return nil
	end

	local ModelID = FacadeControlData.ModelID
	if ModelID and ModelID ~= "" then
		return ModelID
	end
	
	return nil
end

function ActorAppearanceManager:GetModelLibData(ModelID)
	if ModelID == nil then
		return nil
	end
	return self.AvatarModelLib[ModelID]
end

function ActorAppearanceManager:HasAnimClass(ModelID)
	local ModelData = self:GetModelLibData(ModelID)
	local AnimClass = ModelData and ModelData.AnimData and ModelData.AnimData.AnimClass
	return StringValid(AnimClass)
end

function ActorAppearanceManager:HasSKMesh(ModelID)
	local ModelData = self:GetModelLibData(ModelID)
	local SKMesh = ModelData and ModelData.SKMesh and ModelData.SKMesh.SkeletalMesh
	return StringValid(SKMesh)
end

--=========================================外部封装接口==========================================
---@param UID table
---@param MultiModelBodyPartType table
---@param RemovedModelTagList table
---@param AddedMultiInstPartData table
---@param AddedOverrideMakeupDataMapping table
---@param CallbackObj table   返回true的时候才会被调用, 是异步回调
---@param CallbackName table
function ActorAppearanceManager:ModifyModelPartInstancesInBodyPart(UID, MultiModelBodyPartType, RemovedModelTagList, AddedMultiInstPartData, AddedOverrideMakeupDataMapping, CallbackName)
	local AvatarData = self:GetActorAppearanceData(UID)
	if AvatarData == nil then
		Log.ErrorFormat("[ModifyMultiModelBodyPart] Cannot Find Appearance Data, UID:%s ", UID)
		return false
	end

	if AvatarData:IsExecuteDone() == false then
		Log.ErrorFormat("[ActorAppearanceManager:ModifyMultiModelBodyPart] Cannot Modify Parts When Parts Are Loading, UID:%s ", UID)
		return false
	end


	local ExistPartData = AvatarData:GetExistPartData(MultiModelBodyPartType)
	if ExistPartData ~= nil and RemovedModelTagList ~= nil then
		ExistPartData:RemoveModelPartInstances(RemovedModelTagList)
	end

	if AddedMultiInstPartData == nil or not next(AddedMultiInstPartData) then
		return false
	end

	AvatarData.EntityCompositeCallbackName = CallbackName
	if ExistPartData == nil then
		ExistPartData = AvatarData:GetPartData(ActorAppearance_PartData_MultiModel, MultiModelBodyPartType, true)
	end

	ExistPartData.ModifyType = Enum.ActorAppearanceModifyType.Create
	ExistPartData:ModifyData(AvatarData.UID, MultiModelBodyPartType, AvatarData.BaseModelID, AddedMultiInstPartData)
	
	if AddedOverrideMakeupDataMapping ~=nil then
		ExistPartData:InitModifyMultiInstBodyPartOverrideData(AddedOverrideMakeupDataMapping)
	end

	if self.EnableExecuteInOrder then
		self.AvatarFactory:ResetExecuteInOrder(AvatarData)
		self.AvatarFactory:ExecuteInOrder(AvatarData, false)
	else
		AvatarData:OnConvertData(self.AvatarFactory, AvatarData.OwnerActorID, UID)
		-- 重新进行收集, 之前的是可以不用要了, 理论上是没有作用了
		self.AvatarFactory:CollectAssetPath(AvatarData, false)
		self.AvatarFactory:Execute(AvatarData)
	end
	return true
end

-- 批量进行部件替换Head的替换
---@param UID
---@param RemovedBodyParts 移除部件的part数组
---@param AddedBodyParts {Enum.EAvatarBodyPartType.BodyUpper:{ID:xxx,  MakeUpID:yyy}, Enum.EAvatarBodyPartType.Hair:{..}}
---@param AddedOverrideMakeupDataMapping  {Enum.EAvatarBodyPartType.BodyUpper:OverrideMakeUpData1, Enum.EAvatarBodyPartType.Hair: OverrideMakeUpData2}
---@param OverrideBodyShapeAllCompactData table
---@param CallbackObj table   返回true的时候才会被调用, 是异步回调
---@param InvisibleBodyPartList table 需要隐藏的Part
---@return table
function ActorAppearanceManager:ModifyBodyParts(UID, RemovedBodyPartsList, AddedBodyPartsMapping, AddedOverrideMakeupDataMapping, CallbackName)
	
	local AvatarData = self:GetActorAppearanceData(UID)
	if AvatarData == nil then
		Log.WarningFormat("[ModifyBodyParts] Cannot Find Appearance Data, UID:%s ", UID)
		return false
	end

	if AvatarData:IsExecuteDone() == false then
		Log.ErrorFormat("[ActorAppearanceManager:ModifyBodyParts] Cannot Modify Parts When Parts Are Loading, UID:%s ", UID)
		return false
	end
	
	--[[
		{
			单部件PartType: true,
			复数部件PartType:  方式1: {PartTag1, PartTag2, ...}, 对指定的Tag实例进行移除
							 方式2: true,   对指定的BodyType所有实例进行移除
		}
	]]--
	if RemovedBodyPartsList ~= nil then
		for BodyPartType, RemovedMultiModelPartTags in pairs(RemovedBodyPartsList) do
			-- 当为复数实例部件时, RemovedMultiModelPartTags 为true时, 进行整个部件移除
			if not AppearanceConst.IsMultiInstBodyPart(BodyPartType) or RemovedMultiModelPartTags == true then
				self.AvatarFactory:Execute_RemovePart(AvatarData, BodyPartType, AddedBodyPartsMapping~=nil and AddedBodyPartsMapping[BodyPartType] == nil)
			else
				self.AvatarFactory:Execute_RemoveMultiModelPart(AvatarData, BodyPartType, RemovedMultiModelPartTags)
			end
		end
	end
	
	if (AddedBodyPartsMapping == nil or not next(AddedBodyPartsMapping)) and  (AddedOverrideMakeupDataMapping == nil or not next(AddedOverrideMakeupDataMapping)) then
		return false
	end
	
	table.clear(self.AllNeedDirtyParts)
	if AddedBodyPartsMapping ~= nil then
		for bodyPartType, _ in pairs(AddedBodyPartsMapping) do
			self.AllNeedDirtyParts[bodyPartType] = true
		end
	end

	if AddedOverrideMakeupDataMapping ~= nil then
		for bodyPartType, _ in pairs(AddedOverrideMakeupDataMapping) do
			self.AllNeedDirtyParts[bodyPartType] = true
		end
	end

	AvatarData.EntityCompositeCallbackName = CallbackName
	

	for bodypartType, _ in pairs(self.AllNeedDirtyParts) do
		local _PartValue = AddedBodyPartsMapping and AddedBodyPartsMapping[bodypartType]
		local OverrideMakeupData = AddedOverrideMakeupDataMapping and AddedOverrideMakeupDataMapping[bodypartType]
		
		local isMultiInstBodyPart = AppearanceConst.IsMultiInstBodyPart(bodypartType)
		-- 单实例部件
		if not isMultiInstBodyPart then
			local PartData_Model = AvatarData:GetPartData(ActorAppearance_PartData_Model, bodypartType, _PartValue ~= nil)
			PartData_Model.ModifyType = Enum.ActorAppearanceModifyType.Create
			if _PartValue ~= nil then
				PartData_Model:ModifyData(AvatarData.UID, bodypartType, AvatarData.BaseModelID, bodypartType,
					_PartValue, _PartValue.ID, _PartValue.MakeupID, nil, nil, Enum.EAvatarBodyPartTypeName[bodypartType]
				)
			end
			
			if OverrideMakeupData ~= nil then
				PartData_Model:InitModifyBodyPartOverrideData(nil, nil, OverrideMakeupData, nil)
			end
		else 
			-- 多实例部件
			local PartData_MultiModel = AvatarData:GetPartData(ActorAppearance_PartData_MultiModel, bodypartType, false)
			PartData_MultiModel.ModifyType = Enum.ActorAppearanceModifyType.Create
			if _PartValue ~= nil then
				PartData_MultiModel:ModifyData(AvatarData.UID, bodypartType, AvatarData.BaseModelID, _PartValue)
			end
			
			if OverrideMakeupData ~= nil then
				PartData_MultiModel:InitModifyMultiInstBodyPartOverrideData(OverrideMakeupData)
			end
		end
	end

	if self.EnableExecuteInOrder then
		self.AvatarFactory:ResetExecuteInOrder(AvatarData)
		self.AvatarFactory:ExecuteInOrder(AvatarData, false)
	else
		AvatarData:OnConvertData(self.AvatarFactory, AvatarData.OwnerActorID, UID)
		-- 重新进行收集, 之前的是可以不用要了, 理论上是没有作用了
		self.AvatarFactory:CollectAssetPath(AvatarData, false)
		self.AvatarFactory:Execute(AvatarData)
	end
	return true
end

--改脸部件MakeUpData, 全量的Override数据
function ActorAppearanceManager:Avatar_ModifyPart_MakeUpData(UID, BodyPart, OverrideMakeUpData, EntityCallbackName)
	local AvatarData = self:GetActorAppearanceData(UID)
	if AvatarData == nil then
		Log.ErrorFormat("[Avatar_ModifyPart_MakeUpData] Cannot Find Appearance Data, UID:%s ", UID)
		return false
	end

	local BodyPartPartData = AvatarData:GetExistPartData(BodyPart)
	if BodyPartPartData == nil then
		Log.ErrorFormat("[ModifyPart_MakeUpData]  Cannot Get Existed BodyPart UID:%s BodyPartType:%s", UID, BodyPart)
		return false
	end

	local OperateMatData = BodyPartPartData:GetOperateMaterialData()
	if OperateMatData == nil then
		Log.ErrorFormat("[ModifyPart_MakeUpData]  Cannot Get Existed Material Operate Data, UID:%s BodyPartType:%s", UID, BodyPart)
		return false
	end

	local ExecuteSuccess = OperateMatData:ExecuteOverrideBodyPartMakeUpData(OverrideMakeUpData, EntityCallbackName, BodyPartPartData)
	return ExecuteSuccess
end

-- 改体型, 全量的脸 + 体型的Override数据
function ActorAppearanceManager:Avatar_Modify_BodyShape(UID, OverrideBodyShapeAllCompactData)
	local AvatarData = self:GetActorAppearanceData(UID)
	if AvatarData == nil then
		Log.ErrorFormat("[Avatar_Modify_BodyShape] Cannot Find Appearance Data, UID:%s ", UID)
		return false
	end

	local BodyPartPartData = AvatarData:GetExistPartData(Enum.EAvatarBodyPartType.Head)
	if BodyPartPartData == nil then
		Log.ErrorFormat("[Avatar_Modify_BodyShape]  Cannot Get Existed BodyPart UID:%s BodyPartType:%s", UID, Enum.EAvatarBodyPartType.Head)
		return false
	end

	local OperateBodyShapeData = BodyPartPartData:GetOperateBodyShapeData()
	if OperateBodyShapeData == nil then
		Log.ErrorFormat("[Avatar_Modify_BodyShape]  Cannot Get Existed BodyShape Operate Data, UID:%s BodyPartType:%s", UID, Enum.EAvatarBodyPartType.Head)
		return false
	end

	return OperateBodyShapeData:ExecuteOverrideBodyShape(OverrideBodyShapeAllCompactData)
end

-- ========================================外部封装接口============================================

return ActorAppearanceManager