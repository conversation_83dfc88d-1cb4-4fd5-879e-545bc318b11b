local EPropertyClass = import("EPropertyClass")
kg_require("Framework.C7Common.CppManagerBase")

local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local MaterialEffectConst = kg_require("Gameplay.Effect.MaterialEffectConst")
local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
local SEARCH_MATERIAL_TYPE = MaterialEffectParamTemplate.SEARCH_MATERIAL_TYPE

--[[
材质管理器提供了材质栈和材质参数缓存的机制，以及相关联的优先级控制等功能

1，关于材质栈
	不同的业务可能会在不同的场景下去替换角色Mesh上的材质，一方面我们需要一个统一的结构去处理材质替换任务的优先级，另一方面，当一个业务不再需要替换材质时，相应材质栈中次高优先级的
材质替换任务就会生效，在MaterialManager中，这里使用了一个优先队列来处理相应的逻辑
	关于材质栈，可以分为4个层级
	1, 每个Actor包含多个MeshComponent
	2, 每个MeshComponent对应一个MaterialCacheSet, 每个MaterialCacheSet中包含多个 MaterialCacheStack
	3, 每个MaterialCacheStack中包含多个 MaterialCacheItem
	4, 每个MaterialCacheItem中对应一个材质实例（MaterialInstance而非DynamicMaterialInstance）

2，关于材质参数设置
	对于大多数场景下，业务希望通过修改材质参数来播放相应的材质表现，但是并不想替换材质，当材质栈变化的时候，这些材质参数需要能够保留，因此需要一个相对独立的材质参数的缓存，
在材质栈变化的时候能继承相应的材质参数。
	材质参数同样存在优先级，部分材质效果由于修改了相同的材质参数可能存在冲突，而对于修改不同材质参数的使用方来说，这些材质效果通常可以共存不存在冲突
	从目前的使用case来看，绝大部分修改材质表现效果仅需要按照曲线或者固定斜率的线性函数在一定时间内修改一组材质参数即可达成，因此在材质参数设置上，支持外部传入材质参数配置集合的方式
来实现自身需要的材质表现效果，同样可以通过传入EffectType的方式来确认是否需要引入优先级控制（部分简单交互物的材质表现无需引入优先级）。对于小部分特殊逻辑的材质表现来说，后续可以支持
预制好对应的MaterialEffectTask，来实现较为复杂的材质表现控制效果
	关于材质参数优先队列，可以分为2个层级
	1, 每个Actor按照EffectType不同，包含多个材质参数优先队列;
	2, 每个优先队列中保存了修改材质参数的RequestId, 优先级以及SequenceId
	
]]--

---@class MaterialManager
MaterialManager = DefineClass("MaterialManager", CppManagerBase)

MaterialManager.TempVal_MaterialSlotNames = slua.Array(EPropertyClass.Name)
MaterialManager.TempVal_ComponentTypes = slua.Array(EPropertyClass.Str)
MaterialManager.TempVal_ExcludeMeshCompTags = slua.Array(EPropertyClass.Name)
MaterialManager.TempVal_ComponentTypes:Add("/Script/Engine.StaticMeshComponent")
MaterialManager.TempVal_ComponentTypes:Add("/Script/Engine.SkeletalMeshComponent")
MaterialManager.TempVal_ComponentTypes:Add("/Script/Engine.PoseableMeshComponent")

-- 材质管理器中很多日志要调用cpp函数去或者mesh或者material名称, 因此先加个开关, 等后面功能稳定以后日志可以全部注释掉
MaterialManager.bEnableLogging = false

function MaterialManager:Init()
	self:CreateCppManager("KGMaterialManager")
	
	self.cppMgr:SetAllValidMeshComponentTypes(MaterialManager.TempVal_ComponentTypes)
    self.cppMgr:KAPI_Material_SetCameraDitherInfos(
			MaterialEffectConst.CAMERA_DITHER_CHARACTER_TYPE_PARAM_NAME,
			MaterialEffectConst.CAMERA_DITHER_PIVOT_POINT_PARAM_NAME,
			MaterialEffectConst.CAMERA_DITHER_DISTANCE_SCALE_PARAM_NAME,
			MaterialEffectConst.CAMERA_DITHER_DISTANCE_SCALE,
			MaterialEffectConst.CAMERA_DITHER_MAX_ENABLE_CAMERA_DITHER_NUM_PER_TICK,
			ViewResourceConst.CAMERA_DITHER_CHARACTER_DEFAULT_OLM_PATH,
			ViewResourceConst.CAMERA_DITHER_ENV_DEFAULT_OLM_PATH,
			MaterialEffectConst.CAMERA_DITHER_IF_USE_WORLD_PIVOT_POINT_PARAM_NAME,
			MaterialEffectConst.CAMERA_DITHER_WORLD_PIVOT_POINT_PARAM_NAME,
			MaterialEffectConst.CAMERA_DITHER_DEFAULT_PIVOT_OFFSET,
			MaterialEffectConst.CAMERA_DITHER_MANOR_BUILDING_MESH_TAG)
end

function MaterialManager:UnInit()
	self:DestroyCppManager()
end


--region Material

---@public
---@param ChangeMaterialReq ChangeMaterialRequestTemplate
---@param ChangeMaterialParamReq ChangeMaterialParamRequestTemplate
---部分逻辑需要在切换材质以后同时开启材质表现, 但是材质和材质参数在开启前都需要异步加载资源, 实际上材质的应用和材质参数的设置可能存在几帧的偏差, 从而导致表现问题
---例如黑化溶解和普通溶解效果, 为此本接口提供了同时设置材质和开启材质参数的功能
function MaterialManager:ChangeMaterialAndMaterialParam(ChangeMaterialReq, ChangeMaterialParamReq)
	if ChangeMaterialReq.MaterialPath == nil or ChangeMaterialReq.MaterialPath == "" then
		Log.Warning("MaterialManager:ChangeMaterialAndMaterialParam, invalid material path")
		MaterialEffectParamsPool.RecycleToPool(ChangeMaterialReq)
		MaterialEffectParamsPool.RecycleToPool(ChangeMaterialParamReq)
		return
	end

	if ChangeMaterialReq.OwnerActorId ~= ChangeMaterialParamReq.OwnerActorId then
		Log.Error("MaterialManager:ChangeMaterialAndMaterialParam, invalid owner actor id")
		return
	end
	
	local ReqId
	if ChangeMaterialReq.CustomChangeMaterialReqId == nil and ChangeMaterialParamReq.CustomChangeMaterialParamReqId == nil then
		ReqId = self:GenerateChangeMaterialSeqId()
	elseif ChangeMaterialReq.CustomChangeMaterialReqId == ChangeMaterialParamReq.CustomChangeMaterialParamReqId then
		-- 两者此时必定相等
		ReqId = ChangeMaterialReq.CustomChangeMaterialReqId
	else
		Log.Error("MaterialManager:ChangeMaterialAndMaterialParam, invalid custom request id")
		return
	end

	ChangeMaterialReq.CustomChangeMaterialReqId = ReqId
	ChangeMaterialReq.RelatedChangeMaterialParamReqID = ReqId
	ChangeMaterialParamReq.CustomChangeMaterialParamReqId = ReqId
	ChangeMaterialParamReq.RelatedChangeMaterialReqID = ReqId
	
	self:ChangeMaterial(ChangeMaterialReq)
	self:ChangeMaterialParam(ChangeMaterialParamReq)
	
	return ReqId
end


---@public
function MaterialManager:RevertMaterialAndMaterialParam(ReqId)
	self:RevertMaterial(ReqId)
	self:RevertMaterialParam(ReqId)
end


function MaterialManager:AddExcludedMeshComponentId(ActorId, MeshCompId)
	if ActorId == 0 or MeshCompId == 0 then
		return
	end
	
	self.cppMgr:AddExcludedMeshComponentId(ActorId, MeshCompId)
end

function MaterialManager:RemoveExcludedMeshComponentId(ActorId, MeshCompId)
	if ActorId == 0 or MeshCompId == 0 then
		return
	end
	
	self.cppMgr:RemoveExcludedMeshComponentId(ActorId, MeshCompId)
end

---@param ChangeMaterialReq ChangeMaterialRequestTemplate
function MaterialManager:ChangeMaterial(ChangeMaterialReq)
	if ChangeMaterialReq.MaterialPath == nil or ChangeMaterialReq.MaterialPath == "" then
		Log.Warning("MaterialManager:ChangeMaterial, invalid material path")
		MaterialEffectParamsPool.RecycleToPool(ChangeMaterialReq)
		return
	end

	local bNeedClearMaterialSlotNames = false
	if ChangeMaterialReq.MaterialSlotNames ~= nil then
		bNeedClearMaterialSlotNames = true
		for _, SlotName in ipairs(ChangeMaterialReq.MaterialSlotNames) do
			MaterialManager.TempVal_MaterialSlotNames:Add(SlotName)
		end
	end

	local bNeedClearExcludeMeshCompTags = false
	if ChangeMaterialReq.ExcludeMeshComponentTags ~= nil then
		bNeedClearExcludeMeshCompTags = true
		for _, ComponentTag in ipairs(ChangeMaterialReq.ExcludeMeshComponentTags) do
			MaterialManager.TempVal_ExcludeMeshCompTags:Add(ComponentTag)
		end
	end

	local ReqId = self.cppMgr:ScriptChangeMaterial(
			ChangeMaterialReq.MaterialPath, ChangeMaterialReq.SearchMeshType, ChangeMaterialReq.SearchMeshName or "", MaterialManager.TempVal_ExcludeMeshCompTags,
			ChangeMaterialReq.SearchMaterialType, MaterialManager.TempVal_MaterialSlotNames, ChangeMaterialReq.bIgnoreExcludedMeshComp,
			ChangeMaterialReq.Priority, ChangeMaterialReq.TotalLifeMs, ChangeMaterialReq.OwnerActorId,
			ChangeMaterialReq.CustomChangeMaterialReqId or 0,
			ChangeMaterialReq.RelatedChangeMaterialParamReqID)
	
	MaterialEffectParamsPool.RecycleToPool(ChangeMaterialReq)

	if bNeedClearMaterialSlotNames then
		MaterialManager.TempVal_MaterialSlotNames:Clear()
	end

	if bNeedClearExcludeMeshCompTags then
		MaterialManager.TempVal_ExcludeMeshCompTags:Clear()
	end
	
	return ReqId
end

---@public
function MaterialManager:RevertMaterial(ChangeMaterialReqId)
	if ChangeMaterialReqId == nil then
		return
	end

	self.cppMgr:RevertMaterial(ChangeMaterialReqId)
end

---@public
function MaterialManager:RevertAllMaterialsByOwnerActorId(ActorId)
	if ActorId == nil then
		return
	end

	self.cppMgr:RevertMaterialByActorId(ActorId)
end
--endregion Material


--region MaterialParam

---@public
---@param ChangeMaterialParamReq ChangeMaterialParamRequestTemplate
function MaterialManager:ChangeMaterialParam(ChangeMaterialParamReq)

	local bNeedClearMaterialSlotNames = false
	if ChangeMaterialParamReq.MaterialSlotNames ~= nil then
		bNeedClearMaterialSlotNames = true
		for _, SlotName in ipairs(ChangeMaterialParamReq.MaterialSlotNames) do
			MaterialManager.TempVal_MaterialSlotNames:Add(SlotName)
		end
	end
    
	if ChangeMaterialParamReq.bUseOLMDissolve == true then
		if ChangeMaterialParamReq.SearchMaterialType == SEARCH_MATERIAL_TYPE.SearchAllNormalMaterial then
			ChangeMaterialParamReq.SearchMaterialType = SEARCH_MATERIAL_TYPE.SearchRuntimeSeparateOverlayMaterial
		elseif ChangeMaterialParamReq.SearchMaterialType == SEARCH_MATERIAL_TYPE.SearchMaterialBySlots then
			ChangeMaterialParamReq.SearchMaterialType = SEARCH_MATERIAL_TYPE.SearchRuntimeSeparateOverlayMaterialBySlots
		else
			Log.Warning("unexpected SearchMaterialType for OLMDissolve", ChangeMaterialParamReq.SearchMaterialType)
		end
	end

	local ReqId = self.cppMgr:ScriptChangeMaterialParam(
			ChangeMaterialParamReq.SearchMeshType, ChangeMaterialParamReq.SearchMeshName or "",
			ChangeMaterialParamReq.SearchMaterialType, MaterialManager.TempVal_MaterialSlotNames, ChangeMaterialParamReq.bIgnoreExcludedMeshComp,
			ChangeMaterialParamReq.Priority, ChangeMaterialParamReq.TotalLifeMs, ChangeMaterialParamReq.EffectType ~= nil,
			ChangeMaterialParamReq.EffectType or 0, ChangeMaterialParamReq.OwnerActorId,
			ChangeMaterialParamReq.CustomChangeMaterialParamReqId or 0,
			ChangeMaterialParamReq.RelatedChangeMaterialReqID)

	local iter_params_func = ChangeMaterialParamReq.bUseTableConfig and ksbcpairs or pairs
	if ChangeMaterialParamReq.ScalarParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.ScalarParams) do
			self.cppMgr:InnerAddScalarParam(ReqId, ParamName, ParamVal)
		end
	end

	if ChangeMaterialParamReq.VectorParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.VectorParams) do
			self.cppMgr:InnerAddVectorParam(ReqId, ParamName, ParamVal.R, ParamVal.G, ParamVal.B, ParamVal.A)
		end
	end

	if ChangeMaterialParamReq.TextureParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.TextureParams) do
			self.cppMgr:InnerAddTextureParam(ReqId, ParamName, ParamVal)
		end
	end

	if ChangeMaterialParamReq.MediaTextureParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.MediaTextureParams) do
			self.cppMgr:InnerAddMediaTextureParam(ReqId, ParamName, ParamVal)
		end
	end

	------------------------------------------------------------------------------------------------
	-- todo 整合 FloatCurveParams&VectorCurveParams&LinearColorCurveParams
	if ChangeMaterialParamReq.FloatCurveParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.FloatCurveParams) do
			self.cppMgr:InnerAddCurveParam(
					ReqId, ParamName, ParamVal.AssetPath,
					ParamVal.RemapTime ~= nil,
					ParamVal.RemapTime ~= nil and ParamVal.RemapTime or 0,
					ParamVal.bEnableLoop ~= nil and ParamVal.bEnableLoop or false,
					ParamVal.bActivateImmediately ~= nil and ParamVal.bActivateImmediately or false)
		end
	end

	if ChangeMaterialParamReq.VectorCurveParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.VectorCurveParams) do
			self.cppMgr:InnerAddCurveParam(
					ReqId, ParamName, ParamVal.AssetPath,
					ParamVal.RemapTime ~= nil,
					ParamVal.RemapTime ~= nil and ParamVal.RemapTime or 0,
					ParamVal.bEnableLoop ~= nil and ParamVal.bEnableLoop or false,
					ParamVal.bActivateImmediately ~= nil and ParamVal.bActivateImmediately or false)
		end
	end

	if ChangeMaterialParamReq.LinearColorCurveParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.LinearColorCurveParams) do
			self.cppMgr:InnerAddCurveParam(
					ReqId, ParamName, ParamVal.AssetPath,
					ParamVal.RemapTime ~= nil,
					ParamVal.RemapTime ~= nil and ParamVal.RemapTime or 0,
					ParamVal.bEnableLoop ~= nil and ParamVal.bEnableLoop or false,
					ParamVal.bActivateImmediately ~= nil and ParamVal.bActivateImmediately or false)
		end
	end
	------------------------------------------------------------------------------------------------

	if ChangeMaterialParamReq.VectorLinearSampleParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.VectorLinearSampleParams) do
			self.cppMgr:InnerAddLinearSampleVectorParam(ReqId,
					ParamName, ParamVal.StartR, ParamVal.StartG, ParamVal.StartB, ParamVal.StartA,
					ParamVal.EndR, ParamVal.EndG, ParamVal.EndB, ParamVal.EndA, ParamVal.Duration,
					ParamVal.bActivateImmediately ~= nil and ParamVal.bActivateImmediately or false)
		end
	end

	if ChangeMaterialParamReq.ScalarLinearSampleParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.ScalarLinearSampleParams) do
			self.cppMgr:InnerAddLinearSampleScalarParam(
					ReqId, ParamName, ParamVal.StartVal, ParamVal.EndVal, ParamVal.Duration,
					ParamVal.bActivateImmediately ~= nil and ParamVal.bActivateImmediately or false)
		end
	end

	if ChangeMaterialParamReq.ActorLocationParams then
		for ParamName, ParamVal in iter_params_func(ChangeMaterialParamReq.ActorLocationParams) do
			self.cppMgr:InnerAddActorLocationParam(ReqId, ParamName, ParamVal)
		end
	end

	if ChangeMaterialParamReq.TextureMaterialParamsInheritDefaultMaterial then
		for _, ParamName in iter_params_func(ChangeMaterialParamReq.TextureMaterialParamsInheritDefaultMaterial) do
			self.cppMgr:InnerAddInheritDefaultMaterialTextureParam(ReqId, ParamName)
		end
	end
	
	self.cppMgr:ActivateChangeMaterialParamReq(ReqId)
	MaterialEffectParamsPool.RecycleToPool(ChangeMaterialParamReq)

	if bNeedClearMaterialSlotNames then
		MaterialManager.TempVal_MaterialSlotNames:Clear()
	end
	
	return ReqId
end

---@public
function MaterialManager:RevertMaterialParam(ChangeMaterialParamReqId)
	if ChangeMaterialParamReqId == nil then
		return
	end

	self.cppMgr:RevertMaterialParam(ChangeMaterialParamReqId)
end

---@public
function MaterialManager:RevertActorAllMaterialParams(ActorId)
	if ActorId == nil or ActorId == 0 then
		return
	end

	self.cppMgr:RevertMaterialParamByActorId(ActorId)
end

---@public
function MaterialManager:UpdateLinearSampleScalarMaterialParamTargetVal(ChangeMaterialParamId, ParamName, TargetVal, bUseNewDuration, NewDuration)
	self.cppMgr:UpdateLinearSampleParamTargetValue(ChangeMaterialParamId, ParamName, TargetVal, bUseNewDuration, NewDuration)
end

---@public
function MaterialManager:ChangeDefaultMaterialInstanceByID(InMeshCompId, MaterialIndex, bOverlayMaterial, bSeparateOverlapMaterial, MaterialInstanceID)
	self.cppMgr:ChangeDefaultMaterialInstanceByID(InMeshCompId, MaterialInstanceID, MaterialIndex, bOverlayMaterial, bSeparateOverlapMaterial)
end

---@public
function MaterialManager:GetDefaultMaterialInstanceID(InMeshCompId, MaterialIndex, bOverlayMaterial, bSeparateOverlapMaterial)
	return self.cppMgr:GetDefaultMaterialInstanceID(InMeshCompId, MaterialIndex, bOverlayMaterial, bSeparateOverlapMaterial)
end

---@public
---通过这个接口可以获取到当前材质栈中生效的dynamic material instance, 外部获取到对应材质实例以后可以设置相应的材质参数
---需要注意的是，这些材质参数一旦切换了MeshAsset或者MeshComponent, 所有的参数都会丢失，当前只有角色组装会用这部分逻辑
function MaterialManager:GetDynamicMaterialInstanceByID(InMeshCompId, MaterialIndex, bOverlayMaterial, bSeparateOverlapMaterial)
	return self.cppMgr:GetDynamicMaterialInstanceByID(InMeshCompId, MaterialIndex, bOverlayMaterial, bSeparateOverlapMaterial)
end

--- 对于受击闪白类似的材质表现，由于触发频率比较高, 为了尽可能降低这些材质表现的开销
---	1，相应材质参数任务不纳入优先队列管理;
---	2, 相同EffectType的材质更新任务，默认非Transient任务优先级高于Transient任务
---	3, 当发生mesh切换以后，所有transient task都会被直接清理
---@public
function MaterialManager:AddBodyTransientMaterialParamUpdateTask(OwnerActorId, InEffectType, LifeTimeSeconds)
	return self.cppMgr:AddTransientUpdateTask(OwnerActorId, LifeTimeSeconds, InEffectType ~= nil, InEffectType or 0)
end

---@public
function MaterialManager:SetScalarParameterByTransientTaskId(TaskId, ParamName, ParamVal)
	self.cppMgr:SetScalarParameterByTransientTaskId(TaskId, ParamName, ParamVal)
end

function MaterialManager:RemoveTransientTaskById(TaskId)
	self.cppMgr:RemoveTransientTaskById(TaskId)
end

---@public
function MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID(
		ParamName, MaterialInstID, StartVal, EndVal, Duration, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddLinearSampleParamByDynamicMaterialInstanceID(
			ParamName, MaterialInstID, StartVal, EndVal, Duration, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceIDs(
		ParamName, MaterialInstIDs, StartVal, EndVal, Duration, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddLinearSampleParamByDynamicMaterialInstanceIDs(
			ParamName, MaterialInstIDs, StartVal, EndVal, Duration, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddVectorLinearSampleParamByDynamicMaterialInstanceID(
		ParamName, MaterialInstID, StartR, StartG, StartB, StartA, EndR, EndG, EndB, EndA, Duration, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddVectorLinearSampleParamByDynamicMaterialInstanceID(
			ParamName, MaterialInstID, StartR, StartG, StartB, StartA, EndR, EndG, EndB, EndA, Duration, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddVectorLinearSampleParamByDynamicMaterialInstanceIDs(
		ParamName, MaterialInstIDs, StartR, StartG, StartB, StartA, EndR, EndG, EndB, EndA, Duration, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddVectorLinearSampleParamByDynamicMaterialInstanceIDs(
			ParamName, MaterialInstIDs, StartR, StartG, StartB, StartA, EndR, EndG, EndB, EndA, Duration, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddCurveParamByCurveIDAndDynamicMaterialInstanceID(
		ParamName, MaterialInstanceID, CurveObjectID, bNeedRemap, RemapTime, bLoop, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddCurveParamByCurveIDAndDynamicMaterialInstanceID(
			ParamName, MaterialInstanceID, CurveObjectID, bNeedRemap, RemapTime, bLoop, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddCurveParamByCurveIDAndDynamicMaterialInstanceIDs(
		ParamName, MaterialInstanceIDs, CurveObjectID, bNeedRemap, RemapTime, bLoop, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddCurveParamByCurveIDAndDynamicMaterialInstanceIDs(
			ParamName, MaterialInstanceIDs, CurveObjectID, bNeedRemap, RemapTime, bLoop, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

---@param bNeedRemap boolean 很多情况下, 为了避免频繁调整曲线数据, 通常曲线都是用的归一化的时间配置, 整体时长都通过外部逻辑来控制, 如果对应的Curve是归一化的时间控制
--- 这里bNeedRemap给true, RemapTime为最后总的时长
---@param CurvePath string Curve资产路径
function MaterialManager:AddCurveParamByDynamicMaterialInstanceID(
		ParamName, MaterialInstanceID, CurvePath, bNeedRemap, RemapTime, bLoop, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddCurveParamByDynamicMaterialInstanceID(
			ParamName, MaterialInstanceID, CurvePath, bNeedRemap, RemapTime, bLoop, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddCurveParamByDynamicMaterialInstanceIDs(
		ParamName, MaterialInstanceIDs, CurvePath, bNeedRemap, RemapTime, bLoop, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddCurveParamByDynamicMaterialInstanceIDs(
			ParamName, MaterialInstanceIDs, CurvePath, bNeedRemap, RemapTime, bLoop, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddMPCLinearSampleParamTask(ParamName, MPCAssetPath, StartVal, EndVal, Duration, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddMPCLinearSampleParamTask(ParamName, MPCAssetPath, StartVal, EndVal, Duration, 
			bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddMPCActorLocationTask(ParamName, MPCAssetPath, ActorId, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddMPCActorLocationTask(
			ParamName, MPCAssetPath, ActorId, bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:AddMPCCurveParamTask(ParamName, MPCAssetPath, CurvePath, bNeedRemap, RemapTime, bLoop, bRevertToDefaultValueOnTaskEnd)
	return self.cppMgr:AddMPCCurveParamTask(
			ParamName, MPCAssetPath, CurvePath, bNeedRemap, RemapTime, bLoop, bRevertToDefaultValueOnTaskEnd ~= nil and bRevertToDefaultValueOnTaskEnd or false)
end

function MaterialManager:RemoveMaterialParamUpdateTask(TaskID)
	return self.cppMgr:RemoveMaterialParamUpdateTask(TaskID)
end

function MaterialManager:SetCameraDitherDistanceScaleDebugUsage(NewDistanceScale)
	self.cppMgr:KAPI_Material_SetCameraDitherDistanceScale(NewDistanceScale)
end

function MaterialManager:SetCameraDitherDefaultOLMPathDebugUsage(bUseDefaultMaterial)
	self.cppMgr:KAPI_Material_SetCameraDitherUseDefaultOLM(bUseDefaultMaterial)
end

--endregion MaterialParam


--region RefreshMaterialAndParamCache

---@public 全量刷新当前所有材质缓存 注意, 要所有的component操作完成以后再刷新
function MaterialManager:RefreshAllMaterialCaches(InActorId)
	if InActorId == nil or InActorId == 0 then
		return
	end
	
	self.cppMgr:RefreshAllMaterialCaches(InActorId)
end

function MaterialManager:InitMaterialCacheUseMaterialOnComponent(InActorId)
	if InActorId == nil or InActorId == 0 then
		return
	end

	self.cppMgr:InitMaterialCacheUseMaterialOnComponent(InActorId)
end

--endregion RefreshMaterialAndParamCache


--region Debug
---@public
---return TArray<FString>
---debug调试用 暂不考虑性能
function MaterialManager:GetMaterialCacheDebugInfosByActorId(InActorId)
	return self.cppMgr:GetMaterialCacheDebugInfos(InActorId)
end

---@public
---return TArray<FString>
---debug调试用 暂不考虑性能
function MaterialManager:GetMaterialParamDebugInfosByActorId(InActorId)
	return self.cppMgr:GetMaterialParamDebugInfos(InActorId)
end

--endregion Debug


--region Private
---@private
function MaterialManager:GenerateChangeMaterialSeqId()
	return self.cppMgr:GenerateReqId()
end

--endregion Private

return MaterialManager
