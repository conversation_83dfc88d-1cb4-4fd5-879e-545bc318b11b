
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local ParallelBehaviorControlConst = kg_require("Shared.Const.ParallelBehaviorControlConst")
local LOCO_GROUP_STATE_CONST = ParallelBehaviorControlConst.LOCO_GROUP_STATE_CONST
local EMovePosture = ParallelBehaviorControlConst.EMovePosture
local PlayerPostureToSpeed = ParallelBehaviorControlConst.PLAYER_POSTURE_TO_SPEED
local PlayerPostureSpeedBound = ParallelBehaviorControlConst.PLAYER_POSTURE_SPEED_BOUND
-- local LOCO_ANIM_STATE_CONST = ParallelBehaviorControlConst.LOCO_ANIM_STATE_CONST

local const = kg_require("Shared.Const")
local NIAGARA_EFFECT_TAG = const.NIAGARA_EFFECT_TAG

local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local LocoStateTypeToAnimStateNameMap = ViewControlConst.LocoStateTypeToAnimStateNameMap
-- local CollisionConst = kg_require("Shared.Const.CollisionConst")

local EPropertyClass = import("EPropertyClass")
-- local ECollisionResponse = import("ECollisionResponse")
local EInputThrusterFullSpeedAdjustMode = import("EInputThrusterFullSpeedAdjustMode")



--region Important
function ComplexLocomotionControlComponent:InitLocoGroupCallback()
    if Game.me == self then
        -- self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.NormalWalking] = ComplexLocomotionControlComponent.OnLeaveLocoGroupNormalWalkingForP1
        self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.Ride] = ComplexLocomotionControlComponent.OnLeaveLocoGroupRideForP1
        self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.WaterWalk] = ComplexLocomotionControlComponent.OnLeaveLocoGroupWaterWalkForP1
		self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.DizzinessWalk] = ComplexLocomotionControlComponent.OnLeaveLocoGroupDizzinessWalkForP1
		self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.RidePassenger] = ComplexLocomotionControlComponent.OnLeaveLocoGroupRidePassengerForP1
		self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.CameraDirMove] = ComplexLocomotionControlComponent.OnLeaveCameraDirMoveForP1

        self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.NormalWalking] = ComplexLocomotionControlComponent.OnEnterLocoGroupNormalWalkingForP1
        self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.Ride] = ComplexLocomotionControlComponent.OnEnterLocoGroupRideForP1
        self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.WaterWalk] = ComplexLocomotionControlComponent.OnEnterLocoGroupWaterWalkForP1
		self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.DizzinessWalk] = ComplexLocomotionControlComponent.OnEnterLocoGroupDizzinessWalkForP1
		self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.RidePassenger] = ComplexLocomotionControlComponent.OnEnterLocoGroupRidePassengerForP1
		self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.CameraDirMove] = ComplexLocomotionControlComponent.OnEnterCameraDirMoveForP1

        -- self.MovePostureChangedFunc[LOCO_GROUP_STATE_CONST.NormalWalking] = ComplexLocomotionControlComponent.OnMovePostureChangedForNormalWalkingForP1
        self.MovePostureChangedFunc[LOCO_GROUP_STATE_CONST.Ride] = ComplexLocomotionControlComponent.OnMovePostureChangedForRideForP1

        self.bInRevertLocoGroupProcedure = false
    else
        -- self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.NormalWalking] = ComplexLocomotionControlComponent.OnLeaveLocoGroupNormalWalkingForP3
        self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.Ride] = ComplexLocomotionControlComponent.OnLeaveLocoGroupRideForP3
        self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.WaterWalk] = ComplexLocomotionControlComponent.OnLeaveLocoGroupWaterWalkForP3
		-- self.LeaveLocoGroupFunc[LOCO_GROUP_STATE_CONST.DizzinessWalk] = ComplexLocomotionControlComponent.OnLeaveLocoGroupDizzinessWalkForP3

        -- self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.NormalWalking] = ComplexLocomotionControlComponent.OnEnterLocoGroupNormalWalkingForP3
        self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.Ride] = ComplexLocomotionControlComponent.OnEnterLocoGroupRideForP3
        self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.WaterWalk] = ComplexLocomotionControlComponent.OnEnterLocoGroupWaterWalkForP3
		-- self.EnterLocoGroupFunc[LOCO_GROUP_STATE_CONST.DizzinessWalk] = ComplexLocomotionControlComponent.OnEnterLocoGroupDizzinessWalkForP3
    end
end

function ComplexLocomotionControlComponent:UnInitLocoGroupCallback()
    table.clear(self.LeaveLocoGroupFunc)
    table.clear(self.EnterLocoGroupFunc)
    table.clear(self.MovePostureChangedFunc)
end

function ComplexLocomotionControlComponent:InitLocoGroupControl()
    self.OldLocoGroupInChanging = nil

    self.LeaveLocoGroupFunc = {}
    self.EnterLocoGroupFunc = {}

    self.MovePostureChangedFunc = {}
end
--endregion Important


--region API
-- LocomotionGroup大状态切换
-- 离开当前LocoGroup，InNewLocoGroup可以指定目标LocoGroup，为nil就使用AutoCalculateNewLocoGroup计算预期LocoGroup
-- 仅P1使用，P3在Set_LocoGroup里ReceiveLocoGroupChanged即可
function ComplexLocomotionControlComponent:LeaveCurLocoGroupAndEnterNew(InNewLocoGroup, bNeedBlendOutLocoState, bNeedBlendInLocoState, bNoCrossfade)
    local OldLocoGroup = self.LocoGroup
    local NewLocoGroup = InNewLocoGroup or self:AutoCalculateNewLocoGroup(OldLocoGroup)

    if OldLocoGroup == NewLocoGroup or Game.me ~= self then
        return
    end

    local LocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(NewLocoGroup)
    if LocoGroupInfo == nil then
        -- 怀疑是xml不匹配导致总是有错误的LocoGroup，不再抛错处理
        -- DebugLogError(string.format("LeaveCurLocoGroupAndEnterNew Error! OldLocoGroup:%i, NewLocoGroup:%i", self.LocoGroup, NewLocoGroup))
        return
    end

    self.OldLocoGroupInChanging = self.LocoGroup
    self.LocoGroup = NewLocoGroup

    if not self.bInRevertLocoGroupProcedure then
        -- 处于LocoGroup的回退逻辑中，不需要再上报服务器
        local reqParams = self:_AssembleLocoGroupChangeParams()
        self.remote.ReqSetLocoGroup(self.LocoGroup, reqParams)
        self.LastLocoGroupLoc = self:GetPosition()
    end

    local NewLocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(NewLocoGroup)
    local OldLocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(OldLocoGroup)

    bNeedBlendOutLocoState = bNeedBlendOutLocoState and OldLocoGroupInfo.BlendOutLocoState > 0
    bNeedBlendInLocoState = bNeedBlendInLocoState and NewLocoGroupInfo.BlendInLocoState > 0

    local LeaveFunc = self.LeaveLocoGroupFunc[OldLocoGroup]
    if LeaveFunc ~= nil then
        LeaveFunc(self, NewLocoGroup)
    end

    self:SetLocoGroupInfo(OldLocoGroup, NewLocoGroup)
    self:ChangeMovePosture(self.DefaultMovePosture)
    self:PrepareSpeedConfigForMove(NewLocoGroup)

    local EnterFunc = self.EnterLocoGroupFunc[NewLocoGroup]
    if EnterFunc ~= nil then
        EnterFunc(self, OldLocoGroup)
    end

    if bNoCrossfade == true then
        self:SetAnimLocoGroupInfo(NewLocoGroup, false)
    else
        if bNeedBlendOutLocoState then
            self:LocomotionCrossfadeInFixedTime(LocoStateTypeToAnimStateNameMap[OldLocoGroupInfo.BlendOutLocoState], 0)
            self:SetAnimLocoGroupInfo(NewLocoGroup, bNeedBlendInLocoState)
        else
            if bNeedBlendInLocoState then
                self:LocomotionCrossfadeInFixedTime(LocoStateTypeToAnimStateNameMap[NewLocoGroupInfo.BlendInLocoState], 0)
            else
                self:LocomotionCrossfadeInFixedTime(LocoStateTypeToAnimStateNameMap[NewLocoGroupInfo.DefaultLocoState], 0)
            end

            self:SetAnimLocoGroupInfo(NewLocoGroup, false)
        end

        self.CppEntity:KAPI_Movement_ClearInputThrusterFadeOutEffect()
    end

    self.OldLocoGroupInChanging = nil
end

-- Todo：根据地形或其他条件，自动选择对应的LocoGroup
function ComplexLocomotionControlComponent:AutoCalculateNewLocoGroup(InCurLocoGroup)
    return LOCO_GROUP_STATE_CONST.NormalWalking
end

function ComplexLocomotionControlComponent:RevertLocoGroup(InCorrectLocoroup)
    -- 这块LocoGroup的回退功能需要注意，对于水面移动/游泳等客户端先行的状态回退可以通过这个控制
    -- 对于骑行这类由服务器下行控制的状态，不应该再走到这里
    if self.LocoGroup ~= InCorrectLocoroup then
        self.bInRevertLocoGroupProcedure = true
        self:LeaveCurLocoGroupAndEnterNew(InCorrectLocoroup, false, false, true)
        
        self:ResetToDefaultLocoState()
        self:SetPosition(self.LastLocoGroupLoc)
        self.bInRevertLocoGroupProcedure = false
    end
end
--endregion API



--region Receive
function ComplexLocomotionControlComponent:ReceiveLocoGroupChanged(InNewLocoGroup, InOldLocoGroup)
    local LeaveFunc = self.LeaveLocoGroupFunc[InOldLocoGroup]
    if LeaveFunc ~= nil then
        LeaveFunc(self, InNewLocoGroup)
    end

    self:SetLocoGroupInfo(InOldLocoGroup, InNewLocoGroup)
    self:SetAnimLocoGroupInfo(InNewLocoGroup, false)

    local EnterFunc = self.EnterLocoGroupFunc[InNewLocoGroup]
    if EnterFunc ~= nil then
        EnterFunc(self, InOldLocoGroup)
    end
end

function ComplexLocomotionControlComponent:ReceiveMovePostureChanged(InOldMovePosture, InNewMovePosture)
    self:PredictSpeedByMovePosture(InOldMovePosture, InNewMovePosture, self.OldLocoGroupInChanging)

    local MPCF = self.MovePostureChangedFunc[self.LocoGroup]
    if MPCF ~= nil then
        MPCF(self, InOldMovePosture, InNewMovePosture)
    end
end
--endregion Receive



--region Private
function ComplexLocomotionControlComponent:SetLocoGroupInfo(InOldLocoGroup, InNewLocoGroup)
    if self.bInWorld == false then
        return 
    end
    
    local LocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(InNewLocoGroup)
    if LocoGroupInfo == nil then
        -- 怀疑是xml不匹配导致总是有错误的LocoGroup，不再抛错处理
        -- DebugLogError(string.format("SetLocoGroupInfo Error! OldLocoGroup:%i, NewLocoGroup:%i", InOldLocoGroup, InNewLocoGroup))
        return
    end
    self.DefaultLocoState = LocoGroupInfo.DefaultLocoState
    self.CppEntity:KAPI_Movement_SetDefaultLocoState(self.DefaultLocoState)
    self.DefaultMovePosture = EMovePosture.Run
end

function ComplexLocomotionControlComponent:PrepareSpeedConfigForMove(InNewLocoGroup)
	if not self.isAvatar then
		LocomotionControlComponent.PrepareSpeedConfigForMove(self, InNewLocoGroup)
		return
	end
	
	if PlayerPostureToSpeed[InNewLocoGroup] == nil or PlayerPostureSpeedBound[InNewLocoGroup] == nil then
        return
    end

    local WalkSpeed = nil
    local WalkToRunSpeedBound = nil
    local RunSpeed = nil
    local RunToSprintSpeedBound = nil
    local SprintSpeed = nil

    WalkSpeed = PlayerPostureToSpeed[InNewLocoGroup][EMovePosture.Walk]
    WalkToRunSpeedBound = PlayerPostureSpeedBound[InNewLocoGroup][1]
    RunSpeed = PlayerPostureToSpeed[InNewLocoGroup][EMovePosture.Run]
    RunToSprintSpeedBound = PlayerPostureSpeedBound[InNewLocoGroup][2]
    SprintSpeed =PlayerPostureToSpeed[InNewLocoGroup][EMovePosture.Sprint]

    self.WalkSpeed = WalkSpeed
    self.WalkToRunSpeedBound = WalkToRunSpeedBound
    self.RunSpeed = RunSpeed
    self.RunToSprintSpeedBound = RunToSprintSpeedBound
    self.SprintSpeed = SprintSpeed

    local SpeedStages = slua.Array(EPropertyClass.Float)
    SpeedStages:Add(WalkSpeed)
    SpeedStages:Add(RunSpeed)
    SpeedStages:Add(SprintSpeed)
    local MovePostures = slua.Array(EPropertyClass.Float)
    MovePostures:Add(EMovePosture.Walk)
    MovePostures:Add(EMovePosture.Run)
    MovePostures:Add(EMovePosture.Sprint)
    self.CppEntity:KAPI_Movement_InitSpeedStagesAndPosture(SpeedStages, MovePostures)
    
    self:OnLogicSpeedChange(self.GetSpeed and self:GetSpeed() or WalkSpeed)
end

function ComplexLocomotionControlComponent:_AssembleLocoGroupChangeParams()
    if self.LocoGroup == LOCO_GROUP_STATE_CONST.WaterWalk then
        -- 向服务器上报水面的高度，服务器需要水面高度数据控制托管时的离地高度
        local CurDistToWater = self.CppEntity:KAPI_Movement_GetCurDistToWaterSurface()
        local CurLocation = self:GetPosition()
        local CapsuleHalfHeight = self.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()
        local WaterHeight = CurLocation.Z - CapsuleHalfHeight - CurDistToWater
        --self.remote.ReqSetWaterLevelHeight(CurLocation.Z - CapsuleHalfHeight - CurDistToWater)
        -- Log.Debug("[szk]OnEnterLocoGroupWaterWalkForP1", CurDistToWater, CurLocation.Z, CapsuleHalfHeight, CurLocation.Z - CapsuleHalfHeight - CurDistToWater)
        return { WaterHeight = WaterHeight }
    else
        return nil
    end
end
--endregion Private



--region P1Callback
-- function ComplexLocomotionControlComponent:OnLeaveLocoGroupNormalWalkingForP1(InNewLocoGroup)

-- end

function ComplexLocomotionControlComponent:OnLeaveLocoGroupRideForP1(InNewLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.InputAdditive)
end

function ComplexLocomotionControlComponent:OnLeaveLocoGroupWaterWalkForP1(InNewLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.InputAdditive)
    self.CppEntity:KAPI_Movement_SetNeedNotifyIsWaterWalkAllowed(false)

    self:DestroyWalterWalkNiagara()

    self:SCRemove(Enum.EStateConflictAction.InWaterWalk)
end

function ComplexLocomotionControlComponent:OnLeaveLocoGroupDizzinessWalkForP1(InNewLocoGroup)
	self:DisableLocoJump(Enum.ELocoControlTag.DizzinessLocoMove, false, true)
	self:DisableLocoDodge(Enum.ELocoControlTag.DizzinessLocoMove, false, true)
end

function ComplexLocomotionControlComponent:OnLeaveLocoGroupRidePassengerForP1(InNewLocoGroup)
	self:SetNeedLocoAnimStateChangedNotifyFromLocomotionSM(false)
end

function ComplexLocomotionControlComponent:OnLeaveCameraDirMoveForP1(InNewLocoGroup)
	--self:SetNeedLocoAnimStateChangedNotifyFromLocomotionSM(false)
end

function ComplexLocomotionControlComponent:OnEnterLocoGroupNormalWalkingForP1(InOldLocoGroup)
    self:UpdateMultiJumpInfo()
end

function ComplexLocomotionControlComponent:OnEnterLocoGroupRideForP1(InOldLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.AdjustOrientation)
    self:UpdateMultiJumpInfo()
end

function ComplexLocomotionControlComponent:OnEnterLocoGroupWaterWalkForP1(InOldLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.AdjustOrientation)
    self:UpdateMultiJumpInfo()
    self.CppEntity:KAPI_Movement_SetNeedNotifyIsWaterWalkAllowed(true)

    self:PlayWalterWalkNiagara()
    
    self:SCExecute(Enum.EStateConflictAction.InWaterWalk)
end

function ComplexLocomotionControlComponent:OnEnterLocoGroupDizzinessWalkForP1(InOldLocoGroup)
	self:DisableLocoJump(Enum.ELocoControlTag.DizzinessLocoMove, true, true)
	self:DisableLocoDodge(Enum.ELocoControlTag.DizzinessLocoMove, true, true)
end


function ComplexLocomotionControlComponent:OnEnterLocoGroupRidePassengerForP1(InOldLocoGroup)
	self:SetNeedLocoAnimStateChangedNotifyFromLocomotionSM(true)
end

function ComplexLocomotionControlComponent:OnEnterCameraDirMoveForP1(InOldLocoGroup)
	--self:SetNeedLocoAnimStateChangedNotifyFromLocomotionSM(true)
end

function ComplexLocomotionControlComponent:OnMovePostureChangedForRideForP1(InOldMovePosture, InNewMovePosture)
	-- 如果开启了 LocoInputControl 则更新自身转向速率
	if self.isOnGroundUsingYawController then
		local curAnimStateData = self:GetAnimStateData(self.LocoAnimState)
		local InterpValue = self:GetYawInterpValue(curAnimStateData, InNewMovePosture)
		
		self.CppEntity:KAPI_Movement_UpdateLocoYawControllerInterpValue(InterpValue)
	end
	
    self:OnMountCompReceiveMovePostureChanged(InNewMovePosture)
end
--endregion P1Callback



--region P3Callback
-- function ComplexLocomotionControlComponent:OnLeaveLocoGroupNormalWalkingForP3(InNewLocoGroup)

-- end

function ComplexLocomotionControlComponent:OnLeaveLocoGroupRideForP3(InNewLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.InputAdditive)
end

function ComplexLocomotionControlComponent:OnLeaveLocoGroupWaterWalkForP3(InNewLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.InputAdditive)

    self:DestroyWalterWalkNiagara()
end

-- function ComplexLocomotionControlComponent:OnLeaveLocoGroupDizzinessWalkForP3(InNewLocoGroup)
-- 	self:DisableLocoJump(Enum.ELocoControlTag.DizzinessLocoMove, false, true)
-- 	self:DisableLocoDodge(Enum.ELocoControlTag.DizzinessLocoMove, false, true)
-- end

-- function ComplexLocomotionControlComponent:OnEnterLocoGroupNormalWalkingForP3(InOldLocoGroup)

-- end

function ComplexLocomotionControlComponent:OnEnterLocoGroupRideForP3(InOldLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.AdjustOrientation)
end

function ComplexLocomotionControlComponent:OnEnterLocoGroupWaterWalkForP3(InOldLocoGroup)
    self.CppEntity:KAPI_Movement_SetLocoInputThrusterFullSpeedAdjustMode(EInputThrusterFullSpeedAdjustMode.AdjustOrientation)

    self:PlayWalterWalkNiagara()
end

-- function ComplexLocomotionControlComponent:OnEnterLocoGroupDizzinessWalkForP3(InOldLocoGroup)
-- 	self:DisableLocoJump(Enum.ELocoControlTag.DizzinessLocoMove, true, true)
-- 	self:DisableLocoDodge(Enum.ELocoControlTag.DizzinessLocoMove, true, true)
-- end

--endregion P3Callback



--region WaterWalk
function ComplexLocomotionControlComponent:StartWaterWalk()
    if self.LocoGroup ~= LOCO_GROUP_STATE_CONST.NormalWalking then
        -- 非NormalWalking状态不允许进入水面移动
        return false
    end

    if not self.CppEntity:KAPI_Movement_GetIsWaterWalkAllowed() or self:SCCanExecute(Enum.EStateConflictAction.InWaterWalk, true) == Enum.EStateConflictType.BLOCK then
        -- 未检测到水面，不允许进入水面移动
        -- 状态冲突不允许，则不进入水面移动
        return false
    end

    local CurDistToWater = self.CppEntity:KAPI_Movement_GetCurDistToWaterSurface()
    if CurDistToWater > 50.0 then
        -- 高于水面过远，不允许进入水面移动
        return false
    end

    self:LeaveCurLocoGroupAndEnterNew(LOCO_GROUP_STATE_CONST.WaterWalk, false, true)
    return true
end

function ComplexLocomotionControlComponent:StopWaterWalk(bNeedBlend)
    if self.LocoGroup ~= LOCO_GROUP_STATE_CONST.WaterWalk then
        return false
    end

    bNeedBlend = bNeedBlend == nil and true or bNeedBlend

    -- 非地面状态时，不走GetOut，由动画状态机来处理回到NormalWalking的衔接
    self:LeaveCurLocoGroupAndEnterNew(LOCO_GROUP_STATE_CONST.NormalWalking, bNeedBlend, false, not self.CppEntity:KAPI_Movement_GetHasLocoGroundSupport())

    return true
end

function ComplexLocomotionControlComponent:ForceStopWaterWalk()
    self:StopWaterWalk(false)
end

function ComplexLocomotionControlComponent:GetIsWaterWalk()
    return self.LocoGroup == LOCO_GROUP_STATE_CONST.WaterWalk
end

function ComplexLocomotionControlComponent:TempProtectionForWaterWalkWhenEnterWorld()
    -- 目前服务器无法判断水面，客户端下线后会将客户端强行拉入水底，这里做临时保护
    if self.LocoGroup == LOCO_GROUP_STATE_CONST.WaterWalk then
        if not self.CppEntity:KAPI_Movement_GetIsWaterDetected() or self.CppEntity:KAPI_Movement_GetCurDistToWaterSurface() < 0 then
            self:ForceStopWaterWalk()
        end
    end
end

function ComplexLocomotionControlComponent:PlayWalterWalkNiagara()
    if self.WaterWalkNiagaraEffectID == nil and self.LocoGroup == LOCO_GROUP_STATE_CONST.WaterWalk then
        local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
        NiagaraEffectParam.NiagaraEffectPath = ViewResourceConst.WATER_WALK_NIAGARA
        NiagaraEffectParam.bNeedAttach = true
        NiagaraEffectParam.AttachPointName = WATER_WALK_NIAGARA_SOCKET
        NiagaraEffectParam.EffectTags = { NIAGARA_EFFECT_TAG.WATER_MOVE }

        self.WaterWalkNiagaraEffectID = self:PlayNiagaraEffect(NiagaraEffectParam)
    end
end

function ComplexLocomotionControlComponent:DestroyWalterWalkNiagara()
    if self.WaterWalkNiagaraEffectID ~= nil then
        self:DeactivateNiagaraSystem(self.WaterWalkNiagaraEffectID)
        self.WaterWalkNiagaraEffectID = nil
    end
end
--endregion WaterWalk