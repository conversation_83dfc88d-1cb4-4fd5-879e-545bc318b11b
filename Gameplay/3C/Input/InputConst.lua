-- 技能槽位与输入Action映射
SkillSlotToAction = SkillSlotToAction or
{
	[ETE.EBSSkillSlot.SS_Attack] = "ACT_A_Action",
	[ETE.EBSSkillSlot.SS_Slot01] = "Skill_01_Action",
	[ETE.EBSSkillSlot.SS_Slot02] = "Skill_02_Action",
	[ETE.EBSSkillSlot.SS_Slot03] = "Skill_03_Action",
	[ETE.EBSSkillSlot.SS_Slot04] = "Skill_04_Action",
	[ETE.EBSSkillSlot.SS_Slot05] = "Skill_05_Action",
	[ETE.EBSSkillSlot.SS_Slot06] = "Skill_06_Action",
	[ETE.EBSSkillSlot.SS_DeControlSlot] = "DeControl_Action",
	[ETE.EBSSkillSlot.SS_FellowSlot1] = "FellowSkill_01_Action",
	[ETE.EBSSkillSlot.SS_FellowSlot2] = "FellowSkill_02_Action",
	[ETE.EBSSkillSlot.SS_ExtraordinarySlot] = "ExtraordinarySkill_Action",
	[ETE.EBSSkillSlot.SS_Slot11] = "SpecialSkill_01_Action"
}

-- 需要屏蔽的引擎快捷键
NeedBlockEngineShortcut = NeedBlockEngineShortcut or
{
	"F1",
	"F2",
	"F3",
	"F4",
	"F5",
	"F6",
	"F7",
	"F8",
}

-- 引擎快捷键与Action映射
EngineShortcutToActionName = EngineShortcutToActionName or
{
	F1 = "OpenDailyPanel_Action",
	F2 = "OpenSheetPanel_Action",
	F3 = "OpenShopPanel_Action",
	F4 = "OpenSocialActionPanel_Action",
}