local StateBase = require "Gameplay.3C.Interactor.InnerState.InteractiveStateBase"
local StringConst = require "Data.Config.StringConst.StringConst"

local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")
local EActionBlendRule = ViewAnimConst.EActionBlendRule

-- 对于EP的简单交互以及通用交互物的简单交互来说，交互者目前都使用 Interacting State
-- Interacting State处理交互动作、交互条、交互打断等逻辑, 并支持交互表现恢复的机制

InteractiveState_Interacting = DefineClass("InteractiveState_Interacting", StateBase)
-- 成功时离开的参数
InteractiveState_Interacting._SuccessTable = { bSuccess = true }
-- 保持动画离开的参数
InteractiveState_Interacting._RemainInteractAnimLeaveParams = { bSuccess = false, bRemainInteractAnim = true }

--@override
function InteractiveState_Interacting:InitDefaults()
    self.InteractTime = 0
    self.bNeedStopActionAnim = false
    self.bWeakInteractive = false
    self.UITemplateID = nil
    self.InteractorInstID = nil
    self.InteractStartAnimFeature = nil
    self.ProgressBarTitle = nil

    self.bInteractCommonInteractor = false
end

--@override
function InteractiveState_Interacting:OnEnter(Params, OldStatePassParams)
    self.InteractTime = Params.InteractTime
    self.bWeakInteractive = Params.InteractTime <= 0

    self.UITemplateID = Params.UITemplateID
    self.InteractorInstID = Params.InteractorInstID
    self.InteractStartAnimFeature = Params.InteractStartAnimFeature
    self.bInteractCommonInteractor = Params.bInteractCommonInteractor
    self.ProgressBarTitle = Params.ProgressBarTitle

    -- 交互动画、读条、转身表现
    self:StartInteractPerformance(self.InteractTime)
    -- 弱交互直接退出
    if self.bWeakInteractive then
        self:OnLeave(InteractiveState_Interacting._SuccessTable)
    end
end

--@override
function InteractiveState_Interacting:OnLeave(Params)
    self:EndInteractPerformance(Params and Params.bSuccess, Params and Params.bRemainInteractAnim)
end

function InteractiveState_Interacting:StartInteractPerformance(RemainTime)
    local MyEntity = self.StateMachine:GetEntity()
    local AnimAssetID

    Game.EventSystem:PublishBehavior(_G.EEventTypes.INTERACTIVE_PERFORMANCE_START, MyEntity.eid)

    -- 交互物读条
    local TemplateData = self.UITemplateID and Game.TableData.GetInteractorUITemplateRow(self.UITemplateID) or nil
    local ProgressBarTitle = self.ProgressBarTitle
    if TemplateData ~= nil then
        AnimAssetID = TemplateData.AnimAssetID

        if ProgressBarTitle == nil then
            ProgressBarTitle = TemplateData.ProgressBarTitle
        end
    end

    if self.StateMachine:IsMainPlayer() then
        if RemainTime > 0 and UI.IsShow("P_HUDBaseView") then
            UI.ShowUI(UICellConfig.HUDStatusBar, {
                Title = StringConst.Get(ProgressBarTitle),
                Duration = RemainTime
            })
        end
    end

    if self.InteractStartAnimFeature ~= nil then
        if self.isAvatar then
            self:ResetToDefaultLocoState(0)
        end
        
        -- 采集物和通用交互物目前都使用外部指定anim feature的形式播放交互动作
        MyEntity:PlayAnimLibMontage(self.InteractStartAnimFeature[1], self.InteractStartAnimFeature[2], nil, nil, nil, true)

    elseif AnimAssetID ~= nil then
        local BodyMask = EActionBlendRule.FullBodyOverride
        if self.bWeakInteractive then
            BodyMask = EActionBlendRule.PartBodyOverride
            self.bNeedStopActionAnim = false
        else
            self.bNeedStopActionAnim = true
        end
        MyEntity:PlayAnimLibMontageCustomTag(ViewAnimConst.EAnimPlayReqTag.Performance, AnimAssetID, nil, false, nil, nil, BodyMask, nil, nil, true)
    end
end

function InteractiveState_Interacting:EndInteractPerformance(bSuccess, bRemainInteractAnim)
    Log.DebugFormat("InteractiveState_Interacting:EndInteractPerformance bSuccess:%s, bRemainInteractAnim:%s, bNeedStopActionAnim:%s", bSuccess, bRemainInteractAnim, self.bNeedStopActionAnim)
    local MyEntity = self.StateMachine:GetEntity()
    if MyEntity and not bRemainInteractAnim then
        if self.InteractStartAnimFeature ~= nil then
            --if not bSuccess or self.bStopInteractStartAnimOnExit then
            --    MyEntity:StopAnimLibMontage()
            --end
            MyEntity:StopAnimLibMontage()
        else
            if self.bNeedStopActionAnim then
                self.Entity:StopAnimLibMontage()
                self.bNeedStopActionAnim = false
            end
        end
    end
    
    if self.StateMachine:IsMainPlayer() then
		UI.HideUI(UICellConfig.HUDStatusBar)
    end
end

--@override
function InteractiveState_Interacting:OnAddListener()
    Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.LSCENEACTOR_ON_DESTROY, self, self.OnInteractorDestroyed, self.InteractorInstID)

    if self.StateMachine:IsMainPlayer() then
        Game.EventSystem:AddListener(_G.EEventTypes.STATE_CONFLICT_CANCEL_INTERACT, self, self.OnStateConflictCancelEvent)
    end
end

--@override
function InteractiveState_Interacting:OnRemoveListener()
    Game.EventSystem:RemoveObjListeners(self)
end

function InteractiveState_Interacting:OnStateConflictCancelEvent()
    self:CancelInteract()
end

function InteractiveState_Interacting:CancelInteract()
    if self.bInteractCommonInteractor then
        Game.me:ReqCancelCommonInteract(self.InteractorInstID)
    else
        Game.me:ReqCancelInteract()
    end
end

function InteractiveState_Interacting:OnInteractorDestroyed()
    self:OnLeave(InteractiveState_Interacting._RemainInteractAnimLeaveParams)
end

return InteractiveState_Interacting