
InteractiveState_None = DefineClass("InteractiveState_None")

--@override
function InteractiveState_None.Enter(Owner, Params, OldStatePassParams)
    if OldStatePassParams then
        if OldStatePassParams.bFinishPerformance then
            if Owner.CurDirectorID ~= nil then
                Owner:FinishInteractivePerformance(true)
            end
            Owner:StopAnimLibMontage()
        end

        if OldStatePassParams.ContinuousInsID and OldStatePassParams.ContinuousInsID ~= "" then
            local SceneEntity = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(OldStatePassParams.ContinuousInsID)
            if SceneEntity then
                if Owner == Game.me then
                    SceneEntity:OnMainPlayerInteractiveFinish(OldStatePassParams.ContinuousInsID)
                end
                SceneEntity:OnEnterInteractiveFinish()
            end
        end
    end
end

function InteractiveState_None.Leave(Owner, Params)

end

return InteractiveState_None