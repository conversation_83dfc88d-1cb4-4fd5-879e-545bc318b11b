local camera_mode_base = kg_require("Gameplay.3C.Camera.LuaCameraMode.CameraModeBase")

--3rd脸部特写
ThirdFaceCameraMode = DefineClass("ThirdFaceCameraMode", camera_mode_base)

function ThirdFaceCameraMode:GetBoundEntityID()
    return Game.me and Game.me.eid or nil
end

function ThirdFaceCameraMode:GetInitRotByManualControl(LastCameraMode)
    local AttachEntity = Game.me
    if not AttachEntity.CppEntity:KAPI_Movement_IsInDefaultLocoState() then
        if LastCameraMode and LastCameraMode:GetCameraSavedRotation() then
            return LastCameraMode:GetCameraSavedRotation(), camera_mode_base.ZeroRotate
        end
    end

    return camera_mode_base.HalfTurnRotate, Game.me:GetRotation()
end

function ThirdFaceCameraMode:OnActivateMode()
    local CameraID = self:GetCameraActorID()
    Game.CameraManager:KAPI_Camera_EnableHideMeshOnOverlap(CameraID, true)

    Game.CharacterLightManager:ActivateCharacterLight()
end

function ThirdFaceCameraMode:OnDeActivateMode()
    local CameraID = self:GetCameraActorID()
    Game.CameraManager:KAPI_Camera_EnableHideMeshOnOverlap(CameraID, false)

    Game.CharacterLightManager:DeactiveCharacterLight()
end

function ThirdFaceCameraMode:OnBindEvent()
    Game.CameraManager:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Mode, self.CameraModeTag, self, self.Zoom_Axis)
end

function ThirdFaceCameraMode:OnUnBindEvent()
    Game.EventSystem:RemoveObjListeners(self)
    Game.CameraManager:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Mode, self.CameraModeTag)
end

function ThirdFaceCameraMode:Zoom_Axis(value)
    if not self.bActivate or value == 0 then
        return
    end

    local CameraManager = Game.CameraManager
    if CameraManager:IsBlending() then
        return
    end

    local CurArmLen = self:GetCameraZoomLen()
    local ResultStep = -self.ZoomStep * value + CurArmLen
    local _, ZoomMax = self:GetCameraZoomClamp()

    if CurArmLen >= ZoomMax and ResultStep > ZoomMax  then
        Game.CameraManager:EnableThirdFaceCamera(false)
    else
        self:SetCameraZoomLen(ResultStep)
    end
end

return ThirdFaceCameraMode
