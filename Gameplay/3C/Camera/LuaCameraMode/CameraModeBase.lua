local CollisionConst = kg_require("Shared.Const.CollisionConst")
local camera_blackboard = kg_require("Gameplay.3C.Camera.CameraBlackboard")
local EInitArmLenOperation = import("EInitArmLenOperation")
local EInitRotationOperation = import("EInitRotationOperation")
local ECameraEaseFunction = import("ECameraEaseFunction")
local ECameraViewBlendFunction = import("ECameraViewBlendFunction")
local TableData = Game.TableData

--相机mode基类

---@CLASS CameraModeBase
CameraModeBase = DefineClass("CameraModeBase")
-- (0, 0, 0)复用Table
CameraModeBase.InitCameraOffsetToActor = CameraModeBase.InitCameraOffsetToActor or {X = 0, Y = 0, Z = 0}
-- (0, 0, 0)旋转
CameraModeBase.ZeroRotate = CameraModeBase.ZeroRotate or FRotator(0, 0, 0)
-- (0, 180, 0)旋转
CameraModeBase.HalfTurnRotate = CameraModeBase.HalfTurnRotate or FRotator(0, 180, 0)

function CameraModeBase:Initialize()
end

function CameraModeBase:LuaInit(CameraModeTag, UECameraMode)
    self.UECameraMode = UECameraMode
    UECameraMode:LuaInit(self)

    self.bActivate = false
	self.bNeedTraceCheck = true

    self.CameraModeTag = CameraModeTag

	self.CameraModePriority = TableData.GetCameraModesConfigDataRow(CameraModeTag).Priority
	
    self.LastRotation = nil
    self.LastArmLen = nil

    self.needBoneOffset = false

	self.CameraModeConfig = nil

	self.CameraCurvePathToID = {}

	self.SavedRotation = nil
	self.SavedArmLen = nil

	self.CurCameraID = KG_INVALID_ID
	
	self:Initialize()
end

function CameraModeBase:UnInit()
    self.CurCamera = nil
end

function CameraModeBase:GetCameraModeTag()
    return self.CameraModeTag
end

function CameraModeBase:GetCameraModeID()
	local CameraModeActorID = self.CameraModeActorID
	if not CameraModeActorID then
		CameraModeActorID = self.UECameraMode:GetCameraModeID()
		self.CameraModeActorID = CameraModeActorID
	end
	return CameraModeActorID
end

function CameraModeBase:GetCameraActorID()
	return self.CurCameraID
end

function CameraModeBase:SetCameraActorID(CameraActorID)
	self.CurCameraID = CameraActorID
	self.UECameraMode:SetCurCameraByID(CameraActorID)
end

function CameraModeBase:GetCamera()
    return self.UECameraMode.CurCameraPtr
end

-- 激活Mode检测
function CameraModeBase:CanActivateMode(CurActivateModeTag, ...)
    local MainPlayer = Game.me
    if not MainPlayer or not IsValidID(MainPlayer.CharacterID)  then
        return false
    end

    return Game.CameraManager ~= nil and CurActivateModeTag ~= self.CameraModeTag
end

-- 新进入状态是调用，为激活mode准备，相关变量设置
-- 这个阶段不要设置与单位实时表现相关(位置朝向等)的参数，以为模式可能需要异步加载资源后正式激活
function CameraModeBase:BeforeActivateMode(...)
	self._bOverrideBlendOutParams = false
	self._BlendOutOverrideTargetModes = nil
	self._BlendOutOverride_BlendTime = nil
	self._BlendOutOverride_EaseFunc = nil
	self._BlendOutOverride_BlendFunc = nil
	self._BlendOutOverride_bLockOutgoing = nil
	self._BlendOutOverride_CameraBlendCurve = nil

	self._bOverrideBlendInParams = false
	self._BlendInOverride_BlendTime = nil
	self._BlendInOverride_EaseFunc = nil
	self._BlendInOverride_BlendFunc = nil
	self._BlendInOverride_bLockOutgoing = nil
	self._BlendInOverride_CameraBlendCurve = nil

    self:OnBeforeActivateMode(...)
end

function CameraModeBase:OnBeforeActivateMode(...)
end

-- 视角激活
function CameraModeBase:ActivateMode(LastCameraMode, bNotSwitchView)
	local CameraMgr = Game.CameraManager
	local CameraID = self:GetCameraActorID()
	local CameraModeID = self:GetCameraModeID()
	if not IsValidID(CameraID) then
		self:SetCameraActorID(Game.CameraManager:KAPI_Camera_GetOrCreateCamera(self:GetCameraBelongToSetting() or self.CameraModeTag, self:GetCameraTypeSetting()))
		CameraID = self:GetCameraActorID()
	end

	self.bActivate = true
	CameraMgr:KAPI_Camera_SetActiveCameraModeTag(self.CameraModeTag)
	CameraMgr:KAPI_Camera_MarkModeActivate(CameraModeID, true)

	CameraMgr:KAPI_Camera_EnableCameraTick(CameraID, true)

	self:RefreshControlData(LastCameraMode)

	if not bNotSwitchView then
		self:BindEvent()
	end

    self:OnActivateMode()

	CameraMgr:KAPI_Camera_ForceUpdate(CameraModeID, self.bNeedTraceCheck)

	if not bNotSwitchView then
		if LastCameraMode then
			if LastCameraMode:IsNeedOverrideNextCameraBlendInParams(self.CameraModeTag) then
				CameraMgr:KAPI_Camera_SetViewTargetWithBlendCustomByID(CameraID, LastCameraMode:GetBlendOutParams(self.CameraModeTag))
			else
				CameraMgr:KAPI_Camera_SetViewTargetWithBlendCustomByID(CameraID, self:GetBlendInParams(LastCameraMode.CameraModeTag))
			end
		else
			CameraMgr:KAPI_Camera_SetViewTargetWithBlendCustomByID(CameraID, self:GetBlendInParams())
		end
	end

	-- 默认退出相机模式时会关闭gaze,进入新的相机模式的时候检查一下 Gaze 臂长开启条件
	self:UpdateGazeByArmLength()
end

function CameraModeBase:SaveCameraData()
	local CameraManager = Game.CameraManager
	if not CameraManager then
		return
	end

	local CameraID = self:GetCameraActorID()
    self.SavedRotation = CameraManager:KAPI_Camera_GetCameraRotation(CameraID)
	self.SavedArmLen = CameraManager:KAPI_Camera_GetCameraTargetZoomLen(CameraID)
end

-- 视角失效
function CameraModeBase:DeActivateMode(...)
    if not self.bActivate then
        return
    end

    local CameraMgr = Game.CameraManager

    self:SaveCameraData()

	if self.bInGaze then
		if Game.me then
			Game.me:UnGaze(Enum.EGazeTypeMap.ROLE_SHOW)
			self.bInGaze = false
		end
	end

	if self.PPID  then
		Game.PostProcessManager:StopPostProcessPreset(self.PPID, CameraMgr:GetCurrentBlendTime())
		self.PPID = nil
	end

    self:UnBindEvent()

    self:OnDeActivateMode(...)

	self.bActivate = false
	CameraMgr:KAPI_Camera_EnableCameraTick(self:GetCameraActorID(), false)
	CameraMgr:KAPI_Camera_MarkModeActivate(self:GetCameraModeID(), false)
end

function CameraModeBase:ModeExit()
	-- 这里不能删除Blend参数 因为可能会给下一个视角的模式使用
	self:SetCameraActorID(KG_INVALID_ID)
	self.LastCameraMode = nil
	self.PrepareID = nil
	self.bInGaze = nil
	table.clear(self.CameraCurvePathToID)
	self:ResetCameraMode()
end

-- luacheck: push ignore
function CameraModeBase:RefreshControlData(LastCameraMode)
	local CameraManager = Game.CameraManager
	local CameraModeID = self:GetCameraModeID()

	-- Manager层面
	local ViewPitchMin, ViewPitchMax, ViewYawMin, ViewYawMax = self:GetCameraViewLimitSetting()
	CameraManager:KAPI_Camera_SetViewAngleSetting(CameraModeID, ViewPitchMin, ViewPitchMax, ViewYawMin, ViewYawMax, -179.999, 180, 0)

	local PitchSensitivity, YawSensitivity = self:GetCameraRotateSensitivitySetting()
	CameraManager:SetCameraSensitivity(YawSensitivity, PitchSensitivity)

	-- 基础相机配置模式
	local ZoomMin, ZoomMax, ZoomStep = self:GetCameraZoomSetting()
	self.ZoomStep = ZoomStep
	local FOV = self:GetCameraFOVSetting()
	local EaseFuncZoomLag, EaseParamZoomLag, EaseCurveIDZoomLag = self:GetCameraZoomLagSetting()
	local EaseFuncZLag, EaseParamZLag, EaseCurveIDZLag, SoftZoneRadiusZ = self:GetCameraZLagSetting()
	local EaseFuncXOYLag, EaseParamXOYLag, EaseCurveIDXOYLag, SoftZoneRadiusXOYLag = self:GetCameraXOYLagSetting()
	local ReleaseEaseFunc, ReleaseEaseParam, ReleaseCurveID = self:GetCameraCollisionReleaseSetting()
	local CompressEaseFunc, CompressEaseParam, CompressCurveID = self:GetCameraCollisionCompressSetting()
	local ScreenViewOffX, ScreenViewOffY = self:GetCameraViewOffsetSetting()
	local ScreenViewOffEaseFunc, ScreenViewOffEaseParam, ScreenViewOffEaseCurveID = self:GetCameraViewOffsetLagSetting()
	local bEnableDitherFade, DitherFadeDetectType, DitherFadeDetectParam = self:GetCameraDitherFadeSetting()
	local TargetLocationOffsetCurve = self:GetCameraTargetLocationOffsetCurveIDSetting()
	local FovCurve = self:GetCameraFOVCurveIDSetting()
	self.GazeEnableLen = self:GetCameraEnableGazeLen()
	
	local InitArmLen = self:GetCameraInitZoomSetting()
	local InitRot = self:GetCameraInitRotSetting()

	CameraManager:KAPI_Camera_RefreshControlData(
			CameraModeID, self.CameraModePriority,
			ZoomMin, ZoomMax, ZoomStep, FOV,
			EaseFuncZoomLag, EaseParamZoomLag, self:GetCurveIDFromCameraCurveTable(EaseCurveIDZoomLag),
			EaseFuncZLag, EaseParamZLag, self:GetCurveIDFromCameraCurveTable(EaseCurveIDZLag), SoftZoneRadiusZ,
			EaseFuncXOYLag, EaseParamXOYLag, self:GetCurveIDFromCameraCurveTable(EaseCurveIDXOYLag), SoftZoneRadiusXOYLag,
			ReleaseEaseFunc, ReleaseEaseParam, self:GetCurveIDFromCameraCurveTable(ReleaseCurveID),
			CompressEaseFunc, CompressEaseParam, self:GetCurveIDFromCameraCurveTable(CompressCurveID),
			ScreenViewOffX, ScreenViewOffY, ScreenViewOffEaseFunc, ScreenViewOffEaseParam, self:GetCurveIDFromCameraCurveTable(ScreenViewOffEaseCurveID),
			bEnableDitherFade, CollisionConst.QUERY_BY_OBJECTTYPES.CAMERA_DITHER_FADE, DitherFadeDetectType, DitherFadeDetectParam,
			InitArmLen, InitRot,
			YawSensitivity, PitchSensitivity, self:GetCurveIDFromCameraCurveTable(TargetLocationOffsetCurve), self:GetCurveIDFromCameraCurveTable(FovCurve)
	)

	local BoundEntity = Game.EntityManager:getEntity(self:GetBoundEntityID())
	local BoneName, BoneOffsetX, BoneOffsetY, BoneOffsetZ = self:GetCameraBoneSetting()
	CameraManager:KAPI_Camera_SetCameraLookAt(CameraModeID, BoundEntity and BoundEntity.CharacterID or KG_INVALID_ID, BoundEntity and BoundEntity.CharacterID or KG_INVALID_ID, BoneName, BoneOffsetX, BoneOffsetY, BoneOffsetZ)

	-- 初始位置设置
	local InitArmType, DirBlendType = self:GetCameraBlendArmAndRotConfigSetting(LastCameraMode and LastCameraMode.CameraModeTag or -1)
	local OverridePitch, OverrideYaw, OverrideZoom = self:GetCameraOverrideRotAndZoomSetting()
	local RotSpace = CameraModeBase.ZeroRotate
	local bSyncPCRot = BoundEntity ~= nil
	if OverrideZoom == nil then
		if InitArmType ~= EInitArmLenOperation.USE_MANUAL_CONTROL then
			if InitArmType == EInitArmLenOperation.REMAIN_LAST_EXIT_LEN then
				if self:GetCameraSavedArmLen() then
					InitArmLen = self:GetCameraSavedArmLen()
				end
			elseif InitArmType == EInitArmLenOperation.REMAIN_LAST_MODE_LEN then
				if LastCameraMode and LastCameraMode:GetCameraSavedArmLen() then
					InitArmLen = LastCameraMode:GetCameraSavedArmLen()
				end
			elseif InitArmType == EInitArmLenOperation.SET_TO_ZOOM_MAX_LEN then
				InitArmLen = ZoomMax
			elseif InitArmType == EInitArmLenOperation.SET_TO_ZOOM_MIN_LEN then
				InitArmLen = ZoomMin
			elseif InitArmType == EInitArmLenOperation.SET_TO_ZOOM_RANGE_MIDDLE then
				InitArmLen = (ZoomMin + ZoomMax) / 2
			end
		else
			InitArmLen = self:GetInitArmLenByManualControl(LastCameraMode)
		end
	else
		InitArmLen = OverrideZoom
	end

	if DirBlendType ~= EInitRotationOperation.USE_MANUAL_CONTROL then
		if DirBlendType == EInitRotationOperation.USE_MODE_INIT_ARM_ROT then
			if BoundEntity then
				RotSpace = BoundEntity:GetRotation()
				if OverridePitch then InitRot.Pitch = OverridePitch - RotSpace.Pitch end
				if OverrideYaw then InitRot.Yaw = OverrideYaw - RotSpace.Yaw end
			else
				if OverridePitch then InitRot.Pitch = OverridePitch end
				if OverrideYaw then InitRot.Yaw = OverrideYaw end
			end
		elseif DirBlendType == EInitRotationOperation.REMAIN_LAST_EXIT_ROT then
			if self:GetCameraSavedRotation() then
				InitRot = self:GetCameraSavedRotation()
			end
			if OverridePitch then InitRot.Pitch = OverridePitch end
			if OverrideYaw then InitRot.Yaw = OverrideYaw end
		elseif DirBlendType == EInitRotationOperation.REMAIN_LAST_MODE_ROT then
			if LastCameraMode and LastCameraMode:GetCameraSavedRotation() then
				InitRot = LastCameraMode:GetCameraSavedRotation()
			end
			if OverridePitch then InitRot.Pitch = OverridePitch end
			if OverrideYaw then InitRot.Yaw = OverrideYaw end
		elseif DirBlendType == EInitRotationOperation.SET_TO_TARGET_FRONT then
			if BoundEntity then
				InitRot = CameraModeBase.HalfTurnRotate
				RotSpace = BoundEntity:GetRotation()
				if OverridePitch then InitRot.Pitch = OverridePitch - RotSpace.Pitch end
				if OverrideYaw then InitRot.Yaw = OverrideYaw - RotSpace.Yaw end
			else
				if OverridePitch then InitRot.Pitch = OverridePitch end
				if OverrideYaw then InitRot.Yaw = OverrideYaw end
			end
		elseif DirBlendType == EInitRotationOperation.SET_TO_TARGET_BACK then
			if BoundEntity then
				InitRot = CameraModeBase.ZeroRotate
				RotSpace = BoundEntity:GetRotation()
				if OverridePitch then InitRot.Pitch = OverridePitch - RotSpace.Pitch end
				if OverrideYaw then InitRot.Yaw = OverrideYaw - RotSpace.Yaw end
			else
				if OverridePitch then InitRot.Pitch = OverridePitch end
				if OverrideYaw then InitRot.Yaw = OverrideYaw end
			end
		end
	else
		InitRot, RotSpace = self:GetInitRotByManualControl(LastCameraMode)
		if OverridePitch then InitRot.Pitch = OverridePitch - RotSpace.Pitch end
		if OverrideYaw then InitRot.Yaw = OverrideYaw - RotSpace.Yaw end
	end

	CameraManager:KAPI_Camera_SetInitArmLenAndDir(CameraModeID, InitArmLen, InitRot, RotSpace, bSyncPCRot)

	--移除前一个ModePP效果
	if self.PPID  then
		Game.PostProcessManager:StopPostProcessPreset(self.PPID, CameraManager:GetCurrentBlendTime())
		self.PPID = nil
	end

	local CameraEffectID = self:GetCameraPPSetting()
	if CameraEffectID and CameraEffectID > 0 then
		self.PPID = Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.Camera, 3, CameraEffectID, CameraManager:GetCurrentBlendTime(), 0, -1)
	end

	-- 应用Setting层面配置
	CameraManager:ApplySettingToCameraModeWhenActivate(self)
	
	-- 刷新
	CameraManager:KAPI_Camera_SyncModeViewLimitSetting(CameraModeID)
	CameraManager:KAPI_Camera_ForceUpdate(CameraModeID, self.bNeedTraceCheck)
end
-- luacheck: pop

function CameraModeBase:GetInitRotByManualControl(LastCameraMode)
	return self:GetCameraInitRotSetting(), CameraModeBase.ZeroRotate
end

function CameraModeBase:GetInitArmLenByManualControl(LastCameraMode)
	return self:GetCameraInitZoomSetting()
end

function CameraModeBase:GetBoundEntityID()
	return nil
end

function CameraModeBase:BindEvent()
    Log.DebugFormat("CameraModeBase:BindEvent Camera Mode Tag:%s", self.CameraModeTag)
    self:OnBindEvent()
end

function CameraModeBase:UnBindEvent()
    Log.DebugFormat("CameraModeBase:UnBindEvent Camera Mode Tag:%s", self.CameraModeTag)
    self:OnUnBindEvent()
end

-- 绑定输入事件
function CameraModeBase:OnBindEvent()

end

-- 解绑输入事件
function CameraModeBase:OnUnBindEvent()

end

function CameraModeBase:OnModeUpdate(Delta)
end

function CameraModeBase:ResetCameraMode()
    Game.CameraManager:KAPI_Camera_ResetCamera(self:GetCameraModeID())
end

-- 视角激活所做操作，须覆写
function CameraModeBase:OnActivateMode()

end

-- 视角丢失所做操作，须覆写
function CameraModeBase:OnDeActivateMode()

end

function CameraModeBase:DeActivateModeKeepAlive()
	local CameraMgr = Game.CameraManager

	self:SaveCameraData()

	if self.bInGaze then
		if Game.me then
			Game.me:UnGaze(Enum.EGazeTypeMap.ROLE_SHOW)
			self.bInGaze = false
		end
	end

	if self.PPID  then
		Game.PostProcessManager:StopPostProcessPreset(self.PPID, CameraMgr:GetCurrentBlendTime())
		self.PPID = nil
	end

	self:UnBindEvent()
end


function CameraModeBase:SetCameraZoomLen(NewLen, bImmediate)
    local CameraID = self:GetCameraActorID()
    if not IsValidID(CameraID) then
        return
    end

	Game.CameraManager:KAPI_Camera_UpdateZoom(CameraID, NewLen, bImmediate or false)

    self:UpdateGazeByArmLength()
end

function CameraModeBase:UpdateGazeByArmLength()
	local CurArmLength = self:GetCameraZoomLen()

	local GazeEnableLen = self.GazeEnableLen
	if GazeEnableLen and GazeEnableLen > 0 and Game.me then
		if not self.bInGaze and CurArmLength <= GazeEnableLen then
			Game.me:StartGazeCamera(Enum.EGazeTypeMap.THIRD_CAMERA)
			self.bInGaze = true
		end

		if self.bInGaze and CurArmLength > GazeEnableLen then
			Game.me:UnGaze(Enum.EGazeTypeMap.THIRD_CAMERA)
			self.bInGaze = false
		end
	end
end

function CameraModeBase:GetCameraZoomLen()
	local CameraID = self:GetCameraActorID()
	if not IsValidID(CameraID) then
		return
	end

    return Game.CameraManager:KAPI_Camera_GetCameraTargetZoomLen(CameraID)
end

function CameraModeBase:GetCameraZoomClamp()
	local CameraModeID = self:GetCameraModeID()
	if not IsValidID(CameraModeID) then
		return 0, 0
	end

	local CameraManger = Game.CameraManager
	return CameraManger:KAPI_Camera_GetZoomMin(CameraModeID), CameraManger:KAPI_Camera_GetZoomMax(CameraModeID)
end

-------------------------------------region begin Blend---------------------------------

function CameraModeBase:OverrideBlendOutParams(TargetModes, BlendTime, EaseFunc, BlendFunc, bLockOutgoing, CameraBlendCurveID)
	self._bOverrideBlendOutParams = true

	self._BlendOutOverrideTargetModes = TargetModes
	self._BlendOutOverride_BlendTime = BlendTime
	self._BlendOutOverride_EaseFunc = EaseFunc
	self._BlendOutOverride_BlendBlendFunc = BlendFunc
	self._BlendOutOverride_bLockOutgoing = bLockOutgoing
	self._BlendOutOverride_CameraBlendCurveID = CameraBlendCurveID or KG_INVALID_ID
end

function CameraModeBase:OverrideBlendInParams(BlendTime, EaseFunc, BlendFunc, bLockOutgoing, CameraBlendCurveID)
	self._bOverrideBlendInParams = true

	self._BlendInOverride_BlendTime = BlendTime
	self._BlendInOverride_EaseFunc = EaseFunc
	self._BlendInOverride_BlendFunc = BlendFunc
	self._BlendInOverride_bLockOutgoing = bLockOutgoing
	self._BlendInOverride_CameraBlendCurveID = CameraBlendCurveID or KG_INVALID_ID
end

function CameraModeBase:IsNeedOverrideNextCameraBlendInParams(NexModeTag)
	if self._bOverrideBlendOutParams and self._BlendOutOverrideTargetModes ~= nil then
		return table.contains(self._BlendOutOverrideTargetModes, NexModeTag)
	end
	return false
end

-- 处理从上一个ModeBlendOut的参数
function CameraModeBase:GetBlendOutParams(NxtMode)
	if self._bOverrideBlendOutParams and self._BlendOutOverrideTargetModes and table.contains(self._BlendOutOverrideTargetModes, NxtMode) then
		return self._BlendOutOverride_BlendTime, self._BlendOutOverride_BlendFunc, self._BlendOutOverride_BlendFunc, self._BlendOutOverride_bLockOutgoing, self._BlendOutOverride_CameraBlendCurveID ~= nil, self._BlendOutOverride_CameraBlendCurveID
	end

	return 0, 0, 0, false, KG_INVALID_ID
end

---@return bool float BlendTime, ECameraEaseFunction EaseFunc, ECameraViewBlendFunction BlendFunc, bool bLockOutgoing, KGObjectID CameraBlendCurveID
function CameraModeBase:GetBlendInParams(LastModeTag)
	if self._bOverrideBlendInParams then
		return self._BlendInOverride_BlendTime, self._BlendInOverride_EaseFunc, self._BlendInOverride_BlendFunc, self._BlendInOverride_bLockOutgoing, self._BlendInOverride_CameraBlendCurveID or KG_INVALID_ID
	end

	if LastModeTag then
		local EaseFunc, EaseParam, CurveID, BlendFunc = self:GetCameraBlendConfigSetting(LastModeTag)
		return EaseParam, EaseFunc, BlendFunc, true, CurveID
	end

	return 0, 0, 0, true, KG_INVALID_ID
end

-----------------------------------------end region--------------------------------------

-----------------------------------------------CameraSavingData-------------------------------------
function CameraModeBase:GetCameraSavedRotation()
	return self.SavedRotation
end

function CameraModeBase:GetCameraSavedArmLen()
	return self.SavedArmLen
end

----------------------------------------------Camera Base Param--------------------------------------
function CameraModeBase:GetCameraReplaceSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.bReplaceSamePriority
end

function CameraModeBase:GetCameraAutoPopSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.bAutoPopStack
end

function CameraModeBase:GetCameraBelongToSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local ModeBelongTo = CameraModeConfig.ModeBelongTo
	if ModeBelongTo == 0 then
		return nil
	end
	return ModeBelongTo
end

-- false 普通相机 true Cine相机
function CameraModeBase:GetCameraTypeSetting()
	return false
end

function CameraModeBase:GetCameraZoomSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.ZoomMinLen, CameraModeConfig.ZoomMaxLen, CameraModeConfig.ZoomStep
end

function CameraModeBase:GetCameraFOVSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.DefaultFOV
end

function CameraModeBase:GetCameraRotateSensitivitySetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.PitchSensitivity, CameraModeConfig.YawSensitivity
end

function CameraModeBase:GetCameraViewLimitSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.ViewPitchMin, CameraModeConfig.ViewPitchMax, CameraModeConfig.ViewYawMin, CameraModeConfig.ViewYawMax
end

function CameraModeBase:GetCameraInitZoomSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.ZoomInitLen
end

function CameraModeBase:GetCameraInitRotSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CameraInitRot = CameraModeConfig.CameraInitRot
	return FRotator(CameraInitRot[1], CameraInitRot[2], CameraInitRot[3])
end

function CameraModeBase:GetCameraZLagSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CameraZLagParam = CameraModeConfig.CameraZLagParam
	return CameraZLagParam[1], CameraZLagParam[2], CameraZLagParam[3], CameraModeConfig.SoftZoneRadiusZ
end

function CameraModeBase:GetCameraXOYLagSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CameraXOYLagParam = CameraModeConfig.CameraXOYLagParam
	return CameraXOYLagParam[1], CameraXOYLagParam[2], CameraXOYLagParam[3], CameraModeConfig.SoftZoneRadiusXOY
end

function CameraModeBase:GetCameraZoomLagSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CameraZoomLagParam = CameraModeConfig.CameraZoomLagParam
	return CameraZoomLagParam[1], CameraZoomLagParam[2], CameraZoomLagParam[3]
end

function CameraModeBase:GetCameraCollisionReleaseSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CollisionSpringReleaseLagParam = CameraModeConfig.CollisionSpringReleaseLagParam
	return CollisionSpringReleaseLagParam[1], CollisionSpringReleaseLagParam[2], CollisionSpringReleaseLagParam[3]
end

function CameraModeBase:GetCameraCollisionCompressSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CollisionSpringCompressLagParam = CameraModeConfig.CollisionSpringCompressLagParam
	return CollisionSpringCompressLagParam[1], CollisionSpringCompressLagParam[2], CollisionSpringCompressLagParam[3]
end

function CameraModeBase:GetCameraEnableGazeLen()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.GazeEnableLen
end

function CameraModeBase:GetCameraViewOffsetLagSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CameraViewScreenOffsetParam = CameraModeConfig.CameraViewScreenOffsetParam
	return CameraViewScreenOffsetParam[1], CameraViewScreenOffsetParam[2], CameraViewScreenOffsetParam[3]
end

function CameraModeBase:GetCameraViewOffsetSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CameraViewScreenOffset = CameraModeConfig.CameraViewScreenOffset
	return CameraViewScreenOffset[1], CameraViewScreenOffset[2]
end

function CameraModeBase:GetCameraDitherFadeSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local DitherFadeDetectParams = CameraModeConfig.DitherFadeDetectParams
	return CameraModeConfig.bEnableDitherFade, DitherFadeDetectParams[1], DitherFadeDetectParams[2]
end

function CameraModeBase:GetCameraBoneSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	local CameraBoneOffset = CameraModeConfig.CameraBoneOffset
	return CameraModeConfig.CameraBoneName, CameraBoneOffset[1], CameraBoneOffset[2], CameraBoneOffset[3]
end

function CameraModeBase:GetCameraBlendConfigSetting(LastCameraModeTag)
	if LastCameraModeTag and LastCameraModeTag ~= -1 then
		local CameraBlendConfig = TableData.GetCameraMode2ModeConfigDataRow(LastCameraModeTag)[self.CameraModeTag]
		return CameraBlendConfig.EaseBlendConfig, CameraBlendConfig.EaseBlendParam, CameraBlendConfig.EaseBlendCurveID, CameraBlendConfig.RouteBlendConfig
	end
	return ECameraEaseFunction.Linear, 0, KG_INVALID_ID, ECameraViewBlendFunction.Polar
end

function CameraModeBase:GetCameraOverrideRotAndZoomSetting()
	local OverridePitch = nil
	local OverrideYaw = nil
	local OverrideZoom = nil

	local CameraModeModifierConfig = TableData.GetCameraModeModifierConfigDataRow(self.CameraModeTag)
	if CameraModeModifierConfig["OverrideSet"] then
		local Pitch, Yaw, Zoom = camera_blackboard.GetThirdOverrideSetting()
		camera_blackboard.SetThirdOverrideSetting(nil, nil, nil)
		if Pitch then OverridePitch = Pitch end
		if Yaw then OverrideYaw = Yaw end
		if Zoom then OverrideZoom = Zoom end
	end

	return OverridePitch, OverrideYaw, OverrideZoom
end

function CameraModeBase:GetCameraBlendArmAndRotConfigSetting(LastCameraModeTag)
	if LastCameraModeTag and LastCameraModeTag ~= -1 then
		local CameraBlendConfig = TableData.GetCameraMode2ModeConfigDataRow(LastCameraModeTag)[self.CameraModeTag]
		return CameraBlendConfig.ArmBlendConfig, CameraBlendConfig.DirBlendConfig
	end
	return EInitArmLenOperation.USE_MODE_CONFIG, EInitRotationOperation.USE_MODE_INIT_ARM_ROT
end

function CameraModeBase:GetCameraTargetLocationOffsetCurveIDSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.TargetLocationOffsetCurve or KG_INVALID_ID
end

function CameraModeBase:GetCameraFOVCurveIDSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.FOVCurve or KG_INVALID_ID
end

function CameraModeBase:GetCameraPPSetting()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.CameraPP
end
-------------------------------------Camera Asset-----------------------------------
function CameraModeBase:GetCurvesToLoad()
	local CameraModeConfig = TableData.GetCameraModesConfigDataRow(self.CameraModeTag)
	return CameraModeConfig.CurvesToLoad
end

function CameraModeBase:ModePrepare()
	local CurvesToLoad = self:GetCurvesToLoad()
	if ksbcnext(CurvesToLoad) ~= nil then
		local Result = {}
		for _, CurvePath in ksbcpairs(CurvesToLoad) do
			Result[#Result + 1] = CurvePath
		end
		self.LoadID = Game.CameraManager:AsyncLoadExternCurve(Result, self, "OnCurveLoaded")
		return false
	end

	return true
end

function CameraModeBase:SetPrepareID(PrepareID)
	self.PrepareID = PrepareID
end

function CameraModeBase:IsWait()
	return self.PrepareID ~= nil
end

function CameraModeBase:OnCurveLoaded(InLoadID, LoadObjIDs, ...)
	if self.LoadID == InLoadID then
		self.LoadID = nil
	end
	local CurvesToLoad = self:GetCurvesToLoad()
	local CameraCurvePathToID = self.CameraCurvePathToID
	if #CurvesToLoad == LoadObjIDs:Num() then
		for Index = 1, #CurvesToLoad do
			CameraCurvePathToID[CurvesToLoad[Index]] = LoadObjIDs:Get(Index - 1)
		end
	end

	self.UECameraMode:AddCurves(LoadObjIDs)

	local PrepareID = self.PrepareID
	self.PrepareID = nil
	Game.CameraManager:OnCameraModePrepared(self, self.CameraModeTag, PrepareID, ...)
end

function CameraModeBase:GetCurveID(CurvePath)
	if not CurvePath or IsStringNullOrEmpty(CurvePath) then
		return KG_INVALID_ID
	end

	local CameraCurvePathToID = self.CameraCurvePathToID
	if CameraCurvePathToID[CurvePath] then
		return CameraCurvePathToID[CurvePath]
	end
	return KG_INVALID_ID
end

-- 这里给的是相机曲线表里的Key
function CameraModeBase:GetCurveIDFromCameraCurveTable(CameraCurveID)
	if not CameraCurveID or CameraCurveID == 0 then
		return KG_INVALID_ID
	end

	local CameraCurveData = TableData.GetCameraCurveConfigDataRow(CameraCurveID)
	if not CameraCurveData or IsStringNullOrEmpty(CameraCurveData.CurveAsset) then
		return KG_INVALID_ID
	end

	local CurvePath = CameraCurveData.CurveAsset
	local CameraCurvePathToID = self.CameraCurvePathToID
	if CameraCurvePathToID[CurvePath] then
		return CameraCurvePathToID[CurvePath]
	end
	return KG_INVALID_ID
end


------------------------------------胶水层----------------------------
function CameraModeBase:EnableModeTick(bEnable)
	self.UECameraMode:EnableTick(bEnable)
end

function CameraModeBase:EnableNewArmCollision(bEnable)
	if bEnable == nil then
		return
	end

	local CameraID = self:GetCameraActorID()
	if CameraID then
		Game.CameraManager:KAPI_Camera_EnableCameraNewCollision(CameraID, bEnable, CollisionConst.QUERY_BY_OBJECTTYPES.CAMERA_NEW_COLLISION_TRACE)
	end
end

return CameraModeBase