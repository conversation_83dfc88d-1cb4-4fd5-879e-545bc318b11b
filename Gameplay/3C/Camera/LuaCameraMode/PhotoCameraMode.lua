local RelativeTransformSpace = import("ERelativeTransformSpace")
local ECameraEaseFunction = import("ECameraEaseFunction")
local ECameraViewBlendFunction = import("ECameraViewBlendFunction")
local camera_mode_base = kg_require("Gameplay.3C.Camera.LuaCameraMode.CameraModeBase")

--第三人称摄像机模�?
PhotoCameraMode = DefineClass("PhotoCameraMode", camera_mode_base)

PhotoCameraMode.BindActorLocOffset = PhotoCameraMode.BindActorLocOffset or FVector(0,0, Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_FOV_BIND_LOCATION_OFFSET_Z"))

PhotoCameraMode.PhotoCameraAnimInitLoc = PhotoCameraMode.PhotoCameraAnimInitLoc or FVector(0, 0, 0)

function PhotoCameraMode:Initialize()
    self.MoveUpMax = 0
    self.MoveRightMax = 0
    self.MoveStepSize = 0

    self.FirstPersonFOVDeltaMax = 0
    self.FirstPersonFOVDeltaMin = 0
    self.FOVDeltaStepSize = 0
    self.FOVBlendSpeed = 0

    self.DebugPrintCamera = false
    self.DebugZoomLen = nil

    self.OverrideZoomMax = nil
    self.OverrideZoomMin = nil
    self.OverrideZoomStep = nil

    self.TargetFOVDelta = 0

    self.bPlayingAnim = false
end

function PhotoCameraMode:EnableFirstPerson(bEnable)
    if not self.bActivate then
        return
    end

    local CameraID = self:GetCameraActorID()
    if not IsValidID(CameraID) then
        return
    end

    local bActivate = self.bActivate
    local CameraManager = Game.CameraManager
    local CameraModeID = self:GetCameraModeID()
    self.bInFirstPerson = bEnable

    self:RevertPhotoCameraLookAt()
    if bEnable then
        if bActivate then
            CameraManager:KAPI_Camera_SetZoomSetting(CameraModeID, 0, 0, 0)
            self:SetCameraZoomLen(0, true)
        else
            self.OverrideZoomMax = 0
            self.OverrideZoomMin = 0
            self.OverrideZoomStep = 0
        end

        Game.PlayerController:SetControlRotation(FRotator(0.0, Game.me:GetRotation().Yaw, 0.0))
        Game.WorldManager:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Player, false)
        self:SetCameraManualFocus(10000)
    else
        self.OverrideZoomMax = nil
        self.OverrideZoomMin = nil
        self.OverrideZoomStep = nil

        local ZoomMin, ZoomMax, _ = self:GetCameraZoomSetting()
        CameraManager:KAPI_Camera_SetZoomSetting(CameraModeID, ZoomMin, ZoomMax, 0)
        self:SetCameraZoomLen(ZoomMax, true)

        self.TargetFOVDelta = 0

        Game.PlayerController:SetControlRotation(FRotator(0.0, Game.me:GetRotation().Yaw - 180, 0.0))
        CameraManager:KAPI_Camera_SetCameraSocketOffset(CameraID, 0, 0, 0)
        Game.WorldManager:SetEntityVisibleByCategory(Enum.EHideEntityCategory.Player, true)
    end

    CameraManager:KAPI_Camera_ForceUpdate(CameraModeID, true)
end

function PhotoCameraMode:OnActivateMode()
    self.MoveUpMax = Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_FOV_BIND_LOCATION_OFFSET_Z")
    self.MoveRightMax = Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_MOVE_RIGHT_MAX")
    self.MoveStepSize = Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_MOVE_STEP_SIZE")

    self.FirstPersonFOVDeltaMax =  Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_FOV_DELTA_MAX")
    self.FirstPersonFOVDeltaMin = Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_FOV_DELTA_MIN")
    self.FOVDeltaStepSize = Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_FOV_STEP_SIZE")
    self.FOVBlendSpeed = Game.TableData.GetCameraConstDataRow("PHOTO_CAMERA_FOV_BLEND_SPEED")

    Game.CharacterLightManager:ActivateCharacterLight()
    self.TargetFOVDelta = 0
    self.bInFirstPerson = false

    local CameraID = self:GetCameraActorID()
    Game.CameraManager:KAPI_Camera_EnableHideMeshOnOverlap(CameraID, true)
    Game.CameraManager:KAPI_Camera_SetCameraSocketOffset(CameraID, 0, 0, 0)

    self:SetCameraActorTrackFocus(Game.me.CharacterID, PhotoCameraMode.PhotoCameraAnimInitLoc)
end

function PhotoCameraMode:GetBoundEntityID()
    return Game.me and Game.me.eid or nil
end

function PhotoCameraMode:GetCameraTypeSetting()
    return true
end

function PhotoCameraMode:OnDeActivateMode()
    Game.CharacterLightManager:DeactiveCharacterLight()
    Game.CameraManager:KAPI_Camera_EnableHideMeshOnOverlap(self:GetCameraActorID(), false)

    local LastPlayCameraAnimToken = self.LastPlayCameraAnimToken
    if LastPlayCameraAnimToken then
        self:StopCameraAnimation(LastPlayCameraAnimToken, true)
    end
    
    Game.me:UnGaze(Enum.EGazeTypeMap.PHOTO_CAMERA)
end

function PhotoCameraMode:ModeExit()
    self.bPlayingAnim = false
    self.LastPlayCameraAnimToken = nil
    self.TargetFOVDelta = 0

    camera_mode_base.ModeExit(self)
end

function PhotoCameraMode:OnBindEvent()
    local CameraMgr = Game.CameraManager
    CameraMgr:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Mode, self.CameraModeTag, self, self.Zoom_Axis)
    CameraMgr:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.MoveForward_2_Axis, ECameraInputTag.Mode, self.CameraModeTag, self, self.MoveForward_Axis)
    CameraMgr:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.MoveRight_2_Axis, ECameraInputTag.Mode, self.CameraModeTag, self, self.MoveRight_Axis)
end

function PhotoCameraMode:OnUnBindEvent()
    local CameraMgr = Game.CameraManager
    CameraMgr:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Mode, self.CameraModeTag)
    CameraMgr:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.MoveForward_2_Axis, ECameraInputTag.Mode, self.CameraModeTag)
    CameraMgr:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.MoveRight_2_Axis, ECameraInputTag.Mode, self.CameraModeTag)
end

function PhotoCameraMode:MoveForward_Axis(value)
    if self.bPlayingAnim then
        return
    end

    local CameraID = self:GetCameraActorID()
    if not IsValidID(CameraID) then
        return
    end

    Game.CameraManager:KAPI_Camera_AddCameraSocketOffsetYWithClamp(CameraID, self.MoveStepSize * value, - self.MoveUpMax, self.MoveUpMax)
end

function PhotoCameraMode:MoveRight_Axis(value)
    if self.bPlayingAnim then
        return
    end

    local CameraID = self:GetCameraActorID()
    if not IsValidID(CameraID) then
        return
    end

    Game.CameraManager:KAPI_Camera_AddCameraSocketOffsetZWithClamp(CameraID, self.MoveStepSize * value, - self.MoveUpMax, self.MoveUpMax)
end

function PhotoCameraMode:Zoom_Axis(value)
    if not self.bActivate or value == 0 or self.bPlayingAnim then
        return nil
    end

    self:SetCameraZoomLen(-self.ZoomStep * value + self:GetCameraZoomLen())

    self.TargetFOVDelta = 0
end

function PhotoCameraMode:SetCameraFocusLength(NewValue)
    Game.CameraManager:KAPI_CineCamera_SetFocusLengthSetting(self:GetCameraActorID(), NewValue)
end

function PhotoCameraMode:SetCameraAperture(NewValue)
    Game.CameraManager:KAPI_CineCamera_SetApertureSetting(self:GetCameraActorID(), NewValue)
end

function PhotoCameraMode:SetCameraActorTrackFocus(TrackActorID, OffSet)
    Game.CameraManager:KAPI_CineCamera_SetActorTrackFocusSetting(self:GetCameraActorID(), TrackActorID, OffSet)
end

function PhotoCameraMode:SetCameraManualFocus(ManualFocusDistance)
    Game.CameraManager:KAPI_CineCamera_SetManualFocusSetting(self:GetCameraActorID(), ManualFocusDistance)
end

function PhotoCameraMode:PlayCameraAnimation(AnimSeqPath, BlendInTime, BlendOutTime, bLoop, PlayRate, BindEntity, CallBackObjWhenLoaded, CallBackFuncNameWhenLoaded)
    if not self.bActivate then
        return
    end

    if not BindEntity then
        return nil
    end

    PlayRate = PlayRate or 1
    bLoop = bLoop or false

    local PlayerTransForm = BindEntity.CppEntity:KAPI_SceneID_GetSocketTransform(BindEntity.CppEntity:KAPI_Actor_GetMainMesh(), "Root", RelativeTransformSpace.RTS_World)
    local Pos = PlayerTransForm:GetTranslation()
    --local Rot = Game.me:GetRotation()
    local Rot = PlayerTransForm:GetRotation():Rotator()
    local CameraAnimToken = Game.CameraManager:StartCameraAnimByPath(AnimSeqPath, BlendInTime, BlendOutTime, bLoop, BindEntity.CppEntity:KAPI_GetActorID(), Pos, Rot, nil, nil,
        function(ID)
            if self.LastPlayCameraAnimToken == ID then
                self.LastPlayCameraAnimToken = nil
            end
            self.bPlayingAnim = false
        end,
        CallBackObjWhenLoaded, CallBackFuncNameWhenLoaded
    )
    self.LastPlayCameraAnimToken = CameraAnimToken
    return CameraAnimToken
end

function PhotoCameraMode:StopCameraAnimation(CameraAnimToken, bImmediate)
    if not self.bActivate then
        return
    end

    if self.LastPlayCameraAnimToken ~= CameraAnimToken then
        return
    end

    if bImmediate then
        self.bPlayingAnim = false
        self.LastPlayCameraAnimToken = nil
    end
    
    Game.CameraManager:StopCameraAnimByToken(CameraAnimToken, bImmediate)
end

function PhotoCameraMode:SwitchPhotoCameraLookAt(LookAtEntity)
    if not self.bActivate then
        return
    end

    local CameraManager = Game.CameraManager
    local CameraID = self:GetCameraActorID()
    CameraManager:KAPI_Camera_SetCameraSocketOffset(CameraID, 0, 0, 0)
    CameraManager:KAPI_Camera_SetCameraLookAt(self:GetCameraModeID(), LookAtEntity.CharacterID, Game.me.CharacterID, "CAMERA_LOOK", 0, 0, 0)
    CameraManager:KAPI_Camera_SetViewTargetWithBlendCustomByID(CameraID, 0.5, ECameraEaseFunction.EaseOutCubic, ECameraViewBlendFunction.Polar, true, KG_INVALID_ID)
    self:SetCameraActorTrackFocus(LookAtEntity.CharacterID, PhotoCameraMode.PhotoCameraAnimInitLoc)
end

function PhotoCameraMode:RevertPhotoCameraLookAt()
    if not self.bActivate then
        return
    end
    local CameraManager = Game.CameraManager
    local CameraModeID = self:GetCameraModeID()
    local CameraID = self:GetCameraActorID()

    local CurrentLookAtActorID = CameraManager:KAPI_Camera_GetCameraLookAt(CameraModeID)
    local BoundEntity = Game.EntityManager:getEntity(self:GetBoundEntityID())

    if BoundEntity and BoundEntity.CharacterID ~= CurrentLookAtActorID then
        local BoneName, BoneOffsetX, BoneOffsetY, BoneOffsetZ = self:GetCameraBoneSetting()
        CameraManager:KAPI_Camera_SetCameraSocketOffset(CameraID, 0, 0, 0)
        CameraManager:KAPI_Camera_SetCameraLookAt(CameraModeID, BoundEntity and BoundEntity.CharacterID or KG_INVALID_ID, BoundEntity and BoundEntity.CharacterID or KG_INVALID_ID, BoneName, BoneOffsetX, BoneOffsetY, BoneOffsetZ)
        CameraManager:KAPI_Camera_SetViewTargetWithBlendCustomByID(CameraID, 0.5, ECameraEaseFunction.EaseOutCubic, ECameraViewBlendFunction.Polar, true, KG_INVALID_ID)
    end

    self:SetCameraActorTrackFocus(BoundEntity and BoundEntity.CharacterID or KG_INVALID_ID, PhotoCameraMode.PhotoCameraAnimInitLoc)
end

return PhotoCameraMode
