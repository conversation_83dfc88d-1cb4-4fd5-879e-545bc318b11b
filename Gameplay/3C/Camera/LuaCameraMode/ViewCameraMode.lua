local ECameraEaseFunction = import("ECameraEaseFunction")
local ECameraViewBlendFunction = import("ECameraViewBlendFunction")
local ERelativeTransformSpace = import("ERelativeTransformSpace")
local TableData = Game.TableData
local camera_mode_base = kg_require("Gameplay.3C.Camera.LuaCameraMode.CameraModeBase")

--系统3DView 摄像机模式
ViewCameraMode = DefineClass("ViewCameraMode", camera_mode_base)

function ViewCameraMode:CanActivateMode(CurActivateModeTag, CameraActorID, uiViewConfigID)
	return IsValidID(CameraActorID)
end

ViewCameraMode.HitResult = ViewCameraMode.HitResult or import("HitResult")()
ViewCameraMode.TargetRot = ViewCameraMode.TargetRot or FRotator(0, 0, 0)
function ViewCameraMode:OnBeforeActivateMode(CameraActorID, uiViewConfigID, blendTime)
	self:SetCameraActorID(CameraActorID)
	self.bZoomEnable = true
	self.bZoomAdsorption = false

	if uiViewConfigID ~= nil then
		local UICameraConfig = TableData.GetUICameraConfigDataRow(uiViewConfigID)
		if UICameraConfig then
			self.uiViewConfigID = UICameraConfig.ID
			self.bZoomAdsorption = UICameraConfig.bCustomZoom
		end
	end
	self.OverrideBlendTime = blendTime

	self.CachedCameraPos = Game.CameraManager:KAPI_Camera_GetCameraRootLocation(self:GetCameraActorID())
	self.CachedCameraRot = Game.CameraManager:KAPI_Camera_GetCameraRootRotation(self:GetCameraActorID())
end

function ViewCameraMode:GetBlendInParams(LastModeTag)
    local OverrideBlendTime = self.OverrideBlendTime
    if OverrideBlendTime then
        return OverrideBlendTime, ECameraEaseFunction.EaseOutCubic, ECameraViewBlendFunction.Polar, true, KG_INVALID_ID
    end

    return camera_mode_base.GetBlendInParams(self, LastModeTag)
end

function ViewCameraMode:GetCameraZoomSetting()
	local uiViewConfigID = self.uiViewConfigID
	if uiViewConfigID then
		local UICameraConfig = TableData.GetUICameraConfigDataRow(uiViewConfigID)
		if uiViewConfigID == Enum.EUICameraTypeEnum.UICamera_Fashion or uiViewConfigID == Enum.EUICameraTypeEnum.UICamera_SceneCustom then
			return 0, 0, UICameraConfig.ZoomStep
		end
		return UICameraConfig.MinZoom, UICameraConfig.MaxZoom, UICameraConfig.ZoomStep
	end
	return camera_mode_base.GetCameraZoomSetting(self)
end

function ViewCameraMode:GetCameraFOVSetting()
	local uiViewConfigID = self.uiViewConfigID
	if uiViewConfigID then
		local UICameraConfig = TableData.GetUICameraConfigDataRow(uiViewConfigID)
		return UICameraConfig.CameraDesiredFOV
	end

	local CameraActorID = self:GetCameraActorID()
	if IsValidID(CameraActorID) then
		return Game.CameraManager:KAPI_Camera_GetCameraFOV(CameraActorID)
	end

	return camera_mode_base.GetCameraFOVSetting(self)
end

function ViewCameraMode:GetCameraInitZoomSetting()
	local uiViewConfigID = self.uiViewConfigID
	if uiViewConfigID then
		local UICameraConfig = TableData.GetUICameraConfigDataRow(uiViewConfigID)
		return UICameraConfig.InitZoom
	end
	return camera_mode_base.GetCameraInitZoomSetting(self)
end

function ViewCameraMode:GetCameraViewOffsetSetting()
	local uiViewConfigID = self.uiViewConfigID
	if uiViewConfigID then
		local UICameraConfig = TableData.GetUICameraConfigDataRow(uiViewConfigID)
		return UICameraConfig.ScreenOffsetX, UICameraConfig.ScreenOffsetY
	end
	return camera_mode_base.GetCameraViewOffsetSetting(self)
end

function ViewCameraMode:GetCameraInitRotSetting()
	local uiViewConfigID = self.uiViewConfigID
	if uiViewConfigID then
		local UICameraConfig = TableData.GetUICameraConfigDataRow(uiViewConfigID)
		local ArmInitRotation = UICameraConfig.ArmInitRotation
		return FRotator(ArmInitRotation[1], ArmInitRotation[2], ArmInitRotation[3])
	end
	return Game.CameraManager:KAPI_Camera_GetCameraRootRotation(self:GetCameraActorID())
end

-- ViewCameraMode.AdsorptionBones = ViewCameraMode.AdsorptionBones or {"pelvis", "head", "neck_01", "hand_l", "hand_r", "spine_03", "clock_l_01", "clock_r_01", "calf_l", "calf_r", "foot_l", "foot_r"}
function ViewCameraMode:OnActivateMode()
	local CameraManager = Game.CameraManager
	--屏蔽相机效果
	CameraManager:SetEnableCameraModifier(false)

	local uiViewConfigID = self.uiViewConfigID
	if uiViewConfigID == Enum.EUICameraTypeEnum.UICamera_SceneCustom then
		self.RightOffsetMin = TableData.GetCameraConstDataRow("SITUATION_CAMERA_MOVE_LEFT_OFFSET_THRESHOLD")
		self.RightOffsetMax = TableData.GetCameraConstDataRow("SITUATION_CAMERA_MOVE_RIGHT_OFFSET_THRESHOLD")
		self.UpOffsetMin = TableData.GetCameraConstDataRow("SITUATION_CAMERA_MOVE_DOWN_OFFSET_THRESHOLD")
		self.UpOffsetMax = TableData.GetCameraConstDataRow("SITUATION_CAMERA_MOVE_UP_OFFSET_THRESHOLD")
	end
end

function ViewCameraMode:SetLookAtEntity(LookAt, ...)
    if LookAt == nil then
        return
    end
	
	self.LookAtEntityID = LookAt:uid()
    local uiViewConfigID = self.uiViewConfigID
    if uiViewConfigID == Enum.EUICameraTypeEnum.UICamera_Fashion then
        return self:SetLookAtEntityForFashion(LookAt, ...)
    end

	if uiViewConfigID == Enum.EUICameraTypeEnum.UICamera_Mount then
		return self:SetLookAtEntityForMount(LookAt, ...)
	end
	
    if uiViewConfigID == Enum.EUICameraTypeEnum.UICamera_SceneCustom then
        return self:SetLookAtEntityForSituation(LookAt, ...)
    end
end

function ViewCameraMode:OnDeActivateMode()
	local CameraManager = Game.CameraManager
	--取消屏蔽相机效果
	CameraManager:SetEnableCameraModifier(true)
	if self.uiViewConfigID == Enum.EUICameraTypeEnum.UICamera_Fashion then
		CameraManager:KAPI_Camera_CancelZoomAdsorption(self:GetCameraModeID())
	end
end

function ViewCameraMode:ModeExit()
	self.LookAtEntityID = nil
	camera_mode_base.ModeExit(self)
end

function ViewCameraMode:OnBindEvent()
	Game.CameraManager:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Mode, self.CameraModeTag, self, self.Zoom_Axis)
end

function ViewCameraMode:OnUnBindEvent()
	Game.CameraManager:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Mode, self.CameraModeTag)
end

function ViewCameraMode:Zoom_Axis(value)
	if not self.bZoomEnable then
		return
	end
	
	if not self.bActivate or value == 0 then
		return
	end

	if self.bZoomAdsorption then
		Game.CameraManager:KAPI_Camera_UpdateZoomWithPivotAdsorption(self:GetCameraModeID(), -self.ZoomStep * value + self:GetCameraZoomLen())
	else
		self:SetCameraZoomLen(-self.ZoomStep * value + self:GetCameraZoomLen())
	end
end

function ViewCameraMode:IsNeedOverrideNextCameraBlendInParams()
	return false
end

function ViewCameraMode:SetCameraZoomEnable(enable)
	self.bZoomEnable = enable
end

------------------------------------------------时装衣柜相机--------------------------------------------------------
function ViewCameraMode:SetLookAtEntityForFashion(LookAt, camPitch, camYaw, camRoll)
	local UICameraConfig = TableData.GetUICameraConfigDataRow(self.uiViewConfigID)
	local CameraManager = Game.CameraManager
	local CameraModeID = self:GetCameraModeID()
	local CameraID = self:GetCameraActorID()
	CameraManager:KAPI_Camera_CancelZoomAdsorption(CameraModeID)
	CameraManager:KAPI_Camera_SetZoomSetting(CameraModeID, UICameraConfig.MinZoom, UICameraConfig.MaxZoom, 0)
	CameraManager:KAPI_Camera_SetCameraLookAt(CameraModeID, LookAt.CharacterID, KG_INVALID_ID, UICameraConfig.CameraBindSlot, 0, 0, 0, ERelativeTransformSpace.RTS_Actor)
	CameraManager:KAPI_Camera_CancelZoomAdsorption(CameraModeID)
	CameraManager:KAPI_Camera_SetCameraSocketOffset(CameraID, 0, 0, 0)

	local rotator = nil
	if camPitch then
		rotator = FRotator(camPitch, camYaw, camRoll)
	else
		local CameraInitRot = UICameraConfig.ArmInitRotation
		rotator = FRotator(CameraInitRot[1], CameraInitRot[2], CameraInitRot[3])
	end
	
	CameraManager:KAPI_Camera_SetInitArmLenAndDir(CameraModeID, UICameraConfig.InitZoom, rotator, camera_mode_base.ZeroRotate, false)
	CameraManager:KAPI_Camera_ForceUpdate(CameraModeID, true)
	local CustomZoomParams = UICameraConfig.CustomZoomParams
	CameraManager:KAPI_Camera_SetZoomWithAdsorption(CameraModeID, CustomZoomParams[1], CustomZoomParams[2], CustomZoomParams[3], CustomZoomParams[4], ECameraEaseFunction.EaseInOutQuad)
end

-----------------------------------------------坐骑相机----------------------------------------------------------------
function ViewCameraMode:SetLookAtEntityForMount(LookAt, camPitch, camYaw, camRoll, initZoom, minZoom, maxZoom, customZoomParams, offset)
	local UICameraConfig = TableData.GetUICameraConfigDataRow(self.uiViewConfigID)
	local CameraManager = Game.CameraManager
	local CameraModeID = self:GetCameraModeID()
	local CameraID = self:GetCameraActorID()

	local x, y, z = 0, 0, 0
	if offset and ksbcnext(offset) then
		x, y, z = offset[1], offset[2], offset[3]
	end
	CameraManager:KAPI_Camera_SetZoomSetting(CameraModeID, minZoom or UICameraConfig.MinZoom, maxZoom or UICameraConfig.MaxZoom, 0)
	CameraManager:KAPI_Camera_SetCameraLookAt(CameraModeID, LookAt.CharacterID, KG_INVALID_ID, UICameraConfig.CameraBindSlot, x, y, z, ERelativeTransformSpace.RTS_Actor)
	CameraManager:KAPI_Camera_CancelZoomAdsorption(CameraModeID)
	CameraManager:KAPI_Camera_SetCameraSocketOffset(CameraID, 0, 0, 0)

	local rotator = nil
	if camPitch then
		rotator = FRotator(camPitch, camYaw, camRoll)
	else
		local CameraInitRot = UICameraConfig.ArmInitRotation
		rotator = FRotator(CameraInitRot[1], CameraInitRot[2], CameraInitRot[3])
	end

	CameraManager:KAPI_Camera_SetInitArmLenAndDir(CameraModeID, initZoom or UICameraConfig.InitZoom, rotator, camera_mode_base.ZeroRotate, false)
	CameraManager:KAPI_Camera_ForceUpdate(CameraModeID, true)

	local CameraConstGetFunc = TableData.GetCameraConstDataRow
	Game.CameraManager:KAPI_Camera_InitManualMove(self:GetCameraActorID(), false, true,
			CameraConstGetFunc("UI_MOUNT_CAMERA_VIEW_PITCH_THRESHOLD_MAX"),
			CameraConstGetFunc("UI_MOUNT_CAMERA_VIEW_PITCH_THRESHOLD_MIN"),
			CameraConstGetFunc("UI_MOUNT_CAMERA_VIEW_YAW_THRESHOLD_MIN"),
			CameraConstGetFunc("UI_MOUNT_CAMERA_VIEW_YAW_THRESHOLD_MAX")
	)
	self.CameraRotSpeed_Pitch_Mount = CameraConstGetFunc("UI_MOUNT_CAMERA_ROTATE_PITCH_SPEED")
	self.CameraRotSpeed_Yaw_Mount = CameraConstGetFunc("UI_MOUNT_CAMERA_ROTATE_YAW_SPEED")
end

function ViewCameraMode:SetAdsorptionParams(Param1, Param2, Param3, Param4)
	if self.uiViewConfigID ~= Enum.EUICameraTypeEnum.UICamera_Mount and self.uiViewConfigID ~= Enum.EUICameraTypeEnum.UICamera_Fashion then
		return
	end

	Game.CameraManager:KAPI_Camera_SetZoomWithAdsorption(self:GetCameraModeID(), Param1, Param2, Param3, Param4, ECameraEaseFunction.EaseInOutQuad)
end

-----------------------------------------------情境相机----------------------------------------------------------------
function ViewCameraMode:SetLookAtEntityForSituation(LookAt, CameraVirtualPivotPosX, CameraVirtualPivotPosY, CameraVirtualPivotPosZ, CameraRotationX, CameraRotationY, CameraRotationZ, CameraZoomLen)
	local UICameraConfig = TableData.GetUICameraConfigDataRow(self.uiViewConfigID)
	local CameraManager = Game.CameraManager
	local CameraModeID = self:GetCameraModeID()
	local CameraID = self:GetCameraActorID()

	CameraManager:KAPI_Camera_CancelZoomAdsorption(CameraModeID)
	CameraManager:KAPI_Camera_SetZoomSetting(CameraModeID, UICameraConfig.MinZoom, UICameraConfig.MaxZoom, 0)
	CameraManager:KAPI_Camera_SetCameraLookAtWithPivotLoc(CameraModeID, LookAt.CharacterID, KG_INVALID_ID, CameraVirtualPivotPosX, CameraVirtualPivotPosY, CameraVirtualPivotPosZ, CameraRotationX, CameraRotationY, CameraRotationZ, CameraZoomLen)
	CameraManager:KAPI_Camera_SetCameraSocketOffset(CameraID, 0, 0, 0)
	-- CameraManager:KAPI_Camera_InitRotationLagParam(CameraModeID, true, ECameraEaseFunction.Decay, 0.05, KG_INVALID_ID)
	self:SetSituationCameraEditorMode()
end

function ViewCameraMode:SetSituationCameraEditorMode()
	local CameraConstGetFunc = TableData.GetCameraConstDataRow
	Game.CameraManager:KAPI_Camera_InitManualMove(self:GetCameraActorID(), false, true,
			CameraConstGetFunc("SITUATION_CAMERA_VIEW_PITCH_THRESHOLD_MAX"),
			CameraConstGetFunc("SITUATION_CAMERA_VIEW_PITCH_THRESHOLD_MIN"),
			CameraConstGetFunc("SITUATION_CAMERA_VIEW_YAW_THRESHOLD_MIN"),
			CameraConstGetFunc("SITUATION_CAMERA_VIEW_YAW_THRESHOLD_Max")
	)
	self.CameraMoveSpeed_Situation = CameraConstGetFunc("SCENE_CUSTOM_CAMERA_MOVE_SPEED")
	self.CameraRotSpeed_Pitch_Situation = CameraConstGetFunc("SCENE_CUSTOM_CAMERA_ROTATE_PITCH_SPEED")
	self.CameraRotSpeed_Yaw_Situation = CameraConstGetFunc("SCENE_CUSTOM_CAMERA_ROTATE_YAW_SPEED")
end

function ViewCameraMode:LookAtMoveForSituation(selectEntityUID)
	if not self.bActivate then
		return
	end

	if selectEntityUID ~= self.LookAtEntityID then
		return
	end

	local Entity = Game.EntityManager:getEntity(selectEntityUID)
	Game.CameraManager:KAPI_Camera_AdjustTargetOffsetWhenLookAtMove(self:GetCameraActorID(), Entity:GetPosition())
end

function ViewCameraMode:CameraMoveForSituation_RightAxis(Value)
	if Value == 0 or not self.bActivate then
		return
	end

	local CameraID = self:GetCameraActorID()
	local CameraManager = Game.CameraManager
	local CurOffY = CameraManager:KAPI_Camera_GetCameraTargetOffsetY(CameraID)
	Value = Value * self.CameraMoveSpeed_Situation
	if Value > 0 then
		if CurOffY > self.RightOffsetMax then
			return
		end
		CameraManager:KAPI_Camera_AddCameraTargetOffsetYWithClamp(CameraID, Value, CurOffY, self.RightOffsetMax)
	else
		if CurOffY < self.RightOffsetMin then
			return
		end
		CameraManager:KAPI_Camera_AddCameraTargetOffsetYWithClamp(CameraID, Value, self.RightOffsetMin, CurOffY)
	end
end

function ViewCameraMode:CameraMoveForSituation_UpAxis(Value)
	if Value == 0 or not self.bActivate then
		return
	end

	local CameraID = self:GetCameraActorID()
	local CameraManager = Game.CameraManager
	local CurOffZ = CameraManager:KAPI_Camera_GetCameraTargetOffsetZ(CameraID)
	Value = Value * self.CameraMoveSpeed_Situation
	if Value > 0 then
		if CurOffZ > self.UpOffsetMax then
			return
		end
		CameraManager:KAPI_Camera_AddCameraTargetOffsetZWithClamp(CameraID, Value, CurOffZ, self.UpOffsetMax)
	else
		if CurOffZ < self.UpOffsetMin then
			return
		end
		CameraManager:KAPI_Camera_AddCameraTargetOffsetZWithClamp(CameraID, Value, self.UpOffsetMin, CurOffZ)
	end
end

function ViewCameraMode:CameraRotForSituation_LookUp(Value)
	if Value == 0 or not self.bActivate then
		return
	end

	local CamMgr = Game.CameraManager
	Value = Value * self.CameraRotSpeed_Pitch_Situation
	CamMgr:KAPI_Camera_LookUp(self:GetCameraActorID(), -Value)
end

function ViewCameraMode:CameraRotForSituation_Turn(Value)
	if Value == 0 or not self.bActivate then
		return
	end

	local CamMgr = Game.CameraManager
	Value = Value * self.CameraRotSpeed_Yaw_Situation
	CamMgr:KAPI_Camera_Turn(self:GetCameraActorID(), Value)
end

function ViewCameraMode:CameraRotForMount_LookUp(Value)
	if Value == 0 or not self.bActivate then
		return
	end

	local CamMgr = Game.CameraManager
	Value = Value * self.CameraRotSpeed_Pitch_Mount
	CamMgr:KAPI_Camera_LookUp(self:GetCameraActorID(), -Value)
end

function ViewCameraMode:CameraRotForMount_Turn(Value)
	if Value == 0 or not self.bActivate then
		return
	end

	local CamMgr = Game.CameraManager
	Value = Value * self.CameraRotSpeed_Yaw_Mount
	CamMgr:KAPI_Camera_Turn(self:GetCameraActorID(), Value)
end

function ViewCameraMode:GetSavedDataForSituation()
	local Zoom = self:GetCameraZoomLen()
	local CamMgr = Game.CameraManager
	local CameraActorID = self:GetCameraActorID()
	local CameraRot = CamMgr:KAPI_Camera_GetCameraRootRotation(CameraActorID)
	local PivotLoc = CamMgr:KAPI_Camera_GetCameraCachedArmOrigin(CameraActorID)
	return PivotLoc, CameraRot, Zoom
end

return ViewCameraMode
