local CameraModeBase = {}

function CameraModeBase:LuaInit(LuaCameraMode)
    self.LuaCameraMode = LuaCameraMode
    self.CurCameraMgr = Game.CameraManager
end

function CameraModeBase:SetLuaCameraMode(LuaCameraMode)
    self.LuaCameraMode = LuaCameraMode
end

function CameraModeBase:EnableTick(Enable)
    self.bEnableTick = Enable
end

function CameraModeBase:GetLuaCameraMode()
    return self.LuaCameraMode
end

function CameraModeBase:GetCameraModeID()
    return self:GetActorID()
end

function CameraModeBase:OnModeUpdate(DeltaTime)
    local LuaCameraMode = self.LuaCameraMode
    if LuaCameraMode then
        LuaCameraMode:OnModeUpdate(DeltaTime)
    end
end

return Class(nil,nil,CameraModeBase)