local ECameraEaseFunction = import("ECameraEaseFunction")
local TableData = Game.TableData
local ECameraModifierPriority = Enum.ECameraModifierPriority
local ECameraModifierToID = Enum.ECameraModifierToID
local CameraActionHandler = {}

-- Action类型 用于注册输入事件
CameraActionHandler.ActionTag = CameraActionHandler.ActionTag or {
    ActionHandler = 1,
    ParamOverride = 2,
    ParamAdditional = 3,
}

-- 可重复Action Map
CameraActionHandler.ActionMap = CameraActionHandler.ActionMap or {
    [CameraActionHandler.ActionTag.ParamOverride] = {},
    [CameraActionHandler.ActionTag.ParamAdditional] = {},
}

CameraActionHandler.ModifierEffectModeMap = CameraActionHandler.ModifierEffectModeMap or {
    
}

-- region Important
function CameraActionHandler:OnReceiveOnPossess()
    self.CameraManagerOwner:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.LookUp_Axis, ECameraInputTag.Action,
                                                   CameraActionHandler.ActionTag.ActionHandler, self,
                                                   self.OnLookUp_Axis)
    self.CameraManagerOwner:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.Turn_Axis, ECameraInputTag.Action,
                                                   CameraActionHandler.ActionTag.ActionHandler, self, self.OnTurn_Axis)
    self.CameraManagerOwner:RegisterInputAxisEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Action,
                                                   CameraActionHandler.ActionTag.ActionHandler, self, self.OnZoom_Axis)
    self.bCameraActionHandlerOnPoss = true
end

function CameraActionHandler:UnInitForOnUnPossess()
    if not self.bCameraActionHandlerOnPoss then
        return
    end

    self.CameraManagerOwner:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.Zoom_Axis, ECameraInputTag.Action,
                                                 CameraActionHandler.ActionTag.ActionHandler)
    self.CameraManagerOwner:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.Turn_Axis, ECameraInputTag.Action,
                                                 CameraActionHandler.ActionTag.ActionHandler)
    self.CameraManagerOwner:UnRegisterInputEvent(Enum.EInputTypeMouseGroup.LookUp_Axis, ECameraInputTag.Action,
                                                 CameraActionHandler.ActionTag.ActionHandler)
    self.bCameraActionHandlerOnPoss = false
end

function CameraActionHandler:clearActivateMode()
    table.clear(CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamOverride])
    table.clear(CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamAdditional])
    table.clear(CameraActionHandler.ActionManualControlMap)

    self.BattleCorrectCameraActionID = nil
    self.BattleTurnToLockCameraActionID = nil
    self.CameraLockActorForwardActionID = nil
    self.CameraLookAtActionID = nil
    self.CameraViewOffsetByRotActionID = nil
end

-- endregion Important


-- region CommonAPI
function CameraActionHandler:StopCameraAction(InActionID, bImmediate)
    if bImmediate == nil then bImmediate = true end
    self:KAPI_Camera_DisableCameraAction(InActionID, bImmediate)
end

function CameraActionHandler:CheckActionActivate(InActionID)
    if IsValidID(InActionID) then
        return self:KAPI_Camera_CheckCameraActionActivate(InActionID)
    end
    
    return false
end

function CameraActionHandler:OnActionAbort(UUID)
    local ParamAdditionalAction = CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamAdditional][UUID]
    if ParamAdditionalAction then
        self:StopCameraParamAdditionalAction(UUID)
        return
    end

    local ParamOverrideAction = CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamOverride][UUID]
    if ParamOverrideAction then
        self:StopCameraParamOverrideAction(UUID)
        return
    end
end
-- endregion CommonAPI


-- region Event
function CameraActionHandler:OnLookUp_Axis(value)
    if value and value > 0.001 or value < -0.001 then
        self:OnManalControl(1)
    end
end

function CameraActionHandler:OnTurn_Axis(value)
    if value and value > 0.001 or value < -0.001 then
        self:OnManalControl(1)
    end
end

function CameraActionHandler:OnZoom_Axis(value)
    if value and value > 0.001 or value < -0.001 then
        self:OnManalControl(0)
    end
end

function CameraActionHandler:OnThirdCameraDeActive()
end

-- endregion Event

----------------------------------------Base Modifier------------------------------------
function CameraActionHandler:GetModifierEffectCameraMode(ModifierEnum)
    local ModifierEffectModeMap = CameraActionHandler.ModifierEffectModeMap
    local EffectMode = ModifierEffectModeMap[ModifierEnum]
    if EffectMode then
        return EffectMode
    end

    local ModifierEffectModeTags = TableData.GetCameraModeModifierConfigDataRow(ModifierEnum)
    if ModifierEffectModeTags then
        EffectMode = {}
        for _, ModeTag in ksbcpairs(ModifierEffectModeTags) do
            EffectMode[#EffectMode + 1] = ModeTag
        end
    end

    ModifierEffectModeMap[ModifierEnum] = EffectMode
    return EffectMode
end

function CameraActionHandler:StartCameraRotateAction(CameraModeID, Pitch, Yaw, Roll, BlendInTime, BlendOutTime, DurationTime, BlendInType, BlendOutType, CurveInID, CurveOutID, bRecover, bCanInterrupt)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamRotSet)
    return self:KAPI_Camera_StartCameraRotateAction(CameraModeID, ECameraModifierPriority.CamRotSet, ModifierEffectModeTag, Pitch, Yaw, Roll, 0, 0, 0, bRecover, bCanInterrupt, BlendInTime, BlendOutTime, DurationTime, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraZoomAction(CameraModeID, Zoom, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID, bRecover, bCanInterrupt)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamZoomSet)
    return self:KAPI_Camera_StartCameraZoomAction(CameraModeID, ECameraModifierPriority.CamZoomSet, ModifierEffectModeTag, Zoom, 0, bRecover, bCanInterrupt, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraZoomOffsetAction(CameraModeID, ZoomOffSet, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID, bRecover, bCanInterrupt)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamZoomSet)
    return self:KAPI_Camera_StartCameraZoomAction(CameraModeID, ECameraModifierPriority.CamZoomSet, ModifierEffectModeTag, -1, ZoomOffSet, bRecover, bCanInterrupt, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraFOVAction(CameraModeID, FOV, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID, bRecover)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamFOVSet)
    return self:KAPI_Camera_StartCameraFOVAction(CameraModeID, ECameraModifierPriority.CamFOVSet, ModifierEffectModeTag, FOV, 0, bRecover, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraFOVOffsetAction(CameraModeID, FOVOffset, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID, bRecover)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamFOVSet)
    return self:KAPI_Camera_StartCameraFOVAction(CameraModeID, ECameraModifierPriority.CamFOVSet, ModifierEffectModeTag, -1, FOVOffset, bRecover, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraXOYLagAction(CameraModeID, XOYLag, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLagSet)
    return self:KAPI_Camera_StartCameraLagAction(CameraModeID, ECameraModifierPriority.CamLagSet, ModifierEffectModeTag, XOYLag, -1, -1, -1, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraZLagAction(CameraModeID, ZLag, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLagSet)
    return self:KAPI_Camera_StartCameraLagAction(CameraModeID, ECameraModifierPriority.CamLagSet, ModifierEffectModeTag, -1, ZLag, -1, -1, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraXYZLagAction(CameraModeID, XOYLag, ZLag, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLagSet)
    return self:KAPI_Camera_StartCameraLagAction(CameraModeID, ECameraModifierPriority.CamLagSet, ModifierEffectModeTag, XOYLag, ZLag, -1, -1, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraXOYSoftRadiusAction(CameraModeID, XOYSoftRadius, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLagSet)
    return self:KAPI_Camera_StartCameraLagAction(CameraModeID, ECameraModifierPriority.CamLagSet, ModifierEffectModeTag, -1, -1, XOYSoftRadius, -1, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraZSoftRadiusAction(CameraModeID, ZSoftRadius, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLagSet)
    return self:KAPI_Camera_StartCameraLagAction(CameraModeID, ECameraModifierPriority.CamLagSet, ModifierEffectModeTag, -1, -1, -1, ZSoftRadius, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

function CameraActionHandler:StartCameraXYZSoftRadiusAction(CameraModeID, XYSoftRadius, ZSoftRadius, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLagSet)
    return self:KAPI_Camera_StartCameraLagAction(CameraModeID, ECameraModifierPriority.CamLagSet, ModifierEffectModeTag, -1, -1, XYSoftRadius, ZSoftRadius, BlendInTime, BlendOutTime, Duration, BlendInType, BlendOutType, CurveInID, CurveOutID)
end

-------------------------------------Custom Modifier------------------------------------------------------
function CameraActionHandler:StartCameraParamOverrideAction(CameraModeID, FadeInTime, Duration, FadeOutTime, bUseYawAdjust, TargetYaw,
                                                            bUsePitchAdjust, TargetPitch, TargetZoom, TargetFOV, TargetXYZLag, TargetXYZSoftRadius,
                                                            BlendInMode, BlendOutMode, FadeInCurveID, FadeOutCurveID, bDisableLimitView)
    if not IsValidID(CameraModeID) then
        return
    end

    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamParamSetSet)

    local PlayerViewRotation = Game.me and Game.me:GetRotation() or FRotator()
    local CameraParamOverrideActionID = self:KAPI_Camera_StartCameraParamOverrideAction(
            CameraModeID, ECameraModifierPriority.CamParamSetSet, ModifierEffectModeTag,
            FadeInTime, Duration, FadeOutTime, PlayerViewRotation, bUseYawAdjust, TargetYaw, bUsePitchAdjust, 
            TargetPitch, TargetZoom, TargetFOV, TargetXYZLag, TargetXYZSoftRadius, BlendInMode, BlendOutMode, FadeInCurveID, 
            FadeOutCurveID, bDisableLimitView
    )

    if IsValidID(CameraParamOverrideActionID) then
        CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamOverride][CameraParamOverrideActionID] = CameraParamOverrideActionID
        return CameraParamOverrideActionID
    end
    return KG_INVALID_ID
end

function CameraActionHandler:StopCameraParamOverrideAction(ID)
    if not IsValidID(ID) or CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamOverride][ID] == nil then
        return
    end

    self:StopCameraAction(ID)

    CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamOverride][ID] = nil
end

function CameraActionHandler:StartCameraParamAdditionalAction(CameraModeID, FadeInTime, Duration, FadeOutTime, YawOffset, PitchOffset,
                                                              ZoomOffset, FOVOffset, BlendInMode, BlendOutMode,
                                                              FadeInCurveID, FadeOutCurveID, bDisableLimitView)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamParamModifySet)
    
    local CameraParamAdditionalActionID = self:KAPI_Camera_StartCameraParamAdditionalAction(
            CameraModeID, ECameraModifierPriority.CamParamModifySet, ModifierEffectModeTag, 
            FadeInTime, Duration, FadeOutTime, YawOffset,
            PitchOffset, ZoomOffset, FOVOffset, BlendInMode, BlendOutMode,
            FadeInCurveID, FadeOutCurveID, bDisableLimitView
    )

    if IsValidID(CameraParamAdditionalActionID) then
        CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamAdditional][CameraParamAdditionalActionID] = CameraParamAdditionalActionID
        return CameraParamAdditionalActionID
    end
    return KG_INVALID_ID
end

function CameraActionHandler:StopCameraParamAdditionalAction(ID)
    if not IsValidID(ID) or CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamAdditional][ID] == nil then
        return
    end
    self:StopCameraAction(ID)

    CameraActionHandler.ActionMap[CameraActionHandler.ActionTag.ParamAdditional][ID] = nil
end

--- 第三人称镜头专用战斗镜头修正
function CameraActionHandler:GetBattleCameraCorrectActionLockTargetID()
    return self.BattleCameraCorrectActionLockTargetID
end

function CameraActionHandler:StartBattleCameraCorrectAction(CameraModeID, LockTargetID, BufferYawLeft, SafeYawLeft, SafeYawRight, bufferYawRight, BufferPitchHigh, SafePitchHigh, SafePitchLow, BufferPitchLow, NewSmoothPitchLiftTime, NewSmoothPitchDeclineTime, NewSmoothYawTime, NewSmoothMaxTime, NewAdaptiveRatio, NewDampRatio, bContinuous)
    if not IsValidID(LockTargetID) then
        return
    end

    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamBattleCorrectSet)
    self.BattleCameraCorrectActionLockTargetID = LockTargetID

    -- 找不到相机说明生命周期有问题
    local BattleCameraCorrectActionID = self.BattleCorrectCameraActionID
    if not BattleCameraCorrectActionID then
        BattleCameraCorrectActionID = self:KAPI_Camera_StartBattleCameraCorrectAction(CameraModeID, ECameraModifierPriority.CamBattleCorrectSet, ModifierEffectModeTag, LockTargetID, BufferYawLeft, SafeYawLeft, SafeYawRight, bufferYawRight, BufferPitchHigh, SafePitchHigh, SafePitchLow, BufferPitchLow, NewSmoothPitchLiftTime, NewSmoothPitchDeclineTime, NewSmoothYawTime, NewSmoothMaxTime, NewAdaptiveRatio, NewDampRatio, bContinuous)
        if not BattleCameraCorrectActionID then
            return
        end
        self.BattleCorrectCameraActionID = BattleCameraCorrectActionID
        return BattleCameraCorrectActionID
    end

    self.BattleCorrectCameraActionID = self:KAPI_Camera_ReStartBattleCameraCorrectAction(BattleCameraCorrectActionID, CameraModeID, ECameraModifierPriority.CamBattleCorrectSet, ModifierEffectModeTag, LockTargetID, BufferYawLeft, SafeYawLeft, SafeYawRight, bufferYawRight, BufferPitchHigh, SafePitchHigh, SafePitchLow, BufferPitchLow, NewSmoothPitchLiftTime, NewSmoothPitchDeclineTime, NewSmoothYawTime, NewSmoothMaxTime, NewAdaptiveRatio, NewDampRatio, bContinuous)
    return self.BattleCorrectCameraActionID
end

function CameraActionHandler:StopBattleCameraCorrect()
    local BattleCameraCorrectActionID = self.BattleCorrectCameraActionID
    if BattleCameraCorrectActionID then
        self:StopCameraAction(BattleCameraCorrectActionID)
    end
end

function CameraActionHandler:StartBattleCameraTurnToLock(CameraModeID, LockTargetID, YawDemarcation, WideAngleSmoothTime, NarrowAngleSmoothTime, MaxSmoothTime, DampRatio)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamTurnToLockSet)

    local BattleTurnToLockCameraActionID = self.BattleTurnToLockCameraActionID
    if not BattleTurnToLockCameraActionID then
        BattleTurnToLockCameraActionID = self:KAPI_Camera_StartCameraTurnToLockAction(
            CameraModeID, ECameraModifierPriority.CamTurnToLockSet, ModifierEffectModeTag,
            LockTargetID, YawDemarcation, WideAngleSmoothTime, NarrowAngleSmoothTime, 
            MaxSmoothTime, DampRatio
        )
        self.BattleTurnToLockCameraActionID = BattleTurnToLockCameraActionID
        return BattleTurnToLockCameraActionID
    end

    self.BattleTurnToLockCameraActionID = self:KAPI_Camera_ReStartCameraTurnToLockAction(BattleTurnToLockCameraActionID, CameraModeID, ECameraModifierPriority.CamTurnToLockSet, ModifierEffectModeTag, LockTargetID, YawDemarcation, WideAngleSmoothTime, NarrowAngleSmoothTime, MaxSmoothTime, DampRatio)
    return self.BattleTurnToLockCameraActionID
end

function CameraActionHandler:StopBattleCameraTurnToLock()
    local BattleTurnToLockCameraActionID = self.BattleTurnToLockCameraActionID
    if BattleTurnToLockCameraActionID then
        self:StopCameraAction(BattleTurnToLockCameraActionID)
    end
end

function CameraActionHandler:StartThirdCameraLockActorForwardAction(CameraModeID, ActorID)
    if not IsValidID(ActorID) then
        return
    end

    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLockForwardSet)

    local CameraLockActorForwardActionID = self.CameraLockActorForwardActionID
    if not IsValid(CameraLockActorForwardActionID) then
        CameraLockActorForwardActionID = self:KAPI_Camera_StartCameraLockActorForwardAction(CameraModeID, ECameraModifierPriority.CamLockForwardSet, ModifierEffectModeTag, ActorID, 5)
        self.CameraLockActorForwardActionID = CameraLockActorForwardActionID
    end

    return CameraLockActorForwardActionID
end

function CameraActionHandler:StopThirdCameraLockActorForwardAction()
    local CameraLockActorForwardActionID = self.CameraLockActorForwardActionID
    if CameraLockActorForwardActionID then
        self:StopCameraAction(CameraLockActorForwardActionID)
        self.CameraLockActorForwardActionID = nil
    end
end

CameraActionHandler.ZeroVector = CameraActionHandler.ZeroVector or FVector(0, 0, 0)
CameraActionHandler.LookAtDefaultRotateOff = CameraActionHandler.LookAtDefaultRotateOff or FRotator(-10, -10, 0)
function CameraActionHandler:StartCameraLookAtAction(CameraModeID, EntityID, ActorID, Location, BlendInTime, BlendOutTime, Duration, bRecover, bCanInterrupt, TargetRotDelta, bManualControlPitch)
    local Entity = Game.EntityManager:getEntity(EntityID)
    local FinalLookAtActorID = KG_INVALID_ID
    local FinalLookAtLocation = CameraActionHandler.ZeroVector

    if Entity and Entity.CharacterID then
        FinalLookAtActorID = Entity.CharacterID
    elseif Entity and  Entity.isLocalSceneActor  then
        local LocalEntity = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(Entity.InsID)
        if 	LocalEntity then
            FinalLookAtActorID = LocalEntity.CharacterID
        end
    elseif ActorID then
        FinalLookAtActorID = ActorID
    elseif Location then
        FinalLookAtLocation = Location
    else
        return
    end

    if not FinalLookAtLocation and not FinalLookAtActorID then
        Log.DebugFormat("CameraManager:CameraLookAtTarget Failed : LookAtLocation Or Actor Not Found :%s %s %s", EntityID, ActorID, Location)
        return
    end

    TargetRotDelta = TargetRotDelta or CameraActionHandler.LookAtDefaultRotateOff
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamLockForwardSet)
    
    local CameraLookAtActionID = self.CameraLookAtActionID
    if bManualControlPitch == nil then bManualControlPitch = false end
    if bCanInterrupt == nil then bCanInterrupt = false end
    if not IsValidID(CameraLookAtActionID) then
        CameraLookAtActionID = self:KAPI_Camera_StartCameraLookAtAction(CameraModeID, ECameraModifierPriority.CamLookAtSet, 
                ModifierEffectModeTag, FinalLookAtLocation, FinalLookAtActorID, BlendInTime, BlendOutTime, Duration, 
                bRecover, TargetRotDelta, bManualControlPitch, bCanInterrupt, ECameraEaseFunction.EaseOutCubic,
                ECameraEaseFunction.EaseOutCubic, KG_INVALID_ID, KG_INVALID_ID
        )
        self.CameraLookAtActionID = CameraLookAtActionID
        return CameraLookAtActionID
    end

    self.CameraLookAtActionID = self:KAPI_Camera_ReStartCameraLookAtAction(CameraLookAtActionID, CameraModeID, 
            ECameraModifierPriority.CamLookAtSet, ModifierEffectModeTag, FinalLookAtLocation, FinalLookAtActorID, 
            BlendInTime, BlendOutTime, Duration, bRecover, TargetRotDelta, bManualControlPitch, bCanInterrupt, 
            ECameraEaseFunction.EaseOutCubic, ECameraEaseFunction.EaseOutCubic, KG_INVALID_ID, KG_INVALID_ID
    )
    return self.CameraLookAtActionID
end

function CameraActionHandler:StopCameraLookAtAction(bImmediate)
    local CameraLookAtActionID = self.CameraLookAtActionID
    if IsValidID(CameraLookAtActionID) then
        self:StopCameraAction(CameraLookAtActionID, bImmediate)
    end
end

function CameraActionHandler:StartCameraViewOffsetByRotAction(CameraModeID, CoordX, CoordY, BlendTime)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamViewOffSet_Rot)

    local CameraViewOffsetByRotActionID = self.CameraViewOffsetByRotActionID
    if not IsValidID(CameraViewOffsetByRotActionID) then
        CameraViewOffsetByRotActionID = self:KAPI_Camera_StartCameraViewOffByRotAction(CameraModeID, ECameraModifierPriority.CamViewOffSet_Rot, ModifierEffectModeTag, CoordX, CoordY, BlendTime, BlendTime, ECameraEaseFunction.EaseOutCubic, ECameraEaseFunction.EaseOutCubic, KG_INVALID_ID, KG_INVALID_ID)
        self.CameraViewOffsetByRotActionID = CameraViewOffsetByRotActionID
        return CameraViewOffsetByRotActionID
    end

    self:KAPI_Camera_ModifyCameraViewOffByRotAction(CameraViewOffsetByRotActionID, CoordX, CoordY, BlendTime)
    return CameraViewOffsetByRotActionID
end

function CameraActionHandler:ModifyCameraViewOffsetByRotAction(ID, CoordX, CoordY, BlendTime)
    if IsValidID(ID) then
        self:KAPI_Camera_ModifyCameraViewOffByRotAction(ID, CoordX, CoordY, BlendTime)
    end
end

function CameraActionHandler:StopCameraViewOffsetByRotAction(ID)
    local CameraViewOffsetByRotActionID = ID or self.CameraViewOffsetByRotActionID
    if IsValidID(CameraViewOffsetByRotActionID) then
        self:StopCameraAction(CameraViewOffsetByRotActionID)
        if ID == self.CameraViewOffsetByRotActionID then
            self.CameraViewOffsetByRotActionID = nil
        end
    end
end

function CameraActionHandler:StartThirdCameraAdjustAction(CameraModeID, Pitch, Yaw, Zoom, Duration)
    local ModifierEffectModeTag = self:GetModifierEffectCameraMode(ECameraModifierToID.CamRotZoomAdjustSet)

    return self:KAPI_Camera_StartCameraRotZoomAdjustAction(CameraModeID, ECameraModifierPriority.CamRotZoomAdjustSet, ModifierEffectModeTag, Pitch, Yaw, Zoom, Duration, 0, Duration, false, ECameraEaseFunction.EaseOutCubic, ECameraEaseFunction.EaseOutCubic, KG_INVALID_ID, KG_INVALID_ID)
end

-- endregion Action

return Class(nil, nil, CameraActionHandler)