local CameraCommonUtils = DefineClass("CameraCommonUtils")


-- TODO 待删除 禁止使用 
function CameraCommonUtils.GetEntityAnimBoneLocation(Entity, AnimationName, BoneName, bNotSubHalfHeight)
    if Entity then
        local AnimationPath = Entity:GetAnimPathAndLenFromAnimFeatureForSingleAnimation(AnimationName)
        if AnimationPath then
            local Translation = Entity.CppEntity:KAPI_Animation_GetBoneTransformByAnimAtTime(Entity.CppEntity:KAPI_Actor_GetMainMesh(), BoneName, AnimationPath, 0):GetTranslation()
            local HalfHeight = 0
            if not bNotSubHalfHeight then
                HalfHeight  = Entity.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight()
                Translation.Z = Translation.Z - HalfHeight
            end
            Translation.X = 0
			
			-- todo 这里补丁一下, 要和策划对一下全部参考骨架骨骼点
			if Translation.Z < 0 then
				return FVector(0,0,77)
			end
			
            return Translation
        end
    end

    return FVector(0,0,77)
end

return CameraCommonUtils