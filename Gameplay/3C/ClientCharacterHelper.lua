
--客户端角色辅助方法库

OldMainPlayerCharacterID = "111111111"  -- luacheck: ignore

--获取主角客户端属性ID
function GetMainPlayerEID()
    if Game.me ~= nil then
        return Game.me.eid
    end
    return OldMainPlayerCharacterID
    --报错
    --Log.Debug("GetMainPlayerEID Error!")
end

function GetMainPlayerPropertySafely(Name)
	if Game.me == nil then
		-- 在主角不存在的时候进行了数据访问, 生命周期依赖有问题
		Log.Warning("Wrong Lifetime Dependency. Invalid Data Query" )
		return nil
	end

	return Game.me[Name]
end

----对物体进行描边
--function EnableMeshOutLine(bEnable, Mesh, index)
--    if Game.CameraManager and Game.CameraManager.EnableOutLine then
--        Game.CameraManager:EnableOutLine(bEnable, Mesh, index)
--    end
--end

--修改相机配置
function ModifyCameraSetting(Type)
    if Game.CameraManager then
        if Type == Enum.ESettingDataEnum.FarPlane then
            Game.CameraManager:ModifyCameraMaxZoomSettingChanged()
        elseif Type == Enum.ESettingDataEnum.Sensitivity then
            Game.CameraManager:ModifyCameraSensitivity()
        end
    end
end


---@param entA ActorBase
---@param entB ActorBase
function HandleLookAtLoc(entA, entB, bLookAt)
	if not entA or not entB then 
		return 
	end
	
	if not entA.bInWorld or not entB.bInWorld then
		return
	end
	
	if bLookAt then
		entA:EnterLookAtToLoc(entB:GetPosition())
		entB:EnterLookAtToLoc(entA:GetPosition())
	else
		entA:ExitLookAt()
		entB:ExitLookAt()
	end
end

-------------TEMP Begin
--暂时放这


-------------TEMP End

--是否有权限发送消息
--AI托管 当前是否为主Host

function StringValid(Str)
    return Str and type(Str) == "string" and string.len(Str) > 0
end

--table to string 序列化
function TableToString(obj)
    local lua = ""
    local t = type(obj)
    if t == "number" then
        lua = lua .. obj
    elseif t == "boolean" then
        lua = lua .. tostring(obj)
    elseif t == "string" then
        lua = lua .. string.format("%q", obj)
    elseif t == "table" then
        lua = lua .. "{\n"
        for k, v in pairs(obj) do
            lua = lua .. "[" .. TableToString(k) .. "]=" .. TableToString(v) .. ",\n"
        end
        local metatable = getmetatable(obj)
        if metatable ~= nil and type(metatable.__index) == "table" then
            for k, v in pairs(metatable.__index) do
                lua = lua .. "[" .. TableToString(k) .. "]=" .. TableToString(v) .. ",\n"
            end
        end
        lua = lua .. "}"
    elseif t == "nil" then
        return nil
    elseif t == 'userdata' then
        return tostring(obj)
    else
        error("can not TableToString a " .. t .. " type.")
    end
    return lua
end

--string to table 反序列化
function StringToTable(lua)
    local t = type(lua)
    if t == "nil" or lua == "" then
        return nil
    elseif t == "number" or t == "string" or t == "boolean" then
        lua = tostring(lua)
    else
        error("can not unserialize a " .. t .. " type.")
    end
    lua = "return " .. lua
    --local func = loadstring(lua)
    local func = load(lua)
    if func == nil then
        return nil
    end
    return func()
end

--获取地面高度
function GetFloorPos(StartPos, EndPos, OutPos, World)
    return import("LuaHelper").GetFloorPos(StartPos, EndPos, OutPos, World)
end

--获取可行走地面
function GetWalkableFloorPos(StartPos, EndPos, OutPos)
    local floorPos = LuaScriptAPI.GetWalkableFloorPos(Game.me.CharacterID, 
            StartPos.X, StartPos.Y, StartPos.Z, 
            EndPos.X, EndPos.Y, EndPos.Z)
    if floorPos then
        -- 直接修改传入表的字段
        OutPos.X = floorPos.X
        OutPos.Y = floorPos.Y
        OutPos.Z = floorPos.Z
    end
    return floorPos ~= nil
end

