local EInputEvent = import("EInputEvent")
local S_InputProcessor = kg_require("Gameplay.BattleSystem.S_InputProcessor")
local EMouseLockMode = import("EMouseLockMode")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local InputConst = kg_require("Gameplay.3C.Input.InputConst")
local BeHitConst = kg_require("Shared.Const.BeHitConst")
local HIT_FORBID_LOCO_MAP = BeHitConst.HIT_FORBID_LOCO_MAP
local ACTION_NAME_FOR_LOCO_FORBID = BeHitConst.ACTION_NAME_FOR_LOCO_FORBID

---@class PlayerController
local PlayerController = {}

function PlayerController:OnInitInputSystem()
    local bActionOperatorMode = Game.SettingsManager:GetIniData("OperatorMode") == Enum.EMouseOperatorMode.ActionMode
    if bActionOperatorMode then
        WidgetBlueprintLibrary.SetInputMode_GameOnly(self, true)
    else
        WidgetBlueprintLibrary.SetInputMode_GameAndUIEx(self, nil, EMouseLockMode.LockInFullscreen, false)
    end
end

function PlayerController:ReceiveBeginPlay()
	Game.PlayerController = self
	if self.disableJumpSwitcher == nil then
		self.disableJumpSwitcher = Game.ObjectPoolManager:GetOneLockSwitcher()
		self.disableDodgeSwitcher = Game.ObjectPoolManager:GetOneLockSwitcher()
		self.disableMoveSwitcher = Game.ObjectPoolManager:GetOneLockSwitcher()
	end

end

function PlayerController:ReceiveEndPlay(EndPlayReason)
	if self.disableJumpSwitcher ~= nil then
		Game.ObjectPoolManager:ReturnOneLockSwitcher(self.disableJumpSwitcher)
		Game.ObjectPoolManager:ReturnOneLockSwitcher(self.disableDodgeSwitcher)
		Game.ObjectPoolManager:ReturnOneLockSwitcher(self.disableMoveSwitcher)
		self.disableJumpSwitcher = nil
		self.disableDodgeSwitcher = nil
		self.disableMoveSwitcher = nil
	end
	
	Game.PlayerController = nil

end

function PlayerController:OnPostInitializeComponents()
	self.InputDispatcher = import("InputControlDispatcher")(self)
    self.InputDispatcher:LuaBeginPlay()

    self.bAutoManageActiveCameraTarget = false

    --self.InputList = {}
    --self.LastYawInput = 0
    --self.LastPitchInput = 0

    self.CameraSensitivity = 1
    
    -- 这个目前是直接进行赋值的, 后期扩展注意维护
    self.LocoInputToFuncBindMapping = {}

	Game.CameraManager = self.PlayerCameraManager
	-- Streaming Source常开
	self.bEnableStreamingSource = true
end

function PlayerController:OpenMap(InMapName)
    Log.DebugFormat("PlayerController:OpenMap : %s", InMapName)
    self:NativeOpenMap(InMapName)
end


function PlayerController:OnBindEntity()
    if self.InputDispatcher then
        self.InputDispatcher:Reset()
        self.InputDispatcher:BindInit(self:GetInputComponent());
    end

    if self.InputActionAdaptor then
        self.InputActionAdaptor:OnReceiveOnPossess()
    end

    if self.PlayerCameraManager then
        self.PlayerCameraManager:OnReceiveOnPossess()
    end

    if self.BattleSystemInputProcessor == nil then
        self.BattleSystemInputProcessor = S_InputProcessor.new(self)
    end


	self.disableJumpSwitcher:InitFromCycleUsed()
	self.disableDodgeSwitcher:InitFromCycleUsed()
	self.disableMoveSwitcher:InitFromCycleUsed()

    self:InputBind()
end

function PlayerController:OnUnbindEntity()
    self:InputUnbind()

    if self.BattleSystemInputProcessor ~= nil then
        self.BattleSystemInputProcessor:Destroy()
        self.BattleSystemInputProcessor = nil
    end

    if self.PlayerCameraManager then
        self.PlayerCameraManager:UnInitForOnUnPossess()
    end

    if self.InputActionAdaptor then
        self.InputActionAdaptor:UnInitForOnUnPossess()
    end
end

function PlayerController:HudInteract_Axis(value)
	if self.InputActionAdaptor:BlockInputAxis("HudInteract_Axis", value) then
		return
	end
	
    Game.HUDInteractManager:OnReceiveHudInteractAxisEvent(value)
    --npc闲话也需要
    Game.NPCSystem:OnReceiveHudInteractAxisEvent(value)
	-- npc对象选择也需要
	Game.NPCManager:OnReceiveHudInteractAxisEvent(value)
end

function PlayerController:OperatorModeCursor_Axis(value)
	if self.InputActionAdaptor:BlockInputAxis("OperatorModeCursor_Axis", value) then
		return
	end
    Game.CursorManager:ReceiveInputAxis(value)
end

function PlayerController:CommonInput_Action(ActionName, InputEvent)
    if self.InputActionAdaptor == nil then
        return
    end

    if self.InputActionAdaptor:BlockInputAction(ActionName, InputEvent) then
        return 
    end

    Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:InputUnbind()
	if self.InputDispatcher then
		self.InputDispatcher:UnbindAll(self:GetInputComponent())
	end
    if self:GetControlledPawnObjectID() == Game.me.CharacterID then
        Game.me:UnbindLocoInputAction(self, true)
        Game.me.InputProcessor = nil
    end
	self:UninjectEngineShortcut()
    
    -- todo 补InputBind的解绑逻辑
end

--输入事件绑定
function PlayerController:InputBind()
    if self:GetControlledPawnObjectID() == Game.me.CharacterID then
        --交互按钮
        BindInputAxisEvent('HudInteract_Axis', self, self.HudInteract_Axis)
        
        --操作模式显/隐鼠标
        BindInputAxisEvent('OperatorModeCursor_Axis', self, self.OperatorModeCursor_Axis)
        
        for eventType, EventTypeName in pairs(Enum.EInputType) do
            if string.find(EventTypeName, "_Action", 1) ~= nil then
				-- EInputTypeSkillGroup  EInputTypeLocoGroup EInputTypeMouseGroup的绑定已迁移到InputActionAdaptor中
				if Enum.EInputTypeSkillGroup[eventType] == nil and
					Enum.EInputTypeLocoGroup[eventType] == nil and
					Enum.EInputTypeMouseGroup[eventType] == nil then
					BindInputActionEvent(EventTypeName, self, self.CommonInput_Action)
				end
            end
        end
        
        -- 按照自己的逻辑, 对自己模块感兴趣的逻辑进行InputAction-响应函数的binding
        Game.me.InputProcessor = self.BattleSystemInputProcessor
        Game.me:BindLocoInputAction(self, true)

		-- 非Editor下直接劫持引擎的快捷键
		if not import("C7FunctionLibrary").IsC7Editor() then
			self:InjectEngineShortcut()
		end
    end
end

function PlayerController:InjectEngineShortcut()
	for i = 1, #InputConst.NeedBlockEngineShortcut do
		local key = InputConst.NeedBlockEngineShortcut [i]
		Game.UIInputProcessorManager:BindKeyEvent(self, key, EInputEvent.IE_Pressed, "OnEngineShortcutPressed")
		Game.UIInputProcessorManager:BindKeyEvent(self, key, EInputEvent.IE_Released, "OnEngineShortcutReleased")
	end
end

function PlayerController:UninjectEngineShortcut()
	for i = 1, #InputConst.NeedBlockEngineShortcut do
		local key = InputConst.NeedBlockEngineShortcut [i]
		Game.UIInputProcessorManager:UnBindKeyEvent(self, key, EInputEvent.IE_Pressed)
		Game.UIInputProcessorManager:UnBindKeyEvent(self, key, EInputEvent.IE_Released)
	end
end

function PlayerController:OnEngineShortcutPressed(keyName, inputEvent)
	local actionName = InputConst.EngineShortcutToActionName[keyName]
	if actionName then
		NotifyInputAction(actionName, inputEvent)
	end
	return true
end

function PlayerController:OnEngineShortcutReleased(keyName, inputEvent)
	local actionName = InputConst.EngineShortcutToActionName[keyName]
	if actionName then
		NotifyInputAction(actionName, inputEvent)
	end
	return true
end

--GM left Ctrl + Left Mouse右键事件 默认打印角色网络属性
function PlayerController:OnLeftMouseSelectActor(SelectActor)
    if not SelectActor then
        return
    end

    local entity = Game.EntityManager:getEntity(SelectActor:GetEntityUID())
    if not entity then
        return
    end
    
    Log.DebugFormat("SelectActor entityID=%s" , entity:uid())
    Game.GMManager.ExecuteCommandArgs("PrintEntityProp", tostring(entity.eid))
end

function PlayerController:ExecuteCustomCommand(Command, Params)
    Game.GMManager.ExecuteCommandArgs(Command, Params)
end

function PlayerController:GetLocoInputVec()
    if self:HasMoveAxisInput() == false then
        return nil
    end
    
    return self:GetLocoInputDirectionAsVec()
    
end

-- function PlayerController:OnPostProcessInput(DeltaTime, bGamePaused)
--     if self:K2_IsInputKeyDown("C") and self:K2_IsInputKeyDown("LeftAlt") then
--         Game.GMManager.ExecuteCommandArgs("KillNpc", "", "")
--     end
-- end

function PlayerController:DisableControllerInput(tag, bDisable, bAbsolute)
	if bDisable then
		self.disableMoveSwitcher:SwitchOn(tag, bAbsolute)
		self.disableJumpSwitcher:SwitchOn(tag, bAbsolute)
		self.disableDodgeSwitcher:SwitchOn(tag, bAbsolute)
	else
		self.disableMoveSwitcher:SwitchOff(tag, bAbsolute)
		self.disableJumpSwitcher:SwitchOff(tag, bAbsolute)
		self.disableDodgeSwitcher:SwitchOff(tag, bAbsolute)
	end
end

function PlayerController:DisableControllerMove(tag, bDisable, bAbsolute)
	if bDisable then
		self.disableMoveSwitcher:SwitchOn(tag, bAbsolute)
	else
		self.disableMoveSwitcher:SwitchOff(tag, bAbsolute)
	end
end

function PlayerController:DisableControllerJump(tag, bDisable, bAbsolute)
	if bDisable then
		self.disableJumpSwitcher:SwitchOn(tag, bAbsolute)
	else
		self.disableJumpSwitcher:SwitchOff(tag, bAbsolute)
	end
end

function PlayerController:DisableControllerDodge(tag, bDisable, bAbsolute)
	if bDisable then
		self.disableDodgeSwitcher:SwitchOn(tag, bAbsolute)
	else
		self.disableDodgeSwitcher:SwitchOff(tag, bAbsolute)
	end
end

function PlayerController:SetInputWhiteList(Reason, WhiteList)
	if self.InputActionAdaptor then
		self.InputActionAdaptor:SetInputWhiteList(Reason, WhiteList)
	end
end

function PlayerController:ClearAllInputWhiteList(Reason)
	if self.InputActionAdaptor then
		self.InputActionAdaptor:ClearAllInputWhiteList(Reason)
	end
end

function PlayerController:EnableMouseAxisInputChangeNotify(bEnable)
	if self.InputActionAdaptor then
		self.InputActionAdaptor:EnableMouseAxisInputChangeNotify(bEnable)
	end
end

--- region Input Handle

function PlayerController:SetReminder(Switcher, LocoType)
	local ForbidName = HIT_FORBID_LOCO_MAP[LocoType]
	if Switcher:IsLockedBy(ETE.EDisabledSkillReason.Stagger) then
		Game.me:GenReminder(Enum.EReminderTextData.HITCONFLICT_REMINDER, {Game.me:GetStaggerState(), ForbidName})
	elseif Switcher:IsLockedBy(ETE.EDisabledSkillReason.BuffState) then
		Game.me:GenReminder(Enum.EReminderTextData.BUFFCONTROLCONFLICT_REMINDER, {Game.me:GetForbidLocoBuffState(LocoType), ForbidName})
	end
end

function PlayerController:Handle_JumpAction(ActionName, InputEvent)
	if Game.me then
		if Game.me.disableLocoJumpSwitcher and  Game.me.disableLocoJumpSwitcher:IsSwitchOn() and InputEvent == EInputEvent.IE_Pressed then
			self:SetReminder(Game.me.disableLocoJumpSwitcher, ACTION_NAME_FOR_LOCO_FORBID.Jump)
		end
		if self.disableJumpSwitcher ~= nil and not self.disableJumpSwitcher:IsSwitchOn() then
			Game.me:LocoForInputAction(ActionName, InputEvent == EInputEvent.IE_Pressed)
		end
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:Handle_DodgeAction(ActionName, InputEvent)
	if Game.me then
		if Game.me.disableLocoDodgeSwitcher and  Game.me.disableLocoDodgeSwitcher:IsSwitchOn() and InputEvent == EInputEvent.IE_Pressed then
			self:SetReminder(Game.me.disableLocoDodgeSwitcher, ACTION_NAME_FOR_LOCO_FORBID.Doge)
		end
		if self.disableDodgeSwitcher ~= nil and not self.disableDodgeSwitcher:IsSwitchOn() then
			Game.me:LocoForInputAction(ActionName, InputEvent == EInputEvent.IE_Pressed)
		end
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:SetMoveReminder(InputEvent)
	local DisableType = nil
	local Switcher = nil
	if Game.me and Game.me.disableLocoMoveSwitcher and  Game.me.disableLocoMoveSwitcher:IsSwitchOn() and InputEvent == EInputEvent.IE_Pressed then
		DisableType = ACTION_NAME_FOR_LOCO_FORBID.Move
		Switcher = Game.me.disableLocoMoveSwitcher
	end

	if Game.me and Game.me.disableLocoRotateSwitcher and  Game.me.disableLocoRotateSwitcher:IsSwitchOn() and InputEvent == EInputEvent.IE_Pressed then
		DisableType = ACTION_NAME_FOR_LOCO_FORBID.Rotate
		Switcher = Game.me.disableLocoRotateSwitcher
	end

	if DisableType then
		self:SetReminder(Switcher, DisableType)
	end
end

function PlayerController:Handle_MoveForwardAction(ActionName, InputEvent)
	self:SetMoveReminder(InputEvent)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:Handle_MoveBackwardAction(ActionName, InputEvent)
	self:SetMoveReminder(InputEvent)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:Handle_MoveRightAction(ActionName, InputEvent)
	self:SetMoveReminder(InputEvent)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:Handle_MoveLeftAction(ActionName, InputEvent)
	self:SetMoveReminder(InputEvent)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:Handle_MoveForward_2Action(ActionName, InputEvent)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

function PlayerController:Handle_MoveRight_2Action(ActionName, InputEvent)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, ActionName, InputEvent)
end

-- 注意: Forward是X轴, Right是Y轴
function PlayerController:Handle_MoveForward_Axis(AxisValue)
	if self.disableMoveSwitcher == nil or self.disableMoveSwitcher:IsSwitchOn() then
		return nil
	end

	self:InputMoveAxis_X(AxisValue)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_MOVE_INPUT, AxisValue, "X")
end

function PlayerController:Handle_MoveRight_Axis(AxisValue)
	if self.disableMoveSwitcher == nil or self.disableMoveSwitcher:IsSwitchOn() then
		return nil
	end

	self:InputMoveAxis_Y(AxisValue)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_MOVE_INPUT, AxisValue, "Y")
end

function PlayerController:Handle_MoveForward_Axis_2(AxisValue)
	if self.disableMoveSwitcher == nil or self.disableMoveSwitcher:IsSwitchOn() then
		return nil
	end

	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_MOVE_INPUT, AxisValue, "X2")
end

function PlayerController:Handle_MoveRight_Axis_2(AxisValue)
	if self.disableMoveSwitcher == nil or self.disableMoveSwitcher:IsSwitchOn() then
		return nil
	end

	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_MOVE_INPUT, AxisValue, "Y2")
end

function PlayerController:Handle_LookUp_Axis(AxisValue)
	local CamMgr = self.PlayerCameraManager
	if CamMgr then
		AxisValue = AxisValue * CamMgr:GetCameraPitchSensitivity()
	end
	if CamMgr:CanPCRotate() then
		self.InputActionAdaptor:OnLoopUp_Axis(AxisValue)
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.CAMERA_ROTATE_INPUT, AxisValue)
end

function PlayerController:Handle_Turn_Axis(AxisValue)
	local CamMgr = self.PlayerCameraManager
	if CamMgr then
		AxisValue = AxisValue * CamMgr:GetCameraYawSensitivity()
	end
	if CamMgr:CanPCRotate() then
		self.InputActionAdaptor:OnTurn_Axis(AxisValue)
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.CAMERA_ROTATE_INPUT, AxisValue)
end

--- endregion

return Class(nil, nil, PlayerController)
