local PPPriorityQueue = require("Gameplay.3C.PostProcess.PPPriorityQueue")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local PostProcessConst = kg_require("Gameplay.Effect.PostProcessConst")

local ULLFunc = import("LowLevelFunctions")

---@class PostProcessManager
local PostProcessManager = {}

PostProcessManager.DefaultSceneStandardExposureBias = 1.0 

local PPLuaClasses = {
    [Enum.EPostProcessType.SceneVolume] = "Gameplay.3C.PostProcess.PPClasses.PPExternalVolume",
    [Enum.EPostProcessType.Bloom] = "Gameplay.3C.PostProcess.PPClasses.PPBloom",
    [Enum.EPostProcessType.DOF] = "Gameplay.3C.PostProcess.PPClasses.PPDepthOfField",
    [Enum.EPostProcessType.RadialBlur] = "Gameplay.3C.PostProcess.PPClasses.PPMRadialBlur",
    [Enum.EPostProcessType.Darken] = "Gameplay.3C.PostProcess.PPClasses.PPDarken",
    [Enum.EPostProcessType.GI_IndirectLight] = "Gameplay.3C.PostProcess.PPClasses.PPGlobalIllumination_IndirectLight",
    [Enum.EPostProcessType.Vignette] = "Gameplay.3C.PostProcess.PPClasses.PPMVignette",
    [Enum.EPostProcessType.ColorAdjust] = "Gameplay.3C.PostProcess.PPClasses.PPMColorAdjust",
    [Enum.EPostProcessType.RGBSplit] = "Gameplay.3C.PostProcess.PPClasses.PPMRGBSplit",
    [Enum.EPostProcessType.ColorSplitRadialBlur] = "Gameplay.3C.PostProcess.PPClasses.PPMColorSplitRadialBlur",
    [Enum.EPostProcessType.Fog] = "Gameplay.3C.PostProcess.PPClasses.PPMFog",
    [Enum.EPostProcessType.Brighten] = "Gameplay.3C.PostProcess.PPClasses.PPMBrighten",
    [Enum.EPostProcessType.ChromaticAberration] = "Gameplay.3C.PostProcess.PPClasses.PPChromaticAberration",
    [Enum.EPostProcessType.RadialUVDistort] = "Gameplay.3C.PostProcess.PPClasses.PPMRadialUVDistort",
    [Enum.EPostProcessType.CustomMaterial] = "Gameplay.3C.PostProcess.PPClasses.PPMCustomMaterial",
	[Enum.EPostProcessType.Phantom] = "Gameplay.3C.PostProcess.PPClasses.PPMPhantom",
	
	[Enum.EPostProcessType.Sketch] = "Gameplay.3C.PostProcess.PPClasses.PPMSketch",
	[Enum.EPostProcessType.Clip] = "Gameplay.3C.PostProcess.PPClasses.PPMClip",
}

local EKGPostProcessTypes = import("EKGPostProcessTypes")

local PPModifierClasses = {
    [EKGPostProcessTypes.Bloom] = "CEPPBloom_V2",
    [EKGPostProcessTypes.DOF_Mobile] = "CEPPDepthOfField_V2",
    [EKGPostProcessTypes.DOF_PC] = "CEPPDepthOfField_PC_V2",
    [EKGPostProcessTypes.PPMaterial] = "CEPPKGDynamicMaterial_V2",
    [EKGPostProcessTypes.Exposure] = "CEPPExposure_V2",
    [EKGPostProcessTypes.ImageEffect] = "CEPPImageEffect_V2",
    [EKGPostProcessTypes.GlobalIllumination] = "CEPPGlobalIllumination_V2",
    [EKGPostProcessTypes.ChromaticAberration] = "CEPPChromaticAberration_V2",
}

PostProcessManager.SharedMaterialTypes = {
    Common = 1, -- 通用shared后处理材质
    BlinkClip = 2, -- 裁剪&入梦效果共用
}

PostProcessManager.SharedMaterials = {
    [PostProcessManager.SharedMaterialTypes.Common] = ViewResourceConst.POST_PROCESS_MATERIAL_PATH.SharedCommon,
    [PostProcessManager.SharedMaterialTypes.BlinkClip] = ViewResourceConst.POST_PROCESS_MATERIAL_PATH.SharedBlinkClip,
}
PostProcessManager.SharedMaterialToMaterialTypes = {}
for k, v in pairs(PostProcessManager.SharedMaterials) do
    PostProcessManager.SharedMaterialToMaterialTypes[v] = k
end

function PostProcessManager:Init()
    self:NativeInit()
	
    --PP实例
    self.PPInstances = {}

    --记录每个层级的开关情况 ,默认全部开启
    self.PPLayerActiveInfo = {}
    for K, V in pairs(Enum.EPostProcessLayers) do
        self.PPLayerActiveInfo[V] = true
    end

    --PP优先级队列
    self.PPPriorityQueues = {}

    --后效luaClass
    self.PPClasses = {}

    --CameraModifier 实例
    self.PPModifierInstIDMap = {}

    --自动移除pp的计时器
    self.PPAutoRemoveTimers = {}

    self.PPNameIDMap = {}

    self.PostProcessPresetMapByID = {}
    self.PostProcessPresetMapByEffectID = {}
	
	self.CustomDepthPPPriorityQueue = nil

    self.FogPPIDs = {}
	self.SketchPPID = nil
	
    self.PPScalarMaterialBlendParam = import("PPScalarMaterialBlendParam")()
    self.PPVectorMaterialBlendParam = import("PPVectorMaterialBlendParam")()
    self.PPScalarMaterialParam = import("PPScalarMaterial")()
    self.PPVectorMaterialParam = import("PPVectorMaterial")()

    for K, V in pairs(PPLuaClasses) do
        self.PPClasses[K] = require(V)
    end

    -- key: shared material type, value: dynamic material instance
    self.SharedDynamicMaterialInstances = {}
    self.SharedDynamicMaterialInstanceRefCount = {}
    
    self.CameraPPDisableReason = 0
    self.PPVolumeDisableReason = 0
end

function PostProcessManager:UnInit()
    --self:ClearSharedCommonMaterial()
    self:NativeUnInit()

	for _, PPInst in pairs(self.PPInstances) do
		PPInst:UnInit()
	end
	
    self.PPInstances = nil
    self.PPLayerActiveInfo= {}
    self.PPClasses = nil
end

function PostProcessManager:RemoveAllPPInstancesOnLeaveSpace()
    for PPID, PPInst in pairs(self.PPInstances) do
        if PPInst.bDestroyWhenLeaveSpace then
            Log.Debug("PostProcessManager:RemoveAllPPInstancesOnLeaveSpace, remove pp", PPID)
            self:RemovePP(PPID) 
        end
    end
end

function PostProcessManager:GetCameraModifier(PPID)
    if self.PPModifierInstIDMap[PPID] then
        return Game.ObjectActorManager:GetObjectByID(self.PPModifierInstIDMap[PPID])
    end
end

function PostProcessManager:RemoveModifier(PPID,bImmediate)
    if bImmediate then
        local ModifierInst = self:GetCameraModifier(PPID)
        if  ModifierInst then
            self:RemovePPModifier(ModifierInst)
            self.PPModifierInstIDMap[PPID] = nil
        end
    else
        local Modifier = self:GetCameraModifier(PPID)
        if Modifier then
            Modifier:SetAutoRemoveFromList(true)
            Modifier:DisableModifier(false)
            self.PPModifierInstIDMap[PPID] = nil
        end
    end
end

function PostProcessManager:AddPPCameraModifier(PPID, ClassType)
    local Class = import(PPModifierClasses[ClassType])
    if not Class then
        DebugLogError(string.format('PostProcessManager:GetOrAddCameraModifier Error Class Not Found', ClassType))
        return
    end
	
	local Modifier = self:AddNewPPModifier(Class)
	self.PPModifierInstIDMap[PPID] = Game.ObjectActorManager:GetIDByObject(Modifier)
	return self.PPModifierInstIDMap[PPID]
end



function PostProcessManager:GetPPID()
    return ULLFunc.GetGlobalUniqueID()
end

function PostProcessManager:AddPP(PPLayer, PPType, PPParams, Priority, PPDuration, OptionalName)
    if OptionalName then
        if self.PPNameIDMap[OptionalName] then
            self:RemovePPByName(OptionalName)
        end
    end

    local PPID = self:GetPPID()
    local NewInst = self.PPClasses[PPType].new(PPID)
    self.PPInstances[PPID] = NewInst

    Log.Debug("PostProcessManager:AddPP,", PPID, NewInst.__cname, PPParams.MaterialPath, PPDuration)
    
    if OptionalName then
        if not self.PPNameIDMap[OptionalName] then
            self.PPNameIDMap[OptionalName] = PPID
        end
    end

    local FinalPriority = PPLayer + math.clamp(Priority or 0,0,9)
    NewInst:Init(PPParams, PPLayer, PPType, FinalPriority, PPDuration or -1)

	if NewInst:IsCustomDepthControl() then
		--不可叠加的后效,走优先级队列
		self.CustomDepthPPPriorityQueue = self.CustomDepthPPPriorityQueue or PPPriorityQueue.new()
		self.CustomDepthPPPriorityQueue:SimpleAddElement(PPID, FinalPriority, PPID)
	elseif PPType > Enum.EPostProcessType.STACKABLE_PP_ID_START then
        --可叠加的后效，直接开启
        NewInst:SetEnabled()
    else
        --不可叠加的后效,走优先级队列
        self.PPPriorityQueues[PPType] = self.PPPriorityQueues[PPType] or PPPriorityQueue.new()
        self.PPPriorityQueues[PPType]:SimpleAddElement(PPID, FinalPriority, PPID)
    end
    
    if PPDuration and PPDuration > 0 then
        self.PPAutoRemoveTimers[PPID] = Game.TimerManager:CreateTimerAndStart(function()
            self.PPAutoRemoveTimers[PPID] = nil
            self:RemovePP(PPID)
        end, PPDuration * 1000, 1)
    end

    return PPID
end

function PostProcessManager:SetPostProcessDestroyWhenLeaveSpace(PPID)
    if PPID == nil then
        return
    end
    
    local PPInst = self.PPInstances[PPID]
    if PPInst then
        PPInst.bDestroyWhenLeaveSpace = true
    end
end

function PostProcessManager:GetPPInsByName(Name)
    local PPID = self.PPNameIDMap[Name]
    if PPID then
        if self.PPInstances[PPID] then
            return  self.PPInstances[PPID]
        end
    end
end

function PostProcessManager:RemovePPByName(Name,BlendOutTime)
    if self.PPNameIDMap[Name] then
        self:RemovePP(self.PPNameIDMap[Name],BlendOutTime)
        self.PPNameIDMap[Name] = nil
    end
end

function PostProcessManager:RemovePP(PPID,BlendTime)
	local PPInst = self.PPInstances[PPID]
	if PPInst then
		local PPType = PPInst:GetPPType()

        Log.Debug("PostProcessManager:RemovePP,", PPID, PPInst.__cname)
        
		if BlendTime then
			PPInst:SetBlendOutTime(BlendTime)
		end
		
		if PPInst:IsCustomDepthControl() then
			self.CustomDepthPPPriorityQueue:SimpleRemoveElement(PPID)
		end

		if self.PPPriorityQueues[PPType] then
			self.PPPriorityQueues[PPType]:SimpleRemoveElement(PPID)
		end

		if self.PPAutoRemoveTimers[PPID] then
			Game.TimerManager:StopTimerAndKill(self.PPAutoRemoveTimers[PPID] )
			self.PPAutoRemoveTimers[PPID] = nil
		end

		PPInst:UnInit()
		self.PPInstances[PPID] = nil
	end

    if self.SketchPPID == PPID then
        self.SketchPPID = nil
    end

    for _, presetMap in pairs(self.PostProcessPresetMapByID) do
        if presetMap[PPID] ~= nil then
            presetMap[PPID] = nil
            break
        end
    end

    for _, presetMap in pairs(self.PostProcessPresetMapByEffectID) do
        if presetMap[PPID] ~= nil then
            presetMap[PPID] = nil
            break
        end
    end
end

function PostProcessManager:DumpAllPPInstanceInfo()
    for PPID, PPInst in pairs(self.PPInstances) do
        Log.Debug("PostProcessManager:DumpAllPPInstanceInfo, ", PPID, PPInst.__cname)
    end
end

function PostProcessManager:OnWorldMapLoadComplete(_)
    self.CameraPPDisableReason = 0
    self.PPVolumeDisableReason = 0
    self:DisablePostProcess(false)
end

---@param Reason PP_DISABLE_REASON
---@param bDisableCameraPP boolean 所有 gameplay 来源的后处理
---@param bDisablePPVolume boolean 所有PPVolume来源的后处理
--- 理论上后续知会有gameplay来源以及PPVolume两种来源的后处理, 其他的使用方式都会统一到这两种上
--- 默认切换地图就会自动恢复, 后续有跨地图保持PP禁用状态再额外支持
function PostProcessManager:DisablePP(Reason, bDisableCameraPP, bDisablePPVolume)
    Log.Debug("DisablePP", Reason, bDisableCameraPP, bDisablePPVolume)
    
    local BitMask = 1 << Reason
    local OldEnableCameraPP = self.CameraPPDisableReason == 0
    if bDisableCameraPP then
        self.CameraPPDisableReason = self.CameraPPDisableReason | BitMask
    else
        self.CameraPPDisableReason = self.CameraPPDisableReason & (~BitMask)
    end
    local NewEnableCameraPP = self.CameraPPDisableReason == 0

    if OldEnableCameraPP ~= NewEnableCameraPP then
        self:DisablePostProcess(not NewEnableCameraPP)
    end

    local OldEnablePPVolume = self.PPVolumeDisableReason == 0
    if bDisablePPVolume then
        self.PPVolumeDisableReason = self.PPVolumeDisableReason | BitMask
    else
        self.PPVolumeDisableReason = self.PPVolumeDisableReason & (~BitMask)
    end
    local NewEnablePPVolume = self.PPVolumeDisableReason == 0

    if OldEnablePPVolume ~= NewEnablePPVolume then
        Game.WorldManager:EnablePPVolumes(NewEnablePPVolume)
    end
end

function PostProcessManager:PPQueue_ActivatePP(PPID)
    if self.PPInstances[PPID] then
        self.PPInstances[PPID]:SetEnabled()
    end
end

function PostProcessManager:PPQueue_DeActivatePP(PPID)
    if self.PPInstances[PPID] then
        self.PPInstances[PPID]:SetDisable()
    end
end

function PostProcessManager:IsPPLayerActive(Layer)
    return self.PPLayerActiveInfo[Layer]
end

function PostProcessManager:SetLayerActivationInfo(NewInfo)
    self.PPLayerActiveInfo = NewInfo
    self:CheckPPLayers()
end

function PostProcessManager:CheckPPLayers()
    for _, PPInst in pairs(self.PPInstances) do
        if self.PPLayerActiveInfo[PPInst:GetPPLayer()] then
            PPInst:LayerEnabled()
        else
            PPInst:LayerDisabled()
        end
    end
end

function PostProcessManager:CreateDynamicMaterialInstance(MaterialPath, MaterialInstance)
    local materialType = PostProcessManager.SharedMaterialToMaterialTypes[MaterialPath]
    if materialType ~= nil then
        local dynamicMaterialInstance = self.SharedDynamicMaterialInstances[materialType]
        if dynamicMaterialInstance == nil then
            dynamicMaterialInstance = self:CreateNewPPMaterialMID(MaterialInstance)
            self.SharedDynamicMaterialInstances[materialType] = dynamicMaterialInstance
            self.SharedDynamicMaterialInstanceRefCount[materialType] = 1
        else
            self.SharedDynamicMaterialInstanceRefCount[materialType] = self.SharedDynamicMaterialInstanceRefCount[materialType] + 1
        end

        Log.Debug("use shared dynamic material instance", MaterialPath, self.SharedDynamicMaterialInstanceRefCount[materialType])
        return dynamicMaterialInstance
    end

    return Game.PostProcessManager:CreateNewPPMaterialMID(MaterialInstance)
end

function PostProcessManager:RemoveDynamicMaterialInstance(MaterialPath, MaterialInstance)
    local materialType = PostProcessManager.SharedMaterialToMaterialTypes[MaterialPath]
    if materialType ~= nil then
        self.SharedDynamicMaterialInstanceRefCount[materialType] = self.SharedDynamicMaterialInstanceRefCount[materialType] - 1
        Log.Debug("release shared dynamic material instance", MaterialPath, self.SharedDynamicMaterialInstanceRefCount[materialType])

        if self.SharedDynamicMaterialInstanceRefCount[materialType] == 0 then
            local dynamicMaterialInstance = self.SharedDynamicMaterialInstances[materialType]
            self:RemovePPMID(dynamicMaterialInstance)
            self.SharedDynamicMaterialInstances[materialType] = nil
        end

        return
    end

    self:RemovePPMID(MaterialInstance)
end

function PostProcessManager.GetCommonSharedMaterialPath()
    return PostProcessManager.SharedMaterials[PostProcessManager.SharedMaterialTypes.Common]
end

--region DataPreparing

function PostProcessManager:ProcessColor(Data)
    return FLinearColor(Data[1] or 0,Data[2] or 0, Data[3] or 0, Data[4] or 0)
end

PostProcessManager.PPTypeFunctionMap = {
    [Enum.EPPTypes.PP_BLOOM] = "Prepare_PPBloomData",
    [Enum.EPPTypes.PP_BRIGHTEN] = "Prepare_PPBrightenData",
    [Enum.EPPTypes.PP_COLOR_ADJUST] = "Prepare_PPColorAdjustData",
    [Enum.EPPTypes.PP_COLOR_ADJUST_RADIAL_BLUR] = "Prepare_PPColorAdjustRadialBlurData",
    [Enum.EPPTypes.PP_DARKEN] = "Prepare_PPDrakenData",
    [Enum.EPPTypes.PP_DOF] = "Prepare_PPDOFData",
    [Enum.EPPTypes.PP_RADIAL_BLUR] = "Prepare_PPRadialBlurData",
    [Enum.EPPTypes.PP_RADIAL_UV_DISTORT] = "Prepare_RadialUVDistortData",
    [Enum.EPPTypes.PP_RGB_SPLIT] = "Prepare_PPRGBSplitData",
    [Enum.EPPTypes.PP_VIGNETTE] = "Prepare_PPVignetteData",
    [Enum.EPPTypes.PP_CUTOM_MATERIAL] = "Prepare_PPCustomMaterialData",
	[Enum.EPPTypes.PP_PHANTOM] = "Prepare_PPPhantomData",
}

function PostProcessManager:Prepare_PPDrakenData(EffectID)
    local EffectData = Game.TableData.GetPP_DarkenDataRow(EffectID)

    if EffectData then
		if EffectData.ExposureBiasMode == 0 then
			return {bAdditionMode = true, AdditionalAutoExposureBias = EffectData.AutoExposureBias }, Enum.EPostProcessType.Darken
		else
			return {bAdditionMode = false, ExposureBias = EffectData.AutoExposureBias }, Enum.EPostProcessType.Darken
		end

    end
end

function PostProcessManager:Prepare_PPBloomData(EffectID)
    local EffectData = Game.TableData.GetPP_BloomDataRow(EffectID)
    if EffectData then
        local BloomParams = import("PPBloomParams")()

        BloomParams.bOverride_BloomMethod = true
        BloomParams.BloomMethod = EffectData.Method

        BloomParams.bOverride_BloomIntensity = true
        BloomParams.BloomIntensity = EffectData.Intensity

        BloomParams.bOverride_BloomThreshold = true
        BloomParams.BloomThreshold = EffectData.Threshold


        return { BloomParams = BloomParams }, Enum.EPostProcessType.Bloom
    end
end

function PostProcessManager:Prepare_PPDOFData(EffectID)
    local EffectData = Game.TableData.GetPP_DOFDataRow(EffectID)
    if EffectData then
        local DOFParams = import("PPDOFParams")()
        DOFParams.bMobileHQGaussian = EffectData.bMobileHQGaussian
        DOFParams.FocalRegion = EffectData.FocalRegion
        DOFParams.NearTransitionRegion = EffectData.NearTransitionRegion
        DOFParams.FarTransitionRegion = EffectData.FarTransitionRegion
        DOFParams.Scale = EffectData.Scale
        DOFParams.NearBlurSize = EffectData.NearBlurSize
        DOFParams.FarBlurSize = EffectData.FarBlurSize
        DOFParams.SensorWidth = EffectData.SensorWidth
        DOFParams.SqueezeFactor = EffectData.SqueezeFactor
        DOFParams.FocalDistance = EffectData.FocalDistance
        DOFParams.DepthBlurAmount = EffectData.DepthBlurAmount
        DOFParams.DepthBlurRadius = EffectData.DepthBlurRadius
		DOFParams.DepthOfFieldFstop = EffectData.DepthOfFieldFstop
		DOFParams.DepthOfFieldMinFstop = EffectData.DepthOfFieldMinFstop
		DOFParams.DepthOfFieldBladeCount = EffectData.DepthOfFieldBladeCount


		local FocalRegionUpdateType = EffectData.bAutoFocusMainChar and Enum.EDOFFocalRegionUpdateType.ByFocalTarget or Enum.EDOFFocalRegionUpdateType.Fixed

        return {DOFParams = DOFParams, FocalRegionUpdateType = FocalRegionUpdateType,
                TargetID = Game.me and Game.me.CharacterID or nil,
				FocalDistanceOffset = EffectData.FocalDistanceOffset or 0,
                TargetSocket =  EffectData.TargetSocket or  "head"},
        Enum.EPostProcessType.DOF
    end
end

function PostProcessManager:Prepare_PPColorAdjustData(EffectID)
    local EffectData = Game.TableData.GetPP_ColorAdjustDataRow(EffectID)
    if EffectData then
        local OutData = {
            Contrast = EffectData.Contrast,
            ContrastCurve = EffectData.ContrastCurve,
            Desaturate = EffectData.Desaturate,
            DesaturateCurve = EffectData.DesaturateCurve,
            InvertColorR = EffectData.InvertColorR,
            InvertColorRCurve = EffectData.InvertColorRCurve,
            InvertColorG = EffectData.InvertColorG,
            InvertColorGCurve = EffectData.InvertColorGCurve,
            InvertColorB = EffectData.InvertColorB,
            InvertColorBCurve = EffectData.InvertColorBCurve,
			bEnableDesaturateProgress = EffectData.bEnableDesaturateProgress
        }

        return OutData, Enum.EPostProcessType.ColorAdjust
    end
end

function PostProcessManager:Prepare_PPRadialBlurData(EffectID)
    local EffectData = Game.TableData.GetPP_RadialBlurDataRow(EffectID)
    if EffectData then
        local OutData = {
            Intensity = EffectData.Intensity,
            IntensityCurve = EffectData.IntensityCurve,
            Stride = EffectData.Stride,
            StrideCurve = EffectData.StrideCurve,
            MaskRadius = EffectData.MaskRadius,
            MaskRadiusCurve = EffectData.MaskRadiusCurve,
            MaskInvert = EffectData.MaskInvert,
            MaskInvertCurve = EffectData.MaskInvertCurve,
            Softness = EffectData.Softness,
            SoftnessCurve = EffectData.SoftnessCurve,
            Center = self:ProcessColor(EffectData.Center),
            CenterCurve = EffectData.CenterCurve,
        }
        return OutData, Enum.EPostProcessType.RadialBlur
    end
end

function PostProcessManager:Prepare_PPRGBSplitData(EffectID)
    local EffectData = Game.TableData.GetPP_RGBSplitDataRow(EffectID)
    if EffectData then
        local OutData = {
            Intensity = EffectData.Intensity,
            IntensityCurve = EffectData.IntensityCurve,
            Stride = EffectData.Stride,
            StrideCurve = EffectData.StrideCurve,
            MaskRadius = EffectData.MaskRadius,
            MaskRadiusCurve = EffectData.MaskRadiusCurve,
            MaskInvert = EffectData.MaskInvert,
            MaskInvertCurve = EffectData.MaskInvertCurve,
            Softness = EffectData.Softness,
            SoftnessCurve = EffectData.SoftnessCurve,
            Center = self:ProcessColor(EffectData.Center),
            CenterCurve = EffectData.CenterCurve,
        }
        return OutData, Enum.EPostProcessType.RGBSplit
    end
end

function PostProcessManager:Prepare_PPColorAdjustRadialBlurData(EffectID)
    local EffectData = Game.TableData.GetPP_ColorAdjustRadialBlurDataRow(EffectID)
    if EffectData then
        local OutData = {
            Intensity = EffectData.Intensity,
            IntensityCurve = EffectData.IntensityCurve,
            BlurStride = EffectData.BlurStride,
            BlurStrideCurve = EffectData.BlurStrideCurve,
            BlurMaskRadius = EffectData.BlurMaskRadius,
            BlurMaskRadiusCurve = EffectData.BlurRadiusCurve,
            BlurInvert = EffectData.BlurMaskInvert,
            BLurInvertCurve = EffectData.BlurInvertCurve,
            BlurSoftness = EffectData.BlurSoftness,
            BlurSoftnessCurve = EffectData.BlurSoftnessCurve,
            Center = self:ProcessColor(EffectData.Center),
            CenterCurve = EffectData.CenterCurve,
            ColorSplitStride = EffectData.ColorSplitStride,
            ColorSplitStrideCurve = EffectData.ColorSplitStrideCurve,
        }
        return OutData, Enum.EPostProcessType.ColorSplitRadialBlur
    end
end

function PostProcessManager:Prepare_PPVignetteData(EffectID)
    local EffectData = Game.TableData.GetPP_VignetteDataRow(EffectID)
    if EffectData then
        local OutData = {
            Intensity = EffectData.Intensity,
            IntensityCurve = EffectData.IntensityCurve,
            Color = self:ProcessColor(EffectData.Color),
            ColorCurve = EffectData.ColorCurve,
            Radius = EffectData.Radius,
            RadiusCurve = EffectData.RadiusCurve,
            Softness = EffectData.Softness,
            SoftnessCurve = EffectData.SoftnessCurve,
            Center = self:ProcessColor(EffectData.Center),
            CenterCurve = EffectData.CenterCurve,
        }
        return OutData, Enum.EPostProcessType.Vignette
    end
end

function PostProcessManager:Prepare_PPBrightenData(EffectID)
    local EffectData = Game.TableData.GetPP_BrightenDataRow(EffectID)
    if EffectData then
        local OutData = {
            Intensity = EffectData.Intensity,
            IntensityCurve = EffectData.IntensityCurve,
        }
        return OutData, Enum.EPostProcessType.Brighten
    end
end

function PostProcessManager:Prepare_PPPhantomData(EffectID)
	local EffectData = Game.TableData.GetPP_PhantomDataRow(EffectID)
	if EffectData then
		local OutData = {
			bFixBlendWeight = true,
			
			Gap = EffectData.Gap,
			Speed = EffectData.Speed,
			Direction = self:ProcessColor(EffectData.Direction),
		}
		return OutData, Enum.EPostProcessType.Phantom
	end
end

function PostProcessManager:Prepare_RadialUVDistortData(EffectID)
    local EffectData = Game.TableData.GetPP_RadialUVDistortDataRow(EffectID)
    if EffectData then
        local OutData = {
            DistortIntensity = EffectData.DistortIntensity,
            DistortIntensityCurve = EffectData.DistortIntensityCurve,
            TexInvert = EffectData.TexInvert,
            TexInvertCurve = EffectData.TexInvertCurve,
            DistortTex = EffectData.DistortTex
        }
        return OutData, Enum.EPostProcessType.RadialUVDistort
    end
end


function PostProcessManager:Prepare_PPCustomMaterialData(EffectID)
    local EffectData = Game.TableData.GetPP_SingleMaterialDataRow(EffectID)

    if EffectData then
        local OutData = {
            MaterialPath = EffectData.MaterialPath,
			bFixBlendWeight = true
        }
        local EffectParams = Game.TableData.GetPP_SingleMaterialParamsDataRow(EffectID)

        if EffectParams then
			--TODO:可以用Table继承
            OutData.BlendScalars = EffectParams.Scalars
            OutData.BlendVectors = EffectParams.Vectors
            OutData.Textures = EffectParams.Textures
            OutData.ScalarCurves = EffectParams.ScalarsLoop
            OutData.VectorCurves = EffectParams.VectorsLoop
            OutData.PlayerPos = EffectParams.PlayerPos
        end

		OutData.bTableConfig = true
		
        return OutData, Enum.EPostProcessType.CustomMaterial
    end
end

--endregion DataPreparing

function PostProcessManager:PPGetDataAndPPType(EffectID)
    local PPType = nil

    for K,PPTypeID in pairs(Enum.EPPTypes) do
        local PPTypeData = Game.TableData.GetPPTypesDataRow(PPTypeID)
        if PPTypeData and EffectID < PPTypeData.EndID and EffectID > PPTypeData.StartID then
            PPType = PPTypeID
            break
        end
    end

    if PPType and PostProcessManager.PPTypeFunctionMap[PPType] then
        local funcName = PostProcessManager.PPTypeFunctionMap[PPType]
        if self[funcName] then
            return self[funcName](self, EffectID)
        end
    end
end

-----------------------对外接口-------------------------------------------------------------------------------------------

function PostProcessManager:PlayPostProcessByID(Layer, Priority, PPEffectID, AlphaIn, AlphaOut, Duration)
    local PPData, PPType = self:PPGetDataAndPPType(PPEffectID)
    if PPData then
        PPData.AlphaInTime = AlphaIn
        PPData.AlphaOutTime = AlphaOut
        return Game.PostProcessManager:AddPP(Layer, PPType, PPData, Priority, Duration)
    else
        Log.ErrorFormat("Failed To Find PostProcess Effect: %s", PPEffectID)
    end
end

function PostProcessManager:PlayPostProcessPreset(Layer, Priority, EffectID, AlphaIn, AlphaOut, Duration)
    local PPConfig = Game.TableData.GetPPEffectsDataRow(EffectID)
    local ID = ULLFunc.GetGlobalUniqueID()

    if PPConfig and PPConfig.PPConfigsID then
        for _, PPEffectID in ksbcpairs(PPConfig.PPConfigsID) do
            local PPID = self:PlayPostProcessByID(Layer, Priority, PPEffectID, AlphaIn, AlphaOut, Duration)
            local presetMap = self.PostProcessPresetMapByID[ID]
            if presetMap == nil then
                presetMap = {}
                self.PostProcessPresetMapByID[ID] = presetMap
            end
            presetMap[PPID] = true

            presetMap = self.PostProcessPresetMapByEffectID[EffectID]
            if presetMap == nil then
                presetMap = {}
                self.PostProcessPresetMapByEffectID[EffectID] = presetMap
            end
            presetMap[PPID] = true
        end
        return ID
    else
        Log.ErrorFormat("Failed To Find PostProcess Preset: %s",EffectID)
    end
end

function PostProcessManager:StopPostProcessPreset(ID, BlendTime)
    if self.PostProcessPresetMapByID[ID] then
        for PPID, _ in pairs(self.PostProcessPresetMapByID[ID]) do
            self:RemovePP(PPID, BlendTime)
        end
        self.PostProcessPresetMapByID[ID] = nil
    end
end

function PostProcessManager:StopPostProcessPresetByEffectID(EffectID, BlendTime)
    if self.PostProcessPresetMapByEffectID[EffectID] then
        for PPID, _ in pairs(self.PostProcessPresetMapByEffectID[EffectID]) do
            self:RemovePP(PPID, BlendTime)
        end
        self.PostProcessPresetMapByEffectID[EffectID] = nil
    end
end

function PostProcessManager:EnableBloom(Layer, AlphaIn, AlphaOut, Duration, Priority,BloomParams)
    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.Bloom,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             BloomParams = BloomParams,
            },Priority,Duration)
end

function PostProcessManager:EnableDarken(Layer, AlphaIn, AlphaOut, Duration, Priority,ExposureBias)
    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.Darken,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             ExposureBias = ExposureBias,
            }, Priority,Duration)
end

function PostProcessManager:EnableDOF(Layer, AlphaIn, AlphaOut, Duration, Priority,DOFParams,FocalRegionUpdateType)
    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.DOF,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             DOFParams = DOFParams,
             FocalRegionUpdateType = FocalRegionUpdateType
            }, Priority,Duration)
end

function PostProcessManager:EnableVignette(Layer, AlphaIn, AlphaOut, Duration, Priority,Intensity, IntensityCurve, Color,
                                           ColorCurve, Radius, RadiusCurve, Softness, SoftnessCurve, Center, CenterCurve)
    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.Vignette,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             Intensity = Intensity,
             IntensityCurve = IntensityCurve,
             Color = Color,
             ColorCurve = ColorCurve,
             Radius = Radius,
             RadiusCurve = RadiusCurve,
             Softness = Softness,
             SoftnessCurve = SoftnessCurve,
             Center = Center,
             CenterCurve = CenterCurve,
    },Priority,Duration)
end

function PostProcessManager:EnableColorAdjust(Layer, AlphaIn, AlphaOut, Duration, Priority,Contrast, ContrastCurve, Desaturate,
                                              DesaturateCurve, InvertColorR, InvertColorRCurve, InvertColorG, InvertColorGCurve,
                                              InvertColorB, InvertColorBCurve)

    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.ColorAdjust,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             Contrast = Contrast,
             ContrastCurve = ContrastCurve,
             Desaturate = Desaturate,
             DesaturateCurve = DesaturateCurve,
             InvertColorR = InvertColorR,
             InvertColorRCurve = InvertColorRCurve,
             InvertColorG = InvertColorG,
             InvertColorGCurve = InvertColorGCurve,
             InvertColorB = InvertColorB,
             InvertColorBCurve = InvertColorBCurve,
            },Priority,Duration)
end

function PostProcessManager:EnableRadialBlur(Layer, AlphaIn, AlphaOut, Duration, Priority,Intensity, IntensityCurve, Stride,
                                             StrideCurve, MaskRadius, MaskRadiusCurve, MaskInvert, MaskInvertCurve,
                                             Softness, SoftnessCurve,Center,CenterCurve)

    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.RadialBlur,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             Intensity = Intensity,
             IntensityCurve = IntensityCurve,
             Stride = Stride,
             StrideCurve = StrideCurve,
             MaskRadius = MaskRadius,
             MaskRadiusCurve = MaskRadiusCurve,
             MaskInvert = MaskInvert,
             MaskInvertCurve = MaskInvertCurve,
             Softness = Softness,
             SoftnessCurve = SoftnessCurve,
             Center = Center,
             CenterCurve = CenterCurve,
            },Priority,Duration)
end

function PostProcessManager:EnableRGBSplit(Layer, AlphaIn, AlphaOut, Duration, Priority,Intensity, IntensityCurve, Stride,
                                           StrideCurve, Center, CenterCurve, MaskInvert,
                                           MaskInvertCurve, MaskRadius, MaskRadiusCurve,MaskSoftness,
                                           MaskSoftnessCurve)

    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.RGBSplit,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             Intensity = Intensity,
             IntensityCurve = IntensityCurve,
             Stride = Stride,
             StrideCurve = StrideCurve,
             Center = Center,
             CenterCurve = CenterCurve,
             MaskInvert = MaskInvert,
             MaskInvertCurve = MaskInvertCurve,
             MaskRadius = MaskRadius,
             MaskRadiusCurve = MaskRadiusCurve,
             MaskSoftness = MaskSoftness,
             MaskSoftnessCurve =MaskSoftnessCurve
            },Priority,Duration)
end

function PostProcessManager:EnableColorSplitRadialBlur(Layer, AlphaIn, AlphaOut, Duration, Priority,Intensity, IntensityCurve, BlurStride,
                                                     BlurStrideCurve, ColorSplitStride, ColorSplitStrideCurve, Center,
                                                     CenterCurve, BlurMaskRadius, BlurMaskRadiusCurve,BlurMaskSoftness,
                                                     BlurMaskSoftnessCurve,BlurInvert,BlurInvertCurve)



    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.ColorSplitRadialBlur,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
             Intensity = Intensity,
             IntensityCurve = IntensityCurve,
             BlurStride = BlurStride,
             BlurStrideCurve = BlurStrideCurve,
             ColorSplitStride = ColorSplitStride,
             ColorSplitStrideCurve = ColorSplitStrideCurve,
             Center = Center,
             CenterCurve = CenterCurve,
             BlurMaskRadius = BlurMaskRadius,
             BlurMaskRadiusCurve = BlurMaskRadiusCurve,
             BlurMaskSoftness = BlurMaskSoftness,
             BlurMaskSoftnessCurve = BlurMaskSoftnessCurve,
             BlurInvert = BlurInvert,
             BlurInvertCurve = BlurInvertCurve,
            },Priority,Duration)
end

---@param HeadInfoHideDistInMeters number 头顶信息显示距离, 传入nil表示不控制头顶信息
function PostProcessManager:EnableOrUpdateFog(Layer, BlendTime, Priority, FogOpacity, MaxDistInMeters, FogColor, SmoothDistInMeters, HeadInfoHideDistInMeters, bOverrideClimate, SourceType)
    SourceType = SourceType == nil and PostProcessConst.PP_FOG_SOURCE_TYPE.GAME or SourceType
    FogColor = self:ProcessColor(FogColor)
    -- 给个最小值临时处理
    BlendTime = BlendTime ~= nil and math.max(0.001, BlendTime) or 1
    local PPFogID = self.FogPPIDs[SourceType]
    if not PPFogID or not self.PPInstances[PPFogID] then
        PPFogID = Game.PostProcessManager:AddPP(Layer,
                Enum.EPostProcessType.Fog, 
                {
                    AlphaInTime = BlendTime, AlphaOutTime = 0, FogOpacity = FogOpacity, MaxDist = MaxDistInMeters, bOverrideClimate = bOverrideClimate,
                    SmoothDist = SmoothDistInMeters, FogColor = FogColor, BlendTime = BlendTime, HeadInfoHideDistInMeters = HeadInfoHideDistInMeters },
                Priority)
        local PPInstance = self.PPInstances[PPFogID]
        PPInstance.bDestroyWhenLeaveSpace = true
        PPInstance.SourceType = SourceType
        self.FogPPIDs[SourceType] = PPFogID
        return PPFogID
    else
        -- 更新后处理参数
        local FogPP = self.PPInstances[PPFogID]
        if FogPP then
            FogPP:UpdateParams(FogOpacity, MaxDistInMeters, FogColor, SmoothDistInMeters, HeadInfoHideDistInMeters, BlendTime)
        end
    end
end

function PostProcessManager:HasFogPP(SourceType)
    SourceType = SourceType == nil and PostProcessConst.PP_FOG_SOURCE_TYPE.GAME or SourceType
    return self.FogPPIDs[SourceType] ~= nil
end

function PostProcessManager:EnableChromaticAberration(Layer, AlphaIn, AlphaOut, Duration, Priority, Intensity, StartOffset)
    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.ChromaticAberration,
            {AlphaInTime = AlphaIn, AlphaOutTime = AlphaOut,
                Intensity = Intensity,
                StartOffset = StartOffset,
            },Priority,Duration)
end

function PostProcessManager:OverrideFogDistance(DistInMeters, SourceType)
    SourceType = SourceType == nil and PostProcessConst.PP_FOG_SOURCE_TYPE.GAME or SourceType
    local PPFogID = self.FogPPIDs[SourceType]
    if PPFogID then
        local FogPP = self.PPInstances[PPFogID]
        if FogPP then
            FogPP:OverrideFogDistance(DistInMeters)
        end
    end
end

function PostProcessManager:ResetOverrideFogDistance(SourceType)
    SourceType = SourceType == nil and PostProcessConst.PP_FOG_SOURCE_TYPE.GAME or SourceType
    local PPFogID = self.FogPPIDs[SourceType]
    if PPFogID then
        local FogPP = self.PPInstances[PPFogID]
        if FogPP then
            FogPP:ResetOverrideFogDistance()
        end
    end
end

function PostProcessManager:SetFogLocationUpdateActor(Actor, SourceType)
    SourceType = SourceType == nil and PostProcessConst.PP_FOG_SOURCE_TYPE.GAME or SourceType
    local PPFogID = self.FogPPIDs[SourceType]
    if PPFogID then
        local FogPP = self.PPInstances[PPFogID]
        if FogPP then
            FogPP:SetLocationUpdateActor(Actor)
        end
    end
end

function PostProcessManager:SetFogLocationUpdateActorByID(actorID, SourceType)
    local actor = Game.ObjectActorManager:GetObjectByID(actorID)
    self:SetFogLocationUpdateActor(actor, SourceType)
end

function PostProcessManager:RemoveFog(BlendOutTime, SourceType)
    SourceType = SourceType == nil and PostProcessConst.PP_FOG_SOURCE_TYPE.GAME or SourceType
    local PPFogID = self.FogPPIDs[SourceType]
    if PPFogID then
        if BlendOutTime == nil or BlendOutTime == 0 then
            self:RemovePP(PPFogID)
            self.FogPPIDs[SourceType] = nil
        else
            local FogPP = self.PPInstances[PPFogID]
            if FogPP then
                FogPP:UpdateFogOpacity(0.0, BlendOutTime)
            end
        end
    end
end

function PostProcessManager:EnableCustomMaterialPP(Layer, AlphaIn, AlphaOut,Duration, Priority ,MaterialPath,
                                                   BlendScalars, BlendVectors, Textures,
                                                   ScalarCurves, VectorCurves)
    return Game.PostProcessManager:AddPP(Layer,
            Enum.EPostProcessType.CustomMaterial,
            {
                AlphaInTime = AlphaIn,
                AlphaOutTime = AlphaOut,
                MaterialPath = MaterialPath,
                BlendScalars = BlendScalars,
                BlendVectors = BlendVectors,
                Textures = Textures,
                ScalarCurves = ScalarCurves,
                VectorCurves = VectorCurves,
            },Priority,Duration)
end

function PostProcessManager:EnableSketch(Layer, AlphaIn, AlphaOut,Duration, Priority )
	if self.SketchPPID then
		return
	end
	self.SketchPPID = Game.PostProcessManager:AddPP(Layer,
		Enum.EPostProcessType.Sketch,
		{
			AlphaInTime = AlphaIn,
			AlphaOutTime = AlphaOut,
		},Priority,Duration)
    return self.SketchPPID
end

function PostProcessManager:SetSketchCustomDepth(bEnable, TargetID, InsID, EffectType)
	if self.SketchPPID then
        if TargetID == nil and InsID ~= nil then
            local ID = Game.WorldManager:GetNpcByInstance(InsID)
            local Target
            if ID then
                Target = Game.EntityManager:getEntity(ID)
            else
                Target = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(InsID)
            end

            if Target == nil then
                Log.Error("PostProcessManager:SetSketchCustomDepth, TargetID is nil, InsID:", InsID)
                return
            end

            TargetID = Target:uid()
        end
        
		local SketchPP = self.PPInstances[self.SketchPPID]
		if SketchPP then
            if bEnable then
                SketchPP:AddSketchTarget(TargetID, EffectType)
            else
                SketchPP:RemoveSketchTarget(TargetID)
            end
		end
	end
end

function PostProcessManager:DisableSketch()
	if self.SketchPPID then
		self:RemovePP(self.SketchPPID)
	end
end

function PostProcessManager:EnableClip(Layer, AlphaIn, AlphaOut, Duration, Priority, BackgroundColor)
	if self.ClipPPID then
		return
	end
	self.ClipPPID = Game.PostProcessManager:AddPP(Layer,
		Enum.EPostProcessType.Clip,
		{
			AlphaInTime = AlphaIn,
            AlphaOutTime = AlphaOut,
            BackgroundColor = BackgroundColor,
        }, Priority, Duration)
    return self.ClipPPID
end

function PostProcessManager:DisableClip()
    if self.ClipPPID then
        self:RemovePP(self.ClipPPID)
        self.ClipPPID = nil
    end
end

function PostProcessManager:SetClipColor(bUseMask, Color, BlendTimes)
    if self.ClipPPID then
        local ClipPP = self.PPInstances[self.ClipPPID]
        if ClipPP then
            ClipPP:SetClipCharColor(bUseMask, Color, BlendTimes)
        end
    end
end

function PostProcessManager:SetClipCustomDepth(bEnable,TargetID,InsID,CustomDepthStencilValue,bClipNiagara)
	if self.ClipPPID then
		local ClipPP = self.PPInstances[self.ClipPPID]
		if ClipPP then
			ClipPP:SetCustomDepth(bEnable,TargetID,InsID,CustomDepthStencilValue,bClipNiagara)
		end
	end
end


function PostProcessManager:GetCustomDepthSetting(TargetID,InsID)
	if  self.CustomDepthPPPriorityQueue then
		local CustomDepthPPID = self.CustomDepthPPPriorityQueue:Peek()
		if CustomDepthPPID and  self.PPInstances[CustomDepthPPID] then
			return self.PPInstances[CustomDepthPPID]:GetCustomDepthSetting(TargetID,InsID)	
		end
	end
end
--

------------------------------------------------------------------------------------------------------------------------

function PostProcessManager:LuaStructToConst(Name,PPParamData)
	return PPParamData[Name].bConstValue and  PPParamData[Name].Value or nil
end

function PostProcessManager:LuaStructToCurve (Name,PPParamData)
 	return not PPParamData[Name].bConstValue and 
		self:CurveOrCurveAsset(PPParamData[Name].BlendCurve) or nil
end

function PostProcessManager:CurveOrCurveAsset(Curve)
	if Curve == nil then
		return nil
	end
	return (Curve.CurveAsset and type(Curve.CurveAsset)=="string") and Curve.CurveAsset or import("KismetSystemLibrary").GetPathName( Curve.Curve.ExternalCurve)
end

local EEditorConfigPPType =
{
	None = 0,
	CustomMaterial = 1,
	Bloom =2,
	DOF = 3,
	RadialBlur = 4,
	Darken =5,
	Vignette =6,
	ColorAdjust = 7,
	RGBSplit = 8,
	ColorSplitRadialBlur = 9,
	Phantom = 10
}

function PostProcessManager:GetSkillPPTypeNameByEnum(PPType)
	for K,V in pairs(EEditorConfigPPType) do
		if V == PPType then
			return K
		end
	end
	return nil
end

function PostProcessManager:StartPPBySkillPPData(PPData,BlendInTime,BlendOutTime,BlendInCurve,BlendOutCurve,Duration)
	local PPType = PPData.PostProcessType

	if PPType == EEditorConfigPPType.CustomMaterial then
		
		local ScalarsRaw = type(PPData.CustomMaterialParams.Scalars)== "table" and PPData.CustomMaterialParams.Scalars or PPData.CustomMaterialParams.Scalars:ToTable()
		local VectorsRaw =  type(PPData.CustomMaterialParams.Vectors)== "table" and PPData.CustomMaterialParams.Vectors or PPData.CustomMaterialParams.Vectors:ToTable()
		local TexturesRaw =  type(PPData.CustomMaterialParams.Texture)== "table" and PPData.CustomMaterialParams.Texture  or PPData.CustomMaterialParams.Texture:ToTable()


		local Scalars, LoopScalars, Vectors, LoopVectors,Textures = {}, {}, {}, {} , {}

		for _, ScalarStruct in pairs(ScalarsRaw) do
			local ScalarName = ScalarStruct.ParamName
			if ScalarStruct.MaterialParameterType == Enum.MaterialParametersType.ConstantValue then
				Scalars[ScalarName] = {Min = ScalarStruct.Constant, Max = ScalarStruct.Constant}
			elseif ScalarStruct.MaterialParameterType == Enum.MaterialParametersType.CurveValue then
				Scalars[ScalarName] = {Curve =  self:CurveOrCurveAsset(ScalarStruct.Curve)}
			elseif ScalarStruct.MaterialParameterType == Enum.MaterialParametersType.CurveLoop then
				LoopScalars[ScalarName] = {Curve =  self:CurveOrCurveAsset(ScalarStruct.Curve), Duration = ScalarStruct.LoopTime}
			elseif ScalarStruct.MaterialParameterType == Enum.MaterialParametersType.RangeValue then
				Scalars[ScalarName] = {Min = ScalarStruct.Min, Max = ScalarStruct.Max}
			elseif ScalarStruct.MaterialParameterType == Enum.MaterialParametersType.CurveRemap then
				LoopScalars[ScalarName] = {Curve =  self:CurveOrCurveAsset(ScalarStruct.Curve), bRemap = true, bLoop = false, Duration = Duration}
			end
		end

		for _, VectorStruct in pairs(VectorsRaw) do
			local VectorName = VectorStruct.ParamName
			if VectorStruct.MaterialParameterType == Enum.MaterialParametersType.ConstantValue then
				Vectors[VectorName] = {Min = VectorStruct.Constant, Max = VectorStruct.Constant}
			elseif VectorStruct.MaterialParameterType == Enum.MaterialParametersType.CurveValue then
				Vectors[VectorName] = {Curve =  self:CurveOrCurveAsset(VectorStruct.Curve)}
			elseif VectorStruct.MaterialParameterType == Enum.MaterialParametersType.CurveLoop then
				LoopVectors[VectorName] = {Curve =  self:CurveOrCurveAsset(VectorStruct.Curve), Duration = VectorStruct.LoopTime}
			elseif VectorStruct.MaterialParameterType == Enum.MaterialParametersType.RangeValue then
				Vectors[VectorName] = {Min = VectorStruct.Min, Max = VectorStruct.Max}
			elseif VectorStruct.MaterialParameterType == Enum.MaterialParametersType.CurveRemap then
				LoopScalars[VectorName] = {Curve =  self:CurveOrCurveAsset(VectorStruct.Curve), bRemap = true, bLoop = false, Duration = Duration}
			end
		end

		for _, Tex in pairs(TexturesRaw) do
			Textures[Tex.ParamName] = type(Tex.Texture) == "string" and Tex.Texture or Tex.Texture:ToString()
		end


		local MaterialPath = type(PPData.CustomMaterialParams.CustomMaterial) == "string" and PPData.CustomMaterialParams.CustomMaterial or PPData.CustomMaterialParams.CustomMaterial:ToString()

		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.CustomMaterial,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,
				MaterialPath = MaterialPath,
				BlendScalars = Scalars,
				BlendVectors = Vectors,
				Textures = Textures,
				ScalarCurves = LoopScalars,
				VectorCurves = LoopVectors,
				PosUpdateParam = PPData.CustomMaterialParams.MainCharPositionParamName,
				PosUpdateActorID = (Game and Game.me) and Game.me.CharacterID or nil,
				bFixBlendWeight = PPData.CustomMaterialParams.bFixBlendWeight
				
			},
			PPData.Priority,
			-1)


	elseif PPType == EEditorConfigPPType.Bloom then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.Bloom,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,
				BloomParams = 	PPData.BloomParams.BloomParams,
			},	PPData.Priority,-1)

	elseif PPType == EEditorConfigPPType.DOF then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.DOF,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,
				DOFParams = 	PPData.DOFParams.DOFParams,
			},	PPData.Priority,-1)


	elseif PPType == EEditorConfigPPType.Darken then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.Darken,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,
				ExposureBias = PPData.DarkenParams.ExposureBias,
			},
			0,
			-1)


	elseif PPType == EEditorConfigPPType.Vignette then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.Vignette,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,

				Intensity = self:LuaStructToConst("Intensity",PPData.VignetteParams),
				IntensityCurve = self:LuaStructToCurve("Intensity",PPData.VignetteParams),
				Color = self:LuaStructToConst("Color",PPData.VignetteParams),
				ColorCurve = self:LuaStructToCurve("Color",PPData.VignetteParams),
				Radius = self:LuaStructToConst("Radius",PPData.VignetteParams),
				RadiusCurve = self:LuaStructToCurve("Radius",PPData.VignetteParams),
				Softness = self:LuaStructToConst("Softness",PPData.VignetteParams),
				SoftnessCurve = self:LuaStructToCurve("Softness",PPData.VignetteParams),
				Center = self:LuaStructToConst("Center",PPData.VignetteParams),
				CenterCurve = self:LuaStructToCurve("Center",PPData.VignetteParams),
			},
			0,
			-1)


	elseif PPType == EEditorConfigPPType.RadialBlur then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.RadialBlur,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,

				Intensity = self:LuaStructToConst("Intensity",PPData.RadialBlurParams),
				IntensityCurve = self:LuaStructToCurve("Intensity",PPData.RadialBlurParams),
				Stride = self:LuaStructToConst("Stride",PPData.RadialBlurParams),
				StrideCurve = self:LuaStructToCurve("Stride",PPData.RadialBlurParams),
				MaskRadius = self:LuaStructToConst("MaskRadius",PPData.RadialBlurParams),
				MaskRadiusCurve = self:LuaStructToCurve("MaskRadius",PPData.RadialBlurParams),
				MaskInvert = self:LuaStructToConst("MaskInvert",PPData.RadialBlurParams),
				MaskInvertCurve = self:LuaStructToCurve("MaskInvert",PPData.RadialBlurParams),
				Softness = self:LuaStructToConst("Softness",PPData.RadialBlurParams),
				SoftnessCurve = self:LuaStructToCurve("Softness",PPData.RadialBlurParams),
				Center = self:LuaStructToConst("Center",PPData.RadialBlurParams),
				CenterCurve = self:LuaStructToCurve("Center",PPData.RadialBlurParams),
			},
			0,
			-1)


	elseif PPType == EEditorConfigPPType.ColorAdjust then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.ColorAdjust,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,

				Contrast = self:LuaStructToConst("Contrast",PPData.ColorAdjustParams),
				ContrastCurve = self:LuaStructToCurve("Contrast",PPData.ColorAdjustParams),
				Desaturate = self:LuaStructToConst("Desaturate",PPData.ColorAdjustParams),
				DesaturateCurve = self:LuaStructToCurve("Desaturate",PPData.ColorAdjustParams),
				InvertColorR = self:LuaStructToConst("InvertColorR",PPData.ColorAdjustParams),
				InvertColorRCurve = self:LuaStructToCurve("InvertColorR",PPData.ColorAdjustParams),
				InvertColorG = self:LuaStructToConst("InvertColorG",PPData.ColorAdjustParams),
				InvertColorGCurve = self:LuaStructToCurve("InvertColorG",PPData.ColorAdjustParams),
				InvertColorB = self:LuaStructToConst("InvertColorB",PPData.ColorAdjustParams),
				InvertColorBCurve = self:LuaStructToCurve("InvertColorB",PPData.ColorAdjustParams),

				bEnableDesaturateProgress = PPData.ColorAdjustParams.bEnableDesaturateProgress
			},
			0,
			-1)
	elseif PPType == EEditorConfigPPType.RGBSplit then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.RGBSplit,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,

				Intensity = self:LuaStructToConst("Intensity",PPData.RGBSplitParams),
				IntensityCurve = self:LuaStructToCurve("Intensity",PPData.RGBSplitParams),
				Stride = self:LuaStructToConst("Stride",PPData.RGBSplitParams),
				StrideCurve = self:LuaStructToCurve("Stride",PPData.RGBSplitParams),
				Stride = self:LuaStructToConst("Stride",PPData.RGBSplitParams),
				StrideCurve = self:LuaStructToCurve("Stride",PPData.RGBSplitParams),
				Center = self:LuaStructToConst("Center",PPData.RGBSplitParams),
				CenterCurve = self:LuaStructToCurve("Center",PPData.RGBSplitParams),
				MaskInvert = self:LuaStructToConst("MaskInvert",PPData.RGBSplitParams),
				MaskInvertCurve = self:LuaStructToCurve("MaskInvert",PPData.RGBSplitParams),
				MaskRadius = self:LuaStructToConst("MaskRadius",PPData.RGBSplitParams),
				MaskRadiusCurve = self:LuaStructToCurve("MaskRadius",PPData.RGBSplitParams),
				MaskSoftness = self:LuaStructToConst("MaskSoftness",PPData.RGBSplitParams),
				MaskSoftnessCurve = self:LuaStructToCurve("MaskSoftness",PPData.RGBSplitParams),
			},
			0,
			-1)

	elseif PPType == EEditorConfigPPType.ColorSplitRadialBlur then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.ColorSplitRadialBlur,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,

				Intensity = self:LuaStructToConst("Intensity",PPData.ColorSplitRadialBlurParams),
				IntensityCurve = self:LuaStructToCurve("Intensity",PPData.ColorSplitRadialBlurParams),
				BlurStride = self:LuaStructToConst("BlurStride",PPData.ColorSplitRadialBlurParams),
				BlurStrideCurve = self:LuaStructToCurve("BlurStride",PPData.ColorSplitRadialBlurParams),
				ColorSplitStride = self:LuaStructToConst("ColorSplitStride",PPData.ColorSplitRadialBlurParams),
				ColorSplitStrideCurve = self:LuaStructToCurve("ColorSplitStride",PPData.ColorSplitRadialBlurParams),
				Center = self:LuaStructToConst("Center",PPData.ColorSplitRadialBlurParams),
				CenterCurve = self:LuaStructToCurve("Center",PPData.ColorSplitRadialBlurParams),
				BlurMaskRadius = self:LuaStructToConst("BlurMaskRadius",PPData.ColorSplitRadialBlurParams),
				BlurMaskRadiusCurve = self:LuaStructToCurve("BlurMaskRadius",PPData.ColorSplitRadialBlurParams),
				BlurMaskSoftness = self:LuaStructToConst("BlurMaskSoftness",PPData.ColorSplitRadialBlurParams),
				BlurMaskSoftnessCurve = self:LuaStructToCurve("BlurMaskSoftness",PPData.ColorSplitRadialBlurParams),
				BlurInvert = self:LuaStructToConst("BlurInvert",PPData.ColorSplitRadialBlurParams),
				BlurInvertCurve = self:LuaStructToCurve("BlurInvert",PPData.ColorSplitRadialBlurParams),
			},
			0,
			-1)

	elseif PPType == EEditorConfigPPType.Phantom then
		return self:AddPP(
			Enum.EPostProcessLayers.CutScene,
			Enum.EPostProcessType.Phantom,
			{
				AlphaInTime = BlendInTime,
				AlphaOutTime = BlendOutTime,
				AlphaInCurve = BlendInCurve,
				AlphaOutCurve = BlendOutCurve,
				
				Gap = self:LuaStructToConst("Gap", PPData.PhantomParams),
				GapCurve = self:LuaStructToCurve("Gap", PPData.PhantomParams),
				Speed = self:LuaStructToConst("Speed", PPData.PhantomParams),
				SpeedCurve = self:LuaStructToCurve("Speed", PPData.PhantomParams),
				Direction = self:LuaStructToConst("Direction", PPData.PhantomParams),
				DirectionCurve = self:LuaStructToCurve("Direction", PPData.PhantomParams),
			},
			0,
			-1)
	end
end

--测试用指令
--Game.PostProcessManager:EnableCustomMaterialPP(Enum.EPostProcessLayers.World, 2,2,10, 5, true,"/Game/Arts/Effects/FX_Library/FX_Materials/M_PP_EdgeOFScreen_PolarVFX.M_PP_EdgeOFScreen_PolarVFX",{}, {}, {})
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.DOF,{AlphaInTime = 1,AlphaOutTime = 1,FocalRegionUpdateType = Enum.EDOFFocalRegionUpdateType.ByFocalTarget ,TargetID = UOM.GetUGID(Game.me.Character) ,TargetSocket = "head",SensorWidth = 200},0)
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.Bloom,{AlphaInTime = 1,AlphaOutTime = 1,BloomMethod = import("EBloomMethod").BM_FFT ,BloomIntensity =10},0)
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.RadialBlur,{AlphaInTime = 1,AlphaOutTime = 1, Intensity = 1},nil,1)
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.Darken,{AlphaInTime = 1,AlphaOutTime = 1,ExposureBias = 0},nil,1)
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.GI_IndirectLight,{AlphaInTime = 1,AlphaOutTime = 1,IndirectLightingColor = nil,IndirectLightingIntensity = 0},nil,10)
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.Vignette,{AlphaInTime = 1,AlphaOutTime = 1,Color = FLinearColor(1,1,1)},nil,1)
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.ColorAdjust,{AlphaInTime = 1,AlphaOutTime = 1,InverColorR = 1,InvertColorG=1,InverColorB =1},nil,1)
--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.RGBSplit,{AlphaInTime = 1,AlphaOutTime = 1},nil,1)
--Game.PostProcessManager:EnableOrUpdateFog(Enum.EPostProcessLayers.World, 0.5, 0.5, 4, 0, 1, nil,1,nil,FLinearColor(0,0,0), FogColorCurve)
--Game.PostProcessManager:ResetFogDistance(50)
--Game.PostProcessManager:EnableCustomMaterialPP(Enum.EPostProcessLayers.World, 2,2,10, 5, "/Game/Arts/Effects/FX_Library/FX_Materials/M_PP_EdgeOFScreen_PolarVFX.M_PP_EdgeOFScreen_PolarVFX", {}, {}, {},{["distortion"] = {Curve = "/Game/Blueprint/SceneActor/Curve/Curve_DropItem.Curve_DropItem",LoopTime = 1}},{})
--Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.World,nil, 3000001, 0.5  , 0.5, 5)
--Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.World,nil, 13001001, 0.5  , 0.5, 5)
--Game.PostProcessManager:EnableDarken(Enum.EPostProcessLayers.World, 1, 1, 3, -1)
--Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.World, 0, 4000013, 1, 1, 5)
-- Game.PostProcessManager:EnableCustomMaterialPP(Enum.EPostProcessLayers.World, 2,2,10, 5, "/Game/Arts/Effects/FX_Library/FX_Materials/M_PP_EdgeOFScreen_PolarVFX.M_PP_EdgeOFScreen_PolarVFX", {}, {}, {},{["distortion"] = {Curve = "/Game/Blueprint/SceneActor/Curve/Curve_DropItem.Curve_DropItem",Duration = 5}},{})
-- Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.ColorSplitRadialBlur, {AlphaInTime = 0, AlphaOutTime = 0 },nil,2)

--Game.PostProcessManager:AddPP(Enum.EPostProcessLayers.World,Enum.EPostProcessType.ColorAdjust,{AlphaInTime = 1,AlphaOutTime = 1,InverColorR = 1,InvertColorG=1,InverColorB =1,bEnableDesaturateProgress = true},nil,10)


return Class(nil, nil, PostProcessManager)