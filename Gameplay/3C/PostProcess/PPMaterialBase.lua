local PPBase = require "Gameplay.3C.PostProcess.PPBase"
local PPMaterialCore = require "Gameplay.3C.PostProcess.PPMaterialCore"
local PPMaterialBase = DefineClass("PPMaterialBase",PPBase)

PPMaterialBase.EParamType = {
    Scalar = 1,
    Vector = 2,
    Texture = 3,
    ScalarCurve = 4,
    VectorCurve = 5,
	BlendInCurve = 6,
	BlendOutCurve = 7
}

PPMaterialBase.LoadAssetType = {
    Curve = 1,
    Texture = 2,
}

PPMaterialBase.MaterialControlType = {
    CustomMaterialControl = 0,
    CommonMaterialControl = 1,
    MaterialControlByMaterialPath = 2
}

function PPMaterialBase:ctor()
    self.AlphaInTime = 0
    self.AlphaOutTime = 0

    self.IsActive = false

    self.MaterialControl = nil

    self.AssetsToLoad = {}
    self.AssetLoadMap = {}
    self.PendingScalars = {}
    self.PendingVectors = {}
    self.PendingScalarLoops = {}
    self.PendingVectorLoops = {}
    self.PendingTextures = {}
    self.bAsyncLoading = false
end

function PPMaterialBase:dtor()
    self.PendingScalars = {}
    self.PendingVectors = {}
    self.PendingScalarLoops = {}
    self.PendingVectorLoops = {}
    self.PendingTextures = {}
end

function PPMaterialBase:OnMaterialEnable()

end

function PPMaterialBase:OnMaterialDisable()

end

function PPMaterialBase:OnEnable()
    self.IsActive = true
    if self:CanEnable() then
        local CameraModifier = self.PPMaterialCore:GetModifier()
        if CameraModifier then
            CameraModifier:EnableModifier()
            CameraModifier:SetAutoRemoveFromList(true)
        end
        
        self:PushParams()
        self.MaterialControl:SetEnable()
        self:OnMaterialEnable()
    end
end

function PPMaterialBase:OnDisable()
    self.IsActive = false
    local CameraModifier = self.PPMaterialCore:GetModifier()
    if CameraModifier then
        CameraModifier:SetAutoRemoveFromList(false)
        CameraModifier:DisableModifier(true)
    end
    
    if self.MaterialControl then
        self.MaterialControl:SetDisable()
        self:OnMaterialDisable()
    end
end

--覆盖这个函数来初始化材质参数
function PPMaterialBase:OnInitPPMaterial(Params)
end

function PPMaterialBase:GetMaterial()
    return nil
end

function PPMaterialBase:OnInit(Params)
    local ClassCfgMaterial = self:GetMaterial()
    local materialPath = Params.MaterialPath or ClassCfgMaterial
    if materialPath == nil then
        materialPath = Game.PostProcessManager:GetCommonSharedMaterialPath()
    end
	self.bAsyncLoading = false
    self.PPMaterialCore = PPMaterialCore.new()
    self.bFixBlendWeight = Params.bFixBlendWeight
    self.PPMaterialCore:Initialize(
        materialPath,
        Params.AlphaInTime,
        Params.AlphaOutTime,
        Params.bFixBlendWeight ~= true,
        true
    )

    self.PPMaterialCore:GenerateMaterialControl(self, "OnMaterialControlReady", Params)
end


function PPMaterialBase:OnUnInit()
    local DelayRemoveTime = 0
    if self.MaterialControl then
        DelayRemoveTime = self.MaterialControl.AlphaOutTime or 0
        self.MaterialControl:ShutDown(false)
        self.MaterialControl = nil
    end

    if self.PPMaterialCore then
        self.PPMaterialCore:CancelGenerateMaterialControl(self)
    end

    self.PPMaterialCore:OnUnInit(DelayRemoveTime)
    self.PPMaterialCore = nil
end

function PPMaterialBase:CanEnable()
    return self.IsActive and not self.bAsyncLoading and IsValid_L(self.MaterialControl)
end

function PPMaterialBase:OnMaterialControlReady(NewControl,Params)
    self.MaterialControl = NewControl
    self.MaterialControl.Priority = self.Priority

    if Params.AlphaInTime then
        self.MaterialControl:SetAlphaInTime(Params.AlphaInTime) 
    end

    if Params.AlphaOutTime then
        self.MaterialControl:SetAlphaOutTime(Params.AlphaOutTime)
    end

    if Params.AlphaInCurve then
        self:AddBlendInCurve(Params.AlphaInCurve)
    end

    if Params.AlphaOutCurve then
        self:AddBlendOutCurve(Params.AlphaOutCurve)
    end

    self:OnInitPPMaterial(Params)

    if self:CanEnable() then
        self:PushParams()
        self.MaterialControl:SetEnable()
        if self.IsActive then
            self:SetEnabled(true)
        end
    end
end

function PPMaterialBase:PushParams()
    for K,V in pairs(self.PendingScalars) do
        self.MaterialControl.BlendScalars:Add(V)
    end

    self.PendingScalars = {}

    for K,V in pairs(self.PendingVectors) do
        self.MaterialControl.BlendVectors:Add(V)
    end

    self.PendingVectors = {}

    for K,V in pairs(self.PendingScalarLoops) do
        self.MaterialControl.Scalars:Add(V)
    end

    self.PendingScalarLoops = {}

    for K,V in pairs(self.PendingVectorLoops) do
        self.MaterialControl.Vectors:Add(V)
    end

    self.PendingVectorLoops = {}
end

function PPMaterialBase:OnLoadedSuccessfully(InLoadID, Asset)

    local bAllDone = true
    for K, LoadData in pairs(self.AssetLoadMap) do
        if LoadData.LoadID == InLoadID then
            if LoadData.ParamType == PPMaterialBase.EParamType.Scalar and LoadData.Type == PPMaterialBase.LoadAssetType.Curve then
                self.PendingScalars[LoadData.Idx].FloatCurve = Asset
            elseif LoadData.ParamType == PPMaterialBase.EParamType.Vector and LoadData.Type == PPMaterialBase.LoadAssetType.Curve then
                self.PendingVectors[LoadData.Idx].VectorCurve = Asset
            elseif LoadData.ParamType == PPMaterialBase.EParamType.Texture and LoadData.Type == PPMaterialBase.LoadAssetType.Texture then
                self:ApplyTexture(LoadData.ParamName, Asset)
            elseif LoadData.ParamType == PPMaterialBase.EParamType.ScalarCurve and LoadData.Type == PPMaterialBase.LoadAssetType.Curve then
                self.PendingScalarLoops[LoadData.Idx].FloatCurve = Asset
            elseif LoadData.ParamType == PPMaterialBase.EParamType.VectorCurve and LoadData.Type == PPMaterialBase.LoadAssetType.Curve then
                self.PendingVectorLoops[LoadData.Idx].VectorCurve = Asset
            elseif LoadData.ParamType == PPMaterialBase.EParamType.BlendInCurve then
                self:ApplyBlendInCurve(Asset)
            elseif LoadData.ParamType == PPMaterialBase.EParamType.BlendOutCurve then
                self:ApplyBlendOutCurve(Asset)
            end

            LoadData.bLoadDone = true
        end

        if not LoadData.bLoadDone then
            bAllDone = false
            break
        end
    end

    self.bAsyncLoading = not bAllDone

    if self:CanEnable() then
        self:PushParams()
        self.MaterialControl:SetEnable()
        if self.IsActive then
            self:SetEnabled(true)
        end
    end
end


function PPMaterialBase:AddScalar(Name, MinValue, MaxValue,CurveAssetPath)
    local NewPPBlendParam = import("PPScalarMaterialBlendParam")()
    NewPPBlendParam.ParamName = Name
    NewPPBlendParam.MinValue = MinValue or 0
    NewPPBlendParam.MaxValue = MaxValue or 0
    --NewPPBlendParam.FloatCurve = Params.DistScalePowerCurve or nil
    table.insert(self.PendingScalars, NewPPBlendParam)

    if CurveAssetPath ~= nil and CurveAssetPath~= "" then
        local LoadID = self:LoadAsset(CurveAssetPath)
		table.insert(self.AssetLoadMap,
			{
				ParamType = PPMaterialBase.EParamType.Scalar,
				Idx = #self.PendingScalars,
				Type = PPMaterialBase.LoadAssetType.Curve,
				LoadID = LoadID
			})
    end
end


function PPMaterialBase:AddScalarCurve(Name,CurveAssetPath,Duration,bRemap,bLoop)
    local NewPPLoopParam = import("PPScalarMaterial")()
    NewPPLoopParam.ParamName = Name
    NewPPLoopParam.CurTime = 0
    NewPPLoopParam.MaxTime = Duration
    NewPPLoopParam.bRemap = bRemap or true
    NewPPLoopParam.bloop = bLoop or true

    table.insert(self.PendingScalarLoops, NewPPLoopParam)

    if CurveAssetPath ~= nil and CurveAssetPath~= "" then
        local LoadID = self:LoadAsset(CurveAssetPath)
		table.insert(self.AssetLoadMap,
			{
				ParamType = PPMaterialBase.EParamType.ScalarCurve,
				Idx = #self.PendingScalarLoops,
				Type = PPMaterialBase.LoadAssetType.Curve,
				LoadID = LoadID
			})
    end
end

function PPMaterialBase:AddVectorLoopCurve(Name,CurveAssetPath,Duration,bRemap,bLoop)
    local NewPPLoopParam = import("PPVectorMaterial")()
    NewPPLoopParam.ParamName = Name
    NewPPLoopParam.CurTime = 0
    NewPPLoopParam.MaxTime = Duration
    NewPPLoopParam.bRemap = bRemap or true
    NewPPLoopParam.bloop = bLoop or true

    table.insert(self.PendingVectorLoops, NewPPLoopParam)


    if CurveAssetPath ~= nil and CurveAssetPath~= "" then
        local LoadID = self:LoadAsset(CurveAssetPath)
		table.insert(self.AssetLoadMap,
			{
				ParamType = PPMaterialBase.EParamType.VectorCurve,
				Idx = #self.PendingVectorLoops,
				Type = PPMaterialBase.LoadAssetType.Curve,
				LoadID = LoadID
			})
    end
end

function PPMaterialBase:AddVector(Name, MinValue, MaxValue,CurveAssetPath)
    local NewPPBlendParam = import("PPVectorMaterialBlendParam")()
    NewPPBlendParam.ParamName = Name
    if MinValue then
        NewPPBlendParam.MinValue.R = MinValue.X or MinValue.R
        NewPPBlendParam.MinValue.G = MinValue.Y or MinValue.G
        NewPPBlendParam.MinValue.B = MinValue.Z or MinValue.B
        NewPPBlendParam.MinValue.A = MinValue.A or 0
    end

    if MaxValue then
        NewPPBlendParam.MaxValue.R = MaxValue.X or MaxValue.R
        NewPPBlendParam.MaxValue.G = MaxValue.Y or MaxValue.G
        NewPPBlendParam.MaxValue.B = MaxValue.Z or MaxValue.B
        NewPPBlendParam.MaxValue.A = MaxValue.A or 0
    end

    table.insert(self.PendingVectors, NewPPBlendParam)

    if CurveAssetPath ~= nil and CurveAssetPath~= "" then
        local LoadID = self:LoadAsset(CurveAssetPath)
		table.insert(self.AssetLoadMap,
			{
				ParamType = PPMaterialBase.EParamType.Vector,
				Idx = #self.PendingVectors,
				Type = PPMaterialBase.LoadAssetType.Curve,
				LoadID = LoadID
			})
    end
end


function PPMaterialBase:AddTexture(Name, TexName)
    if TexName ~= nil and TexName~= "" then
        local LoadID = self:LoadAsset(TexName)
		table.insert(self.AssetLoadMap,
			{
                ParamType = PPMaterialBase.EParamType.Texture,
				ParamName = Name,
				Type = PPMaterialBase.LoadAssetType.Texture,
				LoadID = LoadID
			})
    end
end

function PPMaterialBase:ApplyTexture(Name, Tex)
    if self.PPMaterialCore then
        self.PPMaterialCore:ApplyTexture(Name, Tex)
    end
end

function PPMaterialBase:ApplyBlendInCurve(CurveAsset)
    local Modifier = self.PPMaterialCore:GetModifier()
    if Modifier then
        Modifier:SetAlphaInCurve(CurveAsset)
    end
end

function PPMaterialBase:ApplyBlendOutCurve(CurveAsset)
    local Modifier = self.PPMaterialCore:GetModifier()
    if Modifier then
        Modifier:SetAlphaOutCurve(CurveAsset)
    end
end


function PPMaterialBase:AddBlendInCurve(CurvePath)
	if CurvePath and CurvePath~="" then
		local LoadID = self:LoadAsset(CurvePath)
		table.insert(self.AssetLoadMap,
			{
				Type = PPMaterialBase.LoadAssetType.BlendInCurve,
				LoadID = LoadID
			})
	end
end

function PPMaterialBase:AddBlendOutCurve(CurvePath)
	if CurvePath and CurvePath~="" then
		local LoadID = self:LoadAsset(CurvePath)
		table.insert(self.AssetLoadMap,
			{
				Type = PPMaterialBase.LoadAssetType.BlendOutCurve,
				LoadID = LoadID
			})
	end

end

function PPMaterialBase:SetBlendOutTime(InTime)
	if self.MaterialControl then
		self.MaterialControl:SetAlphaOutTime(InTime)
	end
end


return PPMaterialBase
