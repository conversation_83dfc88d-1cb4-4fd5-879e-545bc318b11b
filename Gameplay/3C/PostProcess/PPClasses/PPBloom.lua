local EKGPostProcessTypes = import("EKGPostProcessTypes")

local PPBase = require "Gameplay.3C.PostProcess.PPBase" 
local PPBloom = DefineClass("PPBloom", PPBase)


--params:
--  BloomParams FPPBloomParams 泛光详细参数
--  AlphaInTime 渐入时间
--  AlphaInTime 渐出时间
function PPBloom:OnInit(Params)
    if self:BindModifier(EKGPostProcessTypes.Bloom) then
        local CameraModifier = self:GetModifier()

		--CameraModifier:SetParams(Params.BloomParams)
		-- Params.BloomParams既可能是UStruct也可能是table, 当类型为table时, 设置SetParams会出现类型转化失败, 后续这里要重构, 先临时处理为将每个需要的参数
		-- 独立设置给cpp
		local TargetParams = CameraModifier.Params
		local BloomParams = Params.BloomParams
		
		if BloomParams.bOverride_BloomMethod then
			TargetParams.bOverride_BloomMethod = true
			TargetParams.BloomMethod = BloomParams.BloomMethod or 1
		end

		if BloomParams.bOverride_BloomIntensity then
			TargetParams.bOverride_BloomIntensity = true
			TargetParams.BloomIntensity = BloomParams.BloomIntensity or 0
		end

		if BloomParams.bOverride_BloomThreshold then
			TargetParams.bOverride_BloomThreshold = true
			TargetParams.BloomThreshold = BloomParams.BloomThreshold or 0
		end

		if BloomParams.bOverride_BloomSizeScale then
			TargetParams.bOverride_BloomSizeScale = true
			TargetParams.BloomSizeScale = BloomParams.BloomSizeScale or 0
		end
		
        CameraModifier:SetPriority(self.Priority)
        CameraModifier:SetAutoRemoveFromList(false)
        CameraModifier:DisableModifier(true)                  --默认不开启，调用Enable后才开启
        CameraModifier:SetAlphaInTime(Params.AlphaInTime or 0)
        CameraModifier:SetAlphaOutTime(Params.AlphaOutTime or 0)

		self:AddBlendInCurve(Params.AlphaInCurve)
		self:AddBlendOutCurve(Params.AlphaOutCurve)
    end

end



return PPBloom