local PPMaterialBase = require "Gameplay.3C.PostProcess.PPMaterialBase"
local PPMPhantom = DefineClass("PPMPhantom", PPMaterialBase)
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

PPMPhantom.MaterialPath = ViewResourceConst.PPMPHANTOM.MaterialPath
-- 高以及高以上走后处理表现, 否则走退化的特效表现
PPMPhantom.PPQualityThreshold = 2
PPMPhantom.EffectPath = ViewResourceConst.PPMPHANTOM.EffectPath

function PPMPhantom:ctor()
end

function PPMPhantom:dtor()
end

function PPMPhantom:OnEnable()
	if self.bUsePostProcess then
		PPMaterialBase.OnEnable(self)

		local CameraModifier = self.PPMaterialCore:GetModifier()
		CameraModifier:SetAutoRemoveFromList(true)
		CameraModifier:EnableModifier()

	elseif Game.me then
		self.EffectEntityID = Game.me:PlayNiagaraEffectAttached(
			PPMPhantom.EffectPath,
			"Root",
			FTransform(),
			nil,
			false,
			false,
			-1,
			false)
	end
end

function PPMPhantom:OnDisable()
	if self.EffectEntityID then
		Game.me:DeactivateNiagaraSystem(self.EffectEntityID)
		self.EffectEntityID = nil
	end

	if self.bUsePostProcess then
		local CameraModifier = self.PPMaterialCore:GetModifier()
		CameraModifier:SetAutoRemoveFromList(false)
		CameraModifier:DisableModifier(true)

		PPMaterialBase.OnDisable(self)
	end
end

local DefaultDir = FLinearColor(0.5, 0.5, -0.5, -0.5)

function PPMPhantom:OnInitPPMaterial(Params)
	self:AddScalar("Int", 0, 1, Params.IntensityCurve)
	self:AddScalar("jiange", Params.Gap or 40, Params.Gap or 40, Params.GapCurve)
	self:AddScalar("Speed", Params.Speed or 0.2, Params.Speed or 0.2, Params.SpeedCurve)
	self:AddVector("Direction", Params.Direction or DefaultDir, Params.Direction or DefaultDir, Params.DirectionCurve)

	-- 这里后续按需扩展
	if Game.me and Game.me.eid then
		self:SetCustomDepth(true, Game.me.eid, nil, 0)
	end
end

function PPMPhantom:OnInit(Params)
	self:UpdateUsePostProcess()

	PPMaterialBase.OnInit(self, Params)

	local CameraModifier = self.PPMaterialCore:GetModifier()
	CameraModifier:SetAutoRemoveFromList(false)
	CameraModifier:DisableModifier(true)

	Game.GlobalEventSystem:AddListener(EEventTypesV2.SETTING_ON_POST_PROCESS_QUALITY_CHANGE, "OnPostProcessQualityChange", self)
end

function PPMPhantom:OnUnInit()
	Game.GlobalEventSystem:RemoveListener(EEventTypesV2.SETTING_ON_POST_PROCESS_QUALITY_CHANGE, "OnPostProcessQualityChange", self)

	PPMaterialBase.OnUnInit(self)
end

function PPMPhantom:OnPostProcessQualityChange()
	self:OnDisable()
	self:UpdateUsePostProcess()
	self:OnEnable()
end

function PPMPhantom:UpdateUsePostProcess()
	local PPQuality
	if Game.SettingsManager and Game.SettingsManager.ReadSettingByConfig then
		PPQuality = Game.SettingsManager:ReadSettingByConfig(Enum.ESettingConstData.PP_QUALITY)
	end

	if PPQuality == nil or PPQuality >= PPMPhantom.PPQualityThreshold then
		self.bUsePostProcess = true
	else
		self.bUsePostProcess = false
	end
end

function PPMPhantom:GetMaterial()
	return PPMPhantom.MaterialPath
end

function PPMPhantom:IsCustomDepthControl()
	return true
end

return PPMPhantom
