local PPMaterialBase = require "Gameplay.3C.PostProcess.PPMaterialBase"
local PPMCustomMaterial = DefineClass("PPMCustomMaterial", PPMaterialBase)


function PPMCustomMaterial:ctor()
end

function PPMCustomMaterial:dtor()
end


function PPMCustomMaterial:OnInitPPMaterial(Params)
	-- 目前这里既可能来自于技能(raw table), 也可能来自于配表数据
	local tpairs = Params.bTableConfig and ksbcpairs or pairs
	
    if Params.BlendScalars then
        for Name, Param in tpairs(Params.BlendScalars) do
            self:AddScalar(Name, Param.Min or 0, Param.Max or 0, Param.Curve)
        end
    end

    if Params.BlendVectors then
        for Name, Param in tpairs(Params.BlendVectors) do
            self:AddVector(Name, Param.Min , Param.Max, Param.Curve)
        end
    end

    if Params.Textures then
        for Name, TexName in tpairs(Params.Textures) do
            self:AddTexture(Name, TexName)
        end
    end
	
    if Params.ScalarCurves then
        for Name, Param in tpairs(Params.ScalarCurves) do
            self:AddScalarCurve(Name, Param.Curve, Param.Duration,Param.bRemap, Param.bLoop )
        end
    end

    if Params.VectorCurves then
        for Name, Param in tpairs(Params.VectorCurves) do
            self:AddVectorCurve(Name, Param.Curve, Param.Duration,Param.bRemap, Param.bLoop )
        end
    end

    if Params.PlayerPos then
        for Name, _ in tpairs(Params.PlayerPos) do
            if Game.me and Game.ObjectActorManager:GetObjectByID(Game.me.CharacterID) then
                self.MaterialControl.LocationUpdateActor = Game.ObjectActorManager:GetObjectByID(Game.me.CharacterID)
                self.MaterialControl.LocationUpdateParamName = Name
            end
            break
        end
    end

	if Params.PosUpdateParam and Params.PosUpdateParam~= "" and Params.PosUpdateActorID then
		local Actor = Game.ObjectActorManager:GetObjectByID(Params.PosUpdateActorID)
		if Actor then
			self.MaterialControl.LocationUpdateActor = Game.ObjectActorManager:GetObjectByID(Params.PosUpdateActorID)
			self.MaterialControl.LocationUpdateParamName = Params.PosUpdateParam
		end
	end
	
end

return PPMCustomMaterial
