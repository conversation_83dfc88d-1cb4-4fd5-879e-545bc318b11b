local SubsystemBlueprintLibrary = import("SubsystemBlueprintLibrary")
local CrowdNpcControlSubsystem = import("CrowdNpcControlSubsystem")
local ECrowdOverlapPointType = import("ECrowdOverlapPointType")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

local MassCharacterManager= DefineClass("MassCharacterManager", ManagerBase)


MassCharacterManager.MaxBubbleNum = 3

function MassCharacterManager:onInit()
	self.CrowdNpcMap = {}
	self.alphaTexturePoint = {}
	
	--TODO:PQ 现在的氛围NPC浓度计算有点乱，C++插件一块逻辑，lua一块逻辑，理论讲都应该整合到lua去做
	-- MassNPC浓度系数, 时间，天气，性能
	self.CurTimeScale = 1.0
	self.CurClimateScale = 1.0
	self.CurPerformanceScale = 1.0
	
	-- 已注册的马车自行车Trigger的EntityID
	self.TriggerIDMap = {}
	-- 记录正在与某个Npc交互的Mass
	self.NpcInteractorCnt = {}
	
	self.BubbleEntityLst = {}
	
	Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_LEVEL_LOADED, "OnLevelLoaded", self)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_CLIMATE_CHANGE, "OnClimateChangeToMass", self)
end

function MassCharacterManager:onUnInit()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function MassCharacterManager:OnWorldMapLoadComplete(_)
	self:ClearInterestPoints()
	
	local TriggerPointData = Game.WorldDataManager:GetCurLevelAllSceneActorDataByType(EWActorType.MASS_NPC_TRIGGER_POINT)
	for _, TriggerPointConf in pairs(TriggerPointData) do
		local TargetPosition = TriggerPointConf.Transform.Position
		local Radius = TriggerPointConf.Radius
		local PointType = TriggerPointConf.PointType
		if TargetPosition and Radius and PointType then
			if PointType == ECrowdOverlapPointType.Custom then
				self:RegTexturePoint(TargetPosition, Radius)
			else
				self:RegInterestPoint(PointType, TargetPosition, Radius)
			end
		end
	end
end

function MassCharacterManager:OnLevelLoaded()
	-- 重新计算浓度
	local val = Game.SettingsManager:GetSetting(Enum.ESettingDataEnum.ShadingQuality)
	self:OnShadingQualityChange(val)
end


function MassCharacterManager:OnWorldMapDestroy()
	self:ClearInterestPoints()
end

--获取所有的MassAI的角色
function MassCharacterManager:GetMassMap()
    return self.CrowdNpcMap
end

function MassCharacterManager:bMassLocalEntity(InEntityEID)
	local CrowdNPC = Game.EntityManager:getEntity(InEntityEID)
    return table.contains(self.CrowdNpcMap, CrowdNPC:uid())
end

function MassCharacterManager:RegMassCharacter(InEntity)
    table.insert(self.CrowdNpcMap, InEntity:uid())
end

function MassCharacterManager:UnRegMassCharacter(InEntity)
	local EntityUID = InEntity:uid()
	if table.contains(self.BubbleEntityLst, EntityUID) then
		table.removeItem(self.BubbleEntityLst, EntityUID)
	end
    table.removeItem(self.CrowdNpcMap, EntityUID)
	for _, MassEntityUIDs in pairs(self.NpcInteractorCnt) do
		MassEntityUIDs[EntityUID] = nil
	end
end

function MassCharacterManager:SetVisibleActors(bVisible)
    local CrowdNpcControlSubSys = SubsystemBlueprintLibrary.GetWorldSubsystem(_G.GetContextObject(), CrowdNpcControlSubsystem)
    if CrowdNpcControlSubSys then
        CrowdNpcControlSubSys:SetAllEntitiesHidden(not bVisible)
    end
    --Log.Debug("LYL DEBUG SetVisibleActors ", bVisible)
end

function MassCharacterManager:ClaimSmartObjectSlotByActor(ClaimEntityUid, InteractorUid, Index, bRelease)
	local CrowdNpcControlSubsys = SubsystemBlueprintLibrary.GetWorldSubsystem(_G.GetContextObject(), CrowdNpcControlSubsystem)
	if CrowdNpcControlSubsys then
		local ClaimEntity = Game.EntityManager:getEntity(ClaimEntityUid)
		if not ClaimEntity then
			return
		end
		local SmartObjectUserID = ClaimEntity.CharacterID
		if not IsValidID(SmartObjectUserID) then
			return
		end
		local InteractorEntity = Game.EntityManager:getEntity(InteractorUid)
		if not InteractorEntity then
			return
		end
		local SmartObjectOwnerID = InteractorEntity.CharacterID
		if not SmartObjectOwnerID then
			return
		end
		if bRelease then
			CrowdNpcControlSubsys:ReleaseSmartObjectSlotByActor(SmartObjectUserID, SmartObjectOwnerID, Index - 1)
		else
			CrowdNpcControlSubsys:ClaimSmartObjectSlotByActor(SmartObjectUserID, SmartObjectOwnerID, Index - 1)
		end
	end
end

function MassCharacterManager:OnClimateChangeToMassManager(ClimateScaleRatio)
	self.CurClimateScale = ClimateScaleRatio
	self:RefreshMassScaleRatio()
end

function MassCharacterManager:OnShadingQualityChange(Val)
	self.CurPerformanceScale = Game.TableData.GetCrowdNPCPerformanceSettingDataRow(Val).Value
	self:RefreshMassScaleRatio()
end

-- TODO: PQ c++函数命名要改
function MassCharacterManager:RefreshMassScaleRatio()
	local CrowdNpcControlSubSys = SubsystemBlueprintLibrary.GetWorldSubsystem(_G.GetContextObject(), CrowdNpcControlSubsystem)
	if CrowdNpcControlSubSys then
		CrowdNpcControlSubSys:SetClimateScaleRatio(self.CurTimeScale * self.CurClimateScale * self.CurPerformanceScale)
	end
end

function MassCharacterManager:RegInterestPoint(PointType, PointPos, TriggerRadius)
	-- 注意的是, Mass的位置信息在计算时是贴地的, 所以如果用Entity的GetPosition, 需要考虑胶囊体的半高
	local CrowdNpcControlSubSys = SubsystemBlueprintLibrary.GetWorldSubsystem(_G.GetContextObject(), CrowdNpcControlSubsystem)
	if CrowdNpcControlSubSys then
		if not TriggerRadius then
			CrowdNpcControlSubSys:UnRegInterestPoint(PointType, PointPos)
		else
			CrowdNpcControlSubSys:RegInterestPoint(PointType, PointPos, TriggerRadius)
		end
	end
end

function MassCharacterManager:ClearInterestPoints()
	local CrowdNpcControlSubSys = SubsystemBlueprintLibrary.GetWorldSubsystem(_G.GetContextObject(), CrowdNpcControlSubsystem)
	if CrowdNpcControlSubSys then
		CrowdNpcControlSubSys:ClearInterestPoints()
	end
end

function MassCharacterManager:RegTexturePoint(InPointPos, InRadius)
	if not InRadius then
		InRadius = 300
	end
	if not table.contains(self.alphaTexturePoint, InPointPos) then
		table.insert(self.alphaTexturePoint, InPointPos)
		self:RegInterestPoint(ECrowdOverlapPointType.Custom, InPointPos, InRadius)
	end
end

function MassCharacterManager:UnRegTexturePoint(InPointPos)
	if table.contains(self.alphaTexturePoint, InPointPos) then
		table.removeItem(self.alphaTexturePoint, InPointPos)
		self:RegInterestPoint(ECrowdOverlapPointType.Custom, InPointPos)
	end
end

function MassCharacterManager:IsValidTexturePoint(InPointPos)
	return table.contains(self.alphaTexturePoint, InPointPos)
end

function MassCharacterManager:OnNpcEnterWorld(UID, CharacterID)
	local NPC = Game.EntityManager:getEntity(UID)
	if not NPC then
		return
	end

	if NPC.NpcType == Enum.ENpcTypeData.Vehicle then
		NPC:RegisterGridLogicTrigger(
			true, tonumber(Game.TableData.GetCrowdNPCConstDataRow(Enum.ECrowdNPCConst.CarriageHitRadius).Value),
			WorldViewConst.WORLD_GRID_TRIGGER_LOGIC_ENUM.MASSAI_CROWD_INTERESTING
		)
	
		self.TriggerIDMap[UID] = true
	else
		local NpcReactingData = Game.TableData.GetCrowdNpcReactingDataRow(NPC.TemplateID)
		if NpcReactingData then
			self.TriggerIDMap[UID] = true
			-- todo 这里建议做成自己独立的逻辑, 这样回调能够精准的进行调用到对应Entity上
			NPC:RegisterGridLogicTrigger(
				true, 
				NpcReactingData.Radius,
				WorldViewConst.WORLD_GRID_TRIGGER_LOGIC_ENUM.MASSAI_CROWD_INTERESTING
			)
		
		end
	end
end

function MassCharacterManager:OnNpcExitWorld(UID, CharacterID)
	if self.TriggerIDMap[UID] then
		self.TriggerIDMap[UID] = nil
	end
end

function MassCharacterManager:OnTriggerStateChanged(UID, TargetUID, bEnter)
	if not self.TriggerIDMap[TargetUID] then
		return
	end
	
	local CrowdNPC = Game.EntityManager:getEntity(UID)
	if not CrowdNPC or CrowdNPC:IsInvisible() then
		return
	end
	
	local TargetEntity = Game.EntityManager:getEntity(TargetUID)
	if not TargetEntity or TargetEntity:IsInvisible() then
		return
	end
	
	if bEnter then
		if TargetEntity.NpcType == Enum.ENpcTypeData.Vehicle then
			CrowdNPC:OnStartFleeAnim(UID, TargetUID, nil, true)
		else
			local TargetTemplateID = TargetEntity.TemplateID
			local NpcReactingData = Game.TableData.GetCrowdNpcReactingDataRow(TargetEntity.TemplateID)
			if not self.NpcInteractorCnt[TargetTemplateID] then
				self.NpcInteractorCnt[TargetTemplateID] = {}
			end
			if table.contains(self.NpcInteractorCnt[TargetTemplateID], UID) then
				return
			end
			if #self.NpcInteractorCnt[TargetTemplateID] >= NpcReactingData.MaxReactNum then
				return
			end
			
			-- 动作表现
			local ReactAnimList = NpcReactingData.ReactAnimLib
			if #ReactAnimList > 0 then
				CrowdNPC:OnStartApproachAnim(UID, TargetUID, ReactAnimList[math.random(1, #ReactAnimList)])
			end
			-- 气泡与语音
			self:ReqBubble(CrowdNPC.eid, NpcReactingData.BubbleText, NpcReactingData.BubbleVoice)
		end
	end
end

function MassCharacterManager:OnNotifyStartApproachAnim(UID, TemplateID)
	if not self.NpcInteractorCnt[TemplateID] then
		return
	end
	if table.contains(self.NpcInteractorCnt[TemplateID], UID) then
		return
	end
	table.insert(self.NpcInteractorCnt[TemplateID], UID)
end

function MassCharacterManager:OnNotifyFinishApproachAnim(UID, TemplateID)
	if not self.NpcInteractorCnt[TemplateID] then
		return
	end
	table.removeItem(self.NpcInteractorCnt[TemplateID], UID)
end

function MassCharacterManager:ClearEntityReactInfo(UID, TemplateID)
	self:OnNotifyFinishApproachAnim(UID, TemplateID)
end

function MassCharacterManager:ReqBubble(EntityEID, BubbleText, BubbleVoice)
	if #self.BubbleEntityLst >= MassCharacterManager.MaxBubbleNum then
		return false
	end
	local CrowdNPC = Game.EntityManager:getEntity(EntityEID)
	if not CrowdNPC then
		return false
	end
	local EntityUID = CrowdNPC:uid()
	if table.contains(self.BubbleEntityLst, EntityUID) then
		return false
	end
	table.insert(self.BubbleEntityLst, EntityUID)
	
	Game.HeadInfoManager:SetBubbleByEntityID(EntityEID, BubbleText, 2.0 * 1000, false, 0, BubbleVoice, nil, function()
		if table.contains(self.BubbleEntityLst, EntityUID) then
			table.removeItem(self.BubbleEntityLst, EntityUID)
		end
	end)
	return true
end

function MassCharacterManager:OnClimateChangeToMass(climateData)
	-- 遍历所有crowdNPC
	for _, uid in pairs(self.CrowdNpcMap) do
		local entity = Game.EntityManager:getEntity(uid)
		if entity then
			entity:OnClimateChangeToMass(climateData)
		end
	end
end

--region MassNPC生命周期管理
function MassCharacterManager:OnCrowdNPCEnterISM(bInISM, actorID, suitLibKey, bodyType)
	local entity = Game.UEActorManager:GetLuaEntityByActorID(actorID)
	if not bInISM then
		if entity then
			Log.ErrorFormat("CrowdNpc: Already has Entity When not bInISM!!! %s ", entity.eid)
			return
		end
		Game.EntityManager:CreateLocalEntity("CrowdNpc", {
			PreCreateCharacterID = actorID,
			SuitLibKey = suitLibKey,
			BodyType = bodyType,
		})
	else
		if entity then
			entity:destroy()
		end
	end
end
--endregion

return MassCharacterManager
