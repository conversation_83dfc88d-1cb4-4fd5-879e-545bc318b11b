local ULLFunc = import("LowLevelFunctions")
local DebugFlag = require("Gameplay.Debug.DebugFlag")

AnimLibHelper = DefineClass("AnimLibHelper")


function AnimLibHelper.Init()
	AnimLibHelper.AnimIDMap = kg_require("Data.Config.Anim.AnimIDMap")
	AnimLibHelper.AnimLibData = kg_require("Data.Config.Anim.AnimLibData")

	AnimLibHelper.AnimLibDataV2 = kg_require("Data.Config.Anim.AnimLibDataV2")
	AnimLibHelper.AnimIDMapV2 = kg_require("Data.Config.Anim.AnimIDMapV2")

	AnimLibHelper.AnimFeatureMapping = kg_require("Data.Config.Anim.AnimFeatureMapping")

	if _G.UE_EDITOR then
		AnimLibHelper.AnimLibPartData = kg_require("Data.Config.Anim.AnimLibPartDataForEditor")
	end
end

function AnimLibHelper.ReloadLuaData()
	AnimLibHelper.AnimIDMap = kg_require("Data.Config.Anim.AnimIDMap", true)
	AnimLibHelper.AnimLibData = kg_require("Data.Config.Anim.AnimLibData", true)

	AnimLibHelper.AnimFeatureMapping = kg_require("Data.Config.Anim.AnimFeatureMapping", true)

	AnimLibHelper.AnimLibDataV2 = kg_require("Data.Config.Anim.AnimLibDataV2", true)
	AnimLibHelper.AnimIDMapV2 = kg_require("Data.Config.Anim.AnimIDMapV2", true)

	if _G.UE_EDITOR then
		AnimLibHelper.AnimLibPartData = kg_require("Data.Config.Anim.AnimLibPartDataForEditor", true)
	end
end

function AnimLibHelper.GetAnimAssetPathFromAnimID(AnimID)
	if not AnimID then
		Log.Error("AnimLibData:GetAnimAssetPathFromAnimID Get nil AnimID!")
		return nil
	end

	if type(AnimID) == "string" then
		return AnimID
	end

	local AnimIDMap = AnimLibHelper.AnimIDMap
	if AnimID > 200000000 then
		AnimIDMap = AnimLibHelper.AnimIDMapV2
	end
	
	local AnimPath = AnimIDMap[AnimID]
	if AnimPath == "" then
		return nil
	end

	return AnimPath
end

function AnimLibHelper.GetAnimPreLoadDataForLocomotionABP(AnimConfigID)
	if not AnimConfigID then
		return nil, nil
	end
	local AnimData = AnimLibHelper.GetAnimTypeData(AnimConfigID, "PreLoadAnims")
	if not AnimData then
		return nil, nil
	end

	local PreLoadData = AnimData.AnimsForABP
	local PreLoadAnims = PreLoadData.AnimAssets
	if next(PreLoadAnims) == nil then
		return nil, nil
	end

	return PreLoadData.AnimTypes, PreLoadAnims
end


function AnimLibHelper.GetAnimPreLoadPathsForAction(AnimConfigID)
	if not AnimConfigID then
		return nil
	end
	local AnimData = AnimLibHelper.GetAnimTypeData(AnimConfigID, "PreLoadAnims")
	if not AnimData then
		return nil
	end

	local PreLoadData = AnimData.AnimsForAction
	local PreLoadAnims = PreLoadData.AnimAssets
	if next(PreLoadAnims) == nil then
		return nil
	end

	return PreLoadAnims
end

function AnimLibHelper.GetAnimPreLoadPathsForPerformAdditive(AnimConfigID)
	if not AnimConfigID then
		return nil, nil
	end
	local AnimData = AnimLibHelper.GetAnimTypeData(AnimConfigID, "PreLoadAnims")
	if not AnimData then
		return nil
	end

	local PreLoadData = AnimData.AnimsForPerformAdditive
	local PreLoadAnims = PreLoadData.AnimAssets
	if next(PreLoadAnims) == nil then
		return nil
	end

	return PreLoadAnims
end


--编辑器用，返回单个Stage动画时长
function AnimLibHelper.GetEntityAnimStageLen(ModelID, AnimType, StageName)
	local TargetData = Game.ActorAppearanceManager:GetModelLibData(ModelID)
	local AnimDataID = nil
	if TargetData and TargetData.AnimFlag then
		AnimDataID = TargetData.AnimFlag
	end

	if AnimDataID then
		local AnimLibData = AnimLibHelper.GetAnimFeatureData(AnimDataID, AnimType)
		if AnimLibData then
			if AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.Sequence or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimWithAttachItems then
				return AnimLibData.AnimLen
			elseif AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.ThreePhaseAnim or
				AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.ThreePhaseAnimWithAttach or
				AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimList or
				AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimListWithAttach then
				if AnimLibData.AnimItems[StageName] then
					return AnimLibData.AnimItems[StageName].AnimLen
				end
			end
		end
	end

	return nil
end

function AnimLibHelper.GetAnimTypeData(AnimDataID, Key, bNotReport)
	if not AnimDataID then
		return nil
	end

	local AnimLibDataTable = AnimLibHelper.AnimLibDataV2

	local NewAnimData = AnimLibHelper.AnimFeatureMapping[AnimDataID]
	if NewAnimData and NewAnimData[Key] then
		Log.DebugFormat("AnimLibData:GetAnimTypeData Mapping AnimID:%s-->AnimID:%s, AnimTypeName:%s-->AnimTypeName:%s", AnimDataID, NewAnimData[Key][1], Key, NewAnimData[Key][2])
		AnimDataID = NewAnimData[Key][1]
		Key = NewAnimData[Key][2]
	end

	local AnimData = AnimLibDataTable[AnimDataID]
	local Result = nil
	if AnimData then
		Result = AnimData[Key]
		if not Result then
			local Profile = AnimData.Profile
			while (Profile ~= "" and not Result) do
				local ProfileData = AnimLibDataTable[Profile]
				Result = ProfileData[Key]
				Profile = ProfileData.Profile
			end
			if Result then
				AnimData[Key] = Result
			end
		end
	end

	if not Result then
		AnimLibDataTable = AnimLibHelper.AnimLibData
		AnimData = AnimLibDataTable[AnimDataID]
		if AnimData then
			Result = AnimData[Key]
			if not Result then
				local Profile = AnimData.Profile
				while (Profile ~= "" and not Result) do
					local ProfileData = AnimLibDataTable[Profile]
					Result = ProfileData[Key]
					Profile = ProfileData.Profile
				end
				if Result then
					AnimData[Key] = Result
				end
			end
		end
	end

	if not Result and not bNotReport then
		Log.WarningFormat("AnimLibData:GetAnimTypeData Get nil AnimTypeData AnimDataID:%s, AnimTypeName:%s", AnimDataID, Key)
	end

	return Result
end

-- 返回AnimFeatureData
function AnimLibHelper.GetAnimFeatureData(AnimDataID, AnimType)
	if not AnimDataID then
		return nil
	end

	local Result = AnimLibHelper.GetAnimTypeData(AnimDataID, AnimType, true)

	if Result then
		Result = Result.AnimFeatureData
	end

	if not Result then
		Log.WarningFormat("AnimLibData: Can Not Find Anim Feature Data. AnimDataID:%s AnimType:%s", AnimDataID, AnimType)
	end

	return Result
end



function AnimLibHelper.GetBeHitRandomIDs(AnimDataID, HitType)
	if not AnimDataID or not HitType then
		return nil
	end

	local BeHitRandomIDs = AnimLibHelper.GetAnimTypeData(AnimDataID, "BeHitRandomIDs")
	if BeHitRandomIDs then
		return BeHitRandomIDs[HitType]
	end

	return nil
end

function AnimLibHelper.GetAttachInfoFromLeadingAnimType(LeadingAnimID, AnimTypeName, AnimState)
	local AnimFeatureData = AnimLibHelper.GetAnimFeatureData(LeadingAnimID, AnimTypeName)
	
	local AttachItems = AnimFeatureData.AttachItems
	if not AttachItems or next(AttachItems) == nil then
		return nil, nil
	end

	local Result = {}
	for _, AttachInfo in pairs(AttachItems) do
		local AttachAnim = AttachInfo.AnimTypeName
		if not AttachAnim then
			AnimState = AnimState or AnimFeatureData.StartWith
			AttachAnim = AttachInfo.StateAnimTypes[AnimState]
		end
		if AttachAnim then
			local ModelData = Game.ActorAppearanceManager:GetModelLibData(AttachInfo.ModelID)
			if ModelData and ModelData.AnimFlag then
				local AttachAnimData = AnimLibHelper.GetAnimFeatureData(ModelData.AnimFlag, AttachAnim)
				if AttachAnimData and AttachAnimData.Anim then
					AttachAnim = AnimLibHelper.GetAnimAssetPathFromAnimID(AttachAnimData.Anim)
				end
			end
		end
		Result[#Result + 1] = {AttachInfo.ModelID, AttachAnim}
	end
	
	return Result
end

-- 返回AnimFeatureData  仅供编辑器使用
function AnimLibHelper.GetAnimFeatureDataFromPartDataOnlyEditor(AnimPartID, AnimType)
	if not AnimLibHelper.AnimLibPartData then
		return nil
	end

	local Result = nil
	local AnimData = AnimLibHelper.AnimLibPartData[AnimPartID]
	if AnimData then
		Result = AnimData[AnimType]
		Result = Result and Result.AnimFeatureData
	end

	return Result
end

function AnimLibHelper.GetAnimPath(AnimDataID, AnimTypeName)
	local AnimFeatureData = AnimLibHelper.GetAnimFeatureData(AnimDataID, AnimTypeName)
	if not AnimFeatureData then
		return nil
	end

	local GetAnimAssetPathFromAnimIDFunc = AnimLibHelper.GetAnimAssetPathFromAnimID
	local AnimFeatureTag = AnimFeatureData.AnimFeatureTag
	if AnimFeatureTag == "Sequence" or AnimFeatureTag == "AnimWithAttachItems" then
		return GetAnimAssetPathFromAnimIDFunc(AnimFeatureData.Anim)
	elseif AnimFeatureTag == "ThreePhaseAnim" or AnimFeatureTag == "ThreePhaseAnimWithAttach" then
		local AnimItems = AnimFeatureData.AnimItems
		return {GetAnimAssetPathFromAnimIDFunc(AnimItems.Start.Anim), GetAnimAssetPathFromAnimIDFunc(AnimItems.Loop.Anim), GetAnimAssetPathFromAnimIDFunc(AnimItems.End.Anim)}
	elseif AnimFeatureTag == "AnimList" or AnimFeatureTag == "AnimListWithAttach" then
		local AnimItems = AnimFeatureData.AnimItems
		local Result = {}
		local Index = 1
		for _, AnimData in pairs(AnimItems) do
			Result[Index] = GetAnimAssetPathFromAnimIDFunc(AnimData.Anim)
			Index = Index + 1
		end
		return Result
	end

	return nil
end

function AnimLibHelper.GetAnimStage(AnimDataID, AnimTypeName, StartTime, AnimStage)
	if not AnimTypeName or AnimTypeName == "" then
		return false, AnimStage
	end
	
	local AnimFeatureData = AnimLibHelper.GetAnimFeatureData(AnimDataID, AnimTypeName)
	if not AnimFeatureData then
		return false, AnimStage
	end

	local NowTime = math.round(ULLFunc.GetUtcMillisecond() / 1000)
	local AnimFeatureTag = AnimFeatureData.AnimFeatureTag
	if AnimFeatureTag == "Sequence" or AnimFeatureTag == "AnimWithAttachItems" then
		if AnimFeatureData.bLoop or StartTime + AnimFeatureData.AnimLen > NowTime then
			return true, nil
		end
	else
		local AnimItems = AnimFeatureData.AnimItems
		local CurPhase = AnimStage
		if not CurPhase or CurPhase == "" then
			CurPhase = AnimFeatureData.StartWith
			if not CurPhase or CurPhase == "" then
				return false, AnimStage
			end
		end
		
		while CurPhase do
			local AnimItem = AnimItems[CurPhase]
			if not AnimItem then
				return false, AnimStage
			end

			if AnimItem.bLoop or StartTime + AnimItem.AnimLen > NowTime then
				return true, CurPhase
			end

			StartTime = StartTime + AnimItem.AnimLen
			CurPhase = AnimItem.NextPhase
		end
	end

	return false, AnimStage
end 

function AnimLibHelper.GetAnimFeatureClipLengthToNextControlSection(FacadeControlID, AnimFeatureName, AnimState)
	local AnimID = nil
	local ModelID = nil
	local FacadeControlData = Game.TableData.GetFacadeControlDataRow(FacadeControlID)

	if not FacadeControlData then
		return 0
	end

	if FacadeControlData then
		local FacadeAnimAssetID = FacadeControlData.AnimAssetID
		ModelID = FacadeControlData.ModelID
		if FacadeAnimAssetID and FacadeAnimAssetID ~= "" then
			AnimID = FacadeAnimAssetID
		end
	end

	if not AnimID and ModelID then
		local ModelData
		ModelData = Game.ActorAppearanceManager:GetModelLibData(ModelID)
		AnimID = ModelData and ModelData.AnimFlag or nil
	end

	if not AnimID then
		return 0
	end

	local AnimFeatureData = AnimLibHelper.GetAnimFeatureData(AnimID, AnimFeatureName)
	if not AnimFeatureData then
		return 0
	end

	local AnimFeatureTag = AnimFeatureData.AnimFeatureTag
	local Result = 0

	if AnimFeatureTag == "Sequence" or AnimFeatureTag == "AnimWithAttachItems" then
		return AnimFeatureData.AnimLen
	elseif AnimFeatureTag ~= "AnimGroup" then
		local AnimItems = AnimFeatureData.AnimItems
		local CurPhase = AnimState or AnimFeatureData.StartWith
		while CurPhase ~= nil do
			local AnimItem = AnimItems[CurPhase]
			if not AnimItem then
				return 0
			end

			Result = Result + AnimItem.AnimLen
			CurPhase = AnimItem.NextPhase

			if AnimItem.bLoop then
				break
			end
		end
	end

	return Result
end

function AnimLibHelper.SwitchV2(bEnable)
	DebugFlag.UseAnimLibV2 = bEnable
end 