--require("Tools.LuaPanda").start("127.0.0.1", 8818)

local ULLFunc = import("LowLevelFunctions")
local EPropertyClass = import("EPropertyClass")

--_G.C7 = _G.C7 or {}
--require("Framework.Utils.GlobalFunctions")
require "Framework.DoraSDK.DefineClass"
require "Framework.Utils.LuaFunctionLibrary"
require "Framework.Utils.StringExtend"
require("Framework.Library.Math3D")
require("Shared.WorldActorDefine")

unpack = table.unpack


local Json = require "Framework.Library.json"
local ViewResourceConst = require("Gameplay.CommonDefines.ViewResourceConst")

local ModelEdViewGameInstance = {}

function ModelEdViewGameInstance:Inner_OnStart()
	_G.UE_EDITOR = true

    self.StartTS = ULLFunc.GetUtcMillisecond()
    self.CurrentTime = 0.0

    Game = {}
    Game.GameInstance = self
    Game.WorldContext = self:GetOwner()
    _G.ContextObject = Game.WorldContext
    

    require("Editor.EditorLuaEnvironment")
	InitEditorCommonManagers(Game, _G.ContextObject)

    Game.WorldManager:OnWorldMapLoadComplete(1)
	
	self.AnimLibHelper = kg_require("GamePlay.3C.RoleComposite.AnimLibHelper")

	ModelEdViewGameInstance.WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")
end

function ModelEdViewGameInstance:OnLuaTick(DeltaTime)
    --Log.Debug(DeltaTime)
    local TS = ULLFunc.GetUtcMillisecond()
    self.CurrentTime = self.CurrentTime  + DeltaTime
    Game.TimerManager:UpdateTick(DeltaTime, self.CurrentTime, math.floor((TS - self.StartTS) * 0.001))

	if Game.AssetManager then
		Game.AssetManager:EditorTick(DeltaTime)
	end

	if Game.CppAssetManager then
		Game.CppAssetManager:EditorTick(DeltaTime)
	end

    if Game.UEActorManager and Game.UEActorManager.cppMgr then
        Game.UEActorManager.cppMgr:NativeTick(DeltaTime)
    end
end

function ModelEdViewGameInstance:Inner_OnShutdown()
    local Size = #Game.GPManagers
    for i = Size, 1, -1 do
        local Manager = Game.GPManagers[i]
        if (Manager ~= nil) and (Manager.UnInit ~= nil) then
            Manager:UnInit()
        end
    end
end

function ModelEdViewGameInstance:CreatePartPreviewActor(PartData)
    if not IsValid_L(self.PreviewWorld) then
        return nil
    end

    if not self.PreviewActor then
        local SpawnClass = slua.EditorLoadClass(ViewResourceConst.BP_MODEL_PREVIEW.Actor)
        local Transform = FTransform(FRotator(0,0,0):ToQuat(), FVector(0,0,0))
        self.PreviewActor = self.PreviewWorld:SpawnActor(
            SpawnClass,
            Transform,
            import("ESpawnActorCollisionHandlingMethod").AlwaysSpawn,
            nil,
            nil
        )
    end

    if not self.PreviewActor then
        return nil
    end

    self.PreviewActor:K2_SetActorLocation(FVector(0,0, 120), false,nil,false)

    return self.PreviewActor
end

function ModelEdViewGameInstance:GetPartDataTable(PartData)
    local PartDataStr = self:GetPartDataJsonStr(PartData)
    local StrLua = self:JsonStrToLuaStr(PartDataStr)
    local PartDataTable = load(StrLua)()

    return PartDataTable
end

function ModelEdViewGameInstance:JsonStrToLuaStr(JsonStr)
    local luaTable = Json.decode(JsonStr)
    local StrLua = "return  " .. self:Lua2Str(luaTable, 1)
    StrLua = self:FixLuaStr(StrLua)

    return StrLua
end

function ModelEdViewGameInstance:Lua2Str(obj, Deep)
    local lua = ""
    local t = type(obj)
    
    local BegTab = ""
    local EndTab = ""
    for i = 2, Deep, 1 do
        BegTab = BegTab .. "  "
        EndTab = EndTab .. "  "
    end

    if t == "string" then
        local SubStr = string.sub(obj, 1, 3)
        if SubStr == "UE." then
            SubStr = "-$$$" .. obj .. "$$$-"
        else
            SubStr = obj
        end

        lua = lua .. string.format("%q", SubStr)
    elseif t == "table" then
        lua = lua .. "{\n"

        local _TableKey = {}
        for key, value in pairs(obj) do
            table.insert(_TableKey, key)
        end

        if #_TableKey > 1 then
            table.sort(_TableKey, function (a, b)
                return (a < b)
            end)
        end


        for i, _Key in pairs(_TableKey) do
            local key = _Key
            local value = obj[_Key]
            lua = lua .. BegTab .. "[" .. self:Lua2Str(key, Deep + 1) .. "] = " .. self:Lua2Str(value, Deep + 1) .. ",\n"
        end
        lua = lua .. EndTab .. "}"
    elseif t == "number" then
        lua = lua .. tostring(obj)
    elseif t == "boolean" then
        if obj then
           lua = lua .. "true"
        else
           lua = lua .. "false"
        end
    else
        return "nil"
    end

    return lua
end

function ModelEdViewGameInstance:RefreshPartModel(InActor, InPartData)

end

function ModelEdViewGameInstance:CreateSuitPreviewActor(SuitObj);
    if not IsValid_L(self.PreviewWorld) then
        return nil
    end

    if self.PreviewActor then
        self.PreviewActor:K2_DestroyActor()
    end

    local SpawnClass = slua.EditorLoadClass(ViewResourceConst.BP_MODEL_PREVIEW.Actor)
    local Transform = FTransform(FRotator(0,0,0):ToQuat(), FVector(0,0,0))
    self.PreviewActor = self.PreviewWorld:SpawnActor(
        SpawnClass,
        Transform,
        import("ESpawnActorCollisionHandlingMethod").AlwaysSpawn,
        nil,
        nil
    )

    if not self.PreviewActor then
        return nil
    end

    self.PreviewActor.AnimAssetID = "DebugAnimID"

    self.PreviewActor:K2_SetActorLocation(FVector(0,0, 120), false,nil,false)

    return self.PreviewActor
end

function ModelEdViewGameInstance:GetPartIDList(PartType, PartKeyList)
	
end

function ModelEdViewGameInstance:RandomGetPartID(PartType)

end

function ModelEdViewGameInstance:CreateRandomSuitData(InSuitObj)
    
end

function ModelEdViewGameInstance:CreateAvatorModelPreviewActor(InAvatorObj)
    if not IsValid_L(self.PreviewWorld) then
        return nil
    end

    if self.PreviewActor  then
        local ChildActors = slua.Array(import("EPropertyClass").Object,import("Actor"));
        self.PreviewActor:GetAttachedActors(ChildActors, true, false)
        for i = 0, ChildActors:Length()-1 do
            local Child = ChildActors:Get(i)
            if Child then
                Child:K2_DestroyActor()
            end
        end

        self.PreviewActor:K2_DestroyActor()
    end

    --if not self.PreviewActor then
        local SpawnClass = slua.EditorLoadClass(ViewResourceConst.BP_MODEL_PREVIEW.Avator)
        local Transform = FTransform(FRotator(0,0,0):ToQuat(), FVector(0,0,0))
        self.PreviewActor = self.PreviewWorld:SpawnActor(
            SpawnClass,
            Transform,
            import("ESpawnActorCollisionHandlingMethod").AlwaysSpawn,
            nil,
            nil
        )
    --end

    if not self.PreviewActor then
        return nil
    end

    self.PreviewActor:K2_SetActorLocation(FVector(0,0, 120), false,nil,false)

    self:RefreshAvatorModelData(self.PreviewActor, InAvatorObj)

    return self.PreviewActor
end

function ModelEdViewGameInstance:RefreshAvatorModelData(InActor, AvatorObj)

end

function ModelEdViewGameInstance:GetFaceModelData(FaceObj)
    local FaceDataStr = self:GetFaceModelJsonStr(FaceObj)
    local StrLua = self:JsonStrToLuaStr(FaceDataStr)
    local FaceDataTable = load(StrLua)()

    return FaceDataTable
end

function ModelEdViewGameInstance:RefreshFaceModelData(InActor, FaceObj)
    if not IsValid_L(InActor) then
        return nil
    end

    local FaceDataTable = self:GetFaceModelData(FaceObj)


    if InActor:K2_GetRootComponent() ~= nil then
        local CapsuleCom = InActor:K2_GetRootComponent():Cast(import("CapsuleComponent"))
        if CapsuleCom then
            import("LuaHelper").SetSceneComponentVisibleFlag(CapsuleCom, false)
        end
    end

    self.HideBoxComponent = true
    
    self.MergeTime = 2
    self.FaceInfo = FaceDataTable.FaceInfo
end

function ModelEdViewGameInstance:ModifyFacePart(InActor, FaceObj)

end


function ModelEdViewGameInstance:NotifyAttachItem(Actor,ID)
    if not self.PreviewEntity then
        return
    end

    ID = ID + 1
    if self.AttachInfo then
        if self.AttachInfo[ID] and self.AttachInfo[ID].AttachType == import("EAttachType").AnimNotify then
            self:AddAttach(self.PreviewEntity,self.AttachInfo[ID].ItemName)
        end
    end
end


function ModelEdViewGameInstance:NotifyDetachItem(Actor,ID)

    if not self.PreviewEntity then
        return
    end

    ID = ID + 1
    if self.AttachInfo then
        if self.AttachInfo[ID] and self.AttachInfo[ID].AttachType == import("EAttachType").AnimNotify then
            local AllAttaches = self.PreviewEntity:GetAttachItemsByType(ModelEdViewGameInstance.WorldViewConst.ATTACH_REASON.AnimLib)
			if AllAttaches ~= nil then
				for _,Eid in pairs(AllAttaches) do
					local Entity = Game.EntityManager:getEntity(Eid)
					if Entity and Entity:GetAttachItemModelID() ==  self.AttachInfo[ID].ItemName then
						Entity:destroy()
					end
				end
			end
        end
    end

end


function ModelEdViewGameInstance:Get(Entity,ModelID)

end

function ModelEdViewGameInstance:AddAttach(Entity,ModelID)
    local AttachEnttiy = self.PreviewEntity:AddAttachment_V2(ModelEdViewGameInstance.WorldViewConst.ATTACH_REASON.AnimLib,ModelID)
    if AttachEnttiy  then
        AttachEnttiy:SetAttachToByModelCfgSocket(self.PreviewEntity.eid)
		AttachEnttiy:ApplyAttach()
        AttachEnttiy:SetCustomFollowAnim(self.CustomAttachAnims)
    end
end

local function ReloadFile (Module)
    package.loaded[Module] = nil
    return require(Module)
end

function ModelEdViewGameInstance:ReloadLuaData()
	Game.ActorAppearanceManager.NPCSuitLib = ReloadFile("Data.Config.Model.NPCSuitLib")
	Game.ActorAppearanceManager.AvatarModelLib = ReloadFile("Data.Config.Model.AvatarModelLib")
	self.AnimLibHelper.ReloadLuaData()
end

function ModelEdViewGameInstance:RefreshAnimLibPreview(MainAnim, AttachItems, ModelID)
    self:ReloadLuaData()

    self.MainAnim = MainAnim
    local LastModelID = self.ModelID
    if not ModelID or ModelID == "" then
        self.ModelID = ModelID
    end

    if not self.PreviewEntity then
        self.PreviewEntity = Game.EntityManager:CreateLocalEntity(
                "LocalDisplayChar",
                {
                    Position = FVector(0,0, 85),Rotation = FRotator(0, 0, 0),
					FacadeControlID=7600005,
                }
        )
        self.ModelID = "Character_1200005"
        self.PreviewEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
    elseif self.PreviewEntity.bInWorld then
        if LastModelID ~= ModelID then
            self:RefreshAnimLibModel(ModelID)
        end
    end

    self.ActorAnimPlayingTime = 0
    self.CustomAttachAnims = {}
    self.PreviewEntity:RemoveAllAttach()
    self.AttachInfo = AttachItems:ToTable()

    for K, ModelData in pairs(AttachItems) do
        if ModelData.AttachType == import("EAttachType").AttachOnAnimBegin and ModelData.ItemName ~="" then
            --self.PreviewEntity:AddAttachment_V2(ModelEdViewGameInstance.WorldViewConst.ATTACH_REASON.AnimLib,ModelData.ItemName)
            self:AddAttach(self.PreviewEntity,ModelData.ItemName)
        end
    end
end

function ModelEdViewGameInstance:PreviewAnimLibAnim(AnimDataID, AnimType, bLoop, bNewVersion)
	self:ReloadLuaData()

	self.PreviewAnimDataID = AnimDataID
	self.PreviewAnimType = AnimType

	self.MainAnim = nil
	
	if self.PreviewEntity then
		self.AnimPreviewID = self.PreviewEntity:PlayAnimLibFeatureForEditorPreview(AnimDataID, AnimType, bLoop, bNewVersion)
	end
end

function ModelEdViewGameInstance:RefreshAnimLibModel(ModelID, bClearAttach)
    if not self.PreviewEntity or not self.PreviewEntity.bInWorld then
        return
    end
        
    if not ModelID or ModelID == "" then
        return
    end

	local ModelData = Game.ActorAppearanceManager:GetModelLibData(ModelID)
	if not ModelData then
		return
	end
	
	if ModelData.ModelType == "AttachItem" then
		self.bIsAttachView = true
		self.PreviewEntity:destroy()
		local bIsStaticMesh = true
		local bIsSimpleAnim = false
		if ModelData.SMMesh and StringValid(ModelData.SMMesh.StaticMesh) then
			bIsStaticMesh = true
		elseif ModelData.SKMesh and ModelData.SKMesh.SkeletalMesh then
			bIsStaticMesh = false
			if StringValid(ModelData.AnimData.AnimClass) then
				bIsSimpleAnim = false
			else
				bIsSimpleAnim = true
			end
		end
		self.PreviewEntity = Game.EntityManager:CreateLocalEntity(
			"AttachItemJustForEditorView",
			{
				ModelID = ModelID,
				bInIsSimpleAnim = bIsSimpleAnim,
				bInIsStaticMesh = bIsStaticMesh
			}
		)

		self.PreviewEntity.OnAttachItemReady:Add(self, "OnDisplayAttachEntityReady")
		
		if self.PreviewAnimDataID and self.PreviewAnimType then
			self.AnimPreviewID = self.PreviewEntity:PlayAnimLibFeatureForEditorPreview(self.PreviewAnimDataID, self.PreviewAnimType)
		end
		return
	end

	self.ModelID = ModelID

	if self.bIsAttachView then
		self.bIsAttachView = false
		self.PreviewEntity:destroy()

		self.PreviewEntity = Game.EntityManager:CreateLocalEntity(
			"LocalDisplayChar",
			{
				Position = FVector(0,0, 85),Rotation = FRotator(0, 0, 0),
				FacadeControlID = 7600005,
			}
		)
		self.ModelID = ModelID or "Character_1200005"
		self.PreviewEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
		return
	end
	
	self.PreviewEntity:RefreshBodyPart_WholeAvatar(nil, ModelID)
	
    if bClearAttach then
        self.PreviewEntity:RemoveAllAttach()
    end
end

function ModelEdViewGameInstance:ModelIDToAnimDataID(ModelID)
	local ModelLibData = Game.ActorAppearanceManager.AvatarModelLib
	local TargetData = ModelLibData[ModelID]
	if TargetData and TargetData.AnimFlag then
		return TargetData.AnimFlag
	end

	return ""
end

function ModelEdViewGameInstance:GetFacadeControlIDToModelID()
	local data = slua.Map(EPropertyClass.int, EPropertyClass.Str)
	local facadeControl = require("Data.Excel.FacadeControlData").data
	for k, v in pairs(facadeControl) do
		data:Add(k, v.ModelID)
	end
	return data
end

function ModelEdViewGameInstance:GetFacadeControlIDToAnimID(FacadeControlID)
	local facadeControl = require("Data.Excel.FacadeControlData").data
	local Data = facadeControl[FacadeControlID]
	if Data and Data.AnimAssetID then
		return Data.AnimAssetID
	end
	return "" 
end

function ModelEdViewGameInstance:OnDisplayEntityReady()
	self:RefreshAnimLibModel(self.ModelID)
end

function ModelEdViewGameInstance:OnDisplayAttachEntityReady()
	if self.PreviewEntity then
		self.PreviewEntity:SetPosition(FVector(0,0, 85))
		self.PreviewEntity:SetRotation(FRotator(0, 270, 0))
	end
end

function ModelEdViewGameInstance:GetModelLibAnnouncement()
    return [[
模型库动画蓝图规范：
1.NPC通用动画蓝图 使用 BP_Animins_Npc_M2
2.怪物通用蓝图 使用 BP_Animins_Simple_Monster_Base
2.玩家类动画蓝图 使用 BP_Animins_Player_R4
3.四足类NPC 使用 （待定）
4.四足类怪物 使用 BP_Animins_FourFeet
5.模型入库必须确认配置对应标签正确，否则可能导致动画播放不正确。
6.动画蓝图由程序提供，如有特殊动画蓝图需要由程序提供。

                            ---负责程序：@戴唯 @李巍巍
]]
end


--function ModelEdViewGameInstance:CreateAnimLibPreviewActor()
--    if not IsValid_L(self.PreviewWorld) then
--        return nil
--    end
--
--
--
--    return self.PreviewEntity
--end



return Class(nil,nil,ModelEdViewGameInstance)