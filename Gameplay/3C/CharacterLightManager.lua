kg_require("Framework.C7Common.CppManagerBase")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

---@class CharacterLightManager
CharacterLightManager = DefineClass("CharacterLightManager", CppManagerBase)

function CharacterLightManager:Init()
	self:CreateCppManager("CharacterLightManager")
	self.cppMgr:SetCharacterLightFixControllerClassPath(ViewResourceConst.HDR_LIGHT_FIX_BP)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_LEVEL_LOADED, "OnLevelLoaded", self)
	self.CurrentDataPath = nil
end

function CharacterLightManager:UnInit()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
	self:DestroyCppManager()
end

function CharacterLightManager:OnLevelLoaded(levelMapData)
	if levelMapData and levelMapData.LevelPath then
		self.CurrentDataPath = levelMapData.LevelPath
		self.cppMgr:SetLightFixDataPath(levelMapData.LevelPath)
	end
end

function CharacterLightManager:RefreshLightFixDataReference(lightFixDataPath)
	self.CurrentDataPath = lightFixDataPath
	self.cppMgr:RefreshLightFixDataReference(lightFixDataPath)
end

function CharacterLightManager:SetLightCompensation(LightExp,BlendInTime)
	self.cppMgr:StartExposureBlend(LightExp,BlendInTime)
end

function CharacterLightManager:ToggleCharacterLight()
	if self.LightActive then
		self:DeactiveCharacterLight()
	else
		self:ActivateCharacterLight()
	end
end

function CharacterLightManager:CloseSceneWeight()
	self.cppMgr:CloseSceneWeight()
end

function CharacterLightManager:ActivateCharacterLight()
	if not self.DisableCharacterLight and not self.LightActive then
		self.LightActive = true
		Log.Debug("[CharacterLightManager] ActivateCharacterLight")
		self.cppMgr:EnableCharacterLightFix(self.LightActive)
	end
end

function CharacterLightManager:DeactiveCharacterLight()
	if not self.DisableCharacterLight and self.LightActive then
		self.LightActive = false
		Log.Debug("[CharacterLightManager] DeactiveCharacterLight")
		self.cppMgr:EnableCharacterLightFix(self.LightActive)
	end
end

function CharacterLightManager:EnableCharacterLight(value)
	if value then
		self.DisableCharacterLight = false
		self:ActivateCharacterLight()
	else
		self:DeactiveCharacterLight()
		self.DisableCharacterLight = true
	end
end

function CharacterLightManager:SetOverrideLumenIntensity(bOverride, lumenIntensity)
	self.cppMgr:SetOverrideLumenIntensity(bOverride, lumenIntensity)
end

function CharacterLightManager:ToggleInsetShadow()
end

return CharacterLightManager
