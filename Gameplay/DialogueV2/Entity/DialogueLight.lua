---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 21:10
---
local UPointLightComponent = import("PointLightComponent")
local USpotLightComponent = import("SpotLightComponent")
local CollisionConst = kg_require("Shared.Const.CollisionConst")

kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")
local ViewControlVisibleComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlVisibleComponent")

---@class DialogueLight : LocalEntityBase
---@field OwnerParticipant DialogueParticipant
---@field ptpConfig
local DialogueLight = DefineLocalEntity("DialogueLight", LocalEntityBase, {
    ViewControlVisibleComponent,
})

DialogueLight:Register("DialogueLight")

DialogueLight.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {
    CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET
}

DialogueLight.__LightType__ = {
    POINT = 0, -- 点光源
    SPOT = 1, -- 聚光灯
}

function DialogueLight:ctor()
end

function DialogueLight:dtor()
end

function DialogueLight:AfterEnterWorld()
    self.CppEntity:KAPI_Actor_AddActorTag(self.ptpConfig.TrackName)

    local lightCompClassID = 0
    if self.ptpConfig.LightType == self.__LightType__.POINT then
        lightCompClassID = Game.ObjectActorManager:GetIDByClass(UPointLightComponent)
    elseif self.ptpConfig.LightType == self.__LightType__.SPOT then
        lightCompClassID = Game.ObjectActorManager:GetIDByClass(USpotLightComponent)
    end

    if lightCompClassID == 0 then
        Log.WarningFormat("[AfterEnterWorld] invalid light type %s on %s", self.ptpConfig.LightType, self.ptpConfig.TrackName)
        self.OwnerParticipant:CompleteReady()
        return
    end

    local lightCompID = self.CppEntity:KAPI_Actor_AddComponentByClassID(lightCompClassID)
    --self.CppEntity:KAPI_SceneID_SetMobility(lightCompID, EComponentMobility.Movable)

    local lightColorConfig = self.ptpConfig.LightColor
    local lightColor = FLinearColor(lightColorConfig.R, lightColorConfig.G, lightColorConfig.B, lightColorConfig.A)
    self.CppEntity:KAPI_LightID_SetLightFColor(lightCompID, lightColor:ToFColorSRGB())
    self.CppEntity:KAPI_LightID_SetIntensity(lightCompID, self.ptpConfig.LightIntensity)
    self.OwnerParticipant:CompleteReady()
end

function DialogueLight:BeforeExitWorld()
end

return DialogueLight
