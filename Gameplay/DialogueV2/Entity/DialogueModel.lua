---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 14:26
---
local CollisionConst = kg_require("Shared.Const.CollisionConst")

kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")
local ViewControlVisibleComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlVisibleComponent")

---@class DialogueModel : LocalEntityBase
---@field OwnerParticipant DialogueParticipant
---@field ptpConfig
local DialogueModel = DefineLocalEntity("DialogueModel", LocalEntityBase, {
    ViewControlVisibleComponent,
})

DialogueModel:Register("DialogueModel")

DialogueModel.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {
    CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET
}

function DialogueModel:ctor()
end

function DialogueModel:dtor()
end

function DialogueModel:AfterEnterWorld()
    self.CppEntity:KAPI_Actor_AddActorTag(self.ptpConfig.TrackName)

    -- StaticMesh优先级高于SkeletonMesh
    local meshAssetPath
    if not string.isEmpty(self.ptpConfig.StaticMesh) then
        meshAssetPath = self.ptpConfig.StaticMesh
    else
        meshAssetPath = self.ptpConfig.SkeletalMesh
    end

    -- 没有外观
    if string.isEmpty(meshAssetPath) then
        self.OwnerParticipant:CompleteReady()
        return
    end

    self:DoAsyncLoadAsset(meshAssetPath)
end

function DialogueModel:BeforeExitWorld()
end

function DialogueModel:OnAsyncLoadAssetCallback(loadID, assetID)
    if assetID ~= 0 then
        self.meshAssetID = assetID

        if not string.isEmpty(self.ptpConfig.StaticMesh) then
            local meshCompID = self.CppEntity:KAPI_Actor_GetComponentByClassName("StaticMeshComponent")
            if meshCompID ~= 0 then
                self.CppEntity:KAPI_StaticMeshID_SetStaticMesh(meshCompID, self.meshAssetID)
            end
        else
            local meshCompID = self.CppEntity:KAPI_Actor_GetComponentByClassName("SkeletalMeshComponent")
            if meshCompID ~= 0 then
                self.CppEntity:KAPI_SkeletalMeshID_SetSkeletalMesh(meshCompID, self.meshAssetID, true)
            end
        end
    end

    self.OwnerParticipant:CompleteReady()
end

return DialogueModel
