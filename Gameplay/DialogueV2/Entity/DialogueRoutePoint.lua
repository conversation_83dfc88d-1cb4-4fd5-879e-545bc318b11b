---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 14:27
---
local CollisionConst = kg_require("Shared.Const.CollisionConst")

kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")
local ViewControlVisibleComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlVisibleComponent")

---@class DialogueRoutePoint : LocalEntityBase
---@field OwnerParticipant DialogueParticipant
---@field ptpConfig
local DialogueRoutePoint = DefineLocalEntity("DialogueRoutePoint", LocalEntityBase, {
    ViewControlVisibleComponent,
})

DialogueRoutePoint:Register("DialogueRoutePoint")

DialogueRoutePoint.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {
    CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET
}

function DialogueRoutePoint:ctor()
end

function DialogueRoutePoint:dtor()
end

function DialogueRoutePoint:AfterEnterWorld()
    self.CppEntity:KAPI_Actor_AddActorTag(self.ptpConfig.TrackName)
    self.OwnerParticipant:CompleteReady()
end

function DialogueRoutePoint:BeforeExitWorld()
end

return DialogueRoutePoint
