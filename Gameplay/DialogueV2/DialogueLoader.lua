---@class DialogueLoader
---@field private classCDO table @ 对话资产中所有类的默认值，!!!请注意，不要往里面插入任何数据！！！
local DialogueLoader = DefineClass("DialogueLoader")



function DialogueLoader:Init()
    -- KSBC的压缩能力很强大，有很高的压缩比，CDO功能就可以关闭了
    self.bUsingCDO = false
    if self.bUsingCDO then
        self.classCDO = kg_require("Data.Config.Dialogue.DialogueClassCDO")
        assert(self.classCDO, "DialogueClassCDO is nil")
    end    
end

--- 加载对话资产
---@public
---@param assetName string|number
---@param bReload boolean
---@return table
function DialogueLoader:Load(assetName, bReload)
    if _G.bUseKsbc then
        return self:LoadKSBCAsset(assetName, bReload)
    else
        return self:LoadLuaAsset(assetName, bReload)
    end
end


-----------------------------------------------------------------------------------------------------
--- private
--------------------------------------------------------------------------------------------------

---@private
---@param assetName string|number
---@param bReload boolean
---@return table
function DialogueLoader:LoadLuaAsset(assetName, bReload)
    local dialogueConfigPath = string.format("%s%s",DialogueConst.DIALOGUE_ASSET_PATH_PREFIX, tostring(assetName))

    if (bReload == true) and (Game.loaded[dialogueConfigPath] ~= nil) then
        Game.loaded[dialogueConfigPath] = nil
    end

    if Game.loaded[dialogueConfigPath] then
        return Game.loaded[dialogueConfigPath].Ret
    end

    local bOk, dialogueConfig = xpcall(kg_require, _G.CallBackError, dialogueConfigPath)
    if not bOk then
        return nil
    end

    if self.bUsingCDO then
        self.rebuildCDOValues(dialogueConfig, self.classCDO)
    end

    return dialogueConfig
end

---@private
---@param assetName string|number
---@param bReload boolean
---@return table
function DialogueLoader:LoadKSBCAsset(assetName, bReload)
    local strAssetName = tostring(assetName)
    local dialogueConfigPath = string.format("%s%s", DialogueConst.DIALOGUE_ASSET_PATH_PREFIX, strAssetName)

    if (bReload == true) and (Game.loaded[dialogueConfigPath] ~= nil) then
        Game.loaded[dialogueConfigPath] = nil
    end

    if Game.loaded[dialogueConfigPath] then
        return Game.loaded[dialogueConfigPath].Ret
    end

    local data = ksbcRawG()["KSBC_Dialogue"]
    if not data then
        Log.Error("[KGSL]KSBC_Dialogue is nil")
        return nil
    end

    local dialogueCfg = data[strAssetName]
    if not dialogueCfg then
        Log.ErrorFormat("[KGSL]%s does not exist in KSBC data.", strAssetName)
        return nil
    end

    local table = { ENV = {}, Ret = nil }
    local moduleEnv = table.ENV

    local met = { __index = _G }
    if UE_EDITOR and forbidGlobalVal(dialogueConfigPath) then
        met.__newindex = global_val_warn
    end

    setmetatable(moduleEnv, met)
    table.Ret = setmetatable({}, {
        __index = function(t, key)
            return dialogueCfg[key]
        end,
        __newindex = function(t, key, val)
            Log.Warning("[KGSL]dialogue config is read-only, can not add key/value.")
         end,
        __metatable = "READ_ONLY",
    })
    Game.loaded[dialogueConfigPath] = table

    return table.Ret
end

--- 重新构建CDO的值
---@private
function DialogueLoader.rebuildCDOValues(dialogueAsset, dialobueClassCDO)
    local insert = table.insert
    local remove = table.remove
    local needSetCDO = {}
    local tables = { dialogueAsset }
    while #tables > 0 do
        local t = tables[1]
        if t.ObjectClass then
            needSetCDO[t] = t.ObjectClass
        end

        for k, v in pairs(t) do
            if type(v) == "table" then
                insert(tables, v)
            end
        end
        remove(tables, 1)
    end

    local GetClassName = DialogueLoader.getClassName

    local getmetatable = getmetatable
    local setmetatable = setmetatable
    for tbl, class in pairs(needSetCDO) do
        local origMetaTable = getmetatable(tbl)
        if origMetaTable and origMetaTable.__tag == "DialogueCDO" then
            goto continue
        end

        local origIndex = origMetaTable and origMetaTable.__index or nil
        local className = GetClassName(class)
        local CDO = dialobueClassCDO[className]
        if not CDO then
            Log.WarningFormat("DialogueCDO not found for class %s", className)
            goto continue
        end

        setmetatable(tbl, {
            __index = function(tbl, key)
                local val
                if origIndex then
                    val = origIndex(tbl, key)
                end

                if val ~= nil then
                    return val
                end

                if CDO then
                    return CDO[key]
                end

                return nil
            end,
            __tag = "DialogueCDO",
        })

        ::continue::
    end
end

--- 获取类名
---@private
---@param name string
---@return string
function DialogueLoader.getClassName(name)
    local s, e = string.find(name, ".", 1, true)
    if s then
        return string.sub(name, s + 1)
    else
        return name
    end
end

return DialogueLoader