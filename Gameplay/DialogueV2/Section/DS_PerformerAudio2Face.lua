---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/27 17:18
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

-- todo:需要重新接入
---@class DS_PerformerAudio2Face : DialogueSectionBase
local DS_PerformerAudio2Face = DefineClass("DS_PerformerAudio2Face", DialogueSectionBase)

function DS_PerformerAudio2Face:OnInit()
    self.faceAnimCompID = 0
end

function DS_PerformerAudio2Face:OnStart()
    if not self.trackPtpEntity then
        return
    end

    self.faceAnimCompID = self.trackPtpEntity.CppEntity:KAPI_Actor_GetComponentByClassName("FaceAnimComponent")
    if self.faceAnimCompID == 0 then
        return
    end

    local mainMeshCompID = self.trackPtpEntity.CppEntity:KAPI_Actor_GetMainMesh()
    self.trackPtpEntity.CppEntity:KAPI_SkeletalMeshID_PlayLipFaceAnim(mainMeshCompID, self.sectionConfig.FaceAnimID, self.sectionConfig.FixTime, self.trackPtpEntity.Sex)
end

function DS_PerformerAudio2Face:OnTick(deltaTime)
    if not self.trackPtpEntity then
        return
    end

    self.trackPtpEntity.CppEntity:KAPI_FaceAnim_SetLipBlend(self.sectionConfig.LipBlend)
end

function DS_PerformerAudio2Face:OnFinish(finishReason)
    -- todo: 这里似乎应该停掉?
end

return DS_PerformerAudio2Face
