---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/27 17:01
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_PerformerFaceAnimation : DialogueSectionBase
local DS_PerformerFaceAnimation = DefineClass("DS_PerformerFaceAnimation", DialogueSectionBase)

function DS_PerformerFaceAnimation:OnStart()
    if not self.trackPtpEntity then
        return
    end

    self.trackPtpEntity:PlayFaceAnimation(self.sectionConfig.FaceAnimLib.AssetID, true)
end

function DS_PerformerFaceAnimation:OnFinish(finishReason)
    if not self.trackPtpEntity then
        return
    end

    self.trackPtpEntity:StopFaceAnimation(self.sectionConfig.BlendOutTime)
end

return DS_PerformerFaceAnimation
