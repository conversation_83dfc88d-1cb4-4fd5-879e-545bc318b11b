---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/27 18:30
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")
local PostProcessConst = kg_require("Gameplay.Effect.PostProcessConst")

---@class DS_Fog : DialogueSectionBase
local DS_Fog = DefineClass("DS_Fog", DialogueSectionBase)

function DS_Fog:OnStart()
    local centerPtpEntity = self.ptpManager:GetParticipantEntityByName(self.sectionConfig.FogCenter)

    -- 只设置中心
    if self.sectionConfig.UseSceneFogParameter then
        Game.PostProcessManager:SetFogLocationUpdateActorByID(centerPtpEntity and centerPtpEntity.CharacterID or 0, 
                PostProcessConst.PP_FOG_SOURCE_TYPE.DIALOGUE)
        return
    end

    -- 配置的单位是cm，雾效距离参数单位是m，因此这里做个转换
    local realFogDistance = self.sectionConfig.Distance / 100
    local linearColor = {
        ((self.sectionConfig.FogColor.R + 256) % 256) / 256,
        ((self.sectionConfig.FogColor.G + 256) % 256) / 256,
        ((self.sectionConfig.FogColor.B + 256) % 256) / 256,
        ((self.sectionConfig.FogColor.A + 256) % 256) / 256
    }

    -- 以AlphaIn作为blend时间, AlphaOut暂时用不到
    Game.PostProcessManager:EnableOrUpdateFog(
            Enum.EPostProcessLayers.Dialogue,
            self.sectionConfig.AlphaIn,
            nil,
            self.sectionConfig.Density,
            realFogDistance,
            linearColor, self.sectionConfig.SmoothDist ~= nil and self.sectionConfig.SmoothDist / 100 or 2, nil,
    not self.sectionConfig.OverlayClimate,
            PostProcessConst.PP_FOG_SOURCE_TYPE.DIALOGUE)   

    Game.PostProcessManager:SetFogLocationUpdateActorByID(centerPtpEntity and centerPtpEntity.CharacterID or 0,
        PostProcessConst.PP_FOG_SOURCE_TYPE.DIALOGUE)
end

function DS_Fog:OnFinish(finishReason)
    if not self.sectionConfig.bClearAtEnd then
        self:recordInfoToBlackBoard()
        return
    end

    Game.PostProcessManager:RemoveFog(self.sectionConfig.AlphaOut, PostProcessConst.PP_FOG_SOURCE_TYPE.DIALOGUE)
end

function DS_Fog:recordInfoToBlackBoard()
    self.dialogueInstance:SetBlackBoardValue(DialogueConst.BlackBoardKey.NEED_RESET_FOG, true)
end

return DS_Fog
