---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/27 17:36
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_SendFlowchartMsg : DialogueSectionBase
local DS_SendFlowchartMsg = DefineClass("DS_SendFlowchartMsg", DialogueSectionBase)

function DS_SendFlowchartMsg:OnStart()
    if self.sectionConfig.bSendAtFinish then
        local blackBoardValue = self.dialogueInstance:GetBlackBoardValue(DialogueConst.BlackBoardKey.FLOWCHART_MSG)
        if not blackBoardValue then
            blackBoardValue = {}
        end

        Log.DebugFormat("[OnStart] cache flowchart msg:%s", self.sectionConfig.Msg)
        table.insert(blackBoardValue, self.sectionConfig.Msg)
        self.dialogueInstance:SetBlackBoardValue(DialogueConst.BlackBoardKey.FLOWCHART_MSG, blackBoardValue)
    else
        Log.DebugFormat("[OnStart] send flowchart msg:%s", self.sectionConfig.Msg)
        Game.me:ReqClientAIMessage(self.sectionConfig.Msg)
    end
end

return DS_SendFlowchartMsg
