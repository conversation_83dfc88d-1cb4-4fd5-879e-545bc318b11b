---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/8 15:51
---

-- 所有Section的基类,主要封装一些生命周期方法和公共初始化
---@class DialogueSectionBase
---@field trackPtpEntity DialoguePerformer|DialogueCamera|DialogueRoutePoint|DialogueNiagara
local DialogueSectionBase = DefineClass("DialogueSectionBase")

function DialogueSectionBase:ctor(dialogueInstance, track, sectionConfig, trackPtp, trackPtpEntity, sectionIndex)
    -- 暂停标记
    self.bPause = false
    -- Section激活标记
    self.bInRunning = false
    self.sectionConfig = sectionConfig
	self.sectionIndex = sectionIndex

    -- 从Start->Finish的时间,不包括暂停的时间
    self.runningTime = 0
    -- 从Start->Finish的时间,包括暂停的时间
    self.totalRunningTime = 0

    ---@type DialogueTrack
    self.ownerTrack = track

    ---@type DialogueParticipant
    self.trackPtp = trackPtp
    ---@type number 每次指定方法前获取一下,避免O(N)监听
    self.trackPtpEntityID = trackPtpEntity and trackPtpEntity:uid() or 0

    ---@type NormalDialogueInstance
    self.dialogueInstance = dialogueInstance

    ---@type DialogueParticipantManager
    self.ptpManager = self.dialogueInstance.ptpManager

    self.DialogueID = self.dialogueInstance.DialogueID
end

function DialogueSectionBase:dtor()

end

---@public
function DialogueSectionBase:InitSection()
    self:OnInit()
end

---@public
function DialogueSectionBase:StartSection()
    self.bInRunning = true
    self:OnStart()
end

---@public
---@param deltaTime number
function DialogueSectionBase:TickSection(deltaTime)
    self.totalRunningTime = self.totalRunningTime + deltaTime

    if DialogueConst.IGNORE_PAUSE_SECTION[self.sectionConfig.ObjectClass] then
        -- 如果是alwaysTick的,不累加runningTime
        self:OnTick(deltaTime)
    elseif not self.bPause then
        self.runningTime = self.runningTime + deltaTime

        -- 如果已超时,确保叠加的delta不会超过Duration
        if self.runningTime > self.sectionConfig.Duration then
            deltaTime = deltaTime - (self.runningTime - self.sectionConfig.Duration)
        end

        self:OnTick(deltaTime)
    end
end

---@public
---@param finishReason number
function DialogueSectionBase:FinishSection(finishReason)
    self.bInRunning = false
    self:OnFinish(finishReason)
end

---@public
function DialogueSectionBase:PauseSection()
    if not self.bPause then
        self.bPause = true
        self:OnPause()
    end
end

---@public
function DialogueSectionBase:ResumeSection()
    if self.bPause then
        self.bPause = false
        self:OnResume()
    end
end

---获取下一个SectionConfig
function DialogueSectionBase:GetNextSectionConfig()
	local sectionConfigs = self.ownerTrack.trackConfig.ActionSections
	if self.sectionIndex and self.sectionIndex  < #sectionConfigs then
		return sectionConfigs[self.sectionIndex + 1]
	else
		return nil
	end
end

---暂停时累加总时长
---@public
---@param deltaTime number
function DialogueSectionBase:AddTotalRunningTimeOnPause(deltaTime)
    self.totalRunningTime = self.totalRunningTime + deltaTime
end

-- 判断当前Section是否已经激活
---@public
---@return boolean
function DialogueSectionBase:IsRunning()
    return self.bInRunning
end

-- 判断当前Section是否需要开始执行
---@public
---@return boolean
function DialogueSectionBase:NeedStart(trackRunningTime)
    return (self.bInRunning == false) and (trackRunningTime >= self.sectionConfig.StartTime)
end

-- 判断当前Section是否已执行完毕
---@public
---@return boolean
function DialogueSectionBase:NeedFinish()
    return (self.bInRunning == true) and (self.runningTime >= self.sectionConfig.Duration)
end


--region LifeCycle


---@protected
function DialogueSectionBase:OnInit()
    -- 子类重写
end

---@protected
function DialogueSectionBase:OnStart()
    -- 子类重写
end

---@protected
function DialogueSectionBase:OnTick(deltaTime)
    -- 子类重写
end

---@protected
---@param finishReason number
function DialogueSectionBase:OnFinish(finishReason)
    -- 子类重写
end

---@protected
function DialogueSectionBase:OnPause()
    -- 子类重写
end

---@protected
function DialogueSectionBase:OnResume()
    -- 子类重写
end


--endregion LifeCycle


--region Common





--endregion Common


return DialogueSectionBase
