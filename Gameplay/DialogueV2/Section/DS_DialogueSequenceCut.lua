---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/29 10:50
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_DialogueSequenceCut : DialogueSectionBase
local DS_DialogueSequenceCut = DefineClass("DS_DialogueSequenceCut", DialogueSectionBase)

function DS_DialogueSequenceCut:OnInit()
    self.loadID = 0
end

function DS_DialogueSequenceCut:OnStart()
    local sequenceID = self.sectionConfig.SequenceAssetID

    local sequenceData = Game.TableData.GetDilaogueSeuqenceDataRow(sequenceID)
    if not sequenceData then
        return
    end

    self.loadID = Game.DialogueManagerV2.SequenceManager:FindTaskLoadHandleIDByAssetID(sequenceID)
    if self.loadID then
        Game.DialogueManagerV2.SequenceManager:LevelSequencePlay(self.loadID)
    else
        local playParams = { AssetID = sequenceID, bBindCamera = self.sectionConfig.UseSequenceCamera }
        self.loadID = Game.DialogueManagerV2.SequenceManager:PreLoadLevelSequence(playParams, true)
    end
end

function DS_DialogueSequenceCut:OnFinish(finishReason)
    Game.DialogueManagerV2.SequenceManager:StopLevelSequence(self.loadID)
end

return DS_DialogueSequenceCut
