---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/6/4 20:08
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_QTE : DialogueSectionBase
local DS_QTE = DefineClass("DS_QTE", DialogueSectionBase)

function DS_QTE:OnStart()
    self.dialogueInstance:AddDialogueBarrier(DialogueConst.BARRIER_TYPE.QTE, nil, self.sectionConfig)
end

return DS_QTE
