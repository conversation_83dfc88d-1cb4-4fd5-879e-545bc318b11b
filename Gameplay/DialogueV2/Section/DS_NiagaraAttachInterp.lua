---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/26 20:52
---
local KML = import("KismetMathLibrary")
local ERelativeTransformSpace = import("ERelativeTransformSpace")
local EAttachmentRule = import("EAttachmentRule")
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_NiagaraAttachInterp : DialogueSectionBase
local DS_NiagaraAttachInterp = DefineClass("DS_NiagaraAttachInterp", DialogueSectionBase)

DS_NiagaraAttachInterp.__TempTrans__ = M3D.Transform()

function DS_NiagaraAttachInterp:OnInit()
    self.startTrans = nil
    self.targetTrans = nil
end

function DS_NiagaraAttachInterp:OnStart()
    if (self.trackPtpEntity == nil) or (self.trackPtpEntity.isDestroyed == true) then
        return
    end

    local targetPtpEntity = self.ptpManager:GetParticipantEntityByName(self.sectionConfig.AttachActor)
    if (targetPtpEntity == nil) or (targetPtpEntity.isDestroyed == true) then
        return
    end

    local mainMeshCompID = targetPtpEntity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
    if mainMeshCompID == 0 then
        return
    end

    if self.sectionConfig.Immediate then
        local attachRule = EAttachmentRule.SnapToTarget
        targetPtpEntity.CppEntity:KAPI_Actor_AttachToComponent(mainMeshCompID, self.sectionConfig.SocketName, attachRule, attachRule, attachRule, true)
        return
    end

    self.__TempTrans__:Pack(self.trackPtpEntity:GetTransform_P())
    self.startTrans = M3D.ToFTransform(self.__TempTrans__)

    if targetPtpEntity.CppEntity:KAPI_SkeletalMeshID_HasSocket(mainMeshCompID, self.sectionConfig.SocketName) then
        self.targetTrans = targetPtpEntity.CppEntity:KAPI_SceneID_GetSocketTransform(mainMeshCompID, self.sectionConfig.SocketName, ERelativeTransformSpace.RTS_Actor)
    else
        self.__TempTrans__:Pack(targetPtpEntity:GetTransform_P())
        self.targetTrans = M3D.ToFTransform(self.__TempTrans__)
    end
end

function DS_NiagaraAttachInterp:OnTick(deltaTime)
    if self.sectionConfig.Immediate then
        return
    end

    if (self.trackPtpEntity == nil) or (self.trackPtpEntity.isDestroyed == true) then
        return
    end

    local alpha = self.runningTime / self.sectionConfig.Duration
    local interpTrans = KML.TLerp(self.startTrans, self.targetTrans, alpha, 0)
    interpTrans = M3D.ToTransform(interpTrans)
    self.trackPtpEntity:SetTransform_P(interpTrans:Unpack())
end

function DS_NiagaraAttachInterp:OnFinish(finishReason)
    if self.sectionConfig.Immediate then
        return
    end

    if (self.trackPtpEntity == nil) or (self.trackPtpEntity.isDestroyed == true) then
        return
    end

    if finishReason ~= DialogueConst.SECTION_FINISH_REASON.LIFE_END then
        local finalTrans = M3D.ToTransform(self.targetTrans)
        self.trackPtpEntity:SetTransform_P(finalTrans:Unpack())
    end
end

return DS_NiagaraAttachInterp
