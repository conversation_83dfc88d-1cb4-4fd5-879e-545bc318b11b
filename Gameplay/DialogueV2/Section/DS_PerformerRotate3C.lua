---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/22 15:56
---
local EMovementMode = import("EMovementMode")
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_PerformerRotate : DialogueSectionBase
local DS_PerformerRotate3C = DefineClass("DS_PerformerRotate", DialogueSectionBase)

function DS_PerformerRotate3C:OnStart()
    if self.trackPtpEntity == nil or (self.trackPtpEntity.isDestroyed == true) then
        return
    end

    local targetPtpEntity = self.ptpManager:GetParticipantEntityByName(self.sectionConfig.Target)
    if not targetPtpEntity then
        return
    end

    -- 不贴地的演员,为了防止坠落修改MovementMode
    if not self.trackPtpEntity:NeedBornStickGround() then
        self.trackPtpEntity.CppEntity:KAPI_Movement_SetMovementMode(EMovementMode.MOVE_Falling, 0)
    end

    -- todo@chengdong:目前3C移动接3C转身会触发ensure
    self.trackPtpEntity:EnterLookAtToActorWithAnim(targetPtpEntity, self.sectionConfig.Duration)
end

function DS_PerformerRotate3C:OnFinish(finishReason)
    if finishReason == DialogueConst.SECTION_FINISH_REASON.LIFE_END then
        return
    end

    if self.trackPtpEntity == nil or (self.trackPtpEntity.isDestroyed == true) then
        return
    end

    self.trackPtpEntity:StopAllViewControlRotate(true)
end

return DS_PerformerRotate3C
