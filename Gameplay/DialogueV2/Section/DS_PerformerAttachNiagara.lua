---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/27 16:08
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_PerformerAttachNiagara : DialogueSectionBase
local DS_PerformerAttachNiagara = DefineClass("DS_PerformerAttachNiagara", DialogueSectionBase)

function DS_PerformerAttachNiagara:OnInit()
    self.niagaraID = 0
end

function DS_PerformerAttachNiagara:OnStart()
    if not self.trackPtpEntity then
        return
    end

    local sectionData = self.sectionConfig
    local pos = sectionData.Translation
    local rot = sectionData.Rotator
    local scale = sectionData.Scale
    local m3dTransform = M3D.Transform(
            M3D.Vec3(pos.X, pos.Y, pos.Z),
            M3D.Rotator(rot.Pitch, rot.Yaw, rot.Roll),
            M3D.Vec3(scale.X, scale.Y, scale.Z)
    )

    local effectParam = NiagaraEffectParamTemplate.AllocFromPool()
    effectParam.bFollowHidden = sectionData.bFollowHidden
    effectParam.NiagaraEffectPath = sectionData.Niagara
    effectParam.bNeedAttach = true
    effectParam.AttachPointName = sectionData.AttachPoint or ""
    effectParam.bAbsoluteRotation = false
    effectParam.bAbsoluteScale = false
    M3D.ToTransform(m3dTransform, effectParam.SpawnTrans)

    self.niagaraID = self.trackPtpEntity:PlayNiagaraEffect(effectParam)
end

function DS_PerformerAttachNiagara:OnFinish(finishReason)
    if not self.trackPtpEntity then
        return
    end

    if self.niagaraID ~= 0 then
        self.trackPtpEntity:DestroyNiagaraSystem(self.niagaraID)
        self.niagaraID = 0
    end
end

return DS_PerformerAttachNiagara
