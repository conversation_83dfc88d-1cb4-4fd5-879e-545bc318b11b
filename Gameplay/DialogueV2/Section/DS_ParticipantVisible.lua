---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/22 16:49
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_ParticipantVisible : DialogueSectionBase
local DS_ParticipantVisible = DefineClass("DS_ParticipantVisible", DialogueSectionBase)

function DS_ParticipantVisible:OnStart()
    if not self.trackPtpEntity then
        return
    end
	
	Log.DebugFormat("[DS_ParticipantVisible:OnStart] ptpEntity:%s,bVisible:%s", self.trackPtpEntity,self.sectionConfig.Visible)

    if self.sectionConfig.Visible then
        self.trackPtpEntity:ForceToVisibleByDialogue()
    else
        self.trackPtpEntity:ForceToInvisibleByDialogue()
    end
end

return DS_ParticipantVisible
