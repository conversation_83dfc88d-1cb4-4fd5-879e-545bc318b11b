---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/7/14 18:25
---
local DialogueSectionBase = kg_require("Gameplay.DialogueV2.Section.DialogueSectionBase")

---@class DS_Mystery : DialogueSectionBase
local DS_Mystery = DefineClass("DS_Mystery", DialogueSectionBase)

function DS_Mystery:OnStart()
    local sectionConfig = self.sectionConfig
    local mysteryID = sectionConfig.MysteryID

    ---@type ReasoningLayerParams
    local params = {
        bCloseMysteryMode = sectionConfig.bHideOnFinish,
        delayPauseDialogueTime = sectionConfig.TimeOut * 1000
    }

    Game.ReasoningSystem:TryReasoningSingleLayer(mysteryID, params)
end

return DS_Mystery
