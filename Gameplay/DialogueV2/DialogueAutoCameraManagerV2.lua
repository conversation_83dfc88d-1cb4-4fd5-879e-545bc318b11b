---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/6/2 18:22
---
---@class DialogueAutoCameraManagerV2
local DialogueAutoCameraManagerV2 = DefineClass("DialogueAutoCameraManagerV2")

function DialogueAutoCameraManagerV2:ctor(dialogueIns)
    ---@type NormalDialogueInstance
    self.dialogueIns = dialogueIns
    self:ResetParams()
    self.bHaveFinishCal = false      -- 有无加载完模型能正确计算头部位置
end

function DialogueAutoCameraManagerV2:dtor()

end

DialogueAutoCameraManagerV2.__ZeroVector__ = FVector(0, 0, 0)
DialogueAutoCameraManagerV2.__HeadSocketName__ = "head"
DialogueAutoCameraManagerV2.__BipHeadSocketName__ = "Bip001-Head"

---@param entity LocalEntityBase
function DialogueAutoCameraManagerV2:GetActorTargetPosition(entity)
    local meshCompID = entity.CppEntity:KAPI_Actor_GetMainMesh()
    if meshCompID == 0 then
        return self.__ZeroVector__
    end

    local entityLoc = entity:GetPosition()
    local faceLoc = entity.CppEntity:KAPI_SceneID_GetSocketLocation(meshCompID, self.__HeadSocketName__)
    if faceLoc.Z < entityLoc.Z then
        faceLoc = entity.CppEntity:KAPI_SceneID_GetSocketLocation(meshCompID, self.__BipHeadSocketName__)
    end

    if faceLoc.Z < entityLoc.Z then
        return self.__ZeroVector__
    end

    return FVector(faceLoc.X, faceLoc.Y, faceLoc.Z)
end

function DialogueAutoCameraManagerV2:CalculateCameraParams(Section, cameraEntity, SectionData)
    self:ResetParams()
    self.Section = Section
    if SectionData.AutoCameraType == Enum.EAutoCameraType.OneActor then
        self:CalculateCameraParamsOneActor(Section, cameraEntity, SectionData)
    elseif SectionData.AutoCameraType == Enum.EAutoCameraType.TwoActorTypeOne then
        self:CalculateCameraParamsTwoActorTypeOne(Section, cameraEntity, SectionData)
    elseif SectionData.AutoCameraType == Enum.EAutoCameraType.TwoActorTypeTwo then
        self:CalculateCameraParamsTwoActorTypeTwo(Section, cameraEntity, SectionData)
    end
    if SectionData.DepthOfFieldFocusActor ~= "" then
        self:SetFocusActorPostion(SectionData.DepthOfFieldFocusActor, cameraEntity)
    end
end

function DialogueAutoCameraManagerV2:SetFocusActorPostion(FocusActorName, cameraEntity)
    local ptpName = type(FocusActorName) == "string" and  FocusActorName or FocusActorName.PerformerName
    local targetPtpEntity = self.dialogueIns.ptpManager:GetParticipantEntityByName(ptpName)

    if not targetPtpEntity then
        return
    end

    local FocusPos = self:GetActorTargetPosition(targetPtpEntity)
    local CameraPosition = cameraEntity:GetPosition()
    local Distance = math.sqrt((CameraPosition.X - FocusPos.X) ^ 2 + (CameraPosition.Y - FocusPos.Y) ^ 2 + (CameraPosition.Z - FocusPos.Z) ^ 2)
    self.Section.FocusActorDisatance = Distance
    self.Section.SectionData.DepthOfFieldFocalDistance = Distance
end


-- 函数：根据相机和人物信息计算相机坐标
function DialogueAutoCameraManagerV2:CalculateCameraParamsOneActor(Section, cameraEntity, SectionData)
    local ptpName = type(SectionData.Actor1P) == "string" and  SectionData.Actor1P or SectionData.Actor1P.PerformerName
    ---@type LocalEntityBase
    local ptpEntity = self.dialogueIns.ptpManager:GetParticipantEntityByName(ptpName)

    if not ptpEntity then
        Log.WarningFormat("has not set Actor1P Performer")
        return
    end

    self.Actor1 = ptpEntity

    local character_rotation = ptpEntity:GetRotation()
    local ActorPosition = self:GetActorTargetPosition(ptpEntity)
    if (ActorPosition == FVector(0, 0, 0)) then
        return
    end

    local H = SectionData.ActorFocusH
    local V = SectionData.ActorFocusV  -- 希望人物在图像中的位置比例
    local Dist = SectionData.ActorFocusDist
    local camera_rotation = { Yaw = character_rotation:Euler().Z + 180 + SectionData.CameraYaw, Pitch = SectionData.CameraPitch, Roll = 0 }  -- 相机的旋转角度
    local fov_x = SectionData.CameraFovX
    Game.CameraManager:KAPI_Camera_DialogueCamera_SetCameraFOV(cameraEntity.CharacterID, fov_x)
    local Face1ToCamera = self:GetCameraPostionBYHV(camera_rotation, H, V, fov_x)
    local c_x = math.cos(math.rad(camera_rotation.Pitch)) * math.cos(math.rad(camera_rotation.Yaw))
    local c_y = math.cos(math.rad(camera_rotation.Pitch)) * math.sin(math.rad(camera_rotation.Yaw))
    local c_z = math.sin(math.rad(camera_rotation.Pitch))
    local cosalpha = math.abs(Face1ToCamera.X * c_x + Face1ToCamera.Y * c_y + Face1ToCamera.Z * c_z)
    local CameraPosX = ActorPosition.X + Dist / cosalpha * Face1ToCamera.X
    local CameraPoxY = ActorPosition.Y + Dist / cosalpha * Face1ToCamera.Y
    local CameraPosZ = ActorPosition.Z + Dist / cosalpha * Face1ToCamera.Z

    local CameraPos = FVector(CameraPosX, CameraPoxY, CameraPosZ)
    local CameraRotation = FRotator(SectionData.CameraPitch, character_rotation:Euler().Z + 180 + SectionData.CameraYaw, 0)
    cameraEntity:SetPosition(CameraPos)
    cameraEntity:SetRotation(CameraRotation)

    Log.DebugWarningFormat("Face To CameraDistance: %f", math.sqrt((ActorPosition.X - CameraPos.X) ^ 2 + (ActorPosition.Y - CameraPos.Y) ^ 2, (ActorPosition.Z - CameraPos.Z) ^ 2))
    self.bHaveFinishCal = true
end

function DialogueAutoCameraManagerV2:GetPlayerAndActor(Actor1P, Actor2P)
    local Track1P, Track2P

    local ptp1Name = type(Actor1P) == "string" and Actor1P or Actor1P.PerformerName
    Track1P = self.dialogueIns.ptpManager:GetParticipantEntityByName(ptp1Name)

    local ptp2Name = type(Actor2P) == "string" and Actor2P or Actor2P.PerformerName
    Track2P = self.dialogueIns.ptpManager:GetParticipantEntityByName(ptp2Name)

    return Track1P, Track2P
end

function DialogueAutoCameraManagerV2:CalculateCameraParamsTwoActorTypeOne(Section, cameraEntity, SectionData)
    self.Actor1, self.Actor2 = self:GetPlayerAndActor(SectionData.Actor1P, SectionData.Actor2P)
    if not self.Actor1 or not self.Actor2 then
        return
    end

    local Actor1FacePosition = self:GetActorTargetPosition(self.Actor1)
    local Actor2FacePosition = self:GetActorTargetPosition(self.Actor2)
    if (Actor1FacePosition == FVector(0, 0, 0)) then
        return
    end
    if (Actor2FacePosition == FVector(0, 0, 0)) then
        return
    end
    local Face2ToFace1Vector = FVector(Actor1FacePosition.X - Actor2FacePosition.X, Actor1FacePosition.Y - Actor2FacePosition.Y, Actor1FacePosition.Z - Actor2FacePosition.Z)
    local Face1ToFace2Rotation = Face2ToFace1Vector:Rotation()
    local fov = SectionData.CameraFovX
    Game.CameraManager:KAPI_Camera_DialogueCamera_SetCameraFOV(cameraEntity.CharacterID, fov)
    local theta = SectionData.CameraYaw
    local yaw = Face1ToFace2Rotation.Yaw + theta

    self.ActorTwo_Actor1Pos = Actor1FacePosition
    self.ActorTwo_Actor2Pos = Actor2FacePosition
    self.ActorTwo_CameraYaw = yaw
    self.ActorTwo_CameraFovX = fov
    self.ActorTwo_Actor1_H = SectionData.ActorFocusH
    self.ActorTwo_Actor1_V = SectionData.ActorFocusV
    self.ActorTwo_Actor2_H = SectionData.ActorBackendH
    self.ActorTwo_Actor2_V = SectionData.ActorBackendV

    local Pinch = self:SolvePitchEquation(90, -90, 0.01)
    local camera_rotation = { Yaw = yaw, Pitch = Pinch, Roll = 0 }  -- 相机的旋转角度
    local V1 = self:GetCameraPostionBYHV(camera_rotation, self.ActorTwo_Actor1_H, self.ActorTwo_Actor1_V, self.ActorTwo_CameraFovX)
    local V2 = self:GetCameraPostionBYHV(camera_rotation, self.ActorTwo_Actor2_H, self.ActorTwo_Actor2_V, self.ActorTwo_CameraFovX)
    Face2ToFace1Vector = FVector(-Face2ToFace1Vector.X, -Face2ToFace1Vector.Y, -Face2ToFace1Vector.Z)
    local S_H = self:GetVector3Dot(Face2ToFace1Vector, V2)
    local S = V2 * S_H
    local R = Face2ToFace1Vector - S
    local T = V2 * self:GetVector3Module(R) * (1 / math.tan(math.acos(self:GetVector3Dot(V1, V2))))
    local CameraPos = self.ActorTwo_Actor1Pos + R + T
    local CameraRotation = FRotator(Pinch, yaw, 0)
    cameraEntity:SetPosition(CameraPos)
    cameraEntity:SetRotation(CameraRotation)
    self.bHaveFinishCal = true
end

function DialogueAutoCameraManagerV2:CalculateCameraParamsTwoActorTypeTwo(Section, cameraEntity, SectionData)
    self.Actor1, self.Actor2 = self:GetPlayerAndActor(SectionData.Actor1P, SectionData.Actor2P)
    if not self.Actor1 or not self.Actor2 then
        return
    end

    local Actor1FacePosition = self:GetActorTargetPosition(self.Actor1)
    local Actor2FacePosition = self:GetActorTargetPosition(self.Actor2)
    if (Actor1FacePosition == FVector(0, 0, 0)) then
        return
    end
    if (Actor2FacePosition == FVector(0, 0, 0)) then
        return
    end

    local Face2ToFace1Vector = FVector(Actor1FacePosition.X - Actor2FacePosition.X, Actor1FacePosition.Y - Actor2FacePosition.Y, Actor1FacePosition.Z - Actor2FacePosition.Z)
    local Face1ToFace2Rotation = Face2ToFace1Vector:Rotation()
    local fov = SectionData.CameraFovX
    Game.CameraManager:KAPI_Camera_DialogueCamera_SetCameraFOV(cameraEntity.CharacterID, fov)
    local theta = SectionData.CameraYaw
    local yaw = Face1ToFace2Rotation.Yaw + theta
    self.ActorTwo_Actor1Pos = Actor1FacePosition
    self.ActorTwo_Actor2Pos = Actor2FacePosition
    self.ActorTwo_CameraYaw = yaw
    self.ActorTwo_CameraPitch = SectionData.CameraPitch
    self.ActorTwo_CameraFovX = fov
    self.ActorTwo_Actor1_H = SectionData.ActorFocusH
    self.ActorTwo_Actor1_V = SectionData.ActorFocusV
    self.ActorTwo_Actor2_H = SectionData.ActorBackendH
    self.ActorTwo_Actor2_V = self:SolveDistEquation(0, 10000, 0.1)
    local camera_rotation = { Yaw = yaw, Pitch = self.ActorTwo_CameraPitch, Roll = 0 }  -- 相机的旋转角度
    local V1 = self:GetCameraPostionBYHV(camera_rotation, self.ActorTwo_Actor1_H, self.ActorTwo_Actor1_V, self.ActorTwo_CameraFovX)
    local V2 = self:GetCameraPostionBYHV(camera_rotation, self.ActorTwo_Actor2_H, self.ActorTwo_Actor2_V, self.ActorTwo_CameraFovX)
    Face2ToFace1Vector = FVector(-Face2ToFace1Vector.X, -Face2ToFace1Vector.Y, -Face2ToFace1Vector.Z)
    local S_H = self:GetVector3Dot(Face2ToFace1Vector, V2)
    local S = V2 * S_H
    local R = Face2ToFace1Vector - S
    local T = V2 * self:GetVector3Module(R) * (1 / math.tan(math.acos(self:GetVector3Dot(V1, V2))))
    local CameraPos = self.ActorTwo_Actor1Pos + R + T
    local CameraRotation = FRotator(self.ActorTwo_CameraPitch, self.ActorTwo_CameraYaw, 0)
    cameraEntity:SetPosition(CameraPos)
    cameraEntity:SetRotation(CameraRotation)
    self.bHaveFinishCal = true
end

function DialogueAutoCameraManagerV2:GetCameraPostionBYHV(CameraRotation, H, V, Fov_x)
    local fov_y = 2 * math.deg(math.atan(math.tan(math.rad(Fov_x / 2)) / self.aspect_ratio))
    local offset_pitch = math.atan(math.tan(math.rad((fov_y / 2))) * (1 - 2 * V))
    local offset_yaw = -math.atan(math.tan(math.rad((Fov_x / 2))) * (1 - 2 * H))
    local tmp_x = 1 / math.cos(offset_pitch) * math.cos(math.rad(CameraRotation.Pitch + math.deg(offset_pitch)))
    local tmp_y = math.tan(offset_yaw)
    local delta_yaw = math.atan(tmp_y / tmp_x)
    local target_yaw = math.rad(CameraRotation.Yaw + math.deg(delta_yaw))
    local target_pitch = math.atan(math.tan(math.rad(CameraRotation.Pitch + math.deg(offset_pitch))) * math.cos(delta_yaw))
    local v_x = -math.cos(target_pitch) * math.cos(target_yaw)
    local v_y = -math.cos(target_pitch) * math.sin(target_yaw)
    local v_z = -math.sin(target_pitch)
    return FVector(v_x, v_y, v_z)
end

-- 函数：根据相机和人物信息计算相机坐标
function DialogueAutoCameraManagerV2:TransformToOneActor(Section, cameraEntity)
    local CameraPos = cameraEntity:GetPosition()
    local Actor1FacePosition = self:GetActorTargetPosition(self.Actor1)
    local CameraToFaceVector = FVector(CameraPos.X - Actor1FacePosition.X, CameraPos.Y - Actor1FacePosition.Y, CameraPos.Z - Actor1FacePosition.Z)
    local CameraToFaceRotation = CameraToFaceVector:Rotation()
    local pintch = CameraToFaceRotation.Pitch
    local yaw = CameraToFaceRotation.Yaw
    local fov_y = 2 * math.deg(math.atan(math.tan(math.rad(Section.SectionData.CameraFovX / 2)) / self.aspect_ratio))
    local H = (1 + math.tan(yaw) / math.tan(math.rad(Section.SectionData.CameraFovX / 2))) / 2
    local V = (1 + math.tan(pintch) / math.tan(math.rad(fov_y / 2))) / 2
    if H > 1 or H < 0 or V > 1 or V < 0 then
        return
    end
    Section.SectionData.ActorFocusH = H
    Section.SectionData.ActorFocusV = V
end



-- 定义你的一元函数
function DialogueAutoCameraManagerV2:FunctionWithDist(Dist)
    local camera_rotation = { Yaw = self.ActorTwo_CameraYaw, Pitch = self.ActorTwo_CameraPitch, Roll = 0 }  -- 相机的旋转角度
    local V1 = self:GetCameraPostionBYHV(camera_rotation, self.ActorTwo_Actor1_H, self.ActorTwo_Actor1_V, self.ActorTwo_CameraFovX)
    local O = self.ActorTwo_Actor1Pos + V1 * Dist
    local Face2ToCamera = self.ActorTwo_Actor2Pos - O
    local Face2ToCamera_Yaw = FVector()
    Face2ToCamera_Yaw.X = Face2ToCamera.X * math.cos(-math.rad(self.ActorTwo_CameraYaw)) - Face2ToCamera.Y * math.sin(-math.rad(self.ActorTwo_CameraYaw))
    Face2ToCamera_Yaw.Y = Face2ToCamera.X * math.sin(-math.rad(self.ActorTwo_CameraYaw)) + Face2ToCamera.Y * math.cos(-math.rad(self.ActorTwo_CameraYaw))
    Face2ToCamera_Yaw.Z = Face2ToCamera.Z
    local Face2ToCamera_Yaw_Pintch = FVector()
    Face2ToCamera_Yaw_Pintch.X = Face2ToCamera_Yaw.X * math.cos(-math.rad(self.ActorTwo_CameraPitch)) - Face2ToCamera_Yaw.Z * math.sin(-math.rad(self.ActorTwo_CameraPitch))
    Face2ToCamera_Yaw_Pintch.Y = Face2ToCamera_Yaw.Y
    Face2ToCamera_Yaw_Pintch.Z = Face2ToCamera_Yaw.X * math.sin(-math.rad(self.ActorTwo_CameraPitch)) + Face2ToCamera_Yaw.Z * math.cos(-math.rad(self.ActorTwo_CameraPitch))
    local H = Face2ToCamera_Yaw_Pintch.Y / Face2ToCamera_Yaw_Pintch.X / math.tan(math.rad(self.ActorTwo_CameraFovX / 2)) * 0.5 + 0.5
    return H - self.ActorTwo_Actor2_H
end

function DialogueAutoCameraManagerV2:ResetParams()
    self.ActorTwo_Actor1Pos = nil
    self.ActorTwo_Actor2Pos = nil
    self.ActorTwo_CameraYaw = nil
    self.ActorTwo_CameraPitch = nil
    self.ActorTwo_CameraFovX = nil
    self.ActorTwo_Actor1_H = nil
    self.ActorTwo_Actor1_V = nil
    self.ActorTwo_Actor2_H = nil
    self.ActorTwo_Actor2_V = nil
    self.aspect_ratio = 16 / 9  -- 长宽比
    self.Actor1 = nil
    self.Actor2 = nil
end


-- 定义你的一元函数
function DialogueAutoCameraManagerV2:FunctionWithPitch(CameraPitch)
    local camera_rotation = { Yaw = self.ActorTwo_CameraYaw, Pitch = CameraPitch, Roll = 0 }  -- 相机的旋转角度


    local Actor1ToCamera = self:GetCameraPostionBYHV(camera_rotation, self.ActorTwo_Actor1_H, self.ActorTwo_Actor1_V, self.ActorTwo_CameraFovX)
    local Actor2ToCamera = self:GetCameraPostionBYHV(camera_rotation, self.ActorTwo_Actor2_H, self.ActorTwo_Actor2_V, self.ActorTwo_CameraFovX)

    local Face1ToFace2 = self.ActorTwo_Actor1Pos - self.ActorTwo_Actor2Pos
    local CrossResult = self:GetVector3Cross(Actor1ToCamera, Actor2ToCamera)
    return self:GetVector3Dot(CrossResult, Face1ToFace2)
end



-- 二分法求解
function DialogueAutoCameraManagerV2:SolvePitchEquation(PinchLeft, PinchRight, Epsilon)
    local mid, fa, fb

    repeat
        mid = (PinchLeft + PinchRight) / 2
        if (math.abs(mid - PinchLeft) < Epsilon) or (math.abs(mid - PinchRight) < Epsilon) then
            --Game.DialogueManager.EditorManager:ShowMessageBox("H V参数配置异常导致Pintch无法求解")
            break
        end
        fa = self:FunctionWithPitch(PinchLeft)
        fb = self:FunctionWithPitch(mid)

        if fa * fb < 0 then
            PinchRight = mid
        else
            PinchLeft = mid
        end
    until (math.abs(self:FunctionWithPitch(mid)) < Epsilon)

    return mid
end

-- 二分法求解
function DialogueAutoCameraManagerV2:SolveDistEquation(DistLeft, DistRight, Epsilon)
    local mid, fa, fb

    repeat
        mid = (DistLeft + DistRight) / 2
        if (math.abs(mid - DistLeft) < Epsilon) or (math.abs(mid - DistRight) < Epsilon) then
            --Game.DialogueManager.EditorManager:ShowMessageBox("H V参数配置异常导致Dist无法求解")
            break
        end
        fa = self:FunctionWithDist(DistLeft)
        fb = self:FunctionWithDist(mid)

        if fa * fb < 0 then
            DistRight = mid
        else
            DistLeft = mid
        end
    until (math.abs(self:FunctionWithDist(mid)) < Epsilon)

    return self:FunctionWithDist(mid) + self.ActorTwo_Actor2_H
end

function DialogueAutoCameraManagerV2:GetVector3Dot(v1, v2)
    return v1.X * v2.X + v1.Y * v2.Y + v1.Z * v2.Z
end

function DialogueAutoCameraManagerV2:GetVector3Cross(v1, v2)
    local v3 = { x = v1.Y * v2.Z - v2.Y * v1.Z, y = v2.X * v1.Z - v1.X * v2.Z, z = v1.X * v2.Y - v2.X * v1.Y }
    return FVector(v3.x, v3.y, v3.z)
end

function DialogueAutoCameraManagerV2:GetVector3Module(v)
    return math.sqrt(v.X * v.X + v.Y * v.Y + v.Z * v.Z)
end

return DialogueAutoCameraManagerV2
