---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 14:21
---


---@class DialogueTrack
local DialogueTrack = DefineClass("DialogueTrack")

function DialogueTrack:ctor(dialogueInstance, trackConfig)
    -- 暂停标记
    self.bPause = false
    self.runningTime = 0
    self.trackConfig = trackConfig

    ---@type DialogueInstanceBase
    self.dialogueInstance = dialogueInstance

    ---@type DialogueSectionBase[]
    self.sections = {}
end

function DialogueTrack:dtor()

end

---@public
function DialogueTrack:Init()
    local trackPtp = self.dialogueInstance.ptpManager:GetParticipantByName(self.trackConfig.TrackName)
    local trackPtpEntity = self.dialogueInstance.ptpManager:GetParticipantEntityByName(self.trackConfig.TrackName)

    for sectionIndex, sectionConfig in ksbcipairs(self.trackConfig.ActionSections or {}) do
        if not sectionConfig.Enable then
            goto continue
        end

        self:createSection(sectionConfig, trackPtp, trackPtpEntity, sectionIndex)
        :: continue ::
    end

    for _, actionConfig in ksbcipairs(self.trackConfig.Actions or {}) do
        for sectionIndex, sectionConfig in ksbcipairs(actionConfig.ActionSections) do
            if not sectionConfig.Enable then
                goto continue
            end

            self:createSection(sectionConfig, trackPtp, trackPtpEntity, sectionIndex)
            :: continue ::
        end
    end
end

---@public
function DialogueTrack:UnInit()
    local trackPtpEntity = self.dialogueInstance.ptpManager:GetParticipantEntityByName(self.trackConfig.TrackName)
    for _, section in ipairs(self.sections) do
        section.trackPtpEntity = trackPtpEntity
        if section:IsRunning() then
            section:FinishSection(DialogueConst.SECTION_FINISH_REASON.TERMINATE)
        end
    end
    table.clear(self.sections)
end

---@public
function DialogueTrack:PauseTrack()
    local trackPtpEntity = self.dialogueInstance.ptpManager:GetParticipantEntityByName(self.trackConfig.TrackName)
    self.bPause = true
    for _, section in ipairs(self.sections) do
        section.trackPtpEntity = trackPtpEntity
        section:PauseSection()
    end
end

---@public
function DialogueTrack:ResumeTrack()
    local trackPtpEntity = self.dialogueInstance.ptpManager:GetParticipantEntityByName(self.trackConfig.TrackName)
    self.bPause = false
    for _, section in ipairs(self.sections) do
        section.trackPtpEntity = trackPtpEntity
        section:ResumeSection()
    end
end

---@type number[]
DialogueTrack.__TempSectionIdxList__ = {}
DialogueTrack.__TempSectionIdx__ = 0

-- todo:关注性能
---@public
---@param bSkip boolean 跳过时传入该参数
function DialogueTrack:Tick(deltaTime, bSkip)
    table.clear(self.__TempSectionIdxList__)

    if not self.bPause then
        self.runningTime = self.runningTime + deltaTime
    end

    local trackPtpEntity = self.dialogueInstance.ptpManager:GetParticipantEntityByName(self.trackConfig.TrackName)

    for idx, section in ipairs(self.sections) do
        section.trackPtpEntity = trackPtpEntity

        -- 非暂停情况下,启动需要开启的Section
        if (self.bPause == false) and (section:NeedStart(self.runningTime) == true) then
            Log.DebugFormat("[StartSection] %s start at %s", section.sectionConfig.ObjectName, self.runningTime)
            section:InitSection()
            section:StartSection(self.runningTime)
        end

        if section:IsRunning() then
            section:TickSection(deltaTime)
        end

        -- 关闭应当结束的Section并记录Idx
        if section:NeedFinish() then
            section:FinishSection(bSkip and DialogueConst.SECTION_FINISH_REASON.SKIP or DialogueConst.SECTION_FINISH_REASON.LIFE_END)
            table.insert(self.__TempSectionIdxList__, idx)
        end
    end

    -- 反向迭代,销毁已结束的Section
    for idx = #self.__TempSectionIdxList__, 1, -1 do
        self.__TempSectionIdx__ = self.__TempSectionIdxList__[idx]
        self.sections[self.__TempSectionIdx__]:delete()
        table.remove(self.sections, self.__TempSectionIdx__)
    end
end

function DialogueTrack:createSection(sectionConfig, trackPtp, trackPtpEntity, sectionIndex)
    local bNoCameraDialogue = self.dialogueInstance.DialogueType == DialogueConst.DIALOGUE_TYPE.NO_CAMERA
    -- 无镜对话,部分不允许
    if (DialogueConst.EXCLUSIVE_SECTION[sectionConfig.ObjectClass] ~= nil) and (bNoCameraDialogue == true) then
        return
    end

    local sectionCls = DialogueConst.DIALOGUE_SECTION_TYPE_TO_CLASS[sectionConfig.ObjectClass]
    if not sectionCls then
        Log.WarningFormat("[createSection] cannot find class for %s", sectionConfig.ObjectClass)
        return
    end

    local newSection = sectionCls.new(self.dialogueInstance, self, sectionConfig, trackPtp, trackPtpEntity, sectionIndex)
    table.insert(self.sections, newSection)
end

return DialogueTrack
