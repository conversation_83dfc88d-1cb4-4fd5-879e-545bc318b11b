---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/23 16:31
---
---@class DialoguePanelInterface 接口类
local DialoguePanelInterface = DefineClass("DialoguePanelInterface")

function DialoguePanelInterface:ctor(dialoguePanel)
    ---@type Dialogue_Panel
    self.dialoguePanel = dialoguePanel
end

function DialoguePanelInterface:dtor()
    Log.DebugFormat("destroy DialoguePanelInterface.stack trace backe:\n%s", debug.traceback())
    self.dialoguePanel = nil
end

---@public
---@param funcName string
---@return any
function DialoguePanelInterface:InnerCallPanelFunction(funcName, ...)
    if not self.dialoguePanel then
        Log.Warning("[InnerCallPanelFunction] dialogue panel has been destroyed")
        return
    end

    if DialogueConst.PanelFunction[funcName] == nil then
        Log.WarningFormat("[InnerCallPanelFunction] %s not a registered function", funcName)
        return
    end

    local func = self.dialoguePanel[funcName]
    if (func == nil) or (type(func) ~= "function") then
        Log.WarningFormat("[InnerCallPanelFunction] %s not a function", funcName)
        return
    end

    return func(self.dialoguePanel, ...)
end

function DialoguePanelInterface:SetPrintSpeed(speed)
    self.dialoguePanel:SetPrintSpeed(speed)
end

function DialoguePanelInterface:FlushPrint()
    self.dialoguePanel:FlushPrint()
end

function DialoguePanelInterface:ShowContent(title, talkerName, content, talkerType, subtitle, small, smallPos, bShowTalkerNameInCSStyle, sectionConfig)
    self.dialoguePanel:ShowContent(title, talkerName, content, talkerType, subtitle, small, smallPos, bShowTalkerNameInCSStyle, sectionConfig)
end

---@param options DialogueOption[]
---@param optionType number
---@param bTimeLimit boolean
---@param timeLimited number
---@param bChoice boolean
function DialoguePanelInterface:ShowOptions(options, optionType, bTimeLimit, timeLimited, bChoice)
    self.dialoguePanel:ShowOptions(options, optionType, bTimeLimit, timeLimited, bChoice)
end

function DialoguePanelInterface:HideOptionComp()
	self.dialoguePanel:HideOptionComp()
end

function DialoguePanelInterface:SetReviewButtonVisible(bVisible)
    self.dialoguePanel:SetReviewButtonVisible(bVisible)
end

function DialoguePanelInterface:SetSkipButtonVisible(bVisible)
    self.dialoguePanel:SetSkipButtonVisible(bVisible)
end

function DialoguePanelInterface:SetAutoPlayButtonVisible(bVisible)
    self.dialoguePanel:SetAutoPlayButtonVisible(bVisible)
end

function DialoguePanelInterface:PlayMovie(movieName, endCallback)
    self.dialoguePanel:PlayMovie(movieName, endCallback)
end

function DialoguePanelInterface:StopMovie()
    self.dialoguePanel:StopMovie()
end

function DialoguePanelInterface:ShowContentSkipArrow()
    self.dialoguePanel:ShowContentSkipArrow()
end

---@return number
function DialoguePanelInterface:GetCurrentMovieDuration()
    return self.dialoguePanel:GetCurrentMovieDuration()
end

function DialoguePanelInterface:TryFlushContentPrinter()
    self.dialoguePanel:TryFlushContentPrinter()
end

function DialoguePanelInterface:SetDialogueContentUIType(InType)
	self.dialoguePanel:SetDialogueContentUIType(InType)
end

---@param timeCur number @ 单位：秒
---@param duration number @ 单位：秒
function DialoguePanelInterface:UpdateDialogueProgress(timeCur, duration)
    self.dialoguePanel:UpdateDialogueProgress(timeCur, duration)
end


return DialoguePanelInterface
