---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 11:13
---
kg_require("Gameplay.DialogueV2.Entity.DialogueLight")
kg_require("Gameplay.DialogueV2.Entity.DialogueModel")
kg_require("Gameplay.DialogueV2.Entity.DialogueNiagara")
kg_require("Gameplay.DialogueV2.Entity.DialogueRoutePoint")
kg_require("Gameplay.DialogueV2.Entity.DialogueCamera")
kg_require("Gameplay.DialogueV2.Entity.DialoguePerformer")

local DialogueUIProcessor = kg_require("Gameplay.DialogueV2.DialogueUIProcessor")
local DialogueHistory = kg_require("Gameplay.DialogueV2.DialogueHistory")
local DialogueSequenceManager = kg_require("Gameplay.DialogueSystem.DialogueSequenceManager")
local DialogueLoader = kg_require("Gameplay.DialogueV2.DialogueLoader")
local DialogueInputProcessor = kg_require("Gameplay.DialogueV2.DialogueInputProcessor")

---@class DialoguePlayParams
---@field AssetID number|number[]
---@field NPCID number
---@field ActorEntityID number
---@field QuestID number
---@field TaskRingID number
---@field SubmitItemID number
---@field NewSubmitCfg table
---@field CondIdx number
---@field ReceiveRingID number
---@field SpawnTransform table
---@field StartEpisodeLineIdx number(editor) 编辑态可以选择从指定小段开始播放
---@field StartTime number(editor) 编辑态可以通过拖动游标让对话从某个时刻开始播放

---@class DialogueHistoryItem
---@field DialogueID number
---@field SelectedOptions table<number, boolean>

---@return boolean
function _G.IsValidObjectName(name)
    if type(name) ~= "string" then
        return false
    end

    if (name == nil) or (name == "") or (name == "None") then
        return false
    end

    return true
end

---@class DialogueManagerV2
---@field public InputProcessor DialogueInputProcessor
local DialogueManagerV2 = DefineClass("DialogueManagerV2")

function DialogueManagerV2:Init()
    ---@type DialogueLoader
    self.loader = DialogueLoader.new()
    self.loader:Init()

    ---@type DialogueInstanceBase
    self.curExclusiveInstance = nil

    ---@type table<number, DialogueInstanceBase>
    self.activateNoCameraInstances = {}
	
    ---@type DialogueUIProcessor
    self.UIProcessor = DialogueUIProcessor.new()
    self.UIProcessor:Init()

    self.InputProcessor = DialogueInputProcessor.new()
    self.InputProcessor:Init()

    ---@type DialogueHistory
	self.DialogueHistory = DialogueHistory.new()

    ---@type DialogueSequenceManager
    self.SequenceManager = DialogueSequenceManager.new()

    ---#149621 美梦成真每次进剧情都要重新改一次自动播放，希望按之前的设置来
    ---所以这个变量提到Manager来了
    self.bAutoPlay = false

    -- 总timer
    self.tickTimer = Game.TimerManager:TickTimerBindIns(self, "Tick", -1)
end

function DialogueManagerV2:UnInit()
    self:TerminateAllDialogue()
    if self.tickTimer then
        Game.TimerManager:StopTimerAndKill(self.tickTimer)
        self.tickTimer = nil
    end

    self.InputProcessor:UnInit()
end

---@public
---@param params DialoguePlayParams
function DialogueManagerV2:Enter(params)
    local dialogueID = params.AssetID
    if not self:CanPlayDialogue() then
        return
    end

    if self:IsPlayingExclusiveDialogue() then
        self.curExclusiveInstance:Terminate()
        self.curExclusiveInstance = nil
    end

    -- 如果有相同ID的无镜对话,也尝试停止
    local activateNoCameraInstance = self.activateNoCameraInstances[dialogueID]
    if activateNoCameraInstance then
        activateNoCameraInstance:Terminate()
    end

    -- 按类型创建不同的Instance
    local dialogueType = self:getDialogueType(dialogueID)
    if not dialogueType then
        Log.ErrorFormat("[PlayDialogue] get dialogue type failed for %s", dialogueID)
        return
    end

    local dialogueInstanceClass = DialogueConst.INSTANCE_TYPE_TO_CLASS[dialogueType]
    if not dialogueInstanceClass then
        Log.ErrorFormat("[PlayDialogue] get dialogue instance class failed for %s", dialogueID)
        return
    end

    Log.DebugFormat("[Enter] enter new dialogue %s", dialogueID)

    ---@type DialogueInstanceBase
    local newDialogueInstance = dialogueInstanceClass.new(params, dialogueType)
    newDialogueInstance:RegisterStage()
    newDialogueInstance:Init()

    if dialogueType == DialogueConst.DIALOGUE_TYPE.NO_CAMERA then
        self.activateNoCameraInstances[dialogueID] = newDialogueInstance
    else
        self.curExclusiveInstance = newDialogueInstance
    end

    newDialogueInstance:Activate()
end

-- 中断单个对话
---@public
---@param dialogueID number
function DialogueManagerV2:TerminateDialogue(dialogueID)
    -- 不传ID就默认停当前正在进行的独占
    if not dialogueID then
        if self.curExclusiveInstance then
            self.curExclusiveInstance:Terminate()
            self.curExclusiveInstance = nil
        end
    end

    if (self.curExclusiveInstance ~= nil) and (self.curExclusiveInstance.DialogueID == dialogueID) then
        self.curExclusiveInstance:Terminate()
        self.curExclusiveInstance = nil
    end

    local noCameraDialogueInstance = self.activateNoCameraInstances[dialogueID or 0]
    if noCameraDialogueInstance then
        self.activateNoCameraInstances[dialogueID] = nil
        noCameraDialogueInstance:Terminate()
    end
end

-- 中断所有对话
---@public
function DialogueManagerV2:TerminateAllDialogue()
    Log.Debug("[TerminateAllDialogue]")
    if self.curExclusiveInstance then
        self.curExclusiveInstance:Terminate()
        self.curExclusiveInstance = nil
    end

    for _, noCameraDialogueInstance in pairs(self.activateNoCameraInstances) do
        noCameraDialogueInstance:Terminate()
    end

    table.clear(self.activateNoCameraInstances)
end

-- 对话寿终正寝
---@public
---@param dialogueID number
function DialogueManagerV2:OnDialogueFinished(dialogueID)
    if (self.curExclusiveInstance ~= nil) and (self.curExclusiveInstance.DialogueID == dialogueID) then
        self.curExclusiveInstance:UnInit()
        self.curExclusiveInstance = nil
    end

    local noCameraDialogueInstance = self.activateNoCameraInstances[dialogueID or 0]
    if noCameraDialogueInstance then
        self.activateNoCameraInstances[dialogueID] = nil
        noCameraDialogueInstance:UnInit()
    end
end

---@public
---@return boolean
function DialogueManagerV2:CanPlayDialogue()
    -- todo:这里处理状态冲突
    return true
end

---@public
---@return boolean
function DialogueManagerV2:IsPlayingSpecificDialogue(dialogueID)

end

---@public
---@return boolean
function DialogueManagerV2:IsPlayingDialogue()
    return (self.curExclusiveInstance ~= nil) or (next(self.activateNoCameraInstances) ~= nil)
end

---@public
---@return boolean
function DialogueManagerV2:IsPlayingExclusiveDialogue()
    return (self.curExclusiveInstance ~= nil)
end

---@public
---@return boolean
function DialogueManagerV2:IsPlayingSharedDialogue()
    return (next(self.activateNoCameraInstances) ~= nil)
end

---@public
---@param deltaTime number
function DialogueManagerV2:Tick(deltaTime)
    -- 对话内所有deltaTime统一用秒为单位
    deltaTime = deltaTime / 1000

    if self.curExclusiveInstance then
        xpcall(self.curExclusiveInstance.Tick, CallBackError, self.curExclusiveInstance, deltaTime)
    end

    for _, activateNoCameraInstance in pairs(self.activateNoCameraInstances) do
        xpcall(activateNoCameraInstance.Tick, CallBackError, activateNoCameraInstance, deltaTime)
    end
end

-- 获取对话类型以根据类型创建Instance
---@private
---@return number
function DialogueManagerV2:getDialogueType(dialogueID)
    if type(dialogueID) == "table" then
        return DialogueConst.DIALOGUE_TYPE.LIST
    else
        local dialogueData = Game.TableData.GetDialogueAssetDataRow(dialogueID)
        if not dialogueData then
            return
        end

        if dialogueData.bSimpleDialogue then
            return DialogueConst.DIALOGUE_TYPE.SIMPLE
        else
            local dialogueConfig = self:GetDialogueConfig(dialogueID)
            if not dialogueConfig then
                return
            end

            if not dialogueConfig.Unique then
                return DialogueConst.DIALOGUE_TYPE.NO_CAMERA
            else
                return DialogueConst.DIALOGUE_TYPE.NORMAL
            end
        end
    end
end

---@public
---@param dialogueID number
---@param bReload boolean 是否reload
---@return table
function DialogueManagerV2:GetDialogueConfig(dialogueID, bReload)
    assert(dialogueID ~= nil and type(dialogueID) == 'number', "dialogueID is nil or not number")
    return self.loader:Load(dialogueID, bReload)
end

---@public
---@return boolean
function DialogueManagerV2:IsAutoPlay()
    return self.bAutoPlay
end

---@public
function DialogueManagerV2:SetAutoPlay(bAutoPlay)
    if self.bAutoPlay == bAutoPlay then
        return
    end

    self.bAutoPlay = bAutoPlay

    if self.curExclusiveInstance then
        self.curExclusiveInstance:OnAutoPlayStateChange(bAutoPlay)
    end
end

---@public
function DialogueManagerV2:SetDialogueContentUI(ContentUIType)
	self.UIProcessor:SetDialogueContentUIType(ContentUIType)
end

return DialogueManagerV2
