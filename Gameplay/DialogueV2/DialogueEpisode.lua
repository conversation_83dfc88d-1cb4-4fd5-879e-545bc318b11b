---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 14:19
---

local DialogueTrack = kg_require("Gameplay.DialogueV2.DialogueTrack")
local DialogueOption = kg_require("Gameplay.DialogueV2.DialogueOption")
-- luacheck: push ignore
local DialogueOptionActions = kg_require("Gameplay.DialogueV2.DialogueOptionActions")
-- luacheck: pop ignore

---@class DialogueEpisode
---@field private options DialogueOption[]
local DialogueEpisode = DefineClass("DialogueEpisode")

function DialogueEpisode:ctor(dialogueInstance, episodeIdx, episodeConfig)
    ---@type DialogueInstanceBase
    self.dialogueInstance = dialogueInstance
    self.episodeIdx = episodeIdx
    self.episodeConfig = episodeConfig

    ---暂停标记
    self.bPause = false
    ---结束标记
    self.bFinish = false
    ---播放时长
    self.runningTime = 0
    ---@type DialogueTrack[]
    self.tracks = {}
    ---@type DialogueOption[]
    self.options = {}
end

function DialogueEpisode:dtor()
    Game.EventSystem:RemoveObjListeners(self)
end

---@public
function DialogueEpisode:Init()
    -- 创建所有Track
    local allTrackConfigs = self.dialogueInstance:GetAllTrackConfigsInEpisode(self.episodeIdx)
    for _, trackConfig in ipairs(allTrackConfigs) do
        local newTrack = DialogueTrack.new(self.dialogueInstance, trackConfig)
        newTrack:Init()
        table.insert(self.tracks, newTrack)
    end
    ---- 初始化选项数据
    --self:InitDialogueOptions()
end

--------------------------------------------------------- REGION Options ----------------------------------------------
---@type fun(): nil 初始化所有Dialogue选项
function DialogueEpisode:InitDialogueOptions()
    -- 清理options
    self.options = {}
    -- 展示options
    self.showOptions = {}
    -- 初始化options
    local EpisodeOptions = self.episodeConfig.Options
    for i = 1, #EpisodeOptions do
        local OptionInstance = DialogueOption.new()
        OptionInstance:InitData(self, EpisodeOptions[i], i)
        table.insert(self.options, OptionInstance)
    end
end

---@type fun(): table 获取Dialogue的所有选项
function DialogueEpisode:GetEpisodeOptions()
    self:InitDialogueOptions()

    local function ComposeAssetPath(iconPath)
        local packageName = iconPath.AssetPath.PackageName
        local assetName = iconPath.AssetPath.AssetName
        if not packageName or packageName == "None" or packageName == ""
            or not assetName or assetName == "None" or assetName == "" then
            return nil
        end

        local t = {
            packageName,
            ".",
            assetName
        }

        return table.concat(t)
    end

    for index, option in ipairs(self.options) do
        local OptionData = self.episodeConfig.Options[index]
        local Item = OptionData.Item
		if Item and Item.Icon then
			option.Icon = ComposeAssetPath(Item.Icon.Path)
		end
        option.EpisodeID = OptionData.EpisodeID

        -- 如果已经选过该option，并且配置了选择后隐藏，则过滤
        if option.OptionID ~= 0 and Game.DialogueManagerV2.DialogueHistory:HasSelectedOption(option.OptionID) and option:HideWhenPassed() then
            goto continue
        end
        -- 更新option的state数据(之前的逻辑有冗余，可以合并优化到一个函数里面去)
        option:SetOptionState(option:CheckOptionConditionValid(OptionData.Condition))
        option.OptionText = Game.NPCManager.GetFormatTalkText(option.OptionText)
        option.Order = index

        if option:IsLock() then
            --该选项被锁定，去选项表查询这个选项释放应该显示, (只有浮动选项可以,普通选项配置了也不会生效)
            if (option.LockVisible == 1) and (self.episodeConfig.OptionType == 1) then
                --锁定的时候也显示
                --获取锁定状态的文本
                if option.LockText ~= "" then
                    option.OptionText = Game.NPCManager.GetFormatTalkText(option.LockText)
                end
                table.insert(self.showOptions, option)
                Log.DebugFormat("Show Option %s", option.OptionText)
            else
                --锁定的时候不显示，则不添加这个选项到UI
            end
        else
            table.insert(self.showOptions, option)
            Log.DebugFormat("Show Option %s", option.OptionText)
        end
        :: continue ::
    end

    return self.showOptions
end

---@type fun(): nil 展示选项
function DialogueEpisode:ShowDialogueOptions()
    -- 通过DialogueManagerV2.UIProcessor 拉起WBP_Dialogue，显示UI数据
    -- TOOD: 这里好像有个倒计时结束，随机选项，放这里算有点离谱，不是应该等倒计时结束再算吗？
    local choice = nil
    Game.DialogueManagerV2.UIProcessor:ShowDialogueOptions(self.showOptions, self.episodeConfig.OptionType,
        self.episodeConfig.TimeLimit ~= 0, self.episodeConfig.TimeLimit, choice)

    -- 开始展示选项逻辑，暂停Episode track Tick后的逻辑
    self:SetInOptionProcess(true)
end

function DialogueEpisode:SetInOptionProcess(bResult)
    self.bInOptionProcess = bResult
    --if bResult then
    --    self.dialogueInstance:AddDialogueBarrier(DialogueConst.BARRIER_TYPE.WAIT_SELECT_OPTION)
    --end
end

function DialogueEpisode:OnSelectOption(Index)
    local SelectedOptionData = self.options[Index]
    SelectedOptionData:SetOptionState(DialogueConst.OptionState.Passed)

    -- 通知倒计时系统停止
    Game.NPCCountDownSystem:OnDialogueChoiceMade()

    Game.DialogueManagerV2.DialogueHistory:Record(1, SelectedOptionData.OptionText, "", "", SelectedOptionData.OptionID)

    Game.DialogueManagerV2.LastOptionID = SelectedOptionData.OptionID
    -- 执行额外的行为
    if ksbcnext(SelectedOptionData.ExtraAction) then
        SelectedOptionData:executeExtraAction(SelectedOptionData.ExtraAction)
    end

    Game.DialogueManagerV2.UIProcessor:SetReviewButtonVisible(true)
    Game.DialogueManagerV2.UIProcessor:SetSkipButtonVisible(true)
	

	-- 已经选择过选项，关闭掉选项UI
	Game.DialogueManagerV2.UIProcessor:OnSelectOption()

    if not SelectedOptionData.WaitGamePlay and not SelectedOptionData.WaitCloseUI then
        self:ExecuteSelectOption(SelectedOptionData)
    end

	-- 选项部分逻辑结束，恢复Episode的Tick逻辑
	self:SetInOptionProcess(false)
end

function DialogueEpisode:ExecuteSelectOption(SelectedOptionData)
    if SelectedOptionData and SelectedOptionData.bClose == true then
        -- 如果配置了关闭,则直接关闭
        Log.DebugFormat("[OnSelectOption] %d directly finish", SelectedOptionData.OptionID)
        self.dialogueInstance:Deactivate()
        return
    end

    local NextEpisodeRef = self.dialogueInstance:GetEpisodeByIndex(SelectedOptionData.EpisodeID)
    if not NextEpisodeRef then
        --未找到合法的Episode数据
        local NextEpisodeIndex = self.dialogueInstance:GetCurEpisodeIndex() + 1
        Log.ErrorFormat(
        "Dialogue %s has no valid episode %d, Cur Selected OptionID is %s. Please check the validity of the data",
            tostring(self.dialogueInstance.DialogueID), NextEpisodeIndex, tostring(SelectedOptionData.OptionID))
        Game.DialogueManagerV2:TerminateDialogue(self.dialogueInstance.DialogueID)
        return
    end

    Game.DialogueManagerV2.DialogueHistory:AddSelectOption(SelectedOptionData.OptionID, self.dialogueInstance.DialogueID)

    --TOOD: 在这里发送一个事件，告诉外部系统玩法，比如阿罗德斯对话玩法，此时外部系统可以借机填充对话数据
    --Game.EventSystem:Publish(_G.EEventTypes.DIALOGUE_ON_SELECT_OPTION, SelectedOptionData.OptionID, Game.DialogueManagerV2.OptionTalkID, Game.DialogueManagerV2.DialogueTable)

    self.dialogueInstance:CreateNewEpisode(SelectedOptionData.EpisodeID)
end

--------------------------------------------------------- REGION Options End ----------------------------------------------

---@public
function DialogueEpisode:UnInit()
    -- 小段结束的时候检查是否有没处理的消息
    local flowchartMsgList = self.dialogueInstance:GetBlackBoardValue(DialogueConst.BlackBoardKey.FLOWCHART_MSG)
    if (flowchartMsgList ~= nil) and (Game.me ~= nil) then
        for _, msg in pairs(flowchartMsgList) do
            Log.DebugFormat("[checkLogicBeforeFinish] send flowchart msg:%s", msg)
            Game.me:ReqClientAIMessage(msg)
        end

        self.dialogueInstance:SetBlackBoardValue(DialogueConst.BlackBoardKey.FLOWCHART_MSG, nil)
    end

    for _, track in pairs(self.tracks) do
        track:UnInit()
    end
    table.clear(self.tracks)

    ---- 清理所有options
    --for _, option in pairs(self.options) do
    --    option:UnInit()
    --end
    table.clear(self.options)

	-- 如果销毁比 UI_CLOSE早的话，也需要把listener清理掉
	Game.GlobalEventSystem:RemoveListener(EEventTypesV2.ON_UI_CLOSE, "OnCloseUI", self)

    self:delete()
end

---@public
---@param deltaTime number
---@param bSkip boolean
function DialogueEpisode:Tick(deltaTime, bSkip)
    if not self.bPause then
        self.runningTime = self.runningTime + deltaTime
    end

    for _, track in ipairs(self.tracks) do
        track:Tick(deltaTime, bSkip)
    end

    -- 当前小段未结束
    if self.runningTime < self.episodeConfig.Duration then
        return
    end

    -- 时间已到,但是跳过期内可能有东西暂停了对话,要忽略
    if (self.bPause == true) and (bSkip ~= true) then
        return
    end

    -- TODO: 这里需要将tick里基于状态标记处理相关逻辑的实现，修改为两套状态机(play, pause)流程

    -- 当前在等待选项选择
    if self.bInOptionProcess then
        return
    end

    -- 当前在 等待物品提交/领取任务环 阶段(这俩都需要打开UI，等待UI关闭后再继续执行episode tick流)
    if self.WaitCloseUI then
        return
    end

    -- 如果还有选项可以选择，则展示选项面板，执行选项逻辑
    if next(self:GetEpisodeOptions()) then
        -- 开始处理选项逻辑
        self:DealWithDialogueOptionsLogic()
        return
    end

    -- 检查下是否存在SubmitItem的流程
    if self:CheckDialogueSubmit() then
        self:ShowSubmitItemPanel()
        return
    end

    -- 检查下是否存在领取任务环的流程
    if self.dialogueInstance.PlayParams.ReceiveRingID then
        self:ShowReceiveRingPanel()
        return
    end

    -- 没有选项了,结束对话
    if self.bFinish then
        return
    end

    self.bFinish = true

    -- 非跳过直接结束,跳过的情况下由外部控制结束
    if not bSkip then
        self.dialogueInstance:Deactivate()
    end
end

function DialogueEpisode:CheckDialogueSubmit()
    return self.dialogueInstance.PlayParams.SubmitItemID or self.dialogueInstance.PlayParams.NewSubmitCfg
end

function DialogueEpisode:ShowSubmitItemPanel()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_UI_CLOSE, "OnCloseUI", self)
    self.WaitCloseUI = UIPanelConfig.SubmitNew_Panel
    Log.DebugFormat("execute SubmitItem function, now open %s", self.WaitCloseUI)
    local submitParam = self.dialogueInstance.PlayParams
    ---@type ItemSubmitCompatibleParams
    local realSubmitParam = {
        ItemSubmitID = submitParam.SubmitItemID,
        TaskRingID = submitParam.TaskRingID,
        QuestID = submitParam.QuestID,
        CondIdx = submitParam.CondIdx,
        NewSubmitCfg = submitParam.NewSubmitCfg,
        OpIdx = self.LastOpIdx or 1,
    }
    Game.ItemSubmitSystem:ShowItemSubmitUICompatible(realSubmitParam)
end

function DialogueEpisode:OnCloseUI(UIName)
	if self.WaitCloseUI == UIName then
		Log.DebugFormat("[DialogueEpisode] OnCloseUI %s.", UIName)
		Game.GlobalEventSystem:RemoveListener(EEventTypesV2.ON_UI_CLOSE, "OnCloseUI", self)
		self.WaitCloseUI = nil
		self.dialogueInstance:Deactivate()
	end
end

function DialogueEpisode:ShowReceiveRingPanel()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_UI_CLOSE, "OnCloseUI", self)
    self.WaitCloseUI = UIPanelConfig.TaskTipsPanel
    local receiveRingID = self.dialogueInstance.PlayParams.ReceiveRingID
    local npcCfgID = self.dialogueInstance.PlayParams.NPCID
    Log.DebugFormat("[DialogueEpisode] execute Receive Ring function, now open %s RingID is %d", UIName, receiveRingID)
    Game.NewUIManager:OpenPanel(self.WaitCloseUI, receiveRingID, npcCfgID or 0, self.dialogueInstance.DialogueID)
end

function DialogueEpisode:DealWithDialogueOptionsLogic()
    if #self.showOptions > 0 then
        local bOptionHasBeenAutoDone = false
        -- 检查对话选项中是否存在DialogueID == 0的存在，如果有的话，则无需点击，判断condition后直接执行option对应的action
        for _, option in ipairs(self.showOptions) do
            if option.OptionID == 0 then
                option:SetOptionState(option:CheckOptionConditionValid(option.Condition))
                if option:IsUnlock() then
                    self.dialogueInstance:CreateNewEpisode(option.EpisodeID)
                    bOptionHasBeenAutoDone = true
                    break
                end
            end
        end
        -- 没有任何自动执行的选项逻辑，开始正常的选项展示了
        if not bOptionHasBeenAutoDone then
            self:ShowDialogueOptions()
        end
    end
end

---@public
---@return number
function DialogueEpisode:GetEpisodeIndex()
    return self.episodeIdx
end

---@public
---@return number
function DialogueEpisode:GetRemainTime()
    return self.episodeConfig.Duration - self.runningTime
end

---从指定时间点开始播放对话小段
---@public
---@param startTime number|nil 不传则默认从0开始
function DialogueEpisode:StartEpisodeFromSpecificTime(startTime)
    startTime = startTime or 0

    -- 传入时间非法
    if (startTime < 0) or (self.episodeConfig.Duration < startTime) then
        Log.ErrorFormat("[StartEpisodeFromSpecificTime] illegal start time %s", startTime)
        return
    end

    self.startTime = startTime
    Log.DebugFormat("[StartEpisodeFromSpecificTime] startTime=%s", startTime)

    -- 如果不是从0开始的,需要先Tick到对应时间
    if self.startTime > 0 then
        self:Tick(self.startTime, true)
    end
end

---从第几句台本开始播放对话小段
---@public
---@param lineIndex number
function DialogueEpisode:StartEpisodeFromLineIndex(lineIndex)
    Log.DebugFormat("[StartEpisodeFromLineIndex] try start episode from %s", lineIndex)
    local startTime = 0
    local sectionConfigs = self:getDialogueSectionConfigs()
    if (sectionConfigs ~= nil) and (lineIndex <= #sectionConfigs) then
        local targetSection = sectionConfigs[lineIndex]
        startTime = targetSection.StartTime
    else
        Log.WarningFormat("[StartEpisodeFromLineIndex] invalid line index %s", lineIndex)
    end

    self:StartEpisodeFromSpecificTime(startTime)
end

---@public
function DialogueEpisode:PauseEpisode()
    self.bPause = true

    for _, track in pairs(self.tracks) do
        track:PauseTrack()
    end
end

---@public
function DialogueEpisode:ResumeEpisode()
    self.bPause = false

    for _, track in pairs(self.tracks) do
        track:ResumeTrack()
    end
end

---获取当前小段一共有几句台本
---@private
---@return table[]
function DialogueEpisode:getDialogueSectionConfigs()
    for _, trackConfig in ksbcipairs(self.episodeConfig.TrackList) do
        if trackConfig.ObjectClass == DialogueConst.DIALOGUE_TRACK_TYPE.DIALOGUE then
            return trackConfig.ActionSections
        end
    end

    return nil
end

return DialogueEpisode
