---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 11:58
---
local NormalDialogueInstance = kg_require("Gameplay.DialogueV2.Instance.NormalDialogueInstance")
local SimpleDialogueUtils = kg_require("Gameplay.DialogueSystem.Simple.SimpleDialogueUtils")

---@class SimpleDialogueInstance : NormalDialogueInstance
local SimpleDialogueInstance = DefineClass("SimpleDialogueInstance", NormalDialogueInstance)

function SimpleDialogueInstance:ctor()
    self.bSimpleDialogue = true
end

function SimpleDialogueInstance:UnInit()
    SimpleDialogueUtils.ClearDialogueTemplateData()
    NormalDialogueInstance.UnInit(self)
end

---@overload
function SimpleDialogueInstance:RegisterStage()
    self.registeredInStage = {
        [DialogueConst.STAGE.PRELOAD_RESOURCE] = 1,
        [DialogueConst.STAGE.SPAWN_PARTICIPANT] = 1,
        [DialogueConst.STAGE.OPEN_DIALOGUE_PANEL] = 1,
        [DialogueConst.STAGE.BEGIN_PLAY] = 1,
        [DialogueConst.STAGE.END_PLAY] = 1,
        [DialogueConst.STAGE.FINISH] = 1,
    }
end

function SimpleDialogueInstance:GetDialogueConfig()
    return SimpleDialogueUtils.FillDialogueData(self.DialogueID, nil, self.PlayParams.ActorEntityID, self.PlayParams.Distance)
end

function SimpleDialogueInstance:ON_STAGE_BEGIN_PLAY()
    -- 处理各单位显隐
    self:DealEntityVisibilityBeforeDialogueBegin()

    -- 处理外部事件
    self.bActivated = true
    self:DealStateAndEventOnStart()
end

function SimpleDialogueInstance:ON_STAGE_END_PLAY()
    self:HandleBeforeEndPlay()

    -- 处理各单位显隐
    self:DealEntityVisibilityAfterDialogueEnd()
    -- 关闭UI
    Game.DialogueManagerV2.UIProcessor:CloseDialoguePanel()

    self:DealStateAndEventOnEnd()
    self.bActivated = false

    self:ProgressStage()
end

return SimpleDialogueInstance
