---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 11:24
---
local NormalDialogueInstance = kg_require("Gameplay.DialogueV2.Instance.NormalDialogueInstance")

---@class ListDialogueInstance : NormalDialogueInstance
local ListDialogueInstance = DefineClass("ListDialogueInstance", NormalDialogueInstance)

function ListDialogueInstance:ctor()
    self.dialogueIndex = 1
    self.firstDialogueID = 0
end

function ListDialogueInstance:dtor()

end

function ListDialogueInstance:Init()
    self.DialogueID = self.PlayParams.AssetID[self.dialogueIndex]
    NormalDialogueInstance.Init(self)
end

---@overload
function ListDialogueInstance:GetDialogueConfig()
    return Game.DialogueManagerV2:GetDialogueConfig(self.DialogueID)
end

---一个对话表演结束
function ListDialogueInstance:ON_STAGE_END_PLAY()
    self.dialogueIndex = self.dialogueIndex + 1
    local newDialogueID = self.PlayParams.AssetID[self.dialogueIndex]

    -- 播完了,走结束流程
    if not newDialogueID then
        return self:ProgressStage()
    end

    -- 没配置了,也结束
    local dialogueAssetData = Game.TableData.GetDialogueAssetDataRow(newDialogueID)
    if not dialogueAssetData then
        return self:ProgressStage()
    end

    -- 没资产了,也结束
    local newDialogueConfig = self:GetDialogueConfig(newDialogueID)
    if not newDialogueConfig then
        return self:ProgressStage()
    end

    -- 清理上一段对话的数据但不结束
    self:HandleBeforeEnd()

    -- 为下一段重置一些数据
    self.DialogueID = newDialogueID
    self.DialogueConfig = newDialogueConfig
    self.bCanSkip = dialogueAssetData.CanSkip

    self.bActivated = false

    -- 从头开始
    self:SkipToTargetStage(DialogueConst.STAGE.PRELOAD_RESOURCE)
end

return ListDialogueInstance
