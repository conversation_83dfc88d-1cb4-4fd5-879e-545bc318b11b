---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 11:59
---
local DialogueInstanceBase = kg_require("Gameplay.DialogueV2.Instance.DialogueInstanceBase")

---@class NoCameraDialogueInstance : DialogueInstanceBase
local NoCameraDialogueInstance = DefineClass("NoCameraDialogueInstance", DialogueInstanceBase)

---@overload
function NoCameraDialogueInstance:RegisterStage()
    self.registeredInStage = {
        [DialogueConst.STAGE.PRELOAD_RESOURCE] = 1,
        [DialogueConst.STAGE.SPAWN_PARTICIPANT] = 1,
        [DialogueConst.STAGE.BEGIN_PLAY] = 1,
        [DialogueConst.STAGE.END_PLAY] = 1,
        [DialogueConst.STAGE.FINISH] = 1,
    }
end

---对话表演前,处理对话内外所有单位的显隐
---@overload
function NoCameraDialogueInstance:DealEntityVisibilityBeforeDialogueBegin()
    -- 处理其他单位显隐
    --self:DealNonDialogueNpcVisibility(false)

    -- 处理所有参与者显隐
    self.ptpManager:SetParticipantDefaultVisibility()
end

---对话表演后,处理对话内外所有单位的显隐
---@overload
function NoCameraDialogueInstance:DealEntityVisibilityAfterDialogueEnd()
    -- 处理其他单位显隐
    --self:DealNonDialogueNpcVisibility(true)

    -- 隐藏所有参与者
    self.ptpManager:HideAllParticipant()
end

---@overload
function NoCameraDialogueInstance:ON_STAGE_BEGIN_PLAY()
    -- 处理各单位显隐
    self:DealEntityVisibilityBeforeDialogueBegin()
    self.bActivated = true
end

---@overload
function NoCameraDialogueInstance:ON_STAGE_END_PLAY()
    self.bActivated = false
    -- 处理各单位显隐
    self:DealEntityVisibilityAfterDialogueEnd()
    self:ProgressStage()
end

return NoCameraDialogueInstance
