---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 11:20
---

local DialogueParticipantManager = kg_require("Gameplay.DialogueV2.DialogueParticipantManager")
--local DialogueInputHandler = kg_require("Gameplay.DialogueV2.DialogueInputHandler")
local DialogueEpisode = kg_require("Gameplay.DialogueV2.DialogueEpisode")

---@class DialogueInstanceBase
---@field bSimpleDialogue boolean
local DialogueInstanceBase = DefineClass("DialogueInstanceBase")

---@param playParams DialoguePlayParams
function DialogueInstanceBase:ctor(playParams)
    self.stage = 0

    self.DialogueID = playParams.AssetID
    self.PlayParams = playParams

    --region 一些关键状态
    ---标记对话表演真正开启
    self.bActivated = false
    ---标记暂停
    self.bPause = false
    ---标记是否可跳过
    self.bCanSkip = true
    ---跳过过程中的锁
    self.skipLock = nil
    --endregion 一些关键状态

    -- 对话资产静态数据
    self.DialogueConfig = nil

    ---@type DialogueParticipantManager
    self.ptpManager = nil

    ---@type ? 还未实现
    self.inputHandler = nil

    ---@type DialogueEpisode 当前正在播放的小段
    self.currentEpisode = nil

    --- 黑板类
    self.BlackBoard = {}

    ---用于处理范围Npc隐藏的trigger
    self.rangeHideNpcTrigger = nil

    ---@type DialogueBarrierBase[] 暂停屏障
    self.tickBarriers = {}
	
	-- 存储Cinematic结束后的回调
	if playParams and playParams.OnCinematicFinished then
		self.OnCinematicFinished = playParams.OnCinematicFinished
	else
		self.OnCinematicFinished = nil
	end
end

function DialogueInstanceBase:dtor()
    Game.EventSystem:RemoveObjListeners(self)
end

---@protected
function DialogueInstanceBase:RegisterStage()
    error("need override RegisterStage")
end

function DialogueInstanceBase:Init()
    local dialogueAssetData = Game.TableData.GetDialogueAssetDataRow(self.DialogueID)
    self.bCanSkip = dialogueAssetData and dialogueAssetData.CanSkip or true

    -- 每次起新的dialogueInstance，清理掉DialogueHistory的Review部分
    Game.DialogueManagerV2.DialogueHistory:ClearReview()
end

function DialogueInstanceBase:UnInit()
    self:HandleBeforeEnd()
    self:delete()
end

---对话真正结束前的清理,状态重置,事件触发,时序很重要,如果修改需要多测
---@protected
function DialogueInstanceBase:HandleBeforeEnd()
    self.currentEpisode:UnInit()
    self.currentEpisode = nil

    -- 上面所有Section结束,有些黑板值才会设置上来
    self:HandleBlackBoardBeforeEnd()

    -- 前面的Section结束和黑板值有的依赖演员,所以演员要在之后销毁
    self.ptpManager:UnInit()
    self.ptpManager = nil

    self:ReleaseResource()
end

---获取对话Table,可以重载
---@protected
function DialogueInstanceBase:GetDialogueConfig()
    return Game.DialogueManagerV2:GetDialogueConfig(self.DialogueID)
end

---处理对话表演开始前的逻辑
---@public
function DialogueInstanceBase:Activate()
    Log.DebugFormat("[Activate] DialogueID=%s", self.DialogueID)
    self:ProgressStage()
end

---处理对话表演结束后的逻辑
---@public
function DialogueInstanceBase:Deactivate()
    Log.DebugFormat("[Deactivate] DialogueID=%s", self.DialogueID)
    self:ProgressStage()
end

---外部中断对话
---@public
function DialogueInstanceBase:Terminate()
    Log.DebugFormat("[Terminate] DialogueID=%s", self.DialogueID)

    -- 中断不走stage逻辑,直接退出
    self.currentEpisode:UnInit()
    self.currentEpisode = nil

    self:HandleBlackBoardBeforeEnd()

    self:DealEntityVisibilityAfterDialogueEnd()

    self.ptpManager:UnInit()
    self.ptpManager = nil

    self:ReleaseResource()

    if Game.CameraManager then
        Game.CameraManager:EnableDialogueCamera(false)
    end

    Game.DialogueManagerV2.UIProcessor:CloseDialoguePanel()

    self:DealStateAndEventOnEnd()

    self:delete()
end

---@protected
function DialogueInstanceBase:DealStateAndEventOnStart()
    Game.EventSystem:Publish(_G.EEventTypes.DIALOGUE_PLOT_ON_START, self.DialogueID, self.PlayParams.ActorEntityID)
end

---@protected
function DialogueInstanceBase:DealStateAndEventOnEnd()
    local lastOptionID = Game.DialogueManagerV2.DialogueHistory.LastOptionTextID
    Game.EventSystem:Publish(_G.EEventTypes.DIALOGUE_PLOT_ON_END, self.PlayParams.AssetID, lastOptionID, self.PlayParams.NPCID, self.PlayParams.ActorEntityID, self.PlayParams.QuestID, self.PlayParams.CondIdx)

	if self.OnCinematicFinished then
		xpcall(self.OnCinematicFinished, _G.CallBackError)
	end
end

---@public
---@return boolean
function DialogueInstanceBase:IsPause()
    return self.bPause
end

---@public
function DialogueInstanceBase:PausePlay()
    -- 处于自动播放中,且没有锁,不能暂停
    if (Game.DialogueManagerV2:IsAutoPlay() == true) and (self.skipLock == nil) then
        Log.Debug("[PausePlay] in auto playing, cannot pause")
        return
    end

    if self.bPause then
        Log.Warning("[PausePlay] in pausing, cannot reentrancy")
        return
    end

    Log.DebugFormat("[PausePlay] DialogueID=%s", self.DialogueID)
    self.bPause = true
    if self.currentEpisode then
        self.currentEpisode:PauseEpisode()
    end
end

---@public
function DialogueInstanceBase:ResumePlay()
    if not self.bPause then
        Log.Warning("[ResumePlay] in playing, cannot reentrancy")
        return
    end

    Log.DebugFormat("[ResumePlay] DialogueID=%s", self.DialogueID)
    self.bPause = false
    if self.currentEpisode then
        self.currentEpisode:ResumeEpisode()
    end
end

---@public
---@param deltaTime number
---@param bSkip boolean
function DialogueInstanceBase:Tick(deltaTime, bSkip)
    if not self.bActivated then
        return
    end

    for idx = #self.tickBarriers, 1, -1 do
        local barrier = self.tickBarriers[idx]
        barrier:TickBarrier(deltaTime)
        if barrier:NeedFinish() then
            barrier:FinishBarrier()
            self.tickBarriers[idx] = nil
        end
    end

    if self.currentEpisode then
        self.currentEpisode:Tick(deltaTime, bSkip)
    end
end

---@public
---@param bAutoPlay boolean
function DialogueInstanceBase:OnAutoPlayStateChange(bAutoPlay)
    -- 如果开启自动播放的同时在暂停中,继续
    if (bAutoPlay == true) and (self.bPause == true) then
        self:ResumePlay()
    end
end

---@public
function DialogueInstanceBase:ReleaseResource()
    Log.DebugFormat("[ReleaseResource] DialogueID=%s", self.DialogueID)
    Game.AkAudioManager:SyncUnloadBank(tostring(self.DialogueID), self)
end

---@public
---@return number
function DialogueInstanceBase:GetCurEpisodeIndex()
    if not self.currentEpisode then
        return 0
    end

    return self.currentEpisode:GetEpisodeIndex()
end

---@public
---@param episodeIdx number
function DialogueInstanceBase:CreateNewEpisode(episodeIdx)
    -- 先清理barrier
    for _, barrier in pairs(self.tickBarriers) do
        barrier:FinishBarrier()
    end
    table.clear(self.tickBarriers)

    if self.currentEpisode then
        self.currentEpisode:UnInit()
        self.currentEpisode = nil
    end

    local episodeConfig = self.DialogueConfig.Episodes[episodeIdx]
    if not episodeConfig then
        Log.ErrorFormat("[CreateEpisode] no target episode idx %s in %s", episodeIdx, self.DialogueID)
        return
    end

    self.currentEpisode = DialogueEpisode.new(self, episodeIdx, episodeConfig)
    self.currentEpisode:Init()

    if self.PlayParams.StartEpisodeLineIdx then
        self.currentEpisode:StartEpisodeFromLineIndex(self.PlayParams.StartEpisodeLineIdx)
    else
        self.currentEpisode:StartEpisodeFromSpecificTime(self.PlayParams.StartTime or 0)
    end
end

---获取下个小段index,如果没有就返回-1
---@private
---@return number
function DialogueInstanceBase:getNextEpisodeIndex()
    local nextEpisodeIndex = self:GetCurEpisodeIndex() + 1
    if self.DialogueConfig.Episodes[nextEpisodeIndex] ~= nil then
        return nextEpisodeIndex
    else
        return -1
    end
end

function DialogueInstanceBase:GetEpisodeByIndex(EpisodeIndex)
    return self.DialogueConfig.Episodes[EpisodeIndex]
end

---@public
---@param barrierType string
---@param duration number|nil 传空表示无限时长
function DialogueInstanceBase:AddDialogueBarrier(barrierType, duration, ...)
    local barrierCls = DialogueConst.BARRIER_CLASS[barrierType]
    if not barrierCls then
        Log.ErrorFormat("[AddDialogueBarrier] %s not exist", barrierType)
        return
    end

    ---@type DialogueBarrierBase
    local barrier = barrierCls.new(self, duration, ...)
    barrier:StartBarrier()
    table.insert(self.tickBarriers, barrier)
end

---@public
function DialogueInstanceBase:OnDialoguePanelClicked()
    -- 没有屏障,点击就执行跳过
    if #self.tickBarriers == 0 then
        self:ResumePlay()
        return
    end

    -- 否则只执行屏障逻辑
    for idx = #self.tickBarriers, 1, -1 do
        local barrier = self.tickBarriers[idx]
        if barrier:CanSkip() then
            barrier:FinishBarrier()
            self.tickBarriers[idx] = nil
        end
    end

    -- 屏障都没了,也执行恢复
    if #self.tickBarriers == 0 then
        self:ResumePlay()
    end
end

function DialogueInstanceBase:OnDialogueContentPause(dialogueID)
    if self.DialogueID == dialogueID then
        self:PausePlay()
    end
end

---跳过时的tick间隔,按30帧给
DialogueInstanceBase.__SkipTickUnit__ = 0.033

---直接跳到小段末尾
---@public
function DialogueInstanceBase:SkipEpisode()
    -- 跳过时如果在暂停,需要先恢复
    if self:IsPause() then
        self:ResumePlay()
    end

    Game.DialogueManagerV2.UIProcessor:CallPanelFunction(DialogueConst.PanelFunction.TryFlushContentPrinter)

    local remainTime = self.currentEpisode:GetRemainTime()
    xpcall(self.Tick, CallBackError, self, remainTime, true)

    if self.currentEpisode.bFinish then
        self:Deactivate()
    end
end

---@public
---@param period number
function DialogueInstanceBase:SkipPeriodDialogue(period)
    Log.DebugFormat("[SkipPeriodDialogue] period=%s", period)

    Game.DialogueManagerV2.UIProcessor:CallPanelFunction(DialogueConst.PanelFunction.TryFlushContentPrinter)

    self.skipLock = 1

    while period > 0 do
        period = period - self.__SkipTickUnit__
        if period < 0 then
            period = 0
        end

        xpcall(self.Tick, CallBackError, self, self.__SkipTickUnit__, true)

        -- 如果有其他section暂停了对话,那么这里也要暂停
        if self.bPause then
            Log.DebugFormat("[SkipPeriodDialogue] pause in process skip, period left %s", period)
            break
        end

        if self.currentEpisode.bFinish then
            break
        end
    end

    self.skipLock = nil

    if self.currentEpisode.bFinish then
        self:Deactivate()
    end
end

-- 设置黑板属性数据
function DialogueInstanceBase:SetBlackBoardValue(key, value)
    self.BlackBoard[key] = value
end

-- 获取黑板属性数据
function DialogueInstanceBase:GetBlackBoardValue(key)
    return self.BlackBoard[key]
end

---@public
---@return table[]
function DialogueInstanceBase:GetAllTrackConfigsInEpisode(episodeIdx)
    local episodeConfig = self.DialogueConfig.Episodes[episodeIdx]
    if not episodeConfig then
        return {}
    end

    local trackList = {}
    local queue = {}

    for _, v in ksbcipairs(episodeConfig.TrackList) do
        table.insert(queue, v)
    end

    while #queue ~= 0 do
        local queueTrack = queue[1]
        table.remove(queue, 1)
        table.insert(trackList, queueTrack)
        for _, v in ksbcipairs(queueTrack.Childs) do
            table.insert(queue, v)
        end
    end

    -- 这里重新排序的原因是要每轮tick时都要最先tick到台本Section,因为台本section内部会控制对话的暂停状态
    for idx, track in ksbcipairs(trackList) do
        if track.TrackName == "Dialogue" then
            table.remove(trackList, idx)
            table.insert(trackList, 1, track)
            break
        end
    end

    return trackList
end

---控制非对话单位的显隐
---@public
---@param bVisible boolean
function DialogueInstanceBase:DealNonDialogueNpcVisibility(bVisible)
    -- 处理服务器Npc
    if self.DialogueConfig.HideNpcType == DialogueConst.NPC_HIDE_TYPE.ALL then
        Game.WorldManager:SetTaskNpcCategoryVisible(bVisible)

    elseif self.DialogueConfig.HideNpcType == DialogueConst.NPC_HIDE_TYPE.RANGE then
        local anchorLocation = self.ptpManager:GetAnchorPtpLocation()
        if self.rangeHideNpcTrigger then
            Game.WorldManager:ReleaseContinuousHiddenByDis(self.rangeHideNpcTrigger)
            self.rangeHideNpcTrigger = nil
        end

        if not bVisible then
            self.rangeHideNpcTrigger = Game.WorldManager:ClaimContinuousHiddenByDis(anchorLocation, self.DialogueConfig.HideNpcRange)
        end

    elseif self.DialogueConfig.HideNpcType == DialogueConst.NPC_HIDE_TYPE.RANGE then
        for _, performerConfig in ksbcipairs(self.DialogueConfig.PerformerList) do
            if not performerConfig.bIsPlayer then
                Game.WorldManager:SetNpcInvisibleControlByConfigIDForDialogue(not bVisible, performerConfig.AppearanceID)
            end
        end

    elseif self.DialogueConfig.HideNpcType == DialogueConst.NPC_HIDE_TYPE.SAME_CONFIG then
        for _, performerConfig in ksbcipairs(self.DialogueConfig.PerformerList) do
            Game.WorldManager:SetNpcInvisibleControlByConfigIDForDialogue(not bVisible, performerConfig.AppearanceID)
        end
    end

    -- 处理氛围Npc
    if self.DialogueConfig.HideAtmosphereNpc then
        Game.WorldManager:SetEntityVisibleByCategory(Enum.EHideEntityCategory.MassAI, bVisible)
    end
end

---对话表演前,处理对话内外所有单位的显隐
---@protected
function DialogueInstanceBase:DealEntityVisibilityBeforeDialogueBegin()
    -- 未使用真人玩家,到一半时隐藏
    if not self.ptpManager.bUseRealMainPlayer then
        Game.me:ForceToInvisibleByDialogue()
    end

    -- 处理其他单位显隐
    self:DealNonDialogueNpcVisibility(false)

    -- 处理所有参与者显隐
    self.ptpManager:SetParticipantDefaultVisibility()
end

---对话表演后,处理对话内外所有单位的显隐
---@protected
function DialogueInstanceBase:DealEntityVisibilityAfterDialogueEnd()
    -- 未使用真人玩家,到一半时显示
    if (self.ptpManager.bUseRealMainPlayer == false) and (Game.me ~= nil) then
        Game.me:ForceToVisibleByDialogue()
    end

    -- 处理其他单位显隐
    self:DealNonDialogueNpcVisibility(true)

    -- 隐藏所有参与者
    self.ptpManager:HideAllParticipant()
end

---结束时处理残留的黑板逻辑
---@private
function DialogueInstanceBase:HandleBlackBoardBeforeEnd()
    -- 重置雾效
    local bNeedRestFog = self:GetBlackBoardValue(DialogueConst.BlackBoardKey.NEED_RESET_FOG)
    if bNeedRestFog then
        Game.PostProcessManager:RemoveFog()
    end

    -- 重置后效
    local ppIDList = self:GetBlackBoardValue(DialogueConst.BlackBoardKey.POST_PROCESS_ID_LIST)
    for _, ppID in pairs(ppIDList or {}) do
        Game.PostProcessManager:RemovePP(ppID)
    end

    -- 重置因后效设置而受影响的单位
    local ppExcludePerformerList = self:GetBlackBoardValue(DialogueConst.BlackBoardKey.PP_EXCLUDE_PERFORMERS)
    for _, ptpName in pairs(ppExcludePerformerList or {}) do
        self.ptpManager:SetPtpExcludeFromPostProcess(ptpName, false)
    end

    -- 重置简易相机模式
    local bNeedExitSimpleCameraMode = self:GetBlackBoardValue(DialogueConst.BlackBoardKey.NEED_EXIT_SIMPLE_CAMERA_MODE)
    if bNeedExitSimpleCameraMode then
        Game.CameraManager:EnableSimpleDialogueCamera(false)
    end

    -- 如果有黑屏白字界面没关闭,需要关闭
    local bNeedCloseScreenText = self:GetBlackBoardValue(DialogueConst.BlackBoardKey.NEED_CLOSE_SCREEN_TEXT)
    if bNeedCloseScreenText then
        Game.NewUIManager:ClosePanel(UIPanelConfig.DialogueScreenText_Panel)
    end

    table.clear(self.BlackBoard)
end


--region Stage


function DialogueInstanceBase:ON_STAGE_PRELOAD_RESOURCE()
    Log.DebugFormat("[ON_STAGE_PRELOAD_RESOURCE] DialogueID=%s", self.DialogueID)

    self.DialogueConfig = self:GetDialogueConfig()

    Game.AkAudioManager:SyncLoadBank(tostring(self.DialogueID), self)

    -- todo:这里先把口子留好
    self:OnResourceLoaded()
end

function DialogueInstanceBase:OnResourceLoaded()
    Log.DebugFormat("[OnResourceLoaded] DialogueID=%s", self.DialogueID)
    self:ProgressStage()
end

function DialogueInstanceBase:ON_STAGE_SPAWN_PARTICIPANT()
    self.ptpManager = DialogueParticipantManager.new(self)
    self.ptpManager:SpawnAllParticipant()
end

function DialogueInstanceBase:OnAllParticipantReady()
    Log.DebugFormat("[OnAllParticipantReady] DialogueID=%s", self.DialogueID)
    self:CreateNewEpisode(self.PlayParams.StartEpisodeLineIdx or 1, nil, self.PlayParams.StartTime)
    self:ProgressStage()
end

function DialogueInstanceBase:ON_STAGE_FINISH()
    Game.DialogueManagerV2:OnDialogueFinished(self.DialogueID)
end

--endregion Stage


--region StageProgress


---阶段函数公共前缀
DialogueInstanceBase.__StageFuncPrefix__ = "ON_STAGE_"

---步进阶段
---@protected
function DialogueInstanceBase:ProgressStage()
    self.stage = self.stage + 1

    if self.stage > DialogueConst.STAGE.FINISH then
        return
    end

    -- 如果未注册对应阶段,则继续步进到下一个阶段
    if not self.registeredInStage[self.stage] then
        self:ProgressStage()
        return
    end

    local stageName = DialogueConst.STAGE_NAME[self.stage]
    local funcName = self.__StageFuncPrefix__ .. stageName
    local func = self[funcName]
    if (func == nil) or (type(func) ~= "function") then
        Log.ErrorFormat("[progressStage] %s:%s stage function not implemented", stageName, self.stage)
        return
    end

    Log.DebugFormat("[progressStage] stage %s:%s in %s", self.stage, stageName, self.DialogueID)
    xpcall(func, CallBackError, self)
end

---跳到指定阶段
---@protected
function DialogueInstanceBase:SkipToTargetStage(newStage)
    Log.DebugFormat("[SkipToTargetStage] %s skip to stage %s", self.DialogueID, newStage)
    self.stage = newStage - 1
    self:ProgressStage()
end


--endregion StageProgress


return DialogueInstanceBase
