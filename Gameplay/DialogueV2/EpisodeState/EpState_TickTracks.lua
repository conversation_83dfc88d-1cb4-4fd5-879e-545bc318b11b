local EpStateBase = kg_require("Gameplay.DialogueV2.EpisodeState.EpStateBase")

---@class EpState_TickTracks : EpStateBase
---@field private episode DialogueEpisode
---@field private stateId DialogueEpisodeStateID
local EpState_TickTracks = DefineClass("EpState_TickTracks", EpStateBase)
function EpState_TickTracks:ctor(episode, epStateId)
    self.episode = episode
    self.stateId = epStateId
end

---@param preStateId DialogueEpisodeStateID
function EpState_TickTracks:OnEnter(preStateId)
end

---@param deltaTime number
---@param bSkip boolean
function EpState_TickTracks:OnTick(deltaTime, bSkip)
    local episode = self.episode
    if not episode.bPause then
        episode.runningTime = episode.runningTime + deltaTime
    end

    local tracks = episode.tracks
    for _, track in ipairs(tracks) do
        track:Tick(deltaTime, bSkip)
    end
end

function EpState_TickTracks:OnExit()
end

function EpState_TickTracks:CanTransit()
    return self.episode.runningTime >= self.episode.episodeConfig.Duration
end

---@return DialogueEpisodeStateID
function EpState_TickTracks:GetNextStateId()
    local episode = self.episode
    if next(episode:GetEpisodeOptions()) then
        return DialogueConst.EPISODE_STATE.EP_STATE_PAUSE_SUB_OPTIONS
    elseif episode:CheckDialogueSubmit() then
        return DialogueConst.EPISODE_STATE.EP_STATE_PAUSE_SUB_SUBMITINT_ITEM
    elseif episode.dialogueInstance.PlayParams.ReceiveRingID then
        return DialogueConst.EPISODE_STATE.EP_STATE_PAUSE_SUB_ACCEPTING_QUEST
    else
        return DialogueConst.EPISODE_STATE.EP_STATE_FINISH
    end
end

return EpState_TickTracks
