---@class EpStateBase
---@field public episode DialogueEpisode
---@field public stateId DialogueEpisodeStateID
local EpStateBase = DefineClass("EpStateBase")
function EpStateBase:ctor(episode, epStateId)
    self.episode = episode
    self.stateId = epStateId
end

---@param preStateId DialogueEpisodeStateID
function EpStateBase:OnEnter(preStateId)
end

---@param deltaTime number
---@param bSkip boolean
function EpStateBase:OnTick(deltaTime, bSkip)
end

function EpStateBase:OnExit()
end

function EpStateBase:CanTransit()
    return true
end

---@return DialogueEpisodeStateID
function EpStateBase:GetNextStateId()
end

return EpStateBase
