---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/29 20:55
---
local DialogueBarrierBase = kg_require("Gameplay.DialogueV2.TickBarrier.DialogueBarrierBase")

---@class DB_SkipVoice : DialogueBarrierBase
local DB_SkipVoice = DefineClass("DB_SkipVoice", DialogueBarrierBase)

function DB_SkipVoice:ctor(_, _, voicePlayingID)
    self.voicePlayingID = voicePlayingID
end

function DB_SkipVoice:OnFinish()
    Game.AkAudioManager:StopEvent(self.voicePlayingID)
    Game.DialogueManagerV2.UIProcessor:CallPanelFunction(DialogueConst.PanelFunction.HideContent)
end

return DB_SkipVoice
