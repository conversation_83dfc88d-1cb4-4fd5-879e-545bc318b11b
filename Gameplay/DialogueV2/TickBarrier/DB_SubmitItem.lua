---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2025/6/18 20:45
---
local DialogueBarrierBase = kg_require("Gameplay.DialogueV2.TickBarrier.DialogueBarrierBase")

---@class DB_SubmitItem : DialogueBarrierBase
local DB_SubmitItem = DefineClass("DB_SubmitItem", DialogueBarrierBase)

function DB_SubmitItem:ctor(_, _, sectionConfig)
	self.sectionConfig = sectionConfig
end

function DB_SubmitItem:CanSkip()
	return false
end

function DB_SubmitItem:OnStart()
	
end

function DB_SubmitItem:OnFinish()
	
end

return DB_SubmitItem
