---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/5/7 14:25
---
local EAttachmentRule = import("EAttachmentRule")
local UMeshComponent = import("MeshComponent")
local DialogueParticipant = kg_require("Gameplay.DialogueV2.DialogueParticipant")
local DialogueUtilV2 = kg_require("Gameplay.DialogueV2.DialogueUtilV2")

---@class DialogueParticipantManager
---@field private lookAtLookUp table<LocalEntityBase, {targetEntity:LocalEntityBase, socket:string, bAutoLookAt:boolean}>
local DialogueParticipantManager = DefineClass("DialogueParticipantManager")

---@param dialogueInstance DialogueInstanceBase
function DialogueParticipantManager:ctor(dialogueInstance)
    self.dialogueID = dialogueInstance.DialogueID
    self.playParams = dialogueInstance.PlayParams

    ---@type DialogueInstanceBase
    self.dialogueInstance = dialogueInstance
    self.dialogueConfig = dialogueInstance.DialogueConfig

    ---@type table<string, DialogueParticipant>
    self.participants = {}
    ---@type table<string, table>
    self.participantConfigs = {}
    self.lookAtLookUp = {}

    self.participantTotalNum = 0
    self.participantReadyNum = 0

    ---@type DialogueParticipant 当前正在说话的演员
    self.curTalkerPerformer = nil

    -- 标记是否使用真实玩家表演
    self.bUseRealMainPlayer = false

    ---所有演员看向说话者
    self.bEnableAllPerformerLookAtTalker = false
    ---@type DialoguePerformer 当前的说话人
    self.curTalker = nil
	---@TYPE DialoguePerformer 下一句说话人
	self.nextTalker = nil
	---@type DialoguePerformer 说话人需要看向的目标
    self.talkerLookAtTarget = nil

    self.delayLookAtTimerList = {}
end

function DialogueParticipantManager:dtor()

end

---@public
function DialogueParticipantManager:UnInit()
    for _, timer in ipairs(self.delayLookAtTimerList) do
        Game.TimerManager:StopTimerAndKill(timer)
    end
    table.clear(self.delayLookAtTimerList)

    self:DestroyAllParticipant()

    self:delete()
end

---@public
function DialogueParticipantManager:SpawnAllParticipant()
    self.participantConfigs = {}

    for _, performerConfig in ksbcpairs(self.dialogueConfig.PerformerList) do
        self.participantTotalNum = self.participantTotalNum + 1
        self.participantConfigs[performerConfig.TrackName] = performerConfig
    end

    for _, cameraConfig in ksbcpairs(self.dialogueConfig.CameraList) do
        self.participantTotalNum = self.participantTotalNum + 1
        self.participantConfigs[cameraConfig.TrackName] = cameraConfig
    end

    for _, routePointConfig in ksbcpairs(self.dialogueConfig.RoutePointList) do
        self.participantTotalNum = self.participantTotalNum + 1
        self.participantConfigs[routePointConfig.TrackName] = routePointConfig
    end

    for _, otherConfig in ksbcpairs(self.dialogueConfig.NewEntityList) do
        self.participantTotalNum = self.participantTotalNum + 1
        self.participantConfigs[otherConfig.TrackName] = otherConfig
    end

    -- 没有参与者
    if self.participantTotalNum == 0 then
        Log.DebugFormat("[SpawnAllParticipant] no participant found in %s", self.dialogueID)
        self.dialogueInstance:OnAllParticipantReady()
        return
    end

    self.participantReadyNum = 0
    for _, config in pairs(self.participantConfigs) do
        self:spawnSingleParticipant(config)
    end
end

---@private
function DialogueParticipantManager:spawnSingleParticipant(ptpConfig)
    -- todo:编辑态同名加检查
    --if self.participants[config.TrackName] then
    --    Log.WarningFormat("[spawnSingleParticipant] %s has duplicate participant in %s", config.TrackName, self.dialogueInstance.dialogueID)
    --    return
    --end

    local bSimpleDialogue = self.dialogueInstance.bSimpleDialogue

    local newParticipant = DialogueParticipant.new(self, ptpConfig)
    self.participants[ptpConfig.TrackName] = newParticipant

    local spawnTransform = self:getParticipantSpawnTransform(ptpConfig, self.playParams.SpawnTransform)
    if ptpConfig.UseSceneActor then
        newParticipant:BindDialogueEntity(spawnTransform, bSimpleDialogue)
        if ptpConfig.bIsPlayer then
            self.bUseRealMainPlayer = true
        end
    else
        newParticipant:SpawnDialogueEntity(spawnTransform)
		Log.DebugFormat("[DialogueParticipantManager:spawnSingleParticipant] entity: %s, trackName:%s ", newParticipant, ptpConfig.TrackName)
    end
end

---@public
function DialogueParticipantManager:OnParticipantReady()
    self.participantReadyNum = self.participantReadyNum + 1
    if self.participantTotalNum == self.participantReadyNum then
        -- 全员就绪,处理挂接
        self:handleAllPtpAttachRelation()
        self.dialogueInstance:OnAllParticipantReady()
    end
end

---处理所有参与者和参与者之间的挂接关系
---@private
function DialogueParticipantManager:handleAllPtpAttachRelation()
    local attachRule = EAttachmentRule.KeepWorld
    local ptpEntity = nil
    local parentEntity = nil
    local attachCompID = 0
    local attachSocket = ""
    for _, ptp in pairs(self.participants) do
        ptpEntity = ptp:GetDialogueLocalEntity()
        if not ptpEntity then
            goto continue
        end

        parentEntity = self:GetParticipantEntityByName(ptp.ptpConfig.Parent)
        if not parentEntity then
            goto continue
        end

        -- 感觉为空也可以支持,但之前的逻辑不支持,先照搬了过来
        attachSocket = ptp.ptpConfig.FollowParentSocket
        if string.isEmpty(attachSocket) then
            goto continue
        end

        attachCompID = parentEntity.CppEntity:KAPI_Actor_FxGetComponentBySocket(attachSocket)
        if attachCompID == 0 then
            Log.WarningFormat("[handleAllPtpAttachRelation] find socket %s component on %s failed", attachSocket, parentEntity.ptpConfig.TrackName)
            goto continue
        end

        ptpEntity.CppEntity:KAPI_Actor_AttachToComponent(attachCompID, attachSocket, attachRule, attachRule, attachRule, true)
        :: continue ::
    end
end

---@public
function DialogueParticipantManager:SetParticipantDefaultVisibility()
    for _, participant in pairs(self.participants) do
        participant:SetDefaultVisibility()
    end
end

---@public
function DialogueParticipantManager:HideAllParticipant()
    local ptpEntity
    for _, participant in pairs(self.participants) do
        ptpEntity = participant:GetDialogueLocalEntity()
        if ptpEntity then
            ptpEntity:ForceToInvisibleByDialogue()
        end
    end
end

---@public
function DialogueParticipantManager:DestroyAllParticipant()
    for _, participant in pairs(self.participants) do
        if participant.ptpConfig.UseSceneActor then
            participant:UnbindDialogueEntity()
        else
            participant:DestroyDialogueEntity()
        end
    end
    table.clear(self.participants)
end

---@public
---@param name string
---@return DialogueParticipant|nil
function DialogueParticipantManager:GetParticipantByName(name)
    return self.participants[name]
end

---根据名称获取参与者Entity
---@public
---@param name string
---@return DialoguePerformer|DialogueCamera|DialogueNiagara|DialogueModel|DialogueLight
function DialogueParticipantManager:GetParticipantEntityByName(name)
    local ptp = self.participants[name]
    if ptp then
        return ptp:GetDialogueLocalEntity()
    end
end

---获取演员外观ID,直接从表里获取
---@public
---@param performerName
---@return number
function DialogueParticipantManager:GetPerformerAppearanceID(performerName)
    local appearanceID = 0

    -- 先从表格里取
    local dialogueAssetData = Game.TableData.GetDialogueAssetDataRow(self.dialogueID)
    for _, performerInfo in ksbcpairs(dialogueAssetData.Actors) do
        if performerInfo.tag == performerName then
            appearanceID = performerInfo.npcID
            break
        end
    end

    if appearanceID ~= 0 then
        return appearanceID
    end

    -- 再从参与者里面取
    local ptp = self:GetParticipantByName(performerName)
    return ptp and ptp.ptpConfig.AppearanceID or 0
end

---@public
---@param ptpName string
---@param bExclude boolean
function DialogueParticipantManager:SetPtpExcludeFromPostProcess(ptpName, bExclude)
    local ptpEntity = self:GetParticipantEntityByName(ptpName)
    if not ptpEntity then
        return
    end

    local meshCompIDs = ptpEntity.CppEntity:KAPI_Actor_K2_GetComponentsByClass(UMeshComponent)
    for _, meshCompID in pairs(meshCompIDs) do
        -- todo:这里cpp接口把number转成了boolean,应该直接传对应类型
        ptpEntity.CppEntity:KAPI_PrimitiveID_SetCustomDepthAndStencilValue(meshCompID, bExclude and 1 or 0, 10)
    end
end

local __UnitTransform__ = FTransform(FQuat(0, 0, 0, 1), FVector(0, 0, 0), FVector(1, 1, 1)) -- luacheck: ignore
local __OneVector__ = FVector(1, 1, 1) -- luacheck: ignore
local __ZeroVector__ = FVector(0, 0, 0) -- luacheck: ignore

---@private
function DialogueParticipantManager:getParticipantSpawnTransform(ptpConfig, spawnTransform)
    if not ptpConfig then
        return __UnitTransform__
    end

    local ptpParentConfig = self:getParticipantParentConfig(ptpConfig)
    if ptpParentConfig then
        local ptpSpawnTransform = DialogueUtilV2.DialogueTransformToUETransform(ptpConfig.SpawnTransform)
        local parentPtpSpawnTransform = self:getParticipantSpawnTransform(ptpParentConfig, spawnTransform)
        return ptpSpawnTransform * parentPtpSpawnTransform
    end

    -- 已经找不到父级,如果传入了额外的spawnTransform,则使用
    if spawnTransform then
        return spawnTransform
    end

    -- 如果外界没有指定位置,但使用了绝对位置的情况下(ZERO),使用自身的SpawnTransform
    if self.dialogueConfig.AnchorType == DialogueConst.ANCHOR_TYPE.ABSOLUTE then
        return DialogueUtilV2.DialogueTransformToUETransform(ptpConfig.SpawnTransform)
    end

    -- 未使用绝对坐标,根据锚点类型取位置
    return self:GetAnchorTransform()
end

---@private
function DialogueParticipantManager:getParticipantParentConfig(ptpConfig)
    if (ptpConfig == nil) or (string.isEmpty(ptpConfig.Parent) == true) then
        return
    end

    return self.participantConfigs[ptpConfig.Parent]
end

---@public
function DialogueParticipantManager:GetAnchorTransform()
    if not self.dialogueConfig then
        return __UnitTransform__
    end

    local position, rotation

    if (self.dialogueConfig.AnchorType == DialogueConst.ANCHOR_TYPE.NPC_SPAWNER)
            or (self.dialogueConfig.AnchorType == DialogueConst.ANCHOR_TYPE.TRIGGER)
            or (self.dialogueConfig.AnchorType == DialogueConst.ANCHOR_TYPE.INTERACTOR)
    then
        -- 以场景物为锚点,从场景侧取数据
        if _G.StoryEditor then
            position, rotation = Game.WorldDataManager:Editor_GetSceneActorRootPos(self.dialogueConfig.AnchorID)
        else
            position, rotation = Game.WorldDataManager:GetSceneActorRootPos(self.dialogueConfig.AnchorID)
        end

    elseif self.dialogueConfig.AnchorType == DialogueConst.ANCHOR_TYPE.PLAYER then
        -- 以玩家为锚点,取玩家坐标
        if Game.me then
            position, rotation = Game.me:GetPosition(), Game.me:GetRotation()
        end
    end

    if (position ~= nil) and (rotation ~= nil) then
        return FTransform(rotation:ToQuat(), position, __OneVector__)
    else
        return __UnitTransform__
    end
end

DialogueParticipantManager.__AnchorPtpName__ = "锚点" -- luacheck: ignore

-- todo: 有点暴力,先这么用
---获取锚点参与者单位的位置
---@public
---@return PVector3
function DialogueParticipantManager:GetAnchorPtpLocation()
    local anchorPtp = self.participants[self.__AnchorPtpName__]
    local anchorPtpEntity = anchorPtp and anchorPtp:GetDialogueLocalEntity() or nil
    if not anchorPtpEntity then
        return __ZeroVector__
    end

    return anchorPtpEntity:GetPosition()
end

---更新当句的说话者,处理AutoLookAt的逻辑
---@public
---@param talkerName string
function DialogueParticipantManager:SetCurDialogueTalker(talkerName)
    self.curTalker = self:GetParticipantEntityByName(talkerName)

    if self.bEnableAllPerformerLookAtTalker then
        self:handleAllPerformerLookAtTalker()
		self:handleTalkerLookAtTarget()
    end
end

--- 更新下一个台本的说话者，用于更新LookAt信息
function DialogueParticipantManager:SetCurDialogueNextTalker(talkerName)
	self.nextTalker = self:GetParticipantEntityByName(talkerName)
end

---控制全体LookAt的开关
---@public
---@param bEnable boolean
function DialogueParticipantManager:SetEnableAllPtpLookAtTalker(bEnable)
    if self.bEnableAllPerformerLookAtTalker == bEnable then
        return
    end

    Log.DebugFormat("[SetEnableAllPtpLookAtTalker] bEnable=%s", bEnable)
    self.bEnableAllPerformerLookAtTalker = bEnable
    self:handleAllPerformerLookAtTalker()
end

---@public
---@param bEnable boolean
---@param targetName string
function DialogueParticipantManager:SetEnableTalkerLookAtTarget(targetName)
    self.talkerLookAtTarget = self:GetParticipantEntityByName(targetName)
    self:handleTalkerLookAtTarget()
end

---说话人看向目标,说话人切换时也会调用
---@private
function DialogueParticipantManager:handleTalkerLookAtTarget()
    local talkerLookAtTarget = self.talkerLookAtTarget
    local targetCharacterID = talkerLookAtTarget and talkerLookAtTarget.CharacterID or 0
    local curTalker = self.curTalker
    if curTalker then
        if targetCharacterID ~= 0 then
            self:LookAtEntity(curTalker, talkerLookAtTarget, 0, DialogueConst.HEAD_SOCKET_NAME, true)
		else
			if self.nextTalker and self.nextTalker ~= curTalker then
				self:LookAtEntity(curTalker, self.nextTalker, 0, DialogueConst.HEAD_SOCKET_NAME, true)
			end
        end
    end
end

---其他演员看向说话人,说话人切换时也会调用
---@private
function DialogueParticipantManager:handleAllPerformerLookAtTalker()
    local targetCharacterID = self.curTalker and self.curTalker.CharacterID or 0
    local socket = DialogueConst.HEAD_SOCKET_NAME
    local participantTypePerformer = DialogueConst.PARTICIPANT_TYPE.PERFORMER
    local curTalker = self.curTalker

    for _, participant in pairs(self.participants) do
        if participant.PtpType ~= participantTypePerformer then
            goto continue
        end

        local ptpEntity = participant:GetDialogueLocalEntity()
        if not ptpEntity then
            goto continue
        end


		if ptpEntity == self.curTalker then
			self:UnlookAt(ptpEntity)
			goto continue
		end

        -- 如果没找到目标,也需要取消Gaze
        if (self.bEnableAllPerformerLookAtTalker == true) and (targetCharacterID ~= 0) then
            if self:canApplyAutoLookAt(ptpEntity) then
                self:LookAtEntity(ptpEntity, curTalker, 0, socket, true)
            end
        else
            self:UnlookAt(ptpEntity)
        end

        :: continue ::
    end
end

---Section是瞬时的,如果有Delay的LookAt需要通过ptpManager管理
---@public
---@param lookerEntity DialoguePerformer
---@param targetEntity DialoguePerformer
---@param delay number
---@param lookAtSocket string @ lookAt的位置
---@param bAutoLookAt boolean @ 是否为自动LookAt
function DialogueParticipantManager:AddDelayLookAtTask(lookerEntity, targetEntity, delay, lookAtSocket, bAutoLookAt)
    if (delay == nil) or (delay <= 0) then
        Log.WarningFormat("[AddDelayLookAtTask] invalid delay %s", delay)
        return
    end

    local lookerEntityUID = lookerEntity and lookerEntity:uid() or 0
    local targetEntityUID = targetEntity and targetEntity:uid() or 0

    if (lookerEntityUID == 0) or (targetEntityUID == 0) then
        Log.WarningFormat("[AddDelayLookAtTask] invalid entity looker=%s, target=%s", lookerEntityUID, targetEntityUID)
        return
    end

    local timer = Game.TimerManager:CreateTimerAndStart(function()
        self:realDelayLookAt(lookerEntityUID, targetEntityUID, lookAtSocket, bAutoLookAt)
    end, delay * 1000, 1)

    table.insert(self.delayLookAtTimerList, timer)
end

---@private
---@param lookerEntityUID number
---@param targetEntityUID number
function DialogueParticipantManager:realDelayLookAt(lookerEntityUID, targetEntityUID, lookAtSocket, bAutoLookAt)
    local lookerEntity = Game.EntityManager:GetEntityByIntID(lookerEntityUID)
    local targetEntity = Game.EntityManager:GetEntityByIntID(targetEntityUID)
    if (lookerEntity == nil) or (targetEntity == nil) then
        Log.WarningFormat("[realDelayLookAt] entity already destroyed looker=%s, target=%s", lookerEntityUID, targetEntityUID)
        return
    end

    self:LookAtEntity(lookerEntity, targetEntity, 0, lookAtSocket, bAutoLookAt)
end

---@public
---@param lookerEntity string
---@param targetEntity string
---@param delay number
---@param lookAtSocket string
---@param bAutoLookAt boolean @ 是否为自动LookAt
function DialogueParticipantManager:LookAt(lookerName, targetname, delay, lookAtSocket, bAutoLookAt)
    Log.DebugFormat("[LookAt] %s look at %s after %s seconds, autoLookAt:%s", lookerName, targetname, delay, bAutoLookAt)
    local lookerEntity = self:GetParticipantEntityByName(lookerName)
    local targetEntity = self:GetParticipantEntityByName(targetname)
    self:LookAtEntity(lookerEntity, targetEntity, delay, lookAtSocket, bAutoLookAt)
end

---@public
---@param lookerEntity DialoguePerformer|DialogueCamera|DialogueNiagara|DialogueModel|DialogueRoutePoint
---@param targetEntity DialoguePerformer|DialogueCamera|DialogueNiagara|DialogueModel|DialogueRoutePoint
---@param delay number
---@param lookAtSocket string @ lookAt的位置，默认为 "head"
---@param bAutoLookAt boolean @ 是否为自动LookAt
function DialogueParticipantManager:LookAtEntity(lookerEntity, targetEntity, delay, lookAtSocket, bAutoLookAt)
    if lookerEntity == nil or targetEntity == nil then
        return
    end

    delay = delay or 0
    local socket = lookAtSocket or DialogueConst.HEAD_SOCKET_NAME
    
    if delay > 0 then
        Log.DebugFormat("[LookAt] %s look at %s after %s seconds, autoLookAt:%s",
            lookerEntity:uid(), targetEntity:uid(), delay, bAutoLookAt)

        self:AddDelayLookAtTask(lookerEntity, targetEntity, delay, socket, bAutoLookAt)
    else
        Log.DebugFormat("[LookAt] %s look at %s, autoLookAt:%s", lookerEntity:uid(), targetEntity:uid(), bAutoLookAt)

        lookerEntity:StartGazeActor(Enum.EGazeTypeMap.DIALOGUE, targetEntity.CharacterID, socket)
    end

    self.lookAtLookUp[lookerEntity] = { targetEntity = targetEntity, socket = socket, bAutoLookAt = bAutoLookAt }
end

--- 解除LookAt
---@public
---@param lookerEntity DialoguePerformer|DialogueCamera|DialogueNiagara|DialogueModel|DialogueRoutePoint
function DialogueParticipantManager:UnlookAt(lookerEntity)
    if lookerEntity then
        self.lookAtLookUp[lookerEntity] = nil
        lookerEntity:UnGaze(Enum.EGazeTypeMap.DIALOGUE)
    end
end

--- 是否可以使用自动LookAt
--- 当entity已经被手动设置过lookAt目标时，不能使用自动lookAt，
--- 详情参见reminder：#157567 【剧情编辑器优化】LookAt功能优先级调整 
--- https://gamecloud-redmine.corp.kuaishou.com/1007/projects/c7/issues/157567
---@private
---@param lookerEntity DialoguePerformer|DialogueCamera|DialogueNiagara|DialogueModel|DialogueRoutePoint
---@return boolean
function DialogueParticipantManager:canApplyAutoLookAt(lookerEntity)
    local lookAtInfo = self.lookAtLookUp[lookerEntity]
    if lookAtInfo then
        return lookAtInfo.bAutoLookAt ~= false
    end

    return true
end

return DialogueParticipantManager
