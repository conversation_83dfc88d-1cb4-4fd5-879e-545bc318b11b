local GameEntranceManager = DefineClass("GameEntranceManager")
local EntranceEventSystem = require("Gameplay.Launch.EntranceEventSystem")
local LaunchConfig = require("Gameplay.Launch.LaunchConfig")

local UAkGameplayStatics = import("AkGameplayStatics")
local UC7FunctionLibrary = import("C7FunctionLibrary")
local UIFunctionLibrary = import("UIFunctionLibrary")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")
local UKismetSystemLibrary = import("KismetSystemLibrary")
local USubsystemBlueprintLibrary = import("SubsystemBlueprintLibrary")
local UAllInManager = import("AllInManager")
local UGameplayStatics = import("GameplayStatics")
local UKismetTextLibrary = import("KismetTextLibrary")
local pakUpdateSubSystem = USubsystemBlueprintLibrary.GetEngineSubsystem(import("PakUpdateSubsystem"))

_G.UE_EDITOR = UC7FunctionLibrary.IsC7Editor()
_G.SHIPPING_MODE = UC7FunctionLibrary.IsBuildShipping()
_G.GAME_RUNTIME = true

GameEntranceManager.PlayEntranceAudioKey = "PlayEntranceAudio"

GameEntranceManager.NecessaryScripts = {
    "Framework.Utils.LuaFunctionLibrary",
    "Framework.Utils.StringExtend",
    "Framework.C7Common.C7Log",
    "Gameplay.GameInit.Switch"
}

--游戏启动是阶段划分
GameEntranceManager.EGameFlowStageType = {
    SplashScreen = 0,
    HotPatch = 1,
	RestartSetting = 2,
    GamePlayLoop = 3,
}

--事件类型
GameEntranceManager.EventType = {
    ShowPopup = "ShowPopup",
    OpenMap = "OpenMap",
    AllInSdkLogin = "AllInSdkLogin",
    SkipAllInSdk = "SkipAllInSdk",
    CanStartLogin = "CanStartLogin",
    AppUpgrade = "AppUpgrade"
}

function GameEntranceManager:ctor(GI)
    self.gameStageMap = {}
    self.curGameStage = nil
    self.SdkData = {}
    self.stringTableID = LaunchConfig["Update_RichText"]
    self.EntranceEventSystem = EntranceEventSystem
    self:startSGameDebugger()
    self:RequireNecessaryScript()
    self:checkCommandLine()
    GI:SetMapReadyCallback(function(_, mapName)
        self:OnMapLoaded(_, mapName)
    end)
end

function GameEntranceManager:RequireNecessaryScript()
    for _, v in pairs(GameEntranceManager.NecessaryScripts) do
        package.loaded[v] = nil
        require(v)
    end
end

---GameEntrance 游戏逻辑启动入口
function GameEntranceManager:GameEntrance()
    ReleaseLog("[GameEntranceManager-LifeTimeStage] GameEntrance")
    -- Launcher 静默模式启动 ,目前只对windows 启动
    local platformName = UGameplayStatics.GetPlatformName()
    if( 'Windows' == platformName and self.LauncherSilenceMode ) then  -- and 
		ReleaseLog("[GameEntranceManager-LifeTimeStage] GameEntrance with LauncherSilenceMode")
        self:RegisterGameFlowStage(self.EGameFlowStageType.HotPatch, "Gameplay.Launch.HotPatch.HotPatchLauncherSilentStage", false)
        -- 开启阶段
        self:SwitchGameStage(self.EGameFlowStageType.HotPatch)
    else
        self:initDollbyAtmos()
        self:RegisterGameFlowStage(self.EGameFlowStageType.SplashScreen, "Gameplay.Launch.SplashScreen.SplashScreenStage", true)
        self:RegisterGameFlowStage(self.EGameFlowStageType.HotPatch, "Gameplay.Launch.HotPatch.HotPatchStage", false)
        self:RegisterGameFlowStage(self.EGameFlowStageType.RestartSetting, "Gameplay.Launch.RestartSetting.RestartSettingStage", false)
        self:RegisterGameFlowStage(self.EGameFlowStageType.GamePlayLoop, "Gameplay.Launch.GamePlayLoop.GamePlayLoopStage", false)
        self:SwitchGameStage(self.EGameFlowStageType.SplashScreen)

        -- PC包适配: ApplicationScale调整为0.8，编辑器环境下根据DPI PREVIEW_PLATFORM决定是否要缩放，如果Platform == PC,缩放，否则不缩放
        if 'Windows' == platformName then
            if _G.UE_EDITOR then
                if UIFunctionLibrary.GetPreviewPlatform and UIFunctionLibrary.GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC then
                    UC7FunctionLibrary.SetApplicationScale(0.85)
                else
                    UC7FunctionLibrary.SetApplicationScale(1.00)
                end
            else
                UC7FunctionLibrary.SetApplicationScale(0.85)
            end
        end
    end
end

function GameEntranceManager:FlowDownStage()
    local nextStage = self.curGameStage + 1
    self:SwitchGameStage(nextStage)
end

function GameEntranceManager:SwitchGameStage(stage)
    local curStageIns = self:getGameStage(self.curGameStage)
    if curStageIns then
        xpcall(curStageIns.LeaveStage, _G.CallBackError, curStageIns)
    end
    curStageIns = self:getGameStage(stage)
    if curStageIns then
        self.curGameStage = stage
        xpcall(curStageIns.EnterStage, _G.CallBackError, curStageIns)
    end
end

function GameEntranceManager:getGameStage(stageType)
    return self.gameStageMap[stageType]
end

function GameEntranceManager:RegisterGameFlowStage(stageType, luaPath, allowSkip, ...)
    local stageClass = require(luaPath)
    if stageClass == nil then
        DebugLogError(string.format("RegisterGameStage StageClass Error, StageClass Not Found . StageType:%s",
                tostring(stageType)))
        return
    end
    local stageIns = stageClass.new(self, allowSkip, ...)
    if stageIns == nil then
        DebugLogError(string.format("RegisterGameStage StageIns Error. StageType:%s", tostring(stageType)))
        return
    end
    self.gameStageMap[stageType] = stageIns
end

function GameEntranceManager:OnMapLoaded(_, mapName)
	ReleaseLogFormat("[GameEntranceManager-LifeTimeStage]:OnMapLoaded %s", mapName)
    EntranceEventSystem.FireEvent(self.EventType.OpenMap, mapName)
    if string.endsWith(mapName, "LV_Startup") then
        self:GameEntrance()
    end
end

function GameEntranceManager:FixHotPatch()
    if pakUpdateSubSystem:IsPakDisperse() then
        pakUpdateSubSystem:RepairDLC()
        UC7FunctionLibrary.QuitC7Game(_G.GetContextObject())
    end
end

function GameEntranceManager:GetNeedInitAllinSdk()
    return not import("C7FunctionLibrary").IsC7Editor() or self:GetNeedSdk()
end

function GameEntranceManager:GetNeedSdk()
    if _G.SkipAllInSdk or not UAllInManager.IsAllInSDKEnabled() then
        return false
    elseif UGameplayStatics.DoesSaveGameExist("AllInSdkSave", 0) then
        local saveObj = UGameplayStatics.LoadGameFromSlot("AllInSdkSave", 0)
        if not saveObj then
            return not UC7FunctionLibrary.IsC7Editor()
        end
        return saveObj.SdkLogin
    else
        return not UC7FunctionLibrary.IsC7Editor()
    end
end

function GameEntranceManager:SetNeedSdk(value)
    local sdkGameSlot = "AllInSdkSave"
    local saveObj
    if UGameplayStatics.DoesSaveGameExist(sdkGameSlot, 0) then
        saveObj = UGameplayStatics.LoadGameFromSlot(sdkGameSlot, 0)
    end
    if not saveObj then
        saveObj = UGameplayStatics.CreateSaveGameObject(slua.loadClass(LaunchConfig["AllInSdk_Data"]))
    end
    saveObj.SdkLogin = value
    UGameplayStatics.SaveGameToSlot(saveObj, sdkGameSlot, 0)
end

function GameEntranceManager:GetGameEntranceSetting(key, defaultValue)
	if UGameplayStatics.DoesSaveGameExist("GameEntranceSetting", 0) then
		local saveObj = UGameplayStatics.LoadGameFromSlot("GameEntranceSetting", 0)
		if not saveObj or saveObj[key] == nil then
			return defaultValue
		end
		return saveObj[key]
	else
		return defaultValue
	end
end

function GameEntranceManager:SetGameEntranceSetting(key, value)
	local gameEntranceSettingSlot = "GameEntranceSetting"
	local saveObj
	if UGameplayStatics.DoesSaveGameExist(gameEntranceSettingSlot, 0) then
		saveObj = UGameplayStatics.LoadGameFromSlot(gameEntranceSettingSlot, 0)
	end
	if not saveObj then
		saveObj = UGameplayStatics.CreateSaveGameObject(slua.loadClass(LaunchConfig["GameEntrance_Setting"]))
	end
	saveObj[key] = value
	UGameplayStatics.SaveGameToSlot(saveObj, gameEntranceSettingSlot, 0)
end

---CheckCommandLine 检查启动项参数，是否需要跳过sdk登录
function GameEntranceManager:checkCommandLine()
    local commandLine = UKismetSystemLibrary.GetCommandLine()
    if string.contains(commandLine, "SkipAllInSdk", true) then
        _G.SkipAllInSdk = true
    end

    -- Launcher 静默方式启动下载资源
    self.LauncherSilenceMode = false
    if  string.contains(commandLine, "nosound", true)  and  string.contains(commandLine, "nullrhi", true) and string.contains(commandLine, "Launcher", true)  then
        self.LauncherSilenceMode = true
        ReleaseLogFormat("[GameEntranceManager-LifeTimeStage] [checkCommandLine]  self.LauncherSilenceMode  : %s" , self.LauncherSilenceMode)
    end

    -- if string.contains(commandLine, "UIAutomationProfile", true) then
    --     _G.bUIAutomationProfile = true
    --     _G.InvalidationBox = false --临时跳过
    -- end
end

-- todo 这里播放时机比manager初始化还要早 暂时还无法使用MediaManager的视频播放接口, 目前先继续使用当前接口
---PlayEntranceVideo 在进入Patch场景或者重新回到登录界面时从头播放入场视频
function GameEntranceManager:PlayEntranceVideo()
    if not self.entranceVideo and self:GetNeedSdk() then
        self.entranceVideo = slua.loadObject(LaunchConfig["Update_BinkPlayer"])
        self.entranceVideo:AddToRoot()
        local onceVideo = "./Movies/login05_once.bk2"
        local loopVideo = "./Movies/login05_loop.bk2"
        self.entranceVideo:OpenUrl(onceVideo)
        self.entranceVideo.OnMediaReachedEnd:Add(function()
            self.entranceVideo.OnMediaReachedEnd:Clear()
            self.entranceVideo:OpenUrl(loopVideo)
            self.entranceVideo:SetLooping(true)
            self.entranceVideo:Play()
        end)
        self.entranceVideo:Play()
    end
end

---StopEntranceVideo 关闭登录界面时调用，停止播放入场视频
function GameEntranceManager:StopEntranceVideo()
    if self.entranceVideo then
        self.entranceVideo:Stop()
        self.entranceVideo.OnMediaReachedEnd:Clear()
        self.entranceVideo:RemoveFromRoot()
        self.entranceVideo = nil
    end
end

--function GameEntranceManager:PlayEntranceVideo()
--    self.mediaPlayID = Game.MediaManager:PlayBinkMedia(ViewResourceConst.MEDIA_PLAYER_PATH.GameEntranceMediaPath, "login05_once.bk2", nil, true, nil, self, "onMediaReachEnd")
--end
--function GameEntranceManager:onMediaReachEnd(playID)
--    Game.MediaManager:OpenUrl(self.mediaPlayID, "login05_loop.bk2")
--    Game.MediaManager:SetLooping(self.mediaPlayID, true)
--end
--function GameEntranceManager:StopEntranceVideo()
--	if self.mediaPlayID then
--        Game.MediaManager:StopBinkMedia(self.mediaPlayID)
--        self.mediaPlayID = nil
--	end
--end

function GameEntranceManager:startSGameDebugger()
    Game.SGameDebugger = require("Tools.SGameDebugger.SGameDebugger")
    Game.SGameDebugger:Start()
end

function GameEntranceManager:GetString(key)
    local result = UKismetTextLibrary.TextFromStringTable(self.stringTableID, key)
    if string.isEmpty(result) then
        return key
    end
    return result
end

-- 杜比全景声相关初始化,需要在游戏最开始时调用
---@private
function GameEntranceManager:initDollbyAtmos()
    -- todo@shijingzhe: 写死的字符串需要在某个地方进行配置
    UAkGameplayStatics.LoadBankByName("DolbyAtmos")
    local playingID = UAkGameplayStatics.PostEventAtLocation(nil, FVector(), FRotator(), "Play_DolbyAtmos_Silence", _G.GetContextObject()) -- luacheck: ignore

	if playingID ~= 0 then
		ReleaseLog("[GameEntranceManager-LifeTimeStage] [initDollbyAtmos] success")
    else
        ReleaseLogError("[GameEntranceManager-LifeTimeStage] [initDollbyAtmos] failed!!!")
    end

    if UAkGameplayStatics.IsDolbyAtmosSupported() then
		ReleaseLog("[GameEntranceManager-LifeTimeStage] [initDollbyAtmos] enable dolby atmos")
        UAkGameplayStatics.SetState(nil, "DolbyPlugin", "DolbyPlugin_On")
    else
		ReleaseLog("[GameEntranceManager-LifeTimeStage] [initDollbyAtmos] disable dolby atmos")
        UAkGameplayStatics.SetState(nil, "DolbyPlugin", "DolbyPlugin_Bypass")
    end
end

function GameEntranceManager:GameExit()
    -- PC包适配: ApplicationScale调整为1.0
    local platformName = UGameplayStatics.GetPlatformName()
    if 'Windows' == platformName then
        UC7FunctionLibrary.SetApplicationScale(1.0)
    end

    Game.SGameDebugger:Stop()
end

return GameEntranceManager
