local C7FunctionLibrary = import("C7FunctionLibrary")

local GameInstance = { }

require("Framework.DoraSDK.DefineClass")

local MaxUObjectCounts = import("LuaFunctionLibrary").GetEngineObjectArrayCapacity()

function GameInstance:OnLuaPatchBegin(InGI)
    --import("LuaFunctionLibrary").ChangeConsoleVariableOfInt("r.skeletonInstance.button", 0) -- Close Skeleton Instance templately on 2025/05/08 <EMAIL>

	-- luacheck: ignore
	print("[GameInstance-LifeTimeStage][GameInstance:OnLuaPatchBegin] InGI:", InGI) -- 还没有初始化log, --luacheck: ignore
    _G.IsClient = true
    Game = {}
    Enum = Enum or {} --定义一个全局Enum Table
    Game.GameInstance = self
    Game.GameEntranceManager = require("Gameplay.Launch.GameEntranceManager").new(self)
	ReleaseLog("[GameInstance-LifeTimeStage][GameInstance:OnLuaPatchBegin] End")
    _G.SetContextObject(InGI)
end

function GameInstance:OnLuaBegin(InGI)
	-- luacheck: ignore
	print("[GameInstance-LifeTimeStage][GameInstance:OnLuaBegin] InGI:", InGI)	-- 还没有初始化log, --luacheck: ignore
    if _G.GetContextObject():IsPIE() then
        --LuaPanda debugger
        --require("Tools.LuaPanda").start("127.0.0.1", 8818)
		--EmmyLua debugger
		package.cpath = package.cpath .. ';C:/Users/<USER>/AppData/Roaming/JetBrains/Rider2024.3/plugins/EmmyLua/debugger/emmy/windows/x64/?.dll'
		local dbg = require('emmy_core')
		dbg.tcpConnect('localhost', 9966)
	end
	Game.IsGameExiting = false
    require("Gameplay.GameInit.Main")
    Main(InGI)
    
    self.gcTime = 0
    Log.Info("GameInstance:OnBegin End")
end

function GameInstance:OnLuaEnd()
	Log.Info("[GameInstance-LifeTimeStage][GameInstance:OnLuaEnd]" )
	Game.IsGameExiting = true --退出逻辑有些骚操作需要用这个判断处理 <EMAIL>
    Game.GameEntranceManager:GameExit()
    Exit()
end

function GameInstance:SetMapReadyCallback(callback)
    self.mapReadyCallback = callback
end

function GameInstance:OnLuaMapLoaded(InMapName)
    if Game and Game.Logger then
		Log.InfoFormat("[GameInstance-LifeTimeStage][GameInstance:OnLuaMapLoaded] mapName:%s", InMapName )
    end
end

function GameInstance:OnLuaMapReady(World, InMapName)
    if Game and Game.Logger then
		Log.InfoFormat("[GameInstance-LifeTimeStage][GameInstance:OnLuaMapReady] World:%s  MapName:%s", World, InMapName )
    end
    if self.mapReadyCallback then
        self.mapReadyCallback(World, InMapName)
    end
end

function GameInstance:OnLuaLoadMapError(InErrorMsg)
    Log.ErrorFormat("[GameInstance-LifeTimeStage] [GameInstance:OnLuaMapReady] OnLoadMapError : %s", InErrorMsg)
	if Game.LevelManager then
		Game.LevelManager:OnMapLoadError(InErrorMsg)
	end
end

function GameInstance:OpenMap(InMapName)
	Log.InfoFormat("[GameInstance-LifeTimeStage] [GameInstance:OpenMap] InMapName : %s", InMapName)
	LuaGC() --过图前执行下LuaGC @hujianglong
    _G.GetContextObject():NativeOpenMap(InMapName)
end

function GameInstance:OnLuaWpCellLoaded(World, Added, X, Y, Lv, Bounds)
	Log.InfoFormat("[GameInstance-LifeTimeStage] [GameInstance:OnLuaWpCellLoaded] X: %s  Y:%s  Lv:%s", X, Y, Lv)
    Game.WorldManager:OnLuaWpCellLoaded(World, Added, X, Y, Lv, Bounds)
end

function GameInstance:OnLuaExecGM(Cmd)
    Game.GMManager.DispatchCommand(Cmd)
end

function GameInstance:OnLuaShowGM()
    Game.GMManager.ShowGM()
end

-- luacheck: push ignore
---gc 手动调用下GC,释放内存
local function gc(self)
    local t = _now()
    --超过60s才gc
    if t - self.gcTime > 60000 then
		Log.Info("[GameInstance-LifeTimeStage] [gc]")
        collectgarbage('collect')
        C7FunctionLibrary.GC()
        self.gcTime = t
    end
end
-- luacheck: pop

function GameInstance:OnLuaMemoryLowWarning()
	--这里报Error是为了在CrashInsight检测下MemoryLow的情况 <EMAIL>
    Log.Warning("[GameInstance-LifeTimeStage] GameInstance:OnMemoryLowWarning")
    Game:OnMemoryWarning()
    gc(self)
end 

function GameInstance:OnObjectCountNearlyExceed(currentObjectCount)
    Log.Info("[GameInstance-LifeTimeStage] GameInstance:OnObjectCountNearlyExceed")
    Game:OnObjectCountNearlyExceed(currentObjectCount)
    if currentObjectCount >= MaxUObjectCounts * 0.90 then
        self.gcTime = self.gcTime - 55000 --临时提高GC频率
    end
    gc(self)
end 

---ApplicationWillEnterBackground App进入后台事件（Android和iOS）
function GameInstance:ApplicationWillEnterBackground()
	Log.Info("[GameInstance-LifeTimeStage] GameInstance:ApplicationWillEnterBackground")
end

---ApplicationHasEnteredForeground App返回前台事件（Android和iOS）
function GameInstance:ApplicationHasEnteredForeground()
	Log.Info("[GameInstance-LifeTimeStage] GameInstance:ApplicationHasEnteredForeground")
end

---OnWindowMinimizeChanged Windows窗口焦点变更事件(编辑器下窗口为PIE)
---@param bDeactivate boolean 是否失去焦点
---@param bWindowMinimized boolean 是否被最小化
function GameInstance:OnWindowForceChanged(bDeactivate, bWindowMinimized)
	Log.Info("GameInstance:OnWindowForceChanged")
    Game.AkAudioManager:OnWindowFocusChanged(bDeactivate)
end

---OnWindowCloseRequested Windows窗口请求关闭事件
function GameInstance:OnWindowCloseRequested()
	if Game == nil then
		return true
	end
	if Game.MessageBoxSystem == nil then
		local EntranceEventSystem = require("Gameplay.Launch.EntranceEventSystem")
		EntranceEventSystem.FireEvent(Game.GameEntranceManager.EventType.ShowPopup,Game.GameEntranceManager:GetString("QUIT_GAME_CONFIRM_WINDOW"),Game.GameEntranceManager:GetString("QUIT_GAME_CONFIRM_WINDOW_HINT"),true,
			function()
				C7FunctionLibrary.QuitC7Game(_G.GetContextObject())
			end,true)
		return false
	end
	Game.MessageBoxSystem:InterruptPopupByKey(Enum.EDialogPopUpData.QUIT_GAME_CONFIRM_WINDOW)
	Game.MessageBoxSystem:AddPopupByConfig(
		Enum.EDialogPopUpData.QUIT_GAME_CONFIRM_WINDOW,
		function()
			C7FunctionLibrary.QuitC7Game(_G.GetContextObject())
		end,
		nil
	)
	return false
end

return Class(nil, nil, GameInstance)
