local EntranceBaseStage = require("Gameplay.Launch.EntranceBaseStage")
local WebHotfixStage = DefineClass("WebHotfixStage", EntranceBaseStage)

local UBlueprintPathsLibrary = import("BlueprintPathsLibrary")
local UKismetSystemLibrary = import("KismetSystemLibrary")
local ULuaFunctionLibrary = import("LuaFunctionLibrary")
local UGameplayStatics = import("GameplayStatics")

function WebHotfixStage:onCtor()
    self.webHotfixWorker = nil
    self.wenHotfixTimerHandler = nil
    self.timerCCB = nil
end

function WebHotfixStage:onEnterStage()
    local useWebHotfix = false
    if not _G.SHIPPING_MODE then
        local path = UGameplayStatics.GetPlatformName() ~= 'Windows' and UBlueprintPathsLibrary.ProjectPersistentDownloadDir() or UBlueprintPathsLibrary.ProjectSavedDir()
        local fullPath = ULuaFunctionLibrary.GetFilePath(path)
        local File = ULuaFunctionLibrary.LoadFile(fullPath.."/WebHotfixUrl.txt")
        if File ~= "" then
            local result = string.split(File,"\r\n")
            local webhotfix_configValue = result[1]
            local test_webhotfixValue = result[2] and result[2] or "1"
            if webhotfix_configValue then
                if  utf8.len(webhotfix_configValue) > 0 then
                    Game.GameEntranceManager.SdkData.KwaiGatewayZoneInfoTest = {extJson = {webhotfix_config = webhotfix_configValue,
                                                                                           skip_webhotfix = "0",test_webhotfix = test_webhotfixValue}}
                end
            end
        end
    end
    if Game.GameEntranceManager.SdkData then
        local info = Game.GameEntranceManager.SdkData.KwaiGatewayZoneInfoTest or  Game.GameEntranceManager.SdkData.KwaiGatewayZoneInfo
        if info and info.extJson and info.extJson.webhotfix_config then
			if info.extJson.skip_webhotfix then
				-- skip_webhotfix=1表示跳过，0表示不跳过
				useWebHotfix = info.extJson.skip_webhotfix == "0"
			else
				useWebHotfix = true
			end
        end
    end

    if useWebHotfix then
        self:initWebHotfix()
        local WHWorker = require "Gameplay.Launch.HotPatch.WebHotfix.WHWorker"
        ---@type WHWorker
        self.webHotfixWorker = WHWorker:new()
        self.webHotfixWorker:Run(function ()
            if self.isDone == true then
                DebugLogError("WebHotfixStage Is Finished")
                return
            end
            self.owner:FlowDownStage()
        end)
    else
		DebugLog("web hotfix skip!!!")
        self.owner:FlowDownStage()
    end
end

function WebHotfixStage:initWebHotfix()
    self.timerCCB = slua.createDelegate(function() self:onWHTimer() end)
    slua.addRef(self.timerCCB)
    self.wenHotfixTimerHandler = UKismetSystemLibrary.K2_SetTimerDelegate(self.timerCCB, 0.1, true, false, 0, 0)
end

function WebHotfixStage:clearWebHotfix()
    if self.wenHotfixTimerHandler then
        UKismetSystemLibrary.K2_ClearTimerHandle(_G.GetContextObject(), self.wenHotfixTimerHandler)
        slua.removeRef(self.timerCCB)
    end
    DebugLog("webhotfix end work")
end

function WebHotfixStage:onWHTimer()
    if self.webHotfixWorker then
        self.webHotfixWorker:Tick()
    end
end

function WebHotfixStage:onLeaveStage()
    if _OnLuaStarted_ then  --webhotfix流程完毕，执行_OnLuaStarted_(webhotfix)插装
        _OnLuaStarted_()
    end
    self:clearWebHotfix()
end

return WebHotfixStage