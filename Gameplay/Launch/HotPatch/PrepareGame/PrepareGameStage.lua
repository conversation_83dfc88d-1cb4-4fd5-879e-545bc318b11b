local EntranceBaseStage = require("Gameplay.Launch.EntranceBaseStage")
local PrepareGameStage = DefineClass("PrepareGameStage", EntranceBaseStage)

local UGameplayStatics = import("GameplayStatics")
local EntranceEventSystem = require("Gameplay.Launch.EntranceEventSystem")

function PrepareGameStage:onCtor()
    self.sdkLoginSuccessAction = function()
        self:sdkLoginSuccess()
    end
    self.sdkLoginFailAction = function(error)
        self:sdkLoginFailed(error)
    end
    self.sdkGetServerListSuccessAction = function()
		Game.AllInSdkManager:StopGetServerInfo()
        self:initEnd()
    end
    self.sdkGetServerListFailAction = function(error)
        self:sdkGetServerListFailed(error)
    end
end

function PrepareGameStage:onEnterStage()
    EntranceEventSystem.RegEvent(Game.GameEntranceManager.EventType.AllInSdkLogin,self,self.login)
    EntranceEventSystem.RegEvent(Game.GameEntranceManager.EventType.SkipAllInSdk,self,self.skipAllInSdk)
    Game.PrepareGameAndroidBack:UnInit()
    Game.EntranceAllInSdkManager:UnInit()
    Game.GameEntranceManager:RequireNecessaryScript()
    
    local GI = UGameplayStatics.GetGameInstance(_G.GetContextObject())
    GI:HotPatchFlowEnd(0)
    EntranceEventSystem.FireEvent(Game.GameEntranceManager.EventType.CanStartLogin, true)
    if Game.AllInSdkManager == nil then
        return --Game.AllInSdkManager == nil 说明前面初始化流程有报错导致中断了，直接return吧，免得这里又报错
    end
    Game.AllInSdkManager.SdkData.KwaiGatewayZoneInfo = Game.GameEntranceManager.SdkData.KwaiGatewayZoneInfo

    if _OnAllManagerInited_ then  --GamePlay初始化完成，执行_OnAllManagerInited_(webhotfix)插装
        _OnAllManagerInited_()
    end

    self:login()
end

function PrepareGameStage:login()
    if Game.AllInSdkManager.GetNeedSdk() then
        Game.AllInSdkManager:Login(self.sdkLoginSuccessAction, self.sdkLoginFailAction)
    else
        self:initEnd()
    end
end

function PrepareGameStage:skipAllInSdk()
    if Game.AllInSdkManager then
        Game.AllInSdkManager:UnInit()
    end
    Game.AllInSdkManager = require("Framework.AllInSDK.AllInSdkManagerBase").new()
    self:initEnd()
end

function PrepareGameStage:sdkLoginSuccess()
    Game.AllInSdkManager:GetServerList(self.sdkGetServerListSuccessAction,self.sdkGetServerListFailAction)
end

function PrepareGameStage:sdkLoginFailed(error)
    if error.code ~= Game.AllInSdkManager.ErrorCode.LOGIN_CANCEL_BY_USER then
        DebugLogWarning(string.format("AllInSdk Login Failed,ErrorCode :%d, ErrorMessage :%s", error.code, error.msg))
        EntranceEventSystem.FireEvent(Game.GameEntranceManager.EventType.ShowPopup, Game.GameEntranceManager:GetString("WARNING"), Game.GameEntranceManager:GetString("LOGIN_FAILED"),
                nil, true, nil)
    end
end

function PrepareGameStage:sdkGetServerListFailed(error)
    DebugLogWarning(string.format("AllInSdk GetServerList Failed,ErrorCode :%d, ErrorMessage :%s", error.code, error.msg))
    EntranceEventSystem.FireEvent(Game.GameEntranceManager.EventType.ShowPopup, Game.GameEntranceManager:GetString("WARNING"), Game.GameEntranceManager:GetString("GET_SERVER_INFO_FAILED"),
            nil, function()
                self:sdkLoginSuccess()
            end, nil)
end

function PrepareGameStage:initEnd()
    if self.isDone == true then
        DebugLogError("PrepareGameStage Is Finished")
        return
    end
    self.owner:FlowDownStage()
end

function PrepareGameStage:onLeaveStage()
    EntranceEventSystem.UnRegAllEvent(Game.GameEntranceManager.EventType.AllInSdkLogin,self)
    EntranceEventSystem.UnRegAllEvent(Game.GameEntranceManager.EventType.SkipAllInSdk,self)
end

return PrepareGameStage