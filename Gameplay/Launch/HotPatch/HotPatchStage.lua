local EntranceBaseStage = require("Gameplay.Launch.EntranceBaseStage")
local HotPatchStage = DefineClass("HotPatchStage", EntranceBaseStage)
local LaunchConfig = require("Gameplay.Launch.LaunchConfig")
local UGameplayStatics = import("GameplayStatics")
local EntranceEventSystem = require("Gameplay.Launch.EntranceEventSystem")
local AllInSdkInitStage = require("Gameplay.Launch.HotPatch.AllInSdkInit.AllInSdkInitStage")
local WebHotfixStage = require("Gameplay.Launch.HotPatch.WebHotfix.WebHotfixStage")
local AppUpgradeStage = require("Gameplay.Launch.HotPatch.AppUpgrade.AppUpgradeStage")
local PrepareGameStage = require("Gameplay.Launch.HotPatch.PrepareGame.PrepareGameStage")
local PakUpdateStage = require("Gameplay.Launch.HotPatch.UpdatePatch.PakUpdateStage")
local pakUpdateSubSystem = import("SubsystemBlueprintLibrary").GetEngineSubsystem(import("PakUpdateSubsystem"))

function HotPatchStage:onCtor()
    self.curStageIndex = nil
    self.childStageInsList = nil
    self.hotPatchUI = nil
    self.hotPatchPopupUI = nil
end

function HotPatchStage:onEnterStage()
    local needSdk = Game.GameEntranceManager:GetNeedInitAllinSdk()
    Game.EntranceAllInSdkManager = needSdk and require("Gameplay.Launch.HotPatch.EntranceAllInSdkManager").new() or
        require("Gameplay.Launch.HotPatch.EntranceAllInSdkManagerBase").new()
    Game.PrepareGameAndroidBack = require("Gameplay.Launch.HotPatch.PrepareGame.PrepareGameAndroidBack").new()
    self.childStageInsList = {}
    self:addStage(AllInSdkInitStage.new(self))
    self:addStage(WebHotfixStage.new(self))
    self:addStage(AppUpgradeStage.new(self))
    if pakUpdateSubSystem:IsPakDisperse() then
        DebugLog("[HotPatchStage]: Use Disperse Modle")
        self:addStage(PakUpdateStage.new(self))
    end
    self:addStage(PrepareGameStage.new(self))

    EntranceEventSystem.RegEvent(Game.GameEntranceManager.EventType.ShowPopup, self, self.showPopup)
    EntranceEventSystem.RegEvent(Game.GameEntranceManager.EventType.OpenMap, self, self.onOpenMap)
    local GI = UGameplayStatics.GetGameInstance(_G.GetContextObject())
    if UGameplayStatics.GetCurrentLevelName(slua.getWorld()) ~= "LV_HotPatch" then
        GI:BPOPENMAP(LaunchConfig["LV_HotPatch"])
    else
        self:onOpenMap("LV_HotPatch")
    end
end

function HotPatchStage:addStage(stageIns)
    self.childStageInsList[#self.childStageInsList + 1] = stageIns
end

function HotPatchStage:onOpenMap(mapName)
    if string.endsWith(mapName, "LV_HotPatch") then
        local playerController = UGameplayStatics.GetPlayerController(_G.GetContextObject(), 0)
        playerController.bShowMouseCursor = true
        self.hotPatchUI = require("Gameplay.Launch.HotPatch.P_HotPatch").new()
        self.hotPatchUI:ShowUI()
        self.hotPatchPopupUI = require("Gameplay.Launch.HotPatch.P_HotPatchPopup").new()
        self.hotPatchPopupUI:ShowUI()
        self.curStageIndex = 1
        self.childStageInsList[self.curStageIndex]:EnterStage()
    end
end

function HotPatchStage:FlowDownStage()
    self.childStageInsList[self.curStageIndex]:LeaveStage()
    self.curStageIndex = self.curStageIndex + 1
    if self.curStageIndex > #self.childStageInsList then
        self.owner:FlowDownStage()
    else
        self.childStageInsList[self.curStageIndex]:EnterStage()
    end
end

function HotPatchStage:showPopup(title, content, cancelAction, confirmAction, closeAction)
    if self.hotPatchPopupUI then
        self.hotPatchPopupUI:ShowPopup(title, content, cancelAction, confirmAction, closeAction)
    end
end

function HotPatchStage:onLeaveStage()
    self.childStageInsList = nil
    if self.hotPatchUI then
        self.hotPatchUI:DestroyUI()
    end
    if self.hotPatchPopupUI then
        self.hotPatchPopupUI:DestroyUI()
    end
    if not Game.GameEntranceManager:GetNeedSdk() then
        Game.GameEntranceManager:StopEntranceVideo()
    end
    EntranceEventSystem.UnRegAllEvent(Game.GameEntranceManager.EventType.ShowPopup, self)
    EntranceEventSystem.UnRegAllEvent(Game.GameEntranceManager.EventType.OpenMap, self)
end

return HotPatchStage
