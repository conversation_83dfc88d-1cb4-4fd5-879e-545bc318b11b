local EntranceBaseStage = require("Gameplay.Launch.EntranceBaseStage")
local AllInSdkInitStage = DefineClass("AllInSdkInitStage", EntranceBaseStage)

local EntranceEventSystem = require("Gameplay.Launch.EntranceEventSystem") 
local pakUpdateSubSystem = import("SubsystemBlueprintLibrary").GetEngineSubsystem(import("PakUpdateSubsystem"))

function AllInSdkInitStage:onCtor()
    self.allInSdk = nil
    self.getGameZoneFailedWrap = function(code,msg) self:getGameZoneFailed(code,msg) end
    self.getGameZoneSuccessWrap = function(kwaiGatewayZoneInfo) self:getGameZoneSuccess(kwaiGatewayZoneInfo) end
end

function AllInSdkInitStage:onEnterStage()
    if Game.GameEntranceManager:GetNeedInitAllinSdk() then
        if pakUpdateSubSystem:GetNetWorkState() == 2 then      --2为飞行模式
            local retry = function() self:onEnterStage() end  -- luacheck: ignore
            EntranceEventSystem.FireEvent(Game.GameEntranceManager.EventType.ShowPopup,Game.GameEntranceManager:GetString("NETWORK_EXCEPTION"), Game.GameEntranceManager:GetString("CONNECT_FAILED_CHECK_NET"),
                    nil, retry, retry)
            return
        end
        Game.EntranceAllInSdkManager:InitSdk(function(error) self:initSdkFailed(error) end,function(result) self:initSdkSuccess(result) end)
    else
        self.owner:FlowDownStage()
    end
end

function AllInSdkInitStage:initSdkFailed(error)
    self:callSdkFunctionFailedShowPopup("InitSdk", Game.GameEntranceManager:GetString("SDK_INIT_FAILED"),error.code,error.msg,function()
        import("C7FunctionLibrary").QuitC7Game(_G.GetContextObject())
    end)
end

function AllInSdkInitStage:initSdkSuccess()
    Game.EntranceAllInSdkManager:Track("game_privacy_agree", {result = "1", errorMsg = ""},0)
    self:getGameZone()
end

function AllInSdkInitStage:getGameZone()
    Game.EntranceAllInSdkManager:GetGameZone(self.getGameZoneFailedWrap ,self.getGameZoneSuccessWrap)
end

function AllInSdkInitStage:getGameZoneFailed(code,msg)
    self:callSdkFunctionFailedShowPopup("GetGameZone", Game.GameEntranceManager:GetString("GET_SERVER_INFO_FAILED"),code,msg,function() self:getGameZone() end)
end

function AllInSdkInitStage:getGameZoneSuccess(kwaiGatewayZoneInfo)
    DebugLog("AllInSdkInit GetGameZone Success")
    Game.GameEntranceManager.SdkData.KwaiGatewayZoneInfo = kwaiGatewayZoneInfo
    if self.isDone == true then
        DebugLogError("AllInSdkInitStage Is Finished")
        return
    end
    self.owner:FlowDownStage()
end

function AllInSdkInitStage:callSdkFunctionFailedShowPopup(functionName,content,code,msg,callback)
    DebugLogWarning(string.format("AllInSdk %s Failed,ErrorCode :%d, ErrorMessage :%s",functionName,code,msg))
    EntranceEventSystem.FireEvent(Game.GameEntranceManager.EventType.ShowPopup,Game.GameEntranceManager:GetString("WARNING"),content,nil,function()
        if callback then callback() end
    end,nil)
end

function AllInSdkInitStage:onLeaveStage()
end

return AllInSdkInitStage