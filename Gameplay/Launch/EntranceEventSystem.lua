---@class EntranceEventSystem
local EntranceEventSystem = DefineClass("EntranceEventSystem")
EntranceEventSystem.eventList = {}
EntranceEventSystem.uniqueId = 0

function EntranceEventSystem.MakeUniqueId()
    EntranceEventSystem.uniqueId = EntranceEventSystem.uniqueId + 1
    return EntranceEventSystem.uniqueId
end

function EntranceEventSystem.GetFuncNameInner(t, obj, func)

    if t[obj] then
        return
    end

    t[obj] = true

    -- todo. 改成只向父类查找
    for key, _ in pairs(obj) do
        t.cnt = t.cnt + 1
        -- 最多100次
        if t.cnt > 200 then
            return nil
        end
        if obj[key] == func then
            return key
        elseif type(obj[key]) == "table" and key == "class" then
            local r = EntranceEventSystem.GetFuncNameInner(t, obj[key], func)
            if r ~= nil then
                return r
            end
        end
        --DebugLog("key=", key)
    end
    return nil
end
function EntranceEventSystem.GetFuncName(obj, func)
    local t = {}
    t.cnt = 0
    return EntranceEventSystem.GetFuncNameInner(t, obj, func)
end

function EntranceEventSystem.RegEvent(evtName, obj, func)
    
    if evtName == nil or obj == nil or func == nil then
        return
    end
    
    if EntranceEventSystem.eventList[evtName] == nil then
        EntranceEventSystem.eventList[evtName] = {}
    end

    local funcName = EntranceEventSystem.GetFuncName(obj, func)

    if funcName == nil then
        error("EntranceEventSystem. can't find lua function!")
    end

    table.insert(EntranceEventSystem.eventList[evtName],
    {
        Obj = obj,
        FuncName = funcName,
    })
end

function EntranceEventSystem.UnRegEvent(evtName, obj, func)
    local t = EntranceEventSystem.eventList[evtName]
    if t == nil then
        return
    end

    local funcName = EntranceEventSystem.GetFuncName(obj, func)

    local removed = {}
    for i, v in pairs(t) do
        if v.Obj == obj and v.FuncName == funcName then
            v.Obj = nil
            v.FuncName = nil
            table.insert(removed, i)
        end
    end

    while #removed > 0 do 
        local idx = removed[#removed]
        table.remove(t, idx)

        table.remove(removed, #removed)
    end    
end

function EntranceEventSystem.UnRegAllEvent(evtName, obj)
    local t = EntranceEventSystem.eventList[evtName]
    if t == nil then
        return
    end

    local removed = {}
    for i, v in pairs(t) do
        if v.Obj == obj then
            v.Obj = nil
            v.FuncName = nil
            table.insert(removed, i)
        end
    end

    while #removed > 0 do 
        local idx = removed[#removed]
        table.remove(t, idx)

        table.remove(removed, #removed)
    end    
end

function EntranceEventSystem.FireEvent(evtName, evtId, ...)
    DebugLog("FireEvent:", evtName, evtId)
    local t = EntranceEventSystem.eventList[evtName]
    if t == nil then
		DebugLog("FireEvent, no eventList:", evtName, evtId)
        return
    end

    for _, v in pairs(t) do
        if v.Obj and v.Obj[v.FuncName] then
            v.Obj[v.FuncName](v.Obj, evtId, ...)
        end
    end
end

function EntranceEventSystem.Dump()
    for k, v in pairs(EntranceEventSystem.eventList) do
		DebugLog("evtName=", k, "count=", #v)
    end
end

return EntranceEventSystem

