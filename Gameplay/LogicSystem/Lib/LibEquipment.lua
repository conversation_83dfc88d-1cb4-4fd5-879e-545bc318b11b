local EquipTagText_Item = kg_require("Gameplay.LogicSystem.Equip.EquipTagText_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

local ESlateVisibility = import("ESlateVisibility")

---@class LibEquipment_Param
---@field RtbKeyText string rtbKey
---@field RtbValueText string rtbValue
---@field RtbMaxText string 
---@field EquipTagShow boolean

---@class LibEquipment : UIComponent
---@field view LibEquipmentBlueprint
local LibEquipment = DefineClass("LibEquipment", UIComponent)

LibEquipment.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function LibEquipment:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function LibEquipment:InitUIData()
end

--- UI组件初始化，此处为自动生成
function LibEquipment:InitUIComponent()
    ---@type EquipTagText_Item
    self.EquipTag_luaCom = self:CreateComponent(self.view.EquipTag_lua, EquipTagText_Item)
end

---UI事件在这里注册，此处为自动生成
function LibEquipment:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function LibEquipment:InitUIView()
end

---组件刷新统一入口
---@param param LibEquipment_Param
function LibEquipment:Refresh(param)
	
	self:SetWidgetText(self.view.RTB_Key_lua, param.RtbKeyText)
	self:SetWidgetText(self.view.RTB_Val_lua, param.RtbValueText)
	self:SetWidgetText(self.view.RTB_ValMax_lua, param.RtbMaxText)
end

---@private 设置widget的文本，并且会设置显影
function LibEquipment:SetWidgetText(widget, text)
	local show = text ~= nil
	widget:SetVisibility(show and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	if show then
		widget:SetText(text)
	end
end

return LibEquipment
