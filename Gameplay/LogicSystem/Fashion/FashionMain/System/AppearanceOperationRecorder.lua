local LinkedList = require("Framework.Library.LinkedList")

local OperationRecorder = DefineClass("OperationRecorder")

function OperationRecorder:ctor(maxNumber)
	self.MaxRecordNumber = maxNumber or 10			--最大值
	self.CurrentIndex = 0							--当前索引
	self.linkedList = LinkedList.new()				--双向链表
	self:InitLinkedList()
end

---@private method
function OperationRecorder:InitLinkedList(info,new,old)
	self:addToLinkedNodeList(self:BuildDataTable(info,new or -1,old or -1))
end

function OperationRecorder:dtor()
	self.MaxRecordNumber = nil
	self.CurrentIndex = nil
	self.linkedList = nil
end

---@public method
function OperationRecorder:BuildDataTable(info, new, old)
	local dataTable = {}
	dataTable.info = info
	dataTable.new = new
	dataTable.old = old
	return dataTable
end

---@public method
function OperationRecorder:RecordData(info, new, old)
	local dataTable = self:BuildDataTable(info, new, old)
	local length = self.linkedList:GetLength()
	if not(length == 0 or (self.CurrentIndex ~= nil and self.CurrentIndex == self.linkedList.tailNode.Key)) then
		while self.linkedList:GetLength() > 1 do
			if self.CurrentIndex == self.linkedList.tailNode.Key then
				break
			end
			self.linkedList:RemoveTail()
		end
	end
	self:addToLinkedNodeList(dataTable)
end

---@public method
function OperationRecorder:Undo()
	if self:CanUndo() then
		local curNode = self.linkedList.keyValues[self.CurrentIndex]
		if curNode then
			self.CurrentIndex = curNode.Prev.Key
			return true, curNode.Value
		end
	end
	return false
end

---@public method
function OperationRecorder:Redo()
	if self:CanRedo() then
		local curNode = self.linkedList.keyValues[self.CurrentIndex]
		if curNode then
			self.CurrentIndex = curNode.Next.Key
			return true, curNode.Next.Value
		end
	end
	return false
end

function OperationRecorder:CanUndo()
	if self.linkedList:GetLength() <= 1 then
		return false
	end
	return self.linkedList.keyValues[self.CurrentIndex].Prev ~= nil
end

function OperationRecorder:CanRedo()
	if self.linkedList:GetLength() <= 1 then
		return false
	end
	return self.linkedList.keyValues[self.CurrentIndex].Next ~= nil
end

---@public method
function OperationRecorder:Reset()
	if self:CanReset() then
		while self.linkedList:GetLength() > 1 do
			self.linkedList:RemoveTail()
		end
		return true, self:GetInitData()
	end
	return false
end

--考虑MaxRecordNumber的add
function OperationRecorder:addToLinkedNodeList(dataTable)
	self.CurrentIndex = self.CurrentIndex + 1
	self.linkedList:InsertNode(self.CurrentIndex, dataTable)
	
	if self.linkedList:GetLength() > self.MaxRecordNumber+1 then
		self.linkedList:RemoveNodeByPos(2)
	end
end

--region 查询接口
function OperationRecorder:GetCurrentOperationData()
	return self.linkedList:GetValueByKey(self.CurrentIndex)
end

function OperationRecorder:CanReset()
	return self.linkedList:GetLength() > 1
end

function OperationRecorder:GetInitData()
	return self.linkedList:GetHeadNodeValue()
end
--endregion

return OperationRecorder