local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local const = kg_require("Shared.Const")
---@class FashionPresetsLeft_Item : UIListItem
---@field view FashionPresetsLeft_ItemBlueprint
local FashionPresetsLeft_Item = DefineClass("FashionPresetsLeft_Item", UIListItem)

FashionPresetsLeft_Item.eventBindMap = {
	[EEventTypesV2.CHAT_CUSTOM_EMO_UPDATE] = "ResUrlUpdate",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionPresetsLeft_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionPresetsLeft_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionPresetsLeft_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function FashionPresetsLeft_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionPresetsLeft_Item:InitUIView()
end

---面板打开的时候触发
function FashionPresetsLeft_Item:OnRefresh(data)
	self.data = data

	--默认
	if self.index == 1 then
		self:RefreshPreset_Default()
		return
	end

	if data and next(data) then
		self:RefreshPreset_NotEmpty()
	else
		self:RefreshPreset_Empty()
	end
end

function FashionPresetsLeft_Item:RefreshPreset_Default()
	self.userWidget:SetNone(false)
	self.userWidget:SetWearing(false)
	self.userWidget:BP_SetVefiry(false)
	self.userWidget:BP_SetVefiryFail(false)

	local iconPath = Game.TableData.GetAppearanceSystemConstDataRow("DEFAULT_PRESET_ICON")
	self:SetImage(self.view.Img_Dress, iconPath)
	self.view.Text_Title:SetText(StringConst.Get(string.format("FASHION_PRESETS%s", self.index)))
end

function FashionPresetsLeft_Item:RefreshPreset_NotEmpty()
	self.userWidget:BP_SetVefiry(true)
	self.userWidget:BP_SetVefiryFail(false)
	self.userWidget:SetNone(false)
	local curIndex = Game.FashionSystem:GetCurPresetIndex()
	curIndex = curIndex and curIndex+1
	self.userWidget:SetWearing(self.index == curIndex)
	
	self.view.Text_Title:SetText(StringConst.Get(string.format("FASHION_PRESETS%s", self.index)))


	self.PictureID = self.data.PictureID
	if not string.isEmpty(self.PictureID) then
		local resInfo = Game.LocalResSystem:GetResInfoByResID(self.PictureID)
		if resInfo then
			self:RefreshIconByResInfo(resInfo)
		else
			Game.LocalResSystem:ReqResUrls({self.PictureID})
		end
	else
		Log.ErrorFormat("Fashion: RefreshPreset_NotEmpty error, PictureID = %s", self.PictureID)
	end
end

function FashionPresetsLeft_Item:RefreshPreset_Empty()
	self.userWidget:SetNone(true)
	self.userWidget:SetWearing(false)
	self.userWidget:BP_SetVefiry(false)
	self.userWidget:BP_SetVefiryFail(false)
	
	self.view.Text_Title:SetText(StringConst.Get(string.format("FASHION_PRESETS%s", self.index)))
end

function FashionPresetsLeft_Item:ResUrlUpdate(resdata)
	local key, resInfo = next(resdata)
	if self.PictureID ~= key then
		return
	end

	self:RefreshIconByResInfo(resInfo)
end

function FashionPresetsLeft_Item:RefreshIconByResInfo(resInfo)
	--审核中，非审核中
	if resInfo.status == const.CHAT_CUSTOM_IMG_STATUS.APPROVED then
		local url = Game.LocalResSystem:GetResUrlInfoByResID(self.PictureID)
		if url then
			self:SetImageByUrl(self.view.Img_Dress, url, self.PictureID)
		else
			Log.ErrorFormat("RefreshIconByResInfo error, id = %s", self.PictureID)
		end
		self.userWidget:BP_SetVefiry(false)
		self.userWidget:BP_SetVefiryFail(false)
	elseif resInfo.status == const.CHAT_CUSTOM_IMG_STATUS.REJECTED then
		--显示不通过
		self.userWidget:BP_SetVefiry(false)
		self.userWidget:BP_SetVefiryFail(true)
	else
		--显示审核中
		self.userWidget:BP_SetVefiry(true)
		self.userWidget:BP_SetVefiryFail(false)
	end
end

return FashionPresetsLeft_Item
