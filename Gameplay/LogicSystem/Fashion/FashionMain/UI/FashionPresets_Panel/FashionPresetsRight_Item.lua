local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class FashionPresetsRight_Item : UIListItem
---@field view FashionPresetsRight_ItemBlueprint
local FashionPresetsRight_Item = DefineClass("FashionPresetsRight_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

FashionPresetsRight_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionPresetsRight_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionPresetsRight_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionPresetsRight_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function FashionPresetsRight_Item:InitUIEvent()
	self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionPresetsRight_Item:InitUIView()
end

---面板打开的时候触发
function FashionPresetsRight_Item:OnRefresh(data)
	self.view.Img_Text:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Text_Name:SetVisibility(ESlateVisibility.Collapsed)
	
	self.fashionID = data.fashionID
	local fashionConfig = Game.TableData.GetFashionDataRow(self.fashionID)
	
	if not self.fashionID then
		self.userWidget:SetState(0)
		local subTypeConfig = Game.TableData.GetAppearanceSubTypeRow(data.subType)
		self:SetImage(self.view.icon_center, subTypeConfig.Icon)
		return
	end

	self.userWidget:SetState(2)
	self:SetImage(self.view.Icon, fashionConfig.Icon)

	--品质
	self.userWidget:SetRank(fashionConfig.quality)
end

--- 显示tips
--- 此处为自动生成
function FashionPresetsRight_Item:on_Btn_ClickArea_Clicked()
	if not self.fashionID then
		return
	end
	local fashionConfig = Game.TableData.GetFashionDataRow(self.fashionID)
	Game.NewUIManager:OpenPanel('FashionTips_Panel', self.fashionID, fashionConfig.SubType, 0)
end


return FashionPresetsRight_Item
