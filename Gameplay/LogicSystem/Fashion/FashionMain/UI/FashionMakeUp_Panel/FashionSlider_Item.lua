local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class FashionSlider_Item : UIListItem
---@field view FashionSlider_ItemBlueprint
local FashionSlider_Item = DefineClass("FashionSlider_Item", UIListItem)

FashionSlider_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionSlider_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionSlider_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionSlider_Item:InitUIComponent()
    ---@type UIComNumberSlider
    self.WBP_CustomRoleSliderCom = self:CreateComponent(self.view.WBP_CustomRoleSlider, UIComNumberSlider)
end

---UI事件在这里注册，此处为自动生成
function FashionSlider_Item:InitUIEvent()
    self:AddUIEvent(self.WBP_CustomRoleSliderCom.onValueChange, "on_WBP_CustomRoleSliderCom_ValueChange")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionSlider_Item:InitUIView()
end

---面板打开的时候触发
function FashionSlider_Item:OnRefresh(data)
	self.SliderSocket  = data.SliderSocket
	self.SliderName    = data.SliderName
	self.SliderDefault = data.SliderDefault
	self.SliderMin     = data.SliderMin
	self.SliderMax     = data.SliderMax


	self.view.Text_Left:SetText(self.SliderName)
	self.view.Text_Right:SetText(self.SliderDefault)
	self.WBP_CustomRoleSliderCom:Refresh(self.SliderDefault, self.SliderMin, self.SliderMax, 1)
	
	--修改fashion的颜色
end


--- 此处为自动生成
---@param value number
function FashionSlider_Item:on_WBP_CustomRoleSliderCom_ValueChange(value)
	self.view.Text_Right:SetText(value)
end



return FashionSlider_Item
