local FashionBase_Item = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.MountWidget.FashionBase_Item")

---@class FashionVehicle_Item : FashionBase_Item
---@field view FashionVehicle_ItemBlueprint
local FashionVehicle_Item = DefineClass("FashionVehicle_Item", FashionBase_Item)

FashionVehicle_Item.eventBindMap = {
}

---初始化数据
function FashionVehicle_Item:InitUIData()
	FashionBase_Item.InitUIData(self)

	---@type FashionItemParam
	self.ItemParam = nil
end

---面板打开的时候触发
---@param params FashionItemParam
function FashionVehicle_Item:OnRefresh(params)
	self.ItemParam = params
	local mountID = params.ID

	table.clear(self.BaseParam)

	local mountData = Game.TableData.GetFashionMountDataRow(mountID)

	-- 仅当未获得时，获取商店价格
	local uiStatus = Game.MountSystem:GetFashionMountUIStatus(mountID)
	if uiStatus == Enum.EFashionMountUIStatus.NotGet then
		local tokenId, price = Game.MountSystem:GetFashionMountShopExpend(mountID)
		self.BaseParam.TokenID = tokenId
		self.BaseParam.Price = price
	end

	self.BaseParam.ID = mountID
	self.BaseParam.UIStatus = uiStatus
	self.BaseParam.IsWearing = Game.me.curMount == mountID
	self.BaseParam.Name = mountData.MountName
	self.BaseParam.Icon = mountData.icon
	self.BaseParam.Quality = mountData.quality
	self.BaseParam.AchieveType = mountData.AchieveType
	
	self:RefreshCommonItem(self.BaseParam)
end

--- 收藏
--- 此处为自动生成
function FashionVehicle_Item:on_Btn_Collect_Clicked()
	local mountID = self.ItemParam.ID
	if Game.MountSystem:IsFashionMountCollected(mountID) then
		Game.MountSystem.sender:ReqCancelCollectFashionMount(mountID)
	else
		Game.MountSystem.sender:ReqCollectFashionMount(mountID)
	end
end

--- 此处为自动生成
---@param checked bool
function FashionVehicle_Item:on_WBP_ComCheckBoxCom_CheckChanged(checked)

end

return FashionVehicle_Item
