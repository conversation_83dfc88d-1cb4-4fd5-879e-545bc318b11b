local FashionClassBase_Item = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.MountWidget.FashionClassBase_Item")

---@class FashionVehicleClass_Item : FashionClassBase_Item
---@field view FashionClass_ItemBlueprint
local FashionVehicleClass_Item = DefineClass("FashionVehicleClass_Item", FashionClassBase_Item)

FashionVehicleClass_Item.eventBindMap = {
}

---@param subType number
function FashionVehicleClass_Item:OnRefresh(subType)
	self.BaseParam.subType = subType

	-- 后面这块应该可以改改
	local fashionMountId = Game.MountSystem:GetCurFashionMountBySubType(subType)
	if fashionMountId and fashionMountId > 0 then
		local fashionMountData = Game.TableData.GetFashionMountDataRow(fashionMountId)
		self.BaseParam.icon = fashionMountData.icon
		self.BaseParam.quality = fashionMountData.quality
		self.BaseParam.wear = true
	else
		self.BaseParam.wear = false
	end

	--self.BaseParam.hasNew = Game.MountSystem:HasNewFashionMount(subType)

	self:RefreshData(self.BaseParam)
end

return FashionVehicleClass_Item
