---@class FashionFunctionR_CornerVehicleImpl : IFashionFunctionR_CornerImpl
---@field view FashionFunctionR_CornerBlueprint
local FashionFunctionR_CornerVehicleImpl = DefineClass("FashionFunctionR_CornerVehicleImpl")

---@public 刷新
---@param uiComponent FashionFunctionR_CornerBase
---@param mountID int
function FashionFunctionR_CornerVehicleImpl:Refresh(uiComponent, mountID)
	if uiComponent.param then
		table.clear(uiComponent.param)
	else
		uiComponent.param = {}
	end
	
	self.param = uiComponent.param

	if mountID then
		local subType = Enum.EAppearanceSubType2ID.Mount
		local param = uiComponent.param
		local mountData = Game.TableData.GetFashionMountDataRow(mountID)
		param.id = mountID
		param.name = mountData.MountName
		param.subType = subType
		param.hasGot = Game.MountSystem:IsFashionMountUnlock(mountID)
		param.achieveDes = mountData.AchieveDes
		if not param.hasGot then
			local tokenID, price = Game.MountSystem:GetFashionMountShopExpend(mountID)
			param.price = price
			param.tokenId = tokenID
		end
	end

	uiComponent:RefreshImpl(uiComponent.param)
end

-- todo 弹出购买窗口
function FashionFunctionR_CornerVehicleImpl:EnterBuyMoneyPanel()

end

function FashionFunctionR_CornerVehicleImpl:EnterBuyFashion()
	Game.MountSystem.sender:ReqBuyMountComp(self.param.id)
end

return FashionFunctionR_CornerVehicleImpl
