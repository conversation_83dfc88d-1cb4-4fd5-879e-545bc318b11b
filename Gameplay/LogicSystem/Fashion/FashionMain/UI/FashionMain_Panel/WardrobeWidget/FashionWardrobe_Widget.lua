local FashionWardrobe_ItemL = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.WardrobeWidget.FashionWardrobe_ItemL")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class FashionWardrobe_Widget : UIComponent
---@field view FashionWardrobe_WidgetBlueprint
local FashionWardrobe_Widget = DefineClass("FashionWardrobe_Widget", UIComponent)

--衣柜界面
FashionWardrobe_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionWardrobe_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionWardrobe_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionWardrobe_Widget:InitUIComponent()
    ---@type FashionWardrobe_ItemL
    self.BikeItemCom = self:CreateComponent(self.view.BikeItem, FashionWardrobe_ItemL)
    ---@type FashionWardrobe_ItemL
    self.SuitItemCom = self:CreateComponent(self.view.SuitItem, FashionWardrobe_ItemL)
    ---@type UIListView childScript: FashionClass_Item
    self.ListClothCom = self:CreateComponent(self.view.ListCloth, UIListView)
end

---UI事件在这里注册，此处为自动生成
function FashionWardrobe_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionWardrobe_Widget:InitUIView()
end

---组件刷新统一入口
function FashionWardrobe_Widget:Refresh(...)
	local entity = Game.FashionSystem:GetCurFashionEntity()
	local suitID = entity:GetFashionBySubType(Enum.EAppearanceSubType2ID.Suit)
	self.SuitItemCom:Refresh({subType = Enum.EAppearanceSubType2ID.Suit, fashionID = suitID})
	Game.RedDotSystem:RegisterRedDot("FashionRightSubTab", Enum.EAppearanceSubType2ID.Suit)
	self.BikeItemCom:Refresh({subType = Enum.EAppearanceSubType2ID.Mount, mountID = Game.me.curMount})
	Game.RedDotSystem:RegisterRedDot("FashionRightSubTab", Enum.EAppearanceType2ID.Mount)

	local showSubTypeList = {}
	for i, config in ksbcpairs(Game.TableData.GetAppearanceSubTypeTable()) do
		if config.MainType == Enum.EAppearanceType2ID.Fashion or config.MainType == Enum.EAppearanceType2ID.Accessory then
			if config.ID ~= Enum.EAppearanceSubType2ID.Suit then
				table.insert(showSubTypeList, {
					subType = config.ID,
					fashionID = entity:GetFashionBySubType(config.ID),
				})
				Game.RedDotSystem:RegisterRedDot("FashionRightSubTab", config.ID)
			end
		end
	end
	self.ListClothCom:Refresh(showSubTypeList)
end

return FashionWardrobe_Widget
