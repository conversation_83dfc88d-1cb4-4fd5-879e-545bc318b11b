local FashionWardrobe_ItemL = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.WardrobeWidget.FashionWardrobe_ItemL")
---@class FashionWardrobe_ItemS : UIListItem
---@field view FashionWardrobe_ItemSBlueprint
local FashionWardrobe_ItemS = DefineClass("FashionWardrobe_ItemS", FashionWardrobe_ItemL)

FashionWardrobe_ItemS.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionWardrobe_ItemS:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionWardrobe_ItemS:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionWardrobe_ItemS:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function FashionWardrobe_ItemS:InitUIEvent()
	FashionWardrobe_ItemL.InitUIEvent(self)
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionWardrobe_ItemS:InitUIView()
end

---面板打开的时候触发
function FashionWardrobe_ItemS:OnRefresh(data)
	FashionWardrobe_ItemL.OnRefresh(self, data)
end

return FashionWardrobe_ItemS
