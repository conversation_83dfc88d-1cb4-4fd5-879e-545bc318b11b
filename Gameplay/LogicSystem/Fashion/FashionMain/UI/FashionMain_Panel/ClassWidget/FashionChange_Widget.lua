local FashionFunctionL_ButtonAll = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.ClassWidget.FashionFunctionL_ButtonAll")
local FashionFunctionL_Button = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.ClassWidget.FashionFunctionL_Button")
local FashionAchieve_Widget = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.AchieveWidget.FashionAchieve_Widget")
local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIComTextSearchBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComTextSearchBox")
local FashionFunctionR_Widget = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.ClassWidget.FashionFunctionR_Widget")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESelectionMode = import("ESelectionMode")
local ESlateVisibility = import("ESlateVisibility")

---@class FashionChange_Widget : UIComponent
local FashionChange_Widget = DefineClass("FashionChange_Widget", UIComponent)

FashionChange_Widget.eventBindMap = {
	[EEventTypesV2.ON_FASHION_UI_SINGLE_UPDATE] = "RefreshSingleFashion",
	[EEventTypesV2.ON_FASHION_WEAR_UPDATE] = "RefreshWearFashion",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionChange_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionChange_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionChange_Widget:InitUIComponent()
    ---@type FashionFunctionL_Button
    self.WBP_ShowBtnCom = self:CreateComponent(self.view.WBP_ShowBtn, FashionFunctionL_Button)
    ---@type FashionFunctionL_ButtonAll
    self.WBP_FashionFunctionL_ButtonAllCom = self:CreateComponent(self.view.WBP_FashionFunctionL_ButtonAll, FashionFunctionL_ButtonAll)
    ---@type UIComCheckBox
    self.WBP_ComCheckBoxCom = self:CreateComponent(self.view.WBP_ComCheckBox, UIComCheckBox)
    ---@type UIComTextSearchBox
    self.WBP_ComInputCom = self:CreateComponent(self.view.WBP_ComInput, UIComTextSearchBox)
    ---@type FashionAchieve_Widget
    self.WBP_FashionAchieveCom = self:CreateComponent(self.view.WBP_FashionAchieve, FashionAchieve_Widget)
    ---@type FashionFunctionR_Widget
    self.WBP_FashionFunctionR_PageCom = self:CreateComponent(self.view.WBP_FashionFunctionR_Page, FashionFunctionR_Widget)
    ---@type UIListView
    self.KGSubTypeListCom = self:CreateComponent(self.view.KGSubTypeList, UIListView)
    ---@type UIListView
    self.KGItemListCom = self:CreateComponent(self.view.KGItemList, UIListView)
	self.KGItemListCom:SetSelectionMode(ESelectionMode.SingleToggle)
end

---UI事件在这里注册，此处为自动生成
function FashionChange_Widget:InitUIEvent()
    self:AddUIEvent(self.KGItemListCom.onItemSelected, "on_KGItemListCom_ItemSelected")
    self:AddUIEvent(self.KGItemListCom.onItemClicked, "on_KGItemListCom_ItemClicked")
    self:AddUIEvent(self.KGItemListCom.onItemSelectionChanged, "on_KGItemListCom_ItemSelectionChanged")
    self:AddUIEvent(self.KGSubTypeListCom.onItemSelected, "on_KGSubTypeListCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComCheckBoxCom.onCheckChanged, "on_WBP_ComCheckBoxCom_CheckChanged")
    self:AddUIEvent(self.WBP_ComInputCom.onTextChanged, "on_WBP_ComInputCom_TextChanged")
    self:AddUIEvent(self.WBP_ShowBtnCom.onClickEvent, "on_WBP_ShowBtnCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionChange_Widget:InitUIView()
end

---组件刷新统一入口
function FashionChange_Widget:Refresh(defaultSubType)
	self.sex = Game.me.Sex
	
	--全部类型的二级页签数据
	self.allSubTypeDataList = {}

	--全部类型的item数据
	self.allItemList = {}
	--当前类型的二级页签数据
	self.curItemList = nil
	
	self.curSelectIndex = nil
	
	self.curFilterText = ""
	self.curCheckBoxState = false
	self.clickItemList = nil

	--选中即穿戴
	self.selectAndWear = true
	
	self.firstShowItem = true
	self.firstShowSubType = true

	self:RefreshUIHide(false)
	
	self.WBP_ComCheckBoxCom:Refresh(StringConst.Get("FASHION_NOT_HAVE"), false, false)
	self.WBP_FashionFunctionL_ButtonAllCom:Refresh()
	self.WBP_ShowBtnCom:Refresh()

	self:RefreshTypeData(defaultSubType)
	self:RefreshFashionData()
end

function FashionChange_Widget:OnClose()
end

function FashionChange_Widget:RefreshTypeData(defaultSubType)
	self.defaultSubType = defaultSubType or Enum.EAppearanceSubType2ID.Suit
	self.showMainTypeList = {
		[Enum.EAppearanceType2ID.Fashion] = Enum.EAppearanceType2ID.Fashion,
		[Enum.EAppearanceType2ID.Accessory] = Enum.EAppearanceType2ID.Accessory,
	}
end

--region 左侧列表
---刷新数据
function FashionChange_Widget:RefreshFashionData()
	self:RefreshSubType()
	self.WBP_FashionAchieveCom:Refresh()
end

---刷新左侧二级标签（一级选中切换时，触发）
function FashionChange_Widget:RefreshSubType()
	self:RefreshSubTypeDataList()

	self.KGSubTypeListCom:Refresh(self.allSubTypeDataList)
	local index = 1
	for i, data in pairs(self.allSubTypeDataList) do
		if data.subType == self.defaultSubType then
			index = i
			break
		end
	end
	self.KGSubTypeListCom:SetSelectedItemByIndex(index, true)

	if not self.firstShowSubType then
		self:PlayAnimation(self.userWidget.Ani_List_Switch_1)
	end
	self.firstShowSubType = false
end

---成功穿衣后刷新子类Icon
function FashionChange_Widget:RefreshSubTypeIcon()
	self.KGSubTypeListCom:RefreshItems()
end

--- 此处为自动生成
---@param index number
---@param data table
function FashionChange_Widget:on_KGSubTypeListCom_ItemSelected(index, data)
	self.curSelectSubIndex = index
	self.curSelectSubType = data.subType
	local subTypeConfig = Game.TableData.GetAppearanceSubTypeRow(self.curSelectSubType)
	self.curSelectMainType = subTypeConfig.MainType

	self.selectAndWear = subTypeConfig.MaxCount == 1

	self:RefreshRightAreaData(nil)
	self:RefreshItemList()
	
	self.WBP_FashionFunctionL_ButtonAllCom:RefreshMainTypeChange(self.curSelectMainType)
	
	Game.FashionSystem:ReqChangeAppearanceNewFlag(self.curSelectSubType)
end

---刷新item列表（二级选中切换时，触发）
function FashionChange_Widget:RefreshItemList()
	self.curSelectIndex = nil
	self.curItemList = self:GetItemDataList(self.curSelectMainType, self.curSelectSubType)

	self.KGItemListCom:Refresh(self.curItemList)
	--默认选中
	if self.curItemList and self.selectAndWear then
		for i, itemData in ipairs(self.curItemList) do
			local fashionID = itemData.configID
			if Game.FashionSystem:GetCurFashionEntity():IsWearFashion(fashionID) then
				self.KGItemListCom:SetSelectedItemByIndex(i, true)
				self.KGItemListCom:RefreshItemByIndex(i)
			end
		end
	end

	if not self.firstShowItem then
		self:PlayAnimation(self.userWidget.Ani_List_Switch)
	end
	self.firstShowItem = false
end

--- 更新Item选中
--- 此处为自动生成
---@param index number
---@param data table
function FashionChange_Widget:on_KGItemListCom_ItemSelected(index, data)
	Log.DebugFormat("Fashion: ItemSelect id = %s", data.configID)
end

--- 此处为自动生成
---@param index number
---@param selected bool
function FashionChange_Widget:on_KGItemListCom_ItemSelectionChanged(index, selected)
	self.curSelectIndex = index
	self:RefreshRightAreaData(selected and index)
end

--- 此处为自动生成
---@param index number
---@param data table
function FashionChange_Widget:on_KGItemListCom_ItemClicked(index, data)
	local entity = Game.FashionSystem:GetCurFashionEntity()
	if not entity.bInWorld then
		return
	end
	
	if self.selectAndWear then
		self.clickItemList = true
		--当前类型
		local fashionID = data.configID
		if entity:IsWearFashion(fashionID) then
			self:UnWearAppearance(fashionID)
		else
			self:WearAppearance(fashionID)
		end
	end
end

--- 此处为自动生成
---@param checked boolean
function FashionChange_Widget:on_WBP_ComCheckBoxCom_CheckChanged(checked)
	self.curCheckBoxState = checked
	self:RefreshItemList()
end

--- 此处为自动生成
---@param text string
function FashionChange_Widget:on_WBP_ComInputCom_TextChanged(text)
	self.curFilterText = text
	self:RefreshItemList()
end

---获取2级标签列表数据
function FashionChange_Widget:RefreshSubTypeDataList()
	table.clear(self.allSubTypeDataList)

	for _, subConfig in ksbcpairs(Game.TableData.GetAppearanceSubTypeTable()) do
		if self.showMainTypeList[subConfig.MainType] then
			if Game.me.Sex == Enum.ESex.MALE and
				(subConfig.ID == Enum.EAppearanceSubType2ID.Socks or subConfig.ID == Enum.EAppearanceSubType2ID.Shoes)
			then
				goto continue
			end
			table.insert(self.allSubTypeDataList, {
				id = subConfig.ID,
				name = subConfig.Name,
				icon = subConfig.Icon,
				subType = subConfig.ID,
				redPointId = "FashionMainTab",
				redPointSuff = subConfig.ID,
			})
			Game.RedDotSystem:RegisterRedDot("FashionLeftSubTab", subConfig.ID)
			::continue::
		end
	end

	table.sort(self.allSubTypeDataList, function(left, right)
		return left.id < right.id
	end)
end

---获取item列表数据
function FashionChange_Widget:GetItemDataList(mainType, subType)
	if not self.allItemList[mainType] then
		self.allItemList[mainType] = {}
		--根据类型获取数据
		if mainType == Enum.EAppearanceType2ID.Fashion then
			for _, config in ksbcpairs(Game.TableData.GetFashionDataTable()) do
				if config.Gender == Game.me.Sex and config.Type == mainType and not config.IsHideInUI
					and (not config.ClassCond or config.ClassCond == Game.me.Profession)
				then
					if not self.allItemList[mainType][config.SubType] then
						self.allItemList[mainType][config.SubType] = {}
					end

					--获取状态信息
					table.insert(self.allItemList[mainType][config.SubType], {
						configID = config.ID,
						sortType = Game.FashionSystem:GetFashionSortType(config.ID)
					})
				end
			end
		elseif mainType == Enum.EAppearanceType2ID.Accessory then
			for _, config in ksbcpairs(Game.TableData.GetFashionDataTable()) do
				if (not config.Gender or config.Gender == Game.me.Sex) and config.Type == mainType and not config.IsHideInUI
					and (not config.ClassCond or config.ClassCond == Game.me.Profession)
				then
					if not self.allItemList[mainType][config.SubType] then
						self.allItemList[mainType][config.SubType] = {}
					end
					table.insert(self.allItemList[mainType][config.SubType], {
						configID = config.ID,
						sortType = Game.FashionSystem:GetFashionSortType(config.ID)
					})
				end
			end
		elseif mainType == Enum.EAppearanceType2ID.Around then
			for _, config in ksbcpairs(Game.TableData.GetFashionDataTable()) do
				if (not config.Gender or config.Gender == Game.me.Sex) and config.Type == mainType and not config.IsHideInUI
				then
					if not self.allItemList[mainType][config.SubType] then
						self.allItemList[mainType][config.SubType] = {}
					end
					table.insert(self.allItemList[mainType][config.SubType], {
						configID = config.ID,
						sortType = Game.FashionSystem:GetFashionSortType(config.ID)
					})
				end
			end
		end
	end

	if not self.allItemList[mainType][subType] then
		self.allItemList[mainType][subType] = {}
	end
	local typeList = self.allItemList[mainType][subType]

	if not string.isEmpty(self.curFilterText) or self.curCheckBoxState then
		local filterList = {}
		for i, data in ipairs(typeList) do
			local config = Game.TableData.GetFashionDataRow(data.configID)
			local name = config.FashionName
			local sortType = data.sortType

			--勾选了，并且不是未拥有  
			if self.curCheckBoxState and sortType ~= Enum.EFashionSortType.UnPossess then
				goto continue
			end

			--有过滤文本，并且不包含这个name
			if not string.isEmpty(self.curFilterText) and not string.find(name, self.curFilterText) then
				goto continue
			end
			
			table.insert(filterList, data)
			::continue::
		end
		
		self:SortItemDataList(filterList)
		return filterList
	else
		self:SortItemDataList(typeList)
		return typeList
	end
end

---排序
function FashionChange_Widget:SortItemDataList(itemDataList)
	if not itemDataList then
		return
	end

	for i, itemData in pairs(itemDataList) do
		itemData.sortType = Game.FashionSystem:GetFashionSortType(itemData.configID)
	end
	
	table.sort(itemDataList, function(left, right)
		if left.sortType ~= right.sortType then
			return left.sortType < right.sortType
		else
			local leftConfig = Game.TableData.GetFashionDataRow(left.configID)
			local rightConfig = Game.TableData.GetFashionDataRow(right.configID)
			
			--品质
			if leftConfig.quality ~= rightConfig.quality then
				return leftConfig.quality > rightConfig.quality
			end

			--上线时间
			if leftConfig.TimeStamp ~= rightConfig.TimeStamp then
				return leftConfig.TimeStamp > rightConfig.TimeStamp
			end

			local _, priceLeft = Game.FashionSystem:GetFashionShopExpend(leftConfig.ID)
			local _, priceRight = Game.FashionSystem:GetFashionShopExpend(rightConfig.ID)
			--价格
			if priceLeft ~= priceRight then
				return priceLeft > priceRight
			end
			
			--ID
			return leftConfig.ID < rightConfig.ID
		end
	end)
end

--endregion

---@private method 选中改变后，更新右侧内容
function FashionChange_Widget:RefreshRightAreaData(index)
	if not index then
		self.WBP_FashionFunctionR_PageCom:Refresh()
		return
	end

	local selectData = self.curItemList[index]
	self.WBP_FashionFunctionR_PageCom:Refresh(selectData.configID)
end

function FashionChange_Widget:WearAppearance(id)
	Game.FashionSystem:WearAppearance(Game.FashionSystem:GetCurFashionEntity(), id)
end

function FashionChange_Widget:UnWearAppearance(id)
	Game.FashionSystem:UnWearAppearance(Game.FashionSystem:GetCurFashionEntity(), id)
end

function FashionChange_Widget:UnWearAllAppearance()
	Game.FashionSystem:GetCurFashionEntity():WearFashion(4220070, {
		removeAllFashion = true
	})
end

---时装数据变更时刷新（收藏、解锁、等等）
function FashionChange_Widget:RefreshSingleFashion(fashionID)
	for i, data in pairs(self.curItemList) do
		if data.configID == fashionID then
			self.KGItemListCom:RefreshItemByIndex(i)
		end
	end
	
	self:RefreshRightAreaData(self.curSelectIndex)
end

---穿脱时装时刷新
function FashionChange_Widget:RefreshWearFashion()
	if not self.curSelectSubIndex then
		return
	end
	
	--刷新二级标签图标
	for i, data in pairs(self.allSubTypeDataList) do
		self.KGSubTypeListCom:RefreshItemByIndex(i)
	end
	
	--刷新item选中和数据
	local select = false
	local entity = Game.FashionSystem:GetCurFashionEntity()
	local subTypeConfig = Game.TableData.GetAppearanceSubTypeRow(self.curSelectSubType)
	if subTypeConfig.MainType == Enum.EAppearanceType2ID.Around then
		for i, data in pairs(self.curItemList) do
			self.KGItemListCom:RefreshItemByIndex(i)
		end
	else
		local fashionID = entity:GetFashionBySubType(self.curSelectSubType)
		for i, data in pairs(self.curItemList) do
			if data.configID == fashionID then
				select = true
				if i ~= self.KGItemListCom:GetSelectedItemIndex() and subTypeConfig.MainType ~= Enum.EAppearanceType2ID.Around then
					self.KGItemListCom:SetSelectedItemByIndex(i, true)
				end
			end
			self.KGItemListCom:RefreshItemByIndex(i)
		end
	end
	
	if self.clickItemList then
		self.clickItemList = false
	else
		if not select then
			self.KGItemListCom:ClearSelection()
		end
	end
end

--- 此处为自动生成
function FashionChange_Widget:on_WBP_ShowBtnCom_ClickEvent()
	self:RefreshUIHide(false)
end

function FashionChange_Widget:RefreshUIHide(hide)
	if hide then
		self:PlayAnimation(self.userWidget.Ani_Fadeout)
		self:RefreshShowUIVisible(true)
	else
		self:PlayAnimation(self.userWidget.Ani_Fadein)
		self:RefreshShowUIVisible(false)
	end
end

function FashionChange_Widget:RefreshShowUIVisible(visible)
	if visible then
		self.view.WBP_ShowBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.WBP_ShowBtn:SetVisibility(ESlateVisibility.HitTestInvisible)
	end
end

return FashionChange_Widget
