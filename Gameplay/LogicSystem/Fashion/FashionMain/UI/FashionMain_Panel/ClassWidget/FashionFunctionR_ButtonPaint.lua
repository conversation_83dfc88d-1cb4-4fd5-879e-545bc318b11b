local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
---@class FashionFunctionR_ButtonPaint : UIComponent
---@field view FashionFunctionR_ButtonPaintBlueprint
local FashionFunctionR_ButtonPaint = DefineClass("FashionFunctionR_ButtonPaint", UIComponent)

FashionFunctionR_ButtonPaint.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionFunctionR_ButtonPaint:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionFunctionR_ButtonPaint:InitUIData()
	---按钮点击事件
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function FashionFunctionR_ButtonPaint:InitUIComponent()
    ---@type UIListView
    self.ListPaintCom = self:CreateComponent(self.view.ListPaint, UIListView)
end

---UI事件在这里注册，此处为自动生成
function FashionFunctionR_ButtonPaint:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
	self:AddUIEvent(self.ListPaintCom.onItemSelected, "on_ListPaintCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionFunctionR_ButtonPaint:InitUIView()
end

---组件刷新统一入口
function FashionFunctionR_ButtonPaint:Refresh(fashionID, dataList)
	self.showList = false
	self.fashionID = fashionID
	self.dataList = dataList
	
	local config = Game.TableData.GetFashionDataRow(self.fashionID)
	self.hasArrow = config.SubType ~= Enum.EAppearanceSubType2ID.Suit
	self.userWidget:Event_UI_State(false, self.hasArrow)
end

--- 此处为自动生成
function FashionFunctionR_ButtonPaint:on_Btn_ClickArea_Clicked()
	if self.onClickEvent:IsBind() then
		self.onClickEvent:Execute()
	else
		--给服装应用染色，并且上传服务器切换染色槽位
		self.showList = not self.showList
		self.userWidget:Event_UI_State(self.showList, self.hasArrow)
	end
	
	local selectIndex = Game.FashionSystem:GetFashionSaveMakeUpIndex(self.fashionID)
	self.ListPaintCom:Refresh(self.dataList)
	self.ListPaintCom:SetSelectedItemByIndex(selectIndex and selectIndex+1 or 1, true, true)
end

function FashionFunctionR_ButtonPaint:ExpandButton()
	self.showList = not self.showList
	self.userWidget:Event_UI_State(self.showList, self.hasArrow)
end

--- 此处为自动生成
---@param index number
---@param data table
function FashionFunctionR_ButtonPaint:on_ListPaintCom_ItemSelected(index, data)
	Game.FashionSystem:ReqSetCurWearStain(self.fashionID, index-1)
end

return FashionFunctionR_ButtonPaint
