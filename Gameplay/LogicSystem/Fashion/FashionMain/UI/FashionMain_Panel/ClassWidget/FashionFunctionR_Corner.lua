local FashionFunctionR_FashionRightInfo = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.ClassWidget.FashionFunctionR_FashionRightInfo")
local UIComCurrencyItem = kg_require("Framework.KGFramework.KGUI.Component.Tag.UIComCurrencyItem")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ItemConst = kg_require("Shared.ItemConst")
local ESlateVisibility = import("ESlateVisibility")
---@class FashionFunctionR_Corner : UIComponent
---@field view FashionFunctionR_CornerBlueprint
local FashionFunctionR_Corner = DefineClass("FashionFunctionR_Corner", UIComponent)

FashionFunctionR_Corner.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionFunctionR_Corner:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionFunctionR_Corner:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionFunctionR_Corner:InitUIComponent()
    ---@type FashionFunctionR_FashionRightInfo
    self.WBP_FashionRightInfo_WidgetCom = self:CreateComponent(self.view.WBP_FashionRightInfo_Widget, FashionFunctionR_FashionRightInfo)
    ---@type UIComCurrencyItem
    self.WBP_ComCurrencyCom = self:CreateComponent(self.view.WBP_ComCurrency, UIComCurrencyItem)
    ---@type UIComButton
    self.WBP_ComBtnRightCom = self:CreateComponent(self.view.WBP_ComBtnRight, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function FashionFunctionR_Corner:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnRightCom.onClickEvent, "on_WBP_ComBtnRightCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionFunctionR_Corner:InitUIView()
end

---组件刷新统一入口
function FashionFunctionR_Corner:Refresh(fashionID)
	self.fashionID = fashionID

	local fashionConfig = self.fashionID and Game.TableData.GetFashionDataRow(self.fashionID)
	local name = fashionConfig and fashionConfig.FashionName
	local subType = fashionConfig and fashionConfig.SubType
	self.WBP_FashionRightInfo_WidgetCom:Refresh(fashionID, name, subType)

	if not fashionID then
		self.userWidget:Event_UI_Buy(false, false)
		self.view.HB_Default:SetVisibility(ESlateVisibility.Collapsed)
		return
	end

	--时装对应的id是否已解锁
	if Game.FashionSystem:IsFashionUnlock(fashionID) then
		self.view.HB_Default:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

		self.userWidget:Event_UI_Buy(false, true)
		self.WBP_ComBtnRightCom:SetName(StringConst.Get("FASHION_GO_GET"))

		self.view.Text_Path:SetText(fashionConfig.AchieveDes or "")
	else
		self.view.HB_Default:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

		--是否是套装，非套装的判断是否有所属套装，有就按照所属的价格
		if fashionConfig.SubType == Enum.EAppearanceSubType2ID.Suit then
			self:RefreshRightBottom_FashionCost(fashionID)
		else
			if fashionConfig.SuitID and fashionConfig.SuitID>0 then
				self:RefreshRightBottom_FashionCost(fashionConfig.SuitID)
			else
				self:RefreshRightBottom_FashionCost(fashionID)
			end
		end
	end
end

function FashionFunctionR_Corner:RefreshRightBottom_FashionCost(fashionID)
	--未解锁  1、可以买   2、不能买
	local tokenId, price = Game.FashionSystem:GetFashionShopExpend(fashionID)
	if tokenId and price then
		--AchieveDes
		self.userWidget:Event_UI_Buy(true, false)
		self.WBP_ComBtnRightCom:SetName(StringConst.Get("FASHION_BUY"))

		self.WBP_ComCurrencyCom:Refresh(ItemConst.ITEM_SPECIAL_MONEY_CASH_BOUND)
		self.WBP_ComCurrencyCom:SetUIType(Enum.UIComCurrencyType.Cost)
		self.WBP_ComCurrencyCom:SetMoneyNum(price, "")
	else
		self.userWidget:Event_UI_Buy(false, true)
		self.WBP_ComBtnRightCom:SetName(StringConst.Get("FASHION_GO_GET"))

		local config = Game.TableData.GetFashionDataRow(self.fashionID)
		self.view.Text_Path:SetText(config.AchieveDes)
	end
end

--- 此处为自动生成
function FashionFunctionR_Corner:on_WBP_ComBtnRightCom_ClickEvent()
	local buyFashionID = self.fashionID
	local fashionConfig = Game.TableData.GetFashionDataRow(self.fashionID)
	if fashionConfig.SubType ~= Enum.EAppearanceSubType2ID.Suit then
		if fashionConfig.SuitID and fashionConfig.SuitID>0 then
			buyFashionID = fashionConfig.SuitID
		end
	end

	local _, price = Game.FashionSystem:GetFashionShopExpend(buyFashionID)
	--钱不够
	local myGoldNum = Game.CurrencySystem:GetMoneyByType(ItemConst.ITEM_SPECIAL_MONEY_CASH_BOUND)
	if myGoldNum < price then
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.FASHION_CANT_BUY_TIPS,
			function()
				self:EnterBuyMoneyPanel()
			end,
			nil,
			{price}
		)
		return
	end

	--钱够、时装购买
	local buyConfig = Game.TableData.GetFashionDataRow(buyFashionID)
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.FASHION_PURCHASE_CHECK,
		function()
			self:EnterBuyFashion(buyFashionID)
		end,
		nil,
		{price, buyConfig.FashionName}
	)
end

-- todo 弹出购买窗口
function FashionFunctionR_Corner:EnterBuyMoneyPanel()

end

function FashionFunctionR_Corner:EnterBuyFashion(buyFashionID)
	local config = Game.TableData.GetFashionDataRow(buyFashionID)
	local costFashion = {buyFashionID}

	Game.FashionSystem:ReqBuyAppearanceComp(costFashion)
	Game.FashionSystem:ReqChangeAppearanceNewFlag(config.SubType)
end

return FashionFunctionR_Corner
