local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class FashionClass_Item : UIListItem
---@field view FashionClass_ItemBlueprint
local FashionClass_Item = DefineClass("FashionClass_Item", UIListItem)

FashionClass_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionClass_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionClass_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionClass_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function FashionClass_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionClass_Item:InitUIView()
end

---面板打开的时候触发
function FashionClass_Item:OnRefresh(data)
	self.data = data
	self.view.Text_Class:SetText(data.name)

	local entity = Game.FashionSystem:GetCurFashionEntity()
	local curFashionID = entity:GetFashionBySubType(data.subType)
	if curFashionID then
		local config = Game.TableData.GetFashionDataRow(curFashionID)
		if config.IsHideInUI then
			self.userWidget:SetDressDisplay(false)
			self.userWidget:SetWear(true)
			self:SetImage(self.view.Img_UnWear, data.icon)

			self.userWidget:SetLevel(false, 0)
		else
			--显示正在穿的，并且在衣柜里的
			self.userWidget:SetDressDisplay(true)
			self.userWidget:SetWear(false)
			self:SetImage(self.view.Img_Dress, config.Icon)

			self.userWidget:SetLevel(true, config.quality-1)
		end
	else
		self.userWidget:SetDressDisplay(false)
		self.userWidget:SetWear(true)
		self:SetImage(self.view.Img_UnWear, data.icon)
		
		self.userWidget:SetLevel(false, 0)
	end
	Game.RedDotSystem:AttachWidget(self:GetBelongPanel(), self.view.Img_NewTag, "FashionLeftSubTab", data.subType)
end

return FashionClass_Item
