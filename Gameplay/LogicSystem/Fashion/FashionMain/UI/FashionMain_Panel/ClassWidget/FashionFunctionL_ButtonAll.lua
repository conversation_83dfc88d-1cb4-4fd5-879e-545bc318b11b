local FashionFunctionL_ButtonExpand = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.ClassWidget.FashionFunctionL_ButtonExpand")
local FashionFunctionL_Button = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.ClassWidget.FashionFunctionL_Button")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class FashionFunctionL_ButtonAll : UIComponent
---@field view FashionFunctionL_ButtonAllBlueprint
local FashionFunctionL_ButtonAll = DefineClass("FashionFunctionL_ButtonAll", UIComponent)

FashionFunctionL_ButtonAll.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionFunctionL_ButtonAll:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionFunctionL_ButtonAll:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionFunctionL_ButtonAll:InitUIComponent()
    ---@type FashionFunctionL_Button
    self.WBP_UnwearAllBtnCom = self:CreateComponent(self.view.WBP_UnwearAllBtn, FashionFunctionL_Button)
    ---@type FashionFunctionL_Button
    self.WBP_RandomWearBtnCom = self:CreateComponent(self.view.WBP_RandomWearBtn, FashionFunctionL_Button)
    ---@type FashionFunctionL_Button
    self.WBP_HideUIBtnCom = self:CreateComponent(self.view.WBP_HideUIBtn, FashionFunctionL_Button)
    ---@type FashionFunctionL_ButtonExpand
    self.WBP_FashionExpandBtnListCom = self:CreateComponent(self.view.WBP_FashionExpandBtnList, FashionFunctionL_ButtonExpand)
end

---UI事件在这里注册，此处为自动生成
function FashionFunctionL_ButtonAll:InitUIEvent()
    self:AddUIEvent(self.WBP_RandomWearBtnCom.onClickEvent, "on_WBP_RandomWearBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_UnwearAllBtnCom.onClickEvent, "on_WBP_UnwearAllBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_HideUIBtnCom.onClickEvent, "on_WBP_HideUIBtnCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionFunctionL_ButtonAll:InitUIView()
	self.userWidget:Event_UI_Style(0)
end

---组件刷新统一入口
function FashionFunctionL_ButtonAll:Refresh()
	self.WBP_HideUIBtnCom:Refresh()
	self.WBP_RandomWearBtnCom:Hide()
	self.WBP_UnwearAllBtnCom:Refresh()
	
	self.WBP_FashionExpandBtnListCom:Refresh()
end

function FashionFunctionL_ButtonAll:RefreshMainTypeChange(mainType)
	self.WBP_FashionExpandBtnListCom:RefreshMainTypeChange(mainType)
end

--- 此处为自动生成
function FashionFunctionL_ButtonAll:on_WBP_HideUIBtnCom_ClickEvent()
	--触发隐藏
	self:GetParent():RefreshUIHide(true)
end

--- 此处为自动生成
function FashionFunctionL_ButtonAll:on_WBP_RandomWearBtnCom_ClickEvent()
	--Game.FashionSystem:RandomWearFashion()
end

--- 此处为自动生成
function FashionFunctionL_ButtonAll:on_WBP_UnwearAllBtnCom_ClickEvent()
	Game.FashionSystem:UnWearAllFashion()
end


return FashionFunctionL_ButtonAll
