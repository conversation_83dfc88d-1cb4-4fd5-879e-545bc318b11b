local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local const = kg_require("Shared.Const")
---@class FashionPaint_Item : UIListItem
---@field view FashionPaint_ItemBlueprint
local FashionPaint_Item = DefineClass("FashionPaint_Item", UIListItem)

FashionPaint_Item.eventBindMap = {
	[EEventTypesV2.CHAT_CUSTOM_EMO_UPDATE] = "ResUrlUpdate",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionPaint_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionPaint_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionPaint_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function FashionPaint_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionPaint_Item:InitUIView()
end

---面板打开的时候触发
function FashionPaint_Item:OnRefresh(data)
	self.fashionID = data.fashionID
	local fashionConfig = Game.TableData.GetFashionDataRow(self.fashionID)

	--默认的
	if self.index == 1 then
		self.userWidget:SetState(1)
		self:SetImage(self.view.Icon, fashionConfig.Icon)
		self.userWidget:BP_SetVefiry(false)
		self.userWidget:BP_SetVefiryFail(false)
	else
		--有自定义染色数据
		local makeUpData = Game.FashionSystem:GetFashionSaveMakeUpData(self.fashionID, self.index - 1)
		if makeUpData then
			self.userWidget:SetState(1)
			self.StainPictureID = data.icon
			
			if not string.isEmpty(self.StainPictureID) then
				--local resInfo = Game.LocalResSystem:GetResInfoByResID(self.StainPictureID)
				--if resInfo then
				--	self:RefreshIconByResInfo(resInfo)
				--else
				--	Game.LocalResSystem:ReqResUrls({self.StainPictureID})
				--end
				Game.LocalResSystem:SetImageByResId(self, self.view.Icon, self.StainPictureID, nil,
						"Reviewing", "ReviewPass", "ReviewReject")
			else
				Log.ErrorFormat("Fashion: self.StainPictureID = nil, fashionID = %s", self.fashionID)
			end
		else
			--无图片
			self.userWidget:SetState(2)
			self.userWidget:BP_SetVefiry(false)
			self.userWidget:BP_SetVefiryFail(false)
		end
	end
end

function FashionPaint_Item:ResUrlUpdate(resdata)
	local key, resInfo = next(resdata)
	if self.StainPictureID ~= key then
		return
	end

	self:RefreshIconByResInfo(resInfo)
end

function FashionPaint_Item:RefreshIconByResInfo_(resInfo)
	--审核中，非审核中
	if resInfo.status == const.CHAT_CUSTOM_IMG_STATUS.APPROVED then
		local url = Game.LocalResSystem:GetResUrlInfoByResID(self.StainPictureID)
		if url then
			self:SetImageByUrl(self.view.Icon, url, self.StainPictureID)
		else
			Log.ErrorFormat("RefreshIconByResInfo error, id = %s", self.StainPictureID)
		end
		self.userWidget:BP_SetVefiry(false)
		self.userWidget:BP_SetVefiryFail(false)
	elseif resInfo.status == const.CHAT_CUSTOM_IMG_STATUS.REJECTED then
		--显示不通过
		self.userWidget:BP_SetVefiry(false)
		self.userWidget:BP_SetVefiryFail(true)
	else
		--显示审核中
		self.userWidget:BP_SetVefiry(true)
		self.userWidget:BP_SetVefiryFail(false)
	end
end

function FashionPaint_Item:ReviewPass()
	self.userWidget:BP_SetVefiry(false)
	self.userWidget:BP_SetVefiryFail(false)
end

function FashionPaint_Item:ReviewReject()
	--显示不通过
	self.userWidget:BP_SetVefiry(false)
	self.userWidget:BP_SetVefiryFail(true)
end

function FashionPaint_Item:Reviewing()
	--显示审核中
	self.userWidget:BP_SetVefiry(true)
	self.userWidget:BP_SetVefiryFail(false)
end

--- 此处为自动生成
function FashionPaint_Item:on_Btn_ClickArea_Clicked()
	
end

return FashionPaint_Item
