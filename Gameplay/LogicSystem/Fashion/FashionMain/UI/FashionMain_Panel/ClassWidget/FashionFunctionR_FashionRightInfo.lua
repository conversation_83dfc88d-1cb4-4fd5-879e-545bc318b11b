local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@class FashionFunctionR_FashionRightInfo : UIComponent
---@field view FashionFunctionR_WidgetBlueprint
local FashionFunctionR_FashionRightInfo = DefineClass("FashionFunctionR_FashionRightInfo", UIComponent)

FashionFunctionR_FashionRightInfo.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionFunctionR_FashionRightInfo:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionFunctionR_FashionRightInfo:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionFunctionR_FashionRightInfo:InitUIComponent()
    ---@type UIComDiyTitle
    self.WBP_ComFirstBigTextCom = self:CreateComponent(self.view.WBP_ComFirstBigText, UIComDiyTitle)
end

---UI事件在这里注册，此处为自动生成
function FashionFunctionR_FashionRightInfo:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionFunctionR_FashionRightInfo:InitUIView()
end

---组件刷新统一入口
---@param fashionID int
---@param name string
---@param subType int 这块可能是时装也可能是坐骑之类的
function FashionFunctionR_FashionRightInfo:Refresh(fashionID, name, subType)
	self.fashionID = fashionID
	self.subType = subType

	if fashionID then
		self.WBP_ComFirstBigTextCom:Refresh(name)
	end
end

--- 此处为自动生成
function FashionFunctionR_FashionRightInfo:on_Btn_ClickArea_Clicked()
	--local config = Game.TableData.GetFashionDataRow(self.fashionID)
	--local shopItemID = config.ShopItemID
	--if not shopItemID or shopItemID == 0 then
	--	return
	--end
	
	--local shopItemConfig = Game.TableData.GetNpcGoodsDataRow(shopItemID)
	--local bagItemID = shopItemConfig.ItemID
	--local itemConfig = Game.TableData.GetItemNewDataRow(bagItemID)
	
	Game.NewUIManager:OpenPanel('FashionTips_Panel', self.fashionID, self.subType, 1)
end

return FashionFunctionR_FashionRightInfo
