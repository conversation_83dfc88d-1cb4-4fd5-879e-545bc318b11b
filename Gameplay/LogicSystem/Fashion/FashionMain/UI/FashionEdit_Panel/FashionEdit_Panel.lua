local FashionFunctionL_Button = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.ClassWidget.FashionFunctionL_Button")
local UIComSimpleTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UITempComBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UITempComBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class FashionEdit_Panel : UIComponent
local FashionEdit_Panel = DefineClass("FashionEdit_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")


FashionEdit_Panel.eventBindMap = {
	[EEventTypesV2.ON_FASHION_UI_WEAPON_UPDATE] = "RefreshWeapon",
}

function FashionEdit_Panel:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

function FashionEdit_Panel:InitUIData()
end

function FashionEdit_Panel:InitUIComponent()
    ---@type FashionFunctionL_Button
    self.WBP_HideUIBtnCom = self:CreateComponent(self.view.WBP_HideUIBtn, FashionFunctionL_Button)
    ---@type FashionFunctionL_Button
    self.WBP_WeaponBtnCom = self:CreateComponent(self.view.WBP_WeaponBtn, FashionFunctionL_Button)
    ---@type UIComDropDown
    self.WBP_ComDropCom = self:CreateComponent(self.view.WBP_ComDrop, UIComDropDown)
    ---@type UIComSimpleTabList
    self.WBP_ComTabHorCom = self:CreateComponent(self.view.WBP_ComTabHor, UIComSimpleTabList)
    ---@type UIComDiyTitle
    self.WBP_FirstBigTextCom = self:CreateComponent(self.view.WBP_FirstBigText, UIComDiyTitle)
    ---@type UIComButton
    self.WBP_ComBtnRCom = self:CreateComponent(self.view.WBP_ComBtnR, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtnLCom = self:CreateComponent(self.view.WBP_ComBtnL, UIComButton)
    ---@type UIListView
    self.WBP_EditListCom = self:CreateComponent(self.view.WBP_EditList, UIListView)
    ---@type UITempComBtn
    self.WBP_ComBtnCloseNewCom = self:CreateComponent(self.view.WBP_ComBtnCloseNew, UITempComBtn)
end

function FashionEdit_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnLCom.onClickEvent, "on_WBP_ComBtnLCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnRCom.onClickEvent, "on_WBP_ComBtnRCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnCloseNewCom.onClickEvent, "on_WBP_ComBtnCloseNewCom_ClickEvent")
    self:AddUIEvent(self.WBP_WeaponBtnCom.onClickEvent, "on_WBP_WeaponBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_HideUIBtnCom.onClickEvent, "on_WBP_HideUIBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComTabHorCom.onItemSelected, "on_WBP_ComTabHorCom_ItemSelected")
end

function FashionEdit_Panel:InitUIView()
	self.WBP_FirstBigTextCom:Refresh(StringConst.Get("FASHION_EDIT_FINE_TUNING"))
	self.WBP_ComBtnLCom:SetName(StringConst.Get("FASHION_RESET"))
	self.WBP_ComBtnRCom:SetName(StringConst.Get("FASHION_SAVE"))
end

function FashionEdit_Panel:OnRefresh(fashionID)
	self.fashionID = fashionID
	self.fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	self.accConfigID = self.fashionConfig.AccConfigID[1]
	self.accConfig = Game.TableData.GetFashionAccConfigDataRow(self.accConfigID)

	self:InitFashionSocketList()
	self:InitFashionOffsetDefaultData()
	self:InitFashionOffsetList()
	
	Game.UIManager:HidePanel("FashionEdit_Panel","FashionMain_Panel")
end

function FashionEdit_Panel:OnClose()
	Game.UIManager:ShowPanel("FashionEdit_Panel","FashionMain_Panel")
end

function FashionEdit_Panel:InitFashionSocketList()
	local showSocket = self.accConfig.SocketName[1] ~= nil
	if not showSocket then
		self.WBP_ComDropCom:Hide()
		self.WBP_ComTabHorCom:Hide()
		return
	end
	
	self.dropDataList = {}
	table.insert(self.dropDataList, {
		name = self.accConfig.MountPosition
	})
	self.WBP_ComDropCom:Show()
	self.WBP_ComDropCom:Refresh(self.dropDataList)
	
	self.socketDataList = {
		[1] = {name = StringConst.Get("FASHION_SOCKET_LEFT"), index = 1, socketNameList = {self.accConfig.SocketName[1]}},
		[2] = {name = StringConst.Get("FASHION_SOCKET_RIGHT"),index = 2, socketNameList = {self.accConfig.SocketName[2]}},
		[3] = {name = StringConst.Get("FASHION_SOCKET_LEFT_RIGHT"),index = 3, socketNameList = {self.accConfig.SocketName[1], self.accConfig.SocketName[2]}}
	}

	self.firstSelect = true
	self.WBP_ComTabHorCom:Show()
	self.WBP_ComTabHorCom:Refresh(self.socketDataList)

	local wardrobeData = Game.FashionSystem:GetWardrobeDataByID(self.fashionID)
	self.WBP_ComTabHorCom:SetSelectedItemByIndex((wardrobeData and wardrobeData.SlotPos) or 
		self.accConfig.DefaultSocket, true
	)
end

--- 此处为自动生成
---@param index number
---@param data table
function FashionEdit_Panel:on_WBP_ComTabHorCom_ItemSelected(index, data)
	if self.firstSelect then
		self.firstSelect = false
		return
	end

	Game.FashionSystem:GetCurFashionEntity():RefreshFashionSocketData(
		self.fashionID, 
		data.socketNameList, 
		Game.FashionSystem:GetFashionOffsetTransform(
			self.fashionID,
			self.Scale,
			self.LocationX, self.LocationY, self.LocationZ,
			self.RotationX, self.RotationY, self.RotationZ
		)
	)
end

function FashionEdit_Panel:InitFashionOffsetDefaultData()
	--默认值
	self.TransformDefault = Game.FashionSystem:GetFashionDefaultOffsetTransform(self.fashionID)
	
	self:InitFashionOffsetSaveData()
end

function FashionEdit_Panel:InitFashionOffsetSaveData()
	local scale,posX,posY,posZ,roaX,roaY,roaZ = Game.FashionSystem:GetFashionSaveOffsetData(self.fashionID)
	--相对偏移，默认是0
	self.Scale     = scale
	self.LocationX = posX
	self.LocationY = posY
	self.LocationZ = posZ
	self.RotationX = roaX
	self.RotationY = roaY
	self.RotationZ = roaZ

	self:RefreshFashionOffsetData()
end

function FashionEdit_Panel:InitFashionOffsetList()
	self.editDataList = {}
	for i, config in ksbcipairs(Game.TableData.GetAccEditConfigDataTable()) do
		table.insert(self.editDataList, {
			editConfig = config,
			accConfig = self.accConfig
		})
	end
	self.WBP_EditListCom:Refresh(self.editDataList)
end

function FashionEdit_Panel:RefreshFashionOffsetData()
	local transformDefault = Game.FashionSystem:GetFashionDefaultOffsetTransform(self.fashionID)
	local scaleDefault = transformDefault:GetScale3D().X
	local scaleFinal = (self.Scale + scaleDefault)/scaleDefault
	local curTransform = FTransform(
		FRotator(self.RotationX, self.RotationY, self.RotationZ):ToQuat(),
		FVector(self.LocationX, self.LocationY, self.LocationZ),
		FVector(scaleFinal, scaleFinal, scaleFinal)
	)

	local resultTransform = curTransform * self.TransformDefault
	local location = resultTransform:GetLocation()
	local rotation = resultTransform:GetRotation():Euler()
	local scale = resultTransform:GetScale3D()
	
	Game.FashionSystem:GetCurFashionEntity():RefreshFashionOffsetData(self.fashionID,
		location.X, location.Y, location.Z,
		rotation.X, rotation.Y, rotation.Z,
		scale.X, scale.Y, scale.Z
	)
end

--- 此处为自动生成
function FashionEdit_Panel:on_WBP_ComBtnCloseNewCom_ClickEvent()
	--如果有未保存
	if self:IsHasUnSave() then
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.FASHION_ADJUST_QUIT_NOT_SAVED, function()
			--先还原
			self:Reset2Save()
			self:CloseSelf()
		end, nil)
	else
		self:CloseSelf()
	end
end

--- 重置
--- 此处为自动生成
function FashionEdit_Panel:on_WBP_ComBtnLCom_ClickEvent()
	self:Reset2Default()
end

--- 保存
--- 此处为自动生成
function FashionEdit_Panel:on_WBP_ComBtnRCom_ClickEvent()
	--时装未获得
	if not Game.FashionSystem:IsFashionUnlock(self.fashionID) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FASHION_NOT_EDITABLE)
		return
	end

	local index = self.WBP_ComTabHorCom:GetSelectedItemIndex()
	Game.FashionSystem:ReqSaveFinetuneInfo(self.fashionID,
		self.Scale,
		self.LocationX, self.LocationY, self.LocationZ,
		self.RotationX, self.RotationY, self.RotationZ,
		index
	)
end


function FashionEdit_Panel:RefreshWeapon(isShow)
	self.WBP_WeaponBtnCom:RefreshOpenState(isShow)
end

--- 此处为自动生成
function FashionEdit_Panel:on_WBP_WeaponBtnCom_ClickEvent()
	Game.FashionSystem:ReqSetWeaponShowStatus()
end

--- 此处为自动生成
function FashionEdit_Panel:on_WBP_HideUIBtnCom_ClickEvent()
	
end

function FashionEdit_Panel:Reset2Default()
	self.Scale     = 0
	self.LocationX = 0
	self.LocationY = 0
	self.LocationZ = 0
	self.RotationX = 0
	self.RotationY = 0
	self.RotationZ = 0
	if self.accConfig.DefaultSocket and self.accConfig.DefaultSocket > 0 then
		self.WBP_ComTabHorCom:SetSelectedItemByIndex(self.accConfig.DefaultSocket, true)
	end
	self:RefreshFashionOffsetData()

	self.WBP_EditListCom:RefreshItems()
	Game.FashionSystem:ReqSaveClearFinetuneInfo(self.fashionID)
end

function FashionEdit_Panel:Reset2Save()
	self:InitFashionOffsetSaveData()
	local wardrobeData = Game.FashionSystem:GetWardrobeDataByID(self.fashionID)
	if self.accConfig.DefaultSocket and self.accConfig.DefaultSocket > 0 then
		self.WBP_ComTabHorCom:SetSelectedItemByIndex(
			(wardrobeData and wardrobeData.SlotPos) or self.accConfig.DefaultSocket, true)
	end
end

function FashionEdit_Panel:IsHasUnSave()
	local scale,posX,posY,posZ,roaX,roaY,roaZ = Game.FashionSystem:GetFashionSaveOffsetData(self.fashionID)
	if self.Scale ~= scale or
		self.LocationX ~= posX or
		self.LocationY ~= posY or
		self.LocationZ ~= posZ or
		self.RotationX ~= roaX or
		self.RotationY ~= roaY or
		self.RotationZ ~= roaZ
	then
		return true
	end
	
	return false
end


return FashionEdit_Panel
