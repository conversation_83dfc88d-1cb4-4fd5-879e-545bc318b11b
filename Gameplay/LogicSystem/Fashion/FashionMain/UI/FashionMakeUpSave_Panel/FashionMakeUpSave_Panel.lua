local UIComInputBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComInputBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local FashionMakeUpSave_Item = kg_require("Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMakeUpSave_Panel.FashionMakeUpSave_Item")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class FashionMakeUpSave_Panel : UIPanel
local FashionMakeUpSave_Panel = DefineClass("FashionMakeUpSave_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")

FashionMakeUpSave_Panel.eventBindMap = {
	[EEventTypesV2.ON_FASHION_UI_SINGLE_UPDATE] = "RefreshSaveList",
	[EEventTypesV2.CHAT_CUSTOM_EMO_UPDATE] = "RefreshResUrl",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionMakeUpSave_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionMakeUpSave_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionMakeUpSave_Panel:InitUIComponent()
    ---@type UIComInputBox
    self.WBP_ComInputCom = self:CreateComponent(self.view.WBP_ComInput, UIComInputBox)
    ---@type UIComButton
    self.WBP_ComBtn_RightCom = self:CreateComponent(self.view.WBP_ComBtn_Right, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_LeftCom = self:CreateComponent(self.view.WBP_ComBtn_Left, UIComButton)
    ---@type UIListView
    self.WBP_ComListCom = self:CreateComponent(self.view.WBP_ComList, UIListView)
    ---@type UIComBoxFrame 
    self.WBP_ComPopupSCom = self:CreateComponent(self.view.WBP_ComPopupS, UIComBoxFrame)
end

---UI事件在这里注册，此处为自动生成
function FashionMakeUpSave_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_RightCom.onClickEvent, "on_WBP_ComBtn_RightCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_LeftCom.onClickEvent, "on_WBP_ComBtn_LeftCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComListCom.onItemSelected, "on_WBP_ComListCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComListCom.onGetEntryLuaClass, "on_WBP_ComListCom_GetEntryLuaClass")
    self:AddUIEvent(self.WBP_ComPopupSCom.onPreCloseEvent, "on_WBP_ComPopupSCom_PreCloseEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionMakeUpSave_Panel:InitUIView()
	self.WBP_ComBtn_LeftCom:Refresh(StringConst.Get("FASHION_PRESETS_CANCEL"))
	self.WBP_ComBtn_RightCom:Refresh(StringConst.Get("FASHION_PRESETS_CONFIRM"))
	self.WBP_ComPopupSCom:Refresh(StringConst.Get("FASHION_COVERAGE_PLAN"))
	
	self.WBP_ComBtn_RightCom:SetDisable(true)
	self.WBP_ComInputCom:Hide()
end

---面板打开的时候触发
function FashionMakeUpSave_Panel:OnRefresh(fashionID, makeupDataList)
	self.fashionID = fashionID
	self.makeupDataList = makeupDataList
	self.uploading = false
	
	--有一个默认槽位，和三个固定槽位（暂时）
	self.makeUpSlotList = {}
	for i = 1, 4 do
		table.insert(self.makeUpSlotList, {
			fashionID = self.fashionID
		})
	end

	self.WBP_ComListCom:Refresh(self.makeUpSlotList)
end

function FashionMakeUpSave_Panel:OnClose()
	self.captureRT = nil
end

function FashionMakeUpSave_Panel:RefreshSaveList(fashionID)
	if self.fashionID ~= fashionID then
		return
	end

	self.makeUpSlotList = {}
	for i = 1, 4 do
		table.insert(self.makeUpSlotList, {
			fashionID = self.fashionID
		})
	end

	self.WBP_ComListCom:Refresh(self.makeUpSlotList)
end

function FashionMakeUpSave_Panel:TakeScreenShot()
	Game.ScreenShotUtil:TakeScreenShot(self,"OnScreenShotCaptured", false)
end

function FashionMakeUpSave_Panel:OnScreenShotCaptured(rt, sizeX, sizeY)
	Log.DebugFormat("Fashion: OnScreenShotCaptured rt = %s", rt)
	self.captureRT = rt
	self:UploadStain()
end

function FashionMakeUpSave_Panel:UploadStain()
	--保存本地
	local selectIndex = self.WBP_ComListCom:GetSelectedItemIndex()
	if not selectIndex then
		self.uploading = false
		return
	end

	selectIndex = selectIndex - 1
	if selectIndex == 0 then
		self.uploading = false
		return
	end
	
	local savePath = Game.FashionSystem:OnScreenCapture(self.captureRT, "FashionMakeUp", "MakeUp")
	Game.AllInSdkManager:Upload(savePath, function(resourceID)
		self.uploading = false

		if Game.NewUIManager:IsOpened(UIPanelConfig.FashionMakeUpSave_Panel) then
			Game.FashionSystem:ReqSaveStain(self.fashionID, selectIndex, self.makeupDataList, resourceID)
		end
	end, function()
		self.uploading = false
		self.captureRT = nil

		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FASHION_SDK_NOT_OPEN)
	end)
end

function FashionMakeUpSave_Panel:RefreshResUrl(resData)
	for i, v in ipairs(self.makeUpSlotList) do
		local item = self.WBP_ComListCom:GetItemByIndex(i)
		item:ResUrlUpdate(resData)
	end
end

--- 此处为自动生成
function FashionMakeUpSave_Panel:on_WBP_ComBtn_RightCom_ClickEvent()
	if self.uploading then
		return
	end

	self.uploading = true
	self:TakeScreenShot()
end

--- 此处为自动生成
function FashionMakeUpSave_Panel:on_WBP_ComBtn_LeftCom_ClickEvent()
	if self.uploading then
		return
	end
	
	self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function FashionMakeUpSave_Panel:on_WBP_ComListCom_ItemSelected(index, data)
	if index == 1 then
		self.WBP_ComBtn_RightCom:SetDisable(true)
	else
		self.WBP_ComBtn_RightCom:SetDisable(false)
	end
end


--- 此处为自动生成
---@param index number
---@return UIComponent
function FashionMakeUpSave_Panel:on_WBP_ComListCom_GetEntryLuaClass(index)
	return FashionMakeUpSave_Item
end

--- 此处为自动生成
---@return bool
function FashionMakeUpSave_Panel:on_WBP_ComPopupSCom_PreCloseEvent()
	if self.uploading then
		return false
	end
	
	return true
end

return FashionMakeUpSave_Panel
