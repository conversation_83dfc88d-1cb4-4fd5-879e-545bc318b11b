local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UITempComBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UITempComBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class FashionAchieveLevel_Panel : UIPanel
---@field view FashionAchieveLevel_PanelBlueprint
local FashionAchieveLevel_Panel = DefineClass("FashionAchieveLevel_Panel", UIPanel)

FashionAchieveLevel_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionAchieveLevel_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionAchieveLevel_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FashionAchieveLevel_Panel:InitUIComponent()
    ---@type UIComDiyTitle
    self.WBP_FirstBigTextCom = self:CreateComponent(self.view.WBP_FirstBigText, UIComDiyTitle)
    ---@type UIListView
    self.WBP_ComListCom = self:CreateComponent(self.view.WBP_ComList, UIListView)
    ---@type UITempComBtn
    self.WBP_ComBtnCloseNewCom = self:CreateComponent(self.view.WBP_ComBtnCloseNew, UITempComBtn)
end

---UI事件在这里注册，此处为自动生成
function FashionAchieveLevel_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseNewCom.onClickEvent, "on_WBP_ComBtnCloseNewCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComListCom.onItemClicked, "on_WBP_ComListCom_ItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionAchieveLevel_Panel:InitUIView()
end

---面板打开的时候触发
function FashionAchieveLevel_Panel:OnRefresh()
	self:RefreshList()
	self.WBP_FirstBigTextCom:Refresh(StringConst.Get("FASHION_REWARD_LEVEL"))
end

function FashionAchieveLevel_Panel:RefreshList()
	self.rewardList = {}
	for id, config in ksbcipairs(Game.TableData.GetFashionLevelDataTable()) do
		table.insert(self.rewardList, config.FashionLevel)
	end
	self.WBP_ComListCom:Refresh(self.rewardList)
end

--- 此处为自动生成
function FashionAchieveLevel_Panel:on_WBP_ComBtnCloseNewCom_ClickEvent()
	self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function FashionAchieveLevel_Panel:on_WBP_ComListCom_ItemClicked(index, data)
	
end

return FashionAchieveLevel_Panel
