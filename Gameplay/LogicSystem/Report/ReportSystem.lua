---@class ReportSystem:SystemBase
local ReportSystem = DefineClass("ReportSystem", SystemBase)

local LuaFunctionLibrary = import("LuaFunctionLibrary")
local BlueprintPathsLibrary = import("BlueprintPathsLibrary")

function ReportSystem:onInit()
	self.model = kg_require("Gameplay.LogicSystem.Report.ReportModel").new(false,true)
	self.sender = kg_require("Gameplay.LogicSystem.Report.ReportSender").new()
end

function ReportSystem:onUnInit()
	--Game.EventSystem:RemoveObjListeners(self)
end

-- 打开举报/反馈界面
---@param reportID number 必选 (传入 Enum.EReportType 成员)  	举报所用的模版ID，详情参考 Report.xlsx 表备注
---@param targetInfo table  可选  	目标信息，如举报对象玩家、俱乐部等, table内的key: ID, name, isPlayer  （根据举报模版的区别持续拓展）
---@param addOnParam table 	可选		额外参数，暂时无用，故判断是否缓存时未考虑
function ReportSystem:ShowReportUI(reportID, targetInfo, addOnParam)
	local bLoadCache = self:ShouldLoadCache(reportID, targetInfo)
	
	local reportParam = {
		reportID = reportID,
		targetInfo = targetInfo,
		addOnParam = addOnParam,
		bCache = bLoadCache,
	}
	Game.NewUIManager:OpenPanel(UIPanelConfig.ReportPanel, reportParam)
end

--------------------- 辅助功能 ---------------------
--- 是否可以加载缓存
function ReportSystem:ShouldLoadCache(reportID, targetInfo)
	if not self.model:IsCacheValid(reportID, targetInfo) then
		self:ClearCache()
		return false
	end
	return true
end

--- 举报反馈上传成功，给UploadInfo函数回调
function ReportSystem:OnUploadInfoSuccess(resourceID)
	self.model:GetReportData().ImageRID = resourceID
	Log.DebugFormat("AllinSDK Report Upload success. RID=%s", resourceID)
	self:UploadLog()
end

--- 举报反馈上传失败，给UploadInfo函数回调
function ReportSystem:OnUploadInfoFailed()
	Log.Debug("AllInSDK Report Upload Failed")
end

--- 举报日志上传成功，给UploadLog函数回调
function ReportSystem:OnUploadLogSuccess(resourceID, targetFile)
	self.model:GetReportData().logID = resourceID
	Log.DebugFormat("AllinSDK Report Log Upload success. RID=%s", resourceID)
	self:SubmitReport()
	local succ, err = os.remove(targetFile)
	if not succ then
		Log.DebugFormat("Delete Temp Log Failed. %s", err)
	end
end

--- 举报日志上传失败，给UploadLog函数回调
function ReportSystem:OnUploadLogFailed()
	Log.Debug("AllInSDK Report Log Upload Failed")
end

--------------------- 系统功能 ---------------------
-- 准备举报数据。若有图片，上传cdn
function ReportSystem:UploadInfo(reportData)
	self.model:SetReportData(reportData)

	if reportData and reportData.path then
		-- 有截图，先提交CDN
		Game.AllInSdkManager:Upload(reportData.path, function(resourceID)
			self:OnUploadInfoSuccess(resourceID)
		end , function() 
			self:OnUploadInfoFailed()
		end)
	else
		self:UploadLog()
		end
end

function ReportSystem:UploadLog()
	local sourceFile = LuaFunctionLibrary.ConvertToAbsolutePathForExternalAppForRead(LuaFunctionLibrary
		.GetAbsoluteLogFilename())
	local targetFile = LuaFunctionLibrary.ConvertToAbsolutePathForExternalAppForRead(
		BlueprintPathsLibrary.ProjectLogDir() ..
			string.format("C7-backup-%s.log", os.date("%Y.%m.%d-%H.%M.%S", os.time())))
	local source = io.open(sourceFile, "rb")
	local target = io.open(targetFile, "wb")
	if source and target then
		local content = source:read("*a")
		target:write(content)
		source:close()
		target:close()
		Game.AllInSdkManager:Upload(targetFile, function(resourceID)
			self:OnUploadLogSuccess(resourceID, targetFile)
		end, function()
			self:OnUploadLogFailed()
		end)
	end
end

--- 举报反馈提交
function ReportSystem:SubmitReport()
	local reportData = self.model:GetReportData()
	local reportType = reportData.reportType or 0
	local reason = reportData.reportReason or ""
	local text = reportData.reportText or ""
	table.clear(self.model.pics)
	if reportData and reportData.ImageRID then
		table.insert(self.model.pics, reportData.ImageRID)
	end
	local target = reportData.reportTarget
	local targetId = target and target.ID or nil
	local targetSever = Game.LoginSystem:GetServerLoginData().ServerName
	local logID = reportData.logID

	if reportData.reportReason or reportData.reportText or reportData.ImageRID then
		local result, param = Game.AllInSdkManager:Feedback(reportType, reason, text, self.model.pics, targetId, targetSever, logID)
		Log.Debug("Report Submit! "..type(result).."  "..type(param))
	else
		Log.Warning("Empty Report!")
	end
end


--Game.ReportSystem.SubmitReport = function(self)
--	local reportData = self.model:GetReportData()
--	local reportType = reportData.reportType or 0
--	local reason = reportData.reportReason or ""
--	local text = reportData.reportText or ""
--	table.clear(self.model:GetPics())
--	if reportData and reportData.ImageRID then
--		table.insert(self.model:GetPics(), reportData.ImageRID)
--	end
--	local target = reportData.reportTarget
--	local targetId = target.ID
--	local targetSever = Game.LoginSystem:GetServerLoginData().ServerName
--	local logID = reportData.logID
--
--	if reportData.reportReason or reportData.reportText or reportData.ImageRID then
--		local result, param = Game.AllInSdkManager:Feedback(reportType, reason, text, self.model:GetPics(), targetId, targetSever, logID)
--		Log.Error(text)
--	else
--		Log.Warning("Empty Report!")
--	end
--end

-- 打开本地相册，选择单张照片
---@param func function 获取图片成功后的回调，参数为选中图片的路径
---@return string 文件路径
function ReportSystem:PickLocalFile(func)
	if not func then
		return
	end
	if PlatformUtil.IsMobilePlatform() then
		-- 移动平台走SDK
		Log.Debug("Local file by SDK")
		Game.AllInSdkManager:ChoosePhotos(1,nil,nil,nil,function(code,msg,resultData)
			if resultData and resultData.photos then
				func(resultData.photos[1])
			end
		end, function()
			func()
		end)
	else
		-- PC走DesktopPlatform
		Log.Debug("Local file by PC")
		local str = LuaFunctionLibrary.OpenFileDialog()
		Log.Debug("filePath = "..tostring(str))
		func(str)
	end
end

--- 缓存界面数据
function ReportSystem:SaveCache(rid, target, reportReason, reportText, path)
	self.model:SaveCache(rid, target, reportReason, reportText, path)
end

function ReportSystem:ClearCache()
	self.model:ClearCache()
end

---@return string reportReason 举报原因
---@return string reportText 举报内容
---@return string path 截图路径
function ReportSystem:GetCache()
	return self.model:GetCache()
end

return ReportSystem