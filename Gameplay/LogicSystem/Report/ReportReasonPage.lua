local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ESelectionMode = import("ESelectionMode")
---@class ReportReasonPage : UIComponent
---@field view ReportReasonPageBlueprint
local ReportReasonPage = DefineClass("ReportReasonPage", UIComponent)

ReportReasonPage.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReportReasonPage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReportReasonPage:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReportReasonPage:InitUIComponent()
	---@type UIListView
	self.KGTileView_ContentCom = self:CreateComponent(self.view.KGTileView_Content, UIListView)
end

---UI事件在这里注册，此处为自动生成
function ReportReasonPage:InitUIEvent()
	self:AddUIEvent(self.KGTileView_ContentCom.onItemSelectionChanged, "on_KGTileView_ContentCom_onItemSelectionChanged")
	self:AddUIEvent(self.KGTileView_ContentCom.onGetEntryLuaClass, "on_KGTileView_ContentCom_onGetEntryLuaClass")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReportReasonPage:InitUIView()
	self.KGTileView_ContentCom:SetSelectionMode(ESelectionMode.Multi)
	self.KGTileView_ContentCom:SetMultiModeMaxSelectionMode(Enum.EListMultiModeMaxSelectionMode.CantSelect)
end

---面板打开的时候触发
function ReportReasonPage:OnRefresh(param)
	self.view.T_ReportReason:SetText(param.title or "")
	
	-- 初始化多选框
	self.items = param.items
	self.KGTileView_ContentCom:Refresh(self.items)
	self.KGTileView_ContentCom:SetMultiModeMaxSelectionCount(param.limit)
	-- 读取缓存
	if type(param.cache) == string then
		self:readCache(param.cache)
	end
end

--- 获取选中的举报原因
function ReportReasonPage:GetReason()
	local resultStr = ""
	local isSelectedItemsIndexes = self.KGTileView_ContentCom:GetSelectedItemIndexes()
    for _, index in pairs(isSelectedItemsIndexes) do
		resultStr = resultStr .. ";" .. self.items[index].name
	end
	if resultStr == "" then
		return nil
	end
	return resultStr
end

--- 改变多选框中Item挂载的脚本
function ReportReasonPage:on_KGTileView_ContentCom_onGetEntryLuaClass(index, selected)
	return kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBoxItem")
end

---@private readCache 读取缓存
---@param cache string 缓存字符串
function ReportReasonPage:readCache(cache)
	-- todo 等待与策划确认这个功能需不需要
end

return ReportReasonPage
