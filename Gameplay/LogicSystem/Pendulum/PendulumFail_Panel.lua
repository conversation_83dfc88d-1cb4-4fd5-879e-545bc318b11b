local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PendulumFail_Panel : UIPanel
---@field view PendulumFail_PanelBlueprint
local PendulumFail_Panel = DefineClass("PendulumFail_Panel", UIPanel)

PendulumFail_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PendulumFail_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PendulumFail_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PendulumFail_Panel:InitUIComponent()
    ---@type UIComButton
    self.Btn_CloseCom = self:CreateComponent(self.view.Btn_Close, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function PendulumFail_Panel:InitUIEvent()
    self:AddUIEvent(self.Btn_CloseCom.onClickEvent, "on_Btn_CloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PendulumFail_Panel:InitUIView()
end

---面板打开的时候触发
function PendulumFail_Panel:OnRefresh(...)
end

--- 此处为自动生成
function PendulumFail_Panel:on_Btn_CloseCom_ClickEvent()
	if Game.NewUIManager:IsShow(UIPanelConfig.Pendulum_Panel) then
		Game.NewUIManager:ClosePanel(UIPanelConfig.Pendulum_Panel)
	end
	self:CloseSelf()
end

return PendulumFail_Panel
