local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Pendulum_Item : UIListItem
---@field view Pendulum_ItemBlueprint
local Pendulum_Item = DefineClass("Pendulum_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

---@class Pendulum_Item.MetaData
---@field text string 文本

Pendulum_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Pendulum_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Pendulum_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Pendulum_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Pendulum_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Pendulum_Item:InitUIView()
end

---@param data Pendulum_Item.MetaData
function Pendulum_Item:OnRefresh(data)
	if not data or not data.text then
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	self:AutoSetText(data.text)
	self:SetSelected(false)
end

function Pendulum_Item:AutoSetText(text)
	local length = utf8.len(text)
	if length == 0 then
		return
	end

	local part1 = utf8.sub(text, 1, 2) or ""
	local part2 = utf8.sub(text, 2, 3) or ""
	local part3 = utf8.sub(text, 3) or ""

	local lineBreak = length > 4 and "\n" or ""
	local finalUnselectText = string.format("<FirstBigUnSelect>%s</>%s%s%s", part1, part2, lineBreak, part3)
	local finalSelectText = string.format("<FirstBigSelect>%s</>%s%s%s", part1, part2, lineBreak, part3)

	self.view.Text_NameUnselect:SetText(finalUnselectText)
	self.view.Text_NameSelect:SetText(finalSelectText)
end	

-- public
function Pendulum_Item:SetSelected(bSelected)
	if bSelected == true then
		self:PlayAnimation(self.view.Ani_Selected)
	else
		self:PlayAnimation(self.view.Ani_UnSelected, nil, self.userWidget, 0, -1)	
	end
	--self.userWidget:BP_Pendulum_Select(bSelected == true)
end

return Pendulum_Item
