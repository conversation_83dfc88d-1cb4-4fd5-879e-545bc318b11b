kg_require("Gameplay.LogicSystem.GuildOutside_2Create.P_GuildOutCreateTypeView")
---@class P_GuildOutCreateType : UIComponent
---@field public View WBP_GuildOutCreateTypeView
local P_GuildOutCreateType = DefineClass("P_GuildOutCreateType", UIComponent)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

-- 显示类型
P_GuildOutCreateType.BadgeShowType = {
    TEXT = 1, ICON = 0, BG = 2, CONFIRM = 3
}

function P_GuildOutCreateType:OnCreate()
    
end

function P_GuildOutCreateType:OnShow()

end

function P_GuildOutCreateType:OnHide()

end

function P_GuildOutCreateType:RefreshData(index, data, showType, bSelect)
    self.View:Event_UI_Style(showType, bSelect)
    if bSelect then
        self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Select, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_UnSelect, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
    self.index = index
    if showType == self.BadgeShowType.ICON then
        self:SetImage(self.View.Img_Icon, data)
        self.View.TB_Word:SetVisibility(ESlateVisibility.Collapsed)
        self.View.Img_Icon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.Img_BadgeBg:SetVisibility(ESlateVisibility.Collapsed)
    elseif showType == self.BadgeShowType.TEXT then
        self.View.TB_Word:SetText(data)
        self.View.TB_Word:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.Img_Icon:SetVisibility(ESlateVisibility.Collapsed)
        self.View.Img_BadgeBg:SetVisibility(ESlateVisibility.Collapsed)
    else
        self:SetImage(self.View.Img_BadgeBg, data)
        self.View.TB_Word:SetVisibility(ESlateVisibility.Collapsed)
        self.View.Img_Icon:SetVisibility(ESlateVisibility.Collapsed)
        self.View.Img_BadgeBg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end


return P_GuildOutCreateType
