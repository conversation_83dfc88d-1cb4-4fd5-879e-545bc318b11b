local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

---@class ItemNml : UIComponent
---@field view ItemNmlBlueprint
local ItemNml = DefineClass("ItemNml", UIComponent)

local ESlateVisibility = import("ESlateVisibility")

ItemNml.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemNml:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemNml:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ItemNml:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ItemNml:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemNml:InitUIView()
end

---面板打开的时候触发
function ItemNml:OnRefresh(...)
	local param = select(1,...)
	if param and param.itemID then
		self:FillItem(param.itemID)
	end
	
	if param and param.RT then
		self:SetRT(param.RT, param.LvlUp)
	end
end

--- 通用物品逻辑
function ItemNml:FillItem(id, num, bind, bgQuality, bShowName, iconType, bShowUseLimit, state)
	if not state then
		state = Enum.ItemStatus.Normal
	end
	self:SetStatus(state)

	if not id then
		return
	end

	local ItemExcelData = Game.TableData.GetItemNewDataRow(id)
	if not ItemExcelData then
		return
	end

	--self.userWidget:SetNew(false)
	self:SetRB(false)

	-- 物品图标
	iconType = iconType and iconType or Enum.EUIIconType.Default
	local IconPath = Game.UIIconUtils.GetIconByItemId(id, iconType)
	if IconPath then
		self:SetImage(self.view.Icon_lua, IconPath)
	end

	-- 品质
	local Quality = ItemExcelData.quality
	if bgQuality then
		Quality = bgQuality
	end
	self.userWidget:SetQuality(Quality)
	if Quality > 4 then
		self.view.NS_HQ_lua:SetVisibility(ESlateVisibility.HitTestInvisible)
		self:OpenQualityComponent(Quality)
	else
		self.view.NS_HQ_Lua:SetVisibility(ESlateVisibility.Collapsed)
		self:CloseQualityComponent()
	end

	--- 数量
	--[[local Color = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
	if Color then
		self.view.TB_Text_lua:SetColorAndOpacity(Color)
	end]]

	if num then
		if type(num) == "number" then
			if num > 1 then
				self.view.TB_Text_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
				self.view.TB_Text_lua:SetText(Game.CurrencyUtils.GetGameMoneyFormat(num))
			elseif num == -1 then
				self.view.TB_Text_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
				self.view.TB_Text_lua:SetText("?")
			else
				self.view.TB_Text_lua:SetVisibility(ESlateVisibility.Collapsed)
			end
		elseif type(num) == "string" then
			self.view.TB_Text_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			self.view.TB_Text_lua:SetText(num)
		else
			self.view.TB_Text_lua:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		self.view.TB_Text_lua:SetVisibility(ESlateVisibility.Collapsed)
	end

	--- 物品名字
	if bShowName then
		self.view.TB_Name_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.view.TB_Name_lua:SetText(ItemExcelData.itemName)
	else
		self.view.TB_Name_lua:SetVisibility(ESlateVisibility.Collapsed)
	end

	--- 左上角图标：可交易/装备/不显示
	local LeftUpType = Enum.ItemLeftUpIconType.None
	if bind == false then
		LeftUpType = Enum.ItemLeftUpIconType.Trade
	end
	self.userWidget:SetLT(LeftUpType)

	--- 右上角图标
	self.userWidget:SetRT(Enum.RightUpIconType.None)
	--装备锁定
	self.userWidget:SetRB(false)
	--- 使用限制提示
	bShowUseLimit = bShowUseLimit == nil and true or bShowUseLimit
	if bShowUseLimit then
		local isUseLimit = Game.BagSystem:IsShowUseLimit(id)
		if isUseLimit then
			self:SetStatus(Enum.ItemStatus.UseLimit)
		end
	end
end

--不通过id来设置icon
function ItemNml:SetImageWithPath(path)
    self:SetImage(self.view.Icon_lua, path)
end

--- 数量显示的特殊规则1：显示0，=0 显示红色，>0 显示白色
function ItemNml:SetCount(Count)
	-- local Color
	if Count then
		-- if Count > 0 then
		-- 	Color = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
		-- else
		-- 	Color = Game.ColorManager:GetColor("Common", "Red", Game.ColorManager.Type.SlateColor)
		-- end
		if Count > 0 then
			Count = string.format("<Default>%d</>", Count)
		else
			Count = string.format("<CostRed>%d</>", Count)
		end
		self.view.TB_Text_lua:SetVisibility(ESlateVisibility.HitTestInvisible)
		self.view.TB_Text_lua:SetText(Count)
		-- self.view.TB_Text_lua:SetColorAndOpacity(Color)
	else
		self.view.TB_Text_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function ItemNml:RefreshPropItem(gbId, slot, equipId, propId, isSelect, cb, forbidClick)
	local BallId = Enum.EEquipConstData.ITEM_PROP_ITEM_ID
	local ItemExcelData = Game.TableData.GetItemNewDataRow(BallId)
	if not ItemExcelData then
		return
	end
	self:SetStatus(Enum.ItemStatus.Normal)
	self:SetLT(Enum.ItemLeftUpIconType.None)

	local IconPath = Game.UIIconUtils.GetIconByItemId(BallId)

	if IconPath then
		self:SetImage(self.view.Icon, IconPath)
	end

	-- local propData = Game.TableData.GetEquipWordAtkFixedWordDataRow(propId) or {}
	local equipData = Game.EquipmentSystem:GetEquipData(equipId)
	if not equipData or not equipData.quality then
		return
	end
	self.Quality = equipData.quality
	local RarityData = Game.TableData.GetRarityDataRow(self.Quality)
	if RarityData then
		local RarityColor =
		Game.ColorManager:GetColor("Common", RarityData.RarityColorName, Game.ColorManager.Type.SlateColor)
		self.view.Bg_Rarity_lua:SetBrushTintColor(RarityColor)
	end

	self.view.TB_Text_lua:SetVisibility(ESlateVisibility.Collapsed)
end

function ItemNml:OpenQualityComponent(quality)
	assert(quality, "quality is nil")
	if self.OpenQuality and self.OpenQuality ~= quality then
		self:CloseQualityComponent()
	end
	
	if not self.OpenQuality then
		self.OpenQuality = quality
		local qualityCell = string.format("ItemQuality%s", quality)
		self:OpenComponent(qualityCell, self.view.NS_HQ_lua)
	end
end

function ItemNml:CloseQualityComponent()
	--assert(quality == nil or quality == self.OpenQuality, "quality is not equal")
	if self.OpenQuality then
		local qualityCell = string.format("ItemQuality%s", self.OpenQuality)
		self:CloseComponent(qualityCell, true)
		self.OpenQuality = nil
	end
end

function ItemNml:SetCD(cd, leftTime)
	local timeText = math.max(leftTime//1000, 1)
	self.view.text_center_lua:SetText(timeText)
	self.view.BG_CD_lua:GetDynamicMaterial():SetScalarParameterValue("CD", leftTime / (cd*1000) )
end

---@param ltType Enum.ItemLeftUpIconType 设置左上角的角标样式
function ItemNml:SetLT(ltType)
	self.userWidget:SetLT(ltType)
end

---@param rtType Enum.RightUpIconType 设置右上角的角标样式
function ItemNml:SetRT(rtType, info)
	self.userWidget:SetRT(rtType)
	--- 过期
	if rtType == Enum.RightUpIconType.Expired then
		self.view.Text_tag_lua:SetText(StringConst.Get("ITEM_EXPIRED"))
	elseif rtType ==Enum.RightUpIconType.LimitedTime then
		self.view.Text_tag_lua:SetText(StringConst.Get("ITEM_LIMIT_TIME"))
	elseif rtType == Enum.RightUpIconType.Left_24_Hours then
		self.view.Text_tag_lua:SetText(StringConst.Get("ITEM_24HOUR"))
	end

	if rtType == Enum.RightUpIconType.EnhanceLevel and info and info > 0 then
		self.userWidget:SetRT(Enum.RightUpIconType.EnhanceLevel)
		self.view.Text_LvUp:SetText("+"..info)
	else
		self.view.Text_LvUp:SetText("")
	end
end

function ItemNml:SetRTTagIcon(icon)
	self:SetImage(self.view.Img_IconTag_RT, icon)
end

function ItemNml:SetLBIcon(icon)
	self.view.Img_Icon_LB:SetVisibility(ESlateVisibility.HitTestInvisible)
	self:SetImage(self.view.Img_Icon_LB, icon)
end

function ItemNml:SetRBIcon(icon)
    self.view.Img_Icon_RB:SetVisibility(ESlateVisibility.HitTestInvisible)
	self:SetImage(self.view.Img_Icon_RB, icon)
end

---@param statusType Enum.ItemStatus 设置格子的状态
function ItemNml:SetStatus(statusType)
	self.userWidget:SetStatus(statusType)
	if statusType == Enum.ItemStatus.Received then
		self.view.text_center_lua:SetText(StringConst.Get("ITEM_GOT"))
	end
end

---@param rbType bool 设置右下角角标的样式 
function ItemNml:SetRB(rbType)
	self.userWidget:SetRB(rbType)
end

function ItemNml:SetIconOpacity(opacity)
	self.view.Icon_lua:SetOpacity(opacity)
end

---@public 设置自定义名字
function ItemNml:SetName(name)
	self.view.TB_Name_lua:SetText(name)
	self:SetWidgetVisible(self.view.TB_Name_lua, not string.isEmpty(name))
end
return ItemNml
