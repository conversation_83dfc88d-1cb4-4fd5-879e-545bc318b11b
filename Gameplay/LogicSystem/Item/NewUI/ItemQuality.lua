
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ItemQuality : UIComponent
local ItemQuality = DefineClass("ItemQuality", UIComponent)

local FAnchors = import("Anchors")
local Margin = import("Margin")

function ItemQuality:ResetTransform()
	self.userWidget.Slot:SetAlignment(FVector2D(0.5, 0.5))
	local newAnchors = FAnchors()
	newAnchors.Maximum = FVector2D(1.0, 1.0)
	newAnchors.Minimum = FVector2D(0, 0)
	self.userWidget.Slot:SetAnchors(newAnchors)
	self.userWidget.Slot:SetOffSets(Margin(0,0,0,0))
end

return ItemQuality