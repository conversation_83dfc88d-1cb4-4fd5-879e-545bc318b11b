-- 后续不在维护，新框架请使用ItemNml
local BasicItem = DefineClass("BasicItem", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local WidgetEmptyComponent = kg_require("Framework.UI.WidgetEmptyComponent")

function BasicItem:OnCreate() -- luacheck: ignore
end

function BasicItem:OnClose()
    UIBase.OnClose(self)
    self:PushItemComponent()
end

function BasicItem:OnRefresh()
end

--- 通用物品逻辑
function BasicItem:FillItem(id, num, bind, bgQuality, bShowName, iconType, bShowUseLimit, state)
    if not state then
        state = Enum.ItemStatus.Normal
    end
    self:SetStatus(state)

    if not id then
        return
    end

    local ItemExcelData = Game.TableData.GetItemNewDataRow(id)
    if not ItemExcelData then
        return
    end
	
	self:SetRB(false)

    -- 物品图标
    iconType = iconType and iconType or Enum.EUIIconType.Default
    local IconPath = Game.UIIconUtils.GetIconByItemId(id, iconType)
    if IconPath then
        self:SetImage(self.View.Icon, IconPath)
    end

    -- 品质
    local Quality = ItemExcelData.quality
    if bgQuality then
        Quality = bgQuality
    end
    self.View:SetQuality(Quality)
    if Quality >= 5 then
        self.View.NS_HQ:SetVisibility(ESlateVisibility.HitTestInvisible)
        self:GetItemComponent(Quality)
    else
        self.View.NS_HQ:SetVisibility(ESlateVisibility.Collapsed)
        self:PushItemComponent(Quality)
    end

    --- 数量
    -- local Color = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
    -- if Color then
    --     self.View.TB_Text:SetColorAndOpacity(Color)
    -- end
    
    if num then
        if type(num) == "number" then
            if num > 1 then
                self.View.TB_Text:SetVisibility(ESlateVisibility.selfHitTestInvisible)
                self.View.TB_Text:SetText(Game.CurrencyUtils.GetGameMoneyFormat(num))
            elseif num == -1 then
                self.View.TB_Text:SetVisibility(ESlateVisibility.selfHitTestInvisible)
                self.View.TB_Text:SetText("?")
            else
                self.View.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
            end
        elseif type(num) == "string" then
            self.View.TB_Text:SetVisibility(ESlateVisibility.selfHitTestInvisible)
            self.View.TB_Text:SetText(num)
        else
            self.View.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
        end
    else
        self.View.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
    end

    --- 物品名字
    if bShowName then
        self.View.TB_Name:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.View.TB_Name:SetText(ItemExcelData.itemName)
    else
        self.View.TB_Name:SetVisibility(ESlateVisibility.Collapsed)
    end

    --- 左上角图标：可交易/装备/不显示
    local LeftUpType = Enum.ItemLeftUpIconType.None
    if bind == false then
        LeftUpType = Enum.ItemLeftUpIconType.Trade
    end
    self:SetLT(LeftUpType)

	--- 右上角图标
	self:SetRT(Enum.RightUpIconType.None)
	
    --- 使用限制提示
    bShowUseLimit = bShowUseLimit or false
    if bShowUseLimit then
        local isUseLimit = Game.BagSystem:IsShowUseLimit(id)
        if isUseLimit then
            self:SetStatus(Enum.ItemStatus.UseLimit)
        end
    end
end

--不通过id来设置icon
function BasicItem:SetImageWithPath(path)
	self:SetImage(self.View.Icon, path)
end

--- 数量显示的特殊规则1：显示0，=0 显示红色，>0 显示白色
function BasicItem:SetCount(Count)
    -- local Color
    if Count then
        -- if Count > 0 then
        --     Color = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
        -- else
        --     Color = Game.ColorManager:GetColor("Common", "Red", Game.ColorManager.Type.SlateColor)
        -- end
        if Count > 0 then
			Count = string.format("<Default>%d</>", Count)
		else
			Count = string.format("<CostRed>%d</>", Count)
		end
        self.View.TB_Text:SetVisibility(ESlateVisibility.HitTestInvisible)
        self.View.TB_Text:SetText(Count)
        -- self.View.TB_Text:SetColorAndOpacity(Color)
    else
        self.View.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function BasicItem:RefreshPropItem(gbId, slot, equipId, propId, isSelect, cb, forbidClick)
    local BallId = Enum.EEquipConstData.ITEM_PROP_ITEM_ID
    local ItemExcelData = Game.TableData.GetItemNewDataRow(BallId)
    if not ItemExcelData then
        return
    end
    self:SetStatus(Enum.ItemStatus.Normal)
    self:SetLT(Enum.ItemLeftUpIconType.None)
    
    local IconPath = Game.UIIconUtils.GetIconByItemId(BallId)
    
    if IconPath then
        self:SetImage(self.View.Icon, IconPath)
    end
	
    -- local propData = Game.TableData.GetEquipWordAtkFixedWordDataRow(propId) or {}
    local equipData = Game.EquipmentSystem:GetEquipData(equipId)
    if not equipData or not equipData.quality then
        return
    end
    self.Quality = equipData.quality
    local RarityData = Game.TableData.GetRarityDataRow(self.Quality)
    if RarityData then
        local RarityColor =
           Game.ColorManager:GetColor("Common", RarityData.RarityColorName, Game.ColorManager.Type.SlateColor)
        self.View.Bg_Rarity:SetBrushTintColor(RarityColor)
    end

    self.View.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
end

function BasicItem:GetItemComponent(quality)
    if not quality then
        return 
    end
    if not self.HQ then
        self.HQ = self:FormComponent(string.format("%s%s","WBP_ItemQuality", quality), self.View.NS_HQ, WidgetEmptyComponent)
    end
    return self.HQ
end

function BasicItem:PushItemComponent()
    if self.HQ then
        self:PushContainerComponent(self.View.NS_HQ,self.HQ)
        self.HQ = nil
    end
end

function BasicItem:SetCD(cd, leftTime)
    local timeText = math.max(leftTime//1000, 1)
    self.View.text_center:SetText(timeText)
    self.View.BG_CD:GetDynamicMaterial():SetScalarParameterValue("CD", leftTime / (cd*1000) )
end

---@param ltType Enum.ItemLeftUpIconType 设置左上角的角标样式
function BasicItem:SetLT(ltType)
	self.View:SetLT(ltType)
end

---@param rtType Enum.RightUpIconType 设置右上角的角标样式
function BasicItem:SetRT(rtType)
	self.View:SetRT(rtType)
	--- 过期
	if rtType == Enum.RightUpIconType.Expired then
		self.View.Text_tag:SetText("过期")
	elseif rtType ==Enum.RightUpIconType.LimitedTime then
		self.View.Text_tag:SetText("限时")
	elseif rtType == Enum.RightUpIconType.Left_24_Hours then
		self.View.Text_tag:SetText("24时")
	end
end

---@param statusType Enum.ItemStatus 设置格子的状态
function BasicItem:SetStatus(statusType)
	self.View:SetStatus(statusType)
	if statusType == Enum.ItemStatus.Received then
		self.View.text_center:SetText("已领取")
	end
end

---@param rbType bool 设置右下角角标的样式 
function BasicItem:SetRB(rbType)
	self.View:SetRB(rbType)
end

return BasicItem