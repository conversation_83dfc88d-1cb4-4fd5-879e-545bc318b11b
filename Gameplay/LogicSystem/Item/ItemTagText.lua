local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ItemTagText : UIListItem
---@field view ItemTagTextBlueprint
local ItemTagText = DefineClass("ItemTagText", UIListItem)

ItemTagText.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemTagText:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemTagText:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ItemTagText:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ItemTagText:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemTagText:InitUIView()
end

---面板打开的时候触发
function ItemTagText:OnRefresh(name)
	self.RTB_TagName_lua:SetText(name)
end

return ItemTagText
