local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---@class ItemSmallParam
---@field itemId number 物品ID
---@field gbId number 物品GBID
---@field showCount boolean|string|number 数量 nil/true 显示背包里的数量，传进来是具体的数量就显示传进来的数量，false就不显示数量
---@field isGet boolean 是否获取

--- WBP_ItemSmall
------@class ItemSmall:UIListItem
local ItemSmall = DefineClass("ItemSmall", UIListItem)

---@param data ItemSmallParam
function ItemSmall:OnRefresh(data)
    self:FillItem(data.itemId, data.gbId, data.showCount, data.isGet)
end

---@param showCount 数量 nil/true 显示背包里的数量，传进来是具体的数量就显示传进来的数量，false就不显示数量
function ItemSmall:FillItem(itemId, gbId, showCount, isGet)
    local itemExcelData = Game.TableData.GetItemNewDataRow(itemId)
    if not itemExcelData then
        return
    end
    self.userWidget:SetQuality(itemExcelData.quality)
    local IconPath = Game.UIIconUtils.getIcon(itemExcelData.icon) or ''
    if IconPath then
        self:SetImage(self.view.icon_lua, IconPath)
    end
    
    if itemExcelData.type == Game.BagSystem.ItemType.EQUIPMENT and gbId then
        local slotInfo = Game.BagSystem:GetItemInfoWithGbId(gbId)
        local equipSlotIndex = Game.EquipmentSystem:GetBetterSlotByInfo(slotInfo)
        if equipSlotIndex then
            self.userWidget:SetScoreUp(true)
        else
            self.userWidget:SetScoreUp(false)
        end
    else
        self.userWidget:SetScoreUp(false)
    end

    --- nil/true 显示背包里的数量
    if showCount == nil or showCount == true then 
        local count = Game.BagSystem:GetItemCount(itemId)
        self.userWidget:SetShowNum(count > 1)
        self.view.Num_lua:SetText(count)
    --- 传进来是具体的数量就显示传进来的数量
    elseif type(showCount) == "string" or type(showCount) == "number" then
        self.userWidget:SetShowNum(true)
        self.view.Num_lua:SetText(showCount)
    --- false 不显示数量
    elseif showCount == false then
        self.userWidget:SetShowNum(false)
    end

    if isGet then
        self.userWidget:SetGet(isGet)
    else
        self.userWidget:SetGet(false)
    end
end

return ItemSmall