local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---@class ItemTipsFashionTagItem : UIListItem
---@field view ItemTipsBtnBlueprint
local ItemTipsFashionTagItem = DefineClass("ItemTipsFashionTagItem", UIListItem)

ItemTipsFashionTagItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemTipsFashionTagItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemTipsFashionTagItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ItemTipsFashionTagItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ItemTipsFashionTagItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemTipsFashionTagItem:InitUIView()
end

---面板打开的时候触发
---@param param ItemTipsBtn_Param
function ItemTipsFashionTagItem:OnRefresh(param)
	self.view.Text_Fashion:SetText(param.Name or "")
	self.userWidget:Event_UI_Style(param.Color or 0)
end


--- 此处为自动生成
function ItemTipsFashionTagItem:on_Button_lua_Clicked()

end

return ItemTipsFashionTagItem
