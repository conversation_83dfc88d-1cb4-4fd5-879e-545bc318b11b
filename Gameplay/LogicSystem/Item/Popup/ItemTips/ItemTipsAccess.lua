local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local ESlateVisibility = import("ESlateVisibility")

---@class ItemTipsAccess : UIListItem
---@field view ItemTipsAccessBlueprint
local ItemTipsAccess = DefineClass("ItemTipsAccess", UIListItem)

ItemTipsAccess.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemTipsAccess:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemTipsAccess:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ItemTipsAccess:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ItemTipsAccess:InitUIEvent()
    self:AddUIEvent(self.view.Button.OnClicked, "on_Button_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemTipsAccess:InitUIView()
end

---面板打开的时候触发
function ItemTipsAccess:OnRefresh(...)
end

---@public 
function ItemTipsAccess:SetState(state)
	if state >= 0 then
		self:Show(ESlateVisibility.Visible)
		self.userWidget:SetState(state)
	else
		self:Hide(ESlateVisibility.Collapsed)
	end
end


--- 此处为自动生成
function ItemTipsAccess:on_Button_Clicked()
	self:GetParent():GetParent():OnClickTipsAccessPanel()
end

return ItemTipsAccess
