local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local DescFormulaHelper = kg_require("Gameplay.LogicSystem.SkillCustomizer.DescFormulaHelper")
local SystemTipsTitle = kg_require("Gameplay.LogicSystem.Tips.SystemTips.SystemTipsTitle")
---@class ItemTipsText : UIListItem
---@field view ItemTipsTextBlueprint
local ItemTipsText = DefineClass("ItemTipsText", UIListItem)

ItemTipsText.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemTipsText:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemTipsText:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ItemTipsText:InitUIComponent()
    self.ContentList = self:CreateComponent(self.view.WBP_SystemTipsTitle, SystemTipsTitle)
end

---UI事件在这里注册，此处为自动生成
function ItemTipsText:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemTipsText:InitUIView()
end

---面板打开的时候触发
function ItemTipsText:OnRefresh(info, bRegularization)
    self.ContentList:Refresh(info and info.Title)
    local str = ""
    for key, value in pairs(info.Content) do
        str = str .. value .. "\n"
    end
    str = string.sub(str, 0, string.len(str) - 1)

    if not bRegularization then
        self.view.Text_TipsContent:SetText(str)
    else
        local regstr = DescFormulaHelper.GenerateTipsDesc(str)
        self.view.Text_TipsContent:SetText(regstr)
    end
end

return ItemTipsText
