local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ItemTipsWeapon = kg_require("Gameplay.LogicSystem.Item.Popup.ItemTips.ItemTipsWeapon")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local ESlateVisibility = import("ESlateVisibility")

---@class ItemTipsTitleTagParam
---@field Name string 标签文字
---@field Color FColorValue 标签颜色

---@class ItemTipsScoreParam
---@field ScorePanelVis ESlateVisibility
---@field TBEquipTypeTitle string 标题
---@field TBEquipmentScore string 评分
---@field TBSlotScore string 槽位评分
---@field ScoreArrowPanelShow boolean
---@field TBEquipmentScoreUpShow boolean 
---@field TBEquipmentScoreDownShow boolean

---@class ItemTipsTitle_Param
---@field TextName string 名字
---@field TextNum string 数量显示
---@field TextHand string 功能描述
---@field TextJob string 职业说明
---@field TextJobColor FColorValue 职业颜色
---@field TextLevel string 等级说明
---@field TextLevelColor FClolorValue 等级颜色
---@field ShowEquip boolean 是否显示装备标记
---@field ShowImgFunc boolean 是否显示ImageFunc，这块暂时不生效，宿主直接调用
---@field WeaponIcon string 武器icon
---@field TagList ItemTipsTitleTagParam[] 标签列表
---@field ScoreParam ItemTipsScoreParam 评分参数

---@class ItemTipsTitle : UIListItem
---@field view ItemTipsTitleBlueprint
local ItemTipsTitle = DefineClass("ItemTipsTitle", UIListItem)

ItemTipsTitle.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemTipsTitle:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemTipsTitle:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ItemTipsTitle:InitUIComponent()
    ---@type UIListView
    self.ListTagCom = self:CreateComponent(self.view.ListTag, UIListView)
    ---@type ItemTipsWeapon
    self.WBP_ComWeaponECom = self:CreateComponent(self.view.WBP_ComWeaponE, ItemTipsWeapon)
end

---UI事件在这里注册，此处为自动生成
function ItemTipsTitle:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemTipsTitle:InitUIView()
end

---面板打开的时候触发
---@param param ItemTipsTitle_Param
function ItemTipsTitle:OnRefresh(param)
	param = param.tabData or param

	self:RefreshScore(param.ScoreParam)

	self.WBP_ComWeaponECom:SetIcon(param.WeaponIcon)

	self:SetWidgetText(self.view.Text_Name, param.TextName)
	self:SetWidgetText(self.view.Text_Num, param.TextNum)
	self:SetWidgetText(self.view.Text_Hand, param.TextHand)
	self:SetWidgetText(self.view.Text_Level, param.TextLevel, param.TextLevelColor)

	if param.TagList then
		self.ListTagCom:Refresh(param.TagList)
	end
	
	self.view.Equipped:SetVisibility(param.ShowEquip and ESlateVisibility.Visible or ESlateVisibility.Hidden)
	self:SetIconState(0)
end

---@public 设置RT显示类别
---@param flag number
function ItemTipsTitle:SetRT(flag)
	self.userWidget:SetRT(flag)
end

---@public 设置ImageFunc显示
---@param show boolean
function ItemTipsTitle:SetImageFuncShow(show)
	self.view.Image_Func:SetVisibility(show and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Hidden)
end

---@private 设置widget的文本，并且会设置显影
---@param widget UUserWidget
---@param text string
---@param color FColorValue|nil
function ItemTipsTitle:SetWidgetText(widget, text, color)
	local show = text ~= nil
	widget:SetVisibility(show and ESlateVisibility.Visible or ESlateVisibility.Hidden)
	if show then
		widget:SetText(text)
	end
	if color then
		widget:SetColorAndOpacity(color)
	end
end

function ItemTipsTitle:SetIconState(state)
	self.WBP_ComWeaponECom:SetIconState(state)
end

---@private 刷新评分
---@param param ItemTipsScoreParam
function ItemTipsTitle:RefreshScore(param)
	self.view.ScorePanel:SetVisibility(param and param.ScorePanelVis or ESlateVisibility.Collapsed)
	if not param then return end
	
	self:SetWidgetText(self.view.TB_EquipTypeTitle, param.TBEquipTypeTitle)
	self:SetWidgetText(self.view.TB_Equipment_Score, param.TBEquipmentScore)
	self:SetWidgetText(self.view.TB_Slot_Score, param.TBSlotScore)

	self.view.ScoreArrowPanel:SetVisibility(param.ScoreArrowPanelShow and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
	self.view.TB_Equipment_Score_up:SetVisibility(param.TBEquipmentScoreUpShow and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
	self.view.TB_Equipment_Score_down:SetVisibility(param.TBEquipmentScoreDownShow and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end


--- 此处为自动生成
function ItemTipsTitle:on_Btn_ClickArea_Clicked()
end

return ItemTipsTitle
