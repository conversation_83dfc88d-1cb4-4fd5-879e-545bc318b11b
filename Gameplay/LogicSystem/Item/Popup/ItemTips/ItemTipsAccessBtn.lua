local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local ESlateVisibility = import("ESlateVisibility")

---@class ItemTipsAccessBtn_Param
---@field JumpBasicData AchievePathData 跳转基础数据
---@field JumpExtraData number[] 跳转额外数据，道具列表

---@class ItemTipsAccessBtn : UIListItem
---@field view ItemTipsAccessBtnBlueprint
local ItemTipsAccessBtn = DefineClass("ItemTipsAccessBtn", UIListItem)

ItemTipsAccessBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemTipsAccessBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemTipsAccessBtn:InitUIData()
	---@type ItemTipsAccessBtn_Param
	self.Param = nil
end

--- UI组件初始化，此处为自动生成
function ItemTipsAccessBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ItemTipsAccessBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemTipsAccessBtn:InitUIView()
end

---面板打开的时候触发
---@param param ItemTipsAccessBtn_Param 打开面板的参数
function ItemTipsAccessBtn:OnRefresh(param)
	self.Param = param
	
	local achieveBasicData = param.JumpBasicData

	self.view.Text_Com:SetText(achieveBasicData.AchievePathName) -- better use  StringConst.Get("XXX")
	if achieveBasicData.AchievePathIcon then
		self.view.Icon_Access:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self:SetImage(self.view.Icon_Access, achieveBasicData.AchievePathIcon)
	else
		self.view.Icon_Access:SetVisibility(ESlateVisibility.Collapsed)
	end
end


--- 此处为自动生成
function ItemTipsAccessBtn:on_Btn_ClickArea_Clicked()
	local achieveData = self.Param.JumpBasicData
	Game.UIJumpSystem:JumpToUI(achieveData.UIJumpId, nil, self.Param.JumpExtraData)
	
	self:GetBelongPanel():CloseSelf()
end

return ItemTipsAccessBtn
