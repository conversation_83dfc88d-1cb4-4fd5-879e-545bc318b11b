local LibText = kg_require("Gameplay.LogicSystem.Lib.LibText")
local StringConst = kg_require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@class ItemTipsEquipSpecial : UIComponent
---@field view ItemTipsEquipSpecialBlueprint
local ItemTipsEquipSpecial = DefineClass("ItemTipsEquipSpecial", UIComponent)

ItemTipsEquipSpecial.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ItemTipsEquipSpecial:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ItemTipsEquipSpecial:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ItemTipsEquipSpecial:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ItemTipsEquipSpecial:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ItemTipsEquipSpecial:InitUIView()
end

---面板打开的时候触发
function ItemTipsEquipSpecial:OnRefresh(...)
end

---@public
---@param equipInfo INV_SLOT_VAL
---@param equipData _ItemNewDataRow
function ItemTipsEquipSpecial:SetData(equipInfo, equipData)
    if equipData == nil or equipData == nil  then
        return
    end
    
    local uniqueData = Game.TableData.GetEquipmentUniqueDataRow(equipData.UniqueID)
    --local uniqueData = Game.TableData.GetEquipmentUniqueDataRow(1)

    self.view.Text_Name:SetText(equipData.itemName)
    self.view.Text_Detail:SetText(uniqueData.SuitDesc1 .. "\n" .. uniqueData.SuitDesc2)
    self.view.Text_Story:SetText(uniqueData.SuitStory)
    
end


--- 此处为自动生成
function ItemTipsEquipSpecial:on_Button_Clicked()
	
end

return ItemTipsEquipSpecial
