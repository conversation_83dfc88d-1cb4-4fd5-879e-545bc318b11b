local FirstDropTipsItem = kg_require("Gameplay.LogicSystem.Item.Popup.FirstDropTipsItem")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class FirstGetItemTips_Panel : UIPanel
---@field view FirstGetItemTips_PanelBlueprint
local FirstGetItemTips_Panel = DefineClass("FirstGetItemTips_Panel", UIPanel)

FirstGetItemTips_Panel.eventBindMap = {
    [EEventTypesV2.ON_INPUT_UI_SHORTCUT] = "OnInputUIShortcut",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FirstGetItemTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FirstGetItemTips_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FirstGetItemTips_Panel:InitUIComponent()
    ---@type FirstDropTipsItem
    self.WBP_FirstDropTipsItemCom = self:CreateComponent(self.view.WBP_FirstDropTipsItem, FirstDropTipsItem)
end

---UI事件在这里注册，此处为自动生成
function FirstGetItemTips_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FirstGetItemTips_Panel:InitUIView()
end

---面板打开的时候触发
---@param gbId string 物品global id
function FirstGetItemTips_Panel:OnRefresh(gbId)
    if not gbId then
        self:CloseUI()
        return
    end
    
    local itemId = Game.BagSystem:GetItemIdByGbId(gbId)
    local itemExcelData = itemId and Game.TableData.GetItemNewDataRow(itemId)
    if not itemExcelData then
        self:CloseUI()
        return
    end

    if not itemExcelData.tipFirstDrop then
        self:CloseUI()
        return
    end
    
    self.WBP_FirstDropTipsItemCom:Refresh(gbId)

    ---刷新时间
    self:StartTimer("FirstGetItemTimer", function()
        Game.ItemSystem:RefreshFirstGetItemQueue()
    end, Game.TableData.GetConstDataRow("TIP_FIRST_DROP_TIME") * 1000, 1, false, false)
end

function FirstGetItemTips_Panel:CloseUI()
    self:CloseSelf()
end

function FirstGetItemTips_Panel:OnClickToQuickPick()
    self.WBP_FirstDropTipsItemCom:OnClickToQuickPick()
end

function FirstGetItemTips_Panel:OnInputUIShortcut(actionName, keyActionCfg, actionTypeID)
    if actionName == "QuickPick_Action" then
        self:OnClickToQuickPick()
    end
end

return FirstGetItemTips_Panel
