---@class NPCMoodSystem:SystemBase
local NPCMoodSystem = DefineClass("NPCMoodSystem", SystemBase)

function NPCMoodSystem:onInit()
    self.model = kg_require("Gameplay.LogicSystem.NPC.CutPrice.NPCMoodModel").new(false,true)
    self.sender = kg_require("Gameplay.LogicSystem.NPC.CutPrice.NPCMoodSender").new()

    self.SoulList = {"AngrySoul","UnhappySoul","UpsetSoul","CalmSoul","PeaceSoul","HappySoul","ExcitedSoul"} -- mood rank
    self.FaceList = {"AngryFace","CalmFace","PeaceFace","HappyFace"}
    self.tmpQuote = nil
    self.tmpMaxPrice = nil
    self.entityID = nil
    self.finalPrice = nil
    self.itemName = nil
    -- Game.EventSystem:AddListener(_G.EEventTypes.GAME_MAINPALYER_LOGIN, self, self.AddRedPoint)
end

function NPCMoodSystem:onUnInit()
    Game.EventSystem:RemoveObjListeners(self)
end

function NPCMoodSystem:ResetData()
    self.tmpQuote = nil
    self.tmpMaxPrice = nil
    self.entityID = nil
    self.finalPrice = nil
    self.itemName = nil
end

function NPCMoodSystem:ShowMoodUI(EntityID)
    local npcEntity = Game.EntityManager:getEntity(EntityID)
    if not npcEntity then
        return
    end
    local npcData = Game.TableData.GetNpcInfoDataRow(npcEntity.TemplateID)
	if npcData.InitialMood then
		self.sender:ReqSetNpcMood(EntityID, npcData.InitialMood)
	else
		Log.WarningFormat("NPC InitialMood Not Exist, EntityID: ", EntityID)
	end
    
	Game.NewUIManager:OpenPanel(UIPanelConfig.NPCMoodPanel, EntityID)
    self:ResetData()
    self.entityID = EntityID
end

function NPCMoodSystem:CloseMoodUI()
	Game.NewUIManager:ClosePanel(UIPanelConfig.NPCMoodPanel)
    self.entityID = nil
end

function NPCMoodSystem:ShowCutPriceUI(EntityID)
	local npcEntity = Game.EntityManager:getEntity(EntityID)
	if not npcEntity then
		return
	end
	Game.NewUIManager:OpenPanel(UIPanelConfig.NPCBargainPanel, EntityID)
    self:ResetData()
    self.entityID = EntityID

	self:ShowMoodUI(EntityID) -- 默认展示问价界面的同时也展示心情，如果策划说要加需求分步删再删吧
end

function NPCMoodSystem:CloseCutPriceUI()
	Game.NewUIManager:ClosePanel(UIPanelConfig.NPCBargainPanel)
    self.entityID = nil
	
	self:CloseMoodUI()
end

---获取某等级下对应的心情的ID，表格里的主键文本
---@return string
function NPCMoodSystem:GetMood(moodLevel)
    return self.SoulList[moodLevel]
end

---获取某耐心值对应的心情等级
---@return number 范围 [1, 7]
function NPCMoodSystem:GetMoodLevel(moodValue)
	if moodValue then
		local level = #self.SoulList
		for i = 1,#self.SoulList do
			local upRange = Game.TableData.GetMoodSettingDataRow(self.SoulList[i])
			if moodValue < upRange then
				level = i
				return level
			end
		end
	end
    return #self.SoulList	
end

---获取某等级下对应的面部表情
function NPCMoodSystem:GetFace(faceLevel)
	return self.FaceList[faceLevel]
end

---获取某耐心值对应的面部表情等级
---@param value number 耐心值
function NPCMoodSystem:GetFaceLevel(value)
	if value then
		for i = 1,#self.FaceList do
			local upRange = Game.TableData.GetCutPriceSettingDataRow(self.FaceList[i])
			if value < upRange then
				return i
			end
		end
	end
    
    return #self.FaceList
end

---完成砍价
---@param info CutPriceInfo 砍价信息
---@param quote number 报价
---@param itemName string 交易物品名称
---@param bDeal boolean 是否结算
function NPCMoodSystem:FinishCutPrice(info, quote, itemName, bDeal)
    self.tmpQuote = quote
    self.finalPrice = quote
    self.itemName = itemName
    Game.DialogueManager:OnCutPriceFinish(info)
	self:CloseCutPriceUI()
	if bDeal then
		self:DealProcess(quote)
	end
end

---更新砍价
---@param info CutPriceInfo 砍价信息
function NPCMoodSystem:UpdateCutPrice(info)	
    Game.DialogueManager:OnCutPriceUpdate(info)
end

---开始讨价还价
---@param quote number 初始报价
---@param maxPrice number 最高报价
---@param itemName number 交易物品名称
function NPCMoodSystem:StartBargainProcess(quote, maxPrice, itemName)
    self.tmpQuote = quote
    self.tmpMaxPrice = maxPrice
    self.itemName = itemName
    Game.DialogueManager:StartBargainProcess()
end

---讨价还价结束
---@param result boolean 是否成功，成功按报价交易，失败随机报价
function NPCMoodSystem:OnBargainProcessEnd(result)
    if result then
        self.finalPrice = self.tmpQuote
        self.tmpQuote = nil
        self.tmpMaxPrice = nil
    else
        local randomSeed = _G._now()
        math.randomseed(randomSeed)  
        local finalPrice = math.random(self.tmpQuote,self.tmpMaxPrice)
        self.finalPrice = finalPrice
    end
	self:DealProcess(self.finalPrice)
end

---处理交易
---@param price number 交易价格
---@param itemName string 交易物品名称
function NPCMoodSystem:DealProcess(price, itemName)
	local dealPrice = price or self.finalPrice
    local dealItemName = itemName or self.itemName
    Game.ItemSubmitSystem:ShowItemSubmitUI({ ItemSubmitID = 6250082,
											 TypeID = Game.ItemSubmitSystem.ESubmitType.SUBMIT_NEGOTIATION,
											 RPCType = Game.ItemSubmitSystem.ERPCType.RPC_CUTPRICE, 
											 TargetEID = self.entityID,
											 NumOverride = dealPrice})

    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CUTPRICE_END, { { tostring(dealPrice) , dealItemName } })
end

---根据心情值，计算交易价格，最后处理交易
---@param moodValue number 心情值
function NPCMoodSystem:OnMoodSubmitPrice(moodValue)
    local npcEntity = Game.EntityManager:getEntity(self.NpcEID)
    if not npcEntity or not npcEntity.TemplateID then
		Log.WarningFormat("NPC Not Exist, NPC: %s", self.npcEID)
        return
    end
    local cutPriceData = Game.TableData.GetCutPriceDataRow(npcEntity.TemplateID)
    if not cutPriceData then
        Log.WarningFormat("NPC has no CutPrice config! NPC: %s", self.npcEID)
        return 
    end
    local moodLevel = self:GetMoodLevel(moodValue)
    local unAcceptPriceMin = cutPriceData.Unacceptable[1] or 0
    local unAcceptPriceMax = cutPriceData.Unacceptable[2] or 0

    local highPriceMin = cutPriceData.HightPrice[1] or 0
    local highPriceMax = cutPriceData.HightPrice[2] or 0
    local lowestPrice = cutPriceData.LowestPrice
    local initPrice = cutPriceData.InitPrice

    if npcEntity.CutPriceInfo then
        if npcEntity.CutPriceInfo.InitPatienceValue then
            self.Patience = npcEntity.CutPriceInfo.InitPatienceValue
        end

        if npcEntity.CutPriceInfo.InitPrice then
            local newPrice = npcEntity.CutPriceInfo.InitPrice
            local scale = newPrice/self.initPrice
            unAcceptPriceMin = self.unAcceptPriceMin * scale
            unAcceptPriceMax = self.unAcceptPriceMax * scale
            highPriceMin = self.highPriceMin * scale
            highPriceMax = self.highPriceMax * scale
            initPrice = newPrice
        end

        if npcEntity.CutPriceInfo.LowestPrice then
            lowestPrice = npcEntity.CutPriceInfo.LowestPrice
        end
    end
    local finalPrice 
    if moodLevel <= 2 then
        finalPrice = highPriceMax
    elseif moodLevel == 2 then
        finalPrice = highPriceMin
    elseif moodLevel > 2 and moodLevel < 5 then
        finalPrice = initPrice
    elseif moodLevel == 5 then
        finalPrice = unAcceptPriceMax
    elseif moodLevel == 6 then
        finalPrice = unAcceptPriceMin
    else
        finalPrice = lowestPrice
    end
    self.finalPrice = finalPrice
    self:DealProcess(finalPrice)
end

---改变某个NPC心情
function NPCMoodSystem:AddMood(entityID, addMood)
    self.sender:ReqAddNpcMood(entityID, addMood)
end

---退出砍价
function NPCMoodSystem:QuitCutPrice()
    Game.DialogueManager:OnQuitCutPrice()
	self:CloseCutPriceUI()
end

---GM，切换砍价时心情显示
function NPCMoodSystem:SwitchCutPriceShowMood(bShow)
    self.gmShowCutPriceMood = bShow
end

return NPCMoodSystem