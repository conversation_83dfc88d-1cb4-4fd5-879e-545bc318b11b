local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class NPCMoodPanel : UIPanel
---@field view NPCBMoodPanelBlueprint
local NPCMoodPanel = DefineClass("NPCMoodPanel", UIPanel)

local StringConst = kg_require("Data.Config.StringConst.StringConst")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

NPCMoodPanel.eventBindMap = {
	[EEventTypesV2.ON_NPC_MOOD_CHANGE] = "OnPrepareMoodChange",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function NPCMoodPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function NPCMoodPanel:InitUIData()
	---@type number - 对话NPC实例ID
	self.npcEID = nil 
	---@type number - 最近一次心情值
	self.nearestMoodValue = 0
	---@type number - 最近一次心情等级
	self.nearestMoodLevel = 0
	---@type table  - 切换动效
	self.AniInTable = {
		[1] = self.userWidget.Ani_in_0Black,
		[2] = self.userWidget.Ani_in_1White,
		[3] = self.userWidget.Ani_in_2Blue,
		[4] = self.userWidget.Ani_in_3Green,
		[5] = self.userWidget.Ani_in_4Yellow,
		[6] = self.userWidget.Ani_in_5Orange,
		[7] = self.userWidget.Ani_in_6Red,
	}
	---@type table  - 循环动效
	self.AniLoopTable = {
		[1] = self.userWidget.Ani_in_0Black_Loop,
		[2] = self.userWidget.Ani_in_1White_Loop,
		[3] = self.userWidget.Ani_in_2Blue_Loop,
		[4] = self.userWidget.Ani_in_3Green_Loop,
		[5] = self.userWidget.Ani_in_4Yellow_Loop,
		[6] = self.userWidget.Ani_in_5Orange_Loop,
		[7] = self.userWidget.Ani_in_6Red_Loop,
	}
end

--- UI组件初始化，此处为自动生成
function NPCMoodPanel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function NPCMoodPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function NPCMoodPanel:InitUIView()
	self:ShowMoodFire()
end

---组件刷新统一入口
function NPCMoodPanel:Refresh(entityID)
	if not entityID then
		return
	end
	self.npcEID = entityID
	self:OnPrepareMoodChange()
end

function NPCMoodPanel:ShowMoodFire()
	self.userWidget:SetVisibility(ESlateVisibility.Visible)
	self:PlayAnimation(self.userWidget.Ani_Fadein, nil, self.userWidget)
end

function NPCMoodPanel:HideMoodFire()
	self:PlayAnimation(self.userWidget.Ani_Fadeout, nil, self.userWidget)
	self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
end

function NPCMoodPanel:OnClose()
	self.npcEID = nil
	self.nearestMoodValue = nil
	self.nearestMoodLevel = nil
end

function NPCMoodPanel:OnPrepareMoodChange()
	local fireValue = 0
	local npcEntity = Game.EntityManager:getEntity(self.npcEID)
	if not npcEntity or not npcEntity.TemplateID then
		Log.WarningFormat("[NPCMoodPanel] NPC Not Exist. NPC ID: %s", self.npcEID)
		return
	end

	if npcEntity.MoodValue then
		fireValue = npcEntity.MoodValue
	else
		Log.WarningFormat("[NPCMoodPanel] NPC MoodValue Not Exist. NPC ID: %s", self.npcEID)
	end
	
	self:SwitchMoodFire(fireValue)
	Log.DebugFormat("CutPrice: NPC %s mood changed %s", self.npcEID, fireValue)
end

---切换灵视火焰
---@param moodValue number 
function NPCMoodPanel:SwitchMoodFire(moodValue)
	local moodLevel = Game.NPCMoodSystem:GetMoodLevel(moodValue)
	if self.nearestMoodValue == nil or self.nearestMoodLevel == nil then
		self:ChangeMoodState(moodLevel,0)
	elseif self.nearestMoodValue ~= moodValue then
		if moodValue < self.nearestMoodValue then
			self:ChangeMoodState(moodLevel,2)	--心情下降
		else
			self:ChangeMoodState(moodLevel,1)	--心情上升
		end
	end
	Log.DebugFormat("CutPrice MoodFire change value, old %s, new %s", self.nearestMoodValue, moodValue)
	self.nearestMoodValue = moodValue
	self.nearestMoodLevel = moodLevel
end

---@param level integer 心情档位 1-7 7最开心(Red) 1生气(Black)
---@param changeState integer 心情变化时的样子 0常态 1升级 2降级
---@param textState integer 文字提示样式 0两行描述 1一行提示 
function NPCMoodPanel:ChangeMoodState(level, changeState, textState)
	local state = level - 1 
	if changeState > 0 then
		-- 因为蓝图里面设置是 0开始
		self.userWidget:Event_UI_Style(state, changeState, textState or 1)
		
		self:PlayAnimation(self.AniInTable[level], function()
			--todo 打断回调目前有问题，等后续支持到后改
			self:PlayAnimation(self.AniLoopTable[level], nil, self.userWidget, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
		end, self.userWidget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
		
		if changeState == 1 then
			self:PlayAnimation(self.view.TipText.Ani_Fadein_up, nil, self.view.TipText, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
			self:PlayAnimation(self.view.VFX_MoodUp.Ani_up, nil, self.view.VFX_MoodUp, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
		elseif changeState == 2 then
			self:PlayAnimation(self.view.TipText.Ani_Fadein_down, nil, self.view.TipText, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
			self:PlayAnimation(self.view.VFX_MoodDown.Ani_down, nil, self.view.VFX_MoodDown, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
		end
		
		self:StartTimer("textTip", function()
			if changeState == 1 then
				self:PlayAnimation(self.view.TipText.Ani_Fadeout_up, nil, self.view.TipText)
			elseif changeState == 2 then
				self:PlayAnimation(self.view.TipText.Ani_Fadeout_down, nil, self.view.TipText)
			end
			
			self.userWidget:Event_UI_Style(state, changeState, textState or 0)
		end,1000,1)
	else
		self.userWidget:Event_UI_Style(state, changeState, textState or 0)
	end
	self:ChangeMoodText(level, changeState - 1)
	Log.DebugFormat("CutPrice Mood Style Change: %s", state)
end

---@param state integer 文字提示样式 0变亮 1变暗
function NPCMoodPanel:ChangeMoodText(moodLevel, state)
	if state >= 0 then
		if state == 0 then
			self.view.TipText.KRText_Tips:SetText(StringConst.Get("MOOD_LEVELUP"))
		elseif state == 1 then
			self.view.TipText.KRText_Tips:SetText(StringConst.Get("MOOD_LEVELDOWN"))
		end
		self.view.TipText:Event_UI_Style(state)
	end
	self.view.MoodText.KText_Title:SetText(StringConst.Get("CP_"..Game.NPCMoodSystem:GetMood(moodLevel)))
	self.view.MoodText.KText_Info:SetText(StringConst.Get(Game.NPCMoodSystem:GetMood(moodLevel).."_Desc"))
end

return NPCMoodPanel
