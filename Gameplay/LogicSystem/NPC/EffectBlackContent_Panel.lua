local KismetInputLibrary = import("KismetInputLibrary")
local ESlateVisibility = import("ESlateVisibility")

local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class EffectBlackContent_Panel : UIPanel
---@field view EffectBlackContent_PanelBlueprint
local EffectBlackContent_Panel = DefineClass("EffectBlackContent_Panel", UIPanel)

EffectBlackContent_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EffectBlackContent_Panel:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

---初始化数据
function EffectBlackContent_Panel:InitUIData()
	self.curContentIndex = nil		--当前显示的字幕index
	self.skipDelay = nil
	self.blackStartTime = nil
end

--- UI组件初始化，此处为自动生成
function EffectBlackContent_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function EffectBlackContent_Panel:InitUIEvent()
	if PlatformUtil.IsMobilePlatform() then
		self:AddUIEvent(self.view.OnTouchStartedEvent, "OnTouchStarted")
		self:AddUIEvent(self.view.OnTouchEndedEvent, "OnTouchEnded")
	else
		self:AddUIEvent(self.view.OnMouseButtonDownEvent, "OnTouchStarted")
		self:AddUIEvent(self.view.OnMouseButtonUpEvent, "OnTouchEnded")
	end
	self:AddUIEvent(self.view.UMGAnimationNotifyEvents.First.Delegate, "setText")
end

---面板打开的时候触发
function EffectBlackContent_Panel:OnRefresh(contentList, skipDelay, bAutoContinue)
	self.userWidget:SetVisibility(ESlateVisibility.Visible)
	self.contentList = contentList
	self.skipDelay = skipDelay * 1000
	self.bAutoContinue = bAutoContinue
	self.curContentIndex = 0

	if #contentList == 0 then
		Log.Error("Content list is empty")
		return
	end
	self:showContent()
	self:setText()
end

function EffectBlackContent_Panel:showContent()
	self:StopTimer("WaitEnd")
	self.curContentIndex = self.curContentIndex + 1
	if self.curContentIndex > #self.contentList then
		self:CloseSelf()
	else
		local anim = self.curContentIndex > 1 and self.view.Ani_Click or self.view.Ani_Border_Fadein
		self:PlayAnimation(anim, function()
			self:PlayAnimation(self.view.Ani_Loop, nil, nil, 0, 0)
		end)

		local contentInfo = self.contentList[self.curContentIndex]
		if self.bAutoContinue ~= false then
			self:StartTimer("WaitEnd", function()
				self:showContent()
			end, (contentInfo.Duration ~= nil and contentInfo.Duration or 3) * 1000, 1)
		end
		self.blackStartTime = _now()
	end
end

function EffectBlackContent_Panel:setText()
	local contentInfo = self.contentList[self.curContentIndex]
	self.view.KGTextBlock_Content:SetText(contentInfo.Desc)
end

function EffectBlackContent_Panel:OnTouchStarted()
	return UIBase.HANDLED
end

function EffectBlackContent_Panel:OnTouchEnded(InGeometry, InGestureEvent)
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(InGestureEvent)
	if PointerIndex > 0 then
		return
	end
	if self.skipDelay <= _now() - self.blackStartTime then
		self:showContent()
	end
	return
end

function EffectBlackContent_Panel:OnClose()
	Game.CreateRoleSystem:CheckOpenAnswerPanel()
	self.skipDelay = nil
	self.blackStartTime = nil
end

return EffectBlackContent_Panel
