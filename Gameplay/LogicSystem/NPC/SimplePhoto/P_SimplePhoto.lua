---@class P_SimplePhoto : UIController
---@field public View WBP_Decay_PanelView
local P_SimplePhoto = DefineClass("P_SimplePhoto", UIController)

function P_SimplePhoto:OnCreate()

end

---@param photoID number
function P_SimplePhoto:OnRefresh(photoID)
    
    if not photoID then
        Log.Warning("[P_SimplePhoto] no photoID")
        return
    end
    
    local photoData = Game.TableData.GetPhotoDataRow(photoID)
    if photoData then
        self:SetImage(self.View.Img_Photo, photoData.PhotoPath)
    else
        Log.Warning("[P_SimplePhoto] InVaild PhotoID")
    end
end

function P_SimplePhoto:OnClick_AnyRegion()
    UI.HideUI("P_SimplePhoto")
end

return P_SimplePhoto
