
---@class ReviewItem : UIComponent
---@field private data DialogueReviewItem
---@field private parent P_ReviewPanel
---@field private isPlayingSound boolean
---@field private isSelected boolean
---@field private index number
---@field private isPlayingSound boolean
---@field private isSelected boolean
local ReviewItem = DefineClass("ReviewItem", UIComponent)

local ESlateVisibility = import("ESlateVisibility")
function ReviewItem:OnCreate()
    --- isPlayingSound：是否正在播放声音
    self.isPlayingSound = false
    --- isSelected：是否被选中
    self.isSelected = false
    --- isHovered: 是否正在悬停
    self.isHovered = false

    local view = self.View
    self:AddUIListener(EUIEventTypes.CLICK, view.WBP_NPCBtn_lua.KGButton_lua, self.OnClickBtn)
    self:AddUIListener(EUIEventTypes.Hovered, view.Btn_EventTrigger_lua, self.OnHovered)
    self:AddUIListener(EUIEventTypes.Unhovered, view.Btn_EventTrigger_lua, self.OnUnhovered)
    self:AddUIListener(EUIEventTypes.CLICK, view.Btn_EventTrigger_lua, self.OnClicked)
end

function ReviewItem:OnClickBtn()
    local data = self.data
    local parent = self.parent
    if data and not string.isEmpty(data.sound) and parent then
        local func
        if not self.isPlayingSound then
            func = parent.PlaySound
        else
            func = parent.CancelSound
        end

        if func then
            xpcall(func, _G.CallBackError, parent, data.sound, self.index, self)
        end
    end
    
    self:OnClicked()
end

---@param successed boolean
function ReviewItem:OnPlayingSound(successed)
    self.isPlayingSound = successed
    self.View.WBP_NPCBtn_lua:Event_UI_Type(successed and 4 or 3, true, false)
end

function ReviewItem:OnSoundCanceled()
    self:OnSoundEnd()
end

function ReviewItem:OnSoundEnd()
    self.isPlayingSound = false
    self.View.WBP_NPCBtn_lua:Event_UI_Type(3, true, false)
end

function ReviewItem:OnHovered()
    self.isHovered = true
    local isOption = self.data and self.data.type == 1
    if self.isSelected then
        self.View:Event_UI_Type(false, true, isOption, isOption and 1 or 0)
    else
        self.View:Event_UI_Type(true, false, isOption, isOption and 1 or 0)
    end
end

function ReviewItem:OnUnhovered()
    self.isHovered = false
    local isOption = self.data and self.data.type == 1
    if self.isSelected then
        self.View:Event_UI_Type(false, true, isOption, isOption and 1 or 0)
    else
        self.View:Event_UI_Type(false, false, isOption, isOption and 1 or 0)
    end
end

---@param parentUI P_ReviewPanel
---@param bSelect boolean
---@param allData DialogueReviewItem[]
---@param index number
function ReviewItem:OnListRefresh(parentUI, bSelect, allData, index)
    if not index then
        return
    end

    local data = allData[index]
    
    self.isSelected = bSelect
    self.index = index
    self.data = data
    self.parent = parentUI

    local view = self.View
    if view then
        local Collapsed = ESlateVisibility.Collapsed
        local Hidden = ESlateVisibility.Hidden
        local HitTestInvisible = ESlateVisibility.HitTestInvisible
        local Visible = ESlateVisibility.Visible

        local content = data.content
        view.WBP_NPCBtn_lua:Event_UI_Type(3, false, false)
        if data.type == 1 then -- 选项
            view.WBP_NPCBtn_lua:SetVisibility(Collapsed)
            view.Canvas_Option:SetVisibility(HitTestInvisible)

            view.Text_Title:SetText("")
            view.Text_Title:SetVisibility(Collapsed)
        else -- 台本
            view.WBP_NPCBtn_lua:SetVisibility(Visible)
            view.Canvas_Option:SetVisibility(Collapsed)

            view.Text_Title:SetText(data.talkerName or "")
            view.Text_Title:SetVisibility(HitTestInvisible)
            local npcManager = Game.NPCManager
            content = npcManager.GetFormatTalkText(content) or content
        end

        view.Text_Content:SetText(content)

        local bOption = self.data and self.data.type == 1
        if bSelect then
            view:Event_UI_Type(false, true, bOption, bOption and 1 or 0)
        else
            view:Event_UI_Type(self.isHovered, false, bOption, bOption and 1 or 0)
        end
        view.Img_LineUp_lua:SetVisibility(index == 1 and Hidden or HitTestInvisible)
        view.Img_LineDown_lua:SetVisibility(index == #allData and Hidden or HitTestInvisible)
    end
end

function ReviewItem:OnClicked()
    local parent = self.parent
    if parent then
        parent:Select(self.index, self, self.data)
    end
end

return ReviewItem