---@class NPCConditions @与任务相关的客户条件判断
local NPCConditions = DefineClass("NPCConditions")
local QuestConditionUtils = kg_require("Gameplay.LogicSystem.Quest.QuestConditionUtils")
function NPCConditions.ctor(self)
end

NPCConditions.ConditionFun = {
    IsQuestActivated = function(param) --判断任务激活（与服务器判断一致）
        return QuestConditionUtils.IsQuestActivated(param)
    end,
    IsQuestFinished = function(param) --判断任务完成
        return QuestConditionUtils.IsQuestFinished(param)
    end,
    IsRingActivated = function(param) --判断环激活
        return QuestConditionUtils.IsRingActivated(param)
    end,
    IsRingFinished = function(param) --判断环完成
        return QuestConditionUtils.IsRingFinished(param)
    end,
    IsTeleportUnlock = function(param, npcEID)
        local entity = Game.EntityManager:getEntity(npcEID)
        if not entity then
            return false
        end
        local insID = tonumber(entity.InstanceID)
        local NpcToTeleportMap = Game.TableData.Get_NpcToTeleportMap()
        local teleportInsID = NpcToTeleportMap[insID]
        if not teleportInsID then
            return true
        end
        return Game.TeleportManager:IsTeleportPointUnlocked(tostring(teleportInsID))
    end,
    IsTeleportLock = function(param, npcEID)
        local entity = Game.EntityManager:getEntity(npcEID)
        if not entity then
            return false
        end
        local insID = tonumber(entity.InstanceID)
        local NpcToTeleportMap = Game.TableData.Get_NpcToTeleportMap()
        local teleportInsID = NpcToTeleportMap[insID]
        if not teleportInsID then
            return false
        end
        return not Game.TeleportManager:IsTeleportPointUnlocked(tostring(teleportInsID))
    end,
}

function NPCConditions:CheckCondition(condition, npcEID)
    if NPCConditions.ConditionFun[condition.kind] then
        return NPCConditions.ConditionFun[condition.kind](condition, npcEID)
    end
    return nil
end

---@public 选项条件判断
---@param conditions table|nil @条件组
---@param npcEID number @npcEID
---@return boolean @是否满足
function NPCConditions:CheckConditions(conditions, npcEID)
    if not conditions then
        return true
    end
    for i = #conditions, 1, -1 do
        local result = self:CheckCondition(conditions[i], npcEID)
        if result == false then
            return result
        end
    end
    return true
end
-----------------------------------End Trace-----------------------------------
return NPCConditions