local NPCConditions = kg_require "Gameplay.LogicSystem.NPC.System.NPCConditions"
local QuestConditionUtils = kg_require("Gameplay.LogicSystem.Quest.QuestConditionUtils")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

---@class NPCSystem:SystemBase,NPCConditions @系统npc相关操作判断模块
local NPCSystem = DefineClass("NPCSystem", SystemBase, NPCConditions)

NPCSystem._Empty = {}

NPCSystem.TalkType = {
    NONE = 0, --有响应但不影响对话逻辑
    Content = 1, --对话文本
    Option = 2, --选项
    Dialogue = 3, --剧情对话
    Jump = 4, --UI跳转
    Finally = 5, --结束
    WaitForEnd = 6, --等待结束
}

---@class TalkOptionPriority @闲话选项优先级
NPCSystem.TalkOptionPriority = {
    TaskPriorityStart = 10000,
    TaskPriorityEnd = 19999,
    Func = 100000,
    Normal = 100001
}

---@class NPCSystem.EDynamicTalkForm @动态对话来源
NPCSystem.EDynamicTalkForm = {
    Task = 1, --任务模块
    TaskSubmit = 2, --任务模块的道具提交
}

---@class NPCSystem.EDynamicKind @动态对话类型
NPCSystem.EDynamicKind = {
    Dialogue = 1, --剧情对话
    Talk = 2, --闲话
    Submit = 3, --道具提交
    SubmitDialogue = 4 --通过dialogue进行道具提交
}

---@class NPCSystem.EPlayKind @闲话播放类型
NPCSystem.EPlayKind = {
    InOrder = 0, --顺序播放
    Random = 1, --随机播放
}
----------------------------Override----------------------------
NPCSystem.eventBindMap = {
    [EEventTypesV2.REMINDER_ON_LEVEL_UP] = "OnLevelUp",
}

function NPCSystem:onCtor()
end

function NPCSystem:onInit()
    ---@type NPCModel
    self.model = require("Gameplay.LogicSystem.NPC.System.NPCModel").new(true, true)
    ---@type NPCSender
    self.sender = require("Gameplay.LogicSystem.NPC.System.NPCSender").new()
end

----------------------------Static----------------------------
NPCSystem._tmstr = {}
---@pirvate 填充方法
function NPCSystem.mstr(...)
    local len = select("#", ...)
    for i = 2, len, 2 do
        NPCSystem._tmstr[select(i, ...)] = select(i + 1, ...)
    end
    local r = string.gsub(select(1, ...), '{{([^{^}]+)}}', function(s)
        return NPCSystem._tmstr[s] or string.format("{{%s}}", s)
    end)
    table.clear(NPCSystem._tmstr)
    return r
end

---@public 对话自动填充字符
function NPCSystem.GetFormatTalkText(str)
    if Game == nil or Game.me == nil then
        --编辑器下会执行到这个函数，但没有Game.me，直接返回源字符串即可
        return str
    end

    local ClassName = Game.TableData.GetPlayerSocialDisplayDataRow(Game.me.Profession)[0].ClassName
    local Sex = Game.me.Sex
    str = NPCSystem.mstr(str, "PlayerName", Game.me.Name, "Class", ClassName)
    str = string.gsub(str, '{{([^{^}]+)}}', function(s)
        local index = string.find(s, "|")
        if (not index) or index < 0 then
            return string.format("{{%s}}", s)
        end
        local tab = string.split(s, "|")
        if Sex == 0 then
            --男
            return tab[1]
        elseif Sex == 1 then
            --女
            return tab[2]
        end
    end)

    return str
end
---------------------DynamicTalk---------------------
---@public 检查NPC是否有动态插入的对话数据
---@param npcCfgID number @NPC配置ID
function NPCSystem:CheckDynamicTalk(npcCfgID)
    if not self.model.dynamicTalk[npcCfgID] then
        return false
    end
    return #self.model.dynamicTalk[npcCfgID] > 0
end
---@public 检查NPC是否有动态插入的任务对话数据
---@param npcCfgID number @NPC配置ID
---@return boolean
function NPCSystem:HasQuestDynamicTalk(npcCfgID)
    local dynamicTalk = self.model.dynamicTalk[npcCfgID]
    if not dynamicTalk then
        return false
    end
    if #dynamicTalk <= 0 then
        return false
    end
    for i = 1, #dynamicTalk do
        if dynamicTalk[i].Form == NPCSystem.EDynamicTalkForm.Task then
            return true
        end
    end
    return false
end

---@public 获取动态插入的对话数据组
---@param npcCfgID number @NPC配置ID
---@return NPCModel.DynamicTalkData[] @所有的动态对话组，以按优先级排序
function NPCSystem:GetTalkDynamicData(npcCfgID)
    if not self:CheckDynamicTalk(npcCfgID) then
        return NPCSystem._Empty
    end

    return self.model:GetTalkDynamicData(npcCfgID)
end

---@public 获取优先级最高的动态插入的对话ID
---@param npcCfgID number @NPC配置ID
---@return NPCModel.DynamicTalkData @优先级最高的动态对话
function NPCSystem:GetFirstTalkDynamicData(npcCfgID)
    if not self:CheckDynamicTalk(npcCfgID) then
        return NPCSystem._Empty
    end

    return self.model:GetTalkDynamicData(npcCfgID)[1]
end

---@public 插入动态对话
---@param from NPCSystem.EDynamicTalkForm @枚举，动态对话来源
---@param kind NPCSystem.EDynamicKind @枚举，动态对话类型
---@param npcCfgID number @NPC配置ID
---@param genericID number @根据kind枚举来解析的对话ID
---@param priority number @优先级，越大优先级越高
---@return boolean @是否成功添加
function NPCSystem:AddDynamicTalk(form, kind, npcCfgID, genericID, priority, distance)
    return self.model:AddDynamicTalk(form, kind, npcCfgID, genericID, priority, distance)
end

---@public 移除动态对话
---@param from NPCSystem.EDynamicTalkForm @枚举，移除行为的来源
---@param kind NPCSystem.EDynamicKind @枚举，动态对话类型
---@param npcCfgID number @NPC配置ID
---@param genericID number @根据kind枚举来解析的对话ID
---@return boolean @是否成功移除，如果不存在返回false
function NPCSystem:RemoveDynamicTalk(form, kind, npcCfgID, genericID)
    return self.model:RemoveDynamicTalk(form, kind, npcCfgID, genericID)
end

---@public 打开黑屏字幕界面
function NPCSystem:OnMsgPlayBlackBG(desc, delay, skipDelay, endCallBack)
	---@type BlackSingleContent
	local params = {
		Text = desc,
		Duration = delay,
		CanSkipTime = skipDelay,
	}
	-- todo 这里还需要接入bgType
	QuestUtils.OpenBlackOrWhiteBgPanel(nil, {params}, endCallBack)
end

---@public 获取动态交互距离
function NPCSystem:TryGetNpcTriggerRadius(npcCfgID)
    local dynamicData = self:GetFirstTalkDynamicData(npcCfgID)
    if not dynamicData then
        return
    end
    if dynamicData.Form == NPCSystem.EDynamicTalkForm.Task then
        --TODO:收敛到QuestSystem内部
        local questID = Game.QuestSystem:GetQuestIDByDynamicTalk(dynamicData.Kind, dynamicData.GenericID)
        local conditions = Game.QuestSystem:GetConditionCfg(questID)
        for i = 1, #conditions do
            local distance = conditions[i].Distance
            if distance and distance > 0 then
                return distance
            end
        end
    end
end

---@public 道具提交回包
function NPCSystem:OnItemSubmit()
    UI.Invoke("P_NPCTalk", "OnItemSubmitHide")
end
------------------------------------------Talk
--[[
对话领取任务的交互规则：
领取任务Npc没有Talk，并且身上只有一个可领取任务，则F键交互直接开启领取任务对话。
领取任务Npc没有Talk，但是身上有多个可领取任务，则F键交互后，开启一个空的Talk界面，上面显示出相应的任务领取选项。
领任务Npc有Talk（Talk只有一段文本），身上有可领取任务，则F键交互后，在Talk第一段对话的界面显示出相应的任务领取选项。
领任务Npc有Talk（Talk有多段文本），身上有可领取任务，则F键交互后，在Talk第一段对话的界面显示出相应的任务领取选项，并且额外显示出一个选项【下一页】，方便玩家查看后续的Talk内容。
]]

---获取对话选项（任务只与第一段有关）
---@param talkID number @对话ID
---@param order number @组内ID
function NPCSystem:GetTalkOptions(talkID, order, options, npcEntityID)
    local talkData = Game.TableData.GetTalkDataRow(talkID)
    if not talkData then
        return options
    end
    talkData = talkData[order]
    if not talkData then
        return options
    end
    local Options = talkData.Options
    if Options then
		if QuestUtils.UseCommonCondition then
			for i = 1, #Options, 1 do
				local optionCfg = Options[i]
				local optionCommonCondition = optionCfg.CommCondition
				if QuestUtils.CheckCommonConditionMeet(optionCommonCondition) then
					options[#options+1] = optionCfg
				end
			end
		else
			for i = 1, #Options, 1 do
				local optionCfg = Options[i]
				if QuestConditionUtils.CheckConditions(optionCfg.Conditions, npcEntityID) then
					options[#options+1] = optionCfg
				end
			end
		end
    end
    return options
end

function NPCSystem:buildSingleDynamicOption(dynamicData, options, npcEntityID)
    if dynamicData.Form == NPCSystem.EDynamicTalkForm.Task then
        --目前选项只支持任务类型的插入
        local questID = Game.QuestSystem:GetQuestIDByDynamicTalk(dynamicData.Kind, dynamicData.GenericID)
        local questCfg = Game.QuestSystem:GetTaskExcelCfg(questID)
        local params
        if dynamicData.Kind == NPCSystem.EDynamicKind.Talk then
            return self:GetTalkOptions(dynamicData.GenericID, 1, options, npcEntityID)
        elseif dynamicData.Kind == NPCSystem.EDynamicKind.Dialogue then
			local dialogueUniqId = dynamicData.GenericID
			local dialogueId, questId, condIdx = QuestUtils.GetDialogueQuestAndCondIdxByDialogueUniqId(dialogueUniqId)
			params = {
                NextKind = Enum.TalkOptionType.DialogueType,
                DialogueID = dialogueId,
                Distance = dynamicData.Distance,
				QuestID = questId,
				CondIdx = condIdx,
            }
            options[#options+1] = {Title = questCfg.RingName, Params = params}
        elseif dynamicData.Kind == NPCSystem.EDynamicKind.SubmitDialogue then
            local submitID = Game.QuestSystem:GetSubmitIDByDynamicTalk(dynamicData.GenericID)
			local _, condIdx = Game.QuestSystem:GetQuestIDAndCondIdxBySubmitID(submitID)
            local ringID = Game.QuestSystem:GetRingIDByQuestID(questID)
            local oldSubmitId, newSubmitCfg = QuestUtils.GetSubmitItemDataBySubmitUniqId(submitID)
            params = {
                NextKind = Enum.TalkOptionType.DialogueType,
                DialogueID = dynamicData.GenericID,
                SubmitID = oldSubmitId,
                NewSubmitCfg = newSubmitCfg,
				QuestID = questID,
				CondIdx = condIdx,
                RingID = ringID,
                Distance = dynamicData.Distance
            }
            options[#options+1] = {Title = questCfg.RingName, Params = params}
        elseif dynamicData.Kind == NPCSystem.EDynamicKind.Submit then
            local _, condIdx = Game.QuestSystem:GetQuestIDAndCondIdxBySubmitID(dynamicData.GenericID)
            local oldSubmitId, newSubmitCfg = QuestUtils.GetSubmitItemDataBySubmitUniqId(dynamicData.GenericID)
            params = {
                NextKind = Enum.TalkOptionType.ItemSubmit,
                QuestID = questID,
                ItemSubmitID = oldSubmitId,
                NewSubmitCfg = newSubmitCfg,
				CondIdx = condIdx,
            }
            options[#options+1] = {Title = questCfg.RingName, Params = params}
        end
    end
end

function NPCSystem:buildDynamicOptions(dynamicDatas, options)
    for i = 1, #dynamicDatas, 1 do
        local dynamicData = dynamicDatas[i]
        if dynamicData.Form == NPCSystem.EDynamicTalkForm.Task then
            local QuestSystem = Game.QuestSystem
            --目前选项只支持任务类型的插入
            local questID = QuestSystem:GetQuestIDByDynamicTalk(dynamicData.Kind, dynamicData.GenericID)
            if questID then
                local questCfg = QuestSystem:GetTaskExcelCfg(questID)
                local params
                if dynamicData.Kind == NPCSystem.EDynamicKind.Talk then
                    params = {
                        NextKind = Enum.TalkOptionType.Content,
                        OptionID = dynamicData.GenericID,
                        QuestID = questID,
                    }
                elseif dynamicData.Kind == NPCSystem.EDynamicKind.Dialogue then
					local dialogueUniqId = dynamicData.GenericID
					local dialogueID, _, condIdx = QuestUtils.GetDialogueQuestAndCondIdxByDialogueUniqId(dialogueUniqId)
                    local receiveRingID = QuestSystem:GetReceiveRingIDByDialogueID(dialogueID)
                    params = {
                        NextKind = Enum.TalkOptionType.DialogueType,
                        DialogueID = dialogueID,
                        ReceiveRingID = receiveRingID,
                        Distance = dynamicData.Distance,
						QuestID = questID,
						CondIdx = condIdx,
                    }
                elseif dynamicData.Kind == NPCSystem.EDynamicKind.SubmitDialogue then
                    local submitID = QuestSystem:GetSubmitIDByDynamicTalk(dynamicData.GenericID)
					local _, condIdx = QuestSystem:GetQuestIDAndCondIdxBySubmitID(submitID)
                    local ringID = QuestSystem:GetRingIDByQuestID(questID)
                    local oldSubmitId, newSubmitCfg = QuestUtils.GetSubmitItemDataBySubmitUniqId(submitID)
                    params = {
                        NextKind = Enum.TalkOptionType.DialogueType,
                        DialogueID = dynamicData.GenericID,
                        SubmitID = oldSubmitId,
                        NewSubmitCfg = newSubmitCfg,
                        QuestID = questID,
						CondIdx = condIdx,
                        RingID = ringID,
                        Distance = dynamicData.Distance
                    }
                elseif dynamicData.Kind == NPCSystem.EDynamicKind.Submit then
					local _, condIdx = Game.QuestSystem:GetQuestIDAndCondIdxBySubmitID(dynamicData.GenericID)
                    local oldSubmitId, newSubmitCfg = QuestUtils.GetSubmitItemDataBySubmitUniqId(dynamicData.GenericID)
                    params = {
                        NextKind = Enum.TalkOptionType.ItemSubmit,
                        QuestID = questID,
                        ItemSubmitID = oldSubmitId,
                        NewSubmitCfg = newSubmitCfg,
						CondIdx = condIdx,
                    }
                end
                options[#options+1] = {Title = questCfg.RingName, Params = params}
            else
                Log.WarningFormat("【NPCSystem】Error Dynamic Option(Kind:%s, GenericID:%s)", dynamicData.Kind, dynamicData.GenericID)
            end
        end
    end
end

function NPCSystem:getTalkIdByTalkGroupId(talkGroupId)
	local talkGroupCfg = Game.TableData.GetTalkGroupDataRow(talkGroupId)
	if talkGroupCfg == nil then
		Log.ErrorFormat("NPCSystem getTalkIdByTalkGroupId, talkGroupId: %d not exist in excel", talkGroupId)
		return nil
	end
	if QuestUtils.UseCommonCondition then
		for _, info in ksbcpairs(talkGroupCfg.TalkGroup) do
			local commonCondition = info.CommCondition
			if QuestUtils.CheckCommonConditionMeet(commonCondition) then
				return info.TalkID
			end
		end
	else
		for _, info in ksbcipairs(talkGroupCfg.TalkGroup) do
			if QuestConditionUtils.CheckConditions(info.Condition) then
				return info.TalkID
			end
		end
	end
end

---获取闲话ID
---@param npcCfgID number @NPC配置ID
function NPCSystem:GetTalkID(npcCfgID, eid)
    if eid then
        local RPCEntity = Game.EntityManager:getEntity(eid)
		-- 如果服务端设置了
        if RPCEntity and RPCEntity.runtimeSettings and RPCEntity.runtimeSettings.TalkGroupID ~= nil then
			local talkGroupId = RPCEntity.runtimeSettings.TalkGroupID
			if talkGroupId > 0 then
				return self:getTalkIdByTalkGroupId(talkGroupId)
			else
				return 0
			end
        end
    end
    local npcCfg = Game.TableData.GetNpcInfoDataRow(npcCfgID)
    if not npcCfg then
        Log.WarningFormat("Error NPCID(%s)", npcCfgID)
        return 0
    end
	local talkGroupId = npcCfg.TalkGroupID
    if talkGroupId > 0 then
		-- #126089 这里策划为了简化配置，可能这个talkGroupId就是talkId，根据ID分段来判断
		if QuestUtils.CheckTalkIdSegment(talkGroupId) then
			return talkGroupId
		end
        return self:getTalkIdByTalkGroupId(talkGroupId)
    end
    return 0
end

---获取第一段选项（任务只与第一段有关）
---@param npcCfgID number @NPC配置ID
function NPCSystem:GetFirstTalkOptions(npcCfgID, options, npcEntityID)
    options = options or {}
    local talkID = self:GetTalkID(npcCfgID, npcEntityID)
    local dynamicDatas = Game.NPCSystem:GetTalkDynamicData(npcCfgID)

    if talkID and talkID > 0 then
        --有默认闲话
        if dynamicDatas and #dynamicDatas > 0 then
            --有动态对话
            self:buildDynamicOptions(dynamicDatas, options)
            local talkData = Game.TableData.GetTalkDataRow(talkID)
            local Options = talkData[1].Options
            if #talkData > 1 and (not Options or #Options <= 0) then
                --有多句且无选项,塞一个下一页
                options[#options+1] = {Title = StringConst.Get("TASK_NEXT_PAGE"), Params = {
                    NextKind = Enum.TalkOptionType.Content,
                    OptionID = -1,
                }}
            end
            return self:GetTalkOptions(talkID, 1, options, npcEntityID)
        else
            --无动态对话
            return self:GetTalkOptions(talkID, 1, options, npcEntityID)
        end
    else
        --无默认闲话
        if dynamicDatas and #dynamicDatas > 0 then
            if #dynamicDatas > 1 then
                --有多条动态对话
                self:buildDynamicOptions(dynamicDatas, options)
            else
                --仅有一条动态对话
                self:buildSingleDynamicOption(dynamicDatas[1], options, npcEntityID)
            end
            return options
        end
    end
end

function NPCSystem:IsNpcHasIcon(npcCfgID)
	if Game.me.AdditionalSkillParam and Game.me.AdditionalSkillParam.InstanceID then
		-- 卜杖寻路中
		local eid = Game.WorldManager:GetNpcByInstance(tostring(Game.me.AdditionalSkillParam.InstanceID))
		local Entity = Game.EntityManager:getEntity(eid)
		if Entity and Entity.TemplateID == npcCfgID then
			return false
		end
	end
	-- 追踪npc需要icon
	if Game.TraceSystem:IsTracedNPC(npcCfgID) then
		return true
	end
    local IconPath, BGPath,Color1,Color2 = self:GetNpcIcon(npcCfgID)
    if IconPath and Color1 and Color2 then
        return true
    end
    return false
end

function NPCSystem:GetNpcIcon(npcCfgID)
    local dynamicDatas = self:GetTalkDynamicData(npcCfgID)
    if dynamicDatas and #dynamicDatas > 0 then
        local dynamicData = dynamicDatas[1]
        if dynamicData.Form == NPCSystem.EDynamicTalkForm.Task then
            --目前选项只支持任务类型的插入
            local QuestSystem = Game.QuestSystem
            local questID = QuestSystem:GetQuestIDByDynamicTalk(dynamicData.Kind, dynamicData.GenericID, npcCfgID)
            if not questID then
                return
            end
            local ringID = QuestSystem:GetRingIDByQuestID(questID)
            return QuestSystem:GetNPCTaskIconAndColor(ringID, Game.ColorManager.Type.LinearColor)
        end
    end
    local npcCfg = Game.TableData.GetNpcInfoDataRow(npcCfgID)
    if npcCfg and npcCfg.FunctionID ~= 0 then
        --功能图标
        local ConstFunctionData = Game.TableData.GetFunctionInfoDataRow(npcCfg.FunctionID)
        if ConstFunctionData then
            return ConstFunctionData.Icon, nil, Game.ColorManager:GetColor("Common", "M_White", Game.ColorManager.Type.LinearColor), nil
        end
    end
end

---@public 尝试获取NPC关联环ID
---@param npcCfgID number @npc配置ID
---@return number @环ID
function NPCSystem:TryGetRingID(npcCfgID)
    local dynamicDatas = self:GetTalkDynamicData(npcCfgID)
    if dynamicDatas and #dynamicDatas > 0 then
        local dynamicData = dynamicDatas[1]
        if dynamicData.Form == NPCSystem.EDynamicTalkForm.Task then
            --目前选项只支持任务类型的插入
            local QuestSystem = Game.QuestSystem
            local questID = QuestSystem:GetQuestIDByDynamicTalk(dynamicData.Kind, dynamicData.GenericID)
			if questID == nil then
				return nil
			end
            local ringID = QuestSystem:GetRingIDByQuestID(questID)
            return ringID
        end
    end
end

---根据规则获取npc闲话样式
---@param npcCfgID number @npc配置ID
---@return NPCSystem.EDynamicKind,number @对话类型，ID
function NPCSystem:GetNpcTalkContent(npcCfgID, eid)
    local talkID = self:GetTalkID(npcCfgID, eid)

    if talkID and talkID > 0 then
        --有默认闲话
        return 0, NPCSystem.EDynamicKind.Talk, talkID, nil
    else
        --无默认闲话
        local dynamicDatas = self:GetTalkDynamicData(npcCfgID)
        if dynamicDatas and #dynamicDatas > 0 then
            --有动态对话
            if #dynamicDatas > 1 then
                --多条动态对话
                return 0, NPCSystem.EDynamicKind.Talk, Game.TableData.GetConstDataRow("QUEST_NPCTALK_ID"), nil
            else
                --单条动态对话
				local dynamicData = dynamicDatas[1]
                return dynamicData.Form, dynamicData.Kind, dynamicData.GenericID, dynamicData.Distance
            end
        end
        --如果无动态对话无默认闲话且可以交互，显示默认对话
        return 0, NPCSystem.EDynamicKind.Talk, Game.TableData.GetConstDataRow("QUEST_NPCTALK_ID"), nil
    end
end

function NPCSystem:OnInteractNpc(entity)
	Game.QuestSystem:EnterDialogueControlState()
	local UID = entity:uid()
    local npcID = entity:GetConfigTemplateID()
    local from, kind, gID, dis = self:GetNpcTalkContent(npcID, UID)
    if from == NPCSystem.EDynamicTalkForm.Task then
        local QuestSystem = Game.QuestSystem
        if kind == NPCSystem.EDynamicKind.Dialogue then
            local receiveRingID = QuestSystem:GetReceiveRingIDByDialogueID(gID)
			local dialogueId, questId, condIdx = QuestUtils.GetDialogueQuestAndCondIdxByDialogueUniqId(gID)
            Game.CinematicManager:StartPlayCinematic({
                CinematicType = Enum.CinematicType.Dialogue,
                AssetID = dialogueId,
                NPCID = npcID,
                ReceiveRingID = receiveRingID,
                Distance = dis,
                ActorEntityID = UID,
				QuestID = questId,
				CondIdx = condIdx,
            })
            return
        elseif kind == NPCSystem.EDynamicKind.Submit then
            local questID, condIdx = QuestSystem:GetQuestIDAndCondIdxBySubmitID(gID)
            local ringID = QuestSystem:GetRingIDByQuestID(questID)
			local oldSubmitId, newSubmitCfg = QuestUtils.GetSubmitItemDataBySubmitUniqId(gID)
			
			---@type ItemSubmitCompatibleParams
			local submitParam = {
				ItemSubmitID = oldSubmitId,
				TaskRingID = ringID,
				QuestID = questID,
				CondIdx = condIdx,
				NewSubmitCfg = newSubmitCfg,
			}
			Game.ItemSubmitSystem:ShowItemSubmitUICompatible(submitParam)
            return
        elseif kind == NPCSystem.EDynamicKind.SubmitDialogue then
            local submitID = QuestSystem:GetSubmitIDByDynamicTalk(gID)
            local questID, condIdx = QuestSystem:GetQuestIDAndCondIdxBySubmitID(submitID)
            local ringID = QuestSystem:GetRingIDByQuestID(questID)
			local oldSubmitId, newSubmitCfg = QuestUtils.GetSubmitItemDataBySubmitUniqId(submitID)
			Game.CinematicManager:StartPlayCinematic({
                CinematicType = Enum.CinematicType.Dialogue,
                AssetID = gID,
                NPCID = npcID,
                TaskRingID = ringID,
				QuestID = questID,
				CondIdx = condIdx,
                SubmitItemID = oldSubmitId,
				NewSubmitCfg = newSubmitCfg,
                Distance = dis,
                ActorEntityID = UID
            })
            return
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.INTERACTIVE_BEGIN)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_OPEN_FULLSCREEN_PANEL)
	UI.ShowUI("P_NPCTalk", entity)
end

---@public @查看是否可交互
---@param npcEntity EntityBase @NPCEntity
---@return boolean @是否可交互
function NPCSystem:CheckInteract(npcEntity)
    local npcCfg = npcEntity:GetEntityConfigData()
    if self:HasQuestDynamicTalk(npcCfg.ID) then
        --任务流程相关的交互优先级最高
        return true
    end
    if npcEntity.interactiveState == Enum.EnableInteract.Able then
        return true
    elseif npcEntity.interactiveState == Enum.EnableInteract.Not then
        return false
    elseif npcEntity.interactiveState == Enum.EnableInteract.UpToConfig then
        return npcCfg.CanInteract == 1
	elseif npcEntity.bIsLocalSceneNpc == true then
		return npcEntity:CanSceneNpcInteract()
    end
    return false
end

---@public 标记对话
function NPCSystem:MarkDialogue()
    
end
----------------------Event---------------------------
function NPCSystem:OnLevelUp()
    
end

function NPCSystem:OnReceiveHudInteractAxisEvent(value)
    UI.Invoke("P_NPCTalk", "OnHudInteractAxis", value)
end

function NPCSystem:CloseTalk()
    UI.HideUI("P_NPCTalk")
end

function NPCSystem:OnTalkGroupIDUpdate(entity, new, old)
    Log.DebugFormat("[NPCSystem]OnTalkGroupIDUpdate")
end
---------------------------NetEvent---------------------------
function NPCSystem:OnRuntimeSettingStateChange(entity, new, old)
    Log.DebugFormat("[NPCSystem]OnRuntimeSettingStateChange")
end
return NPCSystem
