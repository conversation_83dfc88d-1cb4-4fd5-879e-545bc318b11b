---@class NPCManager
local NPCManager = DefineClass("NPCManager", ManagerBase)
-- local EquipmentManager = kg_require "Gameplay.LogicSystem.Equipment.EquipmentSystem"
-- local StoreManager = kg_require "Gameplay.LogicSystem.Store.StoreManager"

NPCManager.NPCIDActorMap = {}

NPCManager.eventBindMap = {
    [EEventTypesV2.REMINDER_ON_LEVEL_UP] = "OnLevelUp",
}

NPCManager.DynamicTalkDatas = {}
--对话交互类型
NPCManager.InteractType = {
    ["Gossip"] = 1,
    ["Task"] = 2
}
NPCManager.TalkType = {
    NONE = 0, --有响应但不影响对话逻辑
    Content = 1,
    Option = 2,
    Dialogue = 3,
    Jump = 4,
    Finally = 5,
    WaitForFunc = 6,
}
--对话选项优先级
NPCManager.TalkOptionPriority = {
    ["TaskPriorityStart"] = 10000,
    ["TaskPriorityEnd"] = 19999,
    ["Func"] = 100000,
    ["Normal"] = 100001
}
NPCManager.TalkNextType = {
    --与配置表枚举一致(Config: TalkData, Column: NextStep)
    Content = 1,
    JumpType = 2,
    DialogueType = 3,
    GuideGetMoney = 4, --公会领工资
    GuildMerge = 5, --合并公会界面
    ItemSubmit = 6, --道具提交
    SystemAction = 7, --服务器事件
    UnlockTeleport = 8, --解锁传送点
    SystemActionEnum = 10, --SystemAction枚举
    Finally = 99,
}
NPCManager.FuncType = {
    --TODO:未来移动到跳转框架里 -by jiawenjian
    [1241000] = "Dungeon",
    [1240202] = "EquipReforge",
    [1243000] = "NPCShop",
    [1243003] = "NPCShop",
    [1240600] = "ItemSubmit"
}

NPCManager.BWaitForFuncType = {
    --需要waitforfun的跳转类型
    [1240600] = "ItemSubmit" --道具提交
}

NPCManager.showUIFuncs = {
    --TODO:未来移动到跳转框架里 -by jiawenjian
    Dungeon = function(Param)
        return Game.DungeonSystem.CreateDungeonUI()
    end,
    EquipReforge = function(Param)
        -- return EquipmentManager.ShowEquipmentPanel()
    end,
    NPCShop = function(Param)
        UI.ShowUI("P_TradePanel", { 1243000, 2300003 })
    end,
    ItemSubmit = function(Param)
        return Game.ItemSubmitSystem:ShowItemSubmitUI(Param)
    end,
}

NPCManager.ParamRebuildFun = {
    [1240600] = function(Param, TaskHandle) --ItemSubmit 道具提交
        if TaskHandle then
            return { TaskID = TaskHandle.Params.TaskRingID, ItemSubmitID = Param[1] }
        else
            return { ItemSubmitID = Param[1] }
        end
        return Param
    end,
}

NPCManager.SelectIcon = {
    [NPCManager.TalkNextType.GuideGetMoney] = _G.Enum.EArtAssetIconData.GUILD_WAGES,
}

NPCManager.DefaultTalkIcon = UIAssetPath.UI_NPC_Img_Dialogue
NPCManager.DefaultTalkIconBg = UIAssetPath.UI_NPC_Img_DialogueGlow

---NPC trigger交互的形状
NPCManager.TriggerShapeType = {
	Circle = 0,
	Sector = 1,
}

function NPCManager.ShowFuncUI(Type, Param)
    if NPCManager.showUIFuncs[Type] then
        return NPCManager.showUIFuncs[Type](Param)
    end
end

function NPCManager.ParamRebuild(Type, Param, TaskHandle)
    if NPCManager.ParamRebuildFun[Type] then
        return NPCManager.ParamRebuildFun[Type](Param, TaskHandle)
    end
    return Param
end

function NPCManager.AddDynamicTalk(NPCID, TalkID, CallBack, IconBrushFunction, Priority, Params)
    --保存任务NPC信息
    if NPCManager.DynamicTalkDatas[NPCID] == nil then
        NPCManager.DynamicTalkDatas[NPCID] = {}
    end
    local Handle = {
        TalkID = TalkID,
        NPCID = NPCID,
        CallBack = CallBack,
        Params = Params,
        IconBrushFunction = IconBrushFunction,
        IconBrush = true,
        Priority = Priority
    }
    if Params.Distance and Params.Distance > 0 then
        local distance = Params.Distance
        for handle, talkData in pairs(NPCManager.DynamicTalkDatas[NPCID]) do
            if talkData.Params.Distance then
                distance = math.max(distance, talkData.Params.Distance)
            end
        end
        NPCManager.SetNpcTriggerRadius(NPCID, distance)
    end
    NPCManager.DynamicTalkDatas[NPCID][Handle] = Handle
    return Handle
end

function NPCManager.RemoveDynamicTalk(Handle)
    if Handle then
        NPCManager.DynamicTalkDatas[Handle.NPCID][Handle] = nil
        if next(NPCManager.DynamicTalkDatas[Handle.NPCID]) == nil then
            NPCManager.DynamicTalkDatas[Handle.NPCID] = nil
        end
    end
end


function NPCManager.AddTaskNPCToMap(entity)
    if NPCManager.NPCIDActorMap[entity.TemplateID] == nil then
        NPCManager.NPCIDActorMap[entity.TemplateID] = {}
    end
    
    local eid = Game.EntityManager:GetEntityEIDByUID(entity:uid())
    NPCManager.NPCIDActorMap[entity.TemplateID][eid] = eid
end

function NPCManager.RemoveTaskNPCFromMap(entity)
    if entity then
        local eid = Game.EntityManager:GetEntityEIDByUID(entity:uid())
        if eid and entity.TemplateID and NPCManager.NPCIDActorMap[entity.TemplateID] then
            NPCManager.NPCIDActorMap[entity.TemplateID][eid] = nil
        end

        if NPCManager.NPCIDActorMap[entity.TemplateID] and next(NPCManager.NPCIDActorMap[entity.TemplateID]) == nil then
            NPCManager.NPCIDActorMap[entity.TemplateID] = nil
        end
    end
end

function NPCManager:OnRoleVisibilityChanged(uid, bVisible)
	local entity = Game.EntityManager:getEntity(uid)
	if entity then
		if entity.isNpc == true then
			-- and NpcType== Enum.ENpcTypeData.Task
			if bVisible then
				NPCManager.AddTaskNPCToMap(entity)
			else
				NPCManager.RemoveTaskNPCFromMap(entity)
			end
		end
	end
end

function NPCManager.GetTaskNpcByConfigID(ConfigID)
    return NPCManager.NPCIDActorMap[ConfigID]
end

---@param instanceId string
---@return EntityBase
function NPCManager.GetTaskNpcEntByInstanceId(instanceId)
	local eid = Game.WorldManager:GetNpcByInstance(instanceId)
	return Game.EntityManager:getEntity(eid)
end

---@param templateId number
---@return EntityBase
function NPCManager.GetTaskNpcEntByTemplateId(templateId)
	local eidInfo = NPCManager.NPCIDActorMap[templateId]
	if eidInfo then
		for eid, _ in pairs(eidInfo) do    -- luacheck: ignore
			return Game.EntityManager:getEntity(eid)
		end
	end
end

function NPCManager.GetFirstTaskNpcEntByCfgId(ConfigID)
	local TaskNPCs = Game.NPCManager.GetTaskNpcByConfigID(ConfigID)
	if TaskNPCs then
		for _, V in pairs(TaskNPCs) do
			local Entity = Game.EntityManager:getEntity(V)
			if Entity then
				return Entity
			end
		end
	end
	return nil
end

function NPCManager.GetTaskHandle(ConfigID)
    if NPCManager.DynamicTalkDatas[ConfigID] ~= nil and next(NPCManager.DynamicTalkDatas[ConfigID]) ~= nil then
        local MaxPriority = math.maxinteger
        local Handle = nil
        for kk, vv in pairs(NPCManager.DynamicTalkDatas[ConfigID]) do
            if vv.Priority < MaxPriority then
                MaxPriority = vv.Priority
                Handle = vv
            end
        end
        if MaxPriority ~= math.maxinteger and Handle ~= nil then
            return Handle
        end
    end
    return nil
end

function NPCManager.NpcHasIcon(NpcID)
    local TaskHandle = NPCManager.GetTaskHandle(NpcID)
    local NPCInfoTableData = Game.TableData.GetNpcInfoDataRow(NpcID)
    if TaskHandle ~= nil and (not TaskHandle.Params.bHideHeadIcon) then
        return true
    elseif NPCInfoTableData and NPCInfoTableData.FunctionID ~= 0 then
        local ConstFunctionData = Game.TableData.GetFunctionInfoDataRow(NPCInfoTableData.FunctionID)
        if ConstFunctionData and ConstFunctionData.Icon then
            return true
        end
    end

    return false
end

---@public @获取NPC头顶图标（任务、功能）
---@param NpcID number @npc配置表ID
---@return string,string,string,string @图片路径,图片bg路径,色彩,外缘色彩
function NPCManager.GetNpcIcon(NpcID)
    local TaskHandle = NPCManager.GetTaskHandle(NpcID)
    local NPCInfoTableData = Game.TableData.GetNpcInfoDataRow(NpcID)
    if TaskHandle ~= nil and (not TaskHandle.Params.bHideHeadIcon) then
        --TaskIcon优先级更高
        local taskRingID = TaskHandle.Params.TaskRingID
        return Game.QuestSystem:GetNPCTaskIconAndColor(taskRingID)
    elseif NPCInfoTableData and NPCInfoTableData.FunctionID ~= 0 then
        --功能图标
        local ConstFunctionData = Game.TableData.GetFunctionInfoDataRow(NPCInfoTableData.FunctionID)
        if ConstFunctionData then
            return ConstFunctionData.Icon, nil, Game.ColorManager:GetColor("Common", "M_White", Game.ColorManager.Type.SlateColor), nil
        end
    else
        --无图标
        return
    end
end

function NPCManager.BuildNpcIcon(uiComponent, NpcID, IconWidget)
    local TaskHandle = NPCManager.GetTaskHandle(NpcID)
    local NPCInfoTableData = Game.TableData.GetNpcInfoDataRow(NpcID)
    if TaskHandle ~= nil then
        local bSuccess = xpcall(TaskHandle.IconBrushFunction, _G.CallBackError, uiComponent, IconWidget,
            TaskHandle.Params)
        return bSuccess
    elseif NPCInfoTableData and NPCInfoTableData.FunctionID ~= 0 then
        local ConstFunctionData = Game.TableData.GetFunctionInfoDataRow(NPCInfoTableData.FunctionID)
        if ConstFunctionData then
            uiComponent:SetImage(IconWidget, ConstFunctionData.Icon)
            local Color = Game.ColorManager:GetColor("Common", "M_White", Game.ColorManager.Type.SlateColor)
            IconWidget:SetBrushTintColor(Color)
        end
        return true
    else
        return false
    end
end

function NPCManager.PlayBlackBg()
    
end

local _tmstr = {}
local mstr = function(...)
    local len = select("#", ...)
    for i = 2, len, 2 do
        _tmstr[select(i, ...)] = select(i + 1, ...)
    end
    local r = string.gsub(select(1, ...), '{{([^{^}]+)}}', function(s)
        return _tmstr[s] or string.format("{{%s}}", s)
    end)
    table.clear(_tmstr)
    return r
end

---@public 对话自动填充字符
function NPCManager.GetFormatTalkText(str)
    if Game == nil or Game.me == nil then
        --编辑器下会执行到这个函数，但没有Game.me，直接返回源字符串即可
        return str
    end
    if not str then
        return
    end

    local ClassName = Game.TableData.GetPlayerSocialDisplayDataRow(Game.me.Profession)[0].ClassName
    local Sex = Game.me.Sex
    str = mstr(str, "PlayerName", Game.me.Name, "Class", ClassName)
    str = string.gsub(str, '{{([^{^}]+)}}', function(s)
        local index = string.find(s, "|")
        if (not index) or index < 0 then
            return string.format("{{%s}}", s)
        end
        local tab = string.split(s, "|")
        if tab[1] == "Profession" then
            -- 走职业判断
            local professionMap = {}
            for idx=2, #tab do
                local pattern = string.split(tab[idx], ":")
                if #pattern == 2 then
                    professionMap[tonumber(pattern[1])] = pattern[2]
                end
            end

            return professionMap[Game.me.Profession] or string.format("{{%s}}", s)
        else
            -- 走性别判断
            if Sex == 0 then
                --男
                return tab[1]
            elseif Sex == 1 then
                --女
                return tab[2]
            end
        end
    end)

    return str
end

function NPCManager:OnMsgPlayBlackBG(desc, delay, skipDelay, endCallBack)
end

function NPCManager:OnReceiveHudInteractAxisEvent(value)
end

function NPCManager:OnReceiveInputAxis()
end
---------------------------NPCDisplay-------------------------

function NPCManager:CanNpcOpen()
    local ETeleportState = Game.TeleportManager.ETeleportState
    if Game.TeleportManager.CurrentTeleportState == ETeleportState.None then
        return true
    end
    if Game.TeleportManager.CurrentTeleportState == ETeleportState.Wait then
        Game.TeleportManager:CancelTeleportWait()
        return true
    end
    if Game.TeleportManager.CurrentTeleportState == ETeleportState.Teleporting then
        Game.GlobalEventSystem:Publish(EEventTypesV2.INTERACTIVE_FINISH)
        return false
    end
    if Game.TeleportManager.CurrentTeleportState == ETeleportState.ArrivalSequence then
        Game.GlobalEventSystem:Publish(EEventTypesV2.INTERACTIVE_FINISH)
        return false
    end
    return true
end

function NPCManager:OnLevelUp(Params)
    local oldLevel = Params[1]
    local newLevel = Params[2]
    if oldLevel >= newLevel then
        return
    end

	local NpcHideMinLevel = Game.TableData.Get_NpcHideMinLevel()
    for _, tab in ksbcipairs(NpcHideMinLevel) do
        if newLevel < tab.level then
            break
        end
        if oldLevel < tab.level and newLevel >= tab.level then
            for _, instanceID in ksbcpairs(tab.instanceIDs) do
                if NPCManager.CheckNpcCondition(instanceID) then
                    local eid = Game.WorldManager:GetNpcByInstance(instanceID)
                    local RPCEntity = Game.EntityManager:getEntity(eid)
                    if RPCEntity then
                        RPCEntity:SetInvisibleByQuestControl(false)
                    end
                end
            end
        end
    end

	local NpcHideMaxLevel = Game.TableData.Get_NpcHideMaxLevel()
    for _, tab in ksbcipairs(NpcHideMaxLevel) do
        if oldLevel > tab.level then
            break
        end
        if oldLevel < tab.level and newLevel >= tab.level then
            for _, instanceID in ksbcpairs(tab.instanceIDs) do
                if not NPCManager.CheckNpcCondition(instanceID) then
                    local eid = Game.WorldManager:GetNpcByInstance(instanceID)
                    local RPCEntity = Game.EntityManager:getEntity(eid)
                    if RPCEntity then
						RPCEntity:SetInvisibleByQuestControl(true)
                    end
                end
            end
        end
    end
end

function NPCManager:onInit()
    NPCManager.DefaultTalkColor = Game.ColorManager:GetColor("TaskIconColor", "Talk", Game.ColorManager.Type.SlateColor)
    NPCManager.DefaultTalkBgColor = Game.ColorManager:GetColor("TaskIconColor", "Talk_Light", Game.ColorManager.Type.SlateColor)
end

--前置条件判断
function NPCManager.CheckNpcCondition(instanceID)
    local npcHideCfg = Game.TableData.GetNpcHideDataRow(instanceID)
    if not npcHideCfg then
        return true
    end

    --初始刷新
    if not npcHideCfg.Initial then
        return false
    end
    --可见性别
    local Gender = npcHideCfg.Gender
    if Gender ~= -1 then
        local sex = Game.me.Sex
        if Gender ~= sex then
            return false
        end
    end
    --可见职业
    local Profession = npcHideCfg.Profession
    if Profession ~= 0 then
        if Game.me then
            local mainProfession = Game.me.Profession
            if mainProfession ~= nil then
                if mainProfession ~= Profession then
                    return false
                end
            end 
        end
    end
    --等级
    local MinLevel = npcHideCfg.MinLevel
    local MaxLevel = npcHideCfg.MaxLevel
    if MinLevel > 0 then
        if Game.me.Level < MinLevel then
            return false
        end
    end
    if MaxLevel > 0 then
        if Game.me.Level > MaxLevel then
            return false
        end
    end

    return true
end

function NPCManager.IsNPCHide(entity)
    -- 各种编辑器环境可能没有QuestSystem
    if not Game.QuestSystem then
        return false
    end
    
    local InstanceID = entity.InstanceID
	--- 提前判断一下，如果不存在，那么直接返回false
	local npcHideCfg = Game.TableData.GetNpcHideDataRow(InstanceID)
	if not npcHideCfg then
		return false
	end
	
    local bIsVisibility = NPCManager.CheckNpcCondition(InstanceID)
    
    -- if bIsVisibility then
    -- end
    return Game.QuestSystem:IsNPCHide(npcHideCfg, bIsVisibility)

    -- return true
end

function NPCManager.IsNPCHideByInsID(insID)
	local npcHideCfg = Game.TableData.GetNpcHideDataRow(insID)
	if not npcHideCfg then
		return false
	end

	local bIsVisibility = NPCManager.CheckNpcCondition(insID)
	return Game.QuestSystem:IsNPCHide(npcHideCfg, bIsVisibility)
end

function NPCManager.GetNpcTriggerRadius(entity)
    local distance = 0
    if NPCManager.DynamicTalkDatas[entity.TemplateID] then
        for handle, talkData in pairs(NPCManager.DynamicTalkDatas[entity.TemplateID]) do
            if talkData.Params.Distance then
                distance = math.max(distance, talkData.Params.Distance)
            end
        end
    end
    if distance > 0 then
        return distance
    end
    return nil
end

function NPCManager.SetNpcTriggerRadius(tempateID, radius)
	local Entities = Game.EntityManager:getEntitiesByType("NpcActor")
    if Entities then
        for _, Entity in pairs(Entities) do
            if Entity.TemplateID == tempateID then
                Entity:SetTriggerRadius(radius)
            end
        end
    end
end

---------------------------NetEvent---------------------------
return NPCManager
