local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class CutsceneShowText : UIComponent
---@field view CutsceneShowTextBlueprint
local CutsceneShowText = DefineClass("CutsceneShowText", UIPanel)
CutsceneShowText.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CutsceneShowText:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CutsceneShowText:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CutsceneShowText:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function CutsceneShowText:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CutsceneShowText:InitUIView()
end

---组件刷新统一入口
function CutsceneShowText:Refresh(text)
    self.view.RTB_Aside1_lua:SetText(text)
end


return CutsceneShowText
