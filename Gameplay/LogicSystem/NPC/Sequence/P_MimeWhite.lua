local ESlateVisibility = import("ESlateVisibility")

---@class P_MimeWhite:UIController
local P_MimeWhite = DefineClass("P_MimeWhite", UIController)


---OnRefresh
function P_MimeWhite:OnRefresh()
	self.View.WidgetRoot:SetVisibility(ESlateVisibility.Visible)
end

--这个BaseOpacity指的是Border本身的透明度，创建黑屏的时候，这个纯色Border后续会先淡入
function P_MimeWhite:SetBlackScreenBaseAlpha(Alpha)
	if not self.View.WidgetRoot then
		Log.WarningFormat("should set black screen first")
		return
	end
	self.View.WidgetRoot.Border_lua:SetRenderOpacity(Alpha)
end

--纯色Border淡入后，真正的美术Image才开始淡入
function P_MimeWhite:SetBlackScreenBackImageAlpha(Alpha)
	if not self.View.WidgetRoot then
		Log.WarningFormat("should set black screen first")
		return
	end
	self.View.WidgetRoot.CanvasPanel_lua:SetRenderOpacity(Alpha)
end

--设置白字文字
function P_MimeWhite:SetBlackScreenText(Text)
	if not self.View.WidgetRoot then
		Log.WarningFormat("should set black screen first")
		return
	end
	self.View.WidgetRoot.RTB_Aside1_lua:SetText(Text)
end

--设置文字透明度
function P_MimeWhite:SetBlackScreenTextAlpha(Alpha)
	if not self.View.WidgetRoot then
		Log.WarningFormat("should set black screen first")
		return
	end
	self.View.WidgetRoot.RTB_Aside1_lua:SetRenderOpacity(Alpha)
end

return P_MimeWhite