local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class NPCCountDownComp : UIComponent
---@field view NPCCountDownCompBlueprint
local NPCCountDownComp = DefineClass("NPCCountDownComp", UIComponent)

NPCCountDownComp.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function NPCCountDownComp:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function NPCCountDownComp:InitUIData()
	-- 记录该进度条是否已经绑定了事件
	self.bEventBind = false
	-- 记录进度条是否播放到了一半
	self.bHalf = false
	-- 记录进度条的动效位置
	self.VXOffsetVector = FVector2D(328, 0)
	--记录倒计时总时间的数值供参考
	--注意，倒计时本身不依赖于该数值，而是NPCCountDownSystem发送的事件
	self.TimeLimit = -1

	-- 进度条动效移动范围
	self.WidthProgVX = nil
end

--- UI组件初始化，此处为自动生成
function NPCCountDownComp:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function NPCCountDownComp:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function NPCCountDownComp:InitUIView()
end

---组件刷新统一入口
function NPCCountDownComp:Refresh(...)
end

function NPCCountDownComp:SetUp(timeLimit)
	self:StopAllAnimations()
	if self.bEventBind then
		return
	end
	self:PlayAnimation(self.view.Ani_Fadein, function() self:OnFadeInAnimComplete() end)
	self.view.ProgressBar:SetPercent(1.0)
	-- 因为现在就一种情况再用，默认绑定对话倒计时
	Game.EventSystem:AddListener(_G.EEventTypes.DIALOGUE_COUNTDOWN_TICK, self, self.AdjustCountDownPercentage)
	self.bEventBind = true
	self.bHalf = false
	self.TimeLimit = timeLimit
end

function NPCCountDownComp:OnFadeInAnimComplete()
	--self:StopAllAnimations(self.View.WidgetRoot)
	self:PlayAnimation(self.view.Ani_Loop, nil, nil, 0, 0)
end

function NPCCountDownComp:OnHalfWay()
	self:PlayAnimation(self.view.Ani_Dec_1, nil, nil,0, 0, nil, 1 / (self.TimeLimit / 2000))
end

function NPCCountDownComp:OnClose()
	if not self.bEventBind then
		return
	end
	Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.DIALOGUE_COUNTDOWN_TICK, self, self.AdjustCountDownPercentage)
	self.bEventBind = false
end

function NPCCountDownComp:AdjustCountDownPercentage(Percent)
	if Percent <= 0.5 and not self.bHalf then
		self:OnHalfWay()
		self.bHalf = true
	end
	self.view.ProgressBar:SetPercent(Percent)
	local width = 320
	if self.WidthProgVX then
		width = self.WidthProgVX
	end
    self.VXOffsetVector.X = width * Percent
    self.view.ProgressVX_Lua:SetRenderTranslation(self.VXOffsetVector)
end

function NPCCountDownComp:SetProgressVXWidth(width)
    self.WidthProgVX = width
end

return NPCCountDownComp
