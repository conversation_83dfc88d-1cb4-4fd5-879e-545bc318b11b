---@class P_ReviewPanel : UIController
---@field public View WBP_Review_PanelView
---@field ListView ListView3
---@field curItemPlayingSound DialogueReviewItem
---@field curIndexItemPlayingSound number
---@field curSoundEventNamePlaying string
local P_ReviewPanel = DefineClass("P_ReviewPanel", UIController)

local ReviewItem = kg_require("Gameplay.LogicSystem.NPC.ReviewItem")
local StringConst = require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
function P_ReviewPanel:OnCreate()
    --- curItemPlayingSound： 当前播放声音的DialogueReviewItem
    self.curItemPlayingSound = nil
    --- curIndexItemPlayingSound：当前播放声音的DialogueReviewItem的数据Index
    self.curIndexItemPlayingSound = nil
    --- curSoundEventNamePlaying：挡墙播放声音的wwise事件名
    self.curSoundEventNamePlaying = nil

    --- ListView:列表
    self.ListView = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_ComList, ReviewItem)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComPanel.WBP_ComBtnBack_lua.Btn_Back_lua, self.OnClickBtnBack)

    local btnInfo = self.View.WBP_ComPanel.WBP_ComBtnBack_lua.Btn_Info_lua
    self:AddUIListener(EUIEventTypes.CLICK, btnInfo, self.OnClickBtnInfo)
    btnInfo:SetVisibility(ESlateVisibility.Collapsed)
    
    self.View.WBP_ComPanel.WBP_ComBtnBack_lua.Text_Back_lua:SetText(StringConst.Get(
        "DIALOGUE_REVIEW_UI_TITLE"))
    
end

function P_ReviewPanel:OnOpen()
end

function P_ReviewPanel:OnClose()
    self.curItemPlayingSound = nil
    self.curIndexItemPlayingSound = nil
    self.curSoundEventNamePlaying = nil
	Game.DialogueManager:SetDialoguePause(false)
end

function P_ReviewPanel:OnRefresh(...)
    ---@type DialogueReview
    local dialogueReview = Game.DialogueManager.DialogueReview
    if dialogueReview then
        local items = dialogueReview:GetItems()
        local count = #items
        self.ListView.datas = items
        self.ListView:SetData(count)
        self.ListView:ScrollToIndex(count)
        self.ListView:Sel(count)
    end
end

---@param widget ReviewItem
---@param index number
---@param isSelected boolean
function P_ReviewPanel:OnRefresh_WBP_ComList(widget, index, isSelected)
        ---@type DialogueReview
    local dialogueReview = Game.DialogueManager.DialogueReview
 
    if widget and dialogueReview then
        local items = dialogueReview:GetItems()
        local count = #items
        if 0 < index and index <= count then
            widget:BindData(items[index], index, isSelected, index == 1, index == count, self)
        end
    end
end

---@param soundEventName string
---@param indexItem number
---@param item ReviewItem
function P_ReviewPanel:PlaySound(soundEventName, indexItem, item)
    assert(not string.isEmpty(soundEventName))
    local duration = Game.AkAudioManager:GetEventDuration(soundEventName)
    if duration <= 0 then
        self:CancelSound(soundEventName, indexItem, item)
        return
    end

    if soundEventName == self.curSoundEventNamePlaying then
        return
    else
        self:CancelSound(self.curSoundEventNamePlaying, self.curIndexItemPlayingSound, self.curItemPlayingSound)
    end

    self.curSoundEventNamePlaying = soundEventName
    self.curIndexItemPlayingSound = indexItem
    self.curItemPlayingSound = item

    self.handlePlayingSound = Game.AkAudioManager:PostEvent2D(soundEventName)
    local function TimerProc(spanTime)
            self:OnSoundEnd(soundEventName, indexItem, item)
    end

	Game.TimerManager:CreateTimerAndStart(TimerProc, duration * 1000, 1, false, "DialogueReview")

    if item then
        item:OnPlayingSound(true)
    end
end

---@param soundEventName string
---@param indexItem number
---@param item ReviewItem
function P_ReviewPanel:OnSoundEnd(soundEventName, indexItem, item)
    if self.curSoundEventNamePlaying ~= soundEventName then
        return
    end

    if item then
        item:OnSoundEnd()
    end

    self.handlePlayingSound = nil
    self.curIndexItemPlayingSound = nil
    self.curSoundEventNamePlaying = nil
    self.curItemPlayingSound = nil
end

---@param soundEventName string
---@param indexItem number
---@param item ReviewItem
function P_ReviewPanel:CancelSound(soundEventName, indexItem, item)
    if soundEventName and soundEventName == self.curSoundEventNamePlaying then
        if item then
            item:OnSoundCanceled()
        end

        if self.handlePlayingSound then
            Game.AkAudioManager:StopEvent(self.handlePlayingSound)
        end

        self.handlePlayingSound = nil
        self.curIndexItemPlayingSound = nil
        self.curSoundEventNamePlaying = nil
        self.curItemPlayingSound =nil
    end    
end

---@param index number
---@param reviewItemUI ReviewItem
---@param reviewItemData DialogueReviewItem
function P_ReviewPanel:Select(index, reviewItemUI, reviewItemData)
    local curIndex = self.curItemIndexSelected
    if not curIndex or curIndex ~= index then
        self.ListView:Sel(index)
    end
end

function P_ReviewPanel:OnClickBtnBack()
    self:CloseSelf()
end

function P_ReviewPanel:OnClickBtnInfo()
    local geometry = self.View.WBP_ComPanel.WBP_ComBtnBack_lua.Btn_Info_lua:GetCachedGeometry()
    Game.TipsSystem:ShowTips(Enum.ETipsData.Dialogue_REVIEW_DESC, geometry)
end

return P_ReviewPanel