local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local IDialogueContentUI = kg_require("Gameplay.LogicSystem.NPC.Dialogue.IDialogueContentUI")

local ESlateVisibility = import("ESlateVisibility")
---@class DialogueTalk : UIComponent
---@field view DialogueTalkBlueprint
local DialogueTalk = DefineClass("DialogueTalk", UIComponent, IDialogueContentUI)
local P_DialogueContentPrinter = kg_require("Gameplay.LogicSystem.NPC.P_DialogueContentPrinter") -- 其实是个纯class,没有继承自UI框架

DialogueTalk.eventBindMap = {

}

---打字机默认打印速度
DialogueTalk.__DefaultPrintSpeed__ = 20

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DialogueTalk:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DialogueTalk:InitUIData()
    -- 展示用的options列表
    self.OptionsViewParam = {}
    -- 打字机
    self.ContentPrinter = P_DialogueContentPrinter.new(self.__DefaultPrintSpeed__)
    self.ContentPrinter:SetPrintFinishCallBack(self, self.OnPrintFinished)

    local talkTextWidget = self.userWidget
    self.ContentPrinter:BindWidget(talkTextWidget.RTB_TalkContent_Back_lua, talkTextWidget.RTB_TalkContent_lua, talkTextWidget.RTB_TalkContent2_Back_lua, talkTextWidget.RTB_TalkContent2_lua)
    self.ContentPrinter:SetEnableTwoLinePrinter(true)
    self.bCanSkip = false
end

--- UI组件初始化，此处为自动生成
function DialogueTalk:InitUIComponent()
    ---@type UISimpleList
    self.VB_List_ContentCom = self:CreateComponent(self.view.VB_List_Content, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function DialogueTalk:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DialogueTalk:InitUIView()
    self.view.Img_PageTurn_lua:SetVisibility(ESlateVisibility.Collapsed)

    -- 默认隐藏掉选项列表
    self:SetNormalOptionVisible(false)

    -- 一直Loop动画
    self:PlayAnimation(self.view.Ani_ContinueLoop, nil, nil, nil, 0)
end

function DialogueTalk:Tick(deltaTime)
    self.ContentPrinter:Tick(deltaTime)
end

function DialogueTalk:ShowTalkWidget()
    self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self:ShowTalkContent()
end

function DialogueTalk:HideTalkWidget()
    self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	self:HideTalkContent()
end

---@param title string|nil
---@param talkerName string|nil
---@param content string|nil
function DialogueTalk:ShowContent(content, talkerName, talkerType, bCanSkip, subtitle, small, smallPos, title, bShowTalkerNameInCSStyle, sectionConfig)
    self:ShowTalkWidget()
    -- 格式化处理
    title = title and Game.NPCManager.GetFormatTalkText(title) or nil
    talkerName = talkerName and Game.NPCManager.GetFormatTalkText(talkerName) or nil
    content = content and Game.NPCManager.GetFormatTalkText(content) or nil

    if title then
        self.view.Text_Title_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Title_lua:SetText(title)
    else
        self.view.Text_Title_lua:SetVisibility(ESlateVisibility.Collapsed)
    end

    if talkerName then
        self.view.Text_Name_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Name_lua:SetText(talkerName)
    else
        self.view.Text_Name_lua:SetVisibility(ESlateVisibility.Collapsed)
    end

    self.bCanSkip = sectionConfig.CanSkip

    -- 开始显示台本的时候,先隐藏箭头
    self.view.Img_PageTurn_lua:SetVisibility(ESlateVisibility.Collapsed)
    self.ContentPrinter:SetText(content)
    Game.EventSystem:Publish(EEventTypes.ON_DIALOGUE_PRINT_START)
end

function DialogueTalk:HideTalkContent()
    self.view.CanvasPanel_Dialogue_lua:SetVisibility(ESlateVisibility.Collapsed)
end

function DialogueTalk:ShowTalkContent()
	self.view.CanvasPanel_Dialogue_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function DialogueTalk:SetContentUIStyle(talkerStyle, talkerNameType)
    self.userWidget:Event_UI_Style(talkerStyle, 0, talkerNameType)

    self.view.npc_lua:SetVisibility(talkerStyle == DialogueConst.TALKER_TYPE.NPC and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
    self.view.player_lua:SetVisibility(talkerStyle == DialogueConst.TALKER_TYPE.PLAYER and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
    self.view.aside_lua:SetVisibility(talkerStyle == DialogueConst.TALKER_TYPE.ASIDE and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

function DialogueTalk:ShowContentSkipArrow()
    self.view.Img_PageTurn_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

---@param Options DialogueOption
---@param OptionType number @ 1 普通选项 2 浮动选项
---@param bTimeLimit boolean @ 是否为限时选项
---@param timeLimit number @ 限时选项的时长
---@param choice boolean @ 是否为浮动选项
function DialogueTalk:ShowNormalOptions(Options, OptionType, bTimeLimit, timeLimit, choice)
    self.OptionsViewParam = Options
    self:SetNormalOptionVisible(true)
    -- 刷新选项列表
    self.VB_List_ContentCom:Refresh(Options)
end

function DialogueTalk:SetNormalOptionVisible(bVisible)
    if bVisible then
        self.view.VB_List_Content:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.VB_List_Content:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function DialogueTalk:OnSelectNormalOptions(index)
    -- 选中选项，隐藏掉list
    self:SetNormalOptionVisible(false)
end

-- 设置印刷机的显隐逻辑
function DialogueTalk:SetPrinterVisible(bVisible)
	self.view.Deco_Special:SetVisibility(Visible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

function DialogueTalk:OnPrintFinished()
    ---打印结束的时候,显示箭头
    if self.bCanSkip then
        self.view.Img_PageTurn_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end

    Game.EventSystem:Publish(EEventTypes.ON_DIALOGUE_PRINT_FINISH)
end

return DialogueTalk
