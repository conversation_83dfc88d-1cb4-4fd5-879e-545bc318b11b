local ComFrame = kg_require("Gameplay.LogicSystem.Common.Panel.ComFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class Review_Panel2 : UIPanel
---@field view Review_Panel2Blueprint
local Review_Panel2 = DefineClass("Review_Panel2", UIPanel)


local StringConst = require("Data.Config.StringConst.StringConst")


Review_Panel2.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Review_Panel2:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

---初始化数据
function Review_Panel2:InitUIData()
	---@type table @ 当前播放声音的DialogueReviewItem
	self.curItemPlayingSound = nil
	---@type number @ 当前播放声音的DialogueReviewItem的数据Index
	self.curIndexItemPlayingSound = nil
	---@type string @ 挡墙播放声音的wwise事件名
	self.curSoundEventNamePlaying = nil
end

--- UI组件初始化，此处为自动生成
function Review_Panel2:InitUIComponent()
    ---@type UIListView childScript: ReviewListItem
    self.BgSlot_luaCom = self:CreateComponent(self.view.BgSlot_lua, UIListView)
	---@type ComFrame
	self.WBP_ComFrameCom = self:CreateComponent(self.view.WBP_ComFrame, ComFrame)
end

---UI事件在这里注册，此处为自动生成
function Review_Panel2:InitUIEvent()
	self:AddUIEvent(self.BgSlot_luaCom.onItemSelected, "on_BgSlot_luaCom_ItemSelected")

	self.WBP_ComFrameCom.backCb:Add(self, "OnBackBtnClicked")
	self.WBP_ComFrameCom.tipsCb:Add(self, "OnTipsBtnClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Review_Panel2:InitUIView()
	self.WBP_ComFrameCom:SetBackArea(StringConst.Get(
		"DIALOGUE_REVIEW_UI_TITLE"))
end

---面板打开的时候触发
function Review_Panel2:OnRefresh(...)
	---@type DialogueHistory
	local dialogueHistory = Game.DialogueManagerV2.DialogueHistory
	if dialogueHistory then
		local items = dialogueHistory:GetReviewItems()
		local count = #items
		local listView = self.BgSlot_luaCom
		listView:Refresh(items, 1, {totalCount = count, parentPanel = self})
		listView:ScrollToItemByIndex(count)
		listView:SetSelectedItemByIndex(count, true)
	end
end

--- 面板关闭，有需要可以继承重写方法
function Review_Panel2:OnClose()
	self.curItemPlayingSound = nil
	self.curIndexItemPlayingSound = nil
	self.curSoundEventNamePlaying = nil
	local timer = self.timerPlaySound
	if timer then
		Game.TimerManager:StopTimerAndKill(timer, false)
	end

	-- Game.DialogueManager:SetDialoguePause(false)
end

function Review_Panel2:OnBackBtnClicked()
	self:CloseSelf()
end

function Review_Panel2:OnTipsBtnClicked()
	local geometry = self.view.WBP_ComFrame.WBP_ComBtnBack_lua.Btn_Info_lua:GetCachedGeometry()
	Game.TipsSystem:ShowTips(Enum.ETipsData.Dialogue_REVIEW_DESC, geometry)
end

---@param soundEventName string
---@param indexItem number
---@param item ReviewListItem
function Review_Panel2:PlaySound(soundEventName, indexItem, item)
	assert(not string.isEmpty(soundEventName))
	local duration = Game.AkAudioManager:GetEventDuration(soundEventName)
	if duration <= 0 then
		self:CancelSound(soundEventName, indexItem, item)
		return
	end

	if soundEventName == self.curSoundEventNamePlaying then
		return
	else
		self:CancelSound(self.curSoundEventNamePlaying, self.curIndexItemPlayingSound, self.curItemPlayingSound)
	end

	self.curSoundEventNamePlaying = soundEventName
	self.curIndexItemPlayingSound = indexItem
	self.curItemPlayingSound = item

	self.handlePlayingSound = Game.AkAudioManager:PostEvent2D(soundEventName)
	local function TimerProc(spanTime)
		self:OnSoundEnd(soundEventName, indexItem, item)
	end

	self.timerPlaySound = Game.TimerManager:CreateTimerAndStart(TimerProc, duration * 1000, 1, false, "DialogueReview")

	if item then
		item:OnPlayingSound(true)
	end
end

---@param soundEventName string
---@param indexItem number
---@param item ReviewListItem
function Review_Panel2:OnSoundEnd(soundEventName, indexItem, item)
	if self.curSoundEventNamePlaying ~= soundEventName then
		return
	end

	if item then
		item:OnSoundEnd()
	end

	self.handlePlayingSound = nil
	self.curIndexItemPlayingSound = nil
	self.curSoundEventNamePlaying = nil
	self.curItemPlayingSound = nil

	Game.TimerManager:StopTimerAndKill(self.timerPlaySound, false)
	self.timerPlaySound = nil
end

---@param soundEventName string
---@param indexItem number
---@param item ReviewItem
function Review_Panel2:CancelSound(soundEventName, indexItem, item)
	if soundEventName and soundEventName == self.curSoundEventNamePlaying then
		if item then
			item:OnSoundCanceled()
		end

		if self.handlePlayingSound then
			Game.AkAudioManager:StopEvent(self.handlePlayingSound)
		end

		self.handlePlayingSound = nil
		self.curIndexItemPlayingSound = nil
		self.curSoundEventNamePlaying = nil
		self.curItemPlayingSound = nil
	end
end

--- 此处为自动生成
---@param index number
---@param data table
function Review_Panel2:on_BgSlot_luaCom_ItemSelected(index, data)

end

return Review_Panel2
