local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local IDialogueContentUI = kg_require("Gameplay.LogicSystem.NPC.Dialogue.IDialogueContentUI")
---@class Dialogue_CSLine : UIComponent
---@field view Dialogue_CSLineBlueprint
local Dialogue_CSLine = DefineClass("Dialogue_CSLine", UIComponent, IDialogueContentUI)

local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local ESlateVisibility = import("ESlateVisibility")

Dialogue_CSLine.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Dialogue_CSLine:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Dialogue_CSLine:InitUIData()
    ---@type boolean
    self.bNeedRepositionSmall = false
    
    ---@type FVector2D
    self.smallPos = FVector2D(0, 0)

    ---@type boolean
    self.bSetCSType = false
end

--- UI组件初始化，此处为自动生成
function Dialogue_CSLine:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Dialogue_CSLine:InitUIEvent()
    self:AddUIEvent(self.view.title_lua.OnAbsoluteSizeChanged, "on_Title_lua_AbsoluteSizeChanged")
    self:AddUIEvent(self.view.title2_lua.OnAbsoluteSizeChanged, "on_Title2_lua_AbsoluteSizeChanged")
    self:AddUIEvent(self.view.Canvas_Bottom_lua.OnAbsoluteSizeChanged, "on_Canvas_Bottom_lua_AbsoluteSizeChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Dialogue_CSLine:InitUIView()
    self.bSetCSType = self.view.CSType
    self.view.BP_NPCCS( self.view._userWidget,self.bSetCSType)
end

---组件刷新统一入口
function Dialogue_CSLine:Refresh(content, name, bCanSkip, subtitle, small, smallPos, title)     
    -- self:ShowContent(content, name, bCanSkip, subtitle, small, smallPos, title)
end

function Dialogue_CSLine:ShowContent(content, name, speakerType, bCanSkip, subtitle, small, smallPos, title, bShowTalkerNameInCSStyle, sectionConfig)
    local npcManager = Game.NPCManager
    local view = self.view

    local strContent = npcManager and npcManager.GetFormatTalkText(content) or content
    local strSubtitle = npcManager and npcManager.GetFormatTalkText(subtitle) or subtitle
    if bShowTalkerNameInCSStyle then
        local strName = npcManager and npcManager.GetFormatTalkText(name) or name
        strContent = string.format("%s: %s", strName, strContent)
    end

    view.title_lua:SetText(strContent)
    view.subtitle_lua:SetText(strSubtitle)
    view.small_lua:SetText(small)

    view.title2_lua:SetText(strContent)
    view.subtitle2_lua:SetText(strSubtitle)
    view.small2_lua:SetText(small)

    -- 刷新小字位置
    self.bNeedRepositionSmall = true
    self.smallPos = smallPos

    -- 切换样式
    self:switchStyle()
end

--- 此处为自动生成
---@param sizeX float
---@param sizeY float
function Dialogue_CSLine:on_Title_lua_AbsoluteSizeChanged(sizeX, sizeY)
    if not self.bSetCSType and self.bNeedRepositionSmall then
        self:UpdatePos(self.view.small_lua, self.view.title_lua, self.smallPos)
        self.bNeedRepositionSmall = false
    end
end

--- 此处为自动生成
---@param sizeX float
---@param sizeY float
function Dialogue_CSLine:on_Title2_lua_AbsoluteSizeChanged(sizeX, sizeY)
    if self.bSetCSType and self.bNeedRepositionSmall then
        self:UpdatePos(self.view.small2_lua, self.view.title2_lua, self.smallPos)
        self.bNeedRepositionSmall = false
    end
end

--- 此处为自动生成
---@param sizeX float
---@param sizeY float
function Dialogue_CSLine:on_Canvas_Bottom_lua_AbsoluteSizeChanged(sizeX, sizeY)
    self:switchStyle()
end

function Dialogue_CSLine:HideTalkContent()
    self.view.Canvas_Content1:SetVisibility(ESlateVisibility.Collapsed)
    self.view.Canvas_Content2:SetVisibility(ESlateVisibility.Collapsed)
end

function Dialogue_CSLine:ShowTalkContent()
    self.view.Canvas_Content1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.view.Canvas_Content2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end
----------------------------------------------------------------------------------------
--- private
----------------------------------------------------------------------------------------

---@private
function Dialogue_CSLine:switchStyle()
    local view = self.view
    local size = SlateBlueprintLibrary.GetLocalSize(view.Canvas_Bottom_lua:GetCachedGeometry())
    if size.Y > 131.5 then
        if not self.bSetCSType then
            self.bSetCSType = true
            self.view.BP_NPCCS(self.view._userWidget, true)
        end
    else
        if self.bSetCSType then
            self.view.BP_NPCCS(self.view._userWidget, false)
            self.bSetCSType = false
        end
    end
end

---@private
---@param small UKGTextBlock
---@param widgetTitle UKGRichTextBlock
---@param smallPos string
function Dialogue_CSLine:UpdatePos(small, widgetTitle, smallPos)
    if string.isEmpty(smallPos) then
        return
    end
    local location = FVector2D(0, 0)
    local size = FVector2D(0, 0)

    widgetTitle:GetRunLocationAndSize(smallPos, location, size)
    location.X = location.X + size.X / 2
    local newPos = import("SlateBlueprintLibrary").AbsoluteToLocal(small:GetCachedGeometry(), location)
    local slot = small.slot
    local oldPos = slot:GetPosition()
    local targetPos = oldPos + newPos
    local smallSize = small:GetDesiredSize()
    targetPos.X = targetPos.X - smallSize.X / 2
    targetPos.Y = targetPos.Y - smallSize.Y
    slot:SetPosition(targetPos)
end


return Dialogue_CSLine
