local ESlateVisibility = import("ESlateVisibility")

local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class DialoguePerformerDesc_Panel : UIPanel
---@field view.WBP_PerformerDescription DialoguePerformerDesc_PanelBlueprint
local DialoguePerformerDesc_Panel = DefineClass("DialoguePerformerDesc_Panel", UIPanel)

DialoguePerformerDesc_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DialoguePerformerDesc_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DialoguePerformerDesc_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DialoguePerformerDesc_Panel:InitUIComponent()

end

---UI事件在这里注册，此处为自动生成
function DialoguePerformerDesc_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DialoguePerformerDesc_Panel:InitUIView()
    self.view.WBP_PerformerDescription.CanvasPanel_1_lua:SetVisibility(ESlateVisibility.Collapsed)
    self.view.WBP_PerformerDescription.CanvasPanel_2_lua:SetVisibility(ESlateVisibility.Collapsed)
    self.view.WBP_PerformerDescription.CanvasPanel_3_lua:SetVisibility(ESlateVisibility.Collapsed)
    self.view.WBP_PerformerDescription.BG:SetVisibility(ESlateVisibility.Collapsed)
end

---面板打开的时候触发
function DialoguePerformerDesc_Panel:OnRefresh(descID, offsetX, offsetY)
    local descData = Game.TableData.GetDialogueActorDescriptionDataRow(descID)
    if not descData then
        Log.WarningFormat("[OnRefresh] %s data not found", descID)
        self:CloseSelf()
        return
    end

    local offset = self.view.WBP_PerformerDescription.Slot:GetOffsets()
    offset.Left = 0
    offset.Right = offsetX * -1
    offset.Top = 0
    offset.Bottom = offsetY * -1
    self.view.WBP_PerformerDescription.Slot:SetOffsets(offset)

    local canvasToShow

    if descData.Type == 1 then
        canvasToShow = self.view.WBP_PerformerDescription.CanvasPanel_1_lua
        self.view.WBP_PerformerDescription.des_1_1_lua:SetText(descData.Text1)
        self.view.WBP_PerformerDescription.des_1_2_lua:SetText(descData.Text2)
        self.view.WBP_PerformerDescription.des_1_3_lua:SetText(descData.Text3)
        self.view.WBP_PerformerDescription.des_1_4_lua:SetText(descData.Text4)
        self.view.WBP_PerformerDescription.des_1_5_lua:SetText(descData.Text5)
        self.view.WBP_PerformerDescription.des_1_6_lua:SetText(descData.Text6)
        self:PlayAnimation(self.view.WBP_PerformerDescription.Ani_Fadein_1, nil, self.view.WBP_PerformerDescription)
    elseif descData.Type == 2 then
        canvasToShow = self.view.WBP_PerformerDescription.CanvasPanel_2_lua
        self.view.WBP_PerformerDescription.des_2_1_lua:SetText(descData.Text1)
        self.view.WBP_PerformerDescription.des_2_2_lua:SetText(descData.Text2)
        self:PlayAnimation(self.view.WBP_PerformerDescription.Ani_Fadein_2, nil, self.view.WBP_PerformerDescription)
    end

    if not canvasToShow then
        Log.WarningFormat("[OnRefresh] invalid type %s", descData.Type)
        self:CloseSelf()
        return
    end

    canvasToShow:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

return DialoguePerformerDesc_Panel
