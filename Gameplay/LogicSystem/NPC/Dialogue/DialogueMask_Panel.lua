local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class DialogueMask_Panel : UIPanel
---@field view DialogueMask_PanelBlueprint
local DialogueMask_Panel = DefineClass("DialogueMask_Panel", UIPanel)

DialogueMask_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DialogueMask_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DialogueMask_Panel:InitUIData()
    self.fadeInSpeed = nil
    self.fadeOutSpeed = nil
	
	self.BlackScreenTimer = nil
end

--- UI组件初始化，此处为自动生成
function DialogueMask_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DialogueMask_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DialogueMask_Panel:InitUIView()
end

---颜色类型,按需扩展
DialogueMask_Panel.__ColorType__ = {
    WHITE = 0,
    BLACK = 1,
}

DialogueMask_Panel.__WhiteColor__ = FLinearColor(1, 1, 1, 1)
DialogueMask_Panel.__BlackColor__ = FLinearColor(0, 0, 0, 1)

---面板打开的时候触发
function DialogueMask_Panel:OnRefresh(colorType, fadeInTime, fadeOutTime, durationTime)
    local color
    if colorType == self.__ColorType__.WHITE then
        color = self.__WhiteColor__
    else
        color = self.__BlackColor__
    end

    self.view.Border_PictureShow:SetBrushColor(color)

    self.fadeInSpeed = 1 / fadeInTime
    self.fadeOutSpeed = 1 / fadeOutTime


	Log.Debug("[DialogueMask_Panel] Play Ani_FadeIn.")
	
	local WaitTime = durationTime - fadeInTime - fadeOutTime
	-- 淡入和淡出之间可能会有持续的黑屏过程( Section DurationTime 可能会大于
    self:PlayAnimation(self.view.Ani_Fade, function()
		if WaitTime <= 0 then
			Log.Debug("[DialogueMask_Panel] Play Ani_FadeIn End.")
			self:OnFadeInEnd()
		else
			self.BlackScreenTimer = self:StartTimer("BlackScreenTimer", function()
				self:OnFadeInEnd()
			end, WaitTime * 1000, 1)
		end
    end, nil, nil, nil, EUMGSequencePlayMode.Forward, self.fadeInSpeed)
end

function DialogueMask_Panel:OnFadeInEnd()
	Log.Debug("[DialogueMask_Panel] Play Ani_FadeOut.")
    self:PlayAnimation(self.view.Ani_Fade, function()
        self:OnFadeOutEnd()
    end, nil, nil, nil, EUMGSequencePlayMode.Reverse, self.fadeOutSpeed)
end

function DialogueMask_Panel:OnFadeOutEnd()
	Log.Debug("[DialogueMask_Panel] Play Ani_FadeOut End.")
    self:CloseSelf()
end

function DialogueMask_Panel:OnClose()
    self:StopAllAnimations()
end

return DialogueMask_Panel
