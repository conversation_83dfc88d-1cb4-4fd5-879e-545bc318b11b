local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class DialogueTarotCard_Panel : UIPanel
---@field view DialogueTarotCard_PanelBlueprint
local DialogueTarotCard_Panel = DefineClass("DialogueTarotCard_Panel", UIPanel)

DialogueTarotCard_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DialogueTarotCard_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DialogueTarotCard_Panel:InitUIData()
    self.bHasBlackBg = false
end

--- UI组件初始化，此处为自动生成
function DialogueTarotCard_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DialogueTarotCard_Panel:InitUIEvent()
    Game.EventSystem:AddListener(EEventTypes.CLOSE_DIALOGUE_TAROT_CARD_PANEL, self, "OnCloseDialogueTarotCardPanel")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DialogueTarotCard_Panel:InitUIView()
end

-- todo:考虑预加载图片资源
---面板打开的时候触发
function DialogueTarotCard_Panel:OnRefresh(bHasBlackBg, imagePath)
    self.bHasBlackBg = bHasBlackBg

    self.view.Frame:SetVisibility(ESlateVisibility.Collapsed)
    self:SetImage(self.view.Card, imagePath)

    self:PlayAnimation(self.view.Ani_FadeCard)

    if self.bHasBlackBg then
        self:PlayAnimation(self.view.Ani_FadeBorder)
    end
end

function DialogueTarotCard_Panel:OnClose()
    Game.EventSystem:RemoveObjListeners(self)
end

function DialogueTarotCard_Panel:OnCloseDialogueTarotCardPanel()
    self:PlayAnimation(self.view.Ani_FadeCard, nil, nil, nil, nil, EUMGSequencePlayMode.Reverse)

    if self.bHasBlackBg then
        self:PlayAnimation(self.view.Ani_FadeBorder, function()
            self:CloseSelf()
        end, nil, nil, nil, EUMGSequencePlayMode.Reverse)
    else
        self:CloseSelf()
    end
end

return DialogueTarotCard_Panel
