kg_require("Gameplay.LogicSystem.NPC.P_NPCDialogueOptionView")
kg_require("Gameplay.LogicSystem.NPC.P_NPCDialogueOptionView")
kg_require("Gameplay.LogicSystem.NPC.P_NPCDialogueOptionView")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
---@class P_NPCDialogueOption : UIComponent
--- @field public View WBP_NPCBtnSelectView
local P_NPCDialogueOption = DefineClass("P_NPCDialogueOption", UIComponent)

function P_NPCDialogueOption:Refresh(index, Params, selected)
    self.index = index
    --对话选项复用了其他UI，需要重置图标
    --local IconPathBG = UIAssetPath.UI_NPC_Img_DialogueGlow
    --self:SetImage(self.View.Img_SelectBg, IconPathBG)

    local IconPath = UIAssetPath.UI_NPC_Img_Dialogue
    self:SetImage(self.View.Img_Option, IconPath)
	xpcall(self.PlayAnimationForward, _G.CallBackError, self, self.View.WidgetRoot, self.View.Ani_in)
	if index == 1 then
		xpcall(self.PlayAnimationForward, _G.CallBackError, self, self.View.WidgetRoot, self.View.Ani_Hover)
	end
end

function P_NPCDialogueOption:OnClick_Btn_Select()
    if self.View.Ani_click then
        self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_click, 1, nil, function()
            --发送独立的对话选择事件
            UI.Invoke("P_Dialogue", "OnClick_SB_NPCInteractItems", nil, self.index) 
        end)
    else
        --发送独立的对话选择事件
        UI.Invoke("P_Dialogue", "OnClick_SB_NPCInteractItems", nil, self.index)
    end
end

function P_NPCDialogueOption:OnPressed_Btn_Select()
    local Widget = self.View.WidgetRoot
    --self:PlayAnimation(Widget, Widget.Ani_NpcHover_Pingpang, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function P_NPCDialogueOption:OnReleased_Btn_Select()
    local Widget = self.View.WidgetRoot
    --self:PlayAnimation(Widget, Widget.Ani_NpcHover_Pingpang, 0.0, 1, EUMGSequencePlayMode.Reverse, 1, false)
end

function P_NPCDialogueOption:OnHovered_Btn_Select()
	self.View.hover2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.View.KCanv_Selected:SetVisibility(ESlateVisibility.HitTestInvisible)
    self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_Hover)
    -- self:PlayAnimation(Widget, Widget.Ani_Select_In, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self.ShowPlayHoverLoop = true
    -- self:StartTimer("PlayHoverLoop", function()
    --     if self.ShowPlayHoverLoop then
    --         self:PlayAnimation(Widget, Widget.Ani_Select_Loop, 0.0, 0, EUMGSequencePlayMode.Forward, 1, false)
    --     end
    -- end,  750, 1)
end

function P_NPCDialogueOption:OnUnhovered_Btn_Select()
	self.View.hover2:SetVisibility(ESlateVisibility.Hidden)
	self.View.KCanv_Selected:SetVisibility(ESlateVisibility.Hidden)
	-- self:PlayAnimation(Widget, Widget.Ani_Select_In, 0.0, 1, EUMGSequencePlayMode.Reverse, 3, false)
    -- self:StopAnimation(Widget, Widget.Ani_Select_Loop)
    self.ShowPlayHoverLoop = false
end

function P_NPCDialogueOption:OnCreate(forbidClick)
    ---@type table @参数
    self.Params = nil
    ---@type number @索引
    self.index = nil
	self.View.WidgetRoot:Event_UI_Type(0, 1)
end


return P_NPCDialogueOption
