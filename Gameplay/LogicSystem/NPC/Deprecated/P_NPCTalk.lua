--todo 待删除，相关逻辑移动到NPCTalkPanel中
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")
local KismetInputLibrary = import("KismetInputLibrary")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local UIFunctionLibrary = import("UIFunctionLibrary")
local C7FunctionLibrary = import("C7FunctionLibrary")

---@class P_NPCTalk : WBP_NPC
local P_NPCTalk = DefineClass("P_NPCTalk", UIController)
local P_NPCSelectItem = kg_require "Gameplay.LogicSystem.NPC.P_NPCSelectItem"
local StringConst = require "Data.Config.StringConst.StringConst"
local QuestConditionUtils = kg_require("Gameplay.LogicSystem.Quest.QuestConditionUtils")

P_NPCTalk.OptionType = {
    InsideFirst = 1, --插入到第一句
    InsideEnd = 1, --插入到最后一句
}

P_NPCTalk.eventBindMap = {
    [_G.EEventTypes.LEVEL_ON_LEVEL_LOADED] = "endTalk",
    [_G.EEventTypes.NPC_RESUME_INTERACT] = "OnResumeInteract",
    [_G.EEventTypes.LEVEL_ON_ROLE_LOAD_COMPLETED] = "endTalk",
    [_G.EEventTypes.ROLE_ACTION_INPUT_EVENT] = "KeyOnInput",
}

---界面刷新的类型
P_NPCTalk.RefreshType = {
	Entity = 1,
	SystemAction = 2,
}

P_NPCTalk.LastNpcCfgId = nil

---@class P_NPCTalk.SysActionParams
---@field bTalkGroup boolean 是否talkgroup
---@field id number talkId或talkGroupId
---@field npcCfgId number npc的templateId

function P_NPCTalk:OnCreate()
    ---@type number @进入对话的ID
    self.talkID = nil
    ---@type number @当前对话的ID
    self.curTalkID = nil
    ---@type boolean @是否是提交道具
    self.bSubmit = nil
    ---@type boolean @是否是
    self.bShowHint = nil
    ---@type number @当前选中
    self.showHintIndex = 1
	self.curHoverIndex = nil  -- 当前hover的下标
    ---@type table @对话信息
    self.talkInfo = {}
    ---@type string @NPCEntityID
    self.npcEntityID = nil
    ---@type number @NPCCfgID
    self.npcCfgID = nil
    ---@type boolean @是否是选项阶段
    self.talkOption = nil
    ---@type table @选项数据
    self.talkOptionDatas = {}

	self.MainPlayerAnimFeatureID = nil
	self.NpcAnimFeatureID = nil
	
    ---@type boolean @是否等待关闭
    self.bWaitForFun = false

	self.View.WBP_NPCTalkText.WBP_NPCCamera.WidgetRoot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.View.WBP_NPCTalkText:Event_UI_Style(1, 0, 0)
	
	---@type ListView3 @选项List
    self.Interactlist = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.SelectList, P_NPCSelectItem, "SelectList")
	
	self.refreshType = P_NPCTalk.RefreshType.Entity
	self.talkGroupId = nil
	self.talkIdList = {}
	self.curTalkIdx = 1
	
	self.talkEnded = false
end

function P_NPCTalk:OnOpen()
    UIManager:GetInstance():HideAllPanel(UIConst.HIDE_PANELS_SOURCE_TYPE.NPC, {self.uid, "ScreenInput_Panel"})
    local root = self.View.WBP_NPCTalkText
    self:PlayAnimation(root, root.Ani_Dialog, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
        self.View.WBP_NPCTalkText.WBP_NPCCamera.WidgetRoot:SetVisibility(ESlateVisibility.Visible)
    end)
    self.View.icon_wheel:SetVisibility(ESlateVisibility.Collapsed)

    self.bShowHint = false

	-- 交互说这里不需要了，都是false
    --if C7FunctionLibrary.IsC7Editor() then
    --    if UIFunctionLibrary.GetPreviewPlatform
    --        and UIFunctionLibrary.GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC then
    --        self.bShowHint = true
    --    end
    --else
    --    if not PlatformUtil.IsMobilePlatform() then
    --        self.bShowHint = true
    --    end
    --end
end

function P_NPCTalk:OnRefresh(entity, sysActionParams)
	self.talkEnded = false
    self.View.WBP_NPCTalkText.CanvasPanel_Dialogue:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --清除移动输入状态
    Game.ScreenInputManager.InitScreenInputState()

    self.bSubmit = false
	if sysActionParams ~= nil then
		self.refreshType = P_NPCTalk.RefreshType.SystemAction
		self:refreshBySysAction(sysActionParams)
	elseif entity ~= nil and entity ~= -1 then
		self.refreshType = P_NPCTalk.RefreshType.Entity
		self:refreshByEntity(entity)
	else
		Log.ErrorFormat("P_NPCTalk refresh param error")
	end
end

function P_NPCTalk:refreshNpcEntity(entity, triggerNewbie, closeSelfWhenNoNpc)
	if entity then
		local entityUID = entity:uid()
		self.npcCfgID = entity.TemplateID
		self.npcEntityID = entity.eid
		self.npcEntityUID = entityUID
		P_NPCTalk.LastNpcCfgId = entityUID
	else
		self.npcCfgID = nil
		P_NPCTalk.LastNpcCfgId = nil
		self.npcEntityID = nil
		self.npcEntityUID = nil
		Log.Warning("Can't Find NPC When Talk")
		if closeSelfWhenNoNpc then
			self:CloseSelf()
		end
		return false
	end

	Game.CameraManager:EnableInteractiveCamera(true, entity)
	xpcall(HandleLookAtLoc, _G.CallBackError, Game.me, entity, true)
	if entity.OnStartTalkWithPlayer then
		entity:OnStartTalkWithPlayer()
	end
	return true
end

function P_NPCTalk:refreshByEntity(entity)
	local res = self:refreshNpcEntity(entity, true, true)
	if res == false then
		return
	end
	self:buildContent()
	self:showTalkContent()

	-- 下面是不是只有这个任务相关联的才使用
	if self.bWaitForFun then
		self.Interactlist:SetData(0)
		--Game.NPCSystem.StopTask(NpcActor)
		self.bWaitForFun = false
	end
	Game.me:ReqEnterNpcTalk(self.npcCfgID, self.npcEntityID)
end

---@param sysActionParams P_NPCTalk.SysActionParams
function P_NPCTalk:refreshBySysAction(sysActionParams)
	local bTalkGroup, id, npcCfgId = sysActionParams.bTalkGroup, sysActionParams.id, sysActionParams.npcCfgId
	if npcCfgId then
		--策划说如果没有配置npcCfgId，则使用上次的npcCfgId
		if npcCfgId == 0 and P_NPCTalk.LastNpcCfgId ~= nil then
			npcCfgId = P_NPCTalk.LastNpcCfgId
		end
		local npcEnt = Game.NPCManager.GetFirstTaskNpcEntByCfgId(npcCfgId)
		if npcEnt then
			self:refreshNpcEntity(npcEnt)
		end	
	end
	if bTalkGroup then
		self.talkGroupId = id
		table.clear(self.talkIdList)
		local talkGroupData = Game.TableData.GetTalkGroupDataRow(id)
		for _, info in ipairs(talkGroupData.TalkGroup) do
			table.insert(self.talkIdList, info.TalkID)
		end
	else
		self.talkGroupId = nil
		self.talkIdList = {id}
	end
	self.curTalkIdx = 0
	self:tryShowNextTalkContentBySysAction()
end

function P_NPCTalk:checkTalkGroup()
	return self.talkGroupId ~= nil
end

function P_NPCTalk:tryShowNextTalkContentBySysAction()
	self.curTalkIdx = self.curTalkIdx + 1
	if self:checkTalkGroup() then
		-- 检查下条件
		local condition = Game.TableData.GetTalkGroupDataRow(self.talkGroupId).Condition
		if condition == nil or QuestConditionUtils.CheckCondition(condition, self.npcEntityID) then
			self:doShowNextTalkContentBySysAction()
		else
			-- 条件不满足，直接结束
			self:endTalk()
		end
	else
		self:doShowNextTalkContentBySysAction()
	end
end

function P_NPCTalk:doShowNextTalkContentBySysAction()
	self:buildSimpleContent(self.talkIdList[self.curTalkIdx])
	self:showTalkContent()
end

function P_NPCTalk:checkRefreshByEntity()
	return self.refreshType == P_NPCTalk.RefreshType.Entity
end

function P_NPCTalk:checkRefreshBySysAction()
	return self.refreshType == P_NPCTalk.RefreshType.SystemAction
end

---@private 构造对话数据
function P_NPCTalk:buildContent()
    table.clear(self.talkInfo)
    local _, kind, talkID = Game.NPCSystem:GetNpcTalkContent(self.npcCfgID, self.npcEntityID)
    if kind == Game.NPCSystem.EDynamicKind.Dialogue then
        Log.WarningFormat("Error TalkKind When NpcTalk(NPCID:%s,DialogueID:%s)", self.npcCfgID, talkID)
        self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
    elseif kind == Game.NPCSystem.EDynamicKind.Submit then
        Log.WarningFormat("Error TalkKind When NpcTalk(NPCID:%s,TalkID:%s)", self.npcCfgID, talkID)
        self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
    elseif kind == Game.NPCSystem.EDynamicKind.SubmitDialogue then
        Log.WarningFormat("Error TalkKind When NpcTalk(NPCID:%s,TalkID:%s)", self.npcCfgID, talkID)
        self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
    else
        self:buildSimpleContent(talkID)
    end
end

function P_NPCTalk:buildSimpleContent(talkID)
	table.clear(self.talkInfo)
	local talkData = Game.TableData.GetTalkDataRow(talkID)
	if (not talkData) or (not talkData[1]) then
		Log.WarningFormat("【NPCTalk】Error TalkID:%s",talkID)
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
	else
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
		self.talkID = talkID
		self.curTalkID = talkID
		self.talkInfo.TalkID = talkID

		local playKind = talkData[1].PlayKind
		if playKind == Game.NPCSystem.EPlayKind.InOrder then
			self.talkInfo.Order = 1
		elseif playKind == Game.NPCSystem.EPlayKind.Random then
			self.talkInfo.Order = math.random(1, #talkData)
		end
	end
end

---@private 点击选项选项
---@param optionIndex number @选项索引
function P_NPCTalk:onClickOption(optionIndex)
    local optionData = self.talkOptionDatas[optionIndex]
    if not optionData then
        self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
        Log.WarningFormat("Error OptionIndex(OptionIndex:%s, OptionsCount:%s)", optionIndex, #self.talkOptionDatas)
        return
    end

    local talkData = Game.TableData.GetTalkDataRow(self.talkInfo.TalkID)
    local params = optionData.Params

    local TalkOptionType = Enum.TalkOptionType
    if not params then
        if talkData[self.talkInfo.Order+1] then
            self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
            self.talkInfo.Order = self.talkInfo.Order + 1
        else
            self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
        end
        self:showTalkContent()
    else
        for i = 1, #params do
            local param = params[i]
            if param.NextKind == TalkOptionType.Content then
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
                if param.OptionID == -1 then
                    --插入的下一页按钮
                    self.talkInfo.Order = self.talkInfo.Order + 1
                else
                    local talkID = param.OptionID
                    local talkData = Game.TableData.GetTalkDataRow(talkID)
                    if talkData then
                        local talkCfg = talkData[1]
                        self.talkInfo.TalkID = talkID
                        self.curTalkID = talkID
                        if talkCfg.PlayKind == Game.NPCSystem.EPlayKind.InOrder then
                            self.talkInfo.Order = 1
                        elseif talkCfg.PlayKind == Game.NPCSystem.EPlayKind.Random then
                            self.talkInfo.Order = math.random(1,#talkData)
                        else
                            self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
                        end
                    else
                        self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
                    end
                end
            elseif param.NextKind == TalkOptionType.DialogueType then
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Dialogue
                self.talkInfo.DialogueID = param.DialogueID
                self.talkInfo.SubmitID = param.SubmitID
				self.talkInfo.NewSubmitCfg = param.NewSubmitCfg
				self.talkInfo.QuestID = param.QuestID
				self.talkInfo.CondIdx = param.CondIdx
                self.talkInfo.RingID = param.RingID
                self.talkInfo.ReceiveRingID = param.ReceiveRingID
            elseif param.NextKind == TalkOptionType.JumpType then
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Jump
                self.talkInfo.JumpID = param.JumpID
            elseif param.NextKind == TalkOptionType.GuideGetMoney then
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.NONE
                self.talkInfo.Func = function()
                    Game.GuildSystem:HandleWageGet()
                end
            elseif param.NextKind == TalkOptionType.GuildMerge then
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.WaitForEnd
                self.talkInfo.Page = "P_GuildMerge"
                self.talkInfo.FunctionParam = param.FunctionParam
            elseif param.NextKind == TalkOptionType.ItemSubmit then
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.WaitForEnd
                self.talkInfo.Page = UIPanelConfig.SubmitNew_Panel
                self.talkInfo.FunctionParam = {
                    ItemSubmitID = param.ItemSubmitID,
					NewSubmitCfg = param.NewSubmitCfg,
					CondIdx = param.CondIdx,
					QuestID = param.QuestID,
                    TaskRingID = param.QuestID and Game.QuestSystem:GetRingIDByQuestID(param.QuestID) or nil
                }
            elseif param.NextKind == TalkOptionType.SystemAction then
                table.clear(self.talkInfo)
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
                self.talkInfo.SystemActionID = param.SystemActionID
            elseif param.NextKind == TalkOptionType.UnlockTeleport then
                table.clear(self.talkInfo)
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
                self.talkInfo.UnlockTeleport = true
            elseif param.NextKind == TalkOptionType.DebugSystemAction then
                table.clear(self.talkInfo)
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
                self.talkInfo.DebugSystemActionList = param.DebugSystemActionList
            elseif param.NextKind == TalkOptionType.SystemActionEnum then
                table.clear(self.talkInfo)
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
                self.talkInfo.SystemActionList = param.SystemActionList
            elseif param.NextKind == TalkOptionType.Finally then
                table.clear(self.talkInfo)
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
            end
            self:showTalkContent()
        end
    end
end

---@private 点击下一步
function P_NPCTalk:onClickContent()
    table.clear(self.talkOptionDatas)
    local talkData = Game.TableData.GetTalkDataRow(self.talkInfo.TalkID)
    if self.talkInfo.TalkID == self.talkID and self.talkInfo.Order == 1 and self:checkRefreshByEntity() then
        Game.NPCSystem:GetFirstTalkOptions(self.npcCfgID, self.talkOptionDatas, self.npcEntityID)
    else
        Game.NPCSystem:GetTalkOptions(self.talkInfo.TalkID, self.talkInfo.Order, self.talkOptionDatas, self.npcEntityID)
    end
    
    if #self.talkOptionDatas > 0 then
        self.talkInfo.TalkType = Game.NPCSystem.TalkType.Option
    else
        local talkCfg = talkData[self.talkInfo.Order]
        if talkCfg.PlayKind == Game.NPCSystem.EPlayKind.Random then
            --当前是随机播放的，所以为最后一句
            self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
        elseif talkCfg.PlayKind == Game.NPCSystem.EPlayKind.InOrder then
            if talkData[self.talkInfo.Order+1] then
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
                self.talkInfo.Order = self.talkInfo.Order + 1
            else
                self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
            end
        else
            self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
        end
    end
end

---@private 更新任务数据
function P_NPCTalk:updateTalkInfo(optionIndex)
    if optionIndex then
        --上文是选项
        self:onClickOption(optionIndex)
    else
        --上文是文本
		
		-- 如果是sysAction且是talkGroup，那么直接显示下一个
		if self:checkRefreshBySysAction() and self:checkTalkGroup() then
			self:tryShowNextTalkContentBySysAction()
			return
		end
		
		-- 走之前的正常逻辑
        self:onClickContent()
        self:showTalkContent()
    end
end

---@private 显示文本
function P_NPCTalk:showContent()
    local talkInfo = self.talkInfo
    self.Interactlist:SetData(0)
    self.View.SelectList:SetVisibility(ESlateVisibility.Collapsed)
    self.View.icon_wheel:SetVisibility(ESlateVisibility.Collapsed)
    local talkData = Game.TableData.GetTalkDataRow(talkInfo.TalkID)
    if not talkData then
        Log.WarningFormat("Error TalkID:%s", talkInfo.TalkID)
        return self:endTalk()
    end
    self:StopAnimation(self.View.WBP_NPCTalkText, self.View.WBP_NPCTalkText.Ani_Dialogue_Npc_Loop)
    self:PlayAnimation(self.View.WBP_NPCTalkText, self.View.WBP_NPCTalkText.Ani_Dialogue_Npc_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
        self:PlayAnimation(self.View.WBP_NPCTalkText, self.View.WBP_NPCTalkText.Ani_Dialogue_Npc_Loop, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
    end)
    local talkCfg = talkData[talkInfo.Order]
    if talkCfg then
        local talkText = Game.NPCSystem.GetFormatTalkText(talkCfg.Text)
        local NpcInfo = Game.TableData.GetNpcInfoDataRow(self.npcCfgID)

        local bnpc = true
		---npc是否有名称，0是有，1是无
		self.View.WBP_NPCTalkText.NameType = 0
        if talkCfg.NpcName ~= "" then
            if talkCfg.NpcName ~= "-1" then
                self.View.WBP_NPCTalkText.Text_Name:SetVisibility(ESlateVisibility.Visible)
                self.View.WBP_NPCTalkText.Text_Name:SetText(talkCfg.NpcName)
                self.View.WBP_NPCTalkText.TextType = 0
            else
                self.View.WBP_NPCTalkText.Text_Name:SetVisibility(ESlateVisibility.Visible)
                self.View.WBP_NPCTalkText.Text_Name:SetText((GetMainPlayerPropertySafely("Name")))
                self.View.WBP_NPCTalkText.TextType = 1
                bnpc = false
            end
        elseif NpcInfo then
            self.View.WBP_NPCTalkText.Text_Name:SetVisibility(ESlateVisibility.Visible)
            self.View.WBP_NPCTalkText.Text_Name:SetText(NpcInfo.Name)
            self.View.WBP_NPCTalkText.TextType = 0
        else
            self.View.WBP_NPCTalkText.Text_Name:SetVisibility(ESlateVisibility.Hidden)
			self.View.WBP_NPCTalkText.NameType = 1
        end
        local TalkWidget = self.View.WBP_NPCTalkText
        if TalkWidget.npc_lua then
            TalkWidget.npc_lua:SetVisibility(bnpc and ESlateVisibility.SelfHitTestInvisible or
                ESlateVisibility.Collapsed)
            TalkWidget.player_lua:SetVisibility(not bnpc and ESlateVisibility.SelfHitTestInvisible or
                ESlateVisibility.Collapsed)
            TalkWidget.aside_lua:SetVisibility(ESlateVisibility.Collapsed)
        end
        if self.View.WBP_NPCTalkText.WS_Title then
            self.View.WBP_NPCTalkText.WS_Title:SetActiveWidgetIndex(1)
        end
		if self.View.WBP_NPCTalkText.Text_Title then
			if NpcInfo and not string.isEmpty(NpcInfo.Title) then
				self.View.WBP_NPCTalkText.Text_Title:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
				self.View.WBP_NPCTalkText.Text_Title:SetText(NpcInfo.Title)
			else
				self.View.WBP_NPCTalkText.Text_Title:SetVisibility(ESlateVisibility.Collapsed)
			end	
		end
		
		self.View.WBP_NPCTalkText.RTB_TalkContent:SetVisibility(ESlateVisibility.Visible)
        self.View.WBP_NPCTalkText.RTB_TalkContent:SetText(talkText)
        if StringValid(talkCfg.Animation) then
            if talkCfg.NpcName == "-1" then
                --玩家
                self.MainPlayerAnimFeatureID = Game.me:PlayAnimLibMontage(talkCfg.Animation, nil, false, nil, nil, true)
            else
                --NPC
                local entity = Game.EntityManager:getEntity(self.npcEntityID)
                -- todo @liufan npc坐在椅子上服务器未来做成状态来禁止动画的表演
                if entity and not StringValid(entity.ContinuousInteractInsID) then
                    self.NpcAnimFeatureID = entity:PlayAnimLibMontage(talkCfg.Animation, nil, false, nil, nil, true)
                else
                    Log.WarningFormat("Error Animation Play(TalkID:%d, Order:%d)", self.talkInfo.TalkID, self.talkInfo.Order)
                end
            end
        end
        if talkCfg.NpcName ~= "" and talkCfg.NpcName ~= "-1" then
            local chatText = string.format(StringConst.Get("SOCIAL_CHAT_NPC"), talkCfg.NpcName, talkText)
            chatText = Game.NPCSystem.GetFormatTalkText(chatText)
            Game.ChatSystem:AddChannelSystemInfo(chatText, _G._now(), Enum.EChatChannelData.NEARBY)
        end
    end

    local Widget = self.View.WBP_NPCTalkText.WidgetRoot
    self.View.WBP_NPCTalkText.Img_PageTurn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self:PlayAnimation(Widget, Widget.Ani_ContinueLoop, 0.0, 0, EUMGSequencePlayMode.Forward, 1, false)
end

---@private 根据数据更新对话
function P_NPCTalk:showTalkContent()
	---结束了，不用再播了
	if self.talkEnded then return end
	
    self.talkOption = false
    self.View.WBP_NPCTalkText.VB_Content:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local talkInfo = self.talkInfo

    if talkInfo.TalkType == Game.NPCSystem.TalkType.Content then
        self:showContent()
    elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Option then
        self.talkOption = true
        self.View.SelectList:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.showHintIndex = 1
        self.Interactlist:SetData(#self.talkOptionDatas)
        if #self.talkOptionDatas > 1 and self.bShowHint then
            self.View.icon_wheel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        else
            self.View.icon_wheel:SetVisibility(ESlateVisibility.Collapsed)
        end

        local Widget = self.View.WBP_NPCTalkText.WidgetRoot
        self.View.WBP_NPCTalkText.Img_PageTurn:SetVisibility(ESlateVisibility.Collapsed)
        self:StopAnimation(Widget, Widget.Ani_ContinueLoop)
    elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Jump then
        local jumpID = talkInfo.JumpID
        self:completeTalk()
        Game.UIJumpSystem:JumpToUI(jumpID)
    elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Finally then
        if talkInfo.UnlockTeleport then
            if self.npcEntityID then
                local NPCEntity = Game.EntityManager:getEntity(self.npcEntityID)
                if NPCEntity and NPCEntity.InstanceID then
                    local insID = tonumber(NPCEntity.InstanceID)
                    local NpcToTeleportMap = Game.TableData.Get_NpcToTeleportMap()
                    local teleportInsID = NpcToTeleportMap[insID]
                    if teleportInsID then
                        Game.TeleportManager:ReqUnlockTeleportPointByNpc(tostring(teleportInsID))
                    end
                end
            end
        end
		-- 这里应该需要deepcopy一下，防止之后把talkInfo清空了
        local systemActionList = DeepCopy(talkInfo.SystemActionList)
        local debugSystemActionList = DeepCopy(talkInfo.DebugSystemActionList)
        self:completeTalk()
		-- 调整一下时序
		if systemActionList then
            if Game.me then
                Game.SystemActionManager:TriggerSystemActionList(systemActionList)
            end
		end
		if debugSystemActionList then
			for _, actionItem in ipairs(debugSystemActionList) do
				if Game.me then
					Game.me:TriggerDebugSystemAction(actionItem.FuncName, actionItem.FuncArgInfos)
				end
			end
		end
	elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Dialogue then
        local npcID = self.npcCfgID
        local dialogueID = talkInfo.DialogueID
        local receiveRingID = talkInfo.ReceiveRingID
        local ringID = talkInfo.RingID
        local submitID = talkInfo.SubmitID
		local newSubmitCfg = talkInfo.NewSubmitCfg
		local questID = talkInfo.QuestID
		local condIdx = talkInfo.CondIdx
        if Game.ArrodesDialogueSystem:CheckIsArrodesDialogue(dialogueID) then
            Game.ArrodesDialogueSystem:StartDialogue(dialogueID, self.npcEntityID)
        else
            Game.CinematicManager:StartPlayCinematic({
                CinematicType = Enum.CinematicType.Dialogue,
                AssetID = dialogueID,
                SubmitItemID = submitID,
				NewSubmitCfg = newSubmitCfg,
				QuestID = questID,
				CondIdx = condIdx,
                ReceiveRingID = receiveRingID,
                TaskRingID = ringID,
                NPCID = npcID or nil,
                ActorEntityID = self.npcEntityUID
            })
        end
        self:endTalk()
    elseif talkInfo.TalkType == Game.NPCSystem.TalkType.WaitForEnd then
        self.View.WBP_NPCTalkText.CanvasPanel_Dialogue:SetVisibility(ESlateVisibility.Collapsed)
        table.clear(self.talkOptionDatas)
        self.Interactlist:SetData(#self.talkOptionDatas)
        self.bWaitForFun = true
        if talkInfo.Page then
			-- 这里有点恶心，先这么兼容一下吧
			if talkInfo.Page == UIPanelConfig.SubmitNew_Panel then
				Game.ItemSubmitSystem:ShowItemSubmitUICompatible(talkInfo.FunctionParam)
			else
				UI.ShowUI(talkInfo.Page, talkInfo.FunctionParam)	
			end
        end
    elseif talkInfo.TalkType == Game.NPCSystem.TalkType.NONE then
        if talkInfo.Func then
            talkInfo.Func()
        end
    end
end

---@private 点击屏幕
function P_NPCTalk:onNPCCameraClick()
    if self.talkOption == false then
        --当前不为对话选项界面
        self:updateTalkInfo()
    end
end

---@private 完成对话
function P_NPCTalk:completeTalk()
    Game.EventSystem:Publish(_G.EEventTypes.NPC_ON_FINISH_TALK, self.curTalkID, self.npcCfgID)
    self:endTalk()
end

function P_NPCTalk:OnDragDetected(MyGeometry, PointEvent)
    self.bIsDrag = true
    return nil
end

function P_NPCTalk:OnTouchStarted(InGeometry, InGestureEvent)
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(InGestureEvent)
	if PointerIndex > 0 then
		return UIBase.HANDLED
	end
	local ScreenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(InGestureEvent)
	Game.CameraInputManager:OnTouchStarted(SlateBlueprintLibrary.AbsoluteToLocal(InGeometry, ScreenPos))
    return UIBase.HANDLED
end

function P_NPCTalk:OnTouchMoved(InGeometry, InGestureEvent)
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(InGestureEvent)
	if PointerIndex > 0 then
		return UIBase.HANDLED
	end
	local ScreenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(InGestureEvent)
	Game.CameraInputManager:OnTouchMoved(SlateBlueprintLibrary.AbsoluteToLocal(InGeometry, ScreenPos))
	return UIBase.HANDLED
end

function P_NPCTalk:OnTouchEnded(InGeometry, InGestureEvent)
    local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(InGestureEvent)
    if PointerIndex > 0 then
        return UIBase.HANDLED
    end
	Game.CameraInputManager:OnTouchEnded()
    self:onNPCCameraClick()
    return UIBase.HANDLED
end

--刷新交互列表
function P_NPCTalk:OnRefresh_SelectList(r, index, selected)
    --刷新格子数据
    r:Refresh(self.talkOptionDatas[index], index, selected, index == self.showHintIndex and self.bShowHint)
end

function P_NPCTalk:SetKeyHint(index)
	if self.bShowHint then
		local component = self.Interactlist:GetRendererAt(self.showHintIndex)
		if self.showHintIndex and self.showHintIndex ~= index then
			if component then
				component:SetKeyHint(false)
			end
			self.showHintIndex = 0
		end

		self.showHintIndex = index
		component = self.Interactlist:GetRendererAt(self.showHintIndex)
		if component then
			component:SetKeyHint(true)
		end
	else
		local oldHoverIndex = self.curHoverIndex
		if oldHoverIndex then
			---@type P_NPCSelectItem
			local oldComp = self.Interactlist:GetRendererAt(oldHoverIndex)
			if oldComp then
				oldComp:UnHovered()
			end
		end
		self.curHoverIndex = index
		---@type P_NPCSelectItem
		local comp = self.Interactlist:GetRendererAt(self.curHoverIndex)
		if comp then
			comp:OnHovered()
		end
	end
end

function P_NPCTalk:OnHudInteractAxis(value)
    if self.bShowHint and self.showHintIndex and self.Interactlist.total > 1 then
        if value > 0 then
            --上移
            if self.showHintIndex > 1 then
                local cell = self.Interactlist:GetRendererAt(self.showHintIndex)
                if cell then
                    cell:SetKeyHint(false)
                end
                self.showHintIndex = self.showHintIndex - 1
                local cell = self.Interactlist:GetRendererAt(self.showHintIndex)
                if cell then
                    cell:SetKeyHint(true)
                end
            end
        else
            --下移
            if self.showHintIndex < self.Interactlist.total then
                local cell = self.Interactlist:GetRendererAt(self.showHintIndex)
                if cell then
                    cell:SetKeyHint(false)
                end
                self.showHintIndex = self.showHintIndex + 1
                local cell = self.Interactlist:GetRendererAt(self.showHintIndex)
                if cell then
                    cell:SetKeyHint(true)
                end
            end
        end
    end
end

---@public 道具提交界面关闭
function P_NPCTalk:OnItemSubmitHide()
    self.bSubmit = true
    self:OnResumeInteract()
end

function P_NPCTalk:OnResumeInteract(bCloseBySubmit)
    if self.bWaitForFun then
        if self.bSubmit then
            if self.talkInfo.QuestID then
                local conditionCfg = Game.QuestSystem:GetConditionCfg(self.talkInfo.QuestID, self.talkInfo.QuestConditionIndex)
                if conditionCfg.ItemSubmitID then
                    local playDialogueKind = conditionCfg.PlayDialogueKind
                    if playDialogueKind and playDialogueKind ==  Game.QuestSystem.PlayDialogueEnum.After then
                        self.talkInfo.TalkType = Game.NPCSystem.TalkType.Dialogue
                        self.talkInfo.DialogueID = conditionCfg.DialogueID
                        return self:showTalkContent()
                    end
                end
            end
            self:completeTalk()
        else
            if not bCloseBySubmit then
                self:completeTalk()
            end
        end
    end
end

---@private 结束对话
function P_NPCTalk:endTalk()
	self.talkEnded = true
    Game.ItemSubmitSystem:HideItemSubmitUI()
 
    self.bWaitForFun = false
    table.clear(self.talkInfo)
    self.Interactlist:SetData(0)

    local entity = Game.EntityManager:getEntity(self.npcEntityID)

	Game.CameraManager:EnableInteractiveCamera(false)
	if entity and self.NpcAnimFeatureID then
		entity:StopAnimLibMontage(self.NpcAnimFeatureID)
		self.NpcAnimFeatureID = nil
	end
	if Game.me and self.MainPlayerAnimFeatureID then
		Game.me:StopAnimLibMontage(self.MainPlayerAnimFeatureID)
		self.MainPlayerAnimFeatureID = nil
	end
	
    if entity then
		xpcall(HandleLookAtLoc, _G.CallBackError, Game.me, entity, false)
		if entity.OnFinishTalkWithPlayer then
			entity:OnFinishTalkWithPlayer()
		end
		local npcData = entity:GetEntityConfigData()
		local cfgID, entityID = self.npcCfgID, self.npcEntityID
		if npcData.TurnToPlayer then
			-- 转身结束逻辑也是timer做的，只能丑陋的保持一样的timer，暂时也没取消的逻辑
			Game.TimerManager:CreateTimerAndStart(
				function()  Game.me:ReqLeaveNpcTalk(cfgID, entityID) end,
				1000, 1
			)
		else
			Game.me:ReqLeaveNpcTalk(cfgID, entityID)
		end
	end
	
    self.npcEntityID = nil
    self.npcCfgID = nil
    self.bSubmit = false
    self:CloseSelf()
    Game.EventSystem:Publish(_G.EEventTypes.INTERACTIVE_FINISH)
end

function P_NPCTalk:OnClose()
    self.talkID = nil
    self.curTalkID = nil
    UIManager:GetInstance():RestoreAllPanel(UIConst.HIDE_PANELS_SOURCE_TYPE.NPC)
    local Widget = self.View.WBP_NPCTalkText.WidgetRoot
    self:StopAnimation(Widget, Widget.Ani_ContinueLoop)

    EnableZoomInput(true)
	Game.QuestSystem:LeaveDialogueControlState()
    Game.ScreenInputManager.InitScreenInputState()
    Game.HUDInteractManager:OnNPCUIHide()
end

function P_NPCTalk:ConfigKeyHint(bShow)

end

function P_NPCTalk:KeyOnInput(ActionName, InputEvent)
    --TODO：
    if self.bShowHint and InputEvent == 0  then
        if ActionName == Enum.EInputType.Dialogue_Action then
            --空格
            self:onNPCCameraClick()
        elseif ActionName == Enum.EInputType.OpenInteractivePanel_Action then
            --F键
            local uicomponet = self.Interactlist:GetRendererAt(self.showHintIndex)
            if uicomponet then
                uicomponet:OnItemClicked()
            end
        end
    end
    return UIBase.HANDLED
end

--临时：倒计时系统做出选择
function P_NPCTalk:OnChoiceMadeByCountDown(Index)
    Index = Index or 1
    local uicomponet = self.Interactlist:GetRendererAt(Index)
    if uicomponet then
        uicomponet:OnItemClicked()
    end
end

function P_NPCTalk:OnClickBack()
    self:endTalk()
end

function P_NPCTalk:EscOnClickEvent()
    self:endTalk()
end

function P_NPCTalk:OnLeaveInteract(entityID)
    if self.npcEntityID == entityID then
        self:endTalk()
    end
end

return P_NPCTalk
