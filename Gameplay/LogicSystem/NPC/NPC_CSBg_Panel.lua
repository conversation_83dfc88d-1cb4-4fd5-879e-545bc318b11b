local NPCCountDownComp = kg_require("Gameplay.LogicSystem.NPC.NPCCountDown.NPCCountDownComp")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class NPC_CSBg_Panel : UIPanel
---@field view NPC_CSBg_PanelBlueprint
local NPC_CSBg_Panel = DefineClass("NPC_CSBg_Panel", UIPanel)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

NPC_CSBg_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function NPC_CSBg_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function NPC_CSBg_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function NPC_CSBg_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function NPC_CSBg_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function NPC_CSBg_Panel:InitUIView()
end

---面板打开的时候触发
function NPC_CSBg_Panel:OnRefresh(btransition, color, width, height)
	local c = self.view
	c.bg1_lua:SetBrushTintColor(color)
	c.bg2_lua:SetBrushTintColor(color)
	if btransition then
		self:PlayAnimation(c.Ani_Fadein_1)
	end
	self.btransition = btransition
end

function NPC_CSBg_Panel:StartPlayCloseAnim()
	if self.btransition then
		local c = self.view
		self:PlayAnimation(c.Ani_Fadeout_1, function()
			self:CloseSelf()
		end)
	else
		self:CloseSelf()
	end
end

return NPC_CSBg_Panel
