local ItemSmall = kg_require("Gameplay.LogicSystem.Item.ItemSmall")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class NPCBtnSelectItem : UIListItem
---@field view NPCBtnSelectItemBlueprint
local NPCBtnSelectItem = DefineClass("NPCBtnSelectItem", UIListItem)

local KeyPrompt = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.P_KeyPrompt")

---按钮显示状态
NPCBtnSelectItem.EButtonDisplayState = {
	Normal = 0,
	Selected = 1,
	Gray = 2,
}

---icon状态
NPCBtnSelectItem.EIconState = {
	Item = 0,
	Common = 1,
	Locked = 2,
}

NPCBtnSelectItem.DefaultProgressImage = UIAssetPath.UI_Item_Icon_Spirit02_S_DynamicSprite
NPCBtnSelectItem.DefaultCommonImage = UIAssetPath.UI_NPC_Img_DialogueGlow

---选中的icon
NPCBtnSelectItem.SelectIcon = {
	[Enum.TalkOptionType.GuideGetMoney] = _G.Enum.EArtAssetIconData.GUILD_WAGES,
}

---刷新类型，即从哪个界面来调用刷新的
NPCBtnSelectItem.RefreshType = {
	Talk = 1,
	Interact = 2,
}

local ESlateVisibility = import("ESlateVisibility")

NPCBtnSelectItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function NPCBtnSelectItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function NPCBtnSelectItem:InitUIData()
	self.Config = nil
	self.bShowKeyHit = nil
	self.FuncLocked = nil
	self.userWidget:Event_UI_Type(0, 1)
	self.refreshType = nil
	self.Task = nil
	self.clickAnim = false  -- 点击动画
end

--- UI组件初始化，此处为自动生成
function NPCBtnSelectItem:InitUIComponent()
    ---@type KeyPrompt
    self.WBP_KeyPromptCom = self:CreateComponent(self.view.WBP_KeyPrompt, KeyPrompt)
    ---@type ItemSmall
    self.WBP_ItemCom = self:CreateComponent(self.view.WBP_Item, ItemSmall)
end

---UI事件在这里注册，此处为自动生成
function NPCBtnSelectItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Select.OnHovered, "on_Btn_Select_Hovered")
    self:AddUIEvent(self.view.Btn_Select.OnClicked, "on_Btn_Select_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function NPCBtnSelectItem:InitUIView()
end

function NPCBtnSelectItem:setDefaultIcon()
	if self.Config and (not string.isEmpty(self.Config.Icon)) then
		self:SetImage(self.view.Img_Option, self.Config.Icon)
	else
		local NPCManager = Game.NPCManager
		self:SetImage(self.view.Img_Option, NPCManager.DefaultTalkIcon)
		self.view.Img_Option:SetBrushTintColor(NPCManager.DefaultTalkColor)	
	end
end

---面板打开的时候触发
function NPCBtnSelectItem:OnRefresh(config)
	xpcall(self.PlayAnimation, _G.CallBackError, self, self.view.Ani_in)
	if self.index == 1 then
		xpcall(self.PlayAnimation, _G.CallBackError, self, self.view.Ani_Hover)
	end
	local panel = self:GetBelongPanel()
	if panel.uid == "P_HUDInteract" then
		self.refreshType = NPCBtnSelectItem.RefreshType.Interact
		self:refreshInteractSelect()
	elseif panel.uid == "P_NPCTalk" then
		self.refreshType = NPCBtnSelectItem.RefreshType.Talk
		self:refreshTalkSelect(config)
	else
		self.refreshType = nil
	end
end

function NPCBtnSelectItem:refreshTalkSelect(config)
	self.Config = config
	local NPCManager = Game.NPCManager
	local QuestSystem = Game.QuestSystem
	if self.Config then
		self.view.O_BtnContent:SetRenderOpacity(1)
		self.view.Img_Lock:SetVisibility(ESlateVisibility.Collapsed)
		self.FuncLocked = false

		local params = self.Config.Params

		if not params then
			--什么操作都没有，继续下一句对话
			self:setDefaultIcon()
		elseif params.QuestID ~= nil then
			--设置任务入口图标
			local questID = params.QuestID
			local ringID = QuestSystem:GetRingIDByQuestID(questID)
			local iconPath, _, color, _ = QuestSystem:GetNPCTaskIconAndColor(ringID)
			self:SetImage(self.view.Img_Option, iconPath)
			self.view.Img_Option:SetBrushTintColor(color)
		elseif params.FunctionID then
			--设置功能入口图标
			local functionID = params.FunctionID
			local ConstFunctionData = Game.TableData.GetFunctionInfoDataRow(functionID)
			local icon = ConstFunctionData.Icon
			if icon == "" then
				self:setDefaultIcon()
			else
				self:SetImage(self.view.Img_Option, icon)
				self.view.Img_Option:SetBrushTintColor(NPCManager.DefaultTalkColor)
			end
		elseif params.JumpID then
			local jumpID = params.JumpID
			local jumpCfg = Game.TableData.GetUIJumpDataRow(jumpID)
			local mark = false
			if jumpCfg then
				local unlockEnum = jumpCfg.UIUnlock
				if not unlockEnum then
					local uiCfg = UI.GetCfg(jumpCfg.UIclass)
					if uiCfg and uiCfg.unlock ~= "" then
						unlockEnum = uiCfg.unlock
					end
				end
				if unlockEnum and unlockEnum ~= "" then
					local unlockIndex = Enum.EFunctionInfoData[unlockEnum]
					local functionID = Game.TableData.Get_FunctionEnumToLockData()[unlockIndex]
					local functionTableData = Game.TableData.GetFunctionInfoDataRow(functionID)
					if functionTableData.Icon ~= '' then
						self:SetImage(self.view.Img_Option, functionTableData.Icon)
						self.view.Img_Option:SetBrushTintColor(NPCManager.DefaultTalkColor)
						mark = true
					end
				end
			end
			if not mark then
				self:setDefaultIcon()
			end
		elseif params.NextKind and NPCBtnSelectItem.SelectIcon[params.NextKind] then
			self:setDefaultIcon()
		else
			self:setDefaultIcon()
		end
		-- 交互说这里不需要颜色设置了，采用原有的默认颜色
		self.view.Text_Select:SetText(Game.NPCManager.GetFormatTalkText(self.Config.Title))
	end

	---@type NPCTalkPanel
	local panel = self:GetBelongPanel()
	local bShowKeyHit = panel:CheckSelectListItemShowHint()
	self:SetKeyHint(bShowKeyHit)
	if self.index == 1 then
		self:PlayAnimation(self.view.Ani_in)
	end
end

function NPCBtnSelectItem:refreshInteractSelect()
	xpcall(self.PlayAnimation, _G.CallBackError, self, self.view.Ani_in)
	self.clickAnim = false  -- 点击动画
	---@type HUDInteractPanel
	local panel = self:GetBelongPanel()
	self.Task = panel:GetInteractInfo(self.index)
	self:RefreshDisplayState()
	if self.Task and not self.Task.bIsShowing then
		self.Task.bIsShowing = true
	elseif self.Task and not self.Task.bInRemove then
	end
	self:SetKeyHint(panel:GetShowHintIndex() == self.index)
end

function NPCBtnSelectItem:RefreshDisplayState()
	Log.Debug("RefreshDisplayState", self.index, self.Task)
	if not self.Task then
		return
	end
	if self.Task.ExtraParams.bIsGray or self.Task.ExtraParams.bLockedState then
		self.DisplayState = NPCBtnSelectItem.EButtonDisplayState.Gray
	elseif self.bShowKeyHit then
		self.DisplayState = NPCBtnSelectItem.EButtonDisplayState.Selected
	else
		self.DisplayState = NPCBtnSelectItem.EButtonDisplayState.Normal
	end
	self.userWidget:Event_UI_Type(self.DisplayState, self.Task.IconType)

	if self.Task.IconType == Game.HUDInteractManager.EIconType.Common then
		self:BuildCommonIcon(self.Task)
	elseif self.Task.IconType == Game.HUDInteractManager.EIconType.Item then
		self.IconState = NPCBtnSelectItem.EIconState.Item
		self:BuildItemIcon(self.Task)
	end
end

function NPCBtnSelectItem:BuildCommonIcon(Task)
	local icon = self.view.Img_Option

	if self.Task.Title then
		self.view.Text_Select:SetText(self.Task.Title)
		-- 交互说这里不需要颜色设置了，采用原有的默认颜色
	end

	if self.Task.ExtraParams and self.Task.ExtraParams.DynamicData then
		local DynamicData = self.Task.ExtraParams.DynamicData
		local questID = Game.QuestSystem:GetQuestIDByDynamicTalk(DynamicData.Kind, DynamicData.GenericID)
		if not questID then
			self:SetImage(icon, NPCBtnSelectItem.DefaultCommonImage)
		else
			local ringID = Game.QuestSystem:GetRingIDByQuestID(questID)
			local iconPath, _, color, _ = Game.QuestSystem:GetNPCTaskIconAndColor(ringID)
			self:SetImage(icon, iconPath)
			if color then
				icon:SetBrushTintColor(color)
			end
		end
	elseif not string.isEmpty(self.Task.Icon) then
		self:SetImage(icon, self.Task.Icon)
		if self.Task.IconColor then
			icon:SetBrushTintColor(self.Task.IconColor)
		end
	else
		self:SetImage(icon, NPCBtnSelectItem.DefaultCommonImage)
	end
end

function NPCBtnSelectItem:SetGray(bIsGray)
	if bIsGray then
		self.userWidget:Event_UI_State(NPCBtnSelectItem.EButtonDisplayState.Gray)
	else
		self.userWidget:Event_UI_State(NPCBtnSelectItem.EButtonDisplayState.Normal)
	end
end

function NPCBtnSelectItem:BuildItemIcon(Task)
	local ItemData = Game.TableData.GetItemNewDataRow(self.Task.ItemID)
	if self.Task.ItemID and ItemData then
		self.WBP_ItemCom:FillItem(self.Task.ItemID, nil, false)
		-- 交互说这里不需要颜色设置了，采用原有的默认颜色
		if Task.ItemCount > 1 then
			self.view.Text_Select:SetText(string.format("%s x %s",ItemData.itemName, Task.ItemCount))
		else
			if Task.Name then
				self.view.Text_Select:SetText(Task.Name)
			else
				self.view.Text_Select:SetText(ItemData.itemName)
			end
		end
	end
end

function NPCBtnSelectItem:SetKeyHint(bShowKeyHit)
	if self.bShowKeyHit ~= bShowKeyHit then
		self.bShowKeyHit = bShowKeyHit
		if bShowKeyHit then
			self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.Visible)
			self.WBP_KeyPromptCom:Refresh(nil,"F")
			self:OnHovered()
		else
			self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.Hidden)
			self:UnHovered()
		end
		self:configTaskDisplayState()
	end
end

function NPCBtnSelectItem:ConfigKeyHint(bShowKeyHit)
	self:SetKeyHint(bShowKeyHit)
end

function NPCBtnSelectItem:configTaskDisplayState()
	if self.Task then
		if self.Task.ExtraParams.bIsGray or self.Task.ExtraParams.bLockedState then
			self.DisplayState = NPCBtnSelectItem.EButtonDisplayState.Gray
		elseif self.bShowKeyHit then
			self.DisplayState = NPCBtnSelectItem.EButtonDisplayState.Selected
		else
			self.DisplayState = NPCBtnSelectItem.EButtonDisplayState.Normal
		end
		self.userWidget:Event_UI_Type(self.DisplayState, self.Task.IconType)
	end
end

function NPCBtnSelectItem:MarkForRemove()
	self:SetKeyHint(false)
end

function NPCBtnSelectItem:afterItemClickAnim()
	if self.FuncLocked then
		local FunctionTableData = Game.TableData.GetFunctionInfoDataRow(self.Config.Params.FunctionID)
		local PlayerLevel = GetMainPlayerPropertySafely( "Level")
		if FunctionTableData.Lvl > PlayerLevel then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.FUNCTION_LOCK_LEVEL,
				{ { FunctionTableData.Lvl } }
			)
		else
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FUNCTION_LOCK_TASK)
		end
		return
	end
	xpcall(UI.Invoke, _G.CallBackError, "P_NPCTalk","updateTalkInfo", self.index)
end

function NPCBtnSelectItem:OnHovered()
	self.view.hover2:SetVisibility(ESlateVisibility.HitTestInvisible)
	self.view.KCanv_Selected:SetVisibility(ESlateVisibility.HitTestInvisible)
	self:PlayAnimation(self.view.Ani_Hover)
end

function NPCBtnSelectItem:UnHovered()
	self:StopAnimation(self.view.Ani_Hover)
	self.view.hover2:SetVisibility(ESlateVisibility.Hidden)
	self.view.KCanv_Selected:SetVisibility(ESlateVisibility.Hidden)
end

function NPCBtnSelectItem:OnItemClicked()
	self:on_Btn_Select_lua_Clicked()
end

function NPCBtnSelectItem:OnInteractItemHovered()
	if self.Task and not self.Task.bInRemove then
		if self.Task.ExtraParams.bIsGray then
			return
		end
		---@type HUDInteractPanel
		local panel = self:GetBelongPanel()
		panel:SetKeyHint(self.Task.UID)
		panel:SelectItem(self.index)
	end
end

function NPCBtnSelectItem:OnTalkItemHovered()
	---@type NPCTalkPanel
	local panel = self:GetBelongPanel()
	panel:SetKeyHint(self.index)
end

--- 此处为自动生成
function NPCBtnSelectItem:on_Btn_Select_Hovered()
	if self.refreshType == NPCBtnSelectItem.RefreshType.Interact then
		self:OnInteractItemHovered()
	elseif self.refreshType == NPCBtnSelectItem.RefreshType.Talk then
		self:OnTalkItemHovered()
	end
end

function NPCBtnSelectItem:OnInteractItemClicked()
	---@type P_HUDInteract
	local hudInteract = self:GetBelongPanel()
	-- 先播动画，后再实施点击的行为
	if self.view.Ani_click then
		self.clickAnim = true
		self:PlayAnimation(self.view.Ani_click, function()
			if self.clickAnim then
				hudInteract:OnItemClicked(self.index)
				-- 这里延迟执行一下，因为后续的执行可能会有点耗时，要不然就会漏出来
				self:StartTimer("DelayAniInAfterClick", function()
					xpcall(self.PlayAnimation, _G.CallBackError, self, self.view.Ani_in)
				end, 1000, 1)
			end
			self.clickAnim = false
		end)
	else
		hudInteract:OnItemClicked(self.index)
	end
end

function NPCBtnSelectItem:OnTalkItemClicked()
	if self.view.Ani_click then
		self:PlayAnimation(self.view.Ani_click, function()
			-- 这里延迟执行一下，因为后续的执行可能会有点耗时，要不然就会漏出来
			self:StartTimer("DelayAniInAfterClick", function() xpcall(self.PlayAnimation, _G.CallBackError, self, self.view.Ani_in) end,
				1000, 1)
			self:afterItemClickAnim()
		end)
	else
		self:afterItemClickAnim()
	end
end

--- 此处为自动生成
function NPCBtnSelectItem:on_Btn_Select_Clicked()
	if self.refreshType == NPCBtnSelectItem.RefreshType.Interact then
		self:OnInteractItemClicked()
	elseif self.refreshType == NPCBtnSelectItem.RefreshType.Talk then
		self:OnTalkItemClicked()
	end
end

return NPCBtnSelectItem
