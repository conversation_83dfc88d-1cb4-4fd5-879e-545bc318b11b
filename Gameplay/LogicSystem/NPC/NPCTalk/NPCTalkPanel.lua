local InteractSelectListComp = kg_require("Gameplay.LogicSystem.NPC.InteractSelectListComp")
local NPCTalkTextComp = kg_require("Gameplay.LogicSystem.NPC.NPCTalk.NPCTalkTextComp")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESlateVisibility = import("ESlateVisibility")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")
local C7FunctionLibrary = import("C7FunctionLibrary")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local EDrawDebugTrace = import("EDrawDebugTrace")

---@class NPCTalkPanel : UIPanel
---@field view NPCTalkPanelBlueprint
local NPCTalkPanel = DefineClass("NPCTalkPanel", UIPanel)

local StringConst = require "Data.Config.StringConst.StringConst"
local QuestConditionUtils = kg_require("Gameplay.LogicSystem.Quest.QuestConditionUtils")
local KismetInputLibrary = import("KismetInputLibrary")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

NPCTalkPanel.OptionType = {
	InsideFirst = 1, --插入到第一句
	InsideEnd = 1, --插入到最后一句
}

NPCTalkPanel.eventBindMap = {
	[_G.EEventTypes.LEVEL_ON_LEVEL_LOADED] = "endTalk",
	[_G.EEventTypes.NPC_RESUME_INTERACT] = "OnResumeInteract",
	[_G.EEventTypes.LEVEL_ON_ROLE_LOAD_COMPLETED] = "endTalk",
	[_G.EEventTypes.ROLE_ACTION_INPUT_EVENT] = "KeyOnInput",
}

---界面刷新的类型
NPCTalkPanel.RefreshType = {
	Entity = 1,
	SystemAction = 2,
}

---@class NPCTalkPanel.TalkInfo
---@field Order number 序列号
---@field TalkID number
---@field TalkType number 类别
---@field SystemActionList table

---@class NPCTalkPanel.SysActionParams
---@field bTalkGroup boolean 是否talkgroup
---@field id number talkId或talkGroupId
---@field npcCfgId number npc的templateId

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function NPCTalkPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function NPCTalkPanel:InitUIData()
	---@type number @进入对话的ID
	self.talkID = nil
	---@type number @当前对话的ID
	self.curTalkID = nil
	---@type boolean @是否是提交道具
	self.bSubmit = nil
	---@type boolean @是否是
	self.bShowHint = nil
	---@type number @当前选中
	self.showHintIndex = 1
	self.curHoverIndex = nil  -- 当前hover的下标
	---@type NPCTalkPanel.TalkInfo @对话信息
	self.talkInfo = {}
	---@type string @NPCEntityID
	self.npcEntityID = nil
	---@type number @NPCCfgID
	self.npcCfgID = nil
	---@type boolean @是否是选项阶段
	self.talkOption = nil
	---@type table @选项数据
	self.talkOptionDatas = {}

	self.MainPlayerAnimFeatureID = nil
	self.NpcAnimFeatureIDs = {}
	
	---npc看向与转向的字典
	self.npcGazeDict = {}
	self.npcLookAtDict = {}
	self.bNeedUpVehicle = false
	self.bHideWeapon = false

	---@type boolean @是否等待关闭
	self.bWaitForFun = false

	self.view.WBP_NPCTalk_Text.WBP_NPCCamera:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.WBP_NPCTalk_Text:Event_UI_Style(1, 0, 0)
	
	self.refreshType = NPCTalkPanel.RefreshType.Entity
	self.talkGroupId = nil
	self.talkIdList = {}
	self.curTalkIdx = 1

	self.talkEnded = false
	self.mainPlayerEnterPos = nil
end

--- UI组件初始化，此处为自动生成
function NPCTalkPanel:InitUIComponent()
    ---@type InteractSelectListComp
    self.WBP_Interact_SelectListCom = self:CreateComponent(self.view.WBP_Interact_SelectList, InteractSelectListComp)
    ---@type NPCTalkTextComp
    self.WBP_NPCTalk_TextCom = self:CreateComponent(self.view.WBP_NPCTalk_Text, NPCTalkTextComp)
end

---UI事件在这里注册，此处为自动生成
function NPCTalkPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function NPCTalkPanel:InitUIView()
end

---面板打开的时候触发
function NPCTalkPanel:OnRefresh(entity, sysActionParams)
	self.talkEnded = false
	self.WBP_NPCTalk_TextCom:SetDialoguePanelVisibleOrNot(true)
	--清除移动输入状态
	Game.ScreenInputManager.InitScreenInputState()

	self.bSubmit = false
	if sysActionParams ~= nil then
		self.refreshType = NPCTalkPanel.RefreshType.SystemAction
		self:refreshBySysAction(sysActionParams)
	elseif entity ~= nil and entity ~= -1 then
		self.refreshType = NPCTalkPanel.RefreshType.Entity
		self:refreshByEntity(entity)
	else
		Log.ErrorFormat("NPCTalkPanel refresh param error")
	end
end

function NPCTalkPanel:OnOpen()
	UIManager:GetInstance():HideAllPanel(UIConst.HIDE_PANELS_SOURCE_TYPE.NPC, {self.uid, "ScreenInput_Panel"})
	self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(false)

	self.bShowHint = false
end

function NPCTalkPanel:refreshNpcEntity(entity, triggerNewbie, closeSelfWhenNoNpc)
	if entity then
		local entityUID = entity:uid()
		self.npcCfgID = entity.TemplateID
		self.npcEntityID = entity.eid
		self.npcEntityUID = entityUID
		Game.NPCSystem.model:SetLastNpcCfgId(entityUID)
	else
		self.npcCfgID = nil
		Game.NPCSystem.model:ResetLastNpcCfgId()
		self.npcEntityID = nil
		self.npcEntityUID = nil
		Log.Warning("Can't Find NPC When Talk")
		if closeSelfWhenNoNpc then
			self:CloseSelf()
		end
		return false
	end

	Game.CameraManager:EnableInteractiveCamera(true, entity)
	xpcall(HandleLookAtLoc, _G.CallBackError, Game.me, entity, true)
	if entity.OnStartTalkWithPlayer then
		entity:OnStartTalkWithPlayer()
	end
	
	Game.NPCSystem.model:TryStopDelayNPCLeaveTalkTimer(self.npcCfgID, self.npcEntityID)
	Game.me:ReqEnterNpcTalk(self.npcCfgID, self.npcEntityID)
	return true
end

function NPCTalkPanel:refreshByEntity(entity)
	self:moveMainPlayerToTalkPos(entity)
	local res = self:refreshNpcEntity(entity, true, true)
	if res == false then
		return
	end
	self:buildContent()
	self:showTalkContent()

	-- 下面是不是只有这个任务相关联的才使用
	if self.bWaitForFun then
		self.WBP_Interact_SelectListCom:Clear()
		--Game.NPCSystem.StopTask(NpcActor)
		self.bWaitForFun = false
	end
end

---@param sysActionParams NPCTalkPanel.SysActionParams
function NPCTalkPanel:refreshBySysAction(sysActionParams)
	local bTalkGroup, id, npcCfgId = sysActionParams.bTalkGroup, sysActionParams.id, sysActionParams.npcCfgId
	local npcEnt
	--策划说如果没有配置npcCfgId，则使用上次的npcCfgId
	local lastNpcCfgId = Game.NPCSystem.model:GetLastNpcCfgId()
	if (npcCfgId == nil or npcCfgId == 0) and lastNpcCfgId ~= nil then
		npcCfgId = lastNpcCfgId
		npcEnt = Game.EntityManager:getEntity(npcCfgId)
	else
		if npcCfgId then
			npcEnt = Game.NPCManager.GetFirstTaskNpcEntByCfgId(npcCfgId)	
		end
	end
	if npcEnt then
		self:refreshNpcEntity(npcEnt)
	end
	if bTalkGroup then
		self.talkGroupId = id
		table.clear(self.talkIdList)
		local talkGroupData = Game.TableData.GetTalkGroupDataRow(id)
		for _, info in ipairs(talkGroupData.TalkGroup) do
			table.insert(self.talkIdList, info.TalkID)
		end
	else
		self.talkGroupId = nil
		self.talkIdList = {id}
	end
	self.curTalkIdx = 0
	self:tryShowNextTalkContentBySysAction()
end

function NPCTalkPanel:checkTalkGroup()
	return self.talkGroupId ~= nil
end

function NPCTalkPanel:tryShowNextTalkContentBySysAction()
	self.curTalkIdx = self.curTalkIdx + 1
	if self:checkTalkGroup() then
		-- 检查下条件
		local condition = Game.TableData.GetTalkGroupDataRow(self.talkGroupId).Condition
		if condition == nil or QuestConditionUtils.CheckCondition(condition, self.npcEntityID) then
			self:doShowNextTalkContentBySysAction()
		else
			-- 条件不满足，直接结束
			self:endTalk()
		end
	else
		self:doShowNextTalkContentBySysAction()
	end
end

function NPCTalkPanel:doShowNextTalkContentBySysAction()
	self:buildSimpleContent(self.talkIdList[self.curTalkIdx])
	self:showTalkContent()
end

function NPCTalkPanel:checkRefreshByEntity()
	return self.refreshType == NPCTalkPanel.RefreshType.Entity
end

function NPCTalkPanel:checkRefreshBySysAction()
	return self.refreshType == NPCTalkPanel.RefreshType.SystemAction
end

---@private 构造对话数据
function NPCTalkPanel:buildContent()
	table.clear(self.talkInfo)
	local _, kind, talkID = Game.NPCSystem:GetNpcTalkContent(self.npcCfgID, self.npcEntityID)
	if kind == Game.NPCSystem.EDynamicKind.Dialogue then
		Log.WarningFormat("Error TalkKind When NpcTalk(NPCID:%s,DialogueID:%s)", self.npcCfgID, talkID)
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
	elseif kind == Game.NPCSystem.EDynamicKind.Submit then
		Log.WarningFormat("Error TalkKind When NpcTalk(NPCID:%s,TalkID:%s)", self.npcCfgID, talkID)
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
	elseif kind == Game.NPCSystem.EDynamicKind.SubmitDialogue then
		Log.WarningFormat("Error TalkKind When NpcTalk(NPCID:%s,TalkID:%s)", self.npcCfgID, talkID)
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
	else
		self:buildSimpleContent(talkID)
	end
end

function NPCTalkPanel:buildSimpleContent(talkID)
	table.clear(self.talkInfo)
	local talkData = Game.TableData.GetTalkDataRow(talkID)
	if (not talkData) or (not talkData[1]) then
		Log.WarningFormat("【NPCTalk】Error TalkID:%s",talkID)
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
	else
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
		self.talkID = talkID
		self.curTalkID = talkID
		self.talkInfo.TalkID = talkID
		-- 这里需要再判断一下显示条件
		self.talkInfo.Order = self:getTalkNextOrderByTalkID(talkID, 0)
	end
end

function NPCTalkPanel:getTalkNextOrderByTalkID(talkID, order)
	if QuestUtils.UseCommonCondition then
		local talkData = Game.TableData.GetTalkDataRow(talkID)
		local talkDataCnt = #talkData
		for i = order + 1, talkDataCnt do
			local talkCondition = talkData[i].ConditionAst
			if QuestUtils.CheckCommonConditionMeet(talkCondition) then
				return i
			end
		end
		return talkDataCnt + 1
	else
		return order + 1
	end
end

---@private 点击选项选项
---@param optionIndex number @选项索引
function NPCTalkPanel:onClickOption(optionIndex)
	local optionData = self.talkOptionDatas[optionIndex]
	if not optionData then
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
		Log.WarningFormat("Error OptionIndex(OptionIndex:%s, OptionsCount:%s)", optionIndex, #self.talkOptionDatas)
		return
	end

	local talkData = Game.TableData.GetTalkDataRow(self.talkInfo.TalkID)
	local param = optionData.Params
	local systemActionList = param.SystemActionList
	self.talkInfo.SystemActionList = systemActionList

	local TalkOptionType = Enum.TalkOptionType
	if not param then
		self:updateTalkSystemActionEnumType(talkData, systemActionList)
	else
		-- 这里去掉了一层table，因为只有一个元素
		local paramNextKind = param.NextKind
		if paramNextKind == TalkOptionType.Content then
			self:updateTalkContentType(param)
		elseif paramNextKind == TalkOptionType.DialogueType then
			self:updateContentDialogueType(param)
		elseif paramNextKind == TalkOptionType.JumpType then
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Jump
			self.talkInfo.JumpID = param.JumpID
		elseif paramNextKind == TalkOptionType.GuideGetMoney then
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.NONE
			self.talkInfo.Func = function()
				Game.GuildSystem:HandleWageGet()
			end
		elseif paramNextKind == TalkOptionType.GuildMerge then
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.WaitForEnd
			self.talkInfo.Page = "P_GuildMerge"
			self.talkInfo.FunctionParam = param.FunctionParam
		elseif paramNextKind == TalkOptionType.ItemSubmit then
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.WaitForEnd
			self.talkInfo.Page = UIPanelConfig.SubmitNew_Panel
			self.talkInfo.FunctionParam = {
				ItemSubmitID = param.ItemSubmitID,
				NewSubmitCfg = param.NewSubmitCfg,
				CondIdx = param.CondIdx,
				QuestID = param.QuestID,
				TaskRingID = param.QuestID and Game.QuestSystem:GetRingIDByQuestID(param.QuestID) or nil
			}
		elseif paramNextKind == TalkOptionType.SystemAction then
			table.clear(self.talkInfo)
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
			self.talkInfo.SystemActionID = param.SystemActionID
		elseif paramNextKind == TalkOptionType.UnlockTeleport then
			table.clear(self.talkInfo)
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
			self.talkInfo.UnlockTeleport = true
		elseif paramNextKind == TalkOptionType.DebugSystemAction then
			table.clear(self.talkInfo)
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
			self.talkInfo.DebugSystemActionList = param.DebugSystemActionList
		elseif paramNextKind == TalkOptionType.SystemActionEnum then
			self:updateTalkSystemActionEnumType(talkData, systemActionList)
		elseif paramNextKind == TalkOptionType.Finally then
			table.clear(self.talkInfo)
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
		end
	end
	
	self:showTalkContent()
end

function NPCTalkPanel:updateContentDialogueType(param)
	self.talkInfo.TalkType = Game.NPCSystem.TalkType.Dialogue
	self.talkInfo.DialogueID = param.DialogueID
	self.talkInfo.SubmitID = param.SubmitID
	self.talkInfo.NewSubmitCfg = param.NewSubmitCfg
	self.talkInfo.QuestID = param.QuestID
	self.talkInfo.CondIdx = param.CondIdx
	self.talkInfo.RingID = param.RingID
	self.talkInfo.ReceiveRingID = param.ReceiveRingID
end

function NPCTalkPanel:updateTalkContentType(param)
	self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
	if param.OptionID == -1 then
		--插入的下一页按钮
		self.talkInfo.Order = self.talkInfo.Order + 1
	else
		-- 进入新的content
		local talkID = param.OptionID
		local talkData = Game.TableData.GetTalkDataRow(talkID)
		if talkData then
			self.talkInfo.TalkID = talkID
			self.curTalkID = talkID
			self.talkInfo.Order = 1
		else
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
		end
	end
end

function NPCTalkPanel:updateTalkSystemActionEnumType(talkData, systemActionList)
	-- 显示下一句
	if talkData[self.talkInfo.Order+1] then
		QuestUtils.ExecuteTalkSystemActionList(systemActionList)
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
		self.talkInfo.Order = self.talkInfo.Order + 1
	else
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
	end
end

function NPCTalkPanel:checkTalkOptionDatasValid(talkOptionDatas)
	for _, optionData in ksbcpairs(talkOptionDatas) do
		if not string.isEmpty(optionData.Title) then return true end
	end
	return false
end

---@private 点击下一步
function NPCTalkPanel:onClickContent()
	table.clear(self.talkOptionDatas)
	local curTalkOrder = self.talkInfo.Order
	local curTalkInfoId = self.talkInfo.TalkID
	local talkData = Game.TableData.GetTalkDataRow(curTalkInfoId)

	if curTalkInfoId == self.talkID and curTalkOrder == 1 and self:checkRefreshByEntity() then
		Game.NPCSystem:GetFirstTalkOptions(self.npcCfgID, self.talkOptionDatas, self.npcEntityID)
	else
		Game.NPCSystem:GetTalkOptions(curTalkInfoId, curTalkOrder, self.talkOptionDatas, self.npcEntityID)
	end

	-- 有选项
	if #self.talkOptionDatas > 0 and self:checkTalkOptionDatasValid(self.talkOptionDatas) then
		self.talkInfo.TalkType = Game.NPCSystem.TalkType.Option
	else
		-- 无选项
		local systemActionList = nil
		if self.talkOptionDatas[curTalkOrder] then
			systemActionList = self.talkOptionDatas[curTalkOrder].Params.SystemActionList	
		end
		self.talkInfo.SystemActionList = systemActionList
		local nextOrder = self:getTalkNextOrderByTalkID(curTalkInfoId, curTalkOrder)
		-- 默认都是顺序的
		if talkData[nextOrder] then
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
			self.talkInfo.Order = nextOrder
		else
			self.talkInfo.TalkType = Game.NPCSystem.TalkType.Finally
		end
	end
end

---@private 更新任务数据
function NPCTalkPanel:updateTalkInfo(optionIndex)
	if optionIndex then
		--上文是选项
		self:onClickOption(optionIndex)
	else
		--上文是文本

		-- 如果是sysAction且是talkGroup，那么直接显示下一个
		if self:checkRefreshBySysAction() and self:checkTalkGroup() then
			self:tryShowNextTalkContentBySysAction()
			return
		end
		
		local talkData = Game.TableData.GetTalkDataRow(self.talkInfo.TalkID)
		-- 这里已经没有对话了，直接结束
		if talkData == nil then
			self:endTalk()
			return
		end
		
		-- 走之前的正常逻辑
		self:onClickContent()
		local systemActionList = DeepCopy(self.talkInfo.SystemActionList)
		local isFinally = self.talkInfo.TalkType == Game.NPCSystem.TalkType.Finally
		self:showTalkContent()
		-- 这里需要再执行一下systemAction，放在这里执行是因为时序的问题
		-- 同时增加isFinally的判断是因为如果上文是最后一句，那么showTalkContent里已经执行过systemAction
		if not isFinally then
			QuestUtils.ExecuteTalkSystemActionList(systemActionList)
		end
	end
end

function NPCTalkPanel:setSelectListVisibleOrNot(visible)
	self.WBP_Interact_SelectListCom:SetVisible(visible)
end

---@private 显示文本
function NPCTalkPanel:showContent()
	local talkInfo = self.talkInfo
	self.WBP_Interact_SelectListCom:Clear()
	self:setSelectListVisibleOrNot(false)
	self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(false)
	local talkData = Game.TableData.GetTalkDataRow(talkInfo.TalkID)
	if not talkData then
		Log.WarningFormat("Error TalkID:%s", talkInfo.TalkID)
		return self:endTalk()
	end
	---@type _TalkDataRow
	local talkCfg = talkData[talkInfo.Order]
	self.WBP_NPCTalk_TextCom:ShowContent(talkCfg, self.npcCfgID)
	if talkCfg then
		local talkText = Game.NPCSystem.GetFormatTalkText(talkCfg.Text)
		self:processPlayerAndNpcActs(talkCfg)
		if talkCfg.NpcName ~= "" and talkCfg.NpcName ~= "-1" then
			local chatText = string.format(StringConst.Get("SOCIAL_CHAT_NPC"), talkCfg.NpcName, talkText)
			chatText = Game.NPCSystem.GetFormatTalkText(chatText)
			Game.ChatSystem:AddChannelSystemInfo(chatText, _G._now(), Enum.EChatChannelData.NEARBY)
		end
	end
end

function NPCTalkPanel:processPlayerAndNpcActs(talkCfg)
	self:playPlayerAndNpcAnims(talkCfg)
	self:tryMainPlayerDownVehicle(talkCfg)
	self:processGazeAndLookAt(talkCfg)
	self:tryMainPlayerHideWeapon(talkCfg)
end

function NPCTalkPanel:revertPlayerAndNpcActs()
	self:revertPlayerAndNpcAnims()
	self:revertMainPlayerDownVehicles()
	self:revertGazeAndLookAt()
	self:revertMainPlayerHideWeapon()
end

---@param talkCfg _TalkDataRow
function NPCTalkPanel:playPlayerAndNpcAnims(talkCfg)
	for _, animInfo in ipairs(talkCfg.Animation) do
		local id, animName, stageName = animInfo[1], animInfo[2], animInfo[3]
		local numId = tonumber(id)
		-- 策划没有填第一个Id，那么说明是个老的数据，需要转换一下
		if numId == nil then
			-- 这里是一段兼容代码，兼容老数据
			if talkCfg.NpcName == "-1" then
				numId = QuestUtils.NpcTalkerMainPlayer
			else
				numId = QuestUtils.NpcTalkerCurNpc
			end
			animName = id
		end
		
		if numId == QuestUtils.NpcTalkerMainPlayer then
			--玩家
			self.MainPlayerAnimFeatureID = Game.me:PlayAnimLibMontage(animName, stageName, false, nil, nil, true)
		else
			-- NPC
			local entId
			---@type NpcActor
			local ent
			if numId == QuestUtils.NpcTalkerCurNpc then
				entId = self.npcEntityID
				ent = Game.EntityManager:getEntity(entId)
			else
				local isTemplateId = QuestUtils.CheckNpcTemplateId(id)
				if isTemplateId then
					ent = Game.NPCManager.GetTaskNpcEntByTemplateId(numId)
				else
					ent = Game.NPCManager.GetTaskNpcEntByInstanceId(id)
				end
				if ent then
					entId = ent.eid
				end
			end
			if entId then
				local animFeatureId = ent:PlayAnimLibMontage(animName, stageName, false, nil, nil, true)
				self.NpcAnimFeatureIDs[entId] = animFeatureId
			end
		end
	end
	-- 把下面的这段注释先留着吧，不太明白是干啥用的，方便后续查问题
	-- todo @liufan npc坐在椅子上服务器未来做成状态来禁止动画的表演
	--if entity and not StringValid(entity.ContinuousInteractInsID) then
	--	self.NpcAnimFeatureID = entity:PlayAnimLibMontage(talkCfg.Animation, nil, false, nil, nil, true)
	--else
	--	Log.WarningFormat("Error Animation Play(TalkID:%d, Order:%d)", self.talkInfo.TalkID, self.talkInfo.Order)
	--end
end

function NPCTalkPanel:revertPlayerAndNpcAnims()
	for entId, animFeatureId in pairs(self.NpcAnimFeatureIDs) do
		local ent = Game.EntityManager:getEntity(entId)
		if ent then
			ent:StopAnimLibMontage(animFeatureId)
			self.NpcAnimFeatureIDs[entId] = nil
		end
	end
	if Game.me and self.MainPlayerAnimFeatureID then
		Game.me:StopAnimLibMontage(self.MainPlayerAnimFeatureID)
		self.MainPlayerAnimFeatureID = nil
	end
end

---@param talkCfg _TalkDataRow
function NPCTalkPanel:tryMainPlayerDownVehicle(talkCfg)
	if talkCfg.DownVehicle then
		if Game.me:GetIsOnMount() then
			Game.me:ReqGetOffSelfMount(true)
			if talkCfg.UpVehicle then
				self.bNeedUpVehicle = true
			end
		end
	end
end

function NPCTalkPanel:revertMainPlayerDownVehicles()
	if self.bNeedUpVehicle then
		if not Game.me:GetIsOnMount() then
			Game.me:ReqGetOnSelfMount(true)
			self.bNeedUpVehicle = false
		end
	end
end

---@param talkCfg _TalkDataRow
function NPCTalkPanel:processGazeAndLookAt(talkCfg)
	for _, gazeInfo in ksbcpairs(talkCfg.Gaze) do
		local srcId, targetId = gazeInfo[1], gazeInfo[2]
		---@type ViewControlGazeComponent
		local srcEnt = QuestUtils.GetEntityByCompatibleId(srcId)
		local targetEnt = QuestUtils.GetEntityByCompatibleId(targetId)
		if srcEnt and targetEnt then
			srcEnt:StartGazeActor(Enum.EGazeTypeMap.QUEST, targetEnt.CharacterID, "None")
			self.npcGazeDict[srcEnt.eid] = targetEnt.eid
		end
	end
	for _, lookAtInfo in ksbcpairs(talkCfg.TurnTo) do
		local srcId, targetId = lookAtInfo[1], lookAtInfo[2]
		---@type ViewControlRotateComponent
		local srcEnt = QuestUtils.GetEntityByCompatibleId(srcId)
		local targetEnt = QuestUtils.GetEntityByCompatibleId(targetId)
		if srcEnt and targetEnt then
			srcEnt:EnterLookAtToActorWithAnim(targetEnt, 0)
			self.npcLookAtDict[srcEnt.eid] = targetEnt.eid
		end
	end
end

function NPCTalkPanel:revertGazeAndLookAt()
	for srcId, _ in pairs(self.npcGazeDict) do
		---@type ViewControlGazeComponent
		local srcEnt = Game.EntityManager:getEntity(srcId)
		if srcEnt then
			srcEnt:UnGaze(Enum.EGazeTypeMap.DIALOGUE, true)
		end
		self.npcGazeDict[srcId] = nil
	end
	table.clear(self.npcGazeDict)
	for srcId, _ in pairs(self.npcLookAtDict) do
		---@type ViewControlRotateComponent
		local srcEnt = Game.EntityManager:getEntity(srcId)
		if srcEnt then
			srcEnt:ExitLookAt()
		end
		self.npcLookAtDict[srcId] = nil
	end
end

---@param talkCfg _TalkDataRow
function NPCTalkPanel:tryMainPlayerHideWeapon(talkCfg)
	if talkCfg.HideWeapon then
		Game.me:HideAttachItemByType(Enum.EAttachItemType.Weapon, false, Enum.EInVisibleReasons.Dialogue)
		self.bHideWeapon = true
	end
end

function NPCTalkPanel:revertMainPlayerHideWeapon()
	if self.bHideWeapon then
		Game.me:HideAttachItemByType(Enum.EAttachItemType.Weapon, true, Enum.EInVisibleReasons.Dialogue)
		self.bHideWeapon = false
	end
end

function NPCTalkPanel:showOption()
	self.talkOption = true
	self:setSelectListVisibleOrNot(true)
	self.showHintIndex = 1
	self.WBP_Interact_SelectListCom:Refresh(self.talkOptionDatas)
	if #self.talkOptionDatas > 1 and self.bShowHint then
		self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(true)
	else
		self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(false)
	end

	self.WBP_NPCTalk_TextCom:OnOptionShowed()
end

function NPCTalkPanel:showFinally(talkInfo)
	if talkInfo.UnlockTeleport then
		if self.npcEntityID then
			local NPCEntity = Game.EntityManager:getEntity(self.npcEntityID)
			if NPCEntity and NPCEntity.InstanceID then
				local insID = tonumber(NPCEntity.InstanceID)
				local NpcToTeleportMap = Game.TableData.Get_NpcToTeleportMap()
				local teleportInsID = NpcToTeleportMap[insID]
				if teleportInsID then
					Game.TeleportManager:ReqUnlockTeleportPointByNpc(tostring(teleportInsID))
				end
			end
		end
	end
	local systemActionList = DeepCopy(talkInfo.SystemActionList)
	local debugSystemActionList = DeepCopy(talkInfo.DebugSystemActionList)
	local closeTalk = systemActionList == nil and Game.me ~= nil
	-- 这里先结束talk，再执行systemActionList
	self:completeTalk(closeTalk)
	QuestUtils.ExecuteTalkSystemActionList(systemActionList, debugSystemActionList)
end

function NPCTalkPanel:showDialogue(talkInfo)
	local npcID = self.npcCfgID
	local dialogueID = talkInfo.DialogueID
	local receiveRingID = talkInfo.ReceiveRingID
	local ringID = talkInfo.RingID
	local submitID = talkInfo.SubmitID
	local newSubmitCfg = talkInfo.NewSubmitCfg
	local questID = talkInfo.QuestID
	local condIdx = talkInfo.CondIdx
	if Game.ArrodesDialogueSystem:CheckIsArrodesDialogue(dialogueID) then
		Game.ArrodesDialogueSystem:StartDialogue(dialogueID, self.npcEntityID)
	else
		Game.CinematicManager:StartPlayCinematic({
			CinematicType = Enum.CinematicType.Dialogue,
			AssetID = dialogueID,
			SubmitItemID = submitID,
			NewSubmitCfg = newSubmitCfg,
			QuestID = questID,
			CondIdx = condIdx,
			ReceiveRingID = receiveRingID,
			TaskRingID = ringID,
			NPCID = npcID or nil,
			ActorEntityID = self.npcEntityUID
		})
	end
	self:endTalk()
end

function NPCTalkPanel:showWaitForEnd(talkInfo)
	self.WBP_NPCTalk_TextCom:SetDialoguePanelVisibleOrNot(false)
	table.clear(self.talkOptionDatas)
	self.WBP_Interact_SelectListCom:Refresh(self.talkOptionDatas)
	self.bWaitForFun = true
	if talkInfo.Page then
		-- 这里有点恶心，先这么兼容一下吧
		if talkInfo.Page == UIPanelConfig.SubmitNew_Panel then
			Game.ItemSubmitSystem:ShowItemSubmitUICompatible(talkInfo.FunctionParam)
		else
			UI.ShowUI(talkInfo.Page, talkInfo.FunctionParam)
		end
	end
end

---@private 根据数据更新对话
function NPCTalkPanel:showTalkContent()
	---结束了，不用再播了
	if self.talkEnded then return end

	self.talkOption = false
	self.WBP_NPCTalk_TextCom:SetContentVisibleOrNot(true)
	local talkInfo = self.talkInfo

	if talkInfo.TalkType == Game.NPCSystem.TalkType.Content then
		self:showContent()
	elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Option then
		self:showOption()
	elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Jump then
		local jumpID = talkInfo.JumpID
		self:completeTalk()
		Game.UIJumpSystem:JumpToUI(jumpID)
	elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Finally then
		self:showFinally(talkInfo)
	elseif talkInfo.TalkType == Game.NPCSystem.TalkType.Dialogue then
		self:showDialogue(talkInfo)
	elseif talkInfo.TalkType == Game.NPCSystem.TalkType.WaitForEnd then
		self:showWaitForEnd(talkInfo)
	elseif talkInfo.TalkType == Game.NPCSystem.TalkType.NONE then
		if talkInfo.Func then
			talkInfo.Func()
		end
	end
end

---@private 点击屏幕
function NPCTalkPanel:onNPCCameraClick()
	if self.talkOption == false then
		--当前不为对话选项界面
		self:updateTalkInfo()
	end
end

---@private 完成对话
function NPCTalkPanel:completeTalk(closeTalk)
	Game.EventSystem:Publish(_G.EEventTypes.NPC_ON_FINISH_TALK, self.curTalkID, self.npcCfgID)
	self:endTalk(closeTalk)
end

function NPCTalkPanel:OnDragDetected(MyGeometry, PointEvent)
	--self.bIsDrag = true
	--return nil
end

function NPCTalkPanel:SetKeyHint(index)
	if self.bShowHint then
		local component = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
		if self.showHintIndex and self.showHintIndex ~= index then
			if component then
				component:SetKeyHint(false)
			end
			self.showHintIndex = 0
		end

		self.showHintIndex = index
		component = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
		if component then
			component:SetKeyHint(true)
		end
	else
		local oldHoverIndex = self.curHoverIndex
		if oldHoverIndex then
			---@type P_NPCSelectItem
			local oldComp = self.WBP_Interact_SelectListCom:GetItemByIndex(oldHoverIndex)
			if oldComp then
				oldComp:UnHovered()
			end
		end
		self.curHoverIndex = index
		---@type P_NPCSelectItem
		local comp = self.WBP_Interact_SelectListCom:GetItemByIndex(self.curHoverIndex)
		if comp then
			comp:OnHovered()
		end
	end
end

function NPCTalkPanel:OnHudInteractAxis(value)
	if self.bShowHint and self.showHintIndex and self.Interactlist.total > 1 then
		if value > 0 then
			--上移
			if self.showHintIndex > 1 then
				local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
				if cell then
					cell:SetKeyHint(false)
				end
				self.showHintIndex = self.showHintIndex - 1
				local curCell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
				if curCell then
					curCell:SetKeyHint(true)
				end
			end
		else
			--下移
			if self.showHintIndex < self.Interactlist.total then
				local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
				if cell then
					cell:SetKeyHint(false)
				end
				self.showHintIndex = self.showHintIndex + 1
				local curCell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
				if curCell then
					curCell:SetKeyHint(true)
				end
			end
		end
	end
end

---@public 道具提交界面关闭
function NPCTalkPanel:OnItemSubmitHide()
	self.bSubmit = true
	self:OnResumeInteract()
end

function NPCTalkPanel:OnResumeInteract(bCloseBySubmit)
	if self.bWaitForFun then
		if self.bSubmit then
			if self.talkInfo.QuestID then
				local conditionCfg = Game.QuestSystem:GetConditionCfg(self.talkInfo.QuestID, self.talkInfo.QuestConditionIndex)
				if conditionCfg.ItemSubmitID then
					local playDialogueKind = conditionCfg.PlayDialogueKind
					if playDialogueKind and playDialogueKind ==  Game.QuestSystem.PlayDialogueEnum.After then
						self.talkInfo.TalkType = Game.NPCSystem.TalkType.Dialogue
						self.talkInfo.DialogueID = conditionCfg.DialogueID
						return self:showTalkContent()
					end
				end
			end
			self:completeTalk()
		else
			if not bCloseBySubmit then
				self:completeTalk()
			end
		end
	end
end

---@private 结束对话
function NPCTalkPanel:endTalk(closeTalk)
	if closeTalk == nil then
		closeTalk = true
	end
	self.talkEnded = true
	Game.ItemSubmitSystem:HideItemSubmitUI()

	self.bWaitForFun = false
	table.clear(self.talkInfo)
	self.WBP_Interact_SelectListCom:Clear()

	local entity = Game.EntityManager:getEntity(self.npcEntityID)

	if Game.CameraManager == nil then
		--- 游戏退出了
		self:CloseSelf()
		return
	end

	if entity then
		if entity.OnFinishTalkWithPlayer then
			entity:OnFinishTalkWithPlayer()
		end
		local npcData = entity:GetEntityConfigData()
		local cfgID, entityID = self.npcCfgID, self.npcEntityID
		if npcData.TurnToPlayer then
			-- 转身结束逻辑也是timer做的，只能丑陋的保持一样的timer，暂时也没取消的逻辑
			local timerId = Game.TimerManager:CreateTimerAndStart(
				function()  
					Game.me:ReqLeaveNpcTalk(cfgID, entityID)
					Game.NPCSystem.model:OnDelayNPCLeaveTalk(cfgID, entityID)
				end,
				1000, 1
			)
			Game.NPCSystem.model:StartDelayNPCLeaveTalk(timerId, cfgID, entityID)
		else
			Game.me:ReqLeaveNpcTalk(cfgID, entityID)
		end
	end

	self.npcEntityID = nil
	self.npcCfgID = nil
	self.bSubmit = false
	if closeTalk then
		self:CloseSelf()
	end
	Game.QuestSystem:LeaveDialogueControlState()
	Game.EventSystem:Publish(_G.EEventTypes.INTERACTIVE_FINISH)
end

function NPCTalkPanel:revertPlayerAndNpcActsOnClose()
	Game.CameraManager:EnableInteractiveCamera(false)
	self:revertPlayerAndNpcActs()
	local entity = Game.EntityManager:getEntity(self.npcEntityID)
	xpcall(HandleLookAtLoc, _G.CallBackError, Game.me, entity, false)
end

function NPCTalkPanel:OnClose()
	self:revertMainPlayerPos()
	self:revertPlayerAndNpcActsOnClose()
	self.talkID = nil
	self.curTalkID = nil
	UIManager:GetInstance():RestoreAllPanel(UIConst.HIDE_PANELS_SOURCE_TYPE.NPC)

	EnableZoomInput(true)
	Game.ScreenInputManager.InitScreenInputState()
	Game.HUDInteractManager:OnNPCUIHide()
end

function NPCTalkPanel:ConfigKeyHint(bShow)

end

function NPCTalkPanel:KeyOnInput(ActionName, InputEvent)
	--TODO：
	if self.bShowHint and InputEvent == 0  then
		if ActionName == Enum.EInputType.Dialogue_Action then
			--空格
			self:onNPCCameraClick()
		elseif ActionName == Enum.EInputType.OpenInteractivePanel_Action then
			--F键
			local uicomponet = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
			if uicomponet then
				uicomponet:OnItemClicked()
			end
		end
	end
	return UIBase.HANDLED
end

--临时：倒计时系统做出选择
function NPCTalkPanel:OnChoiceMadeByCountDown(Index)
	Index = Index or 1
	local uicomponet = self.WBP_Interact_SelectListCom:GetItemByIndex(Index)
	if uicomponet then
		uicomponet:OnItemClicked()
	end
end

function NPCTalkPanel:OnClickBack()
	self:endTalk()
end

function NPCTalkPanel:EscOnClickEvent()
	self:endTalk()
end

function NPCTalkPanel:OnTouchStarted(InGeometry, InGestureEvent)
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(InGestureEvent)
	if PointerIndex > 0 then
		return UIBase.HANDLED
	end
	local ScreenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(InGestureEvent)
	Game.CameraInputManager:OnTouchStarted(SlateBlueprintLibrary.AbsoluteToLocal(InGeometry, ScreenPos))
	return UIBase.HANDLED
end

function NPCTalkPanel:OnTouchMoved(InGeometry, InGestureEvent)
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(InGestureEvent)
	if PointerIndex > 0 then
		return UIBase.HANDLED
	end
	local ScreenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(InGestureEvent)
	Game.CameraInputManager:OnTouchMoved(SlateBlueprintLibrary.AbsoluteToLocal(InGeometry, ScreenPos))
	return UIBase.HANDLED
end

function NPCTalkPanel:OnTouchEnded(InGeometry, InGestureEvent)
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(InGestureEvent)
	if PointerIndex > 0 then
		return UIBase.HANDLED
	end
	Game.CameraInputManager:OnTouchEnded()
	self:onNPCCameraClick()
	return UIBase.HANDLED
end

function NPCTalkPanel:OnLeaveInteract(entityID)
	if self.npcEntityID == entityID then
		self:endTalk()
	end
end

function NPCTalkPanel:CheckSelectListItemShowHint(index)
	return index == self.showHintIndex and self.bShowHint
end

function NPCTalkPanel:JumpSubTalk(index)
	local curTalkInfoId = self.talkInfo.TalkID
	local talkData = Game.TableData.GetTalkDataRow(curTalkInfoId)
	if talkData == nil then
		return
	end
	local talkCfg = talkData[index]
	if talkCfg == nil then
		Log.ErrorFormat("NPCTalkPanel:JumpSubTalk, talkCfg is nil, curTalkInfoId:%d, index:%d", curTalkInfoId, index)
		return
	end
	self.talkInfo.TalkType = Game.NPCSystem.TalkType.Content
	self.talkInfo.Order = index
	self:showTalkContent()
end

function NPCTalkPanel:moveMainPlayerToTalkPos(npcEntity)
	if npcEntity == nil then return end
	local playerPos = Game.me:GetPosition()
	self.mainPlayerEnterPos = playerPos
	local npcPos = npcEntity:GetPosition()
	-- 计算NPC到玩家的方向向量
	local direction = playerPos - npcPos
	direction:Normalize(1e-8) -- 归一化为单位向量

	-- 计算NPC沿着连线方向1.5米的位置
	local npcCfgId = npcEntity.TemplateID
	local npcInfoData = Game.TableData.GetNpcInfoDataRow(npcCfgId)
	local targetPos = npcPos + direction * npcInfoData.NpcTalkDistance * 100 -- 1.5米 = 150厘米
	-- 检查一下位置是否合理
	if self:checkTargetPosValid(npcPos, targetPos) then
		Game.me:SetPosition(targetPos)
	else
		
	end
end

function NPCTalkPanel:findOtherValidPosAroundNpc(startPos, direction, radius)
	local stepAngle = 45 -- 45度步长
	local maxSteps = 8 -- 360度 / 45度 = 8个位置

	-- 将direction转换为角度（假设direction是一个方向向量）
	local startAngle = math.atan2(direction.Y, direction.X) * 180 / math.pi

	for i = 0, maxSteps - 1 do
		-- 计算当前角度（逆时针）
		local currentAngle = startAngle - (i * stepAngle)
		-- 转换为弧度
		local angleRad = currentAngle * math.pi / 180
		-- 计算圆周上的位置
		local endPos = QuestUtils.GetTempFVectorPos()
		endPos.X = startPos.X + radius * math.cos(angleRad)
		endPos.Y = startPos.Y + radius * math.sin(angleRad)
		endPos.Z = startPos.Z
		if self:checkTargetPosValid(startPos, endPos) then
			return endPos
		end
	end
	return nil
end

function NPCTalkPanel:checkTargetPosValid(startPos, targetPos)
	-- 检测1: 从NPC到目标位置是否有障碍物阻挡
	if QuestUtils.CheckHasObstacleBetween(startPos, targetPos) then return false end
	-- 检测2：从targetPos向下检测是否坠落
	if QuestUtils.CheckFallFromTargetPos(targetPos) then return false end
	return true
end

function NPCTalkPanel:revertMainPlayerPos()
	if self.mainPlayerEnterPos == nil then return end
	Game.me:SetPosition(self.mainPlayerEnterPos)
	self.mainPlayerEnterPos = nil
end

return NPCTalkPanel
