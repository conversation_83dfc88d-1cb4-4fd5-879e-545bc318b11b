local ManorUtils = {}
local table_remove = table.remove
local table_insert = table.insert

---@class ManorVec3
---@field X number
---@field Y number
---@field Z number

---@class ManorVec2
---@field X number
---@field Y number


ManorUtils.MaxCacheVec3Cnt = 100
ManorUtils.CurCacheVec3Cnt = 0
ManorUtils.MaxCacheVec2Cnt = 100
ManorUtils.CurCacheVec2Cnt = 0
---@type ManorVec3[]
ManorUtils.CacheVec3 = {}
---@type ManorVec2[]
ManorUtils.CacheVec2 = {}


---@class ManorBarrieInfo
---@field Pos ManorVec3
---@field Size ManorVec2

---@return ManorVec3
function ManorUtils.GetVec3(x, y, z)
	local ret = table_remove(ManorUtils.CacheVec3)
	if ret == nil then
		ret = {X = x, Y = y, Z = z}
	else
		ret.X = x
		ret.Y = y
		ret.Z = z
		ManorUtils.CurCacheVec3Cnt = ManorUtils.CurCacheVec3Cnt - 1
	end
	return ret
end

---@param vec3 ManorVec3
function ManorUtils.ReturnVec3(vec3)
	if ManorUtils.CurCacheVec3Cnt > ManorUtils.MaxCacheVec3Cnt then
		return
	end
	table_insert(ManorUtils.CacheVec3, vec3)
	ManorUtils.CurCacheVec3Cnt = ManorUtils.CurCacheVec3Cnt + 1
end

---@return ManorVec3
function ManorUtils.GetVec2(x, y)
	local ret = table_remove(ManorUtils.CacheVec2)
	if ret == nil then
		ret = {X = x, Y = y}
	else
		ret.X = x
		ret.Y = y
		ManorUtils.CurCacheVec2Cnt = ManorUtils.CurCacheVec2Cnt - 1
	end
	return ret
end

---@param vec2 ManorVec2
function ManorUtils.ReturnVec2(vec2)
	if ManorUtils.CurCacheVec2Cnt > ManorUtils.MaxCacheVec2Cnt then
		return
	end
	table_insert(ManorUtils.CacheVec2, vec2)
	ManorUtils.CurCacheVec2Cnt = ManorUtils.CurCacheVec2Cnt + 1
end

---@return ManorBarrieInfo
function ManorUtils.ConstructBarrieInfo(posX, posY, posZ, sizeX, sizeY)
	return {
		Pos = ManorUtils.GetVec3(posX, posY, posZ),
		Size = ManorUtils.GetVec2(sizeX, sizeY)
	}
end

---@param barrieInfo ManorBarrieInfo
function ManorUtils.DestructBarrieInfo(barrieInfo)
	ManorUtils.ReturnVec3(barrieInfo.Pos)
	ManorUtils.ReturnVec2(barrieInfo.Size)
end

function ManorUtils.GetBarrierSize(barrierModId)
	local modelAssetData = Game.TableData.GetCommonInteractorModelDataRow(barrierModId)
	local meshCollision = modelAssetData.SkeletalMeshCollision
	local meshType = meshCollision[1]
	-- 这里填表的3，表示是Box
	if meshType == 3 then
		return meshCollision[2], meshCollision[3]
	else
		-- 给个默认值
		Log.DebugErrorFormat("Manor GetBarrierSize: SkeletalMeshCollision type not correct, must be 3, modelId: %s", barrierModId)
		return 100, 100
	end
end

function ManorUtils.GetMainPlayerHomelandLv()
	return Game.me.HomelandLevel
end

function ManorUtils.GetFogMatParamKeyByHomeLv(lv)
	local showKeyLv = 1
	if lv >= 8 then 
		showKeyLv = 8
	elseif lv >= 6 then
		showKeyLv = 6
	elseif lv >= 4 then
		showKeyLv = 4
	elseif lv >= 2 then
		showKeyLv = 2
	end
	return string.format("Level%d", showKeyLv)
end

return ManorUtils