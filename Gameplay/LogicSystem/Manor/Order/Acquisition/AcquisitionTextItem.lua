local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class AcquisitionTextItem : UIListItem
---@field view AcquisitionTextItemBlueprint
local AcquisitionTextItem = DefineClass("AcquisitionTextItem", UIListItem)

AcquisitionTextItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function AcquisitionTextItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function AcquisitionTextItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function AcquisitionTextItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function AcquisitionTextItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function AcquisitionTextItem:InitUIView()
end

---面板打开的时候触发
function AcquisitionTextItem:OnRefresh(params)
    self.view.Text_Title:SetText(params.titleName or "")
    self.view.Text_Description:SetText(params.description or "")
end

return AcquisitionTextItem
