local PCGBuildRoomsItem = DefineClass("PCGBuildRoomsItem", UIComponent)

function PCGBuildRoomsItem:OnCreate()
	--- 索引
	self.index = 1
	--- 是否选中
	self.isSelected = false
	self:AddUIListener(EUIEventTypes.Hovered, self.View.Btn, self.OnHovered_Btn)
	self:AddUIListener(EUIEventTypes.Unhovered, self.View.Btn, self.OnUnHovered_Btn)
	self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn, self.OnClick_Btn)
end

function PCGBuildRoomsItem:OnClose()

end

function PCGBuildRoomsItem:OnRefresh()

end

function PCGBuildRoomsItem:Refresh(index, selected)
	self.index = index
	self.isSelected = selected
	self.View.Text_CustomButton:SetText(tostring(index) .. Game.TableData.GetManorSettingRow("PCGRoomStr"))
	self.View:Event_UI_Style(0, selected)
end

function PCGBuildRoomsItem:OnHovered_Btn()
	self.View:Event_UI_Style(0, true)
end

function PCGBuildRoomsItem:OnUnHovered_Btn()
	if self.isSelected == false then
		self.View:Event_UI_Style(0, false)
	end
end

function PCGBuildRoomsItem:OnClick_Btn()
	self.parent.owner:OnClick_RoomItem(self.index)
end

return PCGBuildRoomsItem