local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
---@class ManorList_Item : UIListItem
---@field view ManorList_ItemBlueprint
local ManorList_Item = DefineClass("ManorList_Item", UIListItem)

ManorList_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ManorList_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ManorList_Item:InitUIData()
	self.data = nil
end

--- UI组件初始化，此处为自动生成
function ManorList_Item:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function ManorList_Item:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.view.WBP_HeadIcon.Btn_ClickArea.OnClicked, "on_WBP_HeadIconBtn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ManorList_Item:InitUIView()
	self.WBP_ComBtnCom:Refresh(StringConst.Get("MANOR_VISIT_HOME"))
	self.view.Img_Select:SetVisibility(ESlateVisibility.Collapsed)
end

---面板打开的时候触发
function ManorList_Item:OnRefresh(data)
	self.data = data
	local portal = Game.TableData.GetInterfaceAppearancePortraitDataRow(data.portrait)
	if portal then
		local iconPath = portal.ResourcePath
		self:SetImage(self.view.WBP_HeadIcon.Img_HeadIcon, iconPath)
	end
	self.view.TB_RoleName:SetText(data.rolename)
	self.view.TB_LV:SetText(data.homelandLevel)
	self.view.TB_Score:SetText(data.homelandTotalScore)
	if string.isEmpty(data.homelandName) then
		self.view.TB_HomeName:SetText(StringConst.Get("MANOR_DEFAULT_NAME", data.rolename))
	else
		self.view.TB_HomeName:SetText(data.homelandName)
	end
	self.userWidget:Event_UI_Style((data.index % 2) == 1, false)
end

--- 此处为自动生成
function ManorList_Item:on_WBP_ComBtnCom_ClickEvent()
	Game.ManorSystem:ReqEnterManor(self.data.avatarID)
end

function ManorList_Item:UpdateSelectionState(selected)
	if selected then
		self.view.Img_Select:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.Img_Select:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--- 此处为自动生成
function ManorList_Item:on_WBP_HeadIconBtn_ClickArea_Clicked()
	--点击头像弹出个人信息
	Game.TeamSystem:ReqOtherRoleOnlineBrief(self.data.avatarID)
end

return ManorList_Item
