local ManorSearch = kg_require("Gameplay.LogicSystem.Manor.Modules.UI.ManoInfo.ManorSearch")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ManorCommunity : UIComponent
---@field view ManorCommunityBlueprint
local ManorCommunity = DefineClass("ManorCommunity", UIComponent)

ManorCommunity.eventBindMap = {
	[EEventTypesV2.ON_MANOR_RECEIVED_FRIENDS_INFO] = "OnReceivedFriendsInfo",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ManorCommunity:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ManorCommunity:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ManorCommunity:InitUIComponent()
    ---@type ManorSearch
    self.WBP_ManorSearchCom = self:CreateComponent(self.view.WBP_ManorSearch, ManorSearch)
    ---@type UIListView childScript: ManorList
    self.ListViewCom = self:CreateComponent(self.view.ListView, UIListView)
    ---@type UIComEmptyConent
    self.WBP_ComEmptyCom = self:CreateComponent(self.view.WBP_ComEmpty, UIComEmptyConent)
end

---UI事件在这里注册，此处为自动生成
function ManorCommunity:InitUIEvent()
    self:AddUIEvent(self.view.WBP_Swipe.WBP_SwipeItem.Btn_ClickArea.OnClicked, "on_WBP_SwipeWBP_SwipeItemBtn_ClickArea_Clicked")
    self:AddUIEvent(self.view.WBP_Swipe.WBP_SwipeItem_1.Btn_ClickArea.OnClicked, "on_WBP_SwipeWBP_SwipeItem_1Btn_ClickArea_Clicked")
    self:AddUIEvent(self.WBP_ManorSearchCom.onSearchResult, "on_WBP_ManorSearchCom_SearchResult")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ManorCommunity:InitUIView()
	self.view.WBP_Swipe.WBP_SwipeItem.Text_Name:SetText(StringConst.Get("MANOR_SOCIAL_TAB_FRIEND"))
	self.view.WBP_Swipe.WBP_SwipeItem_1.Text_Name:SetText(StringConst.Get("MANOR_SOCIAL_TAB_RECOMMENDATION"))
	self:on_WBP_SwipeWBP_SwipeItemBtn_ClickArea_Clicked()
end

---组件刷新统一入口
function ManorCommunity:Refresh()
	Game.ManorSystem.ManorInfoModule:ReqFriendsHomeInfo()
end

--- 接收到好友家园信息再刷新列表
function ManorCommunity:OnReceivedFriendsInfo()
	local data = Game.ManorSystem.ManorInfoModule:GetFriendsHomeInfo()
	self.WBP_ManorSearchCom:SetSearchSource(data, "rolename")
	self:UpdateList(data)
end

--- 此处为自动生成
function ManorCommunity:on_WBP_SwipeWBP_SwipeItemBtn_ClickArea_Clicked()
	self.view.WBP_Swipe.WBP_SwipeItem:Event_UI_Style(true)
	self.view.WBP_Swipe.WBP_SwipeItem_1:Event_UI_Style(false)
end

--- 此处为自动生成
function ManorCommunity:on_WBP_SwipeWBP_SwipeItem_1Btn_ClickArea_Clicked()
	--todo 等待策划确认推荐展示信息
end

--- 此处为自动生成
---@param searchResults table
function ManorCommunity:on_WBP_ManorSearchCom_SearchResult(searchResults)
	self:UpdateList(searchResults)
end

---更新列表
function ManorCommunity:UpdateList(data)
	if #data == 0 then
		self.WBP_ComEmptyCom:Show()
		self.ListViewCom:Refresh(data)
		return
	end
	self.WBP_ComEmptyCom:Hide()
	table.sort(data, function(a, b)
		if a.homelandTotalScore ~= b.homelandTotalScore then
			return a.homelandTotalScore > b.homelandTotalScore
		end
		return a.shortUid < b.shortUid
	end)
	for idx, item in ipairs(data) do
		item.index = idx
	end
	self.ListViewCom:Refresh(data)
end

return ManorCommunity
