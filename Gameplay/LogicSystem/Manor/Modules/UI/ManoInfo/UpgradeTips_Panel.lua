local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class UpgradeTips_Panel : UIPanel
---@field view UpgradeTips_PanelBlueprint
local UpgradeTips_Panel = DefineClass("UpgradeTips_Panel", UIPanel)

UpgradeTips_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UpgradeTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UpgradeTips_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function UpgradeTips_Panel:InitUIComponent()
    ---@type UIListView childScript: UpgradeList
    self.ListViewCom = self:CreateComponent(self.view.ListView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function UpgradeTips_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UpgradeTips_Panel:InitUIView()
end

---面板打开的时候触发
function UpgradeTips_Panel:OnRefresh()
	local data = self:GetListData()
	self.ListViewCom:Refresh(data)
end

---获取升级条件数据
function UpgradeTips_Panel:GetListData()
	local level = Game.ManorSystem.ManorInfoModule:GetManorLevel()
	local levelUpData = Game.TableData.GetManorLevelUpDataRow(level)
	local data = {}
	--角色等级
	if levelUpData.roleLevelLimit then
		table.insert(data, {
			Type = Enum.EManorUpgradeRestrictionType.PlayerLevel,
			Value = { levelUpData.roleLevelLimit },
		})
	end
	--总分
	if levelUpData.totalScore then
		table.insert(data, {
			Type = Enum.EManorUpgradeRestrictionType.TotalScore,
			Value = { levelUpData.totalScore },
		})
	end
	--放置分
	if levelUpData.putScore then
		table.insert(data, {
			Type = Enum.EManorUpgradeRestrictionType.PutScore,
			Value = { levelUpData.putScore },
		})
	end
	--工坊等级
	if levelUpData.workShopLevel then
		for _, v in ipairs(levelUpData.workShopLevel) do
			table.insert(data, {
				Type = Enum.EManorUpgradeRestrictionType.WorkshopLevel,
				Value = { v[1], v[2] },
			})
		end
	end
	--家园币
	if levelUpData.levelUpConsume then
		table.insert(data, {
			Type = Enum.EManorUpgradeRestrictionType.ManorCurrency,
			Value = { levelUpData.levelUpConsume },
		})
	end
	return data
end

return UpgradeTips_Panel
