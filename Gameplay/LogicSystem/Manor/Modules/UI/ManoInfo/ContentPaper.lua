local ManorUpgradeBtn = kg_require("Gameplay.LogicSystem.Manor.Modules.UI.ManoInfo.ManorUpgradeBtn")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ItemConst = kg_require("Shared.ItemConst")
---@class ContentPaper : UIComponent
---@field view ContentPaperBlueprint
local ContentPaper = DefineClass("ContentPaper", UIComponent)

ContentPaper.eventBindMap = {
	[EEventTypesV2.ON_MANOR_NAME_MODIFY] = "OnManorNameModify",
	[EEventTypesV2.ON_MANOR_LEVEL_UPGRADE] = "OnManorLevelUpgrade",
	[EEventTypesV2.ON_MANOR_INTRODUCTION_MODIFY] = "UpdateIntroduction",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ContentPaper:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ContentPaper:InitUIData()
	self.bIntroductionEditable = nil
	self.introductionLimit = Game.TableData.GetManorConstDataRow("HOME_INTRODUCTION_MAX_LEN")
end

--- UI组件初始化，此处为自动生成
function ContentPaper:InitUIComponent()
    ---@type ManorUpgradeBtn
    self.WBP_UpgradeBtnCom = self:CreateComponent(self.view.WBP_UpgradeBtn, ManorUpgradeBtn)
end

---UI事件在这里注册，此处为自动生成
function ContentPaper:InitUIEvent()
    self:AddUIEvent(self.view.Click_HotArea_Rename.OnClicked, "on_Click_HotArea_Rename_Clicked")
    self:AddUIEvent(self.view.Click_HotArea_ReqEnterManor.OnClicked, "on_Click_HotArea_ReqEnterManor_Clicked")
    self:AddUIEvent(self.view.Click_HotArea_ChangeIntroduction.OnClicked, "on_Click_HotArea_ChangeIntroduction_Clicked")
    self:AddUIEvent(self.view.WBP_CoinBtn.Click_HotArea.OnClicked, "on_WBP_CoinBtnClick_HotArea_Clicked")
    self:AddUIEvent(self.view.WBP_TotalScoreBtn.Click_HotArea.OnClicked, "on_WBP_TotalScoreBtnClick_HotArea_Clicked")
	self:AddUIEvent(self.view.TB_Introduction.OnTextChanged, "on_TB_Introduction_TextChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ContentPaper:InitUIView()
	--不会变的文本在这里显示，策划要求文字常量也要在 Excel 配置
	self.view.TB_PutScoreDesc:SetText(Game.TableData.GetManorConstDataRow("PutScoreStr"))
	self.view.TB_AreaSizeDesc:SetText(Game.TableData.GetManorConstDataRow("AreaSizeStr"))
	self.view.TB_ReservedText_1:SetText(Game.TableData.GetManorConstDataRow("ReservedText1Str"))
	self.view.TB_ReservedText_2:SetText(Game.TableData.GetManorConstDataRow("ReservedText2Str"))
	self.view.TB_TotalScoreDesc:SetText(Game.TableData.GetManorConstDataRow("TotalScoreStr"))
	self.view.TB_CoinDesc:SetText(Game.TableData.GetManorConstDataRow("CoinStr"))
	self.view.TB_CoinLimitDesc:SetText(Game.TableData.GetManorConstDataRow("CoinLimitStr"))
	self.view.TB_AcquiredThisWeekDesc:SetText(Game.TableData.GetManorConstDataRow("AcquiredThisWeekStr"))
end

---组件刷新统一入口
function ContentPaper:Refresh()
	self:SetText()
	Game.RedDotSystem:RegisterRedDot("HomeUpgradeBtn")
	self.WBP_UpgradeBtnCom:Refresh()
	self.bIntroductionEditable = false
	self.view.TB_Introduction:SetIsEnabled(false)
end

function ContentPaper:SetText()
	self.view.TB_Level:SetText(Game.ManorSystem.ManorInfoModule:GetManorLevel())
	local manorName = Game.ManorSystem.ManorInfoModule:GetManorName()
	if string.isEmpty(manorName) then
		local name = StringConst.Get("MANOR_DEFAULT_NAME", Game.me.Name)
		self.view.TB_HomeName:SetText(name)
	else
		self.view.TB_HomeName:SetText(manorName)
	end
	self.view.TB_PutScore:SetText(Game.ManorSystem.ManorInfoModule:GetManorPutScore())
	self.view.TB_AreaSize:SetText(Game.ManorSystem.ManorInfoModule:GetManorAreaSize())
	self:UpdateTotalScore()
	self:UpdateCoinLimit()
	self:UpdateAcquiredCoinThisWeek()
	self:UpdateIntroduction()
end

--- 此处为自动生成
function ContentPaper:on_Click_HotArea_Rename_Clicked()
	Game.MessageBoxSystem:AddPopupByConfigParam({
		key = Enum.EDialogPopUpData.MANOR_CHANG_NAME,
		accept = function(name)
			self:ReqModifyHomeName(name)
		end,
		inputParam = {
			hintText = Game.TableData.GetManorConstDataRow("DefaultRenameStr"),
			length = Game.TableData.GetManorConstDataRow("HOME_NAME_MAX_LEN"),
			checkInputFunc = function(name)
				return self:CheckNameIsValid(name)
			end
		},
	})
end

---检查家园名称是否合法
function ContentPaper:CheckNameIsValid(name)
	if string.isEmpty(name) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_RENAME_EMPTY)
		return false
	end
	
	if name == Game.ManorSystem.ManorInfoModule:GetManorName() then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_RENAME_SAME)
		return false
	end
	
	return true
end

---修改家园名称
function ContentPaper:ReqModifyHomeName(name)
	local data = Game.TableData.GetManorConstDataRow("HOME_MODIFY_NAME_COST")
	local needPound = data[2]
	local curBoundPound = Game.BagSystem:GetItemCount(data[1])
	local boundPoundIconPath = Game.UIIconUtils.GetIconByItemId(data[1], nil, true)
	--再弹出一个购买确认界面
	--绑定金镑足够时
	if curBoundPound >= needPound then
		Game.MessageBoxSystem:AddPopupByConfigParam({
			key = Enum.EDialogPopUpData.MONEY_CONSUME_BOUND_POUNDS,
			accept = function()
				Game.ManorSystem.ManorInfoModule:ReqModifyHomeName(name)
			end,
			props = {{propId = data[1], num = needPound}},
		})
		return
	end
	
	local curPound = Game.BagSystem:GetItemCount(ItemConst.ITEM_SPECIAL_MONEY_COIN)
	local poundIconPath = Game.UIIconUtils.GetIconByItemId(ItemConst.ITEM_SPECIAL_MONEY_COIN, nil, true)
	--绑定金镑不够，要加上金镑才够
	if curBoundPound + curPound >= needPound then
		Game.MessageBoxSystem:AddPopupByConfigParam({
			key = Enum.EDialogPopUpData.MONEY_RMBEXCHANGE,
			accept = function()
				Game.ManorSystem.ManorInfoModule:ReqModifyHomeName(name)
			end,
			content = { boundPoundIconPath, curBoundPound, poundIconPath, needPound - curBoundPound },
		})
		return
	end
	
	--绑定金榜和金镑加起来都不够
	Game.MessageBoxSystem:AddPopupByConfigParam({
		key = Enum.EDialogPopUpData.MONEY_NEEDRMB,
		accept = function()
			Game.UIJumpSystem:JumpToUI(1250004)
		end,
		content = { needPound },
	})
end

--- 此处为自动生成
function ContentPaper:on_Click_HotArea_ReqEnterManor_Clicked()
	if Game.ManorSystem:IsInSelfManorSpace() then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_ALREADY_IN_HOME)
		return
	end
	Game.ManorSystem:ReqEnterManor(Game.me.eid)
end

---总评分Tips
function ContentPaper:on_WBP_TotalScoreBtnClick_HotArea_Clicked()
	Game.TipsSystem:ShowTips(Enum.ETipsData.MANOR_TOTAL_SCORE, self.view.WBP_TotalScoreBtn:GetCachedGeometry())
end

---家园币tips
function ContentPaper:on_WBP_CoinBtnClick_HotArea_Clicked()
	Game.TipsSystem:ShowTips(Enum.ETipsData.MANOR_CURRENCY_2, self.view.WBP_CoinBtn:GetCachedGeometry())
end

---点击修改家园简介按钮
function ContentPaper:on_Click_HotArea_ChangeIntroduction_Clicked()
	if self.bIntroductionEditable then
		self.bIntroductionEditable = false
		self.view.TB_Introduction:SetIsEnabled(false)
		Game.ManorSystem.ManorInfoModule:ReqModifyHomeIntroduction(self.view.TB_Introduction:GetText())
	else
		self.bIntroductionEditable = true
		self.view.TB_Introduction:SetIsEnabled(true)
	end
end

--- 此处为自动生成
---@param text FText
function ContentPaper:on_TB_Introduction_TextChanged(text)
	self.view.TB_IntroductionCharactersNum:SetText(string.format("%s/%s", utf8.len(text), self.introductionLimit))
end

function ContentPaper:OnManorNameModify()
	self.view.TB_HomeName:SetText(Game.ManorSystem.ManorInfoModule:GetManorName())
end

---家园升级时更新面板UI
function ContentPaper:OnManorLevelUpgrade()
	self.view.TB_Level:SetText(Game.ManorSystem.ManorInfoModule:GetManorLevel())
	self.WBP_UpgradeBtnCom:SetStyle()
	self:UpdateTotalScore()
	self:UpdateCoinLimit()
	self:UpdateAcquiredCoinThisWeek()
end

---更新家园总评分UI
function ContentPaper:UpdateTotalScore()
	local totalScore = Game.ManorSystem.ManorInfoModule:GetTotalScore()
	local totalScoreForNextLevel = Game.ManorSystem.ManorInfoModule:GetTotalScoreForNextLevel()
	self.view.RichText_TotalScore:SetText(StringConst.Get("MANOR_TOTAL_SCORE", totalScore, totalScoreForNextLevel))
	self.view.Slider_Score:SetPercent(totalScore / totalScoreForNextLevel)
end

---更新家园币上限UI
function ContentPaper:UpdateCoinLimit()
	local curMoney = Game.ManorSystem.ManorInfoModule:GetManorCurrencyNum()
	local maxMoney = Game.ManorSystem.ManorInfoModule:GetManorCurrencyStorageLimit()
	self.view.TB_CoinLimit:SetText(StringConst.Get("MANOR_COIN_LIMIT", curMoney, maxMoney))
end

---更新本周已获得的家园币UI
function ContentPaper:UpdateAcquiredCoinThisWeek()
	local acquiredMoneyThisWeek = Game.ManorSystem.ManorInfoModule:GetWeeklyAcquiredManorCurrencyNum()
	local weeklyLimit = Game.ManorSystem.ManorInfoModule:GetManorCurrencyWeeklyLimit()
	self.view.RichText_AcquiredThisWeek:SetText(StringConst.Get("MANOR_CURRENCY_ACQUIRED_THIS_WEEK", acquiredMoneyThisWeek, weeklyLimit))
end

---更新家园简介UI
function ContentPaper:UpdateIntroduction()
	local introduction = Game.ManorSystem.ManorInfoModule:GetManorIntroduction()
	if not introduction then
		local hintText = Game.TableData.GetManorConstDataRow("DefaultIntroductionStr")
		self.view.TB_Introduction:SetHintText(hintText)
	end
	self.view.TB_Introduction:SetText(introduction)
	self.view.TB_IntroductionCharactersNum:SetText(string.format("%s/%s", utf8.len(introduction), self.introductionLimit))
end

return ContentPaper
