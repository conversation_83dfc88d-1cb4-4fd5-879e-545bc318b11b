local ItemSmall = kg_require("Gameplay.LogicSystem.Item.ItemSmall")
local BuildList = kg_require("Gameplay.LogicSystem.Manor.Modules.UI.Build.BuildList")
local P_KeyPrompt = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.P_KeyPrompt")
local HomeConst = kg_require("Shared.Const.HomeConst")
local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class Build_Panel : UIPanel
---@field view BuildBlueprint
local Build_Panel = DefineClass("Build_Panel", UIPanel)

Build_Panel.eventBindMap = {
	[EEventTypesV2.ON_BUILD_PLACE_NODE_CHANGE] = "OnEditNodeChange",
	[EEventTypesV2.ON_BUILD_SELECT_NODE_CHANGE] = "OnUpdateSelectNode",
	[EEventTypesV2.ON_BUILD_CANT_PLACE_UPDATE] = "OnUpdateCantBuild",
	[EEventTypesV2.ON_MANOR_STORE_ITEM_UPDATE] = "OnStoreFurnitureUpdate",
	[EEventTypesV2.ON_MANOR_FURNITURE_UPDATE] = "OnFurnitureUpdate",
	[EEventTypesV2.ON_FURNITURE_UNLOCK] = "OnFurnitureUnlock",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Build_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Build_Panel:InitUIData()
	---当前左侧的Tab列表是否可见
	self.isListVisible = true
end

--- UI组件初始化，此处为自动生成
function Build_Panel:InitUIComponent()
    ---@type ItemSmall
    self.WBP_NPCBtnSelect_WBP_Item_luaCom = self:CreateComponent(self.view.WBP_NPCBtnSelect.WBP_Item_lua, ItemSmall)
    ---@type BuildList
    self.WBP_BuildListCom = self:CreateComponent(self.view.WBP_BuildList, BuildList)

	--确认
	self.childKeyPromptConfirm = self:CreateComponent(self.view.WBP_ButtonConfirm.WBP_KeyPrompt, P_KeyPrompt)
	self.childKeyPromptConfirm:Refresh("LeftMouse",nil)
	--取消
	self.childKeyPromptCancel = self:CreateComponent(self.view.WBP_ButtonCancel.WBP_KeyPrompt, P_KeyPrompt)
	self.childKeyPromptCancel:Refresh(nil,"Space")
	--旋转
	self.childKeyPromptRotate = self:CreateComponent(self.view.WBP_ButtonRotate.WBP_KeyPrompt, P_KeyPrompt)
	self.childKeyPromptRotate:Refresh(nil,"R")
	--移除
	self.childKeyPromptRemove = self:CreateComponent(self.view.WBP_ButtonRemove.WBP_KeyPrompt, P_KeyPrompt)
	self.childKeyPromptRemove:Refresh(nil,"Space")
end

---UI事件在这里注册，此处为自动生成
function Build_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.WBP_QuitBtn.Btn_ClickArea.OnClicked, "on_WBP_QuitBtnBtn_ClickArea_Clicked")
    self:AddUIEvent(self.view.WBP_ButtonConfirm.WBP_ComBtnTransparent.OnClicked, "on_WBP_ButtonConfirmWBP_ComBtnTransparent_Clicked")
    self:AddUIEvent(self.view.WBP_ButtonCancel.WBP_ComBtnTransparent.OnClicked, "on_WBP_ButtonCancelWBP_ComBtnTransparent_Clicked")
    self:AddUIEvent(self.view.WBP_ButtonRotate.WBP_ComBtnTransparent.OnClicked, "on_WBP_ButtonRotateWBP_ComBtnTransparent_Clicked")
    self:AddUIEvent(self.view.WBP_ButtonRemove.WBP_ComBtnTransparent.OnClicked, "on_WBP_ButtonRemoveWBP_ComBtnTransparent_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Build_Panel:InitUIView()
	self.view.WBP_ManorTipsTop:SetVisibility(ESlateVisibility.Collapsed)
	self.view.WBP_ManorTipsBottom:SetVisibility(ESlateVisibility.Collapsed)
	self.view.WBP_NPCBtnSelect:SetVisibility(ESlateVisibility.Collapsed)
	self.view.WBP_ManorMoney:Event_UI_Style(false)
	self.view.WBP_ManorMoney_1:Event_UI_Style(false)
	self.view.WBP_ManorMoney_2:Event_UI_Style(true)
	self.view.WBP_ButtonConfirm:Event_UI_Style(0)
	self.view.WBP_ButtonCancel:Event_UI_Style(1)
	self.view.WBP_ButtonRemove:Event_UI_Style(2)
	self.view.WBP_ButtonRotate:Event_UI_Style(3)
end

---组件刷新统一入口
function Build_Panel:Refresh()
	self.userWidget:SetButtonState(Enum.EBuildUIButtonState.Normal)
	self:SetListVisibility(true)
	
	Game.ManorSystem.ManorBuildModule:EnterBuild()
	self.WBP_BuildListCom:Refresh()
	self:HideDetailInfo()
	UIManager:GetInstance():HideAllPanel(UIConst.HIDE_PANELS_SOURCE_TYPE.MANOR, {
		self.uid,
		"ScreenInput_Panel",
		"P_FakeWorldWidgetPanel",
		"WorldWidgetPanel",
		UIPanelConfig.ReminderMain_Panel,
	})
	self:UpdateMoneyNum()
	self:OnUpdateAestheticsValue()
	self:OnFurnitureUpdate()
end

function Build_Panel:OnClose()
	UIManager:GetInstance():RestoreAllPanel(UIConst.HIDE_PANELS_SOURCE_TYPE.MANOR)

	Game.ManorSystem.ManorBuildModule:LeaveBuild()
	if Game.NewUIManager:IsOpened(UIPanelConfig.BuildDetailInfo_Panel) then
		Game.NewUIManager:ClosePanel(UIPanelConfig.BuildDetailInfo_Panel)
	end
end

---更新UI中家园代币数量
function Build_Panel:UpdateMoneyNum()
	local moneyNum = Game.BagSystem:GetItemCount(HomeConst.HOME_MONEY_TYPE) or 0
	self.view.WBP_ManorMoney_2.TextBlock:SetText(self:FormatNumber(moneyNum))
end

---更新UI中的美观度
function Build_Panel:OnUpdateAestheticsValue()
	self.view.WBP_ManorMoney.TextBlock:SetText(self:FormatNumber(Game.ManorSystem.ManorInfoModule:GetManorPutScore()))
end

---将数字格式化为每三位加一个逗号的形式s's
---@param num number 输入的数字
---@return string 格式化后的字符串
function Build_Panel:FormatNumber(num)
	--例如输入为1000，输出为1,000
	--例如输入为3700000，输出为3,700,000
	local formatted = tostring(num):reverse():gsub("(%d%d%d)", "%1,"):reverse()
	-- 如果以逗号结尾，去掉多余的逗号
	if formatted:sub(1, 1) == "," then
		formatted = formatted:sub(2)
	end
	return formatted
end

function Build_Panel:ShowDetailInfo(data)
	if self.curSelectItemData == data then
		self:HideDetailInfo()
		return
	end

	self.curSelectItemData = data
	Game.NewUIManager:OpenPanel(UIPanelConfig.BuildDetailInfo_Panel, data)
	Game.ManorSystem.ManorBuildModule:SetSelectNodeOpen(false, true)
end

function Build_Panel:HideDetailInfo()
	self.curSelectItemData = nil
	Game.NewUIManager:ClosePanel(UIPanelConfig.BuildDetailInfo_Panel)
	Game.ManorSystem.ManorBuildModule:SetSelectNodeOpen(true)
end

---退出
function Build_Panel:on_WBP_QuitBtnBtn_ClickArea_Clicked()
	self:CloseSelf()
end

---确认
function Build_Panel:on_WBP_ButtonConfirmWBP_ComBtnTransparent_Clicked()
	--Game.ManorSystem.ManorBuildModule:ConfirmEditNode()
end

---取消
function Build_Panel:on_WBP_ButtonCancelWBP_ComBtnTransparent_Clicked()
	--Game.ManorSystem.ManorBuildModule:CancelEditNode()
end

---旋转
function Build_Panel:on_WBP_ButtonRotateWBP_ComBtnTransparent_Clicked()
end

---回收
function Build_Panel:on_WBP_ButtonRemoveWBP_ComBtnTransparent_Clicked()
end

---@public
---设置建造UI按钮状态
---@param state Enum.EBuildUIButtonState
function Build_Panel:SetButtonState(state)
	self.userWidget:SetButtonState(state)
end

---@public
---设置左侧Tab列表是否可见
---@param state boolean 列表是否可见
function Build_Panel:SetListVisibility(state)
	self.isListVisible = state
	self.userWidget:SetClose(not state)
end

function Build_Panel:OnEditNodeChange(start)
	if start then
		self:OnStartEditNode()
	else
		self:OnCancelEditNode()
	end
end

---开始摆放
function Build_Panel:OnStartEditNode()
	self:SetButtonState(Enum.EBuildUIButtonState.Edit)
	self:HideDetailInfo()
end

--结束摆放
function Build_Panel:OnCancelEditNode()
	self:SetButtonState(Enum.EBuildUIButtonState.Normal)
	self:OnUpdateCantBuild()
end

---更新摆放的规则
function Build_Panel:OnUpdateCantBuild(cantBuildDesc, cantBuildType)
	if string.isEmpty(cantBuildDesc) or cantBuildType == Enum.BuildCantPlaceType.Normal_Count then
		self.view.WBP_ManorTipsBottom:SetVisibility(ESlateVisibility.Collapsed)
		self.view.WBP_ManorTipsBottom.Text_Tips:SetText("")
	else
		self.view.WBP_ManorTipsBottom:SetVisibility(ESlateVisibility.Visible)
		self.view.WBP_ManorTipsBottom.Text_Tips:SetText(cantBuildDesc)
	end
end

---更新选中的node
function Build_Panel:OnUpdateSelectNode(node)
	if node then
		self:SetButtonState(Enum.EBuildUIButtonState.Delete)
		self.view.WBP_ManorTipsTop:SetVisibility(ESlateVisibility.Visible)
		self.view.WBP_ManorTipsTop.Text_Tips:SetText(node:GetName())
	else
		self:SetButtonState(Enum.EBuildUIButtonState.Normal)
		self.view.WBP_ManorTipsTop:SetVisibility(ESlateVisibility.Collapsed)
		self.view.WBP_ManorTipsTop.Text_Tips:SetText("")
	end

	--判断是否可以删除
	self:OnUpdateCantDelete(node)
end

---更新删除的规则
function Build_Panel:OnUpdateCantDelete(node)
	if node then
		local cantDeleteDesc = node:GetCanDeleteDesc()
		if not string.isEmpty(cantDeleteDesc) then
			self.view.WBP_ManorTipsBottom:SetVisibility(ESlateVisibility.Visible)
			self.view.WBP_ManorTipsBottom.Text_Tips:SetText(cantDeleteDesc)
			return
		end
	end

	self.view.WBP_ManorTipsBottom:SetVisibility(ESlateVisibility.Collapsed)
	self.view.WBP_ManorTipsBottom.Text_Tips:SetText("")
end

--- 此处为自动生成
function Build_Panel:on_Btn_ClickArea_Clicked()
	self:SetListVisibility(not self.isListVisible)
	self:HideDetailInfo()
end

function Build_Panel:OnFurnitureUnlock()
	self:HideDetailInfo()
	self:UpdateMoneyNum()
end

function Build_Panel:OnStoreFurnitureUpdate(presetIDList)
	self:UpdateMoneyNum()
end

function Build_Panel:OnFurnitureUpdate()
	self:OnUpdateAestheticsValue()
	self:UpdateMoneyNum()
	--占地面积
	self.view.WBP_NumericPrompt.TB_HomelandAreaSize:SetText(StringConst.Get("MANOR_AREASIZE", Game.me.HomelandAreaSize))
	--建筑数量
	self.view.WBP_NumericPrompt.TB_HomelandBuildingCnt:SetText(StringConst.Get("MANOR_BUILDING_CNT", Game.me.HomelandBuildingCnt))
	--海拔高度
	self.view.WBP_NumericPrompt.TB_HomelandHeight:SetText(StringConst.Get("MANOR_BUILDING_HEIGHT", Game.me.HomelandHeight))	
end

return Build_Panel
