local UIComAccordionList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComAccordionList")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class BuildList : UIComponent
---@field view BuildListBlueprint
local BuildList = DefineClass("BuildList", UIComponent)

---item解锁状态枚举
BuildList.ItemStatusEnum = {
	["Unlocked"] = 1,	-- 已解锁
	["Unlockable"] = 2,	-- 待解锁
	["Locked"] = 3,		-- 未解锁
}

BuildList.eventBindMap = {
	[EEventTypesV2.ON_MANOR_STORE_ITEM_UPDATE] = "OnRefreshStoreFurniture",
	[EEventTypesV2.ON_WORKSHOP_LIST_UPDATE] = "OnRefreshWorkshop",
	[EEventTypesV2.ON_FURNITURE_UNLOCK] = "OnUnlockFurniture",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BuildList:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BuildList:InitUIData()
	---mainList
	self.manorMainTabList = {}
	---subList
	self.manorSubTabList = {}

	---当前选中的一级菜单索引
	self.curMainIndex = nil
	---当前选中的二级菜单索引
	self.curSubIndex = nil
	self.curTypeID = nil
	self.curGroupId = nil

	---item是否显示main类型所有的
	self.itemListShowMain = false
	---item是否收起
	self.itemListPackUp = false

	self.curSelectItemData = nil
	self.menuDataLis = nil
end

--- UI组件初始化，此处为自动生成
function BuildList:InitUIComponent()
    ---@type UIComAccordionList
    self.WBP_ComFoldType1TabListCom = self:CreateComponent(self.view.WBP_ComFoldType1TabList, UIComAccordionList)
    ---@type UIListView
    self.WBP_ItemListCom = self:CreateComponent(self.view.WBP_ItemList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function BuildList:InitUIEvent()
	self:AddUIEvent(self.WBP_ComFoldType1TabListCom.onItemExpansionChanged, "on_WBP_ComFoldType1TabListCom_ItemExpansionChanged")
	self:AddUIEvent(self.WBP_ComFoldType1TabListCom.onItemSelected, "on_WBP_ComFoldType1TabListCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BuildList:InitUIView()
	self.WBP_ComFoldType1TabListCom:SetAutoSelectFirst(true)
end

---组件刷新统一入口
function BuildList:Refresh()
	self:InitMenuList()
end

---@private
---@param index number
---@param data table
---@param expanded boolean
---左侧Tab一级菜单展开和收起事件
function BuildList:on_WBP_ComFoldType1TabListCom_ItemExpansionChanged(index, data, expanded)
	if expanded then
		self:OnClickTreeTab(data.tabData.otherInfo.ParentIndex)
	else
		self:OnCancelClickTreeTab()
	end
end

---@private
---@param index number
---@param data table
---左侧Tab二级菜单被选中事件
function BuildList:on_WBP_ComFoldType1TabListCom_ItemSelected(index, data)
	self:OnClickSecondTab(data.tabData.otherInfo.ParentIndex, data.tabData.otherInfo.ChildIndex)
end

function BuildList:InitMenuList()
	local treeData = self:GetMenuDataList()
	self.WBP_ComFoldType1TabListCom:Refresh(treeData)
	self.WBP_ComFoldType1TabListCom:SetExpansionItemByPath(true, 1)
	self.WBP_ComFoldType1TabListCom:SetSelectedItemByPath(true, 1, 1)
	self:OnClickTreeTab(1)
end

function BuildList:OnCancelClickTreeTab()
	self.itemListShowMain = true
	self.WBP_ItemListCom:Refresh(self.manorMainTabList[self.curTypeID])
	Game.ManorSystem.ManorBuildModule:EnterNoneState()
	self:GetParent():HideDetailInfo()
end

function BuildList:OnClickTreeTab(indexMain)
	if not self.menuDataList[indexMain] then
		return
	end

	self.itemListShowMain = false
	self.curMainIndex = indexMain

	self:RefreshStoreFurnitureList()
	self:OnClickSecondTab(indexMain, 1)
end

function BuildList:OnClickSecondTab(indexMain, indexSub)
	local childData = self.menuDataList[indexMain].Children[indexSub]
	if not childData then
		return
	end

	self.curSubIndex = indexSub
	self.curTypeID = childData.Data.Type
	self.curGroupId = childData.Data.ID

	self:GetParent():SetListVisibility(true)
	self:RefreshStoreFurnitureList()
	
	Game.ManorSystem.ManorBuildModule:EnterNoneState()
	self:GetParent():HideDetailInfo()
end

---刷新物品数量
function BuildList:OnRefreshStoreFurniture(presetIDList)
	self:RefreshStoreFurnitureList()
end

function BuildList:OnRefreshWorkshop()
	self:RefreshStoreFurnitureList(true)
end

function BuildList:RefreshStoreFurnitureList(bForceUpdate)
	if not self.curTypeID or not self.curGroupId then
		self.WBP_ItemListCom:Refresh()
		return
	end

	self:RefreshManorItemListByType(self.curTypeID, bForceUpdate)
	if self.itemListShowMain then
		self.WBP_ItemListCom:Refresh(self.manorMainTabList[self.curTypeID])
	else
		self.WBP_ItemListCom:Refresh(self.manorSubTabList[self.curTypeID][self.curGroupId])
	end
end

---解锁组件
function BuildList:OnUnlockFurniture(presetID)
	self:UpdateMainItemUnlockStatus(presetID)
	self:UpdateSubItemUnlockStatus(presetID)
	self:RefreshStoreFurnitureList()
end

---更新MainItem中该物品的解锁状态
function BuildList:UpdateMainItemUnlockStatus(presetID)
	for _, dataList in pairs(self.manorMainTabList) do
		for _, data in pairs(dataList) do
			if data.ID == presetID then
				data.Status = BuildList.ItemStatusEnum.Unlocked
				return
			end
		end
	end
end

---更新SubItem中该物品的解锁状态
function BuildList:UpdateSubItemUnlockStatus(presetID)
	for _, mainDataList in pairs(self.manorSubTabList) do
		for _, subDataList in pairs(mainDataList) do
			for _, data in pairs(subDataList) do
				if data.ID == presetID then
					data.Status = BuildList.ItemStatusEnum.Unlocked
					return
				end
			end
		end
	end
end

function BuildList:GetMenuDataList()
	local resultList = {}
	local menusDataList = UIComAccordionList.NewTreeViewData()

	local tempTable = Game.TableData.GetManorItemMainTypeDataTable()
	for mainId, mainType in ksbcpairs(tempTable) do
		local mainTab = {}
		mainTab.ID = mainType.ID
		mainTab.name = mainType.TypeName
		mainTab.Children = {}
		mainTab.Data = mainType
		mainTab.MainType = mainType
		self:GetSubMenu(mainId, mainTab.Children)
		resultList[#resultList + 1] = mainTab
	end

	table.sort(resultList, function(a, b)
		return a.ID < b.ID
	end)

	for parentIdx, data in ipairs(resultList) do
		local tabData = UIComAccordionList.NewTabData(data.name)
		tabData.otherInfo = {
			ParentIndex = parentIdx
		}
		menusDataList:AddFirstNode(tabData)
		for childIdx, child in ipairs(data.Children) do
			local subTabData = UIComAccordionList.NewTabData(child.name)
			subTabData.otherInfo = {
				ParentIndex = parentIdx,
				ChildIndex = childIdx
			}
			menusDataList:AddTwoNode(parentIdx, subTabData)
		end
	end

	self.menuDataList = resultList
	return menusDataList
end

function BuildList:GetSubMenu(mainId, children)
	local tempTable = Game.TableData.GetManorItemSubTypeDataTable()
	for _, subType in ksbcpairs(tempTable) do
		if subType.Type == mainId and subType.CanShow then
			local subTab = {}
			subTab.ID = subType.ID
			subTab.name = subType.GroupName
			subTab.Data = subType
			subTab.SubType = subType
			children[#children + 1] = subTab
		end
	end

	table.sort(children, function(a, b)
		return a.ID < b.ID
	end)
end

function BuildList:RefreshManorItemListByType(typeID, bForceUpdate)
	if not self.manorSubTabList then
		self.manorSubTabList = {}
	end

	if self.manorSubTabList[typeID] and not bForceUpdate then
		return
	end

	local subTabList = {}
	local mainTabList = {}
	local tempTable = Game.TableData.GetManorItemDataTable()
	for _, itemConfig in ksbcpairs(tempTable) do
		local subTypeConfig = Game.TableData.GetManorItemSubTypeDataRow(itemConfig.GroupId)
		-- 工坊只显示最高等级
		local isWorkshop = itemConfig.TypeId == Enum.BuildItemType.Workshop
		local canShow
		if isWorkshop then
			canShow = Game.ManorSystem.ManorWorkshopModule:CheckCanWorkshopShowOnUI(itemConfig.ID)
		else
			canShow = itemConfig.CanShow and subTypeConfig and subTypeConfig.CanShow
		end

		if itemConfig.TypeId == typeID and canShow then
			if not subTabList[itemConfig.GroupId] then
				subTabList[itemConfig.GroupId] = {}
			end
			local curGroupNum = Game.ManorSystem.ManorBuildStoreModule:GetFurnitureTypeCountInStore(itemConfig.GroupId)
			local maxGroupNum = Game.TableData.GetManorItemSubTypeDataRow(itemConfig.GroupId).holdMax
			-- 工坊默认解锁
			local status = isWorkshop and BuildList.ItemStatusEnum.Unlocked or self:GetItemStatusEnum(itemConfig.ID)
			local itemData = {
				CurGroupNum = curGroupNum,
				MaxGroupNum = maxGroupNum,
				ID = itemConfig.ID,
				GroupID = itemConfig.GroupId,
				Status = status,
			}
			table.insert(subTabList[itemConfig.GroupId], itemData)
			table.insert(mainTabList, itemData)
		end
	end

	-- 排序逻辑为：已解锁的在前面，待解锁的在中间，未解锁的在后面
	-- 状态相同的按照ID排序
	for _, dataList in pairs(subTabList) do
		table.sort(dataList, function(a, b)
			if a.Status ~= b.Status then
				return a.Status < b.Status
			end
			return a.ID < b.ID
		end)
	end
	self.manorSubTabList[typeID] = subTabList

	table.sort(mainTabList, function(a, b)
		if a.Status ~= b.Status then
			return a.Status < b.Status
		elseif a.GroupID ~= b.GroupID then
			return a.GroupID < b.GroupID
		end
		return a.ID < b.ID
	end)
	self.manorMainTabList[typeID] = mainTabList
end

---@private
---@param id number 组件id
---@return BuildList.ItemStatusEnum
---得到组件状态枚举
function BuildList:GetItemStatusEnum(id)
	local config = Game.TableData.GetManorItemDataRow(id)
	if Game.ManorSystem.ManorBuildStoreModule:IsFurnitureUnlocked(id) then
		return BuildList.ItemStatusEnum.Unlocked
	elseif Game.me.HomelandLevel >= config.UnlockLevel then
		return BuildList.ItemStatusEnum.Unlockable
	end
	return BuildList.ItemStatusEnum.Locked
end

---@private
---@param id number 组件id
---@return boolean
---判断组件是否待解锁
function BuildList:IsItemUnlockable(id)
	local config = Game.TableData.GetManorItemDataRow(id)
	return Game.me.HomelandLevel >= config.UnlockLevel
end

function BuildList:RefreshManorItemMainList()
end

return BuildList
