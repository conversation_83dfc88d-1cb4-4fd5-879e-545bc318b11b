local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class WorkShop_ChooseStuff_Panel : UIPanel
---@field view WorkShop_ChooseStuff_PanelBlueprint
local WorkShop_ChooseStuff_Panel = DefineClass("WorkShop_ChooseStuff_Panel", UIPanel)

--列表排序方式
WorkShop_ChooseStuff_Panel.SortMode = {
	Quality = 1,
	Productivity = 2,
}

WorkShop_ChooseStuff_Panel.eventBindMap = {
	[EEventTypesV2.ON_WORKSHOP_EMPLOYEE_UPDATE] = "RefreshList",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function WorkShop_ChooseStuff_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function WorkShop_ChooseStuff_Panel:InitUIData()
	---@type number
	self.currentBuildingId = nil
	---@type number
	self.currentSortMode = 1
end

--- UI组件初始化，此处为自动生成
function WorkShop_ChooseStuff_Panel:InitUIComponent()
    ---@type UIListView childScript: WorkShop_ChooseStuff_Item
    self.EmployeeListCom = self:CreateComponent(self.view.EmployeeList, UIListView)
    ---@type UIComDropDown
    self.SelectDropCom = self:CreateComponent(self.view.WBP_ComSelectDropType1, UIComDropDown)
end

---UI事件在这里注册，此处为自动生成
function WorkShop_ChooseStuff_Panel:InitUIEvent()
    self:AddUIEvent(self.EmployeeListCom.onItemSelectionChanged, "on_EmployeeListCom_ItemSelectionChanged")
    self:AddUIEvent(self.SelectDropCom.onItemSelected, "on_SelectDropCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function WorkShop_ChooseStuff_Panel:InitUIView()
end

---面板打开的时候触发
function WorkShop_ChooseStuff_Panel:OnRefresh(buildingId)
	self.currentBuildingId = buildingId
	self.SelectDropCom:Refresh({
		[1] = {
			name = "按稀有度排序",
			sortMode = WorkShop_ChooseStuff_Panel.SortMode.Quality,
		},
		[2] = {
			name = "按生产力排序",
			sortMode = WorkShop_ChooseStuff_Panel.SortMode.Productivity,
		}
	}, 1, false)
	self.currentSortMode = WorkShop_ChooseStuff_Panel.SortMode.Quality
	Game.ManorSystem.ManorWorkshopModule:ReqWorkshopEmployeeList()
end

function WorkShop_ChooseStuff_Panel:RefreshList()
	local sortedList = {}
	local index = 1
	for _, employee in pairs(Game.ManorSystem.ManorWorkshopModule:GetEmployeeList()) do
		sortedList[index] = employee
		index = index + 1
	end

	table.sort(sortedList, function(a, b)
		local aConfig = Game.TableData.GetWorkshopEmployeeDataRow(a.employeeID)
		local bConfig = Game.TableData.GetWorkshopEmployeeDataRow(b.employeeID)
		if self.currentSortMode == WorkShop_ChooseStuff_Panel.SortMode.Productivity then
			return aConfig.Productivity > bConfig.Productivity
		end
		return aConfig.Quality > bConfig.Quality
	end)

	self.EmployeeListCom:Refresh(sortedList)
end

--- 此处为自动生成
---@param index number
---@param selected bool
function WorkShop_ChooseStuff_Panel:on_EmployeeListCom_ItemSelectionChanged(index, selected)
	---@type WorkShop_ChooseStuff_Item
	local item = self.EmployeeListCom:GetItemByIndex(index)
	local itemBuildingId = item.data.buildingID
	if itemBuildingId == self.currentBuildingId then
		Game.ManorSystem.ManorWorkshopModule:ReqRemoveWorkshopEmployee(item.data.id)
	elseif itemBuildingId > 0 then
		-- todo: 工坊，二次弹窗
		Game.ManorSystem.ManorWorkshopModule:ReqAssignWorkshop(item.data.id, self.currentBuildingId)
	else
		Game.ManorSystem.ManorWorkshopModule:ReqAssignWorkshop(item.data.id, self.currentBuildingId)	
	end
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function WorkShop_ChooseStuff_Panel:on_SelectDropCom_ItemSelected(index, data)
	self.currentSortMode = data.sortMode
	self:RefreshList()
end

return WorkShop_ChooseStuff_Panel
