local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class WorkshopUp_Item : UIListItem
---@field view WorkshopUp_ItemBlueprint
local WorkshopUp_Item = DefineClass("WorkshopUp_Item", UIListItem)

WorkshopUp_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function WorkshopUp_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function WorkshopUp_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function WorkshopUp_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function WorkshopUp_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function WorkshopUp_Item:InitUIView()
end

---面板打开的时候触发
function WorkshopUp_Item:OnRefresh()
	self.view.Text_Content:SetText(self.data.content)
end

return WorkshopUp_Item
