local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local HomeConst = kg_require("Shared.Const.HomeConst")
local ManorWorkshopUtils = kg_require("Gameplay.LogicSystem.Manor.Modules.Workshop.ManorWorkshopUtils")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class WorkShop_ChooseFormula_Panel : UIPanel
---@field view WorkShop_ChooseFormula_PanelBlueprint
local WorkShop_ChooseFormula_Panel = DefineClass("WorkShop_ChooseFormula_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")

---@class RecipeUIData
---@field recipeId number
---@field locked boolean

WorkShop_ChooseFormula_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function WorkShop_ChooseFormula_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function WorkShop_ChooseFormula_Panel:InitUIData()
	---@type number
	self.buildingId = nil
	---@type number 当前选择的配方ID
	self.currentRecipeId = nil

	---@type number
	self.num = 0
	---@type number
	self.maxNum = 0
	---@type number
	self.singleCost = 0
	---@type number
	self.singleTime = 0
	
	---@type boolean
	self.bEnoughMoney = false
end

--- UI组件初始化，此处为自动生成
function WorkShop_ChooseFormula_Panel:InitUIComponent()
    ---@type UIListView childScript: ManorItemSmall
    self.List_ProduceCom = self:CreateComponent(self.view.List_Produce, UIListView)
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
    ---@type UIListView childScript: WorkShop_ChooseFormula_Item
    self.List_FormulaCom = self:CreateComponent(self.view.List_Formula, UIListView)
end

---UI事件在这里注册，此处为自动生成
function WorkShop_ChooseFormula_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.List_FormulaCom.onItemSelected, "on_List_FormulaCom_ItemSelected")
    self:AddUIEvent(self.view.Btn_Add.OnClicked, "on_Btn_Add_Clicked")
    self:AddUIEvent(self.view.Btn_Delete.OnClicked, "on_Btn_Delete_Clicked")
    self:AddUIEvent(self.view.Btn_MaxValue.OnClicked, "on_Btn_MaxValue_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function WorkShop_ChooseFormula_Panel:InitUIView()
	self.WBP_ComBtnCom:Refresh("选择")
end

---面板打开的时候触发
function WorkShop_ChooseFormula_Panel:OnRefresh(buildingId)
	self.buildingId = buildingId
	self:RefreshUI()
end

function WorkShop_ChooseFormula_Panel:RefreshUI()
	self:RefreshFormulaData()
end

function WorkShop_ChooseFormula_Panel:RefreshFormulaData()
	local ManorWorkshopModule = Game.ManorSystem.ManorWorkshopModule
	local status = ManorWorkshopModule:GetWorkshopStatus(self.buildingId)
	---@type RecipeUIData[]
	local allRecipeData = Game.ManorSystem.ManorWorkshopModule:GetAllRecipeDataByWorkshopType(status.furnitureID)
	self.List_FormulaCom:Refresh(allRecipeData)
	self.List_FormulaCom:SetSelectedItemByIndex(1, true)
end

---@param data RecipeUIData
function WorkShop_ChooseFormula_Panel:RefreshProductData(data)
	self.currentRecipeId = data.recipeId
	local recipeConfig = Game.TableData.GetWorkshopItemProduceDataRow(data.recipeId)
	if data.locked then
		self.userWidget:BP_SetEmpty(true)
		self.view.Text_Empty:SetText(recipeConfig.LockedText)
	else
		self.userWidget:BP_SetEmpty(false)
		local productData = {}
		local index = 1
		for _, item in ksbcpairs(recipeConfig.Product) do
			productData[index] = {
				id = item[1],
				num = item[2],
			}
			index = index + 1
		end
		if recipeConfig.RareProduct then
			for _, item in ksbcpairs(recipeConfig.RareProduct) do
				productData[index] = {
					id = item[1],
					num = item[2],
				}
				index = index + 1
			end
		end
		self.List_ProduceCom:Refresh(productData)
		self.maxNum = recipeConfig.MaxLimit
		self.singleCost = recipeConfig.Cost
		self.num = 0
		self.singleTime = recipeConfig.ProduceTime
		self:RefreshNumAndCost()
	end
end

--- 此处为自动生成
---@param index number
---@param data RecipeUIData
function WorkShop_ChooseFormula_Panel:on_List_FormulaCom_ItemSelected(index, data)
	self:RefreshProductData(data)
end


--- 此处为自动生成
function WorkShop_ChooseFormula_Panel:on_WBP_ComBtnCom_ClickEvent()
	if self.bEnoughMoney then
		Game.ManorSystem.ManorWorkshopModule:ReqWorkshopAddProductionQueue(self.buildingId, self.currentRecipeId, self.num)
		self:CloseSelf()
	end
end

--- 此处为自动生成
function WorkShop_ChooseFormula_Panel:on_Btn_Add_Clicked()
	self.num = self.num + 1
	self:RefreshNumAndCost()
end

--- 此处为自动生成
function WorkShop_ChooseFormula_Panel:on_Btn_Delete_Clicked()
	if self.num > 0 then
		self.num = self.num - 1
		self:RefreshNumAndCost()
	end
end

--- 此处为自动生成
function WorkShop_ChooseFormula_Panel:on_Btn_MaxValue_Clicked()
	local curMoney = Game.BagSystem:GetItemCount(HomeConst.HOME_MONEY_TYPE) or 0
	self.num = math.min(math.floor(curMoney / self.singleCost), self.maxNum)
	self:RefreshNumAndCost(curMoney)
end

function WorkShop_ChooseFormula_Panel:RefreshNumAndCost(curMoney)
	curMoney = curMoney or Game.BagSystem:GetItemCount(HomeConst.HOME_MONEY_TYPE)
	local totalCost = self.num * self.singleCost
	local totalTime = self.num * self.singleTime * 1000
	self.view.Text_Cost:SetText(tostring(totalCost))
	self.view.Text_Num:SetText(tostring(self.num))
	self.bEnoughMoney = totalCost <= curMoney
	self.userWidget:BP_SetEnough(self.bEnoughMoney)
	self.view.Text_TimeTitle:SetText(ManorWorkshopUtils.GetCountDownTimeFormatString(totalTime))

	if self.bEnoughMoney and self.num > 0 and self.num <= self.maxNum then
		self.WBP_ComBtnCom:SetDisable(false)
		self.WBP_ComBtnCom.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
	else
		self.WBP_ComBtnCom:SetDisable(true)		
		self.WBP_ComBtnCom.view.Btn_ClickArea:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end

return WorkShop_ChooseFormula_Panel
