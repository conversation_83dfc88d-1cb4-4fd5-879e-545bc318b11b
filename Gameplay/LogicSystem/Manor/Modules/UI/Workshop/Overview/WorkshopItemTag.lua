local ManorWorkshopUtils = kg_require("Gameplay.LogicSystem.Manor.Modules.Workshop.ManorWorkshopUtils")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class WorkshopItemTag : UIComponent
---@field view WorkshopItemTagBlueprint
local WorkshopItemTag = DefineClass("WorkshopItemTag", UIComponent)

WorkshopItemTag.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function WorkshopItemTag:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function WorkshopItemTag:InitUIData()
end

--- UI组件初始化，此处为自动生成
function WorkshopItemTag:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function WorkshopItemTag:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function WorkshopItemTag:InitUIView()
end

---组件刷新统一入口
function WorkshopItemTag:Refresh(state, leftTime)
	self.userWidget:BP_SetState(state)
	self.view.Text_Time:SetText(ManorWorkshopUtils.GetCountDownTimeFormatString(leftTime))
end

return WorkshopItemTag
