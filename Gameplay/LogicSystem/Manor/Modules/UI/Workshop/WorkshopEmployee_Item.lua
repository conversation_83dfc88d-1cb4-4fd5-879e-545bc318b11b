---@class WorkshopEmployeeItemData
---@field UId number
---@field Locked boolean

local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class WorkshopEmployee_Item : UIListItem
---@field view WorkshopEmployee_ItemBlueprint
---@field data WorkshopEmployeeItemData
local WorkshopEmployee_Item = DefineClass("WorkshopEmployee_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

--- 雇员状态
WorkshopEmployee_Item.State = {
	Locked = 0, 		-- 未解锁雇员位
	Empty = 1,			-- 已解锁，但雇员位为空
	Occupied = 2,		-- 已解锁，且有雇员
}

WorkshopEmployee_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function WorkshopEmployee_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function WorkshopEmployee_Item:InitUIData()
	---@type number
	self.state = nil
	---@type number
	self.employeeId = nil
end

--- UI组件初始化，此处为自动生成
function WorkshopEmployee_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function WorkshopEmployee_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnLongPressed, "on_Btn_ClickArea_LongPressed")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function WorkshopEmployee_Item:InitUIView()
end

function WorkshopEmployee_Item:OnRefresh()
	local employeeData = Game.ManorSystem.ManorWorkshopModule:GetEmployeeByUId(self.data.UId)
	if employeeData then
		self.employeeId = employeeData.employeeID
		local employeeConfig = Game.TableData.GetWorkshopEmployeeDataRow(self.employeeId)
		if employeeConfig then
			-- 超过4个字的名字显示前3个字+...
			local name = employeeConfig.Name
			if utf8.len(name) > 4 then
				name = utf8.sub(name, 1, 4) .. "..."
			end
			self.view.Text_Name:SetText(name)
			if employeeConfig.Icon then
				self:SetImage(self.view.Img_HeadIcon, employeeConfig.Icon)
			end
		end
	end

	if self.data.Locked then
		-- 未解锁雇员位
		self.state = WorkshopEmployee_Item.State.Locked
		self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	elseif self.data.UId then
		-- 已解锁，且有雇员
		self.state = WorkshopEmployee_Item.State.Occupied
		self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
	else
		-- 已解锁，但雇员位为空
		self.state = WorkshopEmployee_Item.State.Empty
		self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
	end
	self.userWidget:BP_SetState(self.state)
end

--- 此处为自动生成
function WorkshopEmployee_Item:on_Btn_ClickArea_LongPressed()
	if self.employeeId then
		Game.NewUIManager:OpenPanel(UIPanelConfig.WorkShop_Stuff_Tips, self.employeeId, self.userWidget:GetCachedGeometry())
	end
end

return WorkshopEmployee_Item
