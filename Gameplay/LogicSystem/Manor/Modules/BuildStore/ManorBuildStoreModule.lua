local HomeConst = kg_require("Shared.Const.HomeConst")
---@class ManorBuildStoreModule
local ManorBuildStoreModule = DefineClass("ManorBuildStoreModule")

---@private
function ManorBuildStoreModule:Init()
	---@type ManorBuildStoreModel
	self.model = kg_require("Gameplay.LogicSystem.Manor.Modules.BuildStore.ManorBuildStoreModel").new(true, true)
end

---@private
function ManorBuildStoreModule:UnInit()
end

---同步仓库信息
function ManorBuildStoreModule:OnMsgHomeFurnitureSync(StoreFurnitureDict)
	local storeFurnitureDict = self.model.StoreFurnitureDict
	local needRefreshFurnitureIds = {}
	local index = 1
	for id, value in pairs(StoreFurnitureDict) do
		if storeFurnitureDict[id] ~= value then
			storeFurnitureDict[id] = value
			needRefreshFurnitureIds[index] = id
			index = index + 1
		end
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_STORE_ITEM_UPDATE, needRefreshFurnitureIds)
end

---@public
---获取仓库内某个家具剩余数量/总数量
function ManorBuildStoreModule:GetStoreFurnitureNumByID(presetID)
	if not self.model.StoreFurnitureDict then
		return 0, 0
	end

	return self.model:GetStoreFurnitureNumByID(presetID)
end

---@public
---获取仓库内某一类家具的数量
---@param groupID number 家具类型
---@return number 该类家具总数量
function ManorBuildStoreModule:GetFurnitureTypeCountInStore(groupID)
	if not self.model.StoreFurnitureDict then
		return 0
	end
	
	return self.model:GetFurnitureTypeCountInStore(groupID)
end

---@public
---获取仓库内所有家具
function ManorBuildStoreModule:GetAllStoreFurniture()
	return self.model.StoreFurnitureDict
end

---@public
---请求解锁家具
---@param presetID number 家具ID
function ManorBuildStoreModule:ReqUnlockFurniture(presetID)
	self.sender:ReqUnlockFurniture(presetID)
end

---@public
---判断家具是否已解锁
---@param presetID number 家具ID
---@return boolean
function ManorBuildStoreModule:IsFurnitureUnlocked(presetID)
	return self.model.StoreFurnitureDict[presetID] ~= nil
end

---@public
---解锁家具回复
---@param result table
---@param buildingID number 家具ID
function ManorBuildStoreModule:RetUnlockFurniture(Result, FurnitureID)
	if Result ~= HomeConst.HOME_ERRCODE.SUCC then
		Log.ErrorFormat("ReqUnlockFurniture failed Code=%s", Result)
		return
	end

	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FURNITURE_UNLOCK, FurnitureID)
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_FORNITURE_UNLOCK_SUCCESSFUL)
end

---@public
---请求购买家具
---@param presetID number 家具ID
---@param count number 数量
function ManorBuildStoreModule:ReqBuyFurniture(presetID, count)
	self.sender:ReqBuyFurniture(presetID, count)
end

---@public
---购买家具回复
---@param Result table
---@param FurnitureID number 家具ID
---@param Count number 数量
function ManorBuildStoreModule:RetBuyFurniture(Result, FurnitureID, Count)
	if Result ~= HomeConst.HOME_ERRCODE.SUCC and Result ~= HomeConst.HOME_ERRCODE.HOME_MONEY_NOT_ENOUGH then
		Log.ErrorFormat("ReqBuyFurniture failed Code=%s", Result)
		return
	end
	
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_STORE_ITEM_UPDATE, {FurnitureID})
	local money = Game.TableData.GetManorItemDataRow(FurnitureID).BuyPrice * Count
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_FORNITURE_BUY_SUCCESSFUL, {{money}})
end

---@public
---请求出售家具
---@param presetID number 家具ID
---@param count number 数量
function ManorBuildStoreModule:ReqSellFurniture(presetID, count)
	self.sender:ReqSellFurniture(presetID, count)
end

---@public
---出售家具回复
---@param Result table
---@param FurnitureID number 家具ID
---@param Count number 数量
function ManorBuildStoreModule:RetSellFurniture(Result, FurnitureID, Count)
	if Result ~= HomeConst.HOME_ERRCODE.SUCC then
		Log.ErrorFormat("ReqSellFurniture failed Code=%s", Result)
		return
	end
	
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_STORE_ITEM_UPDATE, {FurnitureID})
	local money = Game.TableData.GetManorItemDataRow(FurnitureID).SellPrice * Count
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_FORNITURE_SELL_SUCCESSFUL, {{money}})
end

return ManorBuildStoreModule