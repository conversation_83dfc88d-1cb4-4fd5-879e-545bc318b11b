---@class ManorBuildStoreModel:SystemModelBase
local ManorBuildStoreModel = DefineClass("ManorBuildStoreModel",SystemModelBase)

function ManorBuildStoreModel:init()
	---家园仓库数据
	self.StoreFurnitureDict = {}
end

function ManorBuildStoreModel:clear()
	self.StoreFurnitureDict = {}
end

---根据ID得到家具的剩余数量和总数量
---@param presetID number 家具ID
function ManorBuildStoreModel:GetStoreFurnitureNumByID(presetID)
	local data = self.StoreFurnitureDict[presetID]
	if not data then
		return 0, 0
	end

	local totalCount = Game.bit.rshift(data, 16)
	local leftCount = Game.bit.band(data, 0xFFFF)

	return leftCount, totalCount
end

---@public
---获取仓库内某一类家具的数量
---@param groupID number 家具类型（表GroupSub主键）
---@return number 该类家具总数量
function ManorBuildStoreModel:GetFurnitureTypeCountInStore(groupID)
	local count = 0
	for furnitureID, data in pairs(self.StoreFurnitureDict) do
		local furnitureData = Game.TableData.GetManorItemDataRow(furnitureID)
		if furnitureData.GroupId == groupID then
			local num = Game.bit.rshift(data, 16)
			count = count + num
		end
	end
	return count
end

return ManorBuildStoreModel