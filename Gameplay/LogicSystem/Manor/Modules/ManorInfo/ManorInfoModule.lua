local HomeConst = kg_require("Shared.Const.HomeConst")
---@class ManorInfoModule
local ManorInfoModule = DefineClass("ManorInfoModule")

---@private
function ManorInfoModule:Init()
	---@type ManorInfoModel
	self.model = kg_require("Gameplay.LogicSystem.Manor.Modules.ManorInfo.ManorInfoModel").new(false, true)

	self:RegisterRedDotInfo()
end

---@private
function ManorInfoModule:UnInit()
end

--region 得到家园信息
---@public method 家园名称
function ManorInfoModule:GetManorName()
	return Game.me.HomelandName
end

---@public method  家园简介
function ManorInfoModule:GetManorIntroduction()
	return Game.me.HomelandIntroduction
end

---@public method  家园摆放评分
function ManorInfoModule:GetManorPutScore()
	return Game.me.HomelandPutScore
end

---@public method  家园总评分
function ManorInfoModule:GetTotalScore()
	return Game.me.HomelandTotalScore
end

---@public method  家园等级
function ManorInfoModule:GetManorLevel()
	return Game.me.HomelandLevel
end

---@public method 获取建筑面积
function ManorInfoModule:GetManorAreaSize()
	return Game.me.HomelandAreaSize
end

---@public method 获取家园建筑数量
function ManorInfoModule:GetManorBuildingCnt()
	return Game.me.HomelandBuildingCnt
end

---@public method 获取家园海拔高度
function ManorInfoModule:GetManorHeight()
	return Game.me.HomelandHeight
end

---@public method 获取当前拥有的家园币
function ManorInfoModule:GetManorCurrencyNum()
	return Game.BagSystem:GetItemCount(HomeConst.HOME_MONEY_TYPE)
end

---@public method 获取家园币存储上限
function ManorInfoModule:GetManorCurrencyStorageLimit()
	local data = Game.TableData.GetManorLevelUpDataRow(self:GetManorLevel())
	return data.storageLimit
end

---@public method 获取家园币每周获取上限
function ManorInfoModule:GetManorCurrencyWeeklyLimit()
	local data = Game.TableData.GetManorLevelUpDataRow(self:GetManorLevel())
	return data.getLimit
end

---@public method 获取升级到下一个所需要的总评分
function ManorInfoModule:GetTotalScoreForNextLevel()
	local data = Game.TableData.GetManorLevelUpDataRow(self:GetManorLevel())
	return data.totalScore
end

---@public method 获取升级到下一个所需要的摆放评分
function ManorInfoModule:GetPutScoreForNextLevel()
	local data = Game.TableData.GetManorLevelUpDataRow(self:GetManorLevel())
	return data.putScore
end

---@public method 获取本周已获得的家园币数量
function ManorInfoModule:GetWeeklyAcquiredManorCurrencyNum()
	return Game.me.moneyWeekLimitInfoDict[HomeConst.HOME_MONEY_TYPE].weekPossessed
end

---@public method 获取家园等级状态
---@return Enum.EManorLevelStatus 家园等级状态
function ManorInfoModule:GetManorLevelStatus()
	local levelStatus = Enum.EManorLevelStatus.CannotUpgrade
	if self:GetManorLevel() == Game.TableData.GetManorSettingRow("HOME_MAX_LEVEL") then
		levelStatus = Enum.EManorLevelStatus.MaxLevel
	end
	if self:CanUpgradeManor() then
		levelStatus = Enum.EManorLevelStatus.CanUpgrade
	end
	return levelStatus
end

---@public method 获取所有好友的家园信息
function ManorInfoModule:GetFriendsHomeInfo()
	return self.model.friendsHomeInfo
end

---@public method 获取家园升级弹窗信息
function ManorInfoModule:GetUpgradePopupInfo()
	local data = Game.TableData.GetManorLevelUpDataRow(self:GetManorLevel())
	return data.levelUpEffectText
end
--endregion

---@public method  是否可以升级家园
function ManorInfoModule:CanUpgradeManor()
	local level = self:GetManorLevel()
	if level == Game.TableData.GetManorSettingRow("HOME_MAX_LEVEL") then
		return false
	end
	local data = Game.TableData.GetManorLevelUpDataRow(level)
	if data.roleLevelLimit > Game.me.Level then
		return false
	end
	if data.levelUpConsume > self:GetManorCurrencyNum() then
		return false
	end
	if data.totalScore > self:GetTotalScore() then
		return false
	end
	if data.putScore > self:GetManorPutScore() then
		return false
	end
	--检查工坊条件
	if data.workShopLevel then
		for _, workshopData in pairs(data.workShopLevel) do
			local workshopLevel = Game.ManorSystem.ManorWorkshopModule:GetWorkshopLevel(workshopData[1])
			if workshopData[2] > workshopLevel then
				return false
			end
		end
	end
	--todo 指定家具条件 和 交易玩法条件 的检查
	return true
end

---@public method 更新红点状态
function ManorInfoModule:UpdateRedDotStatus()
	Game.RedDotSystem:UpdateRedDot("Home")
	Game.RedDotSystem:UpdateRedDot("HomeInfoHUD")
	Game.RedDotSystem:UpdateRedDot("HomeUpgradeBtn")
end

--region RPC请求  
---@public method 请求升级家园
function ManorInfoModule:ReqHomeUpgrade()
	self.sender:ReqHomeUpgrade()
end

---@public method 请求修改家园名称
function ManorInfoModule:ReqModifyHomeName(name)
	self.sender:ReqModifyHomeName(name)
end

---@public method 请求修改家园简介
function ManorInfoModule:ReqModifyHomeIntroduction(introduction)
	self.sender:ReqModifyHomeIntroduction(introduction)
end

---@public method 请求查询目标玩家家园是否解锁
function ManorInfoModule:ReqIsHomeUnlock()
	self.sender:ReqIsHomeUnlock()
end

---@public method 请求查询所有好友的家园信息
function ManorInfoModule:ReqFriendsHomeInfo()
	self.sender:ReqFriendsHomeInfo()
end
--endregion

--region RPC回复
---@public method 升级家园回复
---@param result table
function ManorInfoModule:RetHomeUpgrade(result)
end

---@public method 修改家园名称回复
---@param result table
function ManorInfoModule:RetModifyHomeName(result)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("ReqModifyHomeName failed Code=%s", result)
		return
	end
	
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_RENAME_SUCCESS)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_NAME_MODIFY)
end

---@public method 修改家园简介回复
---@param result table
function ManorInfoModule:RetModifyHomeIntroduction(result)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("ReqModifyHomeIntroduction failed Code=%s", result)
		return
	end

	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_INTRODUCTION_MODIFY)
end

---@public method 查询目标玩家家园是否解锁回复
---@param result table
---@param id string 玩家ID
---@param isUnlocked bool 是否解锁
function ManorInfoModule:RetIsHomeUnlock(result, id, isUnlocked)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("ReqIsHomeUnlock failed Code=%s", result)
		return
	end
end

---@public method 查询所有好友的家园信息回复
---@param result table
---@param list table
function ManorInfoModule:RetFriendsHomeInfo(result, list)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("ReqFriendsHomeInfo failed Code=%s", result)
		return
	end

	self.model.friendsHomeInfo = list
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_RECEIVED_FRIENDS_INFO)
end
--endregion

---@private method 注册红点信息
function ManorInfoModule:RegisterRedDotInfo()
	Game.RedDotSystem:AddListener("Home", self, self.CanUpgradeManor)
	Game.RedDotSystem:AddListener("HomeInfoHUD", self, self.CanUpgradeManor)
	Game.RedDotSystem:AddListener("HomeUpgradeBtn", self, self.CanUpgradeManor)

	Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_LEVEL_CHANGED, self, self.UpdateRedDotStatus)
end

return ManorInfoModule