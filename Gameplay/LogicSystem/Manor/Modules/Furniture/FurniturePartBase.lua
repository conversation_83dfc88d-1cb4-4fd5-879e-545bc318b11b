---@class FurniturePartBase
local FurniturePartBase = DefineClass("FurniturePartBase")

function FurniturePartBase:ctor(presetID)
	self.presetID = presetID
	self.furnitureEntityID = nil
end

---家具模型加载完成
function FurniturePartBase:OnModelLoaded(actor)
	
end

---家具模型卸载
function FurniturePartBase:UnloadModel()
	self.furnitureEntityID = nil
end

---进入交互
function FurniturePartBase:OnEnterBehaviorTrigger()
end

---离开交互
function FurniturePartBase:OnLeaveBehaviorTrigger()
end

function FurniturePartBase:CheckCanDeleteOnBuild(bNotify)
	return true
end

return FurniturePartBase