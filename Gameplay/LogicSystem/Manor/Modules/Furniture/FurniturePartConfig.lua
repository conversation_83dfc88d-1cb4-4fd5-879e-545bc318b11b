---@class FurniturePartConfig
local FurniturePartConfig = DefineClass("FurniturePartConfig")

---建造物
FurniturePartConfig.TypeConfig =
{
	[Enum.BuildItemSubGroup.Workbench]    		= "Gameplay.LogicSystem.Manor.Modules.Furniture.FurniturePartFactory",
	[Enum.BuildItemSubGroup.WorkshopWine] 		= "Gameplay.LogicSystem.Manor.Modules.Workshop.WorkshopPartCommon",
	[Enum.BuildItemSubGroup.WorkshopDessert] 	= "Gameplay.LogicSystem.Manor.Modules.Workshop.WorkshopPartCommon",
	[Enum.BuildItemSubGroup.WorkshopDetective] 	= "Gameplay.LogicSystem.Manor.Modules.Workshop.WorkshopPartCommon",
	[Enum.BuildItemSubGroup.WorkshopPerfume] 	= "Gameplay.LogicSystem.Manor.Modules.Workshop.WorkshopPartCommon",
	[Enum.BuildItemSubGroup.WorkshopFactory] 	= "Gameplay.LogicSystem.Manor.Modules.Workshop.WorkshopPartCommon",
}

function FurniturePartConfig.CreateFurniturePart(type, presetID)
	local path = FurniturePartConfig.TypeConfig[type] or FurniturePartConfig.TypeConfig[Enum.BuildItemSubGroup.Workbench]
	return kg_require(path).new(presetID)
end

return FurniturePartConfig
