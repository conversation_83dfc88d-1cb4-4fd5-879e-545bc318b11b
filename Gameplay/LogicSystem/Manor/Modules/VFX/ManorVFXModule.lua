---@class ManorVFXModule: LuaClass
local ManorVFXModule = DefineClass("ManorVFXModule")
local ManorUtils = kg_require("Gameplay.LogicSystem.Manor.ManorUtils")


function ManorVFXModule:Init()
	self.fogVFX = nil
end

function ManorVFXModule:UnInit()
	self.fogVFX = nil
end

function ManorVFXModule:EnterManorLevel(levelMapData)
	self:initFogParams()
	self:playFogFadeVFX()
end

function ManorVFXModule:LeaveManorLevel(levelMapData)
	self:stopFogFadeVFX()
	self:unInitFogParams()
end

function ManorVFXModule:initFogParams()
end

function ManorVFXModule:unInitFogParams()
	self.fogVFX = nil
end

function ManorVFXModule:OnHomeUpgradeSucc()
	self:playCameraAnim()
	self:playFogFadeVFX()
end

function ManorVFXModule:playFogFadeVFX()
	local homelandLv = ManorUtils.GetMainPlayerHomelandLv()
	local matParamKey = ManorUtils.GetFogMatParamKeyByHomeLv(homelandLv)
	-- 设置材质参数
	Log.DebugFormat("[ManorVFXModule:playFogFadeVFX] matParamKey: %s", matParamKey)
end

function ManorVFXModule:playCameraAnim()
	
end

function ManorVFXModule:stopFogFadeVFX()
end

return ManorVFXModule
