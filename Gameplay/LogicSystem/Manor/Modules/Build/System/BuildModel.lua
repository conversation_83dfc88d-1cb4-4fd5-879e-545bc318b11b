---@class BuildModel:SystemModelBase
local BuildModel = DefineClass("BuildModel",SystemModelBase)
local BuildGridComponent = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildGrid.BuildGridComponent")
local BuildGridFurniture = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildGrid.BuildGridFurniture")
local BuildNodeFactory = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildNode.BuildNodeFactory")
local BuildTools = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.System.BuildTools")
local BuildInstance = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.System.BuildInstance")
local HomeUtils = kg_require("Shared.Utils.HomeUtils")
local HomeConst = kg_require("Shared.Const.HomeConst")
local EPropertyClass = import("EPropertyClass")
local ManorPartData = import("ManorPartData")
local BuildingPart = import("BuildingPart")
local ManorStairPartData = import("ManorStairPartData")
local Actor = import("Actor")
local EBuildingPartModelType = import("EBuildingPartModelType")
local EGridHideType = import("EGridHideType")
local ManorPartChangeData = import("ManorPartChangeData")
local ManorUtils = kg_require("Gameplay.LogicSystem.Manor.ManorUtils")

function BuildModel:init()
end

function BuildModel:clear()
	self.floorDataInstance = nil
	self.wallDataInstance = nil
	self.pillarDataInstance = nil
	self.allAliveCuttableTreePos = nil
	if self.curPlaceNode then
		self.curPlaceNode:Destroy()
		self.curPlaceNode = nil
	end
end

---@public
function BuildModel:OnMsgHomeBuildingSync(maxFurnitureID, furnitureData, buildingData, destroyedTreeList, destroyedBarrier)
	Log.DebugFormat("Build: OnMsgHomeBuildingSync")
	---最大家具id
	self.maxFurnitureID = maxFurnitureID
	---家具数据
	self.furnitureData = furnitureData
	--组件数据
	self.buildingData = buildingData
	---@type table<number, ManorVec3>
	self.allAliveCuttableTreePos = self:InitAliveCuttableTreePos(destroyedTreeList)  --没被砍的树位置数据
	---@type table<number, ManorBarrieInfo>
	self.allAliveBarriers = self:InitAliveBarriers(destroyedBarrier) -- 可破坏障碍物

	self:InitFurnitureAndComponent()
end

---@public
---进入家园
function BuildModel:EnterManorLevel(levelMapData)
	Log.DebugFormat("Build: 进入家园")
	local levelMapID = levelMapData.ID
	self.levelMapId = levelMapID
	self:InitWorldConfig()

	---@type LocalBuildNodeEditBase 正在摆放的节点
	self.curPlaceNode = nil
	---@type LocalBuildNodeRuntimeBase 鼠标指向的选中节点
	self.curSelectNode = nil
	self.openSelectNode = true
	self.curBuildState = nil
	
	--主角相关
	self.curMainPlayerIdle = nil
	self.curMainPlayerRotateControl = nil
	---主角当前楼层
	self.curMainPlayerPos = {X=0, Y=0, Z=0}
	self.curMainPlayerLayer = 0
	self.curMainPlayerLayerHeight = 0
	
	--网格
	self.gridDecalShow = false
	---@type LocalBuildGridEntity
	self.gridDecalEntity = nil
	self:CreateGridDecal()
	
	--相机溶解
	self:CreateCameraDissolve()
	
	self.ignoreActors =  slua.Array(EPropertyClass.Object, Actor)
	self.ignoreActorIDs =  slua.Array(EPropertyClass.Int64)
	
	self.overlayActors =  slua.Array(EPropertyClass.Object, Actor)
	self.overlayActorIDs =  slua.Array(EPropertyClass.Int64)
	
	self.highLightCustomDataEmpty = slua.Array(EPropertyClass.Float)
	self.highLightCustomDataEmpty:Add(0)
	self.highLightCustomData = {}
	self:InitHighLightCustomData()

	self.curHideAreaType = EGridHideType.None
	
	self.hideAreaTSet =  slua.Set(EPropertyClass.Int)
	self.hideAreaTSetCheck =  slua.Set(EPropertyClass.Int)

	--连线
	self.effectLineEntity = nil
	self.lineEffectBlue = true

	self.inManor = true
	
	---临时服务器数据
	self.reqUpdateBuildDateList = {}
	
	---临时鼠标位置
	self.curMouseViewPos = {X = 0, Y = 0}
	
	---是否在建造（界面打开）
	self.inBuild = false

	---是否开启相机溶解
	self.cameraDissolve = true

	self.isMouseBtnDrag = false
	
	self:InitFurnitureAndComponent()
end

---@public
---离开家园
function BuildModel:LeaveManorLevel()
	Log.DebugFormat("Build: 离开家园")
	self.inManor = false
	self:unInitFurnitureAndComponent()

	if self.curPlaceNode then
		self.curPlaceNode:Destroy()
		self.curPlaceNode = nil
	end
	self.curSelectNode = nil
	self.curBuildState = nil
	
	self.buildInstance = nil
	self:destroyWorldConfig()

	self.curMainPlayerIdle = nil
	self.curMainPlayerRotateControl = nil
	self.curMainPlayerLayer = nil

	self.gridDecalShow = nil
	self.gridDecalActorID = nil
	self:DestroyGridDecal()

	self.highLightCustomDataEmpty = nil
	self.highLightCustomData = nil

	if self.effectLineEntity then
		self.effectLineEntity:destroy()
		self.effectLineEntity = nil
	end

	if self.cameraDissolveActorID then
		Game.ObjectActorManager:DestroyActorByID(self.cameraDissolveActorID)
		self.cameraDissolveActorID = nil
	end
	
	self.ignoreActors =  nil
	self.inManor = nil

	self.inBuild = nil

	self.reqUpdateBuildDateList = nil

	if self.cameraLoadHandle then
		Game.AssetManager:RemoveAssetReferenceByLoadID(self.cameraLoadHandle)
		self.cameraLoadHandle = nil
		self.cameraAssetID = nil
	end
	
	Game.C7ManorBuildingManager:DestroyManorBuildInstance()
end

---@public
function BuildModel:AddRuntimeNode(node)
	self.buildNodeRuntimeMapNew[node.layer][node:GetMainType()][node:GetSubType()][node.uniqueID] = node
end

---@public
function BuildModel:RemoveRuntimeNode(node)
	self.buildNodeRuntimeMapNew[node.layer][node:GetMainType()][node:GetSubType()][node.uniqueID] = nil
	node:Destroy()
end

---@public
function BuildModel:RemoveRuntimeNodeAll()
	for _, nodeListLayer in pairs(self.buildNodeRuntimeMapNew) do
		for _, nodeListMainType in pairs(nodeListLayer) do
			for _, nodeListSubType in pairs(nodeListMainType) do
				for _, node in pairs(nodeListSubType) do
					node:Destroy()
				end
			end
		end
	end
	self:InitRuntimeNodeMap()
end

---@public
function BuildModel:GetRuntimeNodeByUniqueID(uniqueID)
	for _, nodeListLayer in pairs(self.buildNodeRuntimeMapNew) do
		for _, nodeListMainType in pairs(nodeListLayer) do
			for _, nodeListSubType in pairs(nodeListMainType) do
				if nodeListSubType[uniqueID] then
					return nodeListSubType[uniqueID]
				end
			end
		end
	end
	
	return nil
end

---@public
function BuildModel:GetRuntimeNodeByCollisionActorID(collisionActorID, layer, excludeType)
	if layer then
		local buildNodeRuntimeList = self.buildNodeRuntimeMapNew[layer]
		if buildNodeRuntimeList then
			for mainType, nodeListMainType in pairs(buildNodeRuntimeList) do
				if not excludeType or mainType ~= excludeType then
					for _, nodeListSubType in pairs(nodeListMainType) do
						for _, node in pairs(nodeListSubType) do
							if node:GetCollisionActorID() == collisionActorID then
								return node
							end
						end
					end
				end
			end
		end
		return
	end
	
	for _, nodeListLayer in pairs(self.buildNodeRuntimeMapNew) do
		for mainType, nodeListMainType in pairs(nodeListLayer) do
			if not excludeType or mainType ~= excludeType then
				for _, nodeListSubType in pairs(nodeListMainType) do
					for _, node in pairs(nodeListSubType) do
						if node:GetCollisionActorID() == collisionActorID then
							return node
						end
					end
				end
			end
		end
	end

	return nil
end

---@public
function BuildModel:GetComponentRuntimeNodeByIndex(layer, subType, buildIndex)
	local nodeList = self.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][subType]
	for _, node in pairs(nodeList) do
		if node.buildIndex == buildIndex then
			return node
		end
	end
	
	return nil
end

function BuildModel:getBuildAreaConfigByHomelandLv()
	local homelandLv = ManorUtils.GetMainPlayerHomelandLv()
	local configData = Game.TableData.GetManorLevelUpDataRow(homelandLv)
	if not configData then
		Log.ErrorFormat("HomelandLevel = %s not exist in LevelUp_家园升级.xlsx", homelandLv)
		return
	end
	local buildAreaLocation = configData.buildAreaLocation
	return buildAreaLocation[1], buildAreaLocation[2], buildAreaLocation[3]
end

function BuildModel:UpdateBuildAreaGridInfos(init)
	local levelMapID = self.levelMapId
	local buildAreaLoc, buildAreaSizeX, buildAreaSizeY = self:getBuildAreaConfigByHomelandLv()
	--起始位置
	if init then
		-- 建造区域的起点
		self.gridLocation = ManorUtils.GetVec3(buildAreaLoc[1], buildAreaLoc[2], buildAreaLoc[3])
	else
		self.gridLocation.X = buildAreaLoc[1]
		self.gridLocation.Y = buildAreaLoc[2]
		self.gridLocation.Z = buildAreaLoc[3]
	end

	local config = Game.TableData.GetHomeConfigDataRow(levelMapID)
	if not config then
		Log.ErrorFormat("MapID = %s not exist in Manor_庄园.HomeConfig.xlsx", levelMapID)
		return
	end
	local gridSizeGround, gridSizeFloor, gridNumMax = config.GridSizeGround, config.GridSizeFloor, config.GridNumberMax

	-- 可建造区域，随着等级变化
	self.buildGridXMax = math.floor(buildAreaSizeX / gridSizeFloor[1])
	self.buildGridYMax = math.floor(buildAreaSizeY / gridSizeFloor[2])

	if init then
		-- 刚开始初始化的时候，初始化一次就行，防止创建多余的table
		-- 策划配置的家园的最大格子数
		self.gridXMax = gridNumMax[1]
		self.gridYMax = gridNumMax[2]
		self.gridFloorSize = ManorUtils.GetVec3(gridSizeFloor[1], gridSizeFloor[2], gridSizeFloor[3])
		self.gridFurnitureSize = ManorUtils.GetVec3(gridSizeGround[1], gridSizeGround[2], gridSizeGround[3])
		--用来结算格子的初始点
		self.initGridLocation = ManorUtils.GetVec3(
			buildAreaLoc[1], 
			buildAreaLoc[2] - (gridNumMax[2] -self.buildGridYMax) * 0.5 * gridSizeFloor[2],
			buildAreaLoc[3])
		self.layerMax = config.MaxLayer
		-- 建造区域的终点
		self.buildEndLoc = ManorUtils.GetVec3(
			self.gridLocation.X + self.buildGridXMax * self.gridFloorSize.X,
			self.gridLocation.Y + self.buildGridYMax * self.gridFloorSize.Y,
			self.gridLocation.Z
		)
	else
		self:updateBuildGridLoc()
		self:updateGridDecalEntity()
	end
end

function BuildModel:updateGridDecalEntity()
	if self.gridDecalEntity then
		local posX, posY, posZ, scaleX, scaleY, scaleZ = self:GetDecalLocationAndScale()
		self.gridDecalEntity:UpdateScale(posX, posY, posZ, scaleX, scaleY, scaleZ)
	end
end

function BuildModel:updateBuildGridLoc()
	self.buildEndLoc.X = self.gridLocation.X + self.buildGridXMax * self.gridFloorSize.X
	self.buildEndLoc.Y = self.gridLocation.Y + self.buildGridYMax * self.gridFloorSize.Y
end

function BuildModel:IsBuildPosValid(buildPosX, buildPosY)
	return buildPosX >= self.gridLocation.X and buildPosX <= self.buildEndLoc.X and
		buildPosY >= self.gridLocation.Y and buildPosY <= self.buildEndLoc.Y
end

---@private
function BuildModel:InitWorldConfig()
	self:UpdateBuildAreaGridInfos(true)
	self:InitRuntimeNodeMap()

	--高层隐藏相关
	self.curHideAreaFloorIndexList = {}
	for i = 0, self.layerMax-1 do
		self.curHideAreaFloorIndexList[i] = -1
	end

	
	---@type BuildGridComponent
	self.gridComponent = BuildGridComponent.new(self.initGridLocation, self.gridXMax, self.gridYMax, self.layerMax, self.gridFloorSize)
	---@type BuildGridFurniture
	self.gridFurniture = BuildGridFurniture.new(self.initGridLocation, self.gridXMax *5, self.gridYMax *5, self.layerMax, self.gridFurnitureSize)
end

function BuildModel:destroyWorldConfig()
	self:destroyBuildAreaGridInfos()
	self:destroyRuntimeNodeMap()
	self.gridComponent:delete()
	self.gridComponent = nil
	self.gridFurniture:delete()
	self.gridFurniture = nil
end

function BuildModel:destroyBuildAreaGridInfos()
	ManorUtils.ReturnVec3(self.gridLocation)
	ManorUtils.ReturnVec3(self.gridFloorSize)
	ManorUtils.ReturnVec3(self.gridFurnitureSize)
	ManorUtils.ReturnVec3(self.initGridLocation)
	ManorUtils.ReturnVec3(self.buildEndLoc)
end

function BuildModel:destroyRuntimeNodeMap()
	for _, nodeListLayer in pairs(self.buildNodeRuntimeMapNew) do
		for _, nodeListMainType in pairs(nodeListLayer) do
			for _, nodeListSubType in pairs(nodeListMainType) do
				for _, node in pairs(nodeListSubType) do
					node:Destroy()
				end
			end
		end
	end
	self.buildNodeRuntimeMapNew = nil
end

function BuildModel:InitRuntimeNodeMap()
	---运行态节点
	---@type table<number, LocalBuildNodeRuntimeComponent>
	self.buildNodeRuntimeMapNew = {}
	
	for i = 0, self.layerMax-1 do
		self.buildNodeRuntimeMapNew[i] = {}
		for _, config in ksbcpairs(Game.TableData.GetManorItemSubTypeDataTable()) do
			if not self.buildNodeRuntimeMapNew[i][config.Type] then
				self.buildNodeRuntimeMapNew[i][config.Type] = {}
			end
			self.buildNodeRuntimeMapNew[i][config.Type][config.ID] = {}
		end
	end
end

function BuildModel:unInitFurnitureAndComponent()
	if self.inManor then return false end
	self:unInitGridTree()
	self:unInitBarriers()
end

function BuildModel:InitFurnitureAndComponent()
	if not self.inManor then
		return
	end

	if not self.furnitureData or not self.buildingData then
		return
	end

	self:InitGridTree()
	self:initBarriers()
	self:InitComponentMeshPart()
end

---@private
function BuildModel:InitComponentMeshPart()
	self.meshPartData = {}
	self.meshPartPath = {}
	self.meshPartComponentList = {}
	self.meshPartComponentIDList = {}
	for _, config in ksbcpairs(Game.TableData.GetManorItemDataTable()) do
		if config.TypeId == Enum.BuildItemType.Component and config.GroupId>0 and config.FrameValue>0 and config.model then
			if config.ModelPartComponent and #config.ModelPartComponent>0 then
				--多个部分组成，PartValue相同
				local maxLength = #config.ModelPartComponent
				for index, modelPath in ksbcipairs(config.ModelPartComponent) do
					table.insert(self.meshPartPath, modelPath)
					table.insert(self.meshPartData, {
						type = config.GroupId,
						partValue = config.FrameValue,
						modelType = (index == maxLength and EBuildingPartModelType.Top or EBuildingPartModelType.Bottom)
					})
				end
			else
				table.insert(self.meshPartPath, config.model)
				table.insert(self.meshPartData, {
					type = config.GroupId,
					partValue = config.FrameValue,
					modelType = EBuildingPartModelType.Whole
				})
			end
		end
	end

	self.componentMeshPartLoadId = Game.AssetManager:AsyncLoadAssetListKeepReferenceID(self.meshPartPath, self, "OnComponentMeshPartLoaded")
end

function BuildModel:OnComponentMeshPartLoaded(loadID, LoadAssetIDs)
	local LoadObjNum = LoadAssetIDs:Num()
	if LoadObjNum ~= #self.meshPartData then
		Log.ErrorFormat("BuildSystem:OnComponentMeshPartLoaded Fail")
		return
	end
	
	for i = 1, LoadObjNum do
		local partData = BuildingPart()
		partData.Type = BuildTools.GetInstanceComponentTypeByGroupType(self.meshPartData[i].type)
		partData.ModelType = self.meshPartData[i].modelType
		partData.Transform = FTransform()
		partData.PartValue = self.meshPartData[i].partValue
		table.insert(self.meshPartComponentList, partData)
		table.insert(self.meshPartComponentIDList, LoadAssetIDs:Get(i-1))
	end

	self:InitManorBuildInstance()
end

---@private
function BuildModel:InitManorBuildInstance()
	---@type table<number, ManorPartData>
	self.floorDataInstance = {}
	self.wallDataInstance = {}
	self.pillarDataInstance = {}
	---@type table<number, ManorStairPartData>
	self.stairDataInstance = {}
	
	for index, data in ipairs(self.buildingData) do
		self:GenerateInstanceFloorPartData(index-1, data.Floor)
		self:GenerateInstanceWallPartData(index-1, data.Wall)
		self:GenerateInstancePillarPartData(index-1, data.Pillar)
		self:GenerateInstanceStairPartData(index-1, data.Stair)
	end

	Game.C7ManorBuildingManager:CreateManorBuildInstance(
		Game.TableData.GetManorSettingRow("ManorInstancePath"),
		self.initGridLocation,
		self.layerMax,
		self.gridXMax, self.gridYMax,
		self.floorDataInstance, self.wallDataInstance, self.pillarDataInstance, self.stairDataInstance,
		self.meshPartComponentList,
		self.meshPartComponentIDList
	)
	
	---@type BuildInstance
	self.buildInstance = BuildInstance.new()
	--10高亮，1个显隐
	self.buildInstance:SetComponentISMCustomDataMaxLength(11)
	self.buildInstance:SetComponentISMOverlayMaterial(UIAssetPath.MR_SeeThrough_Overlay_01)

	self.floorDataInstance = nil
	self.wallDataInstance = nil
	self.pillarDataInstance = nil
	self.stairDataInstance = nil

	self:InitFurniture()

	self:checkBuildingTransform()

	if self.componentMeshPartLoadId then
		Game.AssetManager:RemoveAssetReferenceByLoadID(self.componentMeshPartLoadId)
		self.componentMeshPartLoadId = nil
	end

	self.meshPartData = nil
	self.meshPartPath = nil
	self.meshPartComponentList = nil
	self.meshPartComponentIDList = nil
end

---@private
function BuildModel:InitFurniture()
	for i, data in pairs(self.furnitureData) do
		self:AddFurnitureRuntimeNode(data.furnitureID, data.buildingID, data.position,
			HomeUtils.BuildingGetYawByTransform(data.transform))
	end
end

---@private
function BuildModel:InitGridTree()
	if not self.allAliveCuttableTreePos or not self.gridComponent then
		return
	end
	
	for _, pos in pairs(self.allAliveCuttableTreePos) do
		self.gridComponent:AddTree(pos.X, pos.Y, pos.Z)
	end
end

function BuildModel:unInitGridTree()
	for instId, _ in pairs(self.allAliveCuttableTreePos) do
		self:OnCuttableTreeDestoyed(instId)
	end
end

---@private
function BuildModel:initBarriers()
	if not self.gridComponent or not self.allAliveBarriers then return end
	for _, barrierInfo in pairs(self.allAliveBarriers) do
		local pos, size = barrierInfo.Pos, barrierInfo.Size
		self.gridComponent:AddBarrier(pos.X, pos.Y, size.X, size.Y)
	end
end

function BuildModel:unInitBarriers()
	for instId, _ in pairs(self.allAliveBarriers) do
		self:OnBarrieDestroyed(instId)
	end
end

---获取格子贴花entity
---@public
function BuildModel:GetGridDecalEntity()
	if not self.gridDecalEntity then
		self:CreateGridDecal()
	end
	
	return self.gridDecalEntity
end

---@private
function BuildModel:CreateCameraDissolve()
	self.cameraLoadHandle = Game.AssetManager:AsyncLoadAssetKeepReferenceID(
		Game.TableData.GetManorSettingRow("CameraDissolvePath"), self, "OnCameraMPCLoad"
	)
end

function BuildModel:OnCameraMPCLoad(LoadID, AssetID)
	self.cameraAssetID = AssetID
end

---@private
function BuildModel:CreateGridDecal()
	if self.gridDecalEntity then
		return
	end

	local posX, posY, posZ, scaleX, scaleY, scaleZ = self:GetDecalLocationAndScale()
	self.gridDecalEntity = Game.EntityManager:CreateLocalEntity(
		"LocalBuildGridEntity",
		{
			actorLocationX = posX,
			actorLocationY = posY,
			actorLocationZ = posZ,
			actorScaleX = scaleX,
			actorScaleY = scaleY,
			actorScaleZ = scaleZ,
			actorShow = self.gridDecalShow,

			gridSize = self.gridFloorSize.X,
			gridLocation = self.gridLocation,
		}
	)
end

---@private
function BuildModel:DestroyGridDecal()
	self.gridDecalShow = false
	if self.gridDecalEntity then
		self.gridDecalEntity:destroy()
		self.gridDecalEntity = nil
	end
end

---@private
function BuildModel:InitHighLightCustomData()
	for i, config in ksbcpairs(Game.TableData.GetManorHighLightEffectDataTable()) do
		local array = slua.Array(EPropertyClass.Float)
		array:Add(config.HighLightOn)
		array:Add(config.HighLightBlinkOn)
		array:Add(config.HighLightRimSpeed)
		array:Add(config.HighLightRimPower)

		array:Add(config.HighLightEmission[1])
		array:Add(config.HighLightEmission[2])
		array:Add(config.HighLightEmission[3])

		array:Add(config.HighLightTint[1])
		array:Add(config.HighLightTint[2])
		array:Add(config.HighLightTint[3])
		
		self.highLightCustomData[config.ID] = array
	end
end

---@private
function BuildModel:GetDecalLocationAndScale()
	--格子贴花位置
	--格子贴花尺寸（格子数量）
	return self.gridLocation.X + self.buildGridXMax *self.gridFloorSize.X/2,
		self.gridLocation.Y + self.buildGridYMax *self.gridFloorSize.X/2,
		self.gridLocation.Z + 0.05,
		self.buildGridXMax, 
		self.buildGridYMax, 
		10
end

function BuildModel:CreateLineEffect()
	if self.effectLineEntity then
		return
	end

	local pos = Game.me:GetPosition()
	self.effectLineEntity = Game.EntityManager:CreateLocalEntity(
		"LocalBuildEffectLineEntity",
		{
			actorLocationX = pos.X,
			actorLocationY = pos.Y,
			actorLocationZ = pos.Z,
			actorShow = false,
		}
	)
end

---获取格子贴花actor
---@public
function BuildModel:GetCurLayerHeight()
	return self.curMainPlayerLayer * self.gridFloorSize.Z + self.gridLocation.Z
end

---获取连线entity
---@publicss
function BuildModel:GetLineEffectEntity(notAutoCreate)
	if not self.effectLineEntity and not notAutoCreate then
		self:CreateLineEffect()
	end
	
	return self.effectLineEntity
end

---@private
function BuildModel:GenerateInstanceFloorPartData(layer, data)
	local PartData = ManorPartData()
	local maxLength = BuildTools.GetComponentMaxLengthByType(Enum.BuildItemGroup.Floor, self.gridXMax, self.gridYMax)
	--优化一下
	for index = 1, maxLength do
		local value = data[index] and data[index].value
		if value then
			PartData.Data:Add(value)
			self:AddComponentRuntimeNode(Enum.BuildItemGroup.Floor, layer, index-1, value)
		else
			PartData.Data:Add(0)
		end
	end

	table.insert(self.floorDataInstance, PartData)
end

---@private
function BuildModel:GenerateInstanceWallPartData(layer, data)
	local PartData = ManorPartData()
	local maxLength = BuildTools.GetComponentMaxLengthByType(Enum.BuildItemGroup.Wall, self.gridXMax, self.gridYMax)
	for index = 1, maxLength do
		local value = data[index] and data[index].value
		if value then
			PartData.Data:Add(value)
			self:AddComponentRuntimeNode(Enum.BuildItemGroup.Wall, layer, index-1, value)
		else
			PartData.Data:Add(0)
		end
	end

	table.insert(self.wallDataInstance, PartData)
end

---@private
function BuildModel:GenerateInstancePillarPartData(layer, data)
	local PartData = ManorPartData()
	local maxLength = BuildTools.GetComponentMaxLengthByType(Enum.BuildItemGroup.Pillar, self.gridXMax, self.gridYMax)
	for index = 1, maxLength do
		local value = data[index] and data[index].value
		if value then
			PartData.Data:Add(value)
			self:AddComponentRuntimeNode(Enum.BuildItemGroup.Pillar, layer, index-1, value)
		else
			PartData.Data:Add(0)
		end
	end

	table.insert(self.pillarDataInstance, PartData)
end

---@private
function BuildModel:GenerateInstanceStairPartData(layer, data)
	local PartData = ManorStairPartData()
	for index, info in pairs(data) do
		local value = info.value
		PartData.Data:Add(index-1, value)
		self:AddComponentRuntimeNode(Enum.BuildItemGroup.Stair, layer, index-1, info.value)
	end

	table.insert(self.stairDataInstance, PartData)
end

function BuildModel:AddFurnitureRuntimeNode(furnitureID, buildingID, position, yaw)
	local node = BuildNodeFactory.CreateRuntimeNode(furnitureID, {
		buildingID = buildingID,
		position = position,
		yaw = yaw
	})
	
	--self.buildNodeRuntimeComponentMap[node.uniqueID] = node
	--self.buildNodeRuntimeMapNew[node.layer][node:GetMainType()][node:GetSubType()][node.uniqueID] = node
	self:AddRuntimeNode(node)
end

---@public
function BuildModel:AddComponentRuntimeNode(type, layer, buildIndex, partValue)
	if not partValue or partValue <= 0 then
		return
	end
	
	--取低16位
	local frameValue = Game.bit.band(partValue, 0xFFFF)
	if not frameValue then
		Log.ErrorFormat("frameValue=nil, type=%s, buildIndex=%s, value=%s", type, buildIndex, partValue)
		return
	end

	if frameValue <= 0 then
		return
	end
	
	local presetID = Enum.BuildFrameType[type][frameValue]
	if not presetID then
		Log.ErrorFormat("presetID=nil, type=%s, buildIndex=%s, value=%s", type, buildIndex, partValue)
		return
	end

	local node = BuildNodeFactory.CreateRuntimeNode(presetID, {
		layer = layer,
		buildIndex = buildIndex,
		partValue = partValue
	})
	self:AddRuntimeNode(node)

	Log.DebugFormat("建造初始化node：type=%s, index=%s, presetID=%s", type, buildIndex, presetID)
end

function BuildModel:InitAliveCuttableTreePos(destroyedTreeList)
	if not destroyedTreeList then
		return {}
	end

	local destroyedInsMark = {}
	for _, InsID in pairs(destroyedTreeList) do
		destroyedInsMark[InsID] = true
	end
	local _SceneActorMap = Game.WorldDataManager:GetCurLevelAllSceneActorDataByType(EWActorType.CUTTABLE_TREE)
	local Res = {}
	for InsID, Data in pairs(_SceneActorMap) do
		if not destroyedInsMark[InsID] then
			local Pos = Data.Transform.Position
			Res[InsID] = ManorUtils.GetVec3(Pos.X, Pos.Y, Pos.Z)
		end
	end
	return Res
end

function BuildModel:OnCuttableTreeDestoyed(InsID)
	local treeInfo = self.allAliveCuttableTreePos[InsID]
	if treeInfo then
		self.gridComponent:RemoveTree(treeInfo.X, treeInfo.Y)
		ManorUtils.ReturnVec3(treeInfo)
		self.allAliveCuttableTreePos[InsID] = nil
	end
end

--todo 测试接口，为了方便障碍物测试，后续删掉
function BuildModel:TestBarriers()
	self.allAliveBarriers = self:InitAliveBarriers({})
	self:initBarriers()
end

function BuildModel:InitAliveBarriers(destroyedBarrier)
	if not destroyedBarrier then
		return {}
	end

	local destroyedInsMark = {}
	for _, InsID in pairs(destroyedBarrier) do
		destroyedInsMark[InsID] = true
	end
	
	local Ret = {}
	local allCommonInteractors = Game.CommonInteractorManager:GetAllCommonInteractors()
	local homeBarrierClsType = Enum.CommonInteractorClassType.HomelandBarrier
	for instId, commonInteractor in pairs(allCommonInteractors) do
		local interactorConfigData = commonInteractor:GetEntityConfigData() 
		local curClsType = interactorConfigData.ClassType
		if homeBarrierClsType == curClsType then
			local pos = commonInteractor:GetPosition()
			local sizeX, sizeY = ManorUtils.GetBarrierSize(interactorConfigData.ModelID)
			local barrieInfo = ManorUtils.ConstructBarrieInfo(pos.X, pos.Y, pos.Z, sizeX, sizeY)
			Ret[instId] = barrieInfo
		end
	end
	return Ret
end

function BuildModel:OnBarrieDestroyed(instId)
	local barrie = self.allAliveBarriers[instId]
	if barrie then
		local pos, size = barrie.Pos, barrie.Size
		self.gridComponent:RemoveBarrier(pos.X, pos.Y, size.X, size.Y)
		ManorUtils.DestructBarrieInfo(barrie)
		self.allAliveBarriers[instId] = nil
	end
end

---@private
---全量刷新建造数据
function BuildModel:RefreshAllByChangeData(manorPartChangeData)
	local buildFrameInfo = {}
	for i = 1, self.layerMax do
		buildFrameInfo[i] = {Wall = {}, Floor = {}, Pillar = {}, Stair = {}}
		for j = 1, BuildTools.GetComponentMaxLengthByType(Enum.BuildItemGroup.Wall, self.gridXMax, self.gridYMax) do
			buildFrameInfo[i].Wall[j] = 0
		end
		for j = 1, BuildTools.GetComponentMaxLengthByType(Enum.BuildItemGroup.Floor, self.gridXMax, self.gridYMax) do
			buildFrameInfo[i].Floor[j] = 0
		end
		for j = 1, BuildTools.GetComponentMaxLengthByType(Enum.BuildItemGroup.Pillar, self.gridXMax, self.gridYMax) do
			buildFrameInfo[i].Pillar[j] = 0
		end
	end
	
	local dataList = manorPartChangeData.DataList
	for i = 1, dataList:Num() do
		local changeData = dataList:Get(i-1)
		local componentType = BuildTools.GetGroupTypeByInstanceComponentType(changeData.BuildingPartType)
		local layer = changeData.LayerIndex
		local index = changeData.PartIndex
		local value = changeData.NewValue
		self:AddComponentRuntimeNode(componentType, layer, index, value)

		if componentType == Enum.BuildItemGroup.Wall then
			buildFrameInfo[layer+1].Wall[index+1] = value
		elseif componentType == Enum.BuildItemGroup.Floor then
			buildFrameInfo[layer+1].Floor[index+1] = value
		elseif componentType == Enum.BuildItemGroup.Pillar then
			buildFrameInfo[layer+1].Pillar[index+1] = value
		elseif componentType == Enum.BuildItemGroup.Stair then
			buildFrameInfo[layer+1].Stair[index+1] = value
		end
	end
	
	return buildFrameInfo
end

-- 检查建筑的校验码
function BuildModel:checkBuildingTransform()
	--TODO:@zhaoran 检查是否是主角的家园

	-- 检查家具的transform
	self:checkFurnitureTransform()

	-- 检查静态组件(地板/楼梯/柱子/墙)的transform
	self:checkStaticComponent()

	-- 检查动态组件(屋顶/围栏/围栏柱子)的value和transform
	self:checkDynamicComponent()

	-- 检查尖顶的value
	self:checkSpireComponent()
end

-- 检查家具部分的transform
function BuildModel:checkFurnitureTransform()
	local transformDiff = {}
	local furnitureData = self.furnitureData
	for _, nodeListLayer in pairs(self.buildNodeRuntimeMapNew) do
		for _, nodeListSubType in pairs(nodeListLayer[Enum.BuildItemType.Furniture]) do
			for uniqueID, node in pairs(nodeListSubType) do
				-- 计算客户端的transform
				local buildPosition = node.buildPosition
				local succ, transform = HomeUtils.BuildingPosYawToTransform(buildPosition.X, buildPosition.Y, buildPosition.Z, node.yaw)
				if succ then
					local buildingID = node.buildingID
					local buildingInfo = furnitureData[buildingID]
					local serverTransform = buildingInfo and buildingInfo.transform
					if transform ~= serverTransform then
						-- 双端transform不一致
						transformDiff[buildingID] = transform
					end
				else
					Log.ErrorFormat("Furniture Node %s BuildingPosYawToTransform failed", uniqueID)
				end
			end
		end
	end
	
	if next(transformDiff) then
		Game.ManorSystem.ManorBuildModule:ReqUpdateFurnitureTransform(transformDiff)
	end
end

function BuildModel:getComponentDataByLayerAndIndex(type, layer, index)
	local buildingData = self.buildingData
	local layerData = buildingData[layer]
	if layerData == nil then
		return
	end

	local layerTypeData
	local buildItemGroup = Enum.BuildItemGroup
	for _, typeName in ipairs(HomeConst.HOMELAND_FRAME_TYPE) do
		if type == buildItemGroup[typeName] then
			layerTypeData = layerData[type]
			break
		end
	end
	if layerTypeData == nil then
		return
	end

	return layerTypeData[index]
end

-- 检查静态组件(地板/楼梯/柱子/墙)的transform
function BuildModel:checkStaticComponent()
	local changeData = ManorPartChangeData()
	self.buildInstance:GetStaticComponentData(changeData)

	local transformDiff = {}
	local maxNum = changeData.DataList:Num()
	if maxNum > 0 then
		for i = 0, maxNum-1 do
			local oneChangeData = changeData.DataList:Get(i)
			local componentType = BuildTools.GetGroupTypeByInstanceComponentType(oneChangeData.BuildingPartType)

			-- 计算客户端transform
			local transformArray = oneChangeData.Transform
			if transformArray:Num() ~= HomeConst.HOMELAND_BUILDING_TRANSFORM_SIZE then
				Log.Error("transformArray size invalid")
				goto continue
			end

			local succ, transform = HomeUtils.BuildingPosYawToTransform(transformArray:Get(0), transformArray:Get(1), transformArray:Get(2), transformArray:Get(3))
			if not succ then
				Log.ErrorFormat("RuntimeComponent %s %s %s transform invalid", componentType, oneChangeData.LayerIndex+1, oneChangeData.PartIndex+1)
				goto continue
			end

			local componentData = self:getComponentDataByLayerAndIndex(componentType, oneChangeData.LayerIndex+1, oneChangeData.PartIndex+1)
			if componentData == nil then
				table.insert(transformDiff, {
					layer = oneChangeData.LayerIndex+1,
					type = componentType,
					index = oneChangeData.PartIndex+1,
					value = oneChangeData.NewValue,
					transform = transform,
				})
			elseif componentData.transform ~= transform then
				table.insert(transformDiff, {
					layer = oneChangeData.LayerIndex+1,
					type = componentType,
					index = oneChangeData.PartIndex+1,
					value = 0,
					transform = transform,
				})
			end

			::continue::
		end
	end

	if next(transformDiff) then
		-- 同步数据给服务器
		Game.ManorSystem.ManorBuildModule:ReqUpdateFrameTransform(transformDiff)
	end	
end

-- 检查动态组件(屋顶/围栏/围栏柱子)的transform
function BuildModel:checkDynamicComponent()	
	local changeData = ManorPartChangeData()
	self.buildInstance:GetRoofAndRailingData(changeData)

	local transformDiff = {}
	local maxNum = changeData.DataList:Num()
	if maxNum > 0 then
		for i = 0, maxNum-1 do
			local oneChangeData = changeData.DataList:Get(i)
			local componentType = BuildTools.GetGroupTypeByInstanceComponentType(oneChangeData.BuildingPartType)

			-- 计算客户端transform
			local transformArray = oneChangeData.Transform
			if transformArray:Num() ~= HomeConst.HOMELAND_BUILDING_TRANSFORM_SIZE then
				Log.Error("transformArray size invalid")
				goto continue
			end

			local succ, transform = HomeUtils.BuildingPosYawToTransform(transformArray:Get(0), transformArray:Get(1), transformArray:Get(2), transformArray:Get(3))
			if not succ then
				Log.ErrorFormat("RuntimeComponent %s %s %s transform invalid", componentType, oneChangeData.LayerIndex+1, oneChangeData.PartIndex+1)
				goto continue
			end

			-- 比较value和transform
			local componentData = self:getComponentDataByLayerAndIndex(componentType, oneChangeData.LayerIndex+1, oneChangeData.PartIndex+1)
			if componentData == nil or transform ~= componentData.transform or oneChangeData.NewValue ~= componentData.value then
				table.insert(transformDiff, {
					layer = oneChangeData.LayerIndex+1,
					type = componentType,
					index = oneChangeData.PartIndex+1,
					value = oneChangeData.NewValue,
					transform = transform,
				})
			end

			::continue::
		end
	end

	if next(transformDiff) then
		-- 同步数据给服务器
		Game.ManorSystem.ManorBuildModule:ReqUpdateFrameTransform(transformDiff)
	end
end

function BuildModel:checkSpireComponent()
	local changeData = ManorPartChangeData()
	self.buildInstance:GetSpireData(changeData)

	local topDiff = {}
	local maxNum = changeData.DataList:Num()
	if maxNum > 0 then
		for i = 0, maxNum-1 do
			local oneChangeData = changeData.DataList:Get(i)
			local componentType = BuildTools.GetGroupTypeByInstanceComponentType(oneChangeData.BuildingPartType)

			-- 比较value
			local componentData = self:getComponentDataByLayerAndIndex(componentType, oneChangeData.LayerIndex+1, oneChangeData.PartIndex+1)
			if componentData == nil or oneChangeData.NewValue ~= componentData.value then
				table.insert(topDiff, {
					layer = oneChangeData.LayerIndex+1,
					type = componentType,
					index = oneChangeData.PartIndex+1,
					value = oneChangeData.NewValue,
					transform = 0,
				})
			end
		end
	end

	if next(topDiff) then
		-- 同步数据给服务器
		Game.ManorSystem.ManorBuildModule:ReqUpdateFrameTransform(topDiff)
	end
end

return BuildModel