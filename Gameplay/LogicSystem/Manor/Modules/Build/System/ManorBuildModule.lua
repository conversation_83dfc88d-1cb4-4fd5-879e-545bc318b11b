---@class ManorBuildModule
local ManorBuildModule = DefineClass("ManorBuildModule")
local KismetMathLibrary = import("KismetMathLibrary")
local EMovementOutputMode = import("EMovementOutputMode")
local EInterpolationMode = import("EInterpolationMode")
local EArgSourceMode = import("EArgSourceMode")
local EInputEvent = import("EInputEvent")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local BuildStateFactory = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildState.BuildStateFactory")
local BuildNodeFactory = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildNode.BuildNodeFactory")
local EGridHideType = import("EGridHideType")
local KismetInputLibrary = import("KismetInputLibrary")
local HomeConst = kg_require("Shared.Const.HomeConst")

local ParallelBehaviorControlConst = kg_require("Shared.Const.ParallelBehaviorControlConst")
local WEAK_FORCE_CONTROL_REASON_TAGS = ParallelBehaviorControlConst.WEAK_FORCE_CONTROL_REASON_TAGS

---@private
function ManorBuildModule:Init()
	---@type BuildModel
	self.model = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.System.BuildModel").new(false, true)
end

---@private
function ManorBuildModule:UnInit()
end

--region 测试
---开关相机显隐
function ManorBuildModule:SetCameraDissolveOpen(open)
	if not open then
		self:UpdateCameraDissolveParameter(10000)
		self.model.cameraDissolve = open
	else
		self.model.cameraDissolve = open
		self:UpdateCameraDissolveParameter(self.model.curMainPlayerLayerHeight)
	end
end

---测试
function ManorBuildModule:PrintCurSelectData()
	if self.model.curSelectNode then
		Log.DebugFormat("Build：CurSelect name=%s, layer=%s",
			self.model.curSelectNode:GetName(), self.model.curSelectNode.layer)
		return
	end

	Log.DebugFormat("Build：CurSelect = nil")
end

function ManorBuildModule:AddOnePart(type, grid, index, value1, value2, value3)
	local instance = self:GetManorBuildingInstance()
	local ManorPartChangeData = import("ManorPartChangeData")()
	if type == 0 then
		instance:AddFloor(grid, index, value1, ManorPartChangeData)
	elseif type == 1 then
		instance:AddWall(grid, index, value1, value2, ManorPartChangeData)
	elseif type == 2 then
		instance:AddPillar(grid, index, value1, ManorPartChangeData)
	elseif type == 4 then
		instance:AddStair(grid, index, value1, value2, value3, ManorPartChangeData)
	end
end

function ManorBuildModule:RemoveOnePart(type, grid, index, value1, value2, value3)
	local instance = self:GetManorBuildingInstance()
	local ManorPartChangeData = import("ManorPartChangeData")()
	instance:RemoveOnePart(type, grid, index, ManorPartChangeData)
end

--endregion

--region 流程相关
---@public
---进入家园场景
function ManorBuildModule:EnterManorLevel(levelMapData)
	self.model:EnterManorLevel(levelMapData)

	self:RegisterAllEvent()
end

---@public
---离开家园场景
function ManorBuildModule:LeaveManorLevel(levelMapData)
	self.model:LeaveManorLevel(levelMapData)

	self:UnRegisterAllEvent()
end

---@public
---进入建造（打开界面）
function ManorBuildModule:EnterBuild()
	if self.model.inBuild then
		return
	end

	self.model.inBuild = true
	self:StartBanSkill()
	self:ShowGridDecal()
end

---@public
function ManorBuildModule:IsInBuild()
	return self.model.inBuild
end

---@public
---离开建造（关闭界面）
function ManorBuildModule:LeaveBuild()
	if not self.model.inBuild then
		return
	end

	self.model.inBuild = false
	self:EndBanSkill()
	self:EnterNoneState()
	self:DestroyGridDecal()
	self:RefreshSelectNodeByNode(nil)
end

---@private
function ManorBuildModule:RegisterAllEvent()
	Game.UIInputProcessorManager:BindMouseButtonDownEvent(self, "OnMouseButtonDown")
	Game.UIInputProcessorManager:BindMouseButtonUpEvent(self, "OnMouseButtonUp")

	Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, "OnRoleActionInputEvent", self)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_CUTTABLE_TREE_DESTROED, "OnCuttableTreeDestoyed", self)

	Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.LOCO_DEFAULT_LOCO_STATE_CHANGE, self, self.OnLocoStateChange, Game.me.eid)
	Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.LOCO_START_CHANGE, self, self.OnLocoStartChange, Game.me.eid)
	
	Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_COMMON_INTERACTOR_DESTROY, "onCommonInteractorDestroy", self)

	if not self.tickTimer then
		self.tickTimer = Game.TimerManager:TickTimer(function(deltaTime)
			self:OnUpdate(deltaTime)
		end, -1)
	end
end

---@private
function ManorBuildModule:UnRegisterAllEvent()
	Game.UIInputProcessorManager:UnBindMouseButtonDownEvent(self)
	Game.UIInputProcessorManager:UnBindMouseButtonUpEvent(self)

	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
	Game.EventSystem:RemoveObjListeners(self)
	if Game.me then
		Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.LOCO_DEFAULT_LOCO_STATE_CHANGE, self, self.OnLocoStateChange, Game.me.eid)
		Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.LOCO_START_CHANGE, self, self.OnLocoStateChange, Game.me.eid)
	end

	if self.tickTimer then
		Game.TimerManager:StopTimerAndKill(self.tickTimer)
		self.tickTimer = nil
	end
end

--endregion

--region 建造状态相关
---@public
function ManorBuildModule:TransferState(stateID, ...)
	if not self:CanTransferState(stateID, ...) then
		return false
	end

	if self.model.curBuildState then
		if self.model.curBuildState.OnLeave then
			self.model.curBuildState:OnLeave(...)
		end
	end

	local nextState = BuildStateFactory.CreateState(stateID)
	if nextState then
		if nextState.OnEnter then
			nextState:OnEnter(...)
		end
	end
	self.model.curBuildState = nextState
end

---@public
function ManorBuildModule:CanTransferState(stateID, ...)
	if not stateID then
		--Log.ErrorFormat("ManorBuildModule:Transfer Failed, StateID Cannot Be Nil")
		return false
	end

	if self.model.curBuildState and self.model.curBuildState.StateID == stateID then
		--if self.model.curBuildState.StateID ~= 0 then
		--	Log.ErrorFormat("ManorBuildModule:Transfer Failed, same State, StateID = %s", stateID)
		--end
		return false
	end

	if not BuildStateFactory.StateConfig[stateID] then
		--Log.ErrorFormat("ManorBuildModule:Transfer Failed, StateConfig Nil StateID = %s", stateID)
		return false
	end

	return true
end

---@public
function ManorBuildModule:GetCurStateID()
	return self.model.curBuildState and self.model.curBuildState.StateID
end
--endregion

--region 协议API
---同步家具、房屋信息
function ManorBuildModule:OnMsgHomeBuildingSync(maxFurnitureID, furnitureData, buildingData, destroyedTreeList, destroyedBarrier)
	if not destroyedTreeList then
		Log.ErrorFormat("Build: OnMsgHomeBuildingSync, destroyedTreeList = nil")
		destroyedTreeList = {}
	end
	self.model:OnMsgHomeBuildingSync(maxFurnitureID, furnitureData, buildingData, destroyedTreeList, destroyedBarrier)
end

function ManorBuildModule:OnHomeUpgradeSucc()
	self.model:UpdateBuildAreaGridInfos()
end

---请求清空组件
function ManorBuildModule:ReqClearBuilding()
	self.sender:ReqClearBuilding()
end

---回复清空组件
function ManorBuildModule:RetClearBuilding(result)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("ReqClearBuilding failed Code=%s", result)
		return
	end
end

---请求新增、删除组件
function ManorBuildModule:ReqUpdateBuildingFrame(buildingFrameList)
	self.sender:ReqUpdateBuildingFrame(buildingFrameList)
end

function ManorBuildModule:ReqUpdateFurnitureTransform(transformDict)
	self.sender:ReqUpdateFurnitureTransform(transformDict)
end

function ManorBuildModule:ReqUpdateFrameTransform(transformList)
	self.sender:ReqUpdateFrameTransform(transformList)
end

---回复新增、删除组件 
function ManorBuildModule:RetUpdateBuildingFrame(result, buildingFrameList)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		--HomeConst.HOME_ERRCODE
		Log.ErrorFormat("RetUpdateBuildingFrame failed Code=%s", result)

		for _, BuildingFrame in pairs(buildingFrameList) do
			--取前16位，如果有值，就是被改动了
			if BuildingFrame.value then
				--删除失败
				Log.DebugFormat("RetUpdateBuildingFrame failed type=%s layer=%s index=%s value=%s", BuildingFrame.type, BuildingFrame.layer-1, BuildingFrame.index, BuildingFrame.value)
				self:AddNodeLocal(BuildingFrame.type, BuildingFrame.layer-1, BuildingFrame.index-1, BuildingFrame.value)
			else
				--新增失败
				Log.DebugFormat("RetUpdateBuildingFrame failed type=%s layer=%s index=%s value=%s", BuildingFrame.type, BuildingFrame.layer-1, BuildingFrame.index, BuildingFrame.value)
				self:RemoveNodeLocal(BuildingFrame.layer-1, BuildingFrame.index-1)
			end
		end
		return
	end

	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_FURNITURE_UPDATE)
	Game.ManorSystem.ManorInfoModule:UpdateRedDotStatus()
	--todo：新增一个待删除列表? 只有成功时，才真的删。失败重新加回来。
end

---请求新增家具
function ManorBuildModule:ReqAddBuilding(furnitureInfo)
	self.sender:ReqAddBuilding(furnitureInfo)
end

---回复新增家具
function ManorBuildModule:RetAddBuilding(result, buildingID)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("RetAddBuilding failed Code=%s", result)

		--删除家具
		for i, node in pairs(self.model.BuildNodeRuntimeComponentList) do
			if node.BuildingID == buildingID or node.buildingID == buildingID then
				self:DestroyRuntimeNode(node)
				break
			end
		end

		self.model.maxFurnitureID = self.model.maxFurnitureID + 100
		return
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_FURNITURE_UPDATE)
	Game.ManorSystem.ManorInfoModule:UpdateRedDotStatus()
	Game.ManorSystem.ManorWorkshopModule:OnFurnitureAdded()
end

---请求回收家具
function ManorBuildModule:ReqDelBuilding(buildingID)
	self.sender:ReqDelBuilding(buildingID)
end

---回复回收家具
function ManorBuildModule:RetDelBuilding(result, buildingID)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("RetDelBuilding failed Code=%s", result)

		--todo_jitengfei: 添加回来
		return
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_FURNITURE_UPDATE)
	Game.ManorSystem.ManorInfoModule:UpdateRedDotStatus()
end

---请求修改家具
function ManorBuildModule:ReqUpdateBuilding(furnitureInfo)
	self.sender:ReqUpdateBuilding(furnitureInfo)
end

---回复修改家具
function ManorBuildModule:RetUpdateBuilding(result, buildingID)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("RetUpdateBuilding failed Code=%s", result)

		--todo_jitengfei: 修改回来
		return
	end
end



--endregion

--region 摆放相关
---进入状态：摆放物摆放
---@public
function ManorBuildModule:EnterPlaceItemState(presetID)
	if self:IsPlaceNode() then
		return
	end

	self:TransferState(Enum.EBuildState.PlaceItem, presetID)
end

---进入状态：家具重新摆放
---@public
function ManorBuildModule:EnterChangeFurnitureState()
	if not self.model.curSelectNode or self.model.curSelectNode:IsComponent() then
		return
	end

	self:TransferState(Enum.EBuildState.ChangeFurniture, self.model.curSelectNode)
end

---进入状态：无
---@public
function ManorBuildModule:EnterNoneState()
	self:TransferState(Enum.EBuildState.None)
end

---新家具摆放
---@public
function ManorBuildModule:CreateBuildItem(presetID)
	if self:IsPlaceNode() then
		Log.ErrorFormat("CreateBuildItem Error, self.model.CurEditNode~=nil, presetID=%s", presetID)
		return
	end

	local newNode = self:CreateEditNode(presetID)
	if not newNode then
		Log.ErrorFormat("CreateBuildItem Error, newNode=nil, presetID=%s", presetID)
		return
	end

	local x,y,z = self:TraceScreenCenter()
	newNode:SetItemPosition(x,y,z)
	newNode:LoadPlaceActor()

	self:StartPlaceNode(newNode)
end

---克隆家具摆放
---@public
function ManorBuildModule:CloneFurnitureItem()
	if not self.model.curSelectNode then
		return
	end

	if self.model.curSelectNode:IsComponent() then
		return
	end

	local newNode = self:CloneEditNodeWithRuntimeNode(self.model.curSelectNode)

	self:StartPlaceNode(newNode)
	self:ForceUpdateBuild()
end

---@private
function ManorBuildModule:OnRoleActionInputEvent(actionName, inputEvent)
	if inputEvent == EInputEvent.IE_Pressed then
		if actionName == Enum.EInputTypeSystemGroup.ManorBuild_Rotate_Action then
			self:OnClickRotate(actionName, inputEvent)
		elseif actionName == Enum.EInputTypeSystemGroup.ManorBuild_Cancel_Action then
			self:OnClickSpace()
		end
	elseif inputEvent == EInputEvent.IE_Released then

	end
end

---@private
function ManorBuildModule:OnClickRotate()
	if not self:IsPlaceNode() then
		return
	end

	if not self.model.curPlaceNode:CanRotate() then
		return
	end

	--旋转
	self.model.curPlaceNode:SetItemRotate()
end

---@private
function ManorBuildModule:OnClickSpace()
	if self:IsPlaceNode() then
		self:CancelPlaceNode()
	else
		self:DestroySelectNode()
	end
end

---开始摆放
---@private
function ManorBuildModule:StartPlaceNode(node)
	self.model.curPlaceNode = node

	if self.model.curMainPlayerIdle then
		self:StartRotateControl()
	end
	self:UpdatePlayerPosition()
end

---取消摆放
---@public
function ManorBuildModule:CancelPlaceNode()
	if not self:IsPlaceNode() then
		return false
	end

	self:EnterNoneState()
end

---确认摆放
---@public
function ManorBuildModule:ConfirmPlaceNode()
	if not self:IsPlaceNode() then
		return
	end

	if not self.model.curPlaceNode:CanBuild() then
		return
	end

	--如果它有未解锁的子组件，不能摆放
	if self:CheckHaveLockedComponent(self.model.curPlaceNode.presetID) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOME_SUB_FORNITURE_LOCK)
		return
	end
	
	--先判断有没有足够的数量摆放，需要检查 要摆放的物品本身的数量，和它子组件的数量
	if not self:CheckCanPlace(self.model.curPlaceNode:GetPlaceNumberData()) then
		--若数量不够，则需要购买
		--判断钱够不够，不够弹出提示
		local curMoney = Game.BagSystem:GetItemCount(HomeConst.HOME_MONEY_TYPE) or 0
		local needMoney = self:GetPlacePrice(self.model.curPlaceNode:GetPlaceNumberData())
		if curMoney < needMoney then
			--钱不够，弹出提示
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOMEMSG_HOME_MONEY_NOT_ENOUGH)
			return
		end
	end
	
	if self.model.curPlaceNode:IsComponent() then
		self:UploadBuildComponent() 
	else
		self:UploadBuildFurniture()
	end

	self:ForceUpdateBuild()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_FURNITURE_UPDATE)
	--获取摆放物剩余数量
	local buildNum = Game.ManorSystem.ManorBuildStoreModule:GetStoreFurnitureNumByID(self.model.curPlaceNode.presetID)
	if buildNum-1 > 0 then
		return
	end
	
	self:EnterNoneState()
end

---@private
---检查是否有子组件未解锁
function ManorBuildModule:CheckHaveLockedComponent(targetID)
	local config = Game.TableData.GetManorItemDataRow(targetID)
	if string.isEmpty(config.CombineComponent)then
		return false
	end

	for group in config.CombineComponent:gmatch("{(.-)}") do
		local id = nil
		for result in group:gmatch("%((.-)%)") do
			if not id then
				id = tonumber(result)
			end
		end
		if not Game.ManorSystem.ManorBuildStoreModule:IsFurnitureUnlocked(id) then
			return true
		end
	end

	return false
end

---@private
---判断该物品与它需要的子组件数量是否足够
---@param placeNumberData table { [物品ID ] = 数量}
---@return boolean 是否足够
function ManorBuildModule:CheckCanPlace(placeNumberData)
	for id, buildNum in pairs(placeNumberData) do
		local curNum = Game.ManorSystem.ManorBuildStoreModule:GetStoreFurnitureNumByID(id)
		if not curNum or curNum < buildNum then
			return false
		end
	end
	return true
end

---@private
---获取摆放物的总价格
---@param placeNumberData table { [物品ID ] = 数量}
---@return number 价格
function ManorBuildModule:GetPlacePrice(placeNumberData)
	local price = 0
	for id, buildNum in pairs(placeNumberData) do
		local config = Game.TableData.GetManorItemDataRow(id)
		price = price + config.BuyPrice * buildNum
	end
	return price
end

---确认修改
---@public
function ManorBuildModule:ConfirmChangeNode()
	if not self:IsPlaceNode() then
		return
	end

	if not self.model.curPlaceNode:CanBuild() then
		return
	end

	self:UploadChangeFurniture()

	self:EnterNoneState()
end

---更换摆放物
---@public
function ManorBuildModule:ChangeEditNode(presetID)
	if not self:IsPlaceNode() then
		return
	end

	self.model.curPlaceNode:Destroy()
	self.model.curPlaceNode:destroy()
	self.model.curPlaceNode = nil

	local newNode = self:CreateEditNode(presetID)
	if not newNode then
		Log.ErrorFormat("ChangeEditNode Error, newNode=nil, presetID=%s", presetID)
		return
	end

	local x,y,z = self:TraceScreenCenter()
	newNode:SetItemPosition(x,y,z)

	self.model.curPlaceNode = newNode
end

---销毁当前选中的节点
---@public
function ManorBuildModule:DestroySelectNode()
	if not self:GetSelectNode() then
		return
	end

	if not self.model.curSelectNode:CanDelete() then
		return
	end

	--二次确认
	local node = self.model.curSelectNode
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.MANOR_RECYCLE_FURNITURE, function()
		self:DestroySelectNodeConfirm(node)
		self:SetSelectNodeOpen(true)
	end, function()
		self:SetSelectNodeOpen(true)
	end, nil,nil,nil, function()
		self:SetSelectNodeOpen(true)
	end)

	--取消选中
	self:SetSelectNodeOpen(false, true)
end

---@private
function ManorBuildModule:DestroySelectNodeConfirm(node)
	node:RemoveData()

	if node:IsComponent() then
		table.clear(self.model.reqUpdateBuildDateList)
		node:GetServerData(self.model.reqUpdateBuildDateList)
		self:UploadDeleteComponent()
	else
		self:UploadDeleteFurniture(node)
	end

	self:RefreshSelectNodeByNode(nil)
	self:DestroyRuntimeNode(node)
end

---@private
function ManorBuildModule:SetSelectNodeOpen(open, forceUnselect)
	self.model.openSelectNode = open
	if forceUnselect then
		self:RefreshSelectNodeByNode(nil)
	end
end

---创建运行时node
---@private
function ManorBuildModule:CreateRuntimeNode(presetID, param)
	local newNode = BuildNodeFactory.CreateRuntimeNode(presetID, param)
	return newNode
end

---创建编辑态node
---@private
function ManorBuildModule:CreateEditNode(presetID)
	local newNode = BuildNodeFactory.CreateEditNode(presetID)
	return newNode
end

---克隆编辑态node，使用运行态node
---@private
function ManorBuildModule:CloneEditNodeWithRuntimeNode(node)
	local newNode = BuildNodeFactory.CloneEditNode(node)
	return newNode
end

---克隆行态node，使用编辑态node
---@private
function ManorBuildModule:CloneRuntimeNodeWithEditNode(node, param)
	local newNode = BuildNodeFactory.CloneRuntimeNode(node, param)
	return newNode
end

---销毁运行时node
---@private
function ManorBuildModule:DestroyRuntimeNode(node)
	self.model:RemoveRuntimeNode(node)
end

---销毁编辑态node
---@private
function ManorBuildModule:DestroyPlaceNode()
	if not self.model.curPlaceNode then
		return
	end

	self.model.curPlaceNode:Destroy()
	self.model.curPlaceNode:destroy()
	self.model.curPlaceNode = nil
end

--function ManorBuildModule:UploadBuildComponent()
--	
--end

---@private
---添加一个组件
function ManorBuildModule:UploadBuildComponent()
	local runtimeNodeMain = self:GetComponentRuntimeNodeByIndex(self.model.curPlaceNode.layer, self.model.curPlaceNode:GetSubType(), self.model.curPlaceNode.buildIndex)
	if runtimeNodeMain then
		runtimeNodeMain:RemoveData()
		self:DestroyRuntimeNode(runtimeNodeMain)
	end

	local parentNode = self:CloneRuntimeNodeWithEditNode(self.model.curPlaceNode)
	self.model:AddRuntimeNode(parentNode)

	table.clear(self.model.reqUpdateBuildDateList)
	parentNode:GetServerData(self.model.reqUpdateBuildDateList)

	self:CheckUpdateComponent(self.model.reqUpdateBuildDateList)

	if self.model.curPlaceNode.childNodeList then
		for _, editNode in pairs(self.model.curPlaceNode.childNodeList) do
			--删除重复柱子
			local runtimeNode = self:GetComponentRuntimeNodeByIndex(editNode.layer, editNode:GetSubType(), editNode.buildIndex)
			if runtimeNode then
				runtimeNode:RemoveData()
				self:DestroyRuntimeNode(runtimeNode)
				Log.DebugFormat("Build: Delete Repeat Node. type=%s,index=%s", editNode:GetSubType(), editNode.buildIndex)
			end

			local childNode = self:CloneRuntimeNodeWithEditNode(editNode)
			self.model:AddRuntimeNode(childNode)

			childNode:GetServerData(self.model.reqUpdateBuildDateList)
		end
	end

	for i, data in pairs(self.model.reqUpdateBuildDateList) do
		Log.DebugFormat("建造上传：type=%s, index=%s, value=%s, layer=%s", data.type, data.index, data.value, data.layer)
	end

	self:ReqUpdateBuildingFrame(self.model.reqUpdateBuildDateList)
end

---@private
---检查组件更新
function ManorBuildModule:CheckUpdateComponent()
	--如果从有到没有，就删除
	for i, data in pairs(self.model.reqUpdateBuildDateList) do
		--根据index能找到，并且value = nil
		--没有或者低16位 = 0
		if not data.value or Game.bit.band(data.value, 0xFFFF) == 0 then
			local node = self:GetComponentRuntimeNodeByIndex(data.layer-1, data.type, data.index-1)
			if node then
				node:RemoveData()
				self:DestroyRuntimeNode(node)
			end
		end
	end
end

---@private
---删除一个组件
function ManorBuildModule:UploadDeleteComponent()
	self:ReqUpdateBuildingFrame(self.model.reqUpdateBuildDateList)
end

function ManorBuildModule:CheckCanUpgradeOnBuild(buildingID, nextFurnitureID)
	return self.model:CheckCanUpgradeOnBuild(buildingID, nextFurnitureID)
end

---@private
---添加一个家具
function ManorBuildModule:UploadBuildFurniture()
	self.model.maxFurnitureID = self.model.maxFurnitureID + 1

	local newNode = self:CloneRuntimeNodeWithEditNode(self.model.curPlaceNode, {
		buildingID = self.model.maxFurnitureID
	})
	self.model.gridFurniture:OnFurnitureAdded(newNode)
	self.model:AddRuntimeFurniture(newNode)

	table.clear(self.model.reqUpdateBuildDateList)
	newNode:GetServerData(self.model.reqUpdateBuildDateList)
	self.model.reqUpdateBuildDateList.buildingID = self.model.maxFurnitureID

	self:ReqAddBuilding(self.model.reqUpdateBuildDateList)
end

---@private
---删除一个家具
function ManorBuildModule:UploadDeleteFurniture(node)
	self.model.gridFurniture:OnFurnitureRemoved(node)
	self:ReqDelBuilding(node.furnitureBuildingID)
end

---@private
---修改家具
function ManorBuildModule:UploadChangeFurniture()
	local changeRuntimeNode = self:GetRuntimeNodeByUniqueID(self.model.curPlaceNode.updateNodeUniqueID)
	if not changeRuntimeNode then
		return
	end

	changeRuntimeNode:UpdateData(self.model.curPlaceNode)

	table.clear(self.model.reqUpdateBuildDateList)
	changeRuntimeNode:GetServerData(self.model.reqUpdateBuildDateList)
	self.model.reqUpdateBuildDateList.buildingID = changeRuntimeNode.furnitureBuildingID

	self:ReqUpdateBuilding(self.model.reqUpdateBuildDateList)
end

function ManorBuildModule:GetManorBuildingInstance()
	return self.model.buildInstance
end

function ManorBuildModule:GetEditNode()
	return self.model.curPlaceNode
end

function ManorBuildModule:GetSelectNode()
	return self.model.curSelectNode
end

---获取运行时节点
---@public
function ManorBuildModule:GetRuntimeNodeByUniqueID(uniqueID)
	return self.model:GetRuntimeNodeByUniqueID(uniqueID)
end

---获取运行时节点，根据碰撞
---@public
function ManorBuildModule:GetRuntimeNodeByCollisionActorID(collisionActorID, layer, excludeType)
	return self.model:GetRuntimeNodeByCollisionActorID(collisionActorID, layer, excludeType)
end

---获取组件运行时节点，根据index
---@public
function ManorBuildModule:GetComponentRuntimeNodeByIndex(layer, subType, index)
	return self.model:GetComponentRuntimeNodeByIndex(layer, subType, index)
end

---本地添加节点
---@private
function ManorBuildModule:AddNodeLocal(type, layer, buildIndex, value)

end

---本地删除节点
---@private
function ManorBuildModule:RemoveNodeLocal(layer, index)

end

function ManorBuildModule:GetGridXMax()
	return self.model.gridXMax
end

function ManorBuildModule:GetGridYMax()
	return self.model.gridYMax
end

function ManorBuildModule:GetGridXSize()
	return self.model.gridFloorSize.X
end

function ManorBuildModule:GetGridComponent()
	return self.model.gridComponent
end

function ManorBuildModule:GetGridFurniture()
	return self.model.gridFurniture
end

function ManorBuildModule:GetCurLayer()
	return self.model.curMainPlayerLayer
end

---是否正在编辑节点
---@public
function ManorBuildModule:IsPlaceNode()
	return self.model.curPlaceNode ~= nil
end

---是否正在编辑家具节点
---@public
function ManorBuildModule:IsPlaceFurnitureNode()
	return self.model.curPlaceNode and not self.model.curPlaceNode:IsComponent()
end

---设置运行时节点射线检测的碰撞有效性
---@public
function ManorBuildModule:SetRuntimeNodeCollisionEnable(enable)
	for _, nodeListLayer in pairs(self.model.buildNodeRuntimeMapNew) do
		for _, nodeListMainType in pairs(nodeListLayer) do
			for _, nodeListSubType in pairs(nodeListMainType) do
				for _, node in pairs(nodeListSubType) do
					node:SetCollisionEnable(enable)
				end
			end
		end
	end
end

---摆放家具时，显示小网格
---@private
function ManorBuildModule:SetFloorSmallGridVisible(visible, layer)
	if not layer then
		layer = self:GetCurLayer()
	end

	local nodeList = self.model.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][Enum.BuildItemGroup.Floor]
	for _, node in pairs(nodeList) do
		node:SetSmallGridVisible(visible)
	end
end

---@private
function ManorBuildModule:RefreshHighLayerVisible()
	local CameraRotation = Game.CameraManager:GetCameraRotation()
	--从下往上看
	if CameraRotation.Pitch > 0 then
		return
	end

	for layer = self.model.curMainPlayerLayer+1, self.model.layerMax-1 do
		local height = layer * self.model.gridFloorSize.Z + self.model.gridLocation.Z
		local success, intersectX, intersectY = Game.C7ManorBuildingManager:GetCameraRayPlaneIntersection(height,0,0,0)
		if success then
			local newFloorIndex = self.model.gridComponent:WorldPos2GridIndex(intersectX, intersectY)
			if newFloorIndex>=0 and (self:GetManorBuildingInstance():IsIndexHasFloor(layer, newFloorIndex) or self:IsFloorIndexInStair(layer, newFloorIndex))  then
				--正在隐藏
				if self.model.curHideAreaFloorIndexList[layer] > 0 then
					self.model.hideAreaTSetCheck:Clear()
					self:GetManorBuildingInstance():GetGridComponentHideData(layer, EGridHideType.None, self.model.hideAreaTSetCheck)
					if self:IsRefreshHideArea() then
						self:ShowHighLayerArea(layer)
						self:HideHighLayerArea(layer, newFloorIndex)
					end
					break
				else
					--正在显示
					self:ShowHighLayerArea(layer)
					self:HideHighLayerArea(layer, newFloorIndex)
					break
				end
			else
				self:ShowHighLayerArea(layer)
			end
		else
			self:ShowHighLayerArea(layer)
		end
	end

	--1、                 能隐藏地板
	--2、                 隐藏家具
	--3、				  隐藏相关组件
	--4、                 多层隐藏
	--5、                 修改组件，强制刷新。（下层的墙壁，修改上层的连通）
	--6、                 隐藏下层组件上半部分
end

---@private
function ManorBuildModule:IsRefreshHideArea()
	if self.model.curHideAreaType == EGridHideType.HideAll then
		return false
	end

	local oldNum = self.model.hideAreaTSet:Num()
	local newNum = self.model.hideAreaTSetCheck:Num()

	if oldNum == 0 or newNum == 0 then
		return true
	end

	if oldNum ~= newNum then
		return true
	end

	for i, v in pairs(self.model.hideAreaTSet) do
		local hideAreaValue = self.model.hideAreaTSetCheck:Get(v)
		if not hideAreaValue then
			return true
		end
	end

	return false
end

---@public
---修改墙壁、地板、楼梯，刷新上层（可能会影响连通区域）
function ManorBuildModule:ForceRefreshHighLayerVisible(layer)
	local upLayer = layer + 1
	if upLayer >= self.model.layerMax then
		return
	end

	self:ShowHighLayerArea(upLayer)
	self:HideHighLayerArea(upLayer, self.model.curHideAreaFloorIndexList[upLayer])
end

---@private
---显示某层的隐藏区域
function ManorBuildModule:ShowHighLayerArea(layer)
	if self.model.curHideAreaFloorIndexList[layer] == -1 then
		return
	end
	self.model.curHideAreaFloorIndexList[layer] = -1

	self.model.hideAreaTSet:Clear()
	self.model.curHideAreaType = self:GetManorBuildingInstance():GetGridComponentHideData(layer, EGridHideType.None, self.model.hideAreaTSet)

	if self.model.curHideAreaType == EGridHideType.HideAll then
		self:SetLayerIndexItemVisible(layer, true, true)
	else
		self:SetFloorIndexItemVisible(layer, true)
	end
	self:GetManorBuildingInstance():ShowGridComponentByLayer(layer)

	--显示以上的所有家具
	for i = layer+1, self.model.layerMax-1 do
		self:SetLayerIndexItemVisible(i, true, i == layer+1)
	end
end

---@private
---隐藏某层的区域
function ManorBuildModule:HideHighLayerArea(layer, floorIndex)
	if self.model.curHideAreaFloorIndexList[layer] == floorIndex then
		return
	end
	self.model.curHideAreaFloorIndexList[layer] = floorIndex

	self:GetManorBuildingInstance():HideGridComponentByLayer(layer, floorIndex)

	self.model.hideAreaTSet:Clear()
	self.model.curHideAreaType = self:GetManorBuildingInstance():GetGridComponentHideData(layer, EGridHideType.None, self.model.hideAreaTSet)

	if self.model.curHideAreaType == EGridHideType.HideAll then
		self:SetLayerIndexItemVisible(layer, false, true)
	else
		self:SetFloorIndexItemVisible(layer, false)
	end

	--隐藏以上的所有家具
	for i = layer+1, self.model.layerMax-1 do
		self:SetLayerIndexItemVisible(i, false, i == layer+1)
	end
end

---显隐地板下标中的所有家具（C++显隐组件，Lua显隐家具）（代理碰撞都是Lua隐藏）
---@private
function ManorBuildModule:SetFloorIndexItemVisible(layer, visible)
	local nodeList = self.model.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][Enum.BuildItemGroup.Floor]
	for _, node in pairs(nodeList) do
		local hideAreaValue = self.model.hideAreaTSet:Get(node.buildIndex)
		if hideAreaValue and hideAreaValue >= 0 then
			--（C++显隐组件，Lua显隐家具）
			node:SetChildFurnitureVisible(visible)
			--（代理碰撞都是Lua隐藏）
			node:SetNearItemCollisionEnable(visible)
		end
	end

	--设置楼梯碰撞
	local stairNodeList = self.model.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][Enum.BuildItemGroup.Stair]
	for _, node in pairs(stairNodeList) do
		node:SetCollisionEnable(visible)
	end
end

---显隐楼层下标中的所有家具（C++显隐组件，Lua显隐家具）（代理碰撞都是Lua隐藏）
---@private
function ManorBuildModule:SetLayerIndexItemVisible(layer, visible, includeCollision)
	--Log.WarningFormat("lua 隐藏显示层级  =%s, index=%s", visible)
	local nodeListLayer = self.model.buildNodeRuntimeMapNew[layer]
	for _, nodeListMainType in pairs(nodeListLayer) do
		for _, nodeListSubType in pairs(nodeListMainType) do
			for _, node in pairs(nodeListSubType) do
				--（C++显隐组件，Lua显隐家具）
				if not node:IsComponent() then
					node:SetVisible(visible)
				end
				--（代理碰撞都是Lua隐藏）
				if includeCollision then
					node:SetCollisionEnable(visible)
				end
			end
		end
	end

end

---@public
---清空家园摆放物
function ManorBuildModule:DestroyAllBuild()
	if not Game.ManorSystem:IsInManorSpace() then
		return
	end

	self:ReqClearBuilding()
	self.model:RemoveRuntimeNodeAll()

	self:GetManorBuildingInstance():ResetInstanceData()
	self:GetManorBuildingInstance():InitEmptyManor(self.model.layerMax, self.model.gridXMax, self.model.gridYMax)
end

---@public
---全量刷新建造数据
function ManorBuildModule:RefreshAllByChangeData(manorPartChangeData)
	return self.model:RefreshAllByChangeData(manorPartChangeData)
end
--endregion

--region 操作相关
---@public
function ManorBuildModule:GetPlayerLayer()
	return self.model.curMainPlayerLayer
end

---@private
function ManorBuildModule:OnUpdate()
	if Game.me.bInWorld == false then
		return
	end
	
	if not Game.me then
		return
	end
	
	if not self:GetManorBuildingInstance() then
		return
	end

	if Game.ManorSystem.ManorPCGModule:IsInPCG() then
		return
	end
	
	local getMousePositionSuccess, mouseX, mouseY = Game.me.CppEntity:KAPI_PlayerController_GetMousePosition()
	if not getMousePositionSuccess then
		return 
	end

	local playerMove = self:UpdatePlayerPosition()
	if not playerMove and self.model.curMouseViewPos.X == mouseX and self.model.curMouseViewPos.Y == mouseY and not self.model.isMouseBtnDrag then
		return
	end

	self.model.curMouseViewPos.X = mouseX
	self.model.curMouseViewPos.Y = mouseY

	self:RefreshHighLayerVisible()

	if self.model.inBuild then
		if self:IsPlaceNode() then
			--摆放状态
			local success, posX, posY, posZ = self:StartMouseRayTraceInGrid()
			if success then
				self:UpdateItemPosition(posX, posY, posZ)
				self:RefreshSelectNodeByNode(nil)
			end
		else
			--非摆放状态
			local success, result = self:StartMouseRayTraceInCenter()
			if success then
				self:RefreshSelectNodeByObject(result)
			end
		end
	end
end

---@private
---重置鼠标位置，强制刷新一次
function ManorBuildModule:ForceUpdateBuild()
	self.model.curMouseViewPos.X = 0
	self.model.curMouseViewPos.Y = 0
end

---@private
function ManorBuildModule:UpdatePlayerPosition()
	if not Game.me then
		return
	end

	local playerPos = Game.me:GetPosition()
	local playerX = playerPos.X
	local playerY = playerPos.Y
	local playerZ = playerPos.Z

	if playerX == self.model.curMainPlayerPos.X and
		playerY == self.model.curMainPlayerPos.Y and
		playerZ == self.model.curMainPlayerPos.Z
	then
		return false
	end

	self.model.curMainPlayerPos.X = playerX
	self.model.curMainPlayerPos.Y = playerY
	self.model.curMainPlayerPos.Z = playerZ

	--Log.DebugFormat("Build: playerPos X=%s,Y=%s,Z=%s, MainPlayerLayer=%s", playerPos.X, playerPos.Y, playerPos.Z,self.model.CurMainPlayerLayer)

	self:UpdatePlayerLayer(playerZ)

	self:UpdateLineEffectPlayerPosAndYaw(playerX, playerY, playerZ)
	
	return true
end

---@private
function ManorBuildModule:UpdatePlayerLayer(playerZ)
	local relativeZ = playerZ - self.model.gridLocation.Z
	local newLayer = math.floor((relativeZ + self.model.gridFloorSize.Z*(1-Game.TableData.GetManorSettingRow("PlayerLayerPercent"))) / self.model.gridFloorSize.Z)

	if newLayer < 0 then
		return
	end

	if self.model.curMainPlayerLayer == newLayer then
		return
	end

	if newLayer == self.model.curMainPlayerLayer+1 then
		self:ShowHighLayerArea(newLayer)
	end

	--如果正在摆放家具，显示小格子
	if self:IsPlaceFurnitureNode() then
		self:SetFloorSmallGridVisible(false, self.model.curMainPlayerLayer)
		self:SetFloorSmallGridVisible(true, newLayer)
	end

	self.model.curMainPlayerLayer = newLayer
	self.model.curMainPlayerLayerHeight = newLayer * self.model.gridFloorSize.Z + self.model.gridLocation.Z
	self:UpdateCameraDissolveParameter(self.model.curMainPlayerLayerHeight)
	self:UpdateGridDecalHeight()
end

---@private
function ManorBuildModule:UpdateItemPosition(inPosX, inPosY, inPosZ)
	self.model.curPlaceNode:SetItemLayer(self.model.curMainPlayerLayer)

	--local posZ = self.model.CurMainPlayerLayer * self.model.GridFloorSize.Z
	--local posZ = inPosZ>=0 and math.floor(inPosZ/self.model.GridFloorSize.Z) * self.model.GridFloorSize.Z or 0
	self.model.curPlaceNode:SetItemPosition(inPosX, inPosY, self.model:GetCurLayerHeight())

	local editX, editY, editZ = self.model.curPlaceNode:GetPosition()
	self:SetLineEffectBuildPos(editX, editY, editZ)
end

---@private
function ManorBuildModule:RefreshSelectNodeByObject(c7HitResult)
	if not self.model.openSelectNode then
		return
	end

	local selectNode = nil
	if c7HitResult and type(c7HitResult) == "userdata" and c7HitResult.HitObjectId then
		--优化：只能选中自己层和上下两层
		for i = self.model.curMainPlayerLayer-1, self.model.curMainPlayerLayer+1 do
			local node = self:GetRuntimeNodeByCollisionActorID(c7HitResult.HitObjectId, i)
			if node then
				selectNode = node
				break
			end
		end
	end

	self:RefreshSelectNodeByNode(selectNode)
end

---@private
function ManorBuildModule:RefreshSelectNodeByNode(node)
	if self.model.curSelectNode == node then
		return
	end

	if self.model.curSelectNode then
		self.model.curSelectNode:UnSelectNode()
	end

	self.model.curSelectNode = node
	if self.model.curSelectNode then
		self.model.curSelectNode:SelectNode()
	end

	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_BUILD_SELECT_NODE_CHANGE, self.model.curSelectNode)
end

---@private
function ManorBuildModule:OnMouseButtonDown(mouseEvent)
	if not Game.ManorSystem:IsInManorSpace() then
		return
	end

	local pointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(mouseEvent)
	if pointerIndex == 0 then
		--左键
		if self:IsPlaceNode() then
			if self:GetCurStateID() == Enum.EBuildState.PlaceItem then
				self:ConfirmPlaceNode()
			elseif self:GetCurStateID() == Enum.EBuildState.ChangeFurniture then
				self:ConfirmChangeNode()
			end
		else
			self:EnterChangeFurnitureState()
		end
	end

	self.model.isMouseBtnDrag = true
end

---@private
function ManorBuildModule:OnMouseButtonUp(mouseEvent)
	if not Game.ManorSystem:IsInManorSpace() then
		return
	end

	self.model.isMouseBtnDrag = false
end

---主角移动状态改变
---@private
function ManorBuildModule:OnLocoStateChange(isEnter)
	if isEnter then
		--进入Idle
		if self.model.curMainPlayerIdle then
			return
		end

		self.model.curMainPlayerIdle = true
		if self:IsPlaceNode() then
			self:StartRotateControl()
		end
	else
		if not self.model.curMainPlayerIdle then
			return
		end

		self.model.curMainPlayerIdle = false
		if self:IsPlaceNode() then
			self:EndRotateControl()
		end
	end
end

---@private
function ManorBuildModule:OnLocoStartChange(IsLocoStart)
	if not IsLocoStart then
		return
	end

	if not self.model.curMainPlayerIdle then
		return
	end

	self.model.curMainPlayerIdle = false
	if self:IsPlaceNode() then
		self:EndRotateControl()
	end
end

---开始控制主角旋转
---@private
function ManorBuildModule:StartRotateControl()
	if self.model.curMainPlayerRotateControl then
		return
	end

	if not self:IsPlaceNode() or not self.model.curPlaceNode.placeActorID then
		return
	end

	self.model.curMainPlayerRotateControl = true
	Game.me.LocoYawControllerFWB:SetForceValue(true, WEAK_FORCE_CONTROL_REASON_TAGS.ManorBuildModule, EMovementOutputMode.EOverride, EInterpolationMode.Halflife, 
        0.05, EArgSourceMode.TargetActor, 0, FVector(0, 0, 0), self.model.curPlaceNode.placeActorID)
end

---结束控制主角旋转
---@private
function ManorBuildModule:EndRotateControl()
	if not self.model.curMainPlayerRotateControl then
		return
	end

	self.model.curMainPlayerRotateControl = false

	if Game.me and Game.me.bInWorld then
		Game.me.LocoYawControllerFWB:ClearForceValue(WEAK_FORCE_CONTROL_REASON_TAGS.ManorBuildModule)
	end
end

---开始禁用技能
---@private
function ManorBuildModule:StartBanSkill()
	if not Game.me or not Game.me.bInWorld then
		return
	end

	Game.me:DisableAllSkillType(ETE.EDisabledSkillReason.ManorBuild)
	Game.me:DisableLocoJump(Enum.ELocoControlTag.Skill, true, false, ETE.EDisabledSkillReason.ManorBuild)
end

---结束禁用技能
---@private
function ManorBuildModule:EndBanSkill()
	if not Game.me or not Game.me.bInWorld then
		return
	end

	Game.me:EnableAllSkillType(ETE.EDisabledSkillReason.ManorBuild)
	Game.me:DisableLocoJump(Enum.ELocoControlTag.Skill, false, false, ETE.EDisabledSkillReason.ManorBuild)
end

--endregion

--region 位置、坐标判断相关
---下标是否在地板顶点上
---@public
function ManorBuildModule:IsPillarIndexInHasFloor(layer, indexX, indexY)
	local pillarIndex = indexX + indexY*(self.model.gridXMax +1)
	--右下、左下、右上、左上
	local rightBottomFloorIndex = pillarIndex - indexY
	local leftBottomFloorIndex = pillarIndex - indexY - 1
	local rightTopFloorIndex = rightBottomFloorIndex - self.model.gridXMax
	local leftTopFloorIndex = leftBottomFloorIndex - self.model.gridXMax

	if self:GetManorBuildingInstance():IsIndexHasFloor(layer, rightBottomFloorIndex, true) then
		return true
	end
	if self:GetManorBuildingInstance():IsIndexHasFloor(layer, leftBottomFloorIndex, true) then
		return true
	end
	if self:GetManorBuildingInstance():IsIndexHasFloor(layer, rightTopFloorIndex, true) then
		return true
	end
	if self:GetManorBuildingInstance():IsIndexHasFloor(layer, leftTopFloorIndex, true) then
		return true
	end

	return false
end

---这层是否有地板
---@public
function ManorBuildModule:IsLayerHasFloor(layer)
	local nodeList = self.model.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][Enum.BuildItemGroup.Floor]
	return table.count(nodeList) > 0
end

---@public
---地板下标位置是否被楼梯给占用
function ManorBuildModule:IsFloorIndexInStair(layer, floorIndex)
	local nodeList = self.model.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][Enum.BuildItemGroup.Stair]
	for _, node in pairs(nodeList) do
		if node:IsFloorIndexInStair(floorIndex) then
			return true
		end
	end

	return false
end

---地板下标是否在下层楼梯的出口位置上
---@public
--function ManorBuildModule:IsIndexInDownStairLeave(downLayer, indexX, indexY)
--	for _, node in pairs(self.model.BuildNodeRuntimeComponentList) do
--		if node.layer == downLayer and node:GetSubType() == Enum.BuildItemGroup.Stair then
--			for _, leaveIndex in pairs(BuildTools.StairEnterLeaveIndex[node:GetGroupSubType()][BuildTools.GetStairRotateValueByYaw(node.Yaw)].Leave) do
--				local stairIndexX, stairIndexY = self.model.GridComponent:GridIndex2LeftTopIndex(node.buildIndex)
--				if indexX == (leaveIndex.X+stairIndexX) and indexY == (leaveIndex.Y+stairIndexY) then
--					return true
--				end
--			end
--		end
--	end
--
--	return false
--end

---下标是否有家具
---@public
--function ManorBuildModule:IsFurnitureIndexHasFurniture(layer, furnitureFloorIndex)
--	if furnitureFloorIndex < 0 then
--		return false
--	end
--
--	for _, node in pairs(self.model.buildNodeRuntimeComponentList) do
--		if node.layer == layer and not node:IsComponent() and node:IsInFurnitureIndex(furnitureFloorIndex) then
--			return true
--		end
--	end
--
--	return false
--end

---向地板添加家具
---@public
function ManorBuildModule:AddChildFurnitureToFloor(layer, floorIndex, furnitureNode)
	---@type table<number, LocalBuildNodeRuntimeFloor>
	local nodeList = self.model.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][Enum.BuildItemGroup.Floor]
	for _, node in pairs(nodeList) do
		if node.buildIndex == floorIndex then
			node:AddChildFurniture(furnitureNode)
			return
		end
	end
end

---向地板删除家具
---@public
function ManorBuildModule:RemoveChildFurnitureToFloor(layer, floorIndex, furnitureNode)
	local nodeList = self.model.buildNodeRuntimeMapNew[layer][Enum.BuildItemType.Component][Enum.BuildItemGroup.Floor]
	for _, node in pairs(nodeList) do
		if node.buildIndex == floorIndex then
			node:RemoveChildFurniture(furnitureNode)
			return
		end
	end
end

---主角是否在建造范围内
---@public
function ManorBuildModule:IsPlayerInBuildArea()
	local playerLocation = Game.me:GetPosition()
	local model = self.model
	local endLocation = model.gridLocation + FVector(model.gridFloorSize.X * model.gridXMax, model.gridFloorSize.Y * model.gridYMax, 0)
	return playerLocation.X <= endLocation.X and playerLocation.Y <= endLocation.Y
		and playerLocation.X >= model.gridLocation.X and playerLocation.Y >= model.gridLocation.Y
end

--endregion

--region 射线
---获取重叠actor。根据自定义box
---@public
function ManorBuildModule:GetOverlayActorByBox(boxPosX,boxPosY,boxPosZ,boxExtentX,boxExtentY,boxExtentZ)
	Game.C7ManorBuildingManager:GetOverlayActorByBox(
		boxPosX,boxPosY,boxPosZ,
		boxExtentX,boxExtentY,boxExtentZ,
		CollisionConst.COLLISION_OBJECT_TYPE_BY_NAME.WorldStatic,
		self.model.overlayActorIDs
	)

	return self.model.overlayActorIDs
end

---获取重叠actor。根据actor
---@public
function ManorBuildModule:GetOverlayActorIDByActorID(actorID)
	if not actorID then
		return
	end
	
	Game.C7ManorBuildingManager:GetOverlayActorByActorID(
		actorID,
		CollisionConst.COLLISION_OBJECT_TYPE_BY_NAME.WorldStatic,
		self.model.overlayActorIDs
	)

	return self.model.overlayActorIDs
end


---鼠标位置射线和平面的相交
function ManorBuildModule:StartMouseRayTraceInGrid()
	local success, posX, posY, posZ = Game.me.CppEntity:KAPI_PlayerController_GetMouseRayPlaneIntersection(self.model:GetCurLayerHeight(), 0, 0, 0)
	return success, posX, posY, posZ
end

---鼠标位置射线碰到的物体
function ManorBuildModule:StartMouseRayTraceInCenter()
	self.model.ignoreActorIDs:Clear()
	self.model.ignoreActorIDs:Add(Game.C7ManorBuildingManager:GetManorBuildingInstanceID())
	self.model.ignoreActorIDs:Add(Game.me.CharacterID)

	local C7HitResult = Game.me.CppEntity:KAPI_PlayerController_GetHitResultUnderCursorWithIgnoreActors(
		CollisionConst.COLLISION_OBJECT_TYPE_BY_NAME.WorldStatic,
		false,
		self.model.ignoreActorIDs
	)

	if not C7HitResult.bResult then
		local x,y,z = Game.me:GetPosition_P()
		return x,y,z
	end
	
	return C7HitResult.bResult, C7HitResult
end

function ManorBuildModule:TraceScreenCenter()
	local StartPos = Game.CameraManager.CameraCachePrivate.POV.Location
	local ForwardVec = KismetMathLibrary.GetForwardVector(Game.CameraManager.CameraCachePrivate.POV.Rotation)
	local EndPos  = StartPos + ForwardVec * 3000
	self.model.ignoreActorIDs:Clear()
	self.model.ignoreActorIDs:Add(Game.me.CharacterID)
	local C7HitResult = LuaScriptAPI.KismetSystem_LineTraceSingle(
		Game.me.CharacterID,
		StartPos,
		EndPos,
		CollisionConst.COLLISION_TRACE_TYPE_BY_NAME.Visibility,
		false,
		self.model.ignoreActorIDs, --ActorsToIgnore
		0, --0
		true, --IgnoreSelf
		FLinearColor(1, 0, 0), --TraceColora
		FLinearColor(0, 1, 0), --HitColor
		5 --duration
	)

	if not C7HitResult.bResult then
		local x,y,z = Game.me:GetPosition_P()
		return x,y,z
	end

	local location = C7HitResult.HitResult.Location
	return location.X, location.Y, location.Z
end
--endregion

--region 网格相关
---@public
--function ManorBuildModule:ToggleManorGridDisplay()
--	if self.model.GridDecalShow then
--		self:HideGridDecal()
--	else
--		self:ShowGridDecal()
--	end
--end

function ManorBuildModule:ShowGridDecal()
	if self.model.gridDecalShow or not Game.ManorSystem:IsInManorSpace() then
		return
	end

	self.model.gridDecalShow = true
	
	local gridDecalEntity = self.model:GetGridDecalEntity()
	gridDecalEntity:SetVisible(self.model.gridDecalShow)
end

function ManorBuildModule:HideGridDecal()
	if not self.model.gridDecalShow then
		return
	end

	self.model.gridDecalShow = false
	
	local gridDecalEntity = self.model:GetGridDecalEntity()
	gridDecalEntity:SetVisible(self.model.gridDecalShow)
end

function ManorBuildModule:DestroyGridDecal()
	self.model:DestroyGridDecal()
end
--endregion

--region 高亮、连线特效
---@public
---高亮特效
function ManorBuildModule:ChangeHighLight(type, layer, index, highLightType)
	if highLightType then
		local config = Game.TableData.GetManorHighLightEffectDataRow(highLightType)
		if not config then
			Log.ErrorFormat("ChangeHighLight error, highLightType = %s", highLightType)
			return
		end

		self:GetManorBuildingInstance():SetComponentISMCustomDataValue(type, layer, index, self.model.highLightCustomData[config.ID])
	else
		self:GetManorBuildingInstance():SetComponentISMCustomDataValue(type, layer, index, self.model.highLightCustomDataEmpty)
	end
end

function ManorBuildModule:ShowLineEffect()
	local lineEffectEntity = self.model:GetLineEffectEntity()
	lineEffectEntity:SetVisible(true)
end

function ManorBuildModule:HideLineEffect()
	local lineEffectEntity = self.model:GetLineEffectEntity()
	lineEffectEntity:SetVisible(false)
end

function ManorBuildModule:UpdateLineEffectPlayerPosAndYaw(x, y, z)
	if not self:IsInBuild() then
		return
	end

	local playerRoa = Game.me:GetRotation()
	local lineEffectEntity = self.model:GetLineEffectEntity(true)
	if lineEffectEntity then
		lineEffectEntity:SetEffectStartLocation(x, y, z, playerRoa.Yaw)
	end
end

function ManorBuildModule:SetLineEffectBuildPos(x, y, z)
	local lineEffectEntity = self.model:GetLineEffectEntity()
	lineEffectEntity:SetEffectEndLocation(x, y, z)
end

function ManorBuildModule:SetLineEffectCanBuild(canBuild)
	local lineEffectEntity = self.model:GetLineEffectEntity()
	if self.model.lineEffectBlue ~= canBuild then
		self.model.lineEffectBlue = canBuild
		lineEffectEntity:SetEffectColorBlue(self.model.lineEffectBlue)
	end
end

---@public
---设置地面网格高度
function ManorBuildModule:UpdateGridDecalHeight()
	if not self:IsInBuild() then
		return
	end

	if self.model.gridDecalEntity then
		self.model.gridDecalEntity:SetHeight(self.model.curMainPlayerLayerHeight+0.05)
	end
end

---@public
---设置溶解材质参数
function ManorBuildModule:UpdateCameraDissolveParameter(height)
	if self.model.cameraAssetID then
		LuaScriptAPI.KismetMaterial_SetScalarParameterValue(
			Game.me.CharacterID,
			self.model.cameraAssetID,
			"SeeThroughFloorHeight",
			height
		)
	end
end
--endregion

--region 树木
-- 获取所有树的位置信息
function ManorBuildModule:GetAllAliveCuttableTreePos()
	return self.model.allAliveCuttableTreePos
end

-- 树被砍倒时更新数据
function ManorBuildModule:OnCuttableTreeDestoyed(InsID)
	self.model:OnCuttableTreeDestoyed(InsID)
end
--endregion

--region 可破坏障碍物
function ManorBuildModule:onCommonInteractorDestroy(instID)
	self.model:OnBarrieDestroyed(instID)
end
--endregion

return ManorBuildModule