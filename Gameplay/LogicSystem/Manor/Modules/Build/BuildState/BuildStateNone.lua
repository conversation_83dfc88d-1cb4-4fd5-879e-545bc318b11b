local BuildStateBase = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildState.BuildStateBase")
local BuildStateNone = DefineClass("BuildStateNone", BuildStateBase)
BuildStateNone.StateID = Enum.EBuildState.None

function BuildStateNone:ctor()
end

function BuildStateNone:OnEnter()
	Game.ManorSystem.ManorBuildModule:EndRotateControl()
	Game.ManorSystem.ManorBuildModule:DestroyPlaceNode()
end

function BuildStateNone:OnLeave()
end

return BuildStateNone