local BuildStateBase = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildState.BuildStateBase")
local BuildStateItemChange = DefineClass("BuildStateItemChange", BuildStateBase)
BuildStateItemChange.StateID = Enum.EBuildState.ChangeFurniture

function BuildStateItemChange:ctor()
end

function BuildStateItemChange:OnEnter(selectNode)
	self.selectNode = selectNode
	Game.ManorSystem.ManorBuildModule:CloneFurnitureItem()
	Game.ManorSystem.ManorBuildModule:ShowLineEffect()
	Game.ManorSystem.ManorBuildModule:SetFloorSmallGridVisible(true)

	if Game.CameraManager then
		Game.CameraManager:EnableBuildCamera(true)
	end

	self.selectNode:MoveFurniture(true)

	Game.ManorSystem.ManorBuildModule:RefreshSelectNodeByNode(nil)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_BUILD_PLACE_NODE_CHANGE, true)
end

function BuildStateItemChange:OnLeave()
	Game.ManorSystem.ManorBuildModule:DestroyPlaceNode()
	Game.ManorSystem.ManorBuildModule:HideLineEffect()
	Game.ManorSystem.ManorBuildModule:SetFloorSmallGridVisible(false)

	if Game.CameraManager then
		Game.CameraManager:EnableBuildCamera(false)
	end

	
	self.selectNode:MoveFurniture(false)

	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_BUILD_PLACE_NODE_CHANGE, false)
end

return BuildStateItemChange