local BuildGridBase = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildGrid.BuildGridBase")
---@class BuildGridComponent: BuildGridBase
local BuildGridComponent = DefineClass("BuildGridComponent", BuildGridBase)

function BuildGridComponent:ctor()
	---@type table<number, number>
	self.treeMap = {}
	---@type table<number, number>
	self.barrierMap = {}
	self:initGridBuildMaxIndex()
end

function BuildGridComponent:dtor()
	self.treeMap = nil
	self.barrierMap = nil
end

function BuildGridComponent:initGridBuildMaxIndex()
	local gridXMax, gridYMax = self.GridXMax, self.GridYMax
	self.gridBuildMaxIndexMap = {
		[Enum.BuildItemGroup.Floor] = gridXMax * gridYMax,
		[Enum.BuildItemGroup.Wall] = (gridXMax*(gridYMax+1)) + (gridYMax*(gridXMax+1)),
		[Enum.BuildItemGroup.Pillar] = (gridXMax+1)*(gridYMax+1),
		[Enum.BuildItemGroup.Stair] = gridXMax * gridYMax,
	}
end

function BuildGridComponent:GetGridBuildMaxIndex(subGroup)
	return self.gridBuildMaxIndexMap[subGroup] or 0
end

---@public
function BuildGridComponent:AddTree(posX, posY)
	local floorIndex = self:WorldPos2GridIndex(posX, posY)
	if not self.treeMap[floorIndex] then
		self.treeMap[floorIndex] = 0
	end
	self.treeMap[floorIndex] = self.treeMap[floorIndex] + 1
end

---@public
function BuildGridComponent:RemoveTree(posX, posY)
	local floorIndex = self:WorldPos2GridIndex(posX, posY)
	self.treeMap[floorIndex] = self.treeMap[floorIndex] - 1
end

---下标是否有树
---@public
function BuildGridComponent:IsIndexHasTree(floorIndex)
	return self.treeMap[floorIndex] and self.treeMap[floorIndex] > 0
end

function BuildGridComponent:processBarrier(posX, posY, sizeX, sizeY, val)
	local _, leftBottomX, leftBottomY = self:WorldPos2GridIndex(posX - sizeX, posY - sizeY)
	local _, rightUpX, rightUpY = self:WorldPos2GridIndex(posX + sizeX, posY + sizeY)
	for i = leftBottomX, rightUpX do
		for j = leftBottomY, rightUpY do
			local idx = i + j * self.GridXMax
			self.barrierMap[idx] = val
		end
	end
end

--region Barrier
function BuildGridComponent:AddBarrier(posX, posY, sizeX, sizeY)
	self:processBarrier(posX, posY, sizeX, sizeY, 1)
end

function BuildGridComponent:RemoveBarrier(posX, posY, sizeX, sizeY)
	self:processBarrier(posX, posY, sizeX, sizeY, 0)
end

function BuildGridComponent:IsIndexHasBarrier(floorIndex)
	return self.barrierMap[floorIndex] and self.barrierMap[floorIndex] > 0
end
--endregion

return BuildGridComponent