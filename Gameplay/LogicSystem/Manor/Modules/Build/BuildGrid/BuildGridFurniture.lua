local BuildGridBase = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.BuildGrid.BuildGridBase")
---@class BuildGridFurniture: BuildGridBase
local BuildGridFurniture = DefineClass("BuildGridFurniture", BuildGridBase)

function BuildGridFurniture:ctor()
	self.furnitureIndexMap = {}
end

function BuildGridFurniture:dtor()
	self.furnitureIndexMap = nil
end

function BuildGridFurniture:GetFurnitureIndex(x, y)
	return x + self.GridXMax * y
end

---@param furniture LocalBuildNodeRuntimeFurniture
function BuildGridFurniture:OnFurnitureAdded(furniture)
	local layer = furniture.layer
	local idxMap = self.furnitureIndexMap[layer] 
	if idxMap == nil then
		idxMap = {}
		self.furnitureIndexMap[layer] = idxMap
	end
	-- 由于一层的家具不能叠加，因此这里只用一个index作为索引
	local minGridIndex, maxGridIndex = furniture.minGridIndex, furniture.maxGridIndex
	local idx
	for x = minGridIndex.X, maxGridIndex.X-1 do
		for y = minGridIndex.Y, maxGridIndex.Y-1 do
			idx = self:GetFurnitureIndex(x, y)
			idxMap[idx] = true
		end
	end
end

---@param furniture LocalBuildNodeRuntimeFurniture
function BuildGridFurniture:OnFurnitureRemoved(furniture)
	local layer = furniture.layer
	local idxMap = self.furnitureIndexMap[layer]
	if idxMap == nil then return end
	local minGridIndex, maxGridIndex = furniture.minGridIndex, furniture.maxGridIndex
	local idx
	for x = minGridIndex.X, maxGridIndex.X-1 do
		for y = minGridIndex.Y, maxGridIndex.Y-1 do
			idx = self:GetFurnitureIndex(x, y)
			idxMap[idx] = nil
		end
	end
end

function BuildGridFurniture:IsIndexHasFurniture(layer, index)
	local idxMap = self.furnitureIndexMap[layer]
	return idxMap and idxMap[index]
end

function BuildGridFurniture:CheckHasFurniture(layer, minGridIndex, maxGridIndex)
	return self:CheckHasFurnitureByGridIndex(layer, minGridIndex.X, minGridIndex.Y, maxGridIndex.X, maxGridIndex.Y)
end

function BuildGridFurniture:CheckHasFurnitureByGridIndex(layer, minGridIndexX, minGridIndexY, maxGridIndexX, maxGridIndexY)
	local idx
	for x = minGridIndexX, maxGridIndexX - 1 do
		for y = minGridIndexY, maxGridIndexY - 1 do
			idx = self:GetFurnitureIndex(x, y)
			if self:IsIndexHasFurniture(layer, idx) then
				return true
			end
		end
	end
	return false
end

return BuildGridFurniture