local HomeConst = kg_require("Shared.Const.HomeConst")
---@class ManorWorkshopModule
---@field sender ManorSender
local ManorWorkshopModule = DefineClass("ManorWorkshopModule")

---@private
function ManorWorkshopModule:Init()
	---@type ManorWorkshopModel
	self.model = kg_require("Gameplay.LogicSystem.Manor.Modules.Workshop.ManorWorkshopModel").new(false, false)
	---@type number
	self.productionTimer = nil
end

---@private
function ManorWorkshopModule:UnInit()
end

function ManorWorkshopModule:EnterManorLevel()
	self:ReqGetWorkshopStatus()
	self:ReqWorkshopEmployeeList()
	self:RefreshWorkshopLevelInfo()
	self:StartProductionTick()
end

function ManorWorkshopModule:LeaveManorLevel()
	self:StopProductionTick()
end

function ManorWorkshopModule:StartProductionTick()
	self:StopProductionTick()
	self.productionTimer = Game.TimerManager:CreateTimerAndStart(function()
		self:ProductionTick()
	end, 500, -1, nil, "ProductionTickTimer")
end

function ManorWorkshopModule:StopProductionTick()
	if self.productionTimer then
		Game.TimerManager:StopTimerAndKill(self.productionTimer)
		self.productionTimer = nil
	end
end

function ManorWorkshopModule:RefreshWorkshopLevelInfo()
	-- todo: 工坊，等级逻辑修改, 服务端同步
	self.model.workshopLevelInfoDict = {}
	local homeLevel = Game.me.HomelandLevel or 1

	---@type _ManorWorkshopDataRow[]
	local allData = Game.TableData.Get_workshopLevelMapByType()
	for workshopType, furnitureIds in ksbcpairs(allData) do
		self.model.workshopLevelInfoDict[workshopType] = {currentLevel = 0}
		local workshopLevelInfo = self.model.workshopLevelInfoDict[workshopType]
		for _, furnitureId in ksbcipairs(furnitureIds) do
			local levelUpData = Game.TableData.GetWorkshopLevelUpDataRow(furnitureId)
			local levelLimit = levelUpData and levelUpData.roleLevelLimit or 1
			if homeLevel >= levelLimit and levelLimit > workshopLevelInfo.currentLevel then
				workshopLevelInfo.currentLevel = levelLimit
				workshopLevelInfo.buildingId = furnitureId
			end
		end
	end
end

--- 客户端自己维护了生产进度，UI只需要读取数据，服务端数据同步时会覆盖客户端数据
function ManorWorkshopModule:ProductionTick()
	local nowTime = _G._now(1)
	for _, workshopStatus in ipairs(self.model.workshopStatusList) do
		for index, productData in ipairs(workshopStatus.productionQueue) do
			if productData.status == 1 then
				-- 等待中
				if not productData.startTime then
					local lastProduction = workshopStatus.productionQueue[index - 1]
					if lastProduction then
						productData.startTime = lastProduction.endTime
					end
					local produceConfig = Game.TableData.GetWorkshopItemProduceDataRow(productData.recipeID)
					productData.singleProduceTime = produceConfig and produceConfig.ProduceTime or 0
					productData.endTime = productData.startTime + productData.singleProduceTime * productData.num
				end
				if nowTime >= productData.startTime then
					productData.status = 2
				end
			elseif productData.status == 2 then
				-- 生产中
				if productData.endTime and nowTime > productData.endTime then
					productData.status = 3
				end

				if not productData.singleProduceTime then
					local produceConfig = Game.TableData.GetWorkshopItemProduceDataRow(productData.recipeID)
					productData.singleProduceTime = produceConfig and produceConfig.ProduceTime or 0
				end

				-- 计算进度
				productData.finishedNum = math.floor((nowTime - productData.singleProduceTime) / productData.singleProduceTime)
				productData.percentage = (nowTime - productData.startTime) / (productData.endTime - productData.startTime)
				productData.percentage = math.min(productData.percentage, 1)
			elseif productData.status == 3 then
				-- 生产完成
			end
		end
	end
end

function ManorWorkshopModule:GetWorkshopStatusList()
	return self.model.workshopStatusList
end

---@return WORKSHOP_STATUS
function ManorWorkshopModule:GetWorkshopStatus(buildingId)
	return self.model.workshopStatusList[buildingId]
end

---@public method 获取工坊等级
---@param buildingId number 工坊ID
---@return number 工坊等级
function ManorWorkshopModule:GetWorkshopLevel(buildingId)
	for _, levelInfo in pairs(self.workshopLevelInfoDict) do
		if levelInfo.buildingId == buildingId then
			return levelInfo.currentLevel
		end
	end
end

---@return WORKSHOP_PRODUCE_ITEM
function ManorWorkshopModule:GetProductionStatus(buildingId, recipeId)
	local workshopStatus = self:GetWorkshopStatus(buildingId)
	if not workshopStatus then
		return nil
	end
	for _, productData in ipairs(workshopStatus.ProductionQueue) do
		if productData.RecipeID == recipeId then
			return productData
		end
	end
end

function ManorWorkshopModule:GetMaxLevelWorkshopId(workshopType)
	-- UI只显示已建造的最高
	local levelInfo = self.model.workshopLevelInfoDict[workshopType]
	return levelInfo and levelInfo.buildingId or nil
end

function ManorWorkshopModule:GetEmployeeList(buildingId)
	if buildingId then
		local employeeList = {}
		for _, employee in pairs(self.model.employeeList) do
			if employee.buildingID == buildingId then
				table.insert(employeeList, employee)
			end
		end
		return employeeList
	end
	return self.model.employeeList
end

function ManorWorkshopModule:GetEmployeeByUId(UId)
	return self.model.employeeList[UId]
end

function ManorWorkshopModule:OnFurnitureAdded()
	self:ReqGetWorkshopStatus()
end

function ManorWorkshopModule:GetAllRecipeDataByWorkshopType(workshopTypeId)
	local workshopLevel = self.model.workshopLevelInfoDict[workshopTypeId] or 1
	-- todo: 工坊，测试数据
	return {
		[1] = {
			recipeId = 101,
			locked = false,
		},
		[2] = {
			recipeId = 102,
			locked = true,
		},
		[3] = {
			recipeId = 103,
			locked = false,
		}
	}
end

--region Req and Ret
function ManorWorkshopModule:ReqGetWorkshopStatus()
	self.sender:ReqGetWorkshopStatus()
end

---@param newWorkshopInfo BUILDING_INFO
function ManorWorkshopModule:ReqWorkshopUpgrade(newWorkshopInfo)
	self.sender:ReqWorkshopUpgrade(newWorkshopInfo)
end

function ManorWorkshopModule:ReqBuyWorkshop(Id)
	self.sender:ReqBuyWorkshop(Id)
end

function ManorWorkshopModule:ReqWorkshopEmployeeList()
	self.sender:ReqWorkshopEmployeeList()
end

function ManorWorkshopModule:ReqAssignWorkshop(uid, buildingId)
	local status = self:GetWorkshopStatus(buildingId)

	local workshopLevelUpConfig = Game.TableData.GetWorkshopLevelUpDataRow(status.furnitureID)
	if not workshopLevelUpConfig then
		return
	end
	
	-- 找空闲的位置
	for index = 1, workshopLevelUpConfig.jobLimit do
		if not status.employees[index] then
			self.sender:ReqAssignWorkshop(uid, buildingId, index)
			return
		end
	end
	
	-- 如果找不到空闲位置，替换第一个
	self.sender:ReqAssignWorkshop(uid, buildingId, 1)
end

function ManorWorkshopModule:ReqRemoveWorkshopEmployee(Id)
	self.sender:ReqRemoveWorkshopEmployee(Id)
end

function ManorWorkshopModule:ReqWorkshopAddProductionQueue(buildingId, recipeID, num)
	self.sender:ReqWorkshopAddProductionQueue(buildingId, recipeID, num)
end

function ManorWorkshopModule:ReqWorkshopRemoveProductionQueue(workshopID, index)
	self.sender:ReqWorkshopRemoveProductionQueue(workshopID, index)
end

function ManorWorkshopModule:ReqWorkshopHarvest(workshopID, index)
	self.sender:ReqWorkshopHarvest(workshopID, index)
end

---@param workshopList WORKSHOP_STATUS[]
function ManorWorkshopModule:RetGetWorkshopStatus(workshopList)
	self.model.workshopStatusList = workshopList
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_WORKSHOP_LIST_UPDATE)
end

---@param newWorkshopInfo BUILDING_INFO
function ManorWorkshopModule:RetWorkshopUpgrade(newWorkshopInfo)
end

function ManorWorkshopModule:RetBuyWorkshop(resultCode)
	if resultCode ~= HomeConst.HOME_ERRCODE.SUCC and resultCode ~= HomeConst.HOME_ERRCODE.HOME_MONEY_NOT_ENOUGH then
		Log.ErrorFormat("RetBuyWorkshop failed Code=%s", resultCode)
	end
end

function ManorWorkshopModule:RetWorkshopEmployeeList(employeeList)
	self.model.employeeList = employeeList
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_WORKSHOP_EMPLOYEE_UPDATE)
end

function ManorWorkshopModule:RetAssignWorkshop(resultCode, uid, buildingId, index)
	if resultCode ~= HomeConst.HOME_ERRCODE.SUCC then
		return
	end
	
	self.model.employeeList[uid].buildingID = buildingId
	local status = self:GetWorkshopStatus(buildingId)
	local lastEmployee = status.employees[index]
	if lastEmployee then
		self.model.employeeList[lastEmployee].buildingID = 0
	end
	status.employees[index] = uid
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_WORKSHOP_EMPLOYEE_UPDATE)
end

function ManorWorkshopModule:RetRemoveWorkshopEmployee(resultCode, uid)
	if resultCode ~= HomeConst.HOME_ERRCODE.SUCC then
		return
	end

	local employee = self.model.employeeList[uid]
	local status = self:GetWorkshopStatus(employee.buildingID)
	for index, _uid in ipairs(status.employees) do
		if _uid == uid then
			status.employees[index] = nil
			break
		end
	end
	employee.buildingID = 0
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_WORKSHOP_EMPLOYEE_UPDATE)
end

function ManorWorkshopModule:RetWorkshopAddProductionQueue(resultCode, buildingId, recipeId, workshopStatus)
	if resultCode ~= HomeConst.HOME_ERRCODE.SUCC then
		return
	end
	self.model.workshopStatusList[buildingId] = workshopStatus
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_WORKSHOP_PRODUCTION_UPDATE)
end

function ManorWorkshopModule:RetWorkshopRemoveProductionQueue(resultCode, buildingId, workshopStatus)
	if resultCode ~= HomeConst.HOME_ERRCODE.SUCC then
		return
	end
	self.model.workshopStatusList[buildingId] = workshopStatus
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_WORKSHOP_PRODUCTION_UPDATE)
end

function ManorWorkshopModule:RetWorkshopHarvest(resultCode, buildingId, workshopStatus)
	if resultCode ~= HomeConst.HOME_ERRCODE.SUCC then
		return
	end
	self.model.workshopStatusList[buildingId] = workshopStatus
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_WORKSHOP_PRODUCTION_UPDATE)
end
--endregion

return ManorWorkshopModule