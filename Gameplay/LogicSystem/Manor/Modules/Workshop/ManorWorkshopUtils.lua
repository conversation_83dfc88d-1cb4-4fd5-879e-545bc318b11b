local ManorWorkshopUtils = {}

function ManorWorkshopUtils.GetEmployeeProductivityBoost(employeeIds)
	local allProductivity = 0
	local factorA = 1000
	local factorB = 0.3
	for _, employeeId in ipairs(employeeIds) do
		local employeeConfig = Game.TableData.GetWorkshopEmployeeDataRow(employeeId)
		if employeeConfig then
			allProductivity = allProductivity + employeeConfig.Productivity
		end
	end
	return (allProductivity / factorA) ^ factorB
end

function ManorWorkshopUtils.GetEmployeeTimeBoost(employeeIds)
	local productivityBoost = ManorWorkshopUtils.GetEmployeeProductivityBoost(employeeIds)
	if productivityBoost ~= 0 then
		return math.max(0, 1 - (1 / productivityBoost))
	else
		return 0
	end
end

function ManorWorkshopUtils.GetCountDownTimeFormatString(milliseconds)
	milliseconds = math.floor(milliseconds)

	local hour = milliseconds // 3600000
	local min = (milliseconds%3600000) // 60000

	local timeString = string.format("%02d:%02d", hour, min)
	return timeString
end

return ManorWorkshopUtils