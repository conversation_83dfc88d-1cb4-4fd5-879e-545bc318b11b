local HomeConst = kg_require("Shared.Const.HomeConst")
local ManorWorkshopUtils = kg_require("Gameplay.LogicSystem.Manor.Modules.Workshop.ManorWorkshopUtils")
local FurniturePartBase = kg_require("Gameplay.LogicSystem.Manor.Modules.Furniture.FurniturePartBase")
---@class WorkshopPartCommon : FurniturePartBase
local WorkshopPartCommon = DefineClass("WorkshopPartCommon", FurniturePartBase)

function WorkshopPartCommon:ctor()
end

---模型卸载
function WorkshopPartCommon:UnloadModel()
	Game.HUDInteractManager.DestroyInteractorTask(self.furnitureEntityID)
	self.furnitureEntityID = nil
	self.belongBuildingId = nil
end

---进入交互
function WorkshopPartCommon:OnEnterBehaviorTrigger()
	local status = Game.ManorSystem.ManorWorkshopModule:GetWorkshopStatus(self.belongBuildingId)
	if not status then return end
	local type, _ = ManorWorkshopUtils.GetWorkshopTypeAndLevel(status.furnitureID)
	local typeConfig = Game.TableData.GetWorkShopDataRow(type)
	
	Game.HUDInteractManager.BuildInteractorTask(self.furnitureEntityID, { 1429 }, function()
		if not Game.NewUIManager:IsShow(UIPanelConfig.WorkShop_Panel) then
			Game.NewUIManager:OpenPanel(UIPanelConfig.WorkShop_Panel, self.belongBuildingId)
		end
	end, {[1] = {extraTextList = {typeConfig.Name}}})
end

---离开交互
function WorkshopPartCommon:OnLeaveBehaviorTrigger()
	Game.HUDInteractManager.DestroyInteractorTask(self.furnitureEntityID)
end

--- 檢查工坊是否可刪除
function WorkshopPartCommon:CheckCanDeleteOnBuild(bNotify)
	if not self.belongBuildingId then
		return true
	end

	local workshopStatus = Game.ManorSystem.ManorWorkshopModule:GetWorkshopStatus(self.belongBuildingId)
	for index = 1, HomeConst.SINGLE_WORKSHOP_MAX_PRODUCTION do
		if workshopStatus.productionQueue[index] then
			if bNotify then
				Game.ReminderManager:AddReminderById(Enum.EReminderTextData.WORKSHOP_PRODUCING_CANNOT_RECYCLED)
			end
			return false
		end
	end
	 
	return true
end

return WorkshopPartCommon