local worldConst = kg_require("Shared.Const.WorldConst")
local HomeConst = kg_require("Shared.Const.HomeConst")
local bit = kg_require("Framework.Utils.bit")
local bit_band = bit.band
local bit_lshift = bit.lshift

---@class ManorSystem:SystemBase
---@field ManorBuildModule ManorBuildModule
---@field ManorPCGModule ManorPCGModule
---@field ManorBuildStoreModule ManorBuildStoreModule
---@field ManorInfoModule ManorInfoModule
---@field ManorWorkshopModule ManorWorkshopModule
---@field ManorVFXModule ManorVFXModule
local ManorSystem = DefineClass("ManorSystem",SystemBase)
local ManorModuleConfig = kg_require("Gameplay.LogicSystem.Manor.Main.ManorModuleConfig")

---@private
function ManorSystem:onCtor()
    self.model = nil
    self.sender = nil
end

---@private
function ManorSystem:onInit()
    ---@type ManorModel
    self.model = kg_require("Gameplay.LogicSystem.Manor.Main.ManorModel").new(false, false)
    ---@type ManorSender
    self.sender = kg_require("Gameplay.LogicSystem.Manor.Main.ManorSender").new()

	Game.EventSystem:AddListener(_G.EEventTypes.LEVEL_ON_LEVEL_LOADED, self, self.OnLevelLoaded)
	Game.EventSystem:AddListener(_G.EEventTypes.LEVEL_ON_LEVEL_LOAD_START, self, self.OnLevelLoadedStart)

	self:InitModules()
end

---@private
function ManorSystem:onUnInit()
	self:UnInitModules()
end

---@private
function ManorSystem:InitModules()
	for name, path in pairs(ManorModuleConfig.ModuleConfig) do
		local module = kg_require(path).new()
		module.sender = self.sender
		module:Init()
		self[name] = module
	end
end

---@private
function ManorSystem:UnInitModules()
	for name, _ in pairs(ManorModuleConfig.ModuleConfig) do
		self[name]:UnInit()
		self[name] = nil
	end
end

function ManorSystem:OnLevelLoadedStart(levelMapData)
	if levelMapData.Type ~= worldConst.WORLD_TYPE.HOMELAND then
		self:LeaveManorLevel(levelMapData)
		return
	end

	if levelMapData.Type ~= self.model.CurLevelType then
		self:LeaveManorLevel(levelMapData)
		return
	end
end

function ManorSystem:OnLevelLoaded(levelMapData)
	if levelMapData.Type ~= worldConst.WORLD_TYPE.HOMELAND then
		self:LeaveManorLevel(levelMapData)
		return
	end
	
	self:EnterManorLevel(levelMapData)
end

function ManorSystem:EnterManorLevel(levelMapData)
	if self.model.InitManor then
		return
	end
	
	self.model.InitManor = true
	self.model.CurLevelType = levelMapData.Type

	for name, _ in pairs(ManorModuleConfig.ModuleConfig) do
		local module = self[name]
		if module.EnterManorLevel then
			module:EnterManorLevel(levelMapData)
		end
	end
end

function ManorSystem:LeaveManorLevel(levelMapData)
	if not self.model.InitManor then
		return
	end

	self.model.InitManor = false
	self.model.CurLevelType = nil

	for name, _ in pairs(ManorModuleConfig.ModuleConfig) do
		local module = self[name]
		if module.LeaveManorLevel then
			module:LeaveManorLevel(levelMapData)
		end
	end
end

---是否在家园
---@public
function ManorSystem:IsInManorSpace()
    local localSpace = Game.NetworkManager.GetLocalSpace()
	if not localSpace then
		return false
	end
	
    return localSpace.WorldType == worldConst.WORLD_TYPE.HOMELAND
end

---@public method 判断玩家是否在自己的家园里
function ManorSystem:IsInSelfManorSpace()
	return Game.me.IsInSelfHome
end

---@public method 判断玩家是否解锁了家园
function ManorSystem:IsUnlockManor()
	return bit_band(Game.me.HomelandUnlockType, bit_lshift(1, HomeConst.HOME_TYPE.HOME_MANOR)) ~= 0
end

---@public method 请求进入家园
function ManorSystem:ReqEnterManor(targetID)
	if self:IsUnlockManor() then
		self.sender:ReqEnterHomePlane(targetID, HomeConst.HOME_TYPE.HOME_MANOR)
	else
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.HOMEMSG_NO_HOME)
	end
end

--region 协议API
---同步仓库信息
function ManorSystem:OnMsgHomeFurnitureSync(StoreFurnitureDict)
	self.ManorBuildStoreModule:OnMsgHomeFurnitureSync(StoreFurnitureDict)
end
--endregion

function ManorSystem:IsCuttableTreeLiveInScene()
	local trees = self.ManorBuildModule:GetAllAliveCuttableTreePos()
	return trees and next(trees)
end

function ManorSystem:IsPlayerInBuildArea()
	return self.ManorBuildModule:IsPlayerInBuildArea()
end

function ManorSystem:OnHomeUpgradeSucc()
	self.ManorBuildModule:OnHomeUpgradeSucc()
	self.ManorVFXModule:OnHomeUpgradeSucc()
end

return ManorSystem