---@class ManorSender:SystemSenderBase
local ManorSender = DefineClass("ManorSender",SystemSenderBase)

---请求离开家园
function ManorSender:ReqLeaveHomePlane()
    Game.me.remote.ReqLeaveHomePlane()
end

--请求进入家园
function ManorSender:ReqEnterHomePlane(TargetAvatarID, HomeType)
    Game.me.remote.ReqEnterHomePlane(TargetAvatarID, HomeType)
end

--region 建造相关
---请求更新组件
function ManorSender:ReqUpdateBuildingFrame(buildingFrameList)
	Game.me.remote.ReqUpdateBuildingFrame(buildingFrameList)
end

---请求添加组件
function ManorSender:ReqAddBuilding(furnitureInfo)
	Game.me.remote.ReqAddBuilding(furnitureInfo)
end

---请求删除组件
function ManorSender:ReqDelBuilding(buildingID)
	Game.me.remote.ReqDelBuilding(buildingID)
end

---请求清空组件
function ManorSender:ReqClearBuilding()
	Game.me.remote.ReqClearBuilding()
end

---请求更新组件
function ManorSender:ReqUpdateBuilding(furnitureInfo)
	Game.me.remote.ReqUpdateBuilding(furnitureInfo)
end

---请求设置建筑全量数据
function ManorSender:ReqSetBuildingFrame(buildFrameInfo)
	Game.me.remote.ReqSetBuildingFrame(buildFrameInfo)
end

---请求解锁家具
function ManorSender:ReqUnlockFurniture(furnitureID)
	Game.me.remote.ReqUnlockFurniture(furnitureID)
end

---请求购买家具
function ManorSender:ReqBuyFurniture(furnitureID, count)
	Game.me.remote.ReqBuyFurniture(furnitureID, count)
end

---请求出售家具
function ManorSender:ReqSellFurniture(furnitureID, count)
	Game.me.remote.ReqSellFurniture(furnitureID, count)
end

---请求升级家园
function ManorSender:ReqHomeUpgrade()
	Game.me.remote.ReqHomeUpgrade()
end

---请求修改家园名称
function ManorSender:ReqModifyHomeName(name)
	Game.me.remote.ReqModifyHomeName(name)
end

---请求修改家园简介
function ManorSender:ReqModifyHomeIntroduction(introduction)
	Game.me.remote.ReqModifyHomeIntroduction(introduction)
end

---请求查询目标玩家家园是否解锁
function ManorSender:ReqIsHomeUnlock()
	Game.me.remote.ReqIsHomeUnlock()
end

---请求查询所有好友的家园信息
function ManorSender:ReqFriendsHomeInfo()
	Game.me.remote.ReqFriendsHomeInfo()
end

function ManorSender:ReqUpdateFurnitureTransform(transformDict)
	Game.me.remote.ReqUpdateFurnitureTransform(transformDict)
end

function ManorSender:ReqUpdateFrameTransform(transformList)
	Game.me.remote.ReqUpdateFrameTransform(transformList)
end
--endregion

return ManorSender