local CommonInteractorEventUtils = DefineClass("CommonInteractorEventUtils")

function CommonInteractorEventUtils.Init()
end

function CommonInteractorEventUtils.Uninit()
end

--region event check functions
function CommonInteractorEventUtils.CheckEventAlwaysFalse(EventParams, Context)
    -- 总是依赖后续事件监听
    return false
end

-- 检查event是否已经完成functions
CommonInteractorEventUtils.CheckEventFunctions = {
    [Enum.CommonInteractorEventType.INTERACT_START] = CommonInteractorEventUtils.CheckEventAlwaysFalse,
    [Enum.CommonInteractorEventType.INTERACT_INTERRUPT] = CommonInteractorEventUtils.CheckEventAlwaysFalse,
    [Enum.CommonInteractorEventType.INTERACT_FINISHED] = CommonInteractorEventUtils.CheckEventAlwaysFalse,
    [Enum.CommonInteractorEventType.ENTER_OR_LEAVE_RANGE] = CommonInteractorEventUtils.CheckEventAlwaysFalse,
    [Enum.CommonInteractorEventType.ENTITY_DEAD_WITH_DESIGN_TAG] = CommonInteractorEventUtils.CheckEventAlwaysFalse,
    [Enum.CommonInteractorEventType.MIRROR_REFLECT_LIGHT_REACH_TARGET] = CommonInteractorEventUtils.CheckEventAlwaysFalse,
}
--endregion event check functions

-- 添加进出trigger事件
function CommonInteractorEventUtils.AddTrigger(EventParams, EventID, Interactor)
    Interactor:AddTriggerObserver(EventParams, EventID)
end

function CommonInteractorEventUtils.AddRoleDeadListener(EventParams, EventID, Interactor)
    Interactor:AddRoleDeadListener()
end

-- 添加部分事件监听需要额外进行一些操作, 例如进出指定范围时, 需要动态添加Trigger(被探测的collision)
CommonInteractorEventUtils.RegisterEventFunctions = {
    [Enum.CommonInteractorEventType.ENTER_OR_LEAVE_RANGE] = CommonInteractorEventUtils.AddTrigger,
    [Enum.CommonInteractorEventType.ENTITY_DEAD_WITH_DESIGN_TAG] = CommonInteractorEventUtils.AddRoleDeadListener,
}

return CommonInteractorEventUtils
