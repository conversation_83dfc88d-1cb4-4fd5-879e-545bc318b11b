local Const = kg_require("Shared.Const")

local WorldActivityUtils = {}

-- 非凡事件追踪标签
function WorldActivityUtils.OnTrackTag(TaskID)
    Game.MapSystem:SetMapTagOuterVisible(TaskID, Enum.MapCommonTagOuterType.Red)
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EVENT_REMINDER)
end

-- 呓语之花前往
function WorldActivityUtils.OnPathingTag(TaskID)
    local tagExcelData = Game.TableData.GetMapTagDataRow(TaskID)
    local X, Y, Z, LevelId = Game.WorldDataManager:GetPositionByInsID(tagExcelData.LinkInsID, true)
    local location = FVector(X, Y, Z)

    local curAutoNavigationMap = Game.MapSystem:GetAutoNavigationInfo()
    if curAutoNavigationMap and curAutoNavigationMap.LevelId == LevelId and curAutoNavigationMap.Loc == location then
        Game.AutoNavigationSystem:StopNavigation()
        Game.MapSystem:RemoveTrace()
    else
        Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.ClickMap, LevelId, location)
        Game.MapSystem:SetTrace(TaskID, Const.TRACING_INFO_TYPE.MAP)
    end
end

return WorldActivityUtils