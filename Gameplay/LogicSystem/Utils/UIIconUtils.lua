local UIIconUtils = {}


function UIIconUtils.Init()
end

function UIIconUtils.Uninit()

end
---@param iconName string Icon名称
---@param iconType Enum.EUIIconType Icon大中小类型
function UIIconUtils.getIcon(iconName, iconType, bTexture)
	local cfg = Game.TableData.GetIconConfigDataRow(iconName)
	if not cfg then return end
	-- local codes = cfg.Code
	-- local flag = codes[1] % 1000
	-- local paths = Game.TableData.Get_Prefixs()

	if not iconType then
		iconType = Enum.EUIIconType.Default
	end

	local iconPath = ""
	if iconType == Enum.EUIIconType.Middle then
		iconPath = cfg.MiddlePath
	elseif iconType == Enum.EUIIconType.Large then
		iconPath = cfg.LargePath
	elseif iconType == Enum.EUIIconType.Small then
		iconPath = cfg.SmallPath
	end
	
	if not iconPath or iconPath == "" then
		if cfg.MiddlePath then
			iconPath = cfg.MiddlePath
		elseif cfg.LargePath then
			iconPath = cfg.LargePath
		elseif cfg.SmallPath then
			iconPath = cfg.SmallPath
		else
			iconPath = cfg.DefaultPath
		end
	end
	
	return iconPath
end

function UIIconUtils.GetIconTexture(iconName, iconType)
	if not iconName or type(iconName)~="string" then
		return nil
	else
		return UIIconUtils.getIcon(iconName, iconType, true)
	end
end


--道具Icon获取
---@param itemId int 物品ID
---@param iconType Enum.EUIIconType Icon大中小类型
---@return string Icon路径
function UIIconUtils.GetIconByItemId(itemId, iconType, bTexture)
    local iconName
    if Game.TableData.GetItemNewDataRow(itemId) then
        iconName = Game.TableData.GetItemNewDataRow(itemId).icon
    end
    if not iconName or type(iconName)~="string" then
        return nil
    else
        return UIIconUtils.getIcon(iconName, iconType, bTexture)
    end
	
end
--元素buffIcon获取
---@param effectId int 效果ID
---@param iconType Enum.EUIIconType Icon大中小类型
---@return string Icon路径
function UIIconUtils.GetIconByEleEffectId(effectId, iconType)
    local elementData = Game.TableData.GetElementEffectsDataRow(effectId)
    if elementData then
        local iconName = elementData.icon
        if not iconName or type(iconName)~="string" then
            return nil
        else
            return UIIconUtils.getIcon(iconName, iconType)
        end
    end
    return nil
end
--封印物Icon获取
---@param sealedId int 物品ID
---@param iconType Enum.EUIIconType Icon大中小类型
---@return string Icon路径
function UIIconUtils.GetIconBySealedItemId(sealedId, iconType)
    local sealedData = Game.TableData.GetSealedItemDataRow(sealedId)
    if sealedData then
        local iconName = sealedData.icon
        if not iconName or type(iconName)~="string" then
            return nil
        else
            return UIIconUtils.getIcon(iconName, iconType)
        end
    end
    return nil
end

return UIIconUtils