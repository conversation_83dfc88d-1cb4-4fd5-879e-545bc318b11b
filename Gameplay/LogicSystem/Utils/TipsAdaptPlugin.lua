---Tips通用适配工具
local TipsAdaptPlugin = DefineClass("TipsAdaptPlugin")

---@field _tipList table<tipName> 记录tip打开顺序
TipsAdaptPlugin._tipList = {}

---@field _tipMap table<tipName,widgetName> 记录tip信息
TipsAdaptPlugin._tipMap = {}

function TipsAdaptPlugin:ctor()
    self._cacheSize = nil
    self._removeTipList = {}
end

function TipsAdaptPlugin:dtor()
end

---使用示例： self:RegisterTip('DrugTips_Panel', 'SizeBox')
---@Param tipName：tip文件名
---@Param widgetName: 获取tip size的widget name
---tip OnShow的时候调用
function TipsAdaptPlugin:RegisterTip(tipName, widgetName)
    local ui = UI.GetUI(tipName)
	if not ui then return end
	local view = ui.view or ui.View
    if view and view[widgetName] then
        -- view.WidgetRoot:SetOpacity(0)
        if TipsAdaptPlugin._tipMap[tipName] then
            Log.WarningFormat('[TipsAdaptPlugin:getSize_TipAdapt] tipName:%s already exists', tipName)
        else
            table.insert(TipsAdaptPlugin._tipList, tipName)
            TipsAdaptPlugin._tipMap[tipName] = widgetName
        end
        self:UpdateTipSize(tipName, widgetName)
    else
        Log.WarningFormat('[TipsAdaptPlugin:RegisterTip] tipName:%s is not correct or not opened', tipName)
    end
end

---刷新当前tip size
function TipsAdaptPlugin:UpdateTipSize(tipName)
    local widgetName = TipsAdaptPlugin._tipMap[tipName]
    if not widgetName then
        Log.WarningFormat('[TipsAdaptPlugin:UpdateSize_TipAdapt] tipName:%s, widgetName not exits', tipName)
        return
    end
    local ui = UI.GetUI(tipName)
	if not ui then return end
	local view = ui.view or ui.View
    if view and view[widgetName] then
        self._cacheSize = import("SlateBlueprintLibrary").GetLocalSize(view[widgetName]:GetCachedGeometry())
        self:calculateTipsPos()
        self:getSize_TipAdapt(tipName, widgetName)
        self:StartTimer("END_REFRESH_TIP_POSITION", function()
            self:StopTimer("REFRESH_TIP_POSITION")
        end, 1000, 1)
    end
end

--- 等待直到拿到当前tip的size
function TipsAdaptPlugin:getSize_TipAdapt(tipName, widgetName)
    self:StartTimer("REFRESH_TIP_POSITION", 
        function()
            local ui = UI.GetUI(tipName)
			local view = ui and (ui.view or ui.View)
            if view and view[widgetName] then
                local size = import("SlateBlueprintLibrary").GetLocalSize(view[widgetName]:GetCachedGeometry())
                if self._cacheSize == nil and (size.X > 0 or size.Y > 0) then
                    self._cacheSize = size
                    self:calculateTipsPos()
                elseif self._cacheSize ~= nil and (size.X ~= self._cacheSize.X or size.Y ~= self._cacheSize.Y) then
                    self._cacheSize = size
                    self:calculateTipsPos()
                end
                self:getSize_TipAdapt(tipName, widgetName)
            else
                Log.WarningFormat('[TipsAdaptPlugin:getSize_TipAdapt] tipName:%s, widgetName:%s is not correct', tipName, widgetName)
            end
        end,
    1, 1)
end

--- 计算tips位置
function TipsAdaptPlugin:calculateTipsPos()
    if #TipsAdaptPlugin._tipList == 0 then
        return
    end

    ---记录需要移除的不合法的tip index
    self._removeTipList = {}
    ---拿到tip显示区域的中心位置width
    local center_width = 0 
    ---检查_tipList的信息是否还有效
    for index, tipName in ipairs(TipsAdaptPlugin._tipList) do
        local ui = UI.GetUI(tipName)
        local widgetName = TipsAdaptPlugin._tipMap[tipName]
		local view = ui and (ui.view or ui.View)
        if view and view[widgetName] then
            local size = import("SlateBlueprintLibrary").GetLocalSize(view[widgetName]:GetCachedGeometry())
            if size.X == 0 or size.Y == 0 then
                -- table.insert(removeTipList, index)
                Log.WarningFormat('[TipsAdaptPlugin:calculateTipsPos] tipName:%s, Unable to obtain size', tipName)
            else
                center_width = center_width + size.X/2
            end
        else
            table.insert(self._removeTipList, index)
        end
    end

    ---清除无效的注册信息
    for _, index in pairs(self._removeTipList) do
        local tipName = TipsAdaptPlugin._tipList[index]
        TipsAdaptPlugin._tipMap[tipName] = nil
        table.remove(TipsAdaptPlugin._tipList, index)
    end

    ---加上固定间距
    center_width = center_width + ((#TipsAdaptPlugin._tipList - 1) * Game.TableData.GetConstDataRow("TIPS_GAP"))/2
    
    -- --- 拿到tip显示区域的中心位置width
    -- local center_width = ((#TipsAdaptPlugin._tipList - 1) * Game.TableData.GetConstDataRow("TIPS_GAP"))/2
    -- for _, tipInfo in pairs(TipsAdaptPlugin._tipMap) do
    --     center_width = center_width + tipInfo.size.X/2
    -- end

    --- 计算tip padding偏移位置
    local temp = 0
    for index, tipName in ipairs(TipsAdaptPlugin._tipList) do
        local widgetName = TipsAdaptPlugin._tipMap[tipName]
        if widgetName then
            local ui = UI.GetUI(tipName)
            if ui then
				local view = ui.view or ui.View
                local widget = view[widgetName].Slot
                local size = import("SlateBlueprintLibrary").GetLocalSize(view[widgetName]:GetCachedGeometry())
                if  widget:IsA(import("CanvasPanelSlot")) then
                    -- local oldPos = widget:GetPosition()
                    local newPos = FVector2D(0, 0)
                    newPos.X = (center_width - size.X/2 - temp) * -1 + newPos.X
                    widget:SetPosition(newPos)
                else
                    -- local curMargin = widget.Padding
                    local newMargin = import("Margin")(0.0, 0.0)
                    newMargin.Left = (center_width - size.X/2 - temp) * -1 + newMargin.Left
                    widget:SetPadding(newMargin)
                end
                temp = temp + size.X + Game.TableData.GetConstDataRow("TIPS_GAP")
                -- view.WidgetRoot:SetOpacity(1)
            end
        else
            Log.WarningFormat('[TipsAdaptPlugin:calculateTipsPos] tipName:%s, tipInfo not exists', tipName)
        end
    end
end

---tip OnHide的时候调用
function TipsAdaptPlugin:UnregisterTip(tipName)
    local mark = nil 
    for index, Name in ipairs(TipsAdaptPlugin._tipList) do
        if Name == tipName then
            mark = index
            break
        end
    end
    table.remove(TipsAdaptPlugin._tipList, mark)
    TipsAdaptPlugin._tipMap[tipName] = nil
    self._cacheSize = nil

    self:calculateTipsPos()
end

return TipsAdaptPlugin