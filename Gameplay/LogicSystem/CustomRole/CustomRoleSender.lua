---@class CustomRoleSender:SystemSenderBase
local CustomRoleSender = DefineClass("CustomRoleSender",SystemSenderBase)

function CustomRoleSender:ReqSaveFaceData(profession, sex, faceData, fashionID)
    local Account = Game.NetworkManager.GetAccountEntity()
	if Account then
		Account:ReqSaveFaceData(profession, sex, faceData or "", fashionID or 0)
	end
end

function CustomRoleSender:ReqClearFaceData()
    local Account = Game.NetworkManager.GetAccountEntity()
	if Account then
		Account:ReqClearFaceData()
	end
end

return CustomRoleSender