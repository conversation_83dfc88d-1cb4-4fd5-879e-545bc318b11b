local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class CustomRoleTab_Item : UIListItem
---@field view CustomRoleTabBlueprint
local CustomRoleTab_Item = DefineClass("CustomRoleTab_Item", UIListItem)

CustomRoleTab_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CustomRoleTab_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CustomRoleTab_Item:InitUIData()
	--当前这个Item的索引
	self.index = nil
end

--- UI组件初始化，此处为自动生成
function CustomRoleTab_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function CustomRoleTab_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CustomRoleTab_Item:InitUIView()
end

---组件刷新统一入口
function CustomRoleTab_Item:OnRefresh(data)
	local tabConfig = Game.TableData.GetCustomRoleTabDataRow(data)
	self.view.Text_tab:SetText(tabConfig.StringValue)
end

function CustomRoleTab_Item:UpdateSelectionState(selected)
	self.userWidget:SetSelect(selected)
end

return CustomRoleTab_Item
