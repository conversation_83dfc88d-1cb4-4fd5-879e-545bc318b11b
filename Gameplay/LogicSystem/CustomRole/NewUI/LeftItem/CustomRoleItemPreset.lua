local CustomRoleItemBase = kg_require ("Gameplay.LogicSystem.CustomRole.NewUI.LeftItem.CustomRoleItemBase")
---@class CustomRoleItemPreset : UIComponent
local CustomRoleItemPreset = DefineClass("CustomRoleItemPreset", CustomRoleItemBase)

function CustomRoleItemPreset:OnCreate()
end

function CustomRoleItemPreset:OnRefresh(data)
	CustomRoleItemBase.OnRefresh(self, data)
end

return CustomRoleItemPreset
