local LeftTabWidget = kg_require("Gameplay.LogicSystem.CustomRole.NewUI.LeftTabWidget")
local RightDetailWidget = kg_require("Gameplay.LogicSystem.CustomRole.NewUI.RightDetailWidget")
local CustomRoleBtnIcon = kg_require("Gameplay.LogicSystem.CustomRole.CustomRoleBtnIcon")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local LuaFunctionLibrary = import("LuaFunctionLibrary")
local UIFunctionLibrary = import("UIFunctionLibrary")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local KismetMathLibrary = import("KismetMathLibrary")
local KismetInputLibrary = import("KismetInputLibrary")
local ESlateVisibility =  import("ESlateVisibility")
---@class CustomRole_Panel : UIPanel
---@field view CustomRoleBlueprint
local CustomRole_Panel = DefineClass("CustomRole_Panel", UIPanel)

CustomRole_Panel.eventBindMap = {
	[EEventTypesV2.ON_CUSTOM_ROLE_TEST_TEXT_CHANGE] = "OnTextTextChange",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CustomRole_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CustomRole_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CustomRole_Panel:InitUIComponent()
    ---@type UIComButton
    self.BackArrowCom = self:CreateComponent(self.view.BackArrow, UIComButton)
    ---@type LeftTabWidget
    self.WBP_LeftTabWidgetCom = self:CreateComponent(self.view.WBP_LeftTabWidget, LeftTabWidget)
    ---@type RightDetailWidget
    self.WBP_RightDetailWidgetCom = self:CreateComponent(self.view.WBP_RightDetailWidget, RightDetailWidget)
    ---@type CustomRoleBtnIcon
    self.WBP_CustomRoleBtnShareCom = self:CreateComponent(self.view.WBP_CustomRoleBtnShare, CustomRoleBtnIcon)
    ---@type CustomRoleBtnIcon
    self.WBP_CustomRoleBtnImportCom = self:CreateComponent(self.view.WBP_CustomRoleBtnImport, CustomRoleBtnIcon)
    ---@type CustomRoleBtnIcon
    self.WBP_CustomRoleBtnSceneCom = self:CreateComponent(self.view.WBP_CustomRoleBtnScene, CustomRoleBtnIcon)
    ---@type CustomRoleBtnIcon
    self.WBP_CustomRoleBtnEmptyCom = self:CreateComponent(self.view.WBP_CustomRoleBtnEmpty, CustomRoleBtnIcon)
end

---UI事件在这里注册，此处为自动生成
function CustomRole_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_CustomRoleBtnSceneCom.onClickEvent, "on_WBP_CustomRoleBtnSceneCom_ClickEvent")
    self:AddUIEvent(self.WBP_CustomRoleBtnImportCom.onClickEvent, "on_WBP_CustomRoleBtnImportCom_ClickEvent")
    self:AddUIEvent(self.WBP_CustomRoleBtnShareCom.onClickEvent, "on_WBP_CustomRoleBtnShareCom_ClickEvent")
    self:AddUIEvent(self.BackArrowCom.onClickEvent, "on_BackArrowCom_ClickEvent")
    self:AddUIEvent(self.WBP_CustomRoleBtnEmptyCom.onClickEvent, "on_WBP_CustomRoleBtnEmptyCom_ClickEvent")
    self:AddUIEvent(self.view.OnTouchStartedEvent, "on_WBP_CustomRole_TouchStartedEvent")
    self:AddUIEvent(self.view.OnTouchMovedEvent, "on_WBP_CustomRole_TouchMovedEvent")
    self:AddUIEvent(self.view.OnTouchEndedEvent, "on_WBP_CustomRole_TouchEndedEvent")
    self:AddUIEvent(self.view.OnMouseWheelEvent, "on_WBP_CustomRole_MouseWheelEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CustomRole_Panel:InitUIView()
	self.WBP_CustomRoleBtnSceneCom:Refresh()
	
	self.WBP_CustomRoleBtnEmptyCom:Hide()
	self.WBP_CustomRoleBtnImportCom:Hide()
	self.WBP_CustomRoleBtnShareCom:Hide()

	self.view.TestData:SetVisibility(ESlateVisibility.Collapsed)
end

---组件刷新统一入口
function CustomRole_Panel:Refresh(cameraBlend)
	---是否正在旋转角色
	self.isChangePlayerRotate = false
	
	self.WBP_LeftTabWidgetCom:Refresh(cameraBlend)
	self.WBP_RightDetailWidgetCom:Refresh()

	self:RefreshUIByStage()
	self:StartAutoSaveTimer()
end

function CustomRole_Panel:OnClose()
	Game.CustomRoleSystem:RefreshCameraStage(nil)
	self:StopTimer("CustomRoleAutoSave")
end

function CustomRole_Panel:RefreshUIByStage()
	self.WBP_LeftTabWidgetCom:RefreshUIByStage()
	self.WBP_RightDetailWidgetCom:RefreshUIByStage()
	
	self.curShowWidgetConfig = nil

	local _, title = Game.CustomRoleSystem:GetCurrentStageStartData()
	self.BackArrowCom:SetName(title)
	self.WBP_LeftTabWidgetCom:RefreshLeftFirstTabList()

	Game.CustomRoleSystem:CustomRoleStopAnimation()
end

---选择1级标签
function CustomRole_Panel:RefreshLeftOnTabSelectChange(index, data)
	self.WBP_RightDetailWidgetCom:RefreshButtonGray()
end

---刷新左右两侧数据ui
function CustomRole_Panel:RefreshShowWidget(tabConfig)
	self.curShowWidgetConfig = tabConfig

	self.WBP_LeftTabWidgetCom:RefreshLeftListUI(tabConfig.LeftListWidget)
	self.WBP_RightDetailWidgetCom:RefreshRightUI(tabConfig.RightDetailWidget)
end

--region 按钮点击事件
---下一阶段
function CustomRole_Panel:EnterNextStage()
	local curStageIndex = Game.CustomRoleSystem:GetCurrentStage()
	if curStageIndex < Enum.ECustomRoleStageType.MaxStage-1 then
		Game.CustomRoleSystem:NextStage()
		self:RefreshUIByStage()
	else
		self.WBP_LeftTabWidgetCom:CloseEyeFollow()
		Game.CustomRoleSystem:PlayLevelSequenceDisplay()
	end
end

---上一阶段
function CustomRole_Panel:on_BackArrowCom_ClickEvent()
	local curStageIndex = Game.CustomRoleSystem:GetCurrentStage()
	if curStageIndex == Enum.ECustomRoleStageType.Stage2 then
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CUSTOMROLE_BACK, function()
			self:EnterLastStage()
		end, nil)
		return
	end

	self:EnterLastStage()
end

function CustomRole_Panel:EnterLastStage()
	local curStageIndex = Game.CustomRoleSystem:GetCurrentStage()
	if curStageIndex > Enum.ECustomRoleStageType.Stage1 then
		Game.CustomRoleSystem:LastStage()
		self:RefreshUIByStage()
	else
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CUSTOMROLE_PANEL_CLOSE, function()
			if not Game.NewUIManager:IsOpened(UIPanelConfig.CustomRole_Panel) then
				return
			end
			
			self:CloseSelf()
			Game.CustomRoleSystem:ReqClearLastCustomRoleInfo()
			Game.GameLoopManagerV2:LoadMapAndSwitchGameStage(Game.GameLoopManagerV2.EGameStageType.CreateRole, Enum.ELevelMapData.LV_Showroom)
		end, nil)
	end
end

---环境切换
function CustomRole_Panel:on_WBP_CustomRoleBtnSceneCom_ClickEvent()
	if Game.NewUIManager:IsOpened(UIPanelConfig.CustomRoleEnvironment_Panel) then
		Game.NewUIManager:ClosePanel(UIPanelConfig.CustomRoleEnvironment_Panel)
	else
		Game.NewUIManager:OpenPanel(UIPanelConfig.CustomRoleEnvironment_Panel)
	end
end

---分享
function CustomRole_Panel:on_WBP_CustomRoleBtnShareCom_ClickEvent()
	Game.CustomRoleSystem:SaveCustomRoleDataInClient()
	local uploadStr = Game.CustomRoleSystem:GetSerializeCustomRoleFaceDataStr()
	local encryptStr = LuaFunctionLibrary.EncryptText(uploadStr)
	UIFunctionLibrary.CopyToClipBoard(encryptStr)

	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CUSTOMROLE_DATA_EXPORT_TIP)
end

---导入
function CustomRole_Panel:on_WBP_CustomRoleBtnImportCom_ClickEvent()
	--todo 废弃功能
	--Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CUSTOMROLE_CODE_IMPORT_COMFIRM,
	--	function()
	--		self:OnClickImport()
	--	end)
end

---确认导入易容码
function CustomRole_Panel:OnClickImport()
	local text = self.view.WBP_ComInputBig.EditText:GetText()
	if string.isEmpty(text) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CUSTOMROLE_IMPORT_TIPS)
		return
	end

	--临时处理，三期这个功能废弃删除
	if #text < 40 then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CUSTOMROLE_IMPORT_FAIL_TIPS)
		return
	end

	local decryptText = LuaFunctionLibrary.DecryptText(text)
	if string.isEmpty(decryptText) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CUSTOMROLE_IMPORT_FAIL_TIPS)
		return
	end

	Game.CustomRoleSystem:ImportDataFromClipBoard(decryptText)
end

function CustomRole_Panel:OnTextTextChange(name, value)
	self.view.TestData:SetText(string.format("%s = %s", name or "", value or ""))
end

--- 此处为自动生成
function CustomRole_Panel:on_WBP_CustomRoleBtnEmptyCom_ClickEvent()
end


--endregion


--region 手势控制  todo 这一坨也整理下
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent
function CustomRole_Panel:on_WBP_CustomRole_TouchStartedEvent(myGeometry, inGestureEvent)
	if self.isChangePlayerRotate then
		return WidgetBlueprintLibrary.Handled()
	end
	self.isChangePlayerRotate = true

	local screenInputManager = Game.ScreenInputManager
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inGestureEvent)
	local ScreenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inGestureEvent)
	screenInputManager.PosTimeTableAdd(
		PointerIndex + 1,
		ScreenPos,
		_G._now(),
		myGeometry,
		inGestureEvent
	)
	if screenInputManager.IsTriggle() then
		local Offset = screenInputManager.PosTimeTable[1].Pos - screenInputManager.PosTimeTable[2].Pos
		screenInputManager.StartedDis = KismetMathLibrary.Sqrt(Offset:SizeSquared())
	end
	
	return WidgetBlueprintLibrary.CaptureMouse(WidgetBlueprintLibrary.Handled(), self.userWidget)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent
function CustomRole_Panel:on_WBP_CustomRole_TouchMovedEvent(myGeometry, inGestureEvent)
	if not self.isChangePlayerRotate then
		return WidgetBlueprintLibrary.Handled()
	end

	local screenInputManager = Game.ScreenInputManager
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inGestureEvent)
	local ScreenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inGestureEvent)
	screenInputManager.UpdatePos(PointerIndex + 1, ScreenPos)

	if screenInputManager.IsTriggle() then
		--双指缩放
		local Offset = screenInputManager.PosTimeTable[1].Pos - screenInputManager.PosTimeTable[2].Pos
		local CurDis = math.sqrt(Offset:SizeSquared())
		--local Scale = CurDis - screenInputManager.StartedDis
		screenInputManager.StartedDis = CurDis
		screenInputManager.bZooming = true
		--self.NowCamInstant = self.NowCamInstant + Scale / 100
		--self.NowCamInstant = KismetMathLibrary.FClamp(self.NowCamInstant, 0, 1)
		--Log.Debug("===============self.NowCamInstant",self.NowCamInstant)
		--if slua.isValid(self.model.RoleDisplayScene) then
		--    self.model.DisplaySceneActor:UpdateCamera(self.NowCamInstant)
		--end
		return UIBase.HANDLED
	elseif screenInputManager.GetPosTimeTableLength() == 1 and screenInputManager.bZooming == true then
		--双指缩放送开一根手指
		return UIBase.HANDLED
	end

	local Delta = KismetInputLibrary.PointerEvent_GetCursorDelta(inGestureEvent)
	Game.CustomRoleSystem:RotateRoleActor(Delta.X)

	return UIBase.HANDLED
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent
function CustomRole_Panel:on_WBP_CustomRole_TouchEndedEvent(myGeometry, inGestureEvent)
	if not self.isChangePlayerRotate then
		return WidgetBlueprintLibrary.Handled()
	end
	self.isChangePlayerRotate = false

	local screenInputManager = Game.ScreenInputManager
	local PointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inGestureEvent)
	screenInputManager.PosTimeTableDel(PointerIndex + 1)
	screenInputManager.StartedDis = 0
	if screenInputManager.GetPosTimeTableLength() == 0 and screenInputManager.bZooming == true then
		screenInputManager.bZooming = false
	end
	
	return WidgetBlueprintLibrary.Handled()
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent
function CustomRole_Panel:on_WBP_CustomRole_MouseWheelEvent(myGeometry, inMouseEvent)
	local Delta = KismetInputLibrary.PointerEvent_GetWheelDelta(inMouseEvent)

	local customRoleCameraMode = Game.CameraManager:GetCameraMode(Enum.ECameraModes.CustomRole)
	if customRoleCameraMode then
		customRoleCameraMode:Zoom_Axis(Delta)
	end
end
--endregion 手势控制


function CustomRole_Panel:StartAutoSaveTimer()
	self:StartTimer("CustomRoleAutoSave", function()
		Game.CustomRoleSystem:SaveCustomRoleDataInClient()
	end, 15000, -1, nil, false)
end

function CustomRole_Panel:GetCurFirstTabID()
	return self.WBP_LeftTabWidgetCom.curFirstTabID
end

return CustomRole_Panel
