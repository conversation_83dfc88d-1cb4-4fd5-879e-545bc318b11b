local CustomRoleSwitchBtn = kg_require("Gameplay.LogicSystem.CustomRole.NewUI.LeftItem.CustomRoleSwitchBtn")
local UIComAccordionList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComAccordionList")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local CustomRoleBtnIcon = kg_require("Gameplay.LogicSystem.CustomRole.CustomRoleBtnIcon")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local ESlateVisibility =  import("ESlateVisibility")
---@class LeftTabWidget : UIComponent
---@field view LeftTabWidgetBlueprint
local LeftTabWidget = DefineClass("LeftTabWidget", UIComponent)

LeftTabWidget.eventBindMap = {
	[EEventTypesV2.ON_CUSTOM_ROLE_CLOSE_PLAIN] = "ClosePlain",
	[EEventTypesV2.ON_CUSTOM_ROLE_REFRESH_WIDGET_UI] = "OnRefreshWidgetUIByData"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function LeftTabWidget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function LeftTabWidget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function LeftTabWidget:InitUIComponent()
	--- 2、3级页签树形列表
    ---@type UIComAccordionList
    self.TabTreeListCom = self:CreateComponent(self.view.TabTreeList, UIComAccordionList)
	
    ---@type CustomRoleSwitchBtn
    self.SwitchOpenWidgetCom = self:CreateComponent(self.view.SwitchOpenWidget, CustomRoleSwitchBtn)
	
	--- 1级标签列表
    ---@type UIListView childScript: CustomRoleTab_Item
    self.WBP_ComListCom = self:CreateComponent(self.view.WBP_ComList, UIListView)
	

	--- 4级item普通列表
    ---@type UIListView childScript: CustomRoleItemNormal
    self.NormalListCom = self:CreateComponent(self.view.NormalList, UIListView)
	--- 4级item竖形列表
    ---@type UIListView childScript: CustomRoleItemS
    self.SListCom = self:CreateComponent(self.view.SList, UIListView)
	--- 4级item预设列表
    ---@type UIListView childScript: CustomRoleItemV
    self.PresetListCom = self:CreateComponent(self.view.PresetList, UIListView)
	--- 4级item横行列表
    ---@type UIListView childScript: CustomRoleItemH
    self.HListCom = self:CreateComponent(self.view.HList, UIListView)
	--- 4级item竖形列表
    ---@type UIListView childScript: CustomRoleItemPreset
    self.VListCom = self:CreateComponent(self.view.VList, UIListView)

    ---@type CustomRoleBtnIcon
    self.RawClothBtnCom = self:CreateComponent(self.view.RawClothBtn, CustomRoleBtnIcon)
    ---@type CustomRoleBtnIcon
    self.HairBtnCom = self:CreateComponent(self.view.HairBtn, CustomRoleBtnIcon)
    ---@type CustomRoleBtnIcon
    self.LookAtBtnCom = self:CreateComponent(self.view.LookAtBtn, CustomRoleBtnIcon)
end

---UI事件在这里注册，此处为自动生成
function LeftTabWidget:InitUIEvent()
    self:AddUIEvent(self.RawClothBtnCom.onClickEvent, "on_RawClothBtnCom_ClickEvent")
    self:AddUIEvent(self.HairBtnCom.onClickEvent, "on_HairBtnCom_ClickEvent")
    self:AddUIEvent(self.LookAtBtnCom.onClickEvent, "on_LookAtBtnCom_ClickEvent")
    self:AddUIEvent(self.VListCom.onItemClicked, "on_VListCom_ItemClicked")
    self:AddUIEvent(self.SListCom.onItemClicked, "on_SListCom_ItemClicked")
    self:AddUIEvent(self.PresetListCom.onItemClicked, "on_PresetListCom_ItemClicked")
    self:AddUIEvent(self.NormalListCom.onItemClicked, "on_NormalListCom_ItemClicked")
    self:AddUIEvent(self.HListCom.onItemClicked, "on_HListCom_ItemClicked")
    self:AddUIEvent(self.WBP_ComListCom.onItemSelected, "on_WBP_ComListCom_ItemSelected")
    self:AddUIEvent(self.TabTreeListCom.onItemSelected, "on_TabTreeListCom_ItemSelected")
    self:AddUIEvent(self.TabTreeListCom.onItemExpansionChanged, "on_TabTreeListCom_ItemExpansionChanged")
    self:AddUIEvent(self.SwitchOpenWidgetCom.onClickEvent, "on_SwitchOpenWidgetCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function LeftTabWidget:InitUIView()
	self.TabTreeListCom:SetAutoSelectFirst(true)
	self.TabTreeListCom:SetExclusive(true)
	
	self.SwitchOpenWidgetCom:Refresh(
		true, false, 
		Game.TableData.GetCustomRoleSettingDataRow("EASY_STRING_TEXT"),
		Game.TableData.GetCustomRoleSettingDataRow("MAJOR_STRING_TEXT")
	)
end

---组件刷新统一入口
function LeftTabWidget:Refresh(cameraBlend)
	---束发开关
	self.curHairTieOpen = false
	---素衣开关
	self.curDefaultClothesOpen = false
	---视线追踪开关
	self.curGazeOpen = false
	---是否简单模式
	self.isEasyMode = false
	
	self.curFirstTabID = nil

	self.cameraBlend = cameraBlend

	---4级item列表数据
	self.itemDataList = {}
	---当前显示的listCom
	self.curShowItemListCom = nil

	self.RawClothBtnCom:Refresh()
	self.HairBtnCom:Refresh()
	self.LookAtBtnCom:Refresh()
end


---初始化1级标签数据
function LeftTabWidget:RefreshLeftFirstTabList()
	local curStageData = Game.CustomRoleSystem:GetCurrentStageStartData()
	self.WBP_ComListCom:Refresh(curStageData)
	self.WBP_ComListCom:SetSelectedItemByIndex(1, true)
end

---一级标签选中
--- 此处为自动生成
---@param index number
---@param data table
function LeftTabWidget:on_WBP_ComListCom_ItemSelected(index, data)
	local curFirstKeyConfig = Game.TableData.GetCustomRoleTabDataRow(data)
	self.curFirstTabID = curFirstKeyConfig.Key
	Game.CustomRoleSystem:SetSelectTabFirstID(data)

	local nextTab = curFirstKeyConfig.NextTab[1]
	if self.isEasyMode and ksbcnext(curFirstKeyConfig.NextSimpleTab) then
		nextTab = curFirstKeyConfig.NextSimpleTab[1]
	end
	
	table.clear(self.itemDataList)
	self:RefreshBandedHairVisible()
	self:GetParent():RefreshLeftOnTabSelectChange(index, data)

	local nextConfig = Game.TableData.GetCustomRoleTabDataRow(nextTab)
	if nextConfig and nextConfig.ThisTabType == Enum.ECustomRoleTabLevel.TabLevel2 then
		self:RefreshLeftTreeTabList(data)
		return
	end

	self:GetParent():RefreshShowWidget(curFirstKeyConfig)
	self:RefreshCameraStage(curFirstKeyConfig)
end

--初始化2、3级标签数据
function LeftTabWidget:RefreshLeftTreeTabList(curFirstKey)
	local curFirstKeyConfig = Game.TableData.GetCustomRoleTabDataRow(curFirstKey)
	local hasSimple = true
	if ksbcnext(curFirstKeyConfig.NextSimpleTab) then
		self.SwitchOpenWidgetCom:Show()
	else
		hasSimple = false
		self.SwitchOpenWidgetCom:Hide()
	end

	local nextTabKey = (self.isEasyMode and hasSimple) and "NextSimpleTab" or "NextTab"
	local treeviewData = UITreeView.NewTreeViewData()
	for index, nextSimpleTabID in ksbcpairs(curFirstKeyConfig[nextTabKey]) do
		local firstTabConfig = Game.TableData.GetCustomRoleTabDataRow(nextSimpleTabID)

		local firstTabData = UIComAccordionList.NewTabData(firstTabConfig.StringValue, Game.UIIconUtils.getIcon(firstTabConfig.TabIcon))
		firstTabData.otherInfo = firstTabConfig
		treeviewData:AddFirstNode(firstTabData)

		for _, value in ksbcpairs(firstTabConfig[nextTabKey]) do
			local twoConfig = Game.TableData.GetCustomRoleTabDataRow(value)
			if twoConfig.ThisTabType == Enum.ECustomRoleTabLevel.TabLevel3 then
				local secondTabData = UIComAccordionList.NewTabData(twoConfig.StringValue, Game.UIIconUtils.getIcon(twoConfig.TabIcon))
				secondTabData.otherInfo = twoConfig
				treeviewData:AddTwoNode(index, secondTabData)
			end
		end
	end

	self.TabTreeListCom:Refresh(treeviewData)
	self.TabTreeListCom:ExpansionMainTab(1)
end

--2、3级标签选中
--- 此处为自动生成
---@param index number
---@param data UITreeViewChildData
---@param selected bool
function LeftTabWidget:on_TabTreeListCom_ItemSelected(index, data, selected)
	if not selected then
		return
	end

	local showWidgetConfig = data.tabData.otherInfo

	self:GetParent():RefreshShowWidget(showWidgetConfig)
	self:RefreshBandedHairVisible()
	if showWidgetConfig.ThisTabType == Enum.ECustomRoleTabLevel.TabLevel2 then
		self:RefreshCameraStage(showWidgetConfig)
	end

	Game.CustomRoleSystem:SaveCustomRoleFaceData()
end

---刷新左侧列表item数据
function LeftTabWidget:RefreshLeftListUI(leftListWidgetID)
	self:RefreshListWidgetVisible(false)

	local tabWidgetConfig = Game.TableData.GetCustomRoleTabWidgetDataRow(leftListWidgetID)
	if not tabWidgetConfig then
		return
	end

	local dataWidget = self:GetWidgetDataBySexAndProfession(tabWidgetConfig)
	for _, data in ksbcpairs(dataWidget) do
		local widgetType, widgetIDList = string.match(data, "(%a+)%((.+)%)")
		if widgetType == Enum.ECustomRoleWidgetType.List then
			self:AddListData(widgetIDList, self.NormalListCom, self.view.SB_NormalList, self.view.NormalList)
			break
		elseif widgetType == Enum.ECustomRoleWidgetType.SList then
			self:AddListData(widgetIDList, self.SListCom, self.view.SB_SList, self.view.SList)
			break
		elseif widgetType == Enum.ECustomRoleWidgetType.ListV then
			self:AddListData(widgetIDList, self.VListCom, self.view.SB_VList, self.view.VList)
			break
		elseif widgetType == Enum.ECustomRoleWidgetType.ListH then
			self:AddListData(widgetIDList, self.HListCom, self.view.SB_HList, self.view.HList)
			break
		elseif widgetType == Enum.ECustomRoleWidgetType.ListVAllBody then
			self:AddListData(widgetIDList, self.PresetListCom, self.view.SB_PresetList, self.view.PresetList)
			break
		end
	end

	self:RefreshBandedHairVisible()
end

function LeftTabWidget:AddListData(widgetIDList, childListView, childListWidgetBox, childListWidget)
	local curSelectIndex = nil
	table.clear(self.itemDataList)

	for idParam in string.gmatch(widgetIDList, "%d+") do
		local id = tonumber(idParam)
		if id then
			local itemConfig = Game.TableData.GetCustomRoleListDataRow(id)
			if itemConfig then
				table.insert(self.itemDataList, itemConfig)

				if not curSelectIndex and Game.CustomRoleSystem:IsHasData(itemConfig.DataType, itemConfig.IDSearch) then
					curSelectIndex = #self.itemDataList
				end
			else
				Log.WarningFormat("CustomRole: itemData = nil, id = %s, check config", id)
			end
		else
			Log.WarningFormat("CustomRole: tabData WidgetName not valid, WidgetName = %s", idParam)
		end
	end

	self.curShowItemListCom = childListView
	childListView:Refresh(self.itemDataList)
	childListWidgetBox:SetVisibility(ESlateVisibility.Visible)
	childListWidget:RequestRefreshSynchronous()

	if curSelectIndex then
		childListView:SetSelectedItemByIndex(curSelectIndex, true)
	else
		childListView:ClearSelection()
	end
end

function LeftTabWidget:GetWidgetDataBySexAndProfession(tabWidgetConfig)
	if tabWidgetConfig.DataDiff == Enum.ECustomRoleWidgetDiffType.Sex then
		local playerSex = Game.CustomRoleSystem:GetCurModelSex()
		local dataWidgetName= string.format("%s%s", Enum.ECustomRoleWidgetDiffType.Sex, playerSex)
		local dataWidget = tabWidgetConfig[dataWidgetName]
		if dataWidget then
			return dataWidget
		end
	elseif tabWidgetConfig.DataDiff == Enum.ECustomRoleWidgetDiffType.Profession then
		local professionID = Game.CustomRoleSystem:GetCurModelProfession()
		local dataWidgetName= string.format("%s%s", Enum.ECustomRoleWidgetDiffType.Profession, professionID)
		local dataWidget = tabWidgetConfig[dataWidgetName]
		if dataWidget then
			local playerSex = Game.CustomRoleSystem:GetCurModelSex()
			if #dataWidget >= playerSex + 1 then
				return {dataWidget[playerSex + 1]}
			else
				return dataWidget
			end
		end
	end

	return tabWidgetConfig.DefaultWidget
end

---点击ItemList
function LeftTabWidget:OnItemListClick(index, data)
	if self.curShowItemListCom:IsSelectedByIndex(index) then
		return
	end

	local item = self.curShowItemListCom:GetItemByIndex(index)
	item:UpdateCustomRoleData(true)
end

--- 此处为自动生成
---@param index number
---@param data UITreeViewChildData
---@param expanded bool
function LeftTabWidget:on_TabTreeListCom_ItemExpansionChanged(index, data, expanded)
	if not expanded then
		return
	end

	local showWidgetConfig = data.tabData.otherInfo
	if showWidgetConfig.ThisTabType == Enum.ECustomRoleTabLevel.TabLevel2 then
		self:RefreshCameraStage(showWidgetConfig)
	end
end

function LeftTabWidget:RefreshSimpleSwitch(switch)
	self.SwitchOpenWidgetCom:SetSwitch(switch)
end

---简单、专业模式切换
--- 此处为自动生成
---@param isOn bool
function LeftTabWidget:on_SwitchOpenWidgetCom_ClickEvent(isOn)
	self:RefreshSimpleSwitch(self.isEasyMode)
	self.isEasyMode = not self.isEasyMode
	
	local nowCurTab1ID = self.WBP_ComListCom:GetSelectedItemIndex()
	self.WBP_ComListCom:ClearSelection()
	self.WBP_ComListCom:SetSelectedItemByIndex(nowCurTab1ID, true)
end

---List显隐
function LeftTabWidget:RefreshListWidgetVisible(visible)
	local visibility = visible and ESlateVisibility.Visible or ESlateVisibility.Collapsed
	self.view.SB_NormalList:SetVisibility(visibility)
	self.view.SB_SList:SetVisibility(visibility)
	self.view.SB_VList:SetVisibility(visibility)
	self.view.SB_HList:SetVisibility(visibility)
	self.view.SB_PresetList:SetVisibility(visibility)
end


function LeftTabWidget:RefreshUIByStage()
	self.curStageIndex = Game.CustomRoleSystem:GetCurrentStage()
	if self.curStageIndex == Enum.ECustomRoleStageType.Stage1 then
		self.view.VB_Tab:SetVisibility(ESlateVisibility.Collapsed)
		self.view.LeftBottom_BtnList:SetVisibility(ESlateVisibility.Collapsed)

		self:RefreshPlainButton(false)
		self:RefreshEyeButton(false)
	elseif self.curStageIndex == Enum.ECustomRoleStageType.Stage2 then
		self.view.VB_Tab:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.LeftBottom_BtnList:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

		self.view.RawClothBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.HairBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

		self:RefreshPlainButton()
		self:RefreshHairButton()
		self:RefreshEyeButton()
	elseif self.curStageIndex == Enum.ECustomRoleStageType.Stage3 then
		self.view.VB_Tab:SetVisibility(ESlateVisibility.Collapsed)
		self.view.LeftBottom_BtnList:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

		self:RefreshPlainButton(false)
		self.view.RawClothBtn:SetVisibility(ESlateVisibility.Collapsed)
	elseif self.curStageIndex == Enum.ECustomRoleStageType.MaxStage then

	end
end

---刷新素衣
function LeftTabWidget:RefreshPlainButton(open)
	if open ~= nil then
		self.curDefaultClothesOpen = open
	end
	self.view.RawClothBtn:SetIconStyle(self.curDefaultClothesOpen and "Plain" or "UnPlain")
	Game.CustomRoleSystem:SetDefaultClothes(self.curDefaultClothesOpen)
end

---刷新束发
function LeftTabWidget:RefreshHairButton(open)
	if open ~= nil then
		self.curHairTieOpen = open
	end
	self.view.HairBtn:SetIconStyle(self.curHairTieOpen and "BandedHair" or "UnBandedHair")
	Game.CustomRoleSystem:SetDefaultHair(self.curHairTieOpen)
end

---刷新视线
function LeftTabWidget:RefreshEyeButton(open)
	if open ~= nil then
		self.curGazeOpen = open
	end
	self.view.LookAtBtn:SetIconStyle(self.curGazeOpen and "Sight" or "UnSight")
	Game.CustomRoleSystem:SetGazeState(self.curGazeOpen)
end

---关闭素衣
function LeftTabWidget:ClosePlain()
	self:RefreshPlainButton(false)
end

---关闭素衣
function LeftTabWidget:CloseEyeFollow()
	self:RefreshEyeButton(false)
end

--- 素衣按钮
--- 此处为自动生成
function LeftTabWidget:on_RawClothBtnCom_ClickEvent()
	self:RefreshPlainButton(not self.curDefaultClothesOpen)
end

--- 束发按钮
--- 此处为自动生成
function LeftTabWidget:on_HairBtnCom_ClickEvent()
	self:RefreshHairButton(not self.curHairTieOpen)
end

---视线开关
--- 此处为自动生成
function LeftTabWidget:on_LookAtBtnCom_ClickEvent()
	self:RefreshEyeButton(not self.curGazeOpen)
end

---束发按钮显隐
function LeftTabWidget:RefreshBandedHairVisible()
	local visible = true
	if self.itemDataList then
		for i, config in pairs(self.itemDataList) do
			if config.DataType == Enum.ECustomRoleDataType.HairColor then
				visible = false
				break
			end

			if config.DataType == Enum.ECustomRoleDataType.Fashion then
				local fashionConfig = Game.TableData.GetFashionDataRow(tonumber(config.IDSearch))
				if fashionConfig and fashionConfig.SubType == Enum.EAppearanceSubType2ID.Hair then
					visible = false
					break
				end
			end
		end
	end

	local curStageIndex = Game.CustomRoleSystem:GetCurrentStage()
	if curStageIndex ~= Enum.ECustomRoleStageType.Stage2 then
		visible = false
	end

	self.view.HairBtn:SetVisibility(visible and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	if not visible then
		self:RefreshHairButton(false)
	end
end



--- 此处为自动生成
---@param index number
---@param data table
function LeftTabWidget:on_VListCom_ItemClicked(index, data)
	self:OnItemListClick(index, data)
end

--- 此处为自动生成
---@param index number
---@param data table
function LeftTabWidget:on_SListCom_ItemClicked(index, data)
	self:OnItemListClick(index, data)
end

--- 此处为自动生成
---@param index number
---@param data table
function LeftTabWidget:on_PresetListCom_ItemClicked(index, data)
	self:OnItemListClick(index, data)
end

--- 此处为自动生成
---@param index number
---@param data table
function LeftTabWidget:on_NormalListCom_ItemClicked(index, data)
	self:OnItemListClick(index, data)
end

--- 此处为自动生成
---@param index number
---@param data table
function LeftTabWidget:on_HListCom_ItemClicked(index, data)
	self:OnItemListClick(index, data)
end


function LeftTabWidget:OnRefreshWidgetUIByData()
	if not next(self.itemDataList) then
		return
	end

	local find = false
	local listConfig = self.itemDataList[1]
	if listConfig.DataType == Enum.ECustomRoleDataType.Fashion then
		for i, config in pairs(self.itemDataList) do
			local hairFashionID = tonumber(config.IDSearch)
			local curHairFashionID = Game.CustomRoleSystem:GetCurHairFashionID()
			if hairFashionID == curHairFashionID then
				self.curShowItemListCom:SetSelectedItemByIndex(i, true)
				find = true
				break
			end
		end
	elseif listConfig.DataType == Enum.ECustomRoleDataType.HairColor then
		for i, config in pairs(self.itemDataList) do
			local hairColorID = tonumber(config.IDSearch)
			local curHairColorID = Game.CustomRoleSystem:GetCurHairMakeUpID()
			if hairColorID ==  curHairColorID then
				self.curShowItemListCom:SetSelectedItemByIndex(i, true)
				find = true
				break
			end
		end
	elseif listConfig.DataType == Enum.ECustomRoleDataType.MakeUp then
		for i, config in pairs(self.itemDataList) do
			local makeUpConfig = Game.TableData.GetFashionMakeUpDataRow(tonumber(config.IDSearch))
			local makeUpValue = Game.CustomRoleSystem:GetData(Enum.ECustomRoleDataType.MakeUp, makeUpConfig.Part)
			if makeUpValue == makeUpConfig.Path then
				self.curShowItemListCom:SetSelectedItemByIndex(i, true)
				find = true
				break
			end
		end
	end

	if not find then
		self.curShowItemListCom:ClearSelection()
	end
end

---刷新相机状态
function LeftTabWidget:RefreshCameraStage(showWidgetConfig)
	Game.CustomRoleSystem:RefreshCameraStage(showWidgetConfig.CameraStage, self.cameraBlend)
	self.cameraBlend = true
end

return LeftTabWidget
