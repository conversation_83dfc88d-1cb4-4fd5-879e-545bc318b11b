local CustomRoleRectSlider = kg_require("Gameplay.LogicSystem.CustomRole.NewUI.RightWidget.CustomRoleRectSlider")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local CustomRoleBtnIcon = kg_require("Gameplay.LogicSystem.CustomRole.CustomRoleBtnIcon")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility =  import("ESlateVisibility")
---@class RightDetailWidget : UIComponent
---@field view RightDetailWidgetBlueprint
local RightDetailWidget = DefineClass("RightDetailWidget", UIComponent)

RightDetailWidget.eventBindMap = {
	[EEventTypes.LOGIN_QUEUE_UPDATE] = "RefreshServerQueueState",
	[EEventTypesV2.ON_CUSTOM_ROLE_CHANGE_BUTTON_STAGE] = "RefreshButtonGray",
	[EEventTypesV2.ON_CUSTOM_ROLE_TEXTURE_EXTRA_CHANGE] = "RefreshItemWidgetUI",
	[EEventTypesV2.ON_CUSTOM_ROLE_REFRESH_WIDGET_UI] = "RefreshItemWidgetUI",
	[EEventTypesV2.ON_CUSTOM_IRIS_STATE_UPDATE] = "OnColorSetUpdate",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RightDetailWidget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RightDetailWidget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RightDetailWidget:InitUIComponent()
	--- 二维参数调整
    ---@type CustomRoleRectSlider
    self.RectSliderCom = self:CreateComponent(self.view.RectSlider, CustomRoleRectSlider)
    ---@type UIListView childScript: CustomRoleSlider_Item
    self.DetailListCom = self:CreateComponent(self.view.DetailList, UIListView)
    ---@type UIComButton
    self.WBP_CreateRoleQueueBtnCom = self:CreateComponent(self.view.WBP_CreateRoleQueueBtn, UIComButton)
	
    ---@type UIComButton
    self.NextStepCom = self:CreateComponent(self.view.NextStep, UIComButton)
    ---@type CustomRoleBtnIcon
    self.ResetBtnCom = self:CreateComponent(self.view.ResetBtn, CustomRoleBtnIcon)
    ---@type CustomRoleBtnIcon
    self.BackBtnCom = self:CreateComponent(self.view.BackBtn, CustomRoleBtnIcon)
    ---@type CustomRoleBtnIcon
    self.ForwardBtnCom = self:CreateComponent(self.view.ForwardBtn, CustomRoleBtnIcon)
end

---UI事件在这里注册，此处为自动生成
function RightDetailWidget:InitUIEvent()
    self:AddUIEvent(self.ForwardBtnCom.onClickEvent, "on_ForwardBtnCom_ClickEvent")
    self:AddUIEvent(self.ResetBtnCom.onClickEvent, "on_ResetBtnCom_ClickEvent")
    self:AddUIEvent(self.BackBtnCom.onClickEvent, "on_BackBtnCom_ClickEvent")
    self:AddUIEvent(self.NextStepCom.onClickEvent, "on_NextStepCom_ClickEvent")
    self:AddUIEvent(self.WBP_CreateRoleQueueBtnCom.onClickEvent, "on_WBP_CreateRoleQueueBtnCom_ClickEvent")
    self:AddUIEvent(self.DetailListCom.onGetEntryClassIndexForItem, "on_DetailListCom_GetEntryClassIndexForItem")
	self:AddUIEvent(self.RectSliderCom.onValueChangeCB, "on_RectSliderCom_ValueChangeCB")
	self:AddUIEvent(self.RectSliderCom.onTriangleCentroidChangedCB, "on_RectSliderCom_TriangleCentroidChangedCB")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RightDetailWidget:InitUIView()
end

---组件刷新统一入口
function RightDetailWidget:Refresh()
	self.rightWidgetDataList = {}
	
	self.ResetBtnCom:Refresh()
	self.BackBtnCom:Refresh()
	self.ForwardBtnCom:Refresh()

	self:RefreshButtonGray()
	self:RefreshServerQueueState()
end

function RightDetailWidget:RefreshRightUI(rightDetailWidgetID)
	local curStageIndex = Game.CustomRoleSystem:GetCurrentStage()
	if curStageIndex == Enum.ECustomRoleStageType.Stage2 then
		self.view.BtnList:SetVisibility(ESlateVisibility.Visible)
	end

	self.DetailListCom:Refresh()
	self.DetailListCom:Hide()
	self.RectSliderCom:Hide()
	
	local tabWidgetConfig = Game.TableData.GetCustomRoleTabWidgetDataRow(rightDetailWidgetID)
	if not tabWidgetConfig then
		return
	end

	local dataWidget = self:GetWidgetDataByType(tabWidgetConfig)
	table.clear(self.rightWidgetDataList)

	local firstNodeIndex = 0
	for _, data in ksbcpairs(dataWidget) do
		local widgetType, widgetIDS = string.match(data, "(%a+)%((.+)%)")
		if widgetType == Enum.ECustomRoleWidgetType.Slider then
			self:AddSliderData(widgetIDS)
			firstNodeIndex = firstNodeIndex + 1
		elseif widgetType == Enum.ECustomRoleWidgetType.ColorBoard then
			self:AddColorBoardData(firstNodeIndex, widgetIDS)
		elseif widgetType == Enum.ECustomRoleWidgetType.RSlider then
			self:AddRectSliderData(widgetIDS, rightDetailWidgetID)
		elseif widgetType == Enum.ECustomRoleWidgetType.GroupSet then
			self:AddGroupSetData(firstNodeIndex, widgetIDS)
		end
	end

	if not next(self.rightWidgetDataList) then
		return
	end

	self.DetailListCom:Show()
	self.DetailListCom:Refresh(self.rightWidgetDataList)
end

---普通Slider
function RightDetailWidget:AddSliderData(widgetIDS)
	for idParam in string.gmatch(widgetIDS, "%d+") do
		local id = tonumber(idParam)
		if id then
			local sliderConfig = Game.TableData.GetCustomRoleSliderDataRow(id)
			if sliderConfig then
				table.insert(self.rightWidgetDataList, sliderConfig)
			else
				Log.Warning("CustomRole: sliderConfig = nil, id = ", id)
			end
		else
			Log.Warning("CustomRole: tabData中 WidgetName无效， = ", idParam)
		end
	end
end

---调色盘
function RightDetailWidget:AddColorBoardData(index, widgetIDS)
	local idP = tonumber(widgetIDS)
	local colorData = Game.TableData.GetCustomRoleColorBoardDataRow(idP)
	if not colorData then
		Log.WarningFormat("CustomRole: colorData = nil, widgetIDS = %s", widgetIDS)
		return
	end

	table.insert(self.rightWidgetDataList, { ID = idP})
end

---样式集合
function RightDetailWidget:AddGroupSetData(index, widgetIDS)
	local idP = tonumber(widgetIDS)
	local GroupSetData = Game.TableData.GetCustomRoleGroupSetDataRow(idP)
	if not GroupSetData then
		Log.WarningFormat("CustomRole: GroupSet = nil, widgetIDS = %s", widgetIDS)
		return
	end

	table.insert(self.rightWidgetDataList, { GroupID = idP})
end

---二维Slider
function RightDetailWidget:AddRectSliderData(widgetIDS, widgetPID)
	local idP = tonumber(widgetIDS)
	local rectSliderData = Game.TableData.GetCustomRoleSliderDataRow(idP)
	if not rectSliderData then
		Log.WarningFormat("CustomRole: rectSliderData = nil, widgetIDS = %s", widgetIDS)
		return
	end

	self.rectSliderData = rectSliderData
	self.isFace =  table.contains(Game.TableData.GetCustomRoleSettingDataRow("FACE_PRESET_IDS"),widgetPID)
	--local isBody = table.contains(Game.TableData.GetCustomRoleSettingDataRow("BODY_PRESET_IDS"),widgetPID)
	self.RectSliderCom:Show()
	self.RectSliderCom:Refresh(rectSliderData.WidgetTitle)
	
	--隐藏前进后退
	self.view.BtnList:SetVisibility(ESlateVisibility.Collapsed)
end

function RightDetailWidget:on_RectSliderCom_ValueChangeCB(x, y)
	--更新人物模型
	self:UpdateRectSliderData(x, y, self.rectSliderData.PresetIDs, self.isFace)
end

---根据x和y更新人物模型
function RightDetailWidget:UpdateRectSliderData(x, y, presetIDs, isFace)
	local presetValues = self:GetPresetIDList(x, y, presetIDs)
	local wa, wb, wc = self.RectSliderCom:GetPercent()
	if not self.percentValueList then
		self.percentValueList = {}
	end
	self.percentValueList[1] = wa
	self.percentValueList[2] = wb
	self.percentValueList[3] = wc
	
	Game.CustomRoleSystem:SetPresetBlend(presetValues, self.percentValueList, isFace)
end

function RightDetailWidget:GetPresetIDList(x, y, presetIDs)
	if not self.presetIDList then
		self.presetIDList = {}
	end
	local indexX = x > 0.5 and 3 or 4
	local indexY = y > 0.5 and 2 or 1
	
	self.presetIDList[1] = presetIDs[indexY]
	self.presetIDList[2] = presetIDs[indexX]
	return self.presetIDList
end

function RightDetailWidget:GetWidgetDataByType(tabWidgetConfig)
	if tabWidgetConfig.DataDiff == Enum.ECustomRoleWidgetDiffType.Sex then
		local playerSex = Game.CustomRoleSystem:GetCurModelSex()
		local dataWidgetName= string.format("%s%s", Enum.ECustomRoleWidgetDiffType.Sex, playerSex)
		local dataWidget = tabWidgetConfig[dataWidgetName]
		if dataWidget then
			return dataWidget
		end
	elseif tabWidgetConfig.DataDiff == Enum.ECustomRoleWidgetDiffType.Profession then
		local professionID = Game.CustomRoleSystem:GetCurModelProfession()
		local dataWidgetName= string.format("%s%s", Enum.ECustomRoleWidgetDiffType.Profession, professionID)
		local dataWidget = tabWidgetConfig[dataWidgetName]
		if dataWidget then
			local playerSex = Game.CustomRoleSystem:GetCurModelSex()
			if #dataWidget >= playerSex + 1 then
				return {dataWidget[playerSex + 1]}
			else
				return dataWidget
			end
		end
	end

	return tabWidgetConfig.DefaultWidget
end


function RightDetailWidget:RefreshUIByStage()
	self.curStageIndex = Game.CustomRoleSystem:GetCurrentStage()
	if self.curStageIndex == Enum.ECustomRoleStageType.Stage1 then
		self.view.BtnList:SetVisibility(ESlateVisibility.Collapsed)
		self.view.NextStep.Text:SetText(StringConst.Get("CUSTOM_ROLE_GO_CUSTOM"))

	elseif self.curStageIndex == Enum.ECustomRoleStageType.Stage2 then
		self.view.NextStep.Text:SetText(Game.TableData.GetCustomRoleSettingDataRow("UI_BUTTON_NEXT"))
	elseif self.curStageIndex == Enum.ECustomRoleStageType.Stage3 then
		self.view.BtnList:SetVisibility(ESlateVisibility.Collapsed)
		self.view.NextStep.Text:SetText(Game.TableData.GetCustomRoleSettingDataRow("UI_BUTTON_FINISH"))
	elseif self.curStageIndex == Enum.ECustomRoleStageType.MaxStage then

	end
end

---前进
--- 此处为自动生成
function RightDetailWidget:on_ForwardBtnCom_ClickEvent()
	Game.CustomRoleSystem:RedoData()
	self:RefreshButtonGray()
end

---后退
--- 此处为自动生成
function RightDetailWidget:on_BackBtnCom_ClickEvent()
	Game.CustomRoleSystem:UndoData()
	self:RefreshButtonGray()
end


---重置
--- 此处为自动生成
function RightDetailWidget:on_ResetBtnCom_ClickEvent()
	local curFirstKeyData = Game.TableData.GetCustomRoleTabDataRow(self:GetParent():GetCurFirstTabID())
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CUSTOMROLE_RESETDATA, function()
		Game.CustomRoleSystem:ResetHistoryDataCurTab()
		self:RefreshButtonGray()
	end, nil, {curFirstKeyData.StringValue})
end

function RightDetailWidget:RefreshButtonGray()
	self.view.BackBtn:SetIsEnabled(Game.CustomRoleSystem:CanUndo())
	self.view.ForwardBtn:SetIsEnabled(Game.CustomRoleSystem:CanRedo())
	self.view.ResetBtn:SetIsEnabled(Game.CustomRoleSystem:CanReset())
end

function RightDetailWidget:RefreshItemWidgetUI()
	for index, v in pairs(self.rightWidgetDataList) do
		local item = self.DetailListCom:GetItemByIndex(index)
		if item then
			item:OnRefreshWidgetUI()
		end
	end
end

function RightDetailWidget:OnColorSetUpdate()
	for index, v in pairs(self.rightWidgetDataList) do
		local item = self.DetailListCom:GetItemByIndex(index)
		if item and item.OnColorSetUpdate then
			item:OnColorSetUpdate()
		end
	end
end

--- 此处为自动生成
function RightDetailWidget:on_NextStepCom_ClickEvent()
	self:GetParent():EnterNextStage()
end

--- 此处为自动生成
function RightDetailWidget:on_WBP_CreateRoleQueueBtnCom_ClickEvent()
	if self.view.WBP_CreateRoleQueueTips:GetVisibility() == ESlateVisibility.Collapsed then
		self.view.WBP_CreateRoleQueueTips:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.WBP_CreateRoleQueueTips:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function RightDetailWidget:RefreshServerQueueState()
	local accountEntity = Game.NetworkManager.GetAccountEntity()
	--排队中
	if accountEntity.bInServerQueue then
		local waitTime = math.ceil(accountEntity.minLoginQueueRank / accountEntity.afkNumPerMin) --当前排队位置/每分钟排进游戏数量
		self.view.WBP_CreateRoleQueueTips.KRichT_Queue:SetText(string.format(StringConst.Get("LOGIN_QUEUE_RANK_FORMAT"), accountEntity.minLoginQueueRank))
		self.view.WBP_CreateRoleQueueTips.KRichT_TIme:SetText(string.format(StringConst.Get("LOGIN_QUEUE_TIME_FORMAT"), waitTime // 60, waitTime % 60))

		self.view.WBP_CreateRoleQueueBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.WBP_CreateRoleQueueBtn:SetVisibility(ESlateVisibility.Collapsed)
	end

	self.view.WBP_CreateRoleQueueTips:SetVisibility(ESlateVisibility.Collapsed)
end


--- 此处为自动生成
---@param index number
---@return number
function RightDetailWidget:on_DetailListCom_GetEntryClassIndexForItem(index)
	local data = self.rightWidgetDataList[index+1]
	if data.ID or data.GroupID then
		return 1
	else
		return 0
	end
end

return RightDetailWidget
