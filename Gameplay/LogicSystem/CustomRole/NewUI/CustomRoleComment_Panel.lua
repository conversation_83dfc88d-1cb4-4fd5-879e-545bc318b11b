local ServerQueueInfo = kg_require("Gameplay.LogicSystem.CreateRole.ServerQueueInfo")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local CustomRoleBtnIcon = kg_require("Gameplay.LogicSystem.CustomRole.CustomRoleBtnIcon")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESlateVisibility =  import("ESlateVisibility")
---@class CustomRoleComment_Panel : UIPanel
---@field view CustomRoleComment_PanelBlueprint
local CustomRoleComment_Panel = DefineClass("CustomRoleComment_Panel", UIPanel)

CustomRoleComment_Panel.eventBindMap = {
	[EEventTypes.LOGIN_QUEUE_UPDATE] = "RefreshServerQueueState",
	[EEventTypes.LOGIN_SWITCH_SERVER] = "OnSwitchServer",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CustomRoleComment_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CustomRoleComment_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CustomRoleComment_Panel:InitUIComponent()
    ---@type CustomRoleBtnIcon
    self.WBP_CustomRoleBtnIconCom = self:CreateComponent(self.view.WBP_CustomRoleBtnIcon, CustomRoleBtnIcon)
	---@type UIButton
	self.BackArrowCom = self:CreateComponent(self.view.BackArrow, UIComButton)
	---@type UI_DiyText
	self.Title_NameCom = self:CreateComponent(self.view.Title_Name, UIComDiyTitle)
	---@type ServerQueueInfo
	self.CreateRoleQueue = self:CreateComponent(self.view.WBP_CustomRoleQueue, ServerQueueInfo)	--排队信息面板
end

---UI事件在这里注册，此处为自动生成
function CustomRoleComment_Panel:InitUIEvent()
    self:AddUIEvent(self.BackArrowCom.onClickEvent, "on_BackArrowCom_ClickEvent")
    self:AddUIEvent(self.view.WBP_CustomRoleBtn.Btn_ClickArea.OnClicked, "on_WBP_CustomRoleBtnBtn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CustomRoleComment_Panel:InitUIView()
	self.WBP_CustomRoleBtnIconCom:SetVisible(false)
end

---面板打开的时候触发
function CustomRoleComment_Panel:OnRefresh()
	local assessmentID = Game.CustomRoleSystem:GetFitAssessmentID()
	if assessmentID then
		local assessmentRow = Game.TableData.GetCustomRoleAssessmentDataRow(assessmentID)
		self.Title_NameCom:Refresh(assessmentRow.Word)
		self.view.DescriptionText:SetText(assessmentRow.Comment)
		if assessmentRow.Type == 1 then
			self.userWidget:Event_UI_State(1)
			self:PlayAnimation(self.userWidget.Ani_Fadein_yellow)
		elseif assessmentRow.Type == 2 then
			self.userWidget:Event_UI_State(0)
			self:PlayAnimation(self.userWidget.Ani_Fadein_blue)
		end
	end

	self.BackArrowCom:SetName("")

	self.defaultServerQueue = Game.NetworkManager.GetAccountEntity().bInServerQueue
	self:RefreshServerQueueState()
end

--- 此处为自动生成
function CustomRoleComment_Panel:on_BackArrowCom_ClickEvent()
	self:CloseSelf()
	Game.CinematicManager:StopPlayCinematic({CinematicType = Enum.CinematicType.Cutscene})
	Game.NewUIManager:OpenPanel(UIPanelConfig.CustomRole_Panel, false)
end

--- 此处为自动生成
function CustomRoleComment_Panel:on_WBP_CustomRoleBtnBtn_ClickArea_Clicked()
	local accountEntity = Game.NetworkManager.GetAccountEntity()
	if accountEntity.bInServerQueue then
		return
	end

	self:CloseSelf()
	Game.CustomRoleSystem:PlayLevelSequenceEnter()
end

function CustomRoleComment_Panel:RefreshServerQueueState()
	local accountEntity = Game.NetworkManager.GetAccountEntity()
	local bInServerQueue = accountEntity.bInServerQueue
	--排队中
	if bInServerQueue then
		self.view.WBP_CustomRoleQueue:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.HB_Btn:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.HB_Btn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.WBP_CustomRoleQueue:SetVisibility(ESlateVisibility.Collapsed)
	end

	if self.defaultServerQueue and not bInServerQueue then
		self.defaultServerQueue = false
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.LOGIN_QUEUE_FINISH_ENTER_GAME, function()
			if not Game.NewUIManager:IsOpened(UIPanelConfig.CustomRoleComment_Panel) then
				return
			end
			
			self:on_WBP_CustomRoleBtnBtn_ClickArea_Clicked()
		end)
	end
end

function CustomRoleComment_Panel:OnSwitchServer()
	-- 切换服务器后重置下defaultServerQueue状态，避免切换完会直接弹窗LOGIN_QUEUE_FINISH_ENTER_GAME
	self.defaultServerQueue = false
end

return CustomRoleComment_Panel
