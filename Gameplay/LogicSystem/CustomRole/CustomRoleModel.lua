---@class CustomRoleModel:SystemModelBase
local CustomRoleModel = DefineClass("CustomRoleModel",SystemModelBase)
local TablePool = kg_require("Framework.Library.TablePool")
require("Gameplay.3C.AvatarCreator.AvatarCreatorDefineAutoGen")
local AvatarProfileLib = kg_require("Data.Config.Model.AvatarProfileLib")
local CustomRoleTools = kg_require("Gameplay.LogicSystem.CustomRole.CustomRoleTools")
local SubsystemBlueprintLibrary = import("SubsystemBlueprintLibrary")
local PakUpdateSubsystem = import("PakUpdateSubsystem")

function CustomRoleModel:init()
	self.CustomRoleSetting = {}
	
end

function CustomRoleModel:unInit()
end

---@public method 初始化数据
function CustomRoleModel:InitData(enterType, professionID, sex)
    if self.IsInCustomRole then
        Log.WarningFormat("CustomRole: InitData Error, IsInCustomRole = true")
        return
    end
	---是否在捏脸场景
    self.IsInCustomRole = true
    ---进入捏脸方式
    self.EnterType = enterType
    
	---当前选择职业
    self.ProfessionID = professionID
	---性别
	self.PlayerSex = sex
	
	---当前选择的时装ID（不包含素衣）（仅预览用）
	self.CurSelectFashionID = nil
	---当前脸部风格
	self.CurFaceStyle = nil
	---当前身体风格
	self.CurBodyStyle = nil

	---是否打开束发
	self.IsDefaultHair = false
	---是否打开素衣
	self.IsDefaultClothes = false
	
	---束发头发 时装ID
	---素衣衣服 时装ID
	if self.PlayerSex == Enum.ESex.FEMALE then
		self.DefaultHairID = Game.TableData.GetCustomRoleSettingDataRow("HAIR_TIE_ID")
		self.DefaultClothesID = Game.TableData.GetCustomRoleSettingDataRow("PLAIN_SUIT_ID")
	else
		self.DefaultHairID = Game.TableData.GetCustomRoleSettingDataRow("HAIR_TIE_MEN_ID")
		self.DefaultClothesID = Game.TableData.GetCustomRoleSettingDataRow("PLAIN_SUIT_MEN_ID")
	end
	
	---异瞳时，是否选择左眼
	self.IsHeterochromiaSelectLeft = true
	
    ---缓存的当前玩家全部自定义捏脸数据
    self.CurModelData = {
		ProfileName = nil,

		FaceData = {},
		MakeUpParamData = {},
		MakeUpResData = {},
        
		--当前预设对应所有头部基础部件
		BaseHeadBodyPartList = {},
		--当前所穿的头部基础部件
		CurHeadBodyPartList = {},
		
		--当前选择穿的服装 key = fashionID
        FashionData = {}
    }
		
	--需要上传服务器的自定义数据
	self.CurCustomRoleData = {
		---版本
		Version = SubsystemBlueprintLibrary.GetEngineSubsystem(PakUpdateSubsystem):GetCurrentPakVersion(),

		---整体预设
		ModelPresetID = 0,
		---头部预设
		HeadPresetFacadeID = nil,

		---Face骨骼、妆容数据(Override)
		FaceData = {},
		MakeUpParamData = {},
		MakeUpResData = {},

		HairFashionID = nil,
		HairMakeUpID = nil,
	}

	---身体预设
	self.BodyPresetFacadeID = nil

	---修改脸部骨骼数据的缓存CompactData，RefreshActor_BodyShape刷新局部骨骼参数时使用
	self.CacheChangeBoneCompactData = {}

	---开始混合时，缓存一份初始骨骼数据
	self.CacheBlendStartBoneData = {}
	
	--头部配饰部件
	self.HeadAccPartData = {
		[Enum.EAvatarBodyPartType.TopAccessory] = Enum.EAvatarBodyPartType.TopAccessory,
		[Enum.EAvatarBodyPartType.HairAccessory] = Enum.EAvatarBodyPartType.HairAccessory,
	}

	--阶段相关数据
	self.StageStartData = {}
	self.StageName = {}
	self.StageListData = {}
	self.CurStageIndex = 1

	---场景蓝图相关数据
	self.SceneName = ""
	self.SceneID = nil

	--模型相关
	self.RoleEntity = nil
	self.RoleEntityID = nil
	self.RoleNowRotator = 90
	self.CameraActorID = nil

	---序列化后的捏脸数据
    self.SerializationCustomRoleFaceDataStr = ""
	
	---缓存表
    self.CustomRoleTablePool = TablePool.new(10, true)

    --历史修改记录，用于还原数据
    self.HistoryDataIndex = 0
    self.MaxHistoryIndex = 0
    self.HistoryDataList = {}

    --当前相机状态配置
    self.CurCameraMode = 0

    --回退时收集不重复数据
    self.CollectRevertData = {}
	for type, value in pairs(Enum.ECustomRoleRevertType) do
		self.CollectRevertData[value] = {}
	end
    self.SelectTabFirstID = 0

	self.IsLSDisplayFirstPlaying = false
	
	self.isRefreshingBodyPart = false
    
	---曲线数据
	self.CurveLoadData = {}
	self.CurveObjs = {}
	
	self.FaceDataSynItem = {}

	local irisSyn = Game.TableData.GetCustomRoleSettingDataRow("IRIS_COLOR_SYN")
	local irisSynLeft = Game.TableData.GetCustomRoleSettingDataRow("LEFT_IRIS_COLOR_SYN")
	--临时处理
	self.FaceDataSynItem[irisSyn[2]] = irisSyn[1]
	self.FaceDataSynItem[irisSynLeft[2]] = irisSynLeft[1]
	if self.EnterType == Enum.ECustomRoleEnterType.CreateRole then
		self.CustomRoleSetting[irisSyn[1]] = false
		self.CustomRoleSetting[irisSynLeft[1]] = false
	end
    self:InitStageData(professionID)
end

---@public method 清空数据
function CustomRoleModel:ClearData()
    if not self.IsInCustomRole then
        Log.WarningFormat("CustomRole: 重复CustomRoleModel:ClearData")
        return
    end
    self.IsInCustomRole = false

    self.StageStartData = nil
    self.StageListData = nil

    if self.SceneID then
        Game.SceneDisplayManager:RemoveScene(self.SceneID)
        self.SceneID = nil
        self.SceneName = nil
    end

	self:DestroyEntity()

    self.CameraActorID = nil
	self.CurCameraMode = nil

    self.CurModelData = nil
    self.SerializationCustomRoleFaceDataStr = nil

    self.CacheChangeBoneCompactData = nil
    self.CustomRoleTablePool:delete()
    self.CustomRoleTablePool = nil

    self.HistoryDataList = nil

    self.CollectRevertData = nil
end

function CustomRoleModel:DestroyEntity()
	local roleEntity = self:GetEntity()
	if roleEntity then
		roleEntity:destroy()
	end

	self.RoleEntityID = nil
end

function CustomRoleModel:GetEntity()
	return Game.EntityManager:GetLocalEntity(self.RoleEntityID)
end

---@public method 下一阶段
function CustomRoleModel:NextStage()
    if self.CurStageIndex < Enum.ECustomRoleStageType.MaxStage-1 then
        self.CurStageIndex = self.CurStageIndex + 1
    end
end

---@public method 上一阶段
function CustomRoleModel:LastStage()
    if self.CurStageIndex > Enum.ECustomRoleStageType.Stage1 then
        self.CurStageIndex = self.CurStageIndex - 1
    end
end

---@public method 获取脸部骨骼数据
function CustomRoleModel:GetFaceData(name)
    return self.CurModelData.FaceData[name]
end

---@public method 获取妆容数据
function CustomRoleModel:GetMakeUpData(name)
    return self.CurModelData.MakeUpResData[name] or self.CurModelData.MakeUpParamData[name]
end

function CustomRoleModel:GetCustomRoleTable()
    return self.CustomRoleTablePool:GetTable()
end

function CustomRoleModel:ReleaseCustomRoleTable(tab)
    self.CustomRoleTablePool:ReleaseTable(tab)
end

function CustomRoleModel:SetFashionData(fashionConfig)
    local allFashionData = self.CurModelData.FashionData
    allFashionData[fashionConfig.SubType] = fashionConfig.ID
end

function CustomRoleModel:GetFashionData(subtype)
	return self.CurModelData.FashionData[subtype]
end

function CustomRoleModel:SetMakeUpData(key, value, isSaveData)
    local MakeupProfileData = AvatarProfileLib[self.CurModelData.ProfileName].MakeupProfile[key]
    if not MakeupProfileData then
        Log.ErrorFormat("CustomRole: MakeupProfileData = nil, makeUpKey = %s", key)
        return
    end

    if MakeupProfileData.BodyPartType == Enum.EAvatarBodyPartType.Head then
		if self:IsMakeUpKeyRes(value) then
			if AvatarProfileLib.TexturePathToIndex[value] then
				self.CurModelData.MakeUpResData[key] = value
				--todo:这里异瞳逻辑后续废弃
				local synKey = self.FaceDataSynItem[key]
				if synKey and not self.CustomRoleSetting[synKey] then
					self.CurModelData.MakeUpResData[synKey] = value
				end

				if isSaveData then
					self.CurCustomRoleData.MakeUpResData[key] = value
				end
			else
				Log.DebugErrorFormat("CustomRole:  Error, key=%s, value=%s, not found in texture path index", key, value)
			end
		else
			if self:IsMakeUpKeyParam(value) then
				value = CustomRoleTools.RoundToDecimalPlaces(value)
			end
			
			self.CurModelData.MakeUpParamData[key] = value
			local synKey = self.FaceDataSynItem[key]
			if synKey and not self.CustomRoleSetting[synKey] then
				self.CurModelData.MakeUpParamData[synKey] = value
			end

			if isSaveData then
				self.CurCustomRoleData.MakeUpParamData[key] = value
			end
		end
    end
end

function CustomRoleModel:IsMakeUpKeyRes(value)
	return type(value) == "string"
end

function CustomRoleModel:IsMakeUpKeyParam(value)
	return type(value) == "number"
end

function CustomRoleModel:SetBoneData(name, value, isSaveData)
	local config = AvatarProfileLib[self.CurModelData.ProfileName].FaceProfile[name]
	if not config then
		Log.DebugErrorFormat("CustomRole: BoneConfig = nil，name = %s", name)
		return
	end

	value = CustomRoleTools.RoundToDecimalPlaces(value)

	local cachedFaceCompactData = self.CacheChangeBoneCompactData
	for _, ControlPoint in pairs(config) do
		local realValue = ControlPoint.Weight * value
		if ControlPoint.IsScaleAll then
			local StartIndex = math.floor((ControlPoint.Index + 0.1) / 3) * 3
			cachedFaceCompactData[StartIndex] = realValue
			cachedFaceCompactData[StartIndex + 1] = realValue
			cachedFaceCompactData[StartIndex + 2] = realValue
		end
		cachedFaceCompactData[ControlPoint.Index] = realValue
	end

	self.CurModelData.FaceData[name] = value
	if isSaveData then
		self.CurCustomRoleData.FaceData[name] = value
	end
end

function CustomRoleModel:SetBrandData(value)
end

function CustomRoleModel:RecordData(dataTable)
    self.HistoryDataIndex = self.HistoryDataIndex + 1
    self.MaxHistoryIndex = self.MaxHistoryIndex + 1

    table.insert(self.HistoryDataList, self.HistoryDataIndex, dataTable)
end

function CustomRoleModel:UndoData()
    if not self:CanUndo() then
        return nil
    end

    local dataTable = nil
    for i = self.HistoryDataIndex, 1, -1 do
        local dataTemp = self.HistoryDataList[i]
        if dataTemp.firstTab == self.SelectTabFirstID then
            dataTable = dataTemp
            self.HistoryDataIndex = i-1
            break
        end
    end

    if not dataTable then
        Log.ErrorFormat("CustomRole:   dataTable = nil")
        return
    end

    return dataTable, true
end

function CustomRoleModel:RedoData()
    if not self:CanRedo() then
        return nil
    end

    local dataTable = nil
    for i = self.HistoryDataIndex+1, self.MaxHistoryIndex, 1 do
        local dataTemp = self.HistoryDataList[i]
        if dataTemp.firstTab == self.SelectTabFirstID then
            dataTable = dataTemp
            self.HistoryDataIndex = i
            break
        end
    end
    
    if not dataTable then
        Log.ErrorFormat("CustomRole:   dataTable = nil")
        return
    end
    
    return dataTable, false
end

function CustomRoleModel:CanUndo()
    if self.HistoryDataIndex == 0 then
        return false
    end

    local hasTabData = false
    for i = self.HistoryDataIndex, 1, -1 do
        local dataTemp = self.HistoryDataList[i]
        if dataTemp.firstTab == self.SelectTabFirstID then
            hasTabData = true
            break
        end
    end

    if not hasTabData then
        return false
    end

    return true
end

function CustomRoleModel:CanRedo()
    if self.HistoryDataIndex == self.MaxHistoryIndex then
        return false
    end
    
    local hasTabData = false
    for i = self.HistoryDataIndex+1, self.MaxHistoryIndex, 1 do
        local dataTemp = self.HistoryDataList[i]
        if dataTemp.firstTab == self.SelectTabFirstID then
            hasTabData = true
            break
        end
    end

    if not hasTabData then
        return false
    end
    
    return true
end

function CustomRoleModel:ResetHistoryData(checkStage, checkTab)
    for type, list in pairs(self.CollectRevertData) do
        for _, data in pairs(list) do
            self:ReleaseCustomRoleTable(data)
        end
        table.clear(list)
    end
    
    for i = self.MaxHistoryIndex, 1, -1 do
        local dataTable = self.HistoryDataList[i]
        if dataTable.stage > self.CurStageIndex then
            Log.ErrorFormat("CustomRole: 重置当前层数据，下一层的数据还存在")
            return
        end

        if (not checkStage or dataTable.stage == self.CurStageIndex) and (not checkTab or dataTable.firstTab == self.SelectTabFirstID) then
            if self.MaxHistoryIndex == self.HistoryDataIndex then
                self:CollectAllRevertData(dataTable)
                self.HistoryDataIndex = self.HistoryDataIndex - 1
            end
            
            table.remove(self.HistoryDataList, i)
            self.MaxHistoryIndex = self.MaxHistoryIndex - 1
        end
    end
end

function CustomRoleModel:SetCameraData(cameraMode)
    self.CurCameraMode = cameraMode
end

function CustomRoleModel:SetRoles(roles)
    self.CurRoleInfoList = roles
end

function CustomRoleModel:InitStageData()
    local data = Game.TableData.GetCustomRoleStageDataTable()
    for _, value in ksbcpairs(data) do
        if self.PlayerSex == value.Sex then
            self.StageStartData[value.Stage] = value.StartTab
            self.StageName[value.Stage] = value.StageTitle
            self.StageListData[value.Stage] = {}
            for _, stageID in ksbcipairs(value.StartTab) do
                local menuData = Game.TableData.GetCustomRoleTabDataRow(stageID)
                table.insert(self.StageListData[value.Stage], {
                    name = menuData.StringValue
                })
            end
        end
    end
end

function CustomRoleModel:SaveCustomRoleFaceData()
	for name, value in pairs(self.CurCustomRoleData.FaceData) do
		self.CurModelData.FaceData[name] = value
	end
end

function CustomRoleModel:CollectAllRevertData(dataTable)
    local collectTypeList = self.CollectRevertData[dataTable.revertType]

    local find = false
    for i, data in ipairs(collectTypeList) do
        if data.key == dataTable.key then
            collectTypeList[i] = dataTable
            find = true
            break
        end
    end

    if not find then
        table.insert(collectTypeList, dataTable)
    end
end

return CustomRoleModel