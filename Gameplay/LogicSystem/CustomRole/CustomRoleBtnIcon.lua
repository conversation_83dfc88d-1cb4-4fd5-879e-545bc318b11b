local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class CustomRoleBtnIcon : UIComponent
---@field view CustomRoleBtnIconBlueprint
local CustomRoleBtnIcon = DefineClass("CustomRoleBtnIcon", UIComponent)

CustomRoleBtnIcon.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CustomRoleBtnIcon:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CustomRoleBtnIcon:InitUIData()
	---按钮点击事件
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function CustomRoleBtnIcon:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function CustomRoleBtnIcon:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CustomRoleBtnIcon:InitUIView()
end

---组件刷新统一入口
function CustomRoleBtnIcon:Refresh()
	self.userWidget:SetIconStyle(self.userWidget.NameIndex)
end

--- 此处为自动生成
function CustomRoleBtnIcon:on_Btn_ClickArea_lua_Clicked()
	self.onClickEvent:Execute()
end

return CustomRoleBtnIcon
