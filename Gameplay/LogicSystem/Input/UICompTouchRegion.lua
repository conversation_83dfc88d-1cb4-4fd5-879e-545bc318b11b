local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local LuaMulticastDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaMulticastDelegate")
local GestureRecognizer = kg_require("Gameplay.LogicSystem.Input.GestureRecognizer")

local KismetInputLibrary = import("KismetInputLibrary")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local UIFunctionLibrary = import("UIFunctionLibrary")
local KismetSystemLibrary = import("KismetSystemLibrary")
local C7FunctionLibrary = import("C7FunctionLibrary")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")

---@class UICompTouchRegionParam
---@field maxTouchCount number 最大触摸点数
---@field allowUpdateTimesInOneFrame boolean 是否允许在同一帧内更新多次
---@filed gestureMask number 手势掩码
---@field pinchCheckDuration number 捏合的两指按下时间差(ms)
---@field mouseWheelSimPinch boolean 鼠标滚轮模拟捏合
---@field mouseWheelSimPinchScale number 鼠标滚轮模拟捏合缩放比例

---@class UICompTouchRegion : UIComponent
---@field view UICompTouchRegionBlueprint
local UICompTouchRegion = DefineClass("UICompTouchRegion", UIComponent)


--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UICompTouchRegion:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end


---@class TouchFingerInfo 手指信息
---@field FingerIdx number 手指索引
---@field Pos Vector2 手指初始位置
---@field IsPressed boolean 是否按下
---@field PressTime number 按下时间(ms)
---@field Moved boolean 是否移动过
---@field LastUpTime number 最后一次抬起时间(ms)
---@field UpdateFrame number 更新帧
---@field IsDrag boolean 是否拖拽中
---@field MouseButton FKey 按下时的鼠标按键

---初始化数据
function UICompTouchRegion:InitUIData()
    ---@type UICompTouchRegionParam
    self.Param = nil

    --[[
        ---@type LuaMulticastDelegate<fun()>
        self.onTapEvent = LuaMulticastDelegate.new()
        ---@type LuaMulticastDelegate<fun()>
        self.onDoubleTapEvent = LuaMulticastDelegate.new()
        ---@type LuaMulticastDelegate<fun()>
        self.onLongPressEvent = LuaMulticastDelegate.new()
        ---@type LuaMulticastDelegate<fun()>
        self.onSwipeEvent = LuaMulticastDelegate.new()
        ---@type LuaMulticastDelegate<fun()>
        self.onRotateEvent = LuaMulticastDelegate.new()
    ]]

    ---@type LuaMulticastDelegate<fun(GestureData)>
    self.onGesturePinchEvent = LuaMulticastDelegate.new()
    
    ---@type LuaMulticastDelegate<fun(FVector2, Geometry, PointerEvent)>
    self.onMainTouchEndEvent = LuaMulticastDelegate.new()

    ---@type LuaMulticastDelegate<fun(FVector2, FVector2, Geometry, PointerEvent)>
    self.onMainTouchMovedEvent = LuaMulticastDelegate.new()
	
	---@type LuaMulticastDelegate<fun(FVector2, Geometry, PointerEvent)>
    self.onMainTouchBeginEvent = LuaMulticastDelegate.new()

    ---@type TouchFingerInfo[]
    self.TouchFingers = {}

    ---@type number 按下的手指数量
    self.PressedTouchCount = 0

    ---@type number 主手指索引
    self.MainTouchFingerIndex = 0

    ---@type table<GestureRecognizer, boolean> 手势组手指索引
    self.MapGestureRecognizer = {}

    ---@type GestureRecognizer[] 需要删除的手势组
    self.DeleteGestureRecognizers = {}

    ---@type boolean 是否启用触摸
    self.bEnableTouch = true
end

--- UI组件初始化，此处为自动生成
function UICompTouchRegion:InitUIComponent()

end

function UICompTouchRegion:IsHandleDevice()
    return PlatformUtil.IsMobilePlatform() or
        (C7FunctionLibrary.IsC7Editor() and UIFunctionLibrary.GetPreviewPlatform() ~= EDPIScalePreviewPlatforms.PC)
end

---UI事件在这里注册，此处为自动生成
function UICompTouchRegion:InitUIEvent()

	if self:IsHandleDevice() then
		self:AddUIEvent(self.view.OnTouchStartedEvent, "onTouchStartedEvent")
		self:AddUIEvent(self.view.OnTouchMovedEvent, "onTouchMovedEvent")
		self:AddUIEvent(self.view.OnTouchEndedEvent, "onTouchEndedEvent")
	else
		self:AddUIEvent(self.view.OnMouseButtonDownEvent, "onMouseButtonDownEvent")
		self:AddUIEvent(self.view.OnMouseMoveEvent, "onMouseButtonMoveEvent")
		self:AddUIEvent(self.view.OnMouseButtonUpEvent, "onMouseButtonUpEvent")
		--self:AddUIEvent(self.view.OnMouseLeaveEvent, "onMouseLeaveEvent")

		self:AddUIEvent(self.view.OnMouseWheelEvent, "onMouseWheelEvent")
	end

	self:AddUIEvent(self.view.OnDragDetectedEvent, "onDragDetectedEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UICompTouchRegion:InitUIView()
end

---@public 刷新
---@param param UICompTouchRegionParam
function UICompTouchRegion:OnRefresh(param)
    self.Param = param

    self.bEnableTouch = true
    self.Param.pinchCheckDuration = param.pinchCheckDuration or 1000
	self.Param.maxTouchCount = param.maxTouchCount or 2
	self.Param.gestureMask = self:IsHandleDevice() and param.gestureMask or 0
end

---@public 
function UICompTouchRegion:DisableTouch()
    self.bEnableTouch = false

    -- 清除手势组
    for gestureRecognizer, _ in pairs(self.MapGestureRecognizer) do
        self:UpdateGesture(gestureRecognizer)
         gestureRecognizer:EndGesture()
    end
    table.clear(self.MapGestureRecognizer)

    for _, touchFinger in pairs(self.TouchFingers) do
        self:ClearTouchInfo(touchFinger)
    end
end

---@public 
function UICompTouchRegion:EnableTouch()
    self.bEnableTouch = true
end

---@private 是否可以触发手势
---@param gestureType GestureRecognizer.GestureType 手势类型
---@return boolean
function UICompTouchRegion:CanTriggerGesture(gestureType)
    return (self.Param.gestureMask|gestureType ~= 0)
end

---@private 绑定手势组
---@param fingerIdx number 手指索引
function UICompTouchRegion:BindGestureGroup(fingerIdx)
	if self.Param.gestureMask == 0 then return end
	
    local curTouchFinger = self.TouchFingers[fingerIdx]
    for k, v in pairs(self.TouchFingers) do
        if k ~= fingerIdx then
            local gestureMask = 0
            local otherTouchFinger = self.TouchFingers[k]
            if otherTouchFinger.IsPressed then

                -- 检查是否可以触发捏合
                if self:CanTriggerGesture(GestureRecognizer.GestureType.PINCH) and 
                  (curTouchFinger.PressTime - otherTouchFinger.PressTime < self.Param.pinchCheckDuration) then
                    gestureMask = gestureMask | GestureRecognizer.GestureType.PINCH
                end

                -- todo...
            end

            if gestureMask ~= 0 then
                local gestureRecognizer = GestureRecognizer.new()
                gestureRecognizer:StartGesture({curTouchFinger, otherTouchFinger}, _G._now(), gestureMask)
                self.MapGestureRecognizer[gestureRecognizer] = true
            end
        end
    end
end

---@private 解绑手势组
---@param fingerIdx number 手指索引
function UICompTouchRegion:UnBindGestureGroup(fingerIdx)
    -- 遍历手势组，如果包含手指索引，则更新手势组，并删除手势组
    for gestureRecognizer, _ in pairs(self.MapGestureRecognizer) do
        if gestureRecognizer:ContainFinger(fingerIdx) then
            -- 尝试更新一次手势
            self:UpdateGesture(gestureRecognizer)
            gestureRecognizer:EndGesture()

            table.insert(self.DeleteGestureRecognizers, gestureRecognizer)
        end
    end

    -- 删除手势组
    for _, gestureRecognizer in pairs(self.DeleteGestureRecognizers) do
        self.MapGestureRecognizer[gestureRecognizer] = nil
    end
    table.clear(self.DeleteGestureRecognizers)
end

---@private 更新手势
---@param gestureRecognizer GestureRecognizer 手势组
function UICompTouchRegion:UpdateGesture(gestureRecognizer)
    local gestureType, gestureData = gestureRecognizer:UpdateGesture(_G._now())
    if gestureType == GestureRecognizer.GestureType.PINCH then
        self.onGesturePinchEvent:Broadcast(gestureData)
    end
end

---@private 更新手势组
---@param fingerIdx number 手指索引
function UICompTouchRegion:UpdateGestureGroupByFingerIdx(fingerIdx)
    for gestureRecognizer, _ in pairs(self.MapGestureRecognizer) do
        if gestureRecognizer:ContainFinger(fingerIdx) then
            self:UpdateGesture(gestureRecognizer)
        end
    end
end

---@private 更新有变动的手势
function UICompTouchRegion:UpdateGestureGroupIfChanged()
    local frame = KismetSystemLibrary.GetFrameCount()
    for gestureRecognizer, _ in pairs(self.MapGestureRecognizer) do
        local changed = false
        for _, finger in pairs(gestureRecognizer.GestureGroupFingers) do
            if finger.UpdateFrame == frame then
                changed = true
                break
            end
        end

        if changed then
            self:UpdateGesture(gestureRecognizer)
        end
    end
end

---@private 确保手指信息
---@param pointIdx number 手指索引
---@return TouchFingerInfo 手指信息
function UICompTouchRegion:EnsureTouchFinger(pointIdx)
    local touchInfo = self.TouchFingers[pointIdx]
    if not touchInfo then
        touchInfo = {FingerIdx = pointIdx}
        self.TouchFingers[pointIdx] = touchInfo
    end

    return touchInfo
end

---@private 记录输入信息
---@param myGeometry Geometry 几何信息
---@param inEvent PointerEvent 事件
function UICompTouchRegion:RecordTouchInfo(myGeometry, inEvent)
    local screenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inEvent)

    local pointIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inEvent)
    local maxTouchCount = self.Param.maxTouchCount
    if maxTouchCount and self.PressedTouchCount >= maxTouchCount then return end

    local touchInfo = self:EnsureTouchFinger(pointIndex)

    if not touchInfo.IsPressed then
        self.PressedTouchCount = self.PressedTouchCount + 1
        if self.PressedTouchCount == 1 then
            self.MainTouchFingerIndex = pointIndex
            self.onMainTouchBeginEvent:Broadcast(screenPos, myGeometry, inEvent)
		end
    end

    touchInfo.LastUpTime = touchInfo.PressTime or 0
    touchInfo.Pos = screenPos
    touchInfo.IsPressed = true
    touchInfo.PressTime = _G._now()
    touchInfo.Moved = false
    touchInfo.UpdateFrame = KismetSystemLibrary.GetFrameCount()
    touchInfo.IsDrag = false
	touchInfo.MouseButton = KismetInputLibrary.PointerEvent_GetEffectingButton(inEvent)

    self:BindGestureGroup(pointIndex)
end

---@private 更新输入信息
---@param myGeometry Geometry 几何信息
---@param inEvent PointerEvent 事件
function UICompTouchRegion:UpdateTouchInfo(myGeometry, inEvent)
    local pointIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inEvent)
    local touchInfo = self.TouchFingers[pointIndex]
    if not touchInfo or not touchInfo.IsPressed then return end

    local currentFrame = KismetSystemLibrary.GetFrameCount()
    if not self.Param.allowUpdateTimesInOneFrame and currentFrame == touchInfo.UpdateFrame then return end

    local oldPos = touchInfo.Pos
    touchInfo.Pos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inEvent)
    touchInfo.Moved = true
    touchInfo.UpdateFrame = currentFrame

    if pointIndex == self.MainTouchFingerIndex then
        self.onMainTouchMovedEvent:Broadcast(touchInfo.Pos, touchInfo.Pos - oldPos, myGeometry, inEvent)
    end

    self:UpdateGestureGroupByFingerIdx(pointIndex)
end

---@private 移除输入信息
---@param myGeometry Geometry 几何信息
---@param inEvent PointerEvent 事件
function UICompTouchRegion:RemoveTouchInfo(myGeometry, inEvent)
    local pointIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inEvent)
    local touchInfo = self.TouchFingers[pointIndex]
    if not touchInfo then return end

    self:ClearTouchInfo(touchInfo)

    if pointIndex == self.MainTouchFingerIndex then
		local pos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inEvent)
        self.onMainTouchEndEvent:Broadcast(pos, myGeometry, inEvent)
        self.MainTouchFingerIndex = nil
    end

    self:UnBindGestureGroup(pointIndex)
end

---@private 清除输入信息
function UICompTouchRegion:ClearTouchInfo(touchInfo)
	self.PressedTouchCount = math.max(self.PressedTouchCount - 1, 0)

	touchInfo.IsPressed = false
    touchInfo.UpdateFrame = KismetSystemLibrary.GetFrameCount()
    touchInfo.Moved = false
    touchInfo.IsDrag = false
	touchInfo.MouseButton = nil
end

---@private 获取触摸信息
---@param inEvent PointerEvent 事件
---@return TouchFingerInfo
function UICompTouchRegion:GetTouch(inEvent)
	local pointIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inEvent)
	local touchInfo = self.TouchFingers[pointIndex]
	if not touchInfo then return nil end
	
	return touchInfo
end

function UICompTouchRegion:onTouchStartedEvent(inGeometry, inGestureEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end
    self:RecordTouchInfo(inGeometry, inGestureEvent)

    local ret = WidgetBlueprintLibrary.DetectDragIfPressed(inGestureEvent, self.userWidget, UIFunctionLibrary.GetKeyFromName("LeftMouseButton"))
    return WidgetBlueprintLibrary.CaptureMouse(ret, self.userWidget)
end

function UICompTouchRegion:onTouchMovedEvent(inGeometry, inGestureEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end

    self:UpdateTouchInfo(inGeometry, inGestureEvent)
    return UIBase.HANDLED
end

function UICompTouchRegion:onTouchEndedEvent(inGeometry, inGestureEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end

    self:RemoveTouchInfo(inGeometry, inGestureEvent)
    return UIBase.HANDLED
end

function UICompTouchRegion:onDragDetectedEvent(_, pointEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end

    local pointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(pointEvent)
    local touchInfo = self:EnsureTouchFinger(pointerIndex)
    touchInfo.IsDrag = true
    return nil
end

function UICompTouchRegion:onMouseButtonDownEvent(myGeometry, inMouseEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end

	local Ekey = KismetInputLibrary.PointerEvent_GetEffectingButton(inMouseEvent)
    if Ekey.KeyName == "MiddleMouseButton" then
        if self.Param.mouseWheelSimPinch then
            self.SinPinchScale = 1.0
            return UIBase.HANDLED
        end
        -- 鼠标中键有InputAction事件，这里返回UNHANDLED让Input能够接收到相关事件
        return UIBase.UNHANDLED
    end

    self:RecordTouchInfo(myGeometry, inMouseEvent)
	
	local pointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inMouseEvent)
    if pointerIndex == 0 then
        -- 左键做一下拖拽检测
        return self:HandleDetectDragIfPressed(inMouseEvent, "LeftMouseButton")
    elseif pointerIndex == 10 then
		-- todo 这种代码其实挺垃圾的，暂时没想好咋搞
		Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, Enum.EInputType.RightMouse_Action, 0)
        -- 右键做一下拖拽检测
        return self:HandleDetectDragIfPressed(inMouseEvent, "RightMouseButton")
    end

    return UIBase.HANDLED
end

function UICompTouchRegion:onMouseButtonMoveEvent(myGeometry, inMouseEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end
	
    local touchInfo = self:GetTouch(inMouseEvent)
    if touchInfo and touchInfo.IsPressed then 
        if touchInfo.MouseButton and not KismetInputLibrary.PointerEvent_IsMouseButtonDown(inMouseEvent, touchInfo.MouseButton) then
            self:RemoveTouchInfo(myGeometry, inMouseEvent)
        else
            self:UpdateTouchInfo(myGeometry, inMouseEvent)
        end
    end

    return UIBase.UNHANDLED
end

function UICompTouchRegion:onMouseButtonUpEvent(myGeometry, inMouseEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end

    self:RemoveTouchInfo(myGeometry, inMouseEvent)

	return WidgetBlueprintLibrary.ReleaseMouseCapture(WidgetBlueprintLibrary.Unhandled())
end

--[[ 表现很奇怪，经过界面上方时会被界面中断掉
function UICompTouchRegion:onMouseLeaveEvent(inMouseEvent)
	if not self.bEnableTouch then return UIBase.UNHANDLED end

	self:RemoveTouchInfo(myGeometry, inMouseEvent)

	return WidgetBlueprintLibrary.ReleaseMouseCapture(WidgetBlueprintLibrary.Unhandled())
end
]]

function UICompTouchRegion:onMouseWheelEvent(myGeometry, inMouseEvent)
    if not self.bEnableTouch then return UIBase.UNHANDLED end

    if self.Param.mouseWheelSimPinch then
        local delta = KismetInputLibrary.PointerEvent_GetWheelDelta(inMouseEvent)
        self.SinPinchScale = self.SinPinchScale + delta/self.Param.mouseWheelSimPinchScale
        local gestureData = {
            type = GestureRecognizer.GestureType.PINCH,
            scale = self.SinPinchScale,
        }
        self.onGesturePinchEvent:Broadcast(gestureData)
        return UIBase.HANDLED
    end

    return UIBase.UNHANDLED
end

function UICompTouchRegion:HandleDetectDragIfPressed(inEvent, keyName)
    if not self.bEnableTouch then return UIBase.UNHANDLED end

    local ret = WidgetBlueprintLibrary.DetectDragIfPressed(inEvent, self.userWidget, UIFunctionLibrary.GetKeyFromName(keyName))
    return WidgetBlueprintLibrary.CaptureMouse(ret, self.userWidget)
end

return UICompTouchRegion
