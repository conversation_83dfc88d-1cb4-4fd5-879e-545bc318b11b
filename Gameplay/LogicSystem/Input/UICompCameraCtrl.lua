local UICompTouchRegion = kg_require("Gameplay.LogicSystem.Input.UICompTouchRegion")
local GestureRecognizer = kg_require("Gameplay.LogicSystem.Input.GestureRecognizer")

local KismetMathLibrary = import("KismetMathLibrary")

---@class UICompCameraCtrlParam : UICompTouchRegionParam
---@field ScaleRate number 缩放比例
---@field X_Scale number 旋转比例
---@field Y_Scale number 旋转比例
---@field SlideRate number 滑动比例
---@field CameraMoveSize FVector2 相机移动大小
---@field CameraDeadZoneOffset number 相机死区偏移

---@class UICompCameraCtrl : UICompTouchRegion
local UICompCameraCtrl = DefineClass("UICompCameraCtrl", UICompTouchRegion)

---初始化数据
function UICompCameraCtrl:InitUIData()
    UICompTouchRegion.InitUIData(self)

    ---@type LuaMulticastDelegate<fun(number)>
    self.onScaleEvent = LuaMulticastDelegate.new()

    ---@type LuaMulticastDelegate<fun(FVector2)>
    self.onRotateEvent = LuaMulticastDelegate.new()

end

--- UI组件初始化，此处为自动生成
function UICompCameraCtrl:InitUIComponent()
    UICompTouchRegion.InitUIComponent(self)
end

---UI事件在这里注册，此处为自动生成
function UICompCameraCtrl:InitUIEvent()
    UICompTouchRegion.InitUIEvent(self)

    self:AddUIEvent(self.onGesturePinchEvent, "onGesturePinchHandler")
    self:AddUIEvent(self.onMainTouchBeginEvent, "onMainTouchBeginHandler")
    self:AddUIEvent(self.onMainTouchMovedEvent, "onMainTouchMovedHandler")
    self:AddUIEvent(self.onMainTouchEndEvent, "onMainTouchEndHandler")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UICompCameraCtrl:InitUIView()
    UICompTouchRegion.InitUIView(self)
end

function UICompCameraCtrl:OnClose()
    self:onMainTouchEndHandler()
end

---@public 刷新
---@param param UICompCameraCtrlParam
function UICompCameraCtrl:OnRefresh(param)
    param = param or {}
    param.gestureMask = GestureRecognizer.GestureType.PINCH
    param.ScaleRate = param.ScaleRate or 100
    param.X_Scale = param.X_Scale or 0.3
    param.Y_Scale = param.Y_Scale or -0.1
    param.SlideRate = param.SlideRate or 50.0
    param.CameraMoveSize = param.CameraMoveSize or FVector2D(100, 100)
    param.CameraDeadZoneOffset = param.CameraDeadZoneOffset or 10

    UICompTouchRegion.OnRefresh(self, param)
end

function UICompCameraCtrl:onGesturePinchHandler(gestureData)
    local scale = gestureData.distance and gestureData.distance/self.Param.ScaleRate or gestureData.scale
    self:DoAddCameraScale(scale)
end

function UICompCameraCtrl:onMainTouchBeginHandler(screenPos)
    self.TouchBeginPos = screenPos
    self.CameraLastPos = screenPos
    self.DeadZoneOffset = FVector2D(0, 0)
    self.DeadZoneLastPos = screenPos
    self.bInDeadZone = true
end

function UICompCameraCtrl:onMainTouchMovedHandler(screenPos, delta)
    self:HandleCameraTouch(screenPos)
end

function UICompCameraCtrl:onMainTouchEndHandler()
    self:DoAddCameraRotate(FVector2D(0, 0))
end

function UICompCameraCtrl:HandleCameraTouch(screenPos)
    local cameraMoveSize = self.Param.CameraMoveSize
    local cameraDeadZoneOffset = self.Param.CameraDeadZoneOffset
    if self.bInDeadZone then
        self.DeadZoneOffset = self.DeadZoneOffset + screenPos - self.DeadZoneLastPos
        self.DeadZoneLastPos = screenPos
    else
        self.Offset = screenPos - self.CameraLastPos
        self.Offset.X = self.Offset.X / cameraMoveSize.X
        self.Offset.Y = (self.Offset.Y / cameraMoveSize.Y) * -1.0
    
        self:DoAddCameraRotate(self.Offset)
        self.CameraLastPos = screenPos
    end
    if self.bInDeadZone and KismetMathLibrary.Sqrt(self.DeadZoneOffset:SizeSquared()) > cameraDeadZoneOffset then
        self.bInDeadZone = false
        self.CameraLastPos = screenPos
    end
end

function UICompCameraCtrl:DoAddCameraRotate(screenPos)
    if self.onRotateEvent:IsBind() then
        self.onRotateEvent:Broadcast(screenPos)
    else
        self:AddCameraInput(screenPos)
    end
end

function UICompCameraCtrl:DoAddCameraScale(scale)
    if self.onScaleEvent:IsBind() then
        self.onScaleEvent:Broadcast(scale)
    else
        self:AddCameraScale(scale)
    end
end

---@private 添加相机缩放
function UICompCameraCtrl:AddCameraScale(scale)
    if scale == 0 then return end
    if scale > 1 then scale = 1 end
    if scale < -1 then scale = -1 end

    NotifyInputAxis("Zoom_Axis", scale)
end

---@private 添加相机旋转输入
function UICompCameraCtrl:AddCameraInput(inputVec)
    --Log.Debug(inputVec)
    --XY轴缩放比, 后面应从配置提取
    local Y_Scale = self.Param.Y_Scale
    local X_Scale = self.Param.X_Scale
    --滑动参数(影响手感, 先常量,后按属性配置)
    local SlideRate = self.Param.SlideRate
    local Y_Value = inputVec.Y * Y_Scale * SlideRate
    local X_Value = inputVec.X * X_Scale * SlideRate
    NotifyInputAxis("LookUp_Axis", Y_Value)
    NotifyInputAxis("Turn_Axis", X_Value)
    --发送相机旋转消息
    --Game.EventSystem:Publish(_G.EEventTypes.CameraInput, self)
end

return UICompCameraCtrl