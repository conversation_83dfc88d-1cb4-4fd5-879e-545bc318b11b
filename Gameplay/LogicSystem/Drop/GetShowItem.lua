local ItemRewardNew = kg_require "Gameplay.LogicSystem.Item.NewUI.ItemRewardNew"

---@begin define

-- GetShowItem_Param
---@class GetShowItem_Param : BagPanelItemBaseParam
---@field ItemID number 物品id
---@field Count number 数量
---@field IsBind number 是否绑定
---@field FreezeTime number 冻结时间
---@field ExpiryTime number 过期时间
---@field Quality number 品质
---@field SlotInfo table 槽位信息

---@end define

---@class GetShowItem: ItemRewardNew
local GetShowItem = DefineClass("GetShowItem", ItemRewardNew)
---------------------------------------------------------- 物品 --------------------------------------------------------------

---@param data GetShowItem_Param
function GetShowItem:OnRefresh(data)
	
	---@type GetShowItem_Param
	local param = data.tabData or data
	
	---@type GetShowItem_Param
	self.Param = param

	---填充道具信息
	local itemExcelData = Game.TableData.GetItemNewDataRow(param.ItemID)
	local quality = param.Quality or itemExcelData.quality
	local selected = self:IsSelected() and Enum.EItemSelectType.Selected or Enum.EItemSelectType.NotSelected
    self:FillItem(param.ItemID, Enum.CommonItemClickType.OverrideTip, 
		selected, param.Count, param.IsBind, quality, function()
			self:OnClick()
		end)

	---刷新CD的信息
	self:SetCoolDown(param.IsBind, param.FreezeTime)
	---刷新过期信息
	self:SetItemExpired(param.ExpiryTime)
end

---@function override
function GetShowItem:OnClick()
	local panel = self:GetBelongPanel()
	if panel then
		panel:OnClickItem(self.index)
	end

	if self.Param.SlotInfo then
		UI.ShowUI('BagItemTips_Panel', self.Param.ItemID, { SlotInfo = self.Param.SlotInfo, IsShowForwardBtn = false })
	else
		UI.ShowUI('BagItemTips_Panel', self.Param.ItemID, { IsShowForwardBtn = false })
	end
end

---更新选择的业务表现
---@field selected bool
function GetShowItem:UpdateSelectionState(selected)
	ItemRewardNew.UpdateSelectionState(self, selected)
	self:SetSelected(selected)
end

return GetShowItem