---@class TraceSender:SystemSenderBase 追踪图标管理
local TraceSender = DefineClass("TraceSender", SystemSenderBase)

--设置追踪req
---@param tracingType number @追踪类型枚举
---@param tracingValue string @追踪参数
function TraceSender:ReqSetTracing(tracingType, tracingValue)
    self.Bridge:ReqSetTracing(tracingType, tracingValue)
end

--移除追踪req
---@param tracingType number @追踪类型枚举
function TraceSender:ReqRemoveTracing(tracingType)
    self.Bridge:ReqRemoveTracing(tracingType)
end

--同步追踪信息
function TraceSender:ReqSyncTracingInfo()
    self.Bridge:ReqSyncTracingInfo()
end

return TraceSender