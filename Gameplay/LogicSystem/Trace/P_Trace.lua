local ESlateVisibility = import("ESlateVisibility")
local UIFunctionLibrary = import("UIFunctionLibrary")
local Const = kg_require("Shared.Const")
local KismetMathLibrary = import("KismetMathLibrary")
local StringConst = require "Data.Config.StringConst.StringConst"

local P_Trace= DefineClass("P_Trace",WorldWidgetCellBase)

function P_Trace:ctor()
	self.TargetLocation = nil
	self.Raidus  = nil
	self.bOnEdge = nil
	self.RemoveRadius = nil
	self.HideRadius = nil
	self.TickTimer = nil
	self.WorldID = nil
	self.PlaneID = nil
	self.Teleport = nil
	self.RingID = nil
end


function P_Trace:OnEdgeChanged(bOnEdge)
	if not bOnEdge then
		self.view.CP_SmallArrow:SetVisibility(ESlateVisibility.Collapsed)
		self.bOnEdge = false
	else
		self.bOnEdge = true
	end
end


function P_Trace:OnUninit()
	Log.Debug(" P_Trace:OnUninit()")
end

function P_Trace:OnInit(Params)

end

function P_Trace:OnGetFromPool(Params)
	self.TargetLocation = Params.TargetLocation
	self.RemoveRadius = Params.RemoveRadius
	self.HideRadius = Params.HideRadius
	self.TracingType = Params.TracingType
	self.MapID = Params.MapID
	self.Teleport = Params.Teleport
	self.MarkValue = Params.MarkValue
	self.RingID = Params.RingID

	self:InitTraceUIByTraceType()

	if self.bOnEdge == nil then
		self.bOnEdge = false
	else
		self.bOnEdge = self.bOnEdge
	end
	self.view.CP_SmallArrow:SetVisibility(ESlateVisibility.Collapsed)
	-- self.view.Img_LockMarkArrow:SetVisibility(ESlateVisibility.Visible)
	self.TickTimer = Game.TimerManager:CreateTimerAndStart(function()
		self:OnTick()
	end, 300, -1, nil, nil, false)
	
	-- todo:pengqi 这里更好的做法是把逻辑都停掉
	if Game.me.InBattle then
		self:Hide(ESlateVisibility.Collapsed)
	end

	self:PlayAnimation(self.view.Ani_AutoSwitch, nil, nil, nil, 0)
	Game.EventSystem:AddListener(_G.EEventTypes.ON_INBATTLE_CHANGED, self, self.OnPlayerInBattle, GetMainPlayerEID())
	Game.GlobalEventSystem:AddListener(EEventTypesV2.QUEST_ON_HUD_TASK_CLICK, "OnHudTaskClick",self)
end

function P_Trace:OnReturnToPool()
	if  self.TickTimer then
		Game.TimerManager:StopTimerAndKill(self.TickTimer)
	end
	Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function P_Trace:OnDirectionChanged(Direction)
	if not self.bOnEdge then
		return
	end
	if self.view.CP_SmallArrow:GetVisibility() ~= ESlateVisibility.HitTestInvisible then
		self.view.CP_SmallArrow:SetVisibility(ESlateVisibility.HitTestInvisible)
	end
	local Angle = import("UIFunctionLibrary").DotProductWithForward2D(Direction)
	Angle = Angle - 90
	self.view.CP_SmallArrow:SetRenderTransformAngle(Angle)
end

function P_Trace:IsWidgetVisible(Widget)
	local Visibility = Widget:GetVisibility()
	if
	(Visibility == ESlateVisibility.HitTestInvisible or Visibility == ESlateVisibility.SelfHitTestInvisible or
		Visibility == ESlateVisibility.Visible)
	then
		return true
	end
	return false
end

function P_Trace:GetSceneName()
	local mapID = self.mapID
	if not mapID then
		mapID =  Game.LevelManager.GetCurrentLevelID()
	end

	local LevelMapData = Game.TableData.GetLevelMapDataRow(mapID)
	if LevelMapData then
		self.name = LevelMapData.Name
		return self.name
	end
	self.name = ""
	return self.name
end

function P_Trace:checkInteract(Distance)
	return Game.TableData.GetConstDataRow("TASK_TRACE_HIDE_DISTANCE_DISPLAY_RANGE") > Distance
end

function P_Trace:OnTick(DeltaTime)
	if Game.me and Game.me.bInWorld then
		if Game.NewUIManager:IsShow(UIPanelConfig.PlayerTotal_Panel) then
			self:Hide(ESlateVisibility.Collapsed)
			return
		end
		
		local Distance = -1

		local sourcePos = Game.me.CppEntity:KAPI_GetLocation()

		if self.TargetLocation then
			Distance = KismetMathLibrary.Vector_Distance(sourcePos, self.TargetLocation)
		end
		local name
		if self.Teleport then
			name = StringConst.Get("MAP__HORSESTATION")
		else
			if Distance / 100 <= Game.TableData.GetConstDataRow("MAP_TRIGGER_DISTANCE") then
				local height
				if self.TargetLocation then
					height = sourcePos.Z - self.TargetLocation.Z
				end
				if math.abs(height) >= Game.TableData.GetConstDataRow("MAP_HEIGHT_DIFFERENCE") then
					if height > 0 then
						name = StringConst.Get("MAP_TRACE_DOWN")
					else
						name = StringConst.Get("MAP_TRACE_UP")
					end
				end
			end
			if not name then
				name = self:GetSceneName()
			end
		end
		-- Log.Warning("GetSceneName ", name or "nil")
		local distanceName = StringConst.Get("TASK_TRACE_DISTANCE")
		if name and name ~= "" then
			local length = string.utf8len(name)
			if length > 4 then
				name = string.format("%s...", string.utf8sub(name, 1, 3))
			end
			self.view.Text_Distance:SetText(string.format("%s%i%s",name, math.floor(Distance / 100), distanceName))
		else
			self.view.Text_Distance:SetText(string.format("%i%s", math.floor(Distance / 100), distanceName))
		end
		if not self.Teleport and self.RemoveRadius and self.RemoveRadius > 0 then
			if Distance < self.RemoveRadius  then
				Game.TraceSystem:RemoveTrace(self.TracingType)
			end
			return
		end

		if self.HideRadius and self.HideRadius > 0 then
			if self.HideRadius > Distance and self:IsWidgetVisible(self.userWidget) then
				self:Hide(ESlateVisibility.Collapsed)
			end

			if  self.HideRadius < Distance and not Game.me.InBattle and
				(self.userWidget:GetVisibility() == ESlateVisibility.Collapsed or
					self.userWidget:GetVisibility() == ESlateVisibility.Hidden)
			then
				self:Show(ESlateVisibility.HitTestInvisible)
			end
		elseif not self:IsWidgetVisible(self.userWidget) and not Game.me.InBattle then
			self:Show(ESlateVisibility.HitTestInvisible)
		end

		--添加冗余
		if self:checkInteract(Distance) then
			self.view.CP_Content:SetVisibility(ESlateVisibility.Collapsed)
		else
			self.view.CP_Content:SetVisibility(ESlateVisibility.HitTestInvisible)
		end
	end
end

function P_Trace:InitTraceUIByTraceType()
	if not self.TracingType then
		return
	end

	-- 染色、图标
	local TraceUIConfig = Game.TableData.GetTraceTypeInfoDataRow(self.TracingType)
	local HUDImage = TraceUIConfig["HUDImagePath"]
	if HUDImage and HUDImage ~= "" then
		self:SetImage(self.view.Img_LockMarkArrow, HUDImage, nil, nil, true)
		self.view.Img_LockMarkArrow:SetRenderScale(FVector2D(1.0, 1.0))
	end

	self:InitUIColor()

	-- Text设置
	local traceText
	if self.TracingType == Const.TRACING_INFO_TYPE.TASK or self.TracingType == Const.TRACING_INFO_TYPE.SECOND_TASK then
		local questType = Game.QuestSystem:GetQuestTypeByRingID(self.RingID)
		local cfg = Game.TableData.GetTaskMiniTypeDataRow(questType)
		if not cfg then
			Log.ErrorFormat("TaskMiniType can't find ID:%s", questType)
		else
			traceText = StringConst.Get(cfg.TitleText)
		end
	elseif self.TracingType == Const.TRACING_INFO_TYPE.SCENE_MARK then
		local groupIDBattleTagList = Game.GuildLeagueSystem:GetGroupBattleTagList(Game.me.groupID)
		if next(groupIDBattleTagList) == nil or groupIDBattleTagList[self.MarkValue] == nil then
			groupIDBattleTagList = Game.GuildLeagueSystem:GetGroupBattleTagList(Game.GuildLeagueSystem.AllGROUPID)
		end
		traceText = groupIDBattleTagList[self.MarkValue].customText
	elseif self.Teleport then
		traceText = StringConst.Get("MAP__HORSESTATION")
	end
	local ConfigText = TraceUIConfig["HUDTraceText"]
	if ConfigText and ConfigText ~= "" then
		traceText = ConfigText
	end
	if not traceText or traceText == "" then
		self.view.Text_TaskType:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Text_TaskType:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Text_TaskType:SetText(traceText)
	end
end

function P_Trace:InitUIColor()
	-- 染色 任务特殊处理
	local color = Game.TraceSystem:GetUIColorByTracingType(self.TracingType)
	if not color then color = FLinearColor(1.0, 1.0, 1.0, 1.0) end
	if self.TracingType == Const.TRACING_INFO_TYPE.TASK or self.TracingType == Const.TRACING_INFO_TYPE.SECOND_TASK then
		local icon = Game.QuestSystem:GetTaskRingIcon(self.RingID, true)
		self:SetImage(self.view.Img_LockMarkArrow, icon, nil, nil, true)
		-- 原图偏大了，特殊调整，如此做并不是很好
		self.view.Img_LockMarkArrow:SetRenderScale(FVector2D(0.6, 0.6))
		return
	end

	self.view.Img_LockMarkArrowSmall:SetColorAndOpacity(color)
	self.view.Img_LockMarkArrowSmallLight:SetColorAndOpacity(color)

	local TextColor = UIFunctionLibrary.LinerColorToSlateColor(color)
	self.view.Text_Distance:SetColorAndOpacity(TextColor)
	self.view.Text_TaskType:SetColorAndOpacity(TextColor)
end

function P_Trace:OnPlayerInBattle(entity, propName, bInBattle, oldValue)
	if bInBattle then
		self:Hide(ESlateVisibility.Collapsed)
	else
		self:Show(ESlateVisibility.HitTestInvisible)
		self:PlayAnimation(self.view.Ani_AutoSwitch, nil, nil, nil, 0)
	end
end

function P_Trace:OnHudTaskClick(ringID)
	if ringID and self.RingID == ringID then
		self:PlayAnimation(self.view.Ani_Circle, nil, nil, nil, 1)
	end
end

return P_Trace
