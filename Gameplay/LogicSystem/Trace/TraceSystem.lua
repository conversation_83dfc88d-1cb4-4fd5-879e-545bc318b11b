local Const = kg_require("Shared.Const")

---@type TraceTask
local TraceTask = kg_require("Gameplay.LogicSystem.Trace.TraceTask")
local BasePosTraceTask = kg_require("Gameplay.LogicSystem.Trace.TraceTypeTasks.BasePosTraceTask")
local QuestTraceTask = kg_require("Gameplay.LogicSystem.Trace.TraceTypeTasks.QuestTraceTask")
local TaskStatusConst = kg_require("Shared.Const.QuestConst").TaskStatusConst

---@brief 追踪任务类型枚举

---@class TraceSystem:SystemBase 追踪图标管理
local TraceSystem= DefineClass("TraceSystem", SystemBase)


TraceSystem.ACCOMPLISH_TARGET_ID = -1
TraceSystem.DynamicTraceID = 1
----------------------------Declare----------------------------
---@class CurrentTaskTraceData @任务追踪缓存结构
---@field TargetTraceTaskMap table<number, TraceTask[]> @以任务目标为索引的TraceTask�??
---@field TaskID number @任务ID
---@field TaskRingID number @任务环ID

---@class CurrentMapTraceData @地图追踪缓存结构
---@field CurrentTrace TraceTask @当前追踪TraceTask
---@field MapTagID number @地图TagID
---@field CustomID string @与MapTagID不能同时存在

---@class CurrentActivityTraceData @活动追踪缓存结构
---@field TargetTraceTaskMap table<number, TraceTask[]> @以任务目标为索引的TraceTask�??
---@field ActivityID number @活动ID
---@field TraceType number @追踪类型

---@class ChatTraceParam @聊天参数�??
---@field TargetPos number[] @pos
---@field levelID number @地图ID
---@field planeID number @位面ID

---@class CurrentChatTraceData @聊天追踪缓存结构
---@field TargetTraceTaskMap table<number, TraceTask[]> @以任务目标为索引的TraceTask�??
---@field ChatTraceParam ChatTraceParam @聊天参数�??

---@class TraceParam @追踪参数
---@field TraceTaskNPCIDs number[]|nil @NpcID列表
---@field CustomTagID string @地图自定义tagID
---@field TraceTaskMonsterIDs number[]|nil @monsterID列表
---@field TraceTaskSpiritual number[]|nil @灵视追踪列表
---@field TraceTargetSpawner string @追踪的SpawnerID
---@field WorldID number|nil @世界ID
---@field PlaneID number @位面ID
---@field Pos Vector3|nil @位置追踪
---@field RemoveRadius number @移除半径
---@field HideRadius number @隐藏半径
---@field ETraceType number @追踪目标类型枚举
---@field MapIDs number[] @地图�?
----------------------------Override----------------------------

TraceSystem.eventBindMap = {
	[EEventTypesV2.TASK_ON_NEW_TASK] = "OutRefreshTracing",
	[EEventTypesV2.LEVEL_ON_LEVEL_LOADED] = "OnLevelLoaded",
	[EEventTypesV2.LEVEL_ON_LEVEL_LOAD_START] = "removeCurrentTraces",
}

TraceSystem.decodeFun = {
	[Const.TRACING_INFO_TYPE.NONE] = function()
		return tonumber(value)
	end,
	[Const.TRACING_INFO_TYPE.TASK] = function(self, value)
		return tonumber(value)
	end,
	[Const.TRACING_INFO_TYPE.SECOND_TASK] = function(self, value)
		return tonumber(value)
	end,
	[Const.TRACING_INFO_TYPE.MAP] = function(self, value)
		return tonumber(value)
	end,
	[Const.TRACING_INFO_TYPE.DUNGEON_TASK] = function(self, value)
		return tonumber(value)
	end,
	[Const.TRACING_INFO_TYPE.ACTIVITY_HUD] = function(self, value)
		return tonumber(value)
	end,
	[Const.TRACING_INFO_TYPE.ACTIVITY_POPUP] = function(self, value)
		return tonumber(value)
	end,
	[Const.TRACING_INFO_TYPE.CHAT] = function(self, value)
		return 0
	end,
	[Const.TRACING_INFO_TYPE.MAP_TAG] = function(self, value)
		return value
	end,
	[Const.TRACING_INFO_TYPE.AXIS] = function(self, value)
		return value
	end,
    [Const.TRACING_INFO_TYPE.SCENE_MARK] = function(self, value)
		return value
	end,
}

function TraceSystem:onCtor()
	---@type TraceModel
	self.model = nil
	---@type TraceSender
    self.sender = nil
	
	---@type table<integer, string> @当前追踪类型与追踪值的映射关系
	self.CurrentTraceTypeToTraceValue = {}
	---@type table<TraceTask, boolean> @TraceTask缓存	
	self.CurrentTraces = {}
	---@type table @临时�??
	self.tempTab = {}

	---@type CurrentTaskTraceData @任务追踪缓存结构
	self.CurrentTaskTraceData = {}
	self.SecCurrentTaskTraceData = {}
	---@type CurrentMapTraceData @地图追踪缓存结构
	self.CurrentMapTraceData = {}
	---@type CurrentActivityTraceData @活动追踪缓存结构
	self.CurrentActivityTraceData = {}
	---@type CurrentChatTraceData @聊天追踪缓存结构
	self.CurrentChatTraceData = {}
	---轻量任务追踪
	self.CurrentTargetGuideTraceData = {}

	---@type SceneMarkTraceData @公会标记缓存结构
    self.SceneMarkTraceData = {}

	---@type table<number,TraceParam> @当前追踪参数后的解析
	self.currentTraceParams = {}
	
	--TODO: 场景特效相关参数
	self.TraceEffectAsset = nil
	self.TraceEffectHandle = nil
	self.TraceEffects = {}
end

function TraceSystem:onInit()
    ---@type TraceModel
    self.model = kg_require("Gameplay.LogicSystem.Trace.TraceModel").new()
    ---@type TraceSender
    self.sender = kg_require("Gameplay.LogicSystem.Trace.TraceSender").new()

	local param = self:OnBackToSelectRole(1)  --luacheck: ignore
end

--返回登录
function TraceSystem:OnBackToLogin()
	self:removeAllTaskTraces()
end

--返回选角
function TraceSystem:OnBackToSelectRole(param1)
	self:removeAllTaskTraces()
	return 1
end


--清理函数, 非destroy, 用于回收资源, 恢复到init状�?
function TraceSystem:onUnInit()
	self:clearAllTraceData()
end

----------------------private---------------------------
--region trace
---private 追踪配置解析（TODO:任务配置表重构后废弃�??
function TraceSystem:parseTraceIDInfo(traceIDTable)
    if traceIDTable == nil then
        return
    end
    local outTraceTable = {}
    local outRadiusTable = {}
    local outMonsterTable = {}
    for _, TraceIDString in pairs(traceIDTable) do
        local TraceIDs = string.split(TraceIDString, ",")
        if TraceIDs[1] == "" then
            TraceIDs[1] = 0
        end
        local SpawnerID = TraceIDs[1]
        local NPCID = tonumber(TraceIDs[2])
        local Radius = -1
        if TraceIDs[3] and TraceIDs[3] ~= "" then
            Radius = tonumber(TraceIDs[3])
        end
        local monsterID
        if TraceIDs[4] and TraceIDs[4] ~= "" then
            monsterID = tonumber(TraceIDs[4])
        end

        if SpawnerID and outTraceTable[SpawnerID] == nil then
            outTraceTable[SpawnerID] = {}
        end

        if SpawnerID then
            if outRadiusTable[SpawnerID] == nil then
                outRadiusTable[SpawnerID] = Radius
            else
                outRadiusTable[SpawnerID] = math.min(Radius, outRadiusTable[SpawnerID])
            end
        end
        if monsterID then
            if not outMonsterTable[SpawnerID] then
                outMonsterTable[SpawnerID] = {}
            end
            table.insert(outMonsterTable[SpawnerID], monsterID)
        end
        table.insert(outTraceTable[SpawnerID], NPCID)
    end
    return outTraceTable, outRadiusTable, outMonsterTable
end
--endregion trace

--region Activity
function TraceSystem:innerGenerateActivityTrace(traceIDTable,traceType)
	local TargetTraceTaskMap = self.CurrentActivityTraceData.TargetTraceTaskMap
	if not TargetTraceTaskMap then
		TargetTraceTaskMap = {}
		self.CurrentActivityTraceData.TargetTraceTaskMap = TargetTraceTaskMap
	end

	local TraceIDs, RadiusTable, OutMonsterTable = self:parseTraceIDInfo(traceIDTable)
	for SpawnerID, NpcIDs in pairs(TraceIDs) do
		local NewTraceTask = self:AddTraceTask({
			TraceTaskNPCIDs = NpcIDs,
			TraceTargetSpawner = SpawnerID,
			TraceTaskMonsterIDs = OutMonsterTable[SpawnerID],
			RemoveRadius = 0,
			HideRadius =RadiusTable[SpawnerID],
			ETraceType = traceType or Const.TRACING_INFO_TYPE.NONE
		})
		table.insert(TargetTraceTaskMap,NewTraceTask)
	end
end

function TraceSystem:removeAllActivityTraces()
	if self.CurrentActivityTraceData then
		if self.CurrentActivityTraceData.TargetTraceTaskMap then
			for K,Tasks in pairs(self.CurrentActivityTraceData.TargetTraceTaskMap) do
				self:removeTraceTask(Tasks)
			end
			table.clear(self.CurrentActivityTraceData.TargetTraceTaskMap)
		end
		self.CurrentActivityTraceData.ActivityID = nil
		self.CurrentActivityTraceData.TraceType = nil
	end
end

--刷新活动追踪
function TraceSystem:refreshActivityTrace(newTraceType, newActivityID)
	newActivityID = self:GetCurrentTracing(newTraceType)
	if not newActivityID then
		self:removeAllActivityTraces()
		return
	end
	newActivityID = tonumber(newActivityID)
	local activityTbData = Game.TableData.GetActivityDataRow(newActivityID)
	if not activityTbData then
		self:removeAllActivityTraces()
		return
	end
	if self.CurrentActivityTraceData.ActivityID == newActivityID then
		if self.CurrentActivityTraceData.TraceType == newTraceType then
			return
		end
	end
	self:removeAllActivityTraces()
	self.CurrentActivityTraceData.TraceType = newTraceType
	self.CurrentActivityTraceData.ActivityID = newActivityID
	if newTraceType == Const.TRACING_INFO_TYPE.ACTIVITY_HUD then
		self:innerGenerateActivityTrace(activityTbData.HudPushJumpToPara, newTraceType)
	elseif newTraceType == Const.TRACING_INFO_TYPE.ACTIVITY_POPUP then
		self:innerGenerateActivityTrace(activityTbData.PopUpJumpToPara, newTraceType)
	end
end
--endregion Activity


--region Task
function TraceSystem:innerRemoveTargetTrace(Tasks)
	for K,V in pairs(Tasks) do
		self:removeTraceTask(V)
	end
end

function TraceSystem:innerGenerateTargetTrace(taskRingID, targetIndex, traceType)
	local taskID = Game.QuestSystem:GetQuestIDByRingID(taskRingID)
	---@type TraceParam[]
	local traceParam = Game.QuestSystem:GetTargetTraceParam(taskID, targetIndex)
	
	table.insert(self.currentTraceParams[traceType], traceParam)

	local Tasks = {}
	
	for index, param in ipairs(traceParam) do
		param.TracingType = traceType
		local NewTraceTask = self:AddQuestTraceTask(param)
		table.insert(Tasks,NewTraceTask)
	end

	return Tasks
end

function TraceSystem:removeAllTaskTraces()
	if self.CurrentTaskTraceData and self.CurrentTaskTraceData.TargetTraceTaskMap then
		for K,Tasks in pairs(self.CurrentTaskTraceData.TargetTraceTaskMap) do
			self:innerRemoveTargetTrace(Tasks)
		end
		table.clear(self.CurrentTaskTraceData)
	end
end

function TraceSystem:removeAllSecTaskTraces()
	if self.SecCurrentTaskTraceData and self.SecCurrentTaskTraceData.TargetTraceTaskMap then
		for K,Tasks in pairs(self.SecCurrentTaskTraceData.TargetTraceTaskMap) do
			self:innerRemoveTargetTrace(Tasks)
		end
		table.clear(self.SecCurrentTaskTraceData)
	end
end

function TraceSystem:updataTaskTrace(traceType, ringID, taskTraceData)
	local questInfo = Game.QuestSystem:GetQuestInfo(ringID)

	--检查是否清理缓存数�?
	local NewTaskID = Game.QuestSystem:GetQuestIDByRingID(ringID)
	if ringID ~= taskTraceData.TaskRingID or taskTraceData.TaskID ~= NewTaskID then
		taskTraceData.TaskRingID = ringID
		taskTraceData.TaskID = NewTaskID
		if taskTraceData.TargetTraceTaskMap then
			for K,V in pairs(taskTraceData.TargetTraceTaskMap) do
				self:innerRemoveTargetTrace(V)
			end
		end
		taskTraceData.TargetTraceTaskMap = {}
	else
		--同一任务
		taskTraceData.TargetTraceTaskMap = taskTraceData.TargetTraceTaskMap or {}
	end

	--更新每个任务目标的追�?
	
	local bTaskAccepted = questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCEPTED
	self.currentTraceParams[traceType] = {}
	local conditionCfgs = Game.QuestSystem:GetConditionCfg(NewTaskID)
	if conditionCfgs then
		for conditionIndex = 1, #conditionCfgs, 1 do
			local bTargetCompleted = Game.QuestSystem:IsConditionCompleteByQuestID(NewTaskID, conditionIndex)
			if bTaskAccepted and (not bTargetCompleted) and (not taskTraceData.TargetTraceTaskMap[conditionIndex]) then
				taskTraceData.TargetTraceTaskMap[conditionIndex] = self:innerGenerateTargetTrace(ringID, conditionIndex, traceType)
			elseif (not bTaskAccepted or bTargetCompleted) and taskTraceData.TargetTraceTaskMap[conditionIndex] then
				self:innerRemoveTargetTrace(taskTraceData.TargetTraceTaskMap[conditionIndex])
			end
		end
	end
end

function TraceSystem:refreshTaskTrace(NewTraceType, OutNewTaskRingID)
	self.currentTraceParams[NewTraceType] = nil
	local NewTaskRingID = self:GetCurrentTracing(NewTraceType)
	if NewTaskRingID == nil then
		self:removeAllTaskTraces()
		return
	end
	NewTaskRingID = tonumber(NewTaskRingID)
	local questInfo = Game.QuestSystem:GetQuestInfo(NewTaskRingID)

	if (not questInfo) or Game.QuestSystem:IsRingFinished(NewTaskRingID) then
		self:removeAllTaskTraces()
		return
	end

	self:removeAllTaskTraces()
	self:updataTaskTrace(NewTraceType, NewTaskRingID, self.CurrentTaskTraceData)
end

function TraceSystem:refreshSecondTaskTrace(NewTraceType, NewTaskRingID)
	self.currentTraceParams[NewTraceType] = nil
	NewTaskRingID = self:GetCurrentTracing(NewTraceType)
	if NewTaskRingID == nil then
		self:removeAllSecTaskTraces()
		return
	end
	NewTaskRingID = tonumber(NewTaskRingID)
	local questInfo = Game.QuestSystem:GetQuestInfo(NewTaskRingID)

	if (not questInfo) or Game.QuestSystem:IsRingFinished(NewTaskRingID) then
		self:removeAllSecTaskTraces()
		return
	end

	self:removeAllSecTaskTraces()
	self:updataTaskTrace(NewTraceType, NewTaskRingID, self.SecCurrentTaskTraceData)
end

--endregion Task

--region Chat
function TraceSystem:innerGenerateChatTrace()
	local TargetTraceTaskMap = self.CurrentChatTraceData.TargetTraceTaskMap
	if not TargetTraceTaskMap then
		TargetTraceTaskMap = {}
		self.CurrentChatTraceData.TargetTraceTaskMap = TargetTraceTaskMap
	end

	local ChatTraceParam = self.CurrentChatTraceData.ChatTraceParam

	local traceParams = {}
	self.currentTraceParams[Const.TRACING_INFO_TYPE.CHAT] = traceParams
	
	local traceParam = {
		MapID = ChatTraceParam.levelID,
		Pos = FVector(ChatTraceParam.TargetPos[1], ChatTraceParam.TargetPos[2], ChatTraceParam.TargetPos[3]),
		RemoveRadius = 200,                                                                                                
		HideRadius = -1,
		TracingType = Const.TRACING_INFO_TYPE.CHAT,
		DynamicTraceID = TraceSystem.DynamicTraceID
	}
	TraceSystem.DynamicTraceID = TraceSystem.DynamicTraceID + 1
	traceParams[#traceParams+1] = traceParam
	
	local NewTraceTask = self:AddBasePosTraceTask(traceParam)
	table.insert(TargetTraceTaskMap,NewTraceTask)
end

function TraceSystem:removeAllChatTraces()
	if self.CurrentChatTraceData.TargetTraceTaskMap then
		for K,Tasks in pairs(self.CurrentChatTraceData.TargetTraceTaskMap) do
			self:removeTraceTask(Tasks)
		end
		table.clear(self.CurrentChatTraceData.TargetTraceTaskMap)
	end
end

--刷新聊天追踪
function TraceSystem:refreshChatTrace(newTraceType, OutnewTraceValue)
	local newTraceValue = self:GetCurrentTracing(newTraceType)
	if newTraceValue == nil then
		self:removeAllChatTraces()
		return
	end
	local ChatTraceParam = self.CurrentChatTraceData.ChatTraceParam
	if (not ChatTraceParam) or (not next(ChatTraceParam)) then
		self:removeAllChatTraces()
		return
	end

	self:removeAllChatTraces()
	self:innerGenerateChatTrace()
end
--endregion Chat

--region MapID
function TraceSystem:removeMapTraces(NewTraceType)
	if self.CurrentMapTraceData[NewTraceType] then
		self:removeTraceTask(self.CurrentMapTraceData[NewTraceType])
	end
end

function TraceSystem:refreshMapTrace(NewTraceType, TracingID)
	TracingID = self:GetCurrentTracing(NewTraceType)
	if not TracingID then
		self:removeMapTraces(NewTraceType)
		return
	end

	self:removeMapTraces(NewTraceType)

	local TraceData
	--TODO:
	if TracingID and (NewTraceType == Const.TRACING_INFO_TYPE.MAP)  then
		TraceData = Game.MapSystem:GetTagTraceData(tonumber(TracingID))
	else
		if TracingID and NewTraceType == Const.TRACING_INFO_TYPE.MAP_TAG then
			TraceData = Game.me.TracingCustomList[TracingID]
		end
	end

	-- Log.Warning("look TraceData")
	-- Log.Dump(TraceData)
	if TracingID and TraceData then
		local traceParams = {}
		if NewTraceType == Const.TRACING_INFO_TYPE.MAP then
			traceParams.TraceTaskNPCIDs = nil
			traceParams.Pos = TraceData.TraceLoaction
			traceParams.PlaneID = Game.MapSystem:GetCurrentPlaneID()
			traceParams.WorldID = TraceData.MapID
			traceParams.RemoveRadius = Game.TableData.GetConstDataRow("TASK_TRACE_HIDE_DISTANCE_DISPLAY_RANGE")
			traceParams.HideDistance = 0
			traceParams.TracingType = Const.TRACING_INFO_TYPE.MAP
			traceParams.DynamicTraceID = TraceSystem.DynamicTraceID
			TraceSystem.DynamicTraceID = TraceSystem.DynamicTraceID + 1
			self.currentTraceParams[NewTraceType] = traceParams
			self.CurrentMapTraceData[NewTraceType] = self:AddBasePosTraceTask(traceParams)
		elseif NewTraceType == Const.TRACING_INFO_TYPE.MAP_TAG then
			traceParams.CustomTagID = TraceData.ID
			traceParams.Pos = FVector(TraceData.X, TraceData.Y, 0)
			traceParams.WorldID = Game.TableData.GetMapDataRow(TraceData.MapID).LevelID
			traceParams.PlaneID = Game.MapSystem:GetCurrentPlaneID()
			traceParams.TagTypeID = TraceData.TagID
			traceParams.Text = TraceData.Text
			traceParams.HideDistance = 0
			traceParams.TracingType = Const.TRACING_INFO_TYPE.MAP_TAG
			traceParams.DynamicTraceID = TraceSystem.DynamicTraceID
			TraceSystem.DynamicTraceID = TraceSystem.DynamicTraceID + 1
			self.currentTraceParams[NewTraceType] = traceParams
			self.CurrentMapTraceData[NewTraceType] = self:AddBasePosTraceTask(traceParams)
		end
	end
end
--endregion Map

--region Common
function TraceSystem:removeTraceTask(TracedInst)
	TracedInst:Uninit()
	self.CurrentTraces[TracedInst] = nil
end

function TraceSystem:removeCurrentTraces()
	for K,V in pairs(self.CurrentTraces) do
		K:Uninit()
	end

	self:clearAllTraceData()
end

function TraceSystem:RefreshQuestTracing()
	local taskValue = self:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	local sec_TaskValue = self:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
	self:refreshTaskTrace(Const.TRACING_INFO_TYPE.TASK, taskValue)
	self:refreshSecondTaskTrace(Const.TRACING_INFO_TYPE.SECOND_TASK, sec_TaskValue)

	Game.GlobalEventSystem:Publish(EEventTypesV2.TRACE_ON_CURRENT_TRACE_CHANGED)
end

function TraceSystem:refreshTracing(tracingType, tracingInfo)
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.TASK then
		self:refreshTaskTrace(Const.TRACING_INFO_TYPE.TASK, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.SECOND_TASK then
		self:refreshSecondTaskTrace(Const.TRACING_INFO_TYPE.SECOND_TASK, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.MAP then
		self:refreshMapTrace(Const.TRACING_INFO_TYPE.MAP, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.MAP_TAG then
		self:refreshMapTrace(Const.TRACING_INFO_TYPE.MAP_TAG, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.ACTIVITY then
		self:refreshActivityTrace(Const.TRACING_INFO_TYPE.ACTIVITY, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.ACTIVITY_POPUP then
		self:refreshActivityTrace(Const.TRACING_INFO_TYPE.ACTIVITY_POPUP, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.CHAT then
		self:refreshChatTrace(Const.TRACING_INFO_TYPE.CHAT, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.AXIS then
		self:refreshAxisPointTrace(Const.TRACING_INFO_TYPE.AXIS, tracingInfo)
	end
	if not tracingType or tracingType == Const.TRACING_INFO_TYPE.SCENE_MARK then
		self:RefreshSceneMark(Const.TRACING_INFO_TYPE.SCENE_MARK, tracingInfo)
	end
	-- 轻量指引
	self:RefreshTargetGuideTrace()
	Game.GlobalEventSystem:Publish(EEventTypesV2.TRACE_ON_CURRENT_TRACE_CHANGED)
end

function TraceSystem:clearAllTraceData()
	table.clear(self.CurrentTaskTraceData)
	table.clear(self.CurrentMapTraceData)
	table.clear(self.CurrentActivityTraceData)
	table.clear(self.CurrentChatTraceData)
	table.clear(self.CurrentTraces)
	table.clear(self.CurrentTraceTypeToTraceValue)
end
--endregion Common
----------------------StaticFun---------------------------

----------------------CommonFunc----------------------
--region Task
function TraceSystem:GetLevelIDBySpawnerID(SpawnerID)
	local targetPos, LevelID, spawnerType
	targetPos, LevelID, spawnerType = self:GetTraceStaticLoaction(targetPos, SpawnerID)
	if LevelID and LevelID > 0 then
		return LevelID
	end
end

function TraceSystem:GetTraceStaticLoaction(targetPos, traceTargetSpawner)
    if traceTargetSpawner then
		local X, Y, Z, LevelID, spawnerType
		local triggerOrSpawner
		local Levels = Game.WorldDataManager:FindSceneActorBelongLevels(traceTargetSpawner)
		if Levels then
			for mapID, _ in ksbcpairs(Levels) do
				local SceneActorData = Game.WorldDataManager:RequireGetSceneActorData(mapID, traceTargetSpawner)
				if SceneActorData then
					LevelID = mapID
					triggerOrSpawner = SceneActorData
					local Location = SceneActorData.Transform.Position
					X, Y, Z = Location.X, Location.Y, Location.Z
					spawnerType = SceneActorData.ActorType -- @liufan EWActorType
					break
				end
			end
		end
		
		
		if triggerOrSpawner then
			if triggerOrSpawner.bTraceGround then
				if targetPos then
					targetPos.X = triggerOrSpawner.TraceGroundLocation.X
					targetPos.Y = triggerOrSpawner.TraceGroundLocation.Y
					targetPos.Z = triggerOrSpawner.TraceGroundLocation.Z
				end
			else
				if targetPos then
					targetPos.X = X
					targetPos.Y = Y
					targetPos.Z = Z
				end
			end
		end
        if LevelID == 0 then
            Log.Debug("Failed To Trace Spawner�?? ", traceTargetSpawner, "Cannot Find Target Spawner")
            return
        end
		return targetPos, LevelID, spawnerType
    end
end
--endregion Task

--region Chat
---@public 添加聊天追踪
function TraceSystem:AddChatTrace(posX, posY, posZ, levelID, planeID)
	--初始化地图追�??
	local ChatTraceParam = self.CurrentChatTraceData.ChatTraceParam
	if not ChatTraceParam then
		ChatTraceParam = {}
		self.CurrentChatTraceData.ChatTraceParam = ChatTraceParam
	end
	table.clear(ChatTraceParam)
	ChatTraceParam.TargetPos = {posX, posY, posZ}
	ChatTraceParam.levelID = levelID
	ChatTraceParam.planeID = planeID
	self:refreshChatTrace(Const.TRACING_INFO_TYPE.CHAT, 1)
end
--endregion Chat

---region AxisPointTrace
function TraceSystem:AddAxisPointTrace(PosX, PosY, LevelID, PlaneID)
	Game.TraceSystem:SetTrace(
		Const.TRACING_INFO_TYPE.AXIS, string.format("AxisPoint_%d_%d_%d_%d", PosX,PosY, LevelID, PlaneID)
	)
end

function TraceSystem:removeAxisPointTrace()
	if self.CurrentAxisPointTraceData then
		self:removeTraceTask(self.CurrentAxisPointTraceData)
	end
	self.CurrentAxisPointTraceData = nil
end

function TraceSystem:refreshAxisPointTrace(NewTraceType, TracingID)
	TracingID = self:GetCurrentTracing(NewTraceType)
	self:removeAxisPointTrace()
	if NewTraceType ~= Const.TRACING_INFO_TYPE.AXIS then return end
	-- Log.Warning("refreshAxisPointTrace TracingID", TracingID or "nil")
	if not TracingID or tonumber(TracingID) == 0 then return end
	local Splited = string.split(TracingID, "_")
	local traceParams = {
		Pos = FVector(tonumber(Splited[2]), tonumber(Splited[3]), 0),
		MapID = tonumber(Splited[4]),
		RemoveRadius = Game.TableData.GetConstDataRow("TASK_TRACE_HIDE_DISTANCE_DISPLAY_RANGE"),
		HideDistance = 0,
		DynamicTraceID = TraceSystem.DynamicTraceID,
		TracingType = Const.TRACING_INFO_TYPE.AXIS
	}
	self.currentTraceParams[Const.TRACING_INFO_TYPE.AXIS] = {}
	table.insert(self.currentTraceParams[Const.TRACING_INFO_TYPE.AXIS], traceParams)
	self.CurrentAxisPointTraceData = self:AddBasePosTraceTask(traceParams)
	TraceSystem.DynamicTraceID = TraceSystem.DynamicTraceID + 1

end
---end reg


function TraceSystem:RefreshTargetGuideTrace()
	local traceParams = Game.TargetGuideSystem:GetTraceParam()
	self:removeAllTargetGuideTrace()

	local Tasks = {}
	if traceParams and #traceParams > 0 then
		for index, param in ipairs(traceParams) do
			local NewTraceTask = self:AddTraceTask(param)
			table.insert(Tasks,NewTraceTask)
		end
	end
	self.CurrentTargetGuideTraceData = Tasks
end

function TraceSystem:removeAllTargetGuideTrace()
	if self.CurrentTargetGuideTraceData then
		for _, traceTask in pairs(self.CurrentTargetGuideTraceData) do
			self:removeTraceTask(traceTask)
		end
		self.CurrentTargetGuideTraceData = nil
	end
end

--region MapID
function TraceSystem:GetCurrentTraceTaskLocations()
	local taskLocationInfos = {}
	Log.ErrorFormat("TaskManager已过时，先去掉")
	return taskLocationInfos
end
--endregion MapID

--region SceneMark
function TraceSystem:RefreshSceneMark(SceneMarkType, TagID)
    if Game.me == nil then
        return 
    end
	TagID = self:GetCurrentTracing(SceneMarkType)
    if TagID == nil and self.SceneMarkTraceData.TraceTask then
        self:removeTraceTask(self.SceneMarkTraceData.TraceTask)
        return
    end
    if Game.NetworkManager.GetLocalSpace() == nil or Game.NetworkManager.GetLocalSpace().CommandSystemMarkInfo == nil then
        if self.SceneMarkTraceData.TraceTask then
            self:removeTraceTask(self.SceneMarkTraceData.TraceTask)
        end
        return 
    end
    if TagID ~= nil and TagID ~= "" then
		local MarkMap = Game.GuildLeagueSystem:GetMarkMapInfo()
        if self.SceneMarkTraceData.TraceTask then
            self:removeTraceTask(self.SceneMarkTraceData.TraceTask)
        end
        local Info = MarkMap[tonumber(TagID)]
        if Info then
            local traceParams = {
                MarkValue = tonumber(TagID), 
                Pos = FVector(Info.position[1], Info.position[2], Info.position[3]),
                MapID = Game.LevelManager.GetCurrentLevelID(),
                RemoveRadius = 0,
                HideDistance = 0,
                DynamicTraceID = TraceSystem.DynamicTraceID,
                TracingType = Const.TRACING_INFO_TYPE.SCENE_MARK
            }
			TraceSystem.DynamicTraceID = TraceSystem.DynamicTraceID + 1
            self.SceneMarkTraceData.TraceTask = self:AddBasePosTraceTask(traceParams)
        end
    else
        if self.SceneMarkTraceData.TraceTask then
            self:removeTraceTask(self.SceneMarkTraceData.TraceTask)
        end
    end
end
--endregion SceneMark

--region Common
function TraceSystem:AddTraceTask(TraceParams)
	local TraceTaskInst = TraceTask.new()
	TraceTaskInst:Init(TraceParams)
	self.CurrentTraces[TraceTaskInst] = true
	return TraceTaskInst
end

function TraceSystem:AddQuestTraceTask(TraceParams)
	TraceParams.DynamicTraceID = TraceSystem.DynamicTraceID
	TraceSystem.DynamicTraceID = TraceSystem.DynamicTraceID + 1
	local TraceTaskInst = QuestTraceTask.new()
	TraceTaskInst:Init(TraceParams)
	self.CurrentTraces[TraceTaskInst] = true
	return TraceTaskInst
end

function TraceSystem:AddBasePosTraceTask(TraceParams)
	TraceParams.DynamicTraceID = TraceSystem.DynamicTraceID
	TraceSystem.DynamicTraceID = TraceSystem.DynamicTraceID + 1
	local TraceTaskInst = BasePosTraceTask.new()
	TraceTaskInst:Init(TraceParams)
	self.CurrentTraces[TraceTaskInst] = true
	return TraceTaskInst
end

function TraceSystem:GetCurrentTracing(tracingType)
	local tracingInfo = self.CurrentTraceTypeToTraceValue[tracingType]
	if tracingInfo == nil then
		return nil
	end
	return self.decodeFun[tracingType](self, tracingInfo)
end

function TraceSystem:GetCurrentTraceParam(tracingType)
	if not tracingType then
		return self.currentTraceParams
	end
	return self.currentTraceParams[tracingType]
end

function TraceSystem:SetTrace(tracingType, tracingValue)
	self.sender:ReqSetTracing(tracingType, tostring(tracingValue))
end

function TraceSystem:RemoveTrace(TracingType)
	self.sender:ReqRemoveTracing(TracingType)
end

----------------------Event---------------------------
function TraceSystem:RetSetTrace(ErrCode, TraceType, TraceID)
	--参数无意义，仅用来通知刷新（老代码，待删除）
	if ErrCode == Game.NetworkManager.ErrCodes.NO_ERR then
		-- self.model.traceID = TraceID
		-- self.model.traceType = TraceType
		self:refreshTracing()
    end
end

function TraceSystem:OnTracingInfoSet(traceInfoType, traceValue)
	self.CurrentTraceTypeToTraceValue[traceInfoType] = traceValue
	self:refreshTracing(traceInfoType, traceValue)
end

function TraceSystem:OnTracingInfoRemove(traceInfoType)
	self.CurrentTraceTypeToTraceValue[traceInfoType] = nil
	self:refreshTracing(traceInfoType, nil)
end

function TraceSystem:OnLevelLoaded()
	--self:refreshTracing()
	self.sender:ReqSyncTracingInfo()
end

-- 所有追踪信息同步
function TraceSystem:OnTracingInfosSync(TracesInfos)
	for _, traceInfoData in pairs(TracesInfos) do
		for _, traceInfoTypeData in pairs(traceInfoData) do
			self.CurrentTraceTypeToTraceValue[traceInfoTypeData.TracingType] = traceInfoTypeData.TracingValue 
		end
	end
	self:refreshTracing()
end

function TraceSystem:refreshSpiritVisionTrace(traceType, traceValue)
	
end

function TraceSystem:refreshAllTracing(traceType, traceValue)
	self:refreshSimpleTracing(traceType, traceValue)
end

function TraceSystem:OutRefreshTracing()
	self:refreshTracing()
end

--- 按照地图系统暂时的设计
function TraceSystem:GenerateMapTaskID(TagPrefix, DynamicTraceID, TraceType, idx)
	return TagPrefix .. "_" .. DynamicTraceID .. "_" .. TraceType .. "_" .. idx
end

function TraceSystem:AnalyseMapTaskID(mapTaskID)
	local result = string.split(mapTaskID, "_")
	-- 只有TraceType用的上
	return tonumber(result[3])
end

function TraceSystem:GetUIColorByTracingType(TracingType)
	local TraceUIConfig = Game.TableData.GetTraceTypeInfoDataRow(TracingType)
	local color, outlineColor  -- luacheck: ignore
	if TracingType == Const.TRACING_INFO_TYPE.TASK or TracingType == Const.TRACING_INFO_TYPE.SECOND_TASK then
		local TraceValue = self.CurrentTraceTypeToTraceValue[TracingType]
		if TraceValue == nil then
			return nil
		end
		local QuestType = Game.QuestSystem:GetQuestTypeByRingID(tonumber(TraceValue))
		color, outlineColor = Game.QuestSystem:GetTraceIconColorByTaskType(QuestType)
	else
		color = Game.ColorManager:GetColor("TraceColor", TraceUIConfig["ImageColor"], Game.ColorManager.Type.LinearColor)
	end
	return color
end

function TraceSystem:IsTracedNPC(NpcID)
	local TracePrams = self.currentTraceParams[Const.TRACING_INFO_TYPE.TASK]
	local secondPrams = self.currentTraceParams[Const.TRACING_INFO_TYPE.SECOND_TASK]
	local fstRing = self:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	local sndRing = self:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
	if self:checkTracedNpcByParams(TracePrams, NpcID) then
		return true, tonumber(fstRing)
	end
	if self:checkTracedNpcByParams(secondPrams, NpcID) then
		return true, tonumber(sndRing)
	end
	return false, nil
end

function TraceSystem:checkTracedNpcByParams(tracePrams, NpcID)
	if not tracePrams then
		return false
	end
	for index, params in ipairs(tracePrams) do
		for _, param in pairs(params) do
			if param.TemplateID == NpcID then
				return true
			end
		end
	end
end

function TraceSystem:GetTraceUIHUDImgPathByTracingType(TracingType)
	local TraceUIConfig = Game.TableData.GetTraceTypeInfoDataRow(TracingType)
	return TraceUIConfig["HUDImagePath"]	
end

function TraceSystem:OnNPCVisibleChange(uid, bVisible)
	local entity = Game.EntityManager:getEntity(uid)
	if entity.isNpc == true then
		if self:IsTracedNPC(entity.TemplateID) then
			for taskInst, _ in pairs(self.CurrentTraces) do
				taskInst:TryRefreshNPCHudTrace(entity, bVisible)
			end
		end
	end
end

return TraceSystem