---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by admin.
--- DateTime: 12/20/2023 10:13 AM
---



local ScreenshotUtil = DefineSingletonClass("ScreenshotUtil")


function ScreenshotUtil:ctor()
    self.Widget = nil
	self.OnScreenCapturedCallBacKObj = nil
	self.OnScreenCapturedCallBackName = nil
    self.DM = nil
    self.DMRef = nil
end

function ScreenshotUtil:Init()
    self.UKGScreenshotUtility = import("KGScreenshotUtility")(GetContextObject())
    if self.UKGScreenshotUtility then
        --self.UKGScreenshotUtilityRef = UnLua.Ref(self.UKGScreenshotUtility)
        self.UKGScreenshotUtility.OnScreenShotCapturedDelegate:Bind(function(...)
            self:OnScreenShotCaptured(...)
        end)
    end
    self.bInCapture = false
	self.OnScreenCapturedCallBacKObj = nil
	self.OnScreenCapturedCallBackName = nil
    self.bSaveAsPNG = false
end

function ScreenshotUtil:UnInit()
    --UnLua.Unref(self.UKGScreenshotUtility )
    self.UKGScreenshotUtilityRef = nil
    self.UKGScreenshotUtility = nil
    self.DM = nil
    self.DMRef = nil
end

function ScreenshotUtil:ResetCapture()

    self.bInCapture = false

	self.OnScreenCapturedCallBacKObj = nil
	self.OnScreenCapturedCallBackName = nil
end

--function ScreenshotUtil:TakeScreenShot(OnScreenCapturedCallBack,bShowUI,FileName)
--    if self.UKGScreenshotUtility and not self.bInCapture then
--        local bSUcc = self.UKGScreenshotUtility:TakeScreenShot(bShowUI or false, FileName or "")
--        if bSUcc then
--            self.bInCapture = true
--            self.OnScreenCapturedCallBack = OnScreenCapturedCallBack
--            return true
--        end
--    end
--    return false
--end

function ScreenshotUtil:TakeScreenShot(Obj,CallBackName,bShowUI,FileName)
	if self.UKGScreenshotUtility and not self.bInCapture then
		local bSUcc = self.UKGScreenshotUtility:TakeScreenShot(bShowUI or false, FileName or "")
		if bSUcc then
			self.bInCapture = true
			self.OnScreenCapturedCallBacKObj = Obj
			self.OnScreenCapturedCallBackName = CallBackName
			return true
		end
	end
	return false
end

function ScreenshotUtil:RegisterExtraOnScreenCapturedCallback(callbackObj, callbackName)
	self.extraCallbackObj = callbackObj
	self.extraCallbackName = callbackName
end

function ScreenshotUtil:UnregisterExtraOnScreenCapturedCallback()
	self.extraCallbackObj = nil
	self.extraCallbackName = nil
end

function ScreenshotUtil:OnScreenShotCaptured(Texture,SizeX,SizeY)
    --local TestW = slua.loadObject("/Game/Blueprint/3C/Camera/Test.Test_C")
    --if TestW then
    --    local Widget = import("WidgetBlueprintLibrary").Create( _G.GetContextObject(),TestW)
    --    Widget:AddToViewport()
    --
    --    local M = Widget.C7Image_27:GetDynamicMaterial()
    --    M:SetTextureParameterValue("InTex", Texture)
    --end


    Log.DebugFormat("[ScreenshotUtil:OnScreenShotCaptured] %s, Size: %s x %s",tostring(Texture),tostring(SizeX),tostring(SizeY))
    if IsValid_L(Texture) then
		local Obj = self.OnScreenCapturedCallBacKObj
		local CBName =self.OnScreenCapturedCallBackName
        if 	CBName and Obj and 	Obj[CBName] then
            xpcall(Obj[CBName],_G.CallBackError,Obj,Texture,SizeX,SizeY)
        end

		local extraCallbackObj = self.extraCallbackObj
		local extraCallbackName = self.extraCallbackName
		if extraCallbackObj and extraCallbackName and extraCallbackObj[extraCallbackName] then
			xpcall(extraCallbackObj[extraCallbackName],_G.CallBackError, extraCallbackObj)
		end
    end

    self:ResetCapture()
end

--local WaterMarkMat = "/Game/Arts/UI_2/Blueprint/Camera/WaterMask_Mat.WaterMask_Mat"
--function ScreenshotUtil:GetWaterMarkDM()
--    if not self.DM or  IsValid_L(self.DM) then
--        self.DM = nil
--        self.DMRef = nil
--        local Mat = slua.loadObject(WaterMarkMat)
--        if Mat then
--            self.DM = import("KismetMaterialLibrary").CreateDynamicMaterialInstance(GetContextObject(), Mat, "", import("EMIDCreationFlags").Transient)
--            self.DMRef = UnLua.Ref(self.DM)
--        end
--    end
--    return self.DM
--end


function ScreenshotUtil:AddWaterMark(Texture,MarkTexture,Loc,WaterMarkSize)
    self.UKGScreenshotUtility:AddWaterMark(Texture,MarkTexture,Loc,WaterMarkSize)
end


function ScreenshotUtil:SaveTexture(RT2D,inFilePath,inFileName)
    if  IsValid_L(RT2D) then
        local FilePath =  import("KismetSystemLibrary").GetProjectSavedDirectory()
        local FileName = "ScreenShot" .. tostring(_G._now()) ..".png"
        import("KismetRenderingLibrary").ExportRenderTarget(GetContextObject(), RT2D, FilePath,FileName)
        return import("C7FunctionLibrary").PathCombine(FilePath, FileName)
    end
end

function ScreenshotUtil:SaveTextureToPath(RT2D, FilePath, FileName)
	if IsValid_L(RT2D) then
		local savedPath =  import("KismetSystemLibrary").GetProjectSavedDirectory()..FilePath.."/"
		import("KismetRenderingLibrary").ExportRenderTarget(GetContextObject(), RT2D, savedPath, FileName)
		return import("C7FunctionLibrary").PathCombine(savedPath,FileName)
	end
end


return ScreenshotUtil
