local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local SkillCustomizerItemModifier = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomizerListItems.SkillCustomizerItemModifier")

---技能按钮
---技能按钮用在了多个地方，这里作为新的UIListView的元素
---@class SkillItem_2 : UIListItem
---@field view SkillItem_2Blueprint
local SkillItem_2 = DefineClass("SkillItem_2", UIListItem)
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"

local ESlateVisibility = import("ESlateVisibility")

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillItem_2:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillItem_2:InitUIData()
	--技能ID
	---@type number
	self.SkillID = -1
	---@type number
	self.RoleSkillID = -1
	---@type number
	self.FellowID = nil
	
	--按钮是否被选中
	---@type boolean
	self.bSelected = false

	--缓存当前状态下的按钮图标路径
	---@type string
	self.SkillIconPath = ""
	--自身的技能槽位
	---@type number
	self.SlotID = -1

	--是否显示进阶tag
	---@type boolean
	self.bShowStageTag = false;
end

--- UI组件初始化，此处为自动生成
function SkillItem_2:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SkillItem_2:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillItem_2:InitUIView()
end

---面板打开的时候触发
function SkillItem_2:OnRefresh(ListData)
	self:InitSkillContent(ListData.SkillID, ListData.RoleSkillID, ListData.FellowID, ListData.SlotID, ListData.SelectSkillID)
end

------------------------------------------------------------------------------------------------------------------------------------------------------

---设置技能按钮的固定内容，如技能本身的图标
---@param SkillID number 技能ID
---@param RoleSkillID number 技能解锁信息ID
---@param FellowID number 伙伴ID
---@param SlotID number 槽位（仅在轮盘生效）
---@param SelectSkillID number 当前状态主界面选定的技能ID
function SkillItem_2:InitSkillContent(SkillID, RoleSkillID, FellowID, SlotID, SelectSkillID)
	self.SkillID = SkillID
	self.RoleSkill = RoleSkillID
	self.FellowID = FellowID
	self.SlotID = SlotID

	SkillID = self.SlotID and SkillImpl.GetMainChaSkillIDBySlot(self.SlotID) or SkillID
	local SkillData = Game.TableData.GetSkillDataNewRow(SkillID)
	
	--加载图标和按钮基础状态
	if SkillData then
		local IconPath = SkillData.SkillIcon

		if IconPath ~= "" then
			local res = self:ConvertSkillIconPath(SkillData)
			IconPath = res
		end
		
		self.view.Img_IconSkill_lua:SetVisibility(ESlateVisibility.Visible)
		if IconPath ~= self.SkillIconPath then
			self.SkillIconPath = IconPath
			self:SetImage(self.view.Img_IconSkill_lua, IconPath)
		end
	else
		self.view.Img_IconSkill_lua:SetVisibility(ESlateVisibility.Collapsed)
	end

	--技能名称等
	self:SetSkillStaticContent()
end

function SkillItem_2:ConvertSkillIconPath(tSkillData)
	--禁止拼接
	--local out = string.gsub(string.gsub(InPath, '/Sprite01/', '/Texture01/'), '_Sprite', '')
	if string.notNilOrEmpty(tSkillData.IconTexture) then
		return tSkillData.IconTexture
	end
	return tSkillData.SkillIcon
end

---设置技能的基本信息
---该方法由SkillCustomizerRouletteItem自动调用，可以覆写
function SkillItem_2:SetSkillStaticContent()
	local SkillData = Game.TableData.GetSkillDataNewRow(self.SkillID)
	
	self.view.Text_Name_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.Text_NamType_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Text_Name_lua:SetText(SkillData.Name)
	self.view.Text_Level_lua:SetText(Game.SkillCustomSystem:GetSkillLevel(self.SkillID))
	self.userWidget:SetStage(0)
	SkillCustomizerItemModifier:Item_Normal(self)
	self:UpdateSkillAdvance()
end

---更新技能的进阶信息
function SkillItem_2:UpdateSkillAdvance()
	if Game.SkillCustomSystem:IsSkillAdvanceSupported(self.RoleSkillID) then
		self.bShowStageTag = true
		self.view.Text_Tag:SetText(Game.SkillCustomSystem.SkillUpgradeLevelText[Game.SkillCustomSystem:GetSkillAdvanceLevel(self.RoleSkillID)])
	else
		self.bShowStageTag = false
	end
end

------------------------------------------------------------------------------------------------------------------------------------------------------

---点击事件
function SkillItem_2:OnClick()
	Game.SkillCustomSystem:ShowSkillDescUI(self.SkillID, true)
end

--- 此处为自动生成
function SkillItem_2:on_Btn_ClickArea_lua_Clicked()
	self:OnClick()
end

return SkillItem_2
