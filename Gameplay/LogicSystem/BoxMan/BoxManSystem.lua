---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by tang<PERSON><PERSON><PERSON>.
--- DateTime: 2025/7/2

---@class BoxManSystem:SystemBase
local BoxManSystem = DefineSingletonClass("BoxManSystem", SystemBase)

function BoxManSystem:Init()
	
end

function BoxManSystem:OnBoxManPanelOpen()
	
end

-- 根据地块ID获取地块的蓝图路径
function BoxManSystem:GetGridBPPath(gridID)
	if not gridID then
		Log.DebugWarning("BoxManSystem:GetGridBPPath not gridID", gridID)
		return
	end
	local gridCfgRow = Game.TableData.GetBoxManGridCfgRow(gridID)
	if not gridCfgRow then
		Log.DebugWarning("BoxManSystem:GetGridBPPath not gridCfgRow", gridID)
		return
	end
	local gridBPCfgRow = Game.TableData.GetBoxManGridBPCfgRow(gridCfgRow.GridType)
	if not gridBPCfgRow then
		Log.DebugWarning("BoxManSystem:GetGridBPPath not gridBPCfgRow", gridCfgRow.gridType)
		return
	end
	return gridBPCfgRow.BPPath
end

-- 根据道具ID获取道具的蓝图路径
function BoxManSystem:GetItemBPPath(itemID)
	if not itemID then
		Log.DebugWarning("BoxManSystem:GetItemBPPath not itemID", itemID)
		return
	end
	local itemCfgRow = Game.TableData.GetBoxManItemCfgRow(itemID)
	if not itemCfgRow then
		Log.DebugWarning("BoxManSystem:GetItemBPPath not itemCfgRow", itemID)
		return
	end
	local itemBPCfgRow = Game.TableData.GetBoxManItemBPCfgRow(itemCfgRow.ItemType)
	if not itemBPCfgRow then
		Log.DebugWarning("BoxManSystem:GetItemBPPath not itemBPCfgRow", itemCfgRow.itemType)
		return
	end
	return itemBPCfgRow.BPPath
end

-- 根据道具ID获取道具能否被踩踏
function BoxManSystem:GetItemCanBeStepOn(itemID)
	if not itemID then
		Log.DebugWarning("BoxManSystem:GetItemCanBeStepOn not itemID", itemID)
		return
	end
	local itemCfgRow = Game.TableData.GetBoxManItemCfgRow(itemID)
	if not itemCfgRow then
		Log.DebugWarning("BoxManSystem:GetItemCanBeStepOn not itemCfgRow", itemID)
		return
	end
	return itemCfgRow.CanBeStepOn
end

function BoxManSystem:UnInit()

end

return BoxManSystem