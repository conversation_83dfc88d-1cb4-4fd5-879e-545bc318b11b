local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local BoxMan_Role = kg_require("Gameplay.LogicSystem.BoxMan.BoxMan_Role")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local BoxMan_DirectionBtn = kg_require("Gameplay.LogicSystem.BoxMan.BoxMan_DirectionBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
---@class BoxMan_Panel : UIPanel
---@field view BoxMan_PanelBlueprint
local BoxMan_Panel = DefineClass("BoxMan_Panel", UIPanel)
local FuncUtils = kg_require("Shared.Utils.FuncUtils")

BoxMan_Panel.MAXROWCOUNT = 9
BoxMan_Panel.MAXCOLUMNCOUNT = 8
BoxMan_Panel.MAXCOUNT = BoxMan_Panel.MAXROWCOUNT * BoxMan_Panel.MAXCOLUMNCOUNT
BoxMan_Panel.VectorCenter = FVector2D(0.5, 0.5)
-- 移动方向枚举
BoxMan_Panel.E_DirectionType =
{
	Up = "Up",
	Down = "Down",
	Left = "Left",
	Right = "Right",
}

BoxMan_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BoxMan_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BoxMan_Panel:InitUIData()
	-- 玩家所在的网格index
	self.PlayerLocatedIndex = 1
	-- 根据index对应信息
	self.AllItemInfoDic = {}
end

--- UI组件初始化，此处为自动生成
function BoxMan_Panel:InitUIComponent()
    ---@type UIListView childScript: BoxMan_GroundItem
    self.TileView_GroundCom = self:CreateComponent(self.view.TileView_Ground, UIListView)
    ---@type BoxMan_Role
    self.WBP_BoxMan_RoleCom = self:CreateComponent(self.view.WBP_BoxMan_Role, BoxMan_Role)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
    ---@type BoxMan_DirectionBtn
    self.WBP_BoxMan_DirectionBtnRightCom = self:CreateComponent(self.view.WBP_BoxMan_DirectionBtnRight, BoxMan_DirectionBtn)
    ---@type BoxMan_DirectionBtn
    self.WBP_BoxMan_DirectionBtnLeftCom = self:CreateComponent(self.view.WBP_BoxMan_DirectionBtnLeft, BoxMan_DirectionBtn)
    ---@type BoxMan_DirectionBtn
    self.WBP_BoxMan_DirectionBtnDownCom = self:CreateComponent(self.view.WBP_BoxMan_DirectionBtnDown, BoxMan_DirectionBtn)
    ---@type BoxMan_DirectionBtn
    self.WBP_BoxMan_DirectionBtnUpCom = self:CreateComponent(self.view.WBP_BoxMan_DirectionBtnUp, BoxMan_DirectionBtn)
end

---UI事件在这里注册，此处为自动生成
function BoxMan_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBackTitleCom.onPreCloseEvent, "on_WBP_ComBackTitleCom_PreCloseEvent")
    self:AddUIEvent(self.WBP_ComBackTitleCom.onTipClickEvent, "on_WBP_ComBackTitleCom_TipClickEvent")
    self:AddUIEvent(self.WBP_BoxMan_DirectionBtnLeftCom.onClickEvent, "on_WBP_BoxMan_DirectionBtnLeftCom_ClickEvent")
    self:AddUIEvent(self.WBP_BoxMan_DirectionBtnRightCom.onClickEvent, "on_WBP_BoxMan_DirectionBtnRightCom_ClickEvent")
    self:AddUIEvent(self.WBP_BoxMan_DirectionBtnUpCom.onClickEvent, "on_WBP_BoxMan_DirectionBtnUpCom_ClickEvent")
    self:AddUIEvent(self.WBP_BoxMan_DirectionBtnDownCom.onClickEvent, "on_WBP_BoxMan_DirectionBtnDownCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BoxMan_Panel:InitUIView()
	self.view.WBP_BoxMan_Role:SetVisibility(FuncUtils:BoolToVisible(false))
	self.GridWidth = self.view.TileView_Ground:GetEntryWidth()
	self.PlayerSlot = self.view.WBP_BoxMan_Role.Slot
end

---面板打开的时候触发
function BoxMan_Panel:OnRefresh(playID, levelID)
	self.PlayID = playID or 101
	self.LevelID = levelID or 1001
	self.PlayCfg = Game.TableData.GetBoxManPlayCfgRow(self.PlayID)
	self.LevelCfg = Game.TableData.GetBoxManLevelCfgRow(self.LevelID)
	self:InitGridLayout()
	self:StartTimer("ItemLayoutTimer", function()
		self:InitItemRootPos()
		self:InitItemLayout()
		self:InitPlayerLocation()
	end, 200, 1)
end

-- 初始化地块
function BoxMan_Panel:InitGridLayout()
	local data = self.LevelCfg.GridLayoutCfg
	-- 存放地块数据
	self.GridDatas = {}
	local index = 0
	for _, rowData in ksbcipairs(data) do
		for _, value in ksbcipairs(rowData) do
			local gridItem = {}
			index = index + 1
			gridItem.ID = index
			gridItem.GridID = value
			table.insert(self.GridDatas, gridItem)
		end
	end
	self.TileView_GroundCom:Refresh(self.GridDatas)
end

function BoxMan_Panel:InitItemRootPos()
	--local itemRootGeo = self.view.Canvas_PropRoot:GetCachedGeometry()
	--self.ItemRootPos = itemRootGeo:GetAbsolutePosition()
	self.ItemRootPos = UIHelper.GetAbsPos(self.view.Canvas_PropRoot, BoxMan_Panel.VectorCenter)
end

-- 初始化道具层
function BoxMan_Panel:InitItemLayout()
	Log.Debug("BoxMan_Panel:InitItemLayout")
	local data = self.LevelCfg.ItemLayoutCfg
	local index = 0
	for _, rowData in ksbcipairs(data) do
		for _, value in ksbcipairs(rowData) do
			index = index + 1
			local gridItem = self.TileView_GroundCom:GetItemByIndex(index)
			local targetPos = UIHelper.GetAbsPos(gridItem.view.GridRoot)
			local localPos = SlateBlueprintLibrary.AbsoluteToLocal(self.view.Canvas_PropRoot:GetCachedGeometry(), targetPos)
			local itemInfo = {}
			itemInfo.Index = index
			itemInfo.GridItem = gridItem
			itemInfo.Pos = localPos
			itemInfo.PlayerCanStepOn, itemInfo.ItemCanStepOn = gridItem:CanStepOn()
			if value ~= 0 then
				local bpPath = Game.BoxManSystem:GetItemBPPath(value)
				self:LoadRes(bpPath, function(res)
					local widget = WidgetBlueprintLibrary.Create(_G.GetContextObject(), res)
					local slot = self.view.Canvas_PropRoot:AddChildToCanvas(widget)
					local NewAnchors = UE.Anchors()
					NewAnchors.Minimum = BoxMan_Panel.VectorCenter
					NewAnchors.Maximum = BoxMan_Panel.VectorCenter
					slot:SetAnchors(NewAnchors)
					--slot:SetOffsets(UE.Margin(0, 0, 0, 0))
					slot:SetAlignment(BoxMan_Panel.VectorCenter)
					slot:SetPosition(localPos)
					--itemInfo.OriginBoxItem = widget
					itemInfo.BoxItem = widget
				end, false)
			end
			self.AllItemInfoDic[index] = itemInfo
		end
	end
end

-- 初始化玩家位置
function BoxMan_Panel:InitPlayerLocation()
	local pointCfg = self.LevelCfg.StartEndPointCfg
	local startPoint = pointCfg[1]
	self.PlayerLocatedIndex = startPoint
	local itemInfo = self.AllItemInfoDic[startPoint]
	if itemInfo and itemInfo.Pos then
		self.PlayerSlot:SetPosition(itemInfo.Pos)
		self.view.WBP_BoxMan_Role:SetVisibility(FuncUtils:BoolToVisible(true))
	end
end

function BoxMan_Panel:PlayerMove(dir)
	Log.Debug("BoxMan_Panel:PlayerMove", dir)
	local canMove, nextIndex = self:CheckPlayerCanMoveNext(dir)
	if canMove then
		local itemInfo = self.AllItemInfoDic[nextIndex]
		self.PlayerLocatedIndex = nextIndex
		self.PlayerSlot:SetPosition(itemInfo.Pos)
	end
end

-- 玩家不能移动遇到了阻碍（无论阻碍能否被推挤玩家都不能触发本次移动）
function BoxMan_Panel:CheckPlayerCanMoveNext(dir)
	local index = self:GetNextIndex(dir)
	local canMove = false
	if index >= 1 and index <= BoxMan_Panel.MAXCOUNT then
		local itemInfo = self:GetItemInfoByIndex(index)
		if itemInfo.BoxItem then
			
		else
			canMove = itemInfo.PlayerCanStepOn
		end
	end
	return canMove, index
end

-- 按9*8搜索nextIndex
function BoxMan_Panel:GetNextIndex(dir)
	local index = 0
	if dir == BoxMan_Panel.E_DirectionType.Up then
		index = self.PlayerLocatedIndex - BoxMan_Panel.MAXROWCOUNT
	elseif dir == BoxMan_Panel.E_DirectionType.Down then
		index = self.PlayerLocatedIndex + BoxMan_Panel.MAXROWCOUNT
	elseif dir == BoxMan_Panel.E_DirectionType.Left then
		-- 在最左侧
		if self.PlayerLocatedIndex % BoxMan_Panel.MAXROWCOUNT ~= 1 then
			index = self.PlayerLocatedIndex - 1
		end
	elseif dir == BoxMan_Panel.E_DirectionType.Right then
		-- 在最右侧
		if self.PlayerLocatedIndex % BoxMan_Panel.MAXROWCOUNT ~= 0 then
			index = self.PlayerLocatedIndex + 1
		end
	end
	return index
end

function BoxMan_Panel:GetItemInfoByIndex(index)
	return self.AllItemInfoDic[index]
end

function BoxMan_Panel:CheckPushBox(dir, index)
	local itemInfo = self:GetItemInfoByIndex(index)
	-- 先判断上面有没有道具
	if itemInfo and itemInfo.BoxItem then
		
	end
end

function BoxMan_Panel:GetPlayerLocatedItemInfo()
	
end

--- 此处为自动生成
---@return bool
function BoxMan_Panel:on_WBP_ComBackTitleCom_PreCloseEvent()
	self:CloseSelf()
end

--- 此处为自动生成
function BoxMan_Panel:on_WBP_ComBackTitleCom_TipClickEvent()
end

--- 此处为自动生成
function BoxMan_Panel:on_WBP_BoxMan_DirectionBtnRightCom_ClickEvent()
	self:PlayerMove(BoxMan_Panel.E_DirectionType.Right)
end

--- 此处为自动生成
function BoxMan_Panel:on_WBP_BoxMan_DirectionBtnLeftCom_ClickEvent()
	self:PlayerMove(BoxMan_Panel.E_DirectionType.Left)
end

--- 此处为自动生成
function BoxMan_Panel:on_WBP_BoxMan_DirectionBtnUpCom_ClickEvent()
	self:PlayerMove(BoxMan_Panel.E_DirectionType.Up)
end

--- 此处为自动生成
function BoxMan_Panel:on_WBP_BoxMan_DirectionBtnDownCom_ClickEvent()
	self:PlayerMove(BoxMan_Panel.E_DirectionType.Down)
end

return BoxMan_Panel
