local MailMask = kg_require("Gameplay.LogicSystem.Mail.MailMask")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class MailDetail_Panel : UIPanel
---@field view MailDetail_PanelBlueprint
local MailDetail_Panel = DefineClass("MailDetail_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local CustomMail = kg_require("Gameplay.LogicSystem.Mail.CustomMail")

MailDetail_Panel.eventBindMap = {
    [EEventTypesV2.FAV_ITEM_ADD] = "OnMailAddFav",
    [EEventTypesV2.FAV_ITEM_DELETE] = "OnMailremoveFav",
    [EEventTypesV2.MAIL_SHOW_DETAIL] = "OnMailShowDetail",

}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MailDetail_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MailDetail_Panel:InitUIData()
    self.params = {}
    self.MailId = nil
    self.isFav = false
    self.bCollect = false
    self.catDoubleClicked = false
    self.catClicked = false
    self.lastClickTime = nil
    self.autoEvent = nil
    self.tmpSize = FVector2D(88,92)
    self.isNewMail = false

    self.cachedComponent = {}

end

--- UI组件初始化，此处为自动生成
function MailDetail_Panel:InitUIComponent()
    ---@type MailMask
    self.WBP_MailMaskCom = self:CreateComponent(self.view.WBP_MailMask, MailMask)
    ---@type UIListView
    self.RewardListCom = self:CreateComponent(self.view.RewardList, UIListView)
    ---@type UIComButton
    self.Btn_ReceiveCom = self:CreateComponent(self.view.Btn_Receive, UIComButton)
    ---@type UIComButton
    self.CloseCom = self:CreateComponent(self.view.Close, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function MailDetail_Panel:InitUIEvent()
    self:AddUIEvent(self.CloseCom.onClickEvent, "on_CloseCom_ClickEvent")
    self:AddUIEvent(self.Btn_ReceiveCom.onClickEvent, "on_Btn_ReceiveCom_ClickEvent")
    self:AddUIEvent(self.view.WBP_MailCollectBtn.Btn_ClickArea.OnClicked, "on_WBP_MailCollectBtnBtn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Content.OnRichTextLinkEvent, "on_Content_RichTextLinkEvent")
    self:AddUIEvent(self.view.Btn_ClickArea_CatIdle.OnClicked, "on_Btn_ClickArea_CatIdle_Clicked")
    self:AddUIEvent(self.RewardListCom.onItemClicked, "on_RewardListCom_ItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MailDetail_Panel:InitUIView()
end

---面板打开的时候触发
function MailDetail_Panel:OnRefresh(params, content, customData, isfav, bCollect, isNewMail)
    Game.NewUIManager:HidePanel(UIPanelConfig.MailDetail_Panel, UIPanelConfig.HUD_Panel)
    self:RefreshData(params, content, customData, isfav, bCollect, isNewMail)
    self:ShowFadeIn(params.category)

    local catInTime = self.view.SpineWidget_catread:GetAnimationDuration("Ani_Fadein")
    self.view.Btn_ClickArea_CatIdle:SetVisibility(ESlateVisibility.Collapsed)
    self.view.SpineWidget_catread:SetAnimation(0, "Ani_Fadein",false)
    self:PlayCatAudio(Enum.EAudioConstData.MAIL_CAT_IN)

    self:StopTimer("catIn")
    --self:StopTimer("catClickWait")
    self:StopTimer("catDoubleClick")
    self:StartTimer("catClick", function ()
        self.view.SpineWidget_catread:SetAnimation(0, "Ani_Loop",true)
        self.view.Btn_ClickArea_CatIdle:SetVisibility(ESlateVisibility.Visible)
    end, catInTime * 1000, 1)
end

function MailDetail_Panel:OnClose()
    self.MailId = nil
    Game.NewUIManager:ShowPanel(UIPanelConfig.MailDetail_Panel, UIPanelConfig.HUD_Panel)
    Game.GlobalEventSystem:Publish(EEventTypesV2.MAIL_DETAIL_CLOSE)
end


--- 此处为自动生成
function MailDetail_Panel:on_CloseCom_ClickEvent()
    self:CloseSelf()
end

--- 此处为自动生成
function MailDetail_Panel:on_Btn_ReceiveCom_ClickEvent()
    self:ReceiveOrDelete()
end

--- 邮件背景类型
MailDetail_Panel.MAIL_DETAIL_BG_TYPE = {
    DEFAULT = 0,
    SYSTEM = 1,
    GUILD = 2,
    NPC = 3,
}

function MailDetail_Panel:SetBackground(type)
    if type > MailDetail_Panel.MAIL_DETAIL_BG_TYPE.NPC then
        type = MailDetail_Panel.MAIL_DETAIL_BG_TYPE.SYSTEM
    end
    self.view.WBP_MailMask:SetType(type)
end

function MailDetail_Panel:RefreshData(params, content, customData, isfav, bCollect, isNewMail)
    self.params = params
    self.isFav = isfav
    self.isNewMail = isNewMail or false
    
    self.bCollect = bCollect
    self:SetBackground(params.category)
    if self.MailId ~= params.id then
        self.MailId = params.id
        local data = Game.TableData.GetSystemMailDataRow(params.templateId)
        local title = ""
        if params.title and params.title ~= "" then
            title = params.title
        elseif data then
            title = data.title
        end
        self.view.Title:SetText(title)
        self.view.Content:SetText(content or params.content or "")
        
        -- Setup custom data from mail data if not provided
        local hasCustomData = customData ~= nil
        if not hasCustomData then
            customData = {}
            if data and data.SpeechPath and data.SpeechPath ~= "" then
                customData.voice = data.SpeechPath
            end
            if data and data.PicturePath and data.PicturePath ~= "" then
                customData.imgPath = data.PicturePath
            end
        end
        
        self:MailLoadWidget(customData)
        self:SetSenderName(params, data)
        
        if params.createTime then
            self.view.Time:SetText(Game.TimeUtils.FormatDateTimeString(params.createTime*1000))
        end
    end
    
    if not bCollect then
        if isfav then
            self.view.WBP_MailCollectBtn:Event_UI_Style(2)
        else
            self.view.WBP_MailCollectBtn:Event_UI_Style(1)
        end
        if params.type == Game.MailSystem.attachmentTag.ATTACHMENT then
            self.view.RewardList:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.RewardListCom:Refresh(self:buildRewardList(params.reward, params.isExtracted))
            if params.isExtracted then
                self:SetupReceiveButton(false, "MAIL_DELETE")
            else
                self:SetupReceiveButton(true, "MAIL_RECEIVE")
            end
        else
            self.view.RewardList:SetVisibility(ESlateVisibility.Collapsed)
            self:SetupReceiveButton(false, "MAIL_DELETE")
        end

        if params.destroyTime and params.destroyTime < _G._now() /1000 + 88914000 then
            self.view.Expire_Desc:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.view.Expire_Desc:SetText(Game.TimeUtils.FormatCountDownString(params.destroyTime * 1000 - _G._now(), true))
        else
            self.view.Expire_Desc:SetVisibility(ESlateVisibility.Collapsed)
        end
        self.view.WBP_MailCollectBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.WBP_MailCollectBtn:SetVisibility(ESlateVisibility.Collapsed)
        self:SetupReceiveButton(false, "MAIL_COLLECTION_REMOVE_SINGLE")
        self.view.Expire_Desc:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function MailDetail_Panel:ShowFadeIn(category)
    self:StopAllAnimations(self.userWidget)
    if category == Game.MailSystem.MailCategory.System then
        self:PlayAnimation(self.view.Ani_Mail_System_in, nil, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif category == Game.MailSystem.MailCategory.Guild then
        self:PlayAnimation(self.view.Ani_Mail_Ocean_in, nil, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif category == Game.MailSystem.MailCategory.Npc then
        self:PlayAnimation(self.view.Ani_Mail_NPC_in, nil, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self:PlayAnimation(self.view.Ani_Mail_Normal_in, nil, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function MailDetail_Panel:OnMailShowDetail(params, content, customData, isfav, bCollect, isNewMail)
    self:OnRefresh(params, content, customData, isfav, bCollect, isNewMail)
end


--- 此处为自动生成
function MailDetail_Panel:on_WBP_MailCollectBtnBtn_ClickArea_Clicked()
    if not self.isFav then
        if self.params.type == Game.MailSystem.attachmentTag.ATTACHMENT and not self.params.isExtracted then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MAIL_COLLECT_ITEM)
            return
        end
        Game.MailSystem:ReqMailFavAdd(self.MailId)
    else
        self:removeMailFromFav()
    end
end

function MailDetail_Panel:removeMailFromFav()
    if not self.bCollect then
        Game.MailSystem:ReqMailFavRemove(self.MailId)
        return
    end

    local isMailExistInInbox = Game.MailSystem:IsMailExist(self.MailId)
    if isMailExistInInbox then
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.MAIL_REMOVE_FROM_FAV, function()
            Game.MailSystem:ReqMailFavRemove(self.MailId)
        end)
        return
    end

    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.MAIL_REMOVE_FROM_FAV_DELETE, function()
        Game.MailSystem:ReqMailFavRemove(self.MailId)
    end)
end


--- 此处为自动生成
---@param url FString
---@param beginIndex int
---@param endIndex int
---@param clickPosition FVector2D
function MailDetail_Panel:on_Content_RichTextLinkEvent(url, beginIndex, endIndex, clickPosition)
    Game.AllInSdkManager:DirectlyOpenWebviewUrl(url)
end

--- 此处为自动生成
function MailDetail_Panel:on_Btn_ClickArea_CatIdle_Clicked()
    self:OnCatClicked()
end

--- 此处为自动生成
---@param index number
---@param data table
function MailDetail_Panel:on_RewardListCom_ItemClicked(index, data)
end


function MailDetail_Panel:OnMailAddFav(MailId)
    if self.MailId == MailId then
        self.isFav = true
        self.view.WBP_MailCollectBtn:Event_UI_Style(2)
    end
end

function MailDetail_Panel:OnMailremoveFav(MailId)
    if self.MailId == MailId then
        self.isFav = false
        self.view.WBP_MailCollectBtn:Event_UI_Style(1)
    end
end

function MailDetail_Panel:OnMailItemReceived(MailId)
    if self.MailId == MailId then
        self.RewardListCom:Refresh(self.params.reward)
    end
end

function MailDetail_Panel:CloseDetail()
    self.MailId = nil
    self:StopCatAudio()
    Game.NewUIManager:ShowPanel(UIPanelConfig.MailDetail_Panel, UIPanelConfig.HUD_Panel)
    self:Hide()
end

function MailDetail_Panel:ReceiveOrDelete()
    if not self.bCollect then
        if self.params.type == Game.MailSystem.attachmentTag.ATTACHMENT and not self.params.isExtracted then
            Game.MailSystem:ExtractMail(self.MailId)
        else
            Game.MailSystem:DeleteMail(self.MailId)
        end
    else
        self:removeMailFromFav()
    end
end

function MailDetail_Panel:OnCatClicked()
    if not self.catDoubleClicked then
        local clickAniTime = 0
        if self.lastClickTime and _G._now() - self.lastClickTime <= 200 then
            self:StopTimer("catClick")
            self.catDoubleClicked = true
            clickAniTime = self.view.SpineWidget_catread:GetAnimationDuration("Ani_DoubleClick")
            self.view.SpineWidget_catread:SetAnimation(0, "Ani_DoubleClick",false)
            self:PlayCatAudio(Enum.EAudioConstData.MAIL_CAT_DOUBLECLICK)
            self:StartTimer("catDoubleClick", function ()
                self.view.SpineWidget_catread:SetAnimation(0, "Ani_Loop", true)
                self.catClicked = false
                self.catDoubleClicked = false
                self.lastClickTime = nil
            end,clickAniTime * 1000,1)
        elseif not self.lastClickTime then
            self.lastClickTime = _G._now()
            self.catClicked = true
            clickAniTime = self.view.SpineWidget_catread:GetAnimationDuration("Ani_Click")      
            self.view.SpineWidget_catread:SetAnimation(0, "Ani_Click", false)
            self:PlayCatAudio(Enum.EAudioConstData.MAIL_CAT_CLICK)
            self:StartTimer("catClick",function ()
                self.view.SpineWidget_catread:SetAnimation(0, "Ani_Loop", true)
                self.catClicked = false
                self.catDoubleClicked = false
                self.lastClickTime = nil
            end,clickAniTime*1000,1)
            -- self:StartTimer("catClickWait", function ()
            --     self.catClicked = true
            --     clickAniTime = self.View.SpineWidget_catread:GetAnimationDuration("Ani_Click")      
            --     self.View.SpineWidget_catread:SetAnimation(0, "Ani_Click", false)
            --     self:StartTimer("catClick",function ()
            --         self.View.SpineWidget_catread:SetAnimation(0, "Ani_Loop", true)
            --         self.catClicked = false
            --         self.catDoubleClicked = false
            --         self.lastClickTime = nil
            --     end,clickAniTime*1000,1)
            -- end, 200, 1)
        end
    end
end

function MailDetail_Panel:PlayCatAudio(eventConst)
    self:StopCatAudio()
    self.autoEvent = Game.AkAudioManager:PostEvent2D(eventConst)
end

function MailDetail_Panel:StopCatAudio()
    if self.autoEvent then
        Game.AkAudioManager:StopEvent(self.autoEvent)
        self.autoEvent = nil
    end
end

function MailDetail_Panel:LoadWidget(type, slot, data)
    if self.cachedComponent[type] then
        local component = self.cachedComponent[type]
        component:Show()
        self:RefreshMailSubWidget(type, data)
        return
    end
    
    local component = self:SyncLoadComponent(type, slot)
    self:OnSubWidgetReady(component, type, slot, data)
end

function MailDetail_Panel:OnSubWidgetReady(component, type, slot, data)
    self.cachedComponent[type] = component
    self:RefreshMailSubWidget(type, data)
end

function MailDetail_Panel:HideWidget(type)
    local component = self.cachedComponent[type]
    if not component then return end
    component:SetVisible(false)
end

function MailDetail_Panel:MailLoadWidget(customData)
    if customData.imgPath or customData.imgUrl then
        self.view.PhotoSlot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:LoadWidget(UICellConfig.MailPhoto, self.view.PhotoSlot, customData)
    else
        self.view.PhotoSlot:SetVisibility(ESlateVisibility.Collapsed)
        self:HideWidget(UICellConfig.MailPhoto)
    end

    if customData.guildPlayerId or customData.guildPlayerName or customData.guildPlayerLevel or customData.guildPlayerSchool then
        self.view.GuildPlayerSlot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:LoadWidget(UICellConfig.MailGuildPlayerName, self.view.GuildPlayerSlot, customData)
    else
        self.view.GuildPlayerSlot:SetVisibility(ESlateVisibility.Collapsed)
        self:HideWidget(UICellConfig.MailGuildPlayerName)
    end
    
    if customData.voice then
        self.view.VoiceSlot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:LoadWidget(UICellConfig.MailVoice, self.view.VoiceSlot, customData)
    else
        self.view.VoiceSlot:SetVisibility(ESlateVisibility.Collapsed)
        self:HideWidget(UICellConfig.MailVoice)
    end
end

function MailDetail_Panel:RefreshMailSubWidget(type, data)
    local component = self.cachedComponent[type]
    if not component then return end
    if type == UICellConfig.MailPhoto then
        if data.imgPath then
            component:SetCustomPhoto(data.imgPath)
        end
    elseif type == UICellConfig.MailGuildPlayerName then
        local school
        local playerName = ""
        local level = 1
        local id = data.guildPlayerId
        if data.guildPlayerLevel then
            level = data.guildPlayerLevel
        end
        if data.guildPlayerName then
            playerName = data.guildPlayerName
        end
        if data.guildPlayerSchool then
            school = data.guildPlayerSchool
        end
        component:SetGuildPlayer(id, school, playerName, level)
    elseif type == UICellConfig.MailVoice then
        if data.voice then
            component:SetVoice(data.voice, self.isNewMail)
        end
    end
end

function MailDetail_Panel:SetSenderName(params, data)
    local senderName, _ = CustomMail.GetSender(params)
    local senderFormat = StringConst.Get("MAIL_SENDER_DESC") or ""
    self.view.Sender_Name:SetText(senderFormat..senderName)
end

function MailDetail_Panel:SetupReceiveButton(IsLight, TitleName)
    self.view.Btn_Receive.IsLight = IsLight
    self.Btn_ReceiveCom:SetType()
    self.Btn_ReceiveCom:SetName(StringConst.Get(TitleName))
end

function MailDetail_Panel:buildRewardList(reward, isReceived)
    if not reward or #reward == 0 then
        return {}
    end

    local rewardList = {}
    for i = 1, #reward do
        local rewardItem = {
            id = reward[i].itemId,
            clickType = true,
            selected = Enum.EItemSelectType.NotSelected,
            num = reward[i].count,
            bind = reward[i].bound,
            bgQuality = reward[i].quality,
        }

        if isReceived then
            rewardItem.selected = Enum.EItemSelectType.Checked
        end

        table.insert(rewardList, rewardItem)
    end
    return rewardList

end

return MailDetail_Panel
