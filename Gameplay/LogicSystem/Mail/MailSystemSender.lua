local MailSystemSender = DefineClass("MailSystemSender",SystemSenderBase)

function MailSystemSender:getMailList()
    self.Bridge:getMailList()
end

function MailSystemSender:getMail(id)
    self.Bridge:getMail(id)
end

function MailSystemSender:deleteMail(id)
    self.Bridge:deleteMail(id)
end

function MailSystemSender:batchDeleteMail(category)
    self.Bridge:batchDeleteMail(category)
end

function MailSystemSender:extractMail(id)
    self.Bridge:extractMail(id)
end

function MailSystemSender:extractAllAttachments(category)
    self.Bridge:extractAllAttachments(category)
end

function MailSystemSender:ReqMailFavAdd(id)
    self.Bridge:ReqMailFavAdd(id)
end

function MailSystemSender:ReqMailFavRemove(id)
    self.Bridge:ReqMailFavRemove(id)
end

function MailSystemSender:ReqMailFavList()
    self.Bridge:ReqMailFavList()
end

function MailSystemSender:ReqMailFavRemoveAll()
    self.Bridge:ReqMailFavRemoveAll()
end

return MailSystemSender