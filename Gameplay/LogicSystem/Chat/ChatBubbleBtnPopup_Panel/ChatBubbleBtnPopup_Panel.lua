local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatBubbleBtnPopup_Panel : UIPanel
---@field view ChatBubbleBtnPopup_PanelBlueprint
local ChatBubbleBtnPopup_Panel = DefineClass("ChatBubbleBtnPopup_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ChatBubbleBtnPopup_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatBubbleBtnPopup_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatBubbleBtnPopup_Panel:InitUIData()
    ---@type　table 聊天消息
    self.ChatInfo = nil
    ---气泡位置
    self.BubblePos = nil
    self.btnRawData = {
        Btn_Report = {
            text = StringConst.Get("SOCIAL_CHAT_REPORT"),
            style = 2
        },
        Btn_Reply = {
            text = StringConst.Get("SOCIAL_CHAT_ATREPLY"),
            callBackFuncName = "OnTextReply",
            style = 0
        },
        Btn_Copy = {
            text = StringConst.Get("SOCIAL_CHAT_COPY"),
            callBackFuncName = "OnTextCopy",
            style = 1
        },
        Btn_ToTop = {
            text = StringConst.Get("SOCIAL_CHATROOM_TOTOP"),
            callBackFuncName = "OnTextToTop",
            style = 3
        }
    }
    -- 需要显示的按钮
    self.showBtnListData = {}
end

--- UI组件初始化，此处为自动生成
function ChatBubbleBtnPopup_Panel:InitUIComponent()
    ---@type UISimpleList
    self.HB_BtnListCom = self:CreateComponent(self.view.HB_BtnList, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function ChatBubbleBtnPopup_Panel:InitUIEvent()
    self:AddUIEvent(self.HB_BtnListCom.onItemSelected, "on_HB_BtnListCom_ItemSelected")
    self:AddUIEvent(self.view.CloseBtn.OnClicked, "on_CloseBtn_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatBubbleBtnPopup_Panel:InitUIView()
end

---面板打开的时候触发
function ChatBubbleBtnPopup_Panel:OnRefresh(chatInfo,pos,size)
    self.ChatInfo = chatInfo
    self:FitPos(pos,size)
    table.insert(self.showBtnListData, self.btnRawData.Btn_Copy)
    local channelRow = Game.TableData.GetChatChannelDataRow(self.ChatInfo.channelType)
    if channelRow and channelRow["CanReply"] then
        if self.ChatInfo.senderInfo.id ~= Game.me.eid then
            table.insert(self.showBtnListData, self.btnRawData.Btn_Reply)
        end
    end

    if channelRow and channelRow["CanToTop"] then
        if self.ChatInfo.channelType == Enum.EChatChannelData.CHATROOM then
            if Game.TeaRoomSystem:IsRoomMasterID(Game.me.eid) then
                table.insert(self.showBtnListData, self.btnRawData.Btn_ToTop)
            end
        else
            table.insert(self.showBtnListData, self.btnRawData.Btn_ToTop)
        end
    end
    if self.HB_BtnListCom.Refresh then
        self.HB_BtnListCom:Refresh(self.showBtnListData)
    end
end

function ChatBubbleBtnPopup_Panel:FitPos(pos,size)
    local NewPos = FVector2D(pos.X + size.X/2, pos.Y + size.Y + 50)
    self.view.KCanvas_Button.Slot:SetPosition(NewPos)
end

function ChatBubbleBtnPopup_Panel:OnTextCopy()
    if self.ChatInfo.rawMessageText == nil then
        self.ChatInfo.rawMessageText = Game.ChatSystem:GetRawMessgaeText(self.ChatInfo)
    end
    if not string.isEmpty(self.ChatInfo.rawMessageText) then
        Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_INPUT_COPY, self.ChatInfo.rawMessageText)  
    else
        Log.Info("ChatInfo.rawMessageText is nil")
    end
    self:CloseSelf()
end

function ChatBubbleBtnPopup_Panel:OnTextReply()
    Game.ChatSystem:AddRefMessage(self.ChatInfo)
    local atMsg = string.format("@%s",self.ChatInfo.senderInfo.rolename)
    Game.ChatSystem:AddAtInput(atMsg, self.ChatInfo.senderInfo.id)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_EMO_INPUT, atMsg)
    self:CloseSelf()
end

-- function ChatBubbleBtnPopup_Panel:OnRepeat()
--     if Game.ChatSystem:CheckChannelIsCanSendMessage(self.Params.channelType) then
--         Game.ChatSystem:SendChatMessage(self.Params.channelType, self.Params.rawMessageText, Enum.EChatMessageType.TEXT, nil, nil, true, false)
--     end
-- end

function ChatBubbleBtnPopup_Panel:OnTextToTop()
    if self.ChatInfo.channelType == Enum.EChatChannelData.CHATROOM then
        if not Game.TeaRoomSystem.model.roomInfoData then
            return
        end

        if string.notNilOrEmpty(Game.TeaRoomSystem.model.roomInfoData.strTopMessageText) then
                Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CHATROOM_PIN_CHANGE_CONFIRM,
                function()
                    local strPlayerName = self.ChatInfo.senderInfo.rolename
                    local nRoomID = Game.TeaRoomSystem.model.roomInfoData and Game.TeaRoomSystem.model.roomInfoData.nRoomID
                    if not string.isEmpty(self.ChatInfo.rawMessageText) and nRoomID ~= nil then
                        Game.TeaRoomSystem:ReqTeaRoomMessageTop(nRoomID, self.ChatInfo.rawMessageText, strPlayerName, self.ChatInfo.opNUID)
                    else
                        Log.Info("ChatInfo.rawMessageText is nil")
                    end
                    self:CloseSelf()
                end)
        else
            local strPlayerName = self.ChatInfo.senderInfo.rolename
            local nRoomID = Game.TeaRoomSystem.model.roomInfoData and Game.TeaRoomSystem.model.roomInfoData.nRoomID
            if not string.isEmpty(self.ChatInfo.rawMessageText) and nRoomID ~= nil then
                Game.TeaRoomSystem:ReqTeaRoomMessageTop(nRoomID, self.ChatInfo.rawMessageText, strPlayerName, self.ChatInfo.opNUID)
            else
                Log.Info("ChatInfo.rawMessageText is nil")
            end
            self:CloseSelf()
        end
    end
end

--- 此处为自动生成
function ChatBubbleBtnPopup_Panel:on_CloseBtn_Clicked()
    self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function ChatBubbleBtnPopup_Panel:on_HB_BtnListCom_ItemSelected(index, data)
    if data.callBackFuncName and self[data.callBackFuncName] then
        self[data.callBackFuncName](self)
    end
end

return ChatBubbleBtnPopup_Panel
