local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESlateVisibility = import("ESlateVisibility")
---@class FriendGroupPlayerIndexPanel : UIPanel
---@field view FriendGroupPlayerIndexPanelBlueprint
local FriendGroupPlayerIndexPanel = DefineClass("FriendGroupPlayerIndexPanel", UIPanel)
local ChatRoom_CheckBox = kg_require("Gameplay.LogicSystem.Chat.ChatRoom.ChatRoom_ScreeningTips_Panel.ChatRoom_CheckBox")

FriendGroupPlayerIndexPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FriendGroupPlayerIndexPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FriendGroupPlayerIndexPanel:InitUIData()
    self.playerData = nil
end

--- UI组件初始化，此处为自动生成
function FriendGroupPlayerIndexPanel:InitUIComponent()
    ---@type SocialHead
    self.WBP_SocialHeadCom = self:CreateComponent(self.view.WBP_SocialHead, SocialHead)
    self.WBP_Public_City_CheckBoxCom = self:CreateComponent(self.view.WBP_Public_City_CheckBox, ChatRoom_CheckBox)
end

---UI事件在这里注册，此处为自动生成
function FriendGroupPlayerIndexPanel:InitUIEvent()
    self:AddUIEvent(self.view.WBP_ReturnBtn.Btn_Back_Lua.OnClicked, "OnClickClose")
    -- self:AddUIEvent(self.view.WBP_ReturnBtn.Btn_Info_lua.OnClicked, "OnClickMsgCenter")
    self:AddUIEvent(self.view.WBP_HistoryMoments_Btn.Big_Button_ClickArea_lua.OnClicked, "OnClickHistoryMoments")
    self:AddUIEvent(self.WBP_Public_City_CheckBoxCom.onClickEvent, "OnChangePublicCityState")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FriendGroupPlayerIndexPanel:InitUIView()
    self.view.Total_Popularity:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_ALL_POPULARITY").StringValue)
    self.view.Fans:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_FANS").StringValue)
    self.view.Follows:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_FOLLOWS").StringValue)
    self.view.Total_Liked:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_LIKED").StringValue)
    self.view.WBP_ReturnBtn.Text_Back_lua:SetText(Game.TableData.GetMomentsStringConstDataRow("HOMEPAGE_NAME").StringValue)

    self.view.WBP_FollowBtn:SetVisibility(ESlateVisibility.Collapsed)
end

---面板打开的时候触发
function FriendGroupPlayerIndexPanel:OnRefresh(params)
    self:InitPlayerInfo(params)
end

function FriendGroupPlayerIndexPanel:InitPlayerInfo(data)
    self.playerData = data
    self.view.PlayerInfo_Text:SetText(string.format("%s\n%s", Game.MomentsSystem:HandleStringValue("MOMENTS_PLAYER_NAME", data.nickname), 
        Game.MomentsSystem:HandleStringValue("MOMENTS_SERVER_NAME", Game.MomentsSystem:GetUrlTable()[data.serverID])))
    -- 总人气值计算：X1*点赞量+X2*转发量+X3*评论量+X4*粉丝数
    local popularity = data.likeNum * Game.TableData.GetMomentsConstDataRow("POPULARITY_LIKE_WEIGHT") +
        data.repostNum * Game.TableData.GetMomentsConstDataRow("POPULARITY_REPOST_WEIGHT") +
        data.commentNum * Game.TableData.GetMomentsConstDataRow("POPULARITY_COMMENT_WEIGHT") +
        data.fansNum * Game.TableData.GetMomentsConstDataRow("POPULARITY_FANS_WEIGHT")
    self.view.Total_Popularity_Num:SetText(popularity)
    local fansNum = data.fansNum + table.count(Game.FriendSystem:GetFriendInfoList()) - table.count(Game.MomentsSystem:GetUnfollowPIDs())
    self.view.Fans_Num:SetText(fansNum)
    local followsNum = table.count(Game.FriendSystem:GetFriendInfoList()) + (data.follows and table.count(data.follows) or 0) - table.count(Game.MomentsSystem:GetUnfollowPIDs())
    self.view.Follows_Num:SetText(followsNum or Game.TableData.GetMomentsStringConstDataRow("HOMEPAGE_NONE_CITY").StringValue)
    self.view.Total_Liked_Num:SetText(data.likeNum + data.repostNum + data.commentNum) 
    self.WBP_SocialHeadCom:SetData({
        ProfessionID = data.school, Level = data.level
    })
    self.view.Guild_Info_Text:SetText(data.guildName == "" and Game.TableData.GetMomentsStringConstDataRow("HOMEPAGE_NONE_GUILD").StringValue or data.guildName)
    self.view.City_Name_Text:SetText(data.city == "" and Game.TableData.GetMomentsStringConstDataRow("HOMEPAGE_NONE_CITY").StringValue or data.city)
    self.view.Personal_Signature_Text:SetText(data.sign == "" and Game.TableData.GetMomentsStringConstDataRow("HOMEPAGE_NONE_SIGN").StringValue or data.city)
    if data.pid == Game.me.eid then
        self.WBP_Public_City_CheckBoxCom:Refresh(data.showCity, Game.TableData.GetMomentsStringConstDataRow("MOMENTS_PUBLIC_CITY").StringValue)
        self.view.WBP_Public_City_CheckBox:SetVisibility(ESlateVisibility.Visible)
        -- self.view.WBP_Public_City_CheckBox.Text_CR_Title:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_PUBLIC_CITY").StringValue)
        -- self.view.WBP_Public_City_CheckBox.CB_ChatRoom_lua:SetIsChecked(data.showCity)
    else
        self.view.WBP_Public_City_CheckBox:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function FriendGroupPlayerIndexPanel:OnClickClose()
    Game.NewUIManager:ShowPanel("FriendGroupPlayerIndexPanel", "ChatSocial_Panel")
    self:CloseSelf()
end

function FriendGroupPlayerIndexPanel:OnClickHistoryMoments()
    self:OpenComponent("HistoryMomentsPage", self.view.HistoryMomentsPage_Root, self.playerData.pid, self.playerData.serverID)
    self.view.WBP_HistoryMoments_Btn:SetVisibility(ESlateVisibility.Collapsed)
end

function FriendGroupPlayerIndexPanel:OnChangePublicCityState(bIsChecked)
    if Game.MomentsSystem:ReqUpdateCityIsPublic(bIsChecked) then
        self.WBP_Public_City_CheckBoxCom:SetChecked(bIsChecked, false)
    else
        self.WBP_Public_City_CheckBoxCom:SetChecked(not bIsChecked, false)
    end
end

return FriendGroupPlayerIndexPanel
