local ComTabHor = kg_require("Gameplay.LogicSystem.Common.Tab.ComTabHor")
local ComCheckBox = kg_require("Gameplay.LogicSystem.Common.CheckBox.ComCheckBox")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local P_MessageCenterItem = kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_MessageCenterItem")
local ESlateVisibility = import("ESlateVisibility")
---@class MessageCenterPanel : UIPanel
---@field view MessageCenterPanelBlueprint
local MessageCenterPanel = DefineClass("MessageCenterPanel", UIPanel)

MessageCenterPanel.eventBindMap = {
    [EEventTypesV2.MOMENTS_ON_MSG_CENTER_LIST_UPDATE] = "OnGetMsgListContent"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MessageCenterPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MessageCenterPanel:InitUIData()
    -- const字符串常量表
    self.tableData = Game.TableData.GetMomentsStringConstDataTable()
    -- 朋友圈/成就页签数据
    self.MsgCenterTabData = {
        [Enum.EMsgCenterTabType.Moments] = self.tableData["MOMENT_TAB"].StringValue,
        -- TODO 后续页签数据扩充页签数据
        -- [Enum.EMsgCenterTabType.Achievement] = "成就",
    }
    -- 上方Tab列表数据
    self.MsgCenterHorTabData = {
        [Enum.EMsgCenterTabType.Moments] = {
            {
                TabName = self.tableData["MOMENTS_MSG_INTERACTION"].StringValue,
                NType = Enum.EMomentsMsgType.Comment,
            },
            {
                TabName = self.tableData["MOMENTS_MSG_VISIT_RECORD"].StringValue,
                NType = Enum.EMomentsMsgType.Visit,
            },
            {
                TabName = self.tableData["MOMENTS_MSG_PUBLISH_VOTE"].StringValue,
                NType = Enum.EMomentsMsgType.VotePublish,
            },
            {
                TabName = self.tableData["MOMENTS_MSG_JOIN_VOTE"].StringValue,
                NType = Enum.EMomentsMsgType.VoteJoin,
            },
            {
                TabName = self.tableData["MOMENTS_MSG_PUNLISH_LOTTERY"].StringValue,
                NType = Enum.EMomentsMsgType.LotteryPublish,
            },
            {
                TabName = self.tableData["MOMENTS_MSG_JOIN_LOTTERY"].StringValue,
                NType = Enum.EMomentsMsgType.LotteryJoin,
            },
        },
        [Enum.EMsgCenterTabType.Achievement] = {
            {
                -- TabName = "成就",
            },
        }
    }

    -- 当前页签索引
    self.TabIndex = 1
    -- 当前选择的数据tab筛选索引
    self.PageTabIndexSelected = 1
    -- 消息中心内容数据
    self.msgContentData = {}

    self.startIndex = 0
end

--- UI组件初始化，此处为自动生成
function MessageCenterPanel:InitUIComponent()
    ---@type ComTabHor
    self.WBP_MsgTypeTabHorCom = self:CreateComponent(self.view.WBP_MsgTypeTabHor, ComTabHor)
    ---@type ComCheckBox
    self.WBP_HideAccess_CheckBoxCom = self:CreateComponent(self.view.WBP_HideAccess_CheckBox, ComCheckBox)

    self.msgCenterTabListView = self:CreateComponent(self.view.WBP_MsgSourceTabList.ComList_lua, UIBaseAdapter, "MessageCenterPanel")
    -- 消息来源列表
    self.msgCenterTabList = self.msgCenterTabListView:CreateBaseList(nil, ComList, "MsgCenterTabList")

    -- 消息内容列表
    self.msgListView = self:CreateComponent(self.view.WBP_ComMsgList, UIBaseAdapter, "MessageCenterPanel")
    self.msgList = self.msgListView:CreateBaseList({{P_MessageCenterItem}}, TreeList, "MsgList")

    -- self.msgList:SetDiffSizeFun("GetMomentsContentSize")
    self.msgList:SetScrollToEndListener("GetMoreMsgsContent")
end

---UI事件在这里注册，此处为自动生成
function MessageCenterPanel:InitUIEvent()
    self:AddUIEvent(self.view.WBP_FunctionBoardBG.WBP_ComBtnCloseNew.Button_lua.OnClicked, "OnClickClose")
    self:AddUIEvent(self.view.WBP_HideAccess_CheckBox.CheckBox_lua.OnCheckStateChanged, "OnChangeHideVisitState")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MessageCenterPanel:InitUIView()
    self.view.WBP_HideAccess_CheckBox.TB_Name_lua:SetText(self.tableData["MOMENTS_MSG_HIDE_VISIT"].StringValue)
    self.view.WBP_HideAccess_CheckBox.CheckBox_lua:SetIsChecked(Game.MomentsSystem:GetHideVisit())
    self.msgCenterTabList:SetData(#self.MsgCenterTabData)
    self.msgCenterTabList:Sel(Enum.EMsgCenterTabType.Moments)
    self:OnClick_MsgCenterTabList(nil, self.TabIndex)
    self.view.WBP_FunctionBoardBG.WBP_FirstBigText:SetText(self.tableData["MSG_CENTER"].StringValue)
end

---面板打开的时候触发
function MessageCenterPanel:OnRefresh(...)
end

function MessageCenterPanel:OnClick_MsgCenterTabList(widget, index)
    self.TabIndex = index
    table.clear(self.msgContentData)
    self.startIndex = 0
    self.WBP_MsgTypeTabHorCom:SetData(self.MsgCenterHorTabData[index], function(tabIndex) self:OnClickPageTab(tabIndex) end, 1)
end

function MessageCenterPanel:OnRefresh_MsgCenterTabList(widget, index, selected)
    -- widget.WidgetRoot:SetSubtitle(false)
    if not self.MsgCenterTabData[index] then return end
    widget.Text_tab:SetText(self.MsgCenterTabData[index])
    -- self:StopAllAnimations(widget)
    if selected then
        self:StopAnimation(widget.Ani_Off, widget)
        self:PlayAnimation(widget.Ani_On, nil, widget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self:StopAnimation(widget.Ani_On, widget)
        self:PlayAnimation(widget.Ani_Off, nil, widget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function MessageCenterPanel:OnClickPageTab(index)
    self.PageTabIndexSelected = index
    table.clear(self.msgContentData)
    self.startIndex = 0
    -- 切换tab
    self:UpdateMsgList(self.PageTabIndexSelected)
end

function MessageCenterPanel:UpdateMsgList(index)
    if self.TabIndex == Enum.EMsgCenterTabType.Moments then
        Game.MomentsSystem:ReqGetNoticeList(self.MsgCenterHorTabData[self.TabIndex][index].NType , self.startIndex)
    elseif self.TabIndex == Enum.EMsgCenterTabType.Achievement then
        -- TODO 显示成就消息内容
        
    end
end

function MessageCenterPanel:OnGetMsgListContent(data)
    for i, v in pairs(data) do
        table.insert(self.msgContentData, v)
    end
    if self.msgContentData and #self.msgContentData > 0 then
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    self.msgList:SetData(self.msgContentData)
    self.startIndex = #self.msgContentData
end

function MessageCenterPanel:OnClickClose()
    self:CloseSelf()
end

function MessageCenterPanel:GetMoreMsgsContent(bIsToBottom)
    if not bIsToBottom then
        return
    end
    if self.TabIndex == Enum.EMsgCenterTabType.Moments then
        Game.TimerManager:CreateTimerAndStart(function()
            Game.MomentsSystem:ReqGetNoticeList(self.MsgCenterHorTabData[self.TabIndex][self.PageTabIndexSelected].NType , self.startIndex)
        end, 500, 1)
    end
end

function MessageCenterPanel:OnChangeHideVisitState(bIsChecked)
    if Game.MomentsSystem:ReqUpdateHideVisit(bIsChecked) then
        self.view.WBP_HideAccess_CheckBox.CheckBox_lua:SetIsChecked(bIsChecked)
    else
        self.view.WBP_HideAccess_CheckBox.CheckBox_lua:SetIsChecked(not bIsChecked)
    end
end

return MessageCenterPanel
