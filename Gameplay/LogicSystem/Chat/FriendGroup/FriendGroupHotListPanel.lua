local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class FriendGroupHotListPanel : UIPanel
---@field view FriendGroupHotListPanelBlueprint
local FriendGroupHotListPanel = DefineClass("FriendGroupHotListPanel", UIPanel)
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local P_FriendGroupHotListItem = kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupHotListItem")
local ESlateVisibility = import("ESlateVisibility")

FriendGroupHotListPanel.eventBindMap = {
    [EEventTypesV2.MOMENTS_ON_HOT_TOPIC_LIST_UPDATE] = "OnGetTopicListContent",
    [EEventTypesV2.MOMENTS_ON_GET_SEARCH_TOPICS] = "OnGetSearchTopics",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FriendGroupHotListPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FriendGroupHotListPanel:InitUIData()
    -- 话题列表数据
    self.topicListData = {}
    -- 搜索结果
    self.searchResult = nil
    -- 官方话题列表
    self.officialTopicList = nil
    -- 热门话题列表
    self.hotTopicList = nil
end

--- UI组件初始化，此处为自动生成
function FriendGroupHotListPanel:InitUIComponent()
    self.topicListView = self:CreateComponent(self.view.WBP_ComList, UIBaseAdapter, "FriendGroupHotListPanel")
    self.topicList = self.topicListView:CreateBaseList(P_FriendGroupHotListItem, ComList, "TopicList")
end

---UI事件在这里注册，此处为自动生成
function FriendGroupHotListPanel:InitUIEvent()
    self:AddUIEvent(self.view.WBP_ComInputSearch.Button_search_lua.OnClicked, "OnClickSearch")
    self:AddUIEvent(self.view.WBP_ComInputSearch.Btn_lua.OnClicked, "OnClickClearInput")
    self:AddUIEvent(self.view.WBP_ComBtnBackNew.Btn_Back_Lua.OnClicked, "OnClickClose")
    -- self:AddUIEvent(self.view.WBP_ComInputSearch.EditText_lua.OnC7TextChanged, "OnEditTextChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FriendGroupHotListPanel:InitUIView()
end

---面板打开的时候触发
function FriendGroupHotListPanel:OnRefresh(bIsSearch)
    self.userWidget:Event_UI_State(bIsSearch)
    self.view.WBP_ComBtnBackNew.Text_Back_lua:SetText(bIsSearch and Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SEARCH_STRING").StringValue or Game.TableData.GetMomentsStringConstDataRow("MOMENTS_HOT_TOPIC").StringValue)
    Game.MomentsSystem:ReqGetHotTopic()
end

function FriendGroupHotListPanel:OnGetTopicListContent(data)
    self.officialTopicList = data.topTopics
    self.hotTopicList = data.topicRank
    self.topicList:SetData(#self.officialTopicList + (#self.hotTopicList > 10 and 10 or #self.hotTopicList))
    self:SetEmptyState(#self.officialTopicList + (#self.hotTopicList > 10 and 10 or #self.hotTopicList))
end

function FriendGroupHotListPanel:OnRefresh_TopicList(widget, index, selected)
    if self.searchResult then
        widget:RefreshData(index, self.searchResult[index], false)
    else
        if index <= #self.officialTopicList then
            widget:RefreshData(index, self.officialTopicList[index], true)
        else
            widget:RefreshData(index - #self.officialTopicList, self.hotTopicList[index - #self.officialTopicList], false)
        end
    end
end

function FriendGroupHotListPanel:OnClickSearch()
    Game.MomentsSystem:ReqSearchTopic(self.view.WBP_ComInputSearch.EditText_lua:GetText())
end

function FriendGroupHotListPanel:OnClickClearInput()
    self.view.WBP_ComInputSearch.EditText_lua:SetText("")
    self.searchResult = nil
    self.topicList:SetData(#self.officialTopicList + (#self.hotTopicList > 10 and 10 or #self.hotTopicList))
    self:SetEmptyState(#self.officialTopicList + (#self.hotTopicList > 10 and 10 or #self.hotTopicList))
end

function FriendGroupHotListPanel:OnClickClose()
    self:CloseSelf()
end

function FriendGroupHotListPanel:OnGetSearchTopics(result)
    self.searchResult = result
    self.topicList:SetData(#self.searchResult)
    self:SetEmptyState(#self.searchResult)
end

function FriendGroupHotListPanel:SetEmptyState(count)
    if count > 0 then
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

return FriendGroupHotListPanel
