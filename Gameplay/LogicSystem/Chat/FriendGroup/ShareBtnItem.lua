---@class ShareBtnItem : UIComponent
---@field view ShareBtnItemBlueprint
local ShareBtnItem = DefineClass("ShareBtnItem", UIComponent)
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

ShareBtnItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ShareBtnItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ShareBtnItem:InitUIData()
    -- 按钮点击回调事件
    self.onClickFunc = ""
end

--- UI组件初始化，此处为自动生成
function ShareBtnItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ShareBtnItem:InitUIEvent()
    -- self:AddUIEvent(self.view.Big_Button_ClickArea_lua.OnClicked, "onBig_Button_ClickArea_luaClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ShareBtnItem:InitUIView()
end

---面板打开的时候触发
function ShareBtnItem:OnRefresh(...)
end

function ShareBtnItem:SetData(type, funcName, shareData)
    self.userWidget:Event_UI_Style(type)
    self.onClickFunc = funcName
    self.shareData = shareData
end

--- 此处为自动生成
function ShareBtnItem:onBig_Button_ClickArea_luaClicked()
    if self[self.onClickFunc] then
        self[self.onClickFunc](self)
    else
        Log.Error("ShareBtnItem:onBig_Button_ClickArea_luaClicked no func")
    end
end

function ShareBtnItem:RefreshData(data, owner, urlClickCB)

end

--region 分享按钮点击事件
-- 点击保存图片
function ShareBtnItem:OnClickSave()
    Log.Debug("OnClickSave")
end

-- 点击进行局内分享
function ShareBtnItem:OnClickShareToChannel()
    Game.TabClose:AttachPanel("FriendGroupInfoBtnList_Panel", Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, self.View.WidgetRoot)
    local cachedGeometry = self.View.WidgetRoot:GetCachedGeometry()
    local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
    local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
        _G.GetContextObject(), cachedGeometry, localSize, nil, nil
    )
    Game.NewUIManager:OpenPanel(
                "FriendGroupInfoBtnList_Panel", viewportPosition.X + 50, viewportPosition.Y - 100,
                {
                    {name = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SHARE_TO_CHANNEL").StringValue, callbackFuncName = "OnSelectShareToChatChannel"},
                    {name = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SHARE_TO_FRIEND").StringValue, callbackFuncName = "OnSelectShareToFriend"},
                    {name = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SHARE_TO_GROUP").StringValue, callbackFuncName = "OnSelectShareToGroup"}
                }, self.shareData
            )
end

-- 点击分享到微信朋友圈
function ShareBtnItem:OnClickShareToWechatMoments()
    Log.Debug("OnClickShareToWechatMoments")
end

-- 点击分享到微信
function ShareBtnItem:OnClickShareToWechat()
    Log.Debug("OnClickShareToWechat")
end

-- 点击分享到QQ
function ShareBtnItem:OnClickShareToQQ()
    Log.Debug("OnClickShareToQQ")
end
--endregion

return ShareBtnItem
