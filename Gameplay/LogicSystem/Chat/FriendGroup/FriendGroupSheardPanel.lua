local FriendGroupItem = kg_require("Gameplay.LogicSystem.Chat.FriendGroup.FriendGroupItem")
local ComInputBig = kg_require("Gameplay.LogicSystem.Common.Input.ComInputBig")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class FriendGroupSheardPanel : UIPanel
---@field view FriendGroupSheardPanelBlueprint
local FriendGroupSheardPanel = DefineClass("FriendGroupSheardPanel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local P_ComboBox = kg_require("Gameplay.LogicSystem.CommonUI.P_ComboBox")
local ComBtnSwitch = kg_require("Gameplay.LogicSystem.Common.Button.ComBtnSwitch")
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
local ChatRoom_CheckBox = kg_require("Gameplay.LogicSystem.Chat.ChatRoom.ChatRoom_ScreeningTips_Panel.ChatRoom_CheckBox")

FriendGroupSheardPanel.eventBindMap = {
    [EEventTypesV2.MOMENTS_ON_ADD_TOPIC] = "OnSelectTopicOrAtItem",
    [EEventTypesV2.CHAT_SHOUT_INPUT] = "OnEmoInput"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FriendGroupSheardPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FriendGroupSheardPanel:InitUIData()
    self.visibleSettingBox = nil
    self.visibleTypeList = {
        {text = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_VISIBILITY_ALL").StringValue},
        {text = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_VISIBILITY_MUTUAL").StringValue},
        {text = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_VISIBILITY_SELF").StringValue},
    }
    self.bAllowForward = true
    self.oldText = ""
    -- 动态类型
    self.momentsType = Enum.EMomentsType.Nomal
    -- 动态话题
    self.momentsTopic = {}
    -- 动态位标识
    --[[
        flags的位标识规则
        可见性：使用第1位和第2位，其中0x0代表所有人可见，0x1代表好友可见，0x2代表仅自己可见
        是否可评论：使用第3位，如果不可评论，则第3位为1；可评论，则第3位为0
        是否可转发：使用第4位，如果不可转发，则第4位为1；可转发，则第4位为0
    ]]
    self.momentsFlags = 0
    -- 所在城市
    self.momentsCity = nil
    -- @玩家列表
    self.momentsAtIDList = {}
    -- 图片资源url列表
    self.momentsUrls = nil
    -- 图片资源ID列表
    self.momentsResIDs = nil
    -- 投票或抽奖的结束时间
    self.endTime = nil
    -- 是否转发动态
    self.bIsRepost = false
    -- 转发的动态信息
    self.repostParams = nil
end

--- UI组件初始化，此处为自动生成
function FriendGroupSheardPanel:InitUIComponent()
    ---@type FriendGroupItem
    self.WBP_FriendGroupItemCom = self:CreateComponent(self.view.WBP_FriendGroupItem, FriendGroupItem)
    ---@type ComInputBig
    self.wBP_MomentsInputCom = self:CreateComponent(self.view.WBP_MomentsInput, ComInputBig)

    ---@type ComBtnSwitch
    self.WBP_ComBtnSwitchCom = self:CreateComponent(self.view.WBP_ComBtnSwitch, ComBtnSwitch)
    self.visibleSettingBox = self:CreateComponent(self.view.WBP_ComComBox, UIBaseAdapter, "FriendGroupSheardPanel", P_ComboBox)

    self.WBP_Moment_CheckBox_ItemCom = self:CreateComponent(self.view.WBP_Moment_CheckBox_Item, ChatRoom_CheckBox)
end

---UI事件在这里注册，此处为自动生成
function FriendGroupSheardPanel:InitUIEvent()
    self:AddUIEvent(self.WBP_Moment_CheckBox_ItemCom.onClickEvent, "OnShowPositionInfo")
    self:AddUIEvent(self.view.WBP_ComBtnBackNew.Btn_Back_Lua.OnClicked, "OnClickCloseSelf")
    self:AddUIEvent(self.view.WBP_ComBtnIconNew.Big_Button_ClickArea_lua.OnClicked, "OnClickTopic")
    self:AddUIEvent(self.view.WBP_ComBtnIconNew_1.Big_Button_ClickArea_lua.OnClicked, "OnClickAt")
    self:AddUIEvent(self.view.WBP_ComBtnIconNew_2.Big_Button_ClickArea_lua.OnClicked, "OnClickEmoji")
    self:AddUIEvent(self.view.WBP_ComBtnIconNew_3.Big_Button_ClickArea_lua.OnClicked, "OnClickVote")
    self:AddUIEvent(self.view.WBP_ComBtnIconNew_4.Big_Button_ClickArea_lua.OnClicked, "OnClickLottery")
    self:AddUIEvent(self.view.Btn_PublishMoments.Btn_Com_lua.OnClicked, "OnClickPublishMoments")
    self:AddUIEvent(self.view.WBP_MomentsInput.EditText_lua.OnC7TextChanged, "OnEditTextChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FriendGroupSheardPanel:InitUIView()
    -- 可见范围下拉框
    -- self.visibleSettingBox:GetUIBase():Init(self, self.OnClickSort, GuildBattleInformationPanel.SortRules)

    self.visibleSettingBox:GetUIBase():Init(self, self.OnSelectVisibleSettingBox, self.visibleTypeList)
    -- self.visibleSettingBox:SetData(self.visibleTypeList, 1, false)
    -- 显示位置信息复选框
    -- self.view.WBP_Moment_CheckBox_Item.Text_CR_Title:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SHOW_CITY").StringValue)
    self.WBP_Moment_CheckBox_ItemCom:Refresh(self.momentsCity ~= nil, Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SHOW_CITY").StringValue)

    self.view.WBP_ComBtnIconNew.Text_Name_lua:SetText("")
    self.view.WBP_ComBtnIconNew_1.Text_Name_lua:SetText("")
    self.view.WBP_ComBtnIconNew_2.Text_Name_lua:SetText("")
    self.view.WBP_ComBtnIconNew_3.Text_Name_lua:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENT_TAB_VOTE").StringValue)
    self.view.WBP_ComBtnIconNew_4.Text_Name_lua:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENT_TAB_LOTTERY").StringValue)
    self.view.Btn_PublishMoments.Text_Com_lua:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_BTN_PUBLISH").StringValue)
end

function FriendGroupSheardPanel:OnClose()
    self.view.WBP_MomentsInput.EditText_lua:SetText("")
    self:InitUIData()
end

---面板打开的时候触发
function FriendGroupSheardPanel:OnRefresh(defaultParams, bIsRepost, topicName)
    -- 是否自带话题
    if topicName then
        self:OnSelectTopicOrAtItem(topicName)
    end
    -- 是否转发动态
    if defaultParams and bIsRepost then
        self.bIsRepost = true
        self.repostParams = defaultParams
        self.view.WBP_ComBtnBackNew.Text_Back_lua:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_REPOST").StringValue)
        self.view.WBP_ComBtnIconNew_3:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_ComBtnIconNew_4:SetVisibility(ESlateVisibility.Collapsed)
        self.WBP_FriendGroupItemCom:SetRepostData(defaultParams)
    else
        self.bIsRepost = false
        self.view.WBP_ComBtnBackNew.Text_Back_lua:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_POSTING").StringValue)
        self.view.WBP_ComBtnIconNew_3:SetVisibility(ESlateVisibility.Visible)
        self.view.WBP_ComBtnIconNew_4:SetVisibility(ESlateVisibility.Visible)
        self.WBP_FriendGroupItemCom:InitImgData()
    end
    self.WBP_ComBtnSwitchCom:SetData({
        IsOpen = true,
        OnSwitchCallback = function(isOpen)
            self.bAllowForward = isOpen
            if not isOpen then
                self.momentsFlags = self.momentsFlags | Enum.EMomentsSettingFlag.CanNotRepost | Enum.EMomentsSettingFlag.CanNotComment
            else
                -- 只保留二进制后两位
                self.momentsFlags = self.momentsFlags & 3
            end
        end
    })
    -- TODO PC端聚焦输入(Shift键按下角色还是会冲刺)
    -- local PC = GameplayStatics.GetPlayerController(_G.GetContextObject(), 0)
    -- if PC then
    --     self.view.WBP_MomentsInput.EditText_lua:SetUserFocus(PC)
    -- end
end

-- 点击添加话题
function FriendGroupSheardPanel:OnClickTopic()
    self:CloseComponent("FriendGroupTopic", true)
    self:OpenComponent("FriendGroupTopic", self.view.FriendGroupTopic_Overlay, {state = 0, itemList = {}})
end

-- 点击@某人
function FriendGroupSheardPanel:OnClickAt()
    self:CloseComponent("FriendGroupTopic", true)
    self:OpenComponent("FriendGroupTopic", self.view.FriendGroupTopic_Overlay, {state = 1, itemList = {}})
end

-- 点击添加表情
function FriendGroupSheardPanel:OnClickEmoji()
    Game.NewUIManager:OpenPanel(UIPanelConfig.ChatExpression_Panel, true)
end

function FriendGroupSheardPanel:OnEmoInput(emoStr)

    if self:CheckStrLenth(self.oldText .. emoStr) then
        self.oldText = self.oldText .. emoStr
        self.view.WBP_MomentsInput.EditText_lua:SetText(self.oldText)
    end
end

-- 点击投票
function FriendGroupSheardPanel:OnClickVote()
end

-- 点击抽奖
function FriendGroupSheardPanel:OnClickLottery()
end

-- 点击返回
function FriendGroupSheardPanel:OnClickCloseSelf()
    self:CloseSelf()
end

-- 选择可见范围
function FriendGroupSheardPanel:OnSelectVisibleSettingBox(index)
    -- 清除二进制后两位再设置
    self.momentsFlags = self.momentsFlags >> 2 << 2 | (index - 1)
end

-- 显示位置信息
function FriendGroupSheardPanel:OnShowPositionInfo(bIsChecked)
    if bIsChecked then
        self.momentsCity = Game.AllInSdkManager and Game.AllInSdkManager.SdkData.Location.city or ""
    else
        self.momentsCity = nil
    end
end

-- 输入文本内容发生变化
function FriendGroupSheardPanel:OnEditTextChanged()
    local text = self.view.WBP_MomentsInput.EditText_lua:GetText()
    -- 限制文本长度，只保留符合长度的字符
    if self:CheckStrLenth(text) then
        self.oldText = text
    else
        self.view.WBP_MomentsInput.EditText_lua:SetText(self.oldText)
    end
end

-- 文本长度检测
function FriendGroupSheardPanel:CheckStrLenth(text)
    if utf8.len(text) > Game.TableData.GetMomentsConstDataRow("MAX_MOMENT_CONTENT_COUNT") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MOMENTS_DYNAMIC_CONTENT_TOO_LONG)
        return false
    end
    return true
end

-- 点击发布动态
function FriendGroupSheardPanel:OnClickPublishMoments()
    if self.oldText == "" then
        self:OnEditTextChanged()
    end
    Game.AllInSdkManager:IsSensitiveWords(self.oldText, function(result)
        self:IsSensitiveWords(result)
    end, function()
        self:PublishMoment()
    end)
end

function FriendGroupSheardPanel:IsSensitiveWords(result)
    if result then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MOMENTS_DYNAMIC_CONTENT_SENSITIVE)
    elseif string.find(self.oldText, "##+") or string.find(self.oldText:gsub("%s+", ""), '<imgid="') or ChatUtils.CheckInjectInput(self.oldText) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MOMENTS_DYNAMIC_CONTENT_SENSITIVE)
    else
        self:PublishMoment()
    end
end

function FriendGroupSheardPanel:CheckTopicText()
    self.oldText = Game.ChatSystem:ProcessEmoMessage(self.oldText)
    self.oldText = self:handleTopicText(self.oldText)
    -- string.gsub(self.oldText, "#(.-)#", function(text)
    --     local tempText = text:gsub("%s+", "")
    --     if utf8.len(tempText) <= Enum.EMomentsConstData.MAX_SEARCH_TOPIC_COUNT and tempText ~= "" and not string.endsWith(tempText, '<imgid="') then
    --         table.insert(self.momentsTopic, tempText)
    --         return string.format(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_TOPIC_TYPE").StringValue, tempText, tempText)
    --     else
    --         return '#'..text..'#'
    --     end
    -- end)
end

-- 处理话题文本,gsub会跳过两对##之间的内容，需要手动跟据不同情况处理
function FriendGroupSheardPanel:handleTopicText(input)
    local result = {}
    local i = 1
    local len = #input
    while i <= len do
        if input:sub(i, i) == "#" then
            -- 查找下一个井号
            local j = i + 1
            while j <= len and input:sub(j, j) ~= "#" do
                j = j + 1
            end

            if j <= len then
                -- 找到成对的井号
                local tempText = string.sub(input, i + 1, j - 1):gsub("%s+", "")
                if utf8.len(tempText) <= Game.TableData.GetMomentsConstDataRow("MAX_SEARCH_TOPIC_COUNT") and tempText ~= "" and not string.endsWith(tempText, '<imgid="') then
                    table.insert(self.momentsTopic, tempText)
                    table.insert(result, string.format(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_TOPIC_TYPE").StringValue, tempText, tempText))
                    i = j + 1
                else
                    table.insert(result, string.sub(input, i, j - 1))
                    i = j
                end
                
            else
                table.insert(result, input:sub(i, i))
                i = i + 1
            end
        else
            table.insert(result, input:sub(i, i))
            i = i + 1
        end
    end
    return table.concat(result)
end

-- 处理@文本,跳过话题里面包含的@文本
function FriendGroupSheardPanel:handleAtText()
    local finalAtList = {}
    local rule = "([^@]*@%s[^<]*)"
    for k,v in pairs(self.momentsAtIDList) do
        -- 初始化起始位置
        local pos = 1
        local friendName = Game.FriendSystem:GetFriendInfoByEntityID(k).rolename
        local pattern = string.format("@%s", friendName)
        local tempStr = string.format("<>%s</>", friendName)
        local atText = string.format(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_AT_TYPE").StringValue, k, friendName)
        while true do
            -- 查找下一个 @friendName 出现的位置
            local startPos, endPos = string.find(self.oldText, pattern, pos)
            if not startPos then
                break
            end
            local linkStartPos, linkEndPos = string.find(self.oldText, string.format(string.format(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_TOPIC_TYPE").StringValue, rule, rule), friendName, friendName))
            if not linkStartPos or startPos < linkStartPos or endPos > linkEndPos then
                -- 满足条件
                self.oldText = string.format("%s%s%s", string.sub(self.oldText, 1, startPos - 1), tempStr, string.sub(self.oldText, endPos + 1))
                finalAtList[k] = v
            end
            pos = endPos + 1
        end
        self.oldText = string.gsub(self.oldText, tempStr, atText)
        -- local friendName = Game.FriendSystem:GetFriendInfoByEntityID(k).rolename
        -- if string.match(self.oldText, string.format("@%s", friendName)) then
        --     local atText = string.format(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_AT_TYPE").StringValue, k, friendName)
        --     self.oldText = string.gsub(self.oldText, string.format("@%s", friendName), atText)
        --     finalAtList[k] = v
        -- end
    end
    return finalAtList
end

function FriendGroupSheardPanel:PublishMoment()
    if self.oldText == "" then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MOMENTS_DYNAMIC_CONTENT_EMPTY)
        return
    end
    -- for k,v in pairs(self.momentsTopic) do
    --     if string.match(self.oldText, string.format("#%s#", v)) then
    --         local topicText = string.format(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_TOPIC_TYPE").StringValue, v, v)
    --         self.oldText = string.gsub(self.oldText, string.format("#%s#", v), topicText)
    --         Log.Debug("ret FriendGroupSheardPanelFriendGroupSheardPanel ", self.momentsTopic[k])
    --     else
    --         self.momentsTopic[k] = nil 
    --     end
    -- end
    self:CheckTopicText()
    local finalAtList = self:handleAtText()
    if self.bIsRepost then
        Game.MomentsSystem:ReqRepostMoment(self.repostParams.pid, self.repostParams.serverID, self.repostParams.mid, self.oldText, self.momentsTopic, self.momentsFlags, self.momentsCity, finalAtList)
    else
        self.momentsResIDs = self.WBP_FriendGroupItemCom:GetImgData()
        Game.MomentsSystem:ReqPublishMoment(self.oldText, self.momentsType, self.momentsTopic, self.momentsFlags, self.momentsCity, finalAtList, self.momentsResIDs, self.endTime)
    end
    -- 目前发布成功之后 直接关闭面板
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MOMENTS_DYNAMIC_RELEASE)
    self:CloseSelf()
end

function FriendGroupSheardPanel:OnClick_TopicList(widget, index, selected)
    widget:RefreshData(self.itemList[index], selected)
end

function FriendGroupSheardPanel:OnSelectTopicOrAtItem(name, id, serverID)
    self:CloseComponent("FriendGroupTopic")
    if name == nil or name == "" then return end
    if self:CheckStrLenth(self.oldText .. name) then
        if not id then
            -- table.insert(self.momentsTopic, name)
            self.oldText = string.format("%s#%s#", self.oldText, name)
        else
            self.momentsAtIDList[id] = serverID
            self.oldText = string.format("%s@%s", self.oldText, name)
        end
        self.view.WBP_MomentsInput.EditText_lua:SetText(self.oldText)
    end
end

return FriendGroupSheardPanel
