local UIComMask = kg_require("Framework.KGFramework.KGUI.Component.BackGround.UIComMask")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatVoice_Panel : UIPanel
---@field view ChatVoice_PanelBlueprint
local ChatVoice_Panel = DefineClass("ChatVoice_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")

ChatVoice_Panel.eventBindMap = {
    [EEventTypesV2.CHAT_VOICE_UPLOAD_COMPLETE] = "OnVoiceUploadComplete",
    [EEventTypesV2.CHAT_VOICE_RECORD_FAIL] = "OnVoiceRecordFailed",
    [EEventTypesV2.CHAT_VOICE_RECORD_FAIL_TOOSHORT] = "OnVoiceRecordTooShort",
    [EEventTypesV2.REDPACKET_VOICE_BTN_RELEASE] = "OnRelease",
    [EEventTypesV2.REDPACKET_VOICE_BTN_HOVERED] = "OnVoiceBtnHovered",
    [EEventTypesV2.REDPACKET_VOICE_BTN_UNHOVERED] = "OnVoiceBtnUnhovered",
    [EEventTypesV2.CHAT_VOICE_TOTEXT_HOVERED] = "OnToTextBtnHovered",
    [EEventTypesV2.CHAT_VOICE_TOTEXT_UNHOVERED] = "OnToTextBtnUnHovered",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatVoice_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatVoice_Panel:InitUIData()
    ---@type number max record time
    self.MaxRecordTime = Game.TableData.GetConstDataRow("CHAT_SEND_VOICE_RECORD")
    ---@type number max tiem to record transfer time
    self.MaxTransTime = Game.TableData.GetConstDataRow("CHAT_SEND_VOICE_TRANSLATE")
    ---@type table 按钮停留位置
    self.BtnEnum = {
        Send = 1,
        Cancle = 2,
        ToText = 3
    }
    ---@type boolean 是否在录制中
    self.IsRecording = false
    ---@type number 按钮当前状态
    self.SendState = self.BtnEnum.Send
    ---@type number 录制开始时间
    self.RecordingStartTime = nil
    ---@type number|string 语音发送对象
    self.TargetID = nil
    ---@type number 语音发送对象类型
    self.TargetType = nil
    --是否需要显示试听按钮
    self.ShowButton = true
    ---@type table 切换显示列表
    self.RecordingWS = {
        self.view.Cancel,
        self.view.Voice,
        self.view.Time,
        self.view.Failure,
        self.view.ToText
    }
end

--- UI组件初始化，此处为自动生成
function ChatVoice_Panel:InitUIComponent()
    ---@type UIComMask
    self.WBP_ComMaskCom = self:CreateComponent(self.view.WBP_ComMask, UIComMask)
end

---UI事件在这里注册，此处为自动生成
function ChatVoice_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatVoice_Panel:InitUIView()
end

---面板打开的时候触发
function ChatVoice_Panel:OnRefresh(targetID, targetType, showMask, bOpenRP, RPData)
    self:StopTimer("ShortWarning")
    self.TargetID = targetID
    if self.TargetID == Enum.EChatChannelData.COMMON then
        self.TargetID = Game.ChatSystem:GetCommonSelectedChannel()
    end
    self.TargetType = targetType
    self.SendState = self.BtnEnum.Send
	self.bOpenRP = bOpenRP
	self.RPData = RPData
    if not Game.VoiceSystem:StartRecording() then
        self:CloseSelf()
        return
    end
    self:ShowRecordingPanel()
    if showMask then
        self.view.WBP_ComMask:SetVisibility(ESlateVisibility.HitTestInvisible)
    else
        self.view.WBP_ComMask:SetVisibility(ESlateVisibility.Hidden)
    end
    self:OnVoiceBtnHovered()
end

function ChatVoice_Panel:OnHoveredCancleBtn()
    Log.Debug("OnHoveredCancleBtn")
    self.SendState = self.BtnEnum.Cancle
    self:RefreshPanel()
end

function ChatVoice_Panel:OnVoiceBtnHovered()
    Log.Debug("OnVoiceBtnHovered")
    self.SendState = self.BtnEnum.Send
    self:RefreshPanel()
end

function ChatVoice_Panel:OnVoiceBtnUnhovered()
    Log.Debug("OnVoiceBtnUnhovered")
    self.SendState = self.BtnEnum.Cancle
    self:RefreshPanel()
end

function ChatVoice_Panel:OnUnhoveredBtn()
    Log.Debug("OnUnhoveredBtn")
    self.SendState = self.BtnEnum.Cancle
    self:RefreshPanel()
end

function ChatVoice_Panel:OnToTextBtnHovered()
    Log.Debug("OnToTextBtnHovered")
    self.SendState = self.BtnEnum.ToText
    self:RefreshPanel()
end

function ChatVoice_Panel:OnToTextBtnUnHovered()
    Log.Debug("OnToTextBtnUnHovered")
    self.SendState = self.BtnEnum.Cancle
    self:RefreshPanel()
end

function ChatVoice_Panel:OnTouchMoved(InGestureEvent)
    -- Log.Debug("OnTouchMoved")
    -- local VoiceBtn1Geometry = self.view.ListeningTestBtn.Btn_Com:GetCachedGeometry()
    -- local isTouchVioceBtn = import("SlateBlueprintLibrary").IsUnderLocation(VoiceBtn1Geometry,import("KismetInputLibrary").PointerEvent_GetScreenSpacePosition(InGestureEvent))
    -- if not isTouchVioceBtn then
    --     -- local nowTime = import("GameplayStatics").GetRealTimeSeconds(_G.GetContextObject())
    --     -- if nowTime - self.RecordingStartTime < self.MaxRecordTime - 10 then
    --     --     self.view.WBP_ChatVoice.WS_Recording:SetActiveWidgetIndex(0)
    --     -- else
    --     --     self.view.WBP_ChatVoice.WS_Recording:SetActiveWidgetIndex(2)
    --     -- end
    -- else
    --     self.SendState = self.BtnEnum.Listen
    --     self:RefreshPanel()
    -- end
end

function ChatVoice_Panel:RefreshPanel()
    if self.IsRecording then
        if self.SendState == self.BtnEnum.Cancle then
            local nowTime = import("GameplayStatics").GetRealTimeSeconds(_G.GetContextObject())
            if nowTime - self.RecordingStartTime < self.MaxRecordTime - 10 then
                self:SetWS_Recording(0)
            else
                self:SetWS_Recording(2)
            end
        else
            local nowTime = import("GameplayStatics").GetRealTimeSeconds(_G.GetContextObject())
            if nowTime - self.RecordingStartTime < self.MaxRecordTime - 10 then
                if self.SendState == self.BtnEnum.ToText then
                    self:SetWS_Recording(4)
                else
                    self:SetWS_Recording(1)
                end
            else
                self:SetWS_Recording(2)
            end
            -- if  then
            --     self.view.Text_Recording:SetText("松手转文本，滑回继续")
            -- else
            --     self.view.Text_Recording:SetText("手指松开立即发送")
            -- end
        end
    end
end

function ChatVoice_Panel:OnRelease()
    Log.Debug("OnRelease")
    if self.IsRecording then
        self.IsRecording = false
        self:StopTimer("RecordingTime")
        if self.SendState == self.BtnEnum.Send then
            Game.VoiceSystem:StopRecording(Enum.EChatRecordingState.Upload)
        elseif self.SendState == self.BtnEnum.ToText then
            self:StopTimer("RecordingTime")
            Game.VoiceSystem:StopRecording(Enum.EChatRecordingState.ToText)
            self:CloseSelf()
        else
            Game.VoiceSystem:StopRecording(Enum.EChatRecordingState.Cancle)
            self:CloseSelf()
        end
    else
        self:CloseSelf()
    end
end

function ChatVoice_Panel:ShowRecordingPanel()
    self.IsRecording = true
    self:SetWS_Recording(1)
    self.view.ImgTime2:GetDynamicMaterial():SetScalarParameterValue("Percent", 0)
    self.RecordingStartTime = import("GameplayStatics").GetRealTimeSeconds(_G.GetContextObject())
    local widget = self.view.WBP_ChatVoiceWave
    self:PlayAnimation(widget.NewAnimation, nil, widget)
    local widget2 = self.view.WBP_ChatVoiceWave3
    self:PlayAnimation(widget2.NewAnimation, nil, widget2)
    local widget3 = self.view.WBP_ChatVoiceWave4
    self:PlayAnimation(widget3.NewAnimation, nil, widget3)
    self:StartTimer("RecordingTime", function()
        local nowTime = import("GameplayStatics").GetRealTimeSeconds(_G.GetContextObject())
        local leftTime = nowTime - self.RecordingStartTime
        local percent = leftTime / self.MaxRecordTime
        self.view.ImgTime:GetDynamicMaterial():SetScalarParameterValue("Percent", percent)
        self.view.ImgTime2:GetDynamicMaterial():SetScalarParameterValue("Percent", percent)
        self.view.ImgTime3:GetDynamicMaterial():SetScalarParameterValue("Percent", percent)
        self.view.ImgTime4:GetDynamicMaterial():SetScalarParameterValue("Percent", percent)
        if nowTime - self.RecordingStartTime > self.MaxRecordTime - 10 then
            self:SetWS_Recording(2)
            local leftTime_Floor = math.floor(self.MaxRecordTime - leftTime)
            leftTime_Floor = leftTime_Floor < 0 and 0 or leftTime_Floor
            self.view.Text_CDTime:SetText(tostring(leftTime_Floor))
            self.view.Text_LeftTime:SetText(string.format(StringConst.Get("SOCIAL_CHAT_VOICE_COUNTDOWN"),
                tostring(leftTime_Floor)))
        end
        if nowTime - self.RecordingStartTime >= self.MaxRecordTime then
            self:StopTimer("RecordingTime")
            Game.VoiceSystem:StopRecording(Enum.EChatRecordingState.Upload)
            --self:CloseSelf()
        end
    end, 200, -1, nil, true)
end


function ChatVoice_Panel:OnVoiceUploadComplete(fileID, text, duration, filepath)
    if self.TargetID then
		if self.TargetType == Enum.EChatTarget.Club then
			Game.ChatClubSystem:reqSendChat(self.TargetID, text, Enum.EChatMessageType.VOICE, nil,
				{
					voiceInfo = {
						voiceKey = fileID,
						voiceDuration = duration
					}
				}, false)
		else
			if self.bOpenRP then
				if string.find(text, self.RPData.passwd) then
                    Game.ChatSystem:SendChatMessage(self.TargetID, text, Enum.EChatMessageType.VOICE, nil, { voiceInfo = {
						voiceKey = fileID,
						voiceDuration = duration,
                        isSecretRedpacket = self.bOpenRP
					} }, true, true)
					Game.me:ReqReceiveRedPacket(self.RPData.uuid, self.RPData.packetChannel, self.RPData.passwd)
				else
                    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.RED_PACKET_SECRET_WORD_WRONG)
				end
			else
				Game.ChatSystem:SendChatMessage(self.TargetID, text, Enum.EChatMessageType.VOICE, nil, { voiceInfo = {
					voiceKey = fileID,
					voiceDuration = duration
				} }, true, false)
			end
		end
    else
        Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_RECORD_SUCCESS, fileID, text, duration, filepath)
    end
    self:CloseSelf()
end

function ChatVoice_Panel:OnVoiceRecordFailed()
    self:CloseSelf()
end

function ChatVoice_Panel:OnVoiceRecordTooShort()
    self:SetWS_Recording(3)
    self:StartTimer("ShortWarning", function()
        self:CloseSelf()
    end, 600, 1)
end


function ChatVoice_Panel:SetWS_Recording(index)
    for i = 1, #self.RecordingWS do
        if i == index + 1 then
            self.RecordingWS[i]:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        else
            self.RecordingWS[i]:SetVisibility(ESlateVisibility.Hidden)
        end
    end
end

return ChatVoice_Panel
