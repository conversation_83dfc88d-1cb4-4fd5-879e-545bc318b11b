---@class ChatCommonClubHead:UIComponent
local ChatCommonClubHead= DefineClass("ChatCommonClubHead",UIComponent)
local ESlateVisibility = import("ESlateVisibility")

function ChatCommonClubHead:OnCreate()
    ---@type function 点击回调
    self.OnClickedCallBack = nil
    ---@table 头像列表
    self.HeadList = {self.View.Img_HeadIcon1,self.View.Img_HeadIcon2,self.View.Img_HeadIcon3}
end

function ChatCommonClubHead:SetData(eids, clubId)
    local needReqGbIdList = {}
    if eids then
        for i = 1,3 do
            local eid = eids[i]
            if eid then
                local memberInfo = Game.ChatClubSystem:getMemberInfo(eid)
                if memberInfo then
                    self:SetHeadIcon(i,memberInfo.school)
                else
                    table.insert(needReqGbIdList,eid)
                end
            else
                self:SetHeadIcon(i,nil)
            end
        end
        if #needReqGbIdList > 0 then
            Log.Debug("requst club detail info to server")
            Game.ChatClubSystem:reqGetClubMemberInfo(needReqGbIdList)
        end
    end

    --设置免打扰标识
    if Game.me.clubsNoDisturb[clubId] then
        self.View.Img_disturb:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.View.Img_disturb:SetVisibility(ESlateVisibility.Collapsed)
    end
end


---设置玩家头像 暂时使用职业头像
---@param InProfessionID number|nil
function ChatCommonClubHead:SetHeadIcon(index,InProfessionID,sex)
    sex = sex or 0
    if InProfessionID and InProfessionID > 0 then
        self.HeadList[index]:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        local OptionClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(InProfessionID)[sex]
        self:SetImage(self.HeadList[index], OptionClassInfo.SoloHeadIcon)
    else
        self.HeadList[index]:SetVisibility(ESlateVisibility.Hidden)
    end
end



function ChatCommonClubHead:SetClickCallBack(callback)
    self.OnClickedCallBack = callback
end

return ChatCommonClubHead
