---@class ChatCommonChannelType:UIComponent
local ChatCommonChannelType = DefineClass("ChatCommonChannelType", UIComponent)
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
local ESlateVisibility = import("ESlateVisibility")

function ChatCommonChannelType:OnCreate()
	self.LastChannelType = nil
end

function ChatCommonChannelType:SetData(channelType)
	if self.LastChannelType == channelType then
		return
	end
	self.LastChannelType = channelType
    local channelData = Game.TableData.GetChatChannelDataRow(channelType)
    if channelData then
        self.View:SetVisibility(ESlateVisibility.Visible)
        local color = 0
        color = channelData.color or 0
        self.View:SetColor(color)
        self.View.text_channel:SetText(channelData.name)
        local isCountOver = ChatUtils.CountChars(channelData.name) > 4
        -- if isCountOver then
        --     self.View.sb:SetWidthOverride(95)
        -- else
        --     self.View.sb:SetWidthOverride(74)
        -- end
        return isCountOver
    else
        self.View:SetVisibility(ESlateVisibility.Collapsed)
    end
    return false
end

return ChatCommonChannelType
