---@class ChatShoutCost:UIComponent
local ChatShoutCost = DefineClass("ChatShoutCost", UIComponent)
local P_ComCurrency = kg_require "Gameplay.LogicSystem.CommonUI.P_ComCurrency"
local ItemBox = kg_require "Gameplay.LogicSystem.Item.ItemBox"
local ESlateVisibility = import("ESlateVisibility")

function ChatShoutCost:OnCreate()
    ---@type P_ComCurrency 货币对象
    self.Currency = self:BindComponent(self.View.WBP_ComCurrency, P_ComCurrency)
    ---@type ItemBox 物品对象
    self.ItemBox = self:BindComponent(self.View.WBP_ItemReward, ItemBox)
    --self:AddUIListener(EUIEventTypes.CheckStateChanged, self.View.WBP_ComCheckBox.CheckBox, self.OnSelect)
end

function ChatShoutCost:SetData(data,selected)
    local currentID
    local pennyCount 
    for i,v in pairs(data.TokenIDs) do
        currentID = i
        pennyCount = v
        break
    end
    local itemID = data.ItemID
    local itemCount = Game.BagSystem:GetItemCount(itemID)
    self.ItemBox:FillItem(itemID, Enum.GridState.Occupy, Enum.CommonItemClickType.OverrideTip, false)
    self.View.WBP_ComCheckBox.CheckBox:SetIsChecked(selected)
    if itemCount == 0 then
        self.Currency:SetData(currentID,pennyCount)
        self.View.WBP_ComCurrency:SetVisibility(ESlateVisibility.Visible)
    else
        self.View.WBP_ComCurrency:SetVisibility(ESlateVisibility.Hidden)
    end
end

return ChatShoutCost
