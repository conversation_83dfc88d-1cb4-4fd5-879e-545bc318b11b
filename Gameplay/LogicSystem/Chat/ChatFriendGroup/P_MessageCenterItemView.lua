--------No Class Find, Create a New UIController Class Using Given Filename--------
---@class WBP_SocialHeadView : WBP_SocialHead_C
---@field public WidgetRoot WBP_SocialHead_C
---@field public img_HeadBack Image
---@field public Img_HeadIcon KGImage
---@field public panel_status CanvasPanel
---@field public Img_lvback Image
---@field public Text_level KGTextBlock
---@field public Img_probg Image
---@field public icon_RT Image
---@field public icon_LT KGImage
---@field public Skill_seleced Image
---@field public Btn_Head KGButton
---@field public IconNone SlateBrush
---@field public TeamLeader SlateBrush
---@field public GroupLeader SlateBrush
---@field public Is Offline boolean
---@field public Is Seletcted boolean
---@field public IsEmpty boolean
---@field public AddSwitch number
---@field public Member Type number
---@field public LT SlateBrush
---@field public HeadSizeX number
---@field public HeadSizeY number
---@field public IconSizeX number
---@field public IconSizeY number
---@field public Event_UI_Style fun(self:self,IsOffline:boolean,IsSeletcted:boolean,MemberType:number,IsEmpty:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetOffline fun(self:self,IsOffline:boolean):void
---@field public SetEmpty fun(self:self,Empty:boolean):void
---@field public SetLT fun(self:self,Member:number):void
---@field public SetSize fun(self:self,HeadSizeX:number,HeadSizeY:number):void
---@field public SetSize2 fun(self:self,InWidthOverride:number,InHeightOverride:number):void
---@field public SetAddSwitch fun(self:self,AddSwitch:number):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public bg KGImage
---@field public hb_btn HorizontalBox
---@field public Text_Com KGTextBlock
---@field public Text_Time KGTextBlock
---@field public Image KGImage
---@field public Btn_Com KGButton
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein_normal WidgetAnimation
---@field public Ani_Fadein_Light WidgetAnimation
---@field public Ani_Fadein_blue WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public SequenceEvent fun(self:self):void
---@field public Construct fun(self:self):void
---@field public OnVisibilityChangedEvent fun(self:self,InVisibility:ESlateVisibility):void
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_ComBtnIconNewView : WBP_ComBtnIconNew_C
---@field public WidgetRoot WBP_ComBtnIconNew_C
---@field public OutCanvas CanvasPanel
---@field public Icon Image
---@field public Text_Name TextBlock
---@field public Big_Button_ClickArea KGButton
---@field public Anim_1 WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public Ani_Hover WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public Btn Style ST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound SlateSound
---@field public Top number
---@field public Event_UI_Style fun(self:self,BtnName:string):void
---@field public Play Hint Anim fun(self:self):void
---@field public BndEvt__WBP_ComBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Btn Style fun(self:self,Btn Style:ST_ComBtnIcon):void
---@field public SetVxSize fun(self:self):void


---@class WBP_MessageCenterItemView : WBP_MessageCenterItem_C
---@field public WidgetRoot WBP_MessageCenterItem_C
---@field public WBP_SocialHead WBP_SocialHeadView
---@field public MsgText KGRichTextBlock
---@field public Btn_Go WBP_ComBtnView
---@field public Btn_Add WBP_ComBtnIconNewView
---@field public VoteText KGRichTextBlock
---@field public Display Mode number
---@field public Player Mode State number
---@field public Chou Jiang State number
---@field public Referendum State number
---@field public Event_UI_Style fun(self:self,DisplayMode:number,PlayerModeState:number,ChouJiangState:number,ReferendumState:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetState fun(self:self,Display Mode:number,Player Mode State:number,Chou Jiang State:number,Referendum State:number):void
---@field public Clear fun(self:self):void


---@class P_MessageCenterItemView : WBP_MessageCenterItemView
---@field public controller P_MessageCenterItem
local P_MessageCenterItemView = DefineClass("P_MessageCenterItemView", UIView)

function P_MessageCenterItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {{self.Btn_Go_lua.WidgetRoot, 0.6},{self.Btn_Add_lua.WidgetRoot, 1.866683},{self.Btn_Accept.WidgetRoot, 0.6},{self.WBP_ComBtn.WidgetRoot, 0.6},},AnimFadeOut = {}}
end

function P_MessageCenterItemView:OnDestroy()
end

return P_MessageCenterItemView
