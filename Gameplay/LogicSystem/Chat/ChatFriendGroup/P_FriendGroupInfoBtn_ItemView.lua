---@class WBP_FriendGroupInfoBtn_ItemView : WBP_FriendGroupInfoBtn_Item_C
---@field public WidgetRoot WBP_FriendGroupInfoBtn_Item_C
---@field public SelectionBtn KGButton
---@field public SelectionName KGTextBlock


---@class P_FriendGroupInfoBtn_ItemView : WBP_FriendGroupInfoBtn_ItemView
---@field public controller P_FriendGroupInfoBtn_Item
local P_FriendGroupInfoBtn_ItemView = DefineClass("P_FriendGroupInfoBtn_ItemView", UIView)

function P_FriendGroupInfoBtn_ItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

function P_FriendGroupInfoBtn_ItemView:OnDestroy()
end

return P_FriendGroupInfoBtn_ItemView
