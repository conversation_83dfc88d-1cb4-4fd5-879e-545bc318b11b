require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupTopicItemView")
---@class P_FriendGroupTopicItem : UIComponent
---@field public View WBP_FriendGroupTopicItemView
local P_FriendGroupTopicItem = DefineClass("P_FriendGroupTopicItem", UIComponent)

function P_FriendGroupTopicItem:OnCreate()
    -- 话题名称
    self.topicName = nil
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button, self.OnSelectTopic)
end

function P_FriendGroupTopicItem:OnShow()

end

function P_FriendGroupTopicItem:OnHide()

end

function P_FriendGroupTopicItem:RefreshData(topicData)
    self.topicName = topicData.topicName and topicData.topicName or topicData
    self.View.Topic_Name:SetText(self.topicName)
end

function P_FriendGroupTopicItem:OnSelectTopic()
    Game.GlobalEventSystem:Publish(EEventTypesV2.MOMENTS_ON_ADD_TOPIC, self.topicName)
end

return P_FriendGroupTopicItem
