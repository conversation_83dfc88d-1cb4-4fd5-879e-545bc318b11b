kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_PlayerFriendGroupPost_ItemView")
---@class P_PlayerFriendGroupPost_Item : UIComponent
---@field public View WBP_PlayerFriendGroupPost_ItemView
local P_PlayerFriendGroupPost_Item = DefineClass("P_PlayerFriendGroupPost_Item", UIComponent)
local P_ComSocialHead = kg_require("Gameplay.LogicSystem.CommonUI.P_ComSocialHead")
local P_FriendGroupImageItem = kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupImageItem")
local ESlateVisibility = import("ESlateVisibility")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

P_PlayerFriendGroupPost_Item.eventBindMap = {
}

function P_PlayerFriendGroupPost_Item:OnCreate()
    -- 临时处理 迁移到新框架后去掉
    Game.GlobalEventSystem:RemoveTargetAllListeners()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.MOMENTS_ON_CHANGE_LIKE_STATE, "OnRefreshLikeState", self)

    -- 发布者头像
    self.playerSocialHead = self:BindComponent(self.View.WBP_SocialHead, P_ComSocialHead)
    -- 转发者头像
    self.repostSocialHead = self:BindComponent(self.View.WBP_SocialHead_3, P_ComSocialHead)
    -- 图片列表
    self.pictureUrlsList = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_ComList, P_FriendGroupImageItem)
    -- 转发图片列表
    self.repostPictureUrlsList = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_ComList_1, P_FriendGroupImageItem)
    -- 当前动态相关数据
    self.ItemData = nil

    -- 存储上次点击时间
    self.lastClickTime = 0
    -- 存储当前点击次数
    self.curClicks = 0
    -- 最大点击次数
    self.maxClicks = 5
    -- 点击时间间隔
    self.timeWindow = 5
    -- 是否被点赞
    self.bIsLiked = false
    -- 是否被关注
    self.bIsFollowed = false

    -- 初始点赞数
    self.firstLikeNum = 0

    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_Like_Num.Btn_ClickArea, self.OnClick_Like_Btn)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_Repost_Num.Btn_ClickArea, self.OnClick_Repost_Btn)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ChatDiscloseBtn.Button, self.OnClick_Follow_Btn)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_Comment_Num.Btn_ClickArea, self.OnClick_Detail_Btn)
    self:AddUIListener(EUIEventTypes.UrlClicked, self.View.Moments_Text_Content, self.OnClick_Text_Url)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Repost_Btn, self.OnClick_Repost_Info)
    -- self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_BtnReply.Btn_Inter, self.OnClick_Reply_Btn)
end

function P_PlayerFriendGroupPost_Item:OnClose()
    Game.GlobalEventSystem:RemoveTargetAllListeners()
end

function P_PlayerFriendGroupPost_Item:OnListRefresh(parentUI, bIsSelect, allData, index)
    self.ItemData = allData[index]
    if self.ItemData == nil then return end
    if self.ItemData.mtype == Enum.EMomentsType.Nomal then
        if self.ItemData.repostMID == "" then          -- 普通动态
            self:HandleNormalMomentsRefresh()
        else
            self:HandleRepostMomentsRefresh()
        end
    elseif self.ItemData.mtype == Enum.EMomentsType.Vote then
        -- TODO 投票动态
        self.View:Event_UI_State(4)
    elseif self.ItemData.mtype == Enum.EMomentsType.Lottery then
        -- TODO 抽奖动态
        self.View:Event_UI_State(1)
    end
    self.playerSocialHead:SetBindButton()
    self.playerSocialHead:Refresh({ProfessionID = self.ItemData.school, Level = self.ItemData.level, OnClickedCallBack = function() self:OnClick_PlayerHead() end})
    self:HandleFollowBtn()
    local Date = {}
	_G._time(Date, self.ItemData.createTime * 1000)
    self.View.RT_PlayerHeadTitle:SetText(string.format("%s %s\n%s", Game.MomentsSystem:HandleStringValue("MOMENTS_PLAYER_NAME", self.ItemData.nickname), 
        Game.MomentsSystem:HandleStringValue("MOMENTS_SERVER_NAME", Game.MomentsSystem:GetUrlTable()[self.ItemData.serverID]), 
        Game.MomentsSystem:HandleStringValue("MOMENTS_PUBLISH_TIME", Date.year, Date.month, Date.day)))
    self.View.Moments_Text_Content:SetText(Game.MomentsSystem:ProcessMomentsContent(self.ItemData.content))
    self.firstLikeNum = self.ItemData.likeNum
    self.View.WBP_Like_Num:BP_SetType(self.View.WBP_Like_Num.Icon)
    self.View.WBP_Like_Num.Text_Name:SetText(Game.MomentsSystem:FormatNumValue(self.ItemData.likeNum))
    self.bIsLiked = Game.MomentsSystem:GetLikeIDs()[self.ItemData.mid] ~= nil
    self.View.WBP_Comment_Num:BP_SetType(self.View.WBP_Comment_Num.Icon)
    self.View.WBP_Comment_Num.Text_Name:SetText(Game.MomentsSystem:FormatNumValue(self.ItemData.commentNum))
    self.View.WBP_Repost_Num:BP_SetType(self.View.WBP_Repost_Num.Icon)
    self.View.WBP_Repost_Num.Text_Name:SetText(Game.MomentsSystem:FormatNumValue(self.ItemData.repostNum))
end

function P_PlayerFriendGroupPost_Item:HandleFollowBtn()
    -- 关注按钮
    if self.ItemData.pid == Game.me.eid then
        self.View.WBP_ChatDiscloseBtn:SetVisibility(ESlateVisibility.Collapsed)
    elseif Game.FriendSystem:CheckIsBothWayFriend(self.ItemData.pid) and Game.MomentsSystem:GetUnfollowPIDs()[self.ItemData.pid] == nil then
        self.bIsFollowed = true
        self.View.WBP_ChatDiscloseBtn:SetVisibility(ESlateVisibility.Visible)
        self.View.WBP_ChatDiscloseBtn:Event_UI_Style(true)
        self.View.WBP_ChatDiscloseBtn.TB_Word:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_PLAYER_MUTUAL_FOLLOW").StringValue)
    else
        self.View.WBP_ChatDiscloseBtn:SetVisibility(ESlateVisibility.Visible)
        self.bIsFollowed = Game.MomentsSystem:GetFollowPIDs()[self.ItemData.pid] ~= nil
        if self.bIsFollowed then
            self.View.WBP_ChatDiscloseBtn:Event_UI_Style(true)
            self.View.WBP_ChatDiscloseBtn.TB_Word:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_PLAYER_UNFOLLOW").StringValue)
        else
            self.View.WBP_ChatDiscloseBtn:Event_UI_Style(false)
            self.View.WBP_ChatDiscloseBtn.TB_Word:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_PLAYER_FOLLOW").StringValue)
        end
    end
end

-- 普通动态子项刷新
function P_PlayerFriendGroupPost_Item:HandleNormalMomentsRefresh()
    self.View:Event_UI_State(0)
    if #self.ItemData.resIDs == 0 then
        self.View.WBP_ComList:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.View.WBP_ComList:SetVisibility(ESlateVisibility.Visible)
        self.pictureUrlsList:SetData(#self.ItemData.resIDs)
    end
end

-- 转发动态子项刷新
function P_PlayerFriendGroupPost_Item:HandleRepostMomentsRefresh()
    self.View:Event_UI_State(2)
    self.View.WBP_Moments_Tag:SetVisibility(ESlateVisibility.Visible)
    self.View.WBP_Moments_Tag.text_channel:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_REPOST_STRING").StringValue)
    -- 如果被转发的动态未被删除
    if self.ItemData.repost then
        local Date = {}
        _G._time(Date, self.ItemData.repost.createTime * 1000)
        self.repostSocialHead:Refresh({ProfessionID = self.ItemData.repost.school, Level = self.ItemData.repost.level, OnClickedCallBack = function() self:OnClick_PlayerHead() end})
        self.View.RT_PlayerHeadTitle_1:SetText(string.format("%s %s\n%s", Game.MomentsSystem:HandleStringValue("MOMENTS_PLAYER_NAME", self.ItemData.repost.nickname),
            Game.MomentsSystem:HandleStringValue("MOMENTS_SERVER_NAME", Game.MomentsSystem:GetUrlTable()[self.ItemData.repost.serverID]), Game.MomentsSystem:HandleStringValue("MOMENTS_PUBLISH_TIME", Date.year, Date.month, Date.day)))
        self.View.Moments_Text_Content_1:SetText(Game.MomentsSystem:ProcessMomentsContent(self.ItemData.repost.content))
        if #self.ItemData.repost.resIDs == 0 then
            self.View.WBP_ComList_1:SetVisibility(ESlateVisibility.Collapsed)
        else
            self.View.WBP_ComList_1:SetVisibility(ESlateVisibility.Visible)
            self.repostPictureUrlsList:SetData(#self.ItemData.repost.resIDs)
        end
    else
        self.View:Event_UI_State(4)
    end
end

function P_PlayerFriendGroupPost_Item:OnHide()

end

function P_PlayerFriendGroupPost_Item:OnClick_WBP_BtnMore_Btn()
    Game.TabClose:AttachPanel("FriendGroupInfoBtnList_Panel", Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, self.View.WidgetRoot)
    local cachedGeometry = self.View.WBP_BtnMore.WidgetRoot:GetCachedGeometry()
    local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
    local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
        _G.GetContextObject(), cachedGeometry, localSize, nil, nil
    )
    local selection = nil
    if self.ItemData.pid == Game.me.eid then
        selection = {{name = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_DELETE_STRING").StringValue, callbackFuncName = "OnSelectDeleteMoment"}}
    else
        selection = {{name = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_REPORT_STRING").StringValue, callbackFuncName = "OnSelectReportMoment"}}
    end
    Game.NewUIManager:OpenPanel(
        UIPanelConfig.FriendGroupInfoBtnList_Panel, viewportPosition.X - 200, viewportPosition.Y - 60,
                selection, self.ItemData
            )
end

function P_PlayerFriendGroupPost_Item:OnClick_WBP_BtnShare_Btn()
    -- 点击分享按钮
    Game.NewUIManager:OpenPanel(UIPanelConfig.ShareWindowPanel, "PlayerFriendGroupPost_Item", self.ItemData)
end

-- 关注/取消关注
function P_PlayerFriendGroupPost_Item:OnClick_Follow_Btn()
    if Game.MomentsSystem:ReqFollowPlayer(self.ItemData.pid, self.ItemData.serverID) then
        self.bIsFollowed = not self.bIsFollowed
        self.View.WBP_ChatDiscloseBtn:Event_UI_Style(self.bIsFollowed)
        self.View.WBP_ChatDiscloseBtn.TB_Word:SetText(self.bIsFollowed and Game.TableData.GetMomentsStringConstDataRow("MOMENTS_PLAYER_UNFOLLOW").StringValue
            or Game.TableData.GetMomentsStringConstDataRow("MOMENTS_PLAYER_FOLLOW").StringValue)
    end
end

-- 点赞动态
function P_PlayerFriendGroupPost_Item:OnClick_Like_Btn()
    local currentTime = _G._now() // 1000
    if currentTime - self.lastClickTime < self.timeWindow then
        self.curClicks = self.curClicks + 1
        if self.curClicks > self.maxClicks then
            -- TODO 弹窗提示
            self.curClicks = 0
        end
    end
    self.lastClickTime = currentTime
    Game.MomentsSystem:ReqLikeMoment(self.ItemData.pid, self.ItemData.serverID, self.ItemData.mid)
    -- if Game.MomentsSystem:ReqLikeMoment(self.ItemData.pid, self.ItemData.serverID, self.ItemData.mid) then
    --     self.ItemData.likeNum = self.bIsLiked and self.ItemData.likeNum - 1 or self.ItemData.likeNum + 1
    --     self.View.WBP_Like_Num:Event_UI_Style(self.View.WBP_Like_Num.Icon, Game.MomentsSystem:FormatNumValue(self.ItemData.likeNum))
    --     self.bIsLiked = not self.bIsLiked
    -- end
    -- TODO 点赞动效
end

function P_PlayerFriendGroupPost_Item:OnClick_Detail_Btn()
    -- 打开详情界面
    -- if self:GetParent():GetParent().OnShowMomentsDetail then
    --     self:GetParent():GetParent():OnShowMomentsDetail(self.ItemData)
    -- else
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.FriendGroupInfo_Panel) and not Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.FriendGroupPlayerIndexPanel) then
        self:GetParent():GetParent():InitMomentsDetailData(self.ItemData)
        self:GetParent():GetParent():OnChangeCommentTarget(nil, nil, nil, true)
    else
        Game.NewUIManager:OpenPanel(UIPanelConfig.FriendGroupInfo_Panel, self.ItemData)
    end
end

function P_PlayerFriendGroupPost_Item:OnClick_Reply_Btn()
    -- TODO 点击回复评论
end

-- 转发动态
function P_PlayerFriendGroupPost_Item:OnClick_Repost_Btn()
    if self.ItemData.flags & Enum.EMomentsSettingFlag.CanNotRepost ~= 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MOMENTS_FORBIDDEN_FORWARD)
        return
    end
    Game.NewUIManager:OpenPanel(UIPanelConfig.FriendGroupSheardPanel, self.ItemData, true)
end

-- 点击玩家头像
function P_PlayerFriendGroupPost_Item:OnClick_PlayerHead()
    Game.TeamSystem:PlayerCardUIDataAsync(self.ItemData.pid, false, Enum.EFriendAddSourceData.MOMENTS, nil, Enum.EMenuType.Chat)
end

-- 点击文本超链接
function P_PlayerFriendGroupPost_Item:OnClick_Text_Url(Url)
    Game.ChatSystem:OnUrlClicked(Url)
end

function P_PlayerFriendGroupPost_Item:OnClick_Repost_Info()
    if self.ItemData.repost then
        Game.MomentsSystem:ReqGetMomentInfo(self.ItemData.repost.pid, self.ItemData.repost.serverID, self.ItemData.repostMID, self.ItemData.mid, nil)
    end
end

-- 刷新普通动态图片列表
function P_PlayerFriendGroupPost_Item:OnRefresh_WBP_ComList(widget, index, selected)
    widget:RefreshMomentsImage(self.ItemData.resIDs, self.ItemData.urls, index)
end

-- 刷新转发动态图片列表
function P_PlayerFriendGroupPost_Item:OnRefresh_WBP_ComList_1(widget, index, selected)
    widget:RefreshMomentsImage(self.ItemData.repost.resIDs, self.ItemData.repost.urls, index)
end

function P_PlayerFriendGroupPost_Item:OnRefreshLikeState(mid, likeNum)
    if mid ~= self.ItemData.mid then return end
    self.ItemData.likeNum = likeNum
    -- TODO 标识是否自己点赞
    if Game.MomentsSystem:GetLikeIDs()[self.ItemData.mid] ~= nil then
        self.View.WBP_Like_Num.Text_Name:SetText(Game.MomentsSystem:FormatNumValue(self.ItemData.likeNum))
    else
        self.View.WBP_Like_Num.Text_Name:SetText(Game.MomentsSystem:FormatNumValue(self.ItemData.likeNum))
    end
end

return P_PlayerFriendGroupPost_Item
