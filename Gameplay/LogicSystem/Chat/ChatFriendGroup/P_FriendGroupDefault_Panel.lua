kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupDefault_PanelView")
---@class P_FriendGroupDefault_Panel : UIController
---@field public View WBP_FriendGroupDefault_PanelView
local P_FriendGroupDefault_Panel = DefineClass("P_FriendGroupDefault_Panel", UIController)
local P_ComTabHor = kg_require "Gameplay.LogicSystem.CommonUI.P_ComTabHor"
local ESlateVisibility = import("ESlateVisibility")
local P_PlayerFriendGroupPost_Item = kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_PlayerFriendGroupPost_Item")
local P_ComboBox = kg_require("Gameplay.LogicSystem.CommonUI.P_ComboBox")
local P_FriendGroupTopic_Item = kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupTopic_Item")
-- local P_FriendGroupInfo = kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupInfo")

P_FriendGroupDefault_Panel.eventBindMap = {
    [EEventTypesV2.MOMENTS_ON_MOMENTS_LIST_UPDATE] = "OnGetMomentsListContent",
    [EEventTypesV2.MOMENTS_ON_DELETE_MOMENT] = "OnDeleteMoment",
    [EEventTypesV2.MOMENTS_ON_HOT_TOPIC_LIST_UPDATE] = "OnGetHotTopicListContent",
}

function P_FriendGroupDefault_Panel:OnCreate()
    -- 上方Tab列表
    self.PageTabList = self:BindComponent(self.View.WBP_ComTabHor, P_ComTabHor)
    -- 当前页签索引
    self.TabIndex = nil
    -- 朋友圈具体内容列表
    self.MomentsContentList = BaseList.CreateList(self, BaseList.Kind.TreeList, self.View.WBP_ComTreeList, {{P_PlayerFriendGroupPost_Item}})
    self.MomentsContentList:SetDiffSizeFun("GetMomentsContentSize")
    self.MomentsContentList:SetScrollToEndListener("GetMoreMomentsContent")
    -- const字符串常量表
    local tableData = Game.TableData.GetMomentsStringConstDataTable()
    -- 上方Tab列表数据
    self.MomentsHorTabData = {
        [Enum.EMomentsTabType.Moments] = {
            {
                TabName = tableData["MOMENT_TAB_ALL"].StringValue,
            },
            {
                TabName = tableData["MOMENT_TAB_FRIEND"].StringValue,
            },
            {
                TabName = tableData["MOMENT_TAB_VOTE"].StringValue,
            },
            {
                TabName = tableData["MOMENT_TAB_LOTTERY"].StringValue,
            }
        },
        [Enum.EMomentsTabType.Find] = {
            {
                TabName = tableData["FIND_TAB_RECOMMEND"].StringValue,
            },
            {
                TabName = tableData["FIND_TAB_INTRACITY"].StringValue,
            },
            {
                TabName = tableData["FIND_TAB_FRIENDSHIP"].StringValue,
            }
        }
    }
    -- 选中不同tab以及selection 发送不同请求
    self.FindMomentsTabReq = {
        {"ReqRecommendMoments", "ReqGetLatestMoments"},
        {"ReqCityHotMoments", "ReqCityLatestMoments"},
        {"ReqServerHotMoments", "ReqServerLatestMoments"}
    }

    -- 话题排序规则信息
    self.topicSortComboBoxData = {
        {
            text = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SORT_HOT_COMMENT").StringValue
        },
        {  
            text = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SORT_LATEST_COMMENT").StringValue
        },
    }
    -- 默认话题列表
    self.deafaultTopicList = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_ComList, P_FriendGroupTopic_Item, "DefaultTopicList")
    
    -- 话题列表数据
    self.deafaultTopicData = {}

    -- 话题排序规则下拉框
    self.commentSortComboBox = P_ComboBox.CreateComboBox(self.View.WBP_ComComBox, self, self.OnSelectComboBox, self.topicSortComboBoxData)

    -- 当前选择的数据tab筛选索引
    self.PageTabIndexSelected = nil
    -- 朋友圈内容数据(不包括已拉黑玩家)
    self.momentsContentData = {}

    -- 上一次查看的最后一条动态id
    self.lastMID = nil
    -- 上一次查看的最后一条动态创建时间
    self.lastCreateTime = nil
    -- 发现列表上一次查看的最后一页动态id
    self.lastMIDs = {}

    -- 当前加载到的列表索引
    self.curListIndex = 0

    -- 动态详情面板
    -- self.momentsDetail = self:BindComponent(self.View.WBP_FriendGroupInfo, P_FriendGroupInfo)
    -- 默认隐藏详情面板
    self.View.WBP_FriendGroupInfo:SetVisibility(ESlateVisibility.Collapsed)

    self.View.WBP_HotTopicRankBtn.Text_Com:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_HOT_TOPIC").StringValue)

    self.View.WBP_ComSearchBtn.WidgetRoot:SetIcon(0)

    -- self.View.WBP_ComBtnIconNew.Icon
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComMsgBtn.Btn_ClickArea, self.OnClickMsgCenter)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_HotTopicRankBtn.Btn_Com, self.OnClickHotTopicRank)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComSearchBtn.Btn_ClickArea, self.OnClickSearchTopicRank)
end

function P_FriendGroupDefault_Panel:OnShow()
    -- 如果是第一次打开朋友圈，请求个人信息
    if Game.MomentsSystem:GetIsFirstReq() then
        Game.MomentsSystem:ReqGetPlayerInfo(Game.me.eid)
    end
end

function P_FriendGroupDefault_Panel:OnRefresh(index)
    self.TabIndex = index
    table.clear(self.momentsContentData)
    table.clear(self.lastMIDs)
    if self.TabIndex == Enum.EMomentsTabType.Moments then
        self.View.WBP_ComMsgBtn.WidgetRoot:SetIcon(0)
        self.View:Event_UI_Style(false)
    else
        self.View.WBP_ComMsgBtn.WidgetRoot:SetIcon(1)
        self.View:Event_UI_Style(true)
        -- 延时获取话题列表
        Game.TimerManager:CreateTimerAndStart(function()
            Game.MomentsSystem:ReqGetHotTopic()
        end, 200, 1)
    end
    self.View.WBP_ChatInput:SetVisibility(ESlateVisibility.Collapsed)
    self.PageTabList:SetData(self.MomentsHorTabData[self.TabIndex], function(tabIndex) self:OnClickPageTab(tabIndex) end, 1)
end

function P_FriendGroupDefault_Panel:OnClickPageTab(index)
    self.lastMID = nil
    self.lastCreateTime = nil
    table.clear(self.lastMIDs)
    self.PageTabIndexSelected = index
    -- 切换tab
    table.clear(self.momentsContentData)
    self:UpdateMomentsContent(self.PageTabIndexSelected)
end

-- 刷新朋友圈内容
function P_FriendGroupDefault_Panel:UpdateMomentsContent(index)
    if self.TabIndex == Enum.EMomentsTabType.Moments then
        Game.MomentsSystem:ReqGetAllFriendsMoments(index, self.lastMID, self.lastCreateTime)
    elseif self.TabIndex == Enum.EMomentsTabType.Find then
        if self.commentSortComboBox:GetSelectedIndex() == 1 then
            Game.MomentsSystem[self.FindMomentsTabReq[index][1]](Game.MomentsSystem, self.lastMIDs)
        elseif self.commentSortComboBox:GetSelectedIndex() == 2 then
            Game.MomentsSystem[self.FindMomentsTabReq[index][2]](Game.MomentsSystem, self.lastMID, self.lastCreateTime)
        end
    end
end

function P_FriendGroupDefault_Panel:OnGetMomentsListContent(data, bIsPublish, tabIndex)
    if tabIndex and tabIndex ~= self.TabIndex then return end
    if data ~= nil and type(data) == "table" then
        if #data > 0 then
            table.clear(self.lastMIDs)
            self.lastMID = data[#data].mid
            self.lastCreateTime = data[#data].createTime
        end
        if bIsPublish and self.TabIndex == Enum.EMomentsTabType.Moments then
            table.insert(self.momentsContentData, 1, data[1])
            if data[1].repostMID ~= '' then
                for i,v in ipairs(self.momentsContentData) do
                    if v.mid == data[1].repostMID then
                        v.repostNum = v.repostNum + 1
                        break
                    end
                end
            end
        elseif not bIsPublish then
            for k,v in pairs(data) do
                -- 剔除黑名单玩家的动态，同时保留对应动态ID用于拉取下一页数据
                if not Game.FriendSystem:IsInBlackList(v.pid) then
                    table.insert(self.momentsContentData, v)
                end
                table.insert(self.lastMIDs, v.mid)
            end
        end
    end
    self.MomentsContentList:SetData(self.momentsContentData)
end

function P_FriendGroupDefault_Panel:OnDeleteMoment(mid)
    local bIsFind = false
    for i,v in ipairs(self.momentsContentData) do
        if v.mid == mid then
            table.remove(self.momentsContentData, i)
            bIsFind = true
            break
        end
    end
    if not bIsFind then return end
    self.MomentsContentList:SetData(self.momentsContentData)
    -- local lastIndex = #self.momentsContentData
    -- if self.momentsContentData and lastIndex > 0 then
    --     self.lastMID = self.momentsContentData[lastIndex].mid
    --     self.lastCreateTime = self.momentsContentData[lastIndex].createTime
    -- end
end

function P_FriendGroupDefault_Panel:OnHide()
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.FriendGroupInfo_Panel) then
        Game.NewUIManager:ClosePanel(UIPanelConfig.FriendGroupInfo_Panel)
    end
end

-- 获取动态内容大小
function P_FriendGroupDefault_Panel:GetMomentsContentSize(index)
    local posY = 0
    local contentInfo = self.momentsContentData[index]
    if contentInfo and contentInfo.MomentsHeight == nil then
        -- 计算头像信息+普通文本
        posY = self.View.DiffRichText:GetTextScale(Game.MomentsSystem:ProcessMomentsContent(contentInfo.content), true).Y
        if posY ~= 0 then
            contentInfo.MomentsHeight = posY + 160
        end
        -- 计算转发内容
        if contentInfo.repostMID ~= '' then
            if contentInfo.repost == nil then
                -- 原动态已经被删除
                contentInfo.MomentsHeight = contentInfo.MomentsHeight + 135
            else
                contentInfo.MomentsHeight = contentInfo.MomentsHeight + self.View.DiffRichText:GetTextScale(Game.MomentsSystem:ProcessMomentsContent(contentInfo.repost.content), true).Y + 160
                -- 如果转发的动态有图片
                if contentInfo.repost.resIDs and #contentInfo.repost.resIDs > 0 then
                    contentInfo.MomentsHeight = contentInfo.MomentsHeight + (math.floor(#contentInfo.repost.resIDs / 4) + 1) *135
                end
            end
        else
            -- 计算图片/投票等其他内容
            if contentInfo.resIDs and #contentInfo.resIDs > 0 then
                contentInfo.MomentsHeight = contentInfo.MomentsHeight + (math.floor(#contentInfo.resIDs / 4) + 1) *135
            end
        end
    end
    return 0, contentInfo and contentInfo.MomentsHeight or 0
end

-- 动态列表拉到底部回调，加载更多动态
function P_FriendGroupDefault_Panel:GetMoreMomentsContent(bIsToEnd)
    if not bIsToEnd then return end
    -- if self.MomentsContentList.oldOffect+self.MomentsContentList.height >= self.MomentsContentList.length - 0.01 and self.MomentsContentList.oldOffect > 0.0 then
    --     Game.MomentsSystem:ReqGetAllFriendsMoments(self.PageTabIndexSelected, self.lastMID, self.lastCreateTime)
    -- end
    Game.TimerManager:CreateTimerAndStart(function()
        self:UpdateMomentsContent(self.PageTabIndexSelected)
    end, 200, 1)
end

-- 打开消息中心
function P_FriendGroupDefault_Panel:OnClickMsgCenter()
    Game.NewUIManager:OpenPanel("MessageCenterPanel")
end

function P_FriendGroupDefault_Panel:OnChangeCommentNum()
    self.MomentsContentList:SetData(self.momentsContentData)
end

--- 选中对应的下拉列表的某项
function P_FriendGroupDefault_Panel:OnSelectComboBox(index)
    if self.TabIndex ~= Enum.EMomentsTabType.Find then return end
    table.clear(self.momentsContentData)
    table.clear(self.lastMIDs)
    self.lastMID = nil
    self.lastCreateTime = nil
	self:UpdateMomentsContent(self.PageTabIndexSelected)
end

-- 查看热门话题按钮
function P_FriendGroupDefault_Panel:OnClickHotTopicRank()
    Game.NewUIManager:OpenPanel("FriendGroupHotListPanel", false)
end

-- 搜索话题按钮
function P_FriendGroupDefault_Panel:OnClickSearchTopicRank()
    Game.NewUIManager:OpenPanel("FriendGroupHotListPanel", true)
end

function P_FriendGroupDefault_Panel:OnGetHotTopicListContent(data)
    table.clear(self.deafaultTopicData)
    table.merge(self.deafaultTopicData, data.topTopics)
    for k,v in pairs(data.topicRank) do
        table.insert(self.deafaultTopicData, v)
    end
    self.deafaultTopicList:SetData(#self.deafaultTopicData)
end

function P_FriendGroupDefault_Panel:OnRefresh_DefaultTopicList(widget, index, selected)
    widget:RefreshData(index, self.deafaultTopicData[index])
end

-- 临时做法 先把详细面板挂载到这里，后续改成新框架后采用动态挂载（component）
-- function P_FriendGroupDefault_Panel:OnShowMomentsDetail(data)
--     self.View.WBP_FriendGroupInfo:SetVisibility(ESlateVisibility.Visible)
--     self.momentsDetail:OnRefresh(data)
-- end

return P_FriendGroupDefault_Panel
