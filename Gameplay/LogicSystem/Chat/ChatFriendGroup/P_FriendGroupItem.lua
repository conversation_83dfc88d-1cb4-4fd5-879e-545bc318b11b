require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupItemView")
---@class P_FriendGroupItem : UIComponent
---@field public View WBP_FriendGroupItemView
local P_FriendGroupItem = DefineClass("P_FriendGroupItem", UIComponent)
local P_ComSocialHead = kg_require("Gameplay.LogicSystem.CommonUI.P_ComSocialHead")

function P_FriendGroupItem:OnCreate()
    -- 发布者头像
    self.playerSocialHead = self:BindComponent(self.View.WBP_SocialHead, P_ComSocialHead)
end

function P_FriendGroupItem:OnShow()

end

function P_FriendGroupItem:OnListRefresh(parentUI, bIsSelect, allData, index)
    Log.Debug("OnListRefresh", parentUI, bIsSelect, allData, index)
    self.playerSocialHead:Refresh({Level = allData.level })
    self.playerSocialHead:SetHeadImage(allData.head)
    self.View.KGRichTextBlock_0:SetText(allData.content)

end

function P_FriendGroupItem:OnHide()

end

return P_FriendGroupItem
