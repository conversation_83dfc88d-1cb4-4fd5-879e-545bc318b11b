kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupRecoverSelection_ItemView")
---@class P_FriendGroupRecoverSelection_Item : UIComponent
---@field public View WBP_FriendGroupRecoverSelection_ItemView
local P_FriendGroupRecoverSelection_Item = DefineClass("P_FriendGroupRecoverSelection_Item", UIComponent)
local P_ComboBox = kg_require("Gameplay.LogicSystem.CommonUI.P_ComboBox")

function P_FriendGroupRecoverSelection_Item:OnCreate()
    -- 评论排序规则信息
    self.commentSortComboBoxData = {
        {
            text = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SORT_HOT_COMMENT").StringValue
        },
        {
            text = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_SORT_LATEST_COMMENT").StringValue
        }
    }
    -- 当前详情界面动态mid信息
    self.mid = nil
    -- 父级界面
    self.parentUI = nil
    -- 评论排序规则下拉框
    self.commentSortComboBox = P_ComboBox.CreateComboBox(self.View.WBP_SortComBox, self, self.OnSelectComboBox, self.commentSortComboBoxData)
end

function P_FriendGroupRecoverSelection_Item:OnShow()

end

function P_FriendGroupRecoverSelection_Item:OnHide()

end

function P_FriendGroupRecoverSelection_Item:OnListRefresh(parentUI, bIsSelect, allData, index)
    self.parentUI = parentUI
    self.mid = allData[index].Info
end

--- 选中对应的下拉列表的某项
function P_FriendGroupRecoverSelection_Item:OnSelectComboBox(index)
    if not self.parentUI then
        return
    end
    self.parentUI.sortType = index
    self.parentUI:OnClearCommentCache()
	if index == 1 then
        -- 请求热门评论数据
        Game.MomentsSystem:ReqGetHotCommentList(self.mid, nil, nil, nil, true)
    elseif index == 2 then
        -- 请求最新评论数据
        Game.MomentsSystem:ReqGetLatestCommentList(self.mid, nil, nil, nil, true)
    end
end

return P_FriendGroupRecoverSelection_Item
