--------No Class Find, Create a New UIController Class Using Given Filename--------
---@class WBP_ComTabSlcView : WBP_ComTabSlc_C
---@field public WidgetRoot WBP_ComTabSlc_C
---@field public Bg_Lock Image
---@field public Text_Name KGTextBlock
---@field public Button KGButton
---@field public Ani_In WidgetAnimation
---@field public Ani_On WidgetAnimation
---@field public SlcPreview number
---@field public BgType number
---@field public BgNoml SlateBrush
---@field public BgWhite SlateBrush
---@field public Event_Tab_Style fun(self:self,SetSwitcher:number,BgType:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetSelect fun(self:self,Set Switcher:number,BgType:number):void


---@class WBP_ComTabHorView : WBP_ComTabHor_C
---@field public WidgetRoot WBP_ComTabHor_C
---@field public HB_Btn HorizontalBox
---@field public WBP_ComSlcTab1 WBP_ComTabSlcView
---@field public BgType number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Event_UI_Style fun(self:self,BgType:number):void


---@class WBP_ComComBoxView : WBP_ComComBox_C
---@field public WidgetRoot WBP_ComComBox_C
---@field public size_total SizeBox
---@field public Img_Arrow Image
---@field public Text_Target KGTextBlock
---@field public Button KGButton
---@field public Ani_Spread WidgetAnimation
---@field public Ani_Fewer WidgetAnimation
---@field public Size number
---@field public Pos number
---@field public ContentHeight number
---@field public ContentScrollCountCondition number
---@field public Text Color SlateColor
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetSize fun(self:self,Size:number):void
---@field public SetTextColor fun(self:self,TextColor:SlateColor):void


---@class WBP_ComHUDBtnIconView : WBP_ComHUDBtnIcon_C
---@field public WidgetRoot WBP_ComHUDBtnIcon_C
---@field public Icon Image
---@field public Button KGButton
---@field public Anim_1 WidgetAnimation
---@field public Anim_click WidgetAnimation
---@field public Anim_3 WidgetAnimation
---@field public Anim_4 WidgetAnimation
---@field public Btn Style ST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound SlateSound
---@field public HUD Btn Style ST_ComHUDBtnIcon
---@field public bIsCheck boolean
---@field public BndEvt__WBP_ComHUDBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public SetIsCheck fun(self:self,InIsCheck:boolean):void
---@field public BndEvt__WBP_ComHUDBtnIcon_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonHoverEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComHUDBtnIcon_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonHoverEvent__DelegateSignature fun(self:self):void
---@field public Play Hint Anim fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Construct fun(self:self):void
---@field public Set Btn Style fun(self:self,Btn Style:ST_ComHUDBtnIcon):void


---@class WBP_ComTreeListView : WBP_ComTreeList_C
---@field public WidgetRoot WBP_ComTreeList_C
---@field public TreeList KGScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public SelectionMode number
---@field public IndexList number
---@field public groupList number
---@field public parentList number
---@field public LayoutList ListLayout
---@field public SpaceUpList number
---@field public SpaceBottomList number
---@field public SpaceLeftList number
---@field public SpaceRightList number
---@field public AlignmentList ListAligment
---@field public indexToTopPos number
---@field public indexToBottomPos number
---@field public indexToXPos number
---@field public oldBottomIndex number
---@field public oldTopIndex number
---@field public allLength number
---@field public PaddingList Margin
---@field public ListPadding Margin
---@field public MaxValueDown number
---@field public RetainerBox RetainerBox
---@field public MaxValueUp number
---@field public IsMultiMenu boolean
---@field public Animation string
---@field public PreviewAnimation boolean
---@field public OnlyNotifyByAnimation boolean
---@field public AniName string
---@field public Structure TreeListCell
---@field public PreviewIndex number
---@field public aniinfo number
---@field public startTime number
---@field public finished number
---@field public maxTime number
---@field public last number
---@field public outPos Vector2D
---@field public cells Widget
---@field public group number
---@field public floor number
---@field public kind number
---@field public parent number
---@field public isAniNotify boolean
---@field public ani string
---@field public ListPlayAnimation MulticastDelegate
---@field public floorFrequence number
---@field public groupFrequence number
---@field public aniMoveData TreeListMoveAnimationStruct
---@field public aniStaggerData TreeListCellAnimationStruct
---@field public PlayListAnimation fun(self:self,ConfigName:string):void
---@field public OnUserScrolled fun(self:self,CurrentOffset:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public MoveAnimation fun(self:self,config:TreeListMoveAnimation):void
---@field public StaggerAnimation fun(self:self,config:TreeListStaggerAnimation):void
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number,libWidget:string,sizeX:number,sizeY:number):Widget,Vector2D
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public InsertSubIndex fun(self:self,floor:number,parentidx:number):void
---@field public GetArrayWidget fun(self:self,index:number):Widget,string,number,number,number,Widget,string,number,number,number
---@field public GetListSize fun(self:self):number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidget:Widget,LibSize:Vector2D):void
---@field public CalculatePos fun(self:self):void
---@field public RebulidList fun(self:self,CurrentOffset:number):Widget,Vector2D
---@field public Cal_OnGirdNextGrid fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,oldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number,number,number,number,number
---@field public Cal_OnGirdNextList fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,OldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number
---@field public Cal_OnListNextGrid fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,OldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number
---@field public Cal_OnListNextList fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,OldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number
---@field public SafeGetFloatArray fun(self:self,InArray:number,key:number):number,number


---@class WBP_SocialHeadView : WBP_SocialHead_C
---@field public WidgetRoot WBP_SocialHead_C
---@field public img_HeadBack Image
---@field public Img_HeadIcon KGImage
---@field public panel_status CanvasPanel
---@field public Img_lvback Image
---@field public Text_level KGTextBlock
---@field public Img_probg Image
---@field public icon_RT Image
---@field public icon_LT KGImage
---@field public Skill_seleced Image
---@field public Btn_Head KGButton
---@field public IconNone SlateBrush
---@field public TeamLeader SlateBrush
---@field public GroupLeader SlateBrush
---@field public Is Offline boolean
---@field public Is Seletcted boolean
---@field public IsEmpty boolean
---@field public AddSwitch number
---@field public Member Type number
---@field public LT SlateBrush
---@field public HeadSizeX number
---@field public HeadSizeY number
---@field public IconSizeX number
---@field public IconSizeY number
---@field public Event_UI_Style fun(self:self,IsOffline:boolean,IsSeletcted:boolean,MemberType:number,IsEmpty:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetOffline fun(self:self,IsOffline:boolean):void
---@field public SetEmpty fun(self:self,Empty:boolean):void
---@field public SetLT fun(self:self,Member:number):void
---@field public SetSize fun(self:self,HeadSizeX:number,HeadSizeY:number):void
---@field public SetSize2 fun(self:self,InWidthOverride:number,InHeightOverride:number):void
---@field public SetAddSwitch fun(self:self,AddSwitch:number):void


---@class WBP_ChatDiscloseBtnView : WBP_ChatDiscloseBtn_C
---@field public WidgetRoot WBP_ChatDiscloseBtn_C
---@field public TB_Word KGTextBlock
---@field public Button KGButton
---@field public IsDisclose boolean
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Event_UI_Style fun(self:self,IsDisclose:boolean):void


---@class WBP_ComListView : WBP_ComList_C
---@field public WidgetRoot WBP_ComList_C
---@field public ListRoot CanvasPanel
---@field public BgSlot NamedSlot
---@field public List KGScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public bIsTileView boolean
---@field public PreviewCount number
---@field public LibWidget ListLib
---@field public ScrollWidget Widget
---@field public Orientation EOrientation
---@field public ScrollBarVisibility ESlateVisibility
---@field public ScrollBarStyle ScrollBarStyle
---@field public SelectionMode number
---@field public Space ListSpace
---@field public Alignment ComListAligment
---@field public bIsCenterContent boolean
---@field public tempIndex number
---@field public oldPosX number
---@field public oldPosY number
---@field public tempPosX number
---@field public tempPosY number
---@field public widgetX number
---@field public widgetY number
---@field public spaceUp number
---@field public spaceBottom number
---@field public spaceLeft number
---@field public spaceRight number
---@field public bSizeToContent boolean
---@field public ListPadding Margin
---@field public MaxValueDown number
---@field public RetainerBox RetainerBox
---@field public OnSetItem MulticastDelegate
---@field public Animation string
---@field public PreviewAnimation boolean
---@field public OnlyNotifyByAnimation boolean
---@field public AniName string
---@field public PriviewIndex number
---@field public AddorRemove boolean
---@field public ExtraAniName string
---@field public Operation CellMoveOperation
---@field public AnimationCurve CurveFloat
---@field public OpacityCurve CurveFloat
---@field public Frequence number
---@field public Duration number
---@field public StartOffset Vector2D
---@field public EndOffset Vector2D
---@field public StartOpacity number
---@field public MaxValueUp number
---@field public MaxValueLeft number
---@field public MaxValueRight number
---@field public StartTime number
---@field public ListPlayAnimation MulticastDelegate
---@field public EndOpacity number
---@field public isAniNotyfy boolean
---@field public MinSize number
---@field public MaxSize number
---@field public Space Space Up number
---@field public outCells Widget
---@field public outPos Vector2D
---@field public PreviewNum number
---@field public NewVar Object
---@field public cellMove boolean
---@field public isPlaying boolean
---@field public ListCellRemove MulticastDelegate
---@field public ListCellAdd MulticastDelegate
---@field public ani string
---@field public finished number
---@field public PlayListAnimation fun(self:self,ConfigName:string):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public StaggerAnimation fun(self:self,config:ComListStaggerAnimation):void
---@field public MoveAnimation fun(self:self,config:ComListMoveAnimation):void
---@field public AddCell fun(self:self,config:ComListMoveAnimation):void
---@field public RemoveCell fun(self:self,config:ComListMoveAnimation):void
---@field public CalculatePos fun(self:self):Widget,Vector2D,number
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number):Widget
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public GetListSize fun(self:self):number,number,number,number,number,number,number,number,number,number,number,number,number,number,number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number,number,number,number,number
---@field public VerticalTileChange fun(self:self):void
---@field public VerticalTile fun(self:self):void
---@field public VerticalList fun(self:self):void
---@field public HorizontalTileChange fun(self:self):void
---@field public HorizontalTile fun(self:self):void
---@field public HorizontalList fun(self:self):void
---@field public VerticalTileAuto fun(self:self):void
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidfget:Widget):void
---@field public VerticalListAuto fun(self:self):void
---@field public SetSlotSize fun(self:self,X:number,Y:number):void
---@field public GetAutoListSize fun(self:self):void
---@field public CheckCellMove fun(self:self):boolean,boolean


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public bg KGImage
---@field public hb_btn HorizontalBox
---@field public Text_Com KGTextBlock
---@field public Text_Time KGTextBlock
---@field public Image KGImage
---@field public Btn_Com KGButton
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein_normal WidgetAnimation
---@field public Ani_Fadein_Light WidgetAnimation
---@field public Ani_Fadein_blue WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public SequenceEvent fun(self:self):void
---@field public Construct fun(self:self):void
---@field public OnVisibilityChangedEvent fun(self:self,InVisibility:ESlateVisibility):void
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_ComBtnIconNewView : WBP_ComBtnIconNew_C
---@field public WidgetRoot WBP_ComBtnIconNew_C
---@field public OutCanvas CanvasPanel
---@field public Icon Image
---@field public Text_Name TextBlock
---@field public Big_Button_ClickArea KGButton
---@field public Anim_1 WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public Ani_Hover WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public Btn Style ST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound SlateSound
---@field public Top number
---@field public Event_UI_Style fun(self:self,BtnName:string):void
---@field public Play Hint Anim fun(self:self):void
---@field public BndEvt__WBP_ComBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Btn Style fun(self:self,Btn Style:ST_ComBtnIcon):void
---@field public SetVxSize fun(self:self):void


---@class WBP_MessageCenterItemView : WBP_MessageCenterItem_C
---@field public WidgetRoot WBP_MessageCenterItem_C
---@field public WBP_SocialHead WBP_SocialHeadView
---@field public MsgText KGRichTextBlock
---@field public Btn_Go WBP_ComBtnView
---@field public Btn_Add WBP_ComBtnIconNewView
---@field public VoteText KGRichTextBlock
---@field public Display Mode number
---@field public Player Mode State number
---@field public Chou Jiang State number
---@field public Referendum State number
---@field public Event_UI_Style fun(self:self,DisplayMode:number,PlayerModeState:number,ChouJiangState:number,ReferendumState:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetState fun(self:self,Display Mode:number,Player Mode State:number,Chou Jiang State:number,Referendum State:number):void
---@field public Clear fun(self:self):void


---@class WBP_ChatRoom_NumberView : WBP_ChatRoom_Number_C
---@field public WidgetRoot WBP_ChatRoom_Number_C
---@field public Text_Location KGTextBlock
---@field public Btn_Inter KGButton
---@field public Prew Text string
---@field public Icon SlateBrush
---@field public Event_UI_Style fun(self:self,InBrush:SlateBrush,Test:string):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Style fun(self:self,InBrush:SlateBrush,InText:string):void


---@class WBP_TaskBtnShareView : WBP_TaskBtnShare_C
---@field public WidgetRoot WBP_TaskBtnShare_C
---@field public Btn KGButton


---@class WBP_PlayerFriendGroupPost_ItemView : WBP_PlayerFriendGroupPost_Item_C
---@field public WidgetRoot WBP_PlayerFriendGroupPost_Item_C
---@field public WBP_SocialHead WBP_SocialHeadView
---@field public WBP_ChatDiscloseBtn WBP_ChatDiscloseBtnView
---@field public RT_PlayerHeadTitle KGRichTextBlock
---@field public Moments_Text_Content KGRichTextBlock
---@field public WBP_ComList WBP_ComListView
---@field public Moments_Text_Content_1 KGRichTextBlock
---@field public WBP_ComList_1 WBP_ComListView
---@field public WBP_SocialHead_3 WBP_SocialHeadView
---@field public RT_PlayerHeadTitle_1 KGRichTextBlock
---@field public WBP_MessageCenterItem WBP_MessageCenterItemView
---@field public WBP_MessageCenterItem_3 WBP_MessageCenterItemView
---@field public WBP_Like_Num WBP_ChatRoom_NumberView
---@field public WBP_Comment_Num WBP_ChatRoom_NumberView
---@field public WBP_Repost_Num WBP_ChatRoom_NumberView
---@field public WBP_BtnShare WBP_TaskBtnShareView
---@field public WBP_BtnMore WBP_TaskBtnShareView
---@field public State number
---@field public Event_UI_State fun(self:self,State:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Raffle fun(self:self):void
---@field public Clear fun(self:self):void
---@field public Set Self Post fun(self:self):void
---@field public Set Reprint fun(self:self):void
---@field public Set Referendum fun(self:self):void


---@class WBP_ComInputView : WBP_ComInput_C
---@field public WidgetRoot WBP_ComInput_C
---@field public SearchTipsBtn KGButton
---@field public icon_search Image
---@field public EditText KGEditableTextBox
---@field public Btn KGButton
---@field public Img_Cancel Image
---@field public Button_search KGButton
---@field public Ani_Press WidgetAnimation
---@field public Ani_Hover WidgetAnimation
---@field public HintText string
---@field public Has Search boolean
---@field public Has SearchTips boolean
---@field public IsNumberOnly boolean
---@field public In Brush SlateBrush
---@field public Event_UI_SetBrush fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetBtn fun(self:self):void
---@field public SetHint fun(self:self):void
---@field public SetSearchTips fun(self:self,NewParam:boolean):void


---@class WBP_ChatInputSmallView : WBP_ChatInputSmall_C
---@field public WidgetRoot WBP_ChatInputSmall_C
---@field public CommonInput HorizontalBox
---@field public CommonBox SizeBox
---@field public CommonChannelComBox WBP_ComComBoxView
---@field public VoiceBtn KGButton
---@field public RefMsg CanvasPanel
---@field public Text_Content KGRichTextBlock
---@field public RefCancle KGButton
---@field public WBP_Input WBP_ComInputView
---@field public ToTextCanvas KGCanvasPanel
---@field public ToTextButton KGButton
---@field public EmotBtn WBP_ComBtnIconNewView
---@field public SendBtn WBP_ComBtnView


---@class WBP_ComCheckBoxView : WBP_ComCheckBox_C
---@field public WidgetRoot WBP_ComCheckBox_C
---@field public CheckBox KGCheckBox
---@field public TB_Name KGTextBlock
---@field public Button KGButton
---@field public Ani_Press WidgetAnimation
---@field public TextValue string
---@field public TextColor SlateColor
---@field public Undetermined boolean
---@field public Has Text boolean
---@field public SetUndetermined fun(self:self,Undetermined:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ChatInputView : WBP_ChatInput_C
---@field public WidgetRoot WBP_ChatInput_C
---@field public SettingButton WBP_ComBtnIconNewView
---@field public CommonInput HorizontalBox
---@field public WBP_ChatInputSmall WBP_ChatInputSmallView
---@field public ShowValue HorizontalBox
---@field public ShowValuablesOnly WBP_ComCheckBoxView
---@field public BanInput HorizontalBox
---@field public BtnBox HorizontalBox
---@field public BtnJump WBP_ComBtnView
---@field public Ani_ChatInput_Content_in WidgetAnimation
---@field public IconHistory SlateBrush
---@field public OnVisibilityChanged_事件 fun(self:self,InVisibility:ESlateVisibility):void
---@field public Construct fun(self:self):void
---@field public SetWS fun(self:self,Index:number):void


---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button KGButton
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public IconNormal SlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public IconWhite SlateBrush
---@field public IconBlack SlateBrush
---@field public BtnType number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Event_UI_Style fun(self:self,BtnType:number):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void


---@class WBP_FriendGroupRecover_ItemView : WBP_FriendGroupRecover_Item_C
---@field public WidgetRoot WBP_FriendGroupRecover_Item_C
---@field public WBP_Post_SocialHead WBP_SocialHeadView
---@field public RT_Post_PlayerHeadTitle KGRichTextBlock
---@field public Post_Reply_Text KGRichTextBlock
---@field public SB_Post_ChouJiang SizeBox
---@field public WBP_Post_MessageCenterItem WBP_MessageCenterItemView
---@field public WBP_Post_BtnReply WBP_ChatRoom_NumberView
---@field public WBP_Post_Like WBP_ChatRoom_NumberView
---@field public WBP_Post_More WBP_TaskBtnShareView


---@class WBP_FriendGroupRecoverSelection_ItemView : WBP_FriendGroupRecoverSelection_Item_C
---@field public WidgetRoot WBP_FriendGroupRecoverSelection_Item_C
---@field public WBP_SortComBox WBP_ComComBoxView


---@class WBP_FriendGroupMoreRecover_ItemView : WBP_FriendGroupMoreRecover_Item_C
---@field public WidgetRoot WBP_FriendGroupMoreRecover_Item_C
---@field public BtnMore_Comment KGButton
---@field public WBP_More_Comment WBP_ChatRoom_NumberView


---@class WBP_FriendGroupInfoView : WBP_FriendGroupInfo_C
---@field public WidgetRoot WBP_FriendGroupInfo_C
---@field public WBP_CommentTreeList WBP_ComTreeListView
---@field public WBP_ComBtnCloseNew WBP_ComBtnCloseNewView
---@field public WBP_PlayerFriendGroupPost_Item WBP_PlayerFriendGroupPost_ItemView
---@field public WBP_FriendGroupRecover_Item WBP_FriendGroupRecover_ItemView
---@field public WBP_FriendGroupRecoverSelection_Item WBP_FriendGroupRecoverSelection_ItemView
---@field public WBP_FriendGroupRecover_SubItem WBP_FriendGroupRecover_ItemView
---@field public WBP_ComInputSearch WBP_ComInputView
---@field public WBP_EmotBtn WBP_ComBtnIconNewView
---@field public WBP_SendBtn WBP_ComBtnView
---@field public WBP_FriendGroupMoreRecover_Item WBP_FriendGroupMoreRecover_ItemView
---@field public DiffRichText KGRichTextBlock
---@field public SubDiffRichText KGRichTextBlock


---@class WBP_FriendGroupDefault_PanelView : WBP_FriendGroupDefault_Panel_C
---@field public WidgetRoot WBP_FriendGroupDefault_Panel_C
---@field public WBP_ComTabHor WBP_ComTabHorView
---@field public WBP_ComComBox WBP_ComComBoxView
---@field public WBP_ComSearchBtn WBP_ComHUDBtnIconView
---@field public WBP_ComMsgBtn WBP_ComHUDBtnIconView
---@field public WBP_ComTreeList WBP_ComTreeListView
---@field public WBP_FriendGroupItem WBP_PlayerFriendGroupPost_ItemView
---@field public WBP_ChatInput WBP_ChatInputView
---@field public DiffRichText KGRichTextBlock
---@field public WBP_ComList WBP_ComListView
---@field public WBP_HotTopicRankBtn WBP_ComBtnView
---@field public MomentsDetailRoot CanvasPanel
---@field public WBP_FriendGroupInfo WBP_FriendGroupInfoView
---@field public Is Topic boolean
---@field public Event_UI_Style fun(self:self,Is Topic:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Topic fun(self:self,Is Topic:boolean):void


---@class P_FriendGroupDefault_PanelView : WBP_FriendGroupDefault_PanelView
---@field public controller P_FriendGroupDefault_Panel
local P_FriendGroupDefault_PanelView = DefineClass("P_FriendGroupDefault_PanelView", UIView)

function P_FriendGroupDefault_PanelView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {{self.WBP_HotTopicRankBtn_lua.WidgetRoot, 0.6},},AnimFadeOut = {}}
end

function P_FriendGroupDefault_PanelView:OnDestroy()
end

return P_FriendGroupDefault_PanelView
