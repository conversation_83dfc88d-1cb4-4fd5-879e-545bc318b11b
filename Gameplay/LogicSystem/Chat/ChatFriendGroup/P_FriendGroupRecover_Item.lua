kg_require("Gameplay.LogicSystem.Chat.ChatFriendGroup.P_FriendGroupRecover_ItemView")
---@class P_FriendGroupRecover_Item : UIComponent
---@field public View WBP_FriendGroupRecover_ItemView
local P_FriendGroupRecover_Item = DefineClass("P_FriendGroupRecover_Item", UIComponent)
local P_ComSocialHead = kg_require("Gameplay.LogicSystem.CommonUI.P_ComSocialHead")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

P_FriendGroupRecover_Item.eventBindMap = {
}

function P_FriendGroupRecover_Item:OnCreate()
    -- 当前评论信息
    self.commentData = nil
    -- 当前评论是否被玩家点赞
    self.bIsLiked = false
    -- 楼主头像
    self.postSocialHead = self:BindComponent(self.View.WBP_Post_SocialHead, P_ComSocialHead)
    -- 父列表UI
    self.parentUI = nil
    -- 当前评论id
    self.commentIndex = 0
    -- 是否二级评论
    self.bIsSubComment = false
    -- 二级评论id
    self.subIndex = nil

    -- 临时处理 迁移到新框架后去掉
    Game.GlobalEventSystem:RemoveTargetAllListeners()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.MOMENTS_ON_CHANGE_LIKE_STATE, "OnRefreshLikeState", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.MOMENTS_ON_DELETE_COMMENT, "OnDeleteMomentsComment", self)
end

function P_FriendGroupRecover_Item:OnClose()
    Game.GlobalEventSystem:RemoveTargetAllListeners()
end

function P_FriendGroupRecover_Item:OnListRefresh(parentUI, bIsSelect, allData, index, subIndex)
    self.parentUI = parentUI
    self.commentIndex = index
    self.subIndex = subIndex
    self.bIsSubComment = allData[index].Children and allData[index].Children[subIndex]
    self.commentData = self.bIsSubComment and allData[index].Children[subIndex].Info or allData[index].Info
    self.bIsLiked = Game.MomentsSystem:GetLikeIDs()[self.commentData.cid]
    local Date = {}
	_G._time(Date, self.commentData.createTime * 1000)
    self.View.RT_Post_PlayerHeadTitle:SetText(string.format("%s %s\n%s", Game.MomentsSystem:HandleStringValue("MOMENTS_PLAYER_NAME", self.commentData.nickname), 
        Game.MomentsSystem:HandleStringValue("MOMENTS_SERVER_NAME", Game.MomentsSystem:GetUrlTable()[self.commentData.serverID]), 
        Game.MomentsSystem:HandleStringValue("MOMENTS_PUBLISH_TIME", Date.year, Date.month, Date.day)))

    self.View.Post_Reply_Text:SetText(Game.ChatSystem:ProcessMomentsEmoMessage(self.commentData.content))
    self.View.WBP_Post_Like:BP_SetType(self.View.WBP_Post_Like.Icon)
    self.View.WBP_Post_Like.Text_Name:SetText(Game.MomentsSystem:FormatNumValue(self.commentData.likeNum))

    self.postSocialHead:SetBindButton()
    self.postSocialHead:Refresh({ProfessionID = self.commentData.school, Level = self.commentData.level, OnClickedCallBack = function() self:OnClick_PlayerHead() end})
    self.View.WBP_Post_BtnReply:BP_SetType(self.View.WBP_Post_BtnReply.Icon)
    self.View.WBP_Post_BtnReply.Text_Name:SetText(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_REPLY_STRING").StringValue)
end

function P_FriendGroupRecover_Item:OnClick_PlayerHead()
    Game.TeamSystem:PlayerCardUIDataAsync(self.commentData.pid, false, Enum.EFriendAddSourceData.CHAT, nil, Enum.EMenuType.Chat)
end

-- 点击回复评论
function P_FriendGroupRecover_Item:OnClick_WBP_Post_BtnReply_Btn_Inter()
    self.parentUI:OnChangeCommentTarget(self.commentIndex, self.bIsSubComment, self.commentData.nickname)
end

-- 点击点赞
function P_FriendGroupRecover_Item:OnClick_WBP_Post_Like_Btn_Inter()
    Game.MomentsSystem:ReqLikeComment(self.commentData.pid, self.commentData.serverID, self.commentData.mid, self.commentData.parentID, self.commentData.cid)
end

-- 点击更多按钮
function P_FriendGroupRecover_Item:OnClick_WBP_Post_More_Btn()
    Game.TabClose:AttachPanel("FriendGroupInfoBtnList_Panel", Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, self.View.WidgetRoot)
    local cachedGeometry = self.View.WBP_Post_More.WidgetRoot:GetCachedGeometry()
    local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
    local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
        _G.GetContextObject(), cachedGeometry, localSize, nil, nil
    )
    local selection = nil
    self.commentData.publishPid = self.parentUI:GetPublishPid(self.commentIndex, self.subIndex)
    if self.commentData.pid == Game.me.eid or self.commentData.publishPid == Game.me.eid then
        selection = {{name = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_DELETE_STRING").StringValue, callbackFuncName = "OnSelectDeleteComment"}}
    else
        selection = {{name = Game.TableData.GetMomentsStringConstDataRow("MOMENTS_REPORT_STRING").StringValue, callbackFuncName = "OnSelectReportComment"}}
    end
    Game.NewUIManager:OpenPanel(
                "FriendGroupInfoBtnList_Panel", viewportPosition.X - 200, viewportPosition.Y - 60,
                selection, self.commentData
            )
end

function P_FriendGroupRecover_Item:OnDeleteMomentsComment(data)
    if not data.cid or data.cid ~= self.commentData.cid then return end
    self.parentUI:OnDeleteComment(self.commentIndex, self.subIndex)
end

function P_FriendGroupRecover_Item:OnRefreshLikeState(cid, likeNum)
    if cid ~= self.commentData.cid then return end
    self.commentData.likeNum = likeNum
    -- TODO 标识是否自己点赞
    -- if Game.MomentsSystem:GetLikeIDs()[self.commentData.cid] ~= nil then
    --     self.View.WBP_Post_Like:Event_UI_Style(self.View.WBP_Post_Like.Icon, Game.MomentsSystem:FormatNumValue(self.commentData.likeNum))
    -- else
    --     self.View.WBP_Post_Like:Event_UI_Style(self.View.WBP_Post_Like.Icon, Game.MomentsSystem:FormatNumValue(self.commentData.likeNum))
    -- end
    self.View.WBP_Post_Like:BP_SetType(self.View.WBP_Post_Like.Icon)
    self.View.WBP_Post_Like.Text_Name:SetText(Game.MomentsSystem:FormatNumValue(self.commentData.likeNum))
end

return P_FriendGroupRecover_Item
