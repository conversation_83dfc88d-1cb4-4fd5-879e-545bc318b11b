---@class WBP_SocialHeadView : WBP_SocialHead_C
---@field public WidgetRoot WBP_SocialHead_C
---@field public img_HeadBack Image
---@field public Skill_seleced Image
---@field public Img_HeadIcon KGImage
---@field public panel_status CanvasPanel
---@field public Img_lvback Image
---@field public Text_level KGTextBlock
---@field public Img_probg Image
---@field public icon_RT Image
---@field public icon_LT KGImage
---@field public Btn_Head KGButton
---@field public IconNone SlateBrush
---@field public TeamLeader SlateBrush
---@field public GroupLeader SlateBrush
---@field public Is Offline boolean
---@field public Is Seletcted boolean
---@field public IsEmpty boolean
---@field public AddSwitch number
---@field public Member Type number
---@field public LT SlateBrush
---@field public HeadSizeX number
---@field public HeadSizeY number
---@field public IconSizeX number
---@field public IconSizeY number
---@field public Event_UI_Style fun(self:self,IsOffline:boolean,IsSeletcted:boolean,MemberType:number,IsEmpty:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetOffline fun(self:self,IsOffline:boolean):void
---@field public SetEmpty fun(self:self,Empty:boolean):void
---@field public SetLT fun(self:self,Member:number):void
---@field public SetSize fun(self:self,HeadSizeX:number,HeadSizeY:number):void
---@field public SetSize2 fun(self:self,InWidthOverride:number,InHeightOverride:number):void
---@field public SetAddSwitch fun(self:self,AddSwitch:number):void


---@class WBP_ComListView : WBP_ComList_C
---@field public WidgetRoot WBP_ComList_C
---@field public ListRoot CanvasPanel
---@field public BgSlot NamedSlot
---@field public List ScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public bIsTileView boolean
---@field public PreviewCount number
---@field public LibWidget ListLib
---@field public ScrollWidget Widget
---@field public Orientation EOrientation
---@field public ScrollBarVisibility ESlateVisibility
---@field public ScrollBarStyle ScrollBarStyle
---@field public SelectionMode number
---@field public Space ListSpace
---@field public Alignment ComListAligment
---@field public bIsCenterContent boolean
---@field public tempIndex number
---@field public oldPosX number
---@field public oldPosY number
---@field public tempPosX number
---@field public tempPosY number
---@field public widgetX number
---@field public widgetY number
---@field public spaceUp number
---@field public spaceBottom number
---@field public spaceLeft number
---@field public spaceRight number
---@field public bSizeToContent boolean
---@field public ListPadding Margin
---@field public MaxValueDown number
---@field public RetainerBox RetainerBox
---@field public OnSetItem MulticastDelegate
---@field public Animation string
---@field public PreviewAnimation boolean
---@field public OnlyNotifyByAnimation boolean
---@field public AniName string
---@field public PriviewIndex number
---@field public AddorRemove boolean
---@field public ExtraAniName string
---@field public Operation CellMoveOperation
---@field public AnimationCurve CurveFloat
---@field public OpacityCurve CurveFloat
---@field public Frequence number
---@field public Duration number
---@field public StartOffset Vector2D
---@field public EndOffset Vector2D
---@field public StartOpacity number
---@field public MaxValueUp number
---@field public MaxValueLeft number
---@field public MaxValueRight number
---@field public StartTime number
---@field public ListPlayAnimation MulticastDelegate
---@field public EndOpacity number
---@field public isAniNotyfy boolean
---@field public MinSize number
---@field public MaxSize number
---@field public Space Space Up number
---@field public outCells Widget
---@field public outPos Vector2D
---@field public PreviewNum number
---@field public NewVar Object
---@field public cellMove boolean
---@field public isPlaying boolean
---@field public ListCellRemove MulticastDelegate
---@field public ListCellAdd MulticastDelegate
---@field public ani string
---@field public finished number
---@field public PlayListAnimation fun(self:self,ConfigName:string):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public StaggerAnimation fun(self:self,config:ComListStaggerAnimation):void
---@field public MoveAnimation fun(self:self,config:ComListMoveAnimation):void
---@field public AddCell fun(self:self,config:ComListMoveAnimation):void
---@field public RemoveCell fun(self:self,config:ComListMoveAnimation):void
---@field public CalculatePos fun(self:self):Widget,Vector2D,number
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number):Widget
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public GetListSize fun(self:self):number,number,number,number,number,number,number,number,number,number,number,number,number,number,number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number,number,number,number,number
---@field public VerticalTileChange fun(self:self):void
---@field public VerticalTile fun(self:self):void
---@field public VerticalList fun(self:self):void
---@field public HorizontalTileChange fun(self:self):void
---@field public HorizontalTile fun(self:self):void
---@field public HorizontalList fun(self:self):void
---@field public VerticalTileAuto fun(self:self):void
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidfget:Widget):void
---@field public VerticalListAuto fun(self:self):void
---@field public SetSlotSize fun(self:self,X:number,Y:number):void
---@field public GetAutoListSize fun(self:self):void
---@field public CheckCellMove fun(self:self):boolean,boolean


---@class WBP_ChatRoom_TagView : WBP_ChatRoom_Tag_C
---@field public WidgetRoot WBP_ChatRoom_Tag_C
---@field public KGTextBlock_215 KGTextBlock
---@field public State number
---@field public State_Gold SlateBrush
---@field public State_Blue SlateBrush
---@field public State_Red SlateBrush
---@field public Event_UI_Style fun(self:self,State:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set State fun(self:self,State:number):void


---@class WBP_FriendGroupItemView : WBP_FriendGroupItem_C
---@field public WidgetRoot WBP_FriendGroupItem_C
---@field public WBP_SocialHead WBP_SocialHeadView
---@field public KGRichTextBlock_0 KGRichTextBlock
---@field public WBP_ComList WBP_ComListView
---@field public KGRichTextBlock KGRichTextBlock
---@field public KGRichTextBlock_1 KGRichTextBlock
---@field public KGRichTextBlock_2 KGRichTextBlock
---@field public WBP_Moments_Tag WBP_ChatRoom_TagView
---@field public WBP_ComList_1 WBP_ComListView
---@field public State number
---@field public Event_UI_State fun(self:self,State:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Player fun(self:self):void
---@field public Set Shared fun(self:self):void
---@field public Set AddImage fun(self:self):void
---@field public Clear fun(self:self):void

---@class P_FriendGroupItemView : WBP_FriendGroupItemView
---@field public controller P_FriendGroupItem
local P_FriendGroupItemView = DefineClass("P_FriendGroupItemView", UIView)

function P_FriendGroupItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

function P_FriendGroupItemView:OnDestroy()
end

return P_FriendGroupItemView
