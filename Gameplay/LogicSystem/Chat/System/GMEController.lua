--语音控制器
local GMEController = {}

function GMEController:OnEnterRoomCompleted(result, error_info)
    if result == 0 then
        --local voiceState = Game.VoiceSystem:GetMyVoiceState() 
        --if voiceState and Game.me then
        --    Game.VoiceSystem:UpdateGroupVoiceState(Game.me.eid,voiceState)
        --end
		Game.VoiceSystem:OnEnterRoomComplete()
    end
end

function GMEController:OnExitRoomCompleted(result, error_info)
    if result == 0 then
        --Game.VoiceSystem:ExitRoom()
		--Game.VoiceSystem.GMEController:DestroyChannelContext(4)
    end
end

function GMEController:OnPttDownloadFileCompleted(result,filePath,fileID)
    Log.DebugFormat("download success:result-%s filepath-%s fileid-%s",result,filePath,fileID)
    if result == 0 then
        Game.VoiceSystem:PlayRecordedFile(filePath)
    end
end

function GMEController:OnPttUploadFileCompleted(result,filePath,fileID)
    Log.DebugFormat("upload success:result-%s filepath-%s fileid-%s",result,filePath,fileID)
    if result == 0 then
        Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_UPLOAD_COMPLETE,{filePath = filePath,fileID = fileID})
    end
end

function GMEController:OnPttRecordFileCompleted(result,filePath,duration,filesize)
    Log.DebugFormat("record success:result-%s filepath-%s duration-%d filesize-%d",result,filePath,duration,filesize)
    if result == 0 and Game.VoiceSystem.SaveRecording then
        Game.VoiceSystem:UploadRecordedFile()
    end
end

function GMEController:OnPttStreamRecognitionCompleted(result,filePath,fileID,text)
    local duration = Game.VoiceSystem.GMEController:GetVoiceFileDuration(filePath)
    Log.DebugFormat("Recognition success:result-%s filepath-%s duration-%dms fileID-%s text-%s",result,filePath,duration,fileID,text)
	local saveRecordingType = Game.VoiceSystem.SaveRecording
    if result == 0 then
        if saveRecordingType == Enum.EChatRecordingState.Upload then
            Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_UPLOAD_COMPLETE,fileID,text,duration,filePath)
        -- elseif saveRecordingType == Enum.EChatRecordingState.HUDUpload then
        --     Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_HUD_UPLOAD_COMPLETE,fileID,text,duration)
        -- elseif saveRecordingType == Enum.EChatRecordingState.Listening then
        --     Game.EventSystem:Publish(_G.EEventTypes.CHAT_VOICE_LISTENING,fileID,text,duration,filePath)
        elseif saveRecordingType == Enum.EChatRecordingState.ToText then
            Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_TOTEXT,fileID,text,duration)
        elseif saveRecordingType == Enum.EChatRecordingState.Cancle then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_SEND_VOICE_CANCEL)
        -- elseif saveRecordingType == Enum.EChatRecordingState.GuildSign then
            -- Game.EventSystem:Publish(_G.EEventTypes.CHAT_VOICE_GUILD_SIGN,fileID,text,duration,filePath)
        end
    else
        if result == 4103 then
            Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_RECORD_FAIL_TOOSHORT)
        else
            if saveRecordingType == Enum.EChatRecordingState.Listening then
                -- Game.EventSystem:Publish(_G.EEventTypes.CHAT_VOICE_LISTENING,fileID,text,duration,filePath)
            else
                if saveRecordingType == Enum.EChatRecordingState.Cancle then
                    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_SEND_VOICE_CANCEL)
                elseif result == 4100 then
                    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.VOICE_RECORDER_NO_AUDIO_DATA_WARN)
                end
                Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_RECORD_FAIL)
            end
            if result == 4098 then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_SEND_VOICE_NO_PERMISSION)
                -- 4098 代表没有权限或者设备被占用等，EnableMic为打开麦克风且需要频道参数(按道理应该是打开失败)，不确定有什么影响，先注释掉
                -- Game.VoiceSystem.GMEController:EnableMic(true)
            end
        end
    end
end

function GMEController:OnPttPlayFileCompleted(result,filePath)
    Log.DebugFormat("playfile complete:result-%s filepath-%s",result,filePath)
    Game.VoiceSystem:ClearPlayingVoice()
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_VOICE_PLAY_COMPLETE,filePath)
    if Game.VoiceSystem.VolumeTimer then
        Game.TimerManager:StopTimerAndKill(Game.VoiceSystem.VolumeTimer)
        Game.VoiceSystem.VolumeTimer = nil
    end

    Game.TimerManager:StopTimerAndKill("VolumeTimer")
    if result == 0 then
        Game.VoiceSystem:RemoveVoiceQueueTop()
    end
end

function GMEController:OnEndpointsUpdateInfo(eventid,identifier)
    Log.DebugFormat("OnEndpointsUpdateInfo:eventid-%d identifier-%s",eventid,identifier)
    --有成员进入语音房间
    if eventid == 1 then
        if Game.VoiceSystem:CheckIsInBlackList(identifier) then
            Game.VoiceSystem:AddAudioBlackList(identifier)
        end
    elseif eventid == 5 then
        -- 有用户发送音频数据事件，返回此时房间内说话的userID，通过此事件可以判断用户是否说话，并展示声纹效果
        Game.GlobalEventSystem:Publish(EEventTypesV2.GME_REALTIME_AUDIO_CHANGE, true, identifier)
    elseif eventid == 6 then
        -- 有用户停止发送音频数据事件，返回此时房间内停止说话的userID
        Game.GlobalEventSystem:Publish(EEventTypesV2.GME_REALTIME_AUDIO_CHANGE, false, identifier)
    end
end

return Class(nil,nil,GMEController)
