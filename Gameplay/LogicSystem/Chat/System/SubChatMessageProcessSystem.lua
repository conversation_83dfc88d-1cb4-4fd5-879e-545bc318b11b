local StringConst = require "Data.Config.StringConst.StringConst"
local const = kg_require("Shared.Const")
---@class ChatSystem
local ChatSystem = ChatSystem -- luacheck: ignore

function ChatSystem:InitMsgReg()
    self.MsgReg = {
        [Enum.EMsgReg.ROLE] = "<HyperLink stylename=\"Chat_PlayerName\" u=\"role=%s\">%s</>",
        [Enum.EMsgReg.ITEM] = "<HyperLink stylename=\"Quality_%d\" u=\"i=%s,%s\">%s</>",
        [Enum.EMsgReg.LOCATION] = "<HyperLink stylename=\"Chat_Locate\" u=\"loc=%s,%s,%s,%s,%s,%s\">%s</>",
        [Enum.EMsgReg.UI] = "<HyperLink stylename=\"Chat_AT\" u=\"UI=%s,%s,%s\">%s</>",
        [Enum.EMsgReg.EMO] = "<img id=\"%s\" width=\"42\" height=\"42\"/>",
        [Enum.EMsgReg.AT] = "<HyperLink stylename=\"Chat_AT\" u=\"at=%s\">%s</>",
        [Enum.EMsgReg.TASK] = "<HyperLink stylename=\"Chat_Task\" u=\"task=%d\">%s</>",
        [Enum.EMsgReg.GROUP_RECRUIT] = "<HyperLink stylename=\"Chat_Locate\" u=\"groupRecruit=%s\">%s</>",
        [Enum.EMsgReg.TEAM_RECRUIT] = "<HyperLink stylename=\"Chat_Locate\" u=\"teamRecruit=%s\">%s</>",
        [Enum.EMsgReg.GUILD_WELCOME] = "<HyperLink stylename=\"Chat_AT\" u=\"guildWelcome\">%s</>",
        [Enum.EMsgReg.TEAM_INVITE] = "<HyperLink stylename=\"Chat_Locate\" u=\"teamInvite=%s\">%s</>",
        [Enum.EMsgReg.TEAROOM_INVITE] = "<HyperLink stylename=\"Chat_Recruit\" u=\"teaRoomType=%s,time=%s\">%s</>",
        [Enum.EMsgReg.MOMENTS] = "<HyperLink stylename=\"Chat_Recruit\" u=\"moments=%s,%s,%s,%s,%s\">%s</>",
        [Enum.EMsgReg.MOMENTS_TOPICS] = "<HyperLink stylename=\"Chat_Currency\" u=\"topics=%s\">%s</>",
        [Enum.EMsgReg.MOMENTS_AT] = "<HyperLink stylename=\"Chat_AT\" u=\"momentsAt=%s\">%s</>",
        [Enum.EMsgReg.JOIN_TAROTTEAM] = "<HyperLink stylename=\"Chat_Recruit\" u=\"joinTarotTeam=%s,%s\">%s</>",
    }

    self.RestoreMsgReg = {
        [Enum.EMsgReg.ROLE] = "<HyperLink stylename=\"Chat_PlayerName\" u=\"role=%S*\">(%S*)</>",
        [Enum.EMsgReg.ITEM] = "<HyperLink stylename=\"%S*\" u=\"i=%S*,%S*\">(%[%S*%])</>",
        [Enum.EMsgReg.LOCATION] = "<HyperLink stylename=\"Chat_Locate\" u=\"loc=%S*\">(%S*)</>",
        [Enum.EMsgReg.UI] = "<HyperLink stylename=\"Chat_AT\" u=\"UI=%S*,%S*,%S*\">(%S*)</>",
        [Enum.EMsgReg.EMO] = "<img id=\"(%S*)\" width=\"42\" height=\"42\"/>",
        [Enum.EMsgReg.AT] = "<HyperLink stylename=\"Chat_AT\" u=\"at=%S*\">(%S*)</>",
        [Enum.EMsgReg.TASK] = "<HyperLink stylename=\"Chat_Task\" u=\"task=%S*\">(%S*)</>",
        [Enum.EMsgReg.GROUP_RECRUIT] = "<HyperLink stylename=\"Chat_Locate\" u=\"groupRecruit=(%S*)\">(%S*)</>",
        [Enum.EMsgReg.TEAM_RECRUIT] = "<HyperLink stylename=\"Chat_Locate\" u=\"teamRecruit=(%S*)\">(%S*)</>",
        [Enum.EMsgReg.GUILD_WELCOME] = "<HyperLink stylename=\"Chat_AT\" u=\"guildWelcome\">(%S*)</>",
        [Enum.EMsgReg.TEAM_INVITE] = "<HyperLink stylename=\"Chat_Locate\" u=\"teamInvite=(%S*)\">(%S*)</>",
        [Enum.EMsgReg.TEAROOM_INVITE] = "<HyperLink stylename=\"Chat_Recruit\" u=\"teaRoomType=%S*,time=%S*\">(%S*)</>",
        [Enum.EMsgReg.MOMENTS] = "<HyperLink stylename=\"Chat_Recruit\" u=\"moments=%S*\">(%S*)</>",
        [Enum.EMsgReg.MOMENTS_TOPICS] = "<HyperLink stylename=\"Chat_Currency\" u=\"topics=%S*\">(%S*)</>",
        [Enum.EMsgReg.MOMENTS_AT] = "<HyperLink stylename=\"Chat_AT\" u=\"momentsAt=%S*\">(%S*)</>",
        [Enum.EMsgReg.JOIN_TAROTTEAM] = "<HyperLink stylename=\"Chat_Recruit\" u=\"joinTarotTeam=%S*\">(%S*)</>",
    }

    self.TeamPattern = {}
    local professiondata = Game.TableData.GetPlayerSocialDisplayDataTable()
    local playerPattern = StringConst.Get("SOCIAL_TEAM_PATTERN2")
    for _, v in ksbcpairs(professiondata) do
        if v and v[0].ClassName then
            table.insert(self.TeamPattern, string.format(playerPattern, v[0].ClassName))
        end
    end
    local pL = StringConst.GetList("SOCIAL_TEAM_PATTERN")
    table.mergeList(self.TeamPattern, pL)
end

function ChatSystem:GetRegMsg(regType, ...)
    if ... ~= nil then
        return string.format(self.MsgReg[regType], ...)
    else
        return self.MsgReg[regType]
    end
end

function ChatSystem:GetRestoreMsg(regType, msg)
    return string.gsub(msg, self.RestoreMsgReg[regType], "%1")
end

function ChatSystem:TryShowBubble(actorID, chatInfo)
    if chatInfo.channelType == Enum.EChatChannelData['ANONYMITY'] then
        return
    end
    if chatInfo.channelType == Enum.EChatChannelData.CHATROOM then
        return
    end
    local text = chatInfo.messageText
    if chatInfo.messageType == Enum.EChatMessageType.IMAGE then
        text = StringConst.Get("SOCIAL_CHAT_STICKER")
    end
    if chatInfo.messageType == Enum.EChatMessageType.VOICE and string.isEmpty(chatInfo.messageText) then
        text = StringConst.Get("SOCIAL_CHAT_VOICE_IDENTIFY_FAIL")
    end
    if chatInfo.functionType == Enum.ChatFunctionType.TEAM_RECRUIT or chatInfo.functionType == Enum.ChatFunctionType.TEAM_GROUP_RECRUIT then
        for _,value in ipairs(Game.TeamSystem.model.ShoutTagName) do
            text = string.gsub(text, "%s*"..string.sub(value,2,#value),"")
        end
    end
    --text = self:GetRestoreMsg(Enum.EMsgReg.LOCATION, text)
    --text = self:GetRestoreMsg(Enum.EMsgReg.AT, text)
    Game.HeadInfoManager:SetBubbleByEntityID(actorID, text, 3000)
end

function ChatSystem:GetRawMessgaeText(chatInfo)
    local text = chatInfo.messageText and chatInfo.messageText or ""
    text = self:GetRestoreMsg(Enum.EMsgReg.EMO, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.AT, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.ITEM, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.TASK, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.LOCATION, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.UI, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.TEAROOM_INVITE, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.MOMENTS, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.MOMENTS_TOPICS, text)
    text = self:RemoveAttach(text, chatInfo.functionType)
    return text
end

function ChatSystem:GetRawMessgaeTextWithEmo(chatInfo)
    local text = chatInfo.messageText and chatInfo.messageText or ""
    text = self:GetRestoreMsg(Enum.EMsgReg.AT, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.ITEM, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.TASK, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.LOCATION, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.UI, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.TEAROOM_INVITE, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.MOMENTS, text)
    text = self:GetRestoreMsg(Enum.EMsgReg.MOMENTS_TOPICS, text)
    text = self:RemoveAttach(text, chatInfo.functionType)
    return text
end

function ChatSystem:GetSendMessage(noAtMsg)
    return self.ChatInputData:GetSendMessage(noAtMsg)
end

function ChatSystem:GenHistoryMessage(richText)
    local t = {}
    t.richText = self:GetRestoreMsg(Enum.EMsgReg.AT, richText)
    local text = ""
    local args = {}
    local itemArg = {}
    local taskArg = {}
    local locationArgs = {}
    text = string.gsub(richText, "<HyperLink stylename=\"Quality_%P\" u=\"i=%w+,%S+\">%S+</>", function(v)
        text = string.gsub(v, "<HyperLink stylename=\"Quality_(%P)\" u=\"i=(%w+),(%S+)\">(%S+)</>",
            "%1--%2--%3--%4")
        args = string.split(text, "--")
        table.insert(itemArg, string.format("%s,%s,%s,%s", args[4], args[2], args[3], args[1]))
        return args[4]
    end)
    t.itemArg = table.concat(itemArg, ";")
    text = self:GetRestoreMsg(Enum.EMsgReg.EMO, text)
    text = string.gsub(text, "<HyperLink stylename=\"Chat_Task\" u=\"task=%w+\">%S+</>", function(v)
        text = string.gsub(v, "<HyperLink stylename=\"Chat_Task\" u=\"task=(%w+)\">(%S+)</>",
            "%1--%2")
        args = string.split(text, "--")
        table.insert(taskArg, string.format("%s,%s", args[2], args[1]))
        return args[2]
    end)
    t.taskArg = table.concat(taskArg, ";")

    text = string.gsub(text, "<HyperLink stylename=\"Chat_Locate\" u=\"loc=[^%[]*\">%[[^%]]*%]</>", function(v)
        text = string.gsub(v, "<HyperLink stylename=\"Chat_Locate\" u=\"loc=([^%[]*)\">(%[[^%]]*%])</>",
            "%1--%2")
        args = string.split(text, "--")
        table.insert(locationArgs, string.format("%s---%s", args[2], args[1]))
        return args[2]
    end)
    t.locationArgs = table.concat(locationArgs, ";")
    t.text = text
    return t
end

function ChatSystem:SendItemMessage(channelType, itemid, gid)
    local itemRow = Game.TableData.GetItemNewDataRow(itemid)
    if itemRow ~= nil then
        local itemname = string.format("[%s]", itemRow.itemName)
        local text = self:GetRegMsg(Enum.EMsgReg.ITEM, itemRow["quality"], tostring(itemid), gid, itemname)
        Game.ChatSystem:SendChatMessage(channelType, text, Enum.EChatMessageType.TEXT, nil, nil, true, false)
    end
end

function ChatSystem:ProcessAtMessage(chatInfo)
    local _ = string.gsub(chatInfo.messageText, "<HyperLink stylename=\"Chat_AT\" u=\"at=(%S*)\">@%S*</>",
        function(v)
            if v == Game.me.eid and Game.ChatSystem:GetChannelShowSetting(chatInfo.channelType) then
                chatInfo.atPlayer = v
                self.model:AddAtMessage(chatInfo)
                Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_AT)
            end
        end)
end

function ChatSystem:ProcessRedPacketMessage(chatInfo)
    if chatInfo.messageType == Enum.EChatMessageType.RED_PACKET and chatInfo.senderInfo.entityId == Game.me.eid then
        if chatInfo.channelType == Enum.EChatChannelData.CHATROOM then
            Game.ChatSystem:OpenSocialPanel(true,Enum.ESocialTab.ChatRoom, chatInfo.channelType ,Enum.EChatTarget.Channel)
        else
            Game.ChatSystem:OpenSocialPanel(true,Enum.ESocialTab.ChannelChat, chatInfo.channelType ,Enum.EChatTarget.Channel)
        end
    end
end

function ChatSystem:ProcessEmoMessage(MessageText)
    local matchString = {}
    for s in string.gmatch(MessageText, "#%d%d%d") do
        if not table.contains(matchString, s) then
            table.insert(matchString, s)
        end
    end
    for i = 1, #matchString do
        if self.EmoData[matchString[i]] then
            MessageText = string.gsub(MessageText, matchString[i], self:GetRegMsg(Enum.EMsgReg.EMO, matchString[i]))
        end
    end
    return MessageText
end

function ChatSystem:ProcessMomentsEmoMessage(MessageText)
    local matchString = {}
    for s in string.gmatch(MessageText, "#%d%d%d") do
        if not string.find(MessageText, string.format(Game.TableData.GetMomentsStringConstDataRow("MOMENTS_TOPIC_TYPE").StringValue, string.sub(s, 2), string.sub(s, 2))) then
            if not table.contains(matchString, s) then
                table.insert(matchString, s)
            end
        end
    end
    for i = 1, #matchString do
        if self.EmoData[matchString[i]] then
            MessageText = string.gsub(MessageText, matchString[i], self:GetRegMsg(Enum.EMsgReg.EMO, matchString[i]))
        end
    end
    return MessageText
end

function ChatSystem:postProcessChatArgs(chatInfo)
    local matchString = {}
    local equidIds = {}
    for s,v in string.gmatch(chatInfo.messageText, "<HyperLink stylename=\"%S*\" u=\"(%S*)\">(%S*)</>") do
        table.insert(matchString, s)
        table.insert(matchString, v)
        if string.startsWith(s, "i=") then
            local itemInfo= string.split(s,",")
            if itemInfo and #itemInfo >= 2 then
                table.insert(equidIds, itemInfo[2])
            end
        end
    end
    chatInfo.messageText = string.gsub(chatInfo.messageText, "<HyperLink stylename=\"(%S*)\" u=\"(%S*)\">(%S*)</>","<HyperLink stylename=\"%1\" u=\"HERFARGS\">\"HERFARGS\"</>")
    chatInfo.chatArgs.hrefArgs = matchString
    chatInfo.chatArgs.equipIDs = equidIds
end

function ChatSystem:processMessageText(chatInfo)
    chatInfo.messageText = chatInfo.messageText or ""
    if chatInfo.chatArgs.hrefArgs and #chatInfo.chatArgs.hrefArgs > 0 then
        chatInfo.messageText  = string.gsub(chatInfo.messageText,"%%","%%%%")
        chatInfo.messageText = string.gsub(chatInfo.messageText, "<HyperLink stylename=\"(%S*)\" u=\"HERFARGS\">\"HERFARGS\"</>","<HyperLink stylename=\"%1\" u=\"%%s\">%%s</>")
        chatInfo.messageText = string.format(chatInfo.messageText,table.unpack(chatInfo.chatArgs.hrefArgs))
    end
end

function ChatSystem:OnUrlClicked(url)
    Log.Debug(url)
    if string.startsWith(url, "i=") then
        self:OnItemClicked(url)
    elseif string.startsWith(url, "role=") then
        self:OnRoleClicked(url)
    elseif string.startsWith(url, "task=") then
        self:OnTaskClicked(url)
    elseif string.startsWith(url, "UI=") then
        self:OnUIClicked(url)
    elseif string.startsWith(url, "groupRecruit=") then
        self:OnGroupRecruitClicked(url)
    elseif string.startsWith(url, "teamRecruit=") then
        self:OnTeamRecruitClicked(url)
    elseif string.startsWith(url, "loc=") then
        self:OnLocationClicked(url)
    elseif string.startsWith(url, "guildWelcome") then
        self:OnGuildWelcomeClicked(url)
    elseif string.startsWith(url, "guildTaskHelp=") then
        self:OnGuildTaskHelpClicked(url)
    elseif string.startsWith(url, "teamInvite=") then
        self:OnTeamInviteClicked(url)
    elseif string.startsWith(url, "at=") then
        self:OnAtRoleClicked(url)
    elseif string.startsWith(url, "teaRoomType=") then
        self:OnTeaRoomInviteClick(url)
	elseif string.startsWith(url, "chatJump=") then
		self:OnJumpToChatInfo(url)
	elseif string.startsWith(url, "chatDisclose=") then
		self:OnDiscloseAnonymousChatInfo(url)
    elseif string.startsWith(url, "moments=") then
        self:OnMomentsClick(url)
    elseif string.startsWith(url, "topics=") then
        self:OnTopicsClick(url)
    elseif string.startsWith(url, "momentsAt=") then
        self:OnMomentsAtClick(url)
    elseif string.startsWith(url, "rescueJump=") then
        self:OnRewardClick(url)
    elseif string.startsWith(url, "joinTarotTeam=") then
        self:OnJoinTarotTeamClick(url)
    end
end

function ChatSystem:OnRewardClick(url)
    local TargetID = Game.me.teamTargetID
    local TargetData = Game.TableData.GetTargetDataRow(TargetID)
    local DungeonData = Game.TableData.GetDungeonDataRow(TargetData.Trick)
    UI.ShowUI(UIPanelConfig.DungeonHandbook, {StageID = Game.DungeonSystem:GetDungeonStageID(), DungeonID = DungeonData.MarkId})
end

function ChatSystem:OnItemClicked(url)
    local ids
    local newstr = string.sub(url, 3)
    ids = string.split(newstr, ",")
    if ids then
        local ItemData = Game.TableData.GetItemNewDataRow(tonumber(ids[1]))
		if ItemData == nil then
			Log.ErrorFormat("OnItemClicked url is error url:%s",url)
			return
		end
        if ItemData["type"] == Game.BagSystem.ItemType.EQUIPMENT then
            local equipData = Game.EquipmentSystem:GetEquipInfoByGbId(ids[2])
            if equipData == nil then
                equipData = self.EquipData[ids[2]]
            end
            if equipData then
                local Params = {
                    SlotInfo = equipData
                }
                UI.ShowUI('BagItemTips_Panel', tonumber(ids[1]), Params)
            else
                self.sender:GetSnapShot(ids[2])
            end
        elseif ItemData["type"] == Game.BagSystem.ItemType.SEALED_ITEM then
            -- local sealedData = Game.SealedSystem:GetSealedInfoByGbId(ids[2])
            -- if sealedData then
            --     local Params = {
            --         SlotInfo = sealedData
            --     }
            --     UI.ShowUI('BagItemTips_Panel', tonumber(ids[1]), Params)
            -- else
            --     self.sender:GetSnapShot(ids[2])
            -- end
        else
            local Params = {
                IsShowBtn = false,
                Position = Game.BagSystem.TipPosType.default,
                StackCount = 0,
            }
            UI.ShowUI('BagItemTips_Panel', tonumber(ids[1]), Params)
        end
    end
end

function ChatSystem:OnRoleClicked(url)
    local eid = string.sub(url, 6)
    Game.TeamSystem:PlayerCardUIDataAsync(eid)
end

function ChatSystem:OnAtRoleClicked(url)
    local eid = string.sub(url, 4)
    Game.TeamSystem:PlayerCardUIDataAsync(eid)
end

function ChatSystem:OnGroupRecruitClicked(url)
    local groupId = tonumber(string.sub(url, 14))
    if string.isEmpty(groupId) then
        Log.ErrorFormat("OnGroupRecruitClicked groupId is error url:%s", url)
        return
    end
    local TeamID = GetMainPlayerPropertySafely("teamID")
    local bIsCaptain = GetMainPlayerPropertySafely("isCaptain") == 1
    --申请加入队伍
    if TeamID ~= 0 then
        if bIsCaptain == false then
            --队员
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEND_APPLICATION_CONFIRM, function()
                Game.me:ApplyJoinGroupByGroup(groupId)
            end)
        end
    elseif Game.me.groupID ~= 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_APPLY_FAIL_IN_GROUP)
    else
        Game.me:ApplyJoinGroupByGroup(groupId)
    end
end

function ChatSystem:OnTeamRecruitClicked(url)
    local teamStr = string.sub(url, 13)
    local teamId = tonumber(teamStr)
    if teamId then
        Game.TeamSystem:EnterApplyJoinTeamProcess(teamId, Game.me.eid, const.DEFAULT_ALL_TARGET_ID, true)
    else
        Log.ErrorFormat("OnTeamRecruitClicked teamid is error url:%s", url)
    end
end

function ChatSystem:OnTeamInviteClicked(url)
    local eid = string.sub(url, 12)
    if eid ~= Game.me.eid then
        if Game.TeamSystem:IsInTeam() then
                Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEND_APPLICATION_CONFIRM,
                    function()
                        Game.me:ReqPreJoinTeam(eid)
                    end
                )
        elseif Game.TeamSystem:IsInGroup() then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEND_APPLICATION_CONFIRM_TEAM, function()
                    Game.me:ReqPreJoinTeam(eid)
                end)
        else
            Game.me:ReqPreJoinTeam(eid)
        end
    end
end

function ChatSystem:OnTaskClicked(url)
    local id = tonumber(string.sub(url, 6))
    Game.NewUIManager:OpenPanel(UIPanelConfig.ChatTips_Panel, id)
end

function ChatSystem:OnUIClicked(url)
    local str = string.sub(url, 4)
    local args = string.split(str, ",")
    if args and #args > 0 then
        local uiName = args[1]
        if uiName == UIPanelConfig.DungeonHandbook then
            UI.ShowUI(uiName, { StageID = tonumber(args[3]), DungeonID = tonumber(args[2]) })
        end
    end
end

function ChatSystem:OnGuildTaskHelpClicked(url)
    local str = string.sub(url, 15)
    local args = string.split(str, ",")
    if args and #args==6 then
        local name = args[1]
        local itemNum = tonumber(args[2])
        local itemId = tonumber(args[3])
        local itemName = Game.TableData.GetItemNewDataRow(itemId).itemName
        self.guildTaskHelp = {}
        self.guildTaskHelp.arg = args[4]
        self.guildTaskHelp.senderId = args[5]
        self.guildTaskHelp.opNUID = args[6]
        self.guildTaskHelp.itemId = itemId
        local bagItemCount = Game.BagSystem:GetItemCount(itemId)
        if bagItemCount < itemNum then
            self.guildTaskHelp.bEnough = false
        else
            self.guildTaskHelp.bEnough = true
        end
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GUILD_MATERIAL_TASK, function()
            Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_GUILD_MATERIAL_TASK_HELP_IN_SAME_GUILD, "OnCheckInSameGuild", self)
            local helpInfo = Game.GuildMaterialTaskSystem.model.GuildMaterialHelpInfo
            local bHelped = false
            for k, v in pairs(helpInfo) do
                if k == self.guildTaskHelp.arg and v.helpPlayerID ~= "" then
                    bHelped = true
                end
            end
            if bHelped then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.REMATERIAL_ALREADY_HELP)
            else
                Game.GuildSystem.sender:reqCheckInSameGuild(self.guildTaskHelp.senderId, Game.me.eid, self.guildTaskHelp.opNUID)
            end
        end, function()
            self.guildTaskHelp = nil
            Game.GlobalEventSystem:RemoveListener(EEventTypesV2.ON_GUILD_MATERIAL_TASK_HELP_IN_SAME_GUILD, "OnCheckInSameGuild", self)
        end, {name, itemNum, itemName})
    end
end

function ChatSystem:OnTeaRoomInviteClick(url)
    local strRoomType, strRoomID, strTime = string.match(url, "teaRoomType=(%d+),teaRoomInvite=(%d+),time=(%d+)")
    local nRoomType = tonumber(strRoomType)
    local nRoomID = tonumber(strRoomID)
    local nTime = tonumber(strTime)
    local nCurTime = _G._now(1)
    if nRoomID and nRoomID ~= 0 then
        if nCurTime - nTime <= Enum.EChatRoomConstData.ChatRoomHyperlinkCD then
            if Game.TeaRoomSystem:IsCurRoomID(nRoomID) then
                Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_OPEN_SOCIAL_PAGE, Enum.ESocialTab.ChatRoom)
            else
                Game.TeaRoomSystem:ReqEnterTeaRoom(nRoomType, nRoomID)
            end
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_LINK_INVALID)
        end
    end
end

function ChatSystem:OnMomentsClick(url)
    local str = string.sub(url, 9)
    local args = string.split(str, ",")
    if args and #args > 0 then
        Game.MomentsSystem:ReqGetMomentInfo(args[1], args[2], args[3], args[4], args[5])
    end
end

function ChatSystem:OnTopicsClick(url)
    local str = string.sub(url, 8)
    if str then
        Game.NewUIManager:OpenPanel("FriendGroupTopicInfo_Panel", {topicName = str})
    end
end

function ChatSystem:OnMomentsAtClick(url)
    local str = string.sub(url, 11)
    if str then
        Game.TeamSystem:PlayerCardUIDataAsync(str, false, Enum.EFriendAddSourceData.CHAT, nil, Enum.EMenuType.Chat)
    end
end

function ChatSystem:OnJoinTarotTeamClick(url)
    local str = string.sub(url, 15)
    local params = string.split(str, ",")
    if str then
        if not Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_TAROTTEAM, false) then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_MODULED_LOCKED)
            return 
        end
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.TAROTTEAM_APPLY_LINK_CONFIRM, function()
            Game.TarotTeamSystem:ApplyJoin(tonumber(params[1]))
        end, nil, {params[2]})
    end
end

function ChatSystem:OnJumpToChatInfo(Url)
	local chatid = string.sub(Url, 10)
	Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_JUMP,chatid)
end

function ChatSystem:OnDiscloseAnonymousChatInfo(Url)
	local newstr = string.sub(Url, 14)
	local ids = string.split(newstr, ",")
	if ids[2] == Game.me.eid then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_CAN_NOT_REVEAL_SELF)
		return
	end
	if Game.ChatSystem:CheckPlayerInBugList(ids[2]) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_ANOY_INVINCIBLE)
	else
        Game.NewUIManager:OpenPanel(UIPanelConfig.ChatDisclosePopup_Panel, ids[1])
	end
end

function ChatSystem:OnCheckInSameGuild(result, chatID)
    Game.GlobalEventSystem:RemoveListener(EEventTypesV2.ON_GUILD_MATERIAL_TASK_HELP_IN_SAME_GUILD, "OnCheckInSameGuild", self)
    if self.guildTaskHelp and self.guildTaskHelp.opNUID == chatID then
        if result then
            if self.guildTaskHelp.bEnough then
                Game.GuildMaterialTaskSystem.sender:ReqHelpGuildMaterialTask(self.guildTaskHelp.arg)
            else
                UI.ShowUI('BagItemTips_Panel', self.guildTaskHelp.itemId, {IsShowForwardPanel = true})
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.REMATERIAL_NO_ENOUGH_ITEM)
            end
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.REMATERIAL_NOT_SAME_GUILD)
        end
    end
end

function ChatSystem:RemoveAttach(msgText, chatfunctionType)
    if chatfunctionType ~= Enum.ChatFunctionType.TEAM_RECRUIT and chatfunctionType ~= Enum.ChatFunctionType.TEAM_GROUP_RECRUIT then
        msgText = string.gsub(msgText, self.RestoreMsgReg[Enum.EMsgReg.TEAM_RECRUIT], "")
        msgText = string.gsub(msgText, self.RestoreMsgReg[Enum.EMsgReg.GROUP_RECRUIT], "")
        msgText = string.gsub(msgText, self.RestoreMsgReg[Enum.EMsgReg.TEAM_INVITE], "")
    end
    return msgText
end

function ChatSystem:AttachInfo(msgText, chatfunctionType,targetID)
    if chatfunctionType ~= Enum.ChatFunctionType.TEAM_RECRUIT and chatfunctionType ~= Enum.ChatFunctionType.TEAM_GROUP_RECRUIT 
    and targetID ~= Enum.EChatChannelData["TEAM"] and targetID ~= Enum.EChatChannelData["GROUP"] then
        local isMatch = false
        for _, v in pairs(self.TeamPattern) do
            if string.find(msgText, v) then
                isMatch = true
                break
            end
        end
        if isMatch then
            local numStr = ""
            if Game.me.teamID ~= 0 then
                numStr = string.format(StringConst.Get("SOCIAL_CHAT_TEAM_NUM"),Game.TeamSystem:GetTeamPlayerNum())
                msgText = msgText .. self:GetRegMsg(Enum.EMsgReg.TEAM_RECRUIT, Game.me.teamID, numStr..StringConst.Get("SOCIAL_CHAT_TEAM_APPLY_CLICK"))
            elseif Game.me.groupID ~= 0 then
                numStr = string.format(StringConst.Get("SOCIAL_CHAT_GROUP_NUM"),Game.GroupSystem:GetMyGroupMemberNum())
                msgText = msgText .. self:GetRegMsg(Enum.EMsgReg.GROUP_RECRUIT, Game.me.groupID, numStr..StringConst.Get("SOCIAL_CHAT_GROUP_APPLY_CLICK"))
            else
                numStr = string.format(StringConst.Get("SOCIAL_CHAT_TEAM_NUM"),0)
                msgText = msgText .. self:GetRegMsg(Enum.EMsgReg.TEAM_INVITE, Game.me.eid, numStr..StringConst.Get("SOCIAL_CHAT_TEAM_INVITE"))
            end
        end
    end
    return msgText
end

function ChatSystem:OnLocationClicked(url)
    local str = string.sub(url, 5)
    local args = string.split(str, ",")
    if args and #args == 6 then
        Game.TraceSystem:AddChatTrace(tonumber(args[1]), tonumber(args[2]), tonumber(args[3]), tonumber(args[5]),
            tonumber(args[6]))
        Log.Debug(string.format("x=%f  y=%f z = %f i=%d mapId =%d planeID = %d",tonumber(args[1]),tonumber(args[2]),tonumber(args[3]),tonumber(args[4]),tonumber(args[5]),tonumber(args[6])))
        if Game.ChatSystem:GetChatSetting(Enum.EChatSetting["PathFindTips"]) then
            if Game.ChatSystem:GetChatSetting(Enum.EChatSetting["PathFinding"]) then
                self:ChatNavigate(tonumber(args[5]),tonumber(args[1]),tonumber(args[2]),tonumber(args[3]))
            end
        else
            Game.ChatSystem:SetChatSetting(Enum.EChatSetting["PathFindTips"],true)
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.LOCATION_TRACE, function()
                Game.ChatSystem:SetChatSetting(Enum.EChatSetting["PathFinding"],true)
                self:ChatNavigate(tonumber(args[5]),tonumber(args[1]),tonumber(args[2]),tonumber(args[3]))
            end,function()
                Game.ChatSystem:SetChatSetting(Enum.EChatSetting["PathFinding"],false)
            end)
        end
    end
end

function ChatSystem:ChatNavigate(mapID,x,y,z)
    local LevelMapData = Game.TableData.GetLevelMapDataRow(mapID)
    if LevelMapData.Type == 1 then
        Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.ClickMap,mapID, 
            { ["X"] = x, ["Y"] = y, ["Z"] = z})
    else
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_PATHFINDING_FALSE)
    end    
end

function ChatSystem:OnGuildWelcomeClicked(url)
    Game.GuildSystem:SendWelcome()
end

function ChatSystem:GetTeamRecruitText(chatInfo)
    local teamInfo = chatInfo.chatArgs.teamRecruitInfo
    local targetName = ""
    local messgaeText = ""
    if teamInfo.details.targetID == 0 then
        targetName = StringConst.Get("TEAM_ALLTARGET")
    else
        local TargetTableData = Game.TableData.GetTargetDataRow(teamInfo.details.targetID)
        targetName = TargetTableData.Name
    end
    local membernum = 0
    for _,_ in pairs(teamInfo.memberInfos) do
        membernum = membernum + 1
    end
    local menberMaxNum = Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT")
    local needStr = StringConst.GetList("TEAM_RECRUIT_MSG")
    local spiltStr = StringConst.Get("TEAM_RECRUIT_MSG")
    if teamInfo.details.positionNeed and #teamInfo.details.positionNeed >= 1 then
        local positionMsgList = {}
        for _,v in pairs(teamInfo.details.positionNeed) do
            table.insert(positionMsgList,needStr[v+1])
        end
        messgaeText = table.concat(positionMsgList,spiltStr)
    end
    -- 招募标签调整
    local rawMessageText = chatInfo.messageText
    local addMessageText = ""
    for _,value in ipairs(Game.TeamSystem.model.ShoutTagName) do
        local findIndex
        rawMessageText, findIndex = string.gsub(rawMessageText, "%s*"..string.sub(value,2,#value),"")
        if findIndex and findIndex > 0 then
            addMessageText = addMessageText .. string.match(value, "<.->(.-)</>") .. " "
        end
    end
    local finishText = addMessageText .. "<Chat_Default>"..rawMessageText.."</>"
    messgaeText = messgaeText .. finishText
    local zlText = ""
    if teamInfo.details.zhanliNeed ~= 0 then
        zlText = StringConst.Get("SOCIAL_CHAT_TEAM_APPLY_CE")
    end
    return string.format(StringConst.Get("SOCIAL_CHAT_TEAM_APPLY"),targetName,membernum,menberMaxNum,messgaeText,zlText,teamInfo.teamUid)
end

function ChatSystem:GetGroupRecruitText(chatInfo)
    local teamInfo = chatInfo.chatArgs.groupRecruitInfo
    local targetName = ""
    local messgaeText = ""
    if teamInfo.details.targetID == 0 then
        targetName = StringConst.Get("TEAM_ALLTARGET")
    else
        local TargetTableData = Game.TableData.GetTargetDataRow(teamInfo.details.targetID)
        targetName = TargetTableData.Name
    end
    local membernum = 0
    for _,v in pairs(teamInfo.memberInfos) do
        membernum = membernum + v
    end
    local needStr = StringConst.GetList("TEAM_RECRUIT_MSG")
    local spiltStr = StringConst.Get("TEAM_RECRUIT_MSG")
    if teamInfo.details.positionNeed and #teamInfo.details.positionNeed >= 1 then
        local positionMsgList = {}
        for _,v in pairs(teamInfo.details.positionNeed) do
            table.insert(positionMsgList,needStr[v+1])
        end
        messgaeText = table.concat(positionMsgList,spiltStr)
    end
    -- 招募标签调整
    local rawMessageText = chatInfo.messageText
    local addMessageText = ""
    for _,value in ipairs(Game.TeamSystem.model.ShoutTagName) do
        local findIndex
        rawMessageText, findIndex = string.gsub(rawMessageText, "%s*"..string.sub(value,2,#value),"")
        if findIndex and findIndex > 0 then
            addMessageText = addMessageText .. string.match(value, "<.->(.-)</>") .. " "
        end
    end
    local finishText = addMessageText .. "<Chat_Default>"..rawMessageText.."</>"
    messgaeText = messgaeText .. finishText
    local menberMaxNum = const.GROUP_TEAM_COUNT * const.TEAM_MEMBER_MAX_NUMBER
    local zlText = ""
    if teamInfo.details.zhanliNeed ~= 0 then
        zlText = StringConst.Get("SOCIAL_CHAT_GROUP_APPLY_CE")
    end
    return string.format(StringConst.Get("SOCIAL_CHAT_GROUP_APPLY"),targetName,membernum,menberMaxNum,messgaeText,zlText,teamInfo.groupID)
end

function ChatSystem:GetGuildTaskHelpText(chatInfo)
    -- local itemId = chatInfo.chatArgs.guildtaskmaterialtask.itemid
    -- local itemNum = chatInfo.chatArgs.guildtaskmaterialtask.itemnum
    -- local ItemExcelData = Game.TableData.GetItemNewDataRow(itemId)
    -- local itemName = ItemExcelData.itemName
    -- local name = chatInfo.senderInfo.rolename
    -- local messageText = string.format("<HyperLink stylename=\"Chat_Task\" u=\"guildTaskHelp=%s,%d,%s,%s,%s,%s\">点击帮助</>", name, itemNum, itemId, 
    --         chatInfo.chatArgs.guildtaskmaterialtask.arg, chatInfo.senderInfo.id, chatInfo.opNUID)
    -- return string.format("%s %d %s", itemName, itemNum, messageText)
end

function ChatSystem:GetTextRoomInviteText(nRoomID, nTime, chatRoomType, roomName)
    roomName = roomName or (Game.TeaRoomSystem.model.roomInfoData and Game.TeaRoomSystem.model.roomInfoData.strRoomName or "")
    local typeName = Game.TableData.GetChatRoomTypeDataRow(Game.TeaRoomSystem.model.roomInfoData.nRoomType) and 
        Game.TableData.GetChatRoomTypeDataRow(Game.TeaRoomSystem.model.roomInfoData.nRoomType).TypeName or ""
    local chatRoomTypeName = Game.TeaRoomSystem:GetChatRoomTypeNameByChatRoomType(chatRoomType)
    local strText = string.format(Game.TableData.GetChatRoomStringConstDataRow("SOCIAL_CHATROOM_SHARE").StringValue, tostring(chatRoomType), tostring(nRoomID), tostring(nTime), typeName, chatRoomTypeName, roomName)
    return strText
end