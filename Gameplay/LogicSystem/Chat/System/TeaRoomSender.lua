local TeaRoomSender = DefineClass("TeaRoomSender",SystemSenderBase)

function TeaRoomSender:ReqEnterQuickly()
    self.Bridge:ReqEnterQuickly()
end

---@overload ReqSearchTeaRoom rpc搜索房间列表
--@param strSearchText string 搜索string
--@param bLocalServer bool true:仅搜索本服  false:搜索全服
--@param bChooseTag Table bool表示每个Tag是否被选上
function TeaRoomSender:ReqSearchTeaRoom(strSearchText, bLocalServer, bChooseTag)
    self.Bridge:ReqSearchTeaRoom(strSearchText, bLocalServer, bChooseTag)
end

---@overload ReqEnterTeaRoom 进入房间
--@param nRoomID int 房间ID
function TeaRoomSender:ReqEnterTeaRoom(nRoomID)
    self.Bridge:ReqEnterTeaRoom(nRoomID)
end

function TeaRoomSender:ReqLeaveAndEnterRoom(nLastRoomID, nTargetRoomID, isParty)
    self.Bridge:ReqLeaveAndEnterRoom(nLastRoomID, nTargetRoomID, isParty)
end

function TeaRoomSender:ReqCreateTeaRoom(roomName, roomType, roomTag, isShareToWorldChannel, isShareToMements)
    self.Bridge:ReqCreateTeaRoom(roomName, roomType, roomTag, isShareToWorldChannel, isShareToMements)
end

function TeaRoomSender:ReqLeaveTeaRoom(nRoomID)
    self.Bridge:ReqLeaveTeaRoom(nRoomID)
end

function TeaRoomSender:ReqTeaRoomMessageTop(nRoomID, strTopMsg, strTopMsgAuthor, strTopMessageID)
    self.Bridge:ReqTeaRoomMessageTop(nRoomID, strTopMsg, strTopMsgAuthor, strTopMessageID)
end

function TeaRoomSender:ReqTeaRoomPullMemberList(nRoomID)
    self.Bridge:ReqTeaRoomPullMemberList(nRoomID)
end

function TeaRoomSender:ReqTeaRoomGetMemberInfoByShortUid(nRoomID, reqUidList)
    self.Bridge:ReqTeaRoomGetMemberInfoByShortUid(nRoomID, reqUidList)
end

function TeaRoomSender:ReqTeaRoomCancelMessageTop(nRoomID)
    self.Bridge:ReqTeaRoomCancelMessageTop(nRoomID)
end

function TeaRoomSender:ReqModifyTeaRoomPrivilege(nRoomID, nMicPermissionType)
    self.Bridge:ReqModifyTeaRoomPrivilege(nRoomID, nMicPermissionType)
end

function TeaRoomSender:ReqTeaRoomPullBlacklist(nRoomID, strAvatarID)
    self.Bridge:ReqTeaRoomPullBlacklist(nRoomID, strAvatarID)
end

function TeaRoomSender:ReqTeaRoomCancelBlacklist(nRoomID, strAvatarID)
    self.Bridge:ReqTeaRoomCancelBlacklist(nRoomID, strAvatarID)
end

function TeaRoomSender:ReqSetTeaRoomSpeechFree(nRoomID)
    self.Bridge:ReqSetTeaRoomSpeechFree(nRoomID)
end

function TeaRoomSender:ReqSetTeaRoomMemberPrivilege(nRoomID, tblMemberChange)
    self.Bridge:ReqSetTeaRoomMemberPrivilege(nRoomID, tblMemberChange)
end

function TeaRoomSender:ReqSetTeaRoomMemberOrder(nRoomID, tblMemberChange)
    self.Bridge:ReqSetTeaRoomMemberOrder(nRoomID, tblMemberChange)
end

function TeaRoomSender:ReqTeaRoomGetApplicationList(nRoomID)
    self.Bridge:ReqTeaRoomGetApplicationList(nRoomID)
end

function TeaRoomSender:ReqTeaRoomGetBlackList(nRoomID)
    self.Bridge:ReqTeaRoomGetBlackList(nRoomID)
end

function TeaRoomSender:ReqTeaRoomCancelGaglist(nRoomID, strAvatarID)
    self.Bridge:ReqTeaRoomCancelGaglist(nRoomID, strAvatarID)
end

function TeaRoomSender:ReqTeaRoomPullGaglist(nRoomID, strAvatarID)
    self.Bridge:ReqTeaRoomPullGaglist(nRoomID, strAvatarID)
end

function TeaRoomSender:ReqTeaRoomApplyOpenMic(nRoomID, strApplyText)
    self.Bridge:ReqTeaRoomApplyOpenMic(nRoomID, strApplyText)
end

function TeaRoomSender:ReqTeaRoomCleanApplicationList(nRoomID, acceptTable, rejectTable)
    self.Bridge:ReqTeaRoomCleanApplicationList(nRoomID, acceptTable, rejectTable)
end

function TeaRoomSender:ReqTeaRoomGetMemberInfo(nRoomID, tblAvatarIDList)
    self.Bridge:ReqTeaRoomGetMemberInfo(nRoomID, tblAvatarIDList)
end

function TeaRoomSender:ReqModifyTeaRoomName(nRoomID, strRoomName)
    self.Bridge:ReqModifyTeaRoomName(nRoomID, strRoomName)
end

function TeaRoomSender:ReqModifyTeaRoomType(nRoomID, strRoomName)
    self.Bridge:ReqModifyTeaRoomType(nRoomID, strRoomName)
end

function TeaRoomSender:ReqTransferTeaRoomMaster(nRoomID, strRoomMasterID)
    self.Bridge:ReqTransferTeaRoomMaster(nRoomID, strRoomMasterID)
end

function TeaRoomSender:ReqTeaRoomGiveGift(nRoomID, strAvatarID, nGiftCount, nGiftID)
    self.Bridge:ReqTeaRoomGiveGift(nRoomID, strAvatarID, nGiftCount, nGiftID)
end

function TeaRoomSender:ReqTeaRoomMemberModifySpeechState(nRoomID, nEnumVoiceState)
    self.Bridge:ReqTeaRoomMemberModifySpeechState(nRoomID, nEnumVoiceState)
end

function TeaRoomSender:ReqTeaRoomMemberCloseMic(nRoomID)
    self.Bridge:ReqTeaRoomMemberCloseMic(nRoomID)
end

function TeaRoomSender:ReqFavoriteTeaRoom()
    self.Bridge:ReqFavoriteTeaRoom()
end

function TeaRoomSender:ReqDeleteFavorites(normalRoomFavorites, partyFavorites)
    self.Bridge:ReqDeleteFavorites(normalRoomFavorites, partyFavorites)
end

function TeaRoomSender:ReqCancelTeaRoomFavorite()
    self.Bridge:ReqCancelTeaRoomFavorite()
end

function TeaRoomSender:ReqGetChatRoomFavorites()
    self.Bridge:ReqGetChatRoomFavorites()
end


--region 派对房间
function TeaRoomSender:ReqCreateParty(roomName, roomType, roomTag, isShareToWorldChannel, isShareToMements)
    self.Bridge:ReqCreateParty(roomName, roomType, roomTag, isShareToWorldChannel, isShareToMements)
end

function TeaRoomSender:ReqEnterParty(roomID)
    self.Bridge:ReqEnterParty(roomID)
end

function TeaRoomSender:ReqEnterPartyQuickly()
    self.Bridge:ReqEnterPartyQuickly()
end

function TeaRoomSender:ReqLeaveParty(roomID)
    self.Bridge:ReqLeaveParty(roomID)
end

function TeaRoomSender:ReqSearchParty(pattern, onlyOneServer, filterList)
    self.Bridge:ReqSearchParty(pattern, onlyOneServer, filterList)
end

function TeaRoomSender:ReqGetPersistentPartyDieTime()
    self.Bridge:ReqGetPersistentPartyDieTime()
end

function TeaRoomSender:ReqDismantlePersistentParty(roomID)
    self.Bridge:ReqDismantlePersistentParty(roomID)
end

function TeaRoomSender:ReqFavoriteParty(roomID)
    self.Bridge:ReqFavoriteParty(roomID)
end

function TeaRoomSender:ReqCancelPartyFavorite(roomID)
    self.Bridge:ReqCancelPartyFavorite(roomID)
end

function TeaRoomSender:ReqUpdatePersisentParty(roomID)
    self.Bridge:ReqUpdatePersisentParty(roomID)
end

function TeaRoomSender:ReqModifyPartyName(roomID, roomName)
    self.Bridge:ReqModifyPartyName(roomID, roomName)
end

function TeaRoomSender:ReqPartyPullBlacklist(roomID, entityID)
    self.Bridge:ReqPartyPullBlacklist(roomID, entityID)
end

function TeaRoomSender:ReqPartyCancelBlacklist(roomID, entityID)
    self.Bridge:ReqPartyCancelBlacklist(roomID, entityID)
end

function TeaRoomSender:ReqGetPersistentPartyInfo()
    self.Bridge:ReqGetPersistentPartyInfo()
end

function TeaRoomSender:ReqPartyMessageTop(roomID, messageText, messageTextAuthor, strTopMessageID)
    self.Bridge:ReqPartyMessageTop(roomID, messageText, messageTextAuthor, strTopMessageID)
end

function TeaRoomSender:ReqPartyCancelMessageTop(roomID)
    self.Bridge:ReqPartyCancelMessageTop(roomID)
end

function TeaRoomSender:ReqPartyGiveGift(roomID, recvRoleName, giftCnt, giftID)
    self.Bridge:ReqPartyGiveGift(roomID, recvRoleName, giftCnt, giftID)
end

function TeaRoomSender:ReqSetPartySpeechFree(nRoomID)
    self.Bridge:ReqSetPartySpeechFree(nRoomID)
end

function TeaRoomSender:ReqSetPartyMemberPrivilege(roomID, privilegeList)
    self.Bridge:ReqSetPartyMemberPrivilege(roomID, privilegeList)
end

function TeaRoomSender:ReqSetPartyMemberOrder(roomID, orderList)
    self.Bridge:ReqSetPartyMemberOrder(roomID, orderList)
end

function TeaRoomSender:ReqPartyPullGaglist(roomID, entityID)
    self.Bridge:ReqPartyPullGaglist(roomID, entityID)
end

function TeaRoomSender:ReqPartyCancelGaglist(roomID, entityID)
    self.Bridge:ReqPartyCancelGaglist(roomID, entityID)
end

function TeaRoomSender:ReqPartyApplyOpenMic(roomID, text)
    self.Bridge:ReqPartyApplyOpenMic(roomID, text)
end

function TeaRoomSender:ReqPartyMemberCloseMic(nRoomID)
    self.Bridge:ReqPartyMemberCloseMic(nRoomID)
end

function TeaRoomSender:ReqPartyMemberModifySpeechState(nRoomID, nEnumVoiceState)
    self.Bridge:ReqPartyMemberModifySpeechState(nRoomID, nEnumVoiceState)
end

function TeaRoomSender:ReqPartyModifyIdentityName(nRoomID, identityID, name)
    self.Bridge:ReqPartyModifyIdentityName(nRoomID, identityID, name)
end

function TeaRoomSender:ReqPartyAssignIdentity2Avatar(nRoomID, identityID, avatarList)
    self.Bridge:ReqPartyAssignIdentity2Avatar(nRoomID, identityID, avatarList)
end

function TeaRoomSender:ReqMasterFindMemberName(nRoomID, patternStr)
    self.Bridge:ReqMasterFindMemberName(nRoomID, patternStr)
end

function TeaRoomSender:ReqRemoveIdentification(nRoomID, identityID)
    self.Bridge:ReqRemoveIdentification(nRoomID, identityID)
end

function TeaRoomSender:ReqPartyModifyIdentityState(nRoomID, identityID, identityState)
    self.Bridge:ReqPartyModifyIdentityState(nRoomID, identityID, identityState)
end

function TeaRoomSender:ReqPartyGetMemberInfo(nRoomID, tblAvatarIDList)
    self.Bridge:ReqPartyGetMemberInfo(nRoomID, tblAvatarIDList)
end

function TeaRoomSender:ReqPartyGetMemberInfoByShortUid(nRoomID, tblShortUidList)
    self.Bridge:ReqPartyGetMemberInfoByShortUid(nRoomID, tblShortUidList)
end

function TeaRoomSender:ReqTransferPartyMaster(nRoomID, strRoomMasterID)
    self.Bridge:ReqTransferPartyMaster(nRoomID, strRoomMasterID)
end
--endregion

return TeaRoomSender