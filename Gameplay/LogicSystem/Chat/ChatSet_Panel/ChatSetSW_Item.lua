local UIComSwitchBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSwitchBtn")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatSetSW_Item : UIListItem
---@field view ChatSetSW_ItemBlueprint
local ChatSetSW_Item = DefineClass("ChatSetSW_Item", UIListItem)

ChatSetSW_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatSetSW_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatSetSW_Item:InitUIData()
    self.data = nil
end

--- UI组件初始化，此处为自动生成
function ChatSetSW_Item:InitUIComponent()
    ---@type UIComSwitchBtn
    self.SetSwitchCom = self:CreateComponent(self.view.SetSwitch, UIComSwitchBtn)
end

---UI事件在这里注册，此处为自动生成
function ChatSetSW_Item:InitUIEvent()
    self:AddUIEvent(self.SetSwitchCom.onClickEvent, "on_SetSwitchCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatSetSW_Item:InitUIView()
end

---面板打开的时候触发
function ChatSetSW_Item:OnRefresh(data)
    self.data = data
    self.view.TB_Word:SetText(data.SwitcherName)
    self.SetSwitchCom:Refresh(Game.ChatSystem:GetChatSetting(data.SwitcherEnum), false, data.LeftText, data.RightText)
end

--- 此处为自动生成
---@param isOn bool
function ChatSetSW_Item:on_SetSwitchCom_ClickEvent(isOn)
    Game.ChatSystem:SetChatSetting(self.data.SwitcherEnum, isOn)
end

return ChatSetSW_Item
