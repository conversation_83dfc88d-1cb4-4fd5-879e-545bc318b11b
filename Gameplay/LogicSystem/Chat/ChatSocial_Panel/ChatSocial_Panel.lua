local UIComTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComTabList")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local UIComMaskAdaptive = kg_require("Framework.KGFramework.KGUI.Component.BackGround.UIComMaskAdaptive")
---@class ChatSocial_Panel : UIPanel
---@field view ChatSocial_PanelBlueprint
local ChatSocial_Panel = DefineClass("ChatSocial_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")

ChatSocial_Panel.eventBindMap = {
    [EEventTypesV2.MODULE_LOCK_CHANGE] = "CheckUnlockState",
    [EEventTypesV2.CHAT_CHANGE_TEA_ROOM_MODULE_STATE] = "OnChangeTeaRoomModuleState",
    [EEventTypesV2.CHAT_OPEN_SOCIAL_PAGE] = "UpdateTabList"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatSocial_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatSocial_Panel:InitUIData()
    self:UpdateTabData()
    ---@type number 当前打开页签index
    self.OpeningTabIndex = 1
    --@type boolean 是否解锁好友
    self.BeUnlockFriend = false

    self.targetID = nil
    self.targetType = nil
    -- 外部打开社交面板携带参数
    self.openParams = nil
end

--- UI组件初始化，此处为自动生成
function ChatSocial_Panel:InitUIComponent()
    ---@type UIComTabList
    self.SB_SecondTabListCom = self:CreateComponent(self.view.SB_SecondTabList, UIComTabList)
    self.WBP_ComMaskL_Adaptive = self:CreateComponent(self.view.WBP_ComMaskL_Adaptive, UIComMaskAdaptive)
end

---UI事件在这里注册，此处为自动生成
function ChatSocial_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.WBP_ComMaskL_Adaptive.Btn_Close.Big_Button_ClickArea_lua.OnClicked, "on_WBP_ComMaskL_AdaptiveBtn_CloseBig_Button_ClickArea_lua_Clicked")
    self:AddUIEvent(self.SB_SecondTabListCom.onItemSelected, "on_SB_SecondTabListCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatSocial_Panel:InitUIView()
    Game.RedDotSystem:AttachWidget(self, self.view.Btn_ClickArea, "ChatSetBtn")
end

---面板打开的时候触发
function ChatSocial_Panel:OnRefresh(socialEnum, targetID, targetType, ...)
    self.targetID = targetID
    self.targetType = targetType
    self.openParams = {...}
    self.view.WBP_ComMaskL_Adaptive.Btn_Close:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    -- self.WBP_ComMaskL_Adaptive:SetWidth(1190)
    Game.HUDSystem:PlayerLeftTopAni(false)
    self.BeUnlockFriend = true
    self:CheckUnlockState()
    if UI.IsShow("P_PartnerClubDetails") then
        UI.HideUI("P_PartnerClubDetails")
    end
    self:UpdateUI(socialEnum)
end

function ChatSocial_Panel:UpdateTabData()
    ---@type table tab列表数据
    self.TabData = {
        [Enum.ESocialTab.ChannelChat] = {
            iconPath = UIAssetPath.UI_Com_Tab_Channel03_Sprite_Copy,
            BoundWidget = self.view.ChatPanel,
            uiType = Enum.ComTabUIType.Image,
            redPointId = "ChannelChat"
        },
        [Enum.ESocialTab.Friend] = {
            iconPath = UIAssetPath.UI_Com_Tab_Friend03_Sprite_Copy,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.FriendPanel,
        },
        [Enum.ESocialTab.PrivateChat] = {
            iconPath = UIAssetPath.UI_Com_Tab_Chat03_Sprite_Copy,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.PrivateChat,
            redPointId = "PrivateChat"
        },
        [Enum.ESocialTab.ChatRoom] = {
            iconPath = UIAssetPath.UI_Com_Img_BuddhistSystem01_Sprite_Copy,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.ChatRoomPanel,
            redPointId = "SocialChatRoom"
        },
        [Enum.ESocialTab.Mail] = {
            iconPath = UIAssetPath.UI_Com_Tab_Mail03_Sprite_Copy,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.MailPanel,
            redPointId = "SocialMail"
        },
        [Enum.ESocialTab.Moments] = {
            iconPath = UIAssetPath.UI_Com_Tab_Channel03_Sprite_Copy,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.ChatRoomPanel,
        },
    }
end

function ChatSocial_Panel:UpdateUI(socialEnum)
    socialEnum = socialEnum or Enum.ESocialTab.ChannelChat
    self:UpdateTabData()
    self:UpdateTabList(socialEnum)
end

function ChatSocial_Panel:OnChangeTeaRoomModuleState()
    self:UpdateTabData()
    self:UpdateTabList()
    self:RefreshTabListCell(Enum.ESocialTab.ChatRoom)
end

function ChatSocial_Panel:UpdateTabList(socialEnum)
    local showTab = {}
    local curSelectedIndex = socialEnum or self.OpeningTabIndex
    for index, tab in ipairs(self.TabData) do
        if index == Enum.ESocialTab.Friend then
            if self.BeUnlockFriend then
                table.insert(showTab, tab)
            end
        elseif index == Enum.ESocialTab.ChatRoom then
            if Game.TeaRoomSystem:IsTeaRoomOpen() then
                table.insert(showTab, tab)
            end
        elseif index == Enum.ESocialTab.Mail then
            if Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_MAIL, false) then
                table.insert(showTab, tab)
            end
        else
            table.insert(showTab, tab)
        end
        -- 真实选中页签索引数据
        if index == socialEnum then
            curSelectedIndex = #showTab
        end
    end
    self.SB_SecondTabListCom:Refresh(showTab)
    self.SB_SecondTabListCom:SetSelectedItemByIndex(curSelectedIndex, true)
end

function ChatSocial_Panel:RefreshTabListCell(nIndex)
    self.SB_SecondTabListCom:RefreshItemByIndex(nIndex)
end

function ChatSocial_Panel:CheckUnlockState()
    local friendunlock = Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_FRIEND,false)
    if self.BeUnlockFriend ~= friendunlock then
        self.BeUnlockFriend = friendunlock
        self:UpdateTabList()
    end
end

function ChatSocial_Panel:GetOpeningTabIndex()
    return self.OpeningTabIndex
end

function ChatSocial_Panel:OnHide()
    Game.HUDSystem:PlayerLeftTopAni(true)
end

function ChatSocial_Panel:UpdateChatRoomRoomList(bShow)
    if bShow then
        Game.TeaRoomSystem.model.socialData.bShowRoomList = not Game.TeaRoomSystem:IsInTeaRoom()
        if Game.TeaRoomSystem.model.socialData.bIsBackToRoomList or Game.TeaRoomSystem.model.socialData.bShowRoomList then
            self:CloseComponent(UICellConfig.ChatRoom_RoomChat_Page, true)
            self:OpenComponent(UICellConfig.ChatRoom_RoomList_Page, self.view.ChatPanel)
            Game.TeaRoomSystem.model.socialData.bIsBackToRoomList = false
        else
            self:CloseComponent(UICellConfig.ChatRoom_RoomList_Page, true)
            self:OpenComponent(UICellConfig.ChatRoom_RoomChat_Page, self.view.Panel_RoomChat)
        end
    else
        self:CloseComponent(UICellConfig.ChatRoom_RoomList_Page, true)
        self:CloseComponent(UICellConfig.ChatRoom_RoomChat_Page, true)
    end
end

function ChatSocial_Panel:UpdateBoundRectWidgets()
    local view = self.view
    local widget = self.widget
    widget:AddPanelRegionWidget(view.WBP_ComMaskL_Adaptive.Canvas_BgContainer_lua)
    widget:AddPanelRegionWidget(view.WBP_ComMaskL_Adaptive.HorizontalBox_Content)
    -- local maildetail = Game.MailSystem:GetDetailPanel()
    -- if maildetail then
    --     widget:AddPanelRegionWidget(maildetail.view.NoEmpty)
    -- end
end

--- 此处为自动生成
function ChatSocial_Panel:on_Btn_ClickArea_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.ChatSet_Panel, 1)
end

--- 此处为自动生成
function ChatSocial_Panel:on_WBP_ComMaskL_AdaptiveBtn_CloseBig_Button_ClickArea_lua_Clicked()
    self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function ChatSocial_Panel:on_SB_SecondTabListCom_ItemSelected(index, data)
    if type(index) == "table" then
        Log.Error("ChatSocial_Panel OnClick_WBP_ComListLv1 Args error")
        Log.Dump(index)
        return
    end
    -- self.TabList:Sel(index)
	--从私聊界面切换到别的界面时，应该清空self.targetID，这样再切回到私聊界面时，才不会一直默认选中 self.targetID
	if self.OpeningTabIndex == Enum.ESocialTab.PrivateChat and index ~= Enum.ESocialTab.PrivateChat then
		self.targetID = nil
	end
	
    self.OpeningTabIndex = index
    local targetType = self.targetType
    if index == Enum.ESocialTab.ChannelChat then
        targetType = targetType or Enum.EChatTarget.Channel
        self:OpenComponent(UICellConfig.ChatContent_Page, self.view.ChatPanel, {targetID = self.targetID, 
            targetType = targetType, openParams = self.openParams})
        self:CloseComponent(UICellConfig.PartnerFather)
        self:CloseComponent(UICellConfig.Mail_Page, true)
        self:UpdateChatRoomRoomList(false)
        UI.HideUI("P_FriendGroup")
    elseif index == Enum.ESocialTab.Friend then
        self:OpenComponent(UICellConfig.PartnerFather, self.view.ChatPanel)
		self:CloseComponent(UICellConfig.ChatContent_Page, true)
        self:CloseComponent(UICellConfig.Mail_Page, true)
        self:UpdateChatRoomRoomList(false)
        UI.HideUI("P_FriendGroup")
    elseif index == Enum.ESocialTab.PrivateChat then
        targetType = targetType or Enum.EChatTarget.Friend
        self:OpenComponent(UICellConfig.ChatContent_Page, self.view.ChatPanel, {targetID = self.targetID, 
            targetType = targetType, openParams = self.openParams})
        self:CloseComponent(UICellConfig.PartnerFather)
        self:CloseComponent(UICellConfig.Mail_Page, true)
        self:UpdateChatRoomRoomList(false)
        UI.HideUI("P_FriendGroup")
    elseif index == Enum.ESocialTab.ChatRoom then
        self:UpdateChatRoomRoomList(true)
		self:CloseComponent(UICellConfig.ChatContent_Page, true)
        self:CloseComponent(UICellConfig.PartnerFather)
        self:CloseComponent(UICellConfig.Mail_Page, true)
        UI.HideUI("P_FriendGroup")
    elseif index == Enum.ESocialTab.Mail then
        targetType = targetType or Enum.EChatTarget.Mail
        self:OpenComponent(UICellConfig.Mail_Page, self.view.MailPanel, {targetID = self.targetID, 
            targetType = targetType, openParams = self.openParams})
		self:CloseComponent(UICellConfig.ChatContent_Page, true)
        self:CloseComponent(UICellConfig.PartnerFather)
        self:UpdateChatRoomRoomList(false)
        UI.HideUI("P_FriendGroup")
    elseif index == Enum.ESocialTab.Moments then
		self:CloseComponent(UICellConfig.ChatContent_Page, true)
        UI.ShowUI("P_FriendGroup")
        self:CloseComponent(UICellConfig.PartnerFather)
        self:UpdateChatRoomRoomList(false)
        self:CloseComponent(UICellConfig.Mail_Page, true)
    end
    self.targetType = nil
end

return ChatSocial_Panel
