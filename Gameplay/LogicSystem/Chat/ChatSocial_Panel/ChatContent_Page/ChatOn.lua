local FavorItem = kg_require("Gameplay.LogicSystem.Partner.FavorItem")
local UIComSwitchBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSwitchBtn")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatOn : UIComponent
---@field view ChatOnBlueprint
local ChatOn = DefineClass("ChatOn", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local tarot_team_const = kg_require("Shared.Const.TarotTeamConsts")

ChatOn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatOn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatOn:InitUIData()
    self.onSwitchCallback = nil
    self.onSwitchCheckCallback = nil
    self.TipsID = nil
end

--- UI组件初始化，此处为自动生成
function ChatOn:InitUIComponent()
    ---@type FavorItem
    self.WBP_FavorItemCom = self:CreateComponent(self.view.WBP_FavorItem, FavorItem)
    ---@type UIComSwitchBtn
    self.BarrageSwitchCom = self:CreateComponent(self.view.BarrageSwitch, UIComSwitchBtn)
    ---@type UIComSwitchBtn
    self.AutoAudioSwitchCom = self:CreateComponent(self.view.AutoAudioSwitch, UIComSwitchBtn)
    ---@type UIComSwitchBtn
    self.FriendBarrageSwitchCom = self:CreateComponent(self.view.FriendBarrageSwitch, UIComSwitchBtn)
end

---UI事件在这里注册，此处为自动生成
function ChatOn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.FriendBarrageSwitchCom.onClickEvent, "on_FriendBarrageSwitchCom_ClickEvent")
    self:AddUIEvent(self.AutoAudioSwitchCom.onClickEvent, "on_AutoAudioSwitchCom_ClickEvent")
    self:AddUIEvent(self.BarrageSwitchCom.onClickEvent, "on_BarrageSwitchCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatOn:InitUIView()
end

---组件刷新统一入口
function ChatOn:Refresh()

end

function ChatOn:SetSimpleChatName(name)
    self.view.HB_Name_Group:SetVisibility(ESlateVisibility.Collapsed)
    self.view.RichText_SignName:SetText(name)
end

function ChatOn:RefreshPrivateChatInfo(info)
    self.view.HB_Name_Group:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local name = Game.FriendSystem:GetFriendShowName(info.id, info.rolename)
    self.view.Text_Name:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    -- self.view.HB_Name_Group:SetVisibility(ESlateVisibility.Hidden)
	self.view.RichText_SignName:SetVisibility(ESlateVisibility.Collapsed)
    self.view.Text_Name:SetText(name)
    --好感度处理
    local showAttractionData = true
    if not Game.FriendSystem:CheckIsBothWayFriend(info.id) then
        --在黑名单、陌生人中的好友，不显示好感度信息
        showAttractionData = false
    end
    if showAttractionData and info.attraction then
        self.view.WBP_FavorItem:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.WBP_FavorItemCom:Refresh(info)
    else
        self.view.WBP_FavorItem:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.view.FriendBarrage:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local IsOn = Game.FriendSystem:getFriendStateRemindFlag(info.id, Enum.EFRIEND_STATE_REMIND_TYPE.BULLET_WHISPER)
    self:SetFriendBarrageSwitch({
        IsOpen = IsOn,
        OnSwitchCallback = function(isOpen)
            Game.FriendSystem:SetFriendStateRemindFlag(info.id, Enum.EFRIEND_STATE_REMIND_TYPE.BULLET_WHISPER, isOpen)
        end,
        OnSwitchCheckCallback = function()
            local relationInfo = Game.FriendSystem:GetFriendInfoByEntityID(info.id)
            if relationInfo == nil then
                Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.FRIEND_SETTING_WARNING_PLEASE_ADD_FIRST, function()
                    if info.lv < Game.TableData.GetConstDataRow("FRIEND_SERVER_LEVEL_LIMIT") then
                        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FRIEND_LEVEL_LIMIT)
                        return
                    end
                    Game.FriendSystem:SendAddFriend(info.id ,Enum.EFriendAddSourceData.CHAT, 0)
                end)
                return false
            end
            return true
        end
    })
    self:HandleTarotTeamTag(info)
end

function ChatOn:RefreshClubChatInfo(roleInfo)
    self.view.ChannelHead:SetVisibility(ESlateVisibility.Collapsed)
    self.view.PrivateHead:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.view.Text_Name:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.view.HB_Name_Group:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.RichText_SignName:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.RichText_SignName:SetText(roleInfo.notice)
    local countText = string.format("%s[%d/%d]", roleInfo.name, roleInfo.onlineCount, #roleInfo.members)
    self.view.Text_Name:SetText(countText)
    self.view.WBP_FavorItem:SetVisibility(ESlateVisibility.Collapsed)
    self.view.FriendBarrage:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local IsOn = Game.ChatClubSystem:IsClubBarrageShow(roleInfo.clubId)
    self:SetFriendBarrageSwitch({
        IsOpen = IsOn,
        OnSwitchCallback = function(isOpen)
            Game.ChatClubSystem:SetFriendClubBulletScreen(roleInfo.clubId, isOpen)
        end
    })
    self:HandleTarotTeamTag(roleInfo)
end

function ChatOn:HandleTarotTeamTag(roleInfo)
    -- 是否塔罗小队群聊
    if roleInfo.owner == "" and Game.me.tarotTeamID ~= tarot_team_const.TAROT_TEAM_DEFAULT_ID then
        self.view.Img_TarotTeam:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.Img_TarotTeam:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function ChatOn:SetTargetType(bIsPrivateChat)
    if bIsPrivateChat then
        self.view.ChannelHead:SetVisibility(ESlateVisibility.Collapsed)
        self.view.PrivateHead:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.ChannelHead:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.PrivateHead:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function ChatOn:SetChannelBarrageSwitch(data)
    self.BarrageSwitchCom:Refresh(data.IsOpen or false)
    self.onChannelBarrageSwitchCallback = data.OnSwitchCallback
    self:SetChannelBarrageVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function ChatOn:SetAutoAudioBarrageSwitch(data)
    self.AutoAudioSwitchCom:Refresh(data.IsOpen)
    self.onAutoAudioBarrageSwitchCallback = data.OnSwitchCallback
    self:SetAutoAudioSwitchVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function ChatOn:SetFriendBarrageSwitch(data)
    self.FriendBarrageSwitchCom:Refresh(data.IsOpen)
    self.onFriendBarrageSwitchCheckCallback = data.OnSwitchCallback
    self.onSwitchCheckCallback = data.OnSwitchCheckCallback
end

function ChatOn:SetChannelBarrageVisibility(visibility)
    self.view.Barrage:SetVisibility(visibility)
end

function ChatOn:SetAutoAudioSwitchVisibility(visibility)
    self.view.AutoVoice:SetVisibility(visibility)
end

function ChatOn:SetBtnInfoVisibility(visibility, tipsID)
    self.view.Btn_Info:SetVisibility(visibility)
    self.TipsID = tipsID
end

--- 此处为自动生成
---@param isOn bool
function ChatOn:on_BarrageSwitchCom_ClickEvent(isOn)
    self.onChannelBarrageSwitchCallback(isOn)
end

--- 此处为自动生成
---@param isOn bool
function ChatOn:on_FriendBarrageSwitchCom_ClickEvent(isOn)
    if self.onFriendBarrageSwitchCheckCallback ~= nil and not self.onSwitchCheckCallback() then
        return
    end
    self.onFriendBarrageSwitchCheckCallback(isOn)
end

--- 此处为自动生成
---@param isOn bool
function ChatOn:on_AutoAudioSwitchCom_ClickEvent(isOn)
    self.onAutoAudioBarrageSwitchCallback(isOn)
end


--- 此处为自动生成
function ChatOn:on_Btn_ClickArea_Clicked()
    Game.TipsSystem:ShowTips(self.TipsID, self.view.Btn_ClickArea:GetCachedGeometry())
end

return ChatOn
