local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatDiscloseBtn : UIComponent
---@field view ChatDiscloseBtnBlueprint
local ChatDiscloseBtn = DefineClass("ChatDiscloseBtn", UIComponent)

ChatDiscloseBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatDiscloseBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatDiscloseBtn:InitUIData()
    ---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function ChatDiscloseBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ChatDiscloseBtn:InitUIEvent()
    self:AddUIEvent(self.view.Button.OnClicked, "on_Button_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatDiscloseBtn:InitUIView()
end

---组件刷新统一入口
function ChatDiscloseBtn:Refresh(bIsDisclose, text)
    self.userWidget:Event_UI_Style(bIsDisclose)
    self.view.TB_Word:SetText(text)
end


--- 此处为自动生成
function ChatDiscloseBtn:on_Button_Clicked()
    self.onClickEvent:Execute()
end

return ChatDiscloseBtn
