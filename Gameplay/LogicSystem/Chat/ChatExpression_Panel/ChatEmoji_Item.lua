local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatEmoji_Item : UIListItem
---@field view ChatEmoji_ItemBlueprint
local ChatEmoji_Item = DefineClass("ChatEmoji_Item", UIListItem)

ChatEmoji_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatEmoji_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatEmoji_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatEmoji_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ChatEmoji_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatEmoji_Item:InitUIView()
end

---面板打开的时候触发
function ChatEmoji_Item:OnRefresh(data)
    self:SetImage(self.view.Img_Emoji, data["Icon"])
end

return ChatEmoji_Item
