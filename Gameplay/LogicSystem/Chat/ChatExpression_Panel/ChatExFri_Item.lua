local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatExFri_Item : UIListItem
---@field view ChatExFri_ItemBlueprint
local ChatExFri_Item = DefineClass("ChatExFri_Item", UIListItem)

ChatExFri_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatExFri_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatExFri_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatExFri_Item:InitUIComponent()
    ---@type SocialHead
    self.WBP_SocialHeadCom = self:CreateComponent(self.view.WBP_SocialHead, SocialHead)
end

---UI事件在这里注册，此处为自动生成
function ChatExFri_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatExFri_Item:InitUIView()
end

---面板打开的时候触发
function ChatExFri_Item:OnRefresh(...)
end

return ChatExFri_Item
