local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatRoom_BulletinTips_Panel : UIPanel
---@field view ChatRoom_BulletinTips_PanelBlueprint
local ChatRoom_BulletinTips_Panel = DefineClass("ChatRoom_BulletinTips_Panel", UIPanel)

ChatRoom_BulletinTips_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_BulletinTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_BulletinTips_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_BulletinTips_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_BulletinTips_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_BulletinTips_Panel:InitUIView()
end

---面板打开的时候触发
function ChatRoom_BulletinTips_Panel:OnRefresh(position, strText)
    position = FVector2D(position.X + 60, position.Y + 60)
    self.view.Canvas_Content.Slot:SetPosition(position)
    self.view.Text_Concent:SetText(strText)
end

return ChatRoom_BulletinTips_Panel