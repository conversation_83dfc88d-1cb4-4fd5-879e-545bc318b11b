local ChatRoom_BlindDateHeadWidget = kg_require("Gameplay.LogicSystem.Chat.ChatRoom.Party_BlindDate.ChatRoom_BlindDateHeadWidget")
local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatRoom_PlayerHead_Item : UIListItem
---@field view ChatRoom_PlayerHead_ItemBlueprint
local ChatRoom_PlayerHead_Item = DefineClass("ChatRoom_PlayerHead_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

ChatRoom_PlayerHead_Item.eventBindMap = {
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_PlayerHead_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_PlayerHead_Item:InitUIData()
    self.playerData = nil
    ---按钮点击事件
    ---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onAddBtnClickEvent = LuaDelegate.new()
    self.bIsSimpleType = false
    self.onHeadBtnClickFuncName = nil
end

--- UI组件初始化，此处为自动生成
function ChatRoom_PlayerHead_Item:InitUIComponent()
    ---@type ChatRoom_BlindDateHeadWidget
    self.WBP_ChatRoom_BlindDateHeadWidgetCom = self:CreateComponent(self.view.WBP_ChatRoom_BlindDateHeadWidget, ChatRoom_BlindDateHeadWidget)
    ---@type SocialHead
    self.WBP_HeadCom = self:CreateComponent(self.view.WBP_Head, SocialHead)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_PlayerHead_Item:InitUIEvent()
    self:AddUIEvent(self.view.WBP_AddBtn.OnClicked, "on_WBP_AddBtn_Clicked")
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_PlayerHead_Item:InitUIView()
    self.view.Img_SwitchIcon:SetVisibility(ESlateVisibility.Collapsed)
    self:SetSelectType(false)
end

---面板打开的时候触发
function ChatRoom_PlayerHead_Item:OnRefresh(data)
    self.playerData = data
    if data then
        if data.IsEmptyType then
            self.userWidget:BP_SetEmptyType(false)
            self:SetSmallType(data.IsSmallType or false)
            self:SetOnlineType(false, false)
            self:SetBlindDateHead(data)
            self:SetSpeechOrder(data.speechOrder)
            self.WBP_HeadCom:SetAnonymousData("/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head00.UI_Character_Icon_Head00")
        else
            self.WBP_HeadCom:SetData(data)
            self:SetSpeechOrder(data.speechOrder)
            self.userWidget:BP_SetEmptyType(false)
            self:SetSmallType(data.IsSmallType or false)
            self:SetBlindDateHead(data)
            if Game.TeaRoomSystem:IsRoomMasterID(data.AvatarID) and Game.TeaRoomSystem.model.roomInfoData.isPersistentParty then
                self:SetOnlineType(Game.TeaRoomSystem.model.roomInfoData.isMasterOffLine, Game.TeaRoomSystem.model.roomInfoData.isMasterLeave)
            else
                self:SetOnlineType(false, false)
            end
            self:SetSelectType(data.bIsSelected or false)
        end
    else
        self.userWidget:BP_SetEmptyType(true)
    end
    if data and data.bIsSimpleType then
        self:SetIdentityTipsSimpleHead()
    end
end

function ChatRoom_PlayerHead_Item:SetBlindDateHead(data)
    if self.bIsSimpleType then return end
    self.WBP_ChatRoom_BlindDateHeadWidgetCom:Refresh(data)
    if data.SystemIdentityState then
        self.WBP_HeadCom:SetProfessionBadgeVisible(false)
    else
        self.WBP_HeadCom:SetProfessionBadgeVisible(true)
    end
    local identityID = Game.TeaRoomSystem:GetShowIdentityIDByAvatarID(data.AvatarID)
    local bIsNotSetPlayerTag = not (data.TypeName and data.RoleTagBoard)
    if identityID and identityID ~= 0 then
        local tagName
        local tagData = Game.TableData.GetChatRoomIdentityDataRow(identityID)
        if Game.TeaRoomSystem.model.roomInfoData.customIdentityID2Name[identityID] then
            tagName = Game.TeaRoomSystem.model.roomInfoData.customIdentityID2Name[identityID]
        else
            tagName = tagData.TypeName
        end
        if bIsNotSetPlayerTag then
            self.WBP_ChatRoom_BlindDateHeadWidgetCom:SetPlayerTag(true, tagName, tagData.RoleTagBoard)
        end
    else
        if Game.TeaRoomSystem:IsRoomMasterID(data.AvatarID) then
            if bIsNotSetPlayerTag then
                local identityData = Game.TeaRoomSystem:GetNormalRoomTagData(Game.TeaRoomSystem:IsRoomMasterID(self.playerData.AvatarID))
                if identityData then
                    self.WBP_ChatRoom_BlindDateHeadWidgetCom:SetPlayerTag(true, identityData.TypeName, identityData.RoleTagBoard)
                else
                    self.WBP_ChatRoom_BlindDateHeadWidgetCom:SetPlayerTag()
                end
            end
        else
            if bIsNotSetPlayerTag then
                -- 听众标签不显示在玩家列表
                self.WBP_ChatRoom_BlindDateHeadWidgetCom:SetPlayerTag()
            end
        end
    end
    self.view.Canvas_HeadWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

-- 发言顺序
function ChatRoom_PlayerHead_Item:SetSpeechOrder(nSpeechOrder)
    if not nSpeechOrder then
        self.view.Canvas_Num:SetVisibility(ESlateVisibility.Collapsed)
        return
    end
    local speechOrder = math.floor(nSpeechOrder)
    if speechOrder and speechOrder > 0 then
        self.view.Text_Num:SetText(tostring(speechOrder))
        self.view.Canvas_Num:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        -- self.WBP_HeadCom:SetLevel(-1)
    else
        self.view.Canvas_Num:SetVisibility(ESlateVisibility.Collapsed)
        -- self.WBP_HeadCom:SetLevel(self.playerData.Level)
    end
    self.WBP_HeadCom:SetLevel(-1)
end

-- 右下角切换图标
function ChatRoom_PlayerHead_Item:SetSwitchType(bIsSwitchType)
    self.view.Img_SwitchIcon:SetVisibility(bIsSwitchType and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
    self.onHeadBtnClickFuncName = "on_WBP_AddBtn_Clicked"
end

function ChatRoom_PlayerHead_Item:SetSmallType(bIsSmallType)
    self.userWidget:BP_SetSmallType(bIsSmallType or false)
    self.WBP_ChatRoom_BlindDateHeadWidgetCom:SetSmallType(bIsSmallType or false)
end

function ChatRoom_PlayerHead_Item:SetEmptyType(bIsEmptyType)
    self.userWidget:BP_SetEmptyType(bIsEmptyType or false)
end

function ChatRoom_PlayerHead_Item:SetSelectType(bIsSelectType)
    self.userWidget:BP_SetSelectType(bIsSelectType or false)
end

function ChatRoom_PlayerHead_Item:SetOffLine(bIsOffLine, bIsLeave)
    self.userWidget:BP_SetOffline(bIsOffLine or bIsLeave or false)
    if bIsOffLine then
        self.view.Text_Offline:SetText(Game.TableData.GetChatRoomStringConstDataRow("CHATROOM_PATRY_OWNER_OFFLINE").StringValue)
    elseif bIsLeave then
        self.view.Text_Offline:SetText(Game.TableData.GetChatRoomStringConstDataRow("CHATROOM_PATRY_OWNER_LEAVE").StringValue)
    end
end

-- 设置常驻房间房主在线状态
function ChatRoom_PlayerHead_Item:SetOnlineType(isOffLine, isLeave)
    if isOffLine then
        self:SetOffLine(true, true)
    elseif isLeave then
        self:SetOffLine(false, true)
    else
        self:SetOffLine(false, false)
    end
end

function ChatRoom_PlayerHead_Item:ShowPlayerInfo()
    Game.TeamSystem:PlayerCardUIDataAsync(self.playerData.AvatarID, false, Enum.EFriendAddSourceData.CHAT, nil, Enum.EMenuType.Chat)
end

function ChatRoom_PlayerHead_Item:OnIdentityAdd(identityID, identityIDMap)
    if not self.playerData or not identityIDMap[self.playerData.AvatarID] then return end
    self:OnRefresh(self.playerData)
end

function ChatRoom_PlayerHead_Item:OnIdentityRemove(entityID)
    if not self.playerData or self.playerData.AvatarID ~= entityID then return end
    self:SetBlindDateHead(self.playerData)
end

function ChatRoom_PlayerHead_Item:OnIdentityNameModify(identityID)
    if not self.playerData or self.playerData.AllocatedIdentityID ~= identityID then return end
    self:SetBlindDateHead(self.playerData)
end

-- 只展示简单头像（头像图片）
function ChatRoom_PlayerHead_Item:SetIdentityTipsSimpleHead()
    self.WBP_HeadCom:SetProfessionBadgeVisible(false)
    self.WBP_HeadCom:SetLT(false)
    self.WBP_HeadCom:SetLevel(-1)
    self.view.Canvas_HeadWidget:SetVisibility(ESlateVisibility.Collapsed)
    self.view.Canvas_Num:SetVisibility(ESlateVisibility.Collapsed)
    self:SetSwitchType(true)
    self.bIsSimpleType = true
end

function ChatRoom_PlayerHead_Item:RefreshOnSpeakingUI(bIsTalking, userId)
    if not self.playerData or self.playerData.AvatarID ~= userId then return end
    self.WBP_ChatRoom_BlindDateHeadWidgetCom:SetTalkingType(self.playerData.speechState == Enum.EVOICE_STATE.VOICE, bIsTalking)
end

--- 此处为自动生成
function ChatRoom_PlayerHead_Item:on_WBP_AddBtn_Clicked()
    self.onAddBtnClickEvent:Execute()
end


--- 此处为自动生成
function ChatRoom_PlayerHead_Item:on_Btn_ClickArea_Clicked()
    if self.onHeadBtnClickFuncName then
        self[self.onHeadBtnClickFuncName](self)
    elseif self.playerData and not (self.parentComponent.onItemClicked and self.parentComponent.onItemClicked:IsBind()) then
        self:ShowPlayerInfo()
    end
end

return ChatRoom_PlayerHead_Item
