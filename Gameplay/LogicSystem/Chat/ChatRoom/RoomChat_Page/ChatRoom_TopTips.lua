local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatRoom_TopTips : UIComponent
---@field view ChatRoom_TopTipsBlueprint
local ChatRoom_TopTips = DefineClass("ChatRoom_TopTips", UIComponent)

ChatRoom_TopTips.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_TopTips:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_TopTips:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_TopTips:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---按钮点击事件
    ---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onClickEvent = LuaDelegate.new()
    ---点击关闭事件
    ---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onClickCloseEvent = LuaDelegate.new()
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_TopTips:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_TopTips:InitUIView()
end

---组件刷新统一入口
function ChatRoom_TopTips:Refresh(text)
    self.view.Text_Name:SetText(text)
end


--- 此处为自动生成
function ChatRoom_TopTips:on_Btn_ClickArea_Clicked()
    self.onClickEvent:Execute()
end

--- 此处为自动生成
function ChatRoom_TopTips:on_WBP_ComBtnCloseCom_ClickEvent()
    self.onClickCloseEvent:Execute()
end

return ChatRoom_TopTips
