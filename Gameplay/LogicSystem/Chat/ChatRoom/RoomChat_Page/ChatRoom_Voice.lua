local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatRoom_Voice : UIComponent
---@field view ChatRoom_VoiceBlueprint
local ChatRoom_Voice = DefineClass("ChatRoom_Voice", UIComponent)

ChatRoom_Voice.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_Voice:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_Voice:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_Voice:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_Voice:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_Voice:InitUIView()
end

---组件刷新统一入口
function ChatRoom_Voice:Refresh(...)
end

function ChatRoom_Voice:PlayTalkingAnim()
    self:PlayAnimation(self.userWidget.Ani_loop, nil, self.userWidget)
end

function ChatRoom_Voice:StopTalkingAnim()
    self:StopAnimation(self.userWidget.Ani_loop)
end

return ChatRoom_Voice
