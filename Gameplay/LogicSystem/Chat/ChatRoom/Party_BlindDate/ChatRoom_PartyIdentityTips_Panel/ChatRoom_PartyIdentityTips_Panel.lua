local SystemTipsTitle = kg_require("Gameplay.LogicSystem.Tips.SystemTips.SystemTipsTitle")
local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatRoom_PartyIdentityTips_Panel : UIPanel
---@field view ChatRoom_PartyIdentityTips_PanelBlueprint
local ChatRoom_PartyIdentityTips_Panel = DefineClass("ChatRoom_PartyIdentityTips_Panel", UIPanel)
local KismetInputLibrary = import("KismetInputLibrary")
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")

ChatRoom_PartyIdentityTips_Panel.eventBindMap = {
    [EEventTypesV2.ON_CHATROOM_ASSIGN_IDENTITY_TO_AVATAR] = "RefreshPartyIdentityCom",
    [EEventTypesV2.ON_CHATROOM_MODIFY_IDENTITY_NAME] = "OnIdentityUpdate",
    [EEventTypesV2.ON_CHATROOM_REMOVE_AVATAR_IDENTITY] = "OnIdentityRemove",
    [EEventTypesV2.GME_REALTIME_AUDIO_CHANGE] = "RefreshOnSpeakingUI",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_PartyIdentityTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_PartyIdentityTips_Panel:InitUIData()
    self.identityData = {}
    self.PrevMousePos = nil
    self.PrevPos = nil
end

--- UI组件初始化，此处为自动生成
function ChatRoom_PartyIdentityTips_Panel:InitUIComponent()
    ---@type SystemTipsTitle
    self.WBP_SystemTipsTitleCom = self:CreateComponent(self.view.WBP_SystemTipsTitle, SystemTipsTitle)
    ---@type UIComEmptyConent
    self.WBP_ComEmptyCom = self:CreateComponent(self.view.WBP_ComEmpty, UIComEmptyConent)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIListView childScript: ChatRoom_PartyIdentityTips_Item
    self.KGListView_PartyIdentityCom = self:CreateComponent(self.view.KGListView_PartyIdentity, UIListView)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_PartyIdentityTips_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.view.KGHotArea_Touch.OnTouchStartedEvent, "on_KGHotArea_Touch_TouchStartedEvent")
    self:AddUIEvent(self.view.KGHotArea_Touch.OnTouchEndedEvent, "on_KGHotArea_Touch_TouchEndedEvent")
    self:AddUIEvent(self.view.KGHotArea_Touch.OnTouchMovedEvent, "on_KGHotArea_Touch_TouchMovedEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_PartyIdentityTips_Panel:InitUIView()
    self.WBP_SystemTipsTitleCom:Refresh(Game.TableData.GetChatRoomStringConstDataRow("CHATROOM_PARTY_IDENTITY_ALLOT").StringValue)
end

---面板打开的时候触发
function ChatRoom_PartyIdentityTips_Panel:OnRefresh(position)
    self.view.Canvas_Content.Slot:SetPosition(position)
    table.clear(self.identityData)
    for _, identityInfo in ksbcpairs(Game.TableData.GetChatRoomIdentityDataTable()) do
        if identityInfo.CanRoomOwnerAssign then
            for _,roomTypeID in ksbcpairs(identityInfo.BelongRoomID) do
                if roomTypeID == Game.TeaRoomSystem.model.roomInfoData.nRoomType then
                    table.insert(self.identityData, {
                        ID = identityInfo.ID,
                        Name = identityInfo.TypeName,
                        CanRoomOwnerRename = identityInfo.CanRoomOwnerRename,
                        CountLimit = identityInfo.CountLimit,
                        RoleTagBoard = identityInfo.RoleTagBoard
                    })
                    break
                end
            end
        end
    end
    self:RefreshPartyIdentityCom()
end

function ChatRoom_PartyIdentityTips_Panel:RefreshPartyIdentityCom()
    self.KGListView_PartyIdentityCom:Refresh(self.identityData)
end

function ChatRoom_PartyIdentityTips_Panel:RefreshOnSpeakingUI(bIsTalking, userId)
    for i, _ in ipairs(self.identityData) do
        local item = self.KGListView_PartyIdentityCom:GetItemByIndex(i)
        if item then
            item:RefreshOnSpeakingUI(bIsTalking, userId)
        end
    end
end

function ChatRoom_PartyIdentityTips_Panel:OnIdentityRemove(entityID)
    for i, identityInfo in ipairs(self.identityData) do
        if identityInfo.AvatarID == entityID then
            local item = self.KGListView_PartyIdentityCom:GetItemByIndex(i)
            if item then
                item:OnIdentityRemove(entityID)
            end
            break
        end
    end
end

function ChatRoom_PartyIdentityTips_Panel:OnIdentityUpdate(identityID, name)
    for i, identityInfo in ipairs(self.identityData) do
        if identityInfo.ID == identityID then
            self.identityData[i].Name = name
            self.KGListView_PartyIdentityCom:RefreshItemByIndex(i)
            break
        end
    end
end

function ChatRoom_PartyIdentityTips_Panel:OnClose()
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.ChatRoom_VoiceManagement_Panel) then
        Game.NewUIManager:ClosePanel(UIPanelConfig.ChatRoom_VoiceManagement_Panel)
    end
end

--- 此处为自动生成
function ChatRoom_PartyIdentityTips_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
    self:CloseSelf()
end


--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent

function ChatRoom_PartyIdentityTips_Panel:on_KGHotArea_Touch_TouchStartedEvent(myGeometry, inPointerEvent)
    self.PrevMousePos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
    self.PrevPos = self.view.Canvas_Content.Slot:GetPosition()
    return WidgetBlueprintLibrary.CaptureMouse(WidgetBlueprintLibrary.Handled(),
        self.view.KGHotArea_Touch)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function ChatRoom_PartyIdentityTips_Panel:on_KGHotArea_Touch_TouchMovedEvent(myGeometry, inPointerEvent)
    if self.PrevMousePos == nil then
        return
    end
    local CurrentMousePos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
    local Delta = CurrentMousePos - self.PrevMousePos
    local viewportScale = WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
    local TargetPos = self.PrevPos + Delta / viewportScale
    self.view.Canvas_Content.Slot:SetPosition(TargetPos)
    return UIBase.HANDLED
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function ChatRoom_PartyIdentityTips_Panel:on_KGHotArea_Touch_TouchEndedEvent(myGeometry, inPointerEvent)
    self.PrevMousePos = nil
    return UIBase.HANDLED
end

return ChatRoom_PartyIdentityTips_Panel
