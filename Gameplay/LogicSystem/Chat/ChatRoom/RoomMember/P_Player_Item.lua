---@class P_Player_Item : UIComponent---@field public View WBP_Player_ItemView
local P_Player_Item = DefineClass("P_Player_Item", UIComponent)
local P_ComSocialHead = kg_require("Gameplay.LogicSystem.CommonUI.P_ComSocialHead")
local ChatCommonClubHead = kg_require("Gameplay.LogicSystem.Chat.ChatCommon.ChatCommonClubHead")

function P_Player_Item:OnCreate()
    -- 玩家头像
    self.playerSocialHead = self:BindComponent(self.View.WBP_SocialHead, P_ComSocialHead)
    -- 群头像
    self.groupSocialHead = self:BindComponent(self.View.WBP_SocialGroupHead, ChatCommonClubHead)
    -- 玩家信息
    self.playerData = nil
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button, self.OnSelectPlayer)
    self:AddUIListener(EUIEventTypes.CheckStateChanged, self.View.CheckBox, self.OnCheckBoxChange)
end

function P_Player_Item:OnShow()

end

function P_Player_Item:OnHide()

end

-- 分享界面 
function P_Player_Item:RefreshItem(relation,clubID,index,selected,editType)
    self.clubID = clubID
    self.relation = relation
    self.index = index
    self.editType = editType
    if self.editType == Game.FriendSystem.EditType.MomentsShareToChatChannel then
        self.View:Event_UI_Style(1)
        self.View.Text_Title:SetText(self.relation.channelName)
    elseif self.editType == Game.FriendSystem.EditType.MomentsShareToChatGroup then
        self.View:Event_UI_Style(2)
        self.groupSocialHead:SetData(self.relation.members)
        self.View.Text_GroupPlayerNumber:SetText(string.format("%d/%d", self.relation.onlineCount, #self.relation.members))
        self.View.Text_Title:SetText(self.relation.name)
    end

	--- 更新状态
	self.View.CheckBox:SetIsChecked(selected)
end

function P_Player_Item:OnCheckBoxChange(bChecked)
    if bChecked then
        UI.Invoke("P_PartnerAddOrRemove", "Sel", self.index)
    else
        UI.Invoke("P_PartnerAddOrRemove", "CancelSel", self.index)
    end
end

function P_Player_Item:RefreshAtPlayerItemData(playerData)
    self.View:Event_UI_Style(9)
    self.View.Text_Title:SetText(playerData.rolename)
    self.playerData = playerData
    self.playerSocialHead:Refresh({Level = playerData.lv, ProfessionID = playerData.school, OnClickedCallBack = function() end})
end

function P_Player_Item:OnSelectPlayer()
    -- TODO 当前好友数据没有服务器信息（默认同服好友，先传自己的服务器id)
    if self.playerData then
        Game.GlobalEventSystem:Publish(EEventTypesV2.MOMENTS_ON_ADD_TOPIC, self.playerData.rolename, self.playerData.id, Game.LoginSystem:GetServerLoginData().ServerId)
    end
end


return P_Player_Item
