local ChatRoom_CreateRoom_RoomName = kg_require("Gameplay.LogicSystem.Chat.ChatRoom.RoomOption_Panel.ChatRoom_CreateRoom_RoomName")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatRoom_CreateRoom_Text : UIComponent
---@field view ChatRoom_CreateRoom_TextBlueprint
local ChatRoom_CreateRoom_Text = DefineClass("ChatRoom_CreateRoom_Text", UIComponent)

ChatRoom_CreateRoom_Text.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_CreateRoom_Text:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_CreateRoom_Text:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_CreateRoom_Text:InitUIComponent()
    ---@type ChatRoom_CreateRoom_RoomName
    self.WBP_ChatRoom_CreateRoom_RoomNameCom = self:CreateComponent(self.view.WBP_ChatRoom_CreateRoom_RoomName, ChatRoom_CreateRoom_RoomName)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_CreateRoom_Text:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_CreateRoom_Text:InitUIView()
end

---组件刷新统一入口
function ChatRoom_CreateRoom_Text:Refresh(title, tipsId, text, bIsIdentityName)
    self.userWidget:BP_SetIdentity(false)
    self.WBP_ChatRoom_CreateRoom_RoomNameCom:Refresh(title, tipsId)
    if bIsIdentityName then
        text = self:GetShowingIdentityName()
    end
    self.view.Text_Info:SetText(text or "暂无")
end

function ChatRoom_CreateRoom_Text:GetShowingIdentityName()
    local identityID = Game.TeaRoomSystem:GetShowIdentityIDByAvatarID(Game.me.eid)
    local tagName
    if identityID and identityID ~= 0 then
        if Game.TeaRoomSystem:GetRoomMemberInfo(Game.me.eid).AllocatedIdentityID and Game.TeaRoomSystem:GetRoomMemberInfo(Game.me.eid).AllocatedIdentityID ~= 0 then
            self.userWidget:BP_SetIdentity(true)
        end
        local tagData = Game.TableData.GetChatRoomIdentityDataRow(identityID)
        if Game.TeaRoomSystem.model.roomInfoData.customIdentityID2Name[identityID] then
            tagName = Game.TeaRoomSystem.model.roomInfoData.customIdentityID2Name[identityID]
        else
            tagName = tagData.TypeName
        end
    end
    return tagName
end

-- 点击摘除身份标签
--- 此处为自动生成
function ChatRoom_CreateRoom_Text:on_Btn_ClickArea_Clicked()
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CHATROOM_IDENTITY_TAG_REMOVE, function()
        if Game.TeaRoomSystem.model.roomInfoData then
            Game.TeaRoomSystem:ReqRemoveIdentification(Game.TeaRoomSystem:GetShowIdentityIDByAvatarID(Game.me.eid))
        end
    end)
end

return ChatRoom_CreateRoom_Text
