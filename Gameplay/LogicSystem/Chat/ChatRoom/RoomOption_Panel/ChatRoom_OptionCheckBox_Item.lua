local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatRoom_OptionCheckBox_Item : UIListItem
---@field view ChatRoom_OptionCheckBox_ItemBlueprint
local ChatRoom_OptionCheckBox_Item = DefineClass("ChatRoom_OptionCheckBox_Item", UIListItem)

ChatRoom_OptionCheckBox_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_OptionCheckBox_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_OptionCheckBox_Item:InitUIData()
    self.bIsChecked = false
end

--- UI组件初始化，此处为自动生成
function ChatRoom_OptionCheckBox_Item:InitUIComponent()
    ---@type UIComCheckBox
    self.WBP_ComCheckBoxCom = self:CreateComponent(self.view.WBP_ComCheckBox, UIComCheckBox)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_OptionCheckBox_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_OptionCheckBox_Item:InitUIView()
end

---面板打开的时候触发
function ChatRoom_OptionCheckBox_Item:OnRefresh(data)
    self.SelfType = data.SelfType
    self.SelectType = data.SelectType
    self.bIsChecked = self.SelectType and self.SelfType == self.SelectType or false
    self.view.Text_CR_Title:SetText(data.TypeName or "")
    self.WBP_ComCheckBoxCom:SetHasText(false)
end

function ChatRoom_OptionCheckBox_Item:GetIsChecked()
    return self.bIsChecked
end

function ChatRoom_OptionCheckBox_Item:UpdateSelectionState(selected)
    self.bIsChecked = selected
    self.WBP_ComCheckBoxCom:SetChecked(selected, false)
end

--- 此处为自动生成
function ChatRoom_OptionCheckBox_Item:on_Btn_ClickArea_Clicked()
end

return ChatRoom_OptionCheckBox_Item
