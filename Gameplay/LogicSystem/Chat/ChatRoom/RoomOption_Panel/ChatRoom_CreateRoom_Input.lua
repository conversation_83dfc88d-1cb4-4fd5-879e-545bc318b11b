local ChatRoom_InputBox = kg_require("Gameplay.LogicSystem.Chat.ChatRoom.RoomOption_Panel.ChatRoom_InputBox")
local ChatRoom_CreateRoom_RoomName = kg_require("Gameplay.LogicSystem.Chat.ChatRoom.RoomOption_Panel.ChatRoom_CreateRoom_RoomName")
local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatRoom_CreateRoom_Input : UIComponent
---@field view ChatRoom_CreateRoom_InputBlueprint
local ChatRoom_CreateRoom_Input = DefineClass("ChatRoom_CreateRoom_Input", UIComponent)

ChatRoom_CreateRoom_Input.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_CreateRoom_Input:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_CreateRoom_Input:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_CreateRoom_Input:InitUIComponent()
    ---@type ChatRoom_InputBox
    self.WBP_RoomNameInputCom = self:CreateComponent(self.view.WBP_RoomNameInput, ChatRoom_InputBox)
    ---@type ChatRoom_CreateRoom_RoomName
    self.WBP_ChatRoom_CreateRoom_RoomNameCom = self:CreateComponent(self.view.WBP_ChatRoom_CreateRoom_RoomName, ChatRoom_CreateRoom_RoomName)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_CreateRoom_Input:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_CreateRoom_Input:InitUIView()
end

---组件刷新统一入口
function ChatRoom_CreateRoom_Input:Refresh(title, tipsId, text, hintText, limitNum)
    self.WBP_ChatRoom_CreateRoom_RoomNameCom:Refresh(title, tipsId)
    self.WBP_RoomNameInputCom:Refresh(text, hintText, limitNum, true)
end

function ChatRoom_CreateRoom_Input:GetInputText()
    return self.WBP_RoomNameInputCom:GetText()
end

return ChatRoom_CreateRoom_Input
