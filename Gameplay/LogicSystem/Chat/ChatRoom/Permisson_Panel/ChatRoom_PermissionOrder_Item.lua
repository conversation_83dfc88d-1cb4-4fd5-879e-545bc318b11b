local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatRoom_PermissionOrder_Item : UIListItem
---@field view ChatRoom_PermissionOrder_ItemBlueprint
local ChatRoom_PermissionOrder_Item = DefineClass("ChatRoom_PermissionOrder_Item", UIListItem)
local UIDrag = kg_require("Framework.KGFramework.KGUI.Component.CommonLogic.UIComDragWidget")
local ESlateVisibility = import("ESlateVisibility")

ChatRoom_PermissionOrder_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_PermissionOrder_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_PermissionOrder_Item:InitUIData()
    self.TemplateUI = nil
    self.bBindDragComp = false
end

--- UI组件初始化，此处为自动生成
function ChatRoom_PermissionOrder_Item:InitUIComponent()
    ---@type SocialHead
    self.WBP_SocialHeadCom = self:CreateComponent(self.view.WBP_SocialHead, SocialHead)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_PermissionOrder_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_PermissionOrder_Item:InitUIView()
    ---@type UIDrag
    self.uiDragCom = self:CreateComponent(self.view.Btn_ClickArea, UIDrag)
    self.uiDragCom:SetDragSource(self.userWidget, false)
    self:AddUIEvent(self.uiDragCom.onDragDetectedEvent, "OnDragDetected")
    self:AddUIEvent(self.uiDragCom.onDragCancelEvent, "OnDragCancelled")
    self:AddUIEvent(self.uiDragCom.onDragDropEvent, "OnDragDroped")
    
end

---面板打开的时候触发
function ChatRoom_PermissionOrder_Item:OnRefresh(playerData)
    if table.count(playerData) == 0 then
        self:SetEmptyInfo(self.index)
    else
        self:SetMicOrderInfo(playerData, true)
    end
    if self.TemplateUI == nil and not self.bBindDragComp then
        self.TemplateUI = self:CreateComponent(self.uiDragCom:GetDragWidget())
        self.TemplateUI.index = self.index
        self.TemplateUI.bBindDragComp = true
    end
    self.TemplateUI:SetMicOrderInfo(playerData, true)
end

function ChatRoom_PermissionOrder_Item:SetEmptyInfo(nIndex)
    if nIndex == nil then
        return
    end
    -- 屏蔽拖拽事件
    self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Collapsed)
    self.userWidget:BP_SetEmptyState(true)
    self.view.Text_OrderNum:SetText(tostring(nIndex))
end

function ChatRoom_PermissionOrder_Item:SetMicOrderInfo(tblPlayerData, bBind)
    if tblPlayerData == nil then
        return
    end
    self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
    self.view.Text_OrderNum:SetText(tostring(self.index))
    self.Data = tblPlayerData
    self.userWidget:BP_SetEmptyState(false)
    self.WBP_SocialHeadCom:Refresh(tblPlayerData)
    -- 隐藏头像等级显示
    self.WBP_SocialHeadCom:SetLevel(0)
    self:SetPlayerName(tblPlayerData.Nickname)
end

function ChatRoom_PermissionOrder_Item:SetPlayerName(strPlayerName)
    self.view.Text_Name:SetText(strPlayerName or "")
end

function ChatRoom_PermissionOrder_Item:onMouseButtonDown(_, PointerEvent)
    local eventReply = import("WidgetBlueprintLibrary").DetectDragIfPressed(
            PointerEvent,
            self.userWidget,
            import("UIFunctionLibrary").GetKeyFromName("LeftMouseButton"))
    return eventReply
end

function ChatRoom_PermissionOrder_Item:OnDragSetUIFunc(Widget)
    Widget:Event_UI_Style(0, false)
    if self.TemplateUI == nil then
        self.TemplateUI = self:CreateComponent(Widget, ChatRoom_PermissionOrder_Item)
    end
    self.TemplateUI.bBindDragComp = true
    self.TemplateUI:SetMicOrderInfo(self.Data, false)
end

function ChatRoom_PermissionOrder_Item:OnDragDetected(MyGeometry,InMouseEvent)
    if not Game.TeaRoomSystem:IsInTeaRoom() or not self.Data then
        return
    end
    if not Game.TeaRoomSystem:IsPermission_SpeechOrderInit() then
        Game.TeaRoomSystem:InitPermission_SpeechOrder()
    end
    local strAvatarID = self.Data.AvatarID
    for nIndex, tmpAvatarID in ipairs(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder) do
        if tmpAvatarID == strAvatarID then
            Game.TeaRoomSystem:SetPermission_MoveOrder_MemberIndex(nIndex)
            break
        end
    end
end

function ChatRoom_PermissionOrder_Item:OnDragCancelled(PointEvent, DragOperation)
    Game.TeaRoomSystem:SetPermission_MoveOrder_MemberIndex(nil)
end

function ChatRoom_PermissionOrder_Item:OnDragDroped(_, _, DragOperation)
    if not Game.TeaRoomSystem:IsInTeaRoom() or self.Data == nil then
        return
    end
    if not Game.TeaRoomSystem:IsPermission_SpeechOrderInit() then
        Game.TeaRoomSystem:InitPermission_SpeechOrder()
    end
    local nOldMemberIndex = Game.TeaRoomSystem:GetPermission_SpeechOrder()
    local strAvatarID = self.Data.AvatarID
    for nIndex, tmpAvatarID in ipairs(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder) do
        if nIndex ~= nOldMemberIndex and tmpAvatarID == strAvatarID then
            local tmp = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder[nOldMemberIndex]
            if tmp == nil then return end
            Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder[nOldMemberIndex] = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder[nIndex]
            Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder[nIndex] = tmp
            self:GetParent():GetParent():OnSpeechOrderChanged()
            break
        end
    end
    Game.TeaRoomSystem:SetPermission_MoveOrder_MemberIndex(nil)
end

return ChatRoom_PermissionOrder_Item
