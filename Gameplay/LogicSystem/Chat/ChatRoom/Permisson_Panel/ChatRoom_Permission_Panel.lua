local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIComTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComTabList")
local UIComSimpleTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComTextSearchBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComTextSearchBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESelectionMode = import("ESelectionMode")
---@class ChatRoom_Permission_Panel : UIPanel
---@field view ChatRoom_Permission_PanelBlueprint
local ChatRoom_Permission_Panel = DefineClass("ChatRoom_Permission_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ChatRoom_Permission_Panel.eventBindMap = {
    [EEventTypesV2.ON_CHATROOM_BLACK_LIST_CHANGE] = "UpdateBlackList",
    [EEventTypesV2.ON_CHATROOM_GAG_LIST_CHANGE] = "UpdateGagList",
    [EEventTypesV2.ON_CHATROOM_ORDER_LIST_CHANGE] = "UpdatePlayerOrderList",
    [EEventTypesV2.ON_CHATROOM_APPLY_LIST_CHANGE] = "UpdateMicApplyList",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_Permission_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_Permission_Panel:InitUIData()
    self.TabEnum = {
        MicManage = 1,
        ForbidSpeech = 2,
        BlackList = 3,
    }
    self.TabData = 
    {
        {
            TabEnum = self.TabEnum.MicManage,
            name = StringConst.Get("SOCIAL_CHATROOM_MICMANAGE_TABNAME"),
            redPointId = "SocialChatRoomPermissionTabMicApply"
        },
        {
            TabEnum = self.TabEnum.ForbidSpeech,
            name = StringConst.Get("SOCIAL_CHATROOM_FORBIDSPEECH_TABNAME"),
        },
        {
            TabEnum = self.TabEnum.BlackList,
            name = StringConst.Get("SOCIAL_CHATROOM_BLACKLIST_TABNAME"),
        },
    }
    self.MicPermissionComboBoxData = {}
    for _, v in pairs(Const.TEAROOM_OPEM_MIC_PERMISSION) do
        self.MicPermissionComboBoxData[v] = {
            name = Const.TEAROOM_OPEM_MIC_PERMISSION_TO_NAME[v],
        }
    end
    self.nSwitchTabIndex = 1
    self.TabInfoData = nil

    ---@type string 搜索字符串
    self.strSearchText = nil
    ---@type table 
    self.EnumType = {
        None = 0,
        SPEAK_RIGHT = 1,
        ApplyOpenMic = 2,
        SPEAK_ORDER = 3,
        ForbidSpeech = 4,
        OutBlackList = 5,
        InBlackList = 6,
    }
    ---@type int
    self.nType = self.EnumType.None
    self.inOrderListData = {}
    self.Data = {}
end

--- UI组件初始化，此处为自动生成
function ChatRoom_Permission_Panel:InitUIComponent()
    ---@type UIComEmptyConent
    self.WBP_ComEmptyCom = self:CreateComponent(self.view.WBP_ComEmpty, UIComEmptyConent)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
    ---@type UIComAccordionList
    self.WBP_ComTabListMainCom = self:CreateComponent(self.view.WBP_ComTabListMain, UIComTabList)
    ---@type UIComSimpleTabList
    self.WBP_ComTabHorizontalCom = self:CreateComponent(self.view.WBP_ComTabHorizontal, UIComSimpleTabList)
    ---@type UIComDropDown
    self.WBP_ComSelectDropType1Com = self:CreateComponent(self.view.WBP_ComSelectDropType1, UIComDropDown)
    ---@type UIListView
    self.TileView_OrderCom = self:CreateComponent(self.view.TileView_Order, UIListView)
    ---@type UIListView
    self.TileView_MemberCom = self:CreateComponent(self.view.TileView_Member, UIListView)
    ---@type UIListView
    self.TileView_ApplyCom = self:CreateComponent(self.view.TileView_Apply, UIListView)
    ---@type UIListView
    self.TileView_ChooseCom = self:CreateComponent(self.view.TileView_Choose, UIListView)
    ---@type UIComTextSearchBox
    self.WBP_ComInputSearchCom = self:CreateComponent(self.view.WBP_ComInputSearch, UIComTextSearchBox)
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_Permission_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComInputSearchCom.onSearchResult, "on_WBP_ComInputSearchCom_SearchResult")
    self:AddUIEvent(self.WBP_ComSelectDropType1Com.onItemSelected, "on_WBP_ComSelectDropType1Com_ItemSelected")
    self:AddUIEvent(self.WBP_ComSelectDropType1Com.onCheckItemCanSelect, "on_WBP_ComSelectDropType1Com_CheckItemCanSelect")
    self:AddUIEvent(self.WBP_ComTabHorizontalCom.onItemSelected, "on_WBP_ComTabHorizontalCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComBackTitleCom.onPreCloseEvent, "on_WBP_ComBackTitleCom_PreCloseEvent")
    self:AddUIEvent(self.WBP_ComTabListMainCom.onItemSelected, "on_WBP_ComTabListMainCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComTabListMainCom.onGetEntryLuaClass, "on_WBP_ComTabListMainCom_GetEntryLuaClass")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_Permission_Panel:InitUIView()
    self:InitTabList(self.TabData, self.WBP_ComTabListMainCom)
    self.WBP_ComBtnCom:Refresh(StringConst.Get("SOCIAL_CHATROOM_PERMISSION_BTNNAME"))
    self.WBP_ComBackTitleCom:Refresh(StringConst.Get("SOCIAL_CHATROOM_PERMISSION_TITLE"), Enum.ETipsData.CHATROOM_HELP_TIPS)
    self.WBP_ComInputSearchCom:SetHintText(StringConst.Get("SOCIAL_CHATROOM_PERMISSION_HINTTEXT"))
end

---面板打开的时候触发
function ChatRoom_Permission_Panel:OnRefresh(...)
    self:SetMicPermissionType(Game.TeaRoomSystem.model.roomInfoData.nSpeechPrivilegeSetting)
    self:UpdateWBP_Permission_ComboBox()
end

function ChatRoom_Permission_Panel:UpdateUIStyle()
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return
    end
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.MicManage then
        local nMicPermissionType = self:GetMicPermissionType()
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_FREE then
            self.WBP_ComEmptyCom:Refresh("全员自由发言")
            self.userWidget:BP_SetFunctionType(0)
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            if self.nSwitchTabIndex == 1 then
                self.userWidget:BP_SetFunctionType(1)
            elseif self.nSwitchTabIndex == 2 then
                self.userWidget:BP_SetFunctionType(2)
            end
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            self.userWidget:BP_SetFunctionType(3)
        end
        self.view.WBP_ComBtn:SetVisibility(ESlateVisibility.Visible)
    elseif nTabIndex == self.TabEnum.ForbidSpeech then
        self.userWidget:BP_SetFunctionType(4)
    elseif nTabIndex == self.TabEnum.BlackList then
        self.userWidget:BP_SetFunctionType(5)
    end
end

function ChatRoom_Permission_Panel:InitTabList(tabData, tabCom)
    -- ---@type UITreeViewData
    -- local treeViewData = UIComAccordionList.NewTreeViewData()
    -- for _,TypeData in ipairs(tabData) do
    --     local topData = UIComAccordionList.NewTabData(TypeData.TabName)
    --     topData.otherInfo = TypeData
    --     treeViewData:AddFirstNode(topData)
    -- end
    tabCom:Refresh(tabData)
    tabCom:SetSelectedItemByIndex(1, true)
end

function ChatRoom_Permission_Panel:UpdateTabHorData()
    self.TabInfoData = nil
    local nTabIndex = self:GetTabIndex()
    local strBlackList = StringConst.Get("SOCIAL_CHATROOM_BLACKLIST_TABNAME")
    local strMemberList = StringConst.Get("SOCIAL_CHATROOM_MEMBERLIST_TABNAME")
    local strApplicationList = StringConst.Get("SOCIAL_CHATROOM_APPLICATION_TABNAME")
    if nTabIndex == self.TabEnum.MicManage then
        self.TabInfoData = {{name = strMemberList, TabIndex = 1},{name = strApplicationList, TabIndex = 2, redPointId = "SocialChatRoomPermissionMicApply"},}
    elseif nTabIndex == self.TabEnum.ForbidSpeech then
        -- 主要是用来刷PlayerList的
        self.TabInfoData = {{name = strMemberList, TabIndex = 1},{name = strApplicationList, TabIndex = 2, redPointId = "SocialChatRoomPermissionMicApply"},}
    elseif nTabIndex == self.TabEnum.BlackList then
        self.TabInfoData = {{name = strMemberList, TabIndex = 1},{name = strBlackList, TabIndex = 2},}
    end
    self.nSwitchTabIndex = 1
    self.WBP_ComTabHorizontalCom:Refresh(self.TabInfoData)
    self.WBP_ComTabHorizontalCom:SetSelectedItemByIndex(1, true)
end

function ChatRoom_Permission_Panel:UpdateWBP_Permission_ComboBox()
    local nMicPermissionType = self:GetMicPermissionType()
    self.WBP_ComSelectDropType1Com:Refresh(self.MicPermissionComboBoxData, nMicPermissionType, true)
end

function ChatRoom_Permission_Panel:UpdatePlayerList(bIsSearch, bIsOldData)
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.MicManage then
        local nMicPermissionType = self:GetMicPermissionType()
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_FREE then
            return
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            if self.nSwitchTabIndex == 1 then
                self:UpdateListByType(self.EnumType.SPEAK_RIGHT, bIsSearch, bIsOldData)
            elseif self.nSwitchTabIndex == 2 then
                self:UpdateListByType(self.EnumType.ApplyOpenMic, bIsSearch, bIsOldData)
                Game.TeaRoomSystem.model.RedPointData.bApplyList = false
                Game.RedPointSystem:ClearNode("SocialChatRoomPermissionMicApply")
            end
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            if self.nSwitchTabIndex == 1 then
                self:UpdateListByType(self.EnumType.SPEAK_ORDER, bIsSearch, bIsOldData)
            elseif self.nSwitchTabIndex == 2 then
                self:UpdateListByType(self.EnumType.ApplyOpenMic, bIsSearch, bIsOldData)
                Game.TeaRoomSystem.model.RedPointData.bApplyList = false
                Game.RedPointSystem:ClearNode("SocialChatRoomPermissionMicApply")
            end
        end
    elseif nTabIndex == self.TabEnum.ForbidSpeech then
        self:UpdateListByType(self.EnumType.ForbidSpeech, bIsSearch, bIsOldData)
    elseif nTabIndex == self.TabEnum.BlackList then
        if self.nSwitchTabIndex == 1 then
            self:UpdateListByType(self.EnumType.OutBlackList, bIsSearch, bIsOldData)
        elseif self.nSwitchTabIndex == 2 then
            self:UpdateListByType(self.EnumType.InBlackList, bIsSearch, bIsOldData)
        end
    end
end

function ChatRoom_Permission_Panel:UpdateBlackList()
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.BlackList then
        if self.nSwitchTabIndex == 1 then
            self:UpdateListByType(self.EnumType.OutBlackList)
        elseif self.nSwitchTabIndex == 2 then
            self:UpdateListByType(self.EnumType.InBlackList)
        end
    end
end

function ChatRoom_Permission_Panel:UpdateMicApplyList()
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.MicManage then
        local nMicPermissionType = self:GetMicPermissionType()
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_FREE then
            return
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            if self.nSwitchTabIndex == 1 then
                return
            elseif self.nSwitchTabIndex == 2 then
                self:UpdateListByType(self.EnumType.ApplyOpenMic)
            end
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            if self.nSwitchTabIndex == 1 then
                return
            elseif self.nSwitchTabIndex == 2 then
                self:UpdateListByType(self.EnumType.ApplyOpenMic)
            end
        end
    end
end

function ChatRoom_Permission_Panel:UpdateGagList(strAvatarID, bIsForbidSpeak)
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.ForbidSpeech then
        if strAvatarID then
             for nIndex, MemberInfo in ipairs(self.Data) do
                if MemberInfo.AvatarID == strAvatarID then
                    MemberInfo.isForbidSpeak = bIsForbidSpeak
                    break
                end
            end
        end
        self:UpdateListByType(self.EnumType.ForbidSpeech, nil, true)
    end
end

function ChatRoom_Permission_Panel:UpdatePlayerOrderList()
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.MicManage then
        local nMicPermissionType = self:GetMicPermissionType()
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            self:UpdateInOrderPlayerList()
        end
    end
end

function ChatRoom_Permission_Panel:SetMicPermissionType(nMicPermissionType)
    if nMicPermissionType == nil then
        return
    end
    if nMicPermissionType < Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMinMicPermissionType
        or nMicPermissionType > Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMaxMicPermissionType then
            return
    end
    Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMicPermissionType = nMicPermissionType
end

function ChatRoom_Permission_Panel:SetTabIndex(nIndex)
    if nIndex == nil then
        return
    end
    if nIndex < self.TabEnum.MicManage or nIndex > self.TabEnum.BlackList then
        return
    end
    Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nTabIndex = nIndex
end

function ChatRoom_Permission_Panel:GetTabIndex()
    return Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nTabIndex
end

function ChatRoom_Permission_Panel:GetMicPermissionType()
    return Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMicPermissionType or Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_FREE
end

function ChatRoom_Permission_Panel:OnSpeechOrderChanged()
    self:UpdatePlayerList(nil, true)
    self:UpdatePlayerOrderList()
end

--region 玩家列表
function ChatRoom_Permission_Panel:UpdatePlayerListData()
    table.clear(self.Data)
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return
    end
    if (self.nType == self.EnumType.SPEAK_RIGHT or self.nType == self.EnumType.SPEAK_ORDER) or
        (self.nType == self.EnumType.ForbidSpeech or self.nType == self.EnumType.OutBlackList) then
        self.Data = DeepCopy(Game.TeaRoomSystem.model.roomInfoData.memberInfoList)
    elseif self.nType == self.EnumType.ApplyOpenMic then
        local nMicPermissionType = self:GetMicPermissionType()
        if Game.TeaRoomSystem.model.roomInfoData.tblOpenMicApplicationList ~= nil then
            if nMicPermissionType == Game.TeaRoomSystem.model.roomInfoData.nSpeechPrivilegeSetting then
                local tblMemberAvatarIDToIndex = {}
                for nIndex, MemberInfo in pairs(Game.TeaRoomSystem.model.roomInfoData.memberInfoList) do
                    local strAvatarID = MemberInfo.AvatarID
                    if strAvatarID ~= nil then
                        tblMemberAvatarIDToIndex[strAvatarID] = nIndex
                    end
                end
                for _, v in ipairs(Game.TeaRoomSystem.model.roomInfoData.tblOpenMicApplicationList) do
                    local strAvatarID = v.entityID
                    local strApplicationText = v.message
                    local nMemberIndex = tblMemberAvatarIDToIndex[strAvatarID]
                    local memberInfo = nMemberIndex and Game.TeaRoomSystem.model.roomInfoData.memberInfoList[nMemberIndex]
                    local bAccept = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApply[strAvatarID]
                    if memberInfo then
                        memberInfo.ApplicationText = strApplicationText
                        memberInfo.Accept = bAccept
                        table.insert(self.Data, memberInfo)
                    end
                end
            end
        else
            Game.TeaRoomSystem:ReqTeaRoomGetApplicationList()
        end
    elseif self.nType == self.EnumType.InBlackList then
        if not Game.TeaRoomSystem.model.roomInfoData.tblBlackList then
            Game.TeaRoomSystem:ReqTeaRoomGetBlackList()
        else
            self.Data = DeepCopy(Game.TeaRoomSystem.model.roomInfoData.tblBlackList)
        end
    end
    self:SortPlayerListData()
    self.WBP_ComInputSearchCom:SetSearchSource(self.Data, "Nickname")
end

function ChatRoom_Permission_Panel:SortPlayerListData()
    -- 对服务端/System未做排序的数据进行排序
    if self.nType == self.EnumType.SPEAK_RIGHT then
        table.sort(self.Data, function(MemberX, MemberY)
            local nValueX = Game.TeaRoomSystem:IsRoomMasterID(MemberX.AvatarID) and 2 or (self:MemberHasSpeechRight(MemberX) and 1 or 0)
            local nValueY = Game.TeaRoomSystem:IsRoomMasterID(MemberY.AvatarID) and 2 or (self:MemberHasSpeechRight(MemberY) and 1 or 0)
            if nValueX ~= nValueY then
                return nValueX > nValueY
            else
                nValueX = (MemberX.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                nValueY = (MemberY.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                if nValueX ~= nValueY then
                    return nValueX > nValueY
                else
                    return MemberX.version < MemberY.version
                end
            end
        end)
    elseif self.nType == self.EnumType.ApplyOpenMic then
        -- sort在服务器做了
    elseif self.nType == self.EnumType.SPEAK_ORDER then
        table.sort(self.Data, function(MemberX, MemberY)
            local nValueX = Game.TeaRoomSystem:IsRoomMasterID(MemberX.AvatarID) and 2 or (self:MemberHasSpeechOrder(MemberX) and 1 or 0)
            local nValueY = Game.TeaRoomSystem:IsRoomMasterID(MemberY.AvatarID) and 2 or (self:MemberHasSpeechOrder(MemberY) and 1 or 0)
            if nValueX ~= nValueY then
                return nValueX > nValueY
            else
                nValueX = (MemberX.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                nValueY = (MemberY.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                if nValueX ~= nValueY then
                    return nValueX > nValueY
                else
                    return MemberX.version < MemberY.version
                end
            end
        end)
    elseif self.nType == self.EnumType.ForbidSpeech then
        table.sort(self.Data, function(MemberX, MemberY)
            local nValueX = Game.TeaRoomSystem:IsRoomMasterID(MemberX.AvatarID) and 2 or (MemberX.isForbidSpeak and 1 or 0)
            local nValueY = Game.TeaRoomSystem:IsRoomMasterID(MemberY.AvatarID) and 2 or (MemberY.isForbidSpeak and 1 or 0)
            if nValueX ~= nValueY then
                return nValueX > nValueY
            else
                nValueX = (MemberX.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                nValueY = (MemberY.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                if nValueX ~= nValueY then
                    return nValueX > nValueY
                else
                    return MemberX.version < MemberY.version
                end
            end
        end)
    elseif self.nType == self.EnumType.OutBlackList then
        table.sort(self.Data, function(MemberX, MemberY)
            local nValueX = Game.TeaRoomSystem:IsRoomMasterID(MemberX.AvatarID) and 2 or 1
            local nValueY = Game.TeaRoomSystem:IsRoomMasterID(MemberY.AvatarID) and 2 or 1
            if nValueX ~= nValueY then
                return nValueX > nValueY
            else
                nValueX = (MemberX.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                nValueY = (MemberY.speechState == Enum.EVOICE_STATE.VOICE) and 1 or 0
                if nValueX ~= nValueY then
                    return nValueX > nValueY
                else
                    return MemberX.version < MemberY.version
                end
            end
        end)
    end
end

function ChatRoom_Permission_Panel:MemberHasSpeechRight(Member)
    local strAvatarID = Member.AvatarID
    if Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight[strAvatarID] == nil then
        return Member.speechRight
    else
        return Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight[strAvatarID]
    end
end

function ChatRoom_Permission_Panel:MemberHasSpeechOrder(Member)
    local strAvatarID = Member.AvatarID
    if Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder == nil then
        return Member.speechOrder ~= 0
    else
        for _, tmpAvatarID in ipairs(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder) do
            if tmpAvatarID == strAvatarID then
                return true
            end
        end
        return false
    end
end

function ChatRoom_Permission_Panel:SetMemberSpeechRight(Member, bOrderRight)
    local strAvatarID = Member.AvatarID
    if Member.speechRight == bOrderRight then
        Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight[strAvatarID] = nil
    else
        Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight[strAvatarID] = bOrderRight
    end
end

function ChatRoom_Permission_Panel:RefreshPlayerList(bIsOldData)
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.MicManage then
        local nMicPermissionType = self:GetMicPermissionType()
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            if self.nSwitchTabIndex == 1 then
                if bIsOldData then
                    self.TileView_MemberCom:RefreshItems()
                else
                    self.TileView_MemberCom:Refresh(self.Data)
                end
            elseif self.nSwitchTabIndex == 2 then
                if bIsOldData then
                    self.TileView_ApplyCom:RefreshItems()
                else
                    self.TileView_ApplyCom:Refresh(self.Data)
                end
            end
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            if self.nSwitchTabIndex == 1 then
                self.TileView_ChooseCom:SetSelectionMode(ESelectionMode.Multi)
                self.TileView_ChooseCom:SetMultiModeMaxSelectionMode(Enum.EListMultiModeMaxSelectionMode.CantSelect)
                self.TileView_ChooseCom:SetMultiModeMaxSelectionCount(8)
            end
            if bIsOldData then
                self.TileView_ChooseCom:RefreshItems()
            else
                self.TileView_ChooseCom:Refresh(self.Data)
            end
        end
    elseif nTabIndex == self.TabEnum.ForbidSpeech then
        if bIsOldData then
            self.TileView_MemberCom:RefreshItems()
        else
            self.TileView_MemberCom:Refresh(self.Data)
        end
    elseif nTabIndex == self.TabEnum.BlackList then
        if bIsOldData then
            self.TileView_MemberCom:RefreshItems()
        else
            self.TileView_MemberCom:Refresh(self.Data)
        end
    end
end

function ChatRoom_Permission_Panel:ClearListData()
    table.clear(self.Data)
end

function ChatRoom_Permission_Panel:UpdateListByType(nType, bIsSearch, bIsOldData)
    self:SetListType(nType)
    if not bIsSearch and not bIsOldData then
        self:UpdatePlayerListData()
    end
    self:RefreshPlayerList(bIsOldData)
end

function ChatRoom_Permission_Panel:SetListType(nType)
    if nType == nil then
        return
    end
    if nType < self.EnumType.None or nType > self.EnumType.InBlackList then
        return
    end
    self.nType = nType
end

function ChatRoom_Permission_Panel:IsPermission_SpeechOrderChanged()
    if not Game.TeaRoomSystem:IsPermission_SpeechOrderInit() then
        return false
    end
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return false
    end
    local tblMicOrder = {}
    for _, MemberInfo in ipairs(Game.TeaRoomSystem.model.roomInfoData.memberInfoList) do
        local nSpeechOrder = MemberInfo.speechOrder
        local strAvatarID = MemberInfo.AvatarID
        if nSpeechOrder ~= 0 then
            tblMicOrder[nSpeechOrder] = strAvatarID
        end
    end
    for i = 1, Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMaxSpeechOrder do
        if tblMicOrder[i] ~= Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder[i] then
            return true
        end
    end
    return false
end

function ChatRoom_Permission_Panel:Remove_SpeechOrder(tblPlayerData)
    if tblPlayerData == nil then
        return
    end
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return
    end
    local nRemoveAvatarID = tblPlayerData.AvatarID
    for nIndex, strAvatarID in ipairs(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder) do
        if strAvatarID == nRemoveAvatarID then
            table.remove(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder, nIndex)
            break
        end
    end
end

function ChatRoom_Permission_Panel:PushBack_SpeechOrder(tblPlayerData)
    if tblPlayerData == nil then
        return
    end
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return
    end
    local nPushBackAvatarID = tblPlayerData.AvatarID
    if #Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder >= Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMaxSpeechOrder then
        return
    end
    for _, AvatarID in ipairs(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder) do
        if AvatarID == nPushBackAvatarID then
            return
        end
    end
    table.insert(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder, nPushBackAvatarID)
end

function ChatRoom_Permission_Panel:IsMicRightOverSize()
    local nMicPermissionType = self:GetMicPermissionType()
    if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
        -- 判断上麦人数是否超标
        local nCount = 1
        for _, memberInfo in ipairs(Game.TeaRoomSystem.model.roomInfoData.memberInfoList) do
            local strAvatarID = memberInfo.AvatarID
            if memberInfo.speechRight == true
                or Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight[strAvatarID] == true then
                    nCount = nCount + 1
            end
            if nCount >= Enum.EChatRoomConstData.RoomSpeakersMaxNumber then
                return true
            end
        end
        
        if nCount >= Enum.EChatRoomConstData.RoomSpeakersMaxNumber then
            return true
        end
    elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
        if not Game.TeaRoomSystem:IsPermission_SpeechOrderInit() then
            Game.TeaRoomSystem:InitPermission_SpeechOrder()
        end
        local nCount = #Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder
        if nCount >= Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMaxSpeechOrder then
            return true
        end
    end
    return false
end

--endregion
function ChatRoom_Permission_Panel:UpdateInOrderPlayerListData()
    self.inOrderListData = {}
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return
    end
    self.inOrderListData = Game.TeaRoomSystem:GetInOrderPlayerListData()
end

function ChatRoom_Permission_Panel:UpdateInOrderPlayerList()
    self:UpdateInOrderPlayerListData()
    local nCellCount = Game.TeaRoomSystem.model.speechOrderMaxNum
    -- 空状态填充
    for i = #self.inOrderListData + 1, nCellCount do
        self.inOrderListData[i] = {}
    end
    self.TileView_OrderCom:Refresh(self.inOrderListData)
end
--endregion

--region 子项点击回调
function ChatRoom_Permission_Panel:Item_RightIconCallBack(tblPlayerData)
    if Game.TeaRoomSystem:IsRoomMasterID(tblPlayerData.AvatarID) then
        return
    end
    local strAvatarID = tblPlayerData.AvatarID
    if self.nType == self.EnumType.SPEAK_RIGHT then
        for k, v in pairs(Game.TeaRoomSystem.model.roomInfoData.memberInfoList) do
            if v.AvatarID == strAvatarID then
                local bOrderRight 
                if Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight[strAvatarID] == nil then
                    bOrderRight = not Game.TeaRoomSystem.model.roomInfoData.memberInfoList[k].speechRight
                else
                    bOrderRight = not Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight[strAvatarID]
                end
                if bOrderRight then
                    if self:IsMicRightOverSize() then
                        local nMaxNum
                        local nMicPermissionType = self:GetMicPermissionType()
                        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
                            nMaxNum = Enum.EChatRoomConstData.RoomSpeakersMaxNumber
                        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
                            nMaxNum = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMaxSpeechOrder
                        end
                        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_SPEAKER_LIMIT_EXCEED, {{tostring(nMaxNum)}})
                        return
                    end
                end
                self:SetMemberSpeechRight(tblPlayerData, bOrderRight)
                break
            end
        end
        -- self:UpdatePlayerListData()
        self:UpdatePlayerList(nil, true)
    elseif self.nType == self.EnumType.ForbidSpeech then
        if not Game.TeaRoomSystem:IsInTeaRoom() then
            return
        end
        if not tblPlayerData.isForbidSpeak then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CHATROOM_BAN_CONFIRM, function()
                Game.TeaRoomSystem:ReqTeaRoomPullGaglist(strAvatarID)
            end, nil, {tblPlayerData.Nickname})
        else
            Game.TeaRoomSystem:ReqTeaRoomCancelGaglist(strAvatarID)
        end
    elseif self.nType == self.EnumType.OutBlackList then
        local strPlayerName = tblPlayerData.Nickname
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CHATROOM_BLACKLIST_CONFIRM,
        function()
            Game.TeaRoomSystem:ReqTeaRoomPullBlacklist(strAvatarID)
        end,
        nil, {strPlayerName})
    elseif self.nType == self.EnumType.InBlackList then
        Game.TeaRoomSystem:ReqTeaRoomCancelBlacklist(strAvatarID)
    end
end

function ChatRoom_Permission_Panel:Item_CheckBoxCallBack(bIsChecked, tblPlayerData)
    if Game.TeaRoomSystem:IsRoomMasterID(tblPlayerData.AvatarID) then
        return
    end
    if self:IsMicRightOverSize() and bIsChecked then
        local nMaxNum
        local nMicPermissionType = self:GetMicPermissionType()
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            nMaxNum = Enum.EChatRoomConstData.RoomSpeakersMaxNumber
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            nMaxNum = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMaxSpeechOrder
        end
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_SPEAKER_LIMIT_EXCEED, {{tostring(nMaxNum)}})
    end
    if self.nType == self.EnumType.SPEAK_ORDER then
        if bIsChecked then
            self:PushBack_SpeechOrder(tblPlayerData)
        else
            self:Remove_SpeechOrder(tblPlayerData)
        end
        self:OnSpeechOrderChanged()
    end
end

function ChatRoom_Permission_Panel:Item_BtnAcceptCallBack(tblPlayerData, nIndex)
    if tblPlayerData == nil then
        return
    end
    if self.nType == self.EnumType.ApplyOpenMic then
        if not Game.TeaRoomSystem:IsInTeaRoom() then
            return
        end
        local nMicPermissionType = self:GetMicPermissionType()
        if self:IsMicRightOverSize() then        
            local nMaxNum
            if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
                nMaxNum = Enum.EChatRoomConstData.RoomSpeakersMaxNumber
            elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
                nMaxNum = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.nMaxSpeechOrder
            end
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_SPEAKER_LIMIT_EXCEED, {{tostring(nMaxNum)}})
            return
        end
        if Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApplyCount >= Enum.EChatRoomConstData.RoomSpeakersOperateMax then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_APPLY_LIST_OPERATE_NUM_MAX, {{tostring(Enum.EChatRoomConstData.RoomSpeakersOperateMax)}})
            return
        end
        local strAvatarID = tblPlayerData.AvatarID
        self.Data[nIndex].Accept = true
        Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApply[strAvatarID] = true
        Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApplyCount = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApplyCount + 1
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            self.TileView_ApplyCom:RefreshItemByIndex(nIndex)
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            self.TileView_ChooseCom:RefreshItemByIndex(nIndex)
        end
        
        for _, MemberInfo in pairs(Game.TeaRoomSystem.model.roomInfoData.memberInfoList) do
            if MemberInfo.AvatarID == strAvatarID then
                if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
                    if MemberInfo.speechRight == false then
                        self:SetMemberSpeechRight(tblPlayerData, true)
                    end
                elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
                    if MemberInfo.speechOrder == 0 then
                        self:PushBack_SpeechOrder(tblPlayerData)
                        Game.TeaRoomSystem:OnMemberOrderChanged()
                    end
                end
                break;
            end
        end
    end
end

function ChatRoom_Permission_Panel:Item_BtnRejectCallBack(tblPlayerData, nIndex)
    if tblPlayerData == nil then
        return
    end
    if self.nType == self.EnumType.ApplyOpenMic then
        if not Game.TeaRoomSystem:IsInTeaRoom() then
            return
        end
        local nMicPermissionType = self:GetMicPermissionType()
        if Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApplyCount >= Enum.EChatRoomConstData.RoomSpeakersOperateMax then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_APPLY_LIST_OPERATE_NUM_MAX, {{tostring(Enum.EChatRoomConstData.RoomSpeakersOperateMax)}})
            return
        end
        local strAvatarID = tblPlayerData.AvatarID
        self.Data[nIndex].Accept = false
        Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApply[strAvatarID] = false
        Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApplyCount = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApplyCount + 1
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            self.TileView_ApplyCom:RefreshItemByIndex(nIndex)
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            self.TileView_ChooseCom:RefreshItemByIndex(nIndex)
        end
    end
end
--endregion

--region 事件回调
--- 此处为自动生成
function ChatRoom_Permission_Panel:on_Btn_ClickArea_Clicked()
    Game.TipsSystem:ShowTips(Enum.ETipsData.CHATROOM_ORDER_MIC_GUIDE, self.view.Canvas_TipBtn:GetCachedGeometry())
end

--- 此处为自动生成
function ChatRoom_Permission_Panel:on_WBP_ComBtnCom_ClickEvent()
    local nTabIndex = self:GetTabIndex()
    if nTabIndex == self.TabEnum.MicManage then
        local nMicPermissionType = self:GetMicPermissionType()
        if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_FREE then
            Game.TeaRoomSystem:ReqSetTeaRoomSpeechFree()
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_RIGHT then
            local tblMemberChange = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight or {}
            Game.TeaRoomSystem:ReqSetTeaRoomMemberPrivilege(tblMemberChange)
        elseif nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
            local tblMemberChange = Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicOrder or {}
            Game.TeaRoomSystem:ReqSetTeaRoomMemberOrder(tblMemberChange)
        end
    end
    self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function ChatRoom_Permission_Panel:on_WBP_ComTabHorizontalCom_ItemSelected(index, data)
    self.nSwitchTabIndex = index
    self:UpdateUIStyle()
    self:UpdatePlayerList()
    self:UpdatePlayerOrderList()
    self.WBP_ComInputSearchCom:on_Btn_Clear_Clicked()
end

--- 此处为自动生成
---@return bool
function ChatRoom_Permission_Panel:on_WBP_ComBackTitleCom_PreCloseEvent()
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return true
    end
    local nMicPermissionType = self:GetMicPermissionType()
    if nMicPermissionType ~= Game.TeaRoomSystem.model.roomInfoData.nSpeechPrivilegeSetting or 
        next(Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblMicRight) or 
        self:IsPermission_SpeechOrderChanged() or
        Game.TeaRoomSystem.model.chatRoom_RoomPermissionData.tblApplyCount ~= 0 then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CHATROOM_PERMISSION_NOT_SAVE_CONFIRM,
            function()
                Game.TeaRoomSystem:ClearPermissionWnd_MemberChangeTable()
                self:CloseSelf()
            end)
    else
        return true
    end
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function ChatRoom_Permission_Panel:on_WBP_ComSelectDropType1Com_ItemSelected(index, data)
    if Game.TeaRoomSystem:IsRoomMasterID(Game.me.eid) then
        -- 是房主, 修改数据需要缓存
        self:SetMicPermissionType(index)
        self:UpdateUIStyle()
        self:UpdateTabHorData()
    else
        -- 不是房主
        self:UpdateWBP_Permission_ComboBox()
    end
    if self:GetTabIndex() == self.TabEnum.MicManage then
        local nVisible = index == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER and ESlateVisibility.Visible or ESlateVisibility.Collapsed
        self.view.Canvas_TipBtn:SetVisibility(nVisible)
    end
end
--endregion

--- 此处为自动生成
---@param searchResults table
function ChatRoom_Permission_Panel:on_WBP_ComInputSearchCom_SearchResult(searchResults)
    self.Data = searchResults
    self:UpdatePlayerList(true)
end


--- 此处为自动生成
---@param index number
---@param data table
function ChatRoom_Permission_Panel:on_WBP_ComTabListMainCom_ItemSelected(index, data)
    self:SetTabIndex(index)
    if index == self.TabEnum.ForbidSpeech then
        self:UpdatePlayerList()
        self.view.Text_Info:SetText(StringConst.Get("CHATROOM_BAN_TIPS"))
    elseif index == self.TabEnum.BlackList then
        self:UpdatePlayerList()
        self.view.Text_Info:SetText(StringConst.Get("CHATROOM_BLACKLIST_TIPS"))
    end
    self.WBP_ComInputSearchCom:on_Btn_Clear_Clicked()
    self:UpdateWBP_Permission_ComboBox()
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function ChatRoom_Permission_Panel:on_WBP_ComTabListMainCom_GetEntryLuaClass(index)
    return kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComTabItem")
end

--- 此处为自动生成
---@param index number
---@return bool
function ChatRoom_Permission_Panel:on_WBP_ComSelectDropType1Com_CheckItemCanSelect(index)
    if Game.TeaRoomSystem:CheckIsPartyRoom() and (index ~= self:GetMicPermissionType()) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_PARTY_MODE_ADJUST_FAIL)
        return false
    end
    return true
end

return ChatRoom_Permission_Panel
