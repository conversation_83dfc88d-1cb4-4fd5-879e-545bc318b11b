local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIComInputBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComInputBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatRoom_VoiceApplyPopup_Panel : UIPanel
---@field view ChatRoom_VoiceApplyPopup_PanelBlueprint
local ChatRoom_VoiceApplyPopup_Panel = DefineClass("ChatRoom_VoiceApplyPopup_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ChatRoom_VoiceApplyPopup_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_VoiceApplyPopup_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_VoiceApplyPopup_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_VoiceApplyPopup_Panel:InitUIComponent()
    ---@type UIComBoxFrame
    self.WBP_ComPopupSCom = self:CreateComponent(self.view.WBP_ComPopupS, UIComBoxFrame)
    ---@type UIComInputBox
    self.WBP_ComInputBigCom = self:CreateComponent(self.view.WBP_ComInputBig, UIComInputBox)
    ---@type UIComButton
    self.WBP_ComBtn_RefuseCom = self:CreateComponent(self.view.WBP_ComBtn_Refuse, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_AcceptCom = self:CreateComponent(self.view.WBP_ComBtn_Accept, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_VoiceApplyPopup_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_AcceptCom.onClickEvent, "on_WBP_ComBtn_AcceptCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_RefuseCom.onClickEvent, "on_WBP_ComBtn_RefuseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_VoiceApplyPopup_Panel:InitUIView()
    self.WBP_ComInputBigCom:Refresh("", StringConst.Get("SOCIAL_CHATROOM_APPLY_HINTTEXT"), Enum.EChatRoomConstData.RoomMicApplyMaxLength)
    self.WBP_ComBtn_AcceptCom:Refresh(StringConst.Get("SOCIAL_CHATROOM_APPLY_BTNNAME"))
    self.WBP_ComBtn_RefuseCom:Refresh(StringConst.Get("CANCLE"))
    self.WBP_ComPopupSCom:Refresh(StringConst.Get("SOCIAL_CHATROOM_APPLY_TITLETEXT"))
end

---面板打开的时候触发
function ChatRoom_VoiceApplyPopup_Panel:OnRefresh(...)
end


--- 此处为自动生成
function ChatRoom_VoiceApplyPopup_Panel:on_WBP_ComBtn_AcceptCom_ClickEvent()
    local strInputText = self.WBP_ComInputBigCom:GetText()
    Game.AllInSdkManager:IsSensitiveWords(strInputText, function(result)
        if result then
            -- 是敏感词
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHATROOM_APPLY_SENSITIVE)
        else
            -- 不是敏感词
            Game.TeaRoomSystem:ReqTeaRoomApplyOpenMic(strInputText)
            if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.ChatRoom_VoiceApplyPopup_Panel) then
                Game.NewUIManager:ClosePanel(UIPanelConfig.ChatRoom_VoiceApplyPopup_Panel)
            end
        end
    end, nil)
end

--- 此处为自动生成
function ChatRoom_VoiceApplyPopup_Panel:on_WBP_ComBtn_RefuseCom_ClickEvent()
    self:CloseSelf()
end

return ChatRoom_VoiceApplyPopup_Panel
