local ChatRoom_PermissionTag = kg_require("Gameplay.LogicSystem.Chat.ChatRoom.Permisson_Panel.ChatRoom_PermissionTag")
local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatRoom_Room_Item : UIListItem
---@field view ChatRoom_Room_ItemBlueprint
local ChatRoom_Room_Item = DefineClass("ChatRoom_Room_Item", UIListItem)
local Const = kg_require("Shared.Const")
local ESlateVisibility = import("ESlateVisibility")

ChatRoom_Room_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_Room_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_Room_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_Room_Item:InitUIComponent()
    ---@type ChatRoom_PermissionTag
    self.WBP_TagNameCom = self:CreateComponent(self.view.WBP_TagName, ChatRoom_PermissionTag)
    ---@type ChatRoom_PermissionTag
    self.WBP_TypeNameCom = self:CreateComponent(self.view.WBP_TypeName, ChatRoom_PermissionTag)
    ---@type UIComCheckBox
    self.WBP_ComCheckBoxCom = self:CreateComponent(self.view.WBP_ComCheckBox, UIComCheckBox)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_Room_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_Room_Item:InitUIView()
    self.WBP_ComCheckBoxCom:Refresh("", strue, false)
    self.WBP_ComCheckBoxCom:SetHasText(false)
end

---面板打开的时候触发
function ChatRoom_Room_Item:OnRefresh(roomData)
    if not roomData then return end
    self.view.Text_RoomName:SetText(roomData.Name or "")
    local strServerName = Game.MomentsSystem:GetUrlTable()[tonumber(roomData.ServerName)]
    self.view.Text_Server:SetText(strServerName or "")
    self.view.Text_PeopleNumber:SetText(tostring(roomData.PlayerCount))
    self:SetRoomType(roomData.RoomType)
    self:SetTeaRoomTag(roomData.Tag, roomData.ID)
    self.userWidget:BP_SetMaskType(roomData.IsSelected or false)
end

function ChatRoom_Room_Item:SetRoomType(nRoomType)
    local strRoomType = Game.TeaRoomSystem:GetTeaRoomTypeName(nRoomType)
    if strRoomType then
        self.WBP_TypeNameCom:Refresh(strRoomType, nRoomType)
    end
end

function ChatRoom_Room_Item:SetTeaRoomTag(nRoomTag, nRoomID)
    local strTeaRoomTagName = self:GetTeaRoomTagName(nRoomTag)
    if strTeaRoomTagName == "" then
        self.view.WBP_TagName:SetVisibility(ESlateVisibility.Hidden)
    else
        self.WBP_TagNameCom:Refresh(strTeaRoomTagName, nRoomTag and nRoomTag + 1)
        self.view.WBP_TagName:SetVisibility(ESlateVisibility.Visible)
    end
    if Game.TeaRoomSystem:IsCurRoomID(nRoomID) then
        self.view.Img_LocationIcon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Permanent:SetVisibility(ESlateVisibility.Collapsed)
    elseif nRoomTag == Const.TEAROOM_TAG.PERSISTENT then
        self.view.Text_Permanent:SetText(Game.TableData.GetChatRoomStringConstDataRow("CHATROOM_PERMANENT_ROOM_BUTTON").StringValue)
        self.view.Text_Permanent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Img_LocationIcon:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.Text_Permanent:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Img_LocationIcon:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function ChatRoom_Room_Item:SetIsMaskType(bIsMaskType)
    self.userWidget:BP_SetMaskType(bIsMaskType)
end

function ChatRoom_Room_Item:GetTeaRoomTagName(nRoomTag)
    return Const.TEAROOM_TAG_TO_NAME[nRoomTag] or Const.TEAROOM_TAG_TO_NAME[Const.TEAROOM_TAG.NONE]
end

--- 此处为自动生成
function ChatRoom_Room_Item:on_Btn_ClickArea_Clicked()
    UIListItem.OnClickBtnInternal(self)
end

return ChatRoom_Room_Item
