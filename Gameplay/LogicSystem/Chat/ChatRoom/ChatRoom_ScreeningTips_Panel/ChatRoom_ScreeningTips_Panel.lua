local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatRoom_ScreeningTips_Panel : UIPanel
---@field view ChatRoom_ScreeningTips_PanelBlueprint
local ChatRoom_ScreeningTips_Panel = DefineClass("ChatRoom_ScreeningTips_Panel", UIPanel)

ChatRoom_ScreeningTips_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_ScreeningTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_ScreeningTips_Panel:InitUIData()
    self.selectionsData = {}
end

--- UI组件初始化，此处为自动生成
function ChatRoom_ScreeningTips_Panel:InitUIComponent()
    ---@type UIListView
    self.KGTileView_CheckBoxCom = self:CreateComponent(self.view.KGTileView_CheckBox, UIListView)
    ---@type UIComButton
    self.WBP_CancelBtnCom = self:CreateComponent(self.view.WBP_CancelBtn, UIComButton)
    ---@type UIComButton
    self.WBP_SelectBtnCom = self:CreateComponent(self.view.WBP_SelectBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_ScreeningTips_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_CancelBtnCom.onClickEvent, "on_WBP_CancelBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_SelectBtnCom.onClickEvent, "on_WBP_SelectBtnCom_ClickEvent")
    self:AddUIEvent(self.KGTileView_CheckBoxCom.onGetEntryLuaClass, "on_KGTileView_CheckBoxCom_GetEntryLuaClass")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_ScreeningTips_Panel:InitUIView()
    self.WBP_CancelBtnCom:Refresh("取消")
    self.WBP_SelectBtnCom:Refresh("筛选")
end

---面板打开的时候触发
function ChatRoom_ScreeningTips_Panel:OnRefresh(positionX, positionY, roomType)
    self.view.KGSmartPositioningArea.Slot:SetPosition(FVector2D(positionX, positionY))
    if Game.TeaRoomSystem.model.chatRoomTypeData[roomType] then
        for k,data in pairs(Game.TeaRoomSystem.model.chatRoomTypeData[roomType]) do
            table.insert(self.selectionsData, data)
        end
    end
    table.sort(self.selectionsData, function(a, b)
        return a.ID < b.ID
    end)
    self.KGTileView_CheckBoxCom:Refresh(self.selectionsData)
end


--- 此处为自动生成
function ChatRoom_ScreeningTips_Panel:on_WBP_CancelBtnCom_ClickEvent()
    self:CloseSelf()
end

--- 此处为自动生成
function ChatRoom_ScreeningTips_Panel:on_WBP_SelectBtnCom_ClickEvent()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_CHATROOM_REFRESH_ROOM_LIST)
    self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function ChatRoom_ScreeningTips_Panel:on_KGTileView_CheckBoxCom_GetEntryLuaClass(index)
    return kg_require("Gameplay.LogicSystem.Chat.ChatRoom.ChatRoom_ScreeningTips_Panel.ChatRoom_CheckBox_Item")
end

return ChatRoom_ScreeningTips_Panel
