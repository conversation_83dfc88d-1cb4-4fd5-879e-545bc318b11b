local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatRoom_VoiceTips_Item : UIListItem
---@field view ChatRoom_VoiceTips_ItemBlueprint
local ChatRoom_VoiceTips_Item = DefineClass("ChatRoom_VoiceTips_Item", UIListItem)
local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")

ChatRoom_VoiceTips_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_VoiceTips_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_VoiceTips_Item:InitUIData()
    ---按钮点击事件
    ---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function ChatRoom_VoiceTips_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_VoiceTips_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_VoiceTips_Item:InitUIView()
end

---面板打开的时候触发
function ChatRoom_VoiceTips_Item:OnRefresh(data)
    if data == nil then
        return
    end
    local bIsSelected = (data.nVoiceState ~= nil) and data.nVoiceState == data.nCurVoiceState
    self.view.Text_Name:SetText(data.strName or "")
    self:SetImage(self.view.Img_Icon, data.Icon)
end

--- 此处为自动生成
function ChatRoom_VoiceTips_Item:on_Btn_ClickArea_Clicked()
    self.onClickEvent:Execute()
end

return ChatRoom_VoiceTips_Item
