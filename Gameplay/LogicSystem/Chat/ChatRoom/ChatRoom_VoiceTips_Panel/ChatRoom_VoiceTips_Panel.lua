local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatRoom_VoiceTips_Panel : UIPanel
---@field view ChatRoom_VoiceTips_PanelBlueprint
local ChatRoom_VoiceTips_Panel = DefineClass("ChatRoom_VoiceTips_Panel", UIPanel)
local Const = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ChatRoom_VoiceTips_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatRoom_VoiceTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatRoom_VoiceTips_Panel:InitUIData()
    --- @type bool 界面是否在茶壶房间内打开
    self.bInTeaRoom = false
    --- @type int 当前界面列表选中控件
    self.nVoiceState = Enum.EVOICE_STATE.LISTEN
    --- @type table ListData
    self.VoiceStateListData = {}
end

--- UI组件初始化，此处为自动生成
function ChatRoom_VoiceTips_Panel:InitUIComponent()
    ---@type UIComButton
    self.Btn_InfoCom = self:CreateComponent(self.view.Btn_Info, UIComButton)
    ---@type UIListView
    self.VoiceStateListCom = self:CreateComponent(self.view.VoiceStateList, UIListView)
    ---@type UIComCheckBox
    self.WBP_ComCheckBoxCom = self:CreateComponent(self.view.WBP_ComCheckBox, UIComCheckBox)
end

---UI事件在这里注册，此处为自动生成
function ChatRoom_VoiceTips_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComCheckBoxCom.onCheckChanged, "on_WBP_ComCheckBoxCom_CheckChanged")
    self:AddUIEvent(self.VoiceStateListCom.onItemSelected, "on_VoiceStateListCom_ItemSelected")
    self:AddUIEvent(self.Btn_InfoCom.onClickEvent, "on_Btn_InfoCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatRoom_VoiceTips_Panel:InitUIView()
    self.view.TipsSelectionText:SetText(StringConst.Get("SOCIAL_CHATROOM_DENOISE_TEXT"))
    self.WBP_ComCheckBoxCom:SetHasText(false)
end

---面板打开的时候触发
function ChatRoom_VoiceTips_Panel:OnRefresh(Params, bInTeaRoom, nVoiceState, bIsBottom)
    if Game.TeaRoomSystem:IsInTeaRoom() then
        self.WBP_ComCheckBoxCom:SetChecked(Game.TeaRoomSystem.model.roomInfoData.bCloseDownNoice)
    end
    self.Pos = Params
    self.bInTeaRoom = bInTeaRoom
    self.nVoiceState = nVoiceState
    self.VoiceStateListData = {{
            nIndex = 1,
            strName = "WBP_Listen",
            nVoiceState = Enum.EVOICE_STATE.LISTEN,
            nCurVoiceState = nVoiceState,
            icon = UIAssetPath.UI_HUD_Com_Icon_Voice_Sprite,
            name = StringConst.Get("TEAM_VOICE_LISTEN"),
            CallBackFunc = self.OnClick_VoiceStateList_CallBack,
        },{
            nIndex = 2,
            strName = "WBP_Mute",
            nVoiceState = Enum.EVOICE_STATE.REFUSE,
            nCurVoiceState = nVoiceState,
            icon = UIAssetPath.UI_HUD_Com_Icon_Nay2Hear_Sprite,
            name = StringConst.Get("TEAM_VOICE_REFUSE"),
            CallBackFunc = self.OnClick_VoiceStateList_CallBack,
        },{
            nIndex = 3,
            strName = "WBP_OpenMic",
            nVoiceState = Enum.EVOICE_STATE.VOICE,
            nCurVoiceState = nVoiceState,
            icon = UIAssetPath.UI_HUD_Com_Icon_OpenMic_Sprite,
            name = StringConst.Get("TEAM_VOICE_OPENMIC"),
            CallBackFunc = self.OnClick_VoiceStateList_CallBack,
        },}

    local bShowOffMic = false
    -- local nDeltaX = 0
    -- local nDeltaY = 0

    if self.bInTeaRoom == true then
        bShowOffMic = self:IsTeaRoomShowOffMic()
    end

    -- OffMic Wnd
    if bShowOffMic then
        table.insert(self.VoiceStateListData, {
            nIndex = 4,
            strName = "WBP_OffMic",
            nVoiceState = nil,
            nCurVoiceState = nVoiceState,
            iconPath = UIAssetPath.UI_HUD_Com_Icon_VoiceOff_Sprite,
            name = StringConst.Get("SOCIAL_CHATROOM_MICOFF_TEXT"),
            CallBackFunc = self.OnClick_VoiceStateList_CallBack,
        })
    end
    self:UpdateVoiceStateList()

    -- if bIsBottom then
    --     nDeltaX = - 250 / 2
    --     nDeltaY = - nHeight
    --     self.view.Img_Line:SetVisibility(ESlateVisibility.Hidden)
    --     self.view.Img_Point:SetVisibility(ESlateVisibility.Hidden)
    -- else
    --     nDeltaY = - nHeight / 2
    --     self.view.Img_Line:SetVisibility(ESlateVisibility.Visible)
    --     self.view.Img_Point:SetVisibility(ESlateVisibility.Visible)
    -- end
    -- self.Pos.X = self.Pos.X + nDeltaX
    -- self.Pos.Y = self.Pos.Y + nDeltaY
    self.Pos.X = self.Pos.X - 250 / 2
    self.view.KGSmartPositioningArea.Slot:SetPosition(self.Pos)
end

function ChatRoom_VoiceTips_Panel:IsTeaRoomShowOffMic()
    if not self.bInTeaRoom then
        return false
    end
    local nMicPermissionType = Game.TeaRoomSystem.model.roomInfoData and Game.TeaRoomSystem.model.roomInfoData.nSpeechPrivilegeSetting
    if nMicPermissionType == Const.TEAROOM_OPEM_MIC_PERMISSION.SPEAK_ORDER then
        local memberInfo = Game.TeaRoomSystem:GetRoomMemberInfo(Game.me.eid)
        if memberInfo and memberInfo.speechOrder == 1 then
            return true
        end
    end
    return false
end

function ChatRoom_VoiceTips_Panel:UpdateVoiceStateList()
    self.VoiceStateListCom:Refresh(self.VoiceStateListData)
    self.VoiceStateListCom:SetSelectedItemByIndex(self.nVoiceState, true, true)
end

function ChatRoom_VoiceTips_Panel:OnClick_WBP_Listen()
    if self.bInTeaRoom then
        Game.TeaRoomSystem:ChangeSelfVoiceState(Enum.EVOICE_STATE.LISTEN)
    else 
        local bInGroup = Game.TeamSystem:IsInGroup()
        local bInTeam = Game.TeamSystem:IsInTeam()
        if bInGroup then
            if Game and Game.me then
                Game.me:ChangeVoiceState(Enum.EVOICE_STATE.LISTEN)
            end
        elseif bInTeam then
            if Game and Game.me then
                Game.me:ReqTeamVoiceStateReport(Enum.EVOICE_STATE.LISTEN)
            end
        end
    end
    self:CloseSelf()
end

function ChatRoom_VoiceTips_Panel:OnClick_WBP_Mute()
    if self.bInTeaRoom then
        Game.TeaRoomSystem:ChangeSelfVoiceState(Enum.EVOICE_STATE.REFUSE)
    else 
        local bInGroup = Game.TeamSystem:IsInGroup()
        local bInTeam = Game.TeamSystem:IsInTeam()
        if bInGroup then
            if Game and Game.me then
                Game.me:ChangeVoiceState(Enum.EVOICE_STATE.REFUSE)
            end
        elseif bInTeam then
            if Game and Game.me then
                Game.me:ReqTeamVoiceStateReport(Enum.EVOICE_STATE.REFUSE)
            end
        end
    end
    self:CloseSelf()
end

function ChatRoom_VoiceTips_Panel:OnClick_WBP_OpenMic()
    if self.bInTeaRoom then
        Game.TeaRoomSystem:ChangeSelfVoiceState(Enum.EVOICE_STATE.VOICE)
    else 
        local bInGroup = Game.TeamSystem:IsInGroup()
        local bInTeam = Game.TeamSystem:IsInTeam()
        if bInGroup then
            if Game and Game.me then
                Game.me:ChangeVoiceState(Enum.EVOICE_STATE.VOICE)
            end
        elseif bInTeam then
            if Game and Game.me then
                Game.me:ReqTeamVoiceStateReport(Enum.EVOICE_STATE.VOICE)
            end
        end
    end
    self:CloseSelf()
end

function ChatRoom_VoiceTips_Panel:OnClick_WBP_OffMic()
    if self:IsTeaRoomShowOffMic() then
        Game.TeaRoomSystem:ReqTeaRoomMemberCloseMic()
    end
    self:CloseSelf()
end

--- 此处为自动生成
function ChatRoom_VoiceTips_Panel:on_Btn_InfoCom_ClickEvent()
    local TipsID = Enum.ETipsData["CHATROOM_AMBIENT_SOUND"]
    Game.TipsSystem:ShowTips(TipsID, self.view.Btn_Info:GetCachedGeometry())
end

--- 此处为自动生成
---@param checked bool
function ChatRoom_VoiceTips_Panel:on_WBP_ComCheckBoxCom_CheckChanged(checked)
    if not Game.TeaRoomSystem:IsInTeaRoom() then
        return
    end
    Game.TeaRoomSystem.model.roomInfoData.bCloseDownNoice = checked
    Game.TeaRoomSystem:UpdateSelfDownNoice()
end

--- 此处为自动生成
---@param index number
---@param data table
function ChatRoom_VoiceTips_Panel:on_VoiceStateListCom_ItemSelected(index, data)
    if data == nil then
        return
    end
    local strName = data.strName
    local callBackFunc = self["OnClick_" .. strName]
    if callBackFunc then
        callBackFunc(self, true)
    end
end

return ChatRoom_VoiceTips_Panel
