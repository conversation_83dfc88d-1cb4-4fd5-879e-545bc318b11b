local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatBubble_TeamRecruitL : UIComponent
---@field view ChatBubble_TeamRecruitLBlueprint
local ChatBubble_TeamRecruitL = DefineClass("ChatBubble_TeamRecruitL", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local const = kg_require("Shared.Const")
local P_Head = kg_require("Gameplay.LogicSystem.Character.P_Head2")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ChatBubble_TeamRecruitL.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatBubble_TeamRecruitL:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatBubble_TeamRecruitL:InitUIData()
    ---@type table 气泡参数
    self.Params = nil
    self.JobData = {
        [1] = 
            {
                Type = 0,
                Text = StringConst.Get("CHAT_TEAMRECRUIT_ATTACK"),
                Widget = self.view.WBP_Attack
            },
        [2] = 
            {
                Type = 1,
                Text = StringConst.Get("CHAT_TEAMRECRUIT_DEFEND"),
                Widget = self.view.WBP_Defend
            },
        [3] = 
            {
                Type = 2,
                Text = StringConst.Get("CHAT_TEAMRECRUIT_CURE"),
                Widget = self.view.WBP_Cure
            },
    }
end

--- UI组件初始化，此处为自动生成
function ChatBubble_TeamRecruitL:InitUIComponent()
    ---@type UIComButton
    self.RecruitAddBtnCom = self:CreateComponent(self.view.RecruitAddBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function ChatBubble_TeamRecruitL:InitUIEvent()
    self:AddUIEvent(self.RecruitAddBtnCom.onClickEvent, "on_RecruitAddBtnCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatBubble_TeamRecruitL:InitUIView()
    ---@type table 头像1
    self.P_Head1 = self:CreateComponent(self.view.TeamHead1, P_Head)
    ---@type table 头像2
    self.P_Head2 = self:CreateComponent(self.view.TeamHead2, P_Head)
    ---@type table 头像3
    self.P_Head3 = self:CreateComponent(self.view.TeamHead3, P_Head)
    ---@type table 头像4
    self.P_Head4 = self:CreateComponent(self.view.TeamHead4, P_Head)
    ---@type table 头像5
    self.P_Head5 = self:CreateComponent(self.view.TeamHead5, P_Head)
    ---@type table 头像6
    self.P_Head6 = self:CreateComponent(self.view.TeamHead6, P_Head)
    ---@type table 头像列表
    self.P_HeadList = { self.P_Head1, self.P_Head2, self.P_Head3, self.P_Head4, self.P_Head5, self.P_Head6 }
    self.RecruitAddBtnCom:SetName(StringConst.Get("TEAM_APPLY_JOINTEAM"))
    
    self.HasAddRecruitEvent = false
end

---组件刷新统一入口
function ChatBubble_TeamRecruitL:Refresh(params)
    self.Params = params
    self.userWidget:SetDirection(self.Params.bIsSelf)
    local OldOffsets = self.userWidget.Slot:GetOffsets() 
    if self.Params.bIsSelf then
        OldOffsets.Left  = 40
        OldOffsets.Right  = -40
        OldOffsets.Top  = 0
        OldOffsets.Bottom = 0
        self.userWidget.Slot:SetOffsets(OldOffsets)
    else
        OldOffsets.Left  = -40
        OldOffsets.Right  = 40
        OldOffsets.Top  = 0
        OldOffsets.Bottom = 0
        self.userWidget.Slot:SetOffsets(OldOffsets)
    end
    if self.Params.functionType == Enum.ChatFunctionType.TEAM_RECRUIT then
        self.bTeam = true
        self.bRescue = self.Params.chatArgs.teamRecruitInfo .bInSeekRescue 
    else
        self.bTeam = false
        self.bRescue = self.Params.chatArgs.groupRecruitInfo.bInSeekRescue 
    end
    self.userWidget:Event_UI_State(self.bRescue, self.bTeam)
    if self.bTeam then
        self:RefreshTeamUI()
        if self.bRescue then
            self.RecruitAddBtnCom:SetName(StringConst.Get("CHAT_TEAMRECRUIT_HELP"))
        else
            self.RecruitAddBtnCom:SetName(StringConst.Get("CHAT_APPLY_TEAM"))
        end
    else
        self:RefreshGroupUI()
        if self.bRescue then
            self.RecruitAddBtnCom:SetName(StringConst.Get("CHAT_TEAMRECRUIT_HELP"))
        else
            self.RecruitAddBtnCom:SetName(StringConst.Get("CHAT_APPLY_GROUP"))
        end
    end
    self.view.RecruitAddBtn:SetVisibility(ESlateVisibility.Visible)
    self.view.Text_HasInvited:SetVisibility(ESlateVisibility.Collapsed)
end

function ChatBubble_TeamRecruitL:RefreshTeamUI()
    local teamInfo = self.Params.chatArgs.teamRecruitInfo
    self.view.Text_HasInvited:SetVisibility(ESlateVisibility.Hidden)
    self.view.RecruitAddBtn:SetVisibility(ESlateVisibility.Visible)
    local PureText = string.split(self.Params.messageText, "<")[1]
    local PosText = string.sub(self.Params.messageText, #PureText + 1)
    if PosText then
        self.view.HB_HelpClass:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        for key, value in pairs(self.JobData) do
            if string.contains(PosText, value.Text) then
                value.Widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                value.Widget.Text_Describe:SetText(value.Text)
                value.Widget:Event_UI_State(value.Type)
            else
                value.Widget:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
    else
        self.view.HB_HelpClass:SetVisibility(ESlateVisibility.Hidden)
    end
    self:RefreshTextDescribeUI(PureText)
    local teamMemberinfo = teamInfo.memberInfos
    local teamMemberNum = 0
    local NewTeamInfo = {}
    for _, v in pairs(teamMemberinfo) do
        table.insert(NewTeamInfo, v)
        teamMemberNum = teamMemberNum + 1
    end
    table.sort(NewTeamInfo, function(a, b)
        if a.isCaptain ~= b.isCaptain then
            return a.isCaptain
        end
        return a.enterTime < b.enterTime
    end)
    local textGoal = nil
    if teamInfo.details.targetID == 0 then
        textGoal = StringConst.Get("TEAM_NOTARGET")
    else
        local TargetTableData = Game.TableData.GetTargetDataRow(teamInfo.details.targetID)
        textGoal = TargetTableData.Name
    end
    
    local textGoalShow = string.format("%s (%s/%s)",textGoal, teamMemberNum, Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") )
    self.view.Text_Goal:SetText(textGoalShow)
    for i = 1, Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") do
        if NewTeamInfo[i] then
            self.P_HeadList[i]:Refresh(
                {
                    ProfessionID = NewTeamInfo[i].profession,
                    bIsCaptain = NewTeamInfo[i].isCaptain
                }
            )
        else
            self.P_HeadList[i]:Refresh(
                {
                    Level = 0,
                    ProfessionID = 0,
                    bIsCaptain = false
                })
        end
    end
end

function ChatBubble_TeamRecruitL:RefreshGroupUI()
    local groupInfo = self.Params.chatArgs.groupRecruitInfo
    local PureText = string.split(self.Params.messageText, "<")[1]
    local PosText = string.sub(self.Params.messageText, #PureText + 1)
    local bHasPos = false
    if PosText then
        for key, value in pairs(self.JobData) do
            if string.contains(PosText, value.Text) then
                bHasPos = true
                value.Widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                value.Widget.Text_Describe:SetText(value.Text)
                value.Widget:Event_UI_State(value.Type)
            else
                value.Widget:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
    end
    if bHasPos then
        self.view.HB_HelpClass:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.HB_HelpClass:SetVisibility(ESlateVisibility.Hidden)
    end
    self:RefreshTextDescribeUI(PureText)
    
    local teamMemberNum = 0
    local teamMemberinfo = groupInfo.memberInfos
    for i = 0, 2 do
        if teamMemberinfo[i] == nil then
            teamMemberinfo[i] = 0
        end
        teamMemberNum = teamMemberNum + teamMemberinfo[i]
    end
    
    if groupInfo.details then
        local textGoal = nil
        if groupInfo.details.targetID == 0 then
            textGoal = StringConst.Get("TEAM_NOTARGET")
        else
            local TargetTableData = Game.TableData.GetTargetDataRow(groupInfo.details.targetID)
            textGoal = TargetTableData.Name
        end
        local groupTeamMax = Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") * Game.TableData.GetConstDataRow("GROUP_TEAM_COUNT")
        textGoal = string.format("%s (%s/%s)",textGoal, teamMemberNum, groupTeamMax)
        self.view.Text_Goal:SetText(textGoal)
    end

    self.view.WBP_GroupPos0.Text_Num:SetText(teamMemberinfo[1])
    self.view.WBP_GroupPos1.Text_Num:SetText(teamMemberinfo[0])
    self.view.WBP_GroupPos2.Text_Num:SetText(teamMemberinfo[2])
end

function ChatBubble_TeamRecruitL:RefreshTextDescribeUI(PureText)
    if PureText == nil or PureText == "" then
        self.view.Text_Describe:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.Text_Describe:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Describe:SetText(PureText)
    end
end

--- 此处为自动生成
function ChatBubble_TeamRecruitL:on_RecruitAddBtnCom_ClickEvent()
    local TeamID = GetMainPlayerPropertySafely( "teamID")
    if self.bTeam then
        local targetTeamId = self.Params.chatArgs.teamRecruitInfo.teamUid
        if TeamID ~= 0 then
            if targetTeamId == TeamID then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_IN_SELF_TEAM_CAN_NOT_APPLY)
                return
            end
        end
        Game.TeamSystem:EnterApplyJoinTeamProcess(targetTeamId, self.Params.senderInfo.id,
            const.DEFAULT_ALL_TARGET_ID, true)
    else
        local groupId = self.Params.chatArgs.groupRecruitInfo.groupID
        -- if not Game.EventSystem:IsEventBehaviorSubscribed(_G.EEventTypes.CHAT_TEAM_RECRUIT_CLICKED, groupId) then
        --     Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.CHAT_TEAM_RECRUIT_CLICKED, self, "ShowHasApply", groupId)
        -- end
        --申请加入队伍
        if TeamID ~= 0 then
            if Game.TeamSystem:IsCaptain() then
                Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.TEAM_ALL_TEAM_APPLY_TIPS, function()
                    Game.me:ApplyJoinGroupByGroup(groupId)
                    -- Game.EventSystem:PublishForUniqueID(_G.EEventTypes.CHAT_TEAM_RECRUIT_CLICKED, groupId)
                end)
            else
                Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEND_APPLICATION_CONFIRM, function()
                    Game.me:ApplyJoinGroupByGroup(groupId)
                    -- Game.EventSystem:PublishForUniqueID(_G.EEventTypes.CHAT_TEAM_RECRUIT_CLICKED, groupId)
                end)
            end
            return
        elseif Game.me.groupID ~= 0 then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_APPLY_FAIL_IN_GROUP)
            return
        else
            Game.me:ApplyJoinGroupByGroup(groupId)
        end
    end
    self:ShowHasApply()
end

function ChatBubble_TeamRecruitL:ShowHasApply()
    self.view.RecruitAddBtn:SetVisibility(ESlateVisibility.Collapsed)
    self.view.Text_HasInvited:SetVisibility(ESlateVisibility.Visible)
end

return ChatBubble_TeamRecruitL
