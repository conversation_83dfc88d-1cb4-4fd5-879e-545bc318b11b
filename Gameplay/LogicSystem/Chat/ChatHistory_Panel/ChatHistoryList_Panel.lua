local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatHistoryList_Panel : UIPanel
---@field view ChatHistoryList_PanelBlueprint
local ChatHistoryList_Panel = DefineClass("ChatHistoryList_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ChatHistoryList_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatHistoryList_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatHistoryList_Panel:InitUIData()
    ---@type table 聊天信息列表
    self.HistoryData = {}
end

--- UI组件初始化，此处为自动生成
function ChatHistoryList_Panel:InitUIComponent()
    ---@type UIListView
    self.HistoryListCom = self:CreateComponent(self.view.HistoryList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function ChatHistoryList_Panel:InitUIEvent()
    self:AddUIEvent(self.HistoryListCom.onItemSelected, "on_HistoryListCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatHistoryList_Panel:InitUIView()
    self.view.Text_History_Title:SetText(StringConst.Get("SOCIAL_CHAT_HISTORY"))
end

---面板打开的时候触发
function ChatHistoryList_Panel:OnRefresh(...)
    self.HistoryData = Game.ChatSystem:GetHistoryData()
    self.HistoryListCom:Refresh(self.HistoryData)
end

--- 此处为自动生成
---@param index number
---@param data table
function ChatHistoryList_Panel:on_HistoryListCom_ItemSelected(index, data)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHAT_INPUT_HISTORY, data[2])
    self:CloseSelf()
end

return ChatHistoryList_Panel
