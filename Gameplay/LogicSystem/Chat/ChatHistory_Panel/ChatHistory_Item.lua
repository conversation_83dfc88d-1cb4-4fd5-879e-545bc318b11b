local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatHistory_Item : UIListItem
---@field view ChatHistory_ItemBlueprint
local ChatHistory_Item = DefineClass("ChatHistory_Item", UIListItem)

ChatHistory_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatHistory_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatHistory_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatHistory_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ChatHistory_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatHistory_Item:InitUIView()
end

---面板打开的时候触发
function ChatHistory_Item:OnRefresh(data)
    self.view.Text_Content:SetText(data[2].richText)
end


--- 此处为自动生成
function ChatHistory_Item:on_Btn_ClickArea_Clicked()
end

return ChatHistory_Item
