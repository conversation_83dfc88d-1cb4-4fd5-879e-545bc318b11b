local ChatResource = kg_require("Gameplay.LogicSystem.Chat.ChatResource")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ChatPanelSmall_Item : UIListItem
---@field view ChatPanelSmall_ItemBlueprint
local ChatPanelSmall_Item = DefineClass("ChatPanelSmall_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

ChatPanelSmall_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatPanelSmall_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatPanelSmall_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatPanelSmall_Item:InitUIComponent()
    ---@type ChatResource
    self.WBP_ChannelCom = self:CreateComponent(self.view.WBP_Channel, ChatResource)
end

---UI事件在这里注册，此处为自动生成
function ChatPanelSmall_Item:InitUIEvent()
    self:AddUIEvent(self.view.Text_Content.OnRichTextLinkEvent, "on_Text_Content_RichTextLinkEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatPanelSmall_Item:InitUIView()
end

---面板打开的时候触发
function ChatPanelSmall_Item:OnRefresh(data)
    if data.time and data.showTime and data.chatArgs == nil then
        local timeString = Game.TimeUtils.FormatDateTimeString(data.time)
        self.view.Text_Content:SetText(timeString)
    else
        self.view.Text_Content:SetText(data.SmallWindowsMessageText)
    end
    if data.channelType then
        self.view.WBP_Channel:SetVisibility(ESlateVisibility.Visible)
        self.WBP_ChannelCom:Refresh(data.channelType)
    else
        self.view.WBP_Channel:SetVisibility(ESlateVisibility.Collapsed)
    end
end

---@param url FString
---@param beginIndex int
---@param endIndex int
---@param clickPosition FVector2D
--- 此处为自动生成
function ChatPanelSmall_Item:on_Text_Content_RichTextLinkEvent(url, beginIndex, endIndex, clickPosition)
    Game.ChatSystem:OnUrlClicked(url)
end

return ChatPanelSmall_Item
