---@class ChatMsgBubbleTeamInvite:UIComponent
local ChatMsgBubbleTeamInvite = DefineClass("ChatMsgBubbleTeamInvite", UIComponent)
local const = kg_require("Shared.Const")
local P_Head = kg_require "Gameplay.LogicSystem.Character.P_Head2"
local StringConst = require "Data.Config.StringConst.StringConst"
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
local ESlateVisibility = import("ESlateVisibility")

function ChatMsgBubbleTeamInvite:OnCreate()
    ---@type table 气泡参数
    self.Params = nil
    ---@type table 头像1
    self.P_Head1 = self:BindComponent(self.View.TeamHead1, P_Head)
    ---@type table 头像2
    self.P_Head2 = self:BindComponent(self.View.TeamHead2, P_Head)
    ---@type table 头像3
    self.P_Head3 = self:BindComponent(self.View.TeamHead3, P_Head)
    ---@type table 头像4
    self.P_Head4 = self:BindComponent(self.View.TeamHead4, P_Head)
    ---@type table 头像5
    self.P_Head5 = self:BindComponent(self.View.TeamHead5, P_Head)
    ---@type table 头像6
    self.P_Head6 = self:BindComponent(self.View.TeamHead6, P_Head)
    ---@type table 头像列表
    self.P_HeadList = { self.P_Head1, self.P_Head2, self.P_Head3, self.P_Head4, self.P_Head5, self.P_Head6 }
    self:AddUIListener(EUIEventTypes.CLICK, self.View.RecruitAddBtn.Btn_Com, self.OnClickRecruitAddBtn)
    self.View.RecruitAddBtn.Text_Com:SetText(StringConst.Get("TEAM_APPLY_JOINTEAM"))
    self.JobData = {
        [1] = 
            {
                Type = 0,
                Text = "来输出",
                Widget = self.View.WBP_Attack
            },
        [2] = 
            {
                Type = 1,
                Text = "来防御",
                Widget = self.View.WBP_Defend
            },
        [3] = 
            {
                Type = 2,
                Text = "来治疗",
                Widget = self.View.WBP_Cure
            },
    }
end

function ChatMsgBubbleTeamInvite:SetData(params)
    self.Params = params
    if self.Params.functionType == Enum.ChatFunctionType.TEAM_RECRUIT then
        self.bTeam = true
        self.bRescue = self.Params.chatArgs.teamRecruitInfo .bInSeekRescue 
    else
        self.bTeam = false
        self.bRescue = self.Params.chatArgs.groupRecruitInfo.bInSeekRescue 
    end
    self.View:Event_UI_State(self.bRescue, self.bTeam)
    if self.bTeam then
        self:RefreshTeamUI()
        if self.bRescue then
            self.View.RecruitAddBtn.Text_Com:SetText("前往支援")
        else
            self.View.RecruitAddBtn.Text_Com:SetText("申请入队")
        end
    else
        self:RefreshGroupUI()
        if self.bRescue then
            self.View.RecruitAddBtn.Text_Com:SetText("前往支援")
        else
            self.View.RecruitAddBtn.Text_Com:SetText("申请入团")
        end
    end
    self.View.RecruitAddBtn:SetVisibility(ESlateVisibility.Visible)
    self.View.Text_HasInvited:SetVisibility(ESlateVisibility.Collapsed)
end

function ChatMsgBubbleTeamInvite:RefreshTeamUI()
    local teamInfo = self.Params.chatArgs.teamRecruitInfo
    self.View.Text_HasInvited:SetVisibility(ESlateVisibility.Hidden)
    self.View.RecruitAddBtn:SetVisibility(ESlateVisibility.Visible)
    local PureText = string.split(self.Params.messageText, "<")[1]
    local PosText = string.sub(self.Params.messageText, #PureText + 1)
    if PosText then
        self.View.HB_HelpClass:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        for key, value in pairs(self.JobData) do
            if string.contains(PosText, value.Text) then
                value.Widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                value.Widget.Text_Describe:SetText(value.Text)
                value.Widget:Event_UI_State(value.Type)
            else
                value.Widget:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
    else
        self.View.HB_HelpClass:SetVisibility(ESlateVisibility.Hidden)
    end
    self:RefreshTextDescribeUI(PureText)
    local teamMemberinfo = teamInfo.memberInfos
    local NewTeamInfo = {}
    for _, v in pairs(teamMemberinfo) do
        table.insert(NewTeamInfo, v)
    end
    table.sort(NewTeamInfo, function(a, b)
        if a.isCaptain ~= b.isCaptain then
            return a.isCaptain
        end
        return a.enterTime < b.enterTime
    end)
    if teamInfo.details.targetID == 0 then
        self.View.Text_Goal:SetText(StringConst.Get("TEAM_NOTARGET"))
    else
        local TargetTableData = Game.TableData.GetTargetDataRow(teamInfo.details.targetID)
        self.View.Text_Goal:SetText(TargetTableData.Name)
    end
    for i = 1, Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") do
        if NewTeamInfo[i] then
            self.P_HeadList[i]:Refresh(
                {
                    ProfessionID = NewTeamInfo[i].profession,
                    bIsCaptain = NewTeamInfo[i].isCaptain
                }
            )
        else
            self.P_HeadList[i]:Refresh(
                {
                    Level = 0,
                    ProfessionID = 0,
                    bIsCaptain = false
                })
        end
    end
end

function ChatMsgBubbleTeamInvite:RefreshGroupUI()
    local groupInfo = self.Params.chatArgs.groupRecruitInfo
    local PureText = string.split(self.Params.messageText, "<>")[1]
    local PosText = string.sub(self.Params.messageText, #PureText + 1)
    local bHasPos = false
    if PosText then
        for key, value in pairs(self.JobData) do
            if string.contains(PosText, value.Text) then
                bHasPos = true
                value.Widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                value.Widget.Text_Describe:SetText(value.Text)
                value.Widget:Event_UI_State(value.Type)
            else
                value.Widget:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
    end
    if bHasPos then
        self.View.HB_HelpClass:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.View.HB_HelpClass:SetVisibility(ESlateVisibility.Hidden)
    end
    self:RefreshTextDescribeUI(PureText)
    if groupInfo.details then
        if groupInfo.details.targetID == 0 then
            self.View.Text_Goal:SetText(StringConst.Get("TEAM_NOTARGET"))
        else
            local TargetTableData = Game.TableData.GetTargetDataRow(groupInfo.details.targetID)
            self.View.Text_Goal:SetText(TargetTableData.Name)
        end
    end
    local teamMemberinfo = groupInfo.memberInfos
    for i = 0, 2 do
        if teamMemberinfo[i] == nil then
            teamMemberinfo[i] = 0
        end
    end
    self.View.WBP_GroupPos0.Text_Num:SetText(teamMemberinfo[1])
    self.View.WBP_GroupPos1.Text_Num:SetText(teamMemberinfo[0])
    self.View.WBP_GroupPos2.Text_Num:SetText(teamMemberinfo[2])
end

function ChatMsgBubbleTeamInvite:RefreshTextDescribeUI(PureText)
    if PureText == nil or PureText == "" then
        self.View.Text_Describe:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.View.Text_Describe:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.Text_Describe:SetText(PureText)
    end
end

function ChatMsgBubbleTeamInvite:OnClickRecruitAddBtn()
    local TeamID = GetMainPlayerPropertySafely( "teamID")
    if self.bTeam then
        if TeamID ~= 0 then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_IN_TEAM_CAN_NOT_APPLY)
            return
        end
        Game.TeamSystem:EnterApplyJoinTeamProcess(self.Params.chatArgs.teamRecruitInfo.teamUid, self.Params.senderInfo.id,
            const.DEFAULT_ALL_TARGET_ID, true)
    else
        local groupid = self.Params.chatArgs.groupRecruitInfo.groupID
        --申请加入队伍
        if TeamID ~= 0 then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEND_APPLICATION_CONFIRM, function()
                Game.me:ApplyJoinGroupByGroup(groupid)
            end)
        elseif Game.me.groupID ~= 0 then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_APPLY_FAIL_IN_GROUP)
            return
        else
            Game.me:ApplyJoinGroupByGroup(groupid)
        end
    end
    self.View.RecruitAddBtn:SetVisibility(ESlateVisibility.Collapsed)
    self.View.Text_HasInvited:SetVisibility(ESlateVisibility.Visible)
end

return ChatMsgBubbleTeamInvite
