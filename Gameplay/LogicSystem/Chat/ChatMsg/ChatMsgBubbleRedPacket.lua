---@class ChatMsgBubbleRedPacket:UIComponent
local ChatMsgBubbleRedPacket = DefineClass("ChatMsgBubbleRedPacket", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require "Data.Config.StringConst.StringConst"
local redPacketConst = kg_require("Shared.Const.RedPacketConst")

ChatMsgBubbleRedPacket.eventBindMap = {
}


function ChatMsgBubbleRedPacket:OnCreate()
    self.KGTextBlock_Discript = self.View.KGTextBlock_Discript
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Big_Button_ClickArea, self.ClickRP)
end

function ChatMsgBubbleRedPacket:Refresh(params)
    self:SetData(params)
end

function ChatMsgBubbleRedPacket:SetData(params)
    self.Params = params
    self.RedInfo = params.chatArgs.redPacketInfo
    if self.RedInfo == nil then
        return 
    end
    self.KGTextBlock_Discript = self.View.KGTextBlock_Discript
	if params.contentType == Enum.ECHAT_CONTENT_TYPE.GUILD_DANCE_RED_PACKET then
		self.Type = 2
	elseif self.RedInfo.emojiInfo then
        self.Type = 1
        self.KGTextBlock_Discript = self.View.KGTextBlock_emoj_Discript
    else
		self.Type = 0
	end
	self:RefreshReceiveUI()
	local discript = nil
	if self.Type == 2 then
		discript = StringConst.Get("RED_PACKET_TEXT_1")
		self:SetImage(self.View.Img_Bg, StringConst.Get("RED_PACKET_BG_PATH")) -- 更换背景
	else
		discript = StringConst.Get("RED_DEFAULT_MESSAGE")
		self.View.KGTextBlock_Name:SetText(params.senderInfo.rolename)
	end

    if not StringValid(self.RedInfo.message) then
        self.KGTextBlock_Discript:SetText(discript)
    else
        self.KGTextBlock_Discript:SetText(self.RedInfo.message)
    end
    self.View.CP_Channel:SetVisibility(ESlateVisibility.selfHitTestInvisible)
    local variety = redPacketConst.TYPE_TO_VARIETY[self.RedInfo.packetType]
    if not variety then
        variety = redPacketConst.CLASS_TO_VARIETY[self.RedInfo.packetClass]
    end
    self.View:BP_SetVariety(variety)
    self.View.CP_Channel:SetVisibility(ESlateVisibility.Collapsed)
end

function ChatMsgBubbleRedPacket:RefreshReceiveUI()
    if self.RedInfo.emojiInfo then
        local emojiID = self.RedInfo.emojiInfo.id
        local emoData = Game.TableData.GetChatStickerDataRow(emojiID)
        if emoData then
            --self.View.WBP_RedPacket_Emoj:SetVisibility(ESlateVisibility.Visible)
            self:SetImage(self.View.WBP_RedPacket_Emoj.Img_Emoj, emoData["Icon"])
        end
    end

    local bSetLight = false
    if Game.GameClientData:GetPlayerValue("RPData") and Game.GameClientData:GetPlayerValue("RPData")[self.RedInfo.uuid] ~= nil then
        if Game.GameClientData:GetPlayerValue("RPData")[self.RedInfo.uuid].bIsFinished == true then
            self.View:Event_UI_Type(false, self.Type, true)
            self.View.WBP_RedPacket_Empty.KGTextBlock:SetText("已抢光")
        elseif Game.GameClientData:GetPlayerValue("RPData")[self.RedInfo.uuid].bIsReceived == true then
            self.View:Event_UI_Type(false, self.Type, true)
            self.View.WBP_RedPacket_Empty.KGTextBlock:SetText("已领取")
        else
            self.View:Event_UI_Type(false, self.Type, false)
            bSetLight = true
        end
    else
        if self.RedInfo.bIsFinished == true then
            self.View:Event_UI_Type(false, self.Type, true)
            self.View.WBP_RedPacket_Empty.KGTextBlock:SetText("已抢光")
        else
            local bIsReceived = false 
            if self.RedInfo.packetClass == redPacketConst.RED_PACKET_CLASS.MONEY  and self.RedInfo.currentStatus.receiveMoneyDict then
                for key, value in pairs(self.RedInfo.currentStatus.receiveMoneyDict) do
                    if key == Game.me.eid then
                        bIsReceived = true
                        break
                    end
                end 
            end
            if self.RedInfo.packetClass == redPacketConst.RED_PACKET_CLASS.Item  and self.RedInfo.currentStatus.receiveGoodsDict then
                for key, value in pairs(self.RedInfo.currentStatus.receiveGoodsDict) do
                    if key == Game.me.eid then
                        bIsReceived = true
                        break
                    end
                end 
            end
            if bIsReceived then
                self.View:Event_UI_Type(false, self.Type, true)
                self.View.WBP_RedPacket_Empty.KGTextBlock:SetText("已领取")
            else
                self.View:Event_UI_Type(false, self.Type, false)
                bSetLight = true
            end
        end
    end
    self.View:BP_SetLight(bSetLight)
end

function ChatMsgBubbleRedPacket:OnReceiveInfoChange(RPInfo)
    if RPInfo.uuid == self.RedInfo.uuid then
        self:RefreshReceiveUI()
    end
end

function ChatMsgBubbleRedPacket:ClickRP()
    if self.RedInfo then 
        Game.me:ReqRedPacketInfo(self.RedInfo.uuid, self.Params.channelType)
    end
end

return ChatMsgBubbleRedPacket
