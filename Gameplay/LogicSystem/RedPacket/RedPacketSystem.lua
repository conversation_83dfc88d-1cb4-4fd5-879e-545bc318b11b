---@class RedPacketSystem:SystemBase
local RedPacketSystem = DefineSingletonClass("RedPacketSystem", SystemBase)
local StringConst = require "Data.Config.StringConst.StringConst"
local EPathFollowingResult = import("EPathFollowingResult")
local redPacketConst = kg_require("Shared.Const.RedPacketConst")

local Game = Game
local Enum = Enum

local pairs = ksbcpairs

function RedPacketSystem:onInit()
    ---@type RedPacketSystemModel
    self.model = kg_require "Gameplay.LogicSystem.RedPacket.RedPacketModel".new()
    --[[self.SendFormData = {
        {Index = redPacketConst.RED_PACKET_TYPE.RANDOM, text = StringConst.Get("RED_PACKET_LUCKY")},
        {Index = redPacketConst.RED_PACKET_TYPE.SECRET_WORD, text = StringConst.Get("RED_PACKET_VOICE")},
        {Index = redPacketConst.RED_PACKET_TYPE.PASSWORD, text = StringConst.Get("RED_PACKET_PASSWORD")}
    }]]

    self.SendFormData = {
        [redPacketConst.RED_PACKET_TYPE.RANDOM] = StringConst.Get("RED_PACKET_LUCKY"),
        [redPacketConst.RED_PACKET_TYPE.SECRET_WORD] = StringConst.Get("RED_PACKET_VOICE"),
        [redPacketConst.RED_PACKET_TYPE.PASSWORD] = StringConst.Get("RED_PACKET_PASSWORD"),
    }
end

function RedPacketSystem:SetRPChannelIndex(Index)
    self.model.RPChannel = Index
end

function RedPacketSystem:GetRPChannelIndex()
    return self.model.RPChannel 
end

function RedPacketSystem:GetRTSendForms()
    return self.SendFormData
end

function RedPacketSystem:SetRPClass(Index)
    self.model.RPClass = Index
end

function RedPacketSystem:GetRPClass()
    return self.model.RPClass 
end

function RedPacketSystem:SetRTSendForm(Index)
    self.model.RTSendForm = Index
end

function RedPacketSystem:GetRTSendForm()
    return self.model.RTSendForm 
end

function RedPacketSystem:AddSelSkin(GoodsID, ItemID, GoodsPrice, Num)
    table.insert(self.model.SelSkinList, {GoodsID = GoodsID, ItemID = ItemID, GoodsPrice = GoodsPrice, Num = Num})
end

function RedPacketSystem:DelSelSkin(Data)
    for key, value in pairs(self.model.SelSkinList) do
        if value.GoodsID == Data.GoodsID then
            table.remove(self.model.SelSkinList, key)
            break
        end
    end
end

function RedPacketSystem:FindSelSkin(ID)
    for key, value in pairs(self.model.SelSkinList) do
        if value.GoodsID == ID then
            return true
        end
    end
    return false
end

function RedPacketSystem:GetSelSkinNum(ID)
    for key, value in pairs(self.model.SelSkinList) do
        if value.GoodsID == ID then
            return value.Num
        end
    end
    return 0
end

function RedPacketSystem:GetSelSkin()
    return self.model.SelSkinList
end

function RedPacketSystem:ClearSelSkin()
    table.clear(self.model.SelSkinList)
end

function RedPacketSystem:ResetData()
    self.model.RPChannel = 1 
    self.model.RPClass = 1
    self.model.RTSendForm = 1
    table.clear(self.model.SelSkinList)
end

function RedPacketSystem:EnterOpenRPProcess(Data)
    if Data.packetClass == redPacketConst.RED_PACKET_CLASS.ITEM and Game.me.MaxGiftCreditLimit - Game.me.UsedGiftCredit < Data.Num then
        --额度不够
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.RED_PACKET_GIFT_CREDIT_NOT_ENOUGH)
        return
    end
    if Data.packetType == redPacketConst.RED_PACKET_TYPE.PASSWORD then
        if Data.passwd == "" then
            --没有密码
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.RED_PACKET_NO_PASSWORD)
            return
        end
    end
    if Data.moneyNum > Game.BagSystem:GetItemCount(redPacketConst.RED_PACKET_MONEY_TYPE) then
        --金额不够
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.RED_PACKET_MONEY_NOT_ENOUGH)
        return
    end
    if Data.packetType == redPacketConst.RED_PACKET_TYPE.SECRET_WORD then
        --特殊字符检查
        if self:IsContainNormalSymbol(Data.passwd) == false then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.RED_PACKET_SECRET_WORD_SPECIAL)
            return
        end
    end
    local CurrencyImg = Game.UIIconUtils.GetIconByItemId(redPacketConst.RED_PACKET_MONEY_TYPE, nil, true)
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SOCIAL_FRIEND_GIFT_BUY_CHECK, 
    function()
        Game.ChatSystem:chatTo(Game.RedPacketSystem:GetRPChannelIndex(), "", Enum.EChatMessageType.RED_PACKET, Enum.ChatFunctionType.RED_PACKET, {redPacketInfo = Data})
        Game.NewUIManager:ClosePanel(UIPanelConfig.RedPacket_BasicPanel)

        Game.RedPacketSystem:ClearSelSkin()
    end, 
    nil,
    { CurrencyImg, Data.moneyNum })
end

function RedPacketSystem:IsContainNormalSymbol(str)
    for i = 1, utf8.len(str) do
        local char = utf8.sub(str, i, i+1)
        -- 检查字符的 Unicode 范围是否在中文字符范围内
        local codepoint = utf8.codepoint(char)
        if (codepoint >= 48 and codepoint <= 57) or (codepoint >= 65 and codepoint <= 90) 
            or (codepoint >= 97 and codepoint <= 122) or (codepoint >= 0x4E00 and codepoint <= 0x9FA5) then
            return true
        end
    end
    return false
end

function RedPacketSystem:GetHeadIconFromSenderInfo(senderInfo)
    if not senderInfo then
        return nil
    end
    local playerData = Game.TableData.GetPlayerSocialDisplayDataRow(senderInfo.school)[senderInfo.sex]
    return playerData and playerData.SoloHeadIcon
end

return RedPacketSystem