local RedPacket_InputSmall_Item = kg_require("Gameplay.LogicSystem.RedPacket.RedPacket_Item.RedPacket_InputSmall_Item")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class RedPacket_Code_Item : UIListItem
---@field view RedPacket_Code_ItemBlueprint
local RedPacket_Code_Item = DefineClass("RedPacket_Code_Item", UIListItem)

RedPacket_Code_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RedPacket_Code_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RedPacket_Code_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RedPacket_Code_Item:InitUIComponent()
    ---@type RedPacket_InputSmall_Item
    self.WBP_inputCom = self:CreateComponent(self.view.WBP_input, RedPacket_InputSmall_Item)
end

---UI事件在这里注册，此处为自动生成
function RedPacket_Code_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RedPacket_Code_Item:InitUIView()
end

---面板打开的时候触发
function RedPacket_Code_Item:OnRefresh(params)
    self.WBP_inputCom:Refresh(params)
end

function RedPacket_Code_Item:GetInputText()
    return self.WBP_inputCom:GetInputText()
end

return RedPacket_Code_Item
