local RedPacket_InputBig_Item = kg_require("Gameplay.LogicSystem.RedPacket.RedPacket_Item.RedPacket_InputBig_Item")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local ESlateVisibility = import("ESlateVisibility")
---@class RedPacket_Comment_Item : UIListItem
---@field view RedPacket_Comment_ItemBlueprint
local RedPacket_Comment_Item = DefineClass("RedPacket_Comment_Item", UIListItem)

local Game = Game

RedPacket_Comment_Item.eventBindMap = {
    [EEventTypesV2.CHAT_SHOUT_INPUT] = "OnEmojiSelected",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RedPacket_Comment_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RedPacket_Comment_Item:InitUIData()
    self.emojiInfo = nil
    self.emojiDefaultIcon = UIAssetPath.UI_RedPacket_Default_Emoji
end

--- UI组件初始化，此处为自动生成
function RedPacket_Comment_Item:InitUIComponent()
    ---@type RedPacket_InputBig_Item
    self.WBP_InputCom = self:CreateComponent(self.view.WBP_Input, RedPacket_InputBig_Item)
end

---UI事件在这里注册，此处为自动生成
function RedPacket_Comment_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Btn_Delete.OnClicked, "on_Btn_Delete_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RedPacket_Comment_Item:InitUIView()
end

---面板打开的时候触发
function RedPacket_Comment_Item:OnRefresh(params)
    self.Params = params
    self.WBP_InputCom:Refresh(params)

    self:RefreshEmoji()
end

function RedPacket_Comment_Item:RefreshEmoji()
    local iconPath
    if self.emojiInfo then
        local emojiID = self.emojiInfo.id
        local emoData = Game.TableData.GetChatStickerDataRow(emojiID)
        if emoData then
            iconPath = emoData["Icon"]
        end
    end
    if iconPath and iconPath ~= "" then
        self:SetImage(self.view.emoji_Icon, iconPath)

        self.view.Btn_Delete:SetVisibility(ESlateVisibility.Visible)
        self.view.Img_Delete:SetVisibility(ESlateVisibility.Visible)
    else
        self:SetImage(self.view.emoji_Icon, self.emojiDefaultIcon)

        self.view.Btn_Delete:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Img_Delete:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function RedPacket_Comment_Item:GetInputText()
    return self.WBP_InputCom:GetInputText()
end

function RedPacket_Comment_Item:SetInputText(text)
    return self.WBP_InputCom:SetInputText(text)
end

function RedPacket_Comment_Item:SetHintText(text)
    return self.WBP_InputCom:SetHintText(text)
end

function RedPacket_Comment_Item:SetBlockTitle(text)
    self.view.KGTextBlock_Title:SetText(text)
end


--- 此处为自动生成
function RedPacket_Comment_Item:on_Btn_ClickArea_Clicked()
    if not Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.ChatExpression_Panel) then
        Game.NewUIManager:OpenPanel(UIPanelConfig.ChatExpression_Panel, true, 0, 0, true)
    end
end

function RedPacket_Comment_Item:OnEmojiSelected(_, _, _, emoData)
    self.emojiInfo = {
        id = emoData.ID
    }
    self:RefreshEmoji()
end

--- 此处为自动生成
function RedPacket_Comment_Item:on_Btn_Delete_Clicked()
    self.emojiInfo = nil
    self:RefreshEmoji()
end

return RedPacket_Comment_Item
