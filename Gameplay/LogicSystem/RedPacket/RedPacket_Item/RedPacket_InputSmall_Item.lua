local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local ESlateVisibility = import("ESlateVisibility")

local Game = Game
local Enum = Enum
--local pairs = ksbcpairs

---@class RedPacket_InputSmall_Item : UIListItem
---@field view RedPacket_InputSmall_ItemBlueprint
local RedPacket_InputSmall_Item = DefineClass("RedPacket_InputSmall_Item", UIListItem)

RedPacket_InputSmall_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RedPacket_InputSmall_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RedPacket_InputSmall_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RedPacket_InputSmall_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function RedPacket_InputSmall_Item:InitUIEvent()
    self:AddUIEvent(self.view.EditText.OnTextChanged, "on_EditText_TextChanged")
    self:AddUIEvent(self.view.EditText.OnC7TextChanged, "on_EditText_C7TextChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RedPacket_InputSmall_Item:InitUIView()
    self.view.EditText:SetVisibility(ESlateVisibility.Visible)
end

---面板打开的时候触发
function RedPacket_InputSmall_Item:OnRefresh(params)
    self.Params = params

    self.InputLimit = params.InputLimit
    if self.Params ~= nil and self.Params.HintText ~= nil then
        self:SetHintText(self.Params.HintText)
    end

    if self.Params ~= nil and self.Params.DefaultInputText ~= nil then
        self:SetInputText(self.Params.DefaultInputText)
    end
    self.view.Text_Num_Right:SetText("/"..self.InputLimit)
end

function RedPacket_InputSmall_Item:SetInputText(text)
    self.view.EditText:SetText(text)
end

function RedPacket_InputSmall_Item:SetHintText(text)
    self.view.EditText:SetHintText(text)
end

function RedPacket_InputSmall_Item:OnTextChanged()
    local value = self.view.EditText:GetText()
    local newText = self:LimitStrByConfig(value)
    if newText ~= value then
        self:SetInputText(newText)
        if self.Params and self.Params.OnValueLimitOverCallback ~= nil then
            self.Params.OnValueLimitOverCallback(self.Params.Owner)
        end
    end
    self.oldText = newText
    if self.Params and self.Params.OnValueChangedCallback then
        self.Params.OnValueChangedCallback(self.Params.Owner, newText)
    end
    self.view.Text_Num_Left:SetText(self:CountChars(self.oldText))
end

function RedPacket_InputSmall_Item:LimitStrByConfig(Str)
    if self.Params.InputLimit then
        local textLength = self:CountChars(Str)
        if textLength > self.Params.InputLimit then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_INPUT_MAX)
            return self.oldText or ""
        end
    end
    return Str
    --[[
    local Count = 0
    local Result = ""

    local LegalTextTable = Game.TableData.GetLegalTextDataTable()
    for P, C in utf8.codes(Str) do
        local bFound = false
        for _, Config in pairs(LegalTextTable) do
            local StartPosition = tonumber(Config.StartPosition, 16)
            local EndPosition = tonumber(Config.EndPosition, 16)
            if C >= StartPosition and C <= EndPosition then
                Count = Count + Config.CharCount
                bFound = true
                break
            end
        end
        if not bFound then
            Count = Count + 1
        end
        if self.Params.InputLimit and Count > self.Params.InputLimit then
            self:SetInputText(Result)
            break
        end
        Result = Result .. utf8.char(C)
    end
    return Result]]
end

function RedPacket_InputSmall_Item:CountChars(str)
    --汉字、数字、字符都按照一个计数
    local len = 0
    if str then
        len = utf8.len(str)
    end
    return len
end

function RedPacket_InputSmall_Item:GetInputText()
    return self.view.EditText:GetText()
end

--- 此处为自动生成
---@param text FText
function RedPacket_InputSmall_Item:on_EditText_TextChanged(text)
    self:OnTextChanged(text)
end

--- 此处为自动生成
---@param text FText
function RedPacket_InputSmall_Item:on_EditText_C7TextChanged(text)
    self:OnTextChanged(text)
end

return RedPacket_InputSmall_Item
