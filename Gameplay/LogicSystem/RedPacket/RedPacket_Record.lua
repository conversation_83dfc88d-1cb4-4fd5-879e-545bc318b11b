local UIComSimpleTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local RedPacket_Coin_Item = kg_require("Gameplay.LogicSystem.RedPacket.RedPacket_Item.RedPacket_Coin_Item")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local redPacketConst = kg_require("Shared.Const.RedPacketConst")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

local ESlateVisibility = import("ESlateVisibility")

local Game = Game


---@class RedPacket_Record : UIComponent
---@field view RedPacket_RecordBlueprint
local RedPacket_Record = DefineClass("RedPacket_Record", UIComponent)

RedPacket_Record.eventBindMap = {
    [EEventTypesV2.REDPACKET_HISTORY_CHANGE] = "OnRPHistoryChange",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RedPacket_Record:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RedPacket_Record:InitUIData()
    self.ClassDatas = {
        [redPacketConst.RED_PACKET_RECORD_TYPE.SEND] = UIComDropDown.NewOptionData(StringConst.Get("RED_PACKET_RECORD_SEND")),
        [redPacketConst.RED_PACKET_RECORD_TYPE.RECEIVE] = UIComDropDown.NewOptionData(StringConst.Get("RED_PACKET_RECORD_RECEIVE")),
    }

    self.SeasonDatas = {
        [1] = UIComDropDown.NewOptionData(StringConst.Get("RED_PACKET_SEASON")),
    }

    self.DisplayMaxNum = 999
    self.DisplayMaxText = self.DisplayMaxNum .. "+"
end

--- UI组件初始化，此处为自动生成
function RedPacket_Record:InitUIComponent()
    ---@type UIComDropDown
    self.WBP_TabList_WBP_ComComBox_SeasonCom = self:CreateComponent(self.view.WBP_TabList.WBP_ComComBox_Season, UIComDropDown)
    ---@type UIComDropDown
    self.WBP_TabList_WBP_ComComBox_ClassCom = self:CreateComponent(self.view.WBP_TabList.WBP_ComComBox_Class, UIComDropDown)
    ---@type UIComSimpleTabList
    self.WBP_ComTabHorCom = self:CreateComponent(self.view.WBP_ComTabHor, UIComSimpleTabList)
    ---@type RedPacket_Coin_Item
    self.WBP_Player_Item_WBP_RedPacket_CoinCom = self:CreateComponent(self.view.WBP_Player_Item.WBP_RedPacket_Coin, RedPacket_Coin_Item)
    ---@type UIListView
    self.WBP_ComListCom = self:CreateComponent(self.view.WBP_ComList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function RedPacket_Record:InitUIEvent()
    self:AddUIEvent(self.WBP_ComTabHorCom.onItemSelected, "on_WBP_ComTabHorCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComListCom.onItemSelected, "on_WBP_ComListCom_ItemSelected")
    self:AddUIEvent(self.WBP_TabList_WBP_ComComBox_ClassCom.onItemSelected, "on_WBP_TabList_WBP_ComComBox_ClassCom_ItemSelected")
    self:AddUIEvent(self.WBP_TabList_WBP_ComComBox_SeasonCom.onItemSelected, "on_WBP_TabList_WBP_ComComBox_SeasonCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RedPacket_Record:InitUIView()
end

---组件刷新统一入口
function RedPacket_Record:Refresh()
    self.WBP_ComTabHorCom:Refresh(redPacketConst.RED_PACKET_TAB_DATA)
    self.SelTabIndex = 2
    self.WBP_ComTabHorCom:SetSelectedItemByIndex(self.SelTabIndex, true)

    self.view.WBP_Num.Txt_Title_luck_Right:SetVisibility(ESlateVisibility.Collapsed)
    self.view.WBP_Player_Item.Txt_Name:SetText(Game.me.Name)
    local OptionClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(Game.me.Profession)[0]
    self:SetImage(self.view.WBP_Player_Item.Img_Head, OptionClassInfo.SoloHeadIcon)

    self.RpClassIndex = 1
    self.WBP_TabList_WBP_ComComBox_ClassCom:Refresh(self.ClassDatas, self.RpClassIndex)
    self.SeasonClassIndex = 1
    self.WBP_TabList_WBP_ComComBox_SeasonCom:Refresh(self.SeasonDatas, self.SeasonClassIndex)

    self:on_WBP_TabList_WBP_ComComBox_ClassCom_ItemSelected(self.RpClassIndex)
end

function RedPacket_Record:OnRPHistoryChange(RPClass, RPSeason, AllMoney, AllTimes, AllBestLuck, HistoryList)
    if RPClass == redPacketConst.RED_PACKET_RECORD_TYPE.SEND then
        local ReceiveMemberNum = 0
        for key, value in ipairs(HistoryList) do
            ReceiveMemberNum = value.receivedNum + ReceiveMemberNum
        end
        self.view.WBP_Num.Txt_Title_luck:SetText(ReceiveMemberNum > self.DisplayMaxNum and self.DisplayMaxText or ReceiveMemberNum)
    elseif RPClass == redPacketConst.RED_PACKET_RECORD_TYPE.RECEIVE then
        self.view.WBP_Num.Txt_Title_luck:SetText(AllBestLuck > self.DisplayMaxNum and self.DisplayMaxText or AllBestLuck)
    end
    self.view.WBP_Num.KGTextBlock_Total:SetText(AllTimes)
    self.view.WBP_Player_Item.WBP_RedPacket_Coin.KGTextBlock:SetText(AllMoney)
    self.HistoryList = HistoryList
    self.WBP_ComListCom:Refresh(HistoryList, 1, {RpClassIndex = self.RpClassIndex})
    self.view.WBP_Num.Img_Coin:SetVisibility(ESlateVisibility.Collapsed)
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function RedPacket_Record:on_WBP_TabList_WBP_ComComBox_ClassCom_ItemSelected(index, data)
    self.RpClassIndex = index
    if index == redPacketConst.RED_PACKET_RECORD_TYPE.SEND then
        self.view.WBP_Num.Txt_Title_Type:SetText(StringConst.Get("RED_PACKET_TOTAL_SEND"))
        self.view.WBP_Num.Txt_Title_CurGet:SetText(StringConst.Get("RED_PACKET_RECEIVE_NUM").." ")
        self.view.WBP_Player_Item.Txt_Type:SetText(StringConst.Get("RED_PACKET_SEND_AMOUNT").." ")
        self:SetImage(self.view.WBP_Player_Item.WBP_RedPacket_Coin.Img_Coin, Game.UIIconUtils.GetIconByItemId(redPacketConst.RED_PACKET_MONEY_TYPE))
    elseif index == redPacketConst.RED_PACKET_RECORD_TYPE.RECEIVE then
        self.view.WBP_Num.Txt_Title_Type:SetText(StringConst.Get("RED_PACKET_TOTAL_RECEIVE"))
        self.view.WBP_Num.Txt_Title_CurGet:SetText(StringConst.Get("RED_PACKET_BEST_LUCK").." ")
        self.view.WBP_Player_Item.Txt_Type:SetText(StringConst.Get("RED_PACKET_RECEIVE_AMOUNT").." ")
        self:SetImage(self.view.WBP_Player_Item.WBP_RedPacket_Coin.Img_Coin, Game.UIIconUtils.GetIconByItemId(redPacketConst.RED_PACKET_MONEY_RETURN_TYPE))
    end
    local maxHistoryNum = Game.TableData.GetConstDataRow("RED_PACKET_RECORD_MAX_LENGTH") or 100
    Game.me:ReqRedPacketHistory(self.RpClassIndex, self.SeasonClassIndex, 1, maxHistoryNum)
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function RedPacket_Record:on_WBP_TabList_WBP_ComComBox_SeasonCom_ItemSelected(index, data)
    self.SeasonClassIndex = index
end


--- 此处为自动生成
---@param index number
---@param data table
function RedPacket_Record:on_WBP_ComTabHorCom_ItemSelected(index, data)
    if self.SelTabIndex ~= index then
        local cellID = redPacketConst.RED_PACKET_TAB_INDEX_TO_CELL_ID[index]
        self:GetParent():RefreshChildUI(cellID)
        self.SelTabIndex = index
    end
end



--- 此处为自动生成
---@param index number
---@param data table
function RedPacket_Record:on_WBP_ComListCom_ItemSelected(index, data)
    Game.me:ReqRedPacketInfo(self.HistoryList[index].uuid, self.HistoryList[index].packetChannel)
end

return RedPacket_Record
