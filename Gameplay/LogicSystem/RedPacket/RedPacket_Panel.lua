local UIComMask = kg_require("Framework.KGFramework.KGUI.Component.BackGround.UIComMask")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

local redPacketConst = kg_require("Shared.Const.RedPacketConst")

local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local Game = Game
local Enum = Enum

---@class RedPacket_Panel : UIPanel
---@field view RedPacket_PanelBlueprint
local RedPacket_Panel = DefineClass("RedPacket_Panel", UIPanel)

RedPacket_Panel.eventBindMap = {
    [_G.EEventTypes.REDPACKET_RECEIVE_SUCCESS] = "OnReceiveRedPacketSuccess",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RedPacket_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RedPacket_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RedPacket_Panel:InitUIComponent()
    ---@type UIComMask
    self.WBP_ComMaskCom = self:CreateComponent(self.view.WBP_ComMask, UIComMask)
    ---@type UIComButton
    self.WBP_CloseCom = self:CreateComponent(self.view.WBP_Close, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function RedPacket_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Report.OnClicked, "on_Btn_Report_Clicked")
    self:AddUIEvent(self.view.Btn_Unlock.OnClicked, "on_Btn_Unlock_Clicked")
    self:AddUIEvent(self.view.Btn_Open.OnClicked, "on_Btn_Open_Clicked")
    self:AddUIEvent(self.view.Btn_Voice.OnPressed, "on_Btn_Voice_Pressed")
    self:AddUIEvent(self.view.Btn_Voice.OnReleased, "on_Btn_Voice_Released")
    self:AddUIEvent(self.view.Btn_Voice.OnHovered, "on_Btn_Voice_Hovered")
    self:AddUIEvent(self.view.Btn_Voice.OnUnhovered, "on_Btn_Voice_Unhovered")
    self:AddUIEvent(self.view.EditText_Passwd.OnTextChanged, "on_EditText_Passwd_TextChanged")
    self:AddUIEvent(self.view.EditText_Passwd.OnC7TextChanged, "on_EditText_Passwd_C7TextChanged")
    self:AddUIEvent(self.WBP_CloseCom.onClickEvent, "on_WBP_CloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RedPacket_Panel:InitUIView()
end

---面板打开的时候触发
function RedPacket_Panel:OnRefresh(params)
    self.RPInfo = params
    self.view.EditText_Passwd:SetText("")

    self:OnRefreshRedPacketData()

    --道具 金榜
    if self.RPInfo.packetClass == 1 then
        local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.REDPACKET_MONEY)
        self:SetImage(self.view.Img_Icon, iconData.AssetPath)
    elseif self.RPInfo.packetClass == 2 then
        local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.REDPACKET_CLOTHES)
        self:SetImage(self.view.Img_Icon, iconData.AssetPath)
    end

    --红包类型
    local message = self.RPInfo.message
    if not StringValid(self.RPInfo.message) then
        if self.RPInfo.sourceType == redPacketConst.RED_PACKET_SOURCE_TYPE.GUILD_DANCE then
            message = StringConst.Get("RED_PACKET_TEXT_1")
        else
            message = StringConst.Get("RED_DEFAULT_MESSAGE")
        end
    end

    local bIsNoEmoji = true
    if self.RPInfo and self.RPInfo.emojiInfo then
        bIsNoEmoji = false
    end

    if self.RPInfo.packetType == 1 then
        self.userWidget:Event_UI_Type(0, bIsNoEmoji)
        if bIsNoEmoji then
            self.view.Empty_Text:SetText(message)
        else
            local emojiID = self.RPInfo.emojiInfo.id
            local emoData = Game.TableData.GetChatStickerDataRow(emojiID)
            if emoData then
                self:SetImage(self.view.WBP_RedPacket_Emoj.Img_Emoj_lua, emoData["Icon"])
            end
            self.view.KGTextBlock_1:SetText(message)
        end
    elseif self.RPInfo.packetType == 2 then
        self.userWidget:Event_UI_Type(1, bIsNoEmoji)
        self.view.KGTextBlock_2:SetText(string.format(StringConst.Get("PLEASE_PRESS_AND_SAY"), message))
    else
        self.userWidget:Event_UI_Type(2, bIsNoEmoji)
        if self.RPInfo.currentStatus.senderInfo.id == Game.me.eid then
            --自己发的
            self.view.EditText_Passwd:SetText(self.RPInfo.passwd)
        else
            self.view.EditText_Passwd:SetText("")
        end
    end

    --聊天频道设置
    if params.packetChannel == Enum.EChatChannelData.WORLD then
        self.view.KGTextBlock_Channel:SetText(StringConst.Get("RED_CHANNEL_WORLD"))
    elseif params.packetChannel == Enum.EChatChannelData.TEAM or params.packetChannel == Enum.EChatChannelData.GROUP then
        self.view.KGTextBlock_Channel:SetText(StringConst.Get("RED_CHANNEL_TEAM"))
    elseif params.packetChannel == Enum.EChatChannelData.GUILD then
        self.view.KGTextBlock_Channel:SetText(StringConst.Get("RED_CHANNEL_GUILD"))
    elseif params.packetChannel == Enum.EChatChannelData.CHATROOM then
        self.view.KGTextBlock_Channel:SetText(StringConst.Get("SOCIAL_CHATROOM_SIMPLE_TYPETEXT"))
    end
end

function RedPacket_Panel:OnRefreshRedPacketData()
    local rolename = nil
    local soloHeadIcon = nil
    if self.RPInfo.sourceType == redPacketConst.RED_PACKET_SOURCE_TYPE.GUILD_DANCE then
        rolename = StringConst.Get("DANCE_NPC_NAME")
        soloHeadIcon = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.GUILD_DANCING_AUDREY).AssetPath
        self:SetImage(self.view.Img_Bg_1, StringConst.Get("RED_PACKET_BG_PATH")) -- 更换背景
    else
        local senderInfo = self.RPInfo.currentStatus.senderInfo
        soloHeadIcon = Game.RedPacketSystem:GetHeadIconFromSenderInfo(senderInfo)
        rolename = senderInfo.rolename
    end
    self:SetImage(self.view.Img_Head, soloHeadIcon) 		--头像
    self.view.KGTextBlock_Name:SetText(rolename)		--姓名
end

--- 此处为自动生成
function RedPacket_Panel:on_WBP_CloseCom_ClickEvent()
    self:CloseSelf()
end


--- 此处为自动生成
function RedPacket_Panel:on_Btn_Report_Clicked()
    Game.ReportSystem:ShowReportUI(Enum.EReportType.Redpacket, {ID = self.RPInfo.currentStatus.senderInfo.id, name = self.RPInfo.currentStatus.senderInfo.rolename})
end

--- 此处为自动生成
function RedPacket_Panel:on_Btn_Unlock_Clicked()
    Game.me:ReqReceiveRedPacket(self.RPInfo.uuid, self.RPInfo.packetChannel, self.view.EditText_Passwd:GetText())
end


--- 此处为自动生成
function RedPacket_Panel:on_Btn_Open_Clicked()
    Game.me:ReqReceiveRedPacket(self.RPInfo.uuid, self.RPInfo.packetChannel, "")
end

function RedPacket_Panel:OnReceiveRedPacketSuccess(RPInfo)
    local widget = self.userWidget
    -- 动画结束不关闭面板, 只留下ComMask节点作为背景用
    self:PlayAnimation(widget.Ani_redopen, nil, widget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

    self:StartTimer("OpenRedPacketAnim", function()
        RPInfo.backPanel = self
        Game.NewUIManager:OpenPanel(UIPanelConfig.RedPacket_RPRecordPanel, RPInfo)
    end, 350, 1)
end


--- 此处为自动生成
function RedPacket_Panel:on_Btn_Voice_Pressed()
    self.PressTime = import("GameplayStatics").GetRealTimeSeconds(_G.GetContextObject())
    -- self:StartTimer("PressTimer", function()
    -- end,  200, 1)
    if Game.ChatSystem:CheckChannelIsCanSendMessage(self.RPInfo.packetChannel) then
        if Game.TableData.GetChatChannelDataRow(self.RPInfo.packetChannel).canSendVoice == 0 then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_NOT_ALLOW_VOICE)
            return
        end
        Game.NewUIManager:OpenPanel(UIPanelConfig.ChatVoice_Panel, self.RPInfo.packetChannel, Enum.EChatTarget.Channel, true, true, self.RPInfo)
        self.IsRecording = true
    end
end

--- 此处为自动生成
function RedPacket_Panel:on_Btn_Voice_Released()
    self.IsRecording = false
    Game.EventSystem:Publish(_G.EEventTypes.REDPACKET_VOICE_BTN_RELEASE)
end

--- 此处为自动生成
function RedPacket_Panel:on_Btn_Voice_Hovered()
    if self.IsRecording then
        Game.EventSystem:Publish(_G.EEventTypes.REDPACKET_VOICE_BTN_HOVERED)
    end
end

--- 此处为自动生成
function RedPacket_Panel:on_Btn_Voice_Unhovered()
    if self.IsRecording then
        Game.EventSystem:Publish(_G.EEventTypes.REDPACKET_VOICE_BTN_UNHOVERED)
    end
end

function RedPacket_Panel:OnPasswdChanged(passwdText)
    local length = string.utf8len(passwdText)
    local maxLength = Game.TableData.GetConstDataRow("RED_PACKET_MAX_PASSWD_LENGTH")
    if length > maxLength then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_INPUT_MAX)
        local oldText = self.oldText or ""
        self.view.EditText_Passwd:SetText(oldText)
        return oldText
    end
    self.oldText = passwdText

    return passwdText
end

--- 此处为自动生成
---@param text FText
function RedPacket_Panel:on_EditText_Passwd_TextChanged(text)
    return self:OnPasswdChanged(text)
end

--- 此处为自动生成
---@param text FText
function RedPacket_Panel:on_EditText_Passwd_C7TextChanged(text)
    return self:OnPasswdChanged(text)
end

return RedPacket_Panel
