local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class RedPacket_BasicPanel : UIPanel
---@field view RedPacket_BasicPanelBlueprint
local RedPacket_BasicPanel = DefineClass("RedPacket_BasicPanel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")

local Game = Game   -- luacheck: ignore
local Enum = Enum   -- luacheck: ignore

-- 子UI的标题和tips
ChildUIInfoMap = ChildUIInfoMap or
{
    [UICellConfig.RedPacket_SelRP] =
    {
        Title = StringConst.Get("RED_PACKET_TITLE_1"),
        Tips = Enum.ETipsData["RED_PACKET_SYSTEM_DESC"],
    },
    [UICellConfig.RedPacket_GiveMoney] =
    {
        Title = StringConst.Get("RED_PACKET_TYPE_1"),
        Tips = Enum.ETipsData["GOLD_RED_PACKET_DESC"],
    },
    [UICellConfig.RedPacket_GiveSkin] =
    {
        Title = StringConst.Get("RED_PACKET_TYPE_2"),
        Tips = Enum.ETipsData["FASHION_PACKET_DESC"],
    },
    [UICellConfig.RedPacket_SelectSkin] =
    {
        Title = StringConst.Get("RED_PACKET_TITLE_2"),
        Tips = Enum.ETipsData["CHOOSE_FASHION_DESC"],
    },
    [UICellConfig.RedPacket_Record] =
    {
        Title = StringConst.Get("RED_PACKET_TITLE_1"),
        Tips = Enum.ETipsData["RED_PACKET_SYSTEM_DESC"],
    },
    [UICellConfig.RedPacket_AllCurRP] =
    {
        Title = StringConst.Get("RED_PACKET_TITLE_1"),
        Tips = Enum.ETipsData["RED_PACKET_SYSTEM_DESC"],
    }
}

RedPacket_BasicPanel.eventBindMap = {
    [EEventTypesV2.UPDATE_CURRENCY_LIST_PANEL] = "UpdateRedpacketCurrencyData",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RedPacket_BasicPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RedPacket_BasicPanel:InitUIData()
    self.ShowUIName = ""
end

--- UI组件初始化，此处为自动生成
function RedPacket_BasicPanel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function RedPacket_BasicPanel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Rule.OnClicked, "on_Btn_Rule_Clicked")
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RedPacket_BasicPanel:InitUIView()
end

---面板打开的时候触发
function RedPacket_BasicPanel:OnRefresh(ChildUIName, Params)
    if ChildUIName == nil then
        self:RefreshChildUI(UICellConfig.RedPacket_SelRP)
    else
        self:RefreshChildUI(ChildUIName, Params)
    end
end

function RedPacket_BasicPanel:OnClose()
    self.ShowUIName = ""
    Game.RedPacketSystem:ResetData()
end

--- 此处为自动生成
function RedPacket_BasicPanel:on_Btn_Rule_Clicked()
    Game.TipsSystem:ShowTips(ChildUIInfoMap[self.ShowUIName].Tips, self.view.Btn_Rule:GetCachedGeometry())
end

function RedPacket_BasicPanel:RefreshChildUI(UIName, Params)
    if self.ShowUIName ~= UIName then
        if self.ShowUIName ~= "" then
            self:CloseComponent(self.ShowUIName)
        end
        self:OpenComponent(UIName, self.view.CP_Child, Params)
        self.view.Txt_Title:SetText(ChildUIInfoMap[UIName].Title)
        self.ShowUIName = UIName
    end
end

--- 此处为自动生成
function RedPacket_BasicPanel:on_Btn_ClickArea_Clicked()
    if self.ShowUIName == UICellConfig.RedPacket_SelectSkin then
        self:RefreshChildUI(UICellConfig.RedPacket_GiveSkin)
    elseif self.ShowUIName == UICellConfig.RedPacket_GiveMoney then
        self:RefreshChildUI(UICellConfig.RedPacket_SelRP)
    elseif self.ShowUIName == UICellConfig.RedPacket_GiveSkin then
        self:RefreshChildUI(UICellConfig.RedPacket_SelRP)
    else
        self:CloseSelf()
    end
end

function RedPacket_BasicPanel:UpdateRedpacketCurrencyData()
    local childUICom = self:GetComponentByCellId(self.ShowUIName)
    if childUICom and childUICom.UpdateRedpacketCurrencyData then
        childUICom:UpdateRedpacketCurrencyData()
    end
end

return RedPacket_BasicPanel
