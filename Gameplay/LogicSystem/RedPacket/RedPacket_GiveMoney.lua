local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local RedPacket_Coin_Item = kg_require("Gameplay.LogicSystem.RedPacket.RedPacket_Item.RedPacket_Coin_Item")
local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local RedPacket_Comment_Item = kg_require("Gameplay.LogicSystem.RedPacket.RedPacket_Item.RedPacket_Comment_Item")
local RedPacket_Code_Item = kg_require("Gameplay.LogicSystem.RedPacket.RedPacket_Item.RedPacket_Code_Item")
local RedPacket_ListRight_Item = kg_require("Gameplay.LogicSystem.RedPacket.RedPacket_Item.RedPacket_ListRight_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local redPacketConst = kg_require("Shared.Const.RedPacketConst")


local Game = Game
local Enum = Enum

---@class RedPacket_GiveMoney : UIComponent
---@field view RedPacket_GiveMoneyBlueprint
local RedPacket_GiveMoney = DefineClass("RedPacket_GiveMoney", UIComponent)

RedPacket_GiveMoney.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RedPacket_GiveMoney:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RedPacket_GiveMoney:InitUIData()
    self.MoneyID = redPacketConst.RED_PACKET_MONEY_TYPE
end

--- UI组件初始化，此处为自动生成
function RedPacket_GiveMoney:InitUIComponent()
    ---@type UIComButton
    self.WBP_Give_WBP_ComBtnCom = self:CreateComponent(self.view.WBP_Give.WBP_ComBtn, UIComButton)
    ---@type RedPacket_Coin_Item
    self.WBP_Give_WBP_MoneyTotalCom = self:CreateComponent(self.view.WBP_Give.WBP_MoneyTotal, RedPacket_Coin_Item)
    ---@type RedPacket_Comment_Item
    self.WBP_MessageCom = self:CreateComponent(self.view.WBP_Message, RedPacket_Comment_Item)
    ---@type RedPacket_Code_Item
    self.WBP_PassWordCom = self:CreateComponent(self.view.WBP_PassWord, RedPacket_Code_Item)
    ---@type UIComNumberSlider
    self.WBP_Num_WBP_SliderCom = self:CreateComponent(self.view.WBP_Num.WBP_Slider, UIComNumberSlider)
    ---@type UIComNumberSlider
    self.WBP_PriceCom = self:CreateComponent(self.view.WBP_Price, UIComNumberSlider)
    ---@type RedPacket_ListRight_Item
    self.WBP_TypeCom = self:CreateComponent(self.view.WBP_Type, RedPacket_ListRight_Item)
end

---UI事件在这里注册，此处为自动生成
function RedPacket_GiveMoney:InitUIEvent()
    self:AddUIEvent(self.WBP_Give_WBP_ComBtnCom.onClickEvent, "on_WBP_Give_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_PriceCom.onValueChange, "on_WBP_PriceCom_ValueChange")
    self:AddUIEvent(self.WBP_Num_WBP_SliderCom.onValueChange, "on_WBP_Num_WBP_SliderCom_ValueChange")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RedPacket_GiveMoney:InitUIView()
end

---组件刷新统一入口
function RedPacket_GiveMoney:Refresh(params)
    self.WBP_TypeCom:Refresh(redPacketConst.LIST_RIGHT_ITEM_TYPE.RED_PACKET_TYPE)
    self.WBP_Give_WBP_ComBtnCom:SetName(StringConst.Get("RED_PACKET_BUTTON_GIVE"))
    local DefaultPrice = Game.TableData.GetConstDataRow("RED_PACKET_MIN_MONEY_NUM")
    local DefaultNum = Game.TableData.GetConstDataRow("RED_PACKET_DEFAULT_PACK_NUM")
    local MinNum = Game.TableData.GetConstDataRow("RED_PACKET_MIN_PACK_NUM")
    local MaxNum = Game.TableData.GetConstDataRow("RED_PACKET_MAX_PACK_NUM")
    if params then
        Game.RedPacketSystem:SetRPClass(params.packetClass)
        Game.RedPacketSystem:SetRPChannelIndex(params.packetChannel)
        Game.RedPacketSystem:SetRTSendForm(params.packetType)
        DefaultNum = params.packNum
        DefaultPrice = params.moneyNum
    else
        local Channel = Game.RedPacketSystem:GetRPChannelIndex()
        if Channel == Enum.EChatChannelData.TEAM or Channel == Enum.EChatChannelData.GROUP then
            if Game.TeamSystem:IsInTeam() then
                DefaultNum = Game.TableData.GetConstDataRow("RED_PACKET_TEAM_DEFAULT_PACK_NUM")
                MinNum = Game.TableData.GetConstDataRow("RED_PACKET_TEAM_MIN_PACK_NUM")
                MaxNum = Game.TableData.GetConstDataRow("RED_PACKET_TEAM_MAX_PACK_NUM")
            elseif Game.TeamSystem:IsInGroup() then
                DefaultNum = Game.TableData.GetConstDataRow("RED_PACKET_GROUP_DEFAULT_PACK_NUM")
                MinNum = Game.TableData.GetConstDataRow("RED_PACKET_GROUP_MIN_PACK_NUM")
                MaxNum = Game.TableData.GetConstDataRow("RED_PACKET_GROUP_MAX_PACK_NUM")
            end
        end
    end
    self.WBP_PassWordCom:Refresh(
            {
                InputLimit= 6,
                HintText = StringConst.Get("RED_PACKET_HINT_PASSWD"),
                DefaultInputText = ""
            })

    --总价格
    local maxPrice = Game.TableData.GetConstDataRow("RED_PACKET_MAX_MONEY_NUM")
    local currentMoneyNum = Game.BagSystem:GetItemCount(redPacketConst.RED_PACKET_MONEY_TYPE)
    if currentMoneyNum < maxPrice then
        maxPrice = currentMoneyNum
    end

    local minPrice = Game.TableData.GetConstDataRow("RED_PACKET_MIN_MONEY_NUM")
    if DefaultPrice < minPrice then
        DefaultPrice = minPrice
    end
    if maxPrice < minPrice then
        maxPrice = minPrice
    end

    self.WBP_PriceCom:Refresh(DefaultPrice, minPrice, maxPrice, 1)

    self.WBP_Num_WBP_SliderCom:Refresh(DefaultNum, MinNum, MaxNum, 1)

    self.RPNum = DefaultNum

    self.WBP_MessageCom:Refresh(
            {
                InputLimit= 20,
                HintText = StringConst.Get("RED_PACKET_GIFT_HINT_WORD"),
                DefaultInputText = "",
                OnValueChangedCallback = self.MessageInputChange
            })

    --货币icon
    self:UpdateRPPrice(DefaultPrice)
    self:SetImage(self.view.Img_Coin1, Game.UIIconUtils.GetIconByItemId(redPacketConst.RED_PACKET_MONEY_RETURN_TYPE))
    self:SetImage(self.view.Img_Coin2, Game.UIIconUtils.GetIconByItemId(redPacketConst.RED_PACKET_MONEY_TYPE))
end

function RedPacket_GiveMoney:UpdateRPNum(Count)
    self.RPNum = Count
end

function RedPacket_GiveMoney:UpdateRPPrice(Count)
    self.RPPrice = Count
    self.view.KGTextBlock_BoundPrice:SetText(self.RPPrice)

    self.WBP_Give_WBP_MoneyTotalCom:OnRefresh(self.MoneyID, Count)
end

function RedPacket_GiveMoney:UpdateRedpacketCurrencyData()
    self.WBP_Give_WBP_MoneyTotalCom:UpdateCurrencyData()
end

--- 此处为自动生成
function RedPacket_GiveMoney:on_WBP_Give_WBP_ComBtnCom_ClickEvent()
    local passwd = self.WBP_PassWordCom:GetInputText()
    local message = self.WBP_MessageCom:GetInputText()
    if Game.RedPacketSystem:GetRTSendForm() == redPacketConst.RED_PACKET_TYPE.SECRET_WORD then
        if message == "" then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.RED_PACKET_NO_SECRET_WORD)
            return
        else
            passwd = message
        end
    end
    local redPacketInfo =
    {
        packetClass = Game.RedPacketSystem:GetRPClass(),
        packetType = Game.RedPacketSystem:GetRTSendForm(),
        packetChannel = Game.RedPacketSystem:GetRPChannelIndex(),
        moneyNum = self.RPPrice,
        packNum = self.RPNum,
        message = message,
        passwd = passwd,
        emojiInfo = self.WBP_MessageCom.emojiInfo,
    }
    Game.RedPacketSystem:EnterOpenRPProcess(redPacketInfo)
end


--- 此处为自动生成
---@param value number
function RedPacket_GiveMoney:on_WBP_PriceCom_ValueChange(value)
    self:UpdateRPPrice(value)
end

--- 此处为自动生成
---@param value number
function RedPacket_GiveMoney:on_WBP_Num_WBP_SliderCom_ValueChange(value)
    self:UpdateRPNum(value)
end

return RedPacket_GiveMoney
