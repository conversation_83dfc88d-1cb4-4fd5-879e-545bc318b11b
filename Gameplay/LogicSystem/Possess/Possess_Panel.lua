local Possess_Intro = kg_require("Gameplay.LogicSystem.Possess.Possess_Intro")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

---@class Possess_Panel : UIPanel
---@field view Possess_PanelBlueprint
local Possess_Panel = DefineClass("Possess_Panel", UIPanel)

Possess_Panel.__EPossessTargetType__ = {
    NOT_SELECT = 0, -- 未选中
    ALLOW = 1, -- 可以附身
    FORBIDDEN = 2, -- 不可附身
}

Possess_Panel.eventBindMap = {
    [EEventTypesV2.ON_POSSESS_TARGET_CHANGE] = "OnPossessTargetChange",
    [EEventTypesV2.ON_POSSESS_STOP_SEARCH] = "OnPossessStopSearch",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Possess_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Possess_Panel:InitUIData()
    self.animTargetType = nil
end

--- UI组件初始化，此处为自动生成
function Possess_Panel:InitUIComponent()
    ---@type Possess_Intro
    self.WBP_Possess_IntroCom = self:CreateComponent(self.view.WBP_Possess_Intro, Possess_Intro)
end

---UI事件在这里注册，此处为自动生成
function Possess_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Possess_Panel:InitUIView()
end

---面板打开的时候触发
function Possess_Panel:OnRefresh(...)
    -- 信息板
    self.WBP_Possess_IntroCom:SetVisible(false)

    -- 准心样式
    self:setAnimTargetType(self.__EPossessTargetType__.NOT_SELECT)

    -- 提示
    self:PlayAnimation(self.view.Ani_Title_Fadein)
end

---射线目标变化
---@param entityID
function Possess_Panel:OnPossessTargetChange(entityID)
    self.WBP_Possess_IntroCom:SetIntro(entityID)

    if entityID == 0 then
        self:setAnimTargetType(self.__EPossessTargetType__.NOT_SELECT)
    else
        -- todo:是否可选中
        self:setAnimTargetType(self.__EPossessTargetType__.ALLOW)
    end
end

function Possess_Panel:OnPossessStopSearch()
    local EPossessTargetType = self.__EPossessTargetType__
    local animPoint = self.view.WBP_Possess_AimPoint

    if self.animTargetType == EPossessTargetType.NOT_SELECT then
        self:PlayAnimation(animPoint.Ani_Content1_Fadeout, nil, animPoint)
    elseif self.animTargetType == EPossessTargetType.ALLOW then
        self:PlayAnimation(animPoint.Ani_Content2_Fadeout, nil, animPoint)
    elseif self.animTargetType == EPossessTargetType.FORBIDDEN then
        self:PlayAnimation(animPoint.Ani_Content3_Fadeout, nil, animPoint)
    end

    self.WBP_Possess_IntroCom:SetIntro(0)

    self:PlayAnimation(self.view.Ani_Title_Fadeout, function()
        self:CloseSelf()
    end)
end

---@private
function Possess_Panel:setAnimTargetType(newType)
    Log.DebugFormat("[setAnimTargetType] form %d to %d", self.animTargetType, newType)
    local EPossessTargetType = self.__EPossessTargetType__
    local animPoint = self.view.WBP_Possess_AimPoint

    -- 动画
    if self.animTargetType == EPossessTargetType.NOT_SELECT then
        self:PlayAnimation(animPoint.Ani_Content1_Fadeout, nil, animPoint)
    elseif self.animTargetType == EPossessTargetType.ALLOW then
        self:PlayAnimation(animPoint.Ani_Content2_Fadeout, nil, animPoint)
    elseif self.animTargetType == EPossessTargetType.FORBIDDEN then
        self:PlayAnimation(animPoint.Ani_Content3_Fadeout, nil, animPoint)
    end

    self.animTargetType = newType
    self.view.WBP_Possess_AimPoint:BP_SetSighting(newType)

    -- 动画
    if self.animTargetType == EPossessTargetType.NOT_SELECT then
        self:PlayAnimation(animPoint.Ani_Content1_Fadein, nil, animPoint)
    elseif self.animTargetType == EPossessTargetType.ALLOW then
        self:PlayAnimation(animPoint.Ani_Content2_Fadein, nil, animPoint)
    elseif self.animTargetType == EPossessTargetType.FORBIDDEN then
        self:PlayAnimation(animPoint.Ani_Content3_Fadein, nil, animPoint)
    end
end

return Possess_Panel
