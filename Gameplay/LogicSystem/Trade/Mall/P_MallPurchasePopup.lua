local ESlateVisibility           = import("ESlateVisibility")

local sharedConst = kg_require("Shared.Const")

---@class P_MallPurchasePopup:UIController
local P_MallPurchasePopup        = DefineClass("P_MallPurchasePopup", UIController)
local StringConst                = require "Data.Config.StringConst.StringConst"
local P_ComCurrency              = kg_require "Gameplay.LogicSystem.CommonUI.P_ComCurrency"

P_MallPurchasePopup.eventBindMap = {
    [EEventTypesV2.RECEIVE_MONEY_CHANGE] = "RefreshMoneyUI",
}

function P_MallPurchasePopup:OnCreate()
    self.View.WBP_ComPopupL.WBP_ComPopupTitle.Text_Title:SetText(StringConst.Get("STORE_PURCHASE")) ---"购买"
    self.Currency1 = self:BindComponent(self.View.WBP_ComCurrency1, P_ComCurrency)
    self.Currency2 = self:BindComponent(self.View.WBP_ComCurrency2, P_ComCurrency)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComSlider.Slider_Delete, self.Click_WBP_ComSlider_Slider_Delete)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComSlider.Slider_Add, self.Click_WBP_ComSlider_Slider_Add)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComSlider.Slider_Top, self.Click_WBP_ComSlider_Slider_Top)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComSlider.btn_Input, self.Click_WBP_ComSlider_btn_Input)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_Cancel.Btn_Com, self.Click_WBP_Cancel_Btn_Com)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_Purchase.Btn_Com, self.Click_WBP_Purchase_Btn_Com)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComPopupL.WBP_ComPopupTitle.WBP_ComBtnClose.Button, self.Close)
end

function P_MallPurchasePopup:CloseUI()
    UI.HideUI("P_MallPurchasePopup")
end

function P_MallPurchasePopup:Close()
    local _isClosing = self._isClosing
    self:CloseUI()
    if _isClosing then
        UIController.Close(self)
    end
end

function P_MallPurchasePopup:OnRefresh(goodId)
    self.goodId = goodId
    UI.ShowUI("P_TradeTips2", { ID = goodId, Style = 'Mall' })

    self.View.WBP_Cancel.Text_Com:SetText(StringConst.Get("CANCLE"))           --- "取消"
    self.View.WBP_Purchase.Text_Com:SetText(StringConst.Get("STORE_PURCHASE")) ---'购买'

    self:SetLimitBoughtNum()
    self:SetOwnNumUIState()
    self:SetRefreshTypeTip()

    self.BuyNum = 1
    self:SetNumInputUIState()
    self:InitCurrencyData()
    self:RefreshMoneyUI()
end

function P_MallPurchasePopup:InitCurrencyData()
    self.MoneyIDList = {}
    self.PriceList = {}
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.goodId)
    for key, value in ksbcpairs(NPCShopTableData.TokenIDs) do
        table.insert(self.MoneyIDList, key)
        table.insert(self.PriceList, value)
    end
end

function P_MallPurchasePopup:UpdateLimitCount()
    self:SetLimitBoughtNum()
    self:SetOwnNumUIState()
end

function P_MallPurchasePopup:SetLimitBoughtNum()
    local bAllServer, UpdateType, AllLimitNum = Game.DepartmentStoreSystem:GetLimitInfo(self.goodId)
    if AllLimitNum == -1 then
        --不限购
        self.View.HB_Limit:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.View.HB_Limit:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        if bAllServer then
            self.View.Text_LimitType:SetText(StringConst.Get("STORE_BUY_ALLSERVER_LIMIT"))
        else
            if UpdateType == Game.DepartmentStoreSystem.LimitRefreshType.Permanent then
                self.View.Text_LimitType:SetText(StringConst.Get("PERMANENT_PURCHASE_LIMIT")) ---"永久限购"
            else
                local TypeString = Game.DepartmentStoreSystem.RefreshType[UpdateType]
                self.View.Text_LimitType:SetText(StringConst.Get("STORE_EVERY") ..
                    TypeString .. StringConst.Get("STORE_BUY_LIMIT")) -- "每" "限购"
            end
        end
        self.View.Text_BoughtLimit:SetText(Game.DepartmentStoreSystem:GetBoughtCount(self.goodId) .. "/" .. AllLimitNum)
    end
end

function P_MallPurchasePopup:SetRefreshTypeTip()
    local _, UpdateType, AllLimitNum = Game.DepartmentStoreSystem:GetLimitInfo(self.goodId)
    if AllLimitNum == -1 then
        self.View.RefreshTimeTip:SetText(StringConst.Get("NO_PURCHASE_TIME_LIMIT"))
    else
        local RefreshTypeString = ''
        if UpdateType == Game.DepartmentStoreSystem.LimitRefreshType.Permanent then
            RefreshTypeString = string.format(StringConst.Get("PERMANENT_PURCHASE_LIMIT"))
        elseif UpdateType == Game.DepartmentStoreSystem.LimitRefreshType.Day then
            RefreshTypeString = string.format(StringConst.Get("REFRESH_EVERY_MORNING"),
                sharedConst.REFRESH_HOUR_PER_DAY) ---"每日早上%s点刷新"
        elseif UpdateType == Game.DepartmentStoreSystem.LimitRefreshType.Week then
            RefreshTypeString = string.format(StringConst.Get("REFRESH_WEEK_MORNING"),
                sharedConst.REFRESH_DAY_PER_WEEK,
                sharedConst.REFRESH_HOUR_PER_DAY) --- "每周%s早上%s点刷新"
        elseif UpdateType == Game.DepartmentStoreSystem.LimitRefreshType.Month then
            RefreshTypeString = string.format(StringConst.Get("REFRESH_MONTH_MORNING"),
                sharedConst.REFRESH_HOUR_PER_DAY) --- "每月1号早上%s点刷新"
        end
        self.View.RefreshTimeTip:SetText(RefreshTypeString)
    end
end

function P_MallPurchasePopup:SetOwnNumUIState()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.goodId)
    self.View.Text_OwnNum:SetText(Game.BagSystem:GetItemCount(NPCShopTableData.ItemID))
end

function P_MallPurchasePopup:SetNumInputUIState()
    if Game.DepartmentStoreSystem:GetLimitBuyCount(self.goodId) == 1 then
        self.View.WBP_ComSlider:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.View.WBP_ComSlider:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.View.WBP_ComSlider.CountText:SetText(self.BuyNum)
    end
end

function P_MallPurchasePopup:RefreshMoneyUI()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.goodId)
    self.View.WBP_ComSlider.CountText:SetText(self.BuyNum)
    self.NeedMoneyNum = 0
    if self.MoneyIDList[2] then
        self.View.WBP_ComCurrency2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        --两种货币的指定原价
        if self.PriceList and self.PriceList[1] then
            self.Currency1:SetData(self.MoneyIDList[1], self.BuyNum * self.PriceList[1])
        end
        if self.PriceList and self.PriceList[2] then
            self.Currency2:SetData(self.MoneyIDList[2], self.BuyNum * self.PriceList[2])
        end
        self.View.WBP_ComCurrency1:SetPriceOff(false)
        self.View.WBP_ComCurrency2:SetPriceOff(false)
    else
        self.View.WBP_ComCurrency2:SetVisibility(ESlateVisibility.Collapsed)
      local PriceType = Game.DepartmentStoreSystem:GetPriceType(self.goodId)
        if PriceType == Enum.SHOP_GOODS_PRICE_TYPE.Discount then
            --打折
            if Game.DepartmentStoreSystem:IsSoldOut(self.goodId) then
                self.View.WBP_ComCurrency1:SetPriceOff(false)
                self.Currency1:SetData(self.MoneyIDList[1], self.NeedMoneyNum)
            else
                self.View.WBP_ComCurrency1:SetPriceOff(true)
                local Price = Game.DungeonBattleStatisticsSystem:GetFormatNumberString(NPCShopTableData.DiscountPrice == - 1 and 0 or NPCShopTableData.DiscountPrice)
                self.NeedMoneyNum = self.BuyNum * Price
                self.Currency1:SetData(self.MoneyIDList[1], self.NeedMoneyNum, false, true,
                    self.BuyNum * self.PriceList[1])
            end
        elseif PriceType == Enum.SHOP_GOODS_PRICE_TYPE.OriginalPrice then
            --原价
            if self.PriceList and self.PriceList[1] then
                self.Currency1:SetData(self.MoneyIDList[1], self.BuyNum * self.PriceList[1])
            end
            self.View.WBP_ComCurrency1:SetPriceOff(false)
        end
    end
end

function P_MallPurchasePopup:Click_WBP_ComSlider_Slider_Delete()
    if self.BuyNum > 1 then
        self.BuyNum = self.BuyNum - 1
        self:RefreshMoneyUI()
    else
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MIN)
    end
end

function P_MallPurchasePopup:Click_WBP_ComSlider_Slider_Add()
    local Count = self.BuyNum + 1
    local limitNum = Game.DepartmentStoreSystem:GetCanBuyMaxNum(self.goodId, Game.TableData.GetConstDataRow("SHOP_MAX_PURCHASE_NUM"))
    if Count > limitNum then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MAX)
        return
    end

    self.BuyNum = self.BuyNum + 1
    self:RefreshMoneyUI()
end

function P_MallPurchasePopup:Click_WBP_ComSlider_Slider_Top()
    self.BuyNum = Game.DepartmentStoreSystem:GetCanBuyMaxNum(self.goodId, Game.TableData.GetConstDataRow("SHOP_MAX_PURCHASE_NUM"))
    if self.BuyNum <= 0 then
        self.BuyNum = 1
    end
    self:RefreshMoneyUI()
end

function P_MallPurchasePopup:Click_WBP_ComSlider_btn_Input()
    local limitNum = math.max(Game.DepartmentStoreSystem:GetCanBuyMaxNum(self.goodId,
        Game.TableData.GetConstDataRow("SHOP_MAX_PURCHASE_NUM")), 1)
	local viewportPosition, anchors = Game.UITipsPosAutoFollowUtils.GetPanelAdaptPosFollowWidget(self.View.WBP_ComSlider.btn_Input)
    UI.ShowUI('ComNumInput_Panel', {
        SelectableMaximum = limitNum,
        FinishCallback = function(Succ, InputNum)
            if Succ then
                self.BuyNum = math.max(InputNum, 1)
                self:RefreshMoneyUI()
            end
        end,
		viewportPosition = viewportPosition,
		anchors = anchors,
    })
end

function P_MallPurchasePopup:Click_WBP_Cancel_Btn_Com()
    self:CloseUI()
end

function P_MallPurchasePopup:Click_WBP_Purchase_Btn_Com()
    local RemainLimitBuyCount = Game.DepartmentStoreSystem:GetRemainLimitBuyCount(self.goodId)
    if RemainLimitBuyCount ~= -1 then
        if RemainLimitBuyCount < self.BuyNum then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NPC_SHOP_BEYOND_LIMIT_COUNT)
            return
        end
    end
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.goodId)
    if Game.DepartmentStoreSystem:IsFree(self.goodId) then
        Game.DepartmentStoreSystem:IntoPurchaseProcess(self.goodId, NPCShopTableData.ShopID, self.BuyNum)
    else
        Game.DepartmentStoreSystem:IntoPurchaseProcess(self.goodId, NPCShopTableData.ShopID, self.BuyNum)
    end
    self:CloseUI()
end

function P_MallPurchasePopup:EscOnClickEvent()
    self:CloseUI()
end

return P_MallPurchasePopup
