local TradeManager = DefineSingletonClass("TradeManager", SystemBase)

-- STALL_PUBLICITY_TIME

TradeManager.Type = {
    WorldAuction = 1,
    GuildAuction = 2
}

TradeManager.State = {
    Prepare = 1,
    InProgress = 2,
    End = 3,
    None = 4
}

TradeManager.AuctionHistoryNum = 30

function TradeManager:onCtor()

end

function TradeManager:onDestroy()

end

function TradeManager:onInit()
    self.FristTab = -1
    self.SecTab = -1
    self.ThirdTab = -1
    self.SelID = -1
    self.HasRecorded = false
    self.StallTabIndexMap = {}
end

function TradeManager:onUnInit()

end

--返回登录
function TradeManager:OnBackToLogin()
end

--返回选角
function TradeManager:OnBackToSelectRole()
end

function TradeManager:RecordCurSelState(FristTab, SecTab, ThirdTab, SelID)
    self.FristTab = FristTab
    self.SecTab = SecTab
    self.ThirdTab = ThirdTab
    self.SelID = SelID
    self.HasRecorded = true
end

function TradeManager:ClearCurSelState()
    self.HasRecorded = false
end

function TradeManager:GetCurSelData()
    return self.FristTab, self.SecTab, self.ThirdTab, self.SelID
end

function TradeManager:GetCurSelState()
    return self.HasRecorded
end

function TradeManager:FormatCurrency(Count)
    if Count >= 10000 then
        return (Count % 10000) .. "万"
    else
        return Count
    end
end

function TradeManager:InitAllTabData()
    --商城数据
    local MallTabData = {}
    local MallData = Game.TableData.GetMallShopDataTable()
    for key, value in ksbcpairs(MallData) do
        table.insert(MallTabData, {
            ID = key,
            Name = value.Name,
            Children = {}
        })
    end
    table.sort(MallTabData, function(a, b)
        return MallData[a.ID].Order < MallData[b.ID].Order
    end)
    --百货数据
    local ShopTabData = {}
    local ShopData = Game.TableData.GetNpcShopDataTable()
    for key, value in ksbcpairs(ShopData) do
        if value.ShopType == 1 and value.ShowType == 1 then
            if Game.ModuleLockSystem:CheckModuleUnlockByEnum(value.ModuleEnum, false) then
                table.insert(ShopTabData,
                    { ID = key, Name = value.Name, CurrencyType = value.CurrencyType, Children = {} })
            end
        end
    end
    table.sort(ShopTabData, function(a, b)
        return ShopData[a.ID].Order < ShopData[b.ID].Order
    end)
    --交易所数据
    local ExchangeData = {}
    local ExchangeFirstTab = Game.TableData.Get_StallTabLevelData()[1]
    local ExchangeSecTab = Game.TableData.Get_StallTabLevelData()[2]
    for key, value in ksbcpairs(ExchangeFirstTab) do
        table.insert(ExchangeData, { ID = value.ID, Name = value.TabName, Children = {} })
    end
    table.sort(ExchangeData, function(a, b)
        return a.ID < b.ID
    end)
    for key, value in ksbcpairs(ExchangeSecTab) do
        local TabData = Game.TableData.Get_StallTypeData()[value.TabLevel][value.ID]
        if TabData and #TabData > 0 then
            table.insert(ExchangeData[value.TabLevel].Children, { ID = value.ID, Name = value.TabName, Children = {} })
            self.StallTabIndexMap[value.ID] = key
        end
    end

    return {
        { ID = Enum.EFunctionInfoData["MODULE_LOCK_SHOPPING_MALL"], Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_SHOPPING_MALL"]).FunctionInfo, Children = MallTabData },
        { ID = Enum.EFunctionInfoData["MODULE_LOCK_SHOP"], Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_SHOP"]).FunctionInfo, Children = ShopTabData },
        { ID = Enum.EFunctionInfoData["MODULE_LOCK_EXCHANGE_HOUSE"], Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_EXCHANGE_HOUSE"]).FunctionInfo, Children = ExchangeData },
        { ID = Enum.EFunctionInfoData["MODULE_LOCK_AUCTION"], Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_AUCTION"]).FunctionInfo, Children = {} },
    }
end

function TradeManager:GetTabData()
    return self.TabList
end

return TradeManager
