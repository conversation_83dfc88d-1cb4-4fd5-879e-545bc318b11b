local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"
---@class P_DepartmentStoreItem : UIController
--- @field public View WBP_TradeGoodsItemHView
local P_DepartmentStoreItem = DefineClass("P_DepartmentStoreItem", UIComponent)
local P_TradeCurrency = kg_require "Gameplay.LogicSystem.Trade.P_TradeCurrency"
local ItemTrade = kg_require "Gameplay.LogicSystem.Item.ItemTrade"

function P_DepartmentStoreItem:OnCreate()
    self.Data = nil
    self.MoneyIDList = {}
    self.PriceList = {}
    self.LTTypeList = 
    {
        None = 0,
        Discount = 1, --折扣
        New = 2 --新
    }
    self.RTTypeList = 
    {
        None = 0,
        LimitTime = 1, --限时
    }
end

function P_DepartmentStoreItem:Refresh(GoodsID, Sel)
    self.GoodsID = GoodsID
    self.Sel = Sel
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodsID)
    local ItemTableData = Game.TableData.GetItemNewDataRow(NPCShopTableData.ItemID)
    self.MoneyIDList = {}
    self.PriceList = {}
    for key, value in ksbcpairs(NPCShopTableData.TokenIDs) do
        table.insert(self.MoneyIDList, key)
        table.insert(self.PriceList, value)
    end
    self:SetPriceUIState()
    self:SetLimitTypeUIState()
    --商品下架时间
    self:SetLimitTimerUIState()
    --Limit and SoldOutLImit
    self:SetLockOrSoldOutUIState()
    --商品icon
    self:SetImage(self.View.Img_Goods, Game.UIIconUtils.GetIconByItemId(NPCShopTableData.ItemID, Enum.EUIIconType.Large))
    if Game.DepartmentStoreSystem:GetbShowItemID() == false then
        self.View.Text_GoodsName:SetText(ItemTableData.itemName)
    else
        self.View.Text_GoodsName:SetText(ItemTableData.itemName.."("..NPCShopTableData.ItemID..")")
    end
    --货币icon
    if self.MoneyIDList[1] then
        local ItemTableData = Game.TableData.GetItemNewDataRow(self.MoneyIDList[1])
        local IconPath = ""
        if ItemTableData then
            IconPath = Game.UIIconUtils.GetIconByItemId(self.MoneyIDList[1])
        end
        if IconPath then
            self:SetImage(self.View.WBP_ComCurrency1.Img_Currency, IconPath)
        end
    end
    if self.MoneyIDList[2] then
        local ItemTableData = Game.TableData.GetItemNewDataRow(self.MoneyIDList[2])
        local IconPath = ""
        if ItemTableData then
            IconPath = Game.UIIconUtils.GetIconByItemId(self.MoneyIDList[2])
        end
        if IconPath then
            self:SetImage(self.View.WBP_ComCurrency2.Img_Currency, IconPath)
        end
    end
    --UI样式
    self.View:SetSelect(Sel)
    self.View:SetStatus(false)
    --品质
    local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData["SHOP_ITEM_QUAILITY_"..ItemTableData.quality])
    self:SetImage(self.View.Img_QualityBg, iconData.AssetPath)
    self.View:SetProperty(self.LTType, self.RTType, self.PriceType , self.bLimit, 
        Game.DepartmentStoreSystem:IsUnlocked(self.GoodsID) == true and Game.DepartmentStoreSystem:GetRemainLimitBuyCount(self.GoodsID) ~= 0)
end

function P_DepartmentStoreItem:OnClick_Btn_ClickArea()
    
end

function P_DepartmentStoreItem:SetLockOrSoldOutUIState()
    local ShowLockCondition = Game.DepartmentStoreSystem:GetLockCondition(self.GoodsID)
    if Game.DepartmentStoreSystem:IsUnlocked(self.GoodsID) == false and #ShowLockCondition > 0 then
        --未解锁
        self:RefreshLockUI(self.View.WBP_UnlockorSoldOut1, ShowLockCondition[1])
        self:RefreshLockUI(self.View.WBP_UnlockorSoldOut2, ShowLockCondition[2])
    elseif Game.DepartmentStoreSystem:GetRemainLimitBuyCount(self.GoodsID) == 0 then
         --售罄
         self.View.WBP_UnlockorSoldOut1.Text_UnlockorSoldOut:SetText("售罄")
         self.View.WBP_UnlockorSoldOut1.Text_Unlock_LN:SetText(StringConst.Get("STORE_SOLD_OUT_LUEN"))
         self.View.WBP_UnlockorSoldOut1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
         self.View.WBP_UnlockorSoldOut1:Event_UI_Color(0)
         self:RefreshLockUI(self.View.WBP_UnlockorSoldOut2, nil)
    else
        self:RefreshLockUI(self.View.WBP_UnlockorSoldOut1, nil)
        self:RefreshLockUI(self.View.WBP_UnlockorSoldOut2, nil)
    end
end

function P_DepartmentStoreItem:RefreshLockUI(Widget, Data)
    if Data then
        Widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        Widget.Text_UnlockorSoldOut:SetText(Data.str)
        Widget:Event_UI_Color(1)
        if Data.Type == "SHOP_SOLDOUT" then
            Widget.Text_Unlock_LN:SetText(StringConst.Get("STORE_PURCHASE_LIMIT_LUEN"))
        elseif Data.Type == "CLASS" then
            Widget.Text_Unlock_LN:SetText(StringConst.Get("STORE_CLASS_LUEN"))
        elseif Data.Type == "LEVELUP" then
            Widget.Text_Unlock_LN:SetText(StringConst.Get("STORE_ACHIEVE_LEVEL_UNLOCK_LUEN"))
        elseif Data.Type == "COMPLETE_TASK_COUNT" or "COMPLETE_TASK" then
            Widget.Text_Unlock_LN:SetText(StringConst.Get("STORE_COMPLETE_TASK_LUEN"))
        elseif Data.Type == "GUILD_SHOP_LEVEL" then
            Widget.Text_Unlock_LN:SetText(StringConst.Get("STORE_GUILD_SHOP_LEVEL_LUEN"))
        end
    else
        Widget:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_DepartmentStoreItem:SetPriceUIState()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodsID)
    local PriceType = Game.DepartmentStoreSystem:GetPriceType(self.GoodsID)
    if Game.DepartmentStoreSystem:IsFree(self.GoodsID) then
        --免费
        self.PriceType = 0
        self.LTType = self.LTTypeList.None
    else
        self.PriceType = 1
        if self.MoneyIDList[2] then
            self.LTType = self.LTTypeList.None
            self.View.WBP_ComCurrency2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            --两种货币的指定原价
            if self.PriceList and self.PriceList[1] then
                self.View.WBP_ComCurrency1.Text_Count:SetText(Game.DungeonBattleStatisticsSystem:GetFormatNumberString(
                    self.PriceList[1]))
            end
            if self.PriceList and self.PriceList[2] then
                self.View.WBP_ComCurrency2.Text_Count:SetText(Game.DungeonBattleStatisticsSystem:GetFormatNumberString(
                    self.PriceList[2]))
            end
        else
            self.View.WBP_ComCurrency2:SetVisibility(ESlateVisibility.Collapsed)
            if PriceType == Enum.SHOP_GOODS_PRICE_TYPE.Discount then
                --打折
                self.LTType = self.LTTypeList.Discount
                local Percent = NPCShopTableData.DiscountPrice / self.PriceList[1]
                if 10 * NPCShopTableData.DiscountPrice % self.PriceList[1] == 0 then
                    Percent = math.floor(Percent * 10)
                else
                    Percent = Percent * 100 / 10
                end
                self.View.Text_Discount:SetText(Percent .. StringConst.Get("EXCHANGE_DISCOUNT"))
                self.View.WBP_ComCurrency1.Text_Count:SetText(Game.DungeonBattleStatisticsSystem:GetFormatNumberString(
                    NPCShopTableData.DiscountPrice))
            elseif PriceType == Enum.SHOP_GOODS_PRICE_TYPE.OriginalPrice then
                --原价
                self.LTType = self.LTTypeList.None
                if self.PriceList and self.PriceList[1] then
                    self.View.WBP_ComCurrency1.Text_Count:SetText(Game.DungeonBattleStatisticsSystem
                        :GetFormatNumberString(self.PriceList[1]))
                end
                if self.PriceList and self.PriceList[2] then
                    self.View.WBP_ComCurrency2.Text_Count:SetText(Game.DungeonBattleStatisticsSystem
                        :GetFormatNumberString(self.PriceList[2]))
                end
            end
        end
    end
end

function P_DepartmentStoreItem:SetLimitTypeUIState()
    local bAllServer, UpdateType, AllLimitNum = Game.DepartmentStoreSystem:GetLimitInfo(self.GoodsID)
    if AllLimitNum == -1 then
        --不限购
        self.bLimit = 0
    else
        local Text = ""
        self.bLimit = 1
        if bAllServer then
            Text = StringConst.Get("STORE_BUY_ALLSERVER_LIMIT")
        else
            if UpdateType == 0 then
                Text = StringConst.Get("STORE_BUY_PERMANENT")
            else
                local TypeString = Game.DepartmentStoreSystem.RefreshType[UpdateType]
                Text = StringConst.Get("STORE_EVERY") ..TypeString
            end
        end
        if Game.DepartmentStoreSystem:GetPriceType(self.GoodsID) == Enum.SHOP_GOODS_PRICE_TYPE.Discount then
            Text = Text .. Game.DepartmentStoreSystem:GetDiscountPriceBoughtCount(self.GoodsID) .. "/" .. AllLimitNum
        else
            if Game.DepartmentStoreSystem:IsSoldOut(self.GoodsID) then
                Text = Text .. Game.DepartmentStoreSystem:GetTotalBoughtCount(self.GoodsID) .. "/" .. AllLimitNum
            else
                Text = Text .. Game.DepartmentStoreSystem:GetNormalPriceBoughtCount(self.GoodsID) .. "/" .. AllLimitNum
            end
        end

        self.View.Text_LimitCount:SetText(Text)
    end
end

function P_DepartmentStoreItem:SetLimitTimerUIState()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodsID)
    if self.RefreshTimer then
        Game.TimerManager:StopTimerAndKill(self.RefreshTimer)
        self.RefreshTimer = nil
    end
    --下架时间
    local CurTime = Game.TimeUtils.GetCurTime()
    local LimitTimeDownStamp = NPCShopTableData.LimitTimeDownStamp
    if NPCShopTableData.LimitTimeUpStamp ~= 0 and NPCShopTableData.LimitTimeUpStamp <= CurTime and
        NPCShopTableData.LimitTimeDownStamp ~= 0 and CurTime <= NPCShopTableData.LimitTimeDownStamp then
        self.RTType = self.RTTypeList.LimitTime
        self.RefreshTimer = self:StartTimer(
            "TimeLimit",
            function()
                local LastTime = LimitTimeDownStamp - Game.TimeUtils.GetCurTime()
                if LastTime < 0 then
                    if self.RefreshTimer then
                        Game.TimerManager:StopTimerAndKill(self.RefreshTimer)
                        self.RefreshTimer = nil
                    end
                    self:StopTimer("TimeLimit")
                    self:Refresh(self.GoodsID, self.Sel)
                else
                    local handledTime = ""
                    if LastTime >= 60 * 60 * 24 then
                        --大于一天 "xx天"
                        handledTime = LastTime // (60 * 60 * 24).."天"
                    elseif LastTime >= 60 * 60 then        
                        --小于一天 "xx时"
                        handledTime = LastTime // (60 * 60).."时"
                    elseif LastTime > 60 then
                        -- 小于1小时，大于1分
                         handledTime = LastTime // 60 .."分"
                    else
                        handledTime = "<1分"
                    end
                    self.View.Text_Timeless:SetText(handledTime)
                end
            end,
            60000, -1, nil, true
        )
    else
        self.RTType = self.RTTypeList.None
    end
end

return P_DepartmentStoreItem
