---@class P_AuctionTips : UIController
---@field public View WBP_AuctionDetailView
local P_AuctionTips        = DefineClass("P_AuctionTips", UIController)
local StringConst          = require "Data.Config.StringConst.StringConst"
local P_TradeCurrency      = kg_require "Gameplay.LogicSystem.Trade.P_TradeCurrency"
local P_TradeSlider        = kg_require "Gameplay.LogicSystem.CommonUI.P_TradeSlider"
local ComTreasure          = kg_require "Gameplay.LogicSystem.CommonUI.ComTreasure.P_ComTreasure"
local DescFormulaHelper    = kg_require "Gameplay.LogicSystem.SkillCustomizer.DescFormulaHelper"
local WidgetEmptyComponent = kg_require "Framework.UI.WidgetEmptyComponent"
local EHorizontalAlignment = import("EHorizontalAlignment")
local ESlateVisibility     = import("ESlateVisibility")
local EVerticalAlignment   = import("EVerticalAlignment")


local LibWidgetConfig = kg_require("Framework.UI.LibWidgetConfig")
-- 添加lib widget预加载声明
P_AuctionTips.PreloadLibMap = {
    Title = LibWidgetConfig.Title,
    TreasureReward = LibWidgetConfig.TreasureReward,
    Equipment = LibWidgetConfig.Equipment,
    Line = LibWidgetConfig.Line,
    Text = LibWidgetConfig.Text,
}

function P_AuctionTips:OnCreate()
    self.TradeCurrencyComp = self:BindComponent(self.View.WBP_TradeCurrency, P_TradeCurrency)
    self.QuickBidTradeCurrencyComp = self:BindComponent(self.View.WBP_AuctionBtnQuickBid.WBP_TradeCurrency,
        P_TradeCurrency)
    self.PriceSlider = self:BindComponent(self.View.WBP_TradeSlider, P_TradeSlider)
    -- self.TreasureRewardList = self:BindComponent(self.View.WBP_ItemDetailList, ComTreasure)
end

function P_AuctionTips:OnRefresh(Params, Type)
    self.Data = Params
    self.Type = Type
    self:UpdateUI()
end

function P_AuctionTips:UpdateUI()
    self.View.VB_Content:ClearChildren()
    self.View.WBP_TradeSlider.WidgetRoot:SetTopBtn(0)
    self.View.WBP_TradeSlider.WidgetRoot:SetType(1)
    local slot = self.View.WidgetRoot.slot
    slot:SetHorizontalAlignment(
        EHorizontalAlignment.HAlign_Fill
    )
    slot:SetVerticalAlignment(
        EVerticalAlignment.VAlign_Fill
    )
    local ItemTableData = Game.TableData.GetItemNewDataRow(self.Data.ID)
    self.View.Text_Name:SetText(ItemTableData.itemName)
    self.View.Text_Name_Misery:SetText(ItemTableData.EnglishName)
    local IconPath = Game.UIIconUtils.GetIconByItemId(self.Data.ID)
    if IconPath then
        self:SetImage(self.View.Icon_Goods, IconPath)
    end
    self.View.WBP_AuctionDetailsTitle1.Text_Title:SetText(StringConst.Get("BID_MAX_PRICE"))
    self.View.WBP_AuctionDetailsTitle2.Text_Title:SetText(StringConst.Get("BID_COUNTDOWN"))
    self.View.WBP_AuctionDetailsTitle3.Text_Title:SetText(StringConst.Get("BID_MY_OFFER"))
    self.View.WBP_AuctionBtnQuickBid.Text_Name:SetText(StringConst.Get("BID_FIXED_PRICE"))
    self.View.WBP_AuctionBtnBid.Text_Name:SetText(StringConst.Get("BID_BID"))
    --最高出价
    if self.Data.currentBidder == "" then
        self.View.WBP_TradeCurrency:SetVisibility(ESlateVisibility.Collapsed)
        self.View.Text_Player:SetText("暂无玩家出价")
    else
        self.View.WBP_TradeCurrency:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.TradeCurrencyComp:FillItem(3, 2001001,
            Game.DungeonBattleStatisticsSystem:GetFormatNumberString(self.Data.currentBidPrice), false)
        self.View.Text_Player:SetText(self.Data.currentBidderName)
    end
    --落锤
    self:UpdateLeftTimeTimer()
    --我的出价
    self.AddCount = 0
    local CurBidPrice = Game.AuctionManager:HandleData(self.Data.currentBidPrice *
        Game.TableData.GetConstDataRow("EXCHANGE_HOUSE_PRICE_ADD"))
    if CurBidPrice > self.Data.bidOutPrice then
        CurBidPrice = math.floor(self.Data.bidOutPrice)
    end
    self.PriceSlider:RefreshSlider(true, 0, Game.TableData.GetConstDataRow("EXCHANGE_HOUSE_PRICE_ADD"), CurBidPrice,
        math.round(self.Data.bidOutPrice), CurBidPrice, nil, 1, Enum.SliderContentType.Price, false,
        function(Count, AddCount)
            self:UpdateAuctionPrice(Count, AddCount)
        end)
    --一口价
    self:UpdateAuctionPrice(CurBidPrice, 0)
    self.QuickBidTradeCurrencyComp:FillItem(4, 2001001,
        math.round(self.Data.bidOutPrice), Game.BagSystem:GetItemCount(2001001) < self.Data.bidOutPrice)
    -------装备属性
    local ItemExcelData = Game.TableData.GetItemNewDataRow(self.Data.ID)
    self:InitNormalText(ItemExcelData)
    self:InitPreviewEquip(ItemExcelData)
    --礼包
    self:InitTreasure(ItemExcelData)
    self:InitIPText(ItemExcelData)
end

function P_AuctionTips:InitTreasure(ItemExcelData)
    -- 宝箱的礼包展示
    -- 是否是宝箱
    self.IsTreasure = ItemExcelData.subType == 2004
    if self.IsTreasure then
        self:PushContainerComponent('TreasureReward')
        local TreasureTitleWidget = self:FormComponent('Title', self.View.VB_Content, WidgetEmptyComponent)
        TreasureTitleWidget.View.Text_Name:SetText(StringConst.Get("BAG_REWARD"))
        self:FormComponent('TreasureReward', TreasureTitleWidget.View.TitleItemList, ComTreasure, self.Data.ID,
            Enum.TreasureStyleType.Style1)
    end
end

---装备预览tips
function P_AuctionTips:InitPreviewEquip(ItemExcelData)
    local equipData = Game.EquipmentSystem:GetEquipData(self.Data.ID)
    if not equipData then
        return
    end
    local TypeData = Game.TableData.GetEquipmentTypeDataRow(equipData.subType)
    local RarityData = Game.TableData.GetEquipmentRarityDataRow(equipData.quality)

    -- 基础属性
    if TypeData.BasicPropAppear and #TypeData.BasicPropAppear > 0 then
        local val1 = equipData[TypeData.BasicPropName[1]]
        local val2 = equipData[TypeData.BasicPropName[2]]
        local propItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
        propItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
        propItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
        propItem.View.RTB_Key:SetText("<DarkGrey>" .. TypeData.BasicPropAppear[1] .. "</>")
        propItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
        propItem.View.RTB_Val:SetText("<White>" .. val1 .. "-" .. val2 .. "</>")
        propItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
    else
        for idx, propName in pairs(TypeData.BasicPropName) do
            local PropNameData = Game.TableData.GetFightPropDataRow(propName)
            local Title = ""
            if PropNameData then
                Title = PropNameData.Discription
            else
                local PropId = Enum.EFightPropModeData[propName]
                local PropModeData = Game.TableData.GetFightPropModeDataRow(PropId)
                if PropModeData then
                    Title = PropModeData.PropName
                else
                    Title = 'No Title'
                end
            end
            local propItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
            propItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
            propItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
            propItem.View.RTB_Key:SetText("<InvDefault>" .. Title .. "</>")
            propItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
            propItem.View.RTB_Val:SetText("<InvDefault>" .. equipData[propName] .. "</>")
            propItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
    self:FormComponent('Line', self.View.VB_Content, WidgetEmptyComponent)

    local Quality = ItemExcelData.quality
    --- 固定词条
    if equipData.FixedWord and #equipData.FixedWord > 0 then
        for index, groupId in pairs(equipData.FixedWord) do
            local GroupData = Game.TableData.GetEquipmentWordFixedGroupDataRow(groupId)
            if GroupData then
                local PropItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
                PropItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetText("<InvDefault>" .. GroupData.Des .. "</>")
                PropItem.View.RTB_Val:SetText("<InvDefault>" .. ' +?' .. "</>")
            end
        end
    else
        local EquipRarityData = Game.TableData.GetEquipmentRarityDataRow(Quality)
        if EquipRarityData.FixedWordNum > 0 then
            for i = 1, EquipRarityData.FixedWordNum do
                local PropItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
                PropItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetText("<InvDefault>" .. StringConst.Get("EQUIP_MYSTERY_ENTRY") .. ":" .. "</>")
                PropItem.View.RTB_Val:SetText("<InvDefault>" .. StringConst.Get("EQUIP_WAIT_TO_OPENED") .. "</>")
            end
        end
    end
    self:FormComponent('Line', self.View.VB_Content, WidgetEmptyComponent)

    --- 随机词条
    local RandomPropItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
    RandomPropItem.View.RTB_Key:SetText("<InvDefault>" .. StringConst.Get("EQUIP_RANDOM_PROP_SYMBOL") .. "</>")
    RandomPropItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
    RandomPropItem.View.RTB_Val:SetVisibility(ESlateVisibility.Collapsed)
    RandomPropItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
end

function P_AuctionTips:InitNormalText(ItemExcelData)
    --- func说明
    if ItemExcelData.funcRep and ItemExcelData.funcRep ~= "" then
        self.NormTextWidgetComp = self:FormComponent('Text', self.View.VB_Content, WidgetEmptyComponent,
            ItemExcelData.funcRep)
        self.NormTextWidgetComp.View.Text_TipsContent:SetText(DescFormulaHelper.FormDescInType(ItemExcelData.funcRep,
            "InvDefault"))
    end
end

function P_AuctionTips:InitIPText(ItemExcelData)
    --- IP说明
    if ItemExcelData.itemDes and ItemExcelData.itemDes ~= "" then
        self.IPTextWidgetComp = self:FormComponent('Text', self.View.VB_Content, WidgetEmptyComponent,
            ItemExcelData.itemDes)
        self.IPTextWidgetComp.View.Text_TipsContent:SetText(DescFormulaHelper.FormDescInType(ItemExcelData.funcRep,
            "InvDefault"))
    end
end

function P_AuctionTips:UpdateLeftTimeTimer()
    --落锤计时
    local AllTime = self.Data.expiryTime - Game.TimeUtils.GetCurTime()
    self.AuctionTimer = self:StartTimer("P_AuctionTips",
        function()
            if Game.TimeUtils.GetCurTime() < Game.AuctionManager:GetBeginTime() then
                --准备期
                self.View.Text_Time:SetVisibility(ESlateVisibility.Collapsed)
                self.View.ProgressBar:SetPercent(1)
            elseif self.Data.expiryTime < Game.TimeUtils.GetCurTime() then
                --拍卖结束
                self.View.WBP_AuctionBtnBid.Img_Btn:SetIsEnabled(false)
                self.View.Text_Name:SetIsEnabled(false)
                self.View.WBP_AuctionBtnQuickBid.Img_Btn:SetIsEnabled(false)
                self.View.WBP_AuctionBtnQuickBid.HB_Money:SetIsEnabled(false)
                self.View.Text_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                self.View.Text_Time:SetText("00:00")
                self.View.ProgressBar:SetPercent(1)
            else
                --拍卖中
                self.View.WBP_AuctionBtnBid.Img_Btn:SetIsEnabled(true)
                self.View.WBP_AuctionBtnBid.Text_Name:SetIsEnabled(true)
                self.View.WBP_AuctionBtnQuickBid.Img_Btn:SetIsEnabled(true)
                self.View.WBP_AuctionBtnQuickBid.HB_Money:SetIsEnabled(true)
                local handledTime = Game.TimeUtils.FormatCountDownString(
                    (self.Data.expiryTime - Game.TimeUtils.GetCurTime()) * 1000,
                    false)
                self.View.Text_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                self.View.Text_Time:SetText(handledTime)
                self.View.ProgressBar:SetPercent((self.Data.expiryTime - Game.TimeUtils.GetCurTime()) / AllTime)
            end
        end,
        1000, -1, nil, true
    )
end

function P_AuctionTips:UpdateAuctionPrice(Price, AddCount)
    self.Price = Price
    self.AddCount = AddCount
end

function P_AuctionTips:OnClick_WBP_AuctionBtnQuickBid_Btn_ClickArea()
    --一口价
    if Game.TimeUtils.GetCurTime() < Game.AuctionManager:GetBeginTime() or Game.TimeUtils.GetCurTime() > self.Data.expiryTime then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.BID_PREPARE_PERIOD_CLICK)
    elseif Game.AuctionManager:CheckIsBidded(self.Data.ID, self.Data.UniqueID) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.BID_ITEM_FAIL_BY_ALREADY_BUY_OUT)
    else
        UI.ShowUI("P_AuctionBoughtConfirmTips", self.Type, self.Data.ID, self.Data.UniqueID, self.Data.currentBidPrice,
            self.Data.bidOutPrice, 0)
    end
end

function P_AuctionTips:OnClick_WBP_AuctionBtnBid_Btn_ClickArea()
    if Game.TimeUtils.GetCurTime() < Game.AuctionManager:GetBeginTime() or Game.TimeUtils.GetCurTime() > self.Data.expiryTime then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.BID_PREPARE_PERIOD_CLICK)
    elseif Game.AuctionManager:CheckIsBidded(self.Data.ID, self.Data.UniqueID) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.BID_ITEM_FAIL_BY_ALREADY_BUY_OUT)
    else
        if self.Data.bidOutPrice == self.Price then
            self.AddCount = 0
        end
        UI.ShowUI("P_AuctionBoughtConfirmTips", self.Type, self.Data.ID, self.Data.UniqueID, self.Data.currentBidPrice,
            self.Price, self.AddCount)
    end
end

return P_AuctionTips
