local AuctionManager = DefineClass("AuctionManager", ManagerBase)

-- STALL_PUBLICITY_TIME

AuctionManager.Type = {
    WorldAuction = 1,
    GuildAuction = 2
}

AuctionManager.State = {
    Prepare = 1,
    InProgress = 2,
    End = 3,
    None = 4
}

AuctionManager.AuctionHistoryNum = 30

function AuctionManager:onCtor()

end

function AuctionManager:onDestroy()

end

function AuctionManager:onInit()
    self.CurAuctionType = AuctionManager.Type.GuildAuction
    self.PreTime = 0
    self.BeginTime = 0
    self.EndTime = 0
    self.CurAuctionDisappearTime = 0
    self.DisappearTime = 0
    self.SearchHistory = {}
    self.GoodsIDNameMap = {}
    self.ContentData = {}
    self.UniqueID = -1
    self.totalDisappearTime = -1
end

function AuctionManager:onUnInit()

end

--返回登录
function AuctionManager:OnBackToLogin()
    self:ClearAuctionTime()
end

--返回选角
function AuctionManager:OnBackToSelectRole()
    self:onInit()
end

function AuctionManager:OnAuctionBegin(Type, preTime, beginTime, endTime, CurAuctionDisapperaTime, totalDisappearTime)
    self.CurAuctionType = Type
    self.PreTime = preTime
    self.BeginTime = beginTime
    self.EndTime = endTime
    self.CurAuctionDisappearTime = CurAuctionDisapperaTime
    self.totalDisappearTime = totalDisappearTime
    self.RecordList = {}
    Game.HUDSystem:ShowUI(UICellConfig.HUDAuctionBtn)
    Game.EventSystem:Publish(_G.EEventTypes.ON_AUCTION_BEGIN)
end

function AuctionManager:GetCurAuctionType()
    return self.CurAuctionType
end

function AuctionManager:GetPreTime()
    return self.PreTime
end

function AuctionManager:GetBeginTime()
    return self.BeginTime
end

function AuctionManager:GetEndTime()
    return self.EndTime
end

function AuctionManager:GetCurAuctionDisappearTime()
    return self.CurAuctionDisappearTime
end

function AuctionManager:GetTotalDisappearTime()
    return self.totalDisappearTime
end

function AuctionManager:ClearAuctionTime()
    self.PreTime = 0
    self.BeginTime = 0
    self.EndTime = 0
    self.CurAuctionDisappearTime = 0
    self.DisappearTime = 0
end

function AuctionManager:GetRecordList()
    return self.RecordList
end

function AuctionManager:HasBit(UniqueID)
    return self.RecordList[UniqueID] ~= nil
end

function AuctionManager:GetContentList()
    return self.ContentData
end

function AuctionManager:CheckIsBidded(ID, UniqueID)
    for Key, value in ipairs(self.HasBitList) do
        if Game.me.eid == value.currentBidder and value.ID == ID and value.UniqueID == UniqueID then
            return true
        end
    end
    return false
end

function AuctionManager:UpdateAuctionData()
    self.HasBitList = {}
    self.NotBitList = {}
    for Key, value in ipairs(self.ItemInfoList) do
        local BidTableData = Game.TableData.GetBidItemDataRow(value.configID)
        if self.RecordList and self.RecordList[value.id] ~= nil then
            table.insert(self.HasBitList, {
                UniqueID = value.id,
                ID = value.itemId,
                expiryTime = value.expiryTime,
                currentBidder = value.currentBidder,
                currentBidderName = value.currentBidderName,
                bidOutPrice = value.bidOutPrice,
                currentBidPrice = value.currentBidPrice,
                beginTime = value.beginTime,
                orderIdx = BidTableData.Order
            })
        else
            table.insert(self.NotBitList, {
                UniqueID = value.id,
                ID = value.itemId,
                expiryTime = value.expiryTime,
                currentBidder = value.currentBidder,
                currentBidderName = value.currentBidderName,
                bidOutPrice = value.bidOutPrice,
                currentBidPrice = value.currentBidPrice,
                beginTime = value.beginTime,
                orderIdx = BidTableData.Order
            })
        end
    end
    self.ContentData = {}
    if #self.HasBitList > 0 then
        self:SortAuctionData(self.HasBitList)
        table.insert(self.ContentData, { Name = "已出价", Children = self.HasBitList })
    end
    if #self.NotBitList > 0 then
        self:SortAuctionData(self.NotBitList)
        table.insert(self.ContentData, { Name = "未出价", Children = self.NotBitList })
    end
end

function AuctionManager:FilterAuctionData(ItemInfoList, RecordList)
    self.ItemInfoList = ItemInfoList
    self.RecordList = RecordList
    self:UpdateAuctionData()
    self:InitGoodsNameMap()
end

function AuctionManager:UpdateAuctionItemInfo(ItemInfo)
    --部分商品更新
    if ItemInfo.currentBidder == Game.me.eid then
        self.RecordList[ItemInfo.id] = ItemInfo.currentBidPrice
    end

    --找到拍卖id对应的道具对象
    local localItemInfo = self.ItemInfoList[ItemInfo.id]
    if localItemInfo then
        for fieldName, fieldValue in pairs(ItemInfo) do
            localItemInfo[fieldName] = fieldValue
        end
    else
        --出错提示
        Log.Debug("not find itemInfo")
    end

    self:UpdateAuctionData()
end

function AuctionManager:SortAuctionData(ItemInfoList)
    table.sort(ItemInfoList,
        function(a, b)
            if a.beginTime ~= b.beginTime then
                return a.beginTime < b.beginTime
            elseif a.orderIdx ~= b.orderIdx then
                return a.orderIdx < b.orderIdx
            elseif a.currentBidPrice ~= b.currentBidPrice then
                return a.currentBidPrice < b.currentBidPrice
            end
        end)
end

function AuctionManager:FindIndexByUiqueID(UniqueID)
    local Index1 = -1
    local Index2 = -1
    for key, value in ipairs(self.ContentData) do
        for kk, vv in ipairs(value.Children) do
            if vv.UniqueID == UniqueID then
                Index1 = key
                Index2 = kk
                break
            end
        end
    end
    return Index1, Index2
end

function AuctionManager:FindJumpItemByUiqueID(UniqueID)
    local UniqueID1 = -1
    local UniqueID2 = -1
    local ItemID = -1
    local Price = -1
    for key, value in ipairs(self.ContentData) do
        for kk, vv in ipairs(value.Children) do
            if vv.UniqueID == UniqueID then
                ItemID = vv.ID
                break
            end
        end
    end
    if ItemID then
        for key, value in ipairs(self.ContentData) do
            for kk, vv in ipairs(value.Children) do
                if vv.ID == ItemID then
                    if key == 1 then
                        if Price == -1 or Price > vv.currentBidPrice then
                            UniqueID1 = vv.UniqueID
                            Price = vv.currentBidPrice
                        end
                    elseif key == 2 then
                        if Price == -1 or Price > vv.currentBidPrice then
                            UniqueID2 = vv.UniqueID
                            Price = vv.currentBidPrice
                        end
                    end
                end
            end
        end
    end
    if UniqueID2 ~= -1 then
        return UniqueID2
    elseif UniqueID1 ~= -1 then
        return UniqueID1
    else
        return -1
    end
end

function AuctionManager:AddSearchItems(List)
    local OriList = {}
    table.insert(OriList, 1, List)
    for key, value in ipairs(self.SearchHistory) do
        if OriList and #OriList >= 5 then
            break
        end
        if not table.contains(OriList, value) then
            table.insert(OriList, value)
        end
    end
    self.SearchHistory = OriList
end

function AuctionManager:RemoveSearchItems()
    table.clear(self.SearchHistory)
end

function AuctionManager:GetSearchHistory()
    return self.SearchHistory
end

function AuctionManager:InitGoodsNameMap()
    self.GoodsIDNameMap = {}
    for key, value in ipairs(self.ContentData) do
        for kk, vv in ipairs(value.Children) do
            local ItemTableData = Game.TableData.GetItemNewDataRow(vv.ID)
            if ItemTableData then
                self.GoodsIDNameMap[vv.UniqueID] = ItemTableData.itemName
            end
        end
    end
end

function AuctionManager:GetGoodsNameMap()
    return self.GoodsIDNameMap
end

function AuctionManager:GetGoodsByIDs(ItemList)
    local Result = {}
    for key, value in ipairs(self.ContentData) do
        for kk, vv in ipairs(value.Children) do
            if table.ikey(ItemList, vv.UniqueID) then
                table.insert(Result, vv)
            end
        end
    end
    return Result
end

function AuctionManager:GetGoodsByUniqueID(UniqueID)
    local Result = {}
    for key, value in ipairs(self.ContentData) do
        for kk, vv in ipairs(value.Children) do
            if UniqueID == vv.ID then
                Result = vv
                break
            end
        end
    end
    return Result
end

function AuctionManager:HandleData(Value)
    local countString = tostring(math.floor(Value))
    if Value > 1000 then
        local Size = string.len(countString)
        local SubStr = string.sub(countString, 1, 3)
        for i = 1, Size - 3 do
            SubStr = SubStr .. "0"
        end
        return tonumber(SubStr)
    end
    return Value
end

return AuctionManager
