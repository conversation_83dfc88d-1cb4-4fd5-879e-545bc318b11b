---@class P_AuctionTabFirst : UIController
--- @field public View WBP_TradeTabLv2FoldView
local P_AuctionTabFirst = DefineClass("P_AuctionTabFirst", UIComponent, ITreeListComponent)

function P_AuctionTabFirst:OnCreate()
    self.Data = nil
end

function P_AuctionTabFirst:OnClick(parentUI, allData, index1)
    local bIsLeafNode = self.parent:IsLeafNode(index1)
    if bIsLeafNode then
        self.parent:FoldAll(true)
    else
        local isFold = self.parent:IsFold(index1)
        self.parent:FoldAll(true)
        if not self.parent:HasSelect(index1) and isFold then
            --第一次打开这个页签
            self.parent:Sel(index1, 1)
        end
        self.parent:Fold(not isFold, index1)
    end
    parentUI:UpdateSecTab(index1)
end

function P_AuctionTabFirst:OnListRefresh(parentUI, bSelect, allData, index)
    self.index = index
    self.View.Text_Title:SetText(allData[index].Name)
end

function P_AuctionTabFirst:Refresh(Data, bHideFollow)

end

return P_AuctionTabFirst
