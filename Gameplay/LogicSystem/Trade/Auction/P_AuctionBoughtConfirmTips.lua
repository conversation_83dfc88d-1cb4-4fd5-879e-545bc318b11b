---@class P_AuctionBoughtConfirmTips : UIController
--- @field public View WBP_AuctionBidPopupView
local P_AuctionBoughtConfirmTips = DefineClass("P_AuctionBoughtConfirmTips", UIController)
local ItemReward                 = kg_require "Gameplay.LogicSystem.Item.ItemReward"
local StringConst               = require "Data.Config.StringConst.StringConst"

function P_AuctionBoughtConfirmTips:OnCreate()
    self.RewardItem = self:BindComponent(self.View.WBP_ItemReward, ItemReward)
end

function P_AuctionBoughtConfirmTips:OnRefresh(Type, ItemID, ID, CurBidPrice, BidPrice, AddCount)
    self.Type = Type
    self.ItemID = ItemID
    self.ID = ID
    self.CurBidPrice = CurBidPrice
    self.BidPrice = BidPrice
    self.AddCount = AddCount
    self.View.WBP_ComPopupM.WBP_ComPopupTitle.Text_Title:SetText(StringConst.Get('BID_CONFIRM_PRICE'))
    self.View.WBP_ComBtn_No.Text_Com:SetText(StringConst.Get('EXCHANGE_CANCEL'))
    self.View.WBP_ComBtn_Yes.Text_Com:SetText(StringConst.Get('AUCTION_ENSURE'))
    local ItemTableData = Game.TableData.GetItemNewDataRow(self.ItemID)

    self.View.Text_Name:SetText(ItemTableData.itemName)
    self.RewardItem:FillItem(self.ItemID, Enum.CommonItemClickType.OverrideTip, false, 0,
        nil, false)
    local IconPath = ""
    local MoneyTableData = Game.TableData.GetItemNewDataRow(2001001)
    if MoneyTableData then
        IconPath = Game.UIIconUtils.GetIconByItemId(2001001, nil, true)
    end


    self.View.C7RichTextBlock:SetText(StringConst.Get('BID_CHOICE') .. string.format("<img tex2d=\"%s\"/>", IconPath) ..
        math.floor(self.BidPrice) .. StringConst.Get('BID_BID') .. ItemTableData.itemName)
end

function P_AuctionBoughtConfirmTips:OnClose()
    UIBase.OnClose(self)
    -- self:StopAnimation(self.View.WBP_ComPopupM, self.View.WBP_ComPopupM.Ani_In, 0, true)
end

function P_AuctionBoughtConfirmTips:OnClick_WBP_ComPopupM_WBP_ComPopupTitle_WBP_ComBtnClose_Button()
    self:CloseSelf()
end

function P_AuctionBoughtConfirmTips:OnClick_WBP_ComBtn_No_Btn_Com()
    self:CloseSelf()
end

function P_AuctionBoughtConfirmTips:OnClick_WBP_ComBtn_Yes_Btn_Com()
    self:CloseSelf()
    if Game.BagSystem:GetItemCount(2001001) >= self.BidPrice then
        if self.Type == Game.AuctionManager.Type.WorldAuction then
            Game.me:ReqWorldBidItem(self.ID, math.floor(self.CurBidPrice), math.floor(self.BidPrice), self.AddCount + 1)
        else
            Game.me:ReqGuildBidItem(self.ID, math.floor(self.CurBidPrice), math.floor(self.BidPrice), self.AddCount + 1)
        end
    else
        Game.TradeManager:RecordCurSelState(4, 1, 1, self.ID)
        --货币不足
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MONEY_NOT_ENOUGH)
		Game.NewUIManager:OpenPanel(UIPanelConfig.ConsignmentTotal_Panel)
    end
end

return P_AuctionBoughtConfirmTips
