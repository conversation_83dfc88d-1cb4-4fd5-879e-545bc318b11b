---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button UC7Button
---@field public Ani_Fadein UWidgetAnimation
---@field public Ani_Press UWidgetAnimation
---@field public IconBrush FSlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):FSlateBrush


---@class WBP_ComTitleView : WBP_ComTitle_C
---@field public WidgetRoot WBP_ComTitle_C
---@field public Text_Title UTextBlock
---@field public Image_Line UImage
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView
---@field public Ani_In UWidgetAnimation
---@field public TitleText string
---@field public OnClicked MulticastDelegate
---@field public BndEvt__WBP_ComTitle_WBP_ComBtnClose_lua_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature fun(self:self):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetText fun(self:self,InText:string):void
---@field public InnerSetText fun(self:self):void


---@class WBP_ComPopupMView : WBP_ComPopupM_C
---@field public WidgetRoot WBP_ComPopupM_C
---@field public WBP_ComPopupTitle WBP_ComTitleView
---@field public Ani_In UWidgetAnimation
---@field public Ani_Out UWidgetAnimation
---@field public Title string
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public Btn_Com UC7Button
---@field public OutOverlay UOverlay
---@field public Text_Com UTextBlock
---@field public Text_Time UTextBlock
---@field public Image UC7Image
---@field public Ani_Press UWidgetAnimation
---@field public Ani_Tower UWidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_AuctionBidPopupView : WBP_AuctionBidPopup_C
---@field public WidgetRoot WBP_AuctionBidPopup_C
---@field public WBP_ComPopupM WBP_ComPopupMView
---@field public WBP_ComBtn_No WBP_ComBtnView
---@field public WBP_ComBtn_Yes WBP_ComBtnView


---@class P_AuctionBoughtConfirmTipsView : WBP_AuctionBidPopupView
---@field public controller P_AuctionBoughtConfirmTips
local P_AuctionBoughtConfirmTipsView = DefineClass("P_AuctionBoughtConfirmTipsView", UIView)

function P_AuctionBoughtConfirmTipsView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPopupM.WBP_ComPopupTitle.WBP_ComBtnClose.Button, "OnClick_WBP_ComPopupM_WBP_ComPopupTitle_WBP_ComBtnClose_Button")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComBtn_No.Btn_Com, "OnClick_WBP_ComBtn_No_Btn_Com")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComBtn_Yes.Btn_Com, "OnClick_WBP_ComBtn_Yes_Btn_Com")

end

function P_AuctionBoughtConfirmTipsView:OnDestroy()
    local controller = self.controller
---DeletePlaceHolder
end

return P_AuctionBoughtConfirmTipsView
