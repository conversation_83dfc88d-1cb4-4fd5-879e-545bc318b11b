---@class WBP_AuctionListTitleView : WBP_AuctionListTitle_C
---@field public WidgetRoot WBP_AuctionListTitle_C
---@field public Text_Title UC7TextBlock


---@class WBP_ComComBoxView : WBP_ComComBox_C
---@field public WidgetRoot WBP_ComComBox_C
---@field public Button UC7Button
---@field public Text_Target UTextBlock
---@field public Img_Arrow UImage
---@field public CanvasPanel_Options UCanvasPanel
---@field public DEOptions UDynamicEntryBox
---@field public Ani_Spread UWidgetAnimation
---@field public Ani_Fewer UWidgetAnimation
---@field public Pos number
---@field public IsExpand boolean
---@field public IsIcon boolean
---@field public IsInverse boolean
---@field public DefaultColor FSlateColor
---@field public InverseColor FSlateColor
---@field public DefaultBg FSlateBrush
---@field public InverseBg FSlateBrush
---@field public TitleWidth number
---@field public TitleHeight number
---@field public ContentWidth number
---@field public ContentHeight number
---@field public ContentScrollCountCondition number
---@field public NormalOptionBGColor FLinearColor
---@field public InverseOptionBGColor FLinearColor
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetTitle fun(self:self,Title:string):void
---@field public SetIcon fun(self:self,IsIcon:boolean):void
---@field public SetInverse fun(self:self,Inverse:boolean):void
---@field public SetItemInverse fun(self:self,Inverse:boolean):void
---@field public SetIconPreview fun(self:self,IconTitle:FSlateBrush):void
---@field public UpdateType fun(self:self,Pos:number):void
---@field public ToggleOptionsVisible fun(self:self):void
---@field public SetOptionsVisible fun(self:self,Visible:boolean):void
---@field public GetOptionTextColor fun(self:self):FLinearColor,FSlateColor,FLinearColor,FSlateColor
---@field public SetContentScrollEnable fun(self:self,Condition:boolean):void


---@class WBP_ComCheckBoxView : WBP_ComCheckBox_C
---@field public WidgetRoot WBP_ComCheckBox_C
---@field public Overlay UOverlay
---@field public CheckBox UC7CheckBox
---@field public TB_Name UTextBlock
---@field public Ani_Press UWidgetAnimation
---@field public TextValue string
---@field public TextColor FSlateColor
---@field public Undetermined boolean
---@field public Has Text boolean
---@field public SetUndetermined fun(self:self,Undetermined:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComBtnIconNewView : WBP_ComBtnIconNew_C
---@field public WidgetRoot WBP_ComBtnIconNew_C
---@field public OutCanvas UCanvasPanel
---@field public Icon UImage
---@field public Text_Name UTextBlock
---@field public Big_Button_ClickArea UC7Button
---@field public Anim_1 UWidgetAnimation
---@field public Anim_2 UWidgetAnimation
---@field public Anim_3 UWidgetAnimation
---@field public Anim_4 UWidgetAnimation
---@field public Ani_Press UWidgetAnimation
---@field public Ani_Hover UWidgetAnimation
---@field public Ani_Tower UWidgetAnimation
---@field public Btn Style FST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound FSlateSound
---@field public Top number
---@field public Event_UI_Style fun(self:self,BtnName:string):void
---@field public Play Hint Anim fun(self:self):void
---@field public BndEvt__WBP_ComBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Btn Style fun(self:self,Btn Style:FST_ComBtnIcon):void


---@class WBP_ComBtnSwitchView : WBP_ComBtnSwitch_C
---@field public WidgetRoot WBP_ComBtnSwitch_C
---@field public text_close UTextBlock
---@field public text_open UTextBlock
---@field public Big_Button_ClickArea UC7Button
---@field public Ani_Switch_Close UWidgetAnimation
---@field public Ani_Switch_Open UWidgetAnimation
---@field public Ani_Switch UWidgetAnimation
---@field public color_Grey FSlateColor
---@field public color_Select FSlateColor
---@field public Event_UI_Style fun(self:self,bOpen:boolean):void


---@class WBP_TradeTitleView : WBP_TradeTitle_C
---@field public WidgetRoot WBP_TradeTitle_C
---@field public Title UOverlay
---@field public Overlay_AuctionTime UOverlay
---@field public Text_Auction UC7TextBlock
---@field public Overlay_Back UOverlay
---@field public Btn_ClickArea UC7Button
---@field public WBP_Job WBP_ComComBoxView
---@field public WBP_Quality WBP_ComComBoxView
---@field public WBP_Level WBP_ComComBoxView
---@field public CB_Num UOverlay
---@field public WBP_CB_Num WBP_ComCheckBoxView
---@field public Btn_Search WBP_ComBtnIconNewView
---@field public Btn_History WBP_ComBtnIconNewView
---@field public WBP_Switch WBP_ComBtnSwitchView
---@field public State number
---@field public Event_UI_Style fun(self:self,State:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetState fun(self:self,State:number):void


---@class WBP_ComTreeListView : WBP_ComTreeList_C
---@field public WidgetRoot WBP_ComTreeList_C
---@field public TreeList UScrollBox
---@field public DiffPanel UCanvasPanel
---@field public DiffPoint UBorder
---@field public Structure FTreeListCell
---@field public IndexList number
---@field public LayoutList ListLayout
---@field public SpaceUpList number
---@field public SpaceBottomList number
---@field public SpaceLeftList number
---@field public SpaceRightList number
---@field public AlignmentList ListAligment
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public CreatListCell fun(self:self,widget:UWidget,posX:number,posY:number,libWidget:string,sizeX:number,sizeY:number):void
---@field public SetAllSlot fun(self:self,Src:UWidget,Tag:UWidget,Position:FVector2D):void
---@field public CalculatePos fun(self:self):void
---@field public InsertSubIndex fun(self:self,floor:number):void
---@field public GetArrayWidget fun(self:self,index:number):UWidget,string,number,number,UWidget,string,number,number
---@field public GetListSize fun(self:self):number,number
---@field public GetWidgetSize fun(self:self,Widget:UWidget):number,number
---@field public GetWidget fun(self:self):void


---@class WBP_TradeEmptyView : WBP_TradeEmpty_C
---@field public WidgetRoot WBP_TradeEmpty_C
---@field public Text_Empty UTextBlock


---@class WBP_AuctionContentView : WBP_AuctionContent_C
---@field public WidgetRoot WBP_AuctionContent_C
---@field public WBP_AuctionListTitle1 WBP_AuctionListTitleView
---@field public WBP_TradeTitle WBP_TradeTitleView
---@field public WBP_ComTreeList WBP_ComTreeListView
---@field public Overlay_Tips UOverlay
---@field public WBP_TradeEmpty WBP_TradeEmptyView
---@field public Option 0 FSlateBrush
---@field public Option 1 FSlateBrush
---@field public Option 2 FSlateBrush
---@field public Option 3 FSlateBrush
---@field public Option 4 FSlateBrush
---@field public Option 5 FSlateBrush
---@field public Quality number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class P_AuctionView : WBP_AuctionContentView
---@field public controller P_Auction
local P_AuctionView = DefineClass("P_AuctionView", UIView)

function P_AuctionView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_TradeTitle.Btn_Search.Big_Button_ClickArea, "OnClick_WBP_TradeTitle_Btn_Search_Big_Button_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_TradeTitle.Btn_History.Big_Button_ClickArea, "OnClick_WBP_TradeTitle_Btn_History_Big_Button_ClickArea")

end

function P_AuctionView:OnDestroy()
    local controller = self.controller
---DeletePlaceHolder
end

return P_AuctionView
