---@class P_AuctionHistoryItem : UIController
--- @field public View WBP_TradeGoodsItemHView
local P_AuctionHistoryItem = DefineClass("P_AuctionHistoryItem", UIComponent)
local ItemReward           = kg_require "Gameplay.LogicSystem.Item.ItemReward"
local P_TradeCurrency      = kg_require "Gameplay.LogicSystem.Trade.P_TradeCurrency"

function P_AuctionHistoryItem:OnCreate()
    self.RewardItem = self:BindComponent(self.View.WBP_ItemReward, ItemReward)
    self.Currency = self:BindComponent(self.View.WBP_TradeCurrency, P_TradeCurrency)
end

function P_AuctionHistoryItem:Refresh(Data)
    self.RewardItem:FillItem(Data.ItemId, Enum.CommonItemClickType.OverrideTip, false, 0, nil, false)
    self.Currency:FillItem(5, 2001001, Game.DungeonBattleStatisticsSystem:GetFormatNumberString(Data.BidPrice), false)
    local ItemTableData = Game.TableData.GetItemNewDataRow(Data.ItemId)
    if ItemTableData then
        self.View.Text_Name:SetText(ItemTableData.itemName)
    end
    self.View.Text_Time:SetText(Game.TimeUtils.FormatDateTimeString(Data.RecordTime * 1000))
end

return P_AuctionHistoryItem
