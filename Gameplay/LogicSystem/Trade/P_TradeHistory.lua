local ESlateVisibility = import("ESlateVisibility")

---@class P_TradeHistory : UIController
--- @field public View WBP_ExchangeHistoryPopupView
local P_TradeHistory = DefineClass("P_TradeHistory", UIController)
local StringConst = require "Data.Config.StringConst.StringConst"
local P_TradeHistoryItem = kg_require "Gameplay.LogicSystem.Trade.P_TradeHistoryItem"
local P_ComTabHor = kg_require "Gameplay.LogicSystem.CommonUI.P_ComTabHor"

function P_TradeHistory:OnCreate()
    self.TabList = self:BindComponent(self.View.WBP_ComTabHor, P_ComTabHor)
    self.TabData = {
        { TabName = StringConst.Get("STORE_PURCHASE") },
        { TabName = StringConst.Get("EXCHANGE_SELL") },
    }
    self.HistoryList = BaseList.CreateList(self, BaseList.Kind.OldList, self.View.List_History, P_TradeHistoryItem)
end

function P_TradeHistory:OnRefresh(Params)
    self.View.WBP_ComPopupL.WBP_ComPopupTitle.Text_Title:SetText(StringConst.Get("TRADE_HISTORY"))
    self.TabList:SetData(self.TabData, function(index) self:OnClickTab(index) end, 1)
end

-----------------------------------------交易Start---------------------------------------------
function P_TradeHistory:OnClickTab(Index)
    if Index == 1 then
        Game.me:getStallPurchaseRecord()
    else
        Game.me:getStallSellRecord()
    end
end

function P_TradeHistory:UpdateData(Data)
    if Data and #Data > 0 then
        self.View.List_History:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.WBP_TradeEmpty:SetVisibility(ESlateVisibility.Collapsed)
        self.Data = Data
        self.HistoryList:SetData(#self.Data)
    else
        self.View.List_History:SetVisibility(ESlateVisibility.Collapsed)
        self.View.WBP_TradeEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.WBP_TradeEmpty.Text_Empty:SetText(StringConst.Get("NO_TRADE_HISTORY"))
    end
end

-----------------------------------------交易end---------------------------------------------

function P_TradeHistory:OnRefresh_List_History(Widget, Index, Sel)
    Widget:Refresh(self.Data[Index])
end

function P_TradeHistory:OnClick_WBP_ComPopupL_WBP_ComPopupTitle_WBP_ComBtnClose_Button()
    self:CloseSelf()
end

return P_TradeHistory
