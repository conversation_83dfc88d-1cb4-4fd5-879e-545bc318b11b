---@class P_TradeSearchTag : UIController
--- @field public View WBP_TradeSearchTagView
local P_TradeSearchTag = DefineClass("P_TradeSearchTag", UIComponent)

P_TradeSearchTag.eventBindMap = {
    [_G.EEventTypes.ON_GET_ITEMINFO_BY_ID_BACK] = "OnExchangeSearchDataChange",
}

function P_TradeSearchTag:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, "OnClick_Tag")
end

function P_TradeSearchTag:Refresh(Data, Type)
    self.OriType = Type
    if self.OriType == Game.ExchangeSystem.PageType.MarketPage then
        --交易所
        self.Data = Data
        local ItemTableData = Game.TableData.GetItemNewDataRow(Data)
        local WidgetRoot = self.View.WidgetRoot
        if ItemTableData then
            self.View.Text_Name:SetText(ItemTableData.itemName)
            WidgetRoot:Event_UI_Style(ItemTableData.quality)
        end
    else
        --拍卖所
        self.Data = Game.AuctionManager:GetGoodsByUniqueID(Data)
        local ItemTableData = Game.TableData.GetItemNewDataRow(self.Data.ID)
        local WidgetRoot = self.View.WidgetRoot
        if ItemTableData then
            self.View.Text_Name:SetText(ItemTableData.itemName)
            WidgetRoot:Event_UI_Style(ItemTableData.quality)
        end
    end
end

function P_TradeSearchTag:OnClick_Tag()
    if self.OriType == Game.ExchangeSystem.PageType.MarketPage then
        UI.Invoke("P_TradeSearch", "SetTagSearchID", self.Data)
        Game.me:getItemsInfoByItemIdList({ self.Data })
    else
        UI.HideUI("P_TradeSearch")
        UI.Invoke("P_Auction", "UpdateSelItem", Game.AuctionManager:FindJumpItemByUiqueID(self.Data.UniqueID))
    end
end

return P_TradeSearchTag
