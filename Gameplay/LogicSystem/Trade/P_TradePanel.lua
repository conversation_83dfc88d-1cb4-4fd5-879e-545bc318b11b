local ESlateVisibility = import("ESlateVisibility")
---@class P_TradePanel : UIController
--- @field public View WBP_TradeTotalView
local P_TradePanel = DefineClass("P_TradePanel", UIController)
local P_TradeTabSec = kg_require "Gameplay.LogicSystem.Trade.P_TradeTabSec"
local P_TradeTabThird = kg_require "Gameplay.LogicSystem.Trade.P_TradeTabThird"
local P_FuncTab = kg_require "Gameplay.LogicSystem.Trade.P_FuncTab"
local StringConst = kg_require "Data.Config.StringConst.StringConst"

function P_TradePanel:OnCreate()
    --first tab index
    self.FirstTabSelIndex = 1
    --sec tab index
    self.SecondTabSelIndex = 1
    --third tab index
    self.ThirdTabSelIndex = 1
    --cursel goods id
    self.SelItemID = -1
    --cur show name
    self.ShowUIName = ""
    --(shop department exchange auction )tab
    self.FuncListView = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.WBP_FuncTab.HB_TabList, P_FuncTab, "FuncList")
    --(one level tab)
    self.SideNormListView = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_ShopComTab_List.WBP_ComList, nil, "NormList") --商城百货一级页签
    --(mutil level tab)
    self.SideFoldListView = BaseList.CreateList(self, BaseList.Kind.TreeList, self.View.WBP_ShopFoldTab_List.WBP_ComTreeList,
    { { P_TradeTabSec }, { P_TradeTabThird } })--商城百货交易所二级页签
end

function P_TradePanel:OnRefresh(Params)
    self.FirstTabSelIndex = 1
    self.SecondTabSelIndex = 1
    self.ThirdTabSelIndex = 1
    self.DepartmentStoreShowType = 1
    self.SelItemID = -1
    self.UIPanelType = 2
    self.Params = Params
    if self.Params then
        self:CalShowType()
        self:InitTabData()
        self:InitIndex()
    else
        self:InitTabData()
    end
    --列表
    self.View:SetListPadding(self.UIPanelType)
    if self.UIPanelType ~= 0 then
         --左侧列表
         if self.FirstTabSelIndex == 1 or self.FirstTabSelIndex == 2 then
             --商城 or 百货 
            if self.DepartmentStoreShowType ~= 10 then
                self.SideNormListView:SetData(#self.TabData[self.FirstTabSelIndex].Children)
            end
         elseif self.FirstTabSelIndex == 3 then
             --交易所
             self.SideFoldListView:SetData(self.TabData[self.FirstTabSelIndex].Children, true)
         end
        --上侧列表
        if self.UIPanelType == 2 or self.UIPanelType == 3 then
            self.FuncListView:SetData(#self.TabData)
            self.FuncListView:Sel(self.FirstTabSelIndex)
        end
    end
    --标题
    self:InitTitle()
    -- 主界面
    self:UpdateContent(self.FirstTabSelIndex, self.SecondTabSelIndex, self.ThirdTabSelIndex)
    if Game.me then
        Game.me:getMyStallInfo()
    end
end

function P_TradePanel:OnClose()
 
end

function P_TradePanel:InitTitle()
    if self.Params then
        self.View.WBP_BtnBack.Btn_Info:SetVisibility(UE.ESlateVisibility.Collapsed)
        if self.UIPanelType == 0 then
            self.View.WBP_BtnBack.Text_Back:SetText(Game.TableData.GetNpcShopDataRow(self.Params[2]).Name)  
        elseif self.UIPanelType == 1 then
            self.View.WBP_BtnBack.Text_Back:SetText(Game.TableData.GetShopTabDataRow(self.ShopTab).TabName) 
        else
            self.View.WBP_BtnBack.Btn_Info:SetVisibility(UE.ESlateVisibility.Visible)
            self.View.WBP_BtnBack.Text_Back:SetText(StringConst.Get("HUD_SHOPMALL")) 
        end
    else
        self.View.WBP_BtnBack.Btn_Info:SetVisibility(UE.ESlateVisibility.Visible)
        self.View.WBP_BtnBack.Text_Back:SetText(StringConst.Get("HUD_SHOPMALL")) 
    end
    if self.UIPanelType == 2 or self.UIPanelType == 3 then
        self.View.Btn_Consignment:SetVisibility(UE.ESlateVisibility.Visible)
        self.View.Btn_Recharge:SetVisibility(UE.ESlateVisibility.Visible) 
    else
        self.View.Btn_Consignment:SetVisibility(UE.ESlateVisibility.Collapsed) 
        self.View.Btn_Recharge:SetVisibility(UE.ESlateVisibility.Collapsed) 
    end
end

function P_TradePanel:CalShowType()
    self.UIPanelType = 2
    if self.Params[1] == Enum.EFunctionInfoData["MODULE_LOCK_SHOPPING_MALL"] then
        --商城
        if self.Params[2] and self.Params[2] == -1 then
            --只显示单独商城
            self.UIPanelType = 1 
            self.ShopTab = 101
        end
    elseif self.Params[1] == Enum.EFunctionInfoData["MODULE_LOCK_SHOP"] then
        -- 百货
        if self.Params[2] and self.Params[2] == -1 then
            --只显示单独百货
            self.UIPanelType = 1   
            self.DepartmentStoreShowType = 1
            self.ShopTab = 201
        else  
            self.ShopTab = Game.TableData.GetNpcShopDataRow(self.Params[2]).ShopTab 
            if self.ShopTab == 0 then
                --NPC商店 无页签
                self.UIPanelType = 0
                self.DepartmentStoreShowType  = 0
            else
                self.DepartmentStoreShowType = Game.TableData.GetShopTabDataRow(self.ShopTab).ShowType 
                if self.DepartmentStoreShowType ~= 1 then
                    --只有一级页签的商店
                    self.UIPanelType = 1
                end  
            end         
        end
    elseif self.Params[1] == Enum.EFunctionInfoData["MODULE_LOCK_AUCTION"] then  
        self.UIPanelType = 3
    end
end

function P_TradePanel:InitTabData()
    self.TabData = {
        { 
            ID = Enum.EFunctionInfoData["MODULE_LOCK_SHOPPING_MALL"], 
            Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_SHOPPING_MALL"]).FunctionInfo, 
            EnglishName = StringConst.Get("FUNCTION_MALL_LUEN"),
            Children = Game.DepartmentStoreSystem:InitMallTabData() 
        },
        { 
            ID = Enum.EFunctionInfoData["MODULE_LOCK_SHOP"], 
            Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_SHOP"]).FunctionInfo, 
            EnglishName = StringConst.Get("FUNCTION_STORE_LUEN"),
            Children = Game.DepartmentStoreSystem:InitDepartmentStoreTabData(self.DepartmentStoreShowType) 
        },
        { 
            ID = Enum.EFunctionInfoData["MODULE_LOCK_EXCHANGE_HOUSE"], 
            Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_EXCHANGE_HOUSE"]).FunctionInfo, 
            EnglishName = StringConst.Get("FUNCTION_EXCHANGE_LUEN"),
            Children = Game.ExchangeSystem:GetExchangeTabData() 
        },
        { 
            ID = Enum.EFunctionInfoData["MODULE_LOCK_AUCTION"], 
            Name = Game.TableData.GetFunctionInfoDataRow(Enum.EFunctionInfoData["MODULE_LOCK_AUCTION"]).FunctionInfo, 
            EnglishName = StringConst.Get("FUNCTION_AUCTION_LUEN"),
            Children = {} 
        },
    }
end

function P_TradePanel:InitIndex()
    if self.Params[1] == Enum.EFunctionInfoData["MODULE_LOCK_SHOPPING_MALL"] then
        --商城
        self.FirstTabSelIndex = 1
        if self.Params[2] and self.Params[2] ~= -1 then
            self.ShopID = self.Params[2]
            self.SecondTabSelIndex = self:CalShopIndexbyShopID(self.ShopID)
        end
        if self.Params[3] then
            self.SelItemID = self.Params[3]
        end
    elseif self.Params[1] == Enum.EFunctionInfoData["MODULE_LOCK_SHOP"] then
        -- 百货
        self.FirstTabSelIndex = 2
        if self.Params[2] and self.Params[2] ~= -1 then
            self.ShopID = self.Params[2]
            self.SecondTabSelIndex, self.ThirdTabSelIndex = self:CalShopIndexbyShopID(self.ShopID)
        end
        if self.Params[3] then
            self.SelItemID = self.Params[3]
        end
    elseif self.Params[1] == Enum.EFunctionInfoData["MODULE_LOCK_EXCHANGE_HOUSE"] then
        --交易所
        self.FirstTabSelIndex = 3
        if self.Params[3] then
            self.SelItemID = self.Params[3]
        end
        self.SecondTabSelIndex, self.ThirdTabSelIndex, _ = Game.ExchangeSystem:GetUIIndexByItemID(self.SelItemID)
    elseif self.Params[1] == Enum.EFunctionInfoData["MODULE_LOCK_AUCTION"] then
        --拍卖
        self.FirstTabSelIndex = 4
    end
end

function P_TradePanel:CalShopIndexbyShopID(ShopID)
    for K1, V1 in pairs(self.TabData) do
        for K2, V2 in pairs(V1.Children) do
            if V2.ID == ShopID then
                return K2, 1
            end
            if V2.Children then
                for K3, V3 in pairs(V2.Children) do
                    if V3.ID == ShopID then
                        return K2, K3
                    end
                end
            end
        end
    end
    return -1
end

function P_TradePanel:UpdateContent(Tab1, Tab2, Tab3)
    if self.ShowUIName ~= nil and self.ShowUIName ~= "" then
        UI.HideUI(self.ShowUIName)
    end
    if Tab1 == 1 then
        --商城
        self:RefreshFoldList()
        if #self.TabData[Tab1].Children[Tab2].Children > 0 then
            UI.ShowUI("P_ShoppingMall", { Tab2, self.TabData[Tab1].Children[Tab2].Children[Tab3].ID, self.SelItemID })
        else
            UI.ShowUI("P_ShoppingMall", { Tab2, self.TabData[Tab1].Children[Tab2].ID, self.SelItemID })
        end
        self:UpdateCurUIName("P_ShoppingMall")
        self:SetCurrencyData({ 2001000, 2001001, 2001002, 2001003 })
    elseif Tab1 == 2 then
        --百货
        if self.DepartmentStoreShowType == 0 then
            --不显示列表
            self:SetCurrencyData(Game.TableData.GetNpcShopDataTable(self.Params[2]).CurrencyType)
            UI.ShowUI("P_DepartmentStore", {self.Params[2], self.Params[3] })
        else
            if self.DepartmentStoreShowType == 1 then
                --hud商店
                self:RefreshFoldList()
                if #self.TabData[Tab1].Children[Tab2].Children > 0 then
                    self:SetCurrencyData(self.TabData[Tab1].Children[Tab2].Children[Tab3].CurrencyType)
                    UI.ShowUI("P_DepartmentStore", {self.TabData[Tab1].Children[Tab2].Children[Tab3].ID, self.SelItemID })
                else
                    self:SetCurrencyData(self.TabData[Tab1].Children[Tab2].CurrencyType)
                    UI.ShowUI("P_DepartmentStore", {self.TabData[Tab1].Children[Tab2].ID, self.SelItemID })
                end
            else
                --显示单独的百货
                self:RefreshComList()
                if self.TabData[Tab1].Children[Tab2] then
                    self:SetCurrencyData(self.TabData[Tab1].Children[Tab2].CurrencyType)
                    UI.ShowUI("P_DepartmentStore", {self.TabData[Tab1].Children[Tab2].ID, self.SelItemID })
                end
            end
        end
        self:UpdateCurUIName("P_DepartmentStore")
    elseif Tab1 == 3 then
        --交易所
        self:RefreshFoldList()
        if Tab2 == 1 or Tab2 == 2 then
            local PageType = Tab2 == 1 and Game.ExchangeSystem.PageType.FollowPage or Game.ExchangeSystem.PageType.MarketPage
            UI.ShowUI("P_ExchangeFollowAndMarket", Tab2, Tab3, PageType, self.SelItemID)
            self:UpdateCurUIName("P_ExchangeFollowAndMarket")
        else
            UI.ShowUI("P_ExchangeSell")
            self:UpdateCurUIName("P_ExchangeSell")
        end
        self:SetCurrencyData({ 2001000, 2001001, 2001002, 2001003 })
    else
        --拍卖行
        self.View.WBP_ShopComTab_List:SetVisibility(ESlateVisibility.Collapsed)
        self.View.WBP_ShopFoldTab_List:SetVisibility(ESlateVisibility.Collapsed)
        UI.ShowUI("P_Auction", self.SelItemID)
        self:SetCurrencyData({ 2001000, 2001001, 2001002, 2001003 })
        self:UpdateCurUIName("P_Auction")
    end
end

function P_TradePanel:RefreshComList()
    self.View.WBP_ShopComTab_List:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.View.WBP_ShopFoldTab_List:SetVisibility(ESlateVisibility.Collapsed)
    self.SideNormListView:SetData(#self.TabData[self.FirstTabSelIndex].Children)
    self.SideNormListView:Sel(self.SecondTabSelIndex)
end

function P_TradePanel:RefreshFoldList()
    self.View.WBP_ShopComTab_List:SetVisibility(ESlateVisibility.Collapsed)
    self.View.WBP_ShopFoldTab_List:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.SideFoldListView:CancelSel()
    self.SideFoldListView:FoldAll(true)
    self.SideFoldListView:SetData(self.TabData[self.FirstTabSelIndex].Children, true)
    if self.SideFoldListView:IsLeafNode(self.SecondTabSelIndex) then
        self.SideFoldListView:Sel(self.SecondTabSelIndex)
    else
        self.SideFoldListView:Sel(self.SecondTabSelIndex, self.ThirdTabSelIndex)
        self.SideFoldListView:Fold(false, self.SecondTabSelIndex)
    end
end

function P_TradePanel:ManualSelIndex(SecIndex, ThirdIndex)
    self.SideFoldListViewSelIndex = SecIndex
    self.ThirdTabSelIndex = ThirdIndex
    self.SideFoldListView:CancelSel()
    self.SideFoldListView:FoldAll(true)
    if self.SideFoldListView:IsLeafNode(SecIndex) then
        self.SideFoldListView:Sel(SecIndex)
    else
        self.SideFoldListView:Sel(SecIndex, ThirdIndex)
        if self.SideFoldListView:IsFold(SecIndex) then
            self.SideFoldListView:Fold(false, SecIndex)
        end
    end
end

function P_TradePanel:UpdateCurUIName(UIName)
    if UIName == "P_ShoppingMall" or UIName == "P_DepartmentStore" then
        if Game.DepartmentStoreSystem:GetLimitTimer() == nil then
            Game.DepartmentStoreSystem:CreateLimitTimer()
        end
    else
        Game.DepartmentStoreSystem:KillLimitTimer()  
    end
    self.ShowUIName = UIName
end

function P_TradePanel:OnRefresh_FuncList(Widget, Index, Sel)
    local Data = self.TabData[Index]
    Widget:Refresh(Data.Name, Data.EnglishName, Sel, Index, #self.TabData)
end

function P_TradePanel:OnClick_FuncList(Index)
    --切换 商城、百货、交易所、拍卖
    if Index ~= self.FirstTabSelIndex then
        self.FirstTabSelIndex = Index
        self.SecondTabSelIndex = 1
        self.ThirdTabSelIndex = 1
        self.SelItemID = nil
        self:UpdateContent(self.FirstTabSelIndex, self.SecondTabSelIndex, self.ThirdTabSelIndex)
        self.FuncListView:Sel(Index)
        if self.FirstTabSelIndex == 4 then
            self.UIPanelType = 3
        else
            self.UIPanelType = 2
        end
        self.View:SetListPadding(self.UIPanelType)
    end
end

function P_TradePanel:OnRefresh_NormList(widget, index, selected)
    widget:SetSelect(selected)
    widget.Text_ComTab:SetText(self.TabData[self.FirstTabSelIndex].Children[index].Name)
    widget.Text_ComTab_LN:SetText(self.TabData[self.FirstTabSelIndex].Children[index].EnglishName)
end

function P_TradePanel:OnClick_NormList(widget, index)
    if self.SecondTabSelIndex ~= index then
        self.SecondTabSelIndex = index
        self.SideNormListView:Sel(index)
        if self.FirstTabSelIndex == 1 then
            UI.Invoke("P_ShoppingMall", "RefreshUI", { index, self.TabData[self.FirstTabSelIndex].Children[index].ID })
        elseif self.FirstTabSelIndex ==2 then
            UI.Invoke("P_DepartmentStore", "RefreshUI", {self.TabData[self.FirstTabSelIndex].Children[index].ID })
            self:SetCurrencyData(self.TabData[self.FirstTabSelIndex].Children[self.SecondTabSelIndex].CurrencyType)
        end
    end
end

------------------------交易所 start------------------------------------------------------
function P_TradePanel:UpdateSecTab(Index)
    --二级页签更新挂载界面
    if Index ~= self.SecondTabSelIndex then
        self.SecondTabSelIndex = Index
        self.ThirdTabSelIndex = 1
        self.SelItemID = nil
        if self.FirstTabSelIndex == 1 then
            if #self.TabData[self.FirstTabSelIndex].Children[Index].Children == 0 then
                UI.Invoke("P_ShoppingMall", "RefreshUI", {self.FirstTabSelIndex, self.TabData[self.FirstTabSelIndex].Children[Index].ID})
            else
                UI.Invoke("P_ShoppingMall", "RefreshUI", {self.FirstTabSelIndex, self.TabData[self.FirstTabSelIndex].Children[Index].Children[self.ThirdTabSelIndex].ID})
            end
        elseif self.FirstTabSelIndex == 2 then
            if #self.TabData[self.FirstTabSelIndex].Children[Index].Children == 0 then
                UI.Invoke("P_DepartmentStore", "RefreshUI", {self.TabData[self.FirstTabSelIndex].Children[Index].ID})
                self:SetCurrencyData(self.TabData[self.FirstTabSelIndex].Children[self.SecondTabSelIndex].CurrencyType)
            else
                UI.Invoke("P_DepartmentStore", "RefreshUI", {self.TabData[self.FirstTabSelIndex].Children[Index].Children[self.ThirdTabSelIndex].ID})
                self:SetCurrencyData(self.TabData[self.FirstTabSelIndex].Children[self.SecondTabSelIndex].Children[self.ThirdTabSelIndex].CurrencyType)
            end
        elseif self.FirstTabSelIndex == 3 then
            if self.ShowUIName == "P_ExchangeFollowAndMarket" and (self.SecondTabSelIndex == 1 or self.SecondTabSelIndex == 2) then
                UI.Invoke("P_ExchangeFollowAndMarket", "UpdateTabSel", Index, self.ThirdTabSelIndex)
            else
                self:UpdateContent(self.FirstTabSelIndex, self.SecondTabSelIndex, self.ThirdTabSelIndex)
            end
        end
    end

end

function P_TradePanel:UpdateThirdTab(Index2, Index3)
    self.ThirdTabSelIndex = Index3
    if self.FirstTabSelIndex == 1 then
        UI.Invoke("P_ShoppingMall", "RefreshUI", {self.FirstTabSelIndex, self.TabData[self.FirstTabSelIndex].Children[Index2].Children[Index3].ID})
    elseif self.FirstTabSelIndex == 2 then
        UI.Invoke("P_DepartmentStore", "RefreshUI", {self.TabData[self.FirstTabSelIndex].Children[Index2].Children[Index3].ID})
        self:SetCurrencyData(self.TabData[self.FirstTabSelIndex].Children[self.SecondTabSelIndex].Children[self.ThirdTabSelIndex].CurrencyType)
    elseif self.FirstTabSelIndex == 3 then
        if self.ShowUIName == "P_ExchangeFollowAndMarket" and (self.SecondTabSelIndex == 1 or self.SecondTabSelIndex == 2) then
            UI.Invoke("P_ExchangeFollowAndMarket", "UpdateTabSel", Index2, Index3)
        else
            self:UpdateContent(self.FirstTabSelIndex, self.SecondTabSelIndex, self.ThirdTabSelIndex)
        end
    else
        self.ThirdTabSelIndex = Index3
        self:UpdateContent(self.FirstTabSelIndex, self.SecondTabSelIndex, Index2)
    end
end
------------------------交易所 end--------------------------------------------------------

function P_TradePanel:OnClick_WBP_BtnBack_Btn_Back()
   self:CloseSelf()
end

function P_TradePanel:OnClick_WBP_BtnBack_Btn_Info()
    Game.TipsSystem:ShowTips(Enum.ETipsData.SHOPPINGMALL, self.View.WBP_BtnBack.Btn_Info:GetCachedGeometry())
end

function P_TradePanel:OnClick_Btn_Consignment_Btn_ClickArea()
	Game.NewUIManager:OpenPanel(UIPanelConfig.ConsignmentTotal_Panel)
end

function P_TradePanel:OnClick_Btn_Recharge_Btn_ClickArea()
	Game.NewUIManager:OpenPanel("RechargePanel")
end

return P_TradePanel