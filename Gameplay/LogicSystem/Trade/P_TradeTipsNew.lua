local ESlateVisibility = import("ESlateVisibility")

---@class P_TradeTipsNew:UIController
local P_TradeTipsNew       = DefineClass("P_TradeTipsNew", UIComponent)
local StringConst       = require "Data.Config.StringConst.StringConst"
local ComTreasure       = kg_require "Gameplay.LogicSystem.CommonUI.ComTreasure.P_ComTreasure"
local DescFormulaHelper = kg_require "Gameplay.LogicSystem.SkillCustomizer.DescFormulaHelper"
local P_TradeMoneyItem     = kg_require "Gameplay.LogicSystem.Trade.Com.P_TradeMoneyItem"
local WidgetEmptyComponent    = kg_require "Framework.UI.WidgetEmptyComponent"
local P_TradeNumSlider = kg_require "Gameplay.LogicSystem.Trade.Com.P_TradeNumSlider"
local P_TradeTreasureItem = kg_require "Gameplay.LogicSystem.Trade.Com.P_TradeTreasureItem"

P_TradeTipsNew.eventBindMap = {
    [EEventTypesV2.RECEIVE_MONEY_CHANGE] = "SetMoneyUI",
}

function P_TradeTipsNew:OnCreate()
    self.ItemId = nil
    self.PickChestInfoList = nil
    self.PickChestList = nil
    self.BuyNum = nil
    self.NeedMoneyNum = nil
    self.GoodID = nil
    self.Currency0 = self:BindComponent(self.View.WBP_ComCurrency0, P_TradeMoneyItem)
    self.Currency1 = self:BindComponent(self.View.WBP_ComCurrency1, P_TradeMoneyItem)
    self.Currency2 = self:BindComponent(self.View.WBP_ComCurrency2, P_TradeMoneyItem)
    self.NumSlider = self:BindComponent(self.View.WBP_ShopNumInput, P_TradeNumSlider)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_Buy.Btn_Com, "OnClick_Buy")
end

function P_TradeTipsNew:Refresh(Params)
    self.bCanBuy = true
    self.View.ScrollBox_Content:ScrollToStart()
    self:UpdateUI(Params)
    if Params.Style == 'Mall' then
        self:MallStyle()
    end
end

function P_TradeTipsNew:MallStyle()
    -- self.View.Stock:SetVisibility(ESlateVisibility.Collapsed)
    -- self.View.HB_Limit:SetVisibility(ESlateVisibility.Collapsed)
    -- self.View.Amount_Panel:SetVisibility(ESlateVisibility.Collapsed)
    self.View.WBP_Buy:SetVisibility(ESlateVisibility.Collapsed)
    self.View.HB_Currency:SetVisibility(ESlateVisibility.Collapsed)
    self.View.WBP_ShopNumInput:SetVisibility(ESlateVisibility.Collapsed)
end

function P_TradeTipsNew:UpdateUI(Params)
    self.GoodID = Params.ID
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodID)
    if NPCShopTableData == nil then
        return 
    end
    
    self.MoneyIDList = {}
    self.PriceList = {}
    for key, value in ksbcpairs(NPCShopTableData.TokenIDs) do
        table.insert(self.MoneyIDList, key)
        table.insert(self.PriceList, value)
    end
    self.BuyNum = 1
    self:InitNumSlider()
    self.ItemId = NPCShopTableData.ItemID
    --下架时间
    self:SetLimitTimerUIState()
    --拥有数量
    self:SetOwnNumUIState()
    self:RefreshSimpleGoodsUI()
    --icon name
    self:SetIconUIState()
    --限购数
    self:SetLimitBoughtNum()

    self.View.VB_Content:ClearChildren()
    local ItemData = Game.TableData.GetItemNewDataRow(self.ItemId)
    --普通文本
    self:InitNormalText(ItemData)
    --屬性
    self:InitPreviewEquip(ItemData)
    --礼包
    self:InitTreasure(ItemData)
    --ip文本
    self:InitIPText(ItemData)
    --购买数量、货币
    self:SetMoneyUI()
end

function P_TradeTipsNew:InitNumSlider()
    local MaxNum = Game.DepartmentStoreSystem:GetRemainLimitBuyCount(self.GoodID)
    if MaxNum == -1 then
        MaxNum = Game.DepartmentStoreSystem:GetCanBuyMaxNum(self.GoodID, 999)
    end    
    self.NumSlider:RefreshSlider(Enum.SliderType.MaxNum, 0, 1, 
        MaxNum,
        self.BuyNum,
        nil, Enum.SliderContentType.Num, true,
        function(Count)
            self:UpdateRPNum(Count)
        end
    )
end

function P_TradeTipsNew:UpdatePartUI()
    self:SetLimitBoughtNum()
    self:SetOwnNumUIState()
    self:InitNumSlider()
end

function P_TradeTipsNew:SetLimitTimerUIState()
    local CurTime = Game.TimeUtils.GetCurTime()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodID)
    if NPCShopTableData.LimitTimeUpStamp ~= 0 and NPCShopTableData.LimitTimeUpStamp <= CurTime and
        NPCShopTableData.LimitTimeDownStamp ~= 0 and CurTime <= NPCShopTableData.LimitTimeDownStamp then
        self.View.HB_TimeLimit:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        local LastTime = NPCShopTableData.LimitTimeDownStamp - CurTime
        self:StartTimer("RefreshTimer", function()
            if LastTime < 0 then
                self:StopTimer("RefreshTimer")
                return
            else
                local handledTime = ""
                if LastTime >= 60 * 60 * 24 then
                    --大于一天 "xx天"
                    handledTime = LastTime // (60 * 60 * 24).."天"
                elseif LastTime >= 60 * 60 then        
                    --小于一天 "xx时"
                    handledTime = LastTime // (60 * 60).."时"
                elseif LastTime > 60 then
                    -- 小于1小时，大于1分
                     handledTime = LastTime // 60 .."分"
                else
                    handledTime = "<1分"
                end
                self.View.Text_TimeLeft:SetText(handledTime)
                LastTime = LastTime - 30000
            end
        end, 60000, -1, nil, true)
    else
        self.View.HB_TimeLimit:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_TradeTipsNew:SetOwnNumUIState()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodID)
    if Game.TableData.GetItemNewDataRow(NPCShopTableData.ItemID).type == 0 then
        self.View.Text_OwnNum:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.View.Text_OwnNum:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.View.Text_OwnNum:SetText("已拥有：".. Game.BagSystem:GetItemCount(NPCShopTableData.ItemID))
    end
end

function P_TradeTipsNew:SetLimitBoughtNum()
    local bAllServer, UpdateType, AllLimitNum = Game.DepartmentStoreSystem:GetLimitInfo(self.GoodID)
    if AllLimitNum == -1 then
        --不限购
        self.View.HB_Limit:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.View.HB_Limit:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        if bAllServer then
            self.View.Text_LimitType:SetText(StringConst.Get("STORE_BUY_ALLSERVER_LIMIT"))
        else
            if UpdateType == 0 then
                self.View.Text_LimitType:SetText(StringConst.Get("STORE_BUY_PERMANENT"))
            else
                local TypeString = Game.DepartmentStoreSystem.RefreshType[UpdateType]
                self.View.Text_LimitType:SetText(StringConst.Get("STORE_EVERY") ..
                    TypeString .. StringConst.Get("STORE_BUY_LIMIT"))
            end
        end
        if Game.DepartmentStoreSystem:GetPriceType(self.GoodID) == Enum.SHOP_GOODS_PRICE_TYPE.Discount then
            self.View.Text_BoughtLimit:SetText(Game.DepartmentStoreSystem:GetDiscountPriceBoughtCount(self.GoodID) .. "/" .. AllLimitNum)
        else
            if Game.DepartmentStoreSystem:IsSoldOut(self.GoodID) then
                self.View.Text_BoughtLimit:SetText(Game.DepartmentStoreSystem:GetTotalBoughtCount(self.GoodID) .. "/" .. AllLimitNum)
            else
                self.View.Text_BoughtLimit:SetText(Game.DepartmentStoreSystem:GetNormalPriceBoughtCount(self.GoodID) .. "/" .. AllLimitNum)
            end
        end
    end
end

function P_TradeTipsNew:RefreshSimpleGoodsUI()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodID)
    local ItemTableData = Game.TableData.GetItemNewDataRow(NPCShopTableData.ItemID)
    --品质
    local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData["SHOP_QUAILITY_"..ItemTableData.quality])
    self:SetImage(self.View.WBP_ShopGoodsSimple.Img_Quality, iconData.AssetPath)
    --商品icon
    self:SetImage(self.View.WBP_ShopGoodsSimple.Img_Goods, Game.UIIconUtils.GetIconByItemId(NPCShopTableData.ItemID, Enum.EUIIconType.Large))
    --折扣
    if Game.DepartmentStoreSystem:IsFree(self.GoodID) then
        self.View.WBP_ShopGoodsSimple:Event_UI_Style(false)
    else
        if Game.DepartmentStoreSystem:GetPriceType(self.GoodID) == Enum.SHOP_GOODS_PRICE_TYPE.Discount then
            --打折
            self.View.WBP_ShopGoodsSimple:Event_UI_Style(true)
            local Percent = NPCShopTableData.DiscountPrice / self.PriceList[1]
            if 10 * NPCShopTableData.DiscountPrice % self.PriceList[1] == 0 then
                Percent = math.floor(Percent * 10)
            else
                Percent = Percent * 100 / 10
            end
            self.View.WBP_ShopGoodsSimple.Text_Discount:SetText(Percent .. "折")
        else
            --原价
            self.View.WBP_ShopGoodsSimple:Event_UI_Style(false)
        end
    end
end

function P_TradeTipsNew:InitNormalText(ItemExcelData)
    --- func说明
    if ItemExcelData.funcRep and ItemExcelData.funcRep ~= "" then
        self.NormTextWidgetComp = self:FormComponent('WBP_ShopDesRichText', self.View.VB_Content, WidgetEmptyComponent)
        self.NormTextWidgetComp.View.RTB_Des:SetText(DescFormulaHelper.FormDescInType(ItemExcelData.funcRep, "ShopDefault"))
    end
end

function P_TradeTipsNew:InitIPText(ItemExcelData)
    --- IP说明
    if ItemExcelData.itemDes and ItemExcelData.itemDes ~= "" then
        self:FormComponent('Line', self.View.VB_Content, WidgetEmptyComponent)
        self.IPTextWidgetComp = self:FormComponent('WBP_ShopDesRichText', self.View.VB_Content, WidgetEmptyComponent)
        self.IPTextWidgetComp.View.RTB_Des:SetText(DescFormulaHelper.ReplaceDescInType(ItemExcelData.itemDes, "ShopPropInfo"))
    end
end

function P_TradeTipsNew:InitTreasure(ItemExcelData)
    -- 宝箱的礼包展示
    -- 是否是宝箱
    self.IsTreasure = ItemExcelData.subType == 2004
    if self.IsTreasure then
        local rewardInfoList = Game.BagSystem:GetTreasureData(ItemExcelData.ID)
        for key, value in pairs(rewardInfoList) do
            self:FormComponent('WBP_ShopDscItem_Item', self.View.VB_Content, P_TradeTreasureItem, self.ItemId, value)
        end
    end
end

---装备预览tips
function P_TradeTipsNew:InitPreviewEquip(ItemExcelData)

    local equipData = Game.EquipmentSystem:GetEquipData(self.ItemId)
    if not equipData then
        return
    end
    self:FormComponent('Line', self.View.VB_Content, WidgetEmptyComponent)
    local TypeData = Game.TableData.GetEquipmentTypeDataRow(equipData.subType)
    -- 基础属性
    if TypeData.BasicPropAppear and #TypeData.BasicPropAppear > 0 then
        local val1 = equipData[TypeData.BasicPropName[1]]
        local val2 = equipData[TypeData.BasicPropName[2]]
        local propItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
        propItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
        propItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
        propItem.View.RTB_Key:SetText(DescFormulaHelper.FormDescInType(TypeData.BasicPropAppear[1], "ShopAttribute"))
        propItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
        propItem.View.RTB_Val:SetText("<Default>" .. val1 .. "-" .. val2 .. "</>")
        propItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
    else
        for idx, propName in ksbcpairs(TypeData.BasicPropName) do
            local PropNameData = Game.TableData.GetFightPropDataRow(propName)
            local Title = ""
            if PropNameData then
                Title = PropNameData.Discription
            else
                local PropId = Enum.EFightPropModeData[propName]
                local PropModeData = Game.TableData.GetFightPropModeDataRow(PropId)
                if PropModeData then
                    Title = PropModeData.PropName
                else
                    Title = 'No Title'
                end
            end
            local propItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
            propItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
            propItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
            propItem.View.RTB_Key:SetText(DescFormulaHelper.FormDescInType(Title, "ShopAttribute"))
            propItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
            propItem.View.RTB_Val:SetText("<Default>" .. equipData[propName] .. "</>")
            propItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
    self:FormComponent('Line', self.View.VB_Content, WidgetEmptyComponent)

    local Quality = ItemExcelData.quality
    --- 固定词条
    if equipData.FixedWord and #equipData.FixedWord > 0 then
        for index, groupId in pairs(equipData.FixedWord) do
            local GroupData = Game.TableData.GetEquipmentWordFixedGroupDataRow(groupId)
            if GroupData then
                local PropItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
                PropItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetText(DescFormulaHelper.FormDescInType(GroupData.Des, "ShopAttribute"))
                PropItem.View.RTB_Val:SetText(' +?', "Default")
            end
        end
    else
        local EquipRarityData = Game.TableData.GetEquipmentRarityDataRow(Quality)
        if EquipRarityData.FixedWordNum > 0 then
            for i = 1, EquipRarityData.FixedWordNum do
                local PropItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
                PropItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
                PropItem.View.RTB_Val:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetVisibility(ESlateVisibility.Visible)
                PropItem.View.RTB_Key:SetText(DescFormulaHelper.FormDescInType(StringConst.Get("EQUIP_MYSTERY_ENTRY") .. ":", "ShopAttribute") )
                PropItem.View.RTB_Val:SetText(StringConst.Get("EQUIP_WAIT_TO_OPENED"))
            end
        end
    end
    self:FormComponent('Line', self.View.VB_Content, WidgetEmptyComponent)
    --- 随机词条
    local RandomPropItem = self:FormComponent('Equipment', self.View.VB_Content, WidgetEmptyComponent)
    RandomPropItem.View.RTB_Key:SetText(DescFormulaHelper.FormDescInType(StringConst.Get("EQUIP_RANDOM_PROP_SYMBOL"), "ShopAttribute"))
    RandomPropItem.View.RTB_ValMax:SetVisibility(ESlateVisibility.Collapsed)
    RandomPropItem.View.RTB_Val:SetVisibility(ESlateVisibility.Collapsed)
    RandomPropItem.View.WidgetSwitcher:SetVisibility(ESlateVisibility.Collapsed)
end

function P_TradeTipsNew:SetIconUIState()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodID)
    local ItemTableData = Game.TableData.GetItemNewDataRow(NPCShopTableData.ItemID)
    local IconPath = ""
    if ItemTableData then
        self.View.Text_Name:SetText(ItemTableData.itemName)
        self.View.Text_Luen:SetText(ItemTableData.EnglishName ~= nil and ItemTableData.EnglishName or "")
    end
end

function P_TradeTipsNew:SetMoneyUI()
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodID)
    self.NeedMoneyNum = 0
    if self.MoneyIDList[2] then
        self.View.WBP_ComCurrency0:SetVisibility(ESlateVisibility.Collapsed)
        self.View.HB_Currency_Double:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        --两种货币的指定原价
        if self.PriceList and self.PriceList[1] then
            self.Currency1:Init(self.MoneyIDList[1], self.BuyNum * self.PriceList[1], false, self.BuyNum * self.PriceList[1])
        end
        if self.PriceList and self.PriceList[2] then
            self.Currency2:Init(self.MoneyIDList[2], self.BuyNum * self.PriceList[2], false, self.BuyNum * self.PriceList[2])
        end
    else
        self.View.WBP_ComCurrency0:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.HB_Currency_Double:SetVisibility(ESlateVisibility.Collapsed)
        local PriceType = Game.DepartmentStoreSystem:GetPriceType(self.GoodID)
        if PriceType == Enum.SHOP_GOODS_PRICE_TYPE.Discount then
            if Game.DepartmentStoreSystem:IsFree(self.GoodID) then
                self.Currency0:Init(self.MoneyIDList[1], self.NeedMoneyNum, true, self.BuyNum * self.PriceList[1])
            else
                self.NeedMoneyNum = self.BuyNum * NPCShopTableData.DiscountPrice
                self.Currency0:Init(self.MoneyIDList[1], self.NeedMoneyNum, true, self.BuyNum * self.PriceList[1])
            end
        elseif PriceType == Enum.SHOP_GOODS_PRICE_TYPE.OriginalPrice then
            self.NeedMoneyNum = self.BuyNum * self.PriceList[1]
            self.Currency0:Init(self.MoneyIDList[1], self.NeedMoneyNum, false, self.NeedMoneyNum)
        end
    end

    if Game.DepartmentStoreSystem:IsUnlocked(self.GoodID) == false then
        --未解锁
        self.View.WBP_Buy:SetIsEnabled(false)
        self.NumSlider:Disable(true)
        self.View.WBP_Buy.Text_Com:SetText(StringConst.Get('STORE_PURCHASE'))
    else
        if Game.DepartmentStoreSystem:IsSoldOut(self.GoodID) == false then
            --可以购买
            self.View.WBP_Buy:SetIsEnabled(true)
            self.NumSlider:Disable(false)
            if Game.DepartmentStoreSystem:IsFree(self.GoodID) then
                self.View.WBP_Buy.Text_Com:SetText(StringConst.Get('STORE_FREE_OBTAIN'))
            else
                self.View.WBP_Buy.Text_Com:SetText(StringConst.Get('STORE_PURCHASE'))
            end
        else
            self.View.WBP_Buy:SetIsEnabled(false)
            self.NumSlider:Disable(true)
            if Game.DepartmentStoreSystem:IsFree(self.GoodID) then
                self.View.WBP_Buy.Text_Com:SetText(StringConst.Get('STORE_HAVE_RECEIVED'))
            else
                self.View.WBP_Buy.Text_Com:SetText(StringConst.Get('STORE_PURCHASE'))
            end
        end
    end
end

function P_TradeTipsNew:UpdateRPNum(Count)
    self.BuyNum = Count
    self:SetMoneyUI()
end

function P_TradeTipsNew:OnClick_Buy()
    if self.bCanBuy == false then
        return 
    end
    self.bCanBuy = false
    self:StartTimer(
        "ButtonCdTimer",
        function()
            self.bCanBuy = true
        end, 1000 / 3, 1)

    local RemainLimitBuyCount = Game.DepartmentStoreSystem:GetRemainLimitBuyCount(self.GoodID)
    if RemainLimitBuyCount ~= -1 then
        if RemainLimitBuyCount < self.BuyNum then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NPC_SHOP_BEYOND_LIMIT_COUNT)
            return
        end
    end
    local NPCShopTableData = Game.TableData.GetNpcGoodsDataRow(self.GoodID)
    if Game.DepartmentStoreSystem:IsFree(self.GoodID) then
        Game.DepartmentStoreSystem:IntoPurchaseProcess(self.GoodID, NPCShopTableData.ShopID, self.BuyNum)
    else
        Game.DepartmentStoreSystem:IntoPurchaseProcess(self.GoodID, NPCShopTableData.ShopID, self.BuyNum)
    end
end

return P_TradeTipsNew
