---@class P_TradeCurrency : UIController
--- @field public View WBP_TradeTabLv2ExpandView
local P_TradeCurrency = DefineClass("P_TradeCurrency", UIComponent)

function P_TradeCurrency:OnCreate()
    self.Data = nil
end

function P_TradeCurrency:FillItem(UIType, MoneyID, Num, bLack)
    self.View.WidgetRoot:Event_UI_Style(UIType, bLack)
    self.View.Text_Count:SetText(Num)
    local MoneyId = MoneyID
    local ItemTableData = Game.TableData.GetItemNewDataRow(MoneyId)
    local IconPath = ""
    if ItemTableData then
        IconPath = Game.UIIconUtils.GetIconByItemId(MoneyId)
    end
   
    if IconPath then
        self:SetImage(self.View.Icon, IconPath)
    end
    
end

return P_TradeCurrency
