---@class P_TradeNumSlider:UIComponent
local P_TradeNumSlider = DefineClass("P_TradeNumSlider", UIComponent)

function P_TradeNumSlider:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Add, self.OnClickSliderAddBtn)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Sub, self.OnClickSliderDeleteBtn)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Max, self.OnClickSliderTopBtn)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Num, self.OnClickInputBtn)

    self.count = nil
    self.minValue = nil
    self.maxValue = nil
    self.calculatorOkCallback = nil
    self.refreshCallback = nil
    self.stepSize = nil
end

--- Ratio 点击+ -增减比例，默认值0
--- sliderCaptureEndCallBack: slider滑动结束的回调
--- initCount:显示Count的初始值
--- bShowTopBtn 0不显示最大按钮 1显示最大按钮
function P_TradeNumSlider:RefreshSlider(isMathFloor, Ratio, minValue, maxValue, initCount,
                                     bShowTopBtn, sliderContentType, bShowCalculatorPopup, refreshCallback)
    self.isBidData = isMathFloor
    self.AddCount = 0
    self.initCount = initCount
    self.count = initCount or 1
    self.maxValue = maxValue
    self.refreshCallback = refreshCallback
    self.Ratio = Ratio
    self.sliderContentType = sliderContentType
    self.bShowCalculatorPopup = bShowCalculatorPopup
    --- 默认从1开始显示
    self.minValue = minValue
    self.bDisable = false
    self:UpdateValue()
end

--- 点击减号
function P_TradeNumSlider:OnClickSliderDeleteBtn()
    if self.bDisable == true then
        return
    end
    if self.count > self.minValue then
        if self.Ratio ~= 0 then
            self.AddCount = self.AddCount - 1
            self.count = self.initCount * (self.Ratio ^ self.AddCount)
            if self.isBidData then
                self.count = Game.AuctionManager:HandleData(self.count)
            end
        else
            self.count = self.count - 1
        end
        self.count = math.floor(self.count < self.minValue and self.minValue or self.count)
        self:UpdateValue()
        self.refreshCallback(self.count, self.AddCount)
    else
        if self.sliderContentType == Enum.SliderContentType.Price then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_PRICE_MIN)
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MIN)
        end
    end
end

--- 点击加号
function P_TradeNumSlider:OnClickSliderAddBtn()
    if self.bDisable == true then
        return
    end
    if self.count >= self.maxValue then
        if self.sliderContentType == Enum.SliderContentType.Price then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_PRICE_MAX)
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MAX)
        end
        return
    end
    if self.Ratio ~= 0 then
        self.AddCount = self.AddCount + 1
        self.count = self.initCount * (self.Ratio ^ self.AddCount)
        if self.isBidData then
            self.count = Game.AuctionManager:HandleData(self.count)
        end
    else
        self.count = self.count + 1
    end
    self.count = math.floor(self.count > self.maxValue and self.maxValue or self.count)
    self:UpdateValue()
    self.refreshCallback(self.count, self.AddCount)
end

--- 点击最大按钮
function P_TradeNumSlider:OnClickSliderTopBtn()
    if self.bDisable == true then
        return
    end
    self:PlayAni(self.View.Ani_ButtonClickMax)
    self.count = self.maxValue
    if self.count < self.minValue then
        self.count = self.minValue
    end
    self:UpdateValue()
    self.refreshCallback(self.count)
end

function P_TradeNumSlider:UpdateValue()
    self.View.CountText:SetText(math.round(self.count))
end

--- 点击输入框
function P_TradeNumSlider:OnClickInputBtn()
    if self.bDisable == true then
        return
    end
    if self.bShowCalculatorPopup then
        local viewportPosition, anchors = Game.UITipsPosAutoFollowUtils.GetPanelAdaptPosFollowWidget(self.View.WidgetRoot)
        UI.ShowUI('ComNumInput_Panel', {
            SelectableMaximum = self.maxValue,
            SelectableMinimum = self.minValue,
            FinishCallback = function(State, InputNum)
                self:FinishCallback(State, InputNum)
            end,
			viewportPosition = viewportPosition,
			anchors = anchors,
        })
    end
end

function P_TradeNumSlider:FinishCallback(state, InputNum)
    if state then
        self.count = InputNum
        self:UpdateValue()
        self.refreshCallback(self.count)
    end
end

--- 播放动效
function P_TradeNumSlider:PlayAni(aniName)
    --self:PlayAnimation(self.View.WidgetRoot, aniName, 0.0, 1, import("EUMGSequencePlayMode").Forward, 1, false)
end

function P_TradeNumSlider:Disable(bDisable)
    self.bDisable = bDisable
end

return P_TradeNumSlider
