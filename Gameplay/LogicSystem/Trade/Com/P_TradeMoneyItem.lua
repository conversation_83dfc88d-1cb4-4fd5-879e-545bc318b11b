---@class P_TradeMoneyItem:UIComponent
local P_TradeMoneyItem = DefineClass("P_TradeMoney", UIComponent)

function P_TradeMoneyItem:OnCreate()

end


function P_TradeMoneyItem:Init(MoneyID, Price, bDiscount, OriPrice)
    self.MoneyID = MoneyID
    local ItemTableData = Game.TableData.GetItemNewDataRow(self.MoneyID)
    local IconPath = ""
    if ItemTableData then
        IconPath = Game.UIIconUtils.GetIconByItemId(self.MoneyID)
    end
    if IconPath then
        self:SetImage(self.View.Img_Currency, IconPath)
    end
    self:RefreshNum(Price, bDiscount, OriPrice)
end

function P_TradeMoneyItem:RefreshNum(Price, bDiscount, OriPrice)
    local OwnMoneyNum = Game.BagSystem:GetItemCount(self.MoneyID)
    if bDiscount then
        if OwnMoneyNum < Price then
            self.View:SetType(3)
        else
            self.View:SetType(2)
        end
    else
        if OwnMoneyNum < Price then
            self.View:SetType(1)
        else
            self.View:SetType(0)
        end
    end
    self.View.Text_Price:SetText(Price)
    self.View.Text_MetaPrice:SetText(OriPrice)
end

return P_TradeMoneyItem
