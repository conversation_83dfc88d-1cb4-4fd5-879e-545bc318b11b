local ESlateVisibility = import("ESlateVisibility")

---@class P_ExchangeSellShelveItem : UIController
--- @field public View WBP_ExhangeSellItemView
local P_ExchangeSellShelveItem = DefineClass("P_ExchangeSellShelveItem", UIComponent)
local P_TradeCurrency = kg_require "Gameplay.LogicSystem.Trade.P_TradeCurrency"
local ItemTrade = kg_require "Gameplay.LogicSystem.Item.ItemTrade"
local StringConst = require "Data.Config.StringConst.StringConst"

function P_ExchangeSellShelveItem:OnCreate()
    self.Data = nil
	self.Time = {}
    self.ItemTrade = self:BindComponent(self.View.WBP_TradeItem, ItemTrade)
    self.Currency = self:BindComponent(self.View.WBP_TradeCurrency, P_TradeCurrency)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, "OnClick_OffShelve")
end

--获得倒计时时间的格式化输出
---@param milliseconds number 时长（毫秒）
---@return string 格式化的时间字符串
function P_ExchangeSellShelveItem:GetCountDownTimeFormatString(milliseconds)
	milliseconds = math.floor(milliseconds)

	local hour = milliseconds // 3600000
	local min = (milliseconds%3600000) // 60000

	local timeString = string.format("%02d:%02d", hour, min)
	return timeString
end

function P_ExchangeSellShelveItem:SetTimeUI()
    local timerKey = "P_ExchangeSellShelveItem" .. self.Index
    self.RefreshTimer = self:StartTimer(timerKey,
        function()
            if self.Data == nil then
                self:StopTimer(timerKey)
                Log.ErrorFormat("timer data is nil, timerKey:%s", timerKey)
                return
            end
            if self.Data.goodsType == Game.ExchangeSystem.GoodsType.Publicity then
                if self.Data.expiryTime < Game.TimeUtils.GetCurTime() then
                    self:StopTimer(timerKey)
                    return
                end
                local handledTime = self:GetCountDownTimeFormatString(
                    (self.Data.expiryTime - Game.TimeUtils.GetCurTime()) * 1000
				)
                self.View.Text_Time:SetText(string.format(StringConst.Get("EXCHANGE_ON_PUBLIC_TIP"),handledTime))
            else
                if self.Data.soleOutTime < Game.TimeUtils.GetCurTime() then
                    self:StopTimer(timerKey)
                    return
                end
                local handledTime = self:GetCountDownTimeFormatString(
                    (self.Data.soleOutTime - Game.TimeUtils.GetCurTime()) * 1000
				)
                self.View.Text_Time:SetText(string.format(StringConst.Get('TIME_OUT'),handledTime))
            end
        end,
        1000, -1, nil, true
    )
end

function P_ExchangeSellShelveItem:Refresh(Data, Index)
    self.Data = Data
    self.Index = Index
    self:StopTimer("P_ExchangeSellShelveItem" .. self.Index)
    if self.Data then
        self.View.Overlay_Date:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.Overlay_Empty:SetVisibility(ESlateVisibility.Collapsed)
        --时间
        if self.Data.goodsType == Game.ExchangeSystem.GoodsType.Publicity or
            self.Data.goodsType == Game.ExchangeSystem.GoodsType.Selling then
            --公示物品   --在售物品
            self.View.Overlay_TimeOut:SetVisibility(ESlateVisibility.Collapsed)
            self.View.Overlay_CountDown:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self:SetTimeUI()
        else
            self.View.Overlay_TimeOut:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.View.Overlay_CountDown:SetVisibility(ESlateVisibility.Collapsed)
        end
        --图片、数量、货币信息
        local ItemTableData = Game.TableData.GetItemNewDataRow(self.Data.item.itemId)
        self.ItemTrade:FillItem(self.Data.item.itemId, true, self.Data.item.count, self.Data.item, true)
        self.View.Text_Name:SetText(ItemTableData.itemName)
        self.Currency:FillItem(7, 2001001, Game.DungeonBattleStatisticsSystem:GetFormatNumberString(self.Data.price), false)
        self.View.WidgetRoot:Event_UI_Style(ItemTableData.quality)
    else
        self.View.Overlay_Date:SetVisibility(ESlateVisibility.Collapsed)
        self.View.Overlay_Empty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

function P_ExchangeSellShelveItem:OnClick_OffShelve()
    --下架界面
    if self.Data ~= nil then
        UI.ShowUI("P_OnOffShelve", Game.ExchangeSystem.OnOffShelvePageType.PutOffShelve, self.Data)
    end
end

return P_ExchangeSellShelveItem
