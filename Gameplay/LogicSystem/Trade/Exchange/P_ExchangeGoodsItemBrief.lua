local ESlateVisibility = import("ESlateVisibility")

---@class P_ExchangeGoodsItemBrief : UIController
--- @field public View WBP_TradeGoodsItemHView
local P_ExchangeGoodsItemBrief = DefineClass("P_ExchangeGoodsItemBrief", UIComponent)
local P_TradeCurrency = kg_require "Gameplay.LogicSystem.Trade.P_TradeCurrency"
local ItemTrade = kg_require "Gameplay.LogicSystem.Item.ItemTrade"

function P_ExchangeGoodsItemBrief:OnCreate()
    --data
    self.Data = nil
    --item trade component
    self.ItemTrade = self:BindComponent(self.View.WBP_TradeItem, ItemTrade)
    --currency component
    self.Currency = self:BindComponent(self.View.WBP_ComCurrency, P_TradeCurrency)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, "OnClick_Item")
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_TradeBtnFollow.Btn_ClickArea, "OnClick_Follow")
end

function P_ExchangeGoodsItemBrief:OnRefresh(Data, Sel)

end

function P_ExchangeGoodsItemBrief:UpdateFollowState(bFollow)
    self.Follow = bFollow
    self.View.WBP_TradeBtnFollow:Event_UI_Style(bFollow)
end

function P_ExchangeGoodsItemBrief:Refresh(Data, Index, sel, bShowFollow, bShowTime, ListIndex, bPublicityGoods, bShowSel)
    if Data == nil then
        return
    end
    self.Data = Data
    self.Index = Index
    self.ListIndex = ListIndex
    self.bPublicityGoods = bPublicityGoods
    self.bEquip = false
    self.bShowSel = bShowSel
    local ItemExcelData = Game.TableData.GetItemNewDataRow(self.Data.item.itemId)
    if ItemExcelData then
        if ItemExcelData.type == Game.BagSystem.ItemType.EQUIPMENT then
            self.bEquip = true
        end
    end
    self.View.WidgetRoot:Event_UI_Style(ItemExcelData.quality)
    if bShowFollow == false then
        self.View.WBP_TradeBtnFollow:SetVisibility(ESlateVisibility.Collapsed)
    else
        local ItemExcelData = Game.TableData.GetItemNewDataRow(self.Data.item.itemId)
        if ItemExcelData then
            if ItemExcelData.type == Game.BagSystem.ItemType.EQUIPMENT then
                if Game.ExchangeSystem:IsInstanceFollowed(self.Data.item.gbId) then
                    self:UpdateFollowState(true)
                else
                    self:UpdateFollowState(false)
                end
            else
                if Game.ExchangeSystem:IsTypeFollowed(self.Data.item.itemId) then
                    self:UpdateFollowState(true)
                else
                    self:UpdateFollowState(false)
                end
            end
        end
        self.View.WBP_TradeBtnFollow:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    if self.RefreshTimer then
        self:StopTimer(self.RefreshTimer)
    end
    if bShowTime then
        self.RefreshTimer = self:StartTimer("P_ExchangeGoodsItemBrief" .. self.Index,
            function()
                if self.Data.expiryTime < Game.TimeUtils.GetCurTime() then
                    self:StopTimer("P_ExchangeGoodsItemBrief" .. self.Index)
                else
                    self.View.Overlay_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                    local handledTime = Game.TimeUtils.FormatCountDownString(
                        (self.Data.expiryTime - Game.TimeUtils.GetCurTime()) * 1000,
                        false)
                    self.View.Text_Time:SetText(handledTime)
                end
            end,
            1000, -1, nil, true,
            function()
                self.View.Overlay_Time:SetVisibility(ESlateVisibility.Collapsed)
            end
        )
    else
        self.View.Overlay_Time:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.ItemTrade:FillItem(self.Data.item.itemId, true, self.Data.item.count, self.Data.item, true)
    self.Currency:FillItem(7, 2001001, self.Data.price, false)

    if sel and bShowSel then
        self.View.Img_Select:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.View.Img_Select:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_ExchangeGoodsItemBrief:OnClick_Item()
    if self.bShowSel == true then
        --只有装备可以点击
        UI.Invoke("P_ChildGoodsPage", "UpdateSelIndex", self.Index)
    end
end

function P_ExchangeGoodsItemBrief:OnClick_Follow()
    --是否关注
    if self.bEquip then
        if self.Follow == false then
            Game.me:stallFollowItem(self.Data.item.itemId, self.ListIndex, self.Data.item.gbId, self.bPublicityGoods)
        else
            Game.me:stallCancelFollowItem(self.Data.item.gbId)
        end
    else
        if self.Follow == false then
            --关注品类
            Game.me:stallFollowItemType(self.Data.item.itemId)
        else
            Game.me:stallCancelFollowItemType(self.Data.item.itemId)
        end
    end
end

return P_ExchangeGoodsItemBrief
