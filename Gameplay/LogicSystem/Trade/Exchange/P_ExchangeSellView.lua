--------No Class Find, Create a New UIController Class Using Given Filename--------
---@class WBP_ComComBoxView : WBP_ComComBox_C
---@field public WidgetRoot WBP_ComComBox_C
---@field public Button UC7Button
---@field public Text_Target UTextBlock
---@field public Img_Arrow UImage
---@field public CanvasPanel_Options UCanvasPanel
---@field public DEOptions UDynamicEntryBox
---@field public Ani_Spread UWidgetAnimation
---@field public Ani_Fewer UWidgetAnimation
---@field public Pos number
---@field public IsExpand boolean
---@field public IsIcon boolean
---@field public IsInverse boolean
---@field public DefaultColor FSlateColor
---@field public InverseColor FSlateColor
---@field public DefaultBg FSlateBrush
---@field public InverseBg FSlateBrush
---@field public TitleWidth number
---@field public TitleHeight number
---@field public ContentWidth number
---@field public ContentHeight number
---@field public NormalOptionBGColor FLinearColor
---@field public InverseOptionBGColor FLinearColor
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetTitle fun(self:self,Title:string):void
---@field public SetIcon fun(self:self,IsIcon:boolean):void
---@field public SetInverse fun(self:self,Inverse:boolean):void
---@field public SetItemInverse fun(self:self,Inverse:boolean):void
---@field public SetIconPreview fun(self:self,IconTitle:FSlateBrush):void
---@field public UpdateType fun(self:self,Pos:number):void
---@field public ToggleOptionsVisible fun(self:self):void
---@field public SetOptionsVisible fun(self:self,Visible:boolean):void
---@field public GetOptionTextColor fun(self:self):FLinearColor,FSlateColor,FLinearColor,FSlateColor


---@class WBP_ComCheckBoxView : WBP_ComCheckBox_C
---@field public WidgetRoot WBP_ComCheckBox_C
---@field public Overlay UOverlay
---@field public CheckBox UC7CheckBox
---@field public TB_Name UTextBlock
---@field public Ani_Press UWidgetAnimation
---@field public TextValue string
---@field public TextColor FSlateColor
---@field public Undetermined boolean
---@field public Has Text boolean
---@field public SetUndetermined fun(self:self,Undetermined:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComBtnIconNewView : WBP_ComBtnIconNew_C
---@field public WidgetRoot WBP_ComBtnIconNew_C
---@field public OutCanvas UCanvasPanel
---@field public Icon UImage
---@field public Text_Name UTextBlock
---@field public Big_Button_ClickArea UC7Button
---@field public Anim_1 UWidgetAnimation
---@field public Anim_2 UWidgetAnimation
---@field public Anim_3 UWidgetAnimation
---@field public Anim_4 UWidgetAnimation
---@field public Ani_Press UWidgetAnimation
---@field public Ani_Hover UWidgetAnimation
---@field public Ani_Tower UWidgetAnimation
---@field public Btn Style FST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound FSlateSound
---@field public Event_UI_Style fun(self:self,BtnName:string):void
---@field public Play Hint Anim fun(self:self):void
---@field public BndEvt__WBP_ComBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Btn Style fun(self:self,Btn Style:FST_ComBtnIcon):void


---@class WBP_ComBtnSwitchView : WBP_ComBtnSwitch_C
---@field public WidgetRoot WBP_ComBtnSwitch_C
---@field public text_close UTextBlock
---@field public text_open UTextBlock
---@field public Big_Button_ClickArea UC7Button
---@field public Ani_Switch_Close UWidgetAnimation
---@field public Ani_Switch_Open UWidgetAnimation
---@field public Ani_Switch UWidgetAnimation
---@field public color_Grey FSlateColor
---@field public color_Select FSlateColor
---@field public Event_UI_Style fun(self:self,bOpen:boolean):void


---@class WBP_TradeTitleView : WBP_TradeTitle_C
---@field public WidgetRoot WBP_TradeTitle_C
---@field public Title UOverlay
---@field public Overlay_Back UOverlay
---@field public Btn_ClickArea UC7Button
---@field public WBP_Job WBP_ComComBoxView
---@field public WBP_Quality WBP_ComComBoxView
---@field public WBP_Level WBP_ComComBoxView
---@field public CB_Num UOverlay
---@field public WBP_CB_Num WBP_ComCheckBoxView
---@field public Btn_Search WBP_ComBtnIconNewView
---@field public Btn_History WBP_ComBtnIconNewView
---@field public WBP_Switch WBP_ComBtnSwitchView


---@class WBP_TradeEmptyView : WBP_TradeEmpty_C
---@field public WidgetRoot WBP_TradeEmpty_C
---@field public Text_Empty UTextBlock


---@class WBP_ComCurrencyView : WBP_ComCurrency_C
---@field public WidgetRoot WBP_ComCurrency_C
---@field public Icon UC7Image
---@field public img_add UImage
---@field public Text_Count UTextBlock
---@field public Text_HasNum UTextBlock
---@field public CostNum UTextBlock
---@field public Text_FullPrice UTextBlock
---@field public Button UC7Button
---@field public Ani_Hover UWidgetAnimation
---@field public Type E_ComCurrencyType
---@field public ColorTip FSlateColor
---@field public ColorCost FSlateColor
---@field public ColorAdd FSlateColor
---@field public ColorLack FSlateColor
---@field public CostPadding FMargin
---@field public TipPadding FMargin
---@field public AddPadding FMargin
---@field public IsLack boolean
---@field public PriceOff boolean
---@field public IsCenter boolean
---@field public CostColor FSlateColor
---@field public NmlColor FSlateColor
---@field public CostFont FSlateFontInfo
---@field public NmlFont FSlateFontInfo
---@field public GiftFont FSlateFontInfo
---@field public ChargeSize FVector2D
---@field public NmlSize FVector2D
---@field public GiftColor FSlateColor
---@field public ChargeType number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self,Type:E_ComCurrencyType):FVector2D,FSlateColor
---@field public SetPriceOff fun(self:self,PriceOff:boolean):void
---@field public SetCenter fun(self:self):void
---@field public SetCharge fun(self:self,ChargeType:number,NmlSize:FVector2D,NmlColor:FSlateColor):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public Btn_Com UC7Button
---@field public OutOverlay UOverlay
---@field public Text_Com UTextBlock
---@field public Text_Time UTextBlock
---@field public Image UC7Image
---@field public Ani_Press UWidgetAnimation
---@field public Ani_Tower UWidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_ExchangeSellView : WBP_ExchangeSell_C
---@field public WidgetRoot WBP_ExchangeSell_C
---@field public WBP_TradeTitle WBP_TradeTitleView
---@field public ListBag UTileViewEx
---@field public WBP_TradeEmpty WBP_TradeEmptyView
---@field public List_OnShelve UTileViewEx
---@field public Text_InCome UTextBlock
---@field public WBP_InCome WBP_ComCurrencyView
---@field public Text_Deposit UTextBlock
---@field public WBP_Deposit WBP_ComCurrencyView
---@field public WBP_ComBtn WBP_ComBtnView


---@class P_ExchangeSellView : WBP_ExchangeSellView
---@field public controller P_ExchangeSell
local P_ExchangeSellView = DefineClass("P_ExchangeSellView", UIView)

function P_ExchangeSellView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_TradeTitle.Btn_Search.Big_Button_ClickArea, "OnClick_WBP_TradeTitle_Btn_Search_Big_Button_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_TradeTitle.Btn_History.Big_Button_ClickArea, "OnClick_WBP_TradeTitle_Btn_History_Big_Button_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComBtn.Btn_Com, "OnClick_WBP_ComBtn_Btn_Com")

end

function P_ExchangeSellView:OnDestroy()
    local controller = self.controller
---DeletePlaceHolder
end

return P_ExchangeSellView
