---@class WBP_ItemNmlView : WBP_ItemNml_C
---@field public WidgetRoot WBP_ItemNml_C
---@field public Bg_Rarity UImage
---@field public Icon UImage
---@field public TB_Text UTextBlock
---@field public Icon_ExpiredTime UImage
---@field public icon_ban UC7Image
---@field public BG_CD UImage
---@field public Text_CD UTextBlock
---@field public TB_Name UC7RichTextBlock
---@field public IconTrade FSlateBrush
---@field public IconNml FSlateBrush
---@field public IconEquip FSlateBrush
---@field public Status number
---@field public Left Up number
---@field public Is Locked boolean
---@field public Is Timeliness boolean
---@field public Is Advent boolean
---@field public Is Score Up boolean
---@field public Quality number
---@field public Is New boolean
---@field public Event_UI_Style fun(self:self,Status:number,LeftUp:number,IsLocked:boolean,IsTimeliness:boolean,IsAdvent:boolean,IsScoreUp:boolean,IsNew:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetLeftUp fun(self:self,LeftUp:number):void
---@field public SetLeftDown fun(self:self,IsLocked:boolean):void
---@field public SetRightUp fun(self:self,IsTimeliness:boolean,IsAdvent:boolean,IsScoreUp:boolean):void
---@field public SetStatus fun(self:self,Status:number):void
---@field public SetQuality fun(self:self,Quality:number):void
---@field public SetNew fun(self:self,IsNew:boolean):void


---@class WBP_ItemRewardView : WBP_ItemReward_C
---@field public WidgetRoot WBP_ItemReward_C
---@field public WBP_ItemNml WBP_ItemNmlView
---@field public New_Tip UOverlay
---@field public Received UOverlay
---@field public Image_select UC7Image
---@field public Big_Button_ClickArea UC7Button
---@field public Ani_NewTip UWidgetAnimation
---@field public Ani_NewTip_Loop UWidgetAnimation
---@field public Is Inverse boolean
---@field public Selected Type number
---@field public Event_UI_Style fun(self:self,IsInverse:boolean,SelectedType:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetInverse fun(self:self,IsInverse:boolean):void
---@field public Set Received fun(self:self,SelectedType:number):void


---@class WBP_ComSliderView : WBP_ComSlider_C
---@field public WidgetRoot WBP_ComSlider_C
---@field public Slider_Delete UC7Button
---@field public CountText UTextBlock
---@field public btn_Input UC7Button
---@field public ProgressBar UProgressBar
---@field public Slider USlider
---@field public Slider_Add UC7Button
---@field public Slider_Top UC7Button
---@field public Ani_ArrowClickAdd UWidgetAnimation
---@field public Ani_ArrowClickDelete UWidgetAnimation
---@field public Ani_ButtonClickMax UWidgetAnimation
---@field public Pure FMargin
---@field public Default FMargin
---@field public InputType E_ComInputType
---@field public Is Vertical boolean
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Pure Num fun(self:self,Pure Num:boolean):void
---@field public Set Slider fun(self:self,Has Slider:boolean):void
---@field public Set Btn fun(self:self,Has Btn:boolean):void
---@field public Set Max fun(self:self,Has Max:boolean):void
---@field public Set Num fun(self:self,Has Num:boolean):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public Btn_Com UC7Button
---@field public Text_Com UTextBlock
---@field public Text_Time UTextBlock
---@field public Image UC7Image
---@field public Ani_Press UWidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void


---@class WBP_ExchangeSellDetailsView : WBP_ExchangeSellDetails_C
---@field public WidgetRoot WBP_ExchangeSellDetails_C
---@field public WBP_ItemReward WBP_ItemRewardView
---@field public WBP_BuyNum WBP_ComSliderView
---@field public WBP_BuyPrice WBP_ComSliderView
---@field public Btn_Cancel WBP_ComBtnView
---@field public Btn_Yes WBP_ComBtnView

---@class P_OnOffShelveDetailTipsView : WBP_ExchangeSellDetailsView
---@field public controller P_OnOffShelveDetailTips
local P_OnOffShelveDetailTipsView = DefineClass("P_OnOffShelveDetailTipsView", UIView)

function P_OnOffShelveDetailTipsView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_Yes.Btn_Com, "OnClick_Btn_Yes_Btn_Com")
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_Cancel.Btn_Com, "OnClick_Btn_Cancel_Btn_Com")
end

function P_OnOffShelveDetailTipsView:OnDestroy()
    local controller = self.controller
end

return P_OnOffShelveDetailTipsView
