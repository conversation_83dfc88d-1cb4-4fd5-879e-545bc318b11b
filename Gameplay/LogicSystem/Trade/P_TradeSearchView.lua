---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button UC7Button
---@field public Ani_Fadein UWidgetAnimation
---@field public Ani_Press UWidgetAnimation
---@field public IconBrush FSlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):FSlateBrush


---@class WBP_ComTitleView : WBP_ComTitle_C
---@field public WidgetRoot WBP_ComTitle_C
---@field public Text_Title UTextBlock
---@field public Image_Line UImage
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView
---@field public Ani_Fadein UWidgetAnimation
---@field public TitleText string
---@field public OnClicked MulticastDelegate
---@field public BndEvt__WBP_ComTitle_WBP_ComBtnClose_lua_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature fun(self:self):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetText fun(self:self,InText:string):void
---@field public InnerSetText fun(self:self):void


---@class WBP_ComPopupMView : WBP_ComPopupM_C
---@field public WidgetRoot WBP_ComPopupM_C
---@field public WBP_ComPopupTitle WBP_ComTitleView
---@field public Ani_In UWidgetAnimation
---@field public Ani_Out UWidgetAnimation
---@field public Title string
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComInputView : WBP_ComInput_C
---@field public WidgetRoot WBP_ComInput_C
---@field public icon_search UImage
---@field public EditText UC7EditableTextBox
---@field public Btn UC7Button
---@field public Img_Cancel UImage
---@field public Button_search UC7Button
---@field public Ani_Press UWidgetAnimation
---@field public Ani_Hover UWidgetAnimation
---@field public HintText string
---@field public Is Light boolean
---@field public Has Search boolean
---@field public Has SearchTips boolean
---@field public Bg Dark FSlateBrush
---@field public Bg Light FSlateBrush
---@field public Icon Dark FSlateColor
---@field public Icon Light FSlateColor
---@field public Cancel Dark FSlateColor
---@field public Cancel LIght FSlateColor
---@field public Style Dark FEditableTextStyle
---@field public Style Light FEditableTextStyle
---@field public LightStyle FEditableTextBoxStyle
---@field public DarkStyle FEditableTextBoxStyle
---@field public IsNumberOnly boolean
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetLight fun(self:self):void
---@field public SetBtn fun(self:self):void
---@field public SetHint fun(self:self):void
---@field public SetEnable fun(self:self):void


---@class WBP_TradeEmptyView : WBP_TradeEmpty_C
---@field public WidgetRoot WBP_TradeEmpty_C
---@field public Text_Empty UTextBlock


---@class WBP_TradeSearchPopupView : WBP_TradeSearchPopup_C
---@field public WidgetRoot WBP_TradeSearchPopup_C
---@field public WBP_ComPopupM WBP_ComPopupMView
---@field public WBP_ComInput WBP_ComInputView
---@field public Btn_Clear UC7Button
---@field public List_HistoryTag UTileViewEx
---@field public List_Result UTileViewEx
---@field public WBP_TradeEmpty WBP_TradeEmptyView


---@class P_TradeSearchView : WBP_TradeSearchPopupView
---@field public controller P_TradeSearch
local P_TradeSearchView = DefineClass("P_TradeSearchView", UIView)

function P_TradeSearchView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_Clear, "OnClick_Btn_Clear")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPopupM.WBP_ComPopupTitle.WBP_ComBtnClose.Button, "OnClick_WBP_ComPopupM_WBP_ComPopupTitle_WBP_ComBtnClose_Button")

end

function P_TradeSearchView:OnDestroy()
    local controller = self.controller
end

return P_TradeSearchView
