---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button UC7Button
---@field public Ani_Fadein UWidgetAnimation
---@field public Ani_Press UWidgetAnimation
---@field public IconBrush FSlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):FSlateBrush


---@class WBP_ComTitleView : WBP_ComTitle_C
---@field public WidgetRoot WBP_ComTitle_C
---@field public Text_Title UTextBlock
---@field public Image_Line UImage
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView
---@field public Ani_In UWidgetAnimation
---@field public TitleText string
---@field public OnClicked MulticastDelegate
---@field public BndEvt__WBP_ComTitle_WBP_ComBtnClose_lua_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature fun(self:self):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetText fun(self:self,InText:string):void
---@field public InnerSetText fun(self:self):void


---@class WBP_ComPopupLView : WBP_ComPopupL_C
---@field public WidgetRoot WBP_ComPopupL_C
---@field public WBP_ComPopupTitle WBP_ComTitleView
---@field public Anim_in UWidgetAnimation
---@field public Anim_Out UWidgetAnimation
---@field public Title string
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComTabSlcView : WBP_ComTabSlc_C
---@field public WidgetRoot WBP_ComTabSlc_C
---@field public WidgetSwitcher UWidgetSwitcher
---@field public Bg_Lock UImage
---@field public Text_Name UTextBlock
---@field public Button UC7Button
---@field public TextContent string
---@field public SetSwitcher number
---@field public In Font Info FSlateFontInfo
---@field public Type E_ComSlcTabType
---@field public SlcPreview number
---@field public Event_Tab_Style fun(self:self,SetSwitcher:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetContent fun(self:self,Tab Content:string):void
---@field public SetSlc fun(self:self,Set Switcher:number):void
---@field public SetType fun(self:self,Type Row:FST_ComSlcTab,Set Switcher:number):void
---@field public SetData fun(self:self,Type:E_ComSlcTabType):FST_ComSlcTab


---@class WBP_ComTabHorView : WBP_ComTabHor_C
---@field public WidgetRoot WBP_ComTabHor_C
---@field public HB_Btn UHorizontalBox
---@field public HorType E_ComSlcTabType
---@field public Type 1 FMargin
---@field public Type 2 FMargin
---@field public Type 3 FMargin
---@field public Is Binary boolean
---@field public Text1 string
---@field public Text2 string
---@field public Text3 string
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Hor fun(self:self,Type:E_ComSlcTabType):void
---@field public SetText fun(self:self):void


---@class WBP_ExchangeHistoryPopupView : WBP_ExchangeHistoryPopup_C
---@field public WidgetRoot WBP_ExchangeHistoryPopup_C
---@field public WBP_ComPopupL WBP_ComPopupLView
---@field public WBP_ComTabHor WBP_ComTabHorView
---@field public List_History UListViewEx
---@field public Type number
---@field public Index number


---@class P_TradeHistoryView : WBP_ExchangeHistoryPopupView
---@field public controller P_TradeHistory
local P_TradeHistoryView = DefineClass("P_TradeHistoryView", UIView)

function P_TradeHistoryView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPopupL.WBP_ComPopupTitle.WBP_ComBtnClose.Button, "OnClick_WBP_ComPopupL_WBP_ComPopupTitle_WBP_ComBtnClose_Button")

end

function P_TradeHistoryView:OnDestroy()
    local controller = self.controller
---DeletePlaceHolder
end

return P_TradeHistoryView
