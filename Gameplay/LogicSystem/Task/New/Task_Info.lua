local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local TaskTag = kg_require("Gameplay.LogicSystem.Task.TaskTag")
local Task_Target_Item = kg_require("Gameplay.LogicSystem.Task.New.Task_Target_Item")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local Const = kg_require("Shared.Const")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")
---@class Task_Info : UIComponent
---@field view Task_InfoBlueprint
local Task_Info = DefineClass("Task_Info", UIComponent)

Task_Info.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Task_Info:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Task_Info:InitUIData()
	self.targetInfos = {}		-- 任务目标
	self.selectRingID = nil
	self.BtnType = nil 			-- btn type
end

--- UI组件初始化，此处为自动生成
function Task_Info:InitUIComponent()
    ---@type UISimpleList
    self.Vert_SubTargetCom = self:CreateComponent(self.view.Vert_SubTarget, UISimpleList)
    ---@type TaskTag
    self.TaskTag3Com = self:CreateComponent(self.view.TaskTag3, TaskTag)
    ---@type TaskTag
    self.TaskTag2Com = self:CreateComponent(self.view.TaskTag2, TaskTag)
    ---@type TaskTag
    self.TaskTag1Com = self:CreateComponent(self.view.TaskTag1, TaskTag)
    ---@type UIComButton
    self.Btn_TraceCom = self:CreateComponent(self.view.Btn_Trace, UIComButton)
    ---@type UIComButtonItem
    self.Btn_AbandonCom = self:CreateComponent(self.view.Btn_Abandon, UIComButtonItem)
    ---@type UIListView
    self.ComLL_ItemCom = self:CreateComponent(self.view.ComLL_Item, UIListView)
    ---@type Task_Target_Item
    self.WBP_TaskTargetItemCom = self:CreateComponent(self.view.WBP_TaskTargetItem, Task_Target_Item)
end

---UI事件在这里注册，此处为自动生成
function Task_Info:InitUIEvent()
    self:AddUIEvent(self.Btn_AbandonCom.onClickEvent, "on_Btn_AbandonCom_ClickEvent")
    self:AddUIEvent(self.Btn_TraceCom.onClickEvent, "on_Btn_TraceCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Task_Info:InitUIView()
	self.Btn_AbandonCom:SetName(StringConst.Get("GIVE_UP_TASK"))
end

---组件刷新统一入口
function Task_Info:Refresh(...)
end

function Task_Info:RefreshInfo(selectRingID, questID)
	self.selectRingID = selectRingID
	local questCfg = Game.QuestSystem:GetTaskExcelCfg(questID)
	-- title
	self:refreshTitle(questCfg)
	-- tag
	self:refreshTagInfo(questCfg)
	-- 滚动区域
	self:refreshQuestInfo(questCfg)
	self:refreshTargetInfo(questCfg)
	-- Reward
	self:refreshRewardInfo(questCfg)
	self:refreshBtnInfo()
end

function Task_Info:refreshTitle(questCfg)
	local contentRoot = self.view
	contentRoot.Text_RingName:SetText(questCfg.RingName or "")
	local chapterName = Game.QuestSystem:GetChapterExcelCfg(questCfg.ChapterID).ChapterName
	if (chapterName or "") == (questCfg.RingName or "") then
		contentRoot.Text_ChapterName:SetVisibility(ESlateVisibility.Collapsed)
	else
		contentRoot.Text_ChapterName:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		contentRoot.Text_ChapterName:SetText(chapterName or "")
	end
end

function Task_Info:refreshTagInfo(questCfg)
	local contentRoot = self.view
	local ringCfg = Game.QuestSystem:GetRingExcelCfg(self.selectRingID)
	local miniData = Game.TableData.GetTaskMiniTypeDataRow(questCfg.QuestType)
	if miniData then
		contentRoot.TaskTag1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if questCfg.QuestType >= 0 and questCfg.QuestType <= 2 then -- todo 配置
			contentRoot.TaskTag1:SetTagType(questCfg.QuestType)
		elseif questCfg.QuestType == 4 then
			contentRoot.TaskTag1:SetTagType(3)
		else
			contentRoot.TaskTag1:SetTagType(4)
		end
		if ringCfg.MainTag and ringCfg.MainTag ~= "" then
			contentRoot.TaskTag1.Text_Tag_lua:SetText(ringCfg.MainTag)
		else
			contentRoot.TaskTag1.Text_Tag_lua:SetText(miniData.TitleText)
		end
	else
		contentRoot.TaskTag1:SetVisibility(ESlateVisibility.Collapsed)
	end
	if ringCfg.SubTagFirst and ringCfg.SubTagFirst ~= "" then
		contentRoot.TaskTag2:SetTagType(4)
		contentRoot.TaskTag2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		contentRoot.TaskTag2.Text_Tag_lua:SetText(ringCfg.SubTagFirst)
	else
		contentRoot.TaskTag2:SetVisibility(ESlateVisibility.Collapsed)
	end
	if ringCfg.SubTagSecond and ringCfg.SubTagSecond ~= "" then
		contentRoot.TaskTag3:SetTagType(4)
		contentRoot.TaskTag3:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		contentRoot.TaskTag3.Text_Tag_lua:SetText(ringCfg.SubTagSecond)
	else
		contentRoot.TaskTag3:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function Task_Info:refreshQuestInfo(questCfg)
	local contentRoot = self.view
	local ringID = questCfg.RingID
	local ringCfg = Game.QuestSystem:GetRingExcelCfg(ringID)
	local taskRingRewards = Game.TableData.GetTaskChapterRewardDataRow(ringID)

	-- 任务描述
	contentRoot.Text_TaskDesc1:SetText(ringCfg.RingDescription)
	contentRoot.Text_TaskDesc1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	if ringCfg.RingExplain and ringCfg.RingExplain ~= "" then
		contentRoot.Text_RingDesc:SetText(ringCfg.RingExplain)
		contentRoot.Text_RingDesc:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		contentRoot.Text_RingDesc:SetVisibility(ESlateVisibility.Collapsed)
	end

	-- 倒计时
	if not Game.QuestSystem:GetOpen(ringID) and taskRingRewards and taskRingRewards.EndTimeCondition and #taskRingRewards.EndTimeCondition > 0 then
		local endCond = taskRingRewards.EndTimeCondition
		contentRoot.Hori_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if endCond[3] then
			local d,h = Game.QuestSystem:GetTimeByTimestamp(endCond[3])
			contentRoot.Text_TaskTime:SetText(string.format(StringConst.Get("TASK_REMAIN_TIME"),d,h))
		elseif endCond[4] then
			local d,h = Game.QuestSystem:GetTimeByTimestamp(endCond[4])
			contentRoot.Text_TaskTime:SetText(string.format(StringConst.Get("TASK_REMAIN_TIME"),d,h))
		end
	else
		contentRoot.Hori_Time:SetVisibility(ESlateVisibility.Collapsed)
	end

	-- 未开启
	contentRoot.Text_TaskDesc2:SetVisibility(ESlateVisibility.Collapsed)
	if Game.QuestSystem:GetOpen(ringID) and taskRingRewards then
		local cond = taskRingRewards.OpenTimeCondition or {}
		if cond[5] then
			contentRoot.Text_TaskDesc2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			contentRoot.Text_TaskDesc2:SetText(StringConst.Get("TASK_TO_BE_CONTINUE"))
		elseif cond[3] then
			local d, h = Game.QuestSystem:GetTimeByTimestamp(cond[3])
			contentRoot.Text_TaskDesc2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			contentRoot.Text_TaskDesc2:SetText(string.format(StringConst.Get("TASK_UPCOMING_DH_DESC"), d,h))
		elseif cond[4] then
			local d, _ = Game.QuestSystem:GetTimeByTimestamp(cond[4])
			contentRoot.Text_TaskDesc2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			contentRoot.Text_TaskDesc2:SetText(string.format(StringConst.Get("TASK_UPCOMING_DAYS_DESC"), d))
		end
	end
end

function Task_Info:refreshTargetInfo(questCfg)
	local widgetRoot = self.view
	local QuestSystem = Game.QuestSystem

	local index = QuestSystem:GeCurMainTargetIndex(questCfg.QuestID)
	local tips = questCfg.MainTargetTraceCombo[index].MainTargetTips
	local bIsMainDescVisible = questCfg.MainTargetTraceCombo[index].bIsDescVisible
	if tips and tips ~= "" and bIsMainDescVisible then
		widgetRoot.RichText_Tips:SetVisibility(ESlateVisibility.Visible)
		widgetRoot.RichText_Tips:SetText(tips)
	else
		widgetRoot.RichText_Tips:SetVisibility(ESlateVisibility.Collapsed)
	end

	if not QuestSystem:IsRingAccepted(self.selectRingID) then
		widgetRoot.Text_Target:SetVisibility(ESlateVisibility.Collapsed)
		widgetRoot.WBP_TaskTargetItem:SetVisibility(ESlateVisibility.Collapsed)
		widgetRoot.Vert_SubTarget:SetVisibility(ESlateVisibility.Collapsed)
		widgetRoot.Text_Reward:SetVisibility(ESlateVisibility.Collapsed)
		widgetRoot.Img_LineH02_1:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	widgetRoot.Text_Target:SetVisibility(ESlateVisibility.Visible)
	widgetRoot.Vert_SubTarget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	widgetRoot.WBP_TaskTargetItem:SetVisibility(ESlateVisibility.Collapsed)
	--TargetList
	local bMainTargetStr = false
	table.clear(self.targetInfos)
	for targetIndex = 1, #questCfg.QuestTargets, 1 do
		local desc = QuestSystem:GetConditionDesc(questCfg.QuestID, targetIndex)
		if desc and desc ~= '' then
			local targetCfg = QuestSystem:GetConditionCfg(questCfg.ID, targetIndex)
			if QuestSystem:IsConditionCompleteByRingID(self.selectRingID, targetIndex) then
				desc = string.gsub(desc, '<Disable>([^/]+)</>', function(s)
					return s
				end)
			end
			if targetCfg.bIsMain then
				-- 主目标只有一个
				widgetRoot.WBP_TaskTargetItem.Text_TargetDesc:SetText(desc)
				if desc and desc ~= "" then
					bMainTargetStr = true
					widgetRoot.WBP_TaskTargetItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
				end
			else
				-- 副目标可能多个
				self.targetInfos[#self.targetInfos + 1] = desc
			end
		end
	end
	self.Vert_SubTargetCom:Refresh(self.targetInfos)
	if #self.targetInfos > 0 or bMainTargetStr then
		widgetRoot.Text_Reward:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		widgetRoot.Img_LineH02_1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		widgetRoot.Text_Reward:SetVisibility(ESlateVisibility.Collapsed)
		widgetRoot.Img_LineH02_1:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function Task_Info:refreshRewardInfo(questCfg)
	local contentRoot = self.view
	if questCfg and questCfg.Reward ~= 0 then
		contentRoot.Text_Reward:SetText(StringConst.Get("TASK_COMPLETE_TASK_GAIN"))
	else
		contentRoot.Text_Reward:SetText(StringConst.Get("TASK_COMPLETE_TASKS_GAIN"))
	end
	local taskRingRewards = Game.TableData.GetTaskChapterRewardDataRow(self.selectRingID)
	local bHideTitle = true
	if taskRingRewards and taskRingRewards.RewardDesc and taskRingRewards.RewardDesc ~= "" then
		contentRoot.Text_TaskDesc3:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		contentRoot.Text_TaskDesc3:SetText(tostring(taskRingRewards.RewardDesc))
		bHideTitle = false
	else
		contentRoot.Text_TaskDesc3:SetVisibility(ESlateVisibility.Collapsed)
	end
	table.clear(self.rewardData)
	self.rewardData = Game.QuestSystem:GetRewardByRingID(self.selectRingID, self.rewardData)
	if self.rewardData[1] and self.rewardData[1].IsMystery then
		contentRoot.WBP_TaskBox:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		contentRoot.ComLL_Item:SetVisibility(ESlateVisibility.Collapsed)
	else
		contentRoot.WBP_TaskBox:SetVisibility(ESlateVisibility.Collapsed)
		contentRoot.ComLL_Item:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.ComLL_ItemCom:Refresh(QuestUtils.TransformRewardDataToShow(self.rewardData))
	end

	if #self.rewardData <= 0 and bHideTitle then
		contentRoot.Vert_ItemTitle:SetVisibility(ESlateVisibility.Collapsed)
	else
		contentRoot.Vert_ItemTitle:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end

function Task_Info:refreshBtnInfo()
	local contentRoot = self.view
	-- Btn
	if Game.QuestSystem:GetCanAccept(self.selectRingID) then
		-- 可接取
		self.BtnType = "Accept"
		contentRoot.Btn_Trace.Text_Name:SetText(StringConst.Get("ACCEPT_TASK"))
		contentRoot.Btn_Trace.IsLight = true
	else
		-- 已接取
		local traceRingID = Game.QuestSystem:GetTracingRing()
		local secondTraceID = Game.QuestSystem:GetSecondTracingRing()
		if traceRingID == self.selectRingID or secondTraceID == self.selectRingID then
			-- 取消追踪
			self.BtnType = "Cancel"
			contentRoot.Btn_Trace.Text_Name:SetText(StringConst.Get("TASK_CANCEL_TRACE"))
			contentRoot.Btn_Trace.IsLight = false
		else
			-- 追踪任务
			self.BtnType = "Trace"
			contentRoot.Btn_Trace.Text_Name:SetText(StringConst.Get("TASK_TRACE_TASK"))
			contentRoot.Btn_Trace.IsLight = true
		end
	end
	--contentRoot.Btn_Trace:SetType()
	local questInfo = Game.QuestSystem:GetQuestInfo(self.selectRingID)
	local ringCfg = Game.QuestSystem:GetRingExcelCfg(self.selectRingID)
	if questInfo and (questInfo.RingStatus == Enum.ETaskConstData.TASK_STATUS__ACCEPTED) and ringCfg.bCanGiveUP then
		contentRoot.Btn_Abandon:SetVisibility(ESlateVisibility.Visible)
	else
		contentRoot.Btn_Abandon:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--- 此处为自动生成
function Task_Info:on_Btn_AbandonCom_ClickEvent()
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.QUEST_ABANDON_CONFIRM, function()
		if not self.selectRingID then
			return
		end
		if Game.me then
			Game.QuestSystem:ReqQuestAbandon(self.selectRingID)
		end
	end)

	Game.QuestSystem:CancelTracingRing(self.selectRingID)
end

--- 此处为自动生成
function Task_Info:on_Btn_TraceCom_ClickEvent()
	if not self.selectRingID then
		return
	end
	local questInfo = Game.QuestSystem:GetQuestInfo(self.selectRingID)
	if (not questInfo) or questInfo.RingStatus == Enum.ETaskConstData.TASK_STATUS__ABANDONED then
		if Game.QuestSystem:GetOpen(self.selectRingID) then
			if self.selectRingID ~= Game.QuestSystem:GetTracingRing()
				and self.selectRingID ~= Game.QuestSystem:GetSecondTracingRing() then
				-- 追踪任务
				Game.QuestSystem:SetTracingRing(self.selectRingID)
				self:closePanel()
			else
				-- 取消追踪
				Game.QuestSystem:CancelTracingRing(self.selectRingID)
			end
		else
			-- 接取任务  (NPC领取类型、道具领取类型需特殊处理)
			local ringCfg = Game.QuestSystem:GetRingExcelCfg(self.selectRingID)
			local applyType = ringCfg.ApplyType.Type
			if applyType == Enum.ETaskConstData.RECEIVE_TYPE__NPC_TALK then
				local npcID = ringCfg.ApplyType.NPCID
				local spawners = Game.WorldDataManager:GetInsIDsByNpcTemplateID(npcID)
				if spawners then
					for spawnerID,_ in ksbcpairs(spawners) do   -- luacheck: ignore
						Game.AutoNavigationSystem:RequestNavigateToNPC(Enum.EAutoNavigationRequesterType.Task, spawnerID, npcID)
						self:closePanel()
						return
					end
				else
					Log.WarningFormat("[Task_Info:on_Btn_TraceCom_ClickEvent] 任务 %d 的NPC %d 没有生成", self.selectRingID, npcID)
				end
				self:closePanel()
			elseif applyType == Enum.ETaskConstData.RECEIVE_TYPE__MANUAL then
				Game.QuestSystem:ReqReceiveQuest(self.selectRingID, Const.RECEIVE_TASK_TYPE.NULL)
			elseif applyType == Enum.ETaskConstData.RECEIVE_TYPE__ITEM then
				local itemID = ringCfg.ApplyActions.ItemID
				if Game.BagSystem:GetItemCount(itemID) > 0 then
					local result,invId = Game.BagSystem:GetAllItemInfosById(itemID)
					Game.BagSystem:ReqUse(itemID, invId, result[1].index, result[1].gbId, 1)
				else
					Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TASK_ITEM_NONE)
				end
			elseif applyType == Enum.ETaskConstData.RECEIVE_TYPE__QUEST_JUMP then
				Game.QuestSystem:ReqReceiveQuest(self.selectRingID, Const.RECEIVE_TASK_TYPE.NULL)
			end
		end
	else
		if self.selectRingID ~= Game.QuestSystem:GetTracingRing()
			and self.selectRingID ~= Game.QuestSystem:GetSecondTracingRing() then
			-- 追踪任务
			Game.QuestSystem:SetTracingRing(self.selectRingID)
			self:closePanel()
		else
			-- 取消追踪
			Game.QuestSystem:CancelTracingRing(self.selectRingID)
		end
	end
end

function Task_Info:closePanel()
	local panel = self:GetBelongPanel()
	if panel then
		panel:CloseSelf()
	end
end

return Task_Info
