local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Task_Target_Item : UIListItem
---@field view Task_Target_ItemBlueprint
local Task_Target_Item = DefineClass("Task_Target_Item", UIListItem)

Task_Target_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Task_Target_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Task_Target_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Task_Target_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Task_Target_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Task_Target_Item:InitUIView()
end

---面板打开的时候触发
function Task_Target_Item:OnRefresh(...)
end

return Task_Target_Item
