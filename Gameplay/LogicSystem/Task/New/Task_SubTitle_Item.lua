local UITreeItem = kg_require("Framework.KGFramework.KGUI.Component.UITreeView.UITreeItem")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")
---@class Task_SubTitle_Item : UIListItem
---@field view Task_SubTitle_ItemBlueprint
local Task_SubTitle_Item = DefineClass("Task_SubTitle_Item", UITreeItem)

Task_SubTitle_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Task_SubTitle_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Task_SubTitle_Item:InitUIData()
	self.state = nil
end

--- UI组件初始化，此处为自动生成
function Task_SubTitle_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Task_SubTitle_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Task_SubTitle_Item:InitUIView()
end

---面板打开的时候触发
function Task_SubTitle_Item:OnRefresh(params)
	self:setState(1)
	local questType = params.tabData
	Log.DebugFormat("Task_SubTitle_Item:OnRefresh")
	if questType == -2 then
		self.view.Text_Big:SetText("")
		self.view.Text:SetText(StringConst.Get("AVALIABLE_TASK"))
	elseif questType == -1 then
		self.view.Text_Big:SetText("")
		self.view.Text:SetText(StringConst.Get("GIVE_UP_TASK"))
	else
		local cfg = Game.TableData.GetTaskMiniTypeDataRow(questType)
		if not cfg then
			Log.ErrorFormat("TaskMiniType can't find ID:%s", questType)
		else
			local color, _ = Game.QuestSystem:GetTextColorByTaskType(questType)
			self.view.Text_Big:SetColorAndOpacity(color)
			local firstChar, remainingChars = QuestUtils.GetFirstAndRemainChars(cfg.TitleText)
			self.view.Text_Big:SetText(firstChar)
			self.view.Text:SetText(remainingChars)
		end
	end
end


--- 此处为自动生成
function Task_SubTitle_Item:on_Btn_ClickArea_Clicked()
	if self.state == 1 then
		self:setState(0)
	else
		self:setState(1)
	end
end

function Task_SubTitle_Item:setState(state)
	self.state = state
	self.userWidget:SetState(state)
end

return Task_SubTitle_Item
