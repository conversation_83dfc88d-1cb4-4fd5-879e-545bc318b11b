---@class TargetGuideModel:SystemModelBase @任务系统
local Const = kg_require("Shared.Const")

local TargetGuideModel = DefineClass("TargetGuideModel", SystemModelBase)

----------------------------Override----------------------------
function TargetGuideModel:init()
    ---@type number @轻量任务ID
    self.targetGuideID = nil
    ---@type number @轻量任务步骤ID
    self.stepID = nil
    ---@type number @轻量任务步骤完成数量
    self.count = nil
    ---@type number @npc对话ID监听标记
    self.npcTalkMark = nil
    ---@type number @npc对话监听标记
    self.npcMark = nil
    ---@type number @剧情对话监听标记
    self.dialogueMark = nil
    self.traceSystem = nil
end

function TargetGuideModel:unInit()
    self:clear()
end

function TargetGuideModel:clear()
    self.targetGuideID = nil
    self.traceSystem = nil
    self.stepID = nil
    self.count = nil
    self:clearListen()
    Game.GlobalEventSystem:Publish(EEventTypesV2.TARGETGUIDE_ON_FINISH)
end

function TargetGuideModel:clearListen()
    self.npcTalkMark = nil
    self.npcMark = nil
    self.dialogueMark = nil
    Game.TargetGuideSystem:RemoveAllListenPosition()
end
----------------------private----------------------
function TargetGuideModel:getPlaneAndMapID(spaceOrMapID, planeID)
    local planeCfg = Game.SceneUtils.GetPlaneConfProxy(spaceOrMapID)
    if planeCfg then
        return planeCfg.ID, spaceOrMapID
    else
        return spaceOrMapID, planeID
    end
end


function TargetGuideModel:getTraceParam(insID, curLevelID, curPlaneID, outParams)
    local worldDataManager = Game.WorldDataManager
    outParams = outParams or {}
    local mapIDs = worldDataManager:FindSceneActorBelongLevels(insID)
    local levelID, planeID
    local sceneActorData
    for mapOrPlaneID, _ in ksbcpairs(mapIDs) do
        levelID, planeID = self:getPlaneAndMapID(mapOrPlaneID, 0)
        if levelID == curLevelID and planeID == curPlaneID then
            sceneActorData = worldDataManager:RequireGetSceneActorData(mapOrPlaneID, insID)
            if sceneActorData then
                ---@type TraceParam
                local param = {}
                param.HideRadius = Game.TableData.GetConstDataRow("TASK_TRACE_HIDE_DISTANCE_DISPLAY_RANGE")
                param.PlaneID = planeID
                param.RemoveRadius = 0
				local mapID, Pos = Game.QuestSystem:getTraceParamByInsID(insID)
				param.MapID = mapID
				param.Pos = Pos
				param.TracingType = Const.TRACING_INFO_TYPE.DUNGEON_TASK
                outParams[#outParams+1] = param
            end
        end
    end
end

TargetGuideModel.TargetTraceFun = {
    KillMonster = function(self, tragetParam)
        --击杀怪物
        local currentLevelID = Game.LevelManager.GetCurrentLevelID()
        local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()
        local monsterCfgIDs = tragetParam.TemplateID
        local worldDataManager = Game.WorldDataManager
        local params = {}
        for monsterCfgID, _ in ksbcpairs(monsterCfgIDs) do
            local spawners = worldDataManager:GetInsIDsByNpcTemplateID(monsterCfgID)
            if spawners then
                for spawnerID, indexs in ksbcpairs(spawners) do
                    local levels = worldDataManager:FindSceneActorBelongLevels(spawnerID)
                    local worldID, tarPlaneID
                    if levels and ksbcnext(levels) then
                        for levelID, _ in ksbcpairs(levels) do
                            worldID, tarPlaneID = self:getPlaneAndMapID(levelID, 0)
                            if currentLevelID == worldID and tarPlaneID == currentPlaneID then
                                local param = {}
                                param.HideRadius = Game.TableData.GetConstDataRow("TASK_TRACE_HIDE_DISTANCE_DISPLAY_RANGE")
                                param.PlaneID = tarPlaneID
                                param.RemoveRadius = 0
                                param.MapID = worldID
                                -- param.TraceTargetSpawner = spawnerID
                                -- param.TraceTaskMonsterIDs = {monsterCfgID}
								param.TracingType = Const.TRACING_INFO_TYPE.DUNGEON_TASK
                                local npcInsID
                                local sceneActorData = worldDataManager:RequireGetSceneActorData(levelID, spawnerID)
                                local pos
                                --TODO:待优化
                                if sceneActorData then
                                    if sceneActorData.GroupMember and sceneActorData.GroupMember[indexs[1]] then
                                        local member = sceneActorData.GroupMember[indexs[1]]
                                        npcInsID = member.InstanceID
                                        pos = member.Position
                                    else
                                        npcInsID = spawnerID
                                        pos = sceneActorData.Transform.Position
                                    end
                                end
                                --TODO:临时记录位置
                                param.Pos = pos
                                params[#params+1] = param
                            end
                        end
                    end
                end
            end
        end
        return params
    end,
    Interact = function(self, tragetParam)
        --交互
        local currentLevelID = Game.LevelManager.GetCurrentLevelID()
        local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()
        local interactCfgIDs = tragetParam.TemplateID
        local params = {}
        for interactCfgID, _ in ksbcpairs(interactCfgIDs) do
            local insIDs = Game.WorldDataManager:GetInsIDsByCollectTemplateID(interactCfgID)
            if insIDs then
                for _, insID in ksbcpairs(insIDs) do
                    self:getTraceParam(insID, currentLevelID, currentPlaneID, params)
                end
            end
        end
        return params
    end,
    TalkToNpc = function(self, tragetParam)
        --与NPC对话
        local currentLevelID = Game.LevelManager.GetCurrentLevelID()
        local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()
        local npcCfgID = tragetParam.NpcID
        local worldDataManager = Game.WorldDataManager
        local spawners = worldDataManager:GetInsIDsByNpcTemplateID(npcCfgID)
        local params = {}
        if spawners then
            for spawnerID, indexs in ksbcpairs(spawners) do
                local levels = worldDataManager:FindSceneActorBelongLevels(spawnerID)
                local worldID, tarPlaneID
                if levels and ksbcnext(levels) then
                    for levelID, _ in ksbcpairs(levels) do
                        worldID, tarPlaneID = self:getPlaneAndMapID(levelID, 0)
                        if currentLevelID == worldID and tarPlaneID == currentPlaneID then
                            local param = {}
                            param.HideRadius = Game.TableData.GetConstDataRow("TASK_TRACE_HIDE_DISTANCE_DISPLAY_RANGE")
                            param.RemoveRadius = 0
                            param.MapID = worldID
                            --param.TraceTargetSpawner = spawnerID
                            --param.TraceTaskNPCIDs = {npcCfgID}
							param.TracingType = Const.TRACING_INFO_TYPE.DUNGEON_TASK
                            local npcInsID
                            local sceneActorData = worldDataManager:RequireGetSceneActorData(levelID, spawnerID)
                            local pos
                            --TODO:待优化
                            if sceneActorData then
                                if sceneActorData.GroupMember and sceneActorData.GroupMember[indexs[1]] then
                                    local member = sceneActorData.GroupMember[indexs[1]]
                                    npcInsID = member.InstanceID
                                    pos = member.Position
                                else
                                    npcInsID = spawnerID
                                    pos = sceneActorData.Transform.Position
                                end
                            end
                            --TODO:临时记录位置
                            param.Pos = pos
                            param.TraceTaskNpcInsIDs = {npcInsID}
                            params[#params+1] = param
                        end
                    end
                end
            end
        end
        return params
    end,
    Collect = function(self, tragetParam)
        --采集
        local currentLevelID = Game.LevelManager.GetCurrentLevelID()
        local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()
        local params = {}
        local collectIDs = tragetParam.TemplateID
        for collectID, _ in ksbcpairs(collectIDs) do
            local insIDs = Game.WorldDataManager:GetInsIDsByCollectTemplateID(collectID)
            if insIDs then
                for _, insID in ksbcpairs(insIDs) do
                    self:getTraceParam(insID, currentLevelID, currentPlaneID, params)
                end
            end
        end

        return params
    end,
    Position = function(self, tragetParam)
        --到达地点
        local currentLevelID = Game.LevelManager.GetCurrentLevelID()
        local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()
        local param = {}
        param.HideRadius = Game.TableData.GetConstDataRow("TASK_TRACE_HIDE_DISTANCE_DISPLAY_RANGE")
        param.PlaneID = currentPlaneID
        param.RemoveRadius = 0
        param.MapID = currentLevelID
		local pos = tragetParam.Position
        param.Pos = FVector(pos.X, pos.Y, pos.Z)
		param.TracingType = Const.TRACING_INFO_TYPE.DUNGEON_TASK
        return {param}
    end
}

function TargetGuideModel:buildTraceParam(targetType, targetParam)
    local fun = TargetGuideModel.TargetTraceFun[targetType]
    if fun then
        self.traceSystem = fun(self, targetParam)
    end
end
----------------------Common----------------------
---------------------------NetEvent---------------------------
---@param targetGuideID number @轻量任务ID
---@param stepID number @轻量任务步骤ID
---@param count number @步骤完成数量
function TargetGuideModel:OnMsgTargetGuideChange(targetGuideID, stepID, count)
    local bIsNew = self.targetGuideID == nil
    self.targetGuideID = targetGuideID
    self.stepID = stepID
    self.count = count
    self:clearListen()
    local targetGuideCfg = Game.TableData.GetTargetGuideInfoDataRow(targetGuideID)
    if targetGuideCfg then
        targetGuideCfg = targetGuideCfg[stepID]
    end

    if targetGuideCfg.TargetType == "TalkToNpc" then
        self.npcTalkMark = targetGuideCfg.Params.TalkID
        self.npcMark = targetGuideCfg.Params.NpcID
    elseif targetGuideCfg.TargetType == "Dialogue" then
        self.dialogueMark = targetGuideCfg.Params.DialogID
    elseif targetGuideCfg.TargetType == "Position" then
        Game.TargetGuideSystem:AddListenPosition(targetGuideID, stepID, targetGuideCfg.Params.Position, targetGuideCfg.Params.Radius)
    end
    
    if not targetGuideCfg.IsHide then
        self:buildTraceParam(targetGuideCfg.TargetType, targetGuideCfg.Params)
    else
        self.traceSystem = nil
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.TARGETGUIDE_ON_UPDATE)
    if bIsNew then
        if UI.IsShow("P_HUDBaseView") then
            Game.HUDSystem:ShowUI("P_HUDSimpleTask")
        end
    end
    Game.TraceSystem:RefreshTargetGuideTrace()
end

function TargetGuideModel:OnMsgTargetGuideFinish()
    self:clear()
    Game.TraceSystem:RefreshTargetGuideTrace()
end

function TargetGuideModel:RefreshTraceParam()
	if not self.targetGuideID then
		return
	end
	local targetGuideCfg = Game.TableData.GetTargetGuideInfoDataRow(self.targetGuideID)
	if targetGuideCfg then
		targetGuideCfg = targetGuideCfg[self.stepID]
	end
	if not targetGuideCfg.IsHide then
		self:buildTraceParam(targetGuideCfg.TargetType, targetGuideCfg.Params)
	else
		self.traceSystem = nil
	end
end

return TargetGuideModel