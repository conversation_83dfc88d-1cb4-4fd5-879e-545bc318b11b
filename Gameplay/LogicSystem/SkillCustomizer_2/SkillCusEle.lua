local SkillCusCoreBtn = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCusCoreBtn")
local SkillCusTalentBtn = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCusTalentBtn")
local SkillCustomResonateBtn = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomResonateBtn")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---技能管理 元素核心Banner
---@class SkillCusEle : UIComponent
---@field view SkillCusEleBlueprint
local SkillCusEle = DefineClass("SkillCusEle", UIComponent)

SkillCusEle.eventBindMap = {
	[EEventTypesV2.ON_ELEMENT_CORE_CHANGED] = "OnCoreChanged",
	[EEventTypesV2.ON_TALENT_TREE_NODE_UPGRADE] = "OnNodeUpgrade",
	[EEventTypesV2.ON_TALENT_TREE_MULTINODE_UPGRADE] = "OnNodeUpgrade",
	[EEventTypesV2.ON_TALENT_TREE_NODE_RESET] = "OnNodeUpgrade",
	[EEventTypesV2.RECEIVE_MONEY_CHANGE] = "OnNodeUpgrade",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCusEle:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCusEle:InitUIData()
	---两个元素天赋树的ID
	---@type table
	self.ElementTreeIDTable = {}
	---当前激活的核心Index
	---@type number 1|2
	self.CurrentActiveCoreIndex = 2
	---动画是否已经播放到了按钮位置完成更改，动画状态会把第二个核心按钮锁死在第一个的位置
	---@tyoe boolean
	self.bCoreExchanged = false
end

--- UI组件初始化，此处为自动生成
function SkillCusEle:InitUIComponent()
    ---@type SkillCusCoreBtn
    self.WBP_SkillCustomizerCoreBtn2_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerCoreBtn2_lua, SkillCusCoreBtn)
    ---@type SkillCusCoreBtn
    self.WBP_SkillCustomizerCoreBtn_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerCoreBtn_lua, SkillCusCoreBtn)
    ---@type SkillCusTalentBtn
    self.WBP_SkillCustomizerTalentBtn_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerTalentBtn_lua, SkillCusTalentBtn)
    ---@type SkillCustomResonateBtn
    self.WBP_SkillCustomizerResonateBtn_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerResonateBtn_lua, SkillCustomResonateBtn)
end

---UI事件在这里注册，此处为自动生成
function SkillCusEle:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Exchange_lua.OnClicked, "on_Btn_Exchange_lua_Clicked")
    self:AddUIEvent(self.view.WBP_ComBtnIconNew_lua.Big_Button_ClickArea_lua.OnClicked, "on_WBP_ComBtnIconNew_luaBig_Button_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCusEle:InitUIView()
	self.userWidget.OnCoreExchanged:Add(function() 
		self:OnExchangeFinished()
	end)
end

---组件刷新统一入口
function SkillCusEle:Refresh(...)
end

---初始化信息
function SkillCusEle:InitBannerInfo()
	--获取当前所持有的两个核心
	table.clear(self.ElementTreeIDTable)
	self:StopAnimation(self.userWidget.Ani_Exchange)
	self.ElementTreeIDTable = Game.ElementCoreSystem:GetCurrentProfessionElementType()
	--初始化的时候将激活的置于左侧
	--获取当前激活的核心
	local ActiveTreeID = Game.ElementCoreSystem:GetCurrentActiveTree()
	self.CurrentActiveCoreIndex = (ActiveTreeID == self.ElementTreeIDTable[1]) and 1 or 2

	if not self.bCoreExchanged then
		self.WBP_SkillCustomizerCoreBtn_luaCom:InitCoreContent(ActiveTreeID)
		self.WBP_SkillCustomizerCoreBtn2_luaCom:InitCoreContent(self.ElementTreeIDTable[self.CurrentActiveCoreIndex == 1 and 2 or 1])
	else
		self.WBP_SkillCustomizerCoreBtn2_luaCom:InitCoreContent(ActiveTreeID)
		self.WBP_SkillCustomizerCoreBtn_luaCom:InitCoreContent(self.ElementTreeIDTable[self.CurrentActiveCoreIndex == 1 and 2 or 1])
	end
	
	self.WBP_SkillCustomizerCoreBtn_luaCom:UpdateButtonState()
	self.WBP_SkillCustomizerCoreBtn2_luaCom:UpdateButtonState()
	
	--刷新天赋按钮
	self.WBP_SkillCustomizerTalentBtn_luaCom:RefreshBtnContent()
	
	--设置中心按钮内容
	self.WBP_SkillCustomizerResonateBtn_luaCom:SetContent()
end

---交换两个核心
function SkillCusEle:ExchangeCores()
	local ActiveTreeID = Game.ElementCoreSystem:GetCurrentActiveTree()
	local TargetTreeID = (ActiveTreeID == self.ElementTreeIDTable[1]) and self.ElementTreeIDTable[2] or self.ElementTreeIDTable[1]
	Game.ElementCoreSystem:ReqActiveEleTalentTree(TargetTreeID)
end

---事件回调---------------------------------------------------------------------------------------------------------------------------------------------

---切换页签时停止动画播放
function SkillCusEle:OnSwitchPage()
	self:StopAnimation(self.userWidget.Ani_Exchange)
end

---动画播放时已经事实上完成了位置交换
function SkillCusEle:OnExchangeFinished()
	self.bCoreExchanged = true
end

---Banner需要关注三个地方：显示的天赋点，当前激活的核心和共鸣等级
function SkillCusEle:OnNodeUpgrade()
	self.WBP_SkillCustomizerTalentBtn_luaCom:RefreshBtnContent()
end

function SkillCusEle:OnCoreChanged()
	self:PlayAnimation(self.userWidget.Ani_Exchange)
	--分别设置两个按钮的新状态
	local ActiveTreeID = Game.ElementCoreSystem:GetCurrentActiveTree()
	self.CurrentActiveCoreIndex = (ActiveTreeID == self.ElementTreeIDTable[1]) and 1 or 2
	self.WBP_SkillCustomizerCoreBtn2_luaCom:InitCoreContent(ActiveTreeID)
	self.WBP_SkillCustomizerCoreBtn_luaCom:InitCoreContent(self.ElementTreeIDTable[self.CurrentActiveCoreIndex == 1 and 2 or 1])
	self.WBP_SkillCustomizerTalentBtn_luaCom:RefreshBtnContent()
	self.WBP_SkillCustomizerCoreBtn_luaCom:UpdateButtonState()
	self.WBP_SkillCustomizerCoreBtn2_luaCom:UpdateButtonState()
end

function SkillCusEle:OnResonateLevelChanged()
	self.WBP_SkillCustomizerResonateBtn_luaCom:SetContent()
	--播放升级动效
	self.WBP_SkillCustomizerResonateBtn_luaCom:PlayUpgradeAnimation()
	--刷新灵光点
	self.WBP_SkillCustomizerTalentBtn_luaCom:RefreshBtnContent()
end

function SkillCusEle:OnClickTips()
	Game.TipsSystem:ShowTips(Enum.ETipsData.ELEMENT_MAIN_TIP)
end

--- 此处为自动生成
function SkillCusEle:on_Btn_Exchange_lua_Clicked()
	self:ExchangeCores()
end

--- 此处为自动生成
function SkillCusEle:on_WBP_ComBtnIconNew_luaBig_Button_ClickArea_lua_Clicked()
	self:OnClickTips()
end

return SkillCusEle
