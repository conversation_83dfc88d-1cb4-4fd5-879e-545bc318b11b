local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---技能管理 中心技能列表区域
---@class SkillCustomizerSkill : UIComponent
---@field view SkillCustomizerSkillBlueprint
local SkillCustomizerSkill = DefineClass("SkillCustomizerSkill", UIComponent)

---技能TreeList所需内容
local NewTreeList = kg_require("Framework.UI.List.NewList.NewTreeList")
local SkillCustomizerSkillTitle = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomizerListItems.SkillCustomizerSkillTitle")
local SkillCustomizerSkillItem = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomizerListItems.SkillCustomizerSkillItem")

local ESlateVisibility = import("ESlateVisibility")

SkillCustomizerSkill.eventBindMap = {
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCustomizerSkill:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCustomizerSkill:InitUIData()
	-- 2D页签技能列表
	---@type table
	self.SkillPanelListData = {}
end

--- UI组件初始化，此处为自动生成
function SkillCustomizerSkill:InitUIComponent()

end

---UI事件在这里注册，此处为自动生成
function SkillCustomizerSkill:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCustomizerSkill:InitUIView()
	--创建技能列表TreeList
	self.SkillPanelList = self:CreateComponent(self.view.SB_SkillBox_lua, NewTreeList, {{SkillCustomizerSkillTitle}, {SkillCustomizerSkillItem}})
end

---组件刷新统一入口
function SkillCustomizerSkill:Refresh()
	
end

------------------------------------------------------------------------------------------------------------------------------------------------------
--region 初始化

---设置当前页的技能
---@param TabData table
function SkillCustomizerSkill:InitSkillPage(TabData)
	self.view.WBP_SkillCusEle_lua:SetVisibility(ESlateVisibility.Collapsed)

	--如果需要则刷新信息
	--刷新技能
	table.clear(self.SkillPanelListData)
	self.SkillPanelListData = Game.SkillCustomSystem:GetSkillIDBySkillTab(TabData.Key, true, true);
	self.SkillPanelList:SetData(self.SkillPanelListData, false, nil, 1)
end
--endregion 初始化
------------------------------------------------------------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------------------------------------------------------------
--region 功能

---常规刷新 - 选择第一个技能
function SkillCustomizerSkill:SelectFirstSkill()
	--如果此时选择失败，获取选定技能ID的相关接口会返回-1
	if self.SkillPanelListData[1] and self.SkillPanelListData[1].Children then
		if self.SkillPanelListData[1].Children[1] then
			self.SkillPanelList:Sel(1, 1)
		end
	end
end

---特殊刷新 - 直接根据ID选定特定的技能
---@param SKillID number 想要刷新的技能ID
function SkillCustomizerSkill:SelectSkillByID(SkillID)
	local Index1, Index2 = self:GetSkillIndexByID(SkillID)
	if Index1 == -1 then
		return
	end
	self.SkillPanelList:Sel(Index1, Index2)
end

---特殊刷新 - 根据索引选择技能
---@param Index1 number
---@param Index2 number
function SkillCustomizerSkill:SelectSkillByIndex(Index1, Index2)
	if not Index1 or Index2 == -1 then
		return
	end
	self.SkillPanelList:Sel(Index1, Index2)
end

---特殊刷新 - 定位特定技能在技能列表内的位置
---@return number 技能所在位置的索引
function SkillCustomizerSkill:GetSkillIndexByID(SkillID)
	local TabData = Game.TableData.GetSkillTabDataRow(self.parentComponent and self.parentComponent.CurrentTab or 1)
	if not TabData then
		return
	end
	--根据技能数据结构返回
	for Index1, SubtitleInfo in pairs(self.SkillPanelListData) do
		for Index2, SkillInfo in pairs(SubtitleInfo.Children) do
			if SkillID == SkillInfo.SkillID then
				return Index1, Index2
			end
		end
	end
	return -1, nil
end

---获取当前选择的技能的RoleSkillID和SkillID
---@return number, number
function SkillCustomizerSkill:GetCurrentSelectedSkill()
	local index1, index2 = self.SkillPanelList:GetSelectedIndex()
	if not index1 or not index2 then return -1 end
	return self.SkillPanelListData[index1].Children[index2].RoleSkillID, self.SkillPanelListData[index1].Children[index2].SkillID
end

---获取当前选择的技能的RoleSkillID
---@return number
function SkillCustomizerSkill:GetCurrentSelectedRoleSkillID()
	local index1, index2 = self.SkillPanelList:GetSelectedIndex()
	if not index1 or not index2 then return -1 end
	return self.SkillPanelListData[index1].Children[index2].RoleSkillID
end

---获取当前选择的技能的SkillID
---@return number
function SkillCustomizerSkill:GetCurrentSelectedSkillID()
	local index1, index2 = self.SkillPanelList:GetSelectedIndex()
	if not index1 or not index2 then return -1 end
	return self.SkillPanelListData[index1].Children[index2].SkillID
end

---取消选择
function SkillCustomizerSkill:ClearSelection()
	self.SkillPanelList:CancelAllSel()
end

---获取特定节点的引用
---@param SkillID number
---@return SkillCustomizerSkillItem
function SkillCustomizerSkill:GetSkillItem(SkillID)
	local Index1, Index2 = self:GetSkillIndexByID(SkillID)
	if Index1 and Index2 then
		return self.SkillPanelList:GetRendererAt(self.SkillPanelList:getIndex(Index1, Index2))
	end
	return nil
end

--endregion 功能
------------------------------------------------------------------------------------------------------------------------------------------------------
--region 回调

---列表技能点击事件
function SkillCustomizerSkill:OnSkillItemSelected()
	if self.parentComponent and self.parentComponent.OnSkillItemSelected then
		self.parentComponent:OnSkillItemSelected()
	end
end

--endregion 回调

return SkillCustomizerSkill
