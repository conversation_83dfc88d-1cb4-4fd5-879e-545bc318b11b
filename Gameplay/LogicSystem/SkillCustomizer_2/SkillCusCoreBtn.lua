local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---技能管理 元素核心Banner切换核心小按钮
---@class SkillCusCoreBtn : UIComponent
---@field view SkillCusCoreBtnBlueprint
local SkillCusCoreBtn = DefineClass("SkillCusCoreBtn", UIComponent)

SkillCusCoreBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCusCoreBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCusCoreBtn:InitUIData()
	---核心ID
	---@type number
	self.CoreID = -1
	---对应天赋树ID
	---@type number
	self.TalentTreeID = -1
end

--- UI组件初始化，此处为自动生成
function SkillCusCoreBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SkillCusCoreBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCusCoreBtn:InitUIView()
end

---组件刷新统一入口
function SkillCusCoreBtn:Refresh(...)
end

------------------------------------------------------------------------------------------------------------------------------------------------------
---初始化内容
---@param ID number 天赋树的ID
function SkillCusCoreBtn:InitCoreContent(ID)
	self.TalentTreeID = ID
	local TalentTreeData = Game.TableData.GetEleTalentTreeDataRow(self.TalentTreeID)
	if TalentTreeData then
		self.CoreID = tonumber(TalentTreeData.ElementType)
	end
	local CoreData = Game.TableData.GetElementTypeDefDataRow(self.CoreID)
	if not CoreData then
		return
	end
	self.view.KText_Title_lua:SetText(CoreData.CoreTypeText)
	self.view.KText_WordL_lua:SetText("")
	
	--图标
	self:SetImage(self.view.Img_Icon_lua, CoreData.SefirotCoreImg)
end

---初始化内容 - 通过核心ID设置
---@param ID number 核心的ID
function SkillCusCoreBtn:InitCoreContentByCoreID(ID)
	self.CoreID = ID
	local CoreData = Game.TableData.GetElementTypeDefDataRow(self.CoreID)
	if not CoreData then
		return
	end
	self.view.KText_Title_lua:SetText(CoreData.CoreTypeText)
	self.view.KText_WordL_lua:SetText("")

	--图标
	self:SetImage(self.view.Img_Icon_lua, CoreData.SefirotCoreImg)
end

---更新状态
function SkillCusCoreBtn:UpdateButtonState()

end

--- 此处为自动生成
function SkillCusCoreBtn:on_Btn_ClickArea_lua_Clicked()
end

return SkillCusCoreBtn
