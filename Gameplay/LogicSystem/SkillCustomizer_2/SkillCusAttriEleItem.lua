local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---技能进阶 - 节点信息
---@class SkillCusAttriEleItem : UIListItem
---@field view SkillCusAttriEleItemBlueprint
local SkillCusAttriEleItem = DefineClass("SkillCusAttriEleItem", UIListItem)

SkillCusAttriEleItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCusAttriEleItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCusAttriEleItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function SkillCusAttriEleItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SkillCusAttriEleItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCusAttriEleItem:InitUIView()
end

---面板打开的时候触发
function SkillCusAttriEleItem:OnRefresh(Data)
	local SkillData = Game.TableData.GetSkillDataNewRow(Data[1])
	if not SkillData then
		Log.WarningFormat("Skill Advance Node contains invalid SkillID: %s", Data[1])
		return
	end
	self.view.Text_Name_lua:SetText(SkillData.Name)
	self.view.RTB_Description_lua:SetText(Game.SkillCustomSystem:GenerateSkillBriefDesc(Data[1]))
	self.userWidget:Event_UI_Style(Data[2])
end

return SkillCusAttriEleItem
