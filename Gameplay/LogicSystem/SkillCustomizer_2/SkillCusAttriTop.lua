local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---技能管理 右侧半屏顶部信息
---@class SkillCusAttriTop : UIComponent
---@field view SkillCusAttriTopBlueprint
local SkillCusAttriTop = DefineClass("SkillCusAttriTop", UIComponent)

local ESlateVisibility = import("ESlateVisibility")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")

local StringConst = require "Data.Config.StringConst.StringConst"

SkillCusAttriTop.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCusAttriTop:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCusAttriTop:InitUIData()
	---标签数据
	---@type table
	self.TagListData = {}
	---是否是PC样式
	---@type boolean
	self.bPCMode = true
	-- 根据平台和设置确定轮盘样式
	if import("C7FunctionLibrary").IsC7Editor() then
		if import("UIFunctionLibrary").GetPreviewPlatform and import("UIFunctionLibrary").GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC then
			self.bPCMode = true
		else
			self.bPCMode = false
		end
	else
		if PlatformUtil.IsMobilePlatform() then
			self.bPCMode = false
		else
			self.bPCMode = true
		end
	end
end

--- UI组件初始化，此处为自动生成
function SkillCusAttriTop:InitUIComponent()
	---@type UIListView
    self.KGListView_Tip_luaCom = self:CreateComponent(self.view.KGListView_Tip_lua, UIListView)
end

---UI事件在这里注册，此处为自动生成
function SkillCusAttriTop:InitUIEvent()
    self:AddUIEvent(self.view.AssembleBtn_lua.Btn_ClickArea_lua.OnClicked, "on_AssembleBtn_luaBtn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCusAttriTop:InitUIView()
	--默认隐藏技能图标左侧的旧版进阶
	self.view.Icon_lua:SetStage(0)
end

---组件刷新统一入口
function SkillCusAttriTop:Refresh(...)
end

------------------------------------------------------------------------------------------------------------------------------------------------------
---设置技能信息
---@param RoleSkillID number
---@param SkillID number
function SkillCusAttriTop:SetSkillInfo(RoleSkillID, SkillID)
	local RoleSkillData = Game.TableData.GetRoleSkillUnlockDataRow(RoleSkillID)
	local SkillData = Game.TableData.GetSkillDataNewRow(SkillID)

	if not RoleSkillData or not ksbcnext(RoleSkillData) or not SkillData then
		Log.WarningFormat("SkillCustomizer SkillCusAttriTop: Unable to retrieve information from RoleSkillID: %s and SkillID: %s", 
			RoleSkillID, SkillID)
		return
	end

	--等级
	if not Game.SkillCustomSystem:CanSkillLevelUp(RoleSkillID) then
		self.view.Text_Lvl_lua:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Text_Lvl_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		local Level, ExtraLvl = Game.SkillCustomSystem:GetSkillLevel(SkillID)
		self.view.Text_Lvl_lua:SetText(string.format(StringConst.Get("SKILL_TIP_ADDITIONAL_LV"), Level + ExtraLvl))
	end
	
	--名称
	self.view.Text_Name_lua:SetText(SkillData.Name);
	
	--进阶
	if Game.SkillCustomSystem:IsSkillAdvanceSupported(RoleSkillID) then
		self.view.WBP_SkillCustomizerTips_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.WBP_SkillCustomizerTips_lua.Text_LvNum_lua:SetText(
			Game.SkillCustomSystem.SkillUpgradeLevelText[Game.SkillCustomSystem:GetSkillAdvanceLevel(RoleSkillID)]
		)
		self.view.WBP_SkillCustomizerTips_lua:Event_UI_Style(2)
	else
		self.view.WBP_SkillCustomizerTips_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
	
	local Level, _ = Game.SkillCustomSystem:GetSkillLevel(SkillID)
	--更新Tag
	table.clear(self.TagListData)
	local CoolDownTime = Game.SkillCustomSystem:GetSkillCoolDownTime(SkillID, Level)
	if CoolDownTime and CoolDownTime > 0 then
		table.insert(self.TagListData, { Time = CoolDownTime })
	end
	if SkillData.DesTags then
		for key, value in ksbcpairs(SkillData.DesTags) do
			table.insert(self.TagListData, { Key = value })
		end
	end
	self.KGListView_Tip_luaCom:Refresh(self.TagListData)
	--设置图标
	self:SetSkillIcon(SkillID)

	--是否可以装配
	local bCanAssemble = Game.SkillCustomSystem:IsSkillEquipAllow(RoleSkillData.SkillType)
		and not Game.SkillCustomSystem:IsSkillLocked(SkillID)
	self.view.AssembleBtn_lua:SetVisibility(bCanAssemble and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Hidden)
end

---设置技能信息：图标区域
function SkillCusAttriTop:SetSkillIcon(SkillID)
	local SkillData = Game.TableData.GetSkillDataNewRow(SkillID)
	--设置图片并且隐藏无用内容
	--检查技能是否锁定
	local bLocked = Game.SkillCustomSystem:IsSkillLocked(SkillID)
	if bLocked then
		self.view.Icon_lua:Event_UI_Style(Game.SkillCustomSystem.ESkillStateEnum.Locked, false, false)
	else
		self.view.Icon_lua:Event_UI_Style(Game.SkillCustomSystem.ESkillStateEnum.Normal, false, false)
	end
	
	self.view.Icon_lua.Text_NamType_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Icon_lua.Text_Name_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Icon_lua.Text_Level_lua:SetText("")
	self.view.Icon_lua.Img_IconSkill_lua:SetVisibility(ESlateVisibility.Visible)
	local IconPath = SkillData.SkillDisplayIcon ~= "" and SkillData.SkillDisplayIcon or SkillData.SkillIcon
	self:SetImage(self.view.Icon_lua.Img_IconSkill_lua, IconPath)
end

------------------------------------------------------------------------------------------------------------------------------------------------------
---打开装配界面
function SkillCusAttriTop:OnClickOpenAssembly()
	if self.parentComponent and self.parentComponent.parentComponent and self.parentComponent.parentComponent.GetCurrentSelectedSkillID then
		Game.SkillCustomSystem:ShowSkillCustomizerRoulette(self.bPCMode, self.parentComponent.parentComponent:GetCurrentSelectedSkillID())
	end
end

--- 此处为自动生成
function SkillCusAttriTop:on_AssembleBtn_luaBtn_ClickArea_lua_Clicked()
	self:OnClickOpenAssembly()
end

return SkillCusAttriTop
