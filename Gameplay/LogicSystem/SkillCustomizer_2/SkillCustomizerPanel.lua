local UITempComFrame = kg_require("Framework.KGFramework.KGUI.Component.Panel.UITempComFrame")
local SkillCusSkillFormSwitchBtn = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCusSkillFormSwitchBtn")
local SkillCusAttriEle = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCusAttriEle")
local SkillCustomizerSkill = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomizerSkill")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

---技能管理 主面板
---@class SkillCustomizerPanel : UIPanel
---@field view SkillCustomizerPanelBlueprint
local SkillCustomizerPanel = DefineClass("SkillCustomizerPanel", UIPanel)

local NewComList = kg_require("Framework.UI.List.NewList.NewComList")
local SkillCustomizerTabItem = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomizerListItems.SkillCustomizerTabItem")

local StringConst = require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")
local EUMGEUMGSequencePlayMode = import("EUMGSequencePlayMode")

SkillCustomizerPanel.eventBindMap = {
	[EEventTypesV2.ON_SKILL_IN_ROULETTE_CLICK] = "OnEquippedItemSelected",
	[EEventTypesV2.ON_SKILL_LEVEL_UP] = "OnSkillLevelUp",
	[EEventTypesV2.SKILL_POST_SKILL_EQUIP_CHANGED] = "OnSkillEquipped",
	[EEventTypesV2.SKILLHUD_UNLOCK_SKILL_lIST_CHANGE] = "OnSkillUnlock",
	[EEventTypesV2.ON_PROFESSION_STATE_CHANGED] = "OnProfessionStateChanged",
	[EEventTypesV2.ON_SKILL_TRAIT_TOGGLED] = "OnTraitPanelToggled",
	[EEventTypesV2.ON_TALENT_RES_LEVEL_UPGRADE] = "OnResonateLevelChanged",
	[EEventTypesV2.ON_SKILL_CUSTOMIZER_PANEL_REFRESH] = "OnRecvRefreshPage",
	[EEventTypesV2.ON_SKILL_ROULETTE_ON] = "OnRouletteOn",
	[EEventTypesV2.ON_SKILL_ROULETTE_OFF] = "OnRouletteOff",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCustomizerPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCustomizerPanel:InitUIData()
	---左侧页签列表数据
	---@type table
	self.TabListData = {}
	---虽然列表会记载当前选择的页签，但是需要记录当前页签ID避免玩家重复点击一个页签时多刷
	---@type number
	self.CurrentTab = 1
	---当前职业形态列表
	---@type table
	self.ProfessionStateList = {}
	---特质子面板
	---@type SkillCustomizerPeculiarityComponent
	self.PeculiarityComponent = nil
end

--- UI组件初始化，此处为自动生成
function SkillCustomizerPanel:InitUIComponent()
    ---@type UITempComFrame
    self.WBP_ComPanel_luaCom = self:CreateComponent(self.view.WBP_ComPanel_lua, UITempComFrame)
    ---@type SkillCusSkillFormSwitchBtn
    self.WBP_SkillCusSkillFormSwitchBtn2_luaCom = self:CreateComponent(self.view.WBP_SkillCusSkillFormSwitchBtn2_lua, SkillCusSkillFormSwitchBtn)
    ---@type SkillCusSkillFormSwitchBtn
    self.WBP_SkillCusSkillFormSwitchBtn_luaCom = self:CreateComponent(self.view.WBP_SkillCusSkillFormSwitchBtn_lua, SkillCusSkillFormSwitchBtn)
    ---@type SkillCusAttriEle
    self.Attribute_luaCom = self:CreateComponent(self.view.Attribute_lua, SkillCusAttriEle)
    ---@type SkillCustomizerSkill
    self.SkillPanel_luaCom = self:CreateComponent(self.view.SkillPanel_lua, SkillCustomizerSkill)
end

---UI事件在这里注册，此处为自动生成
function SkillCustomizerPanel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComPanel_luaCom.onTipClickEvent, "on_WBP_ComPanel_luaCom_TipClickEvent")
    self:AddUIEvent(self.view.RoutineBtn_lua.Btn_ClickArea_lua.OnClicked, "on_RoutineBtn_luaBtn_ClickArea_lua_Clicked")
    self:AddUIEvent(self.view.WBP_ComBtnIconTips_lua.Big_Button_ClickArea_lua.OnClicked, "on_WBP_ComBtnIconTips_luaBig_Button_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCustomizerPanel:InitUIView()
	---创建左侧的页签列表
	self.TabList = self:CreateComponent(self.view.TabList_lua.WBP_ComList_Lua, NewComList, SkillCustomizerTabItem, "TabList")
end

---面板打开的时候触发
---@param Params table 界面打开时的参数
function SkillCustomizerPanel:OnRefresh(Params)
	--构建页签
	self:InitSkillCustomizer()
	self:InitTabList()
	--处理参数，确定是否需要自动选中
	local bNotDefaultSelection, TargetIndex, TargetSkillID = self:ParseRefreshParams(Params)
	if bNotDefaultSelection then
		if TargetIndex > 0 then
			self:SelectTabAndRefresh(TargetIndex)
		elseif (TargetSkillID and TargetSkillID ~= -1) then
			self:SelectTabAndRefresh(1)
			self:SelectSkillByID(TargetSkillID)
		end
	else
		self:SelectTabAndRefresh(1)
	end
	--获取职业形态
	self.ProfessionStateList = Game.SkillCustomSystem:GetCurrentProfessionStates()
	--刷新职业形态
	self:InitProfessionState()
	self:ConfigAddUpCE()
end

---刷新页面，但是不会重新构建页签
function SkillCustomizerPanel:OnRecvRefreshPage(Params)
	--处理参数，确定是否需要自动选中
	local bNotDefaultSelection, TargetIndex, TargetSkillID = self:ParseRefreshParams(Params)
	if bNotDefaultSelection then
		if TargetIndex > 0 then
			self:SelectTabAndRefresh(TargetIndex)
		elseif (TargetSkillID and TargetSkillID ~= -1) then
			self:SelectTabAndRefresh(1)
			self:SelectSkillByID(TargetSkillID)
		end
	else
		self:SelectTabAndRefresh(1)
	end
	--获取职业形态
	self.ProfessionStateList = Game.SkillCustomSystem:GetCurrentProfessionStates()
	--刷新职业形态
	self:InitProfessionState()
	self:ConfigAddUpCE()
end

function SkillCustomizerPanel:OnClose()
	if self.PeculiarityComponent then
		self.PeculiarityComponent:CloseSelf()
	end
	self.PeculiarityComponent = nil
	Game.SkillCustomSystem:CloseSkillCustomizerRoulette()
end

------------------------------------------------------------------------------------------------------------------------------------------------------
---该区域的内容不会主动调用多次执行
--region 初始化

---初始化文本
function SkillCustomizerPanel:InitSkillCustomizer()
	self.view.WBP_ComPanel_lua.WBP_ComBtnBack_lua.Text_Back_lua:SetText(StringConst.Get("SKILLCUSTOMIZER_MAIN_TITLE") or "")
end

---初始化页签TabList
function SkillCustomizerPanel:InitTabList()
	table.clear(self.TabListData)
	self.TabListData = Game.SkillCustomSystem.GetSkillTabTable()
	self.TabList:SetData(#self.TabListData)
end

---处理参数，返回是否自动选中第X页ID为Y的技能
---@param Params table
---@return boolean, number, number
function SkillCustomizerPanel:ParseRefreshParams(params)
	if not params then
		return false
	end
	
	if params.SkillID then
		return true, -1, params.SkillID
	elseif params.TabIndex then
		return true, params.TabIndex, -1
	end
	return false
end

--endregion 初始化

------------------------------------------------------------------------------------------------------------------------------------------------------
---主动调用的界面状态更新

---根据当前页签刷新页面
---@param TargetSkillID number 自动选择的技能ID，为空则选择第一个
---@param bNoAnimation boolean 不播放动画
function SkillCustomizerPanel:RefreshPage(TargetSkillID, bNoAnimation)
	local TabData = Game.TableData.GetSkillTabDataRow(self.CurrentTab)
	if not TabData then
		return
	end
	--刷新技能
	self:ShowSubPageInfo(bNoAnimation)
	if TargetSkillID and TargetSkillID > 0 then
		self:SelectSkillByID(TargetSkillID)
	end
	
	self:ConfigAddUpCE()
end

---选择一个页签并且跳转 -由刷新参数触发
---@param TabIndex number
function SkillCustomizerPanel:SelectTabAndRefresh(TabIndex)
	self.CurrentTab = TabIndex
	self.TabList:Sel(TabIndex)
	self:ShowSubPageInfo()
	--选择第一个技能
	self:SelectFirstSkill()
end

---跳转到页面 -由点击页签列表触发
---@param TabIndex number
function SkillCustomizerPanel:RefreshByTab(TabIndex)
	if TabIndex == self.CurrentTab then
		return
	end
	self.CurrentTab = TabIndex
	self:ShowSubPageInfo()
	--选择第一个技能
	self:SelectFirstSkill()
end

---展示当前面板的内容
---@param bNoAnimation boolean 不播放动画
function SkillCustomizerPanel:ShowSubPageInfo(bNoAnimation)
	--根据列表页签的元数据获取页签信息
	local TabData = self.TabListData[self.TabList:GetSelectedIndex()]
	if not TabData then
		Log.WarningFormat("SkillCustomizer: Unabel to get tab data for current page: %s", self.TabList:GetSelectedIndex())
		return
	end
	
	--如果不是特质界面，通知技能列表进行刷新
	if TabData.SkillDisplayMode ~= 3 then
		self.view.HB_SkillInfo_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if self.PeculiarityComponent then
			self.PeculiarityComponent:CloseSelf()
			self.PeculiarityComponent = nil
		end
		self.SkillPanel_luaCom:InitSkillPage(TabData)
		if not bNoAnimation then
			--刷新页面后，如果移动端轮盘已经打开，应当把右侧半屏设置为置灰
			if Game.NewUIManager:IsShow(UIPanelConfig.SkillCustomizerRoulette_Panel) then
				self:PlayAnimation(self.userWidget.Ani_Content_in_No_Attri_Alpha)
			else
				self:PlayAnimation(self.userWidget.Ani_Content_in)
			end
		end
	else
		self.view.HB_SkillInfo_lua:SetVisibility(ESlateVisibility.Collapsed)
		self:ShowPeculiaritySubPanel()
	end
end

---展示特质界面
function SkillCustomizerPanel:ShowPeculiaritySubPanel()
	if self.PeculiarityComponent ~= nil then
		self.PeculiarityComponent:Show()
	else
		self:AsyncLoadComponent(
			"SkillCustomizerPeculiarityComponent", self.view.Peculiarity_lua,
			function(component) self:OnSkillPeculiarityReady(component) end
		)
	end
end

---展示特质界面 - 加载完毕
function SkillCustomizerPanel:OnSkillPeculiarityReady(component)
	self.PeculiarityComponent = component
	self.PeculiarityComponent:Refresh()
end

---展示右侧半屏
function SkillCustomizerPanel:RefreshAttributeInfo()
	local RoleSkillID, SkillID = self:GetCurrentSelectedSkill()
	if RoleSkillID < 0 or SkillID < 0 then
		self.view.Attribute_lua:SetVisibility(ESlateVisibility.Hidden)
		return
	else
		self.view.Attribute_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
	--设置固定内容
	self.Attribute_luaCom:SetAttributeContent(RoleSkillID, SkillID)
	--设置进阶相关内容
	self.Attribute_luaCom:ConfigUpgradeInfo()
end

---特殊刷新 - 刷新单个技能信息
---用于技能ID没有改变，但是信息发生变化
---@param SkillID number 想要刷新的技能ID
function SkillCustomizerPanel:RefreshSkillByID(SkillID)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_REFRESHED, SkillID)
	--选择这个技能
	self:SelectSkillByID(SkillID)
end

---初始化
function SkillCustomizerPanel:InitProfessionState()
	if #self.ProfessionStateList ~= 2 or not Game.me.ProfessionStateID then
		self.view.KGCanv_SwitchBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	--获取当前职业的职业形态与当前形态
	self.WBP_SkillCusSkillFormSwitchBtn_luaCom:Refresh(self.ProfessionStateList[Game.me.ProfessionStateID], true, false)
	self.WBP_SkillCusSkillFormSwitchBtn2_luaCom:Refresh(
		self.ProfessionStateList[(Game.me.ProfessionStateID == 1) and 2 or 1],
		false,
		false
	)
end

---刷新职业形态
function SkillCustomizerPanel:UpdateProfessionState()
	if #self.ProfessionStateList ~= 2 or not Game.me.ProfessionStateID then
		return
	end
	self.userWidget:PlayAnimation(self.userWidget.Ani_Switch)
	--获取当前职业的职业形态与当前形态
	self.WBP_SkillCusSkillFormSwitchBtn2_luaCom:Refresh(self.ProfessionStateList[Game.me.ProfessionStateID], true, false)
	self.WBP_SkillCusSkillFormSwitchBtn_luaCom:Refresh(
		self.ProfessionStateList[(Game.me.ProfessionStateID == 1) and 2 or 1], 
		false,
		false
	)
end

------------------------------------------------------------------------------------------------------------------------------------------------------
--region Utils

---常规刷新 - 选择第一个技能
function SkillCustomizerPanel:SelectFirstSkill()
	self.SkillPanel_luaCom:SelectFirstSkill()
	self:RefreshAttributeInfo()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_CUSTOMIZER_SELECT, self:GetCurrentSelectedSkillID(), true)
end

---特殊刷新 - 直接根据ID选定特定的技能
---@param SKillID number 想要刷新的技能ID
function SkillCustomizerPanel:SelectSkillByID(SkillID)
	self.SkillPanel_luaCom:SelectSkillByID(SkillID)
	self:RefreshAttributeInfo()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_CUSTOMIZER_SELECT, self:GetCurrentSelectedSkillID(), true)
end

---特殊刷新 - 根据索引选择技能
---@param Index1 number
---@param Index2 number
function SkillCustomizerPanel:SelectSkillByIndex(Index1, Index2)
	self.SkillPanel_luaCom:SelectSkillByIndex(Index1, Index2)
	self:RefreshAttributeInfo()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_CUSTOMIZER_SELECT, self:GetCurrentSelectedSkillID(), true)
end

---获取当前选择的技能的RoleSkillID和SkillID
---@return number, number
function SkillCustomizerPanel:GetCurrentSelectedSkill()
	return self.SkillPanel_luaCom:GetCurrentSelectedSkill()
end

---获取当前选择的技能的RoleSkillID
---@return number
function SkillCustomizerPanel:GetCurrentSelectedRoleSkillID()
	return self.SkillPanel_luaCom:GetCurrentSelectedRoleSkillID()
end

---获取当前选择的技能的SkillID
---@return number
function SkillCustomizerPanel:GetCurrentSelectedSkillID()
	return self.SkillPanel_luaCom:GetCurrentSelectedSkillID()
end

---设置战力显示

function SkillCustomizerPanel:ConfigAddUpCE()
	local CEDetail = Game.me.ZhanliDetail
	self.view.Zhanli_lua:SetText(CEDetail[Game.RoleDisplaySystem.CEType.Skill])
	self.view.ZhanliText_lua:SetText(StringConst.Get(
		"SKILLCUSTOMIZER_CE_ADD_UP"
	))
end

--endregion Utils
------------------------------------------------------------------------------------------------------------------------------------------------------
---该区域为各种回调
--region 列表回调

---技能列表点击事件的回调
---@param RoleSkillID number
---@param SkillID number
function SkillCustomizerPanel:OnSkillItemSelected()
	---刷新右侧面板
	self:RefreshAttributeInfo()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_CUSTOMIZER_SELECT, self:GetCurrentSelectedSkillID(), true)
end

--endregion 列表回调

--region 事件回调

function SkillCustomizerPanel:OnSkillLevelUp(SkillID)
	self:RefreshSkillByID(SkillID)
	-- Post Reminder
	local Level, ExtraLvl = Game.SkillCustomSystem:GetSkillLevel(SkillID) --luacheck:ignore
	if Level then
		local SkillInfo = Game.TableData.GetSkillDataNewRow(SkillID)
		Game.ReminderManager:AddReminderById(
			Enum.EReminderTextData.SKILL_ACTION_UPGRADE_CHECK, { { SkillInfo.Name, Level } }
		)
	end
end

---刷新轮盘和技能 - 收到技能装配后触发
function SkillCustomizerPanel:OnSkillEquipped()
	--通知技能按钮播放装配动效
	local EquippedItemRef = self.SkillPanel_luaCom:GetSkillItem(self:GetCurrentSelectedSkillID())
	if EquippedItemRef then
		EquippedItemRef:OnSkillEquipped()
	end
	--刷新技能列表状态并取消所有选择，右侧半屏保持之前的内容
	self.SkillPanel_luaCom:ClearSelection()
end

---点击轮盘内的技能
function SkillCustomizerPanel:OnEquippedItemSelected(SkillID, SkillSlot)
	Game.SkillCustomSystem:TryEquipSkillToSlot(self:GetCurrentSelectedSkillID(), SkillID, SkillSlot)
end

---技能解锁后刷新
function SkillCustomizerPanel:OnSkillUnlock()
	local CurrentSkillID = self:GetCurrentSelectedSkillID()
	self:RefreshPage(CurrentSkillID)
end

---刷新整页 - 收到形态切换后触发
function SkillCustomizerPanel:OnProfessionStateChanged()
	--更新技能列表
	self:RefreshPage(-1)
	self:UpdateProfessionState()
	self:SelectFirstSkill()
end

---特质页面打开或关闭
function SkillCustomizerPanel:OnTraitPanelToggled(bOpen)
	--打开时隐藏页签中的特质信息，关闭时重新显示
	if bOpen then
		if self.PeculiarityComponent then
			self.PeculiarityComponent:Hide()
		end
	else
		if self.PeculiarityComponent then
			self.PeculiarityComponent:Show()
		end
	end
end

---特质升级后需要刷新页面，因为技能的等级可能会改变
function SkillCustomizerPanel:OnResonateLevelChanged()
	self:RefreshPage(self:GetCurrentSelectedSkillID(), true)
end

--endregion 事件回调

--region 按钮回调

function SkillCustomizerPanel:OnClickExit()
	self:CloseSelf()
end

function SkillCustomizerPanel:OnClickTips()
	local geometry = self.view.WBP_ComPanel_lua.WBP_ComBtnBack_lua.Btn_Info_lua:GetCachedGeometry()
	Game.TipsSystem:ShowTips(Enum.ETipsData.SKILL_CUSTOMIZER, geometry)
end

function SkillCustomizerPanel:OnClickTipsCE()
	Game.TipsSystem:ShowTips(Enum.ETipsData.SKILL_CE_TIPS, self.view.WBP_ComBtnIconTips_lua:GetCachedGeometry())
end

function SkillCustomizerPanel:OnClickSkillRoutine()
	Game.SkillCustomSystem:ShowSkillRoutineMainPanel(true)
end

function SkillCustomizerPanel:OnRouletteOn()
	if not Game.SettingsManager.bPCMode then
		self:PlayAnimation(self.userWidget.Ani_Zhuangpei)
	end
end

function SkillCustomizerPanel:OnRouletteOff()
	if not Game.SettingsManager.bPCMode then
		self:PlayAnimation(self.userWidget.Ani_Zhuangpei, nil, self.userWidget, 0, 1, EUMGEUMGSequencePlayMode.Reverse)
	end
end

--endregion 按钮回调
------------------------------------------------------------------------------------------------------------------------------------------------------
--- 此处为自动生成
function SkillCustomizerPanel:on_WBP_ComPanel_luaCom_backCb()
	self:OnClickExit()
end

--- 此处为自动生成
function SkillCustomizerPanel:on_RoutineBtn_luaBtn_ClickArea_lua_Clicked()
	self:OnClickSkillRoutine()
end

--- 此处为自动生成
function SkillCustomizerPanel:on_WBP_ComPanel_luaCom_TipClickEvent()
	self:OnClickTips()
end

--- 此处为自动生成
function SkillCustomizerPanel:on_WBP_ComBtnIconTips_luaBig_Button_ClickArea_lua_Clicked()
	self:OnClickTipsCE()
end

return SkillCustomizerPanel
