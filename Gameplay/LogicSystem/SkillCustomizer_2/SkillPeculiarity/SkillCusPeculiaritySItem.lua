local SkillCustomizerPeculiaritySBtn = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillPeculiarity.SkillCustomizerPeculiaritySBtn")
local SkillCustomizerPeculiaritySText = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillPeculiarity.SkillCustomizerPeculiaritySText")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---技能特质 - 小特质列表Item
---技能特质 - 未生效特质按钮
---@class SkillCusPeculiaritySItem : UIListItem
---@field view SkillCusPeculiaritySItemBlueprint
local SkillCusPeculiaritySItem = DefineClass("SkillCusPeculiaritySItem", UIListItem)

SkillCusPeculiaritySItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCusPeculiaritySItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCusPeculiaritySItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function SkillCusPeculiaritySItem:InitUIComponent()
    ---@type SkillCustomizerPeculiaritySBtn
    self.WBP_SkillCustomizerPeculiaritySBtn_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerPeculiaritySBtn_lua, SkillCustomizerPeculiaritySBtn)
    ---@type SkillCustomizerPeculiaritySText
    self.WBP_SkillCustomizerPeculiaritySText_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerPeculiaritySText_lua, SkillCustomizerPeculiaritySText)
end

---UI事件在这里注册，此处为自动生成
function SkillCusPeculiaritySItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCusPeculiaritySItem:InitUIView()
end

---面板打开的时候触发
---@param TraitID number
---@param bInSkillCustomizerTabPage boolean 是否在子页签内，即技能管理内或者选择特质的弹窗里
function SkillCusPeculiaritySItem:OnRefresh(TraitID, bInSkillCustomizerTabPage)
	self:SetContent(TraitID, bInSkillCustomizerTabPage)
end

---@param TraitID number
---@param bInSkillCustomizerTabPage boolean 是否在子页签内，即技能管理内或者选择特质的弹窗里
function SkillCusPeculiaritySItem:SetContent(TraitID, bInSkillCustomizerTabPage)
	if type(TraitID) ~= "number" then
		Log.WarningFormat(
			"Incorrect type of parameter was passed when creating SkillCusPeculiaritySItem, expect: number, actually: %s",
			type(TraitID)
		)
		return
	end
	self.WBP_SkillCustomizerPeculiaritySBtn_luaCom:Refresh(TraitID, self:IsLastChild(), bInSkillCustomizerTabPage)
	self.WBP_SkillCustomizerPeculiaritySBtn_luaCom:SetNormal()

	self.WBP_SkillCustomizerPeculiaritySText_luaCom:Refresh(TraitID)
end

return SkillCusPeculiaritySItem
