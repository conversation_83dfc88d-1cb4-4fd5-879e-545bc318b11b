local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---技能特质 - 小特质图标
---@class SkillCustomizerPeculiaritySBtn : UIComponent
---@field view SkillCustomizerPeculiaritySBtnBlueprint
local SkillCustomizerPeculiaritySBtn = DefineClass("SkillCustomizerPeculiaritySBtn", UIComponent)

SkillCustomizerPeculiaritySBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillCustomizerPeculiaritySBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillCustomizerPeculiaritySBtn:InitUIData()
	---是否是最后一个
	---@type boolean
	self.bLast = false
	---是否在子页签内，即技能管理内或者选择特质的弹窗里
	---@type boolean 
	self.bInSkillCustomizerTabPage = true
	---自身是第几个
	---该成员只在按钮没有处于列表当中时使用
	---@type number
	self.Position = 1
end

--- UI组件初始化，此处为自动生成
function SkillCustomizerPeculiaritySBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SkillCustomizerPeculiaritySBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillCustomizerPeculiaritySBtn:InitUIView()
end

---组件刷新统一入口
---@param TraitID number
---@param bLast boolean 是否是最后一个
---@param bInSkillCustomizerTabPage boolean 是否在子页签内，即技能管理内或者选择特质的弹窗里
---@param Position number 是第几个子特质，位于列表之外时记录位置
function SkillCustomizerPeculiaritySBtn:Refresh(TraitID, bLast, bInSkillCustomizerTabPage, Position)
	self.Position = Position
	self.bInSkillCustomizerTabPage = bInSkillCustomizerTabPage
	self.bLast = bLast
	self:SetContent(TraitID)
end

------------------------------------------------------------------------------------------------------------------------------------------------------

---设置按钮的固定内容
---@param TraitID number
function SkillCustomizerPeculiaritySBtn:SetContent(TraitID)
	local TraitData = Game.TableData.GetSkillTraitsDataRow(TraitID)
	if not TraitData then
		Log.WarningFormat("Unable to find trait data, ID: %s", TraitID)
		return
	end
	local SkillData = Game.TableData.GetSkillDataNewRow(TraitData.SkillID)
	if not SkillData then
		Log.WarningFormat("Unable to find skill data, ID: %s", TraitData.SkillID)
		return
	end
	self:SetImage(self.view.KImg_Icon_lua, SkillData.SkillIcon)
end

---设置按钮的状态
function SkillCustomizerPeculiaritySBtn:SetNormal()
	self.userWidget:Event_UI_Style(4, self.bLast)
end

function SkillCustomizerPeculiaritySBtn:SetAdd()
	self.userWidget:Event_UI_Style(1, self.bLast)
end

function SkillCustomizerPeculiaritySBtn:SetLock()
	self.userWidget:Event_UI_Style(2, self.bLast)
end

function SkillCustomizerPeculiaritySBtn:SetArrow()
	self.userWidget:Event_UI_Style(3, self.bLast)
end

function SkillCustomizerPeculiaritySBtn:SetSwitch()
	self.userWidget:Event_UI_Style(0, self.bLast)
end

---不会立刻生效
function SkillCustomizerPeculiaritySBtn:SetSelect(bSelect)
	self.bSelect = bSelect
end

------------------------------------------------------------------------------------------------------------------------------------------------------

function SkillCustomizerPeculiaritySBtn:OnClickButton()
	--当按钮是页签的一部分的时候，打开更换特制的面板
	if self.bInSkillCustomizerTabPage then
		Game.SkillCustomSystem:ShowSkillPeculiarityPopupPanel(self.parentComponent and self.parentComponent.index or 0)
	else
		--选择该特质
		if self.parentComponent and self.parentComponent.OnSelectTrait then
			self.parentComponent:OnSelectTrait(self.Position)
		end
	end
end

--- 此处为自动生成
function SkillCustomizerPeculiaritySBtn:on_Btn_ClickArea_lua_Clicked()
	self:OnClickButton()
end

return SkillCustomizerPeculiaritySBtn
