---@class P_QuickSkillPanel_PC : UIController
---@field public View WBP_QuickSkillPanel_PCView
local P_QuickSkillPanel = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.QuickSkill.P_QuickSkillPanel")
local P_QuickSkillPanel_PC = DefineClass("P_QuickSkillPanel_PC", P_QuickSkillPanel)

---导入精简版技能按钮
local P_QuickSkillItem = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.QuickSkill.P_QuickSkillItem")
---导入套路选项
local P_QuickSkillSelectDropBtn = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.QuickSkill.P_QuickSkillSelectDropBtn")

---重写：PC的解控和机制槽位和移动端是相反的
function P_QuickSkillPanel_PC:OnCreate()
	--左侧技能列表的Tab
	---@type table
	self.SkillTabData = {}
	---@type table
	self.SkillTab = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_SkillTab.ComList, nil, "SkillTab")
	--设置页签列表样式
	self.View:Event_UI_Style(true, false)
	---@type number
	self.CurrentTab = 1
	---左侧列表是否已经初始化
	---@type boolean
	self.bSkillTabInited = false

	--左侧技能列表
	self.SkillListData = {}
	---@type table
	self.SkillList = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_SkillList, P_QuickSkillItem, "SkillList")

	--攻击键图标路径
	---@type string
	self.SkillIconPath = ""

	--技能轮盘组 序列技能
	---@type table
	self.SequenceSkillGroup = {
		[ETE.EBSSkillSlot.SS_Slot01] = self:BindComponent(self.View.WBP_SkillSlot01, P_QuickSkillItem, false),
		[ETE.EBSSkillSlot.SS_Slot02] = self:BindComponent(self.View.WBP_SkillSlot02, P_QuickSkillItem, false),
		[ETE.EBSSkillSlot.SS_Slot03] = self:BindComponent(self.View.WBP_SkillSlot03, P_QuickSkillItem, false),
		[ETE.EBSSkillSlot.SS_Slot04] = self:BindComponent(self.View.WBP_SkillSlot04, P_QuickSkillItem, false),
		[ETE.EBSSkillSlot.SS_Slot05] = self:BindComponent(self.View.WBP_SkillSlot05, P_QuickSkillItem, false),
	}
	--技能轮盘组 不可切换的技能
	---@type table
	self.PassiveSkillGroup = {
		[ETE.EBSSkillSlot.SS_Slot06] = self:BindComponent(self.View.WBP_QuickSkillItem1, P_QuickSkillItem, false),
		[ETE.EBSSkillSlot.SS_DeControlSlot] = self:BindComponent(self.View.WBP_QuickSkillItem2, P_QuickSkillItem, false),
	}
	--技能轮盘组 伙伴技能
	---@type table
	self.FellowSkillGroup = {
		[ETE.EBSSkillSlot.SS_FellowSlot1] = self:BindComponent(self.View.WBP_SkillFellow01, P_QuickSkillItem, false),
		[ETE.EBSSkillSlot.SS_FellowSlot2] = self:BindComponent(self.View.WBP_SkillFellow02, P_QuickSkillItem, false),
	}
	--技能轮盘组 终结技能
	---@type table
	self.UltimateSkillGroup = {
		[ETE.EBSSkillSlot.SS_ExtraordinarySlot] = self:BindComponent(self.View.WBP_SkillUltimate, P_QuickSkillItem, false),
	}

	--技能轮盘组 高亮绑定区域
	---@type table
	self.HighLightGroup = {
		{self.View.Img_Light01_VX, self.View.Img_Light02_VX},
		{self.View.Img_Light04_VX, self.View.Img_Light05_VX},
		{self.View.Img_Light03_VX},
	}
	--技能轮盘组 槽位映射区域
	---@type table
	self.HighLightMap = {
		[ETE.EBSSkillSlot.SS_Slot01] = 1,
		[ETE.EBSSkillSlot.SS_Slot02] = 1,
		[ETE.EBSSkillSlot.SS_Slot03] = 1,
		[ETE.EBSSkillSlot.SS_Slot04] = 1,
		[ETE.EBSSkillSlot.SS_Slot05] = 1,
		[ETE.EBSSkillSlot.SS_FellowSlot1] = 2,
		[ETE.EBSSkillSlot.SS_FellowSlot2] = 2,
		[ETE.EBSSkillSlot.SS_ExtraordinarySlot] = 3,
	}
	--当前高亮的区域
	---@type table
	self.HightLightSlots = {}

	--连招切换按钮绑定
	self.QuickSkillSwitch = self:BindComponent(self.View.WBP_QuickSkillCombo, P_HUDSkillCombo)

	--套路预设下拉框
	---@type table
	self.RoutineListData = {}
	---@type table
	self.RoutineList = BaseList.CreateList(self, BaseList.Kind.GroupView,
		self.View.WBP_QuickSkillSelectTips.ScrollBox, P_QuickSkillSelectDropBtn, "ScrollBox")
	--self.RoutineList = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.WBP_QuickSkillSelectTips.ComList, P_QuickSkillSelectDropBtn, "RoutineList")
	--当前装配的套路ID，记录减少多次刷新
	---@type number
	self.CurrentSchemeID = -1
	--是否已经展开套路列表
	---@type boolean
	self.bRoutineListOpen = false
	--套路列表正在关闭中
	---@type boolean
	self.bRoutineListClosing = false

	--套路切换框的自动关闭事件
	self:AddUIListener(EUIEventTypes.FocusLost, self.View.WBP_QuickSkillSelectTips.WidgetRoot, self.OnCloseRoutineList)
end

---重写：PC HUD的技能按钮没有大小差异
---刷新右侧技能轮盘
function P_QuickSkillPanel_PC:RefreshSkillEquipped()
    ---右侧分区刷新
    --技能轮盘组 序列技能
    self:SetSkillItem(self.SequenceSkillGroup, 3)
    --技能轮盘组 不可切换的技能
    self:SetSkillItem(self.PassiveSkillGroup, 3)
    --技能轮盘组 伙伴技能
    self:SetSkillItem(self.FellowSkillGroup, 3)
    --技能轮盘组 终结技能
    self:SetSkillItem(self.UltimateSkillGroup, 3)
    --攻击键
    self:SetAttackButton()
    self:RefreshAutoSkillStatus()
end

---刷新右侧技能轮盘
---刷新可以装配的部分
function P_QuickSkillPanel_PC:RefreshChangedSkillEquipped()
    ---右侧分区刷新
    --技能轮盘组 序列技能
    self:SetSkillItem(self.SequenceSkillGroup, 3)
	--技能轮盘组 不可切换的技能
	self:SetSkillItem(self.PassiveSkillGroup, 3)
    --技能轮盘组 伙伴技能
    self:SetSkillItem(self.FellowSkillGroup, 3)
    --技能轮盘组 终结技能
    self:SetSkillItem(self.UltimateSkillGroup, 3)
    self:RefreshAutoSkillStatus()
end

return P_QuickSkillPanel_PC