kg_require("Gameplay.LogicSystem.SkillCustomizer_2.QuickSkill.P_QuickSkillSelectDropBtnView")
---@class P_QuickSkillSelectDropBtn : UIComponent
---@field public View WBP_QuickSkillSelectDropBtnView
local P_QuickSkillSelectDropBtn = DefineClass("P_QuickSkillSelectDropBtn", UIComponent, ITreeListComponent)

function P_QuickSkillSelectDropBtn:OnCreate()
    --当前按钮的套路ID
    ---@type number
    self.CurrentID = 1
end

---刷新事件 - 由列表刷新触发
function P_QuickSkillSelectDropBtn:OnListRefresh(parentUI, bSelect, allData, index)
    self.View:Event_UI_Style(false, Game.SkillCustomSystem.model.bPCMode)
    if parentUI and parentUI.RoutineListData then
        local SchemeInfo = parentUI.RoutineListData[index]
        self.CurrentID = SchemeInfo.SchemeID
        if string.isEmpty(SchemeInfo.CustomName) then
            self.View.TB_Title:SetText(StringConst.Get("SKILL_PRESET_TITLE") .. SchemeInfo.SchemeID)
        else
            self.View.TB_Title:SetText(SchemeInfo.CustomName)
        end
    end
end

function P_QuickSkillSelectDropBtn:OnClick_Btn_ClickArea()
    if self.CurrentID == Game.me.curSkillSchemeId then
        Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.SKILL_PRESET_USE_ERRO)
        return
    end
    Game.SkillCustomSystem.sender:ReqSwitchSkillScheme(self.CurrentID)
end

function P_QuickSkillSelectDropBtn:OnHovered()
    self.View:Event_UI_Style(true, Game.SkillCustomSystem.model.bPCMode)
end

function P_QuickSkillSelectDropBtn:OnUnhovered()
    self.View:Event_UI_Style(false, Game.SkillCustomSystem.model.bPCMode)
end



return P_QuickSkillSelectDropBtn
