local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---技能套路 - 套路小技能按钮
---@class SkillRoutineSlot : UIComponent
---@field view SkillRoutineSlotBlueprint
local SkillRoutineSlot = DefineClass("SkillRoutineSlot", UIListItem)

local ESlateVisibility = import("ESlateVisibility")

SkillRoutineSlot.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillRoutineSlot:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillRoutineSlot:InitUIData()
	---Slot对应的技能ID
	---@type number
	self.SkillID = nil
	---Slot是否为特质
	---@type boolean 
	self.bPeculiarity = false
end

--- UI组件初始化，此处为自动生成
function SkillRoutineSlot:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SkillRoutineSlot:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillRoutineSlot:InitUIView()
end

---组件刷新统一入口
function SkillRoutineSlot:OnRefresh(Data)
	self.SkillID = Data[1]
	self.bPeculiarity = Data[3]
	--检查特质或者常规技能 特质技能单独处理
	local SkillData = Game.TableData.GetSkillDataNewRow(Data[1])
	if SkillData then
		self.view.Img_Skill_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.userWidget:SetLarge(Data[2])
		self:SetImage(self.view.Img_Skill_lua, SkillData.SkillIcon)
		if Game.SkillCustomSystem:IsSkillLocked(Data[1]) then
			self.view.Img_Skill_lua:SetIsEnabled(false)
		else
			self.view.Img_Skill_lua:SetIsEnabled(true)
		end
		self.view.KText_Word_lua:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.userWidget:SetLarge(false)
		local TraitData = Game.TableData.GetSkillTraitsDataRow(Data[1])
		if not TraitData then
			Log.WarningFormat("Unable to find trait data, ID: %s", Data[1])
			return
		end
		SkillData = Game.TableData.GetSkillDataNewRow(TraitData.SkillID)
		if not SkillData then
			Log.WarningFormat("Unable to find skill data, ID: %s", TraitData.SkillID)
			return
		end
		self:SetImage(self.view.Img_Skill_lua, SkillData.SkillIcon)
		if Game.SkillCustomSystem:IsTraitUnlocked(Data[1]) then
			self.view.Img_Skill_lua:SetIsEnabled(true)
		else
			self.view.Img_Skill_lua:SetIsEnabled(false)
		end
		self.view.KText_Word_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

---置空
---@param bLarge boolean
function SkillRoutineSlot:SetEmpty(bLarge)
	bLarge = bLarge or false
	self.SkillID = nil
	self.userWidget:SetLarge(bLarge)
	self.view.Img_Skill_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.view.KText_Word_lua:SetVisibility(ESlateVisibility.Collapsed)
end

------------------------------------------------------------------------------------------------------------------------------------------------------

function SkillRoutineSlot:OnClickSlot()
	if self.parentComponent and self.parentComponent.OnClickSlot then
		self.parentComponent:OnClickSlot(self.SkillID, self.bPeculiarity)
	end
end

--- 此处为自动生成
function SkillRoutineSlot:on_Btn_ClickArea_lua_Clicked()
	self:OnClickSlot()
end

return SkillRoutineSlot
