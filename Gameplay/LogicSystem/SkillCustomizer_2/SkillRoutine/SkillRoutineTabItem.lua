local SkillCustomizerTips = kg_require("Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomizerListItems.SkillCustomizerTips")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---技能套路 - 页签元素
---@class SkillRoutineTabItem : UIListItem
---@field view SkillRoutineTabItemBlueprint
local SkillRoutineTabItem = DefineClass("SkillRoutineTabItem", UIListItem)

local StringConst = require "Data.Config.StringConst.StringConst"

local ESlateVisibility = import("ESlateVisibility")

SkillRoutineTabItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillRoutineTabItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillRoutineTabItem:InitUIData()
	---页签是否处于编辑状态
	---@type boolean
	self.bEditMode = false
	---页签是否是新建按钮
	---@type boolean
	self.bAddMode = false
end

--- UI组件初始化，此处为自动生成
function SkillRoutineTabItem:InitUIComponent()
    ---@type SkillCustomizerTips
    self.WBP_SkillCustomizerTips_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerTips_lua, SkillCustomizerTips)
    ---@type SkillCustomizerTips
    self.WBP_SkillCustomizerTips_1_luaCom = self:CreateComponent(self.view.WBP_SkillCustomizerTips_1_lua, SkillCustomizerTips)
end

---UI事件在这里注册，此处为自动生成
function SkillRoutineTabItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Reedit_lua.OnClicked, "on_Btn_Reedit_lua_Clicked")
    self:AddUIEvent(self.view.Btn_Edit_lua.OnClicked, "on_Btn_Edit_lua_Clicked")
    self:AddUIEvent(self.view.Btn_Finish_lua.OnClicked, "on_Btn_Finish_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillRoutineTabItem:InitUIView()
end

---面板打开的时候触发
function SkillRoutineTabItem:OnRefresh(Data)
	self.view.KGHori_World:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.bEditMode = false
	self.bAddMode = false
	--不是添加按钮显示当前套路的信息
	if Data[1] then
		self.bEditMode = false
		local SchemeInfo = Game.me.skillSchemeList[Data[2].Key]
		self.view.Text_Name_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if string.isEmpty(SchemeInfo.CustomName) then
			self.view.Text_Name_lua:SetText(StringConst.Get("SKILL_PRESET_TITLE") .. SchemeInfo.SchemeID)
		else
			self.view.Text_Name_lua:SetText(SchemeInfo.CustomName)
		end
	else
		self.bAddMode = true
		self.view.Text_Name_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

---选择回调
function SkillRoutineTabItem:UpdateSelectionState(bSelected)
	if bSelected then
		if self.parentComponent.parentComponent and self.parentComponent.parentComponent.OnSelectTab then
			self.parentComponent.parentComponent:OnSelectTab(self.index)
		end
	else
		self.bEditMode = false
	end
	self.view.Btn_ClickArea:SetVisibility(bSelected and ESlateVisibility.Hidden or ESlateVisibility.Visible)
	self:RefreshItemStyle(bSelected)
	self:RefreshTagInfo()
end

------------------------------------------------------------------------------------------------------------------------------------------------------

---设置页签状态
function SkillRoutineTabItem:RefreshItemStyle(bSelected)
	if self.bEditMode then
		self.userWidget:Event_UI_Style(false, Game.me.curSkillSchemeId == self.index, 3)
		self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Hidden)
	elseif self.bAddMode then
		self.userWidget:Event_UI_Style(false, Game.me.curSkillSchemeId == self.index, 2)
		self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Hidden)
	else
		self.userWidget:Event_UI_Style(false, Game.me.curSkillSchemeId == self.index, bSelected and 1 or 0)
		self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
	end
	if bSelected then
		self:PlayAnimation(self.userWidget.Ani_Select)
	else
		self:StopAnimation(self.userWidget.Ani_Select)
	end
end

---设置页签状态
function SkillRoutineTabItem:RefreshTagInfo()
	self.view.KGHori_Tips_lua:SetVisibility(ESlateVisibility.Collapsed)
end

------------------------------------------------------------------------------------------------------------------------------------------------------

---编辑模式
---添加模式则请求添加套路，反之编辑名称
function SkillRoutineTabItem:OnClickEdit()
	if self.bAddMode then
		Game.SkillCustomSystem.sender:ReqAddSkillPreset()
	else
		self.bEditMode = true
		self:RefreshItemStyle()
		self:RefreshTagInfo()
	end
end

---确认编辑
---检测并提交套路的名字
function SkillRoutineTabItem:OnClickConfirm()
	self.bEditMode = false
	local Text = self.view.EditText_Name_lua:GetText()
	for key, value in pairs(Game.me.skillSchemeList) do
		if self.index ~= key and value.CustomName == Text then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.SKILL_PRESET_RENAME_ERRO
			)
			return
		end
	end
	local Len = utf8.len(Text)
	if Len > 7 then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_PRESET_RENAME_OVER_ERRO)
		return
	end
	-- 发送请求并且进行敏感字检查
	Game.AllInSdkManager:CheckSensitiveWords(
		{Text},
		function(result)
			if Game.SkillCustomSystem:HasSensitiveWords(result) then
				Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED)
			end
		end, nil
	)
	self.view.EditText_Name_lua:SetText("")
	Game.SkillCustomSystem.sender:ReqChangeSkillSchemeName(self.index, Text)
end

---取消编辑
---返回常规页签
function SkillRoutineTabItem:OnClickCancel()
	self.bEditMode = false
	self:RefreshItemStyle()
	self:RefreshTagInfo()
end

------------------------------------------------------------------------------------------------------------------------------------------------------

--- 此处为自动生成
function SkillRoutineTabItem:on_Btn_Reedit_lua_Clicked()
	self:OnClickCancel()
	self:RefreshItemStyle(true)
end

--- 此处为自动生成
function SkillRoutineTabItem:on_Btn_Edit_lua_Clicked()
	self:OnClickEdit()
end

--- 此处为自动生成
function SkillRoutineTabItem:on_Btn_Finish_lua_Clicked()
	self:OnClickConfirm()
end

return SkillRoutineTabItem
