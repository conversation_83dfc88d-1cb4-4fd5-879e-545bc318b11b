---@class GuildSystemSender:SystemSenderBase
local GuildSystemSender = DefineClass("GuildSystemSender",SystemSenderBase)

function GuildSystemSender:createGuild(name, desc, createType, guildType, badgeIndex, badgeFrameId)
    self.Bridge:createGuild(name, desc, createType, guildType, badgeIndex, badgeFrameId)
end

function GuildSystemSender:getPreGuilds()
    self.Bridge:getPreGuilds()
end

function GuildSystemSender:getGuilds()
    self.Bridge:getGuilds()
end

function GuildSystemSender:getGuildDetail(guildID)
    self.Bridge:getGuildDetail(guildID)
end

function GuildSystemSender:ReqResponseGuildInfo(guildID)
    self.Bridge:ReqResponseGuildInfo(guildID)
end

function GuildSystemSender:responseGuild(guildID)
    self.Bridge:responseGuild(guildID)
end

function GuildSystemSender:cancelResponseGuild()
    self.Bridge:cancelResponseGuild()
end

function GuildSystemSender:applyGuild(guildID, bIsFromCard)
    self.Bridge:applyGuild(guildID, bIsFromCard)
end

function GuildSystemSender:batchApplyGuild(arg, applyList)
    self.Bridge:batchApplyGuild(arg, applyList)
end

function GuildSystemSender:setGuildAutoAgree(bAutoAgree)
    self.Bridge:setGuildAutoAgree(bAutoAgree)
end

function GuildSystemSender:setGuildBanRookieBid(bBanRookie)
    self.Bridge:setGuildBanRookieBid(bBanRookie)
end

function GuildSystemSender:inviteGuild(playerID)
    self.Bridge:inviteGuild(playerID)
end

function GuildSystemSender:agreeGuildInvite(guildID, eid, arg)
    self.Bridge:agreeGuildInvite(guildID, eid, arg)
end

function GuildSystemSender:refuseGuildInvite(arg)
    self.Bridge:refuseGuildInvite(arg)
end

function GuildSystemSender:quitGuild()
    self.Bridge:quitGuild()
end

function GuildSystemSender:getGuildInfo()
    self.Bridge:getGuildInfo()
end

function GuildSystemSender:getGuildMembers()
    self.Bridge:getGuildMembers()
end

function GuildSystemSender:getGuildEvents(arg1, arg2)
    self.Bridge:getGuildEvents(arg1, arg2)
end

function GuildSystemSender:kickGuildMember(eid)
    self.Bridge:kickGuildMember(eid)
end

function GuildSystemSender:getGuildApplys()
    self.Bridge:getGuildApplys()
end

function GuildSystemSender:getGuildApplysNum()
    self.Bridge:getGuildApplysNum()
end

function GuildSystemSender:agreeGuildApply(eid)
    self.Bridge:agreeGuildApply(eid)
end

function GuildSystemSender:deleteGuildApplys()
    self.Bridge:deleteGuildApplys()
end

function GuildSystemSender:resignGuild()
    self.Bridge:resignGuild()
end

function GuildSystemSender:setGuildRole(arg1, arg2)
    self.Bridge:setGuildRole(arg1, arg2)
end

function GuildSystemSender:cancelGuildRole(arg1)
    self.Bridge:cancelGuildRole(arg1)
end

function GuildSystemSender:getCustomRoleRights(arg1)
    self.Bridge:getCustomRoleRights(arg1)
end

function GuildSystemSender:setCustomRoleRights(arg1)
    self.Bridge:setCustomRoleRights(arg1)
end

function GuildSystemSender:resetCustomRoleRights()
    self.Bridge:resetCustomRoleRights()
end

function GuildSystemSender:editDeclaration(arg)
    self.Bridge:editDeclaration(arg)
end

function GuildSystemSender:changeGuildName(arg)
    self.Bridge:changeGuildName(arg)
end

function GuildSystemSender:enterGuildStation()
    self.Bridge:enterGuildStation()
end

function GuildSystemSender:chatToAllGuild(arg)
    self.Bridge:chatToAllGuild(arg)
end

function GuildSystemSender:getGuildWelfare()
    self.Bridge:getGuildWelfare()
end

function GuildSystemSender:getGuildWage()
    self.Bridge:getGuildWage()
end

function GuildSystemSender:getGuildBuildings()
    self.Bridge:getGuildBuildings()
end

function GuildSystemSender:upgradeBuilding(buildID)
    self.Bridge:upgradeBuilding(buildID)
end

function GuildSystemSender:cancelCreateGuild()
    self.Bridge:cancelCreateGuild()
end

function GuildSystemSender:guildSignIn()
    self.Bridge:guildSignIn()
end

function GuildSystemSender:editGuildSignature(arg1, arg2)
    self.Bridge:editGuildSignature(arg1, arg2)
end

function GuildSystemSender:setGuildBadgeFrame(arg1)
    self.Bridge:setGuildBadgeFrame(arg1)
end

function GuildSystemSender:setAutoFormatApprentices(arg1)
    self.Bridge:setAutoFormatApprentices(arg1)
end

function GuildSystemSender:onReceiveGuildNightInvitation()
    self.Bridge:onReceiveGuildNightInvitation()
end

function GuildSystemSender:refreshGuildBless(entityID, bCostMoney)
    self.Bridge:refreshGuildBless(entityID, bCostMoney)
end

function GuildSystemSender:reqCheckInSameGuild(entityID1, entityID2, ChatID)
    self.Bridge:reqCheckInSameGuild(entityID1, entityID2, ChatID)
end

function GuildSystemSender:sendGuildMail(title, content, bCostMoney)
    self.Bridge:sendGuildMail(title, content, bCostMoney)
end

function GuildSystemSender:ChangeGuildInfo(params)
    self.Bridge:ChangeGuildInfo(params)
end

function GuildSystemSender:setGuildRoles(version, rightChanged, id2GroupID)
    self.Bridge:setGuildRoles(version, rightChanged, id2GroupID)
end

function GuildSystemSender:changeGuildBabyName(name)
    self.Bridge:changeGuildBabyName(name)
end

function GuildSystemSender:agreeGuildAllApplys()
    self.Bridge:agreeGuildAllApplys()
end

function GuildSystemSender:getExerciseGuildBuildingLv()
    self.Bridge:getExerciseGuildBuildingLv()
end

function GuildSystemSender:getGuildExerciseInfo()
    self.Bridge:getGuildExerciseInfo()
end

function GuildSystemSender:doGuildExercise(arg1)
    self.Bridge:doGuildExercise(arg1)
end

function GuildSystemSender:getGuildVoyageTask()
    self.Bridge:getGuildVoyageTask()
end

function GuildSystemSender:getGuildVoyageMedalInfo()
    self.Bridge:getGuildVoyageMedalInfo()
end

function GuildSystemSender:getPersonalVoyageTaskReward()
    self.Bridge:getPersonalVoyageTaskReward()
end

function GuildSystemSender:getGuildVoyageBoxReward(arg1)
    self.Bridge:getGuildVoyageBoxReward(arg1)
end

function GuildSystemSender:searchMatchGuilds(pattern, guildType, bExcludeFull, sortTypeList)
    self.Bridge:searchMatchGuilds(pattern, guildType, bExcludeFull, sortTypeList)
end

function GuildSystemSender:getGuildRecommendInfo()
    self.Bridge:getGuildRecommendInfo()
end

function GuildSystemSender:refreshGuildRecommendInfo()
    self.Bridge:refreshGuildRecommendInfo()
end

function GuildSystemSender:changeGuildGroupName(nameData)
    self.Bridge:changeGuildGroupName(nameData)
end

function GuildSystemSender:SendGuildResponseRecruitInfo()
    self.Bridge:SendGuildResponseRecruitInfo()
end

function GuildSystemSender:InviteGuildGroupMemberToGroup(groupID)
    self.Bridge:InviteGuildGroupMemberToGroup(groupID)
end

function GuildSystemSender:refuseGuildApply(eid)
    self.Bridge:refuseGuildApply(eid)
end

function GuildSystemSender:ReqSetGuildRole(version, eid, roleChanged)
    self.Bridge:ReqSetGuildRole(version, eid, roleChanged)
end

function GuildSystemSender:ReqBatchSetGuildRoles(version, roleChangedDict)
    self.Bridge:ReqBatchSetGuildRoles(version, roleChangedDict)
end

function GuildSystemSender:ReqInviteGuildGroup(version, targetId)
    self.Bridge:ReqInviteGuildGroup(version, targetId)
end

function GuildSystemSender:ReqUpdateGuildPicture(resourceID)
    self.Bridge:ReqUpdateGuildPicture(resourceID)
end

function GuildSystemSender:ReqCancelApplyGuild(targetId)
    self.Bridge:ReqCancelApplyGuild(targetId)
end

-- 查询邀请的响应创建的玩家列表的请求
function GuildSystemSender:ReqGetGuildResponseInviteIDs()
	self.Bridge:ReqGetGuildResponseInviteIDs()
end

-- 查询邀请加入公会的玩家列表的请求
function GuildSystemSender:ReqGetGuildRecommendInviteIDs()
	self.Bridge:ReqGetGuildRecommendInviteIDs()
end

return GuildSystemSender
