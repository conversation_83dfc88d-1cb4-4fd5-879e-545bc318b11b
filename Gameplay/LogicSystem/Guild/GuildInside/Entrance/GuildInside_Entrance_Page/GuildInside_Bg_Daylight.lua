local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildInside_Bg_Daylight : UIComponent
---@field view GuildInside_Bg_DaylightBlueprint
local GuildInside_Bg_Daylight = DefineClass("GuildInside_Bg_Daylight", UIComponent)

GuildInside_Bg_Daylight.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_Bg_Daylight:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_Bg_Daylight:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildInside_Bg_Daylight:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildInside_Bg_Daylight:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_Bg_Daylight:InitUIView()
    Game.NewUIManager:ResetInvalidationBox(self:GetBelongPanel())
end

---组件刷新统一入口
function GuildInside_Bg_Daylight:Refresh(...)
end

return GuildInside_Bg_Daylight
