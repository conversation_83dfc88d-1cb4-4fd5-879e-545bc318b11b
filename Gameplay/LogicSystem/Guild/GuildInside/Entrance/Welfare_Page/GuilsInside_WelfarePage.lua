local GuildInside_Welfare01_Item = kg_require("Gameplay.LogicSystem.Guild.GuildInside.Entrance.Welfare_Page.GuildInside_Welfare01_Item")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuilsInside_WelfarePage : UIComponent
---@field view GuilsInside_WelfarePageBlueprint
local GuilsInside_WelfarePage = DefineClass("GuilsInside_WelfarePage", UIComponent)
local GuildConst = kg_require("Gameplay.LogicSystem.Guild.GuildConst")

GuilsInside_WelfarePage.eventBindMap = {
    [EEventTypesV2.GUILD_SIGNATURE_CHANGE] = "RefreshWelfarePage",
    [EEventTypesV2.REFRESH_GUILD_SIGN_IN] = "RefreshWelfarePage",
    [EEventTypesV2.RECEIVE_GUILD_WAGE] = "RefreshWelfarePage",
    [EEventTypesV2.ON_REFRESH_GUILD_WELFARE] = "RefreshWelfarePage",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuilsInside_WelfarePage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuilsInside_WelfarePage:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuilsInside_WelfarePage:InitUIComponent()
    ---@type UISimpleList
    self.GridPanelCom = self:CreateComponent(self.view.GridPanel, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function GuilsInside_WelfarePage:InitUIEvent()
    self:AddUIEvent(self.GridPanelCom.onItemClicked, "on_GridPanelCom_ItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuilsInside_WelfarePage:InitUIView()
end

---组件刷新统一入口
function GuilsInside_WelfarePage:Refresh(...)
    self:RefreshWelfarePage()
    self:ReqGetGuildWelfareWage()
end

function GuilsInside_WelfarePage:RefreshWelfarePage()
    self.GridPanelCom:Refresh(GuildConst.WelfareData)
end

function GuilsInside_WelfarePage:ReqGetGuildWelfareWage()
    Game.GuildSystem.sender:getGuildWelfare()
end

function GuilsInside_WelfarePage:HandleWelfareLink(index, data)
    if data.type ~= GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_SIGN_IN and not Game.GuildSystem:CheckWelfareChoiceValid(index, true) then
        return
    end
    local PlayerInfo = Game.GuildSystem.model.guildPlayer
    if PlayerInfo then
        if data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_NAVI then
            Game.GuildSystem:EnterGuild()
        elseif data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_OPEN_UI then
            UI.ShowUI(table.unpack(data.param))
        elseif data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_GET_AWARD then
            Game.GuildSystem:EnterGuild()
        elseif data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_SIGN_IN then
            UI.ShowUI("P_GuildInSign")
            -- Game.GuildSystem.sender:guildSignIn()
        end
    end
end

--- 此处为自动生成
---@param index number
---@param data table
function GuilsInside_WelfarePage:on_GridPanelCom_ItemClicked(index, data)
    if not data then
        return
    end
    if index == 2 then -- 技能
        Game.GuildSystem:ShowGuildSkill()
    elseif index == 4 then
        Game.UIJumpSystem:JumpToUI(Enum.EGuildConstIntData.GUILD_STORE)
    elseif index == 3 then  -- 分红
        Game.GuildSystem:HandleWageGet()
    else
        self:HandleWelfareLink(index, data)
    end
end

return GuilsInside_WelfarePage
