local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class GuildInside_Welfare01_Item : UIListItem
---@field view GuildInside_Welfare01_ItemBlueprint
local GuildInside_Welfare01_Item = DefineClass("GuildInside_Welfare01_Item", UIListItem)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local GuildConst = kg_require("Gameplay.LogicSystem.Guild.GuildConst")

GuildInside_Welfare01_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_Welfare01_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_Welfare01_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildInside_Welfare01_Item:InitUIComponent()
    ---@type UISimpleList
    self.VB_DetailsCom = self:CreateComponent(self.view.VB_Details, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_Welfare01_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_Welfare01_Item:InitUIView()
end

---面板打开的时候触发
function GuildInside_Welfare01_Item:OnRefresh(data)
    if self:SetActiveType(data) then
        self:SetWelfareName(data.name)
        -- self:SetImage(self.view.Img_Bg, data.icon)
        self:SetWelfareDesc(data)
        self:SetConditionInfo(data)
    end
end

function GuildInside_Welfare01_Item:SetActiveType(data)
    -- TODO 解锁状态
    local bIsDisable = false
    if table.count(data) == 0 then
        bIsDisable = true
        self.userWidget:BP_SetDisable(bIsDisable)
    else
        bIsDisable = false
        self.userWidget:BP_SetDisable(bIsDisable)
    end
    return not bIsDisable
end

function GuildInside_Welfare01_Item:SetWelfareName(name)
    local welfareName = StringConst.Get(name)
    if self.view.Text_Title then
        self.view.Text_Title:SetText(welfareName)
    else
        self.view.Text_First:SetText(utf8.sub(welfareName, 1, 2))
        self.view.Text_Other:SetText(utf8.sub(welfareName, 2))
    end
end

function GuildInside_Welfare01_Item:SetWelfareDesc(data)
    if not self.view.Text_Description then return end
    if data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_SIGN_IN
        and Game.GuildSystem.model.SignatureInfo and Game.GuildSystem.model.SignatureInfo.signature then
        if Game.GuildSystem.model.SignatureInfo.isVoiceSignature then
            self.view.Text_Description:SetText(StringConst.Get("GUILD_WELFARE_SIGNIN_VOICE"))
        else
            self.view.Text_Description:SetText(Game.GuildSystem.model.SignatureInfo.signature)
        end
    else
        self.view.Text_Description:SetText(StringConst.Get(data.description))
    end
end

function GuildInside_Welfare01_Item:SetConditionInfo(data)
    if not self.view.VB_Details then return end
    local conditionInfo = {}
    if data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_SIGN_IN
        and Game.GuildSystem.model.SignatureInfo and Game.GuildSystem.model.SignatureInfo.signature then
        if Game.GuildSystem.model.SignatureInfo.isVoiceSignature then
            table.insert(conditionInfo, {name = StringConst.Get("GUILD_WELFARE_SIGNIN_VOICE")})
        else
            table.insert(conditionInfo, {name = Game.GuildSystem.model.SignatureInfo.signature})
        end
    else
        table.insert(conditionInfo, {name = StringConst.Get(data.description)})
    end
    self.VB_DetailsCom:Refresh(conditionInfo)
end

--- 此处为自动生成
function GuildInside_Welfare01_Item:on_Btn_ClickArea_Clicked()
end

return GuildInside_Welfare01_Item
