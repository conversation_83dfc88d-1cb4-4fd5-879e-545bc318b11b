local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildInActivityTimeMsg : UIComponent
---@field view GuildInActivityTimeMsgBlueprint
local GuildInActivityTimeMsg = DefineClass("GuildInActivityTimeMsg", UIComponent)

GuildInActivityTimeMsg.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInActivityTimeMsg:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInActivityTimeMsg:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildInActivityTimeMsg:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildInActivityTimeMsg:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInActivityTimeMsg:InitUIView()
    -- TODO
    self:SetType(0)
end

---组件刷新统一入口
function GuildInActivityTimeMsg:Refresh(weekTime, countLimit)
    self:SetWeekTime(weekTime)
    self:SetCountLimit(countLimit)
end

function GuildInActivityTimeMsg:SetWeekTime(weekTime)
    self.view.Text_SetWeekTime:SetText(weekTime or "")
end

function GuildInActivityTimeMsg:SetCountLimit(countLimit)
    self.view.Text_Number:SetText(countLimit or "")
end

function GuildInActivityTimeMsg:SetType(type)
    self.userWidget:BP_SetType(type)
end

return GuildInActivityTimeMsg
