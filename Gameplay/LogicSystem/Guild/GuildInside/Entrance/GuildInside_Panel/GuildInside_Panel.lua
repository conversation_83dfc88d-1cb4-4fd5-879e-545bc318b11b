local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GuildInside_Panel : UIPanel
---@field view GuildInside_PanelBlueprint
local GuildInside_Panel = DefineClass("GuildInside_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")

GuildInside_Panel.eventBindMap = {
    [EEventTypesV2.ON_LEAVE_GUILD_SUCCESS] = "OnExitGuild",
    [EEventTypesV2.LEAVE_GUILD] = "OnExitGuild",
    [EEventTypesV2.RECEIVE_GUILD_INFO] = "OnGetGuildInfo",
}

-- 子界面
GuildInside_Panel.GuildSubPages = {
    HOME_PAGE = {
        id = "HOME_PAGE",
        name = StringConst.Get("GUILD_TAG_HOME_PAGE"),
        pageName = UICellConfig.GuildInside_Entrance_Page, needBg = true,
        extraDesc = StringConst.Get("GUILD_TAG_HOME_PAGE_ENG")
    },
    WELFARE_PAGE = {
        id = "RESPONSE_OTHER", name = StringConst.Get("GUILD_TAG_WELFARE_PAGE"),
        pageName = UICellConfig.GuilsInside_WelfarePage, needBg = false, slotId = "CP_ContentRoot",
        extraDesc = StringConst.Get("GUILD_TAG_WELFARE_PAGE_ENG")
    },
    ACTIVITY_PAGE = {
        id = "IN_RESPONSE", name = StringConst.Get("GUILD_TAG_ACTIVITY_PAGE"),
        pageName = UICellConfig.GuildInside_ActivityPage, needBg = false,
        extraDesc = StringConst.Get("GUILD_TAG_ACTIVITY_PAGE_ENG")
    }
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_Panel:InitUIData()
    -- 当前选中页签
    self.showingIndex = -1
    -- 加载的分页组件
    self.pageComponents = {}
    -- 分页数据
    self.tabDisplay = {
        GuildInside_Panel.GuildSubPages.HOME_PAGE,
        GuildInside_Panel.GuildSubPages.WELFARE_PAGE,
        GuildInside_Panel.GuildSubPages.ACTIVITY_PAGE
    }
end

--- UI组件初始化，此处为自动生成
function GuildInside_Panel:InitUIComponent()
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
    ---@type UISimpleList
    self.Selection_SimpleListCom = self:CreateComponent(self.view.Selection_SimpleList, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.Selection_SimpleListCom.onItemSelected, "on_Selection_SimpleListCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_Panel:InitUIView()
    self.WBP_ComBackTitleCom:Refresh(StringConst.Get("RED_CHANNEL_GUILD"), Enum.ETipsData.GUILD)
end

---面板打开的时候触发
function GuildInside_Panel:OnRefresh(...)
    Game.GuildSystem.sender:getGuildInfo()
    self.Selection_SimpleListCom:Refresh(self.tabDisplay)
    -- self.Selection_SimpleListCom:SetSelectedItemByIndex(1, true)
end

function GuildInside_Panel:OnGetGuildInfo(guildPrincipalInfo)
    self.guildPrincipalInfo = guildPrincipalInfo
    local guildId = Game.GameClientData:GetPlayerValue("GuildPopupShowId")
    if guildId ~= Game.me.guildId then
        Game.NewUIManager:OpenPanel("GuildInInvitationPopup_Panel")
        Game.GameClientData:SaveByPlayer("GuildPopupShowId", Game.me.guildId, true)
    end
    self.Selection_SimpleListCom:SetSelectedItemByIndex(self.showingIndex == -1 and 1 or self.showingIndex, true)
    -- if Game.me.eid == self.guildPrincipalInfo.leaderId then
    --     self.view.WBP_GuildComTipsBtnRename:SetVisibility(ESlateVisibility.Collapsed)
    -- else
    --     self.view.WBP_GuildComTipsBtnRename:SetVisibility(ESlateVisibility.Visible)
    -- end
end

function GuildInside_Panel:OnExitGuild()
    self:CloseSelf()
end

function GuildInside_Panel:ShowPage(index)
    local data = self.tabDisplay[index]
    if self.pageComponents[data.pageName] then
        self.pageComponents[data.pageName]:Refresh()
    else
        local slotCP
        if data.slotId then
            slotCP = self.view[data.slotId]
        else
            slotCP = self.view.CP_ContentRoot
        end
        self:AsyncLoadComponent(
            data.pageName, slotCP, 
            function(component)
                self.pageComponents[data.pageName] = component
            end, {self.guildPrincipalInfo}
        )
    end
end

--- 此处为自动生成
function GuildInside_Panel:on_Btn_ClickArea_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.GuildInside_Member_Panel)
end

--- 此处为自动生成
---@param index number
---@param data table
function GuildInside_Panel:on_Selection_SimpleListCom_ItemSelected(index, data)
    if index ~= self.showingIndex then
        self:ShowPage(index)
        local oldTabData = self.tabDisplay[self.showingIndex]
        if oldTabData and self.pageComponents[oldTabData.pageName] then
            self.pageComponents[oldTabData.pageName]:CloseSelf()
            self.pageComponents[oldTabData.pageName] = nil
        end
        self.showingIndex = index
    end
end

return GuildInside_Panel
