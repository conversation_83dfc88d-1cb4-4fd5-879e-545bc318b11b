local GuildInside_Entrance_Item = kg_require("Gameplay.LogicSystem.Guild.GuildInside.Entrance.GuildInside_Entrance_Item")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComBar = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComBar")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GuildInside_Construct_Panel : UIPanel
---@field view GuildInside_Construct_PanelBlueprint
local GuildInside_Construct_Panel = DefineClass("GuildInside_Construct_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")
local GuildFund = Enum.EGuildConstIntData.GUILD_FUND

GuildInside_Construct_Panel.eventBindMap = {
    [EEventTypesV2.RECEIVE_GUILD_BUILDINGS] = "OnGetBuildingInfos",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_Construct_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_Construct_Panel:InitUIData()
    -- 当前建筑信息
    self.buildInfos = {}
    -- 当前选中的建筑id
    self.buildIDInSelected = 1
    -- 所有建筑id
    self.allBuildIndex = {}
    -- 公会信息
    self.guildPrincipalInfo = nil
    -- 公会资金图标
    self.fundIconPath = Game.UIIconUtils.GetIconByItemId(GuildFund, Enum.EUIIconType.Small)
end

--- UI组件初始化，此处为自动生成
function GuildInside_Construct_Panel:InitUIComponent()
    ---@type UISimpleList
    self.Building_SimpleListCom = self:CreateComponent(self.view.Building_SimpleList, UISimpleList)
    ---@type UIComButton
    self.WBP_ComBtnCloseNewCom = self:CreateComponent(self.view.WBP_ComBtnCloseNew, UIComButton)
    ---@type UIComBar
    self.WBP_ComBarNewCom = self:CreateComponent(self.view.WBP_ComBarNew, UIComBar)
    ---@type UIComButton
    self.WBP_LevelUpBtnCom = self:CreateComponent(self.view.WBP_LevelUpBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_Construct_Panel:InitUIEvent()
    self:AddUIEvent(self.Building_SimpleListCom.onItemSelected, "on_Building_SimpleListCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComBtnCloseNewCom.onClickEvent, "on_WBP_ComBtnCloseNewCom_ClickEvent")
    self:AddUIEvent(self.WBP_LevelUpBtnCom.onClickEvent, "on_WBP_LevelUpBtnCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_Construct_Panel:InitUIView()
    self.WBP_LevelUpBtnCom:Refresh(StringConst.Get("GUILD_BUILD_UPGRADE"))
    self:SetImage(self.view.Img_Icon_Fund, self.fundIconPath)
    self:SetImage(self.view.Img_Icon_Fund_Cost, self.fundIconPath)
end

---面板打开的时候触发
function GuildInside_Construct_Panel:OnRefresh(guildPrincipalInfo)
    self.guildPrincipalInfo = guildPrincipalInfo
    Game.GuildSystem.sender:getGuildBuildings()
    self:UpdateGuildBg()
    self:RefreshBuildingList()
    self:RefreshBuildInfo()
end

function GuildInside_Construct_Panel:OnGetBuildingInfos(buildings)
    self.buildInfos = buildings
    self:RefreshBuildingList()
    self:RefreshBuildInfo()
end

function GuildInside_Construct_Panel:RefreshBuildingList()
    local buildingListData = {}
    for _, buildId in pairs(Const.GUILD_BUILDING_TYPE) do
        buildingListData[buildId] = {
            buildData = self.buildInfos[buildId],
            buildId = buildId,
        }
    end
    self.Building_SimpleListCom:Refresh(buildingListData)
    self.Building_SimpleListCom:SetSelectedItemByIndex(self.buildIDInSelected, true)
end

function GuildInside_Construct_Panel:UpdateGuildBg()
    local pageName
    local lastTime = Game.TimeUtils.Day0time(_G._now()) / 1000
    local currentHour = math.floor((_G._now(1) - lastTime) / 3600)
    -- 根据小时数判断是上午、中午、晚上还是夜间
    if currentHour >= 6 and currentHour < 12 then
        pageName = UICellConfig.GuildInside_Bg_Daylight
    elseif currentHour >= 12 and currentHour < 18 then
        pageName = UICellConfig.GuildInside_Bg_Dusk
    else
        pageName = UICellConfig.GuildInside_Bg_Night
    end
    self:AsyncLoadComponent(
        pageName, self.view.Canvas_BgRoot
    )
end

-- 建筑信息
function GuildInside_Construct_Panel:RefreshBuildInfo()
    local buildData = self.buildInfos[self.buildIDInSelected]
    local buildTableData = Game.TableData.GetGuildBuildingDataRow(self.buildIDInSelected)
    if not buildTableData then
        return
    end
    local curBuildLv = Game.GuildSystem:GetBuildingLevel(self.buildIDInSelected)

    self.view.Text_BuildName:SetText(buildTableData.BuildingName)
    self.view.Text_Level:SetText(string.format("Lv.%s", curBuildLv or ""))
    local curEffect = self:GetBuildingEffectText(self.buildIDInSelected, curBuildLv)
    self.view.Text_BuildDes:SetText(Game.TableData.GetGuildBuildingDataRow(self.buildIDInSelected).LevelUpDescription[1])
    self.view.Text_BuildFunction:SetText(curEffect)
    --region 升级条件
    local upgradeInfo = Game.TableData.GetGuildUpgradeDataRow(curBuildLv + 1)
    if not upgradeInfo then
        self.view.Canvas_LevelUp:SetVisibility(ESlateVisibility.Collapsed)
        return
    end
    self.view.Canvas_LevelUp:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.view.Text_BuildLevelUpFunction:SetText(self:GetBuildingEffectText(self.buildIDInSelected, curBuildLv + 1))
    -- 条件描述
    self.view.Text_Result:SetText(StringConst.Get("GUILD_BUILD_CONDITION"))
    if self.buildIDInSelected == Const.GUILD_BUILDING_TYPE.MAIN then
        self.view.Text_Condition:SetText(string.format(
            StringConst.Get("GUILD_BUILD_SUB_CONDITION"), 2, upgradeInfo.MainUpgradeCondition[2]
        ))
    else
        self.view.Text_Condition:SetText(string.format(
            StringConst.Get("GUILD_BUILD_MAIN_CONDITION"), upgradeInfo.SubUpgradeCondition[self.buildIDInSelected]
        ))
    end
    -- 资金条件
    self.view.Text_LevelMoneyTitle:SetText(StringConst.Get("GUILD_BUILD_FUNDS"))
    local guildPrincipalInfoFunds = self.guildPrincipalInfo.funds or 0
    self.view.Text_Num_Fund:SetText(guildPrincipalInfoFunds)
    local needMoney = upgradeInfo.UpgradeCost[self.buildIDInSelected]
    if needMoney > guildPrincipalInfoFunds then
        self.view.Text_Money:SetText(string.format(
            tostring(needMoney), "<Red>%s</>/%s", tostring(guildPrincipalInfoFunds)
        ))
    else
        self.view.Text_Money:SetText(string.format(
            tostring(needMoney), "%s/%s", tostring(guildPrincipalInfoFunds)
        ))
    end
    -- 升级时间
    local totalTime = upgradeInfo.UpgradeTime and upgradeInfo.UpgradeTime[self.buildIDInSelected] or 1
    totalTime = totalTime * 1000
    self.view.Text_TimeWord:SetText(
        Game.TimeUtils.FormatCountDownString(
            totalTime, true
        )
    )
    self:RefreshLevelUpTime()
    -- 升级按钮
    self.view.Text_Num:SetText(string.format("%s/%s", needMoney, guildPrincipalInfoFunds))
    if curBuildLv == Game.GuildSystem:GetBuildingMaxLv(self.buildIDInSelected) then
        self.view.HB_CostInfo:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_LevelUpBtn:SetVisibility(ESlateVisibility.Collapsed)
    else
        local upGradeEndTime = buildData and buildData.endTime or 0
        if upGradeEndTime > 0 and upGradeEndTime * 1000 > _G._now() then
            self.view.HB_CostInfo:SetVisibility(ESlateVisibility.Collapsed)
            self.view.WBP_LevelUpBtn:SetVisibility(ESlateVisibility.Collapsed)
        else
            self.view.HB_CostInfo:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.view.WBP_LevelUpBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
    --endregion
end

-- 建筑升级进度
function GuildInside_Construct_Panel:RefreshLevelUpTime()
    local buildData = self.buildInfos[self.buildIDInSelected] or {}
    local curBuildLv = Game.GuildSystem:GetBuildingLevel(self.buildIDInSelected)
    self.view.Text_TimeTitle:SetText(StringConst.Get("GUILD_BUILD_TIME"))
    local endTime = buildData.endTime or 0
    endTime = endTime * 1000
    local upgradeInfo = Game.TableData.GetGuildUpgradeDataRow((buildData.lv or 1) + 1) or {}
    local totalTime = upgradeInfo.UpgradeTime and upgradeInfo.UpgradeTime[self.buildIDInSelected] or 1
    totalTime = totalTime * 1000
    if endTime - _G._now() > 0 then
        local leftTime = endTime - _G._now()
        self.view.Text_TimeTitle:SetText("剩余时间")
        self.view.Text_Time:SetText(Game.TimeUtils.FormatCountDownString(
            leftTime, true
        ))
        self.WBP_ComBarNewCom:Refresh(math.min(1 - leftTime / totalTime, 1))
        self.view.HB_CostInfo:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_LevelUpBtn:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Canvas_Progress:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.Text_Time:SetText(Game.TimeUtils.FormatCountDownString(
            totalTime, true
        ))
        self.WBP_ComBarNewCom:Refresh(0)
        if curBuildLv == Game.GuildSystem:GetBuildingMaxLv(self.buildIDInSelected) then
            self.view.HB_CostInfo:SetVisibility(ESlateVisibility.Collapsed)
            self.view.WBP_LevelUpBtn:SetVisibility(ESlateVisibility.Collapsed)
        else
            self.view.HB_CostInfo:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.view.WBP_LevelUpBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
        self.view.Canvas_Progress:SetVisibility(ESlateVisibility.Collapsed)
    end
    self:StartTimer('TimeRemainCountDown', function() self:RefreshLevelUpTime() end, 500, 1)
end

function GuildInside_Construct_Panel:GetBuildingEffectText(buildID, curBuildLv)
    local buildTableData = Game.TableData.GetGuildBuildingDataRow(self.buildIDInSelected)
    if buildID == Const.GUILD_BUILDING_TYPE.MAIN then
        return string.format(
            StringConst.Get(buildTableData.Description), curBuildLv * Game.TableData.GetConstDataRow("GUILD_BUILDING_UPGRADE")
        )
    elseif buildID == Const.GUILD_BUILDING_TYPE.PUB then
        local formulaFunc = Game.FormulaManager.GetFormulaFuncByName("Guild_Total_Max_Member_NumFormula")
        return string.format(StringConst.Get(buildTableData.Description), formulaFunc(curBuildLv))
    elseif buildID == Const.GUILD_BUILDING_TYPE.VAULT then
        local formulaFunc = Game.FormulaManager.GetFormulaFuncByName("Guild_Max_FundsFormula")
        return string.format(StringConst.Get(buildTableData.Description), formulaFunc(curBuildLv))
    elseif buildID == Const.GUILD_BUILDING_TYPE.SCHOOL then
        if curBuildLv == 0 then
            return string.format(StringConst.Get(buildTableData.Description), 0)
        else
            return string.format(StringConst.Get(buildTableData.Description), 10)
        end
    elseif buildID == Const.GUILD_BUILDING_TYPE.SHOP then
        local buildInfo = Game.TableData.GetGuildFuncDataRow(curBuildLv)
        if not buildInfo then
            buildInfo = Game.TableData.GetGuildFuncDataRow(Game.GuildSystem:GetBuildingMaxLv(buildID))
        end
        return string.format(StringConst.Get(buildTableData.Description), buildInfo.TotalMaxMemberNum)
    else
        return ""
    end
end

--- 此处为自动生成
---@param index number
---@param data table
function GuildInside_Construct_Panel:on_Building_SimpleListCom_ItemSelected(index, data)
    self.buildIDInSelected = index
    self:RefreshBuildInfo()
end

--- 此处为自动生成
function GuildInside_Construct_Panel:on_WBP_ComBtnCloseNewCom_ClickEvent()
    self:CloseSelf()
end

--- 此处为自动生成
function GuildInside_Construct_Panel:on_WBP_LevelUpBtnCom_ClickEvent()
    if Game.GuildSystem:CheckUpgradeConditions(self.buildIDInSelected) then
        Game.GuildSystem.sender:upgradeBuilding(self.buildIDInSelected)
        local needMoney = 0
        local upgradeInfo = Game.TableData.GetGuildUpgradeDataRow(Game.GuildSystem:GetBuildingLevel(self.buildIDInSelected) + 1)
        if upgradeInfo then
            needMoney = upgradeInfo.UpgradeCost[self.buildIDInSelected]
        end
        self.guildPrincipalInfo.funds = self.guildPrincipalInfo.funds - needMoney
    end
    Game.GuildSystem.sender:getGuildBuildings()
end


--- 此处为自动生成
function GuildInside_Construct_Panel:on_Btn_ClickArea_Clicked()
    Game.CurrencyExchangeSystem:CurrencyItemClickHandler(GuildFund)
end

return GuildInside_Construct_Panel
