local UIComSortBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSortBtn")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class GuildInside_DetailPage_Tab_Item : UIListItem
---@field view GuildInside_DetailPage_Tab_ItemBlueprint
local GuildInside_DetailPage_Tab_Item = DefineClass("GuildInside_DetailPage_Tab_Item", UIListItem)

GuildInside_DetailPage_Tab_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_DetailPage_Tab_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_DetailPage_Tab_Item:InitUIData()
    ---点击排序
    ---@type LuaDelegate<fun(status:SortBtnStatus)>AutoBoundWidgetEvent
    self.onClickSortEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function GuildInside_DetailPage_Tab_Item:InitUIComponent()
    ---@type UIComSortBtn
    self.WBP_ComSortBtnCom = self:CreateComponent(self.view.WBP_ComSortBtn, UIComSortBtn)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_DetailPage_Tab_Item:InitUIEvent()
    self:AddUIEvent(self.WBP_ComSortBtnCom.onSortEvent, "on_WBP_ComSortBtnCom_SortEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_DetailPage_Tab_Item:InitUIView()
end

---面板打开的时候触发
function GuildInside_DetailPage_Tab_Item:OnRefresh(text, defaultSortState)
    self:SetTitle(text)
    self.WBP_ComSortBtnCom:Refresh(defaultSortState or Enum.SortBtnStatus.None)
end

function GuildInside_DetailPage_Tab_Item:SetTitle(text)
    self.view.Text_TabDetail:SetText(text or "")
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_DetailPage_Tab_Item:on_WBP_ComSortBtnCom_SortEvent(status)
    self.onClickSortEvent:Execute(status)
end

return GuildInside_DetailPage_Tab_Item
