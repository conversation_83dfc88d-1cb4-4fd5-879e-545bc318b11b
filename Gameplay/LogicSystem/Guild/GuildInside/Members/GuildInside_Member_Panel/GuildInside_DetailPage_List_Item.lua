local SimpleHead = kg_require("Gameplay.LogicSystem.Social.SimpleHead")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class GuildInside_DetailPage_List_Item : UIListItem
---@field view GuildInside_DetailPage_List_ItemBlueprint
local GuildInside_DetailPage_List_Item = DefineClass("GuildInside_DetailPage_List_Item", UIListItem)
local Const = kg_require("Shared.Const")

GuildInside_DetailPage_List_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_DetailPage_List_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_DetailPage_List_Item:InitUIData()
    self.memberData = nil
end

--- UI组件初始化，此处为自动生成
function GuildInside_DetailPage_List_Item:InitUIComponent()
    ---@type SimpleHead
    self.WBP_HeadCom = self:CreateComponent(self.view.WBP_Head, SimpleHead)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_DetailPage_List_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_DetailPage_List_Item:InitUIView()
    self.userWidget:BP_SetBG(false)
end

---面板打开的时候触发
function GuildInside_DetailPage_List_Item:OnRefresh(memberData)
    self.memberData = memberData
    self.WBP_HeadCom:SetHeadIconByProfession(memberData.school, memberData.sex)
    self.view.Text_Name:SetText(memberData.rolename)
    self.view.Text_LV:SetText(memberData.lv)
    local professionData = Game.TableData.GetPlayerSocialDisplayDataRow(memberData.school)[memberData.sex]
    self.view.Text_Career:SetText(professionData.ClassName)
    self.view.Text_FightingNum:SetText(memberData.power)
    self.view.Text_Ranking:SetText(self.index)
    self.view.Text_Position:SetText(Game.GuildSystem:GetOccupationText(self.memberData, 2))
    self.view.Text_Group:SetText(Game.GuildSystem:GetGroupName(memberData.groupID))
    self.view.Text_Contribute:SetText(Game.GuildSystem:ProcessGuildNumber(memberData.weekContribution))
    self:SetOnline(memberData.offlineTime)
end

function GuildInside_DetailPage_List_Item:SetOnline(offlineTime)
    self.view.Text_State:SetText(Game.GuildSystem:GetOffLineTImeDesc(offlineTime))
    self.userWidget:BP_SetOnline(offlineTime and offlineTime <= 0 or false)
end

return GuildInside_DetailPage_List_Item
