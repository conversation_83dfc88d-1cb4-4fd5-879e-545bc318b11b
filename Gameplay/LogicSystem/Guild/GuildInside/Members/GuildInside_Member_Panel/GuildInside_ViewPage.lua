local GuildInside_DetailPage_Tab_Item = kg_require("Gameplay.LogicSystem.Guild.GuildInside.Members.GuildInside_DetailPage_Tab_Item")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildInside_ViewPage : UIComponent
---@field view GuildInside_ViewPageBlueprint
local GuildInside_ViewPage = DefineClass("GuildInside_ViewPage", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

GuildInside_ViewPage.eventBindMap = {
    [EEventTypesV2.ON_GET_GUILD_RECOMMEND_INFOS] = "OnGetRecommendInfo",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_ViewPage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_ViewPage:InitUIData()
    -- 排序规则
    self.sortRule = {
        { key = "lv", bDescending = false},
        { key = "ZhanLi", bDescending = false },
        { key = "offlineTime",     bDescending = false },
    }
    -- 申请列表
    self.inviteData = {}
    self.inviteShowData = {}

    self.downSelectedIndex = 1
    self.downData = {{type = 0, name = "全部"}}
    local professions = {}
    for professionId, data in ksbcpairs(Game.TableData.GetPlayerSocialDisplayDataTable()) do
        if not table.arrayIndexOf(professions, professionId) then
            table.insert(professions, professionId)
            table.insert(self.downData, {type = professionId, name = data[0].ClassName})
        end
    end
end

--- UI组件初始化，此处为自动生成
function GuildInside_ViewPage:InitUIComponent()
    ---@type GuildInside_DetailPage_Tab_Item
    self.WBP_LvCom = self:CreateComponent(self.view.WBP_Lv, GuildInside_DetailPage_Tab_Item)
    ---@type UIComDropDown
    self.WBP_CareerCom = self:CreateComponent(self.view.WBP_Career, UIComDropDown)
    ---@type GuildInside_DetailPage_Tab_Item
    self.WBP_FightingNumCom = self:CreateComponent(self.view.WBP_FightingNum, GuildInside_DetailPage_Tab_Item)
    ---@type GuildInside_DetailPage_Tab_Item
    self.WBP_OnlineCom = self:CreateComponent(self.view.WBP_Online, GuildInside_DetailPage_Tab_Item)
    ---@type UIListView childScript: GuildInside_ViewPage_List_Item
    self.KGListViewCom = self:CreateComponent(self.view.KGListView, UIListView)
    ---@type UIComButton
    self.WBP_InviteFriendBtnCom = self:CreateComponent(self.view.WBP_InviteFriendBtn, UIComButton)
    ---@type UIComButton
    self.WBP_RefreshBtnCom = self:CreateComponent(self.view.WBP_RefreshBtn, UIComButton)
    ---@type UIComEmptyConent
    self.WBP_ComEmptyCom = self:CreateComponent(self.view.WBP_ComEmpty, UIComEmptyConent)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_ViewPage:InitUIEvent()
    self:AddUIEvent(self.WBP_InviteFriendBtnCom.onClickEvent, "on_WBP_InviteFriendBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_RefreshBtnCom.onClickEvent, "on_WBP_RefreshBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_CareerCom.onItemSelected, "on_WBP_CareerCom_ItemSelected")
    self:AddUIEvent(self.WBP_FightingNumCom.onClickSortEvent, "on_WBP_FightingNumCom_ClickSortEvent")
    self:AddUIEvent(self.WBP_LvCom.onClickSortEvent, "on_WBP_LvCom_ClickSortEvent")
    self:AddUIEvent(self.WBP_OnlineCom.onClickSortEvent, "on_WBP_OnlineCom_ClickSortEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_ViewPage:InitUIView()
    self.WBP_LvCom:Refresh("等级")
    self.WBP_FightingNumCom:Refresh("战力")
    self.WBP_OnlineCom:Refresh("当前状态")
    self.WBP_InviteFriendBtnCom:Refresh("邀请好友")
    if Game.GuildSystem:HasRight(Const.GUILD_RIGHT.MEMBER) then
        self.view.WBP_RefreshBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.WBP_RefreshBtn:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.WBP_CareerCom:Refresh(self.downData, self.downSelectedIndex)
end

---组件刷新统一入口
function GuildInside_ViewPage:Refresh(...)
    self:RefreshInviteList()
    self:RefreshBtn()
    Game.GuildSystem:GetGuildRecommendInfo()
    self:StartTimer("refreshBtn", function() self:RefreshBtn() end, 1000, -1)
end

function GuildInside_ViewPage:RefreshInviteList()
    self.inviteShowData = {}
    local memberCount = 0
    for index, memberData in ipairs(self.inviteData) do
        if self.downData[self.downSelectedIndex].type ~= 0 then
            if memberData.school ~= self.downData[self.downSelectedIndex].type then
                goto continue
            end
        end
        memberCount = memberCount + 1
        table.insert(self.inviteShowData, memberData)
        :: continue ::
    end
    Game.GuildSystem:SortListByRule(self.inviteShowData, self.sortRule)
    self.KGListViewCom:Refresh(self.inviteShowData)
    if memberCount <= 0 then
        self.view.WBP_ComEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.WBP_ComEmpty:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function GuildInside_ViewPage:OnGetRecommendInfo(infos)
    self.inviteData = {}
    for index, data in ipairs(infos) do
        if data.guildId == Const.GUILD_ID_NONE then
            table.insert(self.inviteData, data)
        end
    end
    self:RefreshInviteList()
end

function GuildInside_ViewPage:RefreshBtn()
    local leftTime = (_G._now() - Game.GuildSystem.model.inviteTimestamp) // 1000
    if leftTime > Game.TableData.GetConstDataRow("VISITING_CLUB_MEMBER_COUNTDOWN") then
        self.WBP_RefreshBtnCom:Refresh("刷新列表")
        self.WBP_RefreshBtnCom:SetDisable(false)
    else
        self.WBP_RefreshBtnCom:Refresh(
            string.format(StringConst.Get("GUILD_COMMEN_SECOND"), Game.TableData.GetConstDataRow("VISITING_CLUB_MEMBER_COUNTDOWN") - leftTime)
        )
        self.WBP_RefreshBtnCom:SetDisable(true)
    end
end

function GuildInside_ViewPage:ToggleSortKey(keyName)
    local keyIndex
    for index, sortData in ipairs(self.sortRule) do
        if sortData.key == keyName then
            sortData.bDescending = not sortData.bDescending
            keyIndex = index
            break
        end
    end
    if keyIndex then
        table.insert(self.sortRule, 1, table.remove(self.sortRule, keyIndex))
    end
end

function GuildInside_ViewPage:CanRefresh()
    local curTime = _G._now()
    local leftTime = (curTime - Game.GuildSystem.model.inviteTimestamp) // 1000
    local todayRefreshTimestamp = TimeUtils.Day0time(curTime) + Const.REFRESH_HOUR_PER_DAY * 3600 * 1000
    return leftTime > Game.TableData.GetConstDataRow("VISITING_CLUB_MEMBER_COUNTDOWN") or Game.GuildSystem.model.inviteTimestamp < todayRefreshTimestamp and curTime > todayRefreshTimestamp
end

--- 此处为自动生成
function GuildInside_ViewPage:on_WBP_InviteFriendBtnCom_ClickEvent()
    Game.NewUIManager:OpenPanel("GuildOutResponseAddPopup_Panel")
end

--- 此处为自动生成
function GuildInside_ViewPage:on_WBP_RefreshBtnCom_ClickEvent()
    if self:CanRefresh() then
        Game.GuildSystem.sender:refreshGuildRecommendInfo()
        self.downSelectedIndex = 1
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_INVITE_LIST_REFRESH)
    end
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function GuildInside_ViewPage:on_WBP_CareerCom_ItemSelected(index, data)
    self.downSelectedIndex = index
    self:RefreshInviteList()
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_ViewPage:on_WBP_FightingNumCom_ClickSortEvent(status)
    self:ToggleSortKey("ZhanLi")
    self:RefreshInviteList()
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_ViewPage:on_WBP_LvCom_ClickSortEvent(status)
    self:ToggleSortKey("lv")
    self:RefreshInviteList()
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_ViewPage:on_WBP_OnlineCom_ClickSortEvent(status)
    self:ToggleSortKey("offlineTime")
    self:RefreshInviteList()
end

return GuildInside_ViewPage
