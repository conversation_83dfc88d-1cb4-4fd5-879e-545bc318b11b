local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIComSimpleTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GuildInside_Member_Panel : UIPanel
---@field view GuildInside_Member_PanelBlueprint
local GuildInside_Member_Panel = DefineClass("GuildInside_Member_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")

GuildInside_Member_Panel.eventBindMap = {
    [EEventTypesV2.RECEIVE_GUILD_MEMBERS] = "RefreshGuildInfo",
    [EEventTypesV2.ON_SET_GUILD_ROLES_SUCCESS] = "RefreshGuildInfo",
    [EEventTypesV2.GUILD_MEMBER_KICKED] = "RefreshGuildInfo",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_Member_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_Member_Panel:InitUIData()
    -- 当前界面
    self.currentIndex = -1
    -- 页签数据
    self.tabData = {
        { name = StringConst.Get("GUILD_MEMBER_LIST"), pageName = UICellConfig.GuildInside_DetailPage},
        { name = StringConst.Get("GUILD_MEMBER_STRUCTURE"), pageName = UICellConfig.GuildInside_StructPage},
        { name = "申请列表", pageName = UICellConfig.GuildInside_ApplyPage, redPointId = "GuildInApply"},
        { name = "成员寻访" , pageName = UICellConfig.GuildInside_ViewPage},
    }
    --页签组件
    self.pageComponents = {}
end

--- UI组件初始化，此处为自动生成
function GuildInside_Member_Panel:InitUIComponent()
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
    ---@type UIComSimpleTabList
    self.WBP_ComTabHorizontalCom = self:CreateComponent(self.view.WBP_ComTabHorizontal, UIComSimpleTabList)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_Member_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComTabHorizontalCom.onItemSelected, "on_WBP_ComTabHorizontalCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_Member_Panel:InitUIView()
    self.WBP_ComBackTitleCom:Refresh("主页")
end

---面板打开的时候触发
function GuildInside_Member_Panel:OnRefresh(...)
    self:RefreshGuildInfo()
    self.WBP_ComTabHorizontalCom:Refresh(self.tabData)
    self.WBP_ComTabHorizontalCom:SetSelectedItemByIndex(1, true)
end

function GuildInside_Member_Panel:ShowPage(tabData)
    self:AsyncLoadComponent(
        tabData.pageName, self.view.Canvas_Root,
        function(component)
            self.pageComponents[tabData.pageName] = component
        end
    )
end

function GuildInside_Member_Panel:RefreshGuildInfo()
    self.view.Text_MemberNum:SetText(
        string.format("正式会员：%d/%d", Game.GuildSystem:GetMemberNum(), Game.GuildSystem:GetMaxMemberNum())
    )
    self.view.Text_Candidate:SetText(
        string.format("候补会员：%d/%d", Game.GuildSystem:GetApprenticeNum(), Game.GuildSystem:GetMaxApprenticeNum())
    )
    local memberOnlineNum, apprenticeOnlineNum = Game.GuildSystem:GetOnlineMemberCount()
    self.view.RB_OnlineNum:SetText(string.format("<Green>%d</><Default>人在线</>", memberOnlineNum))
    self.view.RB_OnlineApprenticeNum:SetText(string.format("<Green>%d</><Default>人在线</>", apprenticeOnlineNum))
end

--- 此处为自动生成
---@param index number
---@param data table
function GuildInside_Member_Panel:on_WBP_ComTabHorizontalCom_ItemSelected(index, data)
    if self.currentIndex == index then
        return
    end
    local lastPageName = self.tabData[self.currentIndex] and self.tabData[self.currentIndex].pageName or nil
    if lastPageName and self.pageComponents[lastPageName] then
        self.pageComponents[lastPageName]:CloseSelf()
        self.pageComponents[lastPageName] = nil
    end
    self:ShowPage(data)
    self.currentIndex = index
end

return GuildInside_Member_Panel
