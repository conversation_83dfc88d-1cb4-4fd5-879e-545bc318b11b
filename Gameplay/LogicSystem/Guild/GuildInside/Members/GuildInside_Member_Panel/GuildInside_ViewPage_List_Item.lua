local SimpleHead = kg_require("Gameplay.LogicSystem.Social.SimpleHead")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class GuildInside_ViewPage_List_Item : UIListItem
---@field view GuildInside_ViewPage_List_ItemBlueprint
local GuildInside_ViewPage_List_Item = DefineClass("GuildInside_ViewPage_List_Item", UIListItem)
local Const = kg_require("Shared.Const")

GuildInside_ViewPage_List_Item.eventBindMap = {
    [EEventTypesV2.ON_GUILD_RECOMMEND_INVITE_IDS_CHANGED] = "RefreshInviteStatus",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_ViewPage_List_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_ViewPage_List_Item:InitUIData()
    self.memberData = nil
end

--- UI组件初始化，此处为自动生成
function GuildInside_ViewPage_List_Item:InitUIComponent()
    ---@type SimpleHead
    self.WBP_HeadCom = self:CreateComponent(self.view.WBP_Head, SimpleHead)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_ViewPage_List_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Invite.OnClicked, "on_Btn_Invite_Clicked")
    self:AddUIEvent(self.WBP_HeadCom.onHeadClickEvent, "on_WBP_HeadCom_HeadClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_ViewPage_List_Item:InitUIView()
    self.userWidget:BP_SetBG(false)
end

---面板打开的时候触发
function GuildInside_ViewPage_List_Item:OnRefresh(memberData)
    self.memberData = memberData
    self.WBP_HeadCom:SetHeadIconByProfession(memberData.school, memberData.sex)
    self.view.Text_Name:SetText(memberData.rolename)
    self.view.Text_LV:SetText(memberData.lv)
    local professionData = Game.TableData.GetPlayerSocialDisplayDataRow(memberData.school)[memberData.sex or 0]
    self.view.Text_Career:SetText(professionData.ClassName)
    self.view.Text_FightingNum:SetText(memberData.ZhanLi)
    self:SetOnline(memberData.offlineTime)
    self:RefreshInviteStatus()
end

function GuildInside_ViewPage_List_Item:SetOnline(offlineTime)
    self.view.Text_State:SetText(Game.GuildSystem:GetOffLineTImeDesc(offlineTime))
    self.userWidget:BP_SetOnline(offlineTime and offlineTime <= 0 or false)
end

function GuildInside_ViewPage_List_Item:RefreshInviteStatus()
    self.userWidget:BP_SetInviteState(Game.me.guildRecommendInviteIDs[self.memberData.id] and true or false)
end

--- 此处为自动生成
function GuildInside_ViewPage_List_Item:on_Btn_Invite_Clicked()
    if Game.GuildSystem:HasRight(Const.GUILD_RIGHT.MEMBER) then
        Game.GuildSystem:InviteToGuild(self.data.id, self.data.rolename)
        self:RefreshInviteStatus()
    else
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NONE_GUILD_RIGHT)
    end
end

--- 此处为自动生成
function GuildInside_ViewPage_List_Item:on_WBP_HeadCom_HeadClickEvent()
    Game.TeamSystem:PlayerCardUIDataAsync(self.memberData.id, false, Enum.EFriendAddSourceData.WORLD, nil, Enum.EMenuType.World)
end

return GuildInside_ViewPage_List_Item
