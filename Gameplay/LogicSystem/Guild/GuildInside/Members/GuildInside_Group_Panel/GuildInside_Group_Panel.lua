local GuildInside_GroupTab_Item = kg_require("Gameplay.LogicSystem.Guild.GuildInside.Members.GuildInside_Group_Panel.GuildInside_GroupTab_Item")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComTextSearchBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComTextSearchBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local GuildInside_DetailPage_Tab_Item = kg_require("Gameplay.LogicSystem.Guild.GuildInside.Members.GuildInside_DetailPage_Tab_Item")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GuildInside_Group_Panel : UIPanel
---@field view GuildInside_Group_PanelBlueprint
local GuildInside_Group_Panel = DefineClass("GuildInside_Group_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")
local StringConst = require("Data.Config.StringConst.StringConst")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

GuildInside_Group_Panel.eventBindMap = {
    [_G.EEventTypes.ON_SET_GUILD_GROUP_NAME_SUCCESS] = "RefreshTabData",
    [_G.EEventTypes.ON_SET_GUILD_ROLES_SUCCESS] = "OnRefresh",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_Group_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_Group_Panel:InitUIData()
    self.tabData = {}
    self.memberData = {}
    self.memberShowData = {}
    self.searchKeyWord = ""
    self.memberOnlineNum = 0
    self.noGroupIndex = Game.GuildSystem:GetGroupNum() + 1
    self.selectedTabIndex = 1

    self.downSelectedIndex = 1
    self.downData = {{type = 0, name = "全部"}}
    local professions = {}
    for professionId, data in ksbcpairs(Game.TableData.GetPlayerSocialDisplayDataTable()) do
        if not table.arrayIndexOf(professions, professionId) then
            table.insert(professions, professionId)
            table.insert(self.downData, {type = professionId, name = data[0].ClassName})
        end
    end
    self.sortRule = {
        { key = "offlineTime",     bDescending = false },
        { key = "roles",            bDescending = true },
        { key = "lv",              bDescending = true },
        { key = "school",          bDescending = true },
        { key = "power",           bDescending = true },
    }
end

--- UI组件初始化，此处为自动生成
function GuildInside_Group_Panel:InitUIComponent()
    ---@type UIListView childScript: GuildInside_GroupList_Item
    self.List_MemberCom = self:CreateComponent(self.view.List_Member, UIListView)
    ---@type UIComEmptyConent
    self.WBP_ComEmptyCom = self:CreateComponent(self.view.WBP_ComEmpty, UIComEmptyConent)
    ---@type GuildInside_DetailPage_Tab_Item
    self.WBP_GuildInside_DetailPage_Tab_4Com = self:CreateComponent(self.view.WBP_GuildInside_DetailPage_Tab_4, GuildInside_DetailPage_Tab_Item)
    ---@type GuildInside_DetailPage_Tab_Item
    self.WBP_GuildInside_DetailPage_Tab_3Com = self:CreateComponent(self.view.WBP_GuildInside_DetailPage_Tab_3, GuildInside_DetailPage_Tab_Item)
    ---@type GuildInside_DetailPage_Tab_Item
    self.WBP_GuildInside_DetailPage_Tab_2Com = self:CreateComponent(self.view.WBP_GuildInside_DetailPage_Tab_2, GuildInside_DetailPage_Tab_Item)
    ---@type UIComDropDown
    self.WBP_GuildInside_DetailPage_SelectDropCom = self:CreateComponent(self.view.WBP_GuildInside_DetailPage_SelectDrop, UIComDropDown)
    ---@type GuildInside_DetailPage_Tab_Item
    self.WBP_GuildInside_DetailPage_Tab_1Com = self:CreateComponent(self.view.WBP_GuildInside_DetailPage_Tab_1, GuildInside_DetailPage_Tab_Item)
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
    ---@type UIComTextSearchBox
    self.WBP_ComInputSearchCom = self:CreateComponent(self.view.WBP_ComInputSearch, UIComTextSearchBox)
    ---@type UISimpleList
    self.VB_TabCom = self:CreateComponent(self.view.VB_Tab, UISimpleList)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_Group_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ChangeName.OnClicked, "on_Btn_ChangeName_Clicked")
    self:AddUIEvent(self.view.Btn_MainPage.OnClicked, "on_Btn_MainPage_Clicked")
    self:AddUIEvent(self.List_MemberCom.onItemSelected, "on_List_MemberCom_ItemSelected")
    self:AddUIEvent(self.VB_TabCom.onItemSelected, "on_VB_TabCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComInputSearchCom.onTextChanged, "on_WBP_ComInputSearchCom_TextChanged")
    self:AddUIEvent(self.WBP_GuildInside_DetailPage_SelectDropCom.onItemSelected, "on_WBP_GuildInside_DetailPage_SelectDropCom_ItemSelected")
    self:AddUIEvent(self.WBP_GuildInside_DetailPage_Tab_1Com.onClickSortEvent, "on_WBP_GuildInside_DetailPage_Tab_1Com_ClickSortEvent")
    self:AddUIEvent(self.WBP_GuildInside_DetailPage_Tab_2Com.onClickSortEvent, "on_WBP_GuildInside_DetailPage_Tab_2Com_ClickSortEvent")
    self:AddUIEvent(self.WBP_GuildInside_DetailPage_Tab_3Com.onClickSortEvent, "on_WBP_GuildInside_DetailPage_Tab_3Com_ClickSortEvent")
    self:AddUIEvent(self.WBP_GuildInside_DetailPage_Tab_4Com.onClickSortEvent, "on_WBP_GuildInside_DetailPage_Tab_4Com_ClickSortEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_Group_Panel:InitUIView()
    self.WBP_ComBackTitleCom:Refresh("分组设置")
    self.WBP_ComInputSearchCom:Refresh(StringConst.Get("GUILD_MEMBER_SEARCH"), Game.TableData.GetConstDataRow("FRIEND_SEARCH_LENGTH_LIMIT"))
    self.WBP_ComInputSearchCom:on_Btn_Clear_Clicked()
    self.WBP_ComBtnCom:Refresh("会员候补调整")
    self.WBP_GuildInside_DetailPage_Tab_1Com:Refresh("等级")
    self.WBP_GuildInside_DetailPage_Tab_2Com:Refresh("战力")
    self.WBP_GuildInside_DetailPage_Tab_3Com:Refresh("当前状态")
    self.WBP_GuildInside_DetailPage_Tab_4Com:Refresh("职位")
end

---面板打开的时候触发
function GuildInside_Group_Panel:OnRefresh(memberDatas)
    if memberDatas then
        self:InitData(memberDatas)
    end
    self:RefreshTabData()
    self:RefreshPage()
    self:RefreshGroupMemberList()
end

function GuildInside_Group_Panel:InitData(memberDatas)
    table.clear(self.memberData)
    table.clear(self.memberShowData)
    self.memberOnlineNum = 0
    for _, memberData in ipairs(memberDatas) do
        if memberData.offlineTime == 0 and memberData.roles[Const.GUILD_ROLE_TYPE.COMMON_ROLE] ~= Const.GUILD_ROLE.APPRENTICE then
            self.memberOnlineNum = self.memberOnlineNum + 1
        end
        if memberData.groupID ~= 0 then
            self.memberData[memberData.groupID] = self.memberData[memberData.groupID] or {}
            table.insert(self.memberData[memberData.groupID], memberData)
        else
            self.memberData[self.noGroupIndex] = self.memberData[self.noGroupIndex] or {}
            table.insert(self.memberData[self.noGroupIndex], memberData)
        end
    end
end

function GuildInside_Group_Panel:RefreshTabData()
    table.clear(self.tabData)
    for index = 1, Game.GuildSystem:GetGroupNum() do
        table.insert(self.tabData, {tabName = Game.GuildSystem:GetGroupName(index)})
    end
    table.insert(self.tabData, {tabName = "无分组归属"})
    self.VB_TabCom:Refresh(self.tabData)
    self.VB_TabCom:SetSelectedItemByIndex(self.selectedTabIndex, true)
end

function GuildInside_Group_Panel:RefreshPage()
    self.view.Text_MemberNum:SetText(
        string.format("会员：%d/%d", Game.GuildSystem:GetMemberNum(), Game.GuildSystem:GetMaxMemberNum())
    )
    self.view.RB_OnlineNum:SetText(string.format("<Green>%d</><Default>人在线</>", self.memberOnlineNum))
    self.WBP_GuildInside_DetailPage_SelectDropCom:Refresh(self.downData)
end

function GuildInside_Group_Panel:RefreshGroupMemberList()
    local curMemberData = self.memberData[self.VB_TabCom:GetSelectedItemIndex()] or {}
    local groupMemberOnlineNum = 0
    self.memberShowData = {}
    for _, memberData in ipairs(curMemberData) do
        if memberData.offlineTime == 0 then
            groupMemberOnlineNum = groupMemberOnlineNum + 1
        end
        if self.downData[self.downSelectedIndex].type ~= 0 then
            if memberData.school ~= self.downData[self.downSelectedIndex].type then
                goto continue
            end
        end
        if self.searchKeyWord == nil or self.searchKeyWord == "" then
            table.insert(self.memberShowData, memberData)
        elseif string.contains(memberData.rolename, self.searchKeyWord) then
            table.insert(self.memberShowData, memberData)
        end
        :: continue ::
    end

    Game.GuildSystem:SortListByRule(self.memberShowData, self.sortRule)
    self.List_MemberCom:Refresh(self.memberShowData)

    self.view.Text_MemberNum:SetText(
        string.format(StringConst.Get("GROUP_MEMBER_HINT"), #self.memberShowData, Game.TableData.GetConstDataRow("CLUB_GROUP_MAX_MEMBER"))
    )
    self.view.RB_OnlineNum:SetText(string.format("<Green>%d</><Default>人在线</>", groupMemberOnlineNum))

    if #self.memberShowData == 0 then
        self.view.WBP_ComEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.WBP_ComEmpty:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function GuildInside_Group_Panel:ToggleSortKey(keyName)
    local keyIndex
    for index, sortData in ipairs(self.sortRule) do
        if sortData.key == keyName then
            sortData.bDescending = not sortData.bDescending
            keyIndex = index
            break
        end
    end
    if keyIndex then
        table.insert(self.sortRule, 1, table.remove(self.sortRule, keyIndex))
    end
end

function GuildInside_Group_Panel:OnClose()
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.GuildInside_GroupRight_Panel) then
        Game.NewUIManager:ClosePanel(UIPanelConfig.GuildInside_GroupRight_Panel)
    end
end

--- 此处为自动生成
function GuildInside_Group_Panel:on_Btn_ChangeName_Clicked()
    if not Game.GuildSystem:HasRight(Const.GUILD_RIGHT.GROUP_NAME) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NONE_GUILD_RIGHT)
        return
    end
    local realGroup = DeepCopy(self.tabData)
    table.remove(realGroup, self.noGroupIndex)
    Game.NewUIManager:OpenPanel(UIPanelConfig.GuildInside_GroupRight_Panel, realGroup)
end

--- 此处为自动生成
function GuildInside_Group_Panel:on_Btn_MainPage_Clicked()
    Game.NewUIManager:ClosePanel(UIPanelConfig.GuildInside_Member_Panel)
    self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function GuildInside_Group_Panel:on_List_MemberCom_ItemSelected(index, data)
    local widget = self.List_MemberCom:GetItemByIndex(index).userWidget
    local cachedGeometry = widget:GetCachedGeometry()
    local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
    local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
        _G.GetContextObject(), cachedGeometry, localSize, nil, nil
    )
    if data then
        if data.id ~= Game.me.eid then
            Game.TabClose:AttachPanel(
                "ComTagBoxPanel", Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, widget
            )
            Game.NewUIManager:OpenPanel(
                "ComTagBoxPanel", viewportPosition.X - 1000, viewportPosition.Y - 50,
                {
                    EntityID = data.id, Name = data.rolename, Level = data.lv,
                    ProfessionID = data.school, Sex = data.sex, GuildID = Game.me.guildId,
                    sourceID = Enum.EFriendAddSourceData.GUILD
                },
                Enum.EMenuType.GuildGroup
            )
        end
    end
end

--- 此处为自动生成
---@param index number
---@param data table
function GuildInside_Group_Panel:on_VB_TabCom_ItemSelected(index, data)
    if index == self.noGroupIndex then
        self.view.WBP_GuildInside_DetailPage_Tab_4:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.WBP_ComBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.TB_Word:SetText("邀请入组")
    else
        self.view.WBP_GuildInside_DetailPage_Tab_4:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_ComBtn:SetVisibility(ESlateVisibility.Collapsed)
        self.view.TB_Word:SetText(StringConst.Get("GROUP_INVITE_JOIN_TEAM"))
    end
    self.selectedTabIndex = index
    self:RefreshGroupMemberList()
end

--- 此处为自动生成
function GuildInside_Group_Panel:on_WBP_ComBtnCom_ClickEvent()
    if not Game.GuildSystem:HasRight(Const.GUILD_RIGHT.APPRENTICE) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NONE_GUILD_RIGHT)
        return
    end
    Game.NewUIManager:OpenPanel(UIPanelConfig.GuildInside_Adjust_Panel)
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function GuildInside_Group_Panel:on_WBP_GuildInside_DetailPage_SelectDropCom_ItemSelected(index, data)
    self.downSelectedIndex = index
    self:RefreshGroupMemberList()
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_Group_Panel:on_WBP_GuildInside_DetailPage_Tab_1Com_ClickSortEvent(status)
    self:ToggleSortKey("lv")
    self:RefreshGroupMemberList()
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_Group_Panel:on_WBP_GuildInside_DetailPage_Tab_2Com_ClickSortEvent(status)
    self:ToggleSortKey("power")
    self:RefreshGroupMemberList()
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_Group_Panel:on_WBP_GuildInside_DetailPage_Tab_3Com_ClickSortEvent(status)
    self:ToggleSortKey("offlineTime")
    self:RefreshGroupMemberList()
end

--- 此处为自动生成
---@param status SortBtnStatus
function GuildInside_Group_Panel:on_WBP_GuildInside_DetailPage_Tab_4Com_ClickSortEvent(status)
    self:ToggleSortKey("roles")
    self:RefreshGroupMemberList()
end


--- 此处为自动生成
---@param text string
function GuildInside_Group_Panel:on_WBP_ComInputSearchCom_TextChanged(text)
    self.searchKeyWord = text
    self:RefreshGroupMemberList()
end

return GuildInside_Group_Panel
