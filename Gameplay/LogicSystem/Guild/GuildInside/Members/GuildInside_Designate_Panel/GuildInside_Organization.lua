local GuildInside_Manage_Item = kg_require("Gameplay.LogicSystem.Guild.GuildInside.Members.GuildInside_Designate_Panel.GuildInside_Manage_Item")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildInside_Organization : UIComponent
---@field view GuildInside_OrganizationBlueprint
local GuildInside_Organization = DefineClass("GuildInside_Organization", UIComponent)
local Const = kg_require("Shared.Const")
local ESlateVisibility = import("ESlateVisibility")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local UICompRenderTarget = kg_require("Framework.KGFramework.KGUI.Component.RT.UICompRenderTarget")

GuildInside_Organization.eventBindMap = {
    [EEventTypesV2.ON_TRY_ADD_MEMBER_ROLE] = "OnGetAddRoleOperation",
    [EEventTypesV2.ON_TRY_ADD_MEMBER_GROUP] = "AddMemberToGroup",
    [EEventTypesV2.ON_TRY_REMOVE_MEMBER_GROUP] = "RemoveMemberFromGroup",
    [EEventTypesV2.ON_GUILD_MEMBER_UPDATE] = "OnMemberChanged",
    [EEventTypesV2.ON_PLAYER_APPEARANCE_GET] = "OnGetLeaderAvatarAppearanceData"
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInside_Organization:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInside_Organization:InitUIData()
    self.needChangeEid = nil            -- 需要修改职位的eid
    self.bInAdjusting = false           -- 是否在调整中
    self.guildMembersAdjusting = {}     -- 调整中的成员信息
    self.memberNum = nil                -- 正式会员数量
    self.presidentSelected = false      -- 会长选中

    self.memberChanged = false
    self.groupData = {}
    self.memberDataByEid = {}
    -- [roleType][roleId][slotId]->eid
    self.guildRoleSlotInfo = {}
    self.rightChanged = {}
    self.initRightData = {}

    self.roleSortMap = {}
    -- 初始化职位分类信息（每个分类只会出现一个选中态）
    for _, rightData in ksbcpairs(Game.TableData.GetGuildRightDataTable()) do
        self.roleSortMap[rightData.Id] = rightData.AppointType
    end

    -- 会长id
    self.leaderId = Game.GuildSystem.model.GuildPrincipalInfo.leaderId
	---@type UIRenderTargetComponentParam
	self.RTCompParam = nil
end

--- UI组件初始化，此处为自动生成
function GuildInside_Organization:InitUIComponent()
    ---@type GuildInside_Manage_Item
    self.WBP_VicePresidentCom = self:CreateComponent(self.view.WBP_VicePresident, GuildInside_Manage_Item)
    ---@type UISimpleList
    self.VB_DirectorCom = self:CreateComponent(self.view.VB_Director, UISimpleList)
    ---@type UISimpleList
    self.VB_StarCom = self:CreateComponent(self.view.VB_Star, UISimpleList)
    ---@type UISimpleList
    self.VB_GroupCom = self:CreateComponent(self.view.VB_Group, UISimpleList)
    ---@type UISimpleList
    self.HB_MemberCom = self:CreateComponent(self.view.HB_Member, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function GuildInside_Organization:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Btn_President_ClickArea.OnClicked, "on_Btn_President_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInside_Organization:InitUIView()
    self:RefreshRT()
end

---组件刷新统一入口
function GuildInside_Organization:Refresh(needChangeEid)
    if needChangeEid then 
        self.bInAdjusting = true
    else
        self.bInAdjusting = false
    end
    self:InitSlotData(
        DeepCopy(Game.GuildSystem.model.guildMembers),
        DeepCopy(Game.GuildSystem.model.guildRoleSlotInfo),
        needChangeEid
    )
    -- 顺序从低到高 防止低职位把高职位选中态清掉
    self:RefreshMemberNum()
    self:RefreshGroupData()
    self:RefreshRoleData()
    self:RefreshPresidentData()
    -- 保存角色原始职位数据
    self.initRightData = DeepCopy(self.rightChanged)
end

function GuildInside_Organization:OnClose()
	if self.RTComp then
		self:RemoveComponent(self.RTComp)
		self.RTComp = nil
	end
end

-- 初始化职位数据
function GuildInside_Organization:InitSlotData(guildMembers, guildRoleSlotInfo, needChangeEid)
    -- 分组数据
    self.groupData = {}
    self.memberDataByEid = {}
    self.guildRoleSlotInfo = {}
    for _ = 1, Game.GuildSystem:GetGroupNum() do
        table.insert(self.groupData, {})
    end
    for _, memberData in ipairs(guildMembers) do
        if self.groupData[memberData.groupID] then
            table.insert(self.groupData[memberData.groupID], memberData)
        end
        self.memberDataByEid[memberData.id] = memberData
    end
    self.guildRoleSlotInfo = guildRoleSlotInfo
    self.needChangeEid = needChangeEid
end

-- 设置分组数据
function GuildInside_Organization:RefreshGroupData()
    local groupData = {}
    local groupNum = Game.GuildSystem:GetGroupNum()
    for slotId = 1, groupNum do
        local leaderData = self:GetGroupLeader(slotId)
        table.insert(groupData, {
                RoleId = Const.GUILD_ROLE.GROUP_LEADER,
                SlotId = slotId,
                Num = #self.groupData[slotId],
                MaxNum = Game.TableData.GetConstDataRow("CLUB_GROUP_MAX_MEMBER"),
                IsInAdjusting = self.bInAdjusting,
                MemberData = leaderData,
                NeedChangeEid = self.needChangeEid,
            })
    end
    self.VB_GroupCom:Refresh(groupData)
end

-- 设置会员/候补数据
function GuildInside_Organization:RefreshMemberNum()
    local memberNum, apprenticeNum = 0, 0
    local memberData
    for eid, memberData in pairs(self.memberDataByEid) do
        if memberData.roles[Const.GUILD_ROLE_TYPE.COMMON_ROLE] == Const.GUILD_ROLE.APPRENTICE then
            apprenticeNum = apprenticeNum + 1
        else
            memberNum = memberNum + 1
        end
    end
    self.memberNum = memberNum
    if self.needChangeEid then
        memberData = self.memberDataByEid[self.needChangeEid]
    end
    local memberList = {
        {
            RoleId = Const.GUILD_ROLE.MEMBER,
            SlotId = 1,
            Num = memberNum,
            MaxNum = Game.GuildSystem:GetMaxMemberNum(),
            IsInAdjusting = self.bInAdjusting,
            MemberData = memberData,
            NeedChangeEid = self.needChangeEid,
        },
        {
            RoleId = Const.GUILD_ROLE.APPRENTICE,
            SlotId = 2,
            Num = apprenticeNum,
            MaxNum = Game.GuildSystem:GetMaxApprenticeNum(),
            IsInAdjusting = self.bInAdjusting,
            MemberData = memberData,
            NeedChangeEid = self.needChangeEid,
        }
    }
    self.HB_MemberCom:Refresh(memberList)
end

-- 设置会长数据
function GuildInside_Organization:RefreshPresidentData()
    if self.bInAdjusting then
        self.view.Canvas_BtnInfo:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.Canvas_BtnInfo:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    local leaderEid = Game.GuildSystem.model.guildRoleSlotInfo[Const.GUILD_ROLE.PRESIDENT][1]
    local leaderData = self.memberDataByEid[leaderEid]
    self.view.Text_Name:SetText(leaderData.rolename or "")
    self.view.Text_Level:SetText(leaderData.lv or "")
    local classIcon = Game.TableData.GetPlayerSocialDisplayDataRow(leaderData.school)[0].HeadIcon
    -- 职业图标
    self:SetImage(self.view.Img_Status, classIcon)
    self.presidentSelected = self.needChangeEid == leaderEid
    self:SetSelectedPresident(self.presidentSelected)
end

-- 设置副会长、理事、俱乐部之星数据
function GuildInside_Organization:RefreshRoleData()
    local roleTableData = Game.TableData.GetGuildRightDataTable()
    -- 副会长
    self.WBP_VicePresidentCom:OnRefresh({
        RoleId = Const.GUILD_ROLE.VICE_PRESIDENT,
        SlotId = 1,
        HasTitle = true,
        MemberData = self:GetMemberByRole(Const.GUILD_ROLE.VICE_PRESIDENT, 1),
        IsInAdjusting = self.bInAdjusting,
        NeedChangeEid = self.needChangeEid,
    })
    --理事
    local viceDirectorData = {}
    local selectedIndex = nil
    for slotId = 1, roleTableData[Const.GUILD_ROLE.DIRECTOR].MaxNum do
        table.insert(viceDirectorData, {
            RoleId = Const.GUILD_ROLE.DIRECTOR,
            SlotId = slotId,
            HasTitle = false,
            MemberData = self:GetMemberByRole(Const.GUILD_ROLE.DIRECTOR, slotId),
            IsInAdjusting = self.bInAdjusting,
            NeedChangeEid = self.needChangeEid,
        })
    end
    self.VB_DirectorCom:Refresh(viceDirectorData)
    if selectedIndex then
        self.VB_DirectorCom:SetSelectedItemByIndex(selectedIndex)
    end
    -- 俱乐部之星
    local starData = {}
    selectedIndex = nil
    for starSlot = 1, roleTableData[Const.GUILD_ROLE.BABY].MaxNum do
        table.insert(starData, {
            RoleId = Const.GUILD_ROLE.BABY,
            SlotId = starSlot,
            HasTitle = false,
            MemberData = self:GetMemberByRole(Const.GUILD_ROLE.BABY, starSlot),
            IsInAdjusting = self.bInAdjusting,
            NeedChangeEid = self.needChangeEid,
            IsStar = true,
        })
    end
    self.VB_StarCom:Refresh(starData)
    if selectedIndex then
        self.VB_StarCom:SetSelectedItemByIndex(selectedIndex)
    end
end

function GuildInside_Organization:SetMemberNum(memberNum)
    local item = self.HB_MemberCom:GetItemByKey("RoleId", Const.GUILD_ROLE.MEMBER)
    if item then
        item:UpdateMemberNum(memberNum)
    end
end

function GuildInside_Organization:GetGroupLeader(slotId)
    for _, memberData in ipairs(self.groupData[slotId]) do
        if memberData.roles[Const.GUILD_ROLE_TYPE.GROUP_ROLE] == Const.GUILD_ROLE.GROUP_LEADER then
            return memberData
        end
    end
    return nil
end

function GuildInside_Organization:GetMemberByRole(roleId, slotId)
    local roleData = self.guildRoleSlotInfo[roleId]
    if roleData and self.guildRoleSlotInfo[roleId][slotId] then
        return self.memberDataByEid[self.guildRoleSlotInfo[roleId][slotId]]
    else
        return nil
    end
end

function GuildInside_Organization:SetSelectedPresident(bIsSelected)
    if self.bInAdjusting then
        self.presidentSelected = bIsSelected
        self.view.Img_Select:SetVisibility(bIsSelected and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
    else
        self.view.Img_Select:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--region 会长外观

function GuildInside_Organization:OnGetLeaderAvatarAppearanceData()
	local avatarAppearanceData = Game.FashionSystem:GetOtherPlayerAppearanceData(self.leaderId)
    if not self.RTComp then
		self.RTComp = self:CreateComponent(self.userWidget, UICompRenderTarget)

		self.RTCompParam = {
			SceneName = Enum.ESceneDisplayEnum.UIRTGuildPresident,
			ActorParams = self:CreateRTActorParams(avatarAppearanceData),
			CaptureInterval = 60,
			StartCaptureWaitAllLoad = true,
			OnSceneLoaded = function() 
				self:OnLoadFinish()
			end
		}
	else
		self.RTCompParam.ActorParams = self:CreateRTActorParams(avatarAppearanceData)
	end
	self.RTComp:Refresh(self.RTCompParam)
end

function GuildInside_Organization:CreateRTActorParams(avatarAppearanceData)
	return {
		{
			TagName = "President",
			ActorType = UICompRenderTarget.ActorType.Player,
			Pos = {-2, -10, -70},
			Rotation = {0, 80, 0},
            AppearanceData = avatarAppearanceData
		},	
	}
end

function GuildInside_Organization:RefreshRT()
	self.view.Img_Role:SetVisibility(ESlateVisibility.Collapsed)
	-- 查询会长外观数据
    if Game.FashionSystem:GetOtherPlayerAppearanceData(self.leaderId) then
        self:OnGetLeaderAvatarAppearanceData()
    else
        Game.me:ReqOtherRoleShapeData({self.leaderId})
    end
end

function GuildInside_Organization:OnLoadFinish()
	self.view.Img_Role:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

--endregion

--region 对外接口
function GuildInside_Organization:Reset()
    table.clear(self.rightChanged)
    self:Refresh(self.needChangeEid)
end

function GuildInside_Organization:OnMemberChanged()
    self.memberChanged = true
end

function GuildInside_Organization:MemberChangedReset()
    self.memberChanged = false
    self:Reset()
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CLUB_MEMBER_CHANGE)
end

-- 统一处理每个职位的选中/取消选中情况
function GuildInside_Organization:SetSelectedRoleAndSlot(roleId, slotId, bIsSelected)
    self.rightChanged[self.roleSortMap[roleId]] = self.rightChanged[self.roleSortMap[roleId]] or {}
    local targetRoleTypeData = self.rightChanged[self.roleSortMap[roleId]]
    targetRoleTypeData[roleId] = {}
    if bIsSelected then
        self:ClearOtherSlot(roleId, slotId)
    end
    if roleId == Const.GUILD_ROLE.PRESIDENT then
        self:SetSelectedPresident(bIsSelected)
        if bIsSelected then
            targetRoleTypeData[roleId][1] = self.needChangeEid
        end
    elseif roleId == Const.GUILD_ROLE.VICE_PRESIDENT then
        self.WBP_VicePresidentCom:SetSelected(bIsSelected)
        if bIsSelected then
            targetRoleTypeData[roleId][1] = self.needChangeEid
        end
    elseif roleId == Const.GUILD_ROLE.DIRECTOR then
        if bIsSelected then
            targetRoleTypeData[roleId][slotId] = self.needChangeEid
            self.VB_DirectorCom:SetSelectedItemByIndex(slotId, true)
        else
            self.VB_DirectorCom:ClearSelection()
        end
    elseif roleId == Const.GUILD_ROLE.BABY then
        if bIsSelected then
            targetRoleTypeData[roleId][slotId] = self.needChangeEid
            self.VB_StarCom:SetSelectedItemByIndex(slotId, true)
        else
            self.VB_StarCom:ClearSelection()
        end
    elseif roleId == Const.GUILD_ROLE.GROUP_LEADER or roleId == Const.GUILD_ROLE.GROUP_MEMBER then
        if bIsSelected then
            targetRoleTypeData[roleId][slotId] = self.needChangeEid
            self.VB_GroupCom:SetSelectedItemByIndex(slotId, true)
        else
            self.VB_GroupCom:ClearSelection()
        end
    elseif roleId == Const.GUILD_ROLE.MEMBER or roleId == Const.GUILD_ROLE.APPRENTICE then
        if bIsSelected then
            targetRoleTypeData[roleId][slotId] = self.needChangeEid
            self.HB_MemberCom:SetSelectedItemByIndex(slotId, true)
        else
            self.HB_MemberCom:ClearSelection()
        end
    end
end

-- 清理职位冲突选中情况
function GuildInside_Organization:ClearOtherSlot(newRoleId, newSlotId)
    local newAppointType = self.roleSortMap[newRoleId]
    for roleId, appointType in pairs(self.roleSortMap) do
        if appointType == newAppointType then
            if roleId ~= newRoleId then
                self:SetSelectedRoleAndSlot(roleId, nil, false)
            end
        end
    end
end

-- 检查职位是否发生变化
function GuildInside_Organization:CheckRoleHasChanged()
    return table.count(self:GetChangedInfo()) == 0
end

-- 获取变更的职位数据
function GuildInside_Organization:GetChangedInfo()
    local finalChangedInfo = {}
    for roleType, roleTypeData in pairs(self.rightChanged) do
        for roleId, slotIdData in pairs(roleTypeData) do
            for slotId, eid in pairs(slotIdData) do
                if not (self.initRightData[roleType] and self.initRightData[roleType][roleId] and 
                    self.initRightData[roleType][roleId][slotId] and self.initRightData[roleType][roleId][slotId] == eid) then
                    -- 之前没有 现在有，赋值slotId表示任命
                    finalChangedInfo[roleId] = slotId
                end
            end
        end
    end
    for roleType, roleTypeData in pairs(self.initRightData) do
        for roleId, slotIdData in pairs(roleTypeData) do
            for slotId, eid in pairs(slotIdData) do
                if not (self.rightChanged[roleType] and self.rightChanged[roleType][roleId] and self.rightChanged[roleType][roleId][slotId]) then
                    -- 之前有 现在没有，赋值0表示卸任
                    if not finalChangedInfo[roleId] then
                        finalChangedInfo[roleId] = 0
                    end
                end
            end
        end
    end
    return finalChangedInfo
end

-- 获取最终需要上传给服务端的职位变动数据
function GuildInside_Organization:GetFinalRoleChangedInfo()
    local finalChangedInfo = self:GetChangedInfo()
    -- 候补的slotId需要默认调整为1
    if finalChangedInfo[Const.GUILD_ROLE.APPRENTICE] and finalChangedInfo[Const.GUILD_ROLE.APPRENTICE] ~= 0 then
        finalChangedInfo[Const.GUILD_ROLE.APPRENTICE] = 1
    end
    return finalChangedInfo
end

--endregion

-- 头像点击事件
function GuildInside_Organization:OnClickHead(roleId, slotId)
    if not self.bInAdjusting then
        if Game.GuildSystem.model.guildRoleSlotInfo[roleId] and Game.GuildSystem.model.guildRoleSlotInfo[roleId][slotId] then
            Game.TeamSystem:PlayerCardUIDataAsync(Game.GuildSystem.model.guildRoleSlotInfo[roleId][slotId], true, Enum.EFriendAddSourceData.GUILD, nil, Enum.EMenuType.GuildMember)
        end
    end
end

--- 此处为自动生成
function GuildInside_Organization:on_Btn_ClickArea_Clicked()
    local cachedGeometry = self.view.Btn_ClickArea:GetCachedGeometry()
    local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
    local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
        _G.GetContextObject(), cachedGeometry, localSize, nil, nil
    )
    Game.NewUIManager:OpenPanel(UIPanelConfig.GuildInside_StructTips_Panel, viewportPosition, Const.GUILD_ROLE.PRESIDENT)
end


--- 此处为自动生成
function GuildInside_Organization:on_Btn_President_ClickArea_Clicked()
    if self.bInAdjusting then
        self:SetSelectedRoleAndSlot(Const.GUILD_ROLE.PRESIDENT, 1, not self.presidentSelected)
        self:GetBelongPanel():SetConfirmBtnType()
    else
        self:OnClickHead(Const.GUILD_ROLE.PRESIDENT, 1)
    end
end

return GuildInside_Organization
