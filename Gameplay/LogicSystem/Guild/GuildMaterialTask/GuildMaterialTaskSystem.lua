---@class GuildMaterialTaskSystem : SystemBase
local GuildMaterialTaskSystem = DefineClass("GuildMaterialTaskSystem", SystemBase)

function GuildMaterialTaskSystem:onCtor()
    self.TaskList = {}
end

function GuildMaterialTaskSystem:onInit()
    self.model = kg_require("Gameplay.LogicSystem.Guild.GuildMaterialTask.GuildMaterialTaskModel").new()
    self.sender = kg_require("Gameplay.LogicSystem.Guild.GuildMaterialTask.GuildMaterialTaskSender").new()
end

function GuildMaterialTaskSystem:AfterPlayerInit()
    self:RequestRefreshHelpInfo()
end

function GuildMaterialTaskSystem:RequestRefreshHelpInfo()
    -- local chatList = Game.ChatSystem:GetChannelChatList(Enum.EChatChannelData["GUILD"])
    -- local helpList = {}
    -- for _, v in pairs(chatList) do
    --     if v[2] and v[2].messageType == Enum.EChatMessageType.GUILD_TASK_HELP then
    --         table.insert(helpList, v[2].chatArgs.guildtaskmaterialtask.arg)
    --     end
    -- end

    -- if next(helpList) then
    --     self.sender:ReqRefreshHelpInfo(helpList)
    -- end
end

function GuildMaterialTaskSystem:GetGuildMaterialTaskList()
    local taskInfo = self.model:GetGuildMaterialTaskInfo()
    table.clear(self.TaskList)
    for k, v in pairs(taskInfo) do
        table.insert(self.TaskList, {id = k, info = v})
    end
    return self.TaskList
end

function GuildMaterialTaskSystem:SendChatMessage(taskID)
    -- local attributeList = Game.me.roleplayCommonProp
    -- table.sort(attributeList, function(a, b) return a > b end)
    -- local contentList = {}
    -- local contentTable = Game.TableData.GetGuildMaterialTaskHelpDialogueDataTable()
    -- for _, v in ipairs(contentTable) do
    --     if v.Prop == attributeList[1] or v.Prop == attributeList[2] or #attributeList==0 then
    --         table.insert(contentList, v.Dialogue)
    --     end
    -- end
    -- local content = contentList[math.random(#contentList)]
    -- local taskInfo = self.model:GetGuildMaterialTaskInfo()
    -- local taskItemTable = Game.TableData.GetGuildMaterialTaskItemWeightDataRow(taskID)
    -- local chatArgs = {guildtaskmaterialtask = {id = taskID, arg = taskInfo[taskID].AskHelpID, itemid = taskItemTable.ItemID, itemnum = taskItemTable.ItemNum}}
    -- Game.ChatSystem:SendChatMessage(Enum.EChatChannelData["GUILD"], content, Enum.EChatMessageType.GUILD_TASK_HELP, Enum.ChatFunctionType.GUILD_TASK_HELP,
    --         chatArgs, true, false)
end

function GuildMaterialTaskSystem:OnMsgHelpInfoUpdate(guildMaterialTaskAskInfoList)
    for _, v in pairs(guildMaterialTaskAskInfoList) do
        self.model.GuildMaterialHelpInfo[v.AskHelpID] = {helpPlayerID = v.HelpUsr}
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_MATERIAL_TASK_HELP_REFRESH)
end

return GuildMaterialTaskSystem

