---@class WBP_ComBtnBackNewView : WBP_ComBtnBackNew_C
---@field public WidgetRoot WBP_ComBtnBackNew_C
---@field public Btn_Back UC7Button
---@field public Text_Back UTextBlock
---@field public Btn_Info UC7Button
---@field public Icon UImage
---@field public Ani_Press UWidgetAnimation
---@field public TitleName_lua string
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public BndEvt__WBP_ComBtnBackNew_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public GetBrush_0 fun(self:self):FSlateBrush


---@class WBP_ComPanelView : WBP_ComPanel_C
---@field public WidgetRoot WBP_ComPanel_C
---@field public NS_BackGround UNamedSlot
---@field public Money UNamedSlot
---@field public HorizontalBox UHorizontalBox
---@field public NS_TabList UNamedSlot
---@field public NS_TabListLv1 UNamedSlot
---@field public NS_TabListLv2 UNamedSlot
---@field public NS_ComPanel UNamedSlot
---@field public WBP_ComBtnBack WBP_ComBtnBackNewView
---@field public Title Text string
---@field public TabType number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetTabList fun(self:self,TabType:number):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public Btn_Com UC7Button
---@field public OutOverlay UOverlay
---@field public Text_Com UTextBlock
---@field public Text_Time UTextBlock
---@field public Image UC7Image
---@field public Ani_Press UWidgetAnimation
---@field public Ani_Tower UWidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_GuildMissionSettlementView : WBP_GuildMissionSettlement_C
---@field public WidgetRoot WBP_GuildMissionSettlement_C
---@field public text_num UTextBlock
---@field public text_max UTextBlock
---@field public ProgressBar UProgressBar
---@field public HB_Dot UHorizontalBox
---@field public HB_Reward UHorizontalBox
---@field public WBP_ComBtn WBP_ComBtnView


---@class WBP_GuildMissionView : WBP_GuildMission_C
---@field public WidgetRoot WBP_GuildMission_C
---@field public WBP_ComPanel WBP_ComPanelView
---@field public WB UHorizontalBox
---@field public WBP_GuildMissionSettlement WBP_GuildMissionSettlementView

---@class P_GuildMissionView : WBP_GuildMissionView
---@field public controller P_GuildMission
local P_GuildMissionView = DefineClass("P_GuildMissionView", UIView)
local ESlateVisibility = import("ESlateVisibility")


function P_GuildMissionView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPanel.WBP_ComBtnBack.Btn_Back, "OnClick_WBP_ComPanel_WBP_ComBtnBack_Btn_Back")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPanel.WBP_ComBtnBack.Btn_Info, "OnClick_WBP_ComPanel_WBP_ComBtnBack_Btn_Info")
    self.WBP_ComPanel.WBP_ComBtnBack.Btn_Info:SetVisibility(ESlateVisibility.Visible)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_GuildMissionSettlement.WBP_ComBtn.Btn_Com, "OnClick_WBP_GuildMissionSettlement_WBP_ComBtn_Btn_Com")

end

return P_GuildMissionView
