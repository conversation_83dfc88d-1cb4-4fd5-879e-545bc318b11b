---@class WBP_HUDComBarView : WBP_HUDComBar_C
---@field public WidgetRoot WBP_HUDComBar_C
---@field public PreviewBar UProgressBar
---@field public ProgressBar UProgressBar
---@field public Ani_HighLight UWidgetAnimation


---@class WBP_GuildPuzzleItemView : WBP_GuildPuzzleItem_C
---@field public WidgetRoot WBP_GuildPuzzleItem_C
---@field public VX_Particle01 UNiagaraSystemWidget
---@field public VX_Particle02 UNiagaraSystemWidget
---@field public Choice_Img_Icon UC7Image
---@field public Text UTextBlock
---@field public WidgetSwitcher UWidgetSwitcher
---@field public Ani_Select UWidgetAnimation
---@field public Ani_Right01 UWidgetAnimation
---@field public Ani_Error UWidgetAnimation
---@field public Ani_Right02 UWidgetAnimation
---@field public IsLight boolean
---@field public State number
---@field public ColorGreenHight FLinearColor
---@field public ColorRedHight FLinearColor
---@field public ColorPurpleHight FLinearColor
---@field public ColorYellowHight FLinearColor
---@field public ColorGreenTop FLinearColor
---@field public ColorRedTop FLinearColor
---@field public ColorPurpleTop FLinearColor
---@field public ColorYellowTop FLinearColor
---@field public ColorYellowBottom FLinearColor
---@field public ColorGreenBottom FLinearColor
---@field public ColorRedBottom FLinearColor
---@field public ColorPurpleBottom FLinearColor
---@field public Event_UI_Style fun(self:self,State:number,bLight:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_GuildPuzzleStarView : WBP_GuildPuzzleStar_C
---@field public WidgetRoot WBP_GuildPuzzleStar_C
---@field public Text UTextBlock
---@field public Ani_Fadein UWidgetAnimation


---@class HUD_GuildPuzzleTopicView : HUD_GuildPuzzleTopic_C
---@field public WidgetRoot HUD_GuildPuzzleTopic_C
---@field public WidgetSwitcher UWidgetSwitcher
---@field public VX_Charater_Spine USpineWidget
---@field public Question_Title UTextBlock
---@field public WBP_ComBarNew WBP_HUDComBarView
---@field public WBP_GuildPuzzleItem_A WBP_GuildPuzzleItemView
---@field public WBP_GuildPuzzleItem_B WBP_GuildPuzzleItemView
---@field public WBP_GuildPuzzleItem_C WBP_GuildPuzzleItemView
---@field public WBP_GuildPuzzleItem_D WBP_GuildPuzzleItemView
---@field public WBP_GuildPuzzleStar WBP_GuildPuzzleStarView
---@field public Ani_Fadein UWidgetAnimation
---@field public Ani_Fadein_0 UWidgetAnimation
---@field public Tick fun(self:self,MyGeometry:FGeometry,InDeltaTime:number):void
---@field public Construct fun(self:self):void


---@class P_HUDGuildQuestionBoardView : HUD_GuildPuzzleTopicView
---@field public controller P_HUDGuildQuestionBoard
local P_HUDGuildQuestionBoardView = DefineClass("P_HUDGuildQuestionBoardView", UIView)

function P_HUDGuildQuestionBoardView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

end

return P_HUDGuildQuestionBoardView
