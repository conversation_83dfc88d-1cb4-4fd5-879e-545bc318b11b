local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local GuildComTabBtnInsideItem = kg_require("Gameplay.LogicSystem.Guild.GuildCom.GuildComTabBtnInsideItem")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildComTabBtnInsideList : UIComponent
---@field view GuildComTabBtnInsideListBlueprint
local GuildComTabBtnInsideList = DefineClass("GuildComTabBtnInsideList", UIComponent)

GuildComTabBtnInsideList.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildComTabBtnInsideList:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildComTabBtnInsideList:InitUIData()
    self.tabData = {}
    self.selectedIndex = 1
    self.hasCallRedPointCB = {}
    self.redPointCallback = nil
    ---@public
    ---监听Item选中
    ---@type LuaMulticastDelegate<fun(index:number)>
    self.onItemSelected = LuaMulticastDelegate.new()
    self.canSel = true
end

--- UI组件初始化，此处为自动生成
function GuildComTabBtnInsideList:InitUIComponent()
    self.listView = self:CreateComponent(self.view.ComLL_Tab_lua, UIBaseAdapter, "GuildComTabBtnInsideList")
    self.tabListView = self.listView:CreateBaseList(GuildComTabBtnInsideItem, ComList, "tabListView", false, nil, true, 3)
end

---UI事件在这里注册，此处为自动生成
function GuildComTabBtnInsideList:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildComTabBtnInsideList:InitUIView()
end

---面板打开的时候触发
function GuildComTabBtnInsideList:OnRefresh(...)
end

function GuildComTabBtnInsideList:SetCanSel(bCanSel)
    self.canSel = bCanSel
end

function GuildComTabBtnInsideList:CanSel_tabListView()
    return self.canSel
end

function GuildComTabBtnInsideList:OnRefresh_tabListView(widget, index, bSelected)
    widget:RefreshData(
        index, self.tabData[index], index == self.selectedIndex,
        self.tabData[self.selectedIndex].needBg, self, self.OnClickTab
    )
    if not self.hasCallRedPointCB[index] then
        self.hasCallRedPointCB[index] = true
        if self.redPointCallback then
            self.redPointCallback(widget, index)
        end
    end
end

function GuildComTabBtnInsideList:OnClickTab(index)
    if not self.canSel then
        return
    end
    self.selectedIndex = index
    self.onItemSelected:Broadcast(index)
    for i = 1, #self.tabData do
        self.tabListView:RefreshCell(i)
    end
    -- self.tabListView:SetData(#self.tabData)
end

function GuildComTabBtnInsideList:RefreshData(tabData, redPointCallback, defaultIndex)
    self.selectedIndex = defaultIndex or 1
    self.tabData = tabData
    self.hasCallRedPointCB = {}
    self.redPointCallback = redPointCallback
    self.tabListView:SetData(#self.tabData)
end

function GuildComTabBtnInsideList:Sel(index)
    self:OnClickTab(index)
end

function GuildComTabBtnInsideList:GetSelectedIndex()
    return self.selectedIndex
end

return GuildComTabBtnInsideList
