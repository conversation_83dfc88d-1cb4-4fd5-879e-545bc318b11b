local ESlateVisibility = import("ESlateVisibility")
local const = kg_require("Shared.Const")
---@class GuildComTabItem : UIComponent

local GuildComTabItem = DefineClass("GuildComTabItem", UIComponent)

GuildComTabItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildComTabItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildComTabItem:InitUIData()
    self.index = 1
end

--- UI组件初始化，此处为自动生成
function GuildComTabItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildComTabItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildComTabItem:InitUIView()
end

---面板打开的时候触发
function GuildComTabItem:OnRefresh(...)
end

function GuildComTabItem:RefreshData(data, bSelected)
    self.View.TB_Title:SetText(data.tabName)
    self.View.WidgetRoot:Event_UI_Style(data.tabIconType, bSelected)
    self.View.TB_Word:SetText(data.englishName)
end

return GuildComTabItem
