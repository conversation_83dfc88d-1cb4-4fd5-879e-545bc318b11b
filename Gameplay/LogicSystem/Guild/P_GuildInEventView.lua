---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button UC7Button
---@field public Ani_Fadein UWidgetAnimation
---@field public Ani_Press UWidgetAnimation
---@field public IconBrush FSlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):FSlateBrush


---@class WBP_ComTitleView : WBP_ComTitle_C
---@field public WidgetRoot WBP_ComTitle_C
---@field public Text_Title UTextBlock
---@field public Image_Line UImage
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView
---@field public Ani_In UWidgetAnimation
---@field public TitleText string
---@field public OnClicked MulticastDelegate
---@field public BndEvt__WBP_ComTitle_WBP_ComBtnClose_lua_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature fun(self:self):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetText fun(self:self,InText:string):void
---@field public InnerSetText fun(self:self):void


---@class WBP_ComPopupLView : WBP_ComPopupL_C
---@field public WidgetRoot WBP_ComPopupL_C
---@field public WBP_ComPopupTitle WBP_ComTitleView
---@field public Anim_in UWidgetAnimation
---@field public Anim_Out UWidgetAnimation
---@field public Title string
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_GuildEventView : WBP_GuildEvent_C
---@field public WidgetRoot WBP_GuildEvent_C
---@field public WBP_ComPopupFrameBigNav WBP_ComPopupLView
---@field public TB_Time UTextBlock
---@field public TB_Event UTextBlock
---@field public GuildEvents UListViewEx


---@class P_GuildInEventView : WBP_GuildEventView
---@field public controller P_GuildInEvent
local P_GuildInEventView = DefineClass("P_GuildInEventView", UIView)

function P_GuildInEventView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPopupFrameBigNav.WBP_ComPopupTitle.WBP_ComBtnClose.Button, "OnClick_WBP_ComPopupFrameBigNav_WBP_ComPopupTitle_WBP_ComBtnClose_Button")

end

return P_GuildInEventView
