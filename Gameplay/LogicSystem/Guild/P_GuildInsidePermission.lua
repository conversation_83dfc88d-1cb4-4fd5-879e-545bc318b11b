---@class P_GuildInsidePermission : UIController
--- @field public View WBP_GuildPermissionView
local P_GuildInsidePermission = DefineClass("P_GuildInsidePermission", UIController)
local StringConst = require("Data.Config.StringConst.StringConst")
local GuildConst = kg_require "Gameplay.LogicSystem.Guild.GuildConst"
local const = kg_require("Shared.Const")

P_GuildInsidePermission.rolesRightsMap = {
    const.GUILD_ROLE.PRESIDENT,
    const.GUILD_ROLE.VICE_PRESIDENT,
    const.GUILD_ROLE.BABY,
    const.GUILD_ROLE.DIRECTOR,
    const.GUILD_ROLE.GROUP_LEADER,
    const.GUILD_ROLE.MEMBER,
    const.GUILD_ROLE.APPRENTICE,
}

P_GuildInsidePermission.basicRights = {
    const.GUILD_RIGHT.POSITION_SET,
    const.GUILD_RIGHT.INFO_SET,
    const.GUILD_RIGHT.BABY,
    const.GUILD_RIGHT.CONSTRUCTION,
    --const.GUILD_RIGHT.ACTIVITY,
    --const.GUILD_RIGHT.SKILL,
}

P_GuildInsidePermission.managerRights = {
    --const.GUILD_RIGHT.CONSTRUCTION,
    const.GUILD_RIGHT.MEMBER,
    const.GUILD_RIGHT.APPRENTICE,
    const.GUILD_RIGHT.AUTO_RECEIVE,
    --const.GUILD_RIGHT.MERGE,
    --const.GUILD_RIGHT.MODIFY_NAME,
    --const.GUILD_RIGHT.SET_ELITE,
    const.GUILD_RIGHT.KICKOUT,
    --const.GUILD_RIGHT.CHANGE_GUILD_BABY_NAME,
    const.GUILD_RIGHT.SEND_GUILD_MAIL,
    const.GUILD_RIGHT.GROUP_NAME,
    const.GUILD_RIGHT.GROUP_CONTROL,
    --const.GUILD_RIGHT.CHANGE_GUILD_BACKGROUND,
}

P_GuildInsidePermission.eventBindMap = {
    [EEventTypesV2.RECEIVE_GUILD_RIGHTS] = "OnReceiveGuildRights",
}

function P_GuildInsidePermission:ReqRefreshRightsInfo()
    local currentSel = self.roleListView:GetSelectedIndex()
    if P_GuildInsidePermission.rolesRightsMap[currentSel] then
        Game.GuildSystem.sender:getCustomRoleRights(P_GuildInsidePermission.rolesRightsMap[currentSel])
    end
end

function P_GuildInsidePermission:OnCreate()
    self.selectedRole = nil -- 当前选中职位
    self.curRights = {}     -- 当前选中权限
    self.curRoleID = 1      -- 当前选中职位ID
    -- 职位列表
    self.roleListView = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.Roles.WBP_ComList, nil, "roleList")
    -- 基础权限列表
    self.basicPermissionView = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.WB_BasicRights)
    -- 管理权限列表
    self.managerPermissionView = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.WB_ManagerRights)
    self.basicPermissionView:AddUIListener(EUIEventTypes.CheckStateChanged, "CheckBox", "OnClickBasicRights")
    self.managerPermissionView:AddUIListener(EUIEventTypes.CheckStateChanged, "CheckBox", "OnclickManagerRights")

    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComPopupFrameBigNav.WBP_ComPopupTitle.WBP_ComBtnClose.Button,
        self.OnCLickClose)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_BtnSave.Btn_Com, self.OnClickSave)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_BtnResetDefault.Btn_Com, self.OnClickReset)

    self.View.TB_BasicTitle:SetText(StringConst.Get("GUILD_RIGHT_BASE"))
    self.View.TB_ManagerTitle:SetText(StringConst.Get("GUILD_RIGHT_MANAGE"))
end

function P_GuildInsidePermission:OnRefresh()
    self.roleListView:SetData(#P_GuildInsidePermission.rolesRightsMap, 1)
    self.roleListView:Sel(1)
    table.clear(self.curRights)
    self.View.WBP_ComPopupFrameBigNav.WBP_ComPopupTitle.Text_Title:SetText(StringConst.Get("GUILD_PERMISSION_NAME"))
    self.View.WBP_BtnSave.Text_Com:SetText(StringConst.Get("GUILD_SAVE"))
    self.View.WBP_BtnResetDefault.Text_Com:SetText(StringConst.Get("GUILD_RESTORE_DEFAULT_SETTINGS"))
end

function P_GuildInsidePermission:OnReceiveGuildRights(Role, RightsMap)
    if Role == P_GuildInsidePermission.rolesRightsMap[self.roleListView:GetSelectedIndex()] then
        self.curRights = DeepCopy(Game.GuildSystem.model.CustomRoleRights[Role] or {})
        self:RefreshRightsData()
    end
end

function P_GuildInsidePermission:RefreshRightsData()
    self.basicPermissionView:SetData(#P_GuildInsidePermission.basicRights, nil, true)
    self.managerPermissionView:SetData(#P_GuildInsidePermission.managerRights, nil, true)
end

---@param widget WBP_ComCheckBox_C
function P_GuildInsidePermission:RefreshRightItem(widget, rightID, roleID)
    if (self.curRights[rightID] ~= nil and self.curRights[rightID] or Game.GuildSystem:CheckHasRight(roleID, rightID)) then
        self.curRights[rightID] = true
        widget.CheckBox:SetIsChecked(true)
    else
        self.curRights[rightID] = false
        widget.CheckBox:SetIsChecked(false)
    end

    if Game.GuildSystem:CanEditRight(roleID, rightID) then
        widget.TB_Name:SetRenderOpacity(1)
        widget:SetUndetermined(false)
    else
        widget:SetUndetermined(true)
        widget.TB_Name:SetRenderOpacity(0.6)
    end

    widget.TB_Name:SetText(GuildConst.GUILD_RIGHT_NAME[rightID])
end

function P_GuildInsidePermission:OnRefresh_WB_BasicRights(widget, index, selected)
    local rightID = P_GuildInsidePermission.basicRights[index]
    local roleID = P_GuildInsidePermission.rolesRightsMap[self.roleListView:GetSelectedIndex()]
    self:RefreshRightItem(widget, rightID, roleID)
end

function P_GuildInsidePermission:OnRefresh_WB_ManagerRights(widget, index, selected)
    local rightID = P_GuildInsidePermission.managerRights[index]
    local roleID = P_GuildInsidePermission.rolesRightsMap[self.roleListView:GetSelectedIndex()]
    self:RefreshRightItem(widget, rightID, roleID)
end

function P_GuildInsidePermission:OnRefresh_roleList(widget, index, selected)
    local roleID = P_GuildInsidePermission.rolesRightsMap[index]
    widget.Text_Name:SetText(Game.GuildSystem:RoleIDToRoleName(roleID))
    self.curRights = DeepCopy(Game.GuildSystem.model.CustomRoleRights[self.curRoleID] or {})
    if selected then
        widget:SetSelected(true, false)
        if self.selectedRole ~= roleID then
            self.selectedRole = roleID
            -- self:ReqRefreshRightsInfo()
            -- self.curRights = DeepCopy(Game.GuildSystem.model.CustomRoleRights[self.curRoleID] or {})
            -- self:RefreshRightsData()
            self:ReqRefreshRightsInfo()
        end
    else
        widget:SetSelected(false, false)
    end
end

function P_GuildInsidePermission:OnClickBasicRights(bIsChecked, index)
    local rightID = P_GuildInsidePermission.basicRights[index]
    if rightID then
        local roleID = P_GuildInsidePermission.rolesRightsMap[self.roleListView:GetSelectedIndex()]
        
        -- local bHasRight = Game.GuildSystem:CheckHasRight(roleID, rightID, { [roleID] = self.curRights })
        if Game.GuildSystem:CanEditRight(roleID, rightID) then
            local grdd = Game.TableData.GetGuildRightDataRow(roleID)
            local rightType = grdd and grdd.Right and grdd.Right[rightID]
            -- if rightType == const.GUILD_RIGHT_VARIABLE_TRUE and bHasRight and bIsChecked then
            --     self.curRights[rightID] = true
            -- elseif rightType == const.GUILD_RIGHT_VARIABLE_FALSE and not bHasRight and bIsChecked then
            --     self.curRights[rightID] = true
            -- else
            --     self.curRights[rightID] = false
            -- end
            if rightType then
                self.curRights[rightID] = bIsChecked
            end
        else
            local widget = self.basicPermissionView:GetRendererAt(index)
            if widget then
                widget.CheckBox:SetIsChecked(not bIsChecked)
            end
        end
    end
end

function P_GuildInsidePermission:OnclickManagerRights(bIsChecked, index)
    local rightID = P_GuildInsidePermission.managerRights[index]
    if rightID then
        local roleID    = P_GuildInsidePermission.rolesRightsMap[self.roleListView:GetSelectedIndex()]
        -- local bHasRight = Game.GuildSystem:CheckHasRight(roleID, rightID, { [roleID] = self.curRights })
        if Game.GuildSystem:CanEditRight(roleID, rightID) then
            local grdd = Game.TableData.GetGuildRightDataRow(roleID)
            local rightType = grdd and grdd.Right and grdd.Right[rightID]
            -- if rightType == const.GUILD_RIGHT_VARIABLE_TRUE and bHasRight and bIsChecked then
            --     self.curRights[rightID] = true
            -- elseif rightType == const.GUILD_RIGHT_VARIABLE_FALSE and not bHasRight and bIsChecked then
            --     self.curRights[rightID] = true
            if rightType then
                self.curRights[rightID] = bIsChecked
            end
        else
            local widget = self.managerPermissionView:GetRendererAt(index)
            if widget then
                widget.CheckBox:SetIsChecked(not bIsChecked)
            end
        end
    end
end

function P_GuildInsidePermission:OnClick_list_tab(r, index)
    self:ReqRefreshRightsInfo()
end

function P_GuildInsidePermission:OnClickSave()
    local Selected = self.roleListView:GetSelectedIndex()
    self.curRoleID = P_GuildInsidePermission.rolesRightsMap[Selected]
    Game.GuildSystem:setCustomRoleRights(self.curRoleID, self.curRights)
    self:CloseSelf()
end

function P_GuildInsidePermission:OnClickReset()
    Game.GuildSystem.sender:resetCustomRoleRights()
    Game.GuildSystem:ClearCustomRoleRights()
    self:CloseSelf()
end

function P_GuildInsidePermission:OnClick_Roles(widget, index)
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common_Tab, true)
end

function P_GuildInsidePermission:OnCLickClose()
    self:CloseSelf()
end

return P_GuildInsidePermission
