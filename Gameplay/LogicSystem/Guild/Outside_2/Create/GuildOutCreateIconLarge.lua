local ESlateVisibility = import("ESlateVisibility")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildOutCreateIconLarge : UIComponent
---@field view GuildOutCreateIconLargeBlueprint
local GuildOutCreateIconLarge = DefineClass("GuildOutCreateIconLarge", UIComponent)

GuildOutCreateIconLarge.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildOutCreateIconLarge:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildOutCreateIconLarge:InitUIData()
    self.nameTextIndex = 1
    self.nameTextData = {}
    self.nameIconIndex = nil
    self.nameIconData = {}

    self.bgIndex = 1
    self.bgData = {}

    for id, data in ksbcpairs(Game.TableData.GetGuildBadgeFrameDataTable()) do
        if data.IconType == Enum.EGuildConstIntData.BADGE_TYPE_ICON then
            table.insert(self.nameIconData, id)
        else
            table.insert(self.bgData, id)
        end
    end
end

--- UI组件初始化，此处为自动生成
function GuildOutCreateIconLarge:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildOutCreateIconLarge:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildOutCreateIconLarge:InitUIView()
end

---面板打开的时候触发
function GuildOutCreateIconLarge:OnRefresh(...)
end

function GuildOutCreateIconLarge:RefreshData(name, nameId, bgId)
    self.name = name
    self.nameTextData = {}
    self.nameTextIndex = nil
    self.nameIconIndex = nil
    for i = 1, utf8.len(name) do
        if i > Game.TableData.GetConstDataRow("GUILD_NAME_WORD_NUM_UPPER_LIMIT") then
            break
        end
        table.insert(self.nameTextData, utf8.sub(name, i, i + 1))
    end

    if not nameId then
        self.nameTextIndex = 1
    elseif 1 <= nameId and nameId <= #self.nameTextData then
        self.nameTextIndex = nameId
    else
        self.nameTextIndex = 1
        --self.nameIconIndex = table.arrayIndexOf(self.nameIconData, nameId)
        --if not self.nameIconIndex then
        --    self.nameTextIndex = 1
        --end
    end

    self.bgIndex = table.arrayIndexOf(self.bgData, bgId) or 1

    self:RefreshBadgeLarge()
end

function GuildOutCreateIconLarge:RefreshBadgeLarge()
    if self.nameTextIndex then
        self.view.Text_Title_lua:SetText(self.nameTextData[self.nameTextIndex] or "")
        self.view.Text_Title_lua_vx:SetText(self.nameTextData[self.nameTextIndex] or "")
        self.view.Text_Title_lua_vx_2:SetText(self.nameTextData[self.nameTextIndex] or "")
        self.view.Text_Title_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Img_Icon_lua:SetVisibility(ESlateVisibility.Collapsed)
    else
        self:SetImage(
            self.view.Img_Icon_lua,
            Game.TableData.GetGuildBadgeFrameDataRow(self.nameIconData[self.nameIconIndex]).Icon
        )
        self.view.Text_Title_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Img_Icon_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    self:SetImage(self.view.Img_BgIcon_lua, Game.TableData.GetGuildBadgeFrameDataRow(self.bgData[self.bgIndex]).Icon)
end


return GuildOutCreateIconLarge
