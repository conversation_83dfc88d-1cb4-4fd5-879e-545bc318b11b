local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local stringConst = require "Data.Config.StringConst.StringConst"
local GuildOutCreateIconLarge = kg_require("Gameplay.LogicSystem.Guild.Outside_2.Create.GuildOutCreateIconLarge")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GuildOutCreatePopupPanel : UIPanel
---@field view GuildOutCreatePopupPanelBlueprint
local GuildOutCreatePopupPanel = DefineClass("GuildOutCreatePopupPanel", UIPanel)
local GuildOutCreateBadgeItem = kg_require("Gameplay.LogicSystem.Guild.Outside_2.Create.GuildOutCreateBadgeItem")

GuildOutCreatePopupPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildOutCreatePopupPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildOutCreatePopupPanel:InitUIData()
    self.name = ""
    
    self.nameTextIndex = 1
    self.nameTextData = {}
    self.nameIconIndex = nil
    self.nameIconData = {}
    
    self.bgIndex = 1
    self.bgData = {}

    for id, data in ksbcpairs(Game.TableData.GetGuildBadgeFrameDataTable()) do
        if data.IsShowInCreate then
            if data.IconType == Enum.EGuildConstIntData.BADGE_TYPE_ICON then
                table.insert(self.nameIconData, id)
            else
                table.insert(self.bgData, {
                    type = id,
                    showType = GuildOutCreateBadgeItem.BadgeShowType.ICON,
                })
            end
        end
    end
end

--- UI组件初始化，此处为自动生成
function GuildOutCreatePopupPanel:InitUIComponent()
    ---@type UIComButton
    self.WBP_BtnConfirmCom = self:CreateComponent(self.view.WBP_BtnConfirm, UIComButton)
    ---@type UIListView childScript: GuildOutCreateBadgeItem
    self.ComLLBgCom = self:CreateComponent(self.view.ComLLBg, UIListView)
    ---@type UIListView childScript: GuildOutCreateBadgeItem
    self.ComLLIconCom = self:CreateComponent(self.view.ComLLIcon, UIListView)
    ---@type UIListView childScript: GuildOutCreateBadgeItem
    self.ComLLNameCom = self:CreateComponent(self.view.ComLLName, UIListView)
    ---@type GuildOutCreateIconLarge
    self.WBP_BadgeLargeCom = self:CreateComponent(self.view.WBP_BadgeLarge, GuildOutCreateIconLarge)
    ---@type UIComBoxFrame
    self.WBP_ComPopupMCom = self:CreateComponent(self.view.WBP_ComPopupM, UIComBoxFrame)
end

---UI事件在这里注册，此处为自动生成
function GuildOutCreatePopupPanel:InitUIEvent()
    self:AddUIEvent(self.WBP_BtnConfirmCom.onClickEvent, "on_WBP_BtnConfirmCom_ClickEvent")
    self:AddUIEvent(self.ComLLNameCom.onItemSelected, "on_ComLLNameCom_ItemSelected")
    self:AddUIEvent(self.ComLLBgCom.onItemSelected, "on_ComLLBgCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildOutCreatePopupPanel:InitUIView()
    self.WBP_BtnConfirmCom:Refresh("确认")
    self.view.TB_BadgeBg:SetText("以下图案将作为<T_Brown>底图</>")
    self.WBP_ComPopupMCom:Refresh(stringConst.Get("CLUB_BADGE"))
end

---面板打开的时候触发
function GuildOutCreatePopupPanel:OnRefresh(name, nameId, bgId)
    self:InitData(name, nameId, bgId)
    self:RefreshSelectList()
    self:RefreshBadgeLarge()
end

function GuildOutCreatePopupPanel:InitData(name, nameId, bgId)
    self.name = name
    self.nameTextData = {}
    self.nameTextIndex = nil
    for i = 1, utf8.len(name) do
        if i > Game.TableData.GetConstDataRow("GUILD_NAME_WORD_NUM_UPPER_LIMIT") then
            break
        end
        table.insert(self.nameTextData, {
            type = utf8.sub(name, i, i + 1),
            showType = GuildOutCreateBadgeItem.BadgeShowType.TEXT,
        })
    end
    
    if not nameId then
        self.nameTextIndex = 1
    elseif 1 <= nameId and nameId <= #self.nameTextData then
        self.nameTextIndex = nameId
    else
        self.nameTextIndex = 1
    end
    
    self.bgIndex = table.arrayIndexOf(self.bgData, bgId) or 1
end

function GuildOutCreatePopupPanel:RefreshSelectList()
    self.ComLLNameCom:Refresh(self.nameTextData)
    self.ComLLBgCom:Refresh(self.bgData)
    self.ComLLNameCom:SetSelectedItemByIndex(self.nameTextIndex, true)
    self.ComLLBgCom:SetSelectedItemByIndex(self.bgIndex, true)
end

function GuildOutCreatePopupPanel:RefreshBadgeLarge()
    self:PlayAnimation(self.view.WBP_BadgeLarge.Ani_Text_new, nil, self.view.WBP_BadgeLarge)
    self.WBP_BadgeLargeCom:RefreshData(self.name, self.nameTextIndex, self.bgIndex)
    self:SetImage(
        self.view.WBP_BadgeLarge.Img_BgIcon_lua,
        Game.TableData.GetGuildBadgeFrameDataRow(self.bgData[self.bgIndex].type).Icon
    )
end

--- 此处为自动生成
function GuildOutCreatePopupPanel:on_WBP_BtnConfirmCom_ClickEvent()
    local bgId = self.bgData[self.bgIndex].type
    Game.GlobalEventSystem:Publish(EEventTypesV2.GUILD_ON_CONFIRM_SELECT_BADGE, self.nameTextIndex, bgId)
    self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function GuildOutCreatePopupPanel:on_ComLLNameCom_ItemSelected(index, data)
    self.nameTextIndex = index
    self:RefreshBadgeLarge()
end

--- 此处为自动生成
---@param index number
---@param data table
function GuildOutCreatePopupPanel:on_ComLLBgCom_ItemSelected(index, data)
    self.bgIndex = index
    self:RefreshBadgeLarge()
end

return GuildOutCreatePopupPanel
