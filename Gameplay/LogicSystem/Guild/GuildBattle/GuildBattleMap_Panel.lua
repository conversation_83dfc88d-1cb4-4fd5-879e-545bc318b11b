local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComSimpleTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList")
local GuildBattleResource = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.GuildBattleResource")
local GuildBattleGroupBtn = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.GuildBattleGroupBtn")
local GuildBattleTagLineDrag = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.GuildBattleTagLineDrag")
local GuildBattleFocusDisplay = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.GuildBattleFocusDisplay")
local GuildBattleMapBoard = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.GuildBattleMapBoard")
local GuildBattleSignBtn = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.GuildBattleSignBtn")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

local StringConst = kg_require "Data.Config.StringConst.StringConst"
local Const = kg_require("Shared.Const")
local USlateBlueprintLibrary = import("SlateBlueprintLibrary")
local UKismetInputLibrary = import("KismetInputLibrary")
local ESlateVisibility = import("ESlateVisibility")

---@class GuildBattleMap_Panel : UIPanel
---@field view GuildBattleMap_PanelBlueprint
local GuildBattleMap_Panel = DefineClass("GuildBattleMap_Panel", UIPanel)

GuildBattleMap_Panel.eventBindMap = {
	[EEventTypesV2.ON_GUILD_LEAGUE_RESOURCE_CHANGED] = "OnResourceChange", --公会资源发生改变
	[EEventTypesV2.ON_GUILD_LEAGUE_TAG_TEXT_CHANGE] = "OnTagTextChange", --公会标点文字发生改变
	--[EEventTypesV2.ON_JOIN_GROUP] = "TryRequestGroupInfo", --加入团队请求下团队信息
	--[EEventTypesV2.ON_QUIT_GROUP] = "TryRequestGroupInfo", --离开团队请求下团队信息
	--[EEventTypesV2.GROUP_LEADER_CHANGED] = "TryRequestGroupInfo", --团队信息发生变化
	[EEventTypesV2.ON_GUILD_LEAGUE_RET_REQUEST_GROUP_INFO] = "OnGetAllGroupInfo", --得到团队信息后，刷新团队按钮
	[EEventTypesV2.ON_GUILD_LEAGUE_MAP_TAG_START_LONG_PRESS] = "OnStartLongPressMapTag", --长按地图tag
	[EEventTypesV2.ON_GUILD_LEAGUE_MAP_TAG_END_LONG_PRESS] = "OnEndLongPressMapTag", --长按地图tag结束
	[EEventTypesV2.ON_GUILD_LEAGUE_SET_STAGE] = "OnStageChanged", --阶段改变
	[EEventTypesV2.RECEIVE_GUILD_INFO] = "OnRefresh", --接收到公会信息,重新刷一遍
	[EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_SELECTED] = "OnStrategySelected", --选中策略，刷新其他策略的状态
	[EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_UNSELECTED] = "OnStrategySelected", --取消选中策略，刷新其所有策略的状态
	[EEventTypesV2.ON_GUILD_LEAGUE_RET_USE_STRATEGY_ID] = "OnUseStrategyID",
	[EEventTypesV2.ON_GUILD_LEAGUE_RET_REMOVE_FLAG_MARK] = "OnRemoveFlagID", --移除标点时，刷新标记点
	[EEventTypesV2.ON_GUILD_LEAGUE_RET_ADD_FLAG_MARK] = "OnAddFlagID", --添加标点时，刷新标记点
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleMap_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleMap_Panel:InitUIData()
	--当前选中的标点索引，包括快速标点和标点tab的标点
	self.SelectedTagIndex = {
		quickTagIndex = 0,
		normalTagIndex = 0,
	}
	-- 快捷指令索引
	self.QuickTagMap ={
		Const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_ATTACK,
		Const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_RETREAT,
		Const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_GATHER,
		Const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_WARNING,
	}
	
	self.tagData = {} -- 标点数据
	
	self.groupData = {} -- 团队数据
	self.SelectedGroupTabList = {} -- 当前选中的团队Tab列表
	self.topTabIndexSelected = 0 -- 当前选中的上方Tab索引
	
	
	self.numOfSelectedGroup = 0 -- 当前选中的团队数量
	self.numOfGroup = 0 -- 团队数量
	
	self.currentLongPressTagTaskID = nil -- 当前长按的标记点taskID
	self.mouseScreenPos = nil -- 鼠标屏幕位置
end

--- UI组件初始化，此处为自动生成
function GuildBattleMap_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIListView
    self.KGTileView_DirectBtnCom = self:CreateComponent(self.view.KGTileView_DirectBtn, UIListView)
    ---@type UIComSimpleTabList
    self.WBP_ComTabHor_luaCom = self:CreateComponent(self.view.WBP_ComTabHor_lua, UIComSimpleTabList)
    ---@type GuildBattleResource
    self.WBP_GuildBattleResourceTotal_luaCom = self:CreateComponent(self.view.WBP_GuildBattleResourceTotal_lua, GuildBattleResource)
    ---@type GuildBattleResource
    self.WBP_GuildBattleResource_luaCom = self:CreateComponent(self.view.WBP_GuildBattleResource_lua, GuildBattleResource)
    ---@type GuildBattleGroupBtn
    self.WBP_ComTabR_Tab1_lua_5Com = self:CreateComponent(self.view.WBP_ComTabR_Tab1_lua_5, GuildBattleGroupBtn)
    ---@type GuildBattleGroupBtn
    self.WBP_ComTabR_Tab1_lua_4Com = self:CreateComponent(self.view.WBP_ComTabR_Tab1_lua_4, GuildBattleGroupBtn)
    ---@type GuildBattleGroupBtn
    self.WBP_ComTabR_Tab1_lua_3Com = self:CreateComponent(self.view.WBP_ComTabR_Tab1_lua_3, GuildBattleGroupBtn)
    ---@type GuildBattleGroupBtn
    self.WBP_ComTabR_Tab1_lua_2Com = self:CreateComponent(self.view.WBP_ComTabR_Tab1_lua_2, GuildBattleGroupBtn)
    ---@type GuildBattleGroupBtn
    self.WBP_ComTabR_Tab1_lua_1Com = self:CreateComponent(self.view.WBP_ComTabR_Tab1_lua_1, GuildBattleGroupBtn)
    ---@type GuildBattleGroupBtn
    self.WBP_ComTabR_Tab1_luaCom = self:CreateComponent(self.view.WBP_ComTabR_Tab1_lua, GuildBattleGroupBtn)
    ---@type GuildBattleTagLineDrag
    self.WBP_GuildBattleTagLineDrag_luaCom = self:CreateComponent(self.view.WBP_GuildBattleTagLineDrag_lua, GuildBattleTagLineDrag)
    ---@type GuildBattleFocusDisplay
    self.WBP_GuildBattleFocusItem_luaCom = self:CreateComponent(self.view.WBP_GuildBattleFocusItem_lua, GuildBattleFocusDisplay)
    ---@type GuildBattleMapBoard
    self.WBP_GuildBattleMapItem_luaCom = self:CreateComponent(self.view.WBP_GuildBattleMapItem_lua, GuildBattleMapBoard)
    ---@type GuildBattleSignBtn
    self.WBP_GuildBattleSignBtn_4_luaCom = self:CreateComponent(self.view.WBP_GuildBattleSignBtn_4_lua, GuildBattleSignBtn)
    ---@type GuildBattleSignBtn
    self.WBP_GuildBattleSignBtn_3_luaCom = self:CreateComponent(self.view.WBP_GuildBattleSignBtn_3_lua, GuildBattleSignBtn)
    ---@type GuildBattleSignBtn
    self.WBP_GuildBattleSignBtn_2_luaCom = self:CreateComponent(self.view.WBP_GuildBattleSignBtn_2_lua, GuildBattleSignBtn)
    ---@type GuildBattleSignBtn
    self.WBP_GuildBattleSignBtn_1_luaCom = self:CreateComponent(self.view.WBP_GuildBattleSignBtn_1_lua, GuildBattleSignBtn)
    ---@type UIListView
    self.KGTileView_BattleTagBtnCom = self:CreateComponent(self.view.KGTileView_BattleTagBtn, UIListView)
end

---UI事件在这里注册，此处为自动生成
function GuildBattleMap_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComTabHor_luaCom.onItemSelected, "on_WBP_ComTabHor_luaCom_ItemSelected")
    self:AddUIEvent(self.view.Btn_CustomTag_lua.OnClicked, "on_Btn_CustomTag_lua_Clicked")
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
    self:AddUIEvent(self.view.WBP_MouseMoveMask_lua.Btn_ClickArea.OnClicked, "on_WBP_MouseMoveMask_luaBtn_ClickArea_Clicked")
    self:AddUIEvent(self.view.OnMouseMoveEvent, "on_WBP_GuildBattleMap_Panel_MouseMoveEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleMap_Panel:InitUIView()
	
	-- 快捷指令控件列表
	self.commandQuickWidgetList = {
		self.WBP_GuildBattleSignBtn_1_luaCom,
		self.WBP_GuildBattleSignBtn_2_luaCom,
		self.WBP_GuildBattleSignBtn_3_luaCom,
		self.WBP_GuildBattleSignBtn_4_luaCom,
	}
	
	-- 团队按钮控件列表
	self.groupTabWidgetList = {
		self.WBP_ComTabR_Tab1_luaCom,
		self.WBP_ComTabR_Tab1_lua_1Com,
		self.WBP_ComTabR_Tab1_lua_2Com,
		self.WBP_ComTabR_Tab1_lua_3Com,
		self.WBP_ComTabR_Tab1_lua_4Com,
		self.WBP_ComTabR_Tab1_lua_5Com,
	}

	self.topTabStrategy = 1 --策略Tab
	self.topTabTag = 2  --标点Tab

	self.allPersonGroup = 6 --所有人团队标记
end

---面板打开的时候触发
function GuildBattleMap_Panel:OnRefresh(...)
	-- todo:设置是否在策略页面
	self:InitTabHor()
	self:InitMap()
	self:InitStrategyPage()
	self:InitTagPage()
	self:InitDragLine()
	self:RefreshByAuthority()
end

---面板关闭
function GuildBattleMap_Panel:OnClose()
	self.currentLongPressTagTaskID = nil
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param mouseEvent FPointerEvent

function GuildBattleMap_Panel:on_WBP_MouseMoveMask_luaBtn_ClickArea_Clicked()
	-- 标点
	local mouseScreenPos = self.mouseScreenPos
	if not mouseScreenPos then
		return
	end
	if self:CheckMouseEventIsInMiniMap(mouseScreenPos) then
		self:TryRequestSendTagInstruction(nil, mouseScreenPos)
		self:SetCurrentTagInstruction(self.topTabIndexSelected, 0)
		--重新初始化标点页签，防止状态冲突
		if self.topTabIndexSelected == self.topTabTag then
			self:RefreshTagData()
			self.KGTileView_BattleTagBtnCom:Refresh(self.tagData)
		else
			for i = 1, #self.commandQuickWidgetList do
				self.commandQuickWidgetList[i]:SetSelected(false)
			end
		end
		if self.topTabIndexSelected == self.topTabTag then
			self:InitTagPageOtherWidget()
		end
	end
	return UIBase.UNHANDLED
end


--- 此处为自动生成
---@param index number
---@param data table
function GuildBattleMap_Panel:on_WBP_ComTabHor_luaCom_ItemSelected(index, data)
	Game.GuildLeagueSystem:SetLeagueTopTab(index)
	self.topTabIndexSelected = index
	Log.DebugFormat("GuildBattleMap_Panel: OnSelectedPageTab %d", index)
	--切换指挥策略Tab
	if index == self.topTabStrategy then
		--指挥显示
		self.view.Canvas_Direct:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		--标点隐藏
		self.view.Canvas_Tag_lua:SetVisibility(ESlateVisibility.Collapsed)
		--取消标点Tab选择的快捷信息
		self:SetCurrentTagInstruction(self.topTabTag, 0)
		
		--重新初始化策略页签,防止状态冲突
		self:InitStrategyPage()
		
		--发送切换请求给地图标点
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_LEAGUE_MAP_ON_SWITCH_TOP_TAB)
	elseif index == self.topTabTag then
		--指挥隐藏
		self.view.Canvas_Direct:SetVisibility(ESlateVisibility.Collapsed)
		--标点显示
		self.view.Canvas_Tag_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		--取消策略Tab选择的快捷信息
		self:SetCurrentTagInstruction(self.topTabStrategy, 0)
		--重新初始化标点页签，防止状态冲突
		self:InitTagPage()
		--请求一次组队信息
		self:TryRequestGroupInfo()
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_UNSELECTED)
	end
end

-------------------------------------------------业务逻辑-------------------------------------------------
--region 初始化
--- 初始化上方横向Tab栏
function GuildBattleMap_Panel:InitTabHor()
	local topTabData = self:GetTabListData()
	self.WBP_ComTabHor_luaCom:Refresh(topTabData)
	self.WBP_ComTabHor_luaCom:SetSelectedItemByIndex(self.topTabStrategy, true)
end

--- 上方Tab列表数据
function GuildBattleMap_Panel:GetTabListData()
	local topTabData = {}
	table.insert(topTabData, UIComSimpleTabList.NewTabData(StringConst.Get("GUILD_LEAGUE_TAB_COMMAND_STRATEGY")))
	table.insert(topTabData, UIComSimpleTabList.NewTabData(StringConst.Get("GUILD_LEAGUE_TAB_TAG")))
	return topTabData
end

--- 初始化地图
function GuildBattleMap_Panel:InitMap()
	self.WBP_GuildBattleFocusItem_luaCom:Refresh()
	self.WBP_GuildBattleMapItem_luaCom:Refresh()
	self.WBP_GuildBattleMapItem_luaCom:SetShowInstructionAnim(true)
	self.WBP_GuildBattleMapItem_luaCom:SetMapTagLayerEnableInteraction(true)
end

--- 初始化策略页签
function GuildBattleMap_Panel:InitStrategyPage()
	-- 初始化策略控件列表
	self:InitCommandStrategyWidgetList()

	-- 初始化快捷指令控件列表
	self:InitCommandQuickWidgetList()
	
	-- 初始化其他控件
	self:RefreshOthersInStrategyPage()
end

--- 初始化策略控件列表
function GuildBattleMap_Panel:InitCommandStrategyWidgetList()
	local data = Game.GuildLeagueSystem:GetLeagueStrategyData()
	self.KGTileView_DirectBtnCom:Refresh(data)
end

--- 初始化快捷指令控件列表
function GuildBattleMap_Panel:InitCommandQuickWidgetList()
	for i = 1, #self.commandQuickWidgetList do
		self.commandQuickWidgetList[i]:Refresh({
			index = i,
			DragStartCallBack = function(...)
				self:OnDragStartCallBack(...)
			end,
			DragCallBack = function(...)
				self:OnDragCallBack(...)
			end,
			ClickCallBack = function(...)
				self:OnClickCallBack(...)
			end,
			DragCancelCallBack = function(...)
				self:OnDragCancelCallBack(...)
			end,
		})
	end
end

--- 刷新策略页签其他控件
function GuildBattleMap_Panel:RefreshOthersInStrategyPage(count)
	local GuildLeagueResource = count or Game.GuildLeagueSystem:GetCurrentLeagueResource()
	self.WBP_GuildBattleResourceTotal_luaCom:Refresh(GuildLeagueResource)
	
	local currenCourageLevel, maxCourageLevel, levelUpNeedResource = Game.GuildLeagueSystem:GetLeagueCourageInfo()
	--当前士气等级
	self.view.Text_Lv_lua:SetText(currenCourageLevel)

	if currenCourageLevel == maxCourageLevel then
		--达到最大等级
		self.view.Text_Resource_Desc_lua:SetText(StringConst.Get("GUILD_LEAGUE_GROUP_COURAGE_MAX")) --已达到最大等级
		self.WBP_GuildBattleResource_luaCom:SetVisibility(false)
	else
		--尚未达到最大等级
		self.view.Text_Resource_Desc_lua:SetText(StringConst.Get("GUILD_LEAGUE_GROUP_NEED_RESOURCE")) --升级士气还需采集
		--还需多少资源
		self.WBP_GuildBattleResource_luaCom:Refresh(levelUpNeedResource)
		self.WBP_GuildBattleResource_luaCom:SetVisibility(true)
	end
end

--- 初始化dragLine
function GuildBattleMap_Panel:InitDragLine()
	-- 不显示
	self.WBP_GuildBattleTagLineDrag_luaCom:SetDragLineVisible(false)
end

-- todo:区分有无权限,权限变更时也要触发触发
function GuildBattleMap_Panel:RefreshByAuthority()
	local checkHasLeagueAuthority = Game.GuildLeagueSystem:CheckHasLeagueAuthority()
	if checkHasLeagueAuthority then
		--有权限
		self.view.WBP_ComTabHor_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self:SetCommandQuickVisible(true)
	else
		--没有权限
		--先设置为策略tab
		self.WBP_ComTabHor_luaCom:SetSelectedItemByIndex(self.topTabStrategy)
		Game.GuildLeagueSystem:SetLeagueTopTab(self.topTabStrategy)
		self.view.WBP_ComTabHor_lua:SetVisibility(ESlateVisibility.Collapsed)
		
		self:SetCommandQuickVisible(false)
	end

end

--- 设置快捷指令的可见性
function GuildBattleMap_Panel:SetCommandQuickVisible(visible)
	-- 设置快捷指令的可见性
	for i = 1, #self.commandQuickWidgetList do
		self.commandQuickWidgetList[i]:SetVisibility(visible)
	end
end

--- 初始化标点页签
function GuildBattleMap_Panel:InitTagPage()
	-- 初始化团队按钮控件列表
	self:InitGroupBtnWidgetList()
	-- 初始化标点控件列表
	self:InitCommandTagWidgetList()
	-- 初始化其他TagPage控件
	self:InitTagPageOtherWidget()
end

--- 初始化团队按钮控件列表，团队数据变更时，也进行刷新
function GuildBattleMap_Panel:InitGroupBtnWidgetList()
	self:InitGroupData()
	self.numOfGroup = #self.groupData - 1 --需要减去所有人团队
	for i = 1, self.numOfGroup do
		self.groupData[i].index = i
		self.groupData[i].ClickCallBack = function(...)
			self:OnGroupBtnClick(...)
		end
		self.groupTabWidgetList[i]:SetVisibility(true)
		self.groupTabWidgetList[i]:Refresh(self.groupData[i])
	end
	for i = self.numOfGroup + 1, #self.groupTabWidgetList - 1 do
		self.groupTabWidgetList[i]:SetVisibility(false)
	end
	-- 设置所有人团队按钮,总是最后一个
	self.groupTabWidgetList[self.allPersonGroup]:SetVisibility(true)
	self.groupData[#self.groupData].index = self.allPersonGroup
	self.groupData[#self.groupData].ClickCallBack = function(...)
		self:OnGroupBtnClick(...)
	end 
	self.groupTabWidgetList[self.allPersonGroup]:Refresh(self.groupData[#self.groupData])
end

--- 初始化团队数据，可刷新
function GuildBattleMap_Panel:InitGroupData()
	self.groupData = Game.GuildLeagueSystem:GetAllLeagueGroupInfo()
end

--- 初始化标点控件列表
function GuildBattleMap_Panel:InitCommandTagWidgetList()
	self:InitTagData()
	self.KGTileView_BattleTagBtnCom:Refresh(self.tagData)
end

--- 初始化标点数据
function GuildBattleMap_Panel:InitTagData()
	table.clear(self.tagData)
	local tableData = Game.TableData.GetGuildLeagueTagInfoDataTable()
	for _ ,v in ksbcipairs(tableData) do
		local tagItemData = {}
		tagItemData.id = v.Id
		table.insert(self.tagData, tagItemData)
	end
	self:RefreshTagData()
end

--- 刷新标点name数据,当tagName数据发生变化时需要调用
function GuildBattleMap_Panel:RefreshTagData()
	local groupIDList = self:GetAllSelectGroupIDList()
	local colorData = self:GetMaterialColorData()
	for index, data in ipairs(self.tagData) do
		data.index = index
		data.name = Game.GuildLeagueSystem:GetTagText(index)
		data.bIsAddTag =  Game.GuildLeagueSystem:CheckGroupListHasTag(groupIDList, data.id)
		data.ClickCallBack = function(...)
			self:OnClickCallBack(...)
		end 
		data.DragCallBack =  function(...)
			self:OnDragCallBack(...)
		end
		data.DragCancelCallBack = function(...)
			self:OnDragCancelCallBack(...)
		end 
		data.DragStartCallBack = function(...)
			self:OnDragStartCallBack(...)
		end
		data.colorData = colorData
		data.selected = self.topTabIndexSelected == self.topTabTag and self:GetCurrentTagInstruction() == index
		data.canPressed = #groupIDList > 0
		data.groupIDList = groupIDList
	end
end

--- 初始化刷新其他TagPage控件 四种状态：1、未选择团队，2、选择了团队，未选中tag，3、选择了团队，选中tag，4、选择了团队，拖拽状态
function GuildBattleMap_Panel:InitTagPageOtherWidget(isDrag)
	if self.topTabIndexSelected ~= self.topTabTag then
		return
	end
	self.view.Text_Tips:SetText(StringConst.Get("GUILD_LEAGUE_BATTLE_FLAG_GROUP_TIPS"))
	if self.SelectedGroupTabList[self.allPersonGroup] or self.numOfSelectedGroup > 0 then
		-- 关闭选择团队的提示
		self.view.Text_Tips:SetVisibility(ESlateVisibility.Collapsed)
		-- 选中团队
		if self:GetCurrentTagInstruction() ~= 0 then
			-- 选中tag
			--隐藏自定义标记按钮
			self.view.Canvas_CustonTag:SetVisibility(ESlateVisibility.Collapsed)
			--点击显示普通提示，拖拽不显示
			if isDrag then
				self.view.Text_TagTips_lua:SetVisibility(ESlateVisibility.Collapsed)
				--与Text_TagTips_lua互斥
				self.view.Text_Deco_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			else
				self.view.Text_TagTips_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
				self.view.Text_TagTips_lua:SetText(StringConst.Get("GUILD_LEAGUE_BATTLE_FLAG_CLICK_TIPS"))
				self.view.Text_Deco_lua:SetVisibility(ESlateVisibility.Collapsed)
			end
		else
			--未选中tag
			--显示自定义标记按钮
			self.view.Canvas_CustonTag:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			--显示不操作下的普通提示
			self.view.Text_TagTips_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self.view.Text_TagTips_lua:SetText(StringConst.Get("GUILD_LEAGUE_BATTLE_FLAG_TIPS"))
			--隐藏使用标记的提示
			self.view.Text_Deco_lua:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		-- 显示选择团队的提示
		self.view.Text_Tips:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		--显示自定义标记按钮
		self.view.Canvas_CustonTag:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		--隐藏使用标记的提示
		self.view.Text_TagTips_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Text_Deco_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end
--endregion

--region 拖拽标点
--- 设置当前选中的标点指令
function GuildBattleMap_Panel:SetCurrentTagInstruction(tab, index)
	if tab == self.topTabStrategy then
		self.SelectedTagIndex.quickTagIndex = index
	else
		self.SelectedTagIndex.normalTagIndex = index
	end
end

--- 获取当前选中的标点指令
function GuildBattleMap_Panel:GetCurrentTagInstruction()
	if self.topTabIndexSelected == self.topTabStrategy then
		return self.SelectedTagIndex.quickTagIndex
	else
		return self.SelectedTagIndex.normalTagIndex
	end
end



--- 拖拽开始回调
function GuildBattleMap_Panel:OnDragStartCallBack(index, myGeometry, inPointerEvent)
	if self.topTabIndexSelected == self.topTabStrategy then
		for i = 1, #self.commandQuickWidgetList do
			if i ~= index then
				self.commandQuickWidgetList[i]:SetSelected(false)
			end
		end
	end
	local previousTagInstruction = self:GetCurrentTagInstruction()
	--self.KGTileView_BattleTagBtnCom:Refresh(self.tagData)
	-- 设置当前选中的标点指令
	self:SetCurrentTagInstruction(self.topTabIndexSelected, index)

	-- 刷新描述
	self:InitTagPageOtherWidget(true)
	if previousTagInstruction ~= 0 and previousTagInstruction ~= index then
		self:RefreshTagData()
		self.KGTileView_BattleTagBtnCom:RefreshItemByIndex(previousTagInstruction)
	end
	-- 显隐
	self.WBP_GuildBattleTagLineDrag_luaCom:SetDragLineVisible(true)
	-- 设置拖拽线的起始位置
	local rootGeometry = self:GetRootCachedGeometry()
	local location = self:GetRelativeOffset(rootGeometry, myGeometry)
	self.WBP_GuildBattleTagLineDrag_luaCom:SetDragStartLocation(location)
end

function GuildBattleMap_Panel:OnDragCallBack(index, myGeometry, inPointerEvent)
	local mouseScreenPos = UKismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
	--Log.DebugFormat("GuildBattleMap_Panel: OnDragCallBack mouseScreenPos: %d,%d", mouseScreenPos.X, mouseScreenPos.Y)
	-- 刷新lineDrag End位置
	self.WBP_GuildBattleTagLineDrag_luaCom:UpdateDragLinePosition(self:GetRootCachedGeometry(), mouseScreenPos)
	-- 区分鼠标是否在地图上
	if self:CheckMouseEventIsInMiniMap(mouseScreenPos) then
		self.WBP_GuildBattleFocusItem_luaCom:SetFocusMapVisible(true)
		--- 刷新小地图显示位置
		local relativePos = USlateBlueprintLibrary.AbsoluteToLocal(self:GetMapCanvasCachedGeometry(), mouseScreenPos)
		self.WBP_GuildBattleFocusItem_luaCom:UpdateFocusMapPosition(relativePos)
	else
		-- 隐藏小地图
		self.WBP_GuildBattleFocusItem_luaCom:SetFocusMapVisible(false)
	end
end

--- 点击回调
function GuildBattleMap_Panel:OnClickCallBack(index, hasTag)
	if self.topTabIndexSelected == self.topTabStrategy then
		for i = 1, #self.commandQuickWidgetList do
			if i ~= index then
				self.commandQuickWidgetList[i]:SetSelected(false)
			end
		end
	end
	if hasTag then
		local tagInstructionId = self.tagData[index].id
		local groupIDList = self:GetAllSelectGroupIDList()
		--if groupIDList[#groupIDList] == 0 then
		--	table.clear(groupIDList)
		--	table.insert(groupIDList,0)  --有所有人时只上传所有人
		--end
		Game.GuildLeagueSystem:RequestRemoveTag(groupIDList, tagInstructionId)
		self:SetCurrentTagInstruction(self.topTabIndexSelected, 0)
	else
		if self:GetCurrentTagInstruction() ~= index then
			self:SetCurrentTagInstruction(self.topTabIndexSelected, index)
		else
			if self.topTabIndexSelected == self.topTabStrategy then
				self.commandQuickWidgetList[index]:SetSelected(false)
			end
			self:SetCurrentTagInstruction(self.topTabIndexSelected, 0)
		end
	end
	
	if self.topTabIndexSelected == self.topTabTag then
		self:InitTagPageOtherWidget()
		self:RefreshTagData()
		self.KGTileView_BattleTagBtnCom:Refresh(self.tagData)
	end
end

--- 拖拽释放回调
function GuildBattleMap_Panel:OnDragCancelCallBack(index, inPointerEvent)
	-- 标点
	local mouseScreenPos = UKismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
	-- 刷新聚焦地图框，区分鼠标是否在地图上
	if self:CheckMouseEventIsInMiniMap(mouseScreenPos) then
		self:TryRequestSendTagInstruction(inPointerEvent)
	end
	self:SetCurrentTagInstruction(self.topTabIndexSelected, 0)

	if self.topTabIndexSelected == self.topTabStrategy then
		for i = 1, #self.commandQuickWidgetList do
			self.commandQuickWidgetList[i]:SetSelected(false)
		end
	end
	if self.topTabIndexSelected == self.topTabTag then
		self:InitTagPageOtherWidget()
	end
	-- 显隐
	self.WBP_GuildBattleTagLineDrag_luaCom:SetDragLineVisible(false)
	-- 隐藏小地图
	self.WBP_GuildBattleFocusItem_luaCom:SetFocusMapVisible(false)
end

--endregion

--region位置相关
--- 获取widgetB相对于widgetA的相对偏移（从widgetB的中心算）
function GuildBattleMap_Panel:GetRelativeOffset(aGeometry, bGeometry)
	local localSize = USlateBlueprintLibrary.GetLocalSize(bGeometry)

	local bAbsolutePos = USlateBlueprintLibrary.LocalToAbsolute(bGeometry, localSize/2)
	local relativePos = USlateBlueprintLibrary.AbsoluteToLocal(aGeometry, bAbsolutePos)

	return relativePos
end

--- 获取ROOT Geometry
function GuildBattleMap_Panel:GetRootCachedGeometry()
	return self.view.Canvas_AllContent:GetCachedGeometry()
end

--- 获取地图CanvasGeometry
function GuildBattleMap_Panel:GetMapCanvasCachedGeometry()
	return self.view.Canvas_Map_lua:GetCachedGeometry()
end

--- check鼠标坐标是否在地图上
function GuildBattleMap_Panel:CheckMouseEventIsInMiniMap(mouseScreenPos)
	return USlateBlueprintLibrary.IsUnderLocation(self:GetMapCanvasCachedGeometry(), mouseScreenPos)
end

--获取小地图指定UI 偏移位置的3D世界坐标
function GuildBattleMap_Panel:GetMiniMap3DPosition(relativePosition)
	local mapCanvasGeometry = self:GetMapCanvasCachedGeometry()
	local mapLocalSize = USlateBlueprintLibrary.GetLocalSize(mapCanvasGeometry)
	local relativeCoordinate = relativePosition / mapLocalSize

	local ret = FVector()
	local Map2DPos = self.WBP_GuildBattleMapItem_luaCom:GetMap2DPos(relativeCoordinate)
	ret.X = Map2DPos.X
	ret.Y = Map2DPos.Y

	return ret
end
--endregion

--region 团队选择
--- 获取当前选中的团队的颜色数据
function GuildBattleMap_Panel:GetMaterialColorData()
	local materialColorData = {}
	for _, groupID in pairs(self.SelectedGroupTabList) do
		table.insert(materialColorData, Game.GuildLeagueSystem:GetGroupColorByGroupID(groupID))
	end
	return materialColorData
end

--- 点击团队按钮的回调，团队数量变化时，团队都刷未选中
function GuildBattleMap_Panel:OnGroupBtnClick(index)
	-- 选中团队
	local wantToSelect = self.SelectedGroupTabList[index] == nil

	if index ~= self.allPersonGroup then
		-- 选择正式的团队
		self.SelectedGroupTabList[index] = wantToSelect and self.groupData[index].groupID or nil
		if wantToSelect then
			self.numOfSelectedGroup = self.numOfSelectedGroup + 1
		else
			self.numOfSelectedGroup = self.numOfSelectedGroup - 1
			if self.numOfSelectedGroup <= 0 then
				self.numOfSelectedGroup = 0
				self:SetCurrentTagInstruction(self.topTabIndexSelected, 0)
			end
		end
		-- 如果所有人团队已经选中了，需要取消选中
		if self.SelectedGroupTabList[self.allPersonGroup] then
			self.groupTabWidgetList[self.allPersonGroup]:SetSlcPre(false)
			self.SelectedGroupTabList[self.allPersonGroup] = nil
		end
		---- 如果选中所有团队，自动选上所有人团队
		--if self.numOfSelectedGroup >= self.numOfGroup then
		--	self.SelectedGroupTabList[self.allPersonGroup] = Game.GuildLeagueSystem.AllGROUPID
		--	self.groupTabWidgetList[self.allPersonGroup]:SetSlcPre(true)
		--else
		--	self.SelectedGroupTabList[self.allPersonGroup] = nil
		--	self.groupTabWidgetList[self.allPersonGroup]:SetSlcPre(false)
		--end
	else
		-- 选择所有人团队
		if wantToSelect then
			-- 取消正式团队的选择
			self:SetAllGroupSelected(not wantToSelect, false)
		end
		self.SelectedGroupTabList[index] = wantToSelect and Game.GuildLeagueSystem.AllGROUPID or nil
	end
	-- 刷新标点
	self:InitTagPageOtherWidget()
	self:RefreshTagData()
	self.KGTileView_BattleTagBtnCom:Refresh(self.tagData)
end

--- 获取当前选中的团队ID列表
function GuildBattleMap_Panel:GetAllSelectGroupIDList()
	local groupIDList = {}
	for _, groupID in pairs(self.SelectedGroupTabList) do
		table.insert(groupIDList, groupID)
	end
	return groupIDList
end

--- 设置团队都选中或都未选中
function GuildBattleMap_Panel:SetAllGroupSelected(isSelected, bNotAll)
	self.numOfSelectedGroup = isSelected and self.numOfGroup or 0
	for i = 1, self.numOfGroup do
		self.SelectedGroupTabList[i] = isSelected and self.groupData[i].groupID or nil
		self.groupTabWidgetList[i]:SetSlcPre(isSelected)
	end
	if bNotAll then
		self.SelectedGroupTabList[self.allPersonGroup] = isSelected and Game.GuildLeagueSystem.AllGROUPID or nil
		self.groupTabWidgetList[self.allPersonGroup]:SetSlcPre(isSelected)
	end
	if #self.SelectedGroupTabList == 0 then
		self:SetCurrentTagInstruction(self.topTabIndexSelected, 0)
	end
end

--endregion

--region事件处理

--- 公会资源发生改变
function GuildBattleMap_Panel:OnResourceChange(new)
	--self:InitCommandStrategyWidgetList()
	local data = Game.GuildLeagueSystem:GetLeagueStrategyData()
	for i = 1, #data do
		self.KGTileView_DirectBtnCom:GetItemByIndex(i):OnRefreshCanUseStrategy(false)
	end
	self.WBP_GuildBattleResourceTotal_luaCom:RefreshResourceInfo(new,function()
		self:RefreshOthersInStrategyPage(new)
	end)
end

--- 请求团队信息
function GuildBattleMap_Panel:TryRequestGroupInfo()
	if Game.GuildLeagueSystem:CheckHasLeagueAuthority() then
		--如果有联赛权限，请求一次所有的团队信息
		Game.GuildLeagueSystem:RequestLeagueGroupInfo()
	end
end

--- 请求发送标点指令
function GuildBattleMap_Panel:TryRequestSendTagInstruction(inPointerEvent, position)
	local tagInstruction = self:GetCurrentTagInstruction()
	if tagInstruction == 0 then
		return
	end
	local mouseScreenPos = inPointerEvent and UKismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent) or position
	local mapCanvasGeometry = self:GetMapCanvasCachedGeometry()
	local relativePosition = USlateBlueprintLibrary.AbsoluteToLocal(mapCanvasGeometry, mouseScreenPos)
	local absolutePosition = self:GetMiniMap3DPosition(relativePosition)
	-- 发送标点指令
	if self.topTabIndexSelected == self.topTabStrategy then
		-- 快速指令
		local tagInstructionId = self.QuickTagMap[tagInstruction]
		Game.GuildLeagueSystem:RequestUseQuickInstruction(tagInstructionId, absolutePosition)
	elseif self.topTabIndexSelected == self.topTabTag then
		---- 标点指令
		local tagInstructionId = self.tagData[tagInstruction].id
		local groupIDList = self:GetAllSelectGroupIDList()
		Game.GuildLeagueSystem:RequestAddTag(groupIDList, tagInstructionId, absolutePosition)
	end
end

--- 公会标点文字发生改变
function GuildBattleMap_Panel:OnTagTextChange()
	-- 重新刷新标点数据
	self:RefreshTagData()
	self.KGTileView_BattleTagBtnCom:Refresh(self.tagData)
end

--- 刷新团队信息
function GuildBattleMap_Panel:OnGetAllGroupInfo()
	self:InitDragLine() -- 隐藏拖拽线
	self:InitGroupBtnWidgetList()
	-- 重新计算团队数据映射
	local toRemove, toUpdate = {}, {}

	for index, value in pairs(self.SelectedGroupTabList) do
		if index ~= self.allPersonGroup then
			local foundIndex = nil

			-- 在 groupData 中查找匹配的 groupID
			for i, group in ipairs(self.groupData) do
				if group.groupID == value then
					foundIndex = i
					break
				end
			end

			-- 记录需要删除或更新的项
			if not foundIndex then
				table.insert(toRemove, index)  -- 标记为待删除
			elseif foundIndex ~= index then
				table.insert(toUpdate, {new = foundIndex, value = value})  -- 标记为待更新
				table.insert(toRemove, index)  -- 标记为待删除
			end
		end
	end

	-- 统一处理更新（先更新再删除，避免索引冲突）
	for _, index in ipairs(toRemove) do
		self.SelectedGroupTabList[index] = nil
	end
	
	for _, op in ipairs(toUpdate) do
		self.SelectedGroupTabList[op.new] = op.value
	end

	-- 刷新已经选择的团队
	for index, _ in pairs(self.SelectedGroupTabList) do
		self.groupTabWidgetList[index]:SetSlcPre(true)
	end
	self:InitCommandTagWidgetList()
	-- 刷新其他TagPage控件
	self:InitTagPageOtherWidget()
end

--- 长按地图tag 
function GuildBattleMap_Panel:OnStartLongPressMapTag(taskID)
	if self.currentLongPressTagTaskID == taskID then
		return
	end
	if self.topTabTag ~= self.topTabIndexSelected then
		return
	end
	self.currentLongPressTagTaskID = taskID
end

--- 长按地图tag结束
function GuildBattleMap_Panel:OnEndLongPressMapTag(widget)
	if self.topTabTag ~= self.topTabIndexSelected or widget == nil then
		self.currentLongPressTagTaskID = nil
		return
	end
	local uiTagData = self.WBP_GuildBattleMapItem_luaCom:GetGuildLeagueTagComponent():GetBattleTagInfo(self.currentLongPressTagTaskID)
	self.currentLongPressTagTaskID = nil
	if uiTagData == nil then
		return
	end
	self.WBP_GuildBattleFocusItem_luaCom:SetFocusMapVisible(false)
	local relativePosition = self:GetRelativeOffset(self:GetMapCanvasCachedGeometry(), widget:GetCachedGeometry())
	local absolutePosition = self:GetMiniMap3DPosition(relativePosition)

	Game.GuildLeagueSystem:RequestAddTag(uiTagData.groupIDList, uiTagData.tagID, absolutePosition)
end

--- 阶段变化
function GuildBattleMap_Panel:OnStageChanged(new, old)
	if new == Const.GUILD_LEAGUE_BATTLE_STAGE then
		self:InitCommandStrategyWidgetList() -- 刷新一遍数据显示
	end
end
--endregion 事件处理
-------------------------------------------------业务逻辑-------------------------------------------------


--- 此处为自动生成
function GuildBattleMap_Panel:on_Btn_CustomTag_lua_Clicked()
	Game.NewUIManager:OpenPanel("GuildBattleTagPopup_Panel")
end


--- 此处为自动生成
---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent

function GuildBattleMap_Panel:on_WBP_GuildBattleMap_Panel_MouseMoveEvent(myGeometry, inMouseEvent)
	self.mouseScreenPos = UKismetInputLibrary.PointerEvent_GetScreenSpacePosition(inMouseEvent)
	if self.currentLongPressTagTaskID == nil then
		return
	end
	-- 刷新聚焦地图框，区分鼠标是否在地图上
	if self:CheckMouseEventIsInMiniMap(self.mouseScreenPos) then
		-- 发送事件刷新位置
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_LEAGUE_MAP_TAG_CHANGE_POSITION, self.currentLongPressTagTaskID, self.mouseScreenPos)

		self.WBP_GuildBattleFocusItem_luaCom:SetFocusMapVisible(true)
		--- 刷新小地图显示位置
		local relativePos = USlateBlueprintLibrary.AbsoluteToLocal(self:GetMapCanvasCachedGeometry(), self.mouseScreenPos)
		self.WBP_GuildBattleFocusItem_luaCom:UpdateFocusMapPosition(relativePos)
	else
		-- 隐藏小地图
		self.WBP_GuildBattleFocusItem_luaCom:SetFocusMapVisible(false)
	end
end


--- 此处为自动生成
function GuildBattleMap_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
	self:CloseSelf()
end


--- 此处为自动生成
function GuildBattleMap_Panel:on_Btn_ClickArea_lua_Clicked()
	-- todo:更改文本
	Game.TipsSystem:ShowTips(Enum.ETipsData.GUILD_LEAGUE_MORALE_TIPS, self.view.Btn_ClickArea_lua:GetCachedGeometry())
end

--- 刷新其他策略按钮的状态
function GuildBattleMap_Panel:OnStrategySelected(id)
	if self.topTabIndexSelected ~= self.topTabStrategy then
		return
	end
	local data = Game.GuildLeagueSystem:GetLeagueStrategyData()
	for i = 1, #data do
		if data[i].id ~= id then
			--刷新点击以外的策略按钮状态
			self.KGTileView_DirectBtnCom:GetItemByIndex(i):OnRefreshCanUseStrategy(true)
		end
	end
end

function GuildBattleMap_Panel:OnUseStrategyID(id)
	if self.topTabIndexSelected ~= self.topTabStrategy then
		return
	end
	local data = Game.GuildLeagueSystem:GetLeagueStrategyData()
	for i = 1, #data do
		if data[i].id == id then
			self.KGTileView_DirectBtnCom:GetItemByIndex(i):OnUseStrategyID(id)
			break
		end
	end
end

function GuildBattleMap_Panel:OnRemoveFlagID(groupIDList, tagID, absolutePosition)
	for i = 1, #self.tagData do
		if self.tagData[i].id == tagID then
			self.KGTileView_BattleTagBtnCom:GetItemByIndex(i):OnRemoveFlagID(groupIDList, tagID, absolutePosition)
			break
		end
	end
end

function GuildBattleMap_Panel:OnAddFlagID(groupIDList, tagID, absolutePosition)
	for i = 1, #self.tagData do
		if self.tagData[i].id == tagID then
			self.KGTileView_BattleTagBtnCom:GetItemByIndex(i):OnAddFlagID(groupIDList, tagID, absolutePosition)
			break
		end
	end
end


return GuildBattleMap_Panel
