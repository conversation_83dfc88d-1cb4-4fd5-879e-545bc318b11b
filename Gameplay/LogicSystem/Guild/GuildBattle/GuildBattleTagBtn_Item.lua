local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class GuildBattleTagBtn_Item : UIListItem
---@field view GuildBattleTagBtn_ItemBlueprint
local GuildBattleTagBtn_Item = DefineClass("GuildBattleTagBtn_Item", UIListItem)

GuildBattleTagBtn_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleTagBtn_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleTagBtn_Item:InitUIData()
	self.index = 0 -- 当前的按钮索引
	self.ClickCallBack = nil -- 点击回调
	self.DragStartCallBack = nil -- 长按开始拖动回调
	self.DragCallBack = nil -- 长按拖动回调
	self.DragCancelCallBack = nil -- 长按拖动取消回调

	self.bPressed = false -- 是否按下
	
	self.canPressed = false -- 是否可以按下

	self.bIsAddTag = false -- 是否所选团队集下添加了Tag

	self.LongPressTime = 0.2 -- 长按时间
	self.pressTimer = nil -- 按下定时器

	self.TagState = {  ---	按钮状态
	   Normal	= 0, -- 常态
	   Highlight = 1, -- 高亮
	   Selected = 2, -- 选中
	}
end

---组件刷新统一入口
function GuildBattleTagBtn_Item:OnRefresh(params)
	self.Params = params
	self.index = params.index or 1
	self.bIsAddTag = params.bIsAddTag or false
	self.bPressed = self.Params.selected or false
	self:SetDisable(#params.colorData == 0)
	self:SetTagColor(params.colorData)
	self:RefreshState()
	self:SetText(self.Params.name, self.index)
	self.canPressed = params.canPressed
	if not self.ClickCallBack then
		self.ClickCallBack = params.ClickCallBack
	end
	if not self.DragStartCallBack then
		self.DragStartCallBack = params.DragStartCallBack
	end
	if not self.DragCallBack then
		self.DragCallBack = params.DragCallBack
	end
	if not self.DragCancelCallBack then
		self.DragCancelCallBack = params.DragCancelCallBack
	end
end
--- 设置Tag的层次颜色
function GuildBattleTagBtn_Item:SetTagColor(colorData)
	Game.GuildLeagueSystem:ChangeTagMaterialColor(self.view.Img_TagColor:GetDynamicMaterial(), colorData)
end

--- 设置是否禁用Tag
function GuildBattleTagBtn_Item:SetDisable(bDisable)
	self.userWidget:SetDisable(bDisable)
end

--- 设置类型 0：常态，1：已标记，2：选中准备标记
function GuildBattleTagBtn_Item:SetBtnType(type)
	self.userWidget:Event_UI_Style(type)
end

--- 设置文本内容
function GuildBattleTagBtn_Item:SetText(text, num)
	self.view.Text_Word:SetText(text)
	self.view.Text_Num:SetText(num)
end

--- 刷新按钮状态
function GuildBattleTagBtn_Item:RefreshState()
	-- 如果刷新时有选中，则设置为选中状态
	if self.bPressed then
		self:SetBtnType(self.TagState.Selected)
	else
		if self.bIsAddTag then
			self:SetBtnType(self.TagState.Highlight)
		else
			self:SetBtnType(self.TagState.Normal)
		end
	end
end

--- 销毁计时器
function GuildBattleTagBtn_Item:DestroyTimer()
	if self.pressTimer then
		Game.TimerManager:StopTimerAndKill(self.pressTimer)
		self.pressTimer = nil
	end
end

function GuildBattleTagBtn_Item:OnClose()
	self.bPressed = false
	self:DestroyTimer()
end

---UI事件在这里注册，此处为自动生成
function GuildBattleTagBtn_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnTouchStartedEvent, "on_Btn_ClickArea_lua_TouchStartedEvent")
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnTouchMovedEvent, "on_Btn_ClickArea_lua_TouchMovedEvent")
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnTouchEndedEvent, "on_Btn_ClickArea_lua_TouchEndedEvent")
end


--- 此处为自动生成
function GuildBattleTagBtn_Item:on_Btn_ClickArea_lua_Clicked()
	if not self.canPressed then
		return
	end
	-- 点击回调，这里是保存标点状态，用于地图标点
	if self.Params and self.ClickCallBack then
		if not self.bIsAddTag then
			self.bPressed = true
			self:RefreshState()
			self:PlayAnimation(self.view.Ani_Type2_click)
		end
		self.ClickCallBack(self.index, self.bIsAddTag)
	end
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent

function GuildBattleTagBtn_Item:on_Btn_ClickArea_lua_TouchStartedEvent(myGeometry, inPointerEvent)
	if not self.canPressed then
		return
	end
	self.pressTimer = Game.TimerManager:CreateTimerAndStart(function()
		self:OnStartDrag(myGeometry)
	end, self.LongPressTime * 1000, 1)
end

function GuildBattleTagBtn_Item:OnStartDrag(myGeometry)
	self.bPressed = true
	self:RefreshState()
	self:PlayAnimation(self.view.Ani_Type2_click)
	if self.Params and self.DragStartCallBack then
		-- 标完点需要重新设置SetSelected 和 bPressed
		self.DragStartCallBack(self.index, myGeometry)
		self.pressTimer = nil
	end
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent

function GuildBattleTagBtn_Item:on_Btn_ClickArea_lua_TouchMovedEvent(myGeometry, inPointerEvent)
	if not self.canPressed then
		return
	end
	if self.pressTimer then
		return
	end
	if self.Params and self.DragCallBack then
		self.DragCallBack(self.index, myGeometry, inPointerEvent)
	end
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent

function GuildBattleTagBtn_Item:on_Btn_ClickArea_lua_TouchEndedEvent(myGeometry, inPointerEvent)
	if not self.canPressed then
		return
	end
	self.bPressed = false
	-- 还原
	self:RefreshState()
	if self.pressTimer then
		self:DestroyTimer()
		return
	end
	if self.Params and self.DragCancelCallBack then
		self.DragCancelCallBack(self.index, inPointerEvent)
	end
end

--- 移除标记点
function GuildBattleTagBtn_Item:OnRemoveFlagID(groupIDList, tagID, absolutePosition)
	if tagID == self.Params.id then
		local bGroupListHasTag = Game.GuildLeagueSystem:CheckTableHasEqualTag(groupIDList, self.Params.groupIDList)
		if bGroupListHasTag then
			self.bIsAddTag = false
			self:RefreshState()
		end
	end
end

--- 添加标记点
function GuildBattleTagBtn_Item:OnAddFlagID(groupIDList, tagID, absolutePosition)
	if tagID == self.Params.id then
		local bGroupListHasTag = Game.GuildLeagueSystem:CheckTableHasEqualTag(groupIDList, self.Params.groupIDList)
		if bGroupListHasTag then
			self.bIsAddTag = true
			self:RefreshState()
			self:PlayAnimation(self.view.Ani_Type1)
		end
	end
end

return GuildBattleTagBtn_Item
