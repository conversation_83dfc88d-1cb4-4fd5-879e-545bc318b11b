---WBP_GuildBattleResourceNum
-- todo:zhangfeng后续跳字迭代，和伤害跳字进行统一
local GuildBattleResourceEffect= DefineClass("GuildBattleResourceEffect", WorldWidgetCellBase)

function GuildBattleResourceEffect:OnInit()
    ---self.CurShowPos = FVector2D(0, 0)
end

function GuildBattleResourceEffect:OnGetFromPool(params)
    if params == nil then
        self:Hide()
        Game.GuildLeagueSystem:ReturnObjectToPool(self.WWID)
        return
    end

    local player = Game.me
    if not player then
        self:Hide()
        Game.GuildLeagueSystem:ReturnObjectToPool(self.WWID)
        return
    end
	local targetID = params.targetUID
	
    -- 获取目标角色
	local targetEntity = Game.EntityManager:getEntity(targetID)
	if not targetEntity then
        self:Hide()
		Game.GuildLeagueSystem:ReturnObjectToPool(self.WWID)
		return
	end

    --- 控件缩放规则
    --local ScaleRate = self:GetScale(targetEntity.CharacterID)
    --if ScaleRate then
    --    self.userWidget:SetScale(ScaleRate)
    --end
    ---todo:先设置简单的跳字，不设置随机位置偏移
	self.view.Text_Num:SetText(string.format("%s%s","+",params.resourceCount or 1))
    --播放动画
    self:PlayAniByType(self.view.Ani_Fadein)
end

--- 播放动画
function GuildBattleResourceEffect:PlayAniByType(AniType)
    if not AniType then
        return
    end

    self:PlayAnimation(AniType, function()
        self:Hide()
        self.flagNeedDestroy = true
    end)
end


--- 根据与镜头的距离设置控件缩放
function GuildBattleResourceEffect:GetScale(InTargetID)
    if not InTargetID or InTargetID == 0 then return 1 end
    return Game.CameraManager:GetScaleForDamage(InTargetID)
end

---返回对象池的回调
function GuildBattleResourceEffect:OnReturnToPool()
    self:Hide()
end

function GuildBattleResourceEffect:Dipose()
    WorldWidgetCellBase.Dipose(self)
    if self.flagNeedDestroy then
		Game.GuildLeagueSystem:ReturnObjectToPool(self.WWID)
    end
end

return GuildBattleResourceEffect
