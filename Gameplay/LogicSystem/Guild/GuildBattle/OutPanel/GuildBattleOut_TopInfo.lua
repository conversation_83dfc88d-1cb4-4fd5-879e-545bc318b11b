local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildBattleOut_TopInfo : UIComponent
---@field view GuildBattleOut_TopInfoBlueprint
local GuildBattleOut_TopInfo = DefineClass("GuildBattleOut_TopInfo", UIComponent)
local const = kg_require("Shared.Const")
local StringConst = require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")

GuildBattleOut_TopInfo.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleOut_TopInfo:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleOut_TopInfo:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildBattleOut_TopInfo:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildBattleOut_TopInfo:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleOut_TopInfo:InitUIView()
end

---面板打开的时候触发
function GuildBattleOut_TopInfo:OnRefresh(...)
end

---设置文本内容
function GuildBattleOut_TopInfo:SetTopInfoContent(GroupID, BattleDay, BattleTime, BidTime)
	self:SetSeaonContent(GroupID)
	self:SetGameTime(BattleDay, BattleTime, BidTime)
end

---设置赛季内容
function GuildBattleOut_TopInfo:SetSeaonContent(GroupID)
	--没有资格比赛直接隐藏整个区域
	if Game.GuildLeagueSystem.model.zoneID == 0 then
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		--读取玩家所在的分区和组
		--特殊组要特殊处理
		if Game.GuildLeagueSystem.model.selfGameInfo.groupType == const.GUILD_LEAGUE_GROUP_TYPE.SPECIAL then
			GroupID = Game.GuildLeagueSystem.model.groupCount
		elseif Game.GuildLeagueSystem.model.selfGameInfo.groupType == const.GUILD_LEAGUE_GROUP_TYPE.MATCH then
			GroupID = 0
		end
		local DivisionData = Game.TableData.GetGuildLeagueDivisionNameDataRow(Game.GuildLeagueSystem.model.zoneID)
		local ZoneName = DivisionData and DivisionData.DivisionName or StringConst.Get("GUILD_LEAGUE_OVER_MAX_GROUP_NAME_DESC")
		local GroupName = (GroupID ~= 0) and tostring(GroupID) or StringConst.Get("GUILD_LEAGUE_NO_GROUP_NAME_DESC")
	
		self.view.Text_Info1_lua:SetText(string.format(StringConst.Get("GUILD_LEAGUE_DEFAULT_DIVISION_NAME"), ZoneName, GroupName))
		--读取赛季信息并且进行配置
		local SeaonID = Game.GuildLeagueSystem.model.guildLeagueSeasonInfo.id
		local SeasonData = Game.TableData.GetGuildLeagueSeasonDataRow(SeaonID)
		if SeasonData then
		self.view.Text_TimeInfo_lua:SetText(string.format("%s-%s", SeasonData.SeasonStartTime, SeasonData.SeasonEndTime))
		end
		end
end

---设置比赛时间
function GuildBattleOut_TopInfo:SetGameTime(BattleDay, BattleTime, BidTime)
	--计算展示的轮次
	local Round = Game.GuildLeagueSystem.model.guildLeagueRound
	if (Round.round == 3 and Round.status ~= const.GUILD_LEAGUE_ROUND_STATUS.END and Round.status ~= const.GUILD_LEAGUE_ROUND_STATUS.REWARDED)
		or (Round == 2 and (Round.status == const.GUILD_LEAGUE_ROUND_STATUS.END or Round.status == const.GUILD_LEAGUE_ROUND_STATUS.REWARDED)) then
		self.view.Text_Info2_lua:SetText(string.format(StringConst.Get("GUILD_LEAGUE_SECOND_ROUND_TIME_DESC") or "%s", BattleTime))
	else
		self.view.Text_Info2_lua:SetText(string.format(StringConst.Get("GUILD_LEAGUE_FIRST_ROUND_TIME_DESC") or "%s", BattleTime))
	end
end

return GuildBattleOut_TopInfo