local GuildBattleOutFlag_Item = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.OutPanel.GuildBattleOutFlag_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildBattleOutRanking_Item : UIListItem
---@field view GuildBattleOutRanking_ItemBlueprint
local GuildBattleOutRanking_Item = DefineClass("GuildBattleOutRanking_Item", UIComponent)

GuildBattleOutRanking_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleOutRanking_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleOutRanking_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildBattleOutRanking_Item:InitUIComponent()
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_7_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_7_lua, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_6_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_6_lua, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_5_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_5_lua, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_4_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_4_lua, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_3_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_3_lua, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_1_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_1_lua, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_2_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_2_lua, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_luaCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_lua, GuildBattleOutFlag_Item)
end

---UI事件在这里注册，此处为自动生成
function GuildBattleOutRanking_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleOutRanking_Item:InitUIView()
	--第一至第八名公会旗帜控件
	self.GuildList = 
	{
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_luaCom,
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_1_luaCom,
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_2_luaCom,
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_3_luaCom,
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_4_luaCom,
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_5_luaCom,
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_6_luaCom,
		---@type GuildBattleOutFlag_Item
		self.wBP_GuildBattleOutFlag_Item_7_luaCom,
	}
	self.wBP_GuildBattleOutFlag_Item_luaCom:SetAsPostEvent(false, 1)
	self.wBP_GuildBattleOutFlag_Item_1_luaCom:SetAsPostEvent(false, 2)
	self.wBP_GuildBattleOutFlag_Item_2_luaCom:SetAsPostEvent(false, 3)
	self.wBP_GuildBattleOutFlag_Item_3_luaCom:SetAsPostEvent(false, 4)
	self.wBP_GuildBattleOutFlag_Item_4_luaCom:SetAsPostEvent(false, 5)
	self.wBP_GuildBattleOutFlag_Item_5_luaCom:SetAsPostEvent(false, 6)
	self.wBP_GuildBattleOutFlag_Item_6_luaCom:SetAsPostEvent(false, 7)
	self.wBP_GuildBattleOutFlag_Item_7_luaCom:SetAsPostEvent(false, 8)
end

---面板打开的时候触发
function GuildBattleOutRanking_Item:OnRefresh(...)
end

function GuildBattleOutRanking_Item:OnPageReady()
	self.view._userWidget:PlayAnimationForward(self.view.Ani_Fadein)
end

---设置公会信息
function GuildBattleOutRanking_Item:SetPanelData()
	for Position, Rank in pairs(Game.GuildLeagueSystem.model.guildBattleRankCache) do
		local GuildInfo = Game.GuildLeagueSystem.model.guildBattleOutCache.groupInfo[Rank]
		self.GuildList[Position]:SetGuildInfo(GuildInfo.name, GuildInfo.badgeIndex, GuildInfo.badgeFrameId, Game.GuildLeagueSystem.model.guildBattleRuleCache[Position])
	end
end

return GuildBattleOutRanking_Item
