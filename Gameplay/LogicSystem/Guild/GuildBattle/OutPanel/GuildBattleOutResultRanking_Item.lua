local GuildBattleOutResult_Item = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.OutPanel.GuildBattleOutResult_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildBattleOutResultRanking_Item : UIListItem
---@field view GuildBattleOutResultRanking_ItemBlueprint
local GuildBattleOutResultRanking_Item = DefineClass("GuildBattleOutResultRanking_Item", UIComponent)

GuildBattleOutResultRanking_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleOutResultRanking_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleOutResultRanking_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildBattleOutResultRanking_Item:InitUIComponent()
    ---@type GuildBattleOutResult_Item
    self.wBP_GuildBattleOutResult_Item_3Com = self:CreateComponent(self.view.WBP_GuildBattleOutResult_Item_3, GuildBattleOutResult_Item)
    ---@type GuildBattleOutResult_Item
    self.wBP_GuildBattleOutResult_Item_1Com = self:CreateComponent(self.view.WBP_GuildBattleOutResult_Item_1, GuildBattleOutResult_Item)
    ---@type GuildBattleOutResult_Item
    self.wBP_GuildBattleOutResult_Item_2Com = self:CreateComponent(self.view.WBP_GuildBattleOutResult_Item_2, GuildBattleOutResult_Item)
    ---@type GuildBattleOutResult_Item
    self.wBP_GuildBattleOutResult_ItemCom = self:CreateComponent(self.view.WBP_GuildBattleOutResult_Item, GuildBattleOutResult_Item)
end

---UI事件在这里注册，此处为自动生成
function GuildBattleOutResultRanking_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleOutResultRanking_Item:InitUIView()
	self.ResultGroup = 
	{
		self.wBP_GuildBattleOutResult_ItemCom, self.wBP_GuildBattleOutResult_Item_2Com, 
		self.wBP_GuildBattleOutResult_Item_3Com, self.wBP_GuildBattleOutResult_Item_1Com
	}
end

---面板打开的时候触发
function GuildBattleOutResultRanking_Item:OnRefresh(...)
end

function GuildBattleOutResultRanking_Item:OnPageReady()
end

---设置界面默认状态
function GuildBattleOutResultRanking_Item:SetPanelDefault()
	for _, MatchItem in pairs(self.ResultGroup) do
		self:SetEmptyMatch(MatchItem)
	end
end

function GuildBattleOutResultRanking_Item:SetPanelData(RoundInfo)
	if not RoundInfo or not next(RoundInfo) then
		for _, MatchItem in pairs(self.ResultGroup) do
			self:SetEmptyMatch(MatchItem)
		end
		return
	end
	for Index, Info in pairs(RoundInfo) do
		self:SetResult(self.ResultGroup[Index], Info.GuildA, Info.GuildB, Info.bStarted, Info.bFinished, Info.Winner, Index * 2)
	end
end

---设置为空
function GuildBattleOutResultRanking_Item:SetEmptyMatch(InResultItem)
	InResultItem:SetResult(false, false, -1)
	InResultItem:SetGuild("", "", false, false, -1, -1)
end

---设置四组内容
---@param InResultItem
---@param Guild_A table
---@param Guild_B table
---@param bStarted boolean
---@param bFinished boolean
---@param Winnder number
---@param Index number
function GuildBattleOutResultRanking_Item:SetResult(InResultItem, Guild_A, Guild_B, bStarted, bFinished, Winner, Index)
	InResultItem:SetResult(bStarted, bFinished, Winner)
	InResultItem:SetGuild(Guild_A, Guild_B, bStarted, bFinished, Winner, Index)
end

return GuildBattleOutResultRanking_Item
