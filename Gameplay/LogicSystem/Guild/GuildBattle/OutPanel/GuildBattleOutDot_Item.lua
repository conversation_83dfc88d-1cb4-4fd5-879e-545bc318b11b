local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildBattleOutDot_Item : UIListItem
---@field view GuildBattleOutDot_ItemBlueprint
local GuildBattleOutDot_Item = DefineClass("GuildBattleOutDot_Item", UIComponent)

GuildBattleOutDot_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleOutDot_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleOutDot_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildBattleOutDot_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildBattleOutDot_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleOutDot_Item:InitUIView()
end

---面板打开的时候触发
function GuildBattleOutDot_Item:OnRefresh(...)
end
--设置为战斗没有开始
function GuildBattleOutDot_Item:SetNotBattle()
	self.userWidget:Event_UI_State(1)
end

---设置为战斗
function GuildBattleOutDot_Item:SetAsBattle()
	self.userWidget:Event_UI_State(2)
end

---设置为战斗已经结束
function GuildBattleOutDot_Item:SetAsFinished()
	self.userWidget:Event_UI_State(0)
end

return GuildBattleOutDot_Item
