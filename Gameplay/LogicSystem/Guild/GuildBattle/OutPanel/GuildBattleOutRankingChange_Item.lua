local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildBattleOutRankingChange_Item : UIListItem
---@field view GuildBattleOutRankingChange_ItemBlueprint
local GuildBattleOutRankingChange_Item = DefineClass("GuildBattleOutRankingChange_Item", UIComponent)
local StringConst = require "Data.Config.StringConst.StringConst"

GuildBattleOutRankingChange_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleOutRankingChange_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleOutRankingChange_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildBattleOutRankingChange_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildBattleOutRankingChange_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleOutRankingChange_Item:InitUIView()
end

---面板打开的时候触发
function GuildBattleOutRankingChange_Item:OnRefresh(...)
end

function GuildBattleOutRankingChange_Item:SetRule(Rule)
	self.view._userWidget:Event_UI_State((Rule >= 0) and true)
	if Rule < 0 then
		Rule = Rule * -1
	end
	self.view.TextNum_lua:SetText(string.format(StringConst.Get("GUILD_LEAGUE_GROUP_DESC") or "%s", Rule))
end

return GuildBattleOutRankingChange_Item
