local GuildBattleOutFlag_Item = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.OutPanel.GuildBattleOutFlag_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildBattleOutResult_Item : UIListItem
---@field view GuildBattleOutResult_ItemBlueprint
local GuildBattleOutResult_Item = DefineClass("GuildBattleOutResult_Item", UIComponent)

local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"

GuildBattleOutResult_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleOutResult_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleOutResult_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildBattleOutResult_Item:InitUIComponent()
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_Item_1Com = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item_1, GuildBattleOutFlag_Item)
    ---@type GuildBattleOutFlag_Item
    self.wBP_GuildBattleOutFlag_ItemCom = self:CreateComponent(self.view.WBP_GuildBattleOutFlag_Item, GuildBattleOutFlag_Item)
end

---UI事件在这里注册，此处为自动生成
function GuildBattleOutResult_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleOutResult_Item:InitUIView()
end

---面板打开的时候触发
function GuildBattleOutResult_Item:OnRefresh(...)

end

---设置左右公会
---@param GuildName_1 string
---@param GuildName_2 string
---@param bFinished boolean
---@param bWinnder boolean
---@param Index number 结算界面所处的位置
function GuildBattleOutResult_Item:SetGuild(Guild_1, Guild_2, bStarted, bFinished, bWinner, Index)
	local RankL = -1
	local RankR = -1
	for Ranking, GuildIndex in pairs(Game.GuildLeagueSystem.model.guildBattleRankCache) do
		if GuildIndex == Guild_1.Index then
			RankL = Ranking
		elseif GuildIndex == Guild_2.Index then
			RankR = Ranking
		end
	end
	local RuleL = Game.GuildLeagueSystem.model.guildBattleRuleCache[RankL] or 0
	local RuleR = Game.GuildLeagueSystem.model.guildBattleRuleCache[RankR] or 0
	--左侧
	local GuildName_1 = Guild_1.name or ""
	local GuildBadgeIndex_1 = Guild_1.badgeIndex or 1
	local GuildBadge_1 = Guild_1.badgeFrameId or 1002
	self.wBP_GuildBattleOutFlag_Item_1Com:SetAsRegularPostEvent((GuildName_1 == "" and true), RankL)
	self.wBP_GuildBattleOutFlag_Item_1Com:SetGuildInfo(GuildName_1, GuildBadgeIndex_1, GuildBadge_1, RuleL)
	--右侧
	local GuildName_2 = Guild_2.name or ""
	local GuildBadgeIndex_2 = Guild_2.badgeIndex or 1
	local GuildBadge_2 = Guild_2.badgeFrameId or 1002
	self.wBP_GuildBattleOutFlag_ItemCom:SetAsRegularPostEvent((GuildName_2 == "" and true), RankR)
	self.wBP_GuildBattleOutFlag_ItemCom:SetGuildInfo(GuildName_2, GuildBadgeIndex_2, GuildBadge_2, RuleR)
end

---设置战斗状态
---@param bStarted boolean
---@param bFinished boolean
---@param Winnder number
function GuildBattleOutResult_Item:SetResult(bStarted, bFinished, Winner)
	local State = 0
	local bFlip = false
	if not bStarted and not bFinished and (not Winner or Winner == 0 or Winner == -1) then
		self.view.VX_Panel_BlueBg:SetVisibility(ESlateVisibility.Collapsed)
		State = 0
		self.view.Text_State:SetText(StringConst.Get("GUILD_LEAGUE_ACTIVITY_PANEL_NOT_START_DESC"))
		self.view.VX_Panel_YellowBg:SetVisibility(ESlateVisibility.Collapsed)
	elseif bStarted and not bFinished and (not Winner or Winner == 0 or Winner == -1) then
		self.view.VX_Panel_BlueBg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		State = 1
		self.view.Text_State:SetText(StringConst.Get("GUILD_LEAGUE_ACTIVITY_PANEL_IN_PROGRESS_DESC"))
		self.view.VX_Panel_YellowBg:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.VX_Panel_BlueBg:SetVisibility(ESlateVisibility.Collapsed)
		State = 2
		bFlip = (Winner == 2) and true
		self.view.Text_Win:SetText(bFlip and StringConst.Get("GUILD_LEAGUE_ACTIVITY_PANEL_LOSE_DESC") or 
			StringConst.Get("GUILD_LEAGUE_ACTIVITY_PANEL_WIN_DESC"))
		self.view.Text_Lose:SetText(bFlip and StringConst.Get("GUILD_LEAGUE_ACTIVITY_PANEL_WIN_DESC") or 
			StringConst.Get("GUILD_LEAGUE_ACTIVITY_PANEL_LOSE_DESC"))
		self.view.VX_Panel_YellowBg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
	self.userWidget:Event_UI_State(State, bFlip)
end

return GuildBattleOutResult_Item
