local ESlateVisibility = import("ESlateVisibility")
local USlateBlueprintLibrary = import("SlateBlueprintLibrary")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildBattleTagLineDrag : UIComponent
---@field view GuildBattleTagLineDragBlueprint
local GuildBattleTagLineDrag = DefineClass("GuildBattleTagLineDrag", UIComponent)

GuildBattleTagLineDrag.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleTagLineDrag:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleTagLineDrag:InitUIData()
	--拖拽起始位置
	self.dragStartLocation = nil
	--拖拽当前位置
	self.dragEndLocation = nil
end

--- UI组件初始化，此处为自动生成
function GuildBattleTagLineDrag:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildBattleTagLineDrag:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleTagLineDrag:InitUIView()
end

---组件刷新统一入口
function GuildBattleTagLineDrag:Refresh(...)
end

-- 显隐
function GuildBattleTagLineDrag:SetDragLineVisible(bVisible)
	self.userWidget:SetVisibility(bVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

-- 设置拖拽开始位置,结束拖拽时要重新设置为nil
function GuildBattleTagLineDrag:SetDragStartLocation(location)
	if not location then
		self.dragStartLocation = nil
		return
	end
	Log.DebugFormat("GuildBattleTagLineDrag: SetDragStartLocation %s", location)
	self.dragStartLocation = location
	self.userWidget.Slot:SetPosition(self.dragStartLocation)
	self.userWidget.Slot:SetSize(FVector2D(100, 10))
end


-- 设置拖拽结束点位置，实时的mouseMove位置
function GuildBattleTagLineDrag:UpdateDragLinePosition(panelGeometry, screenPos)
	if not self.dragStartLocation then
		return
	end
	self.dragEndLocation = USlateBlueprintLibrary.AbsoluteToLocal(panelGeometry, screenPos)

	--设置拖拽线的中心位置
	local realLocation = (self.dragStartLocation + self.dragEndLocation)/2
	self.userWidget.Slot:SetPosition(realLocation)

	local lineSize = self.dragEndLocation - self.dragStartLocation

	--根据两点角度，计算旋转
	local xDistance = lineSize.X
	local yDistance = lineSize.Y
	local distance = math.sqrt(xDistance * xDistance + yDistance * yDistance)
	--Log.DebugFormat("size y is %.2f startLocation is %s endLocation is %s", distance, tostring(self.dragStartLocation), tostring(self.dragEndLocation))

	self.userWidget.Slot:SetSize(FVector2D(100, distance))

	local cosRadian = (xDistance * 1 + yDistance * 0) / (distance * 1)
	local angle = math.acos(cosRadian) * 180 / math.pi
	local aCrossB = xDistance * 0 - 1 * yDistance
	if aCrossB > 0 then
		--逆时针
		angle = angle * -1
	else
		--顺时针
	end

	--设置拖拽线的旋转
	self.userWidget:SetRenderTransformAngle(angle + 90)
end

return GuildBattleTagLineDrag
