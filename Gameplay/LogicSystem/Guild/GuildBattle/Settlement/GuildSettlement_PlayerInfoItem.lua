local GuildSettlement_Head = kg_require("Gameplay.LogicSystem.Guild.GuildBattle.Settlement.GuildSettlement_Head")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class GuildSettlement_PlayerInfoItem : UIListItem
---@field view GuildSettlement_PlayerInfoItemBlueprint
local GuildSettlement_PlayerInfoItem = DefineClass("GuildSettlement_PlayerInfoItem", UIListItem)

GuildSettlement_PlayerInfoItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildSettlement_PlayerInfoItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildSettlement_PlayerInfoItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildSettlement_PlayerInfoItem:InitUIComponent()
    ---@type GuildSettlement_Head
    self.WBP_GuildSettlement_HeadCom = self:CreateComponent(self.view.WBP_GuildSettlement_Head, GuildSettlement_Head)
end

---UI事件在这里注册，此处为自动生成
function GuildSettlement_PlayerInfoItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildSettlement_PlayerInfoItem:InitUIView()
end

---面板打开的时候触发
function GuildSettlement_PlayerInfoItem:OnRefresh(params)
	local title = params.Title or ""
	local name = params.Name or ""
	local profession = params.ProfessionID
	local sex = params.Sex
	local bVictory = params.bWin or false
	local honor = params.Honor or 0
	self.view.Text_Title:SetText(title)
	self.view.Text_Name:SetText(name)
	self.WBP_GuildSettlement_HeadCom:OnRefresh({ProfessionID = profession, Sex = sex})
	self.WBP_GuildSettlement_HeadCom:SetVictory(bVictory)
	self.userWidget:BP_SetType(bVictory, honor)
end

return GuildSettlement_PlayerInfoItem
