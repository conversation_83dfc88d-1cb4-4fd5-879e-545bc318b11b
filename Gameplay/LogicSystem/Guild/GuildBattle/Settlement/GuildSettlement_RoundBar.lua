local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildSettlement_RoundBar : UIComponent
---@field view GuildSettlement_RoundBarBlueprint
local GuildSettlement_RoundBar = DefineClass("GuildSettlement_RoundBar", UIComponent)

GuildSettlement_RoundBar.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildSettlement_RoundBar:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildSettlement_RoundBar:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildSettlement_RoundBar:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildSettlement_RoundBar:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildSettlement_RoundBar:InitUIView()
end

---组件刷新统一入口
function GuildSettlement_RoundBar:Refresh(blueScore, redScore)
	self.view.Text_NumBlue:SetText(blueScore)
	self.view.Text_NumRed:SetText(redScore)
	local progress = blueScore / (blueScore + redScore)
	self.view.ProgressBar:SetPercent(progress)
	self:PlayAnimation(self.view.Ani_Percent)
end

return GuildSettlement_RoundBar
