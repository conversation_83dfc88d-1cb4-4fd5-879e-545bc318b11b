local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildSettlement_Title : UIComponent
---@field view GuildSettlement_TitleBlueprint
local GuildSettlement_Title = DefineClass("GuildSettlement_Title", UIComponent)

GuildSettlement_Title.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildSettlement_Title:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildSettlement_Title:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildSettlement_Title:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildSettlement_Title:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildSettlement_Title:InitUIView()

end

---组件刷新统一入口
---@param bVictory boolean 是否胜利
function GuildSettlement_Title:Refresh(bVictory, text)
	bVictory = bVictory or false
	self.userWidget:BP_SetState(bVictory)
	local nameTextData = {}
	if text then
		for i = 1, utf8.len(text) do
			table.insert(nameTextData, utf8.sub(text, i, i + 1))
		end
		local bigText = nameTextData[1]
		local normalText = table.concat(nameTextData, "", 2)
		if bVictory then
			self.view.Text_Big_01:SetText(bigText)
			self.view.Text_Normal_01:SetText(normalText)
		else
			self.view.Text_Big_02:SetText(bigText)
			self.view.Text_Normal_02:SetText(normalText)
		end
	end
end

return GuildSettlement_Title
