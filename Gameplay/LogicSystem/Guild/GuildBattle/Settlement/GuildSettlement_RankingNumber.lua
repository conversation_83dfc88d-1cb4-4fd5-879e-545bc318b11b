local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildSettlement_RankingNumber : UIComponent
---@field view GuildSettlement_RankingNumberBlueprint
local GuildSettlement_RankingNumber = DefineClass("GuildSettlement_RankingNumber", UIComponent)

GuildSettlement_RankingNumber.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildSettlement_RankingNumber:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildSettlement_RankingNumber:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildSettlement_RankingNumber:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildSettlement_RankingNumber:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildSettlement_RankingNumber:InitUIView()
end

---组件刷新统一入口
function GuildSettlement_RankingNumber:Refresh(number, isTop)
	number = number or 0
	self.view.Text_Kill:SetText(number)
	isTop = isTop or false
	self.userWidget:BP_IsTop(isTop)
end

return GuildSettlement_RankingNumber
