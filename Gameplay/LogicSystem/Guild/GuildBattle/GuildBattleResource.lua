local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local ESlateVisibility = import("ESlateVisibility")
---@class GuildBattleResource : UIComponent
---@field view GuildBattleResourceBlueprint
local GuildBattleResource = DefineClass("GuildBattleResource", UIComponent)

GuildBattleResource.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleResource:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleResource:InitUIData()
	self.fxTimer = nil --加减效果的计时器

	self.curShowResource = 0--当前显示的资源值
end

--- UI组件初始化，此处为自动生成
function GuildBattleResource:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildBattleResource:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleResource:InitUIView()
end

---组件刷新统一入口
function GuildBattleResource:Refresh(guildLeagueResource)
	self.curShowResource = guildLeagueResource
	self:RefreshResource()
end

function GuildBattleResource:OnClose()
	self:DestroyTimer()

end

--- 销毁加减效果计时器
function GuildBattleResource:DestroyTimer()
	if self.fxTimer then
		Game.TimerManager:StopTimerAndKill(self.fxTimer)
		self.fxTimer = nil
	end
end

--- 增减数值,  endCallBack士气值相关的回调
function GuildBattleResource:RefreshResourceInfo(count, endCallBack)
	local delta = count - self.curShowResource
	if delta > 0 then
		self.view.Text_Add:SetText("+" .. math.floor(math.abs(delta)))
		self:PlayAnimation(self.view.Ani_Add_Loop)
	else
		self.view.Text_Subtract:SetText("-" .. math.floor(math.abs(delta)))
		self:PlayAnimation(self.view.Ani_Subtract_Loop)
	end

	self:DestroyTimer()
	local GuildLeagueResource = count
	local showDelta = math.floor((GuildLeagueResource - self.curShowResource) / 5)
	
	local n = 1
	self.fxTimer = Game.TimerManager:CreateTimerAndStart(function()
		self:DynamicRefreshResource(showDelta)
		n = n + 1
	end, 100, 5, nil, nil, nil, endCallBack)
end

--- 动态刷新，用于表现资源变化
function GuildBattleResource:DynamicRefreshResource(delta)
	self.curShowResource = self.curShowResource + delta
	self:RefreshResource()
end

--- 刷新显示资源数
function GuildBattleResource:RefreshResource()
	self.view.KText_ResNum_lua:SetText(self.curShowResource)
end

--- 设置显隐
function GuildBattleResource:SetVisibility(isShow)
	if isShow then
		self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

return GuildBattleResource
