local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local const = kg_require("Shared.Const")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local USlateBlueprintLibrary = import("SlateBlueprintLibrary")
local ESlateVisibility = import("ESlateVisibility")

---@type P_MapTagView
local P_MapTagView = kg_require("Gameplay.LogicSystem.MapV2.P_MapTagView")

---@class GuildBattleMapBoard : UIComponent
---@field view GuildBattleMapBoardBlueprint
local GuildBattleMapBoard = DefineClass("GuildBattleMapBoard", UIComponent)

GuildBattleMapBoard.eventBindMap = {
	[EEventTypesV2.ON_GUILD_LEAGUE_RET_USE_QUICK_INSTRUCTION] = "OnUseQuickInstruction",
	[EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_SELECTED] = "OnStrategySelected",
	[EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_UNSELECTED] = "OnStrategyUnSelected",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildBattleMapBoard:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildBattleMapBoard:InitUIData()
	--小地图是否显示快捷信息动效
	self.bShowInstructionAnim = false

	--快捷信息ID到动效的映射
	self.instructionAnim = {}
	self.instructionAnim[const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_ATTACK] = "Ani_Tips_red"
	self.instructionAnim[const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_RETREAT] = "Ani_Tips_blue"
	self.instructionAnim[const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_GATHER] = "Ani_Tips_green"
	self.instructionAnim[const.GUILD_LEAGUE_COMMON_COMMAND.COMMON_WARNING] = "Ani_Tips_yellow"

	--当前分配的动效Widget索引
	self.animWidgetIndex = 0
	self.animWidget = {
		self.view.WBP_GuildBattleWaveTips1_lua,
		self.view.WBP_GuildBattleWaveTips2_lua,
		self.view.WBP_GuildBattleWaveTips3_lua,
		self.view.WBP_GuildBattleWaveTips4_lua,
	}
	self.bDragTag = false -- 是否拖拽tag
end

--- UI组件初始化，此处为自动生成
function GuildBattleMapBoard:InitUIComponent()
	-- todo: 地图迭代后处理
	self.MapTagLayer = P_MapTagView.CreateMapTagLayerNew(self.view.C7MapTagLayer_lua, self, "GuildMapTagLayer")
end

---UI事件在这里注册，此处为自动生成
function GuildBattleMapBoard:InitUIEvent()
    self:AddUIEvent(self.view.Img_Bg_lua.OnMouseButtonDownEvent, "on_Img_Bg_lua_MouseButtonDownEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildBattleMapBoard:InitUIView()
end

---组件刷新统一入口
function GuildBattleMapBoard:Refresh(...)
	self.MapTagLayer:GetUIBase():RefreshDataByMapConfigID(20)
	self.MapTagLayer:GetUIBase():EnableTick()
	self.view.Img_Mask:SetVisibility(ESlateVisibility.Collapsed)
end

---@param myGeometry FGeometry
---@param mouseEvent FPointerEvent

--- 此处为自动生成
function GuildBattleMapBoard:on_Img_Bg_lua_MouseButtonDownEvent(myGeometry, mouseEvent)
	Log.DebugFormat("OnMapItemBgImageMouseBtnDown")
	-- todo:做成事件处理
	-- 指令是待删除状态时，点击其他区域变回正常状态
	local GuildLeagueTagComponent = self:GetGuildLeagueTagComponent()
	if GuildLeagueTagComponent then
		for taskID, tagInfo in pairs(GuildLeagueTagComponent.battleTagList) do
			local tagComponent = GuildLeagueTagComponent.parent:GetComponentByTaskID(taskID)
			if tagComponent and tagComponent.btnType == tagComponent.stateRemove then
				--如果是删除状态，需要切换回正常状态
				tagComponent:setBtnType(tagComponent.stateNormal)
				--handled = true
			end
		end
	end
end

--region 事件回调

--- 快捷指令使用事件相应
function GuildBattleMapBoard:OnUseQuickInstruction(sendInfo, quickInstruction, absolutePosition)
	if not self.bShowInstructionAnim then
		return
	end
	Log.DebugFormat("GuildBattleMapBoard:OnUseQuickInstruction")
	self:PlayInstructionAnim(quickInstruction, absolutePosition)
end
--endregion

--region 动画处理

--- 设置是否显示快捷信息动效
function GuildBattleMapBoard:SetShowInstructionAnim(bShow)
	self.bShowInstructionAnim = bShow
end

--- 在对应位置播放对应动效，支持多个Widget播放
function GuildBattleMapBoard:PlayInstructionAnim(quickInstruction, absolutePosition)
	if not self.bShowInstructionAnim then
		return
	end
	local widget = self:GetWaveWidget()
	local anim = widget[self.instructionAnim[quickInstruction]]
	if anim == nil then
		return
	end

	--位置
	local relativePosRatio = self.MapTagLayer:GetUIBase().widget:GetWidgetOffsetByWorldLocation(absolutePosition)
	local mapSize = USlateBlueprintLibrary.GetLocalSize(self.userWidget:GetCachedGeometry())
	local relativePos = FVector2D(mapSize.X * relativePosRatio.X, mapSize.Y * relativePosRatio.Y)
	widget.Slot:SetPosition(relativePos)
	Log.DebugFormat("relativePosRatio is %s relativePos is %s", relativePosRatio, relativePos)
	
	--图标
	local tableData = Game.TableData.GetGuildLeagueCommonCommandDataRow(quickInstruction)
	if tableData then
		local iconPath = Game.UIIconUtils.getIcon(tableData.Icon)
		self:SetImage(widget.VX_Icon_lua, iconPath)
	end
	
	--动效
	self:PlayAnimation(anim, nil, widget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

--- 获取WaveWidget，每次获取则索引+1
function GuildBattleMapBoard:GetWaveWidget()
	self.animWidgetIndex = (self.animWidgetIndex) % #self.animWidget + 1
	return self.animWidget[self.animWidgetIndex]
end

--- 获取地图tagComponent
function GuildBattleMapBoard:GetGuildLeagueTagComponent()
	return self.MapTagLayer:GetUIBase().controlcomponents.GuildLeagueTagComponent
end

--- 将控件偏移量反投影到世界坐标位置
function GuildBattleMapBoard:DeProjectWidgetOffsetToWorldLocation(relativeCoordinate)
	return self.MapTagLayer:GetUIBase():DeprojectWidgetOffsetToWorldLocation(relativeCoordinate)
end

--- 设置地图是否是可交互的
function GuildBattleMapBoard:SetMapTagLayerEnableInteraction(bEnable)
	self:GetGuildLeagueTagComponent().bInGuildBattleMap = bEnable
end

--- 获取MapTag2dPos
function GuildBattleMapBoard:GetMap2DPos(relativeCoordinate)
	return self:DeProjectWidgetOffsetToWorldLocation(relativeCoordinate)
end

--- 是否拖拽tag
function GuildBattleMapBoard:SetDragTag()
	return self.bDragTag
end

function GuildBattleMapBoard:OnClose()
	self.MapTagLayer:GetUIBase():DisableTick()
	self.MapTagLayer:GetUIBase():ClearAllTags()
end

--- 策略选中时
function GuildBattleMapBoard:OnStrategySelected()
	if not self.bShowInstructionAnim then
		return
	end
	--- 策略选中需要显示遮罩
	self.view.Img_Mask:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

--- 策略取消选中时
function GuildBattleMapBoard:OnStrategyUnSelected()
	if not self.bShowInstructionAnim then
		return
	end
	--- 策略取消选中需要隐藏遮罩
	self.view.Img_Mask:SetVisibility(ESlateVisibility.Collapsed)
end
--endregion


return GuildBattleMapBoard
