local GuildOutCreateIconLarge = kg_require("Gameplay.LogicSystem.Guild.Outside_2.Create.GuildOutCreateIconLarge")
local ComBarNew = kg_require("Gameplay.LogicSystem.Common.Bar.ComBarNew")
local const = kg_require("Shared.Const")
local stringConst = require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")
local GuildInBuildBtn = kg_require("Gameplay.LogicSystem.Guild.Inside_2.Build.GuildInBuildBtn")
local ComBtnSwitch = kg_require("Gameplay.LogicSystem.Common.Button.ComBtnSwitch")
local ComCurrency = kg_require("Gameplay.LogicSystem.Common.Tag.ComCurrency")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildInBuildMain : UIComponent
---@field view GuildInBuildMainBlueprint
local GuildInBuildMain = DefineClass("GuildInBuildMain", UIComponent)

GuildInBuildMain.Icon_Penny = Enum.EGuildConstIntData.GUILD_FUND

GuildInBuildMain.eventBindMap = {
    [EEventTypesV2.RECEIVE_GUILD_INFO] = "OnGetGuildInfo",
    [EEventTypesV2.RECEIVE_GUILD_BUILDINGS] = "OnGetBuildingInfos",
    
    [EEventTypesV2.CHANGE_GUILD_ANNOUNCE_SUCCESS] = "RefreshGuildInfo",
    [EEventTypesV2.GUILD_CHANGE_NAME_SUCCESS] = "RefreshGuildInfo",
    [EEventTypesV2.ON_GUILD_BADGE_CHANGE] = "RefreshGuildInfo",
    [EEventTypesV2.ON_SET_GUILD_BADGE_INDEX_SUCCESS] = "RefreshGuildInfo",
    [EEventTypesV2.ON_SET_GUILD_TYPE_SUCCESS] = "RefreshGuildInfo",
    -- [_G.EEventTypes.ON_GUILD_SCALE_TO_BIG_END] = "OnScaleToBigEnd",
    -- [_G.EEventTypes.ON_GUILD_SCALE_TO_SMALL_END] = "OnScaleToSmallEnd",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInBuildMain:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInBuildMain:InitUIData()
    self.guildPrincipalInfo = {}
    
    self.buildInfos = {}
    self.buildIDInSelected = 0      -- 当前选中的建筑id
    self.allBuildIndex = {}         -- 所有建筑id
    self.buildEntryComponents = {}
    self.showBuild = false
end

--- UI组件初始化，此处为自动生成
function GuildInBuildMain:InitUIComponent()
    ---@type GuildOutCreateIconLarge
    self.WBP_GuildIcon_luaCom = self:CreateComponent(self.view.WBP_GuildIcon_lua, GuildOutCreateIconLarge)
    ---@type ComBarNew
    self.WBP_ComBarNew_luaCom = self:CreateComponent(self.view.WBP_ComBarNew_lua, ComBarNew)
    ---@type GuildInBuildBtn
    self.WBP_BuildBtnMain_luaCom = self:CreateComponent(self.view.WBP_BuildBtnMain_lua, GuildInBuildBtn)
    ---@type GuildInBuildBtn
    self.WBP_BuildBtnMoney_luaCom = self:CreateComponent(self.view.WBP_BuildBtnMoney_lua, GuildInBuildBtn)
    ---@type GuildInBuildBtn
    self.WBP_BuildBtnLibrary_luaCom = self:CreateComponent(self.view.WBP_BuildBtnLibrary_lua, GuildInBuildBtn)
    ---@type GuildInBuildBtn
    self.WBP_BuildBtnPub_luaCom = self:CreateComponent(self.view.WBP_BuildBtnPub_lua, GuildInBuildBtn)
    ---@type GuildInBuildBtn
    self.WBP_BuildBtnSquare_luaCom = self:CreateComponent(self.view.WBP_BuildBtnSquare_lua, GuildInBuildBtn)
    ---@type ComBtnSwitch
    self.WBP_ComBtnSwitchVisit_luaCom = self:CreateComponent(self.view.WBP_ComBtnSwitchVisit_lua, ComBtnSwitch)
    ---@type ComCurrency
    self.WBP_ComCurrency_luaCom = self:CreateComponent(self.view.WBP_ComCurrency_lua, ComCurrency)

    ---@type table<number, GuildInBuildBtn>
    self.buildEntryComponents = {
        [const.GUILD_BUILDING_TYPE.MAIN] = self.WBP_BuildBtnMain_luaCom,
        [const.GUILD_BUILDING_TYPE.PUB] = self.WBP_BuildBtnPub_luaCom,
        [const.GUILD_BUILDING_TYPE.VAULT] = self.WBP_BuildBtnMoney_luaCom,
        [const.GUILD_BUILDING_TYPE.SCHOOL] = self.WBP_BuildBtnLibrary_luaCom,
        [const.GUILD_BUILDING_TYPE.SHOP] = self.WBP_BuildBtnSquare_luaCom ,
    }
    self.buildWidget = {
        [const.GUILD_BUILDING_TYPE.MAIN] = self.view.WBP_BuildBtnMain_lua,
        [const.GUILD_BUILDING_TYPE.PUB] = self.view.WBP_BuildBtnPub_lua,
        [const.GUILD_BUILDING_TYPE.VAULT] = self.view.WBP_BuildBtnMoney_lua,
        [const.GUILD_BUILDING_TYPE.SCHOOL] = self.view.WBP_BuildBtnLibrary_lua,
        [const.GUILD_BUILDING_TYPE.SHOP] = self.view.WBP_BuildBtnSquare_lua,
    }
    self.buildStartPosition = {
        [const.GUILD_BUILDING_TYPE.MAIN] = {-332,-299},
        [const.GUILD_BUILDING_TYPE.PUB] = {-361,180},
        [const.GUILD_BUILDING_TYPE.VAULT] = {-74,-138},
        [const.GUILD_BUILDING_TYPE.SCHOOL] = {-750,-83},
        [const.GUILD_BUILDING_TYPE.SHOP] = {26,243},
    }
    self.buildEndPosition = {
        [const.GUILD_BUILDING_TYPE.MAIN] = {-302,-299},
        [const.GUILD_BUILDING_TYPE.PUB] = {-350,240},
        [const.GUILD_BUILDING_TYPE.VAULT] = {25,-138},
        [const.GUILD_BUILDING_TYPE.SCHOOL] = {-710,-83},
        [const.GUILD_BUILDING_TYPE.SHOP] = {50,243},
    }
end

---UI事件在这里注册，此处为自动生成
function GuildInBuildMain:InitUIEvent()
    self:AddUIEvent(self.WBP_BuildBtnMain_luaCom.onClickedEntry, "onWBP_BuildBtnMain_luaComClickedEntry")
    self:AddUIEvent(self.WBP_BuildBtnSquare_luaCom.onClickedEntry, "onWBP_BuildBtnSquare_luaComClickedEntry")
    self:AddUIEvent(self.WBP_BuildBtnLibrary_luaCom.onClickedEntry, "onWBP_BuildBtnLibrary_luaComClickedEntry")
    self:AddUIEvent(self.WBP_BuildBtnMoney_luaCom.onClickedEntry, "onWBP_BuildBtnMoney_luaComClickedEntry")
    self:AddUIEvent(self.WBP_BuildBtnPub_luaCom.onClickedEntry, "onWBP_BuildBtnPub_luaComClickedEntry")
    
    self:AddUIEvent(self.view.WBP_ComBtnCloseNew_lua.OnClicked, "OnClickBuildClose")
    self:AddUIEvent(self.view.WBP_BuildLevelUpTipsBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickLevelUpTips")
    self:AddUIEvent(self.view.WBP_LevelUpBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickLevelUp")
    self:AddUIEvent(self.view.WBP_GuildInfoEditBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickEdit")
    self:AddUIEvent(self.view.WBP_GuildComTipsBtnRename.Btn_ClickArea_lua.OnClicked, "OnClickChat")
    self:AddUIEvent(self.view.WBP_ReportBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickReport")
    self:AddUIEvent(self.view.WBP_ReturnGuildBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickReturn")
    self:AddUIEvent(self.view.WBP_BuildLevelUpBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickEnterBuildLevelUp")
    self:AddUIEvent(self.view.WBP_MoneyTipsBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickFundTips")
    self:AddUIEvent(self.view.WBP_ActiveTipsBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickActiveTips")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInBuildMain:InitUIView()
    self.view.WBP_ComBtnCloseNew_lua:Event_UI_Style(1)
    self.view.WBP_GuildNumber_lua:Event_UI_Style(false)
    -- self.view.WBP_GuildTag_lua:Event_UI_Style(false)
    -- 公会领地与排名标签 暂时隐藏
    self.view.WBP_GuildTag_lua:SetVisibility(ESlateVisibility.Collapsed)
    self.view.WBP_ReportBtn_lua:Event_UI_Style(4)
    self.view.WBP_GuildInfoEditBtn_lua:Event_UI_Style(3)
    self.view.WBP_ReturnGuildBtn_lua:Event_UI_Style(1)
    self.view.WBP_ReturnGuildBtn_lua.TB_Title_lua:SetText(stringConst.Get("GUILD_BACK_TO_GUILD_RESIDENCE"))
    self.view.WBP_LevelUpBtn_lua:Event_UI_Style(2)
    self.view.WBP_LevelUpBtn_lua.TB_Title_lua:SetText(stringConst.Get("GUILD_BUILD_UPGRADE"))
    self.WBP_ComBtnSwitchVisit_luaCom:SetData({
        IsOpen = false,
        OnSwitchCheckCallback = function() 
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_VISIT_LOCKED)
            return false
        end,
        OnSwitchCallback = function(isOpen) self:OnClickVisitSwitch(isOpen) end,
        Value = {"关","开"}
    })
    self.view.WBP_BuildLevelUpTipsBtn_lua:Event_UI_Style(0)
    self.view.WBP_MoneyTipsBtn_lua:Event_UI_Style(1)
    self.view.WBP_ActiveTipsBtn_lua:Event_UI_Style(1)
    
    self.view.WBP_GuildEventBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
    self.view.WBP_WeekInformationBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
    self.view.WBP_TargetBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
    
    self.view.WBP_BuildLevelUpBtn_lua:Event_UI_Style(3, 2, false)
    self.view.WBP_BuildLevelUpBtn_lua.Text_IconWord_lua:SetText("建筑升级")
    self.view.WBP_GuildComTipsBtnRename:Event_UI_Style(5)

    --if Game.GuildSystem:HasRight(const.GUILD_RIGHT.INFO_SET) then
    self.view.WBP_GuildInfoEditBtn_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --else
    --    self.view.WBP_GuildInfoEditBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
    --end
    -- self:PlayAnimation(self.view.WBP_GuildInBuildBg.Ani_Fadein, nil, self.view.WBP_GuildInBuildBg)
end

---面板打开的时候触发
function GuildInBuildMain:OnRefresh(...)
    self:PlayAnimation(self.view.Ani_Fadein)
    Game.GuildSystem.sender:getGuildInfo()
    Game.GuildSystem.sender:getGuildBuildings()
    self:InitData()
    self:RefreshGuildInfo()
    self:RefreshBuildInfo()
    self:RefreshBuildingList()
    self:RefreshPage()
end

function GuildInBuildMain:InitData()
    self.guildPrincipalInfo = {}
end

function GuildInBuildMain:RefreshGuildInfo()
    self.view.Text_GuildNameTitle_lua:SetText(Game.me.guildName)
    self.view.WBP_GuildNumber_lua.TB_Num_lua:SetText(self.guildPrincipalInfo.shortId or "")
    self.view.Text_GuildLeaderName_lua:SetText(self.guildPrincipalInfo.leaderName or "")
    self.WBP_GuildIcon_luaCom:RefreshData(Game.me.guildName, Game.me.guildBadgeIndex, Game.me.guildBadgeFrameId)
    self.view.Text_OfficialNum_lua:SetText(
        tostring(Game.GuildSystem:GetMemberNum()) .. "/" .. tostring(Game.GuildSystem:GetMaxMemberNum())
    )
    self.view.Text_ApprenticeNum_lua:SetText(
        tostring(Game.GuildSystem:GetApprenticeNum()) .. "/" .. tostring(Game.GuildSystem:GetMaxApprenticeNum())
    )
    self.view.Text_MoneyNum_lua:SetText(self.guildPrincipalInfo.funds or 0)
    self.view.Text_ActiveNum_lua:SetText(self.guildPrincipalInfo.liveValue or 0)
    self.view.Text_Declaration_lua:SetText(self.guildPrincipalInfo.declaration or "")
    self.view.Text_Lv_lua:SetText(self.guildPrincipalInfo.lv or 1)
end

function GuildInBuildMain:RefreshBuildingList()
    for buildId, buildComponent in pairs(self.buildEntryComponents) do
        buildComponent:RefreshData(buildId, self.buildInfos[buildId])
    end
    self:OnScaleToSmallEnd()
end

function GuildInBuildMain:RefreshBuildInfo()
    local buildData = self.buildInfos[self.buildIDInSelected]
    local buildTableData = Game.TableData.GetGuildBuildingDataRow(self.buildIDInSelected)
    if not buildTableData then
        return
    end
    local curBuildLv = Game.GuildSystem:GetBuildingLevel(self.buildIDInSelected)
    --if self.buildIDInSelected == const.GUILD_BUILDING_TYPE.MAIN then
    --    curBuildLv = buildData and buildData.lv or 1
    --else
    --    curBuildLv = buildData and buildData.lv or 0
    --end
    
    self.view.Text_BuildName_lua:SetText(buildTableData.BuildingName)
    self.view.WBP_GuildComIconBtn_lua:Event_UI_Style(
        3, 0, curBuildLv == Game.GuildSystem:GetBuildingMaxLv(self.buildIDInSelected)
    )
    self.view.WBP_GuildComIconBtn_lua.Text_Level_lua:SetText(curBuildLv)
    self.view.WBP_GuildComIconBtn_lua.Text_IconWord_lua:SetText("")
    self.view.Text_BuildDes_lua:SetText(self:GetBuildingEffectText(self.buildIDInSelected, curBuildLv))
    self.view.Text_BuildFunction_lua:SetText(self:GetBuildingEffectText(self.buildIDInSelected, curBuildLv))
    --region 升级条件
    local upgradeInfo = Game.TableData.GetGuildUpgradeDataRow(curBuildLv + 1)
    if not upgradeInfo then
        self.view.Canvas_LevelUp_lua:SetVisibility(ESlateVisibility.Collapsed)
        return
    end
    self.view.Canvas_LevelUp_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.view.Text_BuildLevelUpFunction_lua:SetText(self:GetBuildingEffectText(self.buildIDInSelected, curBuildLv + 1))
    -- 条件描述
    self.view.Text_LevelConditionTitle_lua:SetText(stringConst.Get("GUILD_BUILD_CONDITION"))
    if self.buildIDInSelected == const.GUILD_BUILDING_TYPE.MAIN then
        self.view.Text_Condition_lua:SetText(
            string.format(stringConst.Get("GUILD_BUILD_SUB_CONDITION"), 2, upgradeInfo.MainUpgradeCondition[2])
        )
    else
        self.view.Text_Condition_lua:SetText(
            string.format(stringConst.Get("GUILD_BUILD_MAIN_CONDITION"),
                upgradeInfo.SubUpgradeCondition[self.buildIDInSelected])
        )
    end
    -- 资金条件
    self.view.Text_LevelMoneyTitle_lua:SetText(stringConst.Get("GUILD_BUILD_FUNDS"))
    local guildPrincipalInfoFunds = self.guildPrincipalInfo.funds or 0
    local needMoney = upgradeInfo.UpgradeCost[self.buildIDInSelected]
    -- if self.buildIDInSelected == const.GUILD_BUILDING_TYPE.MAIN then
    --     needMoney = upgradeInfo.MainUpgradeFundsRequire or 1
    -- else
    --     needMoney = upgradeInfo.SubUpgradeFundsRequire[self.buildIDInSelected] or 1
    -- end
    if needMoney > guildPrincipalInfoFunds then
        self.view.Text_Money_lua:SetText(string.format(
            "<T_Red>%s</>/<T_Black>%s</>", tostring(guildPrincipalInfoFunds), tostring(needMoney)
        ))
    else
        self.view.Text_Money_lua:SetText(string.format(
            "<T_Black>%s</>/<T_Black>%s</>", tostring(guildPrincipalInfoFunds), tostring(needMoney)
        ))
    end
    -- 升级时间
    local totalTime = upgradeInfo.UpgradeTime and upgradeInfo.UpgradeTime[self.buildIDInSelected] or 1
    totalTime = totalTime * 1000
    self.view.Text_TimeWord_lua:SetText(
        Game.TimeUtils.FormatCountDownString(
            totalTime, true
        )
    )
    self:RefreshLevelUpTime()
    -- 升级按钮
    self.WBP_ComCurrency_luaCom:SetData(
        GuildInBuildMain.Icon_Penny, upgradeInfo.UpgradeCost[self.buildIDInSelected], nil, nil, nil, true, guildPrincipalInfoFunds
    )
    if curBuildLv == Game.GuildSystem:GetBuildingMaxLv(self.buildIDInSelected) then
        self.view.WBP_ComCurrency_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_LevelUpBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
    else
        local upGradeEndTime = buildData and buildData.endTime or 0
        if upGradeEndTime > 0 and upGradeEndTime * 1000 > _G._now() then
            self.view.WBP_ComCurrency_lua:SetVisibility(ESlateVisibility.Collapsed)
            self.view.WBP_LevelUpBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
        else
            self.view.WBP_ComCurrency_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.view.WBP_LevelUpBtn_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
    --endregion
end

function GuildInBuildMain:RefreshLevelUpTime()
    local buildData = self.buildInfos[self.buildIDInSelected] or {}
    local curBuildLv = Game.GuildSystem:GetBuildingLevel(self.buildIDInSelected)
    --if self.buildIDInSelected == const.GUILD_BUILDING_TYPE.MAIN then
    --    curBuildLv = buildData and buildData.lv or 1
    --else
    --    curBuildLv = buildData and buildData.lv or 0
    --end
    self.view.Text_TimeTitle_lua:SetText(stringConst.Get("GUILD_BUILD_TIME"))
    local endTime = buildData.endTime or 0
    endTime = endTime * 1000
    local upgradeInfo = Game.TableData.GetGuildUpgradeDataRow((buildData.lv or 1) + 1) or {}
    local totalTime = upgradeInfo.UpgradeTime and upgradeInfo.UpgradeTime[self.buildIDInSelected] or 1
    totalTime = totalTime * 1000
    if endTime - _G._now() > 0 then
        local leftTime = endTime - _G._now()
        self.view.Text_Time_lua:SetText(Game.TimeUtils.FormatCountDownString(
            leftTime, true
        ))
        self.WBP_ComBarNew_luaCom:SetProgressBarValue(math.min(1 - leftTime / totalTime, 1))
        self.view.WBP_ComBarNew_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.WBP_ComCurrency_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_LevelUpBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Text_TimeTitle_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Time_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_TimeTitle_lua:SetText("剩余时间")
    else
        self.view.Text_Time_lua:SetText(Game.TimeUtils.FormatCountDownString(
            totalTime, true
        ))
        self.WBP_ComBarNew_luaCom:SetProgressBarValue(0)
        if curBuildLv == Game.GuildSystem:GetBuildingMaxLv(self.buildIDInSelected) then
            self.view.WBP_ComCurrency_lua:SetVisibility(ESlateVisibility.Collapsed)
            self.view.WBP_LevelUpBtn_lua:SetVisibility(ESlateVisibility.Collapsed)
        else
            self.view.WBP_ComCurrency_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.view.WBP_LevelUpBtn_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
        self.view.Text_TimeTitle_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_ComBarNew_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Text_Time_lua:SetVisibility(ESlateVisibility.Collapsed)
    end
    self:StartTimer('TimeRemainCountDown', function() self:RefreshLevelUpTime() end, 500, 1)
end

function GuildInBuildMain:RefreshPage()
    if self.showBuild then
        self.view.Canvas_Main_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Canvas_BuildInfo_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.Canvas_Main_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Canvas_BuildInfo_lua:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function GuildInBuildMain:OnGetGuildInfo(guildPrincipalInfo)
    self.guildPrincipalInfo = guildPrincipalInfo
    self:RefreshGuildInfo()
    local guildId = Game.GameClientData:GetPlayerValue("GuildPopupShowId")
    if guildId ~= Game.me.guildId then
        Game.NewUIManager:OpenPanel("GuildInInvitationPopup_Panel")
        Game.GameClientData:SaveByPlayer("GuildPopupShowId", Game.me.guildId, true)
    end
    if Game.me.eid == self.guildPrincipalInfo.leaderId then
        self.view.WBP_GuildComTipsBtnRename:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.WBP_GuildComTipsBtnRename:SetVisibility(ESlateVisibility.Visible)
    end
end

function GuildInBuildMain:OnGetBuildingInfos(buildings)
    self.buildInfos = buildings
    self:RefreshBuildingList()
    self:RefreshBuildInfo()
end

function GuildInBuildMain:OnClickBuildBtn(buildId)
    if self.showBuild then
        self.showBuild = true
        if self.buildIDInSelected ~= buildId then
            if self.buildEntryComponents[self.buildIDInSelected] then
                self.buildEntryComponents[self.buildIDInSelected]:SetSelected(false)
            end
            self.buildIDInSelected = buildId
            self.buildEntryComponents[buildId]:SetSelected(true)
        end
        self:RefreshBuildInfo()
        self:RefreshPage()
        -- Game.EventSystem:Publish(_G.EEventTypes.ON_SELECT_GUILD_BUILD, buildId)
    else
        local buildData = Game.TableData.GetGuildBuildingDataRow(buildId)
        if buildData.UiJump < 0 then
            self:GetParent().WBP_TabList_luaCom:Sel(math.abs(buildData.UiJump))
        elseif buildData.UiJump > 0 then
            Game.UIJumpSystem:JumpToUI(buildData.UiJump)
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FUNCTION_NOT_OPEN)
        end
    end
end

function GuildInBuildMain:ProcessBuildJump()
    
end


--- 此处为自动生成
---@param value number
function GuildInBuildMain:onWBP_BuildBtnPub_luaComClickedEntry(buildId)
    self:OnClickBuildBtn(buildId)
end

--- 此处为自动生成
---@param value number
function GuildInBuildMain:onWBP_BuildBtnMoney_luaComClickedEntry(buildId)
    self:OnClickBuildBtn(buildId)
end

--- 此处为自动生成
---@param value number
function GuildInBuildMain:onWBP_BuildBtnLibrary_luaComClickedEntry(buildId)
    self:OnClickBuildBtn(buildId)
end

--- 此处为自动生成
---@param value number
function GuildInBuildMain:onWBP_BuildBtnSquare_luaComClickedEntry(buildId)
    self:OnClickBuildBtn(buildId)
end

--- 此处为自动生成
---@param value number
function GuildInBuildMain:onWBP_BuildBtnMain_luaComClickedEntry(buildId)
    self:OnClickBuildBtn(buildId)
end

function GuildInBuildMain:OnClickBuildClose()
    self.showBuild = false
    if self.buildEntryComponents[self.buildIDInSelected] then
        self.buildEntryComponents[self.buildIDInSelected]:SetSelected(false)
    end
    self.buildIDInSelected = nil
    -- Game.EventSystem:Publish(_G.EEventTypes.ON_EXIT_GUILD_BUILD_LEVEL_UP)
    -- for buildId, buildCom in pairs(self.buildEntryComponents) do
    --     buildCom:SetVisible(false)
    -- end
    self:RefreshPage()
end

function GuildInBuildMain:OnClickLevelUpTips()
    Game.TipsSystem:ShowTips(Enum.ETipsData.CLUB_BUILDINGUP_DESC, self.view.WBP_BuildLevelUpTipsBtn_lua.Btn_ClickArea_lua:GetCachedGeometry())
end

function GuildInBuildMain:OnClickFundTips()
    Game.TipsSystem:ShowTips(Enum.ETipsData.CLUB_FUNDS_DESC, self.view.WBP_MoneyTipsBtn_lua.Btn_ClickArea_lua:GetCachedGeometry())
end

function GuildInBuildMain:OnClickActiveTips()
    Game.TipsSystem:ShowTips(Enum.ETipsData.CLUB_ACTIVITY_DESC, self.view.WBP_ActiveTipsBtn_lua.Btn_ClickArea_lua:GetCachedGeometry())
end

function GuildInBuildMain:OnClickEnterBuildLevelUp()
    self.showBuild = true
    self:OnClickBuildBtn(const.GUILD_BUILDING_TYPE.MAIN)
    -- Game.EventSystem:Publish(_G.EEventTypes.ON_ENTER_GUILD_BUILD_LEVEL_UP)
    -- for buildId, buildCom in pairs(self.buildEntryComponents) do
    --     buildCom:SetVisible(false)
    -- end
end

function GuildInBuildMain:OnClickLevelUp()
    local buildData = Game.TableData.GetGuildBuildingDataRow(self.buildIDInSelected)
    if buildData.UiJump == 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FUNCTION_NOT_OPEN)
        return
    end
    if Game.GuildSystem:CheckUpgradeConditions(self.buildIDInSelected) then
        Game.GuildSystem.sender:upgradeBuilding(self.buildIDInSelected)
        local needMoney = 0
        local upgradeInfo = Game.TableData.GetGuildUpgradeDataRow(Game.GuildSystem:GetBuildingLevel(self.buildIDInSelected) + 1)
        if upgradeInfo then
            -- if self.buildIDInSelected == const.GUILD_BUILDING_TYPE.MAIN then
            --     needMoney = upgradeInfo.MainUpgradeFundsRequire or 0
            -- else
            --     needMoney = upgradeInfo.SubUpgradeFundsRequire[self.buildIDInSelected] or 0
            -- end
            needMoney = upgradeInfo.UpgradeCost[self.buildIDInSelected]
        end
        self.guildPrincipalInfo.funds = self.guildPrincipalInfo.funds - needMoney
    end
    Game.GuildSystem.sender:getGuildBuildings()
end

function GuildInBuildMain:OnClickVisitSwitch(isOpen)
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_VISIT_LOCKED)
end


function GuildInBuildMain:GetBuildingEffectText(buildID, curBuildLv)
    local buildTableData = Game.TableData.GetGuildBuildingDataRow(self.buildIDInSelected)
    if buildID == const.GUILD_BUILDING_TYPE.MAIN then
        return string.format(
            stringConst.Get(buildTableData.Description), curBuildLv * Game.TableData.GetConstDataRow("GUILD_BUILDING_UPGRADE")
        )
    elseif buildID == const.GUILD_BUILDING_TYPE.PUB then
        local formulaFunc = Game.FormulaManager.GetFormulaFuncByName("Guild_Total_Max_Member_NumFormula")
        return string.format(stringConst.Get(buildTableData.Description), formulaFunc(curBuildLv))
    elseif buildID == const.GUILD_BUILDING_TYPE.VAULT then
        local formulaFunc = Game.FormulaManager.GetFormulaFuncByName("Guild_Max_FundsFormula")
        return string.format(stringConst.Get(buildTableData.Description), formulaFunc(curBuildLv))
    elseif buildID == const.GUILD_BUILDING_TYPE.SCHOOL then
        if curBuildLv == 0 then
            return string.format(stringConst.Get(buildTableData.Description), 0)
        else
            return string.format(stringConst.Get(buildTableData.Description), 10)
        end
    elseif buildID == const.GUILD_BUILDING_TYPE.SHOP then
        local buildInfo = Game.TableData.GetGuildFuncDataRow(curBuildLv)
        if not buildInfo then
            buildInfo = Game.TableData.GetGuildFuncDataRow(Game.GuildSystem:GetBuildingMaxLv(buildID))
        end
        return string.format(stringConst.Get(buildTableData.Description), buildInfo.TotalMaxMemberNum)
    else
        return ""
    end
end

function GuildInBuildMain:OnClickEdit()
    Game.NewUIManager:OpenPanel(
        "GuildOutCreate_Panel", {
            guildType = self.guildPrincipalInfo.guildType, name = Game.me.guildName,
            declaration = self.guildPrincipalInfo.declaration or "",
            badgeIndex = Game.me.guildBadgeIndex, badgeFrameId = Game.me.guildBadgeFrameId,
        }, true
    )
end

function GuildInBuildMain:OnClickChat()
    if self.guildPrincipalInfo.leaderId then
        Game.ChatWhisperSystem:WhisperToPlayer(self.guildPrincipalInfo.leaderId)
    end
end

function GuildInBuildMain:OnClickReport()
    Game.ReportSystem:ShowReportUI(Enum.EReportType.Guild, 
		{ID = self.guildPrincipalInfo.id or "", 
		 shortId = self.guildPrincipalInfo.shortId or "",  
		 name = Game.me.guildName})
end

function GuildInBuildMain:OnClickReturn()
    Game.GuildSystem:EnterGuild()
end

function GuildInBuildMain:OnScaleToBigEnd()
    --local tableData = Game.TableData.GetGuildBuildingDataTable()
    for buildId, buildCom in pairs(self.buildEntryComponents) do
        buildCom:SetVisible(true)
        self.buildWidget[buildId].Slot:SetPosition(
            FVector2D(self.buildEndPosition[buildId][1], self.buildEndPosition[buildId][2])
        )
    end
end

function GuildInBuildMain:OnScaleToSmallEnd()
    --local tableData = Game.TableData.GetGuildBuildingDataTable()
    for buildId, buildCom in pairs(self.buildEntryComponents) do
        buildCom:SetVisible(true)
        self.buildWidget[buildId].Slot:SetPosition(
            FVector2D(self.buildStartPosition[buildId][1], self.buildStartPosition[buildId][2])
        )
    end
end


return GuildInBuildMain
