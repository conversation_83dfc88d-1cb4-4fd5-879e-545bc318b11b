local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local ItemReward = kg_require "Gameplay.LogicSystem.Item.ItemReward"
---@class GuildInActivityItem : UIComponent

local GuildInActivityItem = DefineClass("GuildInActivityItem", UIComponent)

GuildInActivityItem.JumpType = {
    UI = 1,     -- 界面跳转
    SCENE = 2   -- 场景跳转
}

GuildInActivityItem.eventBindMap = {
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInActivityItem:OnCreate()
    self.index = 0
    self.guildActivityIndex = 0
    self.rewardData = {}
    
    self.rewardList = BaseList.CreateList(
        self, BaseList.Kind.ComList, self.View.ComLL_Reward, ItemReward, "rewardList"
    )
    
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_GuildComBtn.Btn_ClickArea, self.OnClickGoto)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_TipsBtn.Btn_ClickArea, self.OnClickShowTips)
    
    self.View.WBP_TipsBtn:Event_UI_Style(1)
end

function GuildInActivityItem:RefreshData(index, guildActivityIndex)
    self.index = index
    self.guildActivityIndex = guildActivityIndex
    if guildActivityIndex == 0 then
        self.View:Event_UI_Style(0, true)
        return
    end
    self:RefreshPage()
end

function GuildInActivityItem:RefreshPage()
    local guildActivityData = Game.TableData.GetGuildClubActivityDataRow(self.guildActivityIndex)
    local activityData = Game.TableData.GetActivityDataRow(guildActivityData.ActivityID)
    -- TODO 先写死，目前只有俱乐部舞会
    -- if guildActivityData.ActivityID == 1013 then
        
    -- end
    -- 规模
    if guildActivityData.Scale == 1 then
        self.View.Text_Model:SetText("个人")
        self.View:Event_UI_Style(1, false)
    else
        self.View.Text_Model:SetText("集体")
        self.View:Event_UI_Style(0, false)
    end
    -- 奖励
    self.rewardData = {}
    for _, Rewards in pairs(Game.DropSystem.GetDropShowRewardData(activityData.DropDisplay)) do
        for _, V in ipairs(Rewards) do
            table.insert(self.rewardData, { itemID = V[1], count = V[2] })
        end
    end
    self.rewardList:SetData(#self.rewardData)
    -- 前往按钮
    local bIsOpen = Game.ActivitySystem:CheckActivityIsOpen(guildActivityData.ActivityID)
    if bIsOpen or self.guildActivityIndex == 2 then     -- TODO 先写死，等活动迭代
        self.View.WBP_GuildComBtn:Event_UI_Style(0)
        self.View.WBP_GuildComBtn.TB_Title:SetText("前往")
    else
        self.View.WBP_GuildComBtn:Event_UI_Style(3)
        self.View.WBP_GuildComBtn.TB_Title:SetText("暂未开启")
    end
    -- 时间
    if bIsOpen then
        self.View.WBP_TimeInfo:Event_UI_Style(1)
    else
        self.View.WBP_TimeInfo:Event_UI_Style(0)
    end
    self.View.WBP_TimeInfo.Text_SetWeekTime:SetText(guildActivityData.ActivityTimeDisplay)
    self.View.WBP_TimeInfo.Text_Number:SetText("")
    -- 其他
    self:SetImage(self.View.KImg_Bg, guildActivityData.ShowPicture)
    self:SetImage(self.View.Img_Title, guildActivityData.WordPicture)
end

function GuildInActivityItem:OnClickGoto()
    local guildActivityData = Game.TableData.GetGuildClubActivityDataRow(self.guildActivityIndex)
    if guildActivityData.JumpType == self.JumpType.UI then
        Game.UIJumpSystem:JumpToUI(guildActivityData.JumpPara)
    elseif guildActivityData.JumpType == self.JumpType.SCENE then
        local jumpParam = tostring(guildActivityData.JumpPara)
        local _, _, _, LevelID = Game.WorldDataManager:GetPositionByInsID(jumpParam, true)
        if LevelID and LevelID ~= 0 then
            Game.TeleportManager:RequestTeleport(jumpParam, LevelID)
        end
    end
end

function GuildInActivityItem:OnClickShowTips()
    Game.TipsSystem:ShowTips(guildActivityData.info, self.View.WBP_TipsBtn.Btn_ClickArea:GetCachedGeometry())
end

function GuildInActivityItem:OnRefresh_rewardList(widget, index)
    widget:FillItem(self.rewardData[index].itemID, true, Enum.EItemSelectType.NotSelected)
end

return GuildInActivityItem
