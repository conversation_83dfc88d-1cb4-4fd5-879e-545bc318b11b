local GuildInActivityItem = kg_require("Gameplay.LogicSystem.Guild.Inside_2.Activity.GuildInActivityItem")
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildInActivity : UIComponent
---@field view GuildInActivityBlueprint
local GuildInActivity = DefineClass("GuildInActivity", UIComponent)

GuildInActivity.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInActivity:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInActivity:InitUIData()
    self.activityData = {}
end

--- UI组件初始化，此处为自动生成
function GuildInActivity:InitUIComponent()
    self.activityListView = self:CreateComponent(self.view.ComLL_Activity_lua, UIBaseAdapter, "GuildInActivity")
    self.activityList = self.activityListView:CreateBaseList(GuildInActivityItem, ComList, "activityList")
end

---UI事件在这里注册，此处为自动生成
function GuildInActivity:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInActivity:InitUIView()
end

---面板打开的时候触发
function GuildInActivity:OnRefresh(...)
    self:InitData()
    -- 临时先加两个敬请期待
    table.insert(self.activityData, 0)
    table.insert(self.activityData, 0)
    self.activityList:SetData(#self.activityData)
end

function GuildInActivity:InitData()
    for index, activityData in ksbcpairs(Game.TableData.GetGuildClubActivityDataTable()) do
        if Game.TableData.GetActivityDataRow(activityData.ActivityID) then
            table.insert(self.activityData, index)
        end
    end
    table.sort(
        self.activityData,
        function(a, b) 
                return Game.TableData.GetGuildClubActivityDataRow(a).Sort < Game.TableData.GetGuildClubActivityDataRow(b).Sort 
            end
    )
    table.sort(
        self.activityData,
        function(a, b) 
                local guildActivityDataA = Game.ActivitySystem:CheckActivityIsOpen(Game.TableData.GetGuildClubActivityDataRow(a).ActivityID)
                local guildActivityDataB = Game.ActivitySystem:CheckActivityIsOpen(Game.TableData.GetGuildClubActivityDataRow(b).ActivityID)
                if guildActivityDataB and not guildActivityDataA then
                    return a > b
                else
                    return a < b
                end
            end
    )

end

function GuildInActivity:OnRefresh_activityList(widget, index)
    widget:RefreshData(index, self.activityData[index])
end

return GuildInActivity
