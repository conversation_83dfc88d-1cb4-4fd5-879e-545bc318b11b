local ESlateVisibility = import("ESlateVisibility")
local ActivityUtils = kg_require("Shared.Utils.ActivityUtils")
local ItemReward = kg_require "Gameplay.LogicSystem.Item.ItemReward"
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GuildInInvitationPopup_Panel : UIPanel
---@field view GuildInInvitationPopup_PanelBlueprint
local GuildInInvitationPopup_Panel = DefineClass("GuildInInvitationPopup_Panel", UIPanel)

GuildInInvitationPopup_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInInvitationPopup_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInInvitationPopup_Panel:InitUIData()
    self.rewardData = {}
end

--- UI组件初始化，此处为自动生成
function GuildInInvitationPopup_Panel:InitUIComponent()
    ---@type SocialHead
    self.WBP_SocialHead_luaCom = self:CreateComponent(self.view.WBP_SocialHead_lua, SocialHead)

    self.listView = self:CreateComponent(self.view.ComLL_Reward_lua, UIBaseAdapter, "GuildInInvitationPopup_Panel")
    self.rewardList = self.listView:CreateBaseList(ItemReward, ComList, "rewardList")
end

---UI事件在这里注册，此处为自动生成
function GuildInInvitationPopup_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.WBP_ComBtnCloseNew_lua.Button_lua.OnClicked, "OnClickClose")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInInvitationPopup_Panel:InitUIView()
    -- 模拟点击空白处关闭逻辑，邀请卡片被关闭之后才生效
    self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Collapsed)
end

---面板打开的时候触发
function GuildInInvitationPopup_Panel:OnRefresh(...)
    self.view.Vx_Spine_lua:SetVisibility(ESlateVisibility.Visible)
    self.view.Canvas_Open_lua:SetVisibility(ESlateVisibility.Collapsed)
    local principalInfo = Game.GuildSystem.model.GuildPrincipalInfo
    self.view.Text_LeaderName_lua:SetText(principalInfo.leaderName or "")
    self.view.WBP_GuildName_lua:SetText(Game.me.guildName or "")
    self.WBP_SocialHead_luaCom:SetData({
        ProfessionID = principalInfo.leaderSchool,
        Level = principalInfo.leaderLv,
        OnClickedCallBack = function() self:OnClickBoss() end 
    })
    local activityIndex, curStartTimestamp
    for index, activityData in ksbcpairs(Game.TableData.GetGuildClubActivityDataTable()) do
        local timestamp = ActivityUtils.GetRefreshActivityNextOpenTime(activityData.ActivityID)
        if timestamp then
            if not curStartTimestamp or timestamp < curStartTimestamp then
                activityIndex = index
                curStartTimestamp = timestamp
            end
        end
    end

    self.rewardData = {}

    if activityIndex then
        local activityDesData = Game.TableData.GetGuildClubActivityDataRow(activityIndex)
        local activityData = Game.TableData.GetActivityDataRow(activityDesData.ActivityID)
        self.view.Text_NextActivityName_lua:SetText(activityData.Name)
        self:ProcessActivityShowTime(activityDesData.ActivityID, curStartTimestamp)
        for _, Rewards in pairs(Game.DropSystem.GetDropShowRewardData(activityData.DropDisplay)) do
            for _, V in ipairs(Rewards) do
                table.insert(self.rewardData, { itemID = V[1], count = V[2] })
            end
        end
    else
        
    end
    
    self.rewardList:SetData(#self.rewardData)
    
    self.view.Vx_Spine_lua:SetAnimation(1, "Club_open", false)
    self:StartTimer("ShowCardIdle", function()     
        self.view.Vx_Spine_lua:AddAnimation(0, "Club_idle", true, 0.2) 
    end, 2000, 1)
    self:StartTimer("ShowCard", function() self:ShowCard() end, 3000, 1)
end

function GuildInInvitationPopup_Panel:ProcessActivityShowTime(activityId, timestamp)
	--todo_zhangyu73:先暂时用这个方法，等待后续统一整理_G后一并处理时区问题
    local curTimestamp = _G._now(1)
	local date = {}
	local newTime = os.date("!*t", timestamp)
	table.append(date, newTime)

    if self:SameDay(curTimestamp, timestamp) then
        self.view.Text_NextActivityTime_lua:SetText(string.format("今晚%02d:%02d", date.hour, date.min))	--luacheck: ignore
    elseif self:SameDay(curTimestamp + 86400, timestamp) then
        self.view.Text_NextActivityTime_lua:SetText(string.format("明晚%02d:%02d", date.hour, date.min))	--luacheck: ignore
    elseif self:SameWeek(curTimestamp, timestamp) then
        self.view.Text_NextActivityTime_lua:SetText(
            string.format("本%s%02d:%02d", TimeUtils.GetWeekString()[date.wday-1], date.hour, date.min)	--luacheck: ignore
        )
    else
        self.view.Text_NextActivityTime_lua:SetText(
            string.format("下%s%02d:%02d", TimeUtils.GetWeekString()[date.wday-1], date.hour, date.min)	--luacheck: ignore
        )
    end
end

--region 临时代码
---todo_zhangyu73，临时判断。因为os.time()和os.time({...})返回的时间戳不一致，os.time({...})会自动加上时区偏移量
function GuildInInvitationPopup_Panel:SameDay(t1, t2)
	local date1 = os.date("*t", t1)
	local date2 = os.date("!*t", t2)
	return date1.year == date2.year and date1.month == date2.month and date1.day == date2.day
end
function GuildInInvitationPopup_Panel:SameWeek(t1, t2)
	local diffTime = math.abs(t1 - t2)
	if diffTime > 7 * 86400 then
		return false
	end
	local d1 = os.date("*t", t1)
	local d2 = os.date("!*t", t2)
	local w1 = d1.wday
	local w2 = d2.wday
	if w1 == 0 then w1 = 7 end
	if w2 == 0 then w2 = 7 end
	local bigDay
	local smallDay
	if t1 > t2 then
		bigDay = w1
		smallDay = w2
	else
		bigDay = w2
		smallDay = w1
	end
	if bigDay < smallDay then
		return false
	end
	if bigDay == smallDay and diffTime > 86400000 then
		return false
	end
	return true
end
--endregion 临时代码

function GuildInInvitationPopup_Panel:OnRefresh_rewardList(widget, index)
    widget:FillItem(self.rewardData[index].itemID, true, Enum.EItemSelectType.NotSelected)
end

function GuildInInvitationPopup_Panel:OnClickClose()
    self:PlayAnimation(self.view.Ani_Fadeout, function() 
            self.view.Canvas_Open_lua:SetVisibility(ESlateVisibility.Collapsed) 
            self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
            self.view.Vx_Spine_lua:SetAnimation(1, "Club_idle", true)
        end)
end

function GuildInInvitationPopup_Panel:ShowCard()
    self.view.Canvas_Open_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self:PlayAnimation(self.view.Ani_Fadein)
end

function GuildInInvitationPopup_Panel:OnClickBoss()
    if Game.GuildSystem.model.GuildPrincipalInfo.leaderId then
        Game.TeamSystem:PlayerCardUIDataAsync(
            Game.GuildSystem.model.GuildPrincipalInfo.leaderId, nil, nil, nil, Enum.EMenuType.GuildInvite
        )
    end
end

--- 此处为自动生成
function GuildInInvitationPopup_Panel:on_Btn_ClickArea_Clicked()
    self:CloseSelf()
end

return GuildInInvitationPopup_Panel
