local ESlateVisibility = import("ESlateVisibility")
local const = kg_require("Shared.Const")
local GuildInStructureList_Sub = kg_require("Gameplay.LogicSystem.Guild.Inside_2.Structure.GuildInStructureList_Sub")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GuildInStructureEdit_Panel : UIPanel
---@field view GuildInStructureEdit_PanelBlueprint
local GuildInStructureEdit_Panel = DefineClass("GuildInStructureEdit_Panel", UIPanel)

GuildInStructureEdit_Panel.eventBindMap = {
    [EEventTypesV2.ON_SET_GUILD_ROLES_SUCCESS] = "CloseSelf"
    
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInStructureEdit_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInStructureEdit_Panel:InitUIData()
    self.needChangeEid = nil
    self.canBossResign = false
end

--- UI组件初始化，此处为自动生成
function GuildInStructureEdit_Panel:InitUIComponent()
    ---@type GuildInStructureList_Sub
    self.WBP_Structure_luaCom = self:CreateComponent(self.view.WBP_Structure_lua, GuildInStructureList_Sub)
end

---UI事件在这里注册，此处为自动生成
function GuildInStructureEdit_Panel:InitUIEvent()
    self:AddUIEvent(self.view.WBP_BtnLeft_lua.Btn_ClickArea_lua.OnClicked, "OnClickLeftBtn")
    self:AddUIEvent(self.view.WBP_BtnRight_lua.Btn_ClickArea_lua.OnClicked, "OnClickRightBtn")
    self:AddUIEvent(self.view.WBP_BtnSave_lua.Btn_ClickArea_lua.OnClicked, "OnClickSave")
    self:AddUIEvent(self.view.WBP_ComBtnBackNew_lua.Btn_Back_Lua.OnClicked, "OnClickClose")
    self:AddUIEvent(self.view.WBP_ComBtnBackNew_lua.Btn_Info_lua.OnClicked, "OnClickTip")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInStructureEdit_Panel:InitUIView()
    self.view.WBP_BtnSave_lua:Event_UI_Style(1)
    self.view.WBP_BtnSave_lua.TB_Title_lua:SetText("保存修改")
    self.view.WBP_BtnLeft_lua:Event_UI_Style(0)
    self.view.WBP_BtnLeft_lua.TB_Title_lua:SetText("恢复默认")
    self.view.WBP_BtnRight_lua:Event_UI_Style(0)
    self.view.WBP_ComBtnBackNew_lua.Text_Back_lua:SetText("职位调整")
end

---面板打开的时候触发
function GuildInStructureEdit_Panel:OnRefresh(eid)
    self.needChangeEid = eid
    self.WBP_Structure_luaCom:RefreshData(
        eid, function() self:OnClickSave() end, function() self:OnClickSave() end
    )
    self.WBP_Structure_luaCom:StartAdjust()
    self:RefreshBtns()
end

function GuildInStructureEdit_Panel:RefreshBtns()
    local memberData = self.WBP_Structure_luaCom.memberDataByEid[self.needChangeEid]
    if memberData and memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] == const.GUILD_ROLE.PRESIDENT then
        self.canBossResign = true
        self.view.WBP_BtnLeft_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.WBP_BtnRight_lua.TB_Title_lua:SetText("会长辞职")
    else
        self.canBossResign = false
        self.view.WBP_BtnLeft_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_BtnRight_lua.TB_Title_lua:SetText("恢复默认")
    end
end

function GuildInStructureEdit_Panel:OnClickLeftBtn()
    if self.WBP_Structure_luaCom.memberChanged then
        self.WBP_Structure_luaCom:Reset()
        if not self.WBP_Structure_luaCom.memberDataByEid[self.needChangeEid] then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CLUB_MEMBER_CHANGE_OUT)
            self:CloseSelf()
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CLUB_MEMBER_CHANGE)
        end
    else
        self.WBP_Structure_luaCom:Reset()
    end
end

function GuildInStructureEdit_Panel:OnClickRightBtn()
    if self.canBossResign then
        self.WBP_Structure_luaCom:DismissBoss()
    else
        self:OnClickLeftBtn()
    end
end

function GuildInStructureEdit_Panel:OnClickSave()
    if self.WBP_Structure_luaCom.memberChanged then
        self.WBP_Structure_luaCom:Reset()
        if not self.WBP_Structure_luaCom.memberDataByEid[self.needChangeEid] then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CLUB_MEMBER_CHANGE_OUT)
            self:CloseSelf()
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CLUB_MEMBER_CHANGE)
        end
        return
    end
    if next(self.WBP_Structure_luaCom.rightChanged) or next(self.WBP_Structure_luaCom.id2GroupChanged) then
        -- Game.GuildSystem.sender:setGuildRoles(
        --     Game.GuildSystem.model.guildMemberVersion or 0, 
        --     self.WBP_Structure_luaCom.rightChanged, self.WBP_Structure_luaCom.id2GroupChanged
        -- )
        Game.GuildSystem.sender:ReqSetGuildRole(
            Game.GuildSystem.model.guildMemberVersion or 0, self.needChangeEid, 
            self.WBP_Structure_luaCom.rightChanged
        )
    else
        self:CloseSelf()
    end
end

function GuildInStructureEdit_Panel:OnClickClose()
    if next(self.WBP_Structure_luaCom.rightChanged) or next(self.WBP_Structure_luaCom.id2GroupChanged) then
        Game.MessageBoxSystem:AddPopupByConfig(
            Enum.EDialogPopUpData.CLUB_OFFICIAL_CHANGE_CONFIRM,
            function() self:CloseSelf() end
        )
    else
        self:CloseSelf()
    end
end

function GuildInStructureEdit_Panel:OnClickTip()
    Game.TipsSystem:ShowTips(Enum.ETipsData.CLUB_POSITION_ADJUSTMENT, self.view.WBP_ComBtnBackNew_lua.Btn_Info_lua:GetCachedGeometry())
end

return GuildInStructureEdit_Panel
