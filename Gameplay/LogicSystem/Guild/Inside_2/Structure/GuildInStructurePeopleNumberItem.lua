local const = kg_require("Shared.Const")
local ESlateVisibility = import("ESlateVisibility")
local P_ComSocialHead = kg_require("Gameplay.LogicSystem.CommonUI.P_ComSocialHead")
---@class GuildInStructurePeopleNumberItem : UIComponent
local GuildInStructurePeopleNumberItem = DefineClass("GuildInStructurePeopleNumberItem", UIComponent)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

GuildInStructurePeopleNumberItem.SHOW_TYPE = {
    GROUP = 1, MEMBER = 2, APPRENTICE = 3
}
GuildInStructurePeopleNumberItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInStructurePeopleNumberItem:OnCreate()
    self.showType = 0
    self.slotId = 0
    self.num = 0
    self.maxNum = 0
    self.owner = nil
    self.clickCB = nil
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClickBtn)
    ---@type P_ComSocialHead
    self.headCom = self:BindComponent(self.View.WBP_SocialHead, P_ComSocialHead)
end

function GuildInStructurePeopleNumberItem:SetData(showType, slotId, num, maxNum, bInAdjusting, owner, clickCB)
    self.showType = showType
    self.slotId = slotId
    self.num = num
    self.maxNum = maxNum
    self.owner = owner
    self.clickCB = clickCB
    if self.showType == self.SHOW_TYPE.GROUP then
        self.View.Text_PeopleType:SetText("组员")
    elseif self.showType == self.SHOW_TYPE.MEMBER then
        self.View.Text_PeopleType:SetText("会员")
    else
        self.View.Text_PeopleType:SetText("候补")
    end
    self.View.Text_PeopleNumber_lua:SetText(string.format("%d/%d", num, maxNum))
    self:SetAdjustState(bInAdjusting)
end

function GuildInStructurePeopleNumberItem:SetAdjustState(bInAdjusting)
    if self.showType == self.SHOW_TYPE.GROUP then
        if bInAdjusting then
            self.View:Event_UI_Style(2)
            self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Edit_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        else
            self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Edit_Out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            self.View:Event_UI_Style(self.num == 0 and 1 or 0)
        end
    else
        if bInAdjusting then
            self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Edit_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            self.View:Event_UI_Style(5)
        else
            self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Edit_Out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            self.View:Event_UI_Style(self.num == 0 and 4 or 3)
        end
    end
end


function GuildInStructurePeopleNumberItem:OnClickBtn()
    if self.owner then
        self.clickCB(self.owner, self.showType, self.slotId)
    end
end

function GuildInStructurePeopleNumberItem:SetHead(memberData)
    if memberData then
        self.headCom:Refresh({
            IsSelected = true, ProfessionID = memberData.school, Level = memberData.lv,
            OnClickedCallBack = function() self:OnClickBtn() end
        })
        self.headCom:SetBindButton()
        if self.showType == self.SHOW_TYPE.GROUP then
            if memberData.roles[const.GUILD_ROLE_TYPE.GROUP_ROLE] == const.GUILD_ROLE.GROUP_LEADER then
                self.View.WBP_SocialHead:SetVisibility(ESlateVisibility.Collapsed)
            else
                self.View.WBP_SocialHead:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            end
        else
            if memberData.groupID ~= 0 or memberData.roles[const.GUILD_ROLE_TYPE.HONOR_ROLE] ~= 0 then
                self.View.WBP_SocialHead:SetVisibility(ESlateVisibility.Collapsed)
            else
                self.View.WBP_SocialHead:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            end
        end
        self:PlayAnimation(self.View.WBP_SocialHead.WidgetRoot, self.View.WBP_SocialHead.WidgetRoot.Ani_Select,0, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self.View.WBP_SocialHead:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return GuildInStructurePeopleNumberItem
