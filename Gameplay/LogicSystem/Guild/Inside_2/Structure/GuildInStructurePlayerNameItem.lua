local const = kg_require("Shared.Const")
local ESlateVisibility = import("ESlateVisibility")
local P_ComSocialHead = kg_require("Gameplay.LogicSystem.CommonUI.P_ComSocialHead")
---@class GuildInStructurePlayerNameItem : UIComponent
local GuildInStructurePlayerNameItem = DefineClass("GuildInStructurePlayerNameItem", UIComponent)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

GuildInStructurePlayerNameItem.ROLE_DISPLAY = {
    NOT_SHOW = 1, COMMON_SHOW = 0, STAR_SHOW = 2, VICE_PRESIDENT_SHOW = 3
}

GuildInStructurePlayerNameItem.SHOW_TYPE = {
    DEFAULT = 0, ADD = 1, CHANGE = 2, DELETE = 3, EMPTY = 4, BLACKBACK = 5
}

GuildInStructurePlayerNameItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInStructurePlayerNameItem:OnCreate()
    self.roleType = 0
    self.roleId = 0
    self.slotId = 0
    self.owner = nil
    self.clickCB = nil
    self.memberData = {}
    self.changeEid = false
    self.inAdjust = false
    --region ui状态
    self.roleDisplay = self.ROLE_DISPLAY.NOT_SHOW
    self.bSelected = false
    self.showType = self.SHOW_TYPE.EMPTY
    --endregion
    ---@type P_ComSocialHead
    self.headCom = self:BindComponent(self.View.WBP_SocialHead, P_ComSocialHead)

    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClickBtn)
end

function GuildInStructurePlayerNameItem:SetData(roleId, slotId, memberData, changeEid, inAdjust, owner, clickCB)
    self.roleType = Game.TableData.GetGuildRightDataRow(roleId).Type
    self.roleId = roleId
    self.slotId = slotId
    self.owner = owner
    self.clickCB = clickCB
    self.changeEid = changeEid
    self.inAdjust = inAdjust
    self:RefreshRoleTitle()
    self:SetMemberData(memberData)
    self:RefreshShowType()
end

function GuildInStructurePlayerNameItem:SetMemberData(memberData)
    self.memberData = memberData
    
    if memberData then
        self.headCom:Refresh({ProfessionID = memberData.school, Level = memberData.lv, IsInverse = true})
    else
        self.View.WBP_SocialHead:SetVisibility(ESlateVisibility.Collapsed)
    end
    -- self.bSelected = memberData and self.changeEid == memberData.id or false
    self:SetSelected(memberData and self.changeEid == memberData.id or false)
end

function GuildInStructurePlayerNameItem:SetSelected(bSelected)
    self.bSelected = bSelected
    self.View:Event_UI_Style(self.roleDisplay, self.bSelected, self.showType)
    if self.bSelected then
        self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Select, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self:PlayAnimation(self.View.WBP_SocialHead.WidgetRoot, self.View.WBP_SocialHead.WidgetRoot.Ani_Select,0, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_UnSelect, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self:PlayAnimation(self.View.WBP_SocialHead.WidgetRoot, self.View.WBP_SocialHead.WidgetRoot.Ani_UnSelect,0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

-- function GuildInStructurePlayerNameItem:RefreshShowType()
--     if self.changeEid then
--         if self.memberData then
--             self:SetShowType(self.SHOW_TYPE.DEFAULT)
--         else
--             self:SetShowType(self.SHOW_TYPE.CHANGE)
--             self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type2_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
--         end
--     else
--         if self.memberData then
--             if self.inAdjust then
--                 self:SetShowType(self.SHOW_TYPE.DELETE)
--                 self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type3_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
--             else
--                 self:SetShowType(self.SHOW_TYPE.DEFAULT)
--             end
--         else
--             if self.inAdjust then
--                 self:SetShowType(self.SHOW_TYPE.ADD)
--                 self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type1_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
--             else
--                 self:SetShowType(self.SHOW_TYPE.EMPTY)
--             end
--         end
--     end
-- end

function GuildInStructurePlayerNameItem:RefreshShowType()
    -- 这里的动画先临时这么处理 旧框架播放和动画 俱乐部之星动画播放有问题 迭代后处理
    if self.changeEid then
        if self.memberData then
            self:SetShowType(self.SHOW_TYPE.BLACKBACK)
            self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type2_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
                    self:SetShowType(self.SHOW_TYPE.BLACKBACK)
                end)
            -- self:SetShowType(self.SHOW_TYPE.BLACKBACK)
        else
            if self.inAdjust then
                self:SetShowType(self.SHOW_TYPE.CHANGE)
                self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type2_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
                    self:SetShowType(self.SHOW_TYPE.CHANGE)
                end)
            else
                self:SetShowType(self.SHOW_TYPE.CHANGE)
                self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type2_Out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            end
        end
    else
        if self.memberData then
            if self.inAdjust then
                self:SetShowType(self.SHOW_TYPE.DELETE)
                self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type3_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
                self:SetShowType(self.SHOW_TYPE.DELETE)
            end)
            else
                self:SetShowType(self.SHOW_TYPE.DEFAULT)
                self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type3_Out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            end
        else
            if self.inAdjust then
                self:SetShowType(self.SHOW_TYPE.ADD)
                self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type1_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
                self:SetShowType(self.SHOW_TYPE.ADD)
            end)
            else
                self:SetShowType(self.SHOW_TYPE.EMPTY)
                self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Type1_Out, 0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
                self:SetShowType(self.SHOW_TYPE.EMPTY)
            end)
            end
        end
    end
end


function GuildInStructurePlayerNameItem:SetShowType(showType)
    self.showType = showType
    if self.showType == self.SHOW_TYPE.DEFAULT or self.showType == self.SHOW_TYPE.DELETE or self.showType == self.SHOW_TYPE.BLACKBACK then
        self.View.Text_PlayerName:SetText(self.memberData and self.memberData.rolename or "")
    else
        if self.showType == self.SHOW_TYPE.ADD then
            self.View.Text_PlayerName:SetText("添加人选")
        elseif self.showType == self.SHOW_TYPE.CHANGE then
            self.View.Text_PlayerName:SetText("调整职位")
        elseif self.showType == self.SHOW_TYPE.EMPTY then
            self.View.Text_PlayerName:SetText("暂无人选")
        end
    end
    self.headCom:SetSelected(self.bSelected)
    self.View:Event_UI_Style(self.roleDisplay, self.bSelected, self.showType)
end

function GuildInStructurePlayerNameItem:RefreshRoleTitle()
    if self.roleType == const.GUILD_ROLE_TYPE.COMMON_ROLE then
        self.View.Text_Role:SetText(Game.GuildSystem:RoleIDToRoleName(self.roleId))
        if self.roleId == 2 then
            self.roleDisplay = self.ROLE_DISPLAY.VICE_PRESIDENT_SHOW
        elseif self.slotId == 1 then
            self.roleDisplay = self.ROLE_DISPLAY.COMMON_SHOW
        else
            self.roleDisplay = self.ROLE_DISPLAY.NOT_SHOW
        end 
    elseif self.roleType == const.GUILD_ROLE_TYPE.HONOR_ROLE then
        self.View.Text_Role:SetText("俱乐部之星")
        if self.slotId == 1 then
            self.roleDisplay = self.ROLE_DISPLAY.STAR_SHOW
        else
            self.roleDisplay = self.ROLE_DISPLAY.NOT_SHOW
        end
    else
        self.View.Text_Role:SetText(Game.GuildSystem:GetGroupName(self.slotId))
        self.roleDisplay = self.ROLE_DISPLAY.COMMON_SHOW
    end
end

function GuildInStructurePlayerNameItem:OnClickBtn()
    if self.clickCB then
        self.clickCB(self.owner, self.roleId, self.slotId, self.showType)
    end
end

return GuildInStructurePlayerNameItem
