local guildConst = kg_require "Gameplay.LogicSystem.Guild.GuildConst"
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@type GuildHomeWelfareItem
local GuildHomeWelfareItem = kg_require("Gameplay.LogicSystem.Guild.Inside_2.Welfare.GuildHomeWelfareItem")
---@class GuildHomeWelfarePage : UIComponent
---@field view GuildHomeWelfarePageBlueprint
local GuildHomeWelfarePage = DefineClass("GuildHomeWelfarePage", UIComponent)

GuildHomeWelfarePage.eventBindMap = {
    [EEventTypesV2.GUILD_SIGNATURE_CHANGE] = "RefreshWelfarePage",
    [EEventTypesV2.REFRESH_GUILD_SIGN_IN] = "RefreshWelfarePage",
    [EEventTypesV2.RECEIVE_GUILD_WAGE] = "RefreshWelfarePage",
    [EEventTypesV2.ON_REFRESH_GUILD_WELFARE] = "RefreshWelfarePage",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildHomeWelfarePage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildHomeWelfarePage:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GuildHomeWelfarePage:InitUIComponent()
    self.entryListView = self:CreateComponent(self.view.ContentList_lua, UIBaseAdapter, "GuildOutResponseCreatorPage")
    self.contentList = self.entryListView:CreateBaseList(GuildHomeWelfareItem, ComList, "contentList")
end

---UI事件在这里注册，此处为自动生成
function GuildHomeWelfarePage:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildHomeWelfarePage:InitUIView()
end

---面板打开的时候触发
function GuildHomeWelfarePage:OnRefresh(...)
    self:RefreshWelfarePage()
    self:ReqGetGuildWelfareWage()
end

function GuildHomeWelfarePage:RefreshWelfarePage()
    self.contentList:SetData(#guildConst.WelfareData)
end

function GuildHomeWelfarePage:OnRefresh_contentList(widget, index, selected)
    widget:SetData(index, self, self.OnClickGoto, self.OnClickChangeDeclaration)
end

function GuildHomeWelfarePage:ReqGetGuildWelfareWage()
    Game.GuildSystem.sender:getGuildWelfare()
end

function GuildHomeWelfarePage:OnClickChangeDeclaration(index)
    UI.ShowUI("P_GuildInSign")
    self:RefreshWelfarePage()
end

function GuildHomeWelfarePage:OnClickGoto(index)
    if index == 2 then -- 技能
        Game.GuildSystem:ShowGuildSkill()
    elseif index == 4 then
        Game.UIJumpSystem:JumpToUI(Enum.EGuildConstIntData.GUILD_STORE)
    elseif index == 3 then  -- 分红
        Game.GuildSystem:HandleWageGet()
    else
        self:HandleWelfareLink(index)
    end
    --local welfareData = guildConst.WelfareData[index]
    --if welfareData.type == guildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_GET_AWARD and Game.GuildSystem:CheckWelfareChoiceValid(index) then
    --    Game.NewUIManager:ClosePanel("GuildInside_Panel")
    --end
end

function GuildHomeWelfarePage:HandleWelfareLink(index)
    local data = guildConst.WelfareData[index]
    if not Game.GuildSystem:CheckWelfareChoiceValid(index, true) then
        return
    end

    local PlayerInfo = Game.GuildSystem.model.guildPlayer
    if PlayerInfo then
        if data.type == guildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_NAVI then
            Game.GuildSystem:EnterGuild()
        elseif data.type == guildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_OPEN_UI then
            UI.ShowUI(table.unpack(data.param))
        elseif data.type == guildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_GET_AWARD then
            Game.GuildSystem:EnterGuild()
        elseif data.type == guildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_SIGN_IN then
            Game.GuildSystem.sender:guildSignIn()
        end
    end
end

return GuildHomeWelfarePage
