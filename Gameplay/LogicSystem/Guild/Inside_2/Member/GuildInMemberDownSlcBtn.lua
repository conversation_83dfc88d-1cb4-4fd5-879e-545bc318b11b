local P_ComboBoxFloat = kg_require("Gameplay.LogicSystem.CommonUI.P_ComboBoxFloat")
kg_require("Gameplay.LogicSystem.CommonUI.P_ComboBoxFloatView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildInMemberDownSlcBtn : UIComponent
---@field view GuildInMemberDownSlcBtnBlueprint
local GuildInMemberDownSlcBtn = DefineClass("GuildInMemberDownSlcBtn", UIComponent)

GuildInMemberDownSlcBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInMemberDownSlcBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInMemberDownSlcBtn:InitUIData()
    self.currentSelectIndex = nil
    self.owner = nil
    self.selectOptionCallBack = nil
    self.Pos = 0
end

--- UI组件初始化，此处为自动生成
function GuildInMemberDownSlcBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildInMemberDownSlcBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "onBtn_ClickArea_luaClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInMemberDownSlcBtn:InitUIView()
end

---面板打开的时候触发
function GuildInMemberDownSlcBtn:OnRefresh(...)
end

function GuildInMemberDownSlcBtn:SetArrowState(bOpen)
    self.userWidget:Event_UI_Style(bOpen)
end

function GuildInMemberDownSlcBtn:Init(Owner, Options, SelectOptionCallBack, CurrentSelectIndex)
    self.owner = Owner
    self.selectOptionCallBack = SelectOptionCallBack
    if Options then
        self:SetData(Options, CurrentSelectIndex)
    end
end

function GuildInMemberDownSlcBtn:SetData(Options, SelectIndex)
    self.currentSelectIndex = nil
    self.optionsData = Options
    self:createOptions(self.optionsData)
    if SelectIndex ~= -1 then
        SelectIndex = SelectIndex or 1
        if SelectIndex <= #Options then
            self:SelectIndex(SelectIndex)
        else
            Log.Debug("GuildInMemberDownSlcBtn:SetData(Options, SelectIndex) SelectIndex param is illegal，num is not enough!")
        end
    end
end

function GuildInMemberDownSlcBtn:createOptions(Options)
    if UI.IsShow("P_ComboBoxFloat") then
        UI.Invoke("P_ComboBoxFloat", "createOptions", Options)
    end
end

function GuildInMemberDownSlcBtn:SelectIndex(index)
    if index == self.currentSelectIndex then
        --选中相同条目，不处理
        return
    end
    self.currentSelectIndex = index
    self:UpdateTitle()
    if self.selectOptionCallBack then
        self.selectOptionCallBack(self.owner, index)
    end
    self:SetArrowState(false)
end

function GuildInMemberDownSlcBtn:UpdateTitle()
    local OptionData = self.optionsData[self.currentSelectIndex]
    self.view.TB_Job_lua:SetText(OptionData.text)
end

function GuildInMemberDownSlcBtn:GetSelectedIndex()
    return self.currentSelectIndex
end

--- 此处为自动生成
function GuildInMemberDownSlcBtn:onBtn_ClickArea_luaClicked()
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common_Expand, true)
    if UI.GetUI("P_ComboBoxFloat") and UI.GetUI("P_ComboBoxFloat"):IsShow() then
        UI.HideUI("P_ComboBoxFloat")
        self:SetArrowState(false)
    else
        P_ComboBoxFloat.ComboBox = self
        UI.ShowUI("P_ComboBoxFloat", --[[self,]] self.optionsData, self.currentSelectIndex)
        self:SetArrowState(true)
    end
end

return GuildInMemberDownSlcBtn
