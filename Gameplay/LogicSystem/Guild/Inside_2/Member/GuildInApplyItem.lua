local ESlateVisibility = import("ESlateVisibility")
---@class GuildInApplyItem : UIComponent

local GuildInApplyItem = DefineClass("GuildInApplyItem", UIComponent)

GuildInApplyItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInApplyItem:OnCreate()
    self.data = nil
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_BtnYes.Big_Button_ClickArea, self.OnClickYes)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_BtnNo.Big_Button_ClickArea, self.OnClickNo)
    
    self.View.WBP_ApplyTag:SetVisibility(ESlateVisibility.Collapsed)
    self.View.Btn_ClickArea_lua:SetVisibility(ESlateVisibility.Collapsed)
end

function GuildInApplyItem:RefreshData(index, data)
    self.data = data
    self.View:Event_UI_Style(false, index & 1 == 0, 3, false, data.offlineTime == 0)
    local professionData = Game.TableData.GetPlayerSocialDisplayDataRow(data.school)[data.sex]
    self:SetImage(self.View.WBP_HeadIcon.Img_Icon, professionData.SoloHeadIcon)
    self.View.TB_Name:SetText(data.rolename)
    self.View.TB_GuildLv:SetText(data.lv)
    self.View.TB_Profession:SetText(professionData.ClassName)
    self.View.TB_BattleScore:SetText(data.power)
end

function GuildInApplyItem:OnClickYes()
    if self.data then
        Game.GuildSystem.sender:agreeGuildApply(self.data.id)
    end
end

function GuildInApplyItem:OnClickNo()
    if self.data then
        Game.GuildSystem.sender:refuseGuildApply(self.data.id)
    end
end

return GuildInApplyItem
