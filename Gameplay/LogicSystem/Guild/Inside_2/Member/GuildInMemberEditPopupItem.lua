local const = kg_require("Shared.Const")
local P_ComBtnSwitch = kg_require "Gameplay.LogicSystem.CommonUI.P_ComBtnSwitch"
local ESlateVisibility = import("ESlateVisibility")
local guildConst = kg_require "Gameplay.LogicSystem.Guild.GuildConst"
---@class GuildInMemberEditPopupItem : UIComponent

local GuildInMemberEditPopupItem = DefineClass("GuildInMemberEditPopupItem", UIComponent)

GuildInMemberEditPopupItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInMemberEditPopupItem:OnCreate()
    self.index = nil
    self.data = nil
    self.owner = nil
    self.clickCB = nil
    ---@type P_ComBtnSwitch
    self.memberSwitch = self:BindComponent(self.View.WBP_ComBtnSwitch, P_ComBtnSwitch)
    
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtn.Big_Button_ClickArea, self.OnClickBtn)

end

function GuildInMemberEditPopupItem:RefreshData(index, data, editType, param, owner, clickCB)
    self.index = index
    self.data = data
    self.owner = owner
    self.editType = editType
    self.param = param
    self.clickCB = clickCB
    -- self.View:Event_UI_Style(false, index & 1 == 0, 3, false, data.offlineTime == 0)
    self.View:Event_UI_Style(false, data.id == Game.me.eid)
    local professionData = Game.TableData.GetPlayerSocialDisplayDataRow(data.school)[data.sex]
    self:SetImage(self.View.WBP_HeadIcon.Img_Icon, professionData.SoloHeadIcon)
    self.View.TB_Name:SetText(data.rolename)
    self.View.TB_GuildLv:SetText(Game.GuildSystem:RoleIDToRoleName(data.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE]))
    self.View.TB_Profession:SetText(professionData.ClassName)
    self.View.TB_Group:SetText(Game.GuildSystem:GetGroupName(data.groupID))
    self.View.TB_BattleScore:SetText(data.power)
    self.View.TB_Contribution:SetText(string.format(
        "%s/%s",
        Game.GuildSystem:ProcessGuildNumber(data.weekContribution),
        Game.GuildSystem:ProcessGuildNumber(data.contribution)
    ))
    self.View.WBP_ComBtnSwitch:SetVisibility(ESlateVisibility.Collapsed)
    self.View.TB_Word_lua:SetVisibility(ESlateVisibility.Collapsed)
    local curRole = data.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE]
    if editType == guildConst.STRUCTURE_EDIT_TYPE.ROLE then
        if param then
            self.View.WBP_ComBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        else
            self.View.WBP_ComBtn:SetVisibility(ESlateVisibility.Collapsed)
        end
    elseif editType == guildConst.STRUCTURE_EDIT_TYPE.MEMBER then
        self.View.WBP_ComBtnSwitch:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.WBP_ComBtn:SetVisibility(ESlateVisibility.Collapsed)
        self.memberSwitch:SetData({
            IsOpen = curRole ~= const.GUILD_ROLE.MEMBER,
            OnSwitchCallback = function(bSwitch) self:OnSwitchMember(bSwitch) end,
            Value = { "正式", "候补" }
        })
    else
        if data.groupID == 0 then
            self.View.TB_Word_lua:SetText(curRole ~= const.GUILD_ROLE.MEMBER and "候补" or "正式")
        else
            self.View.TB_Word_lua:SetText(Game.GuildSystem:GetGroupName(data.groupID))
        end
        self.View.TB_Word_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

function GuildInMemberEditPopupItem:OnClickBtn()
    if self.owner and self.clickCB then
        self.clickCB(self.owner, self.index)
    end
end

function GuildInMemberEditPopupItem:OnSwitchMember(bSwitch)
    -- Game.EventSystem:Publish(
    --     _G.EEventTypes.ON_SWITCH_GUILD_MEMBER_APPRENTICE_ROLE,
    --     self.data.id, bSwitch and const.GUILD_ROLE.APPRENTICE or const.GUILD_ROLE.MEMBER
    -- )
end

return GuildInMemberEditPopupItem
