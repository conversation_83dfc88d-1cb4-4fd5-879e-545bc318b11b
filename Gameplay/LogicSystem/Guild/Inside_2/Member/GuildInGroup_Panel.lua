local const = kg_require("Shared.Const")
local ComFrame = kg_require("Gameplay.LogicSystem.Common.Panel.ComFrame")
local ESlateVisibility = import("ESlateVisibility")
local stringConst = require("Data.Config.StringConst.StringConst")
local P_SearchInput = kg_require "Gameplay.LogicSystem.CommonUI.CommonInput.P_SearchInput"
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local GuildComTabBtnInsideList = kg_require("Gameplay.LogicSystem.Guild.GuildCom.GuildComTabBtnInsideList")
local GuildInGroupItem = kg_require("Gameplay.LogicSystem.Guild.Inside_2.Member.GuildInGroupItem")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local GuildInMemberDownSlcBtn = kg_require("Gameplay.LogicSystem.Guild.Inside_2.Member.GuildInMemberDownSlcBtn")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
---@class GuildInGroup_Panel : UIPanel
---@field view GuildInGroup_PanelBlueprint
local GuildInGroup_Panel = DefineClass("GuildInGroup_Panel", UIPanel)

GuildInGroup_Panel.eventBindMap = {
    [EEventTypesV2.ON_SET_GUILD_GROUP_NAME_SUCCESS] = "OnSetGroupNameSuccess",
    [EEventTypesV2.ON_SET_GUILD_ROLES_SUCCESS] = "OnRefresh",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildInGroup_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildInGroup_Panel:InitUIData()
    self.tabData = {}
    self.memberData = {}
    self.memberShowData = {}
    self.searchKeyWord = ""

    self.memberOnlineNum = 0
    
    self.downSelectedIndex = 1
    self.downData = {{type = 0, text = "全部"}}
    local professions = {}
    for professionId, data in ksbcpairs(Game.TableData.GetPlayerSocialDisplayDataTable()) do
        if not table.arrayIndexOf(professions, professionId) then
            table.insert(professions, professionId)
            table.insert(self.downData, {type = professionId, text = data[0].ClassName})
        end
    end
end

--- UI组件初始化，此处为自动生成
function GuildInGroup_Panel:InitUIComponent()
    ---@type ComFrame
    self.WBP_ComPanel_luaCom = self:CreateComponent(self.view.WBP_ComPanel_lua, ComFrame)
    ---@type GuildComTabBtnInsideList
    self.wBP_TabList_luaCom = self:CreateComponent(self.view.WBP_TabList_lua, GuildComTabBtnInsideList)
    ---@type P_SearchInput
    self.searchInput = self:CreateComponent(self.view.WBP_ComInput_lua, UIBaseAdapter, "GuildInGroup_Panel", P_SearchInput)

    self.listView = self:CreateComponent(self.view.ComLL_Member_lua, UIBaseAdapter, "GuildInGroup_Panel")
    self.memberListView = self.listView:CreateBaseList(GuildInGroupItem, ComList, "memberList")

    ---@type GuildInMemberDownSlcBtn
    self.WBP_TagDown_luaCom = self:CreateComponent(self.view.WBP_Select_lua.WBP_TagDown_lua, GuildInMemberDownSlcBtn)
end

---UI事件在这里注册，此处为自动生成
function GuildInGroup_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComPanel_luaCom.tipsCb, "onWBP_ComPanel_luaComtipsCb")
    self:AddUIEvent(self.WBP_ComPanel_luaCom.backCb, "onWBP_ComPanel_luaCombackCb")
    self:AddUIEvent(self.wBP_TabList_luaCom.onItemSelected, "onWBP_TabList_luaComItemSelected")

    self:AddUIEvent(self.view.WBP_InviteBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickInvite")
    self:AddUIEvent(self.view.WBP_ChangeNameBtn_lua.Btn_ClickArea_lua.OnClicked, "OnClickChangeName")

    self:AddUIEvent(self.view.WBP_Select_lua.Btn_Role_lua.OnClicked, "OnClickSortByRole")
    self:AddUIEvent(self.view.WBP_Select_lua.Btn_Lv_lua.OnClicked, "OnClickSortByLv")
    self:AddUIEvent(self.view.WBP_Select_lua.Btn_BattleScore_lua.OnClicked, "OnClickSortByBattleScore")
    self:AddUIEvent(self.view.WBP_Select_lua.Btn_Online_lua.OnClicked, "OnClickSortByOnline")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildInGroup_Panel:InitUIView()
    self.view.WBP_Select_lua:Event_UI_Style(1)
    self.view.WBP_ChangeNameBtn_lua:Event_UI_Style(0)
    self.view.WBP_InviteBtn_lua:Event_UI_Style(1)
    self.searchInput:GetUIBase():SetData({
        Owner = self,
        OnClickSearch = self.OnClickSearch,
        OnValueChangedCallback = self.OnInputChanged,
        HintText = stringConst.Get("GUILD_MEMBER_SEARCH"),
        DefaultInputText = "",
    })
    self.view.WBP_InviteBtn_lua.TB_Title_lua:SetText("一键邀请入团")
    self.view.WBP_ChangeNameBtn_lua.TB_Title_lua:SetText("修改分组名")
    self.view.WBP_ComPanel_lua.WBP_ComBtnBack_lua.Text_Back_lua:SetText("分组管理")
    self.view.WBP_ComPanel_lua.WBP_ComBtnBack_lua:Event_UI_Style(3, 0)
end

function GuildInGroup_Panel:RefreshSortSwitch()
    self.view.WBP_Select_lua.WBP_GuildSwitchRole_lua:Event_UI_Style(self:GetSortDescending("roles"))
    self.view.WBP_Select_lua.WBP_GuildSwitchLv_lua:Event_UI_Style(self:GetSortDescending("lv"))
    self.view.WBP_Select_lua.WBP_GuildSwitchBattleScore_lua:Event_UI_Style(self:GetSortDescending("power"))
    self.view.WBP_Select_lua.WBP_GuildSwitchOnline_lua:Event_UI_Style(self:GetSortDescending("offlineTime"))
end

---面板打开的时候触发
function GuildInGroup_Panel:OnRefresh(memberData, guildRoleSlotInfo)
    self:InitData(memberData, guildRoleSlotInfo)
    self:RefreshPage()
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:InitData(memberDatas, guildRoleSlotInfo)
    self.sortRule = {
        { key = "offlineTime",     bDescending = false },
        { key = "roles",            bDescending = true },
        { key = "lv",              bDescending = true },
        { key = "school",          bDescending = true },
        { key = "power",           bDescending = true },
    }
    self.tabData = {}
    for index = 1, Game.GuildSystem:GetGroupNum() do
        table.insert(self.tabData, {tabName = Game.GuildSystem:GetGroupName(index)})
    end
    self.memberData = {}
    self.memberOnlineNum = 0
    for _, memberData in ipairs(memberDatas) do
        if memberData.offlineTime == 0 and memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] ~= const.GUILD_ROLE.APPRENTICE then
            self.memberOnlineNum = self.memberOnlineNum + 1
        end
        if memberData.groupID ~= 0 then
            self.memberData[memberData.groupID] = self.memberData[memberData.groupID] or {}
            table.insert(self.memberData[memberData.groupID], memberData)
        end
    end
    self.memberShowData = {}
end

function GuildInGroup_Panel:RefreshPage()
    self.wBP_TabList_luaCom:RefreshData(self.tabData)
    self.wBP_TabList_luaCom:Sel(1)
    self.view.Text_MemberNum_lua:SetText(
        string.format("正式会员：%d/%d", Game.GuildSystem:GetMemberNum(), Game.GuildSystem:GetMaxMemberNum())
    )
    self.view.RB_OnlineNum_lua:SetText(string.format("<Green>%d</><Default>人在线</>", self.memberOnlineNum))
    self.WBP_TagDown_luaCom:Init(self, self.downData, self.OnClickDownItem)
    self:RefreshSortSwitch()
end

function GuildInGroup_Panel:RefreshGroupMemberList()
    local curMemberData = self.memberData[self.wBP_TabList_luaCom:GetSelectedIndex()] or {}
    local groupMemberOnlineNum = 0
    self.memberShowData = {}
    for _, memberData in ipairs(curMemberData) do
        if memberData.offlineTime == 0 then
            groupMemberOnlineNum = groupMemberOnlineNum + 1
        end
        if self.downData[self.downSelectedIndex].type ~= 0 then
            if memberData.school ~= self.downData[self.downSelectedIndex].type then
                goto continue
            end
        end
        if self.searchKeyWord == nil or self.searchKeyWord == "" then
            table.insert(self.memberShowData, memberData)
        elseif string.contains(memberData.rolename, self.searchKeyWord) then
            table.insert(self.memberShowData, memberData)
        end
        :: continue ::
    end

    Game.GuildSystem:SortListByRule(self.memberShowData, self.sortRule)
    self.memberListView:SetData(#self.memberShowData)

    self.view.Text_MemberNum_lua:SetText(
        string.format(stringConst.Get("GROUP_MEMBER_HINT"), #self.memberShowData, Game.TableData.GetConstDataRow("CLUB_GROUP_MAX_MEMBER"))
    )
    self.view.RB_OnlineNum_lua:SetText(string.format("<Green>%d</><Default>人在线</>", groupMemberOnlineNum))

    if #self.memberShowData == 0 then
        self.view.WBP_Empty_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.WBP_Empty_lua:SetVisibility(ESlateVisibility.Collapsed)
    end
    self:RefreshSortSwitch()
end

function GuildInGroup_Panel:OnRefresh_memberList(widget, index, selected)
    local memberData = self.memberShowData[index]
    widget:RefreshData(index, memberData, selected)
end

function GuildInGroup_Panel:OnClick_memberList(widget, index)
    local memberData = self.memberShowData[index]
    local cachedGeometry = widget.View.WidgetRoot:GetCachedGeometry()
    local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
    local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
        _G.GetContextObject(), cachedGeometry, localSize, nil, nil
    )
    if memberData then
        if memberData.id ~= Game.me.eid then
            Game.TabClose:AttachPanel(
                "ComTagBoxPanel", Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, widget.View.WidgetRoot
            )
            Game.NewUIManager:OpenPanel(
                "ComTagBoxPanel", viewportPosition.X - 1000, viewportPosition.Y - 50,
                {
                    EntityID = memberData.id, Name = memberData.rolename, Level = memberData.lv,
                    ProfessionID = memberData.school, Sex = memberData.sex, GuildID = Game.me.guildId,
                    sourceID = Enum.EFriendAddSourceData.GUILD
                },
                Enum.EMenuType.GuildGroup
            )
        end
    end
end

function GuildInGroup_Panel:OnClickDownItem(index)
    self.downSelectedIndex = index
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:OnClickClose()
    self:CloseSelf()
end

function GuildInGroup_Panel:OnClickSearch()
    self.searchKeyWord = self.searchInput:GetUIBase():GetInputText()
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:OnInputChanged()
    self.searchKeyWord = self.searchInput:GetUIBase():GetInputText()
    if self.searchInput:GetUIBase():GetLength() == 0 then
        self:RefreshGroupMemberList()
    else
        self:OnClickSearch()
    end
end

function GuildInGroup_Panel:ToggleSortKey(keyName)
    local keyIndex
    for index, sortData in ipairs(self.sortRule) do
        if sortData.key == keyName then
            sortData.bDescending = not sortData.bDescending
            keyIndex = index
            break
        end
    end
    if keyIndex then
        table.insert(self.sortRule, 1, table.remove(self.sortRule, keyIndex))
    end
end


--- 此处为自动生成
---@param index number
function GuildInGroup_Panel:onWBP_TabList_luaComItemSelected(index)
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:OnClickInvite()
    if not Game.TeamSystem:IsInGroup() then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CLUB_GROUP_FIRST_CREATE)
        return
    end
    local curGroupId = self.wBP_TabList_luaCom:GetSelectedIndex()
    if curGroupId > 0 then
        Game.GuildSystem.sender:InviteGuildGroupMemberToGroup(curGroupId)
    end
end

function GuildInGroup_Panel:OnClickChangeName()
    if not Game.GuildSystem:HasRight(const.GUILD_RIGHT.GROUP_NAME) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NONE_GUILD_RIGHT)
        return
    end
    Game.NewUIManager:OpenPanel("GuildInMemberEditGroupNamePopup_Panel", self.tabData)
end

function GuildInGroup_Panel:OnClickSortByRole()
    self:ToggleSortKey("roles")
    self:PlayAnimation(self.view.WBP_Select_lua.WBP_GuildSwitchRole_lua.Ani_Press, nil, self.view.WBP_Select_lua.WBP_GuildSwitchRole_lua)
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:OnClickSortByLv()
    self:ToggleSortKey("lv")
    self:PlayAnimation(self.view.WBP_Select_lua.WBP_GuildSwitchLv_lua.Ani_Press, nil, self.view.WBP_Select_lua.WBP_GuildSwitchLv_lua)
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:OnClickSortByBattleScore()
    self:ToggleSortKey("power")
    self:PlayAnimation(self.view.WBP_Select_lua.WBP_GuildSwitchBattleScore_lua.Ani_Press, nil, self.view.WBP_Select_lua.WBP_GuildSwitchBattleScore_lua)
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:OnClickSortByOnline()
    self:ToggleSortKey("offlineTime")
    self:PlayAnimation(self.view.WBP_Select_lua.WBP_GuildSwitchOnline_lua.Ani_Press, nil, self.view.WBP_Select_lua.WBP_GuildSwitchOnline_lua)
    self:RefreshGroupMemberList()
end

function GuildInGroup_Panel:OnSetGroupNameSuccess()
    self.tabData = {}
    for index = 1, Game.GuildSystem:GetGroupNum() do
        table.insert(self.tabData, {tabName = Game.GuildSystem:GetGroupName(index)})
    end
    self.wBP_TabList_luaCom:RefreshData(self.tabData)
end

function GuildInGroup_Panel:GetSortDescending(keyName)
    for index, sortData in ipairs(self.sortRule) do
        if index == 1 and sortData.key == keyName then
            if sortData.bDescending then
                return 1
            else
                return 2
            end
        end
    end
    return 0
end

--- 此处为自动生成
function GuildInGroup_Panel:onWBP_ComPanel_luaCombackCb()
    self:CloseSelf()
end

--- 此处为自动生成
function GuildInGroup_Panel:onWBP_ComPanel_luaComtipsCb()
end

return GuildInGroup_Panel
