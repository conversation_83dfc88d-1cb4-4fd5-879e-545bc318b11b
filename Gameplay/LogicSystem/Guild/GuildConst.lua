local GuildConst = {}
local StringConst = require "Data.Config.StringConst.StringConst"
local const = kg_require("Shared.Const")

GuildConst.GUILD_CAMP_ID_NONE = 0


GuildConst.GUILD_CHANGE_POSTION_COUNT = 8
GuildConst.GUILD_CHANGE_POSTION_MEMBER = 7
GuildConst.GUILD_CHANGE_POSTION_APPRENTICE = 8

GuildConst.MaxBGNum = 4
-- 背景图
GuildConst.BGList = {
    UIAssetPath.UI_Guild_Img_Scenes03,
    UIAssetPath.UI_Loading_Img_bg04,
    UIAssetPath.UI_Gacha_Bg_GeneralPool
}

-- 公会类型名字
GuildConst.GuildTypeName = {
    [0] = "全部",
    [const.GUILD_TYPE.BATTLE] = StringConst.Get("GUILD_BATTLE"),
    [const.GUILD_TYPE.FRIEND] = StringConst.Get("GUILD_FRIEND"),
    [const.GUILD_TYPE.BUDDHA] = StringConst.Get("GUILD_EASY"),
}

GuildConst.GuildTypeIconPath = {
    [const.GUILD_TYPE.BATTLE] = UIAssetPath.UI_Guild_Img_Battle02_Sprite,
    [const.GUILD_TYPE.FRIEND] = UIAssetPath.UI_Guild_Img_MakingFriends02_Sprite,
    [const.GUILD_TYPE.BUDDHA] = UIAssetPath.UI_Guild_Img_BuddhistSystem02_Sprite,
}

-- 公会职位名字
GuildConst.GUILD_RIGHT_NAME =
{
    [const.GUILD_RIGHT.PRESIDENT] = StringConst.Get("GUILD_RIGHT_PRESIDENT"),
    [const.GUILD_RIGHT.BABY] = StringConst.Get("GUILD_RIGHT_BABY"),
    [const.GUILD_RIGHT.POSITION_SET] = StringConst.Get("GUILD_RIGHT_POSITION_SET"),
    [const.GUILD_RIGHT.INFO_SET] = StringConst.Get("GUILD_RIGHT_ANNOUNCE"),
    [const.GUILD_RIGHT.CONSTRUCTION] = StringConst.Get("GUILD_RIGHT_CONSTRUCTION"),
    [const.GUILD_RIGHT.ACTIVITY] = StringConst.Get("GUILD_RIGHT_OPEN_ACTIVITY"),
    [const.GUILD_RIGHT.MEMBER] = StringConst.Get("GUILD_RIGHT_ACCEPT_MEMBER"),
    [const.GUILD_RIGHT.MERGE] = StringConst.Get("GUILD_RIGHT_MERGE"),
    [const.GUILD_RIGHT.APPRENTICE] = StringConst.Get("GUILD_RIGHT_APPRENTICE"),
    [const.GUILD_RIGHT.KICKOUT] = StringConst.Get("GUILD_RIGHT_KICKOUT"),
    [const.GUILD_RIGHT.MODIFY_NAME] = StringConst.Get("GUILD_RIGHT_MODIFY_NAME"),
    [const.GUILD_RIGHT.GROUP_MESSAGE] = StringConst.Get("GUILD_RIGHT_GROUP_MESSAGE"),
    [const.GUILD_RIGHT.RESIGN]= StringConst.Get("GUILD_RIGHT_RESIGN"),
    [const.GUILD_RIGHT.AUTO_RECEIVE] = StringConst.Get("GUILD_RIGHT_AUTO_RECEIVE"),
    [const.GUILD_RIGHT.SKILL] = StringConst.Get("GUILD_RIGHT_SKILL"),
    [const.GUILD_RIGHT.SET_COMMAND] = StringConst.Get("GUILD_RIGHT_SET_COMMAND"),
    [const.GUILD_RIGHT.SET_LOGO] = StringConst.Get("GUILD_RIGHT_SET_LOGO"),
    [const.GUILD_RIGHT.MODIFY_PRESIDENT_STATUE] = StringConst.Get("GUILD_RIGHT_MODIFY_PRESIDENT_STATUE"),
    [const.GUILD_RIGHT.SET_BADGE_FRAME ]= StringConst.Get("GUILD_RIGHT_SET_BADGE_FRAME"),
    [const.GUILD_RIGHT.SET_ELITE] = StringConst.Get("GUILD_RIGHT_SET_ELITE"),
    [const.GUILD_RIGHT.BAN_ROOKIE_BID] = StringConst.Get("GUILD_RIGHT_BAN_ROOKIE_BID"),
    [const.GUILD_RIGHT.SET_GUILD_LEAGUE_ELITE] = StringConst.Get("GUILD_RIGHT_SET_GUILD_LEAGUE_ELITE"),
    [const.GUILD_RIGHT.CHANGE_GUILD_BABY_NAME] = StringConst.Get("GUILD_RENAME_MASCOT"),
    [const.GUILD_RIGHT.SEND_GUILD_MAIL] = StringConst.Get("GUILD_SEND_MAIL"),
    [const.GUILD_RIGHT.CHANGE_GUILD_BACKGROUND] = StringConst.Get("GUILD_BACKGROUND_MODIFY"),
    [const.GUILD_RIGHT.GROUP_NAME] = "修改分组名称",
    [const.GUILD_RIGHT.GROUP_CONTROL] = "修改组员分组",
}
-- 公会举报类型
GuildConst.GUILD_REPORT_TYPE =
{
    ILLEGAL = 1, -- 信息违规
    STUDIO = 2,  -- 工作室公会
    CHEATER = 3, -- 其他游戏的托
    OTHER = 4,   -- 其他
}
-- 公会举报类型名字
GuildConst.GUILD_REPORT_TYPE_STRING =
{
    [GuildConst.GUILD_REPORT_TYPE.ILLEGAL] = StringConst.Get("GUILD_REPORT_TYPE_ILLEGAL"),
    [GuildConst.GUILD_REPORT_TYPE.STUDIO] = StringConst.Get("GUILD_REPORT_TYPE_STUDIO"),
    [GuildConst.GUILD_REPORT_TYPE.CHEATER] = StringConst.Get("GUILD_REPORT_TYPE_CHEATER"),
    [GuildConst.GUILD_REPORT_TYPE.OTHER] =  StringConst.Get("GUILD_REPORT_TYPE_OTHER"),
}
-- 公会链接类型
GuildConst.GUILD_LINK_TYPE = {
    UI_GUILD_LINK_NAVI = 1,         --寻路追踪
    UI_GUILD_LINK_OPEN_UI = 2,      --打开UI
    UI_GUILD_LINK_GET_AWARD = 3 ,
    UI_GUILD_LINK_SIGN_IN = 4,
}

GuildConst.STRUCTURE_EDIT_TYPE = {
    ROLE = 1,
    GROUP = 2,
    MEMBER = 3
}

GuildConst.WelfareData = {
    {
        name = 'GUILD_WELFARE_SIGNIN_NAME',
        icon = Game.TableData.GetArtAssetIconDataRow(_G.Enum.EArtAssetIconData.GUILD_SIGNIN).AssetPath,
        description = 'GUILD_WELFARE_SIGNIN_DESC',
        operation = 'GUILD_WELFARE_SIGNIN_OPR',
        disabledOperation = 'GUILD_SIGNIN_ALREADY',
        type = 4,
    },
    {
        name = 'GUILD_WELFARE_SKILL_NAME',
        icon = Game.TableData.GetArtAssetIconDataRow(_G.Enum.EArtAssetIconData.GUILD_PRACTICE).AssetPath,
        description = 'GUILD_WELFARE_SKILL_DESC',
        operation = 'GUILD_WELFARE_SKILL_OPR',
        type = 2,
        param = { "P_GuildInsideSkill", { tabType = 1 } },
    },
    {
        name = 'GUILD_WELFARE_WAGE_NAME',
        icon = Game.TableData.GetArtAssetIconDataRow(_G.Enum.EArtAssetIconData.GUILD_WAGES).AssetPath,
        description = 'GUILD_WELFARE_WAGE_DESC',
        operation = 'GUILD_WELFARE_WAGE_OPR',
        type = 3,
        param = {},
    },
    {
        name = 'GUILD_WELFARE_SHOP_NAME',
        icon = Game.TableData.GetArtAssetIconDataRow(_G.Enum.EArtAssetIconData.GUILD_SHOP).AssetPath,
        description = 'GUILD_WELFARE_SHOP_DESC',
        operation = 'GUILD_WELFARE_SHOP_OPR',
        type = 1,
        param = {},
    },
    -- 填充一个空表用于展示（敬请期待）
    {}
}



return GuildConst

