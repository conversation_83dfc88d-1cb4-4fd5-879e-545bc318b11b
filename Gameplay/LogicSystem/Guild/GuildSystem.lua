---@class GuildSystem
local GuildSystem = DefineClass("GuildSystem", SystemBase)
local GuildConst = kg_require "Gameplay.LogicSystem.Guild.GuildConst"
local const = kg_require("Shared.const")
local StringConst = require "Data.Config.StringConst.StringConst"
local worldConst = kg_require "Shared.const.WorldConst"
local GuildUtils = kg_require("Shared.Utils.GuildUtils")
local lume = kg_require("Shared.lualibs.lume")
local TaskStatusConst = kg_require("Shared.Const.QuestConst").TaskStatusConst

GuildSystem.guildDetailCacheValidTime = 60  -- 单位: s

function GuildSystem:onInit()
    ---@type GuildSystemModel
    self.model = kg_require("Gameplay.LogicSystem.Guild.GuildSystemModel").new(false,true)
    ---@type GuildSystemSender
    self.sender = kg_require("Gameplay.LogicSystem.Guild.GuildSystemSender").new()
	Game.EventSystem:AddListener(_G.EEventTypes.GAME_MAINPALYER_LOGIN,self,self.OnInitGuildApplyNum)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.RECEIVE_GUILD_APPLIES, "InitGuildApplyNum", self)
	
    ---公会活动start
    Game.GlobalEventSystem:AddListener(EEventTypesV2.GUILD_ON_SYNC_GUILD_ANSWER_ACTIVITY_INFO, "onSyncGuildAnswerActivityInfo", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.GUILD_ON_UPDATE_GUILD_ANSWER_ACTIVITY_INFO, "onUpdateGuildAnswerActivityInfo", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED, "OnLevelLoadCompleted", self)
    ---公会活动end
    Game.GlobalEventSystem:AddListener(EEventTypesV2.LOADING_ON_LOADING_COMPLETE, "OnLoadingLevelComplete", self)

    Game.InputSystem:RegisterSpecialUIJumpHandler("OpenGuildPanel_Action", function(actionName, keyActionCfg, actionTypeID, uiJumpCfg)
        self:ShowGuildMain(nil, true)
        return true
    end)
end

function GuildSystem:onUnInit()
    Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function GuildSystem:CheckGuildSkillCanOpen()
    if not self.model.guildPlayer.enterTime then
        return false
    end
    local DayGap = _G._now()/1000 -  self.model.guildPlayer.enterTime
    if (DayGap / 86400 * 24) < Game.TableData.GetConstDataRow("GUILD_SKILL_LEARN_HOURS") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_ACTIVITY_ENTER_TIME_LIMIT)
        return false
    end
    local PlayerLevel = GetMainPlayerPropertySafely( "Level")
    if PlayerLevel and PlayerLevel < Game.TableData.GetConstDataRow("GUILD_EXERCISE_OPEN_LV") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_EXERCISE_UNOPEN_LEVEL_LIMIT)
        return false
    end
    return true
end

function GuildSystem:HasJoinGuild(entity)
    if entity then
        return entity and entity.guildId ~= const.GUILD_ID_NONE and entity.guildStatus == const.GUILD_STATUS.COMMON or false
    end
    return Game.me.guildId ~= const.GUILD_ID_NONE and Game.me.guildStatus == const.GUILD_STATUS.COMMON
end

function GuildSystem:IsInGuildResponse()
    return Game.me.guildId ~= const.GUILD_ID_NONE and Game.me.guildStatus == const.GUILD_STATUS.INIT
end

function GuildSystem:CheckIsInGuildResponse(targetGuildId, targetGuildStatus)
    -- local targetGuildStatus = self:GetGuildStatus(targetGuildId)
    -- TODO 判断是否为响应
    return targetGuildId ~= const.GUILD_ID_NONE and targetGuildStatus == const.GUILD_STATUS.INIT
end

function GuildSystem:ApplyResponseGuild(targetId)
    if self:HasJoinGuild() then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ALREADY_IN_GUILD)
        return
    end
    if Game.me.applyGuildMap[targetId] then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_APPLY_REPET)
        return
    end
    self.sender:applyGuild(targetId)
end

function GuildSystem:ReqCancelApplyGuild(targetId)
    if self:HasJoinGuild() then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ALREADY_IN_GUILD)
        return
    end
    self.sender:ReqCancelApplyGuild(targetId)
end

function GuildSystem:IsInGuildCreate()
    return self:IsInGuildResponse() and self:IsGuildLeader()
end

function GuildSystem:CheckActivityChoiceValid(data)
    if data then
        if data["activityId"] == 33 then
            if self.model.activityInfos[const.GUILD_NIGHT_ACTIVITY_ID] then
                return true
            else
                return false
            end
        elseif data["activityId"] == 34 then
            local taskInfo = Game.QuestSystem:GetQuestInfo(data.param[1])
            if taskInfo then
                if taskInfo.Status == TaskStatusConst.TASK_STATUS__FINISHED then
                    return false
                else
                    return true
                end
            end
        end
    end
    local PlayerInfo = self.model.guildPlayer
    if PlayerInfo then
        return true
    end
end

function GuildSystem:OnGuildIDChanged(EntityID, PropName, NewValue, OldValue)
    if EntityID == Game.me.eid and OldValue ~= const.GUILD_ID_NONE and NewValue == const.GUILD_ID_NONE then
        self.model:init()
        Game.GlobalEventSystem:Publish(EEventTypesV2.LEAVE_GUILD)
    end
end

function GuildSystem:ProIDToName(ProfID)
    local ClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(ProfID)
    if ClassInfo then
        return ClassInfo[0].ClassName
    end
    return ""
end

---公会活动成员函数start
---

function GuildSystem:GetNightActivityStartTime()
    if self.model.activityInfos and self.model.activityInfos[const.GUILD_NIGHT_ACTIVITY_ID] then
        return self.model.activityInfos[const.GUILD_NIGHT_ACTIVITY_ID][1]
    end
    return nil
end

function GuildSystem:GuildActivityPopout()
    local currentLocalSpace = Game.NetworkManager.GetLocalSpace()
    if not currentLocalSpace then
        return
    end
    if currentLocalSpace.WorldType == worldConst.WORLD_TYPE.DUNGEON or 
        currentLocalSpace.WorldType == worldConst.WORLD_TYPE.TEAM_ARENA_33 or 
        currentLocalSpace.WorldType == worldConst.WORLD_TYPE.GUILD_STATION then
        return
    end
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GUILD_PARTY_INVITE,function()
        self:ActivityEnterGuild()
    end)
end

function GuildSystem:ActivityEnterGuild()
    if self:HasJoinGuild() then
        if self.model.activityInfos[const.GUILD_NIGHT_ACTIVITY_ID] then
            self:EnterGuild()
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_PARTY_UNOPENED)
            return
        end
    else
        if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_GUILD", true) then
            self:ShowGuildMain()
        end
    end
end

function GuildSystem:OnGuildBuffIDChangedManager(_, newValue, oldValue)
    local bRare = Game.TableData.GetGuildPartyRewardDataRow(newValue).IsRare
    local buffName = "InvalidBuffName"
    if bRare and bRare == 1 then
        local ReminderData = Game.TableData.GetReminderTextDataRow(Enum.EReminderTextData.GUILD_PARTY_RARE_BUFF_SHOW)
        Game.me:sendChatMessage(
            Enum.EChatChannelData["GUILD"], string.format(ReminderData.Text1, Game.me.Name), Enum.EChatMessageType.TEXT,
            nil, nil, true, false
        )
    else
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_PARTY_BUFF, { { buffName } })
    end
end

function GuildSystem:OnLevelLoadCompleted()
    local currentLocalSpace = Game.NetworkManager.GetLocalSpace()
    if currentLocalSpace then
        if currentLocalSpace.WorldType ~= worldConst.WORLD_TYPE.GUILD_STATION then
            table.clear(self.model.answerActivityInfo)
        end
    end
end

function GuildSystem:onOpenGuildActivity(activityID, startTime, endTime)
    self.model.activityInfos[activityID] = {startTime,endTime}
    if activityID ~= const.GUILD_ANSWER_ACTIVITY_ID then
        return
    end
    local currentSpace = Game.NetworkManager.GetLocalSpace()
    if currentSpace and currentSpace.WorldType and currentSpace.WorldType == 4 then
        if not UI.IsShow("P_HUDGuildQuestionBoard") then
            Game.HUDSystem:ShowUI("P_HUDGuildQuestionBoard")
        end
    end
end

function GuildSystem:onCloseGuildActivity(activityId)
    self.model.activityInfos[activityId] = nil
    if activityId == const.GUILD_ANSWER_ACTIVITY_ID then
        table.clear(self.model.answerActivityInfo)
    end
end

function GuildSystem:onSyncGuildSystemActivities(activityInfos)
    self.model.activityInfos = activityInfos
end

function GuildSystem:onSyncGuildAnswerActivityInfo(info)
    self.model.answerActivityInfo = info
    Game.LSceneActorEntityManager:CallFunc(tostring(Game.TableData.GetConstDataRow("GUILD_ANSWER_AREA_INTERACTOR_INSID")), "RefreshByAnswerIndex", Game.me.GuildAnswerIndex)
end

function GuildSystem:onUpdateGuildAnswerActivityInfo(info)
    for key, value in pairs(info) do
        self.model.answerActivityInfo[key] = value
    end
end

function GuildSystem:OnSyncAnswerChoice(index)
    self.model.myAnswerActivityChoice = index
end

function GuildSystem:PickUpBuff()
    if not self.model.activityInfos[const.GUILD_NIGHT_ACTIVITY_ID] then
        return
    end
    self.GuildAfkActivityCurrencyType = Game.TableData.GetConstDataRow("GUILD_PARTY_BUFF_REFRESH_MONEY_TYPE")
    self.GuildAfkActivityCurrencyAmount = Game.TableData.GetConstDataRow("GUILD_PARTY_BUFF_REFRESH_MONEY_AMOUNT")
    local iconID = Game.TableData.GetItemNewDataRow(self.GuildAfkActivityCurrencyType).icon
    local imgPath = nil
    if iconID then
        imgPath = Game.UIIconUtils.GetIconByItemId(self.GuildAfkActivityCurrencyType)
    end
    if Game.me and Game.me.guildBlessBuffID and self.model.activityInfos[const.GUILD_NIGHT_ACTIVITY_ID][1] <= Game.me.guildBlessBuffStartTime then
        local bRare = Game.TableData.GetGuildPartyRewardDataRow(Game.me.guildBlessBuffID).IsRare
        if bRare == 1 then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GUILD_PARTY_BUFF_REFRESH, function() 
                Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(self.GuildAfkActivityCurrencyType, self.GuildAfkActivityCurrencyAmount, function() 
                    self.sender:refreshGuildBless("", true) 
                end)
            end, nil, {imgPath, tostring(self.GuildAfkActivityCurrencyAmount)})
        else
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GUILD_PARTY_RARE_BUFF_REFRESH, function() 
                Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(self.GuildAfkActivityCurrencyType, self.GuildAfkActivityCurrencyAmount, function()
                    self.sender:refreshGuildBless("", true)
                end)
            end, nil, {imgPath, tostring(self.GuildAfkActivityCurrencyAmount)})
        end
    else
        self.sender:refreshGuildBless("", false)
    end
end

function GuildSystem:CheckCostBlessMoney()
    local StartTime = self:GetNightActivityStartTime() or 0
    local BuffTime = Game.me.guildBlessBuffStartTime or 0
    return BuffTime >= StartTime
end

function GuildSystem:ReqRefreshGuildBless(InsID)
    self.sender:refreshGuildBless(InsID, self:CheckCostBlessMoney())
end

function GuildSystem:OnSetGuildAnswerIndex(entity, new, old)
    UI.Invoke("P_HUDGuildQuestionBoard","OnGuildAnswerChoiceChanged",entity,new,old)
    Game.LSceneActorEntityManager:CallFunc(tostring(Game.TableData.GetConstDataRow("GUILD_ANSWER_AREA_INTERACTOR_INSID")), "RefreshByAnswerIndex", new)
end

---公会活动成员函数end
---
function GuildSystem:CheckWelfareChoiceValid(index, bShowReminder)
    local data = GuildConst.WelfareData[index]
    if data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_NAVI then
        return true
    elseif data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_OPEN_UI then
        --技能修炼检查
        if not self.model.guildPlayer then
            return false
        end
        if data.param[1] == "P_GuildInsideSkill" then
            if not self.model.guildPlayer.enterTime then
                return false
            end
            local DayGap = _G._now()/1000 - self.model.guildPlayer.enterTime
            if (DayGap / 86400 * 24) < Game.TableData.GetConstDataRow("GUILD_SKILL_LEARN_HOURS") then
                if bShowReminder then
                    Game.ReminderManager:AddReminderById(
                        Enum.EReminderTextData.GUILD_ACTIVITY_ENTER_TIME_LIMIT
                    )
                end
                return false
            end
            local PlayerLevel = GetMainPlayerPropertySafely( "Level")
            if PlayerLevel and PlayerLevel < Game.TableData.GetConstDataRow("GUILD_EXERCISE_OPEN_LV") then
                if bShowReminder then
                    Game.ReminderManager:AddReminderById(
                        Enum.EReminderTextData.GUILD_EXERCISE_UNOPEN_LEVEL_LIMIT
                    )
                end
                return false
            end
        end
        return true
    elseif data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_GET_AWARD then
        if not self.model.GuildWelfareWage then
            return false
        end
        if self.model.GuildWelfareWage.bWageReady then
            if bShowReminder then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_WAGE_RECEIVE)
            end
            return false
        end
        if not self.model.GuildWelfareWage.wage or self.model.GuildWelfareWage.wage <= 0 then
            if bShowReminder then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_WAGE_COUNT_ZERO)
            end
            return false
        end
        return true
    elseif data.type == GuildConst.GUILD_LINK_TYPE.UI_GUILD_LINK_SIGN_IN then
        local LastTS
        if self.model.SignatureInfo.lastSignInTime then
            LastTS = self.model.SignatureInfo.lastSignInTime
        end
        if not LastTS then
            return false
        end
        local _, DayGap = Game.TimeUtils.GetCumulativeRefreshDayCount(LastTS, _G._now() / 1000, { Game.TimeUtils.DROP_LIMIT_TYPE.DAY, 5, 0, 0 })
        if LastTS ~= 0 and DayGap < 1 then
            if bShowReminder then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SIGNIN_ALREADY_SIGNED)
            end
            return false
        end
        if self.model.SignatureInfo and self.model.SignatureInfo.signature == "" then
            if bShowReminder then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SIGNIN_EMPTY)
            end
            return false
        end
        return true
    end
end


function GuildSystem:memberHasRight(role, rightKey, CustomRight)
    local grdd = Game.TableData.GetGuildRightDataRow(role)
    local rightType = grdd and grdd.Right and grdd.Right[rightKey]
    local custom
    if CustomRight then
        custom = CustomRight[role] or {}
    else
        custom = self.model.CustomRoleRights[role] or {}
    end

    if custom[rightKey] ~= nil and (rightType == const.GUILD_RIGHT_VARIABLE_TRUE or rightType == const.GUILD_RIGHT_VARIABLE_FALSE) then
        return custom[rightKey]
    end

    if rightType == const.GUILD_RIGHT_TRUE or rightType == const.GUILD_RIGHT_VARIABLE_TRUE then
        return true
    else
        return false
    end
end

function GuildSystem:ClearCustomRoleRights()
    table.clear(self.model.CustomRoleRights)
end

function GuildSystem:CanEditRight(role, rightKey)
    local roleData = Game.TableData.GetGuildRightDataRow(role)
    local rightType = roleData.Right[rightKey]
    if rightType == const.GUILD_RIGHT_TRUE or rightType == const.GUILD_RIGHT_FALSE  then
        return false
    else
        return true
    end
end

function GuildSystem:CanEditRole(roles, targetRoleID)
    return self:CheckCanEditRole(roles[const.GUILD_ROLE_TYPE.COMMON_ROLE], targetRoleID) 
        or self:CheckCanEditRole(roles[const.GUILD_ROLE_TYPE.HONOR_ROLE], targetRoleID)
        or self:CheckCanEditRole(roles[const.GUILD_ROLE_TYPE.GROUP_ROLE], targetRoleID)
    -- return self:CheckHasRight(self.model.guildPlayer.roles, rightKey, self.model.CustomRoleRights)
end

function GuildSystem:CheckCanEditRole(curRoleID, targetRoleID)
    if curRoleID == const.GUILD_ROLE.PRESIDENT then
        return true
    elseif curRoleID == const.GUILD_ROLE.VICE_PRESIDENT then
        return curRoleID ~= const.GUILD_ROLE.PRESIDENT and targetRoleID ~= const.GUILD_ROLE.VICE_PRESIDENT
    elseif curRoleID == const.GUILD_ROLE.DIRECTOR then
        return targetRoleID == const.GUILD_ROLE.BABY and self:HasRight(const.GUILD_RIGHT.BABY) or 
            ((targetRoleID == const.GUILD_ROLE.GROUP_MEMBER or targetRoleID == const.GUILD_ROLE.GROUP_LEADER) and self:HasRight(const.GUILD_RIGHT.GROUP_CONTROL))
            or targetRoleID == const.GUILD_ROLE.MEMBER or targetRoleID == const.GUILD_ROLE.APPRENTICE
    elseif curRoleID == const.GUILD_ROLE.GROUP_LEADER then
        return targetRoleID == const.GUILD_ROLE.GROUP_MEMBER and self:HasRight(const.GUILD_RIGHT.GROUP_CONTROL)
            or (targetRoleID == const.GUILD_ROLE.APPRENTICE or targetRoleID == const.GUILD_ROLE.MEMBER) and self:HasRight(const.GUILD_RIGHT.APPRENTICE)
    elseif curRoleID == const.GUILD_ROLE.BABY then
        return targetRoleID == const.GUILD_ROLE.APPRENTICE or targetRoleID == const.GUILD_ROLE.MEMBER
    end
    return false
end

function GuildSystem:setCustomRoleRights(Rights)
    self.sender:setCustomRoleRights(Rights)
    self.model.CustomRoleRights[RoleID] = Rights
end

function GuildSystem:HasRight(rightKey)
    local roles = self.model.guildPlayer.roles
    if roles == nil then
        return false
    end
    local roleRights = self.model.CustomRoleRights
    return self:CheckHasRight(roles[const.GUILD_ROLE_TYPE.COMMON_ROLE], rightKey, roleRights) 
        or self:CheckHasRight(roles[const.GUILD_ROLE_TYPE.HONOR_ROLE], rightKey, roleRights)
        or self:CheckHasRight(roles[const.GUILD_ROLE_TYPE.GROUP_ROLE], rightKey, roleRights)
    -- return self:CheckHasRight(self.model.guildPlayer.roles, rightKey, self.model.CustomRoleRights)
end

function GuildSystem:CheckHasRight(roleID, rightKey, CustomRight)
    return self:memberHasRight(roleID, rightKey, CustomRight)
end

function GuildSystem:GetRemainTimeText(RemainTime)
    if RemainTime <= 60000 then
        return StringConst.Get("GUILD_LESS_THAN_CERTAIN_MINUTE", 1)
    elseif RemainTime <= 3600000 then
        return Game.TimeUtils.FormatCountDownString(RemainTime, true)
    else
        return Game.TimeUtils.FormatCountDownString(RemainTime, true)
    end
end

function GuildSystem:FindMemberByEntityID(Eid)
    for K,V in ipairs(self.model.guildMembers) do
        if V.id == Eid then
            return V
        end
    end
end

function GuildSystem:GetCurrentTotalCount()
    local pubLv = self:GetBuildingLevel(const.GUILD_BUILDING_TYPE.PUB)
    local gfd = Game.TableData.GetGuildFuncDataRow(pubLv)
    return gfd.TotalMaxMemberNum
end

function GuildSystem:GetMaxMemberNum()
    return self:GetCurrentTotalCount() - self:GetMaxApprenticeNum()
end

function GuildSystem:GetMemberNum()
    return self.model.GuildPrincipalInfo.memberNum or 0 + self:GetApprenticeNum()
end

function GuildSystem:GetMaxApprenticeNum()
    return Game.TableData.GetGuildRightDataRow(const.GUILD_ROLE.APPRENTICE).MaxNum
end

function GuildSystem:GetApprenticeNum()
    return self.model.GuildPrincipalInfo.apprenticeNum or 0
end

function GuildSystem:GetRoleNumAndMaxNum(RoleID)
    if RoleID < GuildConst.GUILD_CHANGE_POSTION_MEMBER then
        local RoleData =  Game.TableData.GetGuildRightDataRow(RoleID)
        if RoleID == 1 then ---会长的角色RoleID
            if self.model.GuildRoleMemberMap[10]~=nil then --领袖加会长的角色
                return self.model.GuildRoleMemberMap[10]~=nil and #self.model.GuildRoleMemberMap[10] or 0, RoleData~=nil and RoleData.MaxNum or 0
            end
            return self.model.GuildRoleMemberMap[RoleID]~=nil and #self.model.GuildRoleMemberMap[RoleID] or 0, RoleData~=nil and RoleData.MaxNum or 0
        end

        return self.model.GuildRoleMemberMap[RoleID]~=nil and #self.model.GuildRoleMemberMap[RoleID] or 0, RoleData~=nil and RoleData.MaxNum or 0
    elseif RoleID < GuildConst.GUILD_CHANGE_POSTION_APPRENTICE then
        return self.model.GuildRoleMemberMap[RoleID]~=nil and #self.model.GuildRoleMemberMap[RoleID] or 0, self:GetMaxMemberNum()
    else
        return self.model.GuildRoleMemberMap[RoleID]~=nil and #self.model.GuildRoleMemberMap[RoleID] or 0, self:GetMaxApprenticeNum()
    end
end

function GuildSystem:GetRoleMaxNum(RoleID)
    if RoleID < GuildConst.GUILD_CHANGE_POSTION_MEMBER then
        local RoleData =  Game.TableData.GetGuildRightDataRow(RoleID)
        return RoleData~=nil and RoleData.MaxNum or 0
    elseif RoleID <  GuildConst.GUILD_CHANGE_POSTION_APPRENTICE then
        return self:GetMaxMemberNum()
    else
        return self:GetMaxApprenticeNum()
    end
end

function GuildSystem:GetApprenticeNumText()
    return string.format("%i/%i/%i",
        self.model.GuildPrincipalInfo.onlineApprenticeNum or 0,
        self:GetApprenticeNum(),
        self:GetMaxApprenticeNum()
    )
    
end

function GuildSystem:GetTotalMemberNumText()
    if self.model.GuildPrincipalInfo then
        local onlineMemberNum = self.model.GuildPrincipalInfo.onlineMemberNum and self.model.GuildPrincipalInfo.onlineMemberNum or 0
        return string.format("%i/%i/%i",
            tonumber(onlineMemberNum),
            self:GetMemberNum(),
            self:GetMaxMemberNum()
        )
    else
        return string.format("%i/%i/%i",0,0,0)
    end
end

function GuildSystem:GetTotalGuildPlayerNumText()
    if self.model.GuildPrincipalInfo then
        local onlineMemberNum = self.model.GuildPrincipalInfo.onlineMemberNum and self.model.GuildPrincipalInfo.onlineMemberNum or 0
        local onlineApprenticeNum = self.model.GuildPrincipalInfo.onlineApprenticeNum and self.model.GuildPrincipalInfo.onlineApprenticeNum or 0
        return string.format(
            "%i/%i/%i",
            tonumber(onlineMemberNum + onlineApprenticeNum),
            self:GetMemberNum() + self:GetApprenticeNum(),
            self:GetMaxApprenticeNum() + self:GetMemberNum()
        )
    else
        return string.format("%i/%i/%i",0,0,0)
    end
end

-- 获取公会资金
function GuildSystem:GetMaxFunds()
    local formulaFunc = Game.FormulaManager.GetFormulaFuncByName("Guild_Max_FundsFormula")
    if formulaFunc == nil then
        return 0
    end
    local GuildTradingBuildingLv = self:GetBuildingLevel(const.GUILD_BUILDING_TYPE.VAULT)
    return formulaFunc(GuildTradingBuildingLv)
end

function GuildSystem:SearchGuildList(KeyWord,List,Keys)
    local ResultList = {}
    for K, WidgetData in ipairs(List) do
        for _, Key in ipairs(Keys)do
            if WidgetData[Key] and string.startsWith(tostring(WidgetData[Key]), KeyWord) then
                table.insert(ResultList, WidgetData)
                break
            end
        end
    end
    return DeepCopy(ResultList)
end

function GuildSystem:OnPopTopAndToggleSortKey(SortRule,Key)
    local RuleTopPop 
    local IdxToPop 
    for K,V in ipairs(SortRule) do
        if V.Key == Key then
            RuleTopPop = V
            IdxToPop = K
            V.bDescending = not V.bDescending
        end
    end
    if IdxToPop then
        if SortRule[IdxToPop] then
            table.remove(SortRule,IdxToPop)
        end
        table.insert(SortRule,1,RuleTopPop)
    end
    return RuleTopPop and RuleTopPop.bDescending
end

function GuildSystem:SortListByRule(dataList, sortRule)
    table.sort(dataList, function(memberA, memberB)
        for _, sortData in pairs(sortRule) do
            if not sortData.bIsBanSort then
                local aValue, bValue = memberA[sortData.key], memberB[sortData.key]
                if not aValue or not bValue then
                    return true
                end
                if sortData.key == "roles" then
                    local aRoleData, bRoleData = memberA[sortData.key], memberB[sortData.key]
                    if #aRoleData == 0 or #bRoleData == 0 then
                        if sortData.bDescending then
                            return #aRoleData < #bRoleData
                        else
                            return #aRoleData > #bRoleData
                        end
                    end
                    local aCommonValue, bCommonValue = aRoleData[const.GUILD_ROLE_TYPE.COMMON_ROLE], bRoleData[const.GUILD_ROLE_TYPE.COMMON_ROLE]
                    local aHonorValue, bHonorValue = aRoleData[const.GUILD_ROLE_TYPE.HONOR_ROLE], bRoleData[const.GUILD_ROLE_TYPE.HONOR_ROLE]
                    local aGroupValue, bGroupValue = aRoleData[const.GUILD_ROLE_TYPE.GROUP_ROLE], bRoleData[const.GUILD_ROLE_TYPE.GROUP_ROLE]
                    aValue, bValue = Game.TableData.GetGuildRightDataRow(aCommonValue).Rank, Game.TableData.GetGuildRightDataRow(bCommonValue).Rank
                    if aHonorValue ~= 0 then
                        aValue = math.max(aValue, Game.TableData.GetGuildRightDataRow(aHonorValue).Rank)
                    end
                    if aGroupValue ~= 0 then
                        aValue = math.max(aValue, Game.TableData.GetGuildRightDataRow(aGroupValue).Rank)
                    end
                    if bHonorValue ~= 0 then
                        bValue = math.max(bValue, Game.TableData.GetGuildRightDataRow(bHonorValue).Rank)
                    end
                    if bGroupValue ~= 0 then
                        bValue = math.max(bValue, Game.TableData.GetGuildRightDataRow(bGroupValue).Rank)
                    end
                end
                if sortData.key == "offlineTime" then
                    if aValue == 0 then
                        aValue = math.huge
                    end
                    if bValue == 0 then
                        bValue = math.huge
                    end
                end
                if aValue ~= bValue then
                    if sortData.bDescending then
                        return aValue < bValue
                    else
                        return aValue > bValue
                    end
                end
            end
        end
        -- return tostring(memberA) > tostring(memberB)
    end)
    return dataList
end

function GuildSystem:inGuildCreating()
    return Game.me.guildId ~= const.GUILD_ID_NONE and Game.me.guildStatus == const.GUILD_STATUS.INIT and self:IsGuildLeader()
end

function GuildSystem:IsGuildLeader()
    if Game.me.guildId == const.GUILD_ID_NONE  then
        return false
    end
    return self:GetSelfGuildCommonRole() == const.GUILD_ROLE.PRESIDENT or Game.me.eid == self.model.preGuildInfos.leaderId
end

function GuildSystem:HasTitle()
    if self.model.guildPlayer and (self:GetSelfGuildCommonRole() == const.GUILD_ROLE.MEMBER or self:GetSelfGuildCommonRole() == const.GUILD_ROLE.APPRENTICE) then
        return false
    end
    return true
end

function GuildSystem:IsBoss(EntityID)
    local Role = self:FindMemberByEntityID(EntityID)
    return Role and Role.roles and Role.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] == const.GUILD_ROLE.PRESIDENT or false
end

function GuildSystem:RefreshGuildSign()
    if not self.model.guildPlayer then
        self.model.SignatureInfo  = {}
    else
        self.model.SignatureInfo  = {
            signature = self.model.guildPlayer.signature,
            lastSignInTime = self.model.guildPlayer.lastSignInTime,
            isVoiceSignature = self.model.guildPlayer.isVoiceSignature
        }
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.REFRESH_GUILD_SIGN_IN)
end

function GuildSystem:initGuildWelfare()
    self.model.GuildWelfareWage.bWageReady = self.model.guildPlayer.wageFlag
    self.model.GuildWelfareWage.wage = self.model.guildPlayer.lastWeekWage
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_REFRESH_GUILD_WELFARE)
end

---检查公会名字是否有问题
function GuildSystem:CheckGuildNameValid(Name)
    if Name == nil or Name == "" then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_NAME_NULL)
        return false
    end

    local NameLen = utf8.len(Name)
    if NameLen < Game.TableData.GetConstDataRow("GUILD_NAME_WORD_NUM_LOWER_LIMIT") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_NAME_WORD_TOO_FEW)
        return false
    end

    if NameLen > Game.TableData.GetConstDataRow("GUILD_NAME_WORD_NUM_UPPER_LIMIT") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_NAME_INPUT_MAX)
        return false
    end
    
    -- 检查是否为中文
    if not UIHelper:CheckChinese(Name) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_NAME_NOT_CHINESE)
        return false
    end
    
    return true
end

---检查公会签到的合法性
function GuildSystem:CheckGuildSignatureValid(Name)
    local GUILD_SIGNIN_WORD_LOWER_LIMIT = Game.TableData.GetConstDataRow("GUILD_SIGNIN_WORD_LOWER_LIMIT")
    local GUILD_SINGIN_WORD_UPPER_LIMIT = Game.TableData.GetConstDataRow("GUILD_SINGIN_WORD_UPPER_LIMIT")

    if Name == nil or Name == "" then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SIGNATURE_SHOULD_EDIT)
        return false
    end

    local NameLen = utf8.len(Name)
    if NameLen < GUILD_SIGNIN_WORD_LOWER_LIMIT then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SIGNATURE_WORLD_TOO_FEW, {{GUILD_SIGNIN_WORD_LOWER_LIMIT}})
        return false
    end

    if NameLen > GUILD_SINGIN_WORD_UPPER_LIMIT then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_NAME_INPUT_MAX)
        return false
    end
    
    -- 检查是否为中文
    if not UIHelper:CheckChinese(Name) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SIGNIN_NOT_CHINESE)
        return false
    end

    return true
end

function GuildSystem:CheckGuildDesc(Name)
    if Name == nil or Name == "" then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_DECLARATION_NULL)
        return false
    end

    local NameLen = utf8.len(Name)
    if NameLen < Game.TableData.GetConstDataRow("GUILD_ANNOUNCE_WORD_NUM_LOWER_LIMIT") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_ANNOUNCE_WORD_TOO_FEW)
        return false
    end
    
    if NameLen > Game.TableData.GetConstDataRow("GUILD_ANNOUNCE_WORD_NUM_UPPER_LIMIT") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_NAME_INPUT_MAX)
        return false
    end

    return true
end

function GuildSystem:GetGuildDetail(guildId, forceRefresh, bIsResponse)
    local cacheData = self.model.guildDetailCache[guildId] or {
        cachedTime = 0, guildId = guildId, guildDeclaration = "", guildMembers = {},
        badgeFrameId = 1, badgeIndex = 1, guildTimeLiveInfo = {}, guildProfessionInfo = {id = 1200001, num = 0}
    }
    if guildId == Game.me.guildId then
        cacheData.badgeIndex = Game.me.guildBadgeIndex
        cacheData.badgeFrameId = Game.me.guildBadgeFrameId
    end
    if _G._now() / 1000 - cacheData.cachedTime >= GuildSystem.guildDetailCacheValidTime or forceRefresh then
        if bIsResponse then
            self.sender:ReqResponseGuildInfo(guildId)
        else
            self.sender:getGuildDetail(guildId)
        end
    end
    
    return cacheData
end

function GuildSystem:onSetGuildAutoAgree(bCheck)
    self.model.bAutoAcceptApprenticeCache = bCheck
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SET_GUILD_APPLY_AUTO_AGREE, bCheck)
end

function GuildSystem:GetGuildApplies()
    if _G._now() - self.model.guildApplyTimestamp <= 3000 then
        Game.GlobalEventSystem:Publish(
            EEventTypesV2.RECEIVE_GUILD_APPLIES,
            self.model.guildApplyCache, self.model.bAutoAcceptApprenticeCache
        )
    else
        self.sender:getGuildApplys()
    end
end

function GuildSystem:onGetGuildApplys(guildApplys, bAutoAcceptApprentice)
    self.model.guildApplyTimestamp = _G._now()
    for _, data in ipairs(guildApplys) do
        data.sex = data.school % 10
        data.school = data.school // 10
    end
    self.model.guildApplyCache = guildApplys
    self.model.bAutoAcceptApprenticeCache = bAutoAcceptApprentice
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_APPLIES, guildApplys, bAutoAcceptApprentice)
end

function GuildSystem:GetGuildRecommendInfo()
    if _G._now() - self.model.guildInviteTimestamp <= 5000 then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GET_GUILD_RECOMMEND_INFOS, self.model.guildInviteCache)
    else
        self.sender:getGuildRecommendInfo()
    end
end

function GuildSystem:onGetGuildRecommendInfos(infos)
    self.model.guildInviteTimestamp = _G._now()
    self.model.guildInviteCache = infos
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GET_GUILD_RECOMMEND_INFOS, infos)
end

function GuildSystem:RoleIDToRoleName(roleId, slotId)
    local GuildRoleData = Game.TableData.GetGuildRightDataRow(roleId)
    --if roleId == const.GUILD_ROLE.BABY and self.model.GuildPrincipalInfo then
    --    local name = self.model.GuildPrincipalInfo.guildBabyName or ""
    --    if utf8.len(name) > 0 then
    --        return name
    --    end 
    --end
    if not GuildRoleData then return "" end
    if slotId then
        return self:GetGroupName(slotId) .. GuildRoleData.Name
    end
    return GuildRoleData.Name
end

-- 获取公会建筑等级
function GuildSystem:GetBuildingLevel(BuildingType)
    return GuildUtils.getGuildBuildingLv(self.model.GuildBuildings or {}, BuildingType)
end

-- 获取当前建筑等级上限
function GuildSystem:GetBuildingMaxLv(BuildingType)
    if BuildingType == const.GUILD_BUILDING_TYPE.MAIN then
        return Game.TableData.GetConstDataRow("GUILD_LEVEL_TOPLIMIT")
    elseif BuildingType == const.GUILD_BUILDING_TYPE.PUB then
        return Game.TableData.GetConstDataRow("GUILD_PUB_LEVEL_TOPLIMIT")
    elseif BuildingType == const.GUILD_BUILDING_TYPE.VAULT then
        return Game.TableData.GetConstDataRow("GUILD_VAULT_LEVEL_TOPLIMIT")
    elseif BuildingType == const.GUILD_BUILDING_TYPE.SCHOOL then
        return Game.TableData.GetConstDataRow("GUILD_TRAINING_GROUND_LEVEL_TOPLIMIT")
    end
    return 0
end

-- 检查建筑升级条件
function GuildSystem:CheckUpgradeConditions(BuildingType)
	if not self:HasRight(const.GUILD_RIGHT.CONSTRUCTION) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NONE_GUILD_RIGHT)
		return false
	end
    local Level = self:GetBuildingLevel(BuildingType)
    local UpgradeInfo = Game.TableData.GetGuildUpgradeDataRow(Level + 1)
    if UpgradeInfo and self.model.GuildPrincipalInfo and self.model.GuildPrincipalInfo.funds then
        if BuildingType == const.GUILD_BUILDING_TYPE.MAIN then
            if self.model.GuildPrincipalInfo.funds < UpgradeInfo.MainUpgradeFundsRequire then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_FUNDS_COND_NOT_MEET)
                return false
            end
            return self:CheckSubBuildingLevel(UpgradeInfo.MainUpgradeCondition[2], 2)
        else
            if self.model.GuildPrincipalInfo.funds < UpgradeInfo.SubUpgradeFundsRequire[BuildingType] then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_FUNDS_COND_NOT_MEET)
                return false
            end
            return self:CheckMainBuildingLevel(UpgradeInfo.SubUpgradeCondition[BuildingType])
        end
    end
    return false
end

function GuildSystem:CheckSubBuildingLevel(Target, Count)
    local Current = 0
    for _, V in pairs(self.model.GuildBuildings) do
        if V.lv >= Target then
            Current = Current + 1
        end
    end
    if Current < Count then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_LEVEL_UP_CONDITION, {{Target}})
        return false
    end
    return true
end

function GuildSystem:CheckMainBuildingLevel(Target)
    local Level =  self:GetBuildingLevel(const.GUILD_BUILDING_TYPE.MAIN)
    if Level < Target then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_BUILDING_LEVEL_NOT_ENOUGH)
        return false
    end
    return true
end


-- 获取当前公会权限列表
function GuildSystem:ProRoleRightToData(RoleID)
    local RoleInfo = Game.TableData.GetGuildRightDataRow(RoleID)
    if RoleInfo then
        return RoleInfo
    end
    Log.DebugWarning("Unable to find info of the role")
    return nil
end

-- 获取当前公会修炼技�?
function GuildSystem:ProExerciseTypeToData()
    local ExerciseInfo = Game.TableData.GetGuildExerciseTypeDataTable()
    if ExerciseInfo then
        return ExerciseInfo
    end
    Log.DebugWarning("Unable to find info of the guild exercise")
    return nil
end

-- 获取当前公会修炼技能属性
function GuildSystem:ProExerciseInfoToData(Index, Level)
    local SkillID = Game.TableData.Get_GuildExercisePropMapData()[Index][Level]
    if SkillID then
        local ExerciseInfo = Game.TableData.GetGuildExercisePropDataTable()[SkillID]
        if ExerciseInfo then
            return ExerciseInfo
        end
    end
    Log.DebugWarning("Unable to find info of the guild exercise")
    return nil
end

-- 获取修炼表类型
function GuildSystem:GetGuildExerciseCategory()
    local TypeData = self:ProExerciseTypeToData()
    local Count = 0
    local PrevType = 0
    for _, V in pairs(TypeData) do
        if V.Type ~= PrevType then
            Count = Count + 1
            PrevType = V.Type
        end
    end
    return Count
end

-- 获取修炼技能等级
function GuildSystem:GetExerciseLevel(SkillInfo, SkillType)
    local Skill = (SkillInfo or {})[SkillType]
    return Skill or 0
end

-- 邀请玩家加入公会
function GuildSystem:InviteToGuild(eid, playerName)
    if lume.count(self.model.guildRecommendInviteIDs) >= Game.TableData.GetConstDataRow("CLUB_MEMBER_SEARCH_LIMIT_NUM") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_INVITE_LIMIT)
        return
    end
    if eid then
        if playerName then
            self.model.invitePlayersCache[eid] = playerName
        end
        Game.ChatSystem:SendChatMessage(
            eid, "", Enum.ECHAT_MESSAGE_TYPE.GUILD_JOIN,
            Enum.ECHAT_FUNCTION_TYPE.GUILD_JOIN, nil, false
        )
        self.model.guildRecommendInviteIDs[eid] = _G._now(1)
    end
end

-- 领工资
function GuildSystem:HandleWageGet()
    if self.model.GuildWelfareWage.bWageReady then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_WAGE_RECEIVE)
    return
    end
    if not self.model.GuildWelfareWage.wage or self.model.GuildWelfareWage.wage <= 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_WAGE_COUNT_ZERO)
        return
    end
    self.sender:getGuildWage()
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_WAGE_RECEIVE_SUCCESS)
end

-- 领取个人目标
function GuildSystem:SubmitPersonalTask(taskID)
    if Game and Game.me then
        Game.me:ReqTaskManualSubmit(taskID)
    end
end

--region 红点
function GuildSystem:UpdateApplyRedPointInfo()
    Game.RedPointSystem:AddListener("GuildInApply", self, self.CanShowApplyRedPoint)
    Game.RedPointSystem:AddListener("GuildOutResponse", self, self.CheckResponseRedPoint)
    Game.RedPointSystem:InvokeNode("GuildInApply")
end

-- 监听切换角色
function GuildSystem:OnInitGuildApplyNum()
	self:GetGuildApplies()
	self:UpdateApplyRedPointInfo()
end

-- 获取申请列表回调
function GuildSystem:InitGuildApplyNum(guildApplys)
	if self.model.hasInitGuildApplyNumFlag then
		return
	end
	self.model.hasInitGuildApplyNumFlag = true

	local guildApplyNum =#guildApplys
	if guildApplyNum == 0 then
		return
	end
	local newApplyNum = 0
	local guildInMemberApplyOpenTime = Game.GameClientData:GetPlayerValue("GuildInMemberApplyOpenTime")
	if guildInMemberApplyOpenTime then
		for _, apply in ipairs(guildApplys) do
			if apply.applyTime > guildInMemberApplyOpenTime then
				newApplyNum = newApplyNum + 1
			end
		end
	else
		newApplyNum = guildApplyNum
	end
	self.model.guildApplyNum = newApplyNum

end

function GuildSystem:CanShowApplyRedPoint()
	if not self:HasRight(const.GUILD_RIGHT.MEMBER) then
		return false
	end
    return self.model.guildApplyNum > 0
end

function GuildSystem:CheckResponseRedPoint()
    return #self.model.preGuildListInfo > 0 and not self:HasJoinGuild()
end
--endregion


--region 网络回调函数

function GuildSystem:HandleRequestRet(RetName,...)
    if self[RetName] then
        xpcall(self[RetName],_G.CallBackError,self,...)
    end
end

function GuildSystem:onCreateGuildSuccess()
    Game.GlobalEventSystem:Publish(EEventTypesV2.CREATE_GUILD_SUCCESS, next(self.model.preGuildInfos))
end

-- 公会数据标脏 如果打开了界面则需要重新拉取
function GuildSystem:OnMsgGuildDirty()
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.GuildInsidePanel) then
        self.sender:getGuildInfo()
    end
end

function GuildSystem:onGetGuildInfo(GuildPrincipalInfo)
    local member = GuildPrincipalInfo.member
    Game.me.guildShopLv = GuildUtils.getGuildBuildingLv(GuildPrincipalInfo.buildings, const.GUILD_BUILDING_TYPE.SHOP)
    local guildPlayer = {
        status = Game.me.guildStatus,
        roles = member.roles,
        groupID = member.groupID,
        guildBabyName = GuildPrincipalInfo.guildBabyName,
        signature = self.model.SignatureInfo.signature,
        lastSignInTime = self.model.SignatureInfo.lastSignInTime,
        isVoiceSignature = self.model.SignatureInfo.isVoiceSignature,
        enterTime = member.enterTime,
        lastWeekWage = member.lastWeekWage,
        wageFlag = member.wageFlag,
        dayMailNum = member.dayMailNum,
        guildShopLv = Game.me.guildShopLv,
    }
    self:onGuildPlayerInfo(guildPlayer)
    self.model.GuildPrincipalInfo = GuildPrincipalInfo
    self.model.GuildPrincipalInfo.leaderSchool = GuildPrincipalInfo.leaderSchool // 10
    self.model.GuildPrincipalInfo.leaderSex = GuildPrincipalInfo.leaderSchool % 10
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_INFO, GuildPrincipalInfo)
end

function GuildSystem:onGuildPlayerInfo(guildPlayer)
    local sendRoleChangeMsg = false
    local oldRole = self.model.guildPlayer.roles and self.model.guildPlayer.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] or nil
    local oldStatus = self.model.guildPlayer.status
    self.model.guildPlayer = guildPlayer
    local currentRole = guildPlayer.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE]

    if oldRole ~= currentRole then
        for _, member in ipairs(self.model.guildMembers) do
            if member.id == Game.me.eid then
                member.roles = guildPlayer.roles
                break
            end
        end

        if currentRole == const.GUILD_ROLE.PRESIDENT then
            -- 被转让成为了会长
            -- Game.EventSystem:Publish(_G.EEventTypes.GUILD_BE_TRANSFERRED, currentRole)
        elseif currentRole == const.GUILD_ROLE.MEMBER and oldRole == const.GUILD_ROLE.PRESIDENT then
            -- 自己的会长职位被领袖撤销
            -- Game.EventSystem:Publish(_G.EEventTypes.GUILD_BE_TRANSFERRED, currentRole)
        else
            -- 职位变更，非转让
            -- Game.EventSystem:Publish(_G.EEventTypes.GUILD_CHANGE_SINGLE_ROLE_SUCCESS, currentRole)
        end
        sendRoleChangeMsg = true
    end
    if oldRole ~= currentRole then
        self.sender:getCustomRoleRights(currentRole)
        local honorRole = guildPlayer.roles[const.GUILD_ROLE_TYPE.HONOR_ROLE]
        if honorRole and honorRole ~= 0 then
            self.sender:getCustomRoleRights(honorRole)
        end
        local groupRole = guildPlayer.roles[const.GUILD_ROLE_TYPE.GROUP_ROLE]
        if groupRole and groupRole ~= 0 then
            self.sender:getCustomRoleRights(groupRole)
        end
    end
    
    self:RefreshGuildSign()
    if oldStatus == const.GUILD_STATUS.INIT and guildPlayer.status == const.GUILD_STATUS.COMMON then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ENTER_GUILD)
        return
    end
    self:initGuildWelfare()
end

function GuildSystem:onGuildSignIn(signature, lastSignInTime, isVoiceSignature)
    if self.model.SignatureInfo.lastSignInTime ~= lastSignInTime then
        self.model.SignatureInfo = { signature = signature, lastSignInTime = lastSignInTime, isVoiceSignature = isVoiceSignature }
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SIGNIN_SUCCESS)
        if isVoiceSignature then
            local infoList = string.split(signature,";")
            if infoList then
                Game.ChatSystem:SendChatMessage(
                    Enum.EChatChannelData.GUILD, infoList[1], Enum.EChatMessageType.VOICE, nil,
                    { voiceInfo = { voiceKey = infoList[2], voiceDuration = tonumber(infoList[3]) } },
                    true, false
                )
            end
        else
            Game.ChatSystem:SendChatMessage(
                Enum.EChatChannelData.GUILD, signature, Enum.EChatMessageType.TEXT,
                nil, nil, true, false
            )
        end
        Game.GlobalEventSystem:Publish(EEventTypesV2.REFRESH_GUILD_SIGN_IN)
    end
end

function GuildSystem:onGuildSignature(signature, lastSignInTime, isVoiceSignature)
    self.model.SignatureInfo = { signature = signature, lastSignInTime = lastSignInTime, isVoiceSignature = isVoiceSignature }
    Game.GlobalEventSystem:Publish(EEventTypesV2.GUILD_SIGNATURE_CHANGE)
end

-- 查询邀请的响应创建的玩家列表的响应
function GuildSystem:RetGetGuildResponseInviteIDs(Ids)
    self.model.guildResponseInviteIDs = Ids
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_RESPONSE_INVITE_IDS_CHANGED)
end

-- 查询邀请加入公会的玩家列表的响应
function GuildSystem:RetGetGuildRecommendInviteIDs(Ids)
    self.model.guildRecommendInviteIDs = Ids
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_RECOMMEND_INVITE_IDS_CHANGED)
end

-- 响应公会成功
function GuildSystem:onResponseGuildSuccess(name, create)
    if create then
        if Game.NewUIManager:CheckPanelIsOpen("GuildOutPanel") then
            Game.NewUIManager:ClosePanel("GuildOutPanel")
            Game.NewUIManager:OpenPanel("GuildInside_Panel")
        end
    else
        Game.MessageBoxSystem:AddPopupByConfig(
            Enum.EDialogPopUpData.GUILD_RESPOSE, nil, nil, { name or "" }
        )
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.RESPONSE_GUILD_SUCCESS, name, create)
end

-- 获得响应公会的详细信息
function GuildSystem:onGetGuildDetail(
    guildId, guildShortId, guildDeclaration, guildMembers,
    badgeFrameId, badgeIndex, guildTimeLiveInfo, guildProfessionInfo
)

    for _, memberData in ipairs(guildMembers) do
        memberData.sex = memberData.school % 10
        memberData.school = memberData.school // 10
    end
    
    self.model.guildDetailCache[guildId] = {
        cachedTime = _G._now() / 1000,
        guildId = guildId,
        guildShortId = guildShortId,
        guildDeclaration = guildDeclaration,
        guildMembers = guildMembers,
        badgeFrameId = badgeFrameId,
        badgeIndex = badgeIndex,
        guildTimeLiveInfo = guildTimeLiveInfo,
        guildProfessionInfo = guildProfessionInfo
    }
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_DETAIL, guildId)
end

function GuildSystem:onGetGuildMembers(Version, Members, GuildRoleSlotInfo, groupNameData)
    for _, memberData in pairs(Members) do
        memberData.sex = memberData.school % 10
        memberData.school = memberData.school // 10
    end
    self.model.guildMembers = Members
    self.model.guildRoleSlotInfo = GuildRoleSlotInfo
    self.model.guildMemberVersion = Version
    self.model.groupNameData = groupNameData
    --region 待废弃
    self.model.GuildRoleMemberMap = {}
    --endregion

    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_MEMBERS, Members, GuildRoleSlotInfo, groupNameData)
end

function GuildSystem:onGetPreGuildInfo(preGuildInfo)
    for _, data in ipairs(preGuildInfo.members) do
        data.sex = data.school % 10
        data.school = data.school // 10
    end
    self.model.preGuildInfos = preGuildInfo
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_PRE_GUILD_INFO, preGuildInfo)
end

function GuildSystem:onSetGuildGroupNameSuccess(nameData)
    self.model.groupNameData = nameData
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SET_GUILD_GROUP_NAME_SUCCESS, nameData)
end

function GuildSystem:onEditDeclarationSuccess(Announce)
    self.model.PreGuildInfos.declaration = Announce
    self.model.GuildPrincipalInfo.declaration = Announce
    if self.model.guildDetailCache[Game.me.guildId] then
        self.model.guildDetailCache[Game.me.guildId].guildDeclaration = Announce
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHANGE_GUILD_ANNOUNCE_SUCCESS, Announce)
end

function GuildSystem:onChangeGuildNameSuccess(Name)
    self.model.PreGuildInfos.name = Name
    self.model.GuildPrincipalInfo.name = Name
    Game.GlobalEventSystem:Publish(EEventTypesV2.GUILD_CHANGE_NAME_SUCCESS, Name)
end

function GuildSystem:onSetGuildBadgeFrameSuccess(BadgeFrameId,setBadgeFrameCount)
    self.model.GuildPrincipalInfo.setBadgeFrameCount = setBadgeFrameCount
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_BADGE_CHANGE)
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_GUILD_BADGE_FRAME_CHANGE_NOTIFY)
end

function GuildSystem:setGuildBanRookieBid(bBanRookie)
    self.sender:setGuildBanRookieBid(bBanRookie)
    self.model.GuildPrincipalInfo.isBanRookieBid =  bBanRookie
end

function GuildSystem:setAutoFormatApprentices(bAutoFormatApp)
    self.sender:setAutoFormatApprentices(bAutoFormatApp)
    self.model.GuildPrincipalInfo.autoFormatFlag = bAutoFormatApp
end

function GuildSystem:onKickGuildMemberSuccess(eid)
    local memberData
    for index, member in ipairs(self.model.guildMembers) do
        if member.id == eid then
            memberData = table.remove(self.model.guildMembers, index)
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CLUB_EJECT_MEMBER, {{memberData.rolename}})
            break
        end
    end
    if memberData then
        for roleId, roleSlots in pairs(self.model.guildRoleSlotInfo) do
            for slotId, curEid in pairs(roleSlots) do
                if eid == curEid then
                    roleSlots[slotId] = nil
                    break
                end
            end
        end
    end
    self:RefreshGuildMemberNumCache()
    Game.GlobalEventSystem:Publish(EEventTypesV2.GUILD_MEMBER_KICKED, eid)
end

function GuildSystem:onAgreeGuildApplySuccess(guildMember)
    guildMember.sex = guildMember.school % 10
    guildMember.school = guildMember.school // 10
    table.insert(self.model.guildMembers, guildMember)
    for index, member in ipairs(self.model.guildApplyCache) do
        if member.id == guildMember.id then
            table.remove(self.model.guildApplyCache, index)
            break
        end
    end
    self:RefreshGuildMemberNumCache()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ACCEPT_GUILD_APPLY_SUCCESS, guildMember)
end

function GuildSystem:RefreshGuildMemberNumCache()
    local apprenticeNum = 0
    for index, member in ipairs(self.model.guildMembers) do
        if member.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] == const.GUILD_ROLE.APPRENTICE then
            apprenticeNum = apprenticeNum + 1
        end
    end
    self.model.GuildPrincipalInfo.memberNum = #self.model.guildMembers - apprenticeNum
    self.model.GuildPrincipalInfo.apprenticeNum = apprenticeNum
end

function GuildSystem:onRefuseGuildApplySuccess(eid)
    for index, member in ipairs(self.model.guildApplyCache) do
        if member.id == eid then
            table.remove(self.model.guildApplyCache, index)
            break
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.REFUSE_GUILD_APPLY_SUCCESS, eid)
end

function GuildSystem:onDeleteGuildApplysSuccess()
    self.model.guildApplyCache = {}
    Game.GlobalEventSystem:Publish(EEventTypesV2.DELETE_APPLY_LIST_SUCCESS)
end

function GuildSystem:onGetCustomRoleRights(Role, RightsMap)
    self.model.CustomRoleRights[Role] = RightsMap
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_RIGHTS, Role, RightsMap)
end

function GuildSystem:onSetGuildRoleSuccess(eid, roleType, roleId)
    for _, member in ipairs(self.model.guildMembers) do
        if member.id == eid then
            member.roles[roleType] = roleId
            break
        end
    end
    -- Game.EventSystem:Publish(_G.EEventTypes.GUILD_CHANGE_SINGLE_ROLE_SUCCESS, eid, roleId)
end

function GuildSystem:onDemiseSuccess(selfRole, eid, roleId)
    -- 把会长转给别人，或者会长退出公会会
    local roleType = Game.TableData.GetGuildRightDataRow(roleId).Type
    self.model.guildPlayer.roles[roleType] = roleId
    for _, member in ipairs(self.model.guildMembers) do
        if member.id == Game.me.eid then
            member.roles[roleType] = selfRole
        end
        if member.id == eid then
            member.roles[roleType] = roleId
        end
    end
end

function GuildSystem:onGetGuildEvents(Events)
    for _, event in pairs(Events) do
		event.args = _script.cmsgpack.unpack(event.args)
	end
    self.model.GuildEvents = Events
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_EVENT)
end

function GuildSystem:onGetGuildBuildings(buildings)
    self.model.GuildBuildings = buildings
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_BUILDINGS, buildings)
end

function GuildSystem:onUpgradeBuildingSuccess(Type, EndTime)
    self.model.GuildBuildings[Type] = self.model.GuildBuildings[Type] or {}
    self.model.GuildBuildings[Type].lv = self:GetBuildingLevel(Type)
    self.model.GuildBuildings[Type].endTime = EndTime
    Game.GlobalEventSystem:Publish(EEventTypesV2.UPGRADE_GUILD_BUILDING_SUCCESS)
end


function GuildSystem:onGetGuildWelfare(Wage, bIsReady)
    self.model.GuildWelfareWage = self.model.GuildWelfareWage or {}
    self.model.GuildWelfareWage.wage = Wage
    self.model.GuildWelfareWage.bWageReady = bIsReady
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_GUILD_WAGE)
end


function GuildSystem:onGetExerciseGuildBuildingLv(Level)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_EXERCISE_BUILDING_LV, Level)
end


function GuildSystem:onGetGuildExerciseInfo(Data)
    self.model.GuildSkills = Data
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_EXERCISE_INFO)
end


function GuildSystem:onGuildExerciseSuccess(SkillID, SkillLv)
    Game.GlobalEventSystem:Publish(EEventTypesV2.GUILD_EXERCISE_SUCCESS)
end


function GuildSystem:onOtherInviteEnterGuild(GuildID, EntityID, RoleName, GuildName)
    self.model.GuildInviteInfo = {GuildID = GuildID, EntityID = EntityID, RoleName = RoleName, GuildName = GuildName}
end


function GuildSystem:onInviteGuildSuccess(EntityID)
    if self.model.invitePlayersCache[EntityID] then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_INVITE_SUCCESS, {{self.model.invitePlayersCache[EntityID]}})
    end
end

function GuildSystem:onNewApply(num)
    self.model.guildApplyNum = num
    Game.RedPointSystem:ClearNode("GuildInApply")
end

function GuildSystem:onGetGuildVoyageTask(task)
    self.model.GuildTargetInfo.Guild = task[1]
end

function GuildSystem:onGetPersonalVoyageTask(task)
    self.model.GuildTargetInfo.Personal = task[1]
end

function GuildSystem:onGetGuildVoyageMedalInfo(medal, rank, treasure, topThree)
    self.model.GuildTargetInfo.Medal = {medal, rank, treasure, topThree}
end

function GuildSystem:onCompletePersonalVoyageTask(groupId, taskId)
end

function GuildSystem:onGetGuildVoyageBoxReward(slotId)
    self.model.GuildTargetInfo.Medal[3][slotId] = false
end

function GuildSystem:onOtherRefuseGuildInvite(entityID, playerName)
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OTHER_REFUSE_GUILD_INVITE, {{playerName}})
end

function GuildSystem:onUpdateSendGuildMailCount(mailCount, reason)
    if self.model.guildPlayer then
        self.model.guildPlayer.dayMailNum = mailCount
    end
end

function GuildSystem:onSetGuildRolesSuccess(version, members, guildRoleSlotInfo)
    for _, memberData in pairs(members) do
        memberData.sex = memberData.school % 10
        memberData.school = memberData.school // 10
    end
    self.model.guildMemberVersion = version
    self.model.guildMembers = members
    self.model.guildRoleSlotInfo = guildRoleSlotInfo
    self:RefreshGuildMemberNumCache()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SET_GUILD_ROLES_SUCCESS, members, guildRoleSlotInfo)
end

function GuildSystem:onUpdateGuildPictureSuccess(resId)
    self.model.GuildPrincipalInfo = self.model.GuildPrincipalInfo or {}
    self.model.GuildPrincipalInfo.newPosterResID = resId
    self.model.GuildPrincipalInfo.posterStatus = const.CHAT_CUSTOM_IMG_STATUS.MACHINE
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UPDATE_GUILD_PICTURE_SUCCESS)
end

function GuildSystem:onGuildMemberUpdate(version, members, guildRoleSlotInfo, groupNameData)
    for _, memberData in pairs(members) do
        memberData.sex = memberData.school % 10
        memberData.school = memberData.school // 10
        if memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] == const.GUILD_ROLE.APPRENTICE then
        end
    end
    self.model.guildMemberVersion = version
    self.model.guildMembers = members
    self.model.guildRoleSlotInfo = guildRoleSlotInfo
    self.model.groupNameData = groupNameData
    self:RefreshGuildMemberNumCache()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_MEMBER_UPDATE, members, guildRoleSlotInfo)
end

function GuildSystem:onSetGuildTypeSuccess(type)
    if self.model.GuildPrincipalInfo then
        self.model.GuildPrincipalInfo.guildType = type
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SET_GUILD_TYPE_SUCCESS, type)
end

function GuildSystem:onLeaveGuildSuccess()
    Game.ChatSystem:ClearChatListByChannelType(Enum.EChatChannelData.GUILD)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_LEAVE_GUILD_SUCCESS)
end

-- 被逐出俱乐部
function GuildSystem:onBeKickedGuild(guildId, operationName, guildName)
    local leaderName = (operationName == "" and StringConst.Get("SOCIAL_SYSTEM") or operationName)
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.QUIT_GUILD_BY_OTHER, {{leaderName, guildName}})
    -- TODO 目前 Game.me.guildId清理跟onBeKickedGuild前后顺序不保证，临时加个定时器，后面6月迭代后删除
    self:StartTimer("onBeKickedGuild", function()
        Game.ChatSystem:ClearChatListByChannelType(Enum.EChatChannelData.GUILD)
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_LEAVE_GUILD_SUCCESS)
    end, 200, 1)
end

function GuildSystem:onChangeGuildBabyNameSuccess(name)
    self.model.GuildPrincipalInfo.guildBabyName = name
end

function GuildSystem:onSetGuildBadgeIndexSuccess(index)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SET_GUILD_BADGE_INDEX_SUCCESS, index)
end

function GuildSystem:SendWelcome()
    local textData = {}
    for _, v in ksbcpairs(Game.TableData.GetGuildChatWelcomeDataTable()) do
        table.insert(textData, v.WelcomeText)
    end
    Game.ChatSystem:SendChatMessage(
        Enum.EChatChannelData.GUILD, textData[math.random(#textData)], Enum.EChatMessageType.TEXT,
        nil, nil, true, false
    )
end

function GuildSystem:OnLoadingLevelComplete(level)
    if self.model.needOpenCamera and level.ID == Enum.ELevelMapData.LV_Guild_P then
        Game.PhotographSystem:ShowPhotographUI()
    end
    self.model.needOpenCamera = false
end

--- 响应的公会已经成立了的回调
function GuildSystem:onMsgResponseGuildCreated(id, guildName)
    Game.MessageBoxSystem:AddPopupByConfig(
        Enum.EDialogPopUpData.CLUB_APPLY_CONFIRM,
        function()
            self:JumpToApplySpecificGuild(guildName)
        end
    )
end
--endregion

--region 接口

function GuildSystem:JumpToApplySpecificGuild(guildName)
    if Game.NewUIManager:CheckPanelIsOpen("GuildOutPanel") then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SEL_GUILD_OUT_PAGE, "APPLY", guildName)
    else
        Game.NewUIManager:OpenPanel("GuildOutPanel", "APPLY", guildName)
    end
end

function GuildSystem:SeeGuildMainPage(guildId, guildName)
    -- TODO 接入拜访后修改
    self:JumpToApplySpecificGuild(guildName)
end

function GuildSystem:ShowGuildMain(params, bIsInputJump)
    local pageIndex
    if params and #params > 0 then
        pageIndex = params[1]
    end
    local bIsJoinGuild = self:HasJoinGuild()
    if bIsInputJump then
        if not bIsJoinGuild and Game.NewUIManager:CheckPanelIsOpen("GuildOutPanel") then
            Game.NewUIManager:ClosePanel("GuildOutPanel")
            return
        elseif bIsJoinGuild and Game.NewUIManager:CheckPanelIsOpen("GuildInside_Panel") then
            Game.NewUIManager:ClosePanel("GuildInside_Panel")
            return
        end
    end
    if bIsJoinGuild then
        -- 用的是索引
        Game.NewUIManager:OpenPanel("GuildInside_Panel", pageIndex)
    else
        -- 用的是pageId
        Game.NewUIManager:OpenPanel("GuildOutPanel", pageIndex)
    end
end

function GuildSystem:ShowGuildInMain(params)
    local pageIndex
    if #params > 0 then
        pageIndex = params[1]
    end
    if self:HasJoinGuild() then
        Game.NewUIManager:OpenPanel("GuildInside_Panel", pageIndex)
    else
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_HAVE_NOT_YET)
    end
end

function GuildSystem:ShowGuildSkill()
    if self:CheckGuildSkillCanOpen() then
        UI.ShowUI("P_GuildInsideSkill")
    end
end


function GuildSystem:HasSensitiveWords(sensitiveResult)
    for _, data in ipairs(sensitiveResult) do
        if #data.matchResults > 0 then
            return true
        end
    end
    return false
end

-- 判断该玩家是否是当前职位(具体到是否是第几组)
function GuildSystem:CheckMemberHasTargetRole(memberId, targetRoleID, groudId)
    if not memberId then return false end
    local memberInfo = self:FindMemberByEntityID(memberId)
    if not memberInfo then return false end
    for _, role in pairs(memberInfo.roles) do
        if role == targetRoleID then
            if role == const.GUILD_ROLE.GROUP_LEADER or role == const.GUILD_ROLE.GROUP_MEMBER then
                if memberInfo.groupID == groudId then
                    return true
                else
                    return false
                end
            else
                return true
            end
        end
    end
    return false
end

function GuildSystem:GetValidBadgeIndex(badgeIndex)
    if type(badgeIndex) ~= "number" or badgeIndex <= 0 then
        return 1
    end
    return badgeIndex
end

function GuildSystem:GetSelfGuildCommonRole()
    return self.model.guildPlayer.roles and self.model.guildPlayer.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] or const.GUILD_ROLE.APPRENTICE 
end

-- 进入公会场景
function GuildSystem:EnterGuild()
    local localSpace = Game.NetworkManager.GetLocalSpace()
    if localSpace then
        if localSpace.WorldType == worldConst.WORLD_TYPE.GUILD_STATION then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_STATION_INSIDE_ALREADY)
            return
        end
    end
    self.sender:enterGuildStation()
end

function GuildSystem:GetGroupNum()
    local grdd = Game.TableData.GetGuildRightDataRow(const.GUILD_ROLE.GROUP_LEADER)
    return grdd and grdd.MaxNum or 0
end

function GuildSystem:GetGroupName(groupIndex)
    groupIndex = groupIndex or 1
    if groupIndex == 0 then
        return "未分组"
    end
    return self.model.groupNameData[groupIndex] or StringConst.Get("GUILD_GROUP_NAME_" .. tostring(groupIndex))
end

function GuildSystem:ProcessGuildNumber(Num)
    if Num < 100000 then
        return string.format("%.0f",math.ceil(Num))
    elseif Num < 10000000 then
        return string.format("%.0f%s",Num/10000,StringConst.Get("TEN_THOUSAND"))
    elseif Num < 10 ^ 8 then
        return string.format("%.0f%s",Num/10000,StringConst.Get("TEN_THOUSAND"))
    else
        return string.format("%.0f%s",Num / 10^8, StringConst.Get("AHUNDREDMILLION"))
    end
end

-- 获取公会成员在线状态
function GuildSystem:GetOffLineTImeDesc(OfflineTime)
    OfflineTime = OfflineTime > 0 and ((_G._now() / 1000) - OfflineTime) or 0
    if OfflineTime <= 0 then
        return StringConst.Get("SOCIAL_FRIEND_ONLINE")
    elseif OfflineTime <= 60 * 60 then
        return StringConst.Get("GUILD_STATUS_MIN_AGO", tostring(math.floor(OfflineTime / 60)))
    elseif OfflineTime <= 60 * 60 * 24 then
        return StringConst.Get("GUILD_STATUS_HOUR_AGO", tostring(math.floor(OfflineTime / 3600)))
    elseif OfflineTime <= 60 * 60 * 24 * 7 then
        return StringConst.Get("GUILD_STATUS_DAY_AGO", tostring(math.floor(OfflineTime / (60 * 60 * 24))))
    elseif OfflineTime <= 60 * 60 * 24 * 31 then
        return StringConst.Get("SEVERAL_WEEKS_AGO", tostring(math.floor(OfflineTime / (60 * 60 * 24 * 7))))
    elseif OfflineTime <= 60 * 60 * 24 * 365 then
        return StringConst.Get("GUILD_STATUS_MONTH_AGO", tostring(math.floor(OfflineTime / (60 * 60 * 24 * 31))))
    else
        return ""
    end
end

-- 获取玩家职务显示信息
function GuildSystem:GetOccupationText(memberData, showCount)
    local occupationText = ""
    local showOccupations = {}
    showCount = showCount or 1
    if memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] and memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] ~= 0 then
        table.insert(showOccupations, {
            name = self:RoleIDToRoleName(memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE]),
            sortIndex = Game.TableData.GetGuildRightDataRow(memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE]).Rank
        })
    end
    if memberData.roles[const.GUILD_ROLE_TYPE.HONOR_ROLE] and memberData.roles[const.GUILD_ROLE_TYPE.HONOR_ROLE] ~= 0 then
        table.insert(showOccupations, {
            name = self:RoleIDToRoleName(memberData.roles[const.GUILD_ROLE_TYPE.HONOR_ROLE]),
            sortIndex = Game.TableData.GetGuildRightDataRow(memberData.roles[const.GUILD_ROLE_TYPE.HONOR_ROLE]).Rank
        })
    end
    if memberData.roles[const.GUILD_ROLE_TYPE.GROUP_ROLE] and memberData.roles[const.GUILD_ROLE_TYPE.GROUP_ROLE] ~= 0 then
        table.insert(showOccupations, {
            name = self:RoleIDToRoleName(memberData.roles[const.GUILD_ROLE_TYPE.GROUP_ROLE], memberData.groupID),
            sortIndex = Game.TableData.GetGuildRightDataRow(memberData.roles[const.GUILD_ROLE_TYPE.GROUP_ROLE]).Rank
        })
    end
    -- 按照Rank判断显示优先级
    table.sort(showOccupations, function(a, b)
        return a.sortIndex > b.sortIndex
    end)
    -- 只显示前showCount个职务
    for i = 1, showCount do
        if showOccupations[i] then
            local lastText = (i >= #showOccupations or i >= showCount) and "" or "\n"
            occupationText = string.format("%s%s%s", occupationText, showOccupations[i].name, lastText)
        end
    end
    return occupationText
end

-- 获取在线正式/候补会员在线数量
function GuildSystem:GetOnlineMemberCount()
    local memberOnlineNum = 0
    local apprenticeOnlineNum = 0
    if self.model.guildMembers then
        for _, memberData in pairs(self.model.guildMembers) do
            if memberData.offlineTime == 0 then
                if memberData.roles[const.GUILD_ROLE_TYPE.COMMON_ROLE] == const.GUILD_ROLE.APPRENTICE then
                    apprenticeOnlineNum = apprenticeOnlineNum + 1
                else
                    memberOnlineNum = memberOnlineNum + 1
                end
            end
        end
    end
    return memberOnlineNum, apprenticeOnlineNum
end

-- 获取头顶GuildIcon显示的字
function GuildSystem:GetHeadInfoGuildIconText(name, index)
	return utf8.sub(name, index, index + 1)
end

--endregion

return GuildSystem
