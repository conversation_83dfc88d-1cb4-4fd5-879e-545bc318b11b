local ESlateVisibility = import("ESlateVisibility")

---@class P_GuildInsideSkill : UIController
--- @field public View WBP_GuildSkillView
local P_GuildInsideSkill = DefineClass("P_GuildInsideSkill", UIController)
local stringConst = require "Data.Config.StringConst.StringConst"

--ToDo 配表, 本地化
P_GuildInsideSkill.SkillTypeName = {
    [1] = { ['name'] = "GUILD_SKILL_TYPE_ONE" },
    [2] = { ['name'] = "GUILD_SKILL_TYPE_TWO" },
    [3] = { ['name'] = "GUILD_SKILL_TYPE_THREE" },
}

P_GuildInsideSkill.eventBindMap = {
    [_G.EEventTypes.RECEIVE_EXERCISE_INFO] = "RefreshPage",
    [_G.EEventTypes.GUILD_EXERCISE_SUCCESS] = "ReqGetGuildSkillInfo",
    [_G.EEventTypes.UPGRADE_GUILD_BUILDING_SUCCESS] = "ReqGetExerciseGuildBuildingLv",
    [_G.EEventTypes.RECEIVE_EXERCISE_BUILDING_LV] = "ProcessBuildingLevel",
}

function P_GuildInsideSkill:OnCreate()
    self.skillType = { BASIC = 1, ADVANCED = 2, HIGH = 3 }  -- 技能类型

    self.buildingLevel = 0          -- 建筑等级
    self.currentSkillWidget = nil   -- 当前技能widget
    self.currentSkillID = nil       -- 当前技能id

    self.skillProp = {}     -- 技能道具
    self.skillVal = {}      -- 技能价值
    self.currentSkillDetail = {}    -- 当前技能详情
    self.nextLevelSkillDetail = {}  -- 下一级技能详情
    self.guildSkills = {}           -- 当前技能
    self.guildSkillsData = {}       -- 技能表数据
    self.lastSelected = {funcName = "OnClickBasicSkill", index = 1}         -- 上一次选中的技能

    -- 基础技能列表
    self.basicSkillView = BaseList.CreateList(self, BaseList.Kind.GroupView, 
        self.View.WBP_GuildSkillLeft_1.WarpBox_GuildSkills, nil, 'BasicSkillView'
    )
    self.basicSkillView:AddUIListener(EUIEventTypes.CLICK, "Button", "OnClickBasicSkill")
    -- 进阶技能列表
    self.advancedSkillView = BaseList.CreateList(self, BaseList.Kind.GroupView, 
        self.View.WBP_GuildSkillLeft_2.WarpBox_GuildSkills, nil, 'AdvancedSkillView'
    )
    self.advancedSkillView:AddUIListener(EUIEventTypes.CLICK, "Button", "OnClickAdvancedSkill")
    -- 高级技能列表
    self.highSkillView = BaseList.CreateList(self, BaseList.Kind.GroupView, 
        self.View.WBP_GuildSkillLeft_3.WarpBox_GuildSkills, nil, 'HighSkillView'
    )
    self.highSkillView:AddUIListener(EUIEventTypes.CLICK, "Button", "OnClickHighSkill")
    -- 技能升级列表
    self.guildSkillUpgradeGroup = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.WBP_GuildSkillRight.VB_UpgradeResult)


    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComPanelNoTab.WBP_ComBtnBack.Btn_Back, self.OnclickBack)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComPanelNoTab.WBP_ComBtnBack.Btn_Info, self.OnClickInfoButton)
    self.View.WBP_ComPanelNoTab.WBP_ComBtnBack.Btn_Info:SetVisibility(ESlateVisibility.Visible)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_GuildSkillRight.WBP_ComBtnLightM.Btn_Com, self.OnClickExercise)

    self.View.WBP_GuildSkillLeft_1.WBP_GuildSkillTitle.SkillTypeName:SetText(
        stringConst.Get(P_GuildInsideSkill.SkillTypeName[self.skillType.BASIC].name)
    )
    self.View.WBP_GuildSkillLeft_2.WBP_GuildSkillTitle.SkillTypeName:SetText(
        stringConst.Get(P_GuildInsideSkill.SkillTypeName[self.skillType.ADVANCED].name)
    )
    self.View.WBP_GuildSkillLeft_3.WBP_GuildSkillTitle.SkillTypeName:SetText(
        stringConst.Get(P_GuildInsideSkill.SkillTypeName[self.skillType.HIGH].name)
    )
    self.View.WBP_GuildSkillRight.LevelUpDetail.C7TextBlock_0:SetText(stringConst.Get("GUILD_BUILD_PREVIEW"))
    self.View.WBP_GuildSkillRight.LevelUpCondition.C7TextBlock_0:SetText(stringConst.Get("GUILD_BUILD_CONDITION"))
end

function P_GuildInsideSkill:OnRefresh(params)
    self.View.WBP_ComPanelNoTab.WBP_ComBtnBack.Text_Back:SetText(stringConst.Get("GUILD_SKILL_NAME"))
    self:RefreshPage()
    self:ReqGetGuildSkillInfo()
    self:ReqGetExerciseGuildBuildingLv()
    self.View.WBP_GuildSkillRight.WBP_SkillItem.Text_AboutSkill:SetVisibility(ESlateVisibility.Hidden)
end

--region OnRefresh

function P_GuildInsideSkill:OnRefresh_BasicSkillView(widget, index, bSelected)
    self:OnRefreshSkill(self.skillType.BASIC, widget, index, bSelected)
end

function P_GuildInsideSkill:OnRefresh_AdvancedSkillView(widget, index, bSelected)
    self:OnRefreshSkill(self.skillType.ADVANCED, widget, index, bSelected)
end

function P_GuildInsideSkill:OnRefresh_HighSkillView(widget, index, bSelected)
    self:OnRefreshSkill(self.skillType.HIGH, widget, index, bSelected)
end

function P_GuildInsideSkill:OnRefreshSkill(skillType, widget, index, bSelected)
    if self.guildSkillsData[skillType] and self.guildSkillsData[skillType][index] then
        local skillID = self.guildSkillsData[skillType][index]

        widget.Overlay_Fitout:SetVisibility(ESlateVisibility.Hidden)
        widget.Overlay_Selected:SetVisibility(ESlateVisibility.Hidden)
        widget.Text_AboutSkill:SetVisibility(ESlateVisibility.Hidden)

        local currentSkill = Game.TableData.GetGuildExerciseTypeDataTable()[skillID]
        self:SetSkillIcon(widget, currentSkill.Icon)

        widget.TextBlock_SkillName:SetText(currentSkill.Prop)
        widget.TextBlock_level:SetText(tostring(Game.GuildSystem:GetExerciseLevel(self.guildSkills, skillID)))

        widget:Event_UI_Style(true)
    end
end

function P_GuildInsideSkill:OnRefresh_VB_UpgradeResult(widget, index, bSelected, params)
    local PropId = self.skillProp[index]
    local PropName = Game.TableData.GetFightPropModeDataRow(Enum.EFightPropModeData[PropId]).PropName
    widget.Text_Attr:SetText(PropName)

    if P_GuildInsideSkill.PercentageShowTable[PropId] then
        if self.skillVal[1] > 0 then
            local _, b = math.modf(self.skillVal[1] * 100)
            if b > 0 then
                widget.Text_Val:SetText(string.format("%0.2f%s", self.skillVal[1] * 100, "%"))
            else
                widget.Text_Val:SetText(string.format("%0.0f%s", self.skillVal[1] * 100, "%"))
            end
        else
            widget.Text_Val:SetText(string.format("%0.0f%s", self.skillVal[1], "%"))
        end
    else
        widget.Text_Val:SetText(tostring(self.skillVal[1]))
    end

    if self.skillVal[2] then
        widget.Better:SetVisibility(ESlateVisibility.Visible)
        widget.Text_NewVal:SetVisibility(ESlateVisibility.Visible)
        widget.Arrow:SetVisibility(ESlateVisibility.Visible)
        if P_GuildInsideSkill.PercentageShowTable[PropId] then
            local _, b = math.modf(self.skillVal[2] * 100)
            if b > 0 then
                widget.Text_NewVal:SetText(string.format("%0.2f%s", self.skillVal[2] * 100, "%"))
            else
                widget.Text_NewVal:SetText(string.format("%0.0f%s", self.skillVal[2] * 100, "%"))
            end
        else
            widget.Text_NewVal:SetText(tostring(self.skillVal[2]))
        end
    else
        widget.Better:SetVisibility(ESlateVisibility.Hidden)
        widget.Text_NewVal:SetVisibility(ESlateVisibility.Collapsed)
        widget.Arrow:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--endregion

---以下属性是要以百分比显示的
P_GuildInsideSkill.PercentageShowTable = {
    pCritDef_N = 1,
    mCritDef_N = 1,
    pCritHurt_N = 1,
    mCritHurt_N = 1,
}

-- 刷新全部技能以及技能分类标题
function P_GuildInsideSkill:RefreshPage()
    self.guildSkills = Game.GuildSystem.model.GuildSkills
    self.guildSkillsData = {}

    for k, v in ksbcpairs(Game.TableData.GetGuildExerciseTypeDataTable()) do
        self.guildSkillsData[v.Type] = self.guildSkillsData[v.Type] or {}
        table.insert(self.guildSkillsData[v.Type], k)
    end
    for k, v in pairs(self.guildSkillsData) do
        table.sort(
            v,
            function(a, b)
                return Game.TableData.GetGuildExerciseTypeDataRow(a).Location <
                    Game.TableData.GetGuildExerciseTypeDataRow(b).Location
            end
        )
    end
    self:RefreshGuildSkill()
end

function P_GuildInsideSkill:OnExerciseSuccess()
    self:StartTimer("OnExerciseSuccess", function()
        self:ReqGetGuildSkillInfo()
    end, 600, 1, nil ,true) 
end

function P_GuildInsideSkill:ReqGetGuildSkillInfo()
    Game.GuildSystem.sender:getGuildExerciseInfo()
end

function P_GuildInsideSkill:ReqGetExerciseGuildBuildingLv()
    Game.GuildSystem.sender:getExerciseGuildBuildingLv()
end

function P_GuildInsideSkill:ReqUpgradeGuildSkill(skillID)
    Game.GuildSystem.sender:doGuildExercise(skillID)
end

function P_GuildInsideSkill:ProcessBuildingLevel(Level)
    self.buildingLevel = Level
    self:RefreshPage()
end

-- 设置技能
function P_GuildInsideSkill:RefreshGuildSkill()
    self.basicSkillView:SetData(#self.guildSkillsData[self.skillType.BASIC])
    self.advancedSkillView:SetData(#self.guildSkillsData[self.skillType.ADVANCED])
    self.highSkillView:SetData(#self.guildSkillsData[self.skillType.HIGH])
    -- self:OnClickBasicSkill(1)
    self[self.lastSelected.funcName](self, self.lastSelected.index)
end

-- 设置右侧技能细节面板
function P_GuildInsideSkill:SetGuildSkillDetailPanel(skillID)
    local currentSkill = Game.TableData.GetGuildExerciseTypeDataTable()[skillID]
    local level = Game.GuildSystem:GetExerciseLevel(self.guildSkills, skillID)
    self.nextLevelSkillDetail = Game.GuildSystem:ProExerciseInfoToData(skillID, level + 1)
    self.currentSkillDetail = Game.GuildSystem:ProExerciseInfoToData(skillID, level)

    self.View.WBP_GuildSkillRight.TB_SkillName:SetText(currentSkill.Prop)
    self:ConfigCurrencyTip(self.View.WBP_GuildSkillRight.WBP_ComResourceTip,
        Game.TableData.GetConstDataRow("GUILD_EXERCISE_MONEY_TYPE"))
    self:ConfigCurrencyTip(self.View.WBP_GuildSkillRight.WBP_ComResourceTip_1,
        Game.TableData.GetConstDataRow("GUILD_EXERCISE_CONTRIBUTION"))

    self:SetSkillIcon(self.View.WBP_GuildSkillRight.WBP_SkillItem, currentSkill.Icon)

    ---- 如果没有下一级，那么该技能已满级
    if not self.nextLevelSkillDetail then
        self.View.WBP_GuildSkillRight.WBP_ComBtnLightM:SetVisibility(ESlateVisibility.Hidden)
        self.View.WBP_GuildSkillRight.CannotExercise:SetVisibility(ESlateVisibility.Hidden)
        self:SetGuildSkillDetailPanelMax(level)
    else
        if self.buildingLevel < self.nextLevelSkillDetail.GuildLimit then
            self.View.WBP_GuildSkillRight.WBP_ComBtnLightM:SetVisibility(ESlateVisibility.Hidden)
            self.View.WBP_GuildSkillRight.CannotExercise:SetVisibility(ESlateVisibility.Visible)
            self.View.WBP_GuildSkillRight.CannotExercise:SetText(string.format(stringConst.Get('GUILD_BUILD_UNLOCK'),
                self.nextLevelSkillDetail.GuildLimit))
        else
            self.View.WBP_GuildSkillRight.WBP_ComBtnLightM:SetVisibility(ESlateVisibility.Visible)
            self.View.WBP_GuildSkillRight.CannotExercise:SetVisibility(ESlateVisibility.Hidden)
        end
        self:SetGuildSkillDetailPanelInfo(level)
    end

    --if self.buildingLevel
end

-- 设置面板
function P_GuildInsideSkill:SetGuildSkillDetailPanelInfo(level)
    self.View.WBP_GuildSkillRight.TB_Level:SetText(stringConst.Get("GUILD_SKILL_LEVEL"))
    self.View.WBP_GuildSkillRight.VB_UpgradeReqMain:SetVisibility(ESlateVisibility.Visible)
    self.View.WBP_GuildSkillRight.VB_UpgradeInfo:SetVisibility(ESlateVisibility.Visible)

    local a, _ = math.modf(self.nextLevelSkillDetail.Money)
    local b, _ = math.modf(self.nextLevelSkillDetail.Contribution)
    self.View.WBP_GuildSkillRight.WBP_ComResourceTip.Text_Count:SetText(tostring(a))
    self.View.WBP_GuildSkillRight.WBP_ComResourceTip_1.Text_Count:SetText(tostring(b))
    self:SetUpgradeButton(false)
    if GetMainPlayerPropertySafely("Level") >= self.nextLevelSkillDetail.PlayerLimit then
        self.View.WBP_GuildSkillRight.TextUpgradeRequirement:SetText(
            string.format(stringConst.Get("GUILD_SKILL_LEVEL_REQ"), self.nextLevelSkillDetail.PlayerLimit)
        )
    else
        self.View.WBP_GuildSkillRight.TextUpgradeRequirement:SetText(
            string.format(stringConst.Get("GUILD_SKILL_LEVEL_REQ_RED"), self.nextLevelSkillDetail.PlayerLimit)
        )
    end
    if self.buildingLevel >= self.nextLevelSkillDetail.GuildLimit then
        self.View.WBP_GuildSkillRight.TextUpgradeRequirement_1:SetText(
            string.format(stringConst.Get("GUILD_SKILL_BUILDING_REQ"), self.nextLevelSkillDetail.GuildLimit)
        )
    else
        self.View.WBP_GuildSkillRight.TextUpgradeRequirement_1:SetText(
            string.format(stringConst.Get("GUILD_SKILL_BUILDING_REQ_RED"), self.nextLevelSkillDetail.GuildLimit)
        )
    end
    self.View.WBP_GuildSkillRight.TB_Level:SetText(stringConst.Get("GUILD_SKILL_LEVEL"))
    self.View.WBP_GuildSkillRight.TB_CurrentSkillLevel:SetText(tostring(level))

    self.skillProp = self.nextLevelSkillDetail.PropModeName
    if not self.currentSkillDetail then
        self.skillVal = { 0, self.nextLevelSkillDetail.PropIncr }
    else
        self.skillVal = { self.currentSkillDetail.PropIncr, self.nextLevelSkillDetail.PropIncr }
    end
    self.guildSkillUpgradeGroup:SetData(#self.nextLevelSkillDetail.PropModeName)
end

-- 设置满级面板
function P_GuildInsideSkill:SetGuildSkillDetailPanelMax(level)
    self.View.WBP_GuildSkillRight.TB_Level:SetText(stringConst.Get("GUILD_SKILL_LEVEL"))
    self.View.WBP_GuildSkillRight.VB_UpgradeReqMain:SetVisibility(ESlateVisibility.Hidden)
    self.View.WBP_GuildSkillRight.VB_UpgradeInfo:SetVisibility(ESlateVisibility.Hidden)

    self.View.WBP_GuildSkillRight.TextUpgradeRequirement:SetText(
        string.format(stringConst.Get("GUILD_SKILL_TOP_LEVEL"), self.currentSkillDetail.PlayerLimit)
    )
    self.View.WBP_GuildSkillRight.TextUpgradeRequirement_1:SetText(
        string.format(stringConst.Get("GUILD_SKILL_SKILL_TOP_LEVEL"), self.currentSkillDetail.GuildLimit)
    )
    self.View.WBP_GuildSkillRight.TB_Level:SetText(stringConst.Get("GUILD_SKILL_LEVEL"))
    self.View.WBP_GuildSkillRight.TB_CurrentSkillLevel:SetText(tostring(level))

    self.skillProp = self.currentSkillDetail.PropModeName
    self.skillVal = { self.currentSkillDetail.PropIncr }
    self.guildSkillUpgradeGroup:SetData(#self.currentSkillDetail.PropModeName)
end

-- 设置升级按钮
function P_GuildInsideSkill:SetUpgradeButton(bIsMax)
    if bIsMax then
        self.View.WBP_GuildSkillRight.WBP_ComBtnLightM.Text_Com:SetText(stringConst.Get("GUILD_SKILL_SKILL_TOP_LEVEL"))
    else
        self.View.WBP_GuildSkillRight.WBP_ComBtnLightM.Text_Com:SetText(stringConst.Get("GUILD_SKILL_EXERCISE"))
    end
end

-- 设置花费货币图标
function P_GuildInsideSkill:ConfigCurrencyTip(widget, CurrencyType)
    local currencyInfo = Game.TableData.GetItemNewDataRow(CurrencyType)
    if currencyInfo then
        local iconPath = Game.UIIconUtils.GetIconByItemId(CurrencyType)
        if iconPath then
            self:SetImage(widget.Icon, iconPath)
        end
    end
end

-- 设置技能图标
function P_GuildInsideSkill:SetSkillIcon(widget, iconPath)
    if widget and iconPath then
        self:SetImage(widget.Img_Icon, iconPath)
        widget.Img_Lock:SetVisibility(ESlateVisibility.Hidden)
    end
end

-- 提示按钮
function P_GuildInsideSkill:OnClickInfoButton()
    Game.TipsSystem:ShowTips(Enum.ETipsData.GUILD_PRACTICE, self.View.WBP_ComPanelNoTab.WBP_ComBtnBack.Btn_Info:GetCachedGeometry())
end

-------------------------------------------------------OnClick---------------------------------------------------------

function P_GuildInsideSkill:OnClickBasicSkill(index)
    self.lastSelected.funcName = "OnClickBasicSkill"
    self.lastSelected.index = index
    if self.guildSkillsData[self.skillType.BASIC] then
        self:OnClickSkillItem(
            self.basicSkillView:GetRendererAt(index), self.guildSkillsData[self.skillType.BASIC][index]
        )
    end
end

function P_GuildInsideSkill:OnClickAdvancedSkill(index)
    self.lastSelected.funcName = "OnClickAdvancedSkill"
    self.lastSelected.index = index
    if self.guildSkillsData[self.skillType.ADVANCED] then
        self:OnClickSkillItem(self.advancedSkillView:GetRendererAt(index),
            self.guildSkillsData[self.skillType.ADVANCED][index])
    end
end

function P_GuildInsideSkill:OnClickHighSkill(index)
    self.lastSelected.funcName = "OnClickHighSkill"
    self.lastSelected.index = index
    if self.guildSkillsData[self.skillType.HIGH] then
        self:OnClickSkillItem(self.highSkillView:GetRendererAt(index), self.guildSkillsData[self.skillType.HIGH][index])
    end
end

function P_GuildInsideSkill:OnClickSkillItem(widget, skillID)
    if widget and skillID then
        if self.currentSkillWidget then
            self.currentSkillWidget.Overlay_Selected:SetVisibility(ESlateVisibility.Hidden)
        end
        self.currentSkillWidget = widget
        self.currentSkillID = skillID
        self.currentSkillWidget.Overlay_Selected:SetVisibility(ESlateVisibility.Visible)
        self:SetGuildSkillDetailPanel(skillID)
    end
end

function P_GuildInsideSkill:OnclickBack()
    self:CloseSelf()
end

function P_GuildInsideSkill:EscOnClickEvent()
    self:OnclickBack()
end

function P_GuildInsideSkill:OnClickExercise()
    if self.buildingLevel < self.nextLevelSkillDetail.GuildLimit then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SKILL_SCHOOL_LEVEL_LIMIT)
    elseif GetMainPlayerPropertySafely("Level") < self.nextLevelSkillDetail.PlayerLimit then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SKILL_LV_LIMIT)
    elseif Game.CurrencySystem:GetMoneyByType(Game.TableData.GetConstDataRow("GUILD_EXERCISE_CONTRIBUTION")) < self.nextLevelSkillDetail.Contribution then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_SKILL_CONTRI_LIMIT)
    else
        Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(
            Game.TableData.GetConstDataRow("GUILD_EXERCISE_MONEY_TYPE"),
            self.nextLevelSkillDetail.Money,
            function()
                Game.GuildSystem.sender:doGuildExercise(self.nextLevelSkillDetail.ExerciseId)
            end
        )
    end
end

return P_GuildInsideSkill
