local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ClickEffectPanel : UIPanel
---@field view ClickEffectPanelBlueprint
local ClickEffectPanel = DefineClass("ClickEffectPanel", UIPanel)

local ESlateVisibility = import("ESlateVisibility")
local KismetInputLibrary = import("KismetInputLibrary")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

ClickEffectPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ClickEffectPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ClickEffectPanel:InitUIData()
	---@type table<number,number> 记录玩家点击时间
	self.ClickTime = {[1] = 0,[2] = 0,[3] = 0}
	---@type table 动效节点
	self.Effects = {}
	self.Effects[1] = {self.view.WBP_ClickEffect1,self.view.WBP_ClickEffect2,self.view.WBP_ClickEffect3}
	self.Effects[2] = {self.view.WBP_QTEClickEffect1,self.view.WBP_QTEClickEffect2, self.view.WBP_QTEClickEffect3}
	self.ClickEffects = self.Effects[1]
	self.ClickEffectsQte = self.Effects[2]
	self.currentType = 1
	---@type number 动效时长
	self.AnimationTime = self.ClickEffects[1].Ani_Click:GetEndTime()
	if Game.UIInputProcessorManager and Game.UIInputProcessorManager.BindMouseButtonDownEvent then
		Game.UIInputProcessorManager:BindMouseButtonDownEvent(self, "OnMouseButtonDown")
	else
		Log.Warning("BindMouseButtonDownEvent method does not exist in UIInputProcessorManager")
	end
end

--- UI组件初始化，此处为自动生成
function ClickEffectPanel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ClickEffectPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ClickEffectPanel:InitUIView()
end

---面板打开的时候触发
function ClickEffectPanel:OnRefresh(...)
end

function ClickEffectPanel:OnClose()
end

function ClickEffectPanel:OnTouchStart(pos)
	local index = self:GetClickTimeMinIndex()
	self:PlayAnimationByIndex(index,pos)
	Game.CursorManager:RefreshCheck()
end

function ClickEffectPanel:OnMouseButtonDown(mouseEvent)
	if not Game.CursorManager:IsCursorShow() and self.currentType == 1 then
		return
	end
	---@type FVector2D
	local screenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(mouseEvent)
	local index = self:GetClickTimeMinIndex()
	self:PlayAnimationByIndex(index,screenPos)
	Game.CursorManager:RefreshCheck()
end

function ClickEffectPanel:GetClickTimeMinIndex()
	local minTime = 0
	local minIndex = 1
	for i = 1,3 do
		if self.ClickTime[i] == 0 then
			return i
		else
			if minTime == 0 then
				minTime = self.ClickTime[i]
				minIndex = i
			elseif minTime > self.ClickTime[i] then
				minTime = self.ClickTime[i]
				minIndex = i
			end
		end
	end
	return minIndex
end

function ClickEffectPanel:PlayAnimationByIndex(index,pos)
	local TouchPos = SlateBlueprintLibrary.AbsoluteToLocal(self.userWidget:GetCachedGeometry(), pos)
	self.ClickEffects[index].Slot:SetPosition(TouchPos)
	self.ClickEffects[index]:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.ClickTime[index] = _G._now()
	
	self:PlayAnimation(self.ClickEffects[index].Ani_Click, function()
		self.ClickTime[index] = 0
	end,self.ClickEffects[index],0,1,EUMGSequencePlayMode.Forward,1,false)
end

--临时，qte动效替换
function ClickEffectPanel:SwitchclickEffect(type)
	if type ~= self.currentType and self.Effects[type] then
		for i = 1,3 do
			self.ClickEffects[i]:SetVisibility(ESlateVisibility.Collapsed)
		end
		self.currentType = type
		self.ClickEffects = self.Effects[type]
		for i = 1,3 do
			self.ClickEffects[i]:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
end

---OnOperatorModeCursorStateChange 操作模式导致鼠标显隐状态修改时，通过ClickEffectPanel拦截鼠标操作
---@param bCursorShow boolean 鼠标当前显隐状态
function ClickEffectPanel:OnOperatorModeCursorStateChange(bCursorShow)
	local visibility = bCursorShow and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Visible
	self.userWidget:SetVisibility(visibility)
end

return ClickEffectPanel
