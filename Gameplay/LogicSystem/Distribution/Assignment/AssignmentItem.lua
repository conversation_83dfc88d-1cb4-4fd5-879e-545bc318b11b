local UIComBar = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComBar")
local ItemRewardNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemRewardNew")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class AssignmentItem : UIListItem
---@field view AssignmentItemBlueprint
local AssignmentItem = DefineClass("AssignmentItem", UIListItem)

AssignmentItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function AssignmentItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function AssignmentItem:InitUIData()
	---分配cd
	---@type number
	self.CD = Game.TableData.GetConstDataRow("ROLL_COUNTDOWN")
	---分配物品数据
	---@type table
	self.Params = nil
	---结算回调
	---@type boolean
	self.bCallBackToHide = false
end

--- UI组件初始化，此处为自动生成
function AssignmentItem:InitUIComponent()
    ---@type UIComBar
    self.WBP_ComBar_luaCom = self:CreateComponent(self.view.WBP_ComBar_lua, UIComBar)
    ---@type ItemRewardNew
    self.WBP_ComItemNorm_luaCom = self:CreateComponent(self.view.WBP_ComItemNorm_lua, ItemRewardNew)
end

---UI事件在这里注册，此处为自动生成
function AssignmentItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Demand.OnClicked, "on_Btn_Demand_Clicked")
    self:AddUIEvent(self.view.Btn_Giveup.OnClicked, "on_Btn_Giveup_Clicked")
    self:AddUIEvent(self.view.Btn_Greedy.OnClicked, "on_Btn_Greedy_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function AssignmentItem:InitUIView()
	self:SetContent()
end

---面板打开的时候触发
function AssignmentItem:OnRefresh(param)
	self.Params = param
	self:StopTimer("AssignmentItem")
	if self.Params ~= nil then
		local itemData = Game.DungeonAwardSystem.GetItemData(self.Params.itemId)
		local bShowUseLimit = nil
		if itemData.classlimit == nil or (#itemData.classlimit == 0 or (Game.me and table.contains(itemData.classlimit, Game.me.Profession))) then
			bShowUseLimit = false
		else
			bShowUseLimit = true
		end
		self.WBP_ComItemNorm_luaCom:FillItem(self.Params.itemId,Enum.CommonItemClickType.OverrideTip,false,false,self.Params.count or 0,itemData.quality,nil,nil,bShowUseLimit)
		self.view.Text_Name_lua:SetText(itemData["itemName"])
		if self.Params.RollResult and not string.isEmpty(self.Params.RollResult.WinID) then
			self.view.WBP_ComBar_lua:SetVisibility(ESlateVisibility.Collapsed)
			self.bCallBackToHide = true
		else
			--self.view.WBP_ComBarNew:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self.view.WBP_ComBar_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self:CDUpdate()
			self:StartTimer("AssignmentItem",function()
				self:CDUpdate()
			end, 1000, -1, nil, true)
		end
		if self.Params.State == 0 then --待定
			self.userWidget:Event_UI_Style(0)
		elseif self.Params.State == 1 then  --放弃
			self.userWidget:Event_UI_Style(2)
			self.view.WBP_AssignmentRoll_lua:Event_UI_Style(0)
		else
			self.userWidget:Event_UI_Style(1)
			self.view.WBP_AssignmentRoll_lua.Text_Roll_lua:SetText(self.Params.Point)
			self.view.Text_Roll_lua:SetText(self.Params.Point)
			if self.Params.State == 2 then  --需求
				self.view.WBP_AssignmentRoll_lua:Event_UI_Style(2)
			elseif self.Params.State == 3 then -- 贪婪
				self.view.WBP_AssignmentRoll_lua:Event_UI_Style(1)
			end
		end
		self:PlayRollAnimation(self.Params.State)
	end
end

function AssignmentItem:SetContent()
	self.view.Text_Giveup:SetText(StringConst.Get("DUNGEON_ASSIGNMENT_GIVE_UP"))
	self.view.Text_Demand:SetText(StringConst.Get("DUNGEON_ASSIGNMENT_NEED"))
	self.view.Text_Greedy:SetText(StringConst.Get("DUNGEON_ASSIGNMENT_GREEDY"))
end

function AssignmentItem:PlayRollAnimation(state)
	if state == 0 then
		return
	end
	local anim = nil
	if state == 1 then
		anim = self.view.Ani_State_2_GiveUp
	elseif state == 2 then
		anim = self.view.Ani_State_1_Touzi
	elseif state == 3 then
		anim = self.view.Ani_State_1_Zhua
	end
	self:PlayAnimation(anim)
end

function AssignmentItem:CDUpdate()
	if self.Params == nil or self.view == nil then
		self:StopTimer("AssignmentItem")
		return
	end
	local leftTime = (self.Params.StartTS - _G._now())/1000 + self.CD
	local percent = leftTime/self.CD
	self.WBP_ComBar_luaCom:SetProgressBarValue(percent)
	if percent <= Game.TableData.GetConstDataRow("ASSIGNMENT_WARING_TIME_PERCENT")/100 then
		self.WBP_ComBar_luaCom:SetColorType(Enum.UIComBarColorType.Warning)
	else
		self.WBP_ComBar_luaCom:SetColorType(Enum.UIComBarColorType.Default)
	end
end

--- 此处为自动生成
function AssignmentItem:on_Btn_Demand_Clicked()
	if self.Params.State == 0 then
		Game.DungeonAwardSystem:ReqDungeonRoll(self.Params.GroupID,self.Params.gbId,2)
	end
end

--- 此处为自动生成
function AssignmentItem:on_Btn_Giveup_Clicked()
	if self.Params.State == 0 then
		Game.DungeonAwardSystem:ReqDungeonRoll(self.Params.GroupID,self.Params.gbId,1)
	end
end

--- 此处为自动生成
function AssignmentItem:on_Btn_Greedy_Clicked()
	if self.Params.State == 0 then
		Game.DungeonAwardSystem:ReqDungeonRoll(self.Params.GroupID,self.Params.gbId,3)
	end
end

return AssignmentItem
