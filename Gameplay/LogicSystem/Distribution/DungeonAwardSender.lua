---@class DungeonAwardSender : SystemSenderBase
local DungeonAwardSender = DefineClass("DungeonAwardSender",SystemSenderBase)

function DungeonAwardSender:ReqRefreshHelpInfo(helpList)
    self.Bridge:ReqRefreshHelpInfo(helpList)
end

function DungeonAwardSender:Req<PERSON><PERSON><PERSON>Roll(RollID, GoodsID, RollType)
    self.Bridge:Req<PERSON><PERSON><PERSON>Roll(RollID, GoodsID, RollType)
end

function DungeonAwardSender:ReqDungeonAuctionBid(AuctionID, GoodsID, MoneyType, MoneyCount)
    Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(MoneyType, MoneyCount, function()
        self.Bridge:ReqDungeonAuctionBid(AuctionID, GoodsID, MoneyType, MoneyCount)
    end)
end

function DungeonAwardSender:ReqDungeonAuctionGiveUpBid(AuctionID, GoodsID, bGiveUp)
    self.Bridge:ReqDungeonAuctionGiveUpBid(AuctionID, GoodsID, bGiveUp)
end

return DungeonAwardSender