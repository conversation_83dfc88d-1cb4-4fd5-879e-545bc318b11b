local NetworkManager = require "Framework.DoraSDK.NetworkManager"

---@class DungeonAwardSystem : SystemBase
local DungeonAwardSystem = DefineClass("DungeonAwardSystem", SystemBase)

---------------------------------------------------拍卖 start-------------------------------------------------------------

DungeonAwardSystem.AuctionState = {
    UnStart = 0,
    Going = 1,
    End = 2
}

function DungeonAwardSystem:ReqDungeonAuctionBid(AuctionID, GoodsID, MoneyType, MoneyCount)
    self.sender:ReqDungeonAuctionBid(AuctionID, GoodsID, MoneyType, MoneyCount)
end

function DungeonAwardSystem:ReqDungeonAuctionGiveUpBid(AuctionID, GoodsID, bGiveUp)
    self.sender:ReqDungeonAuctionGiveUpBid(AuctionID, GoodsID, bGiveUp)
end

function DungeonAwardSystem:RetDungeonAuctionGiveUpBid(err, GoodsID, bGiveUp)
    if err == NetworkManager.ErrCodes.NO_ERR then
        self.model.ItemGiveUpState[GoodsID] = bGiveUp
		Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_GIVEUP)
    else
        Log.Debug("auction give up bid req failed")
    end
end

---竞拍数据全量更新
---@param DungeonAuctionSyncClientInfos table
function DungeonAwardSystem:OnMsgDungeonAuctionRefresh(DungeonAuctionSyncClientInfos)
    for k, v in pairs(DungeonAuctionSyncClientInfos) do
        for i = 1, #v.Queue do
            local id = v.Queue[i]
            local info = v.GoodsInfos[id]
            Game.ChatSystem:AddEquipData(id, info)
            info.GroupID = k
            self:UpdateAuctionInfo(info, v.SyncInfos[id])
            info.Index = i
            if info.State == self.AuctionState.UnStart then
                table.insert(self.model.WaitAuctionList, info)
            elseif info.State == self.AuctionState.Going then
                table.insert(self.model.NowAuctionList, info)
            else
                table.insert(self.model.HistoryAuctionList, 1, info)
            end
            self.model.AllAuctionList[info.gbId] = info
        end
        if _G._now() < self.model.AllAuctionList[v.Queue[1]].StartTS then
            if self.model.GroupTimer[k] ~= nil then
                Game.TimerManager:StopTimerAndKill(self.model.GroupTimer[k])
                self.model.GroupTimer[k] = nil
            end
            self.model.GroupTimer[k] = Game.TimerManager:CreateTimerAndStart(
                function()
                    self:OnStartAuciton(v.Queue[1])
                    self.model.GroupTimer[k] = nil
                end,
                    self.model.AllAuctionList[v.Queue[1]].StartTS - _G._now(), 1)
        end
        if v.bGiveUps ~= nil then
            for goodID, bGiveUp in pairs(v.bGiveUps) do
                self.model.ItemGiveUpState[goodID] = bGiveUp
            end
        end
    end
    --self:CheckAuctionData()
    self:ShowHUDAuction()
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_UPDATE, nil)
    self:CheckAuctionList()
end

function DungeonAwardSystem:OnMsgSyncDungeonAuctionRemove(waitRemoveIDList)
    for _,id in pairs(waitRemoveIDList) do
        for i = #self.model.WaitAuctionList,1,-1 do
            if self.model.WaitAuctionList[i].GroupID == id then
                table.remove(self.model.WaitAuctionList,i)
            end
        end
        for i = #self.model.NowAuctionList,1,-1 do
            if self.model.NowAuctionList[i].GroupID == id then
                table.remove(self.model.NowAuctionList,i)
            end
        end
        for i = #self.model.HistoryAuctionList,1,-1 do
            if self.model.HistoryAuctionList[i].GroupID == id then
                table.remove(self.model.HistoryAuctionList,i)
            end
        end
        for k,v in pairs(self.model.AllAuctionList) do
            if v.GroupID == id then
                self.model.AllAuctionList[k] = nil
            end
        end
        if self.model.GroupTimer[id] ~= nil then
            Game.TimerManager:StopTimerAndKill(self.model.GroupTimer[id])
            self.model.GroupTimer[id] = nil
        end
    end
    --self:CheckAuctionData()
    self:ShowHUDAuction()
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_UPDATE, nil)
    self:CheckAuctionList()
end

---增量更新拍卖信息
---@param DungeonAuctionSyncClientInfos table
function DungeonAwardSystem:OnMsgDungeonAuctionRefreshState(DungeonAuctionSyncClientInfos)
    local updateUid = nil
    for k, v in pairs(DungeonAuctionSyncClientInfos) do
        for id, syncinfo in pairs(v.SyncInfos) do
            if self.model.AllAuctionList[id] ~= nil then
                -- local bBidderChange =  self:UpdateAuctionInfo(self.model.AllAuctionList[id], syncinfo)
				self:UpdateAuctionInfo(self.model.AllAuctionList[id], syncinfo)
                if syncinfo.State == self.AuctionState.Going then
                    --if bBidderChange then
                    --    self:ShowHUDTips(updateUid)
                    --else
                        --从待拍卖队列中移除，加入正在拍卖队列
					for i = #self.model.WaitAuctionList,1,-1 do
						if self.model.WaitAuctionList[i].gbId == id then
							table.remove(self.model.WaitAuctionList, i)
							table.insert(self.model.NowAuctionList, self.model.AllAuctionList[id])
						end
					end
                    --end
                end
                if syncinfo.State == self.AuctionState.End then
                    self:ShowAuctionResult(self.model.AllAuctionList[id])
                    --从正在拍卖队列中移除，加入历史拍卖队列
                    for i = #self.model.NowAuctionList,1,-1 do
                        if self.model.NowAuctionList[i].gbId == id then
                            table.remove(self.model.NowAuctionList, i)
                        end
                    end
                    for i = #self.model.WaitAuctionList,1,-1 do
                        if self.model.WaitAuctionList[i].gbId == id then
                            table.remove(self.model.WaitAuctionList, i)
                        end
                    end
                    table.insert(self.model.HistoryAuctionList, 1, self.model.AllAuctionList[id])
				else
					self:OnAuctionBiddering(self.model.AllAuctionList[id])
                end
            end
        end
        self:ShowHUDTips(updateUid)
    end
    --self:CheckAuctionData()
    self:ShowHUDAuction()
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_UPDATE, updateUid)
    self:CheckAuctionList()
end

function DungeonAwardSystem:ShowHUDAuction()
    if self:CheckAuctionData() then
        Game.HUDSystem:ShowUI("P_HUDDungeonAwardAuction")
		Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_SHOW_DUNGEON_AWARD_AUCTION)
    end
end

function DungeonAwardSystem:CheckAuctionData()
    return #self.model.NowAuctionList > 0 or #self.model.WaitAuctionList > 0
end

function DungeonAwardSystem:ShowHUDTips(updateUid)
    local AuctionItem = self.model.AllAuctionList[updateUid]
    if AuctionItem == nil then
        AuctionItem = self.model.NowAuctionList[1]
    end
    if AuctionItem ~= nil and self:GetItemGiveUpState(AuctionItem.gbId) ~= true then
        Game.HUDSystem:ShowUI("P_HUDDungeonAwardTips", true, AuctionItem)
		Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_SHOW_DUNGEON_AWARD_TIPS, true, AuctionItem)
    else
        Game.HUDSystem:ShowUI("P_HUDDungeonAwardTips", true, nil)
		Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_SHOW_DUNGEON_AWARD_TIPS, true, nil)
	end
end

function DungeonAwardSystem:OnAuctionBiddering(auctionItem)
    local bBiddered = auctionItem.BidderID ~= nil
    if bBiddered == true then
        self:AddAuctionBidMsgtoSystemInfo(auctionItem)
    end
end

function DungeonAwardSystem:ShowAuctionResult(auctionItem)
    local bBiddered = auctionItem.BidderID ~= nil
    if bBiddered == true and auctionItem.MoneyType ~= 0 then
        local itemData = self.GetItemData(auctionItem.itemId)
        local itemName = itemData["itemName"]
        local moneyData = self.GetItemData(auctionItem.MoneyType)
        if moneyData then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.AUCTION_COMPLETE, { {
                auctionItem.BidderName,
                moneyData["itemName"],
                auctionItem.MoneyCount,
                itemName
            } })
        end
        self:AddAuctionResultMsgtoSystemInfo(auctionItem, bBiddered)
    end
end

function DungeonAwardSystem:AddAuctionResultMsgtoSystemInfo(auctionItem, bBiddered)
    local itemID = auctionItem.itemId
    local gbID = auctionItem.gbId
    local itemData = self.GetItemData(auctionItem.itemId)
    local itemName = string.format("[%s]",itemData["itemName"])
    local itemstr = Game.ChatSystem:GetRegMsg(Enum.EMsgReg.ITEM ,itemData["quality"], itemID, gbID, itemName)
    local str = ""
    if bBiddered then
        local moneyData = self.GetItemData(auctionItem.MoneyType)
        if moneyData then
            local moneyType = moneyData["itemName"]
            str = string.format(self:GetSystemText(Enum.EChatSystemData["AUCTION_COMPLETE"]), auctionItem.BidderName,
                auctionItem.MoneyCount, moneyType, itemstr)
        end
    else
        str = string.format(self:GetSystemText(Enum.EChatSystemData["AUCTION_ALL_GIVEUP"]), itemstr)
    end
    Game.ChatSystem:AddChannelSystemInfo(str, _G._now(), Enum.EChatChannelData["SYSTEM"], itemData["quality"] >= 5)
end

function DungeonAwardSystem:AddAuctionBidMsgtoSystemInfo(auctionItem)
    if auctionItem.MoneyType ~= 0 then
        local itemID = auctionItem.itemId
        local gbID = auctionItem.gbId
        local itemData = self.GetItemData(auctionItem.itemId)
        local itemName = string.format("[%s]",itemData["itemName"])
        local moneyData = self.GetItemData(auctionItem.MoneyType)
        if moneyData then
            local moneyType = moneyData["itemName"]
            local itemstr = Game.ChatSystem:GetRegMsg(Enum.EMsgReg.ITEM ,itemData["quality"], itemID, gbID, itemName)
            local str = string.format(self:GetSystemText(Enum.EChatSystemData["AUCTION_BID"]), auctionItem.BidderName,
                itemstr, auctionItem.MoneyCount, moneyType)
            Game.ChatSystem:AddChannelSystemInfo(str, _G._now(), Enum.EChatChannelData["SYSTEM"], itemData["quality"] >= 5)
        end
        
    end
end

function DungeonAwardSystem:CheckAuctionList()
    if #self.model.WaitAuctionList == 0 and #self.model.NowAuctionList == 0 then
        self.model:ClearAuctionData()
        self:HideAuctionPanel()
    end
end

function DungeonAwardSystem:UpdateAuctionInfo(data, syncinfo)
    local bBidderChange = false
    if syncinfo.BindderID ~= GetMainPlayerEID() and data.BidderID == GetMainPlayerEID() then
        bBidderChange = true
    end
    data.BidderID = syncinfo.BidderID
    data.MoneyCount = syncinfo.MoneyCount
    data.BidTS = syncinfo.BidTS
    data.MoneyType = syncinfo.MoneyType
    data.BidderName = syncinfo.BidderName
    data.StartTS = syncinfo.StartTS
    data.State = syncinfo.State
    return bBidderChange
end

function DungeonAwardSystem:OnStartAuciton(uid)
    local firstAuctionInfo = nil
    for i = 1, #self.model.WaitAuctionList do
        if self.model.WaitAuctionList[i].gbId == uid then
            firstAuctionInfo = self.model.WaitAuctionList[i]
            table.remove(self.model.WaitAuctionList, i)
            break
        end
    end
    if firstAuctionInfo ~= nil then
        firstAuctionInfo.State = self.AuctionState.Going
        table.insert(self.model.NowAuctionList, firstAuctionInfo)
    end
    self:ShowHUDTips(firstAuctionInfo ~= nil and firstAuctionInfo.gbId or nil)
    Game.HUDSystem:ShowUI("P_HUDDungeonAwardAuction")
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_UPDATE, firstAuctionInfo ~= nil and firstAuctionInfo.gbId or nil)
end

function DungeonAwardSystem:GetItemGiveUpState(uid)
    if self.model.ItemGiveUpState[uid] == nil then
        return false
    else
        return self.model.ItemGiveUpState[uid]
    end
end

function DungeonAwardSystem:ShowAuctionPanel()
    --UI.ShowUI("P_DungeonAwardAuction")
	Game.NewUIManager:OpenPanel(UIPanelConfig.Auction_Panel)
end

function DungeonAwardSystem:HideAuctionPanel()
    --UI.HideUI("P_DungeonAwardAuction")
	Game.NewUIManager:ClosePanel(UIPanelConfig.Auction_Panel)
end

function DungeonAwardSystem:OnMsgNotifyGiveUpBid(GoodsID, AvatarName)
    local auctionItem = self.model.AllAuctionList[GoodsID]
    if auctionItem ~= nil then
        local itemData = self.GetItemData(auctionItem.itemId)
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PLAYER_GIVEUP_AUCTION, { {
            AvatarName,
            itemData["itemName"]
        } })
        self:AddGiveUpBidMsgtoSystemInfo(auctionItem, AvatarName)
    end
end

function DungeonAwardSystem:AddGiveUpBidMsgtoSystemInfo(auctionItem, AvatarName)
    local itemID = auctionItem.itemId
    local gbID = auctionItem.gbId
    local itemData = self.GetItemData(auctionItem.itemId)
    local itemName = string.format("[%s]",itemData["itemName"])
    local itemstr = Game.ChatSystem:GetRegMsg(Enum.EMsgReg.ITEM ,itemData["quality"], itemID, gbID, itemName)
    local str = string.format(self:GetSystemText(Enum.EChatSystemData["AUCTION_GIVEUP"]), AvatarName, itemstr)

    Game.ChatSystem:AddChannelSystemInfo(str, _G._now(), Enum.EChatChannelData["SYSTEM"], itemData["quality"] >= 5)
end

function DungeonAwardSystem:GetSystemText(enum)
    local ChatSystemData = Game.TableData.GetChatSystemDataTable()
    local data = ChatSystemData[enum]
    return data and data["Content"] or ""
end

function DungeonAwardSystem:SetSelectAuctionUID(uid)
    self.model.SelectAuctionUID = uid
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_SELECT_AUCTION, uid)
end

---------------------------------------------------拍卖 end---------------------------------------------------------------

---------------------------------------------------分配 start-------------------------------------------------------------

DungeonAwardSystem.AssignmentState = {
    Empty = 0,
    GiveUp = 1,
    Demand = 2,
    Greed = 3
}

function DungeonAwardSystem:UpdateOneAssignmentItem(SyncStates)
    for _, v in pairs(SyncStates) do
        for id, stateinfo in pairs(v) do
            local info = self.model.AllAssignmentList[id]
            if info ~= nil then
                DungeonAwardSystem.MergeAssignmentInfo(info, stateinfo)
                --已经出分配结果，全部放弃或某玩家获得
                if info.RollResult ~= nil and not string.isEmpty(info.RollResult.WinID) then
                    self:AddAssignmentRetChatChannel(info.RollResult, id)
                    self:RemoveEndAssignmentItem(id)
                end
            end
        end
    end
    self:ShowHUDAssignment()
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_ASSIGNMENT_ROLL_RESULT, SyncStates)
end

function DungeonAwardSystem:AddAssignmentRetChatChannel(rollResult, id)
    if rollResult.WinName == nil or rollResult.WinName == "" then
        return
    end
    local name = rollResult.WinName
    local state = rollResult.WinState
    local point = rollResult.WinPoint
    local assignmentItem = self.model.AllAssignmentList[id]
    local itemID = assignmentItem.itemId
    local gbID = assignmentItem.gbId
    local itemData = self.GetItemData(assignmentItem.itemId)
    local itemName = string.format("[%s]",itemData["itemName"])
    local itemstr = Game.ChatSystem:GetRegMsg(Enum.EMsgReg.ITEM ,itemData["quality"], itemID, gbID, itemName)
    local str = ""
	if state == 2 then
		str = string.format(self:GetSystemText(Enum.EChatSystemData["ALLOCATION_COMPLETE_NEED"]), name, point, itemstr)
	elseif state == 3 then
		str = string.format(self:GetSystemText(Enum.EChatSystemData["ALLOCATION_COMPLETE_GREED"]), name, point, itemstr)
	elseif state == 1 then
		str = string.format(self:GetSystemText(Enum.EChatSystemData["ALLOCATION_ALL_GIVEUP"]), itemstr, name)
	end
    if str then
        Game.ChatSystem:AddChannelSystemInfo(str, _G._now(), Enum.EChatChannelData["SYSTEM"], itemData["quality"] >= 5)
    end
end

function DungeonAwardSystem:ReqDungeonRoll(RollID, GoodsID, RollType)
    self.sender:ReqDungeonRoll(RollID, GoodsID, RollType)
end

---新分配
---@param SyncInfos any
function DungeonAwardSystem:OnMsgSyncDungeonRollRefresh(SyncInfos)
    self:ClearAssignmentList()
	for k, v in pairs(SyncInfos) do
		self.model.AssignmentGroupStartTS[k] = v.StartTS
		table.insert(self.model.WaitPopupAssignmentGroup, k)
		if self.model.AssignmentTimer[k] ~= nil then
			Game.TimerManager:StopTimerAndKill(self.model.AssignmentTimer[k])
			self.model.AssignmentTimer[k] = nil
		end
		Log.InfoFormat("DungeonAwardSystem:OnMsgSyncDungeonRollRefresh StartTS:%d,_G._now():%d",v.StartTS, _G._now())
		self.model.AssignmentTimer[k] = Game.TimerManager:CreateTimerAndStart(
			function()
				self:ClearAssignmentList()
			end,
			v.StartTS + Game.TableData.GetConstDataRow("ROLL_COUNTDOWN") * 1000 - _G._now(), 1)
		for id, goodsinfo in pairs(v.GoodsInfos) do
			local info = goodsinfo
			info.GroupID = k
			info.StartTS = v.StartTS
			info.ID = id
			self.MergeAssignmentInfo(info, v.RollStates[id])
			self.model.AllAssignmentList[id] = info
			table.insert(self.model.SortedAssignmentList, info)
			Game.ChatSystem:AddEquipData(id, goodsinfo)
		end
		

		self:ItemsSortRule(self.model.SortedAssignmentList)
		self:HUDAssignmentTipsShow()
		self:ShowHUDAssignment()
		Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_ASSIGNMENT_UPDATE)
	end
end

function DungeonAwardSystem:OnMsgSyncDungeonRollRemove(waitRemoveIDList)
    for _, id in pairs(waitRemoveIDList) do 
        self.model.AssignmentGroupStartTS[id] = nil
        if self.model.AssignmentTimer[id] ~= nil then
            Game.TimerManager:StopTimerAndKill(self.model.AssignmentTimer[id])
            self.model.AssignmentTimer[id] = nil
        end
        for k,v in pairs(self.model.AllAssignmentList) do
            if v.GroupID == k then
                self.model.AllAssignmentList[k] = nil
            end
        end
        for i = #self.model.SortedAssignmentList,1,-1 do
            if self.model.SortedAssignmentList[i].GroupID == id then
                table.remove(self.model.SortedAssignmentList,i)
            end
        end
    end
    self:ShowHUDAssignment()
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_ASSIGNMENT_ROLL_RESULT)
end

function DungeonAwardSystem:ShowHUDAssignment()
    if self:CheckAssignmentData() then
        Game.HUDSystem:ShowUI("P_HUDDungeonAwardAssignment")
		Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_SHOW_DUNGEON_AWARD_ASSIGNMENT)
    elseif UI.IsShow("P_HUDDungeonAwardAssignment") then
        Game.HUDSystem:HideUI("P_HUDDungeonAwardAssignment")
    end
end

function  DungeonAwardSystem:CheckAssignmentData()
    return self:GetEarlyGroupAssignmentTime() > 0 and self:GetEarlyGroupAssignmentTime() + Game.TableData.GetConstDataRow("ROLL_COUNTDOWN") * 1000 > _G._now()
end

function DungeonAwardSystem:HUDAssignmentTipsShow()
    Game.HUDSystem:ShowUI("P_HUDDungeonAwardTips",false)
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_SHOW_DUNGEON_AWARD_TIPS, false)
end

-- 背包排序规则
function DungeonAwardSystem:ItemsSortRule(ItemList)
    --新排序规则 品质 > 物品子类 > 物品ID 
    --相同物品 堆叠数量 > 新物品 > 有效期
    table.sort(
        ItemList,
        function(a, b)
            local idda = Game.TableData.GetItemNewDataTable(a.itemId)
            local iddb = Game.TableData.GetItemNewDataTable(b.itemId)

            --local invdda = Game.TableData.GetInventoryDataRow(idda.invId)
            --local invddb = Game.TableData.GetInventoryDataRow(iddb.invId)

            local qualityA = idda.quality or 0
            local qualityB = iddb.quality or 0
			
            if qualityA ~= qualityB then
                return qualityA > qualityB
			elseif idda.subType ~= iddb.subType then
				return idda.subType > iddb.subType
            elseif a.itemId ~= b.itemId then
                return a.itemId > b.itemId
            elseif a.bound ~= b.bound then
                return a.bound == 1
            elseif a.count ~= b.count then
                return a.count > b.count
            end
        end
    )
    return ItemList
end

function DungeonAwardSystem:ClearAssignmentList()
    for k, v in pairs(self.model.AssignmentGroupStartTS) do
        if v + Game.TableData.GetConstDataRow("ROLL_COUNTDOWN") * 1000 <= _G._now() then
            for i = #self.model.SortedAssignmentList, 1, -1 do
                if self.model.SortedAssignmentList[i].GroupID == k then
                    self.model.AllAssignmentList[self.model.SortedAssignmentList[i].gbId] = nil
                    table.remove(self.model.SortedAssignmentList, i)
                end
            end
            self.model.AssignmentGroupStartTS[k] = nil
            self.model.AssignmentTimer[k] = nil
            table.removev(self.model.WaitPopupAssignmentGroup, k)
        end
    end
end

function DungeonAwardSystem:RemoveEndAssignmentItem(ID)
    local beRemoveedGroupID = nil
    local bNeedRemoveGroup = true
    for i = #self.model.SortedAssignmentList, 1, -1 do
        if self.model.SortedAssignmentList[i].gbId == ID then
            self.model.AllAssignmentList[self.model.SortedAssignmentList[i].gbId] = nil
            beRemoveedGroupID = self.model.SortedAssignmentList[i].GroupID
            table.remove(self.model.SortedAssignmentList, i)
        end
    end
    for i = #self.model.SortedAssignmentList, 1, -1 do
        if self.model.SortedAssignmentList[i].GroupID == beRemoveedGroupID then
            bNeedRemoveGroup = false
        end
    end
    if bNeedRemoveGroup and beRemoveedGroupID then
        self.model.AssignmentGroupStartTS[beRemoveedGroupID] = nil
        table.removev(self.model.WaitPopupAssignmentGroup, beRemoveedGroupID)
        if self.model.AssignmentTimer[beRemoveedGroupID] ~= nil then
            Game.TimerManager:StopTimerAndKill(self.model.AssignmentTimer[beRemoveedGroupID])
            self.model.AssignmentTimer[beRemoveedGroupID] = nil
        end
    end
    self:HUDAssignmentTipsShow()
end

function DungeonAwardSystem.MergeAssignmentInfo(info, rollstate)
    if info ~= nil and rollstate ~= nil then
        if info.State == 0 and rollstate.State ~= 0 then
            info.RollTime = _G._now() + 3000
        end
        if rollstate.RollResult and not string.isEmpty(rollstate.RollResult.WinID) then
            if info.RollTime <  _G._now() or info.RollTime <= 0 then
                info.ResultTime = _G._now() + 3000
            else
                info.ResultTime = info.RollTime + 3000
            end
        end
        info.State = rollstate.State
        info.Point = rollstate.Point
        info.RollResult = rollstate.RollResult
    end
end

---分配信息更新
function DungeonAwardSystem:OnMsgSyncDungeonRollRefreshState(SyncStates)
    self:UpdateOneAssignmentItem(SyncStates)
end

function DungeonAwardSystem:GetEarlyGroupAssignmentTime()
    if self.model.AssignmentGroupStartTS == nil or table.count(self.model.AssignmentGroupStartTS) == 0 then
        return 0
    else
        local eTime = 0
        for k, v in pairs(self.model.AssignmentGroupStartTS) do
            if eTime == 0 then
                eTime = v
            elseif eTime > v then
                eTime = v
            end
        end
        return eTime
    end
end

function DungeonAwardSystem:GetLastedGroupAssignmentTime()
    local groupid = ""
    if self.model.AssignmentGroupStartTS == nil or table.count(self.model.AssignmentGroupStartTS) == 0 then
        return 0
    else
        local eTime = 0
        for k, v in pairs(self.model.AssignmentGroupStartTS) do
            if eTime == 0 then
                eTime = v
                groupid = k
            elseif eTime < v then
                eTime = v
                groupid = k
            end
        end
        return eTime, groupid
    end
end

function DungeonAwardSystem:HasAssignmentPop()
    return #self.model.WaitPopupAssignmentGroup > 0
end

function DungeonAwardSystem:ShowAssignmentPanel()
    local lefttime = self:GetEarlyGroupAssignmentTime() + Game.TableData.GetConstDataRow("ROLL_COUNTDOWN") * 1000 - _G._now()
    if lefttime <= 0 then
        return
    end
    if #Game.DungeonAwardSystem:GetSortedAssignmentList() < 0  then
        return
    end
	Game.NewUIManager:OpenPanel(UIPanelConfig.Assignment_Panel)
end

---------------------------------------------------分配 end --------------------------------------------------------------

function DungeonAwardSystem.GetItemData(itemID)
    local itemData = Game.TableData.GetItemNewDataRow(itemID)
    if itemID ~= nil then
        if itemData ~= nil then
            return itemData
        end
    end
end

function DungeonAwardSystem:GetNowAuctionList()
    return self.model.NowAuctionList
end

function DungeonAwardSystem:GetWaitAuctionList()
    return self.model.WaitAuctionList
end

function DungeonAwardSystem:GetAllAssignmentList()
    return self.model.AllAssignmentList
end


function DungeonAwardSystem:GetSortedAssignmentList()
    return self.model.SortedAssignmentList
end

function DungeonAwardSystem:GetHistoryAuctionList()
    return self.model.HistoryAuctionList
end

function DungeonAwardSystem:GetAllAuctionList()
    return self.model.AllAuctionList
end

function DungeonAwardSystem:GetSelectAuctionUID()
    return self.model.SelectAuctionUID
end

function DungeonAwardSystem:ClearData()
    self.model:ClearAuctionData()
    self.model:ClearAssignmentData()
end

function DungeonAwardSystem:onCtor()
end

function DungeonAwardSystem:onInit()
    self.model = kg_require("Gameplay.LogicSystem.Distribution.DungeonAwardModel").new(false,true)
    self.sender = kg_require("Gameplay.LogicSystem.Distribution.DungeonAwardSender").new()
    Game.EventSystem:AddListener(_G.EEventTypes.GAMELOOP_ON_LOSE_CONNECTION, self, self.ClearData)
    Game.EventSystem:AddListener(_G.EEventTypes.GAME_MAINPALYER_LOGIN, self, self.ClearData)
end

function DungeonAwardSystem:onUnInit()
end

return DungeonAwardSystem
