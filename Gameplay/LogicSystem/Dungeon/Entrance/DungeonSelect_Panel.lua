local DungeonRewardShow = kg_require("Gameplay.LogicSystem.Dungeon.Settlement.DungeonRewardShow")
local UIComAccordionList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComAccordionList")
local UIComFrame = kg_require("Framework.KGFramework.KGUI.Component.Panel.UIComFrame")
local DungeonSelectTitleAndIntroduce = kg_require("Gameplay.LogicSystem.Dungeon.Entrance.DungeonSelectTitleAndIntroduce")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class DungeonSelect_Panel : UIPanel
---@field view DungeonSelect_PanelBlueprint
local DungeonSelect_Panel = DefineClass("DungeonSelect_Panel", UIPanel)
local NewGroupView = kg_require("Framework.UI.List.NewList.NewGroupView")
local StringConst = require("Data.Config.StringConst.StringConst")
local DungeonStageIcon = kg_require("Gameplay.LogicSystem.Dungeon.Entrance.DungeonStageIcon")
local DungeonFirstPass = kg_require("Gameplay.LogicSystem.Dungeon.Entrance.DungeonFirstPass")
local DungeonTeamStart = kg_require("Gameplay.LogicSystem.Dungeon.Entrance.DungeonTeamStart")
local DungeonMode = kg_require("Gameplay.LogicSystem.Dungeon.Entrance.DungeonMode")
local ESlateVisibility = import("ESlateVisibility")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

DungeonSelect_Panel.LightningCurvePath = UIAssetPath.C_Lightning1

DungeonSelect_Panel.eventBindMap = {
	[EEventTypesV2.ARENA3V3_PVP_MATCH_RESULT] = "OnCancelPVPMatch",
	[EEventTypesV2.ON_SELF_LEVEL_CHANGED] = "OnLevelChanged",
	[EEventTypesV2.DUNGEON_ON_GET_FIRST_PASSAGE_RECORD] = "OnGetFirstPassageRecord",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次IsServer
function DungeonSelect_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonSelect_Panel:InitUIData()
	self.DungeonEnterDataList = { }
	self.DifficultyTypeDict = {}
	self.bSingleMode = false --是否人机模式
	self.bHardMode = false --是否困难模式
	self.FirstPassReward = {}
	self.DropReward = {}
	---Boss展示模型的EntityID列表
	---@type table
	self.bossEntityIDList = {}
	self.StageInfoList = {}     --阶段列表
	self.BuffInfoList = {}
	
	---Boss展示的LevelSequence播放参数
	self.BossDisplaySequenceParams = {}
	---场景本来的坐标
	self.SceneOriginalPostiion = {}
end

--- UI组件初始化，此处为自动生成
function DungeonSelect_Panel:InitUIComponent()
    ---@type DungeonRewardShow 掉落奖励
    self.WBP_DungeonFallRewardCom = self:CreateComponent(self.view.WBP_DungeonFallReward, DungeonRewardShow)
    ---@type DungeonRewardShow	首通奖励
    self.WBP_DungeonFixedRewardCom = self:CreateComponent(self.view.WBP_DungeonFixedReward, DungeonRewardShow)
    ---@type DungeonMode
    self.WBP_DungeonModeCom = self:CreateComponent(self.view.WBP_DungeonMode, DungeonMode)
    ---@type DungeonTeamStart
    self.WBP_DungeonTeamStartCom = self:CreateComponent(self.view.WBP_DungeonTeamStart, DungeonTeamStart)
	---@type DungeonSelectTitleAndIntroduce
    self.WBP_DungeonSelectTitleAndIntroduceCom = self:CreateComponent(self.view.WBP_DungeonSelectTitleAndIntroduce, DungeonSelectTitleAndIntroduce)
    ---@type UIComAccordionList
    self.WBP_ComMutiMenuNewCom = self:CreateComponent(self.view.WBP_ComMutiMenuNew, UIComAccordionList)
    ---@type UIComFrame
    self.WBP_ComPanelCom = self:CreateComponent(self.view.WBP_ComPanel, UIComFrame)
    ---@type DungeonFirstPass	首通的队伍
    self.WBP_DungeonFirstPassCom = self:CreateComponent(self.view.WBP_DungeonFirstPass, DungeonFirstPass)
	--副本阶段列表
	self.StageList = self:CreateComponent(self.view.HB_DungeonStage, NewGroupView, DungeonStageIcon)
end

---UI事件在这里注册，此处为自动生成
function DungeonSelect_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComPanelCom.onTipClickEvent, "on_WBP_ComPanelCom_TipClickEvent")
    self:AddUIEvent(self.WBP_ComMutiMenuNewCom.onItemSelected, "on_WBP_ComMutiMenuNewCom_ItemSelected")
    self:AddUIEvent(self.WBP_DungeonModeCom.onTeamModeClickEvent, "on_WBP_DungeonModeCom_TeamModeClickEvent")
    self:AddUIEvent(self.WBP_DungeonModeCom.onDifficultClickEvent, "on_WBP_DungeonModeCom_DifficultClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonSelect_Panel:InitUIView()
end

---面板打开的时候触发
function DungeonSelect_Panel:OnRefresh(params)
	self:InitDungeonScene()
	--self.DungeonSelectList:CancelSel()
	self:InitSelectList(params)
	self.WBP_DungeonModeCom:RefreshTeamMode(false, false, StringConst.Get("DUNGEON_TEAM"), StringConst.Get("DUNGEON_SINGLE"))
	self.WBP_DungeonModeCom:RefreshDifficult(false, false, StringConst.Get("DUNGEON_NORMAL"), StringConst.Get("DUNGEON_HARD"))
	self.WBP_DungeonFixedRewardCom:SetText(StringConst.Get("DUNGEON_FIRST_PASS_REWARD"))
	self.WBP_DungeonFallRewardCom:SetText(StringConst.Get("DUNGEON_DROP_REWARD"))
	self.WBP_DungeonFirstPassCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	local SceneEntity = Game.SceneDisplayManager:GetSceneEntityByConfigID(Enum.ESceneDisplayEnum.DungeonSelect)
	self.SceneOriginalPostiion = DeepCopy(SceneEntity.Position)
end

function DungeonSelect_Panel:OnHide()
	Game.CinematicManager:StopPlayCinematic(self.BossDisplaySequenceParams)
end

function DungeonSelect_Panel:OnClose()
	if self.bossEntityIDList and next(self.bossEntityIDList) then
		for _, EntityID in pairs(self.bossEntityIDList) do
			Game.SceneDisplayManager:RemoveDisplayEntityByUID(self.DisplaySceneID, EntityID)
			Log.Debug("DungeonSelect_Panel RemoveDisplayEntityID: ", _, EntityID)
		end
	end
	table.clear(self.bossEntityIDList)
	Game.CinematicManager:StopPlayCinematic(self.BossDisplaySequenceParams)
	--[[
	local sceneEntity = Game.SceneDisplayManager:GetSceneEntityByID(self.DisplaySceneID)
	if sceneEntity then
		
	end
	
	local sceneActor = Game.SceneDisplayManager:GetSceneByID(self.DisplaySceneID)
	if sceneActor and IsValid_L(sceneActor) then
		local ActorId = Game.ObjectActorManager:GetIDByObject(sceneActor)
		Game.EffectManager:DestroyNiagarasBySpawnerId(ActorId)
	end
	]]--
	--Game.SceneDisplayManager:TerminateLevelSequence()
end

function DungeonSelect_Panel:InitSelectList(params)
	table.clear(self.DungeonEnterDataList)
	if params then
		if params.DungeonType then
			self.DungeonType = params.DungeonType
			self.DungeonID = params.DungeonID
		elseif params.DungeonEnterId then
			local dungeonEnterData = Game.TableData.GetDungeonEnterDataRow(params.DungeonEnterId)
			self.DungeonType = dungeonEnterData.Type
			self.DungeonID = dungeonEnterData.DungeonId[Game.DungeonSystem.EDungeonDifficultyType.None] or 
				dungeonEnterData.DungeonId[Game.DungeonSystem.EDungeonDifficultyType.Normal]
		elseif params[1] then
			local dungeonID = tonumber(params[1])
			if dungeonID then
				local dungeonTableData = Game.TableData.GetDungeonDataRow(dungeonID)
				self.DungeonType = dungeonTableData.Type
				self.DungeonID = dungeonID
			end
		end
	end
	self.DungeonEnterDataList = Game.DungeonSystem.GetDungeonList(self.DungeonType)
	self:InitDifficulty()
	--Log.Debug(self.DungeonType, self.DungeonID, #self.DungeonEnterDataList)
	self.WBP_ComMutiMenuNewCom:Refresh(self:GetMenuData())
	if self.DungeonID then
		for K, V in pairs(self.DungeonEnterDataList) do
			if V.DungeonEnterId == Game.TableData.Get_DungeonID2DungeonEnterID()[self.DungeonID] then
				self.selectedDungeonIndex = K
				self.WBP_ComMutiMenuNewCom:SetSelectedItemByPath(true, K)
			end
		end
	else
		self.WBP_ComMutiMenuNewCom:SetSelectedItemByIndex(1, true)
		self.selectedDungeonIndex = 1
	end
end

function DungeonSelect_Panel:GetMenuData()
	---@type UITreeViewData
	local treeViewData = UIComAccordionList.NewTreeViewData()
	for _, data in ipairs(self.DungeonEnterDataList) do
		local topData = UIComAccordionList.NewTabData(data.Name)
		topData.redPointId = "DungeonItem"
		topData.redPointSuff = self.DungeonType - 1
		treeViewData:AddFirstNode(topData)
	end
	return treeViewData
end

function DungeonSelect_Panel:InitDifficulty()
	for _, data in pairs(self.DungeonEnterDataList) do
		if data.DungeonId[Game.DungeonSystem.EDungeonDifficultyType.None] then
			self.DifficultyTypeDict[data.DungeonEnterId] = Game.DungeonSystem.EDungeonDifficultyType.None
		else
			self.DifficultyTypeDict[data.DungeonEnterId] = Game.DungeonSystem.EDungeonDifficultyType.Normal
		end
	end
end

function DungeonSelect_Panel:InitDungeonScene()
	self.CurveTime = 0
	self.DisplaySceneID = self.defaultSceneID
	--[[
	local scene = Game.SceneDisplayManager:GetSceneByID(self.DisplaySceneID)
	self.LightningIntensity = scene.LightningIntensity
	self.LightningRate = scene.LightningRate
	local components = scene:GetComponentsByTag(import("StaticMeshComponent"), "Skydome")
	if components and components:Length() > 0 then
		local skydome = components:Get(0)
		local skydomeMaterial = skydome:GetMaterial(0)
		if skydomeMaterial:IsA(import("MaterialInstanceDynamic")) then
			---@type MaterialInstanceDynamic
			self.SkydomeDMI = skydomeMaterial
		else
			self.SkydomeDMI = skydome:CreateDynamicMaterialInstance(0, skydomeMaterial, "")
		end
	end
	]]--
end

function DungeonSelect_Panel:UpdateDungeonInfo(dungeonEnterID, dungeonID)
	Log.Debug("DungeonSelect_Panel UpdateDungeonInfo: ", dungeonEnterID, dungeonID)
	local dungeonData = Game.TableData.GetDungeonDataRow(dungeonID)
	local dungeonEnterData = Game.TableData.GetDungeonEnterDataRow(dungeonEnterID)
	if not dungeonData or not dungeonEnterData then
		return
	end
	
	--刷新标题等固定信息
	self.WBP_DungeonSelectTitleAndIntroduceCom:Refresh(dungeonEnterData, dungeonData)
	self.view.KGText_Desc:SetText(dungeonEnterData.Desc)
	self.TipsTypeID = dungeonData.Type
	local TipsMap = Game.TableData.GetDungeonTypeDataRow(self.TipsTypeID)
	if TipsMap then
		self.view.WBP_ComPanel.WBP_ComBackTitle.Text_Title:SetText(TipsMap.Name)
	end
	
	-- 0、有难度有队伍，1、单队伍模式，2、单难度模式
	local SwitchMode = -1
	if self.DifficultyTypeDict[dungeonEnterID] ~= Game.DungeonSystem.EDungeonDifficultyType.None then
		SwitchMode = 2
		if dungeonData.AllowSingleMode then
			SwitchMode = 0
		else
			SwitchMode = 2
		end
	elseif dungeonData.AllowSingleMode then
		SwitchMode = 1
	end
	if SwitchMode >= 0 then
		self.view.WBP_DungeonMode:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.WBP_DungeonMode:SetMode(SwitchMode)
	else
		self.view.WBP_DungeonMode:SetVisibility(ESlateVisibility.Collapsed)
	end
	
	table.clear(self.FirstPassReward)
	self.FirstPassReward = Game.DungeonSystem.GetDisplayRewardInfo(dungeonID, { "FirstPassReward" })
	self.WBP_DungeonFixedRewardCom:Refresh(self.FirstPassReward)

	local fieldNames
	if dungeonData.AllowSingleMode and self.bSingleMode then
		fieldNames = { "PersonalBaseReward", "PersonalShapeReward", "PersonalEquipReward", "PersonalItemReward" }
	else
        local dungeonRewardConfig = Game.TableData.GetDungeonRewardDataRow(dungeonID)
        if ksbcnext(dungeonRewardConfig.TeamBaseReward) or ksbcnext(dungeonRewardConfig.TeamShapeReward) or 
            ksbcnext(dungeonRewardConfig.TeamEquipReward) or ksbcnext(dungeonRewardConfig.TeamItemReward) then
            fieldNames = { "TeamShapeReward", "TeamEquipReward", "TeamItemReward", "TeamExtraReward" }
        else
            fieldNames = {"PersonalShapeReward", "PersonalEquipReward", "PersonalItemReward", "TeamExtraReward" }
        end
	end
	if fieldNames then
		table.clear(self.DropReward)
		self.DropReward = Game.DungeonSystem.GetDisplayRewardInfo(dungeonID, fieldNames)
		self.WBP_DungeonFallRewardCom:Refresh(self.DropReward)
	end

	--阶段信息面板
	table.clear(self.StageInfoList)
	for K, V in ksbcipairs(dungeonData.StageList) do
		table.insert(self.StageInfoList, { StageID = V, DungeonID = dungeonID, StageIndex = K, bHandBook = false })
	end
	self.StageList:SetData(#self.StageInfoList)
	
	--Buff面板
	self:CheckUpdateBuffInfo(dungeonID)
	
	self.WBP_DungeonTeamStartCom:SetData({ dungeonId = dungeonID , dungeonMode = self.bSingleMode and 1 or 2})
	-- 不符合等级条件
	if not Game.ModuleLockSystem:CheckDungeonUnlockByID(dungeonID, true, self.view.WBP_DungeonTeamStart) then
		self.WBP_DungeonTeamStartCom:UpdateMatchingRelatedUI()
	end

	if dungeonData.FirstFinishTeam and dungeonData.FirstFinishTeam == 1 then
		Game.DungeonSystem.sender:ReqGetDungeonFirstPassageRecord(dungeonID)
	else
		self.WBP_DungeonFirstPassCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function DungeonSelect_Panel:CheckUpdateBuffInfo(dungeonID)
	if Game.DungeonSystem:GetFirstPassageRecord(dungeonID) then
		self:UpdateBuffInfo(dungeonID)
	else
		Game.DungeonSystem.sender:ReqGetDungeonFirstPassageRecord(dungeonID)
	end
end

function DungeonSelect_Panel:UpdateBuffInfo(dungeonID)
	self.BuffInfoList = Game.DungeonSystem:GetCurrentDisplayDungeonBuff(dungeonID, self:GetTeamMode(dungeonID))
	if #self.BuffInfoList > 0 then
		self.view.WBP_DungeonSelectTitleAndIntroduce.KHB_Buffs_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.WBP_DungeonSelectTitleAndIntroduceCom:UpdateBuffList(self.BuffInfoList)
	else
		self.view.WBP_DungeonSelectTitleAndIntroduce.KHB_Buffs_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function DungeonSelect_Panel:UpdateDungeonBossInfo(dungeonID, dungeonEnterId)
	local dungeonData = Game.TableData.GetDungeonDataRow(dungeonID)
	local dungeonEnterData = Game.TableData.GetDungeonEnterDataRow(dungeonEnterId)
	Log.Debug("DungeonSelect_Panel:UpdateDungeonBossInfo", dungeonID, dungeonEnterId, dungeonEnterData.DisplaySequence)
	if dungeonEnterData.DisplaySequence and string.len(dungeonEnterData.DisplaySequence) > 0 then
		self:Update3DBoss(dungeonID, dungeonData, dungeonEnterData)
	else
		self.view.Overlay_BG:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Overlay:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Canvas_Bg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if dungeonEnterData.AtmoPath then
			self:SetImage(self.view.Img_Dungeon, dungeonEnterData.AtmoPath)
		end
		self:PlayInAnimation()
	end
end

function DungeonSelect_Panel:Update3DBoss(DungeonID, DungeonData, dungeonEnterData)
	self.view.Overlay_BG:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Overlay:SetVisibility(ESlateVisibility.Hidden)
	self.view.WBP_ComMutiMenuNew:SetVisibility(ESlateVisibility.Hidden)
	Log.Debug("DungeonSelect_Panel Update3DBoss DungeonID: ", DungeonID, dungeonEnterData.DisplaySequence)
	for displaySequence, entityID in pairs(self.bossEntityIDList) do
		Log.Debug("DungeonSelect_Panel bossEntityIDList: ", displaySequence, entityID)
		if not self.bossEntityIDList[dungeonEnterData.DisplaySequence] or (self.bossEntityIDList[dungeonEnterData.DisplaySequence] and entityID ~= self.bossEntityIDList[dungeonEnterData.DisplaySequence]) then
			local Entity = Game.EntityManager:getEntity(entityID)
			if Entity then
				self:StartTimer("HideBoss", function()
					Entity.CppEntity:KAPI_Actor_SetActorHiddenInGame(true)
				end, 500, 1)
				Entity.CppEntity:KAPI_Actor_SetActorHiddenInGame(true)
			end
		end
	end
	--存一下对应boss的EID
	local BossEntityID = self.bossEntityIDList[dungeonEnterData.DisplaySequence]
	local BossEntity = nil
	local bPlay = true
	if not BossEntityID then
		Log.Debug("DungeonSelect_Panel not BossEntityID")
		BossEntity = Game.SceneDisplayManager:CreateDisplayMonsterEntityByFacadeID(self.defaultSceneID, "BossPreview", dungeonEnterData.FacadeControlID)
		self.bossEntityIDList[dungeonEnterData.DisplaySequence] = BossEntity:uid()
	else
		BossEntity = Game.EntityManager:getEntity(BossEntityID)
		BossEntity.CppEntity:KAPI_Actor_SetActorHiddenInGame(false)
		bPlay = false
	end
	Game.CinematicManager:StopPlayCinematic(self.BossDisplaySequenceParams)
	local cameraActorID = Game.SceneDisplayManager:RefreshCameraByTag(self.defaultSceneID, "SequenceCamera", 0)
	Log.Debug("DungeonSelect_Panel cameraActorID", cameraActorID)
	table.clear(self.BossDisplaySequenceParams)
	local CustomTagTable = {}
	CustomTagTable["Boss"] = { ["EntityID"] = BossEntity.eid }
	CustomTagTable["Camera"] = { ["CharacterID"] = cameraActorID }
	self.BossDisplaySequenceParams =
	{
		["AssetID"] = tonumber(dungeonEnterData.DisplaySequence),
		["CustomTagTable"] = CustomTagTable,
		["CinematicType"] = Enum.CinematicType.Cutscene,
		["OnCinematicFinished"] = function()
			self:OnBossDisplayFinished()
		end
	}
	if not BossEntity.bInWorld then
		Log.Debug("DungeonSelect_Panel not BossEntity.bInWorld")
		BossEntity.OnActorReady:Add(self, "OnBossReady")
	else
		self:OnBossReady(BossEntity)
	end
	if not bPlay then
		self.view.Overlay:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Overlay_BG:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.WBP_ComMutiMenuNew:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Canvas_Bg:SetVisibility(ESlateVisibility.Collapsed)
		--self:StopTimer("SequenceCameraDelay")
		--self:StartTimer("SequenceCameraDelay", function()
		--	--Game.SceneDisplayManager:RefreshCameraByTag(self.defaultSceneID, "SequenceCamera", 0)
		--end, 300, 1)
	end
end

function DungeonSelect_Panel:OnBossReady(entity)
	Log.Debug("DungeonSelect_Panel:OnBossReady")
	self:StartTimer("DungeonBossReady", function()
		Log.Debug("DungeonSelect_Panel:OnBossReady DungeonBossReadyTimer")
		local bossMeshID = entity.CppEntity:KAPI_Actor_GetMainMesh()
		entity.CppEntity:KAPI_SceneID_SetRelativeLocationAndRotation(bossMeshID, 0, 0, 0, 0, 0, 0)
		entity.CppEntity:KAPI_SceneID_SetRelativeScale3D(bossMeshID, FVector(1, 1, 1))
		Game.CinematicManager:StartPlayCinematic(self.BossDisplaySequenceParams)
	end, 50, 1)
	self:StartTimer("DungeonBossUIFadeIn", function()
		self.view.Overlay:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Overlay_BG:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.WBP_ComMutiMenuNew:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Canvas_Bg:SetVisibility(ESlateVisibility.Collapsed)
		self:PlayInAnimation()
	end, 1170, 1)
end

function DungeonSelect_Panel:OnBossDisplayFinished()
	Log.Debug("DungeonSelect_Panel:OnBossDisplayFinished")
	--SceneEntity:SetPosition_P(self.SceneOriginalPostiion[1], self.SceneOriginalPostiion[2], self.SceneOriginalPostiion[3])
	if Game.NewUIManager:IsShow(UIPanelConfig.DungeonSelect_Panel) then
		--Game.SceneDisplayManager:RefreshCameraByTag(self.defaultSceneID, "SequenceCamera", 0)
	end
end

function DungeonSelect_Panel:PlayInAnimation()
	self:PlayAnimation(self.view.Ani_VX_Fadein, function()
		--self.RewardPanel:getSize()
		--self.RewardPanel:SetData(#self.displayRewards, 1)
	end, self.userWidget, 1, 1, EUMGSequencePlayMode.Forward, 1, false)
	self:PlayAnimation(self.view.WBP_ComPanelRightN.Ani_Fadein, nil, self.view.WBP_ComPanelRightN)
end

function DungeonSelect_Panel:OnRefresh_HB_DungeonStage(widget, index, selected)
	local stageID = self.StageInfoList[index].StageID
	widget:OnSetData(self.StageInfoList[index], selected, index, function()
		--UI.ShowUI(UIPanelConfig.DungeonHandbook, { StageID = stageID, DungeonID = self.DungeonID })
		Game.NewUIManager:OpenPanel(UIPanelConfig.DungeonHandbook, { StageID = stageID, DungeonID = self.DungeonID })
	end) 
end

function DungeonSelect_Panel:OnRefresh_WBP_ComList(widget, index, selected)
	widget:OnSetData(self.BuffInfoList[index])
end

function DungeonSelect_Panel:OnDifficultyBtnSwitch(bOpen)
	self.bHardMode = bOpen
	self.view.WBP_DungeonMode.WBP_Difficult:Event_UI_Style(bOpen)
	if bOpen then
		self.DifficultyTypeDict[self.DungeonEnterDataList[self.selectedDungeonIndex].DungeonEnterId] = Game.DungeonSystem.EDungeonDifficultyType.Hard
	else
		self.DifficultyTypeDict[self.DungeonEnterDataList[self.selectedDungeonIndex].DungeonEnterId] = Game.DungeonSystem.EDungeonDifficultyType.Normal
	end
	local dungeonEnterId = self.DungeonEnterDataList[self.selectedDungeonIndex].DungeonEnterId
	self.DungeonID = self.DungeonEnterDataList[self.selectedDungeonIndex].DungeonId[self.DifficultyTypeDict[dungeonEnterId]]
	Log.Debug("DungeonSelect_Panel OnDifficultyBtnSwitch: ", self.DungeonID, self.selectedDungeonIndex, dungeonEnterId, self.DifficultyTypeDict[dungeonEnterId])
	--local enterDataCell = self.WBP_ComMutiMenuNewCom:GetItemByPath(self.selectedDungeonIndex)
	--enterDataCell:CheckLevel(self.DungeonID)
	self:UpdateDungeonInfo(dungeonEnterId, self.DungeonID)
end

function DungeonSelect_Panel:OnTeamModeBtnSwitch(bOpen)
	self.bSingleMode = bOpen
	local dungeonEnterId = self.DungeonEnterDataList[self.selectedDungeonIndex].DungeonEnterId
	self:UpdateDungeonInfo(dungeonEnterId, self.DungeonID)
end

function DungeonSelect_Panel:OpenDungeonRequest()
	Game.me:ReqOpenDungeon(self.DungeonID, self.bSingleMode and 1 or 2)
end

function DungeonSelect_Panel:OnCancelPVPMatch(Code)
	if Code == Game.ErrorCodeConst.NO_ERR then
		self:OpenDungeonRequest()
	end
end

function DungeonSelect_Panel:OnLevelChanged()
	local dungeonEnterId = self.DungeonEnterDataList[self.selectedDungeonIndex].DungeonEnterId
	self:UpdateDungeonInfo(dungeonEnterId, self.DungeonID)
end

function DungeonSelect_Panel:OnClickDungeonOpen()
	Game.CinematicManager:StopPlayCinematic(self.BossDisplaySequenceParams)
end

function DungeonSelect_Panel:OnGetFirstPassageRecord(record)
	self:UpdateBuffInfo(self.DungeonID)
	if record.time > 0 then
		self.WBP_DungeonFirstPassCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.WBP_DungeonFirstPassCom:SetData(record)
	end
end

-- 1：组队，2：人机
function DungeonSelect_Panel:GetTeamMode(dungeonID)
	--local dungeonData = Game.TableData.GetDungeonDataRow(dungeonID)
	local teamMode = self.bSingleMode and 1 or 2
	--if self.bHardMode and not dungeonData.AllowSingleMode then
	--	teamMode = 2
	--end
	return teamMode
end

--- 此处为自动生成
function DungeonSelect_Panel:on_WBP_ComPanelCom_TipClickEvent()
	local TipsMap = Game.TableData.GetDungeonTypeDataRow(self.TipsTypeID)
	if TipsMap and TipsMap.TipsID then
		Game.TipsSystem:ShowTips(TipsMap.TipsID, self.view.WBP_ComPanel.WBP_ComBackTitle.Btn_Tips:GetCachedGeometry())
	end
end

--- 此处为自动生成
---@param index number
---@param data UITreeViewChildData
---@param selected bool
function DungeonSelect_Panel:on_WBP_ComMutiMenuNewCom_ItemSelected(index, data, selected)
	Log.Debug("DungeonSelect_Panel:on_WBP_ComMutiMenuNewCom_ItemSelected", index, selected)
	local dungeonEnterId = self.DungeonEnterDataList[index].DungeonEnterId
	local dungeonID = self.DungeonEnterDataList[index].DungeonId[self.DifficultyTypeDict[dungeonEnterId]]
	if selected == true then
		Game.RedPointSystem:DeleteOnceRedPoint("DungeonItem", self.DungeonType - 1, self.DungeonEnterDataList[index].DungeonEnterId)
		self.selectedDungeonIndex = index
		self.DungeonID = dungeonID
		self:UpdateDungeonInfo(dungeonEnterId, self.DungeonID)
		self:UpdateDungeonBossInfo(self.DungeonID, dungeonEnterId)
		self.bSingleMode = false
		self.bHardMode = false
		self.WBP_DungeonModeCom:RefreshTeamMode(false, true, StringConst.Get("DUNGEON_TEAM"), StringConst.Get("DUNGEON_SINGLE"))
	end
end

--- 此处为自动生成
---@param isOn bool
function DungeonSelect_Panel:on_WBP_DungeonModeCom_TeamModeClickEvent(isOn)
	self:OnTeamModeBtnSwitch(isOn)
end

--- 此处为自动生成
---@param isOn bool
function DungeonSelect_Panel:on_WBP_DungeonModeCom_DifficultClickEvent(isOn)
	self:OnDifficultyBtnSwitch(isOn)
end

function DungeonSelect_Panel:OnDestroy()
end

return DungeonSelect_Panel
