local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class DungeonEntrance_Item : BaseListItemComponent
---@field view DungeonEntrance_ItemBlueprint
local DungeonEntrance_Item = DefineClass("DungeonEntrance_Item", UIListItem)
local StringConst = require "Data.Config.StringConst.StringConst"
local LimitConst = kg_require("Shared.Const.LimitConst")

DungeonEntrance_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DungeonEntrance_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonEntrance_Item:InitUIData()
	---副本选择入口id
	self.dungeonEnterId = nil
	---子ui名
	self.subUIName = nil
	---是否解锁
	self.bUnlock = nil
	---最小等级
	self.minimumLevel = nil
	---是否开放
	self.bAvailable = nil
end

--- UI组件初始化，此处为自动生成
function DungeonEntrance_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DungeonEntrance_Item:InitUIEvent()
    self:AddUIEvent(self.view.Button_lua.OnClicked, "on_Button_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonEntrance_Item:InitUIView()		
end

---面板打开的时候触发
function DungeonEntrance_Item:OnRefresh(data)
	self:SetData(data)
	Game.RedPointSystem:RegisterRedPoint(self.parentComponent, self.view, "DungeonList", self.index)
end

function DungeonEntrance_Item:SetData(data)
	self.bUnlock, self.minimumLevel, self.bAvailable = self:GetDungeonTypeUnlock(data.TypeID)
	self.userWidget:Event_UI_Select(not self.bUnlock)
	self.subUIName = data.SubUIName
	self.view.Text_Task_lua:SetText(data.TypeTag)
	for version, value in ksbcpairs(data.VersionDungeonDisplay) do
		if version == Game.SeasonSystem.GetSeasonId() then
			self.dungeonEnterId = value
			break
		end
	end
	local dungeonEnterData = Game.TableData.GetDungeonEnterDataRow(self.dungeonEnterId)
	local dungeonTypeData = Game.TableData.GetDungeonTypeDataRow(data.TypeID)
	if not self.bUnlock then
		if self.bAvailable then
			self.view.Text_Condition_lua:SetText(StringConst.Get("DUNGEON_UNLOCK_LEVEL", self.minimumLevel))
		else
			self.view.Text_Condition_lua:SetText(dungeonTypeData.NotAvailableText)
		end
	end
	if dungeonEnterData then
		self.view.Text_Name_lua:SetText(dungeonEnterData.Name)
		if dungeonEnterData.EnterPreviewBg and dungeonEnterData.EnterPreviewBg ~= "" then
			self:SetImage(self.view.Img_Bg_lua, dungeonEnterData.EnterPreviewBg)
		end
	end
	local usedSum, totalSum = Game.DungeonSystem.GetDungeonTypeLimitTimes(data.TypeID)
	local maxLimit = totalSum
	local stageIDs = Game.TableData.Get_DungeonType2StageIDs()[data.TypeID]
	if stageIDs then
		local _, stageID = ksbcnext(stageIDs)
		if stageID then
			local stageRewardRow = Game.TableData.GetDungeonRewardDataRow(stageID)
			if stageRewardRow.RewardLimit then
				if stageRewardRow.RewardLimit[1] == LimitConst.LIMIT_REFRESH_DAY then
					self.view.Text_RefreshTime:SetText(StringConst.Get("DUNGEON_REWARD_REFRESH_DAILY"))
				elseif stageRewardRow.RewardLimit[1] == LimitConst.LIMIT_REFRESH_WEEK then
					self.view.Text_RefreshTime:SetText(StringConst.Get("DUNGEON_REWARD_REFRESH_WEEKLY"))
				elseif stageRewardRow.RewardLimit[1] == LimitConst.LIMIT_REFRESH_MONTH then
					self.view.Text_RefreshTime:SetText(StringConst.Get("DUNGEON_REWARD_REFRESH_MONTHLY"))
				end
			end
		end
	end
	self.view.KText_RewardFirstNum:SetText(usedSum)
	self.view.KText_RewardSecondNum:SetText(string.format("/%d", totalSum))
	self.view.Text_MaxLimit:SetText(StringConst.Get("DUNGEON_TOTAL_REWARD_LIMIT", maxLimit))
end

function DungeonEntrance_Item:GetDungeonTypeUnlock(typeID)
	local dungeonDataTable = Game.TableData.GetDungeonDataTable()
	local dungeonTypeDataTable = Game.TableData.GetDungeonTypeDataTable()
	local isUnlock = false
	local minimumLevel = 100
	local isAvailable = true
	for _, value in ksbcpairs(dungeonTypeDataTable) do
		if value.TypeID == typeID then
			if value.IsAvailable == 0 then
				isAvailable = false
				return isUnlock, minimumLevel, isAvailable
			end
		end
	end
	for _, value in ksbcpairs(dungeonDataTable) do
		if value.Type == typeID then
			local level = Game.me.Level
			if (level >= value.MinLvlLimit and 0 == value.MaxLvlLimit) then
				isUnlock = true
				break
			elseif (level >= value.MinLvlLimit and level <= value.MaxLvlLimit) then
				isUnlock = true
				break
			else
				minimumLevel = minimumLevel > value.MinLvlLimit and value.MinLvlLimit or minimumLevel
			end
		end
	end
	return isUnlock, minimumLevel, isAvailable
end

--- 此处为自动生成
function DungeonEntrance_Item:on_Button_lua_Clicked()
	if not self.bUnlock then
		if self.bAvailable then
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FUNCTION_LOCK_LEVEL,
				{ { self.minimumLevel } })
		else
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.DUNGEON_TYPE_NOT_AVAILABLE)
		end
	else
		if self.subUIName == "P_TingenHistory" then
			UI.ShowUI(self.subUIName, {DungeonType = 4})
		else
			Game.NewUIManager:OpenPanel(self.subUIName, {DungeonEnterId = self.dungeonEnterId})
		end
	end
end

return DungeonEntrance_Item
