local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class DungeonFirstPass : UIComponent
---@field view DungeonFirstPassBlueprint
local DungeonFirstPass = DefineClass("DungeonFirstPass", UIComponent)
local StringConst = require("Data.Config.StringConst.StringConst")

DungeonFirstPass.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DungeonFirstPass:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonFirstPass:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DungeonFirstPass:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DungeonFirstPass:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonFirstPass:InitUIView()
end

---组件刷新统一入口
function DungeonFirstPass:Refresh(...)
end

function DungeonFirstPass:SetData(record)
	self.view.KText_TeamName:SetText(StringConst.Get("DUNGEON_FIRST_PASS_TEAM", record.leaderName))
	self.view.KText_Date:SetText(TimeUtils.FormatDateTimeString(record.time, "day"))
end

return DungeonFirstPass
