local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class DungeonStageIcon : UIComponent
---@field view DungeonStageIconBlueprint
local DungeonStageIcon = DefineClass("DungeonStageIcon", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local ItemConstSource = kg_require("Shared.ItemConstSource")

DungeonStageIcon.NumberToRomen = {
	[1] = "I",
	[2] = "II",
	[3] = "III",
	[4] = "IV",
	[5] = "V",
	[6] = "VI",
	[7] = "VII",
	[8] = "VIII",
	[9] = "IX",
	[10] = "X",
}

DungeonStageIcon.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DungeonStageIcon:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonStageIcon:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DungeonStageIcon:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DungeonStageIcon:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonStageIcon:InitUIView()
end

---面板打开的时候触发
function DungeonStageIcon:OnRefresh(...)
end

function DungeonStageIcon:OnSetData(params, selected, index, clickCallback)
	self.Params = params
	self.index = index
	self.clickCallback = clickCallback
	if self.Params.StageID then
		local StageData = Game.TableData.GetStageDataRow(self.Params.StageID)
		--self.Params.StageRewards = StageData.StageRewardDisplay
		local DungeonData = Game.TableData.GetDungeonDataRow(self.Params.DungeonID)
		self.Params.StageRealRewards = DungeonData.StagePersonalReward[self.Params.StageIndex]
		self:SetImage(self.view.Img_Icon, StageData.StageIcon)
		self.view.Text_Title:SetText(StageData.StageName)
		local used, total = Game.DropSystem.GetDropLimitTimes(ItemConstSource.ITEM_SOURCE_DUNGEON_STAGE_PERSONAL_REWARD,
			self.Params.StageID, "PersonalBaseReward")
		self.view.KText_PhaseUpNum:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.KText_Phase_DownNum:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.KText_Phase_DownNum:SetText(string.format("%d/%d", used, total))
		self.view.KText_PhaseUpNum:SetText(DungeonStageIcon.NumberToRomen[self.Params.StageIndex])
		if selected then
			self:PlayAnimation(self.View.Ani_Click, nil, self.userWidget)
			self.userWidget:Set_IconState(selected)
		else
			--self:SetWidgetToAnimationEndInstantly(self.View.WidgetRoot, self.View.Ani_Click)
			self:PlayAnimation(self.view.Ani_Click_end, nil, self.userWidget)
			self.userWidget:Set_IconState(false)
		end
	end
	self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

--- 此处为自动生成
function DungeonStageIcon:on_Btn_ClickArea_Clicked()
	if self.clickCallback then
		self.clickCallback(self.Params)
	end
end

return DungeonStageIcon
