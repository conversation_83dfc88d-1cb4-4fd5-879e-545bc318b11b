local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require "Data.Config.StringConst.StringConst"

---@class DungeonBtnSwitch : UIComponent
---@field view DungeonBtnSwitchBlueprint
local DungeonBtnSwitch = DefineClass("DungeonBtnSwitch", UIComponent)

DungeonBtnSwitch.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DungeonBtnSwitch:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonBtnSwitch:InitUIData()
	self.curValue = nil --开关当前的值
	---按钮点击事件
	---@type LuaDelegate<fun(isOn:bool)>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function DungeonBtnSwitch:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DungeonBtnSwitch:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickAreaBig_Button_ClickArea_lua.OnClicked, "on_Btn_ClickAreaBig_Button_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonBtnSwitch:InitUIView()
	self:Refresh(false, false)
end

---组件刷新统一入口
function DungeonBtnSwitch:Refresh(isOn, bNotify, leftText, rightText)
	self.view.text_close_lua:SetText(leftText or StringConst.Get("SWITCH_OFF"))
	self.view.text_open_lua:SetText(rightText or StringConst.Get("SWITCH_ON"))
	self:SetSwitch(isOn, bNotify)
end

function DungeonBtnSwitch:SetSwitch(isOn, bNotify)
	self.curValue = isOn
	if self.userWidget.BP_SetSwitch then
		self.userWidget:BP_SetSwitch(isOn)
	end
	if bNotify then
		self.onClickEvent:Execute(isOn)
	end
end

function DungeonBtnSwitch:GetValue()
	return self.curValue
end

--- 此处为自动生成
function DungeonBtnSwitch:on_Btn_ClickAreaBig_Button_ClickArea_lua_Clicked()
	self:SetSwitch(not self.curValue, true)
	if self.curValue then
		self:PlayAnimation(self.view.Ani_Switch_Open)
	else
		self:PlayAnimation(self.view.Ani_Switch_Close)
	end
end

return DungeonBtnSwitch
