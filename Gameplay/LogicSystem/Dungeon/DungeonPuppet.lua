local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class DungeonPuppet : UIComponent
---@field view DungeonPuppetBlueprint
local DungeonPuppet = DefineClass("DungeonPuppet", UIPanel)

DungeonPuppet.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DungeonPuppet:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonPuppet:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DungeonPuppet:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DungeonPuppet:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonPuppet:InitUIView()
end

---组件刷新统一入口
function DungeonPuppet:Refresh(...)
end

return DungeonPuppet
