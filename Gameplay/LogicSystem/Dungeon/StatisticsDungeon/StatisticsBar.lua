local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class StatisticsBar : UIComponent
---@field view StatisticsBarBlueprint
local StatisticsBar = DefineClass("StatisticsBar", UIComponent)

StatisticsBar.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function StatisticsBar:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function StatisticsBar:InitUIData()
end

--- UI组件初始化，此处为自动生成
function StatisticsBar:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function StatisticsBar:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function StatisticsBar:InitUIView()
end

---组件刷新统一入口
function StatisticsBar:Refresh(...)
end

---设置组件内容
function StatisticsBar:SetContent(Type, Value, Percentage)
	self.userWidget:Event_UI_Style(Type - 1)
	self.view.Text_Value:SetText(Game.DungeonBattleStatisticsSystem:GetFormatNumberString(Value))
	self.view.KText_Percentage:SetText((string.format("(%s)", Game.DungeonBattleStatisticsSystem:GetFormatPercentNumberString(Percentage))))
	self.view.ProgressBar:SetPercent(Percentage)
end

return StatisticsBar
