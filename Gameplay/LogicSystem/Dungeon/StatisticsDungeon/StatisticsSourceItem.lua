local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class StatisticsSourceItem : UIListItem
---@field view StatisticsSourceItemBlueprint
local StatisticsSourceItem = DefineClass("StatisticsSourceItem", UIListItem)
local const = kg_require("Shared.Const")
local ESlateVisibility = import("ESlateVisibility")

StatisticsSourceItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function StatisticsSourceItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function StatisticsSourceItem:InitUIData()
	---默认情况下的技能图标渲染倍率
	self.DefaultRenderScale = FVector2D(0.4,0.4)
	---Buff图标的渲染倍率
	self.EnlargedRenderScale = FVector2D(self.DefaultRenderScale.X * 1.5, self.DefaultRenderScale.Y * 1.5)
end

--- UI组件初始化，此处为自动生成
function StatisticsSourceItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function StatisticsSourceItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function StatisticsSourceItem:InitUIView()
end

---面板打开的时候触发
function StatisticsSourceItem:OnRefresh(params, showbg)
	if params ~= nil then
		local type = params.SkillType or params.Type
		if type == const.BATTLE_STATISTIC_SKILL_TYPE.ELEMENT_EFFECT then
			local elementData = Game.TableData.GetElementEffectsDataRow(params.SkillId)
			if elementData then
				local iconPath = Game.UIIconUtils.GetIconByEleEffectId(params.SkillId)
				if iconPath then
					self:SetImage(self.view.Img_SkillIcon, iconPath)
				end
				local PropString = Game.TableData.GetElementTypeDefDataRow(elementData.ElementType).CoreTypeText
				self.view.Text_SkillName:SetText(PropString)
			end
		elseif type == const.BATTLE_STATISTIC_SKILL_TYPE.SPECIAL_BUFF then
			local buffData = Game.TableData.GetBuffDataNewRow(params.SkillId)
			if buffData then
				local iconPath = buffData.BuffIcon
				if StringValid(iconPath) then
					self:SetImage(self.view.Img_SkillIcon, iconPath)
				end
				local PropString = buffData.BuffName
				self.view.Text_SkillName:SetText(PropString)
			end
		else
			local SkillData = Game.TableData.GetSkillDataNewTable()
			local skillinfo = SkillData[params.SkillId]
			if skillinfo then
				if not string.isEmpty(skillinfo.SkillIcon) then
					self:SetImage(self.view.Img_SkillIcon, skillinfo.SkillIcon)
				end
				self.view.Text_SkillName:SetText(skillinfo.Name)
			end
		end
		self.view.Text_HitPercent:SetText(tostring(params.CastTimes))
		if params.Type == 1 then
			self.view.Text_HealPercent:SetText(string.format("(%s)", Game.DungeonBattleStatisticsSystem:GetFormatPercentNumberString(params.DamagePercent)))
			self.view.Text_HealNum:SetText(Game.DungeonBattleStatisticsSystem:GetFormatNumberString(params.TotalDamage))
		else
			self.view.Text_HealPercent:SetText(string.format("(%s)", Game.DungeonBattleStatisticsSystem:GetFormatPercentNumberString(params.HealingPercent)))
			self.view.Text_HealNum:SetText(Game.DungeonBattleStatisticsSystem:GetFormatNumberString(params.TotalHealing))
		end
	end
	self.view.BG:SetVisibility(showbg and ESlateVisibility.Visible or ESlateVisibility.Hidden)
end

return StatisticsSourceItem
