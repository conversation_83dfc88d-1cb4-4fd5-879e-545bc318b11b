local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class StatisticsBtnSequence : UIComponent
---@field view StatisticsBtnSequenceBlueprint
local StatisticsBtnSequence = DefineClass("StatisticsBtnSequence", UIComponent)

StatisticsBtnSequence.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function StatisticsBtnSequence:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function StatisticsBtnSequence:InitUIData()
	--自身对应的排序类型
	self.SortType = Game.DungeonBattleStatisticsSystem.SortType.TotalDamage
	--自身状态
	self.SortStatus = 0
	--状态切换
	self.StatusMap = 
	{
		[0] = 2,
		[1] = 2,
		[2] = 1
	}
end

--- UI组件初始化，此处为自动生成
function StatisticsBtnSequence:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function StatisticsBtnSequence:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function StatisticsBtnSequence:InitUIView()
end

---组件刷新统一入口
function StatisticsBtnSequence:Refresh(...)
end

---初始化组件
function StatisticsBtnSequence:SetSortType(InType)
	self.SortStatus = 0
	self.SortType = InType
end

---设置状态
function StatisticsBtnSequence:SetStatus(InStatus)
	self.SortStatus = InStatus
	self.userWidget:Event_UI_Style(InStatus)
end

function StatisticsBtnSequence:SetText(content)
	self.view.KText_StatisticsKind:SetText(content)
end

--- 此处为自动生成
function StatisticsBtnSequence:on_Btn_ClickArea_Clicked()
	self.SortStatus = self.StatusMap[self.SortStatus]
	self.parentComponent:UpdateSorting(self.SortStatus ~= 0 and self.SortType or nil, self.SortStatus == 2 and true)
	self:PlayAnimation(self.userWidget.Ani_Press)
end

return StatisticsBtnSequence
