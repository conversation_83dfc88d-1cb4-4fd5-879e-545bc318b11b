---@class WBP_ComBtnBackNewView : WBP_ComBtnBackNew_C
---@field public WidgetRoot WBP_ComBtnBackNew_C
---@field public Btn_Back C7Button
---@field public Text_Back C7TextBlock
---@field public Btn_Info C7Button
---@field public Icon Image
---@field public Ani_Press WidgetAnimation
---@field public TitleName_lua string
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public IsBtnBackNew boolean
---@field public BndEvt__WBP_ComBtnBackNew_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public GetBrush_0 fun(self:self):SlateBrush


---@class WBP_ComPanelView : WBP_ComPanel_C
---@field public WidgetRoot WBP_ComPanel_C
---@field public NS_BackGround NamedSlot
---@field public HorizontalBox HorizontalBox
---@field public NS_TabList NamedSlot
---@field public NS_TabListLv1 NamedSlot
---@field public NS_TabListLv2 NamedSlot
---@field public NS_ComPanel NamedSlot
---@field public Money NamedSlot
---@field public WBP_ComBtnBack WBP_ComBtnBackNewView
---@field public Title Text string
---@field public TabType number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetTabList fun(self:self,TabType:number):void


---@class WBP_ComListView : WBP_ComList_C
---@field public WidgetRoot WBP_ComList_C
---@field public List ScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public bIsTileView boolean
---@field public PreviewCount number
---@field public LibWidget ListLib
---@field public ScrollWidget Widget
---@field public Orientation EOrientation
---@field public ScrollBarVisibility ESlateVisibility
---@field public SelectionMode number
---@field public Space ListSpace
---@field public Alignment ComListAligment
---@field public bIsCenterContent boolean
---@field public tempIndex number
---@field public oldPosX number
---@field public oldPosY number
---@field public tempPosX number
---@field public tempPosY number
---@field public widgetX number
---@field public widgetY number
---@field public spaceUp number
---@field public spaceBottom number
---@field public spaceLeft number
---@field public spaceRight number
---@field public bSizeToContent boolean
---@field public ListPadding Margin
---@field public MaxValue number
---@field public RetainerBox RetainerBox
---@field public OnSetItem MulticastDelegate
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public CalculatePos fun(self:self):void
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number):void
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public GetListSize fun(self:self):number,number,number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number,number,number,number,number
---@field public VerticalTileChange fun(self:self):void
---@field public VerticalTile fun(self:self):void
---@field public VerticalList fun(self:self):void
---@field public HorizontalTileChange fun(self:self):void
---@field public HorizontalTile fun(self:self):void
---@field public HorizontalList fun(self:self):void
---@field public VerticalTileAuto fun(self:self):void
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidfget:Widget):void


---@class WBP_ComBtnIconNewView : WBP_ComBtnIconNew_C
---@field public WidgetRoot WBP_ComBtnIconNew_C
---@field public OutCanvas CanvasPanel
---@field public Icon Image
---@field public Text_Name TextBlock
---@field public Big_Button_ClickArea C7Button
---@field public Anim_1 WidgetAnimation
---@field public Anim_2 WidgetAnimation
---@field public Anim_3 WidgetAnimation
---@field public Anim_4 WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public Ani_Hover WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public Btn Style ST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound SlateSound
---@field public Top number
---@field public Event_UI_Style fun(self:self,BtnName:string):void
---@field public Play Hint Anim fun(self:self):void
---@field public BndEvt__WBP_ComBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Btn Style fun(self:self,Btn Style:ST_ComBtnIcon):void


---@class WBP_TeamGroupIconItemView : WBP_TeamGroupIconItem_C
---@field public WidgetRoot WBP_TeamGroupIconItem_C
---@field public Text_Num TextBlock
---@field public State number
---@field public SizeBg Vector2D
---@field public In Width Override number
---@field public IconSize Vector2D
---@field public Event_UI_Style fun(self:self,State:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_CharactorHeadListView : WBP_CharactorHeadList_C
---@field public WidgetRoot WBP_CharactorHeadList_C
---@field public WBP_ComBtnInvitation WBP_ComBtnIconNewView
---@field public HB_TeamMemberList HorizontalBox
---@field public WBP_GroupPos WBP_TeamGroupIconItemView
---@field public WBP_GroupPos1 WBP_TeamGroupIconItemView
---@field public WBP_GroupPos2 WBP_TeamGroupIconItemView
---@field public Is Group boolean
---@field public Event_UI_Style fun(self:self,Is Group:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public Text_Com C7TextBlock
---@field public Text_Time TextBlock
---@field public Image C7Image
---@field public Btn_Com C7Button
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Fadein_Light WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Construct fun(self:self):void
---@field public OnVisibilityChangedEvent fun(self:self,InVisibility:ESlateVisibility):void
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_DungeonTeamStartView : WBP_DungeonTeamStart_C
---@field public WidgetRoot WBP_DungeonTeamStart_C
---@field public WS_DungeonLock VerticalBox
---@field public WBP_ComHeadList WBP_CharactorHeadListView
---@field public WBP_ComBtnIconText WBP_ComBtnIconNewView
---@field public WidgetSwitcher CanvasPanel
---@field public WS_MatchState CanvasPanel
---@field public WBP_DungeonQuickMatch WBP_ComBtnView
---@field public WBP_DungeonCancelMatch WBP_ComBtnView
---@field public WBP_EnterDungeon WBP_ComBtnView
---@field public Text_LevelLimit C7TextBlock
---@field public SwitcherText C7TextBlock
---@field public Num number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Event_UI_Ready fun(self:self,Num:number):void


---@class WBP_TingenHistoryView : WBP_TingenHistory_C
---@field public WidgetRoot WBP_TingenHistory_C
---@field public WBP_ComPanel WBP_ComPanelView
---@field public Text_Name C7TextBlock
---@field public Text_Info C7TextBlock
---@field public TB_RewardRemian C7TextBlock
---@field public Text_Checked C7TextBlock
---@field public List_Item WBP_ComListView
---@field public WBP_DungeonTeamStart WBP_DungeonTeamStartView
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetFontMaterial fun(self:self,Target:TextBlock):void

---@class P_TingenHistoryView : WBP_TingenHistoryView
---@field public controller P_TingenHistory
local P_TingenHistoryView = DefineClass("P_TingenHistoryView", UIView)

function P_TingenHistoryView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPanel.WBP_ComBtnBack.Btn_Back, "OnClick_ComBtnBack")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPanel.WBP_ComBtnBack.Btn_Info, "OnClick_ComBtnInfo")
    self.WBP_ComPanel.WBP_ComBtnBack.Btn_Info:SetVisibility(import("ESlateVisibility").Visible) -- luacheck: ignore
end

return P_TingenHistoryView
