local P_Head = kg_require "Gameplay.LogicSystem.Character.P_Head2"

---@class P_DungeonReadyItem:UIComponent
local P_DungeonReadyItem = DefineClass("P_DungeonReadyItem", UIComponent)

function P_DungeonReadyItem:SetIsReady(bReady)
    self.View:SetReadyState(bReady)
end

function P_DungeonReadyItem:Refresh(params, selected)
    self.Params = params
    if self.Params then
        self:SetIsReady(self.Params.bReady)
        local TeamMemberData
        if Game.TeamSystem:IsInGroup() then
            TeamMemberData = Game.GroupSystem:GetGroupMemberDetail(self.Params.ID)
        else
            TeamMemberData = Game.TeamSystem:GetTeamMember(self.Params.ID)
        end
        if TeamMemberData then
            self.View.TB_MemberName:SetText(TeamMemberData.name)
            self.P_Head:Refresh(
                {
                    Level = TeamMemberData.level or TeamMemberData.lv,
                    ProfessionID = TeamMemberData.profession,
                    bIsCaptain = TeamMemberData.isCaptain or TeamMemberData.bTeamLeader
                }
            )
        end
    end
end

function P_DungeonReadyItem:OnCreate()
    self.Params = nil
    self.P_Head = self:BindComponent(self.View.WBP_ComHead, P_Head) --头像绑定Component
end

return P_DungeonReadyItem
