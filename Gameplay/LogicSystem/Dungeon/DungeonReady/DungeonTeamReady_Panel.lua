local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local DungeonTeamReadyItem = kg_require("Gameplay.LogicSystem.Dungeon.DungeonReady.DungeonTeamReadyItem")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local NewGroupView = kg_require("Framework.UI.List.NewList.NewGroupView")
local StringConst = require("Data.Config.StringConst.StringConst")
local EKGAnimatedProgressBarSpeedType = import("EKGAnimatedProgressBarSpeedType")
---@class DungeonTeamReady_Panel : UIPanel
---@field view DungeonTeamReady_PanelBlueprint
local DungeonTeamReady_Panel = DefineClass("DungeonTeamReady_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")

DungeonTeamReady_Panel.eventBindMap = {
	[EEventTypesV2.DUNGEON_ON_MEMBER_ACCEPT_READY] = "OnMemberAccept",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DungeonTeamReady_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonTeamReady_Panel:InitUIData()
	self.DungeonReadyListData = nil
	self.StartTime = nil
	self.EndTime = nil
end

--- UI组件初始化，此处为自动生成
function DungeonTeamReady_Panel:InitUIComponent()
    ---@type UIComDiyTitle
    self.TB_DungeonNameCom = self:CreateComponent(self.view.TB_DungeonName, UIComDiyTitle)
    ---@type UIComButton
    self.WBP_BtnAcceptCom = self:CreateComponent(self.view.WBP_BtnAccept, UIComButton)
    ---@type UIComButton
    self.WBP_BtnRejectCom = self:CreateComponent(self.view.WBP_BtnReject, UIComButton)
	
	self.DungeonReadyList = self:CreateComponent(self.view.HB_TeamList, NewGroupView, DungeonTeamReadyItem)
end

---UI事件在这里注册，此处为自动生成
function DungeonTeamReady_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_BtnRejectCom.onClickEvent, "on_WBP_BtnRejectCom_ClickEvent")
    self:AddUIEvent(self.WBP_BtnAcceptCom.onClickEvent, "on_WBP_BtnAcceptCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonTeamReady_Panel:InitUIView()
	self.view.KText_TeamReady:SetText(StringConst.Get("DUNGEON_MATCH_READYCONFIRM"))
end

---面板打开的时候触发
function DungeonTeamReady_Panel:OnRefresh(params)
	if Game.DungeonSystem.bDungeonReadyAccepted then
		self.WBP_BtnAcceptCom:SetName(StringConst.Get("RETRACT"))
	else
		self.WBP_BtnAcceptCom:SetName(StringConst.Get("CONFIRM"))
	end
	self.WBP_BtnRejectCom:SetName(StringConst.Get("REJECT"))
	
	self.DungeonReadyListData = Game.DungeonSystem.DungeonReadyData
	self.EndTime = params.EndTime
	local dungeonData = Game.TableData.GetDungeonDataRow(params.DungeonTemplateID)
	if dungeonData and self.DungeonReadyListData then
		self.TB_DungeonNameCom:Refresh(dungeonData.Name)
		self:SetImage(self.view.Img_Shaow2, dungeonData.PagePath)

		self.DungeonReadyList:SetData(#self.DungeonReadyListData)
		self:StartProgressTimer()

		if Game.TeamSystem:IsInGroup() then
			if Game.GroupSystem:IsGroupLeader(Game.me.eid) and not Game.DungeonSystem.bDungeonReadyAccepted then
				self:onBtnAcceptClicked()
			end
		else
			if Game.me and self.DungeonReadyListData[1] and self.DungeonReadyListData[1].ID 
				and Game.me.eid == self.DungeonReadyListData[1].ID and not Game.DungeonSystem.bDungeonReadyAccepted then
				self:onBtnAcceptClicked()
			end
		end
	end

	-- 如果处于跟随状态则自动确认
	local FollowState = GetMainPlayerPropertySafely("FollowState")
	if FollowState ~= Enum.EFollowState.STOP_FOLLOW then
		self:onBtnAcceptClicked()
	end
	
	Game.HUDSystem:InvokeSubHUDFunc("P_HUDPVECountDown", "OnReadyPageShow")
end

function DungeonTeamReady_Panel:StartProgressTimer()
	self.view.WBP_BtnAccept.Text_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.WBP_BtnAcceptCom:SetExtraText(string.format("(%s)", self.EndTime - _G._now() // 1000))
	self:StartTimer(
		"ReadyCountDown",
		function()
			self:OnTimer()
		end,
		1000,
		-1,
		false,
		true
	)
	self.view.PB_AnimTime:SetPercentInstant((self.EndTime - _G._now(1)) / Game.TableData.GetConstDataRow("DUNGEON_CONFIRM_TIME"))
	self.view.PB_AnimTime:SetSpeed(EKGAnimatedProgressBarSpeedType.TimeSecond, 0, Game.TableData.GetConstDataRow("DUNGEON_CONFIRM_TIME"))
	self.view.PB_AnimTime:SetPercentWithBlend(0)
end

function DungeonTeamReady_Panel:OnTimer()
	if self.EndTime - _G._now(1) <= 0 then
		self:StopTimer("ReadyCountDown")
		self.view.WBP_BtnAccept.Text_Time:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	self.WBP_BtnAcceptCom:SetExtraText(string.format("(%s)", math.floor(self.EndTime - _G._now(1))))
end

function DungeonTeamReady_Panel:OnMemberAccept(memberID)
	self.DungeonReadyList:SetData(#self.DungeonReadyListData)
	if Game.me.eid == memberID then
		Game.DungeonSystem.bDungeonReadyAccepted = true
		self.WBP_BtnAcceptCom:SetName(StringConst.Get("RETRACT"))
	end
end


function DungeonTeamReady_Panel:OnRefresh_HB_TeamList(widget, index, selected)
	widget:SetData(self.DungeonReadyListData[index], selected)
end

function DungeonTeamReady_Panel:onBtnAcceptClicked()
	if Game.DungeonSystem.bDungeonReadyAccepted then
		Game.DungeonSystem.HideTeamReady()
	else
		if Game.TeamSystem:IsInGroup() then
			Game.DungeonSystem.sender:ChangeGroupCheckDungeonReadyState(true)
		else
			Game.me:ReqHandleReadinessCheck(Enum.EDungeonReadinessOperate.ACCEPT)
		end
	end
end

function DungeonTeamReady_Panel:onBtnRejectClicked()
	if Game.TeamSystem:IsInGroup() then
		Game.DungeonSystem.sender:ChangeGroupCheckDungeonReadyState(false)
	else
		Game.me:ReqHandleReadinessCheck(Enum.EDungeonReadinessOperate.REJECT)
	end
end

--- 此处为自动生成
function DungeonTeamReady_Panel:on_WBP_BtnRejectCom_ClickEvent()
	self:onBtnRejectClicked()
end

--- 此处为自动生成
function DungeonTeamReady_Panel:on_WBP_BtnAcceptCom_ClickEvent()
	self:onBtnAcceptClicked()
end

return DungeonTeamReady_Panel
