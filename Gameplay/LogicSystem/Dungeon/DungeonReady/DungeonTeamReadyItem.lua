local BaseListItemComponent = kg_require("Framework.UI.List.NewList.BaseListItemComponent")
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local P_Head = kg_require "Gameplay.LogicSystem.Character.P_Head2"
---@class DungeonTeamReadyItem : BaseListItemComponent
---@field view DungeonTeamReadyItemBlueprint
local DungeonTeamReadyItem = DefineClass("DungeonTeamReadyItem", BaseListItemComponent)

DungeonTeamReadyItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DungeonTeamReadyItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DungeonTeamReadyItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DungeonTeamReadyItem:InitUIComponent()
	self.Head = self:CreateComponent(self.view.WBP_ComHead, UIBaseAdapter, "DungeonTeamReadyItem", P_Head) --头像绑定Component
end

---UI事件在这里注册，此处为自动生成
function DungeonTeamReadyItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DungeonTeamReadyItem:InitUIView()
end

---面板打开的时候触发
function DungeonTeamReadyItem:OnRefresh(...)
end

function DungeonTeamReadyItem:SetData(params)
	self.Params = params
	if self.Params then
		local TeamMemberData
		if Game.TeamSystem:IsInGroup() then
			TeamMemberData = Game.GroupSystem:GetGroupMemberDetail(self.Params.ID)
		else
			TeamMemberData = Game.TeamSystem:GetTeamMember(self.Params.ID)
		end
		if not TeamMemberData then
			if Game.me.teamInfoList then
				for K, V in pairs(Game.me.teamInfoList) do
					Log.WarningFormat("Member:%s", V.id)
				end
			end
			Log.WarningFormat("DUNGEON PREPARE: UNALE TO FIND PLAYER, CurrentPlayer: %s", Game.me.eid)
			Log.WarningFormat("ID: %s", self.Params.ID)
			Log.WarningFormat("%s", Game.TeamSystem:IsInGroup())
			Log.WarningFormat("%s", self.Params.bReady)
			return
		end
		self:SetIsReady(self.Params.bReady, TeamMemberData.id == Game.me.eid)
		if TeamMemberData then
			self.view.TB_MemberName:SetText(TeamMemberData.name)
			self.Head:GetUIBase():Refresh(
				{
					Level = TeamMemberData.level or TeamMemberData.lv,
					ProfessionID = TeamMemberData.profession,
					bIsCaptain = TeamMemberData.isCaptain or TeamMemberData.bTeamLeader
				}
			)
		end
	end
end

function DungeonTeamReadyItem:SetIsReady(bReady, bMainPlayer)
	self.userWidget:SetReadyState(bReady, bMainPlayer)
	if bReady then
		Log.Debug("Stop Animation")
		self:StopAnimation(self.view.Ani_Loop, self.userWidget)
	else
		Log.Debug("Play Animation")
		self:PlayAnimation(self.view.Ani_Loop, nil, self.userWidget, 0, 0)
	end
end

return DungeonTeamReadyItem
