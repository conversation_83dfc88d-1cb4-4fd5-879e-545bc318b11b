local EKGAnimatedProgressBarSpeedType = import("EKGAnimatedProgressBarSpeedType")

local P_DungeonReadyItem = kg_require "Gameplay.LogicSystem.Dungeon.DungeonReady.P_DungeonReadyItem"
local NetWorkManager = require("Framework.DoraSDK.NetworkManager")
local StringConst = require "Data.Config.StringConst.StringConst"
local P_DungeonReady = DefineClass("P_DungeonReady", UIController)

function P_DungeonReady:OnReject()
    if Game.TeamSystem:IsInGroup() then
        Game.DungeonSystem.sender:ChangeGroupCheckDungeonReadyState(false)
    else
        Game.me:ReqHandleReadinessCheck(Enum.EDungeonReadinessOperate.REJECT)
    end
end

function P_DungeonReady:OnAccept()
    if Game.DungeonSystem.bDungeonReadyAccepted then
        Game.DungeonSystem.HideTeamReady()
    else
        if Game.TeamSystem:IsInGroup() then
            Game.DungeonSystem.sender:ChangeGroupCheckDungeonReadyState(true)
        else
            Game.me:ReqHandleReadinessCheck(Enum.EDungeonReadinessOperate.ACCEPT)
        end
    end
end

function P_DungeonReady:StartTImer()
    self.View.PB_AnimTime:SetPercentInstant((self.EndTime - _G._now() // 1000) / Game.TableData.GetConstDataRow("DUNGEON_CONFIRM_TIME"))
    self.View.PB_AnimTime:SetSpeed(EKGAnimatedProgressBarSpeedType.TimeSecond, 0,
        Game.TableData.GetConstDataRow("DUNGEON_CONFIRM_TIME"))
    self.View.PB_AnimTime:SetPercentWithBlend(0)
end

P_DungeonReady.eventBindMap = {
	[EEventTypesV2.DUNGEON_ON_MEMBER_ACCEPT_READY] = "OnMemberAccept",
}

function P_DungeonReady:OnCreate()
    self.PanelData = nil
    self.StartTime = nil
    self.EndTime = nil

    -- 创建一组对象
    self.TeamListPanel = BaseList.CreateList(self, BaseList.Kind.GroupView, "HB_TeamList", P_DungeonReadyItem)


    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_BtnAccept.Btn_Com, self.OnAccept)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_BtnReject.Btn_Com, self.OnReject)
end

---@param MemberID string
function P_DungeonReady:OnMemberAccept(MemberID)
    self.TeamListPanel:SetData(#self.PanelData)
    local avatarActor = NetWorkManager.GetLocalAvatarActor()
    if avatarActor.eid == MemberID then
        Game.DungeonSystem.bDungeonReadyAccepted = true
        self.View.WBP_BtnAccept.Text_Com:SetText(StringConst.Get("RETRACT"))
        -- self.View.WS_ShowHide:SetActiveWidgetIndex(avatarActor.DungeonReadinessPanelState)
    end
end

-- 刷新一组格子
function P_DungeonReady:OnRefresh_HB_TeamList(r, index, selected)
    --刷新格子数据
    r:Refresh(self.PanelData[index], selected)
end

function P_DungeonReady:OnOpen()
    self:ShowAnimation()
end

function P_DungeonReady:OnRefresh(Params)
    self:UpdateUI(Params)
	Game.HUDSystem:InvokeSubHUDFunc("P_HUDPVECountDown", "OnReadyPageShow")
end

function P_DungeonReady:UpdateUI(Params)
    self.View.WBP_ComPopupFull.WBP_ComMask:SetVisibility(import("ESlateVisibility").Collapsed) -- luacheck: ignore
    if Game.DungeonSystem.bDungeonReadyAccepted then
        self.View.WBP_BtnAccept.Text_Com:SetText(StringConst.Get("RETRACT"))
    else
        self.View.WBP_BtnAccept.Text_Com:SetText(StringConst.Get("CONFIRM"))
    end
    self.View.WBP_BtnReject.Text_Com:SetText(StringConst.Get("REJECT"))

    self.PanelData = Game.DungeonSystem.DungeonReadyData

    self.EndTime = Params.EndTime
    local DungeonData = Game.TableData.GetDungeonDataRow(Params.DungeonTemplateID)
    if DungeonData and self.PanelData then
        self.View.TB_DungeonName:SetText(DungeonData.Name)
        self:SetImage(self.View.Img_Shaow2, DungeonData.PagePath)

        self.TeamListPanel:SetData(#self.PanelData)
        self:StartTImer()

        if Game.TeamSystem:IsInGroup() then
            if Game.GroupSystem:IsGroupLeader(Game.me.eid) and not Game.DungeonSystem.bDungeonReadyAccepted then
                self:OnAccept()
            end
        else
            if Game.me and self.PanelData[1] and self.PanelData[1].ID and Game.me.eid == self.PanelData[1].ID and not Game.DungeonSystem.bDungeonReadyAccepted then
                self:OnAccept()
            end
        end
    end

    -- 如果处于跟随状态则自动确认
    local FollowState = GetMainPlayerPropertySafely("FollowState")
    if FollowState ~= Enum.EFollowState.STOP_FOLLOW then
        self:OnAccept()
    end
end

function P_DungeonReady:OnClose()
    UIBase.OnClose(self)
    self.StartTime = nil
end

function P_DungeonReady:MarkForExit()
    self:CloseSelf()
end

function P_DungeonReady:ShowAnimation()
end

return P_DungeonReady
