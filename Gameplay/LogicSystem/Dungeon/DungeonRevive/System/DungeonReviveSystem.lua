---@class DungeonReviveSystem : LuaClass
local DungeonReviveSystem = DefineClass("DungeonReviveSystem", SystemBase)
local Const = kg_require("Shared.Const")

function DungeonReviveSystem:onCtor()
end

-- 失控按钮
DungeonReviveSystem.LOSE_CONTROL_BTN = 6 

---检查死亡复活流程是否需要客户端触发
function DungeonReviveSystem:CheckIsSeverControl()
    local LocalSpace = Game.NetworkManager.GetLocalSpace()
    if LocalSpace and LocalSpace.eid then
        local levelData = Game.TableData.GetLevelMapDataRow(LocalSpace.TemplateID)
        local DeathConfigID = levelData and levelData.RebirthTypeId
        local DeathReviveConfigData = Game.TableData.GetDeathReviveDataRow(DeathConfigID)
        if DeathReviveConfigData then
            return DeathReviveConfigData.IsSeverControl
        end
    end
    return false
end

--检测当前场景是否有死亡复活限制
function DungeonReviveSystem:CheckSpaceReviveLimit()
	local localSpace = Game.NetworkManager.GetLocalSpace()
	if localSpace and localSpace.eid then
		if localSpace.ReviveLimitType == Const.SPACE_REVIVE_LIMIT_TYPE.SYSTEM or localSpace.ReviveLimitType == Const.SPACE_REVIVE_LIMIT_TYPE.ALL then
			return true
		end
	end
	return false
end

---显示死亡复活界面
function DungeonReviveSystem:ShowReviveUI()
    ---检查是否需要走客户端死亡复活流程
    if self:CheckIsSeverControl() then
        return
    end

	--- 检测当前的场景是否有死亡复活限制
	if self:CheckSpaceReviveLimit() then
		return
	end
	
    UI.ShowUI('P_DungeonRevive')
end

--- 死亡复活限制类型属性监听
function DungeonReviveSystem:OnReviveLimitTypeChange(newValue)
	if newValue and Game.me and Game.me.IsDead then
		self:ShowReviveUI()
	end
end

---关闭死亡复活界面
function DungeonReviveSystem:CloseReviveUI()
	self:SetMainPlayerLoseControl(false)
	if not UI.IsShow('P_DungeonRevive') then
		return
	end
    UI.HideUI('P_DungeonRevive')
end

function DungeonReviveSystem:UpdateRoleBornDeadState(NewValue)
    local ui = UI.GetUI("ScreenInput_Panel")
    if not ui then
        return
    end
    ---true: 死了， false:复活了
    if NewValue then
        UI.Invoke("ScreenInput_Panel", "OnRoleDead")
        self:ShowReviveUI()
    else
        UI.Invoke("ScreenInput_Panel", "OnCharacterResurrect")
        self:CloseReviveUI()
        self:CheckCloseSelfRevivePop()
    end
end

function DungeonReviveSystem:CheckMainPlayerDeathState()
    -- 检查角色死亡状态
    local bDead = GetMainPlayerPropertySafely( "IsDead")
    self:UpdateRoleBornDeadState(bDead)
end

--- 用于hud死亡复活界面规则
function DungeonReviveSystem:CheckIsShowReviveUI()
    local bDead = GetMainPlayerPropertySafely("IsDead")
    if bDead and not self:CheckIsSeverControl() then
        return true
    end
    return false
end

---监听死亡复活变化
function DungeonReviveSystem:OnRoleBornDeadState(Entity, PropName, NewValue, OldValue)
    Log.Debug("revive: ", NewValue)
    self:UpdateRoleBornDeadState(NewValue)
end

---复活技能确认回调:空想家自活弹窗
function DungeonReviveSystem:OnSelfReviveConfirm(srcEntityId, reason)
	--脱卡后不允许自活
	if Game.me.DeathReason == Enum.EActorDeadReason.AVATAR_SUICIDE then
		return
	end
    if srcEntityId == Game.me.eid then
		UI.ShowUI("P_HUDRelivePopup", Enum.EDialogPopUpData.ACCEPT_SELFREVIVE_CONFIRM, srcEntityId, reason)
    else
		UI.ShowUI("P_HUDRelivePopup", Enum.EDialogPopUpData.ACCEPT_REVIVE_CONFIRM, srcEntityId, reason)
    end
end

---检查是否关闭自活弹窗
function DungeonReviveSystem:CheckCloseSelfRevivePop()
	-- 不进行isShow判断，因为可能在没加载完成前就隐藏
	UI.HideUI("P_HUDRelivePopup")
end


---全屏受击提示
function DungeonReviveSystem:FullscreenHitFeedback(params)
    if Game.DamageEffectSystem:CheckIsDamage(params) and Game.NewUIManager:IsFullScreenUIOpen() then
        if not self.hitCD or _G._now(1) > self.hitCD then
            ---检查是否在全屏界面上
            self.hitCD = _G._now(1) + Game.TableData.GetConstDataRow("BEATEN_HINT_COOLDOWN")
            UI.ShowUI("P_DungeonOutHurt")
        end
    end
end

---复活技能确认回调:空想家自活弹窗
--function DungeonReviveSystem:OnSelfReviveConfirm(srcEntityId, reason)
--    if self.ReliveDialogId then
--        Game.MessageBoxSystem:InterruptPopupById(self.ReliveDialogId)
--        self.ReliveDialogId = nil
--    end
--    local dialogCallback = function(accept)
--        if Game.me then
--            Game.me:ReqConfirmSkillRevive(accept, srcEntityId, reason)
--        end
--    end
--    if srcEntityId == Game.me.eid then
--        self.ReliveDialogId = Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.ACCEPT_SELFREVIVE_CONFIRM,
--                function() dialogCallback(true) end,
--                function() dialogCallback(false) end)
--    else
--        self.ReliveDialogId = Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.ACCEPT_REVIVE_CONFIRM,
--                function() dialogCallback(true) end,
--                function() dialogCallback(false) end, { StringConst.Get("HUD_TEAMER") })
--    end
--end

---检查是否关闭自活弹窗
--function DungeonReviveSystem:CheckCloseSelfRevivePop()
--    if self.ReliveDialogId then
--        Game.PopupManager:TerminatePopupByUid(self.ReliveDialogId)
--        self.ReliveDialogId = nil
--    end
--end

function DungeonReviveSystem:onInit()
    --- 复活类型id
    self.ReliveDialogId = nil
    --- 受击提示的cd
    self.hitCD = nil
    
    self.model = kg_require("Gameplay.LogicSystem.Dungeon.DungeonRevive.System.DungeonReviveSystemModel").new(false,true)
    self.sender = kg_require("Gameplay.LogicSystem.Dungeon.DungeonRevive.System.DungeonReviveSystemSender").new()
    
    -- 监听角色死亡状态
    Game.EventSystem:AddListener(_G.EEventTypes.ON_IS_DEAD_CHANGED, self, self.OnRoleBornDeadState, GetMainPlayerEID())
    ---监听技能复活
    Game.EventSystem:AddListener(_G.EEventTypes.SERVER_ON_SKILL_REVIVE_CONFIRM, self, self.OnSelfReviveConfirm)
	-- 监听复活类型变更
	Game.EventSystem:AddListener(_G.EEventTypes.ON_REVIVE_LIMIT_TYPE_CHANGE, self, self.OnReviveLimitTypeChange)
end

function DungeonReviveSystem:onUnInit()
    Game.EventSystem:RemoveObjListeners(self)
end

function DungeonReviveSystem:SetMainPlayerLoseControl(loseControl)
    self.model.SelfLostControl = loseControl
end

function DungeonReviveSystem:GetMainPlayerLoseControl()
    return self.model.SelfLostControl
end

return DungeonReviveSystem
