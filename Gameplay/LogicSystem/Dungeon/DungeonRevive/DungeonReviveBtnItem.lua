local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

---@class DungeonReviveBtnItem:UIComponent
local DungeonReviveBtnItem = DefineClass("DungeonReviveBtnItem", UIComponent)
local StringConst = require "Data.Config.StringConst.StringConst"

---按钮类型
Enum.BtnState = {
    Unusable = 1,
    Normal = 0,
    InCD = 2,
}

function DungeonReviveBtnItem:ResetProp()
    self.ReviveConfigID = nil
    self.IsBossBattle = nil
    self.bCanNearbyRelive = true
    self.BtnState = Enum.BtnState.Normal
    self.OnCanNearbyRelivChange = nil
    self.NextReviveTime = 0
end

--- 是否boss脱战
---@param bCanNearbyRelive boolean
function DungeonReviveBtnItem:OnReliveTypeChange(bCanNearbyRelive)
    self.bCanNearbyRelive = bCanNearbyRelive
    self:UpdateBtnState()
end

DungeonReviveBtnItem.eventBindMap = {
    ---监听复活按钮的冷却时间属性同步
    [EEventTypesV2.ON_REVIVERECORDS_ALLOW_TIMESTAMP] = "UpdateReviveCD",
    ---监听boss战时是否能复活属性同步
    [EEventTypesV2.ON_BOSS_BATTLE_REVIVE_CHANGE] = "OnReliveTypeChange",
}

function DungeonReviveBtnItem:OnCreate()
    self:ResetProp()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Com, self.OnClickBtn)
end

function DungeonReviveBtnItem:Refresh(ReviveConfigID)
    self:ResetProp()
    self.ReviveConfigID = ReviveConfigID
    local ReviveConfigData = Game.TableData.GetReviveDataRow(self.ReviveConfigID)
    if ReviveConfigData then
        self.IsBossBattle = ReviveConfigData.IsBossBattle
        --- 是否需要Boss脱战
        if self.IsBossBattle then
            local bCanNearbyRelive = GetMainPlayerPropertySafely( "BossBattleReviveChance")
            self:OnReliveTypeChange(bCanNearbyRelive)
        else
            self:OnReliveTypeChange(true)
        end

        local ReviveDes = ReviveConfigData.ReviveDes
        self.View.Btn_Name:SetText(ReviveDes)
        
        ---设置复活按钮样式
        self.View:SetStyle(ReviveConfigData.ReviveButtonType)
        if self.ReviveConfigID == Game.DungeonReviveSystem.LOSE_CONTROL_BTN then
            self.View.Text_Warning:SetText(StringConst.Get("UNCONTROLLED_STATE"))
        end

        self:UpdateReviveCD()
        --- 播放动效
        self:PlayDianAni()
    else
        self.View.Btn_Name:SetVisibility(ESlateVisibility.Collapsed)
        --self.View.Btn_Image:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--- 更新复活冷却倒计时
function DungeonReviveBtnItem:UpdateReviveCD()
    --- 下次复活时时间戳
    local AllowTimestamp
    ---服务器控制复活
    local ReviveRecords = GetMainPlayerPropertySafely( "ReviveRecords")
    for ReviveId, ReviveItem in pairs(ReviveRecords) do
        if ReviveId == self.ReviveConfigID then
            AllowTimestamp = ReviveItem.AllowTimestamp
            break
        end
    end
    if AllowTimestamp then
        self.NextReviveTime = AllowTimestamp
        local CD = self.NextReviveTime - _G._now()
        if CD // 1000 > 0 then
            self.View.Text_Cold:SetVisibility(ESlateVisibility.selfHitTestInvisible)
            self:StartTimer('ReviveBtnTimer'..self.ReviveConfigID,
                function()
                    local LeftTime = self.NextReviveTime - _G._now()
                    if LeftTime > 0 then
                        local TimeTxt = Game.TimeUtils.FormatCountDownString(LeftTime, false)
                        self.View.Text_Cold:SetText(TimeTxt)
                    else
                        self:PlayDianAni()
                        self:UpdateBtnState()
                        --if self.ReviveConfigID then
                        --    self:StopTimer('ReviveBtnTimer'..self.ReviveConfigID)
                        --end
                        return true
                    end
                end,
                1000, -1, nil, true,
                function()
                    self.View.Text_Cold:SetVisibility(ESlateVisibility.Collapsed)
                end
            )
        else
            self.View.Text_Cold:SetVisibility(ESlateVisibility.Collapsed)
        end
    else
        self.View.Text_Cold:SetVisibility(ESlateVisibility.Collapsed)
    end
    self:UpdateBtnState()
end

function DungeonReviveBtnItem:UpdateBtnState()
    if self.bCanNearbyRelive then
        --- CD中
        if (self.NextReviveTime - _G._now()) // 1000 > 0 then
            self.BtnState = Enum.BtnState.InCD
            self.View.Text_Tip:SetText(StringConst.Get("RELIVE_COOLDOWN")) -- 冷却中
            self.View:SetLockCD(true, false)
        else
            self.BtnState = Enum.BtnState.Normal
            -- self.View.Text_Tip:SetText(StringConst.Get("RELIVE_FREE")) -- 免费
            self.View.Text_Tip:SetText("")
            self.View:SetLockCD(false, false)
        end
    else
        self.BtnState = Enum.BtnState.Unusable
        self.View.Text_Tip:SetText(StringConst.Get("RELIVE_UNAVAILABLE")) --- 暂不可用
        self.View:SetLockCD(false, true)
    end
    self.View.Img_Sphere:SetVisibility(ESlateVisibility.Collapsed)
end

--- 点击复活按钮
function DungeonReviveBtnItem:OnClickBtn()
    if self.BtnState == Enum.BtnState.Unusable then
        local ReminderEnum = Enum.EReminderTextData
        Game.ReminderManager:AddReminderById(ReminderEnum.BOSS_FIGHT_REVIVE)
    elseif self.BtnState == Enum.BtnState.Normal then
        local isPlayering = self.View.WidgetRoot:IsAnimationPlaying(self.View.Ani_Click_On)
        if not isPlayering then
            Log.Debug("[DungeonReviveBtnItem] ReqRevive")
            --- 播放点击效果动效
            self:PlayClickAni()
        end
    end
end

--- 复活请求ReviveConfigID
function DungeonReviveBtnItem:ReqRevive()
    if Game.me then
        Game.DungeonReviveSystem.sender:ReqRevive(self.ReviveConfigID)
        Log.Debug("[ReqRevive]ReviveConfigID:", self.ReviveConfigID)
    end
end

function DungeonReviveBtnItem:OnClose()
    UIBase.OnClose(self)
    if self.ReviveConfigID then
        self:StopTimer("AutoReviveTime"..self.ReviveConfigID)
    end
    self:StopDianAni()
    self:ResetProp()
end

---------------------------------------------------------------- 动效 ------------------------------------------------------------------------
function DungeonReviveBtnItem:PlayDianAni()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Dian, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
        self:PlayAnimation(self.View.WidgetRoot,
                self.View.Ani_Dian_Loop,
                0.0,
                0,
                EUMGSequencePlayMode.Forward,
                1,
                false
        )
    end)
end

function DungeonReviveBtnItem:StopDianAni()
    self:StopAnimation(self.View.WidgetRoot, self.View.Ani_Dian_Loop, 1, false)
end

--- 播放复活按钮点击动效
function DungeonReviveBtnItem:PlayClickAni()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Click_On, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
        --self.View.WidgetRoot:UnbindAllFromAnimationFinished(self.View.Ani_Click_On)
        self:ReqRevive()
    end)
end

return DungeonReviveBtnItem
