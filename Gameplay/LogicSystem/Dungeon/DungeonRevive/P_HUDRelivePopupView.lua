---@class WBP_HUDRelivePopupView : WBP_HUDRelivePopup_C
---@field public WidgetRoot WBP_HUDRelivePopup_C


---@class P_HUDRelivePopupView : WBP_HUDRelivePopupView
---@field public controller P_HUDRelivePopup
local P_HUDRelivePopupView = DefineClass("P_HUDRelivePopupView", UIView)

function P_HUDRelivePopupView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
end

function P_HUDRelivePopupView:OnDestroy()
end

return P_HUDRelivePopupView
