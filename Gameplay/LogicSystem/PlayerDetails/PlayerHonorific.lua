local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerHonorific : UIComponent
---@field view PlayerHonorificBlueprint
local PlayerHonorific = DefineClass("PlayerHonorific", UIComponent)
local ESlateVisibility = import("ESlateVisibility")

PlayerHonorific.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerHonorific:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerHonorific:InitUIData()
    ---@type number 头衔ID
    self.titleID = nil
    ---@type number 面板类型
    self.panelType = nil
end

--- UI组件初始化，此处为自动生成
function PlayerHonorific:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerHonorific:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerHonorific:InitUIView()
end

---组件刷新统一入口
function PlayerHonorific:Refresh(...)
end

function PlayerHonorific:SetTitle(titleID, panelType)
    self.titleID = titleID
    self.panelType = panelType
    --称号文本
    if Game.TitleSystem:IsEmptyTitleID(self.titleID) then
        --空称号
        self:Hide()
    else
        self:Show()

        local titleText = Game.TitleSystem:GetTitleTextByID(self.titleID, self.panelType)
        self.view.Text_Name:SetText(titleText)

        local titleTableData = Game.TitleSystem:GetTableDataRowByType(self.titleID, self.panelType)
        if titleTableData then
            --背景图片
            self:setWidgetIcon(self.view.Img_Bg, titleTableData.TitleBackgroundPath)
            --装饰图片
            self:setWidgetIcon(self.view.Img_Icon, titleTableData.TitleIconPath)
        end

        --称号名称
        self.view.Text_Name:SetText(titleText)
    end
end

function PlayerHonorific:setWidgetIcon(widget, iconName)
    local iconPath = Game.UIIconUtils.getIcon(iconName)
    if iconPath then
        widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:SetImage(widget, iconPath)
    else
        widget:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return PlayerHonorific
