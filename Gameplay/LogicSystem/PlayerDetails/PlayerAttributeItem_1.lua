local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PlayerAttributeItem_1 : UIListItem
---@field view PlayerAttributeItem_1Blueprint
local PlayerAttributeItem_1 = DefineClass("PlayerAttributeItem_1", UIListItem)

PlayerAttributeItem_1.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerAttributeItem_1:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerAttributeItem_1:InitUIData()
	---按钮点击事件
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function PlayerAttributeItem_1:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerAttributeItem_1:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerAttributeItem_1:InitUIView()
end

function PlayerAttributeItem_1:Refresh(textName, textNum)
	self.view.Text_Name:SetText(textName)
	if string.find(textNum, "/") then
		local leftStr, rightStr = string.match(textNum, "([^/]*)(/.*)")
		self.view.Text_Num:SetText(leftStr)
		self.view.Text_Num_Max:SetText(rightStr)
	else
		self.view.Text_Num:SetText(textNum)
		self.view.Text_Num_Max:SetText("")
	end
end

function PlayerAttributeItem_1:on_Btn_ClickArea_lua_Clicked()
	self.onClickEvent:Execute()
end

return PlayerAttributeItem_1
