local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local PlayerBackpack_Item = kg_require("Gameplay.LogicSystem.PlayerDetails.PlayerBackpack_Item")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PlayerBackpack : UIPanel
---@field view PlayerBackpackBlueprint
local PlayerBackpack = DefineClass("PlayerBackpack", UIPanel)
PlayerBackpack.MAX_CAPACITY = 60

PlayerBackpack.eventBindMap = {
	[EEventTypesV2.BAG_ITEM_ADD_OR_DELETE] = "UpdateBackpackAfterBarChange",
	[EEventTypesV2.ON_EQUIP_BAR_CHANGE] = "UpdateBackpackAfterBarChange",
	[EEventTypesV2.BAG_ITEM_DECOMPOSE] = "UpdateBackpackAfterBarChange",
	[EEventTypesV2.ON_CANCEL_SELECTION] = "CancelBackpackSelection",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerBackpack:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerBackpack:InitUIData()
	---@type table
	self.equipFromBagList = {}
	---@type table
	self.equips = nil
	---@type number
	self.slot = nil
	---@type number
	self.professionID = nil
end

--- UI组件初始化，此处为自动生成
function PlayerBackpack:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIListView
    self.TitleViewEx_PlayerBackpackListCom = self:CreateComponent(self.view.TitleViewEx_PlayerBackpackList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function PlayerBackpack:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.TitleViewEx_PlayerBackpackListCom.onItemClicked, "on_TitleViewEx_PlayerBackpackListCom_ItemClicked")
    self:AddUIEvent(self.TitleViewEx_PlayerBackpackListCom.onGetEntryLuaClass, "on_TitleViewEx_PlayerBackpackListCom_GetEntryLuaClass")
    self:AddUIEvent(self.TitleViewEx_PlayerBackpackListCom.onItemSelectionChanged, "on_TitleViewEx_PlayerBackpackListCom_ItemSelectionChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerBackpack:InitUIView()
end

function PlayerBackpack:OnExit()
	if Game.NewUIManager:IsShow(UIPanelConfig.BagItemTips_Panel) then
		Game.NewUIManager:ClosePanel(UIPanelConfig.BagItemTips_Panel)
		self:CancelBackpackSelection()
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_REPLACEABLEEQUIP_UI_CLOSE)
end

function PlayerBackpack:OnRefresh(Params)
	self.selectedIndex = nil
	self.slot = Params.slot
	self.professionID = Params.professionID
	self:UpdateBackpack()
end

function PlayerBackpack:CancelBackpackSelection()
	self.TitleViewEx_PlayerBackpackListCom:ClearSelection()
end

--- 此处为自动生成
function PlayerBackpack:on_WBP_ComBtnCloseCom_ClickEvent()
	self:OnExit()
end

function PlayerBackpack:EscOnClickEvent()
	self:OnExit()
end

function PlayerBackpack:UpdateBackpackAfterBarChange(Params)
	self:UpdateBackpack()
end

function PlayerBackpack:UpdateBackpack()
	self.TitleViewEx_PlayerBackpackListCom:ClearSelection()
	local SlotName = Game.TableData.GetEquipmentSlotDataRow(self.slot).Name
	self.view.Text_SlotName:SetText(Game.TableData.GetStringConstDataRow("SHOW_PROPERTY_EQUIP_CHANGE").StringValue)
	self.view.Text_Name:SetText(SlotName)
	if self.slot < 0 then
		return
	end
	table.clear(self.equipFromBagList)
	local localEquipTypeData = Game.TableData.GetEquipmentTypeDataTable()
	local TabSlotsInfo = Game.BagSystem:GetInvIdInfo(2) ---1指的是tab的值，对应的就是装备tab。装备页签合并进物品里了，改成2了
	local slotItems = TabSlotsInfo.slots
	local tempTypeEquipNeeded = {}
	for key, equipTypeRow in ksbcpairs(localEquipTypeData) do
		if #equipTypeRow.Slot == 1 then
			if equipTypeRow.Slot[1] == self.slot then
				table.insert(tempTypeEquipNeeded, equipTypeRow)
			end
		else
			for i = 1, #equipTypeRow.Slot do
				if equipTypeRow.Slot[i] == self.slot then
					table.insert(tempTypeEquipNeeded, equipTypeRow)
					break
				end
			end
		end
	end

	local tempTypeEquipNeedByProf = {}
	for key, equipTypeRow in pairs(tempTypeEquipNeeded) do
		if equipTypeRow.ClassLimit == nil then
			table.insert(tempTypeEquipNeedByProf, equipTypeRow)
		elseif equipTypeRow.ClassLimit and #equipTypeRow.ClassLimit == 1 then
			if equipTypeRow.ClassLimit[1] == 0 then
				table.insert(tempTypeEquipNeedByProf, equipTypeRow)
			elseif equipTypeRow.ClassLimit[1] == self.professionID then
				table.insert(tempTypeEquipNeedByProf, equipTypeRow)
			end
		else
			for i = 1, #equipTypeRow.ClassLimit do
				if equipTypeRow.ClassLimit[i] == 0 then
					table.insert(tempTypeEquipNeedByProf, equipTypeRow)
					break
				elseif equipTypeRow.ClassLimit[i] == self.professionID then
					table.insert(tempTypeEquipNeedByProf, equipTypeRow)
					break
				end
			end
		end
	end

	for key1, equipFinalData in pairs(slotItems) do
		if Game.TableData.GetItemNewDataRow(equipFinalData.itemId).type == 0 then
			local currSlot = Game.EquipmentSystem:CheckIsInSlot(equipFinalData.gbId)
			if (not currSlot or currSlot ~= self.slot) and Game.EquipmentSystem:GetSlotAllEquip(equipFinalData, self.slot) then
				-- TODO: 做个比较，显示提升标记
				local EquipScore = Game.EquipmentSystem:GetEquipScoreByInfo(equipFinalData)
				local CurEquipInfo = Game.EquipmentSystem:GetBarEquip(self.slot)
				local CmpEquipScore = Game.EquipmentSystem:GetEquipScoreByInfo(CurEquipInfo)
				if EquipScore > CmpEquipScore then
					equipFinalData.MoreScore = true
				else
					equipFinalData.MoreScore = false
				end
				table.insert(self.equipFromBagList, equipFinalData)
			end
		end
	end

	table.clear(tempTypeEquipNeeded)
	table.clear(tempTypeEquipNeedByProf)
	-- 用空数据把背包数据扩充到最大容量
	local maxCapacityList = DeepCopy(self.equipFromBagList)
	local bagItemSize = #self.equipFromBagList
	for i = 1, self.MAX_CAPACITY do
		if maxCapacityList[i] then
			maxCapacityList[i].bagItemSize = bagItemSize
		else
			maxCapacityList[i] = {bagItemSize = bagItemSize}
		end
	end
	self.TitleViewEx_PlayerBackpackListCom:Refresh(maxCapacityList)
end

--- 此处为自动生成
---@param index number
---@param data table
function PlayerBackpack:on_TitleViewEx_PlayerBackpackListCom_ItemClicked(index, data)
	Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common_lvl2, true)
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function PlayerBackpack:on_TitleViewEx_PlayerBackpackListCom_GetEntryLuaClass(index)
	return PlayerBackpack_Item
end

--- 此处为自动生成
---@param index number
---@param selected bool
function PlayerBackpack:on_TitleViewEx_PlayerBackpackListCom_ItemSelectionChanged(index, selected)
	local isAnySelected = self.TitleViewEx_PlayerBackpackListCom:GetSelectedItemNum() > 0
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_BACKPACK_SELECTION_CHANGE, isAnySelected)
end

return PlayerBackpack
