local PlayerInfoBtn = kg_require("Gameplay.LogicSystem.PlayerDetails.PlayerInfoBtn")
local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local PlayerAttribute_Item = kg_require("Gameplay.LogicSystem.PlayerDetails.PlayerAttribute_Item")
local PlayerInfoItem = kg_require("Gameplay.LogicSystem.PlayerDetails.PlayerInfoItem")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerInfoNew : UIComponent
---@field view PlayerInfoNewBlueprint
local PlayerInfoNew = DefineClass("PlayerInfoNew", UIComponent)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")

PlayerInfoNew.PlayerInfoUIStyle = {
	GUILD = 0,      --公会
	TITLE = 1,      --称号
	RANK = 2,       --头衔
}

PlayerInfoNew.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerInfoNew:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerInfoNew:InitUIData()
	---@type table<number, PlayerInfoItem>
	self.UIStyle2Item = {}
	---@type LuaDelegate<fun(index:number)>
	self.onPlayerInfoItemClick = LuaDelegate.new()
	---@type LuaDelegate<fun()>
	self.onAttrLifeBtnClick = LuaDelegate.new()
	---@type table
	self.BasicPropData = {}
	---@type table
	self.AtkPropData = {}
	---@type table
	self.DefPropData = {}
end

--- UI组件初始化，此处为自动生成
function PlayerInfoNew:InitUIComponent()
    ---@type PlayerInfoBtn
    self.WBP_PlayerInfoBtn_DrugCom = self:CreateComponent(self.view.WBP_PlayerInfoBtn_Drug, PlayerInfoBtn)
    ---@type PlayerAttribute_Item
    self.WBP_Attribute_LifeCom = self:CreateComponent(self.view.WBP_Attribute_Life, PlayerAttribute_Item)
    ---@type PlayerInfoItem
    self.WBP_PlayerInfoItem_2Com = self:CreateComponent(self.view.WBP_PlayerInfoItem_2, PlayerInfoItem)
    ---@type PlayerInfoItem
    self.WBP_PlayerInfoItem_1Com = self:CreateComponent(self.view.WBP_PlayerInfoItem_1, PlayerInfoItem)
    ---@type PlayerInfoItem
    self.WBP_PlayerInfoItem_0Com = self:CreateComponent(self.view.WBP_PlayerInfoItem_0, PlayerInfoItem)
    ---@type UISimpleList
    self.List_DefendCom = self:CreateComponent(self.view.List_Defend, UISimpleList)
    ---@type UISimpleList
    self.List_AttackCom = self:CreateComponent(self.view.List_Attack, UISimpleList)
    ---@type UISimpleList
    self.List_BaseCom = self:CreateComponent(self.view.List_Base, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function PlayerInfoNew:InitUIEvent()
    self:AddUIEvent(self.view.PlayerDataBtn.OnClicked, "on_PlayerDataBtn_Clicked")
    self:AddUIEvent(self.WBP_PlayerInfoItem_0Com.onClickEvent, "on_WBP_PlayerInfoItem_0Com_ClickEvent")
    self:AddUIEvent(self.WBP_PlayerInfoItem_1Com.onClickEvent, "on_WBP_PlayerInfoItem_1Com_ClickEvent")
    self:AddUIEvent(self.WBP_PlayerInfoItem_2Com.onClickEvent, "on_WBP_PlayerInfoItem_2Com_ClickEvent")
    self:AddUIEvent(self.WBP_PlayerInfoBtn_DrugCom.onClickEvent, "on_WBP_PlayerInfoBtn_DrugCom_ClickEvent")
    self:AddUIEvent(self.WBP_Attribute_LifeCom.onClickEvent, "on_WBP_Attribute_LifeCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerInfoNew:InitUIView()
	-- 公会/称号/头衔
	self.UIStyle2Item = {
		[PlayerInfoNew.PlayerInfoUIStyle.GUILD] = self.WBP_PlayerInfoItem_0Com,
		[PlayerInfoNew.PlayerInfoUIStyle.TITLE] = self.WBP_PlayerInfoItem_1Com,
		[PlayerInfoNew.PlayerInfoUIStyle.RANK] = self.WBP_PlayerInfoItem_2Com,
	}
	self.WBP_PlayerInfoItem_0Com.userWidget:SetType(PlayerInfoNew.PlayerInfoUIStyle.GUILD)
	self.WBP_PlayerInfoItem_1Com.userWidget:SetType(PlayerInfoNew.PlayerInfoUIStyle.TITLE)
	self.WBP_PlayerInfoItem_2Com.userWidget:SetType(PlayerInfoNew.PlayerInfoUIStyle.RANK)

	--生命
	self.WBP_Attribute_LifeCom.view.Text_Title:SetText(StringConst.Get("HP") or "")
	self.WBP_Attribute_LifeCom.userWidget:Event_UI_Style(504)
	self.WBP_PlayerInfoBtn_DrugCom.userWidget:Event_UI_Style(0)
end

function PlayerInfoNew:on_PlayerDataBtn_Clicked()
	if not Game.me or self.EntityID ~= GetMainPlayerEID() then
		return
	end
	Game.me:ReqGetServerLaunchInfo()
end

function PlayerInfoNew:RefreshUI(selfEntityID)
	self.EntityID = selfEntityID
	self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	if self.EntityID == GetMainPlayerEID() then
		self.view.Img_Progress_Bg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.WBP_PlayerInfoBtn_DrugCom.userWidget:SetVisibility(ESlateVisibility.Visible)
		self.WBP_Attribute_LifeCom.userWidget:Event_UI_Style(504)
		for _, comp in pairs(self.UIStyle2Item) do
			comp.userWidget:SetIcon(true)
		end
	else
		self.userWidget:Event_UI_Style(0)
		self.view.Img_Progress_Bg:SetVisibility(ESlateVisibility.Hidden)
		self.WBP_PlayerInfoBtn_DrugCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
		self.WBP_Attribute_LifeCom.userWidget:Event_UI_Style(552)
		for _, comp in pairs(self.UIStyle2Item) do
			comp.userWidget:SetIcon(false)
		end
	end
end

function PlayerInfoNew:RefreshTitle(TitleName, RankName)
	-- 称号头衔
	local titleComp = self.UIStyle2Item[PlayerInfoNew.PlayerInfoUIStyle.TITLE]
	if TitleName == "" then
		local NoTitleText = StringConst.Get("NOTITLE")
		titleComp.view.Text_Content:SetText(NoTitleText)
		titleComp.userWidget:SetNum(true)
	else
		titleComp.view.Text_Content:SetText(TitleName)
		titleComp.userWidget:SetNum(false)
	end
	
	local rankComp = self.UIStyle2Item[PlayerInfoNew.PlayerInfoUIStyle.RANK]
	if RankName == "" then
		local NoRankText = StringConst.Get("NORANK")
		rankComp.view.Text_Content:SetText(NoRankText)
		rankComp.userWidget:SetNum(true)
	else
		rankComp.view.Text_Content:SetText(RankName)
		rankComp.userWidget:SetNum(false)
	end
end

function PlayerInfoNew:RefreshProp(BasicContentData, AttackContentData, DefendContentData)
	-- 处理基础属性
	table.clear(self.BasicPropData)
	for i = 1, #BasicContentData  do
		table.insert(self.BasicPropData, {BasicContentData[i]})
	end
	self:RefreshPropList(self.List_BaseCom, self.BasicPropData)

	-- 处理攻击属性
	table.clear(self.AtkPropData)
	for i = 1, #AttackContentData do
		table.insert(self.AtkPropData, {AttackContentData[i]})
	end
	self:RefreshPropList(self.List_AttackCom, self.AtkPropData)

	-- 处理防御属性
	table.clear(self.DefPropData)
	for i = 1, #DefendContentData do
		table.insert(self.DefPropData, {DefendContentData[i]})
	end
	self:RefreshPropList(self.List_DefendCom, self.DefPropData)
end

---@param listCom UISimpleList
---@param dataList table
function PlayerInfoNew:RefreshPropList(listCom, dataList)
	local maxIndex = listCom:GetBottomIndex()
	if maxIndex > 1 then
		--- 列表初始化后，子项数量不变，只刷新数据，不会导致SimpleList重复播放动画
		for i = 1, maxIndex do
			---@type PlayerAttribute_Item
			local item = listCom:GetItemByIndex(i)
			item:OnRefresh(dataList[i])
		end
	else
		--- 第一次刷新时初始化列表
		listCom:Refresh(dataList)
	end
end

function PlayerInfoNew:SetGuildInfo(guildName)
	-- 工会信息
	local guildComp = self.UIStyle2Item[PlayerInfoNew.PlayerInfoUIStyle.GUILD]
	if not guildName or guildName == "" then
		local NoGuildText = StringConst.Get("NOGUILD")
		guildComp.view.Text_Content:SetText(NoGuildText)
		guildComp.userWidget:SetNum(true)
	else
		guildComp.view.Text_Content:SetText(guildName)
		guildComp.userWidget:SetNum(false)
	end
end

function PlayerInfoNew:SetSequenceInfo(sequenceInfo, professionId)
end

function PlayerInfoNew:SetLevel(level, exp, maxExp, isSelf)
	self.view.Text_Playerlevel:SetText(level)
	if isSelf == true and maxExp ~= 0 then
		self.userWidget:Event_UI_Style(exp / maxExp)
	end
end

function PlayerInfoNew:SetLife(currLifeStr, totalLifeStr)
	-- 生命
	self.WBP_Attribute_LifeCom.view.Text_Num:SetText(currLifeStr.."<Default>/"..totalLifeStr.."</>")
end

function PlayerInfoNew:SetProfession(professionID)
	-- 职业
	local JobIconPath = Game.TableData.GetPlayerSocialDisplayDataRow(professionID)[0].ClassLogo
	self:SetImage(self.view.Img_JobIcon, JobIconPath)
end

function PlayerInfoNew:on_WBP_PlayerInfoItem_0Com_ClickEvent()
	self.onPlayerInfoItemClick:Execute(PlayerInfoNew.PlayerInfoUIStyle.GUILD)
end

function PlayerInfoNew:on_WBP_PlayerInfoItem_1Com_ClickEvent()
	self.onPlayerInfoItemClick:Execute(PlayerInfoNew.PlayerInfoUIStyle.TITLE)
end

function PlayerInfoNew:on_WBP_PlayerInfoItem_2Com_ClickEvent()
	self.onPlayerInfoItemClick:Execute(PlayerInfoNew.PlayerInfoUIStyle.RANK)
end

function PlayerInfoNew:on_WBP_PlayerInfoBtn_DrugCom_ClickEvent()
	self.onPlayerInfoItemClick:Execute(-1)
end

function PlayerInfoNew:on_WBP_Attribute_LifeCom_ClickEvent()
	self.onAttrLifeBtnClick:Execute()
end

return PlayerInfoNew
