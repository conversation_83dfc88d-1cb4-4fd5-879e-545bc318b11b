local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PlayerAttribute_Item : UIListItem
---@field view PlayerAttribute_ItemBlueprint
local PlayerAttribute_Item = DefineClass("PlayerAttribute_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

PlayerAttribute_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerAttribute_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerAttribute_Item:InitUIData()
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent 按钮点击事件
	self.onClickEvent = LuaDelegate.new()
	---@type table 数据
	self.data = nil
end

--- UI组件初始化，此处为自动生成
function PlayerAttribute_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerAttribute_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerAttribute_Item:InitUIView()
	self.userWidget:Event_UI_Style(self.view.TextSpace)
end

---面板打开的时候触发
function PlayerAttribute_Item:OnRefresh(data)
	self.bShowTips = self.EntityID == GetMainPlayerEID()
	self.data = data
	if data and data[1] then
		self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Text_Title:SetText(data[1].ShowPropertyName)
		self.view.Text_Num:SetText(data[1].Value)
	else
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--- 此处为自动生成
function PlayerAttribute_Item:on_Btn_ClickArea_Clicked()
	self.onClickEvent:Execute()

	if not self.data or not self.data[1] then return end
	local TipsID = self.data[1].TableData.TipsID

	local mouseViewportPosition = import("WidgetLayoutLibrary").GetMousePositionOnViewport(_G.GetContextObject())
	local _, viewportPosition = import("SlateBlueprintLibrary").LocalToViewport(
		_G.GetContextObject(),
		self.userWidget:GetCachedGeometry(),
		FVector2D(0, 0),
		nil,
		nil
	)
	
	viewportPosition.X = mouseViewportPosition.X
	Game.GlobalEventSystem:Publish(EEventTypesV2.PROP_ITEM_SHOW_TIPS, TipsID, viewportPosition)
end

return PlayerAttribute_Item
