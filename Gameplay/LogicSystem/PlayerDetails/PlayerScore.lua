local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerScore : UIComponent
---@field view PlayerScoreBlueprint
local PlayerScore = DefineClass("PlayerScore", UIComponent)

PlayerScore.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerScore:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerScore:InitUIData()
	---按钮点击事件
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function PlayerScore:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerScore:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerScore:InitUIView()
end

function PlayerScore:SetText(text)
	self.view.Text_Score:SetText(text)
end

function PlayerScore:GetText()
	return self.view.Text_Score:GetText()
end

function PlayerScore:on_Btn_ClickArea_Clicked()
	self.onClickEvent:Execute()
end

return PlayerScore
