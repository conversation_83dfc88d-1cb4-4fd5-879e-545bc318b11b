local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerPlanBtn : UIComponent
---@field view PlayerPlanBtnBlueprint
local PlayerPlanBtn = DefineClass("PlayerPlanBtn", UIComponent)

PlayerPlanBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerPlanBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerPlanBtn:InitUIData()
	---按钮点击事件
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function PlayerPlanBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerPlanBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerPlanBtn:InitUIView()
end

---组件刷新统一入口
function PlayerPlanBtn:Refresh(...)
end

function PlayerPlanBtn:SetText(text)
	self.view.Text_PlanName:SetText(text)
end

function PlayerPlanBtn:GetText()
	return self.view.Text_PlanName:GetText()
end
	
function PlayerPlanBtn:on_Btn_ClickArea_Clicked()
	self.onClickEvent:Execute()
end

return PlayerPlanBtn
