local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerTitle : UIComponent
---@field view PlayerTitleBlueprint
local PlayerTitle = DefineClass("PlayerTitle", UIComponent)
local ESlateVisibility = import("ESlateVisibility")

PlayerTitle.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerTitle:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerTitle:InitUIData()
    ---@type number 称号ID
    self.titleID = nil
    ---@type number 面板类型
    self.panelType = nil
end

--- UI组件初始化，此处为自动生成
function PlayerTitle:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerTitle:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerTitle:InitUIView()
end

---组件刷新统一入口
function PlayerTitle:Refresh(...)
end

function PlayerTitle:SetTitle(titleID, panelType)
    self.titleID = titleID
    self.panelType = panelType
    --称号文本
    if Game.TitleSystem:IsEmptyTitleID(self.titleID) then
        --空称号
        self:Hide()
    else
        self:Show()

        local titleText = Game.TitleSystem:GetTitleTextByID(self.titleID, self.panelType)
        self.view.Text_Name:SetText(titleText)

        local titleTableData = Game.TitleSystem:GetTableDataRowByType(self.titleID, self.panelType)
        if titleTableData then
            --背景图片
            self:setWidgetIcon(self.view.Img_Bg, titleTableData.TitleBackgroundPath)
            --装饰图片
            self:setWidgetIcon(self.view.Img_Icon, titleTableData.TitleIconPath)
            -- 称号后缀
            if titleTableData.TitleIconPathTail then
                self:setWidgetIcon(self.view.Img_Icon_1, titleTableData.TitleIconPathTail)
                self.view.Img_Icon_1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            else
                self.view.Img_Icon_1:SetVisibility(ESlateVisibility.Collapsed)
            end
        end

        --称号名称
        self.view.Text_Name:SetText(titleText)
    end
end

function PlayerTitle:setWidgetIcon(widget, iconName)
    local iconPath = Game.UIIconUtils.getIcon(iconName)
    if iconPath then
        widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:SetImage(widget, iconPath)
    else
        widget:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return PlayerTitle
