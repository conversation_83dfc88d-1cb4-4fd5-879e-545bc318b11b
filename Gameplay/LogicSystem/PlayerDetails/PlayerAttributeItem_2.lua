local PlayerAttributeItem_1 = kg_require("Gameplay.LogicSystem.PlayerDetails.PlayerAttributeItem_1")
local UITreeItem = kg_require("Framework.KGFramework.KGUI.Component.UITreeView.UITreeItem")
---@class PlayerAttributeItem_2 : UITreeItem
---@field view PlayerAttributeItem_2Blueprint
local PlayerAttributeItem_2 = DefineClass("PlayerAttributeItem_2", UITreeItem)
local DescFormulaHelper = kg_require "Gameplay.LogicSystem.SkillCustomizer.DescFormulaHelper"
local ESlateVisibility = import("ESlateVisibility")
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")

PlayerAttributeItem_2.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerAttributeItem_2:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerAttributeItem_2:InitUIData()
	self.data = nil
	self.bSelect = false
	self.Attributes = nil
end

--- UI组件初始化，此处为自动生成
function PlayerAttributeItem_2:InitUIComponent()
    ---@type PlayerAttributeItem_1
    self.WBP_PlayerDisplayInfoItemCom = self:CreateComponent(self.view.WBP_PlayerDisplayInfoItem, PlayerAttributeItem_1)
    ---@type PlayerAttributeItem_1
    self.WBP_PlayerDisplayInfoItem_1Com = self:CreateComponent(self.view.WBP_PlayerDisplayInfoItem_1, PlayerAttributeItem_1)
end

---UI事件在这里注册，此处为自动生成
function PlayerAttributeItem_2:InitUIEvent()
    self:AddUIEvent(self.WBP_PlayerDisplayInfoItem_1Com.onClickEvent, "on_WBP_PlayerDisplayInfoItem_1Com_ClickEvent")
    self:AddUIEvent(self.WBP_PlayerDisplayInfoItemCom.onClickEvent, "on_WBP_PlayerDisplayInfoItemCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerAttributeItem_2:InitUIView()
end

---面板打开的时候触发
function PlayerAttributeItem_2:OnRefresh(data)
	self:Refresh(data)
end

function PlayerAttributeItem_2:Refresh(data)
	local parentUI = self:GetBelongPanel()
	if parentUI then
		self.Attributes = parentUI.Attributes
	end

	self.data = data.tabData
	if not self.data then return end
	if self.data[1] then
		self.WBP_PlayerDisplayInfoItemCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.WBP_PlayerDisplayInfoItemCom:Refresh(self.data[1].TableData.ShowPropertyName, self.data[1].Value)
	else
		self.WBP_PlayerDisplayInfoItemCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end

	if self.data[2] then
		self.WBP_PlayerDisplayInfoItem_1Com.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.WBP_PlayerDisplayInfoItem_1Com:Refresh(self.data[2].TableData.ShowPropertyName, self.data[2].Value)
	else
		self.WBP_PlayerDisplayInfoItem_1Com.userWidget:SetVisibility(ESlateVisibility.Hidden)
	end

	if self.index % 2 == 1 then
		self.userWidget:Event_UI_Style(2)
	else
		self.userWidget:Event_UI_Style(3)
	end
end

function PlayerAttributeItem_2:on_WBP_PlayerDisplayInfoItemCom_ClickEvent()
	if not self.data[1] then return end
	local TipsID = self.data[1].TableData.TipsID
	local ViewportPosition = WidgetLayoutLibrary.GetMousePositionOnViewport(_G.GetContextObject())
	if self.EntityID == GetMainPlayerEID() then
		DescFormulaHelper.TargetActor = Game.me
	else
		DescFormulaHelper.TargetActor = self.Attributes
	end

	Game.TipsSystem:ShowTips(TipsID, nil, {
			Pos = ViewportPosition,
			Size = FVector2D(100, 100),
			bregularization = true
		}
	)
end

function PlayerAttributeItem_2:on_WBP_PlayerDisplayInfoItem_1Com_ClickEvent()
	if not self.data[2] then return end
	local TipsID = self.data[2].TableData.TipsID
	local ViewportPosition = WidgetLayoutLibrary.GetMousePositionOnViewport(_G.GetContextObject())
	if self.EntityID == GetMainPlayerEID() then
		DescFormulaHelper.TargetActor = Game.me
	else
		DescFormulaHelper.TargetActor = self.Attributes
	end

	Game.TipsSystem:ShowTips(TipsID, nil, {
			Pos = ViewportPosition,
			Size = FVector2D(100, 100),
			bregularization = true
		}
	)
end

return PlayerAttributeItem_2
