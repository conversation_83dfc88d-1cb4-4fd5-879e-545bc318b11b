local ItemBoxNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemBoxNew")
---@class PlayerBackpack_Item : ItemBoxNew
local PlayerBackpack_Item = DefineClass("PlayerBackpack_Item", ItemBoxNew)

PlayerBackpack_Item.eventBindMap = {
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerBackpack_Item:OnCreate()
	ItemBoxNew.OnCreate(self)
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerBackpack_Item:InitUIData()
	---@type UIComponent 
	self.newTipWidget = nil
	---@type ItemNml
	self.item = nil
	---@type table
	self.data = nil
end

function PlayerBackpack_Item:InitUIComponent()
end

function PlayerBackpack_Item:InitUIEvent()
end

function PlayerBackpack_Item:InitUIView()
end

function PlayerBackpack_Item:OnRefresh(data)
	self:CloseNewItemTipComponent()
	if self.index > data.bagItemSize then
		self:FillItem()
		self:CallItemComponent("SetIconOpacity", 1)
	else
		self.data = data
		local itemID = data.itemId
		--self:FillItem() -- 刷新下格子，不然边缘特效会有残留
		-- todo:获取数量数据 
		self:FillItem(itemID, Enum.GridState.Occupy, Enum.CommonItemClickType.NoFunChick, false, 1, data.bound)
		self:CallItemComponent("SetIconOpacity", 1)
		if data.MoreScore then
			-- TODO:展示提升标记
			self:CallItemComponent("SetRT", Enum.RightUpIconType.UpGrade)
		end

		---新物品提示
		if not Game.BagSystem.bResolveUI and data and data.isNewTip then
			self:ShowNewItemTipComponent()
		else
			self:CloseNewItemTipComponent()
		end

		---冷静期
		if data and data.purchaseInfo and data.purchaseInfo.freezeTime > _G._now(1) then
			self:CallItemComponent("SetLT", Enum.ItemLeftUpIconType.CoolingOffPeriod)
			local leftTime = data.purchaseInfo.freezeTime - _G._now(1)
			self:GetParent():StartTimer("CoolingOffPeriodTimer_"..data.gbId,
				function()
					self:CallItemComponent("SetLT", Enum.ItemLeftUpIconType.Trade)
				end,
				leftTime*1000, 1)
		end

		--- 通用选中态
		if Game.BagSystem.bResolveUI and not Game.BagSystem:CheckCanDecompose(itemID, data) then
			self:CallItemComponent("SetStatus", Enum.ItemStatus.Undecomposable)
		else
			self:SetSelected(false)
			self.userWidget:SetBoxType(1)
		end

		if Game.EquipmentSystem:GetEquip2Plan(data.gbId) then
			self:CallItemComponent("SetLT", 4)
		end
	end
end

function PlayerBackpack_Item:UpdateSelectionState(selected)
	if not self.data then return end

	--- 通用选中态
	if Game.BagSystem.bResolveUI then
		if Game.BagSystem:CheckCanDecompose(self.data.itemID, self.data) then
			self:SetSelected(false)
			if selected then
				self.userWidget:SetBoxType(4)
			else
				self.userWidget:SetBoxType(1)
			end
		else
			self:GetItemComponent().userWidget:SetStatus(Enum.ItemStatus.Undecomposable)
		end
	else
		self:SetSelected(selected)
		self.userWidget:SetBoxType(1)
	end
end

function PlayerBackpack_Item:OnClickBtnInternal()
	ItemBoxNew.OnClickBtnInternal(self)
	self.parentComponent:processListClickInternal(self.index, self.data)
	if not UI.IsShow('BagItemTips_Panel') then
		local Params = {
			IsShowBtn = true,
			ItemFrom = Game.BagSystem.ItemFrom.ITEM_FROM_INVENTORY,
			SlotInfo = self.data,
			TabId = 2,
			SlotID = self.slot
		}
		if self.data.isNewTip then
			Game.BagSystem.sender:ReqChangeSlotTipType(2, self.data.index, false)
			self:CloseNewItemTipComponent()
		end
		UI.ShowUI('BagItemTips_Panel', self.data.itemId, Params)
	end
end

return PlayerBackpack_Item
