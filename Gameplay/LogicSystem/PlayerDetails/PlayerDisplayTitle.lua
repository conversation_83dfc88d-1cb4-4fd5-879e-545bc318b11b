local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerDisplayTitle : UIComponent
---@field view PlayerDisplayTitleBlueprint
local PlayerDisplayTitle = DefineClass("PlayerDisplayTitle", UIComponent)

PlayerDisplayTitle.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerDisplayTitle:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerDisplayTitle:InitUIData()
	---@type LuaDelegate<fun()>
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function PlayerDisplayTitle:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerDisplayTitle:InitUIEvent()
    self:AddUIEvent(self.view.OnMouseButtonUpEvent, "on_WBP_PlayerDisplayTitle_MouseButtonUpEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerDisplayTitle:InitUIView()
end

---组件刷新统一入口
function PlayerDisplayTitle:Refresh(iconPath, textName, textEnglishName)
	self:SetImage(self.view.Img_prop, iconPath, nil, nil, true)
	self.view.Text_Name:SetText(textName)
	self.view.Text_Misery:SetText(textEnglishName or "")
end


--- 此处为自动生成
---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent

function PlayerDisplayTitle:on_WBP_PlayerDisplayTitle_MouseButtonUpEvent(myGeometry, inMouseEvent)
	self.onClickEvent:Execute()
end

return PlayerDisplayTitle
