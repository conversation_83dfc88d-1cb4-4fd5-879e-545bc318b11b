local UIComMask = kg_require("Framework.KGFramework.KGUI.Component.BackGround.UIComMask")
local UITreeView = kg_require("Framework.KGFramework.KGUI.Component.UITreeView.UITreeView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PlayerDetail_Panel : UIPanel
---@field view PlayerDetail_PanelBlueprint
local PlayerDetail_Panel = DefineClass("PlayerDetail_Panel", UIPanel)
local equipUtils = kg_require("Shared.Utils.EquipUtils")

PlayerDetail_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerDetail_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerDetail_Panel:InitUIData()
	---@type table
	self.HandledTitleData = {}
	---@type table
	self.PropNames = {}
	---@type table
	self.ContentData = nil
	---@type UITreeViewData
	self.PropData = nil
	---@type string
	self.EntityID = nil
	---@type number
	self.PageType = nil
end

--- UI组件初始化，此处为自动生成
function PlayerDetail_Panel:InitUIComponent()
    ---@type UIComMask
    self.WBP_ComMaskCom = self:CreateComponent(self.view.WBP_ComMask, UIComMask)
    ---@type UITreeView
    self.ListCom = self:CreateComponent(self.view.List, UITreeView)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function PlayerDetail_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerDetail_Panel:InitUIView()
end

---面板打开的时候触发
function PlayerDetail_Panel:OnRefresh(Params)
	self.ContentData = nil
	self.EntityID = Params.EntityID
	self.PageType = Params.PageType
	if self.EntityID == GetMainPlayerEID() then
		self.ProfessionID = GetMainPlayerPropertySafely( "Profession")
		local JobType = equipUtils.GetPlayerBattleDataRow(self.ProfessionID, 0).ClassPropType
		local PropertyTable = nil
		if JobType == 1 then
			PropertyTable = Game.TableData.GetPhyDetailDataTable()
		else
			PropertyTable = Game.TableData.GetMagDetailDataTable()
		end
		for key, value in ksbcpairs(PropertyTable) do
			for k, v in ksbcipairs(value.ShowProperty) do
				table.insert(self.PropNames, v)
			end
		end
		--属性值
		self.Attributes = {}
		for k, PropName in ksbcipairs(self.PropNames) do
			local Res = GetMainPlayerPropertySafely( PropName)
			self.Attributes[PropName] = Res
		end

		self.ProfessionID = GetMainPlayerPropertySafely( "Profession")
		self.JobType = equipUtils.GetPlayerBattleDataRow(self.ProfessionID, 0).ClassPropType
	else
		self.Name = Params.Name
		self.Level = Params.Level
		self.ProfessionID = Params.ProfessionID
		self.Attributes = Params.Attributes
		self.Attributes.Level = self.Level
		self.JobType = equipUtils.GetPlayerBattleDataRow(self.ProfessionID, 0).ClassPropType
	end
	self:StartTimer("RolePropTimer", function()
		self:RefreshContentValue()
	end, 500, -1, nil, true)
end

--- 此处为自动生成
function PlayerDetail_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
	self:CloseSelf()
end

function PlayerDetail_Panel:RefreshContentValue()
	local PropTitleData = Game.TableData.GetPropTitleDataTable()
	self.HandledTitleData = {}
	local tempContentData = {}

	if self.EntityID == GetMainPlayerEID() then
		--属性值
		for k, PropName in ksbcipairs(self.PropNames) do
			local Res = GetMainPlayerPropertySafely( PropName)
			self.Attributes[PropName] = Res
		end
	end
	if self.JobType == 2 then
		--法攻角色
		local MagBriefDatas = Game.TableData.GetMagDetailDataTable()
		for key, value in ksbcpairs(MagBriefDatas) do
			local Data = Game.RoleDisplaySystem.HandledData(self.Attributes, value, self.ProfessionID)
			if tempContentData[value.Title] == nil then
				tempContentData[value.Title] = {}
			end
			table.insert(tempContentData[value.Title], { TableData = value, Value = Data })
		end
	elseif self.JobType == 1 then
		--物攻角色
		local PhyBriefDatas = Game.TableData.GetPhyDetailDataTable()
		for key, value in ksbcpairs(PhyBriefDatas) do
			local Data = Game.RoleDisplaySystem.HandledData(self.Attributes, value, self.ProfessionID)
			if tempContentData[value.Title] == nil then
				tempContentData[value.Title] = {}
			end
			table.insert(tempContentData[value.Title], { TableData = value, Value = Data })
		end
	end
	for key, value in ksbcpairs(tempContentData) do
		table.sort(tempContentData[key], function(a, b)
			return a.TableData.ID < b.TableData.ID
		end)
	end
	for index, value in ksbcpairs(PropTitleData) do
		if tempContentData[value.ID] and #tempContentData[value.ID] > 0 then
			table.insert(self.HandledTitleData, value)
		end
	end
	table.sort(
		self.HandledTitleData,
		function(a, b)
			return a.ID < b.ID
		end
	)

	-- 只有第一次刷新时刷新整个列表
	local bFirstRefresh = self.ContentData == nil
	local needRefreshItems = {}
	---@type UITreeViewData
	self.PropData = UITreeView.NewTreeViewData() 
	for k, v in ksbcpairs(self.HandledTitleData) do
		self.PropData:AddFirstNode(v)
		local SrcData = tempContentData[k]
		for i = 1, #SrcData -1, 2 do
			self.PropData:AddTwoNode(k, {SrcData[i], SrcData[i + 1]})
			if not bFirstRefresh then
				if self.ContentData[k][i].Value ~= SrcData[i].Value or self.ContentData[k][i + 1].Value ~= SrcData[i + 1].Value then
					table.insert(needRefreshItems, {k, i})
				end
			end
		end
		if #SrcData % 2 == 1 then
			self.PropData:AddTwoNode(k, {SrcData[#SrcData]})
			if not bFirstRefresh then
				table.insert(needRefreshItems, {k, #SrcData})
				--if self.ContentData[k][#SrcData].Value ~= SrcData[#SrcData].Value then
				--	table.insert(needRefreshItems, {k, #SrcData})
				--end
			end
		end
	end
	
	self.ContentData = tempContentData
	if bFirstRefresh then
		self.ListCom:Refresh(self.PropData)
		self.ListCom:ExpandAll()
	else
		for k, v in ipairs(needRefreshItems) do
			local index1, index2 = v[1], (v[2] + 1) / 2
			local item = self.ListCom:GetItemByPath(index1, index2)
			if item then
				item:Refresh(self.PropData.children[index1].children[index2])
			end
		end
	end
end

function PlayerDetail_Panel:OnClose()
	self:StopTimer("RolePropTimer")
end

return PlayerDetail_Panel
