---@class RechargeSystem:SystemBase
local RechargeSystem = DefineClass("RechargeSystem",SystemBase)

function RechargeSystem:onCtor()
    self.model = nil
    self.sender = nil
    self.currentProduct = nil
    self.regetCount = 0
end

function RechargeSystem:onInit()
    ---@type RechargeModel
    self.model = kg_require("Gameplay.LogicSystem.Recharge.RechargeModel").new(false, true)
    ---@type RechargeSender
    self.sender = kg_require("Gameplay.LogicSystem.Recharge.RechargeSender").new()
    self.currentProduct = nil
    self.regetCount = 0
    if Game.AllInSdkManager then
        Game.AllInSdkManager:SetPayCallback(function(result) self:OnSDKPaySuccessListener(result) end,
        function(error) self:OnSDKPayFailedListener(error) end,function(payResult, taskId) self:OnSDKRequestPaymentDetail(payResult, taskId) end)
    end
end

--Logic-----------------------------------------------------------------------------------------------------------------

-- 打开充值界面
function RechargeSystem:ShowRechargePanel()
        if Game.NewUIManager:CheckPanelIsOpen("RechargePanel") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TOPUP_PANEL_OPENED)
    else
        Game.NewUIManager:OpenPanel("RechargePanel")
    end
end

-- 请求商品列表成功
function RechargeSystem:getPlatformNecessaryDataSuccess()
    self.regetCount = 0
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECHARGE_RECV_ITEM_INFO)
end

-- 拉取失败后尝试三次
function RechargeSystem:getPlatformNecessaryDataFailed()
    if self.regetCount < 3 then
        self:ReqGetProductList()
        self.regetCount = self.regetCount + 1
    else
        Game.GlobalEventSystem:Publish(EEventTypesV2.RECHARGE_ITEM_NOT_FOUND)
    end
end

function RechargeSystem:CheckIsFirst(id)
    if self.model.rechargeFirstPay then
        return self.model.rechargeFirstPay[id]
    end
end

--Request---------------------------------------------------------------------------------------------------------------

---GetProductList 获取平台配置产品数据
function RechargeSystem:ReqGetProductList()
    if self.model.productList == nil then
        Game.AllInSdkManager:QueryProductDetails(
                function()
                    self:getPlatformNecessaryDataSuccess()
                end,
                function()
                    self:getPlatformNecessaryDataFailed()
                end
        )
    else
        self:getPlatformNecessaryDataSuccess()
    end
end

-- 请求签名
function RechargeSystem:RequestPaySign(configId, productId)
    if configId and productId then
        self.currentProduct = productId
        self.sender:RequestPaySign(configId,productId)
    end
end

-- 请求收货
function RechargeSystem:RequestAddProduct(configId, productId)
    if configId and productId then
        self.sender:RequestAddProduct(configId, productId)
    end
end

---SDKPayProduction 调用SDK支付接口
---@param productId string 商品ID
---@param sign string 签名信息
---@param thirdPartyTradeNo string 订单号
---@param payNotifyUrl string 支付回调地址
---@param extension string 附加数据，在支付回调时会原样传回,可以留空
function RechargeSystem:SDKPayProduction(productId,sign,thirdPartyTradeNo,payNotifyUrl,extension)
    Game.AllInSdkManager:PayProduction(productId,sign,thirdPartyTradeNo,payNotifyUrl,extension)
end

--CallBack--------------------------------------------------------------------------------------------------------------

-- 收到签名并唤起SDK支付
function RechargeSystem:RetPaySign(result, sign, payNotifyUrl, thirdPartyTradeNo)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECHARGE_RECV_PAYMENT_SIGN)
    if not result or result ~= 0 then
        Game.GlobalEventSystem:Publish(EEventTypesV2.RECHARGE_ITEM_NOT_FOUND)
        return
    end

    local currentProduct = self.currentProduct
    if currentProduct then
        self.currentProduct = nil
        self:SDKPayProduction(currentProduct, sign, thirdPartyTradeNo, payNotifyUrl)
    else
        Log.Error("RechargeSystem RetPaySign error! when returned by server PaySign, client cached currentProduct is nil")
    end
end

-- 收货
function RechargeSystem:RetAddProduct(result, productId, bFirst)
	Game.GlobalEventSystem:Publish(EEventTypesV2.RECHARGE_ADD_PRODUCT, result, productId, bFirst)
end

---OnSDKPaySuccessListener 平台支付成功回调，正式阶段不能依赖这个接口，但是版署包需要在这里主动向服务器请求发货
---@param result PayResultModel 支付结果
function RechargeSystem:OnSDKPaySuccessListener(result)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECHARGE_PURCHASE_SUCCESS)
    local RechargeTable = Game.TableData.Get_TopupData()
    local productId = result.productId
    
    if (nil == productId or "" == productId) and result.data then
        productId = result.data.product_id or "" --用.data.product_id 再拿一次是因为PC这边跟安卓的这个字段名字不一样！！！
    end
    
    local currentConfig = RechargeTable[productId]
    if currentConfig then
        self:RequestAddProduct(currentConfig.ID, productId)
    else
        Log.Error("RechargeSystem OnSDKPaySuccessListener error! can not get this product config productId = ", productId)
    end
end

---OnSDKPayFailedListener 平台支付失败回调
---@param error table {code,msg} 返回的错误结果
function RechargeSystem:OnSDKPayFailedListener(error)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECHARGE_PURCHASE_FAILED, error)
end

---OnSDKRequestPaymentDetail 回传预下单信息回调(iOS独有)
---@param payResult  PayResultModel 支付结果
---@param taskId string taskId
function RechargeSystem:OnSDKRequestPaymentDetail(payResult, taskId)
    --local sign,thirdPartyTradeNo,payNotifyUrl  --todo RetPaySign获取 
    --Game.AllInSdkManager:ResponsePaymentDetail(payResult,taskId) --,sign,thirdPartyTradeNo,payNotifyUrl) -- sdk3.0移除
end

function RechargeSystem:onUnInit()
    Game.AllInSdkManager:ClearPayCallback()
    self.model:unInit()
end

return RechargeSystem