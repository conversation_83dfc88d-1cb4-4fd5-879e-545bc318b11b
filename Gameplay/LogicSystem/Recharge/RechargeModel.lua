---@class RechargeModel:SystemModelBase
local RechargeModel = DefineClass("RechargeModel",SystemModelBase)

function RechargeModel:init()
    self.productList = nil --商品列表
    self.rechargeFirstPay = nil --首充信息
end

---SetProductList 设置平台配置产品数据
---@param productList table{ProductDetail}
function RechargeModel:SetProductList(productList)
    self.productList = productList
    table.sort(self.productList, function(A,B)
        return tonumber(A.price) < tonumber(B.price)
    end)
end

---GetProductList 获取商品列表
function RechargeModel:GetProductList()
    return self.productList
end

---GetFirstPayInfo 缓存首充信息
function RechargeModel:GetFirstPayInfo()
    local mainPlayer = Game.me
    if mainPlayer and not self.rechargeFirstPay then
        self.rechargeFirstPay = DeepCopy(mainPlayer.FirstPayInfos)
    end
end

---UpdateFirstPay 更新首充信息
---@param id number 商品id
function RechargeModel:UpdateFirstPay(id)
    if self.rechargeFirstPay then
        self.rechargeFirstPay[id] = 1
    end
end

function RechargeModel:clear()
    self:init()
end

function RechargeModel:unInit()
    self.productList = nil
end

return RechargeModel