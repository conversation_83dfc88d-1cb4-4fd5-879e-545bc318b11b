local UICurrentcyItem = kg_require("Framework.KGFramework.KGUI.Component.Tag.UICurrentcyItem")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local ESlateVisibility = import("ESlateVisibility")

---@class RechargeChoiceItem : UIComponent
---@field view RechargeChoiceItemBlueprint
local RechargeChoiceItem = DefineClass("RechargeChoiceItem", UIComponent)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RechargeChoiceItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RechargeChoiceItem:InitUIData()
	-- 因为目前主面板无法绑定Component的点击事件，需要持有一个主面板引用才行
	self.parentPanel = nil
	--选项位置
	self.itemIndex = 1
end

--- UI组件初始化，此处为自动生成
function RechargeChoiceItem:InitUIComponent()
    ---@type UICurrentcyItem
    self.WBP_ComCurrencyCom = self:CreateComponent(self.view.WBP_ComCurrency, UICurrentcyItem)
    ---@type UICurrentcyItem
    self.WBP_ComCurrency_GiftedCom = self:CreateComponent(self.view.WBP_ComCurrency_Gifted, UICurrentcyItem)
end

---UI事件在这里注册，此处为自动生成
function RechargeChoiceItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RechargeChoiceItem:InitUIView()
end

---Refresh--------------------------------------------------------------------------------------------------------------

function RechargeChoiceItem:Refresh(parentPanel, itemIndex, id)
	self.parentPanel = parentPanel
	self.itemIndex = itemIndex
	self:setRechargeItem(id)
end

-- 设置单个充值项目
function RechargeChoiceItem:setRechargeItem(id)
    local RechargeTable = Game.TableData.Get_TopupData()
	local idStr = tostring(id)
    local RechargeInfo = RechargeTable[idStr]
    if not RechargeInfo then
        Log.DebugWarningFormat("Unable to find recharge item in local data")
        return
    end
    self.view.Text_Cash:SetText(string.format("%s%i", RechargeInfo.DisplaySymbol, RechargeInfo.DisplayPrice))
    self:SetImage(self.view.Img_CoinBg, RechargeInfo.TopupIconPath)
	self.WBP_ComCurrencyCom:Refresh(RechargeInfo.TopupId, RechargeInfo.TopupQuantity)

    -- 首充奖励
    if not Game.RechargeSystem:CheckIsFirst(RechargeInfo.ID) then
		self.WBP_ComCurrency_GiftedCom:Refresh(RechargeInfo.TopupId, RechargeInfo.FirstGiftQuantity)
        self.view.Overlay_tag:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.Overlay_tag:SetVisibility(ESlateVisibility.Hidden)
    end
end

--- 此处为自动生成
function RechargeChoiceItem:on_Btn_ClickArea_Clicked()
    --- 充值选项点击事件
    if self.parentPanel then
        self.parentPanel:OnClick_RechargeComponent(self.itemIndex)
    end
end

return RechargeChoiceItem
