---推理系统，由dialogue调用的一个ui玩法
local TimerComponent = kg_require("Framework.KGFramework.KGCore.TimerManager.TimerComponent")
---@class ReasoningSystem: SystemBase, TimerComponent
local ReasoningSystem = DefineClass("ReasoningSystem", SystemBase, TimerComponent)

local ReasoningUtils = kg_require("Gameplay.LogicSystem.Reasoning.ReasoningUtils")
ReasoningSystem.DelayPauseDialogueTime = 500

---@class ReasoningLayerParams
---@field bCloseMysteryMode boolean 是否关闭推理模式
---@field delayPauseDialogueTime number 延迟暂停dialogue的时间


function ReasoningSystem:onCtor()
	self.curLayerId = -1
	self.nextLayerId = -1
	self.delayPauseDialogueTimerKey = "DelayPauseDialogue"
	self.curLayerStartTime = 0
	---@type ReasoningLayerParams
	self.curLayerParams = nil
	self.bJudgeSucc = false
end

---返回登录的时候清理一下数据
function ReasoningSystem:OnBackToLogin()
	self:resetLayerParams()
	self:StopTimer(self.delayPauseDialogueTimerKey)
end

function ReasoningSystem:checkSkipLayer(layerId)
	--Log.DebugFormat("[ReasoningSystem:checkSkipLayer] bJugdeSucc: %s, nextLayerId: %s, layerId:%d", self.bJudgeSucc, self.nextLayerId, layerId)
	return self.bJudgeSucc or self.nextLayerId > layerId
end

---推理某一层
function ReasoningSystem:TryReasoningSingleLayer(layerId, params)
--	Log.DebugFormat("[ReasoningSystem:TryReasoningSingleLayer] layerId:%d", layerId)
	if self:checkSkipLayer(layerId) then
		-- 跳过当前的层，通知dialogue
		self:OnReasoningLayerEnd(layerId, params, true)
	else
		self:startReasoning(layerId, params)
	end
end

function ReasoningSystem:resetLayerParams()
	self.curLayerId = -1
	self.nextLayerId = -1
	self.curLayerParams = nil
	self.bJudgeSucc = false
end

function ReasoningSystem:updateLayerIds(layerId, params)
	self.curLayerId = layerId
	---默认+1，因为从小到大的顺序
	self.nextLayerId = layerId + 1
	self.curLayerParams = params
	self.curLayerStartTime = _G._now()
end

function ReasoningSystem:startReasoning(layerId, params)
--	Log.DebugFormat("[ReasoningSystem:startReasoning] layerId:%d", layerId)
	self:updateLayerIds(layerId, params)
	ReasoningUtils.OpenReasoningPanel(layerId)
	Game.DialogueManager:EnableMysteryMode()
	self:StartTimer(self.delayPauseDialogueTimerKey, function() Game.DialogueManager:SetDialoguePause(true) end, self.curLayerParams.delayPauseDialogueTime or ReasoningSystem.DelayPauseDialogueTime, 1)
end

---某层推理结束
function ReasoningSystem:OnReasoningLayerEnd(layerId, params, bSkip)
	if not bSkip then
		---正常推理结束，先检查一下layerId是否一致，如果一致，则判断params来决定是否关闭界面
		if self.curLayerId == layerId then
			if self.curLayerParams.bCloseMysteryMode then
				self:closeReasoningPanel()
			end
		else
			Log.ErrorFormat("[ReasoningSystem:OnReasoningLayerEnd] layerId not match, layerId:%d, curLayerId:%s", layerId, self.curLayerId)
		end
		self:tryStopDelayPauseDialogueTimer()
	end
	if ReasoningUtils.CheckLastLayerId(layerId) then
		self:onReasoningAllLayerEnd()
	end
end

---所有层推理结束
function ReasoningSystem:onReasoningAllLayerEnd()
	self:resetLayerParams()
	self:closeReasoningPanel()
end

function ReasoningSystem:closeReasoningPanel()
	ReasoningUtils.CloseReasoningPanel()
	Game.DialogueManager:DisableMysteryMode()
end

---检定结束
function ReasoningSystem:OnJudgeEnd(succ, layerId)
--	Log.DebugFormat("[ReasoningSystem:OnJudgeEnd] succ:%s, layerId:%d", succ, layerId)
	if not succ then
		ReasoningUtils.ShowJudgeFailureTips()
	end
	self.bJudgeSucc = succ
	self.nextLayerId = ReasoningUtils.GetJudgeResultLayerId(succ, layerId)
	self:tryStopDelayPauseDialogueTimer()
	---如果是最后一层检定，那么则需要reset一下相关参数
	if ReasoningUtils.CheckLastLayerId(layerId) then
		self:onReasoningAllLayerEnd()
	end
end

---如果提前结束了，则停掉暂停的timer，如果没有提前结束，说明已经暂停了，需要恢复
function ReasoningSystem:tryStopDelayPauseDialogueTimer()
	local curTime = _G._now()
	local delayPauseDialogueTime = self.curLayerParams.delayPauseDialogueTime or ReasoningSystem.DelayPauseDialogueTime
	---如果提前结束
	local endEarly = curTime - self.curLayerStartTime < delayPauseDialogueTime
	if endEarly then
		self:StopTimer(self.delayPauseDialogueTimerKey)
	else
		Game.DialogueManager:SetDialoguePause(false)
	end
--	Log.DebugFormat("[ReasoningSystem:tryStopDelayPauseDialogueTimer] endEarly:%s", endEarly)
	return endEarly
end

return ReasoningSystem
