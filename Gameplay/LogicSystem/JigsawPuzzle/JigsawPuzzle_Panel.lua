local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local JigsawPuzzle_ClickArea = kg_require("Gameplay.LogicSystem.JigsawPuzzle.JigsawPuzzle_ClickArea")
local UI_DiyText = kg_require("Framework.KGFramework.KGUI.Component.Tools.UI_DiyText")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local StarBtnRotate = kg_require("Gameplay.LogicSystem.MiniGame.StarGraph.StarBtnRotate")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class JigsawPuzzle_Panel : UIPanel
---@field view JigsawPuzzle_PanelBlueprint
local JigsawPuzzle_Panel = DefineClass("JigsawPuzzle_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local TimerConst = kg_require("Framework.KGFramework.KGCore.TimerManager.TimerConst")
local BoolPropDef = kg_require("Shared.BoolPropDefs")
local type = type -- luacheck: ignore
-- 整圆角度
local CircleAngle = 360 -- luacheck: ignore
local TableData = Game.TableData
local LocalCoordinate = FVector2D(0.5, 0.5)
local CircleInitialColor = import("UIFunctionLibrary").SRGBColorFromHex("FFFFFFFF") -- luacheck: ignore
local EdgeInitialColor = import("UIFunctionLibrary").SRGBColorFromHex("403B34FF") -- luacheck: ignore
local CircleLinearColor = FLinearColor(1, 1, 1, 1)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
--region Config

---@class JigsawPuzzleAdsorbInfo
---@field StartRotate number 起始角度
---@field DeltaRotate number 移动朝向

-- 状态枚举
JigsawPuzzle_Panel.EState = {
	Idle = 0, -- 静止
	ClickRotate = 1, -- 点击旋转
	PressRotate = 2, -- 按下旋转
	AutoAdsorb = 3, -- 自动吸附
	Reset = 4, -- 插值重置
	PreFinish = 5, -- 完成前吸附
	Finish = 6, -- 游戏结束
	Stop = 7, -- 停止，不能操作
}

-- 状态方法
JigsawPuzzle_Panel.StateFunc = {
	[JigsawPuzzle_Panel.EState.Idle] = nil,
	[JigsawPuzzle_Panel.EState.ClickRotate] = "inClickRotate",
	[JigsawPuzzle_Panel.EState.PressRotate] = "inPressRotate",
	[JigsawPuzzle_Panel.EState.AutoAdsorb] = "inAutoAdsorb",
	[JigsawPuzzle_Panel.EState.Reset] = "inReset",
	[JigsawPuzzle_Panel.EState.PreFinish] = "inPreFinish",
	[JigsawPuzzle_Panel.EState.Finish] = nil,
	[JigsawPuzzle_Panel.EState.Stop] = nil,
}

-- 各环的属性名
JigsawPuzzle_Panel.RingNames = { "RotateAngle1", "RotateAngle2", "RotateAngle3" }
-- 半径属性名,1最外,3最内
JigsawPuzzle_Panel.RadiusNames = { "Radius1", "Radius2", "Radius3", "Radius4" }
-- 环边缘宽度属性
JigsawPuzzle_Panel.EdgeWidthConf = { EdgeWidth1 = 0.002, EdgeWidth2 = 0.005, EdgeWidth3 = 0.005, EdgeWidth4 = 0.005 }
-- 环颜色属性名
JigsawPuzzle_Panel.CircleColorNames = { "CircleColor1", "CircleColor2", "CircleColor3", "CircleColor4" }
-- 蒙版属性名和对应值
JigsawPuzzle_Panel.OverlapAlphaConf = {
	[1] = { OverlapAlpha1 = 0.04, OverlapAlpha2 = 0, OverlapAlpha3 = 0, OverlapAlpha4 = 0 },
	[2] = { OverlapAlpha1 = 0, OverlapAlpha2 = 0.04, OverlapAlpha3 = 0, OverlapAlpha4 = 0 },
	[3] = { OverlapAlpha1 = 0, OverlapAlpha2 = 0, OverlapAlpha3 = 0.04, OverlapAlpha4 = 0 },
	[4] = { OverlapAlpha1 = 0, OverlapAlpha2 = 0, OverlapAlpha3 = 0, OverlapAlpha4 = 0 },
}

--endregion Config

--region LifeCycle

JigsawPuzzle_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function JigsawPuzzle_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function JigsawPuzzle_Panel:InitUIData()
	---@type number 题目ID
	self.puzzleID = 0
	-- 状态,tick时驱动状态函数
	self.state = JigsawPuzzle_Panel.EState.Stop
	---@type number 按钮按下的开始时间,超过一定阈值才会执行旋转
	self.pressStartTime = 0
	---@type number 点击开始的时间
	self.clickStartTime = 0
	---@type number 点击开始时的角度
	self.clickStartRotate = 0
	---@type number 旋转方向
	self.direction = 0
	---@type number 圆环索引,1(外环),2(中环),3(内环,初始值)
	self.ringIdx = #JigsawPuzzle_Panel.RingNames
	---@type table<number, table<string, ColorTable>> 颜色参数,外层是环索引,内层是变量名-对应值
	self.colorParamMap = {}
	---@type number[] 各环的半径区间
	self.radiusRange = {}
	-- 按钮位置
	self.dragCenterPos = FVector2D()
	-- 上一帧鼠标位置
	self.lastMousePos = FVector2D()
	---@type boolean 是否有过拖动,没有拖动的情况下,不会进行自动贴合
	self.bDragged = false
	---@type number 重置开始的时间
	self.resetStartTime = 0
	---@type JigsawPuzzleAdsorbInfo[] 重置开始时各环属性
	self.resetInfo = {}
	---@type number 吸附量
	self.adsorbDeltaRotate = 0
	---@type number 开始吸附的角度
	self.adsorbStartRotate = 0
	---@type number 开始吸附的时间
	self.adsorbStartTime = 0
	---@type JigsawPuzzleAdsorbInfo[]
	self.preFinishInfos = {}
	---@type number 完成插值开始时间
	self.preFinishStartTime = 0
	---@type boolean 当次按下是否在可按下范围内
	self.bInDragArea = false
	---@type number 视口缩放比例
	self.viewportScale = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
	---@type boolean 旋转点击锁
	self.bIsInClick = false
end

--- UI组件初始化，此处为自动生成
function JigsawPuzzle_Panel:InitUIComponent()
    ---@type UIComButtonItem
    self.Btn_ResetCom = self:CreateComponent(self.view.Btn_Reset, UIComButtonItem)
    ---@type UI_DiyText
    self.WBP_JigsawPuzzleTL_WBP_DIYTextCom = self:CreateComponent(self.view.WBP_JigsawPuzzleTL.WBP_DIYText, UI_DiyText)
    ---@type JigsawPuzzle_ClickArea
    self.WBP_DragAreaCom = self:CreateComponent(self.view.WBP_DragArea, JigsawPuzzle_ClickArea)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type StarBtnRotate
    self.Btn_ClockCom = self:CreateComponent(self.view.Btn_Clock, StarBtnRotate)
    ---@type StarBtnRotate
    self.Btn_AnticlockCom = self:CreateComponent(self.view.Btn_Anticlock, StarBtnRotate)
    ---@type StarBtnRotate
    self.Btn_SwitchCom = self:CreateComponent(self.view.Btn_Switch, StarBtnRotate)
	---@type UIListView
    self.KList_HintCom = self:CreateComponent(self.view.KList_Hint, UIListView)
end

---UI事件在这里注册，此处为自动生成
function JigsawPuzzle_Panel:InitUIEvent()
    self:AddUIEvent(self.Btn_SwitchCom.onClickEvent, "on_Btn_SwitchCom_ClickEvent")
    self:AddUIEvent(self.Btn_ClockCom.onPressedEvent, "on_Btn_ClockCom_PressedEvent")
    self:AddUIEvent(self.Btn_ClockCom.onReleasedEvent, "on_Btn_ClockCom_ReleasedEvent")
    self:AddUIEvent(self.Btn_AnticlockCom.onPressedEvent, "on_Btn_AnticlockCom_PressedEvent")
    self:AddUIEvent(self.Btn_AnticlockCom.onReleasedEvent, "on_Btn_AnticlockCom_ReleasedEvent")
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.WBP_DragAreaCom.onDragStartedEvent, "on_WBP_DragAreaCom_DragStartedEvent")
    self:AddUIEvent(self.WBP_DragAreaCom.onDragMovedEvent, "on_WBP_DragAreaCom_DragMovedEvent")
    self:AddUIEvent(self.WBP_DragAreaCom.onDragEndedEvent, "on_WBP_DragAreaCom_DragEndedEvent")
    self:AddUIEvent(self.Btn_ResetCom.onClickEvent, "on_Btn_ResetCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function JigsawPuzzle_Panel:InitUIView()
	self.view.Img_Slc_Out:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Img_Slc_In:SetVisibility(ESlateVisibility.Collapsed)
	
	local listTableData = {}
	local tipsData = Game.TableData.GetTipsDataRow(Enum.ETipsData.PUZZLE_TIPS)
	if not IsStringNullOrEmpty(tipsData.Description1[1]) then
		listTableData[1] = tipsData.Description1[1]
	end
	if not IsStringNullOrEmpty(tipsData.Description2[1]) then
		listTableData[2] = tipsData.Description2[1]
	end
	if not IsStringNullOrEmpty(tipsData.Description3[1]) then
		listTableData[3] = tipsData.Description3[1]
	end
	if not IsStringNullOrEmpty(tipsData.Description4[1]) then
		listTableData[4] = tipsData.Description4[1]
	end
	self.KList_HintCom:Refresh(listTableData, 1)
	self.Btn_ResetCom:SetType("MiniGameResetting")
	self.Btn_ResetCom:SetName(StringConst.Get("FASHION_RESET"))
end

---面板打开的时候触发
---@param puzzleID number
function JigsawPuzzle_Panel:OnRefresh(puzzleID)
	self.puzzleID = puzzleID
	self.clickRotateUnit = TableData.GetConstDataRow("JIGSAW_PUZZLE_CLICK_ROTATE_ANGLE") / CircleAngle
	self.pressRotate = TableData.GetConstDataRow("JIGSAW_PUZZLE_PRESS_ROTATE_ANGLE") / CircleAngle

	local jigsawPuzzleData = Game.TableData.GetJigsawPuzzleDataRow(self.puzzleID)
	if not jigsawPuzzleData then
		Log.ErrorFormat("[OnShow] get jigsawPuzzleData failed of puzzleID:%s", self.puzzleID)
		return
	end

	self:calcRadiusRange()
	self:genColorParamMap()
	self:SetMaterialTextureParam(self.view.Img_Puzzle,jigsawPuzzleData.Texture, "MainTex")
	
	-- 检查是否玩过拼图解密，只有第一次游玩时显示
	if Game and Game.me then
		local isPlayed = Game.me:getBoolProp(BoolPropDef.IS_PLAYED_MINIGAME_JIGSAWPUZZLE)
		if isPlayed then
			self.KList_HintCom:SetVisible(false) -- 隐藏指引的各个步骤
		else
			Game.me:clientSetBoolProp(BoolPropDef.IS_PLAYED_MINIGAME_JIGSAWPUZZLE, true)
		end
	end
	
	self:PlayAnimation(self.userWidget.Ani_FadeIn, function()
		self:initialRotate()
	end, self.userWidget, 0, 1, EUMGSequencePlayMode.Forward, 1, ture)
	
	-- Update检查拖动旋转
	self:StartTickTimer("JigsawPuzzle Update",function()
		self:OnUpdate()
	end, -1, true)
	
	Log.DebugFormat("[OnShow] start puzzleID:%s", self.puzzleID)
end

function JigsawPuzzle_Panel:OnClose()
	self:switchState(JigsawPuzzle_Panel.EState.Stop)
	self.pressStartTime = 0
	self.direction = 0
	self.ringIdx = 0
	self.adsorbDeltaRotate = 0
	self.colorParamMap = {}
	self.radiusRange = {}
	self.dragCenterPos = FVector2D()
	self.lastMousePos = FVector2D()
	self.bDragged = false
	Game.QuestSystem:ReqJigsawPuzzleClosed(self.puzzleID)
	-- 专门给通用的2D玩法系统上报的关闭
	Game.Gameplay2DSystem:Req2DGameplayClose(Game.Gameplay2DSystem.Enum2DGameplay.JigsawPuzzle, self.puzzleID)
end

function JigsawPuzzle_Panel:OnUpdate()
	local stateFuncName = JigsawPuzzle_Panel.StateFunc[self.state]
	local stateFunc = self[stateFuncName]
	if not stateFunc or type(stateFunc) ~= "function" then
		return
	end

	stateFunc(self)

	-- 吸附过程中不检测结果
	if self.state ~= JigsawPuzzle_Panel.EState.Reset 
		and self.state ~= JigsawPuzzle_Panel.EState.PreFinish 
		and self.state ~= JigsawPuzzle_Panel.EState.Finish 
		and self.state ~= JigsawPuzzle_Panel.EState.Stop then
		self:checkResult()
	end
end

--endregion LifeCycle

--region Event

--- 此处为自动生成
function JigsawPuzzle_Panel:on_Btn_ResetCom_ClickEvent()
	if not self:canTouch() then
		return
	end

	self.resetStartTime = _now()
	self:switchState(JigsawPuzzle_Panel.EState.Reset)
end

--- 此处为自动生成
function JigsawPuzzle_Panel:on_Btn_SwitchCom_ClickEvent()
	if not self:canTouch() then
		return
	end

	if self.ringIdx == 1 then
		self.ringIdx = #JigsawPuzzle_Panel.RingNames
	else
		self.ringIdx = self.ringIdx - 1
	end

	self:onRingIdxChange()
	Log.DebugFormat("[OnClick_Btn_Switch_Btn_ClickArea] ringIdx=%s", self.ringIdx)
end

--- 此处为自动生成
function JigsawPuzzle_Panel:on_Btn_ClockCom_PressedEvent()
	if not self:canTouch() then
		return
	end

	self:switchState(JigsawPuzzle_Panel.EState.PressRotate)
	self.direction = -1
	self.pressStartTime = _now()
	self.bIsInClick = true
end

--- 此处为自动生成
function JigsawPuzzle_Panel:on_Btn_ClockCom_ReleasedEvent()
	if not self:canRelease() then
		return
	end

	-- 抬起时如果在点击时间内, 则执行点击逻辑
	if _now() - self.pressStartTime < TableData.GetConstDataRow("JIGSAW_PUZZLE_CLICK_TIME_RANGE") then
		self.clickStartTime = _now()
		self.clickStartRotate = self:getImageRotate()
		self:switchState(JigsawPuzzle_Panel.EState.ClickRotate)
	else
		-- 抬起时如果不在点击时间内,执行自动贴合
		self:calcAdsorbTarget()
		self:switchState(JigsawPuzzle_Panel.EState.AutoAdsorb)
	end

	self.pressStartTime = 0
end

--- 此处为自动生成
function JigsawPuzzle_Panel:on_Btn_AnticlockCom_PressedEvent()
	if not self:canTouch() then
		return
	end

	self:switchState(JigsawPuzzle_Panel.EState.PressRotate)
	self.direction = 1
	self.pressStartTime = _now()
	self.bIsInClick = true
end

--- 此处为自动生成
function JigsawPuzzle_Panel:on_Btn_AnticlockCom_ReleasedEvent()
	if not self:canRelease() then
		return
	end

	if _now() - self.pressStartTime < TableData.GetConstDataRow("JIGSAW_PUZZLE_CLICK_TIME_RANGE") then
		-- 抬起时如果在点击时间内,则执行点击逻辑
		self.clickStartTime = _now()
		self.clickStartRotate = self:getImageRotate()
		self:switchState(JigsawPuzzle_Panel.EState.ClickRotate)
	else
		-- 抬起时如果不在点击时间内,执行自动贴合
		self:calcAdsorbTarget()
		self:switchState(JigsawPuzzle_Panel.EState.AutoAdsorb)
	end

	self.pressStartTime = 0
end

--- 此处为自动生成
---@param InGeometry FGeometry
---@param InGestureEvent FPointerEvent
function JigsawPuzzle_Panel:on_WBP_DragAreaCom_DragStartedEvent(InGeometry, InGestureEvent)
	if not self:canTouch() then
		return
	end

	-- 计算鼠标距离中心点距离
	LocalCoordinate.X = 0.5
	LocalCoordinate.Y = 0.5
	self.dragCenterPos = import("UIFunctionLibrary").GetAbsolutePosition(self.view.WBP_DragArea, LocalCoordinate) / self.viewportScale
	local touchPos = import("KismetInputLibrary").PointerEvent_GetScreenSpacePosition(InGestureEvent)
	import("SlateBlueprintLibrary").ScreenToWidgetLocal(_G.GetContextObject(), InGeometry, touchPos, LocalCoordinate, false)
	touchPos = touchPos / self.viewportScale

	local distance = import("KismetMathLibrary").Distance2D(self.dragCenterPos, touchPos)

	for idx = 1, (#self.radiusRange - 1) do
		if self.radiusRange[idx + 1] <= distance and distance < self.radiusRange[idx] then
			-- 落在当前区间内
			self.ringIdx = idx
			self:onRingIdxChange()
			-- 记录当前位置
			self.lastMousePos = touchPos
			-- 标记为可拖动
			self.bInDragArea = true
			break
		end
	end
end

--- 此处为自动生成
---@param InGeometry FGeometry
---@param InGestureEvent FPointerEvent
function JigsawPuzzle_Panel:on_WBP_DragAreaCom_DragMovedEvent(InGeometry, InGestureEvent)
	if not self.bInDragArea then
		return
	end

	LocalCoordinate.X = 0.5
	LocalCoordinate.Y = 0.5
	local touchPos = import("KismetInputLibrary").PointerEvent_GetScreenSpacePosition(InGestureEvent)
	import("SlateBlueprintLibrary").ScreenToWidgetLocal(_G.GetContextObject(), InGeometry, touchPos, LocalCoordinate, false)
	touchPos = touchPos / self.viewportScale

	-- 计算向量
	local center2Touch = touchPos - self.dragCenterPos
	local center2Last = self.lastMousePos - self.dragCenterPos
	-- 归一化
	center2Last = import("KismetMathLibrary").NormalSafe2D(center2Last, 0.00000001)
	center2Touch = import("KismetMathLibrary").NormalSafe2D(center2Touch, 0.00000001)
	-- 计算角度
	local dotProduct = import("KismetMathLibrary").DotProduct2D(center2Last, center2Touch)
	-- 角度数值规范 note@shijingzhe: 手机上容易算出nan
	if dotProduct < -1 then
		dotProduct = -1
	elseif 1 < dotProduct then
		dotProduct = 1
	end
	local deltaRotate = import("KismetMathLibrary").RadiansToDegrees(math.acos(dotProduct)) / CircleAngle

	-- 变化小于0.001,不处理
	if math.abs(deltaRotate) < 0.001 then
		return
	end

	-- 计算方向
	self.direction = import("KismetMathLibrary").CrossProduct2D(center2Last, center2Touch) > 0 and -1 or 1
	self:addImageRotate(deltaRotate * self.direction)
	self.lastMousePos = touchPos
	self.bDragged = true
end

--- 此处为自动生成
---@param InGeometry FGeometry
---@param InGestureEvent FPointerEvent
function JigsawPuzzle_Panel:on_WBP_DragAreaCom_DragEndedEvent(InGeometry, InGestureEvent)
	self.bInDragArea = false

	if not self:canTouch() then
		return
	end

	if self.bDragged then
		self:calcAdsorbTarget()
		self:switchState(JigsawPuzzle_Panel.EState.AutoAdsorb)
		self.bDragged = false
	else
		self:switchState(JigsawPuzzle_Panel.EState.Idle)
	end
end

--- 此处为自动生成
function JigsawPuzzle_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
	Game.NewUIManager:ClosePanel(self.uid)
end

--endregion Event

--region StateFunc

---@private
---@param state number
function JigsawPuzzle_Panel:switchState(state)
	Log.DebugFormat("JigsawPuzzle %s -> %s", self.state, state)
	self.state = state
	if self.state == JigsawPuzzle_Panel.EState.Finish then
		self:onSuccess()
	end
end

---@private
function JigsawPuzzle_Panel:inClickRotate()
	local alpha = (_now() - self.clickStartTime) / TableData.GetConstDataRow("JIGSAW_PUZZLE_CLICK_ROTATE_TIME")
	alpha = alpha > 1 and 1 or alpha
	local rotate = self.clickRotateUnit * alpha * self.direction
	self:setImageRotate(self.clickStartRotate + rotate)
	if alpha == 1 then
		self.bIsInClick = false
		self:switchState(JigsawPuzzle_Panel.EState.Idle)
	end
end

---@private
function JigsawPuzzle_Panel:inPressRotate()
	if _now() - self.pressStartTime < TableData.GetConstDataRow("JIGSAW_PUZZLE_CLICK_TIME_RANGE") then
		return
	end
	
	local deltaTime = TimerConst.TickTime * 0.001
	local deltaRotate = deltaTime * self.pressRotate * self.direction
	self:addImageRotate(deltaRotate)
end

---@private
function JigsawPuzzle_Panel:inAutoAdsorb()
	local alpha = (_now() - self.adsorbStartTime) / TableData.GetConstDataRow("JIGSAW_PUZZLE_CLICK_ROTATE_TIME")
	alpha = alpha > 1 and 1 or alpha
	local rotate = self.adsorbStartRotate + self.adsorbDeltaRotate * alpha
	self:setImageRotate(rotate)
	if alpha == 1 then
		self.bIsInClick = false
		self:switchState(JigsawPuzzle_Panel.EState.Idle)
	end
end

---@private
function JigsawPuzzle_Panel:inReset()
	-- 获取旋转角度
	local initialRotates = Game.TableData.GetJigsawPuzzleDataRow(self.puzzleID).InitialRotate
	for ringIdx, _ in ipairs(JigsawPuzzle_Panel.RingNames) do
		---@type JigsawPuzzleAdsorbInfo
		local info = {}
		info.StartRotate = self:getImageRotate(ringIdx)
		info.DeltaRotate = initialRotates[ringIdx] / CircleAngle - info.StartRotate
		self.resetInfo[ringIdx] = info
	end
	
	-- 如果不需要旋转,则直接切换状态
	local needRotate = false
	for ringIdx, _ in ipairs(JigsawPuzzle_Panel.RingNames) do
		if self.resetInfo[ringIdx].DeltaRotate ~= 0 then
			needRotate = true
			break
		end
	end
	
	if not needRotate then
		self.ringIdx = #JigsawPuzzle_Panel.RingNames
		self:onRingIdxChange()
		self:switchState(JigsawPuzzle_Panel.EState.Idle)
		return
	end
	
	-- 旋转
	local clearNum = 0
	for ringIdx, _ in ipairs(JigsawPuzzle_Panel.RingNames) do
		local resetInfo = self.resetInfo[ringIdx]
		-- 因为是在 Update 里面触发所以可以计算旋转进度
		local alpha = (_now() - self.resetStartTime) / TableData.GetConstDataRow("JIGSAW_PUZZLE_RESET_ROTATE_TIME")
		alpha = alpha > 1 and 1 or alpha
		local rotate = resetInfo.DeltaRotate * alpha
		self:setImageRotate(resetInfo.StartRotate + rotate, ringIdx)
		if alpha == 1 then
			clearNum = clearNum + 1
		end
	end
	
	-- 若所有环都旋转,则切换状态
	if clearNum == #JigsawPuzzle_Panel.RingNames then
		self.ringIdx = #JigsawPuzzle_Panel.RingNames
		self:onRingIdxChange()
		self:switchState(JigsawPuzzle_Panel.EState.Idle)
	end
  end

---@private
function JigsawPuzzle_Panel:inPreFinish()
	local alpha = (_now() - self.preFinishStartTime) / TableData.GetConstDataRow("JIGSAW_PUZZLE_RESET_ROTATE_TIME")
	alpha = alpha > 1 and 1 or alpha

	local clearNum = 0
	for ringIdx, _ in ipairs(JigsawPuzzle_Panel.RingNames) do
		local preFinishInfo = self.preFinishInfos[ringIdx]
		local rotate = preFinishInfo.DeltaRotate * alpha
		self:setImageRotate(preFinishInfo.StartRotate + rotate, ringIdx)
		if alpha == 1 then
			clearNum = clearNum + 1
		end
	end

	-- 描边插值
	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	for edgeWidthName, initialEdgeWidth in pairs(JigsawPuzzle_Panel.EdgeWidthConf) do
		local newEdgeWidth = initialEdgeWidth * (1 - alpha)
		if newEdgeWidth < 0 then
			newEdgeWidth = 0
		end
		dynamicMaterial:SetScalarParameterValue(edgeWidthName, newEdgeWidth)
	end

	-- 未被选中环的颜色插值
	local curIdxInitialColor = self.colorParamMap[self.ringIdx]
	for idx, colorParamName in ipairs(JigsawPuzzle_Panel.CircleColorNames) do
		if idx == self.ringIdx then
			goto continue
		end

		local curInitialColorParam = curIdxInitialColor[colorParamName]
		local newR = (CircleInitialColor.R - curInitialColorParam.R) * alpha + curInitialColorParam.R
		local newG = (CircleInitialColor.G - curInitialColorParam.G) * alpha + curInitialColorParam.G
		local newB = (CircleInitialColor.B - curInitialColorParam.B) * alpha + curInitialColorParam.B
		CircleLinearColor.R = newR
		CircleLinearColor.G = newG
		CircleLinearColor.B = newB
		dynamicMaterial:SetVectorParameterValue(colorParamName, CircleLinearColor)
		:: continue ::
	end

	-- 选中环的描边颜色插值

	if clearNum == #JigsawPuzzle_Panel.RingNames then
		self:switchState(JigsawPuzzle_Panel.EState.Finish)
	end
end

--endregion StateFunc

--region Common

---设置初始旋转
---@private
function JigsawPuzzle_Panel:initialRotate()
	local jigsawPuzzleData = Game.TableData.GetJigsawPuzzleDataRow(self.puzzleID)
	if not jigsawPuzzleData then
		return
	end

	self.resetStartTime = _now()
	self:switchState(JigsawPuzzle_Panel.EState.Reset)
	
	---- 启动定时器
	self:StartTimer("RotateAnimation", function()
		self:initPuzzle()
	end, TableData.GetConstDataRow("JIGSAW_PUZZLE_RESET_ROTATE_TIME"), 1, true, false, nil)
end

-- 设置初始解密
---@private
function JigsawPuzzle_Panel:initPuzzle()
	self.view.Ring_Finished:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Ring_Unfinished:SetVisibility(ESlateVisibility.Visible)
	self.view.Btn_Clock:SetVisibility(ESlateVisibility.Visible)
	self.view.Btn_Anticlock:SetVisibility(ESlateVisibility.Visible)
	self.view.Btn_Reset:SetVisibility(ESlateVisibility.Visible)
	self.view.Btn_Switch:SetVisibility(ESlateVisibility.Visible)
	self.view.Img_Slc_Out:SetVisibility(ESlateVisibility.Visible)
	self.view.Img_Slc_In:SetVisibility(ESlateVisibility.Visible)

	self.dragCenterPos = import("UIFunctionLibrary").GetAbsolutePosition(self.view.WBP_DragArea, LocalCoordinate) / self.viewportScale

	self.ringIdx = #JigsawPuzzle_Panel.RingNames
	self.state = JigsawPuzzle_Panel.EState.Idle
	self:onRingIdxChange()
	self:setInitialEdgeWidth()
end

-- 生成FLinearColor对象映射
---@private
function JigsawPuzzle_Panel:genColorParamMap()
	local jigsawPuzzleColorData = Game.TableData.GetJigsawPuzzleColorDataTable()
	if not jigsawPuzzleColorData then
		Log.Error("[OnShow] no JigsawPuzzleColorData")
		return
	end

	for idx, data in ksbcpairs(jigsawPuzzleColorData) do
		local paramRow = {}
		for paramName, sRGB in ksbcpairs(data) do
			if paramName == "RingIdx" then
				goto continue
			end

			paramRow[paramName] = import("UIFunctionLibrary").SRGBColorFromHex(sRGB)
			:: continue ::
		end
		self.colorParamMap[idx] = paramRow
	end
end

function JigsawPuzzle_Panel:setInitialEdgeWidth()
	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	for paramName, paramVal in pairs(JigsawPuzzle_Panel.EdgeWidthConf) do
		dynamicMaterial:SetScalarParameterValue(paramName, paramVal)
	end
end

---@private
---@param deltaRotate number
function JigsawPuzzle_Panel:addImageRotate(deltaRotate)
	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	local rotate = dynamicMaterial:K2_GetScalarParameterValue(JigsawPuzzle_Panel.RingNames[self.ringIdx])
	dynamicMaterial:SetScalarParameterValue(JigsawPuzzle_Panel.RingNames[self.ringIdx], (rotate + deltaRotate) % 1)
end

---@private
---@param rotate number
---@param ringIdx number
function JigsawPuzzle_Panel:setImageRotate(rotate, ringIdx)
	if not ringIdx then
		ringIdx = self.ringIdx
	end

	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	dynamicMaterial:SetScalarParameterValue(JigsawPuzzle_Panel.RingNames[ringIdx], rotate)
end

---@private
---@param ringIdx number
---@return number
function JigsawPuzzle_Panel:getImageRotate(ringIdx)
	if not ringIdx then
		ringIdx = self.ringIdx
	end

	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	return dynamicMaterial:K2_GetScalarParameterValue(JigsawPuzzle_Panel.RingNames[ringIdx])
end

---@private
function JigsawPuzzle_Panel:checkResult()
	if not self:canTouch() then
		return
	end

	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	for _, ringName in ipairs(JigsawPuzzle_Panel.RingNames) do
		local rotate = math.abs(dynamicMaterial:K2_GetScalarParameterValue(ringName))
		local diff = (rotate > 0.5) and (1 - rotate) or rotate
		if diff > self.clickRotateUnit then
			return
		end
	end

	Log.Debug("[checkResult] all rings checked, go to finish adsorb")
	self:switchState(JigsawPuzzle_Panel.EState.PreFinish)
	self.preFinishStartTime = _now()
	for ringIdx, ringName in ipairs(JigsawPuzzle_Panel.RingNames) do
		local rotate = math.abs(dynamicMaterial:K2_GetScalarParameterValue(ringName))
		---@type JigsawPuzzleAdsorbInfo
		local info = {}
		info.StartRotate = rotate
		info.DeltaRotate = (rotate > 0.5) and (1 - rotate) or (rotate * -1)
		self.preFinishInfos[ringIdx] = info
	end

	self.view.Img_Slc_Out:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Img_Slc_In:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Btn_Clock:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Btn_Anticlock:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Btn_Reset:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Btn_Switch:SetVisibility(ESlateVisibility.Collapsed)
end

---@private
function JigsawPuzzle_Panel:onSuccess()
	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	for edgeWidthName, _ in pairs(JigsawPuzzle_Panel.EdgeWidthConf) do
		dynamicMaterial:SetScalarParameterValue(edgeWidthName, 0)
	end

	self.view.Ring_Unfinished:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Ring_Finished:SetVisibility(ESlateVisibility.Visible)

	self.ringIdx = #JigsawPuzzle_Panel.RingNames + 1
	self:onRingIdxChange()

	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.INTERACTOR_GAME_RESULT, true)

	-- 上报完成
	if Game.me then
		Game.me:ReqStartJigsawPuzzleSuccess(self.puzzleID)
	end
	-- 给通用的2D玩法系统上报完成
	Game.Gameplay2DSystem:Req2DGameplayFinish(Game.Gameplay2DSystem.Enum2DGameplay.JigsawPuzzle, self.puzzleID, Game.Gameplay2DSystem.Enum2DGameplayResult.Success)

	self:PlayAnimation(self.userWidget.Ani_finish, nil, self.userWidget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	
	Game.TimerManager:CreateTimerAndStart(function()
		Game.NewUIManager:ClosePanel(UIPanelConfig.JigsawPuzzle_Panel)
	end, 2000, 1)
end

-- 计算自动吸附所需要的角度
---@private
function JigsawPuzzle_Panel:calcAdsorbTarget()
	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	local rotate = dynamicMaterial:K2_GetScalarParameterValue(JigsawPuzzle_Panel.RingNames[self.ringIdx])
	self.adsorbStartRotate = rotate
	self.adsorbStartTime = _now()
	if self.direction > 0 then
		local adsorbTargetRotate = (math.floor(rotate / self.clickRotateUnit) + 1) * self.clickRotateUnit
		self.adsorbDeltaRotate = adsorbTargetRotate - rotate
	else
		local adsorbTargetRotate = math.floor(rotate / self.clickRotateUnit) * self.clickRotateUnit
		self.adsorbDeltaRotate = adsorbTargetRotate - rotate
	end
end

---@private
---@return boolean
function JigsawPuzzle_Panel:canTouch()
	if self.bIsInClick then
		return false
	end

	if (self.state ~= JigsawPuzzle_Panel.EState.Idle) and (self.state ~= JigsawPuzzle_Panel.EState.PressRotate) then
		return false
	end

	return true
end

---@private
---@return boolean
function JigsawPuzzle_Panel:canRelease()
	if self.state == JigsawPuzzle_Panel.EState.Reset then
		return false
	end

	return true
end

---@private
function JigsawPuzzle_Panel:calcRadiusRange()
	local btnSize = self.view.Img_Puzzle.Brush.ImageSize.X
	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	for _, paramName in ipairs(JigsawPuzzle_Panel.RadiusNames) do
		local radiusRate = dynamicMaterial:K2_GetScalarParameterValue(paramName)
		table.insert(self.radiusRange, radiusRate * btnSize)
	end
end

---@private
function JigsawPuzzle_Panel:onRingIdxChange()
	-- 切换区域颜色和描边颜色
	local dynamicMaterial = self.view.Img_Puzzle:GetDynamicMaterial()
	local paramRow = self.colorParamMap[self.ringIdx]
	for paramName, paramVal in pairs(paramRow) do
		dynamicMaterial:SetVectorParameterValue(paramName, paramVal)
	end

	-- 切换高亮蒙版
	local overlapAlphaRow = self.OverlapAlphaConf[self.ringIdx]
	for paramName, paramVal in pairs(overlapAlphaRow) do
		dynamicMaterial:SetScalarParameterValue(paramName, paramVal)
	end

	self.userWidget:SetSlcCircle(self.ringIdx - 1)
end

--endregion Common

return JigsawPuzzle_Panel
