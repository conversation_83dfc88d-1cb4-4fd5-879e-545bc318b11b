local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---元素核心 - 天赋收益属性条目
---@class EleCoreTypeNameItem : UIListItem
---@field view EleCoreTypeNameItemBlueprint
local EleCoreTypeNameItem = DefineClass("EleCoreTypeNameItem", UIListItem)

EleCoreTypeNameItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EleCoreTypeNameItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EleCoreTypeNameItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function EleCoreTypeNameItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function EleCoreTypeNameItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EleCoreTypeNameItem:InitUIView()
end

---面板打开的时候触发
function EleCoreTypeNameItem:OnRefresh(ListData)
	self.view.KText_Name_lua:SetText(ListData.Name)
	self.view.KText_Number_lua:SetText(string.format("+%s", ListData.Value))
end

return EleCoreTypeNameItem
