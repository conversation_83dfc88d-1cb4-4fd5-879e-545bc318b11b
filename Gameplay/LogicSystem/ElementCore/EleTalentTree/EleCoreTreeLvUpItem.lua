local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class EleCoreTreeLvUpItem : UIListItem
---@field view EleCoreTreeLvUpItemBlueprint
local EleCoreTreeLvUpItem = DefineClass("EleCoreTreeLvUpItem", UIListItem)

local ESlateVisibility = import("ESlateVisibility")

EleCoreTreeLvUpItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EleCoreTreeLvUpItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EleCoreTreeLvUpItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function EleCoreTreeLvUpItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function EleCoreTreeLvUpItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EleCoreTreeLvUpItem:InitUIView()
end

---面板打开的时候触发
function EleCoreTreeLvUpItem:OnRefresh(Data)
	self.userWidget:Event_UI_Style(self.index // 2 ~= 0)
	self.view.Text_Arrt_lua:SetText(Data[1])
	self.view.Text_From_lua:SetText(Data[2])
	self.view.Text_To_lua:SetText(Data[3])
	if Data[4] == true then
		self.view.Text_From_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_Btn_lua:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Text_From_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Img_Btn_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end

return EleCoreTreeLvUpItem
