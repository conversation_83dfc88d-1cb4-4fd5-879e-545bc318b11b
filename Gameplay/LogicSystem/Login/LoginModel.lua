---@class LoginModel:SystemModelBase
local LoginModel = DefineClass("LoginModel", SystemModelBase)

local GameplayStatics = import("GameplayStatics")
local json = require "Framework.Library.json"

LoginModel.SAVE_GAME_SLOT = "LoginSaveGame"
LoginModel.BP_LoginSaveGame = UIAssetPath.BP_LoginSaveGame

function LoginModel:init()
    self.SaveObj = nil
    self.sdkLoginData = nil
    self.roleListData = {}		--从gm后台获取的已有角色列表，按照最后登录时间排序
	self.serverRoleList = {}	--服务器对应的已有角色列表
    self.serverLoginData = { URL = "", Username = nil, Password = nil, ClientVersion = nil, GameId = nil, GameToken = nil, ServerId = "", ServerName = "", }
	self.AccountID = nil
	self.accountQueueServerID = nil		--正在排队的服务器ID
    self:initSaveData()
end

function LoginModel:initSaveData()
    if GameplayStatics.DoesSaveGameExist(LoginModel.SAVE_GAME_SLOT, 0) then
        Log.Debug("InitSaveData Has Save Data, all data load")
        self.SaveObj = GameplayStatics.LoadGameFromSlot(LoginModel.SAVE_GAME_SLOT, 0)
    end

    if not self.SaveObj then
        Log.Debug("InitSaveData No Save Data")
        self.SaveObj = GameplayStatics.CreateSaveGameObject(slua.loadClass(LoginModel.BP_LoginSaveGame))
        Log.Debug("LoadObject", slua.loadObject(LoginModel.BP_LoginSaveGame))
        Log.Debug("SaveObj", self.SaveObj)
    end
end

function LoginModel:GetLoginSaveObj()
    if self.SaveObj == nil then
        self:initSaveData()
    end
    return self.SaveObj
end

function LoginModel:SetRoleRecordData(serverRoleList)
	self.serverRoleList = {}
    if serverRoleList and #serverRoleList > 0 then
        for i = #serverRoleList, 1, -1 do
			local serverRole = serverRoleList[i]
            if string.isEmpty(serverRole.extend) then
                table.remove(serverRoleList, i)
            else
				if type(serverRole.extend) == "string" then
					serverRole.extend = json.decode(serverRole.extend)
				end
				
				if not self.serverRoleList[serverRole.serverId] then
					self.serverRoleList[serverRole.serverId] = {}
				end
				table.insert(self.serverRoleList[serverRole.serverId], serverRole)
			end
        end
        table.sort(serverRoleList, function(a, b)
			if a.lastLoginTime - a.lastLogoutTime > 0 or b.lastLoginTime - b.lastLogoutTime > 0 then
				if a.lastLoginTime - a.lastLogoutTime > 0 and b.lastLoginTime - b.lastLogoutTime > 0 then
					return a.lastLoginTime > b.lastLoginTime
				end
				if a.lastLoginTime - a.lastLogoutTime > 0 then
					return true
				end
				return false
			end
			if a.lastLogoutTime == b.lastLogoutTime then
				return a.ctime > b.ctime
			end
			
			if a.lastLogoutTime == 0 or b.lastLogoutTime == 0 then
				return b.lastLogoutTime ~= 0
            end
            return a.lastLogoutTime > a.lastLogoutTime
        end)
	end
    self.roleListData = serverRoleList
end

function LoginModel:SetAccountQueueServerID(gameAccountInfo)
	if gameAccountInfo and gameAccountInfo.extend and gameAccountInfo.extend.queueStatus == Enum.SERVER_STATUS.NORMAL then
		self.accountQueueServerID = gameAccountInfo.serverId
	else
		self.accountQueueServerID = nil
	end
end

function LoginModel:SetSdkLoginData(data)
    self.sdkLoginData = data
end

function LoginModel:SetAccountID(accountID)
	self.AccountID = accountID
end

function LoginModel:GetAccountID()
	return self.AccountID
end

function LoginModel:SetServerLoginData(url, serverId, serverName, username, password, gameId, gameToken)
	local clientVersion = import("VersionCenter").GetAppVersionString()
	self.serverLoginData.URL = url
	self.serverLoginData.ServerId = serverId
	self.serverLoginData.ServerName = serverName
	local LoginSaveObj = self:GetLoginSaveObj()
	
	if username then --排队切换服务器时，只变更server,用户信息不需要修改
		self.serverLoginData.Username = username
		self.serverLoginData.Password = password
		self.serverLoginData.ClientVersion = clientVersion
		self.serverLoginData.GameId = gameId
		self.serverLoginData.GameToken = gameToken
		
		LoginSaveObj.Account = username
		LoginSaveObj.PassWord = password
	end

	LoginSaveObj.PlatformServerName = serverName
	LoginSaveObj.ServerId = serverId
	LoginSaveObj.Url = url
	GameplayStatics.SaveGameToSlot(LoginSaveObj, LoginModel.SAVE_GAME_SLOT, 0)
	
	Log.DebugFormat("[SetServerLoginData] URL=%s, Username=%s, ClientVersion=%s, ServerId=%s", url, username, clientVersion, serverId)
end

function LoginModel:unInit()
    self.SaveObj = nil
	self.AccountID = nil
    table.clear(self.roleListData)
end

return LoginModel