local StringConst = require "Data.Config.StringConst.StringConst"
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class LoginServerSelect_Panel : UIPanel
---@field view LoginServerSelect_PanelBlueprint
local LoginServerSelect_Panel = DefineClass("LoginServerSelect_Panel", UIPanel)

LoginServerSelect_Panel.eventBindMap = {
	[_G.EEventTypes.LOGIN_QUEUE_UPDATE] = "setServerQueueInfo",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function LoginServerSelect_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function LoginServerSelect_Panel:InitUIData()
	self.groupDataList = {}        --所有的服务器分组数据列表
	self.curSelectGroupIndex = nil  --上一个选中的组页签
	self.bQueueSwitchServer = false		--是否是为了切换服务器
end

--- UI组件初始化，此处为自动生成
function LoginServerSelect_Panel:InitUIComponent()
    ---@type UIListView childScript: Login_Tab_Item
    self.ServerGroupListFoldCom = self:CreateComponent(self.view.ServerGroupListFold, UIListView)
    ---@type UIListView
    self.Server_TileViewCom = self:CreateComponent(self.view.Server_TileView, UIListView)
    ---@type UIComButton
    self.Btn_CloseCom = self:CreateComponent(self.view.Btn_Close, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function LoginServerSelect_Panel:InitUIEvent()
    self:AddUIEvent(self.ServerGroupListFoldCom.onItemSelected, "on_ServerGroupListFoldCom_ItemSelected")
	self:AddUIEvent(self.Server_TileViewCom.onItemClicked, "on_Server_TileViewCom_ItemClicked")
	self:AddUIEvent(self.Btn_CloseCom.onClickEvent, "on_Btn_CloseCom_ClickEvent")
	self:AddUIEvent(self.view.Btn_Info.OnClicked, "on_Btn_Info_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function LoginServerSelect_Panel:InitUIView()
end

---面板打开的时候触发
function LoginServerSelect_Panel:OnRefresh(bQueueSwitchServer)
	self.bQueueSwitchServer = bQueueSwitchServer
	self:initServerData()
	self.ServerGroupListFoldCom:Refresh(self.groupDataList)
	self.ServerGroupListFoldCom:SetSelectedItemByIndex(1, true)
end

---@private initServerData 整合服务器分组信息,将推荐服务器放到分组列表中
function LoginServerSelect_Panel:initServerData()
	local serverList = Game.AllInSdkManager.SdkData.ServerListData
	if serverList.recommendData and next(serverList.recommendData) ~= nil then
		local recommendGroupName = StringConst.Get("RECOMMEND_SERVER")
		self.groupDataList[#self.groupDataList + 1] = {groupName = recommendGroupName,serverList = {serverList.recommendData}}
	end
	for _, group in pairs(serverList.groupDatas) do
		self.groupDataList[#self.groupDataList + 1] = group
	end

	--初始化已有角色数据,并将已有角色服务器信息放到分组列表中
	local tmpRecordLoginRoleData = Game.LoginSystem:GetRoleRecordData()
	if tmpRecordLoginRoleData and #tmpRecordLoginRoleData > 0  then
		local tmpServerList = {}
		for i, v in pairs(tmpRecordLoginRoleData) do
			local serverInfo = Game.AllInSdkManager.SdkData.AllServerMap[v.serverId]
			if serverInfo then
				tmpServerList[#tmpServerList + 1] = {serverInfo = serverInfo, roleData = v}
			end
		end
		if #tmpServerList > 0 then
			local SERVER_STATUS = Enum.SERVER_STATUS
			--把熔断中的服务器放在列表最后，但是不改变其他服务器原始顺序
			local insertIndex = #tmpServerList + 1
			for i = #tmpServerList, 1, -1 do
				local serverInfo = tmpServerList[i].serverInfo
				if (serverInfo.serverStatus == SERVER_STATUS.EXCEED_MAX_QUEUE_NUM) then
					if i ~= insertIndex - 1 then
						table.insert(tmpServerList, insertIndex, tmpServerList[i])
						table.remove(tmpServerList, i)
					end
					insertIndex = insertIndex - 1
				end
			end
			table.insert(self.groupDataList,1 ,{groupName = StringConst.Get("EXIST_ROLE"),serverList = tmpServerList})
		end
	end
end

--- 此处为自动生成
---@param index number
---@param data table
function LoginServerSelect_Panel:on_ServerGroupListFoldCom_ItemSelected(groupIndex, data)
	if groupIndex == self.curSelectGroupIndex then
		return
	end
	if self.curSelectGroupIndex ~= nil then  --第一次打开界面，不播放页签点击音效
		Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common_Tab, true)
	end
	local serverGroupItem = self.ServerGroupListFoldCom:GetItemByIndex(groupIndex)
	if serverGroupItem then
		serverGroupItem:SetSelectUI(true)
		local prevServerGroupItem = self.ServerGroupListFoldCom:GetItemByIndex(self.curSelectGroupIndex)
		prevServerGroupItem:SetSelectUI(false)
	end
	self.curSelectGroupIndex = groupIndex
	self.Server_TileViewCom:Refresh(self.groupDataList[groupIndex].serverList)
end

--- 此处为自动生成
---@param index number
---@param data table
function LoginServerSelect_Panel:on_Server_TileViewCom_ItemClicked(index, data)
	local info = self.groupDataList[self.curSelectGroupIndex].serverList[index]
	local serverInfo = info.roleData ~= nil and info.serverInfo or info
	if serverInfo.serverStatus == Enum.SERVER_STATUS.EXCEED_MAX_ACCOUNT_NUM and not Game.LoginSystem:GetServerRoleList()[serverInfo.serverId] then --服务器注册熔断，且没创建过角色
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.LOGIN_REGIST_LIMITED)
		return
	elseif serverInfo.serverStatus == Enum.SERVER_STATUS.EXCEED_MAX_QUEUE_NUM then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.LOGIN_QUEUE_LIMITED)
		return
	end
	if self.bQueueSwitchServer and Game.LoginSystem:GetServerLoginData().ServerId == serverInfo.serverId then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.LOGIN_QUEUEING_SERVER)
		return
	end

	Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common_lvl2, true)
	Game.AllInSdkManager:Track(Enum.EOperatorTrackType.Game_Server_Confirm,{result = "1", errorMsg = ""},0)
	Game.CreateRoleSystem:SetRoleID( info.roleData ~= nil and info.roleData.roleId or nil)

	if self.bQueueSwitchServer then
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.LOGIN_QUEUE_SWITCH_SERVER, function()
			self:CloseSelf()
			Game.NewUIManager:ClosePanel("ServerQueuePanel")
			Game.LoginSystem:SwitchServer(serverInfo)
		end, nil ,{Game.LoginSystem:GetServerLoginData().ServerName})
	else
		Game.EventSystem:Publish(_G.EEventTypes.PLATFORM_CHANGE_SERVER, serverInfo)
		self:CloseSelf()
	end
end

function LoginServerSelect_Panel:setServerQueueInfo()
	local accountEntity = Game.NetworkManager.GetAccountEntity()
	if not accountEntity.bInServerQueue then
		self:CloseSelf()
	end
end

--- 此处为自动生成
function LoginServerSelect_Panel:on_Btn_Info_Clicked()
	Game.TipsSystem:ShowTips(Enum.ETipsData.LOGIN_CIRCUIT_BREAKER_TIP, self.view.Btn_Info:GetCachedGeometry())
end

--- 此处为自动生成
function LoginServerSelect_Panel:on_Btn_CloseCom_ClickEvent()
	self:CloseSelf()
end

function LoginServerSelect_Panel:OnClose()
	table.clear(self.allServerMap)
	table.clear(self.groupDataList)
	self.curSelectGroupIndex = nil  --上一个选中的组页签
end

return LoginServerSelect_Panel
