local ESlateVisibility = import("ESlateVisibility")
local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class LoginServerItem : UIListItem
---@field view LoginServerItemBlueprint
local LoginServerItem = DefineClass("LoginServerItem", UIListItem)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function LoginServerItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function LoginServerItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function LoginServerItem:InitUIComponent()
    ---@type SocialHead
    self.WBP_HeadCom = self:CreateComponent(self.view.WBP_Head, SocialHead)
end

---UI事件在这里注册，此处为自动生成
function LoginServerItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function LoginServerItem:InitUIView()
end

---面板打开的时候触发
function LoginServerItem:OnRefresh(info)
	local hadRole = info.roleData ~= nil
	local serverStatusAnim = ""
	if hadRole then --有角色信息的话 显示对应的已有角色服务器信息
		self.view.Role_View:SetVisibility(ESlateVisibility.Visible)
		self.view.Server_View:SetVisibility(ESlateVisibility.Collapsed)

		self.view.Role_Name_Text:SetText(info.roleData.extend.Name)
		self.view.Login_Time_Text:SetText(self:loginTimeFormat(info.roleData.lastLoginTime, info.roleData.lastLogoutTime))
		self.view.Server_Name_Text1:SetText(info.serverInfo.name)
		self:setServerItemStatus(self.view.Server_Status_Img1, info.serverInfo)
		serverStatusAnim = "Ani_"..info.serverInfo.color
		self.WBP_HeadCom:Refresh({Level = info.roleData.extend.Level, ProfessionID = info.roleData.extend.Profession})
		local bQueueInfo = info.serverInfo.serverStatus == Enum.SERVER_STATUS.EXCEED_MAX_QUEUE_NUM
		self.userWidget:Event_UI_Style(false, bQueueInfo, bQueueInfo and 2 or 0)
		if bQueueInfo then
			self.view.WBP_Queue_Status:Event_UI_Style(2)
			self.view.WBP_Queue_Status.RTB_TagName:SetText(StringConst.Get("SERVER_QUEUE_STATUS_CIRCUIT_BREAKER"))
		end
	else		--没有角色信息的话，显示服务器信息
		self.view.Role_View:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Server_View:SetVisibility(ESlateVisibility.Visible)

		self.view.Server_Name_Text:SetText(info.name ~= nil and info.name or "")
		local hadTag = info.tags ~= nil and next(info.tags) ~= nil and info.tags[1] ~= "HOT" --HOT标签不显示
		if hadTag then
			local tagKey = "SERVER_TAG_"..info.tags[1]
			local tagIconTbData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData[tagKey])
			self:SetImage(self.view.WBP_ServerTag.KImg_Icon, tagIconTbData.AssetPath)
		end
		local SERVER_STATUS = Enum.SERVER_STATUS
		local accountEntity = Game.NetworkManager.GetAccountEntity()
		local serverInQueue = (accountEntity and accountEntity.bInServerQueue and Game.LoginSystem:GetServerLoginData().ServerId == info.serverId) or
			Game.LoginSystem:GetAccountQueueServerID() == info.serverId
		local bHadQueueInfo = info.serverStatus ~= SERVER_STATUS.NORMAL

		local queueTagStyle, itemBgType = 0, 0
		if serverInQueue then
			queueTagStyle = 1
			self.view.WBP_Queue_Status.RTB_TagName:SetText(StringConst.Get("SERVER_QUEUE_STATUS_IN_QUEUE"))
		elseif bHadQueueInfo then
			if info.serverStatus == SERVER_STATUS.NEED_QUEUE then
				self.view.WBP_Queue_Status.RTB_TagName:SetText(StringConst.Get("SERVER_QUEUE_STATUS_NEED_QUEUE"))
			else
				queueTagStyle = 2
				itemBgType = 2
				self.view.WBP_Queue_Status.RTB_TagName:SetText(StringConst.Get("SERVER_QUEUE_STATUS_CIRCUIT_BREAKER"))
			end
		end
		self.userWidget:Event_UI_Style(hadTag, bHadQueueInfo or serverInQueue, itemBgType)
		self.view.WBP_Queue_Status:Event_UI_Style(queueTagStyle)
		local serverRoleList = Game.LoginSystem:GetServerRoleList()
		local roleList = serverRoleList[info.serverId]
		if roleList then
			self.view.HB_ServerCharacterInfo:SetVisibility(ESlateVisibility.Visible)
			local roleListCount = #roleList
			for i = 1, 3 do
				local simplifyCareersIcon = self.userWidget["WBP_SimplifyCareersIcon"..tostring(i)]
				if i > roleListCount then
					simplifyCareersIcon:SetVisibility(ESlateVisibility.Collapsed)
				else
					simplifyCareersIcon:SetVisibility(ESlateVisibility.Visible)
					if i == 3 and roleListCount > 3 then
						simplifyCareersIcon:Event_UI_Style(true)
					else
						simplifyCareersIcon:Event_UI_Style(false)
						local OptionClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(roleList[i].extend.Profession)[0]
						self:SetImage(simplifyCareersIcon.KImg_Icon, OptionClassInfo.SoloHeadIcon)
						simplifyCareersIcon.KText_Lv:SetText(roleList[i].extend.Level)
					end
				end
			end
		else
			self.view.HB_ServerCharacterInfo:SetVisibility(ESlateVisibility.Collapsed)
		end
		self:setServerItemStatus(self.view.Server_Status_Image, info)
		serverStatusAnim = "Ani_"..info.color .."_Server"
	end
	self:StopAllAnimations()
	if self.view[serverStatusAnim] then
		self:PlayAnimation(self.view[serverStatusAnim])
	end
end

function LoginServerItem:setServerItemStatus(serverStatusImage, info)
	if not string.isEmpty(info.color) and Game.LoginSystem.ServerColorToIconID[info.color] then
		serverStatusImage:SetVisibility(ESlateVisibility.Visible)
		local statusIconTbData = Game.TableData.GetArtAssetIconDataRow(Game.LoginSystem.ServerColorToIconID[info.color])
		self:SetImage(serverStatusImage, statusIconTbData.AssetPath)
	else
		serverStatusImage:SetVisibility(ESlateVisibility.Collapsed)
	end
end

---@private LoginTimeFormat 上线时间格式化
---@param loginTime number 登录时间
---@param logoutTime number 登出时间
function LoginServerItem:loginTimeFormat(loginTime, logoutTime)
	local diffTime = os.time() - logoutTime // 1000
	if logoutTime == 0 or loginTime >= logoutTime then
		return StringConst.Get("SOCIAL_FRIEND_ONLINE")
	elseif diffTime < 60 then
		return StringConst.Get("ONE_MINUTE_AGO")
	elseif diffTime <= 60*60 then
		return string.format(StringConst.Get("SEVERAL_MINUTES_AGO"),math.floor(diffTime/60))
	elseif diffTime <= 60*60*24 then
		return string.format(StringConst.Get("SEVERAL_HOURS_AGO"),math.floor(diffTime/3600))
	elseif diffTime <= 60*60*24*7 then
		return string.format(StringConst.Get("SEVERAL_DAYS_AGO"),math.floor(diffTime/(60*60*24)))
	elseif diffTime <= 60*60*24*31 then
		return string.format(StringConst.Get("SEVERAL_WEEKS_AGO"),math.floor(diffTime/(60*60*24*7)))
	elseif diffTime <= 60*60*24*365 then
		return string.format(StringConst.Get("SEVERAL_MONTHS_AGO"),math.floor(diffTime/(60*60*24*31)))
	else
		return StringConst.Get("ONE_YEAR_AGO")
	end
end

return LoginServerItem
