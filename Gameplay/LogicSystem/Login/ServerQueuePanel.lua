local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")

---@class ServerQueuePanel : UIPanel
---@field view ServerQueuePanelBlueprint
local ServerQueuePanel = DefineClass("ServerQueuePanel", UIPanel)

ServerQueuePanel.eventBindMap = {
	[_G.EEventTypes.LOGIN_QUEUE_UPDATE] = "setServerQueueInfo",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ServerQueuePanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ServerQueuePanel:InitUIData()
	self.recommendServerList = nil		--推荐服务器列表
	self.curSelectServerId = nil		--当前排队中的服务器
	self.bPreCreateRole = false			--是否请求创角阶段
end

--- UI组件初始化，此处为自动生成
function ServerQueuePanel:InitUIComponent()
    ---@type UIComButton
    self.CreateRoleBtnCom = self:CreateComponent(self.view.CreateRoleBtn, UIComButton)
    ---@type UIComDiyTitle
    self.WBP_ServerTitleCom = self:CreateComponent(self.view.WBP_ServerTitle, UIComDiyTitle)
	---@type UIListView
    self.WBP_Server_ComListCom = self:CreateComponent(self.view.WBP_Server_ComList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function ServerQueuePanel:InitUIEvent()
	self:AddUIEvent(self.WBP_Server_ComListCom.onItemClicked, "on_WBP_Server_ComListCom_ItemClicked")
    self:AddUIEvent(self.CreateRoleBtnCom.onClickEvent, "on_CreateRoleBtnCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_Close.OnClicked, "on_Btn_Close_Clicked")
    self:AddUIEvent(self.view.Btn_Tips.OnClicked, "on_Btn_Tips_Clicked")
    self:AddUIEvent(self.view.WBP_PlatformServerMoreBtn.Btn_ClickArea.OnClicked, "on_WBP_PlatformServerMoreBtnBtn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ServerQueuePanel:InitUIView()
	self.CreateRoleBtnCom:Refresh(StringConst.Get("LOGIN_BEFOREHAND_CREATE_ROLE"))
	self.view.Text_Queue_Protect:SetText(string.format(StringConst.Get("LOGIN_QUEUE_PROTECT_TIPS"), Game.TableData.GetConstDataRow("LOGIN_QUEUE_PROTECT_TIME")))
end

---OnRefresh
---@param bCreateRole boolean 是否需要预创角
function ServerQueuePanel:OnRefresh(bPreCreateRole)
	local serverLoginData = Game.LoginSystem:GetServerLoginData()
	self.curSelectServerId = serverLoginData.ServerId
	self.bPreCreateRole = bPreCreateRole
	self.WBP_ServerTitleCom:Refresh(serverLoginData.ServerName)
	self:setServerQueueInfo()
	self:setRecommendServer()
	self.view.Btn_Close:SetVisibility(bPreCreateRole and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
	self.view.Img_Close:SetVisibility(bPreCreateRole and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
	self.view.CreateRoleBtn:SetVisibility(bPreCreateRole and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
end

function ServerQueuePanel:setServerQueueInfo()
	local accountEntity = Game.NetworkManager.GetAccountEntity()
	if not accountEntity.bInServerQueue then
		if self.bPreCreateRole then
			Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.LOGIN_QUEUE_FINISH_ENTER_GAME, function()
				self:on_CreateRoleBtnCom_ClickEvent()
			end)
		else
			self:StartTimerBindIns("DelayClosePanel", "CloseSelf", 1, 1)  --界面打开过程中不能关闭UI,这里延迟一帧处理
		end
		return
	end
	local waitTime = math.ceil(accountEntity.minLoginQueueRank / accountEntity.afkNumPerMin)	--当前排队位置/每分钟排进游戏数量
	self.view.KRichT_QueueNum:SetText(accountEntity.minLoginQueueRank)
	self.view.KRichT_QueueTime:SetText(string.format(StringConst.Get("LOGIN_QUEUE_TIME_FORMAT"), waitTime // 60, waitTime % 60))
end

function ServerQueuePanel:setRecommendServer()
	self.recommendServerList = {}
	local serverList = Game.AllInSdkManager.SdkData.ServerListData
	for _, group in pairs(serverList.groupDatas) do
		for _, serverInfo in pairs(group.serverList) do
			if serverInfo.serverId ~= self.curSelectServerId and (serverInfo.serverStatus == Enum.SERVER_STATUS.NORMAL or serverInfo.serverStatus == Enum.SERVER_STATUS.NEED_QUEUE) then
				self.recommendServerList[#self.recommendServerList + 1] = serverInfo
			end
		end
	end
	table.sort(self.recommendServerList, function(a, b)
		if a.queueSize == b.queueSize then
			return a.serverStatus < b.serverStatus
		end
		return a.queueSize < b.queueSize
	end)
	if #self.recommendServerList > 6 then
		for i = #self.recommendServerList, 7, -1 do
			self.recommendServerList[i] = nil
		end
	end
	self.WBP_Server_ComListCom:Refresh(self.recommendServerList)
end

--- 此处为自动生成
---@param index number
---@param data table
function ServerQueuePanel:on_WBP_Server_ComListCom_ItemClicked(index, data)
	local selectServer = self.recommendServerList[index]
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.LOGIN_QUEUE_SWITCH_SERVER, function()
		self:CloseSelf()
		Game.LoginSystem:SwitchServer(selectServer)
	end, nil ,{Game.AllInSdkManager.SdkData.AllServerMap[self.curSelectServerId].name})
end

--- 点击预创角按钮
function ServerQueuePanel:on_CreateRoleBtnCom_ClickEvent()
	self:CloseSelf()
	Game.GameLoopManagerV2:OnLoginEnterRoleStage({})  -- luacheck: ignore
end

--- 点击关闭按钮
function ServerQueuePanel:on_Btn_Close_Clicked()
	self:CloseSelf()
end

--- 此处为自动生成 点击排队tips按钮
function ServerQueuePanel:on_Btn_Tips_Clicked()
	local protectTime = Game.TableData.GetConstDataRow("LOGIN_QUEUE_PROTECT_TIME")
	Game.TipsSystem:ShowTips(Enum.ETipsData.LOGIN_QUEUE_TIP, self.view.Btn_Tips:GetCachedGeometry(), {
			ExtraContent = {{Content = {{protectTime, protectTime}}}}
		}
	)
end

--- 点击更多服务器按钮
function ServerQueuePanel:on_WBP_PlatformServerMoreBtnBtn_ClickArea_Clicked()
	Game.NewUIManager:OpenPanel("LoginServerSelect_Panel", true)
end

return ServerQueuePanel
