local HUDConstantDisplay_Sub = kg_require("Gameplay.LogicSystem.ConstantDisplay.HUDConstantDisplay_Sub")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDConstantDisplay : UIComponent
---@field view HUDConstantDisplayBlueprint
local HUDConstantDisplay = DefineClass("HUDConstantDisplay", UIComponent)


HUDConstantDisplay.FuncType = {
    CameraMode = 1, --拍照
    ImmerseMode = 2, --沉浸模式
    BanMode = 3, --屏蔽他人
    QuickFashion = 4, --快速换装
    FeedbackMode = 5, --举报反馈
    Dance = 6, --舞蹈
}

HUDConstantDisplay.eventBindMap = {
	[EEventTypesV2.ON_HUD_SETTING_CHANGED] = "Refresh",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDConstantDisplay:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDConstantDisplay:InitUIData()
    local FuncType = HUDConstantDisplay.FuncType
    self.FuncHandleMap = {
        [FuncType.CameraMode] = "CameraMode",
        [FuncType.ImmerseMode] = "ImmerseMode",
        [FuncType.BanMode] = "BanMode",
        [FuncType.QuickFashion] = "QuickFashion",
        [FuncType.FeedbackMode] = "FeedbackMode",
        [FuncType.Dance] = "Dance",
    }
end

--- UI组件初始化，此处为自动生成
function HUDConstantDisplay:InitUIComponent()
    ---@type HUDConstantDisplay_Sub
    self.WBP_ConstantDisplay_Icon_Sub2Com = self:CreateComponent(self.view.WBP_ConstantDisplay_Icon_Sub2, HUDConstantDisplay_Sub)
    ---@type HUDConstantDisplay_Sub
    self.WBP_ConstantDisplay_Icon_Sub1Com = self:CreateComponent(self.view.WBP_ConstantDisplay_Icon_Sub1, HUDConstantDisplay_Sub)
    ---@type HUDConstantDisplay_Sub
    self.WBP_ConstantDisplay_Icon_SubCom = self:CreateComponent(self.view.WBP_ConstantDisplay_Icon_Sub, HUDConstantDisplay_Sub)
end

---UI事件在这里注册，此处为自动生成
function HUDConstantDisplay:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDConstantDisplay:InitUIView()
    self.WBP_ConstantDisplay_Icon_SubCom:SetIconInfo(UIAssetPath.UI_HUD_Icon_B07_Sprite, function() self:OnClickMainBtn() end)

    ---@type HUDConstantDisplay_Sub[]
    self.FuncIconSubList = {
        self.WBP_ConstantDisplay_Icon_Sub1Com,
        self.WBP_ConstantDisplay_Icon_Sub2Com,
    }
end

---组件刷新统一入口
function HUDConstantDisplay:Refresh(...)
    -- 屏蔽他人初始状态设置
	Game.HUDSystem:SetConstantActive(3,Game.me.bHideAllPlayer)

    self:RefreshUI()
end

function HUDConstantDisplay:RefreshUI()
	if not Game.me.HUDSettings then
		return
	end

    for k, v in pairs(self.FuncIconSubList) do
        local funcIdx = Game.me.HUDSettings[k]
        v:SetFuncInfo(funcIdx, function() self:OnClickFuncBtn(funcIdx) end)
    end
end

function HUDConstantDisplay:OnClickMainBtn()
	Game.NewUIManager:OpenPanel("ConstantDisplay_Panel")
end

function HUDConstantDisplay:OnClickFuncBtn(funcIdx)
	if not funcIdx then
		return
	end

    local funcHandle = self.FuncHandleMap[funcIdx]
    if not funcHandle then
        return
    end

    self[funcHandle](self)
end


function HUDConstantDisplay:CameraMode()
	Game.PhotographSystem:ShowPhotographUI()
end

function HUDConstantDisplay:ImmerseMode()
	if Game.HUDSystem:GetConstantActive(2) then
		Game.HUDSystem:QuitImmerse()
		Game.HUDSystem:SetConstantActive(2, false)
	else
		Game.HUDSystem:ImmerseMode()
		Game.HUDSystem:SetConstantActive(2, true)
	end
	self:RefreshUI()
end

function HUDConstantDisplay:BanMode()
	if Game.HUDSystem:GetConstantActive(3) then
		Game.WorldManager:HideAllPlayer(false)
		Game.HUDSystem:SetConstantActive(3, false)
	else
		Game.WorldManager:HideAllPlayer(true)
		Game.HUDSystem:SetConstantActive(3, true)
	end
	self:RefreshUI()
end

function HUDConstantDisplay:QuickFashion()
    Game.FashionSystem:OpenFashionPanel()
end

function HUDConstantDisplay:FeedbackMode()
	Game.ReportSystem:ShowReportUI(Enum.EReportType.Feedback)
end

function HUDConstantDisplay:Dance()
    Game.DanceSystem:openDanceBasicPanel()
end

return HUDConstantDisplay
