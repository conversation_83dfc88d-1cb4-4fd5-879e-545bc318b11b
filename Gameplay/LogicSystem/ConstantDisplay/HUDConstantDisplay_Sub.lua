local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDConstantDisplay_Sub : UIComponent
---@field view HUDConstantDisplay_SubBlueprint
local HUDConstantDisplay_Sub = DefineClass("HUDConstantDisplay_Sub", UIComponent)

HUDConstantDisplay_Sub.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDConstantDisplay_Sub:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDConstantDisplay_Sub:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDConstantDisplay_Sub:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDConstantDisplay_Sub:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDConstantDisplay_Sub:InitUIView()
end


function HUDConstantDisplay_Sub:SetIconInfo(icon, onClickCb)
    self:SetImage(self.view.Img_Icon, icon)
    self.onClickCb = onClickCb
end


---@param icon string 图标
---@param onClickCb function 点击回调
function HUDConstantDisplay_Sub:SetFuncInfo(funcIdx, onClickCb)
    self.funcIdx = funcIdx
    if funcIdx == nil or funcIdx <= 0 then
        self:Hide()
        return
    else
        self:Show()
    end

    self.onClickCb = onClickCb

    local hudData = Game.TableData.GetHUDConstButtonRow(funcIdx)
    local icon
    if Game.HUDSystem:GetConstantActive(funcIdx) then
        icon = hudData.ActiveIcon
    else
        icon = hudData.ButtonIcon
    end
    self:SetImage(self.view.Img_Icon, icon)
end


--- 此处为自动生成
function HUDConstantDisplay_Sub:on_Btn_ClickArea_Clicked()
    if self.onClickCb then
        self.onClickCb()
    end
end

return HUDConstantDisplay_Sub
