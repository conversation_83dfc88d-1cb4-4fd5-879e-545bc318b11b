local CollisionConst = kg_require("Shared.Const.CollisionConst")
local UBSFunc = import("BSFunctionLibrary")
local Const = kg_require("Shared.Const")
local EPropertyClass = import("EPropertyClass")
local EBoneSpaces = import("EBoneSpaces")

---@class SneakSystem:SystemBase
local SneakSystem = DefineClass("SneakSystem",SystemBase)
---@type KismetSystemLibrary
local KismetSystemLibrary = import("KismetSystemLibrary")

SneakSystem.EPlayerBoneName =
{
    "head",
    "spine_03",
    "lowerarm_l",
    "lowerarm_r",
    "foot_l",
    "foot_r",
}

SneakSystem.DecalWarningMIPath = UIAssetPath.MI_Decal_NpcWarning
SneakSystem.DecalRingMIPath = UIAssetPath.MI_Decal_Ring

---@private
function SneakSystem:onCtor()
    self.model = nil
    self.sender = nil
    self.entityWarningList = {} -- 记录警告实体列表
    self.LoadHandles = {}   -- 记录加载的handle
    self.entityDecalMap = nil -- 记录实体decal
    self.entityWarningFirstMap = {} -- 记录实体第一次警告
    self.bDebugPrintSneakWarning = nil  -- 是否打印警告信息
	self.checkTimerID = {}
end

---@private
function SneakSystem:onInit()
    ---@type SneakModel
    self.model = require("Gameplay.LogicSystem.SneakSystem.SneakModel").new(false, false)
    ---@type SneakSender
    self.sender = require("Gameplay.LogicSystem.SneakSystem.SneakSender").new()
    self:RegisterEvent()
end

---@private
function SneakSystem:onUnInit()
    self:UnRegisterEvent()
end

function SneakSystem:OnReLogin()
    if Game.me and Game.me.npcWarningValid then
        --local count = 0
        for entityId, value in pairs(Game.me.npcWarningValid) do
            --count = count + 1
            if value == Const.NPC_WARNING_VALID.WARNING_VALID then
                self:SneakLineTrace(entityId)
            end
        end
        --if count > 0 then
        --    Game.SneakSystem:RegisterEvent()
        --end
    end
end

function SneakSystem:RegisterEvent()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.SPIRIT_VISUAL_ON_OPEN, "OnSpiritVisionOpen", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.SPIRIT_VISUAL_ON_CLOSE, "OnSpiritVisionClose", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.PLANE_ON_CHANGE, "OnPlaneChange", self)
end

function SneakSystem:UnRegisterEvent()
    Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function SneakSystem:OnPlaneChange(planeID)
    Log.Debug("OnPlaneChange")
    self:ExitSneak()
end

function SneakSystem:NpcEntityAddTrap(entityId, trapExcelIdList)
    local entity = Game.EntityManager:getEntity(entityId)
	--先增加判断，后续要监听Actor的EnterWorld
    if entity == nil or not entity.bInWorld then
        return
    end
    local groupId = entity.trapGroupID
    if not groupId or groupId <= 0 then
        return
    end
    if not self.entityDecalMap then
        self.entityDecalMap = {}
    end
    if self.entityDecalMap[entityId] and #self.entityDecalMap[entityId] > 0 then
        return
    end
	
    local groupData = Game.TableData.GetNpcTrapGroupDataRow(groupId)
    local trapIdList = groupData.TrapList
    for _, trapId in ipairs(trapIdList) do
        local trapDefineData = Game.TableData.GetNpcTrapDefineDataRow(trapId)
        if (trapDefineData.Trap_HalfAngle and trapDefineData.Trap_HalfAngle > 0) and (trapDefineData.IsClientShow and trapDefineData.IsClientShow > 0) then
            self:AddTrapDecal(entityId, trapDefineData.Trap_HalfAngle, trapDefineData.Trap_InnerRadius, trapDefineData.Trap_OuterRadius, trapDefineData.IsClientShow)
        else
            if trapDefineData.IsClientShow and trapDefineData.IsClientShow > 0 then
                self:AddTrapDecal(entityId, 180, 0, trapDefineData.Trap_Radius, trapDefineData.IsClientShow)
            end
            if self.bDebugPrintSneakWarning then
                self:AddDebugTrapCircle(entityId, trapDefineData.Trap_Radius)
            end
        end
    end
end

function SneakSystem:NpcEntityHasTrap(entityId)
	if self.entityDecalMap and self.entityDecalMap[entityId] and #self.entityDecalMap[entityId] > 0 then
		return true
	end
	
	return false
end

function SneakSystem:NpcEntityRemoveTrap(entity, trapExcelId)
	self:RemoveTrapDecal(entity)
end

function SneakSystem:PlayerInSneakCircle()
    self:StopTimer("OutOfSneakReminder")
end

function SneakSystem:PlayerOutOfSneakCircle()
    --Reminder
    if not self:HasTimer("OutOfSneakReminder") then
        self:StartTimer("OutOfSneakReminder", function()
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.LOST_FOLLOW_REMINDER)
        end, Game.TableData.GetConstDataRow("OUT_TRAP_REMINDER_GAP") * 1000, -1, nil, true)
    end
end

function SneakSystem:PlayerInWarningCircle(entityId)
    self:StartTimer(string.format("SneakLineTrace_%s", entityId), function()
        self:SneakLineTrace(entityId)   
    end, 33, -1, nil, true)
end

function SneakSystem:PlayerOutOfWarningCircle(entityId)
    self:StopTimer(string.format("SneakLineTrace_%s", entityId))
end

function SneakSystem:NpcWarningAllRemoved(entityId)
    self:PlayerOutOfWarningCircle(entityId)
end

function SneakSystem:OnNPCWarningValueChanged(entityId, value)
    local targetEntity = Game.EntityManager:getEntity(entityId)
    if targetEntity == nil then
        return
    end
    local maxValue, showPercent, hidePercent

	if not targetEntity.trapGroupID or targetEntity.trapGroupID < 0 then
		return
	end

	local groupId = targetEntity.trapGroupID
	local groupData = Game.TableData.GetNpcTrapGroupDataRow(groupId)
	maxValue = groupData.WarningUpperBound
	showPercent = groupData.HUDShowPercent
	hidePercent = groupData.HUDHidePercent
	
    local curValue = Game.me.npcWarningValue[entityId]
    if self.bDebugPrintSneakWarning then
        local msg = string.format("OnNPCWarningValueChanged: %s, %s", entityId, curValue)
        KismetSystemLibrary.PrintString(GetContextObject(), msg, true, false, FLinearColor(0, 1, 0), 1)
    end
    Log.Debug("OnNPCWarningValueChanged: ", entityId, curValue)
    if curValue / maxValue > showPercent then
        if self.entityWarningFirstMap[entityId] == nil or self.entityWarningFirstMap[entityId] == false then
            Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.Camera, nil, 4000005, 1.5, 0, 1.5)
            Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.Camera, nil, 4000010, 1.5, 0, 1.5)
            self.warningAudio = Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.TRACE_WARNING_EVENT)
            self.entityWarningFirstMap[entityId] = true
        end
		Game.HUDSystem:ShowUI("HUD_TraceArrows", entityId)
        if curValue >= maxValue then
            Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.TRACE_BE_FOUND_EVENT)
			Game.UniqEventSystemMgr:PublishTargetAndGlobal(targetEntity:uid(), EEventTypesV2.ON_SNEAK_NPC_WARNING_UPDATE, targetEntity:uid(), Enum.EHeadNPCWarningType.Alarm)
        else
			Game.UniqEventSystemMgr:PublishTargetAndGlobal(targetEntity:uid(), EEventTypesV2.ON_SNEAK_NPC_WARNING_UPDATE, targetEntity:uid(), Enum.EHeadNPCWarningType.Warning)
        end
    else
        if self.entityWarningFirstMap[entityId] == nil or self.entityWarningFirstMap[entityId] then
            if self.warningAudio then
                Game.AkAudioManager:StopEvent(self.warningAudio)
            end
            self.entityWarningFirstMap[entityId] = false
        end
		Game.UniqEventSystemMgr:PublishTargetAndGlobal(targetEntity:uid(), EEventTypesV2.ON_SNEAK_NPC_WARNING_UPDATE, targetEntity:uid(), Enum.EHeadNPCWarningType.UnVisible)
        if curValue / maxValue < hidePercent then
			Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SNEAK_NPC_WARNING_HIDE, entityId)
        end
    end
end

function SneakSystem:SneakLineTrace(targetEntityId)
    local targetEntity = Game.EntityManager:getEntity(targetEntityId)
    if not targetEntity or not targetEntity.bInWorld then
        return
    end
	
	local playerEntity = Game.me
	if not playerEntity or not playerEntity.bInWorld then
		return
	end

	if not targetEntity.trapGroupID or targetEntity.trapGroupID < 0 then
		return
	end

	local groupId = targetEntity.trapGroupID
	local groupData = Game.TableData.GetNpcTrapGroupDataRow(groupId)
	local buffList = groupData.BuffList

	for _, buffId in ipairs(buffList) do
		if playerEntity:GetBuffInstanceByBuffIDNew(buffId) then
			return
		end
	end
	
    local targetHeadLocation = self:GetBoneLocation(targetEntity, "head")
	local targetComponentID = targetEntity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
	local targetLocation = targetEntity.CppEntity:KAPI_Component_K2_GetComponentLocation(targetComponentID)
    targetHeadLocation = targetHeadLocation + targetLocation
    local hitTimes = 0
	
	local playerComponentID = playerEntity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
	local playerLocation = playerEntity.CppEntity:KAPI_Component_K2_GetComponentLocation(playerComponentID)

	local ignoredActorIDs = slua.Array(EPropertyClass.Int64)
	ignoredActorIDs:Add(targetEntity.CharacterID)

	local drawDebugType = 0
	if self.bDebugPrintSneakWarning then
		drawDebugType = 1
	end
	
    for _, boneName in ipairs(self.EPlayerBoneName) do
        local playerBoneLocation = self:GetBoneLocation(Game.me, boneName) + playerLocation
		local C7HitResult = LuaScriptAPI.KismetSystem_LineTraceSingle(
			playerEntity.CharacterID,
			targetHeadLocation,
			playerBoneLocation,
			CollisionConst.COLLISION_TRACE_TYPE_BY_NAME.Visibility,
			false,
			ignoredActorIDs,
			drawDebugType,
			true,
			FLinearColor(1, 0, 0), --TraceColora
			FLinearColor(0, 1, 0), --HitColor
			0 --duration
		)
		
		if C7HitResult.bResult then
			hitTimes = hitTimes + 1
		end
	end
	
    --Log.Debug("EntityId: ", targetEntityId, "SneakSystem Percentage: ", hitTimes/#self.EPlayerBoneName)
    self.sender:ReqReportNpcSight(targetEntityId, hitTimes/#self.EPlayerBoneName < Game.TableData.GetConstDataRow("TRAP_LINE_MODEL_PERCENT"))
end

function SneakSystem:GetBoneLocation(entity, boneName)
	local componentID = entity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
	return entity.CppEntity:KAPI_SkeletalMeshID_GetBoneLocation(componentID, boneName, EBoneSpaces.WorldSpace)
end

function SneakSystem:AddTrapDecal(entityId, halfAngle, innerRadius, outerRadius, showType)
	local entity = Game.EntityManager:getEntity(entityId)
	if entity == nil then
		return
	end
    local timerKey = string.format("AddTrapDecalCheck%s",entityId)
    self:StartTimer(timerKey, function()
		if entity.bInWorld then
			self:StopTimer(timerKey)
			return
		end
    end, 1, -1, nil, true, function()
		self:AddTrapDecalInternal(entityId, halfAngle, innerRadius, outerRadius, showType)
    end)
end

function SneakSystem:AddTrapDecalInternal(entityId, halfAngle, innerRadius, outerRadius, showType)
	if self.entityDecalMap[entityId] and #self.entityDecalMap[entityId] > 0 then
		return
	end
	local entity = Game.EntityManager:getEntity(entityId)
	if entity == nil then
		return
	end
	local EMPTY = {X = 0, Y = 0, Z = 0}
	local EMPTY1 = {R = 0, G = 0, B = 0, A = 0}
	local decalRingConfig = {
		DecalAssetPath = self.DecalRingMIPath,
		Translation = M3D.Vec3(0, 0, -150),
		Rotation = M3D.Vec3(-90, -90, 0),
		bNeedAttach = true,
		DecalSize = {300, outerRadius, outerRadius},
		DynamicMaterialParams = {
			[1] = {ParamName = "SectorAngle", ParamType = 0, InitFloatValue = halfAngle * 2 , InitVectorValue = EMPTY, InitLinearColorValue = EMPTY1, IsLoop = false},
			[2] = {ParamName = "OuterRadius", ParamType = 0, InitFloatValue = 1.0, InitVectorValue = EMPTY, InitLinearColorValue = EMPTY1, IsLoop = false},
			[3] = {ParamName = "InnerRadius", ParamType = 0, InitFloatValue = innerRadius/outerRadius, InitVectorValue = EMPTY, InitLinearColorValue = EMPTY1, IsLoop = false},
		},
		bAttachRotation = true,
		bAttachScale = true,
	}
	local decalRingID = entity:SpawnDecalToTarget(decalRingConfig)
	if self.entityDecalMap[entityId] == nil then
		self.entityDecalMap[entityId] = {}
	end
	table.insert(self.entityDecalMap[entityId], decalRingID)

	local decalWarningConfig = {
		DecalAssetPath = self.DecalWarningMIPath,
		Translation = M3D.Vec3(0, 0, -150),
		Rotation = M3D.Vec3(-90, -90, 0),
		bNeedAttach = true,
		DecalSize = {300, innerRadius, innerRadius},
		DynamicMaterialParams = {
			[1] = {ParamName = "SectorAngle", ParamType = 0, InitFloatValue = halfAngle * 2 , InitVectorValue = EMPTY, InitLinearColorValue = EMPTY1, IsLoop = false},
			[2] = {ParamName = "OuterRadius", ParamType = 0, InitFloatValue = 1.0, InitVectorValue = EMPTY, InitLinearColorValue = EMPTY1, IsLoop = false},
			[3] = {ParamName = "InnerRadius", ParamType = 0, InitFloatValue = 0, InitVectorValue = EMPTY, InitLinearColorValue = EMPTY1, IsLoop = false},
		},
		bAttachRotation = true,
		bAttachScale = true,
	}
	local decalWarningID = entity:SpawnDecalToTarget(decalWarningConfig)
	table.insert(self.entityDecalMap[entityId], decalWarningID)
	
	-- 这里原有用Timer去检测的逻辑本身就比较奇怪，如果有这样的需求应该是用回调或者事件来处理。后续仔细整理需求再优化一下@倪铁
	if showType == 1 then   --灵视显示
		self.checkTimerID[entityId] = Game.TimerManager:CreateTimerAndStart(function()
			self:CheckDecalLoaded(entityId, decalRingID, decalWarningID)
		end, 1, -1)
	end
end

function SneakSystem:CheckDecalLoaded(entityID, decalRingID, decalWarningID)
	local entity = Game.EntityManager:getEntity(entityID)
	if not entity then
		return
	end
	
	local decalRingEntity = Game.EntityManager:getEntity(decalRingID)
	if not decalRingEntity then
		return
	end
	
	local decalWarningEntity = Game.EntityManager:getEntity(decalWarningID)
	if not decalWarningEntity then
		return
	end

	if decalRingEntity.bInWorld and decalWarningEntity.bInWorld then
		if Game.me:IsInSpiritualVision() then
			decalRingEntity.CppEntity:KAPI_Actor_SetActorHiddenInGame(false)
			decalWarningEntity.CppEntity:KAPI_Actor_SetActorHiddenInGame(false)
		else
			decalRingEntity.CppEntity:KAPI_Actor_SetActorHiddenInGame(true)
			decalWarningEntity.CppEntity:KAPI_Actor_SetActorHiddenInGame(true)
		end
		Game.TimerManager:StopTimerAndKill(self.checkTimerID[entityID])
	end
end

function SneakSystem:RemoveTrapDecal(entityId)
	if not self.entityDecalMap or not self.entityDecalMap[entityId] then
		return
	end
	local entity = Game.EntityManager:getEntity(entityId)
	if entity then
		entity:DestroyAllDecalEntities()
	end
	table.clear(self.entityDecalMap[entityId])
end

function SneakSystem:RemoveAllTrapDecal()
    if not self.entityDecalMap then
        return
    end
    for entityID, decalMap in pairs(self.entityDecalMap) do
        self:RemoveTrapDecal(entityID)
        table.clear(decalMap)
    end
    self.entityDecalMap = nil
end

function SneakSystem:OnSpiritVisionOpen()
    if not self.entityDecalMap then
        return
    end
    for entityID, decalMap in pairs(self.entityDecalMap) do
        for _, v in ipairs(decalMap) do
			local decalEntity = Game.EntityManager:getEntity(v)
			if decalEntity and decalEntity.bInWorld then
				decalEntity.CppEntity:KAPI_Actor_SetActorHiddenInGame(false)
			end
        end
    end
end

function SneakSystem:OnSpiritVisionClose()
    if not self.entityDecalMap then
        return
    end
	for entityID, decalMap in pairs(self.entityDecalMap) do
		for _, v in ipairs(decalMap) do
			local decalEntity = Game.EntityManager:getEntity(v)
			if decalEntity and decalEntity.bInWorld then
				decalEntity.CppEntity:KAPI_Actor_SetActorHiddenInGame(true)
			end
		end
	end
end

function SneakSystem:AddDebugTrapCircle(entityId, radius)
    self:StartTimer(string.format("AddDebugTrapCircle%s%d", entityId, radius), function()
        local entity = Game.EntityManager:getEntity(entityId)
        if not entity or not entity.bInWorld then
            return
        end
		
        local center = entity.CppEntity:KAPI_GetLocation()
        import("KismetSystemLibrary").DrawDebugCircle(_G.GetContextObject(), center, radius, 32, FLinearColor.Green, 0, 2,
                FVector(1, 0, 0), FVector(0, 1, 0))
    end, 33, -1, nil, true)
end

function SneakSystem:GetNPCWarningValue(entityId)
    local targetEntity = Game.EntityManager:getEntity(entityId)
    if targetEntity == nil then
        return
    end
    local maxValue
    if targetEntity.trapGroupID and targetEntity.trapGroupID > 0 then
        local groupData = Game.TableData.GetNpcTrapGroupDataRow(targetEntity.trapGroupID)
        maxValue = groupData.WarningUpperBound
        if Game.me.npcWarningValue[entityId] then
            return Game.me.npcWarningValue[entityId] / maxValue
        end
    end
    return 0
end

function SneakSystem:GetNPCWarningHUDAngle(entityId)
    local targetEntity = Game.EntityManager:getEntity(entityId)
    if not targetEntity or not targetEntity.bInWorld then
        return
    end
    local playerLocation = Game.CameraManager.CameraCachePrivate.POV.Location
    local playerRotation = Game.CameraManager.CameraCachePrivate.POV.Rotation
    local targetLocation = targetEntity.CppEntity:KAPI_GetLocation()
    local playerForwardVector = playerRotation:Vector()
    local directionToTarget = (targetLocation - playerLocation):GetSafeNormal(1e-8, FVector())
    local dotProduct = import("KismetMathLibrary").Dot_VectorVector(playerForwardVector, directionToTarget)
    local angle = math.acos(dotProduct)
    local crossProduct = FVector.CrossProduct(playerForwardVector, directionToTarget)
    if crossProduct.Z < 0 then
        angle = -angle
    end
    local angleInDegrees = math.deg(angle)
    return angleInDegrees
end

function SneakSystem:ExitSneak()
    self:StopAllTimer()
    self:RemoveAllTrapDecal()
	Log.Debug("Start Hide HUD_TraceArrows")
	Game.HUDSystem:HideUI("HUD_TraceArrows")
end

function SneakSystem:SetDebugPrintSneakWarning(bPrint)
    self.bDebugPrintSneakWarning = bPrint
end

return SneakSystem