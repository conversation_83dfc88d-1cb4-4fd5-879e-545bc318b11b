---@class GuildLeagueSystemModel:SystemModelBase
--local const = kg_require("Shared.Const")
local GuildLeagueSystemModel = DefineClass("GuildLeagueSystemModel", SystemModelBase)

function GuildLeagueSystemModel:init()
    self.towerEntityIds = {}
    self.altarEntityIds = {}
    self.mapSyncInfos = {}
	self.resourceMonsterIds = {}
	--self.portalEntityIds = {} -- 传送门ID
	-- 载具走变身逻辑，没变身前的载具走服务端位置控制
	
	---局外所需内容
	---@type table guildLeagueSeasonInfo {id=xx, status=xx} , id 对应赛季ID， status 当前赛季的状态 GUILD_LEAGUE_SEASON_STATUS
	self.guildLeagueSeasonInfo = {}
	---@type table guildLeagueRound 当前处于第几轮，每个循环三轮对局，单周一次，双周两次
	self.guildLeagueRound = {}
	---@type table zoneIndex2FixGroupCount 对应分区ID 这个分区固定组的数量
	self.zoneIndex2FixGroupCount = { }

	---展示的奖励ID
	self.showItems = {}

	---ZoneID
	self.zoneID = 1

	self.preZoneID = 1
	---GroupCount
	self.groupCount = 1
	
	---@type table firstGroupGameInfo 返回玩家当前对战的信息，如果没有就是{}
	self.selfGameInfo = {}
	
	---@type table 具体对战信息
	self.guildBattleOutCache = {}
	---@type table 对战排名信息
	self.guildBattleRankCache = {}
	---@type table 组别升降信息
	self.guildBattleRuleCache = {}

	---@type table 联赛指挥系统指挥官查看到的团队信息
	self.leagueAllGroupInfo = {}
	---@type table 联赛指挥系统，指挥策略信息
	self.commandStrategyData = {}
	for id, data in ksbcipairs(Game.TableData.GetGuildLeagueCommandSystemDataTable()) do
		local strategyItemData = {}
		strategyItemData.name = data.Desc
		strategyItemData.id = data.Id
		strategyItemData.cost = data.ResourceCost
		strategyItemData.iconName = data.Icon
		strategyItemData.cd = data.CommandCD
		strategyItemData.canQuickUse = data.Quickuse
		strategyItemData.type = data.Type
		table.insert(self.commandStrategyData, strategyItemData)
	end
	---@type int 联赛指挥系中，我方的总人数
	self.leaguePlayerNumber = 0

	---@type int 联赛指挥界面，当前选中的顶部Tab(再次打开界面时要恢复到这个Tab)
	self.leagueTopTab = 1

	self.guildLeagueResource = 0 -- 联赛指挥系统资源
	self.towerMaxHps = {} -- 塔的最大血量
	self:InitInstanceIDs() -- 初始化实例ID
end

function GuildLeagueSystemModel:InitInstanceIDs()
	self.towerInstanceIds = {} -- 塔的实例ID
	for instanceID, _ in ksbcpairs(Game.TableData.Get_TowerInfoByInstanceID()) do
		self.towerInstanceIds[instanceID] = true
	end
	self.monsterTemplateIds = {} -- 怪物的模板ID
	for _, tempID in ipairs(Game.TableData.GetGuildLeagueConstDataRow("GUILDBATTLEFIELD_SOURCE_MONSTER")) do
		self.monsterTemplateIds[tempID] = true
	end
	for _, tempID in ipairs(Game.TableData.GetGuildLeagueConstDataRow("GUILDBATTLEFIELD_CENTRAL_SOURCE_MONSTER")) do
		self.monsterTemplateIds[tempID] = true
	end
	self.portalInstanceIds = {} -- 传送门的实例ID
	---- 只创建开始传送门点
	--for _, instanceID in ipairs(Game.TableData.GetGuildLeagueConstDataRow("GUILD_LEAGUE_STARTING_PORTAL_INSTANCEID")) do
	--	self.portalInstanceIds[instanceID] = true
	--end
	self.altarInstanceIds = {} -- 祭坛的实例ID
	for _, instanceID in ipairs(Game.TableData.GetGuildLeagueConstDataRow("GUILDBATTLEFIELD_OCCUPYAREA1_INSTANCEID")) do
		self.altarInstanceIds[instanceID] = true
	end
	for _, instanceID in ipairs(Game.TableData.GetGuildLeagueConstDataRow("GUILDBATTLEFIELD_OCCUPYAREA2_INSTANCEID")) do
		self.altarInstanceIds[instanceID] = true
	end
end

return GuildLeagueSystemModel