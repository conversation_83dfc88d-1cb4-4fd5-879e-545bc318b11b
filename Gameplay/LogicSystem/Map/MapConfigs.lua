---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by admin.
--- DateTime: 8/2/2023 3:47 PM
---
local MapConfig = {}
MapConfig.ETagDisplayRule = {
	--CurrentLevel = 1,
	Normal =2,
	SubWay = 3,
	Minimap = 4,
	WorldMap = 5
}

MapConfig.ETagTypes       = {
	--静态读取标签
	CityName = _G.Enum.ETagTypeConfigData.CITY_NAME,
	RegionName = _G.Enum.ETagTypeConfigData.REGION_NAME,
	LandMarkName = _G.Enum.ETagTypeConfigData.LANDMARK_NAME,
	SubWay = _G.Enum.ETagTypeConfigData.SUBWAY,
	Npc = _G.Enum.ETagTypeConfigData.NPC,
	FunctionalNpc = _G.Enum.ETagTypeConfigData.FUNCTION_NPC,
	Entrance = _G.Enum.ETagTypeConfigData.ENTRANCE,
	Exit =  _G.Enum.ETagTypeConfigData.EXIT,

	--动态注册标签
	Playerself = _G.Enum.ETagTypeConfigData.PLAYER_SELF,
	Teammate = _G.Enum.ETagTypeConfigData.PLAYER_TEAMMATE,
	TaskTrace = _G.Enum.ETagTypeConfigData.TASK_TRACE,
	TaskTraceCircle = _G.Enum.ETagTypeConfigData.TASK_TRACE_CIRCLE,
	NormalTrace =  _G.Enum.ETagTypeConfigData.NORMAL_TRACE,
	WorldBoss =  _G.Enum.ETagTypeConfigData.WORLD_BOSS,
	
}


MapConfig.ETagWidgetConfig = {
	[MapConfig.ETagTypes.CityName] = {
		WidgetPath = UIAssetPath.WBP_MapAxisPlace,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility = import("ESlateVisibility").HitTestInvisible,
	},
	[MapConfig.ETagTypes.RegionName] = {
		WidgetPath = UIAssetPath.WBP_MapAxisPlace,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility = import("ESlateVisibility").HitTestInvisible,
	},
	[MapConfig.ETagTypes.LandMarkName] = {
		WidgetPath = UIAssetPath.WBP_MapAxisPlace,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility = import("ESlateVisibility").HitTestInvisible,
	},
	[MapConfig.ETagTypes.SubWay] = {
		WidgetPath     = UIAssetPath.WBP_MapCommonTag,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility     = import("ESlateVisibility").SelfHitTestInvisible,
		TintColor      = "White"
	},
	[MapConfig.ETagTypes.FunctionalNpc] = {
		WidgetPath = UIAssetPath.WBP_MapAxisNPC,
		EdgeWidgetPath = "",
		Visibility = import("ESlateVisibility").SelfHitTestInvisible,
	},
	[MapConfig.ETagTypes.Npc] = {
		WidgetPath = UIAssetPath.WBP_MapAxisNPC,
		EdgeWidgetPath = "",
		Visibility = import("ESlateVisibility").SelfHitTestInvisible,
	},
	[MapConfig.ETagTypes.Playerself] = {
		WidgetPath = UIAssetPath.WBP_MapAxisPlayers,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility = import("ESlateVisibility").HitTestInvisible,
	},
	[MapConfig.ETagTypes.TaskTrace] = {
		WidgetPath = UIAssetPath.WBP_MapAxisTask,
		EdgeWidgetPath = "",
		Visibility = import("ESlateVisibility").SelfHitTestInvisible,
	},
	[MapConfig.ETagTypes.TaskTraceCircle] = {
		WidgetPath = UIAssetPath.WBP_MapAxisTaskTraceCircle,
		EdgeWidgetPath = "",
		Visibility = import("ESlateVisibility").HitTestInvisible,

	},
	[MapConfig.ETagTypes.Teammate] = {
		WidgetPath     = UIAssetPath.WBP_MapAxisPlayeTeamMate,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility     = import("ESlateVisibility").HitTestInvisible,
		TintColor      = "Map_Team"
	},
	[MapConfig.ETagTypes.NormalTrace] = {
		WidgetPath     = UIAssetPath.WBP_MapAxisNormalTrace,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility     = import("ESlateVisibility").HitTestInvisible,
		WidgetPositionOffset = {0,-20}
	},
	[MapConfig.ETagTypes.Entrance] = {
		WidgetPath     = UIAssetPath.WBP_MapCommonTag,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility     = import("ESlateVisibility").SelfHitTestInvisible,
	},
	[MapConfig.ETagTypes.Exit] = {
		WidgetPath     = UIAssetPath.WBP_MapCommonTag,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility     = import("ESlateVisibility").SelfHitTestInvisible,
	},
	[MapConfig.ETagTypes.WorldBoss] = {
		WidgetPath     = UIAssetPath.WBP_MapCommonTag,
		EdgeWidgetPath = UIAssetPath.WBP_MapEdgeWrap,
		Visibility     = import("ESlateVisibility").SelfHitTestInvisible,
	}
}


return MapConfig