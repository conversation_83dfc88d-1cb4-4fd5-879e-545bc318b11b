---@class WBP_MapPanelView : WBP_MapPanel_C
---@field public WidgetRoot WBP_MapPanel_C
---@field public Ani_Fadein WidgetAnimation
---@field public Get_Img_bg_Brush fun(self:self):SlateBrush


---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button C7Button
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public IconBrush SlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):SlateBrush


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public Text_Com C7TextBlock
---@field public Text_Time TextBlock
---@field public Image C7Image
---@field public Btn_Com C7Button
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein_normal WidgetAnimation
---@field public Ani_Fadein_Light WidgetAnimation
---@field public Ani_Fadein_blue WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public SequenceEvent fun(self:self):void
---@field public Construct fun(self:self):void
---@field public OnVisibilityChangedEvent fun(self:self,InVisibility:ESlateVisibility):void
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_MapPanelComposeView : WBP_MapPanelCompose_C
---@field public WidgetRoot WBP_MapPanelCompose_C
---@field public Text_Name C7TextBlock
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView
---@field public WBP_ComBtnLightM WBP_ComBtnView
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Fadeon WidgetAnimation


---@class WBP_MapPanelIndexNewView : WBP_MapPanelIndexNew_C
---@field public WidgetRoot WBP_MapPanelIndexNew_C
---@field public WBP_ComMaskINV_69 WBP_MapPanelView
---@field public WBP_MapPanelbtn WBP_MapPanelComposeView


---@class P_MapIndexPanelView : WBP_MapPanelIndexNewView
---@field public controller P_MapIndexPanel
local P_MapIndexPanelView = DefineClass("P_MapIndexPanelView", UIView)

function P_MapIndexPanelView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {{self.WBP_ComMaskINV_69_lua.WidgetRoot, 0.550017},{self.WBP_MapPanelbtn_lua.WidgetRoot, 0.33335},},AnimFadeOut = {}}
end

function P_MapIndexPanelView:OnDestroy()
end

return P_MapIndexPanelView
