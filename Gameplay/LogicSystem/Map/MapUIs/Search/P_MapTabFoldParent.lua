---@class P_MapTabFoldParent : UIComponent
---@field public View WBP_MapTabFoldParentView
local P_MapTabFoldParent = DefineClass("P_MapTabFoldParent", UIComponent)

function P_MapTabFoldParent:OnCreate()
    
end

function P_MapTabFoldParent:OnListRefresh(parentUI, bSelected, allData, index)
    self.parentUI = parentUI
    self.index = index
    local info = allData[index].Info
    self.firstId = info.ID
    self.View:SetType(info.SecondTab ~= nil)
    local bFold = self.parent:IsFold(index)
    self.View:SetSelected(not bFold)
    self.View.Text_Title:SetText(info.Name)
end

function P_MapTabFoldParent:OnClickArrow()
    Log.Debug("P_MapTabFoldParent: OnClickArrow")
end

function P_MapTabFoldParent:OnClick(parentUI, allData, index)
    local bFold = self.parent:IsFold(index)
    self.parent:Fold(not bFold, index)

    if bFold then
        self.parent:Sel(index,1)
        parentUI:OnClickSecondTab(index, 1)
    end
    
    if allData[index].Children then
    else
        UI.Invoke("P_MapIndexPanel", "UpdateSearchList", self.firstId)
    end
end

function P_MapTabFoldParent:OnDestroy()
    UIBase.OnDestroy(self)
    self.parentUI = nil
end

function P_MapTabFoldParent:OnClose()

end



return P_MapTabFoldParent