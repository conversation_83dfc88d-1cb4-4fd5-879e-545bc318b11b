---@class WBP_ComListView : WBP_ComList_C
---@field public WidgetRoot WBP_ComList_C
---@field public List ScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public bIsTileView boolean
---@field public PreviewCount number
---@field public LibWidget ListLib
---@field public ScrollWidget Widget
---@field public Orientation EOrientation
---@field public ScrollBarVisibility ESlateVisibility
---@field public SelectionMode number
---@field public Space ListSpace
---@field public Alignment ComListAligment
---@field public bIsCenterContent boolean
---@field public tempIndex number
---@field public oldPosX number
---@field public oldPosY number
---@field public tempPosX number
---@field public tempPosY number
---@field public widgetX number
---@field public widgetY number
---@field public spaceUp number
---@field public spaceBottom number
---@field public spaceLeft number
---@field public spaceRight number
---@field public bSizeToContent boolean
---@field public ListPadding Margin
---@field public MaxValue number
---@field public RetainerBox RetainerBox
---@field public OnSetItem MulticastDelegate
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public CalculatePos fun(self:self):void
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number):void
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public GetListSize fun(self:self):number,number,number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number,number,number,number,number
---@field public VerticalTileChange fun(self:self):void
---@field public VerticalTile fun(self:self):void
---@field public VerticalList fun(self:self):void
---@field public HorizontalTileChange fun(self:self):void
---@field public HorizontalTile fun(self:self):void
---@field public HorizontalList fun(self:self):void
---@field public VerticalTileAuto fun(self:self):void
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidfget:Widget):void


---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button C7Button
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public IconBrush SlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):SlateBrush


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public Text_Com C7TextBlock
---@field public Text_Time TextBlock
---@field public Image C7Image
---@field public Btn_Com C7Button
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_MapPanelComposeView : WBP_MapPanelCompose_C
---@field public WidgetRoot WBP_MapPanelCompose_C
---@field public Text_Name C7TextBlock
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView
---@field public WBP_ComBtnLightM WBP_ComBtnView


---@class WBP_ItemNmlView : WBP_ItemNml_C
---@field public WidgetRoot WBP_ItemNml_C
---@field public Bg_Rarity Image
---@field public Icon Image
---@field public NS_HQ NamedSlot
---@field public TB_Text TextBlock
---@field public TB_Name C7RichTextBlock
---@field public BG_CD Image
---@field public text_center TextBlock
---@field public Status number
---@field public Left Up number
---@field public Is Timeliness boolean
---@field public Is Advent boolean
---@field public Is Score Up boolean
---@field public Quality number
---@field public Is New boolean
---@field public Mask_Brush SlateBrush
---@field public TagBrushArray SlateBrush
---@field public TagTextOutline FontOutlineSettings
---@field public TagTextColorArray SlateColor
---@field public Event_UI_Style fun(self:self,Status:number,LeftUp:number,IsTimeliness:boolean,IsAdvent:boolean,IsScoreUp:boolean,IsNew:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetLT fun(self:self,LeftUp:number):void
---@field public SetRT fun(self:self,IsTimeliness:boolean,IsAdvent:boolean,IsScoreUp:boolean,Status:number):void
---@field public SetStatus fun(self:self,Status:number):void
---@field public SetQuality fun(self:self,Quality:number):void
---@field public SetNew fun(self:self,IsNew:boolean):void
---@field public SetTag fun(self:self,TagState:number):void


---@class WBP_ItemRewardView : WBP_ItemReward_C
---@field public WidgetRoot WBP_ItemReward_C
---@field public Overlay Overlay
---@field public WBP_ItemNml WBP_ItemNmlView
---@field public New_Tip Image
---@field public Checked C7Image
---@field public Selected C7Image
---@field public Big_Button_ClickArea C7Button
---@field public Ani_NewTip WidgetAnimation
---@field public Ani_NewTip_Loop WidgetAnimation
---@field public Is Received boolean
---@field public Event_UI_Style fun(self:self,IsReceived:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetReceived fun(self:self,IsReceived:boolean):void
---@field public SetSelected fun(self:self,isSelected:boolean):void


---@class WBP_MapPanelTaskView : WBP_MapPanelTask_C
---@field public WidgetRoot WBP_MapPanelTask_C
---@field public Img_Tracing Image
---@field public Text_Name C7TextBlock
---@field public Text_location TextBlock
---@field public VB_Targets VerticalBox
---@field public Text_Content C7RichTextBlock
---@field public TileView_Rewards WBP_ComListView
---@field public WBP_MapPanelbtn WBP_MapPanelComposeView
---@field public WBP_ComItemNorm WBP_ItemRewardView


---@class P_MapTaskDetailView : WBP_MapPanelTaskView
---@field public controller P_MapTaskDetail
local P_MapTaskDetailView = DefineClass("P_MapTaskDetailView", UIView)

function P_MapTaskDetailView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapPanelbtn.WBP_ComBtnClose.Button, "OnClick_WBP_MapPanelbtn_WBP_ComBtnClose_Button")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapPanelbtn.WBP_ComBtnLightM.Btn_Com, "OnClick_WBP_MapPanelbtn_WBP_ComBtnLightM_Btn_Com")
    controller:AddUIListener(EUIEventTypes.MouseWheel, self.WidgetRoot, "DummyHandle")
    controller:AddUIListener(EUIEventTypes.TouchStarted, self.WidgetRoot, "DummyHandle")
    controller:AddUIListener(EUIEventTypes.TouchEnded, self.WidgetRoot, "DummyHandle")
    controller:AddUIListener(EUIEventTypes.TouchMoved, self.WidgetRoot, "DummyHandle")

end

return P_MapTaskDetailView
