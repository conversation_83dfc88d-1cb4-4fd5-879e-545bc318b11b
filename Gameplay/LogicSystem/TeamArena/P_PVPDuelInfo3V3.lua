local P_PVPDuelInfo = kg_require("Gameplay.LogicSystem.TeamArena.P_PVPDuelInfo")

local P_PVPDuelInfo3V3 = DefineClass("P_PVPDuelInfo3V3", P_PVPDuelInfo)

function P_PVPDuelInfo3V3:OnRefresh()
    P_PVPDuelInfo.OnRefresh(self)

end

function P_PVPDuelInfo3V3:Refresh(Data, index, maxIndex, left)
    P_PVPDuelInfo.Refresh(self, Data)
	
	self.View:SetOrder(left and 0 or 1)
	self.View:SetInfoOrder(left and 0 or 1)
	self.View:SetSlot(left and 0 or 1)
	self.View:SetInteract(left and 0 or 1)
	
	local bWidth = (index ~= 1 and index ~= maxIndex)
    self.View:SetIsWidth(bWidth)
end

return P_PVPDuelInfo3V3
