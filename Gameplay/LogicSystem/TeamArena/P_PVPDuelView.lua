--------No Class Find, Create a New UIController Class Using Given Filename--------
---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button C7Button
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public IconBrush SlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):SlateBrush


---@class WBP_PVPDuelView : WBP_PVPDuel_C
---@field public WidgetRoot WBP_PVPDuel_C
---@field public WBP_ComBtnCloseNew WBP_ComBtnCloseNewView
---@field public Left_PVPDuelInfoBox WrapBox
---@field public Right_PVPDuelInfoBox WrapBox
---@field public text_cd C7TextBlock
---@field public Is5V5 boolean
---@field public Event_UI_Style fun(self:self,Is5V5:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void

---@class P_PVPDuelView : WBP_PVPDuelView
---@field public controller P_PVPDuel
local P_PVPDuelView = DefineClass("P_PVPDuelView", UIView)

function P_PVPDuelView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComBtnCloseNew.Button, "OnClick_WBP_ComBtnCloseNew_Button")

end

return P_PVPDuelView
