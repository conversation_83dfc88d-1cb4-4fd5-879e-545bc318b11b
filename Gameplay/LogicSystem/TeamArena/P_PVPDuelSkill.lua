kg_require("Gameplay.LogicSystem.TeamArena.P_PVPDuelSkillView")
---@class P_PVPDuelSkill : 
---@field public View WBP_PVPDuelSkillView
local P_PVPDuelSkill = DefineClass("P_PVPDuelSkill", UIComponent)

function P_PVPDuelSkill:OnCreate()
end


function P_PVPDuelSkill:Refresh(skillID)
    Log:Debug("P_PVPDuelSkill")
    local skillinfo = Game.TableData.GetSkillDataNewRow(skillID)
    if skillinfo then
        if not string.isEmpty(skillinfo.SkillIcon) then
            self:SetImage(self.View.Img_Icon,skillinfo.SkillIcon)
        end
    end
end

return P_PVPDuelSkill
