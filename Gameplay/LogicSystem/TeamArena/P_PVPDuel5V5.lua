local P_PVPDuel = kg_require("Gameplay.LogicSystem.TeamArena.P_PVPDuel")
local P_PVPDuel5V5 = DefineClass("P_PVPDuel5V5", P_PVPDuel)
local PVPDuelInfoItem5V5 = kg_require("Gameplay.LogicSystem.TeamArena.P_PVPDuelInfo5V5")

function P_PVPDuel5V5:OnCreate()
    P_PVPDuel.OnCreate(self)

    --最多成员
    self.maxPlayer = 5
    --左侧list
    self.leftDuelInfoItems = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.Left_PVPDuelInfoBox, PVPDuelInfoItem5V5)
    --右侧list
    self.rightDuelInfoItems = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.Right_PVPDuelInfoBox, PVPDuelInfoItem5V5)
end

function P_PVPDuel5V5:OnRefresh(memberSkillDetail)
    P_PVPDuel.OnRefresh(self, memberSkillDetail)
    self.View:Event_UI_Style(true)
end
return P_PVPDuel5V5
