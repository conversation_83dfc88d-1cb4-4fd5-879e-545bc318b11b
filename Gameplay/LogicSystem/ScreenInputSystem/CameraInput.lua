local KismetMathLibrary = import("KismetMathLibrary")
local CameraInput = DefineSingletonClass("CameraInput")

function CameraInput:AddCameraInput(InputVec)
    --Log.Debug(InputVec)
    --XY轴缩放比, 后面应从配置提取
    local Y_Scale = -0.1
    local X_Scale = 0.3
    --滑动参数(影响手感, 先常量,后按属性配置)
    local SlideRate = 50.0
    local Y_Value = InputVec.Y * Y_Scale * SlideRate
    local X_Value = InputVec.X * X_Scale * SlideRate
    NotifyInputAxis("LookUp_Axis", Y_Value)
    NotifyInputAxis("Turn_Axis", X_Value)
    --发送相机旋转消息
    --Game.EventSystem:Publish(_G.EEventTypes.CameraInput, self)
end

function CameraInput:OnCameraStarted(ScreenPos)
    self.TouchStartPos = ScreenPos
    self.CameraLastPos = ScreenPos
    self.DeadZoneOffset = FVector2D(0, 0)
    self.DeadZoneLastPos = ScreenPos
    self.bInDeadZone = true
end

function CameraInput:HandleCameraTouch(ScreenPos)
    if self.bInDeadZone then
        self.DeadZoneOffset = self.DeadZoneOffset + ScreenPos - self.DeadZoneLastPos
        self.DeadZoneLastPos = ScreenPos
    else
        self.Offset = ScreenPos - self.CameraLastPos
        self.Offset.X = self.Offset.X / self.CameraMoveSize.X
        self.Offset.Y = (self.Offset.Y / self.CameraMoveSize.Y) * -1.0
    
        self:AddCameraInput(self.Offset)
        self.CameraLastPos = ScreenPos
    end
    if self.bInDeadZone and KismetMathLibrary.Sqrt(self.DeadZoneOffset:SizeSquared()) > self.DEFAULT_NORMAL_CAMERA_DEAD_ZONE_OFFSET then
        self.bInDeadZone = false
        self.CameraLastPos = ScreenPos
    end
end

function CameraInput:OnCameraEnded()
    self:AddCameraInput(FVector2D(0, 0))
end

function CameraInput:OnTouchStarted(ScreenPos)
    self:OnCameraStarted(ScreenPos)   
end

function CameraInput:OnTouchMoved(ScreenPos)
    self:HandleCameraTouch(ScreenPos)
end

function CameraInput:OnTouchEnded()
    self:OnCameraEnded()
end

function CameraInput:ctor()
    self.CameraMoveSize = nil
    self.TouchStartPos = nil
    self.CameraLastPos = nil
end

function CameraInput:dtor()
    self.CameraMoveSize = nil
    self.TouchStartPos = nil
    self.CameraLastPos = nil
end

function CameraInput:Init(Params)
    self.CameraMoveSize = FVector2D(200, 200)
    self.CameraLastPos = FVector2D(0, 0)
    self.Offset = FVector2D(0, 0)
    self.DEFAULT_NORMAL_CAMERA_DEAD_ZONE_OFFSET = Game.TableData.GetCameraConstDataRow("DEFAULT_NORMAL_CAMERA_DEAD_ZONE_OFFSET")
end

function CameraInput:UnInit()
end

function CameraInput:StopCamera()
    self:OnCameraEnded()
end

return CameraInput
