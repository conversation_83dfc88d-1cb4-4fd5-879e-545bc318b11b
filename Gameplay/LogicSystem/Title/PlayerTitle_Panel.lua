local PlayerTitleDetailsSub = kg_require("Gameplay.LogicSystem.Title.PlayerTitleDetailsSub")
local PlayerTitleBtnSub = kg_require("Gameplay.LogicSystem.Title.PlayerTitleBtnSub")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComTextSearchBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComTextSearchBox")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PlayerTitle_Panel : UIPanel
---@field view PlayerTitle_PanelBlueprint
local PlayerTitle_Panel = DefineClass("PlayerTitle_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")
local TitleSystem = Game.TitleSystem

PlayerTitle_Panel.eventBindMap = {
	[EEventTypesV2.ON_TITLE_HONORIFIC_UPDATE] = "OnTitleHonorificUpdate",
	[EEventTypesV2.ON_WEAR_TILE_UPDATE] = "OnWearTitleUpdate",
	[EEventTypesV2.ON_TITLE_HONORIFIC_EXPIRE] = "OnTitleExpire",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerTitle_Panel:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

---初始化数据
function PlayerTitle_Panel:InitUIData()
	---@type number 面板类型
	self.panelType = nil
	---@type number 当前选中的titleID
	self.currentSelectTitleID = nil
	---@type string 输入框的过滤文字
	self.filterText = ""
	---@type number 用于实时检测佩戴的称号变化
	self.lastSecondWearID = nil
	---@type number 本界面Tips按钮对应的TipsID
	self.tipsID = nil
	---@type table 列表数据
	self.titleListData = {}
end

--- UI组件初始化，此处为自动生成
function PlayerTitle_Panel:InitUIComponent()
    ---@type PlayerTitleDetailsSub
    self.WBP_PlayerTitleDetailsSubCom = self:CreateComponent(self.view.WBP_PlayerTitleDetailsSub, PlayerTitleDetailsSub)
    ---@type UIComEmptyConent
    self.WBP_ItemEmptyCom = self:CreateComponent(self.view.WBP_ItemEmpty, UIComEmptyConent)
    ---@type PlayerTitleBtnSub
    self.WBP_Btn_StateCom = self:CreateComponent(self.view.WBP_Btn_State, PlayerTitleBtnSub)
    ---@type UIListView
    self.WBP_ComListCom = self:CreateComponent(self.view.WBP_ComList, UIListView)
    ---@type UIComTextSearchBox
    self.WBP_ComInputCom = self:CreateComponent(self.view.WBP_ComInput, UIComTextSearchBox)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIComDiyTitle
    self.Text_MatchTypeCom = self:CreateComponent(self.view.Text_MatchType, UIComDiyTitle)
end

---UI事件在这里注册，此处为自动生成
function PlayerTitle_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.view.TipsButton.OnClicked, "on_TipsButton_Clicked")
    self:AddUIEvent(self.WBP_Btn_StateCom.onClickEvent, "on_WBP_Btn_StateCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComListCom.onItemSelected, "on_WBP_ComListCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComInputCom.onTextChanged, "on_WBP_ComInputCom_TextChanged")
    self:AddUIEvent(self.WBP_ComInputCom.onLimitOver, "on_WBP_ComInputCom_LimitOver")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerTitle_Panel:InitUIView()
end

function PlayerTitle_Panel:OnRefresh(panelType)
	self.panelType = panelType
	local hintText = Game.TableData.GetConstDataRow("TITLE_MODIFY_TAG_TEXT_TIPS")
	if panelType == Const.TITLE_TYPE.TITLE then
		self.tipsID = Enum.ETipsData["TITLE_DESC"]
		self.Text_MatchTypeCom:Refresh(StringConst.Get("TITLESYSTEM_TITLE"))
	elseif panelType == Const.TITLE_TYPE.HONORIFIC then
		self.tipsID = Enum.ETipsData["HONORIFIC_DESC"]
		self.Text_MatchTypeCom:Refresh(StringConst.Get("TITLESYSTEM_HONORIFIC"))
		hintText = Game.TableData.GetConstDataRow("HONORIFIC_MODIFY_TAG_TEXT_TIPS")
	end
	self:innerInitPanel()
	self.WBP_ComInputCom:Refresh(hintText, Game.TableData.GetConstDataRow("TITLE_TAG_NAME_MAX_COUNT"))
end

function PlayerTitle_Panel:OnClose()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TITLE_PANEL_CLOSE)
	self.filterText = ""
	self.WBP_ComInputCom.view.EditText_Input:SetText("")
	self:endTimer()
end

function PlayerTitle_Panel:TickSecond()
	TitleSystem:RefreshExpireTitleInfo(self.panelType)
	for k, data in ipairs(self.titleListData) do
		local titleID = data.titleID
		local ownTitleInfo = TitleSystem:GetOwnTitleInfo(titleID, self.panelType)
		if ownTitleInfo then
			--已拥有该称号
			local isPermanent = TitleSystem:CheckTitleIsPernament(ownTitleInfo)
			if not isPermanent then
				--限时称号，更新倒计时
				local leftTime = TitleSystem:GetTitleLeftTime(ownTitleInfo)
				if leftTime >= 0 then
					self.WBP_ComListCom:RefreshItemByIndex(k)
				end
			end
		end
	end

	if TitleSystem:GetWearTitleID(self.panelType, Game.me) ~= self.lastSecondWearID then
		Log.DebugFormat("wear info unvalid")
		self.lastSecondWearID = TitleSystem:GetWearTitleID(self.panelType, Game.me)
		self:refreshWearInfo()
	end
end

function PlayerTitle_Panel:innerInitPanel()
	self:startTimer()
	self.lastSecondWearID = TitleSystem:GetWearTitleID(self.panelType, Game.me)
	self:OnSelectTitle(TitleSystem:GetWearTitleID(self.panelType, Game.me))
	self:createTitleList(true)
end

function PlayerTitle_Panel:setWidgetIcon(widget, iconName)
	local iconPath = Game.UIIconUtils.getIcon(iconName)
	if iconPath then
		widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self:SetImage(widget, iconPath)
	else
		widget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--选择一个titleID
function PlayerTitle_Panel:OnSelectTitle(titleID)
	self.currentSelectTitleID = titleID
	--刷新介绍区域信息
	local titleInfo = TitleSystem:GetTableDataRowByType(titleID, self.panelType)
	local detailsPanel = self.WBP_PlayerTitleDetailsSubCom
	if titleInfo and not TitleSystem:IsEmptyTitleID(titleID) then
		detailsPanel.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		--描述
		detailsPanel.view.Text_Description:SetText(titleInfo.TitleContentText)
		--来源
		detailsPanel.view.Text_Src:SetText(titleInfo.TitleOrigin)
	else
		detailsPanel.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end

	--更新列表下方按钮（已佩戴、已过期）
	self:refreshCurrentSelectTitleBtnState()
end

function PlayerTitle_Panel:OnTitleHonorificUpdate(type)
	if type == self.panelType then
		self:createTitleList(true)
		self:refreshCurrentSelectTitleBtnState()
	end
end

function PlayerTitle_Panel:OnWearTitleUpdate(type, entityID)
	if type == self.panelType and entityID == GetMainPlayerEID() then
		self.lastSecondWearID = TitleSystem:GetWearTitleID(self.panelType, Game.me)
		self:refreshWearInfo()
	end
end

function PlayerTitle_Panel:OnTitleExpire(type, titleID)
	if type == self.panelType then
		self:createTitleList(true)
		self:refreshCurrentSelectTitleBtnState()
	end
end

function PlayerTitle_Panel:refreshWearInfo()
	self:createTitleList()
	self:refreshCurrentSelectTitleBtnState()
end

--创建称号列表
--scrollToWear:是否使佩戴的称号滚动到视野中
function PlayerTitle_Panel:createTitleList(scrollToWear)
	--构造称号列表数据
	table.clear(self.titleListData)

	local orderedTitleIDList = TitleSystem:GetOrderedTitleIDList(self.panelType)

	--过滤列表的时候，如果旧的数据有选中数据，需要进行选中
	local newSelectIndex = nil

	for k, titleID in ipairs(orderedTitleIDList) do
		local titleInfo = TitleSystem:GetTableDataRowByType(titleID, self.panelType)
		local titleText = TitleSystem:GetTitleTextByID(titleID, self.panelType)
		local valid = true
		if self.filterText and self.filterText ~= "" then
			--需要进行文本过滤
			if titleInfo then
				--找到了称号信息，过滤文本检测通过
				if not string.find(titleText, self.filterText) then
					valid = false
				end
			else
				--未找到称号信息，说明是空称号
				valid = false
			end
		end
		if valid then
			table.insert(self.titleListData, {titleID = titleID, panelType = self.panelType})

			if self.currentSelectTitleID == titleID then
				newSelectIndex = #self.titleListData
			end
		end
	end

	--是否需要将佩戴的元素滚动到视野中
	local scrollIndex = nil
	if scrollToWear then
		local wearTitleID = TitleSystem:GetWearTitleID(self.panelType, Game.me)
		if not TitleSystem:IsEmptyTitleID(wearTitleID) then
			for k, data in ipairs(self.titleListData) do
				local titleID = data.titleID
				if titleID == wearTitleID then
					scrollIndex = k
					break
				end
			end
		end
	end
	
	self.WBP_ComListCom:Refresh(self.titleListData)
	if scrollIndex then
		self.WBP_ComListCom:ScrollToItemByIndex(scrollIndex)
	end
	if newSelectIndex then
		self.WBP_ComListCom:ClearSelection()
		self.WBP_ComListCom:SetSelectedItemByIndex(newSelectIndex, true)
	end

	--当前暂无内容显示处理
	self.WBP_ItemEmptyCom.userWidget:SetVisibility(#self.titleListData > 0 and ESlateVisibility.Collapsed or ESlateVisibility.SelfHitTestInvisible)
end

function PlayerTitle_Panel:refreshCurrentSelectTitleBtnState()
	if not self.currentSelectTitleID then
		self.WBP_Btn_StateCom:Hide()
		return
	end

	self.WBP_Btn_StateCom:Show()

	if self.currentSelectTitleID == TitleSystem:GetWearTitleID(self.panelType, Game.me) then
		--选中的Title，就是当前佩戴的title
		self.WBP_Btn_StateCom:SetIsWearing(true)
	else
		--不是当前佩戴的
		self.WBP_Btn_StateCom:SetIsWearing(false)
		local ownTitleInfo = TitleSystem:GetOwnTitleInfo(self.currentSelectTitleID, self.panelType)
		if ownTitleInfo then
			--已拥有该称号
			if TitleSystem:CheckTitleIsAvailable(ownTitleInfo) then
				--称号可用，可以佩戴
				self:setWearBtnDisable(false, StringConst.Get("TITLESYSTEM_WEAR"))
			else
				--不可用
				self:setWearBtnDisable(true, StringConst.Get("TITLESYSTEM_LOCK"))
			end
		else
			--未拥有该称号
			self:setWearBtnDisable(true, StringConst.Get("TITLESYSTEM_LOCK"))
		end
	end
end

function PlayerTitle_Panel:setWearBtnDisable(disable, btnText)
	self.WBP_Btn_StateCom:SetWearBtnDisable(disable, btnText)
end

function PlayerTitle_Panel:startTimer()
	self.timerHandle = self:StartTimer(
			"PlayerTitle_PanelTimer",
		function()
			self:TickSecond()
		end, 1000, -1, true)
end

function PlayerTitle_Panel:endTimer()
	if self.timerHandle then
		self:StopTimer("PlayerTitle_PanelTimer")
		self.timerHandle = nil
	end
	if self.timerHandle then
		Game.TimerManager:StopTimerAndKill(self.timerHandle)
		self.timerHandle = nil
	end
end

function PlayerTitle_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
	self:CloseSelf()
end

function PlayerTitle_Panel:on_TipsButton_Clicked()
	Game.TipsSystem:ShowTips(self.tipsID, self.view.TipsButton:GetCachedGeometry())
end

--- 此处为自动生成
function PlayerTitle_Panel:on_WBP_Btn_StateCom_ClickEvent()
	if TitleSystem:GetWearTitleID(self.panelType, Game.me) == self.currentSelectTitleID then
		--佩戴中，当前没有点击卸下功能
		Log.DebugFormat("try to unwear")
		TitleSystem.sender:RequestPutOffTitle(self.panelType)
		return
	end
	local ownTitleInfo = TitleSystem:GetOwnTitleInfo(self.currentSelectTitleID, self.panelType)
	if ownTitleInfo and TitleSystem:CheckTitleIsAvailable(ownTitleInfo) then
		--已拥有该称号
		--点击，佩戴
		if TitleSystem:IsEmptyTitleID(self.currentSelectTitleID) then
			Log.DebugFormat("try to wear empty title, put off current title")
			--如果是空称号，则不是佩戴空称号，而是把当前佩戴的称号卸下
			TitleSystem.sender:RequestPutOffTitle(self.panelType)
		else
			Log.DebugFormat("try to wear")
			TitleSystem.sender:RequestPutOnTitle(self.panelType, self.currentSelectTitleID)
		end
		return
	end

	--未拥有该称号，点击，提示未解锁
	Log.DebugFormat("lock ,can not wear")
end

--- 此处为自动生成
---@param index number
---@param data table
function PlayerTitle_Panel:on_WBP_ComListCom_ItemSelected(index, data)
	local titleID = data.titleID
	self:OnSelectTitle(titleID)
end

--- 此处为自动生成
---@param text string
function PlayerTitle_Panel:on_WBP_ComInputCom_TextChanged(text)
	self.filterText = text
	--重新构造一遍列表数据
	self:createTitleList(false)
end

function PlayerTitle_Panel:on_WBP_ComInputCom_LimitOver()
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_INPUT_MAX)
end

return PlayerTitle_Panel
