local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerTitleIconSub : UIComponent
---@field view PlayerTitleIconSubBlueprint
local PlayerTitleIconSub = DefineClass("PlayerTitleIconSub", UIComponent)

PlayerTitleIconSub.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerTitleIconSub:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerTitleIconSub:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PlayerTitleIconSub:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerTitleIconSub:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerTitleIconSub:InitUIView()
end

---组件刷新统一入口
function PlayerTitleIconSub:Refresh(...)
end

return PlayerTitleIconSub
