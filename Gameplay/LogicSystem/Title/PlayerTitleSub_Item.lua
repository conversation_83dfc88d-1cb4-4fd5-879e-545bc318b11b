local PlayerTitle = kg_require("Gameplay.LogicSystem.PlayerDetails.PlayerTitle")
local PlayerHonorific = kg_require("Gameplay.LogicSystem.PlayerDetails.PlayerHonorific")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PlayerTitleSub_Item : UIListItem
---@field view PlayerTitleSub_ItemBlueprint
local PlayerTitleSub_Item = DefineClass("PlayerTitleSub_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local Const = kg_require("Shared.Const")

PlayerTitleSub_Item.MINUTE_SECONDS = 60
PlayerTitleSub_Item.HOUR_SECONDS = PlayerTitleSub_Item.MINUTE_SECONDS * 60
PlayerTitleSub_Item.DAY_SECONDS = PlayerTitleSub_Item.HOUR_SECONDS * 24

PlayerTitleSub_Item.eventBindMap = {
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerTitleSub_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerTitleSub_Item:InitUIData()
	---@type number 称号ID
	self.titleID = nil
	---@type number 面板类型
	self.panelType = nil
end

--- UI组件初始化，此处为自动生成
function PlayerTitleSub_Item:InitUIComponent()
    ---@type PlayerTitle
    self.WBP_PlayerTitleCom = self:CreateComponent(self.view.WBP_PlayerTitle, PlayerTitle)
    ---@type PlayerHonorific
    self.WBP_PlayerHonorificCom = self:CreateComponent(self.view.WBP_PlayerHonorific, PlayerHonorific)

end

---UI事件在这里注册，此处为自动生成
function PlayerTitleSub_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerTitleSub_Item:InitUIView()
end

function PlayerTitleSub_Item:OnRefresh(data)
	self.titleID = data.titleID
	self.panelType = data.panelType
	--称号文本
	if Game.TitleSystem:IsEmptyTitleID(self.titleID) then
		--空称号图标
		self.view.Img_None:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.Img_None:SetVisibility(ESlateVisibility.Collapsed)
	end

	self.WBP_PlayerTitleCom:Hide()
	self.WBP_PlayerHonorificCom:Hide()

	if self.panelType == Const.TITLE_TYPE.TITLE then
		self.WBP_PlayerTitleCom:Show()
		self.WBP_PlayerTitleCom:SetTitle(self.titleID, self.panelType)
	else
		self.WBP_PlayerHonorificCom:Show()
		self.WBP_PlayerHonorificCom:SetTitle(self.titleID, self.panelType)
	end
	
	local isWearing = Game.TitleSystem:GetWearTitleID(self.panelType, Game.me) == self.titleID
	--正在佩戴，显示佩戴中
	self.userWidget:SetIsWearing(isWearing)

	--倒计时处理
	local leftTimeVisible, leftTime = self:checkShouldShowLeftTime(self.titleID)
	if leftTimeVisible then
		self.userWidget:SetTimeVisible(leftTimeVisible, self:getLeftTimeText(leftTime))
	else
		self.userWidget:SetTimeVisible(leftTimeVisible, "")
	end

	--上锁图标处理
	self.userWidget:SetIsLock(self:checkShouldShowLock(self.titleID))
end

function PlayerTitleSub_Item:UpdateSelectionState(selected)
	--select 选中信息
	self.view.Img_BgLight:SetVisibility(selected and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

--一个称号是否应该显示倒计时UI信息
function PlayerTitleSub_Item:checkShouldShowLeftTime(titleID)
	local ownTitleInfo = Game.TitleSystem:GetOwnTitleInfo(titleID, self.panelType)
	if ownTitleInfo then
		--该称号已拥有
		local isPermanent = Game.TitleSystem:CheckTitleIsPernament(ownTitleInfo)
		if isPermanent then
			--永久称号，隐藏倒计时
			return false
		end
		--限时称号
		local leftTime = Game.TitleSystem:GetTitleLeftTime(ownTitleInfo)
		if leftTime > 0 then
			--还有剩余时间
			--显示倒计时
			return true, leftTime
		end
		--已过期
		--隐藏倒计时
		return false

	end
	--该称号未拥有
	--隐藏倒计时
	return false
end

--一个称号是否应该显示锁定图标信息
function PlayerTitleSub_Item:checkShouldShowLock(titleID)
	local ownTitleInfo = Game.TitleSystem:GetOwnTitleInfo(titleID, self.panelType)
	if ownTitleInfo then
		--该称号已拥有
		local isPermanent = Game.TitleSystem:CheckTitleIsPernament(ownTitleInfo)
		if isPermanent then
			--永久称号，上锁图标隐藏
			return false
		else
			--限时称号
			local leftTime = Game.TitleSystem:GetTitleLeftTime(ownTitleInfo)
			if leftTime > 0 then
				--还有剩余时间
				--上锁图标隐藏
				return false
			end
			--已过期
			--上锁图标显示
			return true
		end
	else
		--该称号未拥有
		--显示上锁
		return true
	end
end

function PlayerTitleSub_Item:getLeftTimeText(leftTime)
	if leftTime >= PlayerTitleSub_Item.DAY_SECONDS then
		return string.format(StringConst.Get("LEFTDAY"), math.floor(leftTime / PlayerTitleSub_Item.DAY_SECONDS))
	end
	if leftTime >= PlayerTitleSub_Item.HOUR_SECONDS then
		return string.format(StringConst.Get("LEFTHOUR"), math.floor(leftTime / PlayerTitleSub_Item.HOUR_SECONDS))
	end
	if leftTime >= PlayerTitleSub_Item.MINUTE_SECONDS then
		return string.format(StringConst.Get("LEFTMINUTE"), math.floor(leftTime / PlayerTitleSub_Item.MINUTE_SECONDS))
	end
	return StringConst.Get("LEFTLASTMINUTE")
end

return PlayerTitleSub_Item
