---@class MountSystemSender
local MountSystemSender = DefineClass("MountSystemSender",SystemSenderBase)

---@public 邀请共乘
function MountSystemSender:ReqInviteOnMount(eid)
    self.Bridge.remote:ReqInviteOnMount(eid)
end

---@public 接受邀请
function MountSystemSender:ReqAcceptMountInvite(eid)
    self.Bridge.remote:ReqAcceptMountInvite(eid)
end

---@public 拒绝邀请
function MountSystemSender:ReqRefuseMountInvite(eid)
    self.Bridge.remote:ReqRefuseMountInvite(eid)
end

---@public 乘客下马（同乘）
function MountSystemSender:ReqGetOffOtherMount()
    self.Bridge.remote:ReqGetOffOtherMount()
end

---@public 请求获取坐骑数据
function MountSystemSender:ReqGetMountWardrobeData()
    self.Bridge.remote:ReqGetMountWardrobeData()
end

---@public 请求收藏坐骑
function MountSystemSender:ReqCollectFashionMount(mountId)
    self.Bridge.remote:ReqCollectFashionMount(mountId)
end

---@public 请求取消收藏坐骑
function MountSystemSender:ReqCancelCollectFashionMount(mountId)
    self.Bridge.remote:ReqCancelCollectFashionMount(mountId)
end

---@public 请求购买坐骑
---@param mountId number 坐骑ID
function MountSystemSender:ReqBuyMountComp(mountId)
    self.Bridge.remote:ReqBuyMountComp(mountId)
end

---@public 请求保存坐骑
function MountSystemSender:ReqSaveMountComp(mountId)
    self.Bridge.remote:ReqSaveMountComp(mountId)
end

---@public 
function MountSystemSender:ReqChangeMountNewFlag(subType)
	local remote = self.Bridge.remote
	if not remote then return end
    remote:ReqChangeMountNewFlag(subType)
end

return MountSystemSender