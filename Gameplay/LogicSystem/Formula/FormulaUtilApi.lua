--[[
1. 技能描述表达式samples

local d11 = "技能CD {C, SkillCD, S_1000001, Task_Yellow}"                      -- 读取skill/buff表的指定行的指定列的数据
local d12 = "buff时长{CS, BuffDuration, c, 2, B_1000002, Task_Yellow}"         -- 读取skill/buff表的指定行的指定列的数据, 以“,"分割，读取分割后的第2个数据(用c代指",")
local d21 = "附加buff{I, AddBuffID, 1, BA_2000002, Task_Yellow}"               -- 读取skill/buff表的指定行的指定列(list)的第几个元素
local d22 = "增加{IS, BuffPropModify, 1, *, 2, BA_2000002, Task_Yellow}"       -- 读取skill/buff表的指定行的指定列(list)的第1个元素, 以“*"分割，读取分割后的第2个数据
local d44 = "造成 {F100001, mark, SA_1000001, Task_Yellow} 伤害"                -- 调用公式100001

2. 提供给公式系统的API

2.1 常见的数学接口

2.2 utils api

-- AbilityIdStr的格式
-- S_1000001: 表示SkillData表的key=1000001的行
-- SA_1000001: 表示SkillAction表的key=1000001的行
-- B_1000001: 表示BuffData表的key=1000001的行
-- BA_1000001: 表示BuffAction表的key=1000001的行

-- GetProp(propName): 获取主角属性值
-- GetLevel(): 获取主角的等级
-- GetClass(): 获取主角职业
-- GetAbDataCol(AbilityIdStr, ColName): 获取技能/buff表指定行的指定列的数据
-- GetAbDataColSplitAt(AbilityIdStr, ColName, Delimiter, SIndex): 获取技能/buff表指定行的指定列的数据, 以Delimiter分割，读取分割后的第SIndex个数据(用c代指",")
-- GetAbDataListColAt(AbilityIdStr, ColName, Index): 读取技能/buff表的指定行的指定列(list)的第Index个元素
-- GetAbDataListColAtSplitAt(AbilityIdStr, ColName, Index, Delimiter, SIndex): 读取技能/buff表的指定行的指定列(list)的第Index个元素, 以Delimiter分割，读取分割后的第SIndex个数据

]]--

FormulaUtilApi = DefineClass("FormulaUtilApi")

-- 数据填写类型
local EDataFillType = {-- luacheck: ignore
    -- 数值
    Number = 0,
    -- 公式
    Formula = 1,
}

function FormulaUtilApi:ctor()

end

function FormulaUtilApi:dtor()

end

---@brief 获取主角属性值
---@param propName string
---@return number
function FormulaUtilApi.GetProp(propName)
    return GetMainPlayerPropertySafely(propName) or 0.0
end

---@brief 获取主角的等级
---@return number
function FormulaUtilApi.GetLevel()
    return GetMainPlayerPropertySafely("Level") or 1
end


---@brief 获取主角的职业
---@return number
function FormulaUtilApi.GetClass()
    return GetMainPlayerPropertySafely("Profession") or 0
end

--region AbilityRawData
function FormulaUtilApi.IsLvlExpression(ExpStr)
    if ExpStr == nil then
        return false
    end

    if type(ExpStr) ~= "string" then
        return false
    end

    local s, _ = string.find(ExpStr, "Lv")
    return s ~= nil
end

function FormulaUtilApi.EvalAbDataValue(ExpStr, AbilityLvl)
    if ExpStr == nil then
        return nil
    end

    if FormulaUtilApi.IsLvlExpression(ExpStr) then
        local script = "return " .. ExpStr
        local func = load(script, nil, nil, setmetatable({Lv = AbilityLvl}, {__index = _G.FormulaEnv}))
        if func then
            local ret = { func() }
            local retCount = #ret
            if retCount == 1 then 
                return ret[1]
            end
        end
    elseif tonumber(ExpStr) then
        return tonumber(ExpStr)
    end

    return ExpStr
end

function FormulaUtilApi.GetAbDataRow(AbilityIdStr)
    local Prefix,ID  = table.unpack(string.split(AbilityIdStr,"_"))
    ID = tonumber(ID)
    if Prefix =="S" then
        return Game.TableData.GetSkillDataNewRow(ID)
    elseif  Prefix =="SA" then
        return nil
    elseif  Prefix =="B" then
        return nil
    elseif  Prefix =="BA" then
        return nil
    end
    return nil
end

function FormulaUtilApi.getAbDataColRaw(AbilityIdStr, ColName)
    local RowData = FormulaUtilApi.GetAbDataRow(AbilityIdStr)
    if RowData == nil then
        return nil
    end

    return RowData[ColName]
end

function FormulaUtilApi.GetAbDataCol(AbilityIdStr, AbilityLvl, ColName)
    local RawData = FormulaUtilApi.getAbDataColRaw(AbilityIdStr, ColName)
    if RawData == nil then
        return nil
    end

    return FormulaUtilApi.EvalAbDataValue(RawData, AbilityLvl)
end

function FormulaUtilApi.GetAbDataColSplitAt(AbilityIdStr, AbilityLvl, ColName, Delimiter, SIndex)
    local RawData = FormulaUtilApi.getAbDataColRaw(AbilityIdStr, ColName)
    if RawData == nil then
        return nil
    end

    if type(RawData) == "string" then
        local Splits = string.split(RawData, Delimiter)
        if Splits == nil then
            return nil
        end

        return FormulaUtilApi.EvalAbDataValue(Splits[SIndex], AbilityLvl)
    end

    return nil
end


function FormulaUtilApi.getAbDataListColAtRaw(AbilityIdStr, ColName, Index)
    local RawData = FormulaUtilApi.getAbDataColRaw(AbilityIdStr, ColName)
    if RawData == nil then
        return nil
    end

    if type(RawData) == "table" then
        return RawData[Index]
    end

    return nil
end

function FormulaUtilApi.GetAbDataListColAt(AbilityIdStr, AbilityLvl, ColName, Index)
    local RawData = FormulaUtilApi.getAbDataListColAtRaw(AbilityIdStr, ColName, Index)
    if RawData == nil then
        return nil
    end

    return FormulaUtilApi.EvalAbDataValue(RawData, AbilityLvl)
end

function FormulaUtilApi.GetAbDataListColAtSplitAt(AbilityIdStr, AbilityLvl, ColName, Index, Delimiter, SIndex)
    local RawData = FormulaUtilApi.getAbDataListColAtRaw(AbilityIdStr, ColName, Index)
    if RawData == nil then
        return nil
    end

    if type(RawData) == "string" then
        local Splits = string.split(RawData, Delimiter)
        if Splits == nil then
            return nil
        end

        return FormulaUtilApi.EvalAbDataValue(Splits[SIndex], AbilityLvl)
    end

    return nil
end

--endregion AbilityRawData

--endregion AbilityLevelRelateData

return FormulaUtilApi
