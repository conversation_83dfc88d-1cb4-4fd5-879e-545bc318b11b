local math = math
--local ipairs = ipairs
local pairs = pairs
local unpack = unpack or table.unpack

local math_round = function(num) return math.floor(num + 0.5) end-- luacheck: ignore
local math_pow = function(x, y) return  x ^ y end-- luacheck: ignore

local FormulaUtilApi = kg_require "Gameplay.LogicSystem.Formula.FormulaUtilApi"
local FormulaFuncData = kg_require("Data.Formula.ClientFormulaFuncData")
local FightFormulaFuncData = kg_require("Data.Formula.ClientFightFormulaFuncData")
local ClientOnlyFuncData = kg_require("Data.Formula.ClientOnlyFuncData")
-- 公式环境
_G.FormulaEnv = {

}

-- local cache
local FormulaEnv = _G.FormulaEnv

-- 公式管理器
FormulaManager = DefineClass("FormulaManager")

function FormulaManager:ctor()

end

function FormulaManager:dtor()

end

function FormulaManager:Init()
    FormulaManager.doReload()
end

function FormulaManager:UnInit()
end

---@brief 添加一些helpFunction到全局FormulaEnv
---@param functionName string
---@param func function
function FormulaManager.addFunc2GlobalFormulaEnv(functionName, func)
    FormulaEnv[functionName] = func

    Log.DebugFormat("addFunc2GlobalFormulaEnv, funcName[%s]", functionName)
end

---@brief 添加一些math函数到全局FormulaEnv
function FormulaManager.addMathFunction2GlobalFormulaEnv()
    local mathFuncMap = {
        max = math.max,
        Max = math.max,
        min = math.min,
        Min = math.min,
        ceil = math.ceil,
        Ceil = math.ceil,
        floor = math.floor,
        Floor = math.floor,
        round = math_round,
        Round = math_round,
        abs = math.abs,
        Abs = math.abs,
        random = math.random,
        Random = math.random,
        pow = math_pow,
        Pow = math_pow,
        Ln = math.log,
        Cos = math.cos
    }

    for k, v in pairs(mathFuncMap) do
        FormulaManager.addFunc2GlobalFormulaEnv(k, v)
    end
end

---@brief 添加auxiliary函数到全局FormulaEnv
function FormulaManager.addAuxiliaryFunction2GlobalFormulaEnv()
    local auxiliaryFuncMap = {
        print = print,
        string = string,
        tonumber = tonumber,
        tostring = tostring,
        -- utils api
        GetProp = FormulaUtilApi.GetProp,
        GetLevel = FormulaUtilApi.GetLevel,
        GetClass = FormulaUtilApi.GetClass,
        GetAbDataRow = FormulaUtilApi.GetAbDataRow,
        GetAbDataCol = FormulaUtilApi.GetAbDataCol,
        GetAbDataColSplitAt = FormulaUtilApi.GetAbDataColSplitAt,
        GetAbDataListColAt = FormulaUtilApi.GetAbDataListColAt,
        GetAbDataListColAtSplitAt = FormulaUtilApi.GetAbDataListColAtSplitAt,
        -- utils api end
    }

    for k, v in pairs(auxiliaryFuncMap) do
        FormulaManager.addFunc2GlobalFormulaEnv(k, v)
    end
end

function FormulaManager.doReload()

    Log.Debug("-------------- FormulaManager reload start -----------------")

    -- clear the env
    _G.FormulaEnv = {}
    FormulaEnv = _G.FormulaEnv
    
    -- Lua 5.2不再支持setfenv，需要手动设置env
    -- 0. add math functions to global formula env
    FormulaManager.addMathFunction2GlobalFormulaEnv()
    
    -- 1. Load ClienFormulaFuncData
    for key, value in pairs(FormulaFuncData) do
        FormulaEnv[key] = value
    end
    -- 2. Load ClientFightFormulaFuncData
    for key, value in pairs(FightFormulaFuncData) do
        FormulaEnv[key] = value
    end
    -- 3. Load CLientOnlyFormula (FormulaForClientData.lua)
    for key, value in pairs(ClientOnlyFuncData) do
        FormulaEnv[key] = value
    end
    
    -- 3. Load auxiliaryFuncMap
    FormulaManager.addAuxiliaryFunction2GlobalFormulaEnv()
    
    Log.Debug("-------------- FormulaManager reload end -----------------")
end

function FormulaManager.Reload()
    FormulaManager.doReload()
end


---region FAttackCtxXX
---@class FDInCtx
---@field SkillData SkillData: 技能数据(如果是技能伤害的话)
---@field BuffData  BuffData: buff数据(如果是buff伤害的话)
---@field IsBlocked boolean: 是否格挡
---@field IsCritical boolean: 是否暴击
---@field IsAllowDead boolean:  如果本次不允许死亡，然后本次伤害是致命的(IsDeadly)，会触发薄葬礼效果(当前血量会恢复到最大生命*RestoreHP)
---@field RestoreHP number: 触发薄葬效果的时候，恢复到的最大生命百分比(float), optional
---@field DamageCoefficient number: 伤害分摊系数(没有分摊的话，默认是1)(float)
---@field DamageCalcType number: 伤害计算类型(1, 使用攻击力; 2, 目标最大血量百分比; 3, 使用指定的传入值; 4, 目标当前血量百分比)(int); 通过不同的Action来区分，不用传入
---@field ExtraDamage number: 伤害类型为3的时候，外界传入的值(float); 需要的时候传入

---@class FDOutCtx
---@field IsDeadly boolean: 本次伤害是否致命
---@field TotalDamage boolean: 本次伤害总值

---@class FAttackCtx
---@field Attacker FightPropProxy	: 攻击者
---@field Defender FightPropProxy	: 受击者
---@field FDIn FDInCtx				: 输入上下文
---@field FDOut FDOutCtx			: 输出上下文

---@param attackCtx FAttackCtx
function FormulaManager.PrepareAttackCtx(attackCtx)
    FormulaEnv["a"] = attackCtx.Attacker
    FormulaEnv["d"] = attackCtx.Defender
    FormulaEnv["FDIn"] = attackCtx.FDIn
    FormulaEnv["FDOut"] = attackCtx.FDOut
end

function FormulaManager.ClearAttackCtx()
    FormulaEnv["a"] = nil
    FormulaEnv["d"] = nil
    FormulaEnv["FDIn"] = nil
    FormulaEnv["FDOut"] = nil
end


---@param formulaName string
---@return function
function FormulaManager.GetFormulaFuncByName(formulaName)
    return FormulaEnv[formulaName]
end

function FormulaManager.GetFormulaFuncByKey(formulaKey)
    local formuladata = Game.TableData.GetFormulaDataRow(formulaKey)
    if not formuladata then
        formuladata = Game.TableData.GetFightActionDataRow(formulaKey)
        if not formuladata then
            formuladata = Game.TableData.GetFormulaForClientDataRow(formulaKey)
            if not formuladata then return end
            return FormulaEnv[formuladata.Name]
        end
        return FormulaEnv[formuladata.ActionName]
    end
    return FormulaEnv[formuladata.Name]
end

---@param formulaName string
---@return boolean, any  :  flag if the formula call is success; formula return
function FormulaManager.CallFormula(formulaName, ...)
    local formulaFunc = FormulaEnv[formulaName]
    if formulaFunc == nil then
        Log.ErrorFormat("CallFormula %s, not found the formula", formulaName)
        return false
    end

    return xpcall(formulaFunc, function(msg)
        Log.ErrorFormat("CallFormula %s, failed %s", formulaName, msg)
        return msg
    end, ...)
end

---@param formulaName string
---@param attackCtx FAttackCtx
---@return boolean, any  :  flag if the formula call is success; formula return
function FormulaManager.CallFormulaWithAttackCtx(formulaName, attackCtx, ...)
    FormulaManager.PrepareAttackCtx(attackCtx)
    local ret = {FormulaManager.CallFormula(formulaName, ...)}
    FormulaManager.ClearAttackCtx()

    return unpack(ret)
end


return FormulaManager
