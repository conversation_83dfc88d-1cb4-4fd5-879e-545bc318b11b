local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class ReconnectLoading_Panel : UIPanel
---@field view ReconnectLoading_PanelBlueprint
local ReconnectLoading_Panel = DefineClass("ReconnectLoading_Panel", UIPanel)

ReconnectLoading_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReconnectLoading_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReconnectLoading_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReconnectLoading_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ReconnectLoading_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReconnectLoading_Panel:InitUIView()
end

---面板打开的时候触发
function ReconnectLoading_Panel:OnRefresh(textKey)
	textKey = textKey or "RECONNECTING"
	self.view.Load:SetText(StringConst.Get(textKey))
	self:PlayAnimation(self.view.Ani_Fadein, function()
		self:PlayAnimation(self.view.Ani_Loop)
	end)
end

return ReconnectLoading_Panel
