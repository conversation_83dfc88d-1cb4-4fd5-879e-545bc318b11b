local EWorldManageStage = kg_require("Gameplay.CommonDefines.WorldViewConst").WORLD_MANAGE_STAGE
---@class LoadingSystem:SystemBase
local LoadingSystem = DefineClass("LoadingSystem", SystemBase)

LoadingSystem.MAIN_PACKAGE_PERCENTAGE = 10

function LoadingSystem:onInit()
    self.loadingAudioID = 0
    self.loadingPresetID = 0
    self.nextLevel = nil
    self.nextSubLevels = nil
    self.currentLoadingUIName = nil
    self.configCache = {}
	self.bHavePreprocess = false		--是否打开了预处理Loading UI
	self.preprocessLoadingID = nil		--预打开的LoadingID
	self.postprocessLoadingID = nil		--需保留的LoadingID
	self.bNeedPostprocess = false		--level/plant切换完成后是否需要保留UI等待后处理
	self.bFakeLoading = false			--是否正在假加载流程
	-- 临时百分比
	self.StagePercentageMap = {
		[EWorldManageStage.WORLD_NOT_READY_TO_PLAY] = 0,
		[EWorldManageStage.LOADING_MAP] = 40,
		[EWorldManageStage.LOADING_PRELOAD_RESOURCE] = 50,
		[EWorldManageStage.LOADING_MAINPLAYER] = 80,
		[EWorldManageStage.LOADING_LOCAL_ENTITIES] = 85,
		[EWorldManageStage.LOADING_AOI_NET_ENTITIES] = 90,
		[EWorldManageStage.LOADING_CINEMATIC] = 99,
		[EWorldManageStage.WORLD_BEGIN_PLAY] = 100,
	}
end

function LoadingSystem:onUnInit()
    if self.loadingAudioID ~= 0 then
        Game.AkAudioManager:StopEvent(self.loadingAudioID)
        self.loadingAudioID = 0
    end

    self.nextLevel = nil
    self.nextSubLevels = nil
    self.configCache = nil
    self.currentLoadingUIName = nil
end

function LoadingSystem:IsLoadingUIShow()
	return Game.NewUIManager:IsShow(self.currentLoadingUIName)
end

function LoadingSystem:GetLoadingConfig(ConfigID)
    if self.configCache[ConfigID] ~= nil then
        return self.configCache[ConfigID]
    end

    local LoadingConfig = Game.TableData.GetLoadingPresetDataRow(ConfigID)

    if LoadingConfig == nil then
        LoadingConfig = Game.TableData.GetLoadingPresetDataRow(1)
    end

    if LoadingConfig then
        local ConfigData = {}
        ConfigData.LoadingImgSet = {}
        ConfigData.LoadingTips = {}
        ConfigData.LoadingMedia = LoadingConfig.LoadingMedia
        ConfigData.LoadingMediaList = LoadingConfig.LoadingMediaList
        ConfigData.ID = LoadingConfig.ID
        ConfigData.LoadingUI = LoadingConfig.LoadingUI
		ConfigData.LoadingTimeLeast = LoadingConfig.LoadingTimeLeast
		ConfigData.LoadingUIParam = LoadingConfig.LoadingUIParam
		ConfigData.NeedPreProcess = LoadingConfig.NeedPreProcess
		ConfigData.NeedPostProcess = LoadingConfig.NeedPostProcess
        for _, V in ksbcpairs(LoadingConfig.LoadingImgSet) do
            table.insert(ConfigData.LoadingImgSet, Game.TableData.GetLoadingImgsDataRow(V))
        end

        for _, V in ksbcpairs(LoadingConfig.LoadingTips) do
            table.insert(ConfigData.LoadingTips, Game.TableData.GetLoadingTipsDataRow(V))
        end

        self.configCache[ConfigID] = ConfigData
        return ConfigData
    end

    return nil
end

---StartPreprocessLoading 打开预表现Loading
---@param loadingID number
---@param planeID number 位面id
function LoadingSystem:StartPreprocessLoading(loadingID, planeID)
    if self.bHavePreprocess then
		Log.Warning("[StartPreprocessLoading] already have preprocess", self.preprocessLoadingID)
        return
    end
	local loadingConfig = self:GetLoadingConfig(loadingID)
	if not loadingConfig then
		Log.ErrorFormat("[StartPreprocessLoading] get loading config for %s failed", loadingID)
		return
	end
	self.preprocessLoadingID = loadingID
	self.bHavePreprocess = true
	self:showLoadingUI(loadingConfig, nil, planeID)
end

---StopPostprocessLoading 关闭预表现Loading
function LoadingSystem:StopPostprocessLoading()
	if self.preprocessLoadingID or (self.postprocessLoadingID == self.loadingPresetID)then
		self:closeLoadingUI()
	end
end

---HavePreprocessLoading 是否有预表现Loading
function LoadingSystem:HavePreprocessLoading()
	return self.bHavePreprocess
end

function LoadingSystem:StartLoadingScreen(NextLevelConfig, LoadingPresetOverride, PlaneID, bFakeLoading)
    Log.Debug("LevelManager StartLoading Screen")
	if NextLevelConfig == nil or not NextLevelConfig.bUseLoadingScreen then
		self:closeLoadingUI()
		return
	end

	if not self.bHavePreprocess and not self.bFakeLoading then
		self:closeLoadingUI()
	end
	
	local loadingPresetID = (LoadingPresetOverride == nil or LoadingPresetOverride == 0) and NextLevelConfig.LoadingPresetID or LoadingPresetOverride
	if self.loadingPresetID ~= loadingPresetID then
		self:closeLoadingUI()
	end
	self.loadingPresetID = loadingPresetID
	
    self.nextLevel = NextLevelConfig

	local loadingConfig = self:GetLoadingConfig(LoadingPresetOverride or NextLevelConfig.LoadingPresetID)
	if not loadingConfig then
		Log.ErrorFormat("[StartLoadingScreen] get loading config for %s failed", LoadingPresetOverride or NextLevelConfig.LoadingPresetID)
		return
	end

	if not self.bFakeLoading then
		self:playLoadingAudio()
	end
	
	self.bFakeLoading = bFakeLoading or false
	self:showLoadingUI(loadingConfig, NextLevelConfig, PlaneID)
	return loadingConfig.ID, loadingConfig.LoadingTimeLeast
end

function LoadingSystem:showLoadingUI(loadingConfig, NextLevelConfig, PlaneID)
	self.loadingPresetID = loadingConfig.ID
	self.bNeedPostprocess = loadingConfig.NeedPostProcess
	if self.bNeedPostprocess then
		self.postprocessLoadingID = loadingConfig.ID
	end
	self.currentLoadingUIName = loadingConfig.LoadingUI or UIPanelConfig.Loading_Panel
	if Game.NewUIManager:IsShow(self.currentLoadingUIName)then
		-- 触发界面刷新逻辑但不播放打开动画
		Game.GlobalEventSystem:Publish(EEventTypesV2.LOADING_ON_REAL_LOADING_START, loadingConfig, NextLevelConfig, PlaneID)
	else
		Game.NewUIManager:OpenPanel(self.currentLoadingUIName, loadingConfig, NextLevelConfig, PlaneID)
	end
end

function LoadingSystem:NotifyLevelLoaded(CurrentLevel)
    if self.nextLevel and CurrentLevel and self.nextLevel.ID == CurrentLevel.ID then
        Log.Debug("loading level complete", self.nextLevel.AssetName)
        Game.GlobalEventSystem:Publish(EEventTypesV2.LOADING_ON_LOADING_COMPLETE, self.nextLevel)
    end

    self:playLoadedAudio()
	if not self.bNeedPostprocess or self.postprocessLoadingID ~= self.loadingPresetID then
		self.loadingPresetID = 0
		self:closeLoadingUI()
	end
    self.nextLevel = nil
    self.nextSubLevels = nil
end

function LoadingSystem:GetPreProcessLoadingID()
    return self.preprocessLoadingID
end

function LoadingSystem:closeLoadingUI()
	if self.currentLoadingUIName then
		LuaGC() --在每次关闭Loading界面前调用下GC，可能是过图，也可能是传送 <EMAIL>
		Game.NewUIManager:ClosePanel(self.currentLoadingUIName)
		self.currentLoadingUIName = nil
	end
	self.loadingPresetID = 0
	self.preprocessLoadingID = nil
	self.postprocessLoadingID = nil
	self.bHavePreprocess = false
	self.bNeedPostprocess = false
	self.bFakeLoading = false
end

function LoadingSystem:IsFakeLoading()
	return self.bFakeLoading == true
end

function LoadingSystem:GetLoadingPercentage()
    local curState = Game.WorldManager:GetCurState()
	return self.StagePercentageMap[curState]
end

---@private loading时播放音频
function LoadingSystem:playLoadingAudio()
    if self.loadingAudioID ~= 0 then
        Game.AkAudioManager:StopEvent(self.loadingAudioID)
        self.loadingAudioID = 0
    end

    local loadingConfig = Game.TableData.GetLoadingPresetDataRow(self.loadingPresetID)
	if not loadingConfig then
		if self.loadingPresetID ~= 0 then
			Log.ErrorFormat("[playLoadingAudio] get loading config for %s failed", self.loadingPresetID)
		end
		return
	end
	if loadingConfig.LoadingSound ~= "" then
        self.loadingAudioID = Game.AkAudioManager:PostEvent2D(loadingConfig.LoadingSound, true)
    end
end

---@private load结束后播放音频
function LoadingSystem:playLoadedAudio()
    local loadingConfig = Game.TableData.GetLoadingPresetDataRow(self.loadingPresetID)
    if not loadingConfig then
		if self.loadingPresetID ~= 0 then
			Log.ErrorFormat("[playLoadedAudio] get loading config for %s failed", self.loadingPresetID)
		end
        if self.loadingAudioID ~= 0 then
            Game.AkAudioManager:StopEvent(self.loadingAudioID)
            self.loadingAudioID = 0
        end
        return
    end

    if loadingConfig.LoadedSound ~= "" then
        Game.AkAudioManager:PostEvent2D(loadingConfig.LoadedSound, true)
    end

    self.loadingAudioID = 0
end

---------------- loading预处理 -----------------
---获取位面预加载loadingID
---@param planeID number  位面ID
function LoadingSystem:GetPlanePreLoadingID(planeID)
    local planeData = Game.SceneUtils.GetPlaneConfProxy(planeID)
    local loadingID = planeData and planeData.LoadingID
    local loadingData = Game.TableData.GetLoadingPresetDataRow(loadingID)
    if loadingData and loadingData.NeedPreProcess then
        return loadingID
    end
end
---------------- loading预处理 End -----------------

return LoadingSystem
