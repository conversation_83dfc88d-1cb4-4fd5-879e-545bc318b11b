local ChatSet_SocialHead = kg_require("Gameplay.LogicSystem.InterfaceAppearance.ChatSet.ChatSet_Head.ChatSet_SocialHead")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatSet_HeadTop : UIComponent
---@field view ChatSet_HeadTopBlueprint
local ChatSet_HeadTop = DefineClass("ChatSet_HeadTop", UIComponent)

ChatSet_HeadTop.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatSet_HeadTop:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatSet_HeadTop:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatSet_HeadTop:InitUIComponent()
    ---@type ChatSet_SocialHead
    self.WBP_ChatSet_SocialHeadCom = self:CreateComponent(self.view.WBP_ChatSet_SocialHead, ChatSet_SocialHead)
end

---UI事件在这里注册，此处为自动生成
function ChatSet_HeadTop:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatSet_HeadTop:InitUIView()
end

---组件刷新统一入口
function ChatSet_HeadTop:Refresh(title, desc, headRes, frameRes, bIsDefaultHeadFrame, bIsDynamicFrame)
    if not headRes or headRes == "" then
        headRes = Game.FashionSystem:GetPlayerHeadRes(Game.me.portrait, Game.me.Profession)
    end
    self.view.TextTitle:SetText(title or "")
    self.view.TextDesc:SetText(desc or "")
    self.WBP_ChatSet_SocialHeadCom:Refresh(headRes, frameRes, bIsDefaultHeadFrame, bIsDynamicFrame)
end

return ChatSet_HeadTop
