local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatSet_SocialHead : UIComponent
---@field view ChatSet_SocialHeadBlueprint
local ChatSet_SocialHead = DefineClass("ChatSet_SocialHead", UIComponent)

ChatSet_SocialHead.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatSet_SocialHead:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatSet_SocialHead:InitUIData()
    self.loadedComponent = nil
end

--- UI组件初始化，此处为自动生成
function ChatSet_SocialHead:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ChatSet_SocialHead:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatSet_SocialHead:InitUIView()
end

---组件刷新统一入口
function ChatSet_SocialHead:Refresh(headRes, frameRes, bIsDefaultHeadFrame, bIsDynamicFrame)
    if headRes then
        self:SetHead(headRes)
    end
    if frameRes and not bIsDefaultHeadFrame then
        if not bIsDynamicFrame then
            self:SetFrame(frameRes)
        else
            if self.loadedComponent then
                self:RemoveComponent(self.loadedComponent)
                self.loadedComponent = nil
            end
            UICellConfig.CellConfig[UICellConfig.UIAppearance_AvatarFrame].res = frameRes
            self:AsyncLoadComponent(UICellConfig.UIAppearance_AvatarFrame, self.view.FrameDynamic, 
                function(component) self.loadedComponent = component end)
        end
        self:SetDynamic(bIsDynamicFrame)
        self:SetHasFrame(true)
    else
        self:SetHasFrame(false)
    end
end

function ChatSet_SocialHead:SetDynamic(bIsDynamic)
    self.userWidget:BP_SetDynamic(bIsDynamic or false)
end

function ChatSet_SocialHead:SetHasFrame(hasFrame)
    self.userWidget:BP_SetFrame(hasFrame)
end

function ChatSet_SocialHead:SetFrame(resPath)
    self:SetImage(self.view.Img_HeadFrame, resPath)
end

function ChatSet_SocialHead:SetHead(resPath)
    self:SetImage(self.view.Img_HeadIcon, resPath)
end

return ChatSet_SocialHead
