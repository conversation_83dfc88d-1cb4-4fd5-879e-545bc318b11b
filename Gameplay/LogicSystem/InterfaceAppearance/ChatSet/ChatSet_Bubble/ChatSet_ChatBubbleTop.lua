local ChatSet_SocialHead = kg_require("Gameplay.LogicSystem.InterfaceAppearance.ChatSet.ChatSet_Head.ChatSet_SocialHead")
local ChatSet_ChatBubble = kg_require("Gameplay.LogicSystem.InterfaceAppearance.ChatSet.ChatSet_Bubble.ChatSet_ChatBubble")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ChatSet_ChatBubbleTop : UIComponent
---@field view ChatSet_ChatBubbleTopBlueprint
local ChatSet_ChatBubbleTop = DefineClass("ChatSet_ChatBubbleTop", UIComponent)

ChatSet_ChatBubbleTop.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatSet_ChatBubbleTop:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatSet_ChatBubbleTop:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatSet_ChatBubbleTop:InitUIComponent()
    ---@type ChatSet_SocialHead
    self.WBP_ChatSet_SocialHeadCom = self:CreateComponent(self.view.WBP_ChatSet_SocialHead, ChatSet_SocialHead)
    ---@type ChatSet_ChatBubble
    self.WBP_ChatSet_ChatBubbleCom = self:CreateComponent(self.view.WBP_ChatSet_ChatBubble, ChatSet_ChatBubble)
end

---UI事件在这里注册，此处为自动生成
function ChatSet_ChatBubbleTop:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatSet_ChatBubbleTop:InitUIView()
end

---组件刷新统一入口
function ChatSet_ChatBubbleTop:Refresh(title, desc, bubbleRes)
    self:InitDefaultHead()
    self.view.TextTitle:SetText(title or "")
    self.view.TextDesc:SetText(desc or "")
    self.WBP_ChatSet_ChatBubbleCom:Refresh(bubbleRes)
end

function ChatSet_ChatBubbleTop:InitDefaultHead()
    local headRes = Game.FashionSystem:GetPlayerHeadRes(Game.me.portrait, Game.me.Profession)
    local frameRes, bIsDynamic = Game.FashionSystem:GetPlayerHeadFrameRes(Game.me.portraitFrame)
    self.WBP_ChatSet_SocialHeadCom:Refresh(headRes, frameRes, frameRes == nil, bIsDynamic)
end

return ChatSet_ChatBubbleTop
