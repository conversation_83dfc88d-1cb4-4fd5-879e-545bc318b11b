local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Photograph_MovementPage : UIComponent
---@field view Photograph_Movement_PageBlueprint
local Photograph_MovementPage = DefineClass("Photograph_MovementPage", UIComponent)

--播放状态枚举
Photograph_MovementPage.EPlayState = {
	Stop = 1,	--停止
	Playing = 2,	--播放中
	Pause = 3, 		--暂停
	PrepareStopPlay = 4,	--播放中，但是播放完后准备停止
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Photograph_MovementPage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Photograph_MovementPage:InitUIData()
	self.playSpeed = 1					--播放速率
	self.bLoop = false					--是否循环播放
	self.playState = Photograph_MovementPage.EPlayState.Stop	--当前播放状态
	self.curConfigData = nil			--当前选中的配置
	self.movementPlaySpeedList = {}		--播放速率列表
	self.startPlayTime = 0			--开始播放的时间
	self.pausePlayTime = 0			--暂停播放的时间
	self.curCutSceneMaxTime = 0			--当前播放的曲线资源时长
	for i, v in ksbcpairs(Game.TableData.GetPhotographConstDataRow("CAMERA_MOVE_PLAY_SPEED")) do
		self.movementPlaySpeedList[#self.movementPlaySpeedList + 1] = {name = tostring(v), speed = v}
	end
	self.movementConfigList = {}		--运镜配置ID列表
	for i, v in ksbcpairs(Game.TableData.GetPhotographMovementDataTable()) do
		self.movementConfigList[#self.movementConfigList + 1] = v
	end
	table.sort(self.movementConfigList, function(a, b) 
		return a.ID < b.ID
	end)
end

--- UI组件初始化，此处为自动生成
function Photograph_MovementPage:InitUIComponent()
    ---@type UIListView childScript: Photograph_Movement_Item
    self.MovementConfigTileCom = self:CreateComponent(self.view.MovementConfigTile, UIListView)
    ---@type UIComDropDown
    self.WBP_PlayControl_WBP_Camera_DropCom = self:CreateComponent(self.view.WBP_PlayControl.WBP_Camera_Drop, UIComDropDown)
    ---@type UIComNumberSlider
    self.WBP_PlayControl_WBP_CameraSliderCom = self:CreateComponent(self.view.WBP_PlayControl.WBP_CameraSlider, UIComNumberSlider)
end

---UI事件在这里注册，此处为自动生成
function Photograph_MovementPage:InitUIEvent()
    self:AddUIEvent(self.MovementConfigTileCom.onItemClicked, "on_MovementConfigTileCom_ItemClicked")
    self:AddUIEvent(self.view.WBP_PlayControl.Btn_ClickArea_Cycle.OnClicked, "on_WBP_PlayControlBtn_ClickArea_Cycle_Clicked")
    self:AddUIEvent(self.view.WBP_PlayControl.Btn_ClickArea_Play.OnClicked, "on_WBP_PlayControlBtn_ClickArea_Play_Clicked")
    self:AddUIEvent(self.WBP_PlayControl_WBP_Camera_DropCom.onItemSelected, "on_WBP_PlayControl_WBP_Camera_DropCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Photograph_MovementPage:InitUIView()
	self.WBP_PlayControl_WBP_Camera_DropCom:Refresh(self.movementPlaySpeedList, 2, true)
	self.MovementConfigTileCom:Refresh(self.movementConfigList)
	if self.bLoop then
		self:PlayAnimation(self.view.WBP_PlayControl.Ani_CycleBtin_loop, nil, self.view.WBP_PlayControl, 0, 0, nil, nil, true)
	end
	self.WBP_PlayControl_WBP_CameraSliderCom:Refresh(0, 0, 100, nil, false)
end

---组件刷新统一入口
function Photograph_MovementPage:Refresh()
	self.playState = Photograph_MovementPage.EPlayState.Stop
	self.WBP_PlayControl_WBP_CameraSliderCom:SetValue(0)
	self.view.WBP_PlayControl:BP_SetPlay(false)
end

---@private playCameraMovement 播放相机运镜
function Photograph_MovementPage:playCameraMovement()
	self.playState = Photograph_MovementPage.EPlayState.Playing
	Game.CinematicManager:StartPlayCinematic({
		CinematicType = Enum.CinematicType.Cutscene,
		AssetID = self.curConfigData.CutSceneID,
		OnCinematicPlay = function()
			self:onCinematicPlay()
		end,
		OnCinematicPause = function()
			self:onCinematicPause()
		end
	})
end

function Photograph_MovementPage:onCinematicPlay()
	self.startPlayTime = os.gameTimeMS
	self.curCutSceneMaxTime = Game.CinematicManager.CutsceneManager:GetTotalPlayTime() * 1000
	self.WBP_PlayControl_WBP_CameraSliderCom:Refresh(0, 0, self.curCutSceneMaxTime, nil, false)
	self.WBP_PlayControl_WBP_CameraSliderCom:SetValue(0)
	self.view.WBP_PlayControl:BP_SetPlay(true)
	self:StartTickTimerBindIns("UpdateMovementProgressTimer", "updateMovementProgress", -1)
end

function Photograph_MovementPage:onCinematicPause()
	if self.playState == Photograph_MovementPage.EPlayState.Playing then
		if self.bLoop then
			self:playCameraMovement()
		else
			self:stopCameraMovement()
		end
	end
end

function Photograph_MovementPage:updateMovementProgress()
	if self.playState == Photograph_MovementPage.EPlayState.Pause then
		return
	end
	local diff = (os.gameTimeMS - self.startPlayTime) * self.playSpeed
	self.WBP_PlayControl_WBP_CameraSliderCom:SetValue(diff)
end

---@private stopCameraMovement 停止播放相机运镜
function Photograph_MovementPage:stopCameraMovement()
	self.curConfigData = nil
	self.playState = Photograph_MovementPage.EPlayState.Stop
	self.MovementConfigTileCom:ClearSelection()
	self.view.WBP_PlayControl:BP_SetPlay(false)
	self.WBP_PlayControl_WBP_CameraSliderCom:SetValue(0)
	self:StopTimer("UpdateMovementProgressTimer")
	Game.CinematicManager:StopPlayCinematic({CinematicType = Enum.CinematicType.Cutscene})
end

--- on_MovementConfigTileCom_ItemClicked 运镜配置item选中
---@param index number
---@param selected boolean
function Photograph_MovementPage:on_MovementConfigTileCom_ItemClicked(index, data)
	local configData = self.movementConfigList[index]
	if self.playState == Photograph_MovementPage.EPlayState.Stop or configData ~= self.curConfigData then
		self.curConfigData = configData
		self:playCameraMovement()
	elseif self.playState == Photograph_MovementPage.EPlayState.Playing then
		self.playState = Photograph_MovementPage.EPlayState.PrepareStopPlay
	end
end

--- on_WBP_PlayControl_WBP_Camera_DropCom_ItemSelected 播放速度下拉列表选中 
---@param index number
---@param data UITabData
function Photograph_MovementPage:on_WBP_PlayControl_WBP_Camera_DropCom_ItemSelected(index, data)
	self.playSpeed = self.movementPlaySpeedList[index].speed
end

--- 此处为自动生成
function Photograph_MovementPage:on_WBP_PlayControlBtn_ClickArea_Cycle_Clicked()
	self.bLoop = not self.bLoop
	if self.bLoop then
		self:PlayAnimation(self.view.WBP_PlayControl.Ani_CycleBtin_loop, nil, self.view.WBP_PlayControl, 0, 0, nil, nil, true)
	else
		self:StopAnimation(self.view.WBP_PlayControl.Ani_CycleBtin_loop, self.view.WBP_PlayControl)
	end
end

--- 此处为自动生成
function Photograph_MovementPage:on_WBP_PlayControlBtn_ClickArea_Play_Clicked()
	if self.playState == Photograph_MovementPage.EPlayState.Playing then	--播放中则暂停播放
		self.pausePlayTime = os.gameTimeMS
		self.playState = Photograph_MovementPage.EPlayState.Pause
		self.view.WBP_PlayControl:BP_SetPlay(false)
	elseif self.playState == Photograph_MovementPage.EPlayState.Pause then	--暂停播放则恢复播放
		self.playState = Photograph_MovementPage.EPlayState.Playing
		self.startPlayTime = self.startPlayTime + os.gameTimeMS - self.pausePlayTime
		self.pausePlayTime = 0
		self.view.WBP_PlayControl:BP_SetPlay(true)
	elseif self.playState == Photograph_MovementPage.EPlayState.PrepareStopPlay then --如果是准备停止，则立即停止
		self:stopCameraMovement()
	elseif self.playState == Photograph_MovementPage.EPlayState.Stop and self.curConfigData then	--如果不在播放且当前选中id不为空，则重新开始播放
		self:playCameraMovement()
	end
end

function Photograph_MovementPage:OnClose()
	self:stopCameraMovement()
end

return Photograph_MovementPage
