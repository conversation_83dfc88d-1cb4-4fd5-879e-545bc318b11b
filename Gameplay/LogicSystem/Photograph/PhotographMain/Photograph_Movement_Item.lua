local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Photograph_Movement_Item : UIListItem
---@field view Photograph_Movement_ItemBlueprint
local Photograph_Movement_Item = DefineClass("Photograph_Movement_Item", UIListItem)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Photograph_Movement_Item:OnCreate()
    self:InitUIData()
end

---初始化数据
function Photograph_Movement_Item:InitUIData()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Photograph_Movement_Item:InitUIView()
end

---面板打开的时候触发
function Photograph_Movement_Item:OnRefresh(config)
	self:SetImage(self.view.Img_Icon, config.Icon)
	self.view.Text_Title:SetText(config.ShowName)
end

return Photograph_Movement_Item
