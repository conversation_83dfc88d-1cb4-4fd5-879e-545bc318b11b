local KismetRenderingLibrary = import("KismetRenderingLibrary")
local LowLevelFunctions = import("LowLevelFunctions")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIComInputBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComInputBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PhotographTemplateSave_Panel : UIPanel
---@field view PhotographTemplateSave_PanelBlueprint
local PhotographTemplateSave_Panel = DefineClass("PhotographTemplateSave_Panel", UIPanel)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PhotographTemplateSave_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PhotographTemplateSave_Panel:InitUIData()
	self.rt = nil	--封面RT
	self.bCreate = false	--是否是创建模板
	self.templateData = nil	--模板数据
end

--- UI组件初始化，此处为自动生成
function PhotographTemplateSave_Panel:InitUIComponent()
    ---@type UIComBoxFrame
    self.WBP_ComPopupSCom = self:CreateComponent(self.view.WBP_ComPopupS, UIComBoxFrame)
    ---@type UIComInputBox
    self.WBP_ComInputCom = self:CreateComponent(self.view.WBP_ComInput, UIComInputBox)
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function PhotographTemplateSave_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_Delete.OnClicked, "on_Btn_Delete_Clicked")
    self:AddUIEvent(self.WBP_ComInputCom.onTextChanged, "on_WBP_ComInputCom_TextChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PhotographTemplateSave_Panel:InitUIView()
	self.WBP_ComBtnCom:Refresh(StringConst.Get("PHOTOGRAPH_TEMPLATE_SAVE"))
end

---面板打开的时候触发
function PhotographTemplateSave_Panel:OnRefresh(bCreate, rt, templateData)
	self.bCreate = bCreate
	self.templateData = templateData
	self.userWidget:BP_SetCreate(bCreate)
	self.WBP_ComPopupSCom:Refresh(bCreate and StringConst.Get("PHOTOGRAPH_TEMPLATE_SAVE_TEMPLATE") or StringConst.Get("PHOTOGRAPH_TEMPLATE_MANAGER"))
	self.WBP_ComBtnCom:SetDisable(bCreate)
	if bCreate and IsValid_L(rt) then
		self.rt = rt
		local dynamicMaterial = self.view.Image_Photo:GetDynamicMaterial()
		dynamicMaterial:SetTextureParameterValue('PhotoImage', rt)
	elseif not bCreate then
		local texture = KismetRenderingLibrary.ImportFileAsTexture2D(_G.GetContextObject(), templateData.CoverPath)
		self.view.Image_Photo:SetBrushFromTexture(texture, false)
		self.WBP_ComInputCom:SetText(templateData.Name)
	end
end

--- 此处为自动生成
function PhotographTemplateSave_Panel:on_WBP_ComBtnCom_ClickEvent()
	local templateName = self.WBP_ComInputCom:GetText() 
	if string.isEmpty(templateName) then
		return
	end
	if self.bCreate then
		local coverName = self.templateData.ID .. ".png"
		self.templateData.CoverPath = Game.PhotographSystem:GetTemplateFullPath() .. "/" .. coverName 
		Game.ScreenShotUtil:SaveTextureToPath(self.rt, Game.PhotographSystem:GetTemplatePath(), coverName)
	end
	self.templateData.Name = templateName
	
	Game.PhotographSystem:SaveTemplate(self.bCreate, self.templateData)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PHOTOGRAPH_TEMPLATE_UPDATE)
	self:CloseSelf()
end

--- 此处为自动生成
function PhotographTemplateSave_Panel:on_Btn_Delete_Clicked()
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.PHOTOGRAPH_DELETE_TEMPLATE, function()
		Game.PhotographSystem:DeleteTemplate(self.templateData.ID)
		Game.GlobalEventSystem:Publish(EEventTypesV2.PHOTOGRAPH_TEMPLATE_UPDATE)
		self:CloseSelf()
	end)
end

--- 此处为自动生成
---@param text string
function PhotographTemplateSave_Panel:on_WBP_ComInputCom_TextChanged(text)
	self.WBP_ComBtnCom:SetDisable(string.isEmpty(text))
end

return PhotographTemplateSave_Panel
