local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Photograph_DisplayPage : UIComponent
---@field view Photograph_DisplayPageBlueprint
local Photograph_DisplayPage = DefineClass("Photograph_DisplayPage", UIComponent)

Photograph_DisplayPage.eventBindMap = {
	[_G.EEventTypes.PHOTOGRAPH_RESTORE]= "PhotographRestore"
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Photograph_DisplayPage:OnCreate()
    self:InitUIData() 
    self:InitUIComponent()
    self:InitUIView()
	Game.PhotographSystem:InitDisplayConfigs()
end

---初始化数据
function Photograph_DisplayPage:InitUIData()
	self.displayConfigs = {}
	for _, v in ksbcpairs(Game.TableData.GetPhotographDisplayDataTable()) do
		self.displayConfigs[#self.displayConfigs + 1] = v
	end
	table.sort(self.displayConfigs, function(a, b) 
		return a.ID < b.ID
	end)
end

--- UI组件初始化，此处为自动生成
function Photograph_DisplayPage:InitUIComponent()
    ---@type UIListView childScript: Photograph_Display_Item
    self.DisplayListViewCom = self:CreateComponent(self.view.DisplayListView, UIListView)
end

---组件刷新统一入口
function Photograph_DisplayPage:Refresh()
	self.DisplayListViewCom:Refresh(self.displayConfigs)
end

---RestoreConfig 恢复到默认状态
function Photograph_DisplayPage:PhotographRestore()
	self.DisplayListViewCom:Refresh(self.displayConfigs)
end

return Photograph_DisplayPage
