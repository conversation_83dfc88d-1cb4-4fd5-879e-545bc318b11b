local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local ESlateVisibility = import("ESlateVisibility")
---@class PhotographSave_UserInfo : UIComponent
---@field view PhotographSave_UserInfoBlueprint
local PhotographSave_UserInfo = DefineClass("PhotographSave_UserInfo", UIComponent)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PhotographSave_UserInfo:OnCreate()
	self:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PhotographSave_UserInfo:InitUIComponent()
    ---@type SocialHead
    self.WBP_SocialHeadCom = self:CreateComponent(self.view.WBP_SocialHead, SocialHead)
end

---组件刷新统一入口
function PhotographSave_UserInfo:Refresh()
	self.view.Text_Name:SetText(Game.me.Name)
	if Game.GuildSystem:HasJoinGuild(Game.me) then
		self.view.Text_Guild:SetVisibility(ESlateVisibility.HitTestInvisible)
		self.view.Text_Guild:SetText(Game.me.guildName)
	else
		self.view.Text_Guild:SetVisibility(ESlateVisibility.Collapsed)
	end
	self.WBP_SocialHeadCom:Refresh({ProfessionID = Game.me.Profession, Level = Game.me.Level})
end


return PhotographSave_UserInfo
