local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local KismetInputLibrary = import("KismetInputLibrary")
local KismetMathLibrary = import("KismetMathLibrary")
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")
local ESlateVisibility = import("ESlateVisibility")

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PhotographEdit_Sticker : UIComponent
---@field view PhotographEdit_StickerBlueprint
local PhotographEdit_Sticker = DefineClass("PhotographEdit_Sticker", UIComponent)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PhotographEdit_Sticker:OnCreate()
    self:InitUIData()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PhotographEdit_Sticker:InitUIData()
	self.bEditorMode = false 	--编辑模式
	self.stickerId = nil		--贴纸id
	self.sourceDirection = nil	--初始方向（旋转用）
	self.sourceAngle = nil		--初始角度（旋转用）
	self.sourcePosition = nil	--初始位置（缩放用）
	self.sourceSize = nil		--初始大小（缩放用）
	self.panelHalfSize = nil	--父面板大小
	self.ZOrder = 0			--层级
end

---UI事件在这里注册，此处为自动生成
function PhotographEdit_Sticker:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Move.OnTouchStartedEvent, "on_Btn_Move_TouchStartedEvent")
    self:AddUIEvent(self.view.Btn_Move.OnTouchMovedEvent, "on_Btn_Move_TouchMovedEvent")
    self:AddUIEvent(self.view.Btn_Move.OnTouchEndedEvent, "on_Btn_Move_TouchEndedEvent")
    self:AddUIEvent(self.view.Btn_Close.Btn_ClickArea.OnClicked, "on_Btn_CloseBtn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Btn_Enlarge.Btn_ClickArea.OnTouchStartedEvent, "on_Btn_EnlargeBtn_ClickArea_TouchStartedEvent")
    self:AddUIEvent(self.view.Btn_Enlarge.Btn_ClickArea.OnTouchMovedEvent, "on_Btn_EnlargeBtn_ClickArea_TouchMovedEvent")
    self:AddUIEvent(self.view.Btn_Enlarge.Btn_ClickArea.OnTouchEndedEvent, "on_Btn_EnlargeBtn_ClickArea_TouchEndedEvent")
    self:AddUIEvent(self.view.Btn_Rotate.Btn_ClickArea.OnTouchStartedEvent, "on_Btn_RotateBtn_ClickArea_TouchStartedEvent")
    self:AddUIEvent(self.view.Btn_Rotate.Btn_ClickArea.OnTouchMovedEvent, "on_Btn_RotateBtn_ClickArea_TouchMovedEvent")
    self:AddUIEvent(self.view.Btn_Rotate.Btn_ClickArea.OnTouchEndedEvent, "on_Btn_RotateBtn_ClickArea_TouchEndedEvent")
    self:AddUIEvent(self.view.MoveBtn_Down.Btn_Click_Area.OnClicked, "on_MoveBtn_DownBtn_Click_Area_Clicked")
    self:AddUIEvent(self.view.MoveBtn_Up.Btn_Click_Area.OnClicked, "on_MoveBtn_UpBtn_Click_Area_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PhotographEdit_Sticker:InitUIView()
	self.view.Btn_Enlarge:BP_Type(0)
	self.view.Btn_Rotate:BP_Type(1)
	self.view.Btn_Close:BP_Type(2)
end

---组件刷新统一入口
---@param stickerData _PhotographEditorStickerDataRow
function PhotographEdit_Sticker:Refresh(stickerId, bEditor, transformInfo, ZOrder)
	self.bEditorMode = bEditor
	self.stickerId = stickerId
	self:setStickerImg(stickerId)
	self.userWidget:BP_SetIsEditor(bEditor)
	if transformInfo then
		self:RefreshTransformAndOrder(transformInfo.Angle, transformInfo.Position, transformInfo.Size)
	end
	self.ZOrder = ZOrder or self.ZOrder
	self.userWidget.Slot:SetZOrder(self.ZOrder)
end

function PhotographEdit_Sticker:setStickerImg(stickerId)
	local stickerData = Game.TableData.GetPhotographEditorStickerDataRow(stickerId)
	self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	self:AsyncLoadRes(stickerData.Sticker_Path, function(texture)
		self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if not IsValid_L(texture) then
			Log.DebugWarning("SetStickerImg failed, texture is null,", stickerData.Sticker_Path)
			return
		end
		self.view.Img_Sticker:SetBrushFromTexture(texture, true)
		if self.bEditorMode then
			local imageSize = self.view.Img_Sticker.Brush.ImageSize
			local size = FVector2D(imageSize.X, imageSize.Y)
			self.view.Canvas_Sticker.Slot:SetSize(size)
		end
	end)
end

function PhotographEdit_Sticker:RefreshTransformAndOrder(angle, position, size, ZOrder)
	self.view.Canvas_Sticker:SetRenderTransformAngle(angle)
	self.view.Canvas_Sticker.Slot:SetPosition(position)
	self.view.Canvas_Sticker.Slot:SetSize(size)
	if ZOrder and ZOrder ~= self.ZOrder then
		local bMoveUp = ZOrder > self.ZOrder
		self:updateZOrder(bMoveUp)
	end
end

function PhotographEdit_Sticker:GetTransformAndOrder()
	local angle = self.view.Canvas_Sticker:GetRenderTransformAngle()
	local position = self.view.Canvas_Sticker.Slot:GetPosition()
	local size = self.view.Canvas_Sticker.Slot:GetSize()
	return {Angle = angle, Position = position, Size = size}, self.ZOrder
end

--- 此处为自动生成
function PhotographEdit_Sticker:on_Btn_CloseBtn_ClickArea_Clicked()
	---@type PhotographSave_Photo
	local photographSave_Photo = self:GetParent()
	---@type PhotographEdit_Panel
	local photographEdit_Panel = photographSave_Photo:GetParent()
	photographEdit_Panel:RecordOperate(Enum.EPhotographEditorType.Sticker, Enum.EPhotographOperateType.Remove, self.stickerId, self)
	photographSave_Photo:RemoveSticker(self.stickerId, self, false)
end

--- 降低层级
function PhotographEdit_Sticker:on_MoveBtn_DownBtn_Click_Area_Clicked()
	self:activeChangeZOrder(false)
end

--- 提高层级
function PhotographEdit_Sticker:on_MoveBtn_UpBtn_Click_Area_Clicked()
	self:activeChangeZOrder(true)
end

function PhotographEdit_Sticker:activeChangeZOrder(bMoveUp)
	local tmpZOrder = self.ZOrder
	self:updateZOrder(bMoveUp)
	if tmpZOrder ~= self.ZOrder then
		self:recordStickInfo(tmpZOrder)
	end
end

function PhotographEdit_Sticker:updateZOrder(bMoveUp)
	---@type PhotographSave_Photo
	local photographSave_Photo = self:GetParent()
	photographSave_Photo:UpdateStickerZOrder(self, bMoveUp)
end

function PhotographEdit_Sticker:OnUpdateZOrder(ZOrder)
	self.ZOrder = ZOrder
	self.userWidget.Slot:SetZOrder(self.ZOrder)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_RotateBtn_ClickArea_TouchStartedEvent(myGeometry, inPointerEvent)
	self:recordStickInfo()
	local dialGeometry = self.view.Canvas_Sticker:GetCachedGeometry()
	local dialHalfSize = SlateBlueprintLibrary.GetLocalSize(dialGeometry) / 2
	self.centerPos = SlateBlueprintLibrary.LocalToAbsolute(dialGeometry, dialHalfSize)
	self.sourceDirection = self:getDirection(inPointerEvent)
	self.sourceAngle = self.view.Canvas_Sticker:GetRenderTransformAngle()
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_RotateBtn_ClickArea_TouchMovedEvent(myGeometry, inPointerEvent)
	local direction = self:getDirection(inPointerEvent)
	local angleRad = KismetMathLibrary.Acos(KismetMathLibrary.DotProduct2D(direction, self.sourceDirection))
	local crossProduct = KismetMathLibrary.CrossProduct2D(self.sourceDirection, direction)
	if crossProduct < 0 then
		angleRad  = -angleRad
	end
	local angle = KismetMathLibrary.RadiansToDegrees(angleRad) + self.sourceAngle
	self.view.Canvas_Sticker:SetRenderTransformAngle(angle)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_RotateBtn_ClickArea_TouchEndedEvent(myGeometry, inPointerEvent)
end

function PhotographEdit_Sticker:getDirection(inPointerEvent)
	local currentPointerPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
	local direction = currentPointerPos - self.centerPos
	direction:Normalize(1e-8)
	return direction
end


--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_EnlargeBtn_ClickArea_TouchStartedEvent(myGeometry, inPointerEvent)
	self:recordStickInfo()
	self.sourcePosition = SlateBlueprintLibrary.AbsoluteToLocal(self.userWidget:GetParent():GetCachedGeometry(), KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent))
	self.sourceSize = self.view.Canvas_Sticker.Slot:GetSize()
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_EnlargeBtn_ClickArea_TouchMovedEvent(myGeometry, inPointerEvent)
	local position = SlateBlueprintLibrary.AbsoluteToLocal(self.userWidget:GetParent():GetCachedGeometry(), KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent))
	local diff = (position - self.sourcePosition) /  WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
	diff.Y = -diff.Y
	self.view.Canvas_Sticker.Slot:SetSize(self.sourceSize + diff)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_EnlargeBtn_ClickArea_TouchEndedEvent(myGeometry, inPointerEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_Move_TouchStartedEvent(myGeometry, inPointerEvent)
	self:recordStickInfo()
	self.panelHalfSize = SlateBlueprintLibrary.GetLocalSize(self.userWidget:GetParent():GetCachedGeometry()) / 2
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_Move_TouchMovedEvent(myGeometry, inPointerEvent)
	local currentMousePos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
	currentMousePos = SlateBlueprintLibrary.AbsoluteToLocal(self.userWidget:GetParent():GetCachedGeometry(), currentMousePos) - self.panelHalfSize
	if currentMousePos.X < -self.panelHalfSize.X then
		currentMousePos.X = -self.panelHalfSize.X
	elseif currentMousePos.X > self.panelHalfSize.X then
		currentMousePos.X = self.panelHalfSize.X
	end
	if currentMousePos.Y < -self.panelHalfSize.Y then
		currentMousePos.Y = -self.panelHalfSize.Y
	elseif currentMousePos.Y > self.panelHalfSize.Y then
		currentMousePos.Y = self.panelHalfSize.Y
	end
	self.view.Canvas_Sticker.Slot:SetPosition(currentMousePos)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function PhotographEdit_Sticker:on_Btn_Move_TouchEndedEvent(myGeometry, inPointerEvent)
end

function PhotographEdit_Sticker:recordStickInfo(ZOrder)
	local angle = self.view.Canvas_Sticker:GetRenderTransformAngle()
	local position = self.view.Canvas_Sticker.Slot:GetPosition()
	local size = self.view.Canvas_Sticker.Slot:GetSize()
	self:GetBelongPanel():RecordOperate(Enum.EPhotographEditorType.Sticker, Enum.EPhotographOperateType.Editor, self.stickerId, self,
		angle, position, size, ZOrder or self.ZOrder)
end

return PhotographEdit_Sticker
