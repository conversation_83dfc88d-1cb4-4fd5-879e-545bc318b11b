local PhotographSave_Photo = kg_require("Gameplay.LogicSystem.Photograph.Edit.PhotographSave_Photo")
local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local PhotographSave_UserInfo = kg_require("Gameplay.LogicSystem.Photograph.Edit.PhotographSave_UserInfo")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
local UMGAsTextureHelper = import("UMGAsTextureHelper")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")
local EPropertyClass = import("EPropertyClass")
local LuaFunctionLibrary = import("LuaFunctionLibrary")

local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PhotographSave_Panel : UIPanel
---@field view PhotographSave_PanelBlueprint
local PhotographSave_Panel = DefineClass("PhotographSave_Panel", UIPanel)

PhotographSave_Panel.eventBindMap = {
	[EEventTypesV2.PHOTOGRAPH_EDITOR_UPDATE]= "RefreshPhotoEditor"
}

PhotographSave_Panel.EPlatformImg = {
	[Enum.SharePlatform.QQ_ZONE] = "",
	[Enum.SharePlatform.WEIBO] = "",
	[Enum.SharePlatform.WEIXIN] = "",
	[Enum.SharePlatform.WEIXIN_TIMELINE] = "",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PhotographSave_Panel:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

---初始化数据
function PhotographSave_Panel:InitUIData()
	self.bAlbumNumMax = false --相册是否已满
	self.photoSize = nil --照片的尺寸
	---@type Enum.SharePlatform[] 支持的分享平台列表
	self.platformList = nil	--分享平台列表
	self.bVideo = false	--是否是视频
	self.bLivePhoto = false	--是否是live图
	self.photoRT = nil	--当前图片rt
	self.savedFilePath = nil 	--图片保存路径
end

--- UI组件初始化，此处为自动生成
function PhotographSave_Panel:InitUIComponent()
    ---@type PhotographSave_Photo
    self.WBP_CameraSave_PhotoCom = self:CreateComponent(self.view.WBP_CameraSave_Photo, PhotographSave_Photo)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIComCheckBox
    self.WBP_ComCheckBox_QRCodeCom = self:CreateComponent(self.view.WBP_ComCheckBox_QRCode, UIComCheckBox)
    ---@type UIComCheckBox
    self.WBP_ComCheckBox_LogoCom = self:CreateComponent(self.view.WBP_ComCheckBox_Logo, UIComCheckBox)
    ---@type UIComCheckBox
    self.WBP_ComCheckBox_UserInfoCom = self:CreateComponent(self.view.WBP_ComCheckBox_UserInfo, UIComCheckBox)
    ---@type UIComButtonItem
    self.WBP_Photograph_Save_BtnCom = self:CreateComponent(self.view.WBP_Photograph_Save_Btn, UIComButtonItem)
    ---@type UIListView childScript: UIComButtonItem
    self.KGListView_ShareCom = self:CreateComponent(self.view.KGListView_Share, UIListView)
    ---@type PhotographSave_UserInfo
    self.WBP_CameraSave_UserInfoCom = self:CreateComponent(self.view.WBP_CameraSave_UserInfo, PhotographSave_UserInfo)
end

---UI事件在这里注册，此处为自动生成
function PhotographSave_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComCheckBox_UserInfoCom.onCheckChanged, "on_WBP_ComCheckBox_UserInfoCom_CheckChanged")
    self:AddUIEvent(self.WBP_ComCheckBox_LogoCom.onCheckChanged, "on_WBP_ComCheckBox_LogoCom_CheckChanged")
    self:AddUIEvent(self.WBP_ComCheckBox_QRCodeCom.onCheckChanged, "on_WBP_ComCheckBox_QRCodeCom_CheckChanged")
    self:AddUIEvent(self.KGListView_ShareCom.onItemClicked, "on_KGListView_ShareCom_ItemClicked")
    self:AddUIEvent(self.WBP_Photograph_Save_BtnCom.onClickEvent, "on_WBP_Photograph_Save_BtnCom_ClickEvent")
    self:AddUIEvent(self.view.WBP_CameraSave_Edit.Btn_ClickArea.OnClicked, "on_WBP_CameraSave_EditBtn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PhotographSave_Panel:InitUIView()
	self.WBP_ComCheckBox_UserInfoCom:Refresh(StringConst.Get("PHOTOGRAPH_SAVE_USERINFO"), Game.PhotographSystem.bShowUserInfo)
	self.WBP_ComCheckBox_LogoCom:Refresh(StringConst.Get("PHOTOGRAPH_SAVE_LOGO"), Game.PhotographSystem.bShowLogo)
	self.WBP_ComCheckBox_QRCodeCom:Refresh(StringConst.Get("PHOTOGRAPH_SAVE_QRCODE"), Game.PhotographSystem.bShowQRCode)
	self.view.WBP_ComCheckBox_QRCode:SetVisibility(ESlateVisibility.Collapsed)	--暂时不支持二维码，ui先屏蔽了
	self.view.Img_QRCode:SetVisibility(ESlateVisibility.Collapsed)	--暂时不支持二维码，ui先屏蔽了
	self.WBP_CameraSave_UserInfoCom:Refresh()
end

---面板打开的时候触发
function PhotographSave_Panel:OnRefresh(bVideoMode, ...)
	local pictureList = slua.Array(EPropertyClass.Str)
	LuaFunctionLibrary.FindFiles(Game.PhotographSystem:GetMysteriesPhotoAlbumFullPath() , pictureList)
	self.bAlbumNumMax = pictureList:Num() >= Game.TableData.GetPhotographConstDataRow("ALBUM_MAX")
	if self.bAlbumNumMax then
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.PHOTOGRAPH_ALBUM_SPACE_FULL)
	end
	self.bVideo = bVideoMode
	Game.AllInSdkManager:GetSupportPlatformList(function(platformList)
		if self.RefreshSharePlatform then
			self:RefreshSharePlatform(platformList)
		end
	end)
	self.userWidget:BP_IsVideo(bVideoMode)
	if bVideoMode then
		self.userWidget:BP_IsLive(false)
		self:setVideoInfo()
	else
		self:setPhotoInfo(...)
	end
end

---RefreshSharePlatform
---@param platformList Enum.SharePlatform[] 支持的分享平台列表
function PhotographSave_Panel:RefreshSharePlatform(platformList)
	self.platformList = platformList
	local tmpPlatformList = {}
	for i, v in pairs(platformList) do
		tmpPlatformList[#tmpPlatformList + 1] = {icon = PhotographSave_Panel.EPlatformImg[v]}
	end
	self.KGListView_ShareCom:Refresh(tmpPlatformList)
end

function PhotographSave_Panel:setVideoInfo()
	--todo 视频
end

function PhotographSave_Panel:setPhotoInfo(bLivePhoto, ...)
	self.bLivePhoto = bLivePhoto
	self.userWidget:BP_IsLive(bLivePhoto)
	if bLivePhoto then
		self:setLivePhoto(...)
	else
		self:setNormalPicture(...)
	end
end

function PhotographSave_Panel:setLivePhoto(videoPath)
	--todo live图
end

function PhotographSave_Panel:setNormalPicture(rt, sizeX, sizeY)
	self.WBP_CameraSave_PhotoCom:Refresh(rt, sizeX, sizeY, false)
	self.photoRT = rt
	if not self.bAlbumNumMax then
		Game.ScreenShotUtil:SaveTextureToPath(rt, Game.PhotographSystem:GetMysteriesPhotoAlbumPath(), tostring(os.time())..".png")
	end
	self.view.WBP_CameraSave_UserInfo:SetVisibility(Game.PhotographSystem.bShowUserInfo and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	self.view.Img_QRCode:SetVisibility(Game.PhotographSystem.bShowQRCode and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	self.view.Img_Logo:SetVisibility(Game.PhotographSystem.bShowLogo and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	self.photoSize = FVector2D(sizeX, sizeY)
end

function PhotographSave_Panel:RefreshPhotoEditor(frameID, stickerList)
	self.WBP_CameraSave_PhotoCom:RefreshFrameAndSticker(frameID, stickerList)
end

--- 此处为自动生成
function PhotographSave_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
	if not self.bAlbumNumMax then
		Game.GlobalEventSystem:Publish(EEventTypesV2.PHOTOGRAPH_SAVE_PANEL_CLOSE, self.photoRT)
	end
	self:CloseSelf()
end

--- 此处为自动生成
---@param checked bool
function PhotographSave_Panel:on_WBP_ComCheckBox_UserInfoCom_CheckChanged(checked)
	self.view.WBP_CameraSave_UserInfo:SetVisibility(checked and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	Game.PhotographSystem.bShowUserInfo = checked
end

--- 此处为自动生成
---@param checked bool
function PhotographSave_Panel:on_WBP_ComCheckBox_LogoCom_CheckChanged(checked)
	self.view.Img_Logo:SetVisibility(checked and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	Game.PhotographSystem.bShowLogo = checked
end

--- 此处为自动生成
---@param checked bool
function PhotographSave_Panel:on_WBP_ComCheckBox_QRCodeCom_CheckChanged(checked)
	self.view.Img_QRCode:SetVisibility(checked and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
	Game.PhotographSystem.bShowQRCode = checked
end


--- 此处为自动生成
---@param index number
---@param data table
function PhotographSave_Panel:on_KGListView_ShareCom_ItemClicked(index, data)
	self:AddWaterMark()
	local platform = self.platformList[index]
	local shareType = (self.bLivePhoto or self.bVideo) and Enum.ShareType.Video or Enum.ShareType.Image
	Game.AllInSdkManager:ShareToPlatform(platform, shareType, self.savedFilePath, "", "", 
		function()
			self:deleteTMPPicture()
			Log.Debug("share success")
		end,
		function()
			self:deleteTMPPicture()
			Log.Debug("share failed")
		end)
end

--- 此处为自动生成
function PhotographSave_Panel:on_WBP_Photograph_Save_BtnCom_ClickEvent()
	self:AddWaterMark()
	if not self.savedFilePath then
		Log.WarningFormat('[P_PhotographPicture:SharedLocalAlbum] SavedFilePath:%s, not exist', self.savedFilePath)
		return
	end
	if PlatformUtil.IsMobilePlatform() then
		Game.AllInSdkManager:SaveToPhotos(self.savedFilePath, 
			function(code,msg,data) 
				self:deleteTMPPicture()
				Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PHOTOGRAPH_SAVE_LOG, {{StringConst.Get("PHOTOGRAPH_ALBUM_TITLE")}})
			end,
			function() 
				self:deleteTMPPicture() 
			end)
	else
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PHOTOGRAPH_SAVE_LOG, {{self.savedFilePath}})
	end
end

function PhotographSave_Panel:AddWaterMark()
	if not self.photoSize then
		return
	end
	if not self.helper then
		self.helper = UMGAsTextureHelper(Game.WorldContext)
	end
	local tmpPhotoRT = Game.ScreenShotUtil:DuplicationRenderTexture(self.photoRT)
	self.WBP_CameraSave_PhotoCom:AddWaterMark(tmpPhotoRT, self.helper)
	local viewportScale = WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
	if Game.PhotographSystem.bShowLogo then
		local logoSize = SlateBlueprintLibrary.GetLocalSize(self.view.Img_Logo:GetCachedGeometry())
		local logoWidgetRT = self.helper:WidgetToTexture(self.view.Img_Logo, logoSize)
		logoSize = logoSize * viewportScale
		local position = self.view.Img_Logo:GetParent().Slot:GetPosition() * viewportScale
		local logoLoc = self.photoSize - logoSize + position
		Game.ScreenShotUtil:AddWaterMark(tmpPhotoRT, logoWidgetRT, logoLoc, logoSize, 0)
	end

	if Game.PhotographSystem.bShowUserInfo then
		local roleSize = SlateBlueprintLibrary.GetLocalSize(self.view.WBP_CameraSave_UserInfo:GetCachedGeometry())
		local roleWidgetRT = self.helper:WidgetToTexture(self.view.WBP_CameraSave_UserInfo, roleSize)
		roleSize = roleSize * viewportScale
		local position = self.view.WBP_CameraSave_UserInfo:GetParent():GetParent().Slot:GetPosition() * viewportScale
		position.X = position.X + 12 * viewportScale --12是水平框边距，先临时写死
		local roleLoc = FVector2D(position.X, self.photoSize.Y - roleSize.Y + position.Y)
		Game.ScreenShotUtil:AddWaterMark(tmpPhotoRT, roleWidgetRT, roleLoc, roleSize, 0)
	end
	self.savedFilePath = Game.ScreenShotUtil:SaveTextureToPath(tmpPhotoRT, Game.PhotographSystem:GetLocalPhotoAlbumPath(), tostring(os.time())..".png")
end

function PhotographSave_Photo:deleteTMPPicture()
	os.remove(self.savedFilePath)
	self.savedFilePath = nil
end

function PhotographSave_Panel:OnClose()
	self.photoRT = nil
	self.helper = nil
end

--- 此处为自动生成
function PhotographSave_Panel:on_WBP_CameraSave_EditBtn_ClickArea_Clicked()
	Game.NewUIManager:OpenPanel("PhotographEdit_Panel", self.photoRT, self.photoSize.X, self.photoSize.Y)
end

return PhotographSave_Panel
