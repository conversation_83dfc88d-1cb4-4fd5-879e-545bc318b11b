---@class FriendSystem:SystemBase
local FriendSystem = DefineClass("FriendSystem", SystemBase)
local const = kg_require("Shared.Const")
local bitset = kg_require("Shared.lualibs.bitset")

local Game = Game
local Enum = Enum

-- 临时处理标签
FriendSystem.E_TEMP_FRIEND_ADD_TAG = {
    NONE = 0,
    SAME_SEQUENCE_MASTER = 1,
    SAME_GUILD_FRIEND = 2,
    SINGLE_FEMALE = 3,
    POWERFUL_CARRY = 4,
    RELIABLE_ASSISTASNT = 5,
    INVINCIBLE_TANK = 6,
}

FriendSystem.EditType = {
    Invite = 2, ---邀请成员
    Remove = 1,  ---移除成员
    TeaRoomInvite = 3, -- 茶壶系统邀请成员
    TeaRoomTransformMaster = 4, -- 茶壶系统转交房主
    MomentsShareToChatChannel = 5, -- 朋友圈分享到聊天频道
    MomentsShareToFriend = 6, -- 朋友圈分享到好友
    MomentsShareToChatGroup = 7, -- 朋友圈分享到群聊
}

FriendSystem.CreateGroupChatUIStyle = {
    Create = 0, ---创建群聊 -默认状态
    ExpandMore = 1, -- 创建群聊  -查看更多
    ExpandGroupMembers = 2, ---创建群聊/设置群聊/退出群聊 -收起群成员
    EditGroup = 3, --设置群聊-默认状态
    EditGroupExpandMore = 4, --设置群聊-查看更多
	ExitGroup = 5, -- 退出群聊 - 默认状态
	ExitGroupExpandMore = 6, -- 退出群聊 - 查看更多
}

FriendSystem.MemberType = {
    Add = 0,  ---邀请
    Remove = 1, ---移除
    Occupy = 2, --被占用
}

-- 三种好友范围设置，随treelist，1base
FriendSystem.EScopeType = {
	Friend = 1,
	Group = 2,
}

-- 好友范围编辑页进去时的状态
FriendSystem.EScopeEditType = {
	AddFriend = 0,
	RemoveFriend = 1,
	AddGroup = 2,
	RemoveGroup = 3,
	AddClub = 4,
	RemoveClub = 5,
}

-- 空闲的游戏场景
FriendSystem.NOT_IN_PLAY_WORLD_TYPE = {
    [1] = true,  -- 大世界
    [4] = true,  -- 公会
    [7] = true,  -- 测试场景
    [11] = true, -- 家园住宅
    [12] = true, -- 家园庄园
}

---@private
function FriendSystem:onCtor()
    self.model = nil
    self.sender = nil
    self.FoeList = {}
    self.HereditaryFoeList = {}
    self.FriendTreeListFold = {}
    self.bIsImmerse = false
    self.FriendTreeListExpansion = {}
end

---@private
function FriendSystem:onInit()
    ---@type FriendModel
    self.model = kg_require("Gameplay.LogicSystem.Friend.FriendModel").new(false, false)
    ---@type FriendSender
    self.sender = kg_require("Gameplay.LogicSystem.Friend.FriendSender").new()

	self:AddListenerV2(EEventTypesV2.ON_HUD_IMMERSE, "OnHUDImmerse")

    Game.InputSystem:RegisterSpecialUIJumpHandler("OpenFriendPanel_Action", function(actionName, keyActionCfg, actionTypeID, uiJumpCfg)
        if actionName == "OpenFriendPanel_Action" and self.bIsImmerse then
            -- 沉浸模式，不做跳转
            return true
        end
    end)
end

function FriendSystem:OnHUDImmerse(isImmerse)
    self.bIsImmerse = isImmerse
end

---@private
function FriendSystem:onUnInit()
end

function FriendSystem:AfterPlayerInit()
    if Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_FRIEND,false) then
        self.sender:reqGetBothWayFriends()
        self.sender:ReqGetRelationInfo()
    end
end

function FriendSystem:OnBackToSelectRole()
    self.model:init()
    table.clear(self.FriendTreeListExpansion)
end

function FriendSystem:OnBackToLogin()
    self.model:init()
    table.clear(self.FriendTreeListExpansion)
end

--region 好友对外接口
---通过entity_id查找好友，封装一层，供外界使用
function FriendSystem:GetRelationInfoByEntityID(entity_id)
    return self.model.relationInfos[entity_id]
end

---查找真正好友，在黑名单、陌生人列表的不算
function FriendSystem:GetFriendInfoByEntityID(entity_id)
    local relationInfo = self.model.relationInfos[entity_id]
    if relationInfo then
        if relationInfo.groupId == const.FRIEND_SERVER_STRANGER_GROUP_ID or
            relationInfo.groupId == const.FRIEND_SERVER_BLACK_GROUP_ID then
            return nil
        end
        return relationInfo
    end
    return nil
end

---返回所有真正好友
function FriendSystem:GetFriendInfoList()
	local realFriendList = {}
	for _,value in pairs(self.model.relationInfos) do
		if value then
			if value.groupId ~= const.FRIEND_SERVER_STRANGER_GROUP_ID and
				value.groupId ~= const.FRIEND_SERVER_BLACK_GROUP_ID and
				self.model.relationBothWayMap[value.id]
			then
				table.insert(realFriendList, value)
			end
		end
	end
	return realFriendList
end

function FriendSystem:onGetBothwayFriends(bothWayMap)
    self.model.relationBothWayMap = bothWayMap or {}
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GET_RELATION_BOTH_WAY_INFOS)
    self:GetBothwayFriendFrequentInfo()
end

---查找所有的好友分组
function FriendSystem:GetFriendGroupList()
    return self.model.friendGroupList
end

---返回好友relationInfos
function FriendSystem:GetRelationInfos()
    return self.model.relationInfos
end

---查找双向好友
function FriendSystem:GetRelationBothWayMap()
    return self.model.relationBothWayMap
end

---获取玩家应该显示的名字（如果是好友且设置了好友备注，则返回备注名，否则返回rolename）
function FriendSystem:GetFriendShowName(entity_id, role_name)
    local relationInfo = self.model.relationInfos[entity_id]
    if relationInfo then
        return string.isEmpty(relationInfo.remark) and relationInfo.rolename or relationInfo.remark
    end
    --非好友
    return role_name
end

function FriendSystem:IsFriendTop(eid)
    if self.model.friendTopMap[eid] and self.model.friendTopMap[eid] > 0 then
        return true
    else
        return false
    end
end

---删除好友申请
---@param EntityID string 好友申请的EntityID
---@param bShowBackground boolean 左侧是否需要显示背景底板，默认显示（"您有%d条好友申请未处理" 或 "暂无好友申请"）
function FriendSystem:DeleteFriendApply(EntityID, bShowBackground)
    self.model:DeleteFriendApply(EntityID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION, bShowBackground)
end

function FriendSystem:IsInBlackList(EntityID)
    return self.model.relationInfos and self.model.relationInfos[EntityID] and
        self.model.relationInfos[EntityID].groupId == const.FRIEND_SERVER_BLACK_GROUP_ID
end

function FriendSystem:GetRelationsByGroupId(groupId)
    local list = {}
    for k, v in pairs(self.model.relationInfos or {}) do
        if groupId == const.FRIEND_SERVER_BLACK_GROUP_ID then
            if v.groupId == groupId then
                list[#list + 1] = v
            end
        else
            if v.groupId == groupId and self.model.relationBothWayMap[v.id] then
                list[#list + 1] = v
            end
        end
    end
    return list
end

function FriendSystem:GetFriendGroupCount()
    local list = self.model:GetFriendGroupList()
    return #list
end

--- 注意这里是单向好友!!! 已申请但是对方未同意的好友也会返回true
--- 正常好友应该使用CheckIsBothWayFriend
function FriendSystem:IsOneWayFriend(eid)
    return self.model:isFriend(eid)
end

--- 当前玩家是否是双向好友, 正常好友都是双向好友
function FriendSystem:CheckIsBothWayFriend(gbId)
    if not self:GetFriendInfoByEntityID(gbId) then
        return false
    end
    if self.model.relationBothWayMap then
        return self.model.relationBothWayMap[gbId] or false
    end
    return false
end

--- 判断是否是正常好友
function FriendSystem:IsFriend(gbId)
    return self:CheckIsBothWayFriend(gbId)
end

--endregion
--region 推荐好友
function FriendSystem:GetCurRecommendFriend()
	if not self.model.recommendFriendList or #self.model.recommendFriendList == 0 then
		Game.FriendSystem.sender:sendGetRecommendationList()
	elseif self.model.recommendFriendIndex > #self.model.recommendFriendList then
		self.model:ClearRecommendFriendList()
		Game.FriendSystem.sender:sendGetRecommendationList()
	else
		self.model.recommendFriendIndex = self.model.recommendFriendIndex + 1
		return self.model.recommendFriendList[self.model.recommendFriendIndex-1]
	end
end
--endregion

--region 好友server method
function FriendSystem:onSyncRelationList(friendList, npcFriendList)
    for _, info in ipairs(friendList) do
        self.model:AddRelationInfo(info)
    end

    -- for _, info in ipairs(npcFriendList) do
    --     info = clientNpcUtils.procNpcRelationInfo(info)
    --     self.model:AddRelationInfo(info)
    -- end

    if not self.model.friendGroupList or #self.model.friendGroupList == 0 then
		self.sender:sendGetFriendGroupList()
    end
end

function FriendSystem:onGetRecommendationListRespond(list)
    self.model:RecordRecommendExpireTime()
    self.model:SetRecommendFriendList(list)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_RECOMEND_FRIEND_LIST)
end

function FriendSystem:onGetRecentlyMeetDataRespond(list)
    table.sort(list, function(a, b)
        return a.time > b.time
    end)
    self.model:SetRecentlyMeetList(list)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_RECENTLY_MEET)
end

function FriendSystem:onAddRecentlyMeetNotify(info)
    self.model:AddRecentlyMeet(info)
end

function FriendSystem:onDelRecentlyMeetNotify(entityID)
    self.model:removeByEntityID(self.model.recentlyMeetList, entityID)
end

function FriendSystem:onGetPlayerDetailInfoRespond(data)
    --TODO: bug
    if data.playerDetailInfo.id == self.id then
        self.model:SetPlayerDetailInfo(data)
    end
end

function FriendSystem:onGetRelationInfoRespond(infoList, npcInfoList)
    for _, info in ipairs(infoList or {}) do
        self.model:AddRelationInfo(info)
        Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_PLAYER_RELATION_INFO, info.id)
    end
end

function FriendSystem:onRefreshRelationIncDataRespond(incDataList)
    if not incDataList then
        return
    end
    for i = 1, #incDataList do
        local data = incDataList[i] or {}
        local baseData = self.model.relationInfos[data.id] or {}
        table.merge(baseData, data)
    end
end

function FriendSystem:onCheckBothwayFriendRespond(isBothWayFriend, playerEntityID)
    self.model:UpdatePlayerBothWayInfo(playerEntityID, isBothWayFriend)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHECK_BOTH_WAY_FRIENDSHIP, { isBothWayFriend, playerEntityID })
end

function FriendSystem:onBroadcastPersonalState(EntityID, state)
    local needUpdateOnline = self.model:UpdateSingleFriendProperty(EntityID, "state", state)
    if needUpdateOnline then
        Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_ONLINE_STATES_CHANGE, EntityID)
    end
end

function FriendSystem:onSyncFriendApplicationCount(count)
    self.model:SetApplicationNotify(count)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
end

function FriendSystem:onSetFriendStateRemindFlag(EntityID, flag)
    local friend = self:GetRelationInfoByEntityID(EntityID)
    if friend then
        friend.remindFlag = flag
    end
end


function FriendSystem:onCheckPlayerExistRespond(EntityID, isExist)
    if not isExist then
    elseif Game.me.whisperTarget and EntityID == Game.me.whisperTarget then
        Game.ChatWhisperSystem:WhisperToPlayer(EntityID)
    end
end

function FriendSystem:onGetFriendApplicationListRespond(list, npcIDList)
	--升序排序后再存储
	table.sort(list, function(a, b)
		return a.time < b.time
	end)
    self.model:SetFriendApplicationList(list)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
end

function FriendSystem:isFriendApplicationAlreadyPopped(applicationInfoID)
    local showType = Enum.EElasticStripData.FriendInvite
    local showList = Game.PopTipsSystem:GetShowApplyInviteList()
    if not showList then
        return false
    end
    for _, showInfo in pairs(showList) do
        if showInfo.Type == showType and showInfo.OperID == applicationInfoID then
            return true
        end
    end
    return false
end


function FriendSystem:onAddFriendApplicationNotify(applicationInfo)
    self.model:AddFriendApplication(applicationInfo)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
    if Game.SettingsManager:GetIniData("ShowTeamApply") then
        local bShow = not self:IsOneWayFriend(applicationInfo.id)   -- 当前是单向好友说明这是互相申请, 那就不弹提示
        if bShow then
            if not self:isFriendApplicationAlreadyPopped(applicationInfo.id) then
                Game.PopTipsSystem:OnAddAddApplyInviteListElem({
                    Type = Enum.EElasticStripData.FriendInvite,
                    OperName = applicationInfo.rolename,
                    OperProfession = applicationInfo.school,
                    OperID = applicationInfo.id,
                    IsTeam = false,
                    OperLevel = applicationInfo.lv
                })
            end
        end
    end
end

function FriendSystem:onDelFriendApplicationNotify(EntityID)
    self:markFriendApplyProcessed({ EntityID })
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
end

function FriendSystem:onAcceptFriendApplicationRespond(EntityIDs, npcIds)
    table.mergeList(EntityIDs, npcIds)
    local acptFriendIds = EntityIDs
    self:markFriendApplyProcessed(acptFriendIds)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
end

function FriendSystem:onIgnoreFriendApplicationRespond(EntityIDs, npcIds)
    table.mergeList(EntityIDs, npcIds)
    local ignoreFriendIds = EntityIDs
    self:markFriendApplyProcessed(ignoreFriendIds)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
end

function FriendSystem:onAddFriendRespond(friendInfo)
    self.model:RemoveRecentlyMeet(friendInfo.id)
    self.model:RemoveRecommendFriend(friendInfo.id)
    local friendData = self.model:AddRelationInfo(friendInfo)

    --据研究，这个事件主要用于驱动UI将“加为好友”按钮隐藏（如果目标好友已被添加的话）
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_ADD_FRIEND, friendData)
end

function FriendSystem:onDelFriendRespond(EntityID)
    if self.model:RemoveFriendRelationInfo(EntityID) then
        self.model:UpdatePlayerBothWayInfo(EntityID, false)
        --这个事件用于驱动UI刷新好友界面
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_DELETE_FRIEND, EntityID)
        --向头像菜单发送更新事件
        Game.GlobalEventSystem:Publish(EEventTypesV2.PLAYERCARD_UPDATE)
    end
end

function FriendSystem:onUpdateFriendAttractionNotify(EntityID, value, level)
    if not self.model.relationInfos or not self.model.relationInfos[EntityID] then
        Log.Warning("onUpdateFriendAttractionNotify: not friend", EntityID)
    end
    self.model:UpdateSingleFriendProperty(EntityID, "attraction", value)
    self.model:UpdateSingleFriendProperty(EntityID, "attractionLevel", level)
    Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_ATTRACTION_CHANGE, EntityID)
end

function FriendSystem:onAddBlackRespond(data)
    Game.ChatWhisperSystem:RemoveWhisperMessage(data.id)
    self.model:AddRelationInfo(data)
    self.model:UpdatePlayerBothWayInfo(data.id, false)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_ADD_BLACK_FRIEND, data.id)
    --向头像菜单发送更新事件
    Game.GlobalEventSystem:Publish(EEventTypesV2.PLAYERCARD_UPDATE)

    Game.ChatSystem:OnBlackListUpdate()
end

function FriendSystem:onDelBlackRespond(EntityID)
    if self.model:RemoveBlackRelationInfo(EntityID) then
        Game.ChatWhisperSystem:RemoveWhisperMessage(EntityID)
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_DELETE_BLACK_FRIEND, EntityID)
        --向头像菜单发送更新事件
        Game.GlobalEventSystem:Publish(EEventTypesV2.PLAYERCARD_UPDATE)

        Game.ChatSystem:OnBlackListUpdate()
    end
end

function FriendSystem:onChangeGroupRespond(EntityID, newGroupId)
    self.model:UpdateSingleFriendProperty(EntityID, "groupId", newGroupId)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_GROUP_LIST)
end

function FriendSystem:onSetFriendRemarkRespond(EntityID, remark)
    self.model:UpdateSingleFriendProperty(EntityID, "remark", remark)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SET_FRIEND_REMARK, { EntityID, remark })
end

function FriendSystem:onGetGroupListRespond(groupList)
    self.model.friendGroupList = groupList
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_GROUP_LIST)
end

function FriendSystem:onAddFriendGroupRespond(groupId, groupName, EntityIDs)
    self.model.friendGroupList = self.model.friendGroupList or {}
    self.model.friendGroupList[#self.model.friendGroupList + 1] = { id = groupId, name = groupName }
    self.model:UpdateFriendGroup(groupId, EntityIDs)
    Game.GlobalEventSystem:Publish(EEventTypesV2.HIDE_PARTNER_EDIT)
end

function FriendSystem:onEditFriendGroupRespond(groupId, groupName, EntityIDs)
    local hasGroup = false
    for i, v in ipairs(self.model.friendGroupList or {}) do
        if v.id == groupId then
            v.name = groupName
            hasGroup = true
            break
        end
    end
    if hasGroup then
        self.model:UpdateFriendGroup(groupId, EntityIDs)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.HIDE_PARTNER_EDIT)
end

function FriendSystem:onDelFriendGroupRespond(groupId)
    --删除分组后，里面的好友移动到默认分组
    for EntityID, info in pairs(self.model.relationInfos or {}) do
        if info.groupId == groupId then
            self.model.relationInfos[EntityID].groupId = const.FRIEND_SERVER_FRIEND_GROUP_ID
        end
    end
    for i, v in ipairs(self.model.friendGroupList or {}) do
        if v.id == groupId then
            table.remove(self.model.friendGroupList, i)
            break
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_GROUP_LIST)
end

function FriendSystem:onWhisperPushSettingsRespond(pushSettings)
    self.model.whisperPushSettings = pushSettings
end

function FriendSystem:onSetOnlineStateRespond(state)
    self.model.whisperState = state
end

function FriendSystem:onRefuseFriendFlagRespond(flag)
    self.model.isRefuseFriend = flag
end

function FriendSystem:onSyncTopWhisperInfo(new, old, time)
    local recentWhisperList = Game.ChatWhisperSystem:GetRecentWhisperList()
    for i, data in ipairs(recentWhisperList) do
        if data.relationInfo.id == old then
            data.time = time
        end
        data.isTop = self:IsFriendTop(v.relationInfo.id)
    end
    Game.ChatWhisperSystem:SortRecentWhisperList()
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHANGE_TOP_WHISPER)
end

function FriendSystem:onTopWhisperMapChange(eid, time)
    local recentWhisperList = Game.ChatWhisperSystem:GetRecentWhisperList()
    for i, data in ipairs(recentWhisperList) do
        if data.relationInfo.id == eid then
            data.isTop = time > 0
            data.time = time
        end
    end
    self.model.friendTopMap[eid] = time
    Game.ChatWhisperSystem:SortRecentWhisperList()
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHANGE_TOP_WHISPER)
end

function FriendSystem:onSyncTopWhisperMap(topWhisperMap)
    local recentWhisperList = Game.ChatWhisperSystem:GetRecentWhisperList()
    self.model.friendTopMap = topWhisperMap
    for i, data in ipairs(recentWhisperList) do
        local isTop = false
        if self.model.friendTopMap[data.relationInfo.id] and self.model.friendTopMap[data.relationInfo.id] > 0 then
            isTop = true
        end
        data.isTop = isTop
    end
    Game.ChatWhisperSystem:SortRecentWhisperList()
    Game.GlobalEventSystem:Publish(EEventTypesV2.CHANGE_TOP_WHISPER)
end

function FriendSystem:onRefreshFriendInfo(EntityID, data)
    local info = self:GetRelationInfoByEntityID(EntityID)
    if info then
        table.merge(info, data)
    end
end

function FriendSystem:onGetBothwayFriendFrequentInfo(frequentInfoMap)
    self.model.frequentInfoMap = frequentInfoMap
    Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_INFO_CHANGE)
end

function FriendSystem:UpdateSingleFriendProperty(EntityID, data)
    self.model:UpdateSingleFriendProperty(EntityID, "state",
    data and data.state or const.FRIEND_SYSTEM_PLAYER_STATE.OFFLINE)
    self.model:UpdateSingleFriendProperty(EntityID, "lv", data.lv)
    self.model:UpdateSingleFriendProperty(EntityID, "photo", data.photo)
    self.model:UpdateSingleFriendProperty(EntityID, "rolename", data.rolename)
    self.model:UpdateSingleFriendProperty(EntityID, "guildId", data.guildId)
    self.model:UpdateSingleFriendProperty(EntityID, "guildName", data.guildName)
    self.model:UpdateSingleFriendProperty(EntityID, "worldTemplateId", data.worldTemplateId)
    self.model:UpdateSingleFriendProperty(EntityID, "worldReturnTemplateId", data.worldReturnTemplateId)
    self.model:UpdateSingleFriendProperty(EntityID, "offlineTime", data.offlineTime)
    --触发界面更新
    Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_ONLINE_STATES_CHANGE, EntityID)
end

function FriendSystem:OnSingleFriendPropertyChange(EntityID, data)
    for propName, value in pairs(data) do
        self.model:UpdateSingleFriendProperty(EntityID, propName, value)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_INFO_CHANGE, EntityID)
end

--endregion

--region 好友client method
function FriendSystem:SendGetRecentlyMeetList()
    local last = self.model.lastSendGetRecentlyMeetListTime or 0
    local cur = _G._now()
    if cur - last > 3 then
        self.model.lastSendGetRecentlyMeetListTime = cur
        self.sender:sendGetRecentlyMeetList()
        return true
    end
    return false
end


function FriendSystem:ApplyFriend(EntityID)
    self.sender:applyFriend(EntityID)
end

function FriendSystem:SendAcceptFriendApplication(EntityIDList)
    if not EntityIDList then
        EntityIDList = self.model:GetApplicationEntityIDList()
        local realEntityIDList = {}
        local realNpcIdList = {}
        for _, id in pairs(EntityIDList) do
            realEntityIDList[#realEntityIDList + 1] = id
        end
        self.sender:doAcceptAllFriendApplicationRequest(realEntityIDList, realNpcIdList)
    else
        for _, id in pairs(EntityIDList) do
            self.sender:doAcceptOneFriendApplicationRequest(id)
        end
    end
end

function FriendSystem:SendIgnoreFriendApplication(EntityIDList)
    Log.Debug("sendIgnoreFriendApplication")
    --Log.Dump(EntityIDList)
    if not EntityIDList then
        EntityIDList = self.model:GetApplicationEntityIDList()
        local realEntityIDList = {}
        local realNpcIdList = {}
        for _, id in pairs(EntityIDList) do
            realEntityIDList[#realEntityIDList + 1] = id
        end
        self.sender:doIgnoreAllFriendApplicationRequest(realEntityIDList, realNpcIdList)
    else
        for _, id in pairs(EntityIDList) do
            self.sender:doIgnoreOneFriendApplicationRequest(id)
        end
    end
end

function FriendSystem:SendGetRelationInfo(EntityIDs)
    local realEntityIDs = {}
    local npcIds = {}
    for _, EntityID in pairs(EntityIDs or {}) do
        realEntityIDs[#realEntityIDs + 1] = EntityID
    end
    self.sender:doGetRelationInfoRequest(realEntityIDs, npcIds)
end

function FriendSystem:SendAddFriend(EntityID, source, subSource)
    if self:isInBlackList(EntityID) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FRIEND_BLACKLIST_APPLY_FAILED)
    else
        Game.me:doAddFriendRequest(EntityID, source, subSource)
    end
end

function FriendSystem:SetFriendStateRemindFlag(EntityID, type, isOn)
    local friend = self:GetRelationInfoByEntityID(EntityID)
    if friend then
        if isOn then
            bitset.setBit(friend.remindFlag, type)
        else
            bitset.clearBit(friend.remindFlag, type)
        end
        self.sender:setFriendStateRemindFlag(EntityID, friend.remindFlag)
    end
end

function FriendSystem:GetBothwayFriendFrequentInfo()
    local friendList = {}
    for k, v in pairs(self.model.relationInfos) do
        if self:CheckIsBothWayFriend(v.id)
            and self:IsFriendOnline(v.id, {[const.FRIEND_SYSTEM_PLAYER_STATE.ONLINE] = true, [const.FRIEND_SYSTEM_PLAYER_STATE.AFK] = true}) then
            table.insert(friendList, v.id)
        end
    end
    if #friendList > 0 then
        self.sender.Bridge.remote:ReqGetBothwayFriendFrequentInfo(friendList)
    else
        Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_INFO_CHANGE)
    end
end

--endregion

--region 对好友的隐身状态
function FriendSystem:ReqChangeShowStatus(status)
	self.sender:ReqChangeShowStatus(status)
end
function FriendSystem:ReqSetShowStatusRange(list)
	if not list then return end
	self.sender:ReqSetShowStatusRange({
		friendsRange = list.friendsRange or {},
		groupsRange = list.groupsRange or {},
	})
end
function FriendSystem:GetShowStatus()
	return GetMainPlayerPropertySafely("showStatus")
end

-- onlineStatus填写一个table，key是state enum，value=true。用以说明哪些state算在线
-- 用以处理暂离的情况。
-- 可不填
function FriendSystem:IsFriendOnline(entityID, onlineStatus)
	local info = self:GetRelationInfoByEntityID(entityID)
	local checkStatus = onlineStatus or {[const.FRIEND_SYSTEM_PLAYER_STATE.ONLINE] = true}
	if not info then
		Log.Error("IsFriendOnline failed, entityID: ", entityID)
		return false
	end
	--真不在线
	if not checkStatus[info.state] then
		return false
	end
	--在线 SHOWALL
	if info.showStatus == const.FRIEND_SYSTEM_PLAYER_HIDE_STATUS.SHOW_ALL then
		return true
	end
	--在线 PART_SHOW 在不在范围里
	if info.showStatus == const.FRIEND_SYSTEM_PLAYER_HIDE_STATUS.PART_SHOW then
		return info.isSet
	end
	--在线 PART_HIDE 在不在范围里
	if info.showStatus == const.FRIEND_SYSTEM_PLAYER_HIDE_STATUS.PART_HIDE then
		return info.isSet ~= true
	end
	--在线 HIDE_ALL
	if info.showStatus == const.FRIEND_SYSTEM_PLAYER_HIDE_STATUS.HIDE_ALL then
		return false
	end
	return checkStatus[info.state] == true
end

function FriendSystem:IsFriendRealOnline(entityID)
	local info = self:GetFriendInfoByEntityID(entityID)
	if info then
		return info.state == const.FRIEND_SYSTEM_PLAYER_STATE.ONLINE
	else
		return false
	end
end

function FriendSystem:GetDisplayOfflineTime(entityID)
	if self:IsFriendOnline(entityID) then
		return 0
	end
	local info = self:GetRelationInfoByEntityID(entityID)
	if not self:IsFriendRealOnline(entityID) then
		return info.offlineTime
	else
		return info.setTime or info.offlineTime
	end
end
--endregion

--region 查询自己的隐身设置范围
FriendSystem.compareFunc = {
	[true] = function(a) return a ~= nil end,
	[false] = function(a) return a == nil end
}

--查询是否在range里
--- @param scopeEnum 范围类型
--- @param bIn 是否在范围内
--- @param rangeList 范围列表 可不填，默认就是Game.me.statusRange
function FriendSystem:GetInOrOutRangeList(scopeEnum, bIn, rangeList)
	local returnList = {}
	if not scopeEnum then
		Log.Error("GetInOrOutRangeList scopeEnum is nil")
		return returnList
	end
	if bIn == nil then
		Log.Error("GetInOrOutRangeList bIn is nil")
		return returnList
	end
	
	local inRangeList, allList
	if scopeEnum == self.EScopeType.Friend then
		inRangeList = rangeList or GetMainPlayerPropertySafely("statusRange")["friendsRange"]
		allList = self:GetFriendInfoList()
		for _, v1 in pairs(allList) do
			if FriendSystem.compareFunc[bIn](inRangeList[v1.id]) then
				table.insert(returnList, {
					id = v1.id,
					school = v1.school,
					lv = v1.lv,
					rolename = v1.rolename,
					attraction = v1.attraction,
					attractionLevel = v1.attractionLevel,
					groupId = v1.groupId,
				})
			end
		end
	elseif scopeEnum == self.EScopeType.Group then
		inRangeList = rangeList or GetMainPlayerPropertySafely("statusRange")["groupsRange"]
		allList = self:GetFriendGroupList()
		for _, v1 in pairs(allList) do
			if FriendSystem.compareFunc[bIn](inRangeList[v1.id]) then
				table.insert(returnList, {
					id = v1.id,
					name = v1.name
				})
			end
		end
	end
	
	return returnList
end

--endregion
--region 好友设置
function FriendSystem:GetFriendIsNoDisturb(entityID)
	local friendInfo = self:GetFriendInfoByEntityID(entityID)
	if friendInfo then
		return friendInfo.noDisturb
	end
end

function FriendSystem:ReqSetNoDisturb(entityID, isSet)
	local bNoDisturb = self:GetFriendIsNoDisturb(entityID)
	if isSet ~= bNoDisturb then
		self.sender:ReqSetNoDisturb(entityID,isSet)
	end
end
--endregion

--region 逻辑相关
function FriendSystem:markFriendApplyProcessed(EntityIDs)
    for _, v in ipairs(EntityIDs or {}) do
        local apply = self.model:GetFriendApplyByEntityID(v)
        if apply then
            apply.isProcessed = true
        end
    end
end

function FriendSystem:getFriendStateRemindFlag(EntityID, type)
    local friend = self:GetRelationInfoByEntityID(EntityID)
    if friend and friend.remindFlag then
        return bitset.getBit(friend.remindFlag, type)
    end
    return false
end

function FriendSystem:isInBlackList(EntityID)
    return self.model.relationInfos and self.model.relationInfos[EntityID] and
        self.model.relationInfos[EntityID].groupId == const.FRIEND_SERVER_BLACK_GROUP_ID
end
--endregion

--region 对手/恩怨
function FriendSystem:SetFoeAndHereditaryList(FoeList, HereditaryFoeList)
    self.FoeList = FoeList
    self.HereditaryFoeList = HereditaryFoeList
end

function FriendSystem:GetFoeList(filterText)
    local list = {}
    for _, v in ipairs(self.FoeList) do
        v.bFoe = true
        table.insert(list, v)
    end
    table.sort(list, function(a, b)
        if a.hateValue ~= b.hateValue then
            return a.hateValue > b.hateValue
        else
            return a.rolename < b.rolename
        end
    end)
    local retList = {}
    local ShowCount = 0
    local OnlineCount = 0
    for _, info in ipairs(list) do
        local ShouldShowThisEntry = true
        if filterText and filterText ~= "" then
            --如果过滤字符串不为空，则用字符串来过滤玩家名字或备注
            local ShowName = string.isEmpty(info.remark) and info.rolename or info.remark
            if string.find(ShowName, filterText) == nil then
                ShouldShowThisEntry = false
            end
        end
        if ShouldShowThisEntry then
            table.insert(retList, info)
            ShowCount = ShowCount + 1
        end
        --在线数量不会受到过滤的影响
        if self:IsFriendOnline(info.id) then
            OnlineCount = OnlineCount + 1
        end
    end
    return retList, OnlineCount, ShowCount
end

function FriendSystem:GetHereditaryFoeList(filterText)
    local list = {}
    for _, v in ipairs(self.HereditaryFoeList) do
        v.bFoe = true
        v.bHereditaryFoe = true
        table.insert(list, v)
    end
    table.sort(list, function(a, b)
        if a.hateValue ~= b.hateValue then
            return a.hateValue > b.hateValue
        else
            return a.rolename < b.rolename
        end
    end)
    local retList = {}
    local ShowCount = 0
    local OnlineCount = 0
    for _, info in ipairs(list) do
        local ShouldShowThisEntry = true
        if filterText and filterText ~= "" then
            --如果过滤字符串不为空，则用字符串来过滤玩家名字或备注
            local ShowName = string.isEmpty(info.remark) and info.rolename or info.remark
            if string.find(ShowName, filterText) == nil then
                ShouldShowThisEntry = false
            end
        end
        if ShouldShowThisEntry then
            table.insert(retList, info)
            ShowCount = ShowCount + 1
        end
        --在线数量不会受到过滤的影响
        if self:IsFriendOnline(info.id) then
            OnlineCount = OnlineCount + 1
        end
    end
    return retList, OnlineCount, ShowCount
end

function FriendSystem:AddFoe(FoeInfo)
    table.insert(self.FoeList, FoeInfo)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_ADD_FRIEND)
end

function FriendSystem:RemoveFoe(FoeId)
    for k, v in ipairs(self.FoeList) do
        if v.id == FoeId then
            table.remove(self.FoeList, k)
            break
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_DELETE_FRIEND)
end

function FriendSystem:IsFoe(eid)
    for k, v in ipairs(self.FoeList) do
        if v.id == eid then
            return true
        end
    end
    return false
end

function FriendSystem:IsHereditaryFoe(eid)
    for k, v in ipairs(self.HereditaryFoeList) do
        if v.id == eid then
            return true
        end
    end
    return false
end

function FriendSystem:AddHereditaryFoe(HereditaryFoeInfo)
    table.insert(self.HereditaryFoeList, HereditaryFoeInfo)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_ADD_FRIEND)
end

function FriendSystem:RemoveHereditaryFoe(HereditaryFoeId)
    for k, v in ipairs(self.HereditaryFoeList) do
        if v.id == HereditaryFoeId then
            table.remove(self.HereditaryFoeList, k)
            break
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_DELETE_FRIEND)
end

function FriendSystem:SyncFoeInfo(FoeInfo)
    for k, v in ipairs(self.FoeList) do
        if v.id == FoeInfo.id then
            v.hateValue = FoeInfo.hateValue
            break
        end
    end
    for k, v in ipairs(self.HereditaryFoeList) do
        if v.id == FoeInfo.id then
            v.hateValue = FoeInfo.hateValue
            break
        end
    end
end

--endregion

function FriendSystem:SetFriendDisplayState(friendInfo)
    local frequentInfo = self.model.frequentInfoMap[friendInfo.id]
    if frequentInfo then
        friendInfo.teamMemberSize = frequentInfo.teamMemberSize
        friendInfo.groupMemberSize = frequentInfo.groupMemberSize
        if frequentInfo.worldTemplateId then
            friendInfo.worldTemplateId = frequentInfo.worldTemplateId
        end
        if frequentInfo.worldReturnTemplateId then
            friendInfo.worldReturnTemplateId = frequentInfo.worldReturnTemplateId
        end

    end
    local worldId = friendInfo.worldTemplateId
    if friendInfo.worldReturnTemplateId > 0 then
        worldId = friendInfo.worldReturnTemplateId
    end
    local bInPlay = false
    if worldId > 0 then
        local worldInfo = Game.TableData.GetLevelMapDataRow(worldId)
        if worldInfo then
            local worldType = worldInfo.Type
            if not FriendSystem.NOT_IN_PLAY_WORLD_TYPE[worldType] then
                bInPlay = true
            end
        end
    end

    if self:IsFriendOnline(friendInfo.id, {[const.FRIEND_SYSTEM_PLAYER_STATE.ONLINE] = true, [const.FRIEND_SYSTEM_PLAYER_STATE.AFK] = true}) then
        if bInPlay then
            friendInfo.DisplayState = Enum.EFriendDisplayState.InPlay
        elseif friendInfo.teamMemberSize and friendInfo.teamMemberSize > 0 or
            friendInfo.groupMemberSize and friendInfo.groupMemberSize > 0 then
            friendInfo.DisplayState = Enum.EFriendDisplayState.InTeam
        else
            friendInfo.DisplayState = Enum.EFriendDisplayState.Empty
        end
    else
		friendInfo.DisplayState = Enum.EFriendDisplayState.Offline
    end
end

function FriendSystem:GetFriendRelationList(groupId, filterText)
    local list = self:GetRelationsByGroupId(groupId)
    for _, v in ipairs(list) do
        self:SetFriendDisplayState(v)
    end

    table.sort(list, function(a, b)
        local attractA = a.attraction or a.favorability or 0
        local attractB = b.attraction or b.favorability or 0

        if a.DisplayState ~= b.DisplayState then
            return a.DisplayState < b.DisplayState
        elseif a.DisplayState == Enum.EFriendDisplayState.Offline then
            return self:GetDisplayOfflineTime(a.id) > self:GetDisplayOfflineTime(b.id)
        elseif attractA ~= attractB then
            return attractA > attractB
        else
            return a.rolename < b.rolename
        end
    end)

    local retList = {}
    local ShowCount = 0
    local OnlineCount = 0
    for _, info in ipairs(list) do
        local ShouldShowThisEntry = true
        if filterText and filterText ~= "" then
            --如果过滤字符串不为空，则用字符串来过滤玩家名字或备注
            if string.find(info.rolename or "", filterText) == nil and string.find(info.remark or "", filterText) == nil then
                ShouldShowThisEntry = false
            end
        end
        if ShouldShowThisEntry then
            table.insert(retList, info)
            ShowCount = ShowCount + 1
            if self:IsFriendOnline(info.id) then
                OnlineCount = OnlineCount + 1
            end
        end
        -- 现在需要一起过滤掉
        --在线数量不会受到过滤的影响
    end
    --NPC默认在线
    if groupId == const.FRIEND_SERVER_NPC_GROUP_ID then
        OnlineCount = #list
    end
    return retList, OnlineCount, ShowCount
end

return FriendSystem
