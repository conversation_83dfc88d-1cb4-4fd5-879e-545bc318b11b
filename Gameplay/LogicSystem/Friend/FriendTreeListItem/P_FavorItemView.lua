---@class WBP_FavorItemView : WBP_FavorItem_C
---@field public WidgetRoot WBP_FavorItem_C
---@field public Button UC7Button
---@field public ProgressBarAttraction UProgressBar
---@field public Text_AttractionLevel UTextBlock
---@field public GetModuleName fun(self:self):string

---@class P_FavorItemView : WBP_FavorItemView
---@field public controller P_FavorItem
local P_FavorItemView = DefineClass("P_FavorItemView", UIView)

function P_FavorItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

    controller:AddUIListener(EUIEventTypes.CLICK, self.Button, "OnClick_Button")
end

return P_FavorItemView
