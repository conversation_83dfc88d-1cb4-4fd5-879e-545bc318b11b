---@class FriendSender:SystemSenderBase
local FriendSender = DefineClass("FriendSender",SystemSenderBase)

function FriendSender:sendGetFriendGroupList()
    self.Bridge:sendGetFriendGroupList()
end

function FriendSender:sendGetFriendApplicationList()
    self.Bridge:sendGetFriendApplicationList()
end

function FriendSender:sendGetFriends()
    self.Bridge:sendGetFriends()
end

function FriendSender:sendLookupFriend(str,choice)
    self.Bridge:sendLookupFriend(str,choice)
end

function FriendSender:sendGetRecommendationList(int_server, int_gender)
    self.Bridge:sendGetRecommendationList(int_server, int_gender)
end

function FriendSender:sendGetRecentlyMeetList()
    self.Bridge:sendGetRecentlyMeetList()
end

function FriendSender:applyFriend(EntityID)
    self.Bridge:applyFriend(EntityID)
end

function FriendSender:doAcceptAllFriendApplicationRequest(realEntityIDList, realNpcIdList)
    self.Bridge.remote:ReqAcceptAllFriendApplication(realEntityIDList)
end

function FriendSender:doAcceptOneFriendApplicationRequest(id)
    self.Bridge.remote:ReqAcceptOneFriendApplication(id)
end

function FriendSender:doGetRelationInfoRequest(realEntityIDs, npcIds)
    self.Bridge.remote:doGetRelationInfoRequest(realEntityIDs, npcIds)
end

function FriendSender:reqGetBothWayFriends()
    self.Bridge:reqGetBothWayFriends()
end

function FriendSender:doQueryOtherPlayerOnlineState(entityID)
    self.Bridge:doQueryOtherPlayerOnlineState(entityID)
end

function FriendSender:doIgnoreAllFriendApplicationRequest(EntityIDList,realNpcIdList)
    self.Bridge.remote:ReqIgnoreAllFriendApplication(EntityIDList)
end

function FriendSender:doIgnoreOneFriendApplicationRequest(id)
    self.Bridge.remote:ReqIgnoreOneFriendApplication(id)
end

function FriendSender:setFriendStateRemindFlag(EntityID, flag)
    self.Bridge:setFriendStateRemindFlag(EntityID, flag)
end

function FriendSender:ReqGetRelationInfo()
    --self.Bridge.remote:onSyncRelationList()
end

--region:隐身
function FriendSender:ReqChangeShowStatus(status)
    self.Bridge:ReqChangeShowStatus(status)
end

function FriendSender:ReqSetShowStatusRange(statusList)
	self.Bridge:ReqSetShowStatusRange(statusList)
end
--endregion

function FriendSender:ReqSetNoDisturb(entityID, isSet)
    self.Bridge:ReqSetNoDisturb(entityID, isSet)
end

return FriendSender