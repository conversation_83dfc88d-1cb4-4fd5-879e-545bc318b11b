---@class FriendModel:SystemModelBase
local FriendModel = DefineClass("FriendModel",SystemModelBase)
local gameConst = kg_require("Gameplay.CommonDefines.C7_Game_Const")
local const = kg_require("Shared.Const")

function FriendModel:init()
    self.relationInfos = {}
    self.friendGroupList = {}
    self.relationBothWayMap = {}
    self.recentlyMeetList = {}
    self.friendApplicationList = {}
    self.frequentInfoMap = {}
    self.whisperPushSettings = false
    self.whisperState = false
    self.autoAFKFlag = false
    self.enterAFKMinute = false
    self.isRefuseFriend = false
    self.afkReplyContent = false
    self.recommendExpireTime = 0
    self.recommendFriendList = {}
	self.recommendFriendIndex = 1
    self.needClearFriendUIData = true
    self.lastSendGetRecentlyMeetListTime = nil
    self.friendTopMap = {}
end

function FriendModel:unInit()

end

function FriendModel:isFriend(EntityID)
    if self.relationInfos and self.relationInfos[EntityID] then
        local groupId = self.relationInfos[EntityID].groupId
        if groupId then
            return groupId == const.FRIEND_SERVER_NPC_GROUP_ID or (groupId >= 0 and groupId <= Game.TableData.GetConstDataRow("FRIEND_SERVER_MAX_GROUP_COUNT"))
        else
            return false
        end
    end
    return false
end

function FriendModel:removeByEntityID(t, EntityID)
    if not t or not EntityID then
        return
    end
    for i, v in ipairs(t) do
        if v.id == EntityID then
            table.remove(t, i)
            break
        end
    end
end


function FriendModel:AddRelationInfo(info)
    if not self.relationInfos then
        self.relationInfos = {}
    end
    self.relationInfos[info.id] = info
    return info
end

function FriendModel:RecordRecommendExpireTime()
    local now = _G._now()
    if self.recommendExpireTime and now < self.recommendExpireTime then
        return
    end
    self.recommendExpireTime = now + gameConst.FRIEND_RECOMMEND_EXPIRE_TIME
end

function FriendModel:SetRecommendFriendList(list)
    self.recommendFriendList = list
end

function FriendModel:ClearRecommendFriendList()
	self.recommendFriendList = {}
end

function FriendModel:SetRecentlyMeetList(list)
    self.recentlyMeetList = list
end

function FriendModel:AddRecentlyMeet(info)
    if not self.recentlyMeetList then
        self.recentlyMeetList = {}
    end
    self:removeByEntityID(self.recentlyMeetList, info.id)
    local list = self.recentlyMeetList
    list[#list + 1] = info
end

function FriendModel:SetPlayerDetailInfo(info)
    self.playerDetailInfo = info
end

function FriendModel:UpdatePlayerBothWayInfo(gbId, isBothWay)
    if not self.relationBothWayMap then
        self.relationBothWayMap = {}
    end
    self.relationBothWayMap[gbId] = isBothWay
end

function FriendModel:UpdateSingleFriendProperty(EntityID, prop, value)
    self:updateSingleRecentWhisperProperty(EntityID, prop, value)
    if not self.relationInfos or not self.relationInfos[EntityID] then
        Log.Warning("no relationInfo EntityID:", EntityID, debug.traceback())
        return false
    end
    local old = self.relationInfos[EntityID][prop]
    if old ~= value then
        self.relationInfos[EntityID][prop] = value

        local frequentInfo = self.frequentInfoMap[EntityID]
        if frequentInfo then
            frequentInfo[prop] = value
        end
        return true
    end
    return false
end

function FriendModel:updateSingleRecentWhisperProperty(EntityID, prop, value)
    local recentWhisperList = Game.ChatWhisperSystem:GetRecentWhisperList()
    for _,v in ipairs(recentWhisperList) do
        if v.relationInfo.id == EntityID then
            v.relationInfo[prop] = value
            return
        end
    end
end

function FriendModel:SetApplicationNotify(count)
    self.syncApplicationNotify = count > 0
end

function FriendModel:SetFriendApplicationList(list)
    self.friendApplicationList = list
end

function FriendModel:AddFriendApplication(apply)
    if not self.friendApplicationList then
        self.friendApplicationList = {}
    end
    self:removeByEntityID(self.friendApplicationList, apply.id)
    self.friendApplicationList[#self.friendApplicationList + 1] = apply
end

function FriendModel:GetFriendApplyByEntityID(EntityID)
    for i,v in ipairs(self.friendApplicationList or {}) do
        if v.id == EntityID then
            return v
        end
    end
    return nil
end

function FriendModel:RemoveRecentlyMeet(EntityID)
    self:removeByEntityID(self.recentlyMeetList, EntityID)
end

function FriendModel:RemoveRecommendFriend(EntityID)
    self:removeByEntityID(self.recommendFriendList, EntityID)
end

function FriendModel:RemoveFriendRelationInfo(EntityID)
    if self.relationInfos and self.relationInfos[EntityID] then
        if self:isFriend(EntityID) then
            self.relationInfos[EntityID].groupId = const.FRIEND_SERVER_STRANGER_GROUP_ID
            return true
        end
    end
    return false
end

function FriendModel:RemoveBlackRelationInfo(EntityID)
    if self.relationInfos and self.relationInfos[EntityID] then
        if self.relationInfos[EntityID].groupId == const.FRIEND_SERVER_BLACK_GROUP_ID then
            self.relationInfos[EntityID].groupId = const.FRIEND_SERVER_STRANGER_GROUP_ID
            return true
        end
    end
    return false
end

function FriendModel:UpdateFriendGroup(groupId, EntityIDList)
    for _, EntityID in ipairs(EntityIDList) do
        self.relationInfos[EntityID].groupId = groupId
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_GROUP_LIST)
end

function FriendModel:GetApplicationEntityIDList()
    local list = {}
    for i, v in ipairs(self.friendApplicationList or {}) do
        if not v.isProcessed then
            list[i] = v.id
        end
    end
    return list
end

function FriendModel:DeleteFriendApply(EntityID)
    self:removeByEntityID(self.friendApplicationList, EntityID)
end

function FriendModel:GetFriendGroupList()
    local list = {}
    for _, v in pairs(self.friendGroupList or {}) do
        list[#list + 1] = v
    end
    return list
end

return FriendModel