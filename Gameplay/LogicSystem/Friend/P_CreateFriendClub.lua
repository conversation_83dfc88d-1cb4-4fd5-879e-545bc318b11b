local ESlateVisibility = import("ESlateVisibility")

local StringConst = require "Data.Config.StringConst.StringConst"
local P_SearchInput = kg_require "Gameplay.LogicSystem.CommonUI.CommonInput.P_SearchInput"
---@class P_CreateFriendClub : UIController
---@field public View WBP_PartnerGroupCreatView
local P_CreateFriendClub = DefineClass("P_CreateFriendClub", UIController)

function P_CreateFriendClub:ctor()
end

function P_CreateFriendClub:OnCreate()
    self.clubID = nil
    --点击X按钮关闭界面
    self:AddUIListener(EUIEventTypes.CLICK,
        self.View.WBP_ComPopupFrameSmall.WBP_ComPopupTitle_lua.WBP_ComBtnClose_lua.Button_lua, self.OnClickClose)

    self.View.WBP_ComPopupFrameSmall:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.View.WBP_ComPopupFrameSmall.WBP_ComMask:SetVisibility(ESlateVisibility.HitTestInvisible)
    --点击背景关闭界面
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button_Back, self.OnClickBackgroud)

    --点击确定按钮
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtn_Accept.Btn_Com, self.OnClickCreate)

    --点击取消按钮
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtn_Refuse.Btn_Com, self.OnClickCancel)

    --公告输入框文字改变
    if PlatformUtil.IsMobilePlatform() then
        self:AddUIListener(EUIEventTypes.TextChanged, self.View.WBP_ComInputBig.EditText_lua, self.OnNoticeInputChange)
    else
        self:AddUIListener(EUIEventTypes.C7TextChanged, self.View.WBP_ComInputBig.EditText_lua, self.OnNoticeInputChange)
    end
    -- self:AddUIListener(EUIEventTypes.TextCommitted, self.View.WBP_ComInputBig.EditText_lua, self.FixCursorStyle)

    --Localization 1
    --修改按钮文字
    self.View.WBP_ComBtn_Accept.Text_Com:SetText(StringConst.Get("CONFIRM"))
    self.View.WBP_ComBtn_Refuse.Text_Com:SetText(StringConst.Get("CANCLE"))

    local noticeCharLimitCount = Game.TableData.GetConstDataRow("FRIEND_GROUPCHAT_BULLETIN_MAX") --群公告字数上限
    self.View.TextBlock_LimitTips:SetText(tostring(noticeCharLimitCount))

    self.comInputCell = self:BindComponent(self.View.WBP_ComInputName, P_SearchInput)
    self.comInputCell:SetData({
        Owner = self,
        InputLimit = Game.TableData.GetConstDataRow("FRIEND_GROUPCHAT_NAME_MAX"),
    })
end

function P_CreateFriendClub:OnRefresh(clubID, arg2)
    --打开时可能需要重置已输入的信息 TODO
    --Localization 1
    self.clubID = clubID
    if self.clubID then
        --现有群聊，修改设置
        self.View.WBP_ComPopupFrameSmall.WBP_ComPopupTitle_lua.Text_Title_lua:SetText(StringConst.Get(
            "SOCIAL_FRIEND_CLUB_MODIFY_SETTING"))
        --设置一下信息
        local clubInfo = Game.ChatClubSystem:getClubInfoById(clubID)
        if clubInfo then
            self.comInputCell:SetInputText(clubInfo.name)
            --Localization 1
            if string.isEmpty(clubInfo.notice) then
                self.View.WBP_ComInputBig.EditText_lua:SetText(StringConst.Get("SOCIAL_FRIEND_CLUB_EMPTY_NOTICE"))
            else
                self.View.WBP_ComInputBig.EditText_lua:SetText(clubInfo.notice)
            end
        end
    else
        --公会名默认为空
        self.comInputCell:SetInputText("")

        --公会公告默认为空
        self.View.WBP_ComInputBig.EditText_lua:SetText("")
        self.View.WBP_ComPopupFrameSmall.WBP_ComPopupTitle_lua.Text_Title_lua:SetText(StringConst.Get(
            "SOCIAL_FRIEND_CLUB_CREATE"))
    end
end

function P_CreateFriendClub:OnClickClose()
    self:__ClosePanel()
end

function P_CreateFriendClub:OnClickBackgroud()
    self:__ClosePanel()
end

function P_CreateFriendClub:OnClickCancel()
    Log.Debug("Click Cancel")
    self:__ClosePanel()
end

function P_CreateFriendClub:__ClosePanel()
    UI.HideUI("P_CreateFriendClub")
end

function P_CreateFriendClub:OnClickCreate()
    --TODO 后续接入合法性判断，目前直接算成功
    Log.Debug("OnClickOK")

    local name = self.comInputCell:GetInputText()
    local notice = self.View.WBP_ComInputBig_lua.EditText_lua:GetText()
    if self.clubID then
        --修改
        Game.ChatClubSystem:reqEditClubInfo(self.clubID, { name = name, notice = notice })
    else
        --创建

        --Localization 1
        if name == "" then
            name = StringConst.Get("SOCIAL_FRIEND_CLUB_DEFAULT_NAME")
        end
        Game.ChatClubSystem:reqBuildClub(name, notice, {}) -- luacheck: ignore
    end
    self:__ClosePanel()
end

function P_CreateFriendClub:FixCursorStyle()
    -- if PlatformUtil.IsWindows() and not import("C7FunctionLibrary").IsC7Editor() then
    --     for i = 0, EMouseCursor.EMouseCursor_MAX, 1 do
    --         import("WidgetBlueprintLibrary").SetHardwareCursor(_G.GetContextObject(), i, "Cursor/invisible", FVector2D())
    --     end
    -- end
end

function P_CreateFriendClub:OnNoticeInputChange()
    -- self:FixCursorStyle()
    --剩余可输入字数
    --Localization 1
    local curText = self.View.WBP_ComInputBig.EditText_lua:GetText()

    local noticeCharLimitCount = Game.TableData.GetConstDataRow("FRIEND_GROUPCHAT_BULLETIN_MAX") --群公告字数上线
    local availableCount = noticeCharLimitCount - utf8.len(curText)
    if availableCount < 0 then
        self.View.WBP_ComInputBig.EditText_lua:SetText(utf8.sub(curText, 1, noticeCharLimitCount + 1))
        availableCount = 0
    end

    self.View.TextBlock_LimitTips:SetText(tostring(availableCount))
end
