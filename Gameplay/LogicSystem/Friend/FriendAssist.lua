local ESlateVisibility = import("ESlateVisibility")



local FriendAssist = {}

--widget_head:WBP_ComResourceHead
--todo:有时间整体迁至通用控件 gsy
function FriendAssist.UpdateHeadInfo(uiComp, widget_head, in_school, int_level, sex)
    --等级显示
    -- if widget_head.Img_lvback then
    --     widget_head.Img_lvback:SetVisibility(import("ESlateVisibility").SelfHitTestInvisible)
    -- else
    --     widget_head.CP_Level:SetVisibility(import("ESlateVisibility").SelfHitTestInvisible)
    -- end
    widget_head.panel_status:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    widget_head.Img_lvback:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    widget_head.Text_level:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    widget_head.Text_level:SetText(tostring(int_level))

    widget_head.vx_Skill_seleced_liuguang:SetVisibility(ESlateVisibility.Collapsed)
    widget_head.Skill_seleced_lua:SetVisibility(ESlateVisibility.Collapsed)
	
	local curSex = sex or 0

    --队长标志隐藏
    --widget_head.Img_ProIcon_lua:SetVisibility(import("ESlateVisibility").Collapsed)
    widget_head:SetLT(0)
    --widget_head:SetSize(112,112)
    --widget_head:SetSize2(50,50)
    --设置头像
    if in_school then
        local Img = FriendAssist.GetSchoolIcon(in_school, curSex)
		uiComp:SetImage(widget_head.Img_HeadIcon,Img)
        local JobInfo = Game.TableData.GetPlayerSocialDisplayDataRow(in_school)
        if JobInfo and JobInfo[curSex] and JobInfo[curSex].SocialSmallIcon then
            widget_head.icon_RT:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			uiComp:SetImage(widget_head.icon_RT,JobInfo[curSex].SocialSmallIcon)
        end
    end
end

function FriendAssist.GetSchoolIcon(school,sex)
    local JobInfo = Game.TableData.GetPlayerSocialDisplayDataRow(school)[sex or 0]
    if JobInfo then
        --local Img = slua.loadObject(JobInfo.SoloHeadIcon)
        return JobInfo.SoloHeadIcon
    end
    return nil
end

function FriendAssist.GetSchoolBackImg(school)
    local JobInfo = Game.TableData.GetPlayerSocialDisplayDataRow(school)[0]
    if JobInfo then
        local BackImg = JobInfo.FriendList
        return BackImg
    end
    return nil
end

function FriendAssist.GetAttractionRange(friendAttractionData, attractionLevel)
    local min = 0
    local max = 0
    local minAttractionData = friendAttractionData[attractionLevel - 1]
    local maxAttractionData = friendAttractionData[attractionLevel]
    if minAttractionData then
        min = minAttractionData.Range
    end
    if maxAttractionData then
        max = maxAttractionData.Range
    end
    return min, max
end

return FriendAssist
