local P_ComTabSlc = kg_require "Gameplay.LogicSystem.CommonUI.P_ComTabSlc"
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@begin define

-- 分解信息数据结构
---@class ComTabHor_Param
---@field TabName string 标签名
---@field IsLock boolean 是否锁定
---@field LockCallback function 锁定回调

---@end define

---@class ComTabHor : UIComponent
---@field view ComTabHorBlueprint
local ComTabHor = DefineClass("ComTabHor", UIComponent)

---@type ComTabHorBgType
ComTabHor.BgType = { DEFAULT = 0, WHITE = 1 }

ComTabHor.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ComTabHor:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ComTabHor:InitUIData()
    ---@type function 选中回调
    self.SelectCallBack = nil
    ---@type number 当前选中序号
    self.SelectIndex = 0
    -- 临时处理异步加载时禁止点击
    self.bCanSel = true
end

--- UI组件初始化，此处为自动生成
function ComTabHor:InitUIComponent()
    self.groupView = self:CreateComponent(self.view.HB_Btn_lua, UIBaseAdapter, "ComTabHor")
    self.GroupTabList = self.groupView:CreateBaseList(P_ComTabSlc, GroupView, "HB_Btn")
end

---UI事件在这里注册，此处为自动生成
function ComTabHor:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ComTabHor:InitUIView()
end

---面板打开的时候触发
function ComTabHor:OnRefresh(...)
end

---初始化数据
---@param tabData table<number,table> tab数据
---@param selectCallback function 选中回调
---@param selectIndex nil|number 当前选中 默认为1
function ComTabHor:SetData(tabData, selectCallback, selectIndex, redPointCallback, bgType)
    self.userWidget:Event_UI_Style(bgType or self.BgType.DEFAULT)
    self.SelectIndex = 0
    self.SelectCallBack = selectCallback
    local tabLength = tabData and #tabData or 0

    if selectIndex and selectIndex > tabLength then
        Log.Error("tabhor selectIndex error")
        return
    end
    self.tabData = tabData
    self.GroupTabList:SetData(#self.tabData)
    for i=1, #self.tabData do
        self.GroupTabList:GetRendererAt(i):ResetAnim()
        self.GroupTabList:GetRendererAt(i):SetLock(self.tabData[i].IsLock)
        --if redPointCallback then
        --    redPointCallback(self.View['WBP_ComSlcTab'..i], i)
        --end
        if redPointCallback then
            redPointCallback(self.GroupTabList:GetRendererAt(i), i)
        end
    end

    self:OnSelectIndex(selectIndex or 1)
end

function ComTabHor:OnRefresh_HB_Btn(widget, index, selected)
    widget:Refresh(index, self.tabData[index].TabName)
end

---设置当前选中
---@param index number
function ComTabHor:OnSelectIndex(index)
    if not self.bCanSel then
        return
    end
    if self.tabData and self.tabData[index] then
        if self.tabData[index].IsLock then
            if self.tabData[index].LockCallback then
                self.tabData[index].LockCallback(index)
            end
            return
        end
    end
    self:SelectIndexView(index)
    if self.SelectCallBack then
        self.SelectCallBack(index)
    end
end

function ComTabHor:SelectIndexView(index)
    if self.SelectIndex == index or index > #self.tabData then
        return
    end
    if self.SelectIndex ~= 0 then
        self.GroupTabList:GetRendererAt(self.SelectIndex):AnimOn()
    end
    self.GroupTabList:GetRendererAt(index):AnimIn()
    --self:StopAllAnimations(self.TabObjectList[index])
    --self:PlayAnimationForward(self.TabObjectList[index], self.TabObjectList[1].Ani_In)
    self.SelectIndex = index
end

function ComTabHor:GetSelectedIndex()
    return self.SelectIndex
end

function ComTabHor:SetCanSel(bCanSel)
    self.bCanSel = bCanSel
end


return ComTabHor
