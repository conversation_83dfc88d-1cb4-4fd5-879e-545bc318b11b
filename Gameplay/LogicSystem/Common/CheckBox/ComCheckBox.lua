local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ComCheckBox : UIComponent
---@field view ComCheckBoxBlueprint
local ComCheckBox = DefineClass("ComCheckBox", UIComponent)

ComCheckBox.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ComCheckBox:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ComCheckBox:InitUIData()
	---@type LuaMulticastDelegate<fun()>
	self.onCheckChanged = LuaMulticastDelegate.new()
end

--- UI组件初始化，此处为自动生成
function ComCheckBox:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ComCheckBox:InitUIEvent()
	
	--manual
	self:AddUIEvent(self.view.CheckBox_lua.OnCheckStateChanged, "onCheckBoxCheckStateChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ComCheckBox:InitUIView()
end

---面板打开的时候触发
function ComCheckBox:OnRefresh(...)
	
end

---@param name string 控件文本
---@param isChecked boolean 初始勾选态
function ComCheckBox:Refresh(name,isChecked)
	self:SetCheckBoxText(name)
	self:SetChecked(isChecked)	
end

function ComCheckBox:GetIsCheck()
	return self.view.CheckBox_lua:isChecked()
end

function ComCheckBox:onCheckBoxCheckStateChanged(bIsChecked)
	self.isChecked = bIsChecked
	self:PlayAnimation(self.userWidget.Ani_Press)

	self.onCheckChanged:Broadcast(self.isChecked, self.idx)
end

---设置名称
function ComCheckBox:SetCheckBoxText(name)
	self.view.TB_Name_lua:SetText(name or "CheckBox")
end

---设置选中
function ComCheckBox:SetChecked(isChecked)
	if isChecked == nil then
		isChecked = false
	end
	self.view.CheckBox_lua:SetIsChecked(isChecked)
end

return ComCheckBox
