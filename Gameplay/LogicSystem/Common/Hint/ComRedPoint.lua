local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ComRedPoint : UIComponent
---@field view ComRedPointBlueprint
local ComRedPoint = DefineClass("ComRedPoint", UIComponent)

ComRedPoint.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ComRedPoint:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ComRedPoint:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ComRedPoint:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ComRedPoint:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ComRedPoint:InitUIView()
end

---面板打开的时候触发
function ComRedPoint:OnRefresh(...)
end

return ComRedPoint
