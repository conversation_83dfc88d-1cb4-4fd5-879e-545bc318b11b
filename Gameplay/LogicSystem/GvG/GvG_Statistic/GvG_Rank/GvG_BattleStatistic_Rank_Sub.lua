local GvG_BattleStatistic_Rank_Sub_Item = kg_require("Gameplay.LogicSystem.GvG.GvG_Statistic.GvG_Rank.GvG_BattleStatistic_Rank_Sub_Item")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GvG_BattleStatistic_Rank_Sub : UIComponent
---@field view GvG_BattleStatistic_Rank_SubBlueprint
local GvG_BattleStatistic_Rank_Sub = DefineClass("GvG_BattleStatistic_Rank_Sub", UIComponent)

GvG_BattleStatistic_Rank_Sub.eventBindMap = {
    [EEventTypesV2.ON_GVG_RANK_LIST_INFO_CHANGED] = "OnGvGRankListInfoChanged",
    [EEventTypesV2.ON_GVG_SELF_RANK_INFO_CHANGED] = "OnGvGRankSelfInfoChanged",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GvG_BattleStatistic_Rank_Sub:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GvG_BattleStatistic_Rank_Sub:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GvG_BattleStatistic_Rank_Sub:InitUIComponent()
    ---@type GvG_BattleStatistic_Rank_Sub_Item
    self.WBP_GvG_BattleStatistic_Rank_Sub_ItemCom = self:CreateComponent(self.view.WBP_GvG_BattleStatistic_Rank_Sub_Item, GvG_BattleStatistic_Rank_Sub_Item)
    ---@type UIListView childScript: GvG_BattleStatistic_Rank_Sub_Item
    self.ListViewCom = self:CreateComponent(self.view.ListView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function GvG_BattleStatistic_Rank_Sub:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GvG_BattleStatistic_Rank_Sub:InitUIView()
end

---组件刷新统一入口
function GvG_BattleStatistic_Rank_Sub:Refresh(...)
    Game.GvGSystem:ReqGVGRankInfo()
	self.WBP_GvG_BattleStatistic_Rank_Sub_ItemCom:SetVisible(false)
end

function GvG_BattleStatistic_Rank_Sub:OnGvGRankListInfoChanged()
    self.ListViewCom:Refresh(Game.GvGSystem.OtherRankList)
end

function GvG_BattleStatistic_Rank_Sub:OnGvGRankSelfInfoChanged()
	self.WBP_GvG_BattleStatistic_Rank_Sub_ItemCom:SetVisible(true)
    self.WBP_GvG_BattleStatistic_Rank_Sub_ItemCom:Refresh(Game.GvGSystem.SelfRankItem)
end

return GvG_BattleStatistic_Rank_Sub
