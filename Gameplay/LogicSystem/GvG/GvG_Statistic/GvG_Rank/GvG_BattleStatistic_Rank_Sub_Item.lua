local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class GvG_BattleStatistic_Rank_Sub_Item : UIListItem
---@field view GvG_BattleStatistic_Rank_Sub_ItemBlueprint
local GvG_BattleStatistic_Rank_Sub_Item = DefineClass("GvG_BattleStatistic_Rank_Sub_Item", UIListItem)

GvG_BattleStatistic_Rank_Sub_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GvG_BattleStatistic_Rank_Sub_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GvG_BattleStatistic_Rank_Sub_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GvG_BattleStatistic_Rank_Sub_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GvG_BattleStatistic_Rank_Sub_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GvG_BattleStatistic_Rank_Sub_Item:InitUIView()
end

---面板打开的时候触发
---@param rankItem GVG_RANK_ELEM
function GvG_BattleStatistic_Rank_Sub_Item:OnRefresh(rankItem)
    self.rankItem = rankItem
    
    self.view.Text_Name:SetText(rankItem.name)
    self.view.Text_NumKill:SetText(rankItem.killNum)
    self.view.Text_NumAssist:SetText(rankItem.assistNum)
    self.view.Text_NumResource:SetText(rankItem.score)
    self.view.Text_NumScore:SetText(rankItem.finalScore)
end

return GvG_BattleStatistic_Rank_Sub_Item
