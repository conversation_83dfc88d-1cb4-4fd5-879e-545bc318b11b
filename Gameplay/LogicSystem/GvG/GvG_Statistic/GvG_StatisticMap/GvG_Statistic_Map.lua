local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GvG_Statistic_Map : UIComponent
---@field view GvG_Statistic_MapBlueprint
local GvG_Statistic_Map = DefineClass("GvG_Statistic_Map", UIComponent)

GvG_Statistic_Map.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GvG_Statistic_Map:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GvG_Statistic_Map:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GvG_Statistic_Map:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GvG_Statistic_Map:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GvG_Statistic_Map:InitUIView()
end

---组件刷新统一入口
function GvG_Statistic_Map:Refresh(...)
	self.userWidget.Slot:SetAutoSize(true)
end

return GvG_Statistic_Map
