local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GvG_Map_Road : UIComponent
---@field view GvG_Map_RoadBlueprint
local GvG_Map_Road = DefineClass("GvG_Map_Road", UIComponent)

GvG_Map_Road.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GvG_Map_Road:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GvG_Map_Road:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GvG_Map_Road:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GvG_Map_Road:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GvG_Map_Road:InitUIView()
end

---组件刷新统一入口
function GvG_Map_Road:Refresh(occupyCampIdx)
    self.userWidget:BP_SetCamp(occupyCampIdx)
end

return GvG_Map_Road
