local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GvG_MapSign_Dot : UIComponent
---@field view GvG_MapSign_DotBlueprint
local GvG_MapSign_Dot = DefineClass("GvG_MapSign_Dot", UIComponent)

GvG_MapSign_Dot.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GvG_MapSign_Dot:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

function GvG_MapSign_Dot:OnClose()
    self.dotWidget = nil
end

---初始化数据
function GvG_MapSign_Dot:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GvG_MapSign_Dot:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GvG_MapSign_Dot:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GvG_MapSign_Dot:InitUIView()
end

---组件刷新统一入口
---@param areaInfo _GVGAreaInfoDataRow
function GvG_MapSign_Dot:Refresh(areaInfo, dotWidget)
    self.AreaID = areaInfo.ID
    self.view.Text_Name:SetText(areaInfo.Name)

    self.dotWidget = dotWidget
end

---@param areaBriefInfo AreaBriefInfo
function GvG_MapSign_Dot:RefreshStatus(areaBriefInfo)
    local campIdx = areaBriefInfo.CampIdx
    self.userWidget:BP_SetCamp(campIdx)
    if self.dotWidget then
        self.dotWidget:BP_SetCamp(campIdx)
    end
end

return GvG_MapSign_Dot
