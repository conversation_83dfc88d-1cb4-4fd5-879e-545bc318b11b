local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
local ReminderUtils = kg_require("Gameplay.LogicSystem.Reminder.ReminderUtils")
local ReminderUICommon = DefineClass("Reminder<PERSON>Common", ReminderUIBase)

ReminderUICommon.DefaultWidgetNum = 2

function ReminderUICommon:OnRefresh(textInfos, iconPaths)
    self:SetUpText(self.reminder, textInfos)
    self:SetUpIcon(self.reminder, iconPaths)
end

-- @param reminder ReminderInfo
-- @param textInfos table 二级列表，传入要格式化显示的内容 例如{{"战士"，5},{"恭喜获得"}}
function ReminderUICommon:SetUpText(reminder, textInfos)
    local textWidgets = self:GetTextWidgets()
    if not textWidgets then 
        return
    end
    local reminderData = reminder:GetReminderTextData()
    for idx, textWidget in ipairs(textWidgets) do
        local text = ReminderUtils.ParseTextInfo(textInfos, reminderData, idx)
        if textWidget then
            self:SetTextInfo(textWidget, text)
        else
            Log.Error(">>reminder widget is not valid, reminderID:%s, text:%s, textWidget%s", reminder.ID, text, textWidget)
        end
    end
end

-- 更新文案
-- @public
function ReminderUICommon:SetTextInfo(textWidget, text)
    textWidget:SetText(text)
end

-- @param reminder ReminderInfo
-- @param iconPaths table 一级列表，传入要替换的图片路径，优先使用传入的路径 例如{"xxx", "yyy"}
function ReminderUICommon:SetUpIcon(reminder, iconPaths)
    local iconWidgets = self:GetIconWidgets()
    if not iconWidgets then return end
    local reminderData = reminder:GetReminderTextData()
    for idx, iconWidget in ipairs(iconWidgets) do
        local iconPath, iconColor = ReminderUtils.ParseIconInfo(iconPaths, reminderData, idx)
        if iconWidget and StringValid(iconPath) then
            self:SetIconInfo(iconWidget, iconPath, iconColor)
        elseif iconWidget or StringValid(iconPath) then
            Log.WarningFormat(">>reminder icon or data is not valid, reminderID:%s, iconPath:%s, iconWidget:%s", reminder.ID, iconPath, iconWidget)
        end
    end
end

-- 更新图片
-- @public
function ReminderUICommon:SetIconInfo(iconWidget, iconPath, iconColor)
    ReminderUtils.SetIcon(iconWidget, iconPath, iconColor, self)
end

function ReminderUICommon:GetTextWidgets()
    return {self.view.Text1, self.view.Text2}
end

function ReminderUICommon:GetIconWidgets()
    return {self.view.Icon1, self.view.Icon2}
end

return ReminderUICommon
