local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ReminderMain_Panel : UIPanel
---@field view ReminderMain_PanelBlueprint
local ReminderMain_Panel = DefineClass("ReminderMain_Panel", UIPanel)

-- 切场景清理子面板白名单，不希望被清理的放这里
ReminderMain_Panel.RetainCellWhitelist = {
    ["ReminderMsgNormal_Widget"] = true,
    ["ReminderMsgLight_Widget"] = true,
    ["ReminderDropList_Widget"] = true,
}

ReminderMain_Panel.eventBindMap = {
    [EEventTypesV2.OPEN_REMINDER] = "ShowReminder",
    [EEventTypesV2.CLOSE_REMINDER] = "HideReminder",
    [EEventTypesV2.LEVEL_ON_LEVEL_LOADED] = "OnSwitchScene",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReminderMain_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReminderMain_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReminderMain_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ReminderMain_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReminderMain_Panel:InitUIView()
end

---面板打开的时候触发
function ReminderMain_Panel:OnRefresh(reminder)
    if reminder then
        self:ShowReminder(reminder)
    end
end

function ReminderMain_Panel:OnHide()
    Game.ReminderManager:StopAllReminders()
end

function ReminderMain_Panel:OnShow()
    if self:IsOpened() then
        Game.ReminderManager:ResumeChannels()
    end
end

-- 是否使用同步加载
function ReminderMain_Panel:IsCellUseSyncLoad(cellId)
    local config = Game.NewUIManager:GetUIConfig(cellId)
    return self:GetPreloadResMap(config.res) ~= nil
end

-- 显示Reminder
function ReminderMain_Panel:ShowReminder(reminder)
    local cellId = reminder:GetReminderUIName()
    Log.Debug(">>ReminderMain_Panel:ShowReminder", cellId, reminder.ID, reminder.gid)
    local comp = self:GetComponentByCellId(cellId)
    if not comp and self:IsCellUseSyncLoad(cellId) then
        self:SyncLoadComponent(cellId, nil, reminder)
    else
        self:OpenComponent(cellId, nil, reminder)
    end
end

-- 关闭Reminder界面
function ReminderMain_Panel:HideReminder(reminder)
    if not Game.ReminderManager:IsReminderPlaying(reminder) then 
        Log.Warning("try hide a reminder which is not showing, ", reminder.ID, reminder.gid)
        return 
    end
    local cellId = reminder:GetReminderUIName()
    Log.Debug(">>ReminderMain_Panel:HideReminder", cellId, reminder.ID, reminder.gid)
    local comp = self:GetComponentByCellId(cellId)
    if comp then
        comp:RemoveReminder(reminder)
    end
end

function ReminderMain_Panel:OnSwitchScene()
    local data = Game.TableData.GetReminderTypeDataTable()
    for _, info in ksbcipairs(data) do
        if Game.ReminderManager:IsChannelEmpty(info.QueueType) then
            local cellName = info.UIName
            local comp = self:GetComponentByCellId(cellName)
            if comp and not self.RetainCellWhitelist[cellName] then
                self:RemoveComponent(comp)
            end
        end
    end
end

return ReminderMain_Panel
