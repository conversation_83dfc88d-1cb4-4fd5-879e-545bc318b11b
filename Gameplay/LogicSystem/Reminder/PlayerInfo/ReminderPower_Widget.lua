local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
---@class ReminderPower_Widget : ReminderUIBase
---@field view ReminderPower_WidgetBlueprint
local ReminderPower_Widget = DefineClass("ReminderPower_Widget", ReminderUIBase)
local lang = kg_require("Shared.language_" .. language)

-- C++ type
local ESlateVisibility = import("ESlateVisibility")
local equipUtils = kg_require("Shared.Utils.EquipUtils")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

ReminderPower_Widget.eventBindMap = {
}

---初始化数据
function ReminderPower_Widget:InitUIData()
    ---@type table 	-属性变化
    self.diffList = {}
    ---@type number -之前的战力
    self.preCE = nil
    ---@type number -当前的战力
    self.curCE = nil
	---@type number -显示条目最大数量
	self.MAX_COUNT = nil
	---@type table	-分批显示条目时每次显示的数量
	self.SINGLE_NUM = {}
end

--- UI组件初始化，此处为自动生成
function ReminderPower_Widget:InitUIComponent()
    ---@type UIListView
    self.PowerItemListCom = self:CreateComponent(self.view.PowerItemList, UIListView)
end

---获取Reminder结束方式
---@public
---@return number 枚举值Enum.EReminderEndType
function ReminderPower_Widget:GetEndType()
    return Enum.EReminderEndType.Timer
end

---获取显示时长
---@public
---@return number 显示时长，单位秒
function ReminderPower_Widget:GetShowDuration()
    local ms = Game.TableData.GetConstDataRow("CE_TIP_OTHER_PAGE_TIME") + Game.TableData.GetConstDataRow("CE_TIP_2ND_PAGE_TIME") + Game.TableData.GetConstDataRow("CE_TIP_1ST_PAGE_TIME")
    local duration = ms / 1000
    return duration
end

---组件刷新统一入口
function ReminderPower_Widget:OnRefresh(Params)
    if not Params then
        return
    end
    
    if Params.isMain then
        self.MAX_COUNT = 11
        self.SINGLE_NUM = {3,4,4}
    else
        self.MAX_COUNT = 12
        self.SINGLE_NUM = {4,4,4}
    end

    self:StopMyAllTimerAndAnimation()
    self:SetDiffList(Params)

    -- 处理属性变化动画
	self:PlayAnimation(self.view.WBP_ComPowerItemSpecial.Ani_Specoal_In, nil, self.view.WBP_ComPowerItemSpecial, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if Params.isAttr then
		self.view.PowerItemList:SetVisibility(ESlateVisibility.HitTestInvisible)
		self:SetPowerItemList(self.SINGLE_NUM[1])
		self:StartTimer("ReminderPower",function()
			self:RemovePowerItemList(self.SINGLE_NUM[1])
			self:SetPowerItemList(self.SINGLE_NUM[2])
			self:StartTimer("ReminderPower2",function()
				self:RemovePowerItemList(self.SINGLE_NUM[2])
				self:SetPowerItemList(self.SINGLE_NUM[3])
			end, Game.TableData.GetConstDataRow("CE_TIP_2ND_PAGE_TIME"),1)
		end, Game.TableData.GetConstDataRow("CE_TIP_1ST_PAGE_TIME"),1)
    else
        self.view.PowerItemList:SetVisibility(ESlateVisibility.Collapsed)
    end

    -- 处理战斗力数值动画
    if Params.isMain and self.curCE > self.preCE then
        self.view.WBP_ComPowerItemSpecial:SetVisibility(ESlateVisibility.HitTestInvisible)
        self.view.WBP_ComPowerItemSpecial.Text_CE:SetText(self.preCE)
        local p = (self.curCE - self.preCE) / (self.userWidget.PowerTime / self.userWidget.PowerFreq)
        local vxCE = self.preCE
		self:PowerNumFunc(p, vxCE, self.curCE)
        self.view.WBP_ComPowerItemSpecial.Text_Change:SetText("+"..(self.curCE - self.preCE))
    else
        Log.Warning("ReminderPower Params.isMain false or self.curCE < self.preCE")
        self.view.WBP_ComPowerItemSpecial:SetVisibility(ESlateVisibility.Hidden)
    end
end
function ReminderPower_Widget:PowerNumFunc(p,vxCE,curCE)
    self:StartTimer("ReminderPowerTimer", function()
        vxCE = vxCE + p
        self.view.WBP_ComPowerItemSpecial.Text_CE:SetText(math.round(vxCE))
        if vxCE >= curCE then
            self:StopTimer("ReminderPowerTimer")
            return
        end
    end, 1000*self.userWidget.PowerFreq, math.floor(self.userWidget.PowerTime / self.userWidget.PowerFreq), nil, true, function()
        self.view.WBP_ComPowerItemSpecial.Text_CE:SetText(math.round(curCE))
    end)
end

function ReminderPower_Widget:StopMyAllTimerAndAnimation()
    --self:StopAllTimer() 不能把ReminderUIBase的Timer也关了
	self:StopTimer("ReminderPower")
	self:StopTimer("ReminderPower2")
	self:StopTimer("ReminderPowerTimer")
	self:StopTimer("PowerItemList")
    self:StopAllAnimations(self.userWidget)
    self:StopAllAnimations(self.view.WBP_ComPowerItemSpecial)
end

function ReminderPower_Widget:SetDiffList(Params)
    local ProfessionID = GetMainPlayerPropertySafely("Profession")
    local playerinfoData = equipUtils.GetPlayerBattleDataRow(ProfessionID, GetMainPlayerPropertySafely("Sex") or 0)
    local JobType = 1
    if playerinfoData then
        JobType = playerinfoData.ClassPropType
    end

    local PropertyTable = nil
    if JobType == 1 then
        PropertyTable = Game.TableData.GetPhyDetailDataTable()
    else
        PropertyTable = Game.TableData.GetMagDetailDataTable()
    end

    self.preCE = Params.preCE
    self.curCE = Params.curCE
    local preDetail = Params.preDetail
    local curDetail = Params.curDetail

    -- 计算属性变化并存入 diffList
    table.clear(self.diffList)
    local PropNames = {}
    for _, value in ksbcpairs(PropertyTable) do
        if value.IsShowChange then
            if #value.ShowProperty > 1 and value.ShowProperty[1] == "Hp" and value.ShowProperty[2] == "MaxHp" then
                -- 生命特殊处理
                table.insert(PropNames, {Name = "MaxHp", Des = value.ShowPropertyName, Type = value.ShowType})
            elseif #value.ShowProperty > 1 then
                -- 攻击特殊处理
                for _, v in ksbcipairs(value.ShowProperty) do
                    if v == "mAtkMin" or v == "pAtkMin" then
                        table.insert(PropNames, {Name = v, Des = string.format(lang.CHINESE_MIN)..value.ShowPropertyName, Type = value.ShowType})
                    else
                        table.insert(PropNames, {Name = v, Des = string.format(lang.CHINESE_MAX)..value.ShowPropertyName, Type = value.ShowType})
                    end
                end
            else
                for _, v in ksbcipairs(value.ShowProperty) do
                    table.insert(PropNames, {Name = v, Des = value.ShowPropertyName, Type = value.ShowType})
                end
            end
        end
    end

    for _, attrInfo in pairs(PropNames) do
        local attr = attrInfo.Name
        if #self.diffList >= self.MAX_COUNT then
            break
        end
        if curDetail[attr] and preDetail[attr] and preDetail[attr] ~= curDetail[attr] then
            local val = curDetail[attr] - preDetail[attr]
            if (attrInfo.Type == 1 and math.round(val * 100) < 1) or (math.round(val) < 1) then
				Log.DebugFormat("val = %s", val)
            else
                table.insert(self.diffList, {attrName = attrInfo.Des, val = val, showType = attrInfo.Type})
            end
        end
    end
end

function ReminderPower_Widget:SetPowerItemList(singleNum)
    local datas = {}
    local otherInfo = {}
    for i = 1, math.min(singleNum, #self.diffList) do
        local data = self.diffList[i]
        if data then
            datas[i] = i
            otherInfo[i] = {attrName = data.attrName, valueNum = data.val, showType = data.showType}
        end
    end
    self.PowerItemListCom:Refresh(datas, 1, otherInfo)
	
	-- 步入动效
    local index = 1
    local count = math.min(singleNum, #self.diffList)
    local fadeInDuration = 40
    self:StartTimer("PowerItemList", function()
        self.PowerItemListCom:SetSelectedItemByIndex(index, true)
        index = index + 1
    end, fadeInDuration, count)
end

function ReminderPower_Widget:RemovePowerItemList(singleNum)
    for i = 1,math.min(singleNum, #self.diffList) do
        table.remove(self.diffList,1)
    end
end

return ReminderPower_Widget
