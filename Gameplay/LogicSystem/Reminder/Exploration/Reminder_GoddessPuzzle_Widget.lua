local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
---@class Reminder_GoddessPuzzle_Widget : ReminderUIBase
---@field view Reminder_GoddessPuzzle_WidgetBlueprint
local Reminder_GoddessPuzzle_Widget = DefineClass("Reminder_GoddessPuzzle_Widget", ReminderUIBase)

Reminder_GoddessPuzzle_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Reminder_GoddessPuzzle_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Reminder_GoddessPuzzle_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Reminder_GoddessPuzzle_Widget:InitUIComponent()
---@type UIListView
    self.KGListView_CountCom = self:CreateComponent(self.view.KGListView_Count, UIListView)
end

---UI事件在这里注册，此处为自动生成
function Reminder_GoddessPuzzle_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Reminder_GoddessPuzzle_Widget:InitUIView()
end

---面板打开的时候触发
function Reminder_GoddessPuzzle_Widget:OnRefresh(CurrentCount, SumCount)
	if CurrentCount == nil or SumCount == nil then
		CurrentCount = Game.GoddessExploreSystem:CheckGoddessCompleteCount()
		SumCount = Game.GoddessExploreSystem:GetGoddessCount()
	end
	
	local IconTypeList = {}
	for i=1, SumCount do
		if i<CurrentCount then
			table.insert(IconTypeList, { IconType = 1 } )
		elseif i == CurrentCount then
			table.insert(IconTypeList, { IconType = 2 })
		else
			table.insert(IconTypeList, { IconType = 0 })
		end
	end
	self.KGListView_CountCom:Refresh(IconTypeList)
end

return Reminder_GoddessPuzzle_Widget
