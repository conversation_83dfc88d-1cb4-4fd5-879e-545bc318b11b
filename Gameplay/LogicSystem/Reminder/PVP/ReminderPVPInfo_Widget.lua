local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
local ESlateVisibility = import("ESlateVisibility")
---@class ReminderPVPInfo_Widget : ReminderUIBase
---@field view ReminderPVPInfo_WidgetBlueprint
local ReminderPVPInfo_Widget = DefineClass("ReminderPVPInfo_Widget", ReminderUIBase)

ReminderPVPInfo_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReminderPVPInfo_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReminderPVPInfo_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReminderPVPInfo_Widget:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ReminderPVPInfo_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReminderPVPInfo_Widget:InitUIView()
end

---组件刷新统一入口
function ReminderPVPInfo_Widget:OnRefresh(param)
	local player = param.players and param.players[1]
	if not player then
		return 
	end
	local data = self.reminder:GetReminderTextData()
	self.view.VX_Text1:SetText(data.Text1)
	self.view.Text1:SetText(data.Text1)
	self.view.Text2:SetText(data.Text2)
	self:SetPVPPPlayerInfo(self.userWidget, player)
	local CampID = Game.TeamAreanaSystem:GetMyCampID(Game.me and Game.me.eid or 0)
	local camp = player.CampID == CampID and 0 or 1
	self.userWidget:SetCamp(camp)
	if camp == 0 then
		if data.Enum == "CONSECUTIVE_KILL" then
			self:PlayAnimation(self.userWidget.Ani_Fadein_blue, nil, self.userWidget)
		else
			self:PlayAnimation(self.userWidget.Ani_Fadein_blue_1, nil, self.userWidget)
		end
	else
		self:PlayAnimation(self.userWidget.Ani_Fadein_red, nil, self.userWidget)
	end

	self.view.HeadIcon:SetType(0, camp)
	self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	local wantedAvatarDict = Game.TeamAreanaSystem:GetWantedAvatarDict()
	self.view.HeadIcon:SetWanted(wantedAvatarDict[player.ID] or false)
end

function ReminderPVPInfo_Widget:SetPVPPPlayerInfo(widget, player)
	if not Game.me then return end
	local OptionClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(player.ProfessionID)[0]
	local CampID = Game.TeamAreanaSystem:GetMyCampID(Game.me.eid)
	local camp = player.CampID == CampID and 0 or 1
	widget:SetCamp(camp)
	self:SetImage(widget.HeadIcon.Icon, OptionClassInfo.TeamHeadIcon)
	widget.Name:SetText(player.Name)
	local wantedAvatarDict = Game.TeamAreanaSystem:GetWantedAvatarDict()
	Log.Debug(">>>SetPVPPPlayerInfo iswanted ", player.ID, wantedAvatarDict[player.ID], player.IsWanted)
	widget.HeadIcon:SetWanted(wantedAvatarDict[player.ID] or player.IsWanted or false)
end

return ReminderPVPInfo_Widget
