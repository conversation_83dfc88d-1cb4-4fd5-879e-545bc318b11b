local ReminderUICommon = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUICommon")
local ESlateVisibility = import("ESlateVisibility")
---@class ReminderChallenges_Widget : ReminderUICommon
---@field view ReminderChallenges_WidgetBlueprint
local ReminderChallenges_Widget = DefineClass("ReminderChallenges_Widget", ReminderUICommon)

ReminderChallenges_Widget.eventBindMap = {
}

function ReminderChallenges_Widget:SetTextInfo(textWidget, text)
    if textWidget == self.view.Text2 then
        if StringValid(text) then
            self.view.Canvas2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        else
            self.view.Canvas2:SetVisibility(ESlateVisibility.Collapsed)
        end
    else
        self.view.VX_Text1:SetText(text)
    end
end

return ReminderChallenges_Widget
