local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
---@class ReminderExplorationStart_Widget : ReminderUIBase
---@field view ReminderExplorationStart_WidgetBlueprint
local ReminderExplorationStart_Widget = DefineClass("ReminderExplorationStart_Widget", ReminderUIBase)

ReminderExplorationStart_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReminderExplorationStart_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReminderExplorationStart_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReminderExplorationStart_Widget:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ReminderExplorationStart_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReminderExplorationStart_Widget:InitUIView()
end

function ReminderExplorationStart_Widget:OnClose()
	Game.HUDSystem:ShowUI("HUD_MiniGame_ItemCount")
end

return ReminderExplorationStart_Widget
