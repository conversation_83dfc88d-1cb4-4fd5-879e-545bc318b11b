local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
local ReminderUtils = kg_require("Gameplay.LogicSystem.Reminder.ReminderUtils")
---@class ReminderDungeonCDM_Widget : ReminderUIBase
---@field view ReminderDungeonCDM_WidgetBlueprint
local ReminderDungeonCDM_Widget = DefineClass("ReminderDungeonCDM_Widget", ReminderUIBase)

ReminderDungeonCDM_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReminderDungeonCDM_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIview()
end

---初始化数据
function ReminderDungeonCDM_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReminderDungeonCDM_Widget:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ReminderDungeonCDM_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReminderDungeonCDM_Widget:InitUIview()
end

---组件刷新统一入口
function ReminderDungeonCDM_Widget:OnRefresh()
    local content = ReminderUtils.ParseTextInfo(self.reminder.params, self.reminder:GetReminderTextData(), 1)
    self.view.RTB_Content:SetText(content)
    local duration = self.reminder:GetDuration()
    self.Duration = duration
    self.LastingTime = duration
    if self.TimerHandle ~= nil then
        self:StopAllTimer()
    end
    self:StopTimer("ShowTime")
    self:StartTimer("ShowTime", function()
        self:ShowTime()
    end, 100, -1, nil, true)
end

function ReminderDungeonCDM_Widget:ShowTime()
    local Percent = self.LastingTime / self.Duration
    self.view.PB:SetPercent(Percent)
    self.view.Slider:SetValue(Percent)
    self.LastingTime = self.LastingTime - 0.1

    if self.LastingTime <= 0 then
        self:StopTimer("ShowTime")
    end
end

return ReminderDungeonCDM_Widget
