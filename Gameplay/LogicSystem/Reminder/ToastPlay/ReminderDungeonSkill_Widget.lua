local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
---@class ReminderDungeonSkill_Widget : ReminderUIBase
---@field view ReminderDungeonSkill_WidgetBlueprint
local ReminderDungeonSkill_Widget = DefineClass("ReminderDungeonSkill_Widget", ReminderUIBase)

ReminderDungeonSkill_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReminderDungeonSkill_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReminderDungeonSkill_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReminderDungeonSkill_Widget:InitUIComponent()
    ---@type UIComDiyTitle
    self.WBP_DIYTextCom = self:CreateComponent(self.view.WBP_DIYText, UIComDiyTitle)
end

---UI事件在这里注册，此处为自动生成
function ReminderDungeonSkill_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReminderDungeonSkill_Widget:InitUIView()
end

function ReminderDungeonSkill_Widget:OnRefresh()
	local text = self.reminder:GetReminderTextData().Text1
	if string.isEmpty(text) then
		Log.ErrorFormat(">>reminder text is not valid, reminderID:%s", self.reminder.ID)
	end
	self.WBP_DIYTextCom:Refresh(text)
	self.view.W_DIYText_First_Shadow:SetText(text)
	self.view.Img_Bg:SetOpacity(0)
end

return ReminderDungeonSkill_Widget
