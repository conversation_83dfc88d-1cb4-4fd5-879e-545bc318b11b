local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
---@class ReminderDungeonTips_Widget : ReminderUIBase
---@field view ReminderDungeonTips_WidgetBlueprint
local ReminderDungeonTips_Widget = DefineClass("ReminderDungeonTips_Widget", ReminderUIBase)

ReminderDungeonTips_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReminderDungeonTips_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReminderDungeonTips_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReminderDungeonTips_Widget:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ReminderDungeonTips_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReminderDungeonTips_Widget:InitUIView()
end

---组件刷新统一入口
function ReminderDungeonTips_Widget:OnRefresh()
	local showText = self.reminder:GetReminderTextData().Text1
	if string.isEmpty(showText) then
		Log.ErrorFormat(">>reminder text is not valid, reminderID:%s", self.reminder.ID)
	end
	self.view.RTB_Content:SetText(showText)
end

return ReminderDungeonTips_Widget
