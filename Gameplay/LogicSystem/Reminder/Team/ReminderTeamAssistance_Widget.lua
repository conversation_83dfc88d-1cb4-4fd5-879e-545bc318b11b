local ReminderUIBase = kg_require("Gameplay.LogicSystem.Reminder.Common.ReminderUIBase")
---@class ReminderTeamAssistance_Widget : ReminderUIBase
---@field view ReminderTeamAssistance_WidgetBlueprint
local ReminderTeamAssistance_Widget = DefineClass("ReminderTeamAssistance_Widget", ReminderUIBase)

ReminderTeamAssistance_Widget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReminderTeamAssistance_Widget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReminderTeamAssistance_Widget:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReminderTeamAssistance_Widget:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ReminderTeamAssistance_Widget:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReminderTeamAssistance_Widget:InitUIView()
end

---组件刷新统一入口
function ReminderTeamAssistance_Widget:OnRefresh(memberInfo)
    local headIcon = nil
    local optionClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(memberInfo.profession)
    if optionClassInfo and optionClassInfo[0] and optionClassInfo[0].SoloHeadIcon then
        headIcon = optionClassInfo[0].SoloHeadIcon
    end
    local randIdx = math.random(1, 5)
    local content = "<Chat_Orange>"..(memberInfo.name or "") .. ":</>" .. StringConst.Get("TEAM_SUPPORT_LINE_" .. randIdx)
    self.view.Text_Disc:SetText(content)
    self:SetImage(self.view.Img_Head, headIcon)
end

return ReminderTeamAssistance_Widget
