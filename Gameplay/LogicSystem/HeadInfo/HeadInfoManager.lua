kg_require "Gameplay.LogicSystem.HeadInfo.HeadInfoFlagBase"
kg_require "Gameplay.LogicSystem.HeadInfo.HeadInfoPanelData"
kg_require "Gameplay.LogicSystem.WorldWidget.WorldWidgetCellBase"
local HeadInfoConst = kg_require ("Gameplay.LogicSystem.HeadInfo.HeadInfoConst")
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
local BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID = kg_require("Shared.Const").BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID
local EHeadInfoActorTypes = HeadInfoConst.EHeadInfoActorTypes
---@class HeadInfoManager
local HeadInfoManager = DefineSingletonClass("HeadInfoManager", ManagerBase)
HeadInfoManager.EShowRules = HeadInfoConst.EShowRules
HeadInfoManager.EHeadInfoSlots = HeadInfoConst.EHeadInfoSlots
HeadInfoManager.EHeadInfoActorTypes = HeadInfoConst.EHeadInfoActorTypes
HeadInfoManager.EBubblePriority = HeadInfoConst.EBubblePriority
HeadInfoManager.ActorTypeMap = {
    [EHeadInfoActorTypes.self] = Enum.ESettingDataEnum.SelfHeader,
    [EHeadInfoActorTypes.TeammatePlayer] = Enum.ESettingDataEnum.TeamHeader,
    [EHeadInfoActorTypes.FriendlyPlayer] = Enum.ESettingDataEnum.OtherHeader,
    [EHeadInfoActorTypes.EnemyPlayer] = Enum.ESettingDataEnum.OtherHeader
}

---@type table 血条显示筛选枚举
HeadInfoManager.ActorTypeHPSettingMap = {
    [EHeadInfoActorTypes.self] = Enum.ESettingDataEnum.SelfHPShow,
    [EHeadInfoActorTypes.TeammatePlayer] = Enum.ESettingDataEnum.TeammateHpShow,
    [EHeadInfoActorTypes.FriendlyNpc] = Enum.ESettingDataEnum.FriendlyNpcHpShow,
}

function HeadInfoManager:onCtor()
    self.bShowHeadInfo = true
    self.ActorHeadInfoMap = nil
    self.ActorHeadInfoHiddenMap = nil
    self.GMDebugEnable = false
	
    self.DisplayBuffTasks = nil
    self.ShowFlagClasses = nil
    self.DebugDelayTimerMap = nil

    self.PendingRegisterQueue = {}
    self.QueueProcessTimer = nil
end

function HeadInfoManager:onInit()
    self.bShowHeadInfo = true
    self.ActorHeadInfoMap = {}
    self.ActorHeadInfoHiddenMap = {}
    self.DisplayBuffTasks = {}
    self.DebugDelayTimerMap = {}
    self.PendingRegisterQueue = {}

    self.EmptyTable = {}
    self.SoundTraceTargetUIDs = {}


    Game.EventSystem:AddListener(_G.EEventTypes.ROLE_ON_VISABLE_CHANGED, self, self.OnRoleVisibilityChanged)
    Game.EventSystem:AddListener(_G.EEventTypes.ROLE_ON_DESTROY, self, self.OnRoleDestory)
    Game.EventSystem:AddListener(_G.EEventTypes.ROLE_ON_BORN, self, self.OnRoleBorn)
    --Game.EventSystem:AddListener(_G.EEventTypes.LEVEL_ON_LEVEL_LOADED, self, self.OnLevelLoaded)

    Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF3DUI_START, self, self.OnBuffStart)
    Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF3DUI_END, self, self.OnBuffEnd)
    Game.EventSystem:AddListener(_G.EEventTypes.SERVER_ON_MSG_SHOW_TALK_BUBBLE, self, self.OnReceiveServerShowBubble)
    Game.EventSystem:AddListener(_G.EEventTypes.SERVER_ON_MSG_SHOW_CUSTOM_BUBBLE, self, self.OnReceiveServerCustomBubble)
    Game.EventSystem:AddListener(_G.EEventTypes.SOUND_TRACE_ENABLE_HEAD_INFO, self, self.OnSoundTraceChanged)

    Game.EventSystem:AddListener(_G.EEventTypes.LSCENEACTOR_ON_BORN, self, self.AddLSceneActorHeadInfo)
    Game.EventSystem:AddListener(_G.EEventTypes.LSCENEACTOR_ON_DESTROY, self, self.RemoveLSceneActorHeadInfo)
	
	Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF3DUI_PUPPET_ON_START, self, "OnPuppetStart")
	Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF3DUI_PUPPET_ON_END, self, "OnPuppetEnd")


    self.ShowFlagClasses = {}
    for K, V in pairs(HeadInfoConst.ShowFlagFileMapping) do
        self.ShowFlagClasses[K] = kg_require(V)
    end
end

function HeadInfoManager:GetActorDisplayBuffTasks(EntityID)
    local Entity = Game.EntityManager:getEntity(EntityID)
    if Entity then
        return self.DisplayBuffTasks[Entity.eid] or self.EmptyTable
    end
    return self.EmptyTable
end

function HeadInfoManager:OnBuffStart(Entity, SkillInstance, BuffInstance, ConfigID)
    local luaProfiler <close> = Game.ProfilerInstrumentation:Start(ProfilerInstrumentationConfig.HeadInfoBuffStart.name) -- 代码插桩统计

    if (Entity == nil)  or (BuffInstance == nil) then
        return
    end

    if (Game.TableData.GetOverHeadBuffDisplayDataRow(ConfigID) == nil) then
        return
    end

    local DisplayTaskData = {
        SkillInstanceID = SkillInstance and SkillInstance.InsID or nil,
        BuffInstanceID = BuffInstance and BuffInstance.InsID or nil,
        BuffAssetID = BSFunc.GetAssetID(BuffInstance),
        BuffInstigatorID = BuffInstance.instigatorID or BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID,
        ConfigID = ConfigID,
        StartTime = os.time()
    }

    self.DisplayBuffTasks[Entity.eid] = self.DisplayBuffTasks[Entity.eid] or {}
    table.insert(self.DisplayBuffTasks[Entity.eid], DisplayTaskData)
	
    if self.ActorHeadInfoMap[Entity:uid()] then
		self.ActorHeadInfoMap[Entity:uid()]:UpDateBuffDisplay()
    end
end

function HeadInfoManager:OnBuffEnd(Entity, SkillInstance, BuffInstance, ConfigID)
    local luaProfiler <close> = Game.ProfilerInstrumentation:Start(ProfilerInstrumentationConfig.HeadInfoBuffEnd.name) -- 代码插桩统计

    if (Entity == nil)  or (BuffInstance == nil) or (self.DisplayBuffTasks[Entity.eid] == nil) then
        return
    end

    if (Game.TableData.GetOverHeadBuffDisplayDataRow(ConfigID) == nil) then
        return
    end

    for idx, Task in ipairs(self.DisplayBuffTasks[Entity.eid]) do
        if (BuffInstance.InsID == Task.BuffInstanceID) and (ConfigID == Task.ConfigID) then
            table.remove(self.DisplayBuffTasks[Entity.eid], idx)
            self.DisplayBuffTasks[Entity.eid] = (#self.DisplayBuffTasks[Entity.eid] > 0) and
                    self.DisplayBuffTasks[Entity.eid] or nil

            if self.ActorHeadInfoMap[Entity:uid()] then
				self.ActorHeadInfoMap[Entity:uid()]:UpDateBuffDisplay()
            end

            break
        end
    end
end

function HeadInfoManager:OnPuppetStart(Entity, ConfigID)
	local luaProfiler <close> = Game.ProfilerInstrumentation:Start(ProfilerInstrumentationConfig.HeadInfoBuffStart.name) -- 代码插桩统计
	if Entity == nil then
		return
	end

	if Game.TableData.GetOverHeadBuffDisplayDataRow(ConfigID) == nil then
		return
	end
	
	if self.ActorHeadInfoMap[Entity:uid()] then
		self.ActorHeadInfoMap[Entity:uid()]:UpDatePuppetDisplay(ConfigID, true)
	end
end

function HeadInfoManager:OnPuppetEnd(Entity, ConfigID)
	local luaProfiler <close> = Game.ProfilerInstrumentation:Start(ProfilerInstrumentationConfig.HeadInfoBuffEnd.name) -- 代码插桩统计
	if Entity == nil then
		return
	end

	if Game.TableData.GetOverHeadBuffDisplayDataRow(ConfigID) == nil then
		return
	end
	
	if self.ActorHeadInfoMap[Entity:uid()] then
		self.ActorHeadInfoMap[Entity:uid()]:UpDatePuppetDisplay(ConfigID, false)
	end
end

function HeadInfoManager:onUnInit()
    if self.ActorHeadInfoMap then
        for K, V in pairs(self.ActorHeadInfoMap) do
            V:Uninit()
        end
    end
    self.ActorHeadInfoMap = {}
    Game.EventSystem:RemoveObjListeners(self)
    if self.QueueProcessTimer then
        Game.TimerManager:StopTimerAndKill(self.QueueProcessTimer)
    end
end

function HeadInfoManager:InnerRegisterHeadInfo(uid)
    if not self.bShowHeadInfo then
        return
    end

    if self.ActorHeadInfoMap[uid] then
        self.ActorHeadInfoMap[uid]:RefreshPanel()
        return
    end

    local HeadInfoConfig = Game.HeadInfoManager:GetHeadInfoConfig(uid)
    if HeadInfoConfig then
        local NewHeadInfoPanelData = HeadInfoPanelData.new()
        local Entity = Game.EntityManager:getEntityWithBrief(uid)        --TODO:暂时兼容
        if not Entity then
            return
        end
        NewHeadInfoPanelData:Init({ HeadInfoConfig = HeadInfoConfig,uid = uid })
        self.ActorHeadInfoMap[uid] = NewHeadInfoPanelData

        -- 检查是否已经设置为隐藏
        if (self.ActorHeadInfoHiddenMap[uid] ~= nil) then
            NewHeadInfoPanelData:ForceHideHeadInfo(true)
        end
    end
end

function HeadInfoManager:RegisterHeadInfo(uid)
    self.PendingRegisterQueue[uid] = true

    --分帧处理头顶信息
    if not self.QueueProcessTimer then
        self.QueueProcessTimer = Game.TimerManager:CreateTimerAndStart(function()
            local uid = next(self.PendingRegisterQueue)
            if uid then
                self.PendingRegisterQueue[uid] = nil
                xpcall(self.InnerRegisterHeadInfo, _G.CallBackError, self, uid)
            else
                Game.TimerManager:StopTimerAndKill(self.QueueProcessTimer)
                self.QueueProcessTimer = nil
                return
            end
        end, 10, -1)
    end
end

function HeadInfoManager:UnRegisterHeadInfo(uid)
    if self.PendingRegisterQueue[uid] then
        self.PendingRegisterQueue[uid] = nil
    end
    if self.ActorHeadInfoMap[uid] then
        self.ActorHeadInfoMap[uid]:Uninit()
        self.ActorHeadInfoMap[uid] = nil
    end
end

function HeadInfoManager:SetWorldWidgetOffset(uid, offset)
	if self.ActorHeadInfoMap[uid] then
		self.ActorHeadInfoMap[uid]:SetWorldWidgetOffset(offset)
	end
end

function HeadInfoManager:SetWorldWidgetNoDepth(uid, bNoDepth)
	if self.ActorHeadInfoMap[uid] then
		self.ActorHeadInfoMap[uid]:SetWorldWidgetNoDepth(bNoDepth)
	end
end

function HeadInfoManager:GetHeadInfoConfig(uid)
    local ActorType = self:GetHeadInfoActorType(uid)
    return HeadInfoConst.HeadInfoDisplayConfig[ActorType]
end

function HeadInfoManager:GetCampRelation(uid)
    local entity = Game.EntityManager:getEntityWithBrief(uid)
    return BSFunc.GetFinalCampRelation(Game.me, entity)
end

function HeadInfoManager:GetHeadInfoActorType(uid)
    local Entity = Game.EntityManager:getEntityWithBrief(uid)

    if not Entity then
        return EHeadInfoActorTypes.None
    end

    if Entity and Entity.HeadInfoActorType and EHeadInfoActorTypes[Entity.HeadInfoActorType] then
        return EHeadInfoActorTypes[Entity.HeadInfoActorType]
    end

    if Entity and Game.me and Entity.eid == Game.me.eid then
        --玩家自身
        return EHeadInfoActorTypes.self
    end

    if Entity.bIsLocalSceneNpc then
        return EHeadInfoActorTypes.LocalSceneNPC
    end
	if Entity.isCrowdNpc then
		return EHeadInfoActorTypes.MassAI
	end
    if Entity.isNpc then
        if Entity.NoNeedHeadInfo then
            return EHeadInfoActorTypes.None
        end
        local MonsterData = Entity:GetEntityConfigData()
        if MonsterData then
            --怪物特殊处理
            if MonsterData.TypeName == "Pet" then
                return EHeadInfoActorTypes.None
            end

            if MonsterData.TypeName == "LevelFlowSkillAgent" then
                return EHeadInfoActorTypes.None
            end

            if MonsterData.TypeName == "Carriage" then
                return EHeadInfoActorTypes.Carriage
            end
        end

        local BossType = Entity.BossType
        local CamRelation = self:GetCampRelation(uid)

        if BossType then
            if BossType == _G.Enum.EBossType.BOSS then
                return EHeadInfoActorTypes.EnemyBossMonster
            elseif BossType == _G.Enum.EBossType.Elite then
                return EHeadInfoActorTypes.EnemyEliteMonster
            elseif BossType == _G.Enum.EBossType.CREEP or BossType == _G.Enum.EBossType.Create then
                return EHeadInfoActorTypes.EnemyNormalMonster
            else
                if CamRelation == Enum.ECampEnumData.Friendly then
                    local NpcType = Entity.NpcType
                    if NpcType then
                        if NpcType == Enum.ENpcTypeData.Task then
                            return EHeadInfoActorTypes.TaskNpc
                        elseif NpcType == Enum.ENpcTypeData.Passerby then
                            return EHeadInfoActorTypes.PasserbyNpc
                        end
                    end
                    return EHeadInfoActorTypes.FriendlyNpc
                elseif CamRelation == Enum.ECampEnumData.Neutral then
                    local NpcType = Entity.NpcType
                    if NpcType then
                        if NpcType == Enum.ENpcTypeData.Task then
                            return EHeadInfoActorTypes.TaskNpc
                        elseif NpcType == Enum.ENpcTypeData.Passerby then
                            return EHeadInfoActorTypes.PasserbyNpc
                        end
                    end
                    return EHeadInfoActorTypes.NeutralNpc
                elseif CamRelation == Enum.ECampEnumData.Enemy then
                    return EHeadInfoActorTypes.EnemyNormalMonster
                end
            end
        end
    elseif Entity.isAvatar == true then
        if Game.TeamSystem:IsTeamMember(Entity.eid) or Game.GroupSystem:IsMyGroupMember(Entity.EntityID) then
            return EHeadInfoActorTypes.TeammatePlayer
        end
        local CamRelation = self:GetCampRelation(uid)

        if CamRelation == Enum.ECampEnumData.Friendly then
            return EHeadInfoActorTypes.FriendlyPlayer
        elseif CamRelation == Enum.ECampEnumData.Enemy then
            return EHeadInfoActorTypes.EnemyPlayer
        elseif CamRelation == Enum.ECampEnumData.Neutral then
            return EHeadInfoActorTypes.FriendlyPlayer
        end
    elseif Entity.ActorType == EWActorType.CHASED_MONSTER then
        return EHeadInfoActorTypes.MonsterChase
	elseif Entity.ActorType == EWActorType.OCCUPY_DETECT_AREA then
		return EHeadInfoActorTypes.OccupyDetectArea
    end

    if Entity.InsID then
        -- 新场景物体
        local LSEntity = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(Entity.InsID)
        if LSEntity then
            local ActorType = LSEntity.ActorType
            if ActorType == EWActorType.COLLECTION then
                return EHeadInfoActorTypes.NewTaskCollect
            elseif ActorType == EWActorType.GUILD_BLESS then
                return EHeadInfoActorTypes.GuildBlessActor
            elseif ActorType == EWActorType.DROP_ITEM then
                return EHeadInfoActorTypes.DropInteractor
            else
                return EHeadInfoActorTypes.NormalSceneActor
            end
        end
    end

    if Entity.bIsCommonInteractor then
        return EHeadInfoActorTypes.CommonInteractor
    end

    return EHeadInfoActorTypes.None
end

function HeadInfoManager:OnRoleBorn(_, uid)
    self:RegisterHeadInfo(uid)
end


function HeadInfoManager:OnRoleDestory(Role, uid)
    self:UnRegisterHeadInfo(uid)
end


function HeadInfoManager:OnRoleVisibilityChanged(uid, bVisible)
    if self.ActorHeadInfoMap[uid] then
        self.ActorHeadInfoMap[uid]:SetHeadInfoDisplay(HeadInfoConst.HiddenSourceType.ROLE_HIDDEN, bVisible)
    end
end

function HeadInfoManager:OnBattleHideHeadInfo(uid, bHidden)
    if self.ActorHeadInfoMap[uid] then
        self.ActorHeadInfoMap[uid]:SetHeadInfoDisplay(HeadInfoConst.HiddenSourceType.BATTLE_INFO_HIDDEN, bVisible)
    end
end

function HeadInfoManager:EnableActorGMDebug(uid, Content, Duration)
    if self.ActorHeadInfoMap[uid] then
        self.ActorHeadInfoMap[uid]:OnGMDebugToggled(true, Content, Duration)
    end
end

function HeadInfoManager:UpdateWhitelist(uid, list)
    if self.ActorHeadInfoMap[uid] then
        self.ActorHeadInfoMap[uid]:UpdateWhitelist(list)
    end
end

-- 处理气泡播放
---@param priority ,HeadInfoManager.EBubblePriority,气泡触发优先级, 默认 Common，可为nil
function HeadInfoManager:SetBubbleByActorID(templateID, bubbleID, order, cutID, bBreakable, bShowChat, endCallBack,
											randomIndex, priority)
    local HeadInfoPanelDataInst
    if templateID == -1 then
        HeadInfoPanelDataInst = self.ActorHeadInfoMap[Game.me:uid()]
    else
        for uid, Data in pairs(self.ActorHeadInfoMap) do
            local Entity = Game.EntityManager:getEntity(uid)
            if Entity and Entity.TemplateID == templateID then
                HeadInfoPanelDataInst = Data
                break
            end
        end
    end
    if not HeadInfoPanelDataInst then
        if endCallBack then
            endCallBack()
        end
        return
    end
    local cfg = Game.TableData.GetBubbleDataRow(bubbleID)
    if cfg then
        cfg = cfg[order]
    end
    if cfg then
        cfg = cfg[cutID]
    end
    if not cfg then
        if endCallBack then
            endCallBack()
        end
        return
    end
    if #cfg.BubbleText <= 0 then
        return
    end
    local bubbleText, duration, voice, anim
    if randomIndex then
        bubbleText = cfg.BubbleText[randomIndex]
        --TODO:默认1秒
        duration = cfg.Duration[randomIndex] or cfg.Duration[1] or 1000
        voice = cfg.Voice and cfg.Voice[randomIndex] or nil
        anim = cfg.Anim and cfg.Anim[randomIndex] or nil
    else
        if #cfg.BubbleText > 1 then
            randomIndex = math.random(#cfg.BubbleText)
            bubbleText = cfg.BubbleText[randomIndex]
            --TODO:默认1秒
            duration = cfg.Duration[randomIndex] or cfg.Duration[1] or 1000
            voice = cfg.Voice and cfg.Voice[randomIndex] or nil
            anim = cfg.Anim and cfg.Anim[randomIndex] or nil
        else
            bubbleText = cfg.BubbleText[1]
            duration = cfg.Duration[1] or 1000
            voice = cfg.Voice and cfg.Voice[1] or nil
            anim = cfg.Anim and cfg.Anim[1] or nil
        end
    end
    HeadInfoPanelDataInst:ShowBubbleText(bubbleText, duration, bShowChat, voice, anim, endCallBack, cfg.Type, priority)
    return duration
end

-- 处理气泡播放
---@param priority ,HeadInfoManager.EBubblePriority,气泡触发优先级, 默认 Common，可为nil
function HeadInfoManager:SetBubbleByEntityID(entityID, message, time, bShowChat, bubbleType, voice, priority, endCallBack)
    local Entity = Game.EntityManager:getEntity(entityID)

    if not Entity then
		if endCallBack then
			endCallBack()
		end
        return
    end
	
	local HeadInfoPanelDataInst = self.ActorHeadInfoMap[Entity:uid()]
    if not HeadInfoPanelDataInst then
        Log.DebugFormat("SetBubbleByEntityID: entity<%s> not found, text<%s>, time<%s>", entityID, message, time)
		if endCallBack then
			endCallBack()
		end
        return
    end
    HeadInfoPanelDataInst:ShowBubbleText(message, time, bShowChat, voice, nil, endCallBack, bubbleType, priority)
end

---@param priority ,HeadInfoManager.EBubblePriority,气泡触发优先级, 默认 Common，可为nil
function HeadInfoManager:ShowBubbleByBubbleID(eid, BubbleID, VList, DurationOverride, bShowChat, order, cutID, randomIndex, priority)
    local BubbleData = Game.TableData.GetBubbleDataRow(BubbleID)
    if not order then
        order = 1
    end
    if not cutID then
        cutID = 1
    end
    if not BubbleData or not BubbleData[order] or not BubbleData[order][cutID] then
        Log.ErrorFormat("ShowBubbleByBubbleID invalid config: BubbleID<%s>, order<%s>, cutID<%s>", BubbleID, order, cutID)
        return
    end

    BubbleData = BubbleData[order][cutID]
    local BubbleText
    local Duration = 1000
    local voice
    if randomIndex then
        BubbleText = VList and string.format(BubbleData.BubbleText[randomIndex], table.unpack(VList)) or BubbleData.BubbleText[randomIndex]
        Duration = BubbleData.Duration[randomIndex] or BubbleData.Duration[1] or 1000
        voice = BubbleData.Voice and BubbleData.Voice[randomIndex] or nil
    else
        if #BubbleData.BubbleText > 1 then
            randomIndex = math.random(#BubbleData.BubbleText)
            BubbleText = VList and string.format(BubbleData.BubbleText[randomIndex], table.unpack(VList)) or BubbleData.BubbleText[randomIndex]
            Duration = BubbleData.Duration[randomIndex] or BubbleData.Duration[1] or 1000
            voice = BubbleData.Voice and BubbleData.Voice[randomIndex] or nil
        else
            BubbleText = VList and string.format(BubbleData.BubbleText[1], table.unpack(VList)) or BubbleData.BubbleText[1]
            Duration = BubbleData.Duration[1] or 1000
            voice = BubbleData.Voice and BubbleData.Voice[1] or nil
        end
    end

    if DurationOverride and DurationOverride > 0 then
        Duration = DurationOverride * 1000
    end
    self:SetBubbleByEntityID(eid, BubbleText, Duration, bShowChat, BubbleData.Type, voice, priority)
end


function HeadInfoManager:ShowCustomBubble(eid, text, voice, time, bShowChat)
    if voice == "" then
        voice = nil
    end
    self:SetBubbleByEntityID(eid, text, time * 1000, bShowChat, 0, voice, nil)
end

function HeadInfoManager:DebugLogMsgAsBubble(eid, Msg)
	local Entity = Game.EntityManager:getEntity(eid)
	if Entity then
		self:SetBubbleByEntityID(eid, Msg, 1 * 1000, true, 0, "", nil)
	end

end

---@return HeadInfoPanelData
function HeadInfoManager:getHeadInfoPanelDataInst(templateID)
	local HeadInfoPanelDataInst
	for uid, Data in pairs(self.ActorHeadInfoMap) do
		local Entity = Game.EntityManager:getEntity(uid)
		if Entity and Entity.TemplateID == templateID then
			HeadInfoPanelDataInst = Data
			break
		end
	end
	return HeadInfoPanelDataInst
end

function HeadInfoManager:ShowCustomBubbleByTemplateID(templateID, text, voice, anim, time, bShowChat)
    local HeadInfoPanelDataInst = self:getHeadInfoPanelDataInst(templateID)
    if HeadInfoPanelDataInst then
        HeadInfoPanelDataInst:ShowBubbleText(text, time, bShowChat, voice, anim)
    end
end

function HeadInfoManager:HideCustomBubbleByTemplateID(templateID)
	local HeadInfoPanelDataInst = self:getHeadInfoPanelDataInst(templateID)
	if HeadInfoPanelDataInst then
		HeadInfoPanelDataInst:HideBubbleText()
	end
end


function HeadInfoManager:IsGMEnabled()
    return self.GMDebugEnable
end

function HeadInfoManager:ToggleGM()
    self.GMDebugEnable = not self.GMDebugEnable
    for _, PanelWrap in pairs(self.ActorHeadInfoMap) do
        PanelWrap:OnGMDebugToggled(self.GMDebugEnable)
    end
end

function HeadInfoManager:AddLSceneActorHeadInfo(UID)
    self:RegisterHeadInfo(UID)
end

function HeadInfoManager:RemoveLSceneActorHeadInfo(UID)
    self:UnRegisterHeadInfo(UID)
end

function HeadInfoManager:OnReceiveServerShowBubble(eid, BubbleID, VList, DurationOverride)
    self:ShowBubbleByBubbleID(eid, BubbleID, VList, DurationOverride)
end

function HeadInfoManager:OnReceiveServerCustomBubble(eid, text, voice, time, bShowChat)
    self:ShowCustomBubble(eid, text, voice, time, bShowChat)
end

function HeadInfoManager:OnSoundTraceChanged(TargetUID, bEnable)
    if TargetUID and TargetUID ~= 0 and bEnable then
        self.SoundTraceTargetUIDs[TargetUID] = true
    elseif TargetUID and TargetUID ~= 0 then
        self.SoundTraceTargetUIDs[TargetUID] = false
    end
    local PanelData = self.ActorHeadInfoMap[TargetUID]
    if PanelData then
        local SoundTraceFlag = PanelData.FlagInstances[HeadInfoManager.EShowRules.SoundTrace]
        if SoundTraceFlag then
            SoundTraceFlag:OnSoundTraceChanged(bEnable)
        end
    end
end

function HeadInfoManager:CreateFlagInst(FlagType)
    if self.ShowFlagClasses[FlagType] then
        return self.ShowFlagClasses[FlagType].new()
    end
end

function HeadInfoManager:OnBossTypeChanged(Ent, New, Old)
    if self.ActorHeadInfoMap[Ent:uid()] then
        self.ActorHeadInfoMap[Ent:uid()]:RefreshPanel()
    end
end

function HeadInfoManager:OnDisplayNameChanged(Ent, New, Old)
    if self.ActorHeadInfoMap[Ent:uid()] then
        self.ActorHeadInfoMap[Ent:uid()]:OnDisplayNameChanged()
    end
end

function HeadInfoManager:OnHUDVisibilityChanged(Ent, New, Old)
    if self.ActorHeadInfoMap[Ent:uid()] then
        self.ActorHeadInfoMap[Ent:uid()]:RefreshHudVisible()
    end
    Game.EventSystem:Publish(_G.EEventTypes.NPC_HUDVISIBILITY_CHANGE, Ent, New, Old)
end

function HeadInfoManager:SetWorldWidgetHiddenDis(Dist)
    for K, V in pairs(self.ActorHeadInfoMap) do
        V:SetWorldWidgetHiddenDis(Dist)
    end
end

function HeadInfoManager:OnTakeDamage(InContext)
    local TargetEntity = Game.EntityManager:getEntityWithBrief(InContext.DefenderID)
    if TargetEntity then
        local PanelWrap = self.ActorHeadInfoMap[TargetEntity:uid()]
        if PanelWrap then
            PanelWrap:OnTakeDamage(InContext)
        end
    end
end

--region Debug

function HeadInfoManager:DebugHeadInfo(uid)
    local headInfoData = self.ActorHeadInfoMap[uid]
    headInfoData:DebugDisplayRules()
    headInfoData:DebugHeadInfoOffset()
end

function HeadInfoManager:DebugSlotDisplayRules(uid, slotName)
    local headInfoData = self.ActorHeadInfoMap[uid]
    headInfoData:DebugSlotDisplayRules(slotName)
end
--endreigon
return HeadInfoManager
