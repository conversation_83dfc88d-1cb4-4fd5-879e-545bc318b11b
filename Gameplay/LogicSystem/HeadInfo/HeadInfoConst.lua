
-- luacheck: push ignore
EHeadInfoSlots = {
    HP = "HP",
    Title = "Title",
    PlayerTitle = "PlayerTitle", --玩家称号
    Name = "Name",
    Guild = "Guild",
    Icons = "Icons",
    Buffs = "Buffs",
    Bubble = "Bubble",
    PVPHonor = "PVPHonor",
    TeamSig = "TeamSig",
    GM_EntityID = "GM_EntityID",
    --GuildDance = "GuildDanceMatchType",
    SneakWarning = "SneakWarning",
    WitchOrSlave = "WitchIcons",
    DoubleCheck = "Assess",
    Captain = "Captain",
    ArenaWanted = "ArenaWanted",
    SoundTrace = "SoundTrace",
    TransferIcons = "TransferIcons",
    CountDown = "CountDown",
    Progress = "Progress",
	Puppet = "Puppet",
}

EHeadInfoActorTypes = {
    None = 0,

    self = 1,

    FriendlyPlayer = 3,
    EnemyPlayer = 4,
    TeammatePlayer = 5,

    TaskNpc = 10,
    FriendlyNpc = 11,
    NeutralNpc = 12,
    PasserbyNpc = 13,
    Carriage = 14,
    MonsterChase = 15,

    EnemyNormalMonster = 20,
    EnemyEliteMonster = 21,
    EnemyBossMonster = 22,

    DropInteractor = 30,
    GuildBlessActor = 32,
    NormalSceneActor = 34,

    LocalSceneNPC = 35,
    NewTaskCollect = 36, --新采集物
    CommonInteractor = 37, -- 通用交互物

	DirectorDisplayNpc = 40, -- 策划展示场景的Npc
	MassAI = 41, -- 氛围Npc
	OccupyDetectArea = 42, -- PVP占点区域
	PVPMagicPoint = 43, -- PVP法阵
}

EShowRules = {
    --Show Rule
    AlwaysShow = 1,
    OnSelected = 2,
    OnTakeDamage = 3,
    GMDebugToggle = 4,
    InteractorMsgTrigger = 5,
    OnDisplayBuff = 6,
    OnBattle = 7,
    OnShowBubble = 8,
    InteractorHeadIconTrigger = 9,
    OnRedName = 10,
    OnTeamHeadSig = 11,
	DropItem = 12,
    OnDisplayCountDown = 13,
    OnDisplayProgress = 14,
	OnDisplayPuppet = 15,

    ItemFlgTitle = 20,
    ItemFlgName = 21,
    ItemFlgIcon = 22,
    ItemFlgHp = 23,
    PVPHonor = 24,
    --ItemFlgGuildDance = 25,
    ItemFlgSneakWarning = 26,
    ArenaWanted = 27,

    OnWitchOrSlave = 30,
    OnDoubleCheck = 31,
    OnCaptain = 32,
    ItemFlgSceneActorName = 33,
    TaskCollectActorNameTrigger = 34,
    SoundTrace = 35,
    ItemFlgTransferIcon = 36,
    CommonInteractor = 37,
	OccupyDetectArea = 38,
	HeadInfoItemFlag_PlayerTitle = 39,
	OccupyMagicPoint = 40,
    --HideRule
    HideOnDead = 104,
    --HideOnTrace = 105,
    SetToHide = 106,
    --ConfigToHide = 107,
    HPHideSetting = 108,
    HUDVisibility = 109,
}

ShowFlagFileMapping = {
    --常驻显示
    [EShowRules.AlwaysShow] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_AlwaysShow",

    --通用flag
    [EShowRules.OnSelected] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnSelected",
    [EShowRules.OnTakeDamage] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnTakeDamage",
    [EShowRules.GMDebugToggle] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_GMDebugToggle",
    [EShowRules.OnDisplayBuff] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnDisplayBuff",
    [EShowRules.OnBattle] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnBattle",
    [EShowRules.OnShowBubble] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnShowBubble",
    [EShowRules.OnRedName] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnRedName",
    [EShowRules.OnDisplayCountDown] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnDisplayCountDown",
    [EShowRules.OnDisplayProgress] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnDisplayProgress",
	[EShowRules.OnDisplayPuppet] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnDisplayPuppet",
    -- 团队头顶标记
    [EShowRules.OnTeamHeadSig] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnTeamHeadSig",

    --交互事件
    [EShowRules.InteractorMsgTrigger] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_InteractorMsgTrigger",
    [EShowRules.InteractorHeadIconTrigger] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_InteractorHeadIconTrigger",

    --采集物事件
    [EShowRules.TaskCollectActorNameTrigger] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_TaskCollectActorNameTrigger",
    
    --通用交互物
    [EShowRules.CommonInteractor] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_CommonInteractor",
	--掉落物
	[EShowRules.DropItem] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_DropItem",

    --魔女-奴隶头顶标记
    [EShowRules.OnWitchOrSlave] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_WitchOrSlave",

    --双人鉴定
    [EShowRules.OnDoubleCheck] =
    "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnDoubleCheck",

    --队长标记
    [EShowRules.OnCaptain] =
    "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnCaptain",

    [EShowRules.SoundTrace] = 
    "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OnSoundTrace",
    [EShowRules.HeadInfoItemFlag_PlayerTitle] =  "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_PlayerTitle",
    --通过NPC身上的属性判断是否显示
    [EShowRules.HUDVisibility] =
    "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_HUDVisibility",

    --隐藏Flag
    [EShowRules.HideOnDead] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_HideOnDead",
    [EShowRules.SetToHide] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_SetToHide",
    --[EShowRules.ConfigToHide] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_ConfigToHide",
    [EShowRules.HPHideSetting] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_HPHideSetting",

    --特定 Head Item 专用显示隐藏逻辑
    [EShowRules.ItemFlgName] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_Name",
    [EShowRules.ItemFlgIcon] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_Icon",
    [EShowRules.ItemFlgTitle] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_Title",
    [EShowRules.ItemFlgHp] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_HP",

    [EShowRules.ItemFlgTransferIcon] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoIItemFlgTransfer_Icon",

    [EShowRules.PVPHonor] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_PVPHonor",
    [EShowRules.ArenaWanted] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_ArenaWanted",
    --[EShowRules.ItemFlgGuildDance] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_GuildDance",
    [EShowRules.ItemFlgSneakWarning] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_SneakWarning",
    [EShowRules.ItemFlgSceneActorName] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoItemFlag_SceneActorName",

	--占领区域的标记
	[EShowRules.OccupyDetectArea] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OccupyDetectArea",
	[EShowRules.OccupyMagicPoint] = "Gameplay.LogicSystem.HeadInfo.HeadInfoDataModules.HeadInfoFlag_OccupyMagicPoint",
}

---@brief 气泡触发优先级， Common > Cycle, 同级可互相打断
EBubblePriority = {
    Common = 0,
    Cycle = 1
}

HeadInfoDisplayConfig = {
    --====================玩家自身配置====================================
    [EHeadInfoActorTypes.self] = {
        PanelDataConfig  = {
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
					[EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.ItemFlgName] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Guild] = {
                PoolName = "/HeadInfoGuildIcon",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.ItemFlgTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PlayerTitle] = {
                PoolName = "/HeadPlayerTitle",
                ShowRule = {
                    [EShowRules.HeadInfoItemFlag_PlayerTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
			[EHeadInfoSlots.Puppet] = {
				PoolName = "/HeadPuppet",
				ShowRule = {
					[EShowRules.OnDisplayPuppet] = true,
					[EShowRules.HUDVisibility] = true,
				},
			},
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadHP",
                ShowRule = {
                    [EShowRules.OnBattle] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HPHideSetting] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PVPHonor] = {
                PoolName = "/PVPHonor",
                ShowRule = {
                    [EShowRules.PVPHonor] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.ArenaWanted] = {
                PoolName = "/ArenaWanted",
                ShowRule = {
                    [EShowRules.ArenaWanted] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.TeamSig] = {
                PoolName = "/TeamHeadSig",
                ShowRule = {
                    [EShowRules.OnTeamHeadSig] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.WitchOrSlave] = {
                PoolName = "/WitchOrSlave",
                ShowRule = {
                    [EShowRules.OnWitchOrSlave] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.DoubleCheck] = {
                PoolName = "/DoubleCheck",
                ShowRule = {
                    [EShowRules.OnDoubleCheck] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Captain] = {
                PoolName = "/Captain",
                ShowRule = {
                    [EShowRules.OnCaptain] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },            
            [EHeadInfoSlots.Progress] = {
                PoolName = "/Progress",
                ShowRule = {
                    [EShowRules.OnDisplayProgress] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.CountDown] = {
                PoolName = "/HeadCountDown",
                ShowRule = {
                    [EShowRules.OnDisplayCountDown] = true,
					[EShowRules.HUDVisibility] = true,
                }
            }

        },
        ActorType = EHeadInfoActorTypes.self,
        BindSocketOverride = "pelvis",
        bAutoSetWorldOffsetToHead = true,
        HiddenDistanceOverride = 10000,
    },
    --====================友方玩家配置====================================
    [EHeadInfoActorTypes.FriendlyPlayer] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.ItemFlgName] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Guild] = {
                PoolName = "/HeadInfoGuildIcon",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
					--https://gamecloud-redmine.corp.kuaishou.com/1007/projects/c7/issues/152822
					-- 变身会使用HUDVisiblity功能来进行公会/团队/小队的图标隐藏控制
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
					[EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.ItemFlgTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PlayerTitle] = {
                PoolName = "/HeadPlayerTitle",
                ShowRule = {
                    [EShowRules.HeadInfoItemFlag_PlayerTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadHP",
                ShowRule = {
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.OnRedName] = true,
                    [EShowRules.HideOnDead] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
			[EHeadInfoSlots.Puppet] = {
				PoolName = "/HeadPuppet",
				ShowRule = {
					[EShowRules.OnDisplayPuppet] = true,
					[EShowRules.HUDVisibility] = true,
				},
			},
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PVPHonor] = {
                PoolName = "/PVPHonor",
                ShowRule = {
                    [EShowRules.PVPHonor] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.ArenaWanted] = {
                PoolName = "/ArenaWanted",
                ShowRule = {
                    [EShowRules.ArenaWanted] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.TeamSig] = {
                PoolName = "/TeamHeadSig",
                ShowRule = {
                    [EShowRules.OnTeamHeadSig] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.WitchOrSlave] = {
                PoolName = "/WitchOrSlave",
                ShowRule = {
                    [EShowRules.OnWitchOrSlave] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.DoubleCheck] = {
                PoolName = "/DoubleCheck",
                ShowRule = {
                    [EShowRules.OnDoubleCheck] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Progress] = {
                PoolName = "/Progress",
                ShowRule = {
                    [EShowRules.OnDisplayProgress] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.Captain] = {
                PoolName = "/Captain",
                ShowRule = {
                    [EShowRules.OnCaptain] = true,
					[EShowRules.HUDVisibility] = true,
                },
            }
        },
        ActorType = EHeadInfoActorTypes.FriendlyPlayer,
        BindSocketOverride = "pelvis",
        bAutoSetWorldOffsetToHead = true,
        HiddenDistanceOverride = 10000,
    },
    --====================队友玩家配置====================================
    [EHeadInfoActorTypes.TeammatePlayer] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.ItemFlgName] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Guild] = {
                PoolName = "/HeadInfoGuildIcon",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
					[EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.ItemFlgTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PlayerTitle] = {
                PoolName = "/HeadPlayerTitle",
                ShowRule = {
                    [EShowRules.HeadInfoItemFlag_PlayerTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadHP",
                ShowRule = {
                    [EShowRules.OnSelected] = true,
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.OnBattle] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HPHideSetting] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
			[EHeadInfoSlots.Puppet] = {
				PoolName = "/HeadPuppet",
				ShowRule = {
					[EShowRules.OnDisplayPuppet] = true,
					[EShowRules.HUDVisibility] = true,
				},
			},
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PVPHonor] = {
                PoolName = "/PVPHonor",
                ShowRule = {
                    [EShowRules.PVPHonor] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.ArenaWanted] = {
                PoolName = "/ArenaWanted",
                ShowRule = {
                    [EShowRules.ArenaWanted] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.TeamSig] = {
                PoolName = "/TeamHeadSig",
                ShowRule = {
                    [EShowRules.OnTeamHeadSig] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.WitchOrSlave] = {
                PoolName = "/WitchOrSlave",
                ShowRule = {
                    [EShowRules.OnWitchOrSlave] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.DoubleCheck] = {
                PoolName = "/DoubleCheck",
                ShowRule = {
                    [EShowRules.OnDoubleCheck] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Captain] = {
                PoolName = "/Captain",
                ShowRule = {
                    [EShowRules.OnCaptain] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Progress] = {
                PoolName = "/Progress",
                ShowRule = {
                    [EShowRules.OnDisplayProgress] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
        },
        ActorType = EHeadInfoActorTypes.TeammatePlayer,
        BindSocketOverride = "pelvis",
        bAutoSetWorldOffsetToHead = true,
        HiddenDistanceOverride = 10000,
    },
    -- --====================敌对玩家配置====================================
    [EHeadInfoActorTypes.EnemyPlayer] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.ItemFlgName] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Guild] = {
                PoolName = "/HeadInfoGuildIcon",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
					[EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.SetToHide] = true,
                    [EShowRules.ItemFlgTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PlayerTitle] = {
                PoolName = "/HeadPlayerTitle",
                ShowRule = {
                    [EShowRules.HeadInfoItemFlag_PlayerTitle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadHP",
                ShowRule = {
                    [EShowRules.OnSelected] = true,
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.HideOnDead] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.PVPHonor] = {
                PoolName = "/PVPHonor",
                ShowRule = {
                    [EShowRules.PVPHonor] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.ArenaWanted] = {
                PoolName = "/ArenaWanted",
                ShowRule = {
                    [EShowRules.ArenaWanted] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.WitchOrSlave] = {
                PoolName = "/WitchOrSlave",
                ShowRule = {
                    [EShowRules.OnWitchOrSlave] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.DoubleCheck] = {
                PoolName = "/DoubleCheck",
                ShowRule = {
                    [EShowRules.OnDoubleCheck] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Captain] = {
                PoolName = "/Captain",
                ShowRule = {
                    [EShowRules.OnCaptain] = true,
					[EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Progress] = {
                PoolName = "/Progress",
                ShowRule = {
                    [EShowRules.OnDisplayProgress] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
        },
        ActorType = EHeadInfoActorTypes.EnemyPlayer,
        BindSocketOverride = "pelvis",
        bAutoSetWorldOffsetToHead = true,
        HiddenDistanceOverride = 10000,
    },
    -- --====================友方NPC配置====================================
    [EHeadInfoActorTypes.FriendlyNpc] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.ItemFlgName] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadHP",
                ShowRule = {
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.ItemFlgHp] = true,
                    [EShowRules.HPHideSetting] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
			[EHeadInfoSlots.Puppet] = {
				PoolName = "/HeadPuppet",
				ShowRule = {
					[EShowRules.OnDisplayPuppet] = true,
					[EShowRules.HUDVisibility] = true,
				},
			},
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.SoundTrace] = {
                PoolName = "/HeadSoundTrace",
                ShowRule = {
                    [EShowRules.SoundTrace] = true,
					[EShowRules.HUDVisibility] = true,
                }
            }
        },
        ActorType = EHeadInfoActorTypes.FriendlyNpc,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 10000,
    },
    -- --====================中立NPC配置====================================
    [EHeadInfoActorTypes.NeutralNpc] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.ItemFlgName] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadHP",
                ShowRule = {
                    [EShowRules.OnSelected] = true,
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.ItemFlgHp] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.SoundTrace] = {
                PoolName = "/HeadSoundTrace",
                ShowRule = {
                    [EShowRules.SoundTrace] = true,
					[EShowRules.HUDVisibility] = true,
                }
            }
        },
        ActorType = EHeadInfoActorTypes.NeutralNpc,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 10000,
    },
    --====================普通怪物配置====================================
    [EHeadInfoActorTypes.EnemyNormalMonster] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.ItemFlgTitle] = true,
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadBossHP",
                ShowRule = {
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.ItemFlgHp] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.SneakWarning] = {
                PoolName = "/HeadSneakWarning",
                ShowRule = {
                    [EShowRules.ItemFlgSneakWarning] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.CountDown] = {
                PoolName = "/HeadCountDown",
                ShowRule = {
                    [EShowRules.OnDisplayCountDown] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.Progress] = {
                PoolName = "/Progress",
                ShowRule = {
                    [EShowRules.OnDisplayProgress] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
        },
        ActorType = EHeadInfoActorTypes.EnemyNormalMonster,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 10000,
    },
    --====================精英怪物配置====================================
    [EHeadInfoActorTypes.EnemyEliteMonster] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.ItemFlgTitle] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadBossHP",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.OnTakeDamage] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.ItemFlgHp] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.CountDown] = {
                PoolName = "/HeadCountDown",
                ShowRule = {
                    [EShowRules.OnDisplayCountDown] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Progress] = {
                PoolName = "/Progress",
                ShowRule = {
                    [EShowRules.OnDisplayProgress] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
        },
        --WorldOffsetOverride    = FVector(0, 0, 120),
        ActorType = EHeadInfoActorTypes.EnemyEliteMonster,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 10000,
    },
    -- --====================Boss怪物配置====================================
    [EHeadInfoActorTypes.EnemyBossMonster] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Buffs] = {
                PoolName = "/HeadBuff",
                ShowRule = {
                    [EShowRules.OnDisplayBuff] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.HP] = {
                PoolName = "/HeadBossHP",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.OnSelected] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.ItemFlgTitle] = true,
                    [EShowRules.HideOnDead] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.SneakWarning] = {
                PoolName = "/HeadSneakWarning",
                ShowRule = {
                    [EShowRules.ItemFlgSneakWarning] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.CountDown] = {
                PoolName = "/HeadCountDown",
                ShowRule = {
                    [EShowRules.OnDisplayCountDown] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.Progress] = {
                PoolName = "/Progress",
                ShowRule = {
                    [EShowRules.OnDisplayProgress] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
        },
        ActorType = EHeadInfoActorTypes.EnemyBossMonster,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 10000,
    },
    --====================任务NPC配置====================================
    [EHeadInfoActorTypes.TaskNpc] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.ItemFlgName] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Title] = {
                PoolName = "/HeadTitle",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.ItemFlgTitle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.Icons] = {
                PoolName = "/HeadIcon",
                ShowRule = {
                    [EShowRules.ItemFlgIcon] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.SneakWarning] = {
                PoolName = "/HeadSneakWarning",
                ShowRule = {
                    [EShowRules.ItemFlgSneakWarning] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.SoundTrace] = {
                PoolName = "/HeadSoundTrace",
                ShowRule = {
                    [EShowRules.SoundTrace] = true,
					[EShowRules.HUDVisibility] = true,
                }
            },
            [EHeadInfoSlots.TransferIcons] = {
                PoolName = "/HeadTransferIcon",
                ShowRule = {
                    [EShowRules.ItemFlgTransferIcon] = true,
					[EShowRules.HUDVisibility] = true,
                },
                bNeedDistanceUpdate = true
            },
        },
        bAutoSetWorldOffsetToHead = true,
        ActorType = EHeadInfoActorTypes.TaskNpc,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = -1,  -- 由Npc的AOI范围决定
    },
    [EHeadInfoActorTypes.MonsterChase] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
					[EShowRules.HUDVisibility] = true,
                },
            }
        },
        ActorType = EHeadInfoActorTypes.MonsterChase,
        HiddenDistanceOverride = 10000,
        WorldOffsetOverride = FVector(0, 0, 50),
    },
    --====================路人npc配置====================================
    [EHeadInfoActorTypes.PasserbyNpc] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                    [EShowRules.HUDVisibility] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                    [EShowRules.HUDVisibility] = true,
                },
            },
            [EHeadInfoSlots.SoundTrace] = {
                PoolName = "/HeadSoundTrace",
                ShowRule = {
                    [EShowRules.SoundTrace] = true
                }
            }
        },
        ActorType = EHeadInfoActorTypes.PasserbyNpc,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 10000,
    },
	--====================路人npc配置====================================
	[EHeadInfoActorTypes.MassAI] = {
		PanelDataConfig = {
			[EHeadInfoSlots.Bubble] = {
				PoolName = "/HeadBubble",
				ShowRule = {
					[EShowRules.OnShowBubble] = true,
					[EShowRules.HUDVisibility] = true,
				},
				--bNeedDistanceUpdate = true
			},
			[EHeadInfoSlots.GM_EntityID] = {
				PoolName = "/GM/GMHeadDebugInfo",
				ShowRule = {
					[EShowRules.GMDebugToggle] = true,
					[EShowRules.HUDVisibility] = true,
				},
			},
		},
		ActorType = EHeadInfoActorTypes.MassAI,
		BindSocketOverride = "pelvis",
		HiddenDistanceOverride = 10000,
	},
    --====================马车配置====================================
    [EHeadInfoActorTypes.Carriage] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/HeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                    [EShowRules.ItemFlgName] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                },
            },
        },
        ActorType = EHeadInfoActorTypes.Carriage,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 10000,
    },
    --====================策划展示场景的Npc====================================
    [EHeadInfoActorTypes.DirectorDisplayNpc] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/DirectorDisplayNpcHeadName",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                },
            },
        },
        ActorType = EHeadInfoActorTypes.DirectorDisplayNpc,
        BindSocketOverride = "pelvis",
        HiddenDistanceOverride = 2000,
    },
    --====================掉落物配置====================================
    [EHeadInfoActorTypes.DropInteractor] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/DropItemInterActorHeadName",
                ShowRule = {
                    [EShowRules.DropItem] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                },
                --bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                },
            }
        },
        ActorType = EHeadInfoActorTypes.DropInteractor,
        HiddenDistanceOverride = 3000,
    },
    --====================工会祈福雕像====================================
    [EHeadInfoActorTypes.GuildBlessActor] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Icons] = {
                PoolName = "/HeadIcon",
                ShowRule = {
                    [EShowRules.AlwaysShow] = true,
                },
                bNeedDistanceUpdate = true
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                },
            }
        },
        WorldOffsetOverride = FVector(0, 0, 250),
        ActorType = EHeadInfoActorTypes.GuildBlessActor,
        HiddenDistanceOverride = 10000,
    },

    --====================场景物体配置====================================
    [EHeadInfoActorTypes.NormalSceneActor] = {
        PanelDataConfig = {
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                },
            },
        },
        ActorType = EHeadInfoActorTypes.NormalSceneActor,
        HiddenDistanceOverride = 3000,
        WorldOffsetOverride    = FVector(0, 0, 200),
    },



    --====================场景NPC配置====================================
    [EHeadInfoActorTypes.LocalSceneNPC] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/SceneActorName",
                ShowRule = {
                    [EShowRules.ItemFlgSceneActorName] = true,
                },
            },
            [EHeadInfoSlots.Bubble] = {
                PoolName = "/HeadBubble",
                ShowRule = {
                    [EShowRules.OnShowBubble] = true,
                },
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                },
            }
        },
        ActorType = EHeadInfoActorTypes.LocalSceneNPC,
		BindSocketOverride = "pelvis",
		bAutoSetWorldOffsetToHead = true,
        HiddenDistanceOverride = 10000,
    },

    --====================新采集物配置====================================
    [EHeadInfoActorTypes.NewTaskCollect] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/TaskCollectHeadName",
                ShowRule = {
                    [EShowRules.TaskCollectActorNameTrigger] = true,
                },
            },
			[EHeadInfoSlots.Title] = {
				PoolName = "/TaskCollectHeadTitle",
				ShowRule = {
					[EShowRules.TaskCollectActorNameTrigger] = true,
				},
			},
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                },
            }
        },
        ActorType = EHeadInfoActorTypes.NewTaskCollect,
        HiddenDistanceOverride = 10000,
    },

    --====================通用交互物配置====================================
    [EHeadInfoActorTypes.CommonInteractor] = {
        PanelDataConfig = {
            [EHeadInfoSlots.Name] = {
                PoolName = "/CommonInteractorHeadName",
                ShowRule = {
                    [EShowRules.CommonInteractor] = true,
                },
            },
            [EHeadInfoSlots.GM_EntityID] = {
                PoolName = "/GM/GMHeadDebugInfo",
                ShowRule = {
                    [EShowRules.GMDebugToggle] = true,
                },
            }
        },
        ActorType = EHeadInfoActorTypes.CommonInteractor,
        HiddenDistanceOverride = 10000,
    },
	--====================PVP占点提示配置====================================
	--OccupyDetectArea
	[EHeadInfoActorTypes.OccupyDetectArea] = {
		PanelDataConfig = {
			[EHeadInfoSlots.Name] = {
				PoolName = "/OccupyDetectArea",
				ShowRule = {
					[EShowRules.OccupyDetectArea] = true,
				},
			},
			[EHeadInfoSlots.GM_EntityID] = {
				PoolName = "/GM/GMHeadDebugInfo",
				ShowRule = {
					[EShowRules.GMDebugToggle] = true,
				},
			},
		},
		ActorType = EHeadInfoActorTypes.OccupyDetectArea,
		HiddenDistanceOverride = 10000,
	},
	[EHeadInfoActorTypes.PVPMagicPoint] = {
		PanelDataConfig = {
			[EHeadInfoSlots.CountDown] = {
				PoolName = "/OccupyMagicPoint",
				ShowRule = {
					[EShowRules.OccupyMagicPoint] = true,
				}
			},
			[EHeadInfoSlots.GM_EntityID] = {
				PoolName = "/GM/GMHeadDebugInfo",
				ShowRule = {
					[EShowRules.GMDebugToggle] = true,
				},
			},
		},
		ActorType = EHeadInfoActorTypes.PVPMagicPoint,
		HiddenDistanceOverride = 10000,
	},
    --====================无信息模板配置====================================
    [EHeadInfoActorTypes.None] = {
        PanelDataConfig = {},
        ActorType = EHeadInfoActorTypes.None,
        HiddenDistanceOverride = 10000,
    }
}

HiddenSourceType = 
{
    CHILD = 1,    -- 是否有子节点
    DISTANCE = 2, -- 距离太远
    ROLE_HIDDEN = 3, --角色隐藏
    BATTLE_INFO_HIDDEN = 4, --战斗逻辑隐藏
}

-- luacheck: pop