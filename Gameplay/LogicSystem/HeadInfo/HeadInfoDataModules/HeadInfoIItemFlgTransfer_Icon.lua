local HeadInfoIItemFlgTransfer_Icon = DefineClass("HeadInfoIItemFlgTransfer_Icon",HeadInfoFlagBase)

function HeadInfoIItemFlgTransfer_Icon:ctor()
	self.bHasIcon = nil
end

function HeadInfoIItemFlgTransfer_Icon:OnInit(Parent)
	local RpcEntity = self:GetAttachedEntity()
	local TemplateID = RpcEntity.TemplateID
	local bHasIcon = Game.NPCSystem:IsNpcHasIcon(TemplateID)
	if bHasIcon then
		self.bHasIcon = false
		return
	end
	local teleportID = RpcEntity:GetForTeleportInsID()
	self.bHasIcon = teleportID and true or false
end

function HeadInfoIItemFlgTransfer_Icon:OnUnInit()
	self.bHasIcon = nil
end

function HeadInfoIItemFlgTransfer_Icon:OnNpcTalkChanged(npcCfgID)
	local RpcEntity = self:GetAttachedEntity()
	if not RpcEntity then
		return
	end
	local TemplateID = RpcEntity.TemplateID
	if  TemplateID and  npcCfgID == TemplateID then
		local bHasNpcIcon = Game.NPCSystem:IsNpcHasIcon(TemplateID)
		local bHastransIcon
		if bHasNpcIcon then
			bHastransIcon = false
		else
			local teleportID = RpcEntity:GetForTeleportInsID()
			bHastransIcon = teleportID and true or false
		end 
		if self.bHasIcon ~= bHastransIcon then
			--状态改变
			self.bHasIcon = bHastransIcon
			local Parent = self:GetParent()
			Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.ItemFlgTransferIcon)
			Parent:OnMessage("OnNpcTalkChanged", npcCfgID)
		end
	end
end

function HeadInfoIItemFlgTransfer_Icon:OnBindEvents()
	Game.GlobalEventSystem:AddListener(EEventTypesV2.NPC_TALK_CHANGED, "OnNpcTalkChanged", self)
end

function HeadInfoIItemFlgTransfer_Icon:OnUnBindEvents()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end


function HeadInfoIItemFlgTransfer_Icon:ShouldShow()
	return self.bHasIcon
end

function HeadInfoIItemFlgTransfer_Icon:ShouldForceToHide()
	return not self.bHasIcon
end


return HeadInfoIItemFlgTransfer_Icon
