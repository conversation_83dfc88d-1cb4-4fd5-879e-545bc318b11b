local HeadInfoItemFlag_HP= DefineClass("HeadInfoItemFlag_HP",HeadInfoFlagBase)

function HeadInfoItemFlag_HP:ctor()
	self.ConfigToShow = nil
end

function HeadInfoItemFlag_HP:OnInit(Parent)
	self.ConfigToShow = false
	local RpcEntity = self:GetAttachedEntity()
	if  RpcEntity then
		if RpcEntity.isNpc == true then
			local MonsterData = RpcEntity:GetEntityConfigData()
			if MonsterData and MonsterData.HpBarResidentDisplay then
				self.ConfigToShow = true
			end
		end
	end

end


function HeadInfoItemFlag_HP:OnUnInit()
	self.HasName = nil
end

function HeadInfoItemFlag_HP:OnBindEvents()
end

function HeadInfoItemFlag_HP:OnUnBindEvents()
end


function HeadInfoItemFlag_HP:ShouldShow()
	return self.ConfigToShow
end

function HeadInfoItemFlag_HP:ShouldForceToHide()
	return false
end


return HeadInfoItemFlag_HP
