local HeadInfoFlag_WitchOrSlave = DefineClass("HeadInfoFlag_WitchOrSlave", HeadInfoFlagBase)



function HeadInfoFlag_WitchOrSlave:ctor()
    self.OnSlave = false
end

function HeadInfoFlag_WitchOrSlave:OnInit(Parent)
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity then
        if RpcEntity.isWitchSlave then
            self.OnSlave = true
        end
    end
end

function HeadInfoFlag_WitchOrSlave:OnUnInit()
    self.OnSlave = nil
end

function HeadInfoFlag_WitchOrSlave:OnBindEvents()
end

function HeadInfoFlag_WitchOrSlave:OnUnBindEvents()
    Game.EventSystem:RemoveObjListeners(self)
end

function HeadInfoFlag_WitchOrSlave:OnIsSlaveStateChanged()
    local Parent = self:GetParent()
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity then
        self.OnSlave = RpcEntity.isWitchSlave
        Parent:OnFlagChanged()
        Parent:OnMessage("OnIsSlaveChanged", RpcEntity.isWitchSlave)
    end
end

function HeadInfoFlag_WitchOrSlave:ShouldShow()
    return self.OnSlave
end

function HeadInfoFlag_WitchOrSlave:ShouldForceToHide()
    -- return self.IsWitchOrSlave
end

return HeadInfoFlag_WitchOrSlave
