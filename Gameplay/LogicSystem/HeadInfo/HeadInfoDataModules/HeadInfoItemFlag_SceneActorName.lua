local HeadInfoItemFlag_SceneActorName= DefineClass("HeadInfoItemFlag_SceneActorName",HeadInfoFlagBase)

function HeadInfoItemFlag_SceneActorName:ctor()
	self.HasName = nil

end

function HeadInfoItemFlag_SceneActorName:OnInit(Parent)
	self.HasName = false
	local RpcEntity = self:GetAttachedEntity()
	if RpcEntity and RpcEntity.GetDisplayName then
		self.HasName = RpcEntity:GetDisplayName() ~= nil
	end
end

function HeadInfoItemFlag_SceneActorName:OnUnInit()
	self.HasName = nil
end

function HeadInfoItemFlag_SceneActorName:OnBindEvents()
end

function HeadInfoItemFlag_SceneActorName:OnUnBindEvents()
end


function HeadInfoItemFlag_SceneActorName:ShouldShow()
	return true
end

function HeadInfoItemFlag_SceneActorName:ShouldForceToHide()
	return not self.HasName
end


return HeadInfoItemFlag_SceneActorName
