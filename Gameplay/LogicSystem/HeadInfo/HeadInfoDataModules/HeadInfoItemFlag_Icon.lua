local HeadInfoItemFlag_Icon= DefineClass("HeadInfoItemFlag_Icon",HeadInfoFlagBase)

function HeadInfoItemFlag_Icon:ctor()
	self.bHasIcon = nil
end

function HeadInfoItemFlag_Icon:OnInit(Parent)
	local RpcEntity = self:GetAttachedEntity()
	local TemplateID = RpcEntity.TemplateID
	self.bHasIcon = Game.NPCSystem:IsNpcHasIcon(TemplateID, RpcEntity.InstanceID)
end

function HeadInfoItemFlag_Icon:OnUnInit()
	self.bHasIcon = nil
end

function HeadInfoItemFlag_Icon:OnNpcTalkChanged(npcCfgID)
	local RpcEntity = self:GetAttachedEntity()
	if not RpcEntity then
		return
	end
	local TemplateID = RpcEntity.TemplateID
	if  TemplateID and  npcCfgID == TemplateID then
		local bHasIcon =  Game.NPCSystem:IsNpcHasIcon(TemplateID)
		if bHasIcon ~= self.bHasIcon then
			self.bHasIcon = bHasIcon
			local Parent = self:GetParent()
			Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.OnHasIcon)
		end
		local Parent = self:GetParent()
		Parent:OnMessage("OnNpcTalkChanged",npcCfgID)
	end
end

function HeadInfoItemFlag_Icon:OnAdditionSkillChange()
	local RpcEntity = self:GetAttachedEntity()
	if not RpcEntity then
		return
	end
	local TemplateID = RpcEntity.TemplateID
	local bHasIcon =  Game.NPCSystem:IsNpcHasIcon(TemplateID)
	if bHasIcon ~= self.bHasIcon then
		self.bHasIcon = bHasIcon
		local Parent = self:GetParent()
		Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.OnHasIcon)
	end
end

function HeadInfoItemFlag_Icon:OnBindEvents()
	Game.GlobalEventSystem:AddListener(EEventTypesV2.NPC_TALK_CHANGED, "OnNpcTalkChanged", self)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.QUEST_ON_TARGET_FINISHED, "OnQuestTargetFinish", self)
	Game.EventSystem:AddListener(_G.EEventTypes.On_ADDITION_SKILL_ADD, self, self.OnAdditionSkillChange)
	Game.EventSystem:AddListener(_G.EEventTypes.On_ADDITION_SKILL_CD_BEGIN, self, self.OnAdditionSkillChange)
	Game.EventSystem:AddListener(_G.EEventTypes.On_ADDITION_SKILL_FINISH, self, self.OnAdditionSkillChange)
end

function HeadInfoItemFlag_Icon:OnUnBindEvents()
	Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end


function HeadInfoItemFlag_Icon:ShouldShow()
	return self.bHasIcon
end

function HeadInfoItemFlag_Icon:ShouldForceToHide()
	return not self.bHasIcon
end

function HeadInfoItemFlag_Icon:OnQuestTargetFinish()
	local RpcEntity = self:GetAttachedEntity()
	if RpcEntity then
		self:OnNpcTalkChanged(RpcEntity.TemplateID)
	end
end

return HeadInfoItemFlag_Icon
