--local p_teammessageview = require("Gameplay.LogicSystem.Team.P_TeamMessageView")
local shareConst = kg_require("Shared.Const")
---根据Entity身上刮的属性HUDVisibility判断是否显示
local HeadInfoItemFlag_HUDVisibility = DefineClass("HeadInfoItemFlag_HUDVisibility", HeadInfoFlagBase)

function HeadInfoItemFlag_HUDVisibility:OnBindEvents()
	Game.UniqEventSystemMgr:AddListener(self.EntityID, EEventTypesV2.NPC_HUDVISIBILITY_CHANGE, "OnHUDVisibilityChanged", self)
end

function HeadInfoItemFlag_HUDVisibility:OnUnBindEvents()
	Game.UniqEventSystemMgr:RemoveListener(self.EntityID, EEventTypesV2.NPC_HUDVISIBILITY_CHANGE, "OnHUDVisibilityChanged", self)
end

function HeadInfoItemFlag_HUDVisibility:OnHUDVisibilityChanged(New, Old)
    local Parent = self:GetParent()
    Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.HUDVisibility)
end

function HeadInfoItemFlag_HUDVisibility:ShouldShow(SlotID)
    return false
end

function  HeadInfoItemFlag_HUDVisibility:ShouldForceToHide(SlotID)
    local RpcEntity = self:GetAttachedEntity()
    if not RpcEntity then
        return true
    end
	local Numeric = shareConst.HEADINFO_TYPE[SlotID]
	if not Numeric then
		return false
	end
    if not RpcEntity.HUDVisibility or not next(RpcEntity.HUDVisibility) then--未配置过，走表格
        return self:ShouldToHideByData(RpcEntity, Numeric)
    end
    if RpcEntity.HUDVisibility[Numeric] == false then
        return true
	elseif RpcEntity.HUDVisibility[Numeric] == true then
		return false
	else
		-- 如果没有明确设置为true或false，则走表格数据
		return self:ShouldToHideByData(RpcEntity, Numeric)
    end
end

function HeadInfoItemFlag_HUDVisibility:ShouldToHideByData(RpcEntity, Numeric)
	if RpcEntity and RpcEntity.isNpc  == true then
		local NPCData = RpcEntity:GetEntityConfigData()
		if NPCData and NPCData.HideNpcHUDList then
			for _, Type in ksbcpairs(NPCData.HideNpcHUDList) do
				if Type == Numeric then
					return true
				end
			end
		end
	end
	return false
end

return HeadInfoItemFlag_HUDVisibility
