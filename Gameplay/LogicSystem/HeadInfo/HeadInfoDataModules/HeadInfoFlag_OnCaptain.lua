local HeadInfoFlag_OnCaptain = DefineClass("HeadInfoFlag_OnCaptain", HeadInfoFlagBase)



function HeadInfoFlag_OnCaptain:ctor()
    self.RoleType = Enum.ETeamGroupRole.None
end

function HeadInfoFlag_OnCaptain:OnInit(Parent)
   self:Refresh()
end

function HeadInfoFlag_OnCaptain:OnUnInit()
end

function HeadInfoFlag_OnCaptain:OnBindEvents()
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity.eid then
        Game.EventSystem:AddListener(_G.EEventTypes.ON_TEAMID_CHANGED, self, self.OnTeamIDChange, RpcEntity.eid)  
        if RpcEntity.eid ~= Game.me.eid then
            Game.EventSystem:AddListener(_G.EEventTypes.ON_TEAMID_CHANGED, self, self.OnTeamIDChange, Game.me.eid)  
        end
        
        Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_CAPTAIN_CHANGED, self, self.OnIsCaptainChange, RpcEntity.eid)
        if RpcEntity.eid ~= Game.me.eid then
            Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_CAPTAIN_CHANGED, self, self.OnIsCaptainChange, Game.me.eid)
        end
        
        if RpcEntity.eid ~= Game.me.eid and RpcEntity.groupID == Game.me.groupID then
            Game.EventSystem:AddListener(_G.EEventTypes.ON_MAINPLAYER_GROUP_ID_CHANGED, self, self.OnGroupIDChange, Game.me.eid)
        end
        Game.EventSystem:AddListener(_G.EEventTypes.ON_MAINPLAYER_GROUP_ID_CHANGED, self, self.OnGroupIDChange, RpcEntity.eid)
        Game.GlobalEventSystem:AddListener(EEventTypesV2.CLIENT_GROUP_TEAMLEADER_UPDATE, self, self.OnGroupTeamLeaderChange)     
        Game.GlobalEventSystem:AddListener(EEventTypesV2.GROUP_LEADER_CHANGED, "OnGroupLeaderChange", self)   
        Game.GlobalEventSystem:AddListener(EEventTypesV2.CLIENT_GROUP_CREATE, "Refresh", self) 
        Game.GlobalEventSystem:AddListener(EEventTypesV2.CLIENT_GROUP_MEMBER_EXCHANGE, "Refresh", self)
        Game.EventSystem:AddListener(_G.EEventTypes.ON_TEAMMEMBER_MEMBERFLAG_CHANGED, self, self.Refresh, GetMainPlayerEID())
        Game.EventSystem:AddListener(_G.EEventTypes.CLIENT_GROUP_MEMBER_MARK_CAHNGED, self, self.Refresh)

        Game.GlobalEventSystem:AddListener(EEventTypesV2.CLIENT_GROUP_DISBAND,"Refresh", self)
    end
end

function HeadInfoFlag_OnCaptain:Refresh()
    local RpcEntity = self:GetAttachedEntity()

    self.RoleType = Enum.ETeamGroupRole.None
    if RpcEntity then
        if RpcEntity.teamID ~= 0 then
            -- 同组的才能看到队长标识
            if RpcEntity.isCaptain == 1 and Game.TeamSystem:IsTeamMember(RpcEntity.eid) then
                self.RoleType = Enum.ETeamGroupRole.Captain
            else
                self.RoleType = Enum.ETeamGroupRole.None
            end
        else
            if Game.GroupSystem:IsMyGroupMember(RpcEntity.eid) then
                if Game.GroupSystem:IsGroupLeader(RpcEntity.eid) then
                    self.RoleType = Enum.ETeamGroupRole.GroupLeader
                elseif Game.GroupSystem:IsInSameTeam(RpcEntity.eid, Game.me.eid) and Game.GroupSystem:IsTeamLeader(RpcEntity.eid) then
                    self.RoleType = Enum.ETeamGroupRole.Captain
                end
            end
        end
    end
    
    local Parent = self:GetParent()
    if Parent then
        Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.OnCaptain)
        Parent:OnMessage("OnRoleChanged", self.RoleType)
    end
    
    --Log.InfoFormat("HeadInfoFlag_OnCaptain:Refresh eid:%s, groupID:%s, bCaptain:%s", RpcEntity.eid, RpcEntity.groupID, self.bCaptain)
end

function HeadInfoFlag_OnCaptain:OnUnBindEvents()
    Game.EventSystem:RemoveObjListeners(self)
end

function HeadInfoFlag_OnCaptain:OnTeamIDChange()
    self:Refresh()
end

function HeadInfoFlag_OnCaptain:OnGroupIDChange()
    self:Refresh()
end

function HeadInfoFlag_OnCaptain:OnGroupTeamLeaderChange(Old, New)
    self:Refresh()
end

function HeadInfoFlag_OnCaptain:OnGroupLeaderChange(Old, New)
    self:Refresh()
end

function HeadInfoFlag_OnCaptain:OnIsCaptainChange()
    self:Refresh()
end

function HeadInfoFlag_OnCaptain:ShouldShow()
    if self.RoleType == Enum.ETeamGroupRole.None then
        return false
    end
    
    local RpcEntity = self:GetAttachedEntity()
    
    --- 组队检查是否是队长
    local teamMemberInfo = Game.TeamSystem:GetTeamMember(RpcEntity.eid)
    if RpcEntity.teamID ~= 0 and RpcEntity.teamID == Game.me.teamID and teamMemberInfo and teamMemberInfo.isCaptain == 1 then
        return true
    end

    -- me不在团队内
    if not Game.TeamSystem:IsInGroup() then
        return false
    end
    
    --- 团队检查是否是团长和队长
    if Game.GroupSystem:GetMyGroupLeaderID() == RpcEntity.eid then
        return true
    end

    if Game.GroupSystem:IsInSameTeam(RpcEntity.eid, Game.me.eid) and Game.GroupSystem:IsTeamLeader(RpcEntity.eid) then
        return true
    end
    
    return false
end

function HeadInfoFlag_OnCaptain:ShouldForceToHide()
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity == nil then
        return true
    end
    -- 队长团长和团队标记 互斥状态
    if Game.TeamSystem:GetTeamMemberMarkID(RpcEntity.eid) > 0 then
        return true
    end
    if Game.GroupSystem:GetGroupMemberInMarkID(RpcEntity.eid) > 0 then
        return true
    end

    return false
end


return HeadInfoFlag_OnCaptain
