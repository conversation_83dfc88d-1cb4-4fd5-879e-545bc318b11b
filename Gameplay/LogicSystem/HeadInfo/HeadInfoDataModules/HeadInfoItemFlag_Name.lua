local HeadInfoItemFlag_Name= DefineClass("HeadInfoItemFlag_Name",HeadInfoFlagBase)

function HeadInfoItemFlag_Name:ctor()
	self.HasName = nil
	self.ConfigToShow = nil
end

function HeadInfoItemFlag_Name:OnInit(Parent)
	self.HasName = false
	self.ConfigToShow = false
	local RpcEntity = self:GetAttachedEntity()
	if not RpcEntity then return end
		---
	if RpcEntity.isAvatar == true then
		self.HasName = true
	elseif  RpcEntity.isNpc == true then
		local NPCData =  RpcEntity:GetEntityConfigData()
		if NPCData and NPCData.Name and NPCData.Name ~="" then
			self.HasName = true
		end

		if NPCData and NPCData.IDResidentDisplay then
			self.ConfigToShow = true
		end
	end
end

function HeadInfoItemFlag_Name:OnUnInit()
	self.HasName = nil
end

function HeadInfoItemFlag_Name:OnBindEvents()
end

function HeadInfoItemFlag_Name:OnUnBindEvents()
end


function HeadInfoItemFlag_Name:ShouldShow()
	return self.ConfigToShow
end

function HeadInfoItemFlag_Name:ShouldForceToHide()
	return not self.HasName
end


return HeadInfoItemFlag_Name
