local common_interactor_const = kg_require("Shared.Const.CommonInteractorConst")
local COMMON_INTERACTOR_NAME_DISPLAY_RULE = common_interactor_const.COMMON_INTERACTOR_NAME_DISPLAY_RULE

local HeadInfoFlag_CommonInteractor = DefineClass("HeadInfoFlag_CommonInteractor", HeadInfoFlagBase)

HeadInfoFlag_CommonInteractor.HeadInfoOffsetVector = FVector(0, 0, 0)

function HeadInfoFlag_CommonInteractor:ctor()
    self.ShouldShowFlag = nil
    self.DisplayRule = -1
    self.EntityUID = 0
end

function HeadInfoFlag_CommonInteractor:OnInit(Parent)
    self.ShouldShowFlag = false
    self.EntityUID = Parent.uid
    local commonInteractorEntity = Game.EntityManager:getEntity(self.EntityUID)
    self:RefreshDisplayRule(commonInteractorEntity:GetDisplayRule())
end

function HeadInfoFlag_CommonInteractor:OnUnInit()
    self.ShouldShowFlag = nil
end

function HeadInfoFlag_CommonInteractor:OnBindEvents()
    if self.DisplayRule == Enum.ETaskCollectShowRule.AlwaysHide then
        return
    end
    
    Game.UniqEventSystemMgr:AddListener(self.EntityUID, EEventTypesV2.ON_COMMON_INTERACTOR_INTERACT_STATE_CHANGED, "OnInteractableStateChanged", self)
    Game.UniqEventSystemMgr:AddListener(self.EntityUID, EEventTypesV2.ON_COMMON_INTERACTOR_DISPLAY_NAME_CHANGED, "OnDisplayNameChanged", self)
end

function HeadInfoFlag_CommonInteractor:OnUnBindEvents()
	Game.UniqEventSystemMgr:RemoveListener(self.EntityUID, EEventTypesV2.ON_COMMON_INTERACTOR_INTERACT_STATE_CHANGED, "OnInteractableStateChanged", self)
	Game.UniqEventSystemMgr:RemoveListener(self.EntityUID, EEventTypesV2.ON_COMMON_INTERACTOR_DISPLAY_NAME_CHANGED, "OnDisplayNameChanged", self)
end

function HeadInfoFlag_CommonInteractor:OnInteractableStateChanged(bInteractable)
    if self.DisplayRule ~= COMMON_INTERACTOR_NAME_DISPLAY_RULE.SHOW_IF_INTERACTABLE then
        return
    end
    
    self.ShouldShowFlag = bInteractable
    local Parent = self:GetParent()
    Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.CommonInteractor)
end

function HeadInfoFlag_CommonInteractor:OnDisplayNameChanged(DisplayName, DisplayRule, HeadInfoOffset)
    self:RefreshDisplayRule(DisplayRule)
    HeadInfoFlag_CommonInteractor.HeadInfoOffsetVector.Z = HeadInfoOffset
    Game.HeadInfoManager:SetWorldWidgetOffset(self.EntityUID, HeadInfoFlag_CommonInteractor.HeadInfoOffsetVector)
end

function HeadInfoFlag_CommonInteractor:RefreshDisplayRule(NewDisplayRule)
    if self.DisplayRule == NewDisplayRule then
        return
    end

    self.DisplayRule = NewDisplayRule
    if self.DisplayRule == COMMON_INTERACTOR_NAME_DISPLAY_RULE.ALWAYS_HIDE then
        self.ShouldShowFlag = false
    elseif self.DisplayRule == COMMON_INTERACTOR_NAME_DISPLAY_RULE.ALWAYS_SHOW then
        self.ShouldShowFlag = true
    elseif self.DisplayRule == COMMON_INTERACTOR_NAME_DISPLAY_RULE.SHOW_IF_INTERACTABLE then
        local commonInteractorEntity = Game.EntityManager:getEntity(self.EntityUID)
        self.ShouldShowFlag = commonInteractorEntity:IsInteractable()
    end

    local Parent = self:GetParent()
    Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.CommonInteractor)
end

function HeadInfoFlag_CommonInteractor:ShouldShow()
    return self.ShouldShowFlag
end

function HeadInfoFlag_CommonInteractor:ShouldForceToHide()
    return false
end

return HeadInfoFlag_CommonInteractor