local HeadInfoFlag_DropItem= DefineClass("HeadInfoFlag_DropItem", HeadInfoFlagBase)


function HeadInfoFlag_DropItem:ctor()
	self.ShouldShowFlag = nil
    self.InsID = ""
end

function HeadInfoFlag_DropItem:OnInit(Parent)
	self.ShouldShowFlag = false
	local RpcEntity = self:GetAttachedEntity()
	if RpcEntity then
		local ItemStage = RpcEntity.ItemStage
		self.ShouldShowFlag = ItemStage == LSAE_DropItem.EStage.Landed
		self.InsID = RpcEntity.InsID
	end
end

function HeadInfoFlag_DropItem:OnUnInit()
	self.ShouldShowFlag = nil
end


function HeadInfoFlag_DropItem:OnBindEvents()
	Game.EventSystem:AddListener(_G.EEventTypes.LSCENEACTOR_DROP_ITEM_STATE_CHANGE, self, self.OnStageChanged, self.InsID)
end

function HeadInfoFlag_DropItem:OnUnBindEvents()
	Game.EventSystem:RemoveObjListeners(self)
end


function HeadInfoFlag_DropItem:OnStageChanged(Stage)
	self.ShouldShowFlag = Stage == LSAE_DropItem.EStage.Landed
	local Parent = self:GetParent()
	Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.InteractorMsgTrigger)
end

function HeadInfoFlag_DropItem:ShouldShow()
	return self.ShouldShowFlag
end

function HeadInfoFlag_DropItem:ShouldForceToHide()
	return false
end

return HeadInfoFlag_DropItem