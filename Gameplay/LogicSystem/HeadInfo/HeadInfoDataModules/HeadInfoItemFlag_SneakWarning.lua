local HeadInfoItemFlag_SneakWarning = DefineClass("HeadInfoItemFlag_SneakWarning", HeadInfoFlagBase)

function HeadInfoItemFlag_SneakWarning:OnBindEvents()
    self.type = Enum.EHeadNPCWarningType.UnVisible
	Game.UniqEventSystemMgr:AddListener(self.EntityID, EEventTypesV2.ON_SNEAK_NPC_WARNING_UPDATE, "UpdateFlag", self)
end

function HeadInfoItemFlag_SneakWarning:OnUnBindEvents()
	Game.UniqEventSystemMgr:RemoveListener(self.EntityID, EEventTypesV2.ON_SNEAK_NPC_WARNING_UPDATE, "UpdateFlag", self)
end

function HeadInfoItemFlag_SneakWarning:UpdateFlag(entityId, type)
    local entity = Game.EntityManager:getEntity(entityId)
    if not entity then
        return
    end
    if self.EntityID == entity:uid() then
        self.type = type
        local Parent = self:GetParent()
        Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.ItemFlgSneakWarning)
        if type == Enum.EHeadNPCWarningType.Alarm then
            Game.TimerManager:CreateTimerAndStart(function()
                self.type = Enum.EHeadNPCWarningType.UnVisible
                Parent:OnFlagChanged(Game.HeadInfoManager.EShowRules.ItemFlgSneakWarning)
            end, 2000, 1)
        end
    end
end

function HeadInfoItemFlag_SneakWarning:ShouldShow()
    if not self.EntityID then
        return false
    end
    
    if self.type > Enum.EHeadNPCWarningType.UnVisible then
        return true
    else
        return false
    end
end

return HeadInfoItemFlag_SneakWarning
