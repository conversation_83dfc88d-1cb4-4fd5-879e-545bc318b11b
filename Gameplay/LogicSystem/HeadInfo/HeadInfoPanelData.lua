local EWoldWidgetType = import("EWoldWidgetType")
local GameConst = kg_require("Gameplay.CommonDefines.C7_Game_Const")

---@class HeadInfoPanelData
local HeadInfoPanelData = DefineClass("HeadInfoPanelData")
local HeadInfoConst = kg_require ("Gameplay.LogicSystem.HeadInfo.HeadInfoConst")

local DistanceCheckTimeInterval = 0.5

local function _Xor(a, b) -- luacheck: ignore
    return (a and not b) or (not a and b)
end

function HeadInfoPanelData:ctor()
    self.HiddenFlagMap = {} --隐藏标记来源
    self.Items = {}
    self.timer = nil
    self.DamageTimer = nil
    self.HeadInfoConfig = nil
    self.HeadInfoWidget = nil

    self.bCycleBubble = false
    self.bCycling = false
    self.InnerBubbleGroupDist = nil
    self.OuterBubbleGroupID = nil
    self.OuterBubbleGroupDist = nil
    self.BubbleOrder = 1
    self.bIsNPC = false
    -- NPC发送气泡信息循环计时器
    self.BubbleCycleTimer = nil
    -- 记录当前气泡时间
    self.BubbleShowTimer = nil
    self.FlagsRequried = nil
    self.Distance = nil
    self.DistCheckTimerHandle = nil

    self.EntityID = nil
    self.FlagInstances = {}

    self.RealHiddenDist = 0
    self.HiddenDistOverride = nil
    self.SlotsStatus = {}
    --外部强控隐藏
    self.bForceHide = false
    self.uid = nil
    self.showWhiteList = {}
    -- 缓存头顶挂点数据
    self.CacheHeadPelvisOffset = FVector()
    -- 缓存头顶偏移数据
    self.CacheHeadOffSet = FVector()
end

function HeadInfoPanelData:InitDisplayFlags()
    self.FlagInstances = {}

    for SlotID, _ in pairs(self.HeadInfoConfig.PanelDataConfig) do
        local SlotCfg = self:GetSlotConfig(SlotID)
        for Flag, bUseThisFlag in pairs(SlotCfg.ShowRule) do
            if bUseThisFlag and not self.FlagInstances[Flag] then
                self.FlagInstances[Flag] = Game.HeadInfoManager:CreateFlagInst(Flag)
                self.FlagInstances[Flag]:Init(self)
            end
        end
    end

    for K, FlgInst in pairs(self.FlagInstances) do
        FlgInst:OnBindEvents()
    end
end

function HeadInfoPanelData:ShowBubbleText(message, duration, bShowChat, voice, anim, endCallBack, bubbleType, priority)
    ---@type HeadInfoFlag_OnShowBubble
	local BubbleFlagInst = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnShowBubble)
    priority = priority or Game.HeadInfoManager.EBubblePriority.Common
    if BubbleFlagInst then
        BubbleFlagInst:ShowBubbleText(message, duration, bShowChat, voice, anim, endCallBack, bubbleType, priority)
    elseif endCallBack then
        endCallBack()
    end
end

function HeadInfoPanelData:HideBubbleText()
	---@type HeadInfoFlag_OnShowBubble
	local BubbleFlagInst = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnShowBubble)
	if BubbleFlagInst then
		BubbleFlagInst:hideBubbleText()
	end
end

function HeadInfoPanelData:CheckTeamType(Members, OpType)
    if self.uid == Game.me:uid() then
        return
    end
    local Entity = Game.EntityManager:getEntityWithBrief(self.uid)
    if not Entity then return end
    if _Xor(Game.TeamSystem:IsTeamMember(Entity.eid), self.HeadInfoConfig.ActorType == Game.HeadInfoManager.EHeadInfoActorTypes.TeammatePlayer) then
        self:RefreshPanel()
    end
end

function HeadInfoPanelData:GetSlotConfig(SlotID)
    return self.HeadInfoConfig.PanelDataConfig[SlotID]
end

function HeadInfoPanelData:CheckDisplayRules()
    local bHasSlotShowing = false
    for SlotID, _ in pairs(self.HeadInfoConfig.PanelDataConfig) do
        if self:CheckDisplayRule(SlotID) and not self.bForceHide then
            if self:EnableWhitelist() then
                if self:CheckWhitelist(SlotID) then
                    self.SlotsStatus[SlotID] = true
                    bHasSlotShowing = bHasSlotShowing or true
                end
            else
                self.SlotsStatus[SlotID] = true
                bHasSlotShowing = bHasSlotShowing or true
            end
        else
            self.SlotsStatus[SlotID] = false
            bHasSlotShowing = bHasSlotShowing or false
        end
    end

    if bHasSlotShowing then
        if not self.HeadInfoWidget then
            self:CreateHeadInfoWidget()
        end
        for SlotID, IsShow in pairs(self.SlotsStatus) do
            if IsShow then
                self.HeadInfoWidget:ShowSlotItem(SlotID)
            else
                self.HeadInfoWidget:HideSlotItem(SlotID)
            end
        end
        self:SetHeadInfoDisplay(HeadInfoConst.HiddenSourceType.CHILD, true)
    else
        self:SetHeadInfoDisplay(HeadInfoConst.HiddenSourceType.CHILD, false)
    end
end

function HeadInfoPanelData:SetHeadInfoDisplay(source, isShow)
    if isShow then
        self.HiddenFlagMap[source] = nil
    else
        self.HiddenFlagMap[source] = true
    end
    self:RefreshHeadDisplay()
end

function HeadInfoPanelData:RefreshHeadDisplay()
    if self.HeadInfoWidget then
        if next(self.HiddenFlagMap) then
            self.HeadInfoWidget:Hide()
        else
            self.HeadInfoWidget:Show()
        end
    end
end

function HeadInfoPanelData:CheckDisplayRule(SlotID)
    local SlotConfig = self.HeadInfoConfig.PanelDataConfig[SlotID]
    if not SlotConfig then
        return
    end

    local bShouldShow = false
    for Flag, bUseFlag in pairs(SlotConfig.ShowRule) do
        if bUseFlag and self.FlagInstances[Flag] then
            bShouldShow = bShouldShow or self.FlagInstances[Flag]:ShouldShow(SlotID)
            if self.FlagInstances[Flag]:ShouldForceToHide(SlotID) then
                bShouldShow = false
                break
            end
        end
    end

    return bShouldShow
end

local PendingRemoveTime = 10000
function HeadInfoPanelData:RemovePanel()
    if self.HeadInfoWidget then
        Game.WorldWidgetManager2:DeleteWorldWidget(self.HeadInfoWidget.WWID)
        self.HeadInfoWidget = nil
    end
end

function HeadInfoPanelData:GetHeadOffset()
 -- 重置头顶高度数据
    self.CacheHeadOffSet.X, self.CacheHeadOffSet.Y, self.CacheHeadOffSet.Z = 0, 0, 20
    local Socket = "TOP_LOGO"
    local RpcEntity = Game.EntityManager:getEntityWithBrief(self.uid)
	local MainMeshID = RpcEntity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
	if MainMeshID <= 0 then
		MainMeshID = RpcEntity.CppEntity:KAPI_Actor_GetMainMesh()
	end
	if MainMeshID <= 0 then
		Socket = "head"
	else
        if RpcEntity.CppEntity:KAPI_SkeletalMeshID_GetBoneIndex(MainMeshID, Socket) ==-1  then
		    Socket = "head"
	    end
    end
    self.CacheHeadOffSet.Z = RpcEntity:GetHeadInfoOffset()
    return self.CacheHeadOffSet, Socket
end

function HeadInfoPanelData:CreateHeadInfoWidget()
    local RpcEntity = Game.EntityManager:getEntityWithBrief(self.uid)
    if not self.HeadInfoWidget and RpcEntity then
        local Config = {}
        Config.WType = 1
        if not RpcEntity.IsTrueInvisible then
            Config.bVisible = true
            -- 查一下哪个Entity没挂组件
            Log.ErrorFormat("HeadInfoPanelData:CreateHeadInfoWidget found entity without visible component, %s", RpcEntity.__cname)
        else
            Config.bVisible = not RpcEntity:IsTrueInvisible()
        end

        Config.ZorderOffSet = (Game.me == RpcEntity) and GameConst.HEAD_INFO_MAIN_PLAYER_ZORDER or 0
        self.RealHiddenDist = Game.TableData.GetConstDataRow("GENERAL_HEADINFO_DISPLAY_RANGE") or 4000

        local NpcData = RpcEntity:GetEntityConfigData()
        if NpcData and NpcData.NpcHideHUDDistance then
            self.RealHiddenDist = NpcData.NpcHideHUDDistance
        end
        Config.HiddenDistance = self.RealHiddenDist
        if NpcData and NpcData.NPCHideHUDBlockJudgment then
            Config.bNoDepth = NpcData.NPCHideHUDBlockJudgment == 1
        end
        Config.Alignment = FVector2D(0.5, 1)

        local Offset, Socket = self:GetHeadOffset()

        Config.SocketName = Socket
        Config.WorldOffset = Offset

        Config.bEnableDistanceScale = true
        Config.DistanceScaleCurveName = UIAssetPath.C_HeadinfoDistanceScaleCurve

        --载具数据
        if NpcData == nil and ConfigID then
            NpcData = Game.TableData.GetVehicleNpcDataRow(ConfigID)
        end


        Config.LayerName = "HeadInfo"

        if self.HeadInfoConfig then
            local params =  { EntityID = self.EntityID, HeadInfoConfig = self.HeadInfoConfig, PanelData = self }
            self.HeadInfoWidget = Game.WorldWidgetManager2:CreateWorldWidget("/HeadInfo", RpcEntity:uid(), Config, params ,"GPUTurboInvalidationBox_lua")
            if _G.UE_EDITOR then
                self.HeadInfoWidget.widget:SetReflectionName(self.EntityID)
            end
        end
    end
end

function HeadInfoPanelData:OnFlagChanged(FlagType)
    self:CheckDisplayRules()
end

function HeadInfoPanelData:UpDateBuffDisplay()
    local FlagInst = self.FlagInstances[Game.HeadInfoManager.EShowRules.OnDisplayBuff]
    if FlagInst then
        FlagInst:UpDateBuffDisplay()
    end
end

function HeadInfoPanelData:UpDatePuppetDisplay(configID, bStart)
	local FlagInst = self.FlagInstances[Game.HeadInfoManager.EShowRules.OnDisplayPuppet]
	if FlagInst then
		FlagInst:UpDatePuppetDisplay(configID, bStart)
	end
end

function HeadInfoPanelData:UpdateWhitelist(list)
    table.clear(self.showWhiteList)
    if list then
        table.merge(self.showWhiteList, list)
    end
    self:CheckDisplayRules()
end

function HeadInfoPanelData:EnableWhitelist()
    return #self.showWhiteList > 0
end

function HeadInfoPanelData:CheckWhitelist(SlotID)
    return table.contains(self.showWhiteList, SlotID)
end

function HeadInfoPanelData:OnCharacterMorph()
    local FlagInst = self.FlagInstances[Game.HeadInfoManager.EShowRules.ItemFlgTitle]
    if FlagInst then
        FlagInst:OnCharacterMorph()
    end
    self:OnMessage("OnCharacterMorph")

    self:RefreshPanel()
end

function HeadInfoPanelData:OnDisplayNameChanged()
    self:OnMessage("OnDisplayNameChanged")
end

function HeadInfoPanelData:OnHeadTitleChanged()
    self:OnMessage("RefreshTitleText")
    self:RefreshPanel()
	self:CheckDisplayRules()
end

function HeadInfoPanelData:GetFlagInstance(Flag)
    return self.FlagInstances[Flag]
end

function HeadInfoPanelData:OnGMDebugToggled(bEnableGM, Msg, Duration)
    local FlagInst = self.FlagInstances[Game.HeadInfoManager.EShowRules.GMDebugToggle]
    if FlagInst then
        FlagInst:UpdateGMDebugToggle(Msg, Duration)
    end
end

function HeadInfoPanelData:OnTakeDamage(InContext)
    local FlagInst = self.FlagInstances[Game.HeadInfoManager.EShowRules.OnTakeDamage]
    if FlagInst then
        FlagInst:OnTakeDamage(InContext)
    end
end

function HeadInfoPanelData:OnMessage(MessageName, ...)
    local Panel = self.HeadInfoWidget
    if Panel then
        Panel:OnMessage(MessageName, ...)
    end
end

local CachedGetDistanceToFunc = import("Actor").GetDistanceTo

function HeadInfoPanelData:OnDistanceCheck()
    local luaProfiler <close> = Game.ProfilerInstrumentation:Start(ProfilerInstrumentationConfig.HeadInfoDistanceCheck.name) -- 代码插桩统计
    local RpcEntity = Game.EntityManager:getEntityWithBrief(self.uid)
    if Game.me and Game.me.bInWorld then
        if RpcEntity and RpcEntity.bInWorld then
            self.Distancce = Game.me.CppEntity:KAPI_GetDistanceTo(RpcEntity.CharacterID)
            self:OnMessage("OnDistanceUpdate", self.Distance)

            local BubbleFlagInst = self.FlagInstances[Game.HeadInfoManager.EShowRules.OnShowBubble]
            if BubbleFlagInst then
                BubbleFlagInst:OnDistanceUpdate(self.Distance)
            end
        end
    end
end

function HeadInfoPanelData:ClearUpHeadInfoFlags()
    for K, V in pairs(self.FlagInstances) do
        V:UnInit()
    end
    self.FlagInstances = {}
end

function HeadInfoPanelData:ResetSlots()
    for SlotID, IsShow in pairs(self.SlotsStatus) do
        if self.HeadInfoWidget then
            self.HeadInfoWidget:HideSlotItem(SlotID)
        end
    end
    table.clear(self.SlotsStatus)
end

function HeadInfoPanelData:InitDistanceTimer()
    if self.DistCheckTimerHandle then
        Game.TimerManager:StopTimerAndKill(self.DistCheckTimerHandle)
        self.DistCheckTimerHandle = nil
    end

    local bNeedDistanceUpdate = false
    local BubbleFlag = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnShowBubble)
    if BubbleFlag and BubbleFlag:NeedDistanceCheck() then
        bNeedDistanceUpdate = true
    end

    self.bCycleBubble = bNeedDistanceUpdate
    if not bNeedDistanceUpdate then
        for _, Config in pairs(self.HeadInfoConfig.PanelDataConfig) do
            if Config.bNeedDistanceUpdate then
                bNeedDistanceUpdate = bNeedDistanceUpdate or Config.bNeedDistanceUpdate
            end
        end
    end

    if bNeedDistanceUpdate then
        self.Distance = 0
        self.DistCheckTimerHandle = Game.TimerManager:CreateTimerAndStart(function()
            self:OnDistanceCheck()
        end, DistanceCheckTimeInterval * 1000, -1, nil, nil, true)
    end
end

function HeadInfoPanelData:InitHeadPanelData()
    self:InitDisplayFlags()
    self:CheckDisplayRules()
    self:InitDistanceTimer()
end

function HeadInfoPanelData:RefreshPanel()
    -- 临时处理 按照规范UI不应该持有Character
    if (not self.EntityID or not Game.EntityManager:getEntityWithBrief(self.EntityID)) then
        return
    end

    local NewHeadInfoCfg = Game.HeadInfoManager:GetHeadInfoConfig(self.uid)

    self:RefreshHeadOffset()
    if NewHeadInfoCfg == self.HeadInfoConfig then
        --如果Config一至 ，通过事件刷新子元素
        self:OnMessage("OnRefresh")
        return
    end
    self:ClearUpHeadInfoFlags()
    self:ResetSlots()
    if self.DistCheckTimerHandle then
        Game.TimerManager:StopTimerAndKill(self.DistCheckTimerHandle)
        self.DistCheckTimerHandle = nil
    end

    self.HeadInfoConfig = Game.HeadInfoManager:GetHeadInfoConfig(self.uid)
    self:InitHeadPanelData()
	self:OnMessage("OnRefresh")
end

function HeadInfoPanelData:RefreshHeadOffset()
    if self.HeadInfoWidget then
        local Offset, _ = self:GetHeadOffset()
        self:SetWorldWidgetOffset(Offset)
    end
end

function HeadInfoPanelData:SetWorldWidgetOffset(offset)
	if self.HeadInfoWidget then
        Game.WorldWidgetManager2:SetWorldWidgetWorldOffset(self.HeadInfoWidget.WWID, offset)
	end
end

function HeadInfoPanelData:SetWorldWidgetNoDepth(bNoDepth)
	if self.HeadInfoWidget then
        Game.WorldWidgetManager2:SetWorldWidgetNoDepth(self.HeadInfoWidget.WWID, bNoDepth)
	end
end

function HeadInfoPanelData:RefreshHudVisible()
    self:OnMessage("OnSetHudVisible")
end

function HeadInfoPanelData:SetWorldWidgetHiddenDis(Dist)
    self.HiddenDistOverride = Dist
    local FinalHiddenDist = self.HiddenDistOverride or self.RealHiddenDist
    if self.HeadInfoWidget then
        Game.WorldWidgetManager2:SetWorldWidgetHiddenDis(self.HeadInfoWidget.WWID, FinalHiddenDist)
    end
end

function HeadInfoPanelData:ForceHideHeadInfo(bHidden)
    self.bForceHide = bHidden
    self:CheckDisplayRules()
end

function HeadInfoPanelData:Init(Params)
    self.EntityID = Params.uid
    self.uid = Params.uid
    if Params.uid then
        self.HeadInfoConfig = Params.HeadInfoConfig
    end

    self.DamageTimer = 0

    local RpcEntity = Game.EntityManager:getEntityWithBrief(self.EntityID)

    if Game.me and Game.me.eid ~= RpcEntity.eid then
        Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.RefreshPanel, RpcEntity.eid)
    end

    Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.RefreshPanel, GetMainPlayerEID())

    Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_TEAMINFOLIST_CHANGED, self, self.CheckTeamType,
            GetMainPlayerEID())

    Game.EventSystem:AddListener(_G.EEventTypes.ON_MORPHID_CHANGED, self, self.OnCharacterMorph, RpcEntity.eid)
    Game.EventSystem:AddListener(_G.EEventTypes.NPC_HEAD_TITLE_CHANGED, self, self.OnHeadTitleChanged, RpcEntity.eid)

    self:InitHeadPanelData()
end

function HeadInfoPanelData:Uninit()
    self:ClearUpHeadInfoFlags()

    if self.HeadInfoWidget then
        Game.WorldWidgetManager2:DeleteWorldWidget(self.HeadInfoWidget.WWID)
    end

    if self.DistCheckTimerHandle then
        Game.TimerManager:StopTimerAndKill(self.DistCheckTimerHandle)
        self.DistCheckTimerHandle = nil
    end

    if self.BubbleCycleTimer then
        Game.TimerManager:StopTimerAndKill(self.BubbleCycleTimer)
        self.BubbleCycleTimer = nil
    end

    Game.EventSystem:RemoveObjListeners(self)
end

--region Debug

function HeadInfoPanelData:DebugDisplayRules()
    if self.bForceHide then
        Log.InfoFormat("HeadInfo uid:%s forceHide", self.uid)
        return
    end
    for SlotID, _ in pairs(self.HeadInfoConfig.PanelDataConfig) do
        if self:CheckDisplayRule(SlotID) then
            Log.InfoFormat("HeadInfo uid:%s SlotID %s DisplayRule: %s", self.uid, SlotID, true)  
        else
            Log.InfoFormat("HeadInfo uid:%s SlotID %s DisplayRule: %s", self.uid, SlotID, false)
        end
    end
end

function HeadInfoPanelData:DebugSlotDisplayRules(SlotID)
    local SlotConfig = self.HeadInfoConfig.PanelDataConfig[SlotID]
    for Flag, bUseFlag in pairs(SlotConfig.ShowRule) do
        if bUseFlag and self.FlagInstances[Flag] then
            bShouldShow = bShouldShow or self.FlagInstances[Flag]:ShouldShow(SlotID)
            Log.InfoFormat("HeadInfo uid:%s SlotID %s Flag:%s ShouldShow: %s", self.uid, SlotID, self.FlagInstances[Flag].__cname, bShouldShow)

            if self.FlagInstances[Flag]:ShouldForceToHide(SlotID) then
                Log.InfoFormat("HeadInfo uid:%s SlotID %s Flag:%s ShouldForceToHide: %s", self.uid, SlotID, self.FlagInstances[Flag].__cname, true)
                break
            end
        end
    end
end

function HeadInfoPanelData:DebugHeadInfoOffset()
    local Offset, Socket = self:GetHeadOffset()
     Log.InfoFormat("HeadInfo uid:%s Offset: %s Socket: %s", self.uid, Offset, Socket)
end
--endregion
return HeadInfoPanelData
