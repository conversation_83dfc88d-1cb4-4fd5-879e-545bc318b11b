local HeadInfoFlagBase= DefineClass("HeadInfoFlagBase")

function HeadInfoFlagBase:OnInit(Parent)
end

function HeadInfoFlagBase:OnUnInit()
end

function HeadInfoFlagBase:ctor()
	self.Parent = nil
	self.EntityID = nil
end

function HeadInfoFlagBase:Init(Parent)
	self.Parent = Parent
	--Depreciated 后续self.Actor将移除
	self.EntityID = Parent.EntityID
	self:OnInit(Parent)
end

function HeadInfoFlagBase:OnBindEvents()
end

function HeadInfoFlagBase:OnUnBindEvents()
end

function HeadInfoFlagBase:ShouldShow()
	return false
end

function HeadInfoFlagBase:ShouldForceToHide()
	return false
end

function HeadInfoFlagBase:UnInit()
	self:OnUnInit()
	self:OnUnBindEvents()
	self.Parent = nil
end

function HeadInfoFlagBase:GetParent()
	return self.Parent
end

function HeadInfoFlagBase:GetPanelData()
	return self.Parent:GetPanelData()
end

function HeadInfoFlagBase:GetAttachedEntity()
	--头顶显示BriefActor也需要 @hujianglong
	return Game.EntityManager:getEntityWithBrief(self.EntityID)
end

function HeadInfoFlagBase:GetFlagInstance(Flag)
	local PD = self.Parent:GetPanelData()
	if PD then
		return PD:GetFlagInstance(Flag)
	end
end

return HeadInfoFlagBase
