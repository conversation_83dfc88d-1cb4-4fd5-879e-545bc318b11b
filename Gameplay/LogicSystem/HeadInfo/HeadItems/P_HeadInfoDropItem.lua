local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoDropItem= DefineClass("P_HeadInfoDropItem",P_HeadInfoItemBase)


function P_HeadInfoDropItem:OnBindActor(EntityID)
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity then
		if RpcEntity.Data and RpcEntity.Data.DropItem then
			local ItemData = Game.TableData.GetItemNewDataRow(RpcEntity.Data.DropItem.DropItemID)
			if ItemData then
				local Quality = ItemData.quality
				self.view.Text_Drop_lua:SetText("<Quality_"..Quality..">"..ItemData.itemName.."</>")
			else
				self.view.Text_Drop_lua:SetText("<Quality_1>UNKNOWN ITEM NAME</>")
			end
		end
    end
end

function P_HeadInfoDropItem:OnUnbindActor()

end

return P_HeadInfoDropItem