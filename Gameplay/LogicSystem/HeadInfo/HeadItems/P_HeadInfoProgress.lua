local ESlateVisibility = import("ESlateVisibility")
local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID = kg_require("Shared.Const").BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID

local P_HeadInfoProgress = DefineClass("P_HeadInfoProgress", P_HeadInfoItemBase)

local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

function P_HeadInfoProgress:OnBindActor(EntityID)
    Game.EventSystem:RemoveObjListeners(self)
    local Entity = self:GetAttachedEntity()
    Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF_LAYER_CHANGED, self, self.RefreshWidgetView, Entity.eid)
    Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF_TOTALLIFE_CHANGED, self, self.RefreshWidgetView, Entity.eid)
    self:BindHeadMessage("OnBuffListChanged", "OnBuffListChanged")
    self.EntityID = EntityID

    self:RefreshWidgetView()
end

function P_HeadInfoProgress:GetConfig()
    local FlagInst = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnDisplayProgress)
    if FlagInst then
        return FlagInst:GetConfigID()
    end
end

function  P_HeadInfoProgress:RefreshWidgetView()
    local ConfigID = self:GetConfig()
    if not ConfigID then return end
    local Entity = self:GetAttachedEntity()
    local OverHeadProgressDisplayDataRow = Game.TableData.GetOverHeadProgressDisplayDataRow(ConfigID)
    local BuffValues = {}
    for Index = 1, 3 do
        local Numeric = Entity:GetBuffInstanceByBuffIDAndInstigatorID(OverHeadProgressDisplayDataRow["BuffID" .. Index], BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID)
        if Numeric then
            Numeric = Numeric:GetCurrentLayer()
            for Operation, Value in ksbcpairs(OverHeadProgressDisplayDataRow["Operation" .. Index]) do
                if Operation == "*" then
                    Numeric = Numeric * Value
                elseif Operation == "/" then
                    Numeric = Numeric / Value
                elseif Operation == "+" then
                    Numeric = Numeric + Value
                elseif Operation == "-" then
                    Numeric = Numeric - Value
                elseif Operation == "//" then
                    Numeric = Numeric // Value
                elseif Operation == "%" then
                    Numeric = Numeric % Value
                end
            end
            table.insert(BuffValues, Numeric)
        end
    end
    local Str = string.format(OverHeadProgressDisplayDataRow.DescText, BuffValues[1], BuffValues[2], BuffValues[3])
    self.view.Text_ProgressText_lua:SetText(Str)    
end


function P_HeadInfoProgress:OnUnbindActor()
    Game.EventSystem:RemoveObjListeners(self)
    if self.TimerHandle then
        self:StopTimer(self.TimerHandle)
        self.TimerHandle = nil
    end
end


return P_HeadInfoProgress
