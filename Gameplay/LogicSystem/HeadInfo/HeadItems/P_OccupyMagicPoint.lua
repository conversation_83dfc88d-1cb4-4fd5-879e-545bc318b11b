local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_OccupyMagicPoint = DefineClass("P_OccupyMagicPoint", P_HeadInfoItemBase)

local StringConst = require "Data.Config.StringConst.StringConst"

local Const = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

local ESlateVisibility = import("ESlateVisibility")

P_OccupyMagicPoint.eventBindMap = {
	[EEventTypesV2.PVP_ON_12V12_MAGIC_POINT_UPDATE] = "OnPointUpdate"
}

function P_OccupyMagicPoint:ctor()
	self.EntityID = nil
	self.InstanceID = "0"
	self.view.Text_Content:SetText("")
	---@type number
	self.MaxProgress = Game.TableData.Get12V12SettingDataRow("OccupyShapeTriggerMaxProgress")
	self.userWidget:SetVisibility(ESlateVisibility.Hidden)
	self.userWidget:Event_UI_State(0)
end

function P_OccupyMagicPoint:dtor()
end

function P_OccupyMagicPoint:OnBindActor(EntityID)
	self.EntityID = EntityID
	local Entity = Game.EntityManager:getEntity(EntityID)
	self.InstanceID = Entity.TemplateID
	
	self.userWidget:SetVisibility(ESlateVisibility.Hidden)
	self.userWidget:Event_UI_State(0)
	self.view.Text_Content:SetText(StringConst.Get("PVP_12V12_MAGIC_POINT_NOT_ACTIVATED"))
end

function P_OccupyMagicPoint:OnPointUpdate(InstanceID, Status, Progress)
	if InstanceID == self.InstanceID then
		self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if Status == Const.OCCUPY_SHAPE_TRIGGER_STATUS.DEACTIVATE then
			if Progress == self.MaxProgress then
				self:OnFullyActivatedAndDeactivated()
			else
				self.view.Text_Content:SetText(StringConst.Get("PVP_12V12_MAGIC_POINT_NOT_ACTIVATED"))
			end
		elseif Status == Const.OCCUPY_SHAPE_TRIGGER_STATUS.ACTIVATE then
			self.view.Text_Content:SetText(StringConst.Get("PVP_12V12_MAGIC_POINT_ACTIVATING"))
		elseif Status == Const.OCCUPY_SHAPE_TRIGGER_STATUS.STOP then
			self.view.Text_Content:SetText(StringConst.Get("PVP_12V12_MAGIC_POINT_PAUSED"))
		elseif Status == Const.OCCUPY_SHAPE_TRIGGER_STATUS.DONE then
			self.view.Text_Content:SetText(StringConst.Get("PVP_12V12_MAGIC_POINT_FULL_ACTIVATED"))
		end
		self.userWidget:Event_UI_State(Progress)
	end
end

---法阵完全激活之后会被流程图关掉，这里我们额外多显示两秒状态
function P_OccupyMagicPoint:OnFullyActivatedAndDeactivated()
	self:StartTimer(
		"PVP12V12MagicFullyActivatedTimer_Entity",
		function()
			self.view.Text_Content:SetText(StringConst.Get("PVP_12V12_MAGIC_POINT_NOT_ACTIVATED"))
			self.userWidget:SetVisibility(ESlateVisibility.Hidden)
		end,
		2000,
		1
	)
end

return P_OccupyMagicPoint