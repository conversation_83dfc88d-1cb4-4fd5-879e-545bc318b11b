local ESlateVisibility = import("ESlateVisibility")

local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_WitchOrSlave = DefineClass("P_WitchOrSlave", P_HeadInfoItemBase)

function P_WitchOrSlave:ctor()
end

function P_WitchOrSlave:OnBindActor(EntityID)
    self:BindHeadMessage("OnIsSlaveChanged", "OnIsSlaveChanged")
    self.EntityID = EntityID
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity and RpcEntity.isWitchSlave then
        self.userWidget:Event_UI_Style(true)
    end
end

-- 这里做ui的刷新操作
function P_WitchOrSlave:OnIsSlaveChanged(SigType)
    if SigType then
        self:Show(ESlateVisibility.selfHitTestInvisible)
        self.userWidget:Event_UI_Style(true)
    else
        self:Hide(ESlateVisibility.Collapsed)
    end
end

function P_WitchOrSlave:OnUnbindActor()
    self.EntityID = nil
end

function P_WitchOrSlave:OnUninit()

end

return P_WitchOrSlave
