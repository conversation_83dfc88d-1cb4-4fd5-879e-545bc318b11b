local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoSceneActorName= DefineClass("P_HeadInfoSceneActorName",P_HeadInfoItemBase)


function P_HeadInfoSceneActorName:OnBindActor(EntityID)
    local entity = Game.EntityManager:getEntity(EntityID)
    if entity and entity.GetDisplayName then
        local RarityColor = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
        self.view.Text_Name_lua:SetText(entity:GetDisplayName())
        self.view.Text_Name_lua:SetColorAndOpacity(RarityColor)
    end
end

function P_HeadInfoSceneActorName:OnUnbindActor()

end

return P_HeadInfoSceneActorName
