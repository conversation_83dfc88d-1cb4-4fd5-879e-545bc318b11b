

local P_HeadInfoItemBase = kg_require"Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_TaskCollectHeadTitle= DefineClass("P_TaskCollectHeadTitle",P_HeadInfoItemBase)
local ESlateVisibility = import("ESlateVisibility")

P_TaskCollectHeadTitle.NPCDistance = {
    500,
}

function P_TaskCollectHeadTitle:ctor()
    self.EntityID = nil
    self.BeUseTitle = nil
    self.AnimHidden = nil
end

function P_TaskCollectHeadTitle:OnBindActor(EntityID)
    --self:BindHeadMessage("OnDistanceUpdate", "OnDistanceUpdate")
    
    self.EntityID = EntityID

    self:RefreshTitleText()

    self.view.SB_SubName:SetHeightOverride(40)
    self.view.SB_SubName:SetRenderOpacity(1)
    self.AnimHidden = false
    self.view.SB_SubName:SetRenderOpacity(1)
    self.view.SB_SubName:SetRenderTranslation(FVector2D(0, 0))
	local RpcEntity = self:GetAttachedEntity()
    if RpcEntity and RpcEntity.InsID then
        self.ListenEntityInsID = RpcEntity.InsID
        Game.UniqEventSystemMgr:AddListener(self.ListenEntityInsID, EEventTypesV2.LSCENEACTOR_HEAD_TITLE_CHANGED, "OnDisplayTitleNameChanged", self)
    else
        self.ListenEntityInsID = nil
    end
end

function P_TaskCollectHeadTitle:OnDisplayTitleNameChanged(TitleName)
	self:RefreshTitleByText(TitleName)
end

function P_TaskCollectHeadTitle:RefreshTitleByText(TitleName)
	if StringValid(TitleName) then
		self.view.SB_SubName:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.RTB_NpcTitle:SetText(TitleName)
		self.BeUseTitle = true
	else
		self.view.SB_SubName:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function P_TaskCollectHeadTitle:RefreshTitleText()
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity then
		local TitleName = RpcEntity.ExcelData.TitleName
		if RpcEntity.Data and RpcEntity.Data.OverrideConf and StringValid(RpcEntity.Data.OverrideConf.HeadTitle) then
			TitleName = RpcEntity.Data.OverrideConf.HeadTitle
		end
		self:RefreshTitleByText(TitleName)
    end
end

function P_TaskCollectHeadTitle:OnDistanceUpdate(Distance)
    -- if Distance >= P_TaskCollectHeadTitle.NPCDistance[1] * 100 and not self.AnimHidden then
    --     self.AnimHidden = true
    --     self:StopAllAnimations()
    --     self:PlayAnimation(self.view.Ani_SubNameR)

    -- elseif Distance < P_TaskCollectHeadTitle.NPCDistance[1] * 100 and self.AnimHidden then
    --     self.AnimHidden = false
    --     if _G.GPUInstanceHeadInfo then
    --         self.view.SB_SubName:SetRenderOpacity(1)
    --     end
    --     self:StopAllAnimations()
    --     self:PlayAnimation(self.view.Ani_SubName)
    -- end
end

function P_TaskCollectHeadTitle:OnUnbindActor()
    self.EntityID = nil
    self:StopAllAnimations()
    if self.ListenEntityInsID then
        Game.UniqEventSystemMgr:RemoveListener(self.ListenEntityInsID, EEventTypesV2.LSCENEACTOR_HEAD_TITLE_CHANGED, "OnDisplayTitleNameChanged", self)
        self.ListenEntityInsID = nil
    end

end

return P_TaskCollectHeadTitle
