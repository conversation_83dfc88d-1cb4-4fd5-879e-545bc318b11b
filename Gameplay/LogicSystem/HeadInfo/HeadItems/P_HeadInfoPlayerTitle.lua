
local P_HeadInfoItemBase = kg_require"Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoPlayerTitle= DefineClass("P_HeadInfoPlayerTitle",P_HeadInfoItemBase)
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")
local Redirection = kg_require("Data.Config.UI.RedirectTextureConfig")
local TitleSystem = Game.TitleSystem

function P_HeadInfoPlayerTitle:ctor()
    self.EntityID = nil
    self.titleType = Const.TITLE_TYPE.TITLE
    Log.DebugFormat("P_HeadInfoPlayerTitle:ctor")   
end

function P_HeadInfoPlayerTitle:OnBindActor(EntityID)
    self:AddListenerV2(EEventTypesV2.ON_WEAR_TILE_UPDATE, "OnWearTitleUpdate", self)

    self.EntityID = EntityID
    Log.DebugFormat("P_HeadInfoPlayerTitle:OnBindActor EntityID: %s", EntityID)   
    self:RefreshTitleText()
end

function P_HeadInfoPlayerTitle:OnUnbindActor()
    Game.EventSystem:RemoveObjListeners(self)
    self.EntityID = nil
    self:StopAllAnimations()
end

function P_HeadInfoPlayerTitle:OnWearTitleUpdate(type, entityID)
    if self:entityIsSelf(entityID) then
        self:RefreshTitleText()
    end
end

function P_HeadInfoPlayerTitle:entityIsSelf(entityID)
    --self.EntityID是整型，event发来的却是字符串，所以这里只能用这种方法来对比
    local selfEntity = Game.EntityManager:getEntity(self.EntityID)
    local inEntity = Game.EntityManager:getEntity(entityID) 
    return selfEntity == inEntity 
end

function P_HeadInfoPlayerTitle:RefreshTitleText()
	local entity = self:getEntity()
	
	-- 兼容机器人称号,后续可以走称号表id
	if entity and entity.isBot and entity.BotID > 0 then
		local titleName = Game.TableData.GetBattleBotTemplateDataRow(entity.BotID).Title
		if titleName == nil or titleName == "" then
			self:Hide(ESlateVisibility.Collapsed)
		else
			self.view.Text_Name_lua:SetText(titleName)
		end
		return
	end
	
    local titleID = TitleSystem:GetWearTitleID(self.titleType, entity)
    if TitleSystem:IsEmptyTitleID(titleID) then
        Log.DebugFormat("hide title")
        self:Hide(ESlateVisibility.Collapsed)
        return
    end
    Log.DebugFormat("show title")
    self:Show(ESlateVisibility.SelfHitTestInvisible)
   
    local titleTableData = TitleSystem:GetTableDataRowByType(titleID, self.titleType)
    if titleTableData then
        --称号文字
        self.view.Text_Name_lua:SetText(TitleSystem:GetWearTitleName(self.titleType, self:getEntity()))
        --背景图片
        self:setWidgetImage(self.view.Img_Bg_lua, titleTableData.TitleBackgroundPath)
        --装饰图片
        self:setWidgetImage(self.view.WBP_HeadInfo_Honorific_lua.Img_Icon, titleTableData.TitleIconPath)
        -- 称号后缀
        self:setWidgetImage(self.view.WBP_HeadInfo_Honorific_lua_1.Img_Icon, titleTableData.TitleIconPathTail)
    else
        Log.WarningFormat("can not find titleID %d table data", titleID)
    end
end

function P_HeadInfoPlayerTitle:setWidgetImage(widget, iconName)
    local iconPath = Game.UIIconUtils.getIcon(iconName)
    if iconPath then
        iconPath = Redirection.Get(iconPath)
        widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:SetImage(widget, iconPath)
    else
        widget:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_HeadInfoPlayerTitle:getEntity()
    if self.EntityID == GetMainPlayerEID() then
        return Game.me
    end
    --其他角色
    local Entity = Game.EntityManager:getEntityWithBrief(self.EntityID)
    return Entity
end 

return P_HeadInfoPlayerTitle
