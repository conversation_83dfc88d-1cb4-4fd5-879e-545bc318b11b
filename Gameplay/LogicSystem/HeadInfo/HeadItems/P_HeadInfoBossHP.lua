local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local CommonBarHelper = kg_require "Gameplay.LogicSystem.CommonUI.CommonBarHelper_Image"
local P_HeadInfoBarBreakDefense = kg_require "Gameplay.LogicSystem.HeadInfo.HeadItems.P_HeadInfoBarBreakDefense"
local P_HeadInfoBossHP = DefineClass("P_HeadInfoBossHP", P_HeadInfoItemBase)
local ESlateVisibility = import("ESlateVisibility")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
function P_HeadInfoBossHP:ctor()
    self.EntityID = nil
    --self.P_HPBar = nil

    -- self.BuffList = {}
    self.HpBarHelper = nil
    -- self.P_BuffIcon = nil
    self.TickTimer = nil
    -- self.battleRelationType = nil

    --self.View.WBP_HeadInfoBossShieldBar:SetVisibility(ESlateVisibility.Collapsed)
    self.P_BarBreakDefense = self:CreateComponent(self.view.WBP_HeadInfoBossShieldBar_lua, P_HeadInfoBarBreakDefense)
end

function P_HeadInfoBossHP:OnBindActor(EntityID)
    self:BindHeadMessage("OnSelectionChanged", "OnSelectionChanged")
    self:BindHeadMessage("OnDisplayNameChanged", "RefreshNameText")

    self.ActEntityIDor = EntityID
    self.HpBarHelper:BindEntity(self.EntityID, "Hp", "MaxHp", "CurrentMaxHp", "aShield")
    self.P_BarBreakDefense:BindActor(self.EntityID)
    local RpcEntity = self:GetAttachedEntity()
    Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.OnCampChanged, RpcEntity.eid)
    if RpcEntity.BossType == _G.Enum.EBossType.BOSS then
		Game.UniqEventSystemMgr:AddListener(RpcEntity.eid, EEventTypesV2.BATTLE_TAKE_DAMAGE, "OnTakeDamage", self)
    end
    local OnSelFlag = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnSelected)
    if OnSelFlag then
        self:OnSelectionChanged(OnSelFlag:IsSelected())
    else
        self:OnSelectionChanged(false)
    end
    self:RefreshNameText()

    self:TintMonsterNameCampColor()
    self.view.WBP_HeadInfoBossHpBar_lua.PB_HPFlash_lua:SetRenderOpacity(0)

    if self.TickTimer then
        Game.TimerManager:StopTimerAndKill(self.TickTimer)
        self.TickTimer = nil
    end
    self.TickTimer = Game.TimerManager:CreateTimerAndStart(function(DeltaTime)
            self.HpBarHelper:Tick(DeltaTime)
        end,
            10, -1, nil, nil, true)
end

function P_HeadInfoBossHP:OnCampChanged()
    self:TintMonsterNameCampColor()
end

function P_HeadInfoBossHP:OnTakeDamage(damageCtx)
    if damageCtx.InstigatorID == Game.me.eid and not damageCtx.IsHeal then
        self:PlayAnimation(self.view.WBP_HeadInfoBossHpBar_lua.Ani_Flash, nil, self.view.WBP_HeadInfoBossHpBar_lua)
    end
end

function P_HeadInfoBossHP:RefreshNameText()
    local RpcEntity = self:GetAttachedEntity()
    local displayName = RpcEntity.DisplayName or ""
    if string.isEmpty(displayName) then
        local MonsterTableData  = RpcEntity:GetEntityConfigData()

        if MonsterTableData then
			if MonsterTableData.bGhost then
				local entity = BSFunc.GetActorInstigator(RpcEntity)
				displayName = entity.Name
				if string.isEmpty(displayName) then
					local ownerTableData = entity:GetEntityConfigData()
					displayName = ownerTableData and ownerTableData.Name or ""
				end
			else
				displayName = MonsterTableData.Name
			end
        end
    end
    self.view.MonsterName_lua:SetText(displayName)
end

function P_HeadInfoBossHP:OnSelectionChanged(isSelected)
    self.userWidget.Selected = isSelected
    local BossType = self:GetAttachedEntity().BossType
    BossType = math.max(0, BossType - 1)
    local isFriendly = self:GetCampRelation(self.EntityID) ~= Enum.ECampEnumData.Enemy
    self.view.WBP_HeadInfoBossHpBar_lua:Event_UI_Style(BossType, isSelected, isFriendly)
    self.view.WBP_HeadInfoBossShieldBar_lua:Event_UI_Style(isSelected)
    self.userWidget:Event_UI_Style(isFriendly, isSelected)
    if isSelected then
        self:PlayAnimation(self.view.WBP_HeadInfoBossHpBar_lua.Ani_Light,
            function()
                self:PlayAnimation(self.view.WBP_HeadInfoBossHpBar_lua.Ani_Loop, nil, self.view.WBP_HeadInfoBossHpBar_lua)
            end, self.view.WBP_HeadInfoBossHpBar_lua)
    else
        self:StopAnimation(self.view.WBP_HeadInfoBossHpBar_lua.Ani_Light, self.view.WBP_HeadInfoBossHpBar_lua)
        self:StopAnimation(self.view.WBP_HeadInfoBossHpBar_lua.Ani_Loop, self.view.WBP_HeadInfoBossHpBar_lua)
    end
    --self:StopAnimation(self.view.WBP_HeadInfoBossHpBar_lua, self.view.WBP_HeadInfoBossHpBar_lua.Ani_Flash, false, false)
end

function P_HeadInfoBossHP:OnUnbindActor()
	local RpcEntity = self:GetAttachedEntity()
	if RpcEntity then
		Game.UniqEventSystemMgr:RemoveListener(RpcEntity.eid, EEventTypesV2.BATTLE_TAKE_DAMAGE, "OnTakeDamage", self)
	end
    self.HpBarHelper:UnBindEntity()
    self.P_BarBreakDefense:UnBindActor()
    self.EntityID = nil
    Game.EventSystem:RemoveObjListeners(self)
    if self.TickTimer then
        Game.TimerManager:StopTimerAndKill(self.TickTimer)
        self.TickTimer = nil
    end

    -- self:UnInitHPBarView()
end

function P_HeadInfoBossHP:OnInitItem(Params)
    self.HpBarHelper = CommonBarHelper.new({
        IncreaseRate = Game.TableData.GetConstDataRow("HP_BAR_INCREASE_RATE"),
        DMGReduceRate = Game.TableData.GetConstDataRow("HP_BAR_DMG_REDUCE_RATE"),
        DMGReduceDelay = 0--Game.TableData.GetConstDataRow("HP_BAR_DMG_REDUCE_DELAY")
    })

    self.HpBarHelper:BindWidget(
        self.view.WBP_HeadInfoBossHpBar_lua,
        self.view.WBP_HeadInfoBossHpBar_lua.PB_HP_lua,
        self.view.WBP_HeadInfoBossHpBar_lua.PB_NextHP_lua,
        self.view.WBP_HeadInfoBossHpBar_lua.PB_Damage_lua,
        nil,
        self.view.WBP_HeadInfoBossHpBar_lua.PB_HPFlash_lua
    )
end

function P_HeadInfoBossHP:GetCampRelation(eid)
    local MainCharacterEntity = Game.me
    local TargetEntity = Game.EntityManager:getEntity(eid)
	local finalOwner = BSFunc.GetActorInstigator(TargetEntity)
    return BSFunc.GetFinalCampRelationByEntity(MainCharacterEntity, finalOwner)
end

function P_HeadInfoBossHP:TintMonsterNameCampColor(Color)
    local CampInfo = self:GetCampRelation(self.EntityID)

    if nil == Color then
        if CampInfo == Enum.ECampEnumData.Friendly then
            Color = Game.ColorManager:GetColor("Common", "LockHP_Friend", Game.ColorManager.Type.SlateColor)
        elseif CampInfo == Enum.ECampEnumData.Neutral then
            Color = Game.ColorManager:GetColor("Common", "LockHP_Neutrality", Game.ColorManager.Type.SlateColor)
        elseif CampInfo == Enum.ECampEnumData.Enemy then
            Color = Game.ColorManager:GetColor("Common", "LockHP_Enemy", Game.ColorManager.Type.SlateColor)
        else
            Color = Game.ColorManager:GetColor("Common", "LockHP_Friend", Game.ColorManager.Type.SlateColor)
        end
    end
    if Color then
        self.view.MonsterName_lua:SetColorAndOpacity(Color)
    end
end

function P_HeadInfoBossHP:OnUninit()

end

return P_HeadInfoBossHP
