local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoDirectorDisplayNpcName= DefineClass("P_HeadInfoDirectorDisplayNpcName",P_HeadInfoItemBase)

function P_HeadInfoDirectorDisplayNpcName:OnBindActor(EntityID)
    self.EntityID = EntityID
    local RarityColor = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
    self.view.Text_Detail_lua:SetColorAndOpacity(RarityColor)
    local DisplayName = ""
    local Entity = self:GetAttachedEntity()
    if Entity then
        local NpcData = Entity:GetEntityConfigData()
        if NpcData then
            DisplayName = NpcData.Name
        end
    end
    self.view.Text_Detail_lua:SetText(DisplayName)
end

function P_HeadInfoDirectorDisplayNpcName:OnUnbindActor()

end

return P_HeadInfoDirectorDisplayNpcName
