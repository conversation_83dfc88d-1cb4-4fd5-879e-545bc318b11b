local ESlateVisibility = import("ESlateVisibility")
local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoBubble = DefineClass("P_HeadInfoBubble", P_HeadInfoItemBase)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

P_HeadInfoBubble.EBubbleType = {
    Normal = 0,
    Warning = 1,
    Witch = 2
}

function P_HeadInfoBubble:ctor()
    self.EntityID = nil
    self.BubbleType = P_HeadInfoBubble.EBubbleType.Normal
end

function P_HeadInfoBubble:OnBindActor(EntityID)
    self:BindHeadMessage("OnBubbleMsg", "OnBubbleMsg")
    self.EntityID = EntityID

    local FlagInst = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnShowBubble)
    if FlagInst then
        local Msg = FlagInst:GetCurrentBubbleText()
        local type = FlagInst:GetCurrentBubbleType()
        if Msg then
            self:PlayBubble(Msg, type)
        else
            self.view.WBP_HeadBubbleWarning_lua:SetVisibility(ESlateVisibility.Collapsed)
            self.view.WBP_NPCBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
            self.view.WBP_HeadInfoRolePlayBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
            Log.Warning("Nil Bubble Text!")
        end
    end
end

function P_HeadInfoBubble:OnBubbleMsg(message, bubbleType)
    if message then
        self:PlayBubble(message, bubbleType)
    else
        self.view.WBP_HeadBubbleWarning_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_NPCBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_HeadInfoRolePlayBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
        Log.Warning("Nil Bubble Text!")
    end
end

function P_HeadInfoBubble:PlayBubble(message, bubbleType)
    self.BubbleType = bubbleType
    if not self.BubbleType then
        self.BubbleType = P_HeadInfoBubble.EBubbleType.Normal
    end
    if self.BubbleType == P_HeadInfoBubble.EBubbleType.Normal then
        self.view.WBP_HeadBubbleWarning_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_NPCBubble_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.view.WBP_HeadInfoRolePlayBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
        if message ~= nil then
            self.view.WBP_NPCBubble_lua.Text_Bubble_lua:SetText(Game.NPCManager.GetFormatTalkText(message))
        end
        if self:GetFadeInAni() then
            self:PlayAni(self.view.WBP_NPCBubble_lua, self:GetFadeInAni(), 1, false, EUMGSequencePlayMode.Forward)
        end
        
    elseif self.BubbleType == P_HeadInfoBubble.EBubbleType.Warning then
        self.view.WBP_HeadBubbleWarning_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.view.WBP_NPCBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_HeadInfoRolePlayBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
        if message ~= nil then
            self.view.WBP_HeadBubbleWarning_lua.Text_lua:SetText(Game.NPCManager.GetFormatTalkText(message))
        end
        ---@brief 播放入场动画
        if self:GetFadeInAni() then
            self:PlayAni(self.view.WBP_HeadBubbleWarning_lua, self:GetFadeInAni(), 1, false, EUMGSequencePlayMode.Forward, function()
                if self.view.WBP_HeadBubbleWarning_lua.Ani_Tips_Loop then
                    self:PlayAni(self.view.WBP_HeadBubbleWarning_lua, self.view.WBP_HeadBubbleWarning_lua.Ani_Tips_Loop, 1, false, EUMGSequencePlayMode.Forward)
                end
            end)
        end
    elseif self.BubbleType == P_HeadInfoBubble.EBubbleType.Witch then
        self.view.WBP_HeadInfoRolePlayBubble_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.view.WBP_NPCBubble_lua:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_HeadBubbleWarning_lua:SetVisibility(ESlateVisibility.Collapsed)
        if message ~= nil then
            self.view.WBP_HeadInfoRolePlayBubble_lua.Text_Content_lua:SetText(Game.NPCManager.GetFormatTalkText(message))
        end
        ---@brief 播放入场动画
        if self:GetFadeInAni() then
            self:PlayAni(self.view.WBP_HeadInfoRolePlayBubble_lua, self:GetFadeInAni(), 1, false, EUMGSequencePlayMode.Forward)
        end
    end
end

function P_HeadInfoBubble:OnUnbindActor()
end

function P_HeadInfoBubble:OnInitItem(Params)
end

function P_HeadInfoBubble:PlayAni(widget, inAni, Speed, bloop, PlayMode, onComplete, args)
    if not inAni then
        Log.WarningFormat("[P_HeadInfoBubble]: Not Ani found! Plz check the Ani name")
        return
    end
    
    if not widget then
        Log.WarningFormat("[P_HeadInfoBubble]: Not widget found!")
        return
    end

    if onComplete and not bloop then
        -- luacheck: push ignore
        local callback = function()
            self._animationBindings = nil
            onComplete(args)
        end
        -- luacheck: pop
        self:StopTimer("Ani")
        self:StartTimer("Ani", callback, 1000 * (inAni:GetEndTime() - inAni:GetStartTime()) / Speed, 1)
    end

    widget:PlayAnimation(inAni, 0.0, bloop and 0 or 1, PlayMode, Speed, false)
end

function P_HeadInfoBubble:GetFadeInAni()
    if self.BubbleType == P_HeadInfoBubble.EBubbleType.Warning then
        if self.view.WBP_HeadBubbleWarning_lua.Ani_FadeIn then
            return self.view.WBP_HeadBubbleWarning_lua.Ani_FadeIn
        end

        if self.view.WBP_HeadBubbleWarning_lua.Ani_Fadein then
            return self.view.WBP_HeadBubbleWarning_lua.Ani_Fadein
        end
    elseif self.BubbleType == P_HeadInfoBubble.EBubbleType.Witch then
        if self.view.WBP_HeadInfoRolePlayBubble_lua.Ani_Fadein then
            return self.view.WBP_HeadInfoRolePlayBubble_lua.Ani_Fadein
        end
    else 
        --Normal
        return self.view.WBP_NPCBubble_lua.Ani_Bubble
    end
    return nil
end

function P_HeadInfoBubble:GetFadeOutAni()
    if self.BubbleType == P_HeadInfoBubble.EBubbleType.Warning then
        if self.view.WBP_HeadBubbleWarning_lua.Ani_FadeOut then
            return self.view.WBP_HeadBubbleWarning_lua.Ani_FadeOut
        end

        if self.view.WBP_HeadBubbleWarning_lua.Ani_Fadeout then
            return self.view.WBP_HeadBubbleWarning_lua.Ani_Fadeout
        end
    elseif self.BubbleType == P_HeadInfoBubble.EBubbleType.Witch then
        if self.view.WBP_HeadInfoRolePlayBubble_lua.Ani_Fadeout then
            return self.view.WBP_HeadInfoRolePlayBubble_lua.Ani_Fadeout
        end
    else 
        --Normal
        return self.view.WBP_NPCBubble_lua.Ani_Bubble
    end
    return nil
end

function P_HeadInfoBubble:DelayHide(callback)
    if self:GetFadeOutAni() then
        if self.BubbleType ==  P_HeadInfoBubble.EBubbleType.Warning then
            self:PlayAni(self.view.WBP_HeadBubbleWarning_lua, self:GetFadeOutAni(), 1, false, EUMGSequencePlayMode.Forward,callback)
        elseif self.BubbleType ==  P_HeadInfoBubble.EBubbleType.Witch then
            self:PlayAni(self.view.WBP_HeadInfoRolePlayBubble_lua, self:GetFadeOutAni(), 1, false, EUMGSequencePlayMode.Forward,callback)
        else
            self:PlayAni(self.view.WBP_NPCBubble_lua, self:GetFadeOutAni(), 1, false, EUMGSequencePlayMode.Reverse,callback)
        end
    elseif callback then
        callback()
    end
end

return P_HeadInfoBubble
