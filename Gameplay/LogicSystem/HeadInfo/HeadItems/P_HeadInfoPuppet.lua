local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoPuppet = DefineClass("P_HeadInfoPuppet", P_HeadInfoItemBase)

function P_HeadInfoPuppet:ctor()
end

function P_HeadInfoPuppet:OnBindActor(EntityID)
    self.EntityID = EntityID
    local HeadSigFlag = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnDisplayPuppet)
	if HeadSigFlag then
        self:OnRefreshPuppet()
    end
end

-- 这里做ui的刷新操作
function P_HeadInfoPuppet:OnRefreshPuppet()
    --self:PlayAnimation(self.view.Ani_open)
end

function P_HeadInfoPuppet:OnUnbindActor()
    self.EntityID = nil
end

function P_HeadInfoPuppet:OnUninit()

end

return P_HeadInfoPuppet
