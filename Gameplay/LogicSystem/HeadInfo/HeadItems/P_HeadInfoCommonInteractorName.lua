local P_HeadInfoItemBase = kg_require("Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase")

local P_HeadInfoCommonInteractorName= DefineClass("P_HeadInfoCommonInteractorName", P_HeadInfoItemBase)

function P_HeadInfoCommonInteractorName:OnBindActor(EntityID)
    local Entity = Game.EntityManager:getEntity(EntityID)
    local NameText = Entity:GetDisplayName()
    self:RefreshNameByText(NameText)
    Game.UniqEventSystemMgr:AddListener(self.EntityID, EEventTypesV2.ON_COMMON_INTERACTOR_DISPLAY_NAME_CHANGED, "OnDisplayNameChanged", self)
end

function P_HeadInfoCommonInteractorName:OnUnbindActor()
	Game.UniqEventSystemMgr:RemoveListener(self.EntityID, EEventTypesV2.ON_COMMON_INTERACTOR_DISPLAY_NAME_CHANGED, "OnDisplayNameChanged", self)
end

function P_HeadInfoCommonInteractorName:OnDisplayNameChanged(NewDisplayName)
    self:RefreshNameByText(NewDisplayName)
end

function P_HeadInfoCommonInteractorName:RefreshNameByText(Name)
    local RarityColor = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
    self.view.Text_Name:SetText(Name)
    self.view.Text_Name:SetColorAndOpacity(RarityColor)
end

return P_HeadInfoCommonInteractorName