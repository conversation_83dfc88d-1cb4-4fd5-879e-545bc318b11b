local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local P_HeadInfoItemBase = kg_require"Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoTitle= DefineClass("P_HeadInfoTitle",P_HeadInfoItemBase)
local ESlateVisibility = import("ESlateVisibility")

P_HeadInfoTitle.NPCDistance = {
    Game.TableData.GetNPCInteractDataRow(1).TitleDistance1,
}

function P_HeadInfoTitle:ctor()
    self.EntityID = nil
    self.BeUseTitle = nil
    self.AnimHidden = nil
end

function P_HeadInfoTitle:OnBindActor(EntityID)
	--self:BindHeadMessage("OnDistanceUpdate", "OnDistanceUpdate")
    self:BindHeadMessage("OnCharacterMorph", "RefreshTitleText")
    self:BindHeadMessage("RefreshTitleText", "RefreshTitleText")

    self.EntityID = EntityID

    self:RefreshTitleText()

    local SlotCfg=  self:GetSlotConfig()
    self.view.SB_SubName:SetHeightOverride(40)
    self.view.SB_SubName:SetRenderOpacity(1)
    self.AnimHidden = false
    self.view.HB_NPCTitle:SetRenderOpacity(1)
    self.view.HB_NPCTitle:SetRenderTranslation(FVector2D(0, 0))
end

function P_HeadInfoTitle:RefreshTitleText()
    self.view.HB_NPCTitle:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local RpcEntity = self:GetAttachedEntity()
    local MorphID = RpcEntity.MorphID
    local MorphData = Game.TableData.GetMorphDataRow(MorphID)
    if MorphData then
        -- 变身时显示变身配置的头衔
        self.view.RTB_NpcTitle:SetText(MorphData.Title)
    elseif RpcEntity.isAvatar == true then
        -- 玩家不显示头衔
        self.view.HB_NPCTitle:SetVisibility(ESlateVisibility.Collapsed)
    elseif RpcEntity.isNpc == true then
        --- Npc优先使用属性上的HeadTitle，如果没有则读表
		local title = RpcEntity.HeadTitle
		if not title then
			-- NPC显示配置的头衔
			local NPDData = RpcEntity:GetEntityConfigData()
			if NPDData then
				title = NPDData.Title
			end
		end
		if not string.isEmpty(title) then
			self.view.RTB_NpcTitle:SetText(title)
			self.BeUseTitle = true
		else
			self.BeUseTitle = false
		end
    end
end

function P_HeadInfoTitle:OnDistanceUpdate(Distance)
    -- if Distance >= P_HeadInfoTitle.NPCDistance[1] * 100 and not self.AnimHidden then
    --     self.AnimHidden = true
    --     self:StopAllAnimations()
    --     self:PlayAnimation(self.view.Ani_SubNameR)

    -- elseif Distance < P_HeadInfoTitle.NPCDistance[1] * 100 and self.AnimHidden then
    --     self.AnimHidden = false
    --     if _G.GPUInstanceHeadInfo then
    --         self.view.HB_NPCTitle:SetRenderOpacity(1)
    --     end
    --     self:StopAllAnimations()
    --     self:PlayAnimation(self.view.Ani_SubName)
    -- end
end

function P_HeadInfoTitle:OnUnbindActor()
    self.EntityID = nil
    self:StopAllAnimations()
end

return P_HeadInfoTitle
