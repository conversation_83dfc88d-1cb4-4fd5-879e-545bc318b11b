local ESlateVisibility = import("ESlateVisibility")
local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"

local P_HeadInfoBuff = DefineClass("P_HeadInfoBuff", P_HeadInfoItemBase)

local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

P_HeadInfoBuff.CommonWBPPool = {}
P_HeadInfoBuff.CommonTexCache = {}
P_HeadInfoBuff.CommonTexCacheCount = 0
P_HeadInfoBuff.MaxContentSize = 3

local MAX_TEX_CACHE = 15
local MAX_POOL_NUM = 15

function P_HeadInfoBuff.InnerGetFromPool(Name, PoolName)
    for K, V in pairs(P_HeadInfoBuff[PoolName]) do
        if V.Name == Name then
            table.remove(P_HeadInfoBuff[PoolName], K)
            slua.removeRef(V.Item)
            return V.Item
        end
    end
end

function P_HeadInfoBuff.InnerReturnToPool(Name, Item, PoolName)
    slua.addRef(Item)
    table.insert(P_HeadInfoBuff[PoolName], {
        Name = Name,
        Item = Item,
    })

    if #P_HeadInfoBuff[PoolName] > MAX_POOL_NUM then
        table.remove(P_HeadInfoBuff[PoolName], 1)
    end
end

function P_HeadInfoBuff.GetFromPool(Name)
    P_HeadInfoBuff.InnerGetFromPool(Name, "CommonWBPPool")
end

function P_HeadInfoBuff.ReturnToPool(Name, Item)
    if Item and IsValid_L(Item) then
        Item:RemoveFromParent()
        P_HeadInfoBuff.InnerReturnToPool(Name, Item, "CommonWBPPool")
    end
end

function P_HeadInfoBuff:ctor()
    self.CurrentBuffID = -1
    self.BuffWBP = nil
    self.EntityID = nil
    self.LayerTextures = nil
end

function P_HeadInfoBuff:GetHighestPriorityTasks(DisplayTasks)
    local TopPriorityTask = nil
    local ContentSize = 0
    local TopTaskPriority = 99999

    table.sort(DisplayTasks, function(TaskA, TaskB)
    local BuffDisplayDataA = Game.TableData.GetOverHeadBuffDisplayDataRow(TaskA.ConfigID)
    local BuffDisplayDataB = Game.TableData.GetOverHeadBuffDisplayDataRow(TaskB.ConfigID)
    if BuffDisplayDataA and BuffDisplayDataB then
        return BuffDisplayDataA.Priority < BuffDisplayDataB.Priority
    elseif BuffDisplayDataA then
        return true
    else
        return false
    end
    end)
    local ResTasks = {}
    for _, Task in pairs(DisplayTasks) do
        local BuffDisplayData = Game.TableData.GetOverHeadBuffDisplayDataRow(Task.ConfigID)
        if BuffDisplayData then
            if BuffDisplayData.Size + ContentSize <= P_HeadInfoBuff.MaxContentSize then
                ResTasks[Task.ConfigID] = {TaskInstanceInfo = Task}
                ContentSize = ContentSize + BuffDisplayData.Size
            else
                break
            end
        else
            break
        end
    end
    return ResTasks
end

---todo BriefActor走另一套头顶配置
function P_HeadInfoBuff:GetDisplayLayer(BuffID, InstigatorID)
    local RpcEntity = self:GetAttachedEntity()
    if not RpcEntity or RpcEntity.isBriefEntity then
        return 0
    end
	local layer = 0
	if InstigatorID == nil then
		layer = RpcEntity:GetBuffLayerNew(BuffID)
	else
		--有source优先取source
		layer = RpcEntity:GetBuffLayerNew(BuffID, InstigatorID)
		if layer == 0 then
			layer = RpcEntity:GetBuffLayerNew(BuffID)
		end
	end
	return layer
	 
end

function P_HeadInfoBuff:OnBuffListChanged(DisplayTasks)
    self:SetBuffDisplay(self:GetHighestPriorityTasks(DisplayTasks))
end

function P_HeadInfoBuff:SetBuffDisplay(TaskTable)
    for ConfigID, TaskInfo in pairs(self.CurrentTasks) do
        if not TaskTable[ConfigID] then
            self:RemoveTaskFromLayer(ConfigID)
        end
    end 
    local NewCurrentTasks = {}
    for ConfigID, TaskInfo in pairs(TaskTable) do
        if not self.CurrentTasks[ConfigID] then
            local Succ = self:LoadTaskToLayer(TaskInfo)
            if Succ then
                NewCurrentTasks[ConfigID] = TaskInfo
            end
        else
            self:SetIconDisplay(self.CurrentTasks[ConfigID], self:GetDisplayLayer(self.CurrentTasks[ConfigID].TaskInstanceInfo.BuffAssetID, self.CurrentTasks[ConfigID].TaskInstanceInfo.BuffInstigatorID))
            NewCurrentTasks[ConfigID] = self.CurrentTasks[ConfigID]
        end
    end
    self.CurrentTasks = NewCurrentTasks
end

function P_HeadInfoBuff:OnAddBuffRecord(InType, InID, ExtraData)
    if InType == ETE.EBSABuffRecord.AddLayer or InType == ETE.EBSABuffRecord.RemoveLayer then
        for ConfigID, TaskInfo in pairs(self.CurrentTasks) do
            if TaskInfo.TaskInstanceInfo.BuffAssetID == InID then
                if TaskInfo.view and IsValid_L(TaskInfo.view) then
                    self:SetIconDisplay(TaskInfo, self:GetDisplayLayer(InID, TaskInfo.TaskInstanceInfo.BuffInstigatorID))
                end
            end
        end
    end
end


function P_HeadInfoBuff:SetIconDisplay(Task, Layer)
    local WidgetRoot = Task.view
    local LayerTextures = Game.TableData.GetOverHeadBuffDisplayDataRow(Task.TaskInstanceInfo.ConfigID).LayerTextures
    if WidgetRoot then
        WidgetRoot:SetVisibility(ESlateVisibility.Visible)

        if WidgetRoot["On Buff Layer Changed"] then
            WidgetRoot["On Buff Layer Changed"](WidgetRoot, Layer)
        end
        if WidgetRoot.Img_Buff_lua and LayerTextures[Layer] then
            self:SetImage(WidgetRoot.Img_Buff_lua, LayerTextures[Layer])
        end
    else
        WidgetRoot:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_HeadInfoBuff:RemoveTaskFromLayer(ConfigID)
    if self.CurrentTasks[ConfigID] and self.CurrentTasks[ConfigID].view and IsValid_L(self.CurrentTasks[ConfigID].view) then
        if self:GetFadeOutAni(self.CurrentTasks[ConfigID].view) then
            self.PendingDeleteTasks[ConfigID] = self.CurrentTasks[ConfigID]
            self:PlayAnimation(self:GetFadeOutAni(self.CurrentTasks[ConfigID].view),  function ()
            P_HeadInfoBuff.ReturnToPool(self.PendingDeleteTasks[ConfigID].WBPName, self.PendingDeleteTasks[ConfigID].view)
            self.PendingDeleteTasks[ConfigID] = nil
            end, self.CurrentTasks[ConfigID].view )
        else
            P_HeadInfoBuff.ReturnToPool(self.CurrentTasks[ConfigID].WBPName, self.CurrentTasks[ConfigID].view)
        end
    end
end

function P_HeadInfoBuff:LoadTaskToLayer(Task)
    local BuffDisplayData = Task ~= nil and Game.TableData.GetOverHeadBuffDisplayDataRow(Task.TaskInstanceInfo.ConfigID) or nil
    if BuffDisplayData then
        local wbp = BuffDisplayData.AnimationWBP
        if _G.GPUInstanceHeadInfo then
            wbp = string.gsub(wbp, "/HeadInfo/", "/HeadInfoUI/")
        end

        Task.view = P_HeadInfoBuff.GetFromPool(wbp)
        if not Task.view then
            local BuffWBP = wbp
            if not string.endsWith(BuffWBP, "_C") then
                BuffWBP = BuffWBP .. "_C"
            end
            BuffWBP = slua.loadClass(BuffWBP)
            Task.view = import("WidgetBlueprintLibrary").Create(_G.GetContextObject(), BuffWBP)
        end
        if Task.view then
            self.view.HB_BuffContent:AddChildToHorizontalBox(Task.view)
            Task.WBPName = wbp
        end

        local Layer = self:GetDisplayLayer(Task.TaskInstanceInfo.BuffAssetID, Task.TaskInstanceInfo.BuffInstigatorID)
        self:SetIconDisplay(Task, Layer)

		local onLayerChangeFunc = Task.view[BuffDisplayData.OnBuffLayerChangeFunc]
		if onLayerChangeFunc then
			onLayerChangeFunc(Task.view, Layer - 1)
		end

        --bug 有时暂时缺失BuffWBP文件
        if Task.view and self:GetFadeInAni(Task.view) then
            --TODO： Add Ani , 不可使用 self.view.WidgetRoot, 替换成 self.BuffWBP
            self:PlayAnimation(self:GetFadeInAni(Task.view),
                    function()
                        --TODO: Fix ,可能循环动画不叫Ani_Loop
                        if Task.view.Ani_Loop then
                            self:PlayAnimation(Task.view.Ani_Loop, nil, Task.view,0,0)
                        end
                    end, Task.view)
        else
            Log.WarningFormat("[P_HeadInfoBuff] failed to get the buff wbp")
        end
    end
    if Task.view then return true end
end

function P_HeadInfoBuff:OnBindActor(EntityID)
    self:BindHeadMessage("OnBuffListChanged", "OnBuffListChanged")
    self.EntityID = EntityID
    self.CurrentTasks = {}
    self.PendingDeleteTasks = {}

    local BuffList
    local FlagInst = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnDisplayBuff)
    if FlagInst then
        BuffList = FlagInst:GetBuffList()
    end

    if not BuffList then
        return
    end

    self:SetBuffDisplay(self:GetHighestPriorityTasks(BuffList))
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity and not RpcEntity.isBriefEntity then
        RpcEntity.OnAddBuffRecord:Add(self, "OnAddBuffRecord")
    end

end

function P_HeadInfoBuff:OnUnbindActor()
    for ConfigID, TaskInfo in pairs(self.CurrentTasks or {}) do
        P_HeadInfoBuff.ReturnToPool(TaskInfo.WBPName, TaskInfo.view)
    end

    for ConfigID, TaskInfo in pairs(self.PendingDeleteTasks or {}) do
        P_HeadInfoBuff.ReturnToPool(TaskInfo.WBPName, TaskInfo.view)
    end
    self.CurrentTasks = nil
    self.PendingDeleteTasks = nil

    Game.EventSystem:RemoveObjListeners(self)
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity and not RpcEntity.isBriefEntity then
        RpcEntity.OnAddBuffRecord:RemoveObject(self)
    end

    self.EntityID = nil
end

function P_HeadInfoBuff:GetObjectNum()
    local WBPObjNum = 0
    if self.BuffWBP and IsValid_L(self.BuffWBP) then
        WBPObjNum = UIHelper.GetObjectNum(self.BuffWBP)
    end
    return UIHelper.GetObjectNum(self.view.WidgetRoot) + WBPObjNum
end

function P_HeadInfoBuff:StopAni(inAni)
    if self._animationBindings then
        self:StopTimer("Ani")
        self._animationBindings = nil
    end
    self.BuffWBP:StopAnimation(inAni)
end

function P_HeadInfoBuff:GetFadeInAni(WidgetRoot)
    if not WidgetRoot or not IsValid_L(WidgetRoot) then
        Log.WarningFormat("[P_HeadInfoBuff] No BuffWBP found in this head buff")
        return nil
    end
    if WidgetRoot.Ani_FadeIn then
        return WidgetRoot.Ani_FadeIn
    end
    if WidgetRoot.Ani_Fadein then
        return WidgetRoot.Ani_Fadein
    end
    return nil
end

function P_HeadInfoBuff:GetFadeOutAni(WidgetRoot)
    if not WidgetRoot then
        for ConfigID, TaskInfo in pairs(self.CurrentTasks) do
            if TaskInfo.view.Ani_FadeOut then
                return true
            end
        end
        --Log.WarningFormat("[P_HeadInfoBuff] No BuffWBP found in this head buff")
        return nil
    end
    if WidgetRoot.Ani_FadeOut then
        return WidgetRoot.Ani_FadeOut
    end
    if WidgetRoot.Ani_Fadeout then
        return WidgetRoot.Ani_Fadeout
    end
    return nil
end

function P_HeadInfoBuff:DelayHide(callback)
    local LongestAnimWBP = nil
    local LongestAnimTime = -1
    for ConfigID, TaskInfo in pairs(self.CurrentTasks) do
        if self:GetFadeOutAni(TaskInfo.view) then
            if self:GetFadeOutAni(TaskInfo.view):GetEndTime() > LongestAnimTime then
                LongestAnimWBP = TaskInfo.view
            end
        end 
    end
    for ConfigID, TaskInfo in pairs(self.CurrentTasks) do
        if self:GetFadeInAni(TaskInfo.view) then
            self:StopAnimation(self:GetFadeInAni(TaskInfo.view), TaskInfo.view)
        end
        if self:GetFadeOutAni(TaskInfo.view) then
            self:StopAnimation(self:GetFadeOutAni(TaskInfo.view), TaskInfo.view)
            self:PlayAnimation(self:GetFadeOutAni(TaskInfo.view),
            function ()
                P_HeadInfoBuff.ReturnToPool(TaskInfo.WBPName, TaskInfo.view)
                if LongestAnimWBP == TaskInfo.view and callback then
                    callback()
                end
            end, TaskInfo.view)
        end 
    end
    if not LongestAnimWBP and callback then
        callback()
    end
end

return P_HeadInfoBuff
