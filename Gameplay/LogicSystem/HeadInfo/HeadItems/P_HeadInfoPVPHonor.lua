local P_HeadInfoItemBase = kg_require"Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoPVPHonor = DefineClass("P_HeadInfoPVPHonor",P_HeadInfoItemBase)

function P_HeadInfoPVPHonor:ctor()
    self.EntityID = nil
end

function P_HeadInfoPVPHonor:OnBindActor(EntityID)
    self:BindHeadMessage("OnPVPHonorChange", "RefreshHonor")
    self.EntityID = EntityID
    self:RefreshHonor()
end

function P_HeadInfoPVPHonor:OnUnbindActor()
    self.EntityID = nil
end

function P_HeadInfoPVPHonor:RefreshHonor()
    local entity = self:GetAttachedEntity()
    if not entity then
        return
    end
	local honorId = Game.TeamAreanaSystem:GetMemberHonor(entity.eid)
    if honorId then
        local bIsEmemy = false
        if Game.me and entity.Camp ~= Game.me.Camp then
            bIsEmemy = true
        end
        local titleData = Game.TableData.GetPVPBattleTitleDataRow(honorId)
        if not titleData then return end
        self.view.Title_lua:SetText(titleData.Title)
        local titleColor
        if bIsEmemy and StringValid(titleData.EnemyTitleColor) then
            titleColor = titleData.EnemyTitleColor
        else
            titleColor = titleData.TitleColor
        end
        local bgColor = Game.ColorManager:GetColor("PVPColor", titleColor, Game.ColorManager.Type.LinearColor)
        self.view.Img_Bg_lua:SetColorAndOpacity(bgColor)
    end
end

function P_HeadInfoPVPHonor:OnUninit()

end

return P_HeadInfoPVPHonor