local P_HeadInfoItemBase = kg_require"Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoTransferIcon = DefineClass("P_HeadInfoTransferIcon",P_HeadInfoItemBase)

function P_HeadInfoTransferIcon:ctor()
    self.EntityID = nil
end

function P_HeadInfoTransferIcon:OnInitItem()
	
end

function P_HeadInfoTransferIcon:OnNpcTalkChanged(npcCfgID)
    self:CheckActorUseIcon()
end

function P_HeadInfoTransferIcon:CheckActorUseIcon()
    local RpcEntity = self:GetAttachedEntity()
    local teleportID = RpcEntity:GetForTeleportInsID()
    if teleportID then
        if Game.TeleportManager:IsTeleportPointUnlocked(tostring(teleportID)) then
            -- Log.Warning("is Unlocked ", teleportID)
            self.userWidget:Event_UI_Style(false)
        else
            -- Log.Warning("is locked ", teleportID)
            self.userWidget:Event_UI_Style(true)
        end
    end
end

function P_HeadInfoTransferIcon:OnBindActor(EntityID)
	--没有需要根据Distance处理的逻辑注释掉了@nitie
    --self:BindHeadMessage("OnDistanceUpdate","OnDistanceUpdate")
    self:BindHeadMessage("OnNpcTalkChanged", "OnNpcTalkChanged")
    self.EntityID = EntityID
end

--[[
function P_HeadInfoTransferIcon:OnDistanceUpdate(Distance)
    if not self.ReceiveTaskDistance then self.ReceiveTaskDistance =  Game.TableData.GetNPCInteractDataRow(1).ReceiveTaskDistanc or 25 end
    if not self.FunctionDistance then self.FunctionDistance = Game.TableData.GetNPCInteractDataRow(1).FunctionDistance or 20 end
    local RpcEntity = self:GetAttachedEntity()
    local bInRange =  (Distance < self.ReceiveTaskDistance * 100 and Game.NPCSystem:CheckDynamicTalk(RpcEntity:GetConfigTemplateID()))  or  Distance < self.FunctionDistance * 100
---TODO Lzm
end
--]]

function P_HeadInfoTransferIcon:OnParentAddSelfFinish()
	self:CheckActorUseIcon()
end

function P_HeadInfoTransferIcon:OnUnbindActor()
end

return P_HeadInfoTransferIcon
