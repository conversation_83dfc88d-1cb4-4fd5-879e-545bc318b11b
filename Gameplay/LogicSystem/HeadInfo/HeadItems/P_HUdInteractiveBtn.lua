local ESlateVisibility = import("ESlateVisibility")

local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HUDInteractiveBtn = DefineClass("P_HUDInteractiveBtn", P_HeadInfoItemBase)

function P_HUDInteractiveBtn:ctor()
end

function P_HUDInteractiveBtn:OnBindActor(EntityID)
    self.EntityID = EntityID
    local HeadSigFlag = self:GetFlagInstance(Game.HeadInfoManager.EShowRules.OnCaptain)
    if HeadSigFlag then
        self:OnRefreshRole(HeadSigFlag.RoleType)
    end
    self:BindHeadMessage("OnRoleChanged", "OnRefreshRole")
end

-- 这里做ui的刷新操作
function P_HUDInteractiveBtn:OnRefreshRole(Role)
    if Role == Enum.ETeamGroupRole.GroupLeader then
        self.userWidget:SetStyle(true)
    elseif Role == Enum.ETeamGroupRole.Captain then
        self.userWidget:SetStyle(false)
    end
end

function P_HUDInteractiveBtn:OnUnbindActor()
    self.EntityID = nil
end

function P_HUDInteractiveBtn:OnUninit()

end

return P_HUDInteractiveBtn
