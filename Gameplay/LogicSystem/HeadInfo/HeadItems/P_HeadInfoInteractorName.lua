local P_HeadInfoItemBase = kg_require "Gameplay.LogicSystem.HeadInfo.P_HeadInfoItemBase"
local P_HeadInfoInteractorName= DefineClass("P_HeadInfoInteractorName",P_HeadInfoItemBase)


function P_HeadInfoInteractorName:OnBindActor(EntityID)
    local RpcEntity = self:GetAttachedEntity()
    if RpcEntity then
		if RpcEntity.ActorType == EWActorType.DROP_ITEM and RpcEntity.Data.DropItem then
			local ItemData = Game.TableData.GetItemNewDataRow(RpcEntity.Data.DropItem.DropItemID)
			if ItemData then
				local Quality = ItemData.quality
				local TexColor = Game.TableData.GetRarityDataRow(Quality).TextColorCommon
				local RarityColor = Game.ColorManager:GetColor("Common", TexColor, Game.ColorManager.Type.SlateColor)
				self.view.Text_Name_lua:SetText(ItemData.itemName)
				self.view.Text_Name_lua:SetColorAndOpacity(RarityColor)
			else
				local RarityColor = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
				self.view.Text_Name_lua:SetText("UNKOWN ITEM NAME")
				self.view.Text_Name_lua:SetColorAndOpacity(RarityColor) self.view.Text_Name_lua:SetColorAndOpacity(RarityColor)
			end
		end
	end
end

function P_HeadInfoInteractorName:OnUnbindActor()

end

return P_HeadInfoInteractorName
