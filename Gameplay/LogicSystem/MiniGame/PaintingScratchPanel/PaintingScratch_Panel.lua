local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESlateVisibility = import("ESlateVisibility")
local CanvasRenderTarget2D = import("CanvasRenderTarget2D")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local UIFunctionLibrary = import("UIFunctionLibrary")

---@class PaintingScratch_Panel : UIPanel
local PaintingScratch_Panel = DefineClass("PaintingScratch_Panel", UIPanel)

PaintingScratch_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PaintingScratch_Panel:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

---初始化数据
function PaintingScratch_Panel:InitUIData()
	-- 检查资源是否加载完成
	self.timerID = nil
	-- 完成擦拭回调
	self.finishCallback = nil
	-- 直接关闭回调
	self.closeCallback = nil
end

--- UI组件初始化，此处为自动生成
function PaintingScratch_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PaintingScratch_Panel:InitUIEvent()
	--self:AddUIEvent(self.view.WBP_ComBtnCloseNew.Button_lua.OnClicked, "OnClickCloseButton")
	self:AddUIEvent(self.view.OnMouseButtonDownEvent, "on_WBP_PaintingScratchPanel_MouseButtonDownEvent")
    self:AddUIEvent(self.view.OnMouseButtonUpEvent, "on_WBP_PaintingScratchPanel_MouseButtonUpEvent")
	self:AddUIEvent(self.view.PaintingScratchImage.OnPaintingScratchReachPercentEvent, "onPaintingScratchImagePaintingScratchReachPercentEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PaintingScratch_Panel:InitUIView()
end

---面板打开的时候触发
function PaintingScratch_Panel:OnRefresh(templateID, finishCallback, closeCallback)
	local tableData = Game.TableData.GetSceneActorPaintingScratchDataRow(templateID)
	if not tableData then
		Log.ErrorFormat(">>>刮刮乐玩法 数据错误, 检查id:%d 是否在PaintingScratchData中", templateID)
		return
	end
	self.finishCallback = finishCallback
	self.closeCallback = closeCallback
	self:SetMaterial(self.view.PaintingScratchImage, tableData.SwipeMaterial)
	if StringValid(tableData.BackgroundImage) then
		self:SetImage(self.view.Img_Painting, tableData.BackgroundImage)
	end
	if StringValid(tableData.FrameImage) then
		self:SetImage(self.view.Img_Frame, tableData.FrameImage)
		self.view.Img_Frame:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.Img_Frame:SetVisibility(ESlateVisibility.Hidden)
	end
	self.timerID = Game.TimerManager:CreateTimerAndStart(function()
		self:checkDesiredSize(tableData.Thickness, tableData.Percentage, tableData.RTParam)
	end, 0, -1)
end

function PaintingScratch_Panel:OnClose()
	if self.timerID then
		Game.TimerManager:StopTimerAndKill(self.timerID)
		self.timerID = nil
	end
	self.userWidget:ReleaseRenderTargets()
end

function PaintingScratch_Panel:checkDesiredSize(thickness, targetPercent, swipeStyle)
	local geometry = self.view.PaintingScratchImage:GetCachedGeometry()
	local width = math.floor(SlateBlueprintLibrary.GetLocalSize(geometry).X)
	local height = math.floor(SlateBlueprintLibrary.GetLocalSize(geometry).Y)
	if width == 0 or height == 0 then
		return
	else
		Game.TimerManager:StopTimerAndKill(self.timerID)
		self.timerID = nil
		
		self.userWidget:InitRTMaterials(swipeStyle, thickness)
		local _, leftTop = SlateBlueprintLibrary.LocalToViewport(slua.getWorld(), geometry, FVector2D(0, 0), nil, nil)
		self.view.PaintingScratchImage:InitParameters(leftTop, thickness, targetPercent, self.view.SwipeRT, self.view.DrawDMI, 
			self.view.AdvertRT, self.view.DrawAdvertDMI, self.view.SimRT, self.view.DrawSimDMI)
	end
end

function PaintingScratch_Panel:onPaintingScratchImagePaintingScratchReachPercentEvent()
	Log.Debug("PaintingScratch_Panel:onPaintingScratchImagePaintingScratchReachPercentEvent")
	self:PlayAnimation(self.view.Dissolve, function()
		if self.finishCallback then
			xpcall(self.finishCallback, _G.CallBackError)
		end
		Game.CommonInteractorManager:TriggerCommonInteractorEvent(Enum.CommonInteractorEventType.SPIRITUAL_VISION_CHANGE)
		self:CloseSelf()
	end, self.userWidget)
end

function PaintingScratch_Panel:OnClickCloseButton()
	if self.closeCallback then
        xpcall(self.closeCallback, _G.CallBackError)
	end
	self:CloseSelf()
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent
function PaintingScratch_Panel:on_WBP_PaintingScratchPanel_MouseButtonDownEvent(myGeometry, inMouseEvent)
	self.view.PaintingScratchImage:SetMouseButtonDown(true)
	local EventReply = WidgetBlueprintLibrary.DetectDragIfPressed(
		inMouseEvent,
		self.userWidget,
		UIFunctionLibrary.GetKeyFromName("LeftMouseButton")
	)
	return WidgetBlueprintLibrary.CaptureMouse(EventReply, self.userWidget)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent
function PaintingScratch_Panel:on_WBP_PaintingScratchPanel_MouseButtonUpEvent(myGeometry, inMouseEvent)
	self.view.PaintingScratchImage:SetMouseButtonDown(false)
	return WidgetBlueprintLibrary.ReleaseMouseCapture(WidgetBlueprintLibrary.Unhandled())
end

return PaintingScratch_Panel
