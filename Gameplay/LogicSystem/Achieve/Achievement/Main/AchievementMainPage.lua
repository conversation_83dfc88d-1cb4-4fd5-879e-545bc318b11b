local AchievementMainBtn = kg_require("Gameplay.LogicSystem.Achieve.Achievement.Main.AchievementMainBtn")
local AchievementClass = kg_require("Gameplay.LogicSystem.Achieve.Achievement.Main.AchievementClass")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class AchievementMainPage : UIComponent
---@field view AchievementMainPageBlueprint
local AchievementMainPage = DefineClass("AchievementMainPage", UIComponent)

local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

AchievementMainPage.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function AchievementMainPage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function AchievementMainPage:InitUIData()
	-- 成就入口UI节点
	self.chileItems = {}
	-- 正在播放的动效
	self.curAnim = nil
	-- 首次打开
	self.bIsFirstShow = true
	-- 成就菜单特殊ID
	self.achievementMenuIDs = {
		ACHIEVEMENT_MENU_TO_FINISH = 1,
		ACHIEVEMENT_MENU_RECENT_FINISH = 2,
	}
end

--- UI组件初始化，此处为自动生成
function AchievementMainPage:InitUIComponent()
    ---@type AchievementMainBtn
    self.Btn_CollectCom = self:CreateComponent(self.view.Btn_Collect, AchievementMainBtn)
    ---@type AchievementMainBtn
    self.Btn_ToCompleteCom = self:CreateComponent(self.view.Btn_ToComplete, AchievementMainBtn)
    ---@type AchievementMainBtn
    self.Btn_RecentGetCom = self:CreateComponent(self.view.Btn_RecentGet, AchievementMainBtn)
    ---@type AchievementClass
    self.WBP_AchieveClass_1Com = self:CreateComponent(self.view.WBP_AchieveClass_1, AchievementClass)
    ---@type AchievementClass
    self.WBP_AchieveClass_2Com = self:CreateComponent(self.view.WBP_AchieveClass_2, AchievementClass)
    ---@type AchievementClass
    self.WBP_AchieveClass_3Com = self:CreateComponent(self.view.WBP_AchieveClass_3, AchievementClass)
    ---@type AchievementClass
    self.WBP_AchieveClass_4Com = self:CreateComponent(self.view.WBP_AchieveClass_4, AchievementClass)
    ---@type AchievementClass
    self.WBP_AchieveClass_5Com = self:CreateComponent(self.view.WBP_AchieveClass_5, AchievementClass)
    ---@type AchievementClass
    self.WBP_AchieveClass_6Com = self:CreateComponent(self.view.WBP_AchieveClass_6, AchievementClass)
    ---@type AchievementClass
    self.WBP_AchieveClass_7Com = self:CreateComponent(self.view.WBP_AchieveClass_7, AchievementClass)
end

---UI事件在这里注册，此处为自动生成
function AchievementMainPage:InitUIEvent()
    self:AddUIEvent(self.Btn_CollectCom.onClickEvent, "on_Btn_CollectCom_ClickEvent")
    self:AddUIEvent(self.Btn_RecentGetCom.onClickEvent, "on_Btn_RecentGetCom_ClickEvent")
    self:AddUIEvent(self.Btn_ToCompleteCom.onClickEvent, "on_Btn_ToCompleteCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function AchievementMainPage:InitUIView()
	local achieveInfo = Game.TableData.GetAchievementMenuDataTable()
	-- 设置名字和样式，蓝图中样式是 Id - 1
	self.Btn_RecentGetCom:Refresh(achieveInfo[self.achievementMenuIDs.ACHIEVEMENT_MENU_RECENT_FINISH].TabName,achieveInfo[self.achievementMenuIDs.ACHIEVEMENT_MENU_RECENT_FINISH].ID - 1)
	self.Btn_ToCompleteCom:Refresh(achieveInfo[self.achievementMenuIDs.ACHIEVEMENT_MENU_TO_FINISH].TabName,achieveInfo[self.achievementMenuIDs.ACHIEVEMENT_MENU_TO_FINISH].ID - 1)
	self.chileItems[1] = self.WBP_AchieveClass_1Com
	self.chileItems[2] = self.WBP_AchieveClass_2Com
	self.chileItems[3] = self.WBP_AchieveClass_3Com
	self.chileItems[4] = self.WBP_AchieveClass_4Com
	self.chileItems[5] = self.WBP_AchieveClass_5Com
	self.chileItems[6] = self.WBP_AchieveClass_6Com
	self.chileItems[7] = self.WBP_AchieveClass_7Com
end

---组件刷新统一入口
function AchievementMainPage:Refresh()
	self:StopCurAnim()
	if self.bIsFirstShow then
		self.bIsFirstShow = false
		self.curAnim = self.userWidget.Ani_FirstOpen
	else
		self.curAnim = self.userWidget.Ani_Switch
	end
	self:PlayAnimation(self.curAnim, function()
		self.curAnim = nil
	end, self.userWidget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	self:RefreshMainEntrance()
	-- 隐藏重点关注入口，不确定后续是否需要，先保留代码
	self.view.Btn_Collect:SetVisibility(ESlateVisibility.Collapsed)
end

function AchievementMainPage:OnHide()
	self:StopCurAnim()
	self:PlayAnimation(self.userWidget.Ani_Fadeout,nil, self.userWidget,0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function AchievementMainPage:StopCurAnim()
	if self.curAnim then
		self:StopAnimation(self.curAnim, self.userWidget)
		self.curAnim = nil
	end
	for i, widget in ipairs(self.chileItems) do
		widget:StopAllAnimations(widget.userWidget)
	end
end

function AchievementMainPage:OnClose()
	self.bIsFirstShow = true
end

function AchievementMainPage:RefreshMainEntrance()
	-- 刷新成就入口
	for i, widget in ipairs(self.chileItems) do
		widget:Refresh(i)
	end
end

--- 此处为自动生成
function AchievementMainPage:on_Btn_CollectCom_ClickEvent()
	-- 屏蔽重点关注入口，不确定后续是否需要，先保留代码
	--Game.NewUIManager:OpenPanel(UIPanelConfig.AchievementInfo_Panel, Enum.EAchievementMenuMainIndex.ACHIEVEMENT_MENU_COLLECT)
end

--- 此处为自动生成
function AchievementMainPage:on_Btn_RecentGetCom_ClickEvent()
	Game.NewUIManager:OpenPanel(UIPanelConfig.AchievementInfo_Panel, Enum.EAchievementMenuMainIndex.ACHIEVEMENT_MENU_RECENT_FINISH)
end

--- 此处为自动生成
function AchievementMainPage:on_Btn_ToCompleteCom_ClickEvent()
	Game.NewUIManager:OpenPanel(UIPanelConfig.AchievementInfo_Panel, Enum.EAchievementMenuMainIndex.ACHIEVEMENT_MENU_TO_FINISH)
end

return AchievementMainPage
