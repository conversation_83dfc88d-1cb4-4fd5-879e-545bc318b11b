---@class AchievementSender : SystemSenderBase
local AchievementSender = DefineClass("AchievementSender",SystemSenderBase)

--收藏成就
function AchievementSender:ReqCollectAchievement(achievementId, isCollect)
    self.Bridge.remote:ReqCollectAchievement(achievementId, isCollect)
end

--领取单个阶段奖励
function AchievementSender:ReqRewardAchievementLevelOne(achievementType, level)
    self.Bridge.remote:ReqRewardAchievementLevelOne(achievementType, level)
end

--一键领取阶段奖励
function AchievementSender:ReqRewardAchievementLevelAll(achievementType)
    self.Bridge.remote:ReqRewardAchievementLevelAll(achievementType)
end

--领取单个成就奖励
function AchievementSender:ReqRewardAchievementOne(achievementId)
    self.Bridge.remote:ReqRewardAchievementOne(achievementId)
end

--一键领取大类
function AchievementSender:ReqRewardAchievementAll(achievementType)
    self.Bridge.remote:ReqRewardAchievementAll(achievementType)
end

return AchievementSender