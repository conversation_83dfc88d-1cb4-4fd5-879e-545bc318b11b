--- 一个特殊的Director, 专门处理魔女风摇筝类的情况
local ArenaDirectorNaviOnly = DefineClass("ArenaDirectorNaviOnly")

local KismetMathLibrary = import("KismetMathLibrary")

ArenaDirectorNaviOnly.PerformanceInterval = 0.5 * 1000
ArenaDirectorNaviOnly.PerformanceOverTime = 3 * 1000


function ArenaDirectorNaviOnly:ctor(ArenaSystem, DirectorID, EntityLst, Params)
    self.ArenaSystem = ArenaSystem  -- 外部的管理器
    self.DirectorID = DirectorID  -- 自己的唯一id
    self.EntityLst = EntityLst  -- 自己负责的EntityLst
    self.Distance = Params[1]  -- 停止距离

    self.NaviTimer = nil
    self.NaviOverTime = 0

    self.finishTimer = nil

    for _, EntityEid in ipairs(self.EntityLst) do
        self.ArenaSystem:RegEntity(EntityEid, self.DirectorID)
    end

    self:startDirector()
end

function ArenaDirectorNaviOnly:dtor()
    self.ArenaSystem = nil
end

function ArenaDirectorNaviOnly:startDirector()
    local EntityA = Game.EntityManager:getEntity(self.EntityLst[1])
    local EntityB = Game.EntityManager:getEntity(self.EntityLst[2])
    if not EntityA or not EntityB then
        self.finishTimer = Game.TimerManager:CreateTimerAndStart(function()
            if EntityA then
                EntityA:BroadcastDirectorNaviFinish(false)
            end
            if EntityB then
                EntityB:BroadcastDirectorNaviFinish(false)
            end
            self:clearFinishTimer()
            self:OnDeleteDirector() -- 避免在OnEnter当帧析构带来潜在风险
        end, 0, 1)
        return
    end

    local CurDis = KismetMathLibrary.Vector_Distance(EntityA:GetPosition(), EntityB:GetPosition())
    if CurDis <= self.Distance then
        -- 距离很近, 直接认为在一起了
        self.finishTimer = Game.TimerManager:CreateTimerAndStart(function()
            EntityA:BroadcastDirectorNaviFinish(true)
            EntityB:BroadcastDirectorNaviFinish(true)
            self:clearFinishTimer()
            self:OnDeleteDirector() -- 避免在OnEnter当帧析构带来潜在风险
        end, 0, 1)
        return
    end

    local NavigationPath = Game.me:generatePath(EntityA:GetPosition(), EntityB:GetPosition())
    if NavigationPath == nil or NavigationPath:Length() < 1 then
        -- 无法寻路
        self.finishTimer = Game.TimerManager:CreateTimerAndStart(function()
            EntityA:BroadcastDirectorNaviFinish(true)
            EntityB:BroadcastDirectorNaviFinish(true)
            self:clearFinishTimer()
            self:OnDeleteDirector() -- 避免在OnEnter当帧析构带来潜在风险
        end, 0, 1)
        return
    elseif NavigationPath:Length() == 1 then
        -- 直接在脸上
        self.finishTimer = Game.TimerManager:CreateTimerAndStart(function()
            EntityA:BroadcastDirectorNaviFinish(true)
            EntityB:BroadcastDirectorNaviFinish(true)
            self:clearFinishTimer()
            self:OnDeleteDirector() -- 避免在OnEnter当帧析构带来潜在风险
        end, 0, 1)
        return
    end

    local bEntityAControl = EntityA == Game.me or Game.EntityManager:IsLocalEntity(EntityA.eid)

    if bEntityAControl then
        if EntityA == Game.me then
            Game.PlayerController:DisableControllerInput(Enum.ELocoControlTag.SocialAction, true, false)
        end
        EntityA:StartPathFollowTarget(EntityB.CharacterID, -1)
    end

    self.NaviTimer = Game.TimerManager:CreateTimerAndStart(
            function(dt)
                self.NaviOverTime = self.NaviOverTime + dt
                self:checkEntityNaviSituation()
            end, ArenaDirectorNaviOnly.PerformanceInterval, -1)
end

function ArenaDirectorNaviOnly:CheckDirector()
    return "NaviOnly"
end

function ArenaDirectorNaviOnly:CheckDirectorEnum()
	return Enum.EDirectorInnerState.Navi
end

function ArenaDirectorNaviOnly:FinishDirector()
    local EntityA = Game.EntityManager:getEntity(self.EntityLst[1])
    local EntityB = Game.EntityManager:getEntity(self.EntityLst[2])

    local bEntityAControl = EntityA == Game.me or Game.EntityManager:IsLocalEntity(EntityA.eid)
    if bEntityAControl then
        EntityA:StopPathFollow()
        if EntityA == Game.me then
            Game.PlayerController:DisableControllerInput(Enum.ELocoControlTag.SocialAction, false, false)
        end
    end
    self:clearNaviTimer()
    self:clearFinishTimer()
    EntityA:BroadcastDirectorNaviFinish(false)
    EntityB:BroadcastDirectorNaviFinish(false)
    self:OnDeleteDirector()
end

function ArenaDirectorNaviOnly:OnDeleteDirector()
    for _, EntityEid in ipairs(self.EntityLst) do
        self.ArenaSystem:UnRegEntity(EntityEid, self.DirectorID)
    end
    self.ArenaSystem:DeleteDirector(self.DirectorID)
end

function ArenaDirectorNaviOnly:checkEntityNaviSituation()
    local EntityA = Game.EntityManager:getEntity(self.EntityLst[1])
    local EntityB = Game.EntityManager:getEntity(self.EntityLst[2])

	if not EntityA.bInWorld or not EntityB.bInWorld then
        self:clearNaviTimer()
        EntityA:BroadcastDirectorNaviFinish(false)
        EntityB:BroadcastDirectorNaviFinish(false)
        if EntityA == Game.me then
            Game.PlayerController:DisableControllerInput(Enum.ELocoControlTag.SocialAction, false, false)
        end
        self:OnDeleteDirector()
        return
    end

    local bEntityAControl = EntityA == Game.me or Game.EntityManager:IsLocalEntity(EntityA.eid)

    local bFinish = false
    if self.NaviOverTime >= ArenaDirectorNaviOnly.PerformanceOverTime then
        -- 超时
        bFinish = true
    else
        local CurDis = KismetMathLibrary.Vector_Distance(EntityA:GetPosition(), EntityB:GetPosition())
        if CurDis <= self.Distance then
            bFinish = true
        end
    end

    if bFinish then
        if bEntityAControl then
            EntityA:StopPathFollow()
            if EntityA == Game.me then
                Game.PlayerController:DisableControllerInput(Enum.ELocoControlTag.SocialAction, false, false)
            end
        end

        self:clearNaviTimer()
        EntityA:BroadcastDirectorNaviFinish(true)
        EntityB:BroadcastDirectorNaviFinish(true)
        self:OnDeleteDirector()
    end
end

function ArenaDirectorNaviOnly:clearNaviTimer()
    if self.NaviTimer ~= nil then
        Game.TimerManager:StopTimerAndKill(self.NaviTimer)
        self.NaviTimer = nil
    end
end

function ArenaDirectorNaviOnly:clearFinishTimer()
    if self.finishTimer ~= nil then
        Game.TimerManager:StopTimerAndKill(self.finishTimer)
        self.finishTimer = nil
    end
end

return ArenaDirectorNaviOnly
