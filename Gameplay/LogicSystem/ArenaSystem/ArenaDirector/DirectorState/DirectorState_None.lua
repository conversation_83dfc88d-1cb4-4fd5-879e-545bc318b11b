local DirectorStateBase = kg_require("Gameplay.LogicSystem.ArenaSystem.ArenaDirector.DirectorState.DirectorStateBase")

local DirectorState_None = DefineClass("DirectorState_None", DirectorStateBase)

--@override
function DirectorState_None:InitDefaults()
    self.finishTimer = nil
end

--@override
function DirectorState_None:BeforeUnInit()
    self:clearFinishTimer()
end

--@override
function DirectorState_None:OnEnter(Params, OldState)
    self:DebugMsg("Enter DirectorState_None")
    self.finishTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:clearFinishTimer()
        self.ArenaDirector:OnDeleteDirector() -- 避免在OnEnter当帧析构带来潜在风险
    end, 0, 1)
end

--@override
function DirectorState_None:OnLeave(Params, NewState)
    self:clearFinishTimer()
end

--@override
function DirectorState_None:OnRefresh(Params)

end

--@override
function DirectorState_None:OnEntityExit(Params)

end

function DirectorState_None:clearFinishTimer()
    if self.finishTimer ~= nil then
        Game.TimerManager:StopTimerAndKill(self.finishTimer)
        self.finishTimer = nil
    end
end

return DirectorState_None