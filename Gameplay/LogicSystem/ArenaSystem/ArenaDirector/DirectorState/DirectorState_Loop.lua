local DirectorStateBase = kg_require("Gameplay.LogicSystem.ArenaSystem.ArenaDirector.DirectorState.DirectorStateBase")
local ParallelBehaviorControlConst = kg_require("Shared.Const.ParallelBehaviorControlConst")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local DirectorState_Loop = DefineClass("DirectorState_Loop", DirectorStateBase)
local EAttachmentRule = import("EAttachmentRule")
local EDetachmentRule = import("EDetachmentRule")
local UStaticMeshComponent = import("StaticMeshComponent")
local ELogicParentFeatureSynchronizeMask = import("ELogicParentFeatureSynchronizeMask")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")
local LocoStateTypeToAnimStateNameMap = ViewControlConst.LocoStateTypeToAnimStateNameMap
local LOCO_ANIM_STATE_CONST = ParallelBehaviorControlConst.LOCO_ANIM_STATE_CONST
local LOCO_GROUP_STATE_CONST = ParallelBehaviorControlConst.LOCO_GROUP_STATE_CONST


--@override
function DirectorState_Loop:InitDefaults()
    self.DirectorPlayAnimFeatureID = {}
    self.LocalEntityLst = {}
    self.FinishFirstLoopLst = {}
	self.setLocoThrusterEntityLst = {}
	self.setLocoStateChangeEntityLst = {}

    self.bCurMove = false
	self.bAttachNoRoot = false
end
 
--@override
function DirectorState_Loop:BeforeUnInit()
    self:clearDirectorAnimFeature()
    self:DebugMsg("BeforeUnInit in Loop State, Strange")
end

--@override
local TEMP_ZEROFVECTOR = FVector(0, 0, 0)
function DirectorState_Loop:OnEnter(Params, OldState)  -- luacheck: ignore
    for _, EntityEid in ipairs(self.ArenaDirector.RemainEntityLst) do
        table.insert(self.LocalEntityLst, EntityEid)
    end

    -- 这里可以不用仔细校验了, 因为Navi阶段已经判断过了
    self:clearDirectorAnimFeature()
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(Params.PerformanceActionID)

    -- 找到锚点角色
    local PilotIdx = Params.PilotIdx

    -- 锁定LookAt, 移动与技能; 隐藏武器; 改变DriveMode; 挂接骨骼; 播放动作
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        if not table.contains(self.LocalEntityLst, Params.EntityLst[idx]) then
            -- 中途离开了
            goto continue
        end

        local TargetEntity = Game.EntityManager:getEntity(Params.EntityLst[idx])
        if not TargetEntity then
            self:DebugMsg("no Entity with eid " .. Params.EntityLst[idx])
            self:failThenSwitchState()
            return
        end
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Interactive and idx > 1 then
            -- 先默认角色是出现在第一个的
            self:OnEnterForInteractive(TargetEntity, PerformanceActionData)
            goto continue
        end

        -- 锁定LookAt
        TargetEntity:LockGazeInPerformance(not PerformanceActionData.LoopCmd_bEnableLookAt)
        -- 锁定移动与技能
        if PerformanceActionData.LoopCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
            TargetEntity:LockMoveInPerformance(true)
        elseif PerformanceActionData.LoopCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
            TargetEntity:LockMoveInPerformance(false)
            TargetEntity:ChangeForceWalkMode(true)
        end
        TargetEntity:LockJumpInPerformance(PerformanceActionData.LoopCmd_bLockJump)
        TargetEntity:LockSkillInPerformance(PerformanceActionData.LoopCmd_bLockSkill)

		-- 改变DriveMode
        if self.Params.DirectorUpperControlLogicID[idx] then
			TargetEntity.LocoControlOperator:UnDoUpperControlLogic(self.Params.DirectorUpperControlLogicID[idx])
			self.Params.DirectorUpperControlLogicID[idx] = nil
		end
		local bEntityControl = TargetEntity == Game.me or Game.EntityManager:IsLocalEntity(TargetEntity.eid)
		if not bEntityControl and PerformanceActionData.LoopCmd_DriveMode == "DriveByNet" then
			TargetEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.NetDriveDualCommonDirector)
			self.Params.DirectorUpperControlLogicID[idx] = Enum.LocoControlUpperControlLogic.NetDriveDualCommonDirector
		else
			TargetEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.LocalDriveDualCommonDirector)
			self.Params.DirectorUpperControlLogicID[idx] = Enum.LocoControlUpperControlLogic.LocalDriveDualCommonDirector
		end
        -- 快速恢复的情况下, 需要补一个快速就位
        if Params.bQuickRecover == true or Params.RecoverTime ~= nil then
            if idx ~= PilotIdx then
                -- 移动非锚点角色
                local BiasPos = PerformanceActionData.PilotBiasPos
                local BiasYaw = PerformanceActionData.PilotBiasYaw
                local FinalTrans = self:GetWorldTransform(Params.PilotPos, Params.PilotYaw, BiasPos, BiasYaw)
				TargetEntity:SetPosition(FinalTrans:GetLocation())
				TargetEntity:SetRotation(FinalTrans:Rotator())
            else
                -- 移动锚点角色
                if Params.PilotPos ~= TargetEntity:GetPosition() or Params.PilotYaw ~= TargetEntity:GetRotation().Yaw then
					TargetEntity:SetPosition(Params.PilotPos)
					TargetEntity:SetRotation_P(0, Params.PilotYaw, 0)
                end
            end
        end
        -- 挂接骨骼
        if PerformanceActionData.AttachSocketOnLoop ~= "" and PerformanceActionData.AttachActorSlotOnLoop > 0 then
            local AttachEntity = Game.EntityManager:getEntity(Params.EntityLst[PerformanceActionData.AttachActorSlotOnLoop])
            if not AttachEntity then
                self:DebugMsg("AttachEntity Destroy or is Destroying " .. Params.EntityLst[PilotIdx])
                self:failThenSwitchState()
                return
            end

			TargetEntity:ChangeAttachMode(true)
			local AttachCompID, SocketName
			local bSpecialAttach = false
			if PerformanceActionData.AttachSocketOnLoop == "root" then
				AttachCompID = AttachEntity.CppEntity:KAPI_Actor_GetRootComponent()
				SocketName = "root"
			else
				local words = {}
				for word in string.gmatch(PerformanceActionData.AttachSocketOnLoop, "([^"..".".."]+)") do
					table.insert(words, word)
				end
				if words[1] == "Mount" and AttachEntity.MountEntity then
					AttachCompID = AttachEntity.MountEntity.SKCompID
					SocketName = words[2]
					-- 注册乘客替代的 Locomotion 状态机
					TargetEntity:LeaveCurLocoGroupAndEnterNew(LOCO_GROUP_STATE_CONST.RidePassenger, false, false)
					TargetEntity:SetAttachToSimple(WorldViewConst.ATTACH_REASON.Mount, AttachEntity.MountEntity.eid, SocketName, 0,0,0,0,0,0, nil)
					TargetEntity:UpdateIsSkipChangeAttachMode(true)
					TargetEntity:ApplyAttach()
					AttachEntity.MountEntity.CppEntity:KAPI_Actor_EnableLogicFeatureSynchronizeToChilds(TargetEntity.CharacterID, ELogicParentFeatureSynchronizeMask.AnimationLocoState)
					bSpecialAttach = true
				else
					AttachCompID = AttachEntity.CppEntity:KAPI_Actor_GetMainMesh()
					if not IsValidID(AttachCompID) then
						-- 没有SK Mesh, 那只能是StaticMesh了
						AttachCompID = AttachEntity.CppEntity:KAPI_Actor_GetComponentByClass(UStaticMeshComponent)
					end
					SocketName = Params.AttachSocket or words[1]
				end
				-- 此时Attach的实际上是RootComponent, 对玩家来说实际上是胶囊体, 所以我们还需要消除Mesh的偏移
				local TargetMeshCompID = TargetEntity.CppEntity:KAPI_Actor_GetMainMesh()
				TargetEntity.CppEntity:KAPI_SceneID_SetRelativeLocation(TargetMeshCompID, 0, 0, 0)
				self.bAttachNoRoot = true
			end
			--Log.Debug("LYL DEBUG FinalPos1", TargetEntity:GetPosition())
			--Log.Debug("LYL DEBUG FinalYaw1", TargetEntity:GetRotation().Yaw)
			if not bSpecialAttach then
				TargetEntity.CppEntity:KAPI_Actor_K2_AttachToComponent(
				        AttachCompID,
				        SocketName,
				        EAttachmentRule.SnapToTarget,
				        EAttachmentRule.SnapToTarget,
				        EAttachmentRule.KeepWorld, false
				)	
			end
			if self.bAttachNoRoot then
				TargetEntity.CppEntity:KAPI_SetRelativeRotation_P(0, 90, 0)
			end
			--Log.Debug("LYL DEBUG FinalPos2", TargetEntity:GetPosition())
			--Log.Debug("LYL DEBUG FinalYaw2", TargetEntity:GetRotation().Yaw)
			
			if idx ~= PilotIdx then
				TargetEntity:RegisterMountPassengerAnimStateReplace()
			end
        end
        -- 播放交互动画
		local replaceAnim
		if Params.ReplaceAnimInfo and Params.ReplaceAnimInfo[idx] then
			replaceAnim = Params.ReplaceAnimInfo[idx][2]
		end
		if replaceAnim and StringValid(replaceAnim[1]) then
			-- 动画替换
			local AnimList = {}
			for _, value in ipairs(replaceAnim) do
				table.insert(AnimList, {AssetID = value})
			end
            local DurationSeq = {}
            for _, Anim in ipairs(AnimList) do
                table.insert(DurationSeq, -1)
            end
			TargetEntity:PlayAnimList(
					AnimList,
					DurationSeq,
					false,
					nil,
					nil,
					self,
					"OnAnimListStop",
					TargetEntity.eid,
					PerformanceActionData
			)
			goto continue
		end
		
        local LoopAnim = PerformanceActionData.Animation[#(PerformanceActionData.Animation)]
        local duration
		local AnimFeatureID
		if not LoopAnim then
			duration = nil
        elseif PerformanceActionData.Type == Enum.EPerformanceActionType.Half then
			AnimFeatureID, duration = TargetEntity:PlayAnimLibMontageForUpper(LoopAnim, nil, true, 0, 0.2, nil, nil, true)
        elseif PerformanceActionData.Type == Enum.EPerformanceActionType.Face then
			AnimFeatureID, duration = TargetEntity:PlayFaceAnimation(LoopAnim, true)
        elseif PerformanceActionData.LoopMode == Enum.EPerformanceActionLoopMode.Loop then
            local RecoverTime, OneLoopTime = self:getRecoverTime(PerformanceActionData, TargetEntity)	
			local blendTime = 0
			AnimFeatureID, duration = TargetEntity:PlayAnimLibMontage_Loop(LoopAnim, nil, blendTime, blendTime)
			if RecoverTime > 0 then
				TargetEntity:SetAnimFeatureStartTime(AnimFeatureID, RecoverTime % OneLoopTime)
            end
        else
            local RecoverTime, OneLoopTime = self:getRecoverTime(PerformanceActionData, TargetEntity)
			local blendTime = 0
            if RecoverTime > 0 then
                if PerformanceActionData.LoopMode == Enum.EPerformanceActionLoopMode.Stay then
					AnimFeatureID, duration = TargetEntity:PlayAnimLibMontage(LoopAnim, nil, nil, blendTime, blendTime, true, self, "OnEnterAnimStop", idx, TargetEntity.eid, PerformanceActionData)
					if AnimFeatureID then
						if RecoverTime > OneLoopTime then
							TargetEntity:SetAnimFeatureStartTime(AnimFeatureID, OneLoopTime)
						else
							TargetEntity:SetAnimFeatureStartTime(AnimFeatureID, RecoverTime)
						end
					end
                else
                    if RecoverTime > OneLoopTime then
                        duration = 0
                    else
						AnimFeatureID, duration = TargetEntity:PlayAnimLibMontage(LoopAnim, nil, nil, blendTime, blendTime, true, self, "OnEnterAnimStop", idx, TargetEntity.eid, PerformanceActionData)
						if AnimFeatureID then
							TargetEntity:SetAnimFeatureStartTime(AnimFeatureID, RecoverTime)
						end
                    end
                end
            else
				AnimFeatureID, duration = TargetEntity:PlayAnimLibMontage(LoopAnim, nil, nil, blendTime, blendTime, true, self, "OnEnterAnimStop", idx, TargetEntity.eid, PerformanceActionData)
            end
        end
        if AnimFeatureID ~= nil then
			self.DirectorPlayAnimFeatureID[idx] = AnimFeatureID
			-- 监听移动阶段替换动作
			if StringValid(PerformanceActionData.MoveAnimOnLoop) then
				self.bCurMove = false
				if not TargetEntity.IsAlreadyNeedNotifyLocoStateChanged then
					table.insert(self.setLocoStateChangeEntityLst, TargetEntity.eid)
				end
				Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.LOCO_STATE_CHANGE, self, self.LoopOnLocoMove, TargetEntity.eid)
			end
        else
            self:succFirstLoop(TargetEntity.eid)
            if PerformanceActionData.LoopMode == Enum.EPerformanceActionLoopMode.AutoEnd then
                self:succThenSwitchState(TargetEntity.eid)
            end
        end
        :: continue ::
    end
end

function DirectorState_Loop:OnEnterAnimStop(bInterrupt, idx, EID, PerformanceActionData)
	self.DirectorPlayAnimFeatureID[idx] = nil
	self:succFirstLoop(EID)
	if PerformanceActionData.LoopMode == Enum.EPerformanceActionLoopMode.AutoEnd then
		self:succThenSwitchState(EID)
	end
end

function DirectorState_Loop:OnAnimListStop(bInterrupt, EID, PerformanceActionData)
	-- 暂时的无奈之举
	if not self.failThenSwitchState then
		return
	end
	if not bInterrupt then
		self:succFirstLoop(EID)
		if PerformanceActionData.LoopMode == Enum.EPerformanceActionLoopMode.AutoEnd then
			self:succThenSwitchState(EID)
		end
	else
		self:failThenSwitchState()
	end
end

function DirectorState_Loop:OnEnterForInteractive(SceneActor, PerformanceActionData)
    -- 播放交互动画
    if #PerformanceActionData.Animation > 0 then
		SceneActor:PlayAnimationByMontageAsyncLoad("DefaultSlot", PerformanceActionData.Animation[#PerformanceActionData.Animation], true, 0, 0)
    end
    self:succThenSwitchState(SceneActor.eid)  -- 原来的交互写法就是玩家结束就切换, 这里暂时保留这个设计
end

--@override
function DirectorState_Loop:OnLeave(Params, NewState)
    -- 根据配置还原禁用状态
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(Params.PerformanceActionID)

    -- 锁定LookAt, 移动与技能; 隐藏武器; 改变DriveMode; 挂接骨骼; 播放动作
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        local TargetEntity = Game.EntityManager:getEntity(Params.EntityLst[idx])
        if not TargetEntity then
            goto continue
        end
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Interactive and idx > 1 then
            -- 先默认角色是出现在第一个的
            goto continue
        end

        -- 如果前面处理了关闭,这里要恢复
        if not PerformanceActionData.LoopCmd_bEnableLookAt then
            TargetEntity:LockGazeInPerformance(false)
        end

        -- 锁定移动与技能
        if PerformanceActionData.LoopCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
            TargetEntity:LockMoveInPerformance(false)
        elseif PerformanceActionData.LoopCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
            TargetEntity:LockMoveInPerformance(false)
            TargetEntity:ChangeForceWalkMode(false)
        end
        TargetEntity:LockJumpInPerformance(false)
        TargetEntity:LockSkillInPerformance(false)

        -- 挂接骨骼
        if PerformanceActionData.AttachSocketOnLoop ~= "" and PerformanceActionData.AttachActorSlotOnLoop > 0 then
			TargetEntity:ChangeAttachMode(false)

			-- 
			local words = {}
			for word in string.gmatch(PerformanceActionData.AttachSocketOnLoop, "([^"..".".."]+)") do
				table.insert(words, word)
			end
			if words[1] == "Mount" and idx ~= PilotIdx then
				TargetEntity:ClearParentToChildStateMap()
				TargetEntity:DetachFromOwner()
				TargetEntity:LeaveCurLocoGroupAndEnterNew(nil, false, false)
				-- 需要手动调用一次,不然中间会有表现上的断档
				TargetEntity:LocomotionCrossfadeInFixedTime(LocoStateTypeToAnimStateNameMap[LOCO_ANIM_STATE_CONST.Idle], 0.0)
			else
				TargetEntity.CppEntity:KAPI_Actor_K2_DetachFromActor(EDetachmentRule.KeepWorld, EDetachmentRule.KeepWorld, EDetachmentRule.KeepWorld)
			end
			
			if self.bAttachNoRoot then
				self.bAttachNoRoot = false
			end
		end
		-- 停止动画
		if self.DirectorPlayAnimFeatureID[idx] then
			TargetEntity:StopAnimLibMontage(self.DirectorPlayAnimFeatureID[idx])
			self.DirectorPlayAnimFeatureID[idx] = nil
		end
		-- 关闭LocoState监听
		if table.contains(self.setLocoStateChangeEntityLst, TargetEntity.eid) then
			table.removeItem(self.setLocoStateChangeEntityLst, TargetEntity.eid)
		end
		-- 关闭移动时的推进器
		if table.contains(self.setLocoThrusterEntityLst, TargetEntity.eid) then
			table.removeItem(self.setLocoThrusterEntityLst, TargetEntity.eid)
			TargetEntity:CancelSetLocoSourceModeToAnimRootMotion()
		end
        :: continue ::
    end
    self:clearDirectorAnimFeature()
    Game.EventSystem:RemoveObjListeners(self)
    self.LocalEntityLst = {}
end

--@override
function DirectorState_Loop:OnRefresh(Params)

end

--@override
function DirectorState_Loop:OnEntityExit(Params)
    local EntityEid = Params.ExitEntityEid
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end
    local TargetEntity = Game.EntityManager:getEntity(EntityEid)
    if not TargetEntity then
        return
    end
    local bAttachSocket = false
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(self.Params.PerformanceActionID)
    for _, PerformanceActionData in ksbcpairs(ActionInfo) do
        if PerformanceActionData.AttachSocketOnLoop ~= "" and PerformanceActionData.AttachActorSlotOnLoop > 0 then
            bAttachSocket = true
        end
    end
    if bAttachSocket then
        self:DebugMsg("Exit At Loop State BUT with Attach, Director Will finish")
        self:failThenSwitchState()
        return
    end
    -- 给他做一个单人的退出
    table.removeItem(self.LocalEntityLst, EntityEid)
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        if self.Params.EntityLst[idx] ~= EntityEid then
            goto continue
        end

        -- 如果前面处理了关闭,这里要恢复
        if not PerformanceActionData.LoopCmd_bEnableLookAt then
            TargetEntity:LockGazeInPerformance(false)
        end

        -- 锁定移动与技能
        if PerformanceActionData.LoopCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
            TargetEntity:LockMoveInPerformance(false)
        elseif PerformanceActionData.LoopCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
            TargetEntity:LockMoveInPerformance(false)
            TargetEntity:ChangeForceWalkMode(false)
        end
        TargetEntity:LockJumpInPerformance(false)
        TargetEntity:LockSkillInPerformance(false)

        -- 结束动画
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Face then
            TargetEntity:StopFaceAnimation()
        else
            TargetEntity:StopAnimLibMontage()
        end
        if self.DirectorPlayAnimFeatureID[idx] then
			TargetEntity:StopAnimLibMontage(self.DirectorPlayAnimFeatureID[idx])
            self.DirectorPlayAnimFeatureID[idx] = nil
        end
		-- 关闭LocoState监听
		if table.contains(self.setLocoStateChangeEntityLst, TargetEntity.eid) then
			table.removeItem(self.setLocoStateChangeEntityLst, TargetEntity.eid)
		end
		-- 关闭移动时的推进器
		if table.contains(self.setLocoThrusterEntityLst, EntityEid) then
			table.removeItem(self.setLocoThrusterEntityLst, EntityEid)
			TargetEntity:CancelSetLocoSourceModeToAnimRootMotion()
		end
		-- 改变DriveMode
		if self.Params.DirectorUpperControlLogicID[idx] then
			TargetEntity.LocoControlOperator:UnDoUpperControlLogic(self.Params.DirectorUpperControlLogicID[idx])
			self.Params.DirectorUpperControlLogicID[idx] = nil
		end
        self:DebugMsg("Entity Exit in Loop State " .. EntityEid)
        :: continue ::
    end
    if #(self.LocalEntityLst) <= 0 then
        self.StateMachine:SwitchState(Enum.EDirectorInnerState.End, self.Params)
    end
end

function DirectorState_Loop:LoopOnLocoMove(lastLocomotionState, newLocomotionState, EntityEid)
    local TargetEntity = Game.EntityManager:getEntity(EntityEid)
    if not TargetEntity then
        return
    end
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end

    local bSwitchMoveOrIdle
    local idleState = {LOCO_ANIM_STATE_CONST.Idle, LOCO_ANIM_STATE_CONST.RunEnd}
    local moveState = {LOCO_ANIM_STATE_CONST.RunStart, LOCO_ANIM_STATE_CONST.Run, LOCO_ANIM_STATE_CONST.MoveTurn}

    if table.contains(idleState, lastLocomotionState) and table.contains(moveState, newLocomotionState) then
        self.bCurMove = true
        bSwitchMoveOrIdle = true
    elseif table.contains(moveState, lastLocomotionState) and table.contains(idleState, newLocomotionState) then
        self.bCurMove = false
        bSwitchMoveOrIdle = false
    elseif self.bCurMove == true then
        if table.contains(moveState, lastLocomotionState) and table.contains(moveState, newLocomotionState) then
            -- do nothing
        else
            self.bCurMove = false
            bSwitchMoveOrIdle = false
        end
    end

    if bSwitchMoveOrIdle == nil then
        return
    end

	local ActionInfo = Game.TableData.GetPerformanceActionDataRow(self.Params.PerformanceActionID)
	
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        if not table.contains(self.LocalEntityLst, self.Params.EntityLst[idx]) then
            goto continue
        end

        local ForLoopEntity = Game.EntityManager:getEntity(self.Params.EntityLst[idx])
        if not ForLoopEntity then
            goto continue
        end

        local LoopAnim
        if bSwitchMoveOrIdle then
            LoopAnim = PerformanceActionData.MoveAnimOnLoop
        else
            LoopAnim = PerformanceActionData.Animation[#(PerformanceActionData.Animation)]
        end
        if not StringValid(LoopAnim) then
            goto continue
        end

        if PerformanceActionData.Type == Enum.EPerformanceActionType.Half then
			-- todo 后面如果还有移动中上下半身替换动画的需求, 需要评估一下是否做成更为通用的结构
			ForLoopEntity:PlayAnimLibMontage_Loop(LoopAnim, nil, 0.1, 0.1)  -- 给个0.1s的blend观察一下效果
			if not table.contains(self.setLocoThrusterEntityLst, ForLoopEntity.eid) then
				table.insert(self.setLocoThrusterEntityLst, ForLoopEntity.eid)
				ForLoopEntity:SetMoveByThrusterWithoutAnimRM()
			end
        elseif PerformanceActionData.Type == Enum.EPerformanceActionType.Face then
            ForLoopEntity:PlayFaceAnimation(LoopAnim, true)
			if table.contains(self.setLocoThrusterEntityLst, ForLoopEntity.eid) then
				table.removeItem(self.setLocoThrusterEntityLst, ForLoopEntity.eid)
				ForLoopEntity:CancelSetLocoSourceModeToAnimRootMotion()
			end
        elseif PerformanceActionData.LoopMode == Enum.EPerformanceActionLoopMode.Loop then
            ForLoopEntity:PlayAnimLibMontage_Loop(LoopAnim, nil, 0.1, 0.1)  -- 给个0.1s的blend观察一下效果
			if table.contains(self.setLocoThrusterEntityLst, ForLoopEntity.eid) then
				table.removeItem(self.setLocoThrusterEntityLst, ForLoopEntity.eid)
				ForLoopEntity:CancelSetLocoSourceModeToAnimRootMotion()
			end
        else
            ForLoopEntity:PlayAnimLibMontage(LoopAnim)
			if table.contains(self.setLocoThrusterEntityLst, ForLoopEntity.eid) then
				table.removeItem(self.setLocoThrusterEntityLst, ForLoopEntity.eid)
				ForLoopEntity:CancelSetLocoSourceModeToAnimRootMotion()
			end
        end

		if self.DirectorPlayAnimFeatureID[idx] then
			ForLoopEntity:StopAnimLibMontage(self.DirectorPlayAnimFeatureID[idx])
			self.DirectorPlayAnimFeatureID[idx] = nil
			self:succFirstLoop(TargetEntity.eid)
			if PerformanceActionData.LoopMode == Enum.EPerformanceActionLoopMode.AutoEnd then
				self:succThenSwitchState(TargetEntity.eid)
			end
		end

        :: continue ::
    end
end

function DirectorState_Loop:succFirstLoop(EntityEid)
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end
    if table.contains(self.FinishFirstLoopLst, EntityEid) then
        return
    end
    table.insert(self.FinishFirstLoopLst, EntityEid)
    if #self.FinishFirstLoopLst == #self.LocalEntityLst then
        for _, loopEntityEid in ipairs(self.LocalEntityLst) do
            local TargetEntity = Game.EntityManager:getEntity(loopEntityEid)
            if TargetEntity and TargetEntity.BroadcastDirectorFirstLoopFinish then
                TargetEntity:BroadcastDirectorFirstLoopFinish()
            end
        end
    end
end

function DirectorState_Loop:succThenSwitchState(EntityEid)
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end
    table.removeItem(self.LocalEntityLst, EntityEid)
    if #(self.LocalEntityLst) <= 0 then
        self.StateMachine:SwitchState(Enum.EDirectorInnerState.End, self.Params)
    end
end

function DirectorState_Loop:failThenSwitchState()
	if self.Params.bFailThenSwitch then
		return
	end
	self.Params.bFailThenSwitch = true
    self.StateMachine:SwitchState(Enum.EDirectorInnerState.Finish, self.Params)
end

function DirectorState_Loop:clearDirectorAnimFeature()
	table.clear(self.DirectorPlayAnimFeatureID)
end

function DirectorState_Loop:getRecoverTime(PerformanceActionData, thisEntity)
	local _, LoopAnimLen = thisEntity:GetAnimPathAndLenFromAnimFeatureForSingleAnimation(PerformanceActionData.Animation[#(PerformanceActionData.Animation)])
    if self.Params.RecoverTime == nil then
        return 0, LoopAnimLen and LoopAnimLen or 0
    end

    local startTime = 0
    for idx, AnimAssetID in ksbcpairs(PerformanceActionData.Animation) do
        if idx >= #(PerformanceActionData.Animation) then
            break
        end
        local _, AnimLen = thisEntity:GetAnimPathAndLenFromAnimFeatureForSingleAnimation(AnimAssetID)
        if AnimLen then
            startTime = startTime + AnimLen
        end
    end
    if self.Params.RecoverTime < startTime then
        return 0, LoopAnimLen and LoopAnimLen or 0
    else
        return self.Params.RecoverTime - startTime, LoopAnimLen and LoopAnimLen or 0
    end
end

return DirectorState_Loop