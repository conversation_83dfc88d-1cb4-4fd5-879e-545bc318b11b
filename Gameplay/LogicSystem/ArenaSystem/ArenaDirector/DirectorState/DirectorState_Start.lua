local DirectorStateBase = kg_require("Gameplay.LogicSystem.ArenaSystem.ArenaDirector.DirectorState.DirectorStateBase")
local WEAK_FORCE_CONTROL_REASON_TAGS = kg_require("Shared.Const.ParallelBehaviorControlConst").WEAK_FORCE_CONTROL_REASON_TAGS
local AnimLibUtils = kg_require("Shared.Utils.AnimLibUtils")
local DirectorState_Start = DefineClass("DirectorState_Start", DirectorStateBase)

local EMoveDriveMode = import("EMoveDriveMode")
local EMoveCorrectorObtainPriority = import("EMoveCorrectorObtainPriority")
local ERootWarpMode = import("ERootWarpMode")
local UStaticMeshComponent = import("StaticMeshComponent")

--@override
function DirectorState_Start:InitDefaults()
    self.LocalEntityLst = {}
	self.DestSmoothTokenLst = {}
	self.MotionWarpTokenLst = {}
    self.bNeedDealFail = true
end

--@override
function DirectorState_Start:BeforeUnInit()
    self:DebugMsg("BeforeUnInit in Start State, Strange")
end

--@override
function DirectorState_Start:OnEnter(Params, OldState)  -- luacheck: ignore
    for _, EntityEid in ipairs(self.ArenaDirector.RemainEntityLst) do
        table.insert(self.LocalEntityLst, EntityEid)
    end

    -- 这里可以不用仔细校验了, 因为Navi阶段已经判断过了
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(Params.PerformanceActionID)

    -- 找到锚点角色
    local PilotIdx = Params.PilotIdx

    -- 锁定LookAt, 移动与技能; 隐藏武器; 改变DriveMode; 二次校验位置; 播放交互动画
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        local TargetEntity = Game.EntityManager:getEntity(Params.EntityLst[idx])
        if not TargetEntity then
            self:DebugMsg("no Entity with eid " .. Params.EntityLst[idx])
            self:failThenSwitchState()
            return
        end

        if PerformanceActionData.Type == Enum.EPerformanceActionType.Interactive and idx > 1 then
            -- 先默认角色是出现在第一个的
            self:OnEnterForInteractive(TargetEntity, PerformanceActionData)
            goto continue
        end

        -- 锁定LookAt
        TargetEntity:LockGazeInPerformance(not PerformanceActionData.StartCmd_bEnableLookAt)
        -- 锁定移动与技能
        if PerformanceActionData.StartCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
            TargetEntity:LockMoveInPerformance(true)
        elseif PerformanceActionData.StartCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
            TargetEntity:LockMoveInPerformance(false)
            TargetEntity:ChangeForceWalkMode(true)
        end
        TargetEntity:LockJumpInPerformance(PerformanceActionData.StartCmd_bLockJump)
        TargetEntity:LockSkillInPerformance(PerformanceActionData.StartCmd_bLockSkill)

		-- 改变DriveMode
		if self.Params.DirectorUpperControlLogicID[idx] then
			TargetEntity.LocoControlOperator:UnDoUpperControlLogic(self.Params.DirectorUpperControlLogicID[idx])
			self.Params.DirectorUpperControlLogicID[idx] = nil
		end
		local bEntityControl = TargetEntity == Game.me or Game.EntityManager:IsLocalEntity(TargetEntity.eid)
		if not bEntityControl and PerformanceActionData.StartCmd_DriveMode == "DriveByNet" then
			TargetEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.NetDriveDualCommonDirector)
			self.Params.DirectorUpperControlLogicID[idx] = Enum.LocoControlUpperControlLogic.NetDriveDualCommonDirector
		else
			TargetEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.LocalDriveDualCommonDirector)
			self.Params.DirectorUpperControlLogicID[idx] = Enum.LocoControlUpperControlLogic.LocalDriveDualCommonDirector
		end
        -- 二次校验位置
		if PerformanceActionData.StartCmd_NaviMode == Enum.EDirectorStartType.MotionWarpToSocket then
			if idx ~= PilotIdx then
				local TargetPos, TargetPitch, TargetYaw, TargetRoll
				local words = {}
				for word in string.gmatch(PerformanceActionData.AttachSocketOnLoop, "([^"..".".."]+)") do
					table.insert(words, word)
				end
				local AttachEntity = Game.EntityManager:getEntity(Params.EntityLst[PerformanceActionData.AttachActorSlotOnLoop])
				-- todo@shijingzhe:存在开始Navi时,载具Actor还没创建出来的情况,这里需要更进一步的处理,先判空
				if words[1] == "Mount" and AttachEntity and AttachEntity.MountEntity and AttachEntity.MountEntity.bInWorld then
					local AttachCompID = AttachEntity.MountEntity.CppEntity:KAPI_Actor_GetMainMesh()
					local worldFTrans = self:GetAttachSocketWorldTransform(AttachEntity.MountEntity, AttachCompID, words[2])
					TargetPos = worldFTrans:GetLocation()
					TargetPitch = worldFTrans:GetRotation():Rotator().Pitch
					TargetYaw = worldFTrans:GetRotation():Rotator().Yaw
					TargetRoll = worldFTrans:GetRotation():Rotator().Roll
				elseif AttachEntity then
					local AttachCompID = AttachEntity.CppEntity:KAPI_Actor_GetMainMesh()
					if not IsValidID(AttachCompID) then
						-- 没有SK Mesh, 那只能是StaticMesh了
						AttachCompID = AttachEntity.CppEntity:KAPI_Actor_GetComponentByClass(UStaticMeshComponent)
					end
					local AttachSocket = Params.AttachSocket or words[1]
					if AttachEntity.isAvatar == true or AttachEntity.isNpc == true then
						-- 如果是玩家orNpc, 那么socket是不需要有额外旋转的
						TargetPos = AttachEntity.CppEntity:KAPI_SceneID_GetSocketLocation(AttachCompID, AttachSocket)
						local TargetRotation = AttachEntity.CppEntity:KAPI_SceneID_GetSocketRotation(AttachCompID, AttachSocket)
						TargetPitch = TargetRotation.Pitch
						TargetYaw = TargetRotation.Yaw
						TargetRoll = TargetRotation.Roll
					else
						local worldFTrans = self:GetAttachSocketWorldTransform(AttachEntity, AttachCompID, AttachSocket)
						TargetPos = worldFTrans:GetLocation()
						TargetPitch = worldFTrans:GetRotation():Rotator().Pitch
						TargetYaw = worldFTrans:GetRotation():Rotator().Yaw
						TargetRoll = worldFTrans:GetRotation():Rotator().Roll
					end
				end
				self.MotionWarpTokenLst[idx] = TargetEntity:ObtainMotionWarpForTranslationAndRotationWithFixMode(
					EMoveCorrectorObtainPriority.Action, TargetPos, ERootWarpMode.RootAsCapsuleCenter, TargetPitch, TargetYaw, TargetRoll)
				--Log.Debug("LYL DEBUG TargetPos", TargetPos)
				--Log.Debug("LYL DEBUG TargetYaw", TargetYaw)
			else
				-- 校验锚点角色
				if Params.PilotPos ~= TargetEntity:GetPosition() or Params.PilotYaw ~= TargetEntity:GetRotation().Yaw then
					self.MotionWarpTokenLst[idx] = TargetEntity:ObtainMotionWarpForTranslationAndRotationWithFixMode(
						EMoveCorrectorObtainPriority.Action, Params.PilotPos, ERootWarpMode.RootAsCapsuleCenter, nil, Params.PilotYaw)
				end
			end
		elseif PerformanceActionData.StartCmd_NaviMode == Enum.EDirectorStartType.MotionWarp then
			if idx ~= PilotIdx then
				local worldFTrans = self:GetWorldTransform(Params.PilotPos, Params.PilotYaw, PerformanceActionData.PilotBiasPos, PerformanceActionData.PilotBiasYaw)
				self.MotionWarpTokenLst[idx] = TargetEntity:ObtainMotionWarpForTranslationAndRotationWithFixMode(
					EMoveCorrectorObtainPriority.Action, worldFTrans:GetLocation(), ERootWarpMode.KeepRoot, nil, worldFTrans:GetRotation():Rotator().Yaw)
			else
				-- 校验锚点角色
				if Params.PilotPos ~= TargetEntity:GetPosition() or Params.PilotYaw ~= TargetEntity:GetRotation().Yaw then
					self.MotionWarpTokenLst[idx] = TargetEntity:ObtainMotionWarpForTranslationAndRotationWithFixMode(
						EMoveCorrectorObtainPriority.Action, Params.PilotPos, ERootWarpMode.KeepRoot, nil, Params.PilotYaw)
				end
			end
		elseif PerformanceActionData.StartCmd_NaviMode == Enum.EDirectorStartType.Interpolation then
			if idx ~= PilotIdx then
				local worldFTrans = self:GetWorldTransform(Params.PilotPos, Params.PilotYaw, PerformanceActionData.PilotBiasPos, PerformanceActionData.PilotBiasYaw)
				self.DestSmoothTokenLst[idx] = TargetEntity:ObtainDestSmoothCorrector(nil, 0.2, nil, TargetEntity:GetPosition(),
					worldFTrans:GetLocation(), TargetEntity:GetRotation().Yaw, worldFTrans:GetRotation():Rotator().Yaw
				)
			else
				-- 校验锚点角色
				if Params.PilotPos ~= TargetEntity:GetPosition() or Params.PilotYaw ~= TargetEntity:GetRotation().Yaw then
					self.DestSmoothTokenLst[idx] = TargetEntity:ObtainDestSmoothCorrector(nil, 0.2, nil, TargetEntity:GetPosition(),
						Params.PilotPos, TargetEntity:GetRotation().Yaw, Params.PilotYaw
					)
				end
			end
		elseif PerformanceActionData.StartCmd_NaviMode == Enum.EDirectorStartType.InterpolationToSocket then
			if idx ~= PilotIdx then
				local TargetPos, TargetYaw
				local words = {}
				for word in string.gmatch(PerformanceActionData.AttachSocketOnLoop, "([^"..".".."]+)") do
					table.insert(words, word)
				end
				local AttachEntity = Game.EntityManager:getEntity(Params.EntityLst[PerformanceActionData.AttachActorSlotOnLoop])
				if words[1] == "Mount" and AttachEntity and AttachEntity.MountEntity then
					local SKCompID = AttachEntity.MountEntity.SKCompID
					TargetPos = AttachEntity.MountEntity.CppEntity:KAPI_SceneID_GetSocketLocation(SKCompID, words[2])
					TargetYaw = AttachEntity.MountEntity.CppEntity:KAPI_SceneID_GetSocketRotation(SKCompID, words[2]).Yaw
				elseif AttachEntity then
					-- TODO:pengqi05 这里代码存疑，压根没有Mesh这个变量，先注释
					--TargetPos = AttachEntity.Mesh:GetSocketLocation(words[1])
					--TargetYaw = AttachEntity.Mesh:GetSocketRotation(words[1]).Yaw
				end
				self.DestSmoothTokenLst[idx] = TargetEntity:ObtainDestSmoothCorrector(nil, 0.2, nil, TargetEntity:GetPosition(),
					TargetPos, TargetEntity:GetRotation().Yaw, TargetYaw
				)
			else
				-- 校验锚点角色
				if Params.PilotPos ~= TargetEntity:GetPosition() or Params.PilotYaw ~= TargetEntity:GetRotation().Yaw then
					self.DestSmoothTokenLst[idx] = TargetEntity:ObtainDestSmoothCorrector(nil, 0.2, nil, TargetEntity:GetPosition(),
						Params.PilotPos, TargetEntity:GetRotation().Yaw, Params.PilotYaw
					)
				end
			end
		else
			if idx ~= PilotIdx then
				local BiasPos = PerformanceActionData.PilotBiasPos
				local BiasYaw = PerformanceActionData.PilotBiasYaw
				local worldFTrans = self:GetWorldTransform(Params.PilotPos, Params.PilotYaw, BiasPos, BiasYaw)
				-- 做个寻路贴地检测
				local bGetGroundPos = self:GetGroundPos(worldFTrans:GetLocation(), TargetEntity)
				if bGetGroundPos then
					TargetEntity:SetPosition(worldFTrans:GetLocation())
					TargetEntity:SetRotation(worldFTrans:Rotator())
				else
					-- 这是一个阴间地方, 我们放弃强行对位置
				end
			else
				-- 校验锚点角色
				if Params.PilotPos ~= TargetEntity:GetPosition() or Params.PilotYaw ~= TargetEntity:GetRotation().Yaw then
					TargetEntity:SetPosition(Params.PilotPos)
					TargetEntity:SetRotation_P(0, Params.PilotYaw, 0)
				end
			end
		end
        -- 播放交互动画
		local AnimList = {}
		local replaceAnim
		if Params.ReplaceAnimInfo and Params.ReplaceAnimInfo[idx] then
			replaceAnim = Params.ReplaceAnimInfo[idx][1]
		end
		if replaceAnim and StringValid(replaceAnim[1]) then
			-- 动画替换
			for _, value in ipairs(replaceAnim) do
				table.insert(AnimList, {AssetID = value})
			end
		else
			for idd, value in ksbcpairs(PerformanceActionData.Animation) do
				if Params.RecoverAnimationIdx ~= nil and idd < Params.RecoverAnimationIdx then
					-- 快速恢复, Start阶段应该没需求, 我们糙一点以动作为单位
					goto next
				end
				if idd ~= #(PerformanceActionData.Animation) then
					table.insert(AnimList, {AssetID = value})
				end
				::next::
			end
		end
		
        if #AnimList == 0 then
            self:succThenSwitchState(TargetEntity.eid)
        else
	        local DurationSeq = {}
	        for _, Anim in ipairs(AnimList) do
				local FacadeControlData = TargetEntity:GetConfigFacadeControlData()
				if FacadeControlData then
					table.insert(DurationSeq, AnimLibUtils.GetAnimFeatureRemainingDuration(FacadeControlData.FacadeControlID, Anim))
				else
					table.insert(DurationSeq, -1)
				end
	        end
            local bSucc = TargetEntity:PlayAnimList(
                    AnimList,
		            DurationSeq,
                    false,
                    nil,
                    nil,
                    self,
                    "OnAnimListStop",
					TargetEntity.eid
            )
			if not bSucc or bSucc <= 0 then
				-- 动画有问题, 会直接触发cb中的failThenSwitchState
				return
			end
        end
        ::continue::
    end
end

function DirectorState_Start:OnAnimListStop(bInterrupt, EID)
	if not bInterrupt then
		-- 暂时的无奈之举
		if not self.succThenSwitchState then
			return
		end
		self:succThenSwitchState(EID)
	elseif self.bNeedDealFail then
		self:failThenSwitchState()
	end
end

function DirectorState_Start:OnEnterForInteractive(SceneActor, PerformanceActionData)
    -- 播放交互动画
    if #(PerformanceActionData.Animation) < 2 then
        -- 只有一个动画, 或者没有, 直接算作Loop
        self:succThenSwitchState(SceneActor.eid)
    else
		SceneActor:PlayAnimationByMontageAsyncLoad("DefaultSlot", PerformanceActionData.Animation[1], false, 0, 0)
        self:succThenSwitchState(SceneActor.eid)  -- 原来的交互写法就是玩家结束就切换, 这里暂时保留这个设计
    end
end

--@override
function DirectorState_Start:OnLeave(Params, NewState)
	self.bNeedDealFail = false
	-- 根据配置还原禁用状态
	local ActionInfo = Game.TableData.GetPerformanceActionDataRow(self.Params.PerformanceActionID)
	for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
		local TargetEntity = Game.EntityManager:getEntity(self.Params.EntityLst[idx])
		if not TargetEntity then
			goto continue
		end
		if PerformanceActionData.Type == Enum.EPerformanceActionType.Interactive and idx > 1 then
			-- 先默认角色是出现在第一个的
			goto continue
		end

		-- 如果前面处理了关闭,这里要恢复
		if not PerformanceActionData.StartCmd_bEnableLookAt then
			TargetEntity:LockGazeInPerformance(false)
		end

		-- 锁定移动与技能
		if PerformanceActionData.StartCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
			TargetEntity:LockMoveInPerformance(false)
		elseif PerformanceActionData.StartCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
			TargetEntity:LockMoveInPerformance(false)
			TargetEntity:ChangeForceWalkMode(false)
		end
		TargetEntity:LockJumpInPerformance(false)
		TargetEntity:LockSkillInPerformance(false)

		if self.DestSmoothTokenLst[idx] then
			TargetEntity:ReleaseDestSmoothCorrector(self.DestSmoothTokenLst[idx])
		end
		if self.MotionWarpTokenLst[idx] then
			TargetEntity:ReleaseMotionWarp(self.MotionWarpTokenLst[idx])
		end
		:: continue ::
	end
	self.LocalEntityLst = {}
end

--@override
function DirectorState_Start:OnRefresh(Params)

end

--@override
function DirectorState_Start:OnEntityExit(Params)
    local EntityEid = Params.ExitEntityEid
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end
    self:DebugMsg("Exit At Start State, Director Will finish")
    self:failThenSwitchState()
end

function DirectorState_Start:succThenSwitchState(EntityEid)
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end
    table.removeItem(self.LocalEntityLst, EntityEid)
    if #(self.LocalEntityLst) <= 0 then
        for _, thisEntityEid in ipairs(self.ArenaDirector.RemainEntityLst) do
            local TargetEntity = Game.EntityManager:getEntity(thisEntityEid)
            if TargetEntity and TargetEntity.BroadcastDirectorStartFinish then
                TargetEntity:BroadcastDirectorStartFinish(false)
            end
        end
        -- 去掉快速恢复的数据, 避免Loop重复计算
        self.Params.RecoverTime = nil
        self.Params.RecoverAnimationIdx = nil
        self.StateMachine:SwitchState(Enum.EDirectorInnerState.Loop, self.Params)
    end
end

function DirectorState_Start:failThenSwitchState()
	if self.Params.bFailThenSwitch then
		return
	end
    for _, thisEntityEid in ipairs(self.ArenaDirector.RemainEntityLst) do
        local TargetEntity = Game.EntityManager:getEntity(thisEntityEid)
        if TargetEntity and TargetEntity.BroadcastDirectorStartFinish then
            TargetEntity:BroadcastDirectorStartFinish(false)
        end
    end
    -- 去掉快速恢复的数据, 避免Loop重复计算
    self.Params.RecoverTime = nil
    self.Params.RecoverAnimationIdx = nil
	self.Params.bFailThenSwitch = true
    self.StateMachine:SwitchState(Enum.EDirectorInnerState.Finish, self.Params)
end

return DirectorState_Start