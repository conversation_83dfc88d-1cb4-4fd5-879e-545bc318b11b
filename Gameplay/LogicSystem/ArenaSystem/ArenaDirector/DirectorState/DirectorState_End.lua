local DirectorStateBase = kg_require("Gameplay.LogicSystem.ArenaSystem.ArenaDirector.DirectorState.DirectorStateBase")
local WEAK_FORCE_CONTROL_REASON_TAGS = kg_require("Shared.Const.ParallelBehaviorControlConst").WEAK_FORCE_CONTROL_REASON_TAGS
local DirectorState_End = DefineClass("DirectorState_End", DirectorStateBase)
local AnimLibUtils = kg_require("Shared.Utils.AnimLibUtils")

local EMoveDriveMode = import("EMoveDriveMode")
local EMoveCorrectorObtainPriority = import("EMoveCorrectorObtainPriority")
local ERootWarpMode = import("ERootWarpMode")

--@override
function DirectorState_End:InitDefaults()
    self.LocalEntityLst = {}
	self.MotionWarpTokenLst = {}
	self.DestSmoothTokenLst = {}
    self.bNeedDealFail = true
end

--@override
function DirectorState_End:BeforeUnInit()
    self:DebugMsg("BeforeUnInit in End State, Strange")
end

--@override
function DirectorState_End:OnEnter(Params, OldState)  -- luacheck: ignore
    for _, EntityEid in ipairs(self.ArenaDirector.RemainEntityLst) do
        table.insert(self.LocalEntityLst, EntityEid)
    end

    -- 这里可以不用仔细校验了, 因为Navi阶段已经判断过了
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(Params.PerformanceActionID)

    -- 锁定LookAt, 移动与技能; 隐藏武器; 改变DriveMode; 播放交互动画
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        if not table.contains(self.LocalEntityLst, Params.EntityLst[idx]) then
            -- 中途离开了
            goto continue
        end

        local TargetEntity = Game.EntityManager:getEntity(Params.EntityLst[idx])
        if not TargetEntity then
            self:DebugMsg("no Entity with eid " .. Params.EntityLst[idx])
            self:failThenSwitchState()
            return
        end
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Interactive and idx > 1 then
            -- 先默认角色是出现在第一个的
            self:OnEnterForInteractive(TargetEntity, PerformanceActionData)
            goto continue
        end
        -- 锁定LookAt
        TargetEntity:LockGazeInPerformance(not PerformanceActionData.EndCmd_bEnableLookAt)
        -- 锁定移动与技能
        if PerformanceActionData.EndCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
            TargetEntity:LockMoveInPerformance(true)
        elseif PerformanceActionData.EndCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
            TargetEntity:LockMoveInPerformance(false)
            TargetEntity:ChangeForceWalkMode(true)
        end
        TargetEntity:LockJumpInPerformance(PerformanceActionData.EndCmd_bLockJump)
        TargetEntity:LockSkillInPerformance(PerformanceActionData.EndCmd_bLockSkill)

		-- 改变DriveMode
		if self.Params.DirectorUpperControlLogicID[idx] then
			TargetEntity.LocoControlOperator:UnDoUpperControlLogic(self.Params.DirectorUpperControlLogicID[idx])
			self.Params.DirectorUpperControlLogicID[idx] = nil
		end
		local bEntityControl = TargetEntity == Game.me or Game.EntityManager:IsLocalEntity(TargetEntity.eid)
		if not bEntityControl and PerformanceActionData.EndCmd_DriveMode == "DriveByNet" then
			TargetEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.NetDriveDualCommonDirector)
			self.Params.DirectorUpperControlLogicID[idx] = Enum.LocoControlUpperControlLogic.NetDriveDualCommonDirector
		else
			TargetEntity.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.LocalDriveDualCommonDirector)
			self.Params.DirectorUpperControlLogicID[idx] = Enum.LocoControlUpperControlLogic.LocalDriveDualCommonDirector
		end
		-- 修正位置
		if PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.Interpolation or 
			PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.MotionWarp or 
			PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.MotionWarpToOri or
			PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.MotionWarpRecoverOnly then
			self:doEndNaviLogic(PerformanceActionData, idx, TargetEntity)
		end
        -- 播放交互动画
        local AnimList = {}
        local bAutoStop = true
		
		local replaceAnim
		if Params.ReplaceAnimInfo and Params.ReplaceAnimInfo[idx] then
			replaceAnim = Params.ReplaceAnimInfo[idx][3]
		end
		if replaceAnim and StringValid(replaceAnim[1]) then
			-- 动画替换
			for _, value in ipairs(replaceAnim) do
				table.insert(AnimList, {AssetID = value})
			end
		else
			for _, value in ksbcpairs(PerformanceActionData.BackSwingAnimation) do
				table.insert(AnimList, {AssetID = value})
			end
			if Params.ExtraBackSwingAnimation and Params.ExtraBackSwingAnimation[idx] and Params.ExtraBackSwingAnimation[idx] ~= "" then
				-- 有额外的后摇数据, 从FinishDirectorWithExtraParams过来的
				table.insert(AnimList, {AssetID = Params.ExtraBackSwingAnimation[idx]})
				bAutoStop = Params.bAutoStop[idx]
			end
		end

        if #AnimList == 0 then
            if PerformanceActionData.Type == Enum.EPerformanceActionType.Face then
                TargetEntity:StopFaceAnimation()
            else
                TargetEntity:StopAnimLibMontage()
            end
            self:succThenSwitchState(TargetEntity.eid)
        else
            if PerformanceActionData.Type == Enum.EPerformanceActionType.Face then
                -- 目前没有表情做后摇的需求, 我们先简单默认只有一个的情况, 不单独写AnimList逻辑
                TargetEntity:PlayFaceAnimation(PerformanceActionData.BackSwingAnimation[1], false, true)
            else
                local DurationSeq = {}
                for _, Anim in ipairs(AnimList) do
					local FacadeControlData = TargetEntity:GetConfigFacadeControlData()
					if FacadeControlData then
						table.insert(DurationSeq, AnimLibUtils.GetAnimFeatureRemainingDuration(FacadeControlData.FacadeControlID, Anim))
					else
						table.insert(DurationSeq, -1)
					end
                end
                local bSucc = TargetEntity:PlayAnimList(
						AnimList,
						DurationSeq,
						false,
						0,
						nil,
						self,
						"OnAnimListStop",
						TargetEntity.eid,
						PerformanceActionData
                )
				if not bSucc or bSucc <= 0 then
					-- 动画有问题, 会直接触发cb中的failThenSwitchState
					return
				end
            end
        end
        :: continue ::
    end
end

function DirectorState_End:OnAnimListStop(bInterrupt, EID)
	-- 暂时的无奈之举
	if not self.succThenSwitchState then
		return
	end
	if not bInterrupt then
		self:succThenSwitchState(EID)
	elseif self.bNeedDealFail then
		self:failThenSwitchState()
	end
end

function DirectorState_End:doEndNaviLogic(PerformanceActionData, idx, TargetEntity)
	-- 后摇回到原位逻辑
	if PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.MotionWarp then
		if idx ~= self.Params.PilotIdx then
			local ActionInfo = Game.TableData.GetPerformanceActionDataRow(self.Params.PerformanceActionID)
			local ignoreEntities = {}
			for index, _ in ksbcpairs(ActionInfo) do
				local ignoreEntity = Game.EntityManager:getEntity(self.Params.EntityLst[index])
				if ignoreEntity then
					table.insert(ignoreEntities, ignoreEntity)
				end
				if ignoreEntity and ignoreEntity.MountEntity then
					table.insert(ignoreEntities, ignoreEntity.MountEntity)
				end
			end
			
			if self.Params.LeaveLocation then
				local GroundPos = self:GetGroundPos(self.Params.LeaveLocation, TargetEntity)
				if GroundPos then
					GroundPos = TargetEntity:GetGameplayPointFromNaviPoint(GroundPos.X, GroundPos.Y, GroundPos.Z, ignoreEntities, true)
					self.MotionWarpTokenLst[idx] = TargetEntity:ObtainMotionWarpForTranslationWithFixMode(
						EMoveCorrectorObtainPriority.Action, GroundPos, ERootWarpMode.RootAsCapsuleBottom)
				end
			else
				local PilotEntity = Game.EntityManager:getEntity(self.Params.EntityLst[self.Params.PilotIdx])
				local worldFTrans = self:GetWorldTransform(PilotEntity:GetPosition(), PilotEntity:GetRotation().Yaw, PerformanceActionData.PilotBiasPos, PerformanceActionData.PilotBiasYaw)
				local GroundPos = self:GetGroundPos(worldFTrans:GetLocation(), TargetEntity)
				if GroundPos then
					GroundPos = TargetEntity:GetGameplayPointFromNaviPoint(GroundPos.X, GroundPos.Y, GroundPos.Z, ignoreEntities, true)
					self.MotionWarpTokenLst[idx] = TargetEntity:ObtainMotionWarpForTranslationWithFixMode(
						EMoveCorrectorObtainPriority.Action, GroundPos, ERootWarpMode.RootAsCapsuleBottom)
				end
			end
		else
			-- 校验锚点角色
		end
	elseif PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.MotionWarpToOri then
		if idx ~= self.Params.PilotIdx then
			local worldFTrans = self:GetWorldTransform(self.Params["PilotPos"], self.Params["PilotYaw"], PerformanceActionData.PilotBiasPos, PerformanceActionData.PilotBiasYaw)
			self.MotionWarpTokenLst[idx] = TargetEntity:ObtainMotionWarpForTranslationAndRotationWithFixMode(
				EMoveCorrectorObtainPriority.Action, worldFTrans:GetLocation(), ERootWarpMode.RootAsCapsuleBottom, nil, worldFTrans:GetRotation():Rotator().Yaw)
		else
			-- 校验锚点角色
		end	
	elseif PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.Interpolation then
		if idx ~= self.Params.PilotIdx then
			local PilotEntity = Game.EntityManager:getEntity(self.Params.EntityLst[self.Params.PilotIdx])
			local worldFTrans = self:GetWorldTransform(PilotEntity:GetPosition(), PilotEntity:GetRotation().Yaw, PerformanceActionData.PilotBiasPos, PerformanceActionData.PilotBiasYaw)
			self.DestSmoothTokenLst[idx] = TargetEntity:ObtainDestSmoothCorrector(nil, 0.2, nil, TargetEntity:GetPosition(),
				worldFTrans:GetLocation(), TargetEntity:GetRotation().Yaw, worldFTrans:GetRotation():Rotator().Yaw
			)
		else
			-- 校验锚点角色
		end
	elseif PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.DirectSet then
		if idx ~= self.Params.PilotIdx then
			local PilotEntity = Game.EntityManager:getEntity(self.Params.EntityLst[self.Params.PilotIdx])
			local PilotPosition = PilotEntity:GetPosition()
			local worldFTrans = self:GetWorldTransform(PilotEntity:GetPosition(), PilotEntity:GetRotation().Yaw, PerformanceActionData.PilotBiasPos, PerformanceActionData.PilotBiasYaw)
			-- 做个寻路贴地检测
			local bGetGroundPos = self:GetGroundPos(PilotPosition, TargetEntity)
			if bGetGroundPos then
				TargetEntity:SetPosition(worldFTrans:GetLocation())
				TargetEntity:SetRotation(worldFTrans:Rotator())
			else
				-- 这是一个阴间地方, 我们放弃强行对位置
			end
		else
			-- 校验锚点角色
		end
	elseif PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.MotionWarpRecoverOnly then
		self.MotionWarpTokenLst[idx] = 0
	end
end

function DirectorState_End:OnEnterForInteractive(SceneActor, PerformanceActionData)
    -- 播放交互动画
    if #PerformanceActionData.BackSwingAnimation > 0 then
		SceneActor:PlayAnimationByMontageAsyncLoad("DefaultSlot", PerformanceActionData.BackSwingAnimation[1], false, 1.0, 0)
    end
    self:succThenSwitchState(SceneActor.eid)  -- 原来的交互写法就是玩家结束就切换, 这里暂时保留这个设计
end

--@override
function DirectorState_End:OnLeave(Params, NewState)
    self.bNeedDealFail = false
    -- 根据配置还原禁用状态
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(self.Params.PerformanceActionID)
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        local TargetEntity = Game.EntityManager:getEntity(self.Params.EntityLst[idx])
        if not TargetEntity then
            goto continue
        end
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Interactive and idx > 1 then
            -- 先默认角色是出现在第一个的
            goto continue
        end

		-- 如果前面处理了关闭,这里要恢复
		if not PerformanceActionData.EndCmd_bEnableLookAt then
			TargetEntity:LockGazeInPerformance(false)
		end

        -- 锁定移动与技能
        if PerformanceActionData.EndCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
            TargetEntity:LockMoveInPerformance(false)
        elseif PerformanceActionData.EndCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
            TargetEntity:LockMoveInPerformance(false)
            TargetEntity:ChangeForceWalkMode(false)
        end
        TargetEntity:LockJumpInPerformance(false)
        TargetEntity:LockSkillInPerformance(false)

		-- 关闭Interpolation/MotionWarp
		if self.DestSmoothTokenLst[idx] then
			TargetEntity:ReleaseDestSmoothCorrector(self.DestSmoothTokenLst[idx])
		end
		if self.MotionWarpTokenLst[idx] then
			TargetEntity:ReleaseMotionWarp(self.MotionWarpTokenLst[idx])
		end
		if PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.DirectSet then
			self:doEndNaviLogic(PerformanceActionData, idx, TargetEntity)
		end
        -- 判断是否停止动画
        local bAutoStop = true
        if Params.ExtraBackSwingAnimation and Params.ExtraBackSwingAnimation[idx] and Params.ExtraBackSwingAnimation[idx] ~= "" then
            -- 有额外的后摇数据, 从FinishDirectorWithExtraParams过来的
            bAutoStop = Params.bAutoStop[idx]
        end
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Face then
            TargetEntity:StopFaceAnimation()
        else
			if bAutoStop then
				TargetEntity:StopAnimLibMontage()
			end
        end

        :: continue ::
    end
    self.LocalEntityLst = {}
end

--@override
function DirectorState_End:OnRefresh(Params)

end

--@override
function DirectorState_End:OnEntityExit(Params)
    local EntityEid = Params.ExitEntityEid
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end
    local TargetEntity = Game.EntityManager:getEntity(EntityEid)
    if not TargetEntity then
        return
    end
    -- 给他做一个单人的退出
    table.removeItem(self.LocalEntityLst, EntityEid)
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(self.Params.PerformanceActionID)
    for idx, PerformanceActionData in ksbcpairs(ActionInfo) do
        if self.Params.EntityLst[idx] ~= EntityEid then
            goto continue
        end

		-- 如果前面处理了关闭,这里要恢复
		if not PerformanceActionData.EndCmd_bEnableLookAt then
			TargetEntity:LockGazeInPerformance(false)
		end

        -- 锁定移动与技能
        if PerformanceActionData.EndCmd_bLockMove == Enum.EDirectorLockMoveType.Lock then
            TargetEntity:LockMoveInPerformance(false)
        elseif PerformanceActionData.EndCmd_bLockMove == Enum.EDirectorLockMoveType.WalkOnly then
            TargetEntity:LockMoveInPerformance(false)
            TargetEntity:ChangeForceWalkMode(false)
        end
        TargetEntity:LockJumpInPerformance(false)
        TargetEntity:LockSkillInPerformance(false)

		-- 关闭Interpolation/MotionWarp
		if self.DestSmoothTokenLst[idx] then
			TargetEntity:ReleaseDestSmoothCorrector(self.DestSmoothTokenLst[idx])
		end
		if self.MotionWarpTokenLst[idx] then
			TargetEntity:ReleaseMotionWarp(self.MotionWarpTokenLst[idx])
		end
		-- 改变DriveMode
		if self.Params.DirectorUpperControlLogicID[idx] then
			TargetEntity.LocoControlOperator:UnDoUpperControlLogic(self.Params.DirectorUpperControlLogicID[idx])
			self.Params.DirectorUpperControlLogicID[idx] = nil
		end
        -- 结束动画
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Face then
            TargetEntity:StopFaceAnimation()
        else
            TargetEntity:StopAnimLibMontage()
        end
		if PerformanceActionData.EndCmd_NaviMode == Enum.EDirectorEndType.DirectSet then
			self:doEndNaviLogic(PerformanceActionData, idx, TargetEntity)
		end
        self:DebugMsg("Entity Exit in End State " .. EntityEid)
        :: continue ::
    end
    if #(self.LocalEntityLst) <= 0 then
        self.StateMachine:SwitchState(Enum.EDirectorInnerState.Finish, self.Params)
    end
end

function DirectorState_End:succThenSwitchState(EntityEid)
    if not table.contains(self.LocalEntityLst, EntityEid) then
        return
    end
    table.removeItem(self.LocalEntityLst, EntityEid)
    if #(self.LocalEntityLst) <= 0 then
        self.StateMachine:SwitchState(Enum.EDirectorInnerState.Finish, self.Params)
    end
end

function DirectorState_End:failThenSwitchState()
	if self.Params.bFailThenSwitch then
		return
	end
	self.Params.bFailThenSwitch = true
    self.StateMachine:SwitchState(Enum.EDirectorInnerState.Finish, self.Params)
end

return DirectorState_End