local TimerBase = require("Framework.KGFramework.KGCore.TimerManager.TimerBase")


--- @class DirectorStateBase: TimerBase
local DirectorStateBase = DefineClass("DirectorStateBase", TimerBase)
local ERelativeTransformSpace = import("ERelativeTransformSpace")

DirectorStateBase.FindGroundPosRange = FVector(100, 100, 300)

function DirectorStateBase:ctor(InnerStateMachine)
    self:InitDefaults()
    self:OnInit(InnerStateMachine)
end

function DirectorStateBase:dtor()
    self:OnUnInit()
end

-- 执行一些初始化逻辑
--@virtual
function DirectorStateBase:InitDefaults()

end

function DirectorStateBase:OnInit(StateMachine)
    self.StateMachine = StateMachine
    self.ArenaDirector = StateMachine.ArenaDirector
end

-- 执行一些清理逻辑
--@virtual
function DirectorStateBase:BeforeUnInit()

end

function DirectorStateBase:OnUnInit()
    self.StateMachine = nil
    self.ArenaDirector = nil
end

function DirectorStateBase:Enter(Params, OldState)
    self.Params = Params
    self:OnEnter(Params, OldState)
end

function DirectorStateBase:Leave(Params, NextState)
    self:OnLeave(Params)
end

function DirectorStateBase:Refresh(Params)
    self:OnRefresh(Params)
end

function DirectorStateBase:EntityExit(Params)
    self:OnEntityExit(Params)
end

--@virtual
function DirectorStateBase:OnEnter(Params, OldState)

end

--@virtual
function DirectorStateBase:OnLeave(Params, NextState)

end

--@virtual
function DirectorStateBase:OnRefresh(Params)

end

--@virtual
function DirectorStateBase:OnEntityExit(Params)

end

function DirectorStateBase:GetWorldTransform(PilotPos, PilotYaw, PilotBiasPos, PilotBiasYaw)
	-- todo 这边计算的方式不太好, 有构造和穿透的问题, 有空优化一下
    local EntityFTrans = FTransform(FRotator(0, PilotYaw, 0):ToQuat(), PilotPos)
    local OffsetFTrans = FTransform(FRotator(0, PilotBiasYaw, 0):ToQuat(), FVector(PilotBiasPos[1], PilotBiasPos[2], PilotBiasPos[3]))
    local worldM3DTrans = self:doGetWorldTransform(M3D.ToTransform(EntityFTrans), M3D.ToTransform(OffsetFTrans))
    return M3D.ToFTransform(worldM3DTrans)
end

function DirectorStateBase:GetAttachSocketWorldTransform(InEntity, AttachCompID, SocketName)
	-- todo 这边计算的方式不太好, 有构造和穿透的问题, 有空优化一下
	local LocalSocketTrans = InEntity.CppEntity:KAPI_SceneID_GetSocketTransform(AttachCompID, SocketName, ERelativeTransformSpace.RTS_Actor)
	local LocalSocketRotator = LocalSocketTrans:GetRotation():Rotator()
	LocalSocketRotator.Yaw = LocalSocketRotator.Yaw + 90
	LocalSocketTrans:SetRotation(LocalSocketRotator:ToQuat())
	local worldM3DTrans = self:doGetWorldTransform(M3D.AssembleTransform(InEntity:GetPosition(), M3D.FRotToQuat(InEntity:GetRotation())), M3D.ToTransform(LocalSocketTrans))
	return M3D.ToFTransform(worldM3DTrans)
end

-- 获取世界坐标系
function DirectorStateBase:doGetWorldTransform(BaseTransform, OffsetTransform)
    local FinalTransform = M3D.Transform()
    OffsetTransform:Mul(BaseTransform, FinalTransform)
    return FinalTransform
end

-- 生成贴地的pos
function DirectorStateBase:GetGroundPos(InPos, InEntity)
    return LuaScriptAPI.GetNearestNaviPoint(InEntity.CharacterID, InPos.X, InPos.Y, InPos.Z, 
        DirectorStateBase.FindGroundPosRange.X, DirectorStateBase.FindGroundPosRange.Y, DirectorStateBase.FindGroundPosRange.Z)
end

function DirectorStateBase:DebugMsg(Msg)
    Log.DebugFormat("Performance [%s]: DirectorID:%s, %s", self.__cname, self.StateMachine.ArenaDirector.DirectorID, Msg)
end

return DirectorStateBase