---@class EquipmentSender:SystemSenderBase
local EquipmentSender = DefineClass("EquipmentSender",SystemSenderBase)

-- function EquipmentSender:ReqTest()
--     self.Bridge:Test()
-- end

function EquipmentSender:ReqEquipPutOn(invId, slot, uid, slotIdx, equipType)
	if Game.me:SCCanExecute(Enum.EStateConflictAction.EquipChange) == Enum.EStateConflictType.BLOCK then 
		return
	end
	self.Bridge:ReqEquipPutOn(invId, slot, uid, slotIdx, equipType)

    --if GetMainPlayerPropertySafely( "InBattle") then
    --    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.COMBAT_DENY_EQUIP)
    --    return
    --end
    --self.Bridge:ReqEquipPutOn(invId, slot, uid, slotIdx, equipType)
end

function EquipmentSender:ReqEquipPutOff(slot,uid)
	if Game.me:SCCanExecute(Enum.EStateConflictAction.EquipChange) == Enum.EStateConflictType.BLOCK then
		return
	end
	self.Bridge:ReqEquipPutOff(slot,uid)

    --if GetMainPlayerPropertySafely( "InBattle") then
    --    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.COMBAT_DENY_EQUIP)
    --    return
    --end
    --self.Bridge:ReqEquipPutOff(slot,uid)
end

function EquipmentSender:ReqEquipGetRandomProbabilityInfo()
    self.Bridge:ReqEquipGetRandomProbabilityInfo()
end

function EquipmentSender:ReqChangeEquipPlanName(index, string)
    self.Bridge:ReqChangeEquipPlanName(index, string)
end

function EquipmentSender:ReqSwitchEquipPlan(planId, invId, newEquip, changeEquip, keepEquip)
	if Game.me:SCCanExecute(Enum.EStateConflictAction.EquipChange) == Enum.EStateConflictType.BLOCK then
		return
	end
	self.Bridge:ReqSwitchEquipPlan(planId, invId, newEquip, changeEquip, keepEquip)
end

function EquipmentSender:ReqEquipRandomPropEnhance(slot,gbId)
	self.Bridge:ReqEquipRandomPropEnhance(slot,gbId)
end
function EquipmentSender:ReqEquipRandomPropSelect(slot,gbId,idx)
	self.Bridge:ReqEquipRandomPropSelect(slot,gbId,idx)
end
function EquipmentSender:ReqEquipClearExtraRandomProps(slot,gbId)
	self.Bridge:ReqEquipClearExtraRandomProps(slot,gbId)
end
function EquipmentSender:ReqUpdateWordLock(slotId,dict)
	self.Bridge:ReqUpdateWordLock(slotId,dict)
end
function EquipmentSender:ReqUpdateWordLockSwitch(slot, bLock)
	self.Bridge:ReqUpdateWordLockSwitch(slot, bLock)
end


function EquipmentSender:ReqSwitchEquipFixedWord(slotIdx,idx,groupId)
	--if not Game.EquipmentSystem.model.equipmentBodyInfo.slotsWordSpace[slotIdx][groupId] then
	--	Log.Error("Word NOT Exist: slot: "..slotIdx..". group: "..groupId)
	--	return
	--end
	self.Bridge:ReqSwitchEquipFixedWord(slotIdx,idx,groupId)
end

function EquipmentSender:ReqEquipChangeSingleRandomWord(slotIdx,gbId,wordId,invId,invSlot,invGbId)
	self.Bridge:ReqEquipChangeSingleRandomWord(slotIdx,gbId,wordId,invId,invSlot,invGbId)
end

function EquipmentSender:ReqEquipmentReform(slotId, equipId)
	self.Bridge:ReqEquipmentReform(slotId, equipId)
end

function EquipmentSender:ReqGetBestReformList(slotId)
	self.Bridge:ReqGetBestReformList(slotId)
end

function EquipmentSender:ReqEquipmentApplyBestReform(slotId, equipId, propIndex)
	self.Bridge:ReqEquipmentApplyBestReform(slotId, equipId, propIndex)
end

function EquipmentSender:ReqSaveCurrentEquipmentReform(slotId, equipId, index)
	self.Bridge:ReqSaveCurrentEquipmentReform(slotId, equipId, index)
end

function EquipmentSender:ReqGetReformMemerySpace()
	self.Bridge:ReqGetReformMemerySpace()
end

function EquipmentSender:ReqAutoReplaceRandomPropFromMemerySpace(slotId, gbId)
	self.Bridge:ReqAutoReplaceRandomPropFromMemerySpace(slotId, gbId)
end

--- 置换记忆空间中的随机属性
---@param slot number 装备的slot
---@param gbId string 装备的uid
---@param propType number 属性类型
---@param key string 记忆空间中的key
---@param index number 随机属性index
---@param propId number 随机属性id
function EquipmentSender:ReqReplaceOneRandomPropFromMemerySpace( slot, gbId, propType, key, index, propId)
	self.Bridge:ReqReplaceOneRandomPropFromMemerySpace(slot, gbId, propType, key, index, propId)
end


return EquipmentSender