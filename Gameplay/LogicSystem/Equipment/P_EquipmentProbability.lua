---@class P_EquipmentProbability:UIController
local P_EquipmentProbability = DefineClass("P_EquipmentProbability", UIController)

local StringConst = require "Data.Config.StringConst.StringConst"
local P_EquipmentCondenseInfo = kg_require "Gameplay.LogicSystem.Equipment.P_EquipmentCondenseInfo"
local P_EquipmentProbWordItem = kg_require "Gameplay.LogicSystem.Equipment.P_EquipmentProbWordItem"
local P_ComCurrency = kg_require "Gameplay.LogicSystem.CommonUI.P_ComCurrency"
local ESlateVisibility = import("ESlateVisibility")

P_EquipmentProbability.eventBindMap = {
    [_G.EEventTypes.ON_EQUIP_CONDENSE_ALLRESET] = "OnAllReset",
    [_G.EEventTypes.ON_EQUIP_CONDENSE_RESET] = "OnReset",
    [_G.EEventTypes.ON_EQUIP_CONDENSE_UP] = "OnUp",
    [_G.EEventTypes.ON_EQUIP_CONDENSE_DOWN] = "OnDown",
    [EEventTypesV2.BAG_ITEM_COUNT_CHANGE] = "RefreshVizInfo",
}

function P_EquipmentProbability:OnCreate()
    -- luacheck: push ignore
    self.classDataList = nil
    self.currTabSelect = nil
    self.currWordSelect = nil
    self.slotIdx = nil
    self.groupWeightList = nil
    self.IsEnough = nil
    self.moneyId = nil
    self.moneyNum = nil
    self.IsUpMax = nil
    self.IsDownMax = nil

    self.ClassList = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.ClassList.WBP_ComList, P_EquipmentCondenseInfo, "WBP_ComList")
    self.WordList = BaseList.CreateList(self, BaseList.Kind.OldList, self.View.WordList, P_EquipmentProbWordItem)

    self.DownCost = self:BindComponent(self.View.ProbInfo.WBP_ComCurrencyDown, P_ComCurrency)
    self.UpCost = self:BindComponent(self.View.ProbInfo.WBP_ComCurrencyUp, P_ComCurrency)
    -- luacheck: pop

    self.View.WBP_ComPopupL.WBP_ComPopupTitle.Text_Title:SetText(StringConst.Get("EQUIP_WORD_TITLE"))
    self.View.Text_TotalDown:SetText(StringConst.Get("EQUIP_WORD_DOWNRESULT"))
    self.View.Empty.Text_Empty:SetText(StringConst.Get("EQUIP_WORD_SELECT"))

    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComPopupL.WBP_ComPopupTitle.WBP_ComBtnClose.Button,
        self.OnClickClose)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_AllReset, self.OnClickAllReset)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.ProbInfo.Btn_SingleReset, self.OnClickSingleReset)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.ProbInfo.Btn_Up.Btn_Com, self.OnClickUp)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.ProbInfo.Btn_Down.Btn_Com, self.OnClickDown)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Tip.Big_Button_ClickArea, self.OnClickTips)
end

function P_EquipmentProbability:OnRefresh(Params)
    --self.slotIdx = Params.SlotIdx
    --local slotData = Game.TableData.GetEquipmentSlotDataRow(self.slotIdx)
    --self.View.Text_TotalUp:SetText(string.format(StringConst.Get("EQUIP_WORD_UPRESULT"), slotData.Name))
    --self.View.ProbInfo.Text_PieInfo:SetText(string.format(StringConst.Get("EQUIP_WORD_SINGLETIP"), slotData.Name))
    --local groupsList = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].stockInfo
    ---- local classList = Game.TableData.GetEquipmentWordRandomClassDataTable()
    --self.classDataList = {}
    --local tempList = {}
    --for groupId, val in pairs(groupsList) do
    --    local groupClassData = Game.TableData.Get_EquipmentWordRandomClassGroupToRow()[groupId]
    --    if not groupClassData then
    --        Log.Error("No groupId: " .. tostring(groupId))
    --        return
    --    end
    --    local groupClassId = groupClassData.ID
    --    if tempList[groupClassId] then
    --        tempList[groupClassId][#tempList[groupClassId] + 1] = groupId
    --    else
    --        tempList[groupClassId] = { groupId }
    --    end
    --end
    --for id, groupList in pairs(tempList) do
    --    local groupClassData = Game.TableData.GetEquipmentWordRandomClassDataRow(id)
    --    self.classDataList[#self.classDataList + 1] = {
    --        classId = id,
    --        groupList = groupList,
    --        sortIdx = groupClassData
    --            .Appear
    --    }
    --end
    --table.sort(self.classDataList, function(a, b)
    --    if a.sortIdx ~= b.sortIdx then
    --        return a.sortIdx < b.sortIdx
    --    end
    --    return a.classId < b.classId
    --end)
    --self.ClassList:SetData(#self.classDataList, 1)
    --self.currTabSelect = 1
    --self.ClassList:Sel(1)
	--
    --self:RefreshWord(true, true)
end

function P_EquipmentProbability:OnClickClose()
    self.ClassList:CancelSel()
    self.WordList:CancelSel()
    UI.HideUI("P_EquipmentProbability")

    self.classDataList = nil
    self.currTabSelect = nil
    self.currWordSelect = nil
    self.slotIdx = nil
    self.groupWeightList = nil
    self.IsEnough = nil
    self.moneyId = nil
    self.moneyNum = nil
end

function P_EquipmentProbability:OnClickTips()
    Game.TipsSystem:ShowTips(Enum.ETipsData.EQUIP_WORD, self.View.Btn_Tip.Big_Button_ClickArea:GetCachedGeometry())
end

function P_EquipmentProbability:RefreshWord(NeedSort, isFirstShow)
    -- 总调整区域
    self.View.Text_UpNum:SetText(string.format(StringConst.Get("EQUIP_WORD_RESULTNUM"),
        Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalUpCount,
        Enum.EEquipmentGrowConstData.RANDOM_WORD_UPLIMIT))
    self.View.Text_DownNum:SetText(string.format(StringConst.Get("EQUIP_WORD_RESULTNUM"),
        Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalDownCount,
        Enum.EEquipmentGrowConstData.RANDOM_WORD_DOWNLIMIT))

    self.IsUpMax = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalUpCount ==
        Enum.EEquipmentGrowConstData.RANDOM_WORD_UPLIMIT
    self.IsDownMax = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalDownCount ==
        Enum.EEquipmentGrowConstData.RANDOM_WORD_DOWNLIMIT

    -- Word列表
    if NeedSort then
        table.sort(self.classDataList[self.currTabSelect].groupList, function(a, b)
            local aIsAdjust = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].stockInfo[a].adjustCount ~= 0
            local bIsAdjust = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].stockInfo[b].adjustCount ~= 0
            if aIsAdjust ~= bIsAdjust then
                return aIsAdjust
            end
            return a < b
        end)
    end
    -- 计算概率
    local equipInfo = Game.EquipmentSystem.model.equipmentSlotInfo.slots[self.slotIdx]
    local equipSet = Game.EquipmentSystem:GetEquipSetById(equipInfo.itemId)
    self.groupWeightList = {}
    local totalWeight = 0
    for idx, groupId in ipairs(self.classDataList[self.currTabSelect].groupList) do
        local groupData = Game.TableData.GetEquipmentWordRandomGroupDataRow(groupId)
        local weight = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].stockInfo[groupId].value *
            groupData["Set_" .. equipSet]
        local arrow = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].stockInfo[groupId].adjustCount
        self.groupWeightList[idx] = { GroupId = groupId, Weight = weight, Arrow = arrow }
        totalWeight = totalWeight + weight
    end
    for idx, groupId in ipairs(self.classDataList[self.currTabSelect].groupList) do
        local prob = self.groupWeightList[idx].Weight / totalWeight
        self.groupWeightList[idx].Prob = prob
    end
    for idx, classInfo in pairs(self.classDataList) do
        if idx ~= self.currTabSelect then
            for _, groupId in ipairs(classInfo.groupList) do
                local groupData = Game.TableData.GetEquipmentWordRandomGroupDataRow(groupId)
                local weight = Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].stockInfo[groupId].value *
                    groupData["Set_" .. equipSet]
                totalWeight = totalWeight + weight
            end
        end
    end
    for idx, groupId in ipairs(self.classDataList[self.currTabSelect].groupList) do
        local prob = self.groupWeightList[idx].Weight / totalWeight
        self.groupWeightList[idx].RealProb = prob
    end

    self.WordList:SetData(#self.classDataList[self.currTabSelect].groupList, 1)
    if isFirstShow then
        self.WordList:Sel(1)
        self.currWordSelect = 1
    end

    self:RefreshVizInfo()
end

-- luacheck: push ignore
function P_EquipmentProbability:RefreshVizInfo()
    if self.currWordSelect then
        self.View.ProbInfo:SetVisibility(ESlateVisibility.Visible)
        self.View.Empty:SetVisibility(ESlateVisibility.Collapsed)

        local arrow = self.groupWeightList[self.currWordSelect].Arrow
        if arrow >= 0 then
            self.View.ProbInfo.Text_Adjust:SetText(StringConst.Get("EQUIP_WORD_SINGLEUP"))
        else
            self.View.ProbInfo.Text_Adjust:SetText(StringConst.Get("EQUIP_WORD_SINGLEDOWN"))
        end

        self.View.ProbInfo.Text_Cnt:SetText(string.format(StringConst.Get("EQUIP_WORD_SINGLENUM"), math.abs(arrow),
            Enum.EEquipmentGrowConstData.RANDOM_WORD_LIMIT))
        if math.abs(arrow) == Enum.EEquipmentGrowConstData.RANDOM_WORD_LIMIT then
            self.View.ProbInfo.HB_Cost:SetVisibility(ESlateVisibility.Collapsed)
            self.View.ProbInfo.OL_Limit:SetVisibility(ESlateVisibility.Visible)
        else
            self.View.ProbInfo.HB_Cost:SetVisibility(ESlateVisibility.Visible)
            self.View.ProbInfo.OL_Limit:SetVisibility(ESlateVisibility.Collapsed)

            self.IsEnough = false
            local costKey = Game.TableData.Get_EquipmentGrowBodyConfigBatchSlotToID()
                [(Game.EquipmentSystem.model.equipmentBatch or 1) .. ";" .. self.slotIdx]
            local costData = Game.TableData.GetEquipmentGrowWordUpDataRow(costKey)
            if arrow >= 0 then
                self.View.ProbInfo.Btn_Up.Text_Com:SetText(StringConst.Get("EQUIP_WORD_UP"))
                self.View.ProbInfo.Btn_Up:SetIsEnabled(true)
                self.View.ProbInfo.WBP_ComCurrencyUp:SetVisibility(ESlateVisibility.Visible)
                for resId, cnt in ksbcpairs(costData.UpConsume) do
                    local resData = Game.TableData.GetItemNewDataRow(resId)
                    if resData.type == 4 then -- 货币
                        -- self.MoneyCost:SetData(resId,cnt)
                        self.moneyId = resId
                        self.moneyNum = cnt
                    else -- 材料
                        self.UpCost:SetData(resId, cnt, true)
                        self.IsEnough = Game.BagSystem:GetItemCount(resId) >= cnt
                    end
                end
            else
                self.View.ProbInfo.Btn_Up.Text_Com:SetText(StringConst.Get("EQUIP_WORD_CANNOTUP"))
                self.View.ProbInfo.Btn_Up:SetIsEnabled(false)
                self.View.ProbInfo.WBP_ComCurrencyUp:SetVisibility(ESlateVisibility.Hidden)
            end

            if arrow <= 0 then
                self.View.ProbInfo.Btn_Down.Text_Com:SetText(StringConst.Get("EQUIP_WORD_DOWN"))
                self.View.ProbInfo.Btn_Down:SetIsEnabled(true)
                self.View.ProbInfo.WBP_ComCurrencyDown:SetVisibility(ESlateVisibility.Visible)
                -- self.DownCost
                for resId, cnt in ksbcpairs(costData.DownConsume) do
                    local resData = Game.TableData.GetItemNewDataRow(resId)
                    if resData.type == 4 then -- 货币
                        -- self.MoneyCost:SetData(resId,cnt)
                        self.moneyId = resId
                        self.moneyNum = cnt
                    else -- 材料
                        self.DownCost:SetData(resId, cnt, true)
                        self.IsEnough = Game.BagSystem:GetItemCount(resId) >= cnt
                    end
                end
            else
                self.View.ProbInfo.Btn_Down.Text_Com:SetText(StringConst.Get("EQUIP_WORD_CANNOTDOWN"))
                self.View.ProbInfo.Btn_Down:SetIsEnabled(false)
                self.View.ProbInfo.WBP_ComCurrencyDown:SetVisibility(ESlateVisibility.Hidden)
            end
        end
    else
        self.View.ProbInfo:SetVisibility(ESlateVisibility.Collapsed)
        self.View.Empty:SetVisibility(ESlateVisibility.Visible)
    end

    if #self.groupWeightList > 10 then
        Log.Warning("Exceed Groups Limit")
        return
    end
    local wordCnt = 1
    local probCnt = 0
    local isFirst = true
    for wordIdx, wordInfo in ipairs(self.groupWeightList) do
        if self.currWordSelect == wordIdx then
            self.View.ProbInfo.Img_Pie:GetDynamicMaterial():SetScalarParameterValue("Selected_RatioStart", probCnt)
            probCnt = probCnt + wordInfo.Prob
            self.View.ProbInfo.Img_Pie:GetDynamicMaterial():SetScalarParameterValue("Selected_RatioEnd", probCnt)
        else
            if isFirst then
                self.View.ProbInfo.Img_Pie:GetDynamicMaterial():SetScalarParameterValue("Ratio_Line" .. wordCnt, probCnt)
                wordCnt = wordCnt + 1
            end
            probCnt = probCnt + wordInfo.Prob
            self.View.ProbInfo.Img_Pie:GetDynamicMaterial():SetScalarParameterValue("Ratio_Line" .. wordCnt, probCnt)
            wordCnt = wordCnt + 1
        end
        isFirst = false
    end
    for i = 1, 8 do
        if i < wordCnt then
            self.View.ProbInfo.Img_Pie:GetDynamicMaterial():SetScalarParameterValue("Opacity_Line" .. i, 1)
        else
            self.View.ProbInfo.Img_Pie:GetDynamicMaterial():SetScalarParameterValue("Opacity_Line" .. i, 0)
        end
    end
end
-- luacheck: pop

function P_EquipmentProbability:OnClickAllReset()
    if Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalUpCount == 0 and
        Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalDownCount == 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_WORD_REVERTNONE)
        return
    end

    local costKey = Game.TableData.Get_EquipmentGrowBodyConfigBatchSlotToID()
        [(Game.EquipmentSystem.model.equipmentBatch or 1) .. ";" .. self.slotIdx]
    local costData = Game.TableData.GetEquipmentGrowWordUpDataRow(costKey)
    local costNum = 0
    for k, v in pairs(costData.UpConsume) do
        costNum = costNum + v * Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalUpCount
    end
    for k, v in pairs(costData.DownConsume) do
        costNum = costNum + v * Game.EquipmentSystem.model.equipmentStockInfo[self.slotIdx].adjustTotalDownCount
    end

    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.EQUIP_WORDREVERT_ALL, function()
        local equipInfo = Game.EquipmentSystem.model.equipmentSlotInfo.slots[self.slotIdx]
        Game.me.remote:ReqEquipRandomPropProbabilityAllReset(self.slotIdx, equipInfo.gbId)
    end, nil, { math.floor(costNum * 0.8) })
end

function P_EquipmentProbability:OnClickSingleReset()
    local arrow = self.groupWeightList[self.currWordSelect].Arrow or 0
    if arrow == 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_WORD_REVERTNONE)
        return
    end
    local costKey = Game.TableData.Get_EquipmentGrowBodyConfigBatchSlotToID()
        [(Game.EquipmentSystem.model.equipmentBatch or 1) .. ";" .. self.slotIdx]
    local costData = Game.TableData.GetEquipmentGrowWordUpDataRow(costKey)

    local costNum = 0
    if arrow > 0 then
        for k, v in pairs(costData.UpConsume) do
            costNum = costNum + v * arrow
        end
    elseif arrow < 0 then
        for k, v in pairs(costData.DownConsume) do
            costNum = costNum - v * arrow
        end
    end

    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.EQUIP_WORDREVERT_SINGLE, function()
        local equipInfo = Game.EquipmentSystem.model.equipmentSlotInfo.slots[self.slotIdx]
        Game.me.remote:ReqEquipRandomPropProbabilitySingleReset(self.slotIdx, equipInfo.gbId,
            self.groupWeightList[self.currWordSelect].GroupId)
    end, nil, { math.floor(costNum * 0.8) })
end

function P_EquipmentProbability:OnClickUp()
    if self.IsUpMax then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_WORD_UPLIMIT)
        return
    end
    if not self.IsEnough then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB1_AUTO_LACK)
        return
    end
    if not self.moneyId then
        local equipInfo = Game.EquipmentSystem.model.equipmentSlotInfo.slots[self.slotIdx]
        Game.me.remote:ReqEquipRandomPropProbabilityUp(self.slotIdx, equipInfo.gbId,
            self.groupWeightList[self.currWordSelect].GroupId)
        return
    end
    Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(self.moneyId, self.moneyNum, function()
        local equipInfo = Game.EquipmentSystem.model.equipmentSlotInfo.slots[self.slotIdx]
        Game.me.remote:ReqEquipRandomPropProbabilityUp(self.slotIdx, equipInfo.gbId,
            self.groupWeightList[self.currWordSelect].GroupId)
    end)
end

function P_EquipmentProbability:OnClickDown()
    if self.IsDownMax then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_WORD_DOWNLIMIT)
        return
    end
    if not self.IsEnough then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB1_AUTO_LACK)
        return
    end
    if not self.moneyId then
        local equipInfo = Game.EquipmentSystem.model.equipmentSlotInfo.slots[self.slotIdx]
        Game.me.remote:ReqEquipRandomPropProbabilityDown(self.slotIdx, equipInfo.gbId,
            self.groupWeightList[self.currWordSelect].GroupId)
        return
    end
    Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(self.moneyId, self.moneyNum, function()
        local equipInfo = Game.EquipmentSystem.model.equipmentSlotInfo.slots[self.slotIdx]
        Game.me.remote:ReqEquipRandomPropProbabilityDown(self.slotIdx, equipInfo.gbId,
            self.groupWeightList[self.currWordSelect].GroupId)
    end)
end

function P_EquipmentProbability:OnAllReset(slot, uid)
    if self.slotIdx ~= slot then
        return
    end
    self.currWordSelect = nil
    self.WordList:CancelSel()
    self:RefreshWord(nil, true)
end

function P_EquipmentProbability:OnReset(slot, uid, groupId)
    if self.slotIdx ~= slot then
        return
    end
    if self.groupWeightList[self.currWordSelect].GroupId ~= groupId then
        return
    end
    self:RefreshWord()
end

function P_EquipmentProbability:OnUp(slot, uid, groupId)
    if self.slotIdx ~= slot then
        return
    end
    if self.groupWeightList[self.currWordSelect].GroupId ~= groupId then
        return
    end
    self:RefreshWord()
end

function P_EquipmentProbability:OnDown(slot, uid, groupId)
    if self.slotIdx ~= slot then
        return
    end
    if self.groupWeightList[self.currWordSelect].GroupId ~= groupId then
        return
    end
    self:RefreshWord()
end

function P_EquipmentProbability:OnRefresh_WBP_ComList(r, index, selected)
    r:UpdateSelect(selected)
    local classId = self.classDataList[index].classId
    local classData = Game.TableData.GetEquipmentWordRandomClassDataRow(classId)
    r.View.Text_Name:SetText(classData.Des)
end

function P_EquipmentProbability:OnClick_WBP_ComList(r, index)
    if index ~= self.currTabSelect then
        self.currTabSelect = index
        self.currWordSelect = nil
        self:RefreshWord(true, true)
        -- self.WordList:CancelSel()
        -- self.WordList:Sel(1)
    end
end

function P_EquipmentProbability:OnRefresh_WordList(r, index, selected)
    local groupId = self.classDataList[self.currTabSelect].groupList[index]
    if not groupId then
        Log.Error("tab: " .. self.currTabSelect)
        Log.Error("group index = " .. index)
        Log.Error("table length = " .. #self.classDataList[self.currTabSelect].groupList)
        Log.Dump(self.classDataList[self.currTabSelect].groupList)
        return
    end
    local groupData = Game.TableData.GetEquipmentWordRandomGroupDataRow(groupId)
    if not groupData then
        Log.Error("GroupId not found" .. groupId)
        return
    end
    local rarityData = Game.TableData.GetEquipmentWordRarityDataRow(groupData.Rarity)
    if rarityData.IsRare > 0 then
        r.View.Img_Rare:SetVisibility(ESlateVisibility.Visible)
        r.View:SetIsRare(true)
    else
        r.View.Img_Rare:SetVisibility(ESlateVisibility.Hidden)
        r.View:SetIsRare(false)
    end
    local weight = self.groupWeightList[index].Weight
    local prob = self.groupWeightList[index].RealProb
    local wordText = string.format(
        "(" .. StringConst.Get("EQUIP_WORD_WEIGHT") .. " %d / " .. StringConst.Get("EQUIP_WORD_PROB") .. " %.1f%%)",
        weight,
        prob * 100)
    r.View.Text_Name:SetText(groupData.Des)
    r.View.Text_Val:SetText(wordText)
    if index % 2 == 0 then
        r.View.Img_Bg:SetVisibility(ESlateVisibility.Visible)
    else
        r.View.Img_Bg:SetVisibility(ESlateVisibility.Hidden)
    end
    local arrow = self.groupWeightList[index].Arrow
    if arrow > 0 then
        r.View.Img_ArrowUp:SetVisibility(ESlateVisibility.Visible)
        r.View.Img_ArrowDown:SetVisibility(ESlateVisibility.Collapsed)
    elseif arrow < 0 then
        r.View.Img_ArrowUp:SetVisibility(ESlateVisibility.Collapsed)
        r.View.Img_ArrowDown:SetVisibility(ESlateVisibility.Visible)
    else
        r.View.Img_ArrowUp:SetVisibility(ESlateVisibility.Collapsed)
        r.View.Img_ArrowDown:SetVisibility(ESlateVisibility.Collapsed)
    end
    r:UpdateSelect(selected)
end

function P_EquipmentProbability:OnClick_WordList(r, index)
    if index ~= self.currWordSelect then
        self.currWordSelect = index
        self:RefreshVizInfo()
    end
end

return P_EquipmentProbability
