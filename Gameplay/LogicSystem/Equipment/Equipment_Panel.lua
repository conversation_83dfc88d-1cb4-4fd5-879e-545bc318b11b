local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIComCurrencyList = kg_require("Framework.KGFramework.KGUI.Component.Tag.UIComCurrencyList")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

local StringConst = require "Data.Config.StringConst.StringConst"
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local EquipmentMainSlotItem = kg_require("Gameplay.LogicSystem.Equipment.EquipmentMainSlotItem")
local ESlateVisibility = import("ESlateVisibility")
local TriggerUtils = kg_require("Shared.Utils.TriggerUtils")

---@class Equipment_Panel : UIPanel
---@field view Equipment_PanelBlueprint
local Equipment_Panel = DefineClass("Equipment_Panel", UIPanel)

Equipment_Panel.eventBindMap = {
    [EEventTypesV2.ON_EQUIP_ENHANCED_ACT] = "OnEnhance",
    [EEventTypesV2.ON_EQUIP_ENHANCED_REF] = "OnEnhance",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Equipment_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Equipment_Panel:InitUIData()
    self.CurrTabIdx = nil	--当前选中页签
    self.CurrSlotIdx = nil	--当前选中槽位
    self.tabCache = {}		--缓存

    self.tabData = {
        [1] = {
            name = StringConst.Get("EQUIP_TAB1"),
            TitleTip = StringConst.Get("EQUIP_TAB1_TIP1"),
            UI = UICellConfig.EquipmentEnhancePage,
            Tips = Enum.ETipsData.EQUIP_FIXED,
            Empty = "EQUIP_TAB1_NONE",
            Module = "MODULE_LOCK_EQUIP_ENHANCE"
        },       -- 强化
        [2] = {
            name = StringConst.Get("EQUIP_TAB3"),
            TitleTip = StringConst.Get("EQUIP_TAB3_TIP1"),
            UI = UICellConfig.EquipmentReformPage,
            Tips = Enum.ETipsData.EQUIPENHANCE_TIPS,
            Empty = "EQUIP_TAB3_NONE",
            Module = "MODULE_LOCK_EQUIP_RANDOM"
        }, -- 重塑
    }
end

--- UI组件初始化，此处为自动生成
function Equipment_Panel:InitUIComponent()
    ---@type UIListView childScript: ItemBoxNew
    self.TileView_EquipmentCom = self:CreateComponent(self.view.TileView_Equipment, UIListView)
    ---@type UIComCurrencyList
    self.WBP_ComCurrencyListCom = self:CreateComponent(self.view.WBP_ComCurrencyList, UIComCurrencyList)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
end

---UI事件在这里注册，此处为自动生成
function Equipment_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBackTitleCom.onTipClickEvent, "on_WBP_ComBackTitleCom_TipClickEvent")
    self:AddUIEvent(self.TileView_EquipmentCom.onItemSelected, "on_TileView_EquipmentCom_ItemSelected")
    self:AddUIEvent(self.TileView_EquipmentCom.onGetEntryLuaClass, "on_WBP_EquipItemList_SubWBP_ComListCom_GetEntryLuaClass")
    self:AddUIEvent(self.TileView_EquipmentCom.onItemClicked, "on_WBP_EquipItemList_SubWBP_ComListCom_ItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Equipment_Panel:InitUIView()
    self.WBP_ComBackTitleCom:Refresh(StringConst.Get("EQUIP_TITLE"), 0)

    self.TabBtnList = {
        [1] = self.view.WBP_Equipment_Tab_Enhance,
        [2] = self.view.WBP_Equipment_Tab_Rebuild,
    }
    self:InitTabItems()
end


--- 此处为自动生成
function Equipment_Panel:on_WBP_ComBackTitleCom_TipClickEvent()
    local tipsId = self.tabCache[self.CurrTabIdx].Tips
    Game.TipsSystem:ShowTips(tipsId, self.WBP_ComBackTitleCom:GetTipsBtnGeometry())
end

---面板打开的时候触发
function Equipment_Panel:OnRefresh(...)
    -- tab 设置
    table.clear(self.tabCache)
    for _,tabInfo in ipairs(self.tabData) do -- todo 去掉测试用的true判断
        if not tabInfo.Module or Game.ModuleLockSystem:CheckModuleUnlockByEnum(tabInfo.Module,false) then
            self.tabCache[#self.tabCache+1] = tabInfo
        end
    end
    self:OnClickTab1()
    self.userWidget:BP_SetEmpty(false)

    self.WBP_ComCurrencyListCom:Refresh( UIPanelConfig.PanelConfig[UIPanelConfig.Equipment_Panel].moneyType)
end

function Equipment_Panel:OnClose()
    self:HideTabPage(self.CurrTabIdx)
    self.CurrTabIdx = nil	--当前选中页签
    self.CurrSlotIdx = nil	--当前选中槽位
    table.clear(self.tabCache)		--缓存
end

--- 此处为自动生成
function Equipment_Panel:on_WBP_ComFrameCom_tipsCb()
    local _, ViewportPosition = SlateBlueprintLibrary.LocalToViewport(
            _G.GetContextObject(),
            self.view.WBP_ComFrame.WBP_ComBtnBack_lua.Btn_Info_lua:GetCachedGeometry(),
            FVector2D(0, 0),
            nil,
            nil
    )
    local tipsId = self.tabCache[self.CurrTabIdx].Tips
    Game.TipsSystem:ShowTips(tipsId, self.view.WBP_ComFrame.WBP_ComBtnBack_lua.Btn_Info_lua:GetCachedGeometry())
end

--------------------------------------- 装备槽位 begin ---------------------------------------
function Equipment_Panel:OnRefresh_EquipSlot(widget, index, bSelect)
    -- 设置装备格&选中态/判断是否可选
    widget:Refresh(index, self.CurrSlotIdx == index, self.CurrTabIdx)

    if self.CurrTabIdx == 1 and Game.EquipmentSystem:IsRecommend(index) then
        -- 强化页签需要推荐角标
        widget:SetRecommend(true)
    else
        -- 非强化页签关闭推荐角标
        widget:SetRecommend(false)
    end
end
function Equipment_Panel:OnClick_EquipSlot(widget,index,bSelect)
    -- 空格子
    local equipInfo = Game.EquipmentSystem:GetEquipBarSlot(index)
    if not equipInfo then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_SELECT_NOEQUIP)
        return
    end
    -- 判断是否可选中
    if not widget:GetCanClick() then
        if widget:GetLockWord() then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.COMMON_CUSTOM_STRING,{{widget:GetLockWord()}})
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_SELECT_NOTSATISFY)
        end
        return
    end
    -- 可点击
    self:SetEquipSlotSelect(index)
end
-- 选中
function Equipment_Panel:SetEquipSlotSelect(index)
    if self.CurrSlotIdx == index then
        return
    end

    local oldWidget = self.TileView_EquipmentCom:GetItemByIndex(self.CurrSlotIdx)
    if oldWidget then
        oldWidget:SetSelect(false)
    else
        Log.Warning("oldWidget nil")
    end
    local widget = self.TileView_EquipmentCom:GetItemByIndex(index)
    if widget then
        widget:SetSelect(true)
    else
        Log.Warning("widget nil")
    end
    self.CurrSlotIdx = index

    -- 更新右侧
    self:ShowTabPage(self.CurrTabIdx)
end

-- 刷新装备列表
---@return
function Equipment_Panel:RefreshEquipList()
    -- 选中第一个可选的槽位并返回
    local canSelectIdx = 0
    for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
        if self:canClickHelper(i) then
            canSelectIdx = i
            break
        end
    end
    if self:canClickHelper(self.CurrSlotIdx) then

    elseif canSelectIdx > 0 then
        self.CurrSlotIdx = canSelectIdx
    else
        self.CurrSlotIdx = nil
    end
    local listData = {}
    for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
        listData[i] = {i, self.CurrSlotIdx == i, self.CurrTabIdx}
    end
    self.TileView_EquipmentCom:Refresh(listData)
    --self.TileView_EquipmentCom:SetData(Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT)
end
function Equipment_Panel:canClickHelper(i)
    local equipInfo = Game.EquipmentSystem:GetEquipBarSlot(i)
    if equipInfo then
        if self.CurrTabIdx == 1 then
            --强化
            local key = Game.EquipmentSystem:GetEnhanceSlotID(i)
            local condID = Game.TableData.GetEquipmentGrowBodyConfigDataRow(key).CompleteCondition
            if condID and condID > 0 then
                return TriggerUtils.CanTriggerCompleted(Game.me, Enum.TriggerModuleType.EquipBodyEnhance, condID, condID)
            else
                return true
            end
        else
            --重塑,涂抹
            local equipData = Game.TableData.GetEquipmentDataRow(equipInfo.itemId)
            if equipData.quality >= Enum.EEquipmentGrowConstData.RANDOM_REQUIREMENT then
                return true
            end
        end
    end
    return false
end
---------------------------------------- 装备槽位 end ----------------------------------------
-- 这里的加载和关闭看看是否还有
-- 四个子页面(tab)的Show/Hide方法,同时负责加载子界面
function Equipment_Panel:HideTabPage(idx)
    local pageName = self.tabCache[idx].UI
    self:InvokeComponent(pageName, self.view.Content_Root, "HideSelfTab")
end
function Equipment_Panel:ShowTabPage(idx)
    self.view.RichText_Tip:SetText(StringConst.Get(self.tabCache[idx].TitleTip))
    local pageName = self.tabCache[idx].UI
    self:InvokeComponent(pageName, self.view.Content_Root, "ShowSelfTab", self.CurrSlotIdx)
end

function Equipment_Panel:OnEnhance(slotIdx,stageIdx)
    --self.EquipSlotGroup:RefreshCell(slotIdx)
    --self.TileView_EquipmentCom:SetData(Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT)
    local listData = {}
    for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
        listData[i] = {i, self.CurrSlotIdx == i, self.CurrTabIdx}
    end
    self.TileView_EquipmentCom:Refresh(listData)
    self:RefreshEnhanceInfo()
end


function Equipment_Panel:RefreshEnhanceInfo()
    local paramCnt
    local maxLevel = Enum.EEquipmentGrowConstData.ENHANCE_PROPSUITE_MAXLEVEL
    local currSuitLevel = Game.EquipmentSystem:GetCurMinEnhanceSuitLevel()

    if currSuitLevel < maxLevel then
        local nextData = Game.EquipmentSystem:GetSuitLevelDataRow(currSuitLevel + 1)
        local nextCnt = 0
        for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
            local slotStage = 0
            if Game.EquipmentSystem:GetBarEquip(i) then
                for j = 1, Enum.EEquipmentGrowConstData.ENHANCE_STAGE_MAX do
                    if Game.EquipmentSystem.model.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].level <= 0 then
                        slotStage = j - 1
                        break
                    end
                    slotStage = j
                end
            end
            if slotStage >= nextData.RequireLevel and Game.EquipmentSystem.model.equipmentSlotInfo.slots[i] then
                nextCnt = nextCnt + 1
            end
        end
        paramCnt = nextCnt
    else
        paramCnt = Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT
    end
    if Game.EquipmentSystem:bIsEquipBarLack() then
        currSuitLevel = 0
    end
    
    --self:ShowEquipStrengthenLevel()
end

function Equipment_Panel:ShowEquipStrengthenLevel()
    local lvWidget = self.view.WBP_EquipStrengthenLevel_Item
    --if currSuitLevel == 0 then
    --	lvWidget.Text_Level:SetText(StringConst.Get("EQUIP_TAB3_SUITE_NONE"))
    --else
    --	lvWidget.Text_Level:SetText(currSuitLevel)
    --end
    lvWidget.Text_Level:SetText(currSuitLevel)
    lvWidget.RichText_Num:SetText(string.format("(%s/%s)",paramCnt,Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT))
    lvWidget:Event_UI_State(0)

    if currSuitLevel == maxLevel then
        lvWidget.RichText_Num:SetVisibility(ESlateVisibility.Collapsed)
        lvWidget.Text_Title:SetText(StringConst.Get("EQUIP_TAB1_FULL"))
    else
        lvWidget.RichText_Num:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        lvWidget.Text_Title:SetText(StringConst.Get("EQUIP_TAB1_SUITE"))
    end
end


--- 此处为自动生成
function Equipment_Panel:on_WBP_ComFrameCom_TipClickEvent()
    local tipsId = self.tabCache[self.CurrTabIdx].Tips
    Game.TipsSystem:ShowTips(tipsId, self.WBP_ComBackTitleCom:GetTipsBtnGeometry())
end



---@param index number
---@param data table
function Equipment_Panel:on_Tab_ItemSelected(index, data)
    if self.CurrTabIdx == index then
        return
    end

    if index == 3 then
        -- 先屏蔽重塑界面，迭代新框架到一半，但是界面要重新做了，后续打开
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.COMMON_CUSTOM_STRING,{{"系统迭代中，暂时不可用"}}) -- luacheck: ignore
        return
    end

    -- 清理老tab状态
    if self.CurrTabIdx and self.CurrTabIdx>0 then
        self:HideTabPage(self.CurrTabIdx)
    end

    self.CurrTabIdx = index
    -- 新tab & 装备列表
    self:RefreshEquipList()
    if self.CurrSlotIdx and self.CurrSlotIdx > 0 then
        self:ShowTabPage(self.CurrTabIdx)
        self.userWidget:BP_SetEmpty(false)
    else
        self.userWidget:BP_SetEmpty(true)
    end

    if self.tabCache[self.CurrTabIdx].UI == "EquipStrengthenPage" then
        self:RefreshEnhanceInfo()
    end
end

--- 此处为自动生成
function Equipment_Panel:on_WBP_EquipStrengthenLevel_ItemBtn_Click_Clicked()
    SlateBlueprintLibrary.LocalToViewport(_G.GetContextObject(),
            self.view.WBP_EquipStrengthenLevel_Item.Btn_Click:GetCachedGeometry(), FVector2D(0, 0),	nil, nil)
    local paramCnt, currData, nextData
    local currSuitLevel = Game.EquipmentSystem:GetCurMinEnhanceSuitLevel()
    currData = Game.EquipmentSystem:GetSuitLevelDataRow(currSuitLevel) or {}
    local maxLevel = Enum.EEquipmentGrowConstData.ENHANCE_PROPSUITE_MAXLEVEL
    if currSuitLevel < maxLevel then
        nextData = Game.EquipmentSystem:GetSuitLevelDataRow(currSuitLevel + 1)
        local nextCnt = 0
        for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
            local slotStage = 0
            for j = 1, Enum.EEquipmentGrowConstData.ENHANCE_STAGE_MAX do
                if Game.EquipmentSystem.model.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].level <= 0 then
                    slotStage = j - 1
                    break
                end
                slotStage = j
            end
            if slotStage >= nextData.RequireLevel then
                nextCnt = nextCnt + 1
            end
        end
        paramCnt = nextCnt
    else
        paramCnt = Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT
    end
    if Game.EquipmentSystem:bIsEquipBarLack() then
        currSuitLevel = 0
        paramCnt = Game.EquipmentSystem:GetNumInEquip()
    end
    local geometry = self.view.WBP_EquipStrengthenLevel_Item.Btn_Click:GetCachedGeometry()
    if currSuitLevel == 0 then
        -- 未强化
        Game.TipsSystem:ShowTips(Enum.ETipsData.EQUIP_ENHANCESUITE_NONE, geometry, {
            ExtraContent = {
                {},
                {
                    Content = {
                        {
                            tostring(nextData and nextData.RequireLevel or ""),
                            tostring(paramCnt),
                            tostring(Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT),
                            tostring( ""),
                        }
                    }
                }
            }
        }
        )
    elseif currSuitLevel == maxLevel then
        -- 已满级
        Game.TipsSystem:ShowTips(Enum.ETipsData.EQUIP_ENHANCESUITE_FULL, geometry, {
            ExtraContent = {
                {
                    Content = {
                        {
                            tostring( ""),
                        }
                    }
                }
            }
        }
        )
    else
        -- 有上下级
        Game.TipsSystem:ShowTips(Enum.ETipsData.EQUIP_ENHANCESUITE, geometry, {
            ExtraContent = {
                {
                    Content = {
                        {
                            tostring( ""),
                        }
                    }
                },
                {
                    Content = {
                        {
                            tostring(nextData and nextData.RequireLevel or ""),
                            tostring(paramCnt),
                            tostring(Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT),
                            tostring( ""),
                        }
                    }
                }
            }
        }
        )
    end
end


--- 此处为自动生成
---@param index number
---@return UIComponent
function Equipment_Panel:on_WBP_EquipItemList_SubWBP_ComListCom_GetEntryLuaClass(index)
    return EquipmentMainSlotItem
end


--- 此处为自动生成
---@param index number
---@param data table
function Equipment_Panel:on_WBP_EquipItemList_SubWBP_ComListCom_ItemClicked(index, data)
    local widget = self.TileView_EquipmentCom:GetItemByIndex(index)
    -- 空格子
    local equipInfo = Game.EquipmentSystem:GetEquipBarSlot(index)
    if not equipInfo then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_SELECT_NOEQUIP)
        return
    end
    -- 判断是否可选中
    if not widget:GetCanClick() then
        if widget:GetLockWord() then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.COMMON_CUSTOM_STRING,{{widget:GetLockWord()}})
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_SELECT_NOTSATISFY)
        end
        return
    end
    -- 可点击
    self:SetEquipSlotSelect(index)
end

function Equipment_Panel:InitTabItems()
    for i = 1, #self.TabBtnList do
        local tabBtn = self.TabBtnList[i]
        local tabData = self.tabData[i]
        if tabData then
            tabBtn.Text_Name:SetText(tabData.name)
            self:AddUIEvent(tabBtn.Btn_ClickArea.OnClicked , "OnClickTab" .. i)
        end
    end
end

function Equipment_Panel:SelectTabItem(selected)
    for i = 1, #self.TabBtnList do
        local tabBtn = self.TabBtnList[i]
        tabBtn:BP_SetSelected(i == selected)
    end
end

function Equipment_Panel:OnClickTab1()
    self:SelectTabItem(1)
    self:on_Tab_ItemSelected(1)
end

function Equipment_Panel:OnClickTab2()
    self:SelectTabItem(2)
    self:on_Tab_ItemSelected(2)
end

return Equipment_Panel
