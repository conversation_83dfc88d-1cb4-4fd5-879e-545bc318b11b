local ItemRewardNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemRewardNew")
local ItemSmall = kg_require("Gameplay.LogicSystem.Item.ItemSmall")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local ESlateVisibility = import("ESlateVisibility")

---@class EquipmentEnhanceUseItemData
---@field itemId number
---@field isSelect boolean
---@field callback function
---@field matNum number
---@field curMatNum number

---@class EquipmentEnhanceUseItem : UIListItem
---@field view EquipmentEnhanceUseItemBlueprint
local EquipmentEnhanceUseItem = DefineClass("EquipmentEnhanceUseItem", UIListItem)
--local StringConst = kg_require("Data.Config.StringConst.StringConst")

EquipmentEnhanceUseItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EquipmentEnhanceUseItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EquipmentEnhanceUseItem:InitUIData()
	self.cb = nil -- onclick callback
	self.state = nil -- state
	self.isSelect = nil -- isSelect
	self.timerID = nil -- timer
end

--- UI组件初始化，此处为自动生成
function EquipmentEnhanceUseItem:InitUIComponent()
    ---@type ItemRewardNew
    self.WBP_ItemRewardCom = self:CreateComponent(self.view.WBP_ItemReward, ItemRewardNew)
    ---@type ItemSmall
    self.WBP_ItemSmallCom = self:CreateComponent(self.view.WBP_ItemSmall, ItemSmall)
end

---UI事件在这里注册，此处为自动生成
function EquipmentEnhanceUseItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EquipmentEnhanceUseItem:InitUIView()
end

---@param data EquipmentEnhanceUseItemData
function EquipmentEnhanceUseItem:OnRefresh(data)
	
	if data.itemId == nil then
		self.itemId = nil
		self.view.WBP_ItemReward:SetVisibility(ESlateVisibility.Hidden)
		self.userWidget:BP_SetState(true, false)
		return
	end
	self.itemId = data.itemId
	self.isSelect = data.isSelect
	self:SetSelect(data.isSelect)

	--UI.ShowUI('BagItemTips_Panel', slotInfo.itemId, params)
	self.view.WBP_ItemReward:SetVisibility(ESlateVisibility.Visible)
	self.WBP_ItemRewardCom:FillItem(data.itemId, Enum.CommonItemClickType.OverrideTip, false,string.format("%d/%d", data.curMatNum, data.matNum))

	self.cb = data.callback
end

function EquipmentEnhanceUseItem:SetSelect(isSelect)
	self.isSelect = isSelect
	self.userWidget:BP_SetState(false, isSelect)
end

--- 此处为自动生成
function EquipmentEnhanceUseItem:on_Btn_ClickArea_Clicked()
	if self.itemId == nil then
		return
	end
	if self.isSelect == true then
		return 
	end
	self:SetSelect(not self.isSelect)
	self:GetParent():GetParent():SelectMat(self.index, self.isSelect)
end

return EquipmentEnhanceUseItem
