local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local EquipmentUtil = kg_require("Gameplay.LogicSystem.Equipment.EquipmentUtil")

---@class Equipment_EnhanceDetails_Panel : UIPanel
---@field view Equipment_EnhanceDetails_PanelBlueprint
local Equipment_EnhanceDetails_Panel = DefineClass("Equipment_EnhanceDetails_Panel", UIPanel)

Equipment_EnhanceDetails_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Equipment_EnhanceDetails_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Equipment_EnhanceDetails_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Equipment_EnhanceDetails_Panel:InitUIComponent()

    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function Equipment_EnhanceDetails_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Equipment_EnhanceDetails_Panel:InitUIView()
end


--- 此处为自动生成
function Equipment_EnhanceDetails_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
    self:CloseSelf()
end


--- 此处为自动生成
function Equipment_EnhanceDetails_Panel:OnRefresh()

    local minLv = Game.EquipmentSystem:GetCurMinEnhanceLevel()
    self.view.Text_Total:SetText(minLv)

    self.score = 0
    
    EquipmentUtil.SetPerfectLabel(self.view.WBP_Equipment_Enhance_Tag)
 
    self:ShowTable0()
    self:ShowTable1()
    
    local scoreTitle = StringConst.Get("EQUIP_SCORE")
    self.view.Text_Score:SetText(string.format("%s: +%d", scoreTitle,   self.score ))
end

function Equipment_EnhanceDetails_Panel:ShowTable0()
    local currSuitLevel = Game.EquipmentSystem:GetCurMinEnhanceSuitLevel()
    local paramCnt, currData, nextData
    currData = Game.EquipmentSystem:GetSuitLevelDataRow(currSuitLevel)
    
    local maxLevel = Enum.EEquipmentGrowConstData.ENHANCE_PROPSUITE_MAXLEVEL
    if currSuitLevel < maxLevel then
        nextData = Game.EquipmentSystem:GetSuitLevelDataRow(currSuitLevel + 1)
        local nextCnt = 0
        for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
            local slotStage = 0
            for j = 1, Enum.EEquipmentGrowConstData.ENHANCE_STAGE_MAX do
                if Game.EquipmentSystem.model.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].level <= 0 then
                    slotStage = j - 1
                    break
                end
                slotStage = j
            end
            if slotStage >= nextData.RequireLevel then
                nextCnt = nextCnt + 1
            end
        end
        paramCnt = nextCnt
    else
        paramCnt = Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT
    end
    if Game.EquipmentSystem:bIsEquipBarLack() then
        currSuitLevel = 0
        paramCnt = Game.EquipmentSystem:GetNumInEquip()
    end
    local reqStr = StringConst.Get("EQUIP_SUIT_REQUIRE_LV") -- "<RedStage>%s</>/%s件装备强化至%s阶"
    local condition = string.format(reqStr, paramCnt, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT, nextData.RequireLevel)
    self:ShowTable(self.view.WBP_Equipment_EnchanceDetailsData, currSuitLevel, maxLevel, condition, currData, nextData)
end

function Equipment_EnhanceDetails_Panel:ShowTable1()
    local currSuitLevel = Game.EquipmentSystem:GetCurEnhancePerfectSuitLevel()
    local paramCnt, currData, nextData
    currData = Game.EquipmentSystem:GetPerfectSuitLevelDataRow(currSuitLevel)
    
    local maxLevel = 3
    local condition = ""
    if currSuitLevel < maxLevel then
        paramCnt = 0
        nextData = Game.EquipmentSystem:GetPerfectSuitLevelDataRow(currSuitLevel + 1)
        local requirePercent = nextData.RequirePercentage
        local requirePromote = nextData.RequirePromote
        local enhanceInfo = Game.EquipmentSystem.model.equipmentBodyInfo.enhanceInfo
        for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
            local count = 0
            for j = 1, Enum.EEquipmentGrowConstData.ENHANCE_STAGE_MAX do
                if enhanceInfo.slots[i].stages[j].percent >= requirePercent then
                    count = count + 1
                    if count >= requirePromote then
                        paramCnt = paramCnt + 1
                        break
                    end
                end
            end
        end
        local condStr = StringConst.Get("EQUIP_SUIT_REQUIRE_PERFECT")
        -- "<RedStage>%s</>/%s件装备精炼至%d阶%d%%完美"
        condition = string.format(condStr, 
                paramCnt, 
                Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT,
                requirePromote,
                requirePercent)
    else
        condition = "已经最高级"
    end
    if Game.EquipmentSystem:bIsEquipBarLack() then
        currSuitLevel = 0
        paramCnt = Game.EquipmentSystem:GetNumInEquip()
    end

    self:ShowTable(self.view.WBP_Equipment_EnchanceDetailsData_1, currSuitLevel, maxLevel, condition, currData, nextData)
end

function Equipment_EnhanceDetails_Panel:ShowTable(tableWidget, currSuitLevel, maxLevel, condition, currData, nextData)
    local titleStr = StringConst.Get("EQUIP_SUIT_MAX_LV")
    -- "总等阶：<YellowStage>%d</>/%d"
    local title = string.format(titleStr, currSuitLevel, maxLevel)
    tableWidget.Text_Title:SetText(title)
    tableWidget.Text_Condition:SetText(condition)

    --- 先拿到所有的属性
    local allProp = {}
    if currData then
        for prop, _ in ksbcpairs(currData.SuitProp) do
            allProp[prop] = 0
        end

        self.score = self.score + currData.Mark
    end
    if nextData then
        for prop, _ in ksbcpairs(nextData.SuitProp) do
            allProp[prop] = 0
        end
    end
    
    local listData = {}
    local index = 1
    for prop, _ in pairs(allProp) do
        local propValue =  0
        if currData then
            propValue = currData.SuitProp[prop] or 0
        end
        local nextValue = nil
        if nextData then
            nextValue = nextData.SuitProp[prop] or 0
        end

        local propId = Enum.EFightPropModeData[prop]
        local PropModeData = Game.TableData.GetFightPropModeDataRow(propId)
        listData[index] = {
            prop = PropModeData.PropName,
            value = propValue,
            nextValue = nextValue,
        }
        index = index + 1
    end

    ---@type UIListView childScript: Equipment_EnhanceDetails_Item
    local listView = self:CreateComponent(tableWidget.KGListView, UIListView)
    listView:Refresh(listData)
    if currSuitLevel == maxLevel then
        tableWidget:BP_SetHighest(true)
    else
        tableWidget:BP_SetHighest(false)
    end
end

return Equipment_EnhanceDetails_Panel
