local P_EquipmentProbWordItem = DefineClass("P_EquipmentProbWordItem", UIComponent)
local ESlateVisibility = import("ESlateVisibility")

function P_EquipmentProbWordItem:OnCreate()
    self.isSelect = nil -- isSelect
end

function P_EquipmentProbWordItem:OnClose()
    UIBase.OnClose(self)
    self.isSelect = nil
end

function P_EquipmentProbWordItem:UpdateSelect(isSelect)
    if isSelect ~= self.isSelect then
        if isSelect then
            -- self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_On, 0.0,1,import("EUMGSequencePlayMode").Forward, 1, false)
            self.View.Img_Select:SetVisibility(ESlateVisibility.Visible)
        else
            -- self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Off, 0.0,1,import("EUMGSequencePlayMode").Forward, 1, false)
            self.View.Img_Select:SetVisibility(ESlateVisibility.Collapsed)
        end
        self.isSelect = isSelect
    end
end

return P_EquipmentProbWordItem
