local SceneDisplayWrapper = kg_require("Gameplay.LogicSystem.SceneDisplay.SceneDisplayWrapper")
local SceneDisplayCustomWrapper = kg_require("Gameplay.LogicSystem.SceneDisplay.SceneDisplayCustomWrapper")
---@class SceneDisplayManager
local SceneDisplayManager = DefineClass("SceneDisplayManager")

function SceneDisplayManager:ctor()
end

function SceneDisplayManager:dtor()
end

function SceneDisplayManager:Init()
	---@type table<number, SceneDisplayWrapper> 当前存在的场景
	self.CurrentDisplayingScene = {}
	---当前显示场景
	self.CurShowSceneID = nil
	---场景位置偏移
	self.SceneOffset = {200000, 200000, -100000}
	---显隐场景效果
	self.SceneEffectVisible = true
	---加载任务
	self.SceneLoadTaskList = {}

	Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_LEVEL_LOAD_START, "OnLevelLoadStart", self)
end

function SceneDisplayManager:PreUnInit()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
	self:RemoveAllScene()
end

function SceneDisplayManager:UnInit()
end

function SceneDisplayManager:OnLevelLoadStart()
	self:RemoveAllScene()
end

--region 场景相关
---@public method 创建场景（UIManager预加载资源）
---@param sceneConfigID string 场景配置表ID
---@param preloadRes userdata 预加载的场景蓝图资源
function SceneDisplayManager:CreateSceneByPreloadRes(sceneConfigID, preloadRes)
	Log.DebugFormat("SceneDisplayManager: CreateSceneByPreloadRes  sceneConfigID:%s", sceneConfigID)
	
	local sceneWrapper = self:GetSceneWrapperByConfigID(sceneConfigID)
	if sceneWrapper then
		return sceneWrapper
	end

	return self:CreateSceneDisplayWrapper(sceneConfigID, preloadRes)
end

---@public method 创建场景（异步加载）
---@param sceneConfigID string 场景配置表key
function SceneDisplayManager:CreateSceneAsync(sceneConfigID)
	Log.DebugFormat("SceneDisplayManager: CreateSceneByPreloadRes  sceneConfigID:%s", sceneConfigID)
	
	local sceneWrapper = self:GetSceneWrapperByConfigID(sceneConfigID)
	if sceneWrapper then
		return sceneWrapper
	end
	
	local sceneConfig = Game.TableData.GetSceneDisplayDataRow(sceneConfigID)
	if not sceneConfig then
		Log.ErrorFormat("SceneDisplayManager: sceneConfig=nil, id = %s", sceneConfigID)
		return
	end
	
	return self:CreateSceneDisplayWrapper(sceneConfigID)
end

---@private method 创建SceneDisplayWrapper
function SceneDisplayManager:CreateSceneDisplayWrapper(sceneConfigID, preloadRes)
	local sceneConfig = Game.TableData.GetSceneDisplayDataRow(sceneConfigID)
	local scenePosition = {}
	if sceneConfig.SceneOffset then
		scenePosition[1] = sceneConfig.SceneOffset[1]
		scenePosition[2] = sceneConfig.SceneOffset[2]
		scenePosition[3] = sceneConfig.SceneOffset[3]
	else
		self.SceneOffset[1] = self.SceneOffset[1] + 10000
		scenePosition[1] = self.SceneOffset[1]
		scenePosition[2] = self.SceneOffset[2]
		scenePosition[3] = self.SceneOffset[3]
	end

	local sceneDisplayWrapper
	if sceneConfigID == Enum.ESceneDisplayEnum.SceneCustomSingle then
		sceneDisplayWrapper = SceneDisplayCustomWrapper.new(sceneConfigID, scenePosition, preloadRes)
	else
		sceneDisplayWrapper = SceneDisplayWrapper.new(sceneConfigID, scenePosition, preloadRes)
	end
	sceneDisplayWrapper:CreateSceneEntity(preloadRes)
	self.CurrentDisplayingScene[sceneDisplayWrapper:GetSceneID()] = sceneDisplayWrapper

	return sceneDisplayWrapper
end

---@public method 显示场景
function SceneDisplayManager:ShowScene(sceneID, useBlackMask, ...)
	if self.CurShowSceneID == sceneID then
		return
	end
	
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	sceneWrapper:SetVisible(true)
	
	--隐藏当前显示场景
	if self.CurShowSceneID then
		local curShowSceneWrapper = self:GetSceneWrapperBySceneID(self.CurShowSceneID)
		curShowSceneWrapper:SetVisible(false)
		sceneWrapper.lastShowSceneID = self.CurShowSceneID
	end

	self.CurShowSceneID = sceneID
	self:SetSceneEffectVisible(false)
	self:ShowSceneCamera(sceneID, useBlackMask, ...)
end

---@public method 移除单个场景
function SceneDisplayManager:RemoveScene(sceneID, withoutMoveCamera)
	if not self.CurrentDisplayingScene then
		return
	end

	local sceneWrapper = self.CurrentDisplayingScene[sceneID]
	if not sceneWrapper then
		return
	end

	if self.CurShowSceneID == sceneID then
		self.CurShowSceneID = nil
		self:SetSceneEffectVisible(true)
	end

	if not withoutMoveCamera then
		self:RemoveSceneCamera(sceneID)
	end

	local lastSceneID = sceneWrapper.lastShowSceneID
	sceneWrapper:delete()
	self.CurrentDisplayingScene[sceneID] = nil

	if not lastSceneID then
		for id, wrapper in pairs(self.CurrentDisplayingScene) do
			if wrapper:GetSceneEntity() then
				lastSceneID = id
				break
			end
		end
	end
	
	if lastSceneID then
		self:ShowScene(lastSceneID)
	end
end

---@public method 移除全部场景
function SceneDisplayManager:RemoveAllScene()
	if self.CurrentDisplayingScene then
		for sceneID, _ in pairs(self.CurrentDisplayingScene) do
			self:RemoveScene(sceneID)
		end
		table.clear(self.CurrentDisplayingScene)
	end
	
	if self.SceneLoadTaskList then
		for loadID, v in pairs(self.SceneLoadTaskList) do
			Game.AssetManager:CancelLoadAsset(loadID)
		end
		table.clear(self.SceneLoadTaskList)
	end
end

---@public method 场景是否存在
function SceneDisplayManager:SceneExists(sceneID)
	local sceneWrapper = self.CurrentDisplayingScene[sceneID]
	return sceneWrapper ~= nil
end

---@public method 获取SceneWrapper(sceneID)
function SceneDisplayManager:GetSceneWrapperBySceneID(sceneID)
	return self.CurrentDisplayingScene[sceneID]
end

---@public method 获取SceneWrapper(configID)
function SceneDisplayManager:GetSceneWrapperByConfigID(sceneConfigID)
	for sceneID, sceneWrapper in pairs(self.CurrentDisplayingScene) do
		if sceneWrapper:GetConfigID() == sceneConfigID then
			return sceneWrapper
		end
	end
end

---@public method 获取场景entity(sceneID)
function SceneDisplayManager:GetSceneEntityByID(sceneID)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	return sceneWrapper and sceneWrapper:GetSceneEntity()
end

---@public method 获取场景entity(configID)
function SceneDisplayManager:GetSceneEntityByConfigID(sceneConfigID)
	for sceneID, sceneWrapper in pairs(self.CurrentDisplayingScene) do
		if sceneWrapper:GetConfigID() == sceneConfigID then
			return sceneWrapper:GetSceneEntity()
		end
	end
end
--endregion

--region 场景角色相关
---@public method 生成本地展示玩家（无自定义外观数据，使用职业性别对应的默认外观数据）
function SceneDisplayManager:CreateDisplayAvatarEntityByProfessionSex(sceneID, tagName, profession, sex)
	local sceneEntity = self:GetSceneWrapperBySceneID(sceneID)
	return sceneEntity:CreateDisplayAvatarEntityByProfessionSex(tagName, profession, sex)
end

---@public method 生成本地展示玩家（无自定义外观数据，使用FacadeID对应外观数据）
function SceneDisplayManager:CreateDisplayAvatarEntityByFacadeID(sceneID, tagName, facadeControlID)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	return sceneWrapper:CreateDisplayAvatarEntityByFacadeID(tagName, facadeControlID)
end

---@public method 生成本地展示玩家（有服务器自定义外观数据）
---@param appearanceData AVATAR_SHAPE_INFO
function SceneDisplayManager:CreateDisplayAvatarEntityByCustomAppearanceData(sceneID, tagName, appearanceData)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	return sceneWrapper:CreateDisplayAvatarEntityByCustomAppearanceData(tagName, appearanceData)
end

---@public method 生成本地展示玩家（从本地已有的entity中拷贝外观数据）
function SceneDisplayManager:CreateDisplayAvatarEntityByAvatarUID(sceneID, tagName, avatarEntityUID)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	return sceneWrapper:CreateDisplayAvatarEntityByAvatarUID(tagName, avatarEntityUID)
end

---@public method 生成本地展示怪物（使用FacadeID配置的外观数据）
function SceneDisplayManager:CreateDisplayMonsterEntityByFacadeID(sceneID, tagName, facadeControlID)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	return sceneWrapper:CreateDisplayMonsterEntityByFacadeID(tagName, facadeControlID)
end

---@public method 生成本地展示Entity
---@param props table {OffsetLocation: 相对Tag点的位置偏移}
function SceneDisplayManager:CreateDisplayEntity(sceneID, tagName, entityName, props)
	local sceneEntity = self:GetSceneWrapperBySceneID(sceneID)
	return sceneEntity:CreateDisplayEntity(tagName, entityName, props)
end

---@public method 获取展示中的展示角色
function SceneDisplayManager:GetDisplayEntityByUID(sceneID, uid)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	if not sceneWrapper then
		return
	end

	return sceneWrapper:GetDisplayEntityByUID(uid)
end

---@public method 获取展示entity
function SceneDisplayManager:GetDisplayEntityByTag(sceneID, tagName)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	if not sceneWrapper then
		return
	end

	return sceneWrapper:GetDisplayEntityByTag(tagName)
end

---@public method 删除展示entity
function SceneDisplayManager:RemoveDisplayEntityByUID(sceneID, uid)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	if not sceneWrapper then
		return
	end

	sceneWrapper:RemoveDisplayEntityByUID(uid)
end

---@public method 删除展示entity
function SceneDisplayManager:RemoveDisplayEntityByTag(sceneID, tagName)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	if not sceneWrapper then
		return
	end

	sceneWrapper:RemoveDisplayEntityByTag(tagName)
end

---@public method 删除展示entity
function SceneDisplayManager:RemoveDisplayEntityAll(sceneID)
	local sceneWrapper = self:GetSceneWrapperBySceneID(sceneID)
	if not sceneWrapper then
		return
	end

	sceneWrapper:RemoveAllDisplayEntity()
end
--endregion

--region 场景组件相关

---@public method 获取组件
function SceneDisplayManager:GetComponentIDByTag(sceneID, componentTagName)
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	if not sceneEntity then
		return nil
	end

	return sceneEntity:GetComponentIDByTag(componentTagName)
end

---@public method 获取组件位置
function SceneDisplayManager:GetComponentPos(sceneID, componentTagName)
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	if not sceneEntity then
		return nil
	end

	return sceneEntity:GetComponentTagLocation(componentTagName)
end

---@public method 获取组件位置、旋转、缩放
function SceneDisplayManager:GetComponentTagLocationAndRotationAndScale(sceneID, componentTagName)
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	if not sceneEntity then
		return nil
	end

	return sceneEntity:GetComponentTagLocationAndRotationAndScale(componentTagName)
end

---@public method 获取组件位置
function SceneDisplayManager:GetComponentLocationByComponentID(sceneID, componentID)
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	if not sceneEntity then
		return nil
	end
	return sceneEntity:GetComponentLocationByComponentID(componentID)
end

---@public method 设置组件世界坐标
function SceneDisplayManager:SetComponentWorldLocationByID(sceneID, componentID, x, y, z)
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	if not sceneEntity then
		return nil
	end
	return sceneEntity:SetComponentWorldLocationByID(componentID, x, y, z)
end

---@public method 设置组件世界坐标
function SceneDisplayManager:SetComponentTagWorldLocation(sceneID, componentTagName, x, y, z)
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	if not sceneEntity then
		return nil
	end

	return sceneEntity:SetComponentTagWorldLocation(componentTagName, x, y, z)
end

--endregion


--region 相机相关

---@private method 显示场景相机
function SceneDisplayManager:ShowSceneCamera(sceneID, useBlackMask, ...)
	if useBlackMask then
		UI.ShowUI("P_BlackMask", true)
	end

	if not Game.CameraManager then
		return
	end

	local sceneEntity = self:GetSceneEntityByID(sceneID)
	local configID = sceneEntity:GetConfigID()
	local cameraActorID = sceneEntity:GetCameraActorID()
	local sceneConfig = Game.TableData.GetSceneDisplayDataRow(configID)
	if sceneConfig.CameraMode == Enum.ECameraModes.UIView then
		Game.CameraManager:EnableUIViewCamera(true, cameraActorID, sceneConfig.UIViewCameraParam)
	else
		Game.CameraManager:EnterCameraMode(sceneConfig.CameraMode, ...)
	end
end


---@private method 显示场景相机
function SceneDisplayManager:RefreshSceneCamera_ViewConfigID(sceneID, configID)
	if not Game.CameraManager then
		return
	end

	local sceneEntity = self:GetSceneEntityByID(sceneID)
	local cameraActorID = sceneEntity:GetCameraActorID()
	Game.CameraManager:EnableUIViewCamera(true, cameraActorID, configID)
end

---@private method 移除场景相机
function SceneDisplayManager:RemoveSceneCamera(sceneID)
	if not Game.CameraManager then
		return
	end
	
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	local configID = sceneEntity:GetConfigID()
	local sceneConfig = Game.TableData.GetSceneDisplayDataRow(configID)
	if sceneConfig.CameraMode == Enum.ECameraModes.UIView then
		Game.CameraManager:EnableUIViewCamera(false)
	else
		Game.CameraManager:ExitCameraMode(sceneConfig.CameraMode)
	end

	--目前固定回到第三人称
	Game.CameraManager:EnableThirdCamera(true, {
		UsePawnControlRotation = true,
		RemainState = true,
		bCameraCut = true
	})
end

---@public method 刷新场景内相机
function SceneDisplayManager:RefreshCameraByTag(sceneID, cameraTag, blendTime)
	local sceneEntity = self:GetSceneEntityByID(sceneID)
	local cameraActorID = sceneEntity and sceneEntity:GetCameraActorID(cameraTag)
	if not cameraActorID then
		return KG_INVALID_ID
	end

	local sceneConfig = Game.TableData.GetSceneDisplayDataRow(sceneEntity:GetConfigID())
	Game.CameraManager:EnableUIViewCamera(
		true, 
		cameraActorID,
		sceneConfig and sceneConfig.UIViewCameraParam, 
		blendTime
	)

	return cameraActorID
end
--endregion


--region 场景效果控制
---@private method 设置场景内各种效果开关
function SceneDisplayManager:SetSceneEffectVisible(visible)
	if self.SceneEffectVisible == visible then
		return
	end

	self.SceneEffectVisible = visible

	--在世界，被cutscene还原了
	Game.CharacterLightManager:EnableCharacterLight(visible) -- 角色光(强制关闭补光功能)
	Game.WorldManager:EnableMainSkyPerformer(visible)
	Game.WorldManager:EnableDirectionalLight(visible)
	
	--在5200064世界没有生效
	Game.WorldManager:EnableFogs(visible)
end
--endregion

return Class(nil, nil, SceneDisplayManager)
