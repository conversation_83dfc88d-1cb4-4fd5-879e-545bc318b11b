local LuaMulticastDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaMulticastDelegate")
local EntityUtils = kg_require("Gameplay.NetEntities.EntityUtils")
local ULLFunc = import("LowLevelFunctions")
local EAttachmentRule = import("EAttachmentRule")

---@class SceneDisplayWrapper : SceneDisplayWrapper
local SceneDisplayWrapper = DefineClass("SceneDisplayWrapper")

function SceneDisplayWrapper:ctor(sceneConfigID, scenePosition, preloadRes)
	---@public 场景actor加载完成回调
	self.OnSceneActorReady = LuaMulticastDelegate.new()
	
	self.sceneID = ULLFunc.GetGlobalUniqueID()
	self.sceneConfigID = sceneConfigID
	self.scenePosition = scenePosition
	---@type LocalSceneEntity
	self.sceneEntity = nil

	self.displayEntityList = {}
	
	self.lastShowSceneID = nil

	self.newSceneLoadTask = nil
end

function SceneDisplayWrapper:dtor()
	if self.sceneEntity then
		self.sceneEntity:destroy()
	end
	self.sceneEntity = nil

	if self.sceneLoadID then
		Game.AssetManager:RemoveAssetReferenceByLoadID(self.sceneLoadID)
	end
	self.sceneLoadID = nil

	if self.newSceneLoadTask then
		Game.AssetManager:RemoveAssetReferenceByLoadID(self.newSceneLoadTask)
	end
	self.newSceneLoadTask = nil

	self.OnSceneActorReady:Clear()
	self.OnSceneActorReady = nil
	
	self:RemoveAllDisplayEntity()
end

--region 场景相关

---@public method
function SceneDisplayWrapper:GetSceneEntity()
	return self.sceneEntity
end

---@public method
function SceneDisplayWrapper:GetSceneID()
	return self.sceneID
end

---@public method
function SceneDisplayWrapper:GetConfigID()
	return self.sceneConfigID
end

---@public method 显隐
function SceneDisplayWrapper:SetVisible(visible)
	self.sceneEntity:SetVisible(visible)
	for i, entity in pairs(self.displayEntityList) do
		if entity.bInWorld then
			entity:SetActorHiddenInGame(not visible)
		end
	end
end

--endregion


--region 角色相关
---@public method 生成本地展示玩家（无自定义外观数据，使用职业性别对应的默认外观数据）
function SceneDisplayWrapper:CreateDisplayAvatarEntityByProfessionSex(tagName, profession, sex)
	local location, rotator = self.sceneEntity:GetComponentTagLocationAndRotationAndScale(tagName)
	local displayEntity = EntityUtils.CreateLocalAvatarEntityByProfessionSex("LocalDisplayChar", {
		Position                           = location,
		Rotation                           = rotator,
		isAvatar                           = true,
	}, profession, sex)

	displayEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
	displayEntity.SceneDisplayLocationTag = tagName

	self.displayEntityList[displayEntity:uid()] = displayEntity
	return displayEntity
end

---@public method 生成本地展示玩家（无自定义外观数据，使用FacadeID对应外观数据）
function SceneDisplayWrapper:CreateDisplayAvatarEntityByFacadeID(tagName, facadeID)
	local location, rotator = self.sceneEntity:GetComponentTagLocationAndRotationAndScale(tagName)
	local displayEntity = EntityUtils.CreateLocalAvatarEntityByFacadeID("LocalDisplayChar", {
		Position                           = location,
		Rotation                           = rotator,
		isAvatar                           = true,
	}, facadeID)

	displayEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
	displayEntity.SceneDisplayLocationTag = tagName

	self.displayEntityList[displayEntity:uid()] = displayEntity
	return displayEntity
end

---@public method 生成本地展示玩家（有自定义外观数据）
function SceneDisplayWrapper:CreateDisplayAvatarEntityByCustomAppearanceData(tagName, appearanceData)
	local location, rotator = self.sceneEntity:GetComponentTagLocationAndRotationAndScale(tagName)
	local displayEntity = EntityUtils.CreateLocalAvatarEntityByCustomAppearanceData("LocalDisplayChar", {
		Position                           = location,
		Rotation                           = rotator,
		isAvatar                           = true,
	}, appearanceData)

	displayEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
	displayEntity.SceneDisplayLocationTag = tagName

	self.displayEntityList[displayEntity:uid()] = displayEntity
	return displayEntity
end

---@public method 生成本地展示玩家（从本地已有的entity中拷贝外观数据）
function SceneDisplayWrapper:CreateDisplayAvatarEntityByAvatarUID(tagName, uid)
	local location, rotator = self.sceneEntity:GetComponentTagLocationAndRotationAndScale(tagName)
	local displayEntity = EntityUtils.CreateLocalAvatarEntityByAvatarEntityID("LocalDisplayChar", {
		Position                           = location,
		Rotation                           = rotator,
		isAvatar                           = true,
	}, uid)

	displayEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
	displayEntity.SceneDisplayLocationTag = tagName

	self.displayEntityList[displayEntity:uid()] = displayEntity
	return displayEntity
end

---@public method 生成本地展示怪物（使用FacadeID配置的外观数据）
function SceneDisplayWrapper:CreateDisplayMonsterEntityByFacadeID(tagName, facadeControlID)
	local location, rotator = self.sceneEntity:GetComponentTagLocationAndRotationAndScale(tagName)

	local displayEntity = Game.EntityManager:CreateLocalEntity("LocalDisplayChar",
		{
			Position = location,
			Rotation = rotator,
			isAvatar = false,
			FacadeControlID = facadeControlID,
		})

	displayEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
	displayEntity.SceneDisplayLocationTag = tagName

	self.displayEntityList[displayEntity:uid()] = displayEntity

	return displayEntity
end

---@private method 生成本地展示玩家
--function SceneDisplayWrapper:CreateDisplayAvatarEntity(tagName, ...)
--	local location, rotator = self.sceneEntity:GetComponentTagLocationAndRotationAndScale(tagName)
--	local displayEntity = Game.EntityManager:CreateDisplayAvatarEntityByProfessionSex("LocalDisplayChar", {
--		Position                           = location,
--		Rotation                           = rotator,
--		isAvatar                           = true,
--	}, ...)
--
--	displayEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
--	displayEntity.SceneDisplayLocationTag = tagName
--
--	self.displayEntityList[displayEntity:uid()] = displayEntity
--	return displayEntity
--end

---@public method 生成本地展示Entity
---@param props table {OffsetLocation: 相对Tag点的位置偏移}
function SceneDisplayWrapper:CreateDisplayEntity(tagName, entityName, props)
	local displayEntity = Game.EntityManager:CreateLocalEntity(entityName, props)
	displayEntity.OnActorReady:Add(self, "OnDisplayEntityReady")
	displayEntity.SceneDisplayLocationTag = tagName

	self.displayEntityList[displayEntity:uid()] = displayEntity
	return displayEntity
end

---@private method 展示角色加载完成
function SceneDisplayWrapper:OnDisplayEntityReady(entity)
	self:BindEntityToScene(entity)
end

---@private method
function SceneDisplayWrapper:BindEntityToScene(entity, attackRule)
	local parentComID = entity.SceneDisplayLocationTag and self.sceneEntity:GetComponentIDByTag(entity.SceneDisplayLocationTag)
	attackRule = attackRule or EAttachmentRule.SnapToTarget
	
	if parentComID and parentComID>0 then
		entity.CppEntity:KAPI_Actor_AttachToComponent(parentComID, "", attackRule, attackRule, attackRule, true)
	else
		local rootID = self.sceneEntity.CppEntity:KAPI_Actor_GetRootComponent()
		entity.CppEntity:KAPI_Actor_AttachToComponent(rootID, "", attackRule, attackRule, attackRule, true)
	end

	local offsetLocation = entity.OffsetLocation
	if offsetLocation then
		entity.CppEntity:KAPI_SetRelativeLocation_P(table.unpack(offsetLocation))
	end
end

---@public method 获取所有展示角色(按数组返回)
function SceneDisplayWrapper:GetDisplayEntityAll()
	local list = {}
	for i, entity in pairs(self.displayEntityList) do
		table.insert(list, entity:uid())
	end

	return list
end

---@public method 获取展示角色(uid)
function SceneDisplayWrapper:GetDisplayEntityByUID(uid)
	return self.displayEntityList[uid]
end

---@public method
function SceneDisplayWrapper:GetDisplayEntityRelativeLocationAndRotationByUID(uid)
	local entity = self:GetDisplayEntityByUID(uid)
	if not entity then
		return 0,0,0
	end

	local rootComponentID = entity.CppEntity:KAPI_Actor_GetRootComponent()
	local relativeTransform = entity.CppEntity:KAPI_SceneID_GetRelativeTransform(rootComponentID)
	return relativeTransform:GetLocation(), relativeTransform:Rotator()
end

---@public method
function SceneDisplayWrapper:GetDisplayEntityByTag(tagName)
	for i, entity in pairs(self.displayEntityList) do
		if entity.SceneDisplayLocationTag and entity.SceneDisplayLocationTag == tagName then
			return entity
		end
	end

	return nil
end

---@public method
function SceneDisplayWrapper:RemoveAllDisplayEntity()
	for i, entity in pairs(self.displayEntityList) do
		entity:destroy()
	end

	table.clear(self.displayEntityList)
end

---@public method
function SceneDisplayWrapper:RemoveDisplayEntityByUID(uid)
	local entity = self.displayEntityList[uid]
	if not entity then
		return
	end

	entity:destroy()
	self.displayEntityList[uid] = nil
end

---@public method
function SceneDisplayWrapper:RemoveDisplayEntityByTag(tagName)
	for i, entity in pairs(self.displayEntityList) do
		if entity.SceneDisplayLocationTag and entity.SceneDisplayLocationTag == tagName then
			entity:destroy()
			self.displayEntityList[i] = nil
			break
		end
	end
end


---@public method
function SceneDisplayWrapper:GetDisplayEntityByActorID(actorID)
	for i, entity in pairs(self.displayEntityList) do
		if entity.CharacterID == actorID then
			return entity
		end
	end

	return nil
end

--endregion

--region 创建相关
---@private method 
function SceneDisplayWrapper:CreateSceneEntity(preloadRes)
	if preloadRes then
		self:CreateSceneEntityByPreloadRes(preloadRes)
	else
		self:CreateSceneEntityAsync()
	end
end

---@private method 
function SceneDisplayWrapper:CreateSceneEntityByPreloadRes(preloadRes)
	self.sceneEntity = Game.EntityManager:CreateLocalEntity("LocalSceneEntity", {
		sceneConfigID = self.sceneConfigID,
		Position = self.scenePosition,
		ActorBPClass = preloadRes,
	})
	
	self.OnSceneActorReady:Broadcast(self.sceneID)
end

---@private method 
function SceneDisplayWrapper:CreateSceneEntityAsync()
	local sceneConfig = Game.TableData.GetSceneDisplayDataRow(self.sceneConfigID)
	self.sceneLoadID = Game.AssetManager:AsyncLoadAssetKeepReferenceID(sceneConfig.ScenePath, self, "OnSceneActorLoaded")
end


---@private method 加载完成回调
function SceneDisplayWrapper:OnSceneActorLoaded(loadID, assetID)
	self:CreateSceneEntityByPreloadRes(assetID)
	
	Game.AssetManager:RemoveAssetReferenceByLoadID(self.sceneLoadID)
	self.sceneLoadID = nil
end


---@public method 切换场景
function SceneDisplayWrapper:ChangeSceneEntityBySceneConfigID(sceneConfigID)
	if self.sceneConfigID == sceneConfigID then
		return
	end

	if self.newSceneLoadTask then
		return
	end
	
	local sceneConfig = Game.TableData.GetSceneDisplayDataRow(sceneConfigID)
	local loadID = Game.AssetManager:AsyncLoadAssetKeepReferenceID(sceneConfig.ScenePath, self, "OnChangeSceneActorLoaded")
	self.newSceneLoadTask = {
		loadID = loadID,
		sceneConfigID = sceneConfigID
	}
end

---@private method
function SceneDisplayWrapper:OnChangeSceneActorLoaded(loadID, assetID)
	self.sceneConfigID = self.newSceneLoadTask.sceneConfigID
	if self.sceneEntity then
		self.sceneEntity:destroy()
	end
	
	self.sceneEntity = Game.EntityManager:CreateLocalEntity("LocalSceneEntity", {
		sceneConfigID = self.sceneConfigID,
		Position = self.scenePosition,
		ActorBPClass = assetID,
	})

	Game.AssetManager:RemoveAssetReferenceByLoadID(self.newSceneLoadTask.loadID)
	self.newSceneLoadTask = nil

	self:ChangeAllDisplayEntityParent()
	Game.SceneDisplayManager:ShowSceneCamera(self.sceneID)
end

---@private method
function SceneDisplayWrapper:ChangeAllDisplayEntityParent()
	for uid, entity in pairs(self.displayEntityList) do
		self:BindEntityToScene(entity, EAttachmentRule.KeepWorld)
	end
end
--endregion

return SceneDisplayWrapper
