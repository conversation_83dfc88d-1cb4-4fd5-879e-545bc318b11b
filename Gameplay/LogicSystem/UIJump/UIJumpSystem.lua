---@class UIJumpSystem:SystemBase
local UIJumpSystem = DefineSingletonClass("UIJumpSystem", SystemBase)
local TaskStatusConst = kg_require("Shared.Const.QuestConst").TaskStatusConst

-- 自定义跳转
UIJumpSystem.customJumpFun = {
    ["P_GuildInsideSkill"] = "Jump_GuildInsideSkill",
    ["EquipMain_Panel"] = "Jump_EquipMainPanel",
    ["P_Guild"] = "Jump_Guild",
    ["GuildOutPanel"] = "Jump_Guild",
    ["GuildInside_Panel"] = "Jump_Guild",
    ["TaskBoardPanel"] = "Jump_TaskBoardPanel",
    ["P_QuestBoard"] = "Jump_TaskBoardPanel",
    ["SkillCustomizerPanel"] = "Jump_SkillCustomizerPanel",
    ["P_SequenceReason"] = "Jump_SequenceReason",
	["P_FashionMain"] = "Jump_FashionMain",
	["DungeonEntrance_Panel"] = "Jump_DungeonEntrancePanel",
	["P_DungeonSelect"] = "Jump_DungeonSelect",
	["PVPMain_Panel"] = "Jump_PVPMainPanel",
	["GuildBattleOut_Panel"] = "Jump_GuildBattleOutPanel",
    ["TarotTeam_Panel"] = "Jump_TarotTeamPanel",
	["SkillCustomizerPeculiarityPopUpPanel"] = "Jump_SkillCustomizerPeculiarityPopUpPanel",
	["EleCoreMain_Panel"] = "Jump_EleCoreMainPanel",
	["EleCoreTree_Panel"] = "Jump_EleCoreTreePanel",
	["P_GachaShop"] = "Jump_P_GachaShop"
}

--Init------------------------------------------------------------------------------------------------------------------

function UIJumpSystem:Init()

end

function UIJumpSystem:UnInit()

end

--Logic-----------------------------------------------------------------------------------------------------------------

--Private-----------------------------


--Public-----------------------------

-- 跳转至UI
function UIJumpSystem:JumpToUI(UIId, ui, overwriteParam)
    local Target = Game.TableData.GetUIJumpDataRow(UIId)
    if not Target then
        Log.DebugWarningFormat("UIJump: Unable to find the jump target %d in table data", UIId)
        return
    end
    local UIClass = Target.UIclass
    local UIInfo = UI.GetCfg(UIClass)
	local TargetUI = UIInfo.parentui and UIInfo.parentui or UIClass
	local customFunName = UIJumpSystem.customJumpFun[TargetUI]
	if customFunName then
		--if not para then para = UIJumpSystem.ParamFormatFun[TargetUI] end
		local param = overwriteParam and overwriteParam or (Target.JumpParamValue or {})
		UIJumpSystem[customFunName](UIJumpSystem, param)
		return
	end
    if not self:CheckUIAvailable(UIInfo, Target, true) or not UIInfo then
        return
    end
    if Target.ClosePreUI and ui then
        local preui = UIJumpSystem.getRootUI(ui)
        if preui and preui.__cname ~= UIClass then
            preui:CloseSelf()
        end
    end
    -- if ((not ui) or ui.__cname ~= UIClass) then
    --     if ClosePreUI then
    --         UI.HideGameAndPop()
    --     end
    -- end

	if overwriteParam then
		UI.ShowUI(TargetUI, overwriteParam)
	elseif Target.JumpParamValue then
		UI.ShowUI(TargetUI, table.unpack(Target.JumpParamValue))
	else
		UI.ShowUI(TargetUI)
	end
end

-- 获取跳转ID的UIName
function UIJumpSystem:GetJumpUITarget(UIId)
    local Target = Game.TableData.GetUIJumpDataRow(UIId)
    if not Target then
        Log.DebugWarningFormat("UIJump: Unable to find the jump target %d in table data", UIId)
        return
    end
    return Target
end

-- 检查对应功能是否解锁
function UIJumpSystem:CheckUIAvailable(UIInfo, JumpInfo, bShowReminder)
    if not UIInfo then
        Log.DebugWarning(
            "UIJUmp: Unable to retrieve UI data from UIData; did you forget to config the UI in UI.xlsx?")
        return false
    end
    -- 检查对应模块主界面的解锁和对应模块的解锁
    if (not JumpInfo.UIUnlock or Game.ModuleLockSystem:CheckModuleUnlockByEnum(JumpInfo.UIUnlock, bShowReminder)) and
        (not UIInfo.unlock or Game.ModuleLockSystem:CheckModuleUnlockByEnum(UIInfo.unlock, bShowReminder)) then
        return true
    end
    return false
end

-- 检查对应功能是否解锁
function UIJumpSystem:CheckJumpUnLock(JumpID, bShowReminder)
    local Target = Game.TableData.GetUIJumpDataRow(JumpID)
    if not Target then
        Log.DebugWarningFormat("UIJump: Unable to find the jump target %d in table data", JumpID)
        return false
    end

    local UIClass = Target.UIclass
    --local JumpParamValue = Target.JumpParamValue
    local ClosePreUI = Target.ClosePreUI
    local UIInfo = UI.GetCfg(UIClass)

    if not self:CheckUIAvailable(Game.TableData.GetUIDataRow(UIClass), Target, bShowReminder) or not UIInfo then
        return false
    end

    --TODO: 公会任务临时跳转, 领取任务后有效，待优化
    if UIClass == "P_GuildMission" then
        local taskInfo = Game.QuestSystem:GetQuestInfo(6500006)
        if taskInfo then
            if taskInfo.Status == TaskStatusConst.TASK_STATUS__ACCEPTED then
                return true
            else
                return false
            end
        else
            return false    
        end
    end

    return true
end

function UIJumpSystem.getRootUI(ui)
    if not ui.parentui then
        return ui
    else
        return UIJumpSystem.getRootUI(ui)
    end
end

----------------------------------------跳转自定义接口 Start-------------------------------------------
function UIJumpSystem:Jump_GuildInsideSkill(param)
	Game.GuildSystem:ShowGuildSkill(param)
end

function UIJumpSystem:Jump_EquipMainPanel(param)
	local slot = nil
	local idx = nil
	if param then
		idx, slot = table.unpack(param)
	end
	--Game.EquipmentSystem:ShowEquipmentUI({tabIdx = idx, slotIdx = slot})
	Game.EquipmentSystem:ShowEquipUI({tabIdx = idx, slotIdx = slot})
end

function UIJumpSystem:Jump_Guild(param)
	Game.GuildSystem:ShowGuildMain(param)
end

function UIJumpSystem:Jump_TaskBoardPanel(param)
	if type(param) == "table" or type(param) == "userdata" then
		--UI.ShowUI("P_QuestBoard")
		Game.NewUIManager:OpenPanel("TaskBoardPanel")
	else
		--UI.ShowUI("P_QuestBoard", param)
		Game.NewUIManager:OpenPanel("TaskBoardPanel", param)
	end
end

function UIJumpSystem:Jump_SkillCustomizerPanel(param)
	Game.SkillCustomSystem:ShowSkillCustomizerPanel(param)
end

function UIJumpSystem:Jump_SequenceReason(param)
	if type(param) == "table" then
		param = nil
	end
	-- Game.SequenceSystem:OpenSequenceReason(param)
end

function UIJumpSystem:Jump_FashionMain(param)
	Game.FashionSystem:OpenFashionPanel(param)
end

function UIJumpSystem:Jump_DungeonEntrancePanel()
	Game.DungeonSystem.CreateDungeonUI()
end

function UIJumpSystem:Jump_DungeonSelect(param)
	local finalParam = param
	if type(ksbcunpack(param)) == "table" or type(ksbcunpack(param)) == "userdata" then
		finalParam = ksbcunpack(param)
	end
	Game.NewUIManager:OpenPanel("DungeonSelect_Panel", finalParam)
end

function UIJumpSystem:Jump_PVPMainPanel(param)
	Game.PVPSystem:ShowPVPPanel(param)
end

function UIJumpSystem:Jump_GuildBattleOutPanel()
	Game.GuildLeagueSystem:ShowOutPanel()
end

function UIJumpSystem:Jump_TarotTeamPanel()
	Game.TarotTeamSystem:TryToOpenTarotTeamUI()
end

function UIJumpSystem:Jump_SkillCustomizerPeculiarityPopUpPanel()
	Game.SkillCustomSystem:ShowSkillPeculiarityPopupPanel(0)
end

function UIJumpSystem:Jump_EleCoreMainPanel()
	Game.ElementCoreSystem:ShowElementPanel()
end

function UIJumpSystem:Jump_EleCoreTreePanel()
	Game.ElementCoreSystem:ShowTalentTree()
end

function UIJumpSystem:Jump_P_GachaShop()
	Log:DebugWarning(
		"UIJUmp: P_GachaShop is deprecated!")
	return
end

----------------------------------------跳转自定义接口 End---------------------------------------------

return UIJumpSystem
