local ESlateVisibility = import("ESlateVisibility")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")

local P_KeyPrompt = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_KeyPrompt"
local P_FlameJump= DefineClass("P_FlameJump",WorldWidgetCellBase)

function P_FlameJump:ctor()
    self.Icon = nil
    self.InsID = nil
    self.TargetLocation = nil
    self.bOnEdge = nil
    self.TickTimer = nil
    self.bEnable = nil
    self.bEnableInteract = nil
    self.bMutex = nil

    local ViewportSize = import("WidgetLayoutLibrary").GetViewportSize(_G.GetContextObject())
    self.ViewportScale = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
    ViewportSize = ViewportSize / self.ViewportScale
    self.halfSizeX = ViewportSize.X/2
    self.halfSizeY = ViewportSize.Y/2
    self.tempVector = FVector2D(0, 0)
end


function P_FlameJump:OnEdgeChanged(bOnEdge)
    self.bOnEdge = bOnEdge
    self:checkVisibility()
end


function P_FlameJump:OnUninit()
    Log.Debug(" P_FlameJump:OnUninit()")
    self.name = nil
end

function P_FlameJump:OnInit(Params)
    self:Show(ESlateVisibility.Visible)
    self.P_KeyPrompt = self:CreateComponent(self.view.WBP_KeyPrompt, P_KeyPrompt)
    self.name = nil
end

function P_FlameJump:OnGetFromPool(Params)
    self.InsID = Params.InsID
    self.TargetLocation = Params.TargetLocation
    self.bEnableInteract = Params.bEnableInteract
    self.bEnable = true
    self.bMutex = false


    self.bOnEdge = false

    if import("C7FunctionLibrary").IsC7Editor() then
        if import("UIFunctionLibrary").GetPreviewPlatform
            and import("UIFunctionLibrary").GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC then
            self.bShowHint = true
        end
    else
        if not PlatformUtil.IsMobilePlatform() then
            self.bShowHint = true
        end
    end

    if self.bShowHint then
        self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.P_KeyPrompt:Refresh("FlameJump")
    else
        self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.Collapsed)
    end
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "OnClick")

    self:checkVisibility()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, "KeyOnInput", self)
end

function P_FlameJump:checkVisibility()
    if Game.me.IsDead then
        self.userWidget:SetRenderOpacity(0)
    else
        if self.bOnEdge then
            self.userWidget:SetRenderOpacity(0)
        else
            if self.bMutex then
                self.userWidget:SetRenderOpacity(0)
            else
                self.userWidget:SetRenderOpacity(1)
                if not self.bEnableInteract then
                    self.view.img_flamejump:SetRenderOpacity(0.4)
                    self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.Collapsed)
                else
                    self.view.img_flamejump:SetRenderOpacity(1.0)
                    self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                end
            end
        end
    end
end

function P_FlameJump:SetEnableInteract(value)
    self.bEnableInteract = value
    self:checkVisibility()
end

function P_FlameJump:SetMutex(value)
    self.bMutex = value
    self:checkVisibility()
end

function P_FlameJump:GetMutex()
    return self.bMutex
end

function P_FlameJump:OnReturnToPool()
    if  self.TickTimer then
        Game.TimerManager:StopTimerAndKill(self.TickTimer)
    end
    self:ClearAllUIEvent()
    Game.EventSystem:RemoveObjListeners(self)
end

function P_FlameJump:IsWidgetVisible(Widget)
    local Visibility = Widget:GetVisibility()
    if
        (Visibility == ESlateVisibility.HitTestInvisible or Visibility == ESlateVisibility.SelfHitTestInvisible or
            Visibility == ESlateVisibility.Visible)
     then
        return true
    end
    return false
end

function P_FlameJump:GetCenterDistance()
    local viewportPos = import("SlateBlueprintLibrary").LocalToViewport(_G.GetContextObject(), self.view.WidgetRoot:GetCachedGeometry(), self.tempVector, nil, nil)
    viewportPos = viewportPos/self.ViewportScale
    local deltaX = viewportPos.X - self.halfSizeX
    local deltaY = viewportPos.Y - self.halfSizeY
    if self.bMutex then
        return deltaX * deltaX + deltaY * deltaY
    else
        --过渡值，避免闪烁
        return deltaX * deltaX + deltaY * deltaY - 400
    end
end

function P_FlameJump:KeyOnInput(ActionName, InputEvent)
    if (not self.bOnEdge) and self.bEnable and self.bEnableInteract and (not self.bMutex) and (not Game.me.IsDead) then
        if self.bShowHint and InputEvent == 0  then
            if ActionName == Enum.EInputType.FlameJump_Action then
                Game.FlameJumpSystem:DoFlameJump(self.InsID)
            end
        end
    end
    return UIBase.HANDLED
end

 function P_FlameJump:OnClick()
     if (not self.bOnEdge) and self.bEnable and self.bEnableInteract and (not self.bMutex) and (not Game.me.IsDead) then
         Game.FlameJumpSystem:DoFlameJump(self.InsID)
     end
 end

return P_FlameJump
