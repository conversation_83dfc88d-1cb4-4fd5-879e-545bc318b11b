local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class RolePlayGameHUD_Task_Item : UIListItem
---@field view RolePlayGameHUD_Task_ItemBlueprint
local RolePlayGameHUD_Task_Item = DefineClass("RolePlayGameHUD_Task_Item", UIListItem)

RolePlayGameHUD_Task_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RolePlayGameHUD_Task_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RolePlayGameHUD_Task_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RolePlayGameHUD_Task_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function RolePlayGameHUD_Task_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RolePlayGameHUD_Task_Item:InitUIView()
end

---面板打开的时候触发
function RolePlayGameHUD_Task_Item:OnRefresh(...)
end


--- 此处为自动生成
function RolePlayGameHUD_Task_Item:on_Btn_ClickArea_Clicked()
end

return RolePlayGameHUD_Task_Item
