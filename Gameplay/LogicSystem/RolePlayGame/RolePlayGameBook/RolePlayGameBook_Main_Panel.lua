local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class RolePlayGameBook_Main_Panel : UIPanel
---@field view RolePlayGameBook_Main_PanelBlueprint
local RolePlayGameBook_Main_Panel = DefineClass("RolePlayGameBook_Main_Panel", UIPanel)

--UI状态
RolePlayGameBook_Main_Panel.UIState = {
    MAIN = 1,                       --章节目录
    SETTLE = 2,                     --结算
    IDENTITY = 3                    --身份选择
}

RolePlayGameBook_Main_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RolePlayGameBook_Main_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RolePlayGameBook_Main_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RolePlayGameBook_Main_Panel:InitUIComponent()
    ---@type UIListView childScript: RolePlayGameBook_Btn_Item
    self.ListView_BtnCom = self:CreateComponent(self.view.ListView_Btn, UIListView)
    ---@type UIComBackTitle
    self.WBP_BackCom = self:CreateComponent(self.view.WBP_Back, UIComBackTitle)
    ---@type UIComDropDown
    self.WBP_RolePlayMain_SelectDropType1Com = self:CreateComponent(self.view.WBP_RolePlayMain_SelectDropType1, UIComDropDown)
end

---UI事件在这里注册，此处为自动生成
function RolePlayGameBook_Main_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RolePlayGameBook_Main_Panel:InitUIView()
end

---面板打开的时候触发
function RolePlayGameBook_Main_Panel:OnRefresh(...)
end

return RolePlayGameBook_Main_Panel
