local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class RolePlayGameShop_Money_Item : UIListItem
---@field view RolePlayGameShop_Money_ItemBlueprint
local RolePlayGameShop_Money_Item = DefineClass("RolePlayGameShop_Money_Item", UIListItem)

RolePlayGameShop_Money_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RolePlayGameShop_Money_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RolePlayGameShop_Money_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RolePlayGameShop_Money_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function RolePlayGameShop_Money_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RolePlayGameShop_Money_Item:InitUIView()
end

---面板打开的时候触发
function RolePlayGameShop_Money_Item:OnRefresh(moneyInfo)
    local moneyData = Game.TableData.GetRPGameMoneyDataRow(moneyInfo.MoneyId)
    if not moneyData then return end
    if StringValid(moneyData.Res) then
        self:SetImage(self.view.Img_Money, moneyData.Res)
    end
    self.view.Text_Money:SetText(moneyData.Name)
    self.view.Text_Number:SetText(moneyInfo.Count)
end

return RolePlayGameShop_Money_Item
