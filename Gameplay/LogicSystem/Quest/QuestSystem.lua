local bitset = kg_require("Shared.lualibs.bitset")
local StringConst = require "Data.Config.StringConst.StringConst"
local Const = kg_require("Shared.Const")
local QuestAction = kg_require("Gameplay.LogicSystem.Quest.QuestAction")
local QuestTrace = kg_require("Gameplay.LogicSystem.Quest.QuestTrace")
local KismetMathLibrary = import("KismetMathLibrary")
--local LuaFunctionLibrary = import("LuaFunctionLibrary")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")

local math_floor = math.floor
local QuestTargetConst = kg_require("Shared.Const.QuestConst").QuestTargetConst
local TaskMainTypeConst = kg_require("Shared.Const.QuestConst").TaskMainTypeConst
local TaskStatusConst = kg_require("Shared.Const.QuestConst").TaskStatusConst

---@class QuestSystem: SystemBase, QuestAction, QuestTrace @任务系统
local QuestSystem = DefineClass("QuestSystem", SystemBase, QuestAction, QuestTrace)

QuestSystem.Empty = {}

---@class EDynamicTalkExtraKind @动态对话扩展数据中枚举类型的含义
QuestSystem.EDynamicTalkExtraKind = {
    Dialogue = 1, --Dialogue
    DialogueStart = 2, --插入第一句
    DialogueEnd = 3, --插入最后一句
    Submit = 4, --道具提交
    SubmitStart = 5, --道具提交插入第一句
    SubmitEnd = 6, --道具提交插入最后一句
}

---@type number @默认标签key
QuestSystem.TagDefault = -1
---@type string @默认标签名称（全部）
QuestSystem.TagDefaultName = StringConst.Get("TASK_TAG_ALL")
QuestSystem.TagAccept = 0
QuestSystem.TagAcceptName = StringConst.Get("TASK_TAG_PROGRESS")
QuestSystem.TagAvaliable = 1
QuestSystem.TagAvaliableName = StringConst.Get("TASK_TAG_ACCEPTABLE")

---@class PriorityTaskStatusOffSet @任务状态优先级
QuestSystem.PriorityTaskStatusOffSet = {
    [TaskStatusConst.TASK_STATUS__AVAILABLE] = 300,
    [TaskStatusConst.TASK_STATUS__ACCEPTED] = 200,
    [TaskStatusConst.TASK_STATUS__ACCOMPLISHED] = 100
}

---@class PriorityTaskTypeOffSet @任务类型优先级
QuestSystem.PriorityTaskTypeOffSet = {
    [TaskMainTypeConst.TASK_MINITYPE__MAIN] = 1000,
    [TaskMainTypeConst.TASK_MINITYPE__SUBTASK] = 2000,
    [TaskMainTypeConst.TASK_MINITYPE__DUNGEON] = 3000,
    [TaskMainTypeConst.TASK_MINITYPE__ROLE_PLAY] = 4000,
}

---@class QuestSystem.PlayChapterEnum @章节效果类型
QuestSystem.PlayChapterEnum = {
    Start = 1,  --章节开始
    End = 2,  --章节结束
}

---@class QuestSystem.ESubmitDialogue @交互道具任务目标接dialogue的类型
QuestSystem.ESubmitDialogue = {
    None = 0, --不播放
    Before = 1, --交付前播
    After = 2   --交付后播
}
----------------------------Override----------------------------
QuestSystem.eventBindMap = {
    [_G.EEventTypes.DIALOGUE_PLOT_ON_START] = "OnDialogueStart",
    [_G.EEventTypes.DIALOGUE_PLOT_ON_END] = "OnDialogueEnd",
	[_G.EEventTypes.DIALOGUE_PLOT_SHOT_END] = "OnDialoguePlotShotEnd",
    [_G.EEventTypes.DIALOGUE_PLOT_SHOT_START] = "OnDialoguePlotShotStart",
    [_G.EEventTypes.CINEMATIC_ON_END] = "OnCinematicEnd",
    [EEventTypesV2.ON_STAR_MYSTERY_COMPLETE] = "OnMysteryComplete",
    [EEventTypesV2.ON_UI_OPEN] = "OnUIOpen",
    [EEventTypesV2.LEVEL_ON_LEVEL_LOAD_START] = "OnLevelLoadStart",
    [EEventTypesV2.ROLE_MOVE_INPUT] = "onRoleMoveInput",
    [_G.EEventTypes.On_ADDITION_SKILL_FINISH] = "onAdditionSkillFinish",
}

function QuestSystem:onCtor()
    ---@type boolean @TODO:是否切换到新任务模块
    self.switch = true
    ---@type table<number,boolean> @交互物Condition注册
    self.interactConditionRegister = {}
    ---@type string[] @描述文字拼接table
    self.conditionDescStr = {}
    ---@type table<number,table> @道具提交记录
    self.submitCache = {}
	---@type table 任务面板任务显示列表
	self.pageCache = {}    
    ---@type table 客户端传送门标记列表
    self.localPlanePortalMap = {}
    ---@type table 客户端传送门标记列表
    self.portalMap = {}


    ---@type table 临时表
    self.temp = {}
	
	---@type table<integer, integer> 激活任务的奖励缓存
	self.activeQuestRewardsCache = {}

	--TODO:临时记录， 从TaskManager移过来的
	self.autoTraceBegin = false
end

function QuestSystem:onInit()
    QuestAction.onInit(self)
    ---@type QuestModel
    self.model = kg_require("Gameplay.LogicSystem.Quest.QuestModel").new(true, true)
    ---@type QuestSender
    self.sender = kg_require("Gameplay.LogicSystem.Quest.QuestSender").new()
    ---@type QuestUseItem
    self.questUseItem = kg_require("Gameplay.LogicSystem.Quest.QuestUseItem").new()
    ---@type QuestCinematic
    self.questCinematic = kg_require("Gameplay.LogicSystem.Quest.QuestCinematic").new()
	
	self.questNavigation = kg_require("Gameplay.LogicSystem.Quest.QuestNavigation").new()
    self.model:initFilterTag(QuestSystem.TagDefault)
end

function QuestSystem:onUnInit()
	self.questNavigation:delete()
	self.questNavigation = nil
	self.questCinematic:delete()
	self.questCinematic = nil
end

---@TODO:是否切换到新任务模块
function QuestSystem:SetSwicth(value)
    self.switch = value
end

function QuestSystem:GetSwicth(value)
    return self.switch
end

function QuestSystem:ShowQuestUI()
	Game.NewUIManager:OpenPanel("TaskBoardPanel")
end

function QuestSystem:OnWorldMapLoadComplete(levelID)
    --快捷道具使用
    self.questUseItem:OnWorldMapLoadComplete(levelID)
    --Dialogue播放
    self.questCinematic:OnWorldMapLoadComplete(levelID)
    --客户端传送门
    if self.portalMap[levelID] then
        for levelID, questMap in pairs(self.portalMap) do
            for questID, conditionMap in pairs(questMap) do
                for conditionIndex, mapData in ipairs(conditionMap) do
                    if mapData.position then
                        self:addPosLocalPlanePortal(questID, conditionIndex, mapData.position, mapData.radius, mapData.bShowEffect)
                    else
                        self:addInsLocalPlanePortal(questID, conditionIndex, mapData.insID, mapData.radius, mapData.bShowEffect)
                    end
                end
            end
        end
    end
end

---@private 清理快捷使用道具图标
---@param nextLevelMapData LevelMapData
function QuestSystem:OnLevelLoadStart(nextLevelMapData)
    self.questUseItem:OnLevelLoadStart(nextLevelMapData)
end
----------------------private---------------------------
--regine TaskNewConfig（兼容任务编辑器）
---@private 任务配置表参数查询
QuestSystem._cfgMatch = {			
	RingName = true,
    ChapterID = true,
	ChapterName = true,
	ClassID = true,
	ChapterIndex = true,
	Type = true,
    Desc = true,
    bCanGiveUP = true,
    TestMode = true,
    MinLevel = true,
    MaxLevel = true,
    PreTaskID = true,
    QuestType = true,
    AbandonActions = true,
    ApplyActions = true,
}

---@private 任务配置表查询表缓存
QuestSystem._cfgMetas = {}

---@private 任务配置表查询元方法
QuestSystem._getCfgMeta = {
    __index = function(tb, key)
        local taskID = rawget(tb, "ID")
        local taskRingID = rawget(tb, "RingID")
        if key == "ID" then
            return taskID
        end
        if key == "RingID" then
            return taskRingID
        end
        local bIsMatch = QuestSystem._cfgMatch[key]
        local cfgKey
        if bIsMatch then
            cfgKey = "__RingCfg"
        else
            cfgKey = "__TaskCfg"
        end
        local cfg = rawget(tb, cfgKey)
        return cfg[key]
    end
}
--endregion TaskNewConfig（兼容任务编辑器）
----------------------CommonFun---------------------------
---@public 获取任务配置
---@param questID number @任务ID
function QuestSystem:GetTaskExcelCfg(questID)
    if not questID then
        return nil
    end

    if self._cfgMetas[questID] then
        return self._cfgMetas[questID]
    end
	self:UpdateCfgMetas(questID)
    return self._cfgMetas[questID]
end

function QuestSystem:UpdateCfgMetas(questID)
	local ringID = math_floor(questID / 100)
	local ok, taskRingCfg = xpcall(kg_require, _G.CallBackError, string.format("Data.Config.Quest.Ring.%d", ringID))
	if not ok then
		-- 因为是读的配置数据，所以这里把问题暴露出来，早点修复
		Log.ErrorFormat("QuestSystem:GetTaskExcelCfg error1 questID: %s", questID)
		return nil
	end

	local taskCfg
    local questData = taskRingCfg.QuestData
    assert(questData, string.format("RingID:%s do not have QuestData", ringID))
    taskCfg = questData[questID]
    if taskCfg == nil then
        -- 因为是读的配置数据，所以这里把问题暴露出来，早点修复
        Log.ErrorFormat("QuestSystem:GetTaskExcelCfg error2 questID:%s", questID)
        return nil
    end
	self._cfgMetas[questID] = setmetatable({ID = questID, RingID = ringID, __TaskCfg = taskCfg, __RingCfg = taskRingCfg}, self._getCfgMeta)
end

---@public 根据环ID获取任务配置
---@param ringID number @任务ID
function QuestSystem:GetQuestExcelCfgByRingID(ringID)
    local questID = self:GetQuestIDByRingID(ringID)
    if questID then
        return self:GetTaskExcelCfg(questID)
    end
end

---@public 获取环配置
---@param ringID number @任务环ID
function QuestSystem:GetRingExcelCfg(ringID)
    local ringFile = string.format("Data.Config.Quest.Ring.%d", ringID)
	local _, taskRingCfg = xpcall(kg_require, _G.CallBackError, ringFile)
	return taskRingCfg
end

---@public 获取章节配置
---@param chapterID number @章节ID
function QuestSystem:GetChapterExcelCfg(chapterID)
	local chapterFile = string.format("Data.Config.Quest.Chapter.%d", chapterID)
	local _, chapterCfg = xpcall(kg_require, _G.CallBackError, chapterFile)
	return chapterCfg
end

---@public 获取任务类型
---@param questID number @任务ID
function QuestSystem:GetQuestTypeByQuestID(questID)
    local ringID = self:GetRingIDByQuestID(questID)
    if not ringID then
        Log.WarningFormat("【Quest】Error QuestID:%s When GetQuestType", questID)
        return
    end
    return self:GetQuestTypeByRingID(ringID)
end

---@public 获取任务类型
---@param ringID number @环ID
function QuestSystem:GetQuestTypeByRingID(ringID)
    local ringCfg = self:GetRingExcelCfg(ringID)
    if not ringCfg then
        Log.WarningFormat("【Quest】Error RingID:%s When GetQuestType", ringID)
        return
    end
    return ringCfg.QuestType
end

---@public 获取任务目标配置（conditionIndex为nil时返回所有配置）
---@param questID number @任务ID
---@param conditionIndex number|nil @任务目标索引
---@return QuestTargetBase|QuestTargetBase[] @目标配置
function QuestSystem:GetConditionCfg(questID, conditionIndex)
    local questCfg = self:GetTaskExcelCfg(questID)
    if questCfg and questCfg.QuestTargets then
        if conditionIndex then
            if questCfg.QuestTargets[conditionIndex] then
                return questCfg.QuestTargets[conditionIndex]
            else
                return QuestSystem.Empty
            end
        else
            return questCfg.QuestTargets
        end
    else
        return QuestSystem.Empty
    end
end

---@public 根据环ID获取任务数据
---@param ringID number @任务环ID
---@return QuestInfo @任务环数据
function QuestSystem:GetQuestInfo(ringID)
    if not ringID then
        Log.WarningFormat("【Quest】RingID is Empty When GetQuestInfo(RingID:%s)", ringID)
        return
    end
    return self.model:GetQuestInfo(ringID)
end

---@public 根据环ID获取任务ID
---@param ringID number @任务环ID
function QuestSystem:GetQuestIDByRingID(ringID)
    if not ringID then
        Log.WarningFormat("【Quest】RingID is Empty When GetQuestIDByRingID (RingID:%s)", ringID)
        return
    end
    local questInfo = self:GetQuestInfo(ringID)
    if not questInfo then
        return self:GetFirstRingQuest(ringID)
    end
    if not questInfo.NowRingQuestID then
        if not questInfo.TaskID then
            --TODO：考虑在CI内标记最后一个任务
            Log.WarningFormat("【Quest】The Ring has already been completed(RingID:%s)", ringID)
        end
    end
    return questInfo.NowRingQuestID or questInfo.TaskID
end

function QuestSystem:CheckRingFinished(ringId)
	return self.model.finishQuests[ringId]
end

---@public 判断任务有效性（任务领取进行中）
---@param questID number @任务ID
---@return boolean @任务是否有效
function QuestSystem:GetQuestValid(questID)
    local ringID = self:GetRingIDByQuestID(questID)
    local questInfo = self:GetQuestInfo(ringID)
    if not questInfo then
        return false
    end
    if questInfo.NowRingQuestID ~= questID then
        return false
    end
    
    return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCEPTED
end

---@public 判断任务有效性（任务领取进行中）
---@param ringID number @环ID
---@return boolean @任务是否有效
function QuestSystem:GetRingIDValid(ringID)
    local questInfo = self:GetQuestInfo(ringID)
    if not questInfo then
        return false
    end
    return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCEPTED
end

---@public 根据任务ID获取环ID
---@param questID number @任务ID
function QuestSystem:GetRingIDByQuestID(questID)
    return math_floor(questID / 100)
end

---@public 根据任务ID获取任务数据
---@param questID number @任务ID
---@return QuestInfo
function QuestSystem:GetQuestInfoByQuestID(questID)
    local ringID = self:GetRingIDByQuestID(questID)
    if ringID then
        return self:GetQuestInfo(ringID)
    end
end

---@public 根据任务ID获取当前主目标描述
---@param questID number @任务ID
function QuestSystem:GeCurMainTargetIndex(questID)
    if self.model.mainConditionMap[questID] then
        return self.model.mainConditionMap[questID]
    end
    return 1
end

---@public 根据任务ID获取主目标索引
function QuestSystem:GetMainConditionIndex(questID)
    local conditionCfgs = self:GetConditionCfg(questID)
    for i = 1, #conditionCfgs do
        if conditionCfgs[i].bIsMain then
            return i
        end
    end
end

---@public 判断任务环是否完成
---@param ringID number @任务环ID
---@return boolean @是否完成
function QuestSystem:IsRingFinished(ringID)
    local questInfo = self:GetQuestInfo(ringID)
	if not questInfo then
		return false
	end
    return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__FINISHED
end

function QuestSystem:IsRingValid(ringID)
	local questInfo = self:GetQuestInfo(ringID)
	if not questInfo then
		return false
	end
	return true
end

---@public 判断任务环是否进行中
---@param ringID number @任务环ID
---@return boolean @是否进行中
function QuestSystem:IsRingAccepted(ringID)
    local questInfo = self:GetQuestInfo(ringID)
	if not questInfo then
		return false
	end
    return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCEPTED
end

---@public 判断任务环是否已完成待提交
---@param ringID number @任务环ID
---@return boolean @是否已完成待提交
function QuestSystem:IsRingAccomplished(ringID)
    local questInfo = self:GetQuestInfo(ringID)
	if not questInfo then
		return false
	end
    return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCOMPLISHED
end

---@public 判断任务是否完成
---@param questID number @任务ID
---@return boolean @是否完成
function QuestSystem:IsQuestFinished(questID)
    local questInfo = self:GetQuestInfoByQuestID(questID)
	if not questInfo then
		return false
	end
    if questInfo.NowRingQuestID == questID then
        return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__FINISHED
    end
    local ringFinishedMap = questInfo.RingFinishedMap
    if not ringFinishedMap then
        return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__FINISHED
    end
    local offect = questID%100
    local bFinished = bitset.getBit(questInfo.RingFinishedMap, offect)
    return bFinished
end

---@public 判断任务是否进行中
---@param questID number @任务ID
---@return boolean @是否进行中
function QuestSystem:IsQuestAccepted(questID)
    local questInfo = self:GetQuestInfoByQuestID(questID)
	if not questInfo then
		return false
	end
    if questInfo.NowRingQuestID == questID then
        return questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCEPTED
    end
    return false
end

---@public 判断任务是否为可领取状态
---@param ringID number 任务环ID
---return boolean 是否可领取
function QuestSystem:GetCanAccept(ringID)
	return self.model:GetCanAccept(ringID)
end

function QuestSystem:GetOpen(ringID)
	if self.model.openQuests[ringID] then
		return true
	end
	return false
end

---@public 判断任务目标是否完成(任务完成则认为任务目标完成，不做或目标类型的区分)
---@param questID number @任务ID
---@param conditionIndex number @任务目标ID
---@return boolean @是否完成
function QuestSystem:IsConditionCompleteByQuestID(questID, conditionIndex)
    if self:IsQuestFinished(questID) then
        return true
    end

    local questInfo = self:GetQuestInfoByQuestID(questID)
    if questInfo then
        if not questInfo.NowQuestTargets then
            Log.WarningFormat("【Quest】Error Find NowQuestTargets(QuestID:%s, Status:%s)", questID, questInfo.RingStatus)
            return false
        end
        local conditionData = questInfo.NowQuestTargets[conditionIndex]
        if not conditionData then
            Log.WarningFormat("【Quest】Error Find ConditionData(QuestID:%s, ConditionIndex:%s)", questID, conditionIndex)
            return false
        end
        return conditionData.IsFinished
    end

    return false
end

---@public 判断任务目标是否完成(任务完成则认为任务目标完成，不做或目标类型的区分)
---@param ringID number @任务ID
---@param conditionIndex number @任务目标ID
---@return boolean @是否完成
function QuestSystem:IsConditionCompleteByRingID(ringID, conditionIndex)
    if self:IsRingFinished(ringID) then
        return true
    end

    local questInfo = self:GetQuestInfo(ringID)
    if questInfo then
        local conditionData = questInfo.NowQuestTargets[conditionIndex]
        if not conditionData then
            Log.WarningFormat("【Quest】Error Find ConditionData(RingID:%s, ConditionIndex:%s)", ringID, conditionIndex)
            return false
        end
        return conditionData.IsFinished
    end

    return false
end

---@public 是否是环的最后任务
---@param questID number @任务ID
---@return boolean @是否是环的最后任务
function QuestSystem:IsLastRingQuest(questID)
    local questCfg = self:GetTaskExcelCfg(questID)
    if questCfg and #questCfg.NextTaskInfoList <= 0 then
        return true
    end
    return false
end

---@public 获取环第一个任务 TODO:待任务配置CI接入后修改
---@param ringID number @任务ID
function QuestSystem:GetFirstRingQuest(ringID)
    local ringCfg = Game.QuestSystem:GetRingExcelCfg(ringID)
    if not ringCfg then
        Log.ErrorFormat("Can't Find RingCfg(RingID:%s)", ringID)
        return 0
    end
	if QuestUtils.UseNewLuaSerializer then
		return ringCfg.FirstQuestID
	end
    return ringCfg.EditorOnly.QuestNode[1].QuestID
end

---@public 获取任务领取时间
---@param ringID number @任务ID
---@return number @时间
function QuestSystem:GetQuestAcceptTime(ringID)
    local questInfo = self:GetQuestInfo(ringID)
    if questInfo then
        return questInfo.RingStartAt or -1
    end
    Log.WarningFormat("【Quest】Error QuestInfo When Get AcceptTime(RingID%s)", ringID)
    return -1
end

---@public 获取任务面板任务显示列表
---@param filterIndex number @任务页签
---@param tagID number @任务标签
function QuestSystem:GetPageFilter(filterIndex, tagID, forceDirty)
	local questList = self.model:GetPageFilter(filterIndex, tagID, forceDirty)
	table.clear(self.pageCache)
	-- TODO:待优化，剔除空类目
	for idx, v in ipairs(questList) do
		local miniData = Game.TableData.GetTaskMiniTypeDataRow(v.QuestType) or {}
		local bNoNullCheck = miniData.NoNullCheck
		if #v.Children > 0 or bNoNullCheck then
			table.insert(self.pageCache, {Children = v.Children, QuestType = v.QuestType})
		end 
	end	
	
    return self.pageCache
end

---@public 获取当前已领取和进行中的所有任务RingID数组
function QuestSystem:GetRingList()
    return self.model:GetRingList()
end

---@public 获取任务面板任务显示列表
---@param filterIndex number @任务页签
function QuestSystem:GetTagOptions(filterIndex)
    return self.model:GetTagOptions(filterIndex)
end

---@public 获取正在追踪的任务环ID
---@return number @环ID
function QuestSystem:GetTracingRing()
    local TracingID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
    if TracingID then
        return TracingID
    end
    return 0
end

function QuestSystem:GetSecondTracingRing()
	local TracingID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
	if TracingID then
		return TracingID
	end
	return 0
end
-- 获取任务追踪的列表（最多追2个）
function QuestSystem:GetTracingList()
	local result = {}
	local TracingID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	if TracingID then
		result[#result+1] = TracingID
	end
	
	TracingID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_TYPE.SECOND_TASK)
	if TracingID then
		result[#result+1] = TracingID
	end
	return result
end

function QuestSystem:SetTracingRing(ringID)
    --Game.TraceSystem:SetTrace(Const.TRACING_TYPE.TASK, Const.TRACING_INFO_TYPE.TASK, ringID)
	self.sender:ReqSetQuestTrace(ringID)
end

-- 取消追踪
function QuestSystem:CancelTracingRing(ringID)
	local TracingID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	if TracingID == ringID then
		self.sender:ReqRemoveQuestTrace(Const.TRACING_INFO_TYPE.TASK)
		return
	end
	TracingID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
	if TracingID == ringID then
		self.sender:ReqRemoveQuestTrace(Const.TRACING_INFO_TYPE.SECOND_TASK)
		return
	end
end

-- 获取任务距离开启还有多久，返回2个int值，分别为天和小时, 输入为任务开启时间的timestamp
function QuestSystem:GetTimeByTimestamp(timestamp)
	local nowTimestamp = math_floor(_G._now() / 1000)
	local diffHour = math.round((timestamp - nowTimestamp) / 60 / 60)
	return math_floor(diffHour / 24), diffHour - math_floor(diffHour / 24) *24
end


---根据动态对话类型和ID获取监听的任务ID
---@param kind NPCSystem.EDynamicKind @枚举，动态对话类型
---@param genericID number @根据kind枚举来解析的对话ID
function QuestSystem:GetQuestIDByDynamicTalk(kind, genericID, npcCfgID)
    if kind == Game.NPCSystem.EDynamicKind.Talk then
        return self.model.talkFlag[genericID]
    elseif kind == Game.NPCSystem.EDynamicKind.Dialogue then
        local ringID = self.model.receiveDialogueFlag[genericID]
        if ringID then
            return self:GetFirstRingQuest(ringID)
        else
            local find_NpcCfgID = self.model.findNpcIconCollapseMap[genericID]
            if find_NpcCfgID and find_NpcCfgID == npcCfgID then
                --屏蔽寻找npc任务目标与任务的联系(仅头顶图标判断)
				Log.WarningFormat("[Quest] Error findNpcIconCollapseMap DynamicTalk Kind(Kind:%s, GenericID:%d)", kind, genericID)
				return nil
            end
			local _, questId, _ = QuestUtils.GetDialogueQuestAndCondIdxByDialogueUniqId(genericID)
            return questId
        end
    elseif kind == Game.NPCSystem.EDynamicKind.SubmitDialogue then
        local submitID = self:GetSubmitIDByDynamicTalk(genericID)
        return self:GetQuestIDBySubmitID(submitID)
    elseif kind == Game.NPCSystem.EDynamicKind.Submit then
        return self:GetQuestIDBySubmitID(genericID)
    else
        Log.WarningFormat("[Quest] Error DynamicTalk Kind(Kind:%s, GenericID:%d)", kind, genericID)
    end
end

---根据动态对话类型和ID获取监听的任务ID
---@param dialogueID number @根据对话ID获取领取任务
function QuestSystem:GetReceiveRingIDByDialogueID(dialogueID)
    return self.model.receiveDialogueFlag[dialogueID]
end

---根据动态对话类型和ID获取监听的道具提交ID
---@param dialogueID number @根据kind枚举来解析的对话ID
function QuestSystem:GetSubmitIDByDynamicTalk(dialogueID)
    return self.model.dialogueSubmitMap[dialogueID]
end

---根据道具提交ID获取任务ID
---@param submitUniqId string @根据kind枚举来解析的对话ID
function QuestSystem:GetQuestIDBySubmitID(submitUniqId)
    return self.model.submitFlag[submitUniqId][1]
end

---根据道具提交ID获取任务ID和序号
function QuestSystem:GetQuestIDAndCondIdxBySubmitID(submitUniqId)
	local submitInfo = self.model.submitFlag[submitUniqId]
	if submitInfo then
		return submitInfo[1], submitInfo[2]
	else
		return nil, nil
	end
end

---@return QuestTarget.ITEM_SUBMIT
function QuestSystem:GetQuestConditionCfgBySubmitID(submitUniqId)
	local submitInfo = self.model.submitFlag[submitUniqId]
	if submitInfo then
		local questID, conditionIndex = submitInfo[1], submitInfo[2]
		return self:GetConditionCfg(questID, conditionIndex)
	else
		Log.WarningFormat("QuestSystem:GetQuestConditionCfgBySubmitID err, submitUniqId: %s", submitUniqId)
	end
end

---@public @根据任务类型获取文本颜色
---@param taskType number @任务类型枚举
---@param colorType ColorManager.Type|nil @颜色类型
---@return color @文本色
function QuestSystem:GetTextColorByTaskType(taskType, colorType)
    colorType = colorType or Game.ColorManager.Type.SlateColor
    local color, outLineColor
    if taskType == TaskMainTypeConst.TASK_MINITYPE__MAIN then
        --主线
        color = Game.ColorManager:GetColor("TaskIconColor", "Task_Text", colorType)
    elseif taskType == TaskMainTypeConst.TASK_MINITYPE__SUBTASK then
        --支线
        color = Game.ColorManager:GetColor("TaskIconColor", "Task_spur_Text", colorType)
    elseif taskType == TaskMainTypeConst.TASK_MINITYPE__ROLE_PLAY then
        --城市
        color = Game.ColorManager:GetColor("TaskIconColor", "City_Text", colorType)
    else
        --系统
        color = Game.ColorManager:GetColor("TaskIconColor", "Daily_Text", colorType)
    end
    return color,outLineColor
end

---@public @根据任务类型获取图标颜色
---@param taskType number @任务类型枚举
---@return string,string @色彩,外缘色彩
function QuestSystem:GetIconColorByTaskType(taskType, colorType)
    colorType = colorType or Game.ColorManager.Type.SlateColor
    local color, outLineColor
    if taskType == TaskMainTypeConst.TASK_MINITYPE__MAIN then
        --主线
        color = Game.ColorManager:GetColor("TaskIconColor", "Task", colorType)
        outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Task_Light", colorType)
    elseif taskType == TaskMainTypeConst.TASK_MINITYPE__SUBTASK then
        --支线
        color = Game.ColorManager:GetColor("TaskIconColor", "Task_spur", colorType)
        outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Task_spur_Light", colorType)
    elseif taskType == TaskMainTypeConst.TASK_MINITYPE__DUNGEON then
        --副本
        color = Game.ColorManager:GetColor("TaskIconColor", "Dungeon", colorType)
        outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Dungeon_Light", colorType)
    elseif taskType == TaskMainTypeConst.TASK_MINITYPE__ROLE_PLAY then
        --城市
        color = Game.ColorManager:GetColor("TaskIconColor", "City", colorType)
        outLineColor = Game.ColorManager:GetColor("TaskIconColor", "City_Light", colorType)
    else
        --系统
        color = Game.ColorManager:GetColor("TaskIconColor", "Daily", colorType)
        outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Daily_Light", colorType)
    end
    return color,outLineColor
end

function QuestSystem:GetTraceIconColorByTaskType(taskType)
	local color, outLineColor
	if taskType == nil then
		--系统
		color = Game.ColorManager:GetColor("TaskIconColor", "Daily_Trace_Main", Game.ColorManager.Type.LinearColor)
		outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Daily_Trace_Light", Game.ColorManager.Type.LinearColor)
		return color,outLineColor
	end
	if taskType == TaskMainTypeConst.TASK_MINITYPE__MAIN then
		--主线
		color = Game.ColorManager:GetColor("TaskIconColor", "Task_Trace_Main", Game.ColorManager.Type.LinearColor)
		outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Task_Trace_Light", Game.ColorManager.Type.LinearColor)
	elseif taskType == TaskMainTypeConst.TASK_MINITYPE__SUBTASK then
		--支线
		color = Game.ColorManager:GetColor("TaskIconColor", "Task_spur_Trace_Main", Game.ColorManager.Type.LinearColor)
		outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Task_spur_Trace_Light", Game.ColorManager.Type.LinearColor)
	elseif taskType == TaskMainTypeConst.TASK_MINITYPE__DUNGEON then
		--副本
		color = Game.ColorManager:GetColor("TaskIconColor", "Dungeon_Trace_Main", Game.ColorManager.Type.LinearColor)
		outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Dungeon_Trace_Light", Game.ColorManager.Type.LinearColor)
	elseif taskType == TaskMainTypeConst.TASK_MINITYPE__ROLE_PLAY then
		--城市
		color = Game.ColorManager:GetColor("TaskIconColor", "CIty_Trace_Main", Game.ColorManager.Type.LinearColor)
		outLineColor = Game.ColorManager:GetColor("TaskIconColor", "CIty_Trace_Light", Game.ColorManager.Type.LinearColor)
	else
		--系统
		color = Game.ColorManager:GetColor("TaskIconColor", "Daily_Trace_Main", Game.ColorManager.Type.LinearColor)
		outLineColor = Game.ColorManager:GetColor("TaskIconColor", "Daily_Trace_Light", Game.ColorManager.Type.LinearColor)
	end
	return color,outLineColor
end


---@public @获取NPC头顶图标
---@param ringID number @任务环ID
---@return string,string,string,string @图片路径,图片bg路径,色彩,外缘色彩
function QuestSystem:GetNPCTaskIconAndColor(ringID, colorType)
    if (not ringID) or ringID <= 0 then
        Log.WarningFormat("【Quest】RingID Is Nil When GetNPCTaskIconAndColor")
        return
    end
    local questType = self:GetQuestTypeByRingID(ringID)
    local color, outLineColor = self:GetIconColorByTaskType(questType, colorType)
    local iconPath, iconBgPath
	-- 这里换成新接口
	iconPath = self:GetTaskRingIcon(ringID, true)
    --local questInfo = self:GetQuestInfo(ringID)
    --if questInfo then
	--	if questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCOMPLISHED then
	--		iconPath = UIAssetPath.UI_Task_Icon_Tick_Sprite
	--		iconBgPath = UIAssetPath.UI_Task_Icon_WhiteTask_Sprite
	--	elseif questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCEPTED then
	--		iconPath = UIAssetPath.UI_Task_Icon_WhiteSquare_Sprite
	--		-- iconBgPath = UIAssetPath.UI_Task_Icon_WhiteTask_Sprite
	--	else
	--		--- 这里即使放弃了，也需要显示，表示可接取
	--		iconPath = UIAssetPath.UI_Task_Icon_Exclamation_Sprite
	--		iconBgPath = UIAssetPath.UI_Task_Icon_WhiteTask_Sprite
	--	end
    --else
    --    if self.model.activeQuests[ringID] then
    --        iconPath = UIAssetPath.UI_Task_Icon_Exclamation_Sprite
    --        iconBgPath = UIAssetPath.UI_Task_Icon_WhiteTask_Sprite
    --    end
    --end

    return iconPath, iconBgPath, color, outLineColor
end

---获取环任务的icon
---@param ringID number 环ID
---@return string
function QuestSystem:GetTaskRingIcon(ringID, bMap)
	local ringExcelCfg = self:GetRingExcelCfg(ringID)
	if ringExcelCfg then
		local taskType = Game.QuestSystem:GetQuestTypeByRingID(ringID)
		local groupType = QuestUtils.GetQuestGroupByType(taskType)
		local taskFilterData = Game.TableData.GetTaskFilterDataRow(groupType)
		if taskFilterData then
			local questInfo = self:GetQuestInfo(ringID)
			if questInfo == nil or questInfo.RingStatus == TaskStatusConst.TASK_STATUS__AVAILABLE then
				-- 激活未接取
				if bMap then
					return taskFilterData.UnReceiveTaskMap
				else
					return taskFilterData.UnReceiveTask
				end
			elseif questInfo.RingStatus == TaskStatusConst.TASK_STATUS__ACCEPTED then
				-- 已接取
				if bMap then
					return taskFilterData.InProgressTaskMap
				else
					return taskFilterData.InProgressTask
				end
			end
		else
			Log.ErrorFormat("【Quest】[GetTaskRingIcon] Failed to get TaskFilterData of groupType %d, ringID: %d", groupType, ringID)
		end
	end
	-- 缺省值
	return UIAssetPath.UI_ConfigIcon_Map_Icon_07
end

---根据任务ID获取奖励配置信息
---@param questID number @任务ID
---@param outTable table @返回的奖励数据
function QuestSystem:GetRewardByQuestID(questID, outTable)
    local ringID = self:GetRingIDByQuestID(questID)
	return self:GetRewardByRingID(ringID, outTable)
end

function QuestSystem:UpdateActiveQuestRewardsCache()
	table.clear(self.activeQuestRewardsCache)
	local activeQuests = self.model.activeQuests
	for ringId, _ in pairs(activeQuests) do
		local rewardItems = {}
		self:GetRewardByRingID(ringId, rewardItems)
		for _, itemData in pairs(rewardItems) do
			---这里不管，后面直接覆盖前面的
			self.activeQuestRewardsCache[itemData.ItemID] = ringId
		end
	end
end

function QuestSystem:CheckItemInQuestRewards(itemId, force)
	if force then
		self:UpdateActiveQuestRewardsCache()
	end
	local ringId = self.activeQuestRewardsCache[itemId] 
	return ringId ~= nil, ringId
end

---根据环ID获取奖励配置信息
---@param ringID number @环ID
---@param outTable table @返回的奖励数据
function QuestSystem:GetRewardByRingID(ringID, outTable)
	outTable = outTable or {}
	--是否是展示奖励，展示奖励优先级最高，会覆盖其他奖励
	local taskRingRewards = Game.TableData.GetTaskChapterRewardDataRow(ringID)
	if not taskRingRewards then
		Log.WarningFormat("【Quest】[GetTaskReward] Failed to get ChapterRewardData of task %d or TaskRewardData", ringID)
		return outTable
	end

	local rewardDatas = {}
	if taskRingRewards.IsHiddenReward then
		table.insert(outTable, { IsMystery = true })
		return outTable
	elseif ksbcnext(taskRingRewards.GroupRewardShow) then
		--展示奖励优先级最高
		table.insert(rewardDatas, taskRingRewards.GroupRewardShow)
	elseif (taskRingRewards.FixedReward and ksbcnext(taskRingRewards.FixedReward)) or
		(taskRingRewards.FinishRingSystemAction and ksbcnext(taskRingRewards.FinishRingSystemAction)) then
		--环最终奖励优先级第二
		if taskRingRewards.FinishRingSystemAction and ksbcnext(taskRingRewards.FinishRingSystemAction) then
			for _, dropAction in ksbcipairs(taskRingRewards.FinishRingSystemAction) do
				table.insert(rewardDatas, dropAction)
			end
		end

		if taskRingRewards.FixedReward and ksbcnext(taskRingRewards.FixedReward) then
			for _, dropAction in ksbcipairs(taskRingRewards.FixedReward) do
				table.insert(rewardDatas, dropAction)
			end
		end
	end

	for _, rewardData in ipairs(rewardDatas) do
		local rewardResult = Game.DropSystem.GetDropShowRewardData(rewardData)
		if rewardResult and rewardResult[2] and next(rewardResult[2]) then
			for _, V in ipairs(rewardResult[2]) do
				table.insert(outTable, {
					ItemID = V[1],
					Count = V[2],
				})
			end
		end
	end

	return outTable
end

---@TODO:历史遗留代码,待删
function QuestSystem:GetTargetRequirement(questID, conditionIndex)
    local conditionCfg = self:GetConditionCfg(questID, conditionIndex)
    return conditionCfg.Count or 1
end

function QuestSystem:GetMainTargetRequireAndFinishCount(questID, conditionIndex)
	local requireCnt, finishCnt = 1, 0
	local questCfg = self:GetTaskExcelCfg(questID)
	if not questCfg then
		return requireCnt, finishCnt
	end
	local conditionCfg = questCfg.QuestTargets[conditionIndex]
	local conditionCfgType = conditionCfg.Type
	if conditionCfgType == QuestTargetConst.TARGET_TYPE__FINISH_ALL_SUBTARGETS then
		local allConditionCnt = #questCfg.QuestTargets
		requireCnt = allConditionCnt - 1
		for i = 1, allConditionCnt do
			if i ~= conditionIndex and self:IsConditionCompleteByQuestID(questID, i) then
				finishCnt = finishCnt + 1
			end
		end
	elseif conditionCfgType == QuestTargetConst.TARGET_TYPE__FINISH_SUBTARGET_LIST then
		requireCnt = #conditionCfg.NecessaryFinishedList + #conditionCfg.SecondaryFinishedList
		for _, idx in ipairs(conditionCfg.NecessaryFinishedList) do
			if self:IsConditionCompleteByQuestID(questID, idx) then
				finishCnt = finishCnt + 1
			end
		end
		for _, idx in ipairs(conditionCfg.SecondaryFinishedList) do
			if self:IsConditionCompleteByQuestID(questID, idx) then
				finishCnt = finishCnt + 1
			end
		end
	else
		requireCnt = conditionCfg.Count or 1
		local questData = self:GetQuestInfoByQuestID(questID)
		if requireCnt > 0 and questData and questData.NowQuestTargets and questData.NowQuestTargets[conditionIndex] then
			finishCnt = questData.NowQuestTargets[conditionIndex].FinishCount
		end
	end
	return requireCnt, finishCnt
end

function QuestSystem:GetConditionRawDescAndShowMode(questID, conditionIndex)
	local questCfg = self:GetTaskExcelCfg(questID)
	local conditionCfg = self:GetConditionCfg(questID, conditionIndex)

	local desc
	local showMode = conditionCfg.TargetNumShowMode
	if conditionCfg.bIsMain then
		local index = self:GeCurMainTargetIndex(questID)
		local mainTarCfg = questCfg.MainTargetTraceCombo[index]
		if not mainTarCfg.bIsDescVisible then
			return ''
		end
		showMode = mainTarCfg.TargetNumShowMode
		desc = mainTarCfg.Desc
	else
		if not conditionCfg.bIsDescVisible then
			return ''
		end
		if conditionCfg.bShowTargetNum then
			showMode = QuestUtils.ETargetNumShowMode.ShowNumOnly
		end
		desc = conditionCfg.Desc
	end
	return desc, showMode
end

---获取任务目标描述（任务目标需要进行富文本处理）
---@param questID number @任务ID
---@param conditionIndex number @任务目标ID
---@return string @任务目标描述
function QuestSystem:GetConditionDesc(questID, conditionIndex)
	local desc, showMode = self:GetConditionRawDescAndShowMode(questID, conditionIndex)
	if showMode and QuestUtils.CheckShowTargetNum(showMode) then
		local requireCnt, finishCnt = self:GetMainTargetRequireAndFinishCount(questID, conditionIndex)
		-- 如果数量是1，那么则不显示
		if requireCnt == 1 then
			return desc
		end
		table.clear(self.conditionDescStr)
		self.conditionDescStr[#self.conditionDescStr+1] = desc
		self.conditionDescStr[#self.conditionDescStr+1] = "("
		self.conditionDescStr[#self.conditionDescStr+1] = tostring(finishCnt)
		self.conditionDescStr[#self.conditionDescStr+1] = "/"
		self.conditionDescStr[#self.conditionDescStr+1] = tostring(requireCnt)
		self.conditionDescStr[#self.conditionDescStr+1] = ")"
		desc = table.concat(self.conditionDescStr)
		table.clear(self.conditionDescStr)
		return desc
	else
		return desc
	end
end

function QuestSystem:IsTransPortValid(InsID)
    local transPortValidMap = self.model.transPortValidMap[InsID]
    if not transPortValidMap then
        return false
    end

    return true, transPortValidMap.questID, transPortValidMap.conditionIndex
end

---@public @根据地图ID获取任务环ID与NPCID的映射
---@param levelID number @地图ID
---@generic RingID number @环ID
---@generic NPCID number @NPC配置ID
---@return table<RingID,NPCID> @任务环ID与NPCID的映射
function QuestSystem:GetRingNpcReciveMapByLevelID(levelID)
    if not self.model.npcReciveMap[levelID] then
        return QuestSystem.Empty
    end
    return self.model.npcReciveMap[levelID]
end

---TODO:检查任务道具是否有效
function QuestSystem:CheckIfTaskItemIsValid(itemId)
    local itemExcelData = Game.TableData.GetItemNewDataRow(itemId)
    if itemExcelData.operationGroupType == 17 or itemExcelData.operationGroupType == 22 then
        return self:OnUseQuestItem(itemId, true)
    else
        return true
    end
end


function QuestSystem:GetInstanceQuestMap()
    local ok, InstanceQuestMap = xpcall(kg_require, _G.CallBackError, "Data.Config.Quest.InstanceQuestMap")
    if not ok then
		-- 因为是读的配置数据，所以这里把问题暴露出来，早点修复
		Log.ErrorFormat("QuestSystem:GetInstanceQuestMap error questID: %s", questID)
        return nil
    end
    return InstanceQuestMap
end


----------------------Interactor----------------------
---@public 获取任务覆盖交互物的交互按钮名称
---@param questID number @任务ID
---@param conditionIndex number @任务目标索引
---@return string @名称
function QuestSystem:GetInteractorName(questID, conditionIndex)
    ---@type QuestTarget.INTERACTION_PLANE_POS
    local conditionCfg = self:GetConditionCfg(questID, conditionIndex)
    return conditionCfg.UIShowText
end

---@public 获取任务覆盖交互物的TemplateID
---@param questID number @任务ID
---@param conditionIndex number @任务目标索引
---@return number @TemplateID
function QuestSystem:GetInteractorTemplateID(questID, conditionIndex)
    ---@type QuestTarget.INTERACTION_PLANE_POS
    local conditionCfg = self:GetConditionCfg(questID, conditionIndex)
    return conditionCfg.UITemplateID
end

---@public 获取任务覆盖交互物是否显示特效
---@param questID number @任务ID
---@param conditionIndex number @任务目标索引
---@return boolean @是否显示特效
function QuestSystem:IsInteractorShowEffect(questID, conditionIndex)
    local conditionCfg = self:GetConditionCfg(questID, conditionIndex)
    return conditionCfg.IsShowParticle
end

---@public 获取任务覆盖交互物交互半径
---@param questID number @任务ID
---@param conditionIndex number @任务目标索引
---@return number @半径
function QuestSystem:IsInteractorRadius(questID, conditionIndex)
    local conditionCfg = self:GetConditionCfg(questID, conditionIndex)
    return conditionCfg.Radius
end
----------------------DynamicTalk----------------------
function QuestSystem:getTalkPriority(questID)
    local statusOffSet = 0
    local typeOffSet = 0

    local questInfo = self:GetQuestInfoByQuestID(questID)
    local questType = self:GetQuestTypeByQuestID(questID)

    if questInfo and self.PriorityTaskStatusOffSet[questInfo.RingStatus] then
        statusOffSet = self.PriorityTaskStatusOffSet[questInfo.RingStatus]
    else
        statusOffSet = 900
    end
    if self.PriorityTaskTypeOffSet[questType] then
        typeOffSet = self.PriorityTaskTypeOffSet[questType]
    else
        typeOffSet = 9000
    end

    return Game.NPCSystem.TalkOptionPriority.TaskPriorityStart + statusOffSet + typeOffSet
end

function QuestSystem:getReciveTalkPriority(ringID)
    local statusOffSet = 900
    local typeOffSet = 0

    local questType = self:GetQuestTypeByRingID(ringID)

    if self.PriorityTaskTypeOffSet[questType] then
        typeOffSet = self.PriorityTaskTypeOffSet[questType]
    else
        typeOffSet = 9000
    end

    return Game.NPCSystem.TalkOptionPriority.TaskPriorityStart + statusOffSet + typeOffSet
end

---检查使用中的任务道具是否满足条件
---@return boolean, boolean 是否在任务中的使用道具， 是否满足条件
function QuestSystem:CheckUsedItemCondMeet(itemID)
	local useItemMap = self.model.useItemMap[itemID]
	if useItemMap and #useItemMap > 0 then
		local questID = useItemMap[1].QuestID
		local result = self:tryUseQuestItem(questID, itemID, true)
		return true, result
	end
	return false, false
end

---@param bIsTry boolean @是否只是尝试使用
---@return boolean
function QuestSystem:tryUseQuestItem(questID, ItemID, bIsTry)
    local playerEntity = Game.me
    if not playerEntity then
        return false
    end
    local pos = playerEntity:GetPosition()
	-- 因为玩家与npc或者交互物的距离计算与trigger相关的计算不同，需要考虑一下玩家的胶囊体半径，下面的100是个魔数，与服务器保持一致
	local extraDist = 100
	
    local conditionCfgs = self:GetConditionCfg(questID)
    local questInfo = self:GetQuestInfoByQuestID(questID)
    local currentLevelID = Game.LevelManager.GetCurrentLevelID()
    local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()

    for conditionIndex = 1, #conditionCfgs do
        local conditionCfg = conditionCfgs[conditionIndex]
        if self.model:checkConditionValid(questInfo, conditionIndex) and ItemID == conditionCfg.ItemID then
            if conditionCfg.Type == QuestTargetConst.TARGET_TYPE__USE_ITEM then
                -- self:ReqUseQuestItemByTarget(ItemID, questID, conditionIndex)
                if not bIsTry then
                    --Game.BagSystem:ReqQuickUse(ItemID, true)
					self:ReqUseQuestItemByTarget(ItemID, questID, conditionIndex)
                end
                return true
            elseif conditionCfg.Type == QuestTargetConst.TARGET_TYPE__USE_ITEM_AT_POSITION then
                local levelID, planeID = self:getPlaneAndMapID(conditionCfg.MapID, 0)
                if levelID == currentLevelID and planeID == currentPlaneID then
                    local distance = KismetMathLibrary.Vector_Distance(pos, conditionCfg.Position)
                    if distance < conditionCfg.Radius + extraDist then
                        if not bIsTry then
                            self:ReqUseQuestItemByTarget(ItemID, questID, conditionIndex)
                        end
                        return true
                    end
                end
            elseif conditionCfg.Type == QuestTargetConst.TARGET_TYPE__USE_ITEM_BY_INSTANCEID then
                local insIDs = conditionCfg.InstanceIDList
                for i = 1, #insIDs do
                    local insID = insIDs[i].InstanceID
                    local eid = Game.WorldManager:GetNpcByInstance(insID)
                    local RPCEntity = Game.EntityManager:getEntity(eid)
                    if RPCEntity then
						local distance = KismetMathLibrary.Vector_Distance(pos, RPCEntity:GetPosition())
                        if distance < insIDs[i].Radius + extraDist then
                            if not bIsTry then
                                self.sender:ReqUseQuestItemByInstanceListIndex(ItemID, questID, conditionIndex, i)
                            end
                            return true
                        end
                    end
                end
            end
        end
    end
    return false
end

function QuestSystem:addInsLocalPlanePortal(questID, conditionIndex, insID, radius, bShowEffect)
    Game.WorldManager:AddLocalPlanePortal(insID, nil, radius, bShowEffect)
    self.localPlanePortalMap[insID] = {
        questID = questID,
        conditionIndex = conditionIndex,
    }
end

function QuestSystem:addPosLocalPlanePortal(questID, conditionIndex, position, radius, bShowEffect)
    local insID = Game.WorldManager:AddLocalPlanePortal(nil, position, radius, bShowEffect)
    self.localPlanePortalMap[insID] = {
        questID = questID,
        conditionIndex = conditionIndex,
    }
end
---------------------Interactor---------------------
---@class QuestSystem.HideConditionType @任务条件状态判断
QuestSystem.HideConditionType = {
    TaskCom = 1,
    TaskAccept = 2,
    TaskRingCom = 3,
    TaskRingAccept = 4,
}

QuestSystem.ConditionFun = {
    ---@param self QuestSystem
    [QuestSystem.HideConditionType.TaskCom] = function(self, param)
        return self:IsQuestFinished(param.TaskID)
    end,
    ---@param self QuestSystem
    [QuestSystem.HideConditionType.TaskAccept] = function(self, param)
        return self:IsQuestAccepted(param.TaskID)
    end,
    ---@param self QuestSystem
    [QuestSystem.HideConditionType.TaskRingCom] = function(self, param)
        return self:IsRingFinished(param.TaskRingID)
    end,
    ---@param self QuestSystem
    [QuestSystem.HideConditionType.TaskRingAccept] = function(self, param)
        return self:IsRingAccepted(param.TaskRingID)
    end,
}

---@采集物是否隐藏
---@param insID number @实例ID
function QuestSystem:IsPickObjectHidden(insID)
    local hideCfg = Game.TableData.GetTaskCollectHideDataRow(insID)
    if not hideCfg then
        return false
    end

    --初始刷新
    if not hideCfg.Initial then
        return true
    end
    --可见性别
    local Gender = hideCfg.Gender
    if Gender ~= -1 then
        local sex = Game.me.Sex
        if Gender ~= sex then
            return true
        end
    end
    --可见职业
    local Profession = hideCfg.Profession
    if Profession ~= 0 then
        if Game.me then
            local mainProfession = Game.me.Profession
            if mainProfession ~= nil then
                if mainProfession ~= Profession then
                    return true
                end
            end 
        end
    end
    --等级
    local MinLevel = hideCfg.MinLevel
    local MaxLevel = hideCfg.MaxLevel
    if MinLevel > 0 then
        if Game.me.Level < MinLevel then
            return true
        end
    end
    if MaxLevel > 0 then
        if Game.me.Level > MaxLevel then
            return true
        end
    end

    for order, conditionCfg in ksbcipairs(hideCfg.ConditionList) do
        if self.ConditionFun[conditionCfg.Type](self, conditionCfg.Param) then
            return not conditionCfg.Visibliity
        end
    end

    return false
end


--是否满足条件
function QuestSystem:IsNPCHide(npcHideCfg, bIsVisibility)
	for _, conditionCfg in ksbcipairs(npcHideCfg.ConditionList) do
		if QuestSystem.ConditionFun[conditionCfg.Type](self, conditionCfg.Param) then
			return not conditionCfg.Visibliity
		end
	end

	return not bIsVisibility
end

function QuestSystem:collectorVisiblityCheckByRing(state, ringID)
    local TaskRingIDMarkForCollect = Game.TableData.Get_TaskRingIDMarkForCollect()
    local markTab = TaskRingIDMarkForCollect[state]
    if markTab then
        markTab = markTab[ringID]
        if markTab and #markTab > 0 then
            for i = 1, #markTab, 1 do
                local info = markTab[i]
                local entity
                if bUseNewPickObject then
                    entity = Game.EntityManager:getEntityByInsID(info.InstanceID)
                else
                    entity = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(info.InstanceID)
                end
                entity:RefreshHiddenByQuest()
            end
        end
    end
end

---@检测交互物显隐状态
---@param state QuestSystem.HideConditionType @状态
---@param questID number @任务ID
function QuestSystem:collectorVisiblityCheck(state, questID)
    local TaskIDMarkForCollect = Game.TableData.Get_TaskIDMarkForCollect()
    local markTab = TaskIDMarkForCollect[state]
    if markTab then
        markTab = markTab[questID]
        if markTab and #markTab > 0 then
            for i = 1, #markTab, 1 do
                local info = markTab[i]
                local entity
                if bUseNewPickObject then
                    entity = Game.EntityManager:getEntityByInsID(info.InstanceID)
                else
                    entity = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(info.InstanceID)
                end
                if entity then
                    entity:RefreshHiddenByQuest()
                end
            end
        end
    end
    local ringID = self:GetRingIDByQuestID(questID)
    if state == QuestSystem.HideConditionType.TaskCom and self:IsLastRingQuest(questID) then
        self:collectorVisiblityCheckByRing(state, ringID)
    end
end

function QuestSystem:npcVisiblityCheckWithRingID(state, ringID)
    local TaskRingIDMark = Game.TableData.Get_TaskRingIDMark()
    local tab = TaskRingIDMark[state]
    if not tab then
        return
    end
    tab = tab[ringID]
    if (not tab) or (#tab <= 0) then
        return
    end
    for _, info in ksbcpairs(tab) do
        local instanceID = info.InstanceID
        local visibility = info.Visibility
        if not visibility then
            --隐藏
            local eid = Game.WorldManager:GetNpcByInstance(instanceID)
            local RPCEntity = Game.EntityManager:getEntity(eid)
            if RPCEntity then
				RPCEntity:SetInvisibleByQuestControl(true)
            end
        else
            --显示
            local eid = Game.WorldManager:GetNpcByInstance(instanceID)
            local RPCEntity = Game.EntityManager:getEntity(eid)
            if RPCEntity then
				RPCEntity:SetInvisibleByQuestControl(false)
            end
        end
    end
end

function QuestSystem:npcVisiblityCheckWithQuestID(state, questID)
    local TaskIDMark = Game.TableData.Get_TaskIDMark()
    local tab = TaskIDMark[state]
    if not tab then
        return
    end
    tab = tab[questID]
    if (not tab) or (#tab <= 0) then
        return
    end
    for _, info in ksbcpairs(tab) do
        local instanceID = info.InstanceID
        local visibility = info.Visibility
        if not visibility then
            --隐藏
            local eid = Game.WorldManager:GetNpcByInstance(instanceID)
            local RPCEntity = Game.EntityManager:getEntity(eid)
            if RPCEntity then
				RPCEntity:SetInvisibleByQuestControl(true)
            end
        else
            --显示
            local eid = Game.WorldManager:GetNpcByInstance(instanceID)
            local RPCEntity = Game.EntityManager:getEntity(eid)
            if RPCEntity then
				RPCEntity:SetInvisibleByQuestControl(false)
            end
        end
    end
end

function QuestSystem:onVisiblityCheckWithQuestID(state, questID)
    self:collectorVisiblityCheck(state, questID)
    self:npcVisiblityCheckWithQuestID(state, questID)
end

function QuestSystem:onVisiblityCheckWithRingID(state, ringID)
    self:collectorVisiblityCheckByRing(state, ringID)
    self:npcVisiblityCheckWithRingID(state, ringID)
end
---------------------Cinematic---------------------
---@private @Cinematic播放完监听处理相关任务
function QuestSystem:onCinematicFinished(kind, assetID, optionID, npcCfgID, questID, condIdx)
    if kind == Enum.CinematicType.Dialogue then
		local dialogueUniqId = assetID
		if condIdx ~= nil then
			dialogueUniqId = QuestUtils.GetQuestDialogueUniqId(assetID, questID, condIdx)
		end
        if self.model.dialogueFlag[dialogueUniqId] then
            Log.DebugFormat("【Quest】On Dialogue Finished(Kind:%s AssetID:%s, OprionID:%s, NpcID:%s)", kind, assetID, optionID, npcCfgID)
			--local questID = self.model.dialogueFlag[assetID]
			if type(assetID) ~= "table" then
				assetID = {assetID}
			end
			---服务器需要该参数，不能为nil
			condIdx = condIdx or 0
			self.sender:ReqPlayDialogueFinished(questID, npcCfgID or 0, assetID, optionID or -1, condIdx)
		end
        if self.submitCache[assetID] then
            local submitCache = self.submitCache[assetID]
            Log.DebugFormat("【Quest】On Submit Dialogue Finished(Kind:%s AssetID:%s, OprionID:%s, NpcID:%s)", kind, assetID, optionID, npcCfgID)
            self.sender:ReqQuestItemSubmit(submitCache.questID, submitCache.itemSubmitID, submitCache.freeChoice)
            self.submitCache[assetID] = nil
        end
    elseif kind == Enum.CinematicType.Cutscene then
        if self.model.cutsceneFlag[assetID] then
            self.sender:ReqPlayCutSceneFinished(assetID)
        end
    end
end

function QuestSystem:TryPlayCutScene(cutsceneID, planeOrLevelID, questID, condIdx)
    self.questCinematic:TryPlayCutScene(cutsceneID, planeOrLevelID, questID, condIdx)
end

function QuestSystem:StopTryPlayCutScene(cutsceneID, planeOrLevelID)
    self.questCinematic:StopTryPlayCutScene(cutsceneID, planeOrLevelID)
end

function QuestSystem:TryPlayDialogue(dialogueID, planeOrLevelID, questID, condIdx)
    self.questCinematic:TryPlayDialogue(dialogueID, planeOrLevelID, questID, condIdx)
end

function QuestSystem:StopTryPlayDialogue(dialogueID, planeOrLevelID)
    self.questCinematic:StopTryPlayDialogue(dialogueID, planeOrLevelID)
end
-----------------------QuestState-----------------------
---@private 任务失败
---@param questID number @任务ID
function QuestSystem:onQuestFailed(questID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_QUEST_FAILED, questID)
	-- 需要弹出失败界面，提醒继续
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.TASK_FAILURE_AGAIN, function()
		self:ReqContinueFailedQuest(questID)
	end)
end

---@private 任务放弃
---@param questID number @任务ID
function QuestSystem:onQuestAbandoned(questID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_QUEST_ABANDONED, questID)
end

---@private 任务完成
---@param questID number @任务ID
function QuestSystem:onQuestAccomplished(questID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_QUEST_ACCOMPLISHED, questID)
end

---@private 任务结束
---@param questID number @任务ID
function QuestSystem:onQuestFinished(questID)
    self:onVisiblityCheckWithQuestID(self.HideConditionType.TaskCom, questID)
	Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_QUEST_FINISHED, questID)
    Game.CommonInteractorManager:TriggerCommonInteractorEvent(
        Enum.CommonInteractorEventType.COMPLETE_TASK, questID)
end

---@private 开启新任务
---@param questID number @任务ID
function QuestSystem:onNewQuest(questID)
    self:onVisiblityCheckWithQuestID(self.HideConditionType.TaskAccept, questID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TASK_ON_NEW_TASK, self:GetRingIDByQuestID(questID))
end

---@private 开启新环
---@param ringID number @环ID
function QuestSystem:onNewRing(ringID)
    self:onVisiblityCheckWithRingID(self.HideConditionType.TaskRingAccept, ringID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TASK_ON_NEW_TASK, ringID)
end

---@private 环任务结束
---@param ringID number @环ID
function QuestSystem:onRinginished(ringID, lastQuestId)
    self:onVisiblityCheckWithRingID(self.HideConditionType.TaskRingCom, ringID)
	QuestUtils.TryOpenRewardPanelOnRingFinished(ringID, lastQuestId)
end
----------------------Event---------------------------
---关闭书信
---@param letterID number @书信ID
function QuestSystem:OnLetterClose(letterID)
    if self.model.letterFlag[letterID] then
        return self:ReqLetterClose(letterID)
    end
end

---Dialogue播放开始
---@param assetID number @DialogueID
function QuestSystem:OnDialogueStart(assetID, npcEntityID)
    if Game.me then
        Game.me:ReqOnDialogBegin(assetID, npcEntityID)
    end
end

function QuestSystem:OnDialoguePlotShotStart(assetID, npcEntityID)
    if Game.me then
        Game.me:ReqOnDialogBegin(assetID, npcEntityID)
    end
end

function QuestSystem:OnDialogueEnd(assetID, optionTalkID, npcCfgID, npcIntID, questID, condIdx)
    self:Action_OnDialoguePlotEnd(assetID, questID, condIdx)
    if Game.me then
        Game.me:ReqLeaveNpcTalk(npcCfgID, nil, npcIntID)
    end
    self:OnDialoguePlotEnd(assetID, nil, optionTalkID, npcCfgID, questID, condIdx)
end

---Dialogue播放完成
---@param assetID number @DialogueID
---@param episodeID number @情节ID
---@param optionID number @选项ID
---@param npcCfgID number @NPC配置ID
function QuestSystem:OnDialoguePlotEnd(assetID, episodeID, optionID, npcCfgID, questID, condIdx)
    self:onCinematicFinished(Enum.CinematicType.Dialogue, assetID, optionID, npcCfgID, questID, condIdx)
	if Game.me then
		Game.me:ReqOnDialogEnd(assetID, optionID)
	end
end

function QuestSystem:OnDialoguePlotShotEnd(assetID, episodeID, optionTalkID)
	if not Game.me then
		return
	end

	Game.me:ReqOnDialogEnd(assetID, optionTalkID)
end

---Cinematic播放完成
---@param kind number @Enum.CinematicType枚举
---@param assetID number @DialogueAssetDataID
---@param episodeID number @情节ID
function QuestSystem:OnCinematicEnd(kind, assetID, episodeID)
    --cutscene走这个，dialogue走另外的回调
    if kind == Enum.CinematicType.Cutscene then
        return self:onCinematicFinished(kind, assetID)
    end
end

---星座解谜完成
---@param interactorInsID number @所关联的交互物ID
---@param starMisteryID number @星座解谜ID
function QuestSystem:OnMysteryComplete(interactorInsID, starMisteryID)
    -- 这里限制了，会导致流程图对应的监听节点无法监听到
    -- if self.model.misteryFlag[starMisteryID] then
    -- end
	self.model:TryClearClientActionFlag(QuestUtils.ClientActionType.Mistery, starMisteryID)
    return self.sender:ReqStartMisterySuccess(starMisteryID)
end

---界面打开
---@param pageUID string @界面类名
function QuestSystem:OnUIOpen(pageUID)
    if self.model.jumpFlag[pageUID] then
        return self.sender:ReqOpenUIJump(self.model.jumpFlag[pageUID])
    end
end

---拼图解谜完成
---@param puzzleID number @拼图解谜ID
function QuestSystem:OnJigsawComplete(puzzleID)
    return self.sender:ReqStartJigsawPuzzleSuccess(puzzleID)
end

---NPC闲话完成
---@param talkID number @闲话ID
---@param npcCfgID number @NPC配置ID
function QuestSystem:OnNPCTalkFinish(talkID, npcCfgID)
    if self.model.talkFlag[talkID] then
        return self.sender:ReqQuestTalk(self.model.talkFlag[talkID], npcCfgID, talkID)
    end
end

function QuestSystem:onQuestInteractorConditionUpdate(insTemplateID, bIsActive)
    if self.interactConditionRegister[insTemplateID] then
        if bIsActive then
            Game.TriggerTransitSystem:ClientTriggerUpdate(Enum.ETriggerTypeData.INTERACTOR_TASK_ACTIVE,{insTemplateID})
        else
            Game.TriggerTransitSystem:ClientTriggerUpdate(Enum.ETriggerTypeData.INTERACTOR_TASK_ACTIVE,{insTemplateID})
        end
    end
end

function QuestSystem:CheckInteractorCondition(insTemplateID, bNeedRegister)
    if bNeedRegister then
        self.interactConditionRegister[insTemplateID] = true
    end
    local questID = self.model.interactorConditionFlag[insTemplateID]
    if questID then
        return true
    end
    return false
end

function QuestSystem:UnregisterInteractorCondition(insTemplateID)
    self.interactConditionRegister[insTemplateID] = nil
end

function QuestSystem:OnUseItem(itemId, number)
    self:tryOpenUsedItemTaskTipsPanel(itemId)
end

function QuestSystem:tryOpenUsedItemTaskTipsPanel(itemId)
	local itemNewData = Game.TableData.GetItemNewDataRow(itemId)
	if itemNewData.UseSkillTime and itemNewData.UseSkillTime > 0 then
		return
	end

	local ringID = self.model.receiveItemMap[itemId]
	if ringID then
		Game.NewUIManager:OpenPanel("TaskTipsPanel", ringID, nil, nil, itemId)
	end
end

function QuestSystem:RetUseReceiveQuestItem(result, itemId)
	if result.Code == Game.ErrorCodeConst.NO_ERR then
		self:tryOpenUsedItemTaskTipsPanel(itemId)
	end
end

---@public 任务道具使用
---@param itemID number @任务道具ID
---@param bIsTry boolean @是否只是尝试使用
function QuestSystem:OnUseQuestItem(itemID, bIsTry)
    --- 如果是任务专属使用道具或者任务目标绑定的可使用任务道具，走任务专属使用RPC -- TODO: 17与22为两种任务道具类型，后续在常量表标识
    local ringID = self.model.receiveItemMap[itemID]
    if ringID then
        if bIsTry then
            return true
        end
        --领取任务用，需要阻塞其他使用
        --Game.BagSystem:ReqQuickUse(itemID, true)
		-- 直接换一个rpc，一开始不会真正扣除道具
		Game.me:ReqReceiveItemQuest(ringID, itemID)
        -- Game.NewUIManager:OpenPanel("TaskTipsPanel", ringID, nil, nil, itemID)
        return false
    end

    ---@任务不可使用道具（任务激活后可用且弹出任务快捷按键） 17 
    local useItemMap = self.model.useItemMap[itemID]
    if useItemMap and #useItemMap > 0 then
        local questID = useItemMap[1].QuestID
        local result = self:tryUseQuestItem(questID, itemID, bIsTry)
        if result ~= nil then
            return result
        end
    end

	local itemExcelData = Game.TableData.GetItemNewDataRow(itemID)
    if not bIsTry then
        if itemExcelData.operationGroupType == 22 then
            ---@任务可使用道具（一直可用，任务激活后弹出任务快捷按键）22
            Game.BagSystem:ReqQuickUse(itemID, true)
        end
	else
		if itemExcelData.operationGroupType == 17 then
			return false
		end
    end

    return true
end

function QuestSystem:AddInsUseItenListen(insID, radius, itemID)
    self.questUseItem:AddInsUseItenListen(insID, radius, itemID)
end

function QuestSystem:RemoveInsUseItenListen(insID, itemID)
    self.questUseItem:RemoveInsUseItenListen(insID, itemID)
end

function QuestSystem:AddQuickUseItemListen(mapID, position, radius, itemID)
    self.questUseItem:AddQuickUseItemListen(mapID, position, radius, itemID)
end

function QuestSystem:RemoveQuickUseItemListen(mapID, itemID, position)
    self.questUseItem:RemoveQuickUseItemListen(mapID, itemID, position)
end

function QuestSystem:AddLocalPlanePortal(questID, conditionIndex, mapID, insID, position, radius, bShowEffect)
    local currentLevelID = Game.LevelManager.GetCurrentLevelID()
    local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()
    local levelID, planeID = self:getPlaneAndMapID(mapID, 0)
    if not self.portalMap[mapID] then
        self.portalMap[mapID] = {}
    end
    if not self.portalMap[mapID][questID] then
        self.portalMap[mapID][questID] = {}
    end
    self.portalMap[mapID][questID][conditionIndex] = {
        insID = insID,
        position = position,
        radius = radius,
        questID = questID
    }

    if levelID == currentLevelID and planeID == currentPlaneID then
        if position then
            self:addPosLocalPlanePortal(questID, conditionIndex, position, radius, bShowEffect)
        else
            self:addInsLocalPlanePortal(questID, conditionIndex, insID, radius, bShowEffect)
        end
    end
end

function QuestSystem:RemoveLocalPlanePortal(questID, conditionIndex, mapID)
    local portalMap = self.portalMap[mapID]
    if portalMap then
        portalMap = portalMap[questID]
    end
    if portalMap then
        portalMap[conditionIndex] = nil
        if not next(portalMap) then
            self.portalMap[mapID][questID] = nil
            if not next(self.portalMap[mapID]) then
                self.portalMap[mapID] = nil
            end
        end
    end

    for insID, map in pairs(self.localPlanePortalMap) do
        if questID == map.questID and conditionIndex == map.conditionIndex then
            Game.WorldManager:RemoveLocalPlanePortal(insID)
            self.temp[#self.temp+1] = insID
        end
    end

    for i = 1, #self.temp do
        self.localPlanePortalMap[self.temp[i]] = nil
    end
    table.clear(self.temp)
end
-----------------------PathFollow自动寻路相关-----------------------
---@private 当玩家输入移动,打断跟随npc寻路状态
function QuestSystem:onRoleMoveInput(Pawn, value, Axis)
    --TODO：缺少特征判断
    -- if Game.me then
    --     Game.me:StopPathFollowTargetWithRelativeLoc()
    -- end
end

---@public 任务监听NPC初始化
---@param npcCfgID number @npcID
function QuestSystem:OnNpcInitTrigger(insID, entityUID, npcCfgID)
    self.questUseItem:OnNpcInitTrigger(insID, entityUID, npcCfgID)
end

function QuestSystem:OnSkillRouttleBattlePanelOpen()
    --卜杖寻路
    if next(self.stickNavigatorMap) then
        self:StartTimer("TryStickNavigator", function ()
            local stickNavigatorMap = {}
            for uid, map in pairs(self.stickNavigatorMap) do
                local uid = Game.me:AddAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, map.params)
                stickNavigatorMap[uid] = map
            end
            self.stickNavigatorMap = stickNavigatorMap
        end, 1, 1)
    end
end

--卜杖寻路结束
function QuestSystem:onAdditionSkillFinish(uid)
    local stickNavigatorMap = self.stickNavigatorMap[uid]
    if stickNavigatorMap then
        self.stickNavigatorMap[uid] = nil
        self.sender:ReqFinishedCaneSkill(stickNavigatorMap.questID, stickNavigatorMap.conditionIndex)
    end
end
---------------------------NetEvent---------------------------
---@param questList table<number,QuestInfo> @剧情总任务数据
function QuestSystem:OnMsgQuestListInfo(questList)
    return self.model:OnMsgQuestListInfo(questList)
end

---@param questList table<number,QuestInfo> @系统总任务数据
function QuestSystem:OnMsgOtherQuestListInfo(questList)
    return self.model:OnMsgOtherQuestListInfo(questList)
end

---@param questList table<number,QUEST_FINISHED_INFO> @已完成的任务数据
function QuestSystem:OnMsgFinishedQuestList(QuestFinishedInfoDict)
    return self.model:OnMsgFinishedQuestList(QuestFinishedInfoDict)
end

---@param questList table<number,QuestInfo> @已放弃的任务数据
function QuestSystem:OnMsgAbandonQuestList(questList)
    return self.model:OnMsgAbandonQuestList(questList)
end

---@param ringList number[] @环数组
function QuestSystem:OnMsgQuestActiveList(ringList)
    return self.model:OnMsgQuestActiveList(ringList)
end

---@param ringID number @环ID
function QuestSystem:OnMsgQuestActive(ringID)
    return self.model:OnMsgQuestActive(ringID)
end

---@param ringID number @任务环ID
---@param planeID number @位面ID
function QuestSystem:OnMsgNowRingPlaneChanged(ringID, planeID)
    return self.model:OnMsgNowRingPlaneChanged(ringID, planeID)
end

---@param ringID number @任务环ID
---@param oldStatus number @旧任务状态
---@param newStatus number @新任务状态
function QuestSystem:OnMsgQuestInfoUpdateStatus(ringID, oldStatus, newStatus)
    return self.model:OnMsgQuestInfoUpdateStatus(ringID, oldStatus, newStatus)
end

---@param questInfo QuestInfo @任务数据结构
function QuestSystem:OnMsgQuestInfoUpdate(questInfo)
    local ret = self.model:OnMsgQuestInfoUpdate(questInfo)
	self:checkQuestMeetCheckPoint(questInfo)
	return ret
end

---检查一下是否存档点
---@param questInfo QuestInfo @任务数据结构
function QuestSystem:checkQuestMeetCheckPoint(questInfo)
	---需要是第一次接取的时候才做表现
	if questInfo.RingStatus ~= TaskStatusConst.TASK_STATUS__ACCEPTED then return end
	local questId = questInfo.NowRingQuestID
	if QuestUtils.CheckQuestSavePoint(questId) then
		UI.Invoke("HUD_Task_Panel", "OnQuestMeetCheckPoint", questId)
	end
end

---@param ringID number @任务环ID
---@param targetIndex number @任务目标下标
---@param count number @任务目标完成数量
---@param bFinished boolean @任务目标是否已完成
function QuestSystem:OnMsgQuestTargetChanged(ringID, targetIndex, count, bFinished)
    return self.model:OnMsgQuestTargetChanged(ringID, targetIndex, count, bFinished)
end

---@param questID number @任务ID
---@param mainIndex number @主任务目标
function QuestSystem:OnMsgChangeTaskStepDescription(questID, mainIndex)
    return self.model:OnMsgChangeTaskStepDescription(questID, mainIndex)
end
-----------------NPC对话请求RPC-----------------
---@param result Result @回包
function QuestSystem:RetQuestTalk(result)
    Game.NetworkManager.ShowNetWorkResultReminder("RetQuestTalk",result)
end
-----------------放弃任务RPC-----------------
---@param ringID number @环ID
function QuestSystem:ReqQuestAbandon(ringID)
    self.sender:ReqQuestAbandon(ringID)
end

---@param result Result @RPC回包结果
function QuestSystem:RetQuestAbandon(result)
    Game.NetworkManager.ShowNetWorkResultReminder("RetQuestAbandon",result)
end
-----------------回到位面RPC-----------------
---@param questID number @任务ID
function QuestSystem:ReqQuestOpenPlane(questID)
    self.sender:ReqQuestOpenPlane(questID)
end

---@param result Result @RPC回包结果
---@param questID number @任务ID
function QuestSystem:RetQuestOpenPlane(result, questID)
    Game.NetworkManager.ShowNetWorkResultReminder("RetQuestOpenPlane",result)
end
-----------------离开位面RPC-----------------
function QuestSystem:ReqQuestLeavePlane()
    self.sender:ReqQuestLeavePlane()
end

---@param result Result @RPC回包结果
function QuestSystem:RetQuestLeavePlane(result)
    Game.NetworkManager.ShowNetWorkResultReminder("RetQuestLeavePlane",result)
end
-----------------手动提交任务RPC-----------------
function QuestSystem:ReqQuestManualSubmit(questID)
    self.sender:ReqQuestLeavePlane(questID)
end

---@param result Result @RPC回包结果
---@param questID number @任务ID
function QuestSystem:RetQuestManualSubmit(result, questID)
    Game.NetworkManager.ShowNetWorkResultReminder("RetQuestManualSubmit",result)
end
-----------------手动领取任务RPC-----------------
---@param ringID number @环ID
---@param type number @Const.RECEIVE_TASK_TYPE枚举
function QuestSystem:ReqReceiveQuest(ringID, type)
    self.sender:ReqReceiveQuest(ringID, type)
end

---@param result Result @RPC回包结果
---@param questID number @任务ID
function QuestSystem:RetReceiveQuest(result, questID)
    Game.NetworkManager.ShowNetWorkResultReminder("RetReceiveQuest",result)
    if result.Code == _G.Game.ErrorCodeConst.NO_ERR then
        Game.NewUIManager:ClosePanel("TaskBoardPanel")
        Game.CommonInteractorManager:TriggerCommonInteractorEvent(
            Enum.CommonInteractorEventType.ACCEPT_TASK, questID)
    end
end
-----------------对话领取任务RPC-----------------
---@param ringID number @环ID
---@param npcCfgID number @NPC配置ID
---@param DialogID number @对话ID
function QuestSystem:ReqReceiveNpcTalkQuest(ringID, npcCfgID, DialogID)
    self.sender:ReqReceiveNpcTalkQuest(ringID, npcCfgID, DialogID)
end

---@param result Result @RPC回包结果
---@param ringID number @环ID
function QuestSystem:RetReceiveNpcTalkQuest(result, ringID)
    Game.NetworkManager.ShowNetWorkResultReminder("RetReceiveNpcTalkQuest",result)
    Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_RECEIVE_NPCTALK_QUEST, ringID)
end
-----------------对话提交任务RPC-----------------
---@param ringID number @环ID
---@param npcCfgID number @NPC配置ID
---@param DialogID number @对话ID
function QuestSystem:ReqSubmitNpcTalkQuest(ringID, npcCfgID, DialogID)
    self.sender:ReqSubmitNpcTalkQuest(ringID, npcCfgID, DialogID)
end

---@param result Result @RPC回包结果
---@param ringID number @环ID
function QuestSystem:RetSubmitNpcTalkQuest(result, ringID)
    Game.NetworkManager.ShowNetWorkResultReminder("RetSubmitNpcTalkQuest",result)
end
-----------------失败任务领取RPC-----------------
---@param questID number @任务ID
function QuestSystem:ReqContinueFailedQuest(questID)
    self.sender:ReqContinueFailedQuest(questID)
end
-----------------道具领取任务RPC-----------------
---@param ringID number @环ID
---@param itemID number @道具ID
function QuestSystem:ReqReceiveItemQuest(ringID, itemID)
    self.sender:ReqReceiveItemQuest(ringID, itemID)
end

function QuestSystem:RetReceiveItemQuest(result, ringID)
    Game.NetworkManager.ShowNetWorkResultReminder("RetReceiveItemQuest",result)
    Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_RECEIVE_NPCTALK_QUEST, ringID)
end
-----------------任务失败结束手动执行RPC-----------------
---@param questID number @任务ID
function QuestSystem:ReqQuestTimeOut(ringID)
    local questID = self:GetQuestIDByRingID(ringID)
    if not questID then
        Log.WarningFormat("【Quest】Error QuestID not found, RingID:%s", ringID)
        return
    end

    self.sender:ReqQuestTimeOut(questID)
end
-----------------任务道具提交RPC-----------------
---@param questID number @任务ID
---@param itemSubmitID number @提交道具ID
---@param freeChoice table @the submitted item ordered
function QuestSystem:ReqQuestItemSubmit(questID, itemSubmitID, freeChoice, conditionIndex)
	conditionIndex = conditionIndex or 1
	local itemSubmitUniqID = QuestUtils.GetQuestSubmitUniqId(questID, conditionIndex)
	local dialogueID = self.model.submitDialogueMap[itemSubmitUniqID]
    if dialogueID then
        Game.NPCSystem:OnItemSubmit()
        Game.ItemSubmitSystem:HideItemSubmitUI()
        --需要在剧情对话后发起真正道具提交
        Game.CinematicManager:StartPlayCinematic({
            CinematicType = Enum.CinematicType.Dialogue,
            AssetID = dialogueID,
			QuestID = questID,
			CondIdx = conditionIndex
        })
        if not self.submitCache[dialogueID] then
            self.submitCache[dialogueID] = {
                questID = questID,
                itemSubmitID = itemSubmitID,
                freeChoice = freeChoice,
            }
        end
    else
        return self.sender:ReqQuestItemSubmit(questID, itemSubmitID, freeChoice)
    end
end

---播放完毕后如果有对话，发起对话
function QuestSystem:AfterQuestItemSubmit(questID, conditionIndex)
	local itemSubmitID = QuestUtils.GetQuestSubmitUniqId(questID, conditionIndex)
	local dialogueID = self.model.submitDialogueMap[itemSubmitID]
	if dialogueID then
		Game.CinematicManager:StartPlayCinematic({
			CinematicType = Enum.CinematicType.Dialogue,
			AssetID = dialogueID,
			QuestID = questID,
			CondIdx = conditionIndex
		})
	end
end

---@param result Result @RPC回包结果
---@param submitID number @道具提交表ID
function QuestSystem:RetQuestItemSubmit(result, submitID)
    if result.Code == _G.Game.ErrorCodeConst.NO_ERR then
        Game.NPCSystem:OnItemSubmit()
        Game.ItemSubmitSystem:HideItemSubmitUI()
    end
    Game.NetworkManager.ShowNetWorkResultReminder("RetQuestItemSubmit",result)
end
-----------------任务使用道具-----------------
function QuestSystem:ReqUseQuestItemByTarget(itemID, questID, targetIndex)
    self.sender:ReqUseQuestItemByTarget(itemID, questID, targetIndex)
end

function QuestSystem:RetUseQuestItemByTarget(result)
    Game.NetworkManager.ShowNetWorkResultReminder("RetUseQuestItemByTarget",result)
end
-----------------【客户端传送门相关】-----------------
---@param questID number @任务ID
---@param targetIndex number @目标ID
function QuestSystem:ReqClientPlanePortalEnterPlane(questID, targetIndex)
    self.sender.ReqClientPlanePortalEnterPlane(questID, targetIndex)
end

function QuestSystem:RetClientPlanePortalEnterPlane(result, questID, targetIndex)
    Game.NetworkManager.ShowNetWorkResultReminder("RetClientPlanePortalEnterPlane",result)
end

function QuestSystem:GetQuestList()
	return self.model.questList
end

-- 外部直接调用星图解谜UI关闭
function QuestSystem:OnStarMysteryClose(starMisteryID)
	-- 后续TaskManager会换成System，通过sender来发，不用此额外判断
	if starMisteryID and Game.me then
		Game.me:OnStarMisteryClose(starMisteryID)
	end
end

function QuestSystem:GetTaskNPCTalkPriority(TaskRingID)
	local questId = self:GetQuestIDByRingID(TaskRingID)
	questId = questId or 0
	return self:getTalkPriority(questId)
end

function QuestSystem:IsDialogueControl()
	return self.model.inDialogueControlState
end

function QuestSystem:CancelDialogueControl()
	--- 如果在对话，则退出对话
	---@type NPCTalkPanel
	local npcTalkUI = UI.GetUI("P_NPCTalk")
	if npcTalkUI then
		npcTalkUI:endTalk()
	end
	Game.ItemSubmitSystem:HideItemSubmitUI()
	--todo 这里还需要处理一下dialogue
end

function QuestSystem:EnterDialogueControlState()
	self.model.inDialogueControlState = true
	if Game.me then
		Game.me:SCExecute(Enum.EStateConflictAction.DialogControl)	
	end
end

function QuestSystem:LeaveDialogueControlState()
	self.model.inDialogueControlState = false
	if Game.me then
		Game.me:SCRemove(Enum.EStateConflictAction.DialogControl)	
	end
end

function QuestSystem:CanEnterDialogueControlState()
	if Game.me then
		return Game.me:SCCanExecute(Enum.EStateConflictAction.DialogControl, true) ~= Enum.EStateConflictType.BLOCK
	else
		return false
	end
end

------- Navigation Begin -------
function QuestSystem:TryAutoTrace(traceParam, questID)
	return self.questNavigation:TryAutoTrace(traceParam, questID)
end

function QuestSystem:OnNavigateNPCBorn(insID)
	self.questNavigation:OnNavigateNPCBorn(insID)
end
------- Navigation End -------

---交互道具任务目标接dialogue的类型
QuestSystem.PlayDialogueEnum = {
	Before = 1, --交付前播
	After = 2   --交付后播
}

---提示类型
QuestSystem.ReminderEnum = {
	Start = 1,    -- 开始
	End = 2,      -- 结束
}

return QuestSystem























