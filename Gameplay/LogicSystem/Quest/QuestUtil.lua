local QuestUtils = {}
local Const = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local TriggerUtils = kg_require("Shared.Utils.TriggerUtils")
local C7FunctionLibrary = import("C7FunctionLibrary")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local EDrawDebugTrace = import("EDrawDebugTrace")
local EPropertyClass = import("EPropertyClass")
local AActor = import("Actor")

---地图是否使用新的tag
QuestUtils.MapUseNewTag = false

---任务数据使用新序列化结构
QuestUtils.UseNewLuaSerializer = false

---任务数据使用新的CommonCondition
QuestUtils.UseCommonCondition = true

function QuestUtils.GetQuestSubmitUniqId(questId, condIdx)
	return string.format("%d__%d", questId, condIdx)
end

function QuestUtils.GetQuestIdAndCondIdxBySubmitUniqId(uniqId)
	local questId, condIdx = uniqId:match("^(%d+)__(%d+)$")
	return tonumber(questId), tonumber(condIdx)
end

QuestUtils.DialogueListUniqIdPrefix = "@DialogueList"
function QuestUtils.GetQuestDialogueUniqId(dialogueId, questId, condIdx)
	if type(dialogueId) == "table" then
		local res = ""
		for _, singleId in ipairs(dialogueId) do
			res = string.format("%s%d__", res, singleId)
		end
		return string.format("%s%s__%d__%d", res, QuestUtils.DialogueListUniqIdPrefix, questId, condIdx)
	end
	return string.format("%d__%d__%d", dialogueId, questId, condIdx)
end

function QuestUtils.CheckDialogueListUniqId(uniqId)
	return string.find(uniqId, QuestUtils.DialogueListUniqIdPrefix) ~= nil
end

function QuestUtils.GetDialogueQuestAndCondIdxByDialogueUniqId(uniqId)
	if type(uniqId) == "number" then
		return uniqId, nil, nil
	end
	if QuestUtils.CheckDialogueListUniqId(uniqId) then
		local dialogueList = {}
		local questId, condIdx
		-- 先按 __DialogueList__ 分成两部分
		local left, right = uniqId:match("^(.-)__@DialogueList__(.*)$")
		-- 确保以 __ 结尾，这样最后一段也能匹配
		left = string.format("%s__", left)
		for part in string.gmatch(left, "(.-)__") do
			table.insert(dialogueList, tonumber(part))
		end
		questId, condIdx = right:match("^(%d+)__(%d+)$")
		return dialogueList, tonumber(questId), tonumber(condIdx)
	end
	local dialogueId, questId, condIdx = uniqId:match("^(%d+)__(%d+)__(%d+)$")
	return tonumber(dialogueId), tonumber(questId), tonumber(condIdx)
end

---根据提交的唯一Id以及分支的Idx，来获取提交道具的数据
function QuestUtils.GetSubmitItemDataBySubmitUniqId(uniqId)
	local cfg = Game.QuestSystem:GetQuestConditionCfgBySubmitID(uniqId)
	local cfgType = cfg.Type
	if cfgType == Enum.ETaskConstData.TARGET_TYPE__ITEM_SUBMIT then
		-- 旧的提交道具类型，则返回ItemSubmitID
		return cfg.ItemSubmitID, nil
	elseif cfgType == Enum.ETaskConstData.TARGET_TYPE__ITEM_SUBMIT_DIRECT or cfgType == Enum.ETaskConstData.TARGET_TYPE__ITEM_SUBMIT_BRANCH then
		return nil, cfg
	end
end

---获取任务失败的的倒计时
----1表示任务没有失败倒计时条件或者没有开始
function QuestUtils.GetQuestFailureCountDown(ringID, questID)
	local QuestSystem = Game.QuestSystem
	local questCfg = QuestSystem:GetTaskExcelCfg(questID)
	local failConditions = questCfg.FailConditions
	if failConditions then
		-- 这里编辑器的数据结构改变了，这里也需要做相应的调整
		local info = failConditions
		if info.Type == Enum.ETaskConstData.TASK_FAIL_TYPE__QUEST_TIMEOUT then
			local taskInfo = QuestSystem:GetQuestInfo(ringID)
			local endTime = 0
			local startAt = taskInfo.RingStartAt
			if taskInfo and startAt > 0 then
				endTime = startAt + (info.Time or 0) * 1000
				return (endTime - _G._now()) / 1000
			end
		end
	end
	return -1
end

function QuestUtils.GetTimeCDFormatStr(cdTime)
	local day = math.floor(cdTime / 86400)
	cdTime = cdTime % 86400
	local hour = math.floor(cdTime / 3600)
	cdTime = cdTime % 3600
	local minute = math.floor(cdTime / 60)
	cdTime = cdTime % 60
	local second = cdTime
	if day > 0 then
		return string.format("%02d%s%02d%s", day, StringConst.Get("DAY"), hour, StringConst.Get("HOUR"))
	elseif hour > 0 then
		return string.format("%02d%s%02d%s", hour, StringConst.Get("HOUR"), minute, StringConst.Get("MINUTE"))
	elseif minute > 0 then
		return string.format("%02d%s%02d%s", minute, StringConst.Get("MINUTE"), second, StringConst.Get("SECOND"))
	else
		return string.format("%02d%s", second, StringConst.Get("SECOND"))
	end
end

---任务状态在侧边栏的显示
QuestUtils.QuestStateInSideBarUI = {
	Normal = 0,
	Saving = 1,
	Saved = 2,
}

---判断是否存档点
function QuestUtils.CheckQuestSavePoint(questId)
	local questCfgData = Game.QuestSystem:GetTaskExcelCfg(questId)
	return questCfgData.FallbackSetting == 1
end

---@public 是否有当前位面正在进行中的护送任务
function QuestUtils.HasEscortTask()
	local planeID = Game.MapSystem:GetCurrentPlaneID()
	if planeID == 0 then
		return false
	end
	local questInfo = Game.QuestSystem:GetQuestList()
	for _, data in pairs(questInfo) do
		if data.RingStatus == Enum.ETaskConstData.TASK_STATUS__ACCEPTED then
			--任务进行中
			if data.NowPlaneID == planeID then
				-- 存档点了
				if data.SavePointQuestID and data.SavePointQuestID > 0 then
					return true
				end
			end
		end
	end
	return false
end

function QuestUtils.CheckTracingRingID(ringID)
	if ringID == nil or ringID == 0 then return false end
	local TraceSystem = Game.TraceSystem
	local tracingRingID = TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	if ringID == tracingRingID then return true end
	tracingRingID = TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
	return tracingRingID == ringID
end

function QuestUtils.GetQuestGroupByType(questType)
	for groupId, groupData in ksbcpairs(Game.TableData.GetTaskFilterDataTable()) do
		if groupId > 1 then
			for _, miniType in ksbcpairs(groupData.Filter) do
				if miniType == questType then
					return groupId
				end
			end
		end
	end
	Log.ErrorFormat("QuestUtils.GetQuestGroupByType questType:%d not found in  TaskFilterDataTable", questType)
end

function QuestUtils.CheckTalkIdSegment(id)
	return id >= 9000000 and id <= 9099999
end

--- 任务目标数显示模式
QuestUtils.ETargetNumShowMode = {
	HideAll = 0,
	ShowNumOnly = 1,
	ShowPBOnly = 2,
	ShowAll = 3,
}

---客户端action类型
QuestUtils.ClientActionType = {
	Letter = 1,
	Mistery = 2,
	JigsawPuzzle = 3,
	Qte = 4,
	PaintingStratch = 5,
}

function QuestUtils.CheckShowTargetNum(mode)
	return mode == QuestUtils.ETargetNumShowMode.ShowAll or mode == QuestUtils.ETargetNumShowMode.ShowNumOnly
end

function QuestUtils.CheckShowTargetProgressBar(mode)
	return mode == QuestUtils.ETargetNumShowMode.ShowAll or mode == QuestUtils.ETargetNumShowMode.ShowPBOnly
end

function QuestUtils.TransformRewardDataToShow(rewardData)
	local ret = {}
	for _, rewardInfo in ipairs(rewardData) do
		ret[#ret + 1] = {
			id = rewardInfo.ItemID,
			num = rewardInfo.Count,
		}
	end
	return ret
end

QuestUtils.DefaultTestMainChapterPerformId = 6460000

---环任务结束显示奖励界面的类型
QuestUtils.RingFinishShowRewardType = {
	Normal = 1,
	MainChapter = 2,
}

function QuestUtils.TryOpenRewardPanelOnRingFinished(ringId, questId)
	local ringData = Game.QuestSystem:GetRingExcelCfg(ringId)
	if ringData.bShowReward then
		local showRewardType = ringData.RewardType.Type
		if showRewardType == QuestUtils.RingFinishShowRewardType.Normal then
			QuestUtils.OpenNormalRewardPanel(questId)
		elseif showRewardType == QuestUtils.RingFinishShowRewardType.MainChapter then
			QuestUtils.OpenMainChapterStartPanel(ringData.RewardType.ChapterId, questId)
		end
	end
end

function QuestUtils.OpenNormalRewardPanel(questId)
	local rewardData = Game.QuestSystem:GetRewardByQuestID(questId)
	local rewardListData = {}
	for _, info in pairs(rewardData) do
		local itemId, count = info.ItemID, info.Count
		if itemId then
			---@type ItemRewardNewParam
			local data = {
				itemId = itemId,
				count = count,
			}
			rewardListData[#rewardListData + 1] = data
		end
	end
	Game.NewUIManager:OpenPanel("GetItemsPopup_Panel", {
		items = { comItems = rewardListData},
		closeCallback= function() 
			-- 通知服务端
		end
	})
end

function QuestUtils.OpenMainChapterStartPanel(chapterId, questId)
	Game.NewUIManager:OpenPanel("MainChapterStartPerform_Panel", chapterId, questId)
end

function QuestUtils.OpenMainChapterEndPanel(chapterId, questId)
	Game.NewUIManager:OpenPanel("MainChapterEndPerform_Panel", chapterId, questId)
end

---黑屏字幕里的背景类型
QuestUtils.PlayBlackBGType = {
	Black = 1,
	MimeWhite = 2
}

function QuestUtils.OpenBlackOrWhiteBgPanel(bgType, contentList, endCallback)
	bgType = bgType or QuestUtils.PlayBlackBGType.Black
	if bgType == QuestUtils.PlayBlackBGType.Black then
		Game.NewUIManager:OpenPanel("BlackContent_Panel", contentList, endCallback)
	elseif bgType == QuestUtils.PlayBlackBGType.MimeWhite then
		Game.NewUIManager:OpenPanel("MimeWhiteContent_Panel", contentList, endCallback)
	end
end

function QuestUtils.GetRingChapterName(ringId)
	local ringCfg = Game.QuestSystem:GetRingExcelCfg(ringId)
	if ringCfg then
		local chapterCfg = Game.QuestSystem:GetChapterExcelCfg(ringCfg.ChapterID)
		if chapterCfg then
			return chapterCfg.ChapterName 
		end
	end
end

QuestUtils.PosEqualApproximate = 0.001
function QuestUtils.CheckPositionEqual(pos1, pos2)
	return math.abs(pos1.X - pos2.X) < QuestUtils.PosEqualApproximate and 
		math.abs(pos1.Y - pos2.Y) < QuestUtils.PosEqualApproximate and
		math.abs(pos1.Z - pos2.Z) < QuestUtils.PosEqualApproximate
end

---@class QuestTriggerDialogueExtraInfo
---@field questID number
---@field condIdx number

---@return QuestTriggerDialogueExtraInfo
function QuestUtils.GetQuestTriggerDialogueExtraInfo(questID, conditionIndex)
	return {
		questID = questID,
		condIdx = conditionIndex,
	}
end

QuestUtils.NpcTalkerMainPlayer = 0
QuestUtils.NpcTalkerCurNpc = 1

---@param npcTalkData _TalkDataRow
function QuestUtils.CheckNpcTalkerMainPlayer(npcTalkData)
	local npcName = npcTalkData.NpcName
	local talkerNpcId = npcTalkData.TalkerNpcID
	return npcName == "-1" or (npcName == "" and talkerNpcId == QuestUtils.NpcTalkerMainPlayer)
end

---@param npcTalkData _TalkDataRow
function QuestUtils.GetNpcTalkerShowName(npcTalkData, curNpcTemplateId)
	local npcName = npcTalkData.NpcName
	if npcName ~= "-1" and npcName ~= "" then
		return npcTalkData.NpcName
	end
	
	if QuestUtils.CheckNpcTalkerMainPlayer(npcTalkData) then
		return Game.me.Name
	end
	
	local npcTemplateId = curNpcTemplateId
	local talkerNpcId = npcTalkData.TalkerNpcID
	if talkerNpcId ~= QuestUtils.NpcTalkerCurNpc then
		npcTemplateId = talkerNpcId
	end
	local NpcInfo = Game.TableData.GetNpcInfoDataRow(npcTemplateId)
	if NpcInfo then
		return NpcInfo.Name	
	end
	return nil
end

---检查NpcId是否是模板Id，与策划约定了，模板Id是小于8位的，instanceId是大于8位的
function QuestUtils.CheckNpcTemplateId(templateOrInstanceIdStr)
	return #templateOrInstanceIdStr < 8
end

--根据策划填的兼容Id，来获得对应的Entity
---@param id number
---@param curNpcEntId string
function QuestUtils.GetEntityByCompatibleId(id, curNpcEntId)
	if id == QuestUtils.NpcTalkerMainPlayer then
		return Game.me
	elseif id == QuestUtils.NpcTalkerCurNpc then
		return Game.EntityManager:getEntity(curNpcEntId)
	else
		local strId = tostring(id)
		if QuestUtils.CheckNpcTemplateId(strId) then
			return Game.NPCManager.GetTaskNpcEntByTemplateId(id)
		else
			return Game.NPCManager.GetTaskNpcEntByInstanceId(strId)
		end
	end
end

function QuestUtils.ExecuteTalkSystemActionList(systemActionList, debugSystemActionList)
	if systemActionList then
		if Game.me then
			Game.SystemActionManager:TriggerSystemActionList(systemActionList)
		end
	end
	if debugSystemActionList then
		for _, actionItem in ipairs(debugSystemActionList) do
			if Game.me then
				Game.me:TriggerDebugSystemAction(actionItem.FuncName, actionItem.FuncArgInfos)
			end
		end
	end
end

function QuestUtils.CheckCommonConditionMeet(commonCondition)
	if commonCondition == nil or ksbcnext(commonCondition) == nil then return true end
	return TriggerUtils.EvaluateConditionAst(commonCondition, Game.me)
end

QuestUtils.ignoreActorIDs = slua.Array(EPropertyClass.Int64)

function QuestUtils.CheckHasObstacleBetween(startPos, endPos)
	QuestUtils.ignoreActorIDs:Clear()
	QuestUtils.ignoreActorIDs:Add(Game.me.CharacterID) -- 忽略玩家自己

	local hitResult = LuaScriptAPI.KismetSystem_LineTraceSingle(
		Game.me.CharacterID,
		startPos,
		endPos,
		CollisionConst.COLLISION_TRACE_TYPE_BY_NAME.Visibility,
		false,
		QuestUtils.ignoreActorIDs,
		EDrawDebugTrace.None,
		true,
		FLinearColor.Red,
		FLinearColor.Green,
		0
	)
	return hitResult.bResult
end


function QuestUtils.CheckFallFromTargetPos(targetPos)
	local startPos = targetPos
	local endPos = FVector(targetPos.X, targetPos.Y, targetPos.Z - 5000) -- 向下检测1000单位

	QuestUtils.ignoreActorIDs:Clear()
	QuestUtils.ignoreActorIDs:Add(Game.me.CharacterID) -- 忽略玩家自己

	local hitResult = LuaScriptAPI.KismetSystem_LineTraceSingle(
		Game.me.CharacterID,
		startPos,
		endPos,
		CollisionConst.COLLISION_TRACE_TYPE_BY_NAME.Visibility,
		false,
		QuestUtils.ignoreActorIDs,
		EDrawDebugTrace.None,
		true,
		FLinearColor.Red,
		FLinearColor.Green,
		0
	)
	local bResult = hitResult.bResult
	return not bResult
end

return QuestUtils
