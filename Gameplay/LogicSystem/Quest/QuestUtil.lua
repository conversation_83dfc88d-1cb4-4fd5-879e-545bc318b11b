local QuestUtils = {}
local Const = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

---地图是否使用新的tag
QuestUtils.MapUseNewTag = false

function QuestUtils.GetQuestSubmitUniqId(questId, condIdx)
	return string.format("%d__%d", questId, condIdx)
end

function QuestUtils.GetQuestIdAndCondIdxBySubmitUniqId(uniqId)
	local questId, condIdx = uniqId:match("^(%d+)__(%d+)$")
	return tonumber(questId), tonumber(condIdx)
end

---根据提交的唯一Id以及分支的Idx，来获取提交道具的数据
function QuestUtils.GetSubmitItemDataBySubmitUniqId(uniqId)
	local cfg = Game.QuestSystem:GetQuestConditionCfgBySubmitID(uniqId)
	local cfgType = cfg.Type
	if cfgType == Enum.ETaskConstData.TARGET_TYPE__ITEM_SUBMIT then
		-- 旧的提交道具类型，则返回ItemSubmitID
		return cfg.ItemSubmitID, nil
	elseif cfgType == Enum.ETaskConstData.TARGET_TYPE__ITEM_SUBMIT_DIRECT or cfgType == Enum.ETaskConstData.TARGET_TYPE__ITEM_SUBMIT_BRANCH then
		return nil, cfg
	end
end

---@return boolean 是否能够自动寻路
function QuestUtils.TryAutoTrace(traceParam, questID)
	if not traceParam then
		Log.Debug("QuestUtils tryAutoTrace traceParam is nil")
		return false
	end
	if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_AUTONAVIGATION") then
		if traceParam.WorldID <= 0 then
			Log.ErrorFormat("HUDQuest Error Trace WorldID(QuestID:%s)", questID)
			return false
		elseif traceParam.Pos then
			Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.Task, traceParam.WorldID, traceParam.Pos)
		else
			if traceParam.TraceTaskNPCIDs and #traceParam.TraceTaskNPCIDs > 0 then
				local npcInsID = traceParam.TraceTaskNpcInsIDs[1]
				local eid = Game.WorldManager:GetNpcByInstance(npcInsID)
				local RPCEntity = Game.EntityManager:getEntity(eid)
				if RPCEntity and RPCEntity.CharacterID then
					if traceParam.bIsFollow then
						Game.me:StartKeepPathFollowTargetWithRelativeLoc(RPCEntity, traceParam.offectX or 0, traceParam.offectY or 0, 0)
					else
						Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.Task, traceParam.WorldID, traceParam.tempPos)
					end
				else
					Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.Task, traceParam.WorldID, traceParam.tempPos)
				end
			else
				local targetPos = FVector()
				targetPos = Game.TraceSystem:GetTraceStaticLoaction(targetPos, traceParam.TraceTargetSpawner)
				Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.Task, traceParam.WorldID, targetPos)
			end
		end
	else
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TASK_AUTO_TRACE_FAIL_TIPS)
		if traceParam.Pos then
			Game.me:StartNavigatorByHudTask(traceParam.WorldID, traceParam.Pos)
		else
			if traceParam.TraceTaskNPCIDs and #traceParam.TraceTaskNPCIDs > 0 then
				local npcID = traceParam.TraceTaskNPCIDs[1]
				--local bMatch = false
				local TaskNPCs = Game.NPCManager.GetTaskNpcByConfigID(npcID)
				if TaskNPCs then
					for _, V in pairs(TaskNPCs) do
						local Entity = Game.EntityManager:getEntity(V)
						if Entity then
							--bMatch = true
							Game.me:StartNavigatorByHudTask(traceParam.WorldID, Entity:GetPosition())
							break
						end
					end
				end
			else
				local targetPos = FVector()
				targetPos = Game.TraceSystem:GetTraceStaticLoaction(targetPos, traceParam.TraceTargetSpawner)
				Game.me:StartNavigatorByHudTask(traceParam.WorldID, targetPos)
			end
		end
	end
	return true
end

---获取任务失败的的倒计时
----1表示任务没有失败倒计时条件或者没有开始
function QuestUtils.GetQuestFailureCountDown(ringID, questID)
	local QuestSystem = Game.QuestSystem
	local questCfg = QuestSystem:GetTaskExcelCfg(questID)
	local failConditions = questCfg.FailConditions
	if failConditions then
		-- 这里编辑器的数据结构改变了，这里也需要做相应的调整
		local info = failConditions
		if info.Type == Enum.ETaskConstData.TASK_FAIL_TYPE__QUEST_TIMEOUT then
			local taskInfo = QuestSystem:GetQuestInfo(ringID)
			local endTime = 0
			local startAt = taskInfo.RingStartAt
			if taskInfo and startAt > 0 then
				endTime = startAt + (info.Time or 0) * 1000
				return (endTime - _G._now()) / 1000
			end
		end
	end
	return -1
end

function QuestUtils.GetTimeCDFormatStr(cdTime)
	local day = math.floor(cdTime / 86400)
	cdTime = cdTime % 86400
	local hour = math.floor(cdTime / 3600)
	cdTime = cdTime % 3600
	local minute = math.floor(cdTime / 60)
	cdTime = cdTime % 60
	local second = cdTime
	if day > 0 then
		return string.format("%02d%s%02d%s", day, StringConst.Get("DAY"), hour, StringConst.Get("HOUR"))
	elseif hour > 0 then
		return string.format("%02d%s%02d%s", hour, StringConst.Get("HOUR"), minute, StringConst.Get("MINUTE"))
	elseif minute > 0 then
		return string.format("%02d%s%02d%s", minute, StringConst.Get("MINUTE"), second, StringConst.Get("SECOND"))
	else
		return string.format("%02d%s", second, StringConst.Get("SECOND"))
	end
end

---任务状态在侧边栏的显示
QuestUtils.QuestStateInSideBarUI = {
	Normal = 0,
	Saving = 1,
	Saved = 2,
}

---判断是否存档点
function QuestUtils.CheckQuestSavePoint(questId)
	local questCfgData = Game.QuestSystem:GetTaskExcelCfg(questId)
	return questCfgData.FallbackSetting == 1
end

---@public 是否有当前位面正在进行中的护送任务
function QuestUtils.HasEscortTask()
	local planeID = Game.MapSystem:GetCurrentPlaneID()
	if planeID == 0 then
		return false
	end
	local questInfo = Game.QuestSystem:GetQuestList()
	for _, data in pairs(questInfo) do
		if data.RingStatus == Enum.ETaskConstData.TASK_STATUS__ACCEPTED then
			--任务进行中
			if data.NowPlaneID == planeID then
				-- 存档点了
				if data.SavePointQuestID and data.SavePointQuestID > 0 then
					return true
				end
			end
		end
	end
	return false
end

function QuestUtils.CheckTracingRingID(ringID)
	if ringID == nil or ringID == 0 then return false end
	local TraceSystem = Game.TraceSystem
	local tracingRingID = TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	if ringID == tracingRingID then return true end
	tracingRingID = TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
	return tracingRingID == ringID
end

function QuestUtils.GetQuestGroupByType(questType)
	for groupId, groupData in ksbcpairs(Game.TableData.GetTaskFilterDataTable()) do
		if groupId > 1 then
			for _, miniType in ksbcpairs(groupData.Filter) do
				if miniType == questType then
					return groupId
				end
			end
		end
	end
	Log.ErrorFormat("QuestUtils.GetQuestGroupByType questType:%d not found in  TaskFilterDataTable", questType)
end

function QuestUtils.CheckTalkIdSegment(id)
	return id >= 9000000 and id <= 9099999
end

--- 任务目标数显示模式
QuestUtils.ETargetNumShowMode = {
	HideAll = 0,
	ShowNumOnly = 1,
	ShowPBOnly = 2,
	ShowAll = 3,
}

---客户端action类型
QuestUtils.ClientActionType = {
	Letter = 1,
	Mistery = 2,
	JigsawPuzzle = 3,
	Qte = 4,
	PaintingStratch = 5,
}

function QuestUtils.CheckShowTargetNum(mode)
	return mode == QuestUtils.ETargetNumShowMode.ShowAll or mode == QuestUtils.ETargetNumShowMode.ShowNumOnly
end

function QuestUtils.CheckShowTargetProgressBar(mode)
	return mode == QuestUtils.ETargetNumShowMode.ShowAll or mode == QuestUtils.ETargetNumShowMode.ShowPBOnly
end

function QuestUtils.TransformRewardDataToShow(rewardData)
	local ret = {}
	for _, rewardInfo in ipairs(rewardData) do
		ret[#ret + 1] = {
			id = rewardInfo.ItemID,
			num = rewardInfo.Count,
		}
	end
	return ret
end

QuestUtils.DefaultTestMainChapterPerformId = 6460000

---环任务结束显示奖励界面的类型
QuestUtils.RingFinishShowRewardType = {
	Normal = 1,
	MainChapter = 2,
}

function QuestUtils.TryOpenRewardPanelOnRingFinished(questId)
	local taskData = Game.QuestSystem:GetTaskExcelCfg(questId)
	if taskData.bShowReward then
		local showRewardType = taskData.RewardType.Type
		if showRewardType == QuestUtils.RingFinishShowRewardType.Normal then
			QuestUtils.OpenNormalRewardPanel(questId)
		elseif showRewardType == QuestUtils.RingFinishShowRewardType.MainChapter then
			QuestUtils.OpenMainChapterStartPanel(taskData.RewardType.ChapterId, questId)
		end
	end
end

function QuestUtils.OpenNormalRewardPanel(questId)
	local rewardData = Game.QuestSystem:GetRewardByQuestID(questId)
	local rewardListData = {}
	for _, info in pairs(rewardData) do
		local itemId, count = info.ItemID, info.Count
		if itemId then
			---@type ItemRewardNewParam
			local data = {
				itemId = itemId,
				count = count,
			}
			rewardListData[#rewardListData + 1] = data
		end
	end
	Game.NewUIManager:OpenPanel("GetItemsPopup_Panel", {
		items = { comItems = rewardListData},
		closeCallback= function() 
			-- 通知服务端
		end
	})
end

function QuestUtils.OpenMainChapterStartPanel(chapterId, questId)
	Game.NewUIManager:OpenPanel("MainChapterStartPerform_Panel", chapterId, questId)
end

function QuestUtils.OpenMainChapterEndPanel(chapterId, questId)
	Game.NewUIManager:OpenPanel("MainChapterEndPerform_Panel", chapterId, questId)
end

---黑屏字幕里的背景类型
QuestUtils.PlayBlackBGType = {
	Black = 1,
	MimeWhite = 2
}

function QuestUtils.OpenBlackOrWhiteBgPanel(bgType, contentList, endCallback)
	bgType = bgType or QuestUtils.PlayBlackBGType.Black
	if bgType == QuestUtils.PlayBlackBGType.Black then
		Game.NewUIManager:OpenPanel("BlackContent_Panel", contentList, endCallback)
	elseif bgType == QuestUtils.PlayBlackBGType.MimeWhite then
		Game.NewUIManager:OpenPanel("MimeWhiteContent_Panel", contentList, endCallback)
	end
end

function QuestUtils.GetRingChapterName(ringId)
	local ringCfg = Game.QuestSystem:GetRingExcelCfg(ringId)
	if ringCfg then
		local chapterCfg = Game.QuestSystem:GetChapterExcelCfg(ringCfg.ChapterID)
		if chapterCfg then
			return chapterCfg.ChapterName 
		end
	end
end

QuestUtils.PosEqualApproximate = 0.001
function QuestUtils.CheckPositionEqual(pos1, pos2)
	return math.abs(pos1.X - pos2.X) < QuestUtils.PosEqualApproximate and 
		math.abs(pos1.Y - pos2.Y) < QuestUtils.PosEqualApproximate and
		math.abs(pos1.Z - pos2.Z) < QuestUtils.PosEqualApproximate
end

return QuestUtils
