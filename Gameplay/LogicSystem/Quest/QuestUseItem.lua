---@class QuestUseItem @任务使用道具处理
local QuestUseItem = DefineClass("QuestUseItem")
--local Const = kg_require("Shared.Const")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")

QuestUseItem._Empty = {}

---@class QuickUseItemMapData
---@field position userdata
---@field radius number
---@field itemID number
---@field triggerInsID number


function QuestUseItem:ctor()
    ---@type table<number, table<number, QuickUseItemMapData>> 快捷使用标记列表
    self.quickUseItemMap = {}
    ---@type table 监听列表
    self.triggerInsIDMap = {}
    ---@type table 监听列表
    self.quickUseInsMap = {}
    ---@type table 临时目录
    self.tempIndex = {}
	self.tempTriggerInstID = {}
end

function QuestUseItem:OnWorldMapLoadComplete(levelID)
    local quickUseItemMap = self.quickUseItemMap[levelID]
    if quickUseItemMap then
        for _, data in pairs(quickUseItemMap) do
            self:addShapeTrigger(data)
        end
    end
    local curPlane = Game.MapSystem:GetCurrentPlaneID()
    quickUseItemMap = self.quickUseItemMap[curPlane]
    if quickUseItemMap then
        for _, data in pairs(quickUseItemMap) do
            if data.position then
                self:addShapeTrigger(data)
            else
                Game.HUDInteractManager:AddQuickUseItem(data.itemID)
            end
        end
    end
end

---@public 清理快捷使用道具图标
---@param nextLevelMapData LevelMapData
function QuestUseItem:OnLevelLoadStart(nextLevelMapData)
    Game.HUDInteractManager:RemoveAllQuickUseItem()
end

function QuestUseItem:onDistanceChange(Owner, Distance)
    local insID = Owner.InstanceID
    local quickUseInsMap = self.quickUseInsMap[insID]
    if quickUseInsMap and #quickUseInsMap > 0 then
        for i = 1, #quickUseInsMap do
            local param = quickUseInsMap[i]
            if Distance < param.radius then
                Game.HUDInteractManager:AddQuickUseItem(param.itemID)
            else
                Game.HUDInteractManager:RemoveQuickUseItem(param.itemID)
            end
        end
    else
        Game.me:UnRegisterMainPlayerDistanceChanged(Owner:uid())
    end
end

function QuestUseItem:OnNpcInitTrigger(insID, entityUID, npcCfgID)
    if self.quickUseInsMap[insID] then
        local quickUseInsMap = self.quickUseInsMap[insID][1]
        if quickUseInsMap then
            Game.me:RegisterMainPlayerDistanceChanged(entityUID, quickUseInsMap.radius+100, function(Owner, Distance)
                self:onDistanceChange(Owner, Distance)
            end)
        end
    end
end

function QuestUseItem.quickUseInsMapSortFun(a, b)
    return a.radius > b.radius
end

function QuestUseItem:AddInsUseItenListen(insID, radius, itemID)
    local quickUseInsMap = self.quickUseInsMap[insID]
    if not quickUseInsMap then
        quickUseInsMap = {}
        self.quickUseInsMap[insID] = quickUseInsMap
    end
    for i = 1, #quickUseInsMap do
        if quickUseInsMap[i].itemID == itemID then
            Log.WarningFormat("【QuestUseItem】The NPC(InsID:%s) Already Has QuickUseItem(ItemID:%s)", insID, itemID)
            return
        end
    end
    quickUseInsMap[#quickUseInsMap+1] = {
        itemID = itemID, 
        radius = radius,
    }
    table.sort(quickUseInsMap, self.quickUseInsMapSortFun)

    local eid = Game.WorldManager:GetNpcByInstance(insID)
    if eid then
        Game.me:RegisterMainPlayerDistanceChanged(eid, radius+100, function(Owner, Distance)
            self:onDistanceChange(Owner, Distance)
        end)
    end
end

function QuestUseItem:RemoveInsUseItenListen(insID, itemID)
    local quickUseInsMap = self.quickUseInsMap[insID]
    if quickUseInsMap then
        for i = 1, #quickUseInsMap do
            if quickUseInsMap[i].itemID == itemID then
                table.remove(quickUseInsMap, i)
                Game.HUDInteractManager:RemoveQuickUseItem(itemID)
                break
            end
        end
    end
end

function QuestUseItem:AddQuickUseItemListen(mapID, position, radius, itemID)
    local currentLevelID = Game.LevelManager.GetCurrentLevelID()
    local currentPlaneID = Game.MapSystem:GetCurrentPlaneID()
    local levelID, planeID = Game.QuestSystem:getPlaneAndMapID(mapID, 0)
    if not self.quickUseItemMap[mapID] then
        self.quickUseItemMap[mapID] = {}
    end
	---@type QuickUseItemMapData
	local quickUseItemMapData = {
		position = position,
		radius = radius,
		itemID = itemID,
	}
	table.insert(self.quickUseItemMap[mapID], quickUseItemMapData)

    if levelID == currentLevelID and planeID == currentPlaneID then
        if position then
            self:addShapeTrigger(quickUseItemMapData)
        else
            Game.HUDInteractManager:AddQuickUseItem(itemID)
        end
    end
end

function QuestUseItem:RemoveQuickUseItemListen(mapID, itemID, position)
    table.clear(self.tempIndex)
	table.clear(self.tempTriggerInstID)
	local checkPos = position ~= nil
	local quickUseItemMapDatas = self.quickUseItemMap[mapID]
	if quickUseItemMapDatas then
        for index, data in ipairs(quickUseItemMapDatas) do
            if data.itemID == itemID then
				if not checkPos or QuestUtils.CheckPositionEqual(data.position, position) then
					self.tempIndex[#self.tempIndex + 1] = index
					self.tempTriggerInstID[#self.tempTriggerInstID + 1] = data.triggerInsID
				end
            end
        end

		for _, index in ipairs(self.tempIndex) do
			quickUseItemMapDatas[index] = nil
		end
		
		for _, toRemoveTriggerInstID in ipairs(self.tempTriggerInstID) do
			local curItemID = self.triggerInsIDMap[toRemoveTriggerInstID]
			if curItemID == itemID then
				Game.WorldManager:RemoveTrigger(toRemoveTriggerInstID)
				self.triggerInsIDMap[toRemoveTriggerInstID] = nil
			end
		end
    end
	
    Game.HUDInteractManager:RemoveQuickUseItem(itemID)
end

function QuestUseItem:enterTrigger(insID, uID, pos)
    local itemID = self.triggerInsIDMap[insID]
    Game.HUDInteractManager:AddQuickUseItem(itemID)
end

function QuestUseItem:leaveTrigger(insID, uID, pos)
    local itemID = self.triggerInsIDMap[insID]
    Game.HUDInteractManager:RemoveQuickUseItem(itemID)
end

---@param data QuickUseItemMapData
function QuestUseItem:addShapeTrigger(data)
	local position, radius, itemID = data.position, data.radius, data.itemID
    local triggerInsID = Game.WorldManager:AddSphereTrigger(position, nil, radius, self, "enterTrigger", "leaveTrigger")
    data.triggerInsID = triggerInsID
	self.triggerInsIDMap[triggerInsID] = itemID
end

return QuestUseItem