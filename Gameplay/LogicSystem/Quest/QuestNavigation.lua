local questTrace = kg_require("Gameplay.LogicSystem.Quest.QuestTrace")
local questTraceAction = kg_require("Gameplay.LogicSystem.Quest.QuestTraceAction")
local QuestNavigation = DefineClass("QuestNavigation")

QuestNavigation.NaviSucDistance = 350 --- cm
function QuestNavigation:ctor()
	self.curTraceParam = nil
	self.curTargetInsID = nil
	self.curPos = nil
	
	--- table<vector3, bool>
	self.posNaviState = {}
	--- table<int, bool>
	self.insIDNaviState = {}
	self.questTraceAction = nil
	
	Game.GlobalEventSystem:AddListener(EEventTypesV2.AUTO_NAVIGATION_END, "OnNavigateFinish", self)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.QUEST_ON_TARGET_FINISHED, "OnQuestTargetFinish", self)
end

function QuestNavigation:dtor()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

--- return InsID, Pos, MapID
function QuestNavigation:GetCurrentNaviParam(questID, traceParam, bLoop)
	if traceParam[1].TraceType == questTrace.TraceParamType.TraceNPC or traceParam[1].TraceType == questTrace.TraceParamType.TraceNPCAnsMonsters then
		return self:GetNPCorMonsterNaviParam(questID, traceParam)
	end
	for i, curTraceParam in ipairs(traceParam) do
		if curTraceParam.InsIDList then
			for insID, pos in pairs(curTraceParam.InsIDList) do
				if not self.insIDNaviState[insID] then
					self.insIDNaviState[insID] = true
					self.curTraceParam = curTraceParam
					self.curTargetInsID = insID
					return insID, pos, curTraceParam.MapID
				end
			end
		end
		local naviPos = curTraceParam.Pos
		if naviPos then
			if not self.posNaviState[naviPos] then
				self.curTraceParam = curTraceParam
				self.curPos = naviPos
				self.posNaviState[naviPos] = true
				return nil, naviPos, curTraceParam.MapID
			end
		end
	end
	if not bLoop then
		table.clear(self.insIDNaviState)
		table.clear(self.posNaviState)
		return self:GetCurrentNaviParam(questID, traceParam, true)
	end
end

function QuestNavigation:GetNPCorMonsterNaviParam(questID, traceParam)
	--- NPC和怪物，总找最近的即可
	local MinDis = 999999999
	local finalInsID = nil
	local finalPos = nil
	local finalMapID = nil
	local playPos = Game.me:GetPosition()
	for i, curTraceParam in ipairs(traceParam) do
		if curTraceParam.InsIDList then
			for insID, pos in pairs(curTraceParam.InsIDList) do
				local insDis = (playPos - pos):Size()
				if insDis < MinDis then
					MinDis = insDis
					finalInsID = insID
					finalPos = pos
					finalMapID = curTraceParam.MapID
					self.curTraceParam = curTraceParam
					self.curTargetInsID = insID
					self.curPos = pos
					self.curMapID = curTraceParam.MapID
				end
			end
		end
	end
	return finalInsID, finalPos, finalMapID
end


function QuestNavigation:TryAutoTrace(traceParam, questID)
	if not traceParam or #traceParam <= 0 or traceParam[1].bNotNavigation then                                                                                                                                      
		return
	end
	local insID, pos, mapID = self:GetCurrentNaviParam(questID, traceParam)
	self.curInsID = insID
	self.curPos = pos
	self.curMapID = mapID
	if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_AUTONAVIGATION") then
		-- 已经很近了，可以直接执行交互逻辑a
		if self:checkNavigateSuccess() and self.curTraceParam.bAutoInteract then
			self:doTraceAction()
			return true
		end
		-- 能拿到entity并且不能交互则执行跟随npc寻路
		if insID and not self.curTraceParam.bAutoInteract then
			local entity = Game.EntityManager:getEntityByInsID(insID)
			if entity and entity.isNpc then
				local x, y, z = 0, 0, 0
				local offset = self.curTraceParam.OffsetXY
				if offset then
					x, y, z = offset.X, offset.Y, offset.Z
				end
				Game.me:StartKeepPathFollowTargetWithRelativeLoc(entity, x, y, z)
				return true
			end
		end
		if pos then
			Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.Task, mapID, pos)
		end
		return true
		--- 暂没有跟随npc的逻辑了， 需要再加
		--- Game.me:StartKeepPathFollowTargetWithRelativeLoc(RPCEntity, traceParam.offectX or 0, traceParam.offectY or 0, 0)
	else
		--- 喵呜贼
		Game.me:StartNavigatorByHudTask(mapID, pos)
	end
end

function QuestNavigation:OnNavigateFinish(targetMapID, curPos, targetNPCID)
	if self:checkNavigateSuccess() then
		self:doTraceAction()
	end
end

function QuestNavigation:doTraceAction()
	if  self.curTraceParam and self.curTraceParam.bAutoInteract then
		if not self.questTraceAction then
			self.questTraceAction = questTraceAction.new()
		end
		local actionParam = {}
		actionParam.InsID = self.curInsID
		self.questTraceAction:BeginAction(self.curTraceParam, actionParam)
	end	
end

function QuestNavigation:checkNavigateSuccess()
	--- 根据距离判断是否算寻路成功
	if not self.curPos then
		return false
	end
	local dis = self.curPos - Game.me:GetPosition()
	if not dis then
		return false
	end
	return dis:Size() < QuestNavigation.NaviSucDistance
end

function QuestNavigation:clearCurState()
	table.clear(self.insIDNaviState)
	table.clear(self.posNaviState)
	self.curTraceParam = nil
	self.curTargetInsID = nil
	self.curPos = nil
end

function QuestNavigation:OnQuestTargetFinish()
	self:clearCurState()
end

return QuestNavigation