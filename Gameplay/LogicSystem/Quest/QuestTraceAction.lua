--- 任务相关寻路成功后的Action表现
local QuestTraceAction = DefineClass("QuestTraceAction")
local QuestTrace = kg_require("Gameplay.LogicSystem.Quest.QuestTrace")

---@class QuestTraceAction.TraceActionDict @任务追踪自动交互定义
QuestTraceAction.TraceActionDict =  {
	[QuestTrace.TraceParamType.TracePos] = function(self, traceParam, actionParam)
		return true
	end,
	[QuestTrace.TraceParamType.TraceTriggerPos] = function(self, traceParam, actionParam)
		return true
	end,
	[QuestTrace.TraceParamType.TraceSceneEnterPos] = function(self, traceParam, actionParam)
		return true
	end,
	[QuestTrace.TraceParamType.TraceScentEnterInsID] = function(self, traceParam, actionParam)
		Game.me:ReqInitiateInteract(tostring(actionParam.InsID))
	end,
	[QuestTrace.TraceParamType.TraceNPC] = function(self, traceParam, actionParam)
		local entity = Game.EntityManager:getEntityByInsID(actionParam.InsID)
		if entity and entity.isNpc then
			local talkTask = Game.HUDInteractManager:BuildNPCInterActTask(entity)
			if talkTask then
				talkTask.OnClicked()
			end
		end
	end,
	[QuestTrace.TraceParamType.TraceNPCAnsMonsters] = function(self, traceParam, actionParam)
		local entity = Game.EntityManager:getEntityByInsID(actionParam.InsID)
		if entity and entity.isNpc then
			local talkTask = Game.HUDInteractManager:BuildNPCInterActTask(entity)
			if talkTask then
				talkTask.OnClicked()
			end
		end
	end,
	[QuestTrace.TraceParamType.TraceCollect] = function(self, traceParam, actionParam)
		Game.me:ReqInitiateInteract(tostring(actionParam.InsID))
	end,
	[QuestTrace.TraceParamType.TraceSeatInsID] = function(self, traceParam, actionParam)
		Game.me:ReqContinuousInteract(true, tostring(actionParam.InsID), 1)
	end,
	[QuestTrace.TraceParamType.TraceDoorInsID] = function(self, traceParam, actionParam)
		Game.me:ReqInitiateInteract(tostring(actionParam.InsID))
	end,
	[QuestTrace.TraceParamType.TraceScene] = function(self, traceParam, actionParam)
		return true
	end,
	[QuestTrace.TraceParamType.TraceRadius] = function(self, traceParam, actionParam)
		return true
	end,
	[QuestTrace.TraceParamType.TraceItemUse] = function(self, traceParam, actionParam)
		Game.QuestSystem:OnUseQuestItem(traceParam.ItemID)
	end,
	[QuestTrace.TraceParamType.TraceItemUsePos] = function(self, traceParam, actionParam)
		Game.QuestSystem:OnUseQuestItem(traceParam.ItemID)
	end,
	[QuestTrace.TraceParamType.TraceItemUseInsID] = function(self, traceParam, actionParam)
		Game.QuestSystem:OnUseQuestItem(traceParam.ItemID)
	end,
}

function QuestTraceAction:ctor()
	
end 

function QuestTraceAction:dtor()
	
end

function QuestTraceAction:BeginAction(traceParam, actionParam)
	QuestTraceAction.TraceActionDict[traceParam.TraceType](self, traceParam, actionParam)
end

return QuestTraceAction
