local bitset = kg_require("Shared.lualibs.bitset")
local Const = kg_require("Shared.Const")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")

local NewHeadInfoConst = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoConst")
local EHeadInfoNodeType = NewHeadInfoConst.EHeadInfoNodeType
local QuestPostProcess = kg_require("Gameplay.LogicSystem.Quest.QuestPostProcess")
---@class QuestModel:SystemModelBase,QuestPostProcess @任务系统
local QuestModel = DefineClass("QuestModel", SystemModelBase, QuestPostProcess)
local TaskMainTypeConst = kg_require("Shared.Const.QuestConst").TaskMainTypeConst
local TaskStatusConst = kg_require("Shared.Const.QuestConst").TaskStatusConst

---@class QuestPageFilter @页面过滤
---@field QuestType number @任务类型
---@field Children table @子类型

QuestModel._Empty = {}

---
QuestModel.PreRingType = {
	RingID = 1,
	Or = 2,
	And = 3,
}

---@class QuestModel.Filter @剧情任务类型过滤
QuestModel.Filter = {
    [TaskMainTypeConst.TASK_MINITYPE__MAIN] = true,
    [TaskMainTypeConst.TASK_MINITYPE__SUBTASK] = true,
    [TaskMainTypeConst.TASK_MINITYPE__ROLE_PLAY] = true,
}

---@class DefaultAbandonedQuestInfo @缺省已完成剧情任务的info结构
QuestModel.DefaultAbandonedQuestInfo = {
    RingStatus = TaskStatusConst.TASK_STATUS__ABANDONED,
}

QuestModel.QuestTypeSortOrder = {
    [TaskMainTypeConst.TASK_MINITYPE__MAIN] = 0,
    [TaskMainTypeConst.TASK_MINITYPE__SUBTASK] = 1,
    [TaskMainTypeConst.TASK_MINITYPE__ROLE_PLAY] = 2,
    [TaskMainTypeConst.TASK_MINITYPE__DUNGEON] = 3,
    [TaskMainTypeConst.TASK_MINITYPE__REPEAT] = 3,
    [TaskMainTypeConst.TASK_MINITYPE__GUILDSINGLE] = 3,
    [TaskMainTypeConst.TASK_MINITYPE__ROLEPLAYSHERIFFTASK] = 3,
    [TaskMainTypeConst.TASK_MINITYPE__SYSTEM_TASK] = 3,
}
----------------------------Override----------------------------
function QuestModel:init()
    ---@type table<number,QuestInfo> @剧情总任务数据
    self.questList = {}
    ---@type table<number,QuestInfo> @系统总任务数据 TODO 废弃
    self.otherQuestList = {}
    ---@type table<number,QUEST_FINISHED_INFO_DICT> @已完成的任务数据
    self.finishQuests = {}
    ---@type table<number,QuestInfo> @已放弃的任务数据
    self.abandonQuests = {}
    ---@type table<number,boolean> @激活任务表
    self.activeQuests = {}
	---@type table<number,boolean> @开启任务表（开启未激活状态，在面板显示但不可接取，仅客户端数据）
	self.openQuests = {}
	---@type table<number,number>> @闲话监听标记
    self.talkFlag = {}
    ---@type table<number,number> @Cutscene监听标记
    self.cutsceneFlag = {}
    ---@type table<number,number> @Dialogue监听标记
    self.dialogueFlag = {}
    ---@type table<number,number> @书信监听标记
    self.letterFlag = {}
    ---@type table<number,number> @星座解谜监听标记
    self.misteryFlag = {}
    ---@type table<number,number> @界面跳转监听标记
    self.jumpFlag = {}
    ---@type table<number,number> @拼图解谜监听标记
    self.jigsawFlag = {}
    ---@type table<number,table<number,number>> @任务面板显示标签过滤
    self.tagFilter = {}
    ---@type table<number,boolean> @排序脏标记
    self.tagFilterDirty = {}
    ---@type table<number,number[]> @tag选项
    self.tagOptions = {}
    ---@type table<number,table<number,QuestPageFilter[]>>> @任务面板显示页签过滤(暂不考虑为空隐藏)
    self.pageFilter = {}
    ---@type table<number,number[]> @任务类型到过滤标签的反向映射
    self.filterMap = {}
    ---@type table<number,number[]> @位面有效性标记
    self.transPortValidMap = {}
    ---@type table<number,number> @使用道具标记<道具ID,任务ID>
    self.useItemMap = {}
    ---@type table<number,number> @使用道具标记<道具ID,任务ID>
    self.useItemIntance = {}
    ---@type table<number,number> @道具提交监听ID
    self.submitFlag = {}
    ---@type table<number,number> @SubmitID与Dialogue映射
    self.submitDialogueMap = {}
    ---@type table<number,number> @Dialogue与SubmitID映射
    self.dialogueSubmitMap = {}
    ---@type table<number,number> @对话领取任务的监听
    self.receiveDialogueFlag = {}
    ---@type table<number,boolean> @ConditionRegister
    self.interactorConditionFlag = {}
    ---@type table<number,table<number,number>> @可从NPC领取的任务记录（地图显示需要）
    self.npcReciveMap = {}
    ---@type table<number,ringID> @道具使用领取相关table<ItemID,RingID>
    self.receiveItemMap = {}
    ---@type table<number,number> @任务主目标切换table<questID,mainIndex>
    self.mainConditionMap = {}
    ---@type table<number,number> @任务主目标切换table<questID,mainIndex>
    self.caneSkillMap = {}
    ---@type table<number,number> @寻找指定NPC对话隐藏任务图标标记
    self.findNpcIconCollapseMap = {}

	self.openQuestTimer = nil -- 任务开启计时器
    ---@type number[] @领取中和已完成未提交的环ID数组
    self.ringList = {}
    ---@type boolean @标记ring数组是否需要排序
    self.ringListDirty = false
	self.tagCache = {} -- tag缓存
	---@type number[] 被互斥的环任务ID dict, 为true则排斥该任务的任务已完成/已接取
	self.mutexDict = {}

	self.inDialogueControlState = false

	---客户端一些Action的flag，主要是做断线重连时，防止重复执行的问题
	self.clientActionFlags = {}
	for _, actionType in pairs(QuestUtils.ClientActionType) do
		self.clientActionFlags[actionType] = -1
	end
	self.pendingProcessActionsAfterLoading = {}
	Game.EventSystem:AddListener(_G.EEventTypes.GAME_ENTER_STAGE_SHOW_UI_END, self, self.Receive_GAME_ENTER_STAGE_SHOW_UI_END)
end

function QuestModel:unInit()
	if self.openQuestTimer then
		Game.TimerManager:StopTimerAndKill(self.openQuestTimer)
		self.openQuestTimer = nil
	end
    self:clear()
	Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.GAME_ENTER_STAGE_SHOW_UI_END, self, self.Receive_GAME_ENTER_STAGE_SHOW_UI_END)
end

function QuestModel:clear()
    ---初始化标签组
    self:initFilterTag(Game.QuestSystem.TagDefault)
    ---清理环存储
    table.clear(self.ringList)
    self.ringListDirty = false
	self.inDialogueControlState = false
    ---清理任务领取监听或标记
    for ringID, _ in pairs(self.activeQuests) do
        self:doRingPostProcess(ringID)
    end
    table.clear(self.activeQuests)
    for ringID, oldQuestInfo in pairs(self.questList) do
        if oldQuestInfo.RingStatus ~= TaskStatusConst.TASK_STATUS__FINISHED then
            --任务回退重置(强行设置旧任务为完成状态以重置旧任务监听)
            oldQuestInfo.RingStatus = TaskStatusConst.TASK_STATUS__FINISHED
            self:doQuestPostProcess(oldQuestInfo.NowRingQuestID)
        end
    end
    table.clear(self.questList)
    table.clear(self.mainConditionMap)
end

function QuestModel:TryRecordClientActionFlag(actionType, id)
	if self.clientActionFlags[actionType] > 0 then
		return false
	end
	self.clientActionFlags[actionType] = id
	return true
end

function QuestModel:TryClearClientActionFlag(actionType, id)
	if self.clientActionFlags[actionType] == id then
		self.clientActionFlags[actionType] = -1
	end
end

---因为断线重连，loading会把客户端的一些界面操作clear掉，因此需要先缓存下来，等loading结束再执行相关操作
function QuestModel:AddPendingProcessActionsAfterLoading(funcName, target, ...)
	---这里约束func只会调用一次，后续会覆盖前面的
	self.pendingProcessActionsAfterLoading[funcName] = {
		Target = target,
		Args = {...}
	}
end

function QuestModel:ProcessActionsAfterLoadingFinish()
	for funcName, action in pairs(self.pendingProcessActionsAfterLoading) do
		local target = action.Target
		local func = target[funcName]
		if func then
			xpcall(func, _G.CallBackError, target, table.unpack(action.Args))
		end
	end
	self.pendingProcessActionsAfterLoading = {}
end

function QuestModel:Receive_GAME_ENTER_STAGE_SHOW_UI_END()
	self:ProcessActionsAfterLoadingFinish()
end

----------------------private----------------------
---界面打开卡顿的优化，提前过滤
function QuestModel:initFilterTag(tagDefault)
    local filterCfg = Game.TableData.GetTaskFilterDataTable()
    local pageMiniType = Game.TableData.GetTaskMiniTypeDataTable()
    
    --默认第一个Tag是全部
    self.tagFilter[tagDefault] = {}
    self.tagFilterDirty[tagDefault] = {}
    for questType, info in ksbcpairs(pageMiniType) do
        self.tagFilter[tagDefault][questType] = {}
    end
    
    for index, config in ksbcipairs(filterCfg) do
        self.pageFilter[index] = {}
        self.tagOptions[index] = {tagDefault}
        for tagID, filterTab in pairs(self.tagFilter) do
            self.pageFilter[index][tagID] = {}
            local pageTagFilter =  self.pageFilter[index][tagID]
            for _, questType in ksbcipairs(config.Filter) do
                pageTagFilter[#pageTagFilter+1] = {QuestType = questType, Children = filterTab[questType]}
            end
        end
    end

    for index, config in ksbcipairs(filterCfg) do
        for _, questType in ksbcipairs(config.Filter) do
            if not self.filterMap[questType] then
                self.filterMap[questType] = {}
            end
            self.filterMap[questType][#self.filterMap[questType]+1] = index
        end
    end
end

function QuestModel.sortTagFilter(aRingID, bRingID)
    local QuestSystem = Game.QuestSystem

	local ringID1 = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	local ringID2 = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
	local bIsTracingA = ringID1 == aRingID or ringID2 == aRingID
	local bIsTracingB = ringID1 == bRingID or ringID2 == bRingID
	if bIsTracingA ~= bIsTracingB then
		return bIsTracingA
	end
	
	local bIsAcceptedA = QuestSystem.model.questList[aRingID] and true or false
	local bIsAcceptedB = QuestSystem.model.questList[bRingID] and true or false
	if bIsAcceptedA ~= bIsAcceptedB then
		return bIsAcceptedA
    elseif not bIsAcceptedA then
        return aRingID < bRingID
	end
	
    if QuestSystem:GetQuestAcceptTime(aRingID) == QuestSystem:GetQuestAcceptTime(bRingID) then
        return aRingID < bRingID
    end

    return QuestSystem:GetQuestAcceptTime(aRingID) < QuestSystem:GetQuestAcceptTime(bRingID)
end

function QuestModel.sortRingList(aRingID, bRingID)
    local QuestSystem = Game.QuestSystem
    local aQuestType = QuestSystem:GetQuestTypeByRingID(aRingID)
    local bQuestType = QuestSystem:GetQuestTypeByRingID(bRingID)
    if QuestModel.QuestTypeSortOrder[aQuestType] ~= QuestModel.QuestTypeSortOrder[bQuestType] then
        return QuestModel.QuestTypeSortOrder[aQuestType] < QuestModel.QuestTypeSortOrder[bQuestType]
    end
    if QuestSystem:GetQuestAcceptTime(aRingID) == QuestSystem:GetQuestAcceptTime(bRingID) then
        return aRingID < bRingID
    end

    return QuestSystem:GetQuestAcceptTime(aRingID) < QuestSystem:GetQuestAcceptTime(bRingID)
end
----------------------Common----------------------
---@param ringID number @环ID
---@return QuestInfo @任务环数据
function QuestModel:GetQuestInfo(ringID)
    if self.questList[ringID] then
        return self.questList[ringID]
    elseif self.abandonQuests[ringID] then
        return self.abandonQuests[ringID]
    elseif self.finishQuests[ringID] then
        self.finishQuests[ringID].RingStatus = TaskStatusConst.TASK_STATUS__FINISHED
        return self.finishQuests[ringID]
    end
    --Log.WarningFormat("Error RingInfo(RingID:%s)", ringID)
end

---@param ringID number 环ID
---@return boolean
function QuestModel:GetCanAccept(ringID)
	if not ringID then
		return false
	end
	if self.activeQuests[ringID] then
		return true
	end
	if self.abandonQuests[ringID] then
		return true
	end
	return false
end

---@param filterIndex number @任务页签
---@param tagID number @任务标签
function QuestModel:GetPageFilter(filterIndex, tagID, forceDirty)
    for miniType, filterTab in pairs(self.tagFilter[tagID]) do
        if self.tagFilterDirty[tagID][miniType] or forceDirty then
            self.tagFilterDirty[tagID][miniType] = false
            table.sort(filterTab, self.sortTagFilter)
        end
    end
    if not self.pageFilter[filterIndex] then
        Log.WarningFormat("【QuestModel】Error FilterIndex:%s", filterIndex)
        return QuestModel._Empty
    end
    return self.pageFilter[filterIndex][tagID]
end

function QuestModel:GetTagOptions(filterIndex)
    return self.tagOptions[filterIndex] or QuestModel._Empty
end

function QuestModel:GetRingList()
    if self.ringListDirty then
        self.ringListDirty = false
        table.sort(self.ringList, self.sortRingList)
    end
    return self.ringList
end

-- 任务开启时间队列本地
function QuestModel:RefreshOpenQuest()
	local timeList = Game.TableData.Get_ShowTimeDateMap()
	local nowTimestamp = _G._now()
	local nearestTime = (math.floor(nowTimestamp / 1000) + 1) * 1000 -- 第二天0点的时间戳
	local nearestRing
	for timestamp, ringList in ksbcpairs(timeList) do
		if timestamp * 1000 < nowTimestamp then
			for _, ringID in ksbcpairs(ringList) do
				if not self.questList[ringID] and not self.finishQuests[ringID]
					and not self.abandonQuests[ringID] and not self.activeQuests[ringID] 
					and not self.openQuests[ringID] then
					-- 判断前置任务&条件是否完成
					if self:checkCanOpen(ringID) then
						self.openQuests[ringID] = true
						self:doOpenPostProcess(ringID)
					end
				end
			end
		else
			if timestamp * 1000 < nearestTime then
				nearestTime = timestamp * 1000
				nearestRing = ringList
			end
		end
	end
	if nearestRing then
		-- 当天timer，每日刷新
		local timeDiff = (nearestTime - _G._now()) * 1000
		self.openQuestTimer = Game.TimerManager:CreateTimerAndStart(function()
			Game.QuestSystem.model:RefreshOpenQuest()
			--Game.EventSystem:Publish(EEventTypesV2.ON_UI_OPEN, panel.uid)
		end, timeDiff, 1)
	end
end

function QuestModel:checkCanOpen(ringID)
	local ringCfg = Game.QuestSystem:GetRingExcelCfg(ringID)
	-- 是否结束
	local nowTimestamp = _G._now()
	local rewardData = Game.TableData.GetTaskChapterRewardDataRow(ringID)
	local mode,timestamp = ksbcnext(rewardData.EndTimeCondition)
	if mode and mode == 3 then
		if timestamp * 1000 < nowTimestamp then
			return false
		end
	end
	if mode and mode == 4 then
		if timestamp * 1000 < nowTimestamp then
			return false
		end
	end
	
	
	-- 等级校验
	local playerLv = GetMainPlayerPropertySafely("Level")
	if (ringCfg.MinLevel~=0 and playerLv < ringCfg.MinLevel) or (ringCfg.MaxLevel~=0 and playerLv > ringCfg.MaxLevel) then
		return false
	end
	-- 前置任务
	if self:checkPreRing(ringCfg.PreRingRelations) == false then
		return false
	end
	-- 职业校验
	local prof = GetMainPlayerPropertySafely("Profession")
	if ringCfg.ClassID and #ringCfg.ClassID~=0 and not table.contains(ringCfg.ClassID,0) and not table.contains(ringCfg.ClassID, prof) then
		return false
	end
	-- 互斥列表
	if self.mutexDict[ringID] then
		return false
	end
	
	return true
end
function QuestModel:checkPreRing(preRing)
	if preRing == nil or next(preRing) == nil then
		return true
	end
	if preRing.Type == QuestModel.PreRingType.RingID then
		if self.finishQuests[preRing.PreRingID] then
			return true
		else
			return false
		end
	end
	local result = true
	if preRing.Type == QuestModel.PreRingType.And and preRing.ChildInfo then
		for _, ringRelations in pairs(preRing.ChildInfo) do
			local nextResult = self:checkPreRing(ringRelations)
			result = result and nextResult
		end
		return result
	end
	if preRing.Type == QuestModel.PreRingType.Or and preRing.ChildInfo then
		result = false
		for _, ringRelations in pairs(preRing.ChildInfo) do
			local nextResult = self:checkPreRing(ringRelations)
			result = result or nextResult
		end
		return result
	end
end

-- 新开放任务计时器
function QuestModel:OpenTimer()
	
end

---------------------------NetEvent---------------------------
---@param questList table<number,QuestInfo> @剧情总任务数据
function QuestModel:OnMsgQuestListInfo(questList)
    self:clear()
    self.questList = questList
    for ringID, questInfo in pairs(self.questList) do
		self.openQuests[ringID] = nil
        self:doQuestPostProcess(questInfo.NowRingQuestID, true)
        self.ringList[#self.ringList+1] = ringID
        --TODO:
        local QuestSystem = Game.QuestSystem
        QuestSystem:onVisiblityCheckWithRingID(QuestSystem.HideConditionType.TaskRingAccept, ringID)
        QuestSystem:onVisiblityCheckWithQuestID(QuestSystem.HideConditionType.TaskAccept, questInfo.NowRingQuestID)
        local ringCfg = QuestSystem:GetRingExcelCfg(ringID)
        local questDatas = ringCfg.QuestData
        for questID, _ in pairs(questDatas) do
            local offect = questID % 100
            local bFinished = bitset.getBit(questInfo.RingFinishedMap, offect)
            if bFinished then
                QuestSystem:onVisiblityCheckWithQuestID(QuestSystem.HideConditionType.TaskCom, questID)
            end
        end

		for _,rid in pairs(ringCfg.MutuallyExclusiveRing) do
			self.mutexDict[rid] = true
		end
    end
    self.ringListDirty = true
    Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_LIST_UPDATE)
end

---@param questList table<number,QuestInfo> @系统总任务数据
function QuestModel:OnMsgOtherQuestListInfo(questList)
    self.otherQuestList = questList
    for ringID, questInfo in pairs(self.otherQuestList) do
        self:doQuestPostProcess(questInfo.NowRingQuestID)
    end
end

---@param questList table<number,QUEST_FINISHED_INFO_DICT> @已完成的任务数据
function QuestModel:OnMsgFinishedQuestList(QuestFinishedInfoDict)
    self.finishQuests = QuestFinishedInfoDict
	for ringID, questInfo in pairs(QuestFinishedInfoDict) do
		self.openQuests[ringID] = nil
        --TODO:
        local QuestSystem = Game.QuestSystem
        QuestSystem:onVisiblityCheckWithRingID(QuestSystem.HideConditionType.TaskRingCom, ringID)

		local ringCfg = QuestSystem:GetRingExcelCfg(ringID)
		for _,rid in pairs(ringCfg.MutuallyExclusiveRing) do
			self.mutexDict[rid] = true
		end
	end
end

---@param questList table<number,QuestInfo> @已放弃的任务数据
function QuestModel:OnMsgAbandonQuestList(questList)
    local QuestSystem = Game.QuestSystem
    for i = 1, #questList, 1 do
        --TODO:
        local ringID = questList[i]
        local firstQuestID = QuestSystem:GetFirstRingQuest(ringID)
        self.abandonQuests[ringID] = {
            RingStatus = TaskStatusConst.TASK_STATUS__ABANDONED,
            NowRingQuestID = firstQuestID,
            NowQuestTargets = {}
        }
    end
    for ringID, questInfo in pairs(self.abandonQuests) do
		self.openQuests[ringID] = nil
        -- self:doRingPostProcess(ringID)
        self:doActivePostProcess(ringID)
    end
end

---@param ringList number[] @环数组
function QuestModel:OnMsgQuestActiveList(ringList)
    local ringID
    for i = 1, #ringList, 1 do
        ringID = ringList[i]
		
        self.activeQuests[ringID] = true
		self.openQuests[ringID] = nil
        self:doActivePostProcess(ringID)
    end
	
	-- 登录数据同步完成，客户端生成开放状态任务列表
	self:RefreshOpenQuest()
end

---@param ringID number @环ID
function QuestModel:OnMsgQuestActive(ringID)
    local alreadyActive = self.activeQuests[ringID]
    self.activeQuests[ringID] = true
	self.openQuests[ringID] = nil
    if not alreadyActive then
        self:doActivePostProcess(ringID)
    end
end

---@param ringID number @任务环ID
---@param oldStatus number @旧任务状态
---@param newStatus number @新任务状态
function QuestModel:OnMsgQuestInfoUpdateStatus(ringID, oldStatus, newStatus)
    local questInfo = self.questList[ringID]
    if not questInfo then
        Log.WarningFormat("OnMsgQuestInfoUpdateStatus Can't Find RingID(%d)", ringID)
        return
    end

    questInfo.RingStatus = newStatus
    self:doQuestPostProcess(questInfo.NowRingQuestID)
	self.openQuests[ringID] = nil

    local QuestSystem = Game.QuestSystem
    if oldStatus == TaskStatusConst.TASK_STATUS__ACCEPTED then
        --当前任务以领取
        if newStatus == TaskStatusConst.TASK_STATUS__FAILED then
            --任务失败
            QuestSystem:onQuestFailed(questInfo.NowRingQuestID)
        elseif newStatus == TaskStatusConst.TASK_STATUS__ABANDONED then
            --任务放弃
            self.abandonQuests[ringID] = questInfo
            self.questList[ringID] = nil
            table.removeItem(self.ringList, ringID)
            self:doActivePostProcess(ringID)
            QuestSystem:onQuestAbandoned(questInfo.NowRingQuestID)
			local ringCfg = QuestSystem:GetRingExcelCfg(ringID)
			for _,rid in pairs(ringCfg.MutuallyExclusiveRing) do
				self.mutexDict[rid] = nil
			end
        elseif newStatus == TaskStatusConst.TASK_STATUS__ACCOMPLISHED then
            --任务已完成，如果需要手动提交任务则进入交任务流程，如果自动提交任务后续会直接进入任务结束状态
            QuestSystem:onQuestAccomplished(questInfo.NowRingQuestID)
        end
    end
    if oldStatus == TaskStatusConst.TASK_STATUS__ACCOMPLISHED then
        --任务已完成，如果需要主动提交任务则进入交任务流程
        if newStatus == TaskStatusConst.TASK_STATUS__FINISHED then
            --任务结束
            if QuestSystem:IsLastRingQuest(questInfo.NowRingQuestID) then
                self.questList[ringID] = nil
                self.finishQuests[ringID] = questInfo
                table.removeItem(self.ringList, ringID)
                QuestSystem:onRinginished(ringID, questInfo.NowRingQuestID)
            end
            QuestSystem:onQuestFinished(questInfo.NowRingQuestID)
			self:RefreshOpenQuest()
        end
    end
end

---@param questInfo QuestInfo @任务数据结构
function QuestModel:OnMsgQuestInfoUpdate(questInfo)
    --如果任务id有修改，或者任务重置则会进行这个同步
    local questID = questInfo.NowRingQuestID
    local taskCfg = Game.QuestSystem:GetTaskExcelCfg(questID)
    local ringID = taskCfg.RingID
    if not self.questList[ringID] then
        --新环任务
        self.questList[ringID] = questInfo
        self:doRingPostProcess(ringID)
        self:doQuestPostProcess(questID)
		self.activeQuests[ringID] = nil
		self.openQuests[ringID] = nil
		self.abandonQuests[ringID] = nil
        self.ringList[#self.ringList+1] = ringID
        self.ringListDirty = true
        self:OnMsgChangeTaskStepDescription(questInfo.NowRingQuestID)
        Game.QuestSystem:onNewRing(ringID)

		local ringCfg = Game.QuestSystem:GetRingExcelCfg(ringID)
		for _,rid in pairs(ringCfg.MutuallyExclusiveRing) do
			self.mutexDict[rid] = true
		end
    else
        local oldQuestInfo = self.questList[ringID]
        if oldQuestInfo.RingStatus ~= TaskStatusConst.TASK_STATUS__FINISHED then
            --任务回退重置(强行设置旧任务为完成状态以重置旧任务监听)
            oldQuestInfo.RingStatus = TaskStatusConst.TASK_STATUS__FINISHED
            self:doQuestPostProcess(oldQuestInfo.NowRingQuestID)
            self:OnMsgChangeTaskStepDescription(oldQuestInfo.NowRingQuestID)
        end
        --环开启新任务（任务ID切换）
        self.questList[ringID] = questInfo
        self:doQuestPostProcess(questID)
        self:OnMsgChangeTaskStepDescription(questInfo.NowRingQuestID)
        Game.QuestSystem:onNewQuest(questID)
    end
end

---@param ringID number @任务环ID
---@param targetIndex number @任务目标下标
---@param count number @任务目标完成数量
---@param bFinished boolean @任务目标是否已完成
function QuestModel:OnMsgQuestTargetChanged(ringID, targetIndex, count, bFinished)
    local questInfo = self.questList[ringID]
    if not questInfo then
        Log.WarningFormat("OnMsgQuestTargetChanged Can't Find RingID(%d)", ringID)
        return
    end

    local targetInfo = questInfo.NowQuestTargets[targetIndex]

    if not targetInfo then
        Log.WarningFormat("OnMsgQuestTargetChanged Can't Find TargetIndex(%d)", targetIndex)
        return
    end

    local oldCount = targetInfo.FinishCount
    targetInfo.FinishCount = count
    targetInfo.IsFinished = bFinished

    self:doConditionPostProcess(questInfo.NowRingQuestID, targetIndex)
	Game.QuestSystem:ClearTargetTraceParam()
	Game.TraceSystem:RefreshQuestTracing()
    if bFinished then
        Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_TARGET_FINISHED, questInfo.NowRingQuestID, targetIndex)
		local uids = Game.TraceSystem:GetTracingNPCsUIDs()
		for _, uid in pairs(uids) do
			Game.NewHeadInfoSystem:UpdateHeadInfoNode(uid, EHeadInfoNodeType.Icon)
		end
    else
        Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_TARGET_COUNT_UPDATE, questInfo.NowRingQuestID, targetIndex, oldCount)
    end
end

---@param questID @任务ID
---@param mainIndex @主任务目标
function QuestModel:OnMsgChangeTaskStepDescription(questID, mainIndex)
    self.mainConditionMap[questID] = mainIndex

    UI.Invoke("HUD_Task_Panel", "OnRefresh")
    if Game.NewUIManager:CheckPanelIsOpen("TaskBoardPanel") then
        --TODO：等获取界面接口有了后改掉
        Game.NewUIManager._openPanelMap.TaskBoardPanel:OnMainTargetIndexChange(questID)
    end
    Game.TraceSystem:RefreshQuestTracing()
end

---@param ringID number @任务环ID
---@param planeID number @位面ID
function QuestModel:OnMsgNowRingPlaneChanged(ringID, planeID)
    local questInfo = self.questList[ringID]
    questInfo.NowPlaneID = planeID

    Game.GlobalEventSystem:Publish(EEventTypesV2.QUEST_ON_NOWPLANE_CHANGED, questInfo.NowRingQuestID, planeID)
end

return QuestModel