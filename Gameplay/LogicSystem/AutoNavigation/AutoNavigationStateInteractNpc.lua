
local AutoNavigationState = kg_require("Gameplay.LogicSystem.AutoNavigation.AutoNavigationState")
local AutoNavigationStateInteractNpc = DefineClass("AutoNavigationStateInteractNpc", AutoNavigationState)
local AutoNavigationDefine = kg_require("Gameplay.LogicSystem.AutoNavigation.AutoNavigationDefine")
local NaviStateEnum = AutoNavigationDefine.NaviStateEnum

function AutoNavigationStateInteractNpc:ctor(owner)
end

function AutoNavigationStateInteractNpc:OnTick(deltaTime)
end

function AutoNavigationStateInteractNpc:OnEnter(preState)
    Game.HUDInteractManager:AutoInteractByNpcID(self.owner.targetNpcId)
end

function AutoNavigationStateInteractNpc:OnLeave(nextState)
end

function AutoNavigationStateInteractNpc:CanTranslate()
    return NaviStateEnum.None
end

return AutoNavigationStateInteractNpc