local AutoNavigationState = kg_require("Gameplay.LogicSystem.AutoNavigation.AutoNavigationState")
local KismetMathLibrary = import("KismetMathLibrary")
local EPathFollowingResult = import("EPathFollowingResult")
local EPathFollowingRequestResult = import("EPathFollowingRequestResult")
local AutoNavigationDefine = kg_require("Gameplay.LogicSystem.AutoNavigation.AutoNavigationDefine")
local NaviStateEnum = AutoNavigationDefine.NaviStateEnum
local NaviTypeEnum = AutoNavigationDefine.NaviTypeEnum
local NaviConst = AutoNavigationDefine.NaviConst

-- const
local MAX_TRY_COUNT = 5  -- luacheck: ignore
local CHECK_JUMPING_END_TIME = 100  -- luacheck: ignore
local HEURISTIC_THRESHOLD = 150  -- luacheck: ignore
local GRAVITY = 980  -- luacheck: ignore
local DIRECTIONS = {0, -45, 45, -90, 90, -135, 135, 180}  -- luacheck: ignore

-- smart move state
local STATE_IDLE = 0  -- luacheck: ignore
local STATE_WALKING = 1  -- luacheck: ignore
local STATE_JUMPING = 2  -- luacheck: ignore
local STATE_JUMPING_END = 3  -- luacheck: ignore

local AutoNavigationStateSmartMove = DefineClass("AutoNavigationStateSmartMove", AutoNavigationState)

function AutoNavigationStateSmartMove:ctor(owner)
end

function AutoNavigationStateSmartMove:OnTick(deltaTime)
    local curPos = self.owner:GetCurPos()

    if self.state == STATE_IDLE then
        if not self.exitState then
            self:exit(false)
        end
    elseif self.state == STATE_WALKING then
    elseif self.state == STATE_JUMPING_END then
        -- 要这个状态是因为，目前OnJumpFinish回调的时候玩家还没有落地
        if self.lastCheckStatusPos and KismetMathLibrary.Vector_DistanceSquared(curPos, self.lastCheckStatusPos) <= 1 then
            self.checkStatusTime = self.checkStatusTime + deltaTime
        else
            if self.checkStatusTime > 0 then
                Log.DebugFormat("[NAVI] jumping end block time [%s]", self.checkStatusTime)
            end
            self.checkStatusTime = 0
        end
        Log.DebugFormat("[NAVI] jumping end time [%s], curPos[%s]", self.checkStatusTime, curPos)
        if self.checkStatusTime >= CHECK_JUMPING_END_TIME then
            self:onMoveFinish()
        end
        self.lastCheckStatusPos = curPos
    end
end

function AutoNavigationStateSmartMove:OnEnter(preState)
    Log.InfoFormat("[NAVI] SmartMove OnEnter, destPos[%s]", self.owner.naviDestPos)
    self.state = STATE_IDLE
    self.exitState = nil
    self.checkStatusTime = 0
    self.lastCheckStatusPos = nil
    self.startPos = self.owner:GetCurFloorPos()
    _, self.destPos = self.owner:GetWalkableFloorPos(self.owner.naviDestPos)  -- luacheck: ignore
    local succ, jumpHeight, toPos = self:CalculateNextPoint(self.startPos, self.destPos)
    if not succ then
        self:exit(false)
        return
    end
    self.toPos = toPos

    if jumpHeight > 0 then
        self:JumpToNextPoint(self.toPos, jumpHeight)
    else
        self:optimizeTargetPoint()
        self:walkToNextPoint(self.toPos)
    end
    self.owner.smartMoveTryCount = self.owner.smartMoveTryCount + 1
end

function AutoNavigationStateSmartMove:OnLeave(nextState)
    if self.state == STATE_WALKING and Game.me then
        Game.me:StopPathFollow()
    end
end

function AutoNavigationStateSmartMove:CanTranslate()
    return self.exitState
end

function AutoNavigationStateSmartMove:CalculateNextPoint(curPos, destPos)
    self.owner:DrawCapsule(curPos, FLinearColor.Yellow, 10, true)

    local heuristic = KismetMathLibrary.Vector_Distance(curPos, destPos)

    -- TODO: 算法先验证效果，之后可以再优化效率
    -- 可能达到路网边缘，朝目标点方向或前进方向往前探测，如果有障碍物则跳跃
    local rayStart = FVector(curPos.X, curPos.Y, curPos.Z + NaviConst.AgentHalfHeight + 10) -- 需要中心坐标
    if self.owner:CanArriveDirectly(curPos, destPos) then
        local canPass, jumpHeight = self:checkCanPass(rayStart, destPos)
        if canPass then
            Log.DebugFormat("[NAVI] CalculateNextPoint, curPos[%s], destPos[%s], jumpHeight[%s]", curPos, destPos, jumpHeight)
            return true, jumpHeight, destPos
        end
    end

    local baseYaw = M3D.GetTargetYawByVector(curPos, destPos)
    for i = 1, 2 do
        for _, dir in pairs(DIRECTIONS) do
            local yaw = baseYaw + dir
            local detect_distance = NaviConst.SmartMoveDetectDistance * i
            local canGo, jumpHeight, toPos = self:checkCanGo(rayStart, destPos, dir, yaw, detect_distance, heuristic)
            if canGo then
                return true, jumpHeight, toPos
            end
        end
    end

    Log.DebugFormat("[NAVI] CalculateNextPoint failed, curPos[%s], destPos[%s]", curPos, destPos)
    return false
end

function AutoNavigationStateSmartMove:checkCanGo(startPos, destPos, dir, yaw, detect_distance, heuristic)
    local rayStart = startPos
    local rayEnd = self.owner:GetTowardPos(startPos, yaw, detect_distance)

    local canPass, jumpHeight = self:checkCanPass(rayStart, rayEnd)
    if not canPass then
        return false
    end

    local nextPos = self.owner:GetFloorPos(rayEnd)
    local succ, naviType, naviPath = self.owner:FindNavMeshPath(nextPos, destPos, false)
    if not succ or naviType == NaviTypeEnum.StartNotInMesh or naviType == NaviTypeEnum.BothNotInMesh then
        return false
    end

    local endPos = self.owner:GetPathEnd(naviPath)
    local distance = KismetMathLibrary.Vector_Distance(endPos, nextPos)
    local heuristic2 = KismetMathLibrary.Vector_Distance(endPos, destPos)
    if heuristic2 > self.owner.arriveRange
        and (distance < detect_distance + HEURISTIC_THRESHOLD or heuristic2 + HEURISTIC_THRESHOLD > heuristic) then
        return false
    end

    -- Log.DebugFormat("[NAVI] checkCanGo, dir[%s], yaw[%s], curPos[%s], nextPos[%s], endPos[%s], jumpHeight[%s], heuristic[%s], heuristic2[%s], distance[%s], detect_distance[%s]", 
    --     dir, yaw, curPos, nextPos, endPos, jumpHeight, heuristic, heuristic2, distance, detect_distance)
    return true, jumpHeight, nextPos
end

AutoNavigationStateSmartMove.__rayStartFVector = FVector()
AutoNavigationStateSmartMove.__rayEndFVector = FVector()
function AutoNavigationStateSmartMove:checkCanPass(start, dest)
    self.__rayStartFVector.X = start.X
    self.__rayStartFVector.Y = start.Y
    self.__rayStartFVector.Z = start.Z
    self.__rayEndFVector.X = dest.X
    self.__rayEndFVector.Y = dest.Y
    self.__rayEndFVector.Z = start.Z

    local bHit = self.owner:CapsuleRaycast(self.__rayStartFVector, self.__rayEndFVector)
    if not bHit then
        return true, 0
    end
    local delta = 50
    local maxIndex = math.ceil((Game.TableData.GetConstDataRow("AUTOPATH_MAX_JUMP_HEIGHT") + 100) / delta)
    for i = 1, maxIndex do
        local height = i * delta
        local worldZ = start.Z + height
        self.__rayStartFVector.Z = worldZ
        self.__rayEndFVector.Z = worldZ
        bHit = self.owner:CapsuleRaycast(self.__rayStartFVector, self.__rayEndFVector)
        if not bHit then
            return true, height
        end
    end
    return false
end

AutoNavigationStateSmartMove.__optimizeTargetPointFVector = FVector()
function AutoNavigationStateSmartMove:optimizeTargetPoint()
     -- 坠落无法落到准确位置，将目标点往前修，避免回头
    local height = self.startPos.Z - self.toPos.Z
    if height <= 0 then
        return
    end
    local fallingTime = math.sqrt(2 * height / GRAVITY)
    local distance2D = fallingTime * self.owner:GetNaviSpeed()

    local forward = M3D.Vec2(self.toPos.X - self.startPos.X, self.toPos.Y - self.startPos.Y)
    forward:Normalize()
    self.__optimizeTargetPointFVector.X = self.toPos.X + forward.X * distance2D
    self.__optimizeTargetPointFVector.Y = self.toPos.Y + forward.Y * distance2D
    self.__optimizeTargetPointFVector.Z = self.toPos.Z

    local succ, detectPos = self.owner:GetWalkableFloorPos(self.__optimizeTargetPointFVector)
    if not succ then
        return
    end
    local hitPos = LuaScriptAPI.NavMeshRaycast(Game.me.CharacterID, self.toPos.X, self.toPos.Y, self.toPos.Z, 
        detectPos.X, detectPos.Y, detectPos.Z)
    if hitPos == nil then
        hitPos = detectPos
    end
    self.owner:DrawCapsule(self.toPos, FLinearColor.Yellow, 10, true)
    Log.InfoFormat("[NAVI] optimizeTargetPoint startPos[%s], detectPos[%s], toPos[%s], newToPos[%s], height[%s], fallingTime[%s], distance2D[%s]", 
        self.startPos, detectPos, self.toPos, hitPos, height, fallingTime, distance2D)
    self.toPos = hitPos
end

function AutoNavigationStateSmartMove:walkToNextPoint(toPos)
    local curPos = self.owner:GetCurFloorPos()
    Log.InfoFormat("[NAVI] walkToNextPoint curPos[%s], toPos[%s]", curPos, toPos)
    self.owner:DrawCapsule(toPos, FLinearColor.Blue, 10, true)

    local result = Game.me:StartPathFollowLocation(toPos, -1, nil, self.owner:GetNaviSpeed(), true, false)
    if result == EPathFollowingRequestResult.Failed then
        Log.WarningFormat("[NAVI] walkToNextPoint failed, result[%s], toPos[%s]", result, toPos)
        return false
    elseif result == EPathFollowingRequestResult.AlreadyAtGoal then
        Log.InfoFormat("[NAVI] walkToNextPoint already at goal, result[%s], toPos[%s]", result, toPos)
        self:onMoveFinish()
        return true
    else
        Game.me:StartListenPathFollowResult(self, "OnWalkFinish")
        self.state = STATE_WALKING
        return true
    end
end

function AutoNavigationStateSmartMove:JumpToNextPoint(toPos, jumpHeight)
    local curPos = self.owner:GetCurFloorPos()
    Log.InfoFormat("[NAVI] JumpToNextPoint curPos[%s], toPos[%s], jumpHeight[%s]", curPos, toPos, jumpHeight)
    self.owner:DrawCapsule(toPos, FLinearColor.Blue, 10, true)

    -- self.teleportToNextPoint(toPos)

    local jumpToPos = M3D.Vec3()
    jumpToPos.X = toPos.X
    jumpToPos.Y = toPos.Y
    jumpToPos.Z = toPos.Z
    if not Game.me:PathFollowJumpToLocation(jumpToPos, jumpHeight, function() self:OnJumpFinish() end) then
        Log.WarningFormat("[NAVI] JumpToNextPoint failed, toPos[%s], jumpHeight[%s]", toPos, jumpHeight)
        return false
    end
    self.state = STATE_JUMPING
    return true
end

function AutoNavigationStateSmartMove:teleportToNextPoint(toPos)
    Game.me.CppEntity:KAPI_Movement_ReceiveSetLocationAndRotation(toPos.X, toPos.Y, toPos.Z, 0, 0, self.owner:GetCurYaw(), false, 0, false)
    self:exit(true)
end

function AutoNavigationStateSmartMove:onMoveFinish()
    self.state = STATE_IDLE
    local curPos = self.owner:GetCurFloorPos()
    if self.owner:CheckArrived(curPos, self.toPos) then
        Log.InfoFormat("[NAVI] onMoveFinish arrive pos")
        self.owner.smartMoveTryCount = 0
        self:exit(true)
        return
    end
    local succ, naviType = self.owner:FindNavMeshPath(curPos, self.toPos, true)
    if succ and naviType == NaviTypeEnum.CanArrive then
        Log.InfoFormat("[NAVI] onMoveFinish arrive mesh")
        self.owner.smartMoveTryCount = 0
        self:exit(true)
        return
    end
    Log.InfoFormat("[NAVI] onMoveFinish failed, naviType[%s], tryCount[%s], curPos[%s], toPos[%s]", 
        naviType, self.owner.smartMoveTryCount, curPos, self.toPos)
    self:exit(self.owner.smartMoveTryCount < MAX_TRY_COUNT)
end

function AutoNavigationStateSmartMove:OnJumpFinish()
    Log.InfoFormat("[NAVI] OnJumpFinish")
    -- self.state = STATE_JUMPING_END
    self:onMoveFinish()
end

function AutoNavigationStateSmartMove:OnWalkFinish(result, eid)
    if eid ~= Game.me.eid then
        return
    end
    Log.InfoFormat("[NAVI] OnWalkFinish result[%s], curPos[%s]", self:formatWalkResult(result), self.owner:GetCurFloorPos())
    Game.me:StopListenPathFollowResult(self, "OnWalkFinish")        
    self:onMoveFinish()
end

function AutoNavigationStateSmartMove:exit(succ)
    if self.state == STATE_WALKING then
        Game.me:StopListenPathFollowResult(self, "OnWalkFinish")
        Log.InfoFormat("[NAVI] stop path follow")
        Game.me:StopPathFollow()
    end

    if not succ or not self.owner.naviDestPos then
        Log.InfoFormat("[NAVI] smart move failed, succ[%s], dest[%s]", succ, self.owner.naviDestPos)
        self.owner:ReminderCanNotArrive()
        self.exitState = NaviStateEnum.None
        return
    end
    
    self.owner.naviType = NaviTypeEnum.CanArrive
    self.exitState = self.owner:GetNextState()
    Log.InfoFormat("[NAVI] smart move to [%s]", self.exitState)
end

function AutoNavigationStateSmartMove:formatWalkResult(result)
    if result == EPathFollowingResult.Success then
        return "Success"
    elseif result == EPathFollowingResult.Blocked then
        return "Blocked"
    elseif result == EPathFollowingResult.OffPath then
        return "OffPath"
    elseif result == EPathFollowingResult.Aborted then
        return "Aborted"
    else
        return result
    end
end

function AutoNavigationStateSmartMove:OnHasGroundSupportChanged(curHasGroundSupport, curHasLocoGroundSupport)
    local curPos = self.owner:GetCurFloorPos()
    Log.InfoFormat("[NAVI] OnHasGroundSupportChanged curPos[%s] curHasGroundSupport[%s]", curPos, curHasGroundSupport)
end

return AutoNavigationStateSmartMove