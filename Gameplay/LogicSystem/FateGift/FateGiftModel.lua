---@class FateGiftModel:SystemModelBase,FateGiftPostProcess @任务系统
local FateGiftModel = DefineClass("FateGiftModel", SystemModelBase)

function FateGiftModel:init()
	---@type table<number,FateGiftInfo> 奇遇列表
	self.FateGiftList = {}
	
	---@type table<levelID,FateGiftInfoList> 指定坐标-待触发奇遇列表
	self.checkPosDict = {}
	---@type table<actionID,FateGiftInfoList> 动作instance-待触发奇遇列表(满足触发条件时,从本列表内遍历尝试触发)
	self.checkActionDict = {}
	---@type table<dialogID,fortuityIDList> 完成dialog-待触发奇遇列表(满足触发条件时,从本列表内遍历尝试触发)
	self.checkDialogDict = {}
	---@type table<instanceID,fortuityID> 指定坐标生成的instance
	self.ins2fidMap = {}
	
	self.rewardCache = {} -- 奇遇奖励界面数据缓存
	self.params = {} -- 奇遇trigger参数解析缓存
	
	self.currFid = nil -- 当前正在播放的奇遇
end

function FateGiftModel:unInit()
	
end

function FateGiftModel:clear()
	self:initClientFortuitous()
	table.clear(self.ins2fidMap)
end

-- 初始化客户端需要负责检测触发的奇遇列表
function FateGiftModel:initClientFortuitous()
	table.clear(self.checkPosDict)
	table.clear(self.checkActionDict)
	table.clear(self.checkDialogDict)
	local fateGiftTable = Game.TableData.GetFortuityDataTable()
	for fid, fData in ksbcpairs(fateGiftTable) do
		local cmd = self:_parseTriggerCmd(fData.Trigger)
		if cmd == "OnCoordinatesPoint" then
			-- 在指定坐标点
			local levelID,x,y,z,r = self:_parseTriggerPara(fData.Trigger)
			if not self.checkPosDict[levelID] then
				self.checkPosDict[levelID] = {}
			end
			table.insert(self.checkPosDict[levelID], {fid = fData.ID,pos={X=x,Y=y,Z=z},r=r})
		elseif cmd == "OnDialogue" then
			-- 完成指定对话
			local dialogID = self:_parseTriggerPara(fData.Trigger)
			if not self.checkDialogDict[dialogID] then
				self.checkDialogDict[dialogID] = {}
			end
			table.insert(self.checkDialogDict[dialogID], fData.ID)
		elseif cmd == "OnInstanceBehavior" then
			-- 在指定npc旁做指定动作
			local instanceID,r,actionID = self:_parseTriggerPara(fData.Trigger)
			if not self.checkActionDict[actionID] then
				self.checkActionDict[actionID] = {}
			end
			table.insert(self.checkActionDict[actionID],{insID=instanceID,r=r,fid=fData.ID,level=fData.LevelMapID})
		end
	end
end
function FateGiftModel:_parseTriggerCmd(triggerStr)
	return triggerStr:match("^(.-)%s*%(")
end
function FateGiftModel:_parseTriggerPara(triggerStr)
	table.clear(self.params)
	local param = triggerStr:match("%(([^%)]+)%)")
	for p in string.gmatch(param, "[^,]+") do
		table.insert(self.params, tonumber(p))
	end
	return table.unpack(self.params)
end

-- 清理场景里的trigger
function FateGiftModel:clearTrigger()
	for insID, _ in pairs(self.ins2fidMap) do
		Game.WorldManager:RemoveTrigger(insID)
	end
	table.clear(self.ins2fidMap)
end

function FateGiftModel:onMsgFinishList()
	
end

return FateGiftModel