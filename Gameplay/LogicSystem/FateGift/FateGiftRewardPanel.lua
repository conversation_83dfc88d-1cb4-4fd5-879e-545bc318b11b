local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class FateGiftRewardPanel : UIPanel
---@field view FateGiftRewardPanelBlueprint
local FateGiftRewardPanel = DefineClass("FateGiftRewardPanel", UIPanel)

local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ESlateVisibility = import("ESlateVisibility")

FateGiftRewardPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FateGiftRewardPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FateGiftRewardPanel:InitUIData()
	self.timerID = nil -- 关闭timer

	self.RewardItemListParam = {}
end

--- UI组件初始化，此处为自动生成
function FateGiftRewardPanel:InitUIComponent()
	self.rewardGroup = self:CreateComponent(self.view.ListReward, UIListView)
end

---UI事件在这里注册，此处为自动生成
function FateGiftRewardPanel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "onBtn_ClickArea_luaClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FateGiftRewardPanel:InitUIView()
end

---面板打开的时候触发
function FateGiftRewardPanel:OnRefresh(...)
	local param = select(1, ...)
	self.view.KText_Title_lua:SetText(param.name or "")

	table.clear(self.RewardItemListParam)
	for i = 1, #param.reward do
		local reward = {
			id = param.reward[i].ItemID,
			num = param.reward[i].Count,
		}
		table.insert(self.RewardItemListParam, reward)
	end
	self.rewardGroup:Refresh(self.RewardItemListParam)

	local closeTime = 5
	self.timerID = self:StartTimer("fateStart", function()
		self:CloseSelf()
	end, closeTime * 1000,1)
end

function FateGiftRewardPanel:OnRefresh_RewardGroup(cell,idx)
	cell.view.ItemSelected_lua:SetVisibility(ESlateVisibility.Collapsed)
	cell.view.Big_Button_ClickArea_lua:SetVisibility(ESlateVisibility.Collapsed)
	cell.view.Canv_Reward_VX:SetVisibility(ESlateVisibility.Collapsed)
end


--- 此处为自动生成
function FateGiftRewardPanel:onBtn_ClickArea_luaClicked()
	self:CloseSelf()
end

function FateGiftRewardPanel:OnClose()
	if self.timerID then
		self:StopTimer(self.timerID)
	end
	self.timerID = nil
end

function FateGiftRewardPanel:FillItem(itemView, id, num)
	itemView:SetStatus(Enum.ItemStatus.Normal)

	if not id then
		return
	end

	local ItemExcelData = Game.TableData.GetItemNewDataRow(id)
	if not ItemExcelData then
		return
	end

	itemView:SetNew(false)

	-- 物品图标
	local IconPath = Game.UIIconUtils.GetIconByItemId(id, Enum.EUIIconType.Default)
	if IconPath then
		self:SetImage(itemView.Icon, IconPath)
	end

	-- 品质
	local Quality = ItemExcelData.quality
	itemView:SetQuality(Quality)
	--if Quality >= 4 then
	--	itemView.NS_HQ:SetVisibility(ESlateVisibility.HitTestInvisible)
	--	self:GetItemComponent(Quality)
	--else
	--	itemView.NS_HQ:SetVisibility(ESlateVisibility.Collapsed)
	--	self:PushItemComponent(Quality)
	--end

	--- 数量
	local Color = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.SlateColor)
	if Color then
		itemView.TB_Text:SetColorAndOpacity(Color)
	end

	if num then
		if type(num) == "number" then
			if num > 1 then
				itemView.TB_Text:SetVisibility(ESlateVisibility.selfHitTestInvisible)
				itemView.TB_Text:SetText(Game.CurrencyUtils.GetGameMoneyFormat(num))
			elseif num == -1 then
				itemView.TB_Text:SetVisibility(ESlateVisibility.selfHitTestInvisible)
				itemView.TB_Text:SetText("?")
			else
				itemView.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
			end
		elseif type(num) == "string" then
			itemView.TB_Text:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			itemView.TB_Text:SetText(num)
		else
			itemView.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		itemView.TB_Text:SetVisibility(ESlateVisibility.Collapsed)
	end

	--- 物品名字
	itemView.TB_Name:SetVisibility(ESlateVisibility.Collapsed)

	--- 左上角图标：可交易/装备/不显示
	local LeftUpType = Enum.ItemLeftUpIconType.None
	itemView:SetLT(LeftUpType)

	--- 右上角图标
	itemView:SetRT(Enum.RightUpIconType.None)
	--装备锁定
	itemView:SetRB(false)
end

return FateGiftRewardPanel
