local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class UIListViewExampleItem : UIListItem
---@field view UIListViewExampleItemABlueprint
local UIListViewExampleItem = DefineClass("UIListViewExampleItem", UIListItem)

UIListViewExampleItem.eventBindMap = {
}

---面板打开的时候触发
function UIListViewExampleItem:OnRefresh(data)
    self.view.KGTextBlock:SetText(data)
end

---更新选择的业务表现
---@field selected bool
function UIListViewExampleItem:UpdateSelectionState(selected)
    local text = selected and "selected" or self.data
    self.view.KGTextBlock:SetText(text)
end
return UIListViewExampleItem
