local UIComCurrencyItem = kg_require("Framework.KGFramework.KGUI.Component.Tag.UIComCurrencyItem")
local UIComPagePointList = kg_require("Framework.KGFramework.KGUI.Component.Indicator.UIComPagePointList")
local UIComPageNum = kg_require("Framework.KGFramework.KGUI.Component.Indicator.UIComPageNum")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class OtherExample : UIComponent
---@field view OtherExampleBlueprint
local OtherExample = DefineClass("OtherExample", UIComponent)

OtherExample.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function OtherExample:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function OtherExample:InitUIData()
end

--- UI组件初始化，此处为自动生成
function OtherExample:InitUIComponent()
    ---@type UIComCurrencyItem
    self.WBP_ComCurrencyCom = self:CreateComponent(self.view.WBP_ComCurrency, UIComCurrencyItem)
    ---@type UIComPagePointList
    self.WBP_ComIndictorPointCom = self:CreateComponent(self.view.WBP_ComIndictorPoint, UIComPagePointList)
    ---@type UIComPageNum
    self.WBP_ComIndicatorCom = self:CreateComponent(self.view.WBP_ComIndicator, UIComPageNum)
end

---UI事件在这里注册，此处为自动生成
function OtherExample:InitUIEvent()
    self:AddUIEvent(self.WBP_ComIndicatorCom.onPageChange, "on_WBP_ComIndicatorCom_PageChange")
    self:AddUIEvent(self.WBP_ComIndictorPointCom.onPageChange, "on_WBP_ComIndictorPointCom_PageChange")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function OtherExample:InitUIView()
    self.WBP_ComIndictorPointCom:Refresh(10)
    self.WBP_ComIndicatorCom:Refresh(1, 20)
    self.WBP_ComCurrencyCom:SetMoneyNum(10,100)
    self.WBP_ComCurrencyCom:SetUIType(Enum.UIComCurrencyType.Discount)
end

---组件刷新统一入口
function OtherExample:Refresh(...)
end

--- 此处为自动生成
---@param index number
function OtherExample:on_WBP_ComIndicatorCom_PageChange(index)
    Log.Debug("on_WBP_ComIndicatorCom_PageChange:", index)
end

--- 此处为自动生成
---@param index number
function OtherExample:on_WBP_ComIndictorPointCom_PageChange(index)
    Log.Debug("on_WBP_ComIndictorPointCom_PageChange:", index)
end

return OtherExample
