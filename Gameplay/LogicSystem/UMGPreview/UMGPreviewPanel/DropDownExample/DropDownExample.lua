local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class DropDownExample : UIComponent
---@field view DropDownExampleBlueprint
local DropDownExample = DefineClass("DropDownExample", UIComponent)

DropDownExample.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DropDownExample:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DropDownExample:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DropDownExample:InitUIComponent()
    ---@type UIComDropDown
    self.WBP_ComSelectDropType1Com = self:CreateComponent(self.view.WBP_ComSelectDropType1, UIComDropDown)
    ---@type UIComDropDown
    self.WBP_ComSelectDropType2Com = self:CreateComponent(self.view.WBP_ComSelectDropType2, UIComDropDown)
end

---UI事件在这里注册，此处为自动生成
function DropDownExample:InitUIEvent()
    self:AddUIEvent(self.WBP_ComSelectDropType1Com.onItemSelected, "on_WBP_ComSelectDropType1Com_ItemSelected")
    self:AddUIEvent(self.WBP_ComSelectDropType2Com.onItemSelected, "on_WBP_ComSelectDropType2Com_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DropDownExample:InitUIView()
    local datas = self:getDropDownData()
    self.WBP_ComSelectDropType1Com:Refresh(datas, 1)
    self.WBP_ComSelectDropType2Com:Refresh(datas, 34)
end

---组件刷新统一入口
function DropDownExample:Refresh()
end

function DropDownExample:getDropDownData()
    local datas = {}
    for i = 1, 100 do
        local data = UIComDropDown.NewOptionData(i)
        datas[#datas + 1] = data
    end
    return datas
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function DropDownExample:on_WBP_ComSelectDropType1Com_ItemSelected(index, data)
    Log.Debug("on_WBP_ComSelectDropType1Com_ItemSelected", index)
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function DropDownExample:on_WBP_ComSelectDropType2Com_ItemSelected(index, data)
    Log.Debug("on_WBP_ComSelectDropType2Com_ItemSelected", index)
end

return DropDownExample
