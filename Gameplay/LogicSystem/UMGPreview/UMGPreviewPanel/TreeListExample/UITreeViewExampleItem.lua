local UITreeItem = kg_require("Framework.KGFramework.KGUI.Component.UITreeView.UITreeItem")
---@class UITreeViewExampleItem : UIListItem
---@field view UIListViewExampleItemBBlueprint
local UITreeViewExampleItem = DefineClass("UITreeViewExampleItem", UITreeItem)

UITreeViewExampleItem.eventBindMap = {
}

---面板打开的时候触发
---@param data UITreeViewChildData
function UITreeViewExampleItem:OnRefresh(data)
    self.view.KGTextBlock:SetText(data.tabData.name)
end

---更新选择的业务表现
---@field selected bool
function UITreeViewExampleItem:UpdateSelectionState(selected)
    if self:HasChild() then
        return
    end
    local text = selected and "selected" or self.data.tabData.name
    self.view.KGTextBlock:SetText(text)
end

---@public
---更新展开的业务表现
---@param expanded bool
function UITreeViewExampleItem:UpdateExpansionState(expanded)
    if not self:HasChild() then
        return
    end
    local text = expanded and (self.data.tabData.name .. "Open") or (self.data.tabData.name .. "Close")
    self.view.KGTextBlock:SetText(text)
end
return UITreeViewExampleItem
