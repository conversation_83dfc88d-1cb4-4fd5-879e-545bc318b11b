---@class TriggerTransitSystemSystem:SystemBase
local TriggerTransitSystem = DefineClass("TriggerTransitSystem",SystemBase)
local TriggerUtils = kg_require("Shared.Utils.TriggerUtils")


TriggerTransitSystem.RegisterCallback = {
	[Enum.ETriggerTypeData.INTERACTOR_TASK_ACTIVE] = "Register2Quest",
}
TriggerTransitSystem.UpdateFunction = {
	[Enum.ETriggerTypeData.INTERACTOR_TASK_ACTIVE] = "UpdateWithQuest",
}
TriggerTransitSystem.UnRegisterCallback = {
	[Enum.ETriggerTypeData.INTERACTOR_TASK_ACTIVE] = "UnRegister2Quest",
}


---@private
function TriggerTransitSystem:onCtor()
	
end

---@private
function TriggerTransitSystem:onInit()
	---@type table<number,function> @交互物Condition注册
	self.sceneActor2ConditionDict = {}
	self.condition2SceneActorDict = {}
	
	Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_LEVEL_CHANGED, self, self.LevelUp, GetMainPlayerEID())
end

---@private
function TriggerTransitSystem:onUnInit()
	self.sceneActor2ConditionDict = {}
	self.condition2SceneActorDict = {}
end

---@param insID number 采集物ID
---@param condID number TriggerCustomData注册的conditionID
---@param bNeedRegister boolean 是否需要注册
---@param callback function<string,boolean,number> 触发Trigger时的回调
---@return boolean 是否完成
function TriggerTransitSystem:CheckSceneActorCondition(insID, condID, bNeedRegister, callback)
	if bNeedRegister then
		self:RegisterSceneActorCondition(insID, condID, callback)
	end
	return TriggerTransitSystem.CanTriggerCompleted(Game.me, 0, 0, condID)
end

function TriggerTransitSystem:RegisterSceneActorCondition(insID, condID, callback)
	self.sceneActor2ConditionDict[insID] = { condID = condID, callback = callback }
	
	local info = Game.TableData.GetTriggerCustomDataRow(condID)
	if not info then
		Log.Error("TriggerTransitSystem:RegisterSceneActorCondition cannot find data, condID:"..condID)
		return
	end
	for k,v in ksbcpairs(info.ConditionList) do
		local triggerEnum = v.ConditionID
		local registerCallback = TriggerTransitSystem.RegisterCallback[triggerEnum]
		if registerCallback and type(self[registerCallback]) == "function" then
			self[registerCallback](self,insID, condID)
		end
		if not self.condition2SceneActorDict[triggerEnum] then
			self.condition2SceneActorDict[triggerEnum] = {}
		end
		self.condition2SceneActorDict[triggerEnum][insID] = true
	end
end

function TriggerTransitSystem:UnregisterSceneActorCondition(insID)
	local curRegisterDict = self.sceneActor2ConditionDict[insID]
	if curRegisterDict and curRegisterDict.condID then
		local info = Game.TableData.GetTriggerCustomDataRow(curRegisterDict.condID)
		for _,v in ksbcpairs(info.ConditionList) do
			local triggerEnum = v.ConditionID
			local unRegisterCallback = TriggerTransitSystem.UnRegisterCallback[triggerEnum]
			if unRegisterCallback and type(self[unRegisterCallback]) == "function" then
				self[unRegisterCallback](self, insID, curRegisterDict.condID)
			end
			if self.condition2SceneActorDict[triggerEnum] then
				self.condition2SceneActorDict[triggerEnum][insID] = nil
			end
		end
	end
	
	self.sceneActor2ConditionDict[insID] = nil
end

function TriggerTransitSystem.GetAllArgsValue(condID, condIndex)
	local info = Game.TableData.GetTriggerCustomDataRow(condID)
	if not info then
		Log.Error("TriggerTransitSystem.GetAllArgsValue cannot find data, condID:"..condID)
		return {}
	end
end

function TriggerTransitSystem.GetArgsValue(condID, condIndex, argIndex)
	local condInfo = Game.TableData.GetTriggerCustomDataRow(condID)
	if condInfo and condInfo.ConditionList[condIndex] and condInfo.ConditionList[condIndex]["ConditionFuncInfo"]["FuncArgInfos"][argIndex] then
		return condInfo.ConditionList[condIndex]["ConditionFuncInfo"]["FuncArgInfos"][argIndex] or 0
	else
		--Log.Error("TriggerTransitSystem.GetArgsValueInCondition cannot find data, condID:"..condID..", condIndex:"..condIndex..", argIndex:"..argIndex)
		return 0
	end
end

function TriggerTransitSystem.HaveTriggerEnum(condID, enum)
	local info = Game.TableData.GetTriggerCustomDataRow(condID)
	if not info then
		Log.Error("TriggerTransitSystem.HaveTriggerEnum cannot find data, condID:"..condID)
		return false
	end
	local needIdx
	for idx,value in ksbcipairs(info.ConditionList) do
		if value.ConditionID == enum then
			needIdx = idx
			break
		end
	end
	return needIdx
end

---@param triggerEnum 来自 Enum.ETriggerTypeData 描述具体触发
---@param params List
---@return void
function TriggerTransitSystem:ClientTriggerUpdate(triggerEnum, params)
	if params == nil then
		params = {}
	end
	if not self.condition2SceneActorDict[triggerEnum] then
		return
	end
	for key,v in pairs(self.condition2SceneActorDict[triggerEnum]) do
		if v then
			local updateFunction = TriggerTransitSystem.UpdateFunction[triggerEnum]
			if updateFunction and type(self[updateFunction]) == "function" then
				self[updateFunction](self, key, params)
			else
				local actorItem = self.sceneActor2ConditionDict[key]
				local idx = TriggerTransitSystem.HaveTriggerEnum(actorItem.condID, triggerEnum)
				if idx and actorItem.callback then
					local bSuccess = TriggerTransitSystem.CanTriggerCompleted(Game.me, 0, 0, actorItem.condID)
					actorItem.callback(key, bSuccess, actorItem.condID)
				end
			end
		end
	end
end

---@param triggerEnum 来自 Enum.ETriggerTypeData 描述具体触发
---@param params List
---@return number,boolean value,isGetValueSuccessed
function TriggerTransitSystem.ClientGetTriggerValue(triggerEnum, params)
	local func = TriggerUtils.TRIGGER_CONDITION_FUNC[triggerEnum]
	if func and Game.me then
		local triggerParams = {}
		if type(params) == "table" then
			for idx,value in ipairs(params) do
				triggerParams[idx] = {["value"] = value}
			end
		else
			Log.Debug("TriggerTransitSystem:ClientTriggerUpdate params type error")
			triggerParams[1] = {["value"] = params}
		end
		return func(Game.me, triggerParams), true
	end
	return 0, false
end

-- 查询接口，是否完成condition
function TriggerTransitSystem.CanTriggerCompleted(avatar, system, key, customID)
	return TriggerUtils.CanTriggerCompleted(avatar, system, key, customID)
end

-- 查询接口，返回一个condition内部的单个条件是否满足
---@param avatar
---@param system Enum.TriggerModuleType
---@param key number 后端注册的key
---@param customID number TriggerCustomData的ID
---@param condIndex number 内部第几个条件
---@return boolean
function TriggerTransitSystem.CanSingleConditionCompleted(avatar, system, key, customID, condIndex)
	return TriggerUtils.CanSingleConditionCompleted(avatar, system, key, customID, condIndex)
end

-- 查询接口，返回一个condition内部的所有条件的情况，注意此处不会区分ConditionExpression表达式，仅返回具体值
---@return table
function TriggerTransitSystem.GetAllSingleTriggerInfo(avatar, system, key, customID)
	return TriggerUtils.GetAllSingleTriggerInfo(avatar, system, key, customID)
end

--region 客户端刷新机制
function TriggerTransitSystem.LevelUp()
	Game.TriggerTransitSystem:ClientTriggerUpdate(Enum.ETriggerTypeData.LEVELUP, {})
end
--endregion

--region 额外的自定义callback
function TriggerTransitSystem.NeedRegist2Quest(condID)
	return TriggerTransitSystem.HaveTriggerEnum(condID, Enum.ETriggerTypeData.INTERACTOR_TASK_ACTIVE)
end
function TriggerTransitSystem:Register2Quest(insID, condId)
	local questRegIdx = TriggerTransitSystem.NeedRegist2Quest(condId)
	if questRegIdx then
		local templateID = TriggerTransitSystem.GetArgsValue(condId, questRegIdx,1)
		Game.QuestSystem:CheckInteractorCondition(templateID, true)
	end
end
function TriggerTransitSystem:UnRegister2Quest(insID, condId)
	local questRegIdx = TriggerTransitSystem.NeedRegist2Quest(condId)
	if questRegIdx then
		local templateID = TriggerTransitSystem.GetArgsValue(condId, questRegIdx,1)
		Game.QuestSystem:UnregisterInteractorCondition(templateID)
	end
end
function TriggerTransitSystem:UpdateWithQuest(insID, params)
	local actorItem = self.sceneActor2ConditionDict[insID]
	local questRegIdx = TriggerTransitSystem.NeedRegist2Quest(actorItem.condID)
	if questRegIdx then
		local templateID = TriggerTransitSystem.GetArgsValue(actorItem.condID, questRegIdx,1)
		if params[1] == templateID and actorItem.callback then
			local bSuccess = TriggerTransitSystem.CanTriggerCompleted(Game.me, 0, 0, actorItem.condID)
			actorItem.callback(insID, bSuccess, actorItem.condID)
		end
	end
end
--endregion

return TriggerTransitSystem