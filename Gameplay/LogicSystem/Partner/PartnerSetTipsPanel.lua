local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PartnerSetTipsPanel : UIPanel
---@field view PartnerSetTipsPanelBlueprint
local PartnerSetTipsPanel = DefineClass("PartnerSetTipsPanel", UIPanel)



PartnerSetTipsPanel.eventBindMap = {
    [EEventTypesV2.ON_CLUB_DISTURB_CHANGE] = "OnClubNoDisturbChange",
    [EEventTypesV2.ON_FRIEND_IS_NODISTURB_CHANGE] = "OnFriendDisturbChange",
    [EEventTypesV2.ON_SET_TIPS_CHANGE_GROUP] = "OnRefresh",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerSetTipsPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerSetTipsPanel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PartnerSetTipsPanel:InitUIComponent()
    ---@type UISimpleList
    self.VBCom = self:CreateComponent(self.view.VB, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function PartnerSetTipsPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerSetTipsPanel:InitUIView()
end

---面板打开的时候触发
function PartnerSetTipsPanel:OnRefresh(params)
    self.SelectData = params.SelectData or {}
    self.ClickCallBack = params.ClickCallBack
    self:SetPosition(params.pos)
    -- 设置数据
    self.VBCom:Refresh(self.SelectData, self.ClickCallBack)

    --设置箭头按钮 已删除, 改为UI_Style_Type PartnerSetTipsBtnItem.STYLE_TYPE.WITH_ARROW
end


function PartnerSetTipsPanel:SetPosition(pos)
    if pos then
        self.view.SB.Slot:SetPosition(pos)
        self.pos = pos
    end
end

function PartnerSetTipsPanel:RefreshIndex(index, bChecked)
    --[[local uiComponent = self.SelectList:GetRendererAt(index)
    if uiComponent then
        uiComponent:UpdateCheckBox(bChecked)
    end]]
    self.VBCom:SetSelectedItemByIndex(index, bChecked, true)
end

function PartnerSetTipsPanel:OnClubNoDisturbChange(clubId, isNotDisturb)
    --self:RefreshIndex(1, isNotDisturb)
end

function PartnerSetTipsPanel:OnFriendDisturbChange(entityID, isSet)
    --self:RefreshIndex(1, isSet)
end

return PartnerSetTipsPanel
