local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local PartnerEditPanel = kg_require("Gameplay.LogicSystem.Partner.PartnerEditPanel")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PartnerScopeItem : UIListItem
---@field view PartnerScopeItemBlueprint
local PartnerScopeItem = DefineClass("PartnerScopeItem", UIListItem)

local Game = Game

PartnerScopeItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerScopeItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerScopeItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PartnerScopeItem:InitUIComponent()
    ---@type SocialHead
    self.WBP_HeadCom = self:CreateComponent(self.view.WBP_Head, SocialHead)
    ---@type UIComButtonItem
    self.WBP_AddBtnCom = self:CreateComponent(self.view.WBP_AddBtn, UIComButtonItem)
    ---@type UIComCheckBox
    self.WBP_CheckBoxCom = self:CreateComponent(self.view.WBP_CheckBox, UIComCheckBox)
end

---UI事件在这里注册，此处为自动生成
function PartnerScopeItem:InitUIEvent()
    self:AddUIEvent(self.WBP_AddBtnCom.onClickEvent, "on_WBP_AddBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_CheckBoxCom.onCheckChanged, "on_WBP_CheckBoxCom_CheckChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerScopeItem:InitUIView()
end

---面板打开的时候触发
function PartnerScopeItem:OnRefresh(data)
    self.ScopeType = data.scopeType
    self.userWidget:Event_UI_Style(data.itemStyle)
    if data.emptyText then
        self.view.TB_Word:SetText(data.emptyText)
    end
    self.rootPanel = data.rootPanel
    local info = data.info
    if info then
        self.Info = info
        self.isSet = info.isSet

        self.view.Text_Name:SetText(info.name)

        local _, onlineCount, showCount = Game.FriendSystem:GetFriendRelationList(info.id)
        self.view.RichText_SignName:SetText(onlineCount .. "/" .. showCount)

        self.WBP_CheckBoxCom:Refresh("", self.isSet, false)
    end
end


--- 此处为自动生成
function PartnerScopeItem:on_WBP_AddBtnCom_ClickEvent()
    local state, data
    if self.ScopeType == Game.FriendSystem.EScopeType.Friend then
        state = Game.FriendSystem.EScopeEditType.AddFriend
        data = Game.FriendSystem:GetInOrOutRangeList(Game.FriendSystem.EScopeType.Friend, false, self.rootPanel:GetTempSelectItem(Game.FriendSystem.EScopeType.Friend))	--luacheck:ignore

        if not Game.NewUIManager:IsShow(UIPanelConfig.PartnerEditPanel) then
            Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerEditPanel, {
                data = data,
                state = state,
                panelType = PartnerEditPanel.EDIT_PANEL_TYPE.SCOPE_EDIT,
                styleType = 2,
                rootEditPanel = self.rootPanel,
            })
        end
    end

end


--- 此处为自动生成
---@param checked bool
function PartnerScopeItem:on_WBP_CheckBoxCom_CheckChanged(checked)
    self.isSet = checked
    if self.rootPanel:SetTempSelectItem(self.ScopeType, self.Info.id, self.isSet) then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_CLIENT_SET_SHOW_STATUS)
    end
end

return PartnerScopeItem
