local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

local const = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")

local Enum = Enum
local Game = Game

---@class PartnerGivePanel : UIPanel
---@field view PartnerGivePanelBlueprint
local PartnerGivePanel = DefineClass("PartnerGivePanel", UIPanel)

PartnerGivePanel.eventBindMap = {
    -- [EEventTypes.ON_GIVE_GIFT_SUCCESS] = "RetOnGiveGiftSuccess",
    [EEventTypesV2.CHECK_BOTH_WAY_FRIENDSHIP] = "OnCheckBothwayFriendRespond",
    [EEventTypes.ON_RET_BUY_GOODS_SUCCESS] = "RetOnBuyGiftSuccess"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerGivePanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerGivePanel:InitUIData()
    self.currentGiftItemID = nil
    self.currentGiftID = nil
    self.giftListData = {}

    ---@type int 送礼界面类型
    self.nType = Enum.SendGiftType.Normal
end

--- UI组件初始化，此处为自动生成
function PartnerGivePanel:InitUIComponent()
    ---@type UIListView
    self.ListViewEx_GiftListCom = self:CreateComponent(self.view.ListViewEx_GiftList, UIListView)
    ---@type UIComNumberSlider
    self.WBP_ComSliderCom = self:CreateComponent(self.view.WBP_ComSlider, UIComNumberSlider)
    ---@type UIComButton
    self.WBP_ComBtnHightLight_AcceptCom = self:CreateComponent(self.view.WBP_ComBtnHightLight_Accept, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function PartnerGivePanel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnHightLight_AcceptCom.onClickEvent, "on_WBP_ComBtnHightLight_AcceptCom_ClickEvent")
    self:AddUIEvent(self.ListViewEx_GiftListCom.onItemSelected, "on_ListViewEx_GiftListCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComSliderCom.onValueChange, "on_WBP_ComSliderCom_ValueChange")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerGivePanel:InitUIView()
    self.WBP_ComSliderCom.userWidget:SetVisibility(ESlateVisibility.Hidden)
    self.view.TextBlock_Name:SetVisibility(ESlateVisibility.Collapsed)
end

---面板打开的时候触发
function PartnerGivePanel:OnRefresh(id, friendName, nType)
    self.doneCheckBothWayFriend = false
    self.currentSelectedIndex = nil
    self.isUIShow = true
    self.friendEntityID = id     --传入好友id，这样才可以知道和谁进行对接。
    self.friendName = friendName --传入好友的名字
    self.view.RT_GiveNam:SetText(StringConst.Get("SOCIAL_FRIEND_GIFT_GIVE_TIPS", self.friendName))
    self.WBP_ComBtnHightLight_AcceptCom:SetName(StringConst.Get("SOCIAL_FRIEND_GIFT_GIVE_BUTTON"))
    self.selectGiftCount = 0
    self.countMaxCurrent = 0
    self.nType = nType

    self:UpdateGiftList(1)
end


function PartnerGivePanel:UpdateGiftList(sendRefresh)
    self:OnRefreshGiftList()

    if sendRefresh == 1 then
        self.ListViewEx_GiftListCom:SetSelectedItemByIndex(1, true)
    elseif sendRefresh == 2 then
        if self.countMaxCurrent > 0 then
            self:SetGiftSelectCount(1)
        else
            self:SetGiftSelectCount(0)
        end
    elseif sendRefresh == 3 then
        self.WBP_ComSliderCom:on_Slider_ValueChanged(self.countMaxCurrent)
        self:SetGiftSelectCount(self.countMaxCurrent)
    end
end

function PartnerGivePanel:SetGiftSelectCount(GiftSelectCount)
    self.selectGiftCount = GiftSelectCount
    if GiftSelectCount > self.countMaxCurrent then
        self.view.TextBlock_Name:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.WBP_ComBtnHightLight_AcceptCom:SetName(StringConst.Get("SOCIAL_FRIEND_GIFT_BUY_BUTTON"))
    else
        self.view.TextBlock_Name:SetVisibility(ESlateVisibility.Collapsed)
        self.WBP_ComBtnHightLight_AcceptCom:SetName(StringConst.Get("SOCIAL_FRIEND_GIFT_GIVE_BUTTON"))
    end
end

function PartnerGivePanel:OnRefreshGiftList()
    local GiftTableData = Game.TableData.GetGiftDataTable()
    table.clear(self.giftListData)
    for _, GiftData in ksbcipairs(GiftTableData) do
        if not (self.nType == Enum.SendGiftType.TeaRoom and GiftData.type ~= 2) then
            local itemID = GiftData.itemId
            local index = #self.giftListData + 1
            local itemBoxData = {
                index = index,
                id = itemID,
                state = Enum.GridState.Occupy,
                clickType = Enum.CommonItemClickType.OverrideTip,
                selected = false,
                num = Game.BagSystem:GetItemCount(itemID),
                deleltBtn = false,
                giftID = GiftData.ID,
                giftName = GiftData.name
            }

            table.insert(self.giftListData, itemBoxData)
        end
    end
    self.ListViewEx_GiftListCom:Refresh(self.giftListData)
end

function PartnerGivePanel:GiveGift()
    if self.selectGiftCount == 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ITEM_NOT_ENOUGH)
        return
    end
    local AvatarActor = Game.me
    if AvatarActor then
        if self.nType == Enum.SendGiftType.Normal then
            local friendInfo = Game.FriendSystem:GetRelationInfoByEntityID(self.friendEntityID)
            if friendInfo then
                if friendInfo.state ~= const.FRIEND_SYSTEM_PLAYER_STATE.DEL then
                    AvatarActor:ReqGiveGift(self.friendEntityID, self.currentGiftID, self.selectGiftCount)
                else
                    Game.ReminderManager:AddReminderById(
                            Enum.EReminderTextData.FRIEND_GIVE_FAIL_OFFLINE
                    )
                end
            else
                AvatarActor:ReqGiveGift(self.friendEntityID, self.currentGiftID, self.selectGiftCount)
            end
        elseif self.nType == Enum.SendGiftType.TeaRoom then
            Game.TeaRoomSystem:ReqTeaRoomGiveGift(self.friendEntityID, self.selectGiftCount, self.currentGiftID)
        end
    end
end

function PartnerGivePanel:RetOnGiveGiftSuccess(entityID, giftID, giftCount, name)
    self:UpdateGiftList(2)
end

function PartnerGivePanel:RetOnBuyGiftSuccess()
    self:UpdateGiftList(3)
end

function PartnerGivePanel:OnCheckBothwayFriendRespond(isBothWayFriend, playerEntityID)
    if self.isUIShow == true and self.doneCheckBothWayFriend == true then
        local isBothWayFriendValue = isBothWayFriend
        self.doneCheckBothWayFriend = false
        if isBothWayFriendValue[1] == true then
            self:GiveGift()
        else
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.BLACKLIST_GIVE, function()
                self:GiveGift()
            end)
        end
    end
end

--- 此处为自动生成
function PartnerGivePanel:on_WBP_ComBtnCloseCom_ClickEvent()
    self.isUIShow = false
    self:CloseSelf()
end

--- 此处为自动生成
function PartnerGivePanel:on_WBP_ComBtnHightLight_AcceptCom_ClickEvent()
    if self.selectGiftCount == 0 then
        if self.countMaxCurrent == 0 then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ITEM_NOT_ENOUGH)
        end
        return
    end

    if self.selectGiftCount > self.countMaxCurrent then
        Log.Debug("P_PartnerGive:礼物数量超过", self.countMaxCurrent, self.countMaxCurrent)

        local DepartmentStoreTable = Game.TableData.GetNpcGoodsDataTable()
        local DepartmentStoreDetails

        for _,key in ksbcpairs(DepartmentStoreTable) do
            if key.ItemID == self.currentGiftItemID then
                DepartmentStoreDetails = key
                break
            end
        end
        if DepartmentStoreDetails then
            local tokenid, num
            for key,value in ksbcpairs(DepartmentStoreDetails.TokenIDs) do
                tokenid = key
                num = value
            end
            local buyNum = self.selectGiftCount - self.countMaxCurrent
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SOCIAL_FRIEND_GIFT_BUY, function()
                Game.DepartmentStoreSystem:IntoPurchaseProcess(DepartmentStoreDetails.ID, DepartmentStoreDetails.ShopID, buyNum)
            end,nil,nil,nil,{
                [1] = {
                    propId = tokenid,
                    num = num*(self.selectGiftCount - self.countMaxCurrent),
                    item = {itemID = self.currentGiftItemID, count = (self.selectGiftCount - self.countMaxCurrent)},
                    note2 = StringConst.Get("SOCIAL_FRIEND_GIFT_CONFIRM_NOTE2")
                }
            }
            )
        else
            Log.Debug("P_PartnerGive:Cannot find DepartmentStore Row")
        end
        return
    end

    if self.nType == Enum.SendGiftType.Normal then
        local AvatarActor = Game.me
        if AvatarActor then
            self.doneCheckBothWayFriend = true
            AvatarActor:doCheckBothwayFriendRequest(self.friendEntityID)
        end
    else
        self:GiveGift()
    end
end


--- 此处为自动生成
---@param index number
---@param data table
function PartnerGivePanel:on_ListViewEx_GiftListCom_ItemSelected(index, data)
    self.currentSelectedIndex = index
    local initCount = 0
    local giftCount = data.num and data.num or 0
    if giftCount > 0 then
        initCount = 1
    end
    self.currentGiftID = data.giftID
    self.currentGiftItemID = data.id
    self.countMaxCurrent = giftCount
    if self.WBP_ComSliderCom.userWidget:GetVisibility() == ESlateVisibility.Hidden then
        self.WBP_ComSliderCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    self.WBP_ComSliderCom:Refresh(initCount, 0, giftCount, 1)
    self:SetGiftSelectCount(initCount)
end


--- 此处为自动生成
---@param value number
function PartnerGivePanel:on_WBP_ComSliderCom_ValueChange(value)
    self:SetGiftSelectCount(value)
end

return PartnerGivePanel
