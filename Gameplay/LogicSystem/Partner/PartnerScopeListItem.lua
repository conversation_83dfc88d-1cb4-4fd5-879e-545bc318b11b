local PartnerScopeItem = kg_require("Gameplay.LogicSystem.Partner.PartnerScopeItem")
local ComSelectTitle = kg_require("Framework.KGFramework.KGUI.Component.Select.ComSelectTitle")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local PartnerEditPanel = kg_require("Gameplay.LogicSystem.Partner.PartnerEditPanel")

local ESlateVisibility = import("ESlateVisibility")
local Game = Game

---@class PartnerScopeListItem : UIListItem
---@field view PartnerScopeListItemBlueprint
local PartnerScopeListItem = DefineClass("PartnerScopeListItem", UIListItem)

PartnerScopeListItem.LIST_TYPE = {
    FRIEND_EMPTY = 0,       -- 好友列表空
    GROUP_CHAT_LIST = 1,    -- 群聊列表(预留)
    FRIEND_LIST = 2,        -- 好友列表
    GROUP_EMPTY = 3,        -- 分组列表空
    GROUP_LIST = 4,         -- 分组列表
}
PartnerScopeListItem.SCOPE_ITEM_STYLE_TYPE = {
    EMPTY = 0,            -- 空
    ADD = 1,              -- 添加按钮
    GROUP = 2,            -- 分组
    FRIEND = 3,           -- 好友
}

PartnerScopeListItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerScopeListItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerScopeListItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PartnerScopeListItem:InitUIComponent()
    ---@type PartnerScopeItem
    self.WBP_PartnerScopeItemCom = self:CreateComponent(self.view.WBP_PartnerScopeItem, PartnerScopeItem)
    ---@type UIListView
    self.TileView_FriendCom = self:CreateComponent(self.view.TileView_Friend, UIListView)
    ---@type ComSelectTitle
    self.WBP_ComSelectTitleCom = self:CreateComponent(self.view.WBP_ComSelectTitle, ComSelectTitle)
    ---@type UIListView
    self.ListViewCom = self:CreateComponent(self.view.ListView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function PartnerScopeListItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerScopeListItem:InitUIView()
end

---面板打开的时候触发
function PartnerScopeListItem:OnRefresh(listData)
    self.WBP_ComSelectTitleCom:Refresh(listData.titleData)
    local listType = listData.listType
    self.userWidget:BP_SetListType(listType, false)
    local itemStyle = listData.itemStyle
    if itemStyle then
        self.view.WBP_PartnerScopeItem:SetVisibility(ESlateVisibility.Visible)
        self.WBP_PartnerScopeItemCom:Refresh({
            scopeType = Game.FriendSystem.EScopeType.Friend,
            itemStyle = itemStyle,
            emptyText = listData.emptyText,
            rootPanel = listData.rootPanel,
            editPanelType = PartnerEditPanel.EDIT_PANEL_TYPE.SCOPE_EDIT,
        })
    else
        self.view.WBP_PartnerScopeItem:SetVisibility(ESlateVisibility.Collapsed)
    end

    local friendList = listData.friendList
    if friendList then
        self.TileView_FriendCom:Refresh(friendList)
    end
    local groupList = listData.groupList
    if groupList then
        self.ListViewCom:Refresh(groupList)
    end
end

return PartnerScopeListItem
