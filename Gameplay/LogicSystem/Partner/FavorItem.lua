local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local FriendAssist = kg_require("Gameplay.LogicSystem.Friend.FriendAssist")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class FavorItem : UIComponent
---@field view FavorItemBlueprint
local FavorItem = DefineClass("FavorItem", UIComponent)

FavorItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FavorItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FavorItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function FavorItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function FavorItem:InitUIEvent()
    self:AddUIEvent(self.view.Button.OnClicked, "on_Button_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FavorItem:InitUIView()
end

---面板打开的时候触发
function FavorItem:Refresh(info)
    self.attraction = info.attraction or 0
    self.attractionLevel = info.attractionLevel or 0

    local friendAttractionData = Game.TableData.GetFriendAttractionDataTable()
    local MinAttractionData, MaxAttractionData = FriendAssist.GetAttractionRange(friendAttractionData,
        self.attractionLevel)
    local Progress = (self.attraction - MinAttractionData) / (MaxAttractionData - MinAttractionData)
    -- 拓展给其他地方使用
    if self.view.Text_AttractionLevel then
        self.view.Text_AttractionLevel:SetText(StringConst.Get("SOCIAL_FRIEND_FAVOR_LEVEL",
            self.attractionLevel))
    end
    self.view.ProgressBarAttraction:SetPercent(Progress)
end


--- 此处为自动生成
function FavorItem:on_Button_Clicked()
    local friendAttractionData = Game.TableData.GetFriendAttractionDataTable()
    local tableAttractionData = friendAttractionData[self.attractionLevel]
    local cur = math.max(self.attraction, 0)
    local max = tableAttractionData.Range
    local text = string.format("%d/%d", cur, max)

    local _, ViewportPosition = import("SlateBlueprintLibrary").LocalToViewport(self.userWidget,
            self.view.Button:GetCachedGeometry(),
            import("SlateBlueprintLibrary").GetLocalSize(self.view.Button:GetCachedGeometry()), nil, nil)
    Game.NewUIManager:OpenPanel(UIPanelConfig.ItemFavorTips_Panel,
        {
            Pos = ViewportPosition,
            Text = text
        }
    )
end

return FavorItem
