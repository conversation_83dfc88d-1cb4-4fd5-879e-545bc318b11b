local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PartnerGroupItemRight : UIListItem
---@field view PartnerGroupItemRightBlueprint
local PartnerGroupItemRight = DefineClass("PartnerGroupItemRight", UIListItem)

local PartnerEditPanel = kg_require("Gameplay.LogicSystem.Partner.PartnerEditPanel")

local ESlateVisibility = import("ESlateVisibility")

local Game = Game

-- @type 控件类型
PartnerGroupItemRight.STATE_TYPE = {
    ADD = 0,        -- 添加按钮
    REMOVE = 1,     -- 删除按钮
    MEMBER = 2,     -- 成员头像
}

PartnerGroupItemRight.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerGroupItemRight:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerGroupItemRight:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PartnerGroupItemRight:InitUIComponent()
    ---@type SocialHead
    self.WBP_HeadCom = self:CreateComponent(self.view.WBP_Head, SocialHead)
    ---@type UIComButtonItem
    self.WBP_AddBtnCom = self:CreateComponent(self.view.WBP_AddBtn, UIComButtonItem)
    ---@type UIComButtonItem
    self.Btn_RemoveMemberCom = self:CreateComponent(self.view.Btn_RemoveMember, UIComButtonItem)
end

---UI事件在这里注册，此处为自动生成
function PartnerGroupItemRight:InitUIEvent()
    self:AddUIEvent(self.WBP_AddBtnCom.onClickEvent, "on_WBP_AddBtnCom_ClickEvent")
    self:AddUIEvent(self.Btn_RemoveMemberCom.onClickEvent, "on_Btn_RemoveMemberCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerGroupItemRight:InitUIView()
end

---面板打开的时候触发
function PartnerGroupItemRight:OnRefresh(params)
    local info = params.info or {}
    self.Info = info
    self.rootPanel = params.rootPanel
    self.editPanelType = params.editPanelType
    self.clubID = params.clubID

    local state = info.state
    local STATE_TYPE = PartnerGroupItemRight.STATE_TYPE
    if state == STATE_TYPE.ADD then
        self.view.Memberpanel:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_AddBtn:SetVisibility(ESlateVisibility.Visible)
        self.view.Btn_RemoveMember:SetVisibility(ESlateVisibility.Collapsed)
    elseif state == STATE_TYPE.REMOVE then
        self.view.Memberpanel:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_AddBtn:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Btn_RemoveMember:SetVisibility(ESlateVisibility.Visible)
    else
        self.view.Memberpanel:SetVisibility(ESlateVisibility.Visible)
        self.view.WBP_AddBtn:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Btn_RemoveMember:SetVisibility(ESlateVisibility.Collapsed)

        self.WBP_HeadCom:Refresh({
            ProfessionID = info.school,
            Level = info.lv,
            Sex = info.sex,
            MemberType = info.MemberType,
        })
        self.view.Name:SetText(Game.FriendSystem:GetFriendShowName(info.id, info.rolename))
    end
end


--- 此处为自动生成
function PartnerGroupItemRight:on_WBP_AddBtnCom_ClickEvent()
    if self.editPanelType == PartnerEditPanel.EDIT_PANEL_TYPE.SCOPE_EDIT then
        local state = Game.FriendSystem.EScopeEditType.AddFriend
        local data = Game.FriendSystem:GetInOrOutRangeList(Game.FriendSystem.EScopeType.Friend, false, self.rootPanel:GetTempSelectItem(Game.FriendSystem.EScopeType.Friend))	--luacheck:ignore

        Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerEditPanel, {
            data = data,
            state = state,
            panelType = self.editPanelType,
            styleType = 2,
            rootEditPanel = self.rootPanel,
        })
    elseif self.editPanelType == PartnerEditPanel.EDIT_PANEL_TYPE.PLAYER_COMMON_EDIT then
        Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerEditPanel, {
            clubID = self.clubID,
            editType = Game.FriendSystem.EditType.Invite,
            panelType = self.editPanelType,
            styleType = 2,
            rootEditPanel = self.rootPanel,
        })
    else
        Log.Error("PartnerGroupItemRight:on_WBP_AddBtnCom_ClickEvent", self.editPanelType)
    end
end

--- 此处为自动生成
function PartnerGroupItemRight:on_Btn_RemoveMemberCom_ClickEvent()
    if self.editPanelType == PartnerEditPanel.EDIT_PANEL_TYPE.SCOPE_EDIT then
        local state, data
        state = Game.FriendSystem.EScopeEditType.RemoveFriend
        data = Game.FriendSystem:GetInOrOutRangeList(Game.FriendSystem.EScopeType.Friend, true, self.rootPanel:GetTempSelectItem(Game.FriendSystem.EScopeType.Friend))

        Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerEditPanel, {
            data = data,
            state = state,
            panelType = self.editPanelType,
            styleType = 1,
            rootEditPanel = self.rootPanel,
        })
    elseif self.editPanelType == PartnerEditPanel.EDIT_PANEL_TYPE.PLAYER_COMMON_EDIT then
        Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerEditPanel, {
            clubID = self.clubID,
            editType = Game.FriendSystem.EditType.Remove,
            panelType = self.editPanelType,
            styleType = 1,
            rootEditPanel = self.rootPanel,
        })
    else
        Log.Error("PartnerGroupItemRight:on_Btn_RemoveMemberCom_ClickEvent", self.editPanelType)
    end
end

return PartnerGroupItemRight
