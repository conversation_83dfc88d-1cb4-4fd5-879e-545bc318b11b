local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComInputBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComInputBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

local PartnerEditPanel = kg_require("Gameplay.LogicSystem.Partner.PartnerEditPanel")
local PartnerGroupItemRight = kg_require("Gameplay.LogicSystem.Partner.PartnerGroupItemRight")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
local lume = kg_require("Shared.lualibs.lume")

local Game = Game
local Enum = Enum

-- 成员数量超过这个数目需要折叠
local NEED_FOLD_NUM = 8       -- luacheck: ignore

-- 查看更多的样式: 0加号；1下箭头；2上箭头
local UNFOLD_BTN_TYPE = {       -- luacheck: ignore
    PLUS = 0,
    DOWN = 1,
    UP = 2,
}

---@class PartnerGroupCreatePanel : UIPanel
---@field view PartnerGroupCreatePanelBlueprint
local PartnerGroupCreatePanel = DefineClass("PartnerGroupCreatePanel", UIPanel)


PartnerGroupCreatePanel.eventBindMap = {
    [EEventTypesV2.ON_UPDATE_FRIEND_CLUB_INFO] = "UpdateEditMemberList"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerGroupCreatePanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerGroupCreatePanel:InitUIData()
    self.clubID = nil
    self.addMemberList = {} ---记录本次编辑新增的成员
    self.removeMemberList = {} ---记录本次编辑移除的成员
    self.GroupPanelStyleType = nil --记录当前的面板的样式类型
    self.bIsNameOrNoticeChange = false -- 记录群名称和公告是否发生了变化
end

--- UI组件初始化，此处为自动生成
function PartnerGroupCreatePanel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtn_QuitCom = self:CreateComponent(self.view.WBP_ComBtn_Quit, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_CreateCom = self:CreateComponent(self.view.WBP_ComBtn_Create, UIComButton)
    ---@type UIComBackTitle
    self.WBP_ComBtnBackNewCom = self:CreateComponent(self.view.WBP_ComBtnBackNew, UIComBackTitle)
    ---@type UIListView
    self.ComListCom = self:CreateComponent(self.view.ComList, UIListView)
    ---@type UIComInputBox
    self.WBP_ComInputNameCom = self:CreateComponent(self.view.WBP_ComInputName, UIComInputBox)
    ---@type UIComInputBox
    self.WBP_ComInputBigCom = self:CreateComponent(self.view.WBP_ComInputBig, UIComInputBox)
    ---@type UIComButton
    self.WBP_ComBtn_RefuseCom = self:CreateComponent(self.view.WBP_ComBtn_Refuse, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_AcceptCom = self:CreateComponent(self.view.WBP_ComBtn_Accept, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function PartnerGroupCreatePanel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnBackNewCom.onPreCloseEvent, "on_WBP_ComBtnBackNewCom_PreCloseEvent")
    self:AddUIEvent(self.WBP_ComBtn_AcceptCom.onClickEvent, "on_WBP_ComBtn_AcceptCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_RefuseCom.onClickEvent, "on_WBP_ComBtn_RefuseCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComInputBigCom.onTextChanged, "on_WBP_ComInputBigCom_TextChanged")
    self:AddUIEvent(self.WBP_ComInputNameCom.onTextChanged, "on_WBP_ComInputNameCom_TextChanged")
    self:AddUIEvent(self.WBP_ComBtn_CreateCom.onClickEvent, "on_WBP_ComBtn_CreateCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_QuitCom.onClickEvent, "on_WBP_ComBtn_QuitCom_ClickEvent")
    self:AddUIEvent(self.view.UnFoldBtn.WBP_ComBtnEmpty.OnClicked, "on_UnFoldBtnWBP_ComBtnEmpty_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerGroupCreatePanel:InitUIView()
    self.WBP_ComBtn_CreateCom:Refresh(StringConst.Get("FRIEND_GROUP_CHAT_CREATE")) -- 创建群聊

    self.WBP_ComBtn_RefuseCom:Refresh(StringConst.Get("SOCIAL_FRIEND_CLUB_DISBAND_CLUB")) -- 解散群聊
    self.WBP_ComBtn_AcceptCom:Refresh(StringConst.Get("SAVE_MODIFY"))   -- 保存修改

    self.WBP_ComBtn_QuitCom:Refresh(StringConst.Get("FRIEND_GROUP_CHAT_EXIT"))  -- 退出群聊

    self.view.UnFoldBtn.TextBlock:SetText(StringConst.Get("MORE_DETAIL"))
end

---面板打开的时候触发
function PartnerGroupCreatePanel:OnRefresh(clubID)
    self.addMemberList = {}
    self.removeMemberList = {}
    self.clubID = clubID
    self.GroupPanelStyleType = nil
    self:InitGroupPanel()
    self:UpdateGroupPanel()
end

function PartnerGroupCreatePanel:genMemberListData(info)
    return {
        rootPanel = self,
        clubID = self.clubID,
        editPanelType = PartnerEditPanel.EDIT_PANEL_TYPE.PLAYER_COMMON_EDIT,
        info = info
    }
end


---初始化面板样式
function PartnerGroupCreatePanel:InitGroupPanel()
    local clubNameInputLimit = Game.TableData.GetConstDataRow("FRIEND_GROUPCHAT_NAME_MAX") -- 群名称字数限制
    local clubNoticeInputLimit = Game.TableData.GetConstDataRow("FRIEND_GROUPCHAT_BULLETIN_MAX") --群公告字数限制
    local defaultNoticeText = StringConst.Get("SOCIAL_FRIEND_CLUB_EMPTY_NOTICE")


    ---群聊编辑面板：现有群聊，修改设置/退出群聊面板：查看成员，退群
    if self.clubID then
        local clubInfo = Game.ChatClubSystem:getClubInfoById(self.clubID)
        if clubInfo then
            if clubInfo.owner == "" then
                self.view.KGCanvas_Mesg:SetVisibility(ESlateVisibility.Collapsed)
                self.view.HB_Btn:SetVisibility(ESlateVisibility.Collapsed)
            end
            self.WBP_ComBtnBackNewCom:Refresh(clubInfo.name)
            if string.isEmpty(clubInfo.notice) then
                self.WBP_ComInputBigCom:Refresh("", defaultNoticeText, clubNoticeInputLimit, false)
            else
                self.WBP_ComInputBigCom:Refresh(clubInfo.notice, "", clubNoticeInputLimit, false)
            end
        end
        ---查看是否是群主：编辑群聊
        if Game.ChatClubSystem:checkIsClubOwner(self.clubID) then
            self.view.memberPanelTitle:SetText(StringConst.Get("FRIEND_GROUP_CHAT_INVITE_MEMBER"))

            self.WBP_ComInputNameCom:Refresh(clubInfo and clubInfo.name or "", "", clubNameInputLimit, false)
        else
            self.WBP_ComInputNameCom:Refresh(clubInfo and clubInfo.name or "", "", clubNameInputLimit, false)
            --- 退出群聊面板
            self.view.memberPanelTitle:SetText(StringConst.Get("FRIEND_GROUP_CHAT_MEMBER"))

            --- 屏蔽输入
            self.WBP_ComInputBigCom:SetEnable(false)
            self.WBP_ComInputNameCom:SetEnable(false)
        end

        self:UpdateEditMemberList()

        ---显示群成员的在线人数
        self.view.TB_Num:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:UpdateMemberOnLineState()
    else
        ---创建群聊面板：新建群聊，邀请/移除群成员
        self.WBP_ComBtnBackNewCom:Refresh(StringConst.Get("FRIEND_GROUP_CHAT_CREATE"))
        self.view.memberPanelTitle:SetText(StringConst.Get("FRIEND_GROUP_CHAT_INVITE_MEMBER"))

        self.view.TB_Num:SetVisibility(ESlateVisibility.Collapsed)

        self.WBP_ComInputNameCom:Refresh("", "", clubNameInputLimit, false)
        self.WBP_ComInputBigCom:Refresh("", defaultNoticeText, clubNoticeInputLimit, false)

        self:UpdateInviteMemberList()
    end

    self:on_WBP_ComInputBigCom_TextChanged()
end


---刷新编辑群面板的成员列表
function PartnerGroupCreatePanel:UpdateEditMemberList()
    if not self.clubID then
        return
    end
    local clubInfo = Game.ChatClubSystem:getClubInfoById(self.clubID)
    local memberInfoList
    if Game.ChatClubSystem:checkIsClubOwner(self.clubID) then
        ---添加初始设置
        memberInfoList = {
            self:genMemberListData({ state = PartnerGroupItemRight.STATE_TYPE.ADD }),
            self:genMemberListData({
                state = PartnerGroupItemRight.STATE_TYPE.MEMBER,
                id = Game.me.eid, school = Game.me.Profession, lv = Game.me.Level, rolename = Game.me.Name,
                MemberType = Game.ChatClubSystem:CheckSomeOneIsTargetClubOwner(self.clubID, Game.me.eid) and 3 or 0,
            }),
        }
        if #clubInfo.members >= 2 then
            table.insert(memberInfoList, 1, self:genMemberListData({ state = PartnerGroupItemRight.STATE_TYPE.REMOVE }))
        end
    else
        --- 添加初始设置
        memberInfoList = {
            self:genMemberListData({
                state = PartnerGroupItemRight.STATE_TYPE.MEMBER,
                id = Game.me.eid, school = Game.me.Profession, lv = Game.me.Level, rolename = Game.me.Name,
                MemberType = Game.ChatClubSystem:CheckSomeOneIsTargetClubOwner(self.clubID, Game.me.eid) and 3 or 0,
            })
        }
    end
    self.memberInfoList = memberInfoList

    if clubInfo then
        ---获取群聊成员列表, 并设置成员信息面板
        local relationInfos = Game.FriendSystem:GetRelationInfos()
        for _, eid in pairs(clubInfo.members) do
            repeat
                if Game.me.eid == eid then
                    break
                end
                local memberInfo = relationInfos[eid]
                if not memberInfo then
                    break
                end

                local memberItemInfo = self:genMemberListData({
                    state = PartnerGroupItemRight.STATE_TYPE.MEMBER,
                    id = memberInfo.id, school = memberInfo.school, lv = memberInfo.lv, rolename = memberInfo.rolename,
                    MemberType = Game.ChatClubSystem:CheckSomeOneIsTargetClubOwner(self.clubID, memberInfo.id) and 3 or 0
                })
                table.insert(memberInfoList, memberItemInfo)
            until true
        end
        self.ComListCom:Refresh(memberInfoList)
    end

    self:UpdateGroupPanel()
    self:UpdateMemberOnLineState()
end

---刷新邀请群面板的成员列表
function PartnerGroupCreatePanel:UpdateInviteMemberList()
    local childList = {
        self:genMemberListData({ state = PartnerGroupItemRight.STATE_TYPE.ADD }),
        self:genMemberListData({
            state = PartnerGroupItemRight.STATE_TYPE.MEMBER,
            id = Game.me.eid, school = Game.me.Profession, lv = Game.me.Level, rolename = Game.me.Name,
        }),
    }
    self.memberInfoList = childList
    self.ComListCom:Refresh(childList)
end


---更新面板StypeType
function PartnerGroupCreatePanel:UpdateGroupPanel()
    -- 展开群成员的状态下
    if self.GroupPanelStyleType == Game.FriendSystem.CreateGroupChatUIStyle.ExpandGroupMembers	then
        return
    end

    if self.clubID then
        --- 编辑群聊面板样式
        if Game.ChatClubSystem:checkIsClubOwner(self.clubID) then
            if #self.memberInfoList <= NEED_FOLD_NUM then
                self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.EditGroup
            else
                self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.EditGroupExpandMore
                self.view.UnFoldBtn:Event_UI_Style(UNFOLD_BTN_TYPE.DOWN)
            end
        else
            --- 退出群聊面板样式
            if #self.memberInfoList <= NEED_FOLD_NUM then
                self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.ExitGroup
            else
                self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.ExitGroupExpandMore
                self.view.UnFoldBtn:Event_UI_Style(UNFOLD_BTN_TYPE.PLUS)
            end
        end
    else
        --- 创建群聊面板样式
        if #self.memberInfoList <= NEED_FOLD_NUM then
            self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.Create
        else
            self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.ExpandMore
            self.view.UnFoldBtn:Event_UI_Style(UNFOLD_BTN_TYPE.DOWN)

        end
    end
    self.userWidget:Event_UI_Style(self.GroupPanelStyleType)
end

--- 此处为自动生成
---@return bool
function PartnerGroupCreatePanel:on_WBP_ComBtnBackNewCom_PreCloseEvent()
    self:CloseSelf()
end

--- 此处为自动生成
function PartnerGroupCreatePanel:on_WBP_ComBtn_AcceptCom_ClickEvent()
    Log.Debug("on_WBP_ComBtn_AcceptCom_ClickEvent")

    local name = self.WBP_ComInputNameCom:GetText()
    local notice = self.WBP_ComInputBigCom:GetText()
    --- 已有群聊的情况下
    if not self.clubID then
        Log.Warning("PartnerGroupCreatePanel:on_WBP_ComBtn_AcceptCom_ClickEvent clubID is nil")
        return
    end
    if not self.bIsNameOrNoticeChange then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_NO_CHANGE)
        return
    end
    --修改
    --local addMemberList, removeMemberList = self:GetDiffAddOrRemoveMemberList()
    local addMemberList, removeMemberList = {}, {}
    Game.ChatClubSystem:reqEditClubInfo(self.clubID, { name = name, notice = notice }, addMemberList, removeMemberList)
    self:CloseSelf()
end

--- 此处为自动生成
function PartnerGroupCreatePanel:on_WBP_ComBtn_RefuseCom_ClickEvent()
    if not Game.ChatClubSystem:checkIsClubOwner(self.clubID) then
        Log.Warning("PartnerGroupCreatePanel:on_WBP_ComBtn_RefuseCom_ClickEvent not owner")
        return
    end
    local clubInfo = Game.ChatClubSystem:getClubInfoById(self.clubID)
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GROUPCHAT_DISSOLVE, function()
        Game.ChatClubSystem:reqDisbandClub(self.clubID)
        self:CloseSelf()
    end, nil, { clubInfo.name })
end

--- 此处为自动生成
function PartnerGroupCreatePanel:on_UnFoldBtnWBP_ComBtnEmpty_Clicked()
    if self.GroupPanelStyleType == Game.FriendSystem.CreateGroupChatUIStyle.ExpandMore
            or self.GroupPanelStyleType == Game.FriendSystem.CreateGroupChatUIStyle.EditGroupExpandMore
            or self.GroupPanelStyleType == Game.FriendSystem.CreateGroupChatUIStyle.ExitGroupExpandMore	then
        self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.ExpandGroupMembers
        self.view.UnFoldBtn:Event_UI_Style(UNFOLD_BTN_TYPE.UP)
        self.view.UnFoldBtn.TextBlock:SetText(StringConst.Get("FOLD_GROUP_CHAT_MEMBER"))
        self.ComListCom:Refresh(self.memberInfoList)
    elseif self.GroupPanelStyleType == Game.FriendSystem.CreateGroupChatUIStyle.ExpandGroupMembers then
        self.GroupPanelStyleType = self.clubID and Game.FriendSystem.CreateGroupChatUIStyle.EditGroupExpandMore or Game.FriendSystem.CreateGroupChatUIStyle.ExpandMore
        if self.clubID then
            if Game.ChatClubSystem:checkIsClubOwner(self.clubID) then
                self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.EditGroupExpandMore
            else
                self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.ExitGroupExpandMore
            end
        else
            self.GroupPanelStyleType = Game.FriendSystem.CreateGroupChatUIStyle.ExpandMore
        end
        self.view.UnFoldBtn:Event_UI_Style(UNFOLD_BTN_TYPE.DOWN)
        self.view.UnFoldBtn.TextBlock:SetText(StringConst.Get("MORE_DETAIL"))
        self.ComListCom:Refresh(self.memberInfoList)
    end
    self.userWidget:Event_UI_Style(self.GroupPanelStyleType)
end

---检查群公告和群名称是否发生了变化
function PartnerGroupCreatePanel:CheckClubNameOrNoticeChange()
    ---群公告
    local clubInfo = Game.ChatClubSystem:getClubInfoById(self.clubID)
    if clubInfo then
        local name = self.WBP_ComInputNameCom:GetText()
        local notice = self.WBP_ComInputBigCom:GetText()
        if notice == clubInfo.notice and name == clubInfo.name  then
            self.WBP_ComBtn_AcceptCom:SetDisable(true)
            self.bIsNameOrNoticeChange = false
        else
            self.WBP_ComBtn_AcceptCom:SetDisable(false)
            self.bIsNameOrNoticeChange = true
        end
    end
end

--- 此处为自动生成
---@param text string
function PartnerGroupCreatePanel:on_WBP_ComInputBigCom_TextChanged(text)
    local curText = self.WBP_ComInputBigCom:GetText()
    local noticeCharLimitCount = Game.TableData.GetConstDataRow("FRIEND_GROUPCHAT_BULLETIN_MAX") --群公告字数限制
    local availableCount = noticeCharLimitCount - utf8.len(curText)
    if availableCount < 0 then
        self.WBP_ComInputBigCom:SetText(utf8.sub(curText, 1, noticeCharLimitCount + 1), false)
        availableCount = 0
    end
    self.view.TextBlock_LimitTips:SetText(tostring(availableCount))
    self:CheckClubNameOrNoticeChange()
end

--- 此处为自动生成
---@param text stringon_WBP_ComInputNameCom_TextChanged
function PartnerGroupCreatePanel:on_WBP_ComInputNameCom_TextChanged(text)
    self:CheckClubNameOrNoticeChange()
end


--- 此处为自动生成
function PartnerGroupCreatePanel:on_WBP_ComBtn_CreateCom_ClickEvent()
    Log.Debug("on_WBP_ComBtn_CreateCom_ClickEvent")

    local name = self.WBP_ComInputNameCom:GetText()
    local notice = self.WBP_ComInputBigCom:GetText()
    --- 已有群聊的情况下
    if self.clubID then
        Log.Warning("PartnerGroupCreatePanel:on_WBP_ComBtn_CreateCom_ClickEvent clubID is not nil")
        return
    end
    --创建
    if name == "" then
        name = StringConst.Get("SOCIAL_FRIEND_CLUB_DEFAULT_NAME")
    end

    local addMemberList = {}
    for index, memberInfo in pairs(self.memberInfoList) do
        local id = memberInfo.info and memberInfo.info.id
        if id ~= Game.me.eid then
            table.insert(addMemberList, id)
        end
    end
    Game.ChatClubSystem:reqBuildClub(name, notice, addMemberList) -- luacheck: ignore
    self:CloseSelf()
end

--- 此处为自动生成
function PartnerGroupCreatePanel:on_WBP_ComBtn_QuitCom_ClickEvent()
    if Game.ChatClubSystem:checkIsClubOwner(self.clubID) then
        Log.Warning("PartnerGroupCreatePanel:on_WBP_ComBtn_QuitCom_ClickEvent owner can not quit")
        return
    end
    Game.ChatClubSystem:ExitClub(self.clubID)
    self:CloseSelf()
end

-- 更新成员的在线情况
function PartnerGroupCreatePanel:UpdateMemberOnLineState()
    local clubInfo = Game.ChatClubSystem:getClubInfoById(self.clubID)
    if clubInfo then
        self.view.TB_Num:SetText("("..clubInfo.onlineCount.."/"..clubInfo.memberCount..")")
    end
end

---获取在原有的成员的基础上新增或者移除的成员列表
function PartnerGroupCreatePanel:GetDiffAddOrRemoveMemberList()
    local addMemberDiffList = {} ---新增但未保存的新成员
    local removeMemberDiffList = {} ---移除但未保存的老成员
    ---已有群聊的情况下
    if self.clubID then
        ---获取原来成员列表
        local clubInfo = Game.ChatClubSystem:getClubInfoById(self.clubID)
        if clubInfo then
            for index, eid in pairs(clubInfo.members) do
                if self.addMemberList[eid] then
                    self.addMemberList[eid] = nil
                end

                if self.removeMemberList[eid] then
                    table.insert(removeMemberDiffList, eid)
                end
            end
        end
    end

    for eid, _ in pairs(self.addMemberList) do
        table.insert(addMemberDiffList, eid)
    end
    return addMemberDiffList, removeMemberDiffList
end

---检查是否有移除按钮
function PartnerGroupCreatePanel:CheckHasMinusBtn()
    for index, memberInfo in pairs(self.memberInfoList) do
        local state = memberInfo.info.state
        if state == PartnerGroupItemRight.STATE_TYPE.REMOVE then
            return index
        end
    end
    return nil
end

---更新成员列表面板
function PartnerGroupCreatePanel:UpdateMemberList(memberInfoList, editType)
    local minusIndex = self:CheckHasMinusBtn()
    if editType == Game.FriendSystem.EditType.Invite then
        local currentMemberNum = 0
        for index, memberInfo in pairs(self.memberInfoList) do
            if memberInfo.info.state == PartnerGroupItemRight.STATE_TYPE.MEMBER then
                currentMemberNum = currentMemberNum + 1
            end
        end

        if currentMemberNum + lume.count(memberInfoList) > Game.TableData.GetConstDataRow("FRIEND_GROUP_MAX_MEBMER_COUNT") then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FRIEND_CLUB_MEMBER_COUNT_LIMIT)
            return false
        end

        -- 检查是否需要添加移除按钮
        if next(memberInfoList) then
            if not minusIndex then
                table.insert(self.memberInfoList, 2,  self:genMemberListData({ state = PartnerGroupItemRight.STATE_TYPE.REMOVE }))
            end
        end

        for eid, member in pairs(memberInfoList) do
            local memberInfo = self:genMemberListData({
                state = PartnerGroupItemRight.STATE_TYPE.MEMBER,
                id = member.id, school = member.school, lv = member.lv, rolename = member.rolename,
                MemberType = Game.ChatClubSystem:CheckSomeOneIsTargetClubOwner(self.clubID, member.id) and 3 or 0
            })
            table.insert(self.memberInfoList, memberInfo)
        end

        table.merge(self.addMemberList, memberInfoList)
        if #self.removeMemberList > 0 then
            table.del(self.removeMemberList, memberInfoList)
        end
    else
        local needRemoveList= {}
        for index, memberInfo in ipairs(self.memberInfoList) do
            if memberInfoList[memberInfo.info.id] then
                table.insert(needRemoveList, index)
            end
        end

        for index = #needRemoveList, 1, -1 do
            table.remove(self.memberInfoList, needRemoveList[index])
        end

        -- 检查是否需要删除移除按钮
        if minusIndex and #self.memberInfoList == 3 then
            table.remove(self.memberInfoList, minusIndex)
        end

        if #self.addMemberList > 0 then
            table.del(self.addMemberList, memberInfoList)
        end
        table.merge(self.removeMemberList, memberInfoList)
    end

    self.ComListCom:Refresh(self.memberInfoList)

    self:UpdateGroupPanel()

    if self.clubID then
        self:UpdateMemberOnLineState()
    end

    return true
end

---创建群聊/编辑群聊，获取新成员列表
function PartnerGroupCreatePanel:GetEditMemberList()
    local memberInfoList = {}
    local relationInfos = Game.FriendSystem:GetRelationInfos()
    for index, memberInfo in pairs(self.memberInfoList) do
        local id = memberInfo.info and memberInfo.info.id
        if id then
            memberInfoList[id] = relationInfos[id]
        end
    end
    return memberInfoList
end


return PartnerGroupCreatePanel
