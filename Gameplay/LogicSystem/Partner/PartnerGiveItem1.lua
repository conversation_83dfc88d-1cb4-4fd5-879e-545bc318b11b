local ItemBoxNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemBoxNew")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PartnerGiveItem1 : UIListItem
---@field view PartnerGiveItem1Blueprint
local PartnerGiveItem1 = DefineClass("PartnerGiveItem1", UIListItem)

local Enum = Enum

PartnerGiveItem1.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerGiveItem1:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerGiveItem1:InitUIData()
    self.index = nil   --index
end

--- UI组件初始化，此处为自动生成
function PartnerGiveItem1:InitUIComponent()
    ---@type ItemBoxNew
    self.WBP_ComItemNorm_ItemCom = self:CreateComponent(self.view.WBP_ComItemNorm_Item, ItemBoxNew)
end

---UI事件在这里注册，此处为自动生成
function PartnerGiveItem1:InitUIEvent()
    self:AddUIEvent(self.view.Big_Button_ClickArea.OnClicked, "on_Big_Button_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerGiveItem1:InitUIView()
end

---面板打开的时候触发
function PartnerGiveItem1:OnRefresh(params)
    self.params = params
    self.index = params.index

    params.clickCallback = function()
        self:itemBoxClickCallback()
    end

    self.view.TextBlock_Name:SetText(params.giftName)

    local bIsSelected = self:IsSelected()
    params.itemState = Enum.ItemStatus.Normal
    local isEmpty = params.num <= 0
    self.userWidget:BP_SetType(bIsSelected, isEmpty)
    if isEmpty then
        params.itemState = Enum.ItemStatus.UseLimit
    end
    self.WBP_ComItemNorm_ItemCom:Refresh(params)
end

function PartnerGiveItem1:itemBoxClickCallback()
    UI.ShowUI('BagItemTips_Panel', self.params.id)
end

--- 此处为自动生成
function PartnerGiveItem1:on_Big_Button_ClickArea_Clicked()
    local parentComponent = self:GetParent()
    local currentIndex = parentComponent:GetSelectedItemIndex()
    parentComponent:ClearSelection()
    parentComponent:SetSelectedItemByIndex(self.index, true)
    local selectedItem = parentComponent:GetItemByIndex(currentIndex)
    if selectedItem then
        selectedItem:Refresh(selectedItem.params)
    end

    self:Refresh(self.params)
end

return PartnerGiveItem1
