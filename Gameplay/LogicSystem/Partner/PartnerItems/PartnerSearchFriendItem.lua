local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PartnerSearchFriendItem : UIListItem
---@field view PartnerSearchFriendItemBlueprint
local PartnerSearchFriendItem = DefineClass("PartnerSearchFriendItem", UIListItem)

local Game = Game
local Enum = Enum

PartnerSearchFriendItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PartnerSearchFriendItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PartnerSearchFriendItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PartnerSearchFriendItem:InitUIComponent()
    ---@type SocialHead
    self.WBP_HeadCom = self:CreateComponent(self.view.WBP_Head, SocialHead)
    ---@type UIComButtonItem
    self.WBP_ComBtnIconNewChatCom = self:CreateComponent(self.view.WBP_ComBtnIconNewChat, UIComButtonItem)
    ---@type UIComButtonItem
    self.WBP_ComBtnIconNewAddCom = self:CreateComponent(self.view.WBP_ComBtnIconNewAdd, UIComButtonItem)
end

---UI事件在这里注册，此处为自动生成
function PartnerSearchFriendItem:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnIconNewAddCom.onClickEvent, "on_WBP_ComBtnIconNewAddCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnIconNewChatCom.onClickEvent, "on_WBP_ComBtnIconNewChatCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PartnerSearchFriendItem:InitUIView()
end

---面板打开的时候触发
function PartnerSearchFriendItem:OnRefresh(data)
    self.data = data
    self.view.Text_PlayerName:SetText(data.rolename)

    local serverName = Game.LoginSystem:GetServerLoginData().ServerName
    self.view.TextBlock_ServerName:SetText(serverName)
    self.WBP_HeadCom:Refresh({
        ProfessionID = data.school,
        Level = data.lv,
        Sex = data.sex,
        MemberType = data.MemberType,
    })
end

--- 此处为自动生成
function PartnerSearchFriendItem:on_WBP_ComBtnIconNewAddCom_ClickEvent()
    if not self.data or not self.data.id then
        return
    end

	if self.data.id == Game.me.eid then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FRIEND_ADD_MYSELF)
		return
	end
	
    local sourceID = Enum.EFriendAddSourceData.DECISIVE_ARENA
    Game.FriendSystem:SendAddFriend(self.data.id, sourceID, 0)
end

--- 此处为自动生成
function PartnerSearchFriendItem:on_WBP_ComBtnIconNewChatCom_ClickEvent()
	if self.data.id == Game.me.eid then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FRIEND_CHAT_MYSELF)
		return
	end
    Game.ChatSystem:OpenSocialPanel(true,Enum.ESocialTab.PrivateChat, self.data.id, Enum.EChatTarget.Friend)
end

return PartnerSearchFriendItem
