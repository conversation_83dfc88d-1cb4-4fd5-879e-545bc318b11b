---@class NewbieGuideGroup
local NewbieGuideGroup = DefineClass("NewbieGuideGroup")

local NewbieGuideStep = require("Gameplay.LogicSystem.NewbieGuide.NewbieGuideStep")
local NewbieGuideImgTextStep = require("Gameplay.LogicSystem.NewbieGuide.NewbieGuideImgTextStep") 
local UIFunctionLibrary = import("UIFunctionLibrary")
local C7FunctionLibrary = import("C7FunctionLibrary")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")

--UI栈顶层忽略可以忽略的界面（用于检查HUD上是不是覆盖了界面）
NewbieGuideGroup.IgnoreUIListInStack = {
    P_HUDInteract = true,
	ScreenInput_Panel = true,
    ClickEffectPanel = true,
}

--引导步骤类型
NewbieGuideGroup.EGuideStepType = {
    Operation = 671,   --操作引导
    ImgText = 672,	--图文引导
}

NewbieGuideGroup.GuideStepTypeSignal = 10000

function NewbieGuideGroup:ctor()
    self.bMobilePlatform = PlatformUtil.IsMobilePlatform() or (C7FunctionLibrary.IsC7Editor() and UIFunctionLibrary.GetPreviewPlatform() ~= EDPIScalePreviewPlatforms.PC) --是否是移动平台
    self.stepList = {} --该引导组绑定的引导步骤列表
    self.finishMarkStepId = nil --关键步骤ID
    self.bInProcess = false --是否处于引导流程中
    self.stepScript = nil  --执行引导步骤的脚本实例
    self.bMarkStepFinish = false	--关键步骤引导是否已完成
    self.stepFinishCallback = function() self:stepFinish() end  --引导完成回调
end

function NewbieGuideGroup:Start(groupInfo)
    self.groupInfo = groupInfo
    self:getStepList()
    if #self.stepList == 0 then
        local groupID = self.groupInfo.ID
        Game.NewbieGuideSystem:GroupKeyStepFinish(groupID)
        Game.NewbieGuideSystem:ExecuteGroupFinish(groupID)
    end
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_UI_OPEN, self, "CheckAndExecute")
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_UI_CLOSE, self, "CheckAndExecute")
    Game.EventSystem:AddListener(_G.EEventTypes.NEWBIE_GUIDE_GROUP_FINISH, self, "CheckAndExecute")
    self:CheckAndExecute()
end

function NewbieGuideGroup:getStepList()
    local stepIdList = nil
    local groupInfo = self.groupInfo
    if #self.groupInfo.CommonList == 0 then
        stepIdList = self.bMobilePlatform and groupInfo.MobileList or groupInfo.PCList
        self.finishMarkStepId = self.bMobilePlatform and groupInfo.MobileFinishMark or groupInfo.PCFinishMark
    else
        stepIdList = groupInfo.CommonList
        self.finishMarkStepId = groupInfo.CommonFinishMark
    end
    for _, step in ksbcpairs(stepIdList) do
        self.stepList[#self.stepList + 1] = step
    end
end


---@private CheckAndExecute
function NewbieGuideGroup:CheckAndExecute()
    if self.bInProcess or #self.stepList == 0 then
        return
    end
    local currentStepId = self.stepList[1]
    local stepType = math.floor(currentStepId / NewbieGuideGroup.GuideStepTypeSignal)
	local bIsOperation = stepType == NewbieGuideGroup.EGuideStepType.Operation
	local stepDetail = bIsOperation and Game.TableData.GetNewbieGuideOperationDataRow(currentStepId) or Game.TableData.GetNewbieGuideImgTextDataRow(currentStepId)
	if not string.isEmpty(stepDetail.UIID) then
		if stepDetail.UIID == "P_HUDBaseView" then
			local topUIID = Game.UIManager:GetTopUI().uid
			if not UI.IsShow("P_HUDBaseView") or (topUIID ~= "P_HUDBaseView" and not NewbieGuideGroup.IgnoreUIListInStack[topUIID]) then
				return
			end
		elseif not Game.UIManager:CheckPanelIsTop(stepDetail.UIID)  then
			return
		end
	end
	--检查下是否有其他引导正在执行
	if Game.NewbieGuideSystem:CheckStartGuideStep(self.groupInfo.ID, stepDetail.ID, not bIsOperation or stepDetail.IsForce) then
		local stepScript = bIsOperation and NewbieGuideStep or NewbieGuideImgTextStep
		self:executeStep(stepDetail, stepScript)
	end
end

function NewbieGuideGroup:executeStep(stepInfo, stepClass)
    self.bInProcess = true
    self.currentStep = stepInfo
    self.stepScript = stepClass.new()
    self.stepScript:Execute(stepInfo, self.stepFinishCallback)
    Game.NewbieGuideSystem:CheckNeedShowSkipUI()
end

function NewbieGuideGroup:GetIsInProcess()
    return self.bInProcess
end

function NewbieGuideGroup:stepFinish()
	Game.NewbieGuideSystem:CheckEndGuideStep(self.currentStep.ID)
    self.bInProcess = false
    assert(self.currentStep.ID == self.stepList[1], "newbie guide step execute exception, current step out of step list")
    table.remove(self.stepList, 1)
    if self.currentStep.ID == self.finishMarkStepId then
        self.bMarkStepFinish = true
        Game.NewbieGuideSystem:GroupKeyStepFinish(self.groupInfo.ID)
    end
    self.stepScript = nil
    self.currentStep = nil
    Game.NewbieGuideSystem:CheckNeedShowSkipUI()
    if #self.stepList > 0 then
        self:CheckAndExecute()
    else
        self:groupOnEnd()
    end
end

function NewbieGuideGroup:groupOnEnd()
    Game.EventSystem:RemoveObjListeners(self)
    Game.NewbieGuideSystem:ExecuteGroupFinish(self.groupInfo.ID)
end

---SkipGroup 跳过当前引导组
function NewbieGuideGroup:SkipGroup()
    self.bInProcess = false
    if not self.bMarkStepFinish and self.finishMarkStepId ~= nil then
        self.bMarkStepFinish = true
        Game.NewbieGuideSystem:GroupKeyStepFinish(self.groupInfo.ID)
    end
    if self.stepScript then
		Game.NewbieGuideSystem:CheckEndGuideStep(self.currentStep.ID)
        self.stepScript:Clear()
    end
	Game.NewbieGuideSystem:CheckNeedShowSkipUI()
    self:groupOnEnd()
end

function NewbieGuideGroup:ForceStopStep()
	self.bInProcess = false
	if self.stepScript then
		self.stepScript:Clear()
	end
end

function NewbieGuideGroup:OnViewportResize()
	if self.stepScript and self.stepScript.OnViewportResize then
		self.stepScript:OnViewportResize()
	end
end

function NewbieGuideGroup:Clear()
    if self.stepScript then
        self.stepScript:Clear()
    end
    Game.EventSystem:RemoveObjListeners(self)
end

return NewbieGuideGroup
