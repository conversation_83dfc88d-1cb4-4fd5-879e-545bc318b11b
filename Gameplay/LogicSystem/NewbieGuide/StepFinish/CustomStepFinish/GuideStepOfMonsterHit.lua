---@class GuideStepOfMonsterHit
local GuideStepOfMonsterHit = DefineClass("GuideStepOfMonsterHit")

function GuideStepOfMonsterHit:ctor(_, finishStepCallback)
	self.finishStepCallback = finishStepCallback
	--todo 目前实现方式默认场景只有一个怪物，后续等3c那边添加伤害监听接口，这里实现逻辑要改下
	local monsterEntities = Game.EntityManager:getEntitiesByType(EEntityType.NpcActor)
	if monsterEntities then
		local key = next(monsterEntities)
		if key then
			Game.UniqEventSystemMgr:AddListener(monsterEntities[key].eid, EEventTypesV2.BATTLE_TAKE_DAMAGE, "OnTakeDamage", self)
		end
	end
end

function GuideStepOfMonsterHit:OnTakeDamage(context)
	self.finishStepCallback()
end

function GuideStepOfMonsterHit:Clear()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
	--local monsterEntities = Game.EntityManager:getEntitiesByType(EEntityType.NpcActor)
	--if monsterEntities then
	--	local key = next(monsterEntities)
	--	if key then
	--		Game.UniqEventSystemMgr:RemoveListener(monsterEntities[key].eid, EEventTypesV2.BATTLE_TAKE_DAMAGE, "OnTakeDamage", self)
	--	end
	--end
end

return GuideStepOfMonsterHit