local GuideStepFinishOfPeriod = DefineClass("GuideStepFinishOfPeriod")

GuideStepFinishOfPeriod.DefaultDuration = 10

function GuideStepFinishOfPeriod:ctor(finishParam, finishStepCallback)
    local duration = tonumber(finishParam)
    if not duration then
        duration = self.DefaultDuration
    end
    self.finishStepCallback = finishStepCallback
    self.timer = Game.TimerManager:CreateTimerAndStart(function()
        self.timer = nil
        xpcall(self.finishStepCallback, _G.CallBackError)
    end, duration * 1000, 1)
end

function GuideStepFinishOfPeriod:Clear()
    if self.timer then
        Game.TimerManager:StopTimerAndKill(self.timer)
        self.timer = nil
    end
end

return GuideStepFinishOfPeriod