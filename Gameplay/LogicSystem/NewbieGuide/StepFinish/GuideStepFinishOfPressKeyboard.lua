local GuideStepFinishOfPressKeyboard = DefineClass("GuideStepFinishOfPressKeyboard")

function GuideStepFinishOfPressKeyboard:ctor(inputTypeStr, finishStepCallback, stepIns)
    self.finishStepCallback = finishStepCallback
	self.targetInputType = Enum.EInputType[inputTypeStr]
	if self.targetInputType == nil then
		Log.Error(inputTypeStr," Input事件不存在")
		return
	end
	self.bIsForce = stepIns:GetIsForce()
	if self.bIsForce then
		Game.PlayerController:SetInputWhiteList(Enum.EInputWhiteListReason.Guide, {self.targetInputType})
	end
	if string.endsWith(self.targetInputType, "_Action") then
		Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, "OnReceiveInputEvent", self)
	elseif self.targetInputType == "MoveForward_Axis" or self.targetInputType == "MoveRight_Axis" 
		or self.targetInputType == "MoveForward_Axis_2" or self.targetInputType == "MoveRight_Axis_2" then
		Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_MOVE_INPUT, "OnReceiveMoveInput", self)
	elseif self.targetInputType == "LookUp_Axis" or self.targetInputType == "Turn_Axis" then
		Game.GlobalEventSystem:AddListener(EEventTypesV2.CAMERA_ROTATE_INPUT, "OnReceiveRotateInput", self)
	end
end

function GuideStepFinishOfPressKeyboard:OnReceiveInputEvent(actionName, keyEvent)
	if actionName == self.targetInputType then
		self.finishStepCallback()
	end
end

function GuideStepFinishOfPressKeyboard:OnReceiveMoveInput()
	self.finishStepCallback()
end

function GuideStepFinishOfPressKeyboard:OnReceiveRotateInput()
	self.finishStepCallback()
end

function GuideStepFinishOfPressKeyboard:Clear()
	self.finishStepCallback = nil
	if self.bIsForce and Game.PlayerController then
		Game.PlayerController:ClearAllInputWhiteList(Enum.EInputWhiteListReason.Guide)
	end
	Game.EventSystem:RemoveObjListeners(self)
end

return GuideStepFinishOfPressKeyboard