---@class P_NewbieGuideImgText:UIController
local P_NewbieGuideImgText = DefineClass("P_NewbieGuideImgText", UIController)

local ESlateVisibility = import("ESlateVisibility")

function P_NewbieGuideImgText:OnCreate()
    self.currentIndex = 1  --当前显示的图片的index
    self.imgListView = BaseList.CreateList(self, BaseList.Kind.ComList, "ImageList") --图片列表UI
    self.navigationItemList = {self.View.WBP_ComIndictorPoint.WBP_ComIndictorPoint_Item.WidgetRoot} --下方导航栏item列表
    self.hadShowCloseBtn = false    --是否显示过关闭按钮
    self.fromNewbieGuide = false    --是否通过新手引导打开
    self.closeCallback = nil        --界面关闭回调
    self:AddUIListener(EUIEventTypes.CLICK, self.View.LeftArrowButton, "OnClickLeftButton")
    self:AddUIListener(EUIEventTypes.CLICK, self.View.RightArrowButton, "OnClickRightButton")
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtnCloseNew.Button, "OnClickClose")
end

function P_NewbieGuideImgText:OnRefresh(guideId, fromNewbieGuide, closeCallback)
    self.guideId = guideId
    self.closeCallback = closeCallback
    self.fromNewbieGuide = fromNewbieGuide
    local guideData = Game.TableData.GetNewbieGuideImgTextDataRow(guideId)
    self.imgListLength = #guideData.Img
    self.View.LeftButton:SetVisibility(ESlateVisibility.Collapsed)
    self.View.RightButton:SetVisibility(self.imgListLength == 1 and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
    self.hadShowCloseBtn = false
    self.View.WBP_ComBtnCloseNew:SetVisibility(fromNewbieGuide and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
    self.View.WBP_TitleText.Text_First:SetText(guideData.Title:sub(1,1))
    self.View.WBP_TitleText.Text_Affix:SetText(guideData.Title:sub(2))
    self.View.ContentText:SetText(guideData.GuideText)
    self.imgListView:SetData(#guideData.Img)
	self.imgListView:LockScroll(true)
    
    if self.imgListLength > #self.navigationItemList then
        local tmpWidget = self.navigationItemList[1]
        for i = 1, self.imgListLength - #self.navigationItemList do
            local widget = import("UIFunctionLibrary").C7CreateWidget(self.View.WidgetRoot, self.View.WBP_ComIndictorPoint.HorizontalBox, tmpWidget)
            self.navigationItemList[#self.navigationItemList + 1] = widget
			widget:Event_UI_Light(false)
        end
    end
    for i, v in pairs(self.navigationItemList) do
        local visibility = (i > self.imgListLength or self.imgListLength == 1) and ESlateVisibility.Collapsed or  ESlateVisibility.SelfHitTestInvisible 
        v:SetVisibility(visibility)
    end
    
    self:updateSelectIndexState(self.currentIndex, 1)
    self.currentIndex = 1
end

function P_NewbieGuideImgText:OnRefresh_ImageList(widget, index, selected)
    local guideData = Game.TableData.GetNewbieGuideImgTextDataRow(self.guideId)
    self:SetImage(widget.WidgetRoot, guideData.Img[index])
end

function P_NewbieGuideImgText:OnClickLeftButton()
    if self.currentIndex == self.imgListLength then
        self.View.RightButton:SetVisibility(ESlateVisibility.Visible)
    end
    self:updateSelectIndexState(self.currentIndex, self.currentIndex - 1)
    self.currentIndex = self.currentIndex - 1
    if self.currentIndex == 1 then
        self.View.LeftButton:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_NewbieGuideImgText:OnClickRightButton()
    if self.currentIndex == 1 then
        self.View.LeftButton:SetVisibility(ESlateVisibility.Visible)
    end
    self:updateSelectIndexState(self.currentIndex, self.currentIndex + 1)
    self.currentIndex = self.currentIndex + 1
    if self.currentIndex == self.imgListLength then
        self.View.RightButton:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_NewbieGuideImgText:OnClickClose()
    if self.closeCallback then
        xpcall(self.closeCallback, _G.CallBackError)
        self.closeCallback = nil
    end
    self:CloseSelf()
end

function P_NewbieGuideImgText:updateSelectIndexState(prevIndex, currentIndex)
    self.navigationItemList[prevIndex]:Event_UI_Light(false)
    self.navigationItemList[currentIndex]:Event_UI_Light(true)
    self.imgListView:ScrollToIndex(currentIndex)
    if self.fromNewbieGuide and not self.hadShowCloseBtn and currentIndex == self.imgListLength then
        self.hadShowCloseBtn = true
        self.View.WBP_ComBtnCloseNew:SetVisibility(ESlateVisibility.Visible)
    end
end

return P_NewbieGuideImgText