local TimerComponent = kg_require("Framework.KGFramework.KGCore.TimerManager.TimerComponent")
---@class NewbieGuideStep
local NewbieGuideStep = DefineClass("NewbieGuideStep", TimerComponent)

local UIFunctionLibrary = import("UIFunctionLibrary")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local Anchors = import("Anchors")
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")
local Margin = import("Margin")
local CanvasPanel = import("CanvasPanel")

NewbieGuideStep.MaskUIPath = UIAssetPath.WBP_NewbieGuideMask
NewbieGuideStep.AnimUIPath = UIAssetPath.WBP_NewbieGuideArrow_Item
NewbieGuideStep.CommonTextUIPath = UIAssetPath.WBP_NewbieGuid_Instruction
NewbieGuideStep.TipsTextUIPath = UIAssetPath.WBP_Reminder_NewbieGuide_Tips

NewbieGuideStep.stepFinishScriptPath = "Gameplay.LogicSystem.NewbieGuide.StepFinish."  --"引导完成条件"脚本路径前缀
NewbieGuideStep.customFinishScriptPath = "Gameplay.LogicSystem.NewbieGuide.StepFinish.CustomStepFinish.GuideStepOf" --自定义完成条件脚本路径前缀

local GuideStepFinishOfNone = require("Gameplay.LogicSystem.NewbieGuide.StepFinish.GuideStepFinishOfNone")

--"引导完成条件"脚本路径合集
NewbieGuideStep.FinishTypeScriptPath = {
    [Enum.ENewbieGuideFinishType.CLICK_ANY_FINISH] = NewbieGuideStep.stepFinishScriptPath .."GuideStepFinishOfClickAny",
    [Enum.ENewbieGuideFinishType.CLICK_FINISH] = NewbieGuideStep.stepFinishScriptPath .. "GuideStepFinishOfClickUI",
    [Enum.ENewbieGuideFinishType.PRESS_KEYBOARD] = NewbieGuideStep.stepFinishScriptPath .. "GuideStepFinishOfPressKeyboard",
    [Enum.ENewbieGuideFinishType.DRAG_FINISH] = NewbieGuideStep.stepFinishScriptPath .. "GuideStepFinishOfDrag",
    [Enum.ENewbieGuideFinishType.LONG_PRESS_FINISH] = NewbieGuideStep.stepFinishScriptPath .. "GuideStepFinishOfLongPress",
    [Enum.ENewbieGuideFinishType.PERIOD_FINISH] = NewbieGuideStep.stepFinishScriptPath .. "GuideStepFinishOfPeriod",
    [Enum.ENewbieGuideFinishType.TASK_FINISH] = NewbieGuideStep.stepFinishScriptPath .. "GuideStepFinishOfTask",
}

function NewbieGuideStep:ctor()
    self.stepDetail = nil --步骤详情
    self.onStepFinishCallback = nil --步骤完成回调
    self.finishStepScripts = {}		--完成条件脚本列表
    self.loadResCallbackList = {}	--资源加载回调列表
	self.parentUINode = nil		--引导UI父节点
	self.mountPointUI = nil		--业务UI引导挂载点
	self.bIsForce = nil --是否是强制引导
	self.bSphereGuideArea = false --是否是圆形引导
    self.vectorZero = FVector2D(0, 0)	--vector缓存
    self.vectorOne = FVector2D(1, 1)	--vector缓存
	self.vectorCenter = FVector2D(0.5, 0.5) --vector缓存
	self.maskCenter = {R = 0, G = 0, B = 0, A = 0}  --遮罩位置|大小
	self.guideTextOffset = nil	--提示文本位置偏移值
	self.guideAnimOffset = nil	--提示动画位置偏移值
	self.guideAnimRotate = nil 	--提示动画旋转角度
    self.finishStepCallback = function() self:FinishStep() end	--引导完成条件达成回调
end

function NewbieGuideStep:Execute(stepDetail, onStepFinishCallback)
    self.stepDetail = stepDetail
	self.bIsForce = self.stepDetail.IsForce
	self.bSphereGuideArea = self.stepDetail.GuideArea == Enum.ENewbieGuideAreaType.ROUND_AREA 
    self.onStepFinishCallback = onStepFinishCallback
    if self.bIsForce then
        Game.NewbieGuideSystem:UpdateOperateInterceptState(true)
    end
	if not self.bIsForce then
		Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_UI_OPEN, "OnUIOpen", self)
	else
		Game.PlayerController:SetInputWhiteList(Enum.EInputWhiteListReason.Guide)
	end
    if stepDetail.DropID and stepDetail.DropID ~= 0 then
        Game.NewbieGuideSystem.sender:ReqTriggerGuideOperationReward(stepDetail.ID)
    end
    if string.isEmpty(self.stepDetail.MountPoint) then
		self:InitGuideView()
    else
		self.mountPointUI = self:GetTargetUINode(self.stepDetail.MountPoint, true)
		if self.mountPointUI then
			self:InitGuideView()
		else
			self:StartTimerBindIns("WaitMountPointNodeLoad", "WaitMountPointNodeLoad", 200, -1)
		end
    end
end

function NewbieGuideStep:WaitMountPointNodeLoad()
	self.mountPointUI = self:GetTargetUINode(self.stepDetail.MountPoint, true)
	if self.mountPointUI then
		self:StopTimer("WaitMountPointNodeLoad")
		self:InitGuideView()
	end
end

function NewbieGuideStep:InitGuideView()
	self:getParentNode()
	if not self.parentUINode then
		return
	end
    if self.mountPointUI then
        local sourcePosition = self.mountPointUI.RenderTransform.Translation
		self:StartTimer("WaitMountPointNodeStationary", function()   --节点可能在播放动效，起个timer检查下
			local tmpSourcePos = self.mountPointUI.RenderTransform.Translation
			if tmpSourcePos.X == sourcePosition.X and tmpSourcePos.Y == sourcePosition.Y then
				self:StopTimer("WaitMountPointNodeStationary")
				self:CreateGuideUI()
			else
				sourcePosition = tmpSourcePos
			end
		end, 300, -1, true)
    else
        self:CreateGuideUI()
    end
end

function NewbieGuideStep:CreateFinishScript()
    self:clearFinishScripts()
    
    local finishTypePreStr = "FinishGuide"
    local finishParameterPreStr = "Parameter"
    for i = 1, 4 do
        local finishType = self.stepDetail[finishTypePreStr..tostring(i)]
        local finishParameter = self.stepDetail[finishParameterPreStr..tostring(i)]

        if finishType and finishType > 0 then
			local finishScriptPath = nil
			if finishType == Enum.ENewbieGuideFinishType.CUSTOM_FINISH then --自定义完成条件
				local paramList = string.split(finishParameter, ";")
				finishScriptPath = NewbieGuideStep.customFinishScriptPath .. paramList[1]
				finishParameter = paramList[2]
			else
				finishScriptPath = self.FinishTypeScriptPath[finishType]
			end
			local finishScript = require(finishScriptPath)
			if finishScript then
				local finishScriptIns = finishScript.new(finishParameter, self.finishStepCallback, self)
				self.finishStepScripts[#self.finishStepScripts + 1] = finishScriptIns
			else
				Log.Error("没有该类型的完成条件, OperationID", self.stepDetail.ID)
			end
		end
    end
    if #self.finishStepScripts == 0 then
        local finishScript = GuideStepFinishOfNone.new(self.finishStepCallback)
        self.finishStepScripts[#self.finishStepScripts + 1] = finishScript
    end
end

function NewbieGuideStep:CreateGuideUI()
    if self.bIsForce and not IsValid_L(self.maskUI) then
        local resLoadId = Game.AssetManager:AsyncLoadAssetKeepReferenceID(self.MaskUIPath, self, "OnAsyncLoadFinish")
        self.loadResCallbackList[resLoadId] = self.AddMaskUI
    else
        self:CreateFinishScript()
    end
    if not string.isEmpty(self.stepDetail.GuideArea) and not IsValid_L(self.guideAreaUI) then
        local resLoadId = Game.AssetManager:AsyncLoadAssetKeepReferenceID(self.stepDetail.GuideArea, self, "OnAsyncLoadFinish")
        self.loadResCallbackList[resLoadId] = self.AddGuideAreaUI
    end
    if not string.isEmpty(self.stepDetail.GuideAnimType) and not IsValid_L(self.guideAnimUI) then
        local resLoadId = Game.AssetManager:AsyncLoadAssetKeepReferenceID(self.AnimUIPath, self, "OnAsyncLoadFinish")
        self.loadResCallbackList[resLoadId] = self.AddGuideAnimUI
		if #self.stepDetail.AnimOffset > 1 then
			self.guideAnimOffset = FVector2D(self.stepDetail.AnimOffset[1],self.stepDetail.AnimOffset[2])
			self.guideAnimRotate = self.stepDetail.AnimOffset[3]
		else
			self.guideAnimOffset = self.vectorZero
		end
	end
    if not string.isEmpty(self.stepDetail.GuideText) and not IsValid_L(self.guideTextUI) then
		local textUIPath = string.isEmpty(self.stepDetail.MountPoint) and self.TipsTextUIPath or self.CommonTextUIPath 
        local resLoadId = Game.AssetManager:AsyncLoadAssetKeepReferenceID(textUIPath, self, "OnAsyncLoadFinish")
        self.loadResCallbackList[resLoadId] = self.AddTextUI
		if #self.stepDetail.GuideTextOffset == 2 then
			self.guideTextOffset = FVector2D(self.stepDetail.GuideTextOffset[1],self.stepDetail.GuideTextOffset[2])
		else
			self.guideTextOffset = self.vectorZero
		end
    end
end

function NewbieGuideStep:AddMaskUI(res) 
    self.maskUI = self:createAndBindWidget(res, self.parentUINode)
    if self.maskUI == nil then
        Log.Error("NewbieGuide MaskUI资源加载失败")
        return
    end
	self.maskDynamicMaterial = self.maskUI.Mask_Img_lua:GetDynamicMaterial()
	self.maskDynamicMaterial:SetScalarParameterValue("Int", 1)
	if self.mountPointUI then
		local absoluteSize = SlateBlueprintLibrary.GetAbsoluteSize(self.mountPointUI:GetCachedGeometry())
		local position = UIFunctionLibrary.GetAbsolutePosition(self.mountPointUI, self.vectorZero) + absoluteSize * 0.5
		local viewPortSize = WidgetLayoutLibrary.GetViewportSize(_G.GetContextObject())
		local viewPortScale = WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())

		local pixelPosition = FVector2D(0, 0)
		local viewportPosition = FVector2D(0, 0)
		SlateBlueprintLibrary.AbsoluteToViewport(_G.GetContextObject(), position, pixelPosition, viewportPosition)

		self.hollowOutPosX = viewportPosition.X/viewPortSize.X * viewPortScale
		self.hollowOutPosY = viewportPosition.Y/viewPortSize.Y * viewPortScale
		self.bPlayMaskAnimation = true
		if self.bSphereGuideArea then
			self.hollowOutRadius = absoluteSize.X/viewPortSize.X / 2
			local currentRadius = 1
			local rate = (currentRadius - self.hollowOutRadius) / Game.TableData.GetConstDataRow("NEWBIE_GUIDE_MASK_ANIMATION_TIME")
			self.maskDynamicMaterial:SetScalarParameterValue("Hardness", Game.TableData.GetConstDataRow("NEWBIE_GUIDE_SPHERE_MASK_SOFTNESS"))
			self.maskDynamicMaterial:SetScalarParameterValue("lerp", 1)
			self:StartTickTimer("MaskAnimation", function(spanTime)
				currentRadius = currentRadius - spanTime * rate / 1000
				self:setSphereMaskMaterial(self.hollowOutPosX, self.hollowOutPosY, currentRadius)
				if currentRadius <= self.hollowOutRadius then
					self.bPlayMaskAnimation = false
					Game.NewbieGuideSystem:UpdateOperateInterceptState(false)
					self:setSphereMaskMaterial(self.hollowOutPosX, self.hollowOutPosY, self.hollowOutRadius)
					self:StopTimer("MaskAnimation")
					return
				end
			end, -1, true)
		else
			self.maskDynamicMaterial:SetScalarParameterValue("EdgeSoftness", Game.TableData.GetConstDataRow("NEWBIE_GUIDE_BOX_MASK_SOFTNESS"))
			self.maskDynamicMaterial:SetScalarParameterValue("lerp", 0)
			self.maskDynamicMaterial:SetScalarParameterValue("Size", 1)
			self.hollowOutSizeX = absoluteSize.X/viewPortSize.X
			self.hollowOutSizeY = absoluteSize.Y/viewPortSize.Y
			local currentSizeX = 2
			local currentSizeY = self.hollowOutSizeY * (2/self.hollowOutSizeX)
			local rateX = (currentSizeX - self.hollowOutSizeX) / Game.TableData.GetConstDataRow("NEWBIE_GUIDE_MASK_ANIMATION_TIME")
			local rateY = (currentSizeY - self.hollowOutSizeY) / Game.TableData.GetConstDataRow("NEWBIE_GUIDE_MASK_ANIMATION_TIME")
			self:StartTickTimer("MaskAnimation", function(spanTime)
				self:setBoxMaskMaterial(self.hollowOutPosX, self.hollowOutPosY, currentSizeX, currentSizeY)
				currentSizeX = currentSizeX - rateX * spanTime / 1000
				currentSizeY = currentSizeY - rateY * spanTime / 1000
				if currentSizeX <= self.hollowOutSizeX or currentSizeY <= self.hollowOutSizeY then
					self.bPlayMaskAnimation = false
					Game.NewbieGuideSystem:UpdateOperateInterceptState(false)
					self:setBoxMaskMaterial(self.hollowOutPosX, self.hollowOutPosY, self.hollowOutSizeX, self.hollowOutSizeY)
					self:StopTimer("MaskAnimation")
					return
				end
			end, -1, true)
		end

		self.copyMountPointWidget = UIFunctionLibrary.DeepDuplicateWidget(self.mountPointUI, Game.NewbieGuideSystem:GetGuideUIRoot())
		self.parentUINode:AddChildToCanvas(self.copyMountPointWidget)
		self:setWidgetPositionAndSize(self.copyMountPointWidget, self.mountPointUI)
	else
		self.maskDynamicMaterial:SetScalarParameterValue("lerp", 1)
		self.maskDynamicMaterial:SetScalarParameterValue("Radius", 0)
	end
	self:CreateFinishScript()
end

function NewbieGuideStep:setSphereMaskMaterial(posX, posY, radius)
	self.maskCenter.R = posX
	self.maskCenter.G = posY
	self.maskDynamicMaterial:SetVectorParameterValue("CenterSphere", self.maskCenter)
	self.maskDynamicMaterial:SetScalarParameterValue("Radius", radius)
end

function NewbieGuideStep:setBoxMaskMaterial(posX, posY, sizeX, sizeY)
	self.maskCenter.R = posX
	self.maskCenter.G = posY
	self.maskCenter.B = sizeX
	self.maskCenter.A = sizeY
	self.maskDynamicMaterial:SetVectorParameterValue("BoxCenter", self.maskCenter)
end

function NewbieGuideStep:AddGuideAreaUI(res)
    self.guideAreaUI = self:createAndBindWidget(res, self.parentUINode, self.mountPointUI)
    if self.guideAreaUI == nil then
        Log.Error("NewbieGuide GuideAreaUI资源加载失败")
        return
    end
	local size = self.guideAreaUI.Slot:GetSize()
	self.guideAreaUI:Event_UI_State(size)
end

function NewbieGuideStep:AddGuideAnimUI(res)
    self.guideAnimUI = self:createAndBindWidget(res, self.parentUINode, self.mountPointUI)
    if self.guideAnimUI == nil  then
        Log.Error("NewbieGuide GuideAnimUI资源加载失败")
        return
    end
	if self.guideAnimOffset then
		local sourcePosition = self.guideAnimUI.Slot:GetPosition()
		self.guideAnimUI.Slot:SetPosition(sourcePosition + self.guideAnimOffset)
	end
	if self.guideAnimRotate then
		self.guideAnimUI:SetRenderTransformAngle(self.guideAnimRotate)
	end
	self.guideAnimUI:SetRenderOpacity(0.0)
	self:StartTimer("GuideAnimUISetRenderOpacity", function ()
		self.guideAnimUI:SetRenderOpacity(1.0)
	end, 1, 1)
	self.guideAnimUI:PlayAnimation(self.guideAnimUI[self.stepDetail.GuideAnimType], 0, 0)
end

function NewbieGuideStep:AddTextUI(res)
    self.guideTextUI = self:createAndBindWidget(res, self.parentUINode, self.mountPointUI)
    if self.guideTextUI == nil  then
        Log.Error("NewbieGuide GuideTextUI资源加载失败")
        return
    end
	local tipsText = self.guideTextUI.Text_Tip_lua 
    tipsText:SetText(self.stepDetail.GuideText)
    tipsText:ForceLayoutPrepass()
	local textDesiredSize = tipsText:GetDesiredSize()
    if self.mountPointUI then
		local mountPointGeometry = self.mountPointUI:GetCachedGeometry()
        local viewportSize = WidgetLayoutLibrary.GetViewportSize(_G.GetContextObject())
        local viewPosition = SlateBlueprintLibrary.LocalToViewport(_G.GetContextObject(), mountPointGeometry, self.vectorZero, nil, nil)
        local mountPointSize = SlateBlueprintLibrary.GetLocalSize(mountPointGeometry)
        local sourcePosition = self.guideTextUI.Slot:GetPosition()
        
        local offsetX = viewPosition.X >= viewportSize.X/2 and -mountPointSize.X/2 - textDesiredSize.X/2 - 30 or mountPointSize.X/2 + textDesiredSize.X/2 + 30
        local offsetY = viewPosition.Y >= viewportSize.Y/2 and -mountPointSize.Y/2 - textDesiredSize.Y/2 - 20 or mountPointSize.Y/2 + textDesiredSize.Y/2 + 20
		self.guideTextOffset = FVector2D(self.guideTextOffset.X + offsetX, self.guideTextOffset.Y + offsetY)
        self.guideTextUI.Slot:SetPosition(sourcePosition + self.guideTextOffset)
		self.guideTextUI:SetRenderOpacity(0.0)
		self:StartTimer("GuideTextUISetRenderOpacity", function ()
			self.guideTextUI:SetRenderOpacity(1.0)
		end, 1, 1)
		self.guideTextUI:PlayAnimation(self.guideTextUI.Ani_Fadein, 0, 1)
		tipsText:SetAutoWrapText(false)
    else
		self.guideTextUI:Event_UI_Style(0)
        self.guideTextUI.Slot:SetPosition(self.guideTextOffset)
    end
end

function NewbieGuideStep:createAndBindWidget(res, parentUINode, mountPoint)
    local widget = UIFunctionLibrary.CreateWidgetWithID(_G.GetContextObject(), res)
    if not IsValid_L(widget) then
        return nil
    end
	parentUINode:AddChildToCanvas(widget)
	self:setWidgetPositionAndSize(widget, mountPoint)
    return widget
end

function NewbieGuideStep:setWidgetPositionAndSize(widget, mountPoint, position, localSize)
    local slot = widget.Slot
    if mountPoint then
		if position == nil or localSize == nil then 
			local absoluteSize = SlateBlueprintLibrary.GetAbsoluteSize(mountPoint:GetCachedGeometry())
			localSize = SlateBlueprintLibrary.GetLocalSize(mountPoint:GetCachedGeometry())
			position = UIFunctionLibrary.GetAbsolutePosition(mountPoint, self.vectorZero) + absoluteSize * 0.5
		end
		local parentUINode = widget:GetParent()
		position = SlateBlueprintLibrary.AbsoluteToLocal(parentUINode:GetCachedGeometry(), position)
		slot:SetAlignment(self.vectorCenter)
		slot:SetPosition(position)
		slot:SetSize(localSize)
	else
        local NewAnchors = Anchors()
        NewAnchors.Minimum = self.vectorZero
        NewAnchors.Maximum = self.vectorOne
        slot:SetAnchors(NewAnchors)
        slot:SetOffsets(Margin(0, 0, 0, 0))
    end
end

---OnViewportResize 当窗口大小变化时更新引导UI位置
function NewbieGuideStep:OnViewportResize()
	if self.viewPortResizeTimer == nil then
		--窗口大小变化和UI适配更新时序不确定，起个Timer在窗口变化结束时再刷新下
		self:StartTimerBindIns("ViewportResizeTimer", "refreshGuideUIPosition", 50, 1, false)
	end
	self:refreshGuideUIPosition()
end

function NewbieGuideStep:refreshGuideUIPosition()
	if self.mountPointUI then
		local absoluteSize = SlateBlueprintLibrary.GetAbsoluteSize(self.mountPointUI:GetCachedGeometry())
		
		local localSize = SlateBlueprintLibrary.GetLocalSize(self.mountPointUI:GetCachedGeometry())
		local position = UIFunctionLibrary.GetAbsolutePosition(self.mountPointUI, self.vectorZero) + absoluteSize * 0.5

		if self.copyMountPointWidget then
			self:setWidgetPositionAndSize(self.copyMountPointWidget, self.mountPointUI, position, localSize)
		end
		if self.guideAreaUI then
			self:setWidgetPositionAndSize(self.guideAreaUI, self.mountPointUI, position, localSize)
			local size = self.guideAreaUI.Slot:GetSize()
			self.guideAreaUI:Event_UI_State(size)
		end
		if self.guideAnimUI then
			self:setWidgetPositionAndSize(self.guideAnimUI, self.mountPointUI, position, localSize)
			if self.guideAnimOffset then
				local sourcePosition = self.guideAnimUI.Slot:GetPosition()
				self.guideAnimUI.Slot:SetPosition(sourcePosition + self.guideAnimOffset)
			end
		end
		if self.guideTextUI then
			self:setWidgetPositionAndSize(self.guideTextUI, self.mountPointUI, position, localSize)
			local sourcePosition = self.guideTextUI.Slot:GetPosition()
			self.guideTextUI.Slot:SetPosition(sourcePosition + self.guideTextOffset)
		end
		if self.maskUI and self.maskDynamicMaterial then
			local viewPortSize = WidgetLayoutLibrary.GetViewportSize(_G.GetContextObject())
			local viewPortScale = WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
			local pixelPosition = FVector2D(0, 0)
			local viewportPosition = FVector2D(0, 0)
			SlateBlueprintLibrary.AbsoluteToViewport(_G.GetContextObject(), position, pixelPosition, viewportPosition)
			self.hollowOutPosX = viewportPosition.X / viewPortSize.X * viewPortScale
			self.hollowOutPosY = viewportPosition.Y / viewPortSize.Y * viewPortScale
			self.hollowOutRadius = absoluteSize.X/viewPortSize.X / 2
			if not self.bSphereGuideArea then
				self.hollowOutSizeX = absoluteSize.X/viewPortSize.X
				self.hollowOutSizeY = absoluteSize.Y/viewPortSize.Y
			end
			if not self.bPlayMaskAnimation then
				if self.bSphereGuideArea then
					self:setSphereMaskMaterial(self.hollowOutPosX, self.hollowOutPosY, self.hollowOutRadius)
				else
					self:setBoxMaskMaterial(self.hollowOutPosX, self.hollowOutPosY, self.hollowOutSizeX, self.hollowOutSizeY)
				end
			end
		end
	end
end

function NewbieGuideStep:OnAsyncLoadFinish(loadID, loadedAssetID)
    if self.loadResCallbackList[loadID] then
        self.loadResCallbackList[loadID](self, loadedAssetID)
        self.loadResCallbackList[loadID] = nil
    end
    Game.AssetManager:RemoveAssetReferenceByLoadID(loadID)
end

---FinishStep 完成引导步骤
---@param bForceStop boolean 是否是强制结束
function NewbieGuideStep:FinishStep(bForceStop)
	if not self.bIsForce then
		Game.EventSystem:RemoveObjListeners(self)
	elseif Game.PlayerController then
		Game.NewbieGuideSystem:UpdateOperateInterceptState(false)
		Game.PlayerController:ClearAllInputWhiteList(Enum.EInputWhiteListReason.Guide)
	end
	self:StopAllTimer()
    for loadId, _ in pairs(self.loadResCallbackList) do
        Game.AssetManager:CancelLoadAsset(loadId)
    end
    if IsValid_L(self.maskUI) then
        self.maskUI:RemoveFromParent()
    end
	self.maskDynamicMaterial = nil
    self.maskUI = nil
	
    if IsValid_L(self.copyMountPointWidget) then
        self.copyMountPointWidget:RemoveFromParent()
    end
    self.copyMountPointWidget = nil
    
    if IsValid_L(self.guideAreaUI) then
        self.guideAreaUI:RemoveFromParent()
    end
    self.guideAreaUI = nil
    
    if IsValid_L(self.guideAnimUI) then
        self.guideAnimUI:RemoveFromParent()
    end
    self.guideAnimUI = nil
    
    if IsValid_L(self.guideTextUI) then
		if bForceStop then
			self.guideTextUI:RemoveFromParent()
		else --正常结束时需要播放下完成动画
			self.playTextUIFinishAnim(self.mountPointUI ~= nil, self.guideTextUI)
		end
    end
    self.guideTextUI = nil
	
    self:clearFinishScripts()
    if self.onStepFinishCallback ~= nil then
        self.onStepFinishCallback()
    end
end

function NewbieGuideStep.playTextUIFinishAnim(bIsCustom, guideTextUI)
	local animation = bIsCustom and guideTextUI.Ani_Fadeout or guideTextUI.Ani_Finish
	if not bIsCustom then
		guideTextUI:Event_UI_Style(1)
	end
	guideTextUI:PlayAnimation(animation, 0 ,1)
	local animationFinishedDelegate = slua.createDelegate(function()
		guideTextUI:RemoveFromParent()
	end)
	guideTextUI:BindToAnimationFinished(animation, animationFinishedDelegate)
end

function NewbieGuideStep:GetTargetUINode(path, ignoreError)
	local targetNode = self:getTargetUIView()
	if not targetNode then
		return
	end
    local pathList = string.split(path,".")
    for i, v in pairs(pathList) do
        targetNode = UIFunctionLibrary.GetWidgetFromName(targetNode, v)
        if not targetNode then
            if not ignoreError then
                Log.ErrorFormat("引导ID:%d 依赖的UI节点:%s不存在", self.stepDetail.ID, path)
            end
            return nil
        end
    end
    return targetNode
end

function NewbieGuideStep:getParentNode()
	if self.bIsForce then
		self.parentUINode = Game.NewbieGuideSystem:GetGuideUIRootCanvas()
	elseif not string.isEmpty(self.stepDetail.ParentNode) then
		self.parentUINode = self:GetTargetUINode(self.stepDetail.ParentNode)
	end
	if not self.parentUINode then
		Log.Error("引导UI绑定的父节点不存在，StepID:", self.stepDetail.ID)		
	elseif not self.parentUINode:IsA(CanvasPanel) then
		self.parentUINode = nil
		Log.Error(self.stepDetail.ID,":挂载点需要在Canvas下")
	end
end

function NewbieGuideStep:getTargetUIView()
	local uiPanel = UI.GetUI(self.stepDetail.UIID)
	if not uiPanel then
		Log.ErrorFormat("引导ID:%d 依赖的UI界面:%s不存在", self.stepDetail.ID, self.stepDetail.UIID)
		return
	end
	if uiPanel.View then
		return uiPanel.View.WidgetRoot
	elseif uiPanel.view then
		return uiPanel.view._userWidget
	end
end

function NewbieGuideStep:OnUIOpen(uiName)
    if uiName == self.stepDetail.UIID then
        self:CreateGuideUI()
    end
end

function NewbieGuideStep:GetIsForce()
	return self.bIsForce
end

function NewbieGuideStep:clearFinishScripts()
    if #self.finishStepScripts > 0 then
        for i, finishStepScript in pairs(self.finishStepScripts) do
            if finishStepScript.Clear then
                finishStepScript:Clear()
            end
        end
        table.clear(self.finishStepScripts)
    end
end

function NewbieGuideStep:Clear()
    self.onStepFinishCallback = nil
    self:FinishStep(true)
end

return NewbieGuideStep