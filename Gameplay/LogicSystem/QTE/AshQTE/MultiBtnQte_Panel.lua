
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local CaseMap = Game.TableData.Get_AshButtonCaseMap()
local AshQTEBtn_2_Item = kg_require("Gameplay.LogicSystem.QTE.AshQTE.AshQTEBtn_2_Item")

---@class MultiBtnQte_Panel : UIPanel
---@field view MultiBtnQte_PanelBlueprint
local MultiBtnQte_Panel = DefineClass("MultiBtnQte_Panel", UIPanel)

local ESlateVisibility = import("ESlateVisibility")
local EInputEvent = import("EInputEvent")
local math_random = math.random

MultiBtnQte_Panel.BIND_KEY_CONST = {"Q", "W", "E", "R", "A", "S", "D", "F"}
MultiBtnQte_Panel.RandomIndex = {1, 2, 3, 4, 5, 6, 7, 8}
MultiBtnQte_Panel.QTEInput = {"QTE_01_Action", "QTE_02_Action", "QTE_03_Action", "QTE_04_Action", "QTE_05_Action", "QTE_06_Action", "QTE_07_Action", "QTE_08_Action",}

MultiBtnQte_Panel.eventBindMap = {
    [EEventTypesV2.ROLE_ACTION_INPUT_EVENT] = "OnKeyInput",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MultiBtnQte_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MultiBtnQte_Panel:InitUIData()
end

function MultiBtnQte_Panel:InitButton()
    self.ButtonPool = {}
    self.ButtonInUse = {}
    
    self.QTEObject = nil
    self.RealCase = nil

    self.PressedBtnNum = 0  -- 已经成功按下的按钮数量
    self.ShowingBtnNum = 0  -- 场上正在显示的按钮数量
    self.SpawnBtnNum = 0    -- 总计生成的按钮数量
    
end

--- UI组件初始化，此处为自动生成
function MultiBtnQte_Panel:InitUIComponent()

    self.bIsMobile = PlatformUtil.IsMobilePlatform()
    if not self.bIsMobile then
        self.InputButtonMap = {}
    end
    self:InitButton()
end

---UI事件在这里注册，此处为自动生成
function MultiBtnQte_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MultiBtnQte_Panel:InitUIView()
end

---面板打开的时候触发
function MultiBtnQte_Panel:OnRefresh(Params)
    if Params then
        self.QTEObject = Params.QTEObj
        self.QTEData = self.QTEObject.Data
    end


    if not self.bIsMobile then
        self:BindInput()
    end
    self:InitGameLogic()
end

function MultiBtnQte_Panel:InitGameLogic()
    local MaxButtonNum = math.max(self.QTEData.InitButtonNum, self.QTEData.TriggerAddButtonNum + self.QTEData.AddButtonNum)
    if CaseMap[MaxButtonNum] == nil then
        DebugLogError(string.format("@Designer, QTEConfig.xlsx need Num = %i AshButton Config!", MaxButtonNum))
        return
    end

    local tRealCaseID
    if next(self.QTEData.AshButtonCases) ~= nil then
        -- 指定了Case，则从指定的Case中随机选一个
        tRealCaseID = self.QTEData.AshButtonCases[math.random(#self.QTEData.AshButtonCases)]
        if math.floor(tRealCaseID / 1000) ~= MaxButtonNum then
            DebugLogError(string.format("@Designer, AshButtonCases Num Error! Need %i Give %i",MaxButtonNum, math.floor(tRealCaseID / 1000)))
            -- 指定Case的按钮数量错误，仍然则从所有符合数量的Case中随机选一个
            tRealCaseID = CaseMap[MaxButtonNum][math.random(#CaseMap[MaxButtonNum])]
        end
    else
        -- 不指定Case，则从所有符合数量的Case中随机选一个
        tRealCaseID = CaseMap[MaxButtonNum][math.random(#CaseMap[MaxButtonNum])]
    end
    
    self.RealCase = Game.TableData.GetQTEAshButtonDataRow(tRealCaseID)
    local Title = "Dot"
    for i = 1, MaxButtonNum do
        local tButton = self:GetButton()

        local tPointData = self.RealCase[Title .. i]
        tButton.Position.X = tPointData[1]
        tButton.Position.Y = tPointData[2]
    end

    self:ReturnAllButton()

    for i = 1, self.QTEData.InitButtonNum do
        self:SpawnButton()
    end

    self.PressedBtnNum = 0
end

function MultiBtnQte_Panel:OnClose()
    UIPanel.OnClose(self)
    if not self.bIsMobile then
        self:UnBindInput()
    end
    self:ReturnAllButton()
end


function MultiBtnQte_Panel:GetButton()
    
     local outButton
    if next(self.ButtonPool) ~= nil then
        local BtnPoolList = {}
        for k, v in pairs(self.ButtonPool) do
            table.insert(BtnPoolList, k)
        end
        outButton = BtnPoolList[math.random(#BtnPoolList)]
        self.ButtonInUse[outButton] = true
        self.ButtonPool[outButton] = nil
        self.ShowingBtnNum = self.ShowingBtnNum + 1
    else
        -- outButton = self:FormComponent("ButtonRes", self.view.Button_Panel, AshQTEBtn_2_Item)
        outButton = self:SyncLoadComponent(UICellConfig.AshQTEBtn_2_Item, self.view.Button_Panel)
        -- 绑定按钮按下事件
        -- outButton.view.Btn_ClickArea.OnClicked:Add(function() outButton:on_Btn_ClickArea_Clicked(self) end)
        
        if outButton then
            self.ButtonInUse[outButton] = true
            self.ShowingBtnNum = self.ShowingBtnNum + 1
        end
    end

    return outButton
    
end

function MultiBtnQte_Panel:ReturnButton(InButton)
    if InButton == nil or (not self.ButtonInUse) or self.ButtonInUse[InButton] == nil then
        return
    end

    self.ButtonPool[InButton] = self.ButtonInUse[InButton]
    self.ButtonInUse[InButton] = nil
    self.ShowingBtnNum = self.ShowingBtnNum - 1

    -- InButton.view:SetVisibility(ESlateVisibility.Collapsed)
    
    local ActionKey = InButton:GetInputAction()
    local InputButtonMap = self.InputButtonMap[ActionKey]
    if not InputButtonMap then
        return
    end
    for Index, Button in pairs(InputButtonMap) do
        if Button == InButton then
            table.remove(InputButtonMap, Index)
            break
        end
    end
end

function MultiBtnQte_Panel:ReturnAllButton()
    for Button, _ in pairs(self.ButtonInUse) do
        self:ReturnButton(Button)
    end
end


function MultiBtnQte_Panel:OnDestroy()
    UIPanel.OnDestroy(self)
    if not self.bIsMobile then
        self.InputButtonMap = nil
    end
    self:ClearButtonPool()
end

function MultiBtnQte_Panel:ClearButtonPool()
    self.ButtonPool = nil
    self.ButtonInUse = nil
end

function MultiBtnQte_Panel:ShuffleTable(InTable)
    if InTable == nil or type(InTable) ~= "table" then
        return InTable
    end

    local tTableNum = #InTable
    for i = 1, tTableNum do
        local j = math_random(i, tTableNum)
        InTable[i], InTable[j] = InTable[j], InTable[i]
    end

    return InTable
end

function MultiBtnQte_Panel:BindInput()
    -- 准备键位按钮映射Map
    EnableQTEInput(true)
    local InputMap = self.InputButtonMap
    if next(InputMap) == nil then
        for _, InputName in pairs(Enum.EInputTypeQTEGroup) do
            InputMap[InputName] = {}
        end
    else
        for _, InputName in pairs(Enum.EInputTypeQTEGroup) do
            table.clear(InputMap[InputName])
        end
    end
    self:ShuffleTable(MultiBtnQte_Panel.RandomIndex)
end

function MultiBtnQte_Panel:UnBindInput()
    -- 新框架会自己去清除事件，这里就不用管了
	-- Game.GlobalEventSystem:RemoveTargetAllListeners(self)
    EnableQTEInput(false)
end

function MultiBtnQte_Panel:OnKeyInput(EventName, EventType)
    
    if Enum.EInputTypeQTEGroup[EventName] == nil or EventType ~= EInputEvent.IE_Pressed then
        return
    end
    
    local ActionButtonList = self.InputButtonMap[EventName]
    if ActionButtonList and next(ActionButtonList) ~= nil then
        ActionButtonList[1]:on_Btn_ClickArea_Clicked(self) 
        table.remove(ActionButtonList, 1)
    end
    
end


function MultiBtnQte_Panel:SpawnButton()
    local tButton = self:GetButton()
    tButton:OnActive(self)
    self:BindButtonAction(tButton)
end

function MultiBtnQte_Panel:UpdateButton()
    if self.ShowingBtnNum <= self.QTEData.TriggerAddButtonNum then  
        local NeedSpawnNum = math.min(self.QTEData.AddButtonNum, self.QTEData.SuccessPressBtnNum - self.PressedBtnNum - self.ShowingBtnNum)
        if NeedSpawnNum > 0 then
            for i = 1, NeedSpawnNum do
                self:SpawnButton()
            end
        end
    end
end

function MultiBtnQte_Panel:SuccessPressOne(InButton)
    self.PressedBtnNum = self.PressedBtnNum + 1
    self.QTEObject:Sucessed()

    --self:ReturnButton(InButton)
    -- self.QTEObject:Sucessed()可能会close panel了 这时候就不用update button了
    if self.UpdateButton then 
        -- 更新Button
        self:UpdateButton()
    end
end
function MultiBtnQte_Panel:FailedPressOne(InButton)
    self:ReturnButton(InButton)

    -- 更新Button
    self:UpdateButton()
end

function MultiBtnQte_Panel:BindButtonAction(InButton, keyIndex)
     if InButton and not self.bIsMobile then
        self.SpawnBtnNum = self.SpawnBtnNum + 1
        local Index = self.SpawnBtnNum
        local RandomIndex = MultiBtnQte_Panel.RandomIndex
        local Len = #RandomIndex
        if Index > Len then
            Index = math_random(1, Len)
        else
            Index = RandomIndex[Index]
        end
        InButton:SetInputAction(MultiBtnQte_Panel.QTEInput[Index])
        InButton.view.Text_key:SetText(MultiBtnQte_Panel.BIND_KEY_CONST[Index])
        table.insert(self.InputButtonMap[MultiBtnQte_Panel.QTEInput[Index]], InButton)
    end
end


return MultiBtnQte_Panel
