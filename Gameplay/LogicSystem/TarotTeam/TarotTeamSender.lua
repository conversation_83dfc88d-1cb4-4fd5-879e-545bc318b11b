---@class TarotTeamSender:SystemSenderBase
---@field Bridge TarotTeamComponent 这里基本不涉及其他系统 注解上指向Component
local TarotTeamSender = DefineClass("TarotTeamSender", SystemSenderBase) ---luacheck: ignore

---@Desc 创建临时小队
---@param name string
---@param bornPlace number
---@param tagList number[]
---@param declaration string 宣言
function TarotTeamSender:ReqCreateTarotTeam(name, bornPlace, tagList, declaration)
    self.Bridge:ReqCreateTarotTeam(name, bornPlace, tagList, declaration)
end

---@Desc 解散小队
function TarotTeamSender:ReqDisbandTarotTeam()
    self.Bridge:ReqDisbandTarotTeam()
end

---@Desc 申请小队
---@param tarotTeamID TAROT_TEAM_ID
---@param isCancel boolean
function TarotTeamSender:ReqApplyTarotTeam(tarotTeamID, isCancel)
    self.Bridge:ReqApplyTarotTeam(tarotTeamID, isCancel)
end

---@Desc 处理申请列表
---@param entityIDs ENTITY_ID_LIST entityID列表
---@param accept boolean 是否同意
function TarotTeamSender:ReqHandleTarotTeamApplies(entityIDs, accept)
    self.Bridge:ReqHandleTarotTeamApplies(entityIDs, accept)
end

---@Desc 退出小队
function TarotTeamSender:ReqQuitTarotTeam()
    self.Bridge:ReqQuitTarotTeam()
end

---@Desc remoteRpc 剔出小队
---@param entityID ENTITY_ID
function TarotTeamSender:ReqKickFromTarotTeam(entityID)
    self.Bridge:ReqKickFromTarotTeam(entityID)
end

---@Desc remoteRpc 请求置顶列表
---@param isRecruitTeam boolean
function TarotTeamSender:ReqQueryTarotTeamGetListTop()
    self.Bridge:ReqQueryTarotTeamGetListTop()
end

---@Desc 请求招募队伍列表
---@param searchType number
function TarotTeamSender:ReqQueryTarotTeamSearchList(searchType)
    self.Bridge:ReqQueryTarotTeamSearchList(searchType)
end

---@Desc remoteRpc 查询搜索小队详情
---@param tarotTeamID TAROT_TEAM_ID
function TarotTeamSender:ReqQueryTarotTeamSearchDetailInfo(tarotTeamID)
    self.Bridge:ReqQueryTarotTeamSearchDetailInfo(tarotTeamID)
end

---@Desc remoteRpc 查找小队
---@param searchArgs TAROT_TEAM_SEARCH_ARGS
function TarotTeamSender:ReqQueryTarotTeamSearchTeams(searchArgs)
    self.Bridge:ReqQueryTarotTeamSearchTeams(searchArgs)
end


---@Desc 查询小队详情
---@param tarotTeamID TAROT_TEAM_ID
function TarotTeamSender:ReqQueryTarotTeamDetailInfo()
    self.Bridge:ReqQueryTarotTeamDetailInfo()
end

---@Desc 修改小队详情
---@param tarotTeamModifyArgs TAROT_TEAM_MODIFY_ARGS
function TarotTeamSender:ReqModifyTarotTeamDetail(tarotTeamModifyArgs)
    self.Bridge:ReqModifyTarotTeamDetail(tarotTeamModifyArgs)
end

---@Desc 查看申请列表 (队长可操作)
function TarotTeamSender:ReqGetTarotTeamGetApplies()
    self.Bridge:ReqGetTarotTeamGetApplies()
end

---@Desc 【1】开始建队流程
function TarotTeamSender:ReqStartTarotTeamBuildFlow()
    self.Bridge:ReqStartTarotTeamBuildFlow()
end

---@Desc 【2】交互npc，进入建队仪式
function TarotTeamSender:ReqStartTarotTeamCeremony()
    self.Bridge:ReqStartTarotTeamCeremony()
end

---@Desc 【3】队长同步位次修改
---@param entityIDs ENTITY_ID_LIST
function TarotTeamSender:ReqSyncTarotTeamMemberOrder(entityIDs)
    self.Bridge:ReqSyncTarotTeamMemberOrder(entityIDs)
end

---@Desc 【4】队长确定排位
---@param entityIDs ENTITY_ID_LIST
function TarotTeamSender:ReqConfirmTarotTeamMemberOrder(entityIDs)
    self.Bridge:ReqConfirmTarotTeamMemberOrder(entityIDs)
end

---@Desc 【5】队员签订合同
function TarotTeamSender:ReqAgreeTarotMemberOrder()
    self.Bridge:ReqAgreeTarotMemberOrder()
end

---@Desc 小队界面更改队友次序
function TarotTeamSender:ReqModifyTarotTeamMemberList(entityIDs)
    self.Bridge:ReqModifyTarotTeamMemberList(entityIDs)
end

---@Desc 领工资
function TarotTeamSender:ReqGetTarotTeamWage()
    self.Bridge:ReqGetTarotTeamWage()
end

---@Desc 领等级奖励
function TarotTeamSender:ReqGetTarotTeamLevelReward(level)
    self.Bridge:ReqGetTarotTeamLevelReward(level)
end

---@Desc 一键领等级奖励
function TarotTeamSender:ReqGetTarotTeamAllLevelReward()
    self.Bridge:ReqGetTarotTeamAllLevelReward()
end

---@Desc 隐藏招募
---@param isHidden boolean
function TarotTeamSender:ReqSetTarotTeamHidden(isHidden)
    self.Bridge:ReqSetTarotTeamHidden(isHidden)
end

---@Desc 弹劾队长
function TarotTeamSender:ReqKickTarotTeamLeader()
    self.Bridge:ReqKickTarotTeamLeader()
end

---@Desc 转让队长
function TarotTeamSender:ReqTarotTeamChangeLeader(entityID)
    self.Bridge:ReqTarotTeamChangeLeader(entityID)
end

---@Desc 队长弹劾期间登录
function TarotTeamSender:ReqConfirmTarotTeamChangeLeader(bIsConfirm)
    self.Bridge:ReqConfirmTarotTeamChangeLeader(bIsConfirm)
end

return TarotTeamSender