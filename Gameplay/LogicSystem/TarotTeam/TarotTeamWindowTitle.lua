local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIComSortBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSortBtn")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
---@class TarotTeamWindowTitle : UIComponent
---@field view TarotTeamWindowTitleBlueprint
local TarotTeamWindowTitle = DefineClass("TarotTeamWindowTitle", UIComponent)

TarotTeamWindowTitle.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamWindowTitle:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamWindowTitle:InitUIData()
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onDownSlcItemEvent = LuaDelegate.new()
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onLvSortBtnClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function TarotTeamWindowTitle:InitUIComponent()
    ---@type UIComDropDown
    self.WBP_DownSlcBtnCom = self:CreateComponent(self.view.WBP_DownSlcBtn, UIComDropDown)
    ---@type UIComSortBtn
    self.Img_SortCom = self:CreateComponent(self.view.Img_Sort, UIComSortBtn)
end

---UI事件在这里注册，此处为自动生成
function TarotTeamWindowTitle:InitUIEvent()
    self:AddUIEvent(self.Img_SortCom.onSortEvent, "on_Img_SortCom_SortEvent")
    self:AddUIEvent(self.WBP_DownSlcBtnCom.onItemSelected, "on_WBP_DownSlcBtnCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamWindowTitle:InitUIView()
	
end

---@public Refresh 刷新数据
---@param downSlcData UITabData
function TarotTeamWindowTitle:Refresh(downSlcData)
	self.WBP_DownSlcBtnCom:Refresh(downSlcData)
end

function TarotTeamWindowTitle:SetTextName(text)
	self.view.Text_Name:SetText(text or "")
end

--- 此处为自动生成
---@param status SortBtnStatus
function TarotTeamWindowTitle:on_Img_SortCom_SortEvent(status)
	self.onLvSortBtnClickEvent:Execute(status)
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function TarotTeamWindowTitle:on_WBP_DownSlcBtnCom_ItemSelected(index, data)
	self.onDownSlcItemEvent:Execute(index)
end

return TarotTeamWindowTitle
