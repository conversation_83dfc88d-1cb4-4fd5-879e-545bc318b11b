local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class TarotTeamLevel : UIComponent
---@field view TarotTeamLevelBlueprint
local TarotTeamLevel = DefineClass("TarotTeamLevel", UIComponent)

TarotTeamLevel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamLevel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamLevel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TarotTeamLevel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TarotTeamLevel:InitUIEvent()
    self:AddUIEvent(self.view.LevelBtn.OnClicked, "on_LevelBtn_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamLevel:InitUIView()
end

---组件刷新统一入口
function TarotTeamLevel:Refresh(level)
    Game.RedPointSystem:RegisterRedPoint(self:GetBelongPanel(), self.userWidget, "TarotTeamTotalLevelAward")
	self.view.Text_Num:SetText(level)
    Game.RedPointSystem:ClearNode("TarotTeamTotalLevelAward")
end

--- 此处为自动生成
function TarotTeamLevel:on_LevelBtn_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.TarotTeamRewardRight_Panel)
end

return TarotTeamLevel
