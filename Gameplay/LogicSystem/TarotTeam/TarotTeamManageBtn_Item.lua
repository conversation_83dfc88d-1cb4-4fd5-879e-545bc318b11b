local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TarotTeamManageBtn_Item : UIListItem
---@field view TarotTeamManageBtn_ItemBlueprint
local TarotTeamManageBtn_Item = DefineClass("TarotTeamManageBtn_Item", UIListItem)

TarotTeamManageBtn_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamManageBtn_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---@class TarotTeamManageBtn_Item_Params
---@field name string
---@field icon string
---@field onClickedFunc function
---@field params any

---初始化数据
function TarotTeamManageBtn_Item:InitUIData()
	self.params = nil
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.OnClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function TarotTeamManageBtn_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TarotTeamManageBtn_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamManageBtn_Item:InitUIView()
end

---面板打开的时候触发
function TarotTeamManageBtn_Item:OnRefresh(params)
	self.params = params
	self.view.Text_Name:SetText(params.name)
	self:SetImage(self.view.Img_Icon, params.icon)
	local myParent = self:GetParent():GetParent() ~= nil and self:GetParent():GetParent():GetParent() or self:GetParent()
	if params.name == Game.TableData.GetTarotTeamStringConstDataRow("TAROTTEAM_APPLICATION_MANAGEMENT_BUTTON").StringValue then
		Game.RedPointSystem:RegisterRedPoint(myParent, self.userWidget, "TarotTeamApply")
	end
end


--- 此处为自动生成
function TarotTeamManageBtn_Item:on_Btn_ClickArea_Clicked()
	self.OnClickEvent:Execute()
end

return TarotTeamManageBtn_Item
