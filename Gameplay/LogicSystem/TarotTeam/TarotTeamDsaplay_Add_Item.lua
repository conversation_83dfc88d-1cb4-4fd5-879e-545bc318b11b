local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TarotTeamDsaplay_Add_Item : UIListItem
---@field view TarotTeamDsaplay_Add_ItemBlueprint
local TarotTeamDsaplay_Add_Item = DefineClass("TarotTeamDsaplay_Add_Item", UIListItem)

TarotTeamDsaplay_Add_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamDsaplay_Add_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamDsaplay_Add_Item:InitUIData()
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function TarotTeamDsaplay_Add_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TarotTeamDsaplay_Add_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamDsaplay_Add_Item:InitUIView()
end

---面板打开的时候触发
function TarotTeamDsaplay_Add_Item:OnRefresh(...)
end

--- 此处为自动生成
function TarotTeamDsaplay_Add_Item:on_Btn_ClickArea_Clicked()
	self.onClickEvent:Execute()
end

return TarotTeamDsaplay_Add_Item
