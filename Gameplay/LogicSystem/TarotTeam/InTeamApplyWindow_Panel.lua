local TarotTeamWindowTitle = kg_require("Gameplay.LogicSystem.TarotTeam.TarotTeamWindowTitle")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class InTeamApplyWindow_Panel : UIPanel
---@field view InTeamApplyWindow_PanelBlueprint
local InTeamApplyWindow_Panel = DefineClass("InTeamApplyWindow_Panel", UIPanel)
local GuildInMemberDownSlcBtn = kg_require("Gameplay.LogicSystem.Guild.Inside_2.Member.GuildInMemberDownSlcBtn")
local stringFunc = Game.TableData.GetTarotTeamStringConstDataRow
local table_insert = table.insert

InTeamApplyWindow_Panel.eventBindMap = {
    [EEventTypesV2.ON_ACCEPT_APPLY_RET] = "OnAcceptApplyRet",
    [EEventTypesV2.ON_GET_APPLY_LIST_SUCC] = "OnGetApplyListSucc",
    [EEventTypesV2.ON_TAROT_TEAM_MEMBER_CHANGE] = "UpdateShowText",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function InTeamApplyWindow_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function InTeamApplyWindow_Panel:InitUIData()
    self.applyMemberList = {}
    self.downData = {{type = 0, name = Game.TableData.GetTarotTeamStringConstDataRow("TAROTTEAM_PLAYER_PROFESSION_ALL_TAG").StringValue}}
    for professionId, data in ksbcpairs(Game.TableData.GetPlayerSocialDisplayDataTable()) do
        table.insert(self.downData, {type = professionId, name = data[0].ClassName})
    end
    self.downSelectedIndex = 1
    self.applyShowData = {}
    self.sortRule = {
        { key = "lv", bDescending = false},
    }
	self.defaultSortRule = self.sortRule
end

--- UI组件初始化，此处为自动生成
function InTeamApplyWindow_Panel:InitUIComponent()
    ---@type TarotTeamWindowTitle
    self.WBP_TarotTeamWindowTitleCom = self:CreateComponent(self.view.WBP_TarotTeamWindowTitle, TarotTeamWindowTitle)
    ---@type UIComBoxFrame
    self.WBP_ComPopupLCom = self:CreateComponent(self.view.WBP_ComPopupL, UIComBoxFrame)
    ---@type UIComButton
    self.WBP_ComBtn_RemoveAllCom = self:CreateComponent(self.view.WBP_ComBtn_RemoveAll, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_AcceptAllCom = self:CreateComponent(self.view.WBP_ComBtn_AcceptAll, UIComButton)
    ---@type UIListView
    self.ApplyMemberListCom = self:CreateComponent(self.view.ApplyMemberList, UIListView)
    --self.WBP_ComBtn_AcceptAllCom = self:CreateComponent(self.view.WBP_ComBtn_AcceptAll, UITempComBtn)
    --self.WBP_ComBtn_RemoveAllCom = self:CreateComponent(self.view.WBP_ComBtn_RemoveAll, UITempComBtn)
end

---UI事件在这里注册，此处为自动生成
function InTeamApplyWindow_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_AcceptAllCom.onClickEvent, "on_WBP_ComBtn_AcceptAllCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_RemoveAllCom.onClickEvent, "on_WBP_ComBtn_RemoveAllCom_ClickEvent")
    self:AddUIEvent(self.WBP_TarotTeamWindowTitleCom.onLvSortBtnClickEvent, "on_WBP_TarotTeamWindowTitleCom_LvSortBtnClickEvent")
    self:AddUIEvent(self.WBP_TarotTeamWindowTitleCom.onDownSlcItemEvent, "on_WBP_TarotTeamWindowTitleCom_DownSlcItemEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function InTeamApplyWindow_Panel:InitUIView()
    self.WBP_ComBtn_RemoveAllCom:SetName(stringFunc("TAROTTEAM_CLEAR_LIST").StringValue)
    self.WBP_ComBtn_AcceptAllCom:SetName(stringFunc("TAROTTEAM_FULL_ACCEPTANCE").StringValue)
    self.WBP_ComPopupLCom:Refresh(stringFunc("TAROTTEAM_APPLICATION_MANAGEMENT").StringValue)
    self:UpdateShowText()
    -- 手动绑定的事件以及组件 临时放这里（自动生成会清理）
    --self:AddUIEvent(self.view.WBP_ComBtn_AcceptAll.Btn_Com_lua.OnClicked, "on_AcceptAll_Clicked")
    --self:AddUIEvent(self.view.WBP_ComBtn_RemoveAll.Btn_Com_lua.OnClicked, "on_RemoveAll_Clicked")
    --self:AddUIEvent(self.view.WBP_ComPopupL.WBP_ComPopupTitle_lua.WBP_ComBtnClose_lua.OnClicked, "on_Close_Clicked")
    --self:AddUIEvent(self.view.WBP_TarotTeamWindowTitle.Img_Sort.Btn_ClickArea_lua.OnClicked, "OnClickSortByLv")
    
    ---@type GuildInMemberDownSlcBtn
    --self.WBP_TagDown_luaCom = self:CreateComponent(self.view.WBP_TarotTeamWindowTitle.WBP_DownSlcBtn, GuildInMemberDownSlcBtn)
end

---面板打开的时候触发
function InTeamApplyWindow_Panel:OnRefresh(...)
    self.bDefaultSort = true
    --self.view.WBP_TarotTeamWindowTitle.Img_Sort:Event_UI_Style(0, -50)
    Game.TarotTeamSystem.sender:ReqGetTarotTeamGetApplies()
	self.WBP_TarotTeamWindowTitleCom:Refresh(self.downData)
    self.WBP_TarotTeamWindowTitleCom:SetTextName(stringFunc("TAROTTEAM_PLAYER_NAME_TAG").StringValue)
end

function InTeamApplyWindow_Panel:OnGetApplyListSucc(roleBriefInfoList)
    table.clear(self.applyMemberList)
    -- 服务端返回的数据是新的插入到后面，需要反序处理（最新申请放到最前面）
    if roleBriefInfoList then
        for i = #roleBriefInfoList, 1, -1 do
            table_insert(self.applyMemberList, roleBriefInfoList[i])
        end
    end
    self:RefreshApplyList()
end

function InTeamApplyWindow_Panel:RefreshApplyList()
    table.clear(self.applyShowData)
    for _, memberData in ipairs(self.applyMemberList) do
        if self.downData[self.downSelectedIndex].type == 0 or self.downData[self.downSelectedIndex].type == memberData.school then
            table_insert(self.applyShowData, memberData)
        end
    end
    if not self.bDefaultSort then
        Game.TarotTeamSystem:SortListByRule(self.applyShowData, self.sortRule)
    end
    self.ApplyMemberListCom:Refresh(self.applyShowData)
    if #self.applyMemberList == 0 then
        self.userWidget:Event_UI_Empty(true)
    else
        self.userWidget:Event_UI_Empty(false)
    end
end

--- 此处为自动生成
function InTeamApplyWindow_Panel:on_WBP_TarotTeamWindowTitleCom_DownSlcItemEvent(index)
	self.downSelectedIndex = index
	self:RefreshApplyList()
end

function InTeamApplyWindow_Panel:OnAcceptApplyRet(entityIDs, isAccept)
    if isAccept then
        for _, roleBriefInfoId in ipairs(entityIDs) do
            for i, memberData in ipairs(self.applyMemberList) do
                if memberData.id == roleBriefInfoId then
                    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_ACCEPT_APPLICATION, {{memberData.rolename}})
                    break
                end
            end
        end
    else
        if table.count(entityIDs) == 1 then
            for i, memberData in ipairs(self.applyMemberList) do
                if memberData.id == entityIDs[1] then
                    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_REJECT_APPLICATION, {{memberData.rolename}})
                    break
                end
            end
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_APPLICATION_LIST_CLEAR)
        end
    end
    table.removeByFunc(self.applyMemberList, function(item) 
        return table.contains(entityIDs, item.id)
    end)
    Game.TarotTeamSystem.model.hasNewApplyMsg = table.count(self.applyMemberList) > 0
    Game.RedPointSystem:ClearNode("TarotTeamApply")
    Game.RedPointSystem:ClearNode("TarotTeamManageBtn")
    Game.RedPointSystem:ClearNode("TarotTeam")
    self:UpdateShowText()
    self:RefreshApplyList()
end

function InTeamApplyWindow_Panel:UpdateShowText()
    local teamInfo = Game.TarotTeamSystem.model.ownTarotTeamInfo
    if #teamInfo.memberList < Game.TableData.GetTarotTeamSettingDataRow("MaxTarotTeamMemberNumber") then
        self.view.RichText_Num:SetText(string.format("当前小队成员：<TarotTeamLight>%s</>/%s", 
                            #teamInfo.memberList, Game.TableData.GetTarotTeamSettingDataRow("MaxTarotTeamMemberNumber")))
    else
        self.view.RichText_Num:SetText(string.format("当前小队成员：<Grey>%s</>/%s", 
                            #teamInfo.memberList, Game.TableData.GetTarotTeamSettingDataRow("MaxTarotTeamMemberNumber")))
    end
end

--- 此处为自动生成
function InTeamApplyWindow_Panel:on_WBP_ComBtn_AcceptAllCom_ClickEvent()
	self:on_ManagerBtn_Clicked(true)
end

--- 此处为自动生成
function InTeamApplyWindow_Panel:on_WBP_ComBtn_RemoveAllCom_ClickEvent()
	self:on_ManagerBtn_Clicked(false)
end

--- 此处为自动生成
function InTeamApplyWindow_Panel:on_WBP_TarotTeamWindowTitleCom_LvSortBtnClickEvent(status)
	self.bDefaultSort = false
	self:ToggleSortKey("lv", status)
	self:RefreshApplyList()
end

function InTeamApplyWindow_Panel:ToggleSortKey(keyName, status)
    local keyIndex
    for index, sortData in ipairs(self.sortRule) do
        if sortData.key == keyName then
            keyIndex = index
            break
        end
    end
    if keyIndex then
		if status == Enum.SortBtnStatus.None then
			self.sortRule[keyIndex].bDescending = self.defaultSortRule[keyName]
			table_insert(self.sortRule, table.remove(self.sortRule, keyIndex))
			return
		end
		if status == Enum.SortBtnStatus.Descending then
			self.sortRule[keyIndex].bDescending = true
		else
			self.sortRule[keyIndex].bDescending = false
		end
		table_insert(self.sortRule, 1, table.remove(self.sortRule, keyIndex))
    end
end

--- 管理一键接受or拒绝
function InTeamApplyWindow_Panel:on_ManagerBtn_Clicked(accept)
    ---全部拒绝
    if #self.applyMemberList <= 0 then
        return
    end
    local entityIDs = {}
    for _, roleBriefInfo in ipairs(self.applyMemberList) do
        table_insert(entityIDs, roleBriefInfo.id)
    end
    Game.TarotTeamSystem.sender:ReqHandleTarotTeamApplies(entityIDs, accept)
end

return InTeamApplyWindow_Panel
