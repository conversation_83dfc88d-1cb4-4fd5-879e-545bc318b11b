local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class TarotTeamInviteWindow : UIComponent
---@field view TarotTeamInviteWindowBlueprint
local TarotTeamInviteWindow = DefineClass("TarotTeamInviteWindow", UIPanel)
local stringFunc = Game.TableData.GetTarotTeamStringConstDataRow
local ESlateVisibility = import("ESlateVisibility")
TarotTeamInviteWindow.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamInviteWindow:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamInviteWindow:InitUIData()
    self.params = {}
	self.inputLimit = 50
	self.leftTextNum = 50
	self.oldText = ""
end

--- UI组件初始化，此处为自动生成
function TarotTeamInviteWindow:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtn_2Com = self:CreateComponent(self.view.WBP_ComBtn_2, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_1Com = self:CreateComponent(self.view.WBP_ComBtn_1, UIComButton)
    ---@type UIComBoxFrame
    self.WBP_ComPopupSCom = self:CreateComponent(self.view.WBP_ComPopupS, UIComBoxFrame)
end

---UI事件在这里注册，此处为自动生成
function TarotTeamInviteWindow:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_1Com.onClickEvent, "on_WBP_ComBtn_1Com_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_2Com.onClickEvent, "on_WBP_ComBtn_2Com_ClickEvent")
    self:AddUIEvent(self.view.EditText_Input.OnC7TextChanged, "on_EditText_Input_C7TextChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamInviteWindow:InitUIView()
	self.WBP_ComBtn_1Com:SetName(stringFunc("TAROTTEAM_CONFIRM_MODIFICATION_BUTTON").StringValue)
	self.WBP_ComBtn_2Com:SetName(stringFunc("TAROTTEAM_CANCEL_BUTTON").StringValue)
	self.view.EditText_input:SetHintText(stringFunc("TAROTTEAM_INPUT_DECLARATION").StringValue)
	self.view.Text_NumLight:SetVisibility(ESlateVisibility.Visible)
	self.view.Text_Tip:SetVisibility(ESlateVisibility.Visible)
end

---@class TarotTeamInviteWindowParmas
---@field callback fun()
---@field uiComponent UIComponent
---@field titleText string
---@field message string

---组件刷新统一入口
function TarotTeamInviteWindow:Refresh(params)
	self.WBP_ComPopupSCom:Refresh(params.titleText)
    self.params = params
	self.view.Text_NumLight:SetText(string.format("%d", self.leftTextNum))
end

function TarotTeamInviteWindow:on_WBP_ComBtn_1Com_ClickEvent()
    if not Game.TarotTeamSystem:hasOwnTeam() then
        return
    end

    local declaration = self.view.EditText_input:GetText()
    if string.isEmpty(declaration) then
        return
    end
    
    if self.params and self.params.callback then
        self.params.callback(self.params.uiComponent, self.params, declaration)
    end
    self:CloseSelf()
end

function TarotTeamInviteWindow:on_WBP_ComBtn_2Com_ClickEvent()
    self:CloseSelf()
end

function TarotTeamInviteWindow:CountChars(str)
	--汉字、数字、字符都按照一个计数
	local len = 0
	if str then
		len = utf8.len(str)
	end
	return len
end

--- 此处为自动生成
---@param text FText
function TarotTeamInviteWindow:on_EditText_Input_C7TextChanged(text)
	local textLength = self:CountChars(text)
	if textLength > self.inputLimit then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_INPUT_MAX)
	else
		self.oldText = text
		self.leftTextNum = self.inputLimit - textLength
	end
	self.view.EditText_input:SetText(self.oldText)
	self.view.Text_NumLight:SetText(string.format("%d", self.leftTextNum))
end

return TarotTeamInviteWindow
