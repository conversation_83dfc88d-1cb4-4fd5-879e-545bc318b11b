---@class TarotTeamSystem
local TarotTeamSystem = DefineClass("TarotTeamSystem", SystemBase) ---luacheck: ignore

local tarot_team_const = kg_require("Shared.Const.TarotTeamConsts")
local const = kg_require("Shared.Const")
local tarot_team_status = tarot_team_const.TAROT_TEAM_STATUS

-- 模型展示位置标签
TarotTeamSystem.ETeamDisplayPlayStartList = {
    [1] = "TeamDisplayFirst",
    [2] = "TeamDisplaySecond",
    [3] = "TeamDisplayThird",
    [4] = "TeamDisplayFourth",
    [5] = "TeamDisplayFifth",
    [6] = "TeamDisplaySixth",
    [7] = "TeamDisplaySeventh",
    [8] = "TeamDisplayEighth",
    [9] = "TeamDisplayNinth",
    [10] = "TeamDisplayTenth",
    [11] = "TeamDisplayEleventh",
    [12] = "TeamDisplayTwelfth",
}

-- 模型展示位置前后标签
TarotTeamSystem.ETeamDisplayPlayPositionTags = {
    CAMERA = "TarotTeamDisplay",
    FRONT = "FrontModel",
    MIDDLE = "MiddleModel",
    BACK = "BackModel"
}

TarotTeamSystem.ETeamDisplayBackTags = {
    [1] = "TeamDisplaySecond",
    [2] = "TeamDisplayFourth",
    [3] = "TeamDisplaySeventh",
    [4] = "TeamDisplayNinth"
}

TarotTeamSystem.EReconnetState = {
    TarotTeamOrder = 0,
    TarotTeamContract = 1
}

function TarotTeamSystem:onInit()
    ---@type TarotTeamModel
    self.model = kg_require("GamePlay.LogicSystem.TarotTeam.TarotTeamModel").new(false, true)
    ---@type TarotTeamSender
    self.sender = kg_require("GamePlay.LogicSystem.TarotTeam.TarotTeamSender").new()

    -- 断线重连之后保留打开面板数据
    Game.EventSystem:AddListener(_G.EEventTypes.GAME_ENTER_STAGE_SHOW_UI_END, self, self.RefreshUI)
    self:refreshSystemState()
end

--- 检查系统是否开启
-- function TarotTeamSystem:CheckSystemUnlockState()

-- end

-- 设置重连状态
function TarotTeamSystem:SetReconnetState(state, params)
    self.model.reconnectState = state
    if state then
        self.model.reconnectParams[state] = params
    end
end

function TarotTeamSystem:RefreshUI()
    if self.model.reconnectState == nil then return end
    self:StartTimer("TarotTeamSystemRefreshUI", function()
        if self.model.reconnectState == TarotTeamSystem.EReconnetState.TarotTeamOrder then
            local parmas = self.model.reconnectParams[TarotTeamSystem.EReconnetState.TarotTeamOrder]
            self:OnMsgStartTarotTeamCeremony(unpack(parmas))
        elseif self.model.reconnectState == TarotTeamSystem.EReconnetState.TarotTeamContract then
            local parmas = self.model.reconnectParams[TarotTeamSystem.EReconnetState.TarotTeamContract]
            UI.ShowUI("P_ChatSmallWindowLayer", parmas[6], Enum.EChatTarget.Club, Game.ChatSystem:GetChatSetting(Enum.EChatSetting["DefaultSmallChatWins"]), true)
            self:OnMsgMemberConfirmTarotTeamMemberList(unpack(parmas))
        end
    end, 500, 1)
end

function TarotTeamSystem:onUnInit()
    Game.EventSystem:RemoveObjListeners(self)
    self.model.cameraLocation = nil
    self.model.hasNewApplyMsg = false
end

function TarotTeamSystem:SortListByRule(dataList, sortRule)
    table.sort(dataList, function(memberA, memberB)
        for _, sortData in pairs(sortRule) do
            local aValue, bValue = memberA[sortData.key], memberB[sortData.key]
            if not aValue or not bValue then
                return true
            end
            if aValue ~= bValue then
                if sortData.bDescending then
                    return aValue < bValue
                else
                    return aValue > bValue
                end
            end
        end
        -- return tostring(memberA) > tostring(memberB)
    end)
    return dataList
end

--- 系统已经解锁的情况下刷新自己的数据信息等
function TarotTeamSystem:refreshSystemState()
    self.model.tarotTeamInfo = {} --TODO 初始化所有的信息
end

-- 更换客户端小队成员职业显示（主要给美术使用）
function TarotTeamSystem:SwitchMemberProfession(position, classId)
    if position == nil or position == "" then
        self.model.testSwitch = not self.model.testSwitch
        Game.GameClientData:SaveGlobal("TarotTeamFillModel", self.model.testSwitch)
        return
    end
    if self.model.showTeamInfoList and self.model.showTeamInfoList[position] then
        self.model.showTeamInfoList[position].classId = classId
        self.model.classIdMap[position] = classId
        Game.GameClientData:SaveGlobal("TarotTeamModelClassIdMap", self.model.classIdMap)
    end
 end
----------------------------------------------------------------------------------------------------
--#region UI Open
--- 打开塔罗小队的面板 主面板入口
---@param defaultRankType TAROT_TEAM_RANK_TYPE|nil
function TarotTeamSystem:TryToOpenTarotTeamUI()
    self.model.hasNewApplyMsg = nil
    self:InitRedPoint()
    if Game.me.tarotTeamID ~= tarot_team_const.TAROT_TEAM_DEFAULT_ID then
        self.sender:ReqQueryTarotTeamDetailInfo()
    else
        self.sender:ReqQueryTarotTeamGetListTop()
    end
end

function TarotTeamSystem:OpenTarotTeamUI()
    local ownTeamInfo = self.model.ownTarotTeamInfo
    if ownTeamInfo == nil then
        return
    end
    ---小队状态查看
    local teamStatus = Game.me.tarotTeamStatus
    if teamStatus == tarot_team_status.FINAL then
        ---正式小队的展示界面
        if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeamDisplay_Panel) then
            Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_MEMBER_CHANGE)
        else
            Game.NewUIManager:OpenPanel(UIPanelConfig.TarotTeamDisplay_Panel)
        end
        return
    elseif teamStatus == tarot_team_status.BUILDING then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_BUILD_BUTTON_INTERCEPTION)
        return
    end
    self.sender:ReqQueryTarotTeamGetListTop()
    --- 打开List界面
    self:OpenTarotTeamListUI()
end

--- 打开小队列表界面
function TarotTeamSystem:OpenTarotTeamListUI()
    if not Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeamListInfo_Panel) then
        Game.NewUIManager:OpenPanel(UIPanelConfig.TarotTeamListInfo_Panel)
    end
end

--- 打开申请列表面板
function TarotTeamSystem:OpenTarotTeamApplyUI()
    Game.NewUIManager:OpenPanel(UIPanelConfig.InTeamApplyWindow_Panel)
end

-- 请离小队
function TarotTeamSystem:KickoffTarotTeam(entityID)
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.TAROTTEAM_KICK_POPUP,
            function()
                self.sender:ReqKickFromTarotTeam(entityID)
            end, nil, {self.model.tarotTeamRoleBriefInfoMap[entityID].rolename})
end

function TarotTeamSystem:OnMsgTarotTeamHandleApplies(entityIDs, isAccept)
    if isAccept then
        self.model.ownTarotTeamInfo = self.model.ownTarotTeamInfo or {}
        self.model.tarotTeamRoleBriefInfoMap = self.model.tarotTeamRoleBriefInfoMap or {}
        for _, entityID in pairs(entityIDs) do
            self.model.tarotTeamRoleBriefInfoMap[entityID] = entityID
            table.insert(self.model.ownTarotTeamInfo.memberList, entityID)
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_ACCEPT_APPLY_RET, entityIDs, isAccept)
end
-- #endregion UI

----------------------------------------------------------------------------------------------------
--#region 服务器回调

---@Desc (own)入队小队回调  [创建 or 同意加入]
---@param tarotTeamBriefInfo TAROT_TEAM_BRIEF_INFO
function TarotTeamSystem:OnMsgTarotTeamJoin(tarotTeamBriefInfo)
    if tarotTeamBriefInfo.id then
        self.model.ownTarotTeamInfo = tarotTeamBriefInfo
        -- self.model.ownTeamSearchInfo = tarotTeamBriefInfo
    end
    Game.NewUIManager:ClosePanel(UIPanelConfig.TarotTeamCreate_Panel)
    Game.NewUIManager:ClosePanel(UIPanelConfig.TarotTeam_Panel)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_JOIN_TAROT_TEAM_SUCC, tarotTeamBriefInfo)
    self:TryToOpenTarotTeamUI()
end

---@Desc 退队小队回调
---@param tarotTeamID TAROT_TEAM_ID
function TarotTeamSystem:OnMsgTarotTeamQuit(tarotTeamID)
    self:ClearTarotTeamInfo()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_QUIT_TAROT_TEAM_SUCC)
end

---@Desc 解散小队回调
---@param tarotTeamID TAROT_TEAM_ID
function TarotTeamSystem:OnMsgTarotTeamDisband(tarotTeamID)
    self:ClearTarotTeamInfo()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_DISHBAND, tarotTeamID)
end

function TarotTeamSystem:ClearTarotTeamInfo()
    self.model.ownTarotTeamInfo = nil
    self.model.hasNewApplyMsg = false
    Game.RedPointSystem:ClearNode("TarotTeamApply")
    Game.RedPointSystem:ClearNode("TarotTeamManageBtn")
    Game.RedPointSystem:ClearNode("TarotTeamLevelAward")
    Game.RedPointSystem:ClearNode("TarotTeamTotalLevelAward")
    Game.RedPointSystem:ClearNode("TarotTeamWageAward")
    Game.RedPointSystem:ClearNode("TarotTeam")
end

-- 队长取消申请 回调
function TarotTeamSystem:OnMsgTarotTeamApplyHandled(tarotTeamId, bIsAccept)
    for i, applyInfo in ipairs(self.model.appliedTeamInfoList) do
        if applyInfo.id == tarotTeamId then
            table.remove(self.model.appliedTeamInfoList, i)
            break
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_LIST_TOP_CHANGE, self.model.ownTeamSearchInfo, self.model.appliedTeamInfoList)
end

---@Desc clientRpc 自己的队伍信息和已经申请的队伍信息
---@param ownTeamSearchInfo TAROT_TEAM_SEARCH_INFO
---@param appliedTeamInfoList TAROT_TEAM_SEARCH_INFO_LIST
function TarotTeamSystem:OnMsgTarotTeamGetListTop(ownTeamSearchInfo, appliedTeamInfoList)
    self.model.ownTeamSearchInfo = ownTeamSearchInfo
    self.model.appliedTeamInfoList = appliedTeamInfoList
    -- 清理一下上个角色的数据（如有）
    if self.model.ownTarotTeamInfo and ownTeamSearchInfo.id ~= self.model.ownTarotTeamInfo.id then
        self.model.ownTarotTeamInfo = nil
    end
    if ownTeamSearchInfo.id ~= tarot_team_const.TAROT_TEAM_DEFAULT_ID or (self.model.appliedTeamInfoList and #self.model.appliedTeamInfoList > 0 and not Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeamListInfo_Panel)) then
        self:OpenTarotTeamListUI()
    elseif not Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeamListInfo_Panel) then
        Game.NewUIManager:OpenPanel(UIPanelConfig.TarotTeam_Panel)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_LIST_TOP_CHANGE, ownTeamSearchInfo, appliedTeamInfoList)
end

---@Desc clientRpc 有新的申请(通知)
function TarotTeamSystem:OnMsgTarotTeamNeedHandleApplies()
    self.model.hasNewApplyMsg = true
    Game.RedPointSystem:ClearNode("TarotTeamApply")
    Game.RedPointSystem:ClearNode("TarotTeamManageBtn")
    Game.RedPointSystem:ClearNode("TarotTeam")
end

--region 红点
function TarotTeamSystem:InitRedPoint()
    Game.RedPointSystem:AddListener("TarotTeam", self, self.CanMenuShowRedPoint)
    Game.RedPointSystem:AddListener("TarotTeamApply", self, self.CanShowApplyRedPoint)
    Game.RedPointSystem:AddListener("TarotTeamManageBtn", self, self.CanShowApplyRedPoint)
    Game.RedPointSystem:AddListener("TarotTeamLevelAward", self, self.CanShowLevelAwardRedPoint)
    Game.RedPointSystem:AddListener("TarotTeamWageAward", self, self.CanShowWageAwardRedPoint)
    Game.RedPointSystem:AddListener("TarotTeamTotalLevelAward", self, self.CanShowTotalLevelAwardRedPoint)
    self:UpdateApplyRedPointInfo()
end

function TarotTeamSystem:UpdateApplyRedPointInfo()
    -- Game.RedPointSystem:ClearNode("TarotTeamApply")
    -- Game.RedPointSystem:ClearNode("TarotTeamLevelAward")
    -- Game.RedPointSystem:ClearNode("TarotTeamTotalLevelAward")
    -- Game.RedPointSystem:ClearNode("TarotTeamWageAward")
    Game.RedPointSystem:ClearNode("TarotTeam")
end

function TarotTeamSystem:CanMenuShowRedPoint()
    if self:CanShowApplyRedPoint() then
        return true
    end
    if self:CanShowTotalLevelAwardRedPoint() then
        return true
    end
    if self:CanShowWageAwardRedPoint() then
        return true
    end
end

function TarotTeamSystem:CanShowApplyRedPoint()
    if self.model.hasNewApplyMsg == nil then
        self.sender:ReqGetTarotTeamGetApplies()
        return false
    end
    return self.model.hasNewApplyMsg
end

function TarotTeamSystem:CanShowTotalLevelAwardRedPoint()
    if Game.me.tarotTeamID == tarot_team_const.TAROT_TEAM_DEFAULT_ID then
        return false
    end
    local rewardMap = Game.me.tarotTeamLevelRewardMap
    if self.model.ownTarotTeamInfo and self.model.ownTarotTeamInfo.level and self.model.ownTarotTeamInfo.level > 0 then
        if not rewardMap or #rewardMap == 0 then
            return true
        end
        for i=self.model.ownTarotTeamInfo.level, 1, -1 do
            if not rewardMap[i] then
                return true
            end
        end
    end
    return false
end

function TarotTeamSystem:CanShowLevelAwardRedPoint(id)
    if Game.me.tarotTeamID == tarot_team_const.TAROT_TEAM_DEFAULT_ID then
        return false
    end
    if not self.model.ownTarotTeamInfo or self.model.ownTarotTeamInfo.level < id then
        return false
    end
    local rewardMap = Game.me.tarotTeamLevelRewardMap
    if not rewardMap or table.count(rewardMap) == 0 or not rewardMap[id] then
        return true
    end
    return false
end

function TarotTeamSystem:CanShowWageAwardRedPoint()
    if Game.me.tarotTeamID == tarot_team_const.TAROT_TEAM_DEFAULT_ID then
        return false
    end
    local ownTarotTeamInfo = self.model.ownTarotTeamInfo
    if not ownTarotTeamInfo or Game.me.tarotTeamStatus ~= tarot_team_status.FINAL or not ownTarotTeamInfo.scoreInfo then
        return false
    end
    return ownTarotTeamInfo.scoreInfo.memberWagedMap[Game.me.eid] and ownTarotTeamInfo.scoreInfo.memberWagedMap[Game.me.eid] == 0 and ownTarotTeamInfo.scoreInfo.lastWeekWage ~= 0
    -- return timeUtils.isSameWeek(Game.me.lastTarotTeamWageTime, _G._now(1))
end

function TarotTeamSystem:UpdateApplyList(tarotTeamInfo, isCancel)
    if not isCancel then
        -- if self.model.ownTarotTeamInfo then
        --     table.insert(self.model.ownTarotTeamInfo.memberList, tarotTeamInfo)
        -- end
        table.insert(self.model.appliedTeamInfoList, tarotTeamInfo)
    else
        for i, info in ipairs(self.model.appliedTeamInfoList) do
            if info.id == tarotTeamInfo.id then
                table.remove(self.model.appliedTeamInfoList, i)
                break
            end
        end
    end 
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MSG_TAROT_TEAM_APPLY_SUCC, tarotTeamInfo, isCancel)
end

--endregion


---@Desc 修改小队信息返回
---@param tarotTeamID TAROT_TEAM_ID
---@param modifyArgs TAROT_TEAM_MODIFY_ARGS
function TarotTeamSystem:OnMsgTarotTeamGetModifyDetailInfo(tarotTeamID, modifyArgs)
    local tarotTeamInfo = self.model.ownTarotTeamInfo
    if tarotTeamInfo and tarotTeamInfo.id == tarotTeamID then
        tarotTeamInfo.name = modifyArgs.name and modifyArgs.name or tarotTeamInfo.name
        tarotTeamInfo.bornPlace = modifyArgs.bornPlace and modifyArgs.bornPlace or tarotTeamInfo.bornPlace
        tarotTeamInfo.tagList = modifyArgs.tagList and modifyArgs.tagList or tarotTeamInfo.tagList
    end

    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_MSG_MODIFY_SUCC, tarotTeamID, modifyArgs)
end

---@Desc clientRpc 查看详细队伍详细信息
---@param tarotTeamSearchDetailInfo TAROT_TEAM_SEARCH_DETAIL_INFO
function TarotTeamSystem:OnMsgTarotTeamGetSearchDetailInfo(tarotTeamSearchDetailInfo)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_DETAIL_INFO_SUCC, tarotTeamSearchDetailInfo)
end

---@Desc 领取工资回调P
function TarotTeamSystem:OnMsgTarotTeamGetWageSucc()
    self.model.ownTarotTeamInfo.scoreInfo.memberWagedMap[Game.me.eid] = _G._now(1)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_GET_WAGE)
end

-- 建队校验通过 开始建队流程 
function TarotTeamSystem:OnMsgTarotTeamStartBuild()
    if not (Game.TeamSystem:IsInTeam() or Game.TeamSystem:IsInGroup()) then
        -- 队长团队ID发生变化
        Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_MAINPLAYER_GROUP_ID_CHANGED, "OnGroupIDChange", self)
        Game.GroupSystem:ReqBuildGroup()
    elseif not self:CheckIsNormalTeamLeader() then
        return 
    else
        self:OnGroupIDChange()
    end
    -- 邀请组队
    for _, memberId in ipairs(self.model.ownTarotTeamInfo.memberList) do
        self:StartTimer("reqInviteJoinTeam_" .. memberId, function()
            if self.model.tarotTeamRoleBriefInfoMap[memberId] and self.model.tarotTeamRoleBriefInfoMap[memberId].state == const.FRIEND_SYSTEM_PLAYER_STATE.ONLINE and 
            not Game.me.teamInfoList[memberId] then
                if table.count(Game.me.teamInfoList) > 0 then
                    Game.me:ReqInviteJoinTeam(memberId)
                else
                    Game.me:InviteJoinGroup(memberId, 0)
                end
            end
        end, 600, 1)
    end
    -- local teamMemnbersNum = math.max(table.count(Game.GroupSystem:GetMyGroupMemberIDList()), table.count(Game.me.teamInfoList))
    -- if teamMemnbersNum < Game.TableData.GetTarotTeamSettingDataRow("MinTarotTeamBuildTeamNearbyCount") then
    --     -- Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_BUILD_FAILED_FOR_PLAYERS_NOT_ENOUGH,{{Game.TableData.GetTarotTeamSettingDataRow("MinTarotTeamBuildTeamNearbyCount"), Game.TableData.GetTarotTeamSettingDataRow("MaxTarotTeamMemberNumber")}})
    --     return
    -- end
    -- Game.TarotTeamSystem.sender:ReqStartTarotTeamBuildFlow()
    Game.NewUIManager:ClosePanel(UIPanelConfig.TarotTeamListInfo_Panel)
    local npcID = Game.TableData.GetTarotTeamSettingDataRow("TarotTeamBuildTeamNpcSpawnerId")
    local InsIDs = Game.WorldDataManager:GetInsIDsByNpcTemplateID(npcID)
    if InsIDs and ksbcnext(InsIDs) then
        local spawnerID, _ = ksbcnext(InsIDs)
        Game.AutoNavigationSystem:RequestNavigateToNPC(Enum.EAutoNavigationRequesterType.Task, spawnerID, npcID)
    end
end

function TarotTeamSystem:CheckIsNormalTeamLeader()
    if not (Game.TeamSystem:IsInTeam() or Game.TeamSystem:IsInGroup()) 
        or Game.TeamSystem:IsInTeam() and not Game.TeamSystem:IsCaptain()
        or Game.TeamSystem:IsInGroup() and not Game.GroupSystem:IsTeamLeader() then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_CREATE_TEAMLEADER)
            return false
    end
    return true
end

-- 创建完团队之后，向群聊发送邀请信息
function TarotTeamSystem:OnGroupIDChange()
    if self:hasOwnTeam() and self:IsTeamLeader(self.model.ownTarotTeamInfo.id) then
        local Text = string.format(Game.TableData.GetTarotTeamStringConstDataRow("TAROTTEAM_FOUND_INVITE_JOIN_TEAM").StringValue, Game.me.groupID)
        Game.AllInSdkManager:IsSensitiveWords(Text, function(bSensitive)
            if bSensitive then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED)
            else
                Game.ChatClubSystem:reqSendChat(self.model.ownTarotTeamInfo.clubID, Text, Enum.EChatMessageType.TEXT,nil, nil, false)
            end
        end)
    end
    Game.GlobalEventSystem:RemoveListener(EEventTypesV2.ON_MAINPLAYER_GROUP_ID_CHANGED, "OnGroupIDChange", self)
end

function TarotTeamSystem:StartBuildTarotTeam()
    if Game.me.tarotTeamStatus == tarot_team_status.FINAL then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_ALREADY_CREATED_NPC_TIPS)
        return
    end
    if not self:CheckIsNormalTeamLeader() then
        return 
    end
    
    local count = math.max(table.count(Game.GroupSystem:GetMyGroupMemberIDList()), table.count(Game.me.teamInfoList))
    if count < Game.TableData.GetTarotTeamSettingDataRow("MinTarotTeamBuildTeamNearbyCount") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_BUILD_FAILED_FOR_PLAYERS_NOT_ENOUGH,{{Game.TableData.GetTarotTeamSettingDataRow("MinTarotTeamBuildTeamNearbyCount"), Game.TableData.GetTarotTeamSettingDataRow("MaxTarotTeamMemberNumber")}})
        return
    end
    self.sender:ReqStartTarotTeamBuildFlow()
end

---@Desc 检查通过后返回成员列表，队长准备调整成员位次(进入次序调整界面)
---@param roleBriefInfoList ROLE_BRIEF_LIST
---@param isTeamLeader boolean
function TarotTeamSystem:OnMsgStartTarotTeamCeremony(roleBriefInfoList, clubID, isTeamLeader)
    -- self:OnMsgTarotTeamStartBuild()
    -- 填充model数据
    self.model.ownTarotTeamInfo = self.model.ownTarotTeamInfo or {}
    self.model.ownTarotTeamInfo.status = Game.me.tarotTeamStatus
    self.model.ownTarotTeamInfo.clubID = clubID
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeamListInfo_Panel) then
        Game.NewUIManager:ClosePanel(UIPanelConfig.TarotTeamListInfo_Panel)
    end
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeam_Panel) then
        Game.NewUIManager:ClosePanel(UIPanelConfig.TarotTeam_Panel)
    end
    Game.NewUIManager:OpenPanel(UIPanelConfig.TarotTeamOrder_Panel, roleBriefInfoList, isTeamLeader, false)
    Game.NewUIManager:OpenPanel(UIPanelConfig.ChatWindowLayer_Panel, clubID, Enum.EChatTarget.Club, Game.ChatSystem:GetChatSetting(Enum.EChatSetting["DefaultSmallChatWins"]), true, {x = 130, y = 600})
end

function TarotTeamSystem:OnMsgMemberConfirmTarotTeamMemberList(teamName, Wage, order, signedMemberCnt, totalMemberCnt, clubId)
    self.model.ownTarotTeamInfo = self.model.ownTarotTeamInfo or {}
    self.model.ownTarotTeamInfo.status = Game.me.tarotTeamStatus
    self.model.ownTarotTeamInfo.clubID = clubId
    Game.NewUIManager:ClosePanel(UIPanelConfig.TarotTeamOrder_Panel)
    Game.NewUIManager:OpenPanel(UIPanelConfig.TarotTeamContract_Panel, teamName, Wage, order, signedMemberCnt, totalMemberCnt, clubId)
end

-- ---@Desc 队长更新顺序(新次序)
-- ---@param entityIDs ENTITY_ID_LIST
function TarotTeamSystem:OnMsgTarotTeamModifyMemberOrder(entityIDs)
    self.model.ownTarotTeamInfo = self.model.ownTarotTeamInfo or {}
    self.model.ownTarotTeamInfo.memberList = entityIDs
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SYNC_TAROT_TEAM_ORDER, entityIDs)
end

---@Desc 建队流程完成
---@param tarotTeamID TAROT_TEAM_ID
function TarotTeamSystem:OnMsgTarotTeamFinishBuild(tarotTeamID)
    self.model.ownTarotTeamInfo = self.model.ownTarotTeamInfo or {}
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_BUILD_SUCC)
    self.model.ownTarotTeamInfo.status = Game.me.tarotTeamStatus
    self.model.ownTarotTeamInfo.id = tarotTeamID
end

---@Desc 隐藏招募回调
---@param isHidden boolean
function TarotTeamSystem:OnMsgTarotTeamSetHidden(isHidden)
    self.model.isHidden = isHidden
end

---@Desc 小队面板回调
---@param tarotTeamDetailInfo TAROT_TEAM_DETAIL_INFO
---@param tarotTeamRoleBriefInfoMap TAROT_TEAM_ROLE_BRIEF_INFO_MAP
function TarotTeamSystem:OnMsgTarotTeamGetDetailInfo(tarotTeamDetailInfo, tarotTeamRoleBriefInfoMap)
    self.model.ownTarotTeamInfo = tarotTeamDetailInfo
    self.model.tarotTeamRoleBriefInfoMap = tarotTeamRoleBriefInfoMap
    self:OpenTarotTeamUI()
end

-- 转让队长成功回调
function TarotTeamSystem:OnMsgTarotTeamChangeLeader(entityID)
    local targetIndex = nil
    for index, memberInfo in pairs(self.model.ownTarotTeamInfo.memberList) do
        if memberInfo == entityID then
            targetIndex = index
            self.model.ownTarotTeamInfo.memberList[targetIndex] = Game.me.eid
            self.model.ownTarotTeamInfo.memberList[1] = entityID
            break
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TAROT_TEAM_MEMBER_CHANGE)
end

--#endregion
----------------------------------------------------------------------------------------------------


---当前是否存在自己创建的队伍
function TarotTeamSystem:hasOwnTeam()
    return Game.me.tarotTeamID ~= tarot_team_const.TAROT_TEAM_DEFAULT_ID
end

---@param teamID TAROT_TEAM_ID
function TarotTeamSystem:IsInTeam(teamID)
    if self.model.ownTarotTeamInfo and self.model.ownTarotTeamInfo.id == teamID then
        return true
    end
    return false
end

---@param teamID TAROT_TEAM_ID
function TarotTeamSystem:IsTeamLeader(teamID)
    if not self:IsInTeam(teamID) then
        return false
    end
    local memberInfo = self.model.ownTarotTeamInfo
    if memberInfo and #memberInfo.memberList > 0 then
        if memberInfo.memberList[1] == Game.me.eid then
            return true
        end
    end

    return false
end

--- 发起申请: 申请加入正式队伍 or 响应队伍
---@param tarotTeamID TAROT_TEAM_ID
function TarotTeamSystem:ApplyJoin(tarotTeamID)
    self.sender:ReqApplyTarotTeam(tarotTeamID, false)
end

function TarotTeamSystem:IsTarotTeamLeader(targetId)
    -- 只有在小队面板才能操作
    if not Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeamDisplay_Panel) then
        return false
    end
    return self.model.ownTarotTeamInfo.memberList[1] == Game.me.eid
end

-- 转让队长
function TarotTeamSystem:TransferTarotTeamLeader(targetID)
    self.sender:ReqTarotTeamChangeLeader(targetID)
end

function TarotTeamSystem:CheckIsBuildingStateChatWindow(targetID)
    return self.model.ownTarotTeamInfo and Game.me.tarotTeamStatus == tarot_team_status.BUILDING and targetID == self.model.ownTarotTeamInfo.clubID
end

function TarotTeamSystem:HasSensitiveWords(sensitiveResult)
    for index, data in ipairs(sensitiveResult) do
        if #data.matchResults > 0 then
            return true, index
        end
    end
    return false
end

return TarotTeamSystem
