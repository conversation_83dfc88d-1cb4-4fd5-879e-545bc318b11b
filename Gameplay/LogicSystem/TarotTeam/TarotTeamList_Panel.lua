local UITempComTabFoldParentNew = kg_require("Framework.KGFramework.KGUI.Component.Tab.UITempComTabFoldParentNew")
local ComFrame = kg_require("Framework.KGFramework.KGUI.Component.Panel.UITempComFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local TarotTeamBtn_Item = kg_require("Gameplay.LogicSystem.TarotTeam.TarotTeamBtn_Item")
local TarotTeamTop_Sub = kg_require("Gameplay.LogicSystem.TarotTeam.TarotTeamTop_Sub")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class TarotTeamList_Panel : UIPanel
---@field view TarotTeamList_PanelBlueprint
local TarotTeamList_Panel = DefineClass("TarotTeamList_Panel", UIPanel)

local tarot_team_const = kg_require("Shared.Const.TarotTeamConsts")
local searchTypeConfig = tarot_team_const.TAROT_TEAM_SEARCH_LIST_TYPE
local teamStatus = tarot_team_const.TAROT_TEAM_STATUS
local ESlateVisibility = import("ESlateVisibility")
local stringFunc = Game.TableData.GetTarotTeamStringConstDataRow
local table_insert = table.insert
local P_SearchInput = kg_require("Gameplay.LogicSystem.CommonUI.CommonInput.P_SearchInput")
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")

TarotTeamList_Panel.eventBindMap = {
    [EEventTypesV2.ON_TAROT_TEAM_DETAIL_INFO_SUCC] = "OnTarotTeamInfoDetailCb",
    [EEventTypesV2.ON_JOIN_TAROT_TEAM_SUCC] = "OnJoinTeamSucc",
    [EEventTypesV2.ON_QUIT_TAROT_TEAM_SUCC] = "onQuitTarotTeamMsg",
    [EEventTypesV2.ON_TAROT_TEAM_DISHBAND] = "OnDishBindTeam",
    [EEventTypesV2.ON_SEARCH_TEAM_SUCC] = "RefreshTeamListCache",
    [EEventTypesV2.ON_TAROT_TEAM_LIST_TOP_CHANGE] = "UpdateApplyInfo",
    [EEventTypesV2.ON_MSG_TAROT_TEAM_APPLY_SUCC] = "OnApplySucc",
    [EEventTypesV2.ON_ACCEPT_APPLY_RET] = "OnLeaderAcceptApplySucc",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamList_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamList_Panel:InitUIData()
    self.manageApplyBtn = { 
        name = stringFunc("TAROTTEAM_APPLICATION_MANAGEMENT").StringValue, 
        onClickedFunc = self.onClickManageApplyMsg,
        icon = UIAssetPath.UI_TarotTeam_Icon_Group01_Sprite
    }
    
    self.createTeamBtn = { 
        name = stringFunc("TAROTTEAM_CREATE_TEAM").StringValue,
        onClickedFunc = self.onClickCreateTeam,
        icon = UIAssetPath.UI_TarotTeam_Icon_Group03_Sprite
    }
    -- 选中的小队状态
    self.menuType = nil 
    self.searchType = searchTypeConfig.NORMAL_DEFAULT

    ---选中队伍的详细信息
    self.itemSelected = 1
    -- 选中的小队标签
    self.selectedTeamTag = 0
    
    self.itemSelectedDetail = nil
    --当前展示的列表
    self.tarotTeamSearchList = {} 
    -- 是否是搜索结果
    self.bIsSearchResult = false 

    ---@type table<TAROT_TEAM_RANK_TYPE, TAROT_TEAM_SEARCH_INFO[]> 不同榜单的前500数据缓存
    self._tarotTeamRankCache = {}
    ---@type table<number, number>
    -- self._tarotTeamRankCacheRefreshTime = {} --- 这里记录一下数据的刷新时间 客户端刷新频率控制(目前是协议控制的)
    ---@type table<TAROT_TEAM_STATUS, TAROT_TEAM_SEARCH_INFO[]> -- 自己申请的处于非正式状态的队伍信息
    self.ownAppliedTeamInfos = {}
    self.ownTeamSearchInfo = {}
end

--- UI组件初始化，此处为自动生成
function TarotTeamList_Panel:InitUIComponent()
    ---@type UITempComTabFoldParentNew
    self.WBP_ComTabFoldParentNewCom = self:CreateComponent(self.view.WBP_ComTabFoldParentNew, UITempComTabFoldParentNew)
    ---@type UITempComTabFoldParentNew
    self.WBP_ComTabFoldParentNew_1Com = self:CreateComponent(self.view.WBP_ComTabFoldParentNew_1, UITempComTabFoldParentNew)
    ---@type ComFrame
    self.WBP_ComFrameCom = self:CreateComponent(self.view.WBP_ComFrame, ComFrame)
    ---@type UIListView
    self.KGListViewCom = self:CreateComponent(self.view.KGListView, UIListView)
    ---@type TarotTeamBtn_Item
    self.WBP_TarotTeamBtn_ItemCom = self:CreateComponent(self.view.WBP_TarotTeamBtn_Item, TarotTeamBtn_Item)
    ---@type TarotTeamTop_Sub
    self.WBP_TarotTeamTop_SubCom = self:CreateComponent(self.view.WBP_TarotTeamTop_Sub, TarotTeamTop_Sub)

    self.P_SearchInputAdapter =self:CreateComponent(self.view.WBP_ComInput, UIBaseAdapter, "TarotTeamList_Panel", P_SearchInput)
    self.P_SearchInputAdapter:GetUIBase():SetData(
        {
            Owner = self,
            -- HintText = StringConst.Get("GUILD_LEAGUE_MODIFY_TAG_TEXT_TIPS"),
            DefaultInputText = "",
            -- OnValueChangedCallback = self.OnTextChanged,
            InputLimit = Game.TableData.GetTarotTeamSettingDataRow("TarotTeamNameMaxCount"),
            OnClickSearch = self.OnClickSearch
        }
    )
end

---UI事件在这里注册，此处为自动生成
function TarotTeamList_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_TarotTeamTop_SubCom.onSortBtnClicked, "on_WBP_TarotTeamTop_SubCom_SortBtnClicked")
    self:AddUIEvent(self.view.WBP_ComTabFoldParentNew.Btn_ClickArea.OnClicked, "onClickChangeMenu", teamStatus.RECRUITING)
    self:AddUIEvent(self.view.WBP_ComTabFoldParentNew_1.Btn_ClickArea.OnClicked, "onClickFinalMenu")
    self:AddUIEvent(self.view.WBP_ComBtnIconNew.Big_Button_ClickArea_lua.OnClicked, "on_WBP_ComBtnIconNewBig_Button_ClickArea_lua_Clicked")
    self:AddUIEvent(self.view.WBP_TarotTeamBtn_Item.Btn_ClickArea.OnClicked, "on_WBP_TarotTeamBtn_ItemCom_BtnClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamList_Panel:InitUIView()
    self.view.WBP_ComTabFoldParentNew_1.Text_Name_lua:SetText(stringFunc("TAROTTEAM_JOIN_TEAM").StringValue)
    self.view.WBP_ComTabFoldParentNew.Text_Name_lua:SetText(stringFunc("TAROTTEAM_RESPOND_TEAM").StringValue)
end

---组件刷新统一入口
function TarotTeamList_Panel:Refresh()
    self.ownTeamSearchInfo = Game.TarotTeamSystem.model.ownTeamSearchInfo
    self.ownAppliedTeamInfos = Game.TarotTeamSystem.model.appliedTeamInfoList
    self.P_SearchInputAdapter:GetUIBase():OnClickClearInputText()
    self.WBP_ComFrameCom:Refresh(stringFunc("TAROTTEAM_TAROT_TEAM").StringValue, Enum.ETipsData.TAROT_TEAM_HELP_TIPS)
    self:onClickChangeMenu(teamStatus.FINAL)
end

function TarotTeamList_Panel:UpdatePanel()
    self.ownAppliedTeamInfos = Game.TarotTeamSystem.model.appliedTeamInfoList
    self:RefreshCreateBtn()
    self:RefreshTopSortMenu()    
    self:RefreshTeamList()
end

function TarotTeamList_Panel:onClickCreateTeam()
    if Game.TarotTeamSystem:hasOwnTeam() then
        return
    end
    if #self.ownAppliedTeamInfos > 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TAROTTEAM_TEAM_BUILD_FAILED_FOR_APPLYING)        
        return
    end
    Game.NewUIManager:OpenPanel("TarotTeamCreate_Panel")
end

function TarotTeamList_Panel:onClickManageApplyMsg()
    if not Game.TarotTeamSystem:hasOwnTeam() then
        return
    end
    Game.TarotTeamSystem:OpenTarotTeamApplyUI()
end

--- 此处为自动生成
---@param searchSortType boolean
function TarotTeamList_Panel:on_WBP_TarotTeamTop_SubCom_SortBtnClicked(searchSortType)
    if not searchSortType and self.searchType == searchSortType then
        return
    end
    if self.bIsSearchResult then
        return
    else
        self.itemSelected = 1
        self.searchType = searchSortType 
        local cacheData = self._tarotTeamRankCache[searchSortType]
        if cacheData and #cacheData > 0 then
            self:RefreshTeamList()
            return
        end
        Game.TarotTeamSystem.sender:ReqQueryTarotTeamSearchList(self.searchType)
    end
end

---刷新顶部菜单
function TarotTeamList_Panel:RefreshTopSortMenu()
    local baseMenuStyle = searchTypeConfig.NORMAL_DEFAULT
    if self.menuType == teamStatus.RECRUITING then
        baseMenuStyle = searchTypeConfig.RECRUIT_DEFAULT    
    end
    self.WBP_TarotTeamTop_SubCom:SetData(baseMenuStyle)
end

---刷新创建按钮状态
function TarotTeamList_Panel:RefreshCreateBtn()
    ---@type TarotTeamBtn_Item_Params
    local btnData = Game.TarotTeamSystem:hasOwnTeam() and self.manageApplyBtn or self.createTeamBtn
    self.WBP_TarotTeamBtn_ItemCom:OnRefresh(btnData)
end

--- 刷新列表状态
function TarotTeamList_Panel:RefreshTeamList()
    local oldSelectTeam = self.tarotTeamSearchList[self.itemSelected]    
    local teamListViewData = self:getTarotTeamList()
    self.tarotTeamSearchList = teamListViewData or {}
    if #self.tarotTeamSearchList <= 0 then
        self.KGListViewCom:Clear() --- 清空列表
        self.itemSelected = 1
        self.view.WBP_SearchResultEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.WBP_SearchResultEmpty:SetVisibility(ESlateVisibility.Collapsed)
        local newSelectedIndex = self.itemSelected or 1
        if oldSelectTeam then
            for index, teamInfo in ipairs(self.tarotTeamSearchList) do 
                if teamInfo.id == oldSelectTeam.id then 
                    newSelectedIndex = index
                    break
                end
            end  
        end
        self.itemSelected = newSelectedIndex
        self.KGListViewCom:Refresh(self.tarotTeamSearchList)
        self.KGListViewCom:ScrollToItemByIndex(self.itemSelected)
        self.KGListViewCom:SetSelectedItemByIndex(self.itemSelected, true)
    end
    if #self.tarotTeamSearchList <= 0 and (self.searchType ~= searchTypeConfig.SEARCH_RESULT) and self.selectedTeamTag == 0 then
        self.view.TeamInfo_CP:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.TeamInfo_CP:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.Collapsed)
    end
    self:RefreshTeamDetailInfo()
end

--- 更新显示的列表信息
function TarotTeamList_Panel:getTarotTeamList()
    ---@type TAROT_TEAM_SEARCH_INFO[]
    local cacheData = self._tarotTeamRankCache[self.searchType]
    if cacheData then
        ---自己的队伍信息
        local teamListViewData = {}
        local excludeTeamId = {}

        if Game.TarotTeamSystem:hasOwnTeam() and self.ownTeamSearchInfo 
            and self.ownTeamSearchInfo ~= tarot_team_const.TAROT_TEAM_DEFAULT_ID 
            and self.ownTeamSearchInfo.status == self.menuType then
            local ownTeamInfo = DeepCopy(self.ownTeamSearchInfo)
            excludeTeamId[#excludeTeamId + 1] = ownTeamInfo.id
            ownTeamInfo.isApplied = false
            ownTeamInfo.IsInTeam = true
            if self:CheckHasTagetTag(ownTeamInfo) then
                table_insert(teamListViewData, #teamListViewData + 1, ownTeamInfo)
            end
        end

        for _, teamInfo in ipairs(self.ownAppliedTeamInfos) do
            if teamInfo.status == self.menuType then
                local ownTeamInfo = DeepCopy(teamInfo)
                excludeTeamId[#excludeTeamId + 1] = teamInfo.id
                ownTeamInfo.isApplied = true
                ownTeamInfo.IsInTeam = false
                if self:CheckHasTagetTag(ownTeamInfo) then
                    table_insert(teamListViewData, #teamListViewData + 1, ownTeamInfo)
                end
            end
        end 

        for _, team in ipairs(cacheData) do
            local bIsInclude = false
            for _, teamID in ipairs(excludeTeamId) do
                if team.id == teamID then 
                    bIsInclude = true
                end
            end
            if not bIsInclude then
                team.isApplied = false
                team.IsInTeam = false
                if self:CheckHasTagetTag(team) then
                    table_insert(teamListViewData, #teamListViewData + 1,team)
                end
            end
        end
        return teamListViewData
    end
    return nil
end

-- 标签筛选
function TarotTeamList_Panel:CheckHasTagetTag(team)
    for _, tag in ipairs(team.tagList) do
        if self.selectedTeamTag == 0 or tag == self.selectedTeamTag then
            return true
        end
    end
    return false
end

--刷新小队详情面板
function TarotTeamList_Panel:RefreshTeamDetailInfo()
    ---刷新Detail面板
    local selectedTeam = self.tarotTeamSearchList[self.itemSelected]
    if selectedTeam then
        Game.TarotTeamSystem.sender:ReqQueryTarotTeamSearchDetailInfo(selectedTeam.id)
    end
end

---队长通过申请
---@param tarotTeamInfo TAROT_TEAM_SEARCH_INFO
---@param isCancel boolean
function TarotTeamList_Panel:OnMsgTarotTeamApplySucc(tarotTeamInfo, isCancel)
    self._tarotTeamRankCache[self.menuType] = self._tarotTeamRankCache[self.menuType] or {}
    if not isCancel then
        table_insert(self._tarotTeamRankCache[self.menuType], tarotTeamInfo)
    else
        for index, teamInfo in ipairs(self._tarotTeamRankCache[self.menuType]) do
            if teamInfo.id == tarotTeamInfo.id then
                table.remove(self._tarotTeamRankCache[self.menuType], index)
                break
            end
        end
    end
    self:RefreshTeamList()
end

---处理申请回调
---@param entityIDs ENTITY_ID_LIST
---@param isAccept boolean
function TarotTeamList_Panel:OnLeaderAcceptApplySucc(entityIDs, isAccept)
    if isAccept and self.tarotTeamSearchList[self.itemSelected] and self.tarotTeamSearchList[self.itemSelected].id == self.ownTeamSearchInfo.id then 
        Game.TarotTeamSystem.sender:ReqQueryTarotTeamGetListTop()
    end
end

--- show Detail Panel
---@param teamDetailInfo TAROT_TEAM_SEARCH_DETAIL_INFO
function TarotTeamList_Panel:OnTarotTeamInfoDetailCb(teamDetailInfo)
    for index, v in pairs(self.tarotTeamSearchList) do
        if v.id == teamDetailInfo.id then
            self.itemSelected = index
            -- 补充字段
            teamDetailInfo.name = v.name
            self.itemSelectedDetail = teamDetailInfo
            teamDetailInfo.hasApplied = self:HasAppliedTeam(teamDetailInfo.id)
            Game.NewUIManager:OpenPanel("TarotTeamInfoPanel", teamDetailInfo)
            break
        end
    end
end

---@public 更新榜单基础列表
---@param searchType TAROT_TEAM_RANK_TYPE
---@param tarotTeamRankList TAROT_TEAM_SEARCH_INFO_LIST
function TarotTeamList_Panel:RefreshTeamListCache(searchType, tarotTeamRankList)
    if type(searchType) == "table" then
        self.bIsSearchResult = true
        searchType = searchTypeConfig.SEARCH_RESULT
    else
        self.bIsSearchResult = false
    end
    self._tarotTeamRankCache[searchType] = tarotTeamRankList
    --记录更新时间
    -- self._tarotTeamRankCacheRefreshTime[searchType] = _G._now() / 1000
    self.searchType = searchType
    self:RefreshTeamList()
    if #self.tarotTeamSearchList >= self.itemSelected then
        self.KGListViewCom:SetSelectedItemByIndex(self.itemSelected, true)
    end
end

---更新自己的申请信息
---@param ownTeamSearchInfo TAROT_TEAM_SEARCH_INFO
---@param appliedTeamInfoList TAROT_TEAM_SEARCH_INFO_LIST
function TarotTeamList_Panel:UpdateApplyInfo(ownTeamSearchInfo, appliedTeamInfoList)
    -- 如果数据相同 则不需要刷新列表
    local bIsSimple = table.equal(self.ownAppliedTeamInfos, appliedTeamInfoList) and 
        (self.ownTeamSearchInfo == nil and ownTeamSearchInfo.id == tarot_team_const.TAROT_TEAM_DEFAULT_ID or table.equal(self.ownTeamSearchInfo, ownTeamSearchInfo))
    if not bIsSimple then
        self.ownTeamSearchInfo = ownTeamSearchInfo
        self.ownAppliedTeamInfos = appliedTeamInfoList
        self:RefreshCreateBtn()
        self:RefreshTeamList()
    end
end

---退出退伍
---@param tarotTeamID TAROT_TEAM_ID
function TarotTeamList_Panel:OnDishBindTeam(tarotTeamID)
    local cacheData = self._tarotTeamRankCache[self.searchType]
        if cacheData then
            for index, v in pairs(cacheData) do
                if v.id == tarotTeamID then
                    table.remove(cacheData, index)
                    break
                end
            end
            for index, v in pairs(self.ownAppliedTeamInfos) do
                if v.id == tarotTeamID then
                    table.remove(self.ownAppliedTeamInfos, index)
                    break
                end
            end
        end

    self.ownTeamSearchInfo = nil
    self:RefreshCreateBtn()
    self:RefreshTeamList()
end

---申请回调
---@param tarotTeamInfo TAROT_TEAM_SEARCH_INFO
---@param isCancle boolean
function TarotTeamList_Panel:OnApplySucc(tarotTeamInfo, isCancle)
    if tarotTeamInfo.id == tarot_team_const.TAROT_TEAM_DEFAULT_ID then
        return
    end
    self.ownAppliedTeamInfos = Game.TarotTeamSystem.model.appliedTeamInfoList
    self:RefreshTeamList()
end

--- 这里返回的信息不够 需要刷新TopList
function TarotTeamList_Panel:OnJoinTeamSucc()
    Game.TarotTeamSystem.sender:ReqQueryTarotTeamGetListTop()
end


--- 此处为自动生成
function TarotTeamList_Panel:on_WBP_TarotTeamBtn_ItemCom_BtnClicked()
    local btnData = Game.TarotTeamSystem:hasOwnTeam() and self.manageApplyBtn or self.createTeamBtn
    if btnData then
        btnData.onClickedFunc(self)
    end
end


-- 小队申请状态判断
function TarotTeamList_Panel:HasAppliedTeam(teamID)
    if self.ownAppliedTeamInfos then
        return table.find(self.ownAppliedTeamInfos, function(item) return item.id == teamID end)
    end
    return false
end

--- 点击刷新按钮
function TarotTeamList_Panel:on_WBP_ComBtnIconNewBig_Button_ClickArea_lua_Clicked()
    self.P_SearchInputAdapter:GetUIBase():OnClickClearInputText()
    self.itemSelected = 1
    table.clear(self._tarotTeamRankCache)
    self.searchType = self.menuType == teamStatus.RECRUITING and searchTypeConfig.RECRUIT_DEFAULT or searchTypeConfig.NORMAL_DEFAULT
    self.WBP_TarotTeamTop_SubCom:SetSortBtnState()
    self:on_WBP_TarotTeamTop_SubCom_SortBtnClicked(self.searchType)
    self:RefreshTopSortMenu()
end

-- 取消响应回调
function TarotTeamList_Panel:onQuitTarotTeamMsg()
    self:UpdatePanel()
    self:RefreshTeamDetailInfo()
end

function TarotTeamList_Panel:onClickFinalMenu()
    self:onClickChangeMenu(teamStatus.FINAL)
end

function TarotTeamList_Panel:onClickChangeMenu(teamStatusSelected)
    if self.menuType == teamStatusSelected then
        return
    end
    --切换menu
    self.menuType = teamStatusSelected
    self.itemSelected = 1
    if self.menuType == teamStatus.RECRUITING then
        self.searchType = searchTypeConfig.RECRUIT_DEFAULT
        self.view.WBP_ComTabFoldParentNew_1:Event_UI_Style(false, false, false, false)
        self.view.WBP_ComTabFoldParentNew:Event_UI_Style(true, false, false, false)
    elseif self.menuType == teamStatus.FINAL then
        self.searchType = searchTypeConfig.NORMAL_DEFAULT
        self.view.WBP_ComTabFoldParentNew_1:Event_UI_Style(true, false, false, false)
        self.view.WBP_ComTabFoldParentNew:Event_UI_Style(false, false, false, false)
    end
    if Game.NewUIManager:CheckPanelIsOpen("TarotTeamInfoPanel") then
        Game.NewUIManager:ClosePanel("TarotTeamInfoPanel")
    end
    if not self._tarotTeamRankCache[self.searchType] then
        Game.TarotTeamSystem.sender:ReqQueryTarotTeamSearchList(self.searchType)
    end
    self.WBP_TarotTeamTop_SubCom:SetSortBtnState()
    self:UpdatePanel()
end

-- 小队搜索
function TarotTeamList_Panel:OnClickSearch()
    local searchText = self.view.WBP_ComInput.EditText_lua:GetText()
    if searchText == "" then
        Game.TarotTeamSystem.sender:ReqQueryTarotTeamSearchList(self.menuType == teamStatus.RECRUITING and searchTypeConfig.RECRUIT_DEFAULT or searchTypeConfig.NORMAL_DEFAULT)
        return
    end
    local searchArgs
    if tonumber(searchText) then
        searchArgs = {id = tonumber(searchText), name = searchText, isRecruiting = self.menuType == teamStatus.RECRUITING}
    else
        searchArgs = {name = searchText, isRecruiting = self.menuType == teamStatus.RECRUITING}
    end
    Game.TarotTeamSystem.sender:ReqQueryTarotTeamSearchTeams(searchArgs)
end

function TarotTeamList_Panel:OnClickDownSelect(selectedTeamTag)
    self.selectedTeamTag = selectedTeamTag - 1
    self.itemSelected = 1
    self:RefreshTeamList()
end

function TarotTeamList_Panel:OnClose()
    Game.NewUIManager:ClosePanel("TarotTeamInfoPanel")
end

return TarotTeamList_Panel
