---@class TarotTeamModel
local TarotTeamModel = DefineClass("TarotTeamModel", SystemModelBase) ---luacheck: ignore

function TarotTeamModel:init()
    ---@type TAROT_TEAM_DETAIL_INFO 自己当前塔罗小队的数据信息
    self.ownTarotTeamInfo = nil
    self.tarotTeamRoleBriefInfoMap = nil

    ---@alias TAROT_TEAM_STATUS number
    ---@type TAROT_TEAM_SEARCH_INFO -- 队伍简短信息
    self.ownTeamSearchInfo = nil

    --- 新的申请标志
    self.hasNewApplyMsg = nil
    ---@type ROLE_BRIEF_LIST 自己队伍的申请信息
    self.teamApplyList = nil
    self.appliedTeamInfoList = {} -- 申请列表

    self.showTeamInfoList = {} -- 客户端模拟的小队成员列表
    self.reconnectState = nil -- 重连状态
    self.reconnectParams = {} -- 重连参数

    -- 临时填充模型测试开关
    self.testSwitch = Game.GameClientData:GetGlobalValue("TarotTeamFillModel") or false
    self.classIdMap = Game.GameClientData:GetGlobalValue("TarotTeamModelClassIdMap") or {}
    self:InitShowTeamInfo()
end

---移除已经处理的请求
------@param entityIDs ENTITY_ID_LIST
function TarotTeamModel:RemoveApplyMsg(entityIDs)
    for _, entityID in ipairs(entityIDs) do
        for index, roleBriefInfo in ipairs(self.teamApplyList) do
            if roleBriefInfo.entityID == entityID then
                table.remove(self.teamApplyList, index)
                break
            end
        end
    end
end

function TarotTeamModel:ClearOwnTeamInfo()
    self.ownTarotTeamInfo = nil -- 清除队伍信息
    self.ownTeamSearchInfo = nil
end

function TarotTeamModel:InitShowTeamInfo()
    for testIndex = 1, 12 do
        self.showTeamInfoList[testIndex] = self.showTeamInfoList[testIndex] or {}
        self.showTeamInfoList[testIndex].school = math.floor((testIndex + 1) / 2) + 1200000
        self.showTeamInfoList[testIndex].sex = testIndex % 2 == 0 and 0 or 1
        self.showTeamInfoList[testIndex].classId = self.classIdMap[testIndex]
    end
end

return TarotTeamModel