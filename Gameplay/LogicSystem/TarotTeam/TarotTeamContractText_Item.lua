local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TarotTeamContractText_Item : UIListItem
---@field view TarotTeamContractText_ItemBlueprint
local TarotTeamContractText_Item = DefineClass("TarotTeamContractText_Item", UIListItem)

TarotTeamContractText_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamContractText_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamContractText_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TarotTeamContractText_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TarotTeamContractText_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamContractText_Item:InitUIView()
end

---面板打开的时候触发
function TarotTeamContractText_Item:OnRefresh(data)
	self.view.Text_Title:SetText(data.title)
    self.view.RichText_Info:SetText(data.content)
end

return TarotTeamContractText_Item
