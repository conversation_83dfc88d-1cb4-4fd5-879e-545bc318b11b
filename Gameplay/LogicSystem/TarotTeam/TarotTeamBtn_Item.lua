local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TarotTeamBtn_Item : UIListItem
---@field view TarotTeamBtn_ItemBlueprint
local TarotTeamBtn_Item = DefineClass("TarotTeamBtn_Item", UIListItem)

TarotTeamBtn_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamBtn_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---@class TarotTeamBtn_Item_Params
---@field name string
---@field icon string
---@field onClickedFunc function
---@field params any

---初始化数据
function TarotTeamBtn_Item:InitUIData()
    self.params = nil
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.OnClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function TarotTeamBtn_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TarotTeamBtn_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamBtn_Item:InitUIView()
end

---面板打开的时候触发
function TarotTeamBtn_Item:OnRefresh(params)
    self.params = params
    self.view.Text_Name:SetText(params.name)
    self:SetImage(self.view.Img_Icon, params.icon)
    local myParent = self:GetParent():GetParent() ~= nil and self:GetParent():GetParent():GetParent() or self:GetParent()
    if params.name == Game.TableData.GetTarotTeamStringConstDataRow("TAROTTEAM_APPLICATION_MANAGEMENT_BUTTON").StringValue then
        Game.RedPointSystem:RegisterRedPoint(myParent, self.userWidget, "TarotTeamApply")
    end
    -- self:SetImage(self.view.Img_Bg, btnInfo.bg)
end

--- 此处为自动生成
function TarotTeamBtn_Item:on_Btn_ClickArea_Clicked()
	self.OnClickEvent:Execute()
end

return TarotTeamBtn_Item
