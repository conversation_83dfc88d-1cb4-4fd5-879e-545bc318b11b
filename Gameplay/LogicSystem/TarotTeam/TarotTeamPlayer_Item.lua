local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TarotTeamPlayer_Item : UIListItem
---@field view TarotTeamPlayer_ItemBlueprint
local TarotTeamPlayer_Item = DefineClass("TarotTeamPlayer_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

TarotTeamPlayer_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamPlayer_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamPlayer_Item:InitUIData()
    self.data = nil
    self.rank = nil
end

--- UI组件初始化，此处为自动生成
function TarotTeamPlayer_Item:InitUIComponent()
    ---@type SocialHead
    self.WBP_SocialHeadCom = self:CreateComponent(self.view.WBP_SocialHead, SocialHead)
end

---UI事件在这里注册，此处为自动生成
function TarotTeamPlayer_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamPlayer_Item:InitUIView()
end

function TarotTeamPlayer_Item:OnRefresh(data)
    self.data = data
    self.WBP_SocialHeadCom:SetData({ProfessionID = data.school, Level = data.lv})
    if data.rank then
        self.view.Text_Num:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Num:SetText(data.rank)
        self.view.Text_NameTitle:SetText(Game.TableData.GetTarotTeamJobTitleDataRow(data.rank).JobTitle)
        self.view.Text_NameTitle:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.Text_Num:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Text_NameTitle:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.view.Text_Name:SetText(data.rolename)
end

function TarotTeamPlayer_Item:OnSelectedChanged(selected)
    -- if selected then
    --     self:PlayAnimation(self.view.WBP_SocialHead.Ani_Select, nil, self.view.WBP_SocialHead)
    -- else
    --     self:PlayAnimation(self.view.WBP_SocialHead.Ani_UnSelect, nil, self.view.WBP_SocialHead)
    -- end
end


--- 此处为自动生成
function TarotTeamPlayer_Item:on_Btn_ClickArea_Clicked()
end

return TarotTeamPlayer_Item
