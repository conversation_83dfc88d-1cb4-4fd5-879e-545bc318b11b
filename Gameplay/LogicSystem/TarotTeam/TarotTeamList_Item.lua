local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TarotTeamList_Item : UIListItem
---@field view TarotTeamList_ItemBlueprint
local TarotTeamList_Item = DefineClass("TarotTeamList_Item", UIListItem)
local tarot_team_const = kg_require("Shared.Const.TarotTeamConsts")
local tarot_team_status = tarot_team_const.TAROT_TEAM_STATUS
local ESlateVisibility = import("ESlateVisibility")

TarotTeamList_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamList_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamList_Item:InitUIData()
    self.isSelf = false
    ---@type TAROT_TEAM_SEARCH_INFO
    self.data = {}
    
    --- 不确定数值是完全顺序的
    self.tagsView = {
        [1] = self.view.Img_Tag_3,
        [2] = self.view.Img_Tag_4,
        [3] = self.view.Img_Tag_5
    }
    self.tagIcon = {
        [1] = UIAssetPath.UI_Com_Img_Battle02_Sprite_Copy,
        [2] = UIAssetPath.UI_Com_Img_BuddhistSystem02_Sprite_Copy,
        [3] = UIAssetPath.UI_Com_Img_Copy02_Sprite
    }

end

--- UI组件初始化，此处为自动生成
function TarotTeamList_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TarotTeamList_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamList_Item:InitUIView()
    
end

---面板打开的时候触发
function TarotTeamList_Item:OnRefresh(data)
    self.data = data
    self.data.scores = self.data.scores or self.data.scoreInfo
    self.isSelf = Game.TarotTeamSystem.model.ownTarotTeamInfo and Game.TarotTeamSystem.model.ownTarotTeamInfo.id == self.data.id or false

    self.view.TarotTeamName:SetText(self.data.name)
    self.view.Member_Count:SetText(self.data.scores.memberCount)

    local locationData = Game.TableData.GetTarotTeamLocationDataRow(self.data.bornPlace)
    if locationData then
        self.view.AreaName:SetText(locationData.LocationString)
    end

    if self.data.status == tarot_team_status.RECRUITING then
        local now = _G._now()
        local bIsShowDay = Game.TimeUtils.GetServerDayDiff(_G._now(1), self.data.scores.expireTime) > 0
        local expireTimeStr = Game.TimeUtils.FormatCountDownString(self.data.scores.expireTime * 1000 - now,
                true)
        self.view.Text_Time:SetText(expireTimeStr)
        self.view.Team_Lv:SetVisibility(ESlateVisibility.Hidden)
    elseif self.data.status == tarot_team_status.FINAL then
        self.view.Team_Lv:SetText(self.data.scores.level)
        self.view.Text_Time:SetVisibility(ESlateVisibility.Hidden)
    end

    ---标签信息
    local tagList = self.data.tagList
    for _, tagView in ipairs(self.tagsView) do
        tagView:SetVisibility(ESlateVisibility.Collapsed)
    end
    for index, tag in ipairs(tagList) do
        if self.tagsView[index] then
            self:SetImage(self.tagsView[index], self.tagIcon[tag])
            self.tagsView[index]:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end

    self.selectState = data.selectState or false
	self.userWidget:BP_SetSelect(self.selectState)
	self.userWidget:BP_SetTime(self.data.status == tarot_team_status.RECRUITING)
	self.userWidget:BP_SetSelf(self.isSelf)

    ---是否申请
    if self.data.isApplied then
        self.view.WBP_TaotTeamrApReTag:SetVisibility(ESlateVisibility.Visible)
    else
        self.view.WBP_TaotTeamrApReTag:SetVisibility(ESlateVisibility.Hidden)
    end
end

---选择变化回调
---@field selected bool
function TarotTeamList_Item:UpdateSelectionState(selected)
    local isChanged = self.selectState ~= selected
    self.selectState = selected
    if self.selectState and isChanged then
        -- 查询队伍详细信息
        Game.TarotTeamSystem.sender:ReqQueryTarotTeamSearchDetailInfo(self.data.id)
    end
    --self.userWidget:Event_UI_State(self.selectState, self.isSelf, true, self.data.status == tarot_team_status.RECRUITING)
	self.userWidget:BP_SetSelect(self.selectState)
	self.userWidget:BP_SetTime(self.data.status == tarot_team_status.RECRUITING)
	self.userWidget:BP_SetSelf(self.isSelf)
end

return TarotTeamList_Item
