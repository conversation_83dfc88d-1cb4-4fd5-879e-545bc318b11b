local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class TarotTeamTip_Item : UIListItem
---@field view TarotTeamTip_ItemBlueprint
local TarotTeamTip_Item = DefineClass("TarotTeamTip_Item", UIComponent)

TarotTeamTip_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TarotTeamTip_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TarotTeamTip_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TarotTeamTip_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TarotTeamTip_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TarotTeamTip_Item:InitUIView()
end

---面板打开的时候触发
function TarotTeamTip_Item:OnRefresh(text, isFinished)
    self.view.Text_Tip:SetText(text)
    self.view._userWidget:Event_UI_Style(isFinished and 1 or 0)
end

return TarotTeamTip_Item
