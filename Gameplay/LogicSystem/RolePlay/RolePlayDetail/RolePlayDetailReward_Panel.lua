local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class RolePlayDetailReward_Panel : UIPanel
---@field view RolePlayDetailReward_PanelBlueprint
local RolePlayDetailReward_Panel = DefineClass("RolePlayDetailReward_Panel", UIPanel)

RolePlayDetailReward_Panel.eventBindMap = {
    [EEventTypesV2.ROLEPLAY_IDENTITY_LEVEL_REWARDED] = "OnIdentityLevelRewarded",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RolePlayDetailReward_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RolePlayDetailReward_Panel:InitUIData()
    self.identityId = nil
end

--- UI组件初始化，此处为自动生成
function RolePlayDetailReward_Panel:InitUIComponent()
    ---@type UIComBoxFrame
    self.WBP_ComPopupMCom = self:CreateComponent(self.view.WBP_ComPopupM, UIComBoxFrame)
    ---@type UIListView childScript: RolePlayDetailReward_Item
    self.KGListViewCom = self:CreateComponent(self.view.KGListView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function RolePlayDetailReward_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RolePlayDetailReward_Panel:InitUIView()
end

---面板打开的时候触发
function RolePlayDetailReward_Panel:OnRefresh(identityId)
    self.identityId = identityId
    local TableData = Game.TableData
    self.WBP_ComPopupMCom:Refresh(TableData.GetRolePlayConstDataRow("IDENTITY_LEVEL_REWARD_TITLE"))
    local data = TableData.GetRolePlayIdentityDataRow(identityId)
    local levelInfos = {}
    for level, levelData in ksbcipairs(data) do
        levelInfos[level] = {
            LevelData = levelData,
            Identity = identityId,
        }
    end
    self.KGListViewCom:Refresh(levelInfos)
end

function RolePlayDetailReward_Panel:OnIdentityLevelRewarded(identityId, level)
    if identityId ~= self.identityId then return end
    self.KGListViewCom:RefreshItemByIndex(level)
end

return RolePlayDetailReward_Panel
