local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class CardBack : UIComponent
---@field view CardBackBlueprint
local CardBack = DefineClass("CardBack", UIComponent)

CardBack.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CardBack:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CardBack:InitUIData()
    self.identityId = nil
end

--- UI组件初始化，此处为自动生成
function CardBack:InitUIComponent()
    ---@type UISimpleList
    self.List_CardCom = self:CreateComponent(self.view.List_Card, UISimpleList)
    ---@type UISimpleList
    self.List_LineCom = self:CreateComponent(self.view.List_Line, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function CardBack:InitUIEvent()
    self:AddUIEvent(self.view.ClickArea.OnClicked, "on_ClickArea_Clicked")
    self:AddUIEvent(self.List_CardCom.onItemSelected, "on_List_CardCom_ItemSelected")
    self:AddUIEvent(self.List_CardCom.onItemClicked, "on_List_CardCom_ItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CardBack:InitUIView()
end

---组件刷新统一入口
function CardBack:Refresh(identityId)
    self.identityId = identityId
    local levelInfos = {}
    local lineInfos = {}
    local identityData = Game.TableData.GetRolePlayIdentityDataRow(identityId)
    for index, levelData in ksbcipairs(identityData) do
        local unlock = Game.RolePlaySystem:IsIdentityConditionComplete(identityId, index)
        levelInfos[index] = {
            Identity = identityId,
            Level = levelData.Level,
            Unlock = unlock,
        }
        if index > 1 then
            table.insert(lineInfos, unlock)
        end
    end
    self.List_CardCom:Refresh(levelInfos)
    self.List_CardCom:SetSelectedItemByIndex(1, true)
    self.List_LineCom:Refresh(lineInfos)
end

--- 此处为自动生成
function CardBack:on_ClickArea_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.RolePlayDetailReward_Panel, self.identityId)
end

--- 此处为自动生成
---@param index number
---@param data table
function CardBack:on_List_CardCom_ItemSelected(index, data)
    Log.Debug(">>>on_List_CardCom_ItemSelected", index, data)
    self:GetBelongPanel():OnSelectIdentityLevel(data.Level)
end

--- 此处为自动生成
---@param index number
---@param data table
function CardBack:on_List_CardCom_ItemClicked(index, data)
    Log.Debug(">>>on_List_CardCom_ItemClicked", index, data)
    self.List_CardCom:SetSelectedItemByIndex(index, true)
end

return CardBack
