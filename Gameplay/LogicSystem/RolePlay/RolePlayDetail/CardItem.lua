local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class CardItem : UIListItem
---@field view CardItemBlueprint
local CardItem = DefineClass("CardItem", UIListItem)

CardItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CardItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CardItem:InitUIData()
    self.level = nil
end

--- UI组件初始化，此处为自动生成
function CardItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function CardItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CardItem:InitUIView()
end

---@class IdentityLevelInfo
---@field Identity number 身份ID
---@field Level number 等级ID
---@field Unlock boolean 是否解锁
---面板打开的时候触发
---@param levelInfo IdentityLevelInfo
function CardItem:OnRefresh(levelInfo)
    local level = levelInfo.Level
    self.level = level
    local identityData = Game.TableData.GetRolePlayIdentityDataRow(levelInfo.Identity)
    local levelData = identityData and identityData[level]
    if not levelData then return end
    self.view.Text_Name:SetText(levelData.Name)
    local bUnlock = levelInfo.Unlock
    local showLevel = Game.TableData.GetRolePlayConstDataRow("IDENTITY_LEVEL_BASE_NUM") - level
    local bpNum = showLevel * 2 + (bUnlock and 1 or 0)
    self.userWidget:BP_CardItem_Num(bpNum)
end

function CardItem:UpdateSelectionState(selected)
    local selectType = 0
    if selected then
        selectType = self:IsFinalLevel() and 1 or 2
    end
    self.userWidget:BP_CardItem_Select(selectType)
end

function CardItem:PlayUnlockAnim()
    if self:IsFinalLevel() then
        self:PlayAnimation(self.userWidget.Ani_light_big)
    else
        self:PlayAnimation(self.userWidget.Ani_light_small)
    end
end

function CardItem:IsFinalLevel()
    return self.level == Game.TableData.GetRolePlayConstDataRow("IDENTITY_LEVEL_BASE_NUM")
end

return CardItem
