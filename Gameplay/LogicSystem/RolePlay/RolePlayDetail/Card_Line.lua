local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Card_Line : UIComponent
---@field view Card_LineBlueprint
local Card_Line = DefineClass("Card_Line", UIComponent)

Card_Line.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Card_Line:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Card_Line:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Card_Line:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Card_Line:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Card_Line:InitUIView()
end

---组件刷新统一入口
function Card_Line:Refresh(unlock)
    self.userWidget:BP_Line(unlock)
end

return Card_Line
