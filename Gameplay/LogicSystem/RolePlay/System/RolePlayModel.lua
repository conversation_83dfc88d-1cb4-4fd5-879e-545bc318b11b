---@class RolePlayModel
local RolePlayModel = DefineClass("RolePlayModel", SystemModelBase)

function RolePlayModel:init()
    self.identityInfos = nil                --身份信息，identityId->ROLEPLAY_IDENTITY
    self.identityUnlockedInfo = nil         --身份当前已解锁最高等级信息，identityId->level
    self.talentLevelInfo = nil              --天赋树节点等级信息，talentId->level
    self.playingIdentity = nil              --上次扮演的身份
    self.displaySceneID = nil               --展示场景ID
    self.displayScenePanel = nil            --带展示场景镜头的UI
    self.displayScenePanelParams = nil      --带展示场景镜头的UI参数
end

function RolePlayModel:unInit()
    self:init()
end

function RolePlayModel:clear()
    self:init()
end

function RolePlayModel:OnMainPlayerCreate()
    local player = Game.me
    self.identityInfos = self.identityInfos or player.rolePlayIdentity
    self.identityUnlockedInfo = self.identityUnlockedInfo or player.rolePlayUnlockIdentity
    self.talentLevelInfo = self.talentLevelInfo or player.rolePlayTalentTree
    self.playingIdentity = self.playingIdentity or player.lastRPIdentity
end

---@class IdentityInfo
---@field level number 展示等级，初始为1级
---@field exp number 当前消化度
---@field curExp number 今日已获得消化度
---@return IdentityInfo
function RolePlayModel:GetShowIdentityInfo(identityId)
    local info = self.identityInfos and self.identityInfos[identityId]
    if not info then
        info = {
            level = 1,
            exp = 0,
            curExp = 0,
        }
    end
    return info
end

function RolePlayModel:OnUpdateRolePlayIdentity(identityInfos)
    local cachedIdentityInfos = self.identityInfos
    for identityId, info in pairs(identityInfos) do
        cachedIdentityInfos[identityId] = info
    end
end

function RolePlayModel:OnRolePlayUnlockIdentityLevel(identityId, level)
    self.identityUnlockedInfo[identityId] = level
end

function RolePlayModel:GetUnlockedIdentityLevel(identityId)
    return self.identityUnlockedInfo[identityId]
end

function RolePlayModel:GetIdentityInfo(identityId)
    return self.identityInfos[identityId]
end

function RolePlayModel:IsTalentUnlocked(talentId)
    local level = self.talentLevelInfo[talentId]
    return level and level > 0 or false
end

function RolePlayModel:IsTalentFillCondition(talentId)
    local level = self.talentLevelInfo[talentId]
    return level ~= nil
end

function RolePlayModel:UpdateTalentsLevelInfo(nodesInfo)
    local cachedLevelInfos = self.talentLevelInfo
    for talentId, level in pairs(nodesInfo) do
        cachedLevelInfos[talentId] = level
    end
end

return RolePlayModel

