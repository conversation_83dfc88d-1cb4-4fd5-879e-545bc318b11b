local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Card_Item : UIListItem
---@field view Card_ItemBlueprint
local Card_Item = DefineClass("Card_Item", UIListItem)

Card_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Card_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Card_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Card_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Card_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Card_Item:InitUIView()
end

---面板打开的时候触发
function Card_Item:OnRefresh(identityId)
    local identityData = Game.TableData.GetRolePlayIdentityDataRow(identityId)
    local state = Game.RolePlaySystem:GetIdentityState(identityId)
    self.userWidget:BP_CardItem(state)
    local info = Game.RolePlaySystem:GetShowIdentityInfo(identityId)
    self.view.Text_Num:SetText(Game.TableData.GetRolePlayConstDataRow("IDENTITY_LEVEL_BASE_NUM") - info.level)
    local levelData = identityData[info.level]
    if not levelData then return end
    self.view.Text_Name:SetText(levelData.Name)
    self:SetImage(self.view.Img_CardBg, identityData[1].BgImage)
end

return Card_Item
