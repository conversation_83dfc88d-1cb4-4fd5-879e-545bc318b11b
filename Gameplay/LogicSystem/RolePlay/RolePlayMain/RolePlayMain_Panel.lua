local Const = kg_require("Shared.Const")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIIrregularListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIIrregularListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class RolePlayMain_Panel : UIPanel
---@field view RolePlayMain_PanelBlueprint
local RolePlayMain_Panel = DefineClass("RolePlayMain_Panel", UIPanel)

RolePlayMain_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RolePlayMain_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RolePlayMain_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RolePlayMain_Panel:InitUIComponent()
    ---@type UIComBackTitle
    self.Btn_BackCom = self:CreateComponent(self.view.Btn_Back, UIComBackTitle)
    ---@type UIIrregularListView
    self.List_CardCom = self:CreateComponent(self.view.List_Card, UIIrregularListView)
end

---UI事件在这里注册，此处为自动生成
function RolePlayMain_Panel:InitUIEvent()
    self:AddUIEvent(self.view.ClickArea_Extra.OnClicked, "on_ClickArea_Extra_Clicked")
    self:AddUIEvent(self.view.ClickArea_Property.OnClicked, "on_ClickArea_Property_Clicked")
    self:AddUIEvent(self.List_CardCom.onItemSelected, "on_List_CardCom_ItemSelected")
    self:AddUIEvent(self.List_CardCom.onItemClicked, "on_List_CardCom_ItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RolePlayMain_Panel:InitUIView()
end

---面板打开的时候触发
function RolePlayMain_Panel:OnRefresh(fadeinType)
    local TableData = Game.TableData
    self.Btn_BackCom:Refresh(TableData.GetRolePlayConstDataRow("ROLEPLAY_MAINPANEL_TITLE"))
    local identityList = {}
    for identityId, _ in ksbcpairs(TableData.GetRolePlayIdentityDataTable()) do
        table.insert(identityList, identityId)
    end
    self.List_CardCom:Refresh(identityList)

    self:PlayCustomFadeinAnim(fadeinType)
end

function RolePlayMain_Panel:PlayCustomFadeinAnim(fadeinType)
    self:StopAllAnimations()
    local animTypeEnum = Const.RolePlayAnimType
    Log.Debug(">>RolePlayMain_Panel PlayCustomFadeinAnim", fadeinType)
    if fadeinType == animTypeEnum.IdentityDetail then
        self:PlayAnimation(self.userWidget.Ani_zhuanchang_card_fan)
    elseif fadeinType == animTypeEnum.Talent then
        self:PlayAnimation(self.userWidget.Ani_zhuanchang_bg_open)
    else
        self:PlayAnimation(self.userWidget.Ani_Fadein1)
    end
end

--- 此处为自动生成
function RolePlayMain_Panel:on_ClickArea_Extra_Clicked()
    Game.RolePlaySystem:ReqEnterRolePlayGame()
end

--- 此处为自动生成
function RolePlayMain_Panel:on_ClickArea_Property_Clicked()
    Game.RolePlaySystem:OpenRolePlayScenePanel(UIPanelConfig.RolePlayTalent_Panel)
    self:PlayAnimation(self.userWidget.Ani_zhuanchang_bg_close)
end

--- 此处为自动生成
---@param index number
---@param data table
function RolePlayMain_Panel:on_List_CardCom_ItemSelected(index, data)
    Log.Debug("on_List_CardCom_ItemSelected", index)
end

--- 此处为自动生成
---@param index number
---@param data table
function RolePlayMain_Panel:on_List_CardCom_ItemClicked(index, data)
    Log.Debug("on_List_CardCom_ItemClicked", index)
    Game.RolePlaySystem:OpenRolePlayScenePanel(UIPanelConfig.RolePlayDetail_Panel, data, true)
    self:PlayAnimation(self.userWidget.Ani_zhuanchang_card)
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function RolePlayMain_Panel:on_WBP_RolePlayMain_SelectDropType1Com_ItemSelected(index, data)
end

function RolePlayMain_Panel:OnClose()
    Game.RolePlaySystem:ClearRolePlayScene()
end

return RolePlayMain_Panel
