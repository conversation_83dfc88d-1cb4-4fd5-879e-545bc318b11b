local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Condition_Item : UIListItem
---@field view Condition_ItemBlueprint
local Condition_Item = DefineClass("Condition_Item", UIListItem)

Condition_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Condition_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Condition_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Condition_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Condition_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Condition_Item:InitUIView()
end

---面板打开的时候触发
function Condition_Item:OnRefresh(data)
    local desc = data.Desc
    local isDone = data.bDone
    self.view.Text_Condition:SetText(desc)
    self.userWidget:BP_Condition(isDone)
end

return Condition_Item
