local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Talent_Item : UIListItem
---@field view Talent_ItemBlueprint
local Talent_Item = DefineClass("Talent_Item", UIListItem)

Talent_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Talent_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Talent_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Talent_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Talent_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Talent_Item:InitUIView()
    Log.Debug(">>RolePlayTalent_Item:InitUIView")
    self.userWidget.slot:SetAlignment(FVector2D(0.5, 0.5))
end

---面板打开的时候触发
function Talent_Item:OnRefresh(talentId)
    Log.Debug(">>Talent_Item:OnRefresh", talentId)
    local talentData = Game.TableData.GetRolePlayTalentTreeDataRow(talentId)
    local pos = FVector2D(table.unpack(talentData.NodePos))
    self.userWidget.Slot:SetPosition(pos)
    local bUnlock = Game.RolePlaySystem:IsTalentUnlock(talentId)
    self.userWidget:BP_TalentItem_Lock(bUnlock)
    local bFill = Game.RolePlaySystem:IsTalentFillCondition(talentId)
    self.userWidget:BP_TalentItem_Condition(bFill)
end

function Talent_Item:UpdateSelectionState(selected)
    self.userWidget:BP_TalentItem_Select(selected)
end

function Talent_Item:PlayUnlockAnim()
    self:PlayAnimation(self.userWidget.Ani_condition)
end

return Talent_Item
