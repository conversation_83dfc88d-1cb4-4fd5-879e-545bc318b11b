--- web文件缓存系统
--- 使用LRU缓存算法
--- 缓存文件记录在self.model中
---@class WebFileCacheSystem:SystemBase
local WebFileCacheSystem = DefineClass("WebFileCacheSystem",SystemBase)
local WebFileCacheModel = kg_require("Gameplay.LogicSystem.LocalResSystem.WebFileCacheModel")
local UBlueprintPathsLibrary = import("BlueprintPathsLibrary")
local ULuaFunctionLibrary = import("LuaFunctionLibrary")

function WebFileCacheSystem:ctor()
	-- 保存位置 Saved/UI_Web_Cache
	self.SaveDirectoryPath = UBlueprintPathsLibrary.ProjectSavedDir() .. "UI_Web_Cache"

	-- 转成绝对路径 然后创建文件夹
	self.SaveDirectoryPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(self.SaveDirectoryPath)
	self:CheckAndCreateDirectory(self.SaveDirectoryPath)
	for _,moduleName in pairs(UIConst.UIWebImageModule) do
		self:CheckAndCreateDirectory(self.SaveDirectoryPath .. "/".. moduleName )
	end
	
	-- 每个模块独立的LRU缓存
	self.moduleCaches = {}


	
	-- 每个模块的当前缓存大小（字节）
	self.moduleSizes = {}
	
	-- 并发下载控制
	self.downloadingCount = 0
	self.downloadQueue = {}
end

function WebFileCacheSystem:dtor()
	-- 保存缓存数据
	if self.model then
		self.model:SaveAllLocalData()
	end
end

function WebFileCacheSystem:AfterPlayerInit()
end

function WebFileCacheSystem:onInit()
	Log.Debug('WebFileCacheSystem Init ----- onInit')
	---@type WebFileCacheModel
	self.model = WebFileCacheModel.new(true, true)
	
	-- 加载本地缓存数据
	self.model:LoadAllLocalData()
	
	-- 初始化LRU缓存
	self:InitLRUCache()
	
	-- 清理过期文件
	self:CleanExpiredFiles()
end

function WebFileCacheSystem:UpdateFpHost(fpHost, fpPort)
	self.Host = fpHost
	self.Port = fpPort
end

function WebFileCacheSystem:OnInitNew()
end

--- 初始化LRU缓存
function WebFileCacheSystem:InitLRUCache()
	for moduleName, cacheData in pairs(self.model.CacheMap) do
		for resId, item in pairs(cacheData) do
			-- 检查文件是否存在
			if UBlueprintPathsLibrary.FileExists(item.path) then
				-- 检查是否过期
				if not self:IsExpired(item) then
					self:AddToLRU(moduleName, resId, item)
				else
					-- 删除过期文件
					os.remove(item.path)
					cacheData[resId] = nil
				end
			else
				-- 文件不存在，从缓存中移除
				cacheData[resId] = nil
			end
		end
	end
end

--- 检查文件是否过期
---@param item LocalCacheMapItemNew
---@return boolean
function WebFileCacheSystem:IsExpired(item)
	if not item.expire_ts or item.expire_ts == 0 then
		return false
	end
	return os.time() > item.expire_ts
end

--- 添加到LRU缓存
---@param moduleName string
---@param resId string
---@param item LocalCacheMapItemNew
function WebFileCacheSystem:AddToLRU(moduleName, resId, item)
	-- 确保模块缓存结构存在
	if not self.moduleCaches[moduleName] then
		self.moduleCaches[moduleName] = {
			head = {},
			tail = {},
			cache = {}
		}
		self.moduleCaches[moduleName].head.next = self.moduleCaches[moduleName].tail
		self.moduleCaches[moduleName].tail.prev = self.moduleCaches[moduleName].head
	end
	
	if not self.moduleSizes[moduleName] then
		self.moduleSizes[moduleName] = 0
	end
	
	local key = resId -- 使用resId作为key，因为已经按模块分离
	
	-- 如果已存在，先移除
	if self.moduleCaches[moduleName].cache[key] then
		self:RemoveFromLRU(moduleName, key)
	end
	
	-- 创建新节点
	local node = {
		moduleName = moduleName,
		resId = resId,
		item = item,
		prev = self.moduleCaches[moduleName].head,
		next = self.moduleCaches[moduleName].head.next
	}
	
	-- 插入到链表头部
	self.moduleCaches[moduleName].head.next.prev = node
	self.moduleCaches[moduleName].head.next = node
	
	-- 添加到缓存映射
	self.moduleCaches[moduleName].cache[key] = node
	self.moduleSizes[moduleName] = self.moduleSizes[moduleName] + (item.size or 0)
	
	-- 检查是否需要清理
	self:CheckAndCleanCache(moduleName)
end

--- 从LRU缓存中移除
---@param moduleName string
---@param key string
function WebFileCacheSystem:RemoveFromLRU(moduleName, key)
	local node = self.moduleCaches[moduleName].cache[key]
	if not node then
		return
	end
	
	-- 从链表中移除
	node.prev.next = node.next
	node.next.prev = node.prev
	
	-- 从缓存映射中移除
	self.moduleCaches[moduleName].cache[key] = nil
	self.moduleSizes[moduleName] = self.moduleSizes[moduleName] - (node.item.size or 0)
end

--- 获取缓存项（会更新LRU顺序）
---@param moduleName string
---@param resId string
---@return LocalCacheMapItemNew|nil
function WebFileCacheSystem:GetCacheItem(moduleName, resId)
	if not self.moduleCaches[moduleName] then
		return nil
	end
	
	local key = resId
	local node = self.moduleCaches[moduleName].cache[key]
	
	if not node then
		return nil
	end
	
	-- 检查是否过期
	if self:IsExpired(node.item) then
		self:RemoveCacheItem(moduleName, resId)
		return nil
	end
	
	-- 移动到链表头部（更新LRU顺序）
	self:MoveToHead(moduleName, node)
	
	return node.item
end

--- 移动节点到链表头部
---@param moduleName string
---@param node table
function WebFileCacheSystem:MoveToHead(moduleName, node)
	-- 从当前位置移除
	node.prev.next = node.next
	node.next.prev = node.prev
	
	-- 插入到头部
	node.prev = self.moduleCaches[moduleName].head
	node.next = self.moduleCaches[moduleName].head.next
	self.moduleCaches[moduleName].head.next.prev = node
	self.moduleCaches[moduleName].head.next = node
end

--- 移除缓存项
---@param moduleName string
---@param resId string
function WebFileCacheSystem:RemoveCacheItem(moduleName, resId)
	local key = resId
	
	-- 从LRU缓存中移除
	self:RemoveFromLRU(moduleName, key)
	
	-- 从model中移除
	if self.model.CacheMap[moduleName] then
		local item = self.model.CacheMap[moduleName][resId]
		if item and item.path then
			-- 删除文件
			os.remove(item.path)
		end
		self.model.CacheMap[moduleName][resId] = nil
	end
end

--- 检查并清理缓存
---@param moduleName string
function WebFileCacheSystem:CheckAndCleanCache(moduleName)
	local config = UIConst.UIWebImageModuleConfig[moduleName]
	if not config then
		return
	end
	
	local maxSize = (config.CacheSize or 100) * 1024 * 1024 -- 转换为字节
	
	-- 如果当前大小超过限制，清理最久未使用的项
	while self.moduleSizes[moduleName] > maxSize and self.moduleCaches[moduleName].tail.prev ~= self.moduleCaches[moduleName].head do
		local oldestNode = self.moduleCaches[moduleName].tail.prev
		self:RemoveCacheItem(oldestNode.moduleName, oldestNode.resId)
	end
end

--- 清理过期文件
function WebFileCacheSystem:CleanExpiredFiles()
	for moduleName, cacheData in pairs(self.model.CacheMap) do
		local toRemove = {}
		for resId, item in pairs(cacheData) do
			if self:IsExpired(item) then
				table.insert(toRemove, resId)
			end
		end
		
		for _, resId in ipairs(toRemove) do
			self:RemoveCacheItem(moduleName, resId)
		end
	end
end

--- 添加缓存项
---@param moduleName string
---@param resId string
---@param urls string[]
---@param localPath string
---@param fileSize number
---@param ttl number 过期时间（秒），0表示不过期
function WebFileCacheSystem:AddCacheItem(moduleName, resId, urls, localPath, fileSize, ttl)
	-- 确保模块目录存在
	local moduleDir = string.format("%s/%s", self.SaveDirectoryPath, moduleName)
	self:CheckAndCreateDirectory(moduleDir)
	
	-- 创建缓存项
	local item = {
		res_id = resId,
		expire_ts = ttl > 0 and (os.time() + ttl) or 0,
		status = true,
		urls = urls,
		path = localPath,
		size = fileSize
	}
	
	-- 添加到model
	if not self.model.CacheMap[moduleName] then
		self.model.CacheMap[moduleName] = {}
	end
	self.model.CacheMap[moduleName][resId] = item
	
	-- 添加到LRU缓存
	self:AddToLRU(moduleName, resId, item)
	
	-- 保存到本地
	self.model:SaveLocalData(moduleName)
end

--- 获取文件大小
---@param filePath string
---@return number
function WebFileCacheSystem:GetFileSize(filePath)
	local file = io.open(filePath, "rb")
	if not file then
		return 0
	end
	
	local size = file:seek("end")
	file:close()
	return size
end

--- 检查并创建目录
function WebFileCacheSystem:CheckAndCreateDirectory(directoryPath)
	if not UBlueprintPathsLibrary.DirectoryExists(directoryPath) then
		ULuaFunctionLibrary.MakeDirectory(directoryPath, true)
	end
end

--- 获取文件路径
function WebFileCacheSystem:GetFilePath(imgName, moduleName)
	if moduleName == nil then
		moduleName = UIConst.UIWebImageModule.Default
	end
	return string.format("%s/%s/%s.png", self.SaveDirectoryPath, moduleName, imgName)
end

--- 获取缓存统计信息
---@param moduleName string
---@return table
function WebFileCacheSystem:GetCacheStats(moduleName)
	local stats = {
		totalItems = 0,
		totalSize = 0,
		expiredItems = 0
	}
	
	if not self.model.CacheMap[moduleName] then
		return stats
	end
	
	for resId, item in pairs(self.model.CacheMap[moduleName]) do
		stats.totalItems = stats.totalItems + 1
		stats.totalSize = stats.totalSize + (item.size or 0)
		
		if self:IsExpired(item) then
			stats.expiredItems = stats.expiredItems + 1
		end
	end
	
	return stats
end

--- 清理指定模块的缓存
---@param moduleName string
function WebFileCacheSystem:ClearModuleCache(moduleName)
	if not self.model.CacheMap[moduleName] then
		return
	end
	
	-- 删除所有文件
	for resId, item in pairs(self.model.CacheMap[moduleName]) do
		if item.path then
			os.remove(item.path)
		end
		-- 从LRU缓存中移除
		self:RemoveFromLRU(moduleName, resId)
	end
	
	-- 清空model数据
	self.model:ClearModule(moduleName)
	self.model:SaveLocalData(moduleName)
	
	-- 清空模块缓存结构
	self.moduleCaches[moduleName] = nil
	self.moduleSizes[moduleName] = nil
end

--- 清理所有缓存
function WebFileCacheSystem:ClearAllCache()
	for moduleName, _ in pairs(self.model.CacheMap) do
		self:ClearModuleCache(moduleName)
	end
end

--- 获取缓存项（会更新LRU顺序）
---@param imgName string
---@param moduleName string
---@return LocalCacheMapItemNew|nil
function WebFileCacheSystem:GetCachedImage(imgName, moduleName)
	if moduleName == nil then
		moduleName = UIConst.UIWebImageModule.Default
	end
	
	return self:GetCacheItem(moduleName, imgName)
end

return WebFileCacheSystem
