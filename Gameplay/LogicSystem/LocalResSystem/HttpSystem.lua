--- 管理 Framework/DoraSDK/HttpClient.lua HttpRequest
--- 有请求队列与优先级
---@class HttpSystem:SystemBase
local HttpSystem = DefineClass("HttpSystem",SystemBase)

---@alias HttpCallbackHandler fun(errorStr:string, httpCode:number, responseHeaders:table, responseBody:any):void

-- HTTP请求状态枚举
local HttpRequestStatus = {
    Pending = "Pending",      -- 等待中
    Running = "Running",      -- 执行中
    Completed = "Completed",  -- 已完成
    Failed = "Failed",        -- 失败
    Cancelled = "Cancelled"   -- 已取消
}

-- HTTP请求优先级枚举
local HttpRequestPriority = {
    Low = 1,      -- 低优先级
    Normal = 2,   -- 普通优先级
    High = 3,     -- 高优先级
    Critical = 4  -- 关键优先级
}

function HttpSystem:ctor()
    -- 请求队列
    self.requestQueue = {}
    
    -- 正在执行的请求
    self.runningRequests = {}
    
    -- 请求统计
    self.stats = {
        totalRequests = 0,
        completedRequests = 0,
        failedRequests = 0,
        cancelledRequests = 0,
        queuedRequests = 0
    }
    
    -- 配置参数
    self.config = {
        maxConcurrentRequests = 5,  -- 最大并发请求数
        defaultTimeout = 30,        -- 默认超时时间（秒）
        defaultRetryCount = 3,      -- 默认重试次数
        defaultRetryInterval = 2,   -- 默认重试间隔（秒）
        enableRetry = true,         -- 是否启用重试
        enableQueue = true          -- 是否启用队列
    }
    
    -- 请求ID计数器
    self.requestIdCounter = 0
    
    -- 回调映射表
    ---@type table<string, HttpCallbackHandler>
    self.callbackMap = {}
end

function HttpSystem:dtor()
    -- 清理所有请求
    self:ClearAllRequests()
end

function HttpSystem:onInit()
    Log.Info('HttpSystem Init ----- onInit')
end

--- 设置配置参数
---@param config table 配置参数
function HttpSystem:SetConfig(config)
    if config.maxConcurrentRequests then
        self.config.maxConcurrentRequests = config.maxConcurrentRequests
    end
    if config.defaultTimeout then
        self.config.defaultTimeout = config.defaultTimeout
    end
    if config.defaultRetryCount then
        self.config.defaultRetryCount = config.defaultRetryCount
    end
    if config.defaultRetryInterval then
        self.config.defaultRetryInterval = config.defaultRetryInterval
    end
    if config.enableRetry ~= nil then
        self.config.enableRetry = config.enableRetry
    end
    if config.enableQueue ~= nil then
        self.config.enableQueue = config.enableQueue
    end
    
    Log.InfoFormat("HttpSystem配置已更新: 最大并发数=%d, 默认超时=%d秒, 默认重试次数=%d", 
        self.config.maxConcurrentRequests, self.config.defaultTimeout, self.config.defaultRetryCount)
end

--- 发送HTTP请求
---@param method string HTTP方法 (GET, POST, DELETE, PUT)
---@param url string 完整URL
---@param body string|nil 请求体
---@param headers table|nil 请求头
---@param options table|nil 请求选项
---@param callback HttpCallbackHandler 回调函数
---@return string requestId 请求ID
function HttpSystem:SendRequest(method, url, body, headers, options, callback)
    local requestId = self:GenerateRequestId()
    
    -- 解析URL
    local host, port, path, isSSL = self:ParseURL(url)
    
    -- 设置默认参数
    local timeout = options and options.timeout or self.config.defaultTimeout
    local retryCount = options and options.retryCount or self.config.defaultRetryCount
    local retryInterval = options and options.retryInterval or self.config.defaultRetryInterval
    local priority = options and options.priority or HttpRequestPriority.Normal
    
    -- 创建请求对象
    local request = {
        id = requestId,
        options = options or {},
        method = method,
        host = host,
        port = port,
        path = path,
        body = body,
        headers = headers or {},
        isSSL = isSSL,
        timeout = timeout,
        retryCount = retryCount,
        retryInterval = retryInterval,
        priority = priority,
        status = HttpRequestStatus.Pending,
        callback = callback,
        createTime = os.time(),
        startTime = nil,
        endTime = nil,
        currentRetry = 0,
        errorMessage = nil,
        responseCode = nil,
        responseHeaders = nil,
        responseBody = nil
    }
    
    -- 保存回调
    if callback then
        self.callbackMap[requestId] = callback
    end
    
    -- 添加到队列或直接执行
    if self.config.enableQueue and #self.runningRequests >= self.config.maxConcurrentRequests then
        self:AddToQueue(request)
        Log.InfoFormat("HTTP请求已加入队列: %s %s (优先级: %d)", method, url, priority)
    else
        self:ExecuteRequest(request)
    end
    
    return requestId
end

--- 发送GET请求
---@param url string URL
---@param headers table|nil 请求头
---@param options table|nil 请求选项
---@param callback function|nil 回调函数
---@return string requestId
function HttpSystem:Get(url, headers, options, callback)
    return self:SendRequest("GET", url, nil, headers, options, callback)
end

--- 发送POST请求
---@param url string URL
---@param body string 请求体
---@param headers table|nil 请求头
---@param options table|nil 请求选项
---@param callback function|nil 回调函数
---@return string requestId
function HttpSystem:Post(url, body, headers, options, callback)
    return self:SendRequest("POST", url, body, headers, options, callback)
end

--- 发送PUT请求
---@param url string URL
---@param body string 请求体
---@param headers table|nil 请求头
---@param options table|nil 请求选项
---@param callback function|nil 回调函数
---@return string requestId
function HttpSystem:Put(url, body, headers, options, callback)
    return self:SendRequest("PUT", url, body, headers, options, callback)
end

--- 发送DELETE请求
---@param url string URL
---@param headers table|nil 请求头
---@param options table|nil 请求选项
---@param callback function|nil 回调函数
---@return string requestId
function HttpSystem:Delete(url, headers, options, callback)
    return self:SendRequest("DELETE", url, nil, headers, options, callback)
end

---@alias DownloadHttpCallbackHandler fun(errorStr:string, httpCode:number, responseHeaders:table, filePath:string):void


--- 下载文件
---@param urls string[] 文件URL(主备地址)
---@param fileName string 文件名 
---@param moduleName string 模块名称
---@param options table|nil 请求选项
---@param callback DownloadHttpCallbackHandler 回调函数
---@return string requestId
function HttpSystem:DownloadFile(urls, fileName, moduleName, options, callback)
    assert(type(urls) == "table" and #urls > 0, "urls必须为非空数组")
    -- 先检查缓存系统
    local cacheSystem = Game.WebFileCacheSystem
    if cacheSystem then
        local cachedItem = cacheSystem:GetCachedImage(fileName, moduleName)
        if cachedItem then
            Log.InfoFormat("文件已缓存: %s", fileName)
            if callback then
                callback(nil, 200, {}, cachedItem.path)
            end
            return "cached_" .. fileName
        end
    end

    -- 设置下载专用选项
    local downloadOptions = options or {}
    downloadOptions.isDownload = true
    downloadOptions.fileName = fileName
    downloadOptions.moduleName = moduleName

    -- 递归尝试下载
    local function tryDownload(index, lastError)
        if index > #urls then
            -- 全部失败
            if callback then
                callback(lastError or "所有地址下载失败", 0, {}, nil)
            end
            return
        end
        local url = urls[index]
        self:Get(url, nil, downloadOptions, function(errorStr, httpCode, responseHeaders, responseBody)
            if not errorStr and httpCode == 200 then
                -- 成功
                if callback then
                    callback(nil, httpCode, responseHeaders, responseBody)
                end
            else
                Log.ErrorFormat("下载失败，尝试下一个地址: %s, 错误: %s", url, errorStr or ("HTTP"..tostring(httpCode)))
                tryDownload(index + 1, errorStr or ("HTTP"..tostring(httpCode)))
            end
        end)
    end
    tryDownload(1, nil)
    return "download_" .. fileName .. "_" .. os.time()
end

--- 取消请求
---@param requestId string 请求ID
---@return boolean success 是否成功取消
function HttpSystem:CancelRequest(requestId)
    -- 检查是否在运行中
    if self.runningRequests[requestId] then
        local request = self.runningRequests[requestId]
        request.status = HttpRequestStatus.Cancelled
        self.runningRequests[requestId] = nil
        self.stats.cancelledRequests = self.stats.cancelledRequests + 1
        self.stats.queuedRequests = self.stats.queuedRequests - 1
        
        Log.InfoFormat("HTTP请求已取消: %s", requestId)
        
        -- 执行下一个请求
        self:ProcessQueue()
        return true
    end
    
    -- 检查是否在队列中
    for i, request in ipairs(self.requestQueue) do
        if request.id == requestId then
            table.remove(self.requestQueue, i)
            request.status = HttpRequestStatus.Cancelled
            self.stats.cancelledRequests = self.stats.cancelledRequests + 1
            self.stats.queuedRequests = self.stats.queuedRequests - 1
            
            Log.InfoFormat("队列中的HTTP请求已取消: %s", requestId)
            return true
        end
    end
    
    return false
end

--- 获取请求状态
---@param requestId string 请求ID
---@return string|nil status 请求状态
function HttpSystem:GetRequestStatus(requestId)
    -- 检查运行中的请求
    if self.runningRequests[requestId] then
        return self.runningRequests[requestId].status
    end
    
    -- 检查队列中的请求
    for _, request in ipairs(self.requestQueue) do
        if request.id == requestId then
            return request.status
        end
    end
    
    return nil
end

--- 获取队列状态
---@return table stats 队列统计信息
function HttpSystem:GetQueueStats()
    return {
        totalRequests = self.stats.totalRequests,
        completedRequests = self.stats.completedRequests,
        failedRequests = self.stats.failedRequests,
        cancelledRequests = self.stats.cancelledRequests,
        queuedRequests = self.stats.queuedRequests,
        runningRequests = #self.runningRequests,
        queueLength = #self.requestQueue,
        maxConcurrent = self.config.maxConcurrentRequests
    }
end

--- 清理所有请求
function HttpSystem:ClearAllRequests()
    -- 取消所有运行中的请求
    for requestId, _ in pairs(self.runningRequests) do
        self:CancelRequest(requestId)
    end
    
    -- 清空队列
    self.requestQueue = {}
    self.stats.queuedRequests = 0
    
    -- 清空回调映射
    self.callbackMap = {}
    
    Log.Info("所有HTTP请求已清理")
end

--- 生成请求ID
---@return string requestId
function HttpSystem:GenerateRequestId()
    self.requestIdCounter = self.requestIdCounter + 1
    return "http_" .. self.requestIdCounter .. "_" .. os.time()
end

--- 解析URL
---@param url string URL
---@return string host, number port, string path, boolean isSSL
function HttpSystem:ParseURL(url)
    local protocol, rest = url:match("^(https?)://(.+)")
    local isSSL = protocol == "https"
    
    local host, path = rest:match("^([^/]+)(.*)")
    if not path or path == "" then
        path = "/"
    end
    
    local port = isSSL and 443 or 80
    if host:find(":") then
        host, portStr = host:match("^([^:]+):(.+)")
        port = tonumber(portStr) or port
    end
    
    return host, port, path, isSSL
end

--- 添加到队列
---@param request table 请求对象
function HttpSystem:AddToQueue(request)
    -- 按优先级插入队列
    local insertIndex = #self.requestQueue + 1
    for i, queuedRequest in ipairs(self.requestQueue) do
        if request.priority > queuedRequest.priority then
            insertIndex = i
            break
        end
    end
    
    table.insert(self.requestQueue, insertIndex, request)
    self.stats.queuedRequests = self.stats.queuedRequests + 1
    self.stats.totalRequests = self.stats.totalRequests + 1
end

--- 执行请求
---@param request table 请求对象
function HttpSystem:ExecuteRequest(request)
    request.status = HttpRequestStatus.Running
    request.startTime = os.time()
    self.runningRequests[request.id] = request
    
    Log.InfoFormat("执行HTTP请求: %s %s:%d%s", request.method, request.host, request.port, request.path)
    
    -- 调用SDK的HttpRequest
    HttpRequest(
        request.method,
        request.timeout,
        request.host,
        request.port,
        request.path,
        request.body or "",
        request.isSSL,
        request.headers,
        function(errorStr, httpCode, responseHeaders, responseBody)
            self:OnHttpCallback(request.id, errorStr, httpCode, responseHeaders, responseBody)
        end
    )
end

--- HTTP回调处理
---@param requestId string 请求ID
---@param errorStr string|nil 错误信息
---@param httpCode number HTTP状态码
---@param responseHeaders table 响应头
---@param responseBody string 响应体
function HttpSystem:OnHttpCallback(requestId, errorStr, httpCode, responseHeaders, responseBody)
    local request = self.runningRequests[requestId]
    if not request then
        Log.ErrorFormat("未找到请求: %s", requestId)
        return
    end
    
    request.endTime = os.time()
    request.responseCode = httpCode
    request.responseHeaders = responseHeaders
    request.responseBody = responseBody
    
    -- 处理下载文件
    if request.options and request.options.isDownload then
        self:HandleDownloadResponse(request, errorStr, httpCode, responseHeaders, responseBody)
        return
    end
    
    -- 检查是否需要重试
    if errorStr and self.config.enableRetry and request.currentRetry < request.retryCount then
        self:RetryRequest(request, errorStr)
        return
    end
    
    -- 完成请求
    self:CompleteRequest(request, errorStr, httpCode, responseHeaders, responseBody)
end

--- 处理下载响应
---@param request table 请求对象
---@param errorStr string|nil 错误信息
---@param httpCode number HTTP状态码
---@param responseHeaders table 响应头
---@param responseBody string 响应体
function HttpSystem:HandleDownloadResponse(request, errorStr, httpCode, responseHeaders, responseBody)
    if errorStr or httpCode ~= 200 then
        Log.ErrorFormat("文件下载失败: %s, 错误: %s, 状态码: %d", request.options.fileName, errorStr or "未知", httpCode)
        self:CompleteRequest(request, errorStr or "HTTP错误: " .. httpCode, httpCode, responseHeaders, responseBody)
        return
    end
    local filePath = Game.WebFileCacheSystem:GetFilePath(request.options.fileName, request.options.moduleName)

    -- 保存文件
    local success = self:SaveFile(filePath, responseBody)
    if not success then
        Log.ErrorFormat("文件保存失败: %s %s", request.options.fileName, filePath)
        self:CompleteRequest(request, "文件保存失败", httpCode, responseHeaders, responseBody)
        return
    end
    
    -- 添加到缓存系统
    if request.options.moduleName then
        local cacheSystem = Game.WebFileCacheSystem
        if cacheSystem then
            local fileName = request.options.fileName
            local fileSize = #responseBody
            cacheSystem:AddCacheItem(
                request.options.moduleName,
                fileName,
                {request.host .. request.path}, 
                    filePath,
                fileSize,
                0  -- 不过期
            )
            Log.InfoFormat("文件已缓存: %s, 大小: %d字节", fileName, fileSize)
        end
    end
    
    self:CompleteRequest(request, nil, httpCode, responseHeaders, filePath)
end

--- 保存文件
---@param filePath table 文件路径
---@param content string 文件内容
---@return boolean success 是否成功
function HttpSystem:SaveFile(filePath, content)
    -- 写入文件
    local file = io.open(filePath, "wb")
    if not file then
        return false
    end
    
    file:write(content)
    file:close()
    return true
end

--- 重试请求
---@param request table 请求对象
---@param errorStr string 错误信息
function HttpSystem:RetryRequest(request, errorStr)
    request.currentRetry = request.currentRetry + 1
    request.errorMessage = errorStr
    
    Log.InfoFormat("HTTP请求重试 %d/%d: %s", request.currentRetry, request.retryCount, request.id)
    
    -- 延迟重试
    local retryDelay = request.retryInterval * request.currentRetry
    Game.TimerManager:CreateTimerAndStart(function()
        if request.status == HttpRequestStatus.Running then
            self:ExecuteRequest(request)
        end
    end, retryDelay * 1000, 1)
end

--- 完成请求
---@param request table 请求对象
---@param errorStr string|nil 错误信息
---@param httpCode number HTTP状态码
---@param responseHeaders table 响应头
---@param responseBody string 响应体
function HttpSystem:CompleteRequest(request, errorStr, httpCode, responseHeaders, responseBody)
    -- 更新状态
    if errorStr then
        request.status = HttpRequestStatus.Failed
        self.stats.failedRequests = self.stats.failedRequests + 1
        Log.ErrorFormat("HTTP请求失败: %s, 错误: %s", request.id, errorStr)
    else
        request.status = HttpRequestStatus.Completed
        self.stats.completedRequests = self.stats.completedRequests + 1
        Log.InfoFormat("HTTP请求完成: %s, 状态码: %d", request.id, httpCode)
    end

    -- 从运行列表中移除
    self.runningRequests[request.id] = nil
    
    -- 执行回调
    local callback = self.callbackMap[request.id]
    if callback then
        callback(errorStr, httpCode, responseHeaders, responseBody)
        self.callbackMap[request.id] = nil
    end
    
    -- 处理队列中的下一个请求
    self:ProcessQueue()
end

--- 处理队列
function HttpSystem:ProcessQueue()
    if #self.requestQueue == 0 then
        return
    end
    
    if #self.runningRequests >= self.config.maxConcurrentRequests then
        return
    end
    
    -- 取出队列中的第一个请求
    local request = table.remove(self.requestQueue, 1)
    self.stats.queuedRequests = self.stats.queuedRequests - 1
    
    self:ExecuteRequest(request)
end

--- 从路径中提取文件名
---@param filePath string 文件路径
---@return string fileName 文件名
function HttpSystem:ExtractFileNameFromPath(filePath)
    return filePath:match("([^/]+)$") or filePath
end

return HttpSystem