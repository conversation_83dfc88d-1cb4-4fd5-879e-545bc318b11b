---@class SocialActionSystem:SystemBase
local SocialActionSystem = DefineClass("SocialActionSystem",SystemBase)


--服务器下发的actiontype
SocialActionSystem.ESocialActionType = {
    NONE = 0,
    SINGLE = 1,
    DUAL = 2,
}

--避免男女模型的magic num
SocialActionSystem.ESex = {
    MALE = 0,
    FEMALE = 1,
}

function SocialActionSystem:CheckActionIDLocked(actionID)
    --if Game.TableData.GetSocialActionDataRow(actionID).defaultUnlock == true then return false end
    return self.model.actionInfoList[actionID].bLocked
end

function SocialActionSystem:CheckActionIDCollected(actionID)
    return self.model.actionInfoList[actionID].bCollected
end

function SocialActionSystem:CheckActionIDCanDo(actionID)
    return true
end

---@获取标签信息
function SocialActionSystem:GetTabDataList()
    return self.model.tabDataList
end

---@获取具体的动作信息
function SocialActionSystem:GetSocialActionDataList()
    local outputlist = {}
    for k,v in pairs(self.model.tabDataList) do
        outputlist[k] = {}
    end

    for k,v in pairs(self.socialActionDataList) do
        table.insert(outputlist[v.class],v)
    end

    return outputlist
end

---@从服务端获取动作信息
function SocialActionSystem:getSocialActionInfo()
    return self.model.actionInfoList
end

---@获取收藏的ID
function SocialActionSystem:GetCollectedActionIDList()
    return self.model.collectActionIDList
end

---@获取收藏的动作
function SocialActionSystem:GetCollectedActionList()
    local outputCollectedActionList = {}

    for _,action in pairs(self.socialActionDataList) do
        if self.model.collectActionIDList[action.ID] then 
            table.insert(outputCollectedActionList, action) 
        end
    end

    return outputCollectedActionList
end

---@获取进入的标签页
function SocialActionSystem:GetEnterTabListIndex()
    if next(self.model.collectActionIDList)  then
        return #self.model.tabDataList
    end
    return self.tabListIndex or 1
end

function SocialActionSystem:ChangeCollected(list)
    if not list then return end
    for action,v in pairs(self.model.actionInfoList) do
        if list[action] then 
            if v.bCollected == false then
                self.sender:ReqCollectSocialAction(action)
                v.bCollected = true
            end
        else
            if v.bCollected == true then
                self.sender:ReqCancelCollectSocialAction(action)
                v.bCollected = false
            end
        end
    end
	self:OnSocialActionCollectedChange()
end


--region 使用动作逻辑 
---@使用动作
function SocialActionSystem:ReqPlayAction(ID, player)
    -- todo: 等后端接入condition系统，查看是否可以满足Play条件
    --if not self:CheckActionIDCanDo(ID, player) then
    --    local useConditionReminder = Game.TableData.GetSocialActionDataRow(ID).useConditionReminder
    --    Game.ReminderManager:AddReminderById(useConditionReminder, {})  --luacheck: ignore
    --    return
    --end
    if not Game.me then return end
    if Game.me:SCCanExecute(Enum.EStateConflictAction.SocialActionSystem) == Enum.EStateConflictType.BLOCK then 
        return
    end
    
    -- 临时状态拒绝
    -- 有马车状态
    if Game.me.VehicleEntID and Game.me.VehicleEntID ~= "" and Game.me.VehicleSeatIndex > 0 then
        local info = Game.TableData.GetSocialActionDataRow(ID)
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SOCIALACTION_FAILURE_POSITION, {{info.name}})  --luacheck: ignore
        return
    end
    if Game.me.IsDead then
        return
    end
    
    self.inviteSocialActionID = ID
    self.invitePlayer = player

    -- 先尝试融合下
    local socialActionData = Game.TableData.GetSocialActionDataRow(ID)
    local bBlendSuccess = Game.me:TryUpperBlendPerformanceAction(socialActionData and socialActionData.ActionBaseID[1] or 0)

    -- 融合失败,走下面的逻辑
    if not bBlendSuccess then
        -- 如果当前有正在进行的动作
        if Game.me:BeUnderAction() then
            local info = Game.TableData.GetSocialActionDataRow(Game.me.CurrentSocialAction.ActionID)
            if info.interruptLoopPlayNew then
                self.sender:ReqCastSocialAction(ID,player)
            else
                self:ReqCancelCurrentSocialAction()
            end
        else
            self.sender:ReqCastSocialAction(ID,player)
        end
    end
end

function SocialActionSystem:ClearInviteInfo()
	self.inviteSocialActionID = nil
	self.invitePlayer = nil
end

--- 单人执行动作
function SocialActionSystem:PlaySingleAction(entity, ActionID, new, old)
    local PerformanceActionID = self:GetPerformanceActionID(entity.CurrentSocialAction.Caster, entity.CurrentSocialAction.Target , ActionID)
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(PerformanceActionID)
    if not ActionInfo then return end

	if entity == Game.me then
		Game.FateGiftSystem:OnActionPlayed(ActionID)
	end
    
    -- todo: 时间
    --local hasPassedTime = _G._now()/1000 - new
    --Log.Debug("SASystem: Single Action hasPassedTime : " .. hasPassedTime)

    if entity.CurDirectorID then
        entity:FinishSinglePerformance(true)
    end

    local DirectorID = entity:RequestSinglePerformance(PerformanceActionID)
    if DirectorID ~= false and DirectorID ~= nil then
        entity:StartListenDirectorFirstLoopFinish(self, "OnSocialActionFinishLoop")
    end
end

--- 双人执行动作
function SocialActionSystem:PlayDualAction(entity, ActionID, new, old)
    -- 处理弹窗
    self:RemoveSocialActionApplicaitonTip()
	if entity == Game.me then
		Game.FateGiftSystem:OnActionPlayed(ActionID)
	end

	-- 由于是双人, 在进出AOI/上下线时, 存在先后问题，所以这里主从双方都需要跑一次这个逻辑
	local Target = Game.EntityManager:getEntity(entity.CurrentSocialAction.Target)
	local Caster = Game.EntityManager:getEntity(entity.CurrentSocialAction.Caster)
	if not Target or not Caster or not Target.bInWorld or not Caster.bInWorld then
		return
	end
	
    local PerformanceActionID = self:GetPerformanceActionID(entity.CurrentSocialAction.Caster, entity.CurrentSocialAction.Target, ActionID)
    -- todo: 时间
    --local hasPassedTime = _G._now()/1000 - new
	
    if entity.CurDirectorID then
        entity:FinishSinglePerformance(true)
    end

    -- 填三个ID且出现女邀请男时，需要交换一下双方动作
    local socialActionInfo = Game.TableData.GetSocialActionDataRow(ActionID)
    if #(socialActionInfo.ActionBaseID) == 3 and
            self:FindActionEntitySex(Caster) == SocialActionSystem.ESex.FEMALE and self:FindActionEntitySex(Target) == SocialActionSystem.ESex.MALE then
        local DirectorID = Target:RequestDualPerformance(Caster, PerformanceActionID)
        if DirectorID ~= false and DirectorID ~= nil then
            Target:StartListenDirectorFirstLoopFinish(self, "OnSocialActionFinishLoop")
        end
    else
        local DirectorID = Caster:RequestDualPerformance(Target, PerformanceActionID)
        if DirectorID ~= false and DirectorID ~= nil then
            Caster:StartListenDirectorFirstLoopFinish(self, "OnSocialActionFinishLoop")
        end
    end

end

---半身融合
---@public
---@param entity ActorBase
---@param actionID number ref:SocialActionData
function SocialActionSystem:UpperBlendSocialAction(entity, actionID)
    local performanceActionID = self:GetPerformanceActionID(entity.CurrentSocialAction.Caster, entity.CurrentSocialAction.Target , actionID)
    if not performanceActionID then
        Log.Warning("[OnSocialActionUpperBlend] get performanceActionID failed")
        return
    end

    local casterEntity = Game.EntityManager:getEntity(entity.CurrentSocialAction.Caster)
    if not casterEntity then
        Log.WarningFormat("[OnSocialActionUpperBlend] get caster %s failed", entity.CurrentSocialAction.Caster)
        return
    end

    casterEntity:TryUpperBlendPerformanceAction(performanceActionID)
end

function SocialActionSystem:OnSocialActionFinishLoop(FinishEntity)
    if FinishEntity.CurDirectorID ~= Game.me.CurDirectorID then
        return
    end
    local CurrentSocialAction = Game.me.CurrentSocialAction
    local ActionID = CurrentSocialAction.ActionID
    local SAInfo = Game.TableData.GetSocialActionDataRow(ActionID)
    if SAInfo and SAInfo.autoCancel then
        self.sender:ReqCancelCastSocialAction(CurrentSocialAction.ActionID)
    end
    FinishEntity:StopListenDirectorFirstLoopFinish(self, "OnSocialActionFinishLoop")
end

---请求结束动作
function SocialActionSystem:ReqCancelCurrentSocialAction()
    if not Game.me then return end
    if not Game.me:BeUnderAction() then return end

    local CurrentSocialAction = Game.me.CurrentSocialAction
    local PerformanceActionID = self:GetPerformanceActionID(CurrentSocialAction.Caster, CurrentSocialAction.Target, CurrentSocialAction.ActionID)
    local ActionInfo = Game.TableData.GetPerformanceActionDataRow(PerformanceActionID)
    if not ActionInfo then return end

    local bFullBody = true
    for _, PerformanceActionData in ksbcpairs(ActionInfo) do
        if PerformanceActionData.Type == Enum.EPerformanceActionType.Half then
            bFullBody = false
            break
        end
    end

    if not bFullBody then
        return
    end

    self.sender:ReqCancelCastSocialAction(CurrentSocialAction.ActionID)
end

function SocialActionSystem:ReqCancelCastSocialAction()
	local actionID = Game.me.CurrentSocialAction.ActionID
	self.sender:ReqCancelCastSocialAction(actionID)
	return true, true
end

function SocialActionSystem:CheckSocialActionSystemState()
	local actionID = Game.me.CurrentSocialAction.ActionID
	if not actionID or table.contains(self.model.noConflictActionID, actionID) then
		return false
	end
	return true
end

--- 收到服务器消息结束动作时触发
function SocialActionSystem:OnInterruptAction(entity, ActionID, new, old)
    if not ActionID or ActionID == 0 then return end
    
	self:ClearInviteInfo()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_REFRESH_SOCIAL_ACTION_PANEL)
	
    if old == SocialActionSystem.ESocialActionType.DUAL then
        -- 双人动作
        local Caster = Game.EntityManager:getEntity(entity.CurrentSocialAction.Caster)
        local Target = Game.EntityManager:getEntity(entity.CurrentSocialAction.Target)
        if not Target or not Caster then
            return
        end
        -- 由于是双人, 这里主从端都会到这里, 做个重入确保流程正确
        Log.Info("SocialActionSystem FinishDualPerformance, can ignore warning check in ArenaSystem")
        Caster:FinishDualPerformance(Target)
        return
    else
        -- 单人动作
		if entity and entity.CurDirectorID then
			entity:FinishSinglePerformance()
		end
		Game.EventSystem:Publish(_G.EEventTypes.SELF_SOCIAL_ACTION_AUTOSTOP, ActionID)
    end
end

--endregion


--region:Invite & Be Invited
function SocialActionSystem:AddSocialActionInviteTip(actionID)
    local actionInfoData = Game.TableData.GetSocialActionDataRow(actionID)
    if actionInfoData and actionInfoData.autoAccept then
        return
    else
        if self.inviteTipsGid then
            Game.PopTipsSystem:RemoveApplyInviteListByGID(self.inviteTipsGid)
        end

        self.inviteTipsGid = Game.PopTipsSystem:OnAddAddApplyInviteListElem({
            Type = Enum.EElasticStripData.SendSocialAction,
            OperName = "等待对方接受邀请",
        })
    end
end

function SocialActionSystem:RemoveSocialActionInviteTip(IsAccept)
    if self.inviteTipsGid then
        Game.PopTipsSystem:RemoveApplyInviteListByGID(self.inviteTipsGid)
        self.inviteTipsGid = nil
    end
	if IsAccept == false then
		self:ClearInviteInfo()
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_REFRESH_SOCIAL_ACTION_PANEL)
	end
end

function SocialActionSystem:AddSocialActionApplicaitonTip(actionID, player)
    local actionInfoData = Game.TableData.GetSocialActionDataRow(actionID)
    if actionInfoData.autoAccept then
        self.sender:ReqHandleQueryCastSocialAction(actionID, player, true)
    else
        local entity = Game.EntityManager:getEntityWithBrief(player) --需要支持Brief的情况 @hujianglong
        if entity then
            if not self.applicationTipsGidDict then
                self.applicationTipsGidDict = {}
            end
            if self.applicationTipsGidDict[player] then
                Game.PopTipsSystem:RemoveApplyInviteListByGID(self.applicationTipsGidDict[player])
            end
    
            self.applicationTipsGidDict[player] = Game.PopTipsSystem:OnAddAddApplyInviteListElem({
                Type = Enum.EElasticStripData.ReceivedSocialAction,
                OperName = entity.Name,
                -- TimerDuration = 30 * 1000,
                -- OperProfession = applicationInfo.school,
                OperProfession = entity.Profession,
                OperLevel = entity.Level,
                OperID = player,
                OperActionID = actionID,
            })
        end
    end
end

function SocialActionSystem:RemoveSocialActionApplicaitonTip()
    if self.applicationTipsGidDict then
        for key,value in pairs(self.applicationTipsGidDict) do
            Game.PopTipsSystem:RemoveApplyInviteListByGID(value)
        end
        self.applicationTipsGidDict = {}
    end
end
--endregion

--region: 服务器
function SocialActionSystem:OnMsgSyncSocialAction(unlockList, collectedList)
    self.model.unlockList = unlockList
    self.model.collectedList = collectedList
    self.model:CalcCollectedAndUnLockedActionIDList()
end

function SocialActionSystem:OnMsgUnlockSocialAction(list)
    -- todo
end

function SocialActionSystem:OnMsgQueryCastSocialAction(caster, actionID, handleDeadline)
    Log.Debug("OnMsgQueryCastSocialAction: " .. caster .. " " .. actionID .. " " .. handleDeadline)
    self:AddSocialActionApplicaitonTip(actionID, caster)
    -- self.sender:ReqHandleQueryCastSocialAction(actionID, caster)
end
--endregion


---@private
function SocialActionSystem:onCtor()
    self.model = nil
    self.sender = nil
end
---@打开SocialAction页面
function SocialActionSystem:OpenSocialActionPanel(enterIndex,preSelectPlayer)
    if not UI.IsShow(UIPanelConfig.HUDSocialAction_Panel) then
        Game.NewUIManager:OpenPanel(UIPanelConfig.HUDSocialAction_Panel,enterIndex,preSelectPlayer) 
    end
end
---@关闭SocialAction页面
function SocialActionSystem:CloseSocialActionPanelWithOutSave()
    if UI.IsShow(UIPanelConfig.HUDSocialAction_Panel) then
		Game.NewUIManager:ClosePanel(UIPanelConfig.HUDSocialAction_Panel)
    end
end

---@通用动作item排序
function SocialActionSystem:SortActionItem(List)
    table.sort(
        List, function(a,b) 
            local aLocked = self:CheckActionIDLocked(a.ID) 
            local bLocked = self:CheckActionIDLocked(a.ID)
            if aLocked ~= bLocked then 
                if not aLocked then return true
                    else return false end
            elseif a.SortID ~= b.SortID then
                return a.SortID < b.SortID
            else 
                return a.ID < b.ID
            end
        end
    )
    return List
end

---@模糊搜索
function SocialActionSystem:GetSearchedList(List, Keyword)
    local newList = {}
    local partten = ""
    for i = 1, utf8.len(Keyword) do
        local str = utf8.sub(Keyword, i, i + 1)
        partten = partten .. str .. "(.*)"
    end
    for key, value in pairs(List) do
        if string.find(value.name, partten) then
            table.insert(newList, value)
        end
    end
    return newList
end

---@private
function SocialActionSystem:onInit()
    ---@type SocialActionModel
    self.model = kg_require("Gameplay.LogicSystem.SocialAction.SocialActionModel").new(false, true)
    ---@type SocialActionSender
    self.sender = kg_require("Gameplay.LogicSystem.SocialAction.SocialActionSender").new()

    --记录之前页面
    self.tabListIndex = nil

    --临时处理：打开耦合的互动邀请
    self.bOpenInteractiveInvite = true
    --self.canInvite = true --邀请CD

    --region 收藏相关
    --是否在收藏页面
    self.bInCollectStatus = false
    --endregion
	self.model:InitTabData()

    self.socialActionDataList = {}
    --暂留本地记录
    self.model.actionInfoList = {}
    local ActionDataTable = Game.TableData.GetSocialActionDataTable() or {}
    for k,v in ksbcpairs(ActionDataTable) do 
        self.socialActionDataList[#self.socialActionDataList+1] = {ID=k, name = v.name, type = v.type, icon = v.icon, actionTag = v.actionTag, class = v.class}
        self.model.actionInfoList[k] = {bLocked = false,bCollected = false}
    end
    self.socialActionDataList = self:SortActionItem(self.socialActionDataList)

    -- 计算哪些收藏的actionID
    Game.GlobalEventSystem:AddListener(EEventTypesV2.MODULE_LOCK_CHANGE, "ReqQuerySocialAction", self)

    self.model:CalcCollectedAndUnLockedActionIDList()

    -- PlayAction
    self.inviteSocialActionID = nil   --邀请动作
    self.invitePlayer = nil    --邀请人
    self.inviteTipsGid = nil   --邀请弹条的gid
    self.applicationTipsGidDict = nil --被邀请弹条的gid
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, "ReqCancelCurrentSocialAction", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_MOVE_INPUT, "ReqCancelCurrentSocialAction", self)
    ---redpoint
    Game.EventSystem:AddListener(_G.EEventTypes.GAME_MAINPALYER_LOGIN, self, self.AddRedPoint)
end

---@private
function SocialActionSystem:onUnInit()
    self.tabListIndex = nil
    self.inviteSocialActionID = nil
    self.invitePlayer = nil
    self.inviteTipsGid = nil
    self.applicationTipsGidDict = nil
end

function SocialActionSystem:ReqQuerySocialAction()
    self.sender:ReqQuerySocialAction()
end

function SocialActionSystem:AddRedPoint()
    Game.SocialActionSystem.sender:ReqQuerySocialAction()
    local tabData = self:GetTabDataList()
    for _, value in pairs(tabData) do
        Game.RedPointSystem:InvokeNode("SocialActionTabList", value.ID)
    end
    
    --回调后期改为用服务端的OnceRedPoint
    local SocialActionData = self.socialActionDataList -- todo change
    for _, value in pairs(SocialActionData) do
        Game.RedPointSystem:InvokeNode("SocialActionItemList",value.class, value.ID)
    end
end


---- 红点 ----
function SocialActionSystem:AddOnceRedPoint(ID)
    local info = Game.TableData.GetSocialActionDataRow(ID)
    if info then
        Game.RedPointSystem:MarkOnceRedPoint("SocialActionItemList", info.class, ID)
    end
end

function SocialActionSystem:DeleteOnceRedPoint(ID)
    local info = Game.TableData.GetSocialActionDataRow(ID)
    if info then
        Game.RedPointSystem:DeleteOnceRedPoint("SocialActionItemList", info.class, ID)
    end
end


--- 应对男女不同情况所读取的动作
SocialActionSystem.SexActionMap = {
    ["3_0_1"] = 1,
    ["3_1_0"] = 1,
    ["3_0_0"] = 2,
    ["3_1_1"] = 3,
    ["4_0_1"] = 1,
    ["4_0_0"] = 2,
    ["4_1_1"] = 3,
    ["4_1_0"] = 4,
}

function SocialActionSystem:FindActionEntitySex(entity)
    if not entity then
        Log.Error("SocialActionSystem:CheckEntitySex entity is nil")
        return nil
    end
    return entity.Sex
end

function SocialActionSystem:GetPerformanceActionID(entityAEid, entityBEid, ActionID)
    local entityA = Game.EntityManager:getEntity(entityAEid)
    local entityB = Game.EntityManager:getEntity(entityBEid)
    local SAInfo = Game.TableData.GetSocialActionDataRow(ActionID)
    if not SAInfo then return end
    if not entityA then return end
    if #SAInfo.ActionBaseID == 1 or not entityB then
        return SAInfo.ActionBaseID[1]
    else
        local sexA = entityA.Sex
        local sexB = entityB.Sex
        local sexStr = #SAInfo.ActionBaseID .."_".. sexA .. "_" .. sexB
        return SAInfo.ActionBaseID[self.SexActionMap[sexStr]]
    end
end

function SocialActionSystem:OnSocialActionLockChange()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SOCIAL_ACTION_LOCK_CHANGED)
	self.model:CalcCollectedAndUnLockedActionIDList()
end

function SocialActionSystem:OnSocialActionCollectedChange()
	self.model:CalcCollectedAndUnLockedActionIDList()
end

return SocialActionSystem