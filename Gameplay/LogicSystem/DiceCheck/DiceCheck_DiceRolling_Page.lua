local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local DiceCheck_Points = kg_require("Gameplay.LogicSystem.DiceCheck.DiceCheck_Points")
local DiceCheck_SuccessFailure_Page = kg_require("Gameplay.LogicSystem.DiceCheck.DiceCheck_SuccessFailure_Page")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class DiceCheck_DiceRolling_Page : UIComponent
---@field view DiceCheck_DiceRolling_PageBlueprint
local DiceCheck_DiceRolling_Page = DefineClass("DiceCheck_DiceRolling_Page", UIComponent)

local Const = kg_require("Shared.Const")

DiceCheck_DiceRolling_Page.eventBindMap = {
	[EEventTypesV2.ON_DICE_CHECK_ROLL_FINISH] = "OnGetDiceCheckRollResult",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DiceCheck_DiceRolling_Page:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DiceCheck_DiceRolling_Page:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DiceCheck_DiceRolling_Page:InitUIComponent()
    ---@type UISimpleList : DiceCheck_Dice_Item
    self.DiceListCom = self:CreateComponent(self.view.DiceList, UISimpleList)
	---@type DiceCheck_Points
	self.WBP_DiceCheck_PointsCom = self:CreateComponent(self.view.WBP_DiceCheck_Points, DiceCheck_Points)
	---@type DiceCheck_SuccessFailure_Page
	self.WBP_DiceCheck_SuccessFailure_PageCom = self:CreateComponent(self.view.WBP_DiceCheck_SuccessFailure_Page, DiceCheck_SuccessFailure_Page)
end

---UI事件在这里注册，此处为自动生成
function DiceCheck_DiceRolling_Page:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DiceCheck_DiceRolling_Page:InitUIView()
	self.userWidget:BP_SetState(false)
	self.userWidget:BP_SetNumber(false)
end

---组件刷新统一入口
---@param previewInfo DiceCheckPreviewInfo 骰子预览信息
function DiceCheck_DiceRolling_Page:Refresh(previewInfo)
	self.view.Text_Number:SetText(tostring(previewInfo.targetCount)) -- 目标点数文本
	self:OnPlayerClickDiceBefore()
end

--region 普通检定流程

---玩家点击骰子之前
---@param previewInfo DiceCheckPreviewInfo 骰子预览信息
function DiceCheck_DiceRolling_Page:OnPlayerClickDiceBefore(previewInfo)
	-- 获取要投掷的骰子列表
	local diceList = self:getDiceList(previewInfo)
	-- 根据骰子总数设置骰子位置
	local diceCount = #diceList
	if diceCount > 0 and diceCount <= Const.MAX_DICE_COUNT then
		self.userWidget:BP_SetDiceLocation(#diceList - 1)
	end
	-- 刷新骰子列表
	local datas = {}
	for i = 1, #diceList do
		local data = {
			style = diceList[i] or Const.DICE_TYPE.NORMAL, -- 骰子样式
			num = 0, -- 初始数字为0
		}
		table.insert(datas, data)
	end
	self.DiceListCom:Refresh(datas)
end

---获取要投掷的骰子列表，先添加特殊骰子，再添加基础骰子
---[这部分与服务端写法保持一致]
---@param previewInfo DiceCheckPreviewInfo 骰子预览信息
function DiceCheck_DiceRolling_Page:getDiceList(previewInfo)
	local diceList = {}
	for diceType, bonusCount in pairs(previewInfo.bonusDiceCount) do
		for i = 1, bonusCount do
			table.insert(diceList, diceType)
		end
	end
	-- 添加基础骰子数量
	if #diceList < Const.MAX_DICE_COUNT then
		for i = 1, previewInfo.defaultDiceCount do
			table.insert(diceList, Const.DICE_TYPE.NORMAL)
			if #diceList >= Const.MAX_DICE_COUNT then
				break
			end
		end
	end
	return diceList
end

---此处为自动生成 
---点击骰子进行检定
---@param index number
---@param data table
function DiceCheck_DiceRolling_Page:on_DiceListCom_ItemClicked(index, data)
	Game.DiceCheckSystem:ReqDiceCheckRoll(id)
	-- TODO 播放骰子动效
end

---当收到服务器传来的骰子检定结果时
---@param rollResultInfo DiceCheckRollResultInfo 骰子检定结果信息
function DiceCheck_DiceRolling_Page:OnGetDiceCheckRollResult(rollResultInfo)
	-- TODO 动效一
	-- 显示检定结果，设置初始显示的骰子
	self:ShowDiceCheckRollResultOrigin(rollResultInfo)
	-- TODO 动效二
	-- 显示检定结果，设置最终显示的骰子
	self:ShowDiceCheckRollResultTotal(rollResultInfo)
	-- TODO 动效三
	-- 显示最终点数和成功或失败
	self:ShowFinallyPoints(rollResultInfo)
end

---显示检定结果，设置初始显示的骰子
---@param rollResultInfo DiceCheckRollResultInfo 骰子检定结果信息
function DiceCheck_DiceRolling_Page:ShowDiceCheckRollResultOrigin(rollResultInfo)
	local diceList = rollResultInfo.rollResult or {}
	local datas = {}
	local points = 0
	for i = 1, #diceList do
		local data = {
			num = diceList[i].origin or 0,
			style = diceList[i].type or 0,
		}
		table.insert(datas, data)
		points = points + (diceList[i].origin or 0)
	end
	self.DiceListCom:Refresh(datas)
	self:setDiceCheckPoints(points) -- 投出来的点数
end

---显示检定结果，设置最终显示的骰子
---@param rollResultInfo DiceCheckRollResultInfo 骰子检定结果信息
function DiceCheck_DiceRolling_Page:ShowDiceCheckRollResultTotal(rollResultInfo)
	local diceList = rollResultInfo.rollResult or {}
	local datas = {}
	local points = 0
	for i = 1, #diceList do
		local data = {
			num = diceList[i].total or 0,
			style = diceList[i].type or 0,
		}
		table.insert(datas, data)
		points = points + (diceList[i].total or 0)
	end
	self.DiceListCom:Refresh(datas)
	self:setDiceCheckPoints(points) -- 投出来的点数 + 特殊骰子加成的点数
end


---@param rollResultInfo DiceCheckRollResultInfo 骰子检定结果信息
function DiceCheck_DiceRolling_Page:ShowFinallyPoints(rollResultInfo)
	self:setDiceCheckPoints(rollResultInfo.total) -- 投出来的点数 + 特殊骰子加成的点数 + 天赋的点数
	self:setDiceCheckSuccess(rollResultInfo.isSuccess)
end

--endregion 普通检定流程

--- 设置骰子检定结果点数
function DiceCheck_DiceRolling_Page:setDiceCheckPoints(points)
	self.userWidget:BP_SetNumber(true)
	self.WBP_DiceCheck_PointsCom:Refresh(points)
end

--- 设置骰子检定成功或失败
function DiceCheck_DiceRolling_Page:setDiceCheckSuccess(bSuccess)
	self.userWidget:BP_SetState(true)
	self.WBP_DiceCheck_SuccessFailure_PageCom:Refresh(bSuccess)
end

return DiceCheck_DiceRolling_Page
