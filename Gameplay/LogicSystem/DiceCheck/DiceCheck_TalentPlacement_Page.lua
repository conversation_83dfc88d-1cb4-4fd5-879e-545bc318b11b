local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class DiceCheck_TalentPlacement_Page : UIComponent
---@field view DiceCheck_TalentPlacement_PageBlueprint
local DiceCheck_TalentPlacement_Page = DefineClass("DiceCheck_TalentPlacement_Page", UIComponent)

local TriggerUtils = kg_require("Shared.Utils.TriggerUtils")
local Const = kg_require("Shared.Const")

DiceCheck_TalentPlacement_Page.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DiceCheck_TalentPlacement_Page:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DiceCheck_TalentPlacement_Page:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DiceCheck_TalentPlacement_Page:InitUIComponent()
	---@type UISimpleList : DiceCheck_Talent_Item
	self.TalentDiceListCom = self:CreateComponent(self.view.TalentList, UISimpleList)
	---@type UISimpleList : DiceCheck_Talent_Item
	self.TalentPointListCom = self:CreateComponent(self.view.TalentList, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function DiceCheck_TalentPlacement_Page:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DiceCheck_TalentPlacement_Page:InitUIView()
end

---组件刷新统一入口
---@param id number 检定ID
function DiceCheck_TalentPlacement_Page:Refresh(id)
	local diceCheckData = Game.TableData.GetDiceCheckDataRow(id)
	local resultExtraDiceList = {}
	local resultExtraPointList = {}
	resultExtraDiceList, resultExtraPointList = self:diceCheckGetBonusByBonusID(diceCheckData.Bonus)
	-- 填充天赋列表, 左边四个是额外骰子，右边四个是额外点数
	self:RefreshTalentDiceList(resultExtraDiceList)
	self:RefreshTalentPointList(resultExtraPointList)
end

---刷新天赋骰子列表
function DiceCheck_TalentPlacement_Page:RefreshTalentDiceList(resultExtraDiceList)
	if not resultExtraDiceList or next(resultExtraDiceList) == nil then
		return
	end
	
	local datas = {}
	for _, bonusData in pairs(resultExtraDiceList) do
		if #datas > 4 then
			Log.DebugWarning("[DiceCheck_TalentPlacement_Page]:RefreshTalentDiceList", "超过4个天赋，无法全部显示")
			break
		end
		table.insert(datas, bonusData)
	end
	
	self.TalentDiceListCom:Refresh(datas)
end

---刷新天赋点数列表
function DiceCheck_TalentPlacement_Page:RefreshTalentPointList(resultExtraPointList)
	if not resultExtraPointList or next(resultExtraPointList) == nil then
		return
	end
	
	local datas = {}
	for _, bonusData in pairs(resultExtraPointList) do
		if #datas > 4 then
			Log.DebugWarning("[DiceCheck_TalentPlacement_Page]:RefreshTalentPointList", "超过4个天赋，无法全部显示")
			break
		end
		table.insert(datas, bonusData)
	end
	
	self.TalentPointListCom:Refresh(datas)
end

---获取加成数据
---@param bonusList table 加成ID列表
---@return table|nil resultExtraDiceList 额外骰子列表
---@return table|nil resultExtraPointList 额外点数列表
function DiceCheck_TalentPlacement_Page:diceCheckGetBonusByBonusList(bonusList)
	if not bonusList or #bonusList == 0 then
		return
	end

	local resultExtraDiceList = {}
	local resultExtraPointList = {}

	for _, bonusID in ipairs(bonusList) do
		local bonusDatas = Game.TableData.GetDiceCheckBonusData(bonusID)
		if bonusDatas then
			for _, bonusData in ipairs(bonusDatas) do
				self:processBonusData(bonusID, bonusData, resultExtraDiceList, resultExtraPointList)
			end
		end
	end

	return resultExtraDiceList, resultExtraPointList
end

--- 判断该加成是否生效
function DiceCheck_TalentPlacement_Page:processBonusData(bonusID, bonusData, resultExtraDiceList, resultExtraPointList)
	if bonusData.Key ~= bonusID then
		return
	end

	-- 判断是否生效
	local isActive = TriggerUtils.CanTriggerCompleted(
		Game.me,
		Enum.TriggerModuleType.DiceCheckBonus,
		bonusData.ID,
		bonusData.ConditionID
	)

	if not isActive then
		return
	end

	local params = {
		type = bonusData.Type,		-- 加成类型
		value = bonusData.Value,	-- 加成数值
		name = bonusData.Name,		-- 天赋名称
	}

	if bonusData.Type == Const.DICE_CHECK_BONUS_TYPE.EXTRA_DICE then
		-- 当加成类型为额外骰子时，需要额外指定样式
		params.style = bonusData.DiceType
		local exist = resultExtraDiceList[bonusData.Key]
		if not exist or (exist.Level or 0) < bonusData.Level then	-- 生效等级高的覆盖等级低的
			params.Level = bonusData.Level
			resultExtraDiceList[bonusData.Key] = params
		end

	elseif bonusData.Type == Const.DICE_CHECK_BONUS_TYPE.EXTRA_POINT then
		local exist = resultExtraPointList[bonusData.Key]
		if not exist or (exist.Level or 0) < bonusData.Level then
			params.Level = bonusData.Level
			resultExtraPointList[bonusData.Key] = params
		end
	end
end




return DiceCheck_TalentPlacement_Page
