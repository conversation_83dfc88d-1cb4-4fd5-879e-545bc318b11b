local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class DiceCheck_Talent_Item : UIListItem
---@field view DiceCheck_Talent_ItemBlueprint
local DiceCheck_Talent_Item = DefineClass("DiceCheck_Talent_Item", UIListItem)

local Const = kg_require("Shared.Const")

--- 因为交互设计时没有按顺序排列，这里做一个映射
DiceCheck_Talent_Item.StyleMapping = {
	[Const.DICE_TYPE.NORMAL] = nil, -- 普通骰子 白
	[Const.DICE_TYPE.CHARM] = 3, 	-- 魅力骰子 紫
	[Const.DICE_TYPE.THEFT] = 1, 	-- 盗窃骰子 蓝
	[Const.DICE_TYPE.HARVEST] = 2, 	-- 丰收骰子 绿
	[Const.DICE_TYPE.ALCHEMY] = 0, 	-- 炼金骰子 红
}

DiceCheck_Talent_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DiceCheck_Talent_Item:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

---初始化数据
function DiceCheck_Talent_Item:InitUIData()
	---@type number 天赋加成类型
	self.bonusType = nil
end

--- UI组件初始化，此处为自动生成
function DiceCheck_Talent_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DiceCheck_Talent_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DiceCheck_Talent_Item:InitUIView()
	self:SetTagDisable(true)
	self:SetIconVisible(false)
	self:SetBonusValue(0)
	self:SetTalentName("")
end

---面板打开的时候触发
---@param params table|nil
---@param params.type number|DiceCheck_Talent_Item.BonusType 加成类型	--TODO 放到Const里
---@param params.style number|Const.DICE_TYPE 加成类型样式
---@param params.value number 加成数值
---@param params.name number 天赋名称
function DiceCheck_Talent_Item:OnRefresh(params)
	if not params then
		return
	end
	self.bonusType = params.type
	self:SetTalentName(params.name or "")
	if params.type == Const.DICE_CHECK_BONUS_TYPE.DiceCount then
		-- 当加成类型为额外骰子时
		self:SetTagDisable(false)
		self:SetIconVisible(false)
		self:SetBonusValue(params.value)
		self:SetBonusDiceType(params.style)
	elseif params.type == Const.DICE_CHECK_BONUS_TYPE.Target then
		-- 当加成类型为额外点数时
		self:SetTagDisable(true)
		self:SetIconVisible(false)
		self:SetBonusValue(params.value)
	else
		self:SetTagDisable(false)
		self:SetIconVisible(false)
		self:SetBonusValue(0)
	end
end

--- 设置天赋加成骰子数量时的样式
---@param style Const.DICE_TYPE  炼金 0, 偷盗 1, 丰收 2, 魅惑 3
function DiceCheck_Talent_Item:SetBonusDiceType(style)
	local index = DiceCheck_Talent_Item.StyleMapping[style]
	if self.userWidget.BP_SetIconAdd and index then
		self.userWidget:BP_SetIconAdd(index)
	end
end

--- 设置天赋图标显隐
function DiceCheck_Talent_Item:SetIconVisible(visible)
	if self.userWidget.BP_SetIcon then
		self.userWidget:BP_SetIcon(visible)
	end
end

--- 设置天赋加成标签显隐
function DiceCheck_Talent_Item:SetTagDisable(disable)
	if self.userWidget.BP_SetTag then
		self.userWidget:BP_SetTag(disable)
	end
end

--- 设置天赋名称
function DiceCheck_Talent_Item:SetTalentName(name)
	self.view.Text_Triangle_Name:SetText(name)
end

--- 设置天赋加成数值
function DiceCheck_Talent_Item:SetBonusValue(value)
	value = tonumber(value)

	if value > 0 then
		self.view.Text_Add:SetText(string.format("+%d", value))
	elseif value < 0 then
		self.view.Text_Add:SetText(string.format("%d", value))
	else
		self.view.Text_Add:SetText("")
	end
	
	if self.bonusType and self.bonusType == Const.DICE_CHECK_BONUS_TYPE.DiceCount then
		self:SetTagDisable(value == 0)
	end
end

return DiceCheck_Talent_Item
