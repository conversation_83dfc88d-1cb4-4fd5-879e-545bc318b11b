local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class DiceCheck_Tips_Panel : UIPanel
---@field view DiceCheck_Tips_PanelBlueprint
local DiceCheck_Tips_Panel = DefineClass("DiceCheck_Tips_Panel", UIPanel)

DiceCheck_Tips_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DiceCheck_Tips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DiceCheck_Tips_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DiceCheck_Tips_Panel:InitUIComponent()
    ---@type UIListView childScript: DiceCheck_Tips_Item
    self.ListViewCom = self:CreateComponent(self.view.ListView, UIListView)
    ---@type UIComBoxFrame
    self.WBP_ComPopupLCom = self:CreateComponent(self.view.WBP_ComPopupL, UIComBoxFrame)
end

---UI事件在这里注册，此处为自动生成
function DiceCheck_Tips_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DiceCheck_Tips_Panel:InitUIView()

end

---面板打开的时候触发
function DiceCheck_Tips_Panel:OnRefresh()
	local diceTypeTable = Game.TableData.GetDiceCheckTypeDataTable()
	local datas = {}
	for _, row in pairs(diceTypeTable) do
		local data = {
			title = row.TipsTitle,
			desc = row.TipsDesc,
		}
		table.insert(datas, data)
	end
	self.ListViewCom:Refresh(datas)
	-- 设置标题
	self.WBP_ComPopupLCom:Refresh("检定说明") -- TODO 改配表
end

return DiceCheck_Tips_Panel
