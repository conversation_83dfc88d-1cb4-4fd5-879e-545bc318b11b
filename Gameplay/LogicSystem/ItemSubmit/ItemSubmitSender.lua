---@class ItemSubmitSender:SystemSenderBase
local ItemSubmitSender = DefineClass("ItemSubmitSender",SystemSenderBase)

--向NPC提交物品后触发交互
function ItemSubmitSender:ReqNpcSPInteract(TargetEID, SubmitButtonId, submit)
    self.Bridge:ReqNpcSPInteract(TargetEID, SubmitButtonId, submit)
end

---小丑相关
function ItemSubmitSender:ReqJokerGiveReward(SubmitID,ItemDict,EntityID)
    self.Bridge:ReqJokerGiveReward(SubmitID,ItemDict,EntityID)
end

function ItemSubmitSender:ReqNpcAskPrice(entityID, price)
    self.Bridge:ReqNpcAskPrice(entityID, price)
end

function ItemSubmitSender:ReqQuestItemSubmitDirect(questID, targetIndex)
	self.Bridge:ReqQuestItemSubmitDirect(questID, targetIndex)
end


function ItemSubmitSender:ReqQuestItemSubmitBranch(questID, targetIndex, optionIndex)
	self.Bridge:ReqQuestItemSubmitBranch(questID, targetIndex, optionIndex)
end


return ItemSubmitSender