local UITempComBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UITempComBtn")
local UIComSwitchBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSwitchBtn")
local ESelectionMode = import("ESelectionMode")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local stringConst = kg_require("Data.Config.StringConst.StringConst")


---@class DanceSetTipsPanel : UIPanel
---@field view DanceSetTipsPanelBlueprint
local DanceSetTipsPanel = DefineClass("DanceSetTipsPanel", UIPanel)

DanceSetTipsPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DanceSetTipsPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

DanceSetTipsPanel.blockCheckBoxList = {
    -- {
    --     name = "DANCE_ACTOR_BLOCK_TYPE_TEAMMATE",
    --     target = Game.DanceSystem.ActorBlockType.Teammate,
    -- },
    -- {
    --     name = "DANCE_ACTOR_BLOCK_TYPE_GUILD",
    --     target = Game.DanceSystem.ActorBlockType.Guild,
    -- },
    -- {
    --     name = "DANCE_ACTOR_BLOCK_TYPE_PASSERBY",
    --     target = Game.DanceSystem.ActorBlockType.Passerby,
    -- },
    -- {
    --     name = "DANCE_ACTOR_BLOCK_TYPE_NPC",
    --     target = Game.DanceSystem.ActorBlockType.Npc,
    -- },
    -- {
    --     name = "DANCE_ACTOR_BLOCK_TYPE_OTHER",
    --     target = Game.DanceSystem.ActorBlockType.Other,
    -- },
    {
        name = "DANCE_ACTOR_BLOCK_TYPE_AOI_PLAYER",
        target = Game.DanceSystem.ActorBlockType.AoiPlayer,
    },
    {
        name = "DANCE_ACTOR_BLOCK_TYPE_NPC",
        target = Game.DanceSystem.ActorBlockType.NPC,
    },
}

---初始化数据
function DanceSetTipsPanel:InitUIData()
    self.checkboxList = {}
end

--- UI组件初始化，此处为自动生成
function DanceSetTipsPanel:InitUIComponent()
    ---@type UIListView childScript: UIComCheckBox
    self.List_CharacterCom = self:CreateComponent(self.view.List_Character, UIListView)
    ---@type UIComSwitchBtn
    self.WBP_ComBtnSwitchSmallCom = self:CreateComponent(self.view.WBP_ComBtnSwitchSmall, UIComSwitchBtn)
    ---@type UITempComBtn
    self.WBP_ComBtnCloseNewCom = self:CreateComponent(self.view.WBP_ComBtnCloseNew, UITempComBtn)
end

-- self:AddUIEvent(self.List_CharacterCom.onGetEntryLuaClass, "on_List_CharacterCom_onGetEntryLuaClass")
---UI事件在这里注册，此处为自动生成
function DanceSetTipsPanel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnSwitchSmallCom.onClickEvent, "on_WBP_ComBtnSwitchSmallCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnCloseNewCom.onClickEvent, "on_WBP_ComBtnCloseNewCom_ClickEvent")
    self:AddUIEvent(self.List_CharacterCom.onItemSelectionChanged, "on_List_CharacterCom_ItemSelectionChanged")
    self:AddUIEvent(self.List_CharacterCom.onGetEntryLuaClass, "on_List_CharacterCom_GetEntryLuaClass")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DanceSetTipsPanel:InitUIView()
    self.List_CharacterCom:SetSelectionMode(ESelectionMode.Multi)
    self.List_CharacterCom:SetMultiModeMaxSelectionMode(Enum.EListMultiModeMaxSelectionMode.CantSelect)
end

---面板打开的时候触发
function DanceSetTipsPanel:OnRefresh(...)
    local currentBlockedTypes = Game.DanceSystem:GetBlockedActorTypes()
    self.checkboxList = {}
    local selectedIndexes = {}
    for idx, data in ipairs(DanceSetTipsPanel.blockCheckBoxList) do
        table.insert(self.checkboxList, {
            name = Game.TableData.GetBasicDanceConstDataRow(data.name)
        })
        if currentBlockedTypes[data.target] then
            table.insert(selectedIndexes, idx)
        end
    end
    self.List_CharacterCom:Refresh(self.checkboxList)

    -- set current selections
    self.List_CharacterCom:SetSelectedItemByIndexs(selectedIndexes, true, true)

    -- set switch state
    self.WBP_ComBtnSwitchSmallCom:SetSwitch(#selectedIndexes == #DanceSetTipsPanel.blockCheckBoxList, false)

    self.view.Text_Title:SetText(stringConst.Get("SHIELD_TITLE"))
    self.view.Text_OpenClose:SetText(stringConst.Get("ROLE_DISPLAY"))

end


--- 此处为自动生成
---@param isOn boolean 
function DanceSetTipsPanel:on_WBP_ComBtnSwitchSmallCom_ClickEvent(isOn)
    self.checkboxList = {}
    local checkboxIndexes = {}
    local selectedTypes = {}
    for idx, data in ipairs(DanceSetTipsPanel.blockCheckBoxList) do
        table.insert(checkboxIndexes, idx)
        if isOn then
            selectedTypes[data.target] = true
        end
    end
    self.List_CharacterCom:SetSelectedItemByIndexs(checkboxIndexes, isOn, true)
    Game.DanceSystem:UpdateBlockedActorTypes(selectedTypes)
end

--- 此处为自动生成
function DanceSetTipsPanel:on_WBP_ComBtnCloseNewCom_ClickEvent()
    self:CloseSelf()
end


-- --- 此处为自动生成
-- ---@param index number
-- ---@return number
-- function DanceSetTipsPanel:on_List_CharacterCom_onGetEntryLuaClass(index, selected)
-- 	return kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBoxItem")
-- end


--- 此处为自动生成
---@param index number
---@param selected bool
function DanceSetTipsPanel:on_List_CharacterCom_ItemSelectionChanged(index, selected)
    local selectedIndexes = self.List_CharacterCom:GetSelectedItemIndexes()
    local selectedTypes = {}
    for _, idx in pairs(selectedIndexes) do
        local target = DanceSetTipsPanel.blockCheckBoxList[idx].target
        selectedTypes[target] = true
    end

    Game.DanceSystem:UpdateBlockedActorTypes(selectedTypes)

    -- set switch state
    self.WBP_ComBtnSwitchSmallCom:SetSwitch(#selectedIndexes == #DanceSetTipsPanel.blockCheckBoxList, false)
end


--- 此处为自动生成
---@param index number
---@return UIComponent
function DanceSetTipsPanel:on_List_CharacterCom_GetEntryLuaClass(index)
    return kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBoxItem")
end

return DanceSetTipsPanel
