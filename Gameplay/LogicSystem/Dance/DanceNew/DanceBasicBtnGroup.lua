local DanceBasicVoiceBtn = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceBasicVoiceBtn")
local DanceBasicListenBtn = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceBasicListenBtn")
local DanceBasicBtn = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceBasicBtn")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@class DanceBasicBtnGroup : UIComponent
---@field view DanceBasicBtnGroupBlueprint
local DanceBasicBtnGroup = DefineClass("DanceBasicBtnGroup", UIComponent)

DanceBasicBtnGroup.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DanceBasicBtnGroup:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DanceBasicBtnGroup:InitUIData()
	self.BtnTypes = {
		chat = 0,
		set = 1,
		hide = 2,
	}

	self.isHideBtnClick = false
end

--- UI组件初始化，此处为自动生成
function DanceBasicBtnGroup:InitUIComponent()
    ---@type DanceBasicVoiceBtn
    self.WBP_VoiceBtnCom = self:CreateComponent(self.view.WBP_VoiceBtn, DanceBasicVoiceBtn)
    ---@type DanceBasicListenBtn
    self.WBP_ListenBtnCom = self:CreateComponent(self.view.WBP_ListenBtn, DanceBasicListenBtn)
    ---@type DanceBasicBtn
    self.WBP_ChatBtnCom = self:CreateComponent(self.view.WBP_ChatBtn, DanceBasicBtn)
    ---@type DanceBasicBtn
    self.WBP_SetBtnCom = self:CreateComponent(self.view.WBP_SetBtn, DanceBasicBtn)
    ---@type DanceBasicBtn
    self.WBP_HideBtnCom = self:CreateComponent(self.view.WBP_HideBtn, DanceBasicBtn)
end

---UI事件在这里注册，此处为自动生成
function DanceBasicBtnGroup:InitUIEvent()
    self:AddUIEvent(self.WBP_ChatBtnCom.onClick, "on_WBP_ChatBtnCom_Click")
    self:AddUIEvent(self.WBP_SetBtnCom.onClick, "on_WBP_SetBtnCom_Click")
    self:AddUIEvent(self.WBP_HideBtnCom.onClick, "on_WBP_HideBtnCom_Click")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DanceBasicBtnGroup:InitUIView()
end

---组件刷新统一入口
function DanceBasicBtnGroup:Refresh(...)
	self:InitBtns()
end

--- 初始化按钮组
function DanceBasicBtnGroup:InitBtns()
	self.WBP_VoiceBtnCom:Refresh()
	if Game.TeamSystem:IsInGroup() or Game.TeamSystem:IsInTeam() then
		self.userWidget:Event_UI_Style(false)
		self.WBP_ListenBtnCom:Refresh()
		-- todo:text等表出来添加name
		self.WBP_ChatBtnCom:Refresh(self.BtnTypes.chat)
	else
		self.userWidget:Event_UI_Style(true)
	end
	self.WBP_SetBtnCom:Refresh(self.BtnTypes.set)
	self.WBP_HideBtnCom:Refresh(self.BtnTypes.hide)
end


--- 此处为自动生成
function DanceBasicBtnGroup:on_WBP_ChatBtnCom_Click()
	-- 打开聊天窗口
	if not Game.NewUIManager:IsShow(UIPanelConfig.ChatWindowLayer_Panel) then
		Game.NewUIManager:OpenPanel(UIPanelConfig.ChatWindowLayer_Panel,
			Enum.EChatChannelData.TEAM, Enum.EChatTarget.Channel, Game.ChatSystem:GetChatSetting(Enum.EChatSetting["DefaultSmallChatWins"]))
	end
end

--- 此处为自动生成
function DanceBasicBtnGroup:on_WBP_SetBtnCom_Click()
	if not Game.NewUIManager:IsShow(UIPanelConfig.DanceSetTipsPanel) then
		Game.NewUIManager:OpenPanel(UIPanelConfig.DanceSetTipsPanel)
	else
		Game.NewUIManager:ClosePanel(UIPanelConfig.DanceSetTipsPanel)
	end
end

--- 此处为自动生成
function DanceBasicBtnGroup:on_WBP_HideBtnCom_Click()
	self.isHideBtnClick = not self.isHideBtnClick
	self.WBP_HideBtnCom:SetType(self.BtnTypes.hide, self.isHideBtnClick)
	self:GetParent():onClickHideBtn(self.isHideBtnClick)

	if self.isHideBtnClick then
		Game.NewUIManager:ClosePanel(UIPanelConfig.DanceSetTipsPanel)
	end
end


function DanceBasicBtnGroup:OnClose()
	Game.NewUIManager:ClosePanel(UIPanelConfig.DanceSetTipsPanel)
end

return DanceBasicBtnGroup
