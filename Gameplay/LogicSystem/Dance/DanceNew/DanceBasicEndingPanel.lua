local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local sharedConst = kg_require("Shared.Const")


---@class DanceBasicEndingPanel : UIPanel
---@field view DanceBasicEndingPanelBlueprint
local DanceBasicEndingPanel = DefineClass("DanceBasicEndingPanel", UIPanel)

DanceBasicEndingPanel.eventBindMap = {
	[EEventTypesV2.ON_DANCE_STOP] = "OnDanceStop",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DanceBasicEndingPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DanceBasicEndingPanel:InitUIData()
	self.danceMusicData = nil
	self.mode = 0
end

--- UI组件初始化，此处为自动生成
function DanceBasicEndingPanel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DanceBasicEndingPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DanceBasicEndingPanel:InitUIView()
end

---面板打开的时候触发
function DanceBasicEndingPanel:OnRefresh(danceMusicID, mode)
	self.danceMusicData = Game.TableData.GetBasicDanceMusicDataRow(danceMusicID)
	self.mode = mode
	self:PlayAnimation(self.view.Ani_Fadein, function()
		self:AnimationEndCallBack()
	end)
end

function DanceBasicEndingPanel:AnimationEndCallBack()
	-- 动画结束后，关闭面板
	Game.NewUIManager:ClosePanel(UIPanelConfig.DanceBasicDisplayPanel)
	Game.DanceSystem:StopSingleDancePerformance()
	self:CloseSelf()

	-- 重新打开选曲面板
	Game.NewUIManager:OpenPanel(UIPanelConfig.DanceBasicPanel, self.mode)
end

function DanceBasicEndingPanel:OnDanceStop()
	self:StopAnimation(self.view.Ani_Fadein)
	Game.NewUIManager:ClosePanel(UIPanelConfig.DanceBasicDisplayPanel)
	Game.DanceSystem:ReqLeaveDanceActivity()
	self:CloseSelf()
end

return DanceBasicEndingPanel
