local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local LQueue = require "Framework.Library.LQueue"
local chartData = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceQTE.ChartData")
local chartProcessor = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceQTE.ChartProcessor")
local KismetInputLibrary = import("KismetInputLibrary")
local EBasicDanceQTEData = Enum.EBasicDanceQTEData

---@class HUDGuildDanceTabAll : UIComponent
---@field view HUDGuildDanceTabAllBlueprint
local HUDGuildDanceTabAll = DefineClass("HUDGuildDanceTabAll", UIComponent)

HUDGuildDanceTabAll.eventBindMap = {
	[EEventTypesV2.ON_DANCE_ITEM_JUDGE_END] = "OnJudgementEnd",
	--[_G.EEventTypes.ON_GUILD_DANCE_ITEM_HOLD_START_JUDGE_END] = "OnHoldStartJudged",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDGuildDanceTabAll:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIview()
end

---初始化数据
function HUDGuildDanceTabAll:InitUIData()
	self.showIndex = 1          -- 展示出来的键的数据索引
	self.showTime = 0           -- 在轨道上需要展示的时间
	self.judgementDistance = 0  -- 判定距离     
	self.isStart = false        -- 是否开始
	
	self.movePos = {}       -- 每个手指的位移

end

--- UI组件初始化，此处为自动生成
function HUDGuildDanceTabAll:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDGuildDanceTabAll:InitUIEvent()
    self:AddUIEvent(self.view.WBP_Judge_lua.Btn_ClickArea_lua.OnTouchStartedEvent, "on_WBP_Judge_luaBtn_ClickArea_lua_TouchStartedEvent")
    self:AddUIEvent(self.view.WBP_Judge_lua.Btn_ClickArea_lua.OnTouchMovedEvent, "on_WBP_Judge_luaBtn_ClickArea_lua_TouchMovedEvent")
    self:AddUIEvent(self.view.WBP_Judge_lua.Btn_ClickArea_lua.OnTouchEndedEvent, "on_WBP_Judge_luaBtn_ClickArea_lua_TouchEndedEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDGuildDanceTabAll:InitUIview()
end

---组件刷新统一入口
function HUDGuildDanceTabAll:Refresh(id)
	self:InitData(id)
end

function HUDGuildDanceTabAll:OnClose()
	if self.tickTimer then
		self:StopTimer(self.tickTimer)
		self.tickTimer = nil
	end
	if self.chartProcessor then
		self.chartProcessor:End()
		self.chartProcessor = nil
	end
	self.showItems:Clear()
end

--- 开始qte
function HUDGuildDanceTabAll:StartPlay()
	-- todo:打点数据换读表id
	self.chartProcessor:SetVelocity(Game.TableData.GetBasicDanceConstDataRow("CURRENT_SPEED"))
	self.chartProcessor:Start()
	self:GenerateOptionWidgets()
	self:UpdatePositions()
	self:StartOnIdle()
end

--- recover play
function HUDGuildDanceTabAll:RecoverPlay(startStamp, score)
	self.isStart = true
	self.chartProcessor:SetVelocity(Game.TableData.GetBasicDanceConstDataRow("CURRENT_SPEED"))
	self.chartProcessor:StartAndTraceToLast(startStamp, score)
	self:GenerateOptionWidgets()
	self:UpdatePositions()
	self:StartOnIdle()
end

--- 结束qte
function HUDGuildDanceTabAll:EndChartProcess()
	self.chartProcessor:End()
end

function HUDGuildDanceTabAll:StartOnIdle()
	if self.tickTimer then
		self:StopTimer(self.tickTimer)
	end
	self.tickTimer = self:StartTickTimer("BasicDanceUpdate",function(deltaTime)
		self:OnIdle(deltaTime)
	end, -1)
end

function HUDGuildDanceTabAll:OnIdle(deltaTime)
	self.chartProcessor:OnTick(deltaTime)
	self:GenerateOptionWidgets()
	self:UpdatePositions()
	self:ClearInvalidWidgets()
end

function HUDGuildDanceTabAll:InitData(id)
	self.showItems = LQueue.new()   -- 键的widget队列
	self.chartProcessor = chartProcessor.new() -- 舞蹈图处理器
	self.chartProcessor:LoadChart(chartData, id)
	self.showIndex = 1
	self.movePos = {}
	local generateLine = self.view.GenerateLine_lua.Slot:GetPosition()
	local judgementLinePos = self.view.JudgementLine_lua.Slot:GetPosition()
	self.judgementDistance = judgementLinePos.Y - generateLine.Y
	self.showTime = self.judgementDistance / self.chartProcessor.flowVelocity * 1000
	while not self.showItems:IsEmpty() do
		self:PushOneComponent(self.view.CP_Main_lua, self.showItems:PopLeft()[2])
	end
	self.showItems:Clear()
end

-- 创建轨道上的键
function HUDGuildDanceTabAll:GenerateOptionWidgets()
	while self.showIndex <= #self.chartProcessor.optionQueue do
		local curOption = self.chartProcessor.optionQueue[self.showIndex]
		if curOption.offset < self.chartProcessor:GetTimeAfterStart() + self.showTime then
			local curWidget = self:GetOptionWidget(curOption.type, self.showIndex)
			if curWidget then
				self.showItems:PushRight({self.showIndex, curWidget})
			end
			if curOption.type == chartProcessor.OptionType.HOLD_START then
				self.showIndex = self.showIndex + 1
			end
			self.showIndex = self.showIndex + 1
		else
			break
		end
	end
end

-- 创建单个键
function HUDGuildDanceTabAll:GetOptionWidget(optionType, index)
	local widget
	if optionType == chartProcessor.OptionType.TAP then
		widget = self:SyncLoadComponent(UICellConfig.DanceTap_Widget, self.view.CP_Main_lua)
	elseif optionType == chartProcessor.OptionType.FLICK then
		widget = self:SyncLoadComponent(UICellConfig.DanceFlick_Widget, self.view.CP_Main_lua)
	elseif optionType == chartProcessor.OptionType.HOLD_START then
		widget = self:SyncLoadComponent(UICellConfig.DanceHold_Widget, self.view.CP_Main_lua)
		--widget = self:FormComponent("WBP_DanceHold", self.view.CP_Main_lua, HUDGuildDanceHold)
		local startData = self.chartProcessor.optionQueue[index]
		local endData = self.chartProcessor.optionQueue[index+1]
		widget:RefreshSize(FVector2D(96, self.judgementDistance * (endData.offset - startData.offset) / self.showTime + 96))
	end
	if widget then
		widget:Refresh()
		widget:SetType(true)
	end
	return widget
end

---更新键位置
function HUDGuildDanceTabAll:UpdatePositions()
	local lastTime = self.chartProcessor:GetTimeAfterStart()
	local baseY = self.view.JudgementLine_lua.Slot:GetPosition().Y
	for _, item in pairs(self.showItems) do
		local tapIndex, tapWidget = table.unpack(item)
		local tapData = self.chartProcessor.optionQueue[tapIndex]
		local percent = (tapData.offset - lastTime) / self.showTime
		local tapY = baseY - percent * self.judgementDistance
		if tapData.type == chartProcessor.OptionType.HOLD_START then
			if percent > 0 then
				tapWidget:UpdatePositionAndSize(FVector2D(0, tapY), percent)
			else
				local endData = self.chartProcessor.optionQueue[tapIndex+1]
				local baseLength = math.max(self.judgementDistance * (endData.offset - lastTime) / self.showTime + 96, 96)
				tapWidget:RefreshSize(FVector2D(96, baseLength))
				if self.chartProcessor.bInHold then
					tapWidget:EnterPress()
				else
					tapWidget:ExitPress()
				end
			end
			tapWidget:ShowItem()
		else
			tapWidget:UpdatePositionAndSize(FVector2D(0, tapY), percent)
			tapWidget:ShowItem()
		end
	end
end

--- 处理无效键
function HUDGuildDanceTabAll:ClearInvalidWidgets()
	local optionQueue = self.chartProcessor.optionQueue
	while not self.showItems:IsEmpty() do
		local dataIndex = self.showItems:Peek()[1]
		local data = optionQueue[dataIndex]
		if data.type == chartProcessor.OptionType.HOLD_START then
			if optionQueue[dataIndex + 1].result then   -- 看按压结束的判定
				self:RemoveComponent(self.showItems:PopLeft()[2])
			else
				break
			end
		else
			if data.result then
				self:RemoveComponent(self.showItems:PopLeft()[2])
			else
				break
			end
		end
	end
end


--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent

function HUDGuildDanceTabAll:on_WBP_Judge_luaBtn_ClickArea_lua_TouchStartedEvent(myGeometry, inPointerEvent)
	local pointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inPointerEvent)
	local screenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
	self.movePos[pointerIndex] = {screenPos, _G._now()}
	self:ProcessSelfPress()
end

function HUDGuildDanceTabAll:ProcessSelfPress()
	self.chartProcessor:ProcessInput(chartProcessor.InputType.Press)
	if self.chartProcessor.bInHold then
		self:PlayAnimation(self.view.WBP_Judge_lua.Ani_help_hold_in, nil, self.view.WBP_Judge_lua)
		--Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.GUILD_DANCING_CLICK_LONG)
	end
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent

function HUDGuildDanceTabAll:on_WBP_Judge_luaBtn_ClickArea_lua_TouchMovedEvent(myGeometry, inPointerEvent)
	local pointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inPointerEvent)
	local screenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
	local curTimestamp = _G._now()
	if not self.movePos[pointerIndex] then
		self.movePos[pointerIndex] = {screenPos, curTimestamp}
		return UIBase.HANDLED
	end
	local lastPos, lastTimestamp = table.unpack(self.movePos[pointerIndex])
	if (curTimestamp - lastTimestamp) <= 0 then
		return UIBase.HANDLED
	end
	local diffDistance = screenPos - lastPos
	local distance = math.sqrt(diffDistance.X * diffDistance.X + diffDistance.Y * diffDistance.Y)
	if distance / (curTimestamp - lastTimestamp) > Game.TableData.GetBasicDanceConstDataRow("FLICK_TIME_DIFFERENCE") then
		Log.Debug(distance / (curTimestamp - lastTimestamp))
		self.chartProcessor:ProcessInput(chartProcessor.InputType.Flick)
	end
	self.movePos[pointerIndex] = {screenPos, curTimestamp}
end



--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent

function HUDGuildDanceTabAll:on_WBP_Judge_luaBtn_ClickArea_lua_TouchEndedEvent(myGeometry, inPointerEvent)
	local pointerIndex = KismetInputLibrary.PointerEvent_GetPointerIndex(inPointerEvent)
	self:ProcessSelfRelease()
	self.movePos[pointerIndex] = nil
end

function HUDGuildDanceTabAll:ProcessSelfRelease()
	self.chartProcessor:ProcessInput(chartProcessor.InputType.Release)
	if self.chartProcessor.bInHold then
		self:StopAnimation(self.view.WBP_Judge_lua.Ani_help_hold_in, self.view.WBP_Judge_lua)
		self:PlayAnimation(self.view.WBP_Judge_lua.Ani_help_hold_out, nil, self.view.WBP_Judge_lua)
		--Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.GUILD_DANCING_CLICK_LONG_STOP)
	end
end

function HUDGuildDanceTabAll:OnJudgementEnd(chartProcessorID, judgementIndex, isAuto)
	if chartProcessorID ~= self.chartProcessor.chartProcessorID then
		return
	end
	local widget = self.view.WBP_Judge_lua
	local evaluateLevel = self.chartProcessor.optionQueue[judgementIndex].result
	local type = self.chartProcessor.optionQueue[judgementIndex].type
	local animation
	if type == chartProcessor.OptionType.TAP then
		if evaluateLevel == EBasicDanceQTEData.PERFECT then
			animation = widget.Ani_click_perfect
		elseif evaluateLevel == EBasicDanceQTEData.GREAT then
			animation = widget.Ani_click_excellence
		elseif evaluateLevel == EBasicDanceQTEData.GOOD then
			animation = widget.Ani_click_good
		elseif evaluateLevel == EBasicDanceQTEData.COOL then
			animation = widget.Ani_click_normal
		else
			animation = widget.Ani_click_fail
		end
		if not isAuto then
			--Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.GUILD_DANCING_CLICK)
		end
	elseif type == chartProcessor.OptionType.FLICK then
		if evaluateLevel == EBasicDanceQTEData.PERFECT then
			animation = widget.Ani_click_perfect_slide
		elseif evaluateLevel == EBasicDanceQTEData.GREAT then
			animation = widget.Ani_click_excellence_slide
		elseif evaluateLevel == EBasicDanceQTEData.GOOD then
			animation = widget.Ani_click_good_slide
		elseif evaluateLevel == EBasicDanceQTEData.COOL then
			animation = widget.Ani_click_normal_slide
		else
			animation = widget.Ani_click_fail
		end
		if not isAuto then
			--Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.GUILD_DANCING_CLICK_SLIDE)
		end
	elseif type == chartProcessor.OptionType.HOLD_END then
		self:StopAnimation(widget.Ani_help_hold_in, widget)
		animation = widget.Ani_help_hold_out
		--Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.GUILD_DANCING_CLICK_LONG_STOP)
	end
	if animation then
		self:PlayAnimation(animation, nil, widget)
	end
end

--- 获取第一个键离曲子开始时的出生时间
function HUDGuildDanceTabAll:GetFirstBornTime()
	return self.chartProcessor:GetFirstBeatTimeStand()
end

--- 获取最后一个键离曲子开始时的出生时间
function HUDGuildDanceTabAll:GetLastTime()
	return self.chartProcessor:GetLastBeatTimeStand() + self.showTime
end

return HUDGuildDanceTabAll
