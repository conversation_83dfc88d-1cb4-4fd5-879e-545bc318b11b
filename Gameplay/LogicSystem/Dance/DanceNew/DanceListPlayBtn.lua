local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local SharedConst = kg_require("Shared.Const")
---@class DanceListPlayBtn : UIComponent
---@field view DanceListPlayBtnBlueprint
local DanceListPlayBtn = DefineClass("DanceListPlayBtn", UIComponent)

DanceListPlayBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DanceListPlayBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DanceListPlayBtn:InitUIData()
    self.isPlay = false
    self.playingState = SharedConst.DanceVoicePlayingState.INITIAL
    self.playingID = 0
    self.playStartTime = 0  -- 音频播放开始时间
    self.playPauseTime = 0  -- 音频播放暂停时间
    self.pauseDuration = 0
    self.progress = 0
    self.audioPlayerTimer = 0
end

--- UI组件初始化，此处为自动生成
function DanceListPlayBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DanceListPlayBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DanceListPlayBtn:InitUIView()
end

---组件刷新统一入口
function DanceListPlayBtn:Refresh(...)
end

function DanceListPlayBtn:OnClose()
    -- 加个保底清除Timer
    if self.audioPlayerTimer then
        Game.TimerManager:StopTimerAndKill(self.audioPlayerTimer)
        self.audioPlayerTimer = nil
    end
    self:StopAudio()
end


--- 此处为自动生成
function DanceListPlayBtn:on_Btn_ClickArea_Clicked()
    if self.playingState == SharedConst.DanceVoicePlayingState.INITIAL then
        self:StartAudio()
    elseif self.playingState == SharedConst.DanceVoicePlayingState.PLAYING then
        self:PauseAudio()
    elseif self.playingState == SharedConst.DanceVoicePlayingState.PAUSE then
        self:ResumeAudio()
    end
end

function DanceListPlayBtn:StartAudio()
    if self.playingState == SharedConst.DanceVoicePlayingState.PLAYING then
        return
    end
    self.playingID = Game.AkAudioManager:PostEvent2D(self.eventName)
    self.playStartTime = _G._now()
    self.playingState = SharedConst.DanceVoicePlayingState.PLAYING
    self.audioPlayerTimer = Game.TimerManager:CreateTimerAndStart(function()
        if self.playingState ~= SharedConst.DanceVoicePlayingState.PLAYING then
            return
        end
        self:updateAndReturnProgress()
        if self.progress >= 1 then
            -- todo 等jingzhe给音频加好回调之后用回调做
            self:StopAudio()
            return
        end
        self.userWidget:Event_UI_Style(self:getIsPlay(), self.progress)
    end, 0.2 * 1000, -1, true)
end

function DanceListPlayBtn:PauseAudio()
    if self.playingState == SharedConst.DanceVoicePlayingState.PAUSE then
        return
    end
    Game.AkAudioManager:PauseEvent(self.playingID)
    self.playingState = SharedConst.DanceVoicePlayingState.PAUSE
    self.playPauseTime = _G._now()
    self.userWidget:Event_UI_Style(self:getIsPlay(), self.progress)
    --Game.TimerManager:PauseTimer(self.audioPlayerTimer)
end

function DanceListPlayBtn:ResumeAudio()
    if self.playingState == SharedConst.DanceVoicePlayingState.PLAYING then
        return
    end
    Game.AkAudioManager:ResumeEvent(self.playingID)
    self.playingState = SharedConst.DanceVoicePlayingState.PLAYING
    self.pauseDuration = self.pauseDuration + _G._now() - self.playPauseTime
    --Game.TimerManager:ResumeTimer(self.audioPlayerTimer)
end

function DanceListPlayBtn:StopAudio()
    if self.playingState == SharedConst.DanceVoicePlayingState.INITIAL then
        return
    end
    Game.AkAudioManager:StopEvent(self.playingID)
    self.playingState = SharedConst.DanceVoicePlayingState.INITIAL
    if self.audioPlayerTimer then
        Game.TimerManager:StopTimerAndKill(self.audioPlayerTimer)
        self.audioPlayerTimer = nil
    end
    self:InitUIData()
    self.userWidget:Event_UI_Style(false, 0)
end


function DanceListPlayBtn:updateAndReturnProgress()
    if self.totalDuration <= 0 then
        return 0
    end

    if self.playingState == SharedConst.DanceVoicePlayingState.PLAYING then
        self.progress = (_G._now() - self.playStartTime - self.pauseDuration) / self.totalDuration
    end

    return self.progress
end

function DanceListPlayBtn:getIsPlay()
    return self.playingState == SharedConst.DanceVoicePlayingState.PLAYING
end

function DanceListPlayBtn:InitVoiceInfo(eventName)
    self.eventName = eventName
    local ABD = Game.TableData.GetAkAudioEventDataRow(eventName)
    self.totalDuration = ABD.Duration * 1000
end

return DanceListPlayBtn
