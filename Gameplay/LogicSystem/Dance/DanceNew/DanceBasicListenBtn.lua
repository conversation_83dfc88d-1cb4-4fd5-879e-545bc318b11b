local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class DanceBasicListenBtn : UIComponent
---@field view DanceBasicListenBtnBlueprint
local DanceBasicListenBtn = DefineClass("DanceBasicListenBtn", UIComponent)

DanceBasicListenBtn.eventBindMap = {
	[_G.EEventTypes.ON_TEAMMEMBER_VOICESTATE_CHANGED] = { "RefreshVoiceState", GetMainPlayerEID },
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DanceBasicListenBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DanceBasicListenBtn:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DanceBasicListenBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function DanceBasicListenBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DanceBasicListenBtn:InitUIView()
end

---组件刷新统一入口
function DanceBasicListenBtn:Refresh(...)
	self:RefreshVoiceState()
end


--- 此处为自动生成
function DanceBasicListenBtn:on_Btn_ClickArea_Clicked()
	local geometry = self.userWidget:GetCachedGeometry()
	local _, ViewportPosition =
	import("SlateBlueprintLibrary").LocalToViewport(
		_G.GetContextObject(),
		geometry,
		FVector2D(1, 0),
		nil,
		nil
	)
	local Size = import("SlateBlueprintLibrary").GetLocalSize(geometry)
	ViewportPosition = FVector2D(ViewportPosition.X + 20 , ViewportPosition.Y + 2 * Size.Y + 80)
	Game.NewUIManager:OpenPanel("P_HUDTeamVoiceTips",ViewportPosition, Enum.EVOICE_CHANNEL.WORLD, nil, true)
end

--- 刷新语音状态
function DanceBasicListenBtn:RefreshVoiceState()
	local voiceState = Game.TeamSystem:GetVoiceState()
	local iconData = ""
	local title = ""
	if voiceState == Enum.EVOICE_STATE.LISTEN then
		iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_LISTEN)
		title = StringConst.Get("TEAM_VOICE_LISTEN")
	elseif voiceState == Enum.EVOICE_STATE.VOICE then
		iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_OPEN_MIC)
		title = StringConst.Get("TEAM_VOICE_OPENMIC")
	elseif voiceState == Enum.EVOICE_STATE.REFUSE then
		iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_NOT_LISTEN)
		title = StringConst.Get("TEAM_VOICE_REFUSE")
	end

	local Image = self.view.Img_Btn
	self:SetImage(Image, iconData.AssetPath)

	self.view.Text_Btn:SetText(title)
end

return DanceBasicListenBtn
