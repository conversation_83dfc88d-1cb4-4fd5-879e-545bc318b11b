local DanceBasicCorner = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceBasicCorner")
local DanceBasicStageBtn = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceBasicStageBtn")
local DanceBasicBtnGroup = kg_require("Gameplay.LogicSystem.Dance.DanceNew.DanceBasicBtnGroup")
local UIComFrame = kg_require("Framework.KGFramework.KGUI.Component.Panel.UIComFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComTabList")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local ESlateVisibility = import("ESlateVisibility")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local sharedConst = kg_require("Shared.Const")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

---@class DanceBasicPanel : UIPanel
---@field view DanceBasicPanelBlueprint
local DanceBasicPanel = DefineClass("DanceBasicPanel", UIPanel)

DanceBasicPanel.eventBindMap = {
    [_G.EEventTypes.ON_TEAMMEMBER_VOICESTATE_CHANGED] = { "RefreshVoiceState", GetMainPlayerEID },
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DanceBasicPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIEventCustom()
    self:InitUIView()
end

---初始化数据
function DanceBasicPanel:InitUIData()
    self.mode = sharedConst.DanceActivityMode.MAX
    ---@type table tab列表数据
    ---todo 移动到枚举
    self.tabDataV1 = {
        [1] = {     -- 舞曲
            iconPath = UIAssetPath.UI_Dance_Img_Tab01_Sprite,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.ListSong,
        },
        [2] = {     -- 舞台
            iconPath = UIAssetPath.UI_Dance_Img_Tab02_Sprite,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.ListStage,
        }
    }
    self.tabDataV2 = {
        [1] = {     -- 舞曲
            iconPath = UIAssetPath.UI_Dance_Img_Tab01_Sprite,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.ListSong,
        },
        [2] = {     -- 舞台
            iconPath = UIAssetPath.UI_Dance_Img_Tab02_Sprite,
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.ListStage,
        },
        [3] = {     -- 阵型
            iconPath = UIAssetPath.UI_Dance_Img_Tab03_Sprite,      
            uiType = Enum.ComTabUIType.Image,
            BoundWidget = self.view.ListFormation
        }
    }

    self.curTabIndex = 1        -- 当前选中的tab
    self.curSongIndex = 1       -- 当前选中的SongIndex
    self.curStageIndex = 1      -- 当前选中的StageIndex
    self.curFormationIndex = 1  -- 当前选中的FormationIndex
    self.cornerUIType = 0       -- DanceBasicCorner的UIType
    self.isFold = true
    self.isWalk = true
end

--- UI组件初始化，此处为自动生成
function DanceBasicPanel:InitUIComponent()
    ---@type DanceBasicCorner
    self.WBP_DanceBasicCornerCom = self:CreateComponent(self.view.WBP_DanceBasicCorner, DanceBasicCorner)
    ---@type DanceBasicStageBtn
    self.WBP_DanceBasicStageBtnCom = self:CreateComponent(self.view.WBP_DanceBasicStageBtn, DanceBasicStageBtn)
    ---@type DanceBasicBtnGroup
    self.WBP_DanceBasicBtnGroupCom = self:CreateComponent(self.view.WBP_DanceBasicBtnGroup, DanceBasicBtnGroup)
    ---@type UIComFrame
    self.WBP_ComFrameCom = self:CreateComponent(self.view.WBP_ComFrame, UIComFrame)
    ---@type UIListView
    self.ListStageCom = self:CreateComponent(self.view.ListStage, UIListView)
    ---@type UIListView
    self.ListFormationCom = self:CreateComponent(self.view.ListFormation, UIListView)
    ---@type UIListView
    self.ListSongCom = self:CreateComponent(self.view.ListSong, UIListView)
    ---@type UIComTabList
    self.SB_SecondTabListCom = self:CreateComponent(self.view.SB_SecondTabList, UIComTabList)
    ---@type UIComButton
    self.WBP_DanceBasicCorner_WBP_ComBtnCom = self:CreateComponent(self.view.WBP_DanceBasicCorner.WBP_ComBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function DanceBasicPanel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComFrameCom.onPreCloseEvent, "on_WBP_ComFrameCom_PreCloseEvent")
    self:AddUIEvent(self.SB_SecondTabListCom.onItemSelectionChanged, "on_SB_SecondTabListCom_ItemSelectionChanged")
    self:AddUIEvent(self.ListSongCom.onItemSelectionChanged, "on_ListSongCom_ItemSelectionChanged")
    self:AddUIEvent(self.ListStageCom.onItemSelectionChanged, "on_ListStageCom_ItemSelectionChanged")
    self:AddUIEvent(self.view.WBP_DanceBasicRunBtn.Btn_ClickArea.OnClicked, "on_WBP_DanceBasicRunBtnBtn_ClickArea_Clicked")
    self:AddUIEvent(self.view.WBP_DanceBasicStageBtn.Btn_ClickArea.OnClicked, "on_WBP_DanceBasicStageBtnBtn_ClickArea_Clicked")
    self:AddUIEvent(self.WBP_DanceBasicCorner_WBP_ComBtnCom.onClickEvent, "on_WBP_DanceBasicCorner_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.ListFormationCom.onItemSelectionChanged, "on_ListFormationCom_ItemSelectionChanged")
end

--手动写的
function DanceBasicPanel:InitUIEventCustom()
    self:AddUIEvent(self.view.WBP_DanceBasicBtnGroup.WBP_ListenBtn.Btn_ClickArea.OnClicked, "OnClickListenBtn")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DanceBasicPanel:InitUIView()
    local returnTitle = Game.TableData.GetBasicDanceConstDataRow("DANCE_RETURN_TITLE")
    self.WBP_ComFrameCom:Refresh(returnTitle)
    local startTitle = Game.TableData.GetBasicDanceConstDataRow("DANCE_START_TITLE")
    self.WBP_DanceBasicCorner_WBP_ComBtnCom:SetName(startTitle)
end

---面板打开的时候触发
function DanceBasicPanel:OnRefresh(mode)
    self:PlayAnimation(self.view.Ani_Fadein)
    if mode == nil then
        mode = sharedConst.DanceActivityMode.SINGLE
    end
    self.mode = mode
    self:refreshStageBtn()
    self:refreshTabList()
    self:RefreshVoiceState()
    self:RefreshCorner()
    self.WBP_DanceBasicStageBtnCom:SetUIStyle(self.isFold)
    self.view.WBP_DanceBasicRunBtn:Event_UI_Style(true)
    self.WBP_DanceBasicBtnGroupCom:Refresh()
end

function DanceBasicPanel:refreshStageBtn()
    --self.view.WBP_DanceBasicStageBtn
end

function DanceBasicPanel:refreshTabList()
    if self.mode ~= sharedConst.DanceActivityMode.MULTI then
        self.tabData = self.tabDataV1
        self.SB_SecondTabListCom:Refresh(self.tabData)
    else
        self.tabData = self.tabDataV2
        self.SB_SecondTabListCom:Refresh(self.tabData)
    end
    -- 默认选中第一个
    self:refreshMusicList(true)
    self.view.ListStage:SetVisibility(ESlateVisibility.Collapsed)
    self.view.ListFormation:SetVisibility(ESlateVisibility.Collapsed)
    self.SB_SecondTabListCom:SetSelectedItemByIndex(1, true)
end

function DanceBasicPanel:refreshMusicList(isSelected)
    if not isSelected then
        self.view.ListSong:SetVisibility(ESlateVisibility.Collapsed)
        local item = self.ListSongCom:GetItemByIndex(self.curSongIndex)
        item:RefreshOnSelectChange(isSelected)
        return
    end
    self.showSongIDList = Game.DanceSystem:GetDanceSongList()
    self.ListSongCom:Refresh(self.showSongIDList, 1, 
    { 
        curIndex = self.curSongIndex,
        mode = self.mode,
        isSelected = isSelected,
    })
    self.ListSongCom:SetSelectedItemByIndex(self.curSongIndex, true)
    self.view.ListSong:SetVisibility(ESlateVisibility.Visible)
end

function DanceBasicPanel:refreshStageList(isSelected)
    if not isSelected then
        self.view.ListStage:SetVisibility(ESlateVisibility.Collapsed)
        return
    end

    self.showSongStageList = Game.DanceSystem:GetDanceStageList()
    self.ListStageCom:Refresh(self.showSongStageList, 1,
    {
        curIndex = self.curStageIndex,
        isLeader = self:IsDanceLeader()
    })
    self.ListStageCom:SetSelectedItemByIndex(self.curStageIndex, true)
    self.view.ListStage:SetVisibility(ESlateVisibility.Visible)
end

function DanceBasicPanel:refreshInformationList(isSelected)
    if not isSelected then
        self.view.ListFormation:SetVisibility(ESlateVisibility.Collapsed)
        return
    end
    self.showFormationList = Game.DanceSystem:GetDanceFormationList()
    self.ListFormationCom:Refresh(self.showFormationList, 1,
    {
        curIndex = self.curFormationIndex
    })

    self.showFormationList = Game.DanceSystem:GetDanceFormationList()
    self.ListFormationCom:SetSelectedItemByIndex(self.curFormationIndex, true)
    self.view.ListFormation:SetVisibility(ESlateVisibility.Visible)
end

function DanceBasicPanel:IsDanceLeader()
    if self.mode == sharedConst.DanceActivityMode.SINGLE then
        return true
    else
        return Game.DanceSystem:IsDanceLeader()
    end
end

function DanceBasicPanel:SetDanceInfo(danceName, danceTotalTime)
    if danceName then
        self.view.WBP_DanceBasicSongName.TextSongName:SetText(danceName)
    end
    self.view.WBP_DanceBasicSongName.Text_Time:SetText(
        Game.TimeUtils.FormatCountDownString(danceTotalTime, false)
    )

end

--- 此处为自动生成
---@return bool
function DanceBasicPanel:on_WBP_ComFrameCom_PreCloseEvent()
    if Game.me.danceStageID > 0 then
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.BASICDANCE_STAGE_EXIT,
        function()
            self:ClosePanel()
        end)
    else
        self:ClosePanel()
    end
end

function DanceBasicPanel:ClosePanel()
    self:PlayAnimation(self.view.Ani_Fadeout)
    Game.DanceSystem:LockMoveInDance(false)
    Game.DanceSystem:ReqLeaveDanceActivity()
    self:CloseSelf()
end

function DanceBasicPanel:StopCurrentAudio()
    local widget = self.ListSongCom:GetItemByIndex(self.curSongIndex)
    if not widget then
        return
    end
    widget.WBP_DanceListPlayBtnCom:StopAudio()
end

--- 此处为自动生成
function DanceBasicPanel:on_WBP_DanceBasicCorner_WBP_ComBtnCom_ClickEvent()
    if self.cornerUIType ~= 0 then
        return
    end

    -- 获取当前Song
    local widget = self.ListSongCom:GetItemByIndex(self.curSongIndex)
    if not widget then
        return
    end
    local musicID = widget.musicID

    Game.DanceSystem:LockMoveInDance(false)
    self:StopCurrentAudio()
    -- 获取当前stage,暂时不需要
    if self.mode == sharedConst.DanceActivityMode.SINGLE then
        Game.DanceSystem:ReqStartSingleDance(musicID)
    elseif self.mode == sharedConst.DanceActivityMode.DOUBLE then
        Game.DanceSystem:ReqStartDoubleDance(musicID)
    elseif self.mode == sharedConst.DanceActivityMode.MULTI then
        Game.DanceSystem:ReqStartMultiDance(musicID)
    end
end

--- 此处为自动生成
---@param index number
---@param selected bool
function DanceBasicPanel:on_ListSongCom_ItemSelectionChanged(index, selected)
    if selected then
        self.curSongIndex = index
    end
    local widget = self.ListSongCom:GetItemByIndex(index)
    if widget then
        widget:RefreshOnSelectChange(selected)
    end
end


--- 此处为自动生成
---@param index number
---@param selected bool
function DanceBasicPanel:on_ListStageCom_ItemSelectionChanged(index, selected)
    if selected then
        self.curStageIndex = index
    end
    local widget = self.ListStageCom:GetItemByIndex(index)
    if widget then
        widget:RefreshOnSelectChange(selected)
    end
end


--- 此处为自动生成
---@param index number
---@param selected bool
function DanceBasicPanel:on_SB_SecondTabListCom_ItemSelectionChanged(index, selected)
    if selected then
        self.curTabIndex = index
        self.SB_SecondTabListCom:SetSelectedItemByIndex(index, true)
    else
        self.SB_SecondTabListCom:SetSelectedItemByIndex(index, false)
    end
    if index == 1 then
        self:refreshMusicList(selected)
    elseif index == 2 then
        self:refreshStageList(selected)
    elseif index == 3 then
        self:refreshInformationList(selected)
    end
end
 
function DanceBasicPanel:RefreshCorner()
    if not self:IsDanceLeader() then
        self.cornerUIType = 1
        local waitTitle = Game.TableData.GetBasicDanceConstDataRow("DANCE_WAIT_TITLE")
        self.view.WBP_DanceBasicCorner.TextTint1:SetText(waitTitle)
    else
        local startTitle = Game.TableData.GetBasicDanceConstDataRow("DANCE_START_TITLE")
        self.WBP_DanceBasicCorner_WBP_ComBtnCom:SetName(startTitle)
        local SongWidget = self.ListSongCom:GetItemByIndex(self.curSongIndex)
        if not SongWidget then
            return
        end
        -- lock还未规划，后续接入
        if self.mode ~= sharedConst.DanceActivityMode.DOUBLE and SongWidget.isTag then
            self.cornerUIType = 2
        else
            self.cornerUIType = 0
        end
    end

    self.view.WBP_DanceBasicCorner:Event_UI_Style(self.cornerUIType)
end

function DanceBasicPanel:RefreshVoiceState()
    local voiceState = Game.TeamSystem:GetVoiceState()
    local iconData = ""
    local title = ""
    if voiceState == Enum.EVOICE_STATE.LISTEN then
        iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_LISTEN)
        title = StringConst.Get("TEAM_VOICE_LISTEN")
    elseif voiceState == Enum.EVOICE_STATE.VOICE then
        iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_OPEN_MIC)
        title = StringConst.Get("TEAM_VOICE_OPENMIC")
    elseif voiceState == Enum.EVOICE_STATE.REFUSE then
        iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_NOT_LISTEN)
        title = StringConst.Get("TEAM_VOICE_REFUSE")
    end
    
    local Image = self.view.WBP_DanceBasicBtnGroup.WBP_ListenBtn.Img_Btn
    self:SetImage(Image, iconData.AssetPath)

    self.view.WBP_DanceBasicBtnGroup.WBP_ListenBtn.Text_Btn:SetText(title)
end

function DanceBasicPanel:OnClickListenBtn()
    local geometry = self.view.WBP_DanceBasicBtnGroup.WBP_ListenBtn:GetCachedGeometry()
    local _, ViewportPosition =
    import("SlateBlueprintLibrary").LocalToViewport(
            _G.GetContextObject(),
            geometry,
            FVector2D(1, 0),
            nil,
            nil
    )
    local Size = import("SlateBlueprintLibrary").GetLocalSize(geometry)
    ViewportPosition = FVector2D(ViewportPosition.X + 20 , ViewportPosition.Y + 2 * Size.Y + 80)
    Game.NewUIManager:OpenPanel("P_HUDTeamVoiceTips",ViewportPosition, Enum.EVOICE_CHANNEL.WORLD, nil, true)
end

--- 此处为自动生成
function DanceBasicPanel:on_WBP_DanceBasicStageBtnBtn_ClickArea_Clicked()
    self.isFold = not self.isFold
    self.WBP_DanceBasicStageBtnCom:SetUIStyle(self.isFold)
    if self.isFold then
        -- 打开舞美
        self:PlayAnimation(self.view.Ani_list_open)
    else
        -- 收起舞美
        self:PlayAnimation(self.view.Ani_list_close)
    end
end

function DanceBasicPanel:onClickHideBtn(bIsClick)
    if bIsClick then
        self:PlayAnimation(self.view.Ani_hide)
    else
        self:PlayAnimation(self.view.Ani_show)
    end
end


--- 此处为自动生成
function DanceBasicPanel:on_WBP_DanceBasicRunBtnBtn_ClickArea_Clicked()
    self.isWalk = not self.isWalk
    self.view.WBP_DanceBasicRunBtn:Event_UI_Style(self.isWalk)
    Game.DanceSystem:LockMoveInDance(not self.isWalk)
end


--- 此处为自动生成
---@param index number
---@param selected bool
function DanceBasicPanel:on_ListFormationCom_ItemSelectionChanged(index, selected)
    if selected then
        self.curFormationIndex = index
    end

    local widget = self.ListFormationCom:GetItemByIndex(index)
    if widget then
        widget:RefreshOnSelectChange(selected)
    end
end

return DanceBasicPanel
