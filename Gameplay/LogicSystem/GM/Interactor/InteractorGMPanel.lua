local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class InteractorGMPanel : UIPanel
---@field view InteractorGMPanelBlueprint
local InteractorGMPanel = DefineClass("InteractorGMPanel", UIPanel)
local InteractorGMItem = kg_require("Gameplay.LogicSystem.GM.Interactor.InteractorGMItem")
local NewComList = kg_require("Framework.UI.List.NewList.NewComList")
local UIBaseAdapter = kg_require("Framework.UI.UIBaseAdpater")
local P_ComboBox = kg_require("Gameplay.LogicSystem.CommonUI.P_ComboBox")

InteractorGMPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function InteractorGMPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function InteractorGMPanel:InitUIData()
	self.SceneActorMap = {}
	self.regionList = {}
	self.typeList = {}
	self.selectTypeIndex = 1
	self.selectRegionIndex = 1
end

--- UI组件初始化，此处为自动生成
function InteractorGMPanel:InitUIComponent()
	self.gmListView = self:CreateComponent(self.view.WBP_ComList, NewComList, InteractorGMItem)
	
	self.mapBox = self:CreateComponent(self.view.MapBox, UIBaseAdapter, "InteractorGMPanel", P_ComboBox)
	self.mapBox:GetUIBase():Init(self, self.OnSelectMapBox)
	self.typeBox = self:CreateComponent(self.view.TypeBox, UIBaseAdapter, "InteractorGMPanel", P_ComboBox)
	self.typeBox:GetUIBase():Init(self, self.OnSelectTypeBox)
end

---UI事件在这里注册，此处为自动生成
function InteractorGMPanel:InitUIEvent()
    self:AddUIEvent(self.view.Button_Close.OnClicked, "onButton_CloseClicked")
    self:AddUIEvent(self.view.KGButton_Reset.OnClicked, "onKGButton_ResetClicked")
    self:AddUIEvent(self.view.KGButton_Jump.OnClicked, "onKGButton_JumpClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function InteractorGMPanel:InitUIView()
end

---面板打开的时候触发
function InteractorGMPanel:OnRefresh(...)
	local curSceneActorData = Game.WorldDataManager:GetSceneActorDataByLevelID(Game.WorldDataManager.CurLoadLevelID)
	for insID, data in pairs(curSceneActorData) do
		local position = data.Transform.Position
		local belongedRegionID = Game.WorldDataManager:GetSceneFieldType({ X = position.X, Y = position.Y}, "Map")
		if not self.SceneActorMap[belongedRegionID] then
			self.SceneActorMap[belongedRegionID] = {}
		end
		if data.SceneActorCommon and data.SceneActorCommon.ExploreType then
			if not self.SceneActorMap[belongedRegionID][data.SceneActorCommon.ExploreType] then
				self.SceneActorMap[belongedRegionID][data.SceneActorCommon.ExploreType] = {}
			end
			table.insert(self.SceneActorMap[belongedRegionID][data.SceneActorCommon.ExploreType], insID)
		end
	end
	
	local mapData = Game.TableData.GetLevelMapDataRow(Game.WorldDataManager.CurLoadLevelID)
	if not mapData then
		return
	end
	
	table.clear(self.regionList)
	local curPos = Game.me:GetPosition()
	local curRegionID = Game.WorldDataManager:GetSceneFieldType({ X = curPos.X, Y = curPos.Y},"Map")
	local selectIndex
	for regionID, _ in pairs(self.SceneActorMap) do
		local displayText = mapData.Name
		if regionID ~= -1 then
			local secondLevelAreaID = self:FindRegionNameByID(regionID)
			local secondLevelAreaData = Game.TableData.GetExploreSecondLevelAreaDataRow(secondLevelAreaID)
			displayText = displayText .. secondLevelAreaData.Name
		end
		table.insert(self.regionList, {
			text = displayText,
			value= regionID
		})
		if regionID == curRegionID then
			selectIndex = #self.regionList
		end
	end
	table.clear(self.typeList)
	local typeData = Game.TableData.GetExploreTypeDataTable()
	for id, data in ksbcipairs(typeData) do
		table.insert(self.typeList, {
			text = data.Name,
			value = id
		} )
	end
	table.insert(self.typeList, {text = "其他", value = 0})
	
	self.mapBox:GetUIBase():SetData(self.regionList, selectIndex)
	self.typeBox:GetUIBase():SetData(self.typeList, 1)
	
	self:OnSelectMapBox(selectIndex)
	self:OnSelectTypeBox(1)
end

function InteractorGMPanel:FindRegionNameByID(regionID)
	return Enum.EExploreAreafieldIDToSecondLevelAreaID[regionID]
end

function InteractorGMPanel:OnSelectMapBox(index)
	self.selectRegionIndex = index
	self:RefreshSceneActorList()
end

function InteractorGMPanel:OnSelectTypeBox(index)
	self.selectTypeIndex = index
	self:RefreshSceneActorList()
end

function InteractorGMPanel:RefreshSceneActorList()
	local regionID = self.regionList[self.selectRegionIndex].value
	local typeID = self.typeList[self.selectTypeIndex].value
	local listData = self.SceneActorMap[regionID][typeID]
	if listData then
		self.gmListView:SetData(#listData)
	else
		self.gmListView:SetData(0)
	end
end


function InteractorGMPanel:OnRefresh_WBP_ComList(widget, index)
	local regionID = self.regionList[self.selectRegionIndex].value
	local typeID = self.typeList[self.selectTypeIndex].value
	local listData = self.SceneActorMap[regionID][typeID]
	widget:Refresh(listData[index])
end

--- 此处为自动生成
function InteractorGMPanel:onKGButton_JumpClicked()
	local insID = self.view.KGEditableTextBox_0:GetText()
	local pos, levelID = Game.WorldDataManager:GetTransformByInsID(insID, false)
	Game.me:ReqExtraGM("teleport", {
		X = tostring(pos.Position.X), Y = tostring(pos.Position.Y), Z = tostring(pos.Position.Z), 
		MapID = tostring(levelID) })
end

--- 此处为自动生成
function InteractorGMPanel:onKGButton_ResetClicked()
	local insID = self.view.KGEditableTextBox_0:GetText()
	if insID ~= "" then
		Game.me:ReqExtraGM("GMResetGameplay", {groupID = insID})
	else
		Game.me:ReqExtraGM("GMResetGameplay", {})
	end
end


--- 此处为自动生成
function InteractorGMPanel:onButton_CloseClicked()
	self:CloseSelf()
end

return InteractorGMPanel
