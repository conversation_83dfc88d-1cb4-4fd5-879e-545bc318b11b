local GMConfigBase = kg_require "Gameplay.LogicSystem.GM.GMConfigBase"
local ToolsGmConfig = DefineClass("ToolsGmConfig", GMConfigBase)

-- luacheck: push ignore
local function BuildGMCombatDataConfig(InTable)
    local RetTable = {}
    table.insert(
        RetTable,
        {
            Name = "战斗记录",
            Content = function(...)
                UI.ShowUI("GMBattleInfo_Panel")
            end
        }
    )
    table.insert(InTable, { Name = "战斗记录", Content = RetTable })
    return InTable
end


local function BuildGMRuntimeAggroInfoConfig(InTable)
    local FollowHandles = {}
    table.insert(
        InTable,
        {
            Name = "仇恨调试工具",
            Content = {
                {
                    Name = "显示角色id",
                    Content =
                        function(Input, TextDelegate)
                            Game.HeadInfoManager:ToggleGM()
                        end
                },
                {
                    Name = "显示目标怪物累计进战人数",
                    Content = function()
                        if UI.GetUI("GMTickTextPanel") then
                            UI.HideUI("GMTickTextPanel")
                        else
                            UI.ShowUI("GMTickTextPanel", {
                                TickRate = 1,
                                Content = function(OnUpdateTextCallBack)
                                    local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"
                                    local TargetEntity = SkillImpl.GetLockTargetEntity(ETE.EBSTargetType.TT_Skill)
                                    if Game.me and Game.me.eid then
                                        if not TargetEntity then
                                            TargetEntity = Game.me
                                        end
                                        Game.GMManager.SetRetCallbackFunc(
                                            function(Result)
                                                if Result.Content then
                                                    OnUpdateTextCallBack(Result.Content)
                                                end
                                            end
                                        )
                                        local targetEID = TargetEntity.eid
                                        Game.GMManager.ExecuteCommandArgs("GetMaxFightEnrolledPlayerNum", targetEID)
                                    end
                                end
                            })
                        end
                    end
                },
            }
        }
    )
    return InTable
end
-- luacheck: pop

function ToolsGmConfig:GetConfig()
    local ConfigTable = {}
    ConfigTable = BuildGMRuntimeAggroInfoConfig(ConfigTable)
    ConfigTable = BuildGMCombatDataConfig(ConfigTable)
    return { Name = "Tools", Content = ConfigTable }
end

return ToolsGmConfig
