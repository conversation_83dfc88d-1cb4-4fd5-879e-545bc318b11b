local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GM_Time_Panel : UIPanel
---@field view GM_Time_PanelBlueprint
local GM_Time_Panel = DefineClass("GM_Time_Panel", UIPanel)

GM_Time_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GM_Time_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GM_Time_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GM_Time_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GM_Time_Panel:InitUIEvent()
    Game.TimerManager:CreateTimerAndStart(function()
        self:OnTimerTick()
    end, 20, -1, false, "GM_Time_Panel", true)
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GM_Time_Panel:InitUIView()
end

---面板打开的时候触发
function GM_Time_Panel:OnRefresh(...)
end

function GM_Time_Panel:OnTimerTick()
    local Time = Game.TimeManager
    local view = self.view
    view.txt_ServerTime_lua:SetText(Time:GetServerDateStr())
    view.txt_ClientTime_lua:SetText(Time:GetClientDateStr())
    view.txt_LocalTime_lua:SetText(Time:GetLocalDateStr())
end

return GM_Time_Panel
