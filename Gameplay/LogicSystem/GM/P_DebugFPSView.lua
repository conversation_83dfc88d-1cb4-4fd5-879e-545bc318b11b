---@class WBP_DebugFPSView : WBP_DebugFPS_C
---@field public WidgetRoot WBP_DebugFPS_C
---@field public Text_Prims KGTextBlock
---@field public Text_Game KGTextBlock
---@field public Text_Draw KGTextBlock
---@field public Text_Draws KGTextBlock
---@field public Text_FPS KGTextBlock


---@class P_DebugFPSView : WBP_DebugFPSView
---@field public controller P_DebugFPS
local P_DebugFPSView = DefineClass("P_DebugFPSView", UIView)

function P_DebugFPSView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

function P_DebugFPSView:OnDestroy()
end

return P_DebugFPSView
