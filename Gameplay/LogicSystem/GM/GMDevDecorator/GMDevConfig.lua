---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by fangg.
--- DateTime: 2025/2/25 15:58
---

local function GMTestMaterial()
	-- 必须要在登录界面、登录场景才行
	if Game.GameLoopManagerV2:GetCurGameLoopStage() ~= Game.GameLoopManagerV2.EGameStageType.Login then
		Log.Error("必须在登录界面才能测试，否则就不准了！重启游戏再次来过。")
		return
	end
	
	-- 关闭所有界面（反正只是用于测试）
	UIManager:GetInstance():CloseAllPanel()
	UIManager:GetInstance():ClearCache()
	UI.ShowUI("MaterialTest")
	return false
end

local function GMSetReportLogC7AndLogCrashSightOnly()
	local C7FunctionLibrary = import("C7FunctionLibrary")
	-- 默认bReportWithLuaTraceOnly=true
	local v = C7FunctionLibrary.GetReportLogC7AndLogCrashSightOnly()
	v = not v 
	C7FunctionLibrary.SetReportLogC7AndLogCrashSightOnly(v)
	Log.Info("set report-logc7andlogcrashsight-only:"..tostring(v))
end
local function GMSetReportWithLuaTraceOnly()
	local C7FunctionLibrary = import("C7FunctionLibrary")
	-- 默认bReportWithLuaTraceOnly=true
	local v = C7FunctionLibrary.GetReportWithLuaTraceOnly()
	v = not v
	C7FunctionLibrary.SetReportWithLuaTraceOnly(v)
	Log.Info("set report-luatrace-only:"..tostring(v))
end
local function GMSetReportErrorsInPIE()
	local C7FunctionLibrary = import("C7FunctionLibrary")
	-- 默认bReportErrorsInPIE=false
	local v = C7FunctionLibrary.GetReportErrorsInPIE()
	v = not v
	C7FunctionLibrary.SetReportErrorsInPIE(v)
	Log.Info("set report-inPIE:"..tostring(v))
end
local function GMSetReportErrorsInRuntime()
	local C7FunctionLibrary = import("C7FunctionLibrary")
	-- 默认bReportErrorsInRuntime=true
	local v = C7FunctionLibrary.GetReportErrorsInRuntime()
	v = not v
	C7FunctionLibrary.SetReportErrorsInRuntime(v)
	Log.Info("set report-InRuntime:"..tostring(v))
end

-- 所有程序定义的GM指令和按钮
local DevGMConfigs =
{
	{"测试材质性能", "无参数", GMTestMaterial},
	{"开启所有Error（包含无lua堆栈）", "无参数", GMSetReportWithLuaTraceOnly},
	{"开启所有Error（包含所有Category）", "无参数", GMSetReportLogC7AndLogCrashSightOnly},
	{"开启PIE编辑器Error上报", "无参数", GMSetReportErrorsInPIE},
	{"关闭运行时Error上报", "无参数", GMSetReportErrorsInRuntime},
}

-- 默认添加到‘常用’的GM面板当中
-- @param GMManager GM管理器
function GMDevConfigRegister(gmManager)
	local innerContent = gmManager.UsualTable.Content
	for _, v in ipairs(DevGMConfigs) do
		table.insert(innerContent, 2,
			{
				Name = v[1],
				Description = v[2],
				Panel = "/GM/GMInputOutputPanel",
				NotSizeToContent = true,
				Content = v[3]
			})
	end
	Log.Info("Append Developer GM Configs!!!!")
end