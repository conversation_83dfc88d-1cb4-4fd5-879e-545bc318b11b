local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local White = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.LinearColor)
local Color = Game.ColorManager:GetColor("Common", "Grey", Game.ColorManager.Type.LinearColor)

---@class GM_Command_Item : UIListItem
---@field view GM_Command_ItemBlueprint
local GM_Command_Item = DefineClass("GM_Command_Item", UIListItem)
GM_Command_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GM_Command_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GM_Command_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GM_Command_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GM_Command_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GM_Command_Item:InitUIView()
end

---面板打开的时候触发
function GM_Command_Item:OnRefresh(data)
	self.view.Text_Item:SetText(data.Name)
	self.view.Btn_Click:SetBackgroundColor(data.bSelected and Color or White)
end

return GM_Command_Item
