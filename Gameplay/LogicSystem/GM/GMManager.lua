local LRUCache = kg_require("Shared.Container.LRUCache")
local json = require("Framework.Library.json")
local SubsystemBlueprintLibrary = import("SubsystemBlueprintLibrary")
local PakUpdateSubsystem = import("PakUpdateSubsystem")
local C7FunctionLibrary = import("C7FunctionLibrary")
local EPropertyClass = import("EPropertyClass")
local table_insert = table.insert

---@class GMManager
local GMManager = DefineClass("GMManager")

kg_require "Gameplay/LogicSystem/GM/LocalGM"
local ClientGMCommands = {}
GMExcelTable = GMExcelTable or {}
LowerToUpperIntegratedMap = LowerToUpperIntegratedMap or {}
function GMManager:ctor()
    ---@type function
    self.RetCallbackFunc = nil
    self.RetCallbackFuncCounter = nil
    self.CallbackResult = nil
    self.IntegratedMap = nil
    self.GMCounter = nil
    self.globalGMUObjects = {} --记录下创建的Root UObject对象，UnInit时移除
    
    --记录当前行的富文本Properties
    self.currentProperties = {}
end

function GMManager.Init()
    local TempGMExcelTable = Game.TableData.GetGMCommandsDataTable()
    for K, V in ksbcpairs(TempGMExcelTable) do
        GMExcelTable[string.lower(K)] = V
    end
    for K,V in ksbcpairs(Game.TableData.GetIntegratedCommandDataTable()) do
        LowerToUpperIntegratedMap[string.lower(K)] = K
    end
    GMManager.SearchMap = {}
    GMManager.IntegratedMap = {}
    GMManager.RetCallbackFuncCounter = 0
    GMManager.CallbackResult = {}
    GMManager.currentProperties = {}
    GMManager.ExtraMap = {}
    GMManager.IsCombatDataAllMsg = false
    GMManager.GMCounter = 1
    GMManager.DecorateGMConfigs()
    -- 先临时放到这里，其中有ExtraGM会用到上面的变量
    kg_require("Gameplay.LogicSystem.GM.ClientGMCommands", true)
    if import("GameplayStatics").DoesSaveGameExist("GMHostiryCache", 0) then
        GMManager.SaveGame = import("GameplayStatics").LoadGameFromSlot("GMHostiryCache", 0)
    end
    if not GMManager.SaveGame then
        GMManager.SaveGame = import("GameplayStatics").CreateSaveGameObject(slua.loadClass(
            UIAssetPath.BP_GMHistory
        ))
    end
    -- GMManager.SaveGameRef = UnLua.Ref(GMManager.SaveGame)
    GMManager.HistoryCache = LRUCache.new(20)
    if GMManager.SaveGame.History and GMManager.SaveGame.Params and
    GMManager.SaveGame.History:Length() == GMManager.SaveGame.Params:Length() then
        for i = GMManager.SaveGame.History:Length() - 1, 0, -1 do
            local paramlru = LRUCache.new(5)
            local strs = string.split(GMManager.SaveGame.Params:Get(i), ";")
            for j = 1, #strs do
                paramlru:set(strs[j], strs[j])
            end
            GMManager.HistoryCache:set(GMManager.SaveGame.History:Get(i), {
                Cmd = GMManager.SaveGame.History:Get(i),
                Params = paramlru
            })
        end
    end


    Game.EventSystem:AddListener(_G.EEventTypes.GAMELOOP_POST_GAMELOOP_STARTUP,GMManager,GMManager.OnGameloopStartup)
    if not import("C7FunctionLibrary").IsBuildShipping() then
        Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, "OnKeyInputAction", GMManager)
    end
end

function GMManager.Parallel(Para, Index)
    GMManager.Stack:Pop()
    GMManager.Stack:Push(Para)
    GMManager.IndexStack:Pop()
    GMManager.IndexStack:Push(Index)
    local presenter = UI.GetUI("GM_Main_Panel")
    if presenter then
        presenter:UpdateMenu(Index)
        presenter:UpdateSelections()
    end
end

function GMManager.Backward()
    GMManager.Stack:Pop()
    GMManager.IndexStack:Pop()
    local presenter = UI.GetUI("GM_Main_Panel")
    if presenter then
        if #GMManager.Stack._data <= 2 then
            presenter:SetBackwardVisibility(import("ESlateVisibility").Collapsed)
        end
        presenter:UpdateMenu(GMManager.IndexStack:Top())
        presenter:UpdateSelections()
    end
end

function GMManager.SetCombatDataAllMsg(Value)
    if Value == "1" then
        GMManager.IsCombatDataAllMsg = true
    else
        GMManager.IsCombatDataAllMsg = false
    end
end

function GMManager.DecorateGMConfigs()
    GMManager.configs = {Name = "GM_MENU",Content = {}}
    GMManager.UsualTable = {Name = '常用', Content = {}}
    GMManager.CustomTable = {Name = '自定义', Content = {}}
	GMManager.TestCase = {Name = 'TestCase', Content = {}}
    table.insert(GMManager.configs.Content, GMManager.UsualTable)
    table.insert(GMManager.configs.Content, GMManager.CustomTable)
	table.insert(GMManager.configs.Content, GMManager.TestCase)
    GMManager.SwitchIndex = 0
    table.insert(GMManager.UsualTable.Content, 1,
    {
        Name = "Command",
        Description = "请输入函数名和参数",
        Panel = "/GM/GMInputOutputPanel",
        NotSizeToContent = true,
        Content = function(...)
            local Map = {...}
            if Map[1] and GMManager.IntegratedMap[Map[1]] then
                Game.GMManager.ExecuteIntegrateCommand(...)
            else
                Game.GMManager.ExecuteSingleCommand(...)
            end
        end
    })

    table.insert(GMManager.UsualTable.Content, 2,
            {
                Name = "CreateLocalP3Avatar",
                Description = "创建本地P3对象",
                Panel = "/GM/GMInputOutputPanel",
                NotSizeToContent = true,
                Content = function(...)
                    local p3 = Game.EntityManager:getEntity("localP3")
                    if p3 then
                        return
                    end
                    local data = DeepCopy(Game.me)
                    data.Profession = Game.me.Profession
                    data.Name = "p3"
                    data.rpcMap = nil
                    data.ActorType = Game.me.ActorType
                    local entity = Game.GameSDK.entityManager:creatEntity("AvatarActor", data, "localP3", 1233211)
                    Game.GameSDK.entityManager:entitySlaveOf(Game.me.eid, "localP3")
                    entity:CreateDummyRemoteProxy()
                end
            })
    table.insert(GMManager.UsualTable.Content, 2,
            {
                Name = "释放新技能",
                Description = "技能ID",
                Panel = "/GM/GMInputOutputPanel",
                NotSizeToContent = true,
                Content = function(skillID)
                    skillID = tonumber(skillID)
                    Game.me:ReqCastSkillNew(skillID or 1)
                end
            })

	local GM_TestCase = kg_require("Gameplay.LogicSystem.GM.GM_TestCase")
	GM_TestCase.RegisterGM_TestCase()
	-- 码农方便增加GM指令
	local Dev = kg_require("Gameplay.LogicSystem.GM.GMDevDecorator.GMDevConfig")
	Dev.GMDevConfigRegister(GMManager)
	
    local CommandConfig = require("Gameplay.LogicSystem.GM.GMConfigs.GMCommandConfig").new()
    CommandConfig:Register(GMManager)
    CommandConfig = require("Gameplay.LogicSystem.GM.GMConfigs.UIGMConfig").new()
    CommandConfig:Register(GMManager)
    CommandConfig = require("Gameplay.LogicSystem.GM.GMConfigs.ToolsGMConfig").new()
    CommandConfig:Register(GMManager)
	


end

function GMManager.RecursivelyLoadData(Data)
    for key, value in pairs(Data.Content) do
        if type(value.Content) == "function" then
            GMManager.SearchMap[value.Name] = value
            if value.IsFrequently then
                table.insert(GMManager.UsualTable.Content, value)
            end
            if value.IsIntegrated then
                GMManager.IntegratedMap[string.lower(value.Command)] = 1
            end
        elseif type(value.Content) == "table" then
            GMManager.RecursivelyLoadData(value)
        end
    end
end

function GMManager.Register(Data)
    if Data then
        table.insert(GMManager.configs.Content, Data)
        GMManager.RecursivelyLoadData(Data)
    end
end

function GMManager.OnGameloopStartup()
    if import("C7FunctionLibrary").IsBuildShipping() then
		GMManager.checkForceOpenGM(1)
	else
		UI.ShowUI("GM_Overlay_Panel")
    end
	
	-- 这俩为什么不放一起!!
	UI.ShowUI("DebugFPS_Panel")
    UI.ShowUI("GM_DebugInfo_Panel")
end

---@private checkForceOpenGM 检查是否需要强制打开GM
---@param tryCount number 尝试次数
function GMManager.checkForceOpenGM(tryCount)
	if Game.AllInSdkManager.SdkData.KwaiGatewayZoneInfo and Game.AllInSdkManager.SdkData.KwaiGatewayZoneInfo.extJson then
		local versionConfigUrl = Game.AllInSdkManager.SdkData.KwaiGatewayZoneInfo.extJson.VersionConfigUrl
		if string.notNilOrEmpty(versionConfigUrl) then
			local versionUrls = string.split(versionConfigUrl, ";")
			if tryCount > #versionUrls then
				return
			end
			local versionUrl = versionUrls[tryCount]
			versionUrl = string.gsub(versionUrl, "{0}", SubsystemBlueprintLibrary.GetEngineSubsystem(PakUpdateSubsystem):GetResourceChannel())
			versionUrl = string.gsub(versionUrl, "{1}", Game.AllInSdkManager.SdkData.Channel)
			versionUrl = string.gsub(versionUrl, "https://", "")
			versionUrl = string.gsub(versionUrl, "http://", "")
			tryCount = tryCount + 1
			local callback = slua.createDelegate( function(result, content)
				if result then
					result, content = pcall(json.decode, content) -- luacheck: ignore
					if result and content.ShippingGMSwitch then
						UI.ShowUI("GM_Overlay_Panel")
					end
				else
					GMManager.checkForceOpenGM(tryCount)
				end
			end)
			C7FunctionLibrary.HttpPost(versionUrl, slua.Map(EPropertyClass.Str, EPropertyClass.Str), "", callback)
		end
	end
end

local function ParseCommandStr(CommandStr)
    if type(CommandStr) ~= "string" then
        Log.Warning("Failed Parse GM Command :" .. tostring(CommandStr) .. ", GM Command Is not a String")
        return
    end
    local Args = string.split(CommandStr, " ")
    local Command = Args[1]
    table.remove(Args, 1)
    local ArgsArray = Args

    return Command, ArgsArray
end

function GMManager.ExecuteClientCommand(Command, ...)
    if ClientGMCommands[Command] == nil or not _G.IsCallable(ClientGMCommands[Command]) then
        local ErroMsg =
            "Failed Execute Client GM Command : Unkown Command " .. tostring(Command) .. ", Command not registered."
        Log.Warning(ErroMsg)
        GMManager.ExecuteRetCallback({bSucc = false, Content = ErroMsg})
        return
    end

    local XpCallSucc, InteralSucc, RetText = xpcall(ClientGMCommands[Command], _G.CallBackError, ...)
    if XpCallSucc then
        if InteralSucc == nil then
            InteralSucc = true
        end

        if RetText == nil then
            if InteralSucc then
                RetText = "GM Command Execute Succ"
            else
                RetText = "GM Command Execute Failed"
            end
        end
        GMManager.ExecuteRetCallback({bSucc = InteralSucc, Content = RetText})
    else
        GMManager.ExecuteRetCallback({bSucc = false, Content = "Failed Execute Client GM Command : Command Execution Error"})
    end

end

function GMManager.ExecuteServerCommand(Command, ...)
    local ArgsArray = table.pack(...)
    ---map the Args and keys in excel

    local ArgsKeyTable = {}
    local ArgKeys = GMExcelTable[Command].Args

    for Idx, Key in ksbcpairs(ArgKeys) do
        ArgsKeyTable[Key] = ArgsArray[Idx]
    end

    if Game.me then
        Game.me:ReqGM(Command, ArgsKeyTable)
    else
        Log.Debug("Server Command Execute Err: Server Not Connected")
        GMManager.ExecuteRetCallback({bSucc = false, Content = "Server Command Execute Err: Server Not Connected"})
    end
end

--输入可以是单独/组合指令
function GMManager.DispatchCommand(CommandStr)
    local oriCommand, ArgsArray = ParseCommandStr(CommandStr)
    local Command = string.lower(oriCommand)
    if GMManager.IntegratedMap[Command] then
        GMManager.ExecuteIntegrateCommand(Command, table.unpack(ArgsArray))
    else
        GMManager.ExecuteCommand(CommandStr)
    end
end

---输入只能是单指令
function GMManager.ExecuteCommand(CommandStr)
    local Command, ArgsArray = ParseCommandStr(CommandStr)
    return GMManager.ExecuteCommandArgs(Command, table.unpack(ArgsArray))
end

--输入只能是单指令
function GMManager.ExecuteCommandArgs(Command, ...)
    if GMManager.ExtraMap[Command] then
        return GMManager.ExecuteExtraCommand(Command, ...)
    end

    Command = string.lower(Command)

    if GMExcelTable[Command] == nil then
        Log.Warning("Failed Execute GM Command : Unkown Command " .. tostring(Command))
        if(GMManager[Command]) then
            GMManager[Command](...)
        end
        return
    end

    if GMExcelTable[Command].bIsServerCommand then
        return GMManager.ExecuteServerCommand(Command, ...)
    else
        return GMManager.ExecuteClientCommand(Command, ...)
    end
end

function GMManager.RegisterGMCommand(Command, func)
    if type(Command) ~= "string" then
        Log.Warning("Failed Register GM Command :" .. tostring(Command) .. ", GM Command Is not a String")
        return
    end

    Command = string.lower(Command)
    if ClientGMCommands[Command] ~= nil then
        Log.Warning("Failed Register GM Command : Found Duplicate GM Command --" .. tostring(Command))
        return
    end

    ClientGMCommands[Command] = func
end
--输入单指令
function GMManager.ExecuteSingleCommand(Command, ...)

    GMManager.SetRetCallbackFunc(
        function (Result)
            local CurrenTime = os.date("%H:%M:%S", os.time())
            local cmd = GMExcelTable[string.lower(Command)].Name
            local DisplayString = string.format("Idx:(%d) Cmd:%s [%s]: %s", GMManager.GMCounter, cmd, CurrenTime,Result.Content)
            if GMManager.GMCounter < 99 then
                GMManager.GMCounter = GMManager.GMCounter + 1
            else
                GMManager.GMCounter = 1
            end
            local Lines = string.split(DisplayString,"\n")
            DisplayString = GMManager.BuildRichText(Result, Lines)
            
            Log.Debug(DisplayString)
            UI.Invoke("GM_Main_Panel", "OnLog", DisplayString)
        end
    )
    GMManager.ExecuteCommandArgs(Command, ...)
end

--构建富文本
function GMManager.BuildRichText(Result, Lines)
    local outString = ""
    for _,Line in pairs(Lines) do
        table.clear(GMManager.currentProperties)
        if Result.bSucc or Result[1] and Result[1].bSucc then
            GMManager.currentProperties["size"] = 26
            outString = outString .. GMManager.RichTextToString(Line, "Default")
        else
            local TableName = "Common"
            local Prefix = "DT_"
            if string.sub(TableName, 1, #Prefix) == Prefix then
                TableName = string.sub(TableName, #Prefix + 1, -1)
            end
            GMManager.currentProperties["tablecolor"] = TableName .. "_" .. "Red"
            GMManager.currentProperties["size"] = 26
            outString = outString .. GMManager.RichTextToString(Line, "Default")
        end
        outString = outString .. "\n"
    end
    return outString
end

--将单行富文本转换为字符串
function GMManager.RichTextToString(Content, Tag)
    if #GMManager.currentProperties > 0 and Tag == nil then
        Tag = "default"
    end
    local PropertiesString = ""
    for K,V in pairs(GMManager.currentProperties) do
        local s = string.format("%s %s=\"%s\"", PropertiesString, tostring(K), tostring(V))
        PropertiesString = PropertiesString .. " " .. tostring(K) .. "=" .. "\"" ..tostring(V).. "\""
        Log.Debug(s == PropertiesString)
    end
    if Tag == nil then
        return tostring(Content)
    end

    if Content == nil then
        return string.format('<%s%s/>', tostring(Tag), PropertiesString)
    else
        return string.format('<%s%s>%s</>', tostring(Tag), PropertiesString, tostring(Content))
    end
end

--输入组合指令
function GMManager.ExecuteIntegrateCommand(Command, ...)
    local Params = {...}
    Command = string.lower(Command)
    local Args = Game.TableData.GetIntegratedCommandDataRow(LowerToUpperIntegratedMap[Command]).Args
    GMManager.SetRetCallbackFuncCounter(#Args)
    GMManager.SetRetCallbackFunc(
        function (Result)
            local CurrenTime = os.date("%H:%M:%S", os.time())
            local Display = ""
            local Counter = GMManager.GMCounter
            if GMManager.GMCounter < 99 then
                GMManager.GMCounter = GMManager.GMCounter + 1
            else
                GMManager.GMCounter = 1
            end
            for key, val in pairs(Result) do
                local cmd = Game.TableData.GetIntegratedCommandDataRow(LowerToUpperIntegratedMap[Command]).Name
                local DisplayString = string.format("Idx:(%d) Cmd:%s [%s]: %s", Counter, cmd, CurrenTime,val.Content)
                local Lines = string.split(DisplayString,"\n")
                DisplayString = GMManager.BuildRichText(Result, Lines)
                Display = Display .. DisplayString
            end
            UI.Invoke("GM_Main_Panel", "OnLog", Display)
        end
    )
    local i = 1
    for key, value in ksbcpairs(Args) do
        local str = value
        local t, p-- luacheck: ignore
        while string.find(str, "%$",t, p) and i <= #Params do
            str = string.gsub(str, "%$", Params[i], 1)
            i = i + 1
        end
        GMManager.ExecuteCommand(str)
    end
end

function GMManager:RegExtraServerGM(gmMap)
    for category, key2GM in pairs(gmMap) do
        for cmd, gmInfo in pairs(key2GM) do
            gmInfo.Name = gmInfo.name
            local args = {}
            for i, argInfo in ipairs(gmInfo.args) do
                args[i] = argInfo[1]
            end
            gmInfo.Args = args
            gmInfo.Description = gmInfo.desc
            gmInfo.Command = cmd
            gmInfo.Category = category
            self:innerRegExtraGM(category, cmd, gmInfo, true)
        end
    end
end

function GMManager:RegisterExtraCommand(category, cmd, name, desc, func)
    self:innerRegExtraGM(category, cmd, {
        Args = {},
        Name = name,
        Command = cmd,
        Description = desc,
        func = func
    }, false)
end

function GMManager:innerRegExtraGM(category, cmd, gmInfo, isServer)
    -- 和现有GM系统的参数格式保持一致
    gmInfo.bIsServerCommand = isServer
    self.ExtraMap[cmd] = gmInfo
    gmInfo.Content = function(...)
        Game.GMManager.ExecuteExtraCommand(cmd, ...)
    end

    local hasCategory = false
    for _, info in ipairs(self.configs.Content) do
        if info.Name == category then 
            for _, info in pairs(info.Content) do
                if info.Command == cmd then
                    -- 不必加入GM列表
                    return
                end
            end
            hasCategory = true
            table_insert(info.Content, gmInfo)
            break
        end
    end

    if not hasCategory then
        table_insert(self.configs.Content, {
            Content = { gmInfo },
            Name = category
        })
    end
    self.SearchMap[gmInfo.Name] = gmInfo
end

--输入单指令
function GMManager.ExecuteExtraCommand(Command, ...)
    local ExtraMap = GMManager.ExtraMap
    GMManager.SetRetCallbackFunc(
        function (Result)
            local CurrenTime = os.date("%H:%M:%S", os.time())
            local cmd = ExtraMap[Command].Name
            local DisplayString = string.format("Idx:(%d) Cmd:%s [%s]: %s", GMManager.GMCounter, cmd, CurrenTime,Result.Content)
            if GMManager.GMCounter < 99 then
                GMManager.GMCounter = GMManager.GMCounter + 1
            else
                GMManager.GMCounter = 1
            end
            local Lines = string.split(DisplayString,"\n")
            DisplayString = GMManager.BuildRichText(Result, Lines)
            
            Log.Debug(DisplayString)
            UI.Invoke("GM_Main_Panel", "OnLog", DisplayString)
        end
    )
    local gmInfo = ExtraMap[Command]
    if gmInfo == nil then
        Log.Warning("Failed Execute GM Command : unknown Command " .. tostring(Command))
        return
    end

    if gmInfo.bIsServerCommand then
        if Game.me then
            local args = {}
            for i, key in ipairs(gmInfo.Args) do
                args[key] = select(i, ...)
            end
            Game.me:ReqExtraGM(Command, args)
        end
    else
        gmInfo["func"](...)
    end
end


---Ret
function GMManager.SetRetCallbackFunc(func)
	if type(func) == "function" then
		GMManager.RetCallbackFunc = func
	end
end

function GMManager.SetRetCallbackFuncCounter(Index)
	GMManager.RetCallbackFuncCounter = Index
end

function GMManager.ExecuteRetCallback(Result)
	if GMManager.RetCallbackFunc and 
	type(GMManager.RetCallbackFunc) == "function" then
		if GMManager.RetCallbackFuncCounter and GMManager.RetCallbackFuncCounter > 0 then
			GMManager.RetCallbackFuncCounter = GMManager.RetCallbackFuncCounter - 1
			table.insert(GMManager.CallbackResult, Result)
			if GMManager.RetCallbackFuncCounter == 0 then
				GMManager.RetCallbackFunc(GMManager.CallbackResult)
				table.clear(GMManager.CallbackResult)
				GMManager.RetCallbackFunc = nil
			end
		else
			GMManager.RetCallbackFunc(Result)
			GMManager.RetCallbackFunc = nil
		end
	end
end




---UI展示
function GMManager.ShowPanel(InPanelType, Params)
    UI.ShowUI(InPanelType, Params)
end

function GMManager.ShowGM()
	local uid = "GM_Main_Panel"
	if (not UI.GetUI(uid)) then
		--UI.ShowUI(uid)
		Game.UIManager:OpenPanel(uid)
	else
		local topUI = Game.UIManager:GetTopUI()
		if topUI.uid ~= uid then
			Game.UIManager:OpenPanel(uid) -- bring to front
		else
    		Game.UIManager:ShowPanel(uid, uid)
		end
	end
end

function GMManager.HideGM()
    if UI.GetUI("GM_Main_Panel") ~= nil then
    	Game.UIManager:ClosePanel("GM_Main_Panel")
        --UI.HideUI("GM_Main_Panel")
    end
end

function GMManager:AddGlobalGMUObject(object)
    if IsValid_L(object) then
        self.globalGMUObjects[#self.globalGMUObjects + 1] = object
        object:AddToRoot()
    end
end

function GMManager:RemoveGlobalGMUObject(object)
    table.removeItem(self.globalGMUObjects, object)
    if IsValid_L(object) then
        object:RemoveFromRoot()
    end
end

function GMManager:UnInit()
	if GMManager.SaveGame and GMManager.HistoryCache then
		GMManager.SaveGame.History:Clear()
		GMManager.SaveGame.Params:Clear()
		local node = GMManager.HistoryCache.head.next
		while node.next do
			GMManager.SaveGame.History:Add(node.data.Cmd)
			local strs = ""
			local paramnode = node.data.Params.head.next
			while paramnode.next do
				if paramnode.data ~= "" then
					strs = strs .. paramnode.data .. ";"
				end
			 paramnode = paramnode.next
			end
			if string.sub(strs, -1) then
				strs = string.sub(strs, 1, #strs - 1)
			end
			GMManager.SaveGame.Params:Add(strs)
			node = node.next
		end
		import("GameplayStatics").SaveGameToSlot(GMManager.SaveGame, "GMHostiryCache", 0)
	end
    for i, v in pairs(self.globalGMUObjects) do
        self:RemoveGlobalGMUObject(v)
    end
    -- Unlua.Unref(GMManager.SaveGame)
    --UI.HideUI("GM_Main_Panel")
    --UI.HideUI("GM_Overlay_Panel")
	Game.GlobalEventSystem:RemoveTargetAllListeners(GMManager)
end

function GMManager:OnKeyInputAction(actionName, inputEvent)
    if import("C7FunctionLibrary").IsBuildShipping() then
        return
    end
    if not actionName or inputEvent == 1 then
        return
    end
    if actionName == "DebugPlayerPos_Action" then
		---@type ActorBase
		local playerEntity = Game.me
		if playerEntity then
			local position = playerEntity.CppEntity:KAPI_GetLocation()
            local msg = "X=" .. math.round(position.X) .. ", Y=" .. math.round(position.Y) .. ", Z=" ..  math.round(position.Z)
            import("UIFunctionLibrary").CopyToClipBoard(msg)
		end
    end
end

return GMManager
