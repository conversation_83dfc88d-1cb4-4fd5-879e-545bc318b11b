local LRUCacheEnv = kg_require("Shared.Container.LRUCache")
local GM_LongInput_Item = kg_require("Gameplay.LogicSystem.GM.GM_LongInput_Item")
local GM_Menu_Item = kg_require("Gameplay.LogicSystem.GM.GM_Menu_Item")
local GM_Command_Item = kg_require("Gameplay.LogicSystem.GM.GM_Command_Item")
local UIFunctionLibrary = import("UIFunctionLibrary")
local ESlateVisibility = import("ESlateVisibility")
local Stack = require "Framework.Library.Stack"
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

local MenuHistoryIndex = -1
local MenuFavoritesIndex = -2

---@class GM_Main_Panel : UIPanel
---@field view userdata GM_Main_PanelBlueprint
local GM_Main_Panel = DefineClass("GM_Main_Panel", UIPanel)

GM_Main_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GM_Main_Panel:OnCreate()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

---初始化数据
function GM_Main_Panel:InitUIData()
	self.OnExec = nil
	self.historyParamsVisible = false
	self.SelectedButtonIndex = 0
	self.SelectedMenuIndex = 0
	self.ComboboxData = {}
	self.MenuData = {}
	self.CommandsData = {}  ---@type GM_Main_PanelParam[]
	self.ParamsData = {} ---@type table<string, string>[]
	self.InputData = {}
	self.Stack = Stack.new()
	self.Stack:Push({
		Info = Game.GMManager.configs,
		Index = 1
	})
	self.Stack:Push({
		Info = Game.GMManager.configs.Content[1],
		Index = 1
	})
	
	self.White = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.LinearColor)
	self.Color = Game.ColorManager:GetColor("Common", "Red", Game.ColorManager.Type.LinearColor)

	self.IsIntegrated = nil
end

--- UI组件初始化，此处为自动生成
function GM_Main_Panel:InitUIComponent()
    ---@type GM_LongInput_Item
    self.Input_SearchCom = self:CreateComponent(self.view.Input_Search, GM_LongInput_Item)
    ---@type UIListView childScript: GM_LongInput_Item
    self.List_GMInputCom = self:CreateComponent(self.view.List_GMInput, UIListView)
    ---@type GM_LongInput_Item
    self.Input_CommandCom = self:CreateComponent(self.view.Input_Command, GM_LongInput_Item)
    ---@type UIListView childScript: GM_Text_Item
    self.List_HistoryParamsCom = self:CreateComponent(self.view.List_HistoryParams, UIListView)
    ---@type UIListView childScript: GM_Command_Item
    self.Tile_SelectionsCom = self:CreateComponent(self.view.Tile_Selections, UIListView)
    ---@type UIListView childScript: GM_Menu_Item
    self.List_MenuCom = self:CreateComponent(self.view.List_Menu, UIListView)
end

---UI事件在这里注册，此处为自动生成
function GM_Main_Panel:InitUIEvent()
	self:AddUIEvent(self.view.Input_Search.Text_Input.OnTextCommitted, "on_Input_Search_Commited")
	self:AddUIEvent(self.view.Input_Command.Text_Input.OnTextCommitted, "on_Btn_Command_Commited")
    self:AddUIEvent(self.view.Btn_Close.OnClicked, "on_Btn_Close_Clicked")
    self:AddUIEvent(self.view.Btn_Console.OnClicked, "on_Btn_Console_Clicked")
    self:AddUIEvent(self.view.Btn_Execute.OnClicked, "on_Btn_Execute_Clicked")
    self:AddUIEvent(self.view.Btn_HistoryParams.OnClicked, "on_Btn_HistoryParams_Clicked")
    self:AddUIEvent(self.view.Btn_Search.OnClicked, "on_Btn_Search_Clicked")
    self:AddUIEvent(self.view.Btn_ShowHistory.OnClicked, "on_Btn_ShowHistory_Clicked")
    self:AddUIEvent(self.view.Btn_Switch.OnClicked, "on_Btn_Switch_Clicked")
    self:AddUIEvent(self.Tile_SelectionsCom.onItemSelected, "on_Tile_SelectionsCom_ItemSelected")
    self:AddUIEvent(self.view.Btn_Backward.OnClicked, "on_Btn_Backward_Clicked")
    self:AddUIEvent(self.List_MenuCom.onItemSelected, "on_List_MenuCom_ItemSelected")
    self:AddUIEvent(self.List_HistoryParamsCom.onItemSelected, "on_List_HistoryParamsCom_ItemSelected")
    self:AddUIEvent(self.view.Btn_Favorite.OnClicked, "on_Btn_Favorite_Clicked")
    self:AddUIEvent(self.view.Btn_ShowFavorites.OnClicked, "on_Btn_ShowFavorites_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GM_Main_Panel:InitUIView()
	self.view.Input_Search.Text_Name:SetText("搜索")
	self.view.Input_Command.Text_Name:SetText("输入")
end

---面板打开的时候触发
function GM_Main_Panel:OnRefresh(...)
	local saveData = Game.GMManager.SaveGame
	local index = saveData.LastMenuIndex
	local lastCommandIndex = saveData.LastCommandIndex
	if saveData and index > 0 then
		self.SelectedMenuIndex = index
	end
	self:UpdateBackwardVisible()
	self:UpdateMenu()
	self:UpdateSelections()
	self:UpdateExecutionInterface()
	
	if index == MenuFavoritesIndex then
		self:on_Btn_ShowFavorites_Clicked()
	elseif index == MenuHistoryIndex then
		self:on_Btn_ShowHistory_Clicked()
	elseif self.MenuData[self.SelectedMenuIndex] then
		self:on_List_MenuCom_ItemSelected(self.SelectedMenuIndex, self.MenuData[self.SelectedMenuIndex])
	end

	--if self.CommandsData[lastCommandIndex] then
	--	self:on_Tile_SelectionsCom_ItemSelected(lastCommandIndex, self.CommandsData[lastCommandIndex])
	--end
	--
	--self.view.Input_Command.Text_Input:SetText(saveData.LastCommandText)
	
	self:StartTimer("GameTimeTickTimer", function()
		local time = Game.ClimateSystem and Game.ClimateSystem.GameTime
		if time then
			self.view.Text_GameTime:SetText(string.format("%d:%d:%d", time.hour, time.min, time.sec))
		end
	end, 0.25, -1)
end

function GM_Main_Panel:OnClose()
	self:StopAllTimer()
end

function GM_Main_Panel:UpdateMenu()
	local Top = self.Stack._data[#self.Stack._data - 1]
	if not Top or not Top.Info then
		return
	end
	if type(Top.Info) ~= "table" or Top.Info.Name == nil or Top.Info.Content == nil then
		Log.Warning("Failed to load GM config, Please check your GM configuration")
		return
	end
	table.clear(self.MenuData)
	for key, value in pairs(Top.Info.Content) do
		if not _G.IsCallable(value.Content) then
			table.insert(self.MenuData, value)
			value.bSelected = key == self.SelectedMenuIndex
		end
	end
	self.List_MenuCom:Refresh(self.MenuData)
	local Path = "/"
	for _, V in pairs(self.Stack._data) do
		Path = Path .. tostring(V.Info.Name) .. "/"
	end
	self.view.Text_Path:SetText(Path)
	self.historyParamsVisible = false
	if self.SelectedMenuIndex > 0 then
		self.List_MenuCom:ScrollToItemByIndex(self.SelectedButtonIndex)
	end
end

function GM_Main_Panel:UpdateSelections()
	local Top = self.Stack:Top()
	if not Top or not Top.Info then
		return
	end
	if type(Top) ~= "table" or Top.Info.Name == nil or Top.Info.Content == nil then
		return
	end
	self.CommandsData = Top.Info.Content
	for index, value in pairs(self.CommandsData) do
		value.bSelected = index == self.SelectedButtonIndex 
	end
	self.Tile_SelectionsCom:Refresh(self.CommandsData)
end

function GM_Main_Panel:UpdateExecutionInterface()
	self.List_HistoryParamsCom:SetVisible(self.historyParamsVisible)
	local Params = self.CommandsData[self.SelectedButtonIndex]
	if not Params then
		self.OnExec = nil
		self.KeyOfExec = nil
		self.InputData = {}
		self.List_GMInputCom:Clear()
		self.view.Input_Command.Text_Input:SetText("")
		self.view.RichT_Desc:SetText("")
		return
	end
	self.OnExec = Params.Content
	self.KeyOfExec = Params.Name
	self.view.Input_Command.Text_Input:SetText("")
	
	self:UpdateHistoryParams()
	
	self.IsIntegrated = Params.IsIntegrated
	if not Params.Description then
		Params.Description = ""
	end

	if not self.IsIntegrated and Params.Params then
		self.InputData = Params.Params
	elseif self.IsIntegrated and Params.UndefinedParams then
		self.InputData = Params.UndefinedParams
	else
		self.InputData = {}
	end
	self.List_GMInputCom:Refresh(self.InputData)
	self.view.Text_CommandName:SetText(Params.Command or "")
	self.view.RichT_Desc:SetText(
		'<TCom size="26"> ' .. Params.Name .. ': ' .. Params.Description .. "</>")
	
	self:UpdateFavoriteBtn()
end

function GM_Main_Panel:UpdateHistoryParams()
	local Params = self.CommandsData[self.Tile_SelectionsCom:GetSelectedItemIndex()]
	self.ComboboxData = {}
	local HistoryCache = Game.GMManager and Game.GMManager.HistoryCache
	if Params and HistoryCache and HistoryCache.cache[Params.Name] then
		local node = HistoryCache.cache[Params.Name].data.Params.head.next
		while node.next do
			if node.data ~= "" then
				table.insert(self.ComboboxData, { text = node.data })
			end
			node = node.next
		end
	end
	if #self.ComboboxData ~= 0 then
		self.List_HistoryParamsCom:Refresh(self.ComboboxData)
		self.view.Btn_HistoryParams:SetVisibility(import("ESlateVisibility").Visible)
	else
		self.view.Btn_HistoryParams:SetVisibility(import("ESlateVisibility").Collapsed)
	end
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_Close_Clicked()
	Log.Debug("GM Main Panel Closed")
	Game.GMManager.HideGM();
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_HistoryParams_Clicked()
	self.historyParamsVisible = not self.historyParamsVisible
	self.List_HistoryParamsCom:SetVisible(self.historyParamsVisible)
end

function GM_Main_Panel:on_Input_Search_Commited()
	self:on_Btn_Search_Clicked()
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_Search_Clicked()
	local searchText = self.view.Input_Search.Text_Input:GetText()
	local CurrentTable = {}
	local pattern = ""
	for i = 1, utf8.len(searchText) do -- Todo: 全 to lower 比较就够了吧
		local str = utf8.sub(searchText, i, i + 1)
		if str:match("%a") then
			-- Check if the character is a letter
			local lower = str:lower()
			local upper = str:upper()
			pattern = pattern .. "[" .. lower .. upper .. "]" .. "(.*)"
		else
			pattern = pattern .. str .. "(.*)"
		end
	end
	for key, value in pairs(Game.GMManager.SearchMap) do
		if string.find(key, pattern) then
			table.insert(CurrentTable, value)
		end
	end
	self.CommandsData = CurrentTable
	self.Tile_SelectionsCom:Refresh(self.CommandsData)
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_Console_Clicked()
	UIFunctionLibrary.OpenConsole()
end

function GM_Main_Panel:on_Btn_Command_Commited(_, commitType)
	if commitType == 1 then
		self:on_Btn_Execute_Clicked()
	end
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_Execute_Clicked()
	if not self.OnExec then
		return
	end

	local InputTexts = self.view.Input_Command.Text_Input:GetText()
	local bGmSuccess = nil
	if self.view.Swit_Command:GetActiveWidgetIndex() == 0 then
		local retStrTable = string.split(InputTexts, " ")
		if _G.IsCallable(self.OnExec) then
			bGmSuccess, _ = xpcall(self.OnExec, _G.CallBackError, table.unpack(retStrTable))
		end
	else
		local Inputs = {}
		for i = 1, #self.InputData do
			table.insert(Inputs, self.List_GMInputCom:GetItemByIndex(i).view.Text_Input:GetText())
		end
		if _G.IsCallable(self.OnExec) then
			bGmSuccess, _ = xpcall(self.OnExec, _G.CallBackError, table.unpack(Inputs))
		end
	end

	local HistoryCache = Game.GMManager.HistoryCache
	if self.KeyOfExec and bGmSuccess == true and HistoryCache then
		if HistoryCache:get(self.KeyOfExec) then
			local data = HistoryCache:get(self.KeyOfExec)
			data.Params:set(InputTexts, InputTexts)
		else
			local lru = LRUCacheEnv.new(5)
			lru:set(InputTexts, InputTexts)
			HistoryCache:set(self.KeyOfExec, {
				Cmd = self.KeyOfExec,
				Params = lru
			})
		end
		local saveData = Game.GMManager.SaveGame
		if saveData then
			saveData.LastCommandText = InputTexts
		end
		self:UpdateHistoryParams()
	end
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_ShowHistory_Clicked()
	self:UnSelectMenuAndCommand()
	
	local CurrentTable = {}
	table.clear(CurrentTable)
	if Game.GMManager.HistoryCache then
		local node = Game.GMManager.HistoryCache.head.next
		while node.next do
			table.insert(CurrentTable, Game.GMManager.SearchMap[node.key])
			node = node.next
		end
	end
	
	self.CommandsData = CurrentTable
	Game.GMManager.SaveGame.LastMenuIndex = MenuHistoryIndex
	self.Tile_SelectionsCom:Refresh(CurrentTable)
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_Switch_Clicked()
	if self.view.Swit_Command:GetActiveWidgetIndex() == 1 then
		self.view.Swit_Command:SetActiveWidgetIndex(0)
	else
		self.view.Swit_Command:SetActiveWidgetIndex(1)
	end
end

--- 此处为自动生成
---@param index number
---@param data table
function GM_Main_Panel:on_Tile_SelectionsCom_ItemSelected(index, data)
	local currentSelected = data
	if not currentSelected then
		return
	end

	local lastSelected = self.CommandsData[self.SelectedButtonIndex]
	if lastSelected then
		lastSelected.bSelected = false
		self.Tile_SelectionsCom:RefreshItemByIndex(self.SelectedButtonIndex)
	end
	self.SelectedButtonIndex = index
	currentSelected.bSelected = true
	self.Tile_SelectionsCom:RefreshItemByIndex(index)
	self.historyParamsVisible = false
	
	if _G.IsCallable(currentSelected.Content) then
		self:UpdateExecutionInterface()
	else
		self:IStack_Forward(currentSelected, index)
	end
	
	local saveData = Game.GMManager.SaveGame
	if saveData then
		saveData.LastCommandIndex = index
	end
end

function GM_Main_Panel:UpdateBackwardVisible()
	local visibility = #self.Stack._data > 2 and ESlateVisibility.Visible or ESlateVisibility.Collapsed
	self.view.VB_Backward:SetVisibility(visibility)
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_Backward_Clicked()
    if #self.Stack._data > 2 then
        self:IStack_Backward()
    end
end

--- 此处为自动生成
---@param index number
---@param data table
function GM_Main_Panel:on_List_MenuCom_ItemSelected(index, data)
	if data.Panel ~= nil then
		Game.GMManager.ShowPanel(data.Panel, {Params = data}) -- 兼容
	elseif _G.IsCallable(data.Content) then
		xpcall(data.Content, _G.CallBackError)
	else
		local lastSelected = self.MenuData[self.SelectedMenuIndex]
		if lastSelected then
			lastSelected.bSelected = false
			self.List_MenuCom:RefreshItemByIndex(self.SelectedMenuIndex)
		end
		self.SelectedMenuIndex = index
		data.bSelected = true
		self.List_MenuCom:RefreshItemByIndex(index)
		
		self.SelectedButtonIndex = 0
		
		self:IStack_Parallel(data, index)
		self:UpdateExecutionInterface()
	end
	
	local saveData = Game.GMManager.SaveGame
	if saveData then
		saveData.LastMenuIndex = index
	end
end

--- 此处为自动生成
---@param index number
---@param data table
function GM_Main_Panel:on_List_HistoryParamsCom_ItemSelected(index, data)
	self.view.Input_Command.Text_Input:SetText(
		data.text
	)
end

---维护文件夹树结构
function GM_Main_Panel:IStack_Forward(Next)
    self.Stack:Push({
        Info = Next,
        Index = 1
    })
	self:UpdateBackwardVisible()
    self:UpdateMenu()
    self:UpdateSelections()
end

function GM_Main_Panel:IStack_Parallel(Para, Index)
    self.Stack:Pop()
    self.Stack:Push({
        Info = Para,
        Index = Index
    })
    --self:UpdateMenu()
    self:UpdateSelections()
end

function GM_Main_Panel:IStack_Backward()
    self.Stack:Pop()
	self:UpdateBackwardVisible()
    self:UpdateMenu()
    self:UpdateSelections()
end


--- 此处为自动生成
function GM_Main_Panel:on_Btn_Favorite_Clicked()
	local data = self.CommandsData[self.SelectedButtonIndex]
	local saveData = Game.GMManager.SaveGame
	if not saveData or not data then
		return
	end

	local findIndex = -1
	for index, name in pairs(saveData.Favorites) do
		if name == data.Name then
			findIndex = index
			break
		end
	end
	if findIndex >= 0 then
		saveData.Favorites:Remove(findIndex)
	else
		saveData.Favorites:Add(data.Name)
	end
	
	self:UpdateFavoriteBtn()
end

function GM_Main_Panel:UpdateFavoriteBtn()
	local data = self.CommandsData[self.SelectedButtonIndex]
	local saveData = Game.GMManager.SaveGame
	if data and saveData then
		local currentState = saveData.Favorites:Contains(data.Name)
		self.view.Btn_Favorite:SetBackgroundColor(currentState and self.Color or self.White)
	end
end

--- 此处为自动生成
function GM_Main_Panel:on_Btn_ShowFavorites_Clicked()
	self:UnSelectMenuAndCommand()
	
	local CurrentTable = {}
	table.clear(CurrentTable) -- 此处需要一个SetNumber(Nil)
	local saveData = Game.GMManager.SaveGame
	if saveData then
		for _, key in pairs(saveData.Favorites) do
			local find = Game.GMManager.SearchMap[key]
			if find then
				table.insert(CurrentTable, find)
			end
		end
	end
	saveData.LastMenuIndex = MenuFavoritesIndex
	self.CommandsData = CurrentTable
	self.Tile_SelectionsCom:Refresh(CurrentTable)
end

function GM_Main_Panel:UnSelectMenuAndCommand()
	local lastSelected = self.CommandsData[self.SelectedButtonIndex]
	if lastSelected then
		lastSelected.bSelected = false
	end
	self.SelectedButtonIndex = 0
	local lastSelectedMenu = self.MenuData[self.SelectedMenuIndex]
	if lastSelectedMenu then
		lastSelectedMenu.bSelected = false
		self.List_MenuCom:SetSelectedItemByIndex(self.SelectedMenuIndex, false)
		self.List_MenuCom:RefreshItemByIndex(self.SelectedMenuIndex)
		self.SelectedMenuIndex = 0
	end
end

function GM_Main_Panel:OnLog(Text)
    if Text then
        self.view.RichT_Log:SetText(Text)
    end
end

return GM_Main_Panel
