local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class P_GM_FeedbackOverlayInner_Item : UIListItem
---@field view P_GM_FeedbackOverlayInner_ItemBlueprint
local P_GM_FeedbackOverlayInner_Item = DefineClass("P_GM_FeedbackOverlayInner_Item", UIListItem)

P_GM_FeedbackOverlayInner_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function P_GM_FeedbackOverlayInner_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function P_GM_FeedbackOverlayInner_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function P_GM_FeedbackOverlayInner_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function P_GM_FeedbackOverlayInner_Item:InitUIEvent()
    self:AddUIEvent(self.view.OnDragDetectedEvent, "on_P_GM_FeedbackOverlayInner_Item_DragDetectedEvent")
    self:AddUIEvent(self.view.OnDragCancelledEvent, "on_P_GM_FeedbackOverlayInner_Item_DragCancelledEvent")
    self:AddUIEvent(self.view.OnMouseMoveEvent, "on_P_GM_FeedbackOverlayInner_Item_MouseMoveEvent")
    self:AddUIEvent(self.view.Btn_Debug.OnClicked, "on_Btn_Debug_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function P_GM_FeedbackOverlayInner_Item:InitUIView()
end

---面板打开的时候触发
function P_GM_FeedbackOverlayInner_Item:OnRefresh(...)
end

---@param myGeometry FGeometry
---@param inDragDetectedEvent FPointerEvent

--- 此处为自动生成
function P_GM_FeedbackOverlayInner_Item:on_P_GM_FeedbackOverlayInner_Item_DragDetectedEvent(myGeometry, inDragDetectedEvent)
end

---@param inDragDropEvent FPointerEvent
---@param inDragDropOperation UDragDropOperation
--- 此处为自动生成
function P_GM_FeedbackOverlayInner_Item:on_P_GM_FeedbackOverlayInner_Item_DragCancelledEvent(inDragDropEvent, inDragDropOperation)
end

---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent

--- 此处为自动生成
function P_GM_FeedbackOverlayInner_Item:on_P_GM_FeedbackOverlayInner_Item_MouseMoveEvent(myGeometry, inMouseEvent)
end

--- 此处为自动生成
function P_GM_FeedbackOverlayInner_Item:on_Btn_Debug_Clicked()
end

return P_GM_FeedbackOverlayInner_Item
