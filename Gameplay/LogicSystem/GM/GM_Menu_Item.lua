local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local White = Game.ColorManager:GetColor("Common", "White", Game.ColorManager.Type.LinearColor)
local Black = Game.ColorManager:GetColor("Common", "Black", Game.ColorManager.Type.LinearColor)

---@class GM_Menu_Item : UIListItem
---@field view GM_Menu_ItemBlueprint
local GM_Menu_Item = DefineClass("GM_Menu_Item", UIListItem)
GM_Menu_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GM_Menu_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GM_Menu_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GM_Menu_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GM_Menu_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Click.OnClicked, "on_Btn_Click_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GM_Menu_Item:InitUIView()
end

---面板打开的时候触发
function GM_Menu_Item:OnRefresh(data)
	self.view.Text_Item:SetText(data.Name)
	self.view.Btn_Click:SetBackgroundColor(data.bSelected and White or Black)
end


--- 此处为自动生成
function GM_Menu_Item:on_Btn_Click_Clicked()
end

return GM_Menu_Item
