local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class GMCommonInteractorPanel : UIPanel
---@field view GMCommonInteractorPanelBlueprint
local GMCommonInteractorPanel = DefineClass("GMCommonInteractorPanel", UIPanel)

GMCommonInteractorPanel.eventBindMap = {
}

local table_clear = table.clear
local table_insert = table.insert

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GMCommonInteractorPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
	
	-- 因为通用交互物执行Action没有事件通知，单独为GM加的话不太合适，这里就做轮巡更新
	self:CreateRefreshTimer()
end

---初始化数据
function GMCommonInteractorPanel:InitUIData()
	self.data = {}
end

--- UI组件初始化，此处为自动生成
function GMCommonInteractorPanel:InitUIComponent()
    ---@type UIListView
    self.listViewCom = self:CreateComponent(self.view.ListView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function GMCommonInteractorPanel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Close.OnClicked, "on_Btn_Close_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GMCommonInteractorPanel:InitUIView()
end

---面板打开的时候触发
function GMCommonInteractorPanel:OnRefresh()
	self:RefreshListView()
	self.listViewCom:Refresh(self.data)
end

function GMCommonInteractorPanel:CreateRefreshTimer()
	self.refreshTimer = Game.TimerManager:CreateTimerAndStart(function()
		self:RefreshListView()
	end, 1 * 1000, -1)
end

function GMCommonInteractorPanel:ClearTimer()
	if self.refreshTimer then
		Game.TimerManager:StopTimerAndKill(self.refreshTimer)	
	end
end

function GMCommonInteractorPanel:RefreshListView()
	local data = {}
	local interactors = Game.CommonInteractorManager.CommonInteractors
	for instanceID, interactor in pairs(interactors) do
		local allActions = {}

		local interactorTableData = Game.TableData.GetCommonInteractorDataRow(interactor.interactorID)
		local stateList = interactorTableData.StateList
		if stateList then
			for _, stateID in ksbcpairs(stateList) do
				local stateTableData = Game.TableData.GetCommonInteractorStateDataRow(stateID)
				local actionList = stateTableData.ActionList

				if actionList then
					for k, actionID in ksbcpairs(actionList) do
						local actionInfo = string.format("%s: %s", actionID, interactor.actionExecuteTimes[actionID] or 0)
						table_insert(allActions, actionInfo)
					end
				end
			end
		end

		-- 分割allActions为每三个一组
		local chunks = {}
		for i = 1, #allActions, 3 do
			local chunk = {}
			for j = i, math.min(i + 2, #allActions) do
				table_insert(chunk, allActions[j])
			end
			table_insert(chunks, chunk)
		end
		
		local interactorInfo = string.format("[场编ID: %s], [ID: %s], [State: %s]",  interactor.insID, instanceID, interactor.curState)
		table_insert(data, interactorInfo)
		
		for _, chunk in ipairs(chunks) do
			local actionStr = table.concat(chunk, ", ")
			local actionsInfo = string.format("[Action: %s]", actionStr)
			table_insert(data, actionsInfo)
		end

		-- 插入空行
		table_insert(data, "")
	end

	-- 数据有变化再更新，否则列表滑动的时候会被中断
	local hasChanged = false
	local oldData = self.data
	if oldData then
		if #oldData ~= #data then
			hasChanged = true
		else
			for i = 1, #oldData do
				if oldData[i] ~= data[i] then
					hasChanged = true
					break
				end
			end
		end
	else
		hasChanged = true
	end

	if hasChanged then
		self.data = data
		self.listViewCom:Refresh(self.data)
	end
end

--- 此处为自动生成
function GMCommonInteractorPanel:on_Btn_Close_Clicked()
	Game.NewUIManager:ClosePanel(UIPanelConfig.GMCommonInteractorPanel)
end

function GMCommonInteractorPanel:OnClose()
	table_clear(self.data)
	self:ClearTimer()
end

return GMCommonInteractorPanel
