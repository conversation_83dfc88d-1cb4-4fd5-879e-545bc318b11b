local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDGMPanel : UIPanel
---@field view HUDGMPanelBlueprint
local HUDGMPanel = DefineClass("HUDGMPanel", UIComponent)

HUDGMPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDGMPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDGMPanel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDGMPanel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDGMPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDGMPanel:InitUIView()
end

function HUDGMPanel:GetEntitySummary()
	local EM = Game.EntityManager

	local avatarCount = EM.avatarCount
	local npcCount = EM.npcCount
	local briefCount = EM.briefCount

	local top1Type, top1Count = nil, 0
	local top2Type, top2Count = nil, 0
	local top3Type, top3Count = nil, 0

	for etype, count in pairs(EM.localEntityCountMap) do
		if count > top1Count then
			top3Type, top3Count = top2Type, top2Count
			top2Type, top2Count = top1Type, top1Count
			top1Type, top1Count = etype, count
		elseif count > top2Count then
			top3Type, top3Count = top2Type, top2Count
			top2Type, top2Count = etype, count
		elseif count > top3Count then
			top3Type, top3Count = etype, count
		end
	end

	local topTypesMsg = ""
	if top1Type then
		topTypesMsg = string.format("%s: %d", top1Type, top1Count)
	end
	if top2Type then
		topTypesMsg = topTypesMsg .. string.format(", %s: %d", top2Type, top2Count)
	end
	if top3Type then
		topTypesMsg = topTypesMsg .. string.format(", %s: %d", top3Type, top3Count)
	end

	local msg = string.format("AvatarCount: %d, NpcCount: %d, BriefEntities: %d\n%s", avatarCount, npcCount, briefCount, topTypesMsg)
	return msg
end


---面板打开的时候触发
function HUDGMPanel:OnRefresh(bPrint)
	self:StartTimer("PlayerInfo", function()
		local playerEntity = Game.me 
		if playerEntity then
			local position = playerEntity:GetPosition()
			local rotation = playerEntity:GetRotation()
			local localSpace = Game.NetworkManager.GetLocalSpace()
			local msg = "LevelMap ID=" .. (localSpace and localSpace:GetPlaneIDOrMapID() or 0)
			msg = msg .. "\nPosition X=" .. self.round(position.X) .. ", Y=" .. self.round(position.Y) .. ", Z=" ..  self.round(position.Z)
			msg = msg .. "\nRotation Pitch=" ..  self.round(rotation.Pitch) .. ", Yaw=" .. self.round(rotation.Yaw) .. ", Roll=" .. self.round(rotation.Roll)
			msg = msg .. "\n" .. self:GetEntitySummary()
			if bPrint == "1" then
				Log.Debug(msg)
			end
			self.view.Text_Text:SetText(msg)
		end
	end, 1000, -1, nil, true)
end

function HUDGMPanel.round(num)
    if num >= 0 then
        return math.floor(num + 0.5)
    else
        return math.ceil(num - 0.5)
    end
end

return HUDGMPanel
