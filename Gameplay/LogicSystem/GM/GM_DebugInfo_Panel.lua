local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local NetworkManager = require "Framework.DoraSDK.NetworkManager"
local pakUpdateSubSystem = import("SubsystemBlueprintLibrary").GetEngineSubsystem(import("PakUpdateSubsystem"))
---@class GM_DebugInfo_Panel : UIPanel
---@field view GM_DebugInfo_PanelBlueprint
local GM_DebugInfo_Panel = DefineClass("GM_DebugInfo_Panel", UIPanel)

GM_DebugInfo_Panel.eventBindMap = {
    [_G.EEventTypes.DEBUG_INFO_UPDATE] = "OnUpdateInfo",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GM_DebugInfo_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GM_DebugInfo_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GM_DebugInfo_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GM_DebugInfo_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GM_DebugInfo_Panel:InitUIView()
end

---面板打开的时候触发
function GM_DebugInfo_Panel:OnRefresh(...)
    self.view.Text_PlainInfo:SetText(pakUpdateSubSystem:GetCurrentPakVersion())
end

function GM_DebugInfo_Panel:OnUpdateInfo()
    local UserID = tostring(Game.NetworkManager.RetLoginData.uid)
    local ClientVersion = pakUpdateSubSystem:GetCurrentPakVersion()
    local ServerVersion = Game.NetworkManager.RetLoginData.serverVersion
    local ProcessName = Game.NetworkManager.RetLoginData.processName
    local localSpace = NetworkManager.GetLocalSpace()
    local Info
    if localSpace == nil then
        Info = string.format("%s|%s|%s|%s", ClientVersion, ServerVersion, UserID, ProcessName)
    else
        Info = string.format("%s|%s|%s|%s|%s", ClientVersion, ServerVersion, UserID, ProcessName, localSpace.eid)
    end
    Log.DebugFormat("LoginRet %s, %s", ProcessName, Info)
    self.view.Text_PlainInfo:SetText(Info)
end

return GM_DebugInfo_Panel
