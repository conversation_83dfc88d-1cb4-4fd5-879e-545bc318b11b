local KGUECompositeOperateLibrary = import("KGUECompositeOperateLib")
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")
local AnimLibHelper = kg_require("GamePlay.3C.RoleComposite.AnimLibHelper")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")
function GMManager.printg(...)
    print("printg")
    local Count = 0 
    local ScanData = {}
    local forTable = _G.GProxy or _G
    for k, v in pairs(forTable) do
        local type = type(v)
        if type == "table" and v.__cname then
            type = "DefineClass"
        end
        local info = string.format("type:%s name:%s value:%s", type, k, tostring(v))
        ScanData[type] = ScanData[type] or {}

        table.insert(ScanData[type], info)
        Count = Count + 1
    end


    print("printg: Count is " .. Count)
    local subCount = 0
    for k, v in pairs(ScanData) do
        print("printg " .. k .." " .. #v .. "-------------------------------")

        for k2,v2 in pairs(v) do
            print("printg     " .. v2)
        end
    end
end

function GMManager.testnewdecal(flag)
	local EffectTaskFactory = kg_require("Gameplay.Combat.CombatEffect.EffectTaskFactory").EffectTaskFactory
	local TaskFx = kg_require("Gameplay.Combat.CombatEffect.TaskSpan.TaskFx")
	local sharedConst = kg_require("Shared.Const")
	local TASK_STATE_DATA_TYPE = sharedConst.TASK_STATE_DATA_TYPE
	Game.RefreshScript = true
	if flag == true then
		EffectTaskFactory.Register(Enum.EffectTaskType.PlayDecal, TaskFx.PlayDecalNew, TASK_STATE_DATA_TYPE.WITH_STATE)
	else
		EffectTaskFactory.Register(Enum.EffectTaskType.PlayDecal, TaskFx.PlayDecal, TASK_STATE_DATA_TYPE.WITH_STATE)
	end
	Game.RefreshScript = false
end

function GMManager.testnewhit()
	if not Game.me.USE_NEW_ATTACK then
		Game.me.USE_NEW_ATTACK = true
	else
		Game.me.USE_NEW_ATTACK = false
	end
	Game.GMManager.ExecuteCommandArgs("useNewHit", "2")
end

function GMManager.stoptarget()
	local TargetEntity = Game.EntityManager:getEntity(Game.AkAudioManager.lockTargetUID)
	if not TargetEntity then return end
	TargetEntity:StopAnimLibMontage(nil, 0.1)
end

function GMManager.testcycletime(...)
    local cycleTimes = 1 * 10000  --循环次数
    local time = import("LowLevelFunctions").GetUtcMillisecond()

    local forTable = _G.GProxy or _G  --全局变量存放的表
    for i = 1, cycleTimes do
        for k, v in pairs(forTable) do

        end
    end
    local timeSpan = import("LowLevelFunctions").GetUtcMillisecond() - time

    print(string.format("cycle %d times , cost time %d", cycleTimes, timeSpan))
end

function GMManager.testquerytimedirect(queryTimes)
    aa = 123

    local time = import("LowLevelFunctions").GetUtcMillisecond()

    local tempFindRet
    for i = 1, queryTimes do
        tempFindRet = aa
    end
    local timeSpan = import("LowLevelFunctions").GetUtcMillisecond() - time

    print(string.format("query %d times , cost time %d", queryTimes, timeSpan))
end

-- 执行lua mem snapshot
function GMManager.luamemdump(FileName)
    if not FileName then FileName = "LuaMemDump" end
    local mri = kg_require("Tools.LuaMemSnapshot")
    collectgarbage("collect")
    mri.m_cMethods.DumpMemorySnapshot("./", FileName, -1)
end

-- 求一个b快照相对于a快照新增的内容
function GMManager.luamemcmp(aName, bName, resultName)
    if not resultName then resultName = "ComparedResult" end
    local mri = kg_require("Tools.LuaMemSnapshot")
    local fileA = "LuaMemRefInfo-All-[" .. aName .. "]"
    local fileB = "LuaMemRefInfo-All-[" .. bName .. "]"
    mri.m_cMethods.DumpMemorySnapshotComparedFile("./", resultName, -1, fileA, fileB)
end

function GMManager.OpenRank()
    Game.RankManager:ShowRank()
end

function GMManager.printtelepoint()
    Game.AutoNavigationSystem:PrintDebugTelepoint()
end

--寻路到目标点，FrontDistance，玩家前方多少，RightDistance，玩家右方多少
function GMManager.navigateto(FrontDistance, RightDistance, ValidDistance)
    FrontDistance = tonumber(FrontDistance)
    RightDistance = tonumber(RightDistance)
    ValidDistance = tonumber(ValidDistance)

	local playerEntity = Game.me
	if not playerEntity then return end
	
	local rotation = playerEntity:GetRotation()
	local playerForward = import("KismetMathLibrary").GetForwardVector(rotation)
    local playerRight = import("KismetMathLibrary").GetRightVector(rotation)
    local playerLocation = playerEntity:GetPosition()

    local newLocation = playerLocation + playerForward*FrontDistance
    newLocation = newLocation + playerRight*(RightDistance or 0)
    
    local RealLocation = Game.AutoNavigationSystem:FindLocationZ(newLocation.X, newLocation.Y)
    if not RealLocation then
        RealLocation = newLocation 
    end

    Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.GM, LevelManager.GetCurrentLevelID(), RealLocation,ValidDistance)
end

--寻路到NPCSpawner
function GMManager.navigatetonpcspawner(npcSpanwnerID)
    Game.AutoNavigationSystem:RequestNavigateToNPC(npcSpanwnerID, nil)
end


--停止寻路
function GMManager.stopnavigation()
    Game.AutoNavigationSystem:StopNavigation()
end

--设置寻路drawDebug
function GMManager.navigationdrawdebug(drawdebug)
    Game.AutoNavigationSystem:SetDrawDebug(drawdebug == "1" and true or false)
end

--设置是否支持点击地图开启寻路
function GMManager.clickmapnavigation(value)
    Game.AutoNavigationSystem:SetEnableClickMapNavigation(value == "1" and true or false)
end

--Runtime对话状态下是否支持开启FreeCamera
function GMManager.dialoguefreecamera(value)
    Game.DialogueManager.FreeCamera = value == "1" and true or false
    if Game.DialogueManager.FreeCamera and Game.DialogueManager.P_Dialogue then
        Game.DialogueManager.P_Dialogue.View.WidgetRoot:SetVisibility(import("ESlateVisibility").Collapsed)
    else 
        Game.DialogueManager.P_Dialogue.View.WidgetRoot:SetVisibility(import("ESlateVisibility").SelfHitTestInvisible)
    end
end

--播放受击抖动
function GMManager.playhitperformance()
    Game.me:PlayNormalHitPerformance()
end

function GMManager.enablestateconflictlock(flag)
	Game.ENABLE_CONFLICT_LOCK = flag == true
end

function GMManager.playability(id)
    Game.me:StartAbilityRunner(tonumber(id))
end

function GMManager.testsplinelinkniagara()
    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    NiagaraEffectParam.TotalLifeMs = 5000
    NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_VFX_HatredLine
    NiagaraEffectParam.bNeedAttach = true
	
    NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetMainMesh()
	NiagaraEffectParam.AttachPointName = "pelvis"
    local CurTargetEntity = Game.me:GetLockTargetEntity(ETE.EBSTargetType.TT_Skill, true)
	NiagaraEffectParam.UserVals_Float = {}
    NiagaraEffectParam.UserVals_Float.Lengths = 1.0

    local Id = Game.me:PlayNiagaraEffect(NiagaraEffectParam)
	Game.EffectManager:UpdateNiagaraCameraArmLengthParam(Id, "CameraArmLength")
	Game.EffectManager:SetNiagaraSplineLinkParams(
			Id, "Spline", UIAssetPath.BP_HatredLineSplineActor,
			CurTargetEntity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent(), true, "ball_l")
end

function GMManager.testfloatcurveniagara()
    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Boss_Goulu_Skill06_04
    NiagaraEffectParam.bNeedAttach = true
    NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	NiagaraEffectParam.UserVals_FloatCurves = {}
	NiagaraEffectParam.UserVals_FloatCurves.Scale = UIAssetPath.CardMoveCurve
	NiagaraEffectParam.UserVals_FloatCurveRemapTime = {}
	NiagaraEffectParam.UserVals_FloatCurveRemapTime.Scale = 2.5

    NiagaraEffectParam.TotalLifeMs = 5000
    Game.me:PlayNiagaraEffect(NiagaraEffectParam)
end

function GMManager.testblendoutniagara()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Boss_Goulu_Skill06_04
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	NiagaraEffectParam.UserVals_FloatCurves = {}
	NiagaraEffectParam.UserVals_FloatCurves.Scale = UIAssetPath.CameraMoveCurve
	
	local EffectId = Game.me:PlayNiagaraEffect(NiagaraEffectParam)
	Game.TimerManager:CreateTimerAndStart(function()
		Game.EffectManager:BlendOutNiagaraWithCurve(EffectId, "Scale", UIAssetPath.CardMoveCurve)
	end, 2000, 1)
end

function GMManager.testblendoutniagaralinearsample()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Boss_Goulu_Skill06_04
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	NiagaraEffectParam.UserVals_FloatCurves = {}
	NiagaraEffectParam.UserVals_FloatCurves.Scale = UIAssetPath.CameraMoveCurve
	local EffectId = Game.me:PlayNiagaraEffect(NiagaraEffectParam)
	
	Game.TimerManager:CreateTimerAndStart(function()
		Game.EffectManager:BlendOutNiagaraLinearSample(EffectId, "Scale", 0.5, 0.0, 3.0)
	end, 2000, 1)
end

function GMManager.testfollowactorniagara()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_ChongGaoZhiLing
	NiagaraEffectParam.TotalLifeMs = 5000
	local CurPos = Game.me:GetPosition()
	local Trans = NiagaraEffectParam.SpawnTrans.Translation
	Trans.X = CurPos.X + 500
	Trans.Y = CurPos.Y
	Trans.Z = CurPos.Z
	local EffectId = Game.me:PlayNiagaraEffect(NiagaraEffectParam)
	Game.EffectManager:UpdateNiagaraFollowActor(EffectId, "PlayerPos", Game.me.CharacterID, true)
end

function GMManager.testplayniagara(effectPath, duration)
	local id = Game.me:PlayNiagaraEffectAttached(effectPath)

	Game.TimerManager:CreateTimerAndStart(function()
		Game.me:DeactivateNiagaraSystem(id)
	end, tonumber(duration), 1)
end

function GMManager.testspawndecalentity()
	local DecalParamTemplate = kg_require("GamePlay.BattleSystem.Effect.Template.DecalParamTemplate")
	local DecalParams = DecalParamTemplate.new()
	DecalParams.DecalMaterialAssetPath = UIAssetPath.MI_Decal_Yujing_SDF_Ring01
	DecalParams.SpawnerEntityUID = Game.me:uid()

	DecalParams.Translation = M3D.Vec3(100, 0, 0)

	DecalParams.bNeedAttach = true
	DecalParams.AttachSocketName = ""
	DecalParams.bAbsoluteRotation = true
	DecalParams.bAbsoluteScale = false

	DecalParams.DecalSizeX = 200
	DecalParams.DecalSizeY = 1000
	DecalParams.DecalSizeZ = 1000

	DecalParams.OriginSpecialName = "BSAttach.Bottom"

	DecalParams.bNeedCheckGround = true
	DecalParams.GroundOffset = M3D.Vec3(-300, 300, 0.2)
	DecalParams.GroundCheckObjectTypes = { 0 }

	DecalParams.LifeTimeMs = 3000

	DecalParams.FloatCurveParams = {
		Control = {
			AssetPath = UIAssetPath.CameraMoveCurve,
			bLoop = true,
			RemapTime = 0.3
		}
	}

	Game.me:SpawnDecalEntity(DecalParams)
end

function GMManager.updateenemyeffect(hiddenParam)
	local const = kg_require("Shared.Const")
	local bHidden = hiddenParam == "1"
	Game.EffectManager:UpdateNiagaraHiddenStateByEffectTag(const.NIAGARA_EFFECT_TAG.ENEMY, bHidden, const.NIAGARA_HIDDEN_REASON.COMBAT_SETTINGS)
end

function GMManager.updatebattleeffect(hiddenParam)
	local const = kg_require("Shared.Const")
	local bHidden = hiddenParam == "1"
	Game.EffectManager:UpdateNiagaraHiddenStateByEffectTag(const.NIAGARA_EFFECT_TAG.BATTLE, bHidden, const.NIAGARA_HIDDEN_REASON.CUTSCENE)
end

function GMManager.testniagarafacetolocation()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Boss_SasrielSkill05_Beam_Loop
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	M3D.FRotToQuat({ Yaw = -90 }, NiagaraEffectParam.SpawnTrans.Rotation)
	NiagaraEffectParam.bForceFaceToLocation = true
	local CurPos = Game.me:GetPosition()
	NiagaraEffectParam.FacingTargetLocation = M3D.Vec2(CurPos.X, CurPos.Y)
	NiagaraEffectParam.TotalLifeMs = 5000

	Game.me:PlayNiagaraEffect(NiagaraEffectParam)
end

function GMManager.testniagarafacetoactor()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Boss_SasrielSkill05_Beam_Loop
	NiagaraEffectParam.bForceFaceToTargetActor = true
	local TargetEntity = Game.me:GetLockTargetEntity(ETE.EBSTargetType.TT_Skill, true)
	NiagaraEffectParam.FacingTargetActorId = Game.me.CharacterID
	NiagaraEffectParam.TotalLifeMs = 5000
	M3D.FRotToQuat({ Yaw = -90 }, NiagaraEffectParam.SpawnTrans.Rotation)
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.AttachComponentId = TargetEntity.CppEntity:KAPI_Actor_GetRootComponent()

	TargetEntity:PlayNiagaraEffect(NiagaraEffectParam)
end



function GMManager.testlazerfollowactor()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Raybieber_Laser_Loop
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.bAbsoluteRotation = true
	NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	--M3D.FRotToQuat({ Yaw = -90 }, NiagaraEffectParam.SpawnTrans.Rotation)
	NiagaraEffectParam.TotalLifeMs = 5000

	local EffectId = Game.me:PlayNiagaraEffect(NiagaraEffectParam)
	local CurTargetEntity = Game.me:GetLockTargetEntity(ETE.EBSTargetType.TT_Skill, true)
	if CurTargetEntity then
		Game.EffectManager:UpdateNiagaraFollowActor(EffectId, "LaserEndPos", CurTargetEntity.CharacterID, false)
	end
end

function GMManager.testlazerscan()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Raybieber_Laser_Loop
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	NiagaraEffectParam.TotalLifeMs = 5000

	local EffectId = Game.me:PlayNiagaraEffect(NiagaraEffectParam)
	Game.EffectManager:UpdatePositionWithArcParams(EffectId, "LaserEndPos", 1000, -30, 540, 2)
end

function GMManager.testniagaraupdatetargetvalupdatetargetval(TargetVal, bUseNewDuration, NewDuration)
	if Game.me.TestNiagaraTargetValReqId == nil then
		local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
		NiagaraEffectParam.NiagaraEffectPath = "/Game/Arts/Effects/FX_Envrinment/12V12/12V12Zhandian/NS_Zhandian_Blu_Bianyuan.NS_Zhandian_Blu_Bianyuan"
		NiagaraEffectParam.bNeedAttach = true
		NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
		NiagaraEffectParam.UserVals_LinearSampleFloat = {["Progress"] = { StartVal=0, EndVal=1, Duration=2 }}

		Game.me.TestNiagaraTargetValReqId = Game.me:PlayNiagaraEffect(NiagaraEffectParam)
		Game.me:UpdateLinearSampleScalarNiagaraParamTargetVal(Game.me.TestNiagaraTargetValReqId, "Progress", 0.5, true, 0)
	else
		TargetVal = tonumber(TargetVal)
		NewDuration = tonumber(NewDuration)
		bUseNewDuration = bUseNewDuration == "1"
		Game.me:UpdateLinearSampleScalarNiagaraParamTargetVal(Game.me.TestNiagaraTargetValReqId, "Progress", TargetVal, bUseNewDuration, NewDuration)
	end
end

function GMManager.testfresneleffect(DurationStr)
	local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
	local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
	local ChangeMaterialRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialRequestTemplate
	local SEARCH_MESH_TYPE = MaterialEffectParamTemplate.SEARCH_MESH_TYPE
	
	local Duration = tonumber(DurationStr)
	local ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
	ChangeMaterialReq.MaterialPath = UIAssetPath.MI_2049Born_Body_Fresnel
	ChangeMaterialReq.SearchMeshType = SEARCH_MESH_TYPE.SearchAllMesh
	ChangeMaterialReq.AffectedAttachEntityTypes = { WorldViewConst.ATTACH_REASON.Weapon }
	
	local ReqId = Game.me:ChangeMaterial(ChangeMaterialReq)
	Game.TimerManager:CreateTimerAndStart(function()
		Game.me:RevertMaterial(ReqId)
	end, Duration, 1)
end

function GMManager.testedgeeffect(DurationStr)
	local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
	local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
	local ChangeMaterialParamRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialParamRequestTemplate
	local MATERIAL_EFFECT_TYPE = MaterialEffectParamTemplate.MATERIAL_EFFECT_TYPE
	local Duration = tonumber(DurationStr)
	
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.AffectedAttachEntityTypes = { WorldViewConst.ATTACH_REASON.Weapon }
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Edge
	ChangeMaterialParamReq.VectorLinearSampleParams = {}
	ChangeMaterialParamReq.VectorLinearSampleParams["Edge Color"] = {
		StartR = 0, StartG = 0, StartB = 0, StartA = 0,
		EndR = 1, EndG = 1, EndB = 1, EndA = 1,
		Duration = Duration / 1000
	}
	ChangeMaterialParamReq.ScalarLinearSampleParams = {}
	ChangeMaterialParamReq.ScalarLinearSampleParams["Edge Intensity"] = {
		StartVal = 0, EndVal = 20, Duration = Duration / 1000
	}
	ChangeMaterialParamReq.ScalarParams = {}
	ChangeMaterialParamReq.ScalarParams["Edge Pow"] = 15

	local ReqId = Game.me:ChangeMaterialParam(ChangeMaterialParamReq)
	Game.TimerManager:CreateTimerAndStart(function()
		Game.me:RevertMaterialParam(ReqId)
	end, Duration, 1)
end

function GMManager.testdissolveeffectupdatetargetval(TargetVal, bUseNewDuration, NewDuration)
	local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
	local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
	local ChangeMaterialParamRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialParamRequestTemplate
	local MATERIAL_EFFECT_TYPE = MaterialEffectParamTemplate.MATERIAL_EFFECT_TYPE

	if Game.me.TestEffectTargetValReqId == nil then
		local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
		ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Dissolve
		ChangeMaterialParamReq.bUseOLMDissolve = true
		ChangeMaterialParamReq.ScalarLinearSampleParams = {
			["_DissolveAlpha"] = {
				StartVal = 0, EndVal = 1, Duration = 2
			}
		}

		Game.me.TestEffectTargetValReqId = Game.me:ChangeMaterialParam(ChangeMaterialParamReq)
	else
		TargetVal = tonumber(TargetVal)
		NewDuration = tonumber(NewDuration)
		bUseNewDuration = bUseNewDuration == "1"
		Game.me:UpdateLinearSampleScalarMaterialParamTargetVal(Game.me.TestEffectTargetValReqId, "_DissolveAlpha", TargetVal, bUseNewDuration, NewDuration)
	end
end

function GMManager.testresetdissolveeffectupdatetargetval(TargetVal)
	local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
	local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
	local ChangeMaterialParamRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialParamRequestTemplate
	local MATERIAL_EFFECT_TYPE = MaterialEffectParamTemplate.MATERIAL_EFFECT_TYPE
	local SEARCH_MESH_TYPE = MaterialEffectParamTemplate.SEARCH_MESH_TYPE

	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.SearchMeshType = SEARCH_MESH_TYPE.SearchSelfMeshes
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Dissolve
	ChangeMaterialParamReq.bUseOLMDissolve = true
	ChangeMaterialParamReq.ScalarLinearSampleParams = {
		["_DissolveAlpha"] = {
			StartVal = 0, EndVal = 1, Duration = 2
		}
	}


	ChangeMaterialParamReq.FloatCurveParams = {
		["TestParam"] = {
			AssetPath = UIAssetPath.CameraMoveCurve,
			bEnableLoop = false,
			RemapTime = 1.0
		}
	}

	if Game.me.TestEffectTargetValReqId ~= nil then
		Game.me:RevertMaterialParam(Game.me.TestEffectTargetValReqId)
	end

	Game.me.TestEffectTargetValReqId = Game.me:ChangeMaterialParam(ChangeMaterialParamReq)
	Game.me:UpdateLinearSampleScalarMaterialParamTargetVal(Game.me.TestEffectTargetValReqId, "_DissolveAlpha", tonumber(TargetVal), false, 0)
end

function GMManager.testdissolveeffect(DurationStr, EnableLoopStr, RemapTimeStr)
	local Duration = tonumber(DurationStr)
	local bEnableLoop = EnableLoopStr == "1"
	local RemapTime = tonumber(RemapTimeStr)

	local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
	local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
	local ChangeMaterialRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialRequestTemplate
	local ChangeMaterialParamRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialParamRequestTemplate
	local SEARCH_MESH_TYPE = MaterialEffectParamTemplate.SEARCH_MESH_TYPE
	local MATERIAL_EFFECT_TYPE = MaterialEffectParamTemplate.MATERIAL_EFFECT_TYPE
	
	local ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
	ChangeMaterialReq.MaterialPath = UIAssetPath.MI_DeathDissolveFromBlack
	ChangeMaterialReq.SearchMeshType = SEARCH_MESH_TYPE.SearchSelfMeshes
	ChangeMaterialReq.AffectedAttachEntityTypes = { WorldViewConst.ATTACH_REASON.Weapon }

	local ChangeMaterialReqId = Game.me:ChangeMaterial(ChangeMaterialReq)
	Game.TimerManager:CreateTimerAndStart(function()
		Game.me:RevertMaterial(ChangeMaterialReqId)
	end, Duration, 1)

	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.SearchMeshType = SEARCH_MESH_TYPE.SearchSelfMeshes
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Dissolve
	local MaterialCurveParam = {}
	MaterialCurveParam.AssetPath = UIAssetPath.CameraMoveCurve
	MaterialCurveParam.bEnableLoop = bEnableLoop
	MaterialCurveParam.RemapTime = RemapTime
	ChangeMaterialParamReq.FloatCurveParams = {}
	ChangeMaterialParamReq.FloatCurveParams["_DissolveAlpha"] = MaterialCurveParam
	ChangeMaterialParamReq.AffectedAttachEntityTypes = { WorldViewConst.ATTACH_REASON.Weapon }

	local ChangeMaterialParamReqId = Game.me:ChangeMaterialParam(ChangeMaterialParamReq)
	Game.TimerManager:CreateTimerAndStart(function()
		Game.me:RevertMaterialParam(ChangeMaterialParamReqId)
	end, Duration, 1)
end

function GMManager.testfresneloverlayeffect(DurationStr)
	local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
	local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
	local ChangeMaterialRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialRequestTemplate
	local SEARCH_MATERIAL_TYPE = MaterialEffectParamTemplate.SEARCH_MATERIAL_TYPE

	local Duration = tonumber(DurationStr)
	local ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
	ChangeMaterialReq.MaterialPath = UIAssetPath.MI_2049Born_Body_Fresnel
	ChangeMaterialReq.AffectedAttachEntityTypes = { WorldViewConst.ATTACH_REASON.Weapon }
	ChangeMaterialReq.SearchMaterialType = SEARCH_MATERIAL_TYPE.SearchOverlayMaterial

	local ReqId = Game.me:ChangeMaterial(ChangeMaterialReq)
	Game.TimerManager:CreateTimerAndStart(function()
		Game.me:RevertMaterial(ReqId)
	end, Duration, 1)
end

function GMManager.testsurfaceeffect(DurationStr)
	local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
	local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
	local ChangeMaterialRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialRequestTemplate
	local SEARCH_MATERIAL_TYPE = MaterialEffectParamTemplate.SEARCH_MATERIAL_TYPE
	
	local Duration = tonumber(DurationStr)
	local ChangeMaterialReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialRequestTemplate)
	ChangeMaterialReq.MaterialPath = UIAssetPath.MI_BossCotard_Skill09_BodyOverlay
	ChangeMaterialReq.AffectedAttachEntityTypes = { WorldViewConst.ATTACH_REASON.Weapon }
	ChangeMaterialReq.SearchMaterialType = SEARCH_MATERIAL_TYPE.SearchOverlayMaterial

	local ReqId = Game.me:ChangeMaterial(ChangeMaterialReq)
	Game.TimerManager:CreateTimerAndStart(function()
		Game.me:RevertMaterial(ReqId)
	end, Duration, 1)
end

function GMManager.removedefaultoverlaymaterial()
	local MeshComp = Game.me.CppEntity:KAPI_Actor_GetMainMesh()
	Game.MaterialManager.cppMgr:RemoveDefaultMaterialInstance(MeshComp, 0, true, false)
end

function GMManager.playwhiteflash()
	Game.me:PlayWhiteFlash()
end

function GMManager.updatelinebonename(boneName)
	local const = kg_require("Shared.Const")
	const.LINE_BONE_NAME = boneName
end

local TestLoader = {}
function TestLoader:OnAssetLoadCb(LoadId, LoadObjIDs)
	local AssetNum = LoadObjIDs:Num()
	local Index = 0
	while Index < AssetNum do
		local LoadAssetID = LoadObjIDs:Get(Index)
		Index = Index + 1
		Log.Debug("Load", LoadAssetID)
	end
end

function GMManager.testassetload()
	local AssetsToLoad = {
		UIAssetPath.MI_DeathDissolveFromBlack,
		"xx",
		UIAssetPath.MI_DeathDissolveFromBlack,
		UIAssetPath.CameraMoveCurve,
		UIAssetPath.MI_2049Born_Body_Fresnel,
		UIAssetPath.MI_2049Born_Body_FresnelA,
		UIAssetPath.MI_DeathDissolveFromBlack,
		"xx"
	}
	Game.AssetManager:AsyncLoadAssetListKeepReferenceID(AssetsToLoad, TestLoader, "OnAssetLoadCb")
end

function GMManager.switchcamerapovsort(bOpen)
	Game.me.bCameraPOVSort = bOpen
	kg_require("Shared.Const").CAMERA_POV_SORT_SWITCH = bOpen
end

function GMManager.updatecamerapovrule(bOnlyAngle)
	local const = kg_require("Shared.Const")
	const.ONLY_ANGLE = bOnlyAngle
end

function GMManager.enablesortdebugdraw(EnableDebug)
	local const = kg_require("Shared.Const")
	const.SORT_DEBUG_SWITCH = EnableDebug
end

function GMManager.enablelocklimit(Enable)
	local const = kg_require("Shared.Const")
	const.IS_LOCK_LIMIT_SWITCH = Enable
end

function GMManager.enablelockselecteffect(Enable)
	local const = kg_require("Shared.Const")
	if Enable == "true" then
		const.LOCK_SELECT_EFFECT_SWITCH = true
	elseif Enable == "false" then
		const.LOCK_SELECT_EFFECT_SWITCH = false
	end
end

function GMManager.enableselecteffectrefresh(Enable)
	local const = kg_require("Shared.Const")
	if Enable == "true" then
		const.SELECT_EFFECT_REFRESH_SWITCH = true
	elseif Enable == "false" then
		const.SELECT_EFFECT_REFRESH_SWITCH = false
	end
end

function GMManager.testfall()
	local CachedVecPhysicsFall = FVector()
	local StartPos = Game.me.CppEntity:KAPI_GetLocation()
	StartPos.Z = StartPos.Z + 500
	CachedVecPhysicsFall.X = StartPos.X + 1000
	CachedVecPhysicsFall.Y = StartPos.Y + 1000
	CachedVecPhysicsFall.Z = StartPos.Z
	Game.me.CppEntity:KAPI_SetLocation(StartPos)
	Game.me.CppEntity:KAPI_Movement_AddMoveParabola(StartPos.X, StartPos.Y, StartPos.Z, StartPos.X + 1000, StartPos.Y + 1000, StartPos.Z, 1.0, 800)
end

function GMManager.testfall2()
	local CachedVecPhysicsFall = FVector()
	local StartPos = Game.me.CppEntity:KAPI_GetLocation()
	StartPos.Z = StartPos.Z + 500
	CachedVecPhysicsFall.X = StartPos.X + 1000
	CachedVecPhysicsFall.Y = StartPos.Y + 1000
	CachedVecPhysicsFall.Z = StartPos.Z + 500
	Game.me.CppEntity:KAPI_SetLocation(StartPos)
	Game.me.CppEntity:KAPI_Movement_AddMoveParabola(StartPos.X, StartPos.Y, StartPos.Z, StartPos.X + 1000, StartPos.Y + 1000, StartPos.Z + 500, 0.7, 800)
	
	Game.TimerManager:CreateTimerAndStart(function()
		local temp = Game.me.CppEntity:KAPI_GetLocation()
		Game.me.CppEntity:KAPI_Movement_AddMoveParabola(temp.X, temp.Y, temp.Z, temp.X, temp.Y, temp.Z, 0.3, 800)
	end, 0.7 * 1000, 1)

end

function GMManager.testcurvetable()
	local CurveTable = Game.AssetManager:SyncLoadAsset(UIAssetPath.CT_Hand)
	local UKGCurveUtil = import("KGCurveUtil")
	local EPropertyClass = import("EPropertyClass")
	local CurveValues = slua.Map(EPropertyClass.Name, EPropertyClass.Float)
	local bResult, CurveValues = UKGCurveUtil.GetRichCurveTableValues(CurveTable, 0.5, CurveValues)
	for k, v in pairs(CurveValues) do
		Log.Debug("CurveTable, ", k, v)
	end
end

function GMManager.showcharactertypeforviewbudget(bOpen)
	if Game.me.ShowCharacterTypeTimer ~= nil then
		Game.TimerManager:StopTimerAndKill(Game.me.ShowCharacterTypeTimer)
		Game.me.ShowCharacterTypeTimer = nil
	end

	if bOpen ~= "1" then
		return
	end

	local UKismetSystemLibrary = import("KismetSystemLibrary")
	local EmptyLocation = FVector()
	local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
	local VIEW_CHARACTER_TYPE_TO_TYPE_NAME = WorldViewBudgetConst.VIEW_CHARACTER_TYPE_TO_TYPE_NAME

	Game.me.ShowCharacterTypeTimer = Game.TimerManager:CreateTimerAndStart(function()
		local CharacterIdToDebugMsgs = {}
		for i, EntityId in ipairs(Game.me.RecentAttackerEntityIds) do
			local Entity = Game.EntityManager:getEntity(EntityId)
			if Entity then
				local DebugMsg = string.format("[%d]%s", i, VIEW_CHARACTER_TYPE_TO_TYPE_NAME[Entity:GetCharacterTypeForViewBudget()])
				CharacterIdToDebugMsgs[Entity] = DebugMsg
			end
		end

		local AvatarActors = Game.EntityManager:getEntitiesByType(EEntityType.AvatarActor)
		if AvatarActors then
			for _, Entity in pairs(AvatarActors) do
				local DebugMsg = string.format("%s", VIEW_CHARACTER_TYPE_TO_TYPE_NAME[Entity:GetCharacterTypeForViewBudget()])
				if CharacterIdToDebugMsgs[Entity] ~= nil then
					DebugMsg = string.format("%s\n%s", CharacterIdToDebugMsgs[Entity], DebugMsg)
				end
				CharacterIdToDebugMsgs[Entity] = DebugMsg
			end
		end

		local NpcActors = Game.EntityManager:getEntitiesByType(EEntityType.NpcActor)
		if NpcActors then
			for _, Entity in pairs(NpcActors) do
				local DebugMsg = string.format("%s", VIEW_CHARACTER_TYPE_TO_TYPE_NAME[Entity:GetCharacterTypeForViewBudget()])
				if CharacterIdToDebugMsgs[Entity] ~= nil then
					DebugMsg = string.format("%s\n%s", CharacterIdToDebugMsgs[Entity], DebugMsg)
				end
				CharacterIdToDebugMsgs[Entity] = DebugMsg
			end
		end

		for Entity, DebugMsg in pairs(CharacterIdToDebugMsgs) do
			if Entity.bInWorld then
				LuaScriptAPI.KismetSystem_DrawDebugString(
					_G.GetContextObjectID(),
					EmptyLocation,
					DebugMsg,
					Entity.CharacterID
				)
			end
		end
	end, 1, -1)
end

function GMManager.testniagaraprioritycullingbudgettimeout()
	Game.EffectManager:TryObtainNiagaraBudget(
		Game.me:GetCharacterTypeForViewBudget(), NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL, Game.me.CharacterID)
end

function GMManager.enablepreselect(Enable)
	local const = kg_require("Shared.Const")
	const.PRE_SELECT_SWITCH = Enable
end

function GMManager.enabledefaultselectsort(Enable)
	local const = kg_require("Shared.Const")
	const.DEFAULT_SELECT_SORT_SWITCH = Enable
end

function GMManager.testeffectpp(Id)
	Game.PostProcessManager:PlayPostProcessByID(Enum.EPostProcessLayers.World, 0, tonumber(Id), 1, 1, 5)
end

function GMManager.testppclip()
    Game.PostProcessManager:EnableClip(Enum.EPostProcessLayers.World, 1, 1, -1, 0, {R = 0, G = 1, B = 0, A = 0})
    Game.PostProcessManager:SetClipColor(true, {R = 0, G = 0, B = 1, A = 1}, nil)
    Game.PostProcessManager:SetClipCustomDepth(true, Game.me:uid(), nil, 1)

    Game.TimerManager:CreateTimerAndStart(function()
        Game.PostProcessManager:DisableClip()
    end, 5000, 1)
end

function GMManager.testpp()
    local PP1 = Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.Camera, nil, 4000020, 1, 1, -1)
    Game.PostProcessManager:EnableClip(Enum.EPostProcessLayers.World, 1, 1, -1, 0)
    Game.PostProcessManager:SetClipCustomDepth(true, Game.me:uid(), nil, 1)
	--Game.PostProcessManager:SetClipColor(true, {R = 0, G = 0, B = 1, A = 1}, nil)

    Game.TimerManager:CreateTimerAndStart(function()
        Game.PostProcessManager:StopPostProcessPreset(PP1)
        --Game.PostProcessManager:DisableClip()
		
        --Game.PostProcessManager:EnableClip(Enum.EPostProcessLayers.World, 1, 1, -1, 0)
        --Game.PostProcessManager:SetClipCustomDepth(true, Game.me:uid(), nil, 1)
		--Game.PostProcessManager:SetClipColor(true, {R = 1, G = 0, B = 0, A = 1}, nil)
        local PP2 = Game.PostProcessManager:PlayPostProcessPreset(Enum.EPostProcessLayers.Camera, nil, 4000019, 1, 1, -1)
		
        Game.TimerManager:CreateTimerAndStart(function()
            Game.PostProcessManager:StopPostProcessPreset(PP2)
            Game.PostProcessManager:DisableClip()
        end, 5000, 1)

    end, 5000, 1)

end

function GMManager.testlinkniagara()
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_VFX_Link
	NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	NiagaraEffectParam.SpawnerId = Game.me:uid()
	NiagaraEffectParam.TotalLifeMs = 5000

	NiagaraEffectParam.UserVals_MeshCompIds = {}
	NiagaraEffectParam.UserVals_SkeletalMeshCompFilterBones = {}
	
	local selfCompID = Game.me.CppEntity:KAPI_Actor_GetMainMesh()
	NiagaraEffectParam.UserVals_MeshCompIds["mesh"] = selfCompID
	NiagaraEffectParam.UserVals_SkeletalMeshCompFilterBones["mesh"] = { "pelvis" }
	
	local CurTargetEntity = Game.me:GetLockTargetEntity(ETE.EBSTargetType.TT_Skill, true)
	local targetCompID = CurTargetEntity.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
	NiagaraEffectParam.UserVals_MeshCompIds["mesh2"] = targetCompID
	NiagaraEffectParam.UserVals_SkeletalMeshCompFilterBones["mesh2"] = { "pelvis" }
	
	Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

function GMManager.testoverlap(radius)
    local OwnerActorLocation = 	Game.me.CppEntity:KAPI_GetLocation()

    local CollisionConst = kg_require("Shared.Const.CollisionConst")

    local bRes
    local comps = {}
    bRes, comps = import("KismetSystemLibrary").SphereOverlapComponents(
        _G.GetContextObject(),
        OwnerActorLocation,
        tonumber(radius),
        { import("C7FunctionLibrary").ConvertToObjectType(CollisionConst.COLLISION_OBJECT_TYPE_BY_NAME.SceneIndoorField) },
        nil,
        {},
        comps
    )

    Log.Debug("overlap comp", bRes, comps:Num())
    for _, comp in pairs(comps:ToTable()) do
        Log.Debug("overlap comp", comp:GetName(), comp:GetOwner() and comp:GetOwner():GetName() or "nil")
    end

    local outHits = slua.Array(import("EPropertyClass").Struct, import("HitResult"))
    bRes, outHits = import("KismetSystemLibrary").SphereTraceMultiForObjects(
        _G.GetContextObject(),
        OwnerActorLocation,
        OwnerActorLocation,
        tonumber(radius),
        { import("C7FunctionLibrary").ConvertToObjectType(CollisionConst.COLLISION_OBJECT_TYPE_BY_NAME.SceneIndoorField) },
        false,
        {},
        2,
        outHits,
        false,
        FLinearColor(1, 0, 0, 0),
        FLinearColor(0, 1, 0, 0),
        5
    )

    Log.Debug("sweep comp", bRes, outHits:Num())
    for _, hitRes in pairs(outHits:ToTable()) do
        Log.Debug("sweep comp", hitRes.Component:GetName(), hitRes.Component:GetOwner() and hitRes.Component:GetOwner():GetName() or "nil")
    end
end

function GMManager.rotatetest(rotateAngle, rotateTime)
    rotateAngle = tonumber(rotateAngle)
    rotateTime = tonumber(rotateTime)

    Game.me:RotateActorLinearSpeed(rotateAngle, rotateTime)
end

function GMManager.testniagaraparticlecolorscale(duration)
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraEffectPath = UIAssetPath.NS_Boss_SasrielSkill05_Beam_Loop
	NiagaraEffectParam.bNeedAttach = true
	NiagaraEffectParam.AttachComponentId = Game.me.CppEntity:KAPI_Actor_GetRootComponent()
	M3D.FRotToQuat({ Yaw = -90 }, NiagaraEffectParam.SpawnTrans.Rotation)
	NiagaraEffectParam.TotalLifeMs = 5000
	NiagaraEffectParam.ParticleColorScaleUpdateCurve = UIAssetPath.CF_NewspaperMove
	NiagaraEffectParam.ParticleColorScaleCurveTime = tonumber(duration)

	Game.me:PlayNiagaraEffect(NiagaraEffectParam)
end

function GMManager.testmedia(MediaPlayerPath, Url, bLoop)
	Game.MediaManager:PlayBinkMedia(MediaPlayerPath, Url, bLoop=="1", true)
end

function GMManager.testmediareachend(MediaPlayerPath, StartUrl, LoopUrl)
	local testObj = {}
	testObj.onMediaReachEnd = function(playID)
		Game.MediaManager:OpenUrl(playID, LoopUrl)
	end
	Game.MediaManager:PlayBinkMedia(MediaPlayerPath, StartUrl, false, true, nil, true, testObj, "onMediaReachEnd")
end

function GMManager.testweapondissolve(ID)
	local AttachEntityIds = Game.me:GetAttachEntitiesByAttachReason(WorldViewConst.ATTACH_REASON.Weapon)
	if AttachEntityIds == nil then
		return
	end
	for _, AttachEntityId in ipairs(AttachEntityIds) do
		local AttachEntity = Game.EntityManager:getEntity(AttachEntityId)
		AttachEntity:SetVisibilityByDissolveDataEffect(tonumber(ID), Enum.EInVisibleReasons.WeaponHidden)
	end
end

function GMManager.testmountdissolve(ID)
	Game.me.MountEntity:SetVisibilityByDissolveDataEffect(tonumber(ID), Enum.EInVisibleReasons.AttachRelationSyncToChild)
end

-- function GMManager.PrintDebugPathFollowPoints()
-- 	local Num = Game.me.DebugAutoNavigatePath:Num()
-- 	Log.Debug("[szk]AutoNavigationSystem.pathPoints, Num:", Num)
-- 	for i = 0, Num-1, 1 do
-- 		local Pos = Game.me.DebugAutoNavigatePath:Get(i)
-- 		Log.Debug("[szk]AutoNavigationSystem.pathPoints, Point", i, ":", Pos.X, Pos.Y, Pos.Z)
-- 	end

-- 	if Game.me.TaskTrackLine and Game.me.TaskTrackLine.DebugTrackPoints then
-- 		local Num = Game.me.TaskTrackLine.DebugTrackPoints:Num()
-- 		Log.Debug("[szk]Traceline pathPoints, Num:", Num)
-- 		for i = 0, Num-1, 1 do
-- 			local Pos = Game.me.TaskTrackLine.DebugTrackPoints:Get(i)
-- 			Log.Debug("[szk]Traceline pathPoints, Point", i, ":", Pos.X, Pos.Y, Pos.Z)
-- 		end

-- 		Game.me.TaskTrackLine.CppEntity:KAPI_TrackSpline_PrintPointInfo()
-- 	end
-- end

function GMManager.TestCarDrift()
	Game.me.MountConfigID = 3
	Game.me:RequestStartRide()
	-- Game.me.TestCarDriftTimer = Game.TimerManager:CreateTimerAndStart(function()
	-- 	Game.me.MountEntity:UpdateMountAnimIsEnableMoveTurn(false)
	-- 	Game.TimerManager:StopTimerAndKill(Game.me.TestCarDriftTimer)
	-- 	Game.me.TestCarDriftTimer = nil
	-- end, 500, -1)
end

function GMManager.ReplaceMainPlayerAnimBluePrint(animPath)
	--if not animPath then return end

	animPath = animPath or ViewResourceConst.ABP.ABP_BU_Player_R4
	
	local AnimClass = slua.loadClass(animPath)
	local AnimID = Game.ObjectActorManager:GetIDByClass(AnimClass)
	local MainMeshID = Game.me.CppEntity:KAPI_Actor_GetMainMesh()
	Game.me.CppEntity:KAPI_SkeletalMeshID_SetAnimClass(MainMeshID, AnimID)

	local AnimAssetPathValues = {}
	local AnimAssetPathNames = {}
	local OutPaths = {}
	local PreLoadIDs, PreLoadAssets = AnimLibHelper.GetAnimPreLoadDataForLocomotionABP(Game.me.ConfigAnimAssetID)
	if PreLoadIDs and PreLoadAssets then
		table.move(PreLoadIDs, 1, #PreLoadIDs, #AnimAssetPathNames + 1, AnimAssetPathNames)
		table.move(PreLoadAssets, 1, #PreLoadAssets, #AnimAssetPathValues + 1, AnimAssetPathValues)
		table.move(PreLoadAssets, 1, #PreLoadAssets, #OutPaths + 1, OutPaths)
	end
	
	KGUECompositeOperateLibrary.SetAnimParams(Game.me:uid(), _G.GetContextObject(), Game.me.CharacterID, "BodyUpper", animPath, Game.me.ConfigAnimAssetID,
		ViewAnimConst.ANIM_LOCO_CONTAINER.CONTAINER_SIZE,
		ViewAnimConst.ANIM_LOCO_CONTAINER.BASIC_LOCO_PRIORITY, ViewAnimConst.ANIM_LOCO_CONTAINER.BASIC_LOCO_SEMANTIC,
		AnimAssetPathNames, AnimAssetPathValues)

	Game.me.bNotifyAnyStateChangedFromStateMachine = false
	Game.me:SetNeedLocoAnimStateChangedNotifyFromLocomotionSM(true)
end

function GMManager.SetAllowAirDodge(InEnable)
	Game.me.bAllowAirDodge = InEnable
end

function GMManager.SetBackUpLocoDebugMode(InEnable)
	if InEnable then
		ComplexLocomotionControlComponent.TARGET_DASH_LOCOSTATE = "BackUpDashLocoState"
		MultiJumpComponent.JUMPSTAGE_TO_JUMPLOCOSTATE = {
			[0] = "BackUpJumpLocoState",
			[1] = "BackUpJumpSecondLocoState",
			[2] = "BackUpJumpThirdLocoState"
		}
	else
		ComplexLocomotionControlComponent.TARGET_DASH_LOCOSTATE = "DashLocoState"
		MultiJumpComponent.JUMPSTAGE_TO_JUMPLOCOSTATE = {
			[0] = "JumpLocoState",
			[1] = "JumpSecondLocoState",
			[2] = "JumpThirdLocoState"
		}
	end
end

function GMManager.SetAllowClimb(InEnable)
	InEnable = InEnable and true or false
	Game.me.CppEntity:KAPI_Movement_SwitchClimbDetect(InEnable)
end