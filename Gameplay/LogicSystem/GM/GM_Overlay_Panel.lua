local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESlateVisibility = import("ESlateVisibility")
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")
local UIDrag = kg_require("Framework.KGFramework.KGUI.Component.CommonLogic.UIComDragWidget")

---@class GM_Overlay_Panel : UIPanel
---@field view GM_Overlay_PanelBlueprint
local GM_Overlay_Panel = DefineClass("GM_Overlay_Panel", UIPanel)

GM_Overlay_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GM_Overlay_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GM_Overlay_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function GM_Overlay_Panel:InitUIComponent()
    ---@type UIComDragWidget
    self.dragDebug = self:CreateComponent(self.view.GM.Btn_Debug, UIDrag)
    self.dragDebug:SetDragSource(self.userWidget, false)
	self.dragFeedback = self:CreateComponent(self.view.Feedback.Btn_Debug, UIDrag)
	self.dragFeedback:SetDragSource(self.userWidget, false)
end

---UI事件在这里注册，此处为自动生成
function GM_Overlay_Panel:InitUIEvent()
	self:AddUIEvent(self.dragDebug.onClickEvent, "onDebugClicked")
	self:AddUIEvent(self.dragFeedback.onClickEvent, "onFeedbackClicked")
	
    self:AddUIEvent(self.dragDebug.onDragDetectedEvent, "OnDragDetected")
    self:AddUIEvent(self.dragFeedback.onDragDetectedEvent, "OnDragDetected2")
	self:AddUIEvent(self.dragDebug.onDragCancelEvent, "onDragCancel")
	self:AddUIEvent(self.dragFeedback.onDragCancelEvent, "onDragCancel2")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GM_Overlay_Panel:InitUIView()
	self.view.GM.Text_Name:SetText("GM")
end

---面板打开的时候触发
function GM_Overlay_Panel:OnRefresh(...)
    self.clickCount = nil
    self.TimeDiff = nil
    self.DragStartPos = nil
    self.isShowGM = nil
    self.bInCapture = false
    self.clickCount = 0
    self.TimeDiff = 0

	self:StopTimer("TimeCountTimer")
	local CurTimeText = self.view.CurTime
	self:StartTimer("TimeCountTimer", function()
		local curTimeStr = Game.TimeUtils.FormatDateTimeString(_G._now())
		CurTimeText:SetText(curTimeStr)
	end, 1000, -1, false)
	
    self:SetDefaultPos()
end

function GM_Overlay_Panel:OnScreenShotCaptured(rt, size_x, size_y)
	local relativePath = import("BlueprintPathsLibrary").ProjectSavedDir()
	local fileName = "qafeedback.png"
	self.bInCapture = false
	local filePath  = Game.ScreenShotUtil:SaveTexture(rt,relativePath,fileName)
	UI.ShowUI("P_QAFeedback",filePath)
end

function GM_Overlay_Panel:ShowTimeCountTimer(show)
	local curTimeText = self.view.CurTime
	local FuncUtils = kg_require("Shared.Utils.FuncUtils")
	if show == FuncUtils:VisibleToBool(curTimeText:GetVisibility()) then
		return
	end
	self:StopTimer("TimeCountTimer")
	curTimeText:SetVisibility(FuncUtils:BoolToVisible(show, true))
	if show then
		self:StartTimer("TimeCountTimer", function()
			local curTimeStr = Game.TimeUtils.FormatDateTimeString(_G._now(), "sec")
			curTimeText:SetText(curTimeStr)
		end, 1000, -1, false)
	end
end

function GM_Overlay_Panel:OnDragDetected(MyGeometry,InMouseEvent)
    self.PrevPos =  self.view.SB_PanelRoot.Slot:GetPosition()
    self.startDragPos =  import("KismetInputLibrary").PointerEvent_GetScreenSpacePosition(InMouseEvent)
end

function GM_Overlay_Panel:OnDragDetected2(MyGeometry,InMouseEvent)
	self:OnDragDetected(MyGeometry,InMouseEvent)
end

function GM_Overlay_Panel:onDragCancel(inPointerEvent)
	local mousePos =  UE.KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
	local Delta = mousePos - self.startDragPos
	local ViewportScale = WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
	local TargetPos = self.PrevPos + Delta / ViewportScale
	local ViewportSize = WidgetLayoutLibrary.GetViewportSize(_G.GetContextObject())
	if TargetPos.X + 150 > ViewportSize.X / ViewportScale then
		TargetPos.X = ViewportSize.X / ViewportScale - 160.0
	elseif TargetPos.X < 0 then
		TargetPos.X = 0.0
	end
	if TargetPos.Y < 0 then
		TargetPos.Y = 0
	elseif TargetPos.Y + 150 > ViewportSize.Y / ViewportScale then
		TargetPos.Y = ViewportSize.Y / ViewportScale - 150
	end
	self.view.SB_PanelRoot.Slot:SetPosition(TargetPos)
end

function GM_Overlay_Panel:onDragCancel2(inPointerEvent)
	self:onDragCancel(inPointerEvent)
end

function GM_Overlay_Panel:SetDefaultPos()
    local ViewportSize = WidgetLayoutLibrary.GetViewportSize(_G.GetContextObject())
    local ViewportScale = WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
    self.view.SB_PanelRoot.Slot:SetPosition(FVector2D(0, ViewportSize.Y / 2 / ViewportScale))
end

function GM_Overlay_Panel:onDebugClicked()
	Game.GMManager.ShowGM()
end

function GM_Overlay_Panel:onFeedbackClicked()
	local relativePath = import("BlueprintPathsLibrary").ProjectSavedDir()
	local fileName = "qafeedback.png"
	os.remove(import("LuaFunctionLibrary").ConvertToAbsolutePathForExternalAppForRead(relativePath .. fileName))
	Game.ScreenShotUtil:TakeScreenShot(self,"OnScreenShotCaptured",true)	
end

return GM_Overlay_Panel
