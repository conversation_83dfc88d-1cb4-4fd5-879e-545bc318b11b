local ESlateVisibility = import("ESlateVisibility")
local SettingWidgetBase = kg_require("Gameplay.LogicSystem.Settings.SettingWidgetBase")
---@class SetBtnItem : SettingWidgetBase
---@field view SetBtnItemBlueprint
local SetBtnItem = DefineClass("SetBtnItem", SettingWidgetBase)

SetBtnItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SetBtnItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SetBtnItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function SetBtnItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SetBtnItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Tips.Big_Button_ClickArea.OnClicked, "on_TipsBig_Button_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SetBtnItem:InitUIView()
end

---面板打开的时候触发
function SetBtnItem:Refresh(data)
	self.view.Text:SetText(self.MetaData.SettingName)
	self.view.Text_Content:SetText(self.MetaData.VisualValue[1])

	if self.MetaData.SettingTipsID ~= 0 then
		self.view.Tips:SetVisibility(ESlateVisibility.Visible)
	else
		self.view.Tips:SetVisibility(ESlateVisibility.Hidden)
	end
end

--- 此处为自动生成
function SetBtnItem:on_Btn_ClickArea_Clicked()
	Game.SettingsManager:ExecuteFunc(self.MetaData.Const_1)
end

--- 此处为自动生成
function SetBtnItem:on_TipsBig_Button_ClickArea_Clicked()
	Game.TipsSystem:ShowTips(self.MetaData.SettingTipsID, self.view.Tips.Big_Button_ClickArea:GetCachedGeometry())
end

return SetBtnItem
