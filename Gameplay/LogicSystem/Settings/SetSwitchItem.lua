local UIComSwitchBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSwitchBtn")
local ESlateVisibility = import("ESlateVisibility")
local SettingWidgetBase = kg_require("Gameplay.LogicSystem.Settings.SettingWidgetBase")
---@class SetSwitchItem : SettingWidgetBase
---@field view SetSwitchItemBlueprint
local SetSwitchItem = DefineClass("SetSwitchItem", SettingWidgetBase)

SetSwitchItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SetSwitchItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SetSwitchItem:InitUIData()
	---@type number 开关状态
	self.State = 0
end

--- UI组件初始化，此处为自动生成
function SetSwitchItem:InitUIComponent()
    ---@type UIComSwitchBtn
    self.SwitcherCom = self:CreateComponent(self.view.Switcher, UIComSwitchBtn)
end

---UI事件在这里注册，此处为自动生成
function SetSwitchItem:InitUIEvent()
    self:AddUIEvent(self.view.Tips.Big_Button_ClickArea.OnClicked, "on_TipsBig_Button_ClickArea_Clicked")
    self:AddUIEvent(self.SwitcherCom.onClickEvent, "on_SwitcherCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SetSwitchItem:InitUIView()
end

---面板打开的时候触发
function SetSwitchItem:Refresh(data)
	self.view.Text:SetText(self.MetaData.SettingName)
	self.SwitcherCom:SetSwitch(self.State ~= 0, false)
	if self.MetaData.SettingTipsID ~= 0 then
		self.view.Tips:SetVisibility(ESlateVisibility.Visible)
	else
		self.view.Tips:SetVisibility(ESlateVisibility.Hidden)
	end
end

---@param bAnim boolean 是否播放动画
function SetSwitchItem:UpdateData(bAnim)
	local Val = Game.SettingsManager:ReadSettingByConfig(self.MetaData.Const_1)
	if not self.State or self.State ~= Val then
		self.State = Val
		self:SetSwitcher(bAnim)
	end
end

function SetSwitchItem:UpdateValue()
	if self.State ~= 0 then
		self:PlayAnimation(self.SwitcherCom.view.Ani_Switch_Open, nil, self.SwitcherCom.userWidget)
	else
		self:PlayAnimation(self.SwitcherCom.view.Ani_Switch_Close, nil, self.SwitcherCom.userWidget)
	end
end

function SetSwitchItem:SetDefaultValue()
	local DefaultVal = Game.SettingsManager:GetSettingDefaultValue(self.MetaData.Const_1)
	if DefaultVal then
		self.State = DefaultVal
		self:SetSwitcher(false)
	end
	Game.SettingsManager:SaveSettingByConfig(self.MetaData.Const_1, self.State, self.MetaData.SubType, true)
end

---@param bAnim boolean 是否播放动画
function SetSwitchItem:SetSwitcher(bAnim)
	if bAnim then
		if self.State ~= 0 then
			self:PlayAnimation(self.SwitcherCom.view.Ani_Switch_Open, nil, self.SwitcherCom.userWidget)
		else
			self:PlayAnimation(self.SwitcherCom.view.Ani_Switch_Close, nil, self.SwitcherCom.userWidget)
		end
	else
		self.SwitcherCom:SetSwitch(self.State ~= 0, false)
	end
end

--- 此处为自动生成
---@param isOn bool
function SetSwitchItem:on_SwitcherCom_ClickEvent(isOn)
	Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common_lvl2, true)
	self.State = isOn and 1 or 0
	self:UpdateValue()
	Game.SettingsManager:SaveSettingByConfig(self.MetaData.Const_1, self.State, self.MetaData.SubType)
end

--- 此处为自动生成
function SetSwitchItem:on_TipsBig_Button_ClickArea_Clicked()
	Game.TipsSystem:ShowTips(self.MetaData.SettingTipsID, self.view.Tips.Big_Button_ClickArea:GetCachedGeometry())
end

return SetSwitchItem
