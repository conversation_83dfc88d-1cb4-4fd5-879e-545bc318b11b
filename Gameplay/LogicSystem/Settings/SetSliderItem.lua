local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local ESlateVisibility = import("ESlateVisibility")
local SettingWidgetBase = kg_require("Gameplay.LogicSystem.Settings.SettingWidgetBase")
---@class SetSliderItem : SettingWidgetBase
---@field view SetSliderItemBlueprint
local SetSliderItem = DefineClass("SetSliderItem", SettingWidgetBase)
local floor = math.floor

SetSliderItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SetSliderItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SetSliderItem:InitUIData()
	---@type number 最小值
	self.MinValue = 0
	---@type number 最大值
	self.MaxValue = 1
	---@type number 步进
	self.StepValue = 1
	---@type boolean 是否为浮点
	self.bFloat = false
end

--- UI组件初始化，此处为自动生成
function SetSliderItem:InitUIComponent()
    ---@type UIComNumberSlider
    self.PBCom = self:CreateComponent(self.view.PB, UIComNumberSlider)
end

---UI事件在这里注册，此处为自动生成
function SetSliderItem:InitUIEvent()
    self:AddUIEvent(self.view.Tips.Big_Button_ClickArea.OnClicked, "on_TipsBig_Button_ClickArea_Clicked")
    self:AddUIEvent(self.PBCom.onValueChange, "on_PBCom_ValueChange")
    self:AddUIEvent(self.PBCom.onValueCommit, "on_PBCom_ValueCommit")
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SetSliderItem:InitUIView()
end

---映射值到真实值
---@param VisualVal number 映射值
function SetSliderItem:MapVisualValueToReal(VisualVal)
	local OutVal = ((VisualVal - self.MinValue) / (self.MaxValue - self.MinValue)) *
		(self.MetaData.Value[2] - self.MetaData.Value[1]) + self.MetaData.Value[1]
	return self.bFloat and OutVal or floor(OutVal+0.5)
end

---真实值到映射值
---@param RealVal number 显示值
function SetSliderItem:MapRealValToVisual(RealVal)
	local OutVal = ((RealVal - self.MetaData.Value[1]) / (self.MetaData.Value[2] -self.MetaData.Value[1])) *
		(self.MaxValue - self.MinValue) + self.MinValue
	return self.bFloat and OutVal or floor(OutVal+0.5)
end

function SetSliderItem:SetDefaultValue()
	local UnMappedDefaultVal = Game.SettingsManager:GetSettingDefaultValue(self.MetaData.Const_1)
	local DefaultVal = self:MapRealValToVisual(UnMappedDefaultVal)
	DefaultVal = self:RoundTo(DefaultVal, self.StepValue)
	self.view.CurNum:SetText(DefaultVal)
	self:SetSlider(DefaultVal)
	self:on_PBCom_ValueCommit(DefaultVal, true)
end

function SetSliderItem:SetSlider(value)
	if not self.bFloat then
		value = floor(value + 0.5)
	end
	self.PBCom:setValueInternal(value, false)
	self.PBCom:UpdateView()
end

function SetSliderItem:Refresh(data)
	if not self.MetaData or not next(self.MetaData) then
		return
	end
	self.userWidget:SetSoundType(false)
	self.view.Text:SetText(self.MetaData.SettingName)
	self.MinValue = self.MetaData.VisualValue[1]
	self.MaxValue = self.MetaData.VisualValue[2]
	self.StepValue = self.MetaData.VisualValue[3]
	
	self.PBCom:Refresh(self.MinValue, self.MinValue, self.MaxValue, self.StepValue, false)
	self.bFloat = (self.StepValue % 1 ~= 0) and true
	self.PBCom:SetNearest(not self.bFloat)
	
	if self.MetaData.SettingTipsID ~= 0 then
		self.view.Tips:SetVisibility(ESlateVisibility.Visible)
	else
		self.view.Tips:SetVisibility(ESlateVisibility.Hidden)
	end
end

---@param bAnim boolean 是否播放动画
function SetSliderItem:UpdateData(bAnim)
	local UnMappedVal = Game.SettingsManager:ReadSettingByConfig(self.MetaData.Const_1)
	local Val = self:MapRealValToVisual(UnMappedVal)
	Val = self:RoundTo(Val, self.StepValue)
	self.view.CurNum:SetText(Val)
	self:SetSlider(Val)
end

function SetSliderItem:RoundTo(inVal, snap)
	return floor((inVal / snap) + 0.5) * snap
end

--- 此处为自动生成
function SetSliderItem:on_TipsBig_Button_ClickArea_Clicked()
	Game.TipsSystem:ShowTips(self.MetaData.SettingTipsID, self.view.Tips.Big_Button_ClickArea:GetCachedGeometry())
end

--- 此处为自动生成
---@param value number
function SetSliderItem:on_PBCom_ValueChange(value)
	value = self:RoundTo(value, self.StepValue)
	self.view.CurNum:SetText(value)
end

--- 此处为自动生成
---@param value number
---@param bResetting boolean 是否为重置
function SetSliderItem:on_PBCom_ValueCommit(value, bResetting)
	value = self:RoundTo(value, self.StepValue)
	local RealVal = self:MapVisualValueToReal(value)
	Game.SettingsManager:SaveSettingByConfig(self.MetaData.Const_1, floor(RealVal + 0.5), self.MetaData.SubType, bResetting)
end

--- 此处为自动生成
function SetSliderItem:on_Btn_ClickArea_Clicked()
end

return SetSliderItem
