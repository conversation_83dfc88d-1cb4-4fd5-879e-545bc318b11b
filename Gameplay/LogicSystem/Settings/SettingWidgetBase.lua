local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class SettingWidgetBase : UIListItem
local SettingWidgetBase = DefineClass("SettingWidgetBase", UIListItem)

--设置选项基类
function SettingWidgetBase:OnCreate()
	---@type table
	self.MetaData = nil
end

function SettingWidgetBase:OnRefresh(data)
	local SettingID = data.OrderID
	self.MetaData = Game.SettingsManager:GetMetaData(SettingID)
	self:Refresh(data)
	self:UpdateData(false)
end

function SettingWidgetBase:Refresh(...)
end

--刷新数据，自动或手动触发
---@param bAnim boolean 是否播放动画
function SettingWidgetBase:UpdateData(bAnim)

end

--设置为默认值
function SettingWidgetBase:SetDefaultValue()

end

function SettingWidgetBase:OnIndirectSync(SubType, SettingConst)
	if not self.MetaData or not next(self.MetaData) then
		return
	end
	--自己触发的不算
	if self.MetaData.Const_1 == SettingConst or (self.MetaData.Const_2 and self.MetaData.Const_2 == SettingConst) then
		return
	end
	if self.MetaData.SubType and self.MetaData.SubType ~= 0 and self.MetaData.SubType == SubType then
		self:UpdateData(true)
	end
end

return SettingWidgetBase
