local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Set_SingleChoice_Btn : UIListItem
---@field view Set_SingleChoice_BtnBlueprint
local Set_SingleChoice_Btn = DefineClass("Set_SingleChoice_Btn", UIListItem)

Set_SingleChoice_Btn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Set_SingleChoice_Btn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Set_SingleChoice_Btn:InitUIData()
	---@type boolean 选项文本字数是否超出了限制
	self.bTextLengthExceed = false
	---@type boolean 是否为推荐
	self.bRec = false
end

--- UI组件初始化，此处为自动生成
function Set_SingleChoice_Btn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Set_SingleChoice_Btn:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Set_SingleChoice_Btn:InitUIView()
end

function Set_SingleChoice_Btn:OnRefresh(data)
	--画质设置设置对应档位为推荐
	self.bRec = data.bRec
	self.bTextLengthExceed = data.bTextLengthExceed
	self.userWidget:Event_UI_Style(false, self.bTextLengthExceed and 1 or 0, self.bRec)
	self.view.Text_Content:SetText(data.textContent or "")
end

function Set_SingleChoice_Btn:OnSelected(bSelected)
	self.userWidget:Event_UI_Style(bSelected, self.bTextLengthExceed and 1 or 0, self.bRec)
end

return Set_SingleChoice_Btn
