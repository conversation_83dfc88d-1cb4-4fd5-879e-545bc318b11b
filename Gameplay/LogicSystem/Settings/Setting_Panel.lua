local ESlateVisibility = import("ESlateVisibility")
local UIComFrame = kg_require("Framework.KGFramework.KGUI.Component.Panel.UIComFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComAccordionList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComAccordionList")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

local SetTItle = kg_require("Gameplay.LogicSystem.Settings.SetTItle")
local SetSwitchItem = kg_require("Gameplay.LogicSystem.Settings.SetSwitchItem")
local SetSliderItem = kg_require("Gameplay.LogicSystem.Settings.SetSliderItem")
local SetSC = kg_require("Gameplay.LogicSystem.Settings.SetSC")
local SetSelectItem = kg_require("Gameplay.LogicSystem.Settings.SetSelectItem")
local SetBtnItem = kg_require("Gameplay.LogicSystem.Settings.SetBtnItem")
local SetDoubleSwitchItem = kg_require("Gameplay.LogicSystem.Settings.SetDoubleSwitchItem")
local Set_SingleChoice_Item = kg_require("Gameplay.LogicSystem.Settings.Set_SingleChoice_Item")
local Set_SingleChoiceMode_Item = kg_require("Gameplay.LogicSystem.Settings.Set_SingleChoiceMode_Item")

local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class Setting_Panel : UIPanel
---@field view Setting_PanelBlueprint
local Setting_Panel = DefineClass("Setting_Panel", UIPanel)

Setting_Panel.eventBindMap = {
	---网络请求回包
	[EEventTypesV2.SETTING_ON_AVATARACTOR_UNSTUCK] = "RetAvatarActorUnstuck",
	[EEventTypesV2.SETTING_ON_TOGGLE] = "OnSettingsToggle",
	[EEventTypesV2.SETTING_ON_RESET] = "OnReset",
	[EEventTypesV2.SETTING_ON_SUBTYPE_SYNC] = "OnIndirectSync"
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Setting_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Setting_Panel:InitUIData()
	---@type number 脱卡CD
	self.CD = nil
	---@type UITreeViewData 页签数据
	self.NavListData = nil
	---@type UITreeViewData 承载设置项的TreeList数据
	self.OptionListData = nil
	---@type table
	self.OptionListWidgetMap = {
		[Enum.ESettingWidgetTypeData.TITLE] = SetTItle,
		[Enum.ESettingWidgetTypeData.SWITCH] = SetSwitchItem,
		[Enum.ESettingWidgetTypeData.SLIDER] = SetSliderItem,
		[Enum.ESettingWidgetTypeData.SWITCH_SLIDER] = SetSC,
		[Enum.ESettingWidgetTypeData.DROPDOWNLIST] = SetSelectItem,
		[Enum.ESettingWidgetTypeData.BUTTON] = SetBtnItem,
		[Enum.ESettingWidgetTypeData.SWITCH_DOUBLE] = SetDoubleSwitchItem,
		[Enum.ESettingWidgetTypeData.OPTION] = Set_SingleChoice_Item,
		[Enum.ESettingWidgetTypeData.IMAGE_OPTION] = Set_SingleChoiceMode_Item,
	}
end

--- UI组件初始化，此处为自动生成
function Setting_Panel:InitUIComponent()
	---@type UIComFrame
	self.WBP_ComFrameCom = self:CreateComponent(self.view.WBP_ComFrame, UIComFrame)
	---@type UIListView childScript: SetTItle
	self.OptionListCom = self:CreateComponent(self.view.OptionList, UIListView)
	---@type UIComButton
	self.ResetCom = self:CreateComponent(self.view.Reset, UIComButton)
	---@type UIComButton
	self.dropCom = self:CreateComponent(self.view.drop, UIComButton)
	---@type UIComButton
	self.exchangeCom = self:CreateComponent(self.view.exchange, UIComButton)
	---@type UIComButton
	self.LogoutCom = self:CreateComponent(self.view.Logout, UIComButton)
	---@type UIComButton
	self.AnnounceCom = self:CreateComponent(self.view.Announce, UIComButton)
	---@type UIComButton
	self.ServiceCom = self:CreateComponent(self.view.Service, UIComButton)
	---@type UIComButton
	self.QRCodeCom = self:CreateComponent(self.view.QRCode, UIComButton)
	---@type UIComButton
	self.UserCenterCom = self:CreateComponent(self.view.UserCenter, UIComButton)
	---@type UIComButton
	self.ReportCom = self:CreateComponent(self.view.Report, UIComButton)
	---@type UIComAccordionList
	self.TabTreeList = self:CreateComponent(self.view.TabList, UIComAccordionList)
end

---UI事件在这里注册，此处为自动生成
function Setting_Panel:InitUIEvent()
	self:AddUIEvent(self.AnnounceCom.onClickEvent, "on_AnnounceCom_ClickEvent")
	self:AddUIEvent(self.LogoutCom.onClickEvent, "on_LogoutCom_ClickEvent")
	self:AddUIEvent(self.QRCodeCom.onClickEvent, "on_QRCodeCom_ClickEvent")
	self:AddUIEvent(self.ReportCom.onClickEvent, "on_ReportCom_ClickEvent")
	self:AddUIEvent(self.ResetCom.onClickEvent, "on_ResetCom_ClickEvent")
	self:AddUIEvent(self.exchangeCom.onClickEvent, "on_ExchangeCom_ClickEvent")
	self:AddUIEvent(self.dropCom.onClickEvent, "on_DropCom_ClickEvent")
	self:AddUIEvent(self.UserCenterCom.onClickEvent, "on_UserCenterCom_ClickEvent")
	self:AddUIEvent(self.TabTreeList.onItemSelected, "on_TabListCom_ItemSelected")
	self:AddUIEvent(self.ServiceCom.onClickEvent, "on_ServiceCom_ClickEvent")
	self:AddUIEvent(self.OptionListCom.onGetEntryLuaClass, "on_OptionListCom_GetEntryLuaClass")
	self:AddUIEvent(self.OptionListCom.onGetEntryClassIndexForItem, "on_OptionListCom_GetEntryClassIndexForItem")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Setting_Panel:InitUIView()
	self.AnnounceCom:SetName(StringConst.Get("SETTING_BTN_ANNOUNCEMENT"))
	self.ServiceCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	self.QRCodeCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	self.UserCenterCom:SetName(StringConst.Get("SETTING_BTN_USERCENTER"))
	self.ReportCom:SetName(StringConst.Get("SETTING_BTN_REPORT"))
	self:InitTextData()
end

function Setting_Panel.GetTimeBySec(Time)
	local Min = Time // 60
	local Sec = Time % 60
	if Min < 10 then
		Min = "0" .. Min
	end
	if Sec < 10 then
		Sec = "0" .. Sec
	end
	return Min .. ":" .. Sec
end

function Setting_Panel:InitTextData()
	self.WBP_ComFrameCom:Refresh(StringConst.Get("SETTING_TITLE"), Enum.ETipsData.SETTING)
	self.exchangeCom:SetName(StringConst.Get("SETTING_FUNC_EXCHANGE"))
	self.LogoutCom:SetName(StringConst.Get("SETTING_FUNC_LOGOUT"))
	self.ResetCom:SetName(StringConst.Get("SETTING_FUNC_DEFAULT"))
end

function Setting_Panel:OnOpen()
	self:ConstructNavList()
end

function Setting_Panel:OnClose()
	Game.SettingsManager:ApplyCache()
end

function Setting_Panel:OnRefresh(tabIndex)
	self.TabTreeList:SetExclusive(true)
	tabIndex = tabIndex or 1
	self.TabTreeList:ExpansionMainTab(tabIndex)
	self:StartCountDownIfInCD()
end

---构建设置界面 - 页签列表
function Setting_Panel:ConstructNavList()
	self.NavListData = Game.SettingsManager:GetSettingsTab()
	self.TabTreeList:Refresh(self.NavListData)
end

---构建设置界面 - 单个子界面
---@param Tab string 页签类型
function Setting_Panel:ConstructSettingPanel(Tab)
	self.OptionListData = Game.SettingsManager:GetSettingsByType(Tab)
	self.OptionListCom:Refresh(self.OptionListData)
end

function Setting_Panel:StartCountDownIfInCD()
	local lastUnstuckDuration = GetMainPlayerPropertySafely("LastUnstuckDuration")
	local time = math.round(_G._now() / 1000)
	local seconds = lastUnstuckDuration - time
	self:StartTimer('cd_auto_time', function()
		if seconds < 0 then
			self.dropCom:SetName(StringConst.Get("SETTING_FUNC_UNSTUCK"))
			self.CD = false
			self:StopTimer("cd_auto_time")
			return
		else
			self.dropCom:SetName(StringConst.Get("SETTING_FUNC_UNSTUCK") .. Setting_Panel.GetTimeBySec(seconds))
			self.CD = true
			seconds = seconds - 1
		end
	end, 1000, -1, nil, true)
end

function Setting_Panel:RetAvatarActorUnstuck(Result)
	if Result.Code == Game.ErrorCodeConst.NO_ERR then
		self:StartCountDownIfInCD()
		Game.SettingsManager:HideSetting()
	end
end

---重置后刷新本页
function Setting_Panel:OnReset()
	self.OptionListCom:Refresh(self.OptionListData)
end

---折叠对应类型的设置
function Setting_Panel:OnSettingsToggle()
	local TabIndex = self.TabTreeList:GetSelectedItemIndex()
	self:ConstructSettingPanel(self.NavListData.children[TabIndex].tabData.Const)
end

--- 此处为自动生成
function Setting_Panel:on_AnnounceCom_ClickEvent()
	Game.AnnounceSystem:ShowAnnouncement()
end

--- 退出
function Setting_Panel:on_LogoutCom_ClickEvent()
	Game.SettingsManager:ApplyCache()
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.QUIT_GAME_CONFIRM, function()
		Game.GameLoopManagerV2:PlayerReqLogout()
	end)
end

--- 此处为自动生成
function Setting_Panel:on_QRCodeCom_ClickEvent()
end

--- 此处为自动生成
function Setting_Panel:on_ReportCom_ClickEvent()
	Game.ReportSystem:ShowReportUI(Enum.EReportType.Feedback)
end

--- 重置
function Setting_Panel:on_ResetCom_ClickEvent()
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SETTING_RESET_PAGE, function()
		Game.SettingsManager:ResetCurrentPageSettings()
	end)
end

--- 换角
function Setting_Panel:on_ExchangeCom_ClickEvent()
	Game.SettingsManager:ApplyCache()
	Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SWITCH_PLAYER_CONFIRM, function()
		Game.GameLoopManagerV2:PlayerReqBackToSelectRoleUI()
	end)
end

--- 脱离卡死
function Setting_Panel:on_DropCom_ClickEvent()
	Game.MenuSystem:BreakStuck()
end

--- 此处为自动生成
function Setting_Panel:on_UserCenterCom_ClickEvent()
	Game.AllInSdkManager:ShowUserCenter()
end

function Setting_Panel:on_ServiceCom_ClickEvent()
	Game.AllInSdkManager:DirectlyOpenWebviewUrl("https://lotm.tzxxx.com")
end

---@param index number
---@param data UITreeViewChildData
--- 此处为自动生成
function Setting_Panel:on_TabListCom_ItemSelected(index, data)
	if data then
		local tabData = data.tabData
		self.ResetCom.userWidget:SetVisibility(tabData.TabReset == true and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
		self:ConstructSettingPanel(tabData.Const)
	else
		self.ResetCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--- 此处为自动生成
---@param index number
---@return number
function Setting_Panel:on_OptionListCom_GetEntryClassIndexForItem(index)
	local child = self.OptionListData[index + 1]
	return child and child.Kind or 1
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function Setting_Panel:on_OptionListCom_GetEntryLuaClass(index)
	local kind = self.OptionListData[index].Kind
	return self.OptionListWidgetMap[kind]
end

function Setting_Panel:OnIndirectSync(SubType, SettingConst)
	-- todo: panel内部事件 @曲思成 
	for index = 1, #self.OptionListData do
		---@type SettingWidgetBase
		local item = self.OptionListCom:GetItemByIndex(index)
		if item then
			item:OnIndirectSync(SubType, SettingConst)
		end
	end
end

return Setting_Panel
