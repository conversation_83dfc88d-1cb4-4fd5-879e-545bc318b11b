-- local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

---@class RedPointSystem:SystemBase
---@field public model RedPointModel
local RedPointSystem = DefineClass("RedPointSystem",SystemBase)
local EKGRedPointState = import("EKGRedPointState")
local WidgetEmptyComponent = kg_require("Framework.UI.WidgetEmptyComponent")
local ComRedPoint = kg_require("Gameplay.LogicSystem.Common.Hint.ComRedPoint")
local ERedPointEnum = { -- luacheck: ignore
    None = 0,
    Node = 1,
    List = 2
}

RedPointSystem.eventBindMap = {
	[EEventTypesV2.MODULE_LOCK_CHANGE] = "OnModuleLock",
}
function RedPointSystem.ConvertToFullName(TypeName, ...)
    local Res = TypeName
    for key, value in pairs({...}) do
        Res = Res .. "_" .. value
    end
    return Res
end

---@private
function RedPointSystem:onCtor()
    self.model = nil
    self.sender = nil
    self.childPendings = {}
    self.pendings = {}
    self.temp = {}
    self.lastt = 0 --_G._now()
    self.useRedDot = true
end

---@private
function RedPointSystem:onInit()
    ---@type RedPointModel
    self.model = kg_require("Gameplay.LogicSystem.RedPoint.RedPointModel").new(true, true)
    ---@type RedPointSender
    self.sender = kg_require("Gameplay.LogicSystem.RedPoint.RedPointSender").new()
    self.redPointTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:OnTick()
    end, 33, -1, nil, "RedPointTimer")
end

---@private
function RedPointSystem:onUnInit()
    Game.TimerManager:StopTimerAndKill(self.redPointTimer)
end

function RedPointSystem:OnModuleLock()
    local next = next
    local childPendings = self.childPendings
    for FullName, _ in next, self.model.FullNameMap do
        if not childPendings[FullName] then
            childPendings[FullName] = true
        end
    end
end

--红点的增删查改

---私有：创建红点
---@private
---@param Name string
---@return string
function RedPointSystem:CreateNode(Name, ...)
    local Indexs = { ... }
    local model = self.model
    local Config = model.RedPointConfig[Name]
    if not Config then
        Log.DebugFormat("Cannot Find Node Config with Name:%s", Name)
        return
    end

    local FullName = RedPointSystem.ConvertToFullName(Name, ...)
    model.FullNameMap[FullName] = { Params = { ... }, TypeName = Config.Name }

    if Config.Name ~= "Root" then
        if Config.Type == ERedPointEnum.List then
            table.remove(Indexs, #Indexs)
        end

        local Parent = self:GetNode(Config.Parent, table.unpack(Indexs))
        model.ParentNodeMap[FullName] = Parent
        if not model.ChildNodeMap[Parent] then
            model.ChildNodeMap[Parent] = {}
        end

        if Config.bIsolate then
            model.bIsolatedMap[Config.Name] = true
        end

        table.insert(model.ChildNodeMap[Parent], FullName)
    end

    return FullName
end

--私有：查找红点，找不到，则创建一个新红点
---@private
---@param Name string
---@return string
function RedPointSystem:GetNode(Name, ...)
    local FullName = self:FindNode(Name, ...)
    if FullName then
        return FullName
    end

    FullName = self:CreateNode(Name, ...)
    return FullName
end

--私有：查找红点，找不到返回nil
---@private
---@param Name string
---@return string
function RedPointSystem:FindNode(Name, ...)
    local FullName = RedPointSystem.ConvertToFullName(Name, ...)
    if self.model.FullNameMap[FullName] then
        return FullName
    else
        return nil
    end
end

--公有：清除红点的状态，红点状态变为脏，并通知上层红点
---@public
---@param Name string
function RedPointSystem:ClearNode(Name, ...)
    if self.useRedDot then
        Game.RedDotSystem:UpdateRedDot(Name, ...)
        return
    end

    local FullName = RedPointSystem.ConvertToFullName(Name, ...)
    if not self.model.FullNameMap[FullName] then
        return
    end

    if not self.childPendings[FullName] then
        self.childPendings[FullName] = true
    end
end

---公有：激活红点，并注册到红点树上
---@public
---@param Name string
function RedPointSystem:InvokeNode(Name, ...)
    if self.useRedDot then
        Game.RedDotSystem:RegisterRedDot(Name, ...)
        return
    end

    self:GetNode(Name, ...)
end

--注册/解注册红点, 操作三张表
--ViewBindings: UIView -> FullName
--UIViewMap: FullName -> UIView
--UIControllerMap: FullName -> UIController

---@公有：解除指定UIView绑定的红点
---@public
---@param UIView UIView
function RedPointSystem:UnRegisterView(UIView)
    if self.useRedDot then
        Game.RedDotSystem:DetachWidget(UIView, false)
        return
    end

    if UIView.WidgetRoot then
        UIView = UIView.WidgetRoot
    end

    local model = self.model
    local viewBindings = model.ViewBindings
    if not viewBindings[UIView] then 
        return 
    end

    local FullName = viewBindings[UIView]
    self:UnRegister(FullName)
    viewBindings[UIView] = nil

    model.UIViewMap[FullName] = nil
    local key = model.UIControllerMap[FullName]
    if model.UIController2RedPointNamesMap[key] then
        model.UIController2RedPointNamesMap[key][FullName] = nil
    end

    self.model.UIControllerMap[FullName] = nil
end

---@私有：界面关闭时，删除对UObject的引用，防止出现无法GC的情况
---@param UIControllerName string
function RedPointSystem:OnUIClosed(UIControllerName, UIController)
    local model = self.model
    local redPointNames = model.UIController2RedPointNamesMap[UIControllerName]
    if redPointNames then
        for FullName, _ in pairs(redPointNames) do
            self:UnRegisterNode(FullName, UIController)
            model.CustomFunctionMap[FullName] = nil
        end
    end
end

---@公有：解除指定UIView绑定的红点
---@public
---@param UIView UIView
function RedPointSystem:UnRegisterView_WidgetRoot(UIView)
    local model = self.model
    local viewBindings = model.ViewBindings
    local fullName = viewBindings[UIView]
    if not fullName then
        return 
    end
    
    self:UnRegister(fullName)
    viewBindings[UIView] = nil
    model.UIViewMap[fullName] = nil

    local key = self.model.UIControllerMap[fullName]
    if model.UIController2RedPointNamesMap[key] then
        model.UIController2RedPointNamesMap[key][fullName] = nil
    end
    self.model.UIControllerMap[fullName] = nil
end

---私有：解除FullName所代表的红点身上的所有注册
---@private
---@param FullName string
---@param UIController UIController
function RedPointSystem:UnRegisterNode(FullName, UIController)
    local model = self.model
    local viewMap = model.UIViewMap
    local UIView = viewMap[FullName]
    if not UIView then
        return 
    end

    self:UnRegister(FullName, model.UIControllerMap[FullName])
    model.ViewBindings[UIView] = nil
    viewMap[FullName] = nil

    local key = self.model.UIControllerMap[FullName]
    local names = model.UIController2RedPointNamesMap[key]
    if names then
        names[FullName] = nil
    end

    model.UIControllerMap[FullName] = nil
end

---公有：将一个控件注册到指定红点上
---UIController, UIView分别是这个控件所在的Controller和View
---Name和不定长参数决定要绑定的红点
---@public
---@param UIController UIController
---@param UIView UIView
---@param Name string
function RedPointSystem:RegisterRedPoint(UIController, UIView, Name, ...)
    if self.useRedDot then
        Game.RedDotSystem:RegisterRedDot(Name, ...)
        Game.RedDotSystem:AttachWidget(UIController, UIView, Name, ...)
        return
    end

    local fullName = self:GetNode(Name, ...)
    self:UnRegisterView(UIView)

    if UIView.WidgetRoot then 
        UIView = UIView.WidgetRoot 
    end
    self:UnRegisterNode(fullName)

    local model = self.model
    if not model.FullNameMap[fullName] then
        return 
    end

    local name = UIController.__cname
    model.ViewBindings[UIView] = fullName
    model.UIViewMap[fullName] = UIView
    model.UIControllerMap[fullName] = name

    local names = model.UIController2RedPointNamesMap[name]
    if names then
        names[fullName] = true
    else
        model.UIController2RedPointNamesMap[name] = { [fullName] = true }
    end

    self:UpdateIfUIViewExist(fullName)
end

---对偶的两个方法，添加红点到View

---私有：添加红点到控件上
---UIController, UIView表示添加的Slot所在的Controller, View, Widget表示添加的控件, CanvasPanel或Overlay
---@private
---@param UIController UIController
---@param UIView UIView
---@param Widget PanelWidget
function RedPointSystem:AddRedPointToWidget(UIController, UIView, Widget)
    if not Widget then return end

    local name = "ComRedPoint"
    local classPath = UIView.RedPointClass:ToString()
    if classPath and not string.find(classPath, name, 1, true) then
        local idxDot = string.find(classPath, ".", 1, true)
        if idxDot then
            name = string.sub(classPath, idxDot + 1, -3)
        end
    end

    UIController:PushContainerComponent(Widget)
    if Widget:IsA(import("CanvasPanel")) then
        local RedPoint = UIController:FormComponent(name, Widget, UIController.bIsOldUI and WidgetEmptyComponent or ComRedPoint)
        ---@type UCanvasPanelSlot
        local widgetRoot
        if UIController.bIsOldUI then
            widgetRoot = RedPoint.View.WidgetRoot
        else
            widgetRoot = RedPoint.userWidget
        end
        widgetRoot.ComRedPoint_lua:SetUserSpecifiedScale(UIView.DisplayScale)
        local layout = UIView.RedPointCanvasLayout
        local Slot = widgetRoot.Slot
        Slot:SetAlignment(layout.Alignment)
        Slot:SetAnchors(layout.Anchors)
        Slot:SetOffsets(layout.Offset)
        Slot:SetAutoSize(layout.bAutoSize)
        --UIController:PlayAnimation(RedPoint.View.RedPointWidget, RedPoint.View.RedPointWidget.Ani_in, 0.0, 1,
        --    EUMGSequencePlayMode.Forward, 1, false)
    elseif Widget:IsA(import("Overlay")) then
        local RedPoint = UIController:FormComponent(name, Widget, UIController.bIsOldUI and WidgetEmptyComponent or ComRedPoint)
        local widgetRoot
        if UIController.bIsOldUI then
            widgetRoot = RedPoint.View.WidgetRoot
        else
            widgetRoot = RedPoint.userWidget
        end
        widgetRoot.ComRedPoint_lua:SetUserSpecifiedScale(UIView.DisplayScale)
        local layout = UIView.RedPointOverlayLayout
        ---@type UOverlaySlot
        local Slot = widgetRoot.Slot
        Slot:SetHorizontalAlignment(layout.HorizontalAlignment)
        Slot:SetVerticalAlignment(layout.VerticalAlignment)
        Slot:SetPadding(layout.Padding)
        --UIController:PlayAnimation(RedPoint.View.RedPointWidget, RedPoint.View.RedPointWidget.Ani_in, 0.0, 1,
        --    EUMGSequencePlayMode.Forward, 1, false)
    end
end

---私有，删除控件上的红点
---UIController表示Slot所在的Controller，Widget表示要删除的红点所在Slot，类型为CanvasPanel或Overlay
---@private
---@param UIController UIController
---@param Widget PanelWidget
function RedPointSystem:RemoveRedPointToWidget(UIController, Widget)
    if self.useRedDot then
        Game.RedDotSystem:DetachWidget(Widget, false)
        return
    end

    UIController:PushContainerComponent(Widget)
end

function RedPointSystem:MarkOnceRedPoint(Name, ...)
    if self.useRedDot then
        Game.RedDotSystem:MarkOnceRedDot(Name, ...)
        return
    end

    local Node = self:GetNode(Name, ...)
    if not Node then return end
    local EnumMap = Game.TableData.Get_RedPointConfigToEnumMap()
    local Indexs = table.pack(...)
    local RedPointTriggerType = Enum.ERedPointEnumData[EnumMap[Name]]
    if #Indexs ~= 0 then
        if Game.me then
            Game.me:UpdateRedPointReq({
                RedPointTriggerType = RedPointTriggerType,
                TypePatamID = Indexs[#Indexs],
                PointType = 1,
            }, true)
        end
    end
    local RedPointEnumData = Game.TableData.GetRedPointEnumDataTable()
    local data = RedPointEnumData[RedPointTriggerType]
    local FullName = RedPointSystem.ConvertToFullName(Name, ...)
    self.model.OnceRedPoints[FullName] = true
    self.model.OnceRedPointUnlocks[FullName] = data.Unlock

    self:ClearNode(Name, ...)
end

---公有：删除一次性红点
---@public
---@param Name string
function RedPointSystem:DeleteOnceRedPoint(Name, ...)
    if self.useRedDot then
        Game.RedDotSystem:RemoveOnceRedDot(Name, ...)
        return
    end

    local FullName = RedPointSystem.ConvertToFullName(Name, ...)
    local model = self.model
    if not model.FullNameMap[FullName] then return end
    local EnumMap = Game.TableData.Get_RedPointConfigToEnumMap()
    local Indexs = { ... }
    --local SplitName = #Indexs == 0 and Name or Name .. "_" .. Indexs[#Indexs]
    if #Indexs ~= 0 and model.OnceRedPoints[FullName] then
        self.sender:UpdateRedPointReq({
            RedPointTriggerType = Enum.ERedPointEnumData[EnumMap[Name]],
            TypePatamID = Indexs[#Indexs],
            PointType = 1,
        }, false)
    end
    model.OnceRedPoints[FullName] = nil
    model.OnceRedPointUnlocks[FullName] = nil
    self:ClearNode(Name, ...)
end

---私有：某个红点是否是一次性点亮的
---@private
---@param NodeFullName string
function RedPointSystem:IsOnceRedPointActive(NodeFullName)
    local SplitName = string.split(NodeFullName, "_")
    SplitName = #SplitName >= 2 and SplitName[1] .. "_" .. SplitName[#SplitName] or SplitName[1]
    if not Game.ModuleLockSystem:CheckModuleUnlockByEnum(self.model.OnceRedPointUnlocks[NodeFullName], false) then
        return false
    end
    return self.model.OnceRedPoints[NodeFullName] and true or false
end

---服务器回包的红点类型，TriggerType表示红点的枚举类型，PatamID表示红点的列表ID
---@class RedPointInfo
---@field public RedPointTriggerType integer
---@field public TypePatamID integer
---@field public PointType integer

---私有：登录全量同步来自服务器的红点
---@private
---@param RedPointInfo RedPointInfo[]
function RedPointSystem:SyncOnceRedPoints(RedPointInfo)
    -- if self.useRedDot then
    --     Game.RedDotSystem:SyncOnceRedPoints(RedPointInfo)
    --     return
    -- end

    local ERedPointEnumData = Enum.ERedPointEnumData
    local TableData = Game.TableData
    local model = self.model

    local redPointTriggerType
    local RedPointEnumData = TableData.GetRedPointEnumDataTable()
    for key, value in ksbcpairs(RedPointInfo) do
        redPointTriggerType = value.RedPointTriggerType
        if redPointTriggerType == ERedPointEnumData.REDPOINT_JOIN_COMBAT or
            redPointTriggerType == ERedPointEnumData.REDPOINT_ASSIST_COMBAT then
            goto continue
        elseif redPointTriggerType == ERedPointEnumData.REDPOINT_SKILL_LIST then
            local skillidmap = TableData.Get_skillId2UniqueIdMap()
            if not skillidmap[value.TypePatamID] then
                goto continue
            end
            local skilldata = TableData.GetRoleSkillUnlockDataRow(skillidmap[value.TypePatamID].ID)
            if not skilldata.IsDisplay then
                goto continue
            end
        elseif redPointTriggerType == ERedPointEnumData.REDPOINT_DUNGEON_UNLOCK then
            local dungeondata = TableData.GetDungeonDataRow(value.TypePatamID).Type - 1
            local data = RedPointEnumData[value.RedPointTriggerType]
            local FullName = self:GetNode(data.Name, dungeondata, value.TypePatamID)
            if FullName then
                model.OnceRedPoints[FullName] = true
                model.OnceRedPointUnlocks[FullName] = data.Unlock
            end
            goto continue
        elseif redPointTriggerType == ERedPointEnumData.REDPOINT_SOCIALACTION_ITEM then
            local socialActionInfo = TableData.GetSocialActionDataRow(value.TypePatamID)
            if socialActionInfo then
                local data = RedPointEnumData[value.RedPointTriggerType]
                local FullName = self:GetNode(data.Name, socialActionInfo.class, value.TypePatamID)
                if FullName then
                    self.model.OnceRedPoints[FullName] = true
                    self.model.OnceRedPointUnlocks[FullName] = data.Unlock
                end
            end
            goto continue
		elseif redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_FASHION_ITEM then
			--local firstTab, subTab = Game.AppearanceSystem:GetFirstAndSubTabByID(value.TypePatamID)
			--if firstTab and subTab then
			--	local FullName = self:GetNode("AppearanceItem", firstTab, subTab, value.TypePatamID)
			--	if FullName then
			--		self.model.OnceRedPoints[FullName] = true
			--		self.model.OnceRedPointUnlocks[FullName] = RedPointEnumData[value.RedPointTriggerType].Unlock
			--		self:NotifyChildCountChanged(FullName)
			--	end
			--end
			goto continue
        end
        local data = RedPointEnumData[value.RedPointTriggerType]
        local FullName = self:GetNode(data.Name, value.TypePatamID)
        if FullName then
            model.OnceRedPoints[FullName] = true
            model.OnceRedPointUnlocks[FullName] = data.Unlock
        end
        ::continue::
    end
end

--私有：游戏中增量下发红点
---@private
---@param RedPointInfo RedPointInfo
function RedPointSystem:SyncOnceRedPoint(RedPointInfo)
    -- if self.useRedDot then
    --     Game.RedDotSystem:SyncOnceRedDot(RedPointInfo)
    --     return
    -- end

    local redPointTriggerType = RedPointInfo.RedPointTriggerType
    local TableData = Game.TableData
	local RedPointEnumData = TableData.GetRedPointEnumDataTable()
    if redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_DUNGEON_UNLOCK then
        Log.Debug("Sync Red Point: Type: Dungeon")
    end
    if redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_JOIN_COMBAT or
        redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_ASSIST_COMBAT then
        return
    elseif redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_DUNGEON_UNLOCK then
        local dungeondata = TableData.GetDungeonDataRow(RedPointInfo.TypePatamID).Type - 1
        local data = RedPointEnumData[RedPointInfo.RedPointTriggerType]
        local FullName = self:GetNode(data.Name, dungeondata, RedPointInfo.TypePatamID)
        if FullName then
            self.model.OnceRedPoints[FullName] = true
            self.model.OnceRedPointUnlocks[FullName] = data.Unlock
            self:NotifyChildCountChanged(FullName)
        end
        return
	elseif redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_FASHION_ITEM then
		--local firstTab, subTab = Game.AppearanceSystem:GetFirstAndSubTabByID(RedPointInfo.TypePatamID)
		--if firstTab and subTab then
		--	local FullName = self:GetNode("AppearanceItem", firstTab, subTab, RedPointInfo.TypePatamID)
		--	if FullName then
		--		self.model.OnceRedPoints[FullName] = true
		--		self.model.OnceRedPointUnlocks[FullName] = RedPointEnumData[RedPointInfo.RedPointTriggerType].Unlock
		--		self:NotifyChildCountChanged(FullName)
		--	end
		--end
		return
    end
    local data = RedPointEnumData[RedPointInfo.RedPointTriggerType]
    if not data then
        Log.Error("RedPointSystem:SyncOnceRedPoint: RedPointEnumData not found,RedPointInfo=%s", table.tostring(RedPointInfo))
        return
    end
    
    local FullName = self:GetNode(data.Name, RedPointInfo.TypePatamID)
    if FullName then
        self.model.OnceRedPoints[FullName] = true
        self.model.OnceRedPointUnlocks[FullName] = data.Unlock
        self:NotifyChildCountChanged(FullName)
    end
end

---公有：为类型为ConfigName的红点添加统一监听变化的方法
---Fun为自定义的方法，接受一个boolean类型作为输入的状态
---@public 
---@param Class LuaClass
---@param Action fun(self:LuaClass, State:boolean):void
---@param Name string
function RedPointSystem:AddCustomViewMethodToRedPoint(Class, Action, Name, ...)
    local FullName = RedPointSystem.ConvertToFullName(Name, ...)
    --Log.DebugFormat("Add Custom View Method To RedPoint: %s %s", FullName, debug.traceback())
    if self.model.FullNameMap[FullName] then
        self.model.CustomFunctionMap[FullName] = function(IsActive)
            Action(Class, IsActive)
        end
        self:UpdateIfUIViewExist(FullName)
    end
end

---公有：为类型为Name的红点添加统一监听变化的方法
---Action的输入为红点的参数，例：红点SkillCustomizerBuildBtn_8001，则... == 8001
---@public
---@param Name string
---@param Class LuaClass
---@param Action fun():boolean
function RedPointSystem:AddListener(Name, Class, Action)
    if self.useRedDot then
        Game.RedDotSystem:AddListener(Name, Class, Action)
        return
    end

    self.model.ListenerFunctionMap[Name] = function(...)
        return Action(Class, ...)
    end
end

---私有：某个类型，某个ID的红点的监听函数，并返回结果
---@private
---@param ConfigName string
---@return boolean
function RedPointSystem:CallListenerFunction(ConfigName, ...)
    if self.useRedDot then
        Game.RedDotSystem:CallListener(ConfigName, ...)
        return
    end

    if self.model.ListenerFunctionMap[ConfigName] then
        return self.model.ListenerFunctionMap[ConfigName](...)
    end
    return false
end

---RedPoint Interface

---私有：模拟一个红点类的行为
---判断名为FullName的红点是否显示
---依次查询是否为一次性点亮的->是否有自己的监听逻辑->是否能够通过子红点点亮
---@private
---@param FullName string
function RedPointSystem:IsRedPointActive(FullName)
    if self.useRedDot then
        return Game.RedDotSystem:IsRedDotActive(FullName)
    end
    
    local model = self.model

	if not model.FullNameMap[FullName] then return false end

    local TypeName = model.FullNameMap[FullName].TypeName
    local Params = model.FullNameMap[FullName].Params
    if model.StateMap[FullName] ~= nil then return self.model.StateMap[FullName] end
    if self:IsOnceRedPointActive(FullName) then
        model.StateMap[FullName] = true
        return true
    end
    if model.ListenerFunctionMap[TypeName] then
        local r = self:CallListenerFunction(TypeName, table.unpack(Params))
        model.StateMap[FullName] = r
        return r
    end
    if model.ChildNodeMap[FullName] then
        for key, value in pairs(model.ChildNodeMap[FullName]) do
            local ChildTypeName = model.FullNameMap[value].TypeName
            if not model.bIsolatedMap[ChildTypeName] then
                if self:IsRedPointActive(value) then
                    -- Log.Warning("set true ChildTypeName", ChildTypeName, FullName)
                    model.StateMap[FullName] = true
                    return true
                end
            end
        end
    end
    model.StateMap[FullName] = false
    return false
end

---私有：模拟一个红点类的行为
---通知名为FullName的红点状态发生了改变
---跟据是否为孤立的红点，向父级传导，将他们的状态置为脏
---@private
---@param FullName string
function RedPointSystem:NotifyChildCountChanged(FullName)
    local model = self.model
    local map = model.FullNameMap[FullName]
    if not map then return end
    local TypeName = map.TypeName
    model.StateMap[FullName] = nil
    self:UpdateIfUIViewExist(FullName)
    local parent = model.ParentNodeMap[FullName]
    if not parent then
        return
    end
    if not model.bIsolatedMap[TypeName] then
        if not self.pendings[parent] then
            self.pendings[parent] = true
        end
    end
end

function RedPointSystem:OnTick()
    local now = _G._now()
    local t = now - self.lastt
    if t > 1000 then
        self.lastt = now
        
        for k, v in next, self.childPendings do
            if v then
                self.temp[k] = 1
            end
        end
        
        for k, _ in next, self.temp do
            self:NotifyChildCountChanged(k)
            self.childPendings[k] = nil
            self.temp[k] = nil
        end
        
        for k, v in next, self.pendings do
            if v then
                self.temp[k] = 1
            end
        end

        for k, _ in next, self.temp do
            self:NotifyChildCountChanged(k)
            self.pendings[k] = nil
            self.temp[k] = nil
        end
    end
end

---私有：模拟一个红点类的行为
---查找名为FullName的红点是否正在显示，如果正在显示则更新其状态
---@private
---@param FullName string
function RedPointSystem:UpdateIfUIViewExist(FullName)
    local model = self.model
    local UIView = model.UIViewMap[FullName]
    if not UIView then 
        Log.WarningFormat("[RedPointSystem]UpdateIfUIViewExist: UIView is nil, fullName:%s", FullName)

        local key = model.UIControllerMap[FullName]
        local names = model.UIController2RedPointNamesMap[key]
        if names then
            names[FullName] = nil
        end

        model.CustomFunctionMap[FullName] = nil
        model.UIControllerMap[FullName] = nil
        model.UIViewMap[FullName] = nil
        return
    end

    local UIController = model.UIControllerMap[FullName]
    if UIController then
        UIController = UI.GetUI(UIController)
    end
    if UIView and UIController and UIController:IsOpened() then
        if model.StateMap[FullName] == nil then
            self:IsRedPointActive(FullName)
        end
        if model.CustomFunctionMap[FullName] then
            model.CustomFunctionMap[FullName](self.model.StateMap[FullName])
        else
            if model.StateMap[FullName] then
                if UIView:IsA(import("Widget")) and not UIView:IsA(import("KGRedPointUserWidget")) then
                    -- local comRedDot = UIView:GetComponent(import("KGRedDotComponent"))
                    -- if comRedDot then
                    --     comRedDot:SetVisibility(true)
                    -- end
                    return
                end
                if UIView.RedPointState ~= EKGRedPointState.None and UIView.WidgetName then
                    local str = UIView.WidgetName
                    str = string.gsub(str, "_lua", "")
                    self:AddRedPointToWidget(UIController, UIView,
                        UIView[str] or
                        import("UIFunctionLibrary").GetWidgetFromName(UIView, UIView.WidgetName)
                    )
                end
            else
                if UIView:IsA(import("Widget")) and not UIView:IsA(import("KGRedPointUserWidget")) then
                    -- local comRedDot = UIView:GetComponent(import("KGRedDotComponent"))
                    -- if comRedDot then
                    --     comRedDot:SetVisibility(false)
                    -- end
                    return
                end

                if UIView.RedPointState ~= EKGRedPointState.None and UIView.WidgetName then
                    local str = UIView.WidgetName
                    str = string.gsub(str, "_lua", "")
                    self:RemoveRedPointToWidget(UIController, UIView, UIView[str] or
                        import("UIFunctionLibrary").GetWidgetFromName(UIView, UIView.WidgetName))
                end
            end
        end
    end
end

---私有：模拟一个红点类的行为
---查找名为FullName的红点是否绑定了显示的控件，解绑并把控件置回到有红点之前的状态
---@private
---@param FullName string
function RedPointSystem:UnRegister(FullName, UIController)
    local model = self.model
    if model.CustomFunctionMap[FullName] then
        model.CustomFunctionMap[FullName](false)
    end
    local UIView = model.UIViewMap[FullName]
    if (UIController == nil or type(UIController) == 'string') and model.UIControllerMap[FullName] then
        UIController = UI.GetUI(model.UIControllerMap[FullName])
    end
    if UIView and UIController and (IsValid_L(UIView)) then
        if UIView:IsA(import("Widget")) and not UIView:IsA(import("KGRedPointUserWidget")) then
            -- local comRedDot = UIView:GetComponent(import("KGRedDotComponent"))
            -- if comRedDot then
            --     comRedDot:SetVisibility(false)
            -- end
            model.UIViewMap[FullName] = nil
            model.UIControllerMap[FullName] = nil
            return
        end

        if UIView.RedPointState ~= EKGRedPointState.None and UIView.WidgetName then
            local str = UIView.WidgetName
            str = string.gsub(str, "_lua", "")
            self:RemoveRedPointToWidget(UIController, UIView, UIView[str] or
                import("UIFunctionLibrary").GetWidgetFromName(UIView, UIView.WidgetName))
        end
    end
    model.UIViewMap[FullName] = nil
    model.UIControllerMap[FullName] = nil
end

return RedPointSystem
