---@class RedDotModel:SystemModelBase
---@field private NodeMap table<string, RedDotNode>
---@field private NodeNameMap table<RedDotNode, string>
---@field private ListenerMap table<string, LuaDelegate>
---@field private OnceNodes table<string, RedDotOnceNode>
---@field private WidgetToNode table<UIView|UIComponent, RedDotNode>
---@field private UIScriptToNode table<UIComponent|UIBase, RedDotNode>
local RedDotModel = DefineClass("RedDotModel", SystemModelBase)

local RedDotNode = kg_require("Gameplay.LogicSystem.RedPoint.RedDot")
local RedDotOnceNode = kg_require("Gameplay.LogicSystem.RedPoint.RedDotOnce")
local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
function RedDotModel:init()
    self.RedDotConfig = kg_require("Gameplay.LogicSystem.RedPoint.RedPointConfig")
    self.NodeMap = {}
    self.NodeNameMap = {}
    self.WidgetToNode = setmetatable({}, {
        __mode = "kv"
    })
    self.UIScriptToNode = setmetatable({}, {
        __mode = "kv"
    })
    self.ListenerMap = {}
    self.OnceNodes = {}
    self.Root = self:CreateNode("Root", nil)
end

function RedDotModel:unInit()
    self:Clear()
    self.ListenerMap = {}
end

function RedDotModel:Clear()
    self.NodeMap = {}
    self.NodeNameMap = {}
    self.OnceNodes = {}
    self.Root = nil
    Log.DebugFormat("[RedDot]Clear all red dot nodes\n%s", debug.traceback())
end

--- 创建红点节点
---@public
---@param name string
---@param ... any
---@return RedDotNode
function RedDotModel:CreateNode(name, ...)
    return self:internalCreateNode(RedDotNode.new, name, ...)
end

--- 创建一次性红点节点
---@public
---@param name string
---@param ... any
---@return RedDotOnceNode
function RedDotModel:CreateOnceNode(name, ...)
    local onceNode = self:internalCreateNode(RedDotOnceNode.new, name, ...)
    if not onceNode then
        return
    end
    
    self.OnceNodes[onceNode.FullName] = onceNode
    return onceNode
end

--- 获取红点节点
---@public
---@param name string
---@param ... any
---@return RedDotNode
function RedDotModel:GetNode(name, ...)
    local fullName = self.ComposeFullName(name, ...)
    return self.NodeMap[fullName]
end

--- 获取或创建红点节点
---@public
---@param name string
---@param ... any
---@return RedDotNode
function RedDotModel:FindOrCreateNode(name, ...)
    local fullName = self.ComposeFullName(name, ...)
    local node = self.NodeMap[fullName]
    if not node then
        node = self:CreateNode(name, ...)
    end

    return node
end

--- 获取或创建一次性红点节点
---@public
---@param name string
---@param ... any
---@return RedDotOnceNode
function RedDotModel:FindOrCreateOnceNode(name, ...)
    local fullName = self.ComposeFullName(name, ...)
    local node = self.OnceNodes[fullName]
    if not node then
        node = self:CreateOnceNode(name, ...)
    end

    return node
end

--- 绑定一个红点到widget
---@public
---@param uiScript UIComponent
---@param widget UWidget
---@param name string
---@param ... any
function RedDotModel:AttachWidget(uiScript, widget, name, ...)
    local node = self:FindOrCreateNode(name, ...)
    if node then
        node:AttachWidget(uiScript, widget)
        self.WidgetToNode[widget] = node
        self.UIScriptToNode[uiScript] = node
    end
end

--- 解绑一个红点到widget
---@public
---@param widget UWidget
---@param hideOrDestroyRedDotUI boolean @ 隐藏/销毁红点UI，  nil|true: 隐藏widget，false: 销毁widget
function RedDotModel:DetachWidget(widget, hideOrDestroyRedDotUI)
    local node = self.WidgetToNode[widget]
    if node then
        local uiScript = node.UIAttached.UIScript
        node:DetachWidget(widget)
        self.WidgetToNode[widget] = nil
        self.UIScriptToNode[uiScript] = nil
    end
end

--- 通过UI控件获取红点节点
---@public
---@param widget UWidget
---@return RedDotNode|nil
function RedDotModel:GetNodeByWidget(widget)
    local node = self.WidgetToNode[widget]
    return node or nil
end

--- 通过UI脚本获取红点节点
---@public
---@param uiScript UIComponent
---@return RedDotNode|nil
function RedDotModel:GetNodeByUIScript(uiScript)
    local node = self.UIScriptToNode[uiScript]
    return node or nil
end

--- 移除红点节点
---@public
---@param name string
---@param ... any
---@return RedDotNode
function RedDotModel:RemoveNode(name, ...)
    local fullName = self.ComposeFullName(name, ...)
    local node = self.NodeMap[fullName]
    if node then
        self:Remove(node)
    end
end

--- 移除红点节点
---@public
---@param node RedDotNode
function RedDotModel:Remove(node)
    if node then
        local widget = node.UIAttached.Widget
        local uiScript = node.UIAttached.UIScript

        node:DetachWidget()
        node:DetachFromParent()

        self.NodeMap[node.FullName] = nil
        self.NodeNameMap[node] = nil
        self.WidgetToNode[widget] = nil
        self.UIScriptToNode[uiScript] = nil
    end
end

function RedDotModel:RemoveOnceNode(name, ...)
    local fullName = self.ComposeFullName(name, ...)
    local node = self.OnceNodes[fullName]
    if node then
        self:Remove(node)
        self.OnceNodes[fullName] = nil
    end
end

--- 生成节点全名
---@public
---@param name string
---@param ... any
---@return string
function RedDotModel.ComposeFullName(name, ...)
    local table = table
    local t = table.pack(...)
    table.insert(t, 1, name)
    return table.concat(t, "_")
end

--- 添加监听
---@public
---@param name string
---@param obj any
---@param func string|fun(name:string, ...):boolean
function RedDotModel:SetListener(name, obj, func)
    local listener = self.ListenerMap[name]
    if not listener then
        listener = LuaDelegate.new()
        listener:Bind(func, obj)
        self.ListenerMap[name] = listener
        Log.DebugFormat("[RedDot]SetListener: %s callback:%s obj:%s", name, tostring(func), tostring(obj))
    else
        listener:Bind(func, obj)
        Log.DebugFormat("[RedDot]UpdateListener: %s callback:%s obj:%s", name, tostring(func), tostring(obj))
    end

    return listener
end

function RedDotModel:CallListener(redDotCfgName, ...)
    ---@type LuaDelegate
    local listener = self.ListenerMap[redDotCfgName]
    if listener then
        listener:Execute(...)
    else
        Log.ErrorFormat("RedDotModel:SetListener:func is not function or string.Reddot config name")
    end
end

function RedDotModel:MarkAllNodeDirty()
    for fullName, node in pairs(self.NodeMap) do
        node:MarkDirty(true)
    end
end

--------------------------------------------------------------------------------------------
--- private
--------------------------------------------------------------------------------------------

---@private
---@param funcCreator fun(redDotCfg:RedDotConfig, name:string, ...: any):RedDotNode
---@param name string
---@param ... any
---@return  RedDotNode|RedDotOnceNode
function RedDotModel:internalCreateNode(funcCreator, name, ...)
    local config = self.RedDotConfig[name]
    if not config then
        Log.DebugFormat("CreateNode faield:has no config for %s", name)
        return nil
    end

    local fullName = self.ComposeFullName(name, ...)

    local listener = self.ListenerMap[name]
    if not listener then
        listener = self:SetListener(name, nil, function()
            Log.DebugFormat("[RedDot]default listener for %s", name)
            return false
        end)
    end
    assert(funcCreator, "funcCreator is nil")

    ---@type RedDotNode
    local node = funcCreator(config, fullName, ...)
    node:Init(listener, nil)

    if config.Parent then
        local parameters = { ... }
        if config.Type == 2 then
            table.remove(parameters, #parameters)
        end

        local parent = self:FindOrCreateNode(config.Parent, table.unpack(parameters))
        if not parent then
            parent = self.Root
        end

        node:AttachToParent(parent)
    end

    self.NodeMap[fullName] = node
    self.NodeNameMap[node] = name

    return node
end
return RedDotModel
