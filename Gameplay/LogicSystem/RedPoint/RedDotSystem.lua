---@class RedDotSystem : SystemBase
---@field private TypeListeners table
---@field private DirtyNodes RedDotModel[]
---@field private Model RedDotModel
---@field private Sender RedPointSener
local RedDotSystem = DefineClass("RedDotSystem", SystemBase)

RedDotSystem.eventBindMap = {
    [EEventTypesV2.MODULE_LOCK_CHANGE] = "OnModuleLock",
}

function RedDotSystem:onCtor()
    self.TypeListeners = {}
    self.DirtyNodes = {
        cur = {},
        last = {},
        n = 0,
        Swap = function(self)
            local temp = self.cur
            local count = self.n
            self.cur = self.last
            self.n = 0
            self.last = temp
            return temp, count
        end,

        Push = function(self, val)
            if val ~= nil then
                self.n = self.n + 1
                self.cur[self.n] = val
            end
        end,
    }
    self.Model = nil
    self.Sender = nil
end

function RedDotSystem:onInit()
    self.Model = kg_require("Gameplay.LogicSystem.RedPoint.RedDotModel").new(true, true)
    self.Model:init()

    self.Sender = kg_require("Gameplay.LogicSystem.RedPoint.RedPointSender").new()
    self.DirtyNodes:Swap()
    self.TimerRedDotTicker = Game.TimerManager:CreateTimerAndStart(function()
        self:OnTick()
    end, 33, -1, nil, "RedDotTicker")
end

function RedDotSystem:onUnInit()
    Game.TimerManager:StopTimerAndKill(self.TimerRedDotTicker)
    self.TimerRedDotTicker = nil
    self.DirtyNodes = nil
    self.Model:unInit()
end


function RedDotSystem:OnModuleLock()
    self.Model:MarkAllNodeDirty()
end

function RedDotSystem:OnTick()
    local nodesNeedUpdate, count = self.DirtyNodes:Swap()
    local realChangd, node
    for i = 1, count do
        node = nodesNeedUpdate[i]
        realChangd = node:Process()
        if realChangd and node.Parent then
            self:UpdateNode(node.Parent)
        end
    end
end

--- 注册一个红点
---@public
---@param name string
---@param ... any
---@return string @ 红点标识符
function RedDotSystem:RegisterRedDot(name, ...)
    local node = self.Model:FindOrCreateNode(name, ...)
    if node then
        return node.FullName
    end
    return ""
end

--- 反注册红点
---@public
---@param name string
---@param ... any
function RedDotSystem:UnregisterRedDot(name, ...)
    self.Model:RemoveNode(name, ...)
end

--- 更新一个红点
---@public
---@param name string
---@param ... any
function RedDotSystem:UpdateRedDot(name, ...)
    local node = self.Model:GetNode(name, ...)
    if node then
        self:UpdateNode(node)
    end
end

--- 绑定一个红点到widget
---@public
---@param uiScript UIComponent
---@param widget UWidget
---@param name string
---@param ... any
function RedDotSystem:AttachWidget(uiScript, widget, name, ...)
    self.Model:AttachWidget(uiScript, widget, name, ...)
end

--- 解绑一个红点到widget
---@public
---@param widget UWidget
---@param hideOrDestroyRedDotUI boolean @ 隐藏/销毁红点UI，  nil|true: 隐藏widget，false: 销毁widget
function RedDotSystem:DetachWidget(widget, hideOrDestroyRedDotUI)
    self.Model:DetachWidget(widget, hideOrDestroyRedDotUI)
end

--- 添加一个红点监听
---@public
---@param nameRedDot string
---@param obj any
---@param func string|fun(name:string, ...):boolean
function RedDotSystem:AddListener(nameRedDot, obj, func )
    assert(self.Model, "has no model")
    self.Model:SetListener(nameRedDot, obj, func)
end

function RedDotSystem:CallListener(redDotCfgName, ...)
    local node = self.Model:GetNode(redDotCfgName)
end

--- 界面关闭时，清除红点
---@public
function RedDotSystem:OnUIClosed(uid, uiScript)
    local node = self.Model:GetNodeByUIScript(uiScript)
    if node then
        node:DetachWidget(true)
    end
end

---@public
---@param name string
---@param ... any
function RedDotSystem:MarkOnceRedDot(name, ...)
    local node = self.Model:FindOrCreateOnceNode(name, ...)
    if node then
        self:UpdateRedDot(name, ...)
    end
end

function RedDotSystem:RemoveOnceRedDot(name, ...)
end

function RedDotSystem:IsRedDotActive(name, ...)
    local fullName = self.Model.ComposeFullName(name, ...)
    local node = self.Model:GetNode(fullName)
    if node then
        return node:IsActive()
    end

    return false
end

function RedDotSystem:SyncOnceRedPoints(redDotInfo)
    local ERedPointEnumData = Enum.ERedPointEnumData
    local TableData = Game.TableData
    local model = self.Model

    local redPointTriggerType
    local RedPointEnumData = TableData.GetRedPointEnumDataTable()
    for key, value in ksbcpairs(redDotInfo) do
        redPointTriggerType = value.RedPointTriggerType
        if redPointTriggerType == ERedPointEnumData.REDPOINT_JOIN_COMBAT or
            redPointTriggerType == ERedPointEnumData.REDPOINT_ASSIST_COMBAT then
            goto continue
        elseif redPointTriggerType == ERedPointEnumData.REDPOINT_SKILL_LIST then
            local skillidmap = TableData.Get_skillId2UniqueIdMap()
            if not skillidmap[value.TypePatamID] then
                goto continue
            end
            local skilldata = TableData.GetRoleSkillUnlockDataRow(skillidmap[value.TypePatamID].ID)
            if not skilldata.IsDisplay then
                goto continue
            end
        elseif redPointTriggerType == ERedPointEnumData.REDPOINT_DUNGEON_UNLOCK then
            local dungeondata = TableData.GetDungeonDataRow(value.TypePatamID).Type - 1
            local data = RedPointEnumData[value.RedPointTriggerType]
            local node = model:FindOrCreateOnceNode(data.Name, dungeondata, value.TypePatamID)
            if node then
                node:SetAliveFlag(true)
                node:SetUnlockFlag(data.Unlock)
            end
            goto continue
        elseif redPointTriggerType == ERedPointEnumData.REDPOINT_SOCIALACTION_ITEM then
            local socialActionInfo = TableData.GetSocialActionDataRow(value.TypePatamID)
            if socialActionInfo then
                local data = RedPointEnumData[value.RedPointTriggerType]
                local node = model:FindOrCreateOnceNode(data.Name, socialActionInfo.class, value.TypePatamID)
                if node then
                    node:SetAliveFlag(true)
                    node:SetUnlockFlag(data.Unlock)
                end
            end
            goto continue
        elseif redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_FASHION_ITEM then
            goto continue
        end

        local data = RedPointEnumData[value.RedPointTriggerType]
        local node = model:FindOrCreateOnceNode(data.Name, value.TypePatamID)
        if node then
            node:SetAliveFlag(true)
            node:SetUnlockFlag(data.Unlock)
        end
        ::continue::
    end
end

function RedDotSystem:SyncOnceRedDot(RedPointInfo)
    local redPointTriggerType = RedPointInfo.RedPointTriggerType
    local TableData = Game.TableData
    local RedPointEnumData = TableData.GetRedPointEnumDataTable()
    if redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_DUNGEON_UNLOCK then
        Log.Debug("Sync Red dot: Type: Dungeon")
    end
    if redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_JOIN_COMBAT
        or redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_ASSIST_COMBAT
        or redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_FASHION_ITEM then
        return
    elseif redPointTriggerType == Enum.ERedPointEnumData.REDPOINT_DUNGEON_UNLOCK then
        local dungeondata = TableData.GetDungeonDataRow(RedPointInfo.TypePatamID).Type - 1
        local data = RedPointEnumData[RedPointInfo.RedPointTriggerType]
        local node = self.Model:FindOrCreateOnceNode(data.Name, dungeondata, RedPointInfo.TypePatamID)
        if node then
            node:SetAliveFlag(true)
            node:SetUnlockFlag(data.Unlock)
            self:UpdateNode(node)
        end
        return
    end

    local data = RedPointEnumData[RedPointInfo.RedPointTriggerType]
    if not data then
        Log.Error("RedPointSystem:SyncOnceRedPoint: RedPointEnumData not found,RedPointInfo=%s",
            table.tostring(RedPointInfo))
        return
    end

    local node = self.Model:FindOrCreateOnceNode(data.Name, RedPointInfo.TypePatamID)
    if node then
        node:SetAliveFlag(true)
        node:SetUnlockFlag(data.Unlock)
        self:UpdateNode(node)
    end
end
----------------------------------------------------------------------------------
--- private
----------------------------------------------------------------------------------
function RedDotSystem:UpdateNode(node)
    if node then
        node:MarkDirty(true)
        self.DirtyNodes:Push(node)
    end
end

return RedDotSystem