local UI_DiyText = kg_require("Framework.KGFramework.KGUI.Component.Tools.UI_DiyText")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")
---@class MainChapterEndPerformPanel : UIPanel
---@field view MainChapterEndPerformPanelBlueprint
local MainChapterEndPerformPanel = DefineClass("MainChapterEndPerformPanel", UIPanel)

MainChapterEndPerformPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MainChapterEndPerformPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MainChapterEndPerformPanel:InitUIData()
	self.chapterId = nil
	self.questId = nil
	self.rewardData = {}
end

--- UI组件初始化，此处为自动生成
function MainChapterEndPerformPanel:InitUIComponent()
    ---@type UI_DiyText
    self.Title_luaCom = self:CreateComponent(self.view.Title_lua, UI_DiyText)
    ---@type UIListView childScript: ItemRewardNew
    self.EquipList_luaCom = self:CreateComponent(self.view.EquipList_lua, UIListView)
end

---UI事件在这里注册，此处为自动生成
function MainChapterEndPerformPanel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MainChapterEndPerformPanel:InitUIView()
end

---面板打开的时候触发
function MainChapterEndPerformPanel:OnRefresh(chapterId, questId)
	if chapterId == nil then
		Log.ErrorFormat("MainChapterStartPerformPanel:OnRefresh chapterId is nil, questId: %s", questId or "")
		questId = QuestUtils.DefaultTestMainChapterPerformId
	end
	self.chapterId = chapterId
	self.questId = questId
	self:refreshChapterInfo()
	self:playEndAnim()
end

--- 此处为自动生成
function MainChapterEndPerformPanel:on_Btn_ClickArea_lua_Clicked()
end

function MainChapterEndPerformPanel:refreshChapterInfo()
	local chapterPerformData = Game.TableData.GetMainChapterPerformDataRow(self.chapterId)
	self:SetImage(self.view.C7Image_Card02_lua, chapterPerformData.CardFrontImg)
	self:LoadRes(chapterPerformData.CardBackImg, function(res)
		local dynamicMaterial = self.view.C7Image_Card_lua:GetDynamicMaterial()
		dynamicMaterial:SetTextureParameterValue("MainTex", res)
	end, false)
	self:LoadRes(chapterPerformData.CardBackIcon, function(res)
		local dynamicMaterial = self.view.Icon_lua:GetDynamicMaterial()
		dynamicMaterial:SetTextureParameterValue("MainTex", res)
	end, false)
	self.Title_luaCom:Refresh(chapterPerformData.TitleCN)
	self.view.RingIndex_lua:SetText(chapterPerformData.ChapterNo)
	self.view.CutIndex_lua:SetText(chapterPerformData.ActNo)
	self.view.TitleMistery_lua:SetText(chapterPerformData.TitleEN)
	self.view.SubTitleMistery_lua:SetText(chapterPerformData.SubTitleOnEnd)
	self.view.Desc_lua:SetText(chapterPerformData.Desc)
	table.clear(self.rewardData)
	Game.QuestSystem:GetRewardByQuestID(self.questId, self.rewardData)
	local rewardListData = {}
	for _, info in pairs(self.rewardData) do
		local itemId, num = info.ItemID, info.Count
		if itemId then
			---@type ItemRewardNewParam
			local data = {
				id = itemId,
				num = num,
			}
			rewardListData[#rewardListData + 1] = data
		end
	end
	self.EquipList_luaCom:Refresh(rewardListData)
end

function MainChapterEndPerformPanel:playEndAnim()
	self:PlayAnimation(self.view.Ani_Card_Endin, function() self:onCardEndInAnimEnd() end)
	self:StartTimer("EnsureEndAnimEnd", function() self:onAllEndAnimEnd() end, 5100, 1)
end

function MainChapterEndPerformPanel:onCardEndInAnimEnd()
	self:StartTimer("WaitEnd", function() self:PlayAnimation(self.view.Ani_Card_Endout, function() self:onAllEndAnimEnd() end) end,
		1700, 1)
end

function MainChapterEndPerformPanel:onAllEndAnimEnd()
	---这里还需要再通知服务端
	Game.me.remote.ReqMainStoryChapterUIClosed(self.chapterId)
	self:CloseSelf()
end

return MainChapterEndPerformPanel
