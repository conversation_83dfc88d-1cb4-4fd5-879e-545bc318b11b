local MainChapterEndPerfomComp = kg_require("Gameplay.LogicSystem.Perform.MainChapterEndPerfomComp")
local UI_DiyText = kg_require("Framework.KGFramework.KGUI.Component.Tools.UI_DiyText")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class MainChapterPerformPanel : UIPanel
---@field view MainChapterPerformPanelBlueprint
local MainChapterPerformPanel = DefineClass("MainChapterPerformPanel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")

MainChapterPerformPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MainChapterPerformPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MainChapterPerformPanel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function MainChapterPerformPanel:InitUIComponent()
    ---@type UI_DiyText
    self.Title_luaCom = self:CreateComponent(self.view.Title_lua, UI_DiyText)
    ---@type MainChapterEndPerfomComp
    self.WBP_PerformEnd_luaCom = self:CreateComponent(self.view.WBP_PerformEnd_lua, MainChapterEndPerfomComp)
end

---UI事件在这里注册，此处为自动生成
function MainChapterPerformPanel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MainChapterPerformPanel:InitUIView()
end

---面板打开的时候触发
---@param kind QuestSystem.PlayChapterEnum @章节效果类型枚举
---@param actionParam QuestAction.PlayStartTask|QuestAction.PlayEndTask @参数
---@param questID number @任务ID
function MainChapterPerformPanel:OnRefresh(kind, actionParam, questID)
	self:refreshPanel(kind, actionParam, questID)

	if kind == Game.QuestSystem.ReminderEnum.Start then
		--章节开始
		self:playStart()
	elseif kind == Game.QuestSystem.ReminderEnum.End then
		--章节结束
		self:playEnd()
	end
end

--更新界面信息
---@param kind QuestSystem.PlayChapterEnum @章节效果类型枚举
---@param actionParam QuestAction.PlayStartTask|QuestAction.PlayEndTask @参数
---@param questID number @任务ID
function MainChapterPerformPanel:refreshPanel(kind, actionParam, questID)
	if kind == Game.QuestSystem.ReminderEnum.End then
		self:refreshChapterEndInfo(actionParam, questID)
	else
		self:refreshChapterStartInfo(actionParam, questID)
	end
end

---@param actionParam QuestAction.PlayStartTask|QuestAction.PlayEndTask @参数
---@param questID number @任务ID
function MainChapterPerformPanel:refreshChapterEndInfo(actionParam, questID)
	self:SetImage(self.view.C7Image_Card02_lua, actionParam.CardImage)
	self:LoadRes(actionParam.CardBg, function(res)
		local dynamicMaterial = self.view.C7Image_Card_lua:GetDynamicMaterial()
		dynamicMaterial:SetTextureParameterValue("MainTex", res)
	end, false)
	self.WBP_PerformEnd_luaCom:RefreshChapterInfo(actionParam, questID)
end

---@param actionParam QuestAction.PlayStartTask|QuestAction.PlayEndTask @参数
---@param questID number @任务ID
function MainChapterPerformPanel:refreshChapterStartInfo(actionParam, questID)
	self.Title_luaCom:Refresh(actionParam.Title)
	self.view.RingIndex_lua:SetText(actionParam.RingIndex)
	self.view.CutIndex_lua:SetText(actionParam.CutIndex)
	self.view.TitleMistery_lua:SetText(actionParam.TitleMistery)
	self.view.Desc_lua:SetText(actionParam.Desc)
	self:SetImage(self.view.C7Image_ContentCard_lua, actionParam.CardImage)
	self:LoadRes(actionParam.CardBg, function(res)
		local dynamicMaterial = self.view.C7Image_ContentCard2_lua:GetDynamicMaterial()
		dynamicMaterial:SetTextureParameterValue("MainTex", res)
	end, false)
end

---@private @章节开始
function MainChapterPerformPanel:playStart()
	self.view.Card_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.view.ContentCard_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self:PlayAnimation(self.view.Ani_Card_Start, function() self:CloseSelf() end, self.userWidget, 0, 1)
end

---@private @章节结束
function MainChapterPerformPanel:playEnd()
	self.view.Card_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.ContentCard_lua:SetVisibility(ESlateVisibility.Collapsed)
	self:PlayAnimation(self.view.Ani_Card_Endin, function() self:onCardEndInAnimEnd() end, self.userWidget, 0, 1)
end

function MainChapterPerformPanel:onCardEndInAnimEnd()
	self:StartTimer("WaitEnd", function()
		self:PlayAnimation(self.view.Ani_Card_Endout, function() self:CloseSelf() end,  self.userWidget, 0, 1) end,
		1700, 1)
end

--- 此处为自动生成
function MainChapterPerformPanel:on_Btn_ClickArea_lua_Clicked()
end

return MainChapterPerformPanel
