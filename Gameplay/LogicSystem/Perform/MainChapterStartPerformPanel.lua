local UI_DiyText = kg_require("Framework.KGFramework.KGUI.Component.Tools.UI_DiyText")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")
---@class MainChapterStartPerformPanel : UIPanel
---@field view MainChapterStartPerformPanelBlueprint
local MainChapterStartPerformPanel = DefineClass("MainChapterStartPerformPanel", UIPanel)

MainChapterStartPerformPanel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MainChapterStartPerformPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MainChapterStartPerformPanel:InitUIData()
	self.chapterId = nil
	self.questId = nil
end

--- UI组件初始化，此处为自动生成
function MainChapterStartPerformPanel:InitUIComponent()
    ---@type UI_DiyText
    self.Title_luaCom = self:CreateComponent(self.view.Title_lua, UI_DiyText)
end

---UI事件在这里注册，此处为自动生成
function MainChapterStartPerformPanel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MainChapterStartPerformPanel:InitUIView()
end

---面板打开的时候触发
function MainChapterStartPerformPanel:OnRefresh(chapterId, questId)
	if chapterId == nil then
		Log.ErrorFormat("MainChapterStartPerformPanel:OnRefresh chapterId is nil, questId: %s", questId or "")
		questId = QuestUtils.DefaultTestMainChapterPerformId
	end
	self.chapterId = chapterId
	self.questId = questId
	self:refreshChapterStartInfo()
	self:playStartAnim()
end

--- 此处为自动生成
function MainChapterStartPerformPanel:on_Btn_ClickArea_lua_Clicked()
end

function MainChapterStartPerformPanel:refreshChapterStartInfo()
	local chapterPerformData = Game.TableData.GetMainChapterPerformDataRow(self.chapterId)
	self.Title_luaCom:Refresh(chapterPerformData.TitleCN)
	self.view.RingIndex_lua:SetText(chapterPerformData.ChapterNo)
	self.view.CutIndex_lua:SetText(chapterPerformData.ActNo)
	self.view.TitleMistery_lua:SetText(chapterPerformData.TitleEN)
	self.view.Desc_lua:SetText(chapterPerformData.Desc)
	self:SetImage(self.view.C7Image_ContentCard_lua, chapterPerformData.CardFrontImg)
	self:LoadRes(chapterPerformData.CardBackImg, function(res)
		local dynamicMaterial = self.view.C7Image_ContentCard2_lua:GetDynamicMaterial()
		dynamicMaterial:SetTextureParameterValue("MainTex", res)
	end, false)
end

function MainChapterStartPerformPanel:playStartAnim()
	self:PlayAnimation(self.view.Ani_Card_Start, function() self:onStartAnimEnd() end)
end

function MainChapterStartPerformPanel:onStartAnimEnd()
	---这里还需要再通知服务端
	self:CloseSelf()
end

function MainChapterStartPerformPanel:OnClose()
	---向服务器通知一下 @nongtanggao
	Game.me.remote.ReqMainStoryChapterUIClosed(self.chapterId)
end

return MainChapterStartPerformPanel
