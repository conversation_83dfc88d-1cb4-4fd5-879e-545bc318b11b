---@class ClimateSystem : SystemBase
local ClimateSystem = DefineClass("ClimateSystem", SystemBase)
local SceneUtils = kg_require("Shared.Utils.SceneUtils").SceneUtils
local GameplayStatics = import("GameplayStatics")
local worldConst = kg_require("Shared.Const.WorldConst")

---@class LevelMapClimateData
---@field id number
---@field worldType number
---@field bloodMoon number
---@field phaseAngle number
---@field climateId number
---@field bUseDefaultTime boolean 使用场景默认的TOD时间

--region Enum
ClimateSystem.Enum = {
	--月相
	EPhaseType = {
		NewMoon = 1,            --朔月
		CrescentMoon = 2,       --上眉月
		FirstQuarterMoon = 3,   --上弦月
		GibbousMoon = 4,        --凸月
		FullMoon = 5,           --满月
		WaningMoon = 6,         --残月
		LowerStringMoon = 7,    --下弦月
		EyebrowMoon = 8,        --下眉月
	},
	--红月
	EBloodMoonType = {
		Scarlet = 1,            --绯月
		BloodRed = 2,           --血月
	},

	--时钟时段，已与表格对其请勿改动！
	ETimePeriod = {
		Morning = 1,            --晨
		Daytime = 2,            --昼
		Evening = 3,            --夕
		Night = 4,              --夜
	},

	--时钟表盘背景
	EClockState = {
		Day = 1,
		Night = 2,
	},

	--时钟天气组件状态
	EClockWeatherCompPeriod = {
		Morning = 0,            --晨
		Daytime = 1,            --昼
		Evening = 2,            --夕
		Night = 3,              --夜
	},
}

--endregion

function ClimateSystem:onCtor()
	---@type number
	self.lastTimeRtpcValue = nil
	self.gameTimeData = nil
	self.InitServerTime = nil
	self.GameTime = nil
	self.cycleTime = nil

	-- 血月和月相
	self.bloodMoonCurveID = nil
	self.phaseAngleCurveID = nil
	self.bloodMoonCurveAssetID = nil
	self.phaseAngleCurveAssetID = nil
	---@type CurveFloat
	self.bloodMoonCurve = nil
	---@type CurveFloat
	self.phaseAngleCurve = nil
	---@type number
	self.curBloodMoonFactor = 0.0
	---@type number
	self.curPhaseAngleFactor = 180.0

	---@type GeographyAndDateTimeController
	self.geographyAndDateTimeController = nil
	---@type ClimateController
	self.climateController = nil
	---@type LevelMapClimateData 当前地图信息
	self.currentLevelMap = {}
	self.currentClimateId = nil
	self.controlledClimateId = nil
	self.lastCurveTime = nil
	self.timeFlowSpeed = nil
	self.bForceLocalControl = nil
	self.bInDayTime = true
	self.LightOnTime = 5
	self.LightOffTime = 19
	self.bLightInSequenceControl = false	--路灯是否由CutScene控制
	self.TODLightDuration = 0.8
	self.timePeriods = nil
	self.lastJumpTime = 0
	self.stopTimeElapsed = nil
	self.bMinChange = false --分钟是否改变
	self.bShowClimateBtn = true --是否显示按钮
	--下一个时段的 EndPoint
	self.NextTime = { hour = 0, min = 0 }
	self.bInLoaded = false	--处理在loaded的时候不要播音频的问题
	---@type number
	self.curTimePeriod = nil

	self.climateManagerTimer = nil
	self.bHudWeatherShow = false

	---@type number 上一次请求的时间
	self.lastReqClimateInfoTime = -1
	---@type number 向服务端请求数据的cd（秒）
	self.reqClimateInfoCd = 5
	---@type table 用于请求限频时缓存上一次请求的结果
	self.cachedRegionClimateInfo = nil
end

function ClimateSystem:onInit()
	self.model = require("Gameplay.LogicSystem.ClimateSystem.ClimateModel").new(false, true)

	self.gameTimeData = Game.TableData.GetGameTimeDataRow(1)
	self.InitServerTime = self.gameTimeData.Time
	self.cycleTime = self.gameTimeData.CycleTime

	local hour, min, sec = Game.TableData.GetClimateConstDataRow("LightOnTime").Value:match("(%d+):(%d+):(%d+)")
	self.LightOnTime =  tonumber(hour) * 3600 + tonumber(min) * 60 + tonumber(sec)
	hour, min, sec = Game.TableData.GetClimateConstDataRow("LightOffTime").Value:match("(%d+):(%d+):(%d+)")
	self.LightOffTime =  tonumber(hour) * 3600 + tonumber(min) * 60 + tonumber(sec)

	self.timeFlowSpeed = 1.0
	self.lastCurveTime = 0

	self.bloodMoonCurveID, self.bloodMoonCurveAssetID = Game.AssetManager:SyncLoadAssetKeepReferenceID(self.gameTimeData.BloodMoonCurve)
	self.phaseAngleCurveID, self.phaseAngleCurveAssetID = Game.AssetManager:SyncLoadAssetKeepReferenceID(self.gameTimeData.PhaseAngleCurve)

	self.bloodMoonCurve = Game.ObjectActorManager:GetObjectByID(self.bloodMoonCurveAssetID)
	self.phaseAngleCurve = Game.ObjectActorManager:GetObjectByID(self.phaseAngleCurveAssetID)
	
	self.timePeriods = {}
	self:InitTimePeriods()

	self.bAtNight = nil
	self.audioDayTime = tonumber(Enum.EAudioConstData.MUS_DAY_TIME)
	self.audioNightTime = tonumber(Enum.EAudioConstData.MUS_NIGHT_TIME)
	
	self:AddListenerV2(EEventTypesV2.ON_TOGGLE_HUD_CLIMATE_ALL, "OnToggleHUDClimateAll", self)
	Game.EventSystem:AddListener(_G.EEventTypes.CINEMATIC_ON_END, self, self.OnCutSceneEnd)
end

function ClimateSystem:InitTimePeriods()
	table.clear(self.timePeriods)
	self.realTimePerDay, self.timePeriods, self.ETimeStartPoint, self.ETimeEndPoint = SceneUtils.InitTimePeriods(self.timeFlowSpeed, self.timePeriods)
end

function ClimateSystem:onUnInit()
	Game.AssetManager:RemoveAssetReferenceByLoadID(self.bloodMoonCurveID)
	Game.AssetManager:RemoveAssetReferenceByLoadID(self.phaseAngleCurveID)
end

function ClimateSystem:CalCurrentGameTime()
	if not self.GameTime then
		self.GameTime = {hour = 0, min = 0, sec = 0}
	end

	local inLastJumpTime, inElapsedStopTime
	local spaceState = Game.NetworkManager.GetLocalSpace()
	if self.lastJumpTime and self.lastJumpTime ~= 0 then
		inLastJumpTime, inElapsedStopTime = self.lastJumpTime, self.stopTimeElapsed
	elseif spaceState then
		inLastJumpTime, inElapsedStopTime = spaceState.lastJumpTime, spaceState.elapsedStopTime
	end
	local gameTime, timePeriodIndex = SceneUtils.CalCurrentGameTime(inLastJumpTime, inElapsedStopTime,
		self.InitServerTime, self.realTimePerDay, self.timePeriods)

	self:setGameTime(SceneUtils.ConvertGameTime(gameTime))
	self.curTimePeriod = self.timePeriods[timePeriodIndex].id
end

function ClimateSystem:OnTick()
	self:CalCurrentGameTime()
	self:setDateTime()
	self:setGameTimeRtpc(self.GameTime.hour, self.GameTime.min)
	if self.geographyAndDateTimeController then
		local curveTime = ((_G._now() - self.InitServerTime * 1000) / 3600000 * self.timeFlowSpeed + self.lastCurveTime) % self.cycleTime
		self:setBloodMoon(curveTime)
		self:setPhaseAngle(curveTime)
	end
	self:updateCurrentClimateId()

	if self.bMinChange then
		local curr, timePeriod, min_angle, hour_angle = Game.ClimateSystem.ClockTimeTick(self.NextTime)
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GAME_TIME_CLOCK_CHANGE, curr, timePeriod, min_angle, hour_angle, self.NextTime)
	end
end

function ClimateSystem:updateCurrentClimateId()
	local spaceState = Game.NetworkManager.GetLocalSpace()
	local curFieldId = self:GetBelongedRegionID()
	if spaceState and spaceState.region2ClimateList then
		for regionId, climateIDList in pairs(spaceState.region2ClimateList) do
			local climateData = Game.TableData.GetRegionClimateDataRow(regionId)
			if climateData then
				local mapFieldId = climateData.MapFieldId
				if curFieldId == nil or (mapFieldId and mapFieldId > 0 and mapFieldId == curFieldId) then
					self.RegionId = regionId
					self.controlledClimateId = self:RefreshClimate(climateIDList[1])
					return
				end
			end
		end
	end

	self.controlledClimateId = self:RefreshClimate(self.controlledClimateId)
end

--region setdata
function ClimateSystem:setGameTime(hour,min,sec)
	self.GameTime.hour = hour
	self.bMinChange = self.GameTime.min ~= min
	self.GameTime.min = min
	self.GameTime.sec = sec
end

function ClimateSystem:setDateTime()
	local currentTimeInSeconds = self.GameTime.hour * 3600 + self.GameTime.min * 60 + self.GameTime.sec
	if self.geographyAndDateTimeController and self.currentLevelMap.bUseDefaultTime == false then
		self.geographyAndDateTimeController.Hours = self.GameTime.hour
		self.geographyAndDateTimeController.Minutes = self.GameTime.min
		self.geographyAndDateTimeController.Seconds = self.GameTime.sec
	end

	if (currentTimeInSeconds >= self.LightOnTime or currentTimeInSeconds < self.LightOffTime) and self.bInDayTime then
		self.bInDayTime = false
		if not self.bLightInSequenceControl then
			Game.WorldManager:TurningTODStreetlight(self.TODLightDuration, self.bInDayTime)
		end
	elseif currentTimeInSeconds >= self.LightOffTime and currentTimeInSeconds < self.LightOnTime and not self.bInDayTime then
		self.bInDayTime = true
		if not self.bLightInSequenceControl then
			Game.WorldManager:TurningTODStreetlight(self.TODLightDuration, self.bInDayTime)
		end
	end
end

function ClimateSystem:setBloodMoon(curveTime)
	local factor = 0.0
	if self.currentLevelMap.bloodMoon then
		factor = self.currentLevelMap.bloodMoon
	elseif self.bloodMoonCurve then
		factor = self.bloodMoonCurve:GetFloatValue(curveTime)
	end

	if self.curBloodMoonFactor ~= factor then
		self.curBloodMoonFactor = factor
		self.geographyAndDateTimeController.BloodMoonFactor = factor
	end
end

function ClimateSystem:setPhaseAngle(curveTime)
	local factor = 180.0
	if self.currentLevelMap.phaseAngle then
		factor = self.currentLevelMap.phaseAngle
	elseif self.phaseAngleCurve then
		factor = self.phaseAngleCurve:GetFloatValue(curveTime)
	end

	if self.curPhaseAngleFactor ~= factor then
		self.curPhaseAngleFactor = factor
		self.geographyAndDateTimeController.PhaseAngle = factor
	end
end
--endregion

function ClimateSystem:OnWorldMapLoadComplete(levelId)
	self.bInLoaded = false
	self.RegionId = nil
	self.bForceLocalControl = false
	self.lastCurveTime = 0
	self.timeFlowSpeed = 1.0
	self.lastJumpTime = 0
	self.stopTimeElapsed = nil
	
	-- 原则上如果level加载到这TOD还没加载出来，说明这张地图就没有配置天气，在这之后加载TOD属于流程不规范，功能上不做支持。
	self.geographyAndDateTimeController = GameplayStatics.GetActorOfClass(_G.GetContextObject(), import("GeographyAndDateTimeController"))
	self.climateController = GameplayStatics.GetActorOfClass(_G.GetContextObject(), import("ClimateController"))
	if not IsValid_L(self.geographyAndDateTimeController) then
		self.geographyAndDateTimeController = nil
	end

	self:SetLevelMapClimateData(levelId)
	self:StartTickTimer()

	-- 进入场景时根据当前日夜触发一下效果
	Log.DebugFormat("[OnWorldMapLoadComplete] bAtNight=%s", self.bAtNight)
	if self.bAtNight then
		Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.MUS_GLOBAL_TIME_NIGHT)
	else
		Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.MUS_GLOBAL_TIME_DAY)
	end
end

function ClimateSystem:StartTickTimer()
	self:StopTickTimer()

	-- tick的间隔为游戏内分钟的最短变化时间
	local duration = 1000
	if self.timePeriods then
		local maxFlowSpeed = 1.0
		for _, period in ipairs(self.timePeriods) do
			maxFlowSpeed = math.max(maxFlowSpeed, period.flowSpeed)
		end
		duration = 60000 / maxFlowSpeed / self.timeFlowSpeed
	end

	self.climateManagerTimer = Game.TimerManager:CreateTimerAndStart(function()
		self:OnTick()
	end, duration, -1, nil, "ClimateManagerTimer")
end

function ClimateSystem:StopTickTimer()
	if self.climateManagerTimer then
		Game.TimerManager:StopTimerAndKill(self.climateManagerTimer)
		self.climateManagerTimer = nil
	end
end

function ClimateSystem:OnWorldMapDestroy(levelId)
	self:StopTickTimer()
	self.bInLoaded = true
	self.geographyAndDateTimeController = nil
	self.bForceLocalControl = false
	self.climateController = nil
	self.currentClimateId = nil
	self.stopTimeElapsed = nil
	self.currentLevelMap = nil
	self.controlledClimateId = nil
end

function ClimateSystem:SetLevelMapClimateData(levelId)
	table.clear(self.currentLevelMap)
	self.currentLevelMap = {}
	self.currentLevelMap.id = levelId
	local hideClimateBtn = false
	local levelMapData = Game.TableData.GetLevelMapDataRow(levelId)
	if levelMapData then
		-- 表里不配time，TOD使用美术设置的默认时间
		self.currentLevelMap.bUseDefaultTime = levelMapData.Hour == nil
		self.currentLevelMap.climateId = levelMapData.Climate
		self.currentLevelMap.bloodMoon = levelMapData.BloodMoon
		self.currentLevelMap.phaseAngle = levelMapData.PhaseAngle
		self.currentLevelMap.worldType = levelMapData.Type
		hideClimateBtn = levelMapData.HideClimateBtn or false
	end

	local spaceState = Game.NetworkManager.GetLocalSpace()
	if spaceState then
		self.currentLevelMap.worldType = spaceState.WorldType
		if spaceState.climateID and spaceState.climateID > 0 then
			self.currentLevelMap.climateId = spaceState.climateID
		end
	end

	for k, v in pairs(self.currentLevelMap) do
		if v == -1 then
			self.currentLevelMap[k] = nil
		end
	end

	-- 判断是否隐藏气候按钮
	self:SetbShowClimateBtn(not hideClimateBtn)
end

function ClimateSystem:OnSpaceClimateChange(newId)
	if newId and newId > 0 then
		if self.currentLevelMap == nil then
			self.currentLevelMap = {}
		end
		self.currentLevelMap.climateId = newId
	end
end

--region 真正处理天气变化
function ClimateSystem:RefreshClimate(climateID)
	if not self.climateController then
		return climateID
	end

	local runningClimateID
	if self.bForceLocalControl then
		runningClimateID = self.controlledClimateId
	elseif self.currentLevelMap.climateId then
		runningClimateID = self.currentLevelMap.climateId
	else
		runningClimateID = climateID
	end

	if not runningClimateID or runningClimateID == -1 or self.currentClimateId == runningClimateID then
		return self.currentClimateId
	end

	local previousClimateId = self.currentClimateId
	self.currentClimateId = runningClimateID
	self:UpdateClimateAudio(previousClimateId, self.currentClimateId)

	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_CLIMATE_CHANGE_UI, previousClimateId, self.currentClimateId)
	local data = Game.TableData.GetCrowdClimateSettingDataRow(self.currentClimateId)
	if data then
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_CLIMATE_CHANGE, data, false)
		Game.MassCharacterManager:OnClimateChangeToMassManager(data.ClimateScaleRatio)
	end
	
	self:UpdateClimateController(self.currentClimateId)
	return runningClimateID
end

function ClimateSystem:UpdateClimateController(climateID)
	local climateSetting = Game.TableData.GetClimateSettingDataRow(climateID)
	if not climateSetting or not self.climateController then
		return
	end
	local timePeriodId = self.curTimePeriod
	self.climateController.Cloud = climateSetting.Cloud[timePeriodId]
	self.climateController.Fog = climateSetting.Fog[timePeriodId]
	self.climateController.Wind = climateSetting.Wind[timePeriodId]
	self.climateController.Rain = climateSetting.Rain[timePeriodId]
	self.climateController.Snow = climateSetting.Snow[timePeriodId]
	self.climateController.Thunderstorm = climateSetting.Thunderstorm[timePeriodId]
	if climateID == Enum.EClimateType.Rainbow and self.curTimePeriod ~= ClimateSystem.Enum.ETimePeriod.Night then
		self.climateController.Rainbow = 1.0
	else
		self.climateController.Rainbow = 0.0
	end
end

function ClimateSystem:UpdateClimateAudio(pre, cur)
	if self.bInLoaded then
		return
	end
	Log.DebugFormat("[Aduio_OnCurrentClimateChange] pre=%s, cur=%s", pre, cur)
	local preClimateSetting = Game.TableData.GetClimateSettingDataRow(pre)
	local curClimateSetting = Game.TableData.GetClimateSettingDataRow(cur)
	
	local bInBigWorld = self.currentLevelMap and self.currentLevelMap.worldType == worldConst.WORLD_TYPE.BIGWORLD

	-- pre
	if bInBigWorld and preClimateSetting and (preClimateSetting.ID == Enum.EClimateType.HeavyRain) then
		Log.Debug("[Aduio_OnCurrentClimateChange] post rain leave event")
		Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.LEAVE_RAIN_AMB_EVENT)
	end

	-- cur
	if curClimateSetting then
		Game.AkAudioManager:SetGroupState(curClimateSetting.AkStateGroup, curClimateSetting.AkStateValue)
	end

	if bInBigWorld and curClimateSetting and (curClimateSetting.ID == Enum.EClimateType.HeavyRain) then
		Log.Debug("[Aduio_OnCurrentClimateChange] post rain enter event")
		Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.ENTER_RAIN_AMB_EVENT)
	end
end
--endregion

--region gametime
-- 设置游戏时间的Rtpc,需要将分钟数切换为十进制
---@private
---@param hour number
---@param min number
function ClimateSystem:setGameTimeRtpc(hour, min)
	local prcTime = tonumber(string.format("%.2f", min / 60)) + hour
	if prcTime ~= self.lastTimeRtpcValue then
		Game.AkAudioManager:SetRtpcValue(Enum.EAudioConstData.GAME_TIME_RTPC, prcTime)
		self.lastTimeRtpcValue = prcTime
	end

	if self.bAtNight == nil then
		if (self.audioNightTime <= prcTime) or (prcTime <= self.audioDayTime) then
			self.bAtNight = true
		else
			self.bAtNight = false
		end
	end
	
	if (self.bAtNight == false) and ((self.audioNightTime <= prcTime) or (prcTime <= self.audioDayTime)) then
		self.bAtNight = true
		Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.MUS_GLOBAL_TIME_NIGHT)
	elseif (self.bAtNight == true) and ((self.audioDayTime < prcTime) and (prcTime < self.audioNightTime)) then
		self.bAtNight = false
		Game.AkAudioManager:PostEvent2D(Enum.EAudioConstData.MUS_GLOBAL_TIME_DAY)
	end
end

function ClimateSystem:SetCurrentGameTime(hour, min, bElapsed)
	local preGameTime = self.GameTime.hour * 3600 + self.GameTime.min * 60 + self.GameTime.sec
	local preRealTime = self:CalGameTimeToRealTime(preGameTime)
	local curGameTime = hour * 3600 + min * 60
	local curRealTime = self:CalGameTimeToRealTime(curGameTime)
	self.lastJumpTime = self.lastJumpTime + curRealTime - preRealTime
	if not bElapsed then
		self.stopTimeElapsed = _G._now()
	else
		self.stopTimeElapsed = nil
	end
end

function ClimateSystem:CalGameTimeToRealTime(gameTime)
	return SceneUtils.CalGameTimeToRealTime(gameTime, self.timePeriods, self.realTimePerDay)
end
--endregion

--region 路灯相关
function ClimateSystem:IsDayTime()
	return self.bInDayTime
end

function ClimateSystem:OnCutSceneEnd()
	self.bLightInSequenceControl = false
end
--endregion

--region 服务器
function ClimateSystem:ReqAllRegionClimateInfo()
	-- 防止请求限频，在本地加个缓存
	if _G._now() - self.lastReqClimateInfoTime > self.reqClimateInfoCd * 1000 then
		self.lastReqClimateInfoTime = _G._now()
		if Game.me then
			Game.me:ReqAllRegionClimateInfo()
		end
	elseif self.cachedRegionClimateInfo then
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_RET_ALL_CLIMATE_INFO, self.cachedRegionClimateInfo)
	end
end

function ClimateSystem:GetClientCurrentClimate()
	return self.controlledClimateId
end

function ClimateSystem:SetClientCurrentClimate(climateID)
	self.bForceLocalControl = true
	self.controlledClimateId = climateID
end

function ClimateSystem:ResetCurrentClimateToServer()
	self.bForceLocalControl = false
end
--endregion

--region数据接口
function ClimateSystem:GetGameTime()
	return self.GameTime
end

function ClimateSystem:GetTimeFlowSpeed()
	return self.timeFlowSpeed
end

function ClimateSystem:GetBloodMoonFactor()
	return self.curBloodMoonFactor
end

function ClimateSystem:GetPhaseAngle()
	return self.curPhaseAngleFactor
end

function ClimateSystem:GetCurrentClimateID()
	return self.currentClimateId
end

function ClimateSystem.ClockTimeTick(NextTime)
	local time = Game.ClimateSystem:GetGameTime()
	local ETimeEP = Game.ClimateSystem.ETimeEndPoint
	local timePeriod = Game.ClimateSystem.curTimePeriod

	--update  
	if time then
		if NextTime then
			NextTime.hour = ETimeEP[timePeriod]
		end

		return Game.ClimateSystem:GetTimeInfo(time)
	end
end

function ClimateSystem:GetTimeInfo(time)
	if time then
		local timePeriod = Game.ClimateSystem.curTimePeriod

		local min_angle = 6 * time.min - 90
		local hour_angle = 30.0 * (time.hour + time.min / 60) - 180

		return time, timePeriod, min_angle, hour_angle
	end
	return nil
end

function ClimateSystem:GetBelongedRegionID()
	local curLevelId = Game.LevelManager.GetCurrentLevelID()
	if not curLevelId or not Game.me or Game.me.isDestroyed then
		return
	end
	
	local pos = {}
	pos.X, pos.Y, pos.Z = Game.me:GetPosition_P()
	if pos.X == nil then
		return nil
	end

	local ans = Game.WorldDataManager:GetSceneFieldType(pos,"Map",curLevelId)
	if ans == -1 then
		return nil
	else
		return ans
	end
end

function ClimateSystem:GetTimePeriods()
	return self.timePeriods
end

function ClimateSystem:IsOverTime(hour)
	return hour > self.ETimeEndPoint[self.timePeriods[#self.timePeriods].id] and hour < self.ETimeEndPoint[self.timePeriods[1].id]
end
--endregion

--region UI接口

-- HUD按钮
function ClimateSystem:GetbShowClimateBtn()
	return self.bShowClimateBtn
end

function ClimateSystem:SetbShowClimateBtn(bShow)
	self.bShowClimateBtn = bShow
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SHOW_CLIMATE_BTN, bShow)
	return self.bShowClimateBtn
end

--HUD小地图

function ClimateSystem:SetHUDWeatherShowingState(bShow)
	if not UI.IsShow("P_HUDBaseView") then
		self.bHudWeatherShow = false
		return
	end
	if bShow then
		self:ShowHUDWeather()
	else
		self:HideHUDWeather()
	end
end

--处理因为切换数据统计的显隐
function ClimateSystem:OnToggleHUDClimateAll(bShow)
	if bShow then
		self:SetHUDWeatherShowingState(self.bHudWeatherShow)
		if self.bHudWeatherShow then
			if Game.NewUIManager:IsShow("P_HUDBaseView") then
				Game.HUDSystem:HideUI(UICellConfig.HUD_WeatherBtn)
			end
		else
			if Game.NewUIManager:IsShow("P_HUDBaseView") then
				Game.HUDSystem:ShowUI(UICellConfig.HUD_WeatherBtn)
			end
		end
	else
		if Game.NewUIManager:IsShow(UICellConfig.HUD_Weather) then
			Game.HUDSystem:HideUI(UICellConfig.HUD_Weather)
		end
		if Game.NewUIManager:IsShow("P_HUDBaseView") then
			Game.HUDSystem:HideUI(UICellConfig.HUD_WeatherBtn)
		end
	end
end

function ClimateSystem:HideHUDWeather()
	self.bHudWeatherShow = false
	if Game.NewUIManager:IsShow(UICellConfig.HUD_Weather) then
		Game.HUDSystem:HideUI(UICellConfig.HUD_Weather)
	end
end

function ClimateSystem:ShowHUDWeather()
	self.bHudWeatherShow = true
	if not UI.IsShow(UICellConfig.HUD_Weather) then
		Game.HUDSystem:ShowUI(UICellConfig.HUD_Weather)
	end
end

function ClimateSystem:HUDWeather2HUDMiniMap()
	self:SetHUDWeatherShowingState(false)
	Game.MapSystem:SetMiniMapShowingState(true, true)
end

function ClimateSystem:HUDMiniMap2HUDWeather()
	self:SetHUDWeatherShowingState(true)
	Game.MapSystem:SetMiniMapShowingState(false, true)
end
--endregion

function ClimateSystem:IsHUDWeatherShow()
	return self.bHudWeatherShow
end

return ClimateSystem
