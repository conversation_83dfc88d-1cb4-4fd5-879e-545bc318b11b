local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local ScreenEffectComponentBase = kg_require("Gameplay/LogicSystem/ScreenEffect/ScreenEffectComponentBase")
---@class WriteMessage : ScreenEffectComponentBase
---@field view WriteMessageBlueprint
local WriteMessage = DefineClass("WriteMessage", ScreenEffectComponentBase)

WriteMessage.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function WriteMessage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function WriteMessage:InitUIData()
	self.effectID = 3000004
end

--- UI组件初始化，此处为自动生成
function WriteMessage:InitUIComponent()
    ---@type UIComButton
    self.BtnCloseCom = self:CreateComponent(self.view.BtnClose, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function WriteMessage:InitUIEvent()
    self:AddUIEvent(self.BtnCloseCom.onClickEvent, "on_BtnCloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function WriteMessage:InitUIView()
    self.BtnCloseCom:SetVisible(false)
end

---面板打开的时候触发
function WriteMessage:OnRefresh(...)
    local view = self.view
    view.Spine_Book:SetAnimation(0, "book_open2", false)
    local anim = view.Ani_Bookin
    local duration = anim:GetEndTime() - anim:GetStartTime() + 0.1
    self:PlayAnimation(view.Ani_Bookin)
    Game.TimerManager:CreateTimerAndStart(function()
		Game.GlobalEventSystem:Publish(EEventTypesV2.REMOVE_SCREEN_EFFECT, self.effectID)
    end, duration * 1000, 1, true, "WriteMessage")
end

--- 此处为自动生成
function WriteMessage:on_BtnCloseCom_ClickEvent()
	Game.GlobalEventSystem:Publish(EEventTypesV2.REMOVE_SCREEN_EFFECT, self.effectID)
end

return WriteMessage
