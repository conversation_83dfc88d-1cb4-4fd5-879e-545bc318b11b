---@class P_SkillInfoPopup : UIController
--- @field public View WBP_SkillTipsConformityView
local P_SkillInfoPopup = DefineClass("P_SkillInfoPopup", UIController)
local StringConst = require "Data.Config.StringConst.StringConst"
local WidgetEmptyComponent = kg_require("Framework.UI.WidgetEmptyComponent")

---@type ESlateVisibility
local ESlateVisibility = import("ESlateVisibility")

---@type WidgetLayoutLibrary
local WidgetLayoutLibrary = import("WidgetLayoutLibrary")

---@type SlateBlueprintLibrary
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
---@type GameplayStatics
local GameplayStatics = import("GameplayStatics")

local LibWidgetConfig = kg_require("Framework.UI.LibWidgetConfig")
-- 添加lib widget预加载声明
P_SkillInfoPopup.PreloadLibMap = {
    Text = LibWidgetConfig.Text,
}

function P_SkillInfoPopup:OnCreate()
    self.View.WBP_SkillTipsTitle.Text_Info2:SetVisibility(
        ESlateVisibility.Collapsed
    )
    self.View.WBP_SkillTipsBg.img_QualityColourlight:SetVisibility(ESlateVisibility.Collapsed)
    self.View.WBP_SkillTipsBg.img_QualityColour:SetVisibility(ESlateVisibility.Collapsed)
    self.DescCell = self:FormComponent("Text", self.View.SB_Content, WidgetEmptyComponent)  --描述文本绑定Component
    self.SkillID = nil
    self.TagList = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.WBP_SkillTipsTitle.Text_Info2)
    self.TagListData = {}
    self:AddUIListener(EUIEventTypes.UrlClicked, self.DescCell.View.Text_TipsContent, self.ShowURITips)
end

function P_SkillInfoPopup:OnRefresh(SkillID, DescText, CDTime, parentwidget)
    self.SkillID = SkillID
    if parentwidget then
        local ViewportSize = WidgetLayoutLibrary.GetViewportSize(_G.GetContextObject())
        local ViewportScale = WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
        local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(_G.GetContextObject(),
            parentwidget:GetCachedGeometry(), FVector2D(0, 0), nil, nil)
        local slot = self.View.WBP_SkillTipsBg.Slot
        local anchor = import("Anchors")()
        anchor.Minimum = FVector2D(0, 0)
        anchor.Maximum = FVector2D(0, 0)
        slot:SetAnchors(anchor)
        slot:SetAlignment(FVector2D(0, 0))

        local x = viewportPosition.X
        local y = viewportPosition.Y

        if y + 600 > ViewportSize.Y / ViewportScale then
            y = ViewportSize.Y / ViewportScale - 700
        end
        if y < 0 then
            y = 0
        end
        x = x + 250
        if x + 600 > ViewportSize.X / ViewportScale then
            x = x - 850
        end
        slot:SetPosition(FVector2D(x, y))
    else
        local slot = self.View.WBP_SkillTipsBg.Slot
        local anchor = import("Anchors")()
        anchor.Minimum = FVector2D(0.5, 0.5)
        anchor.Maximum = FVector2D(0.5, 0.5)
        slot:SetAnchors(anchor)
        slot:SetAlignment(FVector2D(0.5, 0.5))
        slot:SetPosition(FVector2D())
    end
    local PC = GameplayStatics.GetPlayerController(_G.GetContextObject(), 0)
    if PC then
        self.View.WidgetRoot:SetUserFocus(PC)
    end
    local SkillLvl, SkillExtraLevel = Game.SkillCustomSystem:GetSkillLevel(SkillID)
    local SkillInfo = Game.TableData.GetSkillDataNewRow(SkillID)
    table.clear(self.TagListData)
    if SkillInfo then
        self.View.WBP_SkillTipsTitle.Text_Name:SetText(
            SkillInfo.Name
        )
        self:SetImage(self.View.WBP_SkillTipsTitle.Img, SkillInfo.SkillIcon)
        if CDTime == 0 then
            self.View.WBP_SkillTipsTitle.Text_Info2:SetVisibility(ESlateVisibility.Collapsed)
        elseif CDTime % 1 == 0 then
            self.View.WBP_SkillTipsTitle.Text_Info2:SetVisibility(ESlateVisibility.Visible)
            table.insert(self.TagListData, {Time = math.floor(CDTime)})
        else
            self.View.WBP_SkillTipsTitle.Text_Info2:SetVisibility(ESlateVisibility.Visible)
            table.insert(self.TagListData, {Time = CDTime})
        end
		if SkillInfo.DesTags then
			for key, value in ksbcpairs(SkillInfo.DesTags) do
				table.insert(self.TagListData, { Key = value })
			end
		end
        self.DescCell.View.Text_TipsContent:SetText(DescText)

		--[[
		if SkillInfo.Tag then
			self.View.WBP_SkillTipsTitle.Text_ji:SetText(
				SkillInfo.Tag
			)
		end
		]]--
		self.View.WBP_SkillTipsTitle.Text_ji:SetVisibility(ESlateVisibility.Collapsed)

        if SkillExtraLevel == 0 then
            self.View.WBP_SkillTipsTitle.Text_Level:SetText(
                StringConst.Get("SKILLCUSTOMIZER_LEVEL_FORMAT", SkillLvl)
            )
        else
            self.View.WBP_SkillTipsTitle.Text_Level:SetText(
                StringConst.Get("SKILLCUSTOMIZER_LEVEL_FORMAT", SkillLvl) .. "+" .. SkillExtraLevel
            )
        end
    end
    self.TagList:SetData(#self.TagListData)
end

function P_SkillInfoPopup:OnFocusLost()
    --if self:IsShow() then
        --self:CloseSelf()
    --end
end

function P_SkillInfoPopup:OnClose()
    UIBase.OnClose(self)
    self.SkillID = nil
end


function P_SkillInfoPopup:ShowURITips(TipsID)
    Game.TipsSystem:ShowTips(tonumber(TipsID),
        self.DescCell.View.Text_TipsContent:GetCachedGeometry(),
        {
            bregularization = true
        }
    )
end
function P_SkillInfoPopup:OnRefresh_Text_Info2(widget, index)
    local data = self.TagListData[index]
    if data then
        if data.Time then
            -- widget.Img_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            -- widget.RTB_TagName:SetText(string.format(StringConst.Get("FELLOW_SKILL_CD"), data.Time))
            if data.Time == 0 then
                widget.View.WidgetRoot:SetVisibility(ESlateVisibility.Collapsed)
            else
                widget:Event_UI_Style(true)
                widget.RTB_TagName:SetText(data.Time .. StringConst.Get("SECOND"))
                widget.View.WidgetRoot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            end
        elseif data.Key then
            -- widget.Img_Time:SetVisibility(ESlateVisibility.Collapsed)
            widget:Event_UI_Style(false)
            local TagData = Game.TableData.GetSkillTagDataRow(data.Key)
            if TagData then
                widget.RTB_TagName:SetText(TagData.Tag)
            end
        end
    end
end

return P_SkillInfoPopup
