local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class SocialItem : UIListItem
---@field view SocialItemBlueprint
local SocialItem = DefineClass("SocialItem", UIListItem)

SocialItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SocialItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SocialItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function SocialItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SocialItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SocialItem:InitUIView()
end

---面板打开的时候触发
function SocialItem:OnRefresh(text)
    self.view.Text_Tag_lua:SetText(text or "")
end

function SocialItem:SetColor(colorType)
    if colorType then
        self.userWidget:SetColor(colorType)
    end
end

return SocialItem
