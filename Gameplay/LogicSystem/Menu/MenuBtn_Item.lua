local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")


---@class MenuBtn_ItemParam
---@field id number 菜单id

---@class MenuBtn_Item : UIListItem
---@field view MenuBtn_ItemBlueprint
local MenuBtn_Item = DefineClass("MenuBtn_Item", UIListItem)

MenuBtn_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MenuBtn_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MenuBtn_Item:InitUIData()
	---@type number
	self.MenuID = nil
end

--- UI组件初始化，此处为自动生成
function MenuBtn_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function MenuBtn_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MenuBtn_Item:InitUIView()
end

---面板打开的时候触发
---@param params MenuBtn_ItemParam 参数
function MenuBtn_Item:OnRefresh(params)
	self.MenuID = params.id
	if not self.MenuID or self.MenuID <= 0 then return end

	local menuData = Game.TableData.GetMenuDataRow(self.MenuID)
	
    self:SetImage(self.view.Img_Icon, menuData.ButtonIcon)
    self.view.Text_Name:SetText(menuData.ButtonName or "")

    self:UpdateLock()
    self:GetBelongPanel():RegisterRedPointInfoByEnum(menuData.ModuleEnum, self.userWidget)
end

function MenuBtn_Item:UpdateLock()
    if not self.MenuID or self.MenuID <= 0 then return end

	local menuData = Game.TableData.GetMenuDataRow(self.MenuID)
    local isUnlock = Game.ModuleLockSystem:CheckModuleUnlockByEnum(menuData.ModuleEnum, false)
    if menuData.ButtonPositionX ~= 0 then
        self.userWidget:UI_Event_Lock(not isUnlock)
    else
        self:SetVisible(isUnlock)
    end
end


--- 此处为自动生成
function MenuBtn_Item:on_Btn_ClickArea_Clicked()
    if not self.MenuID or self.MenuID <= 0 then return end
    self:GetBelongPanel():ExecuteCmd(self.MenuID)
end

return MenuBtn_Item
