local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

local UIFunctionLibrary = import("UIFunctionLibrary")

---@class Menu_Battery : UIComponent
---@field view Menu_BatteryBlueprint
local Menu_Battery = DefineClass("Menu_Battery", UIComponent)

Menu_Battery.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Menu_Battery:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Menu_Battery:InitUIData()
    ---@type number GM 电池电量
    self.GM_BatteryPercent = nil
    ---@type bool GM 电池充电状态
    self.GM_BatteryCharging = nil
end

--- UI组件初始化，此处为自动生成
function Menu_Battery:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Menu_Battery:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Menu_Battery:InitUIView()
end

---组件刷新统一入口
function Menu_Battery:Refresh(...)
    self:ReCatchData()
end

---@private 重新获取数据
function Menu_Battery:ReCatchData()
	-- 如果 GM 指定了电池电量和充电状态，使用 GM 值
	local percent = self.GM_BatteryPercent or UIFunctionLibrary.GetBatteryLevel()
	local bInCharging = self.GM_BatteryCharging or self:GetIsCharging()
	self.userWidget:Event_UI_Style(percent <= Game.TableData.GetConstDataRow("Low_Batttery_Threshold"), bInCharging, percent/100, not PlatformUtil.IsMobilePlatform())
	local timeDict = os.date("*t", _G._now(1))
	self.view.KText_Time:SetText(
		(timeDict.hour < 10 and "0"..timeDict.hour or timeDict.hour)
			.." : "..
			(timeDict.min < 10 and "0"..timeDict.min or timeDict.min)
	)
end

---@private 获取是否充电
function Menu_Battery:GetIsCharging()
	-- IOS电池枚举文档
	-- https://developer.apple.com/documentation/uikit/uidevice/batterystate-swift.enum?language=objc
	-- Android电池枚举文档
	-- https://developer.android.com/reference/android/os/BatteryManager#BATTERY_STATUS_CHARGING

	local androidStateID = UIFunctionLibrary.GetBatteryStateID()
	if PlatformUtil.IsAndroid() and (androidStateID == 2 or androidStateID == 5) then	-- EBatteryState.BATTERY_STATE_CHARGING or EBatteryState.BATTERY_STATE_FULL
		return true
	end

	local iosStateID = UIFunctionLibrary.GetBatteryStateID()
	if PlatformUtil.IsiOS() and (iosStateID == 2 or iosStateID == 3) then	-- UIDeviceBatteryState.Charging or UIDeviceBatteryState.Full
		return true
	end
	return false
end

return Menu_Battery
