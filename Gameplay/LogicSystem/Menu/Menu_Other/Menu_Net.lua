local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = require "Data.Config.StringConst.StringConst"

local MobilePatchingLibrary = import("MobilePatchingLibrary")

---@class Menu_Net : UIComponent
---@field view Menu_NetBlueprint
local Menu_Net = DefineClass("Menu_Net", UIComponent)

Menu_Net.eventBindMap = {
}

-- 信号延迟分级
Menu_Net.StateValue = 
{
	[1] = { maxValue = Game.TableData.GetConstDataRow("Signal_Threshold_Fir"), UIState = 3 },
	[2] = { maxValue = Game.TableData.GetConstDataRow("Signal_Threshold_Sec"), UIState = 2 },
	[3] = { maxValue = Game.TableData.GetConstDataRow("Signal_Threshold_Last"), UIState = 1 },
	[4] = { maxValue = 1000, UIState = 0 },
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Menu_Net:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Menu_Net:InitUIData()
    ---@type number GM 网络状态
    self.GM_NetState = nil
    ---@type number GM 网络延迟
    self.GM_NetDelay = nil
end

--- UI组件初始化，此处为自动生成
function Menu_Net:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Menu_Net:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Menu_Net:InitUIView()
    self:Refresh(Game.me.__averageDelay)
end

---组件刷新统一入口
function Menu_Net:Refresh(delayMs)
    local bHasWifi
	if PlatformUtil.IsMobilePlatform() then
		bHasWifi = MobilePatchingLibrary.HasActiveWiFiConnection()
	else
		bHasWifi = true
	end
	-- 如果 GM 指定了网络状态和延迟，则使用 GM 值，否则使用传入值
	local state = self.GM_NetState or self:FindState(delayMs)
	local netDelay = self.GM_NetDelay or delayMs
	self.userWidget:Event_UI_Style(state, bHasWifi)
	self.view.KText_ms:SetText(state == 0 and StringConst.Get("LOSTSIGNALSTRING") or math.floor(netDelay) .. "ms")
end


function Menu_Net:FindState(ms)
	for key,value in ipairs(Menu_Net.StateValue) do
		if ms <= value.maxValue then
			return value.UIState
		end
	end
	return 0
end

return Menu_Net
