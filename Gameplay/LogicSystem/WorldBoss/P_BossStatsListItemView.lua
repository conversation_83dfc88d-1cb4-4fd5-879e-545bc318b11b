---@class WBP_BossStatsListHeadView : WBP_BossStatsListHead_C
---@field public WidgetRoot WBP_BossStatsListHead_C
---@field public Img_Head C7Image
---@field public Img_Icon_Dead C7Image
---@field public Text_HP C7TextBlock
---@field public ProgressBar_HP ProgressBar
---@field public RTB_Refrsh C7RichTextBlock
---@field public BossType number
---@field public Event_UI_Style fun(self:self,BossType:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public Text_Com C7TextBlock
---@field public Text_Time TextBlock
---@field public Image C7Image
---@field public Btn_Com C7Button
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_BossStatsListItemView : WBP_BossStatsListItem_C
---@field public WidgetRoot WBP_BossStatsListItem_C
---@field public WBP_BossStatsListHead WBP_BossStatsListHeadView
---@field public Img_Icon_Network C7Image
---@field public Text_Name_Line C7TextBlock
---@field public WBP_ComBtn WBP_ComBtnView
---@field public IsEvenRow boolean
---@field public LineState number
---@field public Event_UI_Style fun(self:self,IsEvenRow:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetLineState fun(self:self,LineState:number):void

---@class P_BossStatsListItemView : WBP_BossStatsListItemView
---@field public controller P_BossStatsListItem
local P_BossStatsListItemView = DefineClass("P_BossStatsListItemView", UIView)

function P_BossStatsListItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

end

return P_BossStatsListItemView
