local Const = kg_require"Shared.Const.WorldBossConst"
local worldConst = kg_require "Shared.Const.WorldConst"
---@class WorldBossModel:SystemModelBase
local WorldBossModel = DefineClass("WorldBossModel",SystemModelBase)

function WorldBossModel:init()
    self.isInWorldBoss = false
    self.inWorldBossActivityID = nil
    self.inWorldBossID = nil
    self.curActivityTimestamp = 0
    self.mechanismCoolDownData = nil
    self.lineList = nil
end

function WorldBossModel:clear()
    self.isInWorldBoss = nil
    self.inWorldBossActivityID = nil
    self.inWorldBossID = nil
    self.curActivityTimestamp = nil
    self.mechanismCoolDownData = nil
    self.lineList = nil
end

function WorldBossModel:unInit()

end

--排行榜数据
function WorldBossModel:UpdateRankInfo(rankInfoArray)
    self.rankDataOther = rankInfoArray
end

function WorldBossModel:UpdateRankInfoSelf(rankData)
    self.rankDataSelf = rankData
end

--更新boss分线数据
function WorldBossModel:UpdateBossLineData(bossID, bossLineData)
    if not self.allBossLineData  then
        self.allBossLineData = {}
    end

    if not self.allBossLineData[bossID] then
        self.allBossLineData[bossID] = {}
    end

    self:updateSelectBossLineData(self.allBossLineData[bossID], bossLineData)
end

--获取boss分线数据
function WorldBossModel:GetBossLineDataByBossKey(wroldBossKey)
    return self.allBossLineData and self.allBossLineData[wroldBossKey]
end

--清空boss分线数据
function WorldBossModel:ClearBossLineData()
    --清空某个活动的世界boss数据
end

function WorldBossModel:updateSelectBossLineData(showBossLineData, newBossLineData)
    if not showBossLineData or not newBossLineData then
        return
    end

    for lineID, data in pairs(newBossLineData) do
        local find = nil
        for _, value in ipairs(showBossLineData) do
            if lineID == value.lineID then
                find = true
                value.bossData.status = data.status
                value.bossData.hpRatio = data.hpRatio
                value.bossData.avatarRatio = data.avatarRatio
                value.bossData.LineType = data.LineType
                break
            end
        end

        if not find then
            table.insert(showBossLineData, {
                lineID = lineID,
                bossData = data
            })
        end
    end
end

function WorldBossModel:newDefaultBossLineData(lineData)
    return {
        lineID = lineData.WorldID % worldConst.MAX_LINE_NUM,
        bossData = {
            status =  Const.WORLD_BOSS_STATUS.NOTREFRESH,
            hpRatio = 1.0,
            avatarRatio = 0,
        }
    }
end

return WorldBossModel