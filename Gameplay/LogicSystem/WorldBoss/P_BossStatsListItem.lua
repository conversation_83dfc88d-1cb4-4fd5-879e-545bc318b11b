local BossHead = kg_require"Gameplay.LogicSystem.WorldBoss.P_BossStatsListHead"
local worldConst = kg_require "Shared.Const.WorldConst"
local StringConst = require "Data.Config.StringConst.StringConst"
---@class P_BossStatsListItem : UIComponent
---@field public View WBP_BossStatsListItemView
local P_BossStatsListItem = DefineClass("P_BossStatsListItem", UIComponent)

function P_BossStatsListItem:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtn.Btn_Com, self.onClickGoButton)
    --boss头部图标
    self.worldBossHead = self:BindComponent(self.View.WBP_BossStatsListHead, BossHead)
end

function P_BossStatsListItem:OnRefresh()
    self.View.WBP_ComBtn.Text_Com:SetText(StringConst.Get("MAP_ACTIVITY_VISIT"))
end

function P_BossStatsListItem:UpdateUI(activityID, curSelectWorldBossKey, data, curActivityTimestamp)
    self.activityID = activityID
    self.curSelectWorldBossKey = curSelectWorldBossKey
    self.bossLineData = data

    self.worldBossHead:UpdateUI(curSelectWorldBossKey, data, curActivityTimestamp)
    local percentList = Game.TableData.GetWorldBossSettingDataRow("WORLD_BOSS_LINE_PERCENTAGE")
    local block_num = #percentList-1
    local index = block_num
    for i = 1, block_num do
        local bigPercent = percentList[i]
        local smallPercent = percentList[i+1] or 0
        if data.bossData.avatarRatio<=bigPercent and data.bossData.avatarRatio>=smallPercent then
            index = block_num - i
            break
        end
    end
    local bossData = Game.TableData.GetWorldBossDataRow(curSelectWorldBossKey)
    local space = Game.NetworkManager.GetLocalSpace()
    if Game.WorldBossSystem:IsInWorldBoss() and space.mapID == bossData.WorldID and data.lineID == space.WorldID % worldConst.MAX_LINE_NUM then
        self.View.WBP_ComBtn:SetIsEnabled(false)
    else
        self.View.WBP_ComBtn:SetIsEnabled(true)
    end
    self.View:SetLineState(index)
    local LevelMapData = Game.TableData.GetLevelMapDataRow(bossData.WorldID)
    if LevelMapData then
        self.View.Text_World:SetText(LevelMapData.Name)
        self.View.Text_Content:SetText(Game.HUDSystem.GetLineContentText(data.bossData.LineType, data.lineID % worldConst.MAX_LINE_NUM))
    end
end

function P_BossStatsListItem:onClickGoButton()
    if not self.activityID or not self.curSelectWorldBossKey or not self.bossLineData then
        Log.WarningFormat("P_BossStatsListItem data error! self.activityID=%s, self.curSelectWorldBossKey=%s, self.bossLineData=%s", 
            self.activityID, self.curSelectWorldBossKey, self.bossLineData)
        return
    end
    Game.WorldBossSystem:TrySwitchLine(function ()
        UI.HideUI("P_BossStatsList")
        Game.WorldBossSystem:ReqTeleportToBossRegion(self.activityID, self.curSelectWorldBossKey, self.bossLineData.lineID)
    end)
end


return P_BossStatsListItem
