---@class WBP_FirstBigTextView : WBP_FirstBigText_C
---@field public WidgetRoot WBP_FirstBigText_C
---@field public Text_First C7TextBlock
---@field public Text_Affix C7TextBlock
---@field public TextColor SlateColor
---@field public Text string
---@field public BigSize number
---@field public NmlSize number
---@field public BigStyle UMGTextStyleAsset
---@field public NmlStyle UMGTextStyleAsset
---@field public FitPadding Margin
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetText fun(self:self,text:string):void


---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button C7Button
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public IconBrush SlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):SlateBrush


---@class WBP_ComTitleView : WBP_ComTitle_C
---@field public WidgetRoot WBP_ComTitle_C
---@field public Text_Title WBP_FirstBigTextView
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView
---@field public Ani_In WidgetAnimation
---@field public TitleText string
---@field public OnClicked MulticastDelegate
---@field public MediaPlayer BinkMediaPlayer
---@field public BndEvt__WBP_ComTitle_WBP_ComBtnClose_lua_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature fun(self:self):void
---@field public SequenceEvent fun(self:self):void
---@field public Construct fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetText fun(self:self,InText:string):void
---@field public InnerSetText fun(self:self):void
---@field public Play Son fun(self:self):void


---@class WBP_ComPopupMView : WBP_ComPopupM_C
---@field public WidgetRoot WBP_ComPopupM_C
---@field public WBP_ComPopupTitle WBP_ComTitleView
---@field public Ani_In WidgetAnimation
---@field public Ani_Out WidgetAnimation
---@field public Title string
---@field public onclosed MulticastDelegate
---@field public Duration number
---@field public BG_IN fun(self:self):void
---@field public Title_In fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Construct fun(self:self):void
---@field public onclosed_Event fun(self:self):void


---@class WBP_ComListView : WBP_ComList_C
---@field public WidgetRoot WBP_ComList_C
---@field public List ScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public bIsTileView boolean
---@field public PreviewCount number
---@field public LibWidget ListLib
---@field public ScrollWidget Widget
---@field public Orientation EOrientation
---@field public ScrollBarVisibility ESlateVisibility
---@field public SelectionMode number
---@field public Space ListSpace
---@field public Alignment ComListAligment
---@field public bIsCenterContent boolean
---@field public tempIndex number
---@field public oldPosX number
---@field public oldPosY number
---@field public tempPosX number
---@field public tempPosY number
---@field public widgetX number
---@field public widgetY number
---@field public spaceUp number
---@field public spaceBottom number
---@field public spaceLeft number
---@field public spaceRight number
---@field public bSizeToContent boolean
---@field public ListPadding Margin
---@field public MaxValue number
---@field public RetainerBox RetainerBox
---@field public OnSetItem MulticastDelegate
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public CalculatePos fun(self:self):void
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number):void
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public GetListSize fun(self:self):number,number,number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number,number,number,number,number
---@field public VerticalTileChange fun(self:self):void
---@field public VerticalTile fun(self:self):void
---@field public VerticalList fun(self:self):void
---@field public HorizontalTileChange fun(self:self):void
---@field public HorizontalTile fun(self:self):void
---@field public HorizontalList fun(self:self):void
---@field public VerticalTileAuto fun(self:self):void
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidfget:Widget):void


---@class WBP_ComTabListFoldView : WBP_ComTabListFold_C
---@field public WidgetRoot WBP_ComTabListFold_C
---@field public WBP_ComList WBP_ComListView
---@field public Max number
---@field public Min number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_BossStatsListHeadView : WBP_BossStatsListHead_C
---@field public WidgetRoot WBP_BossStatsListHead_C
---@field public BossType number
---@field public Event_UI_Style fun(self:self,BossType:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public Text_Com C7TextBlock
---@field public Text_Time TextBlock
---@field public Image C7Image
---@field public Btn_Com C7Button
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_BossStatsListItemView : WBP_BossStatsListItem_C
---@field public WidgetRoot WBP_BossStatsListItem_C
---@field public WBP_BossStatsListHead WBP_BossStatsListHeadView
---@field public WBP_ComBtn WBP_ComBtnView
---@field public IsEvenRow boolean
---@field public Event_UI_Style fun(self:self,IsEvenRow:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_BossStatsListView : WBP_BossStatsList_C
---@field public WidgetRoot WBP_BossStatsList_C
---@field public WBP_ComPopupM WBP_ComPopupMView
---@field public WBP_ComTabListFold WBP_ComTabListFoldView
---@field public WBP_BossStatsListItem WBP_BossStatsListItemView
---@field public WBP_ComList WBP_ComListView

---@class P_BossStatsListView : WBP_BossStatsListView
---@field public controller P_BossStatsList
local P_BossStatsListView = DefineClass("P_BossStatsListView", UIView)

function P_BossStatsListView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

end

return P_BossStatsListView
