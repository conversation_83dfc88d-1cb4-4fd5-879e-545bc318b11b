local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@class Group_Panel_MemberItem : UIComponent
---@field public View WBP_M3TeamGroupItemView
local Group_Panel_MemberItem = DefineClass("Group_Panel_MemberItem", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local UIDrag = kg_require("Framework.KGFramework.KGUI.Component.CommonLogic.UIComDragWidget")

Group_Panel_MemberItem.eventBindMap = { 
    [_G.EEventTypes.GROUP_LEADER_CHANGED] = "onGroupLeaderChange",
    [_G.EEventTypes.CLIENT_GROUP_TEAMLEADER_UPDATE] = "OnGroupTeamLeaderChange",
    [_G.EEventTypes.CLIENT_GROUP_MEMBER_PROP_UPDATE] = "OnGroupPropChanged",
    [_G.EEventTypes.ON_GROUP_END_ONCE_EXCHANGE] = "OnEndExchange",
    [_G.EEventTypes.CLIENT_GROUP_MEMBER_VOICE_UPDATE] = "OnGroupVoiceChange"
}

function Group_Panel_MemberItem:OnCreate()
    self.teamUUID = nil
    self.bIsDrag = nil
    self.ShowData = nil
    self.GroupIndex = nil
    self.TeamIndex = nil
    self.MemberIndex = nil
    self.id = nil
    self.prePareState = 0
    self.bShowVoice = false
    self.isEmpty = true
    self.bInAoi = true
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "OnClick_Btn")

    self.userWidget:SetVisibility(ESlateVisibility.Visible)

    
    ---@type UIComDragWidget
    self.uiDragCom = self:CreateComponent(self.view.Btn_DragArea, UIDrag)
    
    self.uiDragCom:SetDragSource(self.userWidget, false)

    self:AddUIEvent(self.uiDragCom.onClickEvent, "OnClickDrag")
    
    self:AddUIEvent(self.uiDragCom.onDragDetectedEvent, "OnDragDetected")
    self:AddUIEvent(self.uiDragCom.onDragCancelEvent, "OnDragCancel")
    self:AddUIEvent(self.uiDragCom.onDragLeaveEvent, "OnDragLeave")
    self:AddUIEvent(self.uiDragCom.onDragEnterEvent, "OnDragEnter")
    self:AddUIEvent(self.uiDragCom.onDragDropEvent, "OnDrop")
end

function Group_Panel_MemberItem:Refresh(GroupIndex, TeamIndex, MemberIndex)
    self.bIsDrag = false
    self.ShowData = Game.GroupSystem:GetMemberInfo(GroupIndex, TeamIndex, MemberIndex)
    self.GroupIndex = GroupIndex
    self.TeamIndex = TeamIndex
    self.MemberIndex = MemberIndex
    self.id = ""
    self.teamUUID = Game.GroupSystem:GetTeamUUID(self.GroupIndex, self.TeamIndex)
    --团长、管理员、成员背景
    self.prePareState = 0
    self.voiceState = 0
    self.isEmpty = true
    self.SpecialCareer = 2
    if self.ShowData then
        self.userWidget:SetEmptyState(false)
        self.isEmpty = false
        self.id = self.ShowData.uid
        self.teamUUID = self.ShowData.teamUUID
        self.captainState = 0
        if Game.GroupSystem:IsGroupLeader(self.id) then
            self.captainState = 2
        elseif Game.GroupSystem:IsTeamLeader(self.id) then
            self.captainState = 1
        end
        --姓名
        self.userWidget:SetName(self.id == Game.me.eid)
        if self.ShowData.botID ~= nil and self.ShowData.botID ~= 0 then
            local botTableData = Game.TableData.GetBattleBotTemplateDataRow(self.ShowData.botID)
            self.view.Text_Name_lua:SetText(botTableData.Name)
        else
            self.view.Text_Name_lua:SetText(self.ShowData.name)
        end
        --等级
        self.view.Text_Lv_lua:SetText(math.floor(self.ShowData.lv))
        --职业
        local OptionClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(self.ShowData.profession)[0]
        self:SetImage(self.view.Img_CareerIcon_lua, OptionClassInfo.HeadIcon)

        local professionStateID = Game.GroupSystem.EHUDProMap[self.ShowData.profession]
        if professionStateID == nil then
            Log.ErrorFormat("HUD_GroupMemberItem:Refresh 找不到职业状态 %s", self.Data.profession)
            professionStateID = 0
        end
        self.userWidget:SetCareer(professionStateID, false)
        --队长 团长
        if Game.GroupSystem:IsGroupLeader(self.ShowData.uid) then
            self.userWidget:SetRole(2)
            local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.GROUP_LEADER_FLAG)
            self:SetImage(self.view.Img_TeamLeader_lua, iconData.AssetPath)
        elseif Game.GroupSystem:IsTeamLeader(self.ShowData.uid) then
            self.userWidget:SetRole(1)
            local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_LEADER_FLAG)
            self:SetImage(self.view.Img_TeamLeader_lua, iconData.AssetPath)
        else
            self.userWidget:SetRole(0)
        end
        --Aoi
        self.bInAoi = true
        if self.id ~= GetMainPlayerEID() then
            --self.bInAoi = Game.EntityManager:getEntityWithBrief(self.ShowData.uid) ~= nil
        end
        --语音
        local iconData = ""
        self.bShowVoice = false
        if Game.me.groupBlockVoices[self.ShowData.uid] == true then
            self.bShowVoice = true
            iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_BLOCK_MIC)
            self:SetImage(self.view.Img_VoiceMicrophone_lua, iconData.AssetPath)
        else
            if self.ShowData.voiceState == Enum.EVOICE_STATE.LISTEN then
                self.bShowVoice = true
                iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_LISTEN)
                self:SetImage(self.view.Img_VoiceMicrophone_lua, iconData.AssetPath)
            elseif self.ShowData.voiceState == Enum.EVOICE_STATE.VOICE then
                self.bShowVoice = true
                iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_VOICE)
                self:SetImage(self.view.Img_VoiceMicrophone_lua, iconData.AssetPath)
            elseif self.ShowData.voiceState == Enum.EVOICE_STATE.REFUSE then
                self.bShowVoice = true
                iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_NOT_LISTEN)
                self:SetImage(self.view.Img_VoiceMicrophone_lua, iconData.AssetPath)
            end
        end
        self.userWidget:SetVoiceState(self.bShowVoice)
        self.userWidget:SetLight(false, false)
        
        local mapID, _ = Game.TeamSystem:UnpackWorldID(self.ShowData.worldID)
        local LevelTableData = Game.TableData.GetLevelMapDataRow(mapID)
        if LevelTableData then
            self.view.Text_Map_lua:SetText(LevelTableData.Name)
        else
            self.view.Text_Map_lua:SetText("")
        end
        
        self:RefreshConfirmUI(Game.GroupSystem:GetConfirmValuebyEID(self.id))
    else
        self.userWidget:SetEmptyState(true)
        self.userWidget:SetLight(false, false)
    end
    self:OnEditModuleChange(Game.GroupSystem:GetbEditModule())
end

function Group_Panel_MemberItem:RefreshConfirmUI(prePareState)
    --就位
    if self.ShowData then
        self.prePareState = prePareState
        self.userWidget:SetMaskState(self.ShowData.isOnline == 1, not self.bInAoi, self.prePareState, self.ShowData.bFollow)
        if self.prePareState == 1 then
            local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_CONFIRM_YES)
            self:SetImage(self.view.Img_PrerareState_lua, iconData.AssetPath)
        elseif self.prePareState == 2 then
            local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_CONFIRM_NO)
            self:SetImage(self.view.Img_PrerareState_lua, iconData.AssetPath)
        end
    end
end

--- 这个界面aoi不用了
function Group_Panel_MemberItem:EnterAoi()
    self.bInAoi = true
    self.userWidget:SetMaskState(self.ShowData.isOnline == 1, not self.bInAoi, self.prePareState, self.ShowData.bFollow)
end

function Group_Panel_MemberItem:LeaveAoi()
    self.bInAoi = false
    self.userWidget:SetMaskState(self.ShowData.isOnline == 1, not self.bInAoi, self.prePareState, self.ShowData.bFollow)
end

function Group_Panel_MemberItem:OnClickDrag()
    if Game.GroupSystem:GetbEditModule() then
        self.bIsDrag = false
        if next(Game.GroupSystem:GetDragInfo()) == nil then
            self:SetDragInfo()
        else
            self.userWidget:SetLight(false, true)
            self:ExchangeProcess()
        end
    end
end

function Group_Panel_MemberItem:SetDragInfo()
    if self.ShowData then
        self.userWidget:SetLight(true, false)
        Game.GroupSystem:SetDragInfo(
                self.id,
                self.teamUUID,
                self.GroupIndex,
                self.TeamIndex,
                self.MemberIndex)
        Game.GroupSystem:SetGroupDragType(Game.GroupSystem.EDragType.EMember)
    end
end


function Group_Panel_MemberItem:OnDragDetected(data)
    Log.InfoFormat("OnDragDetected %s %s", self.TeamIndex, self.MemberIndex)
    self:SetDragInfo()
    
    local drag = self.uiDragCom:GetDragWidget()
    ---@type Group_Panel_MemberItem
    local tempItem = self:CreateComponent(drag, Group_Panel_MemberItem)
    tempItem:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)

    -- 假设 widget 是要获取宽高的 UMG 控件
    local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
    local geometry = self.userWidget:GetCachedGeometry()
    local size = SlateBlueprintLibrary.GetLocalSize(geometry)
    
    drag.SizeBox_lua:SetWidthOverride(size.X)
    drag.SizeBox_lua:SetHeightOverride(size.Y)
    
    self.bIsDrag = true
    UI.Invoke(UIPanelConfig.Group_Panel, "InDrag", true)
end

function Group_Panel_MemberItem:OnDragEnter()
    Log.InfoFormat("OnDragEnter %s %s", self.TeamIndex, self.MemberIndex)

    if Game.GroupSystem:GetGroupDragType() == Game.GroupSystem.EDragType.EMember and Game.GroupSystem:GetDragInfo().uid ~= self.id then
        self.userWidget:SetLight(false, true)
    end
end

function Group_Panel_MemberItem:OnDragLeave()
    Log.InfoFormat("OnDragLeave %s %s", self.TeamIndex, self.MemberIndex)

    if Game.GroupSystem:GetGroupDragType() == Game.GroupSystem.EDragType.EMember and Game.GroupSystem:GetDragInfo().uid ~= self.id then
        self.userWidget:SetLight(false, false)
    end
end

function Group_Panel_MemberItem:OnDragCancel()
    self.userWidget:SetLight(false, false)
end

function Group_Panel_MemberItem:OnDrop()
    --离开拖拽
    UI.Invoke(UIPanelConfig.Group_Panel, "InDrag", false)
    self:ExchangeProcess()
    return UIBase.HANDLED
end

function Group_Panel_MemberItem:ExchangeProcess()
    if Game.GroupSystem:GetGroupDragType() == Game.GroupSystem.EDragType.EMember then
        if (Game.GroupSystem:GetDragInfo().groupIndex ~= Game.GroupSystem:GetMyGroupIndex() and Game.GroupSystem:IsGroupLeader(Game.GroupSystem:GetDragInfo().uid))
            or (Game.GroupSystem:IsGroupLeader(self.id) and self.groupIndex ~= Game.GroupSystem:GetMyGroupIndex()) then
            --不可交换团长
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.LEAGUE_CAN_NOT_CHANGE_LEADER)
            Game.GroupSystem:ClearDragInfo()
        elseif self.id ~= "" and Game.GroupSystem:GetDragInfo().uid == Game.me.eid and Game.GroupSystem:IsNormalTeamMember(self.id) then
            --转让团长给队员
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GROUP_LEADER_CHANGE_CONFIRM, function()   
                Game.me:ChangeGroupMemberSlot(Game.GroupSystem:GetDragInfo(),
                {
                    uid = self.id,
                    teamUUID = self.teamUUID,
                    groupIndex = self.GroupIndex,
                    teamIndex = self.TeamIndex,
                    memberIndex = self.MemberIndex
                })  
                Game.GroupSystem:ClearDragInfo()
            end,
            function ()
                Game.GroupSystem:ClearDragInfo()
            end, 
            {self.ShowData.name})  
        else
            Game.me:ChangeGroupMemberSlot(Game.GroupSystem:GetDragInfo(),
            {
                uid = self.id,
                teamUUID = self.teamUUID,
                groupIndex = self.GroupIndex,
                teamIndex = self.TeamIndex,
                memberIndex = self.MemberIndex
            })  
            Game.GroupSystem:ClearDragInfo()
        end  
    end
end

function Group_Panel_MemberItem:OnEndExchange()
    self.userWidget:SetLight(false, false)
end

function Group_Panel_MemberItem:OnClick_Btn()
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common, true)
    if self.ShowData then
        if self.id ~= GetMainPlayerEID() then
            Game.TeamSystem:PlayerCardUIDataAsync(self.id, false, Enum.EFriendAddSourceData.TEAM_GROUP, nil, Enum.EMenuType.TeamDisplay)
        end  
    else
        Game.TabClose:AttachPanel(UIPanelConfig.TeamInvite_Panel, Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, self.userWidget)
        Game.NewUIManager:OpenPanel(UIPanelConfig.TeamInvite_Panel)
    end
end

function Group_Panel_MemberItem:OnEditModuleChange(bEnter)
    self.view.Btn_ClickArea_lua:SetIsEnabled(not bEnter)

    if bEnter then
        self.view.Btn_DragArea:SetVisibility(ESlateVisibility.Visible)
    else
        self.view.Btn_DragArea:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function Group_Panel_MemberItem:onGroupLeaderChange(OldLeaderID, NewLeaderID)
    if self.ShowData and OldLeaderID == self.ShowData.uid then
        self:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)
    end
    if self.ShowData and NewLeaderID == self.ShowData.uid then
        self:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)
    end
end


function Group_Panel_MemberItem:OnGroupTeamLeaderChange(OldLeaderID, NewLeaderID)
    if self.ShowData and OldLeaderID == self.ShowData.uid then
        self:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)
    end
    if self.ShowData and NewLeaderID == self.ShowData.uid then
        self:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)
    end
end

function Group_Panel_MemberItem:OnGroupPropChanged(EID, Key, Value)
    if self.ShowData and EID == self.ShowData.uid and Key ~= "hp" then
        if Key == "bFollow" then
            self.ShowData.bFollow = Value
            self.userWidget:SetMaskState(self.ShowData.isOnline == 1, not self.bInAoi, self.prePareState, self.ShowData.bFollow)
        else
            self:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)
        end
    end
end

function Group_Panel_MemberItem:OnBlockVoiceChange(NewValue)
    if self.ShowData then
        self:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)
    end
end

function Group_Panel_MemberItem:OnGroupVoiceChange(EID, VoiceState)
    if self.ShowData and EID == self.ShowData.uid then
        self:Refresh(self.GroupIndex, self.TeamIndex, self.MemberIndex)
    end
end

return Group_Panel_MemberItem
