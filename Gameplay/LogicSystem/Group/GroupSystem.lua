---@class GroupSystem
local GroupSystem = DefineSingletonClass("GroupSystem", SystemBase)
local StringConst = require "Data.Config.StringConst.StringConst"
local team_utils = kg_require("Shared.Utils.TeamUtils")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local lang = kg_require("Shared.language_" .. language)
local VIEW_BUDGET_CHARACTER_TYPE = WorldViewBudgetConst.VIEW_BUDGET_CHARACTER_TYPE

-- 第一个位置一定是队长, 判定队长通过位置即可
local GROUP_TEAM_LEADER_INDEX = 1        -- luacheck: ignore

GroupSystem.EGroupData = {
    StringConst.Get("GROUP_FIRST") .. StringConst.Get("GROUP_GROUP"),
    StringConst.Get("GROUP_SECOND") .. StringConst.Get("GROUP_GROUP"),
    StringConst.Get("GROUP_THIRD") .. StringConst.Get("GROUP_GROUP"),
    StringConst.Get("GROUP_FOURTH") .. StringConst.Get("GROUP_GROUP"),
    StringConst.Get("GROUP_FIFTH") .. StringConst.Get("GROUP_GROUP"),
}

GroupSystem.ETeamData = {
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
}

GroupSystem.EGroupIDData = {
    StringConst.Get("GROUP_FIRST"),
    StringConst.Get("GROUP_SECOND"),
    StringConst.Get("GROUP_THIRD"),
    StringConst.Get("GROUP_FOURTH"),
    StringConst.Get("GROUP_FIFTH"),

}

GroupSystem.EDragType = {
    ["ETeam"] = 0,
    ["EMember"] = 1
}
--- 后续去掉
GroupSystem.EHUDProMap = {
    [1200001] = 0, --歌颂者
    [1200002] = 1, --观众
    [1200003] = 2, --占卜家
    [1200004] = 5, --仲裁人
    [1200005] = 4, --学徒
    [1200006] = 3, --战士
    [1200007] = 0, 
    [1200008] = 0, 
    [1200009] = 0, 
    [1200010] = 0,
    [1200011] = 0,
    [1200012] = 0,
    [1200013] = 0,
}

function GroupSystem:Init()
    self.model = kg_require "Gameplay.LogicSystem.Group.GroupSystemModel"
    self.model:init()
    ---------------------------------主角属性&&团员 start-------------------------
    
    ---------------------------------主角属性&&团员 end-------------------------
end

function GroupSystem:Uninit()

end

function GroupSystem:OnBackToLogin()
    self.model:clear()
end

function GroupSystem:OnBackToSelectRole()
    self.model:clear()
end

function GroupSystem:GetSceneMarkList()
    return self.model.SceneMarkList
end

function GroupSystem:ClearSceneMarkList()
    self.model:ClearSceneMarkData()
end

function GroupSystem:OnSceneMarkPartChanged(Index, SpaceFlagInfo)
    ---增量修改
    self.model:UpdateSceneMarkData(Index, SpaceFlagInfo)
    if self:IsGroupLeader(Game.me.eid) and SpaceFlagInfo and SpaceFlagInfo.spaceId ~= "" and SpaceFlagInfo.pos then
        local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
        local PosText = ChatUtils.GetLoactionMsg(FVector(SpaceFlagInfo.pos.X, SpaceFlagInfo.pos.Y, SpaceFlagInfo.pos.Z),
        Game.LevelManager.GetCurrentLevelID(),Game.MapSystem:GetCurrentPlaneID(),Game.HUDSystem.GetCurrentLineWorldID() % 100000)
        local Text = string.format(StringConst.Get("TEAM_SCENE_MARK_INFO"), Game.me.Name, Index, PosText)
        Game.ChatSystem:AddChannelSystemInfoByServer(Text, Enum.EChatChannelData.GROUP)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_SCENE_MARK_CAHNGED)
end

function GroupSystem:OnGroupMemberMarkChange(EID, MemberFlag)
    local Index = math.floor(MemberFlag)
    self.model:UpdateGroupMemberMarkData(EID, Index)
    if Index and Index ~= 0 and self:IsGroupLeader(Game.me.eid) then
        local name = self:GetGroupMemberDetail(EID).name
        local Text = string.format(StringConst.Get("TEAM_MEMBER_MARK_INFO"), Game.me.Name, name)..string.format("<img id=\"%s\"/>", "MemberMark"..Index)
        Game.ChatSystem:AddChannelSystemInfoByServer(Text, Enum.EChatChannelData.GROUP)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_MEMBER_MARK_CAHNGED, EID)
end

function GroupSystem:IsGroupMemberInMark(EID)
    local Res = false
    local MemberMarkList = self:GetGroupMemberMarkList()
    if MemberMarkList then
        for key, value in pairs(MemberMarkList) do
            if key == EID and value and value ~= 0 then
                Res = true
                break
            end
        end
    end
    return Res
end

function GroupSystem:GetGroupMemberInMarkID(EID)
    local MarkID = -1
    local MemberMarkList = self:GetGroupMemberMarkList()
    if MemberMarkList then
        for key, value in pairs(MemberMarkList) do
            if key == EID then
                MarkID = value
                break
            end
        end
    end
    return MarkID
end

function GroupSystem:GetGroupMemberMarkList()
    return self.model.MemberMarkList
end

function GroupSystem:ClearGroupMemberMarkList()
    self.model:ClearGroupMemberMarkData()
    if UI.IsShow(UICellConfig.HUD_TeamMemberMarkMenu) then
        UI.HideUI(UICellConfig.HUD_TeamMemberMarkMenu)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_MEMBER_MARK_CAHNGED)
end

function GroupSystem:CanSeekRescueChange(CanSeekRescue)
    self.model:SetCanSeekRescue(CanSeekRescue)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_CAN_RESCUE_CHANGED)
end

function GroupSystem:GetCanRescueState()
    return self.model.CanSeekRescue
end

function GroupSystem:OnSelfPropChanged(PropName, NewVal, OldVal)
    if self.model.GroupID == 0 then
        return
    end
    self.model:UpdateSelfPropData(PropName, NewVal, OldVal)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_MEMBER_PROP_UPDATE, Game.me.eid, PropName, NewVal)
end

function GroupSystem:AutoAgreeStateChange(bAutoAgree)
    self.model:SetAutoAgree(bAutoAgree)
    UI.Invoke("P_Group", "UpdateAutoAgreeState", bAutoAgree)
end

function GroupSystem:OnRoleBorn()
    self:SetGroupMemberCharacterTypeForViewBudget(self:GetMyGroupMemberIDList(), true)
end

function GroupSystem:RoleAfterEnterWorld(eid)
    UI.Invoke(UICellConfig.HUD_Group, "EnterAoi", eid)
end

function GroupSystem:RoleExitWorld(eid)
    UI.Invoke(UICellConfig.HUD_Group, "LeaveAoi", eid)
end


function GroupSystem:GroupIDChangeTo0(eid, propName, newValue)
    --清除状态
    self:SetGroupMemberCharacterTypeForViewBudget(self:GetMyGroupMemberIDList(), false)
    self.model:clear()
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_DISBAND)
    Game.ChatSystem:OnTeamStateChange()
    Game.DungeonBattleStatisticsSystem:OnTeamStateChange()
    Game.VoiceSystem:OnTeamStateChange()
end

function GroupSystem:VoiceStateChange(EID, VoiceState)
    self.model:UpdateVoiceState(EID, VoiceState)
    Game.VoiceSystem:UpdateGroupVoiceState(EID, VoiceState,Enum.EVOICE_CHANNEL.WORLD)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_MEMBER_VOICE_UPDATE,EID, VoiceState)
end

function GroupSystem:UpdateBlockVoices(eid, value)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_BLOCK_VOICE_CHANGED, eid, value)
end

function GroupSystem:IsInBlockVoice(EID)
    local GroupBlockVoices = GetMainPlayerPropertySafely("groupBlockVoices")
    for key, value in pairs(GroupBlockVoices) do
        if key == EID then
            return true
        end
    end
    return false
end

function GroupSystem:ReqBuildGroup()
    Game.me:ReqBuildGroup()
end

function GroupSystem:OnQuitGroup(bDisbandGroup, bKicked)
    if bKicked then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_BE_KICKED_OFF_GROUP)
    end
end

function GroupSystem:SetbEditModule(value)
    if self.model.bEditModule ~= value then
        self.model.bEditModule = value
        if value == false then
            self:ClearDragInfo()
        end
    end
end

function GroupSystem:GetbEditModule()
    return self.model.bEditModule
end

function GroupSystem:UpdateConfirm(EID, ConfirmType)
    self.model:UpdateConfirm(EID, ConfirmType)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_CONFIRM_UPDATE, EID, ConfirmType)
end

function GroupSystem:EndConfirm()
    self.model:ClearConfirm()
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_CONFIRM_END)
end

function GroupSystem:ReceiveConfirm(Time, Oper)
    self.model:ClearConfirm()
    self.model.confirmStartTime = Time
    if Oper ~= GetMainPlayerEID() then
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.READY_CHECK, function()
            Game.me:AgreeGroupConfirm()
        end, function()
            Game.me:CancelGroupConfirm()
        end)
    end
end

function GroupSystem:GetConfirmTimer()
    return self.model.confirmStartTime
end

function GroupSystem:TidyGroupTeamSlot(GroupIndex, TeamIndex)
    self.model:TidyGroupTeamSlot(GroupIndex, TeamIndex)
end

function GroupSystem:ExChangeMemberSlot(SrcSlot, DstSlot)
    local bTeamLeaderOld = self:IsTeamLeader()
    local ChangeTeamIndexList1, ChangeTeamIndexList2 = self.model:ExChangeMemberSlot(SrcSlot, DstSlot)
    local bTeamLeaderNew = self:IsTeamLeader()
    if bTeamLeaderOld ~= bTeamLeaderNew then
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_MEMBER_EXCHANGE, SrcSlot, DstSlot, ChangeTeamIndexList1, ChangeTeamIndexList2)
    self:SetGroupMemberCharacterTypeForViewBudget(self:GetMyGroupMemberIDList(), true)
end

function GroupSystem:ExChangeTeamSlot(SrcSlot, DstSlot)
    self.model:ExChangeTeamSlot(SrcSlot, DstSlot)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_TEAM_EXCHANGE, SrcSlot, DstSlot)
end

function GroupSystem:OnMemberSlotMove(changedSlotsInfo)
    --local changelist = self.model:OnMemberSlotMove(changedSlotsInfo)
end

function GroupSystem:OnOthersPropChange(EID, Key, Value)
    self.model:UpdateOthersPropData(EID, Key, Value)
    if Key == "planeID" then
        if self:IsInSameTeam() and self:IsTeamLeader(EID) and 
            GetMainPlayerPropertySafely("FollowState") ~= Enum.EFollowState.STOP_FOLLOW then
                Game.me:StopFollow()
        end
    end
    if Key == "memberFlag" then
        self:OnGroupMemberMarkChange(EID, Value)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_MEMBER_PROP_UPDATE, EID, Key, Value)
end

function GroupSystem:ReceiveConvene()
    Game.TeamSystem:ShowTeamTipsUI(Game.TeamSystem.TipsType.GroupConvene)
end

function GroupSystem:GetConfirmValuebyEID(EID)
    local ConfirmValue = 0
    if self.model.Confirm and self.model.Confirm[EID] then
        ConfirmValue = self.model.Confirm[EID]
    end
    return ConfirmValue
end

function GroupSystem:CreateLeague(groupLeagueId)
    self.model.LeagueID = groupLeagueId
end

function GroupSystem:ExitLeague(groupid)
    self.model:DelLeagueGroupData(groupid)
    Game.GlobalEventSystem:Publish(EEventTypesV2.GROUP_LEAGUE_DEL_GROUP, groupid)
end

function GroupSystem:JoinLeague(groupLeagueId, otherGroupInfos)
    self.model:JoinLeague(groupLeagueId, otherGroupInfos)
    Game.GlobalEventSystem:Publish(EEventTypesV2.GROUP_LEAGUE_ADD_GROUP)
    
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.LEAGUE_CREATE_SUCCESS)
end

function GroupSystem:AddLeagueGroup(groupLeagueId, otherGroupInfos)
    self.model:AddLeagueGroup(otherGroupInfos)
    Game.GlobalEventSystem:Publish(EEventTypesV2.GROUP_LEAGUE_ADD_GROUP)
end

function GroupSystem:IsInLeague()
    if self.model.LeagueID and self.model.LeagueID ~= 0 then
        return true 
    end
    return false
end

function GroupSystem:GetLeagueID()
    return self.model.LeagueID
end

function GroupSystem:AllSyncGroupInfo(groupLeagueId, groupId, GroupInfo)
    self.model:UpdateAllGroupData(groupLeagueId, groupId, GroupInfo)
    if self:GetMyGroupLeaderID() ~= Game.me.eid then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_PLAYER_ENTER_TEAM, {{self:GetMyGroupLeaderInfo().name}})
    end
    --团员属性
    self:SetGroupMemberCharacterTypeForViewBudget(self:GetMyGroupMemberIDList(), true)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_CREATE, groupId)
    Game.ChatSystem:OnTeamStateChange()
    Game.DungeonBattleStatisticsSystem:OnTeamStateChange()

    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_SCENE_MARK_CAHNGED)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_MEMBER_MARK_CAHNGED)
end

function GroupSystem:SetGroupMemberCharacterTypeForViewBudget(IDList, bAdd)
    if Game.me == nil then
        return
    end
    for key, value in ipairs(IDList) do
        if value ~= Game.me.eid then
            local Entity = Game.EntityManager:getEntity(value)
            if Entity then
                if bAdd then
                    if self:IsMyGroupMember(value) then
                        if self:IsInSameTeam(Game.me.eid, value) then
                            if Entity.CharacterTypeForViewBudget == nil or 
                            Entity.CharacterTypeForViewBudget ~= VIEW_BUDGET_CHARACTER_TYPE.PLAYER_TEAMMATE_SAME_SQUAD then
                                Entity.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_TEAMMATE_SAME_SQUAD  
                            end
                        else
                            if Entity.CharacterTypeForViewBudget == nil or 
                            Entity.CharacterTypeForViewBudget ~= VIEW_BUDGET_CHARACTER_TYPE.PLAYER_TEAMMATE_DIFFERENT_SQUAD then
                                Entity.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_TEAMMATE_DIFFERENT_SQUAD  
                            end
                        end
                    else
                        Entity.CharacterTypeForViewBudget = nil
                    end
                else
                    Entity.CharacterTypeForViewBudget = nil
                end
            end
        end
    end
end

function GroupSystem:GetMyGroupMemberIDList()
    local MyMemberInfo = self:GetMyGroupAllMemberInfo()
    local IDList = {}
    for TeamIndex, TeamInfo in pairs(MyMemberInfo) do
        for MemberIndex, MemberInfo in pairs(TeamInfo) do
            table.insert(IDList, MemberInfo.uid)
        end
    end
    return IDList
end

function GroupSystem:GetCurGroupNum()
    return self.model:GetCurGroupNum()
end

function GroupSystem:GetAutoAgreeState()
    if self.model.bAutoAgree == nil then
        return false  
    end 
    return self.model.bAutoAgree 
end

function GroupSystem:AddGroupMemberInfo(groupID, memberInfo, fromBuildApply, fromRescue, bIsTeamJoin)
    self.model:AddGroupMemberData(groupID, memberInfo, fromBuildApply, fromRescue, bIsTeamJoin)
    local IDList = {}
    for key, value in pairs(memberInfo) do
        table.insert(IDList, value.uid)
    end
    self:SetGroupMemberCharacterTypeForViewBudget(IDList, true)
    Game.GlobalEventSystem:Publish(EEventTypesV2.GROUP_ADD_MEMBER, groupID, memberInfo, fromBuildApply, fromRescue)
end

function GroupSystem:DelGroupMemberInfo(groupID, MemberInfo, OPUid)
    local refreshTeamList = self.model:DelGroupMemberInfo(groupID, MemberInfo, OPUid)
    local IDList = {}
    for key, value in pairs(MemberInfo) do
        table.insert(IDList, value.uid)
    end
    self:SetGroupMemberCharacterTypeForViewBudget(IDList, false)
    Game.GlobalEventSystem:Publish(EEventTypesV2.GROUP_DEL_MEMBER, groupID, MemberInfo, refreshTeamList)
end

function GroupSystem:GetGroupID()
    return self.model.GroupID
end

function GroupSystem:IsInGroup()
    if self.model.GroupID ~= 0 then
        return true
    end
    return false
end

function GroupSystem:GetIndexByEIDInMyGroup(EID)
    local AllGroupData = self:GetMyGroupAllMemberInfo()
    for k1, team in pairs(AllGroupData) do
        for k2, member in pairs(team) do
            if member.uid == EID then
                return k1, k2
            end
        end
    end
    return -1, -1
end

function GroupSystem:GetGroupIndexByGroupID(groupID)
    local Index = - 1 
    local AllGroupData = self:GetGroupAllData()
    for key, value in pairs(AllGroupData) do
        if value.groupId == groupID then
            Index = key
        end
    end
    return Index
end
---@return GroupRoleBrief
function GroupSystem:GetMemberInfo(GroupIndex, TeamIndex, MemberIndex)
    local Data = nil 
    local AllGroupData = self:GetGroupAllData()
    if AllGroupData[GroupIndex] and AllGroupData[GroupIndex].groupMembers and AllGroupData[GroupIndex].groupMembers[1] and 
        AllGroupData[GroupIndex].groupMembers[1][TeamIndex] and AllGroupData[GroupIndex].groupMembers[1][TeamIndex][MemberIndex] then
            Data = AllGroupData[GroupIndex].groupMembers[1][TeamIndex][MemberIndex]
    end
    return Data
end

function GroupSystem:GetMemberByEID(EID)
    local Data = nil 
    local AllGroupData = self:GetGroupAllData()
    for k1, group in pairs(AllGroupData)do
        for k2, team in pairs(group.groupMembers[1]) do
            for k3, member in pairs(team) do
                if member.uid == EID then
                    return member
                end
            end
        end
    end
    return Data
end

function GroupSystem:CalMyTeamMemberNum(TeamIndex)
    local GroupMemberInfo = self:GetMyGroupAllMemberInfo()
    local Num = 0
    if GroupMemberInfo then
        if GroupMemberInfo[TeamIndex] then
            for k1, v1 in pairs(GroupMemberInfo[TeamIndex]) do
                Num = Num + 1
            end
        end
    end
    return Num
end

function GroupSystem:GetGroupApplyList()
    return self.model:GetGroupApplyList()
end

function GroupSystem:HasGroupApplication()
    return self.model:HasGroupApplication()
end

function GroupSystem:GetGroupInviteList()
    return self.model:GetGroupInviteList()
end

function GroupSystem:HasGroupInvitation()
    return self.model:HasGroupInvitation()
end

function GroupSystem:UpdateGroupApply(dataType, roleBriefs, teamApplyBriefs)
    self.model:UpdateGroupApply(dataType, roleBriefs, teamApplyBriefs)
    Game.TeamSystem:OnGroupApplyChange(dataType, roleBriefs, teamApplyBriefs)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_APPLY_UPDATE,dataType, roleBriefs, teamApplyBriefs)
end

function GroupSystem:UpdateGroupInvite(roleBrief, groupID, groupLeaderInfo, groupDetails,
                                       bGroupLeader, bTeamLeader, positionCount)
    self.model:UpdateGroupInvite(roleBrief, groupID, groupLeaderInfo, groupDetails,
        bGroupLeader, bTeamLeader, positionCount)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_INVITE_CHANGED)
end

function GroupSystem:DelGroupInvite(groupID)
    self.model:DelGroupInvite(groupID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_INVITE_CHANGED)
end

function GroupSystem:UpdateGroupDetails(GroupDetails)
    self.model:UpdateGroupDetails(GroupDetails)
end

function GroupSystem:GetGroupDetails()
    return self.model:GetGroupDetails()
end

function GroupSystem:SetDragInfo(EID, TeamUUID, GroupIndex, TeamIndex, MemberIndex)
    self.model.DragInfo = {
        uid = EID,
        teamUUID = TeamUUID,
        groupIndex = GroupIndex,
        teamIndex = TeamIndex,
        memberIndex = MemberIndex
    }
end

function GroupSystem:GetDragInfo()
    return self.model.DragInfo
end

function GroupSystem:SetGroupDragType(Type)
    self.model.DragType = Type
end

function GroupSystem:GetGroupDragType()
    return self.model.DragType
end

function GroupSystem:ClearDragInfo()
    self.model.DragInfo = {}
    self.model.DragType = nil
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GROUP_END_ONCE_EXCHANGE)
end

function GroupSystem:SetTeamTabDragInfo(GroupIndex, TeamUUID, TeamIndex)
    self.model:SetTeamTabDragInfo(GroupIndex, TeamUUID, TeamIndex)
end

function GroupSystem:GetTeamTabDragInfo()
    return self.model:GetTeamTabDragInfo()
end

function GroupSystem:UpdateGroupLeader(groupID, leaderInfo)
    local OldLeaderEID = self:GetMyGroupLeaderInfo()
    local GroupAllInfo = self:GetGroupAllData()
    if GroupAllInfo then
        for key, value in pairs(GroupAllInfo)do
            if value.groupId == groupID then
                OldLeaderEID = value.leader 
                break
            end
        end
    end
    self.model:UpdateGroupLeader(groupID, leaderInfo)
    if leaderInfo.uid == GetMainPlayerEID() then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_BECOME_GROUP_LEADER, { { lang.CHINESE_YOU_RESP, } })
    else
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_BECOME_GROUP_LEADER,
            { { leaderInfo.name } })
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.GROUP_LEADER_CHANGED, OldLeaderEID, leaderInfo.uid)
end

function GroupSystem:GetGroupAllData()
    return self.model.GroupAllInfo
end

function GroupSystem:GetGroupAllMemberInfo()
    local Members = {}
    for key, value in pairs(self.model.GroupAllInfo)do
        Members[key] = value.groupMembers
    end
    return  Members
end

function GroupSystem:GetTeamNumbyIndex(GroupIndex, TeamIndex)
    local Num = 0
    local Data = self:GetGroupAllData()
    if Data and Data[GroupIndex] and Data[GroupIndex].groupMembers and Data[GroupIndex].groupMembers[1][TeamIndex] then
        for key, value in pairs(Data[GroupIndex].groupMembers[1][TeamIndex]) do
            Num = Num + 1
        end
    end
    return Num
end

function GroupSystem:GetTeamUUID(GroupIndex, TeamIndex)
    local TeamUUID = 0
    local Data = self:GetGroupAllData()
    if Data and Data[GroupIndex] 
            and Data[GroupIndex].groupMembers
            and Data[GroupIndex].groupMembers[1]
            and Data[GroupIndex].groupMembers[1][TeamIndex] then
        for key, value in pairs(Data[GroupIndex].groupMembers[1][TeamIndex]) do
            TeamUUID = value.teamUUID
            break
        end
    end
    return TeamUUID
end

function GroupSystem:GetMyGroupIndex()
    return self.model.MyGroupIndex
end

function GroupSystem:GetMyTeamLeaderInfo()
    local MyGroupData = self:GetMyGroupAllMemberInfo()
    if MyGroupData then
        for k1, v1 in pairs(MyGroupData) do
            if v1[GROUP_TEAM_LEADER_INDEX] then
                return v1[GROUP_TEAM_LEADER_INDEX]
            end
        end
    end
    return nil
end

function GroupSystem:GetMyTeamNum()
    local Num= 0
    local GroupTeamInfo = self:GetMyGroupTeamMemberData()
    if GroupTeamInfo then
        for k1, v1 in pairs(GroupTeamInfo) do
            Num = Num +1
        end
    end
    return Num
end

function GroupSystem:GetMyTeamInfo(TeamIndex)
    local TeamInfo = {}
    if self:GetMyGroupAllMemberInfo()[TeamIndex] then
        TeamInfo = self:GetMyGroupAllMemberInfo()[TeamIndex]
    end
    return TeamInfo
end

function GroupSystem:GetMyGroupLeaderID()
    local ID = ""
    if next(self:GetMyGroupAllInfo()) ~= nil then
        ID =  self:GetMyGroupAllInfo().leader
    end
    return ID
end

function GroupSystem:GetMyGroupLeaderInfo()
    local AllMemberInfo = self:GetMyGroupAllMemberInfo()
    if AllMemberInfo then
        for k2, v2 in pairs(AllMemberInfo) do
            for k3, v3 in pairs(v2) do
                if v3.uid == self:GetMyGroupLeaderID() then
                    return v3
                end
            end
        end
    end
    return {}
end

function GroupSystem:GetMyGroupAllInfo()
    if self.model.MyGroupIndex and self.model.GroupAllInfo[self.model.MyGroupIndex] then
        return self.model.GroupAllInfo[self.model.MyGroupIndex]
    end
    return {}
end

function GroupSystem:GetMyGroupAllMemberInfo()
    local Info = {}
    if self:GetMyGroupAllInfo() and self:GetMyGroupAllInfo().groupMembers and self:GetMyGroupAllInfo().groupMembers[1] then
        Info = self:GetMyGroupAllInfo().groupMembers[1]
    end
    return Info
end

function GroupSystem:GetMyGroupMemberInfo(TeamIndex, MemberIndex)
    local Data = {}
    local MyGroupMemberInfo = self:GetMyGroupAllMemberInfo()
    if MyGroupMemberInfo and MyGroupMemberInfo[TeamIndex] and MyGroupMemberInfo[TeamIndex][MemberIndex] then
        Data = MyGroupMemberInfo[TeamIndex][MemberIndex]
    end
    return Data
end

function GroupSystem:GetMyGroupMemberNum()
    local Num = 0
    local AllMemberInfo = self:GetMyGroupAllMemberInfo()
    if AllMemberInfo then
        for k2, v2 in pairs(AllMemberInfo) do
            for k3, v3 in pairs(v2) do
                Num = Num + 1
            end
        end
    end
    return Num
end

function GroupSystem:GetTeamUUIDByEID(EID)
	if EID == nil then
        EID = GetMainPlayerEID()
	end
    local GroupAllInfo = self:GetGroupAllData()
    local GroupTeamInfo = {}
    for k1, v1 in pairs(GroupAllInfo) do
        for k2, v2 in pairs(v1.groupMembers[1]) do
            for k3, v3 in pairs(v2) do
                if v3.uid == EID then
                    return v3.teamUUID
                end
            end
        end
    end
    return ""
end

function GroupSystem:UpdateTeamLeader(oldLeaderID, newLeaderID)
    self.model:UpdateTeamLeader(oldLeaderID, newLeaderID)
    if oldLeaderID == Game.me.eid or newLeaderID == Game.me.eid then
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.CLIENT_GROUP_TEAMLEADER_UPDATE, oldLeaderID, newLeaderID)
end

function GroupSystem:GetDragEID()
    return self.model:GetDragEID()
end

function GroupSystem:IsGroupLeader(EID)
    local SearchID = EID
    if SearchID == nil then
        SearchID = GetMainPlayerEID()
    end
    local Data = self:GetGroupAllData()
    if Data then
        for key, value in pairs(Data) do
            if value.leader == SearchID then
                return true
            end
        end
    end
    return false
end

function GroupSystem:IsTeamLeader(EID)
    local SearchID = EID
    if SearchID == nil then
        SearchID = GetMainPlayerEID()
    end
    local GroupAllData = self:GetGroupAllData()
    if GroupAllData then
        for k1, v1 in pairs(GroupAllData) do
            if v1.groupMembers == nil or v1.groupMembers[1] == nil then
                return false
            end
            for k2, v2 in pairs(v1.groupMembers[1]) do
                if v2[GROUP_TEAM_LEADER_INDEX] and v2[GROUP_TEAM_LEADER_INDEX].uid == SearchID then
                    return true
                end
            end
        end   
    end     
    return false
end

function GroupSystem:IsNormalTeamMember(EID)
    if self:IsGroupLeader(EID) then
        return false
    elseif self:IsTeamLeader(EID) then
        return false
    end
    return true
end

function GroupSystem:InSameGroupAndNotInSameTeam(EID)
    if self:IsMyGroupMember(EID) and self:IsInSameTeam(EID, Game.me.eid) then
        return true
    end
    return false
end

function GroupSystem:IsInSameTeam(ID1, ID2)
    local GeoupAllData = self:GetGroupAllData()
    local GroupIndex1 = 0
    local TeamIndex1 = 0
    local GroupIndex2 = 0
    local TeamIndex2 = 0
    for k1, v1 in pairs(GeoupAllData) do
        if v1.groupMembers == nil or v1.groupMembers[1] == nil then
            return false
        end
        for k2, v2 in pairs(v1.groupMembers[1]) do
            for k3, v3 in pairs(v2) do
                if v3.uid == ID1 then
                    GroupIndex1 = k1
                    TeamIndex1 = k2
                end
                if v3.uid == ID2 then
                    GroupIndex2 = k1
                    TeamIndex2 = k2
                end
            end
        end
    end
    if GroupIndex1 > 0 and TeamIndex1 > 0 and GroupIndex2 > 0 and TeamIndex2 > 0 and
        GroupIndex1 == GroupIndex2 and TeamIndex1 == TeamIndex2 then
        return true
    end
    return false
end

function GroupSystem:IsMyGroupMember(EID)
    local MyGroupInfo = self:GetMyGroupAllMemberInfo()
    if MyGroupInfo then
        for k1, v1 in pairs(MyGroupInfo) do
            for k2, v2 in pairs(v1) do
                if v2.uid == EID then
                    return true
                end
            end
        end
    end
    return false
end

function GroupSystem:GetMyGroupTeamMemberData()
    local GroupMemberInfo = self:GetMyGroupAllMemberInfo()
    local GroupTeamInfo = {}
    if GroupMemberInfo then
        for k1, v1 in pairs(GroupMemberInfo) do
            for k2, v2 in pairs(v1) do
                if v2.uid == GetMainPlayerEID() then
                    GroupTeamInfo = v1
                end
            end    
        end
    end
    return GroupTeamInfo
end

function GroupSystem:IsMyGroupMemberAllFollowed()
    local GroupMemberInfo = self:GetMyGroupAllMemberInfo()
    if GroupMemberInfo then
        for k1, v1 in pairs(GroupMemberInfo) do
            for k2, v2 in pairs(v1) do
                if v2.bFollow == false and Game.me and v2.uid ~= Game.me.eid then
                    return false
                end
            end
        end
    end
    return true
end

function GroupSystem:IsTeamMemberAllFollowed()
    local GroupTeamInfo = self:GetMyGroupTeamMemberData()
    if GroupTeamInfo then
        for k1, v1 in pairs(GroupTeamInfo) do
            if v1.bFollow == false and Game.me and v1.uid ~= Game.me.eid then
                return false
            end
        end
    end
    return true
end

function GroupSystem:GetLeagueEachGroupNum(GroupIndex)
    local Num = 0
    if self:GetGroupAllData()[GroupIndex] and self:GetGroupAllData()[GroupIndex].groupMembers then
        for k1, v1 in pairs(self:GetGroupAllData()[GroupIndex].groupMembers) do
            for k2, v2 in pairs(v1) do
                for k3, v3 in pairs(v2) do
                    Num = Num + 1
                end
            end
        end
    end
    return Num
end

function GroupSystem:IsTeamInConvene()
    if Game.me.groupID == 0 then
        return false
    end
    if self:IsTeamMemberAllFollowed() and self:GetMyTeamNum() > 1 then
        return true
    end
    return false
end

function GroupSystem:GetGroupTeamMemberNum()
    local GroupTeamInfo = self:GetMyGroupTeamMemberData()
    return #GroupTeamInfo
end

function GroupSystem:CalGroupMemberTypeNum(Type)
    local GroupMemberInfo = self:GetMyGroupAllMemberInfo()
    local Num = 0
    for k2, v2 in pairs(GroupMemberInfo) do
        for k3, v3 in pairs(v2) do  
            local PlayerTableData = Game.TableData.GetPlayerSocialDisplayDataRow(v3.profession)
            if PlayerTableData then
                if table.ikey(PlayerTableData[0].PositionType, Type) ~= nil then
                    Num = Num + 1
                end
            end
        end
    end
end

function GroupSystem:GetDefendMemberNum()
    -----坦克数量----
    return self:CalGroupMemberTypeNum(team_utils.TeamPosition.Defend)
end

function GroupSystem:GetAttackMemberNum()
    -----输出数量----
    return self:CalGroupMemberTypeNum(team_utils.TeamPosition.Attack)
end

function GroupSystem:GetHealMemberNum()
    -----治疗数量----
    return self:CalGroupMemberTypeNum(team_utils.TeamPosition.Defend)
end

function GroupSystem:GetGroupMemberDetail(eid)
    local GroupAllData = self:GetGroupAllData()
    for k1, v1 in pairs(GroupAllData) do
        if v1.groupMembers[1] then
            for k2, v2 in pairs(v1.groupMembers[1]) do
                for k3, v3 in pairs(v2) do
                    if v3.uid == eid then
                        return v3
                    end
                end
            end
        end
    end
    return nil
end

function GroupSystem:GetGroupMembers()
	local groupMembers = {}
	local groupAllData = self:GetGroupAllData()
	for k1, v1 in pairs(groupAllData) do
		for k2, v2 in pairs(v1.groupMembers) do
			for k3, v3 in pairs(v2) do
				for k4, v4 in pairs(v3) do
					table.insert(groupMembers, v4)
				end
			end
		end
	end
	return groupMembers
end

function GroupSystem:GetMaxGroupSize()
    return Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") * Game.TableData.GetConstDataRow("GROUP_TEAM_COUNT")
end

return GroupSystem
