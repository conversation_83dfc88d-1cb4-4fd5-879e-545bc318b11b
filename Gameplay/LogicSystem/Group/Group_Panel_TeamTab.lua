local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@class Group_Panel_TeamTab : UIComponent
local Group_Panel_TeamTab = DefineClass("Group_Panel_TeamTab", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local UIDrag = kg_require("Framework.KGFramework.KGUI.Component.CommonLogic.UIComDragWidget")


function Group_Panel_TeamTab:OnCreate()
    self.GroupIndex = nil -- 团队索引
    self.TeamIndex = nil  -- 队伍索引
    self.TeamUUID = nil   -- 队伍ID
    -- self.view.SizeBox:SetVisibility(ESlateVisibility.Visible)
    --self:AddUIListener(EUIEventTypes.MouseButtonDown, self.userWidget, self.OnMouseBtnDown)
    --self:AddUIListener(EUIEventTypes.DragEnter, self.userWidget, self.OnDragEnter)
    --self:AddUIListener(EUIEventTypes.DragLeave, self.userWidget, self.OnDragLeave)


    ---@type UIComDragWidget
    self.uiDragCom = self:CreateComponent(self.view.Btn_DragArea, UIDrag)

    self.uiDragCom:SetDragSource(self.userWidget, false)

    self:AddUIEvent(self.uiDragCom.onDragDetectedEvent, "OnDragDetected")
    self:AddUIEvent(self.uiDragCom.onDragCancelEvent, "OnDragCancelled")
    self:AddUIEvent(self.uiDragCom.onDragLeaveEvent, "OnDragLeave")
    self:AddUIEvent(self.uiDragCom.onDragEnterEvent, "OnDragEnter")
    self:AddUIEvent(self.uiDragCom.onDragDropEvent, "OnDrop")
end

function Group_Panel_TeamTab:Refresh(PageIndex, TeamIndex)
    self.GroupIndex = PageIndex
    self.TeamIndex = TeamIndex
    self.TeamUUID = Game.GroupSystem:GetTeamUUID(PageIndex, TeamIndex)
    self.view.Text_TeamTitleNumber_lua:SetText(Game.GroupSystem.ETeamData[TeamIndex])
end

function Group_Panel_TeamTab:OnEditModuleChange(enter)
    if enter then
        self.view.Btn_DragArea:SetVisibility(ESlateVisibility.Visible)
    else
        self.view.Btn_DragArea:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function Group_Panel_TeamTab:OnDragDetected()
    local drag = self.uiDragCom:GetDragWidget()
    drag.Text_TeamTitleNumber_lua:SetText(Game.GroupSystem.ETeamData[self.TeamIndex])

    self.bIsDrag = true
    Game.GroupSystem:SetTeamTabDragInfo(
        self.GroupIndex,
        self.TeamUUID,
        self.TeamIndex)
    Game.GroupSystem:SetGroupDragType(Game.GroupSystem.EDragType.ETeam)
end

function Group_Panel_TeamTab:OnDragCancelled()
    Log.Debug("DragDropOperation.OnDragCancelled")
end

function Group_Panel_TeamTab:OnDragEnter()
end

function Group_Panel_TeamTab:OnDragLeave()
end

function Group_Panel_TeamTab:OnDrop()
    if Game.GroupSystem:GetGroupDragType() == Game.GroupSystem.EDragType.ETeam and self.TeamUUID ~= 0 then
        Game.me:ChangeGroupTeamSlot(Game.GroupSystem:GetTeamTabDragInfo(), {
            teamUUID = self.TeamUUID,
            teamIndex = self.TeamIndex,
            groupIndex = self.GroupIndex
        })
    end
    return nil
end

return Group_Panel_TeamTab
