---@class P_Shout : UIController
--- @field public View WBP_TeamDialogueView
local P_Shout = DefineClass("P_Shout", UIController)
local StringConst = require "Data.Config.StringConst.StringConst"
local P_BasicInput = kg_require "Gameplay.LogicSystem.CommonUI.CommonInput.P_BasicInput"
local const = kg_require("Shared.Const")
local P_ComCheckBox = kg_require("Gameplay.LogicSystem.CommonUI.P_ComCheckBox")
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
local ESlateVisibility = import("ESlateVisibility")

P_Shout.eventBindMap = {
    [_G.EEventTypes.TEAM_SHOUT_TEXT_CHANGED] = "OnHistoryTextChange",
}


function P_Shout:OnCreate()
    self.Type = nil
    self.Text = ""
    self.ShoutInput = self:BindComponent(self.View.WBP_ComInput, P_BasicInput)
    self.TagCheckBoxList = {}  --优化新增的标签
    self:AddUIListener(EUIEventTypes.TextChanged, self.View.WBP_ComInput.EditText, self.FakeTextChange)
    for i = 1,3 do
        self.TagCheckBoxList[i] = self:BindComponent(self.View["WBP_ComCheckBox_" .. tostring(i)], P_ComCheckBox)
        self.TagCheckBoxList[i]:SetCheckBoxText(string.match(Game.TeamSystem.model.ShoutTagName[i], "<.->(.-)</>"))
        self.TagCheckBoxList[i].OnCheckStateChangeCallBack = function()
            Game.TeamSystem.model.ShoutTagInfoList[i] = self.TagCheckBoxList[i]:IsChecked()
            self:FakeTextChange()
        end
    end
    self:OnTextChanged()
    self:FakeTextChange()
end

function P_Shout:OnRefresh()
    self.bRescue =  Game.NetworkManager.GetLocalSpace() ~= nil and Game.NetworkManager.GetLocalSpace().bCanSeekRescue or false
    if self.bRescue then
        self.View.WBP_ComPopupM.WBP_ComPopupTitle.Text_Title:SetText("求援")
    else
        self.View.WBP_ComPopupM.WBP_ComPopupTitle.Text_Title:SetText(StringConst.Get("GROUP_SHOUT"))
    end
    self.View.WBP_ComBtn_Recruit.Text_Com:SetText(StringConst.Get("INVITE_SEND_TO_RECRUIT"))
    self.View.WBP_ComBtn_Guild.Text_Com:SetText(StringConst.Get("INVITE_SEND_TO_GUILD"))
    local TeamTime = Game.ChatSystem:GetChatChannelExpirationTime(Enum.EChatChannelData["RECRUIT"])//1000
    if TeamTime > 0 then
        --申请CD中
        self.View.WBP_ComBtn_Recruit:SetIsEnabled(false)
        self:StartTimer(
            "TeamRescuetimer",
            function()
                if TeamTime <= 0 then
                    self.View.WBP_ComBtn_Recruit:SetIsEnabled(true)
                    self.View.WBP_ComBtn_Recruit.Text_Com:SetText(StringConst.Get("INVITE_SEND_TO_RECRUIT"))
                    self:StopTimer("TeamRescuetimer")
                    return
                else
                    self.View.WBP_ComBtn_Recruit.Text_Com:SetText(StringConst.Get("INVITE_SEND_TO_RECRUIT")..math.floor(TeamTime))
                end
                TeamTime = TeamTime - 1
            end, 
            1000, 
            -1, 
            nil, 
            true
        )
    else
        self.View.WBP_ComBtn_Recruit:SetIsEnabled(true)
    end
    local GuildTime = Game.ChatSystem:GetChatChannelExpirationTime(Enum.EChatChannelData["GUILD"])//1000
    if GuildTime > 0 then
        --申请CD中
        self.View.WBP_ComBtn_Guild:SetIsEnabled(false)
        self:StartTimer(
            "GuildRescuetimer",
            function()
                if GuildTime <= 0 then
                    self.View.WBP_ComBtn_Guild:SetIsEnabled(true)
                    self.View.WBP_ComBtn_Guild.Text_Com:SetText(StringConst.Get("INVITE_SEND_TO_GUILD"))
                    self:StopTimer("GuildRescuetimer")
                    return
                else
                    self.View.WBP_ComBtn_Guild.Text_Com:SetText(StringConst.Get("INVITE_SEND_TO_GUILD")..math.floor(GuildTime))
                end
                GuildTime = GuildTime - 1
            end, 
            1000, 
            -1, 
            nil, 
            true
        )
    else
        self.View.WBP_ComBtn_Guild:SetIsEnabled(true)
    end
    self.Text = StringConst.Get("GROUP_SHOUT_DEFAULT_MESSAGE")
    self.View.WBP_ComInput.EditText:SetText(self.Text)
    self.View.WBP_ComInput.EditText:SetHintText(StringConst.Get("GROUP_HISTORT_MESSAGE"))

    local DefaultText = ""
    self.ShoutInput:ClearInputText()
    if self.bRescue then
        --求援
        for i = 1,3 do
            Game.TeamSystem.model.ShoutTagInfoList[i] = false
            self.TagCheckBoxList[i]:SetChecked(false)
        end
        DefaultText = StringConst.Get("TEAM_RESCUE_DEFAULT")
        local PositionNeedMap = {}
        local CurTeamPosMap = {} 
        if Game.me.teamTargetID and Game.me.teamTargetID ~= const.DEFAULT_NO_TARGET_ID then
            local TargetTable = Game.TableData.GetTargetDataRow(Game.me.teamTargetID)
            if TargetTable then
                local ProfessionNeed = TargetTable.ProfessionNeed
                if ProfessionNeed then
                    for key, value in ksbcipairs(ProfessionNeed) do
                        if PositionNeedMap[value] == nil then
                            PositionNeedMap[value] = 0 
                        end
                        PositionNeedMap[value] = PositionNeedMap[value] + 1
                    end
                end
            end
        end
        local CurTeamPosMap = {}
        for key, value in pairs(Game.me.teamInfoList) do
            local Pos = Game.TableData.GetPlayerSocialDisplayDataRow(value.profession)[value.sex].PositionType[1]
            if CurTeamPosMap[Pos] == nil then
                CurTeamPosMap[Pos] = 0 
            end
            CurTeamPosMap[Pos] = CurTeamPosMap[Pos] + 1
        end
        local Res = {}
        if next(PositionNeedMap) then
            for key, value in pairs(PositionNeedMap) do
                if CurTeamPosMap[key] == nil or CurTeamPosMap[key] < value then
                    table.insert(Res, key)
                end
            end
        end
        if #Res > 0 then
            for key, value in ipairs(Res) do
                self.TagCheckBoxList[value + 1]:SetChecked(true)
                Game.TeamSystem.model.ShoutTagInfoList[value + 1] = self.TagCheckBoxList[value + 1]:IsChecked()
            end
        end
        self:FakeTextChange()
    else
        for index,value in ipairs(self.TagCheckBoxList) do
            value:SetChecked(Game.TeamSystem.model.ShoutTagInfoList[index])
        end
    end
    self.ShoutInput:SetData(
        {
            Owner = self,
            HintText = StringConst.Get("GROUP_SHOUT_HINT"),
            DefaultInputText = DefaultText,
            OnValueChangedCallback = self.OnTextChanged,
            InputLimit = 30,
        }
    )
end

function P_Shout:OnHistoryTextChange(text)
    self.ShoutInput:SetInputText(text)
    self:OnTextChanged()
end

function P_Shout:OnTextChanged()
    self.View.Text_RemainNum:SetText(string.format(StringConst.Get("GROUP_LEFT_COUNT"), 30 - self.ShoutInput:GetLength()))
end

function P_Shout:FakeTextChange()
    local text = self.ShoutInput:GetInputText()
    if not text or text == "" then
        text = StringConst.Get("GROUP_SHOUT_HINT")
    else
        if string.find(text,"<.*>") then
            text = string.gsub(text, ">", "<")
        end
    end
    self.View.RichTextBlock:SetText("<NormalShout>" .. text .. "</>" .. Game.TeamSystem:GetShoutInfoOutput(Game.TeamSystem.model.ShoutTagInfoList))
end

function P_Shout:OnClick_WBP_Dialogueinput_HistoryBtn()
    local _, ViewportPosition =
        import("SlateBlueprintLibrary").LocalToViewport(
            _G.GetContextObject(),
            self.View.WBP_Dialogueinput.HistoryBtn:GetCachedGeometry(),
            FVector2D(1, 0),
            nil,
            nil
        )
    Game.NewUIManager:OpenPanel("TeamDialogueHistory", ViewportPosition)
end

function P_Shout:SetShoutText(Text)
    self.View.WBP_ComInput.EditText:SetText(Text)
    self:OnTextChanged()
end

function P_Shout:OnClick_WBP_ComBtn_Recruit_Btn_Com()
    local Text = self.View.WBP_ComInput.EditText:GetText()
    if ChatUtils.CheckInjectInput(Text) or string.match(Text,"</>.*<.*>") ~= nil then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED)  --luacheck: ignore
        return
    end 
    Game.AllInSdkManager:IsSensitiveWords(Text, function(bSensitive)
        Game.TeamSystem:IntoShoutProcess(bSensitive, Enum.EChatChannelData["RECRUIT"], Text)
    end)
    self:CloseSelf()
end

function P_Shout:OnClick_WBP_ComBtn_Guild_Btn_Com()
    if not Game.GuildSystem:HasJoinGuild() then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_HAVE_NOT_YET) -- luacheck: ignore
        return
    end
    local Text = self.View.WBP_ComInput.EditText:GetText()
    if ChatUtils.CheckInjectInput(Text) or string.match(Text,"</>.*<.*>") ~= nil then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED)  --luacheck: ignore
        return
    end
    Game.AllInSdkManager:IsSensitiveWords(Text, function(bSensitive)
        Game.TeamSystem:IntoShoutProcess(bSensitive, Enum.EChatChannelData["GUILD"], Text)
    end)
    self:CloseSelf()
end

function P_Shout:OnClick_WBP_ComPopupM_WBP_ComPopupTitle_WBP_ComBtnClose_Button()
    self:CloseSelf()
end

function P_Shout:OnTextCommitted_WBP_Dialogueinput_EditText()

end

function P_Shout:UpdateBoundRectWidgets()
    self.widget:AddPanelRegionWidget(self.View.WBP_ComPopupM.WidgetRoot)
end

return P_Shout
