local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@class Group_Panel_TabItem : BaseListItemComponent
--- @field public View WBP_TeamHTabList2View
local Group_Panel_TabItem = DefineClass("Group_Panel_TabItem", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
function Group_Panel_TabItem:OnCreate()
    --self:AddUIListener(EUIEventTypes.Drop, self.userWidget, "OnDrop")
    
    self:AddUIEvent(self.view.Button_lua.OnClicked, "OnClick_Button")
    --self:AddUIListener(EUIEventTypes.Drop, self.userWidget, "OnDrop")
    self:AddUIEvent(self.widget.OnDropEvent, "OnDrop")

    self.Index = nil -- 团队索引
end

function Group_Panel_TabItem:OnClick_Button()
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Paper_Switch, true)
    UI.Invoke(UIPanelConfig.Group_Panel, "UpdateGroupSelTab", self.Index)
end

function Group_Panel_TabItem:Refresh(Index, Selected, bEditModule)
    self.Index = Index
    local CurNum = Game.GroupSystem:GetLeagueEachGroupNum(self.Index)
    if CurNum > 0 then
        self.userWidget:SetVisibility(ESlateVisibility.Visible)
        self.view.Text_Name_lua:SetText(Game.GroupSystem.EGroupData[Index] .. CurNum .. "/30")
    else
        self.userWidget:SetVisibility(ESlateVisibility.Hidden)
    end
    if bEditModule then
        self.view.Button_lua:SetIsEnabled(false)
    else
        self.view.Button_lua:SetIsEnabled(true)
    end
    self.userWidget:Event_Tab_Style(Selected and 1 or 0, 0)
end

function Group_Panel_TabItem:OnDrop()
    UI.Invoke(UIPanelConfig.Group_Panel, "InDrag", false)
    if Game.GroupSystem:GetGroupDragType() == Game.GroupSystem.EDragType.EMember then
        Game.me:ChangeGroupMemberSlot(Game.GroupSystem:GetDragInfo(), {
            uid = "",
            teamUUID = 0,
            groupIndex = self.Index,
            teamIndex = 0,
            memberIndex = 0
        })
    end
    Game.GroupSystem:ClearDragInfo()
    return nil
end

return Group_Panel_TabItem
