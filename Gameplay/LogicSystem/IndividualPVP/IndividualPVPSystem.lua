local ESpawnActorCollisionHandlingMethod = import("ESpawnActorCollisionHandlingMethod")

---@class IndividualPVPSystem:SystemBase
local IndividualPVPSystem = DefineClass("IndividualPVPSystem",SystemBase)
local pvpConst = kg_require("Shared.Const.PVPConst")

---@private
function IndividualPVPSystem:onCtor()
    self.model = nil
    self.sender = nil
end

---@private
function IndividualPVPSystem:onInit()
    ---@type IndividualPVPModel
    self.model = kg_require("Gameplay.LogicSystem.IndividualPVP.IndividualPVPModel").new(false, false)
    ---@type IndividualPVPSender
    self.sender = kg_require("Gameplay.LogicSystem.IndividualPVP.IndividualPVPSender").new()
    Game.EventSystem:AddListener(_G.EEventTypes.GAME_ENTER_STAGE_SHOW_UI_END, self, self.RefreshUI)
end

---@private
function IndividualPVPSystem:onUnInit()
end

-- 加载场景特效资源
function IndividualPVPSystem:LoadSceneEffect(x, y, z)
    self.model.vector = FVector(x, y, z)
    local path = Game.TableData.Get1V1SettingDataRow("SpecEffectPath")
    self.model.effectID = Game.me:PlayNiagaraEffectAtLocation(path, FTransform(FRotator(0):ToQuat(), self.model.vector))
end

-- 进入切磋位面
function IndividualPVPSystem:EnterIndividualPVP(x, y, z)
    self:LoadSceneEffect(x, y, z)
    -- 开始倒计时
    self:ShowIndividualPVPStartCountDown()
    -- 隐藏附近的NPC以及交换按钮
	Game.WorldManager:SetTaskNpcCategoryVisible(false)
    Game.HUDSystem:HideUI("P_HUDInteract")
end

-- 发起切磋邀请
function IndividualPVPSystem:ApplyIndividualPVP(targetAvatarID)
    -- 发送切磋请求
    self.sender:ReqIndividualPVP(targetAvatarID)
end

-- 收到切磋邀请
function IndividualPVPSystem:OnRecieveIndividualPVP(inviterID, name, level, profession)
    Game.PopTipsSystem:OnAddAddApplyInviteListElem({
        Type = Enum.EElasticStripData.ApplyIndividualPVP,
        OperName = name,
        OperProfession = profession,
        OperID = inviterID,
        OperLevel = level,
        IsTeam = false
    })
end

-- 点击同意/拒绝切磋邀请（在这里做一下多次点击拦截处理）
function IndividualPVPSystem:OnClickReqIndivitualPVPResponse(InviterID, agree)
    if self.reqList == nil then
        self.reqList = {}
    end
    if self.reqList[InviterID] == nil then
        self.reqList[InviterID] = agree
        self:StartTimer("ReqIndivitualPVPResponse", function()
            self.sender:ReqIndivitualPVPResponse(InviterID, agree)
            self.reqList[InviterID] = nil
    end, 200, 1)
    end
end

-- 战斗5秒倒计时
function IndividualPVPSystem:ShowIndividualPVPStartCountDown()
    local now = self.model.prepareStateStartTime or _G._now()
    -- 如果是断线重连
    if now + 100 < _G._now() then
        -- 隐藏附近的NPC以及交换按钮
        Game.HUDSystem:HideUI("P_HUDInteract")
    end
    local endTime = now + Game.TableData.Get1V1SettingDataRow("PrepareTime")*1000
    local remainTime = endTime - now
    if remainTime > 0 then
        if endTime == nil then
            Log.Error("IndividualPVPSystem:ShowIndividualPVPStartCountDown1 endTime = isNil, IndividualPVPSystem")
            return
        end
        -- 加100 兼容地板除 防止5倒计时文本被吞
		Game.NewUIManager:OpenPanel(UIPanelConfig.CountDownPVP_Panel, endTime + 100)
    else
        if endTime == nil then
            Log.Error("IndividualPVPSystem:ShowIndividualPVPStartCountDown2 endTime = isNil, IndividualPVPSystem")
            return
        end
		Game.NewUIManager:OpenPanel(UIPanelConfig.CountDownPVP_Panel, endTime + 100)
    end
end

-- 切磋总时间倒计时
function IndividualPVPSystem:ShowIndividualPVPCountDown(x, y, z)
    local startTime = self.model.battleStateStartTime or _G._now()
    -- 判断如果是断线重连
    if startTime + 500 < _G._now() then
        -- 断线重连为加载完场景之后下发，UI加载需要一定延迟
        self:StartTimer("IndividualPVPCountDown", function()
            if self:CanShow1v1Countdown() then
                self:LoadSceneEffect(x, y, z)
                -- 隐藏附近的NPC以及交换按钮
                Game.HUDSystem:HideUI("P_HUDInteract")
                self:StopTimer("IndividualPVPCountDown")
            end
        end, 500, 1)
    else
        Game.HUDSystem:ShowUI(UICellConfig.HUD1v1Countdown, startTime +  Game.TableData.Get1V1SettingDataRow("FightTime")*1000)
    end
end

-- 切磋状态统一处理
function IndividualPVPSystem:ChangeIndividualPVPState(state, stateStartTime, x, y, z)
    self.model:OnStateChange(state)
    if state == pvpConst.INDIVIDUAL_PVP_STAGE.STAGE_PREPARE then
        self.model.prepareStateStartTime = stateStartTime
        self:EnterIndividualPVP(x, y, z)
    elseif state == pvpConst.INDIVIDUAL_PVP_STAGE.STAGE_BATTLE then
        self.model.battleStateStartTime = stateStartTime
        self:ShowIndividualPVPCountDown(x, y, z)
    end
end

-- 离开结果统一处理
function IndividualPVPSystem:OnReceiveIndividualPVPLeave(result)
    local leaveReason = pvpConst.INDIVIDUAL_PVP_LEAVE_REASON
    if result == leaveReason.REASON_LEAVE_RANGE then
        self:StartOutOfIndividualPVPSceneTimer()
    elseif result == leaveReason.REASON_REENTER_RANGE then
        self:OnCancelReminder()
    elseif result == leaveReason.REASON_LEAVE_SAFE_PLACE then
        -- todo 离开安全区提醒

    end
end

-- 邀请结果统一处理
function IndividualPVPSystem:ApplyIndividualPVPResult(reason)
    if self.model.reasonToReminderMap == nil then
        self.model:InitReasonToReminderMap()
    end
    local reminderTextData = self.model.reasonToReminderMap[reason]
    if reminderTextData then
        Game.ReminderManager:AddReminderById(reminderTextData)
    end
end

-- 超出切磋范围提醒
function IndividualPVPSystem:StartOutOfIndividualPVPSceneTimer()
    local now = _G._now()
    local endTime = now + Game.TableData.Get1V1SettingDataRow("LeaveRangeTime")*1000
    local remainTime = endTime - now
    -- 超出切磋范围倒计时
    Log.InfoFormat("IndividualPVPSystem：超出切磋范围倒计时:%s", remainTime)
    if remainTime > 0 then
        -- 这个是通用的通知调用
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OUT_OF_SCENE, endTime)
    end
end

-- 回到安全区域，提示框停止
function IndividualPVPSystem:OnCancelReminder()
    Game.ReminderManager:StopSpecificReminder(Enum.EReminderTextData.OUT_OF_SCENE)
end

-- 战斗结束，进行战斗结算
function IndividualPVPSystem:OnEndIndividualPVP(result)
    if self.model.effectID then
        Game.me:DestroyNiagaraSystem(self.model.effectID)
    end
    Game.HUDSystem:HideUI(UICellConfig.HUD1v1Countdown)
    if result == pvpConst.INDIVIDUAL_PVP_ACTOR_RESULT.RESULT_WIN then
        -- 弹出胜利面板
		Game.NewUIManager:OpenPanel(UIPanelConfig.HUDDungeonResult_Panel, Enum.ArenaSettleResult.WIN)
    elseif result == pvpConst.INDIVIDUAL_PVP_ACTOR_RESULT.RESULT_LOSE then
        -- 弹出失败面板
		Game.NewUIManager:OpenPanel(UIPanelConfig.HUDDungeonResult_Panel, Enum.ArenaSettleResult.FAIL)
    elseif result == pvpConst.INDIVIDUAL_PVP_ACTOR_RESULT.RESULT_DRAW then
        -- 弹出平局面板(目前按照胜利面板)
		Game.NewUIManager:OpenPanel(UIPanelConfig.HUDDungeonResult_Panel, Enum.ArenaSettleResult.WIN)
    end
    -- 恢复显示附近的NPC以及交换按钮
	Game.WorldManager:SetTaskNpcCategoryVisible(true)
    Game.HUDSystem:ShowUI("P_HUDInteract")
    self.model.IndividualPVPState = nil
    table.clear(self.model.reasonToReminderMap)
end

-- 切完场景类ui返回，重新刷一下ui跟特效
function IndividualPVPSystem:RefreshUI()
    if self.model.IndividualPVPState == pvpConst.INDIVIDUAL_PVP_STAGE.STAGE_PREPARE then
        self:LoadSceneEffect(self.model.vector.X, self.model.vector.Y, self.model.vector.Z)
        self:ShowIndividualPVPStartCountDown()
    elseif self.model.IndividualPVPState == pvpConst.INDIVIDUAL_PVP_STAGE.STAGE_BATTLE then
        self:LoadSceneEffect(self.model.vector.X, self.model.vector.Y, self.model.vector.Z)
        Game.HUDSystem:HideUI("P_HUDInteract")
        local startTime = self.model.battleStateStartTime or _G._now()
        Game.HUDSystem:ShowUI(UICellConfig.HUD1v1Countdown, startTime +  Game.TableData.Get1V1SettingDataRow("FightTime")*1000)
    end
end

-- 是否显示1v1倒计时
function IndividualPVPSystem:CanShow1v1Countdown()
    return self.model.IndividualPVPState == pvpConst.INDIVIDUAL_PVP_STAGE.STAGE_BATTLE
end

-- 是否处于切磋阶段
function IndividualPVPSystem:CheckIsInIndividualState()
    return self.model.IndividualPVPState ~= nil
end

return IndividualPVPSystem