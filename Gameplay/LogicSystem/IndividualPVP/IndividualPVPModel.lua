---@class IndividualPVPModel:SystemModelBase
local IndividualPVPModel = DefineClass("IndividualPVPModel",SystemModelBase)
local pvpConst = kg_require("Shared.Const.PVPConst")

function IndividualPVPModel:init()
    self.IndividualPVPState = nil
    self.prepareStateStartTime = nil
    self.battleStateStartTime = nil
    self.niagaraActor = nil
    self.vector = nil
    self.reasonToReminderMap = nil
    self.effectID = nil
end

function IndividualPVPModel:OnStateChange(newState)
    self.IndividualPVPState = newState
end

function IndividualPVPModel:InitReasonToReminderMap()
    if self.reasonToReminderMap == nil then
        local INDIVIDUAL_PVP_ERRCODE = pvpConst.INDIVIDUAL_PVP_ERRCODE
        self.reasonToReminderMap = {
            [INDIVIDUAL_PVP_ERRCODE.PVP_SEND_SUCC] = Enum.EReminderTextData.PVP_INVITE_SUCESS,
            [INDIVIDUAL_PVP_ERRCODE.PVP_TARGET_IN_FIGHT] = Enum.EReminderTextData.PVP_INVITE_FAILED_IN,
            [INDIVIDUAL_PVP_ERRCODE.PVP_INVITER_TOO_FAR] = Enum.EReminderTextData.PVP_ACCEPT_FAILED_OUT,
            [INDIVIDUAL_PVP_ERRCODE.PVP_TARGET_OFFLINE] = Enum.EReminderTextData.PVP_INVITE_FAILED_OFF,
            [INDIVIDUAL_PVP_ERRCODE.PVP_ALREADY_SEND] = Enum.EReminderTextData.PVP_INVITE_FAILED_REPEAT,
            [INDIVIDUAL_PVP_ERRCODE.PVP_TARGET_IN_CD] = Enum.EReminderTextData.PVP_INVITE_FAILED_FREQUENT,
            [INDIVIDUAL_PVP_ERRCODE.PVP_TARGET_RECV_LIST_FULL] = Enum.EReminderTextData.PVP_INVITE_FAILED_OVER,
            [INDIVIDUAL_PVP_ERRCODE.PVP_DISTANCE_TOO_FAR] = Enum.EReminderTextData.PVP_INVITE_FAILED_OUT,
            [INDIVIDUAL_PVP_ERRCODE.PVP_TARGET_NO_IN_SAFE] = Enum.EReminderTextData.PVP_INVITE_FAILED_UNSAFE,
            [INDIVIDUAL_PVP_ERRCODE.PVP_BAN] = Enum.EReminderTextData.INDIVIDUAL_PVP_OVER,
            [INDIVIDUAL_PVP_ERRCODE.PVP_INVITE_INVITER_DEAD] = Enum.EReminderTextData.PVP_INVITE_FAILED_REVIVE,
            [INDIVIDUAL_PVP_ERRCODE.PVP_INVITE_TARGET_DEAD] = Enum.EReminderTextData.PVP_INVITE_FAILED_DEATH,
            [INDIVIDUAL_PVP_ERRCODE.PVP_ACCEPT_INVITER_DEAD] = Enum.EReminderTextData.PVP_ACCEPT_FAILED_DEATH,
            [INDIVIDUAL_PVP_ERRCODE.PVP_INVITER_OFFLINE] = Enum.EReminderTextData.PVP_ACCEPT_FAILED_OFF,
            [INDIVIDUAL_PVP_ERRCODE.PVP_SEND_LIST_FULL] = Enum.EReminderTextData.PVP_INVITE_EXCESS_FAILED,
            [INDIVIDUAL_PVP_ERRCODE.PVP_INVITER_IN_FIGHT] = Enum.EReminderTextData.PVP_ACCEPT_FAILED_IN,
            [INDIVIDUAL_PVP_ERRCODE.PVP_FAILED] = Enum.EReminderTextData.PVP_FAILED,
            -- [INDIVIDUAL_PVP_ERRCODE.PVP_INVITER_NO_IN_SAFE] = Enum.EReminderTextData.PVP_FAILED_INVITE,
            [INDIVIDUAL_PVP_ERRCODE.PVP_FAILED_TARGET_IN_FIGHT] = Enum.EReminderTextData.PVP_FAILED_TARGET_IN_FIGHT,
            [INDIVIDUAL_PVP_ERRCODE.PVP_ACCEPT_TARGET_DEAD] = Enum.EReminderTextData.PVP_ACCEPT_TARGET_DEAD,
            [INDIVIDUAL_PVP_ERRCODE.PVP_CANNOT_BATTLE_WITH_SELF] = Enum.EReminderTextData.PVP_CANNOT_BATTLE_WITH_SELF,
            [INDIVIDUAL_PVP_ERRCODE.PVP_TARGET_IS_REDNAME] = Enum.EReminderTextData.PVP_FAILED_INVITE_CRAZY,
            [INDIVIDUAL_PVP_ERRCODE.PVP_SELF_IS_REDNAME] = Enum.EReminderTextData.PVP_FAILED_CRAZY_INVITE,
            [INDIVIDUAL_PVP_ERRCODE.PVP_STATE_CONFLICT] = Enum.EReminderTextData.PVP_STATE_CONFLICT,
        }
    end
end

function IndividualPVPModel:unInit()

end

return IndividualPVPModel