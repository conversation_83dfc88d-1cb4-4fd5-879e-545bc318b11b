local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComInputBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComInputBox")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class MarqueeShout_Panel : UIPanel
---@field view MarqueeShout_PanelBlueprint
local MarqueeShout_Panel = DefineClass("MarqueeShout_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")

MarqueeShout_Panel.eventBindMap = {
    [EEventTypesV2.CHAT_SHOUT] = "OnShout",
    [EEventTypesV2.CHAT_SHOUT_INPUT] = "OnEmoInput",
}

-- 喇叭商品ID映射, Const表格中的ID
MarqueeShout_Panel.LOUD_SPEAKER_GOODS_ID_MAP = {
    [1] = "LOUD_SPEAKER_ITEM_ID1",
    [2] = "LOUD_SPEAKER_ITEM_ID2",
    [3] = "LOUD_SPEAKER_ITEM_ID3",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MarqueeShout_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MarqueeShout_Panel:InitUIData()
    ---@type table 喇叭数据
    -- self.ChatLoudSpeakerData = Game.TableData.GetChatLoudSpeakerDataTable()
    self.ChatLoudSpeakerData = nil
    ---@type number 选中页签
    self.TabSelectIndex = 1
end

--- UI组件初始化，此处为自动生成
function MarqueeShout_Panel:InitUIComponent()
    ---@type UIComBoxFrame
    self.WBP_FrameCom = self:CreateComponent(self.view.WBP_Frame, UIComBoxFrame)
    ---@type UIComButton
    self.SendBtnCom = self:CreateComponent(self.view.SendBtn, UIComButton)
    ---@type UIComInputBox
    self.InputTextCom = self:CreateComponent(self.view.InputText, UIComInputBox)
    ---@type UIListView
    self.KGListView_CostCom = self:CreateComponent(self.view.KGListView_Cost, UIListView)
end

---UI事件在这里注册，此处为自动生成
function MarqueeShout_Panel:InitUIEvent()
    self:AddUIEvent(self.InputTextCom.onLimitOver, "on_InputTextCom_LimitOver")
    self:AddUIEvent(self.KGListView_CostCom.onItemSelectionChanged, "on_KGListView_CostCom_ItemSelectionChanged")
    self:AddUIEvent(self.SendBtnCom.onClickEvent, "on_SendBtnCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MarqueeShout_Panel:InitUIView()
    self.WBP_FrameCom:Refresh(StringConst.Get("SOCIAL_CHAT_SPEAKER"))
    self.InputTextCom:Refresh("", "", Game.TableData.GetConstDataRow("CHAT_PLAYER_CONTENT_LIMIT"))
    self.SendBtnCom:SetName(StringConst.Get("SEND"))
end

---面板打开的时候触发
function MarqueeShout_Panel:OnRefresh(...)
    self:InitChatLoudSpeakerData()
    self.KGListView_CostCom:Refresh(self.ChatLoudSpeakerData)
    self:RefreshUI(1)
    self:StartTimer("MarqueeShout_Panel", function() self.KGListView_CostCom:SetSelectedItemByIndex(1, true) end, 10, 1)

end

-- 初始化喇叭数据
function MarqueeShout_Panel:InitChatLoudSpeakerData()
    self.ChatLoudSpeakerData = self.ChatLoudSpeakerData or {}
    for i,v in ksbcpairs(MarqueeShout_Panel.LOUD_SPEAKER_GOODS_ID_MAP) do
        local itemData = Game.TableData.GetMallGoodsDataRow(Game.TableData.GetChatConstDataRow(v))
        if itemData then
            self.ChatLoudSpeakerData[i] = itemData
        end
    end
end

function MarqueeShout_Panel:RefreshUI(index)
    self.TabSelectIndex = index
    local speakerData = self.ChatLoudSpeakerData[index]
    local itemID = speakerData["ItemID"]
    self.SpeakerID = index
    local itemData = Game.TableData.GetItemNewDataRow(itemID)
    local quality = itemData.quality
    self.view.Text_ItemName:SetText(string.format("<Quality_%d>%s</>",quality, Game.TableData.GetItemRMBDataRow(speakerData.ItemID).itemName))
    self.view.WBP_ChatShoutItem:setbrush(index - 1)
end

function MarqueeShout_Panel:OnShout()
    self:RefreshUI(self.TabSelectIndex)
end

function MarqueeShout_Panel:ProcessText()
    local matchString = {}
    local newString = self.InputTextCom:GetText()
    for s in string.gmatch(self.InputTextCom:GetText(), "#%d%d%d") do
        if not table.contains(matchString, s) then
            table.insert(matchString, s)
        end
    end
    for i = 1, #matchString do
        if Game.ChatSystem.EmoData[matchString[i]] then
            newString = string.gsub(newString, matchString[i],
                string.format("<img id=\"%s\" width=\"42\" height=\"42\"/>", matchString[i]))
        end
    end
    return newString
end

function MarqueeShout_Panel:OnEmoInput(emoStr)
    if not self:CheckInputLimit(emoStr) then
        local text = self.InputTextCom:GetText() .. emoStr
        self.InputTextCom:SetText(text)
        self.InputTextCom:on_EditText_Input_TextChanged()
    end
end

function MarqueeShout_Panel:CheckInputLimit(addvalue)
    local isOverLimit = false
    if addvalue then
        local value = self.InputTextCom:GetText() .. addvalue
        local num = ChatUtils.CountChars(value)
        isOverLimit = num > Game.TableData.GetConstDataRow("CHAT_PLAYER_CONTENT_LIMIT")
    else
        local value = self.InputTextCom:GetText()
        local num = ChatUtils.CountChars(value)
        isOverLimit = num > Game.TableData.GetConstDataRow("CHAT_PLAYER_CONTENT_LIMIT")
    end
    if isOverLimit then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_INPUT_MAX)
    end
    return isOverLimit
end

--- 此处为自动生成
---@param index number
---@param selected bool
function MarqueeShout_Panel:on_KGListView_CostCom_ItemSelectionChanged(index, selected)
    self.KGListView_CostCom:GetItemByIndex(index):OnChangeCheckBox(selected)
    self:RefreshUI(index)
end


--- 此处为自动生成
function MarqueeShout_Panel:on_SendBtnCom_ClickEvent()
    if self.InputTextCom:GetText() == "" then
        return
    end
    local speakerData = self.ChatLoudSpeakerData[self.TabSelectIndex]
    local itemID = speakerData["ItemID"]
    local Count = Game.BagSystem:GetItemCount(itemID)
    local moneyID
    local moneyCount
    for i,v in ksbcpairs(speakerData.TokenIDs) do
        moneyID = i
        moneyCount = v
        break
    end
    if Count == 0 then
        --if self.AutoUse then
        if Game.CurrencySystem:GetMoneyByType(moneyID) < moneyCount then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.INV_MONEY_NOT_ENOUGH)
            return
        end
        -- else
        --     Game.ReminderManager:AddReminderById(Enum.EReminderTextData.INV_ITEM_NOT_ENOUGH)
        --     return
        -- end
    end
    local acousticInfo = { loudSpeakerId = self.SpeakerID }
    local messageInfo = {
        channelType = Enum.EChatChannelData["WORLD"],
        functionType = Enum.ChatFunctionType.LOUD_SPEAKER,
        messageType = Enum.EChatMessageType.TEXT
    }
    messageInfo.messageText = self:ProcessText()
    messageInfo.chatArgs = { loudSpeakerInfo = acousticInfo }
    Game.ChatSystem:loudSpeaker(messageInfo)
    self.InputTextCom:on_Btn_Clear_Clicked()
end

--- 此处为自动生成
function MarqueeShout_Panel:on_Btn_ClickArea_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.ChatExpression_Panel, true)
end


--- 此处为自动生成
function MarqueeShout_Panel:on_InputTextCom_LimitOver()
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_INPUT_MAX)
end

return MarqueeShout_Panel
