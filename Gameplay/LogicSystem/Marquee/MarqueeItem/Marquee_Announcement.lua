local ChatResource = kg_require("Gameplay.LogicSystem.Chat.ChatResource")
local MarqueeItemBase = kg_require("Gameplay.LogicSystem.Marquee.MarqueeItem.MarqueeItemBase")
---@class Marquee_Announcement : UIComponent
---@field view Marquee_AnnouncementBlueprint
local Marquee_Announcement = DefineClass("Marquee_Announcement", MarqueeItemBase)

Marquee_Announcement.eventBindMap = {
}

function Marquee_Announcement:InitUIComponent()
    ---@type ChatResource
    self.ChatFlag = self:CreateComponent(self.view.AnnouncementFlag, ChatResource)
end

function Marquee_Announcement:GetAdjustPosBaseWidget()
    return self.view.Announcement_Content
end

function Marquee_Announcement:GetCalculateSizeWidgets()
    return {self.view.ClipCanvas, self.view.AnnouncementFlag.Text_Channel}
end

function Marquee_Announcement:RefreshUI()
    self:SetTextContent()
    local info = self.marqueeInfo.extraData
    if info and info.channelType then
        self.ChatFlag:Refresh(info.channelType)
    end
end

return Marquee_Announcement
