local BaseCheck =  kg_require("Gameplay.LogicSystem.NewHeadInfo.HeadInfoCheck.BaseCheck")
---@class HeadInfoTeamSigCheck
local HeadInfoTeamSigCheck = DefineClass("HeadInfoTeamSigCheck", BaseCheck)

HeadInfoTeamSigCheck.CheckFuncName =
{
	"CheckHiddenByNotInTeamOrNotInGroupOrNotMark",
}

function HeadInfoTeamSigCheck:CheckHiddenByNotInTeamOrNotInGroupOrNotMark(entity, actorType)
	-- 自己在组队或者团队，同时目标也在，并且目标还被标记了，就能看到目标的头顶信息
	if Game.TeamSystem:IsInTeam() and Game.TeamSystem:IsTeamMember(entity.eid) and Game.TeamSystem:IsTeamMemberInMark(entity.eid) then
		return false
	elseif Game.TeamSystem:IsInGroup() and Game.GroupSystem:IsMyGroupMember(entity.eid) and Game.GroupSystem:IsGroupMemberInMark(entity.eid) then
		return false
	end
	return true
end
