local BaseCheck =  kg_require("Gameplay.LogicSystem.NewHeadInfo.HeadInfoCheck.BaseCheck")
---@class HeadInfoPVPHonorCheck
local HeadInfoPVPHonorCheck = DefineClass("HeadInfoPVPHonorCheck", BaseCheck)

--- 返回true表示要隐藏
HeadInfoPVPHonorCheck.CheckFuncName = 
{
	"CheckHiddenByPVPHonor",	-- 检查是否有荣誉称号
	"CheckHiddenByArena",	-- 检查是否竞技场中
}

function HeadInfoPVPHonorCheck:CheckHiddenByPVPHonor(entity, actorType)
	local honorId = Game.TeamAreanaSystem:GetMemberHonor(entity.eid)
	if not honorId or honorId == 0 then
		return true
	end
	return false
end

function HeadInfoPVPHonorCheck:CheckHiddenByArena(entity, actorType)
	if not Game.TeamAreanaSystem:IsInTeamArena() then
		return true
	end
	return false
end