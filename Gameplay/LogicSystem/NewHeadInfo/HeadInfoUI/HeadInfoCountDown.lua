local HeadInfoUIBase = kg_require("Gameplay.LogicSystem.NewHeadInfo.HeadInfoUI.HeadInfoUIBase")
local ESlateVisibility = import("ESlateVisibility")
---@class HeadInfoCountDown : HeadInfoUIBase
---@field view HeadNameBlueprint
local HeadInfoCountDown = DefineClass("HeadInfoCountDown", HeadInfoUIBase)

---组件刷新统一入口
function HeadInfoCountDown:Refresh(configId)
	if configId then
		self:RefreshWidgetView(configId)
	end
end

local CachedVector2f = import("Vector2f")()
function  HeadInfoCountDown:RefreshWidgetView(configId)
	local OverHeadCountDownDisplayDataRow = Game.TableData.GetOverHeadCountDownDisplayDataRow(configId)
	self.view.KRichT_Info_lua:SetText(OverHeadCountDownDisplayDataRow.DescText)

	if self.TimerHandle then
		self:StopTimer(self.TimerHandle)
		self.TimerHandle = nil
	end
	local initialTime = _G._now()
	local maxDelay = OverHeadCountDownDisplayDataRow.CountDownTime
	self.TimerHandle = self:StartTimer("CountDownTimer", function()
		local currentTime = _G._now()
		local delay = currentTime - initialTime
		if  delay >= maxDelay then
			self:StopTimer(self.TimerHandle)
			self.TimerHandle = nil
			return
		end
		local percent = 1 - delay / maxDelay
		self.view.WBP_HeadInfo_Pve_Bar_lua:Event_UI_State(percent * 100)
		CachedVector2f.X = percent
		CachedVector2f.Y = 1
		self.view.WBP_HeadInfo_Pve_Bar_lua.bg_bar_lua:SetSpriteShowPercent(CachedVector2f)
	end, 100, -1)
end

return HeadInfoCountDown