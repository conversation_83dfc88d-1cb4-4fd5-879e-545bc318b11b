local HeadInfoUIBase = kg_require("Gameplay.LogicSystem.NewHeadInfo.HeadInfoUI.HeadInfoUIBase")
local ESlateVisibility = import("ESlateVisibility")
local Redirection = kg_require("Data.Config.UI.RedirectTextureConfig")
---@class HeadInfoGuildIcon : HeadInfoUIBase
---@field view HeadInfoGuildIconBlueprint
local HeadInfoGuildIcon = DefineClass("HeadInfoGuildIcon", HeadInfoUIBase)

---组件刷新统一入口
function HeadInfoGuildIcon:Refresh()
    self:RefreshNameText()
end

function HeadInfoGuildIcon:RefreshNameText()
    local RpcEntity = self:GetAttachedEntity()
    local badgeText = Game.GuildSystem:GetHeadInfoGuildIconText(RpcEntity.guildName, RpcEntity.guildBadgeIndex)
    self:RefreshData(badgeText, RpcEntity.guildBadgeFrameId)
    self.view.Ov_Icon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function HeadInfoGuildIcon:RefreshData(text, bgId)
    local guildBadgeFrameData = Game.TableData.GetGuildBadgeFrameDataRow(bgId or 1001)
    local iconType = guildBadgeFrameData.IconType
    local icon = guildBadgeFrameData.WorldIcon
    local path = Redirection.Get(icon)
    self:SetImage(self.view.Img_BgIcon, path)

    if iconType == Enum.EGuildConstIntData.BADGE_TYPE_ICON then
        self.view.Text_Title:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Img_Icon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Img_BgIcon:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.Text_Title:SetText(text or "")
        self.view.Text_Title:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Img_Icon:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return HeadInfoGuildIcon