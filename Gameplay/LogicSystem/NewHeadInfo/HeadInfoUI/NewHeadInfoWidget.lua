local NewHeadInfoConst = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoConst")
local WorldWidgetCellBase2 = kg_require ("Gameplay.LogicSystem.WorldWidget2.WorldWidgetCellBase2")
local EHorizontalAlignment = import("EHorizontalAlignment")
local EVerticalAlignment = import("EVerticalAlignment")
---@class NewHeadInfoWidget:WorldWidgetCellBase2
local NewHeadInfoWidget = DefineClass("NewHeadInfoWidget", WorldWidgetCellBase2)

function NewHeadInfoWidget:ctor()
    self.WWID = nil       -- 对应C++对象的id
    self.entityID = nil   -- entityId
    self.actorType = nil  -- actorType
    self.newHeadInfo = nil -- headinfo
    ---@type table<number,HeadInfoUIBase>
    self.children = {}    -- 头顶子节点
    self.bIsHidden = nil   --是否显示
    self.controlSource = nil     --状态来源
end

function NewHeadInfoWidget:InitData(entityID, actorType, newHeadInfo)
    self.newHeadInfo = newHeadInfo
    self.entityID = entityID
    self.actorType = actorType
    Game.WorldWidgetManager2:BindWorldWidgetEvent(Enum.WorldWidgetEventTypes.OnHiddenDistance, self.WWID, "OnHiddenDistance")
end

function NewHeadInfoWidget:UninitData()
    self.entityID = nil
    self.actorType = nil
    table.clear(self.children)
    Game.WorldWidgetManager2:UnbindWorldWidgetEvent(Enum.WorldWidgetEventTypes.OnHiddenDistance, self.WWID)
end

function NewHeadInfoWidget:SetHeadInfoStatus(bIsHidden, controlSource)
    self.bIsHidden = bIsHidden
    self.controlSource = controlSource
    if bIsHidden then
        self:Hide()
    else
        self:Show()
        self:updateChildren()
    end
end

function NewHeadInfoWidget:SetHeadNodeStatus(nodeType, bIsHidden, source)
    local cell = self.children[nodeType]
    if not bIsHidden and not cell then
        local config = NewHeadInfoConst.HeadInfoNodeConfig[nodeType]
        local cellId = config.cellId
        cell = Game.NewUIManager:InvokePanel(UIPanelConfig.WorldWidgetPanel, "CreateHeadInfoComponent", cellId)
        self.children[nodeType] = cell
        cell:SetEntityId(self.entityID)
        self.view[config.UIRoot]:AddChild(cell.widget)
        if cell.widget.Slot then
            cell.widget.Slot.HorizontalAlignment = EHorizontalAlignment.HAlign_Center
            cell.widget.Slot.VerticalAlignment = EVerticalAlignment.VAlign_Bottom
        end
    end
    if cell then
        cell:SetHeadInfoStatus(bIsHidden, source)
    end
end

function NewHeadInfoWidget:updateChildren()
    local headConfig = NewHeadInfoConst.HeadInfoConfig[self.actorType]
    for _, nodeType in ipairs(headConfig) do
        self:updateHeadInfoNode(nodeType)
    end
end

function NewHeadInfoWidget:updateHeadInfoNode(nodeType)
    local bIsHidden, source = Game.NewHeadInfoSystem:CheckHeadInfoNodeIsHidden(self.entityID, self.actorType, nodeType)
    self:SetHeadNodeStatus(nodeType, bIsHidden, source)
end

function NewHeadInfoWidget:ReleaseChildren()
    for _, value in pairs(self.children) do
        Game.NewUIManager:InvokePanel(UIPanelConfig.WorldWidgetPanel, "ReleaseHeadInfoComponent", value)
    end
    self:UninitData()
end

function NewHeadInfoWidget:InvokeNodeFunc(nodeType, funcName, ...)
    local child = self.children[nodeType]
    if child then
        child[funcName](child, ...)
    end
end
function NewHeadInfoWidget:OnHiddenDistance(ID, bIsHidden)
    self.newHeadInfo:SetHeadInfoHidden(bIsHidden, NewHeadInfoConst.HeadInfoHiddenFlag.Distance)
end
return  NewHeadInfoWidget