local ESlateVisibility = import("ESlateVisibility")
local HeadInfoUIBase = kg_require("Gameplay.LogicSystem.NewHeadInfo.HeadInfoUI.HeadInfoUIBase")

---@class HeadInfoIcon : HeadInfoUIBase
---@field view HeadIconBlueprint
local HeadInfoIcon = DefineClass("HeadInfoIcon", HeadInfoUIBase)

---组件刷新统一入口
function HeadInfoIcon:Refresh()
	local entity = self:GetAttachedEntity()
	if entity.isNpc then
		self:CheckActorUseIcon()
	else
		self:CheckSceneActorUseIcon()
	end
end

function HeadInfoIcon:CheckActorUseIcon()
	local RpcEntity = self:GetAttachedEntity()
	local TemplateID = RpcEntity.TemplateID
	local IconPath, BGPath,Color1,Color2 = Game.NPCSystem:GetNpcIcon(TemplateID)
	-- 任务追踪npc的ui表现
	local bTraced, ringID = Game.TraceSystem:IsTracedNPC(TemplateID)
	if bTraced then
		IconPath, BGPath,Color1,Color2 = Game.QuestSystem:GetNPCTaskIconAndColor(ringID, Game.ColorManager.Type.LinearColor)
	end
	if IconPath then
		self:SetImage(self.view.Img_NPCTitle_lua, IconPath)
		if BGPath then
			self.view.Img_NPCTitleOut_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self:SetImage(self.view.Img_NPCTitleOut_lua, BGPath)
		else
			self.view.Img_NPCTitleOut_lua:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
	if Color1 then
		self.view.Img_NPCTitle_lua:SetColorAndOpacity(Color1)
	end
	if Color2 then
		self.view.Img_NPCTitleOut_lua:SetColorAndOpacity(Color2)
	end
end

function HeadInfoIcon:CheckSceneActorUseIcon()
	--目前只有公会祈福有图标，以后再根据不同交互物配置
	local Color = Game.ColorManager:GetColor("Common", "Fri_Lightyellow", Game.ColorManager.Type.SlateColor)
	self.view.Img_NPCTitle_lua:SetColorAndOpacity(Color)
end

return HeadInfoIcon
