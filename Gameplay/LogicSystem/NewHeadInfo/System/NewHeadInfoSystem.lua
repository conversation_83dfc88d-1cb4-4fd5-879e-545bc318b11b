local NewHeadInfoModel = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoModel")
local NewHeadInfoSender = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoSender")
local NewHeadInfoConst = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoConst")
local NewHeadInfo = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfo")
local HeadInfoActorType = NewHeadInfoConst.HeadInfoActorType
---@class NewHeadInfoSystem:SystemBase
---@field model NewHeadInfoModel
---@field sender NewHeadInfoSender
local NewHeadInfoSystem = DefineClass("NewHeadInfoSystem", SystemBase)

function NewHeadInfoSystem:onInit()
	self.model = NewHeadInfoModel.new()
	self.sender = NewHeadInfoSender.new()
	self.headTriggers = {}					    --头顶信息触发器
	---@type NewHeadInfo[]
	self.headInfoPool = {}                      --头顶信息缓存池
	---@type table<number,NewHeadInfo>
	self.headInfoMap = {}                       --头顶信息容器
	self:initHeadNodeTrigger()
end

function NewHeadInfoSystem:onUnInit()

end

function NewHeadInfoSystem:RegisterHeadInfo(entityID)
	if not _G.bUseNewHeadInfo then
		return
	end
	local actorType = self:GetHeadInfoActorType(entityID)
	if self:CheckForbidHeadInfo(entityID, actorType) then
		return
	end
	local head = self:getFreeHeadInfo()
	head:InitData(entityID, actorType)
	self.headInfoMap[entityID] = head
	self:UpdateHeadInfo(entityID)
end

function NewHeadInfoSystem:UnRegisterHeadInfo(entityID)
	if not _G.bUseNewHeadInfo then
		return
	end
	local headInfo = self.headInfoMap[entityID]
	self.headInfoMap[entityID] = nil
	headInfo:ReleaseChildren()
	table.insert(self.headInfoPool, headInfo)
end

---更新头顶信息全部节点信息
---@param entityID number uid
function NewHeadInfoSystem:UpdateHeadInfo(entityID)
	if not _G.bUseNewHeadInfo then
		return
	end
	local headInfo = self.headInfoMap[entityID]
	if headInfo then
		headInfo:UpdateHeadInfo()
	else
        Log.DebugErrorFormat("[NewHeadInfoSystem :] UpdateHeadInfo headInfo is nil entityID = %s", entityID)
	end
end

---更新头顶单个节点信息
function NewHeadInfoSystem:UpdateHeadInfoNode(entityID, nodeType)
	if not _G.bUseNewHeadInfo then
		return
	end
	local headInfo = self.headInfoMap[entityID]
	headInfo:UpdateHeadInfoNode(nodeType)
end

--region 内部实现外部不要访问
function NewHeadInfoSystem:initHeadNodeTrigger()
	for k, filePath in ipairs(NewHeadInfoConst.HeadInfoTrigger) do
		self.headTriggers[k] = kg_require(filePath).new()
	end
end

---@return NewHeadInfo
function NewHeadInfoSystem:getFreeHeadInfo()
	if #self.headInfoPool > 0 then
		local head = self.headInfoPool[#self.headInfoPool]
		table.remove(self.headInfoPool, #self.headInfoPool)
		return head
	end
	return NewHeadInfo.new()
end

function NewHeadInfoSystem:GetHeadInfoActorType(entityID)
	local Entity = Game.EntityManager:getEntityWithBrief(entityID)
	if Entity.isAvatar then
		return HeadInfoActorType.Player
	elseif Entity.isNpc then
		if Entity.BossType > Enum.EBossType.NoMonster then
			return HeadInfoActorType.NPC_Monster
		else
			return HeadInfoActorType.NPC_Neutral
		end
	elseif Entity.bIsCommonInteractor then
		return HeadInfoActorType.CommonInteractor
	else
		return HeadInfoActorType.Other
	end
end

function NewHeadInfoSystem:CheckHeadInfoNodeVisible(entityID, actorType, NodeType)
	return self.headTriggers[NodeType]:CheckTrigger(entityID, actorType)
end

function NewHeadInfoSystem:CheckHeadInfoVisible(entityID, actorType)
end

function NewHeadInfoSystem:CheckForbidHeadInfo(entityID, actorType)
end
--endregion
return NewHeadInfoSystem

