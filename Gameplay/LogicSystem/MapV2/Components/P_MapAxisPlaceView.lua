---@class WBP_MapAxisPlaceView : WBP_MapAxisPlace_C
---@field public WidgetRoot WBP_MapAxisPlace_C
---@field public Text_TagContent TextBlock
---@field public FetchTextWidget fun(self:self):Widget


---@class P_MapAxisPlaceView : WBP_MapAxisPlaceView
---@field public controller P_MapAxisPlace
local P_MapAxisPlaceView = DefineClass("P_MapAxisPlaceView", UIView)

function P_MapAxisPlaceView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

end

function P_MapAxisPlaceView:OnDestroy()
---DeletePlaceHolder
end

return P_MapAxisPlaceView
