---@class WBP_MapTracingView : WBP_MapTracing_C
---@field public WidgetRoot WBP_MapTracing_C
---@field public Ani_Loop WidgetAnimation


---@class P_MapAxisTracingView : WBP_MapTracingView
---@field public controller P_MapAxisTracing
local P_MapAxisTracingView = DefineClass("P_MapAxisTracingView", UIView)

function P_MapAxisTracingView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

function P_MapAxisTracingView:OnDestroy()
end

return P_MapAxisTracingView
