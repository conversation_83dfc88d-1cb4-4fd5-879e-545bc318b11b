kg_require("Gameplay.LogicSystem.MapV2.Components.P_WonderFlowerTagView")
local ESlateVisibility = import("ESlateVisibility")
-- local KismetMathLibrary = import("KismetMathLibrary")
local RAVING_FLOWER_SUBSTATE = kg_require("Shared.Const").RAVING_FLOWER_SUBSTATE

---@class P_WonderFlowerTag : UIComponent
---@field public View WBP_FlowersIconSubView
local P_WonderFlowerTag = DefineClass("P_WonderFlowerTag", UIComponent)

P_WonderFlowerTag.eventBindMap = {
    [EEventTypesV2.WONDER_FLOWER_SUB_STATE_UPDATE] = "UpdateFlowerState",
}

-- 交互物SubState对应的UI状态
P_WonderFlowerTag.UIStateMap = {
    [RAVING_FLOWER_SUBSTATE.None] = 0,
    [RAVING_FLOWER_SUBSTATE.Factory] = 1,
    [RAVING_FLOWER_SUBSTATE.Sycamore] = 2,
    [RAVING_FLOWER_SUBSTATE.Sewer] = 3,
    [RAVING_FLOWER_SUBSTATE.Factory | RAVING_FLOWER_SUBSTATE.Sycamore] = 4,
    [RAVING_FLOWER_SUBSTATE.Factory | RAVING_FLOWER_SUBSTATE.Sewer] = 5,
    [RAVING_FLOWER_SUBSTATE.Sycamore | RAVING_FLOWER_SUBSTATE.Sewer] = 6,
    [RAVING_FLOWER_SUBSTATE.Factory | RAVING_FLOWER_SUBSTATE.Sycamore | RAVING_FLOWER_SUBSTATE.Sewer] = 7,
}

function P_WonderFlowerTag:OnCreate()
    -- 连线
    self.lineWidgetList = {
        self.View.WBP_FlowersEffect_center,
        self.View.WBP_FlowersEffect_left,
        self.View.WBP_FlowersEffect_right,
    }
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClick_Btn_ClickArea)
end

function P_WonderFlowerTag:SetScale(tagKind)
    local TypeData = Game.TableData.GetTagTypeConfigDataRow(tagKind)
    local scale = TypeData.MinimapTagScale
    if scale then
        self.View.RootPanel:SetRenderScale(FVector2D(scale, scale))
    end
end

function P_WonderFlowerTag:OnRefresh(TaskID, bSelect, bInEdge)
    self.TaskID = TaskID
    self:UpdateFlowerState()
    self:OnSelectionStateChanged(bSelect)
end

function P_WonderFlowerTag:UpdateFlowerState()
    local Data = Game.TableData.GetMapTagDataRow(tonumber(self.TaskID))--最基本的情况
    local InsID = Data.LinkInsID
    local wonderInfo = Game.WorldDataManager:GetCurLevelSceneActorData(InsID)
    if wonderInfo then
        -- local selfPos = self.parent:GetWidgetPosByTaskID(TaskID)
        local subState = 0
        for idx, eventID in ipairs(wonderInfo.EventIDList) do
            -- local node = self.lineWidgetList[idx]
            -- local actData = Game.TableData.GetWorldActivityDataRow(eventID)
            -- local tagID = tostring(actData.MapTag_ID)
            -- -- 更新连线角度和长度
            -- if self.parent:GetComponentByTaskID(tagID) then
            --     local targetPos = self.parent:GetWidgetPosByTaskID(tagID)
            --     local direction = targetPos - selfPos
            --     local angle = KismetMathLibrary.Atan2(direction.Y, direction.X)
            --     local degree = KismetMathLibrary.RadiansToDegrees(angle)
            --     node:SetRenderTransformAngle(-180 + degree)
            --     local oriSize = node.Slot:GetSize()
            --     node.Slot:SetSize(FVector2D(oriSize.X, direction:Size()))
            -- end
            -- 获取花的状态
            if Game.WorldActivitySystem:IsWorldActivityDone(eventID) then
                subState = subState | (1 << (idx - 1))
            end
        end
        local uiState = P_WonderFlowerTag.UIStateMap[subState]
        if uiState then
            self.View:Event_UI_Style(uiState)
        end
    end
end

function P_WonderFlowerTag:OnClick_Btn_ClickArea()
    if self.TaskID then
        local OutTasks = self.parent:ReqTaskNearByByTaskID(self.TaskID, 0.05)
        if #OutTasks > 1 then
            UI.Invoke("P_MapV2", "ReqOpenTaskList", OutTasks)
        else
            UI.Invoke("P_MapV2", "ReqOpenHalfScreen", self.TaskID)
        end
        if self.bInEdge then
            local MapUI = UI.GetUI("P_MapV2")
            if MapUI then MapUI:MoveMapCenterToTask(self.TaskID, true) end
        end
    end
    self.parent:RemoveTempCustomSelection()
    self.parent:NotifySelection(self.TaskID)
end

function P_WonderFlowerTag:OnSelectionStateChanged(bSelect)
    if bSelect then
        self.View.WBP_MapAxisSelection:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.View.WBP_MapAxisSelection:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_WonderFlowerTag:OnSetCustomTrace(bTrace)
    -- if bTrace then
    --     self:PlayAnimation(self.View.WBP_MapTracing, self.View.WBP_MapTracing.Ani_Loop, 0, 0, EUMGSequencePlayMode.Forward)
    -- else
    --     self:StopAllAnimations(self.View.WBP_MapTracing)
    -- end
end

return P_WonderFlowerTag
