---大地图上的任务点，从P_MapReceiveTask文件移动过来，接入完成后，删除对应的旧文件
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Map_TaskPoint : UIComponent
---@field view Map_TaskPointBlueprint
local Map_TaskPoint = DefineClass("Map_TaskPoint", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")

Map_TaskPoint.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Map_TaskPoint:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Map_TaskPoint:InitUIData()
	self.TaskID = nil
end

--- UI组件初始化，此处为自动生成
function Map_TaskPoint:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Map_TaskPoint:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Map_TaskPoint:InitUIView()
end

---组件刷新统一入口
function Map_TaskPoint:Refresh()
end

function Map_TaskPoint:OnRefresh(TaskID, bSelect, bInEdge)
	self.TaskID = TaskID
	local splited = string.split(TaskID, "_")
	local taskRingID = tonumber(splited[2])
	-- Log.Warning("P_MapReceiveTask OnRefresh, TaskID ", TaskID, " taskRingID ", taskRingID)
	local TypeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.TASK_WAITING_TAKE)
	local scale = TypeData.MinimapTagScale
	if scale and self:GetParent().bMiniMap then
		self.userWidget:SetRenderScale(FVector2D(scale, scale))
	else
		self.userWidget:SetRenderScale(FVector2D(1, 1))
	end
	---这里改成直接设置图片了，不需要设置颜色
	self:setImgIcon(taskRingID)
	self:checkInEdge(bInEdge)
	self:tryPlayTracingAnim()
end

function Map_TaskPoint:setImgIcon(taskRingID)
	local iconPath = Game.QuestSystem:GetTaskRingIcon(taskRingID, true)
	self:SetImage(self.view.Img_Icon, iconPath)
end

function Map_TaskPoint:checkInEdge(bInEdge)
	if bInEdge then
		self.view.WBP_MapEdgeWrap:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.WBP_MapEdgeWrap:SetVisibility(ESlateVisibility.Hidden)
	end
end

function Map_TaskPoint:tryPlayTracingAnim(taskRingID)
	if QuestUtils.CheckTracingRingID(taskRingID) then
		local tracingWidget = self.view.WBP_MapTracing
		self:PlayAnimation(tracingWidget.Ani_Loop, nil, tracingWidget, 0, 0)	
	end
end

--- 此处为自动生成
function Map_TaskPoint:on_Btn_ClickArea_Clicked()
end

return Map_TaskPoint
