---@class WBP_MapMarkView : WBP_MapMark_C
---@field public WidgetRoot WBP_MapMark_C
---@field public Btn_ClickArea KGButton

---@class P_MapMarkView : WBP_MapMarkView
---@field public controller P_MapMark
local P_MapMarkView = DefineClass("P_MapMarkView", UIView)

function P_MapMarkView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_ClickArea, "OnClick_Btn_ClickArea")
end

function P_MapMarkView:OnDestroy()
end

return P_MapMarkView
