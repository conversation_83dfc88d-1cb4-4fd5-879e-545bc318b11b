kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisTaskTraceCircleView")

---@class P_MapAxisTaskTraceCircle : UIComponent
---@field public View WBP_MapAxisTaskTraceCircleView
local P_MapAxisTaskTraceCircle = DefineClass("P_MapAxisTaskTraceCircle", UIComponent)

function P_MapAxisTaskTraceCircle:OnCreate()
    
end

function P_MapAxisTaskTraceCircle:OnRefresh()
    local TypeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.TASK_TRACE_CIRCLE)
    local scale = TypeData.MinimapTagScale
    if scale and self.parent.bMiniMap then
        self.View.RootPanel:SetRenderScale(FVector2D(scale, scale))
    else
        self.View.RootPanel:SetRenderScale(FVector2D(1, 1))
    end
    
    self:PlayAnimation(
        self.View.WidgetRoot, self.View.An_Track, 0, 0, UE.EUMGSequencePlayMode.Forward
        )
end


function P_MapAxisTaskTraceCircle:OnHide()

end



return P_MapAxisTaskTraceCircle