---@class WBP_MapAxisPlayeTeamMateView : WBP_MapAxisPlayeTeamMate_C
---@field public WidgetRoot WBP_MapAxisPlayeTeamMate_C
---@field public Img_Content C7Image
---@field public FetchRotateWidget fun(self:self):Widget
---@field public FetchIconWidget fun(self:self):Widget


---@class P_MapAxisPlayeTeamMateView : WBP_MapAxisPlayeTeamMateView
---@field public controller P_MapAxisPlayeTeamMate
local P_MapAxisPlayeTeamMateView = DefineClass("P_MapAxisPlayeTeamMateView", UIView)

function P_MapAxisPlayeTeamMateView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

end

function P_MapAxisPlayeTeamMateView:OnDestroy()
---DeletePlaceHolder
end

return P_MapAxisPlayeTeamMateView
