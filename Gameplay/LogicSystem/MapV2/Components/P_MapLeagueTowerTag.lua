--local worldConst = kg_require "Shared.Const.WorldConst"
--local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

---@class P_MapLeagueTowerTag : UIComponent
local P_MapLeagueTowerTag = DefineClass("P_MapLeagueTowerTag", UIComponent)

P_MapLeagueTowerTag.eventBindMap = {
    --[EEventTypesV2.ON_MSG_GUILD_LEAGUE_TOWER_ATTACKED_BY_AREA] = "OnAttackedByArea",
    --[EEventTypesV2.ON_GUILD_LEAGUE_LOCKING_TOWER_IDS] = "OnLockByAltar",
	[EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_SELECTED] = "OnStrategySelected",
	[EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_UNSELECTED] = "OnStrategyUnSelected",
}

function P_MapLeagueTowerTag:OnCreate()
    self.taskID = nil
    self.instanceId = nil
	self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea_lua, "OnClick_Btn_ClickArea")
end

function P_MapLeagueTowerTag:OnRefresh(TaskID, bSelect, bInEdge)
    self.taskID = TaskID
    self.instanceId = string.gsub(self.taskID, "GuildLeagueTag_", "")
    self:ResetAllAnimationsState()
	local towerData = Game.TableData.Get_TowerInfoByInstanceID()[self.instanceId]
	self.bIsTower = towerData.level ~= 4
    self:RefreshTower()
    self:SetEnterAnimation()
	self.View.Text_Mask:SetVisibility(ESlateVisibility.Collapsed)
	self.View.Btn_ClickArea_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.canClick = false
end

function P_MapLeagueTowerTag:RefreshTower()
    if self:TowerCanShow() then
        self.View:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.View:SetVisibility(ESlateVisibility.Collapsed)
    end
	self:SetMaterial(self.View.Img_Icon, UIAssetPath.MI_HUD_Tower_HP,function()
		self:AfterMILoad()
	end, false)
end

function P_MapLeagueTowerTag:AfterMILoad()
	local tower = Game.GuildLeagueSystem:GetTowerInfo(self.instanceId)
	if tower then
		self.towerUID = tower:uid()
		Game.UniqEventSystemMgr:AddListener(self.towerUID, EEventTypesV2.BATTLE_BUFF_START, "OnEnterSuperTower", self)
		Game.UniqEventSystemMgr:AddListener(self.towerUID, EEventTypesV2.BATTLE_BUFF_END, "OnExitSuperTower", self)

		Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.ON_HP_CHANGED, self, "OnHPChanged", tower.eid)
	end
	self:SetTowerIcon(self:GetSelfIsRed(), Game.GuildLeagueSystem:IsSuperTower(self.instanceId), self.bIsTower)
	self:OnHPChanged()
end

function P_MapLeagueTowerTag:OnDestroy()
	if self.towerUID then
		Game.UniqEventSystemMgr:RemoveListener(self.towerUID, EEventTypesV2.BATTLE_BUFF_START, "OnEnterSuperTower", self)
		Game.UniqEventSystemMgr:RemoveListener(self.towerUID, EEventTypesV2.BATTLE_BUFF_END, "OnExitSuperTower", self)
	end
	
	Game.EventSystem:RemoveObjListeners(self)
end

function P_MapLeagueTowerTag:TowerCanShow()
    local towerData = Game.GuildLeagueSystem:GetTowerInfo(self.instanceId)
    if not towerData then
        return false
    end
    local currentSpace = NetworkManager.GetLocalSpace()
    if not currentSpace then
        return false
    end
    return true
end

function P_MapLeagueTowerTag:GetSelfIsRed()
    local towerData = Game.TableData.Get_TowerInfoByInstanceID()[self.instanceId]
    local selfCampId = Game.GuildLeagueSystem:GetSelfCampID()
	--- 自己阵营的就是蓝方
    if towerData.camp == selfCampId then
        --if selfCampId == Enum.EGuildLeagueConstIntData.MULTI_PVP_CAMP_B_ID then
        --    return false
        --else
        --    return true
        --end
		return false
    else
        --if selfCampId == Enum.EGuildLeagueConstIntData.MULTI_PVP_CAMP_B_ID then
        --    return true
        --else
        --    return false
        --end
		return true
    end
end

function P_MapLeagueTowerTag:SetEnterAnimation()
    self:StopAllAnimations(self.View.WidgetRoot)
end

--function P_MapLeagueTowerTag:OnAttackedByArea(attackSpawnerId, defenderSpawnerId)
--    if defenderSpawnerId == self.instanceId then
--        self:PlayAnimation(
--            self.View.WidgetRoot, self.View.Ani_Locking_Arrow, 0, 1, EUMGSequencePlayMode.Forward, 1, false, function()
--				-- 攻击动效结束后刷新锁定状态，否则触发攻击后，动画互斥，会导致锁定效果丢失
--				self:RefreshLockStatus()
--            end
--        )
--    end
--end

--function P_MapLeagueTowerTag:RefreshLockStatus()
--    self.View.VX_Img_Arrow:SetVisibility(ESlateVisibility.Collapsed)
--    local currentSpace = NetworkManager.GetLocalSpace()
--    if not currentSpace then
--        return
--    end
--    if currentSpace.WorldType ~= worldConst.WORLD_TYPE.GUILD_LEAGUE then
--        return
--    end
--    for altarId, towerId in pairs(currentSpace.LockingTowerIDs) do
--        if towerId == self.instanceId then
--            self.View.VX_Img_Arrow:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
--            self:PlayAnimation(
--                self.View.WidgetRoot, self.View.Ani_Locking, 0, 0, EUMGSequencePlayMode.Forward, 1, false
--            )
--            break
--        end
--    end
--end

--function P_MapLeagueTowerTag:OnLockByAltar(towerId)
--    self:RefreshLockStatus()
--end

function P_MapLeagueTowerTag:ResetAllAnimationsState()
    self:StopAllAnimations(self.View.WidgetRoot)
    self.View.VX_Img_Fire:SetVisibility(ESlateVisibility.Collapsed)
    self.View.VX_Img_Fire_Blue:SetVisibility(ESlateVisibility.Collapsed)
    self.View.VX_Img_Fire_1:SetVisibility(ESlateVisibility.Collapsed)
    self.View.Img_Icon:SetRenderOpacity(1)
end

----- 进入超级塔时，设置图标
function P_MapLeagueTowerTag:OnEnterSuperTower(buff)
	self:SetTowerIcon(self:GetSelfIsRed(),  Game.GuildLeagueSystem:IsSuperTower(self.instanceId), self.bIsTower)
end

function P_MapLeagueTowerTag:OnExitSuperTower(buff)
	self:SetTowerIcon(self:GetSelfIsRed(),  Game.GuildLeagueSystem:IsSuperTower(self.instanceId), self.bIsTower)
end
--- 如果是升级塔的，则不变，其他的则压暗
function P_MapLeagueTowerTag:OnStrategySelected(id)
	if not self:IsInGuildBattleMap() then
		return
	end
	local type = Game.GuildLeagueSystem:GetStrategyType(id)
	if type == Game.GuildLeagueSystem.StrategyType.INTERACTOR_COMMON then -- todo:后续确定了类型在处理
		self.canClick = true
		self.View.Img_Icon:SetColorAndOpacity(Game.GuildLeagueSystem.MapTagMaskColor.Normal)
		self.View.Btn_ClickArea_lua:SetVisibility(ESlateVisibility.Visible)
		return
	end
	self:ResetAllAnimationsState()
	self.View.Img_Icon:SetColorAndOpacity(Game.GuildLeagueSystem.MapTagMaskColor.Mask)
end

function P_MapLeagueTowerTag:OnStrategyUnSelected(id)
	if not self:IsInGuildBattleMap() then
		return
	end
	if id then
		local type = Game.GuildLeagueSystem:GetStrategyType(id)
		if type == Game.GuildLeagueSystem.StrategyType.INTERACTOR_COMMON then -- todo:后续确定了类型在处理
			self.View.Btn_ClickArea_lua:SetVisibility(ESlateVisibility.Collapsed)
			return
		end
	end
	self.canClick = false
	--self:RefreshLockStatus() -- 恢复锁定状态
	self.View.Img_Icon:SetColorAndOpacity(Game.GuildLeagueSystem.MapTagMaskColor.Normal)
end

function P_MapLeagueTowerTag:GetGuildLeagueTagComponent()
	return self.parent.controlcomponents.GuildLeagueTagComponent
end

function P_MapLeagueTowerTag:IsInGuildBattleMap()
	return self:GetGuildLeagueTagComponent().bInGuildBattleMap
end

function P_MapLeagueTowerTag:OnHPChanged()
	-- 计算血量
	local tower = Game.GuildLeagueSystem:GetTowerInfo(self.instanceId)
	if not tower then
		return
	end
	local widget = self.View.Img_Icon:GetDynamicMaterial()
	if not widget then
		return
	end
	local hpPercent = tower.Hp / tower.MaxHp
	if hpPercent <= 0 then
		hpPercent = 0
	end
	if hpPercent >= 1 then
		hpPercent = 1
	end
	widget:SetScalarParameterValue("DisolveIntensity", 1 - hpPercent)
end

function P_MapLeagueTowerTag:SetTowerIcon(bIsRed, bIsSuperTower, bTower)
	local icon = nil
	if bIsRed then
		if not bTower then
			icon = UIAssetPath.UI_HUD_Icon_RedMark02
		else
			if bIsSuperTower then
				icon = UIAssetPath.UI_HUD_Icon_RedMark07
			else
				icon = UIAssetPath.UI_HUD_Icon_RedMark03
			end
		end
	else
		if not bTower then
			icon = UIAssetPath.UI_HUD_Icon_BlueMark02
		else
			if bIsSuperTower then
				icon = UIAssetPath.UI_HUD_Icon_BlueMark07
			else
				icon = UIAssetPath.UI_HUD_Icon_BlueMark03
			end
		end
	end
	if not icon then
		return
	end
	self:SetTextureParameterValue(self.View.Img_Icon, "MainTex", icon, nil, nil, true)
	self:SetTextureParameterValue(self.View.Img_Icon, "MainTex2", icon, nil, nil, true)
end

function P_MapLeagueTowerTag:OnClick_Btn_ClickArea()
	if not self.canClick then
		return
	end
	-- todo:发rpc给服务器，升级塔
end

return P_MapLeagueTowerTag