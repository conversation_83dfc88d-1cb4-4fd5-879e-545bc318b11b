kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapTeleportTagView")
---@class P_MapTeleportTag : UIController
---@field public View WBP_MapTpTagView
local P_MapTeleportTag = DefineClass("P_MapTeleportTag", UIComponent)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
---@type ESlateVisibility
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")


function P_MapTeleportTag:OnCreate()
end

function P_MapTeleportTag:FlushView()
    self:OnRefresh(self.TaskID, self.bSelect, self.bInEdge)
end

function P_MapTeleportTag:OnRefresh(TaskID, bSelect, bInEdge)
    self.TaskID = TaskID
    self.bSelect = bSelect
    self.bInEdge = bInEdge
    local Data = P_MapTeleportTag.GetMapTagData(TaskID)
    local TypeData = Game.TableData.GetTagTypeConfigDataRow(Data.TagKind)
    local scale = TypeData.MinimapTagScale
    if scale and self.parent.bMiniMap then
        self.View.RootPanel:SetRenderScale(FVector2D(scale, scale))
        self.View.Btn_ClickArea:SetVisibility(ESlateVisibility.Hidden)
    else
        self.View.RootPanel:SetRenderScale(FVector2D(1, 1))
        self.View.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
    end
    self.View.Tb_Name:SetText(Data.TagName)
    self.View:Event_UI_Style(bSelect)
    if Data.TagKind == Enum.ETagTypeConfigData.STELE or Data.TagKind == Enum.ETagTypeConfigData.SUBWAY
        or Data.TagKind == Enum.ETagTypeConfigData.FIRSTSUBWAY then
        if Game.TeleportManager:IsTeleportPointUnlocked(Data.LinkInsID) then
            self.View.WidgetRoot:Set_Lock_State(Data.TagKind == Enum.ETagTypeConfigData.STELE and 0 or 1, false)
            self.View.Img_Icon:SetOpacity(1.0)
            self:SetImage(self.View.Img_Icon, Data.TagIcon)
        else
            if Data.LockedDetailIcon ~= "" then
                self.View.WidgetRoot:Set_Lock_State(Data.TagKind == Enum.ETagTypeConfigData.STELE and 0 or 1, true)
                self.View.Img_Icon:SetOpacity(1.0)
                self:SetImage(self.View.Img_Icon, Data.LockedDetailIcon)
            else
                self.View.WidgetRoot:Set_Lock_State(Data.TagKind == Enum.ETagTypeConfigData.STELE and 0 or 1, true)
                self.View.Img_Icon:SetOpacity(0.5)
                self:SetImage(self.View.Img_Icon, Data.TagIcon)
            end
        end
    else
        self:SetImage(self.View.Img_Icon, Data.TagIcon)
    end
    if self.parent.LayerID < Data.LayerID then
        self.View.WBP_MapLayerTrend:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.WBP_MapLayerTrend:SetRenderTransformAngle(180)
    elseif self.parent.LayerID > Data.LayerID then
        self.View.WBP_MapLayerTrend:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.View.WBP_MapLayerTrend:SetRenderTransformAngle(0)
    else
        self.View.WBP_MapLayerTrend:SetVisibility(ESlateVisibility.Collapsed)
    end

    local TraceID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.MAP)
    if TraceID and TraceID == tonumber(self.TaskID) then
        --self.parent:NotifyCustomTrace(self.TaskID)
        self:OnSetCustomTrace(true)
        self.parent.CurrentTraceCustomID = self.TaskID
        if bInEdge then
            self.parent.View.WidgetRoot:RegisterWidgetRotateInEdge(self.TaskID, self.View.WBP_MapEdgeWrap.WidgetRoot, 180)
            self.View.WBP_MapEdgeWrap:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        else
            self.View.WBP_MapEdgeWrap:SetVisibility(ESlateVisibility.Collapsed)
        end
    else
        self.View.WBP_MapEdgeWrap:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_MapTeleportTag.GetMapTagData(TaskID)
    local Data = Game.TableData.GetMapTagDataRow(tonumber(TaskID))--最基本的情况
    if not Data then
        local Split = string.split(TaskID, "_")
        if Split[1] == "WorldBoss" then
            local ActivityData = Game.TableData.GetActivityDataRow(tonumber(Split[2]))
            if ActivityData then
                Data = Game.TableData.GetMapTagDataRow(ActivityData.MapTagID)
            end
        end
    end
    return Data
end

function P_MapTeleportTag:OnHide()
    self.parent.View.WidgetRoot:UnRegisterWidgetRotateInEdge(self.TaskID)
end


function P_MapTeleportTag:OnClick_Btn_ClickArea()
    if self.TaskID then
        local OutTasks = self.parent:ReqTaskNearByByTaskID(self.TaskID, 0.05)
        if #OutTasks > 1 then
            UI.Invoke("P_MapV2", "ReqOpenTaskList", OutTasks)
        else
            UI.Invoke("P_MapV2", "ReqOpenHalfScreen", self.TaskID)
        end
        if self.bInEdge then
            local MapUI = UI.GetUI("P_MapV2")
            if MapUI then MapUI:MoveMapCenterToTask(self.TaskID, true) end
        end
    end
    self.parent:RemoveTempCustomSelection()
    self.parent:NotifySelection(self.TaskID)
end

function P_MapTeleportTag:OnSelectionStateChanged(bSelect)
    self.View:Event_UI_Style(bSelect)
end

function P_MapTeleportTag:OnSetCustomTrace(bTrace)
    if bTrace then
        self:PlayAnimation(
            self.View.WBP_MapTracing, self.View.WBP_MapTracing.Ani_Loop, 0, 0, EUMGSequencePlayMode.Forward
        )
    else
        self:StopAllAnimations(self.View.WBP_MapTracing)
    end
end


return P_MapTeleportTag
