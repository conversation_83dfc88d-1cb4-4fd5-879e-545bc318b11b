local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class MapWorldExploreTag : UIComponent
---@field view MapWorldExploreTagBlueprint
local MapWorldExploreTag = DefineClass("MapWorldExploreTag", UIComponent)

MapWorldExploreTag.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function MapWorldExploreTag:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function MapWorldExploreTag:InitUIData()
end

--- UI组件初始化，此处为自动生成
function MapWorldExploreTag:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function MapWorldExploreTag:InitUIEvent()
    self:AddUIEvent(self.view.KHotA.OnClicked, "on_KHotA_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function MapWorldExploreTag:InitUIView()
end

---组件刷新统一入口
function MapWorldExploreTag:Refresh(...)
end


function MapWorldExploreTag:OnRefresh(TaskID, bSelect, bInEdge)
	self.TaskID = TaskID
	local splited = string.split(TaskID, "_")
	local magTagID = tonumber(splited[2])
	local tagUIData = Game.TableData.GetMapTagDataRow(magTagID)
	-- 补充解锁、未解锁不同的UI
	self:SetImage(self.view.Img_Icon, tagUIData.TagIcon)
end

--- 此处为自动生成
function MapWorldExploreTag:on_KHotA_Clicked()
	-- 也许要打开半屏UI，可后续支持
end

return MapWorldExploreTag
