kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapEdgeWrapView")
---@class P_MapEdgeWrap : UIComponent
---@field public View WBP_MapEdgeWrapView
local P_MapEdgeWrap = DefineClass("P_MapEdgeWrap", UIComponent)

function P_MapEdgeWrap:OnCreate()
    
end

function P_MapEdgeWrap:OnRefresh(TaskID)
    self.TaskID = TaskID
end

function P_MapEdgeWrap:OnIdle()
    local Angle = 180 - self.parent.widget:GetWidgetShearByTaskID(
        self.TaskID
    ) * 180 / 3.14
    self.View.CP_ArrowPanel:SetRenderTransformAngle(
        Angle
    )
    Log.Warning("lizhemian : Angle " .. Angle)
end

function P_MapEdgeWrap:OnHide()

end




return P_MapEdgeWrap
