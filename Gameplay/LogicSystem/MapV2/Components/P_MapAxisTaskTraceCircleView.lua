---@class WBP_MapAxisTaskTraceCircleView : WBP_MapAxisTaskTraceCircle_C
---@field public WidgetRoot WBP_MapAxisTaskTraceCircle_C
---@field public Img_Tracing Image
---@field public An_Track WidgetAnimation
---@field public FetchIconWidget fun(self:self):Widget


---@class P_MapAxisTaskTraceCircleView : WBP_MapAxisTaskTraceCircleView
---@field public controller P_MapAxisTaskTraceCircle
local P_MapAxisTaskTraceCircleView = DefineClass("P_MapAxisTaskTraceCircleView", UIView)

function P_MapAxisTaskTraceCircleView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

end

function P_MapAxisTaskTraceCircleView:OnDestroy()
---DeletePlaceHolder
end

return P_MapAxisTaskTraceCircleView
