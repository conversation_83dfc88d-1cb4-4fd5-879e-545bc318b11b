kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapReceiveTaskView")

---@class P_MapReceiveTask : UIComponent
---@field public View P_MapReceiveTaskView
local P_MapReceiveTask = DefineClass("P_MapReceiveTask", UIComponent)

local ESlateVisibility = import("ESlateVisibility")

function P_MapReceiveTask:OnCreate()
    
end

function P_MapReceiveTask:OnRefresh(TaskID, bSelect, bInEdge)
    local splited = string.split(TaskID, "_")
    local taskRingID = tonumber(splited[2])
    -- Log.Warning("P_MapReceiveTask OnRefresh, TaskID ", TaskID, " taskRingID ", taskRingID)
    local TypeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.TASK_WAITING_TAKE)
    local scale = TypeData.MinimapTagScale
    if scale and self.parent.bMiniMap then
        self.View.RootPanel:SetRenderScale(FVector2D(scale, scale))
    else
        self.View.RootPanel:SetRenderScale(FVector2D(1, 1))
    end
    local iconPath, iconBgPath, color, outLineColor = Game.QuestSystem:GetNPCTaskIconAndColor(taskRingID)
    if color then
        -- Log.Dump(color)
        -- Log.Dump(color.SpecifiedColor)
        self.View.Img_Icon:SetColorAndOpacity(color.SpecifiedColor)
        self.View.Img_IconBg:SetColorAndOpacity(color.SpecifiedColor)
    else
        self.View.Img_Icon:SetColorAndOpacity(FLinearColor.White)
        self.View.Img_IconBg:SetColorAndOpacity(FLinearColor.White)
    end
    if iconPath then
        self:SetImage(self.View.Img_Icon, iconPath)
        if iconBgPath then
            self.View.Img_IconBg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self:SetImage(self.View.Img_IconBg, iconBgPath)
        else
            self.View.Img_IconBg:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
    -- self.View:Event_UI_Style(0)
end

function P_MapReceiveTask:OnHide()

end



return P_MapReceiveTask