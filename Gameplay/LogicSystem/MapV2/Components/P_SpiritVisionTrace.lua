kg_require("Gameplay.LogicSystem.MapV2.Components.P_SpiritVisionTraceView")

---@class P_SpiritVisionTrace : UIComponent
---@field public View WBP_SpiritVisionTraceView
local P_SpiritVisionTrace = DefineClass("P_SpiritVisionTrace", UIComponent)

local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require "Data.Config.StringConst.StringConst"

--灵视与交互物图标
P_SpiritVisionTrace.IconPathList = {
    [Enum.ESpiritualIconType.Treasure] = "SPIRITUAL_TREASURE",
    [Enum.ESpiritualIconType.Question] = "SPIRITUAL_QUESTION",
    [Enum.ESpiritualIconType.Key] = "SPIRITUAL_KEY",
}

function P_SpiritVisionTrace:OnCreate()
    
end

function P_SpiritVisionTrace:OnRefresh(TaskID, bSelect, bInEdge)
    local c = self.View
    c.Text_Distance:SetVisibility(ESlateVisibility.Collapsed)
    local arr = string.split(TaskID, "_")
    local InsID = arr[2]
    -- Log.Warning("P_SpiritVisionTrace TaskID ", TaskID, " InsID ",  InsID)
    local LSEntity = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(InsID)
    if LSEntity then
        
    else
        local NpcEid = Game.WorldManager:GetNpcByInstance(InsID)
        if NpcEid then
            LSEntity = Game.EntityManager:getEntity(NpcEid)
        end
    end
    if LSEntity then
        local iconType = LSEntity:GetSpiritualIconType()
        local iconName = P_SpiritVisionTrace.IconPathList[iconType]
        if not iconName then
            Log.WarningFormat("Error SpiritualTraceIcon: iconType: %s entityid: %s", iconType, LSEntity:uid())
            iconName = P_SpiritVisionTrace.IconPathList[Enum.ESpiritualIconType.Question]
        end
        local icon = StringConst.Get(iconName)
        self:SetImage(c.icon_target, icon)
    end

    local TypeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.SPIRIT_VISION)
    local scale = TypeData.MinimapTagScale
    if scale and self.parent.bMiniMap then
        self.View.RootPanel:SetRenderScale(FVector2D(scale, scale))
    else
        self.View.RootPanel:SetRenderScale(FVector2D(1, 1))
    end
end

function P_SpiritVisionTrace:OnHide()

end



return P_SpiritVisionTrace