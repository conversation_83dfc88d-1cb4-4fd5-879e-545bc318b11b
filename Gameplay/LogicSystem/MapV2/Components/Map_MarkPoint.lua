---从P_MapMark文件移动过来，接入完成后，删除对应的旧文件
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Map_MarkPoint : UIComponent
---@field view Map_MarkPointBlueprint
local Map_MarkPoint = DefineClass("Map_MarkPoint", UIComponent)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local Const = kg_require("Shared.Const")

Map_MarkPoint.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Map_MarkPoint:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Map_MarkPoint:InitUIData()
	self.TaskID = nil
end

--- UI组件初始化，此处为自动生成
function Map_MarkPoint:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Map_MarkPoint:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Map_MarkPoint:InitUIView()
end

---组件刷新统一入口
function Map_MarkPoint:Refresh(...)
end


function Map_MarkPoint:OnRefresh(TaskID, bSelect, bInEdge)
	self.TaskID = TaskID
	if self.TaskID == "CustomTrace_Temp" then
		self:GetParent():NotifySelection(self.TaskID)
		self:RefreshTagType(1)
		--self:PlayAnimation(self.view.WidgetRoot, self.view.Ani_MarkArrow, 0, 1, EUMGSequencePlayMode.Forward)
	else
		--self.view.Img_Mark_VX:SetVisibility(ESlateVisibility.Collapsed)
		local uid = string.sub(self.TaskID, 13, #self.TaskID)
		if Game.me.TracingCustomList[uid] then
			local TagID = Game.me.TracingCustomList[uid].TagID
			self:RefreshTagType(TagID)
			local TraceID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.MAP_TAG)
			if TraceID == uid then
				--self:GetParent():NotifyCustomTrace(self.TaskID)
				self:OnSetCustomTrace(true)
				self:GetParent().CurrentTraceCustomID = self.TaskID
			end
		end
	end
	-- 新增追踪染色逻辑, 且是大地图的表现。
	self:InitUIbyTracingType(bInEdge)
end

function Map_MarkPoint:InitUIbyTracingType(bInEdge)
	local TraceUIConfig = Game.TableData.GetTraceTypeInfoDataRow(Const.TRACING_INFO_TYPE.MAP_TAG)
	local IconImg
	if bInEdge then
		IconImg = TraceUIConfig["BigMapEdgeImgPath"]
	else
		IconImg = TraceUIConfig["MapTraceImagePath"]
	end
	if IconImg ~= "" then
		self:SetImage(self.view.Img_Icon, HUDImage, nil, nil, nil, true)
	end
	local color = Game.TraceSystem:GetUIColorByTracingType(Const.TRACING_INFO_TYPE.MAP_TAG)
	if color then
		self.view.Img_Icon:SetColorAndOpacity(color)
	end
end

function Map_MarkPoint:OnMapImgLoaded(color)
	-- 染色, 暂时没有edge相关逻辑
	if not color then
		return
	end
	self.view.Img_Icon:SetBrushTintColor(color)
end

function Map_MarkPoint:RefreshTagType(TagType)
	local TagData = Game.TableData.GetMapCustomTagDataRow(TagType)
	if TagData then
		self:SetImage(self.view.Img_Icon, TagData.DetailIcon)
	end
end

function Map_MarkPoint:OnHide()
	self:StopAllAnimations(self.view.WBP_MapTracing)
end

function Map_MarkPoint:OnSelectionStateChanged(bSelect)
	if bSelect then
		self:PlayAnimation(self.view.Ani_MarkArrow, nil, self.userWidget, 0, 1, EUMGSequencePlayMode.Forward)
	end
end

function Map_MarkPoint:OnSetCustomTrace(bTrace)
	local mapTracingWidget = self.view.WBP_MapTracing
	if bTrace then
		self:PlayAnimation(mapTracingWidget.Ani_Loop, nil, mapTracingWidget, 0, 0, EUMGSequencePlayMode.Forward)
	else
		self:StopAllAnimations(mapTracingWidget)
	end
end

--- 此处为自动生成
function Map_MarkPoint:on_Btn_ClickArea_Clicked()
	---@type P_MapTagView
	local parent = self:GetParent()
	Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Map_Position, true)
	if self.TaskID == "CustomTrace_Temp" then
		parent:CancelDeleteCustomTrace_TempTimer()
		--local UIName = "P_MapTagPanel"
		UI.GetUI("P_MapV2"):ReqOpenHalfScreen(self.TaskID)
	elseif self.TaskID then
		local OutTasks = parent:ReqTaskNearByByTaskID(self.TaskID, 0.05)
		if #OutTasks > 1 then
			UI.Invoke("P_MapV2", "ReqOpenTaskList", OutTasks)
		else
			UI.Invoke("P_MapV2", "ReqOpenHalfScreen", self.TaskID)
		end
	end

	if self.TaskID ~= "CustomTrace_Temp" then
		parent:RemoveTempCustomSelection()
	end
	parent:NotifySelection(self.TaskID)
end

return Map_MarkPoint
