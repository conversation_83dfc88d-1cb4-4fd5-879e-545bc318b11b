kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapMarkView")
kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapMarkView")
---@class P_MapMark : UIComponent
---@field public View WBP_MapMarkView
local P_MapMark = DefineClass("P_MapMark", UIComponent)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local Const = kg_require("Shared.Const")


--local P_MapV2 = kg_require("Gameplay.LogicSystem.MapV2.P_MapV2")
function P_MapMark:OnCreate()
    
end

function P_MapMark:OnRefresh(TaskID, bSelect, bInEdge)
    self.TaskID = TaskID
    if self.TaskID == "CustomTrace_Temp" then
        self.parent:NotifySelection(self.TaskID)
        self:RefreshTagType(1)
        --self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_MarkArrow, 0, 1, EUMGSequencePlayMode.Forward)
    else
        --self.View.Img_Mark_VX:SetVisibility(ESlateVisibility.Collapsed)
        local uid = string.sub(self.TaskID, 13, #self.TaskID)
        if Game.me.TracingCustomList[uid] then
            local TagID = Game.me.TracingCustomList[uid].TagID
            self:RefreshTagType(TagID)
            local TraceID = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.MAP_TAG)
            if TraceID == uid then
                --self.parent:NotifyCustomTrace(self.TaskID)
                self:OnSetCustomTrace(true)
                self.parent.CurrentTraceCustomID = self.TaskID
            end
        end
    end
	-- 新增追踪染色逻辑, 且是大地图的表现。
	self:InitUIbyTracingType(bInEdge)
end

function P_MapMark:InitUIbyTracingType(bInEdge)
	local TraceUIConfig = Game.TableData.GetTraceTypeInfoDataRow(Const.TRACING_INFO_TYPE.MAP_TAG)
	local IconImg
	if bInEdge then
		IconImg = TraceUIConfig["BigMapEdgeImgPath"]
	else
		IconImg = TraceUIConfig["MapTraceImagePath"]
	end
	if IconImg ~= "" then
		self:SetImage(self.View.Img_Icon, HUDImage, nil, nil, nil, true)
	end
	local color = Game.TraceSystem:GetUIColorByTracingType(Const.TRACING_INFO_TYPE.MAP_TAG)
	if color then
		self.View.Img_Icon:SetColorAndOpacity(color)
	end
end

function P_MapMark:OnMapImgLoaded(color)
	-- 染色, 暂时没有edge相关逻辑
	if not color then
		return
	end
	self.View.Img_Icon:SetBrushTintColor(color)
end

function P_MapMark:RefreshTagType(TagType)
    local TagData = Game.TableData.GetMapCustomTagDataRow(TagType)
    if TagData then
        self:SetImage(self.View.Img_Icon, TagData.DetailIcon)
    end
end

function P_MapMark:OnHide()
    self:StopAllAnimations(self.View.WBP_MapTracing)
end




function P_MapMark:OnClick_Btn_ClickArea()
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Map_Position, true)
    if self.TaskID == "CustomTrace_Temp" then
        self.parent:CancelDeleteCustomTrace_TempTimer()
        local UIName = "P_MapTagPanel"
        UI.GetUI("P_MapV2"):ReqOpenHalfScreen(self.TaskID)
    elseif self.TaskID then
        local OutTasks = self.parent:ReqTaskNearByByTaskID(self.TaskID, 0.05)
        if #OutTasks > 1 then
            UI.Invoke("P_MapV2", "ReqOpenTaskList", OutTasks)
        else
            UI.Invoke("P_MapV2", "ReqOpenHalfScreen", self.TaskID)
        end
    end
    
    if self.TaskID ~= "CustomTrace_Temp" then
        self.parent:RemoveTempCustomSelection()
    end
    self.parent:NotifySelection(self.TaskID)
end

function P_MapMark:OnSelectionStateChanged(bSelect)
	if bSelect then 
		self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_MarkArrow, 0, 1, EUMGSequencePlayMode.Forward)
	end
end

function P_MapMark:OnSetCustomTrace(bTrace)
    if bTrace then
        self:PlayAnimation(
            self.View.WBP_MapTracing, self.View.WBP_MapTracing.Ani_Loop, 0, 0, EUMGSequencePlayMode.Forward
        )
    else
        self:StopAllAnimations(self.View.WBP_MapTracing)
    end
end

return P_MapMark
