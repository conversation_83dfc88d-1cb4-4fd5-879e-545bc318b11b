kg_require("Gameplay.LogicSystem.MapV2.Components.P_MiniMapDoubleEdgeView")

---@class P_MiniMapDoubleEdge : UIComponent
---@field public View WBP_HUDMapTraceView
local P_MiniMapDoubleEdge = DefineClass("P_MiniMapDoubleEdge", UIComponent)

---@type ESlateVisibility
local ESlateVisibility = import("ESlateVisibility")

function P_MiniMapDoubleEdge:OnCreate()
end

function P_MiniMapDoubleEdge:OnRefresh(TaskID, bSelect, bInEdge)
    self.bInEdge = bInEdge
    self.TaskID = TaskID
    if self.bInEdge then
        self.View.WidgetRoot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.View.WidgetRoot:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.parent.View.WidgetRoot:RegisterWidgetRotateInEdge(self.TaskID, self.View.RotatePanel, 270)
end

function P_MiniMapDoubleEdge:OnHide()
    self.parent.View.WidgetRoot:UnRegisterWidgetRotateInEdge(self.TaskID)
end


return P_MiniMapDoubleEdge
