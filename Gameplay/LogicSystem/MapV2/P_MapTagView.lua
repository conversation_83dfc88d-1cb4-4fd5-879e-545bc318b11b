local uicomponent = require("Framework.UI.UIComponent")
local UIBaseAdapter = kg_require "Framework.UI.UIBaseAdpater"
--[[
各种控制地图标签的Component，由这些Component控制哪些Tag被显示，事件监听，Tag的Getter全在这些Component里
]]

local StaticTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.StaticTagComponent")
local TraceTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.TraceTagComponent")
local MiniMapEdgeTraceTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.MiniMapEdgeTraceTagComponent")
local AxisPointTraceTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.AxisPointTraceTagComponent")
local TeammateTagComponent =  kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.TeammateTagComponent")
local SpiritTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.SpiritTagComponent")
local CustomTraceComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.CustomTraceComponent")
local PlayerTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.PlayerTagComponent")
local ActivityTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.ActivityTagComponent")
local AudioTraceComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.AudioTraceComponent")
local MapSpiritualVisionComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.MapSpiritualVisionComponent")
local ReciveTaskTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.ReciveTaskTagComponent")
local GuildLeagueTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.GuildLeagueTagComponent")
local WorldMapExploreTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.WorldMapExploreTagComponent")
local KismetMathLibrary = import("KismetMathLibrary")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")

---@class P_MapTagView
---@field private widget C7MapTagLayerV2
local P_MapTagView = DefineClass("P_MapTagView", UIComponent)

--[[
UIComponent, 控制刷新出来的标签控件的Layout，点击事件
]]
local P_MapAxisPlace = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisPlace")
local P_MapCommonTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapCommonTag")
local P_MapAxisNPC = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisNPC")
local Map_NPCPoint = kg_require("Gameplay.LogicSystem.MapV2.Components.Map_NPCPoint")
local P_MapAxisPlayers = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisPlayers")
local P_MapAxisPlayeTeamMate = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisPlayeTeamMate")
local P_MapAxisTask = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisTask")
local P_MapAxisTaskTraceCircle = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisTaskTraceCircle")
local P_MapAxisNormalTrace = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisNormalTrace")
local P_MapEdgeWrap = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapEdgeWrap")
local P_MapAxisTracing = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAxisTracing")
local P_MapMark = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapMark")
local Map_MarkPoint = kg_require("Gameplay.LogicSystem.MapV2.Components.Map_MarkPoint")
local P_MiniMapEdge = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MiniMapEdge")
local P_MiniMapNormalEdge = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MiniMapNormalEdge")
local P_MiniMapDoubleEdge = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MiniMapDoubleEdge")
local P_MiniMapThreeEdge = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MiniMapThreeEdge")
local P_MapTeleportTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapTeleportTag")
local P_MapAudioTrace = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapAudioTrace")
local P_SpiritTrace = kg_require("Gameplay.LogicSystem.MapV2.Components.P_SpiritTrace")
local P_SpiritVisionTrace = kg_require("Gameplay.LogicSystem.MapV2.Components.P_SpiritVisionTrace")
local P_MapReceiveTask = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapReceiveTask")
local Map_TaskPoint = kg_require("Gameplay.LogicSystem.MapV2.Components.Map_TaskPoint")
local P_MapLeagueAltarTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapLeagueAltarTag")
local P_MapLeagueTowerTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapLeagueTowerTag")
local P_MapLeagueMonsterTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapLeagueMonsterTag")
local P_MapLeagueBattleTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapLeagueBattleTag")
local P_WonderFlowerTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_WonderFlowerTag")
local P_MapGuildLeaguePortalTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapGuildLeaguePortalTag")
local P_MapGuildLeagueVehicleTag = kg_require("Gameplay.LogicSystem.MapV2.Components.P_MapGuildLeagueVehicleTag")
local MapWorldExploreTag = kg_require("Gameplay.LogicSystem.MapV2.Components.MapWorldExploreTag")

local Const = kg_require("Shared.Const")

---@type EMapEdgeType
local EMapEdgeType = import("EMapEdgeType")


local ComponentList = {
    [1] = P_MapAxisPlace,
    [2] = P_MapCommonTag,
    [3] = QuestUtils.MapUseNewTag and Map_NPCPoint or P_MapAxisNPC,
    [4] = P_MapAxisPlayers,
    [5] = P_MapAxisPlayeTeamMate,
    [6] = P_MapAxisTask,
    [7] = P_MapAxisTaskTraceCircle,
    [8] = P_MapAxisNormalTrace,
    [10] = P_MapLeagueTowerTag,
    [11] = P_MapAxisTracing,
    [13] = QuestUtils.MapUseNewTag and Map_MarkPoint or P_MapMark,
    [14] = P_MiniMapEdge,
    [15] = P_MapTeleportTag,
    [16] = P_MapAudioTrace,
    [17] = P_SpiritTrace,
    [18] = P_SpiritVisionTrace,
    [19] = P_MiniMapNormalEdge,
    [20] = P_MiniMapDoubleEdge,
    [21] = P_MiniMapThreeEdge,
    [22] = QuestUtils.MapUseNewTag and Map_TaskPoint or P_MapReceiveTask,
    [23] = P_MapLeagueAltarTag,
    [24] = P_MapLeagueBattleTag,
    [25] = P_MapLeagueMonsterTag,
    [26] = P_WonderFlowerTag,
	[27] = P_MapGuildLeaguePortalTag,
	[28] = P_MapGuildLeagueVehicleTag,
	[29] = MapWorldExploreTag,
    [6657] = P_MapEdgeWrap
}

---@type EMapShowTypeOnEdge
local EMapShowTypeOnEdge = import("EMapShowTypeOnEdge")

--[[
    还需要增加一个事件监听的接口，让上层逻辑不关心啥时候刷新
]]

P_MapTagView.eventBindMap = {
    [EEventTypesV2.TRACE_ON_CURRENT_TRACE_CHANGED] = "OnMapTagTraceSet",
    [EEventTypesV2.CUSTOM_TRACING_TAG_ADD_SUCCESS]= "RemoveTempCustomSelection",
}

function P_MapTagView:ctor()
    self.controlcomponents = {
        StaticTagComponent = StaticTagComponent.new(),
        TraceTagComponent = TraceTagComponent.new(),
        SpiritTagComponent = SpiritTagComponent.new(),
        CustomTraceComponent = CustomTraceComponent.new(),
        PlayerTagComponent = PlayerTagComponent.new(),
        TeammateTagComponent = TeammateTagComponent.new(),
        ActivityTagComponent = ActivityTagComponent.new(),
        AxisPointTraceTagComponent = AxisPointTraceTagComponent.new(),
        MiniMapEdgeTraceTagComponent = MiniMapEdgeTraceTagComponent.new(),
        AudioTraceComponent = AudioTraceComponent.new(),
        MapSpiritualVisionComponent = MapSpiritualVisionComponent.new(),
        ReciveTaskTagComponent = ReciveTaskTagComponent.new(),
        GuildLeagueTagComponent = GuildLeagueTagComponent.new(),
		WorldMapExploreTagComponent = WorldMapExploreTagComponent.new(),
    }
    for index, component in pairs(self.controlcomponents) do
        component.parent = self
        component:OnCreate()
    end
end

function P_MapTagView:OnOpen()
    for index, component in pairs(self.controlcomponents) do
        component:BindEvent()
    end
end
function P_MapTagView:OnClose()
    for index, component in pairs(self.controlcomponents) do
        Game.EventSystem:RemoveObjListeners(component)
    end
    for index, component in pairs(self.controlcomponents) do
        Game.GlobalEventSystem:RemoveTargetAllListeners(component)
    end
    self.MapConfigID = nil
end

function P_MapTagView.CreateMapTagLayer(widget, Owner, Name)
    local MapTagBase = Owner:BindComponent(widget, P_MapTagView)
    MapTagBase:Init(Owner, Name)
    return MapTagBase
end

function P_MapTagView.CreateMapTagLayerNew(widget, Owner, Name)
	local MapTagBase = Owner:CreateComponent(widget, UIBaseAdapter, Name, P_MapTagView)
	MapTagBase:GetUIBase():Init(Owner, Name)
	return MapTagBase
end

function P_MapTagView:Init(Owner, Name)
    self.Name = Name
    ---@type table<number, UIComponent>
    self.components = ComponentList
    self.uiComponents = {}
    self.tickComponents = {}
    self.TaskID2ComponentMap = {}
    self.Component2TaskIDMap = {}
    --if Component then
    self.widget.OnMapTagInitializedDynamic:Add(
        function(Widget, TypeID, TaskID, bInEdge)
			if not self:IsOpened() then
				return
			end
            local uiComponent = self:getComponent(Widget, TypeID, TaskID, bInEdge)
            self.TaskID2ComponentMap[TaskID] = uiComponent
            self.Component2TaskIDMap[uiComponent] = TaskID
        end
    )
    local OnRemovedCallback = "OnRemoved_" .. self.widget:GetName()
    if string.endsWith(OnRemovedCallback, "_lua") then
        OnRemovedCallback = string.sub(OnRemovedCallback, 1, -5)
    end
    self.OnRemoved_Callback = Owner[OnRemovedCallback]
    self.widget.OnMapTagRemovedDynamic:Add(
        function(Widget, TypeID, TaskID)
			if not Widget then return end
            local uiComponent = self.uiComponents[Widget]
            if uiComponent then
                local TaskID = self.Component2TaskIDMap[uiComponent]
                self.Component2TaskIDMap[uiComponent] = nil
                self.TaskID2ComponentMap[TaskID] = nil

                self.tickComponents[uiComponent] = nil
                uiComponent:Hide()
                uiComponent:Close()
            end
        end
    )
    self.CurrentSelectTaskID = nil
    self.GetWidgetOffsetByCenterAndWorldLocation_Wrap = self.widget.GetWidgetOffsetByCenterAndWorldLocation
  --  end
end

function P_MapTagView:NotifySelection(TaskID)
    if self.CurrentSelectTaskID then
        if self.TaskID2ComponentMap[self.CurrentSelectTaskID] then
            self.TaskID2ComponentMap[self.CurrentSelectTaskID]:OnSelectionStateChanged(false)
        end
    end
    if self.TaskID2ComponentMap[TaskID] then
        self.TaskID2ComponentMap[TaskID]:OnSelectionStateChanged(true)
        self.CurrentSelectTaskID = TaskID
    else
        self.CurrentSelectTaskID = TaskID
    end
end

function P_MapTagView:NotifyMapTagTrace(TaskID)
	TaskID = tostring(TaskID)
    if self.CurrentTraceCustomID then
        if self.TaskID2ComponentMap[self.CurrentTraceCustomID] then
            self.TaskID2ComponentMap[self.CurrentTraceCustomID]:OnSetCustomTrace(false)
        end
        self.widget:SetTaskShowEdgeType(self.CurrentTraceCustomID, EMapEdgeType.None)
    end
    if self.TaskID2ComponentMap[TaskID] then
        self.TaskID2ComponentMap[TaskID]:OnSetCustomTrace(true)
        self.CurrentTraceCustomID = TaskID
        if not self.bMiniMap then
            self.widget:SetTaskShowEdgeType(TaskID, EMapEdgeType.ShowOnEdge)
        end
    else
        if not self.bMiniMap then
            self.widget:SetTaskShowEdgeType(TaskID, EMapEdgeType.ShowOnEdge)
        end
        self.CurrentTraceCustomID = nil
    end
end


function P_MapTagView:OnCreate()
    
end

function P_MapTagView:SetData(Data)
    self.widget:ClearAllTags()
    for key, value in pairs(Data) do
        self.widget:AddSingleTag(value)
    end
    self:OnMapTagTraceSet()
end

function P_MapTagView:ClearAllTags()
    self.widget:ClearAllTags()
end

function P_MapTagView:getComponent(Widget, TypeID, TaskID, bInEdge)
    local uiComponent = self.uiComponents[Widget]
    if uiComponent and self.components[TypeID] and uiComponent.__cname == self.components[TypeID].__cname then 
        uiComponent:Show()
        uiComponent:Open()
        uiComponent:Refresh(TaskID, self.CurrentSelectTaskID == TaskID, bInEdge)
        if uiComponent.OnIdle then
            self.tickComponents[uiComponent] = 0
        end
        return uiComponent
    end
    if uiComponent then
        table.removev(self._childComponents, uiComponent)
        Game.EventSystem:RemoveObjListeners(uiComponent)
        uiComponent:Destroy(true)
    end
    if self.components[TypeID] then
        uiComponent = self:BindComponent(Widget, self.components[TypeID], TaskID, self.CurrentSelectTaskID == TaskID, bInEdge)
    end
    self.uiComponents[Widget] = uiComponent
    if uiComponent.OnIdle then
        self.tickComponents[uiComponent] = 0
    end
    return uiComponent

end

function P_MapTagView:GetComponentIndex(Component)
    return self.componentIndexs[Component]
end

function P_MapTagView:OnDestroy()
    self.widget.OnMapTagInitializedDynamic:Clear()
    self.widget.OnMapTagRemovedDynamic:Clear()
    for widget, component in pairs(self.uiComponents) do
        if self.component then
            component:Dispose()
        end
    end
    self.uiComponents = nil
    UIBase.OnDestroy(self)
end

function P_MapTagView:RefreshDataByMapConfigID(MapConfigID, OverrideRatio)
    --基础信息
    local MapConfigData = Game.TableData.GetMapDataRow(MapConfigID)
    if not MapConfigData then return end
    self.widget:SetCameraLocation(FVector(MapConfigData.CamLoc[1], MapConfigData.CamLoc[2], MapConfigData.CamLoc[3]))
    self.widget:SetCameraRotation(FRotator(MapConfigData.CamRot[1], MapConfigData.CamRot[2], MapConfigData.CamRot[3]))
    self.widget:SetCameraViewportSize(FVector2D(MapConfigData.CamOrthoWidth, MapConfigData.CamOrthoWidth / MapConfigData.CamAspectRatio))
    self.widget.PanelMapEdgeType = EMapShowTypeOnEdge.ShowOnEdgeByRectangle
    if not OverrideRatio then
        self.widget:SetViewportScale(1 / MapConfigData.MapDefaultScale)
    else
        self.widget:SetViewportScale(OverrideRatio)
    end
    self.widget:SetCurrentCenterLocation(FVector2D(MapConfigData.CamOrthoWidth / 2, MapConfigData.CamOrthoWidth / MapConfigData.CamAspectRatio / 2))
    
    local Geometry = import("WidgetLayoutLibrary").GetViewportWidgetGeometry(_G.GetContextObject())
    local NewSize = import("SlateBlueprintLibrary").GetLocalSize(Geometry)
    self.widget.Slot:SetSize(FVector2D(MapConfigData.MapWidgetSize[1], MapConfigData.MapWidgetSize[2]))
    self.widget.ConstraintRectangle.X = (MapConfigData.MapWidgetSize[1] - NewSize.X) / 2 / MapConfigData.MapWidgetSize[1] + 0.115 * (1 - MapConfigData.MapWidgetSize[1] / 4096)
    self.widget.ConstraintRectangle.Y = (MapConfigData.MapWidgetSize[2] - NewSize.Y) / 2 / MapConfigData.MapWidgetSize[2] + 0.115 * (1 - MapConfigData.MapWidgetSize[2] / 4096)
    self.widget.ConstraintRectangle.Z = (MapConfigData.MapWidgetSize[1] - NewSize.X) / 2 / MapConfigData.MapWidgetSize[1] + 0.115 * (1 - MapConfigData.MapWidgetSize[1] / 4096)
    self.widget.ConstraintRectangle.W = (MapConfigData.MapWidgetSize[2] - NewSize.Y) / 2 / MapConfigData.MapWidgetSize[2] + 0.115 * (1 - MapConfigData.MapWidgetSize[2] / 4096)
    self.widget.Slot:SetAutoSize(false)

    self.widget.Slot:SetAlignment(FVector2D(0.5, 0.5))
    local Filter = MapConfigData.ShowTagList
    --Tag信息
    self.MapID = MapConfigData.LevelID
    self.levelID = MapConfigData.LevelID
    if self.MapID == Game.LevelManager.GetCurrentLevelID() then
        self.WorldID = Game.HUDSystem.GetCurrentLineWorldID()
    else
        self.WorldID = -1
    end
    if MapConfigData.LayerID == -10 then
        self.LayerID = 0
    else
        self.LayerID = MapConfigData.LayerID
    end
    self.MapConfigID = MapConfigID
    self.bMiniMap = false
    self.CurrentTraceCustomID = nil
    self.CurrentSelectTaskID = nil
    self.planeID = Game.MapSystem:GetCurrentPlaneID()
    local curSpace = Game.NetworkManager.GetLocalSpace()
    local Map = slua.Map(import("EPropertyClass").Str, import("EPropertyClass").Struct, nil, import("MapTagInfo"))
	for index, component in pairs(self.controlcomponents) do
		local Data = component:GetMapTagData()
		for TaskID, TagData in pairs(Data) do
			Map:Add(TaskID, TagData)
		end
	end
    self:SetData(
        Map
    )
	Game.TraceSystem:refreshTracing()
end

function P_MapTagView:GetMapTagData()

end

function P_MapTagView:RefreshDataAsMiniMap()
    --基础信息初始化
	--todo 这些变量在init时要有明确的初始值，可能不是nil
    local CurrentLevelData = Game.MapSystem:GetUICurrentLevelData()
	self.MapID = Game.LevelManager.GetCurrentLevelID()
	self.levelID = self.MapID
	self.WorldID = Game.HUDSystem.GetCurrentLineWorldID()
	self.MapConfigID = CurrentLevelData.MapConfigID
	self.bMiniMap = true

	local curSpace = Game.NetworkManager.GetLocalSpace()
	if not curSpace then return end
	self.planeID = curSpace.planeID
	
    local MapConfigData = Game.TableData.GetMapDataRow(CurrentLevelData.MapConfigID)
    if not MapConfigData then return end
    self.widget:SetCameraLocation(FVector(MapConfigData.CamLoc[1], MapConfigData.CamLoc[2], MapConfigData.CamLoc[3]))
    self.widget:SetCameraRotation(FRotator(MapConfigData.CamRot[1], MapConfigData.CamRot[2], MapConfigData.CamRot[3]))
    self.widget:SetCameraViewportSize(FVector2D(MapConfigData.CamOrthoWidth, MapConfigData.CamOrthoWidth / MapConfigData.CamAspectRatio))
    self.widget.PanelMapEdgeType = EMapShowTypeOnEdge.ShowOnEdgeByCircle

    local ScaleDiff = math.sqrt(MapConfigData.MapMaxScale) - math.sqrt(MapConfigData.MapMinScale);
    local MapScale = (MapConfigData.MiniMapScale * ScaleDiff + math.sqrt(MapConfigData.MapMinScale)) ^ 2

    self.widget:SetViewportScale(1)
    self.widget.ConstraintRadius = 0.035
    self.widget:SetCurrentCenterLocation(FVector2D(MapConfigData.CamOrthoWidth / 2, MapConfigData.CamOrthoWidth / MapConfigData.CamAspectRatio / 2))
    self.widget.Slot:SetSize(FVector2D(MapScale * MapConfigData.MapWidgetSize[1], MapScale * MapConfigData.MapWidgetSize[2]))

    self.widget.Slot:SetAutoSize(false)

    self.widget.Slot:SetAlignment(FVector2D(0.5, 0.5))

    --Tag信息
    self.LayerID = MapConfigData.LayerID

    local Map = slua.Map(import("EPropertyClass").Str, import("EPropertyClass").Struct, nil, import("MapTagInfo"))
    for index, component in pairs(self.controlcomponents) do
        local Data = component:GetMapTagData()
        for TaskID, TagData in pairs(Data) do
            Map:Add(TaskID, TagData)
        end
    end
    self:SetData(
        Map
    )
	Game.TraceSystem:refreshTracing()
end

function P_MapTagView:GetComponentByTaskID(TaskID)
    return self.TaskID2ComponentMap[TaskID]
end

function P_MapTagView:EnableTick()
    self:StartTimer("TickTimer" .. "Map", function(DeltaTime)
        self:TickAllComponents(DeltaTime)
    end, 1, -1, nil, true)
end

function P_MapTagView:TickAllComponents(DeltaTime)
    for uiComponent, _ in pairs(self.tickComponents) do
        uiComponent:OnIdle(DeltaTime)
    end
    return 
end

function P_MapTagView:DisableTick()
    self:StopTimer("TickTimer" .. "Map")
end

function P_MapTagView:ReqTaskNearByByTaskID(TaskID, ToleranceDistance)
    local OutTasks = self.widget:ReqTaskNearByByTaskID(TaskID, ToleranceDistance)
    OutTasks = OutTasks:ToTable()
    local NPCTasks = {}
    for Index, TaskID in pairs(OutTasks) do
        local Data = Game.TableData.GetMapTagDataRow(tonumber(TaskID))
		--- todo PQ 把tagkind当枚举导出一份数据，不在这里判断常量
        if Data and Data.TagKind ~= 1 and Data.TagKind ~= 2 then
            table.insert(NPCTasks, TaskID)
        elseif string.contains(TaskID, "CustomTrace") and TaskID ~= "CustomTrace_Temp" then
            table.insert(NPCTasks, TaskID)
        end
    end
    return NPCTasks
end

--缓存UFunction，现在每次调UFunction都会malloc一个闭包，先这样存起来减少开销
function P_MapTagView:GetWidgetOffsetByCenterAndWorldLocation(Location)
    return self.GetWidgetOffsetByCenterAndWorldLocation_Wrap(self.widget, Location)
end

function P_MapTagView:DeprojectWidgetOffsetToWorldLocation(Location2D)
    Log.WarningFormat("LIzhemian: X %f Y %f", Location2D.X, Location2D.Y)
    return self.widget:DeprojectWidgetOffsetToWorldLocation(Location2D)
end

---@type MapTagInfo
local MapTagInfo = import("MapTagInfo")

function P_MapTagView:AddTempCustomSelection(Location2D)
    self.CustomTraceDataCoord = Location2D
    self.CustomTraceData = Game.MapSystem:BuildCustomTraceData(Location2D)
    self.widget:RemoveSingleTag(self.CustomTraceData.TaskID)
    self.widget:AddSingleTag(self.CustomTraceData)
    self:NotifySelection("CustomTrace_Temp")
    self:StartTimer("CustomTrace_Temp_Timer", 
    function()
        self:RemoveTempCustomSelection()    
    end, 3000, 1)
end

function P_MapTagView:RefreshCustomSelection(TagType)
    if self.TaskID2ComponentMap["CustomTrace_Temp"] then
        self.TaskID2ComponentMap["CustomTrace_Temp"]:RefreshTagType(TagType)
    end
end

function P_MapTagView:RemoveTempCustomSelection()
    self.widget:RemoveSingleTag("CustomTrace_Temp")
    self.CustomTraceData = nil
end

function P_MapTagView:CancelDeleteCustomTrace_TempTimer()
    self:StopTimer("CustomTrace_Temp_Timer")
end

function P_MapTagView:RefreshStaticTags()
    self.controlcomponents.StaticTagComponent:RefreshAllTags()
end
function P_MapTagView:RefreshComponentByTaskID(TaskID)
    local uiComponent = self.TaskID2ComponentMap[TaskID]
    if uiComponent and uiComponent.FlushView then
        uiComponent:FlushView()
    end
end

function P_MapTagView:OnMapTagTraceSet()
--[[    local TraceType, TraceInfo = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.MAP_TAG)
    if TraceType == 0 then 
        self:NotifyMapTagTrace("") 
        return 
    end]]
	local uid = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.MAP_TAG)
	if not uid then
		uid = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.MAP)
	end
	if not uid then
		uid = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.AXIS)
	end
	if not uid then return end
    if tonumber(uid) then 
        self:NotifyMapTagTrace(uid) 
        return 
    end
    self:NotifyMapTagTrace("CustomTrace_" .. uid)
end

function P_MapTagView:AddTag(TagData)
    self.widget:AddSingleTag(TagData)
end

function P_MapTagView:RemoveTag(TaskID)
    self.widget:RemoveSingleTag(TaskID)
end

function P_MapTagView:RetargetTagLocation(TaskID, Location)
    self.widget:RetargetTagLocation(TaskID, Location)
end

function P_MapTagView:GetWidgetPosByTaskID(TaskID)
    return self.widget:GetWidgetPosByTaskID(TaskID)
end

function P_MapTagView:RefreshGuildLeagueBattleTag(groupIDList)
    self.controlcomponents.GuildLeagueTagComponent:RefreshGuildLeagueBattleTag(groupIDList)
end

function P_MapTagView:FindTagDataByTaskID(TaskID)
    local ret = self.widget.MapTagInfoData:Find(TaskID)
    return ret
end

function P_MapTagView:InvokeTagEvent(taskID, eventName, ...)
	local uiComponent = self.TaskID2ComponentMap[taskID]
	if not uiComponent then
		Log.ErrorFormat("P_MapTagView:InvokeTagEvent: No component found for TaskID %s with event %s", taskID, eventName)
		return
	end
	return uiComponent[eventName](uiComponent, ...)
end

return P_MapTagView


--[[
目前打算的做法：lua侧生成一系列MapTag数据池，以TaskID为键，用于不同地图创建

C++侧复制池子里的数据，自己做View的创建


]]