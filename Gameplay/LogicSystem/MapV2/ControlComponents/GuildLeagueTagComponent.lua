local MapTagComponent = kg_require("Gameplay.LogicSystem.MapV2.ControlComponents.MapTagComponent")
---@type MapTagInfo
local MapTagInfo = import("MapTagInfo")
---@type EMapTagType
local EMapTagType = import("EMapTagType")
---@type EMapEdgeType
local EMapEdgeType = import("EMapEdgeType")

local GuildLeagueTag = "GuildLeagueTag_"
local GuildLeaguePlayerPosTag = "GuildLeaguePlayerPosTag_"

---@class GuildLeagueTagComponent: MapTagComponent
local GuildLeagueTagComponent = DefineClass("GuildLeagueTagComponent", MapTagComponent)

GuildLeagueTagComponent.TagType =
{
    Tower = "Tower",
    Monster = "Monster",
}

-- 玩家标记类型
GuildLeagueTagComponent.PlayerTagType =
{
	Enemy = "_enemy", --敌人
	Friend = "_friend", --友方
	Teammate = "_teammate", --团队
	Leader = "_leader", --团长
	Partner = "_partner", --队友
	Vehicle = "_vehicle", --载具
}

function GuildLeagueTagComponent:OnCreate()
    self.altarHandles = {}
    self.towerHandles = {}
    self.monsterHandles = {}
    self.playerPosTaskHandles = {}

    --当前小地图上的标记列表
    self.battleTagList = {}
    self.battleTagCounter = 0

    --判断是否在联赛指挥系统界面中，默认为nil，表示是HUD右上的区域
    self.bInGuildBattleMap = nil
end

function GuildLeagueTagComponent:OnDestroy()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end


function GuildLeagueTagComponent:OnAddTagID(groupIDList, tagID, absolutePosition)
    for k, groupID in pairs(groupIDList) do
        self:innerOnAddFlagID(groupID, tagID, absolutePosition)
    end
end

function GuildLeagueTagComponent:canShowGroupTag(groupID)
    local ret = false

    local hasLeagueAuthority = Game.GuildLeagueSystem:CheckHasLeagueAuthority()
    if hasLeagueAuthority then
        Log.DebugFormat("has authority, add tag")
        --拥有联赛指挥系统权限，则显示所有
        ret = true
    else
        --没有联赛指挥系统权限，判断是否是自己的队伍
        if groupID == GuildLeagueSystem.AllGROUPID then
            Log.DebugFormat("has no authority, %d ia all group, add tag", groupID)
            ret = true
        elseif groupID == Game.GroupSystem:GetGroupID() then
            Log.DebugFormat("has no authority, %d is my team, add tag", groupID)
            ret = true
        end
    end
    return ret
end

function GuildLeagueTagComponent:innerOnAddFlagID(groupID, tagID, absolutePosition)
    local GuildLeagueSystem = Game.GuildLeagueSystem
    local shouldAddTagUI = self:canShowGroupTag(groupID)

    if shouldAddTagUI then
        local TagData = self:BuildGroupBattleTag(groupID, tagID, absolutePosition)
        if TagData then
            self.parent:AddTag(TagData)
        end
    end
end

function GuildLeagueTagComponent:GetBattleTagInfo(taskID)
    return self.battleTagList[taskID]
end

function GuildLeagueTagComponent:OnRemoveTagID(groupIDList, tagID)
    for k, groupID in ipairs(groupIDList) do
        self:innerOnRemoveTagID(groupID, tagID)
    end
end

--某个团队的标记清空
function GuildLeagueTagComponent:OnGroupClearTags(groupID)
    Log.DebugFormat("GuildLeagueTagComponent:OnGroupClearTags, groupID = %s", groupID)
	local deleteList = {}
    for taskID, uiTagData in pairs(self.battleTagList) do
        if table.contains(uiTagData.groupIDList, groupID) then
            for k,v in ipairs(uiTagData.groupIDList) do
                if v == groupID then
					--Game.GuildLeagueSystem:OnRemoveFlag(uiTagData.groupIDList, uiTagData.tagID)
					--table.remove(uiTagData.groupIDList, k)
					deleteList[taskID] = k
                    break
                end
            end
            --if #uiTagData.groupIDList == 0 then
            --    Log.DebugFormat("uiTag is empty ,should remove now")
            --    self.parent:RemoveTag(taskID)
            --    self.battleTagList[taskID] = nil
            --else
            --    Log.DebugFormat("uiTag is count decrease ,still exist, current groupIDList is %s", 
            --            Game.GuildLeagueSystem:GetGroupIDListStr(uiTagData.groupIDList))
            --end
        end 
    end
	for taskID, k in pairs(deleteList) do
		local uiTagData = self.battleTagList[taskID]
		local GroupIDList = DeepCopy(uiTagData.groupIDList)
		table.remove(uiTagData.groupIDList, k)
		if #uiTagData.groupIDList == 0 then
			Game.GuildLeagueSystem:OnRemoveFlag(GroupIDList, uiTagData.tagID)
			-- 通知删除标点按钮显示
			--Game.EventSystem:Publish(_G.EEventTypes.ON_GUILD_LEAGURE_RET_REMOVE_FLAG_MARK, GroupIDList, uiTagData.tagID)
			Log.DebugFormat("uiTag is empty ,should remove now")
			self.parent:RemoveTag(taskID)
			self.battleTagList[taskID] = nil
		else
			Game.UniqEventSystemMgr:Publish(uiTagData.tagID, EEventTypesV2.ON_GUILD_LEAGUE_REFRESH_TAG, uiTagData.tagID) -- 刷新标点
			Log.DebugFormat("uiTag is count decrease ,still exist, current groupIDList is %s", 
					Game.GuildLeagueSystem:GetGroupIDListStr(uiTagData.groupIDList))
		end
	end
end


function GuildLeagueTagComponent:innerOnRemoveTagID(groupID, tagID)
    --尝试减少一个标记对应的groupID，如果减少完毕，则删除
    for taskID, uiTagData in pairs(self.battleTagList) do
        if uiTagData.tagID == tagID then
			local GroupIDList = DeepCopy(uiTagData.groupIDList)
            for i = #uiTagData.groupIDList, 1, -1 do
                if uiTagData.groupIDList[i] == groupID then
                    table.remove(uiTagData.groupIDList, i)
                    break
                end
            end
            if #uiTagData.groupIDList == 0 then
                Log.DebugFormat("uiTag is empty ,should remove now")
				-- 通知删除标点按钮显示
				Game.GlobalEventSystem:Publish(EEventTypesV2.ON_GUILD_LEAGUE_RET_REMOVE_FLAG_MARK, GroupIDList, uiTagData.tagID)
                self.parent:RemoveTag(taskID)
                self.battleTagList[taskID] = nil
            else
                Log.DebugFormat("uiTag is count decrease ,still exist, current groupIDList is %s", 
                        Game.GuildLeagueSystem:GetGroupIDListStr(uiTagData.groupIDList))
            end
        end 
    end 
end

function GuildLeagueTagComponent:getAllLeagueTagData(outMapTags)
    self:clearCurrentBattleTagList()
    local groupIDList = self:getGuildLeagueTagGroupIDList()
    local outBattleTags = self:BuildAllLeagueBattleTag(groupIDList)
    for k,v in pairs(outBattleTags) do
        outMapTags[k] = v
    end
end

function GuildLeagueTagComponent:GetMapTagData()
    local outMapTags = {}

    self:getAllLeagueTagData(outMapTags)
    
    local outAltarMapTags = self:BuildAltarData()
    for TaskID, TagData in pairs(outAltarMapTags) do
        self.altarHandles[TaskID] = true
        outMapTags[TaskID] = TagData
    end
    local towerMapTags = self:BuildTowerTagData()
    for TaskID, TagData in pairs(towerMapTags) do
        self.towerHandles[TaskID] = true
        outMapTags[TaskID] = TagData
    end
    local monsterMapTags = self:BuildMonsterTagData()
    for TaskID, TagData in pairs(monsterMapTags) do
        self.monsterHandles[TaskID] = true
        outMapTags[TaskID] = TagData
    end 
    local playerPosTags = self:BuildAllPlayerPosData()
    for TaskID, TagData in pairs(playerPosTags) do
        self.playerPosTaskHandles[TaskID] = true
        outMapTags[TaskID] = TagData
    end
	local portalMapTags = self:BuildPortalTagData()
	for TaskID, TagData in pairs(portalMapTags) do
		outMapTags[TaskID] = TagData
	end
    return outMapTags
end

function GuildLeagueTagComponent:BuildAltarData()
    local outMapTags = {}
    if not Game.me then return outMapTags end
    if self.parent.Name ~= "GuildMapTagLayer" or not Game.GuildLeagueSystem:IsInGuildLeague() then
        return outMapTags
    end
    for instanceID, eid in pairs(Game.GuildLeagueSystem.model.altarEntityIds) do
        local tagData = self:BuildAltarTag(instanceID)
        if tagData then
            outMapTags[tagData.TaskID] = tagData
        end
    end
    
    return outMapTags
end

function GuildLeagueTagComponent:BuildAltarTag(InsID)
    local typeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.GUILD_LEAGUE_ALTAR)
    local typeLibData = Game.TableData.GetMapTagLibDataTable()
    local TagData = MapTagInfo()
    TagData.TaskID = string.format("%s%s",GuildLeagueTag, InsID)
    TagData.MapTagType = EMapTagType.Static
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(InsID)
    if SceneActorData then
        TagData.StaticLocation = SceneActorData.Transform.Position
        TagData.TemplateWidgetType = Game.MapSystem:GetMapTagTypeClass(typeLibData[typeData.WidgetPath].WidgetPath)
        TagData.TypeID = typeData.WidgetPath
        TagData.ShowRatioInterval = FVector2D(typeData.ShowHideRatio[1], typeData.ShowHideRatio[2])
        TagData.ZOrder = typeData.Zorder
    else
        return
    end
    return TagData
end


function GuildLeagueTagComponent:BuildTowerTagData()
    local outMapTags = {}
    if not Game.me then return outMapTags end
    if self.parent.Name ~= "GuildMapTagLayer" or not Game.GuildLeagueSystem:IsInGuildLeague() then
        return outMapTags
    end
    for spawnerID, uid in pairs(Game.GuildLeagueSystem.model.towerEntityIds) do
        local tagData = self:BuildTowerTag(spawnerID)
        if tagData then
            outMapTags[tagData.TaskID] = tagData
        end
		local entity = Game.EntityManager:getEntity(uid)
		if entity then
			Game.EventSystem:AddListener(
				_G.EEventTypes.ON_IS_DEAD_CHANGED, self,
				function(new, old) self:OnTowerDead(spawnerID, new, old) end,
				entity.eid
			)
		end
    end
    return outMapTags
end

function GuildLeagueTagComponent:BuildTowerTag(InsID)
    local typeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.GUILD_LEAGUE)
    local typeLibData = Game.TableData.GetMapTagLibDataTable()
    local TagData = MapTagInfo()
    TagData.TaskID = string.format("%s%s",GuildLeagueTag, InsID)
    TagData.MapTagType = EMapTagType.Static
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(InsID)
    if SceneActorData then
        TagData.StaticLocation = SceneActorData.Transform.Position
        TagData.TemplateWidgetType = Game.MapSystem:GetMapTagTypeClass(typeLibData[typeData.WidgetPath].WidgetPath)
        TagData.TypeID = typeData.WidgetPath
        TagData.ShowRatioInterval = FVector2D(typeData.ShowHideRatio[1], typeData.ShowHideRatio[2])
        TagData.ZOrder = typeData.Zorder
    else
        return
    end
    return TagData
end

function GuildLeagueTagComponent:BuildMonsterTagData()
    local outMapTags = {}
    if not Game.me then return outMapTags end
    if self.parent.Name ~= "GuildMapTagLayer" or not Game.GuildLeagueSystem:IsInGuildLeague() then
        return outMapTags
    end
	local monsterData = Game.GuildLeagueSystem:GetMonsterSpawner2TimeStamp()
    for spawnerID, _ in pairs(monsterData) do
		local tagData = self:BuildMonsterTag(spawnerID)
		if tagData then
			outMapTags[tagData.TaskID] = tagData
		end
    end

    return outMapTags
end

function GuildLeagueTagComponent:BuildMonsterTag(spawnerID)
    local InsID = spawnerID
    local typeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.GUILD_LEAGUE_MONSTER)
    local typeLibData = Game.TableData.GetMapTagLibDataTable()
    local TagData = MapTagInfo()
    TagData.TaskID = string.format("%s%s",GuildLeagueTag, spawnerID)
    TagData.MapTagType = EMapTagType.Static
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(InsID)
    if SceneActorData then
        TagData.StaticLocation = SceneActorData.Transform.Position
        TagData.TemplateWidgetType = Game.MapSystem:GetMapTagTypeClass(typeLibData[typeData.WidgetPath].WidgetPath)
        TagData.TypeID = typeData.WidgetPath
        TagData.ShowRatioInterval = FVector2D(typeData.ShowHideRatio[1], typeData.ShowHideRatio[2])
        TagData.ZOrder = typeData.Zorder
    else
        return
    end
    return TagData
end

-- 传送门标点，一直存在，在地图上隐藏，标点或生成时显示
function GuildLeagueTagComponent:BuildPortalTagData()
	local outMapTags = {}
	if not Game.me then return outMapTags end
	if self.parent.Name ~= "GuildMapTagLayer" or not Game.GuildLeagueSystem:IsInGuildLeague() then
		return outMapTags
	end
	local selfCamp = Game.GuildLeagueSystem:GetSelfCampID()
	local Portals = nil
	if selfCamp == Enum.EGuildLeagueConstIntData.MULTI_PVP_CAMP_A_ID then
		Portals = Game.GuildLeagueSystem:GetEndPortalInstanceIDs(Enum.EGuildLeagueConstIntData.MULTI_PVP_CAMP_B_ID)
	elseif selfCamp == Enum.EGuildLeagueConstIntData.MULTI_PVP_CAMP_B_ID then
		Portals = Game.GuildLeagueSystem:GetEndPortalInstanceIDs(Enum.EGuildLeagueConstIntData.MULTI_PVP_CAMP_A_ID)
	else
		return outMapTags
	end
	for _, spawnerID in ipairs(Portals) do
		local tagData = self:BuildPortalTag(spawnerID)
		if tagData then
			outMapTags[tagData.TaskID] = tagData
		end
	end
	return outMapTags
end

function GuildLeagueTagComponent:BuildPortalTag(spawnerID)
	local InsID = spawnerID
	local typeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.GUILD_LEAGUE_PORTAL)
	local typeLibData = Game.TableData.GetMapTagLibDataTable()
	local TagData = MapTagInfo()
	TagData.TaskID = string.format("%s%s",GuildLeagueTag, spawnerID)
	TagData.MapTagType = EMapTagType.Static
	local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(InsID)
	if SceneActorData then
		TagData.StaticLocation = SceneActorData.Transform.Position
		TagData.TemplateWidgetType = Game.MapSystem:GetMapTagTypeClass(typeLibData[typeData.WidgetPath].WidgetPath)
		TagData.TypeID = typeData.WidgetPath
		TagData.ShowRatioInterval = FVector2D(typeData.ShowHideRatio[1], typeData.ShowHideRatio[2])
		TagData.ZOrder = typeData.Zorder
	else
		return
	end
	return TagData
end

function GuildLeagueTagComponent:BindEvent()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_MSG_OCCUPY_DETECT_AREA_READY_OPEN, "OnAltarReady", self)
	--Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_MSG_GUILD_LEAGUE_CREATE_RESOURCE_MONSTER, "OnMonsterCreate", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_GUILD_LEAGUE_RET_ADD_FLAG_MARK, "OnAddTagID", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_GUILD_LEAGUE_RET_REMOVE_FLAG_MARK, "OnRemoveTagID", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_GUILD_LEAGUE_GROUP_CLEAR_TAGS, "OnGroupClearTags", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_MSG_SYNC_GUILD_LEAGUE_MAP_INFO, "OnGetMapSyncInfo", self)
	--Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_GUILD_LEAGUE_RET_REMOVE_MONSTER_SPAWNER, "OnMonsterDead", self)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.ON_MSG_GUILD_LEAGUE_CREATE_TOWER, "OnTowerCreate", self)
end

function GuildLeagueTagComponent:OnTowerDead(spawnerID, new, old)
    local taskID = string.format("%s%s",GuildLeagueTag, spawnerID)
    if self.towerHandles[taskID] then
        self.parent:RemoveTag(taskID)
        self.towerHandles[taskID] = nil
    end
end

function GuildLeagueTagComponent:OnMonsterDead(instanceID)
    local taskID = string.format("%s%s",GuildLeagueTag, instanceID)
    if self.monsterHandles[taskID] then
        self.parent:RemoveTag(taskID)
        self.monsterHandles[taskID] = nil
    end
end

function GuildLeagueTagComponent:BuildAllPlayerPosData()
    local outMapTags = {}
    if not Game.me then return outMapTags end
    if self.parent.Name ~= "GuildMapTagLayer" or not Game.GuildLeagueSystem:IsInGuildLeague() then
        return outMapTags
    end
    local mapPlayerInfos = Game.GuildLeagueSystem.model.mapSyncInfos
    for index, posData in pairs(mapPlayerInfos.Enemy) do
        local tagData = self:BuildPlayerPosData(posData, GuildLeagueTagComponent.PlayerTagType.Enemy, index)
        if tagData then
            outMapTags[tagData.TaskID] = tagData
        end
    end
	
    for index, posData in pairs(mapPlayerInfos.Friend) do
        local tagData = self:BuildPlayerPosData(posData, GuildLeagueTagComponent.PlayerTagType.Friend, index)
        if tagData then
            outMapTags[tagData.TaskID] = tagData
        end
    end
	for index, posData in pairs(mapPlayerInfos.Teammate) do
		local tagData = self:BuildPlayerPosData(posData, GuildLeagueTagComponent.PlayerTagType.Teammate, index)
		if tagData then
			outMapTags[tagData.TaskID] = tagData
		end
	end
	for index, posData in pairs(mapPlayerInfos.Leader) do
		local tagData = self:BuildPlayerPosData(posData, GuildLeagueTagComponent.PlayerTagType.Leader, index)
		if tagData then
			outMapTags[tagData.TaskID] = tagData
		end
	end
	for index, posData in pairs(mapPlayerInfos.Partner) do
		local tagData = self:BuildPlayerPosData(posData, GuildLeagueTagComponent.PlayerTagType.Partner, index)
		if tagData then
			outMapTags[tagData.TaskID] = tagData
		end
	end
	for index, posData in pairs(mapPlayerInfos.Vehicle) do
		local tagData = self:BuildPlayerPosData(posData, GuildLeagueTagComponent.PlayerTagType.Vehicle, index)
		if tagData then
			outMapTags[tagData.TaskID] = tagData
		end
	end
	
    return outMapTags
end

function GuildLeagueTagComponent:BuildPlayerPosData(playerPos, type, index)
    Log.DebugFormat("x: %s  y: %s" , playerPos[1], playerPos[2])
    local typeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.PLAYER_TEAMMATE)
    local typeLibData = Game.TableData.GetMapTagLibDataTable()
    local TagData = MapTagInfo()
	TagData.TaskID = string.format("%s%s%s",GuildLeaguePlayerPosTag, type, index)
    TagData.MapTagType = EMapTagType.Static
    TagData.StaticLocation = FVector(playerPos[1], playerPos[2], 0)
    TagData.TemplateWidgetType = Game.MapSystem:GetMapTagTypeClass(typeLibData[typeData.WidgetPath].WidgetPath)
    TagData.TypeID = typeData.WidgetPath
    TagData.ShowRatioInterval = FVector2D(typeData.ShowHideRatio[1], typeData.ShowHideRatio[2])
    TagData.ZOrder = typeData.Zorder
    return TagData
end

function GuildLeagueTagComponent:OnGetMapSyncInfo()
    local newTags = self:BuildAllPlayerPosData()
    local Removed, Modify, Add = self:DiffData_PlayerPosTag(newTags)
    for TaskID, _ in pairs(Removed) do
        self.parent:RemoveTag(TaskID)
        self.playerPosTaskHandles[TaskID] = nil
    end
    for TaskID, TagData in pairs(Modify) do
        self.parent:RetargetTagLocation(TaskID, TagData.StaticLocation)
        self.playerPosTaskHandles[TaskID] = nil
    end
    for TaskID, TagData in pairs(Add) do
        self.parent:AddTag(TagData)
        self.playerPosTaskHandles[TaskID] = true
    end
end

function GuildLeagueTagComponent:DiffData_PlayerPosTag(NewTags)
    local Removed = {}
    local Modify = {}
    local Add = {}
    for TaskID, TagData in pairs(NewTags) do
        Add[TaskID] = TagData
    end
    for TaskID, _ in pairs(self.playerPosTaskHandles) do
        if Add[TaskID] then
            Modify[TaskID] = Add[TaskID]
            Add[TaskID] = nil
        else
            Removed[TaskID] = true
        end
    end
    return Removed, Modify, Add
end

function GuildLeagueTagComponent:DiffData_SceneTag(NewTags, handles)
    local Removed = {}
    local Modify = {}
    local Add = {}
    for TaskID, TagData in pairs(NewTags) do
        Add[TaskID] = TagData
    end
    for TaskID, _ in pairs(handles) do
        if Add[TaskID] then
            Modify[TaskID] = Add[TaskID]
            Add[TaskID] = nil
        else
            Removed[TaskID] = true
        end
    end
    return Removed, Modify, Add
end

function GuildLeagueTagComponent:OnAltarReady()
    local NewTags = self:BuildAltarData()
    local Removed, Modify, Add = self:DiffData_SceneTag(NewTags, self.altarHandles)
    for TaskID, _ in pairs(Removed) do
        self.parent:RemoveTag(TaskID)
    end
    for TaskID, TagData in pairs(Add) do
        self.parent:AddTag(TagData)
    end
    table.clear(self.altarHandles)
    for TaskID, TagData in pairs(NewTags) do
        self.altarHandles[TaskID] = true
    end
end

function GuildLeagueTagComponent:OnMonsterCreate()
    local NewTags = self:BuildMonsterTagData()
    local Removed, Modify, Add = self:DiffData_SceneTag(NewTags, self.monsterHandles)
    for TaskID, _ in pairs(Removed) do
        self.parent:RemoveTag(TaskID)
    end
    for TaskID, TagData in pairs(Add) do
        self.parent:AddTag(TagData)
    end
    table.clear(self.monsterHandles)
    for TaskID, TagData in pairs(NewTags) do
        self.monsterHandles[TaskID] = true
    end
end

function GuildLeagueTagComponent:OnTowerCreate()
	local NewTags = self:BuildTowerTagData()
	local Removed, _, Add = self:DiffData_SceneTag(NewTags, self.towerHandles)
	for TaskID, _ in pairs(Removed) do
		self.parent:RemoveTag(TaskID)
	end
	for _, TagData in pairs(Add) do
		self.parent:AddTag(TagData)
	end
	table.clear(self.towerHandles)
	for TaskID, _ in pairs(NewTags) do
		self.towerHandles[TaskID] = true
	end
end

function GuildLeagueTagComponent:RefreshGuildLeagueBattleTag(groupIDList)
    --刷新前先删除旧的
    self:clearCurrentBattleTagList()
    --添加新的
    local allTagData = self:BuildAllLeagueBattleTag(groupIDList)
    for TaskID, TagData in pairs(allTagData) do
        self.parent:AddTag(TagData)
    end
end

--groupIDList
function GuildLeagueTagComponent:BuildAllLeagueBattleTag(groupIDList)
    local outMapTags = {}

    for _, groupID in ipairs(groupIDList) do
        local shouldAddTagUI = self:canShowGroupTag(groupID)
        if shouldAddTagUI then
            local groupTagList = Game.GuildLeagueSystem:GetGroupBattleTagList(groupID)
            for _, tagInfo in pairs(groupTagList) do
                local tagData = self:BuildGroupBattleTag(groupID, tagInfo.index, tagInfo.position)
                if tagData then
                    outMapTags[tagData.TaskID] = tagData
                end
            end
        end
    end
    return outMapTags
end


function GuildLeagueTagComponent:BuildGroupBattleTag(groupID, tagID, absolutePosition)
    local GuildLeagueSystem = Game.GuildLeagueSystem 
    if not GuildLeagueSystem:CheckHasLeagueAuthority() then
        --如果没有指挥系统权限，则显示一个所有人团的标记时，判断是否有其他团的标记，如果有，则所有人团不添加标记
        if groupID == GuildLeagueSystem.AllGROUPID then
            local allLeagueGroup =  GuildLeagueSystem:GetAllLeagueGroupInfo()
            for k, leagueGroupInfo in ipairs(allLeagueGroup) do
                if leagueGroupInfo.groupID ~= GuildLeagueSystem.AllGROUPID then
                    local groupTagList = GuildLeagueSystem:GetGroupBattleTagList(leagueGroupInfo.groupID)
                    if groupTagList[tagID] then
                        Log.DebugFormat("has no authority, add try add a all group tagID %d, but group %d has this tag, so do not add ALLGROUP this tag"
                        ,tagID, leagueGroupInfo.groupID)
                        return
                    end
                end  
            end
        end
    end

    --检测这个标记，是否可以和现有的标记合并
    for TaskID, uiTagData in pairs(self.battleTagList) do
        local tempGroupID = uiTagData.groupIDList[1] --只要检测1个即可
        local tempBattleTagID = uiTagData.tagID
        local isEqual = Game.GuildLeagueSystem:CheckTwoTagIsEqual(tempGroupID, tempBattleTagID, groupID, tagID)
        if isEqual then
            Log.DebugFormat("groupID %d tagID %d is equal to groupID %d tagID %d, merge", 
                groupID, tagID, tempGroupID, tempBattleTagID)
            table.insert(uiTagData.groupIDList, groupID)

            local mapTagComponent = self.parent.TaskID2ComponentMap[TaskID] 
            if mapTagComponent then
                mapTagComponent:Refresh()
            end
            return
        end
    end

    local typeData = Game.TableData.GetTagTypeConfigDataRow(_G.Enum.ETagTypeConfigData.GUILD_LEAGUE_BATTLE_TAG)

    local typeLibData = Game.TableData.GetMapTagLibDataTable()

    local TagData = MapTagInfo()
    TagData.TaskID = self:CreateTeamTagUniqueID(groupID, tagID)
    TagData.MapTagType = EMapTagType.Static
    TagData.StaticLocation = FVector(absolutePosition[1],absolutePosition[2],absolutePosition[3])  
    TagData.TemplateWidgetType = Game.MapSystem:GetMapTagTypeClass(typeLibData[typeData.WidgetPath].WidgetPath)
    TagData.TypeID = typeData.WidgetPath
    TagData.ZOrder = typeData.Zorder
    
    local uiTagData = { tagData = TagData, tagID = tagID, groupIDList = {groupID}}
    self.battleTagList[TagData.TaskID] = uiTagData 
    return TagData
end

function GuildLeagueTagComponent:clearCurrentBattleTagList()
    for taskID, tagInfo in pairs(self.battleTagList) do
        self.parent:RemoveTag(taskID)
    end
    table.clear(self.battleTagList)
end

function GuildLeagueTagComponent:CreateTeamTagUniqueID(groupID, tagID)
    self.battleTagCounter = self.battleTagCounter + 1
    return string.format("%s%d_%d_%d",GuildLeagueTag, groupID, tagID, self.battleTagCounter)
end

function GuildLeagueTagComponent:RefreshMapTagData()
end

--联赛指挥系统中显示标记信息时，应该显示哪些组
function GuildLeagueTagComponent:getGuildLeagueTagGroupIDList()
    local GuildLeagueSystem = Game.GuildLeagueSystem
    local groupIDList = GuildLeagueSystem:GetHasTagGroupIDList()
    
    return groupIDList
end

return GuildLeagueTagComponent