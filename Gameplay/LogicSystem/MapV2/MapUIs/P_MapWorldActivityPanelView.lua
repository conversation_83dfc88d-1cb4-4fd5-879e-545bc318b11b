---@class WBP_MapPanelTagView : WBP_MapPanelTag_C
---@field public WidgetRoot WBP_MapPanelTag_C
---@field public Btn_ClickArea C7Button
---@field public EditText C7EditableTextBox
---@field public Btn_Yes C7Button

---@class EEventMapTagConditionParams
---@field public ConditionText string  条件文本
---@field public CurCount number  当前进度
---@field public NeedCount number  目标进度

---@class EEventMapTagParams
---@field public TaskID number 地图图标ID
---@field public IsRewarded boolean  奖励是否已领取
---@field public IsDone boolean  是否已完成
---@field public ButtonText string  按钮文本
---@field public NameText string  标题文本
---@field public DescribeText string 描述文本
---@field public ConditionInfo EEventMapTagConditionParams 条件信息
---@field public RewardData table 奖励物品信息
---@field public TagData table 标签信息
---@field public ImgIcon string 图标路径
---@field public ClickFunc function 按钮点击回调
---@field public viewportPosition table  当前控件中心的视口坐标

---@class P_MapWorldActivityPanelView : WBP_MapPanelTagView
---@field public controller P_MapWorldActivityPanel
local P_MapWorldActivityPanelView = DefineClass("P_MapWorldActivityPanelView", UIView)

function P_MapWorldActivityPanelView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
-- self.AnimationInfo = {AnimFadeIn = {{self.WidgetRoot,0.33335}, {self.BtnYes_lua.WidgetRoot, 0.6},},AnimFadeOut = {{self.WidgetRoot,0.03335}, }}
end

function P_MapWorldActivityPanelView:OnDestroy()
end

return P_MapWorldActivityPanelView
