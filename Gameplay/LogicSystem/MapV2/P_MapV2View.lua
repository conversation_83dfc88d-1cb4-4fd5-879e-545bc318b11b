---@class WBP_MapCoordinateView : WBP_MapCoordinate_C
---@field public WidgetRoot WBP_MapCoordinate_C
---@field public Button_X KGButton
---@field public TextX KGTextBlock
---@field public Button_Y KGButton
---@field public Text_Y KGTextBlock
---@field public Is Selected boolean
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Selected fun(self:self,Is Selected:boolean):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public bg KGImage
---@field public Text_Com KGTextBlock
---@field public Text_Time KGTextBlock
---@field public Image KGImage
---@field public Btn_Com KGButton
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein_normal WidgetAnimation
---@field public Ani_Fadein_Light WidgetAnimation
---@field public Ani_Fadein_blue WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public SequenceEvent fun(self:self):void
---@field public Construct fun(self:self):void
---@field public OnVisibilityChangedEvent fun(self:self,InVisibility:ESlateVisibility):void
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_MapExploreBtnView : WBP_MapExploreBtn_C
---@field public WidgetRoot WBP_MapExploreBtn_C
---@field public Text_Percent KGTextBlock
---@field public Btn_ClickArea KGButton


---@class WBP_MapInnerView : WBP_MapInner_C
---@field public WidgetRoot WBP_MapInner_C
---@field public CP_MapRoot CanvasPanel
---@field public BgImage KGImage
---@field public Scaler ScaleBox
---@field public MapImg KGImage
---@field public KGMapTraceWidget KGMapTraceWidget
---@field public C7MapTagLayer C7MapTagLayerV2
---@field public VB_Slection VerticalBox
---@field public Overlay_Layers Overlay
---@field public VB_layers VerticalBox
---@field public CP_NpcSearch CanvasPanel
---@field public WBP_ComBtnNpcSearch KGButton
---@field public WBP_MapCoordinate WBP_MapCoordinateView
---@field public WBP_ComBtn WBP_ComBtnView
---@field public WBP_MapExploreBtn WBP_MapExploreBtnView
---@field public Ani_UnClick WidgetAnimation
---@field public Ani_Click WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Fadeon WidgetAnimation
---@field public Tick fun(self:self,MyGeometry:Geometry,InDeltaTime:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_MapSmokeView : WBP_MapSmoke_C
---@field public WidgetRoot WBP_MapSmoke_C
---@field public Ani_Click_Smoke WidgetAnimation
---@field public Ani_UnClick WidgetAnimation


---@class WBP_ComSliderVerticalView : WBP_ComSliderVertical_C
---@field public WidgetRoot WBP_ComSliderVertical_C
---@field public Slider_Add KGButton
---@field public PB_Progress ProgressBar
---@field public Slider_ScaleValue Slider
---@field public Slider_Detele KGButton
---@field public Ani_SliderAdd WidgetAnimation
---@field public Ani_SliderDetele WidgetAnimation
---@field public OnValueChanged MulticastDelegate
---@field public StepValue number
---@field public UserDefaultFunction boolean
---@field public Construct fun(self:self):void
---@field public CheckBtnDisable fun(self:self):void
---@field public SetValue fun(self:self,InValue:number):void
---@field public SetStepValue fun(self:self,Value:number):void
---@field public BndEvt__WBP_ComInputVertical_Slider_Detele_lua_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComInputVertical_Slider_Add_lua_K2Node_ComponentBoundEvent_1_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComInputVertical_Slider_ScaleValue_lua_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature fun(self:self,Value:number):void


---@class WBP_MapView : WBP_Map_C
---@field public WidgetRoot WBP_Map_C
---@field public CP_MapInner CanvasPanel
---@field public WBP_MapInner WBP_MapInnerView
---@field public WorldMapPanel CanvasPanel
---@field public WBP_MapSmoke WBP_MapSmokeView
---@field public WBP_ComSliderVertical WBP_ComSliderVerticalView
---@field public CP_Panels CanvasPanel
---@field public Discovery_Panel CanvasPanel
---@field public CityName KGTextBlock
---@field public WBP_ComBtnBackArrow KGButton
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Fadeon WidgetAnimation
---@field public Construct fun(self:self):void

---@class P_MapV2View : WBP_MapView
---@field public controller P_MapV2
local P_MapV2View = DefineClass("P_MapV2View", UIView)

function P_MapV2View:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {{self.WidgetRoot,1.016683}, {self.WBP_MapInner_lua.WidgetRoot, 0.33335},{self.WBP_MapOrnament.WidgetRoot, 1.016683},},AnimFadeOut = {}}
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComBtnBackArrow, "OnClick_WBP_ComBtnBackArrow")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComSliderVertical.Slider_Detele, "OnClick_WBP_ComSliderVertical_Slider_Detele")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComSliderVertical.Slider_Add, "OnClick_WBP_ComSliderVertical_Slider_Add")
    controller:AddUIListener(EUIEventTypes.OnValueChanged, self.WBP_ComSliderVertical.Slider_ScaleValue, "OnValueChanged_WBP_ComSliderVertical_Slider_ScaleValue")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapInner.WBP_MapExploreBtn.Btn_ClickArea, "OnClick_WBP_MapInner_WBP_MapExploreBtn_Btn_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapInner.WBP_ComBtnNpcSearch, "OnClick_WBP_MapInner_WBP_ComBtnNpcSearch")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapInner.WBP_ComBtn.Btn_Com, "OnClick_WBP_MapInner_WBP_ComBtn_Btn_Com")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapInner.WBP_MapCoordinate.Button_Y, "OnClick_WBP_MapInner_WBP_MapCoordinate_Button_Y")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapInner.WBP_MapCoordinate.Button_X, "OnClick_WBP_MapInner_WBP_MapCoordinate_Button_X")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapInner.Btn_ClickArea, "OnClick_WBP_MapInner_Btn_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_MapInner.WBP_MapList.Btn_ClickArea, "OnClick_WBP_MapInner_WBP_MapList_Btn_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_ClickArea, "OnClick_WBP_Map_Btn_ClickArea")
end

function P_MapV2View:OnDestroy()
end

return P_MapV2View
