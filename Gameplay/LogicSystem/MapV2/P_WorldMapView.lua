---@class WBP_MapBigView : WBP_MapBig_C
---@field public WidgetRoot WBP_MapBig_C
---@field public CP_WorldMap CanvasPanel
---@field public Overlay_BeckLand CanvasPanel
---@field public Img_maskBeckLand KGImage
---@field public CP_MapTag_BeckLand C7MapTagLayer
---@field public Overlay_Tiengen CanvasPanel
---@field public Img_MaskTiengen KGImage
---@field public CP_MapTag_TienGen C7MapTagLayer
---@field public Btn_Tiengen KGButton
---@field public CP_Tiengen_VX CanvasPanel
---@field public Overlay_PirateSchool CanvasPanel
---@field public Img_MaskPirateSch KGImage
---@field public CP_MapTag_PirateSchool C7MapTagLayer
---@field public Btn_PirateSchool KGButton
---@field public CP_PirateSchool_VX CanvasPanel
---@field public Tag_BeckLand CanvasPanel
---@field public Tag_PirateSchool CanvasPanel
---@field public Tag_Tiengen CanvasPanel
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Fadeon WidgetAnimation
---@field public Ani_Tab WidgetAnimation
---@field public Ani_MapBig_Switch WidgetAnimation
---@field public Ani_Map_Tips WidgetAnimation
---@field public Ani_Map_Click_Tiengen WidgetAnimation
---@field public Ani_Map_Click_PirateSchool WidgetAnimation
---@field public Ani_MapBig_Switch_in WidgetAnimation
---@field public Ani_Map_Hover_PirateSchool WidgetAnimation
---@field public Ani_Map_Hover_Tiengen WidgetAnimation
---@field public Ani_Map_UnHover_PirateSchool WidgetAnimation
---@field public Ani_Map_UnHover_Tiengen WidgetAnimation

---@class P_WorldMapView : WBP_MapBigView
---@field public controller P_WorldMap
local P_WorldMapView = DefineClass("P_WorldMapView", UIView)

function P_WorldMapView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {{self.WidgetRoot,1.083333}, },AnimFadeOut = {}}
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_PirateSchool, "OnClick_Btn_PirateSchool")
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_Tiengen, "OnClick_Btn_Tiengen")
    controller:AddUIListener(EUIEventTypes.CLICK, self.BackGroundBtn, "OnClick_BackGroundBtn")
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_ClickArea, "OnClick_Btn_ClickArea")
    controller:AddUIListener(EUIEventTypes.Unhovered, self.Btn_PirateSchool, "OnUnhovered_Btn_PirateSchool")
    controller:AddUIListener(EUIEventTypes.Hovered, self.Btn_PirateSchool, "OnHovered_Btn_PirateSchool")
    controller:AddUIListener(EUIEventTypes.Unhovered, self.Btn_Tiengen, "OnUnhovered_Btn_Tiengen")
    controller:AddUIListener(EUIEventTypes.Hovered, self.Btn_Tiengen, "OnHovered_Btn_Tiengen")
end

function P_WorldMapView:OnDestroy()
end

return P_WorldMapView
