local MapInputComponent = kg_require("Gameplay.LogicSystem.MapV2.MapInputComponent")
---@class P_WorldMap : UIController
---@field public View WBP_MapBigView
local P_WorldMap = DefineClass("P_WorldMap", UIController, MapInputComponent)

local ESlateVisibility = import("ESlateVisibility")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

local MapX = 2414
local MapY = 1080

local TimesToScrollFromMinToMax = 2
local WorldMapMoveToSpeed = 50

function P_WorldMap:OnCreate()
    self:BindEvent(self.View.WidgetRoot)
    self.MapRegionsMap = {
        [Enum.ELevelMapData.LV_Tiengen_P] = {
            TagCanvas = self.View.CP_MapTag_TienGen,
            CanvasContent = self.View.Overlay_Tiengen,
            ImgMask = self.View.Img_MaskTiengen
        },
        [Enum.ELevelMapData.LV_Beckland_P] = {
            TagCanvas = self.View.CP_MapTag_BeckLand,
            CanvasContent = self.View.Overlay_BeckLand,
            ImgMask = self.View.Img_maskBeckLand
        },
        [Enum.ELevelMapData.LV_Pirate_School_P] = {
            TagCanvas = self.View.CP_MapTag_PirateSchool,
            CanvasContent = self.View.Overlay_PirateSchool,
            ImgMask = self.View.Img_MaskPirateSch
        }

    }
end

function P_WorldMap:GetCenterMapConfigID()
	local CenterPos = self:GetPinchStartPos()
	if not CenterPos then
		local VPosition, LocalSize = self:GetWidgetViewPositionAndSize(self.View.CP_WorldMap)
		CenterPos = VPosition - LocalSize / 2
	end
	local ClosestLevelID, MinDis = nil, 100000

    for LevelID, V in pairs(self.MapRegionsMap) do
		local VPosition, LocalSize = self:GetWidgetViewPositionAndSize(V.CanvasContent)
		local WidgetPos = VPosition - LocalSize / 2
		local Dis = (WidgetPos - CenterPos):Size()
		if LocalSize:Size() > 5 and Dis < MinDis then
			MinDis = (WidgetPos - CenterPos):Size()
			ClosestLevelID = LevelID
		end
    end
	return ClosestLevelID
end

function P_WorldMap:GetWidgetViewPositionAndSize(Widget)
	local cachedGeometry = Widget:GetCachedGeometry()
	local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
	local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
		_G.GetContextObject(), cachedGeometry, localSize, nil, nil
	)
	return viewportPosition, localSize
end

function P_WorldMap:OnRefresh(CenterLevelID)
    self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_MapBig_Switch_in)
    self.CurrentRatio = 0.3
    self.MapCenter = FVector2D(MapX / 2, MapY / 2)
    self.parent.View.CityName:SetText("世界地图")
    self.bLocalMapClicked = false
    if CenterLevelID and self.MapRegionsMap[CenterLevelID] then
        local Anchors = self.MapRegionsMap[CenterLevelID].CanvasContent.Slot:GetAnchors()
        self.MapCenter = FVector2D((Anchors.Minimum.X + Anchors.Maximum.X) / 2 * MapX, (Anchors.Minimum.Y + Anchors.Maximum.Y) / 2 * MapY)
    end
    if self.MapCenter.X / MapX <= 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * 0.5 * self.CurrentRatio
    elseif self.MapCenter.X / MapX >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * (1 - 0.5 * self.CurrentRatio)
    end
    if self.MapCenter.Y / MapY <= 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * 0.5 * self.CurrentRatio
    elseif self.MapCenter.Y / MapY >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * (1 - 0.5 * self.CurrentRatio)
    end
    self.View.CP_WorldMap.Slot:SetAlignment(FVector2D(
        self.MapCenter.X / MapX,
        self.MapCenter.Y / MapY
    ))
    self.View.CP_WorldMap.Slot:SetSize(
        FVector2D(
            1 / self.CurrentRatio * MapX, 1 / self.CurrentRatio * MapY
        )
    )

    self.RecordClickUI = nil
    --self.View.CP_PirateSchool_VX:SetVisibility(ESlateVisibility.Hidden)
    --self.View.CP_Tiengen_VX:SetVisibility(ESlateVisibility.Hidden)
end

function P_WorldMap:OnHide()
end

function P_WorldMap:TryOpenLocalMap()
    local MapLevelID = self:GetCenterMapConfigID()
    local ProjectData = Game.TableData.Get_LevelLayerID2MapDataMap()
    if MapLevelID and ProjectData[MapLevelID] then
        self:CloseSelfAndSwitchLocalMap(
            ProjectData[MapLevelID][0].ID, MapLevelID
        )
    end
end

function P_WorldMap:OnMapRatioChanged(Delta)
    --local NewRatio = self.CurrentRatio + Delta * 7 / 3

    local NewRatio = self.CurrentRatio + Delta * 7 / 3 / TimesToScrollFromMinToMax

    if NewRatio <= 0.3 then
        self:TryOpenLocalMap()
        self.parent.View.WBP_ComSliderVertical.PB_Progress:SetPercent(0.3)
        self.parent.View.WBP_ComSliderVertical.Slider_ScaleValue:SetValue(0.3)
        self.parent.View.WBP_ComSliderVertical.WidgetRoot:CheckBtnDisable()
        return
    elseif NewRatio > 1.0 then
        return
    end
    self.CurrentRatio = NewRatio
    if self.MapCenter.X / MapX <= 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * 0.5 * self.CurrentRatio
    elseif self.MapCenter.X / MapX >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * (1 - 0.5 * self.CurrentRatio)
    end
    if self.MapCenter.Y / MapY <= 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * 0.5 * self.CurrentRatio
    elseif self.MapCenter.Y / MapY >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * (1 - 0.5 * self.CurrentRatio)
    end
    self.View.CP_WorldMap.Slot:SetAlignment(FVector2D(
        self.MapCenter.X / MapX,
        self.MapCenter.Y / MapY
    ))
    self.View.CP_WorldMap.Slot:SetSize(
        FVector2D(
            1 / self.CurrentRatio * MapX, 1 / self.CurrentRatio * MapY
        )
    )
    local ConvertedRatio = (1 - self.CurrentRatio) / (1 - 0.3) * 0.3
    self.parent.View.WBP_ComSliderVertical.PB_Progress:SetPercent(ConvertedRatio)
    self.parent.View.WBP_ComSliderVertical.Slider_ScaleValue:SetValue(ConvertedRatio)

end

function P_WorldMap:GetConvertedPercentageFromView(Percentage)
    return Percentage / 0.3
end

function P_WorldMap:OnValueChangedScaleValue(Percentage)
    local ConvertedPercent = self:GetConvertedPercentageFromView(Percentage)
    local NewRatio
    if ConvertedPercent > 1 then
        self:TryOpenLocalMap()
        self.parent.View.WBP_ComSliderVertical.PB_Progress:SetPercent(0.3)
        self.parent.View.WBP_ComSliderVertical.Slider_ScaleValue:SetValue(0.3)
        self.parent.View.WBP_ComSliderVertical.WidgetRoot:CheckBtnDisable()
        if self.CurrentRatio == 0.3 then
            return
        end
        NewRatio = 0.3
    end
    NewRatio = 0.3 * ConvertedPercent + (1 - ConvertedPercent)
    if NewRatio < 0.3 or NewRatio > 1 then return end
    self.CurrentRatio = NewRatio
    if self.MapCenter.X / MapX <= 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * 0.5 * self.CurrentRatio
    elseif self.MapCenter.X / MapX >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * (1 - 0.5 * self.CurrentRatio)
    end
    if self.MapCenter.Y / MapY <= 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * 0.5 * self.CurrentRatio
    elseif self.MapCenter.Y / MapY >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * (1 - 0.5 * self.CurrentRatio)
    end
    self.View.CP_WorldMap.Slot:SetAlignment(FVector2D(
        self.MapCenter.X / MapX,
        self.MapCenter.Y / MapY
    ))
    self.View.CP_WorldMap.Slot:SetSize(
        FVector2D(
            1 / self.CurrentRatio * MapX, 1 / self.CurrentRatio * MapY
        )
    )
end


function P_WorldMap:OnMapCenterChanged(Delta)
    self.MapCenter = self.MapCenter + Delta
    if self.MapCenter.X / MapX <= 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * 0.5 * self.CurrentRatio
    elseif self.MapCenter.X / MapX >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.X = MapX * (1 - 0.5 * self.CurrentRatio)
    end
    if self.MapCenter.Y / MapY <= 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * 0.5 * self.CurrentRatio
    elseif self.MapCenter.Y / MapY >= 1 - 0.5 * self.CurrentRatio then
        self.MapCenter.Y = MapY * (1 - 0.5 * self.CurrentRatio)
    end
    self.View.CP_WorldMap.Slot:SetAlignment(FVector2D(
        self.MapCenter.X / MapX,
        self.MapCenter.Y / MapY
    ))
end

function P_WorldMap:OnClickMapAtLocation(CoordX, CoordY)
end

function P_WorldMap:OnClick_Btn_Tiengen()
    if self.RecordClickUI ~= Enum.ELevelMapData.LV_Tiengen_P then
        self.RecordClickUI = Enum.ELevelMapData.LV_Tiengen_P
        self:StopAllAnimations(self.View.WidgetRoot)
        self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_Map_Click_Tiengen)
        self:MoveCenterTo(Enum.ELevelMapData.LV_Tiengen_P)
    else
        self:StopAllAnimations(self.View.WidgetRoot)
        self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_Map_Click_Tiengen, nil, nil, function()
        local MapLevelID = Enum.ELevelMapData.LV_Tiengen_P
        local ProjectData = Game.TableData.Get_LevelLayerID2MapDataMap()
        if MapLevelID and ProjectData[MapLevelID] then
            self:CloseSelfAndSwitchLocalMap(
                ProjectData[MapLevelID][0].ID, MapLevelID
            )
        end
    end)
    end
end

function P_WorldMap:OnClick_Btn_PirateSchool()
    if self.RecordClickUI ~= Enum.ELevelMapData.LV_Pirate_School_P then
        self.RecordClickUI = Enum.ELevelMapData.LV_Pirate_School_P
        self:StopAllAnimations(self.View.WidgetRoot)
        self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_Map_Click_PirateSchool)
        self:MoveCenterTo(Enum.ELevelMapData.LV_Pirate_School_P)
    else
        self:StopAllAnimations(self.View.WidgetRoot)
        self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_Map_Click_PirateSchool, nil, nil, function()
        local MapLevelID = Enum.ELevelMapData.LV_Pirate_School_P
        local ProjectData = Game.TableData.Get_LevelLayerID2MapDataMap()
        if MapLevelID and ProjectData[MapLevelID] then
            self:CloseSelfAndSwitchLocalMap(
                ProjectData[MapLevelID][0].ID, MapLevelID
            )
        end
        self.bLocalMapClicked = false
        end)
    end
end

function P_WorldMap:CloseSelfAndSwitchLocalMap(MapConfigID, LevelMapID)
    --if self.View.WidgetRoot:IsAnyAnimationPlaying() then return end
    local MapConfigData = Game.TableData.GetMapDataRow(MapConfigID)
    UI.Invoke("P_MapV2", "SwitchMap", {
        MapConfigID = MapConfigID,
        LevelMapID = LevelMapID,
        OverrideRatio = 1 / MapConfigData.MapMinScale
    })
    self:PlayAnimationForward(
        self.View.WidgetRoot, self.View.Ani_MapBig_Switch, nil, nil,
        function()
            self:CloseSelf()
        end
    )
end

function P_WorldMap:MoveCenterTo(CenterEnum)
    self:StopTimer("WorldMap_MoveCenter")
    ---@type CanvasPanelSlot
    local CanvasSlot = self.MapRegionsMap[CenterEnum].CanvasContent.Slot
    ---@type Anchors
    local Anchor = CanvasSlot:GetAnchors()
    ---@type Margin
    local Margin = CanvasSlot:GetOffsets()
    
    local TargetLocation = FVector2D()
    TargetLocation.X = (Anchor.Maximum.X + Anchor.Minimum.X) * MapX / 2  - Margin.Right / 2 + Margin.Left / 2-- + ((Anchor.Maximum.X * MapX + Margin.Right) - (Anchor.Minimum.X * MapX + Margin.Left)) / 2
    TargetLocation.Y = (Anchor.Maximum.Y + Anchor.Minimum.Y) * MapY / 2  - Margin.Bottom / 2 + Margin.Top / 2--(Anchor.Maximum.Y * MapY + Margin.Bottom) + ((Anchor.Maximum.Y * MapY + Margin.Bottom) - (Anchor.Minimum.X * MapY + Margin.Top)) / 2
    local Unit = (TargetLocation - self.MapCenter) * (1 / WorldMapMoveToSpeed)

    self:StartTimer("WorldMap_MoveCenter", function()
    self:OnMapCenterChanged(Unit)
    end, 1, WorldMapMoveToSpeed)
end


function P_WorldMap:OnHovered_Btn_Tiengen()
    self:StopAnimation(self.View.WidgetRoot, self.View.Ani_Map_UnHover_Tiengen)
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Map_Hover_Tiengen)
end

function P_WorldMap:OnUnhovered_Btn_Tiengen()
    self:StopAnimation(self.View.WidgetRoot, self.View.Ani_Map_Hover_Tiengen)
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Map_UnHover_Tiengen)
end

function P_WorldMap:OnHovered_Btn_PirateSchool()
    self:StopAnimation(self.View.WidgetRoot, self.View.Ani_Map_UnHover_PirateSchool)
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Map_Hover_PirateSchool)
end

function P_WorldMap:OnUnhovered_Btn_PirateSchool()
    self:StopAnimation(self.View.WidgetRoot, self.View.Ani_Map_Hover_PirateSchool)
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Map_UnHover_PirateSchool)
end

function P_WorldMap:OnClick_BackGroundBtn()
    self.RecordClickUI = nil
    if not self.bInClickBackGroundCD then
        self.bInClickBackGroundCD = true
        self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_Map_Click_OtherAreas)
        self:StartTimer("ClickBackGroundCD", function()
        self.bInClickBackGroundCD = false    
    end, 5000, 1)
    end
end

function P_WorldMap:OnClick_Btn_ClickArea()
    self:PlayAnimationForward(self.View.WidgetRoot, self.View.Ani_Click)
    local LevelID = Game.LevelManager.GetCurrentLevelID()
    if self.MapRegionsMap[LevelID] then
        self.RecordClickUI = LevelID
        self:MoveCenterTo(LevelID)
    end
end

return P_WorldMap
