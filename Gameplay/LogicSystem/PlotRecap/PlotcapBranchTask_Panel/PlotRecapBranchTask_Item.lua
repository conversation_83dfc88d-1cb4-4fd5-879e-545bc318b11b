local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local PlotRecapConst = kg_require("Shared.Const.PlotRecapConst")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
---@class PlotRecapBranchTask_Item : UIListItem
---@field view PlotRecapBranchTask_ItemBlueprint
local PlotRecapBranchTask_Item = DefineClass("PlotRecapBranchTask_Item", UIListItem)

PlotRecapBranchTask_Item.eventBindMap = {
    [EEventTypes.PLOTRECAP_ONE_INFO_NOTIFY_COMPLETE] = "RefreshUnlockState",
    [EEventTypes.PLOTRECAP_ONE_REWARD_GET] = "RefreshRewardState"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlotRecapBranchTask_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlotRecapBranchTask_Item:InitUIData()
    self.state = nil
    self.rewarded = true
end

--- UI组件初始化，此处为自动生成
function PlotRecapBranchTask_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlotRecapBranchTask_Item:InitUIEvent()
    self:AddUIEvent(self.view.BTN_ClickArea.OnClicked, "on_BTN_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlotRecapBranchTask_Item:InitUIView()
end

---面板打开的时候触发
function PlotRecapBranchTask_Item:OnRefresh(data)
    self.data = data
    self.view.Text_Name:SetText(data.OtherInfo.TitleDesc)
    self.state = Game.PlotRecapSystem:GetPlotRecapState(self.data.OtherInfo.ID)
    self.rewarded = Game.PlotRecapSystem:CheckPlotRecapRewarded(self.data.OtherInfo.ID)
    self:RefreshState()
end

function PlotRecapBranchTask_Item:RefreshState()
    local styeIndex = 2

    local bShowSelected = self.data.IsSelected
    if self.state == PlotRecapConst.PLOT_RECAP_STATE.UNLOCK then
        styeIndex = 0
    elseif self.state == PlotRecapConst.PLOT_RECAP_STATE.WAIT_SUBMIT then
        styeIndex = 1
    else
        styeIndex = 2
        bShowSelected = false
    end
    self.userWidget:Event_UI_Style(bShowSelected, styeIndex)
    Game.RedPointSystem:RegisterRedPoint(self:GetBelongPanel(), self.userWidget, "PlotRecapItem", self.data.OtherInfo.ID)
end


--- 此处为自动生成
function PlotRecapBranchTask_Item:on_BTN_ClickArea_Clicked()
    if self.state == PlotRecapConst.PLOT_RECAP_STATE.LOCK or self.state == nil then
        local taskID = self.data.OtherInfo.ConditionAst.ConditionFuncInfo.FuncArgInfos[1]
        local taskRingID = Game.QuestSystem:GetRingIDByQuestID(taskID)
        local ringCfg = Game.QuestSystem:GetRingExcelCfg(taskRingID)
        if ringCfg ~= nil then 
            local chapterCfg = Game.QuestSystem:GetChapterExcelCfg(ringCfg.ChapterID)
            if chapterCfg ~= nil then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PLOTRECAP_UNLOCK, {{chapterCfg.ChapterName}})
            end
        end
        return
    elseif self.state == PlotRecapConst.PLOT_RECAP_STATE.WAIT_SUBMIT then
        Game.PlotRecapSystem:ReqCommitPlotRecapUnlock(self.data.OtherInfo.ID)
    else
        self:OpenBranchInfoListPanel()
    end
end

function PlotRecapBranchTask_Item:RefreshUnlockState()
    local state = Game.PlotRecapSystem:GetPlotRecapState(self.data.OtherInfo.ID)
    if self.state == PlotRecapConst.PLOT_RECAP_STATE.WAIT_SUBMIT and state == PlotRecapConst.PLOT_RECAP_STATE.UNLOCK then
        function finishCallback()
            self.state = state
            self:RefreshState()
            if self.rewarded == false then
                Game.PlotRecapSystem:ReqOnePlotRecapReward(self.data.OtherInfo.ID)
            end
        end
        self:PlayAnimation(self.view.Ani_unlock, finishCallback, self.view.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function PlotRecapBranchTask_Item:OpenBranchInfoListPanel()
    if self.data.OtherInfo.PlotRecapType == "MistSideRecap" or self.data.OtherInfo.PlotRecapType == "FactSideRecap" then
        local listParent = self:GetParent()
        local branchListParent = listParent:GetParent()
        local containerParent = branchListParent:GetParent()
    
        local data =
        {
            prePanel = containerParent._curCellPanel,
            curPanel = UICellConfig.PlotRecapBranchInfoList,
            questID = self.data.OtherInfo.ID
        }
        
        containerParent:CloseComponent(UICellConfig.PlotRecapBranchTaskList, false)
        containerParent._curCellPanel = UICellConfig.PlotRecapBranchInfoList
        containerParent:OpenComponent(UICellConfig.PlotRecapBranchInfoList, containerParent.view.WBP_Child, data)
        Game.EventSystem:Publish(EEventTypes.PlOTRECAP_QUEST_INFO_OPEN)
    end
end

function PlotRecapBranchTask_Item:RefreshRewardState()
    self.rewarded = true
end

return PlotRecapBranchTask_Item
