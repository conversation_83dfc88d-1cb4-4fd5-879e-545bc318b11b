---@class PlotRecapSender
local PlotRecapSender = DefineClass("PlotRecapSender", SystemSenderBase)

-- 获取全量的剧情回顾的信息
function PlotRecapSender:ReqFetchAllPlotRecapInfo()
    self.Bridge:ReqFetchAllPlotRecapInfo()
end


-- 获取某个的剧情回顾的信息
function PlotRecapSender:ReqFetchOnePlotRecapInfo(plotRecapID)
    self.Bridge:ReqFetchOnePlotRecapInfo(plotRecapID)
end


-- 手动解锁剧情
function PlotRecapSender:ReqCommitPlotRecapUnlock(plotRecapID)
    self.Bridge:ReqCommitPlotRecapUnlock(plotRecapID)
end


-- 获取某个剧情回顾的奖励
function PlotRecapSender:ReqOnePlotRecapReward(plotRecapID)
    self.Bridge:ReqOnePlotRecapReward(plotRecapID)
end


-- 一键领取某个子类型已完成的迷雾奖励
function PlotRecapSender:ReqAllPlotRecapReward(plotRecapType)
    self.Bridge:ReqAllPlotRecapReward(plotRecapType)
end


-- 领取某个剧情大类某个等级的奖励
function PlotRecapSender:ReqOnePlotRecapLevelReward(recapMainType, index)
    self.Bridge:ReqOnePlotRecapLevelReward(recapMainType, index)
end


-- 领取大类型所有等级的奖励
function PlotRecapSender:ReqAllPlotRecapLevelReward(recapMainTyp)
    self.Bridge:ReqAllPlotRecapLevelReward(recapMainTyp)
end

-- 获取某个大类的信息
function PlotRecapSender:ReqOnePlotRecapMainTypeInfo(recapMainType)
    self.Bridge:ReqOnePlotRecapMainTypeInfo(recapMainType)
end


return PlotRecapSender

