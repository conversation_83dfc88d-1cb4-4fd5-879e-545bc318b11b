local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UI_DiyText = kg_require("Framework.KGFramework.KGUI.Component.Tools.UI_DiyText")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PlotcapMainLinePanel_Background : UIPanel
---@field view PlotcapMainLinePanel_BackgroundBlueprint
local PlotcapMainLinePanel_Background = DefineClass("PlotcapMainLinePanel_Background", UIPanel)

PlotcapMainLinePanel_Background.eventBindMap = {
    [EEventTypes.PlOTRECAP_MIST_DROPDOWN_FRESH] = "RefreshDropDownMenu"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlotcapMainLinePanel_Background:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlotcapMainLinePanel_Background:InitUIData()
    self._chapterData = {}
    self._filterData = {}
    self._maxChapter = 0
    self._curChapter = 0
end

--- UI组件初始化，此处为自动生成
function PlotcapMainLinePanel_Background:InitUIComponent()
    ---@type UIListView
    self.KList_NodeCom = self:CreateComponent(self.view.KList_Node, UIListView)
    ---@type UIComDropDown
    self.WBP_PlotRecapClassChangeBtnCom = self:CreateComponent(self.view.WBP_PlotRecapClassChangeBtn, UIComDropDown)
    ---@type UI_DiyText
    self.WBP_PlotRecapClassChangeTitle_WBP_DIYTextCom = self:CreateComponent(self.view.WBP_PlotRecapClassChangeTitle.WBP_DIYText, UI_DiyText)
end

---UI事件在这里注册，此处为自动生成
function PlotcapMainLinePanel_Background:InitUIEvent()
    self:AddUIEvent(self.WBP_PlotRecapClassChangeBtnCom.onItemSelected, "on_WBP_PlotRecapClassChangeBtnCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlotcapMainLinePanel_Background:InitUIView()
end

---面板打开的时候触发
function PlotcapMainLinePanel_Background:OnRefresh(data)
    self.data = data
    self._curChapter = 1
    self._maxChapter = #data
    self.WBP_PlotRecapClassChangeBtnCom:Refresh(self:GetFilterTypeData(), self._curChapter, true)
    self:RefreshChapterData(self.data[1].Info.Key)
    self.KList_NodeCom:Refresh(self:GetItemListData())
end

function PlotcapMainLinePanel_Background:RefreshChapterData(Chapter)
    self._chapterData = {}
    local plotRecapData = Game.TableData.GetPlotRecapDataTable()
    local index = 1
    for _, info in ksbcpairs(plotRecapData) do
        if info.ChapterBelong == Chapter then
            local elementData = 
            {
                Index = index,
                OtherInfo = info
            }
            table.insert(self._chapterData, elementData)
            index = index + 1
        end
    end
end

function PlotcapMainLinePanel_Background:GetFilterTypeData()
    local data = {}
    self._filterData = {}
    for _, info in pairs(self.data) do
        local elementData = 
        {
            name = info.Info.StringValueList[1]
        }
        table.insert(data, elementData)
        local filterData = 
        {
            key = info.Info.Key,
            name = info.Info.StringValueList[1],
            desc = info.Info.StringValueList[2],
        }
        table.insert(self._filterData, filterData)
    end
    return data
end

function PlotcapMainLinePanel_Background:GetItemListData()
    local itemData = {}
    local curNodeData = nil
    local index = 0
    local itemIndex = 0

    local sortedData = self:SortNode(self._chapterData)
    for _, info in pairs(sortedData) do
        if index % 8 == 0 then
            local newNodeData = {}
            itemIndex = itemIndex + 1
            local elementData = 
            {
                Index = index + 1,
                ItemIndex = itemIndex,
                IsNextChapter = false,
                Chapter = self._curChapter,
                OtherInfo = info.OtherInfo
            }
            table.insert(newNodeData, elementData)
            curNodeData = newNodeData
            table.insert(itemData, curNodeData)
        else
            local elementData = 
            {
                Index = index + 1,
                ItemIndex = itemIndex,
                IsNextChapter = false,
                Chapter = self._curChapter,
                OtherInfo = info.OtherInfo
            }
            table.insert(curNodeData, elementData)
        end
        index = index + 1
    end

    local lastNodeData = 
    {
        Index = index + 1,
        ItemIndex = itemIndex,
        IsNextChapter = true,
        Chapter = self._curChapter,
        OtherInfo = nil
    }

    local itemNum = #itemData
    if self._curChapter < self._maxChapter then 
        if #itemData[itemNum] < 8 then
            table.insert(itemData[itemNum], lastNodeData)
        else
            local newNodeData = {}
            lastNodeData.ItemIndex = itemIndex + 1
            table.insert(newNodeData, lastNodeData)
            table.insert(itemData, newNodeData)
        end
    end

    return itemData
end

function PlotcapMainLinePanel_Background:SortNode(inData)
    local sortedData = {}
    local firstData = nil

    for _, tempData in pairs(inData) do
        if tempData.OtherInfo.DependPoint == 0 then
            firstData = tempData
            table.insert(sortedData, firstData)
            break
        end
    end

    local endSort = false
    local curData = firstData
    while endSort ~= true do
        endSort = true
        for _, tempData in pairs(inData) do
            if tempData.OtherInfo.DependPoint == curData.OtherInfo.ID then
                curData = tempData
                table.insert(sortedData, tempData)
                endSort = false
            end
        end
    end

    return sortedData
end


--- 此处为自动生成
---@param index number
---@param data UITabData
function PlotcapMainLinePanel_Background:on_WBP_PlotRecapClassChangeBtnCom_ItemSelected(index, data)
    self:RefreshDropDownMenu(index)
end

function PlotcapMainLinePanel_Background:RefreshDropDownMenu(index)
    if index <= #self._filterData then
        self._curChapter = index
        self:RefreshChapterData(self._filterData[index].key)
        self.KList_NodeCom:Refresh(self:GetItemListData())
        self.WBP_PlotRecapClassChangeTitle_WBP_DIYTextCom:Refresh(self._filterData[index].desc)
    end

    if Game.NewUIManager:IsShow(UIPanelConfig.PlotRecapRightInfo_Panel) then
        Game.NewUIManager:ClosePanel(UIPanelConfig.PlotRecapRightInfo_Panel)
    end 
end

return PlotcapMainLinePanel_Background
