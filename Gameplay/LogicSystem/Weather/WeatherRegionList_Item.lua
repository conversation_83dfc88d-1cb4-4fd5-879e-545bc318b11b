local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class WeatherRegionList_Item : UIListItem
---@field view WeatherRegionList_ItemBlueprint
local WeatherRegionList_Item = DefineClass("WeatherRegionList_Item", UIListItem)
local ESlateVisibility = import("ESlateVisibility")

WeatherRegionList_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function WeatherRegionList_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function WeatherRegionList_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function WeatherRegionList_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function WeatherRegionList_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function WeatherRegionList_Item:InitUIView()
end

---@param data RegionListItemData
function WeatherRegionList_Item:OnRefresh(data)
	if self.index == 1 then
		self.userWidget:SetBgType(false)
	else
		self.userWidget:SetBgType(true)
	end

	if self.index % 2 == 1 then
		self.userWidget:SetCondition(false)
	else
		self.userWidget:SetCondition(true)
	end
	self.userWidget:SetVisibility(ESlateVisibility.Visible)

	if data then
		local regionData = Game.TableData.GetRegionClimateDataRow(data.regionId)
		local climateData = Game.TableData.GetClimateSettingDataRow(data.climateId)
		self.view.Text_Region:SetText((regionData and regionData.RegionName) and regionData.RegionName or Game.TableData.GetClimateConstDataRow("DefaultRegionName").Value)
		self:SetImage(self.view.Img_Climate, climateData.Icon)
		self.view.Text_Temperature:SetText(string.format(Game.TableData.GetClimateConstDataRow("TemperatureString").Value, climateData.Temperature))
	end
end

return WeatherRegionList_Item
