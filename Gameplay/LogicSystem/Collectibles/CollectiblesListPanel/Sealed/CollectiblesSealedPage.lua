local CollectiblesListBasePage = kg_require("Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.CollectiblesListBasePage")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ESlateVisibility = import("ESlateVisibility")
---@class CollectiblesSealedPage : CollectiblesListBasePage
local CollectiblesSealedPage = DefineClass("CollectiblesSealedPage", CollectiblesListBasePage)

function CollectiblesSealedPage:InitUIComponent()
	CollectiblesListBasePage.InitUIComponent(self)
	---@type UIListView
	self.ChildListView = self:CreateComponent(self.view.TileView_Sealed, UIListView)
end

function CollectiblesSealedPage:InitUIEvent()
	CollectiblesListBasePage.InitUIEvent(self)
	self:AddUIEvent(self.view.WBP_CollectiblesInfoPositive.WBP_CollectiblesBtnGo_Item.Btn_Go.OnClicked, "OnJumpToSource")
end

function CollectiblesSealedPage:OnRefreshRightUI()
	local iconPath = Game.UIIconUtils.getIcon(self.curSelectConfig.Icon)
	if iconPath then
		self:SetMaterialTextureParam(self.view.WBP_CollectiblesSealedItemInfo_Item.Img_Icon, iconPath, "MainTex")
	end
	
	if self.lock then
		self:RefreshLockedRightUIComponent()
	else
		self:RefreshUnlockedRightUIComponent()
	end
	
	self.view.ScrollBox:ScrollToStart()
	self.view.ScrollBox:EndInertialScrolling()
	self:PlayAnimation(self.userWidget.Ani_qiehuan_in, nil, self.userWidget)
end

function CollectiblesSealedPage:RefreshUnlockedRightUIComponent()
	self.view.WBP_CollectiblesSealedItemInfo_Item:Event_UI_Empty(false)
	self.view.WBP_CollectiblesInfoPositive:Event_UI_Title(true, false, true, false)
	self.view.WBP_CollectiblesInfonegative:Event_UI_Title(true, false, true, false)
	self.view.WBP_CollectiblesInfoInfo:Event_UI_Title(true, false, true, false)
	
	local sealedNegativeTextStr = self.curSelectConfig.Negative_effect
	if sealedNegativeTextStr then
		self.view.WBP_CollectiblesInfoPositive.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("SealedPositiveTextStr"))
		self.view.WBP_CollectiblesInfoPositive.Text_Info:SetText(self.curSelectConfig.Positive_effect)

		self.view.WBP_CollectiblesInfonegative.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("SealedNegativeTextStr"))
		self.view.WBP_CollectiblesInfonegative.Text_Info:SetText(sealedNegativeTextStr)

		self.view.WBP_CollectiblesInfoInfo:SetVisibility(ESlateVisibility.Visible)
		self.view.WBP_CollectiblesInfoInfo.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("SealedRecordTextStr"))
		self.view.WBP_CollectiblesInfoInfo.Text_Info:SetText(self.curSelectConfig.Introduction_Description)
	else
		self.view.WBP_CollectiblesInfoPositive.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("SealedPositiveTextStr"))
		self.view.WBP_CollectiblesInfoPositive.Text_Info:SetText(self.curSelectConfig.Positive_effect)

		self.view.WBP_CollectiblesInfonegative.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("SealedRecordTextStr"))
		self.view.WBP_CollectiblesInfonegative.Text_Info:SetText(self.curSelectConfig.Introduction_Description)

		self.view.WBP_CollectiblesInfoInfo:SetVisibility(ESlateVisibility.Collapsed)
	end

	local riskData = Game.TableData.GetSealedRiskRow(self.curSelectConfig.Risk)
	self.view.WBP_CollectiblesSealedItemInfo_Item.WBP_CollectiblesTips_Item.Text_Num:SetText(riskData.RiskLevel)
	self.view.WBP_CollectiblesSealedItemInfo_Item.WBP_CollectiblesSealedTips_Item.Text_Info:SetText(riskData.RiskDescription)
	self.view.WBP_CollectiblesSealedItemInfo_Item.WBP_CollectiblesTips_Item:SetStyle(riskData.RiskID - 1)
	self.view.WBP_CollectiblesSealedItemInfo_Item.WBP_CollectiblesSealedTips_Item:SetStyle(riskData.RiskID - 1)
end

function CollectiblesSealedPage:RefreshLockedRightUIComponent()
	-- 图标显示为灰态，隐藏效果文本，显示获取途径，显示前往按钮
	self.view.WBP_CollectiblesSealedItemInfo_Item:Event_UI_Empty(true)
	self.view.WBP_CollectiblesInfoPositive:Event_UI_Title(true, true, false, true)
	self.view.WBP_CollectiblesInfoInfo:Event_UI_Title(true, false, false, false)
	
	self.view.WBP_CollectiblesInfoPositive.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("SealedAcquisitionMethodTextStr"))
	self.view.WBP_CollectiblesInfoPositive.Text_Info:SetText(self.curSelectConfig.JumpName)
	
	self.view.WBP_CollectiblesInfonegative:SetVisibility(ESlateVisibility.Collapsed)

	self.view.WBP_CollectiblesInfoInfo.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("SealedRecordTextStr"))
	self.view.WBP_CollectiblesInfoInfo.Text_Info:SetText(Game.TableData.GetCollectiblesSettingRow("SealedLockedRecordTextStr"))
end

function CollectiblesSealedPage:RefreshTitle()
	local config = self.curSelectConfig
	if config.Level then
		self.ChildTitle:Refresh(config.Name, config.Token, true, config.Level)
	else
		self.ChildTitle:Refresh(config.Name, config.Token, false)
	end
end

function CollectiblesSealedPage:OnRefreshFilter()
	if #self.showDataList <1 then
		return
	end

	local newIndex = 1
	for i, data in pairs(self.showDataList) do
		if self.curSelectData == data then
			newIndex = i
			break
		end
	end

	self.ChildListView:SetSelectedItemByIndex(newIndex, true)
end

function CollectiblesSealedPage:SortShowListData()
	table.sort(self.showDataList, function(leftData, rightData)
		if leftData.rewardType == rightData.rewardType then
			local leftTableData = Game.TableData.GetCollectiblesRow(leftData.id)
			local rightTableData = Game.TableData.GetCollectiblesRow(rightData.id)
			if leftTableData.Order == rightTableData.Order then
				return leftData.id < rightData.id
			end
			return leftTableData.Order < rightTableData.Order
		end
		return leftData.rewardType < rightData.rewardType
	end)
end

function CollectiblesSealedPage:OnJumpToSource()
	Game.UIJumpSystem:JumpToUI(self.curSelectConfig.Source)
end

function CollectiblesSealedPage:OtherFilter(data, filterName)
	local level = Game.TableData.GetCollectiblesRow(data.id).Level
	return level and string.find(tostring(level), string.lower(filterName))
end

return CollectiblesSealedPage
