local ItemRewardNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemRewardNew")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local ESlateVisibility = import("ESlateVisibility")
---@class CollectiblesProp_Item : UIListItem
---@field view Collectible_Sealed_ItemBlueprint
local CollectiblesProp_Item = DefineClass("CollectiblesProp_Item", UIListItem)

CollectiblesProp_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CollectiblesProp_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CollectiblesProp_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CollectiblesProp_Item:InitUIComponent()
    ---@type ItemRewardNew
    self.WBP_ItemRewardCom = self:CreateComponent(self.view.WBP_ItemReward, ItemRewardNew)
end

---UI事件在这里注册，此处为自动生成
function CollectiblesProp_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CollectiblesProp_Item:InitUIView()
end

---面板打开的时候触发
function CollectiblesProp_Item:OnRefresh(data)
	local config = Game.TableData.GetCollectiblesRow(data.id)
	self.view.Text_Name:SetText(config.Name)
	self.view.Text_Info:SetText(config.Describe)
	self.view.Text_Num:SetText(string.format("+%s", config.Token))
	
	if data.rewardType ~= Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH then
		self.view.Image_Icon:SetVisibility(ESlateVisibility.Collapsed)
	else
		local iconPath = Game.CollectiblesSystem:GetCollectiblesIcon(data.id)
		self:SetImage(self.view.Image_Icon, iconPath)
	end

	self.WBP_ItemRewardCom:FillItem(
		config.Item,
		Enum.CommonItemClickType.OverrideTip,
		nil, 0,
		true, nil, nil, false, false
	)

	self.userWidget:Event_UI_Select(self:IsSelected(), data.rewardType == Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH)
	Game.RedPointSystem:RegisterRedPoint(self:GetBelongPanel(), self.userWidget, "CollectiblesListItem", data.id)
end

function CollectiblesProp_Item:UpdateSelectionState(selected)
	self.userWidget:Event_UI_Select(selected, self.data.rewardType == Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH)
end

return CollectiblesProp_Item
