local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local CollectiblesListBasePage = kg_require("Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.CollectiblesListBasePage")
---@class CollectiblesPropPage : CollectiblesListBasePage
local CollectiblesPropPage = DefineClass("CollectiblesPropPage", CollectiblesListBasePage)

--道具（博物之室）
function CollectiblesPropPage:InitUIComponent()
	CollectiblesListBasePage.InitUIComponent(self)
	---@type UIListView
	self.ChildListView = self:CreateComponent(self.view.ListView_Letter, UIListView)
end

function CollectiblesPropPage:OnRefreshRightUI()
	self.view.WBP_Collectible_Item:Event_UI_Empty(self.lock)
	
	--物品描述 + 物品属性
	self.view.WBP_CollectiblesInfo.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("ItemDescTextStr"))
	self.view.WBP_CollectiblesInfo_1.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("ItemBuffTextStr"))

	local itemConfig = Game.TableData.GetItemNewDataRow(self.curSelectConfig.Item)
	local isStartActive = false
	if self.lock then
		local text = Game.TableData.GetCollectiblesSettingRow("CommonLockedTextStr")
		self.view.WBP_CollectiblesInfo.Text_Info:SetText(text)
		self.view.WBP_CollectiblesInfo_1.Text_Info:SetText(text)
	else
		self.view.WBP_CollectiblesInfo.Text_Info:SetText(self.curSelectConfig.Describe)
		local itemDesc = itemConfig and itemConfig.funcRep or ""
		self.view.WBP_CollectiblesInfo_1.Text_Info:SetText(itemDesc)
		isStartActive = true
	end
	-- 设置文本左边的星星显隐
	self.view.WBP_CollectiblesInfo:Event_UI_Title(true, false, isStartActive, false)
	self.view.WBP_CollectiblesInfo_1:Event_UI_Title(true, false, isStartActive, false)

	local iconPath = Game.UIIconUtils.GetIconTexture(itemConfig.icon)
	if iconPath then
		self:SetMaterialTextureParam(self.view.WBP_Collectible_Item.Img_Icon, iconPath, "MainTex")
	end
	self:PlayAnimation(self.userWidget.Ani_qiehuan_in, nil, self.userWidget)
end

return CollectiblesPropPage
