local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class CollectiblesNPCLand_Item : UIListItem
---@field view CollectiblesConstableLand_ItemBlueprint
local CollectiblesNPCLand_Item = DefineClass("CollectiblesNPCLand_Item", UIListItem)

CollectiblesNPCLand_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CollectiblesNPCLand_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CollectiblesNPCLand_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CollectiblesNPCLand_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function CollectiblesNPCLand_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CollectiblesNPCLand_Item:InitUIView()
end

---面板打开的时候触发
function CollectiblesNPCLand_Item:OnRefresh(data)
	self.view.Text_Land:SetText(data)
end

return CollectiblesNPCLand_Item
