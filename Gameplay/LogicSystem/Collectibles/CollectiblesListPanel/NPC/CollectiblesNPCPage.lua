local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local CollectiblesListBasePage = kg_require("Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.CollectiblesListBasePage")
local UICompRenderTarget = kg_require("Framework.KGFramework.KGUI.Component.RT.UICompRenderTarget")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ESlateVisibility = import("ESlateVisibility")
---@class CollectiblesNPCPage : CollectiblesListBasePage
local CollectiblesNPCPage = DefineClass("CollectiblesNPCPage", CollectiblesListBasePage)

function CollectiblesNPCPage:InitUIData()
	CollectiblesListBasePage.InitUIData(self)
	
	---@type UICompRenderTargetParam
	self.RTCompParam = nil
end

--命运之网（NPC模型）
function CollectiblesNPCPage:InitUIComponent()
	CollectiblesListBasePage.InitUIComponent(self)
	---@type UIComDiyTitle
	self.WBP_DIYTextCom = self:CreateComponent(self.view.WBP_DIYText, UIComDiyTitle)
	---@type UIListView
	self.ChildListView = self:CreateComponent(self.view.TileView_Constables, UIListView)
	---@type UIListView
	self.ChildListPosView = self:CreateComponent(self.view.TileView_LandName, UIListView)
	---@type UIListView
	self.ChildListDropView = self:CreateComponent(self.view.TileView_Item, UIListView)

	--设置物品描述style
	self.view.WBP_CollectiblesInfo:Event_UI_Title(true, false, true, false)
	
	---@type UICompRenderTarget
	self.RTComp = nil
end

function CollectiblesNPCPage:InitUIEvent()
	CollectiblesListBasePage.InitUIEvent(self)
	self:AddUIEvent(self.view.WBP_Collectibles_ChangeBtn_Image.Btn_Select.OnClicked, "on_WBP_Collectibles_ChangeBtn_ImageBtn_Select_Clicked")
	self:AddUIEvent(self.view.WBP_Collectibles_ChangeBtn_Story.Btn_Select.OnClicked, "on_WBP_Collectibles_ChangeBtn_StoryBtn_Select_Clicked")
end

---更新两个按钮的文本
function CollectiblesNPCPage:RefreshEdgeUI()
	self.view.WBP_Collectibles_ChangeBtn_Image.Text_Name:SetText(Game.TableData.GetCollectiblesSettingRow("NPCImageTextStr"))
	self.view.WBP_Collectibles_ChangeBtn_Story.Text_Name:SetText(Game.TableData.GetCollectiblesSettingRow("NPCStoryTextStr"))
end

function CollectiblesNPCPage:OnClose()
	if self.RTComp then
		self:RemoveComponent(self.RTComp)
		self.RTComp = nil
	end
	CollectiblesListBasePage.OnClose(self)
end

function CollectiblesNPCPage:OnRefreshRightUI()
	self:RefreshRightData()
	self:RefreshRightUIComponent()
	self:RefreshRT()
end

function CollectiblesNPCPage:RefreshRightData()
	self.posDataList = {}
	for _, posStr in ksbcipairs(self.curSelectConfig.Position) do
		table.insert(self.posDataList, posStr)
	end

	self.rewardDropDataList = {}
	--这配置也太难取了
	for _, itemNo in ksbcipairs(self.curSelectConfig.Item) do
		table.insert(self.rewardDropDataList, {
			id = itemNo,
			clickType = true,
			selected = Enum.EItemSelectType.NotSelected,
		})
	end
end

function CollectiblesNPCPage:RefreshRightUIComponent()
	self.WBP_DIYTextCom:Refresh(self.curSelectConfig.Name)
	self.view.Text_Tip:SetText(self.curSelectConfig.Describe)
	self.view.WBP_CollectiblesInfo.Text_Title:SetText(
		Game.TableData.GetCollectiblesSettingRow("NPGBGTextStrLength")
	)
	self.view.WBP_CollectiblesInfo.Text_Info:SetText(self.curSelectConfig.Describe)

	local iconPath = Game.UIIconUtils.getIcon(self.curSelectConfig.Icon)

	self:SetMaterialTextureParam(
		self.view.Img_Boss_Lock,
		iconPath, "MainTex"
	)

	if self.lock then
		self.view.Img_Boss_Lock:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.Img_Boss_Lock:SetVisibility(ESlateVisibility.Collapsed)
	end

	self.ChildListPosView:Refresh(self.posDataList)
	self.ChildListDropView:Refresh(self.rewardDropDataList)

	self.showStory = nil
	self:OnShowChangeStory(false)
end

function CollectiblesNPCPage:OnShowChangeStory(show)
	if self.showStory == show then
		return
	end

	self.showStory = show
	self.userWidget:Event_UI_State(show)

	self:StopAnimation(self.userWidget.Ani_qiehuan_in, nil, self.userWidget)
	self:StopAnimation(self.userWidget.Ani_qiehuan_out, nil, self.userWidget)
	if show then
		if self.RTComp then
			self.RTComp:EndCapture()
		end
		self:PlayAnimation(self.userWidget.Ani_qiehuan_in, nil, self.userWidget)
	else
		--todo 要等scene加载完成才能调用
		if self.RTComp then
			--self.RTComp:StartCapture()
		end
		self:PlayAnimation(self.userWidget.Ani_qiehuan_out, nil, self.userWidget)
	end
end

---@return UIActorParam[]
function CollectiblesNPCPage:CreateRTActorParams()
	local modelID = self.curSelectConfig.ModelID
	local pos = self.curSelectConfig.ModelPostion
	local roaY = self.curSelectConfig.ModelRotationY
	return {
		{
			Model = modelID,
			ActorType = UICompRenderTarget.ActorType.Monster,
			Pos = {pos[1], pos[2], pos[3]},
			Rotation = {0, roaY, 0},
		},	
	}
end

function CollectiblesNPCPage:RefreshRT()
	self.view.Img_Boss_RT:SetVisibility(ESlateVisibility.Collapsed)
	if self.RTComp then
		self.RTComp:EndCapture()
	end

	if self.lock then
		return
	end
	
	if not self.RTComp then
		self.RTComp = self:CreateComponent(self.userWidget, UICompRenderTarget)
		---@type UICompRenderTargetParam
		self.RTCompParam = {
			SceneName = Enum.ESceneDisplayEnum.UIRTMonster,
			ActorParams = self:CreateRTActorParams(),
			CaptureInterval = 60,
			StartCaptureWaitAllLoad = true,
			OnEntityAllLoaded = function() 
				if not self.isDestroyed then self:OnLoadFinish() end 
			end
		}
	else
		self.RTCompParam.ActorParams = self:CreateRTActorParams()
	end

	self.RTComp:Refresh(self.RTCompParam)
end

function CollectiblesNPCPage:OnLoadFinish()
	self.view.Img_Boss_RT:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

--- 此处为自动生成
function CollectiblesNPCPage:on_WBP_Collectibles_ChangeBtn_ImageBtn_Select_Clicked()
	self:OnShowChangeStory(false)
end

--- 此处为自动生成
function CollectiblesNPCPage:on_WBP_Collectibles_ChangeBtn_StoryBtn_Select_Clicked()
	self:OnShowChangeStory(true)
end

return CollectiblesNPCPage
