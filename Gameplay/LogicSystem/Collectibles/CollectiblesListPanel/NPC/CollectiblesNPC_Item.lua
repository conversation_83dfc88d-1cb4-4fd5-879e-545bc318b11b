local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class CollectiblesNPC_Item : UIListItem
---@field view CollectiblesConstable_ItemBlueprint
local CollectiblesNPC_Item = DefineClass("CollectiblesNPC_Item", UIListItem)

CollectiblesNPC_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CollectiblesNPC_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CollectiblesNPC_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CollectiblesNPC_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function CollectiblesNPC_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CollectiblesNPC_Item:InitUIView()
end

---面板打开的时候触发
function CollectiblesNPC_Item:OnRefresh(data)
	local config = Game.TableData.GetCollectiblesRow(data.id)
	local iconPath = Game.UIIconUtils.getIcon(config.Icon)
	self:SetImage(self.view.Image_Icon, iconPath)

	self.view.Text_Name:SetText(data.name)
	self.view.Text_Num:SetText(string.format("+%s", config.Token))

	self.userWidget:Event_UI_Empty(data.rewardType == Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH, self:IsSelected())
	Game.RedPointSystem:RegisterRedPoint(self:GetBelongPanel(), self.userWidget, "CollectiblesListItem", data.id)
end

function CollectiblesNPC_Item:UpdateSelectionState(selected)
	self.userWidget:Event_UI_Empty(self.data.rewardType == Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH, selected)
end

return CollectiblesNPC_Item
