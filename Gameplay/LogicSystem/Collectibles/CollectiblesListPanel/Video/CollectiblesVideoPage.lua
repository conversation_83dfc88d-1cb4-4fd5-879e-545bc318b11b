local CollectiblesListBasePage = kg_require("Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.CollectiblesListBasePage")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
---@class CollectiblesVideoPage : CollectiblesListBasePage
---@field view CollectiblesVideo_ItemBlueprint
local CollectiblesVideoPage = DefineClass("CollectiblesVideoPage", CollectiblesListBasePage)

--历史孔隙（视频）
function CollectiblesVideoPage:InitUIComponent()
	CollectiblesListBasePage.InitUIComponent(self)
	---@type UIListView
	self.ChildListView = self:CreateComponent(self.view.ListView_Video, UIListView)
end

function CollectiblesVideoPage:InitUIEvent()
	CollectiblesListBasePage.InitUIEvent(self)
    self:AddUIEvent(self.view.WBP_CollectibleVideoBig.Btn_FullWindow.OnClicked, "on_WBP_CollectibleVideoBigBtn_FullWindow_Clicked")
end

function CollectiblesVideoPage:OnRefreshRightUI()
	self.view.WBP_CollectibleVideoBig:Event_UI_Size(self.lock)
	--设置物品描述和发生位置的标题
	self.view.WBP_CollectiblesInfo.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("VideoDescTextStr"))
	self.view.WBP_CollectiblesInfo_1.Text_Title:SetText(Game.TableData.GetCollectiblesSettingRow("VideoDescPosTextStr"))
	
	local hasStart = false	-- 是否有左边的星星
	local describeText = nil
	local sourceText = nil
	if self.lock then
		--设置物品描述和发生位置的内容
		describeText = Game.TableData.GetCollectiblesSettingRow("CommonLockedTextStr")
		sourceText = Game.TableData.GetCollectiblesSettingRow("CommonLockedTextStr")
	else
		hasStart = true
		describeText = self.curSelectConfig.Describe
		sourceText = self.curSelectConfig.Source
	end
	--设置物品描述和发生位置的style和内容
	self.view.WBP_CollectiblesInfo:Event_UI_Title(true, false, hasStart, false)
	self.view.WBP_CollectiblesInfo_1:Event_UI_Title(true, false, hasStart, false)
	self.view.WBP_CollectiblesInfo.Text_Info:SetText(describeText)
	self.view.WBP_CollectiblesInfo_1.Text_Info:SetText(sourceText)
	
	local iconPath = Game.UIIconUtils.getIcon(self.curSelectConfig.Icon)
	if iconPath then
		self:SetImage(self.view.WBP_CollectibleVideoBig.Image_Icon, iconPath)
	end
	self:PlayAnimation(self.userWidget.Ani_qiehuan_in, nil, self.userWidget)
end

---@private
---用于设置提示框的确定事件
function CollectiblesVideoPage:setMsgBoxEvent()
	local params = {
		CinematicType = Enum.CinematicType.Cutscene,
		AssetID = self.curSelectConfig.CutSceneID,
	}
	Game.CinematicManager:StartPlayCinematic(params)
end


--- 此处为自动生成
function CollectiblesVideoPage:on_WBP_CollectibleVideoBigBtn_FullWindow_Clicked()
	local videoPath = nil
	--视频传过去
	if self.curSelectConfig.Video and self.curSelectConfig.CutSceneID then
		Log.ErrorFormat("video:%s have both video and cutscene config",self.curSelectConfig.Name)
		return
	elseif self.curSelectConfig.CutSceneID then
		local cutSceneData = Game.TableData.GetCutsceneDataRow(self.curSelectConfig.CutSceneID)
		if cutSceneData.ConditionType == 1 then
			videoPath = cutSceneData.ConditionAssetPath
		else
			videoPath = cutSceneData.AssetPath
		end
	elseif self.curSelectConfig.Video then
		videoPath = self.curSelectConfig.Video
	else
		Log.ErrorFormat("video:%s have no video config",self.curSelectConfig.Name)
		return
	end

	--判断videoPath是否是以 .bk2 结尾
	if string.match(videoPath, "%.bk2$") then
		--如果是视频，打开视频播放面板
		Game.NewUIManager:OpenPanel(UIPanelConfig.CollectiblesVideo_Panel, videoPath)
	else
		--如果是 Cutscene，弹出提示
		local data =  Game.TableData.GetDialogPopUpDataRow(Enum.EDialogPopUpData.COLLECTIBLES_JUMP_TO_CUTSCENE)
		local messageInfo = Game.MessageBoxSystem:CreateMessageInfo(data.ID, data.Title, data.Content,data.RefuseBtn, data.AcceptBtn)
		messageInfo.rightBtnEvent = function ()
			self:setMsgBoxEvent()
		end

		Game.MessageBoxSystem:ShowMessageBox(messageInfo)
	end
end

return CollectiblesVideoPage
