local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local ESlateVisibility = import("ESlateVisibility")
---@class CollectiblesVideo_Item : UIListItem
---@field view CollectibleVideo_ItemBlueprint
local CollectiblesVideo_Item = DefineClass("CollectiblesVideo_Item", UIListItem)

CollectiblesVideo_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CollectiblesVideo_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CollectiblesVideo_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CollectiblesVideo_Item:InitUIComponent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CollectiblesVideo_Item:InitUIView()
end

---面板打开的时候触发
function CollectiblesVideo_Item:OnRefresh(data)
	local config = Game.TableData.GetCollectiblesRow(data.id)
	local iconPath = Game.UIIconUtils.getIcon(config.Icon)
	self:SetImage(self.view.Image_Icon, iconPath)

	self.view.Text_Name:SetText(config.Name)
	self.view.Text_Num:SetText(string.format("+%s", config.Token))

	self.view.WBP_CollectiblesMedium_Btn:SetVisibility(ESlateVisibility.Collapsed)
	self.userWidget:Event_UI_Size(data.rewardType == Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH, self:IsSelected())
	Game.RedPointSystem:RegisterRedPoint(self:GetBelongPanel(), self.userWidget, "CollectiblesListItem", data.id)
end

function CollectiblesVideo_Item:UpdateSelectionState(selected)
	self.userWidget:Event_UI_Size(self.data.rewardType == Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH, selected)
end

return CollectiblesVideo_Item
