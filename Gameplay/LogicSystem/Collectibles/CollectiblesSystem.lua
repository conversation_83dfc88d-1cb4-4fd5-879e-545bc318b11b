local AchieveTab_Panel = kg_require("Gameplay.LogicSystem.Achieve.AchieveTab_Panel")
---@class CollectiblesSystem:SystemBase
local CollectiblesSystem = DefineClass("CollectiblesSystem",SystemBase)

---@private
function CollectiblesSystem:onCtor()
    self.model = nil
    self.sender = nil
end

---@private
function CollectiblesSystem:onInit()
    ---@type CustomRoleModel
    self.model = kg_require("Gameplay.LogicSystem.Collectibles.CollectiblesModel").new(false, true)
    ---@type CustomRoleSender
    self.sender = kg_require("Gameplay.LogicSystem.Collectibles.CollectiblesSender").new()
end

---@private
function CollectiblesSystem:onUnInit()
end

---@public
---获取当前类型收藏品的分数，等级
function CollectiblesSystem:GetCollectiblesCurScoreAndLevel(type)
    return self.model:GetCollectiblesCurScoreAndLevel(type)
end

---@public
---获取当前类型收藏品的分数，总分数
function CollectiblesSystem:GetCollectiblesCurMaxScore(type)
    local typeConfig = Game.TableData.GetCollectiblesTypeRow(type)
    if not typeConfig then
        return 0, 0
    end
	
    local curScore, curLevel = self:GetCollectiblesCurScoreAndLevel(type)
	local maxScore = typeConfig.TokenList[curLevel]
	if curLevel >= typeConfig.Level then
		maxScore = typeConfig.TokenList[curLevel - 1]
	end
    
    return curScore, maxScore
end

---@public
---获取当前类型下，一个已有的收藏品
function CollectiblesSystem:GetOneHasCollectiblesByType(type)
	return self.model:GetOneHasCollectiblesByType(type)
end

---@public
---获取某个大类已获得收藏品数据，总的收藏品数量（减未获得的隐藏）
function CollectiblesSystem:GetCollectiblesCurMaxNum(type)
	return self.model:GetCollectiblesCurMaxNum(type)
end

---@public
function CollectiblesSystem:GetHasCollectedDataDict()
	return self.model.collectedDataDict
end

---@public
function CollectiblesSystem:RefreshCollectiblesCurMaxNum()
	self.model:RefreshCollectiblesCurMaxNum()
end

---@public
---获取某个大类等级阶段奖励状态
function CollectiblesSystem:GetCollectiblesLevelState(type, level)
    local typeInfo = Game.TableData.GetCollectiblesTypeRow(type)
    local score = self:GetCollectiblesCurScoreAndLevel(type)
    
    if score < typeInfo.TokenList[level-1] then
        return Enum.ACHIEVEMENT_LEVEL_STATE.NOT_REACH
    elseif self.model:HasRewardLevel(type, level) then
        return Enum.ACHIEVEMENT_LEVEL_STATE.GOT_REWARD
    else
        return Enum.ACHIEVEMENT_LEVEL_STATE.CAN_GET_REWARD
    end
end

---@public
---获取某个收藏品是否已获得
function CollectiblesSystem:IsHasCollectibles(id)
    return self.model:IsHasCollectibles(id)
end

---@public
---获取某个main类型是否有可领取的等级奖励
function CollectiblesSystem:IsMainTypeHasLevelReward(mainType)
    return self.model:IsMainTypeHasLevelReward(mainType)
end

---@public
---获取某个main类型是否有可领取的收藏品奖励
function CollectiblesSystem:IsMainTypeHasCollectiblesReward(mainType)
	return self.model:IsMainTypeHasCollectiblesReward(mainType)
end

---@public
---获取某个类型收藏品list
function CollectiblesSystem:GetCollectiblesListData(mainType, subType)
    return self.model:GetCollectiblesListData(mainType, subType)
end

---@public
---获取单个收藏品数据
function CollectiblesSystem:GetCollectiblesData(id)
    return self.model:GetCollectiblesData(id)
end

---@public
---获取收藏品描述信息
function CollectiblesSystem:GetCollectiblesDesc(id)
    return self.model:GetCollectiblesDesc(id)
end

---@public
---获取收藏品图标
function CollectiblesSystem:GetCollectiblesIcon(id)
	local config = Game.TableData.GetCollectiblesRow(id)
	if not config then
		return nil
	end

	if not string.isEmpty(config.Icon) then
		return Game.UIIconUtils.GetIconTexture(config.Icon)
	else
		if config.Item then
			local itemData = Game.TableData.GetItemNewDataRow(config.Item)
			if itemData and itemData.icon then
				return Game.UIIconUtils.GetIconTexture(itemData.icon)
			end
		end
	end
	
	return nil
end

--region 上行协议
---获取收藏品全量信息请求
function CollectiblesSystem:ReqGetCollectiblesInfo()
    self.sender:ReqGetCollectiblesInfo()
end

---领取收藏品奖励请求
function CollectiblesSystem:ReqGetCollectiblesReward(id)
    self.sender:ReqGetCollectiblesReward(id)
end

---一键领取收藏品奖励请求
function CollectiblesSystem:ReqGetAllCollectiblesReward(mainType)
	local hasReward = self:IsMainTypeHasCollectiblesReward(mainType)
	if not hasReward then
		return
	end
	
	self.sender:ReqGetAllCollectiblesReward(mainType)
end

---修改照片收藏品的文本描述
function CollectiblesSystem:ReqModifyCollectiblesDesc(id, desc)
    self.sender:ReqModifyCollectiblesDesc(id, desc)
end

--领取等级奖励
function CollectiblesSystem:ReqGetCollectiblesLevelReward(collectiblesType, isGetAll, level)
    self.sender:ReqGetCollectiblesLevelReward(collectiblesType, isGetAll, level)
end

--endregion 上行协议

--region 下行协议

-- 玩家获得收藏品通知
function CollectiblesSystem:OnMsgGetCollectiblesNotify(info, scoreList)
    self.model:OnMsgGetCollectiblesNotify(info, scoreList)
    for id, _ in pairs(info) do
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ACHIEVEMENT_COMMON,
            {collectiblesId = id}
        )
        self:RefreshRedPointInfo(id)
    end

    Game.GlobalEventSystem:Publish(EEventTypesV2.COLLECTIBLES_GET)
end

-- 获取收藏品全量信息（登录下发）
function CollectiblesSystem:RetGetCollectiblesInfo(info, externinfo, reward)
    self.model:RetGetCollectiblesInfo(info, externinfo, reward)
    self:RegisterRedPointInfo()
end

---一键领取收藏品奖励回复
function CollectiblesSystem:RetGetAllCollectiblesReward(mainType, result, collectiblesInfoList)
	if result ~= Game.ErrorCodeConst.NO_ERR then
		Log.WarningFormat("收藏品：GetAllCollectiblesReward failed type = %s, fail = %s", mainType, Game.NetworkManager.GetErrCodeDesc(result))
		return
	end
	
	for id, time in pairs(collectiblesInfoList) do
		self.model:RetGetCollectiblesReward(id)
		self:RefreshRedPointInfo(id)
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.COLLECTIBLES_REWARD_GET_UPDATE)
	Game.RedPointSystem:ClearNode("AchieveBottomItem", AchieveTab_Panel.TabEnum.Collectibles)
end

-- 领取等级奖励回复
function CollectiblesSystem:RetGetCollectiblesLevelReward(type, isGetAll, level, result)
    if result ~= Game.ErrorCodeConst.NO_ERR then
        Log.WarningFormat("收藏品：GetCollectiblesLevelReward failed type = %s, isGetAll = %s, level = %s, fail = %s", type, isGetAll, level, Game.NetworkManager.GetErrCodeDesc(result))
        return
    end
	
    self.model:RetGetCollectiblesLevelReward(type, isGetAll, level)
	Game.RedPointSystem:ClearNode("CollectiblesMainTab", type)
	Game.RedPointSystem:ClearNode("CollectiblesMainTypeEntrance", type)
	Game.RedPointSystem:ClearNode("CollectiblesLevelBar", type)
	Game.RedPointSystem:ClearNode("Achievement")
	Game.RedPointSystem:ClearNode("AchieveBottomItem", AchieveTab_Panel.TabEnum.Collectibles)
	
    Game.GlobalEventSystem:Publish(EEventTypesV2.COLLECTIBLES_REWARDLEVEL_GET_UPDATE)
end

-- 修改收藏品描述回复
function CollectiblesSystem:RetModifyCollectiblesDesc(id, desc, result)
    if result ~= Game.ErrorCodeConst.NO_ERR then
        Log.WarningFormat("收藏品：ModifyCollectiblesDesc failed id = %s, fail = %s", id, Game.NetworkManager.GetErrCodeDesc(result))
        return
    end
    
    self.model:RetModifyCollectiblesDesc(id, desc)
    Game.GlobalEventSystem:Publish(EEventTypesV2.COLLECTIBLES_LIST_PHOTO_DESC_UPDATE, id, desc)
end

-- 领取收藏品奖励回复
function CollectiblesSystem:RetGetCollectiblesReward(id, result)
    if result ~= Game.ErrorCodeConst.NO_ERR then
        Log.WarningFormat("收藏品：GetCollectiblesReward failed id = %s, fail = %s", id, Game.NetworkManager.GetErrCodeDesc(result))
        return
    end
    
    self.model:RetGetCollectiblesReward(id)
    self:RefreshRedPointInfo(id)
    Game.GlobalEventSystem:Publish(EEventTypesV2.COLLECTIBLES_REWARD_GET_UPDATE)
end
--endregion 下行协议


-- 注册大类入口红点
function CollectiblesSystem:RegisterRedPointInfo()
    Game.RedPointSystem:AddListener("CollectiblesMainTypeEntrance", self, self.CheckCollectiblesMainTypeRedpoint)
	Game.RedPointSystem:AddListener("CollectiblesLevelBar", self, self.IsMainTypeHasLevelReward)
	
    Game.RedPointSystem:AddListener("CollectiblesMainTab", self, self.CheckCollectiblesMainTypeRedpoint)
    Game.RedPointSystem:AddListener("CollectiblesSubTab", self, self.CheckCollectiblesSubTypeRedpoint)
    Game.RedPointSystem:AddListener("CollectiblesListItem", self, self.CheckCollectiblesItemRedpoint)
    --书信
    Game.RedPointSystem:AddListener("CollectiblesTreeParentItem", self, self.CheckCollectiblesLetterCategoryRedpoint)
    Game.RedPointSystem:AddListener("CollectiblesTreeChildItem", self, self.CheckCollectiblesItemRedpoint)
end

-- 检查菜单入口红点
function CollectiblesSystem:CheckCollectiblesMenuRedpoint()
    return self.model:CheckMenuRedpoint()
end

---检查整个收藏品红点状态
function CollectiblesSystem:CheckBottomTabRedpoint()
	return self.model:CheckBottomTabRedpoint()
end

---检查大类红点状态
function CollectiblesSystem:CheckCollectiblesMainTypeRedpoint(mainType)
	Log.DebugFormat("红点:%d, %s", mainType, self.model:IsMainTypeHasCollectiblesReward(mainType, nil, true))
    return self.model:IsMainTypeHasCollectiblesReward(mainType, nil, true)
end

---检查子类红点状态
function CollectiblesSystem:CheckCollectiblesSubTypeRedpoint(subType)
    return self.model:IsSubTypeHasCollectiblesReward(subType)
end

---检查item红点状态
function CollectiblesSystem:CheckCollectiblesItemRedpoint(id)
    return self.model:IsItemHasCollectiblesReward(id)
end

---检查书信类别
function CollectiblesSystem:CheckCollectiblesLetterCategoryRedpoint(letterCategory)
    return self.model:IsLetterCategoryHasCollectiblesReward(letterCategory)
end

function CollectiblesSystem:RefreshRedPointInfo(id)
	local config = Game.TableData.GetCollectiblesRow(id)
	
    Game.RedPointSystem:ClearNode("CollectiblesSubTab", config.Subclass_ID)
    Game.RedPointSystem:ClearNode("CollectiblesMainTab", config.Type)
    Game.RedPointSystem:ClearNode("CollectiblesMainTypeEntrance", config.Type)
    Game.RedPointSystem:ClearNode("CollectiblesListItem", id)
    Game.RedPointSystem:ClearNode("CollectiblesTreeParentItem", config.Category_Number)
    Game.RedPointSystem:ClearNode("CollectiblesTreeChildItem", id)
    Game.RedPointSystem:ClearNode("Achievement")
	Game.RedPointSystem:ClearNode("CollectiblesLevelBar", config.Type)
	Game.RedPointSystem:ClearNode("AchieveBottomItem", AchieveTab_Panel.TabEnum.Collectibles)
end

return CollectiblesSystem