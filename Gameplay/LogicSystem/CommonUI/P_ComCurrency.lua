---@class P_ComCurrency
local P_ComCurrency = DefineClass("P_ComCurrency", UIComponent)

P_ComCurrency.eventBindMap = {
    -- [_G.EEventTypes.RECEIVE_MONEY_CHANGE] = "RefreshUI",
}
function P_ComCurrency:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button, self.OnMatClick)
end

---初始化数据
---@param itemId int 物品ID
---@param needNum int 所需数量
function P_ComCurrency:SetData(itemId, needNum, isItem, IsPriceOff, OriginalPrice, bLightBg)
    self.ItemId = itemId
    self.NeedNum = needNum

    local IconPath = Game.UIIconUtils.GetIconByItemId(itemId)
    if IconPath then
        self:SetImage(self.View.Icon, IconPath)
    end

    if needNum then
        local hasNum = Game.BagSystem:GetItemCount(itemId) or 0
        -- local tColor = self.View.ColorTip
        -- if hasNum < needNum then
        --     tColor = self.View.ColorLack
        -- end
        if bLightBg then
            self.userWidget:SetIsLack(hasNum < needNum and 2 or 0)
        else
            self.userWidget:SetIsLack(hasNum < needNum and 1 or 0)
        end
        self.View:SetType(self.View.Type)
        -- self.View.Text_Count:SetColorAndOpacity(tColor)
        if isItem then
            self.View.Text_Count:SetText(hasNum)
            self.View.CostNum:SetText(needNum)
        else
            self.View.Text_Count:SetText(needNum)
        end
        if IsPriceOff then
            self.View.WidgetRoot:SetPriceOff(true)
            self.View.Text_FullPrice:SetText(OriginalPrice)
        else
            self.View.WidgetRoot:SetPriceOff(false)
        end
    end
end

function P_ComCurrency:RefreshUI()
    -- local hasMoneyNum = Game.BagSystem:GetItemCount(moneyId)
    -- local IconID = Game.TableData.GetItemNewDataRow(moneyId).icon
    -- local IconPath = Game.UIIconUtils.GetIconByItemId(moneyId)
    --
    --
    --     if IconPath then
    --         self:SetImage(widget.WBP_ComCurrency.Icon, IconPath)
    --     end
    --
    -- widget.WBP_ComCurrency.CostNum:SetText(lvData.LvUpMoney)
    -- widget.WBP_ComCurrency.Text_Count:SetText(lvData.LvUpMoney)
    -- local tColor
    -- if lvData.LvUpMoney > hasMoneyNum then
    --     tColor = widget.WBP_ComCurrency.ColorLack
    --     self.IsEnough = false
    --     self.isMoneyNotEnough = true
    --     self.moneyExchangeId = moneyId
    --     self.moneyExchangeNum = lvData.LvUpMoney - hasMoneyNum
    -- else
    --     tColor = widget.WBP_ComCurrency.ColorCost
    -- end
    -- widget.WBP_ComCurrency.Text_Count:SetColorAndOpacity(tColor)

    -- self.View.Text_Count:SetText()
    -- self.View.CostNum:SetText(self.NeedNum)
end

---出tips
---@param index number
function P_ComCurrency:OnMatClick()
    if self.ItemId then
        UI.ShowUI('BagItemTips_Panel', self.ItemId)
    end
end

return P_ComCurrency
