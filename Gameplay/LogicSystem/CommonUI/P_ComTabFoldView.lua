---@class WBP_ComTabFoldView : WBP_ComTabFold_C
---@field public WidgetRoot WBP_ComTabFold_C
---@field public Text_Name C7TextBlock
---@field public Btn_ClickArea C7Button
---@field public Ani_On WidgetAnimation
---@field public Ani_Off WidgetAnimation
---@field public Ani_Hover WidgetAnimation
---@field public TabText string
---@field public Selected boolean
---@field public DecBrush SlateBrush
---@field public Intree boolean
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetSelected fun(self:self,Selected:boolean,Intree:boolean):void
---@field public SetTreeSlot fun(self:self,Intree:boolean):void


---@class P_ComTabFoldView : WBP_ComTabFoldView
---@field public controller P_ComTabFold
local P_ComTabFoldView = DefineClass("P_ComTabFoldView", UIView)

function P_ComTabFoldView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_ClickArea, "OnClick_Btn_ClickArea")

end

return P_ComTabFoldView
