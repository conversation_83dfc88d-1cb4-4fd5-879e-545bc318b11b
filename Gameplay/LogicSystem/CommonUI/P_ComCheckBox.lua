local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local P_ComCheckBox = DefineClass("P_ComCheckBox", UIComponent)

function P_ComCheckBox:OnCreate()
    self.OnCheckStateChangeCallBack = nil
    self.state = self.View.CheckBox:isChecked()
    self.idx = 0
    self:AddUIListener(EUIEventTypes.CheckStateChanged, self.View.CheckBox, self.OnCheckStateChanged)
end

function P_ComCheckBox:SetCheckBoxText(text)
    self.View.TB_Name:SetText(text)
end

function P_ComCheckBox:OnCheckStateChanged(state)
    self.state = self.View.CheckBox:isChecked()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Press, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if self.OnCheckStateChangeCallBack then
        xpcall(self.OnCheckStateChangeCallBack, _G.CallBackError, self, self.state, self.idx)
    end
end

function P_ComCheckBox:SetCheckStateChangeCallBack(func)
    self.OnCheckStateChangeCallBack = func
end

function P_ComCheckBox:SetState(state)
    self.View.CheckBox:SetCheckedState(state)
end

function P_ComCheckBox:SetChecked(isChecked)
    self.View.CheckBox:SetIsChecked(isChecked)
end

function P_ComCheckBox:IsChecked()
    return self.View.CheckBox:isChecked()
end

function P_ComCheckBox:SetIdx(idx)
    self.idx = idx
end

return P_ComCheckBox