---@class P_BasicInput:UIComponent
local P_BasicInput = DefineClass("P_BasicInput", UIComponent)
local EMouseCursor = import("EMouseCursor")
local ESlateVisibility = import("ESlateVisibility")

function P_BasicInput:OnCreate()
    self.oldText = nil
    self.NumberOnly = nil
    --[[
    Owner 所在Owner
    InputLimit 限制输入的文字数量
    HintText   输入框的默认提示文本
    DefaultInputText       输入框的默认文本
    OnValueChangedCallback 输入文字更改的回调函数
    OnClickSearch   点击搜索按钮的回调函数
    CnCharCount     一个汉字占据几个字符位，不传默认为1
    --]]
    self.Params = nil
    if PlatformUtil.IsMobilePlatform() then
        self:AddUIListener(EUIEventTypes.TextChanged, self.View.EditText, self.OnTextChanged)
    else
        self:AddUIListener(EUIEventTypes.C7TextChanged, self.View.EditText, self.OnTextChanged)
    end
    self:AddUIListener(EUIEventTypes.TextCommitted, self.View.EditText, self.OnTextCommitted)

    --是否是数字输入框，默认可以在UMG中进行静态配置
    self:SetIsNumberOnly(self.View.IsNumberOnly)

    --输入时是否显示剩余字数
    self:SetShowLeftCharCountWhenInput(self.View.ShowLeftCharCountWhenInput)
    -- self.View.Text_LimitShow:SetVisibility(ESlateVisibility.Collapsed)
end

function P_BasicInput:OnRefresh()
    self.Params = {}
end

function P_BasicInput:SetIsNumberOnly(numberOnly)
    self.NumberOnly = numberOnly
end

function P_BasicInput:SetShowLeftCharCountWhenInput(ShowLeftCharCountWhenInput)
    self.ShowLeftCharCountWhenInput = ShowLeftCharCountWhenInput
end

function P_BasicInput:filterNumberString(inText)
    local ret = inText
    if self.NumberOnly then
        ret = ""
        local strLength = utf8.len(inText)
        local isNumberValid = true
        for i = 1, strLength do
            local str = utf8.sub(inText, i, i + 1)
            local num = tonumber(str)
            if num ~= nil then
                --是数字，保留
                Log.DebugFormat("current number is %d", num)
                ret = ret .. str
            end
        end
        --[[
        --如果全部为数字，则检测首数字是否为0
        if isNumberValid and strLength > 0 then
            local firstChar = utf8.sub(inText, 1, 2)
            isNumberValid = firstChar ~= "0"
        end
        ]]
    end
    return ret
end

function P_BasicInput:SetData(params)
    self.Params = params
    self.Params.CnCharCount = params.CnCharCount == 2 and 2 or 1

    if self.Params ~= nil and self.Params.HintText ~= nil then
        self.View.EditText:SetHintText(self.Params.HintText)
    end

    if self.Params ~= nil and self.Params.DefaultInputText ~= nil then
        self:SetInputText(self.Params.DefaultInputText)
    end
    self:OnTextChanged()
end

function P_BasicInput:OnTextCommitted()
    if self.Params and self.Params.OnTextCommitted then
        self.Params.OnTextCommitted(self.Params.Owner, newText)
    end
    if self.ShowLeftCharCountWhenInput then
        self.View.Text_LimitShow:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_BasicInput:FixCursorStyle()
    -- if PlatformUtil.IsWindows() and not import("C7FunctionLibrary").IsC7Editor() then
    --     for i= 0,EMouseCursor.EMouseCursor_MAX,1 do
    --         import("WidgetBlueprintLibrary").SetHardwareCursor(_G.GetContextObject(), i, "Cursor/invisible", FVector2D())
    --     end
    -- end
end

function P_BasicInput:OnTextChanged()
    -- self:FixCursorStyle()
    local value = self.View.EditText:GetText()
    local filterNumberText = self:filterNumberString(value)
    if filterNumberText ~= value then
        --数字检测失败，更新文本
        self.View.EditText:SetText(filterNumberText)
        value = self.View.EditText:GetText()
    end

    local newText = self:LimitStrByConfig(value)
    if newText ~= value then
        self.View.EditText:SetText(newText)
        if self.Params and self.Params.OnValueLimitOverCallback ~= nil then
            self.Params.OnValueLimitOverCallback(self.Params.Owner)
        end
    end
    self.oldText = newText
    if self.Params and self.Params.OnValueChangedCallback then
        self.Params.OnValueChangedCallback(self.Params.Owner, newText)
    end
    self:UpdateCharLimitShow(newText)
end

function P_BasicInput:UpdateCharLimitShow(newText)
    if self.ShowLeftCharCountWhenInput then
        self.View.Text_LimitShow:SetText(string.format("%d/%d", self:CountChars(newText), self.Params.InputLimit))
        self.View.Text_LimitShow:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

function P_BasicInput:SetEnable(bEnable)
    self.View.WidgetRoot:SetIsEnabled(bEnable)
end

function P_BasicInput:GetInputText()
    return self.View.EditText:GetText()
end

function P_BasicInput:SetInputText(text)
    self.View.EditText:SetText(text)
    self.oldText = text
end

function P_BasicInput:ClearInputText()
    self.View.EditText:SetText("")
    self.oldText = ""
    if self.Params and self.Params.OnValueChangedCallback then
        self.Params.OnValueChangedCallback(self.Params.Owner, "")
    end
end

function P_BasicInput:SetHintText(text)
    return self.View.EditText:SetHintText(text)
end

function P_BasicInput:LimitStrByConfig(Str)
    if self.Params.InputLimit then
        local textLength = self:CountChars(Str)
        if textLength > self.Params.InputLimit then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_INPUT_MAX)
            return self.oldText or ""
        end
    end
    do return Str end
    --
    local Count = 0
    local Result = ""

    local LegalTextTable = Game.TableData.GetLegalTextDataTable()
    for P, C in utf8.codes(Str) do
        local bFound = false
        for _, Config in pairs(LegalTextTable) do
            local StartPosition = tonumber(Config.StartPosition, 16)
            local EndPosition = tonumber(Config.EndPosition, 16)
            if C >= StartPosition and C <= EndPosition then
                Count = Count + Config.CharCount
                bFound = true
                break
            end
        end
        if not bFound then
            Count = Count + 1
        end
        if self.Params.InputLimit and Count > self.Params.InputLimit then
            self:SetInputText(Result)
            break
        end
        Result = Result .. utf8.char(C)
    end
    return Result
end

-- function P_BasicInput:CountChars(str)
--     local len = 0
--     if str then
--         for _, c in utf8.codes(str) do
--             if c > 127 then
--                 len = len + self.Params.CnCharCount
--             else
--                 len = len + 1
--             end
--         end
--     end
--     return len
-- end

function P_BasicInput:CountChars(str)
    --汉字、数字、字符都按照一个计数
    local len = 0
    if str then
        len = utf8.len(str)
    end
    return len
end

function P_BasicInput:GetLength()
    return self:CountChars(self.oldText)
end

return P_BasicInput
