local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local P_ComIndicator = DefineClass("P_ComIndicator", UIComponent)

function P_ComIndicator:OnCreate()
    self.MaxPage = 0;
    self.MinPage = 1;
    self.CurPage = 0;
    self.OnClickAddCallBack = nil
    self.OnClickDeleteCallBack = nil
end

function P_ComIndicator:OnRefresh(min, max)
    self.MinPage = min and min or 1;
    self.MaxPage = max and max or 1;
    self:SetCurPage(self.MinPage)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button_Add, self.OnClickAdd)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button_Delete, self.OnClickDelete)
end

function P_ComIndicator:SetMaxPage(page)
    self.MaxPage = page
    self.View.text_page:SetText(string.format("%s/%s",self.CurPage,self.MaxPage))    
    self:UpdateStyle()
end

function P_ComIndicator:SetMinPage(page)
    self.MinPage = page
end

function P_ComIndicator:SetCurPage(page)
    if page > self.MaxPage then
        page = self.MaxPage
    end
    self.CurPage = page
    self.View.text_page:SetText(string.format("%s/%s",self.CurPage,self.MaxPage))    
    self:UpdateStyle()
end

function P_ComIndicator:UpdateStyle()
    if self.CurPage <= self.MinPage then
        self.View.WidgetSwitcher_Delete:SetActiveWidgetIndex(1)
    elseif self.CurPage >= self.MaxPage then
        self.View.WidgetSwitcher_Add:SetActiveWidgetIndex(1)
    else
        self.View.WidgetSwitcher_Delete:SetActiveWidgetIndex(0)
        self.View.WidgetSwitcher_Add:SetActiveWidgetIndex(0)
    end
    
end

function P_ComIndicator:OnClickAdd()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_ArrowClickAdd, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self.CurPage = self.CurPage + 1
    if self.CurPage > self.MaxPage then
        self.CurPage = self.MaxPage
    end
    self:UpdateStyle()
    if self.OnClickAddCallBack then
        xpcall(self.OnClickAddCallBack, _G.CallBackError, self, self.CurPage)
    end
    self.View.text_page:SetText(string.format("%s/%s",self.CurPage,self.MaxPage))    
end

function P_ComIndicator:OnClickDelete()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_ArrowClickDelete, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self.CurPage = self.CurPage - 1
    if self.CurPage < self.MinPage then
        self.CurPage = self.MinPage
    end
    self:UpdateStyle()
    if self.OnClickDeleteCallBack then
        xpcall(self.OnClickDeleteCallBack, _G.CallBackError, self, self.CurPage)
    end
    self.View.text_page:SetText(string.format("%s/%s",self.CurPage,self.MaxPage))    
end

function P_ComIndicator:GetCurPage()
    return self.CurPage
end

function P_ComIndicator:GetMaxPage()
    return self.MaxPage
end

function P_ComIndicator:SetAddCallBack(func)
    self.OnClickAddCallBack = func
end

function P_ComIndicator:SetDeleteCallBack(func)
    self.OnClickDeleteCallBack = func
end

return P_ComIndicator