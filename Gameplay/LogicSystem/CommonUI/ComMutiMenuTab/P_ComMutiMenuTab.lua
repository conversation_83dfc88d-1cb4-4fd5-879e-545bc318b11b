
local P_ComMutiMenuTab = DefineClass("P_ComMutiMenuTab", UIComponent)
local MutiMenuFirstTabItem = kg_require("Gameplay.LogicSystem.CommonUI.ComMutiMenuTab.MutiMenuFirstTabItem")
local MutiMenuSecondTabItem = kg_require("Gameplay.LogicSystem.CommonUI.ComMutiMenuTab.MutiMenuSecondTabItem")

function P_ComMutiMenuTab:OnCreate()
	self.tabTreeList = BaseList.CreateList(self, BaseList.Kind.TreeList, self.View.TabList, {{MutiMenuFirstTabItem},{MutiMenuSecondTabItem}})
end

function P_ComMutiMenuTab:OnRefresh()
end

---@param data table treelist页签的构造数据 
---@param callback func 点击页签的回调函数
function P_ComMutiMenuTab:Refresh(data, callback)
	self.params = data
	self.callback = callback
	self.tabTreeList:SetData(data)
	self:SelTab(1)
end

--- 设置选中态
---@param index1 int 一级页签index
---@param index2 int 二级页签index
function P_ComMutiMenuTab:SelTab(index1, index2)
	if not index1 and not index2 then return end
	if self.params[index1].Children and next(self.params[index1].Children) ~= nil then
		self.tabTreeList:Fold(false, index1)
		if index2 then
			self.tabTreeList:Sel(index1, index2)
		else
			self.tabTreeList:Sel(index1, 1)
		end
	else
		self.tabTreeList:Sel(index1)
	end
	self:OnClick(index1, index2)
end

function P_ComMutiMenuTab:OnClick(index1, index2)
	if self.callback then
		self.callback(index1, index2)
	end
end

function P_ComMutiMenuTab:OnClose()
	
end

return P_ComMutiMenuTab