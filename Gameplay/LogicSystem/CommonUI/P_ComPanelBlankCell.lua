---@class P_ComPanelBlankCell:Object
local P_ComPanelBlankCell= DefineClass("P_ComPanelBlankCell",UIComponent)

local ESlateVisibility = import("ESlateVisibility")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

-- local P_CommonCurrencyItem = kg_require "Gameplay.LogicSystem.CommonUI.P_CommonCurrencyItem"

function P_ComPanelBlankCell:OnCreate()
end
---Params字段：
-- TitleName, 左上角标题名
-- MoneyList, 右上角需显示的货币ID列表
-- SystemTips, 系统Tips信息,包含TipsID和ToggleInfo字段
-- BackBtnCb, 返回按钮的点击回调
function P_ComPanelBlankCell:SetBg(Params)
    if Params.TitleName then
        self.View.WBP_ComBtnBack.Text_Back:SetText(Params.TitleName)
    end

    if Params.MoneyList then
        -- self.CurrencyViewPanel:Init(
        --     {
        --         ParentPresenter = self,
        --         Root = self.View.HB_TokenBox,
        --         ClassType = P_CommonCurrencyItem,
        --         Resource = "/Game/Arts/UI/Common/Buttons/WBP_ComBtnTokens.WBP_ComBtnTokens",
        --         Capacity = 5
        --     }
        -- )
        -- local NeedDisplay = {}
        -- for _, MoneyID in pairs(Params.MoneyList) do
        --     local ItemInfo = ItemData.data[MoneyID]
        --     table.insert(NeedDisplay, ItemInfo)
        -- end
        -- self.CurrencyViewPanel:SetData(NeedDisplay)
    end

    if Params.SystemTips then
        self.TipsID = Params.SystemTips.TipsID
        self.ToggleInfo = Params.SystemTips.ToggleInfo
        self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtnBack.Btn_Info, self.ShowTips)
        self.View.WBP_ComBtnBack.Btn_Info:SetVisibility(ESlateVisibility.Visible)
    end

    if Params.BackBtnCb then
        self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtnBack.Btn_Back, Params.BackBtnCb)
    end
end

function P_ComPanelBlankCell:ShowTips()
    Game.TipsSystem:ShowTips(self.TipsID, self.View.WBP_ComBtnBack.Icon:GetCachedGeometry())
end


return P_ComPanelBlankCell
