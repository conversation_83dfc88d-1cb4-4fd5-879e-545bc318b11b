local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local P_ComCurrencyListItem = DefineClass("P_ComCurrencyListItem", UIComponent)

function P_ComCurrencyListItem:OnCreate()
    self:AddUIListener(EUIEventTypes.Hovered, self.View.Button, self.OnHovered)
    self:AddUIListener(EUIEventTypes.Unhovered, self.View.Button, self.OnUnHovered)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button, self.OnClick_CurrencyList_Button)
end
function P_ComCurrencyListItem:OnRefresh()
end

function P_ComCurrencyListItem:SetId(id)
    self.id = id
end

function P_ComCurrencyListItem:SetIcon(IconPath)
    self:SetImage(self.View.Icon, IconPath)
end

function P_ComCurrencyListItem:SetCount(num)
    self.View.Text_Count:SetText(num)
end

function P_ComCurrencyListItem:OnHovered()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Hover, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function P_ComCurrencyListItem:OnClick_CurrencyList_Button()
    Log.Debug("[UIControlerEx] OnClick_CurrencyList_Button, currencyId:", self.id)
    Game.CurrencyExchangeSystem:CurrencyItemClickHandler(self.id)
end

function P_ComCurrencyListItem:OnUnHovered()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Hover, 0.0, 1, EUMGSequencePlayMode.Reverse, 1, false)
end

return P_ComCurrencyListItem