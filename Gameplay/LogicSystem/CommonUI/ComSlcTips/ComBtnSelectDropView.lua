---@class WBP_ComBtnSelectDropView : WBP_ComBtnSelectDrop_C
---@field public WidgetRoot WBP_ComBtnSelectDrop_C
---@field public Button C7Button
---@field public Ani_Press WidgetAnimation
---@field public Ani_Cut WidgetAnimation
---@field public BgBrush SlateBrush
---@field public Is Selected boolean
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetSelected fun(self:self,IsSelected:boolean):void


---@class ComBtnSelectDropView : WBP_ComBtnSelectDropView
---@field public controller ComBtnSelectDrop
local ComBtnSelectDropView = DefineClass("ComBtnSelectDropView", UIView)

function ComBtnSelectDropView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.Button, "OnClick_Button")

	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

return ComBtnSelectDropView
