kg_require("Gameplay.LogicSystem.CommonUI.ComSlcTips.ComBtnSelectDropView")

---@class ComBtnSelectDrop : UIComponent
---@field public View WBP_ComBtnSelectDropView
local ComBtnSelectDrop = DefineClass("ComBtnSelectDrop", UIComponent)

function ComBtnSelectDrop:SetData(data, clickCallBack, owner)
    self.clickFunc = clickCallBack
    self.index = data.index
    self.owner = owner
	self.View.Text_Content:SetJustification(data.justification or 0)
    self.View.Text_Content:SetText(data.content)
end

function ComBtnSelectDrop:OnClick_Button()
    if self.clickFunc then
        self.clickFunc(self.index)
    end
	self.parent.parent:CloseSelf()	--luacheck:ignore
end

return ComBtnSelectDrop