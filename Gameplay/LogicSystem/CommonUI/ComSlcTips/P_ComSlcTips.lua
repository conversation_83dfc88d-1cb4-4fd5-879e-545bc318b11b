

---@class P_ComSlcTips : UIController
---@field public View WBP_ComSlcTipsView
local P_ComSlcTips = DefineClass("P_ComSlcTips", UIController)
local ComBtnSelectDrop = kg_require("Gameplay.LogicSystem.CommonUI.ComSlcTips.ComBtnSelectDrop")

function P_ComSlcTips:OnCreate()
    ---下拉框
    self.SelectList = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.VB_Btn, ComBtnSelectDrop)
    ---下拉框数据
    self.SelectData = {}
end

function P_ComSlcTips:OnRefresh(params)
	if not params.SelectData then
		return
	end
    self.SelectData = params.SelectData
    self.ClickCallBack = params.ClickCallBack
    self.SelectList:SetData(#self.SelectData)
    if params.initSelectIndex then
        self.SelectList:Sel(params.initSelectIndex)
    end
    self.View.SB.Slot:SetPosition(params.pos)
end

function P_ComSlcTips:OnRefresh_VB_Btn(widget, index, selected)
    widget:SetData(self.SelectData[index], self.ClickCallBack)
    widget.View:SetSelected(selected)
end

function P_ComSlcTips:UpdateBoundRectWidgets()
	self.widget:AddChildrenToPanelRegionWidgets(self.View.SB, true)
end

return P_ComSlcTips