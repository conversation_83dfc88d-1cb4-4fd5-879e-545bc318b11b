---@class WBP_PVPMatchPlayerView : WBP_PVPMatchPlayer_C
---@field public WidgetRoot WBP_PVPMatchPlayer_C
---@field public Img_Add C7Image
---@field public BgBrush SlateBrush
---@field public Type number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetBg fun(self:self,Type:number):void


---@class P_PVPMatchPlayerItemView : WBP_PVPMatchPlayerView
---@field public controller P_PVPMatchPlayerItem
local P_PVPMatchPlayerItemView = DefineClass("P_PVPMatchPlayerItemView", UIView)

function P_PVPMatchPlayerItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

return P_PVPMatchPlayerItemView
