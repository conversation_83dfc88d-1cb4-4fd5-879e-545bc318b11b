---@class P_PVPReady : UIController
---@field public View WBP_PVPReadyView 准备界面
local P_PVPReady = DefineClass("P_PVPReady", UIController)
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"

P_PVPReady.eventBindMap = {
    [EEventTypesV2.TEAM_ARENA_MATCH_STATE_CHANGE] = "OnMatchStateChange",
    [EEventTypesV2.TEAM_ARENA_MATCH_CONFIRM_STATE_CHANGE] = "OnReadyStateChange",
}

--- 倒计时数字图片地址
P_PVPReady.ImagePath = {
    [9] = UIAssetPath.UI_CountDown_Img_BigTime_Num9,
    [8] = UIAssetPath.UI_CountDown_Img_BigTime_Num8,
    [7] = UIAssetPath.UI_CountDown_Img_BigTime_Num7,
    [6] = UIAssetPath.UI_CountDown_Img_BigTime_Num6,
    [5] = UIAssetPath.UI_CountDown_Img_BigTime_Num5,
    [4] = UIAssetPath.UI_CountDown_Img_BigTime_Num4,
    [3] = UIAssetPath.UI_CountDown_Img_BigTime_Num3,
    [2] = UIAssetPath.UI_CountDown_Img_BigTime_Num2,
    [1] = UIAssetPath.UI_CountDown_Img_BigTime_Num1,
    [0] = UIAssetPath.UI_CountDown_Img_BigTime_Num0,
}

--- 倒计时图片背景图地址
P_PVPReady.ImageShadePath = {
    [0] = UIAssetPath.UI_CountDown_Img_BigTime_Num0a,
    [1] = UIAssetPath.UI_CountDown_Img_BigTime_Num1a,
    [2] = UIAssetPath.UI_CountDown_Img_BigTime_Num2a,
    [9] = UIAssetPath.UI_CountDown_Img_BigTime_Num9a,
    [8] = UIAssetPath.UI_CountDown_Img_BigTime_Num8a,
    [7] = UIAssetPath.UI_CountDown_Img_BigTime_Num7a,
    [6] = UIAssetPath.UI_CountDown_Img_BigTime_Num6a,
    [5] = UIAssetPath.UI_CountDown_Img_BigTime_Num5a,
    [4] = UIAssetPath.UI_CountDown_Img_BigTime_Num4a,
    [3] = UIAssetPath.UI_CountDown_Img_BigTime_Num3a,
}

function P_PVPReady:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.ReadyBtn.Btn_Com, self.OnClickConfirmReady)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.CancelBtn.Btn_Com, self.OnClickCancelBtn)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.FoldBtn.Big_Button_ClickArea, self.OnClickFoldBtn)

    self.View.CancelBtn.Text_Com:SetText(StringConst.Get("EXIT"))
    self.View.ReadyBtn.Text_Com:SetText(StringConst.Get("ARENA3V3_CONFIRM_READY"))
end

function P_PVPReady:OnRefresh(matchInfo, bPlayAnimation)
    self.bFive = Game.PVPSystem.PVPMatchType == Enum.EMatchTypeData.MATCH_TYPE_5V5
    self.selfCampId = Game.me.PvpMatchInfo.PvpMatchCampID --玩家所在的阵营id
    self.View.Party.WidgetRoot:SetPlayerNum(self.bFive)
    self.View.Enemy.WidgetRoot:SetPlayerNum(self.bFive)
    self.matchInfo = matchInfo
    self:UpdateReadyState()
    self:StartReadyCountdown()

	if bPlayAnimation then
		--- 播放进入的动效
		self:PlayAnimation(self.View.WidgetRoot, self.View.WidgetRoot.Ani_Fadein_Manual, 0, 1,
			EUMGSequencePlayMode.Forward, 1, false)
	end
	
    ---显示PVP类型字样
    local PVPNum = self.bFive and 5 or 3
    self.View.WBP_CountDownPVP.Text_PVPNum:SetText(PVPNum.."v"..PVPNum)
end

--准备状态变更
function P_PVPReady:OnReadyStateChange(campId, memberId, prepared)
    local members = self.matchInfo.info[campId].members
    for index, info in pairs(members) do
        if info.id == memberId then
            if campId == self.selfCampId then
                self.View.Party.WidgetRoot:SetLight(self.bFive, prepared, index)
            else
                self.View.Enemy.WidgetRoot:SetLight(self.bFive, prepared, index)
            end
        end
    end
end

---更新玩家的准备信息
function P_PVPReady:UpdateReadyState()
    for campIndex, campListInfo in pairs(self.matchInfo.info) do
        if campIndex == self.selfCampId then
            for index = 1, #campListInfo.members do
				local campInfo = campListInfo.members[index]
				if campInfo.id == Game.me.eid then
					self.bConfirmed = campInfo.prepared
				end
				local Delay = self.matchInfo.delayPrepareTime[campInfo.id] or 0
				local bPrepared = campListInfo.members[index].prepared and (_G._now(1) > Game.PVPSystem.model.PvpMatchReadyTime + Delay)
                self.View.Party.WidgetRoot:SetLight(self.bFive, bPrepared, index)
            end
        else
            for index = 1, #campListInfo.members do
				local Delay = self.matchInfo.delayPrepareTime[campListInfo.members[index].id] or 0
				local bPrepared = campListInfo.members[index].prepared and (_G._now(1) > Game.PVPSystem.model.PvpMatchReadyTime + Delay)
                self.View.Enemy.WidgetRoot:SetLight(self.bFive, bPrepared, index)
            end
        end
    end

    self:UpdateBtnState()
end

--当有机器人玩家时，计算准备延时
function P_PVPReady:MonitorBotPrepareState()
	for campIndex, campListInfo in pairs(self.matchInfo.info) do
		if campIndex == self.selfCampId then
			for index = 1, #campListInfo.members do
				local Delay = self.matchInfo.delayPrepareTime[campListInfo.members[index].id] or 0
				local bPrepared = campListInfo.members[index].prepared and (_G._now(1) > Game.PVPSystem.model.PvpMatchReadyTime + Delay)
				self.View.Party.WidgetRoot:SetLight(self.bFive, bPrepared, index)
			end
		else
			for index = 1, #campListInfo.members do
				local Delay = self.matchInfo.delayPrepareTime[campListInfo.members[index].id] or 0
				local bPrepared = campListInfo.members[index].prepared and (_G._now(1) > Game.PVPSystem.model.PvpMatchReadyTime + Delay)
				self.View.Enemy.WidgetRoot:SetLight(self.bFive, bPrepared, index)
			end
		end
	end
end

---开启准备倒计时
function P_PVPReady:StartReadyCountdown()
    --local confirmEndTime = Game.me.PvpMatchInfo.PvpMatchConfirmEndTime or Game.PVPSystem.PvpMatchConfirmEndTime
	local confirmEndTime = Game.PVPSystem.PvpMatchConfirmEndTime or 0
    Log.Debug('3v3, confirmEndTime:', confirmEndTime)
    local LeftTime = 0
    if confirmEndTime and confirmEndTime > 0 then
        LeftTime = confirmEndTime - _G._now(0) / 1000000
    end
    if LeftTime > 0 then
		local ReadyContDownImageExcelData = Game.TableData.GetReadyCountDownImageDataTable()
        self:StartTimer('ReadyTimer',
            function()
                LeftTime = confirmEndTime -  _G._now(0) / 1000000
                if LeftTime > 0 and LeftTime < 10 then
					LeftTime = math.floor(LeftTime)
					self:MonitorBotPrepareState()
                    self.View.WBP_CountDownPVP.Img_NumR:SetVisibility(ESlateVisibility.Collapsed)
                    self.View.WBP_CountDownPVP.Img_NumL:SetVisibility(ESlateVisibility.selfHitTestInvisible)
					self:SetImageByRes(self.View.WBP_CountDownPVP.Img_NumL, self.View.WBP_CountDownPVP.NumArrayLight:Get(LeftTime))
					
                    self.View.WBP_CountDownPVP.Img_NumShadowR:SetVisibility(ESlateVisibility.Collapsed)
                    self.View.WBP_CountDownPVP.Img_NumShadowL:SetVisibility(ESlateVisibility.selfHitTestInvisible)
					self:SetImageByRes(self.View.WBP_CountDownPVP.Img_NumShadowL, self.View.WBP_CountDownPVP.NumArrayDark:Get(LeftTime))
					
                elseif LeftTime >= 10 then
					self:MonitorBotPrepareState()
					local left = math.floor(LeftTime / 10)
                    self.View.WBP_CountDownPVP.Img_NumL:SetVisibility(ESlateVisibility.selfHitTestInvisible)
                    self.View.WBP_CountDownPVP.Img_NumR:SetVisibility(ESlateVisibility.selfHitTestInvisible)
					self:SetImageByRes(self.View.WBP_CountDownPVP.Img_NumL, self.View.WBP_CountDownPVP.NumArrayLight:Get(left))
					self:SetImageByRes(self.View.WBP_CountDownPVP.Img_NumShadowL, self.View.WBP_CountDownPVP.NumArrayDark:Get(left))
					local right = math.floor(LeftTime % 10)
                    self.View.WBP_CountDownPVP.Img_NumShadowR:SetVisibility(ESlateVisibility.selfHitTestInvisible)
                    self.View.WBP_CountDownPVP.Img_NumShadowL:SetVisibility(ESlateVisibility.selfHitTestInvisible)
					self:SetImageByRes(self.View.WBP_CountDownPVP.Img_NumR, self.View.WBP_CountDownPVP.NumArrayLight:Get(right))
					self:SetImageByRes(self.View.WBP_CountDownPVP.Img_NumShadowR, self.View.WBP_CountDownPVP.NumArrayDark:Get(right))
                else
                    self:StopTimer("ReadyTimer")
					self:CloseUI()
                    return
                end
            end,
            100, -1, nil, true
        )
    end
end

--就位按钮
function P_PVPReady:OnClickConfirmReady()
    self.bConfirmed = true
    self:SendReadyRpc()
    self:UpdateBtnState()
end

--取消按钮
function P_PVPReady:OnClickCancelBtn()
    self.bConfirmed = false
    self:SendReadyRpc()
    self:CloseUI()
end

--收起按钮
function P_PVPReady:OnClickFoldBtn()
    ---隐藏准备界面
    UIManager:GetInstance():HidePanel("P_PVPReady", "P_PVPReady")
end

---更新就位按钮的状态
function P_PVPReady:UpdateBtnState()
    if self.bConfirmed then
        self.View.ReadyBtn:SetVisibility(ESlateVisibility.Collapsed)
		self.View.CancelBtn:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.View.ReadyBtn:SetVisibility(ESlateVisibility.Visible)
		self.View.CancelBtn:SetVisibility(ESlateVisibility.Visible)
    end
end

---就位确认rpc
function P_PVPReady:SendReadyRpc()
    Game.PVPSystem.sender:ReqConfirmEnter(self.bConfirmed)
end

function P_PVPReady:OnMatchStateChange(MatchType, State)
    if State ~= Game.PVPSystem.EPVPMatchState.WAIT_CONFIRM then
        self:CloseUI()
    end
end

function P_PVPReady:CloseUI()
    UI.HideUI('P_PVPReady')
end


return P_PVPReady
