

---@class P_PVPPlayerItem : UIController
---@field public View WBP_PVPMatchPlayerView 模型卡片
local P_PVPPlayerItem = DefineClass("P_PVPPlayerItem", UIComponent)
local ESlateVisibility =  import("ESlateVisibility")

function P_PVPPlayerItem:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClickTeamInvitation)
end

function P_PVPMatch:OnDestroy()
    UIBase.OnDestroy(self)
end

function P_PVPPlayerItem:OnRefresh()
end

function P_PVPPlayerItem:RefreshItem(index, playerInfo, bClick)
    ---已有玩家信息的情况下不可点击
    if playerInfo then
		self.View.Btn_ClickArea:SetVisibility(ESlateVisibility.Collapsed)
		if playerInfo.name and playerInfo.name ~= "" then
			self.View.text_name:SetText(playerInfo.name)
		elseif playerInfo.botID then
			local botTableData = Game.TableData.GetBattleBotTemplateDataRow(playerInfo.botID)
			self.View.text_name:SetText(botTableData.Name)	
		end
    else
        if bClick then
            self.View.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
        else
            self.View.Btn_ClickArea:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
    
    self.View:SetBg(playerInfo and 0 or 2)
end

---点击加号邀请，同时创建队伍
function P_PVPPlayerItem:OnClickTeamInvitation()
    Game.TeamSystem:CreateTeam()
    Game.me:ReqTeamRefreshTarget(5300005, { 0, 1, 2 }, 0, "", 1)
    Game.NewUIManager:OpenPanel(UIPanelConfig.TeamInvite_Panel)
end


return P_PVPPlayerItem