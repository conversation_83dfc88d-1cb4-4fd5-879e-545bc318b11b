local ItemSmall = kg_require("Gameplay.LogicSystem.Item.ItemSmall")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---比武大会赛程奖励
---@class PVP_ChampionMatch_Progress_Item : UIListItem
---@field view PVP_ChampionMatch_Progress_ItemBlueprint
local PVP_ChampionMatch_Progress_Item = DefineClass("PVP_ChampionMatch_Progress_Item", UIListItem)

local ChampionConsts = kg_require ("Shared.Const.ChampionConsts")

local StringConst = kg_require("Data.Config.StringConst.StringConst")

---具体比赛类型的文本
PVP_ChampionMatch_Progress_Item.MatchTypeText = {
	[1] = StringConst.Get("PVP_CHAMPION_PLAYOFF_TEXT_FULL_R1"),
	[2] = StringConst.Get("PVP_CHAMPION_PLAYOFF_TEXT_FULL_R2"),
	[3] = StringConst.Get("PVP_CHAMPION_PLAYOFF_TEXT_FULL_R3"),
	[4] = StringConst.Get("PVP_CHAMPION_PLAYOFF_TEXT_FULL_R4"),
}

PVP_ChampionMatch_Progress_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_ChampionMatch_Progress_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_ChampionMatch_Progress_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVP_ChampionMatch_Progress_Item:InitUIComponent()
    ---@type ItemSmall
    self.WBP_ItemSmallCom = self:CreateComponent(self.view.WBP_ItemSmall, ItemSmall)
end

---UI事件在这里注册，此处为自动生成
function PVP_ChampionMatch_Progress_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_ChampionMatch_Progress_Item:InitUIView()
end

---刷新奖励信息
function PVP_ChampionMatch_Progress_Item:OnRefresh(Data)
	--根据比赛类型设置数据
	if Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.scheduleType ~= ChampionConsts.CHAMPION_SCHEDULE_TYPE.ELIMINATION then
		self.view.Text_Format:SetText(StringConst.Get("PVP_CHAMPION_SINGLE_GAME"))
		self.view.Text_Time:SetText("")
		self.WBP_ItemSmallCom:FillItem(Data.ItemID, 0, 0, false)
		self.view.WBP_ItemSmall:SetShowNum(false)
		self.view.WBP_ItemSmall:SetGet(Data.Result and Data.Result > 0 or false)
	else
		self.view.Text_Format:SetText(PVP_ChampionMatch_Progress_Item.MatchTypeText[self.index])
		self.view.Text_Time:SetText("")
		self.WBP_ItemSmallCom:FillItem(Data.ItemID, 0, 0, false)
		self.view.WBP_ItemSmall:SetShowNum(false)
		self.view.WBP_ItemSmall:SetGet(Data.Result and Data.Result > 0 or false)
	end
end

return PVP_ChampionMatch_Progress_Item
