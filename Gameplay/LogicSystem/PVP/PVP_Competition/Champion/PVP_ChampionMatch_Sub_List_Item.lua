local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---比武大会 赛程列表的队伍信息
---@class PVP_ChampionMatch_Sub_List_Item : UIListItem
---@field view PVP_ChampionMatch_Sub_List_ItemBlueprint
local PVP_ChampionMatch_Sub_List_Item = DefineClass("PVP_ChampionMatch_Sub_List_Item", UIListItem)

local ChampionConsts = kg_require ("Shared.Const.ChampionConsts")

PVP_ChampionMatch_Sub_List_Item.eventBindMap = {
	[_G.EEventTypes.PVP_CHAMPION_GET_TROOP_MEMBER_INFO] = "OnGetTeamMemberInfo"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_ChampionMatch_Sub_List_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_ChampionMatch_Sub_List_Item:InitUIData()
	---队伍ID
	---@type table
	self.TroopIDs = {}
	---是否有轮空
	---@type boolean
	self.bEmpty = false
	---队员信息
	---@type table
	self.TeamMemberInfo = {}
	---是否展开
	---@type boolean
	self.bExpand = false
end

--- UI组件初始化，此处为自动生成
function PVP_ChampionMatch_Sub_List_Item:InitUIComponent()
    ---@type UIListView
    self.TileList_LeftCom = self:CreateComponent(self.view.TileList_Left, UIListView)
    ---@type UIListView
    self.TileList_RightCom = self:CreateComponent(self.view.TileList_Right, UIListView)
end

---UI事件在这里注册，此处为自动生成
function PVP_ChampionMatch_Sub_List_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_Watch.OnClicked, "on_Btn_ClickArea_Watch_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_ChampionMatch_Sub_List_Item:InitUIView()
end

---面板打开的时候触发
function PVP_ChampionMatch_Sub_List_Item:OnRefresh(Data)
	self.bEmpty = false
	self.userWidget:SetBg(self.index % 2 ~= 0)
	self.userWidget:BP_SetSelf(0)
	table.clear(self.TroopIDs)
	self.TroopIDs = {0, 0}
	--双方队伍
	if Data.matchItems[1].name == "" and Data.matchItems[1].troopID == 0 then
		self.view.Text_Name_Left:SetText("轮空")
		self.view.Text_Num_Left:SetText("")
		self.view.Text_Tag:SetText("已结束")
		self.bEmpty = true
		self.userWidget:SetGameState(2)
		self.view.Text_Score_Left:SetText(0)
	else
		if Data.matchItems[1].troopID == Game.me.championTroopID then
			self.userWidget:BP_SetSelf(1)
		end
		self.view.Text_Name_Left:SetText(Data.matchItems[1].name)
		self.view.Text_Num_Left:SetText(Data.matchItems[1].score)
		self.view.Text_Score_Left:SetText(Data.matchItems[1].gameScore or 0)
		self.TroopIDs[1] = Data.matchItems[1].troopID
	end
	
	if Data.matchItems[2] then
		if Data.matchItems[2].troopID == Game.me.championTroopID then
			self.userWidget:BP_SetSelf(2)
		end
		self.view.Text_Name_Right:SetText(Data.matchItems[2].name)
		self.view.Text_Num_Right:SetText(Data.matchItems[2].score)
		self.view.Text_Score_Right:SetText(Data.matchItems[2].gameScore or 0)
		self.TroopIDs[2] = Data.matchItems[2].troopID
	else
		self.view.Text_Name_Right:SetText("轮空")
		self.view.Text_Num_Right:SetText("")
		self.view.Text_Tag:SetText("已结束")
		self.bEmpty = true
		self.userWidget:SetGameState(2)
		self.view.Text_Score_Right:SetText(0)
	end

	if Data.winTroopID ~= 0 and self.TroopIDs[1] == Data.winTroopID then
		self:SetGameStatus(Data.winTroopID, 1)
	elseif Data.winTroopID ~= 0 and self.TroopIDs[2] == Data.winTroopID then
		self:SetGameStatus(Data.winTroopID, 2)
	else
		local Winner = Data.winTroopID == 0 and 0 or 1
		if Winner > 0 then
			Winner = self.TroopIDs[1] == Data.winTroopID and 1 or 2
		end
		self:SetGameStatus(Data.winTroopID, Winner)
	end
	
	--判断是小组赛还是淘汰赛
	self.userWidget:BP_SetGameType(
		self.parentComponent.parentComponent.parentComponent:GetScheduleType() == ChampionConsts.CHAMPION_SCHEDULE_TYPE.GROUP_BATTLE
	)
	
	local bExpand = self.parentComponent.parentComponent:GetExpandedItemIndex() == self.index
	self.bExpand = bExpand
	--如果是展开状态，查询两个队伍的队员信息
	--为了减少请求和内存的开销，不能重复请求
	--PVPChampionMemberInfoCache
	if bExpand then
		local TroopIDs
		if self.TroopIDs[1] > 0 and 
			not Game.PVPSystem.model.PVPChampionMemberInfoCache[self.TroopIDs[1]] then
			TroopIDs = {}
			table.insert(TroopIDs, Data.matchItems[1].troopID)
		end
		if self.TroopIDs[2] > 0 and
			not Game.PVPSystem.model.PVPChampionMemberInfoCache[self.TroopIDs[2]] then
			TroopIDs = TroopIDs or {}
			table.insert(TroopIDs, Data.matchItems[2].troopID)
		end
		if TroopIDs and next(TroopIDs) then
			Game.PVPSystem.sender:ReqChampionGetOtherTroopMemberInfo(TroopIDs)
		else
			self:OnGetTeamMemberInfo()
		end
	else
		self:ToggleExpand(bExpand)
	end
end

---展示队员信息
function PVP_ChampionMatch_Sub_List_Item:SetMember()
	--左侧和右侧的两个队伍按照请求的顺序设置队员信息
	if self.TeamMemberInfo[self.TroopIDs[1]] then
		self.TileList_LeftCom:Refresh(self.TeamMemberInfo[self.TroopIDs[1]])
	else
		self.TileList_LeftCom:Clear()
	end
	if self.TeamMemberInfo[self.TroopIDs[2]] then
		self.TileList_RightCom:Refresh(self.TeamMemberInfo[self.TroopIDs[2]])
	else
		self.TileList_RightCom:Clear()
	end
end

function PVP_ChampionMatch_Sub_List_Item:OnGetTeamMemberInfo(InfoMap)
	--列表需要Array
	local bNoExpand = false
	table.clear(self.TeamMemberInfo)
	for TeamID, TeamInfo in pairs(self.TroopIDs) do
		self.TeamMemberInfo[TeamInfo] = {}
		if Game.PVPSystem.model.PVPChampionMemberInfoCache[TeamInfo] then
			for _, Member in pairs(Game.PVPSystem.model.PVPChampionMemberInfoCache[TeamInfo]) do
				table.insert(self.TeamMemberInfo[TeamInfo], Member)
			end
		elseif TeamInfo > 0 then
			bNoExpand = true
		end
	end
	if not bNoExpand then
		--检查自己显示的队伍是否已经有cache，没有的话不要展开
		self:ToggleExpand(self.bExpand)
		self:SetMember()
	end
end

---设置比赛状态
function PVP_ChampionMatch_Sub_List_Item:SetGameStatus(WinTroopID, Winner)
	--轮空比赛直接结束
	if self.bEmpty then
		self.view.Text_Tag:SetText("已结束")
		self.userWidget:SetGameState(2)
		self.userWidget:SetWinTeam(Winner)
		return
	end
	--如果WinTroopID不是0，那么比赛已经结束
	--但是是否开始需要判断时间戳
	local Stage = WinTroopID ~= 0 and 2 or 0
	if Stage == 2 then
		self.view.Text_Tag:SetText("已结束")
	else
		local bStarted = self.parentComponent.parentComponent:IsCurrentRoundAlreadyBegan()
		self.view.Text_Tag:SetText(bStarted and "进行中" or "未开始")
		Stage = bStarted and 1 or 0
	end
	self.userWidget:SetGameState(Stage)
	self.userWidget:SetWinTeam(Winner)
end

---设置展开与关闭
function PVP_ChampionMatch_Sub_List_Item:ToggleExpand(bExpand)
	self.userWidget:SetExpanded(bExpand)
end


--- 此处为自动生成
function PVP_ChampionMatch_Sub_List_Item:on_Btn_ClickArea_Watch_Clicked()
end

return PVP_ChampionMatch_Sub_List_Item
