local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PVP_ChampionTeam_Member_Name_Item : UIListItem
---@field view PVP_ChampionTeam_Member_Name_ItemBlueprint
local PVP_ChampionTeam_Member_Name_Item = DefineClass("PVP_ChampionTeam_Member_Name_Item", UIListItem)

local ESlateVisibility = import("ESlateVisibility")

PVP_ChampionTeam_Member_Name_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_ChampionTeam_Member_Name_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_ChampionTeam_Member_Name_Item:InitUIData()
	---玩家EID
	---@type string
	self.EID = ""
end

--- UI组件初始化，此处为自动生成
function PVP_ChampionTeam_Member_Name_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PVP_ChampionTeam_Member_Name_Item:InitUIEvent()
    self:AddUIEvent(self.view.WBP_ComHead_lua.Btn_Head_lua.OnClicked, "on_WBP_ComHead_luaBtn_Head_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_ChampionTeam_Member_Name_Item:InitUIView()
end

function PVP_ChampionTeam_Member_Name_Item:OnRefresh(UserInfo)
	self.view.Text_Name_lua:SetText(UserInfo.name)
	self.view.WBP_ComHead_lua.KImg_Add:SetVisibility(ESlateVisibility.Collapsed)
	self.view.WBP_ComHead_lua.Text_level_lua:SetText(UserInfo.level)
	self.view.WBP_ComHead_lua:SetIsCaptain(UserInfo.isCaptain)
	local IconPath = Game.TableData.GetPlayerSocialDisplayDataRow(UserInfo.profession)[UserInfo.sex].ClassLogo
	self:SetImage(self.view.WBP_ComHead_lua.icon_head_lua, IconPath)
	self.userWidget:Event_UI_Mid(UserInfo.id == Game.me.eid or false)
end

---点击头像展示玩家的个人名片弹窗
function PVP_ChampionTeam_Member_Name_Item:on_WBP_ComHead_luaBtn_Head_lua_Clicked()
	if self.EID ~= Game.me.eid then
		
	end
end

return PVP_ChampionTeam_Member_Name_Item
