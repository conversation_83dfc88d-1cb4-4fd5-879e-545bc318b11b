local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---比武大会 - 小组赛排行榜元素列表
---@class PVP_ChampionMatchRank_Sub_Item : UIListItem
---@field view PVP_ChampionMatchRank_Sub_ItemBlueprint
local PVP_ChampionMatchRank_Sub_Item = DefineClass("PVP_ChampionMatchRank_Sub_Item", UIListItem)

local StringConst = kg_require("Data.Config.StringConst.StringConst")


PVP_ChampionMatchRank_Sub_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_ChampionMatchRank_Sub_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_ChampionMatchRank_Sub_Item:InitUIData()
	---ID
	---@type number
	self.TroopID = -1
	---@type boolean
	self.bListItem = false
end

--- UI组件初始化，此处为自动生成
function PVP_ChampionMatchRank_Sub_Item:InitUIComponent()
    ---@type UISimpleList
    self.Canvas_TeamHeadCom = self:CreateComponent(self.view.Canvas_TeamHead, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function PVP_ChampionMatchRank_Sub_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_ChampionMatchRank_Sub_Item:InitUIView()
end
---面板打开的时候触发
function PVP_ChampionMatchRank_Sub_Item:OnRefresh(Data)
	--通知上级界面当前进度
	--self.parentComponent.parentComponent:UpdateLargestIndex(self.index)
	self:SetContent(Data, true)
end

---设置内容
---@param bListItem boolean 是否是列表元素，否则为独立展示的玩家自身对战信息
function PVP_ChampionMatchRank_Sub_Item:SetContent(Data, bListItem)
	self.bListItem = bListItem
	self.TroopID = Data.id
	if bListItem then
		if self.index > 3 then
			self.userWidget:BP_SetRankType(self.index % 2 == 0 and 3 or 4)
		else
			self.userWidget:BP_SetRankType(self.index - 1)
		end
		self.view.Text_Rank:SetText(self.index)
		local CachedData = Game.PVPSystem.model.PVPChampionMemberInfoCache[self.TroopID]
		if CachedData then
			for Member, MemberInfo in pairs(CachedData) do
				if Member == Data.leaderID then
					self.view.Text_CaptainName:SetText(MemberInfo.rolename)
					local SocialDisplayData = Game.TableData.GetPlayerSocialDisplayDataRow(MemberInfo.professionID)
					self:SetImage(self.view.Img_CaptainCareer, SocialDisplayData[0].ClassLogoHud)
					break
				end
			end
		end
	else
		self.userWidget:BP_SetRankType(5)
		self.view.Text_Rank:SetText(Data.rank)
		if Data.rank > 50 then
			self.view.Text_Rank:SetText(StringConst.Get("PVP_CHAMPION_RANK_NO_RANK"))
		end
		self.view.Text_CaptainName:SetText(Game.me.Name)
		local SocialDisplayData = Game.TableData.GetPlayerSocialDisplayDataRow(Game.me.Profession)
		self:SetImage(self.view.Img_CaptainCareer, SocialDisplayData[0].ClassLogoHud)
	end
	self.view.Text_Team:SetText(Data.name)
	self.view.Text_Record:SetText(string.format(StringConst.Get("PVP_CHAMPION_RANK_WIN_LOSE"), Data.winTimes, Data.loseTimes))
	self.view.Text_Honor:SetText(Data.score)
end

------------------------------------------------------------------------------------------------------------------------------------------------------

---点击查看具体队员信息
function PVP_ChampionMatchRank_Sub_Item:on_Btn_ClickArea_Clicked()
	Game.PVPSystem:ShowPVPChampionRankMemberInfoTip(self.TroopID, self.view.Btn_ClickArea:GetCachedGeometry())
end

return PVP_ChampionMatchRank_Sub_Item
