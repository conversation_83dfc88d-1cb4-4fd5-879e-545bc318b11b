local UIComTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComTabList")
local PVP_ChampionMatch_Progress_Item = kg_require("Gameplay.LogicSystem.PVP.PVP_Competition.Champion.PVP_ChampionMatch_Progress_Item")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local ItemSmall = kg_require("Gameplay.LogicSystem.Item.ItemSmall")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

local UITabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UITabList")

---比武大会 赛程与排行面板
---@class PVP_ChampionMatch_Panel : UIPanel
---@field view PVP_ChampionMatch_PanelBlueprint
local PVP_ChampionMatch_Panel = DefineClass("PVP_ChampionMatch_Panel", UIPanel)

local ESlateVisibility = import("ESlateVisibility")
local ChampionConsts = kg_require("Shared.Const.ChampionConsts")
local PVPConst = kg_require("Shared.Const.PVPConst")

---界面顶部的比赛轮次文本
PVP_ChampionMatch_Panel.MatchStageText = {
	[ChampionConsts.CHAMPION_ACTIVITY_STAGE.CLOSE] = "赛事未开始",
	[ChampionConsts.CHAMPION_ACTIVITY_STAGE.SIGN_UP] = "报名中",
	[ChampionConsts.CHAMPION_ACTIVITY_STAGE.SIGN_UP_LOCK] = "报名已锁定",
	[ChampionConsts.CHAMPION_ACTIVITY_STAGE.PREPARE] = "比赛准备中",
	[ChampionConsts.CHAMPION_ACTIVITY_STAGE.CALCULATE] = "赛后结算",
}

---具体比赛类型的文本
PVP_ChampionMatch_Panel.MatchTypeText = {
	["GroupBattle"] = "小组赛（第%s/%s轮）",
	[1] = "淘汰赛十六强",
	[2] = "淘汰赛八强",
	[3] = "淘汰赛半决赛",
	[4] = "淘汰赛决赛",
}

---子界面
---@type table<string>
PVP_ChampionMatch_Panel.SubPage =
{
	[UICellConfig.PVP_ChampionMatch_Sub_Page] = { PageName = UICellConfig.PVP_ChampionMatch_Sub_Page, Index = 1, Slot = "HB_Center_Content_Group" },
	[UICellConfig.PVP_ChampionMatchRank_Sub_Page] = { PageName = UICellConfig.PVP_ChampionMatchRank_Sub_Page, Index = 1, Slot = "HB_Center_Content_Full" },
}

PVP_ChampionMatch_Panel.eventBindMap = {
	[_G.EEventTypes.PVP_CHAMPION_GET_TROOP_DETAIL] = "OnGetTeamDetail",
	[_G.EEventTypes.PVP_CHAMPION_GET_PERSONAL_MATCHES] = "OnGetPersonalInfo",
	[_G.EEventTypes.PVP_CHAMPION_GET_STAGE_INFO] = "OnGetStageInfo",
	[_G.EEventTypes.PVP_CHAMPION_GET_ELIMINATION_MATCHES] = "OnGetEliminationMatches",
	[_G.EEventTypes.PVP_CHAMPION_GET_REGION_NUM] = "OnGetRegionNum"
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_ChampionMatch_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_ChampionMatch_Panel:InitUIData()
	---页签数据
	---@type table
	self.TablistData = {}
	
	---当前比赛阶段
	---@type number
	self.CurrentGameScheduleType = -1
	---当前展示的比赛阶段
	---@type number
	self.CurrentDisplayedScheduleType = -1
	
	---子面板组件
	---@type table<UIComponent>
	self.pageComponents = {}
	---当前展示的赛区ID
	---@type number
	self.CurrentRegionID = 0
	---赛区列表
	---@type table
	self.RegionDataList = {}
	---当前展示子界面
	---@type string
	self.CurrentCurrentPageName = ""

	---本轮比赛的信息
	---当前最新轮的比赛信息
	---@type table
	self.StageInfo = {}
	---当前最新轮的玩家自身比赛信息
	---@type table
	self.PlayerMatchInfoMap = {}
	
	---当前游戏阶段
	---@type number
	self.GameStage = 1
	---当前游戏轮次 不是最新轮次
	---@type number
	self.CurrentDisplayedGameRound = -1
	---倒计时时间
	---@type number
	self.CountDownTime = 0
end

--- UI组件初始化，此处为自动生成
function PVP_ChampionMatch_Panel:InitUIComponent()
    ---@type UIComTabList
    self.WBP_ComTabListMainCom = self:CreateComponent(self.view.WBP_ComTabListMain, UIComTabList)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
    ---@type UIComDropDown
    self.WBP_ComSelectDropType1Com = self:CreateComponent(self.view.WBP_ComSelectDropType1, UIComDropDown)
    ---@type UIComDiyTitle
    self.WBP_ComDIYTextCom = self:CreateComponent(self.view.WBP_ComDIYText, UIComDiyTitle)
    ---@type UISimpleList
    self.HB_Item_GroupCom = self:CreateComponent(self.view.HB_Item_Group, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function PVP_ChampionMatch_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComSelectDropType1Com.onItemSelected, "on_WBP_ComSelectDropType1Com_ItemSelected")
    self:AddUIEvent(self.WBP_ComTabListMainCom.onItemSelected, "on_WBP_ComTabListMainCom_ItemSelected")
    self:AddUIEvent(self.view.Btn_Go.OnClicked, "on_Btn_Go_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_ChampionMatch_Panel:InitUIView()
end

---面板打开的时候触发
function PVP_ChampionMatch_Panel:OnRefresh()
	self:InitContent()
	if not Game.me.championTroopID or Game.me.championTroopID <= 0 then
		self:CollectMatchInfo()
	else
		--首先要请求一次玩家自己的队伍信息，因为如果玩家没打开过组队界面model不会存储相关数据
		Game.PVPSystem.sender:ReqGetChampionTroopDetail()
	end
end

function PVP_ChampionMatch_Panel:OnClose()
	if next(self.pageComponents) then
		for _, component in pairs(self.pageComponents) do
			component:CloseSelf()
		end
	end
end

------------------------------------------------------------------------------------------------------------------------------------------------------

---初始化所有静态内容
function PVP_ChampionMatch_Panel:InitContent()
	self.view.WBP_ComBackTitle.Text_Title:SetText("比武大会")
	self.view.Text_Title:SetText("比赛轮次")
	self.view.Text_Sub_Title:SetText("")
	self.view.Text_Sub_Num:SetText("")
	self.view.WBP_PVP_ChampionMatch_Progress_Dot_2.KGTextBlock_SignUp:SetText("结束")
end

---1. 获取比赛阶段的信息
function PVP_ChampionMatch_Panel:CollectMatchInfo()
	Game.PVPSystem.sender:ReqChampionGetProgress(0)
	self:StartTimer(
		"PVPChampionMatchReqTimer",
		function()
			self:OnTimerCollectMatchInfo()
		end,
		10000,
		-1,
		false
	)
end

---定时更新
function PVP_ChampionMatch_Panel:OnTimerCollectMatchInfo()
	Game.PVPSystem.sender:ReqChampionGetProgress(self.CurrentRegionID)
end

---2. 获取到玩家的比赛信息后，更新奖励状态，并且将玩家所在赛区和当前轮次作为默认值通知子界面刷新
function PVP_ChampionMatch_Panel:RefreshPlayerInfo()
	--奖励与进度条
	self:UpdateRewardStatus()
	self:UpdateMatchText()
end

function PVP_ChampionMatch_Panel:GetRegionID()
	return self.CurrentRegionID
end

function PVP_ChampionMatch_Panel:GetScheduleType()
	return self.CurrentDisplayedScheduleType
end

---更新奖励内容
function PVP_ChampionMatch_Panel:UpdateRewardStatus()
	--不是玩家的赛区不更新，没有战队的玩家设置为默认态
	if Game.me.championTroopID ~= 0 and self.CurrentRegionID ~= 0 and self.CurrentRegionID ~= Game.PVPSystem.model.PVPChampionCurrentStageInfo.PlayerRegionID then
		return
	end
	self.view.WBP_PVP_ChampionMatch_Progress_Dot_1:BP_SetStage(false)
	self.view.WBP_PVP_ChampionMatch_Progress_Dot_2:BP_SetStage(false)
	local RewardProgressData, Progress = Game.PVPSystem:GetCurrentChampionRewardInfo()
	self.HB_Item_GroupCom:Refresh(RewardProgressData)
	local FinishedGameCount = 0
	for _, RoundInfo in pairs(RewardProgressData) do
		if RoundInfo.Result and RoundInfo.Result > 0 then
			FinishedGameCount = FinishedGameCount + 1
		end
	end

	--淘汰赛使用交互提供的进度条位置
	if Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.scheduleType ~= ChampionConsts.CHAMPION_SCHEDULE_TYPE.ELIMINATION then
		self.view.WBP_PVP_ChampionMatch_Progress_Dot_1.KGTextBlock_SignUp:SetText("报名")
		self.view.ProgressBar:SetPercent((FinishedGameCount == #RewardProgressData) and 1 or (Progress / (#RewardProgressData + 1)))
	else
		self.view.WBP_PVP_ChampionMatch_Progress_Dot_1.KGTextBlock_SignUp:SetText("小组赛")
		local DisplayRoundProgress = (FinishedGameCount == #RewardProgressData) and FinishedGameCount or Progress
		self.view.ProgressBar:SetPercent(ChampionConsts.CHAMPION_ELIMINATION_REWARD_STATUS_PROGRESS_BAR[DisplayRoundProgress] or 0)
	end

	--进度条两端
	local TroopInfo = Game.PVPSystem:GetPVPChampionTroopInfo()
	self.view.WBP_PVP_ChampionMatch_Progress_Dot_1:SetLight(TroopInfo.signUp ~= nil and TroopInfo.signUp or false)
	self.view.WBP_PVP_ChampionMatch_Progress_Dot_2:SetLight(Progress == #RewardProgressData or false)
end

---收到子界面的轮次切换后更新
function PVP_ChampionMatch_Panel:UpdateGameRoundDisplay(NewDisplayRound)
	self.CurrentDisplayedGameRound = NewDisplayRound
	self:UpdateMatchText()
end

---获取本轮比赛是否开始
function PVP_ChampionMatch_Panel:IsCurrentRoundAlreadyBegan()
	return self.CurrentDisplayedGameRound <= Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.roundIndex 
		and Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.roundStatus > PVPConst.PVP_OUTTER_STATUS.PREPARE
end

---更新赛程进度文本
function PVP_ChampionMatch_Panel:UpdateMatchText()
	local CurrentStage = Game.PVPSystem.model.PVPChampionCurrentStageInfo.Stage
	local MatchInfoMap = Game.PVPSystem.model.PVPChampionPlayerOwnInfo
	--开战阶段可能需要倒计时
	if CurrentStage == ChampionConsts.CHAMPION_ACTIVITY_STAGE.BATTLE then
		--主标题：比赛轮次
		--副标题：比赛时间或状态
		local MatchTypeText = ""
		if self.CurrentDisplayedGameRound == 0 then
			MatchTypeText = "比赛准备中"
		elseif self.CurrentDisplayedScheduleType == ChampionConsts.CHAMPION_SCHEDULE_TYPE.GROUP_BATTLE then
			MatchTypeText = string.format(PVP_ChampionMatch_Panel.MatchTypeText.GroupBattle,
				math.max(1, self.CurrentDisplayedGameRound), Game.PVPSystem:GetPVPChampionBattleTotalRound(self.CurrentDisplayedScheduleType))
		else
			MatchTypeText = string.format(PVP_ChampionMatch_Panel.MatchTypeText[math.max(1, self.CurrentDisplayedGameRound)] or "淘汰赛")
		end
		--如果此时展示的比轮次和当前最新轮次不同，那么进行不同的展示
		if Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.roundIndex ~= self.CurrentDisplayedGameRound then
			self.view.Text_Title:SetText(MatchTypeText)
			self.view.Text_Sub_Title:SetText("")
			self.view.Text_Sub_Num:SetVisibility(ESlateVisibility.Collapsed)
			return
		end

		--如果显示的比赛阶段不一样，如淘汰赛查看小组赛赛程，直接隐藏提示文本
		local bShowSubTitle = self.CurrentDisplayedScheduleType > 0 and 
			self.CurrentDisplayedScheduleType == Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.scheduleType
		
		--获取时间等信息
		local RoundStatus = Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.roundStatus
		if RoundStatus == PVPConst.PVP_OUTTER_STATUS.INIT and bShowSubTitle then
			self.view.Text_Title:SetText(MatchTypeText)
			self.view.Text_Sub_Title:SetText("匹配中")
		elseif RoundStatus == PVPConst.PVP_OUTTER_STATUS.PREPARE and bShowSubTitle then
			self.view.Text_Sub_Num:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self.view.Text_Title:SetText(MatchTypeText)
			local CountDownTime = Game.PVPSystem:GetPVPChampionGroupMatchTitleAndCountDownInfo(
				_G._now(1), 
				Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo
			)
			self.CountDownTime = CountDownTime
			self.view.Text_Sub_Title:SetText("本轮开启倒计时：")
			self:StartTimer(
				"CountDownTimer",
				function()
					self:OnTimer()
				end,
				1000,
				-1,
				false,
				true
			)
		elseif RoundStatus == PVPConst.PVP_OUTTER_STATUS.BATTLE and bShowSubTitle then
			self.view.Text_Title:SetText(MatchTypeText)
			--确定玩家自己这场有没有打完 
			--玩家自己的比赛还没打或者显示的赛区不一样的时候，显示比赛对战中
			if Game.PVPSystem.model.PVPChampionPlayerOwnInfo.RegionID ~= Game.PVPSystem.model.PVPChampionCurrentStageInfo.RegionID or 
				MatchInfoMap[self.CurrentDisplayedGameRound] and 
				MatchInfoMap[self.CurrentDisplayedGameRound].winTroopID == 0 then
				self.view.Text_Sub_Title:SetText("本轮比赛对战中")
			else
				self.view.Text_Sub_Title:SetText("等待其他队伍对战结束")
			end
		elseif not bShowSubTitle then
			self.view.Text_Title:SetText(MatchTypeText)
			self.view.Text_Sub_Title:SetText("小组赛已结束")
		else
			self.view.Text_Title:SetText(MatchTypeText)
			self.view.Text_Sub_Title:SetText("")
		end
	else
		self.view.Text_Title:SetText(PVP_ChampionMatch_Panel.MatchStageText[CurrentStage] or "已结束")
	end
end

function PVP_ChampionMatch_Panel:OnTimer()
	if self.CountDownTime <= 0 then
		self.view.Text_Sub_Num:SetText("")
		self:StopTimer("CountDownTimer")
		return
	end
	self.view.Text_Sub_Num:SetText(TimeUtils.FormatCountDownString(self.CountDownTime * 1000, false))
	self.CountDownTime = self.CountDownTime - 1
end

---更新切换赛程展示的按钮
function PVP_ChampionMatch_Panel:UpdateScheduleTypeBtn()
	if Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.scheduleType == ChampionConsts.CHAMPION_SCHEDULE_TYPE.ELIMINATION then
		self.view.Canvas_GoBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if self.CurrentDisplayedScheduleType == ChampionConsts.CHAMPION_SCHEDULE_TYPE.ELIMINATION then
			self.view.Text_Go:SetText("小组赛")
		else
			self.view.Text_Go:SetText("淘汰赛")
		end
	else
		self.view.Canvas_GoBtn:SetVisibility(ESlateVisibility.Collapsed)
	end
end

---切换时Round设置为1
function PVP_ChampionMatch_Panel:ToggleScheduleType()
	self.CurrentDisplayedScheduleType = 
	(self.CurrentDisplayedScheduleType == ChampionConsts.CHAMPION_SCHEDULE_TYPE.ELIMINATION) and 
		ChampionConsts.CHAMPION_SCHEDULE_TYPE.GROUP_BATTLE or 
		ChampionConsts.CHAMPION_SCHEDULE_TYPE.ELIMINATION
	self:UpdateScheduleTypeBtn()
	self.CurrentDisplayedGameRound = 1
	self.pageComponents[self.CurrentPageName]:OnChangeDisplayedScheduleType()
	Game.PVPSystem.sender:ReqChampionGetProgress(self.CurrentRegionID)
end

------------------------------------------------------------------------------------------------------------------------------------------------------

--region SubPageLogic

---更新页签
function PVP_ChampionMatch_Panel:RefreshTabList()
	if self.CurrentGameScheduleType <= 0 or self.CurrentGameScheduleType ~= Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.scheduleType then
		self.CurrentGameScheduleType = Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.scheduleType
		table.clear(self.TablistData)
		self.TablistData = Game.PVPSystem:GetPVPChampionMatchPanelTabData()
		self.WBP_ComTabListMainCom:Refresh(self.TablistData)
		self.WBP_ComTabListMainCom:SetSelectedItemByIndex(1, true)
	end
end

---显示子界面
---@param PageIndex number
function PVP_ChampionMatch_Panel:ShowSubPage(PageIndex)
	local PageName = self.TablistData[PageIndex].Page or UICellConfig.PVP_ChampionMatch_Sub_Page
	--关闭之前的界面
	if self.CurrentPageName ~= PageName and self.pageComponents[self.CurrentPageName] then 
		self.pageComponents[self.CurrentPageName]:CloseSelf()
		self.pageComponents[self.CurrentPageName] = nil
	end
	if self.pageComponents[PageName] then
		if not self.pageComponents[PageName]:IsShow() then
			self.pageComponents[PageName]:Show()
		end
	else
		self:AsyncLoadComponent(
			PVP_ChampionMatch_Panel.SubPage[PageName].PageName, self.view[PVP_ChampionMatch_Panel.SubPage[PageName].Slot],
			function(component) 
				self:OnSubPageReady(component, PageName) 
			end
		)
	end
end

---子界面加载完之后的回调
---@param PageIndex number
function PVP_ChampionMatch_Panel:OnSubPageReady(component, PageName)
	self.CurrentPageName = PageName
	self.pageComponents[PageName] = component
	--切换底部比赛进度的显示
	local BottomVisibility = PVP_ChampionMatch_Panel.SubPage[PageName].Slot == "HB_Center_Content_Group" and 
		ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed
	self.view.Canvas_Bottom_Group:SetVisibility(BottomVisibility)
	self.view.Img_Bottom_Bg_Left:SetVisibility(BottomVisibility)
	self.view.Img_Bottom_Bg_Right:SetVisibility(BottomVisibility)
end

--endregion SubPageLogic

------------------------------------------------------------------------------------------------------------------------------------------------------

function PVP_ChampionMatch_Panel:OnGetRegionNum()
	local CurrentRegionID = math.max(1, Game.PVPSystem.model.PVPChampionCurrentStageInfo.RegionID)
	local RegionDataList = Game.PVPSystem:GetPVPChampionRegionDataList()
	if not next(RegionDataList) then
		self.view.WBP_ComSelectDropType1:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	if #self.RegionDataList == #RegionDataList then
		return
	end
	self.RegionDataList = RegionDataList
	self.view.WBP_ComSelectDropType1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.WBP_ComSelectDropType1Com:Refresh(self.RegionDataList, CurrentRegionID, false)
end

function PVP_ChampionMatch_Panel:OnGetTeamDetail(TeamDetailInfo)
	self:CollectMatchInfo()
end

function PVP_ChampionMatch_Panel:OnGetStageInfo()
	--先刷新一次奖励进度，没有比赛的玩家也可以正常看到进度
	self:UpdateRewardStatus()
	Game.PVPSystem.sender:ReqGetChampionAvaliableRegions()
	if self.CurrentDisplayedGameRound == -1 then
		self.CurrentDisplayedGameRound = Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.roundIndex
	end
	if self.CurrentDisplayedScheduleType == -1 then
		self.CurrentDisplayedScheduleType =  Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.scheduleType
	end
	self:RefreshTabList()
	self:UpdateScheduleTypeBtn()
	--根据淘汰赛还是小组赛请求更多信息
	if self.CurrentDisplayedScheduleType == ChampionConsts.CHAMPION_SCHEDULE_TYPE.GROUP_BATTLE then
		Game.PVPSystem.sender:ReqChampionGroupBattlePersonalMatches(Game.me.championTroopID)
	else
		if Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.roundStatus > 0 then
			Game.PVPSystem.sender:ReqChampionGetEliminationBracket(self.CurrentRegionID)
		else
			self:RefreshPlayerInfo()
			local CurrentPageIndex = self.WBP_ComTabListMainCom:GetSelectedItemIndex()
			self:ShowSubPage(CurrentPageIndex)
		end
	end
end

function PVP_ChampionMatch_Panel:OnGetEliminationMatches()
	self:RefreshPlayerInfo()
	local CurrentPageIndex = self.WBP_ComTabListMainCom:GetSelectedItemIndex()
	self:ShowSubPage(CurrentPageIndex)
end

function PVP_ChampionMatch_Panel:OnGetPersonalInfo()
	self:RefreshPlayerInfo()
	--刷新内部的子界面，此时子界面已经可以从外部获取玩家所在的赛区和轮次，子界面会因为刷新的需要尝试获取玩家的个人比赛信息，随后触发OnGetPersonalInfo
	local CurrentPageIndex = self.WBP_ComTabListMainCom:GetSelectedItemIndex()
	self:ShowSubPage(CurrentPageIndex)
end

--- 此处为自动生成
function PVP_ChampionMatch_Panel:on_WBP_ComBackTitleCom_TipClickEvent()
	Game.TipsSystem:ShowTips(Enum.ETipsData.CHAMPION_MATCH_DESC, self.view.WBP_ComBackTitle:GetCachedGeometry())
end

---刷新
---@param index number
---@param data UITabData
function PVP_ChampionMatch_Panel:on_WBP_ComSelectDropType1Com_ItemSelected(index, data)
	--根据所选赛区刷新
	self.CurrentRegionID = index
	Game.PVPSystem.sender:ReqChampionGetProgress(index)
	self.pageComponents[self.CurrentPageName]:OnChangeRegion()
end

---页签选中
---@param index number
---@param data table
function PVP_ChampionMatch_Panel:on_WBP_ComTabListMainCom_ItemSelected(index, data)
	self:ShowSubPage(index)
end

--- 此处为自动生成
function PVP_ChampionMatch_Panel:on_Btn_Go_Clicked()
	self:ToggleScheduleType()
end

return PVP_ChampionMatch_Panel
