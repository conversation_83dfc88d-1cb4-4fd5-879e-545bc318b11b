local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---PVP结算 - 玩家Tag
---@class PVP_FinishedItemTag : UIListItem
---@field view PVP_FinishedItemTagBlueprint
local PVP_FinishedItemTag = DefineClass("PVP_FinishedItemTag", UIListItem)

PVP_FinishedItemTag.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_FinishedItemTag:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_FinishedItemTag:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVP_FinishedItemTag:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PVP_FinishedItemTag:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_FinishedItemTag:InitUIView()
end

---面板打开的时候触发
function PVP_FinishedItemTag:OnRefresh(TitleName, ColorType)
	self.view.KText_Tag_lua:SetText(TitleName)
	self.userWidget:Event_UI_Style(ColorType == "TitleColor_1" and 2 or (ColorType == "TitleColor_2" and 0 or 1))
end

return PVP_FinishedItemTag
