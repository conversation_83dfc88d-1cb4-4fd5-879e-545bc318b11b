local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PVP_12V12Ready_Panel : UIPanel
---@field view PVP_12V12Ready_PanelBlueprint
local PVP_12V12Ready_Panel = DefineClass("PVP_12V12Ready_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"

PVP_12V12Ready_Panel.eventBindMap = {
	[_G.EEventTypes.TEAM_ARENA_MATCH_STATE_CHANGE] = "OnMatchStateChange",
	[_G.EEventTypes.TEAM_ARENA_MATCH_CONFIRM_STATE_CHANGE] = "OnReadyStateChange",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_12V12Ready_Panel:OnCreate()
	---玩家是否准备好
	self.bConfirmed = false
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_12V12Ready_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVP_12V12Ready_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtn_Confirm_luaCom = self:CreateComponent(self.view.WBP_ComBtn_Confirm_lua, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_Cancel_luaCom = self:CreateComponent(self.view.WBP_ComBtn_Cancel_lua, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function PVP_12V12Ready_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_Cancel_luaCom.onClickEvent, "on_WBP_ComBtn_Cancel_luaCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_Confirm_luaCom.onClickEvent, "on_WBP_ComBtn_Confirm_luaCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_12V12Ready_Panel:InitUIView()
	self.view.WBP_ComBtn_Cancel_lua.Text_Name:SetText(StringConst.Get("EXIT"))
	self.view.WBP_ComBtn_Confirm_lua.Text_Name:SetText(StringConst.Get("ARENA3V3_CONFIRM_READY"))
end

---面板打开的时候触发
function PVP_12V12Ready_Panel:OnRefresh(matchInfo)
	self.bConfirmed = false
	self:UpdateBtnState()
	self:StartReadyCountdown()
end

---确认准备
function PVP_12V12Ready_Panel:OnConfirm()
	self.bConfirmed = true
	self:SendReadyRpc()
	self:UpdateBtnState()
end

---取消并退出
function PVP_12V12Ready_Panel:OnCancel()
	self.bConfirmed = false
	self:SendReadyRpc()
	self:CloseSelf()
end

---更新就位按钮的状态
function PVP_12V12Ready_Panel:UpdateBtnState()
	if self.bConfirmed then
		self.view.WBP_ComBtn_Confirm_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.WBP_ComBtn_Cancel_lua:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.WBP_ComBtn_Confirm_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.WBP_ComBtn_Cancel_lua:SetVisibility(ESlateVisibility.Visible)
	end
end

---倒计时----------------------------------------------------------------------------------------------------------------------------------------------

---开启准备倒计时
function PVP_12V12Ready_Panel:StartReadyCountdown()
	local confirmEndTime = Game.PVPSystem.PvpMatchConfirmEndTime or 0
	local LeftTime = 0
	if confirmEndTime and confirmEndTime > 0 then
		LeftTime = confirmEndTime - _G._now(1)
	end
	if LeftTime > 0 then
		self:StartTimer('ReadyTimer',
			function()
				self:OnTimer(confirmEndTime)
			end,
			1000, -1, nil, true
		)
	end
end

function PVP_12V12Ready_Panel:OnTimer(confirmEndTime)
	local LeftTime = confirmEndTime - _G._now(1)
	if LeftTime > 0 then
		Log.Debug(LeftTime)
		self.view.WBP_ComBtn_Confirm_lua.Text_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.WBP_ComBtn_Confirm_lua.Text_Time:SetText(string.format("(%s)", LeftTime))
	else
		self.view.WBP_ComBtn_Confirm_lua.Text_Time:SetVisibility(ESlateVisibility.Collapsed)
		self:StopTimer("ReadyTimer")
		return
	end
end

---请求------------------------------------------------------------------------------------------------------------------------------------------------
---就位RPC
function PVP_12V12Ready_Panel:SendReadyRpc()
	Game.PVPSystem.sender:ReqConfirmEnter(self.bConfirmed)
end

---回调------------------------------------------------------------------------------------------------------------------------------------------------
--准备状态变更
function PVP_12V12Ready_Panel:OnReadyStateChange(campId, memberId, prepared)

end

function PVP_12V12Ready_Panel:OnMatchStateChange(MatchType, State)
	if State ~= Game.PVPSystem.EPVPMatchState.WAIT_CONFIRM then
		self:CloseSelf()
	end
end

--- 此处为自动生成
function PVP_12V12Ready_Panel:on_Btn_ClickArea_lua_Clicked()
	--隐藏面板
	self:CloseSelf()
end

--- 此处为自动生成
function PVP_12V12Ready_Panel:on_WBP_ComBtn_Cancel_luaCom_ClickEvent()
	self:OnCancel()
end

--- 此处为自动生成
function PVP_12V12Ready_Panel:on_WBP_ComBtn_Confirm_luaCom_ClickEvent()
	self:OnConfirm()
	--播放动画
	self.userWidget:PlayAnimationForward(self.userWidget.Ani_Click)
end

return PVP_12V12Ready_Panel
