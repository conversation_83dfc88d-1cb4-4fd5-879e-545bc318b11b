local PVPSystemSender = DefineClass("PVPSystemSender", SystemSenderBase)

---查询赛季信息3v3
function PVPSystemSender:ReqQuery3v3SeasonStats(seasonId)
    self.Bridge:ReqQuery3v3SeasonStats(seasonId)
end

---查询赛季信息5v5
function PVPSystemSender:ReqQuery5v5SeasonStats(seasonId)
    self.Bridge:ReqQuery5v5SeasonStats(seasonId)
end

---查询赛季信息12v12
function PVPSystemSender:ReqQuery12v12SeasonStats(seasonId)
	self.Bridge:ReqQuery12v12SeasonStats(seasonId)
end

---查询33所有对局记录信息
function PVPSystemSender:ReqQueryAll3v3BattleRecords()
    self.Bridge:ReqQueryAll3v3BattleRecords()
end

---查询55所有对局记录信息
function PVPSystemSender:ReqQueryAll5v5BattleRecords()
    self.Bridge:ReqQueryAll5v5BattleRecords()
end

---查询1212所有对局记录信息
function PVPSystemSender:ReqQueryAll12v12BattleRecords()
	self.Bridge:ReqQueryAll12v12BattleRecords()
end

---查询33单局对局记录信息
function PVPSystemSender:ReqQuery3v3DetailBattleRecord(gameId)
    self.Bridge:ReqQuery3v3DetailBattleRecord(gameId)
end

---查询55单局对局记录信息
function PVPSystemSender:ReqQuery5v5DetailBattleRecord(gameId)
    self.Bridge:ReqQuery5v5DetailBattleRecord(gameId)
end

---查询1212单局对局记录信息
function PVPSystemSender:ReqQuery12v12DetailBattleRecord(gameId)
	self.Bridge:ReqQuery12v12DetailBattleRecord(gameId)
end

---确认准备
function PVPSystemSender:ReqConfirmEnter(bReady)
    self.Bridge:ReqConfirmEnter(bReady)
end

---取消匹配
function PVPSystemSender:ReqCancelPVPMatch(matchType)
    self.Bridge:ReqCancelPVPMatch(matchType)
end

function PVPSystemSender:ReqCancelStartNextPVPMatch(matchType)
	self.Bridge:ReqCancelStartNextPVPMatch(matchType)
end

-- 开启匹配
function PVPSystemSender:ReqStartPVPMatch(matchType)
    self.Bridge:ReqStartPVPMatch(matchType)
end

--领取33的段位奖励
function PVPSystemSender:ReqGetTeam3v3RankReward(rankId)
    self.Bridge:ReqGetTeam3v3RankReward(rankId)
end

---领取55的段位奖励
function PVPSystemSender:ReqGetTeam5v5RankReward(rankId)
    self.Bridge:ReqGetTeam5v5RankReward(rankId)
end

---领取1212的段位奖励
function PVPSystemSender:ReqGetTeam12v12RankReward(rankId)
	self.Bridge:ReqGetTeam12v12RankReward(rankId)
end

---领取1212全部奖励
function PVPSystemSender:ReqGetTeam12v12AllRankReward()
	self.Bridge:ReqGetTeam12v12AllRankReward()
end

---领取55全部奖励
function PVPSystemSender:ReqGetTeam5v5AllRankReward()
    self.Bridge:ReqGetTeam5v5AllRankReward()
end

---领取33全部奖励
function PVPSystemSender:ReqGetTeam3v3AllRankReward()
    self.Bridge:ReqGetTeam3v3AllRankReward()
end

---pvp通用点赞他人对局记录
function PVPSystemSender:ReqLikeTeamArenaCommonBattleResult(eid)
	self.Bridge:ReqLikeTeamArenaCommonBattleResult(eid)
end

---pvp通用批量点赞他人对局记录
function PVPSystemSender:ReqBatchLikeTeamArenaCommonBattleResult(eidList)
	self.Bridge:ReqBatchLikeTeamArenaCommonBattleResult(eidList)
end

------------------------------------------------------------------------------------------------------------------------------------------------------
--region 比武大会

---请求创建战队
---@param bAutoCreate boolean 是否客户端自动创建
---@param Name string
function PVPSystemSender:ReqCreateChampionTroop(bAutoCreate, Name)
	self.Bridge:ReqCreateChampionTroop(bAutoCreate, Name)
end

---请求加入战队
---@param ChampionTroopID number 战队ID
function PVPSystemSender:ReqJoinChampionTroop(ChampionTroopID)
	self.Bridge:ReqJoinChampionTroop(ChampionTroopID)
end

---请求退队
function PVPSystemSender:ReqQuitChampionTroop()
	self.Bridge:ReqQuitChampionTroop()
end

---请求解散战队
function PVPSystemSender:ReqDisbandChampionTroop()
	self.Bridge:ReqDisbandChampionTroop()
end

---请求报名比赛
function PVPSystemSender:ReqSignUpChampion()
	self.Bridge:ReqSignUpChampion()
end

---请求准备场信息
function PVPSystemSender:ReqChampionPrepareArenaInfo()
	self.Bridge:ReqChampionPrepareArenaInfo()
end

---请求获得战队详细以及简略信息
function PVPSystemSender:ReqGetChampionTroopDetail()
	self.Bridge:ReqGetChampionTroopDetail()
end

function PVPSystemSender:ReqGetChampionTroopBrief()
	self.Bridge:ReqGetChampionTroopBrief()
end

---改名
function PVPSystemSender:ReqChampionModifyTroopName(Name)
	self.Bridge:ReqChampionModifyTroopName(Name)
end

---邀请入队
---@param EID string
function PVPSystemSender:ReqChampionInviteJoinTroop(EID)
	self.Bridge:ReqChampionInviteJoinTroop(EID)
end

---踢人
function PVPSystemSender:ReqKickChampionTroopMember(ID)
	self.Bridge:ReqKickChampionTroopMember(ID)
end

---移交队长
function PVPSystemSender:ReqChampionChangeTroopLeader(ID)
	self.Bridge:ReqChampionChangeTroopLeader(ID)
end

---获取小组赛对局信息
function PVPSystemSender:ReqChampionGroupBattleMatches(ZoneID, RoundIndex)
	self.Bridge:ReqChampionGroupBattleMatches(ZoneID, RoundIndex)
end

---获取小组赛个人战队信息（无战队不会返回）
function PVPSystemSender:ReqChampionGroupBattlePersonalMatches(EID)
	self.Bridge:ReqChampionGroupBattlePersonalMatches(EID)
end

---小组赛排行榜
function PVPSystemSender:ReqChampionGroupBattleRanklist(ZoneID)
	self.Bridge:ReqChampionGroupBattleRanklist(ZoneID)
end

---淘汰赛赛程
function PVPSystemSender:ReqChampionGetEliminationBracket(ZoneID)
	self.Bridge:ReqChampionGetEliminationBracket(ZoneID)
end

---获取淘汰赛助力
function PVPSystemSender:ReqGetEliminationSupportInfo(RegionID)
	self.Bridge:ReqGetEliminationSupportInfo(RegionID)
end

---比赛当前阶段
function PVPSystemSender:ReqChampionGetProgress(ZoneID)
	self.Bridge:ReqChampionGetProgress(ZoneID)
end

---请求队伍队员信息
function PVPSystemSender:ReqChampionGetOtherTroopMemberInfo(IDList)
	self.Bridge:ReqChampionGetOtherTroopMemberInfo(IDList)
end

---获取赛区数量
function PVPSystemSender:ReqGetChampionAvaliableRegions()
	self.Bridge:ReqGetChampionAvaliableRegions()
end

--endregion 比武大会
------------------------------------------------------------------------------------------------------------------------------------------------------

return PVPSystemSender