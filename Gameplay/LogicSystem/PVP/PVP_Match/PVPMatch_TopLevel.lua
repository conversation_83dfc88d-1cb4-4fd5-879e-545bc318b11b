local ComBarNew = kg_require("Gameplay.LogicSystem.Common.Bar.ComBarNew")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PVPMatch_TopLevel : UIComponent
---@field view PVPMatch_TopLevelBlueprint
local PVPMatch_TopLevel = DefineClass("PVPMatch_TopLevel", UIComponent)

PVPMatch_TopLevel.eventBindMap = {
	[EEventTypesV2.RECEIVE_MONEY_CHANGE] = "SetContent"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVPMatch_TopLevel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVPMatch_TopLevel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVPMatch_TopLevel:InitUIComponent()
    ---@type ComBarNew
    self.WBP_ComBarNew_luaCom = self:CreateComponent(self.view.WBP_ComBarNew_lua, ComBarNew)
end

---UI事件在这里注册，此处为自动生成
function PVPMatch_TopLevel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVPMatch_TopLevel:InitUIView()
end

---面板打开的时候触发
function PVPMatch_TopLevel:Refresh(...)
end

---刷新数据
function PVPMatch_TopLevel:SetContent()
	local Num = Game.me.moneyWeekLimitInfoDict[2001010].weekPossessed or Game.BagSystem:GetItemCount(2001010)
	local Limit = Game.TableData.GetMoneyWeekLimitDataRow(2001010).weekMaxNum
	self.view.KGRichTextBlock_lua:SetText(string.format("<12V12TopLevelLight>%s</>/%s", Num, Limit))
	self.WBP_ComBarNew_luaCom:SetProgressBarValue(Num/Limit)
end


--- 此处为自动生成
function PVPMatch_TopLevel:on_Btn_ClickArea_lua_Clicked()
	Game.UIJumpSystem:JumpToUI(Enum.EPVPEntranceConstData.PVP_ENTRANCE_SHOP_UIJUMP)
end

return PVPMatch_TopLevel
