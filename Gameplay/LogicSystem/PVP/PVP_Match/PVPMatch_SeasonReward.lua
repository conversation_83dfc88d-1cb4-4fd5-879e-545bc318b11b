local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PVPMatch_SeasonReward : UIComponent
---@field view PVPMatch_SeasonRewardBlueprint
local PVPMatch_SeasonReward = DefineClass("PVPMatch_SeasonReward", UIComponent)

PVPMatch_SeasonReward.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVPMatch_SeasonReward:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVPMatch_SeasonReward:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVPMatch_SeasonReward:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PVPMatch_SeasonReward:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Info_lua.OnClicked, "on_Btn_Info_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVPMatch_SeasonReward:InitUIView()
end

---面板打开的时候触发
function PVPMatch_SeasonReward:Refresh(...)
end

--- 此处为自动生成
function PVPMatch_SeasonReward:on_Btn_Info_lua_Clicked()
	local TipsID = Game.TableData.Get3V3ConstDataRow("PVP_ARENA_REWARD_DESC_TIPS").Value
	Game.TipsSystem:ShowTips(TipsID, nil, {
		Pos = FVector2D(1820.0, 155),
		Size = import(
			"SlateBlueprintLibrary").GetLocalSize(self.view.Btn_Info_lua:GetCachedGeometry())
	})
end

return PVPMatch_SeasonReward
