local BaseListItemComponent = kg_require("Framework.UI.List.NewList.BaseListItemComponent")
local PVPMatchTabListItem = DefineClass("PVPMatchTabListItem", BaseListItemComponent, ITreeListComponent)
local ESlateVisibility = import("ESlateVisibility")

function PVPMatchTabListItem:OnListRefresh(parentUI, bSelect, allData, index1)
	self.view.Text_Sub_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.userWidget:SetArrow(false)
	if allData[index1].Children then
		self.userWidget:SetSelected(not bSelect)
	else
		self.userWidget:SetSelected(bSelect)
	end
	self.view.Text_Name_lua:SetText(allData[index1].Name)
end

function PVPMatchTabListItem:CanSel(parentUI, allData, index)
	--主界面竞技界面只开放第一个
	if parentUI and not parentUI.bSpecificPVPPanel then
		if index ~= 1 then
			return false
		end
	end
	return true
end

function PVPMatchTabListItem:OnClick(parentUI, allData, index1, index2)
	if parentUI and parentUI.bSpecificPVPPanel then
		parentUI:ShowSubPanel(index1 + 1)
	end
end

return PVPMatchTabListItem