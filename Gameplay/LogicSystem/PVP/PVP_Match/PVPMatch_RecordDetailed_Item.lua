local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PVPMatch_RecordDetailed_Item : UIListItem
---@field view PVPMatch_RecordDetailed_ItemBlueprint
local PVPMatch_RecordDetailed_Item = DefineClass("PVPMatch_RecordDetailed_Item", UIListItem)

PVPMatch_RecordDetailed_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVPMatch_RecordDetailed_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVPMatch_RecordDetailed_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVPMatch_RecordDetailed_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PVPMatch_RecordDetailed_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVPMatch_RecordDetailed_Item:InitUIView()
end

function PVPMatch_RecordDetailed_Item:OnRefresh(Data)
	local ExcelData = Data
	self.view.Text_Name_lua:SetText(ExcelData.DataName)
	local DisplayNum = Game.PVPSystem.model.CurrentResult[Game.PVPSystem.PVPGameMode]
		and Game.PVPSystem.model.CurrentResult[Game.PVPSystem.PVPGameMode][ExcelData.DataEnum] or 0
	if DisplayNum == math.floor(DisplayNum) then
		self.view.Text_Num_lua:SetText(string.format("%d", DisplayNum))
	else
		self.view.Text_Num_lua:SetText(string.format("%.2f", DisplayNum))
	end

	-- Set the formatted number to the text
	
	self:SetImage(self.view.Img_Icon_lua, ExcelData.IconResource)
end

return PVPMatch_RecordDetailed_Item
