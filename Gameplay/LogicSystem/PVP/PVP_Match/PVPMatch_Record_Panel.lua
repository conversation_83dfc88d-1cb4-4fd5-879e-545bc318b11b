local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PVPMatch_Record_Panel : UIComponent
---@field view PVPMatch_Record_PanelBlueprint
local PVPMatch_Record_Panel = DefineClass("PVPMatch_Record_Panel", UIComponent)
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")

PVPMatch_Record_Panel.eventBindMap = {
	[_G.EEventTypes.TEAM_ARENA_ALL_GAME_STATS] = "OnGetAchieveInfo",
	[EEventTypesV2.PVP_JUMP_TO_NEXT_HISTORY] = "OnSwitchGameLog",
	[_G.EEventTypes.TEAM_ARENA_SINGLE_GAME_STATS] = "OnRecvTeamArenaBattleStat",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVPMatch_Record_Panel:OnCreate()
	--对局记录
	self.RecordDataListData = {}
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVPMatch_Record_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVPMatch_Record_Panel:InitUIComponent()
	---@type UIListView
    self.KLV_DataBoard_luaCom = self:CreateComponent(self.view.KLV_DataBoard_lua, UIListView)
end

---UI事件在这里注册，此处为自动生成
function PVPMatch_Record_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVPMatch_Record_Panel:InitUIView()
end

---面板打开的时候触发
function PVPMatch_Record_Panel:Refresh(...)
end

---刷新列表界面
function PVPMatch_Record_Panel:RefreshSubPage()
	self.userWidget:SetEmpty(true)
	---考虑到限频，这个时候先显示上次的数据
	self.RecordDataListData = Game.PVPSystem.model.PVPHistoryRecordCache[Game.PVPSystem.PVPMatchType] or {}
	if next(self.RecordDataListData) then
		self.userWidget:SetEmpty(false)
	end
	self.KLV_DataBoard_luaCom:Refresh(self.RecordDataListData)
	
	---获取全部对局记录
	if Game.PVPSystem.PVPGameMode == Enum.EPVPGameModeData.TEAM3V3 then
		Game.PVPSystem.sender:ReqQueryAll3v3BattleRecords()
	elseif Game.PVPSystem.PVPGameMode == Enum.EPVPGameModeData.TEAM12V12 then
		Game.PVPSystem.sender:ReqQueryAll12v12BattleRecords()
	else
		Game.PVPSystem.sender:ReqQueryAll5v5BattleRecords()
	end
end

---回调事件---------------------------------------------------------------------------------------------------------------------------------------------

---上一条或者下一条
function PVPMatch_Record_Panel:OnSwitchGameLog(bNext, Index)
	local NewIndex = bNext and Index + 1 or Index - 1
	self:OnSelect(NewIndex)
end

function PVPMatch_Record_Panel:OnGetAchieveInfo(result)
	Game.PVPSystem.model.PVPHistoryRecordCache[Game.PVPSystem.PVPMatchType] = result
	if (#result) > 0 then
		table.clear(self.RecordDataListData)
		self.RecordDataListData = result
		self.KLV_DataBoard_luaCom:Refresh(self.RecordDataListData)
		self.userWidget:SetEmpty(false)
	else
		self.userWidget:SetEmpty(true)
	end
end

---用户选择上一条或者下一条历史记录后，因为查询限频的存在，必须接到回包再刷新
---@param index number
function PVPMatch_Record_Panel:OnSelect(index)
	local SingleGameInfo = self.RecordDataListData[index]
	if Game.PVPSystem.PVPMatchType == Enum.EMatchTypeData.MATCH_TYPE_3V3 then
		Game.PVPSystem.sender:ReqQuery3v3DetailBattleRecord(SingleGameInfo.GameId)
	else
		Game.PVPSystem.sender:ReqQuery12v12DetailBattleRecord(SingleGameInfo.GameId)
	end
end

function PVPMatch_Record_Panel:OnRecvTeamArenaBattleStat(fullData)
	--回包之后获取Index
	local index = 1
	
	for Index, Info in pairs(self.RecordDataListData) do
		if Info.GameId == fullData.GameId then
			index = Index
			break
		end
	end
	
	local SingleGameInfo = self.RecordDataListData[index]
	SingleGameInfo.Pos = index
	SingleGameInfo.Total = #self.RecordDataListData
	if Game.PVPSystem.PVPGameMode == Enum.EPVPGameModeData.TEAM3V3 then
		Game.PVPSystem:ShowTeamPVPStatsPanel(PVPSystem.EBattltStatOpenMode.HISTORY_RECORD, fullData, SingleGameInfo)
	else
		Game.PVPSystem:ShowGroupPVPStatsPanel(PVPSystem.EBattltStatOpenMode.HISTORY_RECORD, fullData, SingleGameInfo)
	end
end

return PVPMatch_Record_Panel
