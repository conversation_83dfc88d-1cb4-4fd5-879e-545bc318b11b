local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---PVP结算 - 胜利/失败标识
---@class PVP_FinishedTopState : UIComponent
---@field view PVP_FinishedTopStateBlueprint
local PVP_FinishedTopState = DefineClass("PVP_FinishedTopState", UIComponent)

PVP_FinishedTopState.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVP_FinishedTopState:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVP_FinishedTopState:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PVP_FinishedTopState:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PVP_FinishedTopState:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVP_FinishedTopState:InitUIView()
end

---组件刷新统一入口
---@param bWin boolean 是否胜利
---@param bScale boolean 是否为缩小的版本
---@param bNeedSunShine boolean 是否需要sunShine
function PVP_FinishedTopState:Refresh(bWin, bScale, bNeedSunShine)
	bWin = bWin or false
	bScale = bScale or false
	self.userWidget:Event_UI_Style(bWin, bScale)
	if self.userWidget.BP_WithSunShine then
		bNeedSunShine = bNeedSunShine or false
		self.userWidget:BP_WithSunShine(bNeedSunShine)
	end
end

return PVP_FinishedTopState
