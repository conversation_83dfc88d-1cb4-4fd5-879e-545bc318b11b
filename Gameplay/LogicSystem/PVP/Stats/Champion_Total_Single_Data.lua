local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---比武大会数据结算的综合数据统计
---@class Champion_Total_Single_Data : UIComponent
---@field view Champion_Total_Single_DataBlueprint
local Champion_Total_Single_Data = DefineClass("Champion_Total_Single_Data", UIComponent)

local StringConst = require "Data.Config.StringConst.StringConst"

Champion_Total_Single_Data.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Champion_Total_Single_Data:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Champion_Total_Single_Data:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Champion_Total_Single_Data:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Champion_Total_Single_Data:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Champion_Total_Single_Data:InitUIView()
end

---组件刷新统一入口
---@param Data table 传入数据
---@param Position number 自身位置（1： 蓝，2： 红）
function Champion_Total_Single_Data:Refresh(Data, Position)
	self.view.WBP_Champion_Total_Data_1.Text_Data:SetText(string.format(StringConst.Get("PVP_CHAMPION_STATS_DEFEAT"), Data[1][Position]))
	self.view.WBP_Champion_Total_Data_1:SetLight(Data[1][3] == Position or false)

	self.view.WBP_Champion_Total_Data_2.Text_Data:SetText(string.format(StringConst.Get("PVP_CHAMPION_STATS_SURVIVE"), Data[2][Position]))
	self.view.WBP_Champion_Total_Data_2:SetLight(Data[2][3] == Position or false)
	
	local TotalDamage = Data[3][Position]
	if Data[3][Position] > 10000 then
		TotalDamage = string.format("%.1f%s", TotalDamage / 10000, StringConst.Get("TEN_THOUSAND"))
	end
	self.view.WBP_Champion_Total_Data_3.Text_Data:SetText(string.format(StringConst.Get("PVP_CHAMPION_STATS_DAMAGE"), TotalDamage))
	self.view.WBP_Champion_Total_Data_3:SetLight(Data[3][3] == Position or false)
end

return Champion_Total_Single_Data
