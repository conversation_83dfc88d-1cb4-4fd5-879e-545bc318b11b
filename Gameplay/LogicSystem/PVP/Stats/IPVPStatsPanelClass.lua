---@class IPVPPanelClass
---PVP数据结算面板通用方法，继承后需要实现特定方法
local IPVPStatsPanelClass = DefineClass("IPVPPanelClass")

local ESlateVisibility = import("ESlateVisibility")

local StringConst = require "Data.Config.StringConst.StringConst"

------------------------------------------------------------------------------------------------------------------------------------------------------

--region Requried Implemented 需要各自实现的方法

---面板打开时触发
---@param OpenMode number 打开模式 PVPSystem.EBattltStatOpenMode
---@param BattleStat table 对战统计信息
---@param extraInfo table 对战附加信息
function IPVPStatsPanelClass:IProcessOpenPanel(OpenMode, BattleStat, extraInfo)
	--content
end

---将面板样式设置为对战中打开的样式
function IPVPStatsPanelClass:ISetWidgetAsInBattleStat()
	
end

---将面板样式设置为结算后打开的样式
function IPVPStatsPanelClass:ISetWidgetAsFinishBattleStat()

end

---将面板样式设置为历史记录中打开的样式
function IPVPStatsPanelClass:ISetWidgetAsHistoryBattleStat()

end

--endregion Required Implemented

------------------------------------------------------------------------------------------------------------------------------------------------------

--region General Inherit 这里的方法需要继承，一般来说不需要重写

---设置界面打开后的Style
---@param OpenMode number 打开模式 PVPSystem.EBattltStatOpenMode
function IPVPStatsPanelClass:SetPanelWidgetStyle(OpenMode)
	if OpenMode == PVPSystem.EBattltStatOpenMode.HISTORY_RECORD then
		self:ISetWidgetAsHistoryBattleStat()
	elseif OpenMode == PVPSystem.EBattltStatOpenMode.IN_BATTLE then
		self:ISetWidgetAsInBattleStat()
	else
		self:ISetWidgetAsFinishBattleStat()
	end
end

---@class OutOnMsgTeamArenaRoundCalc
---@field Result number
---@field Mvp string
---@field StartTime number
---@field EndTime number
---@field Members number

---处理结算后的数据 - 获取双方队伍ID
---@param BattleStat OutOnMsgTeamArenaRoundCalc OnMsgTeamArenaRoundCalc 回传的数据
---@return number|number 两个队伍的ID，玩家队伍在前
function IPVPStatsPanelClass:GetCampIDs(BattleStat)
	--首先获取玩家所在队伍的ID
	local PlayerCampID = nil
	local OtherCampID = nil
	for CampID, CampInfo in pairs(BattleStat) do
		for _, PlayerInfo in pairs(CampInfo.Members) do
			if PlayerInfo.ID == Game.me.eid then
				PlayerCampID = CampID
				break
			end
		end
	end
	for CampID, CampInfo in pairs(BattleStat) do
		if CampID ~= PlayerCampID then
			OtherCampID = CampID
		end
	end
	
	return PlayerCampID, OtherCampID
end

---更新历史记录查看模式下的上下局
---@param extraInfo table
---@param widgetLeft userdata 左侧箭头控件
---@param widgetRight userdata 右侧箭头控件
function IPVPStatsPanelClass:UpdatePreviousAndNextGameArrow(extraInfo, widgetLeft, widgetRight)
	widgetLeft:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	widgetRight:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	if extraInfo.Pos == 1 then
		widgetRight:SetVisibility(ESlateVisibility.Hidden)
	end
	if extraInfo.Pos >= extraInfo.Total then
		widgetLeft:SetVisibility(ESlateVisibility.Hidden)
	end
end

---更新历史对局的状态
---@param extraInfo table
---@param BattleTimeWidget userdata 战斗时间文本控件
---@param ScoreWidget userdata 积分变化文本控件
function IPVPStatsPanelClass:SetHistoryBattleDetail(extraInfo, BattleTimeWidget, ScoreWidget)
	--对局时间
	BattleTimeWidget:SetText(
		os.date("%Y-%m-%d %H:%M:%S", extraInfo.Time)
	)
	--对局分数变化
	local RankData = Game.PVPSystem:GetRankDataRow(extraInfo.BriefInfo.PreRankID)
	local RankPoint = Game.PVPSystem:ConvertCurrentRankPointToRelative(extraInfo.BriefInfo.PreRankID, extraInfo.BriefInfo.PreRankPoints)
	local FormattedPoints = extraInfo.BriefInfo.GainPoints > 0 and
		(string.format("<GrassGreen>+%s</>", extraInfo.BriefInfo.GainPoints)) or
		(string.format("<BrickRedLight>%s</>", extraInfo.BriefInfo.GainPoints))
	self.userWidget:Event_UI_Style(false, extraInfo.BriefInfo.GainPoints > 0)
	ScoreWidget:SetText(string.format(
		StringConst.Get("PVP_STATS_RANK_POINT_CHANGE"),
		RankData.Name,
		RankData.SubName,
		RankPoint,
		FormattedPoints)
	)
end

--endregion General Inherit

return IPVPStatsPanelClass