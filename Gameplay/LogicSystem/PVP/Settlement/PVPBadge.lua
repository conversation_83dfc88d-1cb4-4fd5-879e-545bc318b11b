local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---PVP结算界面 - 段位信息
---@class PVPBadge : UIComponent
---@field view PVPBadgeBlueprint
local PVPBadge = DefineClass("PVPBadge", UIComponent)

local StringConst = kg_require("Data.Config.StringConst.StringConst")

local ESlateVisibility = import("ESlateVisibility")

PVPBadge.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVPBadge:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVPBadge:InitUIData()
	---段位数据
	---@type table
	self.RankPointData = {}
end

--- UI组件初始化，此处为自动生成
function PVPBadge:InitUIComponent()
    ---@type UIComDiyTitle
    self.WBP_ComFirstBigTextCom = self:CreateComponent(self.view.WBP_ComFirstBigText, UIComDiyTitle)
end

---UI事件在这里注册，此处为自动生成
function PVPBadge:InitUIEvent()
    self:AddUIEvent(self.view.Btn_HotArea.OnClicked, "on_Btn_HotArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVPBadge:InitUIView()
end

---组件刷新统一入口
function PVPBadge:Refresh(Data, bWin)
	self.view.KGTextBlock_VictoryTips:SetVisibility(ESlateVisibility.Collapsed)
	--拼接当前段位的文本和分数变化
	--显示段位信息
	local RankText = ""
	local RankData = Game.PVPSystem:GetRankDataRow(Data.preRankId)
	---显示胜点值
	local RankPoint = Game.PVPSystem:ConvertCurrentRankPointToRelative(Data.preRankId, Data.preRankPoints)
	self.WBP_ComFirstBigTextCom:Refresh(string.format("%s%s", RankData.Name, RankData.SubName))
	RankText = string.format(StringConst.Get("PVP_FINISH_TOP_RANK_POINT"), RankPoint)
	local FormattedPoints = Data.gainPoints > 0 and
		(string.format(" (+%s)", Data.gainPoints)) or
		(string.format(" (%s)", Data.gainPoints))
	
	self.view.KGTextBlock_Point:SetText(RankText)
	self.view.KGTextBlock_PointChange:SetText(FormattedPoints)
	
	self.userWidget:BP_SetState(not bWin, Data.gainPoints <= 0 and true)
	
	---设置段位图标
	self:SetImage(self.view.Img_Badge, RankData.SmallRankIconSource)

	local FinalProtected = math.max(Data.preProtected + Data.gainProtected - (Data.gainPoints < 0 and -Data.gainPoints or 0), 0)
	self.RankPointData = {
		Data.preProtected,
		Data.gainProtected,
		Data.gainPoints < 0 and -Data.gainPoints or 0,
		math.min(FinalProtected, 100)
	}
end

---点击tip
function PVPBadge:OnClickTips()
	Game.TipsSystem:ShowTips(Enum.ETipsData["PVP_PROTECT_SCORE_DESC"], self.view.Btn_HotArea:GetCachedGeometry(), 
		{
			ExtraContent = { {Content = { self.RankPointData }} 
			}
		}
	)
end

--- 此处为自动生成
function PVPBadge:on_Btn_HotArea_Clicked()
	self:OnClickTips()
end

return PVPBadge
