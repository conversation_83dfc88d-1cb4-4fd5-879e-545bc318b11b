local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---PVP结算 点赞按钮
---@class PraiseBtn : UIComponent
---@field view PraiseBtnBlueprint
local PraiseBtn = DefineClass("PraiseBtn", UIComponent)

local StringConst = kg_require("Data.Config.StringConst.StringConst")

local ESlateVisibility = import("ESlateVisibility")

PraiseBtn.eventBindMap = {
	[_G.EEventTypes.TEAM_ARENA_ON_LIKED] = "OnLiked",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PraiseBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PraiseBtn:InitUIData()
	---该按钮对应的玩家EID
	---@type string
	self.EID = nil
	---该按钮对应的玩家EID列表
	---@type table
	self.EIDList = {}
	---是否为一键点赞按钮
	---@type boolean
	self.bAllLikeButton = false
	---是否已经点赞
	---@type boolean
	self.bLiked = false
end

--- UI组件初始化，此处为自动生成
function PraiseBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PraiseBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_HotArea.OnClicked, "on_Btn_HotArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PraiseBtn:InitUIView()
end

function PraiseBtn:Refresh(eid, bAllLike)
	self.view.Img_Shadow:SetVisibility(ESlateVisibility.Hidden)
	self.view.Text_Num:SetVisibility(ESlateVisibility.Hidden)
	self.bAllLikeButton = bAllLike
	self.bLiked = false
	if bAllLike then
		self.EIDList = eid
		self.view.KGTextBlock_LikeAll:SetText(StringConst.Get("PVP_SETTLEMENT_LIKE_ALL"))
		self.userWidget:BP_SetType(false, true)
	else
		self.EID = eid
		self.userWidget:BP_SetType(false, false)
	end
end

---更新点赞信息
function PraiseBtn:UpdateLikeInfo(instigatorEID, targetEID, num)
	if not self.bAllLikeButton and self.EID == targetEID then
		self.view.Img_Shadow:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Text_Num:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Text_Num:SetText(num)
		if instigatorEID == Game.me.eid then
			self.userWidget:BP_SetType(true, false)
			self.bLike = true
		else
			self.userWidget:BP_SetType(false, false)
		end
	end
end

---点赞事件回包
function PraiseBtn:OnLiked(instigatorEID, targetEID, num)
	self:UpdateLikeInfo(instigatorEID, targetEID, num)
end

---点击事件
function PraiseBtn:OnClickLike()
	if self.bLike then
		return
	end
	--点赞全部时使用不同的接口
	if self.bAllLikeButton then
		Game.PVPSystem.sender:ReqBatchLikeTeamArenaCommonBattleResult(self.EIDList)
		self.userWidget:BP_SetType(true, true)
	else
		Game.PVPSystem.sender:ReqLikeTeamArenaCommonBattleResult(self.EID)
	end
end

function PraiseBtn:on_Btn_HotArea_Clicked()
	self:OnClickLike()
end

return PraiseBtn
