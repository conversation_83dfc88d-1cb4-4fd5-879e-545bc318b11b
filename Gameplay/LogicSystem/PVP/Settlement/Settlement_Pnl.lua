local PVPDataBtn = kg_require("Gameplay.LogicSystem.PVP.Settlement.PVPDataBtn")
local PVPTitle = kg_require("Gameplay.LogicSystem.PVP.Settlement.PVPTitle")
local SettlementSign = kg_require("Gameplay.LogicSystem.PVP.Settlement.SettlementSign")
local PVPBadge = kg_require("Gameplay.LogicSystem.PVP.Settlement.PVPBadge")
local PVPMatchBtn = kg_require("Gameplay.LogicSystem.PVP.Settlement.PVPMatchBtn")
local PVPMatchBtn02 = kg_require("Gameplay.LogicSystem.PVP.Settlement.PVPMatchBtn02")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local Text_Item = kg_require("Gameplay.LogicSystem.PVP.Settlement.Text_Item")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local PraiseBtn = kg_require("Gameplay.LogicSystem.PVP.Settlement.PraiseBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class Settlement_Pnl : UIPanel
---@field view Settlement_PnlBlueprint
local Settlement_Pnl = DefineClass("Settlement_Pnl", UIPanel)

local StringConst = kg_require("Data.Config.StringConst.StringConst")

local ESlateVisibility = import("ESlateVisibility")

Settlement_Pnl.eventBindMap = {
	[EEventTypesV2.ARENA3V3_PVP_AUTO_MATCH_CANCEL] = "OnCancelAutoMatchChange"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Settlement_Pnl:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Settlement_Pnl:InitUIData()
	---是否取消了自动匹配
	---@type boolean
	self.bCancelAutoMatch = false
	---玩家列表
	---@type table
	self.PlayerMemberListData = { }
	---是否胜利
	---@type boolean
	self.bWin = true
	---收到的战斗结果
	---@type table
	self.BattleStat = {}
	---未处理的战斗结果
	---@type table
	self.RawBattleStat = {}
	
	---显示的玩家自身统计内容
	---@type table
	self.PlayerDataDisplayConfig = {
		1, 3 ,4 ,5
	}
end

--- UI组件初始化，此处为自动生成
function Settlement_Pnl:InitUIComponent()
    ---@type SettlementSign
    self.WBP_SettlementSignCom = self:CreateComponent(self.view.WBP_SettlementSign, SettlementSign)
    ---@type PVPDataBtn
    self.WBP_PVPDataBtnCom = self:CreateComponent(self.view.WBP_PVPDataBtn, PVPDataBtn)
    ---@type PVPTitle
    self.WBP_PVPTitle_MemberCom = self:CreateComponent(self.view.WBP_PVPTitle_Member, PVPTitle)
    ---@type PVPTitle
    self.WBP_PVPTitle_DataCom = self:CreateComponent(self.view.WBP_PVPTitle_Data, PVPTitle)
    ---@type PVPBadge
    self.WBP_PVPBadgeCom = self:CreateComponent(self.view.WBP_PVPBadge, PVPBadge)
    ---@type PVPMatchBtn
    self.WBP_PVPMatchBtnCom = self:CreateComponent(self.view.WBP_PVPMatchBtn, PVPMatchBtn)
    ---@type PVPMatchBtn02
    self.WBP_PVPMatchBtn02Com = self:CreateComponent(self.view.WBP_PVPMatchBtn02, PVPMatchBtn02)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type Text_Item
    self.WBP_Text_ItemCom = self:CreateComponent(self.view.WBP_Text_Item, Text_Item)
    ---@type Text_Item
    self.WBP_Text_Item_1Com = self:CreateComponent(self.view.WBP_Text_Item_1, Text_Item)
    ---@type Text_Item
    self.WBP_Text_Item_2Com = self:CreateComponent(self.view.WBP_Text_Item_2, Text_Item)
    ---@type Text_Item
    self.WBP_Text_Item_3Com = self:CreateComponent(self.view.WBP_Text_Item_3, Text_Item)
    ---@type UISimpleList
    self.KGWrapBox_PlayerCom = self:CreateComponent(self.view.KGWrapBox_Player, UISimpleList)
    ---@type PraiseBtn
    self.WBP_PraiseBtnCom = self:CreateComponent(self.view.WBP_PraiseBtn, PraiseBtn)
end

---UI事件在这里注册，此处为自动生成
function Settlement_Pnl:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.view.WBP_PVPDataBtn.Btn_HotArea.OnClicked, "on_WBP_PVPDataBtnBtn_HotArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Settlement_Pnl:InitUIView()
	---展示的玩家自己的战绩控件
	---@type table
	self.PlayerInfoItemList = {
		self.WBP_Text_ItemCom,
		self.WBP_Text_Item_1Com,
		self.WBP_Text_Item_2Com,
		self.WBP_Text_Item_3Com,
	}
end

---面板打开的时候触发
---@param bWin boolean 玩家是否胜利
---@param result table 比赛的结果与每一位玩家的信息
---@param extraInfo table 比赛的分数相关内容
---@param rawBattleStat table 未经合并处理的结算数据，用于在结算界面打开数据统计
function Settlement_Pnl:OnRefresh(bWin, result, extraInfo, autoMatchCountDown, rawBattleStat)
	self:PlayAnimation(bWin and self.userWidget.Ani_Fadein_Win or self.userWidget.Ani_Fadein_Fail)
	
	self.BattleStat = result
	self.RawBattleStat = rawBattleStat
	self.bWin = bWin
	
	self:SetContent(bWin)
	--胜负标志
	self.WBP_SettlementSignCom:Refresh(bWin, Enum.EPVPGameModeData.TEAM3V3, false, true)
	
	--设置段位
	self.WBP_PVPBadgeCom:Refresh(extraInfo, bWin)

	--更新玩家列表
	self:RefreshPlayerListAndMVP(result.Members, result.Mvp, result.LMVP)
	---更新按钮的状态
	self:UpdateNextButton(autoMatchCountDown)
end

---界面设置---------------------------------------------------------------------------------------------------------------------------------------------

---设置界面固定内容
function Settlement_Pnl:SetContent(bWin)
	--设置小标题
	self.WBP_PVPTitle_DataCom:Refresh(StringConst.Get("PVP_SETTLEMENT_MY_SCORE"), bWin)
	self.WBP_PVPTitle_MemberCom:Refresh(StringConst.Get("PVP_SETTLEMENT_HONOR_TITLE"), bWin)
end

---设置一键组队
function Settlement_Pnl:SetQuickTeam(PlayerList)
	--3V3模式如果本局有队友和自己不在一个原本的队伍，那么展示一键组队
	if Game.PVPSystem:GetGameModeType() == Enum.EMatchTypeData.MATCH_TYPE_3V3 then
		local TeamMembers = Game.me.teamInfoList or {}
		if not TeamMembers[PlayerList[1][3]] or not TeamMembers[PlayerList[2][3]] then
			self.view.WBP_PVPMatchBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self.WBP_PVPMatchBtnCom:Refresh(PlayerList)
			return
		end
	end
	self.view.WBP_PVPMatchBtn:SetVisibility(ESlateVisibility.Collapsed)
end

---刷新玩家列表以及MVP
---@param NewList table
---@param MVPID string
function Settlement_Pnl:RefreshPlayerListAndMVP(NewList, MVPID, LMVPID)
	table.sort(NewList,
		function(a, b)
			return a.Score > b.Score
		end
	)
	local AllLikeEIDList = {}
	local SelfTeamOtherPlayerList = {}
	local MaxDisplayNum = Game.PVPSystem:GetGameModeType() == Enum.EMatchTypeData.MATCH_TYPE_3V3 and 4 or 6
	local CurrentCount = 0
	table.clear(self.PlayerMemberListData)
	--通过玩家的队伍确定胜负分别是谁
	local PlayerCampID = Game.TeamAreanaSystem.model.memberMap[Game.me.eid].CampID
	for Pos, PlayerInfo in pairs(NewList) do
		local CurrentPlayerCampID = Game.TeamAreanaSystem.model.memberMap[PlayerInfo.ID].CampID
		--设置玩家自己的数据
		if PlayerInfo.ID == Game.me.eid then
			self:SetPlayerInfo(PlayerInfo)
		end
		if CurrentPlayerCampID == PlayerCampID and PlayerInfo.ID ~= Game.me.eid then
			table.insert(SelfTeamOtherPlayerList, {PlayerInfo.Name, PlayerInfo.ProfessionID, PlayerInfo.ID})
		end
		if CurrentCount < MaxDisplayNum and PlayerInfo.TitleID > 0 then
			PlayerInfo.bWin = (self.bWin and PlayerCampID == CurrentPlayerCampID) or (not self.bWin and PlayerCampID ~= CurrentPlayerCampID)
			if PlayerInfo.ID ~= Game.me.eid then
				table.insert(AllLikeEIDList, PlayerInfo.ID)
			end
			table.insert(self.PlayerMemberListData, PlayerInfo)
			CurrentCount = CurrentCount + 1
		end
	end

	--设置快捷组队
	self:SetQuickTeam(SelfTeamOtherPlayerList)
	---更新一键点赞，玩家荣誉
	self.WBP_PraiseBtnCom:Refresh(AllLikeEIDList, true)
	self.KGWrapBox_PlayerCom:Refresh(self.PlayerMemberListData)
end

---设置玩家自己的统计信息
function Settlement_Pnl:SetPlayerInfo(PlayerInfo)
	for Index, Component in ipairs(self.PlayerInfoItemList) do
		Component:Refresh(self.PlayerDataDisplayConfig[Index], PlayerInfo)
	end
end

---匹配------------------------------------------------------------------------------------------------------------------------------------------------

function Settlement_Pnl:UpdateNextButton(autoMatchCountDown)
	self.WBP_PVPMatchBtn02Com:Refresh(autoMatchCountDown)
end

---事件与回调-------------------------------------------------------------------------------------------------------------------------------------------

function Settlement_Pnl:OnCancelAutoMatchChange()
	self.WBP_PVPMatchBtn02Com:OnCancelAutoMatchChange()
end

function Settlement_Pnl:OnClickClose()
	self:CloseSelf()
end

---点击数据统计
function Settlement_Pnl:OnClickDataButton()
	if Game.PVPSystem:GetGameModeType() == Enum.EMatchTypeData.MATCH_TYPE_12V12 then
		Game.PVPSystem:ShowGroupPVPStatsPanel(PVPSystem.EBattltStatOpenMode.POST_BATTLE, self.RawBattleStat, {IsWin = self.bWin})
	else
		Game.PVPSystem:ShowTeamPVPStatsPanel(PVPSystem.EBattltStatOpenMode.POST_BATTLE, self.RawBattleStat, {IsWin = self.bWin})
	end
end

--- 此处为自动生成
function Settlement_Pnl:on_WBP_ComBtnCloseCom_ClickEvent()
	self:OnClickClose()
end

--- 此处为自动生成
function Settlement_Pnl:on_WBP_PVPDataBtnBtn_HotArea_Clicked()
	self:OnClickDataButton()
end

return Settlement_Pnl
