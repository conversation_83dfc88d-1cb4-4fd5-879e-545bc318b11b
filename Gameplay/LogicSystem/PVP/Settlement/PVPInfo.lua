local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---PVP结算 头像
---@class PVPInfo : UIComponent
---@field view PVPInfoBlueprint
local PVPInfo = DefineClass("PVPInfo", UIComponent)

local ESlateVisibility = import("ESlateVisibility")

PVPInfo.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PVPInfo:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PVPInfo:InitUIData()
	---该按钮对应的玩家EID
	---@type string
	self.EID = nil
end

--- UI组件初始化，此处为自动生成
function PVPInfo:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PVPInfo:InitUIEvent()
    self:AddUIEvent(self.view.Btn_HotArea.OnClicked, "on_Btn_HotArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PVPInfo:InitUIView()
end

---组件刷新统一入口
function PVPInfo:Refresh(EID, ProfessionID, bWin)
	self.EID = EID
	local ProfessionLogoPath = Game.TableData.GetPlayerSocialDisplayDataRow(ProfessionID).SoloHeadIcon
		or Game.TableData.GetPlayerSocialDisplayDataRow(ProfessionID)[0].SoloHeadIcon
	self:SetImage(self.view.Img_HeadIcon_lua, ProfessionLogoPath)
	self.userWidget:BP_SetClick(not bWin)

	--好友或者自己不显示加好友
	---是否是好友
	local bFriend = Game.FriendSystem:GetFriendInfoByEntityID(EID)
	if Game.me.eid == EID or bFriend then
		self.view.Btn_HotArea:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_Add:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Btn_HotArea:SetVisibility(ESlateVisibility.Visible)
		self.view.Img_Add:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end

function PVPInfo:on_Btn_HotArea_Clicked()
	Game.FriendSystem:SendAddFriend(self.EID, Enum.EFriendAddSourceData.PVPGAME, 0)
	self.view.Btn_HotArea:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Img_Add:SetVisibility(ESlateVisibility.Collapsed)
end

return PVPInfo
