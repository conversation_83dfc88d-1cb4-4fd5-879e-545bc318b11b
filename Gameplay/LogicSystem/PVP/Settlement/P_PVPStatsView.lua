---@class WBP_ComBtnCloseNewView : WBP_ComBtnCloseNew_C
---@field public WidgetRoot WBP_ComBtnCloseNew_C
---@field public Button C7Button
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public IconBrush SlateBrush
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public Construct fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnClose_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Get_Icon_lua_Brush_0 fun(self:self):SlateBrush


---@class WBP_PVPStatsView : WBP_PVPStats_C
---@field public WidgetRoot WBP_PVPStats_C
---@field public LeftRoot VerticalBox
---@field public RightRoot VerticalBox
---@field public WBP_ComBtnClose WBP_ComBtnCloseNewView

---@class P_PVPStatsView : WBP_PVPStatsView
---@field public controller P_PVPStats
local P_PVPStatsView = DefineClass("P_PVPStatsView", UIView)

function P_PVPStatsView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

    --controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComBtnClose.Button, "OnClick_WBP_ComBtnClose_Button")
end

return P_PVPStatsView
