---@class P_PVPStats : UIController
---@field public View WBP_PVPStatsView 局内实时数据统计预览界面
local P_PVPStats = DefineClass("P_PVPStats", UIController)
local P_PVPStatsItem = kg_require("Gameplay.LogicSystem.PVP.Settlement.P_PVPStatsItem")

P_PVPStats.eventBindMap = {
    [_G.EEventTypes.TEAM_ARENA_RECEIVE_BATTLE_STAT] = "OnRecvTeamArenaBattleStat",
}

function P_PVPStats:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_ComBtnClose.Button, self.OnClickCloseBtn)
    self.blueTeamList = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.LeftRoot, P_PVPStatsItem) -- luacheck:ignore
    self.redTeamList = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.RightRoot, P_PVPStatsItem) -- luacheck:ignore
end

function P_PVPStats:OnRefresh()
    self.blueCampData = Game.TeamAreanaSystem:GetBlueCampInfo() or {}
    self.redCampData = Game.TeamAreanaSystem:GetRedCampInfo() or {}
    self.battleStat =  {}
    Game.TeamAreanaSystem.sender:GetTeamArenaBattleStat()
    self:RefreshTeamList()
end

function P_PVPStats:RefreshTeamList()
    local blueTeamCount = #self.blueCampData.Members
    local redTeamCount = #self.redCampData.Members
    self.blueTeamList:SetData(blueTeamCount)
    self.redTeamList:SetData(redTeamCount)
end

function P_PVPStats:OnRefresh_LeftRoot(widget, index, selected)
    local member = self.blueCampData.Members[index]
    local playerStat = self.battleStat and self.battleStat[member.ID] or {}
    widget:RefreshItem(member, playerStat)
end

function P_PVPStats:OnRefresh_RightRoot(widget, index, selected)
    local member = self.redCampData.Members[index]
    local playerStat = self.battleStat and self.battleStat[member.ID] or {}
    widget:RefreshItem(member, playerStat)
end

function P_PVPStats:OnClickCloseBtn()
    UI.HideUI("P_PVPStats")
end

function P_PVPStats:OnRecvTeamArenaBattleStat(battleStat)
    self.battleStat = battleStat or {}
    self:RefreshTeamList()
end

return P_PVPStats
