local Sealed_Entries_Icon = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Entries_Icon")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local sharedSealedUtils = kg_require("Shared.Utils.SharedSealedUtils")
local ESlateVisibility = import("ESlateVisibility")
local itemConst = kg_require("Shared.ItemConst")
---@class Sealed_Mid_Item : UIListItem
---@field view Sealed_Mid_ItemBlueprint
local Sealed_Mid_Item = DefineClass("Sealed_Mid_Item", UIListItem)

Sealed_Mid_Item.eventBindMap = {
}
Sealed_Mid_Item.equipSlotInfos = {
    {
        ["leftText"] = string.sub(StringConst.Get("SealMaterial_Ordinary_Type1_Text"), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type1_Text"), 1), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type1_Text"), 2)-1),
        ["rightText"] = string.sub(StringConst.Get("SealMaterial_Ordinary_Type1_Text"), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type1_Text"), 2)),
    },
    {
        ["leftText"] = string.sub(StringConst.Get("SealMaterial_Ordinary_Type2_Text"), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type2_Text"), 1), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type2_Text"), 2)-1),
        ["rightText"] = string.sub(StringConst.Get("SealMaterial_Ordinary_Type2_Text"), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type2_Text"), 2)),
    },
    {
        ["leftText"] = string.sub(StringConst.Get("SealMaterial_Ordinary_Type3_Text"), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type3_Text"), 1), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type3_Text"), 2)-1),
        ["rightText"] = string.sub(StringConst.Get("SealMaterial_Ordinary_Type3_Text"), utf8.offset(StringConst.Get("SealMaterial_Ordinary_Type3_Text"), 2)),
    },
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Mid_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Mid_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Sealed_Mid_Item:InitUIComponent()
    ---@type Sealed_Entries_Icon
    self.WBP_Sealed_Entries_IconCom = self:CreateComponent(self.view.WBP_Sealed_Entries_Icon, Sealed_Entries_Icon)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Mid_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_Entry.OnClicked, "on_Btn_ClickArea_Entry_Clicked")
    self:AddUIEvent(self.view.Btn_ClickArea_Prop.OnClicked, "on_Btn_ClickArea_Prop_Clicked")
    self:AddUIEvent(self.view.Btn_ClickArea_Switch.OnClicked, "on_Btn_ClickArea_Switch_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Mid_Item:InitUIView()
end

---面板打开的时候触发
function Sealed_Mid_Item:OnRefresh(...)
    local args = {...}
    self.equipSlot, self.bSpecial, self.sealedId, self.bMainPage = args[1], args[2], args[3], args[4]
    local equipSlotInfo = Sealed_Mid_Item.equipSlotInfos[self.equipSlot]
    self.view.Text_Left:SetText(equipSlotInfo["leftText"])
    self.view.Text_Right:SetText(equipSlotInfo["rightText"])
    local sealedInfo = Game.SealedSystem:getEquippedSealedInfo(self.equipSlot)
    if not self.bMainPage then
        self.view.Btn_ClickArea_Entry:SetVisibility(ESlateVisibility.Collapsed)
    end
    if self.bMainPage and not sealedInfo then
        -- 在封印物主界面下才会显示为空，在封印装配界面下，选中哪个封印物就显示哪个封印物
        self.userWidget:BP_SetHide(true)
        self.view.Text_Name:SetText(StringConst.Get("SealMaterial_Unassembled_Text"))
        self.xMat = nil
    else
        self.userWidget:BP_SetHide(false)
        self.sealedId = self.sealedId or sealedInfo.sealedId
        self.bHasSealed = (Game.SealedSystem:getSealedInfo(self.sealedId) ~= nil)
        local configSealedInfo = Game.TableData.GetSealedInfoDataRow(self.sealedId)
        self.view.Text_Name:SetText(configSealedInfo.Name)
        local iconPath = Game.UIIconUtils.getIcon(configSealedInfo.Icon)
        self:SetImage(self.view.Img_Mid_Icon, iconPath)
        local xMat = Game.SealedSystem:getSealedXMatInfo(self.sealedId)
        self.userWidget:BP_SetSpecial(self.bSpecial)
        if xMat then
            self.xMat = xMat
            local configXMatInfo = Game.TableData.GetItemNewDataRow(xMat.itemId)
            iconPath = Game.UIIconUtils.getIcon(configXMatInfo.icon)
            --self.view.Img_Icon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self:SetImage(self.view.Img_Icon, iconPath)
            local isRare = sharedSealedUtils.getSpecialWordNum(xMat.xtraMatPropInfo.randomProps) > 0
            self.userWidget:BP_SetRareTag(isRare)
            self.userWidget:BP_SetQuality(configXMatInfo.quality-1)
            self.WBP_Sealed_Entries_IconCom:SetWordNum(#xMat.xtraMatPropInfo.randomProps)
            self.userWidget:BP_SetAssemble(true)
        else
            self.xMat = nil
            self.userWidget:BP_SetAssemble(false)
        end
    end
end

function Sealed_Mid_Item:tryOpenXMatEquipPanel()
    if self.bHasSealed then
        Game.NewUIManager:OpenPanel(UIPanelConfig.Sealed_XMatEquip_Panel, false, self.sealedId)
    end 
end


--- 此处为自动生成
function Sealed_Mid_Item:on_Btn_ClickArea_Entry_Clicked()
    if self.bMainPage then
        Game.NewUIManager:OpenPanel(UIPanelConfig.Sealed_Equip_Panel, self.equipSlot)
    end
end

--- 此处为自动生成
function Sealed_Mid_Item:on_Btn_ClickArea_Prop_Clicked()
    if self.xMat then
        Game.BagSystem:ShowItemTips(itemConst.INV_TYPE_XMAT, self.xMat, false, Game.BagSystem.ItemFrom.ITEM_FROM_INVENTORY, self.userWidget)
    else
        self:tryOpenXMatEquipPanel()
    end
end

--- 此处为自动生成
function Sealed_Mid_Item:on_Btn_ClickArea_Switch_Clicked()
    self:tryOpenXMatEquipPanel()
end

return Sealed_Mid_Item
