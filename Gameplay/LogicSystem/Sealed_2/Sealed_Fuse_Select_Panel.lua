local Sealed_Fuse_Select_Physical = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Fuse_Select_Physical")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class Sealed_Fuse_Select_Panel : UIPanel
---@field view Sealed_Fuse_Select_PanelBlueprint
local Sealed_Fuse_Select_Panel = DefineClass("Sealed_Fuse_Select_Panel", UIPanel)

Sealed_Fuse_Select_Panel.eventBindMap = {
    [EEventTypesV2.ON_SEALED_XMAT_SELECT_FUSE] = "onXMatSelected"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Fuse_Select_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Fuse_Select_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Sealed_Fuse_Select_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
    ---@type Sealed_Fuse_Select_Physical
    self.PhysicalBtn3Com = self:CreateComponent(self.view.PhysicalBtn3, Sealed_Fuse_Select_Physical)
    ---@type Sealed_Fuse_Select_Physical
    self.PhysicalBtn2Com = self:CreateComponent(self.view.PhysicalBtn2, Sealed_Fuse_Select_Physical)
    ---@type Sealed_Fuse_Select_Physical
    self.PhysicalBtn1Com = self:CreateComponent(self.view.PhysicalBtn1, Sealed_Fuse_Select_Physical)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Fuse_Select_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Fuse_Select_Panel:InitUIView()
    self.WBP_ComBtnCom:SetName(StringConst.Get("SealMaterial_Substance_Synthesis_Confirm_Selection"))
end

---面板打开的时候触发
function Sealed_Fuse_Select_Panel:OnRefresh(...)
    local fuseProgressPoint = Game.SealedSystem:getFuseProgressPoint()
    self.view.Text_Num:SetText(fuseProgressPoint)
    self.recommendIdx = Game.SealedSystem:getRecommendIdx()
    for i = 1,3 do
        local itemName = string.format("PhysicalBtn%dCom", i)
        self[itemName]:Refresh(i, i == self.recommendIdx, i == 3)
        self[itemName]:SetSelect(false)
    end
end

function Sealed_Fuse_Select_Panel:UpdateSelectState(idx)
    self.curSelectIdx = idx
    for i = 1,3 do
        local itemName = string.format("PhysicalBtn%dCom", i)
        self[itemName]:SetSelect(i == idx)
    end
end

function Sealed_Fuse_Select_Panel:onXMatSelected()
    self:CloseSelf()
end

--- 此处为自动生成
function Sealed_Fuse_Select_Panel:on_WBP_ComBtnCom_ClickEvent()
    if self.curSelectIdx then
        if self.recommendIdx == self.curSelectIdx then
            self:doSelect()
        else
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEAL_SYNTHESIS_CHOOSE_LOW_SCORE_ITEM,
                    function()
                        self:doSelect()
                    end
            )
        end
    end
end

function Sealed_Fuse_Select_Panel:doSelect()
    Game.me:ReqSelectFuse(self.curSelectIdx)
end

return Sealed_Fuse_Select_Panel
