local Sealed_XMatBag_RightTip = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_XMatBag_RightTip")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local Sealed_Filter_Btn = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Filter_Btn")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIComSortBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSortBtn")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local BagTree_Item = kg_require("Gameplay.LogicSystem.BagSystem.Common.BagTree_Item")
local sealedConst = kg_require("Shared.Const.SealedConst")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESelectionMode = import("ESelectionMode")
local itemConst = kg_require("Shared.ItemConst")
---@class Sealed_XMatBag_Panel : UIPanel
---@field view Sealed_XMatBag_PanelBlueprint
local Sealed_XMatBag_Panel = DefineClass("Sealed_XMatBag_Panel", UIPanel)

Sealed_XMatBag_Panel.eventBindMap = {
    [_G.EEventTypes.ON_SEALED_XMAT_FILTER_UPDATE] = "OnXMatUpdate",
    [_G.EEventTypes.ON_SEALED_XMAT_UPDATE] = "OnXMatUpdate",
    [_G.EEventTypes.ON_SEALED_XMAT_LOCK_UPDATE] = "OnXMatLockUpdate",
    [EEventTypesV2.BAG_ITEMS_DECOMPOSE_BATCH] = "OnXMatUpdate",
}
-- 非凡物质背包排序文本
Sealed_XMatBag_Panel.SortName = {
    [sealedConst.SORT_TYPE.MARK] = {["name"] = StringConst.Get("SealMaterial_Substance_Sort_Text_P2")},
    [sealedConst.SORT_TYPE.QUALITY] = {["name"] = StringConst.Get("SealMaterial_Substance_Sort_Text_P3")},
    [sealedConst.SORT_TYPE.WORD_NUM] = {["name"] = StringConst.Get("SealMaterial_Substance_Sort_Text_P1")},
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_XMatBag_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_XMatBag_Panel:InitUIData()
    self.SortSelectedIndex = sealedConst.SORT_TYPE.MARK
    self.bDecomposeMode = false
    self.decomposeCache = {}
end

--- UI组件初始化，此处为自动生成
function Sealed_XMatBag_Panel:InitUIComponent()
    ---@type Sealed_XMatBag_RightTip
    self.WBP_Sealed_XMatBag_RightTipCom = self:CreateComponent(self.view.WBP_Sealed_XMatBag_RightTip, Sealed_XMatBag_RightTip)
    ---@type UIComSortBtn
    self.WBP_ComSortBtnCom = self:CreateComponent(self.view.WBP_ComSortBtn, UIComSortBtn)
    ---@type UIComDropDown
    self.WBP_ComSelectDropType1Com = self:CreateComponent(self.view.WBP_ComSelectDropType1, UIComDropDown)
    ---@type UIComButton
    self.WBP_ComBtn_GoCom = self:CreateComponent(self.view.WBP_ComBtn_Go, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_ConfirmCom = self:CreateComponent(self.view.WBP_ComBtn_Confirm, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_ResolveCom = self:CreateComponent(self.view.WBP_ComBtn_Resolve, UIComButton)
    ---@type Sealed_Filter_Btn
    self.WBP_Sealed_Filter_BtnCom = self:CreateComponent(self.view.WBP_Sealed_Filter_Btn, Sealed_Filter_Btn)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
    ---@type UIListView childScript: ItemBoxNew
    self.TileViewCom = self:CreateComponent(self.view.TileView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function Sealed_XMatBag_Panel:InitUIEvent()
    self:AddUIEvent(self.TileViewCom.onGetEntryLuaClass, "on_TileViewCom_GetEntryLuaClass")
    self:AddUIEvent(self.TileViewCom.onItemSelected, "on_TileViewCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComBtn_ResolveCom.onClickEvent, "on_WBP_ComBtn_ResolveCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_ConfirmCom.onClickEvent, "on_WBP_ComBtn_ConfirmCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_GoCom.onClickEvent, "on_WBP_ComBtn_GoCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComSortBtnCom.onSortEvent, "on_WBP_ComSortBtnCom_SortEvent")
    self:AddUIEvent(self.WBP_ComSelectDropType1Com.onItemSelected, "on_WBP_ComSelectDropType1Com_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_XMatBag_Panel:InitUIView()
    self.WBP_ComBackTitleCom:Refresh(StringConst.Get("SealMaterial_Substance_Title"), Enum.ETipsData.SEAL_SUBSTANCE_TIPS)
end

---面板打开的时候触发
function Sealed_XMatBag_Panel:OnRefresh(...)
    Game.SealedSystem:genXMatsShowData(self, true)
    self.TileViewCom:Refresh(Game.SealedSystem.xMatShowDatas)
    if next(Game.SealedSystem.xMatShowDatas) then
        self.TileViewCom:SetSelectedItemByIndex(1, true)
        if not self.bDecomposeMode then
            -- 分解过程中如果道具被分解了，需要刷新一下界面显示，但是此时SetEmpty为0会把前往按钮再显示出来，这里并不是希望的行为
            self.userWidget:BP_SetEmpty(0)
        end
    else
        if Game.SealedSystem:hasXMat() then
            -- 由于筛选导致为空
            self.userWidget:BP_SetEmpty(2)
        else
            -- 背包本身为空
            self.userWidget:BP_SetEmpty(1)
        end
    end
    self.WBP_ComSelectDropType1Com:Refresh(Sealed_XMatBag_Panel.SortName, self.SortSelectedIndex, false)
    self.WBP_ComBtn_GoCom:SetName(StringConst.Get("SealMaterial_Go_To_Assembly"))
    self.WBP_ComBtn_ConfirmCom:SetName(StringConst.Get("SealMaterial_Substance_Break_Down_Confirm"))
    self:updateDecomposeMode(false)
end

function Sealed_XMatBag_Panel:OnXMatUpdate()
    self:OnRefresh()
end

function Sealed_XMatBag_Panel:OnXMatLockUpdate(slotIndex)
    local showIdx = Game.SealedSystem.xMatSlotIdxToShowIdx[slotIndex]
    if showIdx then
        local xMatItem = self.TileViewCom:GetItemByIndex(showIdx)
        local xMat = Game.BagSystem:GetItemInfoByIndex(itemConst.INV_TYPE_XMAT, xMatItem.SlotIndex)
        local xMatPropInfo = xMat.xtraMatPropInfo
        if xMatPropInfo.lock then
            xMatItem:CallItemComponent("SetRT", Enum.RightUpIconType.XMat_Lock)
        else
            xMatItem:CallItemComponent("SetRT", Enum.RightUpIconType.None)
        end    
    end
end

function Sealed_XMatBag_Panel:updateDecomposeMode(bSwitch)
    if bSwitch then
        self.bDecomposeMode = not self.bDecomposeMode
        self.decomposeCache = {}
    end
    if self.bDecomposeMode then
        self.WBP_ComBtn_ResolveCom:SetName(StringConst.Get("SealMaterial_Substance_Break_Down_Cancel"))
        self.WBP_ComBtn_ConfirmCom:Show()
        self.TileViewCom:SetSelectionMode(ESelectionMode.Multi)
        self.WBP_ComBtn_GoCom:Hide()
    else
        self.WBP_ComBtn_ResolveCom:SetName(StringConst.Get("SealMaterial_Substance_Break_Down_Text"))
        self.WBP_ComBtn_ConfirmCom:Hide()
        self.TileViewCom:SetSelectionMode(ESelectionMode.Single)
        self.WBP_ComBtn_GoCom:Show()
        if bSwitch then
            self.TileViewCom:RefreshItems()
        end
    end
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function Sealed_XMatBag_Panel:on_TileViewCom_GetEntryLuaClass(index)
    return BagTree_Item
end


--- 此处为自动生成
function Sealed_XMatBag_Panel:on_WBP_ComBtn_ResolveCom_ClickEvent()
    self:updateDecomposeMode(true)
end

--- 此处为自动生成
function Sealed_XMatBag_Panel:on_WBP_ComBtn_ConfirmCom_ClickEvent()
    local decomposeItemInfo = {}
    local invId = itemConst.INV_TYPE_XMAT
    for _, slotIdx in pairs(self.decomposeCache) do
        local slotInfo = Game.BagSystem:GetItemInfoByIndex(invId, slotIdx)
        if slotInfo then
            decomposeItemInfo[slotInfo.index] = slotInfo.gbId
        end
    end
    Game.me:decomposeBatchItem(invId, decomposeItemInfo, 0, {}) -- luacheck: ignore
end


--- 此处为自动生成
function Sealed_XMatBag_Panel:on_WBP_ComBtn_GoCom_ClickEvent()
    Game.NewUIManager:OpenPanel(UIPanelConfig.Sealed_Main_Panel)
end


--- 此处为自动生成
---@param status SortBtnStatus
function Sealed_XMatBag_Panel:on_WBP_ComSortBtnCom_SortEvent(status)
    if status == 2 then
        Game.SealedSystem:updateSortDescend(false)
    else
        Game.SealedSystem:updateSortDescend(true)
    end
    Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_UPDATE)
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function Sealed_XMatBag_Panel:on_WBP_ComSelectDropType1Com_ItemSelected(index, data)
    self.SortSelectedIndex = index
    Game.SealedSystem:updateSortType(index)
    Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_UPDATE)
end


--- 此处为自动生成
---@param index number
---@param data table
function Sealed_XMatBag_Panel:on_TileViewCom_ItemSelected(index, data)
    local xMatItem = self.TileViewCom:GetItemByIndex(index)
    if xMatItem then
        local xMat = Game.BagSystem:GetItemInfoByIndex(itemConst.INV_TYPE_XMAT, xMatItem.SlotIndex)
        local xMatPropInfo = xMat.xtraMatPropInfo
        if self.bDecomposeMode and not xMatPropInfo.lock and not xMatPropInfo.equippedSealedId then
            xMatItem:SetBoxType(4)
            table.insert(self.decomposeCache, xMatItem.SlotIndex)
        end
    end
    local xMatData = self.TileViewCom:GetChildData(index)
    if xMatData then
        -- 初始刷新的时候item可能还没有，通过data来刷新tips数据
        self.WBP_Sealed_XMatBag_RightTipCom:Refresh(xMatData.slotIdx)
    end
end

return Sealed_XMatBag_Panel
