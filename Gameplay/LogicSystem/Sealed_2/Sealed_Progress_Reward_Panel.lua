local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class Sealed_Progress_Reward_Panel : UIPanel
---@field view Sealed_Progress_Reward_PanelBlueprint
local Sealed_Progress_Reward_Panel = DefineClass("Sealed_Progress_Reward_Panel", UIPanel)
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local BagTree_Item = kg_require("Gameplay.LogicSystem.BagSystem.Common.BagTree_Item")

Sealed_Progress_Reward_Panel.eventBindMap = {
    [_G.EEventTypes.ON_SEALED_ALL_PROGRESS_REWARDS_COLLECTED] = "onAllRewardsCollected"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Progress_Reward_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Progress_Reward_Panel:InitUIData()
    self.curBigRewardId = nil
end

--- UI组件初始化，此处为自动生成
function Sealed_Progress_Reward_Panel:InitUIComponent()
    ---@type UIListView childScript: ItemBoxNew
    self.List_Item_1Com = self:CreateComponent(self.view.List_Item_1, UIListView)
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIListView childScript: Sealed_Progress_Reward_Panel_Reward_Item
    self.List_RewardCom = self:CreateComponent(self.view.List_Reward, UIListView)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Progress_Reward_Panel:InitUIEvent()
    self:AddUIEvent(self.List_RewardCom.onBackEdgeIntersectionListItemChanged, "on_List_RewardCom_BackEdgeIntersectionListItemChanged")
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.List_Item_1Com.onGetEntryLuaClass, "on_List_Item_1Com_GetEntryLuaClass")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Progress_Reward_Panel:InitUIView()
    self.WBP_ComBtnCom:SetName(StringConst.Get("SealMaterial_One_Touch_Receive_Text"))
end

---面板打开的时候触发
function Sealed_Progress_Reward_Panel:OnRefresh(...)
    local rewardInfos = Game.TableData.GetFuseProgressInfoDataTable()
    local showRewardInfos = {}
    for _, rewardInfo in ksbcpairs(rewardInfos) do
        if rewardInfos.IfCycle ~= 1 then
            table.insert(showRewardInfos, rewardInfo)
        end
    end
    self.List_RewardCom:Refresh(showRewardInfos)
    local curPoint = Game.me.fuseProgressInfo[Game.me.sealedCurSeasonId]
    self.view.Text_Num:SetText(curPoint)
    self.view.Text_NumGet:SetText(curPoint)
    self.view.Text_NumAll:SetText(string.format("/%d", Game.TableData.Get_maxVal()))
end


--- 此处为自动生成
function Sealed_Progress_Reward_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
    self:CloseSelf()
end


--- 此处为自动生成
function Sealed_Progress_Reward_Panel:on_WBP_ComBtnCom_ClickEvent()
    Game.me:ReqCollectAllProgressRewards()
end


--- 此处为自动生成
---@param index number
---@return UIComponent
function Sealed_Progress_Reward_Panel:on_List_RewardCom_BackEdgeIntersectionListItemChanged(index)
    local configData = self.List_RewardCom:GetChildData(index)
    if configData then
        local bigRewardId = Game.TableData.Get_toBigRewardMap()[configData.Id]
        if bigRewardId and bigRewardId ~= self.curBigRewardId then
            local bigRewardConfigData = Game.TableData.GetFuseProgressInfoDataRow(bigRewardId)
            local rewardShowDatas = Game.SealedSystem:genProgressRewardShowInfo(bigRewardConfigData)
            self.List_Item_1Com:Refresh(rewardShowDatas)
            self.curBigRewardId = bigRewardId
        end
    end
end

function Sealed_Progress_Reward_Panel:onAllRewardsCollected()
    self:OnRefresh()
end


--- 此处为自动生成
---@param index number
---@return UIComponent
function Sealed_Progress_Reward_Panel:on_List_Item_1Com_GetEntryLuaClass(index)
    return BagTree_Item
end

return Sealed_Progress_Reward_Panel
