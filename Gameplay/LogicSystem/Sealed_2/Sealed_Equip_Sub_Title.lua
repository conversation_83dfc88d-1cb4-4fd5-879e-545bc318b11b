local Sealed_Entries_Icon = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Entries_Icon")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Sealed_Equip_Sub_Title : UIComponent
---@field view Sealed_Equip_Sub_TitleBlueprint
local Sealed_Equip_Sub_Title = DefineClass("Sealed_Equip_Sub_Title", UIComponent)
local ESlateVisibility = import("ESlateVisibility")

Sealed_Equip_Sub_Title.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Equip_Sub_Title:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Equip_Sub_Title:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Sealed_Equip_Sub_Title:InitUIComponent()
    ---@type Sealed_Entries_Icon
    self.WBP_Sealed_Entries_IconCom = self:CreateComponent(self.view.WBP_Sealed_Entries_Icon, Sealed_Entries_Icon)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Equip_Sub_Title:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Equip_Sub_Title:InitUIView()
end

---组件刷新统一入口
function Sealed_Equip_Sub_Title:Refresh(...)
    local args = {...}
    local name = args[1]
    local bShowHelp = args[2]
    self.view.Text_Name:SetText(name)
    self.view.Canvas_Entries:SetVisibility(ESlateVisibility.Collapsed)
    if bShowHelp then
        self.view.Canvas_HelpBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.Canvas_HelpBtn:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function Sealed_Equip_Sub_Title:setTipId(tipId)
    self.tipsId = tipId
end

function Sealed_Equip_Sub_Title:setEntries(num)
    self.userWidget:BP_SetEntries(true)
    if num == 0 then
        self.WBP_Sealed_Entries_IconCom:Hide()
    else
        self.WBP_Sealed_Entries_IconCom:SetWordNum(num)
    end
end

function Sealed_Equip_Sub_Title:SetDesc(desc)
    if desc then
        self.view.Text_Entries:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Entries:SetText(desc)
    else
        self.view.Text_Entries:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--- 此处为自动生成
function Sealed_Equip_Sub_Title:on_Btn_ClickArea_Clicked()
    if self.tipsId then
        Game.TipsSystem:ShowTips(self.tipsId, self.view.Btn_ClickArea:GetCachedGeometry())
    end
end

return Sealed_Equip_Sub_Title
