local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Sealed_XMatWordFilter_Item : UIListItem
---@field view Sealed_XMatWordFilter_ItemBlueprint
local Sealed_XMatWordFilter_Item = DefineClass("Sealed_XMatWordFilter_Item", UIListItem)

Sealed_XMatWordFilter_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_XMatWordFilter_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_XMatWordFilter_Item:InitUIData()
    self.wordId = nil
end

--- UI组件初始化，此处为自动生成
function Sealed_XMatWordFilter_Item:InitUIComponent()
    ---@type UIComCheckBox
    self.WBP_ComCheckBoxCom = self:CreateComponent(self.view.WBP_ComCheckBox, UIComCheckBox)
end

---UI事件在这里注册，此处为自动生成
function Sealed_XMatWordFilter_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_XMatWordFilter_Item:InitUIView()
end

---面板打开的时候触发
function Sealed_XMatWordFilter_Item:OnRefresh(data)
    self.wordId = data.wordId
    self.isCheck = data.isCheck
    local wordConfigInfo = Game.TableData.GetXtraMatRandomWordDataRow(self.wordId)
    self.view.Text_Name:SetText(wordConfigInfo.SPEffectText)
    self.WBP_ComCheckBoxCom:SetChecked(self.isCheck)
end

--- 此处为自动生成
function Sealed_XMatWordFilter_Item:on_Btn_ClickArea_Clicked()
    self.isCheck = not self.isCheck
    self.WBP_ComCheckBoxCom:SetChecked(self.isCheck)
end

return Sealed_XMatWordFilter_Item
