local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class XMatQualityFilterItem : UIListItem
local XMatQualityFilterItem = DefineClass("XMatQualityFilterItem", UIListItem)

XMatQualityFilterItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function XMatQualityFilterItem:OnCreate()
	self:InitWidget()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

function XMatQualityFilterItem:InitWidget()
	self.text_Content = self.view.Text_Content
end

---初始化数据
function XMatQualityFilterItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function XMatQualityFilterItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function XMatQualityFilterItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function XMatQualityFilterItem:InitUIView()
end

---@public Refresh 刷新数据
function XMatQualityFilterItem:OnRefresh(data)
	self.qualityId = data.qualityId
    local qualityName = data.qualityName
    local isFilter = data.isFilter
    self:SetName(qualityName)
    self:UpdateSelectionState(isFilter)
end

function XMatQualityFilterItem:UpdateSelectionState(selected)
	self.userWidget:BP_SetChecked(selected)
	Game.SealedSystem:updateCacheXMatQualityFilter(self.qualityId, selected)
end

---设置名称
function XMatQualityFilterItem:SetName(name)
	if self.text_Content then
		self.text_Content:SetText(name or "")
	end
end

---设置是否有文本
---@param hasText boolean
function XMatQualityFilterItem:SetHasText(hasText)
	self.userWidget:BP_SetHasText(hasText)
end

---设置类型（暗、白）
---@param isLight boolean
function XMatQualityFilterItem:SetType(isLight)
	self.userWidget:BP_SetType(isLight)
end

---设置有效性
---@param disable boolean
function XMatQualityFilterItem:SetDisable(disable)
	self.userWidget:BP_SetDisable(disable)
end

return XMatQualityFilterItem
