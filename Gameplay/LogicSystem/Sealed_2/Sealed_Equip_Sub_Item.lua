local Sealed_MoreRare_Tag = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_MoreRare_Tag")
local Sealed_Rare_Tag = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Rare_Tag")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local ESlateVisibility = import("ESlateVisibility")
---@class Sealed_Equip_Sub_Item : UIListItem
---@field view Sealed_Equip_Sub_ItemBlueprint
local Sealed_Equip_Sub_Item = DefineClass("Sealed_Equip_Sub_Item", UIListItem)
local sealedConst = kg_require("Shared.Const.SealedConst")

Sealed_Equip_Sub_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Equip_Sub_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Equip_Sub_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Sealed_Equip_Sub_Item:InitUIComponent()
    ---@type Sealed_MoreRare_Tag
    self.WBP_Sealed_MoreRare_TagCom = self:CreateComponent(self.view.WBP_Sealed_MoreRare_Tag, Sealed_MoreRare_Tag)
    ---@type Sealed_Rare_Tag
    self.WBP_Sealed_Rare_TagCom = self:CreateComponent(self.view.WBP_Sealed_Rare_Tag, Sealed_Rare_Tag)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Equip_Sub_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Equip_Sub_Item:InitUIView()
end

---面板打开的时候触发
function Sealed_Equip_Sub_Item:OnRefresh(args)
    local bSeasonSkill = args[1]
    if bSeasonSkill then
        self:refreshBySeasonSkill(args)
    else
        self:refreshByWordId(args)
    end
end

function Sealed_Equip_Sub_Item:refreshBySeasonSkill(args)
    local extraSkillId = args[2]
    local extraSkillConfig = Game.TableData.GetSealedExtraEffectDataRow(extraSkillId)
    self.userWidget:BP_SetRare(0)
    self.userWidget:BP_SetGrey(false)
    if Game.me.sealedExtraCompletedConditions and Game.me.sealedExtraCompletedConditions[extraSkillConfig.TriggerID] then
        self.view.Text_PropertyName:SetText(extraSkillConfig.ExtraSkillText)
        self.userWidget:BP_SetLock(false)
    else
        self.view.Text_PropertyName:SetText(extraSkillConfig.ExtraSkillLockedText)
        self.userWidget:BP_SetLock(true)
    end
    self.view.Text_PropertyNum:SetVisibility(ESlateVisibility.Collapsed)
end

function Sealed_Equip_Sub_Item:refreshByWordId(args)
    local wordId, isGrey, bLock = args[2], args[3], args[4]
    local rareType = 0
    local configWordInfo = Game.TableData.GetXtraMatRandomWordDataRow(wordId)
    if wordId == Game.TableData.GetRelicsConstDataRow("AuraEntryMustAppearTimes")[1] then
        rareType = 2
    elseif configWordInfo.Type == sealedConst.SEALED_GROUP.SPECIAL then
        rareType = 1
    end
    self.userWidget:BP_SetRare(rareType)
    if bLock ~= nil then
        self.userWidget:BP_SetLock(bLock)
    end
    self.userWidget:BP_SetGrey(isGrey)

    for propName, propVal in ksbcpairs(configWordInfo.FightProp) do
        -- 这里其实只支持单个词条
        propVal = propVal[1]
        local propId = Game.TableData.Get_propChangeModeMap()[propName]
        local PropData = Game.TableData.GetFightPropModeDataRow(propId)
        local PropName = Game.TableData.GetFightPropDataRow(Enum.EFightPropData[PropData.Prop])
        --local propKeyTxt = "<DarkGrey>" .. PropName.Discription .. "</>"
        local propKeyTxt = PropName.Discription
        local propValTxt
        if PropData.ShowType == 0 then
            -- 整数
            --propValTxt = "<White>+" .. string.format("%i", math.floor(propVal)) .. "</>"
            propValTxt = string.format("+%i", math.floor(propVal))
        elseif PropData.ShowType == 1 then
            -- 百分比，一位小数
            --propValTxt = "<White>+" .. string.format("%.1f%%", propVal * 100) .. "</>"
            propValTxt = string.format("+%.1f%%", propVal * 100)
        elseif PropData.ShowType == 2 then
            -- 浮点一位小数
            --propValTxt = "<White>+" .. string.format("%.1f", propVal) .. "</>"
            propValTxt = string.format("+%.1f", propVal)
        elseif PropData.ShowType == 3 then
            -- 不显示
            propValTxt = ""
        end
        self.view.Text_PropertyName:SetText(propKeyTxt)
        self.view.Text_PropertyNum:SetText(propValTxt)
    end

    if rareType ~= 0 then
        local propKeyTxt = configWordInfo.SPEffectName
        local propValTxt = configWordInfo.SPEffectText
        if rareType == 1 then
            self.WBP_Sealed_Rare_TagCom:Refresh(propKeyTxt, isGrey)
        elseif rareType == 2 then
            self.WBP_Sealed_MoreRare_TagCom:Refresh(propKeyTxt, isGrey)
        end
        self.view.Text_PropertyName:SetText(propValTxt)
    end
end

return Sealed_Equip_Sub_Item
