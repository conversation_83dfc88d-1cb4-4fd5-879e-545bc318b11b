local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Sealed_Equip_Sub_Tab_Item : UIListItem
---@field view Sealed_Equip_Sub_Tab_ItemBlueprint
local Sealed_Equip_Sub_Tab_Item = DefineClass("Sealed_Equip_Sub_Tab_Item", UIListItem)

Sealed_Equip_Sub_Tab_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Equip_Sub_Tab_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Equip_Sub_Tab_Item:InitUIData()
    self.sealedId = 0
end

--- UI组件初始化，此处为自动生成
function Sealed_Equip_Sub_Tab_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Sealed_Equip_Sub_Tab_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Equip_Sub_Tab_Item:InitUIView()
end

---面板打开的时候触发
function Sealed_Equip_Sub_Tab_Item:OnRefresh(data)
    local iconPath = Game.UIIconUtils.getIcon(data.Icon)
    self:SetImage(self.view.Img_Icon, iconPath)
    self.sealedId = data.ID
    local sealedInfo = Game.SealedSystem:getSealedInfo(self.sealedId)
    self.userWidget:BP_SetSeason(true)
    self.userWidget:BP_SetEquip(false)
    if sealedInfo then
        if sealedInfo.equippedSlot then
            self.userWidget:BP_SetEquip(true)
        end
        self.userWidget:BP_SetLock(false)
    else
        self.userWidget:BP_SetLock(true)
    end
end

function Sealed_Equip_Sub_Tab_Item:UpdateSelectionState(selected)
    self.userWidget:BP_SetSelected(selected)
end

return Sealed_Equip_Sub_Tab_Item
