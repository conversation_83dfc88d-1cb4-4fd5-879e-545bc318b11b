local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local Sealed_Equip_Sub_Title = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Equip_Sub_Title")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local Sealed_Mid_Item = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Mid_Item")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
---@class Sealed_Equip_Panel : UIPanel
---@field view Sealed_Equip_PanelBlueprint
local Sealed_Equip_Panel = DefineClass("Sealed_Equip_Panel", UIPanel)

Sealed_Equip_Panel.eventBindMap = {
    [_G.EEventTypes.ON_SEALED_EQUIP] = "OnSealedUpdate",
    [_G.EEventTypes.ON_SEALED_XMAT_UPDATE] = "OnSealedUpdate",
    [_G.EEventTypes.ON_SEALED_XMAT_EQUIP_CLOSE] = "onXMatEquipClose",
    [_G.EEventTypes.ON_SEALED_XMAT_EQUIP_OPEN] = "onXMatEquipOpen",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Equip_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Equip_Panel:InitUIData()
    self.equipSlot = 0
    self.selectIndex = 0
end

--- UI组件初始化，此处为自动生成
function Sealed_Equip_Panel:InitUIComponent()
    ---@type UIComEmptyConent
    self.WBP_ComEmptyCom = self:CreateComponent(self.view.WBP_ComEmpty, UIComEmptyConent)
    ---@type UIListView childScript: Sealed_Equip_Sub_Item
    self.TileView_SubstanceCom = self:CreateComponent(self.view.TileView_Substance, UIListView)
    ---@type UIComButton
    self.WBP_ComBtn_EquipCom = self:CreateComponent(self.view.WBP_ComBtn_Equip, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_UnloadCom = self:CreateComponent(self.view.WBP_ComBtn_Unload, UIComButton)
    ---@type Sealed_Equip_Sub_Title
    self.Sub_Title3Com = self:CreateComponent(self.view.Sub_Title3, Sealed_Equip_Sub_Title)
    ---@type Sealed_Equip_Sub_Title
    self.Sub_Title2Com = self:CreateComponent(self.view.Sub_Title2, Sealed_Equip_Sub_Title)
    ---@type Sealed_Equip_Sub_Title
    self.Sub_Title1Com = self:CreateComponent(self.view.Sub_Title1, Sealed_Equip_Sub_Title)
    ---@type UIComDiyTitle
    self.WBP_ComDIYTextCom = self:CreateComponent(self.view.WBP_ComDIYText, UIComDiyTitle)
    ---@type UIListView childScript: Sealed_Equip_Sub_Tab_Item
    self.ListView_TabCom = self:CreateComponent(self.view.ListView_Tab, UIListView)
    ---@type Sealed_Mid_Item
    self.WBP_Sealed_Mid_ItemCom = self:CreateComponent(self.view.WBP_Sealed_Mid_Item, Sealed_Mid_Item)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Equip_Panel:InitUIEvent()
    self:AddUIEvent(self.ListView_TabCom.onItemSelected, "on_ListView_TabCom_ItemSelected")
    self:AddUIEvent(self.WBP_ComBtn_UnloadCom.onClickEvent, "on_WBP_ComBtn_UnloadCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_EquipCom.onClickEvent, "on_WBP_ComBtn_EquipCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Equip_Panel:InitUIView()
    self.Sub_Title1Com:Refresh(StringConst.Get("SealMaterial_Extraordinary_abilities"))
    self.Sub_Title2Com:Refresh(StringConst.Get("SealMaterial_Negative_Effects"))
    self.Sub_Title3Com:Refresh(StringConst.Get("SealMaterial_Extraordinary_Substance"))
    self.WBP_ComBtn_EquipCom:SetName(StringConst.Get("SealMaterial_Replace_Button"))
    self.WBP_ComBtn_UnloadCom:SetName(StringConst.Get("SealMaterial_Remove_Button"))
    self.WBP_ComBackTitleCom:Refresh(StringConst.Get("SealMaterial_Assemble_Title"))
end

---面板打开的时候触发
function Sealed_Equip_Panel:OnRefresh(...)
    local args = {...}
    self.equipSlot = args[1]
    self.selectIndex = args[2]
    local sealedInfos = Game.SealedSystem:getAllCurSeasonSameGroupIdSealedInfos(self.equipSlot)
    self.ListView_TabCom:Refresh(sealedInfos)
    if next(sealedInfos) then
        local sealedInfo = Game.SealedSystem:getEquippedSealedInfo(self.equipSlot)
        if sealedInfo and not self.selectIndex then
            for i, showSealedInfo in ipairs(sealedInfos) do
                if sealedInfo.sealedId == showSealedInfo.ID then
                    self.selectIndex = i
                    break
                end
            end
        end
    end
    self.selectIndex = self.selectIndex or 1
    self.ListView_TabCom:SetSelectedItemByIndex(self.selectIndex, true)
    local xMat = Game.SealedSystem:getEquippedXMatInfo(self.equipSlot)
    local wordShowData = {}
    if xMat then
        local sealedInfo = Game.SealedSystem:getEquippedSealedInfo(self.equipSlot)
        local invalidWordIndexMap = sealedInfo.invalidWordIndexMap or {}
        for i, wordId in ipairs(xMat.xtraMatPropInfo.randomProps) do
            table.insert(wordShowData, {wordId, invalidWordIndexMap[i] or false})
        end
        --self.TileView_SubstanceCom:Refresh(xMat.xtraMatPropInfo.randomProps)
        self.TileView_SubstanceCom:Refresh(wordShowData)
    end
end


--- 此处为自动生成
---@param index number
---@param data table
function Sealed_Equip_Panel:on_ListView_TabCom_ItemSelected(index, data)
    local sealedData = self.ListView_TabCom:GetChildData(index)
    if not sealedData then
        return
    end
    local sealedId = sealedData.ID
    local sealedInfo = Game.SealedSystem:getSealedInfo(sealedId)
    local configSealedInfo = Game.TableData.GetSealedInfoDataRow(sealedId)
    self.selectIndex = index
    self.WBP_ComDIYTextCom:Refresh(configSealedInfo.Name)
    self.userWidget:BP_SetEquip(false)
    local hasEquip = Game.SealedSystem:getEquippedSealedInfo(self.equipSlot)
    if sealedInfo then
        self.userWidget:BP_SetEmpty(false)
        self:showBtn()
        self.view.Text_Info:SetText(configSealedInfo.ItemDes)
        self.view.Text_Ability:SetText(configSealedInfo.BuffDescribeText)
        self.view.Text_Negative:SetText(configSealedInfo.DebuffDescribeText)
        if sealedInfo.equippedSlot then
            self.userWidget:BP_SetEquip(true)
            self:showUnload()
        else
            if hasEquip then
                self:showEquip(StringConst.Get("SealMaterial_Replace_Button"))
            else
                self:showEquip(StringConst.Get("SealMaterial_Assembly_Button"))
            end
        end
    else
        self.userWidget:BP_SetEmpty(true)
        self.WBP_ComEmptyCom:Refresh(configSealedInfo.LockedText)
        self:hideBtn()
    end
    local bSeasonTag = true
    for _, seasonId in ipairs(configSealedInfo.SeasonIdList) do
        if seasonId == 0 then
            bSeasonTag = false
            break
        end
    end 
    self.userWidget:BP_SetSeason(bSeasonTag)
    self.WBP_Sealed_Mid_ItemCom:OnRefresh(self.equipSlot, false, sealedId, false)
end

function Sealed_Equip_Panel:onXMatEquipClose()
    self.view.WBP_ComMaskR_Big:SetVisibility(ESlateVisibility.selfHitTestInvisible)
end

function Sealed_Equip_Panel:onXMatEquipOpen()
    self.view.WBP_ComMaskR_Big:SetVisibility(ESlateVisibility.Collapsed)
end

function Sealed_Equip_Panel:showUnload()
    self.WBP_ComBtn_UnloadCom:Show()
    self.WBP_ComBtn_EquipCom:Hide()
end

function Sealed_Equip_Panel:showEquip(equipBtnStr)
    self.WBP_ComBtn_UnloadCom:Hide()
    self.WBP_ComBtn_EquipCom:Show()
    self.WBP_ComBtn_EquipCom:SetName(equipBtnStr)
end

function Sealed_Equip_Panel:showBtn()
    self.WBP_ComBtn_UnloadCom:Show()
    self.WBP_ComBtn_EquipCom:Show()
end

function Sealed_Equip_Panel:hideBtn()
    self.WBP_ComBtn_UnloadCom:Hide()
    self.WBP_ComBtn_EquipCom:Hide()
end

--- 此处为自动生成
function Sealed_Equip_Panel:on_WBP_ComBtn_UnloadCom_ClickEvent()
    local selectedIndex = self.ListView_TabCom:GetSelectedItemIndex()
    local sealedData = self.ListView_TabCom:GetChildData(selectedIndex)
    local sealedId = sealedData.ID
    local sealedInfo = Game.SealedSystem:getSealedInfo(sealedId)
    if sealedInfo and sealedInfo.equippedSlot then
        Game.me:ReqSealedPutOff(self.equipSlot)
    end
end

--- 此处为自动生成
function Sealed_Equip_Panel:on_WBP_ComBtn_EquipCom_ClickEvent()
    local selectedIndex = self.ListView_TabCom:GetSelectedItemIndex()
    local sealedData = self.ListView_TabCom:GetChildData(selectedIndex)
    if not sealedData then
        return
    end
    local sealedId = sealedData.ID
    local sealedInfo = Game.SealedSystem:getSealedInfo(sealedId)
    if sealedInfo then
        Game.me:ReqSealedPutOn(sealedId, sealedInfo.gbId, self.equipSlot)
    end
end

function Sealed_Equip_Panel:OnSealedUpdate()
    self:OnRefresh(self.equipSlot, self.selectIndex)
end

return Sealed_Equip_Panel
