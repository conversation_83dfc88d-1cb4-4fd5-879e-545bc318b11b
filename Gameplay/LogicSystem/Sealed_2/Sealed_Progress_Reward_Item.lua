local Sealed_Progress_Reward_Tick_Item = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Progress_Reward_Tick_Item")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Sealed_Progress_Reward_Item : UIListItem
---@field view Sealed_Progress_Reward_ItemBlueprint
local Sealed_Progress_Reward_Item = DefineClass("Sealed_Progress_Reward_Item", UIListItem)
local BagTree_Item = kg_require("Gameplay.LogicSystem.BagSystem.Common.BagTree_Item")

Sealed_Progress_Reward_Item.eventBindMap = {
    [_G.EEventTypes.ON_SEALED_PROGRESS_REWARD_COLLECTED] = "onRewardCollected"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Progress_Reward_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Progress_Reward_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Sealed_Progress_Reward_Item:InitUIComponent()
    ---@type Sealed_Progress_Reward_Tick_Item
    self.WBP_Sealed_Progress_Reward_Tick_ItemCom = self:CreateComponent(self.view.WBP_Sealed_Progress_Reward_Tick_Item, Sealed_Progress_Reward_Tick_Item)
    ---@type UIListView childScript: ItemBoxNew
    self.List_ItemCom = self:CreateComponent(self.view.List_Item, UIListView)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Progress_Reward_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Get.OnClicked, "on_Btn_Get_Clicked")
    self:AddUIEvent(self.List_ItemCom.onGetEntryLuaClass, "on_List_ItemCom_GetEntryLuaClass")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Progress_Reward_Item:InitUIView()
end

---面板打开的时候触发
function Sealed_Progress_Reward_Item:OnRefresh(configData)
    self.rewardId = configData.Id
    local curSeasonId = Game.me.sealedCurSeasonId
    local curPoint = Game.me.fuseProgressInfo[curSeasonId]
    local bAchievePoint = curPoint >= configData.Value
    self.bAchievePoint = bAchievePoint
    local nextRewardId = configData.Id + 1
    local nextConfigData = Game.TableData.GetFuseProgressInfoDataRow(nextRewardId)
    local bAchieveNextPoint = false
    if nextConfigData and nextConfigData.IfCycle ~= 1 then
        bAchieveNextPoint = curPoint >= nextConfigData.Value
    end
    if not bAchievePoint then
        self.userWidget:BP_SetActivation(0)
        self.userWidget:BP_SetBarBGState(1)
    else
        if not bAchieveNextPoint then
            self.userWidget:BP_SetActivation(1)
        else
            self.userWidget:BP_SetActivation(2)
        end
    end
    self.userWidget:BP_SetBarBGState(1)
    self.bRewardCollected = Game.me.fuseProgressRewardCollectInfos[curSeasonId].progressReward[self.rewardId]
    self.WBP_Sealed_Progress_Reward_Tick_ItemCom:OnRefresh(configData, bAchievePoint, self.bRewardCollected)
    local rewardShowDatas = Game.SealedSystem:genProgressRewardShowInfo(configData)
    self.List_ItemCom:Refresh(rewardShowDatas)
end

--- 此处为自动生成
function Sealed_Progress_Reward_Item:on_Btn_Get_Clicked()
    if self.bAchievePoint and not self.bRewardCollected then
        Game.me:ReqCollectProgressReward(self.rewardId)
    end
end

function Sealed_Progress_Reward_Item:onRewardCollected(rewardId)
    if self.rewardId == rewardId then
        self.bRewardCollected = true
        local configData = Game.TableData.GetFuseProgressInfoDataRow(self.rewardId)
        local rewardShowDatas = Game.SealedSystem:genProgressRewardShowInfo(configData)
        self.List_ItemCom:Refresh(rewardShowDatas)
        self.WBP_Sealed_Progress_Reward_Tick_ItemCom:SetRewardCollected()
    end
end


--- 此处为自动生成
---@param index number
---@return UIComponent
function Sealed_Progress_Reward_Item:on_List_ItemCom_GetEntryLuaClass(index)
    return BagTree_Item
end

return Sealed_Progress_Reward_Item
