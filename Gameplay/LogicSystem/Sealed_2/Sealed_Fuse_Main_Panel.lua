local Sealed_Fuse_Main_Physical = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Fuse_Main_Physical")
local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local sharedSealedUtils = kg_require("Shared.Utils.SharedSealedUtils")
local itemConst = kg_require("Shared.ItemConst")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESlateVisibility = import("ESlateVisibility")
---@class Sealed_Fuse_Main_Panel : UIPanel
---@field view Sealed_Fuse_Main_PanelBlueprint
local Sealed_Fuse_Main_Panel = DefineClass("Sealed_Fuse_Main_Panel", UIPanel)

Sealed_Fuse_Main_Panel.eventBindMap = {
    [_G.EEventTypes.ON_SEALED_XMAT_FUSE_SELECT_UPDATE] = "onFuseSelectUpdate",
    [_G.EEventTypes.ON_SEALED_XMAT_FUSE_UPDATE] = "onFuseUpdate",
    [_G.EEventTypes.ON_SEALED_XMAT_SELECT_FUSE] = "onXMatSelected",
    [_G.EEventTypes.ON_SEALED_XMAT_EQUIP_CLOSE] = "onFuseCloseSideBar",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Fuse_Main_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Fuse_Main_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Sealed_Fuse_Main_Panel:InitUIComponent()
    ---@type Sealed_Fuse_Main_Physical
    self.PhysicalBtn_2Com = self:CreateComponent(self.view.PhysicalBtn_2, Sealed_Fuse_Main_Physical)
    ---@type Sealed_Fuse_Main_Physical
    self.PhysicalBtn_1Com = self:CreateComponent(self.view.PhysicalBtn_1, Sealed_Fuse_Main_Physical)
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
    ---@type UIComButton
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function Sealed_Fuse_Main_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Expand.OnClicked, "on_Btn_Expand_Clicked")
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_Experience.OnClicked, "on_Btn_Experience_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Fuse_Main_Panel:InitUIView()
    self.WBP_ComBtnCom:SetName(StringConst.Get("SealMaterial_Substance_Synthesis"))
end

---面板打开的时候触发
function Sealed_Fuse_Main_Panel:OnRefresh(...)
    self.expand = false
    self:updateSideBar()
    self.PhysicalBtn_1Com:SetEmpty(true)
    self.PhysicalBtn_2Com:SetEmpty(true)
    self:updateFuseBtnAndConsume()
    if next(Game.me.fuseXMatSlots) then
        -- 有未选择的合成结果，直接展示合成页面要求玩家选择
        Game.NewUIManager:OpenPanel(UIPanelConfig.Sealed_Fuse_Select_Panel)
    end
end

function Sealed_Fuse_Main_Panel:updateSideBar()
    self.userWidget:BP_SetExpand(self.expand)
    if self.expand then
        self.view.Canvas_ExpandBtn:SetVisibility(ESlateVisibility.Collapsed)
        Game.NewUIManager:OpenPanel(UIPanelConfig.Sealed_XMatEquip_Panel, true)
    else
        self.view.Canvas_ExpandBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        Game.NewUIManager:ClosePanel(UIPanelConfig.Sealed_XMatEquip_Panel)
    end
end

function Sealed_Fuse_Main_Panel:onFuseSelectUpdate(bApply, fuseSlotIdx, xMatSlotIdx)
    local fuseItemName = string.format("PhysicalBtn_%dCom", fuseSlotIdx)
    local fuseItem = self[fuseItemName]
    if bApply then
        local xMatItem = Game.BagSystem:GetItemInfoByIndex(itemConst.INV_TYPE_XMAT, xMatSlotIdx)
        fuseItem:Refresh(xMatItem)
    else
        fuseItem:SetEmpty(true)
    end
    self:updateFuseBtnAndConsume()
end

function Sealed_Fuse_Main_Panel:onFuseUpdate()
    self:PlayAnimation(self.view.Ani_ronghe, function()
        Game.NewUIManager:OpenPanel(UIPanelConfig.Sealed_Fuse_Select_Panel)
    end)
end

function Sealed_Fuse_Main_Panel:onXMatSelected()
    self:OnRefresh()
end

function Sealed_Fuse_Main_Panel:updateFuseBtnAndConsume()
    self.view.Text_Info:SetText(StringConst.Get("RED_PACKET_COST"))
    local currencyType = sharedSealedUtils:getFuseConsumeCurrencyType()
    local configCurrencyInfo = Game.TableData.GetItemNewDataRow(currencyType)
    local iconPath = Game.UIIconUtils.getIcon(configCurrencyInfo.icon)
    self:SetImage(self.view.Img_Currency, iconPath)
    local consumeStr = "0/0"
    if Game.SealedSystem:hasEmptyFuseSlot() then
        self.WBP_ComBtnCom:SetDisable(true)
    else
        local totalWordNum = Game.SealedSystem:getFuseTotalWordNum()
        local fuseConfig = Game.TableData.GetFuseInfoDataRow(totalWordNum)
        for _, consumeNum in pairs(fuseConfig.Consume) do
            consumeStr = string.format("%s/%s", consumeNum, Game.CurrencySystem:GetMoneyByType(currencyType))
        end
        self.WBP_ComBtnCom:SetDisable(false)
    end
    self.view.Text_Num:SetText(consumeStr)
end

function Sealed_Fuse_Main_Panel:onFuseCloseSideBar()
    self.expand = false
    self:updateSideBar()
end

--- 此处为自动生成
function Sealed_Fuse_Main_Panel:on_Btn_Expand_Clicked()
    self.expand = not self.expand
    self:updateSideBar()
end

--- 此处为自动生成
function Sealed_Fuse_Main_Panel:on_WBP_ComBtnCom_ClickEvent()
    if Game.SealedSystem.fuseSlot1 and Game.SealedSystem.fuseSlot2 then
        Game.me:ReqFuseXMat(Game.SealedSystem.fuseSlot1, Game.SealedSystem.fuseSlot2)
    end
end


--- 此处为自动生成
function Sealed_Fuse_Main_Panel:on_Btn_Experience_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.Sealed_Progress_Reward_Panel)
end

return Sealed_Fuse_Main_Panel
