local ItemBoxNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemBoxNew")
---@class XMat_Item : ItemBoxNew
local XMat_Item = DefineClass("XMat_Item", ItemBoxNew)

XMat_Item.eventBindMap = {
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function XMat_Item:OnCreate()
	ItemBoxNew.OnCreate(self)
end

---初始化数据
function XMat_Item:InitUIData()
	--self.EntityID = nil
	--self.ItemID = nil
	--self.EquipedSlot = nil
	--self.ProfessionID = nil
	--self.bMainPlayer = nil
	--self.Index = nil
end

function XMat_Item:InitUIComponent()
end

function XMat_Item:InitUIEvent()
end

function XMat_Item:InitUIView()
end

--function XMat_Item:OnRefresh(Params)
--	if not Params then return end
--	if Params.Data then
--		self.ItemID = Params.Data.itemId
--	else
--		self.ItemID = nil
--	end
--	self.EntityID = Params.EntityID
--	self.EquipedSlot = Params.EquipedSlot
--	self.ProfessionID = Params.ProfessionID
--	self.bMainPlayer = Params.bMainPlayer
--
--	if Params.Data == nil then
--		local DefaultIcon = Game.TableData.GetEquipmentSlotDataRow(self.EquipedSlot).DefaultIcon
--		if DefaultIcon then
--			self:RefreshEquipSlot(DefaultIcon)
--		end
--	else
--		self:FillItem(self.ItemID, Enum.GridState.Occupy, Enum.CommonItemClickType.NoFunChick, false, 1)
--	end
--
--	-- 他人角色展示，不做装备可佩戴
--	if self.EntityID ~= GetMainPlayerEID()  then
--		self:CallItemComponent("SetStatus", Enum.ItemStatus.Normal)
--	end
--end
--
--function XMat_Item:UpdateSelectionState(selected)
--	self:SetSelected(selected)
--end
--
--function XMat_Item:RefreshEquipSlot(imgPath)
--	self:FillItem(nil, nil, Enum.CommonItemClickType.NoFunChick)
--	self.view.Bg_Equip_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
--	self:SetImage(self.view.Bg_Equip_lua, imgPath)
--end

function XMat_Item:OnClickBtnInternal()
	ItemBoxNew.OnClickBtnInternal(self)
	self.parentComponent:processListClickInternal(self.index, self.data)
end

return XMat_Item
