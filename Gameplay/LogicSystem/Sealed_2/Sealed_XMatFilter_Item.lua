local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class Sealed_XMatFilter_Item : UIListItem
---@field view Sealed_XMatFilter_ItemBlueprint
local Sealed_XMatFilter_Item = DefineClass("Sealed_XMatFilter_Item", UIListItem)

Sealed_XMatFilter_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_XMatFilter_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_XMatFilter_Item:InitUIData()
    self.wordId = nil
    self.isAdd = false
    self.clickCb = nil
end

--- UI组件初始化，此处为自动生成
function Sealed_XMatFilter_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Sealed_XMatFilter_Item:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_XMatFilter_Item:InitUIView()
end

---面板打开的时候触发
function Sealed_XMatFilter_Item:OnRefresh(args)
    self.wordId = args["wordId"]
    self.isAdd = args["isAdd"] or false
    self.clickCb = args["clickCb"]
    self.userWidget:BP_SetAdd(self.isAdd)
    if not self.isAdd then
        local wordConfigInfo = Game.TableData.GetXtraMatRandomWordDataRow(self.wordId)
        self.view.Text_Name:SetText(wordConfigInfo.SPEffectText)
    end
    
end

--- 此处为自动生成
function Sealed_XMatFilter_Item:on_Btn_ClickArea_Clicked()
    if self.clickCb then
        self.clickCb()
    end
    if not self.isAdd then
        self:GetParent():GetParent():RemoveWord(self.index, self.wordId)
    end
end

return Sealed_XMatFilter_Item
