local Sealed_Filter_Btn = kg_require("Gameplay.LogicSystem.Sealed_2.Sealed_Filter_Btn")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIComSortBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSortBtn")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local itemConst = kg_require("Shared.ItemConst")
local BagTree_Item = kg_require("Gameplay.LogicSystem.BagSystem.Common.BagTree_Item")
local sealedConst = kg_require("Shared.Const.SealedConst")
local ESlateVisibility = import("ESlateVisibility")

---@class Sealed_XMatEquip_Panel : UIPanel
---@field view Sealed_XMatEquip_PanelBlueprint
local Sealed_XMatEquip_Panel = DefineClass("Sealed_XMatEquip_Panel", UIPanel)

Sealed_XMatEquip_Panel.eventBindMap = {
    [_G.EEventTypes.ON_SEALED_XMAT_FILTER_UPDATE] = "onFilterUpdate",
    [_G.EEventTypes.ON_SEALED_XMAT_UPDATE] = "OnXMatUpdate",
    [EEventTypesV2.BAG_ITEMS_DECOMPOSE_BATCH] = "OnXMatUpdate",
}

-- 排序名称
Sealed_XMatEquip_Panel.SortName = {
    [sealedConst.SORT_TYPE.MARK] = {["name"] = StringConst.Get("SealMaterial_Substance_Sort_Text_P2")},
    [sealedConst.SORT_TYPE.QUALITY] = {["name"] = StringConst.Get("SealMaterial_Substance_Sort_Text_P3")},
    [sealedConst.SORT_TYPE.WORD_NUM] = {["name"] = StringConst.Get("SealMaterial_Substance_Sort_Text_P1")},
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_XMatEquip_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_XMatEquip_Panel:InitUIData()
    self.SortSelectedIndex = sealedConst.SORT_TYPE.MARK
end

--- UI组件初始化，此处为自动生成
function Sealed_XMatEquip_Panel:InitUIComponent()
    ---@type UIComSortBtn
    self.WBP_ComSortBtnCom = self:CreateComponent(self.view.WBP_ComSortBtn, UIComSortBtn)
    ---@type UIComDropDown
    self.WBP_ComSelectDropType1Com = self:CreateComponent(self.view.WBP_ComSelectDropType1, UIComDropDown)
    ---@type Sealed_Filter_Btn
    self.WBP_Sealed_Filter_BtnCom = self:CreateComponent(self.view.WBP_Sealed_Filter_Btn, Sealed_Filter_Btn)
    ---@type UIListView childScript: ItemBoxNew
    self.TileViewCom = self:CreateComponent(self.view.TileView, UIListView)
    ---@type UIComButton
    self.WBP_ComBtn_EquipCom = self:CreateComponent(self.view.WBP_ComBtn_Equip, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_UnloadCom = self:CreateComponent(self.view.WBP_ComBtn_Unload, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function Sealed_XMatEquip_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_UnloadCom.onClickEvent, "on_WBP_ComBtn_UnloadCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_EquipCom.onClickEvent, "on_WBP_ComBtn_EquipCom_ClickEvent")
    self:AddUIEvent(self.TileViewCom.onItemSelected, "on_TileViewCom_ItemSelected")
    self:AddUIEvent(self.TileViewCom.onGetEntryLuaClass, "on_TileViewCom_GetEntryLuaClass")
    self:AddUIEvent(self.WBP_ComSelectDropType1Com.onItemSelected, "on_WBP_ComSelectDropType1Com_ItemSelected")
    self:AddUIEvent(self.WBP_ComSortBtnCom.onSortEvent, "on_WBP_ComSortBtnCom_SortEvent")
    self:AddUIEvent(self.view.Btn_Expand.OnClicked, "on_Btn_Expand_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_XMatEquip_Panel:InitUIView()
end

---面板打开的时候触发
function Sealed_XMatEquip_Panel:OnRefresh(...)
    local args = {...}
    local bFuse = args[1]
    self.bFuse = bFuse
    self.sealedId = args[2]
    self.userWidget:BP_SetFuse(bFuse)
    Game.SealedSystem.bFuseUI = bFuse
    --self.TileViewCom:SetSelectionMode(bFuse and ESelectionMode.Multi or ESelectionMode.Single)
    self:updateXMatItems()
    self:on_TileViewCom_ItemSelected(self.curSelectedIdx)
    self.WBP_ComBtn_EquipCom:SetName(StringConst.Get("SealMaterial_Assembly_Button"))
    if bFuse then
        self.WBP_ComBtn_UnloadCom:SetName(StringConst.Get("SealMaterial_Substance_List"))
        self.view.Canvas_ExpandBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.WBP_ComBtn_UnloadCom:SetName(StringConst.Get("SealMaterial_Remove_Button"))
        self.view.Canvas_ExpandBtn:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.WBP_ComSelectDropType1Com:Refresh(Sealed_XMatEquip_Panel.SortName, self.SortSelectedIndex, false)
end

function Sealed_XMatEquip_Panel:updateXMatItems()
    Game.SealedSystem:genXMatsShowData()
    if next(Game.SealedSystem.xMatShowDatas) then
        self.userWidget:BP_SetEmpty(false)
        self.TileViewCom:Refresh(Game.SealedSystem.xMatShowDatas)
        self.curSelectedIdx = self.curSelectedIdx or 1
    else
        self.userWidget:BP_SetEmpty(true)
    end
end

function Sealed_XMatEquip_Panel:onFilterUpdate()
    self:updateXMatItems()
end

function Sealed_XMatEquip_Panel:UpdateBoundRectWidgets()
    self.widget:AddPanelRegionWidget(self.view.Canvas_Right)
end

function Sealed_XMatEquip_Panel:OnXMatUpdate()
    self:OnRefresh(self.bFuse, self.sealedId)
end

--- 此处为自动生成
function Sealed_XMatEquip_Panel:on_WBP_ComBtn_UnloadCom_ClickEvent()
    if self.bFuse then
        if self.curSelectedIdx then
            local item = self.TileViewCom:GetItemByIndex(self.curSelectedIdx)
            if item then
                -- 如果当前有空位，则改为勾选态
                if Game.SealedSystem:canBeSelected(item.SlotIndex) then
                    local fuseSlotIdx = Game.SealedSystem:selectForFuse(item.SlotIndex)
                    item:SetBoxType(4)
                    Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_FUSE_SELECT_UPDATE, true, fuseSlotIdx, item.SlotIndex)
                end
            end
        end
    else
        Game.me:ReqPutOffXMat(self.sealedId)
    end
end

--- 此处为自动生成
function Sealed_XMatEquip_Panel:on_WBP_ComBtn_EquipCom_ClickEvent()
    if self.curSelectedIdx then
        local xMatData = self.TileViewCom:GetChildData(self.curSelectedIdx)
        Game.me:ReqPutOnXMat(xMatData.slotIdx, self.sealedId)
    end
end


--- 此处为自动生成
---@param index number
---@param data table
function Sealed_XMatEquip_Panel:on_TileViewCom_ItemSelected(index, data)
    if not index then
        return
    end
    self.curSelectedIdx = index
    self:updateEquipBtns()
    local item = self.TileViewCom:GetItemByIndex(index)
    if item then
        local fuseSlotIdx = Game.SealedSystem:getFuseSlot(item.SlotIndex)
        if fuseSlotIdx then
            -- 当前自己的状态如果是已选中，则本次选择为取消选中
            Game.SealedSystem:cancelFuseSelect(item.SlotIndex)
            item:SetBoxType(1)
            Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_FUSE_SELECT_UPDATE, false, fuseSlotIdx, item.SlotIndex)
        end
    end
end

function Sealed_XMatEquip_Panel:updateEquipBtns()
    self.WBP_ComBtn_UnloadCom:Hide()
    self.WBP_ComBtn_EquipCom:Hide()
    if self.bFuse then
        self.WBP_ComBtn_UnloadCom:Show()
    else
        local sealedInfo = Game.SealedSystem:getSealedInfo(self.sealedId)
        local xMatData = self.TileViewCom:GetChildData(self.curSelectedIdx)
        local xtraMatIdx = sealedInfo and sealedInfo.xtraMatIdx
        local xMat
        if xtraMatIdx then
            xMat = Game.BagSystem:GetItemInfoByIndex(itemConst.INV_TYPE_XMAT, xtraMatIdx)
        end
        if xMat and xMat.gbId == xMatData.SlotInfo.gbId then
            self.WBP_ComBtn_UnloadCom:Show()
        else
            self.WBP_ComBtn_EquipCom:Show()
        end
    end
end

function Sealed_XMatEquip_Panel:OnClose()
    Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_EQUIP_CLOSE)
end

function Sealed_XMatEquip_Panel:OnOpen()
    Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_EQUIP_OPEN)
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function Sealed_XMatEquip_Panel:on_TileViewCom_GetEntryLuaClass(index)
    return BagTree_Item
end


--- 此处为自动生成
---@param index number
---@param data UITabData
function Sealed_XMatEquip_Panel:on_WBP_ComSelectDropType1Com_ItemSelected(index, data)
    self.SortSelectedIndex = index
    Game.SealedSystem:updateSortType(index)
    Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_UPDATE)
end


--- 此处为自动生成
---@param status SortBtnStatus
function Sealed_XMatEquip_Panel:on_WBP_ComSortBtnCom_SortEvent(status)
    if status == 2 then
        Game.SealedSystem:updateSortDescend(false)
    else
        Game.SealedSystem:updateSortDescend(true)
    end
    Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_UPDATE)
end


--- 此处为自动生成
function Sealed_XMatEquip_Panel:on_Btn_Expand_Clicked()
    if self.bFuse then
        self:CloseSelf()
    end
end

return Sealed_XMatEquip_Panel
