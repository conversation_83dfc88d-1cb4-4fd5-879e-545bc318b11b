local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Sealed_Special_Sub : UIComponent
---@field view Sealed_Special_SubBlueprint
local Sealed_Special_Sub = DefineClass("Sealed_Special_Sub", UIComponent)

Sealed_Special_Sub.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Sealed_Special_Sub:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Sealed_Special_Sub:InitUIData()
end

--- UI组件初始化，此处为自动生成
function Sealed_Special_Sub:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Sealed_Special_Sub:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Sealed_Special_Sub:InitUIView()
end

---组件刷新统一入口
function Sealed_Special_Sub:Refresh(...)
end

return Sealed_Special_Sub
