local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class Letter_Arrow : UIComponent
---@field view Letter_ArrowBlueprint
local Letter_Arrow = DefineClass("Letter_Arrow", UIComponent)

Letter_Arrow.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Letter_Arrow:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Letter_Arrow:InitUIData()
	---按钮点击事件
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
	self.onClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function Letter_Arrow:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function Letter_Arrow:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Letter_Arrow:InitUIView()
end

---组件刷新统一入口
function Letter_Arrow:Refresh(...)
end


--- 此处为自动生成
function Letter_Arrow:on_Btn_ClickArea_Clicked()
	self.onClickEvent:Execute()
end

return Letter_Arrow
