local UIComMask = kg_require("Framework.KGFramework.KGUI.Component.BackGround.UIComMask")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class RussellDiary_Panel : UIPanel
---@field view RussellDiary_PanelBlueprint
local RussellDiary_Panel = DefineClass("RussellDiary_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")

RussellDiary_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RussellDiary_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RussellDiary_Panel:InitUIData()
	---@type number 笔记Id
	self.Id = nil
	---@type number
	self.currenPage = 1
	---@type number
	self.maxPage = 1
	---@type table
	self.content = nil
end

--- UI组件初始化，此处为自动生成
function RussellDiary_Panel:InitUIComponent()
    ---@type UIComButton
    self.Btn_CloseCom = self:CreateComponent(self.view.Btn_Close, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function RussellDiary_Panel:InitUIEvent()
    self:AddUIEvent(self.Btn_CloseCom.onClickEvent, "on_Btn_CloseCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_Left.OnClicked, "on_Btn_Left_Clicked")
    self:AddUIEvent(self.view.Btn_Right.OnClicked, "on_Btn_Right_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RussellDiary_Panel:InitUIView()
end

---面板打开的时候触发
function RussellDiary_Panel:OnRefresh(Id)
	self.Id = Id
	local data = Game.TableData.GetRussellDiaryDataRow(Id)
	self.content = {}

	if data then
		local index = 1
		for _, page in ksbcpairs(data) do
			self.content[index] = {
				pageId = page.Page,
				date = page.Date,
				content = page.Content,
			}
			index = index + 1
		end
	end
	table.sort(self.content, function(a, b)
		return a.pageId < b.pageId
	end)

	self.maxPage = #self.content
	self.currenPage = 1
	self:SetPage(self.currenPage)
end

function RussellDiary_Panel:SetPage(pageId)
	if pageId < 1 or pageId > self.maxPage then
		return
	end

	self.view.Text_Date:SetText(self.content[pageId].date or "")
	self.view.Text_Content:SetText(self.content[pageId].content or "")

	if pageId == 1 then
		self.view.Img_Left:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Img_Left:SetVisibility(ESlateVisibility.SelfHitTestInvisible)	
	end

	if pageId == self.maxPage then
		self.view.Img_Right:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Img_Right:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end

function RussellDiary_Panel:OnClose()
	if not _G.StoryEditor then
		Game.LetterSystem:OnLetterSystemPanelClose(self.Id, true)
	end
end

function RussellDiary_Panel:on_Btn_CloseCom_ClickEvent()
	self:CloseSelf()
end

function RussellDiary_Panel:on_Btn_Left_Clicked()
	if self.currenPage > 1 then
		self.currenPage = self.currenPage - 1
		self:SetPage(self.currenPage)
	end
end

function RussellDiary_Panel:on_Btn_Right_Clicked()
	if self.currenPage < self.maxPage then
		self.currenPage = self.currenPage + 1
		self:SetPage(self.currenPage)
	end
end

return RussellDiary_Panel
