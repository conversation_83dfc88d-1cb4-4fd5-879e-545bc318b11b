local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class RussellDiary_Panel : UIPanel
---@field view RussellDiary_PanelBlueprint
local RussellDiary_Panel = DefineClass("RussellDiary_Panel", UIPanel)

RussellDiary_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RussellDiary_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RussellDiary_Panel:InitUIData()
	---@type integer 笔记Id
	self.Id = nil
end

--- UI组件初始化，此处为自动生成
function RussellDiary_Panel:InitUIComponent()
    ---@type UIComButton
    self.Btn_CloseCom = self:CreateComponent(self.view.Btn_Close, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function RussellDiary_Panel:InitUIEvent()
    self:AddUIEvent(self.Btn_CloseCom.onClickEvent, "on_Btn_CloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RussellDiary_Panel:InitUIView()
end

---面板打开的时候触发
function RussellDiary_Panel:OnRefresh(Id)
	self.Id = Id
	local data = Game.TableData.GetRussellDiaryDataRow(Id)
	self.view.Text_Date:SetText(data.Date or "")
	self.view.Text_Content:SetText(data.Content or "")
end

function RussellDiary_Panel:OnClose()
	if not _G.StoryEditor then
		Game.LetterSystem:OnLetterSystemPanelClose(self.Id, true)
	end
end

--- 此处为自动生成
function RussellDiary_Panel:on_Btn_CloseCom_ClickEvent()
	self:CloseSelf()
end

return RussellDiary_Panel
