local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ReadSmall_Panel : UIPanel
---@field view ReadSmall_PanelBlueprint
local ReadSmall_Panel = DefineClass("ReadSmall_Panel", UIPanel)

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ReadSmall_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ReadSmall_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ReadSmall_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnTransparentCom = self:CreateComponent(self.view.WBP_ComBtnTransparent, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function ReadSmall_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnTransparentCom.onClickEvent, "on_WBP_ComBtnTransparentCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ReadSmall_Panel:InitUIView()
	--local anchor = import("Anchors")()
	--anchor.Minimum = FVector2D(0.0, 0.0)
	--anchor.Maximum = FVector2D(1.0, 1.0)
	--
	--local btnSlot = self.WBP_ComBtnTransparentCom.view.Btn_ClickArea.Slot
	--btnSlot:SetAnchors(anchor)
	--btnSlot:SetOffsets(UE.Margin(0, 0, 0, 0))
end

---面板打开的时候触发
function ReadSmall_Panel:OnRefresh(Id, callback)
	self.callback = callback
	self.Id = Id
	self.WBP_ComBtnTransparentCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.WBP_ComBtnCloseCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self:ShowContent()
	self:PlayAnimation(self.view.Ani_Fadein, function ()
		--self.WBP_ComBtnTransparentCom.userWidget:SetVisibility(ESlateVisibility.Visible)
		self.WBP_ComBtnCloseCom.userWidget:SetVisibility(ESlateVisibility.Visible)
	end, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function ReadSmall_Panel:OnClose()
	local data = Game.TableData.GetLetterTextDataRow(self.Id)
	if data and not _G.StoryEditor then
		Game.LetterSystem:OnLetterSystemPanelClose(self.Id, data.NotifyClose == 1)
	end
	
	if self.callback then
		self.callback()
		self.callback = nil
	end
end

function ReadSmall_Panel:ShowContent()
	local data = Game.TableData.GetLetterTextDataRow(self.Id)
	local ContentAlign = Game.TableData.GetLetterTextDataRow(self.Id).ContentAlign
	local TitleAlign = Game.TableData.GetLetterTextDataRow(self.Id).TitleAlign

	self.view.Text_Title:SetText(data.Title)
	if TitleAlign then
		self.view.Text_Title:SetJustification(Game.LetterSystem.TextJustify[TitleAlign]
			and Game.LetterSystem.TextJustify[TitleAlign] or Game.LetterSystem.TextJustify[1])
	end

	self.view.Text_Content:SetText(data.Content)
	if ContentAlign then
		self.view.Text_Content:SetJustification(Game.LetterSystem.TextJustify[ContentAlign] 
			and Game.LetterSystem.TextJustify[ContentAlign] or Game.LetterSystem.TextJustify[1])
	end
end

--- 此处为自动生成
function ReadSmall_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
	self:CloseSelf()
end

--- 此处为自动生成
function ReadSmall_Panel:on_WBP_ComBtnTransparentCom_ClickEvent()
	--self:CloseSelf()
end

return ReadSmall_Panel
