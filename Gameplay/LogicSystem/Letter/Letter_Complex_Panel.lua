local BookTips = kg_require("Gameplay.LogicSystem.Book.BookTips")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComMask = kg_require("Framework.KGFramework.KGUI.Component.BackGround.UIComMask")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local UWidgetLayoutLib = import("WidgetLayoutLibrary")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local KismetInputLibrary = import("KismetInputLibrary")
local Letter_Arrow = kg_require("Gameplay.LogicSystem.Letter.Letter_Arrow")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class Letter_Complex_Panel : UIPanel
---@field view Letter_Complex_PanelBlueprint
local Letter_Complex_Panel = DefineClass("Letter_Complex_Panel", UIPanel)

---底纹的显隐样式
Letter_Complex_Panel.SignatureState = {
	UpperAndLower = 0,
	UpperOnly = 1,
	LowerOnly = 2,
	None = 3,
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Letter_Complex_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function Letter_Complex_Panel:InitUIData()
	---@type table<integer, table> 每一页的数据
	self.content = nil
	---@type integer 当前页码
	self.curPage = 1
	---@type boolean 正在翻页的标记位
	self.isTurning = false
	---@type boolean 信件已经打开标记位
	self.hasOpened = false
	---@type boolean 关闭时的回调
	self.callback = nil
	---@type boolean 显示tips的标记位
	self.tipsShow = false

	---@type function 获取viewportSize
	self.getViewportSizeFunc =  UWidgetLayoutLib.GetViewportSize
	---@type function 获取viewportScale
	self.getViewportScaleFunc = UWidgetLayoutLib.GetViewportScale
	---@type function 将绝对坐标转换为viewport坐标
	self.AbsoluteToLocalFunc = SlateBlueprintLibrary.AbsoluteToViewport
end

--- UI组件初始化，此处为自动生成
function Letter_Complex_Panel:InitUIComponent()
    ---@type BookTips
    self.WBP_BookTipsCom = self:CreateComponent(self.view.WBP_BookTips, BookTips)
    ---@type UIComButton
    self.Btn_CloseCom = self:CreateComponent(self.view.Btn_Close, UIComButton)
    ---@type UIComMask
    self.WBP_ComMaskCom = self:CreateComponent(self.view.WBP_ComMask, UIComMask)
    ---@type Letter_Arrow
    self.Btn_NextCom = self:CreateComponent(self.view.Btn_Next, Letter_Arrow)
    ---@type Letter_Arrow
    self.Btn_PreviousCom = self:CreateComponent(self.view.Btn_Previous, Letter_Arrow)
end

---UI事件在这里注册，此处为自动生成
function Letter_Complex_Panel:InitUIEvent()
    self:AddUIEvent(self.view.TipCloseButton.OnClicked, "on_TipCloseButton_Clicked")
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Text_Content_1.OnRichTextLinkEvent, "on_Text_Content_1_RichTextLinkEvent")
    self:AddUIEvent(self.Btn_PreviousCom.onClickEvent, "on_Btn_PreviousCom_ClickEvent")
    self:AddUIEvent(self.Btn_NextCom.onClickEvent, "on_Btn_NextCom_ClickEvent")
    self:AddUIEvent(self.Btn_CloseCom.onClickEvent, "on_Btn_CloseCom_ClickEvent")
    self:AddUIEvent(self.view.ChangeContent, "on_WBP_Letter_Complex_Panel_ChangeContent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function Letter_Complex_Panel:InitUIView()
end

function Letter_Complex_Panel:OnRefresh(NeedOpen, Id, callback)
	self.callback = callback
	self.hasOpened = not NeedOpen
	self.curPage = 1
	self.Id = Id
	self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.TipCloseButton:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.Img_Drawing:SetVisibility(ESlateVisibility.Collapsed)
	self.isTurning = false
	self:ShowLetter()
end

--- 此处为自动生成
function Letter_Complex_Panel:on_TipCloseButton_Clicked()
	self:CheckTipsClose()
end

--- 此处为自动生成
function Letter_Complex_Panel:on_Btn_ClickArea_Clicked()
	--self:OnClickedClose()
end

---@param url FString
---@param beginIndex int
---@param endIndex int
---@param clickPosition FVector2D
--- 此处为自动生成
function Letter_Complex_Panel:on_Text_Content_1_RichTextLinkEvent(url, beginIndex, endIndex, clickPosition)
	local location = FVector2D(0, 0)
	local size = FVector2D(0, 0)
	local PixelPosition = FVector2D(0, 0)
	local ViewportPosition = FVector2D(0, 0)

	self.view.Text_Content_1:GetRunLocationAndSizeByRange(beginIndex, endIndex, location, size, true, clickPosition)
	self.AbsoluteToLocalFunc(_G.GetContextObject(), location + size/2, PixelPosition, ViewportPosition)
	local ViewportSize = self.getViewportSizeFunc(_G.GetContextObject())
	local ViewportScale = self.getViewportScaleFunc(_G.GetContextObject())
	ViewportSize = ViewportSize / ViewportScale

	local bLeft
	local bup
	if ViewportPosition.X < ViewportSize.X/2 then
		bLeft = true
	end
	if ViewportPosition.Y > ViewportSize.Y/2 then
		bup = true
	end

	self:ShowTips(true, tonumber(url), location, size, bLeft, bup)
end


--- 此处为自动生成
function Letter_Complex_Panel:on_Btn_CloseCom_ClickEvent()
	self:OnClickedClose()
end

--- 此处为自动生成
function Letter_Complex_Panel:on_Btn_PreviousCom_ClickEvent()
	self:CheckTipsClose()
	if self.isTurning == false then
		self.isTurning = true
		if self.curPage == #self.content then
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_PreviousPage_end", false)
		else
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_PreviousPage", false)
		end
		self.curPage = self.curPage - 1
		if self.curPage < 1 then
			self.curPage = 1
		end
		self:PlayAnimation(self.view.Ani_mail_previous_page, function()
			self.isTurning = false
		end, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
	end
end

--- 此处为自动生成
function Letter_Complex_Panel:on_Btn_NextCom_ClickEvent()
	self:CheckTipsClose()
	if self.isTurning == false then
		self.isTurning = true
		self.curPage = self.curPage + 1
		if self.curPage > #self.content then
			self.curPage = #self.content
		end
		if self.curPage == #self.content then
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_NextPage_end", false)
		else
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_NextPage", false)
		end
		self:PlayAnimation(self.view.Ani_mail_next_page, function()
			self.isTurning = false
		end, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
	end
end

--- 此处为自动生成
function Letter_Complex_Panel:on_WBP_Letter_Complex_Panel_ChangeContent()
	self:UpdatePage()
end

function Letter_Complex_Panel:ShowTips(bShow, id, Location, size, bLeft, bup)
	local tipWidget = self.WBP_BookTipsCom.userWidget
	local textBlock = self.WBP_BookTipsCom.view.Text_Content
	if bShow and tipWidget then
		local data = Game.TableData.GetTextTipsDataRow(id)
		local newPos = SlateBlueprintLibrary.AbsoluteToLocal(tipWidget:GetCachedGeometry(), Location)
		local oldPos = tipWidget.Slot:GetPosition()

		local targetPos = oldPos + newPos
		targetPos.X=targetPos.X+size.X/2
		targetPos.Y=targetPos.Y+size.Y
		local tipSize = textBlock:GetTextScale(data.Tips, true)
		if bLeft then
			targetPos.X=targetPos.X - tipSize.X
		end
		if bup then
			targetPos.Y=targetPos.Y - size.Y - tipSize.Y
		end
		tipWidget.Slot:SetPosition(targetPos)
		tipWidget:SetRenderOpacity(1.0)
		if data and data.bHandWriting == 1 then
			textBlock:SetDefaultRowName("Tips_Default_HW")
		else
			textBlock:SetDefaultRowName("Tips_Default")
		end
		textBlock:SetText(data.Tips)
		self.tipsShow = id
	else
		tipWidget:SetRenderOpacity(0.0)
		self.tipsShow = nil
	end
end

function Letter_Complex_Panel:CheckTipsClose()
	if self.tipsShow then
		self:ShowTips(false)
	end
end

function Letter_Complex_Panel:ShowLetter()
	---@type _LetterTextDataRow
	local letterData = Game.TableData.GetLetterTextDataRow(self.Id)
	if letterData and letterData.bHandWriting == 1 then
		self.view.Text_Title:SetDefaultRowName("Letter_Title_HW")
		self.view.Text_Sender:SetDefaultRowName("Letter_Title_HW")
		self.view.Text_Content_1:SetDefaultRowName("Letter_Normal_HW")
	else
		self.view.Text_Title:SetDefaultRowName("Letter_Title_Normal")
		self.view.Text_Sender:SetDefaultRowName("Letter_Title_Normal")
		self.view.Text_Content_1:SetDefaultRowName("Letter_Normal")
	end

	self:AutoPage(self.Id)
	self.isTurning = false
	--self.userWidget:SetType(0)
	if self.hasOpened then
		self.view.Text_Tips:SetVisibility(ESlateVisibility.Collapsed)
		self.isTurning = true
		self:ShowContent()
		if #self.content == 1 then
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_Open_Onepage", false)
		else
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_Open", false)
		end
		self:PlayAnimation(self.view.Ani_AutoOpen, function()
			self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
			self.view.TipCloseButton:SetVisibility(ESlateVisibility.Visible)
			self.isTurning = false
		end, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
	else
		self:SetPageStyle(false)
		self.isTurning = true
		self:PlayAnimation(self.view.Ani_Fadein_T, function()
			self.isTurning = false
		end, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
		Log.DebugFormat("LetterBindTouch")
		local track
		if #self.content == 1 then
			track = self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_Open_Onepage", false)
		else
			track = self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_Open", false)
		end
		track:SetAnimationEnd(0)
		self:AddUIEvent(self.view.OnTouchStartedEvent, "on_WBP_Letter_Complex_Panel_TouchStartedEvent")
		self:AddUIEvent(self.view.OnTouchEndedEvent, "on_WBP_Letter_Complex_Panel_TouchEndedEvent")
		self.userWidget:SetVisibility(ESlateVisibility.Visible)
		local tipText = Game.TableData.GetLetterTextDataRow(self.Id).InterText
		self.view.Text_Tips:SetText(tipText and tipText or Game.TableData.GetStringConstDataRow("LetterSlideOpenTips").StringValue)
		self.view.Text_Tips:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Btn_Close:SetVisibility(ESlateVisibility.Collapsed)
		self.WBP_ComMaskCom.userWidget:SetVisibility(ESlateVisibility.HitTestInvisible)
	end
end

function Letter_Complex_Panel:OnClose()
	local data = Game.TableData.GetLetterTextDataRow(self.Id)
	if data and not _G.StoryEditor then
		Game.LetterSystem:OnLetterSystemPanelClose(self.Id, data.NotifyClose == 1)
	end

	if self.callback then
		self.callback()
		self.callback = nil
	end
end

function Letter_Complex_Panel:ShowContent()
	self:UpdatePage()
	self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	Game.AkAudioManager:PostEvent2D("Play_Plot_Common_2D_PageOff")
end

function Letter_Complex_Panel:UpdatePage()
	if self.content[self.curPage] then
		--第一�        
		if self.content[self.curPage].page == 1 and self.content[self.curPage].maxPage > 1 then
			self:SetPageStyle(true, false, true)
			--中间�        
		elseif self.content[self.curPage].page > 1 and self.content[self.curPage].maxPage > self.content[self.curPage].page then
			self:SetPageStyle(true, true, true)
			--末页
		elseif self.content[self.curPage].page > 1 and self.content[self.curPage].maxPage == self.content[self.curPage].page then
			self:SetPageStyle(true, true, false)
			--仅一�        
		else
			self:SetPageStyle(true, false, false)
		end
		self.view.Text_Page:SetText(string.format("%d/%d",self.curPage, self.content[self.curPage].maxPage))
		self:SetContent(self.curPage)
	else
		Log.Warning("No Text Content!")
	end
end

--设置页面内容
function Letter_Complex_Panel:SetContent(page)
	local ContentAlign = Game.TableData.GetLetterTextDataRow(self.Id).ContentAlign
	local TitleAlign = Game.TableData.GetLetterTextDataRow(self.Id).TitleAlign
	local SenderAlign = Game.TableData.GetLetterTextDataRow(self.Id).SenderAlign
	if self.content[page].title then
		local titletext =  Game.NPCManager.GetFormatTalkText(self.content[page].title)
		self.view.Text_Title:SetText(titletext)
		if TitleAlign then
			self.view.Text_Title:SetJustification(Game.LetterSystem.TextJustify[TitleAlign] and Game.LetterSystem.TextJustify[TitleAlign] or Game.LetterSystem.TextJustify[1])
		end
	end
	if self.content[page].content then
		local contenttext = Game.NPCManager.GetFormatTalkText(self.content[page].content)
		self.view.Text_Content_1:SetText(contenttext)
		if ContentAlign then
			self.view.Text_Content_1:SetJustification(Game.LetterSystem.TextJustify[ContentAlign] and Game.LetterSystem.TextJustify[ContentAlign] or Game.LetterSystem.TextJustify[1])
		end
	end
	if self.content[page].sender then
		local sendertext = Game.NPCManager.GetFormatTalkText(self.content[page].sender)
		self.view.Text_Sender:SetText(sendertext)
		if SenderAlign then
			self.view.Text_Sender:SetJustification(Game.LetterSystem.TextJustify[SenderAlign] and Game.LetterSystem.TextJustify[SenderAlign] or Game.LetterSystem.TextJustify[3])
		end
	end
	self.view.Text_Page:SetText(string.format("%d/%d", page, #self.content))

	-- 设置落款样式
	local signatureState
	if not string.isEmpty(self.content[page].title) then
		if not string.isEmpty(self.content[page].sender) then
			signatureState = Letter_Complex_Panel.SignatureState.UpperAndLower
		else
			signatureState = Letter_Complex_Panel.SignatureState.UpperOnly
		end
	else
		if not string.isEmpty(self.content[page].sender) then
			signatureState = Letter_Complex_Panel.SignatureState.LowerOnly
		else
			signatureState = Letter_Complex_Panel.SignatureState.None
		end
	end
	self.userWidget:SetPaper(signatureState)
end

--设置页面样式
function Letter_Complex_Panel:SetPageStyle(isShow, Previous, Next)
	if isShow == true then
		self.view.Letter:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Letter_Spine:SetVisibility(ESlateVisibility.Visible)

		if Previous ~= true then
			self.Btn_PreviousCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
		else
			self.Btn_PreviousCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		end
		if Next ~= true then
			self.Btn_NextCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
		else
			self.Btn_NextCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		end
	else
		self.view.Letter:SetVisibility(ESlateVisibility.Collapsed)
		self.Btn_NextCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
		self.Btn_PreviousCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--打开信封
function Letter_Complex_Panel:OnLetterOpened()
	self.view.Text_Tips:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Btn_Close:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.WBP_ComMaskCom.userWidget:SetVisibility(ESlateVisibility.Visible)
	self.isTurning = true
	self:ShowContent()
	self:PlayAnimation(self.view.Ani_open, function()
		self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
		self.view.TipCloseButton:SetVisibility(ESlateVisibility.Visible)
		self.isTurning = false
	end, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
	self:StartTimer("spine_open", function()
		if #self.content == 1 then
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_Open_Onepage", false)	
		else
			self.view.Letter_Spine:SetAnimation(0, "Ani_Letter_Open", false)
		end
	end, 250, 1)
	self.isTurning = false
	--Remove Listener
	self:RemoveUIEvent(self.view.OnTouchStartedEvent, "on_WBP_Letter_Complex_Panel_TouchStartedEvent")
	self:RemoveUIEvent(self.view.OnTouchEndedEvent, "on_WBP_Letter_Complex_Panel_TouchEndedEvent")

	--TODO:信件打开全局事件广播
	Game.LetterSystem:LetterOpened(self.Id)
end

--关闭
function Letter_Complex_Panel:OnClickedClose()
	self.isTurning = false
	--self:RemoveUIListener(EUIEventTypes.CLICK, self.view.Btn_ClickArea)
	self:ShowTips(false)
	self:CloseSelf()
end

--- 滑动开始
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent
function Letter_Complex_Panel:on_WBP_Letter_Complex_Panel_TouchStartedEvent(myGeometry, inGestureEvent)
	local ScreenPos = KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inGestureEvent)
	ScreenPos = SlateBlueprintLibrary.AbsoluteToLocal(myGeometry, ScreenPos)
	self.StartPosition = ScreenPos
	Log.DebugFormat("LetterTouchStart")
	return UIBase.HANDLED
end

--- 滑动结束
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent
function Letter_Complex_Panel:on_WBP_Letter_Complex_Panel_TouchEndedEvent(myGeometry, inGestureEvent)
	Log.DebugFormat("LetterTouchEnd")
	if not self.isTurning then
		self:OnLetterOpened()
	end
end

function Letter_Complex_Panel:AutoPage(id)
	self.content = {}
	local data = Game.TableData.GetLetterTextDataRow(id)
	local pages = self.view.Text_Content_1:GetAutoPages(data.Content, true)
	local pageNum = pages:Num()
	if pageNum == 0 then
		local newPage = {
			content = data.Content,
			page = 1,
			maxPage = 1,
			title = data.Title,
			sender = data.Sender,
		}
		table.insert(self.content, newPage)
	else
		for pageid = 1, pageNum, 1 do
			local nowpage = pages:Get(pageid - 1)
			local newContent = utf8.sub(data.Content, nowpage.BeginIndex + 1, nowpage.EndIndex + 1)
			if nowpage.StartAdd and nowpage.StartAdd ~= "" then
				newContent = string.format("%s%s", nowpage.StartAdd, newContent)
			end
			if nowpage.EndAdd and nowpage.EndAdd ~= "" then
				newContent = string.format("%s%s", newContent, nowpage.EndAdd)
			end
			local newPage = {}
			if pageid == 1 then newPage.title = data.Title else newPage.title = "" end
			newPage.content = newContent
			if pageid == pageNum then newPage.sender = data.Sender else newPage.sender = "" end
			newPage.page = pageid
			newPage.maxPage = pageNum
			table.insert(self.content, newPage)
		end
	end
end

return Letter_Complex_Panel
