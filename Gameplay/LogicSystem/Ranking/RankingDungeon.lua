local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local StringConst = require "Data.Config.StringConst.StringConst"

local RankingItem_Dungeon = kg_require("Gameplay.LogicSystem.Ranking.RankingItem_Dungeon")

---@class RankingDungeon : UIComponent
---@field view RankingDungeonBlueprint
local RankingDungeon = DefineClass("RankingDungeon", UIComponent)

RankingDungeon.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RankingDungeon:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RankingDungeon:InitUIData()
    self.CacheRankData = {}
    self.RankItemInfo = {}
    self.RankSecTitle = ""
    self.RankTopTitles = {}
end

--- UI组件初始化，此处为自动生成
function RankingDungeon:InitUIComponent()
    ---@type UIListView
    self.ListDungeonCom = self:CreateComponent(self.view.ListDungeon, UIListView)

    ---@type UIComSimpleTabList
    self.WBP_RankTitleCom = self:CreateComponent(self.view.WBP_RankingTitle_Dungeon.HB_Content, UISimpleList)

    ---@type RankingItem_Dungeon
    self.WBP_SelfRankingItemCom = self:CreateComponent(self.view.WBP_RankingItem_Dungeon, RankingItem_Dungeon)
end

---UI事件在这里注册，此处为自动生成
function RankingDungeon:InitUIEvent()

end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RankingDungeon:InitUIView()
end

---组件刷新统一入口
function RankingDungeon:Refresh(rankId)
    self.RankId = rankId
    self.ProfessionId = -1
    Game.RankSystem:ReqRankData(rankId, 1, -1, 0, 0)
end

function RankingDungeon:UpdateRankData(rankData, pageID, rankID)
    table.clear(self.CacheRankData)
    if rankData and #rankData > 0 then
        for i = 1, math.min(Game.TableData.GetRankAllListDataRow(self.RankId).DisplayNum, #rankData) do
            table.insert(self.CacheRankData, rankData[i])
        end

        if pageID then
            local PageCount = 20
            local SelectedIndex = (pageID - 1) * PageCount + 1
            self.ListDungeonCom:Refresh(rankData, SelectedIndex, self.RankItemInfo)
        else
            self.ListDungeonCom:Refresh(rankData, 1, self.RankItemInfo)
        end
        self.WBP_SelfRankingItemCom:SetVisible(true)
        self.WBP_SelfRankingItemCom:OnRefresh(Game.RankSystem:GetSelfRankInfo(rankData.self, rankID), self.RankItemInfo, true)
    else
        self.WBP_SelfRankingItemCom:SetVisible(false)
        self.ListDungeonCom:Refresh(self.CacheRankData)
    end
    self.view.WBP_RankingTitle_Dungeon.WBP_RankingTitleText_1.TextRankInfo:setText(StringConst.Get("RANK_RANK_NO"))
    self.view.WBP_RankingTitle_Dungeon.WBP_RankingTitleText_2.TextRankInfo:setText(self.RankSecTitle)
    self.WBP_RankTitleCom:Refresh(self.RankTopTitles)
end

return RankingDungeon
