local StringConst = require "Data.Config.StringConst.StringConst"
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class RankingText : UIListItem
---@field view RankingTextBlueprint
local RankingText = DefineClass("RankingText", UIListItem)

RankingText.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RankingText:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RankingText:InitUIData()
end

--- UI组件初始化，此处为自动生成
function RankingText:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function RankingText:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RankingText:InitUIView()
end

function RankingText:OnRefresh(data)
    local content = data.content or ""
    local reverseData = Game.RankSystem.reverseData
	if data.displayType == reverseData["Profession"] then   --profession
		if Game.TableData.GetPlayerSocialDisplayDataRow(content) then
			content = Game.TableData.GetPlayerSocialDisplayDataRow(content)[0].ClassName
		end
	elseif data.displayType == reverseData["Sex"] then   --Sex
		if content == 0 then
			content = StringConst.Get("RANK_SEX_0")
		else
			content = StringConst.Get("RANK_SEX_1")
		end
	elseif data.displayType == reverseData["DungeonStageCompleteTS"] then
		if type(content) == "number" then
			content = os.date(StringConst.Get("RANKING_SPECIFIC_TIME"), math.floor(content/1000))
		end
	elseif data.displayType == reverseData["WinLoseCount"] then
		if type(content) == "table" and (#content) == 2 then
			content = string.format("%s/%s", content[1], content[2])
		else
			content = "0/0"
		end
	elseif data.displayType == reverseData["LeagueGroupInfo"] then
		if type(content) == "table" and (#content) == 2 then
			content = string.format(StringConst.Get("RANK_GUILDBATTLE_GROUP_AND_RANK"), content[1], content[2])
		else
			content = ""
		end
	end

    self.view.TextRankInfo:setText(content)
end

return RankingText
