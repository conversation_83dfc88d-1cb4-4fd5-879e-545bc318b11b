local StringConst = require "Data.Config.StringConst.StringConst"
local GuildIcon = kg_require("Gameplay.LogicSystem.Common.Tools.GuildIcon")
local SocialHead = kg_require("Gameplay.LogicSystem.Social.SocialHead")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

local ESlateVisibility = import("ESlateVisibility")
---@class RankingItem_Guild : UIListItem
---@field view RankingItem_GuildBlueprint
local RankingItem_Guild = DefineClass("RankingItem_Guild", UIListItem)

RankingItem_Guild.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RankingItem_Guild:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RankingItem_Guild:InitUIData()
    self.ClickType = nil
    self.ClickRankData = nil
end

--- UI组件初始化，此处为自动生成
function RankingItem_Guild:InitUIComponent()
    ---@type GuildIcon
    self.WBP_RankingHeadWithName_WBP_GuildIconCom = self:CreateComponent(self.view.WBP_RankingHeadWithName.WBP_GuildIcon, GuildIcon)
    ---@type SocialHead
    self.WBP_RankingHeadWithName_WBP_SocialHeadCom = self:CreateComponent(self.view.WBP_RankingHeadWithName.WBP_SocialHead, SocialHead)
    ---@type UIListView
    self.List_GuildTextCom = self:CreateComponent(self.view.HB_Content, UISimpleList)
end

---UI事件在这里注册，此处为自动生成
function RankingItem_Guild:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RankingItem_Guild:InitUIView()
    self.WBP_RankingHeadWithName_WBP_GuildIconCom:SetVisible(false)
    self:SetSelect(false)
end

---面板打开的时候触发
function RankingItem_Guild:OnRefresh(rankData, rankItemInfo, bSelf)
    local selfRankNum = rankItemInfo.selfRank
    local rankNum = rankData.rankNum
    local displayData, bpType = Game.RankSystem:ConvertRankDataToDisplay(rankData, rankItemInfo)
    -- TODO 拆出为通用
    if bpType <= 2 and not bSelf then
        self.view.WBP_RankingNumList:SetVisibility(ESlateVisibility.Hidden)
    else
        self.view.WBP_RankingNumList:SetVisibility(ESlateVisibility.Visible)
        if rankNum < 0 then
            self.view.WBP_RankingNumList.RankNum:SetVisibility(ESlateVisibility.Hidden)
            self.view.WBP_RankingNumList.RankWord:SetVisibility(ESlateVisibility.Visible)
            self.view.WBP_RankingNumList.RankWord:SetText(StringConst.Get("RANK_NOT_IN_RANK"))
        else
            self.view.WBP_RankingNumList.RankNum:SetVisibility(ESlateVisibility.Visible)
            self.view.WBP_RankingNumList.RankWord:SetVisibility(ESlateVisibility.Hidden)
            self.view.WBP_RankingNumList.rankNum:SetText(rankNum)
        end
    end
    if bSelf or (selfRankNum == rankNum and rankNum > 3) then
        bpType = 5
    end
    self.view.WBP_RankingListBg_L:Event_UI_Style(bpType)

    local headComData = {}
    for _, info in pairs(displayData) do
        local displayType = info.displayType
        if displayType == 1 then
            self.WBP_RankingHeadWithName_WBP_SocialHeadCom:SetVisible(true)
            self.view.WBP_RankingHeadWithName.KText_Name:SetText(info.content)
        elseif displayType == 11 or displayType == 20 then
            self.WBP_RankingHeadWithName_WBP_SocialHeadCom:SetVisible(false)
            self.view.WBP_RankingHeadWithName.KText_Name:SetText(info.content or "")
        elseif displayType == 2 then
            headComData.ProfessionID = info.content
        elseif displayType == 4 then
            headComData.Level = info.content
        end
    end

    if rankData[11] and rankData[26] and rankData[27] then
        self.WBP_RankingHeadWithName_WBP_GuildIconCom:SetVisible(true)
        self.WBP_RankingHeadWithName_WBP_GuildIconCom:RefreshData(rankData[11], rankData[27], rankData[26])
    else
        self.WBP_RankingHeadWithName_WBP_GuildIconCom:SetVisible(false)
    end

    local textDisplayData = {}
    for i=3, #displayData do
        table.insert(textDisplayData, displayData[i])
    end

    self.List_GuildTextCom:Refresh(textDisplayData)
    self.WBP_RankingHeadWithName_WBP_SocialHeadCom:SetData(headComData)

    local rankTableData = Game.TableData.GetRankAllListDataRow(rankData.rankID)
    if rankTableData then
        self.ClickType = rankTableData.ClickType
        self.ClickRankData = rankData
    end
    self:SetSelect(false)
end

function RankingItem_Guild:on_Btn_ClickArea_ClickEvent()
    if self.ClickType == "OpenPlayerCard" then
        local rankData = self.ClickRankData
        Game.TeamSystem:PlayerCardUIDataAsync(rankData[0])
    elseif self.ClickType == "OpenGuildOption" then
        local rankData = self.ClickRankData
        local cachedGeometry = self.view.Btn_ClickArea:GetCachedGeometry()
        local localSize = SlateBlueprintLibrary.GetLocalSize(cachedGeometry)
        local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(
            _G.GetContextObject(), cachedGeometry, localSize, nil, nil
        )
        Game.TabClose:AttachPanel(
            "ComTagBoxPanel", Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, self.view.Btn_ClickArea
        )
        Game.NewUIManager:OpenPanel(
            "ComTagBoxPanel", viewportPosition.X - 1000, viewportPosition.Y - 50,
            {
                GuildID = rankData[0],
                Name = rankData[1],
                ProfessionID = rankData[2],
                GuildName = rankData[3],
                Level = rankData[4],
                EntityID = rankData[12],
                Sex =rankData[17]
            },
            Enum.EMenuType.GuildRank
        )
    end
end

function RankingItem_Guild:SetSelect(isSelect)
    self.userWidget:Event_UI_Style(isSelect)
end

return RankingItem_Guild