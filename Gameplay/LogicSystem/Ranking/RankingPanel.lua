local UITempComFrame = kg_require("Framework.KGFramework.KGUI.Component.Panel.UITempComFrame")
local StringConst = require "Data.Config.StringConst.StringConst"
local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local UIComAccordionList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComAccordionList")
local UIAccordionList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIAccordionList")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local SharedConst = kg_require("Shared.Const")
local lume = kg_require("Shared.lualibs.lume")

local ESlateVisibility = import("ESlateVisibility")
local RankingPersonalNew = kg_require("Gameplay.LogicSystem.Ranking.RankingPersonalNew")
local RankingGuild = kg_require("Gameplay.LogicSystem.Ranking.RankingGuild")
local RankingDungeon = kg_require("Gameplay.LogicSystem.Ranking.RankingDungeon")
local RankingLoading = kg_require("Gameplay.LogicSystem.Ranking.RankingLoading")
---@class RankingPanel : UIPanel
---@field view RankingPanelBlueprint
local RankingPanel = DefineClass("RankingPanel", UIPanel)

RankingPanel.eventBindMap = {
    [_G.EEventTypes.ON_REFRESH_RANK_DATA] = "OnRefreshRankData",
    [EEventTypesV2.ON_RECV_GUILD_LEAGUE_PRE_ZONE_ID] = "OnGetLeagueInfo"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function RankingPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function RankingPanel:InitUIData()
    self.Params = nil
    self.CurCom = nil
    self.RankType2Index = {}
end

--- UI组件初始化，此处为自动生成
function RankingPanel:InitUIComponent()
    ---@type RankingPersonalNew
    self.WBP_RankingPersonalCom = self:CreateComponent(self.view.WBP_RankingPersonal, RankingPersonalNew)
    ---@type RankingGuild
    self.WBP_RankingGuildCom = self:CreateComponent(self.view.WBP_RankingGuild, RankingGuild)
    ---@type RankingDungeon
    self.WBP_RankingDungeonCom = self:CreateComponent(self.view.WBP_RankingDungeon, RankingDungeon)
    ---@type RankingLoading
    self.WBP_RankingLoadingCom = self:CreateComponent(self.view.WBP_RankingLoading, RankingLoading)
    ---@type UITempComFrame
    self.WBP_ComPanelCom = self:CreateComponent(self.view.WBP_ComPanel, UITempComFrame)
    ---@type UIComEmptyConent
    self.WBP_ComEmptyCom = self:CreateComponent(self.view.WBP_ComEmpty, UIComEmptyConent)
    ---@type UIComAccordionList
    self.WBP_ComFoldType1TabListCom = self:CreateComponent(self.view.WBP_ComFoldType1TabList, UIComAccordionList)
end

---UI事件在这里注册，此处为自动生成
function RankingPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function RankingPanel:InitUIView()
	self:AddUIEvent(self.WBP_ComFoldType1TabListCom.onItemSelected, "OnTreeListItemSelected")
    self.WBP_ComFoldType1TabListCom:SetAutoSelectFirst(true)

    self.WBP_RankingLoadingCom:SetVisible(true)
    self.WBP_RankingPersonalCom:SetVisible(false)
    self.WBP_RankingGuildCom:SetVisible(false)
    self.WBP_RankingDungeonCom:SetVisible(false) -- 规则不完备，暂不接入
    self.WBP_ComEmptyCom:SetVisible(false)

    --设置赛季信息
    local SeasonTitleComponent = kg_require("Gameplay.LogicSystem.Season.SeasonTitleComponent")
    ---@type SeasonTitleComponent
    self.SeasonTitleCom = self:CreateComponent(self.view.WBP_Ranking_SeasonTitle, SeasonTitleComponent)
    self.SeasonTitleCom:Refresh()
end

RankingPanel.Type2Com = {
    [1] = "WBP_RankingPersonalCom", -- 个人
    [2] = "WBP_RankingGuildCom",     -- 俱乐部
    [3] = "WBP_RankingPersonalCom", -- 竞技
    -- [4]  生活舞会 废弃    
    [5] = "WBP_RankingGuildCom",     -- 联赛
    [6] = "WBP_RankingGuildCom",     -- 塔罗小队 待和交互沟通
    [7] = "WBP_RankingGuildCom",   -- 副本
}
function RankingPanel:OnTreeListItemSelected(index, data, selected)
    local rankTableDataID = data.tabData.rankTableDataID
    local rankData = Game.TableData.GetRankAllListDataRow(rankTableDataID)
    local rankType = rankData.Type

    local com = self[RankingPanel.Type2Com[rankType]]
    if com then
        self.WBP_RankingLoadingCom:SetVisible(false)
        self.WBP_RankingPersonalCom:SetVisible(false)
        self.WBP_RankingGuildCom:SetVisible(false)
        self.WBP_RankingDungeonCom:SetVisible(false)
        self.WBP_ComEmptyCom:SetVisible(false)
        self.view.CP_Bg:SetVisibility(ESlateVisibility.Hidden)
        self:RefreshRankItemInfo(rankTableDataID, com)
        com:Refresh(rankTableDataID)
        self:RefreshDescTimeText(com)
        self.CurCom = com
    else
        self.WBP_RankingLoadingCom:SetVisible(false)
        self.WBP_RankingPersonalCom:SetVisible(false)
        self.WBP_RankingGuildCom:SetVisible(false)
        self.WBP_RankingDungeonCom:SetVisible(false)
        self.WBP_ComEmptyCom:SetVisible(true)
        self.CurCom = nil
    end
    self:CloseRankTips()
end

function RankingPanel:OnDisplayEntityReady(entity)
    entity:SetWeaponVisibilityOnActorComplete(false, Enum.EInVisibleReasons.RolePlayHideWeapon)
end

RankingPanel.EDisplayTag =  -- 场景模型位置
{
    ["First"] = "First",
    ["Second"] = "Second",
    ["Third"] = "Third",
}
function RankingPanel:RefreshRankModel(rankData, bSceneDisplay)
    local sceneDisplayManager = Game.SceneDisplayManager
    local EDisplayTag = RankingPanel.EDisplayTag
    local defaultSceneID = self.defaultSceneID
    if not rankData then
        sceneDisplayManager:RemoveDisplayEntityByTag(defaultSceneID, EDisplayTag.First)
        sceneDisplayManager:RemoveDisplayEntityByTag(defaultSceneID, EDisplayTag.Second)
        sceneDisplayManager:RemoveDisplayEntityByTag(defaultSceneID, EDisplayTag.Third)
    end
    local displayEntity1 = sceneDisplayManager:GetDisplayEntityByTag(defaultSceneID, EDisplayTag.First)
    if displayEntity1 and (not rankData[1] or 
        displayEntity1.FaceDataEid ~= rankData[1][0]) then
        sceneDisplayManager:RemoveDisplayEntityByTag(defaultSceneID, EDisplayTag.First)
    end

    local displayEntity2 = sceneDisplayManager:GetDisplayEntityByTag(defaultSceneID, EDisplayTag.Second)
    if displayEntity2 and (not rankData[2] or
        displayEntity2.FaceDataEid ~= rankData[2][0]) then
        sceneDisplayManager:RemoveDisplayEntityByTag(defaultSceneID, EDisplayTag.Second)
    end

    local displayEntity3 = sceneDisplayManager:GetDisplayEntityByTag(defaultSceneID, EDisplayTag.Third)
    if displayEntity3 and (not rankData[3] or
        displayEntity3.FaceDataEid ~= rankData[3][0]) then
        sceneDisplayManager:RemoveDisplayEntityByTag(defaultSceneID, EDisplayTag.Third)
    end
    if bSceneDisplay then
        if rankData and #rankData >= 1 then
			--todo: 这个是默认职业外观。后续从服务器取自定义外观数据，然后换接口CreateDisplayAvatarEntityByCustomAppearanceData
			local entity1 = sceneDisplayManager:CreateDisplayAvatarEntityByProfessionSex(
				defaultSceneID, EDisplayTag.First, rankData[1][2], rankData[1][17] or 0
			)
			entity1:SetDefaultDisplayAnim("RankPos1")
			entity1.OnActorReady:Add(self, "OnDisplayEntityReady")
			
			if #rankData >= 2 then
				local entity2 = sceneDisplayManager:CreateDisplayAvatarEntityByProfessionSex(
					defaultSceneID, EDisplayTag.Second, rankData[2][2], rankData[2][17] or 0
				)
				entity2:SetDefaultDisplayAnim("RankPos2")
				entity2.OnActorReady:Add(self, "OnDisplayEntityReady")
			end
			
			if #rankData >= 3 then
				local entity3 = sceneDisplayManager:CreateDisplayAvatarEntityByProfessionSex(
					defaultSceneID, EDisplayTag.Third, rankData[3][2], rankData[3][17] or 0
				)
				entity3:SetDefaultDisplayAnim("RankPos3")
				entity3.OnActorReady:Add(self, "OnDisplayEntityReady")
			end
        end
    end
end

function RankingPanel:RefreshRankItemInfo(rankId, com)
    local RankItemInfo = com.RankItemInfo
    local rankTableData = Game.TableData.GetRankAllListDataRow(rankId)
    table.clear(RankItemInfo)
    RankItemInfo.ExtraInfo = rankTableData.ExtraInfo
    RankItemInfo.Key = rankTableData.Key1
    RankItemInfo.SecondKey = rankTableData.Key2
	RankItemInfo.needManualAddSex = rankTableData.needManualAddSex
    if rankTableData.Key2 == 0 then
        RankItemInfo.SecondKey = nil
    end
    
    com.RankSecTitle = nil
    local rankListDisplayData = Game.TableData.GetRankListDisplayDataTable() 
    local topTitleData = {}

    local hasPvpPoints = false
    for _, v in ksbcipairs(RankItemInfo.ExtraInfo) do
        local propInfo = rankListDisplayData[v]
        local propName = propInfo and propInfo.propName
        if propName ~= "Sex" then
            if com.RankSecTitle then
                table.insert(topTitleData, propInfo.display)
            else
                com.RankSecTitle = propInfo.display
            end
        end
    end
    local keyInfo = rankListDisplayData[RankItemInfo.Key]
    if keyInfo then
        if keyInfo.propName == "PVPPoints" then
            hasPvpPoints = true
        end
        table.insert(topTitleData, keyInfo.display)
    end
    local secKeyInfo = rankListDisplayData[RankItemInfo.SecondKey]
    if secKeyInfo then
        table.insert(topTitleData, secKeyInfo.display)
    end

    if hasPvpPoints then
        table.insert(topTitleData, rankListDisplayData[Game.TableData.Get_RANK_PROP()["PVPRankName"]].display)
    end
    com.RankTopTitles = topTitleData
end


---面板打开的时候触发
function RankingPanel:OnRefresh(Params)
    self.WBP_ComPanelCom:SetTitleButtonCom(StringConst.Get("RANK_NAME"), Enum.ETipsData.RANKINGLIST)
    self.Params = Params

    Game.GuildLeagueSystem.sender:ReqSyncGuildLeaguePreZoneID()
end

--收到公会联赛信息后决定是否展示联赛排行榜
function RankingPanel:OnGetLeagueInfo()
	if Game.GuildLeagueSystem.model.preZoneID == 0 then
		self:RefreshTabInfo(nil, {5})
	else
		if Game.GuildLeagueSystem.model.groupCount == 0 then
			self:RefreshTabInfo({ 6230008, 6230009 })
		else
			self:RefreshTabInfo({ 6230010 })
		end
	end
end

function RankingPanel:RefreshTabInfo(HiddenList, HiddenType)
    local RankListData = Game.TableData.GetRankAllListDataTable()

    local treeViewData = UIAccordionList.NewTreeViewData()
    local sortData = {}

    for _, v in ksbcpairs(RankListData) do
        if (not HiddenList or not table.contains(HiddenList, v.ID)) and (not HiddenType or not table.contains(HiddenType, v.Type)) then
            table.insert(sortData, v)
            table.sort(sortData, 
                function(a, b)
                    if a.Type == b.Type then
                        return a.SubType < b.SubType
                    else
                        return a.Type < b.Type 
                    end
                end
            )
        end
    end

    local tmpData = {}
    local index = 0
    local rankType2Index = self.RankType2Index
    for _, v in ipairs(sortData) do
        local typeName = v.TypeName
        local tabType = v.Type
        if not tmpData[tabType] then
            index = index + 1
            tmpData[tabType] = true
            treeViewData:AddFirstNode(UIComAccordionList.NewTabData(typeName))
        end
        
        local twoNodeData = UIComAccordionList.NewTabData(v.SubTypeName)
        twoNodeData.rankTableDataID = v.ID
        treeViewData:AddTwoNode(index, twoNodeData)

        local indexData = rankType2Index[tabType]
        if not indexData then
            indexData = {}
            rankType2Index[tabType] = indexData
        end
        indexData[v.SubType] = index
    end

    self.WBP_ComFoldType1TabListCom:Refresh(treeViewData)
    local Params = self.Params
    local subIndex = 1
    if Params and Params.type == "PVP" then
        index = rankType2Index[3] and rankType2Index[3][subIndex]
	elseif Params and Params.type == "PVP12V12" then
        subIndex = 2
        index = rankType2Index[3] and rankType2Index[3][subIndex]
	elseif Params and Params.type == "GuildLeague" then
		if rankType2Index[5] then
			index = rankType2Index[5] and rankType2Index[5][subIndex] or rankType2Index[5][3]
		else
			index = 1
		end
	else
        index = 1
	end
    self.WBP_ComFoldType1TabListCom:SetSelectedItemByPath(true, index, subIndex)
    self.WBP_ComFoldType1TabListCom:SetExpansionItemByPath(true, index)
end

function RankingPanel:OnRefreshRankData(rankID, rankKey, pageID, rankData)
    local curCom = self.CurCom
    local curComRankId = curCom and curCom.RankId
    if not curComRankId then
        return
    end
    local modelData = Game.RankSystem.model.ClientRankData
    local RankData = (modelData[rankID] and modelData[rankID][rankKey]) or rankData

    if pageID == 1 then
        local rankTableData = Game.TableData.GetRankAllListDataRow(rankID)
        self:RefreshRankModel(RankData, rankTableData.IsSceneDisplay)
    end
    
    if not RankData or lume.count(RankData) <= 1 then
        if curCom and curCom.SwitchEmptyState then
            curCom:SwitchEmptyState(true)
        else
            self.WBP_RankingLoadingCom:SetVisible(false)
            self.WBP_RankingPersonalCom:SetVisible(false)
            self.WBP_RankingGuildCom:SetVisible(false)
            self.WBP_RankingDungeonCom:SetVisible(false)
            self.WBP_ComEmptyCom:SetVisible(true)
            self.CurCom = nil
            return
        end
    else
        if curCom and curCom.SwitchEmptyState then
            curCom:SwitchEmptyState(false)
        else
            self.WBP_ComEmptyCom:SetVisible(false)
        end
    end
    curCom:UpdateRankData(RankData, pageID, rankID)
    curCom:SetVisible(true)
end

function RankingPanel:RefreshDescTimeText(com)
    local time = Game.TimeUtils.GetNextRefreshWeek(os.time()*1000, SharedConst.REFRESH_DAY_PER_WEEK, SharedConst.REFRESH_HOUR_PER_DAY, 0, 0) / 1000
    local date = os.date("*t", time)
    local text = string.format(StringConst.Get("RANK_SETTLE_TIME"), date.year, date.month, date.day, date.hour)
    com.view.TextDescTime:setText(text)
end

function RankingPanel:CloseRankTips()
    Game.TeamSystem:HidePlayerCardUI()
    if Game.NewUIManager:CheckPanelIsOpen("ComTagBoxPanel") then
		Game.NewUIManager:ClosePanel("ComTagBoxPanel")
	end
end

function RankingPanel:OnHide()
    self:CloseRankTips()
end

return RankingPanel
