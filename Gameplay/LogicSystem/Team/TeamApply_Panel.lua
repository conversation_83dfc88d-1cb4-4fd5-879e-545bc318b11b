local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

---团队组队 申请列表
---@class TeamApply_Panel : UIPanel
---@field view TeamApply_PanelBlueprint
local TeamApply_Panel = DefineClass("TeamApply_Panel", UIPanel)

local StringConst = require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")

TeamApply_Panel.eventBindMap = {
    [EEventTypesV2.ON_SELF_TEAMAPPLICATORLIST_CHANGED] = "OnTeamApplyUpdate",
    [EEventTypesV2.ON_SELF_TEAMCOMBINELIST_CHANGED] = "OnTeamApplyUpdate",
    [EEventTypesV2.CLIENT_GROUP_APPLY_UPDATE] = "OnGroupApplyUpdate",
}


--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TeamApply_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TeamApply_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TeamApply_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIComButton
    self.WBP_IgnoreAllApplyCom = self:CreateComponent(self.view.WBP_IgnoreAllApply, UIComButton)
    ---@type UIListView
    self.ApplyListCom = self:CreateComponent(self.view.ApplyList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function TeamApply_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.WBP_IgnoreAllApplyCom.onClickEvent, "on_WBP_IgnoreAllApplyCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TeamApply_Panel:InitUIView()
end

---面板打开的时候触发
function TeamApply_Panel:OnRefresh(...)
    self:UpdateUI()
end


function TeamApply_Panel:UpdateUI()
    self.view.WBP_FirstBigText.Text_First_lua:SetText(StringConst.Get("TEAM_APPLY_FIRST"))
    self.view.WBP_FirstBigText.Text_Affix_lua:SetText(StringConst.Get("TEAM_APPLY_AFFIX"))
    local bInTeam = Game.TeamSystem:IsInTeam()
    self.WBP_IgnoreAllApplyCom:SetName(StringConst.Get("TEAM_ALLIGNORE"))
    if bInTeam then
        self:OnTeamApplyUpdate()
    else
        self:OnGroupApplyUpdate()
    end
end

function TeamApply_Panel:OnRefresh_WBP_ComList(Widget, Index, Selected)
    local bInTeam = Game.TeamSystem:IsInTeam()
    if bInTeam then
        local Data = Game.TeamSystem:GetTeamApplyList()[Index]
        if Data.Type == Game.TeamSystem.EApplyType.JoinTeam then
            Widget:Refresh(Data.Data, Enum.EElasticStripData.ReceivedJoinTeamApplication)
        else
            Widget:Refresh(Data.Data, Enum.EElasticStripData.ReceivedCombineTeamApplication)
        end
    else
        Widget:Refresh(Game.GroupSystem:GetGroupApplyList()[Index],
                Enum.EElasticStripData.ReceivedJoinGroupApplication)
    end
end

function TeamApply_Panel:RefreshListUI(bEmpty)
    if bEmpty then
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.view.WBP_ItemEmpty.Text_Content_lua:SetText(StringConst.Get("TEAM_NO_APPLY"))
        --self.view.CP_ApplyLIst:SetVisibility(ESlateVisibility.Collapsed)
        --self.WBP_IgnoreAllApplyCom:SetDisable(true)
        self.view.WBP_IgnoreAllApply:SetIsEnabled(false)
    else
        self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.Collapsed)
        --self.view.CP_ApplyLIst:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        --self.WBP_IgnoreAllApplyCom:SetDisable(false)
        self.view.WBP_IgnoreAllApply:SetIsEnabled(true)
    end
end

function TeamApply_Panel:OnTeamApplyUpdate()
    if Game.TeamSystem:HasApplication() == false then
        self:RefreshListUI(true)
    else
        self:RefreshListUI(false)
    end
    local listData = Game.TeamSystem:GetTeamApplyList()
    self.ApplyListCom:Refresh(listData)
end

function TeamApply_Panel:OnGroupApplyUpdate()
    if Game.GroupSystem:HasGroupApplication() == false then
        self:RefreshListUI(true)
        self.ApplyListCom:Clear()
    else
        self:RefreshListUI(false)
        self.ApplyListCom:Refresh(Game.GroupSystem:GetGroupApplyList())
    end
end

function TeamApply_Panel:on_WBP_IgnoreAllApplyCom_ClickEvent()
    Game.PopTipsSystem:ClearAllApplyInvite()
    local bInTeam = Game.TeamSystem:IsInTeam()
    local bInGroup = Game.TeamSystem:IsInGroup()
    if bInTeam then
        if Game.TeamSystem:HasApplication() then
            Game.me:ReqHandleJoinTeam(2, "")
        end
    elseif bInGroup then
        Game.me:RefuseAllGroupApply()
    end
    self:CloseSelf()
end

function TeamApply_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
    self:CloseSelf()
end

return TeamApply_Panel
