--------No Class Find, Create a New UIController Class Using Given Filename--------
---@class WBP_ComBtnBackNewView : WBP_ComBtnBackNew_C
---@field public WidgetRoot WBP_ComBtnBackNew_C
---@field public Btn_Back KGButton
---@field public Text_Back KGTextBlock
---@field public Btn_Info KGButton
---@field public Icon Image
---@field public Ani_Press WidgetAnimation
---@field public TitleName_lua string
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public IsBtnBackNew boolean
---@field public BndEvt__WBP_ComBtnBackNew_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public GetBrush_0 fun(self:self):SlateBrush


---@class WBP_ComPanelView : WBP_ComPanel_C
---@field public WidgetRoot WBP_ComPanel_C
---@field public NS_BackGround NamedSlot
---@field public HorizontalBox HorizontalBox
---@field public NS_TabList NamedSlot
---@field public NS_TabListLv1 NamedSlot
---@field public NS_TabListLv2 NamedSlot
---@field public NS_ComPanel NamedSlot
---@field public Money NamedSlot
---@field public WBP_ComBtnBack WBP_ComBtnBackNewView
---@field public Title Text string
---@field public TabType number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetTabList fun(self:self,TabType:number):void


---@class WBP_CharacterHeadView : WBP_CharacterHead_C
---@field public WidgetRoot WBP_CharacterHead_C
---@field public img_HeadBack Image
---@field public icon_head KGImage
---@field public icon_LT Image
---@field public img_LB Image
---@field public scale_LB ScaleBox
---@field public Text_level KGTextBlock
---@field public Btn_Head KGButton
---@field public Is TeamGroup boolean
---@field public Bg SlateBrush
---@field public Empty boolean
---@field public Is Fellow boolean
---@field public Tint SlateColor
---@field public Dead boolean
---@field public Icon Object
---@field public LT SlateBrush
---@field public Event_UI_Style fun(self:self,Team:boolean,Empty:boolean,IsFellow:boolean,Icon:KGImage,Tint:SlateColor,Dead:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetBg fun(self:self,Team:boolean):void
---@field public SetEmpty fun(self:self,Empty:boolean):void
---@field public SetHead fun(self:self,IsFellow:boolean,Tint:SlateColor):void
---@field public SetDead fun(self:self,Dead:boolean):void
---@field public SetLT fun(self:self,Member:number):void


---@class WBP_TeamDisplayItemView : WBP_TeamDisplayItem_C
---@field public WidgetRoot WBP_TeamDisplayItem_C
---@field public HB_TeammateInfo HorizontalBox
---@field public WBP_ComHead WBP_CharacterHeadView
---@field public Text_Name KGTextBlock
---@field public True SlateColor
---@field public False SlateColor
---@field public Is Mid boolean
---@field public Event_UI_Mid fun(self:self,bIsMid:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComComBoxView : WBP_ComComBox_C
---@field public WidgetRoot WBP_ComComBox_C
---@field public size_total SizeBox
---@field public Img_Arrow Image
---@field public Text_Target KGTextBlock
---@field public Button KGButton
---@field public Ani_Spread WidgetAnimation
---@field public Ani_Fewer WidgetAnimation
---@field public Pos number
---@field public ContentHeight number
---@field public ContentScrollCountCondition number
---@field public Text Color SlateColor
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetTitle fun(self:self,Title:string):void


---@class WBP_ComCheckBoxView : WBP_ComCheckBox_C
---@field public WidgetRoot WBP_ComCheckBox_C
---@field public CheckBox KGCheckBox
---@field public TB_Name KGTextBlock
---@field public Ani_Press WidgetAnimation
---@field public TextValue string
---@field public TextColor SlateColor
---@field public Undetermined boolean
---@field public Has Text boolean
---@field public SetUndetermined fun(self:self,Undetermined:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComBtnView : WBP_ComBtn_C
---@field public WidgetRoot WBP_ComBtn_C
---@field public OutOverlay CanvasPanel
---@field public Text_Com KGTextBlock
---@field public Text_Time KGTextBlock
---@field public Image KGImage
---@field public Btn_Com KGButton
---@field public Ani_Press WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein_normal WidgetAnimation
---@field public Ani_Fadein_Light WidgetAnimation
---@field public Ani_Fadein_blue WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public IsLight boolean
---@field public BtnType E_ComBtnType
---@field public IsDisabled boolean
---@field public IsPlayVx boolean
---@field public SequenceEvent fun(self:self):void
---@field public Construct fun(self:self):void
---@field public OnVisibilityChangedEvent fun(self:self,InVisibility:ESlateVisibility):void
---@field public BndEvt__WBP_ComBtn_Btn_Com_lua_K2Node_ComponentBoundEvent_1_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public SetDisabled fun(self:self,bIsDisabled:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetType fun(self:self):void
---@field public SetPlayVx fun(self:self,IsPlay:boolean):void


---@class WBP_TeamInviteBtnView : WBP_TeamInviteBtn_C
---@field public WidgetRoot WBP_TeamInviteBtn_C
---@field public Big_Button_ClickArea KGButton


---@class WBP_ComBtnIconNewView : WBP_ComBtnIconNew_C
---@field public WidgetRoot WBP_ComBtnIconNew_C
---@field public OutCanvas CanvasPanel
---@field public Icon Image
---@field public Text_Name TextBlock
---@field public Big_Button_ClickArea KGButton
---@field public Anim_1 WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public Ani_Hover WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public Btn Style ST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound SlateSound
---@field public Top number
---@field public Event_UI_Style fun(self:self,BtnName:string):void
---@field public Play Hint Anim fun(self:self):void
---@field public BndEvt__WBP_ComBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Btn Style fun(self:self,Btn Style:ST_ComBtnIcon):void
---@field public SetVxSize fun(self:self):void


---@class WBP_TeamToolsView : WBP_TeamTools_C
---@field public WidgetRoot WBP_TeamTools_C
---@field public WBP_QuickTeamUp WBP_ComBtnIconNewView
---@field public WBP_Recruit WBP_ComBtnIconNewView
---@field public WBP_TurnToGroup WBP_ComBtnIconNewView
---@field public Event_UI_IsFilter fun(self:self,Condition:boolean):void


---@class WBP_TeamDisplayView : WBP_TeamDisplay_C
---@field public WidgetRoot WBP_TeamDisplay_C
---@field public NS_BackGround WBP_ComPanelView
---@field public CP_ModelInfo CanvasPanel
---@field public WBP_TeamDisplayItemFrist WBP_TeamDisplayItemView
---@field public WBP_TeamDisplayItemFifth WBP_TeamDisplayItemView
---@field public WBP_TeamDisplayItemSecond WBP_TeamDisplayItemView
---@field public WBP_TeamDisplayItemThird WBP_TeamDisplayItemView
---@field public WBP_TeamDisplayItemFourth WBP_TeamDisplayItemView
---@field public WBP_ObjectFilter WBP_ComComBoxView
---@field public WBP_AutoAcceptApply WBP_ComCheckBoxView
---@field public Pnl_QuitTeamGroup CanvasPanel
---@field public Text_QuitTeamGroup TextBlock
---@field public Btn_QuitTeamGroup KGButton
---@field public WBP_ApplyCaptain WBP_ComBtnView
---@field public HB_IsCaptain2 HorizontalBox
---@field public WBP_Match WBP_ComBtnView
---@field public WBP_GoTarget WBP_ComBtnView
---@field public WBP_Invite WBP_TeamInviteBtnView
---@field public HB_IsCaptain1 HorizontalBox
---@field public WBP_ApplyList WBP_ComBtnIconNewView
---@field public WBP_TeamTools WBP_TeamToolsView
---@field public Ani_New WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public bIsFull boolean
---@field public Event_UI_TeamFull fun(self:self,bIsFull:boolean):void


---@class P_TeamDisplayView : WBP_TeamDisplayView
---@field public controller P_TeamDisplay
local P_TeamDisplayView = DefineClass("P_TeamDisplayView", UIView)

function P_TeamDisplayView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.DragDetected, self.WidgetRoot, "OnDragDetected")
    controller:AddUIListener(EUIEventTypes.TouchStarted, self.WidgetRoot, "OnTouchStarted")
    controller:AddUIListener(EUIEventTypes.TouchMoved, self.WidgetRoot, "OnTouchMoved")
    controller:AddUIListener(EUIEventTypes.TouchEnded, self.WidgetRoot, "OnTouchEnded")
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_QuitTeamGroup, "OnClick_Btn_QuitTeamGroup")
    controller:AddUIListener(EUIEventTypes.CLICK, self.NS_BackGround.WBP_ComBtnBack.Btn_Back, "OnClick_NS_BackGround_WBP_ComBtnBack_Btn_Back")
    controller:AddUIListener(EUIEventTypes.CLICK, self.NS_BackGround.WBP_ComBtnBack.Btn_Info, "OnClick_NS_BackGround_WBP_ComBtnBack_Btn_Info")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ObjectFilter.Button, "OnClick_WBP_ObjectFilter_Button")
    controller:AddUIListener(EUIEventTypes.CheckStateChanged, self.WBP_AutoAcceptApply.CheckBox, "OnCheckStateChanged_WBP_AutoAcceptApply_CheckBox")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ApplyCaptain.Btn_Com, "OnClick_WBP_ApplyCaptain_Btn_Com")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_Match.Btn_Com, "OnClick_WBP_Match_Btn_Com")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_GoTarget.Btn_Com, "OnClick_WBP_GoTarget_Btn_Com")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_Invite.Big_Button_ClickArea, "OnClick_WBP_Invite_Big_Button_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ApplyList.Big_Button_ClickArea, "OnClick_WBP_ApplyList_Big_Button_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_TeamTools.WBP_QuickTeamUp.Big_Button_ClickArea, "OnClick_WBP_TeamTools_WBP_QuickTeamUp_Big_Button_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_TeamTools.WBP_Recruit.Big_Button_ClickArea, "OnClick_WBP_TeamTools_WBP_Recruit_Big_Button_ClickArea")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_TeamTools.WBP_TurnToGroup.Big_Button_ClickArea, "OnClick_WBP_TeamTools_WBP_TurnToGroup_Big_Button_ClickArea")

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {{self.WidgetRoot,1.000017}, {self.WBP_ApplyCaptain_lua.WidgetRoot, 3.066683},{self.WBP_Match_lua.WidgetRoot, 3.066683},{self.WBP_GoTarget_lua.WidgetRoot, 3.066683},{self.WBP_ApplyList_lua.WidgetRoot, 1.866683},},AnimFadeOut = {}}
end

function P_TeamDisplayView:OnDestroy()
end

return P_TeamDisplayView
