local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TeamDisplayItem : UIListItem
---@field view TeamDisplayItemBlueprint
local TeamDisplayItem = DefineClass("TeamDisplayItem", UIListItem)
local CharacterHead = kg_require("Gameplay.LogicSystem.Character.CharacterHead")
local ESlateVisibility = import("ESlateVisibility")

TeamDisplayItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TeamDisplayItem:OnCreate()
    self:InitUIEvent()
    self:InitUIView()
end

---UI事件在这里注册，此处为自动生成
function TeamDisplayItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TeamDisplayItem:InitUIView()
    ---@type CharacterHead
    self.Head = self:CreateComponent(self.view.WBP_ComHead, CharacterHead)
end

function TeamDisplayItem:OnSetData(charInfo)
    self.eid = charInfo.EntityID
    self.view.Text_Name:SetText(charInfo.Name)
    self.Head:Refresh(charInfo)
    if self.eid == Game.me.eid then
        self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Hidden)
    else
        self.view.Btn_ClickArea:SetVisibility(ESlateVisibility.Visible)
    end
end

--- 此处为自动生成
function TeamDisplayItem:on_Btn_ClickArea_Clicked()
    Game.TeamSystem:PlayerCardUIDataAsync(self.eid, false,
            Enum.EFriendAddSourceData.TEAM, nil, Enum.EMenuType.TeamDisplay)
end

return TeamDisplayItem
