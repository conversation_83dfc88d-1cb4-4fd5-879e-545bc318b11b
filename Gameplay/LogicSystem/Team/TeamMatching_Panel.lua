--------No Class Find, Create a New UIController Class Using Given Filename--------
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")


---@class TeamMatching_Panel : UIPanel
---@field public View WBP_TeamMatching
local TeamMatching_Panel = DefineClass("TeamMatching_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local const = kg_require("Shared.Const")

TeamMatching_Panel.eventBindMap = {
    [EEventTypesV2.ON_SELF_CAPTAIN_CHANGED] = "OnCaptainStateChange",
    [EEventTypesV2.ON_TEAMMATCH_CHANGED] = "OnTeamMatchChange",
    [EEventTypesV2.ON_SELF_MATCHLIST_CHANGED] = "OnSingleMatchChange",
    [EEventTypesV2.ON_TEAMID_CHANGED] = "OnTeamIDChange",
    [EEventTypesV2.ON_MAINPLAYER_GROUP_ID_CHANGED] = "OnGroupIDChange",
}

function TeamMatching_Panel:OnCreate()
    self:AddUIEvent(self.view.Btn_Back.OnClicked, "OnClick_WBP_CancelMatch_Btn_Com")
end

function TeamMatching_Panel:OnRefresh(Params)
    self.Pos = Params
    self.userWidget:SetRenderOpacity(0)
    self.bInTeam = Game.TeamSystem:IsInTeam()
    self.bInGroup = Game.TeamSystem:IsInGroup()
    local IsInTeamMatch = GetMainPlayerPropertySafely("isInTeamMatch") == 1 and true or false
    local bIsInSingleMatch = GetMainPlayerPropertySafely("isInSingleMatch") == 1 and true or false
    if IsInTeamMatch then
        self.TargetID = const.DEFAULT_ALL_TARGET_ID
        if self.bInTeam then
            self.TargetID = GetMainPlayerPropertySafely("teamTargetID")
        elseif self.bInGroup then
            --团队
            local Details = Game.GroupSystem:GetGroupDetails()
            if Details.targetID and Details.targetID ~= 0 then
                self.TargetID = Details.targetID
            end
        end
        if (self.bInTeam and Game.TeamSystem:IsCaptain()) or (self.bInGroup and Game.GroupSystem:IsGroupLeader()) then
            self.view.CP_Cancel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        else
            self.view.CP_Cancel:SetVisibility(ESlateVisibility.Collapsed) 
        end
    elseif bIsInSingleMatch then
        self.TargetID = Game.me.singleMatchInfoList[1].singleTargetID
        self.view.CP_Cancel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    local TargetTableData = Game.TableData.GetTargetDataRow(self.TargetID)
    if TargetTableData then
        self.view.Text_Name:SetText(TargetTableData.Name)
    end
    self:StartTimer(
        "Adaptor_Pos",
        function()
            self:AdpatorPos()
            self.userWidget:SetRenderOpacity(1)
        end, 50, 1)
end

function TeamMatching_Panel:AdpatorPos()
    self.Size = import("SlateBlueprintLibrary").GetLocalSize(self.view.Overlay_Pos:GetCachedGeometry()) 
    self.Pos.X = self.Pos.X - self.Size.X + 25
    self.Pos.Y = self.Pos.Y + self.Size.Y - 90
    self.view.Overlay_Pos.slot:SetPosition(self.Pos)
end

function TeamMatching_Panel:OnClick_WBP_CancelMatch_Btn_Com()
    Game.TeamSystem:ReqTeamMatch({ self.TargetID }, 1)
    self:CloseSelf()
end

function TeamMatching_Panel:OnCaptainStateChange()
    self:OnRefresh()
end

function TeamMatching_Panel:OnTeamMatchChange()
    self:CloseSelf()
end

function TeamMatching_Panel:OnTeamIDChange()
    self:CloseSelf()
end

function TeamMatching_Panel:OnGroupIDChange()
    self:CloseSelf()
end

return TeamMatching_Panel
