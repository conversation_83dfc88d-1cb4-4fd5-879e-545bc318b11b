local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComInputBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComInputBox")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIComCheckBox = kg_require("Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class TeamGroupShout_Panel : UIPanel
local TeamGroupShout_Panel = DefineClass("TeamGroupShout_Panel", UIPanel)

local StringConst = require "Data.Config.StringConst.StringConst"
local const = kg_require("Shared.Const")
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")

TeamGroupShout_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TeamGroupShout_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TeamGroupShout_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TeamGroupShout_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtn_RecruitCom = self:CreateComponent(self.view.WBP_ComBtn_Recruit, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_GuildCom = self:CreateComponent(self.view.WBP_ComBtn_Guild, UIComButton)
    ---@type UIComInputBox
    self.WBP_ComInputCom = self:CreateComponent(self.view.WBP_ComInput, UIComInputBox)
    ---@type UIComBoxFrame
    self.WBP_ComPopupMCom = self:CreateComponent(self.view.WBP_ComPopupM, UIComBoxFrame)
    ---@type UIComCheckBox
    self.WBP_ComCheckBox_3Com = self:CreateComponent(self.view.WBP_ComCheckBox_3, UIComCheckBox)
    ---@type UIComCheckBox
    self.WBP_ComCheckBox_2Com = self:CreateComponent(self.view.WBP_ComCheckBox_2, UIComCheckBox)
    ---@type UIComCheckBox
    self.WBP_ComCheckBox_1Com = self:CreateComponent(self.view.WBP_ComCheckBox_1, UIComCheckBox)
end

---UI事件在这里注册，此处为自动生成
function TeamGroupShout_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_RecruitCom.onClickEvent, "OnClick_WBP_ComBtn_Recruit_Btn_Com")
    self:AddUIEvent(self.WBP_ComBtn_GuildCom.onClickEvent, "OnClick_WBP_ComBtn_Guild_Btn_Com")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TeamGroupShout_Panel:InitUIView()

    self:AddUIEvent(self.view.WBP_Dialogueinput.HistoryBtn_lua.OnClicked, "OnClick_WBP_Dialogueinput_HistoryBtn")

    self.Type = nil
    self.Text = ""
    self:AddUIEvent(self.WBP_ComInputCom.onTextChanged, "OnTextChanged")

    ---@type table<number, UIComCheckBox>
    self.TagCheckBoxList = {
        self.WBP_ComCheckBox_1Com,
        self.WBP_ComCheckBox_2Com,
        self.WBP_ComCheckBox_3Com,
    }
    
    --self.WBP_ComCheckBox_1Com:SetName(StringConst.Get("CHAT_TEAMRECRUIT_ATTACK"))
    --self.WBP_ComCheckBox_2Com:SetName(StringConst.Get("CHAT_TEAMRECRUIT_DEFEND"))
    --self.WBP_ComCheckBox_3Com:SetName(StringConst.Get("CHAT_TEAMRECRUIT_CURE"))
    
    for i = 1,3 do
        self.TagCheckBoxList[i]:SetName(string.match(Game.TeamSystem.model.ShoutTagName[i], "<.->(.-)</>"))
        local index = i
        self:AddUIEvent(self.TagCheckBoxList[i].onCheckChanged, "OnCheckBoxStateChanged" .. tostring(i), index)
    end
    self:OnTextChanged()
    self:FakeTextChange()
end

function TeamGroupShout_Panel:OnCheckBoxStateChanged1(isChecked, index)
    Game.TeamSystem.model.ShoutTagInfoList[index] = isChecked
    self:FakeTextChange()
end

function TeamGroupShout_Panel:OnCheckBoxStateChanged2(isChecked, index)
    Game.TeamSystem.model.ShoutTagInfoList[index] = isChecked
    self:FakeTextChange()
end

function TeamGroupShout_Panel:OnCheckBoxStateChanged3(isChecked, index)
    Game.TeamSystem.model.ShoutTagInfoList[index] = isChecked
    self:FakeTextChange()
end

function TeamGroupShout_Panel:OnRefresh()
    self.bRescue =  Game.NetworkManager.GetLocalSpace() ~= nil and Game.NetworkManager.GetLocalSpace().bCanSeekRescue or false
    if self.bRescue then
        -- 求援
        self.WBP_ComPopupMCom:Refresh(StringConst.Get("TEAM_RESCUE"))
    else
        self.WBP_ComPopupMCom:Refresh(StringConst.Get("GROUP_SHOUT"))
    end
    
    self.WBP_ComBtn_RecruitCom:SetName(StringConst.Get("INVITE_SEND_TO_RECRUIT"))
    self.WBP_ComBtn_GuildCom:SetName(StringConst.Get("INVITE_SEND_TO_GUILD"))
    local TeamTime = Game.ChatSystem:GetChatChannelExpirationTime(Enum.EChatChannelData["RECRUIT"])//1000
    if TeamTime > 0 then
        --申请CD中
        self.WBP_ComBtn_RecruitCom:SetDisable(true)
        self:StartTimer(
                "TeamRescuetimer",
                function()
                    if TeamTime <= 0 then
                        self.WBP_ComBtn_RecruitCom:SetDisable(false)
                        self.WBP_ComBtn_RecruitCom:SetName(StringConst.Get("INVITE_SEND_TO_RECRUIT"))
                        self:StopTimer("TeamRescuetimer")
                        return
                    else
                        self.WBP_ComBtn_RecruitCom:SetName(StringConst.Get("INVITE_SEND_TO_RECRUIT")..math.floor(TeamTime))
                    end
                    TeamTime = TeamTime - 1
                end,
                1000,
                -1,
                nil,
                true
        )
    else
        self.WBP_ComBtn_RecruitCom:SetDisable(false)
    end
    local GuildTime = Game.ChatSystem:GetChatChannelExpirationTime(Enum.EChatChannelData["GUILD"])//1000
    if GuildTime > 0 then
        --申请CD中
        self.WBP_ComBtn_GuildCom:SetDisable(true)
        self:StartTimer(
                "GuildRescuetimer",
                function()
                    if GuildTime <= 0 then
                        self.WBP_ComBtn_GuildCom:SetDisable(false)
                        self.WBP_ComBtn_GuildCom:SetName(StringConst.Get("INVITE_SEND_TO_GUILD"))
                        self:StopTimer("GuildRescuetimer")
                        return
                    else
                        self.WBP_ComBtn_GuildCom:SetName(StringConst.Get("INVITE_SEND_TO_GUILD")..math.floor(GuildTime))
                    end
                    GuildTime = GuildTime - 1
                end,
                1000,
                -1,
                nil,
                true
        )
    else
        self.WBP_ComBtn_GuildCom:SetDisable(false)
    end
    self.Text = StringConst.Get("GROUTeamGroupShout_Panel_DEFAULT_MESSAGE")
    self.WBP_ComInputCom:SetText(self.Text)
    self.WBP_ComInputCom:SetHintText(StringConst.Get("GROUP_HISTORT_MESSAGE"))

    local DefaultText = ""
    --self.WBP_ComInputCom:SetText(DefaultText)
    if self.bRescue then
        --求援
        for i = 1,3 do
            Game.TeamSystem.model.ShoutTagInfoList[i] = false
            self.TagCheckBoxList[i]:SetChecked(false)
        end
        DefaultText = StringConst.Get("TEAM_RESCUE_DEFAULT")
        local PositionNeedMap = {}
        if Game.me.teamTargetID and Game.me.teamTargetID ~= const.DEFAULT_NO_TARGET_ID then
            local TargetTable = Game.TableData.GetTargetDataRow(Game.me.teamTargetID)
            if TargetTable then
                local ProfessionNeed = TargetTable.ProfessionNeed
                if ProfessionNeed then
                    for key, value in ksbcipairs(ProfessionNeed) do
                        if PositionNeedMap[value] == nil then
                            PositionNeedMap[value] = 0
                        end
                        PositionNeedMap[value] = PositionNeedMap[value] + 1
                    end
                end
            end
        end
        local CurTeamPosMap = {}
        for key, value in pairs(Game.me.teamInfoList) do
            local Pos = Game.TableData.GetPlayerSocialDisplayDataRow(value.profession)[value.sex].PositionType[1]
            if CurTeamPosMap[Pos] == nil then
                CurTeamPosMap[Pos] = 0
            end
            CurTeamPosMap[Pos] = CurTeamPosMap[Pos] + 1
        end
        local Res = {}
        if next(PositionNeedMap) then
            for key, value in pairs(PositionNeedMap) do
                if CurTeamPosMap[key] == nil or CurTeamPosMap[key] < value then
                    table.insert(Res, key)
                end
            end
        end
        if #Res > 0 then
            for key, value in ipairs(Res) do
                self.TagCheckBoxList[value + 1]:SetChecked(true)
                Game.TeamSystem.model.ShoutTagInfoList[value + 1] = self.TagCheckBoxList[value + 1]:GetIsChecked()
            end
        end
        self:FakeTextChange()
    else
        for index,value in ipairs(self.TagCheckBoxList) do
            value:SetChecked(Game.TeamSystem.model.ShoutTagInfoList[index])
        end
    end

    self.WBP_ComInputCom:SetLimitLength(30)
    self.WBP_ComInputCom:SetText(DefaultText)
end

function TeamGroupShout_Panel:OnHistoryTextChange(text)
    self.WBP_ComInputCom:SetText(text)
    self:OnTextChanged()
end

function TeamGroupShout_Panel:OnTextChanged()
    local text = self.WBP_ComInputCom:GetText()
    local len = utf8.len(text)
    self.view.Text_RemainNum:SetText(string.format(StringConst.Get("GROUP_LEFT_COUNT"), 30 - len))
end

function TeamGroupShout_Panel:FakeTextChange()
    local text = self.WBP_ComInputCom:GetText()
    if not text or text == "" then
        text = StringConst.Get("GROUTeamGroupShout_Panel_HINT")
    else
        if string.find(text,"<.*>") then
            text = string.gsub(text, ">", "<")
        end
    end
    self.view.RichTextBlock:SetText("<NormalShout>" .. text .. "</>" .. Game.TeamSystem:GetShoutInfoOutput(Game.TeamSystem.model.ShoutTagInfoList))
end

function TeamGroupShout_Panel:OnClick_WBP_Dialogueinput_HistoryBtn()
    local _, ViewportPosition =
    import("SlateBlueprintLibrary").LocalToViewport(
            _G.GetContextObject(),
            self.view.WBP_Dialogueinput.HistoryBtn_lua:GetCachedGeometry(),
            FVector2D(1, 0),
            nil,
            nil
    )
    Game.NewUIManager:OpenPanel("TeamDialogueHistory", ViewportPosition)
end

function TeamGroupShout_Panel:SetShoutText(Text)
    self.WBP_ComInputCom:SetText(Text)
    self:OnTextChanged()
end

function TeamGroupShout_Panel:OnClick_WBP_ComBtn_Recruit_Btn_Com()
    local Text = self.WBP_ComInputCom:GetText()
    if ChatUtils.CheckInjectInput(Text) or string.match(Text,"</>.*<.*>") ~= nil then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED)  --luacheck: ignore
        return
    end
    Game.AllInSdkManager:IsSensitiveWords(Text, function(bSensitive)
        Game.TeamSystem:IntoShoutProcess(bSensitive, Enum.EChatChannelData["RECRUIT"], Text)
    end)
    self:CloseSelf()
end

function TeamGroupShout_Panel:OnClick_WBP_ComBtn_Guild_Btn_Com()
    if not Game.GuildSystem:HasJoinGuild() then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_HAVE_NOT_YET) -- luacheck: ignore
        return
    end
    local Text = self.WBP_ComInputCom:GetText()
    if ChatUtils.CheckInjectInput(Text) or string.match(Text,"</>.*<.*>") ~= nil then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED)  --luacheck: ignore
        return
    end
    Game.AllInSdkManager:IsSensitiveWords(Text, function(bSensitive)
        Game.TeamSystem:IntoShoutProcess(bSensitive, Enum.EChatChannelData["GUILD"], Text)
    end)
    self:CloseSelf()
end

function TeamGroupShout_Panel:OnClick_WBP_ComPopupM_WBP_ComPopupTitle_WBP_ComBtnClose_Button()
    self:CloseSelf()
end

function TeamGroupShout_Panel:OnTextCommitted_WBP_Dialogueinput_EditText()

end

function TeamGroupShout_Panel:UpdateBoundRectWidgets()
    self.widget:AddPanelRegionWidget(self.view.WBP_ComPopupM.WidgetRoot)
end

return TeamGroupShout_Panel
