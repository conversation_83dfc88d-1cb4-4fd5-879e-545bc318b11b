local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TeamChatHistoryITem : UIListItem
---@field view TeamChatHistoryITemBlueprint
local TeamChatHistoryITem = DefineClass("TeamChatHistoryITem", UIListItem)

TeamChatHistoryITem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TeamChatHistoryITem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TeamChatHistoryITem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TeamChatHistoryITem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TeamChatHistoryITem:InitUIEvent()
    self:AddUIEvent(self.view.Button_lua.OnClicked, "on_Button_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TeamChatHistoryITem:InitUIView()
end

---面板打开的时候触发
function TeamChatHistoryITem:OnRefresh(Data)
    self.Data = Data
    self.view.Text_Content_lua:SetText(Data)
end


--- 此处为自动生成
function TeamChatHistoryITem:on_Button_lua_Clicked()
    Game.EventSystem:Publish(_G.EEventTypes.TEAM_SHOUT_TEXT_CHANGED, self.Data)
    Game.NewUIManager:ClosePanel("TeamDialogueHistory")
end

return TeamChatHistoryITem
