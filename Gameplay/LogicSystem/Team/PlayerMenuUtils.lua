---@class PlayerMenuUtils
local PlayerMenuUtils = DefineClass("PlayerMenuUtils")
local const = kg_require("Shared.Const")
local SharedWorldConst = kg_require("Shared.Const.WorldConst")
local HomeConst = kg_require("Shared.Const.HomeConst")
local bit = kg_require("Framework.Utils.bit")
local bit_band = bit.band
local bit_lshift = bit.lshift

function PlayerMenuUtils:ctor(targetInfo, menuId)
    self.PlayerInfo = Game.me
    self.TargetInfo = targetInfo
    self.MenuId = menuId or 1
    self.sourceID = targetInfo.sourceID or Enum.EFriendAddSourceData.DECISIVE_ARENA
    self.MenuRow = Game.TableData.GetPlayerInteractMenuInfoDataRow(self.MenuId)
    self.ButtonMap = {}
    self.FriendInfo = Game.FriendSystem:GetRelationInfoByEntityID(targetInfo.EntityID)
    self.bInBlackList = false
    if self.FriendInfo and self.FriendInfo.groupId == const.FRIEND_SERVER_BLACK_GROUP_ID then
        self.bInBlackList = true
    end
end

function PlayerMenuUtils:GetMenuList()
    for k, v in ksbcpairs(self.MenuRow) do
        if v == 1 then
            local buttonRow = Game.TableData.GetPlayerInteractUnitDataRow(k)
            if buttonRow and self:CheckButtonMeet(buttonRow) then
                local UnitState = true
                if k == "MuteVoice" then
                    UnitState = not Game.VoiceSystem:CheckIsInBlackList(self.TargetInfo.EntityID,Enum.EVOICE_CHANNEL.WORLD)
                elseif k == "BlockPlayer" then
                    UnitState = not self.bInBlackList
                end
                table.insert(self.ButtonMap, { k, UnitState })
            end
        end
    end
    table.sort(self.ButtonMap, function(a, b)
        local buttonRowa = Game.TableData.GetPlayerInteractUnitDataRow(a[1])
        local buttonRowb = Game.TableData.GetPlayerInteractUnitDataRow(b[1])
        return buttonRowa.Rank < buttonRowb.Rank
    end)
    return self.ButtonMap
end

function PlayerMenuUtils:CheckButtonMeet(buttonRow)
    local SceneTypeBlackList = buttonRow["SceneTypeBlackList"]
    if SceneTypeBlackList ~= 0 then
        local levelid = Game.LevelManager.GetCurrentLevelID()
        if SceneTypeBlackList == levelid then
            return false
        end
    end
    local CheckAndOr = buttonRow["TargetLimitRelation"] == 1 or buttonRow["TargetLimitRelation"] == nil
    local meetCondition = CheckAndOr
    for k, v in ksbcpairs(buttonRow) do
        local bMeet = CheckAndOr
        if k == "OpenLv" then
            if not self:CheckLv(v) then
                return false
            end
        elseif k ~= "Rank" and k ~= "Name" and k ~= "SceneTypeBlackList" and self[k] ~= nil then
            if v == 1 then
                bMeet = self[k](self)
            end
            if v == 2 then
                bMeet = not self[k](self)
            end
        end
        if CheckAndOr then
            if not bMeet then
                return false
            end
        else
            if bMeet then
                return true
            end
        end
    end
    return meetCondition
end

function PlayerMenuUtils:CheckLv(level)
    return Game.me.Level >= level
end

function PlayerMenuUtils:HasGuild()
    return Game.GuildSystem:HasJoinGuild()
end

function PlayerMenuUtils:HasTeam()
    return Game.TeamSystem:IsInTeam()
end

function PlayerMenuUtils:IsTeamLeader()
    if Game.TeamSystem:IsInTeam() then
        return Game.TeamSystem:IsCaptain()
    elseif Game.TeamSystem:IsInGroup() then
        return Game.GroupSystem:IsTeamLeader()
    end
    return false
end

function PlayerMenuUtils:IsTeamGroupLeader()
    return Game.GroupSystem:IsGroupLeader(Game.me.eid)
end

function PlayerMenuUtils:IsTeamGroupAdministrator()
    return Game.GroupSystem:IsGroupManager(Game.me.eid)
end

function PlayerMenuUtils:HasTeamGroup()
    return Game.TeamSystem:IsInGroup()
end

function PlayerMenuUtils:IsCanHandleEnterGuildApply()
    return Game.GuildSystem:HasRight(const.GUILD_RIGHT.MEMBER)
end

function PlayerMenuUtils:IsGuildChairMan()
    return Game.GuildSystem:IsGuildLeader()
end

function PlayerMenuUtils:IsCanKickGuildPlayer()
    return Game.GuildSystem:HasRight(const.GUILD_RIGHT.KICKOUT)
end

function PlayerMenuUtils:TargetIsFriend()
    return Game.FriendSystem:CheckIsBothWayFriend(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TargetHasGuild()
    return self.TargetInfo.GuildID ~= "" and self.TargetInfo.GuildID ~= nil
end

function PlayerMenuUtils:TargetGuildIsInResponse()
    return Game.GuildSystem:CheckIsInGuildResponse(self.TargetInfo.GuildID, self.TargetInfo.GuildStatus)
end

function PlayerMenuUtils:TargetHasTeam()
    return self.TargetInfo.TeamID ~= 0 and self.TargetInfo.TeamID ~= nil
end

function PlayerMenuUtils:TargetHasTeamGroup()
    return self.TargetInfo.GroupID ~= 0 and self.TargetInfo.GroupID ~= nil
end

function PlayerMenuUtils:TargetIsGroupLeader()
    return self.TargetInfo.bGroupLeader
end

function PlayerMenuUtils:TargetIsTeamLeader()
    if self:TargetHasTeam() then
        return self.TargetInfo.IsCaptain
    elseif self:TargetHasTeamGroup() then
        if Game.GroupSystem:IsMyGroupMember(self.TargetInfo.EntityID) and Game.GroupSystem:IsTeamLeader(self.TargetInfo.EntityID) == true then
            return true
        end
    end
    return false
end

function PlayerMenuUtils:TargetIsTeamGroupLeader()
    return Game.GroupSystem:IsGroupLeader(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TargetIsTeamGroupAdministrator()
    return Game.GroupSystem:IsGroupManager(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TargetIsGuildChairMan()
    return Game.GuildSystem:IsBoss(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TargetIsInSeamGuild()
    return Game.me.GuildID == self.TargetInfo.GuildID and self:HasGuild()
end

function PlayerMenuUtils:TargetIsInEnmityList()
    return Game.FriendSystem:IsHereditaryFoe(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TargetIsInFoeList()
    return Game.FriendSystem:IsFoe(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TargetCanBeInvitedBattle()
    local targetEntity = Game.EntityManager:getEntityWithBrief(self.TargetInfo.EntityID)
    return not BSFunc.CheckTargetCamp(Game.me, targetEntity, 32)
end

function PlayerMenuUtils:TargetIsInSameGroup()
    return Game.GroupSystem:IsMyGroupMember(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TargetIsInSameTeam()
    if Game.me.teamID ~= 0 and self.TargetInfo.TeamID ~= 0 and Game.me.teamID == self.TargetInfo.TeamID then
        return true
    elseif Game.TeamSystem:IsInGroup() then
        return Game.GroupSystem:IsInSameTeam(self.TargetInfo.EntityID, Game.me.eid)
    end
    return false
end

function PlayerMenuUtils:TargetInSafeZone()
    return self.TargetInfo.LineType == Enum.ELineType.SAFE and self.TargetInfo.MapType == SharedWorldConst.WORLD_TYPE.BIGWORLD
end

function PlayerMenuUtils:PlayerInSafeZone()
    local localSpace = Game.NetworkManager.GetLocalSpace()
    if not localSpace then
        return false
    end
    return localSpace:IsLineSpace() and Game.HUDSystem.GetCurrentLineWorldLineType() == Enum.ELineType.SAFE
end

function PlayerMenuUtils:TagetInSameTeaRoom()
    return Game.TeaRoomSystem:GetRoomMemberInfo(self.TargetInfo.EntityID) ~= nil
end

function PlayerMenuUtils:TagetNotInSameTeaRoom()
    return Game.TeaRoomSystem:GetRoomMemberInfo(self.TargetInfo.EntityID) == nil
end

function PlayerMenuUtils:IsTarotTeamLeader()
    return Game.TarotTeamSystem:IsTarotTeamLeader(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:IsCanHandleSetGuildPosition()
    return Game.GuildSystem:HasRight(const.GUILD_RIGHT.POSITION_SET)
end

function PlayerMenuUtils:TargetIsUnlockManor()
	return bit_band(self.TargetInfo.HomelandUnlockType, bit_lshift(1, HomeConst.HOME_TYPE.HOME_MANOR)) == 1	--是否解锁了家园
end

---Button Callback

function PlayerMenuUtils:ViewInformation(bState)
    Game.RoleDisplaySystem.ReqRoleAttrBrief(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:SendMessage(bState)
    Game.ChatWhisperSystem:WhisperToPlayer(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:Addfriend(bState)
    if not Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_FRIEND,true) then
        return
    end
    if self.TargetInfo.Level < Game.TableData.GetConstDataRow("FRIEND_SERVER_LEVEL_LIMIT") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FRIEND_LEVEL_LIMIT)
        return
    end
    if Game.TeamAreanaSystem:IsInTeamArena() then
        self.TargetInfo.sourceID = Enum.EFriendAddSourceData.PVPGAME
    end
    --发出添加好友请求,先算主动添加
    if self.bShowGuildOptions then
        Game.FriendSystem:SendAddFriend(self.TargetInfo.EntityID, self.TargetInfo.sourceID or self.sourceID, 0)
    else
        Game.FriendSystem:SendAddFriend(self.TargetInfo.EntityID, self.TargetInfo.sourceID or self.sourceID, 0)
    end
end

function PlayerMenuUtils:DeleteFriend(bState)
    Game.me:sendDelFriend(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:InviteGuild(bState)
    Game.GuildSystem:InviteToGuild(self.TargetInfo.EntityID, self.TargetInfo.Name)
end

function PlayerMenuUtils:ApplyGuild(bState)
    Game.GuildSystem.sender:applyGuild(self.TargetInfo.GuildID)
end

function PlayerMenuUtils:InviteTeam(bState)
    Game.me:ReqInviteJoinTeam(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:ApplyTeam(bState)
    Game.TeamSystem:EnterApplyJoinTeamProcess(self.TargetInfo.TeamID, self.TargetInfo.EntityID,
        const.DEFAULT_ALL_TARGET_ID)
end

function PlayerMenuUtils:AppointTeamLeader(bState)
	if Game.DungeonSystem:IsInSingleDungeonWithBot() then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OPERATIONAL_LIMITATION_IN_SINGLE_DUNGEON)
		return
	end
    local TeamIndex, MemberIndex = Game.GroupSystem:GetIndexByEIDInMyGroup(self.TargetInfo.EntityID)
    local TeamUUID = Game.GroupSystem:GetTeamUUIDByEID(self.TargetInfo.EntityID)
	local MyTeamUUID = Game.GroupSystem:GetTeamUUIDByEID()
    if Game.GroupSystem:IsGroupLeader() and TeamUUID == MyTeamUUID then
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GROUP_LEADER_TRANSFER, function()
            Game.me:SetGroupTeamLeader({
                uid = self.TargetInfo.EntityID,
                teamUUID = TeamUUID,
                teamIndex = TeamIndex,
                memberIndex = MemberIndex
            })
        end)
    else
        Game.me:SetGroupTeamLeader({
            uid = self.TargetInfo.EntityID,
            teamUUID = TeamUUID,
            teamIndex = TeamIndex,
            memberIndex = MemberIndex
        })
    end
end

function PlayerMenuUtils:SwitchTeamLeader(bState)
	if Game.DungeonSystem:IsInSingleDungeonWithBot() then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OPERATIONAL_LIMITATION_IN_SINGLE_DUNGEON)
		return
	end
    if Game.TeamSystem:IsInTeam() then
        local teammenberInfo = Game.me.teamInfoList[self.TargetInfo.EntityID]
        if teammenberInfo and  teammenberInfo.isOnline == 1 then
            Game.me:ReqTransferCaptain(self.TargetInfo.EntityID)
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_PLAYER_NOT_EXIST)
        end
    elseif Game.TeamSystem:IsInGroup() then
        local MemberInfo = Game.GroupSystem:GetGroupMemberDetail(self.TargetInfo.EntityID)
        if MemberInfo and MemberInfo.isOnline == 1 then
            Game.me:ReqTransferGroupTeamLeader(self.TargetInfo.EntityID)
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_PLAYER_NOT_EXIST)
        end
    end
end

function PlayerMenuUtils:ApplyTeamLeader(bState)
	if Game.DungeonSystem:IsInSingleDungeonWithBot() then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OPERATIONAL_LIMITATION_IN_SINGLE_DUNGEON)
		return
	end
    if Game.me and Game.TeamSystem:IsCaptain() == false then
        Game.me:ReqApplyForCaptain()
    end
end

function PlayerMenuUtils:KickoffTeam(bState)
    if Game.me then
        Game.me:ReqKickTeamMember(self.TargetInfo.EntityID)
    end
end

function PlayerMenuUtils:ApplyMerge(bState)
    if Game.me then
        if Game.WorldBossSystem:IsInWorldBoss() and Game.WorldBossSystem:DoesPlayerHaveBossScore() then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.WORLD_BOSS_MERGE_TEAM, function()
                Game.me:ReqCombineTeam(self.TargetInfo.TeamID,self.TargetInfo.EntityID)
            end)
        else
            Game.me:ReqCombineTeam(self.TargetInfo.TeamID,self.TargetInfo.EntityID)
        end
    end
end

function PlayerMenuUtils:LeaveTeam(bState)
    Game.me:ReqLeaveTeam()
end

function PlayerMenuUtils:InviteTeamGroup(bState)
    Game.me:InviteJoinGroup(self.TargetInfo.EntityID, 0, 0)
end

function PlayerMenuUtils:ApplyTeamGroup(bState)
    Game.me:ApplyJoinGroupByGroup(self.TargetInfo.GroupID)
end

function PlayerMenuUtils:SwitchTeamGroupLeader(bState)
	if Game.DungeonSystem:IsInSingleDungeonWithBot() then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OPERATIONAL_LIMITATION_IN_SINGLE_DUNGEON)
		return
	end
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GROUP_LEADER_TRANSFER, function()
        Game.me:ChangeGroupLeader(self.TargetInfo.EntityID)
    end)
end

function PlayerMenuUtils:InviteTeamJoinGroup()
    Game.me:InviteJoinGroup(self.TargetInfo.EntityID, 0)
end

function PlayerMenuUtils:MoveMember(bState)
end

function PlayerMenuUtils:SetTeamGroupAdmin(bState)
    Game.me:SetGroupManager(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:RevokeTeamGroupAdmin(bState)
    Game.me:UnsetGroupManager(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:QuitTeamGroupAdmin(bState)
    Game.me:UnsetGroupManager(Game.me.eid)
end

function PlayerMenuUtils:QuitTeamGroup(bState)
    Game.me:QuitGroup()
end

function PlayerMenuUtils:KickoffTeamGroup(bState)
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GROUP_KICK_MEMBER, function()
        Game.me:KickGroupMember(self.TargetInfo.EntityID)
    end)
end

function PlayerMenuUtils:SendGift(bState)
    local friendInfo = Game.FriendSystem:GetRelationInfoByEntityID(self.TargetInfo.EntityID)
    if friendInfo then
        if friendInfo.state ~= const.FRIEND_SYSTEM_PLAYER_STATE.DEL then
            Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerGivePanel, self.TargetInfo.EntityID, self.TargetInfo.Name, Enum.SendGiftType.Normal)
        else
            Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.FRIEND_GIVE_FAIL_OFFLINE
            )
        end
    else
        Game.FriendSystem.sender:doQueryOtherPlayerOnlineState(self.TargetInfo.EntityID)
    end
end

function PlayerMenuUtils:SendTeaRoomGift(bState)
    local friendInfo = Game.FriendSystem:GetRelationInfoByEntityID(self.TargetInfo.EntityID)
    if friendInfo then
        if friendInfo.state ~= const.FRIEND_SYSTEM_PLAYER_STATE.DEL then
            Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerGivePanel, self.TargetInfo.EntityID, self.TargetInfo.Name, Enum.SendGiftType.TeaRoom)
        else
            Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.FRIEND_GIVE_FAIL_OFFLINE
            )
        end
    else
        Game.FriendSystem.sender:doQueryOtherPlayerOnlineState(self.TargetInfo.EntityID)
    end
end


function PlayerMenuUtils:TransferGuildLeader(bState)
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GUILD_LEADER_TRANSFER, function()
        if self.TargetInfo.EntityID and Game.GuildSystem then
            Game.GuildSystem.sender:setGuildRole(self.TargetInfo.EntityID, const.GUILD_ROLE.PRESIDENT)
        else
            Log.Debug("Fail to transfer role: Entity ID or GuildSystem is nil")
        end
    end, nil, { string.format("</><Pop>") })
end

function PlayerMenuUtils:KickoffGuild(bState)
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.GUILD_KICK, function()
        if self.TargetInfo.EntityID and Game.GuildSystem then
            Game.GuildSystem.sender:kickGuildMember(self.TargetInfo.EntityID)
        else
            Log.Debug("Fail to Kick: Entity ID or GuildSystem is nil")
        end
    end, nil, { self.TargetInfo.Name })
end

function PlayerMenuUtils:LeagueApply()
    Game.me:InviteJoinGroupLeague(self.TargetInfo.GroupID)
end

function PlayerMenuUtils:ChangePosition(bState)
    if not Game.GuildSystem:HasRight(const.GUILD_RIGHT.POSITION_SET) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NONE_GUILD_RIGHT)
        return
    end
    Game.NewUIManager:OpenPanel(UIPanelConfig.GuildInside_Designate_Panel, self.TargetInfo.EntityID)
end

function PlayerMenuUtils:ReportPlayer(bState)
	Game.ReportSystem:ShowReportUI(Enum.EReportType.PlayerReport, {ID = self.TargetInfo.EntityID, name = self.TargetInfo.Name, isPlayer = true})
end

function PlayerMenuUtils:MuteVoice(bState)
    if bState then
        if self:HasTeam() then
            Game.me:ReqTeamBlockVoiceReport(self.TargetInfo.EntityID, true)
        elseif self:HasTeamGroup() then
            Game.me:BlockGroupVoice(self.TargetInfo.EntityID, true)
        end
    else
        if self:HasTeam() then
            Game.me:ReqTeamBlockVoiceReport(self.TargetInfo.EntityID, false)
        elseif self:HasTeamGroup() then
            Game.me:BlockGroupVoice(self.TargetInfo.EntityID, false)
        end
    end
end

function PlayerMenuUtils:BlockPlayer(bState)
    if bState then
        Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.ADDBLACKLIST, function()
            Game.me:sendAddBlackList(self.TargetInfo.EntityID)
            --添加黑名单后，隐藏名片
            UI.HideUI(UIPanelConfig.PlayerCard_Panel)
        end)
    else
        Game.me:sendDelBlackList(self.TargetInfo.EntityID)
    end
end

function PlayerMenuUtils:FriendNameModify(bState)
    Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerRenamePanel, self.TargetInfo.EntityID)
end

function PlayerMenuUtils:InviteTeamUp()
    if Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_TEAM,true) then
        Game.me:ReqInviteJoinTeam(self.TargetInfo.EntityID)
    end
end

function PlayerMenuUtils:RemoveFromEnmityList()
    if Game.FriendSystem:IsFoe(self.TargetInfo.EntityID) then
        Game.FriendSystem:RemoveFoe(self.TargetInfo.EntityID)
    else
        Game.FriendSystem:RemoveHereditaryFoe(self.TargetInfo.EntityID)
    end
end

function PlayerMenuUtils:InviteBattle()
    Game.IndividualPVPSystem:ApplyIndividualPVP(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:WhisperToGuildChairMan()
    Game.ChatWhisperSystem:WhisperToPlayer(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:PersonalHomePage()
    Game.MomentsSystem:OpenPersonalHomePage(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:KickoffTarotTeam()
    Game.TarotTeamSystem:KickoffTarotTeam(self.TargetInfo.EntityID)
end

function PlayerMenuUtils:TransferTarotTeamLeader()
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.TAROTTEAM_TRANSFER_CAPTAIN, function()
        Game.TarotTeamSystem:TransferTarotTeamLeader(self.TargetInfo.EntityID)
    end, nil, { self.TargetInfo.Name })
end

function PlayerMenuUtils:ApplyResponseGuild()
    Game.GuildSystem:ApplyResponseGuild(self.TargetInfo.GuildID)
end

function PlayerMenuUtils:ReportClub()
    local data = self.TargetInfo
    Game.ReportSystem:ShowReportUI(Enum.EReportType.Guild, {ID = data.GuildID, name = data.GuildName})
end

--- 拜访家园
function PlayerMenuUtils:VisitManor()
	Game.ManorSystem:ReqEnterManor(self.TargetInfo.EntityID)
end

-- 请离公会分组
function PlayerMenuUtils:KickoffGuildGroup()
	Game.GuildSystem.sender:ReqSetGuildRole(
        Game.GuildSystem.model.guildMemberVersion or 0, self.TargetInfo.EntityID, 
        {[const.GUILD_ROLE.GROUP_MEMBER] = 0}
    )
end

return PlayerMenuUtils
