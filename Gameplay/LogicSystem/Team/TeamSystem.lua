---@class TeamSystem:SystemBase
local TeamSystem = DefineSingletonClass("TeamSystem", SystemBase)
local StringConst = require "Data.Config.StringConst.StringConst"
local const = kg_require("Shared.Const")
local team_utils = kg_require("Shared.Utils.TeamUtils")

TeamSystem.TipsType = {
    ApplyCaptain = 0,
    FollowCaptain = 1,
    GroupConvene = 2
}

TeamSystem.ETeamDisplayPlayStartList = {
    [1] = "TeamDisplayFirst",
    [2] = "TeamDisplaySecond",
    [3] = "TeamDisplayThird",
    [4] = "TeamDisplayFourth",
    [5] = "TeamDisplayFifth",
    [6] = "TeamDisplaySixth"
}

TeamSystem.EPageType = {
    Team = 0,
    Group = 1
}

TeamSystem.EChannel = {
    AllTarget = 5390001,
    CollectTarget = 5390002
}

TeamSystem.EApplyType = {
    JoinTeam = 0,
    CombineTeam = 1
}

TeamSystem.EPositionUIStyleMap = {
    Tank = 0,
    Defend = 1,
    Attack = 2
}

function TeamSystem:onInit()
    ---@type TeamSystemModel
    self.model = kg_require "Gameplay.LogicSystem.Team.TeamSystemModel".new()

    Game.EventSystem:AddListener(_G.EEventTypes.ON_TEAMID_CHANGED, self, self.OnTeamStateChange, GetMainPlayerEID())
    Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_CAPTAIN_CHANGED, self, self.OnCaptainStateChange, GetMainPlayerEID())
    Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_TEAM_TARGETID_CHANGED, self, self.OnTeamTargetChange, GetMainPlayerEID())
    Game.EventSystem:AddListener(_G.EEventTypes.SWITCH_MAP_LINE_DONE, self, self.SwitchMapLineDone)
	Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_LEVEL_LOADED, "Receive_LEVEL_ON_LEVEL_LOADED", self)
    Game.EventSystem:AddListener(_G.EEventTypes.CLIENT_GROUP_APPLY_UPDATE, self, self.OnGroupApplyChange)
    self:AddListener(_G.EEventTypes.ON_FOLLOWSTATE_CHANGED, self.OnSelfFollowStateChanged, GetMainPlayerEID())
    self:AddListener(_G.EEventTypes.TEAM_FOLLOW_TELEPORT_DONE, self.OnFollowTeleportDone)
    self:AddListenerV2(EEventTypesV2.ON_INPUT_UI_SHORTCUT, "OnInputUIShortcut")
    self.FollowForm = Enum.EFollowForm.NONE
    self.FollowBegin = true
    self.FollowPos = {}

    Game.EventSystem:AddListener(_G.EEventTypes.GAME_MAINPALYER_LOGIN, self, self.AddRedPoint)
    Game.RedPointSystem:AddListener("TeamApplicationIcon", self, self.CheckTaskIsReceive)
end

function TeamSystem:onUnInit()
end

function TeamSystem:RoleAfterEnterWorld(eid)
    UI.Invoke(UICellConfig.HUD_Team, "EnterAoi", eid)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_GROUP_ROLE_ON_BORN, eid, TeamSystem.EPageType.Team)
end

function TeamSystem:RoleExitWorld(eid)
    UI.Invoke(UICellConfig.HUD_Team, "LeaveAoi", eid)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_GROUP_ROLE_ON_DESTROY, eid, TeamSystem.EPageType.Team)
end

function TeamSystem:OnInviteApplyCombineUpdate(InviteInfo, ApplicatorInfo, CombineInfo)
    if next(InviteInfo) ~= nil and Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.ShowTeamInvite) == 1 then
        Game.PopTipsSystem:OnAddAddApplyInviteListElem({
            Type = Enum.EElasticStripData.ReceivedTeamInvitation,
            OperName = InviteInfo.initiatorName,
            OperProfession = InviteInfo.captainProfession,
            OperLevel = InviteInfo.captainLevel,
            OperID = InviteInfo.initiatorID,
            CaptainID = InviteInfo.captainID,
            targetID = InviteInfo.targetID,
            IsTeam = false
        })
    end
    if next(ApplicatorInfo) ~= nil and Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.ShowTeamApply) == 1 then
        Game.PopTipsSystem:OnAddAddApplyInviteListElem({
            Type = Enum.EElasticStripData.ReceivedJoinTeamApplication,
            OperName = ApplicatorInfo.name,
            OperProfession = ApplicatorInfo.profession,
            OperLevel = ApplicatorInfo.level,
            OperID = ApplicatorInfo.id,
            zhanli = ApplicatorInfo.zhanli,
            description = ApplicatorInfo.description,
            IsTeam = false
        })
    end
    if next(CombineInfo) ~= nil and Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.ShowTeamApply) == 1 then
        local teamMemberInfo = {}
        for key, value in pairs(CombineInfo.teamMemberInfo) do
            teamMemberInfo[#teamMemberInfo + 1] = value
        end
        table.insert(teamMemberInfo, 1,
            {
                memberID = CombineInfo.captainID,
                level = CombineInfo.captainLevel,
                profession = CombineInfo.captainProfession
            })
        Game.PopTipsSystem:OnAddAddApplyInviteListElem({
            Type = Enum.EElasticStripData.ReceivedCombineTeamApplication,
            OperName = CombineInfo.captainName,
            OperProfession = CombineInfo.captainProfession,
            OperLevel = CombineInfo.captainLevel,
            TeamID = CombineInfo.teamID,
            OperID = CombineInfo.captainID,
            memberSize = CombineInfo.memberSize,
            targetID = CombineInfo.targetID,
            TeamMemberInfo = teamMemberInfo,
            IsTeam = true
        })
    end
    self:ClearRedPointApplication()
end

-- 清理队伍申请红点
function TeamSystem:ClearRedPointApplication()
    Game.RedPointSystem:ClearNode('TeamApplicationIcon')
end

function TeamSystem:OnGroupApplyChange(dataType, roleBriefs, teamApplyBriefs)
    Game.PopTipsSystem:UpdateGroupApplyData(dataType, roleBriefs, teamApplyBriefs)
end

function TeamSystem:OnGroupInviteChange(roleBriefs, groupID, groupLeaderInfo, groupDetails,
                                        bGroupLeader, bTeamLeader, positionCount)
    Game.PopTipsSystem:UpdateGroupInviteData(roleBriefs, groupID, groupLeaderInfo, groupDetails,
        bGroupLeader, bTeamLeader, positionCount)
end 

function TeamSystem:OnBackToLogin()
    self:clearOnExitInGame()
end

function TeamSystem:OnBackToSelectRole()
    self:clearOnExitInGame()
end

function TeamSystem:OnMatchStateChange(State, TargetID, StartTime, Reason)
    if State == 0 then
        --匹配中
        Game.HUDSystem:ShowUI("HUD_TeamMatch_Panel")
    end
end

function TeamSystem:clearOnExitInGame()
    Game.PopTipsSystem:ClearAllApplyInvite()
end

--- region: Shout
function TeamSystem:ResetShoutHistory()
    self.model.ShoutTagInfoList = {[1]=false, [2]=false, [3]=false}
end

function TeamSystem:IntoShoutProcess(bSensitive, RecruitType, Text)
    if bSensitive then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED) -- luacheck: ignore
    else
        if self:IsInTeam() then
            Game.me:SendTeamRecruitInfo(Text .. self:GetShoutInfoOutput(self.model.ShoutTagInfoList), RecruitType)
        else
            Game.me:SendGroupRecruitInfo(Text .. self:GetShoutInfoOutput(self.model.ShoutTagInfoList), RecruitType)
        end
        self:SenderRescue()
    end
end

function TeamSystem:GetShoutInfoOutput(list)
    if not list then
        return ""
    end
    local text = ""
    for j = 1,#self.model.ShoutTagName do
        text = text .. (list[j] and self.model.ShoutTagName[j] or "")
    end
    return text
end
---endregion

---Data Getters and Setters -----------------------------------------------------------------------------------------

function TeamSystem:GetTeamDiscription()
    return self.model:GetTeamDiscription()
end

function TeamSystem:OnSceneMarkAllSync(AvatarActorSpaceFlag)
    ---全量同步
    self.model:UpdateAllSceneMarkData(AvatarActorSpaceFlag)
    Game.EventSystem:Publish(_G.EEventTypes.CLIENT_SCENE_MARK_CAHNGED)
end

function TeamSystem:OnSceneMarkPartChanged(Index, SpaceFlagInfo)
    ---增量修改
    self.model:UpdatePartSceneMarkData(Index, SpaceFlagInfo)
    Game.EventSystem:Publish(_G.EEventTypes.CLIENT_SCENE_MARK_CAHNGED)
    if Game.TeamSystem:IsCaptain() and SpaceFlagInfo.pos then
        local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
        local PosText = ChatUtils.GetLoactionMsg(FVector(SpaceFlagInfo.pos.X, SpaceFlagInfo.pos.Y, SpaceFlagInfo.pos.Z),
        Game.LevelManager.GetCurrentLevelID(),Game.MapSystem:GetCurrentPlaneID(),Game.HUDSystem.GetCurrentLineWorldID() % 100000)
        string.format(StringConst.Get("TEAM_SCENE_MARK_INFO"), Game.me.Name, Index, PosText)
        Game.ChatSystem:AddChannelSystemInfoByServer(PosText, Enum.EChatChannelData.TEAM)
    end
end

function TeamSystem:GetSceneMarkList()
    return self.model:GetSceneMarkList()
end

function TeamSystem:ClearSceneMarkList()
    self.model:ClearSceneMarkList()
end

-- 标记状态切换  todo 里面的UI操作 需要拿走
function TeamSystem:SetMarkState(MarkState, fromUI)
    if MarkState ~= self.model.MarkState then
        if MarkState == Enum.EMARK.Scene then
            if fromUI ~= UICellConfig.HUD_TeamMemberMarkMenu and UI.GetUI(UICellConfig.HUD_TeamMemberMarkMenu) then
                UI.HideUI(UICellConfig.HUD_TeamMemberMarkMenu)
            end
            if UI.GetUI(UICellConfig.HUD_Team) then
                UI.HideUI(UICellConfig.HUD_Team)
            elseif UI.GetUI(UICellConfig.HUD_Group) then
                UI.HideUI(UICellConfig.HUD_Group)
            end
            UI.ShowUI(UICellConfig.HUD_SceneMarkMenu)
        else
            if fromUI ~= UICellConfig.HUD_SceneMarkMenu and UI.GetUI(UICellConfig.HUD_SceneMarkMenu) then
                UI.HideUI(UICellConfig.HUD_SceneMarkMenu)
            end
            if Game.TeamSystem:IsInTeam() then
                if UI.GetUI(UICellConfig.HUD_TeamGroup_Container) then
                    if UI.GetUI(UICellConfig.HUD_Team) == nil then
                        Game.HUDSystem:ShowUI(UICellConfig.HUD_Team)
                    end
                else
                    Game.HUDSystem:ShowUI(UICellConfig.HUD_TeamGroup_Container)
                end
            elseif Game.TeamSystem:IsInGroup() then
                if UI.GetUI(UICellConfig.HUD_TeamGroup_Container) then
                    if UI.GetUI(UICellConfig.HUD_Group) == nil then
                        Game.HUDSystem:ShowUI(UICellConfig.HUD_Group)
                    end
                else
                    Game.HUDSystem:ShowUI(UICellConfig.HUD_TeamGroup_Container)
                end
            end
            if MarkState == Enum.EMARK.TeamGroup then
                --UI.ShowUI("P_TeamMemberMarkMenu")
                Game.HUDSystem:ShowUI(UICellConfig.HUD_TeamMemberMarkMenu)
            else
                if fromUI ~= UICellConfig.HUD_TeamMemberMarkMenu and UI.GetUI(UICellConfig.HUD_TeamMemberMarkMenu) then
                    UI.HideUI(UICellConfig.HUD_TeamMemberMarkMenu)
                end
            end
        end
        self.model:SetMarkState(MarkState)
        Game.GlobalEventSystem:Publish(EEventTypesV2.HUD_MARK_STATE_CAHNGED)
    end
end

function TeamSystem:GetMarkState()
    return self.model:GetMarkState()
end

function TeamSystem:IsTeamMemberInMark(EID)
    local bInMark = false
    if Game.me and Game.me.teamInfoList and Game.me.teamInfoList[EID] then
        if Game.me.teamInfoList[EID].memberFlag ~= 0 then
            bInMark = true
        end
    end
    return bInMark
end

function TeamSystem:GetTeamMemberMarkID(EID)
    local MarkID = -1
    if Game.me and Game.me.teamInfoList and Game.me.teamInfoList[EID] then
        if Game.me.teamInfoList[EID].memberFlag ~= 0 then
            MarkID = Game.me.teamInfoList[EID].memberFlag
        end
    end
    return MarkID
end

function TeamSystem:GetTeamMemberVoice(EID)
    if Game.me and Game.me.teamInfoList and Game.me.teamInfoList[EID] then
        return Game.me.teamInfoList[EID].voiceState
    end
    return 1
end

function TeamSystem:GetTeamMemberLineID(EID)
    if Game.me and Game.me.teamInfoList and Game.me.teamInfoList[EID] then
        return Game.me.teamInfoList[EID].worldID % 100000
    end
end

function TeamSystem:IsInBlockVoice(EID)
    local teamBlockVoices = GetMainPlayerPropertySafely("teamBlockVoices")
    for key, value in pairs(teamBlockVoices) do
        if key == EID then
            return true
        end
    end
    return false
end

function TeamSystem:GetLatestTargetID()
    return self.model:GetLatestTargetID()
end

function TeamSystem:GetAutoFollow()
    return self.model:GetAutoFollow()
end

function TeamSystem:bIsInvited(EID)
    return self.model:bIsInvited(EID)
end

function TeamSystem:AddInvitedPlayerIDs(EID)
    self.model:AddInvitedPlayerIDs(EID)
end

function TeamSystem:ClearInvitedPlayerIDs()
    self.model:ClearInvitedPlayerIDs()
end

function TeamSystem:bIsAppliedTeamGroup(ID)
    self.model:bIsAppliedTeamGroup(ID)
end

function TeamSystem:AddAppliedTeamGroupIDs(EID)
    self.model:AddAppliedTeamGroupIDs(EID)
end

function TeamSystem:ClearAppliedTeamGroupIDs()
    self.model:ClearAppliedTeamGroupIDs()
end

function TeamSystem:IsCanConvene()
    if Game.TeamAreanaSystem:IsInTeamArena() == false then
        local MapID = Game.NetworkManager.GetLocalSpace().mapID
        local MapData = Game.TableData.GetLevelMapDataRow(MapID)
        if MapData.IfTeamFollow and MapData.IfTeamFollow == true then
            return true
        end
    end
    return false
end

function TeamSystem:GetSortedTeamInfo()
    local TeamMateData = {}
    local teamInfoList = Game.me.teamInfoList
    local SelfData = nil
    for _, V in pairs(teamInfoList) do
        local Name = ""
        if V.botID ~= nil and V.botID ~= 0 then
            local botTableData = Game.TableData.GetBattleBotTemplateDataRow(V.botID)
            Name = botTableData.Name
        else
            Name = V.name
        end
        if V.id ~= GetMainPlayerEID() then
            TeamMateData[#TeamMateData + 1] = {
                id = V.id,
                profession = V.profession,
                sex = V.sex,
                enterTime = V.enterTime,
                name = Name,
                level = V.level,
                isCaptain = V.isCaptain == 1 and true or false
            }
        else
            SelfData = {
                id = V.id,
                profession = V.profession,
                sex = V.sex,
                enterTime = V.enterTime,
                name = Name,
                level = V.level,
                isCaptain = V.isCaptain == 1 and true or false
            }
        end
    end

    table.sort(
        TeamMateData,
        function(a, b)
            return a.enterTime < b.enterTime
        end
    )
    if SelfData then
        table.insert(TeamMateData, 1, SelfData)
    end
    return TeamMateData
end

function TeamSystem:GetTeamMembers()
    return Game.me and Game.me.teamInfoList or {}
end

function TeamSystem:GetTeamPlayerNum()
    local TeamMemberDatas = TeamSystem:GetTeamMembers()
    local Num = 0
    for key, value in pairs(TeamMemberDatas) do
        Num = Num + 1
    end
    return Num
end

function TeamSystem:IsTeamMember(EntityID)
    if Game.me and Game.me.teamInfoList and Game.me.teamInfoList[EntityID] then
        return true
    end
    return false
end

---@return AvatarActorTeamInfo
function TeamSystem:GetTeamMember(EntityID)
    local Members = self:GetTeamMembers()
    if Members then
        return Members[EntityID]
    else
        return nil
    end
end

function TeamSystem:GetCaptainInfo()
    local Members = TeamSystem:GetTeamMembers()
    for _, V in pairs(Members) do
        if V.isCaptain == 1 then
            return V
        end
    end
    return {}
end

function TeamSystem:IsCaptain(EntityID)
    if EntityID == nil then
        EntityID = Game.me.eid
    end

    local TeamMembers = TeamSystem:GetTeamMembers()
    for _, V in pairs(TeamMembers) do
        if V.id == EntityID then
            return V.isCaptain ~= 0
        end
    end
    return false
end

function TeamSystem:IsInGroup()
    if Game.GroupSystem:GetGroupID() ~= 0 then
        return true
    end
    return false
end

function TeamSystem:IsInTeam()
    if Game.me and Game.me.teamID ~= 0 then
        return true
    end
    return false
end

function TeamSystem:SenderRescue()
    if Game.NetworkManager.GetLocalSpace().bInSeekRescue == false then
        if self:IsInTeam() then
            Game.me:ReqTeamSeekRescue()
            Game.ChatSystem:chatTo(Enum.EChatChannelData.TEAM, StringConst.Get("TEAM_RESCUE_SLOGAN"), Enum.EChatMessageType.TEXT, Enum.ChatFunctionType.COMMON)
        elseif self:IsInGroup() then
            Game.me:GroupSeekRescue()
            Game.ChatSystem:chatTo(Enum.EChatChannelData.GROUP, StringConst.Get("TEAM_RESCUE_SLOGAN"), Enum.EChatMessageType.TEXT, Enum.ChatFunctionType.COMMON)
        end
    end
end

function TeamSystem:HasInvitaition()
    local Result = false
    if Game.me then
        Result = #Game.me.invitedInfoList > 0
    end
    return Result
end

function TeamSystem:HasApplication()
    if Game.me then
        local Application1 = false
        local Application2 = false
        if Game.me.teamApplicatorList and next(Game.me.teamApplicatorList) ~= nil then
            Application1 = true
        end
        if Game.me.teamCombineList and next(Game.me.teamCombineList) ~= nil then
            Application2 = true
        end
        return Application1 or Application2
    end
end

function TeamSystem:GetTeamApplyList()
    local ApplyList = {}
    if Game.me then
        if Game.me.teamApplicatorList and next(Game.me.teamApplicatorList) ~= nil then
            for key, value in pairs(Game.me.teamApplicatorList) do
                ApplyList[#ApplyList + 1] = { Data = value, Type = Game.TeamSystem.EApplyType.JoinTeam }
            end
        end
        if Game.me.teamCombineList and next(Game.me.teamCombineList) ~= nil then
            for key, value in pairs(Game.me.teamCombineList) do
                ApplyList[#ApplyList + 1] = { Data = value, Type = Game.TeamSystem.EApplyType.CombineTeam }
            end
        end
    end
    if #ApplyList > 0 then
        table.sort(
            ApplyList,
            function(a, b)
                return a.Data.applicateTime > b.Data.applicateTime
            end
        )
    end
    return ApplyList
end

function TeamSystem:UnpackWorldID(worldID)
    local MAP_ID_BASE = 5200000
    local SERVER_ID_INDEX = 1000
    local WORLD_BASE = 100000
    if type(worldID) ~= "number" then
        return 0,0
    end
    local worldBase = WORLD_BASE
    local lineBase = worldBase * SERVER_ID_INDEX
    local tempID = worldID % lineBase
    local mapID = (worldID - tempID) / lineBase + MAP_ID_BASE
    local lineID = tempID % worldBase
    return mapID, lineID
end

--------------------------------跟随 start ----------------------------------------
function TeamSystem:SetAutoFollow(value)
    self.model.bAutoFollow = value
end

function TeamSystem:OnSelfFollowStateChanged()
    local FollowState = GetMainPlayerPropertySafely("FollowState")
    if FollowState == Enum.EFollowState.ON_FOLLOWING then
    else
        --停止跟随
        Game.me:StopPathFollow()
        self.FollowForm = Enum.EFollowForm.NONE
        self.FollowPos = {}
    end
end

function TeamSystem:OnFollowTeleportDone()
    if GetMainPlayerPropertySafely("FollowState") == Enum.EFollowState.ON_FOLLOWING then
        self.FollowForm = Enum.EFollowForm.PATH_FOLLOW
    end
end

function TeamSystem:OnFollowInteractorDone(EndResult)
    if GetMainPlayerPropertySafely("FollowState") == Enum.EFollowState.ON_FOLLOWING then
        self.FollowForm = Enum.EFollowForm.PATH_FOLLOW
    end
end

function TeamSystem:SwitchMapLineDone()
  
end

function TeamSystem:CalFollowType(Pos)
    if Game.me == nil then
        return
    end
    local MeToFollowerDis = import("KismetMathLibrary").Vector_Distance( Game.me:GetPosition(), Pos)
    local teleport, TeleportToFollowerDis = Game.AutoNavigationSystem:CalculateNearestTeleportPoint(Game.NetworkManager.GetLocalSpace().mapID, Pos)
    if teleport then
        --传送点
        if TeleportToFollowerDis < MeToFollowerDis then
            return Enum.EFollowForm.TRANS_TELEPORT,teleport.ID
        end
    else
        --传送门
        local LevelMapTableData = Game.TableData.GetLevelMapDataRow(Game.NetworkManager.GetLocalSpace().mapID)
        if LevelMapTableData.InteractorID ~= 0 then
            local X, Y, Z, MapID = Game.WorldDataManager:GetPositionByInsID(LevelMapTableData.InteractorID, true)
            return Enum.EFollowForm.TRANS_INTERACTOR , {X = X, Y = Y, Z = Z}
        end
    end
    return Enum.EFollowForm.PATH_FOLLOW , nil
end

function TeamSystem:OnFollowPosChange(bSameScene, PosX, PosY, PosZ)
    local CurPos = {X = PosX, Y = PosY, Z = PosZ}
    if bSameScene then
        if self.FollowForm == Enum.EFollowForm.NONE then
            local FollowForm, Param = self:CalFollowType(CurPos)
            if FollowForm == Enum.EFollowForm.TRANS_TELEPORT then
                self.FollowForm = Enum.EFollowForm.TRANS_TELEPORT
                Game.TeleportManager:RequestTeleport(Param, Game.NetworkManager.GetLocalSpace().mapID)
            elseif FollowForm == Enum.EFollowForm.TRANS_INTERACTOR then
                self.FollowForm = Enum.EFollowForm.TRANS_TELEPORT
                Game.AutoNavigationSystem:StopNavigation()
                Game.me:StartPathFollowLocation(Param, -1)
                Game.me:StartListenPathFollowResult(self, "OnFollowInteractorDone")
            else
                self.FollowForm = Enum.EFollowForm.PATH_FOLLOW
                self.FollowPos = CurPos
                Game.AutoNavigationSystem:StopNavigation()
                Game.me:StartPathFollowLocation(CurPos, Game.TableData.GetConstDataRow("FOLLOW_STOP_DISTANCE"))
            end
        elseif self.FollowForm == Enum.EFollowForm.PATH_FOLLOW then
                if self.FollowPos and CurPos and import("KismetMathLibrary").Vector_Distance(CurPos, self.FollowPos) < 100 
                and import("KismetMathLibrary").Vector_Distance(Game.me:GetPosition(), self.FollowPos) < 100 then
                    return
                end
                self.FollowPos = CurPos
                Game.AutoNavigationSystem:StopNavigation()
                Game.me:StartPathFollowLocation(CurPos, Game.TableData.GetConstDataRow("FOLLOW_STOP_DISTANCE"))
        end
    else
        if self.FollowForm == Enum.EFollowForm.NONE then
            local FollowTargetID = nil
            if self:IsInTeam() then
                FollowTargetID = self:GetCaptainInfo().id
            elseif self:IsInGroup() then
                local bNormalMember = Game.GroupSystem:IsNormalTeamMember(Game.me.eid)
                if bNormalMember then
                    if Game.me.bFollowGroupLeader then
                        FollowTargetID = Game.GroupSystem:GetMyGroupLeaderInfo().uid
                    else
                        FollowTargetID = Game.GroupSystem:GetMyTeamLeaderInfo().uid
                    end
                elseif Game.GroupSystem:IsTeamLeader(Game.me.eid) then
                    FollowTargetID = Game.GroupSystem:GetMyGroupLeaderInfo().uid
                end
            end
            self.FollowForm = Enum.EFollowForm.TRANS_DUNGEON
            if FollowTargetID then
                Game.me:ReqToUserLine(FollowTargetID, const.USERLINE_TYPE.TEAM_USER_LINE)
            end
        end
    end
end

--------------------------------跟随 end ----------------------------------------

function TeamSystem:FollowCaptain()
    local FollowState = GetMainPlayerPropertySafely("FollowState")
    if FollowState == Enum.EFollowState.ON_FOLLOWING or FollowState == Enum.EFollowState.PAUSE_FOLLOW then
		self:StopFollow()
    elseif FollowState == Enum.EFollowState.STOP_FOLLOW then
        if Game.me then
            Game.me:StartFollow()
        end
    end
end

function TeamSystem:StopFollow()
	if Game.me then
		Game.me:StopFollow()
	end
end

function TeamSystem:IsTeamMemberAllFollowed()
    if Game.me and Game.me.teamInfoList then
        for key, value in pairs(Game.me.teamInfoList) do
            if value.bFollowing == false and Game.me and value.id ~= Game.me.eid then
                return false
            end
        end
    end
    return true
end

function TeamSystem:IsInConvene()
    if self:IsTeamMemberAllFollowed() and self:GetTeamPlayerNum() > 1 then
        return true
    end
    return false
end

function TeamSystem:IsInSingleMatchTargets(Target)
    local SingleMatchInfo = Game.me.singleMatchInfoList
    if SingleMatchInfo and #SingleMatchInfo > 0 then
        for key, value in pairs(SingleMatchInfo) do
            if value.singleTargetID == Target then
                return true
            end
        end
    end
    return false
end

----    UI Fucntions Start  ------------------------------------------------------------

function TeamSystem:ShowQuickTeamUpUI()
    Game.NewUIManager:OpenPanel(UIPanelConfig.TeamQuickUp_Panel)
end

function TeamSystem:ShowPlayerCardUI(Params)
    if Params == nil or (Params and Params.EntityID == Game.me.eid) then
        return
    end
    local Data = self.model:GetPlayerCardInfo()
    Params.bShowGuildOptions = Data.CardBeShowGuildOption[Params.EntityID]
    Params.sourceID = Data.CardSourceID[Params.EntityID]
    Params.CardOtherParams = Data.CardOtherParams
    Params.MenuType = Data.CardMenuType[Params.EntityID]
    Game.NewUIManager:OpenPanel(UIPanelConfig.PlayerCard_Panel, Params)
end

function TeamSystem:PlayerCardUIDataAsync(EntityID, bShowGuildOptions, sourceID, otherParams, menuType)
    if EntityID ~= nil then
        if UI.IsShow(UIPanelConfig.PlayerCard_Panel) then
            local cardUI = UI.GetUI(UIPanelConfig.PlayerCard_Panel)
            if cardUI.EntityID == EntityID then
                UI.HideUI(UIPanelConfig.PlayerCard_Panel)
                return
            end
        end
            
        self.model:SetPlayerCardInfo(EntityID, bShowGuildOptions, sourceID, otherParams, menuType)
        self:HidePlayerCardUI()
        if EntityID ~= "" and EntityID ~= Game.me.eid then
			-- 策划要求敌对状态下不显示玩家卡片，且不会有频繁请求的Tips，所以这里做额外判断
			local TargetRpcEntity = Game.EntityManager:getEntityWithBrief(EntityID)
			local CamRelation = BSFunc.GetFinalCampRelation(Game.me, TargetRpcEntity)
			if CamRelation == Enum.ECampEnumData.Enemy then
				return
			end
            TeamSystem:ReqOtherRoleOnlineBrief(EntityID)
        end
    end
end

function TeamSystem:HidePlayerCardUI()
    if UI.IsShow(UIPanelConfig.PlayerCard_Panel) then
        UI.HideUI(UIPanelConfig.PlayerCard_Panel)
    end
end

function TeamSystem:Receive_LEVEL_ON_LEVEL_LOADED()
    Game.PopTipsSystem:RefreshQueueUI()
    self.FollowForm = Enum.EFollowForm.NONE
end


function TeamSystem:ReceiveApplyForCaptain(teamMemberID)
    local memberInfo = Game.me.teamInfoList[teamMemberID]
    if memberInfo then
        Game.PopTipsSystem:OnAddAddApplyInviteListElem({
            Type = Enum.EElasticStripData.ApplyTeamLeader,
            OperID = teamMemberID,
            OperName = memberInfo.name,
            OperProfession = memberInfo.profession,
            OperLevel = memberInfo.lv,
            LineID = memberInfo.worldID % 100000,
            IsTeam = false
        })
    end
end

function TeamSystem:OnReceiveConvene()
    local followState = GetMainPlayerPropertySafely("FollowState")
    if followState == Enum.EFollowState.ON_FOLLOWING then
        return
    end
    if self:GetAutoFollow() == false then
        local CaptainInfo = self:GetCaptainInfo()
        if CaptainInfo then
            Game.PopTipsSystem:OnAddAddApplyInviteListElem({
                Type = Enum.EElasticStripData.GroupTeamMember,
                OperID = CaptainInfo.id,
                OperName = CaptainInfo.name,
                OperProfession = CaptainInfo.profession,
                OperLevel = CaptainInfo.lv,
                IsTeam = false
            })
        end
    else
        --直接接受召集
        Game.me:StartFollow()
    end
end

function TeamSystem:SortTarget(OriData)
    -- 排序
    local SortedValue = {}
    local FirstItem = nil
    for index, value in pairs(OriData) do
        if value.MarkId == const.DEFAULT_ALL_TARGET_ID or value.MarkId == const.DEFAULT_NO_TARGET_ID then
            FirstItem = { MarkId = value.MarkId, Locked = 0, Tab = value.Tab }
        else
            local Locked = 0
            local LockLevel = 0
            local TargetTableTable = Game.TableData.GetTargetDataRow(value.MarkId)
            if TargetTableTable then
                local FunctionID = TargetTableTable.Function
                local DungeonID = TargetTableTable.Trick
                if not Game.ModuleLockSystem:CheckDungeonUnlockByIndex(DungeonID, FunctionID, TargetTableTable.Type, false) then
                    Locked = 1
                end
                local DungeonTable = Game.TableData.GetDungeonDataRow(DungeonID)
                if DungeonTable and DungeonTable.MinLvlLimit then
                    LockLevel = DungeonTable.MinLvlLimit
                end
            end
            SortedValue[#SortedValue + 1] =
            {
                MarkId = value.MarkId,
                Locked = Locked,
                Tab = value.Tab,
                LockLevle = LockLevel
            }
        end
    end
    table.sort(
        SortedValue,
        function(a, b)
            if a.Locked ~= b.Locked then
                return a.Locked < b.Locked
            elseif a.LockLevle ~= b.LockLevle then
                return a.LockLevle < b.LockLevle
            else
                return a.MarkId < b.MarkId
            end
        end
    )
    if FirstItem then
        table.insert(SortedValue, 1, FirstItem)
    end
    return SortedValue
end

function TeamSystem:GetVoiceState()
    if self:IsInTeam() then
        if Game.me.teamInfoList and Game.me.teamInfoList[Game.me.eid] then
            return Game.me.teamInfoList[Game.me.eid].voiceState
        end
    elseif self:IsInGroup() then
        self.TotalGroupData = Game.GroupSystem:GetMyGroupAllMemberInfo()
        for _, v2 in pairs(self.TotalGroupData) do
            for _, v3 in pairs(v2) do
                if Game.me and v3.uid == Game.me.eid then
                    return v3.voiceState    
                end
            end
        end
    end
    return Enum.EVOICE_STATE.LISTEN
end

function TeamSystem:AddRedPoint()
    Game.RedPointSystem:InvokeNode("TeamApplicationIcon")
end

function TeamSystem:CheckTaskIsReceive()
    if not Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_TEAM, false) then
        return false
    end
    return self:GetTeamApplyList() and next(self:GetTeamApplyList())
end
----    Handle errcode Start -----------------------------------------------------------

function TeamSystem:ApplyCaptainErr(Result)
    local ErrCodes = Game.NetworkManager.ErrCodes
    if Result.Code == ErrCodes.TEAM_ALREADY_CAPTAIN then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_ALREADY_CAPTAIN)
    end
end

function TeamSystem:MatchErr(Result)
    local ErrCodes = Game.NetworkManager.ErrCodes
    if Result.Code == ErrCodes.PLAYER_IN_DUNGEON then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_MATCH_MEMBER_IN_DUNGEON)
    elseif Result.Code == ErrCodes.ILLEGAL_LEVEL then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.DUNGEON_LEVEL_NOT_MATCH)
    else
        Game.NetworkManager.ShowNetWorkResultReminder("MatchErr", Result)
    end
end

----    Handle errcode End  ------------------------------------------------------------
function TeamSystem:GetTeamNumLimit()
    local TargetID = GetMainPlayerPropertySafely("teamTargetID")
    if TargetID and TargetID ~= 0 then
        local TargetData = Game.TableData.GetTargetDataRow(TargetID)
        if TargetData then
            local DungeonData = Game.TableData.GetDungeonDataRow(TargetData.Trick)
            if DungeonData then
                return DungeonData.MaxPlayerLimit
            end
        end
    end
    return Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") --默认6人
end

function TeamSystem:OnTeamStateChange()
    local TeamID = GetMainPlayerPropertySafely("teamID")
    if TeamID ~= 0 then
        Game.ChatSystem:AddParttingLine(Enum.EChatChannelData["TEAM"])
    else
        self:SetTeamDiscript("")
        self:ClearSceneMarkList()
    end
    if UI.IsShow("P_TeamMemberMarkMenu") then
        UI.HideUI("P_TeamMemberMarkMenu")
    end
    self.FollowForm = Enum.EFollowForm.NONE
    self:SetMarkState(Enum.EMARK.None)
    ---需要重置头顶血条显示
    Game.GlobalEventSystem:Publish(EEventTypesV2.SETTING_ON_HP_SHOW)
end

function TeamSystem:SetTeamDiscript(TeamDiscript)
    self.model:SetTeamDiscript(TeamDiscript)
end

function TeamSystem:GroupStateChange()
    Game.PopTipsSystem:ClearAllApplyInvite()
end

function TeamSystem:OnCaptainStateChange()
    Game.PopTipsSystem:ClearAllApplyInvite()
end

function TeamSystem:SetLatestTargetID(TeamTargetID)
    self.model:SetLatestTargetID(TeamTargetID)
end

function TeamSystem:OnTeamTargetChange()
    local TeamID = GetMainPlayerPropertySafely("teamID")
    if TeamID ~= 0 then
        --有队伍
        local TeamTargetID = GetMainPlayerPropertySafely("teamTargetID")
        if TeamTargetID ~= const.DEFAULT_NO_TARGET_ID then
            self:SetLatestTargetID(TeamTargetID)
        end
    else
        self:SetLatestTargetID(const.DEFAULT_ALL_TARGET_ID)
    end
end

function TeamSystem:IsTeamInPveMatch()
    return Game.me.isInTeamMatch == 1
end

function TeamSystem:OnTeamInfoListChange()
    self:OnTeamNumChange()
end

function TeamSystem:OnTeamNumChange()
    if Game.DungeonSystem.IsInDungeon() then
        return
    end
    local TeamTarget = GetMainPlayerPropertySafely("teamTargetID")
    local CurTeamNum = Game.TeamSystem:GetTeamPlayerNum()
    local Limit = Game.TeamSystem:GetTeamNumLimit()
    if TeamTarget == const.DEFAULT_NO_TARGET_ID or CurTeamNum < Limit then
        return
    end
    local TargetData = Game.TableData.GetTargetDataRow(TeamTarget)
    if TargetData then
        if TargetData.Type == 3 then
            if self:IsTeamInPveMatch() then
                Game.me:ReqOpenDungeon(TargetData.Trick, const.DUNGEON_MODE.NO_ROBOT)
            end
        elseif TargetData.Type == 4 then
            Game.PVPSystem:ShowPVPPanel()
        end
    end
end

function TeamSystem:OnCameraChange()
    if UI.IsShow(UIPanelConfig.TeamDisplay_Panel) then
        Game.NewUIManager:HidePanel("TeamSystemOnCameraChange", UIPanelConfig.TeamDisplay_Panel)
    end
    TeamSystem:HidePlayerCardUI()
end

function TeamSystem:OnInputUIShortcut(actionName, keyActionCfg, actionTypeID)
    if Game.TableData.GetConstDataRow("CHOOSE_TEAMMATE_ACTION") ~= actionTypeID then return end
    
    self:SelectTeammateByIdx(tonumber(keyActionCfg.ActionArg))
end

function TeamSystem:SelectTeammateByIdx(Idx)
    if not self:IsInTeam() then
        return
    end

    local TeamData = Game.TeamSystem:GetTeamMembers()
    local TeamList = {}
    if TeamData then
        for K, V in pairs(TeamData) do
            if Game.me.eid ~= K then
                table.insert(TeamList, { MemberID = K, EnterTime = V.enterTime })
            end
        end
    end

    table.sort(
        TeamList,
        function(a, b)
            return a.EnterTime < b.EnterTime
        end
    )
    table.insert(TeamList, 1, { MemberID = Game.me.eid, EnterTime = 0 })


    if TeamList[Idx] then
        local CharUID = nil
        if TeamList[Idx].MemberID == Game.me.eid then
            CharUID = Game.me:uid()
        else
			local Entity = Game.EntityManager:getEntity(TeamList[Idx].MemberID)
			if Entity then
				CharUID = Entity:uid()
			end
        end

        if CharUID then
            Game.TargetLockSystem.ChangeLockTargetByEID(TeamList[Idx].MemberID)
        end
    end
end

----    UI Fucntions End  ------------------------------------------------------------

---Net Request -----------------------------------------------------------------------------------------
function TeamSystem:CreateTeam()
    if Game.me then
        Game.me:ReqCreateTeam()
    end
end

function TeamSystem:ReqHandleTeamInvite(OP, CaptainID)
    if Game.me then
        return Game.me:ReqHandleTeamInvite(OP, CaptainID)
    end
end

function TeamSystem:EnterApplyJoinTeamProcess(TeamID, TargetActorID, TargetID, bFromRecruit)
    local SelfTeamID = GetMainPlayerPropertySafely("teamID")
    --local bIsCaptain = GetMainPlayerPropertySafely("isCaptain") == 1
    --申请加入队伍
    if self:IsInTeam() then
        if SelfTeamID == TeamID then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_APPLY_TEAM_FAIL)
        else
            --if bIsCaptain == false then
                --队员
                Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEND_APPLICATION_CONFIRM,
                    function()
                        self:RequestTargetJoinTeam(TeamID, TargetActorID, TargetID, bFromRecruit)
                    end,
                    nil
                )
            --end
        end
    elseif self:IsInGroup() then
        if Game.GroupSystem:IsGroupLeader() then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_LEADER_CAN_NOT_APPLY)
        else
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.SEND_APPLICATION_CONFIRM_TEAM, function()
                    self:RequestTargetJoinTeam(TeamID, TargetActorID, TargetID, bFromRecruit)
                    UI.Invoke(UIPanelConfig.TeamQuickUp_Panel, "RecordAppliedIDs", TeamID)
                end,
                nil
			)
        end
    else
        self:RequestTargetJoinTeam(TeamID, TargetActorID, const.DEFAULT_ALL_TARGET_ID, bFromRecruit)
    end
end

function TeamSystem:RequestTargetJoinTeam(TeamID, TargetActorID, TargetID, bFromRecruit)
    local func = function()
        Game.me:ReqJoinTeam(TeamID, TargetActorID, TargetID, bFromRecruit)
        UI.Invoke(UIPanelConfig.TeamQuickUp_Panel, "RecordAppliedIDs", TeamID)
    end
    if Game.DungeonSystem.IsInDungeon() then
        if not Game.DungeonSystem.ShowDungeonSuc(true, func) then
            func()
        end
        return
    end
    func()
end

function TeamSystem:EnterLeaveTeamProcess()
    if Game.me then
        if Game.DungeonSystem.IsInDungeon() then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.LEAVE_DUNGEONTEAM_CONFIRM, function()
                if not Game.DungeonSystem.ShowDungeonSuc(true) then
                    Game.me:ReqLeaveTeam()
                    Game.me:ReqLeaveDungeon(false)
                end
            end)
        elseif Game.WorldBossSystem:IsInWorldBoss() then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.WORLD_BOSS_LEAVE_TEAM, function()
                Game.me:ReqLeaveTeam()
            end)
        else
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.QUIT_TEAM_CONFIRM, function()
                if not Game.DungeonSystem.ShowDungeonSuc(true) then
                    Game.me:ReqLeaveTeam()
                end
            end)
        end
    end
end

function TeamSystem:ReqTeamMatch(TargetID, Operate)
    Game.me:ReqTeamMatch(TargetID, Operate)
end

function TeamSystem:ReqTeamRefreshInviteInfo()
    Game.me:ReqTeamRefreshInviteInfo()
end

function TeamSystem:ReqOtherRoleOnlineBrief(EntityID)
    Game.me:ReqOtherRoleOnlineBrief(EntityID)
end

function TeamSystem:OnMsgTeamMemberChanged(Reason1, Reason2, MemberInfo)
    if Game.me == nil then
        return
    end
    local bIsSelf = (MemberInfo.id == Game.me.eid)
    if Reason1 == team_utils.TeamMemberChangeReason.TMCR_ENTER then
        if not bIsSelf then
            if Reason2 ~= team_utils.TeamMemberChangeSubReason.TMCSR_GROUP_DISBAND then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_MEMBER_ENTER_TEAM, { { MemberInfo.name } })
            end
            Game.ChatSystem:AddChannelSystemInfo(string.format(StringConst.Get("SOCIAL_TEAM_ENTER"), MemberInfo.name),
                _G._now(), Enum.EChatChannelData["TEAM"])
            if Reason2 == team_utils.TeamMemberChangeSubReason.TMCSR_SUPPORT then
                --应援入队
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_ASSISTANCE, MemberInfo)
                Game.ChatSystem:AddChannelSystemInfo(StringConst.Get("TEAM_SUPPORT_THANKS_1"), _G._now(),
                    Enum.EChatChannelData["TEAM"])
            end
        end
    elseif Reason1 == team_utils.TeamMemberChangeReason.TMCR_LEAVE then
        if MemberInfo.id ~= Game.me.eid then
            Game.ChatSystem:AddChannelSystemInfo(string.format(StringConst.Get("SOCIAL_TEAM_EXIT"), MemberInfo.name),
                _G._now(), Enum.EChatChannelData["TEAM"])
            Game.VoiceSystem:RemoveBlackList(MemberInfo.id, Enum.EVOICE_CHANNEL.WORLD)
        else
            if Reason2 == team_utils.TeamMemberChangeSubReason.TMCSR_DEFAULT then
                --自己退出队伍 清理一下红点
                self:ClearRedPointApplication()
            elseif Reason2 == team_utils.TeamMemberChangeSubReason.TMCSR_KICKED then
                --踢出队伍
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_KICKED_OUT)
				Game.ChatSystem:UpdateChannelList(Enum.EChatChannelData.Team)
            elseif Reason2 == team_utils.TeamMemberChangeSubReason.TMCSR_BUILD_GROUP then
                --创建团队离队
            end
        end
    elseif Reason1 == team_utils.TeamMemberChangeReason.TMCR_CAPTAIN_CHANGE then
        local ReminderEnum = Enum.EReminderTextData
        if bIsSelf then
            if Game.TeamSystem:GetLatestTargetID() == const.WORLD_BOSS_TARGET_ID then
                Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.WORLD_BOSS_TEAM_MEMBER_TRANSFER_PERMISSION)
            end
            Game.ReminderManager:AddReminderById(
                ReminderEnum.TEAM_BECOME_TEAM_LEADER,
                { { StringConst.Get("TEAM_YOUHAD") } }
            )
            Game.ChatSystem:AddChannelSystemInfo(
                string.format(StringConst.Get("SOCIAL_TEAM_LEADER"), StringConst.Get("YOU")), _G._now(),
                Enum.EChatChannelData["TEAM"])
        else
            Game.ReminderManager:AddReminderById(ReminderEnum.TEAM_BECOME_TEAM_LEADER, { { MemberInfo.name } })
            Game.ChatSystem:AddChannelSystemInfo(string.format(StringConst.Get("SOCIAL_TEAM_LEADER"), MemberInfo.name),
                _G._now(), Enum.EChatChannelData["TEAM"])
        end
        Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ON_CAPTAIN_CHANGED)
    end

    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ON_TEAM_MEMBER_UPDATE)
    Game.GlobalEventSystem:Publish(EEventTypesV2.SERVER_MSG_TEAM_MEMBER_CHANGED, Reason1, Reason2, MemberInfo)
end

function TeamSystem:OnMsgCaptainApplyRejected()
    local CaptainInfo = self:GetCaptainInfo()
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_REFUSE_APPLICATION, { { CaptainInfo.name } })
end

function TeamSystem:UpdateTeamBlockVoices(eid, newValue)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_BLOCK_VOICE_CHANGED, eid, newValue)
end

---@param isStart boolean
---@param buff Buff
function TeamSystem:OnBuffChange(isStart, buff)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TEAM_GROUP_BUFF_CHANGED, isStart, buff)
end



return TeamSystem
