local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local BaseListItemComponent = kg_require("Framework.UI.List.NewList.BaseListItemComponent")

---@class TeamInviteApplyComItem : UIListItem
---@field view TeamInviteApplyComItemBlueprint
local TeamInviteApplyComItem = DefineClass("TeamInviteApplyComItem", UIListItem, BaseListItemComponent)
local TeamHeadCom = kg_require("Gameplay.LogicSystem.Team.TeamHeadCom")

local CharacterHead = kg_require "Gameplay.LogicSystem.Character.CharacterHead"
local TEAM_SIZE_LIMIT = Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT")
local StringConst = require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")
local Sharedconst = kg_require"Shared.Const"


TeamInviteApplyComItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TeamInviteApplyComItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TeamInviteApplyComItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TeamInviteApplyComItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TeamInviteApplyComItem:InitUIEvent()
    self:AddUIEvent(self.view.WBP_Reject.Big_Button_ClickArea_lua.OnClicked, "on_WBP_RejectBig_Button_ClickArea_lua_Clicked")
    self:AddUIEvent(self.view.WBP_Accept.Big_Button_ClickArea_lua.OnClicked, "on_WBP_AcceptBig_Button_ClickArea_lua_Clicked")
    self:AddUIEvent(self.view.WBP_ComBtnIcon.Big_Button_ClickArea_lua.OnClicked, "on_WBP_ComBtnIconBig_Button_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TeamInviteApplyComItem:InitUIView()
    self.Capatin_Head = self:CreateComponent(self.view.WBP_ComHead, CharacterHead)
    self.TeamMemberUIMap = {
        [1] = self.view.WBP_ComHeadFirst,
        [2] = self.view.WBP_ComHeadSecond,
        [3] = self.view.WBP_ComHeadThird,
        [4] = self.view.WBP_ComHeadFourth,
        [5] = self.view.WBP_ComHeadFifth,
    }
    self.TeamMember_Head = {}
    for i = 1, TEAM_SIZE_LIMIT - 1, 1 do
        local head = self:CreateComponent(self.TeamMemberUIMap[i], TeamHeadCom)
        self.TeamMember_Head[#self.TeamMember_Head + 1] = head
    end
    self.view.WBP_ComHead.KImg_Add:SetVisibility(ESlateVisibility.Collapsed)
end




---面板打开的时候触发
function TeamInviteApplyComItem:OnRefresh(data)
    if data.parentUID == UIPanelConfig.TeamInvite_Panel then
        if data.OpenType and data.OpenType == Enum.EElasticStripData.InviteToChampionTroop then
            self:Refresh(data, Enum.EElasticStripData.InviteToChampionTroop)
        elseif Game.TeamSystem:IsInGroup() then
            self:Refresh(data, Enum.EElasticStripData.InviteOthersInGroup)
        else
            self:Refresh(data, Enum.EElasticStripData.InviteOthersInTeam)
        end
        return
    end
    
    local bInTeam = Game.TeamSystem:IsInTeam()
    if bInTeam then
        if data.Type == Game.TeamSystem.EApplyType.JoinTeam then
            self:Refresh(data.Data, Enum.EElasticStripData.ReceivedJoinTeamApplication)
        else
            self:Refresh(data.Data, Enum.EElasticStripData.ReceivedCombineTeamApplication)
        end
    else
        self:Refresh(data, Enum.EElasticStripData.ReceivedJoinGroupApplication)
    end
end


--- 此处为自动生成
function TeamInviteApplyComItem:on_WBP_RejectBig_Button_ClickArea_lua_Clicked()
    self:Click_WBP_Reject_Big_Button_ClickArea()
end

--- 此处为自动生成
function TeamInviteApplyComItem:on_WBP_AcceptBig_Button_ClickArea_lua_Clicked()
    self:Click_WBP_Accept_Big_Button_ClickArea()
end

--- 此处为自动生成
function TeamInviteApplyComItem:on_WBP_ComBtnIconBig_Button_ClickArea_lua_Clicked()
    self:Click_WBP_ComBtnIcon_Big_Button_ClickArea()
end


------------------------------邀请他人组队 start------------------------------
function TeamInviteApplyComItem:Click_WBP_ComBtnIcon_Big_Button_ClickArea()
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common, true)
    self:RefreshInviteStateUI(true)
    if Game.NewUIManager:CheckPanelIsOpen(UIPanelConfig.TarotTeamInviteEditWindow) then
        local params = self:GetParent():GetParent().params
        local inputText = string.format(Game.TableData.GetTarotTeamStringConstDataRow("TAROTTEAM_RECRUITMENT_INVITATION_TEXT").StringValue, params.name, params.declaration)
        local deafaultText = string.format(Game.TableData.GetTarotTeamStringConstDataRow("TAROTTEAM_INVITE_TEXT").StringValue, params.id, params.name)
        inputText = string.format("%s%s", inputText, deafaultText)
        Game.ChatSystem:SendChatMessage(self.Data.EntityID, inputText, Enum.EChatMessageType.TEXT, nil, nil, true, false)
        return
    end
    Game.TeamSystem:AddInvitedPlayerIDs(self.Data.EntityID)
    if self.TipsType == Enum.EElasticStripData.InviteOthersInTeam then
        --组队
        Game.me:ReqInviteJoinTeam(self.Data.EntityID)
    elseif self.TipsType == Enum.EElasticStripData.InviteOthersInGroup then
        --团队
        Game.me:InviteJoinGroup(self.Data.EntityID, 0)
    elseif self.TipsType == Enum.EElasticStripData.InviteToChampionTroop then
        --比武大会战队
        Game.PVPSystem.sender:ReqChampionInviteJoinTroop(self.Data.EntityID)
    end
end

function TeamInviteApplyComItem:RefreshInvitePlayerUI()
    self.userWidget:Event_UI_Style(0 ,false)
    self.view.Text_Name:SetText(self.Data.Name)
    self.ApplyerID = self.Data.EntityID
    self.Level = self.Data.Level
    self.ProfessionID = self.Data.Profession
    self.Name = self.Data.Name
    self.Capatin_Head:Refresh(
            {
                ProfessionID = self.ProfessionID,
                Level = self.Level,
                EntityID = self.ApplyerID,
                bIsCaptain = false,
                OnClickedCallBack = function() self.Capatin_Head:OpenPlayerCard() end
            }
    )
    if Game.TeamSystem:bIsInvited(self.Data.EntityID) then
        self:RefreshInviteStateUI(true)
    else
        self:RefreshInviteStateUI(false)
    end
    if Game.FriendSystem:IsFriend(self.Data.EntityID) then
        self.view.WBP_SocialItem:SetColor(1)
        self.view.WBP_SocialItem.Text_Tag_lua:SetText(StringConst.Get("TEAM_INVITE_TAB_FRIEND"))
    else
        self.view.WBP_SocialItem:SetColor(0)
        self.view.WBP_SocialItem.Text_Tag_lua:SetText(StringConst.Get("TEAM_INVITE_TAB_STRANGER"))
    end
    if self.TipsType == Enum.EElasticStripData.InviteToChampionTroop then
        self.view.Text_TeamCol:SetText("")
        self.userWidget:Event_UI_Style(0, false)
        return
    end

    if self.Data.teamNum > 0 then
        self.view.Text_TeamCol:SetText(string.format(StringConst.Get("TEAM_INVITE_APPLY_HINT"), self.Data.teamNum, Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT")))
        self.userWidget:Event_UI_Style(0, false)
    elseif self.Data.groupNum > 0 then
        self.view.Text_TeamCol:SetText(string.format(StringConst.Get("TEAM_INVITE_APPLY_GROUP_HINT"), self.Data.groupNum, Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT")*Game.TableData.GetConstDataRow("GROUP_TEAM_COUNT")))
        self.userWidget:Event_UI_Style(0, true)
    else
        self.view.Text_TeamCol:SetText("")
        self.userWidget:Event_UI_Style(0, false)
    end
end

function TeamInviteApplyComItem:RefreshInviteStateUI(bInvited)
    if bInvited then
        self.view.WBP_ComBtnIcon:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Text_Invite:SetVisibility(ESlateVisibility.selfHitTestInvisible)
    else
        self.view.WBP_ComBtnIcon:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.view.Text_Invite:SetVisibility(ESlateVisibility.Collapsed)
    end
end

------------------------------邀请他人组队 end------------------------------

------------------------------收到的申请 start------------------------------
function TeamInviteApplyComItem:Click_WBP_Accept_Big_Button_ClickArea()
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common, true)
    if self.TipsType == Enum.EElasticStripData.ReceivedJoinTeamApplication then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_APPLY, self.ApplyerID)
        Game.me:ReqHandleJoinTeam(0, self.ApplyerID)
    elseif self.TipsType == Enum.EElasticStripData.ReceivedCombineTeamApplication then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_COMBINE, self.CaptainID)
        local TargetID = Game.TeamSystem:GetLatestTargetID()
        if TargetID == self.Data.TeamTargetID and TargetID == Sharedconst.WORLD_BOSS_TARGET_ID then
            Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.WORLD_BOSS_MERGE_TEAM, function()
                Game.me:ReqHandleCombineTeam(self.TeamID, self.CaptainID, 0)
            end)
        else
            Game.me:ReqHandleCombineTeam(self.TeamID, self.CaptainID, 0)
        end
    elseif self.TipsType == Enum.EElasticStripData.ReceivedJoinGroupApplication then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_APPLY, self.ApplyerID)
        Game.me:AgreeGroupApply(self.ApplyerID)
        local Apply = {}
        Apply[self.ApplyerID] = { uid = self.ApplyerID }
        Game.GroupSystem:UpdateGroupApply(3, Apply)
    elseif self.TipsType == Enum.EElasticStripData.ReceivedTeamInvitation then
        Game.TeamSystem:ReqHandleTeamInvite(0, self.CaptainID)
    elseif self.TipsType == Enum.EElasticStripData.ReceivedGroupInvitation then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_INVITE, self.InviterID)
        Game.me:AgreeGroupInvite(self.GroupID, self.InviterID)
        Game.GroupSystem:DelGroupInvite(self.GroupID)
    end
    Game.PopTipsSystem:RemoveApplyInviteListByType(self.TipsType)
end

function TeamInviteApplyComItem:Click_WBP_Reject_Big_Button_ClickArea()
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common, true)
    if self.TipsType == Enum.EElasticStripData.ReceivedJoinTeamApplication then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_APPLY, self.ApplyerID)
        Game.me:ReqHandleJoinTeam(1, self.ApplyerID)
    elseif self.TipsType == Enum.EElasticStripData.ReceivedCombineTeamApplication then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_COMBINE, self.CaptainID)
        Game.me:ReqHandleCombineTeam(self.TeamID, self.CaptainID, 1)
    elseif self.TipsType == Enum.EElasticStripData.ReceivedJoinGroupApplication then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_APPLY, self.ApplyerID)
        Game.me:RefuseGroupApply(self.ApplyerID)
        local Apply = {}
        Apply[self.ApplyerID] = { uid = self.ApplyerID }
        Game.GroupSystem:UpdateGroupApply(3, Apply)
        if not Game.GroupSystem:HasGroupApplication() then
            UI.HideUI(UIPanelConfig.TeamApply_Panel)
        end
    elseif self.TipsType == Enum.EElasticStripData.ReceivedTeamInvitation then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_INVITE, self.CaptainID)
        Game.TeamSystem:ReqHandleTeamInvite(1, self.CaptainID)
    elseif self.TipsType == Enum.EElasticStripData.ReceivedGroupInvitation then
        Game.EventSystem:Publish(_G.EEventTypes.TEAM_HANDLED_SINGLE_INVITE, self.InviterID)
        Game.me:RefuseGroupInvite(self.GroupID, self.InviterID)
        Game.GroupSystem:DelGroupInvite(self.GroupID)
    end
    Game.PopTipsSystem:RemoveApplyInviteListByType(self.TipsType)
end

function TeamInviteApplyComItem:RefreshReceivedApplyUI()
    if self.TipsType == Enum.EElasticStripData.ReceivedJoinTeamApplication then
        --个人申请
        self.userWidget:Event_UI_Style(2,false)
        self.view.Text_TeamCol:SetVisibility(ESlateVisibility.Collapsed)
        self.ApplyerID = self.Data.id
        self.Level = self.Data.level
        self.ProfessionID = self.Data.profession
        self.Name = self.Data.name
        self.Capatin_Head:Refresh({
            ProfessionID = self.ProfessionID,
            Level = self.Level,
            EntityID = self.ApplyerID,
            bIsCaptain = false,
            OnClickedCallBack = function(P)
                P:OpenPlayerCard()
            end
        })
        self.view.Text_Name:SetText(self.Data.name)
        self.view.Text_zhanli:SetText(self.Data.zhanli)
    elseif self.TipsType == Enum.EElasticStripData.ReceivedCombineTeamApplication then
        self.userWidget:Event_UI_Style(1,false)
        self.view.Text_TeamCol:SetVisibility(ESlateVisibility.Collapsed)
        --队伍合并
        self.TeamID = self.Data.teamID
        self.CaptainID = self.Data.captainID
        self.Capatin_Head:Refresh(
                {
                    ProfessionID = self.Data.captainProfession,
                    Level = self.Data.captainLevel,
                    EntityID = self.CaptainID,
                    bIsCaptain = true,
                    OnClickedCallBack = function(P) P:OpenPlayerCard() end
                }
        )
        self:RefreshTeammateUI()
        self.view.Text_CaptainName:SetText(self.Data.captainName)
        local TeamNum = #self.Data.teamMemberInfo + 1
        self.view.Text_Num:SetText(TeamNum)
        local Target = self.Data.target
        if Target == "" or Target == 0 then
            self.view.Text_Target:SetText(StringConst.Get("TEAM_NOTARGET"))
        else
            local TargetTableData = Game.TableData.GetTargetDataRow(Target)
            if TargetTableData then
                self.view.Text_Target:SetText(TargetTableData.Name)
            end
        end
    elseif self.TipsType == Enum.EElasticStripData.ReceivedJoinGroupApplication then
        --申请进团
        self.userWidget:Event_UI_Style(2,false)
        self.view.Text_TeamCol:SetVisibility(ESlateVisibility.Collapsed)
        self.ApplyerID = self.Data.uid
        self.Level = self.Data.lv
        self.ProfessionID = self.Data.profession
        self.Name = ""
        if self.Data.name then
            self.Name = self.Data.name
        end
        self.Capatin_Head:Refresh(
                {
                    ProfessionID = self.Data.profession,
                    Level = self.Data.level,
                    EntityID = self.Data.ApplyerID,
                    bIsCaptain = false
                }
        )
        self.view.Text_Name:SetText(self.Name)
        self.view.Text_zhanli:SetText(self.Data.zhanLi)
    end
end

------------------------------收到的申请 end------------------------------

------------------------------收到的邀请 start------------------------------
function TeamInviteApplyComItem:RefreshReceivedInviteUI()
    if self.TipsType == Enum.EElasticStripData.ReceivedTeamInvitation then
        --组队邀请
        self.userWidget:Event_UI_Style(1,false)
        self.view.Text_TeamCol:SetVisibility(ESlateVisibility.Collapsed)
        self.CaptainID = self.Data.captainID
        self.view.Text_CaptainName:SetText(self.Data.captainName)
        self.view.Text_Num:SetText(# self.Data.teamMemberInfo + 1)
        local TargetID = self.Data.TeamTargetID
        if TargetID == nil or TargetID == 0 then
            self.view.Text_Target:SetText(StringConst.Get("TEAM_NOTARGET"))
        else
            local TargetTableData = Game.TableData.GetTargetDataRow(TargetID)
            self.view.Text_Target:SetText(TargetTableData.Name)
        end
        self.Capatin_Head:Refresh(
                {
                    ProfessionID = self.Data.captainProfession,
                    Level = self.Data.captainLevel,
                    EntityID = self.Data.captainID,
                    bIsCaptain = true,
                    OnClickedCallBack = function() self.Capatin_Head:OpenPlayerCard() end
                }
        )
        self:RefreshTeammateUI()
    elseif self.TipsType == Enum.EElasticStripData.ReceivedGroupInvitation then
        --团队邀请
        self.userWidget:Event_UI_Style(3,false)
        self.view.Text_TeamCol:SetVisibility(ESlateVisibility.Collapsed)
        self.GroupID = self.Data.groupID
        self.InviterID = self.Data.roleBrief.uid
        self.groupLeaderInfo = self.Data.groupLeaderInfo
        self.groupDetails = self.Data.groupDetails
        self.view.Text_GroupName:SetText(self.groupLeaderInfo.name .. StringConst.Get("GROUP_X_GROUP"))
        local TargetID = self.groupDetails.targetID
        if TargetID == nil or TargetID == 0 then
            self.view.Text_Target:SetText(StringConst.Get("TEAM_NOTARGET"))
        else
            local TargetTableData = Game.TableData.GetTargetDataRow(TargetID)
            self.view.Text_Target:SetText(TargetTableData.Name)
        end
        self.Capatin_Head:Refresh({
            EntityID = self.groupLeaderInfo.uid,
            ProfessionID = self.groupLeaderInfo.profession,
            Level = self.groupLeaderInfo.lv,
            bIsCaptain = true,
            OnClickedCallBack = function() self.Capatin_Head:OpenPlayerCard() end
        })
        local Pos0 = 0
        local Pos1 = 0
        local Pos2 = 0
        if self.Data.positionCount and self.Data.positionCount[0] then
            Pos0 = self.Data.positionCount[0]
        end
        if self.Data.positionCount and self.Data.positionCount[1] then
            Pos1 = self.Data.positionCount[1]
        end
        if self.Data.positionCount and self.Data.positionCount[2] then
            Pos2 = self.Data.positionCount[2]
        end
        self.view.WBP_GroupPos0:Event_UI_Style(0,false)
        self.view.WBP_GroupPos1:Event_UI_Style(1,false)
        self.view.WBP_GroupPos2:Event_UI_Style(2,false)
        self.view.WBP_GroupPos0.Text_Num:SetText(Pos0)
        self.view.WBP_GroupPos1.Text_Num:SetText(Pos1)
        self.view.WBP_GroupPos2.Text_Num:SetText(Pos2)
        self.view.Text_GroupNum:SetText((Pos0 + Pos1 + Pos2) .. '/30')
    end
end

------------------------------收到的邀请 end------------------------------


function TeamInviteApplyComItem:Refresh(data, tipsType)
    self.Data = data
    self.TipsType = tipsType
    if tipsType == Enum.EElasticStripData.InviteOthersInTeam or tipsType == Enum.EElasticStripData.InviteOthersInGroup or tipsType == Enum.EElasticStripData.InviteToChampionTroop then
        self:RefreshInvitePlayerUI()
    elseif tipsType == Enum.EElasticStripData.ReceivedJoinTeamApplication
            or tipsType == Enum.EElasticStripData.ReceivedJoinGroupApplication
            or tipsType == Enum.EElasticStripData.ReceivedCombineTeamApplication
    then
        self:RefreshReceivedApplyUI()
    elseif tipsType == Enum.EElasticStripData.ReceivedTeamInvitation
            or tipsType == Enum.EElasticStripData.ReceivedGroupInvitation then
        self:RefreshReceivedInviteUI()
    end
end

function TeamInviteApplyComItem:RefreshTeammateUI()
    for i = 1, TEAM_SIZE_LIMIT - 1, 1 do
        if self.Data.teamMemberInfo and self.Data.teamMemberInfo[i] then
            self.TeamMember_Head[i]:Refresh(
                    {
                        ProfessionID = self.Data.teamMemberInfo[i].profession,
                        Level = self.Data.teamMemberInfo[i].level,
                        EntityID = self.Data.teamMemberInfo[i].memberID,
                        bIsCaptain = false,
                        Size = FVector2D(54, 54),
                        isGroup = false,
                        captainState = 0,
                        OnClickedCallBack = function()
                            self:ShowPlayerInfo(self.Data.teamMemberInfo[i].memberID)
                        end
                    }
            )
        else
            self.TeamMember_Head[i]:Refresh(
                    {
                        EntityID = "",
                        ProfessionID = 0,
                        Level = 0,
                        bIsCaptain = false,
                        Size = FVector2D(54, 54),
                        isGroup = false,
                        captainState = 0
                    }
            )
        end
    end
end

function TeamInviteApplyComItem:UpdateReadyState(bCancle, bFirst, ConfirmInfoList)
    for _, Info in pairs(ConfirmInfoList) do
        if Info.AvatarActorID == self.AvatarActorID then
            if Info.bConfirmed then
                self.view.WidgetSwitcher:SetActiveWidgetIndex(1)
            end
            break
        end
    end
end

return TeamInviteApplyComItem
