local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComInputBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComInputBox")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class TeamRequestMessage_Panel : UIPanel
---@field view TeamRequestMessage_PanelBlueprint
local TeamRequestMessage_Panel = DefineClass("TeamRequestMessage_Panel", UIPanel)

local StringConst = require "Data.Config.StringConst.StringConst"

TeamRequestMessage_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TeamRequestMessage_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TeamRequestMessage_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function TeamRequestMessage_Panel:InitUIComponent()
    ---@type UIComBoxFrame
    self.WBP_ComPopupMCom = self:CreateComponent(self.view.WBP_ComPopupM, UIComBoxFrame)
    ---@type UIComButton
    self.WBP_ConfirmCom = self:CreateComponent(self.view.WBP_Confirm, UIComButton)
    ---@type UIComInputBox
    self.WBP_ComInputBigCom = self:CreateComponent(self.view.WBP_ComInputBig, UIComInputBox)
end

---UI事件在这里注册，此处为自动生成
function TeamRequestMessage_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Clear.OnClicked, "on_Btn_Clear_Clicked")
    self:AddUIEvent(self.WBP_ConfirmCom.onClickEvent, "on_WBP_ConfirmCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TeamRequestMessage_Panel:InitUIView()
    self:AddUIEvent(self.WBP_ComInputBigCom.onTextChanged, "OnDiscriptTextChanged")
end


--- 此处为自动生成
function TeamRequestMessage_Panel:on_WBP_ConfirmCom_ClickEvent()
    self:OnClick_WBP_Confirm_Btn_Com()
end

--- 此处为自动生成
function TeamRequestMessage_Panel:on_Btn_Clear_Clicked()
    self:OnClick_Btn_Clear()
end


function TeamRequestMessage_Panel:OnRefresh()
    self.WBP_ComPopupMCom:Refresh(StringConst.Get("TEAM_APPLY_MESSAGE"))
    self.WBP_ConfirmCom:SetName(StringConst.Get("TEAM_APPLY_MESSAGE_SAVE"))
    --self.WBP_ComInputBigCom:SetData({
    --    Owner = self,
    --    OnValueChangedCallback = self.OnDiscriptTextChanged,
    --    InputLimit = 10,
    --    CnCharCount = 2,
    --    HintText = StringConst.Get("TEAM_INPUT")
    --})
    self.WBP_ComInputBigCom:SetLimitLength(10)

    self.WBP_ComInputBigCom:SetHintText(StringConst.Get("TEAM_INPUT"))
    self.WBP_ComInputBigCom:SetText(Game.me.joinTeamDescription)

    -- When first shown, self:GetInputTextLen() cannot give the correct length
    self.view.Text_Info:SetText(string.format(StringConst.Get("TEAM_MESSAGE_NUM_LIMIT"), utf8.len(Game.me.joinTeamDescription),
            10 - self:GetInputTextLen()))
end

function TeamRequestMessage_Panel:OnDiscriptTextChanged()
    self.view.Text_Info:SetText(string.format(StringConst.Get("TEAM_MESSAGE_NUM_LIMIT"), self:GetInputTextLen(),
            10 - self:GetInputTextLen()))
end

function TeamRequestMessage_Panel:GetInputTextLen()
    local str = self.WBP_ComInputBigCom:GetText()
    return utf8.len(str)
end

function TeamRequestMessage_Panel:OnClick_WBP_ComPopupM_WBP_ComPopupTitle_WBP_ComBtnClose_Button()
    self:CloseSelf()
end

function TeamRequestMessage_Panel:OnClick_WBP_Confirm_Btn_Com()
    local Message = self.WBP_ComInputBigCom:GetText()
    Game.me:ReqSetJoinTeamDescription(Message)
    self:CloseSelf()
end

function TeamRequestMessage_Panel:OnClick_Btn_Clear()
    self.WBP_ComInputBigCom:SetText("")
end


return TeamRequestMessage_Panel
