local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class TeamSetTargetItem : UIListItem
---@field view TeamSetTargetItemBlueprint
local TeamSetTargetItem = DefineClass("TeamSetTargetItem", UIListItem)


local const = kg_require("Shared.Const")
local StringConst = require "Data.Config.StringConst.StringConst"
local ESlateVisibility = import("ESlateVisibility")


TeamSetTargetItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TeamSetTargetItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TeamSetTargetItem:InitUIData()
    self.Locked = false
    self.TargetID = nil
end

--- UI组件初始化，此处为自动生成
function TeamSetTargetItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function TeamSetTargetItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Btn_Collect.OnClicked, "OnClick_Btn_Collect")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TeamSetTargetItem:InitUIView()
end

---面板打开的时候触发
function TeamSetTargetItem:OnRefresh(data)
    self:Refresh(data.data, data.Selected, data.Locked)
end


function TeamSetTargetItem:OnClick_Btn_Collect()
    if self.TargetID ~= const.DEFAULT_ALL_TARGET_ID and self.TargetID ~= const.DEFAULT_NO_TARGET_ID then
        local TeamCollectList = GetMainPlayerPropertySafely("teamCollectList")
        if table.contains(TeamCollectList, self.TargetID) then
            Game.me:ReqTeamCollectTarget(self.TargetID, 1)
        else
            Game.me:ReqTeamCollectTarget(self.TargetID, 0)
        end
    end
end

function TeamSetTargetItem:on_Btn_ClickArea_Clicked()
    --UI.Invoke(UIPanelConfig.TeamSetTarget_Panel, "OnClick_SB_Content", self.index)
end

function TeamSetTargetItem:Refresh(Params, Selected, Locked)
    if Selected then
        self.view.WBP_ComSelected:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:PlayAnimation(self.view.WBP_ComSelected.Ani_Fadein, nil, self.view.WBP_ComSelected)
    else
        self.view.WBP_ComSelected:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.Locked = Locked
    self.TargetID = Params.MarkId
    local TargetTableTable = Game.TableData.GetTargetDataRow(self.TargetID)
    self.view.Text_Name_Big:SetText(TargetTableTable.Name)
    self:SetImage(self.view.Img_bg_show, TargetTableTable.PagePath)
    self.view.Overlay_Time:SetVisibility(ESlateVisibility.Collapsed)
    self:SetCollectionLockState()
    if self.TargetID ~= const.DEFAULT_ALL_TARGET_ID and self.TargetID ~= const.DEFAULT_NO_TARGET_ID then
        if TargetTableTable then
            local TrickID = TargetTableTable.Trick
            local DungeonTableData = Game.TableData.GetDungeonDataRow(TrickID)
            -- self.view.Overlay_Collect:SetVisibility(ESlateVisibility.selfHitTestInvisible)
            self.view.HB_Num:SetVisibility(ESlateVisibility.selfHitTestInvisible)
            if DungeonTableData then
                self.view.Text_Num:SetText(DungeonTableData.MinPlayerLimit .. "-" .. DungeonTableData.MaxPlayerLimit)
            end
            local PVPTableData = Game.TableData.GetPVPGameModeDataRow(TrickID)
            if PVPTableData then
                self.view.Text_Num:SetText(PVPTableData.PlayerNum)
            end
            local WorldBossData = Game.TableData.GetWorldBossDataRow(TrickID)
            if WorldBossData then
                self.view.Text_Num:SetText(StringConst.Get("TEAM_NOLIMIT"))
            end
        end
    else
        -- self.view.Overlay_Collect:SetVisibility(ESlateVisibility.Collapsed)
        self.view.HB_Num:SetVisibility(ESlateVisibility.Collapsed)
    end
    self.view.Overlay_Collect:SetVisibility(ESlateVisibility.Collapsed)
end

function TeamSetTargetItem:SetCollectionLockState()
    if self.TargetID == const.DEFAULT_ALL_TARGET_ID or self.TargetID == const.DEFAULT_NO_TARGET_ID then
        self.userWidget:Event_UI_Style(false, not self.Locked)
    else
        local TeamCollectList = GetMainPlayerPropertySafely("teamCollectList")
        if table.contains(TeamCollectList, self.TargetID) then
            self.userWidget:Event_UI_Style(true, not self.Locked)
        else
            self.userWidget:Event_UI_Style(false, not self.Locked)
        end
    end
end


return TeamSetTargetItem
