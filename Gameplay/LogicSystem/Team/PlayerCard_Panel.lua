local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local PlayerMenuUtils = kg_require "Gameplay.LogicSystem.Team.PlayerMenuUtils"
local StringConst = require "Data.Config.StringConst.StringConst"
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local ESlateVisibility = import("ESlateVisibility")
local const = kg_require("Shared.Const")
local FAnchors = import("Anchors")

local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class PlayerCard_Panel : UIComponent
---@field view PlayerCard_PanelBlueprint
local PlayerCard_Panel = DefineClass("PlayerCard_Panel", UIPanel)

PlayerCard_Panel.eventBindMap = {
    [EEventTypesV2.PLAYERCARD_UPDATE] = "OnPlayerInfoUpdate",
    [_G.EEventTypes.ON_ROLE_ATTR_BRIEF] = "OnRoleAttrBrief",
    [_G.EEventTypes.SERVER_BATCHINFO_UPDATE] = "UpdateTeamTarget",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerCard_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerCard_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function PlayerCard_Panel:InitUIComponent()
    ---@type UIListView childScript: MemberInfoItem
    self.WBP_TileViewCom = self:CreateComponent(self.view.WBP_TileView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function PlayerCard_Panel:InitUIEvent()
    self:AddUIEvent(self.view.GuildBtn.OnClicked, "OnClick_GuildBtn")
    self:AddUIEvent(self.view.FashionBtn.OnClicked, "OnClick_FashionBtn")
    self:AddUIEvent(self.WBP_TileViewCom.onItemClicked, "OnItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerCard_Panel:InitUIView()
    ---@type table 按钮数据
    self.ButtonMap = nil
end

function PlayerCard_Panel:OnClose()
    self.PlayerMenuUtils = nil
end

function PlayerCard_Panel:Refresh(Params)
    if UIManager:GetInstance():CheckLayoutInStack(Enum.EUILayout.LeftHalfScreen) then
        self:SetRightPosition()
    elseif UIManager:GetInstance():CheckLayoutInStack(Enum.EUILayout.RightHalfScreen) then
        self:SetLeftPosition()
    else
        self:SetCenterPosition()
    end
    
    ---@type PlayerMenuUtils
    self.PlayerMenuUtils = PlayerMenuUtils.new(Params, Params.MenuType)
    --local isSameToLast = self.EntityID == Params.EntityID
    self.EntityID = Params.EntityID
    self.Name = Params.Name
    self.ProfessionID = Params.ProfessionID
    self.Level = Params.Level
    self.bIsCaptain = Params.IsCaptain
    self.FriendInfo = Game.FriendSystem:GetRelationInfoByEntityID(self.EntityID)
    self.GuildID = Params.GuildID
    self.GuildName = Params.GuildName
    self.sourceID = Params.sourceID or Enum.EFriendAddSourceData.DECISIVE_ARENA
    self.GroupID = Params.GroupID
    self.bGroupLeader = Params.bGroupLeader
    self.Sex = Params.Sex or 0
    self.CE = Params.CE
    self.customFaceData = Params.customFaceData
    self.fashionData = Params.fashionData
    self.pvpData = Params.pvpData
    self.TeamID = Params.TeamID
    self.LineType = Params.LineType
    self.MapType = Params.MapType
    self.TeamTarget = ""
    if (self.TeamID and self.TeamID ~= 0) or (self.GroupID and self.GroupID ~= 0) then
        self.view.WBP_PlayerInfoDetail02:Event_UI_Style(false, 1)
        self.view.WBP_PlayerInfoDetail02.Text_Content:SetText("")
        Game.me:ReqTeamBatchQueryInfo(self.TeamID ~= 0 and {self.TeamID} or {}, self.GroupID ~= 0 and {self.GroupID} or {})
    else
        self.view.WBP_PlayerInfoDetail02:Event_UI_Style(true, 1)
        --"暂无队伍"
        self.view.WBP_PlayerInfoDetail02.Text_Content:SetText(StringConst.Get("TEAM_LIST_EMPTY"))
    end
    if string.isEmpty(self.GuildName) then
        self.view.WBP_PlayerInfoDetail01:Event_UI_Style(true, 0)
        self.view.WBP_PlayerInfoDetail01.Text_Content:SetText("暂未加入俱乐部")
        self.view.Canvas_GuildBtn:SetVisibility(ESlateVisibility.Hidden)
    else
        self.view.WBP_PlayerInfoDetail01:Event_UI_Style(false, 0)
        self.view.WBP_PlayerInfoDetail01.Text_Content:SetText(self.GuildName)
        self.view.Canvas_GuildBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end

    local professionRow = Game.TableData.GetPlayerSocialDisplayDataRow(self.ProfessionID)
    if professionRow then
        local tableData = professionRow[self.Sex]
        if tableData then
            self:SetImageSafe(self.view.Img_PlayerCareer, tableData.ClassLogo)
            self:SetImageSafe(self.view.Img_PlayerBust, tableData.Drawing)
            if tableData.Card then
                self.view.Canv_Signet:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                self:SetImageSafe(self.view.Img_SignetIcon, tableData.Card)
                --self.view:Event_UI_Style()
            else
                self.view.Canv_Signet:SetVisibility(ESlateVisibility.Hidden)
            end
        end
    end
    self.view.Text_PlayerName:SetText(self.Name)
    self.CardOtherParams = Params.CardOtherParams
    self.bShowGuildOptions = Params.bShowGuildOptions or false
    self.ButtonMap = self.PlayerMenuUtils:GetMenuList()

    local listData = {}
    for key, value in ipairs(self.ButtonMap) do
        local item = {}

        local rowData = Game.TableData.GetPlayerInteractUnitDataRow(value[1])
        if rowData then
            local btnText = ""
            if value[2] then
                btnText = rowData.Name or ""
            else
                btnText = rowData.NameState1 or ""
            end
            item.btnText = btnText
            item.itemIndex = key
        end
        
        table.insert(listData, item)
    end

    self.WBP_TileViewCom:Refresh(listData)

    self.view.Text_Level:SetText(self.Level)

    if self.fashionData.viewOpen ~= 0 then
        local _, level, _, _  = Game.FashionSystem:GetFashionLevelNameAndScore(self.fashionData.fashionScore)
        local fashionLevelInfo = Game.TableData.GetFashionLevelDataRow(level)
        self.view.Img_FashionIcon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        if fashionLevelInfo and string.notNilOrEmpty(fashionLevelInfo.Levelicon) then
            self:SetImageSafe(self.view.Img_FashionIcon, fashionLevelInfo.Levelicon)
        end
        self.view.FashionBtn:SetVisibility(ESlateVisibility.Visible)
    else
        self.view.Img_FashionIcon:SetVisibility(ESlateVisibility.Hidden)
        self.view.FashionBtn:SetVisibility(ESlateVisibility.Hidden)
    end

    -- todo:未来接入
    if self.pvpData.viewOpen ~= 0 then
        self.view.WBP_PlayerInfoRank:Event_UI_Style(true,false,false,false)
    else
        self.view.WBP_PlayerInfoRank:Event_UI_Style(true,false,false,false)
    end
end

---@param list table<number, number>
function PlayerCard_Panel:GetSumTable(list)
    local sum = 0
    for _, value in pairs(list) do
        sum = sum + value
    end
    return sum
end

function PlayerCard_Panel:UpdateTeamTarget(Result, BatchQueryTeamInfoList, BatchQueryGroupInfoList)

    if BatchQueryGroupInfoList and #BatchQueryGroupInfoList > 0 and BatchQueryGroupInfoList[1].groupID == self.GroupID then
        self.targetID = BatchQueryGroupInfoList[1].details.targetID
        self.teamMemberNum = self:GetSumTable(BatchQueryGroupInfoList[1].memberInfos)
        self.teamMemberMax = Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") * Game.TableData.GetConstDataRow("GROUP_TEAM_COUNT")
        if self.targetID ~= nil and self.targetID ~= const.DEFAULT_ALL_TARGET_ID and self.targetID ~= const.DEFAULT_NO_TARGET_ID then
            local TargetTableData = Game.TableData.GetTargetDataRow(self.targetID)
            if TargetTableData then
                self.view.WBP_PlayerInfoDetail02.Text_Content:SetText(TargetTableData.Name.."("..tostring(self.teamMemberNum).."/"..tostring(self.teamMemberMax)..")")
            end
        else
            self.view.WBP_PlayerInfoDetail02.Text_Content:SetText(StringConst.Get("TEAM_NOTARGET").."("..tostring(self.teamMemberNum).."/"..tostring(self.teamMemberMax)..")")
        end

    elseif BatchQueryTeamInfoList and #BatchQueryTeamInfoList > 0 and BatchQueryTeamInfoList[1].teamID == self.TeamID then
        self.targetID = BatchQueryTeamInfoList[1].targetID
        self.teamMemberNum = #(BatchQueryTeamInfoList[1].teamMemberInfo)+1
        self.teamMemberMax = Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT")
        if self.targetID ~= const.DEFAULT_ALL_TARGET_ID and self.targetID ~= const.DEFAULT_NO_TARGET_ID then
            local TargetTableData = Game.TableData.GetTargetDataRow(self.targetID)
            if TargetTableData then
                self.view.WBP_PlayerInfoDetail02.Text_Content:SetText(TargetTableData.Name.."("..tostring(self.teamMemberNum).."/"..tostring(self.teamMemberMax)..")")
            end
        else
            self.view.WBP_PlayerInfoDetail02.Text_Content:SetText(StringConst.Get("TEAM_NOTARGET").."("..tostring(self.teamMemberNum).."/"..tostring(self.teamMemberMax)..")")
        end
    end
end


function PlayerCard_Panel:OnPlayerInfoUpdate()
    self.FriendInfo = Game.FriendSystem:GetRelationInfoByEntityID(self.EntityID)
end

function PlayerCard_Panel:OnExit()
    self.PlayerMenuUtils = nil
    Game.TeamSystem:HidePlayerCardUI()
end

function PlayerCard_Panel:OnRoleAttrBrief(Result, AttrBrief)
    if Result == Game.NetworkManager.ErrCodes.NO_ERR then
        self:OnExit()
        for key, value in pairs(AttrBrief.briefAttr) do
            AttrBrief.briefAttr[key] = value
        end
        Game.RoleDisplaySystem.ShowOtherPlayerDisplay(
                self.EntityID,
                self.Name,
                self.Level,
                self.ProfessionID,
                self.Sex,
                AttrBrief
        )
    end
end

function PlayerCard_Panel:OnQueryOtherPlayerOnlineStateRespond(entityID, onlineState)
    if self.PlayerMenuUtils and self.PlayerMenuUtils:TagetInSameTeaRoom() then
        Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerGivePanel, self.EntityID, self.Name, Enum.SendGiftType.TeaRoom)
    else
        Game.NewUIManager:OpenPanel(UIPanelConfig.PartnerGivePanel, self.EntityID, self.Name, Enum.SendGiftType.Normal)
    end
end

function PlayerCard_Panel:OnClick_FashionBtn()
    if self.fashionData then
        local name, level, curScore, targetScore  = Game.FashionSystem:GetFashionLevelNameAndScore(self.fashionData.fashionScore)
        local Position =
        SlateBlueprintLibrary.LocalToAbsolute(
                self.view.FashionBtn:GetCachedGeometry(),
                FVector2D(50,-130)
        )
        local relativePos = SlateBlueprintLibrary.AbsoluteToLocal(
                self.userWidget:GetCachedGeometry(), Position)
        if not UI.IsShow(UIPanelConfig.PlayerCardSmallTips_Panel) then
            local tipsData = Game.TableData.GetTipsDataRow(Enum.ETipsData.PLAYCARD_FASHION_TIPS)
            local text = ""
            for key,value in ksbcipairs(tipsData.Description1) do
                text = text .. value
                if key ~= #tipsData.Description1 then
                    text = text .. "\n"
                end
            end
            UI.ShowUI(UIPanelConfig.PlayerCardSmallTips_Panel,{Title = string.format(tipsData.Title, name), Content = string.format(text, level, curScore, targetScore), Pos = relativePos})
        end
    end
end

function PlayerCard_Panel:OnClick_GuildBtn()
    -- todo:等俱乐部拜访功能加入后再补充
    Game.GuildSystem:SeeGuildMainPage(self.GuildID, self.GuildName)
end

---整个弹窗居中显示
function PlayerCard_Panel:SetCenterPosition()
    local NewAnchors = FAnchors()
    NewAnchors.Minimum = FVector2D(0.5, 0.5)
    NewAnchors.Maximum = FVector2D(0.5, 0.5)
    self.view.CP.Slot:SetAnchors(NewAnchors)
end

---整体弹窗居左显示
function PlayerCard_Panel:SetLeftPosition()
    local NewAnchors = FAnchors()
    NewAnchors.Minimum = FVector2D(0.33, 0.5)
    NewAnchors.Maximum = FVector2D(0.33, 0.5)
    self.view.CP.Slot:SetAnchors(NewAnchors)
end

---整体弹窗居右显示
function PlayerCard_Panel:SetRightPosition()
    local NewAnchors = FAnchors()
    NewAnchors.Minimum = FVector2D(0.67, 0.5)
    NewAnchors.Maximum = FVector2D(0.67, 0.5)
    self.view.CP.Slot:SetAnchors(NewAnchors)
end

function PlayerCard_Panel:SetImageSafe(imageView, path)
    --策划没配置图片路径 一直提bug
    if imageView and string.notNilOrEmpty(path) then
        self:SetImage(imageView, path)
    end
end

function PlayerCard_Panel:OnItemClicked(index, itemData)
    local itemIndex = itemData.itemIndex
    local itemName = self.ButtonMap[itemIndex][1]
    local func = self.PlayerMenuUtils[itemName]
    if func then
        func(self.PlayerMenuUtils, self.ButtonMap[itemIndex][2])
    end

    if itemName ~= "ViewInformation" and itemName ~= "SendGift" then
        self:CloseSelf()
    end
end

return PlayerCard_Panel
