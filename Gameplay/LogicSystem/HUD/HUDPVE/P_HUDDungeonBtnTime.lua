

---@class P_HUDDungeonBtnTime : UIController
---@field public View WBP_HUDDungeonBtnTimeView
local P_HUDDungeonBtnTime = DefineClass("P_HUDDungeonBtnTime", UIController)
local BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID = kg_require("Shared.Const").BUFF_NOT_CHECK_SOURCE_INSTIGATOR_ID

P_HUDDungeonBtnTime.EDataType =
{
    FromTable = 1,
    Buff = 2,
}

P_HUDDungeonBtnTime.EDisplayType =
{
    Normal = 1,
    Percentage = 2,
    SourceOnly = 3,
}

function P_HUDDungeonBtnTime:OnCreate()
    self.id = nil
    self.targetBuffId = nil
    self.targetBuffLayer = nil
    self.currentBuffInst = nil
end

function P_HUDDungeonBtnTime:OnRefresh(param)
    self.id = param.id
	self.entityID = param.entityID
	Log.Debug("P_HUDDungeonBtnTime:OnRefresh", self.id, self.entityID)
	if self.entityID then
		self.targetEntity = Game.EntityManager:getEntity(self.entityID)
	else
		self.targetEntity = Game.me
	end
    Game.PlayerInfoManager:RegisterRefreshBuff(self)
	self:CloseOnTime()
    self:UpdateUI()
end

function P_HUDDungeonBtnTime:OnClose()
    UIBase.OnClose(self)
    Game.PlayerInfoManager:UnregisterRefreshBuff(self)
end

function P_HUDDungeonBtnTime:UpdateUI()
    local tableData = Game.TableData.GetCountTipsDataRow(self.id)
    self.View.Text_Des:SetText(tableData.Description)
    local source, target
    if tableData.Data1Type == P_HUDDungeonBtnTime.EDataType.Buff then
        source = self:EvaluateBuffType(tableData.Data1Param1) or 0
    elseif tableData.Data1Type == P_HUDDungeonBtnTime.EDataType.FromTable then
        source = self:EvaluateFromTableType(tableData.Data1Param1)
    end
	if not source then
		Log.DebugErrorFormat("P_HUDDungeonBtnTime:UpdateUI source nil, id = %d, Data1Type = %d, Data1Param1 = %d", self.id, tableData.Data1Type, tableData.Data1Param1)
	end
    if tableData.Data2Type == P_HUDDungeonBtnTime.EDataType.Buff then
        target = self:EvaluateBuffType(tableData.Data2Param1)
    elseif tableData.Data2Type == P_HUDDungeonBtnTime.EDataType.FromTable then
        target = self:EvaluateFromTableType(tableData.Data2Param1)
    end
    if tableData.DisplayType == P_HUDDungeonBtnTime.EDisplayType.Normal then
        self.View.Text_Progress:SetText(string.format("%d/%d", source, target))
        self.View:SetCD(source/target)
    elseif tableData.DisplayType == P_HUDDungeonBtnTime.EDisplayType.Percentage then
        self.View.Text_Progress:SetText(string.format("%d%%", source/target * 100))
        self.View:SetCD(source/target)
    elseif tableData.DisplayType == P_HUDDungeonBtnTime.EDisplayType.SourceOnly then
        self.View.Text_Progress:SetText(string.format("%d", source))
        self.View:SetCD(0)
    end
end

function P_HUDDungeonBtnTime:EvaluateBuffType(buffId)
    self.targetBuffId = buffId
    self.targetBuffLayer = 0
    self.currentBuffInst = nil
	if not self.targetEntity or not self.targetEntity.CharacterID then
		return
	end
    local buffData = Game.PlayerInfoManager:GetTargetBuffs(self.targetEntity:uid())
    for i = 1, #buffData do
        local entity = Game.EntityManager:getEntity(buffData[i].OwnerEID)
        local buffInst = entity:GetBuffInstanceByBuffIDAndInstigatorID(buffData[i].buffID, buffData[i].instigatorID)
        if buffInst then
			if buffInst.buffID == self.targetBuffId then
                self.currentBuffInst = buffInst
                self.targetBuffLayer = buffInst:GetCurrentLayer()
				break
            end
        end
    end
    return self.targetBuffLayer
end

function P_HUDDungeonBtnTime:EvaluateFromTableType(param)
    return param
end

function P_HUDDungeonBtnTime:GetTargetEntityID()
	if self.targetEntity and not self.targetEntity.isDestroyed then
		return self.targetEntity:uid()
	end
	return nil
end

function P_HUDDungeonBtnTime:RefreshBuffs(_, eid, buffData, countdownBuffs)
	if not self.targetEntity or self.targetEntity.isDestroyed then
		return
	end
	if eid == self.targetEntity:uid() then
        for i = 1, #buffData do
            local entity = Game.EntityManager:getEntity(buffData[i].OwnerEID)
            local buffInst = entity:GetBuffInstanceByBuffIDAndInstigatorID(buffData[i].buffID, buffData[i].instigatorID)
            if buffInst then
                if buffInst.buffID == self.targetBuffId then
                    self.currentBuffInst = buffInst
                    self.targetBuffLayer = buffInst:GetCurrentLayer()
                    self:UpdateUI()
                    return
                end
            end
        end
    end
    if self.targetBuffLayer ~= 0 then
        self:UpdateUI()
    end
end

function P_HUDDungeonBtnTime:OnIdle()
    if self.currentBuffInst then
        local layer = self.currentBuffInst:GetCurrentLayer()
        if layer ~= self.targetBuffLayer then
            self.targetBuffLayer = layer
            self:UpdateUI()
        end
    end
end

function P_HUDDungeonBtnTime:CloseOnTime()
	local tableData = Game.TableData.GetCountTipsDataRow(self.id)
	if tableData then
		self:StartTimer("CloseSelfTimer", function() self:CloseSelf() end, tableData.DestroyTime * 1000, 1, true)
	end
end

return P_HUDDungeonBtnTime