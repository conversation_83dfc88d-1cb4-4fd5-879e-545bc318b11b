local UIComTextSearchBox = kg_require("Framework.KGFramework.KGUI.Component.Input.UIComTextSearchBox")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComEmptyConent = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComEmptyConent")
local HUDSocialActionSelectPlayer = kg_require("Gameplay.LogicSystem.HUD.HUD_SocialAction.HUDSocialActionSelectPlayer")
local HUDSocialClooectBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_SocialAction.HUDSocialClooectBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class HUDSocialAction_Panel : UIPanel
---@field view HUDSocialAction_PanelBlueprint
local HUDSocialAction_Panel = DefineClass("HUDSocialAction_Panel", UIPanel)
local ESlateVisibility =  import("ESlateVisibility")
local StringConst = require("Data.Config.StringConst.StringConst")

HUDSocialAction_Panel.eventBindMap = {
	[EEventTypesV2.ON_SOCIAL_ACTION_LOCK_CHANGED] = "OnRefreshActionDataList",
	[EEventTypesV2.ON_DUAL_ACTION_SELECT_PLAYER] = "SelectPlayer",
	[EEventTypesV2.ON_REFRESH_SOCIAL_ACTION_PANEL] = "OnRefreshActionDataList",
}

HUDSocialAction_Panel.UIEvent =
{
	DEFAULT = 0,        --正常界面
	COLLECTNONE = 1,    --点进收藏页面没有东西
	SAVETIPS = 2,       --显示savetips
	INVITE = 3,         --邀请玩家
	COLLECTDEFAULT = 4, --点选收藏状态
	SEARCHNOTNONE = 5,   --搜索框输入了内容
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSocialAction_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSocialAction_Panel:InitUIData()
	---@type integer 当前所在的标签
	self.tabListIndex = nil
	---@type string 搜索栏输入
	self.searchKeyWord = nil
	---@type table 当前所在的标签下的内容
	self.currentActionList = nil
	---@type table 当前选中的内容
	self.selectedActionIDList = nil
	---@type boolean = 是否在保存tips中
	self.bInSaveTips = false
	---@type string 选择的人eid
	self.preSelectPlayer = nil 
	---@type number
	self.prevInviteItemIndex = nil
end

--- UI组件初始化，此处为自动生成
function HUDSocialAction_Panel:InitUIComponent()
    ---@type UIComButton
    self.Cancel_BtnCom = self:CreateComponent(self.view.Cancel_Btn, UIComButton)
    ---@type UIComButton
    self.Collect_BtnCom = self:CreateComponent(self.view.Collect_Btn, UIComButton)
    ---@type UIListView
    self.ActionListCom = self:CreateComponent(self.view.ActionList, UIListView)
    ---@type UIComTextSearchBox
    self.WBP_ComInputCom = self:CreateComponent(self.view.WBP_ComInput, UIComTextSearchBox)
    ---@type UIComButton
    self.WBP_ComBtnCloseNewCom = self:CreateComponent(self.view.WBP_ComBtnCloseNew, UIComButton)
    ---@type UIListView
    self.KGListViewCom = self:CreateComponent(self.view.KGListView, UIListView)
    ---@type UIComEmptyConent
    self.WBP_ItemEmptyCom = self:CreateComponent(self.view.WBP_ItemEmpty, UIComEmptyConent)
    ---@type HUDSocialActionSelectPlayer
    self.WBP_HUDSocialSelectPlayerCom = self:CreateComponent(self.view.WBP_HUDSocialSelectPlayer, HUDSocialActionSelectPlayer)
    ---@type HUDSocialClooectBtn
    self.WBP_HUDSocialClooectBtnCom = self:CreateComponent(self.view.WBP_HUDSocialClooectBtn, HUDSocialClooectBtn)
end

---UI事件在这里注册，此处为自动生成
function HUDSocialAction_Panel:InitUIEvent()
    self:AddUIEvent(self.KGListViewCom.onItemSelected, "on_KGListViewCom_ItemSelected")
    self:AddUIEvent(self.ActionListCom.onItemClicked, "on_ActionListCom_ItemClicked")
    self:AddUIEvent(self.Collect_BtnCom.onClickEvent, "on_Collect_BtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnCloseNewCom.onClickEvent, "on_WBP_ComBtnCloseNewCom_ClickEvent")
    self:AddUIEvent(self.Cancel_BtnCom.onClickEvent, "on_Cancel_BtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_HUDSocialClooectBtnCom.onClickEvent, "on_WBP_HUDSocialClooectBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComInputCom.onSearchResult, "on_WBP_ComInputCom_SearchResult")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSocialAction_Panel:InitUIView()
	self.WBP_ComInputCom:Refresh(StringConst.Get("SOCIALACTION_SEARCH"), 99)
	-- todo: 组件不进行实际搜索，只是监听文本变化
	self.WBP_ComInputCom:SetSearchSource({{}}, nil)
	self.Collect_BtnCom.view.Text_Name:SetText(StringConst.Get("SOCIALACTION_CANCEL"))
	self.Cancel_BtnCom.view.Text_Name:SetText(StringConst.Get("SOCIALACTION_CONFIRM"))
end

---面板打开的时候触发
function HUDSocialAction_Panel:OnRefresh(enterIndex,preSelectPlayer)
	self.currentActionList = {}
	self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.Collapsed)
	self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.DEFAULT)

	self.selectedActionIDList = DeepCopy(Game.SocialActionSystem:GetCollectedActionIDList())

	---tab标签
	self.tabListIndex = enterIndex or Game.SocialActionSystem:GetEnterTabListIndex()
	---每个tab里的内容
	self:RefreshNewTab()
	
	if enterIndex and preSelectPlayer then
		self:SelectPlayer(preSelectPlayer)
	else
		self.preSelectPlayer = nil
	end
end

function HUDSocialAction_Panel:RefreshNewTab()
	self.tabDataList = Game.SocialActionSystem:GetTabDataList()
	self.KGListViewCom:Refresh(self.tabDataList)
	self.KGListViewCom:SetSelectedItemByIndex(self.tabListIndex or 1, true)
end

function HUDSocialAction_Panel:OnClose()
	Game.SocialActionSystem.tabListIndex = self.tabListIndex
	Game.SocialActionSystem.bInCollectStatus = false
	self.tabListIndex = nil
	self.selectedActionIDList = nil
	self.preSelectPlayer = nil
end

function HUDSocialAction_Panel:SelectPlayer(preSelectPlayer)
	self.preSelectPlayer = preSelectPlayer
	local entity = Game.EntityManager:getEntity(self.preSelectPlayer)
	if not entity then
		Log.Debug("HUDSocialAction_Panel:SelectPlayer entity is nil")
		return
	end
	self.WBP_HUDSocialSelectPlayerCom:ReqDualActionUIEvent(entity.Name)
end

function HUDSocialAction_Panel:OnRefreshTabDataList()
	self:RefreshNewTab()
end

function HUDSocialAction_Panel:OnRefreshActionDataList()
	self.selectedActionIDList = DeepCopy(Game.SocialActionSystem:GetCollectedActionIDList())
	self:RefreshCurrentActionList()
end

function HUDSocialAction_Panel:RefreshCurrentActionList()
	if self.currentActionList and Game.RedPointSystem:IsRedPointActive("SocialAction") then
		for key,value in pairs(self.currentActionList) do
			Game.SocialActionSystem:DeleteOnceRedPoint(value.ID)
		end
	end
	local index = self.tabListIndex
	local actionDatas = {}
	if index == #self.tabDataList then
		actionDatas = Game.SocialActionSystem:GetCollectedActionList()
	else
		actionDatas = Game.SocialActionSystem:GetSocialActionDataList()[index]
	end
	if (self.searchKeyWord and self.searchKeyWord ~= "") and index ~= #self.tabDataList then
		self.currentActionList = Game.SocialActionSystem:GetSearchedList(actionDatas, self.searchKeyWord)
	else
		self.currentActionList = actionDatas
		self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.SEARCHNOTNONE)
	end
	self.ActionListCom:Refresh(self.currentActionList)
	self:RefreshMainUI(index)
end

function HUDSocialAction_Panel:RefreshMainUI(index)
	if #self.currentActionList == 0 then
		self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.Collapsed)
	end
	if index == #self.tabDataList then
		self.userWidget:Event_UI_State(Game.SocialActionSystem.bInCollectStatus and HUDSocialAction_Panel.UIEvent.COLLECTDEFAULT or HUDSocialAction_Panel.UIEvent.COLLECTNONE)
		if next(Game.SocialActionSystem:GetCollectedActionIDList()) then
			self.WBP_HUDSocialClooectBtnCom.userWidget:Event_UI_None(false)
			local bHasDualSocialAction = false
			for key,value in pairs(Game.SocialActionSystem:GetCollectedActionIDList()) do
				local info = Game.TableData.GetSocialActionDataRow(key)
				if value and info then
					if info.type == 2 then
						bHasDualSocialAction = true
						break
					end
				end
			end
			if bHasDualSocialAction and not Game.SocialActionSystem.bInCollectStatus then
				self.WBP_HUDSocialSelectPlayerCom.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
				self:OpenInvitePanel()
			else
				self.WBP_HUDSocialSelectPlayerCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
			end
		else
			self.WBP_HUDSocialClooectBtnCom.userWidget:Event_UI_None(true)
			self.WBP_HUDSocialSelectPlayerCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		if Game.SocialActionSystem.bInCollectStatus then
			self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.COLLECTDEFAULT)
			self.WBP_HUDSocialClooectBtnCom.userWidget:Event_UI_None(false)
		else
			self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.DEFAULT)
			self.WBP_HUDSocialClooectBtnCom.userWidget:Event_UI_None(true)
		end
	end
	if index == 2 and not Game.SocialActionSystem.bInCollectStatus then
		self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.INVITE)
		self:OpenInvitePanel()
	end
end
-- endregion

-- region 收藏界面下的蓝色按钮
function HUDSocialAction_Panel:on_Collect_BtnCom_ClickEvent()
	self.Collect_BtnCom.view.Text_Name:SetText(StringConst.Get("SOCIALACTION_SAVE"))
	if Game.SocialActionSystem.bInCollectStatus then
		--点击的时候，正在收藏状态，因此需要退出
		if self.bInSaveTips then
			self.bInSaveTips = false
			self:JumpToPage(self.tabListIndex)
			return
		end
		Game.SocialActionSystem.bInCollectStatus = false
		Game.SocialActionSystem:ChangeCollected(self.selectedActionIDList)
		if self.tabListIndex == #self.tabDataList then
			self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.COLLECTNONE)
		else
			self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.DEFAULT)
		end
		self:JumpToPage(#self.tabDataList)
	else
		--点击的时候，不在收藏状态，因此需要变为保存
		--进入管理的状态
		self.WBP_ComInputCom:on_Btn_Clear_Clicked()
		Game.SocialActionSystem.bInCollectStatus = true
		self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.COLLECTDEFAULT)
		if next(Game.SocialActionSystem:GetCollectedActionIDList()) then
			self:JumpToPage(#self.tabDataList)
		else
			self:JumpToPage(1)
		end
	end
end
--endregion

---@检查点选的action是不是和收藏的action一样
function HUDSocialAction_Panel:NeededPopSaveTips()
	local A = self.selectedActionIDList
	local B = Game.SocialActionSystem:GetCollectedActionIDList()
	local bEquals = true

	for k,v in pairs(A) do
		if not B[k] or B[k]~=v then
			bEquals = false
			break
		end
	end
	for k,v in pairs(B) do
		if not A[k] or A[k]~=v then
			bEquals = false
			break
		end
	end

	if not bEquals then
		self.bInSaveTips = true
		self.Collect_BtnCom.view.Text_Name:SetText(StringConst.Get("SOCIALACTION_CANCEL"))
		self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.SAVETIPS)
		self.view.WBP_ItemEmpty:SetVisibility(ESlateVisibility.Collapsed)
	end
	return bEquals
end

function HUDSocialAction_Panel:OpenInvitePanel()
	self.WBP_HUDSocialSelectPlayerCom:OnOpenInvitePanel()
end

function HUDSocialAction_Panel:JumpToPage(pageNum)
	self.KGListViewCom:SetSelectedItemByIndex(pageNum, true)
	self.KGListViewCom:ScrollToItemByIndex(pageNum)
	if self.tabListIndex == pageNum then
		self:RefreshCurrentActionList()
	end
end

function HUDSocialAction_Panel:UpdateBoundRectWidgets()
	local widget = self.widget
	widget:AddPanelRegionWidget(self.WBP_HUDSocialSelectPlayerCom.view.VB_Btn)
end

---界面的关闭按钮回调
function HUDSocialAction_Panel:on_WBP_ComBtnCloseNewCom_ClickEvent()
	if Game.SocialActionSystem.bInCollectStatus then
		if self:NeededPopSaveTips() == false then
			return
		end
	end
	self:CloseSelf()
end

---收藏界面下的黄色按钮
function HUDSocialAction_Panel:on_Cancel_BtnCom_ClickEvent()
	self.bInSaveTips = false
	Game.SocialActionSystem:CloseSocialActionPanelWithOutSave()
end

--- 此处为自动生成
function HUDSocialAction_Panel:on_WBP_HUDSocialClooectBtnCom_ClickEvent()
	self.Collect_BtnCom.view.Text_Name:SetText(StringConst.Get("SOCIALACTION_SAVE"))
	if Game.SocialActionSystem.bInCollectStatus then
		--点击的时候，正在收藏状态，因此需要退出
		Game.SocialActionSystem.bInCollectStatus = false
		Game.SocialActionSystem:ChangeCollected(self.selectedActionIDList)
		if self.tabListIndex == #self.tabDataList then
			self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.COLLECTNONE)
		else
			self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.DEFAULT)
		end
		self:JumpToPage(#self.tabDataList)
	else
		--点击的时候，不在收藏状态，因此需要变为保存
		--进入管理的状态
		self.WBP_ComInputCom:on_Btn_Clear_Clicked()
		Game.SocialActionSystem.bInCollectStatus = true
		self.userWidget:Event_UI_State(HUDSocialAction_Panel.UIEvent.COLLECTDEFAULT)
		if next(Game.SocialActionSystem:GetCollectedActionIDList()) then
			self:JumpToPage(#self.tabDataList)
		else
			self:JumpToPage(1)
		end
	end
end

--- 此处为自动生成
---@param index number
---@param data table
function HUDSocialAction_Panel:on_KGListViewCom_ItemSelected(index, data)
	self.tabListIndex = index
	self:RefreshCurrentActionList()
end

--- 此处为自动生成
---@param searchResults table
function HUDSocialAction_Panel:on_WBP_ComInputCom_SearchResult(searchResults)
	-- todo: 组件不进行实际搜索，只是监听文本变化
	self.searchKeyWord = self.WBP_ComInputCom.view.EditText_Input:GetText()
	self:RefreshCurrentActionList()
end

--- 此处为自动生成
---@param index number
---@param data table
function HUDSocialAction_Panel:on_ActionListCom_ItemClicked(index, data)
	---@type HUDSocialAction_Item
	local item = self.ActionListCom:GetItemByIndex(index)
	item:OnClick_Btn_PlayAction()
	item:UIChangeState()

	if self.prevInviteItemIndex then
		item = self.ActionListCom:GetItemByIndex(self.prevInviteItemIndex)
		if item then
			item:UIChangeState()
		end
	end

	self.prevInviteItemIndex = index
end

return HUDSocialAction_Panel
