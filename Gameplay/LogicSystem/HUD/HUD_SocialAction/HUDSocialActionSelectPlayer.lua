local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local StringConst = require("Data.Config.StringConst.StringConst")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSocialActionSelectPlayer : UIComponent
---@field view HUDSocialActionSelectPlayerBlueprint
local HUDSocialActionSelectPlayer = DefineClass("HUDSocialActionSelectPlayer", UIComponent)

HUDSocialActionSelectPlayer.eventBindMap = {
}
---面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSocialActionSelectPlayer:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSocialActionSelectPlayer:InitUIData()
	---@type boolean 是否打开列表
	self.openList = false
	---@type table 玩家信息
	self.playerInfoList = {}
end

---UI组件初始化，此处为自动生成
function HUDSocialActionSelectPlayer:InitUIComponent()
    ---@type UIListView
    self.List_PlayerCom = self:CreateComponent(self.view.List_Player, UIListView)
end

---UI事件在这里注册，此处为自动生成
function HUDSocialActionSelectPlayer:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Target.OnClicked, "on_Btn_Target_Clicked")
    self:AddUIEvent(self.List_PlayerCom.onItemSelected, "on_List_PlayerCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSocialActionSelectPlayer:InitUIView()
end

---组件刷新统一入口
function HUDSocialActionSelectPlayer:Refresh(...)
end

function HUDSocialActionSelectPlayer:OnOpen()
	self.userWidget:Event_UI_SelectPlayer(false)
	self.userWidget:UI_Event_SelectTargetPlayer(false)
	self.userWidget:Event_UI_Arrow(false,true)
end

function HUDSocialActionSelectPlayer:UpdateNearbyPlayerData()
	local aoiEntityList = Game.EntityManager:getEntityCharacterList()

	self.playerInfoList = {}
	if aoiEntityList then
		for _, entity in pairs(aoiEntityList) do
			if entity ~= Game.me then
				self.playerInfoList[#self.playerInfoList + 1] = {
					name = entity.Name,
					EntityID = entity.eid,
					Distance = (Game.me:GetPosition()-entity:GetPosition()):Size()
				}
			end
		end
	end
	table.sort(self.playerInfoList,function(a, b)
		if a.Distance~=b.Distance then
			return a.Distance < b.Distance
		end
	end)
	self.playerInfoList = table.slice(self.playerInfoList, 1, math.min(10,#self.playerInfoList))
	self.userWidget:Event_UI_SelectPlayer(self.openList)
	self.userWidget:UI_Event_SelectTargetPlayer(false)
	self.userWidget:Event_UI_Arrow(self.openList, next(self.playerInfoList)~=nil)
	self.List_PlayerCom:Refresh(self.playerInfoList)
	if next(self.playerInfoList) then
		self.view.Text_Name:SetText(string.format(StringConst.Get("SOCIALACTION_PLAYER_NUM"), tostring(#self.playerInfoList)))
	else
		self.view.Text_Name:SetText(StringConst.Get("SOCIALACTION_NO_PLAYER"))
	end
end

function HUDSocialActionSelectPlayer:OnRefresh()
	self.openList = false
	self.userWidget:Event_UI_Arrow(self.openList, next(self.playerInfoList)~=nil)
	self.userWidget:UI_Event_SelectTargetPlayer(false)
	self.userWidget:Event_UI_SelectPlayer(self.openList)
	self.List_PlayerCom:Refresh(self.playerInfoList)
end

function HUDSocialActionSelectPlayer:ReqDualPlayAction(actionID, entityID, name)
	-- Game.SocialActionSystem:ReqPlayAction(Game.SocialActionSystem.currentActionID, entityID)
	self:GetParent().preSelectPlayer = entityID
	self:ReqDualActionUIEvent(name)
end

function HUDSocialActionSelectPlayer:ReqDualActionUIEvent(name)
	self.openList = false
	self.userWidget:UI_Event_SelectTargetPlayer(true)
	self.userWidget:Event_UI_SelectPlayer(false)
	self.view.Text_Name:SetText(string.format(StringConst.Get("SOCIALACTION_INVITE_NAME"), name))
end

--- 此处为自动生成
function HUDSocialActionSelectPlayer:on_Btn_Target_Clicked()
	if not self.playerInfoList or not next(self.playerInfoList) then return end
	if not self.openList then
		self.userWidget:Event_UI_Arrow(not self.openList, next(self.playerInfoList)~=nil)
		self.userWidget:Event_UI_SelectPlayer(not self.openList)

		self.openList = not self.openList
		self:UpdateNearbyPlayerData()
	else
		self.userWidget:Event_UI_Arrow(not self.openList, next(self.playerInfoList)~=nil)
		self.userWidget:Event_UI_SelectPlayer(not self.openList)
		self.openList = not self.openList
	end
end

function HUDSocialActionSelectPlayer:OnOpenInvitePanel()
	self.openList = false
	self.userWidget:UI_Event_SelectTargetPlayer(false)
	self.userWidget:Event_UI_SelectPlayer(false)
	self.userWidget:Event_UI_Arrow(false,true)

	self:UpdateNearbyPlayerData()
end

--- 此处为自动生成
---@param index number
---@param data table
function HUDSocialActionSelectPlayer:on_List_PlayerCom_ItemSelected(index, data)
	self:ReqDualPlayAction(Game.SocialActionSystem.currentActionID, data.EntityID, data.name)
end

return HUDSocialActionSelectPlayer
