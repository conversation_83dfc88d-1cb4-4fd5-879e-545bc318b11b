local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUD_TraceArrows : UIComponent
---@field view HUD_TraceArrowsBlueprint
local HUD_TraceArrows = DefineClass("HUD_TraceArrows", UIComponent)
local GroupView = kg_require("Framework.UI.List.NewList.NewGroupView")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

HUD_TraceArrows.eventBindMap = {
	[EEventTypesV2.ON_SNEAK_NPC_WARNING_HIDE] = "OnNPCWarningHide",
	[EEventTypesV2.ON_SNEAK_NPC_WARNING_UPDATE] = "OnNPCWarningAlarm",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUD_TraceArrows:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUD_TraceArrows:InitUIData()
	self.entityList = {} -- npc列表
	self.tickTimer = self:StartTickTimer("OnTick",
		function() 
			self:OnTick() 
		end, -1)
end

--- UI组件初始化，此处为自动生成
function HUD_TraceArrows:InitUIComponent()
	self.arrowsGroup = self:CreateComponent(self.view.CanvasPanel, GroupView, nil, "ArrowsGroup")
end

---UI事件在这里注册，此处为自动生成
function HUD_TraceArrows:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUD_TraceArrows:InitUIView()
end

---面板打开的时候触发
function HUD_TraceArrows:OnRefresh(entityId)
	local bInsert = true
	for _, v in ipairs(self.entityList) do
		if v.id == entityId then
			bInsert = false
		end
	end
	if bInsert then
		table.insert(self.entityList, {id = entityId, bPlayAnimIn = true})
	end
	self.arrowsGroup:SetData(#self.entityList)
end

function HUD_TraceArrows:OnClose()
	if self.tickTimer then
		self:StopTimer(self.tickTimer)
		self.tickTimer = nil
	end
	
	UIBase.OnClose(self)
	Log.Debug("HUD_TraceArrows:OnClose")
end

function HUD_TraceArrows:OnTick()
	for index, v in ipairs(self.entityList) do
		---@type WBP_HUD_TraceArrowsView
		local widget = self.arrowsGroup:GetRendererAt(index)
		local angle = Game.SneakSystem:GetNPCWarningHUDAngle(v.id)
		if angle then
			widget.view.SizeBox:SetRenderTransformAngle(angle)
		end

		local warningValue = Game.SneakSystem:GetNPCWarningValue(v.id)
		if warningValue and not v.needToRemove then
			if not v.lastWarningValue then
				widget.userWidget:Event_UI_Style(warningValue)
				v.lastWarningValue = warningValue
			elseif v.lastWarningValue ~= warningValue then
				local lastValue = v.lastWarningValue
				local deltaValue = (warningValue - v.lastWarningValue) / 500 * 33
				local timerKey = string.format("%s_%f", v.id, warningValue)
				self:StartTimer(timerKey, function()
					lastValue = lastValue + deltaValue
					widget.userWidget:Event_UI_Style(lastValue)
					if deltaValue > 0 then
						if lastValue > warningValue then
							self:StopTimer(timerKey)
							return
						end
					else
						if lastValue < warningValue then
							self:StopTimer(timerKey)
							return
						end
					end
				end, 33, -1, nil, true)
				v.lastWarningValue = warningValue
			end
		end
	end
end

function HUD_TraceArrows:OnNPCWarningHide(entityId)
	for index, id in ipairs(self.entityList) do
		if id.id == entityId then
			id.needToRemove = true
			local widget = self.arrowsGroup:GetRendererAt(index)
			local inAnimation = widget.view.Ani_Fadeout
			local startTime = 0
			
			if widget.userWidget:IsAnimationPlaying(inAnimation) then
				startTime = widget.userWidget:GetAnimationCurrentTime(inAnimation)
			end
			
			self:PlayAnimation(inAnimation, function()
				for k, v in ipairs(self.entityList) do
					if v.id == entityId then
						table.remove(self.entityList, k)
						break
					end
				end
				self.arrowsGroup:SetData(#self.entityList)
			end, widget.userWidget, startTime, 1, EUMGSequencePlayMode.Forward, 1, false)
			break
		end
	end
end

function HUD_TraceArrows:OnNPCWarningAlarm(entityId, type)
	if type == Enum.EHeadNPCWarningType.Alarm then
		for index, id in ipairs(self.entityList) do
			if id.id == entityId then
				id.needToRemove = true
				local widget = self.arrowsGroup:GetRendererAt(index)
				self:PlayAnimation(widget.view.Ani_Loop, function()
						for k, v in ipairs(self.entityList) do
							if v.id == entityId then
								table.remove(self.entityList, k)
								break
							end
						end
						self.arrowsGroup:SetData(#self.entityList)
					end, widget.userWidget, 0, 6, EUMGSequencePlayMode.Forward, 1, false)
				break
			end
		end
	end
end

function HUD_TraceArrows:OnRefresh_ArrowsGroup(widget, index)
	widget.view.Slot:SetAutoSize(true)
	local anchor = import("AnchorData")()
	anchor.Alignment = FVector2D(0.5, 0.5)
	anchor.Anchors.Maximum = FVector2D(0.5, 0.5)
	anchor.Anchors.Minimum = FVector2D(0.5, 0.5)
	widget.view.Slot:SetLayout(anchor)
	if self.entityList[index].bPlayAnimIn then
		self.entityList[index].bPlayAnimIn = false
		self:PlayAnimation(widget.view.Ani_Fadein, nil, widget.userWidget)
	end
end

return HUD_TraceArrows
