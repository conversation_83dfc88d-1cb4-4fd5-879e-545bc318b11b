local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local P_HUDRP = DefineClass("P_HUDRP", UIController)

P_HUDRP.eventBindMap = {
    [_G.EEventTypes.REDPACKET_INFO_UPDATE] = "OnReceiveInfoChange",
}

function P_HUDRP:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClicked)
end

function P_HUDRP:OnRefresh(Params)
    self.Params = Params
    self.View.Slot:SetAutoSize(true)
end

function P_HUDRP:OnClicked()
    Game.me:ReqRedPacketInfo(self.Params.chatArgs.redPacketInfo.uuid, self.Params.channelType)
    self:CloseSelf() 
end

function P_HUDRP:OnReceiveInfoChange()
    if Game.GameClientData:GetPlayerValue("RPData") then
        if Game.GameClientData:GetPlayerValue("RPData")[self.Params.chatArgs.redPacketInfo.uuid] ~= nil then
            if Game.GameClientData:GetPlayerValue("RPData")[self.Params.chatArgs.redPacketInfo.uuid].bIsReceived == true then
                self:CloseSelf()
            end
        end
    end
end

function P_HUDRP:OnClose()

end

return P_HUDRP

