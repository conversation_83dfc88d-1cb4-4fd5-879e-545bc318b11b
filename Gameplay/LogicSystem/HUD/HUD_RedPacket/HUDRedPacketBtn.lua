local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDRedPacketBtn : UIComponent
---@field view HUDRedPacketBtnBlueprint
local HUDRedPacketBtn = DefineClass("HUDRedPacketBtn", UIComponent)

local Game = Game

HUDRedPacketBtn.eventBindMap = {
    [EEventTypesV2.REDPACKET_INFO_UPDATE] = "OnReceiveInfoChange",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDRedPacketBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDRedPacketBtn:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDRedPacketBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDRedPacketBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDRedPacketBtn:InitUIView()
end

---组件刷新统一入口
function HUDRedPacketBtn:Refresh(params)
    self.Params = params
    self.view.Slot:SetAutoSize(true)
end


--- 此处为自动生成
function HUDRedPacketBtn:on_Btn_ClickArea_Clicked()
    Game.me:ReqRedPacketInfo(self.Params.chatArgs.redPacketInfo.uuid, self.Params.channelType)
    self:CloseSelf()
end

function HUDRedPacketBtn:OnReceiveInfoChange()
    local GameClientData = Game.GameClientData
    local redPacketInfo = self.Params.chatArgs.redPacketInfo
    if GameClientData:GetPlayerValue("RPData") then
        if GameClientData:GetPlayerValue("RPData")[redPacketInfo.uuid] ~= nil then
            if GameClientData:GetPlayerValue("RPData")[redPacketInfo.uuid].bIsReceived == true then
                self:CloseSelf()
            end
        end
    end
end

return HUDRedPacketBtn
