local ESlateVisibility = import("ESlateVisibility")

---@class P_HUDDungeonAwardAuction : UIController
local P_HUDDungeonAwardAuction = DefineClass("P_HUDDungeonAwardAuction", UIController)

function P_HUDDungeonAwardAuction:OnClickShow()
    --UI.ShowUI("P_DungeonAwardAuction")
	Game.NewUIManager:OpenPanel("Auction_Panel")
end

function P_HUDDungeonAwardAuction:OnAuctiontUpdate()
    if Game.DungeonAwardSystem:GetNowAuctionList()[1] ~= nil then
        self.AuctionInfo = Game.DungeonAwardSystem:GetNowAuctionList()[1]
    else
        self.AuctionInfo = Game.DungeonAwardSystem:GetWaitAuctionList()[1]
    end
    self:StopTimer("Auction")
    self.EndTime = 0
    if self.AuctionInfo ~= nil then
		local itemData = Game.DungeonAwardSystem.GetItemData(self.AuctionInfo.itemId)
		local AuctionMaxPrice = itemData["AuctionMaxPrice"] or 1000
        if self.AuctionInfo.StartTS ~= nil then
            if _G._now() < self.AuctionInfo.StartTS then
                self.EndTime = self.AuctionInfo.StartTS
                self.View:SetIcon(Enum.HUDBtnType.Aucton, true, true, true)
                self.View.BG_Rarity:SetVisibility(ESlateVisibility.Hidden)
            else
				if self.AuctionInfo.MoneyCount >= AuctionMaxPrice and self.AuctionInfo.MoneyCount ~= 0 then
					self.EndTime = self.AuctionInfo.BidTS + Game.TableData.GetConstDataRow("AUCTION_MAX_PRICE_COUNTDOWN") * 1000
                elseif self.AuctionInfo.BidTS ~= nil and self.AuctionInfo.BidTS + Game.TableData.GetConstDataRow("EXTRA_AUCTION_TIME") * 1000 >= self.AuctionInfo.StartTS + Game.TableData.GetConstDataRow("DEFAULT_AUCTION_COUNTDOWN") * 1000 then
                    self.EndTime = self.AuctionInfo.BidTS + Game.TableData.GetConstDataRow("EXTRA_AUCTION_TIME") * 1000
                else
                    self.EndTime = self.AuctionInfo.StartTS + Game.TableData.GetConstDataRow("DEFAULT_AUCTION_COUNTDOWN") * 1000
                end
                
                local itemiconpath = Game.UIIconUtils.GetIconByItemId(self.AuctionInfo.itemId)

                if itemiconpath then
                    self:SetImage(self.View.Icon, itemiconpath)
                end

                self.View.BG_Rarity:SetVisibility(ESlateVisibility.Visible)
                self.View:SetQuality(itemData["quality"])
            end
        end
        if self.EndTime > _G._now() then
            self.View:SetVisibility(ESlateVisibility.Visible)
            self:TimeUpdate()
            self:StartTimer("Auction", function() self:TimeUpdate() end, 500, -1, nil, true)
        else
            self.View:SetVisibility(ESlateVisibility.Collapsed)
        end
    else
        self.View:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_HUDDungeonAwardAuction:TimeUpdate()
    local leftTime = self.EndTime - _G._now()
    self.View.Text_LeftTime:SetText(
        Game.TimeUtils.FormatCountDownString(leftTime, true)
    )
    if self.AuctionInfo ~= nil and self.EndTime ~= 0 and _G._now() < self.AuctionInfo.StartTS then
        self.View:SetCD((leftTime / 1000) / Game.TableData.GetConstDataRow("AUCTION_BEGIN_COUNTDOWN"))
    else
        self.View:SetCD((leftTime / 1000) / Game.TableData.GetConstDataRow("DEFAULT_AUCTION_COUNTDOWN"))
    end
    if leftTime <= 0 then
        self:StopTimer("Auction")
        self:OnAuctiontUpdate()
    end
end

function P_HUDDungeonAwardAuction:OnCtrlIndicator(state)
    if state == 1 then
        self.View.Img_Indicator:SetVisibility(ESlateVisibility.Visible)
    else
        self.View.Img_Indicator:SetVisibility(ESlateVisibility.Hidden)
    end
end

P_HUDDungeonAwardAuction.eventBindMap = {
	[EEventTypesV2.DUNGEON_ON_AUCTION_UPDATE] = "OnAuctiontUpdate",
	[EEventTypesV2.DUNGEON_ON_AUCTION_TIPS_INDICATOR] = "OnCtrlIndicator",
}

function P_HUDDungeonAwardAuction:OnCreate()
    ---@type table 当前或下一件拍卖物品
    self.AuctionInfo = nil
    ---@type number 倒计时结束时间
    self.EndTime = nil

    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClickShow)
    self.View.Time_CD:SetVisibility(ESlateVisibility.Visible)
    self.View:SetIcon(Enum.HUDBtnType.Aucton, true, true,true)
end

function P_HUDDungeonAwardAuction:OnRefresh()
    self:OnAuctiontUpdate()
end

function P_HUDDungeonAwardAuction:OnUninit()
end

return P_HUDDungeonAwardAuction
