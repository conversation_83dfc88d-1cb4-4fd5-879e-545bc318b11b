---@class P_HUDDungeonAwardAssignment : UIController
local P_HUDDungeonAwardAssignment= DefineClass("P_HUDDungeonAwardAssignment",UIController)
local ESlateVisibility = import("ESlateVisibility")

function P_HUDDungeonAwardAssignment:OnClickBtn()
    Game.DungeonAwardSystem:ShowAssignmentPanel()
end

function P_HUDDungeonAwardAssignment:OnAssignmentUpdate()
    self:StartTimer("Assignment", function() 
        if self.OnAssignmentTimeUpdate then
            self:OnAssignmentTimeUpdate()
        else
            Log.Info("P_HUDDungeonAwardAssignment OnAssignmentTimeUpdate is nil")
            Log.Dump(self)
            self:StopTimer("Assignment")
        end 
    end, 500, -1, nil, true)
end

function P_HUDDungeonAwardAssignment:OnAssignmentTimeUpdate()
     local leftTime = Game.DungeonAwardSystem:GetEarlyGroupAssignmentTime() + Game.TableData.GetConstDataRow("ROLL_COUNTDOWN") * 1000 - _G._now()
    if leftTime > 0 then
        self.View.Text_LeftTime:SetText(
            Game.TimeUtils.FormatCountDownString(
                leftTime,
                true
            )
        )
       --self.View.Time_CD:GetDynamicMaterial():SetScalarParameterValue("Percent",(leftTime/1000)/Game.TableData.GetConstDataRow("ROLL_COUNTDOWN"))
    else
        self:StopTimer("Assignment")
        self:CloseSelf()
    end
end

function P_HUDDungeonAwardAssignment:OnClose()
    self:StopTimer("Assignment")
    UIBase.OnClose(self)
end

function P_HUDDungeonAwardAssignment:OnCtrlIndicator(state)
    if state == 2 then
        self.View.Img_Indicator:SetVisibility(ESlateVisibility.Visible)
    else
        self.View.Img_Indicator:SetVisibility(ESlateVisibility.Hidden)
    end
end

function P_HUDDungeonAwardAssignment:OnRefresh()
    self:OnAssignmentUpdate()
end

P_HUDDungeonAwardAssignment.eventBindMap = {
	[EEventTypesV2.DUNGEON_ON_ASSIGNMENT_UPDATE] = "OnAssignmentUpdate",
	[EEventTypesV2.DUNGEON_ON_AUCTION_TIPS_INDICATOR] = "OnCtrlIndicator",
	[EEventTypesV2.DUNGEON_ON_ASSIGNMENT_ROLL_RESULT] = "OnAssignmentUpdate",
}

function P_HUDDungeonAwardAssignment:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClickBtn)
    self.View.Time_CD:SetVisibility(ESlateVisibility.Visible)
    self.View:SetIcon(Enum.HUDBtnType.Roll,true,true,true)
end

return P_HUDDungeonAwardAssignment
