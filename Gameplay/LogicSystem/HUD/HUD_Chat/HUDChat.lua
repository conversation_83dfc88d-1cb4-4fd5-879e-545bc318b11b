local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local HUDChatCheck = kg_require("Gameplay.LogicSystem.HUD.HUD_Chat.HUDChatCheck")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDChat : UIComponent
---@field view HUDChatBlueprint
local HUDChat = DefineClass("HUDChat", UIComponent)
local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local redPacketConst = kg_require("Shared.Const.RedPacketConst")

HUDChat.eventBindMap = {
    [EEventTypesV2.CHAT_COMMON_CHANNEL_UPDATE] = "RefreshChannelData",
    [EEventTypesV2.CHAT_AT] = "ShowAtBtn",
    [EEventTypesV2.ON_VIEWPORT_RESIZED] = "OnChannelListUpdate",
    [EEventTypesV2.MODULE_LOCK_CHANGE] = "CheckUnlockState",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDChat:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDChat:InitUIData()
    ---@type number 快捷语音发送选中频道index
    self.NowChannelSelected = 1
    ---@type number 快捷语音发送选中频道ID
    self.NowChannel = Enum.EChatChannelData.WORLD
    ---@type number 聊天消息总数
    self.ChatListLen = 0
    ---@type table 聊天数据
    self.ChatData = {}
    ---@type boolean 是否置底
    self.ToEndBtnFlag = false
    ---@type number 未读消息总数
    self.UnReadMsgNum = 0
    ---@type table 频道数据
    self.ChannelData = {}
    ---@type boolean 聊天数据标脏
    self.DataDirty = false
    self.lastUpdateTime = 0
    ---@type boolean 当前是否处于置底
    self.BeBottom = true 
end

--- UI组件初始化，此处为自动生成
function HUDChat:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnIcon_FriendCom = self:CreateComponent(self.view.WBP_ComBtnIcon_Friend, UIComButton)
    ---@type HUDChatCheck
    self.HUDChatCheckCom = self:CreateComponent(self.view.HUDChatCheck, HUDChatCheck)
    ---@type UIListView
    self.ChatListCom = self:CreateComponent(self.view.ChatList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function HUDChat:InitUIEvent()
    self:AddUIEvent(self.view.AtBtn.OnClicked, "on_AtBtn_Clicked")
    self:AddUIEvent(self.view.BlankButton.OnClicked, "on_BlankButton_Clicked")
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.view.Button_Expand.OnClicked, "on_Button_Expand_Clicked")
    self:AddUIEvent(self.view.Button_Fold.OnClicked, "on_Button_Fold_Clicked")
    self:AddUIEvent(self.view.ToBottomBtn.OnClicked, "on_ToBottomBtn_Clicked")
    self:AddUIEvent(self.WBP_ComBtnIcon_FriendCom.onClickEvent, "on_WBP_ComBtnIcon_FriendCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDChat:InitUIView()
    self.view.BlankButton:SetVisibility(ESlateVisibility.Hidden)
    self:CheckUnlockState()

    -- 列表滚动事件 手动注册
    self:AddUIEvent(self.view.AtBtn.OnClicked, "OnScrollChatList")
end

---组件刷新统一入口
function HUDChat:Refresh(...)
    self:on_Button_Expand_Clicked()
    Game.ChatSystem:InitCommonChannelList()
    Game.ChatSystem:UpdateHUDChatData()
    self:GetHudChatData()
    self.ChatListCom:Refresh(self.ChatData, self.ChatListLen)
    self.ChatListCom:ScrollToItemByIndex(self.ChatListLen, 1)
    self.UnReadMsgNum = 0
    self.view.NewMsg:SetVisibility(ESlateVisibility.Hidden)
    self:RefreshChannelData()
    -- self:StartTimer("hudchatlistscroll", function()
    --     self:ScrollListToIndex(self.ChatListLen)
    -- end, 600, 1)
    local row = Game.TableData.GetChatChannelDataRow(self.NowChannel)
    if row then
        self.HUDChatCheckCom:Refresh(row.VoiceTip, self.NowChannel, self.NowChannelSelected)
    end
    -- self.view.WBP_KeyPrompt:SetSize(2)
    -- self.view.WBP_KeyPrompt:SetActiveIndex(1)
    -- self:SetImage(self.view.WBP_KeyPrompt.img_mouse_lua, UIAssetPath.UI_KeyPrompt_Img_KeyboardFrame02)
    -- if Game.HUDSystem.IsInPC() then
    --     self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    -- else
    --     self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.Collapsed)
    -- end
    --Game.RedPointSystem:RegisterRedPoint(self, self.View.WBP_ComBtnIcon_Friend, "HUDFriend")
    Game.RedPointSystem:RegisterRedPoint(self:GetBelongPanel(), self.view.WBP_ComBtnIcon_Friend, "HUDFriend")
end

function HUDChat:GetHudChatData()
    self.ChatData = Game.ChatSystem:GetHUDChatData()
    for k,v in pairs(self.ChatData) do
        self:GetShowMessage(v)
    end
    self.ChatListLen = #self.ChatData
    if self.ChatListLen == 0 then
        self.view.ChatList:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.ChatList:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

function HUDChat:GetShowMessage(chatInfo)
    if string.isEmpty(chatInfo.ShowMessageText) then
        local messageText = ""
        if chatInfo.messageType == Enum.EChatMessageType.IMAGE then
            messageText = StringConst.Get("SOCIAL_CHAT_STICKER")
        elseif chatInfo.functionType == Enum.ChatFunctionType.TEAM_RECRUIT then
            messageText = Game.ChatSystem:GetTeamRecruitText(chatInfo)
        elseif chatInfo.functionType == Enum.ChatFunctionType.TEAM_GROUP_RECRUIT then
            messageText = Game.ChatSystem:GetGroupRecruitText(chatInfo)
        elseif chatInfo.messageType == Enum.EChatMessageType.VOICE then
            if string.isEmpty(chatInfo.messageText) then
                messageText = StringConst.Get("SOCIAL_CHAT_VOICE_IDENTIFY_FAIL")
            else
                messageText = chatInfo.messageText
            end
        elseif chatInfo.messageType == Enum.EChatMessageType.GUILD_TASK_HELP then
            messageText = Game.ChatSystem:GetGuildTaskHelpText(chatInfo)
        elseif chatInfo.messageType == Enum.EChatMessageType.RED_PACKET then
            if chatInfo.chatArgs.redPacketInfo.packetClass == redPacketConst.RED_PACKET_CLASS.MONEY then
                messageText = "["..StringConst.Get("RED_PACKET_TYPE_1").."]"
            else
                messageText = "["..StringConst.Get("RED_PACKET_TYPE_2").."]"
            end
        elseif chatInfo.messageType == Enum.EChatMessageType.GUILD_RESPONSE or chatInfo.messageType == Enum.EChatMessageType.GUILD_JOIN then
            messageText = StringConst.Get("SOCIAL_CHAT_INVITE_CARD")
        else 
            if string.isEmpty(chatInfo.messageText) then
                messageText = ""
            else
                messageText = string.gsub(chatInfo.messageText, "width=\"42\" height=\"42\"", "width=\"32\" height=\"32\"")
            end
        end
        if chatInfo.messageType ~= Enum.EChatMessageType.SYSTEM_TEXT then
            if not (chatInfo.senderInfo == nil or chatInfo.senderInfo.school == 0 or chatInfo.senderInfo.school == nil)then
                local roleName = chatInfo.senderInfo.rolename
                if Game.me then
                    roleName = Game.FriendSystem:GetFriendShowName(chatInfo.senderInfo.id, chatInfo.senderInfo.rolename)
                end
                local roleText = Game.ChatSystem:GetRegMsg(Enum.EMsgReg.ROLE, chatInfo.senderInfo.id, roleName)
                messageText = string.format("%s：%s", roleText, messageText)
            end
        end
        chatInfo.ShowMessageText = messageText
    end
end

function HUDChat:ShowAtBtn()
    local hasAtMsg = false
    local allAtlist = Game.ChatSystem:GetAtMessage()
    for k,v in pairs(allAtlist) do
        if not(v.clubId and Game.me.clubsNoDisturb and Game.me.clubsNoDisturb[v.clubId]) then
            hasAtMsg = true
            break
        end
    end
    self.view.AtView:SetVisibility(hasAtMsg and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

function HUDChat:OnChannelListUpdate()
    if self.lastUpdateTime and _G._now() - self.lastUpdateTime < 200 then
        return
    else
		self.lastUpdateTime = _G._now()
        self:StartTimer("updateHUDChatData",
            function()
                self:RefreshChatListUI()
            end, 200, 1)
    end
end

function HUDChat:RefreshChatListUI()
    self:GetHudChatData()
    if self.BeBottom then
		self.ChatListCom:Refresh(self.ChatData, self.ChatListLen)
        self.ChatListCom:ScrollToItemByIndex(self.ChatListLen, 1)
        self:SetToEndBtnVisible(false)
    else
		self.ChatListCom:Refresh(self.ChatData)
        self:SetToEndBtnVisible(true)
    end
end

function HUDChat:SetURLActive()
    for k,v in pairs(self.ChatData) do
        local item = self.ChatListCom:GetItemByIndex(k)
        if item then
            item:SetURLActive()
        end
    end
end

function HUDChat:RefreshChannelData()
    self.ChannelData, self.NowChannel, self.NowChannelSelected = Game.ChatSystem:GetCommonChannelList()
    local row = Game.TableData.GetChatChannelDataRow(self.NowChannel)
    self.HUDChatCheckCom:Refresh(row.VoiceTip, self.NowChannel, self.NowChannelSelected)
    self.HUDChatCheckCom:RefreshChannelList(self.ChannelData)
end

function HUDChat:RefreshChannelSelected(NowChannel, NowChannelSelected)
    self.NowChannel = NowChannel
    self.NowChannelSelected = NowChannelSelected
    local row = Game.TableData.GetChatChannelDataRow(self.NowChannel)
    self.HUDChatCheckCom:Refresh(row.VoiceTip, self.NowChannel, self.NowChannelSelected)
end

function HUDChat:OnScrollChatList()
    local lastItemIndex = self.ChatListCom:GetBottomIndex()
    if self.ChatListLen == lastItemIndex then
        self:SetToEndBtnVisible(false)
        self.BeBottom = true
    else
        self.BeBottom = false
        if self.UnReadMsgNum > 0 then
            if self.UnReadMsgNum > self.ChatListLen - lastItemIndex then
                self.UnReadMsgNum = self.ChatListLen - lastItemIndex
                self:SetUnreadMsgNum()
            end
        end
    end
end   


function HUDChat:ScrollListToIndex(index)
    if index == nil then
        return
    end

    self.ChatListCom:ScrollToItemByIndex(index, 1)
    if index >= self.ChatListLen then
        self:SetToEndBtnVisible(Enum.EEndBtn.Hide)
        self.BeBottom = true
    else
        local lastItemIndex = self.ChatListCom:GetBottomIndex()
        if self.UnReadMsgNum > 0 then
            if self.UnReadMsgNum > self.ChatListLen - lastItemIndex then
                self.UnReadMsgNum = self.ChatListLen - lastItemIndex
                self:SetUnreadMsgNum()
            end
        else
            self:SetToEndBtnVisible(Enum.EEndBtn.ShowEnd)
        end
    end
end

function HUDChat:SetToEndBtnVisible(enumShow)
    if self.ToEndBtnFlag ~= enumShow then
        self.ToEndBtnFlag = enumShow
        if not enumShow or enumShow == Enum.EEndBtn.Hide then
            self.UnReadMsgNum = 0
            self.view.NewMsg:SetVisibility(ESlateVisibility.Hidden)
        else
            self.view.NewMsg:SetVisibility(ESlateVisibility.Visible)
            self.UnReadMsgNum = 1
            self:SetUnreadMsgNum()
        end
    else
        if self.ToEndBtnFlag then
            self.UnReadMsgNum = self.UnReadMsgNum + 1
            self:SetUnreadMsgNum()
        end
    end
end

function HUDChat:SetUnreadMsgNum()
    local newMsgNumString
    if self.UnReadMsgNum <= 99 then
        newMsgNumString = tostring(self.UnReadMsgNum)
    else
        newMsgNumString = "99+"
    end
    self.view.Text_Msg:SetText(string.format(StringConst.Get("SOCIAL_CHAT_NEW_MESSAGE"), newMsgNumString))
end

function HUDChat:OnViewportResize()
    self:OnChannelListUpdate()
end

function HUDChat:CheckUnlockState()
    if Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_FRIEND,false) then
        self.view.WBP_ComBtnIcon_Friend:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.view.WBP_ComBtnIcon_Friend:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--- 此处为自动生成
function HUDChat:on_AtBtn_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.ChatSet_Panel, 2)
    self.view.AtView:SetVisibility(ESlateVisibility.Hidden)
end

--- 此处为自动生成
function HUDChat:on_BlankButton_Clicked()
end

--- 此处为自动生成
function HUDChat:on_Btn_ClickArea_Clicked()
    Game.ChatSystem:OpenSocialPanel()
    self:on_ToBottomBtn_Clicked()
    self.HUDChatCheckCom:CloseVoiceChannel()
end

--- 此处为自动生成
function HUDChat:on_Button_Expand_Clicked()
    self.userWidget:SetFold(false)
end

--- 此处为自动生成
function HUDChat:on_Button_Fold_Clicked()
    self.userWidget:SetFold(true)
end

--- 此处为自动生成
function HUDChat:on_ToBottomBtn_Clicked()
    self.BeBottom = true
    self.ChatListCom:ScrollToItemByIndex(self.ChatListLen, 1)
    self:SetToEndBtnVisible(false)
end

--- 此处为自动生成
function HUDChat:on_WBP_ComBtnIcon_FriendCom_ClickEvent()
    Game.ChatSystem:OpenSocialPanel(true,Enum.ESocialTab.Friend)
    self.HUDChatCheckCom:CloseVoiceChannel()
end

return HUDChat
