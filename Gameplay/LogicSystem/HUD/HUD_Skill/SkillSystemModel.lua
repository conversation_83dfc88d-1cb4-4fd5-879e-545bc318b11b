---@class SkillSystemModel:SystemModelBase
local SkillSystemModel = DefineClass("SkillSystemModel",SystemModelBase)

---@type LuaFunctionLibrary
local LuaFunctionLibrary = import("LuaFunctionLibrary")

---@type KismetSystemLibrary
local KismetSystemLibrary = import("KismetSystemLibrary")
function SkillSystemModel:init()
    ---@static
    SkillSystemModel.Directory = LuaFunctionLibrary.ConvertToAbsolutePathForExternalAppForRead(
            KismetSystemLibrary.GetProjectSavedDirectory() .. "HUDSkill"
    )
    ---@static
    SkillSystemModel.FilePath = SkillSystemModel.Directory .. "/HUDSkillData.json"

end

function SkillSystemModel:clear()

end

function SkillSystemModel:unInit()

end

return SkillSystemModel