-- 提供给技能HUD的一些通用接口
-- Author：Shi <PERSON>

local SkillImpl = {}
local EInputEvent = import("EInputEvent")
local SkillCheck = kg_require("Gameplay.Combat.Skill.SkillCheck").SkillCheck

-- -- region CommonAPI
--获取职业机制技能ID
function SkillImpl.GetMechnismSkillID()
    local SkillID = -1
    local ProTableData = Game.TableData.GetProfessionSkillDataRow(Game.me.Profession)
    if ProTableData and ProTableData.SkillID then
        SkillID = ProTableData.SkillID
    end
    return SkillID
end

-- 获取主角对应插槽的技能ID
function SkillImpl.GetMainChaSkillIDBySlot(InSlot)
    -- Game Exit会触发到这儿，UI清理流程需要调整
    if not Game.me then
        return nil
    end
	if not InSlot then
		return nil
	end

    return Game.me:GetViewSkillIDBySlot(InSlot)
end

-- 根据ID获取技能所在主角的插槽
function SkillImpl.FindMainChaSkillSlot(InSkillID)
    return Game.me:FindSkillSlot(InSkillID)
end

-- 获取主角对应插槽的当前方案技能ID
function SkillImpl.GetMainChaSchemeSkillIDBySlot(InSlot)
    local SkillSchemeList = GetMainPlayerPropertySafely("skillSchemeList")
    local CurSkillSchemeId = GetMainPlayerPropertySafely("curSkillSchemeId")
    if SkillSchemeList and CurSkillSchemeId then
        local CurSkillSchemeEntryInfo = SkillSchemeList[CurSkillSchemeId]
        if CurSkillSchemeEntryInfo then
            local SkillSlotDict = CurSkillSchemeEntryInfo.SkillSlotDict
            for SkillID, SkillSlot in pairs(SkillSlotDict) do
                if SkillSlot == InSlot then
                    return SkillID
                end
            end
        end
    end
    return nil
end

-- 获取主角对应插槽的当前方案技能插槽
function SkillImpl.GetMainChaSchemeSkillSlotByID(InSkillID)
    local SkillSchemeList = GetMainPlayerPropertySafely("skillSchemeList")
    local CurSkillSchemeId = GetMainPlayerPropertySafely("curSkillSchemeId")
    if SkillSchemeList and CurSkillSchemeId then
        local CurSkillSchemeEntryInfo = SkillSchemeList[CurSkillSchemeId]
        if CurSkillSchemeEntryInfo then
            local SkillSlotDict = CurSkillSchemeEntryInfo.SkillSlotDict
            for SkillID, SkillSlot in pairs(SkillSlotDict) do
                if SkillID == InSkillID then
                    return SkillSlot
                end
            end
        end
    end
    return nil
end

-- endregion CommonAPI



-- region Event

-- 按下技能按钮的回调
-- @param inSkillSlot ETE.EBSSkillSlot 激活的技能槽
function SkillImpl.onSkillBtnPressed(inSkillSlot)
    --Log.Debug("按下技能按钮激活")
    if inSkillSlot then
        Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_BTNPRESSED, inSkillSlot)
    end
end

-- 调整瞄准位置的回调
-- @param inVec FVector2D 传回技能图标遥感相对于边缘的向量
function SkillImpl.onSkillJoyStickMoved(inVec)
    -- 由于Decal在Y轴旋转-90°以朝向地面，导致此处以-Y和X传入参数来设置WarnDecal的RelativeLocation
    if inVec then
        Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_JOYSTICKMOVED, -inVec.Y, inVec.X)
    end
end

-- 释放技能按钮的回调
-- @param inSkillSlot  ETE.EBSSkillSlot 技能槽
-- @param inIsActivate bool 根据是否在取消按钮上释放，决定ReleaseBtn后是否释放技能
function SkillImpl.onSkillBtnReleased(inSkillSlot, inIsActivate)
    --Log.Debug("抬起技能按钮结束技能")
    if inSkillSlot then
        Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_BTNRELEASED, inSkillSlot, inIsActivate)
    end
end

-- 瞄准遥感进入或离开取消释放区域的回调
-- @param bInOrOut int 进入还是离开SkillCancel区域
function SkillImpl.onAimBtnCrossCancelBtn(bInOrOut)
    local tPlayerController = import("GameplayStatics").GetPlayerController(_G.GetContextObject(), 0)
    ---@type S_InputProcessor
    local tInputProcessor = tPlayerController ~= nil and tPlayerController.BattleSystemInputProcessor or nil
    if tInputProcessor ~= nil then
        tInputProcessor:SetForewarnCanceling(bInOrOut)
        tInputProcessor:UpdateWarnColor()
    end
end

-- 跳跃按钮回调
function SkillImpl.Jump(bPressed)
    if bPressed then
        NotifyInputAction("Jump_Action", EInputEvent.IE_Pressed)
    else
        NotifyInputAction("Jump_Action", EInputEvent.IE_Released)
    end
end

-- 冲刺按钮回调
function SkillImpl.Dodge(bPressed)
    if bPressed then
        NotifyInputAction("Dodge_Action", EInputEvent.IE_Pressed)
    else
        NotifyInputAction("Dodge_Action", EInputEvent.IE_Released)
    end
end

-- endregion Event



-- region Skill

-- 根据技能ID判断是否在冷却中
-- @param inSkillID int 技能ID
function SkillImpl.checkSkillInCoolDown(inSkillID)
    return SkillCheck.CheckSkillInCD(Game.me, Game.TableData.GetSkillDataNewRow(inSkillID))
end

---根据技能ID判断充能是否充满
---@param inSkillID integer
---@return boolean
function SkillImpl.checkSkillChargeFull(inSkillID)
    local skillTableData = Game.TableData.GetSkillDataNewRow(inSkillID)
    return SkillCheck.IsSkillChargeEnough(Game.me, skillTableData, skillTableData.ChargeNum)
end

-- 根据技能ID获取冷却数据
-- @param inSkillID int 技能ID
-- @return  UE.FBSACoolDownContext 冷却数据，包含：
------ TotalCoolDown：总冷却时间
------ CurrentCoolDown：当前剩余冷却时间
------ TotalChargeTimes：总充能点数
------ CurrentChargeTimes：当前充能点数
function SkillImpl.getSkillCoolDownMessage(inSkillID)
    if (Game.me == nil) then
        return nil
    end

    return Game.me:GetSkillCoolDownMessageNew(inSkillID)
end

-- 根据技能ID判断是否在循环中
-- @param inSkillID int 技能ID
function SkillImpl.checkSkillInLoop(inSkillID)
    if (Game.me == nil) then
        return false
    end

    return Game.me:CheckSkillInLoop(inSkillID) or false
end

function SkillImpl.getSkillLoopMessage(inSkillID)
    if (Game.me == nil) then
        return nil
    end

    return Game.me:GetSkillLoopMessage(inSkillID) or nil
end

-- 根据技能ID判断是否在Combo中
-- @param inSkillID int 技能ID
function SkillImpl.checkSkillInCombo(inSkillID)
    if (Game.me == nil) then
        return false
    end

    return Game.me:CheckSkillInComboNew(inSkillID) or false
end

function SkillImpl.getSkillComboMessage(inSkillID)
    if (Game.me == nil) then
        return nil
    end

    return Game.me:GetSkillComboMsgNew(inSkillID) or false
end

-- 根据技能ID获取当前Combo窗口的冷却数据
-- @param inSkillID int 技能ID
-- @return  UE.FBSAComboCoolDownContext Combo窗口冷却数据，包含：
------ TotalComboCoolDown：Combo窗口时长
------ CurrentComboCoolDown：当前窗口时长
------ CurrentComboTimes：当前段数
function SkillImpl.getSkillComboCoolDownMessage(inSkillID)
    if (Game.me == nil) then
        return nil
    end

    return Game.me:GetSkillComboCDMsgNew(inSkillID) or false
end

function SkillImpl.CanActivateSkill(inSkillID)
    if (Game.me == nil) then
        return false
    end

    return Game.me:CanReleaseSkill(inSkillID) or false
end

function SkillImpl.InternalCanActivateSkill(inSkillID)
    if (Game.me == nil) then
        return
    end

	local _, SkillActivateResultMsg, DisableSkllDetailReason = Game.me:CanReleaseSkillNew(inSkillID)
	return SkillActivateResultMsg, DisableSkllDetailReason
end

function SkillImpl.InternalCanActivateComboSkill(oldSkillID, comboSkillID)
    if (Game.me == nil) then
        return false
    end

    if Game.me:IsSkillRunning(oldSkillID) and SkillCheck.CheckComboSkill(Game.me, Game.TableData.GetSkillDataNewRow(comboSkillID)) then
        return true
    end

    return false
end

-- 在DisableType和DisableTag的技能禁用情况下，检查是否是外部禁用导致的
-- 会影响按钮是否需要真正置灰
-- function SkillImpl.CheckSkillBtnNeedDisable(InSkillID)
--     if (Game.me == nil) then
--         return false
--     end
--     if Game.CombatDataManager:IsNewSkill(InSkillID) then
--         local SkillData = Game.TableData.GetSkillDataNewRow(InSkillID)
--         return not Game.me:IsSkillDisableByReason(SkillData.Type, ETE.EDisabledSkillReason.Temporary)
--     end
--     return Game.me:CheckSkillDisableReason(InSkillID, ETE.EDisabledSkillReason.Temporary, true) or false
-- end

-- function SkillImpl.SimpleReleaseSkill(InSkillID)
--     if (Game.me == nil) then
--         return
--     end

--     Game.me:SimpleReleaseSkill(InSkillID, nil, true)
-- end

-- endregion Skill



-- region Buff
-- 根据Buff的ID获取当前角色的Buff层数
-- @param inBuffID int BuffID
-- @return int 对应的Buff层数
function SkillImpl.getBuffLayerNumByBuffID(inBuffID)
    if (Game.me == nil) then
        return 0
    end

    return Game.me:GetBuffLayerNew(inBuffID) or 0
end

-- endregion Buff



-- region Lock
-- 自动切换索敌目标
-- @param bUpdateTargetList bool 是否更新目标
-- @return  AActor* 索敌目标
function SkillImpl.getNextTarget()
    if not Game.me then
        return
    end
    Game.me:SwitchNextTarget(ETE.EBSTargetType.TT_Skill)
    return Game.me:GetLockTargetUID(ETE.EBSTargetType.TT_Skill)
end

-- 手动选择并切换索敌目标
---@param inTargetType ETE.EBSTargetType 索敌目标类型
---@param inNewTargeteid  string 新索敌目标
function SkillImpl.ChangeLockTargetByEID(inTargetType, inNewTargeteid)
    if Game.me then 
        Game.me:ChangeLockTargetByEID(inTargetType, inNewTargeteid)
    end
end

function SkillImpl.ForceChangeLockTargetByEID(inTargetType, inNewTargeteid)
    if Game.me then
        Game.me:ForceChangeLockTargetByEID(inTargetType, inNewTargeteid)
    end
end

-- 返回当前索敌目标
-- @param TargetType ETE.EBSTargetType 索敌目标类型
-- @return  AActor* 当前索敌目标
function SkillImpl.GetLockTargetEntity(TargetType)
    if Game.me then
        return Game.me:GetLockTargetEntity(TargetType)
    end
end

function SkillImpl.GetLockTargetUID(TargetType)
    if Game.me then
        return Game.me:GetLockTargetUID(ETE.EBSTargetType.TT_Skill, true)
    end
end

-- endregion Lock

return SkillImpl
