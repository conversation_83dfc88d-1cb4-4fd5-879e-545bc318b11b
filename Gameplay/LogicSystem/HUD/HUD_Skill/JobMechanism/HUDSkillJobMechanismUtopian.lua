local JobMechanismConst = kg_require "Gameplay.LogicSystem.HUD.HUD_Skill.JobMechanism.JobMechanismConst"
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillJobMechanismUtopian : UIComponent
---@field view HUDSkillJobMechanismUtopianBlueprint
local HUDSkillJobMechanismUtopian = DefineClass("HUDSkillJobMechanismUtopian", UIComponent)

HUDSkillJobMechanismUtopian.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillJobMechanismUtopian:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillJobMechanismUtopian:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDSkillJobMechanismUtopian:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDSkillJobMechanismUtopian:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSkillJobMechanismUtopian:InitUIView()
	self.UtopianUIMap = {
		[1] = self.view.WBP_HUDUtopianSkillDot,
		[2] = self.view.WBP_HUDUtopianSkillDot_1,
		[3] = self.view.WBP_HUDUtopianSkillDot_2,
		[4] = self.view.WBP_HUDUtopianSkillDot_3,
		[5] = self.view.WBP_HUDUtopianSkillDot_4
	}
end

---组件刷新统一入口
function HUDSkillJobMechanismUtopian:Refresh(...)
end

function HUDSkillJobMechanismUtopian:OnOpen()
	local ProfessionData = Game.TableData.GetProfessionSkillDataRow(JobMechanismConst.ProfessionEnum.Utopian)
	if Game.me and ProfessionData then
		local Value = GetMainPlayerPropertySafely(ProfessionData.ProfessionProp)
		self:OnProfessionPropChanged(Value, 0.0)
	end
end

function HUDSkillJobMechanismUtopian:OnProfessionPropChanged(NewValue, OldValue)
	if self.JobStyleInited then
		return
	end
	self.JobStyleInited = true
	local WBP_HUDUtopianSkill = self.userWidget
	self:StopAnimation(WBP_HUDUtopianSkill.Ani_bluelight_loop_open, WBP_HUDUtopianSkill)
	self:StopAnimation(WBP_HUDUtopianSkill.Ani_bluelight_loop, WBP_HUDUtopianSkill)
	self:PlayAnimation(WBP_HUDUtopianSkill.Ani_bluelight_close, nil, WBP_HUDUtopianSkill, 0, 1,
		EUMGSequencePlayMode.Forward, 1, false)
end

return HUDSkillJobMechanismUtopian
