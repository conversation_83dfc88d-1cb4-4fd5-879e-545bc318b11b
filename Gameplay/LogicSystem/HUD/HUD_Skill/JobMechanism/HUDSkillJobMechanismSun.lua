local JobMechanismConst = kg_require "Gameplay.LogicSystem.HUD.HUD_Skill.JobMechanism.JobMechanismConst"

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillJobMechanismSun : UIComponent
---@field view HUDSkillJobMechanismSunBlueprint
local HUDSkillJobMechanismSun = DefineClass("HUDSkillJobMechanismSun", UIComponent)

HUDSkillJobMechanismSun.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillJobMechanismSun:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillJobMechanismSun:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDSkillJobMechanismSun:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDSkillJobMechanismSun:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSkillJobMechanismSun:InitUIView()
end

---组件刷新统一入口
function HUDSkillJobMechanismSun:Refresh(...)
end

function HUDSkillJobMechanismSun:OnOpen()
	local ProfessionData = Game.TableData.GetProfessionSkillDataRow(JobMechanismConst.ProfessionEnum.Sun)
	if Game.me and ProfessionData then
		local Value = GetMainPlayerPropertySafely(ProfessionData.ProfessionProp)
		self:OnProfessionPropChanged(Value, 0.0)
	end
end

function HUDSkillJobMechanismSun:OnProfessionPropChanged(NewValue, OldValue)
	-- 太阳
	self.view.progress_sun:SetPercent(NewValue / 400)
end

return HUDSkillJobMechanismSun
