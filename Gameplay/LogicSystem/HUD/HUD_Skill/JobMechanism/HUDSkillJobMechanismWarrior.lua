local JobMechanismConst = kg_require "Gameplay.LogicSystem.HUD.HUD_Skill.JobMechanism.JobMechanismConst"

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillJobMechanismWarrior : UIComponent
---@field view HUDSkillJobMechanismWarriorBlueprint
local HUDSkillJobMechanismWarrior = DefineClass("HUDSkillJobMechanismWarrior", UIComponent)

HUDSkillJobMechanismWarrior.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillJobMechanismWarrior:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillJobMechanismWarrior:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDSkillJobMechanismWarrior:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDSkillJobMechanismWarrior:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSkillJobMechanismWarrior:InitUIView()
	self.CommonUIMap = {
		[1] = self.view.UtopianDot01,
		[2] = self.view.UtopianDot02,
		[3] = self.view.UtopianDot03,
		[4] = self.view.UtopianDot04,
		[5] = self.view.UtopianDot05
	}
end

---组件刷新统一入口
function HUDSkillJobMechanismWarrior:Refresh(...)
end

function HUDSkillJobMechanismWarrior:OnOpen()
	local ProfessionData = Game.TableData.GetProfessionSkillDataRow(JobMechanismConst.ProfessionEnum.Warrior)
	if Game.me and ProfessionData then
		local Value = GetMainPlayerPropertySafely(ProfessionData.ProfessionProp)
		self:OnProfessionPropChanged(Value, 0.0)
	end
end

function HUDSkillJobMechanismWarrior:CheckJobMechanismType(InValue)
	-- 战士
	local Digits = {}
	local CurValue = InValue
	while CurValue > 0 do
		local Digit = CurValue % 10
		table.insert(Digits, Digit)
		CurValue = math.floor(CurValue / 10)
	end
	return Digits
end

function HUDSkillJobMechanismWarrior:OnProfessionPropChanged(NewValue, OldValue)
	for key, value in pairs(self.CommonUIMap) do
		if key <= NewValue then
			value:Event_UI_Style(4)
		else
			value:Event_UI_Style(0)
		end
	end
end

return HUDSkillJobMechanismWarrior
