local ESlateVisibility = import("ESlateVisibility")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillFlameJumpBtn : UIComponent
---@field view HUDSkillFlameJumpBtnBlueprint
local HUDSkillFlameJumpBtn = DefineClass("HUDSkillFlameJumpBtn", UIComponent)

HUDSkillFlameJumpBtn.eventBindMap = {
	[EEventTypesV2.ON_FLAMEJUMP_UPDATE] = "OnFlameJumpUpdate",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillFlameJumpBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillFlameJumpBtn:InitUIData()
	self.InsID = nil
end

--- UI组件初始化，此处为自动生成
function HUDSkillFlameJumpBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDSkillFlameJumpBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSkillFlameJumpBtn:InitUIView()
end

---组件刷新统一入口
function HUDSkillFlameJumpBtn:Refresh(...)
end


--- 此处为自动生成
function HUDSkillFlameJumpBtn:on_Btn_ClickArea_Clicked()
	self:OnClick()
end

function HUDSkillFlameJumpBtn:setVisible(bVisible)
	if bVisible then
		self.userWidget:SetVisibility(ESlateVisibility.Visible)
	else
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function HUDSkillFlameJumpBtn:OnOpen()
	self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
end

function HUDSkillFlameJumpBtn:OnRefresh(Params)
end

function HUDSkillFlameJumpBtn:OnFlameJumpUpdate(bVisible, InsID)
	self:setVisible(bVisible)
	self.InsID = InsID
end

function HUDSkillFlameJumpBtn:OnClose()
	UIBase.OnClose(self)
	self.InsID = nil
end

function HUDSkillFlameJumpBtn:OnClick()
	Game.FlameJumpSystem:DoFlameJump(self.InsID)
end

return HUDSkillFlameJumpBtn
