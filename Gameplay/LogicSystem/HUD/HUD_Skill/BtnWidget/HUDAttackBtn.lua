local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local EInputEvent = import("EInputEvent")
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"
local InputControlConst = kg_require("Shared.Const.InputControlConst")
local FAnchors = import("Anchors")
local DebugFlag = require("Gameplay.Debug.DebugFlag")
local LuaDebugProxy = require("Gameplay.Debug.LuaDebugProxy")
local DebugConst = kg_require("Gameplay.Debug.DebugConst")
local InputConst = kg_require("Gameplay.3C.Input.InputConst")
local ABILITY_MONIOTOR_OPERATION = DebugConst.ABILITY_MONIOTOR_OPERATION
local MONITOR_COLOR = DebugConst.MONITOR_COLOR

local KeyPrompt = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.KeyPrompt")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDAttackBtn : UIComponent
---@field view HUDAttackBtnBlueprint
local HUDAttackBtn = DefineClass("HUDAttackBtn", UIComponent)

HUDAttackBtn.eventBindMap = {
	[EEventTypesV2.ROLE_ACTION_INPUT_EVENT] = "KeyOnInput",
	[_G.EEventTypes.ON_IS_DEAD_CHANGED] = { "OnIsDeadChanged", GetMainPlayerEID },
	[_G.EEventTypes.SKILL_POST_SKILL_EQUIP_CHANGED] = "onSkillEquipChanged",
	[_G.EEventTypes.SKILLHUD_AUTOSKILL_SWITCH_CHANGE] = { "OnAutoSkillChange", GetMainPlayerEID },
	[_G.EEventTypes.ON_INBATTLE_CHANGED] = { "OnEnterBattle", GetMainPlayerEID },
	[_G.EEventTypes.OPERATOR_MODE_CHANGE] = { "OnOperatorModeChange", bClassic },
	[_G.EEventTypes.SKILLHUD_ON_NORMAL_ATTACK_ACTION_TRIGGERED] = "onNormalAttackActionTriggered",
	[_G.EEventTypes.SKILLHUD_ON_NORMAL_ATTACK_ACTION_RELEASED] = "onNormalAttackActionReleased",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDAttackBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDAttackBtn:InitUIData()
	self.SkillSlot = nil
	self.AttackAnimTimer = nil
	self.RippleAnimEndTime = 0

	-- 用于记录当前技能图标路径
	self.FirstPress = false

	self.AttackPointerIndex = -1
	self.RippleAnimList = {}
	self.AvailableRippleList = {}
	self.KeyActionName = nil
	self.PointerIndex = -1
end

--- UI组件初始化，此处为自动生成
function HUDAttackBtn:InitUIComponent()
    ---@type KeyPrompt
    self.WBP_KeyPromptCom = self:CreateComponent(self.view.WBP_KeyPrompt, KeyPrompt)
end

---UI事件在这里注册，此处为自动生成
function HUDAttackBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Act.OnClicked, "on_Btn_Act_Clicked")
    self:AddUIEvent(self.view.OnTouchStartedEvent, "on_WBP_HUDACT1_TouchStartedEvent")
    self:AddUIEvent(self.view.OnTouchEndedEvent, "on_WBP_HUDACT1_TouchEndedEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDAttackBtn:InitUIView()
	self:SetSingleSkillIcon()
	self:InitRippleAnimList()
	self.view.Btn_Act:SetIsEnabled(false)

	-- Init the state of the button
	if Game.me.bAutoSkillSwitchOpen then
		self.userWidget:SetType(1)
	end
end

---组件刷新统一入口
function HUDAttackBtn:Refresh(...)
end


--- 此处为自动生成
function HUDAttackBtn:on_Btn_Act_Clicked()
end

HUDAttackBtn.RippleWidgetClass = UIAssetPath.WBP_HUDActAnimItem
HUDAttackBtn.BlockActionSlot = InputControlConst.BLOCK_ACTION_SLOT_CONST
HUDAttackBtn.RippleNumMax = 3

function HUDAttackBtn:OnDestroy()
	Game.EventSystem:RemoveObjListeners(self)
end

function HUDAttackBtn:RefreshSlot(Params)
	if Params then
		-- 技能插槽
		self.SkillSlot = Params.SkillSlot
	end
end

function HUDAttackBtn:RefreshUI()
	self:OnOperatorModeChange(Game.SettingsManager:GetIniData("OperatorMode") == 2)
	self:StopPress()
	self:UpdateRippleAnimList()
	self:SetSingleSkillIcon()
end

function HUDAttackBtn:InitRippleAnimList()
	for _ = 1, HUDAttackBtn.RippleNumMax do
		local RippleWidget = import("WidgetBlueprintLibrary").Create(_G.GetContextObject(),
			slua.loadClass(HUDAttackBtn.RippleWidgetClass))
		self.view.Overlay_ActAnimItems:AddChild(RippleWidget)
		local NewAnchors = FAnchors()
		NewAnchors.Minimum = FVector2D(0.5, 0.5)
		NewAnchors.Maximum = FVector2D(0.5, 0.5)
		RippleWidget.Slot:SetAnchors(NewAnchors)
		RippleWidget.Slot:SetAlignment(FVector2D(0.5, 0.5))
		table.insert(self.RippleAnimList, RippleWidget)
		table.insert(self.AvailableRippleList, RippleWidget)
	end
	self.RippleAnimEndTime = 1000 * self.RippleAnimList[#self.RippleAnimList].An_Act_Pressed:GetEndTime()
end

function HUDAttackBtn:UpdateRippleAnimList()
	for i = 1, HUDAttackBtn.RippleNumMax do
		local RippleWidget = self.RippleAnimList[i]
		RippleWidget:SetVisibility(ESlateVisibility.HitTestInvisible)
	end
end


function HUDAttackBtn:_FireAnimTimerCallback(AvailableRipple)
	if AvailableRipple and IsValid_L(AvailableRipple) then
		table.insert(self.AvailableRippleList, AvailableRipple)
		self:StopAllAnimations(AvailableRipple)
	end
end

function HUDAttackBtn:FireAnim()
	local AvailableRipple = self.AvailableRippleList[#self.AvailableRippleList]
	if AvailableRipple and IsValid_L(AvailableRipple) then
		self:PlayAnimation(AvailableRipple.An_Act_Pressed, nil, AvailableRipple, 0.0, 1,
			EUMGSequencePlayMode.Forward, 1, true, nil, nil, true)
		table.remove(self.AvailableRippleList, #self.AvailableRippleList)
		
		self:AddTimer(self.RippleAnimEndTime / 1000, 1, "_FireAnimTimerCallback", AvailableRipple)
		
		--Game.TimerManager:CreateTimerAndStart(
		--	function()
		--		if AvailableRipple and IsValid_L(AvailableRipple) then
		--			table.insert(self.AvailableRippleList, AvailableRipple)
		--			self:StopAllAnimations(AvailableRipple)
		--		end
		--	end,
		--	self.RippleAnimEndTime, 1)
	end
end

function HUDAttackBtn:SetSingleSkillIcon()
	if not self.view or not self.userWidget or not IsValid_L(self.userWidget) then
		return
	end
	self.SkillID = SkillImpl.GetMainChaSkillIDBySlot(self.SkillSlot)
	local ActData = Game.TableData.GetSkillDataNewRow(SkillImpl.GetMainChaSkillIDBySlot(self.SkillSlot))
	if ActData then
		-- Set according to autoskill status
		self.userWidget:SetVisibility(ESlateVisibility.Visible)
		local IconPath = ActData.SkillIcon
		if Game.me.bAutoSkillSwitchOpen then
			IconPath = ActData.ComboIcon
		end
		if IconPath and IconPath ~= "" then
			self:SetImage(self.view.Img_Act, IconPath)
		end
	else
		self.userWidget:SetVisibility(ESlateVisibility.Hidden)
	end
end

function HUDAttackBtn:KeyOnInput(ActionName, InputEvent)
	-- TODO zihan 先糊一下(即便Skill相关的Action屏蔽了，LeftMouse_Action这种现在还是会抛过来)
	if not Game.PlayerController.InputActionAdaptor:IsEnableSkillPressed() then
		return
	end
	if Game.SettingsManager:GetIniData("OperatorMode") ~= Enum.EMouseOperatorMode.ActionMode then
		return
	end

	-- Autoskill or regular attack based on autoskill status
	if (ActionName == "ACT_A_Action" or ActionName == "LeftMouse_Action")  then
		self:FireAnim()
		if Game.me.bAutoSkillSwitchOpen then
			-- When the button is down, begin the auto skill
			if InputEvent == EInputEvent.IE_Pressed then
				Game.SkillSystem:OnPressAttack()
			else
				Game.SkillSystem:OnReleaseAttack()
			end
		end
	end
end

function HUDAttackBtn:onSkillEquipChanged()
	local CurSkillID = SkillImpl.GetMainChaSkillIDBySlot(self.SkillSlot)
	CurSkillID = CurSkillID or -1
	if self.SkillID ~= CurSkillID then
		self.SkillID = CurSkillID
		self:SetSingleSkillIcon()
	end
end

function HUDAttackBtn:OnIsDeadChanged(Entity, PropName, NewValue, OldValue)
	if NewValue == true then
		self:StopPress()
	end
end

function HUDAttackBtn:OnAutoSkillChange()
	if Game.me.bAutoSkillSwitchOpen then
		self:PlayAnimation(self.view.Ani_Switch, nil, self.userWidget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
		self.userWidget:SetType(0)
	else
		self:PlayAnimation(self.view.Ani_Switch, nil, self.userWidget, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
		self.userWidget:SetType(1)
	end
	self:SetSingleSkillIcon()
end

function HUDAttackBtn:OnOperatorModeChange(bClassic)
	if bClassic then
		self.view.WBP_KeyPrompt:SetActiveIndex(0)
	else
		self.view.WBP_KeyPrompt:SetActiveIndex(1)
		self:SetImage(self.view.WBP_KeyPrompt.img_mouse_lua, UIAssetPath.UI_KeyPrompt_Img_MouseWheel03)
	end
end

function HUDAttackBtn:OnClose()
	UIBase.OnClose(self)
	self:StopPress()
end

function HUDAttackBtn:OnTouchStarted(InGeometry, InGestureEvent)
	-- if Game.ScreenInputManager.bZooming == true then
	--     --处于双指缩放，普攻按钮不可点击
	--     return nil
	-- end
	-- 向技能监视器发送消息
	if DebugFlag.OpenAbilityMonitor then
		local SkillID = SkillImpl.GetMainChaSkillIDBySlot(self.SkillSlot)
		LuaDebugProxy.AddSkillRunningInfo(Game.me.eid, ABILITY_MONIOTOR_OPERATION.HUD_BASIC_ATTACK_KEY_DOWN, {AblityID=SkillID},MONITOR_COLOR.BLUE)
	end
	self.FirstPress = false
	local Widget = self.userWidget
	self.AttackPointerIndex = import("KismetInputLibrary").PointerEvent_GetPointerIndex(InGestureEvent)

	local ParentP = self:GetParent()
	if ParentP then
		ParentP:AddOnTouchSlot(self.SkillSlot)
	end
	if ParentP and ParentP:GetSkillRouttleVisibleStateValue() == false then
		--锁着状态，只呼出轮盘不触发攻击
		self:PlayAnimation(Widget.Ani_Act, nil, Widget, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
		ParentP:SetSkillRouttleCollapsedTimer(-1)
		self.FirstPress = true
		return UIBase.HANDLED
	end

	-- 未解锁的普攻
	local SkillID = SkillImpl.GetMainChaSkillIDBySlot(self.SkillSlot)
	if not SkillID then
		return
	end
	if Game.SkillCustomSystem:IsSkillLocked(SkillID) then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_MANAGE_UNLOCK)
		return
	end

	--长按状态
	if self.AttackAnimTimer == nil then
		self.AttackAnimTimer = self:AddTimer(self.RippleAnimEndTime / 1000, -1, "FireAnim")
		
		--self.AttackAnimTimer =
		--Game.TimerManager:CreateTimerAndStart(
		--	function()
		--		self:FireAnim()
		--	end,
		--	self.RippleAnimEndTime, -1
		--)
	end

	-- Autoskill
	if Game.me.bAutoSkillSwitchOpen then
		Game.SkillSystem:OnPressAttack()
	end

	self:onNormalAttackActionTriggered()
	if (self.SkillSlot ~= ETE.EBSSkillSlot.SS_Attack) or (not Game.me.bAutoSkillSwitchOpen and not Game.me:IsAutoSkill()) then
		NotifyInputAction(InputConst.SkillSlotToAction[self.SkillSlot], EInputEvent.IE_Pressed)
	end

	return import("WidgetBlueprintLibrary").CaptureMouse(import("WidgetBlueprintLibrary").Handled(), self.userWidget)
end

function HUDAttackBtn:OnTouchEnded(InGeometry, InGestureEvent)
	self:StopPress()
	return UIBase.HANDLED
end

function HUDAttackBtn:StopPress()
	if DebugFlag.OpenAbilityMonitor then
		local SkillID = SkillImpl.GetMainChaSkillIDBySlot(self.SkillSlot)
		LuaDebugProxy.AddSkillRunningInfo(Game.me.eid, ABILITY_MONIOTOR_OPERATION.HUD_BASIC_ATTACK_KEY_UP, {AblityID=SkillID},MONITOR_COLOR.BLUE)
	end
	if self.AttackPointerIndex >= 0 then
		local ParentP = self:GetParent()
		if ParentP then
			ParentP:DelOnTouchSlot(self.SkillSlot)
			ParentP:SetSkillRouttleCollapsedTimer()
		end

		if self.AttackAnimTimer then
			self:DelTimer(self.AttackAnimTimer)
			--Game.TimerManager:StopTimerAndKill(self.AttackAnimTimer)
			self.AttackAnimTimer = nil
		end

		self:onNormalAttackActionReleased()
		if self.FirstPress == false then
			--第一次打开轮盘不触发普攻
			NotifyInputAction(InputConst.SkillSlotToAction[self.SkillSlot], EInputEvent.IE_Released)
		end
		self.AttackPointerIndex = -1

		-- Autoskill
		if Game.me.bAutoSkillSwitchOpen then
			Game.SkillSystem:OnReleaseAttack()
		end
	end
end

function HUDAttackBtn:RestoreSkillState()
	self:StopPress()
end

function HUDAttackBtn:onNormalAttackActionTriggered()
	self:FireAnim()
	local Widget = self.userWidget
	self:PlayAnimation(Widget.Ani_Act, nil, Widget,  0, 1, EUMGSequencePlayMode.Forward, 1, true)
end

function HUDAttackBtn:onNormalAttackActionReleased()
	local Widget = self.userWidget
	local CurrentTime = Widget:GetAnimationCurrentTime(Widget.Ani_Act)
	self:PlayAnimation(Widget.Ani_Act, nil, Widget, CurrentTime, 1, EUMGSequencePlayMode.Forward, 1.5, true,
		function()
			self:StopAnimation(Widget.Ani_Act, Widget)
		end
	)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDAttackBtn:on_WBP_HUDACT1_TouchStartedEvent(myGeometry, inGestureEvent)
	return self:OnTouchStarted(myGeometry, inGestureEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDAttackBtn:on_WBP_HUDACT1_TouchEndedEvent(myGeometry, inGestureEvent)
	return self:OnTouchEnded(myGeometry, inGestureEvent)
end

return HUDAttackBtn
