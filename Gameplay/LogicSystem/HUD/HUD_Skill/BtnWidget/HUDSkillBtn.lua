local ESlateVisibility = import("ESlateVisibility")
local C7FunctionLibrary = import("C7FunctionLibrary")
local UIFunctionLibrary = import("UIFunctionLibrary")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"
local SkillBtnStateManageComponent = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.HUDSkillBattle.SkillBtn.SkillBtnStateManageComponent")
local SkillBtnInputManageComponent = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.HUDSkillBattle.SkillBtn.SkillBtnInputManageComponent")

local KeyPrompt = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.KeyPrompt")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillBtn : UIComponent
---@field view HUDSkillBtnBlueprint
local HUDSkillBtn = DefineClass("HUDSkillBtn", UIComponent, SkillBtnStateManageComponent, SkillBtnInputManageComponent)

HUDSkillBtn.ActionNameToCallbackName = HUDSkillBtn.ActionNameToCallbackName or {
	[Enum.EInputType.CancelSkill_Action] = "KeyOnInput_CancelSkill",
	[Enum.EInputType.RightMouse_Action] = "KeyOnInput_RightMouse",
	[Enum.EInputType.Exit_Action] = "KeyOnInput_ESC",
}

HUDSkillBtn.eventBindMap = {
	[_G.EEventTypes.CINEMATIC_ON_START] = "OnCinematicStart",
}

HUDSkillBtn.RippleWidgetClass = UIAssetPath.WBP_HUDActAnimItem
HUDSkillBtn.RippleNumMax = 3
-- Tick更新技能按钮状态的时间间隔
HUDSkillBtn.SkillRefInterval = 0.2

HUDSkillBtn.SkillBtnState = {
	Lock = "Lock",     --锁定状态
	Common = "Common", -- 普通状态
	Loop = "Loop",     -- 循环技能状态
	Combo = "Combo",   -- Combo状态
	CD = "CD"          -- CD状态
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillBtn:InitUIData()
	table.merge(HUDSkillBtn.eventBindMap, SkillBtnInputManageComponent.eventBindMap)
	table.merge(HUDSkillBtn.eventBindMap, SkillBtnStateManageComponent.eventBindMap)
	self.State = HUDSkillBtn.SkillBtnState.Common
	self.SkillSlot = ETE.EBSSkillSlot.SS_TMax
	-- 技能ID为-1表示不存在配置技能
	self.SkillID = -1
	-- 机制类显示的假技能id
	self.FakeSkillID = -1
	-- 记录当前技能是否锁定
	-- 用于记录CD状态时的剩余充能次数，来判断是否仍能够触发技能输入
	-- 技能移动的死区保护
	self.AimSkillFirstEnterMove = false
	-- 记录是否在刷新中
	self.bRefresh = false

	-- 记录当前的技能推荐动效开启状态
	self.PointerIndex = -1

	self.SkillRefreshTimer = 0.0

	--特殊右键操作
	self.bPressingMouseRightBtn = false
	self.bSetMouseRightMoveCameraPos = false

	-- 记录当前的绝技是否可以释放
	self.bExtraSkillCanRelease = false
	--hover态
	self.bHover = false
	self.bMobilePlatform = PlatformUtil.IsMobilePlatform() or
		(import("C7FunctionLibrary").IsC7Editor() and
			import("UIFunctionLibrary").GetPreviewPlatform() ~= EDPIScalePreviewPlatforms.PC)
end

--- UI组件初始化，此处为自动生成
function HUDSkillBtn:InitUIComponent()
    ---@type KeyPrompt
    self.WBP_KeyPromptCom = self:CreateComponent(self.view.WBP_KeyPrompt, KeyPrompt)
end

---UI事件在这里注册，此处为自动生成
function HUDSkillBtn:InitUIEvent()
    self:AddUIEvent(self.view.OnTouchStartedEvent, "on_WBP_HUDSkillNew1_TouchStartedEvent")
    self:AddUIEvent(self.view.OnTouchMovedEvent, "on_WBP_HUDSkillNew1_TouchMovedEvent")
    self:AddUIEvent(self.view.OnTouchEndedEvent, "on_WBP_HUDSkillNew1_TouchEndedEvent")
    self:AddUIEvent(self.view.OnMouseEnterEvent, "on_WBP_HUDSkillNew1_MouseEnterEvent")
    self:AddUIEvent(self.view.OnMouseLeaveEvent, "on_WBP_HUDSkillNew1_MouseLeaveEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSkillBtn:InitUIView()
	local isEditorPCStyle = C7FunctionLibrary.IsC7Editor() and UIFunctionLibrary.GetPreviewPlatform and
		UIFunctionLibrary.GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC
	local isPackagePCStyle = not C7FunctionLibrary.IsC7Editor() and not PlatformUtil.IsMobilePlatform()
	if isEditorPCStyle or isPackagePCStyle then
		self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.WBP_KeyPrompt:SetVisibility(ESlateVisibility.Collapsed)
	end
end

---组件刷新统一入口
function HUDSkillBtn:Refresh(...)
	self.view.Text_AboutSkill:SetText("")
end



function HUDSkillBtn:FireAnim(bDisable)
	Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_ON_SKILL_BTN_FIRE_ANIM, self, self.view.Overlay_ActAnimItems, bDisable)
end

---@return MaterialInstanceDynamic
function HUDSkillBtn:GetDynamicMaterial()
	return self.view.MID:GetDynamicMaterial()
end

function HUDSkillBtn:OnDestroy()
	Game.EventSystem:RemoveObjListeners(self)
	if Game.me then
		Game.UniqEventSystemMgr:RemoveListener(Game.me:uid(), EEventTypesV2.ON_SELF_ULTIMATE_POINT_CHANGED, "OnJueJiChange", self)	
	end
	Game.BSQTEManager.OnAnyQTEStarted:Remove(self, "OnStartQTE")
end

function HUDSkillBtn:onRoleActionInput(actionName, inputEvent)
	local callbackName = HUDSkillBtn.ActionNameToCallbackName[actionName]
	if callbackName then
		self[callbackName](self, inputEvent)
	end
end

function HUDSkillBtn:RefreshSlot(Params)
	if Params then
		-- 技能插槽
		self.SkillSlot = Params.SkillSlot
	end
	self.PointerIndex = -1
	self.AimSkillFirstEnterMove = false
	local tSkillData = Game.TableData.GetSkillDataNewRow(SkillImpl.GetMainChaSkillIDBySlot(self.SkillSlot))
	if tSkillData and tSkillData.FightPropCondition then
		if self.SkillSlot == ETE.EBSSkillSlot.SS_ExtraordinarySlot then
			if Game.me then
				Game.UniqEventSystemMgr:RemoveListener(Game.me:uid(), EEventTypesV2.ON_SELF_ULTIMATE_POINT_CHANGED, "OnJueJiChange", self)
				Game.UniqEventSystemMgr:AddListener(Game.me:uid(), EEventTypesV2.ON_SELF_ULTIMATE_POINT_CHANGED, "OnJueJiChange", self)
			end
		end
	end
end

function HUDSkillBtn:OnProfessionPropChanged(NewValue, OldValue)
	self:RefreshResourceState()
end

function HUDSkillBtn:OnCinematicStart()
	self:StopPress()
end

function HUDSkillBtn:StopPress()
	if self.PointerIndex < 0 then
		-- 没按
		return
	end
	self:DoStopPress(self.PointerIndex)
end

function HUDSkillBtn:OnRightClick(PointerIndex)
	if PointerIndex and PointerIndex == 10 then
		--取消技能释放
		self:BreakAimSKill()
		Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_BTNRELEASED, self.SkillSlot, false)
	end
end

--理论上不需要判断Actor是否是玩家, 因为客户端只有一个PlayerController
function HUDSkillBtn:KeyOnInput_CancelSkill(InputEvent)
	if InputEvent == 0 then
		self:BreakAimSKill()
		Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_BTNRELEASED, self.SkillSlot, false)
	end
end

function HUDSkillBtn:KeyOnInput_RightMouse(InputEvent)
	self:BreakAimSKill()

	self.bPressingMouseRightBtn = InputEvent == 0
	self.bSetMouseRightMoveCameraPos = false
	if Game.CameraInputManager.PointerIndex ~= -1 and self.bPressingMouseRightBtn then
		--已经在滑镜头就忽略右键
		self.bPressingMouseRightBtn = false
	end
end

function HUDSkillBtn:KeyOnInput_ESC()
	if self:IsAiming() then
		self:StopPress()
	end
end

function HUDSkillBtn:IsAiming()
	return Game.PlayerController.BattleSystemInputProcessor and Game.PlayerController.BattleSystemInputProcessor:IsAiming() or false
end


--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDSkillBtn:on_WBP_HUDSkillNew1_TouchStartedEvent(myGeometry, inGestureEvent)
	return self:OnTouchStarted(myGeometry, inGestureEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDSkillBtn:on_WBP_HUDSkillNew1_TouchMovedEvent(myGeometry, inGestureEvent)
	return self:OnTouchMoved(myGeometry, inGestureEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDSkillBtn:on_WBP_HUDSkillNew1_TouchEndedEvent(myGeometry, inGestureEvent)
	return self:OnTouchEnded(myGeometry, inGestureEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent
function HUDSkillBtn:on_WBP_HUDSkillNew1_MouseEnterEvent(myGeometry, inMouseEvent)
	self:MouseEnter()
end


--- 此处为自动生成
---@param inMouseEvent FPointerEvent
function HUDSkillBtn:on_WBP_HUDSkillNew1_MouseLeaveEvent(inMouseEvent)
	self:MouseLeave()
end

return HUDSkillBtn
