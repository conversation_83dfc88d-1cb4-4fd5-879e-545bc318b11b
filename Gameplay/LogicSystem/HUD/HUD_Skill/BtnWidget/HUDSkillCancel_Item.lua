local UIFunctionLibrary = import("UIFunctionLibrary")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")
local ESlateVisibility = import("ESlateVisibility")
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"

local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class HUDSkillCancel_Item : UIListItem
---@field view HUDSkillCancel_ItemBlueprint
local HUDSkillCancel_Item = DefineClass("HUDSkillCancel_Item", UIListItem)

HUDSkillCancel_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillCancel_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillCancel_Item:InitUIData()
	self.State = 0
end

--- UI组件初始化，此处为自动生成
function HUDSkillCancel_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDSkillCancel_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSkillCancel_Item:InitUIView()
end

---面板打开的时候触发
function HUDSkillCancel_Item:OnRefresh(...)
end

function HUDSkillCancel_Item:Refresh(uiComponent)
	self.AttachedComponent = uiComponent
	self.State = 0
	self.userWidget:Event_UI_State(0)
	SkillImpl.onAimBtnCrossCancelBtn(false)
	self.AimTimer = nil
	if Game.SettingsManager:ReadSettingByConfig(Enum.ESettingConstData.SKILL_RELEASE_CANCEL_MODE) == 0 then
		self.view.Text_Cancel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Text_Cancel_1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Img_Cancel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self:GetParent().view.Btn_Cancle:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Text_Cancel:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Text_Cancel_1:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_Cancel:SetVisibility(ESlateVisibility.Collapsed)
		local Style = self:GetParent().view.Btn_Cancle.WidgetStyle
		Style.Normal = Style.Disabled
		self:GetParent().view.Btn_Cancle:SetStyle(Style)
		self:GetParent().view.Btn_Cancle:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
	--local ViewportSize = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
	if self.AttachedComponent.userWidget.Slot:IsA(import("CanvasPanelSlot")) then
		self.WidgetCenterLocation = self.AttachedComponent.userWidget.Slot:GetPosition()
		self.TargetWidgetCenterLocation = self:GetParent().view.Btn_Cancle.Slot:GetPosition()
	end

end

function HUDSkillCancel_Item:OnReturnToPool()
	self.AimTimer = nil
	self.AttachedComponent = nil
	self.State = 0
	self:StopTimer("SkillBtn_Aim_ChangeState")
	self:StopTimer("DelayGetLocation")
	self.view.Text_Cancel:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Text_Cancel_1:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Img_Cancel:SetVisibility(ESlateVisibility.Collapsed)
	self:GetParent().view.Btn_Cancle:SetVisibility(ESlateVisibility.Collapsed)
end

---@type Anchors
local TempWidgetAnchor = import("Anchors")()
local TempCenterVector2D =  FVector2D(0.5, 0.5)
function HUDSkillCancel_Item:OnAimPosMoved(MoveOffset)
	local Size = MoveOffset:Size()
	local Normalized = MoveOffset:GetSafeNormal(1e-8)
	if Game.SettingsManager:ReadSettingByConfig(Enum.ESettingConstData.SKILL_RELEASE_CANCEL_MODE) == 0 then
		local ViewportSize = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
		local threshold = 350
		local isEidtorPCStyle = _G.UE_EDITOR and UIFunctionLibrary.GetPreviewPlatform and
			UIFunctionLibrary.GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC
		local isPackagePCStyle = not _G.UE_EDITOR and not PlatformUtil.IsMobilePlatform()
		if isEidtorPCStyle or isPackagePCStyle then
			threshold = 275
		end
		if Size > threshold / ViewportSize then
			if self.State == 0 and not self.AimTimer then
				self.AimTimer = self:StartTimer("SkillBtn_Aim_ChangeState", function()
					self.userWidget:Event_UI_State(1)
					self.State = 1
					SkillImpl.onAimBtnCrossCancelBtn(true)
				end, Game.SettingsManager:ReadSettingByConfig(Enum.ESettingConstData.SKILL_RELEASE_CANCEL_TIME), 1)
			end
		else
			if self.State == 1 then
				self:StopTimer("SkillBtn_Aim_ChangeState")
				self.AimTimer = nil
				self.State = 0
				self.userWidget:Event_UI_State(0)
				SkillImpl.onAimBtnCrossCancelBtn(false)
			end
		end
	elseif self.TargetWidgetCenterLocation then
		if (self.WidgetCenterLocation + MoveOffset - self.TargetWidgetCenterLocation):Size() < 100 then
			if self.State == 0 and not self.AimTimer then
				local Style = self:GetParent().view.Btn_Cancle.WidgetStyle
				Style.Normal = Style.Hovered
				self:GetParent().view.Btn_Cancle:SetStyle(Style)
				self.AimTimer = self:StartTimer("SkillBtn_Aim_ChangeState", function()
					self.userWidget:Event_UI_State(1)
					self.State = 1
					SkillImpl.onAimBtnCrossCancelBtn(true)
				end, Game.SettingsManager:ReadSettingByConfig(Enum.ESettingConstData.SKILL_RELEASE_CANCEL_TIME), 1)
			end
		else
			if self.State == 1 then
				local Style = self:GetParent().view.Btn_Cancle.WidgetStyle
				Style.Normal = Style.Disabled
				self:GetParent().view.Btn_Cancle:SetStyle(Style)
				self:StopTimer("SkillBtn_Aim_ChangeState")
				self.AimTimer = nil
				self.State = 0
				self.userWidget:Event_UI_State(0)
				SkillImpl.onAimBtnCrossCancelBtn(false)
			end
		end
	end


	local Angle = Normalized.Y > 0 and math.acos(Normalized.X) or (2 * math.pi - math.acos(Normalized.X))
	self.view.CP_Cancel:SetRenderTransformAngle(Angle * 180 / math.pi + 90)

	local Vec = Normalized * Size / (390)

	if Vec:Size() > 0.4 then--控件图片的安全区
		Vec = Vec * 0.4 / Vec:Size()
	end
	SkillImpl.onSkillJoyStickMoved(Vec / 0.4)

	Vec = Vec + TempCenterVector2D
	TempWidgetAnchor.Minimum = Vec
	TempWidgetAnchor.Maximum = Vec
	self.view.Img_SkillJoystick.Slot:SetAnchors(TempWidgetAnchor)
end


return HUDSkillCancel_Item
