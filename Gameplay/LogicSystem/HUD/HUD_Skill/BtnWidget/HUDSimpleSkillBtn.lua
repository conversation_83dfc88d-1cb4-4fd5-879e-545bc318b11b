local ESlateVisibility = import("ESlateVisibility")
local const = kg_require("Shared.Const")
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"

local HUDBtnCD = kg_require("Gameplay.LogicSystem.HUD.HUDBtn.HUDBtnCD")
local KeyPrompt = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.KeyPrompt")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSimpleSkillBtn : UIComponent
---@field view HUDSimpleSkillBtnBlueprint
local HUDSimpleSkillBtn = DefineClass("HUDSimpleSkillBtn", UIComponent)

HUDSimpleSkillBtn.eventBindMap = {
	[_G.EEventTypes.ON_ROLE_PLAY_SKILLList_CHANGE] = "OnRolePlaySkillChange",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSimpleSkillBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSimpleSkillBtn:InitUIData()
	self.PointerIndex = -1
	self.SkillID = -1
	self.OuterSkillID = -1
	self.bHover = false
	self.OnTouchEndedTimeStamp = -1
end

--- UI组件初始化，此处为自动生成
function HUDSimpleSkillBtn:InitUIComponent()
    ---@type HUDBtnCD
    self.WBP_CDCom = self:CreateComponent(self.view.WBP_CD, HUDBtnCD)
    ---@type KeyPrompt
    self.WBP_KeyPromptCom = self:CreateComponent(self.view.WBP_KeyPrompt, KeyPrompt)
end

---UI事件在这里注册，此处为自动生成
function HUDSimpleSkillBtn:InitUIEvent()
    self:AddUIEvent(self.view.OnTouchStartedEvent, "on_WBP_HUDSimpleSkillItem_TouchStartedEvent")
    self:AddUIEvent(self.view.OnTouchMovedEvent, "on_WBP_HUDSimpleSkillItem_TouchMovedEvent")
    self:AddUIEvent(self.view.OnTouchEndedEvent, "on_WBP_HUDSimpleSkillItem_TouchEndedEvent")
    self:AddUIEvent(self.view.OnMouseEnterEvent, "on_WBP_HUDSimpleSkillItem_MouseEnterEvent")
    self:AddUIEvent(self.view.OnMouseLeaveEvent, "on_WBP_HUDSimpleSkillItem_MouseLeaveEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSimpleSkillBtn:InitUIView()
end

---组件刷新统一入口
function HUDSimpleSkillBtn:Refresh(...)
end

function HUDSimpleSkillBtn:RefreshSlot(Params, SlotName)
	if Params then
		-- 技能插槽
		self.SkillSlot = Params.SkillSlot
	end
end

function HUDSimpleSkillBtn:RefreshUI()
	if Game.SkillSystem:GetSkillRouttleState() == const.SKILL_ROUTTLE_STATE.RolePlay then
		self.userWidget:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		local TrueSlot =  Game.SkillSystem:GetRolePlaySkillSlotMap()[tostring(self.SkillSlot)]
		if self.SkillID ~= Game.SkillSystem:GetSkillList()[TrueSlot] then
			self.SkillID = Game.SkillSystem:GetSkillList()[TrueSlot]
			if self.SkillID and self.SkillID ~= -1 then
				self.userWidget:SetEmpty(0)
				self.OuterSkillID = Game.TableData.GetRolePlayActingSkillDataRow(self.SkillID).OuterSkillID
				local tSkillData = Game.TableData.GetSkillDataNewRow(self.OuterSkillID)
				if tSkillData then
					if tSkillData.SkillIcon ~= "" then
						self:SetImage(self.view.Icon_Skill, tSkillData.SkillIcon)
					end
					self.view.Text_AboutSkill:SetText(tSkillData.Tag)
				end
			else
				self.userWidget:SetEmpty(1)
			end
		end
		if self.SkillID then
			self:OnCDDisplay()
		else
			self.userWidget:SetEmpty(1)
			self.OuterSkillID = nil
		end
		self.view.Img_Disable:SetVisibility(ESlateVisibility.Collapsed)
	elseif Game.SkillSystem:GetSkillRouttleState() == const.SKILL_ROUTTLE_STATE.Action then
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	elseif Game.SkillSystem:GetSkillRouttleState() == const.SKILL_ROUTTLE_STATE.InvisibleHand then
		--无形之手   
		self.SkillID = Game.SkillSystem:GetInvisibleHandSkillList()[self.SkillSlot]
		if self.SkillID then
			self.userWidget:SetEmpty(0)
			local tSkillData = Game.TableData.GetSkillDataNewRow(self.SkillID)
			self.view.Icon_Skill:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			self.view.Text_AboutSkill:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			self:SetImage(self.view.Icon_Skill, tSkillData.SkillIcon)
			self.view.Text_AboutSkill:SetText(tSkillData.Tag)
			self.view.WBP_CD:SetVisibility(ESlateVisibility.Collapsed)
			self.view.Img_Disable:SetVisibility(ESlateVisibility.Collapsed)
		else
			self.userWidget:SetEmpty(1)
			self.view.Icon_Skill:SetVisibility(ESlateVisibility.Collapsed)
			self.view.Text_AboutSkill:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
	self.view.Img_Hover:SetVisibility(ESlateVisibility.Collapsed)
end

function HUDSimpleSkillBtn:OnRolePlaySkillChange()
	self:RefreshUI()
end

function HUDSimpleSkillBtn:OnCDDisplay()
	self.view.WBP_CD:SetVisibility(ESlateVisibility.Collapsed)
end

function HUDSimpleSkillBtn:OnTouchStarted(InGeometry, InGestureEvent)
	local PointerIndex = import("KismetInputLibrary").PointerEvent_GetPointerIndex(InGestureEvent)
	self.PointerIndex = PointerIndex
	if self.SkillID == nil or self.SkillID == -1 then
		return import("WidgetBlueprintLibrary").CaptureMouse(import("WidgetBlueprintLibrary").Handled(), self.userWidget)
	end
	if Game.HUDSystem.IsInPC() == false then
		self:StartTimer("SkillDescpDelayShowTimer",
			function()
				self:UpdateShowSkillDescrp(true)
			end, Game.TableData.GetConstDataRow("SKILLTIPS_TRIGGER_TIME") * 1000, 1)
	end
	if Game.SkillSystem:GetSkillRouttleState() == const.SKILL_ROUTTLE_STATE.InvisibleHand and
		(self.SkillSlot == 2 or self.SkillSlot == 3) then
		self:StartTimer(
			"InvisibleHand",
			function()
				if self.SkillSlot == 2 then
					NotifyInputAxis("LookUp_Axis", 1)
				elseif self.SkillSlot == 3 then
					NotifyInputAxis("LookUp_Axis", -1)
				end
			end, 33, -1, nil, true
		)
	end
	return import("WidgetBlueprintLibrary").CaptureMouse(import("WidgetBlueprintLibrary").Handled(), self.userWidget)
end

function HUDSimpleSkillBtn:OnTouchMoved(InGeometry, InGestureEvent)

end

function HUDSimpleSkillBtn:OnTouchEnded(InGeometry, InGestureEvent)
	self.PointerIndex = -1
	if self.SkillID == nil or self.SkillID == -1 then
		return
	end
	if self.OnTouchEndedTimeStamp ~= -1 and _G._now() - self.OnTouchEndedTimeStamp <= 1000 then
		return
	end
	if Game.SkillSystem:GetSkillRouttleState() == const.SKILL_ROUTTLE_STATE.RolePlay then
		self:IntoRolePlaySkillProcess(InGestureEvent)
	elseif Game.SkillSystem:GetSkillRouttleState() == const.SKILL_ROUTTLE_STATE.InvisibleHand then
		self:IntoInvisibleHandSkillProcess()
	end
	if Game.HUDSystem.IsInPC() == false then
		self:StopTimer("SkillDescpDelayShowTimer")
	end
end

function HUDSimpleSkillBtn:MouseEnter()
	if self.PointerIndex == -1 and self.SkillID and self.SkillID ~= -1 and Game.HUDSystem.IsInPC() then
		self.bHover = true
		self:StartTimer("ShowDescrpTimer", function()
			--停止动画
			if self.bHover then
				self:UpdateShowSkillDescrp(true)
			end
		end, Game.TableData.GetConstDataRow("SKILLTIPS_HOVER_TIME")*1000, 1)
		self.view.Img_Hover:SetVisibility(ESlateVisibility.selfHitTestInvisible)
	end
end

function HUDSimpleSkillBtn:MouseLeave()
	if Game.HUDSystem.IsInPC() then
		self.bHover = false
		self:UpdateShowSkillDescrp(false)
		self.view.Img_Hover:SetVisibility(ESlateVisibility.Collapsed)
	end
end

-- 更新技能介绍页签显示
function HUDSimpleSkillBtn:UpdateShowSkillDescrp(bShow)
	if self.OuterSkillID ~= -1 then
		Game.SkillCustomSystem:ShowSkillDescUI(self.OuterSkillID, bShow)
	end
end

------------------------------技能释放处理begin--------------------------
--角色扮演
function HUDSimpleSkillBtn:IntoRolePlaySkillProcess(InGestureEvent)
	local PointerIndex = import("KismetInputLibrary").PointerEvent_GetPointerIndex(InGestureEvent)
	if self.PointerIndex == PointerIndex then
		if Game.me.IsPlayingMorph == false then
			Game.me:ReleaseRolePlaySkill(self.SkillID, Game.me.eid,
				{ SkillImpl.GetLockTargetUID(ETE.EBSTargetType.TT_Skill) })
			self.OnTouchEndedTimeStamp = _G._now()
		end
	end
end

--无形之手
function HUDSimpleSkillBtn:IntoInvisibleHandSkillProcess()
	if self.SkillSlot == 4 then
		-- 退出无形之手
		-- Game.me:ReqExitControlNpcInteract()
		Game.SkillSystem:UpdatetInvisibleHandSkillList(0, 8091021)
	end
	self:StopTimer("InvisibleHand")
end


--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDSimpleSkillBtn:on_WBP_HUDSimpleSkillItem_TouchStartedEvent(myGeometry, inGestureEvent)
	return self:OnTouchStarted(myGeometry, inGestureEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDSimpleSkillBtn:on_WBP_HUDSimpleSkillItem_TouchMovedEvent(myGeometry, inGestureEvent)
	return self:OnTouchMoved(myGeometry, inGestureEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inGestureEvent FPointerEvent

function HUDSimpleSkillBtn:on_WBP_HUDSimpleSkillItem_TouchEndedEvent(myGeometry, inGestureEvent)
	return self:OnTouchEnded(myGeometry, inGestureEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inMouseEvent FPointerEvent
function HUDSimpleSkillBtn:on_WBP_HUDSimpleSkillItem_MouseEnterEvent(myGeometry, inMouseEvent)
	self:MouseEnter()
end

--- 此处为自动生成
---@param inMouseEvent FPointerEvent
function HUDSimpleSkillBtn:on_WBP_HUDSimpleSkillItem_MouseLeaveEvent(inMouseEvent)
	self:MouseLeave()
end

return HUDSimpleSkillBtn
