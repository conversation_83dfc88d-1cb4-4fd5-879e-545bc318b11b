
local HUDLifeSkillBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.HUDLifeSkillBtn")

---@class HUDRideBtn : HUDLifeSkillBtn
---@field view HUDLifeSkillBtnBlueprint
local HUDRideBtn = DefineClass("HUDRideBtn", HUDLifeSkillBtn)

HUDRideBtn.eventBindMap = {
    [EEventTypesV2.MOUNT_RIDER_STATUS_CHANGED] = "RefreshVisible",
    [EEventTypesV2.MOUNT_CUR_MOUNT_CHANGED] = "RefreshVisible",
	--[_G.EEventTypes.ROLE_ACTION_INPUT_EVENT] = "OnKeyAction",
	[_G.EEventTypes.SYSTEM_ACTION_INPUT_EVENT] = {"OnKeyAction", Enum.EInputTypeSystemGroup.ToggleRideHorse_Action},
}

function HUDRideBtn:InitUIEvent()
	HUDLifeSkillBtn.InitUIEvent(self)
    self:AddUIEvent(self.onClickEvent, "OnClickEvent")
end

---@private
function HUDRideBtn:RefreshSlot()
	self:RefreshVisible()
end

function HUDRideBtn:Refresh()
	self:RefreshVisible()
end

function HUDRideBtn:RefreshVisible()
	self.param = Game.MountSystem:GetNowRideBtnParam(self.param)
	local visible = self.param and next(self.param)~=nil
	self:SetVisible(visible)
	if visible then
		--self.param.KeyPrompt = self.KeyPrompt
		HUDLifeSkillBtn.Refresh(self, self.param)
	end
end

function HUDRideBtn:OnClickEvent()
	Game.MountSystem:ToggleRideMount()
end

function HUDRideBtn:OnHandleKeyAction()
	self:OnClickEvent()
end

return HUDRideBtn
