-- 技能轮盘基类，平台无关的代码放这里，子类放具体平台
local HUDSkillRouletteBase = DefineClass("HUDSkillRouletteBase")

HUDSkillRouletteBase.eventBindMap = {
	[EEventTypesV2.ROLE_ON_SKILL_READY] = "onSkillReady",
	[EEventTypesV2.ON_OPEN_FULLSCREEN_PANEL] = "onOpenFullScreen",
	[EEventTypesV2.ROLE_ACTION_INPUT_EVENT] = "onRoleActionInput",
}

function HUDSkillRouletteBase:refreshChildNode(methodName, ...)
	for _, V in pairs(self.SkillCellBindData) do
		if V.CellInst then
			local func = V.CellInst[methodName]
			if func then
				func(V.CellInst, ...)
			end
		end
	end
end

function HUDSkillRouletteBase:onSkillReady()
	self:refreshChildNode("RefreshUI")
end

function HUDSkillRouletteBase:onOpenFullScreen()
	self:refreshChildNode("BreakAimSKill")
end

---@param actionName string
---@param inputEvent EInputEvent
function HUDSkillRouletteBase:onRoleActionInput(actionName, inputEvent)
	self:refreshChildNode("onRoleActionInput", actionName, inputEvent)
end