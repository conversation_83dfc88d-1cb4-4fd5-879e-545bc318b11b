local HUDSimpleSkillBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSimpleSkillBtn")
local HUDSimpleAttackBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSimpleAttackBtn")
local HUDSkillSimpleRouletteBase = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.Roulette.HUDSkillSimpleRouletteBase")

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillSimpleRoulette_PC : UIComponent
---@field view HUDSkillSimpleRoulette_PC_PanelBlueprint
local HUDSkillSimpleRoulette_PC = DefineClass("HUDSkillSimpleRoulette_PC", UIComponent, HUDSkillSimpleRouletteBase)

HUDSkillSimpleRoulette_PC.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillSimpleRoulette_PC:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillSimpleRoulette_PC:InitUIData()
	table.merge(HUDSkillSimpleRoulette_PC.eventBindMap, HUDSkillSimpleRouletteBase.eventBindMap)
end

--- UI组件初始化，此处为自动生成
function HUDSkillSimpleRoulette_PC:InitUIComponent()
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill10Com = self:CreateComponent(self.view.Btn_Skill10, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill09Com = self:CreateComponent(self.view.Btn_Skill09, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill08Com = self:CreateComponent(self.view.Btn_Skill08, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill07Com = self:CreateComponent(self.view.Btn_Skill07, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill06Com = self:CreateComponent(self.view.Btn_Skill06, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill05Com = self:CreateComponent(self.view.Btn_Skill05, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill04Com = self:CreateComponent(self.view.Btn_Skill04, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill03Com = self:CreateComponent(self.view.Btn_Skill03, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill02Com = self:CreateComponent(self.view.Btn_Skill02, HUDSimpleSkillBtn)
    ---@type HUDSimpleSkillBtn
    self.Btn_Skill01Com = self:CreateComponent(self.view.Btn_Skill01, HUDSimpleSkillBtn)
    ---@type HUDSimpleAttackBtn
    self.WBP_HUDACTCom = self:CreateComponent(self.view.WBP_HUDACT, HUDSimpleAttackBtn)
    
end

---UI事件在这里注册，此处为自动生成
function HUDSkillSimpleRoulette_PC:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
-- luacheck: ignore
function HUDSkillSimpleRoulette_PC:InitUIView()
	self.SkillCellBindData = {
		Slot01 = {
			SkillWidget = self.view.Btn_Skill01,
			CellInst = self.Btn_Skill01Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot01
			},
			KeyPromptAction = "Skill_01"
		},
		Slot02 = {
			SkillWidget = self.view.Btn_Skill02,
			CellInst = self.Btn_Skill02Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot02
			},
			KeyPromptAction = "Skill_02"
		},
		Slot03 = {
			SkillWidget = self.view.Btn_Skill03,
			CellInst = self.Btn_Skill03Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot03
			},
			KeyPromptAction = "Skill_03"
		},
		Slot04 = {
			SkillWidget = self.view.Btn_Skill04,
			CellInst = self.Btn_Skill04Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot04
			},
			KeyPromptAction = "Skill_04"
		},
		Slot05 = {
			SkillWidget = self.view.Btn_Skill05,
			CellInst = self.Btn_Skill05Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot05
			},
			KeyPromptAction = "Skill_05"
		},
		Slot06 = {
			SkillWidget = self.view.Btn_Skill06,
			CellInst = self.Btn_Skill06Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_DeControlSlot
			},
			KeyPromptAction = "DeControl"
		},
		Slot07 = {
			SkillWidget = self.view.Btn_Skill07,
			CellInst = self.Btn_Skill07Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot06
			},
			KeyPromptAction = "Skill_06"
		},
		FellowSlot1 = {
			SkillWidget = self.view.Btn_Skill08,
			CellInst = self.Btn_Skill08Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_FellowSlot1
			},
			KeyPromptAction = "FellowSkill_01"
		},
		FellowSlot2 = {
			SkillWidget = self.view.Btn_Skill09,
			CellInst = self.Btn_Skill09Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_FellowSlot2
			},
			KeyPromptAction = "FellowSkill_02"
		},
		FellowSlot3 = {
			SkillWidget = self.view.Btn_Skill10,
			CellInst = self.Btn_Skill10Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_ExtraordinarySlot
			},
			KeyPromptAction = "ExtraordinarySkill"
		},
		SlotAttack = {
			SkillWidget = self.view.WBP_HUDACT,
			CellInst = self.WBP_HUDACTCom,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Attack
			},

			KeyPromptAction = "ACT_A"
		},
	}
end

---面板打开的时候触发
function HUDSkillSimpleRoulette_PC:OnRefresh(...)
	Game.SkillSystem:RefreshRolePlaySkillList()
	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst then
			V.CellInst:RefreshSlot(V.CellInitParams)
			if V.CellInst.RefreshUI then
				V.CellInst:RefreshUI()
			end
			if V.KeyPromptAction then
				V.CellInst.WBP_KeyPromptCom:Refresh(V.KeyPromptAction)
			end
		end
	end
end

return HUDSkillSimpleRoulette_PC
