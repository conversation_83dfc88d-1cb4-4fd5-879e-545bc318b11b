local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillRouletteTotal : UIComponent
---@field view HUDSkillRouletteTotal_PanelBlueprint
local HUDSkillRouletteTotal = DefineClass("HUDSkillRouletteTotal", UIComponent)

local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")
local C7FunctionLibrary = import("C7FunctionLibrary")
local UIFunctionLibrary = import("UIFunctionLibrary")
local WidgetBlueprintLibrary = import("WidgetBlueprintLibrary")
local ESlateVisibility = import("ESlateVisibility")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local const = kg_require("Shared.Const")
local EReleaseSkillResult = kg_require("Shared.Const.AbilityConst").EReleaseSkillResult
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"
local BeHitConst = kg_require("Shared.Const.BeHitConst")
local FORBID_NONEDECONTROL_SKILL_MAP = BeHitConst.FORBID_NONEDECONTROL_SKILL_MAP
local EDetailDisableSkillReason = kg_require("Shared.Const.AbilityConst").EDetailDisableSkillReason

HUDSkillRouletteTotal.eventBindMap = {
	[EEventTypesV2.ON_HUD_ROUTTLE_STATE_CHANGED] = "RefreshChildUI",
	[_G.EEventTypes.SKILLHUD_REFRESH_SKILL_ACTIVATE_RESULT] = "OnReceiveSkillActivateResult",
	[_G.EEventTypes.SKILLHUD_ON_CARD_DEBUFF_END] = "OnCardDebuffEnd",
	[_G.EEventTypes.SKILLHUD_ON_SKILL_BTN_FIRE_ANIM] = "RequestRippleWidget",
	
}

HUDSkillRouletteTotal.CardDebuffEffect = UIAssetPath.WBP_HUDFeatherSkillCardTail

-- 技能释放结果到提示文本的映射
HUDSkillRouletteTotal.SkillResultToReminderMsg = {
	-- 按照EReleaseSkillResult枚举定义顺序排列
	[EReleaseSkillResult.NoSkillData] = Enum.EReminderTextData.SKILL_MANAGE_UNLOCK,
	[EReleaseSkillResult.InCoolDown] = Enum.EReminderTextData.SKILL_IN_COOL_DOWN,
	[EReleaseSkillResult.InValidTarget] = Enum.EReminderTextData.SKILL_TARGETLACK,
	[EReleaseSkillResult.DisabledType] = Enum.EReminderTextData.SKILL_IN_DISABLE,
	[EReleaseSkillResult.DisabledTag] = Enum.EReminderTextData.SKILL_IN_DISABLE,
	[EReleaseSkillResult.NotEnoughProp] = Enum.EReminderTextData.SKILL_FIGHTRES_NOT_ENOUGH,
	[EReleaseSkillResult.NotEnoughSkillPower] = Enum.EReminderTextData.SKILL_FIGHTRES_NOT_ENOUGH,
	[EReleaseSkillResult.SkillIsLocked] = Enum.EReminderTextData.SKILL_MANAGE_UNLOCK,
	[EReleaseSkillResult.NotEnoughFightRes] = Enum.EReminderTextData.SKILL_FIGHTRES_NOT_ENOUGH,
}

HUDSkillRouletteTotal.RippleWidgetClass = UIAssetPath.WBP_HUDActAnimItem
HUDSkillRouletteTotal.RippleNumMax = 3

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillRouletteTotal:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

function HUDSkillRouletteTotal:OnClose()
	self.ChildUIName = ""
end

---初始化数据
function HUDSkillRouletteTotal:InitUIData()
	self.ChildUIName = ""
end

--- UI组件初始化，此处为自动生成
function HUDSkillRouletteTotal:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDSkillRouletteTotal:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDSkillRouletteTotal:InitUIView()
	local UClass = slua.loadClass(HUDSkillRouletteTotal.RippleWidgetClass)
	self.AvailableRippleList = {}
	for i = 1, HUDSkillRouletteTotal.RippleNumMax do
		local RippleWidget = WidgetBlueprintLibrary.Create(_G.GetContextObject(), UClass)
		table.insert(self.AvailableRippleList, RippleWidget)
	end

	if GetMainPlayerPropertySafely("Profession") == 1200003 then
		self:LoadRes(HUDSkillRouletteTotal.CardDebuffEffect, function(Res)
			self:OnResourceLoaded(Res)
		end)
	end
end

---面板打开的时候触发
function HUDSkillRouletteTotal:OnRefresh(...)
	Game.SkillSystem:UpdateSkillRouttleState()
	self:RefreshChildUI()
end

function HUDSkillRouletteTotal:RefreshChildUI()
	local RouttleState = Game.SkillSystem:GetSkillRouttleState()
	local ChildUIName = ""
	local isEditorPCStyle = C7FunctionLibrary.IsC7Editor() and UIFunctionLibrary.GetPreviewPlatform and
		UIFunctionLibrary.GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC
	local isPackagePCStyle = not C7FunctionLibrary.IsC7Editor() and not PlatformUtil.IsMobilePlatform()
	if RouttleState == const.SKILL_ROUTTLE_STATE.Battle then
		--普通模式
		if isEditorPCStyle or isPackagePCStyle then
			ChildUIName = UICellConfig.HUDSkillRoulette_PC
		else
			ChildUIName = UICellConfig.HUDSkillRoulette_Mobile
		end
	else
		--扮演模式 or 动作系统 or 无形之手
		if isEditorPCStyle or isPackagePCStyle then
			ChildUIName = UICellConfig.HUDSkillSimpleRoulette_PC
		else
			ChildUIName = UICellConfig.HUDSkillSimpleRoulette_Mobile
		end
	end
	if ChildUIName ~= self.ChildUIName then
		if self.ChildUIName ~= "" then
			self:CloseComponent(self.ChildUIName)
		end
		self.ChildUIName = ChildUIName
		self:OpenComponent(self.ChildUIName, self.view.CP_SkillRoulette)
	end
end

local RippleAnchorTemp = import("Anchors")()
RippleAnchorTemp.Minimum = FVector2D(0.5, 0.5)
RippleAnchorTemp.Maximum = FVector2D(0.5, 0.5)
local RippleAlignmentTemp = FVector2D(0.5, 0.5)

function HUDSkillRouletteTotal:RequestRippleWidget(inUIComponent, PanelWidget, bDisable)
	local AvailableRipple = self.AvailableRippleList[#self.AvailableRippleList]
	if AvailableRipple then
		PanelWidget:AddChild(AvailableRipple)
		AvailableRipple.Slot:SetAnchors(RippleAnchorTemp)
		AvailableRipple.Slot:SetAlignment(RippleAlignmentTemp)
		table.remove(self.AvailableRippleList, #self.AvailableRippleList)
		local TargetAnimInst = bDisable and AvailableRipple.An_Act_Pressed_disable or AvailableRipple.An_Act_Pressed
		inUIComponent:PlayAnimation(TargetAnimInst, nil, AvailableRipple, 0.0, 1, EUMGSequencePlayMode.Forward, 1, true,
			function()
				PanelWidget:RemoveChild(AvailableRipple)
				table.insert(self.AvailableRippleList, AvailableRipple)
			end, nil, true)
	end
end


function HUDSkillRouletteTotal:OnCardDebuffEnd(EnemyEntity, TweenDuration, TrailInterpolationMode)
	local PlayerController = import("GameplayStatics").GetPlayerController(_G.GetContextObject(), 0)
	local WorldPosition = EnemyEntity:GetPosition()
	local ScreenPosition = FVector2D()
	import("WidgetLayoutLibrary").ProjectWorldLocationToWidgetPosition(
		PlayerController, WorldPosition, ScreenPosition, true
	)
	local ViewportSize = import("WidgetLayoutLibrary").GetViewportSize(_G.GetContextObject())
	local ViewportScale = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
	if ScreenPosition.X < 0 then  ScreenPosition.X = 0 end
	if ScreenPosition.Y < 0 then  ScreenPosition.Y = 0 end
	if ScreenPosition.X > ViewportSize.X / ViewportScale then ScreenPosition.X = ViewportSize.X / ViewportScale end
	if ScreenPosition.Y > ViewportSize.Y / ViewportScale then ScreenPosition.Y = ViewportSize.Y / ViewportScale end
	if self.CardDebuffWidget then
		self.CardDebuffWidget:SetVisibility(ESlateVisibility.HitTestInvisible)
		self.CardDebuffWidget:PlayAnimationForward(self.CardDebuffWidget.Ani_Fadein, 0.65 / TweenDuration, false)
	end
	local TargetPosition
	if self.ChildUIName == UICellConfig.HUDSkillRoulette_PC then
		local TargetWiget = UI.GetUI(self.ChildUIName).view.WBP_HUDACT
		TargetPosition = import("SlateBlueprintLibrary").LocalToViewport(_G.GetContextObject(), TargetWiget:GetCachedGeometry(), FVector2D(0.0, 0.0), nil, nil)
		TargetPosition = TargetPosition + import("SlateBlueprintLibrary").GetAbsoluteSize(TargetWiget:GetCachedGeometry())-- + FVector2D(0, 200)
		TargetPosition = TargetPosition * ( 1/ ViewportScale) * 0.88
	elseif self.ChildUIName == UICellConfig.HUDSkillRoulette_Mobile then
		local TargetWiget = UI.GetUI(self.ChildUIName).view.WBP_HUDACT
		TargetPosition = import("SlateBlueprintLibrary").LocalToViewport(_G.GetContextObject(), TargetWiget:GetCachedGeometry(), FVector2D(0.0, 0.0), nil, nil)
		TargetPosition = TargetPosition --+ import("SlateBlueprintLibrary").GetAbsoluteSize(TargetWiget:GetCachedGeometry()) + FVector2D(200, 0)
		TargetPosition = TargetPosition * ( 1/ ViewportScale)
	end
	if self.tweenSystem then
		self.tweenSystem:SetData(ScreenPosition, TargetPosition)
		self.tweenSystem:SetLoopTime(1)
		self.tweenSystem:SetDurationTime(TweenDuration)
		self.tweenSystem:SetEaseType(TrailInterpolationMode)
		self.tweenSystem:Play()
	end
end

function HUDSkillRouletteTotal:OnResourceLoaded(Res)
	local Widget = WidgetBlueprintLibrary.Create(_G.GetContextObject(), Res)
	self.view.FoolCardEffect:AddChildToCanvas(Widget)
	local NewAnchors = import("Anchors")()
	NewAnchors.Minimum = FVector2D(0, 0)
	NewAnchors.Maximum = FVector2D(0, 0)
	Widget.Slot:SetAnchors(NewAnchors)
	Widget:SetVisibility(ESlateVisibility.Collapsed)
	self.CardDebuffWidget = Widget
	self.tweenSystem = Widget:AddComponent(UE.KGPositionTween)
end

function HUDSkillRouletteTotal:OnReceiveSkillActivateResult(InSkillSlot, InResult, DisableSkillDetailReason)
	if InResult == EReleaseSkillResult.DisabledType then
		local SkillID = SkillImpl.GetMainChaSkillIDBySlot(InSkillSlot)
		local skillTableData = Game.TableData.GetSkillDataNewRow(SkillID)
		local caster = Game.me
		-- 拿到type
		local skillType = skillTableData.Type

		if DisableSkillDetailReason == EDetailDisableSkillReason.NormalSkillForbidByHit then
			caster:GenReminder(Enum.EReminderTextData.HITCONFLICT_REMINDER, {caster:GetStaggerState(), FORBID_NONEDECONTROL_SKILL_MAP[skillType]})
			return
		elseif DisableSkillDetailReason == EDetailDisableSkillReason.NormalSkillForbidByBuffState then
			caster:GenReminder(Enum.EReminderTextData.BUFFCONTROLCONFLICT_REMINDER, {caster:GetForbidSkillBuffState(skillType), FORBID_NONEDECONTROL_SKILL_MAP[skillType]})
			return
		elseif DisableSkillDetailReason == EDetailDisableSkillReason.DeControlSkillForbid_HighLevel then
			caster:GenReminder(Enum.EReminderTextData.HITCONFLICT_REMINDER, {caster:GetStaggerState(), BeHitConst.DeControlSkillForbid_HighLevel})
			return
		elseif DisableSkillDetailReason == EDetailDisableSkillReason.DeControlSkillForbidByHit then
			caster:GenReminder(Enum.EReminderTextData.HITCONFLICT_REMINDER, {caster:GetStaggerState(), FORBID_NONEDECONTROL_SKILL_MAP[skillType]})
			return
		elseif DisableSkillDetailReason == EDetailDisableSkillReason.DeControlSkillForbidByBuffState then
			caster:GenReminder(Enum.EReminderTextData.BUFFCONTROLCONFLICT_REMINDER, {caster:GetForbidSkillBuffState(skillType), FORBID_NONEDECONTROL_SKILL_MAP[skillType]})
			return
		end
	end
	
	local reminderMsg = HUDSkillRouletteTotal.SkillResultToReminderMsg[InResult]
	if reminderMsg then
		Game.ReminderManager:AddReminderById(reminderMsg)
	end
end

return HUDSkillRouletteTotal
