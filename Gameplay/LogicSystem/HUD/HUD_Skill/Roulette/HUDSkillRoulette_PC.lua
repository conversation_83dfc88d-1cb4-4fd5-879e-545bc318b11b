local HUDSkillChangeTargetBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillChangeTargetBtn")
local HUDSkillQuickChangeBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillQuickChangeBtn")
local HUDSkillComboBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillComboBtn")
local HUD_Medcine = kg_require("Gameplay.LogicSystem.HUD.HUDMedicine.HUD_Medcine")
local HUDAttackBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDAttackBtn")
local HUDSkillBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillBtn")
local HUDSkillCancel_Item = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillCancel_Item")
local HUDSkillAdditionalBtn = kg_require( "Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillAdditionalBtn")
local HUDInvMultiRideBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.HUDInvMultiRideBtn")
local HUDRideBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.HUDRideBtn")
local HUDSkillRouletteBase = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.Roulette.HUDSkillRouletteBase")
local ESlateVisibility = import("ESlateVisibility")

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillRoulette_PC : UIComponent
---@field view HUDSkillRoulette_PC_PanelBlueprint
local HUDSkillRoulette_PC = DefineClass("HUDSkillRoulette_PC", UIComponent, HUDSkillRouletteBase)

HUDSkillRoulette_PC.eventBindMap = {
	[EEventTypesV2.HUD_POST_QTE_STATE_CHANGED] = "CheckSkillRouttleVisible",
	[_G.EEventTypes.ON_IS_DEAD_CHANGED] = { "CheckSkillRouttleVisible", GetMainPlayerEID },
	[EEventTypesV2.ON_UI_OPEN] = "OnUIOpen",
	[EEventTypesV2.SKILLHUD_PROFESSION_Q_LOCK_CHANGE] = "RefreshProfessionQLock",
}

local InputControlConst = kg_require("Shared.Const.InputControlConst")
local BlockActionSlot = InputControlConst.BLOCK_ACTION_SLOT_CONST

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillRoulette_PC:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillRoulette_PC:InitUIData()
	table.merge(HUDSkillRoulette_PC.eventBindMap, HUDSkillRouletteBase.eventBindMap)
end

--- UI组件初始化，此处为自动生成
function HUDSkillRoulette_PC:InitUIComponent()
    ---@type HUDRideBtn
    self.WBP_HUDRideCom = self:CreateComponent(self.view.WBP_HUDRide, HUDRideBtn)
    ---@type HUDInvMultiRideBtn
    self.WBP_HUDInvMultiRideCom = self:CreateComponent(self.view.WBP_HUDInvMultiRide, HUDInvMultiRideBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill11Com = self:CreateComponent(self.view.Btn_Skill11, HUDSkillBtn)
    ---@type HUDSkillChangeTargetBtn
    self.WBP_HUDChangeObjectCom = self:CreateComponent(self.view.WBP_HUDChangeObject, HUDSkillChangeTargetBtn)
    ---@type HUDSkillQuickChangeBtn
    self.WBP_HUDSkillChangeCom = self:CreateComponent(self.view.WBP_HUDSkillChange, HUDSkillQuickChangeBtn)
    ---@type HUDSkillComboBtn
    self.WBP_HUDSkillComboCom = self:CreateComponent(self.view.WBP_HUDSkillCombo, HUDSkillComboBtn)
    ---@type HUD_Medcine
    self.WBP_HUDMedcineCom = self:CreateComponent(self.view.WBP_HUDMedcine, HUD_Medcine)
    ---@type HUDAttackBtn
    self.WBP_HUDACTCom = self:CreateComponent(self.view.WBP_HUDACT, HUDAttackBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill12Com = self:CreateComponent(self.view.Btn_Skill12, HUDSkillBtn)
    ---@type HUDSkillAdditionalBtn
    self.Btn_Skill11Com = self:CreateComponent(self.view.Btn_Skill11, HUDSkillAdditionalBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill10Com = self:CreateComponent(self.view.Btn_Skill10, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill09Com = self:CreateComponent(self.view.Btn_Skill09, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill08Com = self:CreateComponent(self.view.Btn_Skill08, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill07Com = self:CreateComponent(self.view.Btn_Skill07, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill06Com = self:CreateComponent(self.view.Btn_Skill06, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill05Com = self:CreateComponent(self.view.Btn_Skill05, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill04Com = self:CreateComponent(self.view.Btn_Skill04, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill03Com = self:CreateComponent(self.view.Btn_Skill03, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill02Com = self:CreateComponent(self.view.Btn_Skill02, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill01Com = self:CreateComponent(self.view.Btn_Skill01, HUDSkillBtn)
end

---UI事件在这里注册，此处为自动生成
function HUDSkillRoulette_PC:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Cancle.OnClicked, "on_Btn_Cancle_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
-- luacheck: ignore
function HUDSkillRoulette_PC:InitUIView()
	self.view.WBP_HUDMedcine:SetVisibility(ESlateVisibility.Collapsed)
	-- 技能轮盘收缩
	self.SKillShowTimerHandle = nil
	self.CollapseTimeRemain = 0
	self.AimWidgetComponent = self:CreateComponent(self.userWidget.AimWidget, HUDSkillCancel_Item)
	self.SkillRouttleVisible = true
	self.RolePlayList = {}

	self.SkillCellBindData = {
		Slot01 = {
			SkillWidget = self.view.Btn_Skill01,
			CellInst = self.Btn_Skill01Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot01
			},
			KeyPromptAction = "Skill_01"
		},
		Slot02 = {
			SkillWidget = self.view.Btn_Skill02,
			CellInst = self.Btn_Skill02Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot02
			},
			KeyPromptAction = "Skill_02"
		},
		Slot03 = {
			SkillWidget = self.view.Btn_Skill03,
			CellInst = self.Btn_Skill03Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot03
			},
			KeyPromptAction = "Skill_03"
		},
		Slot04 = {
			SkillWidget = self.view.Btn_Skill04,
			CellInst = self.Btn_Skill04Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot04
			},
			KeyPromptAction = "Skill_04"
		},
		Slot05 = {
			SkillWidget = self.view.Btn_Skill05,
			CellInst = self.Btn_Skill05Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot05
			},
			KeyPromptAction = "Skill_05"
		},
		Slot06 = {
			SkillWidget = self.view.Btn_Skill06,
			CellInst = self.Btn_Skill06Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_DeControlSlot
			},
			KeyPromptAction = "DeControl"
		},
		Slot07 = {
			SkillWidget = self.view.Btn_Skill07,
			CellInst = self.Btn_Skill07Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot06
			},
			KeyPromptAction = "MechanicalSkill"
		},
		FellowSlot1 = {
			SkillWidget = self.view.Btn_Skill08,
			CellInst = self.Btn_Skill08Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_FellowSlot1
			},
			KeyPromptAction = "FellowSkill_01"
		},
		FellowSlot2 = {
			SkillWidget = self.view.Btn_Skill09,
			CellInst = self.Btn_Skill09Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_FellowSlot2
			},
			KeyPromptAction = "FellowSkill_02"
		},
		FellowSlot3 = {
			SkillWidget = self.view.Btn_Skill10,
			CellInst = self.Btn_Skill10Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_ExtraordinarySlot
			},
			KeyPromptAction = "ExtraordinarySkill"
		},

		SlotAttack = {
			SkillWidget = self.view.WBP_HUDACT,
			CellInst = self.WBP_HUDACTCom,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Attack
			},

			KeyPromptAction = "ACT_A"
		},
		AutoChangeLockObj = {
			SkillWidget = self.view.WBP_HUDChangeObject,
			CellInitParams = {}
		},
		SlotAddition = {
			SkillWidget = self.view.Btn_Skill11,
			CellInst = self.Btn_Skill11Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot11
			},
			KeyPromptAction = "SpecialSkill_01"
		},
		AutoSkill = {
			SkillWidget = self.view.WBP_HUDSkillCombo,
			CellInst = self.WBP_HUDSkillComboCom,
			CellInitParams = {
			},
		},
		QuickSkill = {
			SkillWidget = self.view.WBP_HUDSkillChange_PC,
			CellInst = self.WBP_HUDSkillChangeCom,
			CellInitParams = {
			},
		},
		Ride = {
			SkillWidget = self.view.WBP_HUDRide,
			CellInst = self.WBP_HUDRideCom,
			CellInitParams = {

			},
			KeyPromptAction = "ToggleRideHorse"
		},
		InvMultiRide = {
			SkillWidget = self.view.WBP_HUDInvMultiRide,
			CellInst = self.WBP_HUDInvMultiRideCom,
			CellInitParams = {
			},
			KeyPromptAction = "InviteMultiMount"
		}
	}
end

---面板打开的时候触发
function HUDSkillRoulette_PC:OnRefresh(...)
	self:initProfessionPropChanged()
	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst then
			if V.CellInst.RefreshSlot then
				V.CellInst:RefreshSlot(V.CellInitParams)
			end
			if V.CellInst.RefreshUI then
				V.CellInst:RefreshUI()
			end
			if V.KeyPromptAction then
				V.CellInst.WBP_KeyPromptCom:Refresh(V.KeyPromptAction)
			end
		end
	end

	--任务卜杖寻路按钮需要监听
	Game.QuestSystem:OnSkillRouttleBattlePanelOpen()

	self:CheckSkillRouttleVisible()
	self:RefreshJobMechanism()
	self:RefreshProfessionQLock(Game.me.ProfessionQLock or 0)
end

-------------------------Set SkillRouttle Visible Start-----------------------------
function HUDSkillRoulette_PC:SetSkillRouttleCollapsedTimer()
	self.CollapseTimeRemain = 15
	self:CheckSkillRouttleShow()
end

function HUDSkillRoulette_PC:GetSkillRouttleVisibleStateValue()
	return self.SkillRouttleVisible
end

function HUDSkillRoulette_PC:CheckSkillRouttleShow()

end

-------------------------Set SkillRouttle Visible End-----------------------------
function HUDSkillRoulette_PC:CheckSkillRouttleVisible()
	local bIsDead = Game.me and Game.me.IsDead or false
	local bIsQTE = Game.HUDSystem.IsInQte()
	if bIsDead or bIsQTE then
		self.view.CP_SkillRouttle:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.CP_SkillRouttle:SetVisibility(ESlateVisibility.selfHitTestInvisible)
	end
end

function HUDSkillRoulette_PC:AddOnTouchSlot(Slot)

end

function HUDSkillRoulette_PC:DelOnTouchSlot(Slot)

end

function HUDSkillRoulette_PC:RemoveOnTouchSlot()

end

function HUDSkillRoulette_PC:OnLevelLoaded()
	self:RemoveOnTouchSlot()
	self:CheckSkillRouttleVisible()
end

function HUDSkillRoulette_PC:OnUIOpen(UIName)
	local UIOwner = UI.GetUI(UIName)
	if not UIOwner or not UIOwner:IsShow() then
		return
	end
	local uiCfg = Game.NewUIManager:GetUIConfig(UIName)
	if not uiCfg then
		Log.DebugFormat("[P_SkillBtn_Battle]Missing UIData UIName:%s", UIName)
		return
	end

	local InputTypeBlockWhiteList = uiCfg.InputTypeBlockWhiteList or UIConst.UIInputBlockWhiteConfig[uiCfg.layout or Enum.EUILayout.Normal]
	if InputTypeBlockWhiteList then
		for _, Slot in ksbcpairs(InputTypeBlockWhiteList) do
			if Slot == BlockActionSlot.Skill then
				return
			end
		end
	end
	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst and V.CellInst.StopPress then
			V.CellInst:StopPress()
		end
	end
end

function HUDSkillRoulette_PC:OnClose()
	UIBase.OnClose(self)
	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst and V.CellInst.RestoreSkillState then
			V.CellInst:RestoreSkillState()
		end
	end
end

---@type Anchorss
local AimWidgetAnchor = import("Anchors")()
AimWidgetAnchor.Minimum.X = 0.5
AimWidgetAnchor.Minimum.Y = 0.5
AimWidgetAnchor.Maximum.X = 0.5
AimWidgetAnchor.Maximum.Y = 0.5

---@type FVector2D
local AimWidgetAlignment = FVector2D(0.5, 0.5)
---@param PanelWidget CanvasPanel
function HUDSkillRoulette_PC:RequestAimWidget(PanelWidget, uiComponent)
	if self.view.AimWidget.Slot ~= nil then
		return false
	end
	PanelWidget:AddChild(self.view.AimWidget)
	---@type CanvasPanelSlot
	local CanvsPanelSlot = self.view.AimWidget.Slot
	CanvsPanelSlot:SetAutoSize(true)
	CanvsPanelSlot:SetAnchors(AimWidgetAnchor)
	CanvsPanelSlot:SetAlignment(AimWidgetAlignment)
	self.AimWidgetComponent:Refresh(uiComponent)
	return true
end

---@param PanelWidget CanvasPanel
function HUDSkillRoulette_PC:ReturnAimWidget(PanelWidget)
	if self.view.AimWidget.Slot == nil then
		return
	end
	self.AimWidgetComponent:OnReturnToPool()
	PanelWidget:RemoveChild(self.view.AimWidget)
end

function HUDSkillRoulette_PC:OnAimPosMoved(MoveOffset)
	self.AimWidgetComponent:OnAimPosMoved(MoveOffset)
end

function HUDSkillRoulette_PC:IsAimCancelSkillRelease()
	return self.AimWidgetComponent.State == 1
end

function HUDSkillRoulette_PC:IsAiming()
	return Game.PlayerController.BattleSystemInputProcessor:IsAiming()
end

function HUDSkillRoulette_PC:RefreshProfessionQLock(NewValue, OldValue)
	if NewValue and NewValue > 0 then
		self.view.CP_JobMechanism:SetVisibility(ESlateVisibility.Hidden)
	else
		self.view.CP_JobMechanism:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end

--- 此处为自动生成
function HUDSkillRoulette_PC:on_Btn_Cancle_Clicked()
end

return HUDSkillRoulette_PC
