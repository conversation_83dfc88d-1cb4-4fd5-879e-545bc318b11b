local HUDSkillChangeTargetBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillChangeTargetBtn")
local HUDSkillFlameJumpBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillFlameJumpBtn")
local HUDSkillQuickChangeBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillQuickChangeBtn")
local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local HUDSkillComboBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillComboBtn")
local HUDSkillJumpDodgeBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillJumpDodgeBtn")
local HUDSkillBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillBtn")
local HUDSkillAdditionalBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillAdditionalBtn")
local HUDAttackBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDAttackBtn")
local HUDSkillCancel_Item = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.BtnWidget.HUDSkillCancel_Item")
local HUDInvMultiRideBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.HUDInvMultiRideBtn")
local HUDRideBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.HUDRideBtn")
local HUDSkillRouletteBase = kg_require("Gameplay.LogicSystem.HUD.HUD_Skill.Roulette.HUDSkillRouletteBase")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

local InputControlConst = kg_require("Shared.Const.InputControlConst")
local BlockActionSlot = InputControlConst.BLOCK_ACTION_SLOT_CONST

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDSkillRoulette_Mobile : UIComponent
---@field view HUDSkillRoulette_Mobile_PanelBlueprint
local HUDSkillRoulette_Mobile = DefineClass("HUDSkillRoulette_Mobile", UIComponent, HUDSkillRouletteBase)

HUDSkillRoulette_Mobile.eventBindMap = {
	[EEventTypesV2.LEVEL_ON_LEVEL_LOADED] = "OnLevelLoaded",
	[EEventTypesV2.HUD_POST_QTE_STATE_CHANGED] = "CheckSkillRouttleVisible",
	[_G.EEventTypes.ON_IS_DEAD_CHANGED] = { "CheckSkillRouttleVisible", GetMainPlayerEID },
	[_G.EEventTypes.ON_INBATTLE_CHANGED] = { "CheckSkillRouttleShow", GetMainPlayerEID },
	[EEventTypesV2.ON_UI_OPEN] = "OnUIOpen",
	[EEventTypesV2.SERVER_ON_SKILL_WHEEL_CHANGED] = "OnSkillWheelChanged",
	[EEventTypesV2.SKILLHUD_PROFESSION_Q_LOCK_CHANGE] = "RefreshProfessionQLock",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDSkillRoulette_Mobile:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDSkillRoulette_Mobile:InitUIData()
	table.merge(HUDSkillRoulette_Mobile.eventBindMap, HUDSkillRouletteBase.eventBindMap)
	-- 技能轮盘收缩
	self.SKillShowTimerHandle = nil
	self.CollapseTimeRemain = -1

	self.SkillRouttleVisible = nil

	self.OnTouchSlot = {}
	self.RolePlayList = {}
	self:StartTimer("AutoCollapse", function()
		-- 如果处于技能轮盘，并且配置为不可还原为休闲轮盘
		if Game.me and Game.me.skillWheelID ~= 0 then
			local skillWheelData = Game.TableData.GetSkillWheelDataRow(Game.me.skillWheelID)
			if skillWheelData.beRecover == false then
				return
			end
		end
		if self.CollapseTimeRemain > 0 then
			self.CollapseTimeRemain = self.CollapseTimeRemain - 0.3
			if self.CollapseTimeRemain <= 0 then
				self.CollapseTimeRemain = -1
				self:CheckSkillRouttleShow()
			end
		end
	end, 300, -1, true, true)
end

--- UI组件初始化，此处为自动生成
function HUDSkillRoulette_Mobile:InitUIComponent()
	---@type HUDRideBtn
	self.WBP_HUDRideCom = self:CreateComponent(self.view.WBP_HUDRide, HUDRideBtn)
	---@type HUDInvMultiRideBtn
	self.WBP_HUDInvMultiRideCom = self:CreateComponent(self.view.WBP_HUDInvMultiRide, HUDInvMultiRideBtn)
    ---@type HUDSkillChangeTargetBtn
    self.WBP_HUDChangeObjectCom = self:CreateComponent(self.view.WBP_HUDChangeObject, HUDSkillChangeTargetBtn)
    ---@type HUDSkillFlameJumpBtn
    self.WBP_HUDFlameJumpCom = self:CreateComponent(self.view.WBP_HUDFlameJump, HUDSkillFlameJumpBtn)
    ---@type HUDSkillQuickChangeBtn
    self.WBP_HUDChangePlanCom = self:CreateComponent(self.view.WBP_HUDChangePlan, HUDSkillQuickChangeBtn)
    ---@type UISimpleList
    self.Panel_HideCom = self:CreateComponent(self.view.Panel_Hide, UISimpleList)
    ---@type HUDSkillComboBtn
    self.WBP_HUDSkillComboCom = self:CreateComponent(self.view.WBP_HUDSkillCombo, HUDSkillComboBtn)
    ---@type HUDSkillBtn
    self.Btn_SkillBigCom = self:CreateComponent(self.view.Btn_SkillBig, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_SkillFeiFanCom = self:CreateComponent(self.view.Btn_SkillFeiFan, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill08Com = self:CreateComponent(self.view.Btn_Skill08, HUDSkillBtn)
    ---@type HUDSkillAdditionalBtn
    self.Btn_Skill11Com = self:CreateComponent(self.view.Btn_Skill11, HUDSkillAdditionalBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill07Com = self:CreateComponent(self.view.Btn_Skill07, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill06Com = self:CreateComponent(self.view.Btn_Skill06, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill01Com = self:CreateComponent(self.view.Btn_Skill01, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill02Com = self:CreateComponent(self.view.Btn_Skill02, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill03Com = self:CreateComponent(self.view.Btn_Skill03, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill04Com = self:CreateComponent(self.view.Btn_Skill04, HUDSkillBtn)
    ---@type HUDSkillBtn
    self.Btn_Skill05Com = self:CreateComponent(self.view.Btn_Skill05, HUDSkillBtn)
    ---@type HUDAttackBtn
    self.WBP_HUDACTCom = self:CreateComponent(self.view.WBP_HUDACT, HUDAttackBtn)
    ---@type HUDSkillJumpDodgeBtn
    self.WBP_JumpCom = self:CreateComponent(self.view.WBP_Jump, HUDSkillJumpDodgeBtn)
    ---@type HUDSkillJumpDodgeBtn
    self.WBP_MissCom = self:CreateComponent(self.view.WBP_Miss, HUDSkillJumpDodgeBtn)
end

---UI事件在这里注册，此处为自动生成
function HUDSkillRoulette_Mobile:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
-- luacheck: ignore
function HUDSkillRoulette_Mobile:InitUIView()
	self.view.WBP_HUDNull_First:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.WBP_HUDNull_Second:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

	self.AimWidgetComponent = self:CreateComponent(self.userWidget.AimWidget, HUDSkillCancel_Item)
	-- Skill Btn Data
	self.SkillCellBindData = {
		Slot01 = {
			SkillWidget = self.view.Btn_Skill01,
			CellInst = self.Btn_Skill01Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot01
			},
		},
		Slot02 = {
			SkillWidget = self.view.Btn_Skill02,
			CellInst = self.Btn_Skill02Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot02
			},
		},
		Slot03 = {
			SkillWidget = self.view.Btn_Skill03,
			CellInst = self.Btn_Skill03Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot03
			},
		},
		Slot04 = {
			SkillWidget = self.view.Btn_Skill04,
			CellInst = self.Btn_Skill04Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot04
			},
		},
		Slot05 = {
			SkillWidget = self.view.Btn_Skill05,
			CellInst = self.Btn_Skill05Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot05
			},
		},
		SlotDeControl = {
			SkillWidget = self.view.Btn_Skill06,
			CellInst = self.Btn_Skill06Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_DeControlSlot
			},
		},
		Slot06 = {
			SkillWidget = self.view.Btn_Skill07,
			CellInst = self.Btn_Skill07Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot06
			},
		},
		SlotFellow1 = {
			SkillWidget = self.view.Btn_Skill08,
			CellInst = self.Btn_Skill08Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_FellowSlot1
			},
		},
		SlotFellow2 = {
			SkillWidget = self.view.Btn_SkillFeiFan,
			CellInst = self.Btn_SkillFeiFanCom,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_FellowSlot2
			},
		},
		Slot_Extraordinary = {
			SkillWidget = self.view.Btn_SkillBig,
			CellInst = self.Btn_SkillBigCom,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_ExtraordinarySlot
			},
		},
		SlotAttack = {
			SkillWidget = self.view.WBP_HUDACT,
			CellInst = self.WBP_HUDACTCom,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Attack
			},
		},

		SlotJump = {
			SkillWidget = self.view.WBP_Jump,
			CellInst = self.WBP_JumpCom,
			CellInitParams = {
				JumpMissType = Game.HUDSystem.JumpMissType.Jump
			},
		},

		SlotMiss = {
			SkillWidget = self.view.WBP_Miss,
			CellInst = self.WBP_MissCom,
			CellInitParams = {
				JumpMissType = Game.HUDSystem.JumpMissType.Dodge
			},
		},
		AutoChangeLockObj = {
			SkillWidget = self.view.WBP_HUDChangeObject,
			CellInst = self.WBP_HUDChangeObjectCom,
			CellInitParams = {}
		},
		FlameJump = {
			SkillWidget = self.view.WBP_HUDFlameJump,
			CellInst = self.WBP_HUDFlameJumpCom,
			CellInitParams = {}
		},
		SlotAddition = {
			SkillWidget = self.view.Btn_Skill11,
			CellInst = self.Btn_Skill11Com,
			CellInitParams = {
				SkillSlot = ETE.EBSSkillSlot.SS_Slot11
			},
		},
		AutoSkill = {
			SkillWidget = self.view.WBP_HUDSkillCombo,
			CellInst = self.WBP_HUDSkillComboCom,
			CellInitParams = {}
		},
		QuickSkill = {
			SkillWidget = self.view.WBP_HUDChangePlan,
			CellInst = self.WBP_HUDChangePlanCom,
			CellInitParams = {
			},
		},
		Ride = {
			SkillWidget = self.view.WBP_HUDRide,
			CellInst = self.WBP_HUDRideCom,
			CellInitParams = {

			},
		},
		InvMultiRide = {
			SkillWidget = self.view.WBP_HUDInvMultiRide,
			CellInst = self.WBP_HUDInvMultiRideCom,
			CellInitParams = {
			},
		}
	}

	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst then
			V.CellInst:Refresh(V.CellInitParams, K)
		end
	end
end

function HUDSkillRoulette_Mobile:OnOpen()
	--任务卜杖寻路按钮需要监听
	Game.QuestSystem:OnSkillRouttleBattlePanelOpen()
end

---面板打开的时候触发
function HUDSkillRoulette_Mobile:OnRefresh(...)
	self:initProfessionPropChanged()
	self.SkillRouttleVisible = nil

	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst then
			if V.CellInst.RefreshSlot then
				V.CellInst:RefreshSlot(V.CellInitParams)
			end
			if V.CellInst.RefreshUI then
				V.CellInst:RefreshUI()
			end
		end
	end

	self:CheckSkillRouttleShow(true)
	self:CheckSkillRouttleVisible()
	self:RefreshJobMechanism()
	self:RefreshProfessionQLock( Game.me.ProfessionQLock or 0)
end

function HUDSkillRoulette_Mobile:RefreshProfessionQLock(NewValue, OldValue)
	if NewValue and NewValue > 0 then
		self.view.CP_JobMechanism:SetVisibility(ESlateVisibility.Hidden)
	else
		self.view.CP_JobMechanism:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
end

-------------------------Set SkillRouttle Visible Start-----------------------------
function HUDSkillRoulette_Mobile:GetSkillRouttleVisibleStateValue()
	return self.SkillRouttleVisible
end

function HUDSkillRoulette_Mobile:SetSkillRouttleVisible(bShow, bForceCheck)
	if bShow == self:GetSkillRouttleVisibleStateValue() and not bForceCheck then
		return
	end
	self.SkillRouttleVisible = bShow
	local WidgetRoot = self.userWidget
	if bShow then
		-- 展开技能轮盘
		self.view.Panel_Hide:SetVisibility(ESlateVisibility.HitTestInvisible)
		if WidgetRoot:IsAnimationPlaying(WidgetRoot.An_SkillCollapse) then
			self:StopAnimation(WidgetRoot.An_SkillCollapse, WidgetRoot)
		end
		self:PlayAnimation(WidgetRoot.An_SkillCollapse, function()
				self.view.Panel_Hide:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			end, WidgetRoot, 0.0, 1, EUMGSequencePlayMode.Reverse, 1,
			false
			)
	else
		-- 隐藏技能轮盘
		self.view.Panel_Hide:SetVisibility(ESlateVisibility.HitTestInvisible)
		if WidgetRoot:IsAnimationPlaying(WidgetRoot.An_SkillCollapse) then
			self:StopAnimation(WidgetRoot.An_SkillCollapse, WidgetRoot)
		end
		if bForceCheck then
			self:PlayAnimation(WidgetRoot.An_SkillCollapse, nil , WidgetRoot,
				WidgetRoot.An_SkillCollapse:GetEndTime(), 1, EUMGSequencePlayMode.Forward, 1,
				false)
		else
			self:PlayAnimation(WidgetRoot.An_SkillCollapse, nil, WidgetRoot, 0.0, 1, EUMGSequencePlayMode.Forward, 1,
				false)
		end
		for _, V in pairs(self.SkillCellBindData) do
			if V.CellInst and V.CellInst.BreakAimSKill then
				V.CellInst:BreakAimSKill()
			end
		end
	end
	
	self:SetWidgetVisible(self.view.Canvas_LifeSkills, not bShow)
end

function HUDSkillRoulette_Mobile:SetSkillRouttleCollapsedTimer(InTimer)
	if not InTimer then
		InTimer = 15
	end
	self.CollapseTimeRemain = InTimer
	self:CheckSkillRouttleShow()
end

-------------------------Set SkillRouttle Visible End-----------------------------
function HUDSkillRoulette_Mobile:CheckSkillRouttleShow(bForceCheck)
	local bInBattle = false
	if Game.me and Game.me.InBattle then
		bInBattle = Game.me.InBattle
	end
	local CollapseTimerNotFinished = self.CollapseTimeRemain > 0

	local bSettingHideSkillBoard = Game.SettingsManager:GetIniData(Enum.ESettingDataEnum["HideSkillBoard"]) == 1
	local bIsIndungeon = Game.DungeonSystem.IsInDungeon()

	local bShouldHideInLocation = bSettingHideSkillBoard and (not bIsIndungeon)

	local bShouldShowBySkillWheel = false
	local skillWheelID = Game.me.skillWheelID
	if skillWheelID ~= 0 then
		local skillWheelData = Game.TableData.GetSkillWheelDataRow(skillWheelID)
		if skillWheelData.beRecover == false then
			bShouldShowBySkillWheel = true
		end
	end

	local bShouldShow = bInBattle or not bShouldHideInLocation or CollapseTimerNotFinished or #self.OnTouchSlot > 0 or bShouldShowBySkillWheel

	self:SetSkillRouttleVisible(bShouldShow, bForceCheck)
end

function HUDSkillRoulette_Mobile:CheckSkillRouttleVisible()
	local bIsDead = false
	if Game.me and Game.me.IsDead then
		bIsDead = true
	end
	local bIsQTE = Game.HUDSystem.IsInQte()
	if bIsDead or bIsQTE then
		self.view.CP_SkillRouttle:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.CP_SkillRouttle:SetVisibility(ESlateVisibility.selfHitTestInvisible)
	end
end

function HUDSkillRoulette_Mobile:AddOnTouchSlot(Slot)
	if not table.contains(self.OnTouchSlot, Slot) then
		table.insert(self.OnTouchSlot, Slot)
	end
end

function HUDSkillRoulette_Mobile:DelOnTouchSlot(Slot)
	if table.contains(self.OnTouchSlot, Slot) then
		table.removev(self.OnTouchSlot, Slot)
	end
end

function HUDSkillRoulette_Mobile:RemoveOnTouchSlot()
	self.OnTouchSlot = {}
end

function HUDSkillRoulette_Mobile:OnLevelLoaded()
	self:RemoveOnTouchSlot()
	self:CheckSkillRouttleShow(true)
	self:CheckSkillRouttleVisible()
end

function HUDSkillRoulette_Mobile:HideSkillBoardSettingChange()
	self:CheckSkillRouttleShow(true)
end

function HUDSkillRoulette_Mobile:OnUIOpen(UIName)
	local UIOwner = UI.GetUI(UIName)
	if not UIOwner or not UIOwner:IsShow() then
		return
	end
	local uiCfg = Game.NewUIManager:GetUIConfig(UIName)
	if not uiCfg then
		Log.DebugFormat("[P_SkillBtn_Battle]Missing UIData UIName:%s", UIName)
		return
	end

	local InputTypeBlockWhiteList = uiCfg.InputTypeBlockWhiteList or UIConst.UIInputBlockWhiteConfig[uiCfg.layout or Enum.EUILayout.Normal]
	if InputTypeBlockWhiteList then
		for _, Slot in ksbcpairs(InputTypeBlockWhiteList) do
			if Slot == BlockActionSlot.Skill then
				return
			end
		end
	end
	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst and V.CellInst.StopPress then
			V.CellInst:StopPress()
		end
	end
end

function HUDSkillRoulette_Mobile:OnClose()
	UIBase.OnClose(self)
	for K, V in pairs(self.SkillCellBindData) do
		if V.CellInst and V.CellInst.RestoreSkillState then
			V.CellInst:RestoreSkillState()
		end
	end
end

---@type Anchorss
local AimWidgetAnchor = import("Anchors")()
AimWidgetAnchor.Minimum.X = 0.5
AimWidgetAnchor.Minimum.Y = 0.5
AimWidgetAnchor.Maximum.X = 0.5
AimWidgetAnchor.Maximum.Y = 0.5

---@type FVector2D
local AimWidgetAlignment = FVector2D(0.5, 0.5)
---@param PanelWidget CanvasPanel
function HUDSkillRoulette_Mobile:RequestAimWidget(PanelWidget, uiComponent)
	if self.view.AimWidget.Slot ~= nil then
		return false
	end
	PanelWidget:AddChild(self.view.AimWidget)
	---@type CanvasPanelSlot
	local CanvsPanelSlot = self.view.AimWidget.Slot
	CanvsPanelSlot:SetAutoSize(true)
	CanvsPanelSlot:SetAnchors(AimWidgetAnchor)
	CanvsPanelSlot:SetAlignment(AimWidgetAlignment)
	self.AimWidgetComponent:Refresh(uiComponent)
	return true
end

---@param PanelWidget CanvasPanel

function HUDSkillRoulette_Mobile:ReturnAimWidget(PanelWidget)
	if self.view.AimWidget.Slot == nil then
		return
	end
	self.AimWidgetComponent:OnReturnToPool()
	PanelWidget:RemoveChild(self.view.AimWidget)
end

function HUDSkillRoulette_Mobile:OnAimPosMoved(MoveOffset)
	self.AimWidgetComponent:OnAimPosMoved(MoveOffset)
end

function HUDSkillRoulette_Mobile:IsAimCancelSkillRelease()
	return self.AimWidgetComponent.State == 1
end

function HUDSkillRoulette_Mobile:IsAiming()
	return Game.PlayerController.BattleSystemInputProcessor:IsAiming()
end

function HUDSkillRoulette_Mobile:OnSkillWheelChanged(skillWheelID)
	-- 如果处于技能轮盘，并且配置为不可还原为休闲轮盘
	if skillWheelID == 0 then
		self:SetSkillRouttleCollapsedTimer()
		return
	end

	local skillWheelData = Game.TableData.GetSkillWheelDataRow(skillWheelID)
	if skillWheelData.beRecover == false then
		self.CollapseTimeRemain = -1
		self:SetSkillRouttleVisible(true)
	end
end

return HUDSkillRoulette_Mobile
