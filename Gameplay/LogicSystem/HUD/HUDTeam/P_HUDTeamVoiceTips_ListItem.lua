---@class P_HUDTeamVoiceTips_ListItem : UIComponent
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local P_HUDTeamVoiceTips_ListItem = DefineClass("P_HUDTeamVoiceTips_ListItem", UIListItem)

function P_HUDTeamVoiceTips_ListItem:OnCreate()
    --- @type table
    self.Data = nil

    self:AddUIEvent(self.view.Btn_Info.OnClicked, "OnClick_Btn_Info")
end
------------------------------------------------- View Func Start --------------------------------------------------
function P_HUDTeamVoiceTips_ListItem:OnClick_Btn_Info()
    local nIndex = self.Data.nIndex
    local CallBackFunc = self.Data.CallBackFunc
    local ParentWnd = self.Data.ParentWnd

    if nIndex == nil or CallBackFunc == nil or ParentWnd == nil then
        return
    end
    CallBackFunc(ParentWnd, nIndex)
end

function P_HUDTeamVoiceTips_ListItem:OnRefresh(data)
    if data == nil then
        return
    end

    self.Data = data
    local bIsSelected = (data.nVoiceState ~= nil) and data.nVoiceState == data.nCurVoiceState
    local bHasIcon = true
    local strBtnName = data.strBtnName
    local bIsEnd = false

    self.userWidget:Event_UI_Style(bIsSelected, bHasIcon, strBtnName, bIsEnd)
    self:SetImage(self.view.Img_Icon, data.Icon)
end
------------------------------------------------- View Func End ----------------------------------------------------

return P_HUDTeamVoiceTips_ListItem