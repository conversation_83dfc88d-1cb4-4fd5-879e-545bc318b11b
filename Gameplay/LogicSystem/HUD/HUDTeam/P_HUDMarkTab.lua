local StringConst = require "Data.Config.StringConst.StringConst"
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

---@class P_HUDMarkTab : UIPanel
---@field public View WBP_HUDGroupMarkPnlView
local P_HUDMarkTab = DefineClass("P_HUDMarkTab", UIPanel)

P_HUDMarkTab.eventBindMap = {
    [EEventTypesV2.ON_SELF_CAPTAIN_CHANGED] = "onSelfCaptainChange",
    [EEventTypesV2.GROUP_LEADER_CHANGED] = "onGroupLeaderChange",
}

function P_HUDMarkTab:OnCreate()
    self:AddUIEvent(self.view.WBP_Mark.Btn1.OnClicked, "OnClick_WBP_SeceneMark_Btn")
    self:AddUIEvent(self.view.WBP_Mark.Btn2.OnClicked, "OnClick_WBP_TeamMemberMark_Btn")
end

function P_HUDMarkTab:OnOpen()
    self.view.WBP_Mark.Text_Name1:SetText(StringConst.Get("TEAM_MARK_SCENE"))
    self.view.WBP_Mark.Text_Name2:SetText(StringConst.Get("TEAM_MARK_MEMBER"))
    local iconData1 = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.SCENE_MARK)
    self:SetImage(self.view.WBP_Mark.Img_Mark01, iconData1.AssetPath)
    local iconData2 = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAMMATE_MARK)
    self:SetImage(self.view.WBP_Mark.Img_Mark02, iconData2.AssetPath)
end

function P_HUDMarkTab:OnRefresh(Pos)
    local Size = self.view.Overlay_SetPos.Slot:GetSize()
    Pos.Y = Pos.Y - Size.Y / 2
    self.view.Overlay_SetPos.Slot:SetPosition(Pos)
end

function P_HUDMarkTab:OnClose()
    UIBase.OnClose(self)
end

function P_HUDMarkTab:OnIdle(DeltaTime)

end

function P_HUDMarkTab:onSelfCaptainChange(eid, name, newValue, oldValue)
    if newValue == 0 then
        self:CloseSelf()
    end
end

function P_HUDMarkTab:onGroupLeaderChange()
    if Game.GroupSystem:IsGroupLeader() == false then
        self:CloseSelf()
    end
end

function P_HUDMarkTab:OnClick_WBP_SeceneMark_Btn()
    Game.TeamSystem:SetMarkState(Enum.EMARK.Scene)
    self:CloseSelf()
end

function P_HUDMarkTab:OnClick_WBP_TeamMemberMark_Btn()
    Game.TeamSystem:SetMarkState(Enum.EMARK.TeamGroup)
    self:CloseSelf()
end

return P_HUDMarkTab
