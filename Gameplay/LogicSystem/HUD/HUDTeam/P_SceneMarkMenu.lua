---@class P_SceneMarkMenu : UIController
---@field public View WBP_HUDGroupMarkView
local P_SceneMarkMenu = DefineClass("P_SceneMarkMenu", UIController)
local P_SceneMarkMenuItem = kg_require "Gameplay.LogicSystem.HUD.HUDTeam.P_SceneMarkMenuItem"

P_SceneMarkMenu.eventBindMap = {
    [_G.EEventTypes.CLIENT_SCENE_MARK_CAHNGED] = "OnSceneMarkPartChanged",
    [_G.EEventTypes.ON_TEAMID_CHANGED] = { "onTeamIDChange", GetMainPlayerEID },
    [_G.EEventTypes.CLIENT_GROUP_CREATE] = "onGroupIDChange",
    [_G.EEventTypes.CLIENT_GROUP_DISBAND] = "onGroupIDChange",
	[EEventTypesV2.HUD_SET_SIDE_BAR_TAB] = "onSideBarTabChanged",
}

function P_SceneMarkMenu:OnCreate()
    self.SceneMenuItemWidget = {
        [1] = self.View.WBP_HUDGroupPlacemarks_0,
        [2] = self.View.WBP_HUDGroupPlacemarks_1,
        [3] = self.View.WBP_HUDGroupPlacemarks_2,
        [4] = self.View.WBP_HUDGroupPlacemarks_3,
        [5] = self.View.WBP_HUDGroupPlacemarks_4,
        [6] = self.View.WBP_HUDGroupPlacemarks_5,
    }
    self.SceneMenuCompList = {}
    for key, value in pairs(self.SceneMenuItemWidget) do
        local Comp = self:BindComponent(value, P_SceneMarkMenuItem, key)
        table.insert(self.SceneMenuCompList, Comp)
    end
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Close, "OnClick_Btn_Close")
end

function P_SceneMarkMenu:OnRefresh()
    self.bHandleClose = false
    self:RefreshUI()
end

function P_SceneMarkMenu:OnClose()
    UIBase.OnClose(self)
    if self.bHandleClose then
        Game.TeamSystem:SetMarkState(Enum.EMARK.None)
    end
end

function P_SceneMarkMenu:OnIdle(DeltaTime)

end

function P_SceneMarkMenu:RefreshUI()
    self.bHandleClose = false
    local MarkList = {}
    if Game.TeamSystem:IsInTeam() then
        MarkList = Game.TeamSystem:GetSceneMarkList()
    elseif Game.TeamSystem:IsInGroup() then
        MarkList = Game.GroupSystem:GetSceneMarkList()
    end

    for key, value in ipairs(self.SceneMenuCompList) do
        local Marked = false
        for kk, vv in ipairs(MarkList) do
            if tonumber(vv.Index) == key then
                Marked = true
                break
            end
        end
        value:Refresh(key, Marked)
    end
end

function P_SceneMarkMenu:OnSceneMarkPartChanged()
    self:RefreshUI()
end

function P_SceneMarkMenu:onTeamIDChange()
    self:CloseSelf()
end

function P_SceneMarkMenu:onGroupIDChange()
    self:CloseSelf()
end

function P_SceneMarkMenu:OnClick_Btn_Close()
    self.bHandleClose = true
    self:CloseSelf()
end

function P_SceneMarkMenu:onSideBarTabChanged(tabType)
    if tabType == Enum.SIDE_BAR.EQuest then
        self:OnClick_Btn_Close()
    end
end

return P_SceneMarkMenu
