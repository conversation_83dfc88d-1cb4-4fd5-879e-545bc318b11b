---@class P_HUDTeamMenu : P_HUDTeamMenuBase
local P_HUDTeamMenu = DefineClass("P_HUDTeamMenu", UIController)
local PlayerMenuUtils = kg_require "Gameplay.LogicSystem.Team.PlayerMenuUtils"
local team_utils = kg_require("Shared.Utils.TeamUtils")

P_HUDTeamMenu.eventBindMap = {
    [_G.EEventTypes.ON_TEAMID_CHANGED] = { "TeamIDChange", GetMainPlayerEID },
    [_G.EEventTypes.CLIENT_GROUP_CREATE] = "GroupIDChange",
    [_G.EEventTypes.CLIENT_GROUP_DISBAND] = "GroupIDChange",
    [EEventTypesV2.SERVER_MSG_TEAM_MEMBER_CHANGED] = "OnMsgTeamMemberChanged",
    [_G.EEventTypes.GROUP_DEL_MEMBER] = "OnMsgGroupMemberQuit",

    [_G.EEventTypes.ON_ROLE_ATTR_BRIEF] = "OnRoleAttrBrief",
}

function P_HUDTeamMenu:OnCreate()
    self.ButtonList = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.ButtonList)
    self.ButtonList:AddUIListener(EUIEventTypes.CLICK, "Btn_ClickArea", "OnClick_WBP_ComList_Button")
    self.TEMP_ANCHORS = import("Anchors")()
    self.TEMP_ANCHORS.Minimum = FVector2D(0.0, 0.0)
    self.TEMP_ANCHORS.Maximum = FVector2D(0.0, 0.0)
    local ViewportSize = import("WidgetLayoutLibrary").GetViewportSize(_G.GetContextObject())
    local ViewportScale = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
    ---@type number 屏幕宽度
    self.ViewportWidth = math.ceil(ViewportSize.X / ViewportScale)
    ---@type number 屏幕高度
    self.ViewportHeight = math.ceil(ViewportSize.Y / ViewportScale)
end

function P_HUDTeamMenu:OnRefresh(Params)
    self.View.WidgetRoot:SetRenderOpacity(0)
    Params.IsCaptain = Game.TeamSystem:IsCaptain(Params.EntityID)
    Params.TeamID = Game.me.teamID
    Params.GroupID = Game.me.groupID
    self.Type = Params.Type
    self.EntityID = Params.EntityID
    self.Name = Params.Name
    self.Level = Params.Level
    self.ProfessionID = Params.ProfessionID
	self.Sex = Params.Sex
    self.Pos = Params.Pos
    self.BtnSize = Params.BtnSize
	local menuType = Enum.EMenuType.HUDTeam
	local bLeader = Game.GroupSystem:IsGroupLeader() or Game.TeamSystem:IsCaptain()
	if Game.DungeonSystem:IsInSingleDungeonWithBot() and bLeader then
		menuType = Enum.EMenuType.SingleDungeon
	end
    self.PlayerMenuUtils = PlayerMenuUtils.new(Params, menuType)
    self.ButtonMap = self.PlayerMenuUtils:GetMenuList()
    self.ButtonList:SetData(#self.ButtonMap)
    self:StartTimer(
        "Adaptor_Pos",
        function()
            self:AdpatorPos()
            self.View.WidgetRoot:SetRenderOpacity(1)
        end, 50, 1)
end

function P_HUDTeamMenu:AdpatorPos()
    self.Size = import("SlateBlueprintLibrary").GetLocalSize(self.View.SB_Menu:GetCachedGeometry()) 
    if self.Pos.X <= self.ViewportWidth / 2 then
        --屏幕左边
        self.Pos.X = self.Pos.X + self.BtnSize.X
        if self.Pos.Y <  110 then
            self.Pos.Y = 110
        end
        if self.Size.Y + self.Pos.Y > self.ViewportHeight then
            self.TEMP_ANCHORS.Minimum = FVector2D(0.0, 1.0)
            self.TEMP_ANCHORS.Maximum = FVector2D(0.0, 1.0)
            self.View.SB_Menu.slot:SetAlignment(FVector2D(0.0, 1.0))
            self.Pos.Y = - (self.ViewportHeight - self.Pos.Y - self.BtnSize.Y)
        else
            self.TEMP_ANCHORS.Minimum = FVector2D(0.0, 0.0)
            self.TEMP_ANCHORS.Maximum = FVector2D(0.0, 0.0)
            self.View.SB_Menu.slot:SetAlignment(FVector2D(0.0, 0.0))
        end
    else
        --屏幕右边
        if self.Pos.Y <  110 then
            self.Pos.Y = 110
        end
        if self.Size.Y + self.Pos.Y > self.ViewportHeight then
            self.TEMP_ANCHORS.Minimum = FVector2D(1.0, 1.0)
            self.TEMP_ANCHORS.Maximum = FVector2D(1.0, 1.0)
            self.View.SB_Menu.slot:SetAlignment(FVector2D(1.0, 1.0))
            self.Pos.X = - (self.ViewportWidth - self.Pos.X)
            self.Pos.Y = - (self.ViewportHeight - self.Pos.Y - self.BtnSize.Y)
        else
            self.TEMP_ANCHORS.Minimum = FVector2D(1.0, 0.0)
            self.TEMP_ANCHORS.Maximum = FVector2D(1.0, 0)
            self.View.SB_Menu.slot:SetAlignment(FVector2D(1.0, 0.0))
            self.Pos.X = - (self.ViewportWidth - self.Pos.X)
        end
    end
    self.View.SB_Menu.slot:SetAnchors(self.TEMP_ANCHORS)
    self.View.SB_Menu.slot:SetPosition(self.Pos)

end

function P_HUDTeamMenu:OnClick_WBP_ComList_Button(index)
    if self.ButtonMap[index] then
        self.PlayerMenuUtils[self.ButtonMap[index][1]](self.PlayerMenuUtils, self.ButtonMap[index][2])
        if self.ButtonMap[index][1] ~= "ViewInformation" then
            self:CloseSelf()
        end
    else
        self:CloseSelf()
    end
end

--任务显示
function P_HUDTeamMenu:OnRefresh_ButtonList(widget, index, selected)
    local rowData = Game.TableData.GetPlayerInteractUnitDataRow(self.ButtonMap[index][1])
    if rowData then
        if self.ButtonMap[index][2] then
            widget.Text_Content:SetText(rowData.Name)
        else
            widget.Text_Content:SetText(rowData.NameState1)
        end
    else
        Log.Error("P_HUDTeamMenu Button Data Error!" .. tostring(self.ButtonMap[index][1]))
    end
    -- if index == #self.ButtonMap then
    --     widget:SetEnd(true)
    -- else
    --     widget:SetEnd(false)
    -- end
end

function P_HUDTeamMenu:OnClose()
    UIBase.OnClose(self)
    self.PlayerMenuUtils = nil
end

function P_HUDTeamMenu:TeamIDChange()
    self:CloseSelf()
end

function P_HUDTeamMenu:GroupIDChange()
    self:CloseSelf()
end

function P_HUDTeamMenu:OnMsgTeamMemberChanged(Reason, MemberID)
    if Reason == team_utils.TeamMemberChangeReason.TMCR_LEAVE then
        if MemberID == self.EntityID then
            self:CloseSelf()
        end
    end
end

function P_HUDTeamMenu:OnMsgGroupMemberQuit(groupID, MemberInfo, OPUid)
    for key, value in pairs(MemberInfo) do
        if self.EntityID == value.uid then
            self:CloseSelf()
        end
    end
end


function P_HUDTeamMenu:OnRoleAttrBrief(Result, AttrBrief)
    if Result == NetworkManager.ErrCodes.NO_ERR then
        for key, value in pairs(AttrBrief.briefAttr) do
            AttrBrief.briefAttr[key] = value
        end
        Game.RoleDisplaySystem.ShowOtherPlayerDisplay(
            self.EntityID,
            self.Name,
            self.Level,
            self.ProfessionID,
			self.Sex,
            AttrBrief
        )
        self:CloseSelf()
    end
end

return P_HUDTeamMenu
