local P_HUDFullScreenQte = DefineClass("P_HUDFullScreenQte",UIController)

function P_HUDFullScreenQte:OnCreate()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_Qte, self.OnClicked)
end

function P_HUDFullScreenQte:OnRefresh(Params)
    if Params then
        self.QTEObject = Params.QTEObj
    end
    --Game.ReminderManager:AddReminderById(6408100)
    local tips = Game.TableData.GetQTEStringConstDataRow("FullScreenTips").Value
    self.View.WarningTips.RTB_Content:SetText(tips)
    local ClickEffectPanel = UI.GetUI("ClickEffectPanel")
    if ClickEffectPanel then
        ClickEffectPanel:Show()
        UI.Invoke("ClickEffectPanel", "SwitchclickEffect", 2)
    end
end

function P_HUDFullScreenQte:OnClicked()
    if (self.QTEObject ~= nil) and self.QTEObject.ButtonDown then
        self.QTEObject:ButtonDown()
    end
end

function P_HUDFullScreenQte:OnHide()
    UIBase.OnClose(self)
    UI.Invoke("ClickEffectPanel", "SwitchclickEffect", 1)
end

function P_HUDFullScreenQte:OnClose()
    UIBase.OnClose(self)
    UI.Invoke("ClickEffectPanel", "SwitchclickEffect", 1)
    local ClickEffectPanel = UI.GetUI("ClickEffectPanel")
    ClickEffectPanel:Hide()
end

return P_HUDFullScreenQte