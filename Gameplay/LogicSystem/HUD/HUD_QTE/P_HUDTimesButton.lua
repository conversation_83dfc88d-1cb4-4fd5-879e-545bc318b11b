
local P_HUDTimesButton = DefineClass("P_HUDTimesButton", UIController)
local P_HUDTimesSingleButton = kg_require("Gameplay.LogicSystem.HUD.HUD_JokerQTE.P_HUDTimesSingleButton")
local ESlateVisibility = import("ESlateVisibility")
function P_HUDTimesButton:OnCreate()

    -- 是否移动平台
    self.bIsMobile = PlatformUtil.IsMobilePlatform()
    if not self.bIsMobile then
        self.InputButtonMap = {}
    end
    self.ButtonGroup = BaseList.CreateList(self, BaseList.Kind.GroupView, self.View.Button_Panel, P_HUDTimesSingleButton)
    self.ClickTimes = {}
    -- self.ClickTimes = 1

    
end

function P_HUDTimesButton:OnRefresh(Params)
    if Params then
        self.QTEObject = Params.QTEObj
        self.QTEData = self.QTEObject.Data
    end
    --临时数据
    -- self.QTEObject = {}
    -- self.QTEData = {}
    -- self.QTEData.CaseId = 2001
    -- self.QTEData.SingleSuccessTimesList = {}
    -- self.QTEData.HideButtonOnSuccess = false
    ----
    table.clear(self.ClickTimes)
    self:InitButton(self.QTEData.CaseId)
end

function P_HUDTimesButton:InitButton(caseId, num)
    local data = Game.TableData.GetQTETimesButtonDataRow(caseId)
    local maxInit = #data.Pos
    if not maxInit or maxInit <=0 then return end
    if num and num > 0 then
        maxInit = math.min(maxInit, num)
    end
    self.ButtonGroup:SetData(maxInit)
    for idx = 1, maxInit, 1 do
        local newButton = self.ButtonGroup:GetRendererAt(idx)
        newButton.View.WidgetRoot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.ClickTimes[idx] = 0
        local posx = data.Pos[idx][1]
        local posy = data.Pos[idx][2]
        newButton:OnActive(self, posx, posy, idx)
    end
end

function P_HUDTimesButton:SuccessPressOne(idx)
    if self.QTEObject then
       self.QTEObject:ButtonDown(idx)
        if self.ClickTimes[idx] then
            self.ClickTimes[idx] = self.ClickTimes[idx] + 1
            local maxTimes = self.QTEData.PressTimes or 1
            if self.QTEData.SingleCount then
                maxTimes = self.QTEData.SingleSuccessTimesList[idx] or 1
            end
            if self.ClickTimes[idx] >= maxTimes and self.QTEData.HideButtonOnSuccess then
                self.ButtonGroup:GetRendererAt(idx).View.WidgetRoot:SetVisibility(ESlateVisibility.Collapsed)
                self.ClickTimes[idx] = nil
            end
        end
    end
    -- self.ClickTimes = self.ClickTimes - 1
    -- if self.ClickTimes <=0 then
    --     self:CloseSelf()
    -- end
end

function P_HUDTimesButton:ReturnButton()
    
end

function P_HUDTimesButton:OnRefresh_Button_Panel()
    
end


return P_HUDTimesButton
