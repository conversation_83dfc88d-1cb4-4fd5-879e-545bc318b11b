local ESlateVisibility = import("ESlateVisibility")
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local P_HUDBranch = DefineClass("P_HUDBranch", UIController)

local HUDSystem = kg_require "Gameplay.LogicSystem.HUD.HUDSystem"

P_HUDBranch.eventBindMap = {
    [EEventTypesV2.LEVEL_ON_LEVEL_LOADED] = "OnSpaceChanged",
}
---region create
function P_HUDBranch:ctor()
    self.IsOpen = nil
end

function P_HUDBranch:OnCreate()
    self.IsOpen = nil    -- 是否打开list
    self:OnNewBranchLine()
end

function P_HUDBranch:OnNewBranchLine()
    self:AddUIListener(EUIEventTypes.CLICK, self.View.Button_ClickSelect, self.OnListOpen)

    local PC = import("GameplayStatics").GetPlayerController(_G.GetContextObject(), 0)
    if PC then
        self.View.WidgetRoot:SetUserFocus(PC)
    end

    self:AddUIListener(EUIEventTypes.FocusLost, self.View.WidgetRoot, self.OnFocusLost)


    self.View.SB_BranchListBox:SetVisibility(ESlateVisibility.Hidden)
	self.View.bg_linelist:SetVisibility(ESlateVisibility.Hidden)

    self.BranchListData = {}
    self.BranchLinesGroup = BaseList.CreateList(self, BaseList.Kind.GroupView, "VB_BranchList")
    self.BranchLinesGroup:AddUIListener(
            EUIEventTypes.CLICK, "Button", "OnClick_VB_BranchList_Button"
    )
end
---endregion

function P_HUDBranch:OnRefresh()
    if HUDSystem.GetCurrentLineWorldID() then
        self.View.Text_Object:SetText(HUDSystem.GetLineContentText(HUDSystem.GetCurrentLineWorldLineType(), HUDSystem.GetCurrentLineWorldID()))
        if HUDSystem.GetCurrentLineWorldLineType() == 0 then
            self.View.WidgetRoot:Event_UI_Style(false)
        else
            self.View.WidgetRoot:Event_UI_Style(true)
        end
    end
	
	local showName = ""
	
    local LevelMapID = Game.LevelManager.GetCurrentLevelID()
    local LevelMapData = Game.TableData.GetLevelMapDataRow(LevelMapID)
    if LevelMapData then
		showName = LevelMapData.Name
	end

	self.View.Text_ObjectFilter:SetText(showName)
	self:OnSpaceChanged()
end

function P_HUDBranch:OnFocusLost(Params)
    self.IsOpen = false
    self.View.SB_BranchListBox:SetVisibility(ESlateVisibility.Hidden)
	self.View.bg_linelist:SetVisibility(ESlateVisibility.Hidden)
    self.View.arrow:SetRenderTransformAngle(0)
end

function P_HUDBranch:OnSpaceChanged()
    local CurrentSpace = NetworkManager.GetLocalSpace()
    if CurrentSpace and CurrentSpace:IsLineSpace() and CurrentSpace.planeID == 0 then
        --HUDSystem.ReqGetLineList(CurrentSpace.TemplateID)
        --self.View.CanvasPanel_BrachLine:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.View.Text_Object:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.View.arrow:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.View.SizeBox:SetMaxDesiredWidth(100)
    else
        --self.View.CanvasPanel_BrachLine:SetVisibility(ESlateVisibility.Hidden)
		self.View.Text_Object:SetVisibility(ESlateVisibility.Collapsed)
		self.View.arrow:SetVisibility(ESlateVisibility.Collapsed)
		self.View.SizeBox:SetMaxDesiredWidth(200)
    end
end

function P_HUDBranch:OnLineListUpdate(NewList)
    NewList = NewList or {}

    table.sort(NewList, function(a, b)
        return a.LineType < b.LineType or (a.LineType == b.LineType and a.WorldID % 100000 < b.WorldID % 100000)
    end)

    self.BranchListData = NewList
    for K, V in pairs(NewList) do
        if HUDSystem.GetCurrentLineWorldID() == V.WorldID then
            self.View.Text_Object:SetText(HUDSystem.GetLineContentText(HUDSystem.GetCurrentLineWorldLineType(), HUDSystem.GetCurrentLineWorldID()))
            if HUDSystem.GetCurrentLineWorldLineType() == 0 then
                self.View.WidgetRoot:Event_UI_Style(false)
            else
                self.View.WidgetRoot:Event_UI_Style(true)
            end
        end
    end

	--self.View.bg_linelist.slot:SetSize(FVector2D(295,65*#self.BranchListData))
    self.BranchLinesGroup:SetData(#self.BranchListData)
    -- 长度大于10的时候修改高度为650，反之默认
    if #self.BranchListData > 10 then
        self.View.SB_BranchListBox:SetHeightOverride(650)
    else
        self.View.SB_BranchListBox:ClearHeightOverride()
    end
end

function P_HUDBranch:OnListOpen()
	local CurrentSpace = NetworkManager.GetLocalSpace()
	if not(CurrentSpace and CurrentSpace:IsLineSpace() and CurrentSpace.planeID == 0) then
		return
	end
    if self.IsOpen then
        self:OnFocusLost()
        return
    end
    local Space = Game.NetworkManager.GetLocalSpace()
    if not Space or not Space:IsLineSpace() or Space.planeID ~= 0 then return end--位面中，不显示
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common_Expand, true)
    self.IsOpen = true
    self.View.SB_BranchListBox:SetVisibility(ESlateVisibility.Visible)
	self.View.bg_linelist:SetVisibility(ESlateVisibility.Visible)
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Fadein_list, 0.0, 1,
            EUMGSequencePlayMode.Forward, 1, false)
    self.View.arrow:SetRenderTransformAngle(180)

    local PC = import("GameplayStatics").GetPlayerController(_G.GetContextObject(), 0)
    if PC then
        self.View.WidgetRoot:SetUserFocus(PC)
    end

    if CurrentSpace and CurrentSpace:IsLineSpace() then
        HUDSystem.ReqGetLineList(CurrentSpace.TemplateID)
    end
end

-- 刷新一组格子
function P_HUDBranch:OnRefresh_VB_BranchList(r, index, selected)
    local widget = r --.WidgetRPCpp'coot

    local WorldID = self.BranchListData[index].WorldID
    local LevelMapID = Game.LevelManager.GetCurrentLevelID()

    local LevelMapData = Game.TableData.GetLevelMapDataRow(LevelMapID)
    if LevelMapData then
        widget.Text_Object:SetText(LevelMapData.Name)
    end
    
    widget.Text_Content:SetText(HUDSystem.GetLineContentText(self.BranchListData[index].LineType, WorldID))

    if index == #self.BranchListData then
        widget.Img_Line:SetVisibility(ESlateVisibility.Hidden)
    else
        widget.Img_Line:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    if self.BranchListData[index].LineType == 0 then
        widget:Event_UI_Style(false)
    else
        widget:Event_UI_Style(true)
    end
end

function P_HUDBranch:OnClick_VB_BranchList_Button(index)
    Log.Debug("OnClick_VB_BranchList_ButtonLine ", index)

    local WorldID = self.BranchListData[index].WorldID
    local LineType = self.BranchListData[index].LineType
    if not WorldID then
        return
    end
    local CurrentLineID = HUDSystem.GetCurrentLineWorldID()
    
    local function doSwitchLine()
        Game.me:ReqSwitchLine(WorldID)
    end
    if Game.me and CurrentLineID ~= WorldID then
        if Game.WorldBossSystem:IsInWorldBoss() then
            Game.WorldBossSystem:TrySwitchLine(doSwitchLine)
        else
            doSwitchLine()
        end
    end
    self:OnFocusLost()
end

return P_HUDBranch
