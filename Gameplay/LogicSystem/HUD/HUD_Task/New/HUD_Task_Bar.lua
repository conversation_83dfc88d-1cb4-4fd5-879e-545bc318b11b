local ESlateVisibility = import("ESlateVisibility")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUD_Task_Bar : UIComponent
---@field view HUD_Task_BarBlueprint
local HUD_Task_Bar = DefineClass("HUD_Task_Bar", UIComponent)

HUD_Task_Bar.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUD_Task_Bar:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUD_Task_Bar:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUD_Task_Bar:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUD_Task_Bar:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUD_Task_Bar:InitUIView()
end

---组件刷新统一入口
function HUD_Task_Bar:Refresh(val)
	self.view.Prog_Task:SetPercent(val)
end

function HUD_Task_Bar:SetVisibleOrNot(visible)
	if visible then
		self.userWidget:SetVisibility(ESlateVisibility.Visible)
	else
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

return HUD_Task_Bar
