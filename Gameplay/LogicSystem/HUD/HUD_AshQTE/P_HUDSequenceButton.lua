local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local P_HUDSequenceButton = DefineClass("P_HUDSequenceButton",UIComponent)

function P_HUDSequenceButton:OnCreate()
    self.RemainPressTime = 0    -- 剩余待按次数
    self.AllowUnClickAnim = true    -- 是否允许播放点击动画
    self.AllowPress = true  -- 是否允许按下按钮

    self.PressKey = nil -- 对应触发的键盘按钮
    self.Owner = nil
    self:AddUIListener(_G.EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnButtonClicked)
end

local TEMP_ANCHORS = import("Anchors")()
TEMP_ANCHORS.Minimum = FVector2D(0.0, 0.0)
TEMP_ANCHORS.Maximum = FVector2D(0.0, 0.0)

function P_HUDSequenceButton:OnActive(InParentrHUD, PositionX, PositionY)
    self.Owner = InParentrHUD
    TEMP_ANCHORS.Minimum.X = PositionX 
    TEMP_ANCHORS.Minimum.Y = PositionY
    TEMP_ANCHORS.Maximum.X = TEMP_ANCHORS.Minimum.X
    TEMP_ANCHORS.Maximum.Y = TEMP_ANCHORS.Minimum.Y
    local TEMP_ALIGNMENT = FVector2D(0.5, 0.5)
    
    self.RemainPressTime = self.Owner.QTEData.SingleBtnPressTime
    self.AllowPress = true
    self.AllowUnClickAnim = true

    self.View.Slot:SetAnchors(TEMP_ANCHORS)
    self.View.Slot:SetAlignment(TEMP_ALIGNMENT)
    self.View.Slot:SetAutoSize(true)
    --self.View:SetVisibility(ESlateVisibility.Hidden)

    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Show, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false, function()
        local Speed = 5.0 / (InParentrHUD.QTEData.ButtonLifeTime)
        self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Loop, 0.0, 1, EUMGSequencePlayMode.Forward, Speed, false, function()
            -- 播放隐藏动画时禁止点击
            if self.AllowUnClickAnim == true then
                self.AllowPress = false
                self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Close, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false, function()
                    InParentrHUD:FailedPressOne(self)
                    -- 播放完隐藏动画后允许点击
                    self.AllowPress = true
                end)
            end
        end)
    end)
end

function P_HUDSequenceButton:OnButtonClicked()
    if self.RemainPressTime > 0 and self.AllowPress == true then
        self.RemainPressTime = self.RemainPressTime - 1
        if self.RemainPressTime <= 0 then
            self:StopAnimation(self.View.WidgetRoot, self.View.Ani_Loop)
            self:StopAnimation(self.View.WidgetRoot, self.View.Ani_Show)
            self.Owner:SuccessPressOne(self)
            -- 播放命中动画时禁止点击
            self.AllowPress = false
            self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Click, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false, function()
                self.Owner:ReturnButton(self)
                -- 播放完命中动画后允许点击
                self.AllowPress = true
            end)
        end

    end
end

function P_HUDSequenceButton:OnClose()
    UIBase.OnClose(self)
    self.RemainPressTime = nil
    self.AllowUnClickAnim = nil
    self.AllowPress = nil
    self.Owner = nil
    self.PressKey = nil
end

function P_HUDSequenceButton:SetInputAction(ActionName)
    self.PressKey = ActionName
end

function P_HUDSequenceButton:GetInputAction()
    return self.PressKey
end

return P_HUDSequenceButton