local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

local P_WeakLock = DefineClass("P_WeakLock", WorldWidgetCellBase)

function P_WeakLock:ctor()
end

function P_WeakLock:OnEdgeChanged(bOnEdge)

end

function P_WeakLock:OnUninit()

end

function P_WeakLock:OnInit(Params)
	
end

function P_WeakLock:OnGetFromPool(uid)
    self.EntityID = uid
    self:RefreshUI()
    Game.GlobalEventSystem:AddListener(EEventTypesV2.SETTING_ON_UI_LOCK_STATE_CHANGE, "OnSettingChanged", self)
end

function P_WeakLock:OnReturnToPool()
    Game.GlobalEventSystem:RemoveListener(EEventTypesV2.SETTING_ON_UI_LOCK_STATE_CHANGE, "OnSettingChanged", self)
    self:PlayAnimation(self.View.Ani_Fadeon)
end

function P_WeakLock:OnSettingChanged()
    self:RefreshUI()
end

function P_WeakLock:RefreshUI()
    self:StopAllAnimations(self.View.WidgetRoot)
    local entity = Game.EntityManager:getEntity(self.EntityID)
    if not BSFunc.CheckTargetCamp(Game.me, entity, 32) then
        --友方 中立
        self.bFriend = true
        if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.FriendLockPoint) == 0 then
            self:Hide(ESlateVisibility.Collapsed)
        else
            self:Show(ESlateVisibility.HitTestInvisible)
            self:PlayAnimation(self.View.Ani_Fadein)
        end
    else
        --敌对
        self.bFriend = false
        if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.EnemyLockPoint) == 0 then
            self:Hide(ESlateVisibility.Collapsed)
        else
            self:Show(ESlateVisibility.HitTestInvisible)
            self:PlayAnimation(self.View.Ani_Fadein)
        end
    end
end

return P_WeakLock
