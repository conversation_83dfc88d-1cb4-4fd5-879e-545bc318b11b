local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"

local P_StrongLock = DefineClass("P_StrongLock", WorldWidgetCellBase)

function P_StrongLock:ctor()
    self.bOnEdge = false
end

function P_StrongLock:OnEdgeChanged(bOnEdge)
    self.bOnEdge = bOnEdge
    if bOnEdge then
        self:Hide()
    else
        self:Show()
    end
    Game.EventSystem:Publish(_G.EEventTypes.LOCKDISTANCE_CHANGE,self.bOnEdge)
end


function P_StrongLock:OnUninit()

end

function P_StrongLock:OnInit(Params)

end

function P_StrongLock:OnBindActor(Params)
	
end

function P_StrongLock:OnUnbindActor()

end


function P_StrongLock:OnGetFromPool(uid)
    Game.TargetLockSystem.RegisterStrongLockWidget(self)
	self.EntityID = uid
    --self.view.HUD_Lock:SetVisibility(ESlateVisibility.HitTestInvisible)
    self:RefreshUI()
    if Game.me and Game.me:uid() ~= self.EntityID then
        Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.OnCampChanged, Game.EntityManager:GetEntityEIDByUID(self.EntityID))
    end
    Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.OnCampChanged, GetMainPlayerEID())
    Game.GlobalEventSystem:AddListener(EEventTypesV2.SETTING_ON_UI_LOCK_STATE_CHANGE, "OnSettingChanged", self)
    self.userWidget:SetShowFont(true)
    self.TickTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:OnTick()
    end, 0, -1, nil, nil, true)
end

function P_StrongLock:OnReturnToPool()
    Game.TargetLockSystem.UnRegisterStrongLockWidget()
    self:StopAllAnimations(self.userWidget)
    self.bFriend = nil
    Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function P_StrongLock:OnCampChanged()
    self:RefreshUI()
end

function P_StrongLock:OnSettingChanged()
    self:RefreshUI()
end

function P_StrongLock:OnTick(DeltaTime)
    if Game.TargetLockSystem.LockType == LDE.EBSLockingState.AutoLockTarget then
        self.userWidget:SetShowFont(false)
        return
    end
    if not Game.me or not Game.me.bInWorld then
        return
    end

	local entity = Game.EntityManager:getEntity(self.EntityID)
	if not entity or not entity.bInWorld then
        self.userWidget:SetShowFont(false)
        return
	end

    local Distance = -1
    local playerPos = M3D.Vec3()
    playerPos:Pack(Game.me:GetPosition_P())
    local targetPos = M3D.Vec3()
    targetPos:Pack(entity:GetPosition_P())
    Distance = M3D.GetDistance3D(playerPos, targetPos)
    if Distance >= 250 then
        if Game.SettingsManager:GetIniData('ShowLockDistance') == 1 then
            self.userWidget:SetShowFont(true)
            self.view.Text_Distance:SetText(string.format(StringConst.Get("LOCK_TARGET_DISTANCE"), math.ceil(Distance / 100)))
        else
            self.userWidget:SetShowFont(false)
        end
    else
        self.userWidget:SetShowFont(false)
    end
end

function P_StrongLock:RefreshUI()
	local entity = Game.EntityManager:getEntityWithBrief(self.EntityID)
	if not entity then
		return
	end 

    self:StopAllAnimations(self.userWidget)
    
    local MonsterData = Game.TableData.GetMonsterDataRow(entity.TemplateID)
    local bRequireAnimation = true
    if MonsterData and MonsterData.bGhost then
        bRequireAnimation = false
    end
	local instigator = BSFunc.GetActorInstigator(entity)
    local bEnemy = BSFunc.CheckTargetCamp(Game.me, instigator, 32)
    if Game.TargetLockSystem.LockType == LDE.EBSLockingState.AutoLockTarget then
        self.view.VX_LockWeak:SetVisibility(ESlateVisibility.HitTestInvisible)
        self.view.VX_LockGreen:SetVisibility(ESlateVisibility.Collapsed)
        self.view.VX_LockRed:SetVisibility(ESlateVisibility.Collapsed)
        if not bEnemy then
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.FriendLockPoint) == 0 then
                self:Hide()
            else
                self:Show(ESlateVisibility.HitTestInvisible)
                if bRequireAnimation then
                    --渲染的第一帧会有个跳变，这里先隐藏，通过动画显示出来
                    self.view.Canv_Lock:SetRenderOpacity(0.0)
                    self:PlayAnimation(self.view.Ani_Fadein_White)
                else
                    self:PlayAnimation(self.view.Ani_White_End)
                end
            end
        else
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.EnemyLockPoint) == 0 then
                self:Hide()
            else
                self:Show(ESlateVisibility.HitTestInvisible)
                if bRequireAnimation then
                    self.view.Canv_Lock:SetRenderOpacity(0.0)
                    self:PlayAnimation(self.view.Ani_Fadein_White)
                else
                    self:PlayAnimation(self.view.Ani_White_End)
                end
            end
        end
         
    else
        if not bEnemy then
            --友方 中立
            self.view.VX_LockWeak:SetVisibility(ESlateVisibility.Collapsed)
            self.view.VX_LockGreen:SetVisibility(ESlateVisibility.HitTestInvisible)
            self.view.VX_LockRed:SetVisibility(ESlateVisibility.Collapsed)
            self.bFriend = true
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.FriendLockWindow) == 0 then
                self:Hide(ESlateVisibility.Collapsed)
            else
                self:Show(ESlateVisibility.HitTestInvisible)
                self.userWidget:Event_UI_Style(1, 1)
                if bRequireAnimation then
                    self.view.Canv_Lock:SetRenderOpacity(0.0)
                    self:PlayAnimation(self.view.Ani_Fadein_Green)
                else
                    self:PlayAnimation(self.view.Ani_Green_End)
                end
            end
        else
            self.view.VX_LockWeak:SetVisibility(ESlateVisibility.Collapsed)
            self.view.VX_LockGreen:SetVisibility(ESlateVisibility.Collapsed)
            self.view.VX_LockRed:SetVisibility(ESlateVisibility.HitTestInvisible)
            --敌对
            self.bFriend = false
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.EnemyLockWindow) == 0 then
                self:Hide(ESlateVisibility.Collapsed)
            else
                self.userWidget:Event_UI_Style(0, 2)
                self:Show(ESlateVisibility.HitTestInvisible)
                if bRequireAnimation then
                    self.view.Canv_Lock:SetRenderOpacity(0.0)
                    self:PlayAnimation(self.view.Ani_Fadein_Red)
                else
                    self:PlayAnimation(self.view.Ani_Red_End)
                end
            end
        end
    end
end


function P_StrongLock:NotifyLockingTypeChanged(OldType, NewType)
    local entity = Game.EntityManager:getEntity(self.EntityID)
    local bEnemy = BSFunc.CheckTargetCamp(Game.me, entity, 32)
    if NewType == LDE.EBSLockingState.AutoLockTarget then
        self.view.VX_LockWeak:SetVisibility(ESlateVisibility.HitTestInvisible)
        self.view.VX_LockGreen:SetVisibility(ESlateVisibility.Collapsed)
        self.view.VX_LockRed:SetVisibility(ESlateVisibility.Collapsed)
        if not bEnemy then
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.FriendLockPoint) == 0 then
                self:Hide(ESlateVisibility.Collapsed)
            else
                self:Show(ESlateVisibility.HitTestInvisible)
                self:PlayAnimation(self.view.Ani_Fadein_White)
            end
        else
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.EnemyLockPoint) == 0 then
                self:Hide(ESlateVisibility.Collapsed)
            else
                self:Show(ESlateVisibility.HitTestInvisible)
                self:PlayAnimation(self.view.Ani_Fadein_White)
            end
        end
    else
        if not bEnemy then
            --友方 中立
            self.view.VX_LockWeak:SetVisibility(ESlateVisibility.HitTestInvisible)
            self.view.VX_LockGreen:SetVisibility(ESlateVisibility.HitTestInvisible)
            self.view.VX_LockRed:SetVisibility(ESlateVisibility.Collapsed)
            self.bFriend = true
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.FriendLockWindow) == 0 then
                self:Hide(ESlateVisibility.Collapsed)
            else
                self:Show(ESlateVisibility.HitTestInvisible)
                self.userWidget:Event_UI_Style(1, 1)
                self:PlayAnimation(self.view.Ani_White_To_Green)
            end
        else
            self.view.VX_LockWeak:SetVisibility(ESlateVisibility.HitTestInvisible)
            self.view.VX_LockGreen:SetVisibility(ESlateVisibility.Collapsed)
            self.view.VX_LockRed:SetVisibility(ESlateVisibility.HitTestInvisible)
            --敌对
            self.bFriend = false
            if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum.EnemyLockWindow) == 0 then
                self:Hide(ESlateVisibility.Collapsed)
            else
                self.userWidget:Event_UI_Style(0, 2)
                self:Show(ESlateVisibility.HitTestInvisible)
                self:PlayAnimation(self.view.Ani_White_To_Red)
            end
        end
    end
end

function P_StrongLock:NotifyFightRelationshipChanged()
	self:RefreshUI()
end

return P_StrongLock
