local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"

local P_StrongLockTrace = DefineClass("P_StrongLockTrace", WorldWidgetCellBase)

function P_StrongLockTrace:ctor()
    self.bOnEdge = false
end

function P_StrongLockTrace:OnDistanceChanged(bOnEdge)
    self.bOnEdge = bOnEdge
	self:RefreshShowHide()
end

function P_StrongLockTrace:OnDirectionChanged(Direction)
    if not self.bOnEdge then
        return
    end
    local Angle = import("UIFunctionLibrary").DotProductWithForward2D(Direction)
    Angle = Angle + 90
    self.view.SizeBox_Arrow:SetRenderTransformAngle(Angle)
end

function P_StrongLockTrace:OnUninit()

end

function P_StrongLockTrace:OnInit(Params)

end

function P_StrongLockTrace:OnBindActor(Params)
end

function P_StrongLockTrace:OnUnbindActor()

end

function P_StrongLockTrace:OnOpen()
	self.bOnEdge = false
	self:RefreshShowHide()
end

function P_StrongLockTrace:RefreshShowHide()
    if self.bOnEdge then
		self:Show(ESlateVisibility.HitTestInvisible)
	else
		self:Hide(ESlateVisibility.Collapsed)
	end
end

function P_StrongLockTrace:OnGetFromPool(uid)
    self.EntityID = uid
	self:Hide(ESlateVisibility.Collapsed)
    self:RefreshUI()
    if self.EntityID ~= Game.me:uid() then
        Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.OnCampChanged, self.EntityID)
    end
    Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.OnCampChanged, GetMainPlayerEID())
	Game.GlobalEventSystem:AddListener(EEventTypesV2.SETTING_ON_UI_LOCK_STATE_CHANGE, "OnSettingChanged", self)
    Game.EventSystem:AddListener(_G.EEventTypes.LOCKDISTANCE_CHANGE, self, self.OnDistanceChanged)
    self.TickTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:OnTick()
    end, 0, -1, nil, nil, true)
end

function P_StrongLockTrace:OnReturnToPool()
    if self.bFriend then
        self.userWidget:Event_UI_Style(1)
    else
        self.userWidget:Event_UI_Style(0)
    end
    self.bFriend = nil
    self.EntityID = nil
    Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function P_StrongLockTrace:OnCampChanged()
    self:RefreshUI()
end

function P_StrongLockTrace:OnSettingChanged()
    self:RefreshUI()
end

function P_StrongLockTrace:OnTick(DeltaTime)
    if Game.me then
        local Distance = -1
		local entity = Game.EntityManager:getEntity(self.EntityID)
		if not entity then
			return
		end
		local playerPos = M3D.Vec3()
		playerPos:Pack(Game.me:GetPosition_P())
		local targetPos = M3D.Vec3()
		targetPos:Pack(entity:GetPosition_P())
		Distance = M3D.GetDistance3D(playerPos, targetPos)
		self.view.Text_Distance:SetText(string.format(StringConst.Get("LOCK_TARGET_DISTANCE"), math.ceil(Distance / 100)))
    end 
end

function P_StrongLockTrace:RefreshUI()
    if Game.SettingsManager:GetIniData(Enum.ESettingDataEnum['ShowLockDistance']) == 1 then
        self.view.Text_Distance:SetVisibility(ESlateVisibility.HitTestInvisible)
    else
        self.view.Text_Distance:SetVisibility(ESlateVisibility.Hidden)
    end

    local entity = Game.EntityManager:getEntity(self.EntityID)
    if (BSFunc.CheckTargetCamp(Game.me, entity, 32) == false) then
        --友方 中立
        self.bFriend = true
        self.userWidget:Event_UI_Style(1)
    else
        --敌对
        self.bFriend = false
        self.userWidget:Event_UI_Style(0)
    end
end

return P_StrongLockTrace
