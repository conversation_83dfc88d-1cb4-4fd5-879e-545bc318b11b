local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDAuctionBtn : UIComponent
---@field view HUDAuctionBtnBlueprint
local HUDAuctionBtn = DefineClass("HUDAuctionBtn", UIComponent)
local ESlateVisibility = import("ESlateVisibility")

HUDAuctionBtn.eventBindMap = {
	[_G.EEventTypes.ON_AUCTION_BEGIN] = "RefreshAuction",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDAuctionBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDAuctionBtn:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDAuctionBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDAuctionBtn:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDAuctionBtn:InitUIView()
end

---组件刷新统一入口
function HUDAuctionBtn:OnRefresh()
	self:RefreshAuction()
end

function HUDAuctionBtn:on_Btn_ClickArea_Clicked()
	UI.ShowUI("P_TradePanel", { Enum.EFunctionInfoData["MODULE_LOCK_AUCTION"] })
end

function HUDAuctionBtn:OnClose()
	Log.Debug("========================关闭拍卖")
end

function HUDAuctionBtn:RefreshAuction()
	if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_AUCTION", false) then
		local TotalDisappearTime = Game.AuctionManager:GetTotalDisappearTime()
		if TotalDisappearTime then
			self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self.view.Text_CD:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			local ShowTime = TotalDisappearTime - Game.TableData.GetConstDataRow("BID_CLOSE_ENTER_DELAY") * 60
			self.RefreshTimer = self:StartTimer("P_HUDBaseViewAuction",
				function()
					if TotalDisappearTime < Game.TimeUtils.GetCurTime() then
						self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
						self:StopTimer("P_HUDBaseViewAuction")
					else
						if Game.TimeUtils.GetCurTime() < ShowTime then
							local handledTime = Game.TimeUtils.FormatCountDownString(
								(ShowTime - Game.TimeUtils.GetCurTime()) * 1000,
								false)

							self.view.Text_CD:SetText(handledTime)
						else
							self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
							self.view.Text_CD:SetVisibility(ESlateVisibility.Collapsed)
						end
					end
				end,
				1000, -1, nil, true
			)
		end
	else
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end

return HUDAuctionBtn
