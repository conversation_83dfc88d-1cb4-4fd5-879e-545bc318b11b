local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDTopRightBtn : UIComponent
---@field view HUDTopRightBtnBlueprint
local HUDTopRightBtn = DefineClass("HUDTopRightBtn", UIComponent)

HUDTopRightBtn.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDTopRightBtn:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDTopRightBtn:InitUIData()
    ---按钮点击事件
    ---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onClickEvent = LuaDelegate.new()
    ---按钮Hover事件
    ---@type LuaDelegate<fun()>
    self.onHoveredEvent = LuaDelegate.new()
    ---按钮UnHover事件
    ---@type LuaDelegate<fun()>
    self.onUnhoveredEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function HUDTopRightBtn:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDTopRightBtn:InitUIEvent()
    self:AddUIEvent(self.view.Button.OnClicked, "on_Button_Clicked")
    self:AddUIEvent(self.view.Button.OnHovered, "on_Button_Hovered")
    self:AddUIEvent(self.view.Button.OnUnhovered, "on_Button_Unhovered")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDTopRightBtn:InitUIView()
end

---组件刷新统一入口
function HUDTopRightBtn:Refresh(...)
end

--- 此处为自动生成
function HUDTopRightBtn:on_Button_Clicked()
    self.onClickEvent:Execute()
end

--- 此处为自动生成
function HUDTopRightBtn:on_Button_Hovered()
    self.onHoveredEvent:Execute()
end

--- 此处为自动生成
function HUDTopRightBtn:on_Button_Unhovered()
    self.onUnhoveredEvent:Execute()
end

return HUDTopRightBtn
