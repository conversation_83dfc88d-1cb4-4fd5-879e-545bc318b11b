local InteractSelectListComp = kg_require("Gameplay.LogicSystem.NPC.InteractSelectListComp")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")
local UIFunctionLibrary = import("UIFunctionLibrary")
local C7FunctionLibrary = import("C7FunctionLibrary")

---@class HUDInteractPanel : UIPanel
---@field view HUDInteractPanelBlueprint
local HUDInteractPanel = DefineClass("HUDInteractPanel", UIPanel)

function HUDInteractPanel:CheckVisibility()
	if #self.interactInfos <= 0 then
		self.WBP_Interact_SelectListCom:SetVisible(false)
	else
		self.WBP_Interact_SelectListCom:SetVisible(true)
	end
end

HUDInteractPanel.eventBindMap = {
	[EEventTypesV2.INTERACTIVE_BEGIN] = "LockKeyInput",
	[EEventTypesV2.INTERACTIVE_FINISH] = "UnlockKeyInput",
	[EEventTypesV2.ON_INPUT_UI_SHORTCUT] = "OnInputUIShortcut",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDInteractPanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDInteractPanel:InitUIData()
	self.showHintIndex = 0
	self.bShowHint = nil    -- luacheck: ignore
	self.bOnCtrlButtonPressed = false
	self.tempPos = FVector2D()

	---@type table @交互按钮数据
	self.interactInfos = {}
	self.clickBehaviour = true
end

--- UI组件初始化，此处为自动生成
function HUDInteractPanel:InitUIComponent()
    ---@type InteractSelectListComp
    self.WBP_Interact_SelectListCom = self:CreateComponent(self.view.WBP_Interact_SelectList, InteractSelectListComp)
end

---UI事件在这里注册，此处为自动生成
function HUDInteractPanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDInteractPanel:InitUIView()
end

---面板打开的时候触发
function HUDInteractPanel:OnRefresh(...)
end


---@public 添加交互
---@param Task table 交互按钮详情
function HUDInteractPanel:AddInterActTask(Task)
	if type(Task) ~= "table" then
		return
	end
	self.interactInfos[#self.interactInfos+1] = Task
	Log.DebugFormat("[HUDInteract] AddInterActTask Task（Title:%s）", Task.Title)
	if not self.tid then
		self.tid = self:StartTimer("NextFrameUpdate", function()
			self:sortInteractInfos()
			self.tid = nil
		end, 1, 1)
	end
end

function HUDInteractPanel:RemoveInteractTask(Task)
	local toBeRemoveTaskUID = nil
	for i = 1, #self.interactInfos do
		if self.interactInfos[i] == Task then
			if not self.interactInfos[i].bInRemove then
				---@type NPCBtnSelectItem
				local component = self.WBP_Interact_SelectListCom:GetItemByIndex(i)
				if component then
					self.interactInfos[i].bInRemove = true
					if component.Task then
						toBeRemoveTaskUID = component.Task.UID
					end
				else
					table.remove(self.interactInfos, i)
					if not self.tid1 then
						self.tid1 = self:StartTimer("NextFrameUpdate1", function()
							table.removeByFunc(self.interactInfos, function(e) return e.bInRemove end)
							self.WBP_Interact_SelectListCom:Refresh(self.interactInfos)
							self.tid1 = nil
							self:recoverClickBehaviour()
						end, 1, 1)
					end
				end
				self:UpdateKeyHint()
			end
			break
		end
	end
	if toBeRemoveTaskUID ~= nil then
		self:DoRemove(nil, toBeRemoveTaskUID)
	end
	self:CheckVisibility()
end

function HUDInteractPanel:GetIndexByUID(UID)
	for i = 1, #self.interactInfos, 1 do
		if self.interactInfos[i].UID == UID then
			return i
		end
	end
end

function HUDInteractPanel:DoRemove(index, UID)
	table.removeByFunc(self.interactInfos, function(e) return e.bInRemove end)
	local removeIndex = self:GetIndexByUID(UID)
	if removeIndex then
		local task = table.remove(self.interactInfos, removeIndex)
		Log.DebugFormat("[HUDInteract] RemoveInterActTask Task（Title:%s， RemoveIndex）", task.Title, removeIndex)
	end
	self.WBP_Interact_SelectListCom:Refresh(self.interactInfos)
	self:UpdateKeyHint()
	self:CheckVisibility()
	self:recoverClickBehaviour()
end

function HUDInteractPanel:DoPlayOn(index, UID)
	local idx = self:GetIndexByUID(UID)
	if idx then
		self.interactInfos[index].bIsShowing = true
	end
end

function HUDInteractPanel:UpdateInteractTask(OldTask, NewTask)
	if NewTask then
		local match = false
		for i = 1, #self.interactInfos, 1 do
			if self.interactInfos[i] == OldTask then
				self.interactInfos[i] = NewTask
				---@type NPCBtnSelectItem
				local uiComponent = self.WBP_Interact_SelectListCom:GetItemByIndex(i)
				if uiComponent then
					uiComponent:refreshInteractSelect()
				end
				match = true
				break
			end
		end
		if not match then
			self:AddInterActTask(NewTask)
		end
	end
end

function HUDInteractPanel:sortInteractInfos()
	table.removeByFunc(self.interactInfos, function(e) return e.bInRemove end)
	table.sort(self.interactInfos, function(a, b)
		if a.bInRemove then
			if not b.bInRemove then
				return false
			end
		elseif b.bInRemove then
			return true
		end
		if a.Priority == b.Priority then
			if a.Order and b.Order then
				return a.Order < b.Order
			end

			return a.UID < b.UID
		end
		return a.Priority > b.Priority
	end)
	self.WBP_Interact_SelectListCom:Refresh(self.interactInfos)
	self.WBP_Interact_SelectListCom:SetSelectedItemByIndex(1, true)
	self:CheckVisibility()
	self:UpdateKeyHint()
	self:recoverClickBehaviour()
end

function HUDInteractPanel:GetInteractInfo(index)
	return self.interactInfos[index]
end

function HUDInteractPanel:OnOpen()
	self.bShowHint = false
	self.bOnCtrlButtonPressed = false
	self.showHintIndex = 0
	table.clear(self.interactInfos)
	if C7FunctionLibrary.IsC7Editor() then
		if UIFunctionLibrary.GetPreviewPlatform
			and UIFunctionLibrary.GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC then
			self.bShowHint = true
		end
	else
		if not PlatformUtil.IsMobilePlatform() then
			self.bShowHint = true
		end
	end
	if not self.bShowHint then
		self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(false)
	else
		self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(true)
	end

	local InteractTasks, npcTasks, npcSPTasks, quickUseItemTasks = Game.HUDInteractManager:GetAllTasks()

	for uid, Tasks in pairs(InteractTasks) do
		for _, Task in pairs(Tasks) do
			if type(Task) == "table" then
				self.interactInfos[#self.interactInfos+1] = Task
			end
		end
	end
	for _, Task in pairs(npcTasks) do
		if type(Task) == "table" then
			self.interactInfos[#self.interactInfos+1] = Task
		end
	end
	for _, TaskList in pairs(npcSPTasks) do
		for _, Task in pairs(TaskList) do
			if type(Task) == "table" then
				self.interactInfos[#self.interactInfos+1] = Task
			end
		end
	end
	for _, itemTask in pairs(quickUseItemTasks) do
		if type(itemTask) == "table" then
			self.interactInfos[#self.interactInfos+1] = itemTask
		end
	end
	local avatarInteractorHandles = Game.HUDInteractManager:GetAvatarInteractorHandles()
	for eid, task in pairs(avatarInteractorHandles) do
		self.interactInfos[#self.interactInfos+1] = task
	end
	self:sortInteractInfos()
	self:CheckVisibility()
end

function HUDInteractPanel:OnShow()
	self:StartTimer("NextFrameKeyHintCheck", function()
		self:UpdateKeyHint()
	end, 1, 1)
end

function HUDInteractPanel:LockKeyInput()
end

function HUDInteractPanel:UnlockKeyInput()
end

function HUDInteractPanel:GetShowHintIndex()
	return self.showHintIndex
end

function HUDInteractPanel:SelectItem(index)
	self.WBP_Interact_SelectListCom:SetSelectedItemByIndex(index, true)
	self.showHintIndex = index
end

function HUDInteractPanel:IsHudQuickUseShow()
	local hudPanel = UI.GetUI(UIPanelConfig.HUD_Panel)
	return hudPanel and hudPanel:IsComponentShow("HUD_QuickUse")
end

function HUDInteractPanel:OnHudInteractAxis(value)
	if self.showHintIndex then
		local total = #self.interactInfos
		local bHasQuickUseItem = false
		if self:IsHudQuickUseShow() then
			total = total + 1
			bHasQuickUseItem = true
		end
		if total > 1 then
			if value > 0 then
				--上移
				local beforeCell = self.interactInfos[self.showHintIndex-1]
				if beforeCell and (not beforeCell.bInRemove) then
					local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
					if cell then
						cell:ConfigKeyHint(false)
					end
					self.showHintIndex = self.showHintIndex - 1
					local prevCell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
					if prevCell then
						prevCell:ConfigKeyHint(true)
						self.WBP_Interact_SelectListCom:SetSelectedItemByIndex(self.showHintIndex, true)
					end
				else
					if bHasQuickUseItem then
						local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
						if cell then
							cell:ConfigKeyHint(false)
						end
						self.showHintIndex = 0
						local HUDInteractManager = Game.HUDInteractManager
						HUDInteractManager:SetKeyHint(HUDInteractManager.KeyHintType.QuickUse)
						Game.GlobalEventSystem:Publish(EEventTypesV2.OK_KEY_HINT_UPDATE)
					end
				end
			else
				--下移
				local nextCell = self.interactInfos[self.showHintIndex+1]
				if nextCell and (not nextCell.bInRemove) then
					local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
					if cell then
						cell:ConfigKeyHint(false)
					end
					self.showHintIndex = self.showHintIndex + 1
					local HUDInteractManager = Game.HUDInteractManager
					HUDInteractManager:SetKeyHint(HUDInteractManager.KeyHintType.Interact)
					local afterCell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
					if afterCell then
						afterCell:ConfigKeyHint(true)
						self.WBP_Interact_SelectListCom:SetSelectedItemByIndex(self.showHintIndex, true)
					end
				end
			end
		end
	end
end

function HUDInteractPanel:SetKeyHint(uid)
	if self.bShowHint then
		local index = self:GetIndexByUID(uid)
		if index then
			if self.showHintIndex and self.showHintIndex ~= index then
				local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
				if cell then
					cell:ConfigKeyHint(false)
				end
				self.showHintIndex = 0
			end
			self.showHintIndex = index
			self:keyHintCurItem()
		end
	end
end

function HUDInteractPanel:UpdateKeyHint()
	if self.bShowHint then
		local HUDInteractManager = Game.HUDInteractManager
		local bIsNone = HUDInteractManager:GetKeyHintType() == HUDInteractManager.KeyHintType.None
		local bIsInteract = HUDInteractManager:GetKeyHintType() == HUDInteractManager.KeyHintType.Interact
		if bIsNone or bIsInteract then
			for i = 1, #self.interactInfos, 1 do
				local info = self.interactInfos[i]
				if (not info.bInRemove) and (not info.ExtraParams.bIsGray) then
					if self.showHintIndex and self.showHintIndex > 0 then
						local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
						if cell then
							cell:ConfigKeyHint(false)
						end
					end
					self.showHintIndex = i
					if bIsNone then
						HUDInteractManager:SetKeyHint(HUDInteractManager.KeyHintType.Interact)
					end
					local cell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
					if cell then
						cell:ConfigKeyHint(true)
					end
					break
				end
			end
		end

		local total = 0
		for i = 1, #self.interactInfos, 1 do
			if not self.interactInfos[i].bInRemove then
				total = total + 1
				if total > 1 then
					break
				end
			end
		end
		if self:IsHudQuickUseShow() then
			total = total + 1
		end
		if total > 1 then
			self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(true)
			EnableZoomInput(false)
		else
			self.WBP_Interact_SelectListCom:SetIconWheelVisibleOrNot(false)
			EnableZoomInput(true)
			if total <= 0 then
				if HUDInteractManager:GetKeyHintType() == HUDInteractManager.KeyHintType.Interact then
					HUDInteractManager:SetKeyHint(HUDInteractManager.KeyHintType.None)
				end
			end
		end
	end
end

function HUDInteractPanel:recoverClickBehaviour()
	self.clickBehaviour = true
end

---因为交互物也是走的这里，所以需要判断一下Task的类型
function HUDInteractPanel:needCheckDialogueControlState(index)
	local info = self.interactInfos[index]
	if info and not info.bInRemove then
		local taskType = info.Type
		local interactTypes = Game.HUDInteractManager.InteractType
		return taskType == interactTypes.Task or taskType == interactTypes.Common or taskType == interactTypes.Function
	end
end

function HUDInteractPanel:OnItemClicked(index)
	if not self.clickBehaviour then return end
	-- 这里需要考虑一下状态冲突
	if self:needCheckDialogueControlState(index) and (not Game.QuestSystem:CanEnterDialogueControlState()) then
		return false
	end
	self.clickBehaviour = false
	self:StartTimer("DisableDoubleClick", function() self:recoverClickBehaviour() end, 0.5, 1)

	local info = self.interactInfos[index]
	if info and not info.bInRemove then
		if Game.me then
			Game.me:TryStopAutoBattle()
		end
		if info.ExtraParams.bLockedState and info.ExtraParams.lockedReminderID then
			Game.ReminderManager:AddReminderById(info.ExtraParams.lockedReminderID)
		else
			xpcall(info.OnClicked, _G.CallBackError, info.ExtraParams.uiTemplateID)
		end
		return true
	end
end

function HUDInteractPanel:ClickCell(taskRingID)
	if #self.interactInfos <= 0 then
		return false
	end

	if taskRingID then
		for i = 1, #self.interactInfos, 1 do
			local info = self.interactInfos[i]
			if not info.bInRemove then
				if info.ExtraParams and info.ExtraParams.TaskRingID == taskRingID then
					return self:OnItemClicked(i)
				end
			end
		end
	elseif self.showHintIndex > 0 then
		return self:OnItemClicked(self.showHintIndex)
	end
end


---@public
function HUDInteractPanel:SetGray(bIsGray, Task)
	if Task and type(Task) == "table" and Task.UID then
		for i = 1, #self.interactInfos do
			local info = self.interactInfos[i]
			if info == Task then
				info.ExtraParams.bIsGray = bIsGray
				local component = self.WBP_Interact_SelectListCom:GetItemByIndex(i)
				if component then
					component:SetGray(bIsGray)
				end
				self:UpdateKeyHint()
				break
			end
		end
	end
end

---@public
function HUDInteractPanel:UnlockTaskClick(task)
	if task and type(task) == "table" and task.UID then
		for i = 1, #self.interactInfos do
			local info = self.interactInfos[i]
			if info == task then
				info.bOnClicked = false
			end
		end
	end
end

function HUDInteractPanel:OnClose()
	self.tid = nil
	self.tid1 = nil
	EnableZoomInput(true)
	local HUDInteractManager = Game.HUDInteractManager
	if HUDInteractManager:GetKeyHintType() == HUDInteractManager.KeyHintType.Interact then
		HUDInteractManager:SetKeyHint(HUDInteractManager.KeyHintType.None)
	end
end

function HUDInteractPanel:OnHide()
	EnableZoomInput(true)
end

function HUDInteractPanel:keyHintCurItem()
	---@type NPCBtnSelectItem
	local showCell = self.WBP_Interact_SelectListCom:GetItemByIndex(self.showHintIndex)
	if showCell then
		showCell:ConfigKeyHint(true)
		local HUDInteractManager = Game.HUDInteractManager
		HUDInteractManager:SetKeyHint(HUDInteractManager.KeyHintType.Interact)
	end
end

function HUDInteractPanel:InterActTaskReEnter()
	self:keyHintCurItem()
end

function HUDInteractPanel:OnInputUIShortcut(actionName, keyActionCfg, actionTypeID)
	if Game.TableData.GetConstDataRow("OPEN_INTERACTIVE_INTERFACE") == actionTypeID then
		self:ClickCell()
	end
end

return HUDInteractPanel
