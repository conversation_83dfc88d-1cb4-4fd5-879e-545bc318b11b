local HUDGuildBattleTop = kg_require("Gameplay.LogicSystem.HUD.HUD_GuildBattle.HUDGuildBattleTop")
local HUDGuildTower_Bar = kg_require("Gameplay.LogicSystem.HUD.HUD_GuildBattle.HUDGuildTower_Bar")
local HUDGuildBattleOccupation_List = kg_require("Gameplay.LogicSystem.HUD.HUD_GuildBattle.HUDGuildBattleOccupation_List")
local UITempComBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UITempComBtn")
local HUDGuildBattleQuick = kg_require("Gameplay.LogicSystem.HUD.HUD_GuildBattle.HUDGuildBattleQuick")

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDGuildBattlePanel : UIPanel
---@field view HUDGuildBattlePanelBlueprint
local HUDGuildBattlePanel = DefineClass("HUDGuildBattlePanel", UIComponent)

HUDGuildBattlePanel.eventBindMap = {
	-- todo:预留进入攻击范围事件，刷新血条
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDGuildBattlePanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDGuildBattlePanel:InitUIData()
	self.quickIsShow = false -- 快捷栏是否显示

	self.showTowerID = 0 -- 当前显示的塔的EID
end

--- UI组件初始化，此处为自动生成
function HUDGuildBattlePanel:InitUIComponent()
    ---@type HUDGuildBattleTop
    self.WBP_HUDGuildBattleTopCom = self:CreateComponent(self.view.WBP_HUDGuildBattleTop, HUDGuildBattleTop)
    ---@type HUDGuildTower_Bar
    self.WBP_HUDGuildTower_BarCom = self:CreateComponent(self.view.WBP_HUDGuildTower_Bar, HUDGuildTower_Bar)
    ---@type HUDGuildBattleOccupation_List
    self.WBP_HUDGuildBattleOccupation_ListCom = self:CreateComponent(self.view.WBP_HUDGuildBattleOccupation_List, HUDGuildBattleOccupation_List)
    ---@type UITempComBtn
    self.WBP_HUDExitDungeonBtn_luaCom = self:CreateComponent(self.view.WBP_HUDExitDungeonBtn_lua, UITempComBtn)
    ---@type UITempComBtn
    self.WBP_InformationBtn_luaCom = self:CreateComponent(self.view.WBP_InformationBtn_lua, UITempComBtn)
    ---@type UITempComBtn
    self.WBP_QuickSetBtn_luaCom = self:CreateComponent(self.view.WBP_QuickSetBtn_lua, UITempComBtn)
    ---@type HUDGuildBattleQuick
    self.WBP_HUDGuildBattleQuick_luaCom = self:CreateComponent(self.view.WBP_HUDGuildBattleQuick_lua, HUDGuildBattleQuick)
end

---UI事件在这里注册，此处为自动生成
function HUDGuildBattlePanel:InitUIEvent()
    self:AddUIEvent(self.WBP_HUDExitDungeonBtn_luaCom.onClickEvent, "on_WBP_HUDExitDungeonBtn_luaCom_ClickEvent")
    self:AddUIEvent(self.WBP_InformationBtn_luaCom.onClickEvent, "on_WBP_InformationBtn_luaCom_ClickEvent")
    self:AddUIEvent(self.WBP_QuickSetBtn_luaCom.onClickEvent, "on_WBP_QuickSetBtn_luaCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDGuildBattlePanel:InitUIView()
end

---面板打开的时候触发
function HUDGuildBattlePanel:OnRefresh(...)
	self.WBP_HUDGuildBattleTopCom:Refresh()
	self.WBP_HUDGuildBattleOccupation_ListCom:Refresh()
	--self.WBP_HUDGuildTower_BarCom:Refresh()
	self.WBP_HUDGuildTower_BarCom:SetVisibility(false)

	-- todo：中文后续处理
	self.WBP_HUDExitDungeonBtn_luaCom:Refresh()
	self.WBP_InformationBtn_luaCom:Refresh()
	self.WBP_QuickSetBtn_luaCom:Refresh()
	self.WBP_HUDGuildBattleQuick_luaCom:Refresh()
	self.quickIsShow = false
	self.WBP_HUDGuildBattleQuick_luaCom:SetVisibility(self.quickIsShow)
end


--- 此处为自动生成
function HUDGuildBattlePanel:on_WBP_HUDExitDungeonBtn_luaCom_ClickEvent()
	Game.GuildLeagueSystem:ExitGuildLeague()
end

--- 此处为自动生成
function HUDGuildBattlePanel:on_WBP_InformationBtn_luaCom_ClickEvent()
	Game.NewUIManager:OpenPanel("GuildBattleInformationPanel", { inMatch = true })
end

--- 此处为自动生成
function HUDGuildBattlePanel:on_WBP_QuickSetBtn_luaCom_ClickEvent()
	self.quickIsShow = not self.quickIsShow
	self.WBP_HUDGuildBattleQuick_luaCom:SetVisibility(self.quickIsShow)
end

function HUDGuildBattlePanel:OnEnterTowerAttackBox(instanceID)
	self.showTowerID = instanceID
	self.WBP_HUDGuildTower_BarCom:Refresh(instanceID)
	self.WBP_HUDGuildTower_BarCom:SetVisibility(true)
end

function HUDGuildBattlePanel:OnExitTowerAttackBox(instanceID)
	self.showTowerID = 0
	self.WBP_HUDGuildTower_BarCom:PlayOutAnimation()
	
end

function HUDGuildBattlePanel:RefreshTowerBarHP(id)
	if self.showTowerID == id then
		self.WBP_HUDGuildTower_BarCom:SetHpPercent()
	end
end

return HUDGuildBattlePanel
