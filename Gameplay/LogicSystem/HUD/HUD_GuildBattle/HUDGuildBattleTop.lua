local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local const = kg_require("Shared.Const")
local worldConst = kg_require "Shared.Const.WorldConst"
---@class HUDGuildBattleTop : UIComponent
---@field view HUDGuildBattleTopBlueprint
local HUDGuildBattleTop = DefineClass("HUDGuildBattleTop", UIComponent)

HUDGuildBattleTop.eventBindMap = {
	[EEventTypesV2.ON_GUILD_LEAGUE_SET_START_TIMESTAMP] = "OnStartTimestampChanged",
	[EEventTypesV2.ON_GUILD_LEAGUE_SET_END_TIMESTAMP] = "InitData",
	[EEventTypesV2.ON_MSG_GUILD_LEAGUE_CREATE_TOWER] = "RefreshTowerInfo",
	[EEventTypesV2.ON_GUILD_LEAGUE_SET_STAGE] = "OnStageChanged",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDGuildBattleTop:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDGuildBattleTop:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDGuildBattleTop:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDGuildBattleTop:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_Info.OnClicked, "on_Btn_ClickArea_Info_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDGuildBattleTop:InitUIView()
end

---组件刷新统一入口
function HUDGuildBattleTop:Refresh(...)
	self:InitData()
	self:RefreshTowerInfo()
	self:RefreshTime()
	self:PlayAnimation(self.view.Ani_Fadein)
end

function HUDGuildBattleTop:InitData()
	local currentSpace = NetworkManager.GetLocalSpace()
	if not currentSpace or currentSpace.WorldType ~= worldConst.WORLD_TYPE.GUILD_LEAGUE then
		return
	end
	self.startTimeStamp = currentSpace.StartTimeStamp + Enum.EGuildLeagueConstIntData.GUILD_LEAGUE_PREPARE_TIME * 1000
	self.endTimeStamp = currentSpace.EndTimeStamp
	self.stage = currentSpace.Stage
end

function HUDGuildBattleTop:RefreshTowerInfo(spawnerID)
	self:RegisterEvent(spawnerID)
	local selfCampID = Game.GuildLeagueSystem:GetSelfCampID()
	local oppositeCampID = Game.GuildLeagueSystem:GetOppositeCampID()
	self:SetCrystalHpPercent(selfCampID)
	self:SetCrystalHpPercent(oppositeCampID)
	self:SetGuildBadgeName(selfCampID)
	self:SetGuildBadgeName(oppositeCampID)
	self:SetTowerCount(selfCampID)
	self:SetTowerCount(oppositeCampID)
	self:SetTotalHpPercent(selfCampID)
	self:SetTotalHpPercent(oppositeCampID)
end

--- 此处为自动生成
function HUDGuildBattleTop:on_Btn_ClickArea_Info_Clicked()
	Game.TipsSystem:ShowTips(Enum.ETipsData.GUILD_LEAGUE_ACTIVITY_DESC, self.view.Btn_ClickArea_Info:GetCachedGeometry())
end

function HUDGuildBattleTop:OnStageChanged(new, old)
	self.stage = new
end

--- 时间Timer
function HUDGuildBattleTop:RefreshTime()
	local leftTime
	if self.stage == const.GUILD_LEAGUE_BATTLE_STAGE then
		leftTime = self.startTimeStamp + (
			Enum.EGuildLeagueConstIntData.GUILD_LEAGUE_TOTAL_TIME - Enum.EGuildLeagueConstIntData.GUILD_LEAGUE_PREPARE_TIME
		) * 1000 - _G._now()
		if leftTime < 0 then leftTime = 0 end
	elseif self.stage == const.GUILD_LEAGUE_PREPARE_STAGE then
		leftTime = self.startTimeStamp - _G._now()
		if leftTime < 0 then leftTime = 0 end
		if leftTime // 1000 > 0 and leftTime // 1000 < 6 then
			Game.NewUIManager:OpenPanel(UIPanelConfig.CountDownPVP_Panel, self.startTimeStamp)
		end
	elseif self.stage == const.GUILD_LEAGUE_OVER_STAGE then
		leftTime = self.endTimeStamp + Enum.EGuildLeagueConstIntData.GUILD_LEAGUE_CAN_KEEP_TIME * 1000 - _G._now()
		if leftTime < 0 then leftTime = 0 end
	end
	self:SetTime(leftTime)
	self:StartTimer('CountDown', function() self:RefreshTime() end, 1000, 1)
end

--- 开始时间戳变化，重新刷新时间
function HUDGuildBattleTop:OnStartTimestampChanged(new, old)
	self.startTimeStamp = new
	self:StopTimer("CountDown")
	self:RefreshTime()
end

--- 设置时间
function HUDGuildBattleTop:SetTime(time)
	self.view.BattleTimer_lua:SetText(Game.TimeUtils.FormatCountDownString(
		time, false))
end

---根据阵营设置水晶血量百分比
function HUDGuildBattleTop:SetCrystalHpPercent(campID)
	local hp = Game.GuildLeagueSystem:GetCrystalHPPercent(campID)
	if Game.GuildLeagueSystem:GetSelfCampID() == campID then
		self.view.Text_HP:SetText(string.format("%.1f%%", hp))
	else
		self.view.Text_RightHP:SetText(string.format("%.1f%%", hp))
	end
end

--- 设置公会名称
function HUDGuildBattleTop:SetGuildBadgeName(campID)
	local guildName = Game.GuildLeagueSystem:GetGuildBadgeName(campID) or ""
	if Game.GuildLeagueSystem:GetSelfCampID() == campID then
		self.view.Text_LeftBadge:SetText(guildName)
	else
		self.view.Text_RightBadge:SetText(guildName)
	end
end

--- 设置塔的数量
function HUDGuildBattleTop:SetTowerCount(campID)
	local count = Game.GuildLeagueSystem:GetAliveTowerNums(campID)
	if Game.GuildLeagueSystem:GetSelfCampID() == campID then
		self.view.Text_Tower:SetText(count)
	else
		self.view.Text_RightTower:SetText(count)
	end
end

--- 设置总血量
function HUDGuildBattleTop:SetTotalHpPercent(campID)
	local totalHp = Game.GuildLeagueSystem:GetTotalHpPercent(campID)
	if Game.GuildLeagueSystem:GetSelfCampID() == campID then
		self.view.Text_TowerHP:SetText(string.format("%.1f%%", totalHp))
	else
		self.view.Text_RightTowerHP:SetText(string.format("%.1f%%", totalHp))
	end
end

--- 水晶血量变化
function HUDGuildBattleTop:RegisterEvent(spawnerID)
	if not spawnerID then
		return
	end
	local eid = Game.GuildLeagueSystem.model.towerEntityIds[spawnerID]
	Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.ON_HP_CHANGED, self, "OnTowerHpChanged", eid)
	if Game.TableData.Get_TowerInfoByInstanceID()[spawnerID].level ~= 4 then
		Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.ON_IS_DEAD_CHANGED, self, "OnTowerDead", eid)
	end
end

function HUDGuildBattleTop:OnTowerHpChanged(entity, new, old)
	if entity and entity.campID then
		self:SetCrystalHpPercent(entity.campID)
		self:SetTotalHpPercent(entity.campID)
	else
		-- 如果没有entity.campID，全刷新一遍
		local selfCampID = Game.GuildLeagueSystem:GetSelfCampID()
		local oppositeCampID = Game.GuildLeagueSystem:GetOppositeCampID()
		self:SetCrystalHpPercent(selfCampID)
		self:SetCrystalHpPercent(oppositeCampID)
		self:SetTotalHpPercent(selfCampID)
		self:SetTotalHpPercent(oppositeCampID)
	end
	self:GetParent():RefreshTowerBarHP(entity.spawnerID)
end

function HUDGuildBattleTop:OnTowerDead(entity, new, old)
	if entity and entity.campID then
		self:SetTowerCount(entity.campID)
	else
		-- 如果没有entity.campID，全刷新一遍
		local selfCampID = Game.GuildLeagueSystem:GetSelfCampID()
		local oppositeCampID = Game.GuildLeagueSystem:GetOppositeCampID()
		self:SetTowerCount(selfCampID)
		self:SetTowerCount(oppositeCampID)
	end
end

return HUDGuildBattleTop
