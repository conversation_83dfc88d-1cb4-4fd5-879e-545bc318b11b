local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local const = kg_require("Shared.Const")
local stringConst = kg_require("Data.Config.StringConst.StringConst")
---@class HUDGuildBattleOccupation_List : UIComponent
---@field view HUDGuildBattleOccupation_ListBlueprint
local HUDGuildBattleOccupation_List = DefineClass("HUDGuildBattleOccupation_List", UIComponent)

HUDGuildBattleOccupation_List.eventBindMap = {
	[EEventTypesV2.ON_MSG_OCCUPY_DETECT_AREA_READY_OPEN] = "OnAltarOpen",
	[EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_PROGRESS_CHANGED] = "OnAltarProgressChanged",
	[EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_STATUS_CHANGED] = "OnAltarStatusChanged",
	[EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_ACTIVATE_CHANGED] = "OnAltarChanged",
	[EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_CAMP_CHANGED] = "OnAltarCampChanged",
	[EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_SHOW_CHANGED] = "OnAltarChanged",
	[EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_INIT_END] = "OnAltarChanged",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDGuildBattleOccupation_List:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDGuildBattleOccupation_List:InitUIData()
	self.altarData = {} -- 祭坛数据
end

--- UI组件初始化，此处为自动生成
function HUDGuildBattleOccupation_List:InitUIComponent()
    ---@type UIListView childScript: HUDGuildBattleOccupationItem
    self.ListViewCom = self:CreateComponent(self.view.ListView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function HUDGuildBattleOccupation_List:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDGuildBattleOccupation_List:InitUIView()
end

---组件刷新统一入口
function HUDGuildBattleOccupation_List:Refresh(...)
	self:OnAltarOpen() --打开面板先刷新是否有祭坛数据，和reindeer
end

-- 祭坛开始时，reminder
function HUDGuildBattleOccupation_List:RefreshAltarOpenReminder()
	local startTimestamp = Game.GuildLeagueSystem:GetAlterOpenTimeStamp()
	if startTimestamp then
		local leftTime = startTimestamp - _G._now()
		if leftTime < Enum.EGuildLeagueConstIntData.GUILD_LEAGUE_BEFORE_OCCUPY_DETECT_AREA_ACTIVE_TIME * 1000 and leftTime > 0 then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.GUILD_LEAGUE_OCCUPY_AREA_START_COUNTDOWN, startTimestamp
			)
		end
	end
end

-- 祭坛刷新
function HUDGuildBattleOccupation_List:RefreshAltar()
	self.altarData = Game.GuildLeagueSystem:GetAltarDataList()
	if next(self.altarData) == nil then
		return
	end
	self.ListViewCom:Refresh(self.altarData)
end

-- 祭坛打开
function HUDGuildBattleOccupation_List:OnAltarOpen()
	self:RefreshAltar()
	self:RefreshAltarOpenReminder()
end

--- 祭坛进度变化
function HUDGuildBattleOccupation_List:OnAltarProgressChanged(instanceID, new, old)
	if next(self.altarData) == nil then
		self.altarData = Game.GuildLeagueSystem:GetAltarDataList()
	end
	for index, altarData in ipairs(self.altarData) do
		if altarData == instanceID then
			local data = Game.GuildLeagueSystem:GetAltarData(instanceID)
			local item = self.ListViewCom:GetItemByIndex(index)
			if item then
				local maxProgress = math.max(Game.TableData.GetOccupyDetectAreaTypeDataRow(data.OccupyDetectAreaType).FullyOccupyValue, 1)
				item:SetHeath(1 - data.OccupyProgress / maxProgress)
			end
			return
		end
	end
end

--- 祭坛状态变化
function HUDGuildBattleOccupation_List:OnAltarStatusChanged(instanceID, new, old)
	if next(self.altarData) == nil then
		self.altarData = Game.GuildLeagueSystem:GetAltarDataList()
	end
	for index, altarData in ipairs(self.altarData) do
		if altarData == instanceID then
			local item = self.ListViewCom:GetItemByIndex(index)
			if item then
				item:SetState(new)
			end
			return
		end
	end
	local data = Game.GuildLeagueSystem:GetAltarData(instanceID)
	if not data then
		return
	end
	if new == const.OCCUPY_DETECT_AREA_STATUS.OCCUPIED then
		local text = stringConst.Get("GUILD_LEAGUE_OCCUPY_DEC")
		if data.OccupyCamp == Game.GuildLeagueSystem:GetSelfCampID() then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.GUILD_LEAGUE_SUCCESS_OCCUPY_AREA_ENEMY,
				{{ text }}
			)
		elseif data.OccupyCamp == Game.GuildLeagueSystem:GetOppositeCampID() then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.GUILD_LEAGUE_SUCCESS_OCCUPY_AREA_ALLY,
				{{ text }}
			)
		end
	end
end

-- 完全刷新祭坛变化
function HUDGuildBattleOccupation_List:OnAltarChanged()
	self.altarData = Game.GuildLeagueSystem:GetAltarDataList()
	self.ListViewCom:Refresh(self.altarData)
end

-- 祭坛阵营变化
function HUDGuildBattleOccupation_List:OnAltarCampChanged(instanceID, new, old)
	if next(self.altarData) == nil then
		self.altarData = Game.GuildLeagueSystem:GetAltarDataList()
	end
	for index, altarData in ipairs(self.altarData) do
		if altarData == instanceID then
			local item = self.ListViewCom:GetItemByIndex(index)
			if item then
				item:SetCamp(new)
			end
			return
		end
	end
end

return HUDGuildBattleOccupation_List
