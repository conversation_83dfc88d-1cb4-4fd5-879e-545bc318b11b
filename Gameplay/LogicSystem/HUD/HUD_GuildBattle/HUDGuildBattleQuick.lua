local ESlateVisibility = import("ESlateVisibility")

local HUDGuildBattleQuickBtn = kg_require("Gameplay.LogicSystem.HUD.HUD_GuildBattle.HUDGuildBattleQuickBtn")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDGuildBattleQuick : UIComponent
---@field view HUDGuildBattleQuickBlueprint
local HUDGuildBattleQuick = DefineClass("HUDGuildBattleQuick", UIComponent)

HUDGuildBattleQuick.eventBindMap = {
	[EEventTypesV2.ON_GUILD_LEAGUE_RESOURCE_CHANGED] = "OnResourceChanged",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDGuildBattleQuick:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDGuildBattleQuick:InitUIData()
	self.resourceCount = 0 -- 资源数量
end

--- UI组件初始化，此处为自动生成
function HUDGuildBattleQuick:InitUIComponent()
    ---@type HUDGuildBattleQuickBtn
    self.WBP_HUDGuildBattleQuickBtn_6_luaCom = self:CreateComponent(self.view.WBP_HUDGuildBattleQuickBtn_6_lua, HUDGuildBattleQuickBtn)
    ---@type HUDGuildBattleQuickBtn
    self.WBP_HUDGuildBattleQuickBtn_5_luaCom = self:CreateComponent(self.view.WBP_HUDGuildBattleQuickBtn_5_lua, HUDGuildBattleQuickBtn)
    ---@type HUDGuildBattleQuickBtn
    self.WBP_HUDGuildBattleQuickBtn_4_luaCom = self:CreateComponent(self.view.WBP_HUDGuildBattleQuickBtn_4_lua, HUDGuildBattleQuickBtn)
    ---@type HUDGuildBattleQuickBtn
    self.WBP_HUDGuildBattleQuickBtn_3_luaCom = self:CreateComponent(self.view.WBP_HUDGuildBattleQuickBtn_3_lua, HUDGuildBattleQuickBtn)
    ---@type HUDGuildBattleQuickBtn
    self.WBP_HUDGuildBattleQuickBtn_2_luaCom = self:CreateComponent(self.view.WBP_HUDGuildBattleQuickBtn_2_lua, HUDGuildBattleQuickBtn)
    ---@type HUDGuildBattleQuickBtn
    self.WBP_HUDGuildBattleQuickBtn_1_luaCom = self:CreateComponent(self.view.WBP_HUDGuildBattleQuickBtn_1_lua, HUDGuildBattleQuickBtn)
end

---UI事件在这里注册，此处为自动生成
function HUDGuildBattleQuick:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDGuildBattleQuick:InitUIView()
	self.commandStrategyWidgetList =
	{
		self.WBP_HUDGuildBattleQuickBtn_1_luaCom,
		self.WBP_HUDGuildBattleQuickBtn_2_luaCom,
		self.WBP_HUDGuildBattleQuickBtn_3_luaCom,
		self.WBP_HUDGuildBattleQuickBtn_4_luaCom,
		self.WBP_HUDGuildBattleQuickBtn_5_luaCom,
		self.WBP_HUDGuildBattleQuickBtn_6_luaCom,
	}
end

---组件刷新统一入口
function HUDGuildBattleQuick:Refresh(...)
	self:SetResourceCount()
	self:RefreshCommandStrategy()
end

--- 设置资源数量 bAnimation 是否需要动画
function HUDGuildBattleQuick:SetResourceCount(bAnimation)
	if bAnimation then
		local count = Game.GuildLeagueSystem:GetCurrentLeagueResource()
		local delta = count - self.resourceCount
		if delta > 0 then
			self.view.Text_Add:SetText("+" .. math.floor(math.abs(delta)))
			self:PlayAnimation(self.view.Ani_Add_Loop)
		else
			self.view.Text_Subtract:SetText("-" .. math.floor(math.abs(delta)))
			self:PlayAnimation(self.view.Ani_Subtract_Loop)
		end
		local showDelta = math.floor(delta / 5)
		local n = 1
		self:StartTimer("ResourceChange", function()
			if n >= 5 then
				self:UpdateLeagueResource()
			else
				self:DynamicRefreshResource(showDelta)
			end
			n = n + 1
		end, 100, 5)
	else
		self:UpdateLeagueResource()
	end
end

function HUDGuildBattleQuick:DynamicRefreshResource(delta)
	self.resourceCount = self.resourceCount + delta
	self.view.KText_ResourceNum_lua:SetText(self.resourceCount)
end

function HUDGuildBattleQuick:UpdateLeagueResource()
	self.resourceCount = Game.GuildLeagueSystem:GetCurrentLeagueResource()
	self.view.KText_ResourceNum_lua:SetText(self.resourceCount)
end

--- 刷新指挥策略
function HUDGuildBattleQuick:RefreshCommandStrategy()
	for i, widget in ipairs(self.commandStrategyWidgetList) do
		local data = Game.GuildLeagueSystem:GetLeagueStrategyDataByIndex(i)
		if data then
			widget:Refresh(data.id, data.iconName)
			widget:SetVisibility(true)
		else
			widget:SetVisibility(false)
		end
	end
end

--- 资源变化事件
function HUDGuildBattleQuick:OnResourceChanged()
	self:SetResourceCount(true)
	-- 刷新指挥策略状态
	for _, widget in ipairs(self.commandStrategyWidgetList) do
		widget:SetState()
	end
end


--- 此处为自动生成
function HUDGuildBattleQuick:on_Btn_ClickArea_lua_Clicked()
	self:SetVisibility(false)
	self:GetParent().quickIsShow = false
end

function HUDGuildBattleQuick:SetVisibility(bVisible)
	self.userWidget:SetVisibility(bVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

return HUDGuildBattleQuick
