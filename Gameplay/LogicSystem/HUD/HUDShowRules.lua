local worldConst = kg_require "Shared.Const.WorldConst"
local const = kg_require("Shared.Const")
-- luacheck: push ignore
--local HUD_Panel = HUD_Panel

local HUDShowRules = DefineClass("HUDShowRules")

-- luacheck: pop
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"

function HUDShowRules:AuctionRule()
    if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_AUCTION", false) then
        local TotalDisappearTime = Game.AuctionManager:GetTotalDisappearTime()
        if TotalDisappearTime and TotalDisappearTime > Game.TimeUtils.GetCurTime() then
            return true
        end
    end
    return false
end

function HUDShowRules:TeamMatchRule()
    if GetMainPlayerPropertySafely("isInTeamMatch") == 1 or GetMainPlayerPropertySafely("isInSingleMatch") == 1 then
        return true
    end
    return false
end

function HUDShowRules:BottomTipsRule()
	if Game.me.ObserverType == Enum.EObserverType.PVP then
		return true
	elseif Game.me.ObserverType == Enum.EObserverType.PVE then
		return true
	elseif Game.me:IsAutoSkill() and Game.me.bAutoSkillSwitchOpen then
		return true
	elseif GetMainPlayerPropertySafely("FollowState") ~= Enum.EFollowState.STOP_FOLLOW then
		return true
	elseif Game.DungeonSystem:IsInCanAutoBattleDungeon() then
		return true
	end
    return false
end

function HUDShowRules:LockRule()
    local LockTargetEntity = SkillImpl.GetLockTargetEntity(ETE.EBSTargetType.TT_Skill)
    if LockTargetEntity then
        return true
    end
    return false
end

function HUDShowRules:StatisticsRule()
    if Game.DungeonBattleStatisticsSystem:IsInDungeon() or (Game.WorldBossSystem:IsInWorldBoss()) then
        return true
    end
    return false
end

function HUDShowRules:ChatRule()
    if Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_CHAT,false) then
        return true
    end
    return false
end

function HUDShowRules:ManorRule()
    return Game.ManorSystem:IsInManorSpace()
end

function HUDShowRules:SpiritBtn()
    return true
end

function HUDShowRules:DungeonAwardAssignmentRule() 
    return Game.DungeonAwardSystem:CheckAssignmentData()
end

function HUDShowRules:DungeonAwardTipsRule()
    return false
end

function HUDShowRules:DungeonAwardAuctionRule()
    return Game.DungeonAwardSystem:CheckAuctionData()
end

function HUDShowRules:AggroListPanelRule()
    return false
end

function HUDShowRules:SideBarRule()
    return true 
end

-- P_HUDBossInfo TraceBossing里面有数据的时候才显示P_HUDBossInfo，否则不显示
function HUDShowRules:BossInfoRule()
    if next(Game.HUDSystem.TracingBoss) == nil then
        return false
    else
        return true
    end
end

function HUDShowRules:CameraRule()
    if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_PHOTOGRAPH", false) then
        return true
    end
    return false
end

function HUDShowRules:PVPStatRule()
    if Game.TeamAreanaSystem:IsInTeamArena() and 
		not Game.TeamAreanaSystem:IsInChampionArena() and 
		not Game.TeamAreanaSystem:IsInChampionPreparationArena() then
        return true
    end
    return false
end

function HUDShowRules:PVPLightLikeRule()
    return Game.TeamAreanaSystem:HasHighLightLike()
end

function HUDShowRules:Arena3V3HUDRule()
    return Game.TeamAreanaSystem:IsIn3v3TeamArena() 
end

function HUDShowRules:Arena5V5HUDRule()
	--return Game.TeamAreanaSystem:IsIn5v5TeamArena()
	return false
end

function HUDShowRules:Arena12V12HUDRule()
	return Game.TeamAreanaSystem:IsIn12v12TeamArena()
end

function HUDShowRules:ArenaPreparationChampionHUDRule()
	return Game.TeamAreanaSystem:IsInChampionPreparationArena()
end

function HUDShowRules:ArenaChampionHUDRule()
	return Game.TeamAreanaSystem:IsInChampionArena()
end

function HUDShowRules:ArenaChampionPreparationRule()
	return Game.TeamAreanaSystem:IsInChampionPreparationArena()
end

function HUDShowRules:PVPMatchTimerTipRule()
    return Game.PVPSystem:CheckMatchTimerTipIsShow() 
end

function  HUDShowRules:PVPReadyTimerTipRule()
    return Game.PVPSystem:CheckReadyTimerTipIsShow()
end

function HUDShowRules:DeathReviveRule()
    return Game.DungeonReviveSystem:CheckIsShowReviveUI() 
end

function HUDShowRules:MedicineRule()
    return true
end

function HUDShowRules:ContinuousDamageCountRule()
    return Game.DamageEffectSystem:CheckContinuousDamageUIIsShow()
end

function HUDShowRules:QuickUsePopupRule()
    if Game.BagSystem:GetItemSelected() then
        return true
    end
    return false
end

function HUDShowRules:BicycleRule()
	return false
end

function HUDShowRules:GMRule()
    return false
end

-- 技能进度条
function HUDShowRules:SkillProgressBarRule()
    return false
end

-- P_HUDExpBar 常显
function HUDShowRules:EXPRule()
    return true
end

-- P_HUDPlayerInfo 常显
function HUDShowRules:PlayerInfoRule()
    return true
end

-- P_HUDBuffList MainCharacter身上有需要CountDown的Buff的时候显示
-- 这里是HUD面板上人物头顶左侧的Buff Panel, Game.PlayerInfoManager:RegisterRefreshBuff(self)这个操作需要先注册Buff刷新回调，得先默认显示上，后面需要迭代一波，根据buff真实触发ShowUI逻辑
function HUDShowRules:BuffListRule()
    -- local BuffData = Game.PlayerInfoManager:GetTargetBuffs(GetMainPlayerCharacter())
    -- for i = 1, #BuffData do
    --     local ownerEntity = Game.EntityManager:getEntity(BuffData[i].Owner:GetEntityUID())
    --     local buffInst = ownerEntity:GetBuffInstanceByBuffInstanceID(BuffData[i].BuffInstID)
    --     if buffInst then
    --         local tableBuffData = buffInst:GetBuffData()
    --         if tableBuffData.IsShowCountDown then
    --             return true
    --         end
    --     end
    -- end
    -- return false

    return true
end

------------------------------任务------------------------------
---HUD轻量任务栏 常显
function HUDShowRules:SimpleQuestInfo()
    return Game.TargetGuideSystem:IsSimpleTaskActice()
end

---已经没有用了
---任务倒计时控件（有新的表现效果待接入）
function HUDShowRules:QuestCountDown()
    return true
end
----------------------------------------------------------------
function HUDShowRules:AsideTalk()
    return false
end

--function HUDShowRules:GuildDanceInviteRule()
--    return Game.GuildDanceSystem:IsInDanceInviteAndMatch()
--end

function HUDShowRules:GuildQuestRule()
    local currentSpace = NetworkManager.GetLocalSpace()
    if not currentSpace or currentSpace.WorldType ~= worldConst.WORLD_TYPE.GUILD_STATION then
        return false
    end
    return Game.ActivitySystem:CheckActivityIsOpen(const.GUILD_ANSWER_ACTIVITY_ID)
end

function HUDShowRules:GuildLeagueRule()
    return Game.GuildLeagueSystem:IsInGuildLeague()
end

function HUDShowRules:GvGRule()
	return Game.GvGSystem:IsInGvG()
end

-- 天气时间按钮 默认显示
function HUDShowRules:WeatherBtnRule()
	return not Game.DungeonBattleStatisticsSystem:IsStatWindowExpanded() or not Game.DungeonBattleStatisticsSystem:CheckStatisticPanelVisible()
end

-- 天气轮盘
function HUDShowRules:WeatherRule()
	return false
end 

--动作按钮 随模块锁显示
function HUDShowRules:SocialActionBtnRule()
	if Game.ModuleLockSystem:CheckModuleUnlockByEnum(Enum.EFunctionInfoData.MODULE_LOCK_SOCIAL_ACTION,false) then
		return true
	end
	return false
end

function HUDShowRules:BossMechanismCoolDown()
	if next(Game.me.BossMechanismCoolDownData) ~= nil then
		return true
	end
	return false
end

-- 分线 默认显示
function HUDShowRules:BranchRule()
	return not Game.DungeonBattleStatisticsSystem:IsStatWindowExpanded() or not Game.DungeonBattleStatisticsSystem:CheckStatisticPanelVisible()
end

--region:先帮jiaqi写着 回头再处理
function HUDShowRules:TraceArrowsRule()
    return false
end

function HUDShowRules:DungeonBtnTimeRule()
    return false
end

function HUDShowRules:TowerLevelHintRule()
    return false
end

function HUDShowRules:TowerInfoRule()
    return false
end

function HUDShowRules:ExitDungeonRule()
    return not Game.GuildLeagueSystem:IsInGuildLeague() and not Game.TeamAreanaSystem:IsInChampionArena()
end

function HUDShowRules:DungeonReadyHUDRule()
	return Game.DungeonSystem:IsDungeonTeamReady()
end

function HUDShowRules:AutoBattleAIRule()
	-- 在单人副本中，队长才显示，不需要支持切换队长
	local bLeader = Game.GroupSystem:IsGroupLeader() or Game.TeamSystem:IsCaptain()
	return Game.DungeonSystem:IsInSingleDungeonWithBot() and bLeader
end

function HUDShowRules:DungeonAutoBattleRule()
	return Game.DungeonSystem:IsInCanAutoBattleDungeon()
end
--endregion

-- 小地图
function HUDShowRules:MiniMapRule()
    return not Game.GuildLeagueSystem:IsInGuildLeague() and
		(not Game.DungeonBattleStatisticsSystem:IsStatWindowExpanded() or not Game.DungeonBattleStatisticsSystem:CheckStatisticPanelVisible())
end

-- 技能面板
function HUDShowRules:SkillRouttleRule()
    return true
end

-- 摇杆、输入底板
function HUDShowRules:ScreenInputRule()
    return true
end

function HUDShowRules:GMPanelRule()
    -- TODO: 测试环境判定 目前无法判断
    return not _G.UE_EDITOR and PlatformUtil.IsWindows()
end

-- 右上角四个图标
function HUDShowRules:TopRightRule()
    return true
end

---ActivityNoticeRule 活动列表按钮显示规则
function HUDShowRules:ActivityNoticeRule()
	if Game.GuildLeagueSystem:IsInGuildLeague() then --在联赛内，不显示
		return false
	end
	local activityList = Game.ActivitySystem:GetShowActivityList()
	return #activityList > 0
end

---DLCDownloadRule 分包下载按钮显示规则
function HUDShowRules:DLCDownloadRule()
	return false
end

function HUDShowRules:ConstantDisplayRule()
	return true
end 
function HUDShowRules:CanShow1v1Countdown()
	return Game.IndividualPVPSystem:CanShow1v1Countdown()
end 

function HUDShowRules:ExploreTopTipRule()
	return false
end

function HUDShowRules:HUDActivityListRule()
	return Game.HUDSystem:GetConstantActive(UICellConfig.HUDActivityList)
end

function HUDShowRules:PVPContinuousKillRule()
	return true
end

return HUDShowRules
