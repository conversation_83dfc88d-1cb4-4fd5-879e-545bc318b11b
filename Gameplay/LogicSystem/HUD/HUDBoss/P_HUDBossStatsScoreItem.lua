---@class P_HUDBossStatsScoreItem : UIComponent
---@field public View WBP_HUDBossStatsScoreItemView
local P_HUDBossStatsScoreItem = DefineClass("P_HUDBossStatsScoreItem", UIComponent)
local worldBossConst = kg_require("Shared.Const.WorldBossConst")

function P_HUDBossStatsScoreItem:OnCreate()
    -- 是否显示背景
    self.isStableList = false
end

function P_HUDBossStatsScoreItem:UpdateUI(data, firstScore)
    if not data or not next(data) then
        return
    end
    
    if data.rankType == worldBossConst.WORLD_BOSS_RANK_TYPE.TEAM then
        self.View.Text_Team:SetText(data.name..Game.TableData.GetWorldBossSettingDataRow("WORLD_BOSS_HUD_TEAM_MARK"))
    else
        self.View.Text_Team:SetText(data.name)
    end
    self.View.Text_Rank:SetText(data.rank)
    self.View.Text_Data:SetText(data.totalDamage)

    self.View:Event_UI_Style(
        math.min(3, data.rank - 1),
        self.isStableList
    )
    self.View.ProgressBar_Score:SetPercent( (firstScore and firstScore>0) and data.totalDamage/firstScore or 0)
end

function P_HUDBossStatsScoreItem:SetSelfData(isStableList)
    self.isStableList = isStableList
end

return P_HUDBossStatsScoreItem
