local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = require "Data.Config.StringConst.StringConst"

---@class HUDTeamQuickTeam_Panel : UIPanel
---@field view HUDTeamQuickTeam_PanelBlueprint
local HUDTeamQuickTeam_Panel = DefineClass("HUDTeamQuickTeam_Panel", UIPanel)
local const = kg_require("Shared.Const")

HUDTeamQuickTeam_Panel.eventBindMap = {
    [EEventTypesV2.ON_TEAMID_CHANGED] = "CloseSelfHUD",
    [EEventTypesV2.ON_MAINPLAYER_GROUP_ID_CHANGED] = "CloseSelfHUD",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDTeamQuickTeam_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDTeamQuickTeam_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUDTeamQuickTeam_Panel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDTeamQuickTeam_Panel:InitUIEvent()
    self:AddUIEvent(self.view.OpenPanel.ClickButton.OnClicked, "on_OpenPanelClickButton_Clicked")
    self:AddUIEvent(self.view.OpenPanel.ClickButton.OnHovered, "on_OpenPanelClickButton_Hovered")
    self:AddUIEvent(self.view.OpenPanel.ClickButton.OnUnhovered, "on_OpenPanelClickButton_Unhovered")
    self:AddUIEvent(self.view.QuickTeam.ClickButton.OnHovered, "on_QuickTeamClickButton_Hovered")
    self:AddUIEvent(self.view.QuickTeam.ClickButton.OnUnhovered, "on_QuickTeamClickButton_Unhovered")
    self:AddUIEvent(self.view.QuickTeam.ClickButton.OnClicked, "on_QuickTeamClickButton_Clicked")
    self:AddUIEvent(self.view.QuickGroup.ClickButton.OnClicked, "on_QuickGroupClickButton_Clicked")
    self:AddUIEvent(self.view.QuickGroup.ClickButton.OnHovered, "on_QuickGroupClickButton_Hovered")
    self:AddUIEvent(self.view.QuickGroup.ClickButton.OnUnhovered, "on_QuickGroupClickButton_Unhovered")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDTeamQuickTeam_Panel:InitUIView()
end

---面板打开的时候触发
function HUDTeamQuickTeam_Panel:OnRefresh(...)
    local posX, posY = ...
    local anchor = import("Anchors")()
    anchor.Minimum = FVector2D(0, 0)
    anchor.Maximum = FVector2D(0, 0)
    self.view.ListContent.Slot:SetAnchors(anchor)
    self.view.ListContent.Slot:SetPosition(FVector2D(posX, posY))
    
    --"创建团队"
    self.view.QuickGroup.Text_Title:SetText(StringConst.Get("GROUP_CREATE_GROUP"))
    self.view.QuickGroup:Event_UI_Style(false, 0)
    
    self.view.QuickTeam:Event_UI_Style(false, 1)
    
    self.view.OpenPanel.Text_Title:SetText("便捷组队")
    self.view.OpenPanel:Event_UI_Style(false, 2)
end


--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_OpenPanelClickButton_Clicked()
    Game.NewUIManager:OpenPanel(UIPanelConfig.TeamQuickUp_Panel)
    self:CloseSelfHUD()
end


--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_QuickGroupClickButton_Clicked()
    Game.GroupSystem:ReqBuildGroup()
    local TargetID = Game.TeamSystem:GetLatestTargetID()
    if TargetID ~= const.DEFAULT_ALL_TARGET_ID and TargetID ~= const.DEFAULT_NO_TARGET_ID then
        Game.me:ReqTeamRefreshTarget(TargetID, nil, 0, "", 1)
    end
    self:CloseSelfHUD()
end


--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_QuickTeamClickButton_Clicked()
    Game.TeamSystem:CreateTeam()
    local TargetID = Game.TeamSystem:GetLatestTargetID()
    if TargetID ~= const.DEFAULT_ALL_TARGET_ID and TargetID ~= const.DEFAULT_NO_TARGET_ID then
        Game.me:ReqTeamRefreshTarget(TargetID, nil, 0, "", 1)
    end
    self:CloseSelfHUD()
end

function HUDTeamQuickTeam_Panel:OnClose()
    UI.Invoke(UIPanelConfig.HUD_Panel, "InvokeChildHUD", UIPanelConfig.P_HUDSideBar, "SelDefaultTab")
end

function HUDTeamQuickTeam_Panel:CloseSelfHUD()
    --self.parentComponent:RemoveChildPanel(self.uid)

    self:CloseSelf()
end


--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_OpenPanelClickButton_Hovered()
    self.view.OpenPanel:Event_UI_Style(true, 2)
end

--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_OpenPanelClickButton_Unhovered()
    self.view.OpenPanel:Event_UI_Style(false, 2)
end

--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_QuickTeamClickButton_Hovered()
    self.view.QuickTeam:Event_UI_Style(true, 1)
end

--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_QuickTeamClickButton_Unhovered()
    self.view.QuickTeam:Event_UI_Style(false, 1)
end

--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_QuickGroupClickButton_Hovered()
    self.view.QuickGroup:Event_UI_Style(true, 1)
end

--- 此处为自动生成
function HUDTeamQuickTeam_Panel:on_QuickGroupClickButton_Unhovered()
    self.view.QuickGroup:Event_UI_Style(false, 1)
end

return HUDTeamQuickTeam_Panel
