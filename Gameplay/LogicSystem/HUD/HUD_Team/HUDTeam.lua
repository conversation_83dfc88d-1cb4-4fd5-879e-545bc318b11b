local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

local ESlateVisibility = import("ESlateVisibility")
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"

---@class HUDTeam : UIComponent
---@field view HUDTeamBlueprint
local HUDTeam = DefineClass("HUDTeam", UIComponent)

HUDTeam.eventBindMap = {
    [_G.EEventTypes.SKILLHUD_COOLDOWNUPDATE] = "OnCoolDownStarted",
    [EEventTypesV2.SERVER_ROLE_OBSERVER_CHANGED] = "OnObserverChange",
    [EEventTypesV2.ON_TEAM_GROUP_BUFF_CHANGED] = "OnBuffChange",
    [EEventTypesV2.ON_TEAMMEMBER_NAME_CHANGED] = "OnTeamMemberPropChange",
    [EEventTypesV2.ON_TEAMMEMBER_LEVEL_CHANGED] = "OnTeamMemberPropChange",
    [EEventTypesV2.ON_PROFESSION_STATE_CHANGED] = "OnProfessionStateChange",
    [EEventTypesV2.ON_TEAMMEMBER_PROFESSION_CHANGED] = "OnTeamMemberPropChange",
    [EEventTypesV2.ON_TEAMMEMBER_ISCAPTAIN_CHANGED] = "OnTeamMemberPropChange",
    [EEventTypesV2.ON_TEAMMEMBER_ISONLINE_CHANGED] = "OnTeamMemberPropChange",
    [EEventTypesV2.ON_TEAMMEMBER_ISDEAD_CHANGED] = "OnTeamMemberDeadChange",
    [EEventTypesV2.ON_TEAMMEMBER_BFOLLOWING_CHANGED] = "OnTeamMemberPropChange",
    [EEventTypesV2.ON_TEAMMEMBER_PROFESSIONSTATEID_CHANGED] = "OnTeamMemberPropChange",
    [EEventTypesV2.ON_SELF_TEAMINFOLIST_CHANGED] = "UpdateTeamInfoListUI",
    [EEventTypesV2.ON_TEAMMEMBER_VOICESTATE_CHANGED] = "OnMemberVoiceChange",
    [EEventTypesV2.ON_TEAMMEMBER_MEMBERFLAG_CHANGED] = "OnMemberMarkChange",
    [EEventTypesV2.ON_TEAMMEMBER_MAPORLINE_CHANGED] = "OnMemberMapLineChange",
    [EEventTypesV2.ON_TEAMMEMBER_LOCATION_CHANGED] = "OnMemberLocationChange",
    [EEventTypesV2.ON_SELF_BLOCK_VOICE_CHANGED] = "OnBlockVoiceChange",
    [EEventTypesV2.BATTLE_LOCKTARGET_CHANGE] = "LockTargetChange",
    [EEventTypesV2.HUD_TARGET_SELECT] = "UI_TargetChange",

    
    [EEventTypesV2.HUD_MARK_STATE_CAHNGED] = "onMarkStateChange",
    [EEventTypesV2.ON_CAN_RESCUE_STATE_CHANGED] = "OnCanRescueChanged",
    [EEventTypesV2.TEAM_ON_CAPTAIN_CHANGED] = "OnCaptainChange",
    [EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED] = "UpdateRescueStatus",
    [EEventTypesV2.GME_REALTIME_AUDIO_CHANGE] = "RefreshOnSpeakingUI",
    [EEventTypesV2.TEAM_ARENA_MEMBER_REVIVE_COUNT_CHANGE] = "OnPVPChampionReviveTimeChanged",
    [EEventTypesV2.ON_UIAPPEARENCE_TEAM_NAMEPLATE_CHANGED] = "OnSelfTeamFrameChanged",
}


--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDTeam:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDTeam:InitUIData()
    self.PanelData = {}         -- 面板数据
    self.TeamReviveCDTime = nil -- 复活计时器
    self.RebirthSkillID = -1
end

--- UI组件初始化，此处为自动生成
function HUDTeam:InitUIComponent()
    ---@type UIListView
    self.TeamViewListCom = self:CreateComponent(self.view.TeamViewList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function HUDTeam:InitUIEvent()
    self:AddUIEvent(self.view.WBP_HUDTeamAdd.Btn_ClickArea_Invite.OnClicked, "on_WBP_HUDTeamAddBtn_ClickArea_Invite_Clicked")
    self:AddUIEvent(self.view.WBP_HUDTeamAdd.Btn_ClickArea_Invite.OnHovered, "OnHoverInviteButton")
    self:AddUIEvent(self.view.WBP_HUDTeamAdd.Btn_ClickArea_Invite.OnUnhovered, "OnUnhoveredInviteButton")
    self:AddUIEvent(self.view.WBP_HUDTeamAdd.Btn_ClickArea_Quick.OnClicked, "on_WBP_HUDTeamAddBtn_ClickArea_Quick_Clicked")
    self:AddUIEvent(self.view.WBP_HUDTeamAdd.WBP_HUDHelpBtn.Btn_Help_lua.OnClicked, "on_WBP_HUDTeamAddWBP_HUDHelpBtnBtn_Help_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDTeam:InitUIView()
    self.TeamMemberSelectIndex = 0
    self:OnUnhoveredInviteButton()
end

function HUDTeam:StartOnIdle()
    if self.tickTimer then
        self:StopTimer(self.tickTimer)
    end
    self.tickTimer = self:StartTickTimer("HUD_Team Update",function(deltaTime)
        self:OnIdle(deltaTime)
    end, -1)
end

---组件刷新统一入口
function HUDTeam:Refresh(...)
    self:OnRefresh()
end

function HUDTeam:OnRefresh()
    local CurrReviveSkillID = Game.me:GetReviveSkillID()
    if Game.TableData.GetSkillDataNewRow(CurrReviveSkillID) then
        self.RebirthSkillID = CurrReviveSkillID
    end
    self:UpdateTeamInfoListUI()
    self:UpdateRescueStatus()
    self:StartOnIdle()
end

function HUDTeam:OnClose()
    if self.TeamReviveCDTime then
        Game.TimerManager:StopTimerAndKill(self.TeamReviveCDTime)
        self.TeamReviveCDTime = nil
    end
end

---刷新TeamInfoUI内容 start---
function HUDTeam:UpdateTeamInfoListUI()
    local TeamData = Game.TeamSystem:GetTeamMembers()
    table.clear(self.PanelData)
    local CaptainInfo = nil
    if TeamData then
        for K, V in pairs(TeamData) do
            if V.isCaptain ~= 1 then
                table.insert(self.PanelData, { 
                    MemberID = K, 
                    EnterTime = V.enterTime,
                    RebirthSkillID = self.RebirthSkillID,
                    Selected = false
                })
            else
                CaptainInfo = { 
                    MemberID = K, 
                    EnterTime = V.enterTime,
                    RebirthSkillID = self.RebirthSkillID,
                    Selected = false
                }
            end
        end
    end
    table.sort(
            self.PanelData,
            function(a, b)
                return a.EnterTime < b.EnterTime
            end
    )
    if CaptainInfo ~= nil then
        table.insert(self.PanelData, 1, CaptainInfo)
    end
    self.TeamViewListCom:Refresh(self.PanelData)
    if Game.TeamSystem:GetMarkState() == Enum.EMARK.TeamGroup then
        self.TeamViewListCom:SetSelectedItemByIndex(1, true, true)
    end
    if #self.PanelData < Game.TeamSystem:GetTeamNumLimit() then
        self.view.HB_NotFull:SetVisibility(ESlateVisibility.selfHitTestInvisible)
    else
        self.view.HB_NotFull:SetVisibility(ESlateVisibility.Collapsed)
    end
    --初始化检查复活CD
    if self.RebirthSkillID ~= -1 and SkillImpl.checkSkillInCoolDown(self.RebirthSkillID) then
        self:OnCoolDownStarted(self.RebirthSkillID)
    end
end

---@return HUDTeamMemberItem 
function HUDTeam:GetMemberItemByEid(eid)
    for i = 1, #self.PanelData do
        if self.PanelData[i].MemberID == eid then
            local teamMemWidget = self.TeamViewListCom:GetItemByIndex(i)
            return teamMemWidget
        end
    end
    return nil
end

function HUDTeam:UpdateSelEID(eid)
    if not self.TeamViewListCom then
        return
    end
    if eid then
        local Index = -1
        if self.PanelData and eid then
            for key, value in ipairs(self.PanelData) do
                if value.MemberID == eid then
                    Index = key
                    break
                end
            end
        end
        self:SetSelectedItemByIndex(Index)
    else
        self:SetSelectedItemByIndex(-1)
    end
end

function HUDTeam:SetSelectedItemByIndex(index)
    self.TeamMemberSelectIndex = index

    for i = 1, #self.PanelData do
        local isSelected = i == index
        if self.PanelData[i].Selected ~= isSelected then
            self.PanelData[i].Selected = isSelected
            self.TeamViewListCom:RefreshItemByIndex(i)
        end
    end
end


---刷新TeamInfoUI内容 end---
function HUDTeam:LockTargetChange(InTargetType, targetUid)
    self:UI_TargetChange(targetUid)
end

function HUDTeam:UI_TargetChange(targetUid)
    if targetUid == nil then
        self:UpdateSelEID(nil)
        return
    end
    --3c战斗锁定
    local entity = Game.EntityManager:getEntity(targetUid)
    local EID = nil
    if entity ~= nil then
        if Game.TeamSystem:IsTeamMember(entity.eid) then
            EID = entity.eid
        end
    end
    self:UpdateSelEID(EID)
end


function HUDTeam:onMarkStateChange()
    if Game.TeamSystem:GetMarkState() == Enum.EMARK.None then
        self:UpdateSelEID()
    elseif Game.TeamSystem:GetMarkState() == Enum.EMARK.TeamGroup then
        self:UpdateSelEID(Game.TeamSystem:GetCaptainInfo().id)
    end
    for i = 1, #self.PanelData do
        local teamMemWidget = self.TeamViewListCom:GetItemByIndex(i)
        if teamMemWidget then
            teamMemWidget:RefreshMarkStateUI()
        end
    end

end

---@param param DamageContext
---收到攻击的血条表现
function HUDTeam:OnTakeDamage(param)
    if param.InstigatorID ~= Game.me.eid or not param.IsHeal then
        return
    end
    ---todo 这里原来通过技能表判断是伤害还是治疗
    local memberItem = self:GetMemberItemByEid(param.DefenderID)
    if memberItem then
        local teamMemWidget = memberItem.userWidget
        self:PlayAnimation(teamMemWidget.Ani_Cure, nil, teamMemWidget, 0.0, 1)
    end
end

function HUDTeam:OnIdle(DeltaTime)
    for key, value in pairs(self.PanelData) do
        local item = self.TeamViewListCom:GetItemByIndex(key)
        if item then
            item:OnIdle(DeltaTime * 1000)
        end
    end
end

function HUDTeam:OnTeamMemberPropChange(propname, changeEID, newValue, oldValue)
    local memberItem = self:GetMemberItemByEid(changeEID)
    if memberItem then
        memberItem:Refresh(Game.TeamSystem:GetTeamMember(changeEID), self.TeamMemberSelectIndex == memberItem.index, self.RebirthSkillID)
    end
end

-- 组队底框目前先支持自己的底框实时更新
function HUDTeam:OnSelfTeamFrameChanged(old, new)
    local memberItem = self:GetMemberItemByEid(Game.me.eid)
    if memberItem then
        memberItem:LoadTeamFrame(Game.FashionSystem:GetPlayerTeamNameplateRes(new))
    end
end

function HUDTeam:OnProfessionStateChange(eid)
    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        memberItem:OnProfessionStateChange()
    end
end

---@param isStart boolean
---@param buff Buff
function HUDTeam:OnBuffChange(isStart, buff)
    if isStart then
        self:OnBuffAdd(buff)
    else
        self:OnBuffEnd(buff)
    end
end

function HUDTeam:OnBuffAdd(buff)
    local memberItem = self:GetMemberItemByEid(buff.finalOwnerID)
    if memberItem then
        memberItem:OnBuffAdd(buff)
    end
end

function HUDTeam:OnBuffEnd(buff)
    local memberItem = self:GetMemberItemByEid(buff.finalOwnerID)
    if memberItem then
        memberItem:OnBuffEnd(buff)
    end
end

function HUDTeam:OnMemberVoiceChange(_, eid, newValue, oldValue)
    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        memberItem:RefreshVoiceUI()
    end
end

function HUDTeam:OnMemberMarkChange(_, eid, newValue, oldValue)
    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        memberItem:RefreshMarkIconUI()
    end
end

function HUDTeam:OnMemberMapLineChange(_, eid, newValue, oldValue)
    if eid == Game.me.eid then
        --- 自己切场景 所有成员刷新
        self:OnRefresh()
        return
    end
    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        local selected = self.TeamMemberSelectIndex == memberItem.index
        memberItem:Refresh(Game.TeamSystem:GetTeamMember(eid), selected, self.RebirthSkillID)
    end
end

function HUDTeam:OnMemberLocationChange(_, eid, newValue, oldValue)
    for i = 1, #self.PanelData do
        if self.PanelData[i].MemberID == eid then
            self.PanelData[i].location = newValue
            break
        end
    end

    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        memberItem.data.location = newValue
        memberItem:RefreshLocationUI()
    end
end

function HUDTeam:OnTeamMemberDeadChange(_, eid, newValue, oldValue)
    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        if self.RebirthSkillID == -1 then 
            memberItem:RefreshDeadUI(newValue)
        else
            if eid == Game.me.eid then 
                local state = Game.TeamSystem:GetTeamMember(eid).isDead
                memberItem:RefreshDeadUI(state)
            else 
                memberItem:RefreshDeadUI(newValue)
            end
        end
    end
end

function HUDTeam:EnterAoi(eid)
    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        memberItem:RefreshAOIUI(true)
    end
end

function HUDTeam:LeaveAoi(eid)
    local memberItem = self:GetMemberItemByEid(eid)
    if memberItem then
        memberItem:RefreshAOIUI(false)
    end
end

function HUDTeam:OnCoolDownStarted(SkillID)
    if SkillID and SkillID ~= -1 and SkillID == self.RebirthSkillID then
        self:StopTimer("ReviveCDTimer")
        for i = 1, #self.PanelData do
            --进入CD动画
            local item = self.TeamViewListCom:GetItemByIndex(i)
            if item then
                local teamMemWidget = item.userWidget
                if teamMemWidget and self.PanelData[i] and Game.me.teamInfoList[self.PanelData[i].MemberID].isDead then
                    self:StopAnimation(teamMemWidget.Ani_Rebirth_Loop, teamMemWidget)
                    self:PlayAnimation(teamMemWidget.Ani_RebirthCD, nil, teamMemWidget, 0.0, 1)
                end
            end
        end
        local CoolDownMsg = SkillImpl.getSkillCoolDownMessage(self.RebirthSkillID)
        local CD = CoolDownMsg.CurrentCoolDown
        self:StartTimer(
                "ReviveCDTimer",
                function()
                    if CD < 0 then
                        self:StopTimer("ReviveCDTimer")
                        return
                    else
                        for i = 1, #self.PanelData do
                            local item = self.TeamViewListCom:GetItemByIndex(i)
                            if item then
                                item:UpdateReviveUI(CD)
                            end
                        end
                        CD = CD - 0.5
                    end
                end,
                500, -1, nil, true
        )
    end
end

------其余回调注册start------
function HUDTeam:OnRoleVisibilityChanged(_, uid, bVisible)
    local entity = Game.EntityManager:getEntity(uid)
    if entity then
        if Game.TeamSystem:IsTeamMember(entity.eid) then
            self:UpdateTeamInfoListUI()
        end
    end
end

function HUDTeam:OnObserverChange(AvatarActorEID, NewType)
    local curSpace = Game.NetworkManager.GetLocalSpace()
    if not curSpace then
        return
    end

    if curSpace:IsDungeonSpace() or curSpace:IsTeamPVPSpace() then
        local memberItem = self:GetMemberItemByEid(AvatarActorEID)
        if memberItem then
            if NewType ~= Enum.EObserverType.NONE then
                memberItem.userWidget:SetOB(true)
                memberItem.bObserver = true
            else
                memberItem.userWidget:SetOB(false)
                memberItem.bObserver = false
            end
        end
    end
end

function HUDTeam:UpdateRescueStatus()
    local bCanSeek = false
    if Game.NetworkManager.GetLocalSpace() then
        bCanSeek = Game.NetworkManager.GetLocalSpace().bCanSeekRescue
    end
    self:OnCanRescueChanged(Game.me.eid, "bCanSeekRescue", bCanSeek, nil)
end

function HUDTeam:OnCaptainChange()
    self:UpdateRescueStatus()
end

function HUDTeam:OnShow()
    self:UpdateRescueStatus()
end

function HUDTeam:OnOpen()
    self:OnRefresh()
end

function HUDTeam:OnBlockVoiceChange(EID, NewValue)
    local memberItem = self:GetMemberItemByEid(EID)
    if memberItem then
        memberItem:RefreshVoiceUI()
    end
end


function HUDTeam:OnCanRescueChanged(eid,name,newvalue,oldvalue)
    if newvalue == true and Game.TeamSystem:IsCaptain() == true then
        self.view.WBP_HUDTeamAdd.WBP_HUDHelpBtn:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.view.WBP_HUDTeamAdd.CP_QuickBtn:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.view.WBP_HUDTeamAdd.WBP_HUDHelpBtn:SetVisibility(ESlateVisibility.Collapsed)
        self.view.WBP_HUDTeamAdd.CP_QuickBtn:SetVisibility(ESlateVisibility.selfHitTestInvisible)
    end
end

------其余回调注册end------
function HUDTeam:OnClick_WBP_HUDTeamAdd_Btn_Invite()
    Game.TabClose:AttachPanel(UIPanelConfig.TeamInvite_Panel, Enum.EUIBlockPolicy.UnblockOutsideBoundsExcludeRegions, self.view.WBP_HUDTeamAdd.Btn_Invite)
    Game.AkAudioManager:PostEvent2D(Enum.EUIAudioEvent.Play_UI_Common, true)
    Game.NewUIManager:OpenPanel(UIPanelConfig.TeamInvite_Panel)
end

function HUDTeam:OnClick_WBP_QuickTeamUp_Button()
    Game.NewUIManager:OpenPanel(UIPanelConfig.TeamQuickUp_Panel)
end

function HUDTeam:OnHoverInviteButton()
    self.view.WBP_HUDTeamAdd.img_hover:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function HUDTeam:OnUnhoveredInviteButton()
    self.view.WBP_HUDTeamAdd.img_hover:SetVisibility(ESlateVisibility.Collapsed)
end

function HUDTeam:OnClick_WBP_HUDTeamAdd_WBP_HUDHelpBtn_Btn_Help()
    --求援
     Game.NewUIManager:OpenPanel(UIPanelConfig.TeamGroupShout_Panel)
end

--- 此处为自动生成
function HUDTeam:on_WBP_HUDTeamAddBtn_ClickArea_Invite_Clicked()
    self:OnClick_WBP_HUDTeamAdd_Btn_Invite()
end

--- 此处为自动生成
function HUDTeam:on_WBP_HUDTeamAddBtn_ClickArea_Quick_Clicked()
    self:OnClick_WBP_QuickTeamUp_Button()
end

--- 此处为自动生成
function HUDTeam:on_WBP_HUDTeamAddWBP_HUDHelpBtnBtn_Help_lua_Clicked()
    self:OnClick_WBP_HUDTeamAdd_WBP_HUDHelpBtn_Btn_Help()
end

function HUDTeam:RefreshOnSpeakingUI(bShow, userId)
    local memberItem = self:GetMemberItemByEid(userId)
    if memberItem then
        memberItem:RefreshOnSpeakingUI(bShow)
    end
end

function HUDTeam:OnPVPChampionReviveTimeChanged()
    for i = 1, #self.PanelData do
        local item = self.TeamViewListCom:GetItemByIndex(i)
        if item then
            item:OnPVPChampionReviveTimeChanged()
        end
    end
end

return HUDTeam
