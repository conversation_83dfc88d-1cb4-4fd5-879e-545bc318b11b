local PlayerHPBar = kg_require("Gameplay.LogicSystem.HUD.HUD_PlayerInfo.PlayerHPBar")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class HUDTeamMemberItem : UIListItem
---@field view HUDTeamMemberItemBlueprint
local HUDTeamMemberItem = DefineClass("HUDTeamMemberItem", UIListItem)
local StringConst = kg_require("Data.Config.StringConst.StringConst")


local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")

local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"
local CommonBarHelper = kg_require "Gameplay.LogicSystem.CommonUI.CommonBarHelper"
local HUDSystem = kg_require "Gameplay.LogicSystem.HUD.HUDSystem"
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local KismetMathLibrary = import("KismetMathLibrary")

HUDTeamMemberItem.eventBindMap = {
	[EEventTypesV2.OTHER_PLAYERS_CHARACTER_BATTLE_APPEARANCE_CHANGED] = "RefreshBarColor",
	[EEventTypesV2.PLAYER_CHARACTER_BATTLE_APPEARANCE_CHANGED] = "RefreshBarColor",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDTeamMemberItem:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDTeamMemberItem:InitUIData()
    self.ReviveMsgTime = 0
    self.TeamReviveCDTime = nil
    ---@type AvatarActorTeamInfo
    self.Data = nil
    self.bObserver = false
    self.CommonBarHelper = CommonBarHelper.new({
        IncreaseRate = Game.TableData.GetConstDataRow("HP_BAR_INCREASE_RATE"),
        DMGReduceRate = Game.TableData.GetConstDataRow("HP_BAR_DMG_REDUCE_RATE"),
        DMGReduceDelay = Game.TableData.GetConstDataRow("HP_BAR_DMG_REDUCE_DELAY")
    })
    self.CommonBarHelper:BindWidget(self.view.WBP_PlayerHPBar, self.view.WBP_PlayerHPBar.PB_HPMain,
            self.view.WBP_PlayerHPBar.PB_Shield,
            self.view.WBP_PlayerHPBar.PB_Damage, self.view.WBP_PlayerHPBar.PB_HPLock)
    
    self.TeamIconBuffIDs = {}
    self.BuffIDMap = {}
    self.ProfessionStateList = {}
    self.view.Image_Buff:SetVisibility(ESlateVisibility.Hidden)
    self.CanRebirth = false
    self.LastDistanceShow = 0

    -- 组队底框
    self.loadedFrameCom = nil
    -- 组队动效
    self.loadedAniCom = nil
end

--- UI组件初始化，此处为自动生成
function HUDTeamMemberItem:InitUIComponent()
    ---@type PlayerHPBar
    self.WBP_PlayerHPBarCom = self:CreateComponent(self.view.WBP_PlayerHPBar, PlayerHPBar)
end

---UI事件在这里注册，此处为自动生成
function HUDTeamMemberItem:InitUIEvent()
    self:AddUIEvent(self.view.Btn.OnClicked, "on_Btn_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDTeamMemberItem:InitUIView()
end

---面板打开的时候触发
function HUDTeamMemberItem:OnRefresh(itemData)
    local Data = Game.TeamSystem:GetTeamMember(itemData.MemberID)
    self:Refresh(Data, itemData.Selected, itemData.RebirthSkillID)
    if itemData.EnterTime and _G._now(1) - itemData.EnterTime < 1 then
        self:LoadTeamVx(Game.FashionSystem:GetPlayerTeamJoinAnimationRes(Data.teamJoinAnimation))
    end
end

function HUDTeamMemberItem:OnClose()
    UIBase.OnClose(self)
    self.ReviveMsgTime = 0
    if self.TeamReviveCDTime then
        Game.TimerManager:StopTimerAndKill(self.TeamReviveCDTime)
        self.TeamReviveCDTime = nil
    end
    -- 复活
    self.view.Text_Rebirth:SetText(StringConst.Get("HUD_REVIVE"))
    if self.CommonBarHelper then
        self.CommonBarHelper:UnBindEntity()
    end
end

function HUDTeamMemberItem:OnIdle(DeltaTime)
    if self.CommonBarHelper then
        -- self.CommonBarHelper:SetHP(Game.me.teamInfoList[self.Data.id].hp)
        self.CommonBarHelper:Tick(DeltaTime)
    end
    if not self.bInAOI then
        self:RefreshLocationUI()
    end
end

function HUDTeamMemberItem:UpdateReviveUI(CD)
    if self.Data and self.bSelf then
        return
    end
    if math.floor(CD * 10 // 10) > 0 then
        if self.Data and self.Data.isDead and CD >= 1 then
            self.view.Text_Rebirth:SetText(string.format("%.f s", CD))
        end
    else
        if self.Data and self.Data.isDead then
            self.view.Text_Rebirth:SetText(StringConst.Get("HUD_REVIVE")) -- 复活
            local WidgetRoot = self.userWidget
            WidgetRoot:SetRebirth(true, false)
            self:StopAnimation(self.userWidget.Ani_RebirthCD, self.userWidget)

            self:PlayAnimation(self.userWidget.Ani_Rebirth, function() self:PlayAnimationRebirthLoop()  end , self.userWidget, 0.0, 1,
                    EUMGSequencePlayMode.Forward, 1, false)
        end
    end
end

function HUDTeamMemberItem:PlayAnimationRebirthLoop()
    self:PlayAnimation( self.userWidget.Ani_Rebirth_Loop, nil, self.userWidget, 0.0, 0,
            EUMGSequencePlayMode.Forward, 1, false)
end

function HUDTeamMemberItem:Click_Btn()
    if self.Data then
        local entity = Game.EntityManager:getEntity(self.Data.id)
        if entity then
            Game.TargetLockSystem.ForceChangeLockTarget(self.Data.id)
        else
            Game.TargetLockSystem.ForceChangeLockTarget(nil)
        end
        
        if not UI.IsShow("P_LockTeammate") then
            HUDSystem:ShowUI("P_LockTeammate", self.Data.id, (self.Data.botID ~= nil and self.Data.botID ~= 0))
        else
            UI.Invoke("P_LockTeammate", "TeamLocktargetChange", self.Data.id, (self.Data.botID ~= nil and self.Data.botID ~= 0))
        end
    
        if self.Selected == false then
            self:GetParent():GetParent():SetSelectedItemByIndex(self.index)
        end
    
        if Game.TeamSystem:GetMarkState() == Enum.EMARK.TeamGroup then
            UI.Invoke(UICellConfig.HUD_TeamMemberMarkMenu, "UpdateSelID", self.Data.id)
        end
    
        self:ClickToRebirth()
        self:ClickToChangeAI()
    end
end

--- 点击切换AI 切换模式
function HUDTeamMemberItem:ClickToChangeAI()
    if self.Data.botID == nil or self.Data.botID == 0 then
        if UI.IsShow(UIPanelConfig.HUD_TeamAIChange_Panel) then
            UI.HideUI(UIPanelConfig.HUD_TeamAIChange_Panel)
        end
        return
    end
    
    if Game.TeamSystem:IsCaptain() and Game.DungeonSystem:IsInSingleDungeonWithBot() then
        local params = {}
        params.id = self.Data.id
        params.profession = self.Data.profession
        params.btnSize = SlateBlueprintLibrary.GetLocalSize(self.view.Btn:GetCachedGeometry())
        local _, ViewportPosition =
        SlateBlueprintLibrary.LocalToViewport(
                _G.GetContextObject(),
                self.view.Btn:GetCachedGeometry(),
                FVector2D(0, 0),
                nil,
                nil
        )
        params.pos = ViewportPosition
        if not UI.IsShow(UIPanelConfig.HUD_TeamAIChange_Panel) then
            Game.HUDSystem:ShowUI(UIPanelConfig.HUD_TeamAIChange_Panel, params)
        else
            UI.Invoke(UIPanelConfig.HUD_TeamAIChange_Panel, "UpdateClassChange", params)
        end
    end
end



function HUDTeamMemberItem:OnProfessionStateChange()
    if #self.ProfessionStateList ~= 2 then
        self.view.Image_ProfessionState:SetVisibility(ESlateVisibility.Hidden)
        return
    end
    if self.Data and self.Data.id then
        local professionStateData = Game.TableData.GetProfessionStateDataRow(tonumber(self.ProfessionStateList[self.Data.professionStateID]))
        self.view.Image_ProfessionState:SetVisibility(ESlateVisibility.Visible)
        self:SetImage(self.view.Image_ProfessionState, professionStateData.StateIcon)
    end
end

function HUDTeamMemberItem:OnBuffAdd(buff)
    local buffData = buff:GetBuffData()
    if self.BuffIDMap[buffData.ID] then
        return
    end
    self.BuffIDMap[buffData.ID] = true
    if not string.isEmpty(buffData.TeamIcon) then
        table.insert(self.TeamIconBuffIDs, buffData.ID)
        self:SetImage(self.view.Image_Buff, buffData.TeamIcon)
        self.view.Image_Buff:SetVisibility(ESlateVisibility.Visible)
    end
end

function HUDTeamMemberItem:OnBuffEnd(buff)
    local buffData = buff:GetBuffData()
    self.BuffIDMap[buffData.ID] = nil

    if not string.isEmpty(buffData.TeamIcon) then
        for index,v in ipairs(self.TeamIconBuffIDs) do
            if buffData.ID == v then
                table.remove(self.TeamIconBuffIDs, index)
                if #self.TeamIconBuffIDs > 0 then
                    buffData = Game.TableData.GetBuffDataNewRow(self.TeamIconBuffIDs[#self.TeamIconBuffIDs])
                    self:SetImage(self.view.Image_Buff, buffData.TeamIcon)
                else
                    self.view.Image_Buff:SetVisibility(ESlateVisibility.Hidden)
                end
                break
            end
        end
    end
end

function HUDTeamMemberItem:Refresh(Data, Selected, RebirthID)
    local WidgetRoot = self.userWidget
    self.Data = Data
    self.Selected = Selected
    self.RebirthID = RebirthID
    self.bSelf = false
    if self.bObserver then
        Selected = false
    end
    if Data then
        self.bSelf = Data.id == Game.me.eid
        --是否在线
        self.IsOnline = math.floor(Data.isOnline) == 1 and true or false
        --地图、AOI
        self:RefreshLocUI()
        --是否是队长
        local IsLeader = Data.isCaptain == 1 and true or false
        self.userWidget:SetFollow(Data.bFollowing)
        self.userWidget:SetOffline(not self.IsOnline)
        self.userWidget:SetLeader(IsLeader)
        self.userWidget:SetOB(self.bObserver)
        self.userWidget:SetAggro(Data.bTargetByBoss)
        self:RefreshSelUI(Selected)
        local ClassInfoData = Game.TableData.GetPlayerSocialDisplayDataTable()
        if ClassInfoData[Data.profession] then
            local HeadIcon = ClassInfoData[Data.profession][0].HeadIcon
            self:SetImage(self.view.Img_HeadIcon, HeadIcon)
            self.view.Img_HeadIcon:SetVisibility(ESlateVisibility.Visible)
        else
            self.view.Img_HeadIcon:SetVisibility(ESlateVisibility.Hidden)
        end
        --等级
        self.view.Text_Level:SetText(Data.level)

        if Data.botID ~= nil and Data.botID ~= 0 then
            local botTableData = Game.TableData.GetBattleBotTemplateDataRow(Data.botID)
            self.userWidget:SetText(botTableData.Name)
        else
            self.userWidget:SetText(Data.name)
        end

        --血条
        self.CommonBarHelper:BindEntity(Data.id, "Hp", "MaxHp", "CurrentMaxHp", "aShield")
        self.CommonBarHelper:TintCampColor()
        --动画
        if Data and Data.bTargetByBoss then
            self:PlayAnimation(self.userWidget.Ani_Hate_Loop, nil, self.userWidget, 0.0, 0,
                    EUMGSequencePlayMode.Forward,
                    1,
                    false)
        else
            self:StopAnimation(self.userWidget.Ani_Hate_Loop)
        end
        self:RefreshMarkIconUI()
        self:RefreshMarkStateUI()
        self:RefreshVoiceUI()
        self:RefreshDeadUI(Data.isDead)
        self:StopSpeakingAnim()
        self.ProfessionStateList = Game.SkillCustomSystem:GetProfessionStatesByID(self.Data.profession)
        if #self.ProfessionStateList == 2 then
            self.view.Image_ProfessionState:SetVisibility(ESlateVisibility.Visible)
            local professionStateData = Game.TableData.GetProfessionStateDataRow(tonumber(self.ProfessionStateList[self.Data.professionStateID]))
            self:SetImage(self.view.Image_ProfessionState, professionStateData.StateIcon)
        else
            self.view.Image_ProfessionState:SetVisibility(ESlateVisibility.Hidden)
        end
        self:LoadTeamFrame(Game.FashionSystem:GetPlayerTeamNameplateRes(Data.teamNameplate))
    else
        WidgetRoot:SetOB(false)
        self:RefreshSelUI(false)
    end
end

function HUDTeamMemberItem:LoadTeamFrame(res)
    if not res then return end
    if self.loadedFrameCom then
        self:RemoveComponent(self.loadedFrameCom)
        self.loadedFrameCom = nil
    end
    self:AsyncLoadComponent(UICellConfig.UIAppearance_TeamFrame, self.view.Canvas_Frame,
            function(component) 
                self.loadedFrameCom = component 
                self.loadedFrameCom:Refresh(res, Game.GroupSystem.EHUDProMap[self.Data.profession])
            end)
end

function HUDTeamMemberItem:LoadTeamVx(res)
    if not res then return end
    if self.loadedAniCom then
        self:RemoveComponent(self.loadedAniCom)
        self.loadedAniCom = nil
    end
    self:AsyncLoadComponent(UICellConfig.UIAppearance_TeamMistVx, self.view.Canvas_Frame,
            function(component) 
                self.loadedAniCom = component
                self.loadedAniCom:Refresh(res)
            end)
end

function HUDTeamMemberItem:RefreshMarkIconUI()
    local MarkID = Game.TeamSystem:GetTeamMemberMarkID(self.Data.id)
    if MarkID ~= -1 then
        self.view.Img_MarkIcon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData["TEAM_MARK_MEMBER"..MarkID])
        self:SetImage(self.view.Img_MarkIcon, iconData.AssetPath)
    else
        self.view.Img_MarkIcon:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function HUDTeamMemberItem:RefreshMarkStateUI()
    if Game.TeamSystem:GetMarkState() == Enum.EMARK.None then
        self.view.CP_Voice:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:RefreshLocUI()
    elseif Game.TeamSystem:GetMarkState() == Enum.EMARK.TeamGroup then
        self.view.CP_Voice:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Text_Map:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function HUDTeamMemberItem:RefreshOnSpeakingUI(bShow)
    if bShow then
        self:SetSpeakingVisibility(ESlateVisibility.SelfHitTestInvisible)
        -- loops = 0 表示无限循环
        self:PlayAnimation(
                self.userWidget.Ani_Voice, 
                function()
                    self:SetSpeakingVisibility(ESlateVisibility.Collapsed)
                end,
                self.userWidget,
                1, 
                0
        )
    else
        self:StopSpeakingAnim()
    end
end

function HUDTeamMemberItem:StopSpeakingAnim()
    self:StopAnimation(self.userWidget.Ani_Voice, self.userWidget)
    self:SetSpeakingVisibility(ESlateVisibility.Collapsed)
end

function HUDTeamMemberItem:SetSpeakingVisibility(state)
    -- 动画里是ESlateVisibility 所以这里用透明度， 这样不会冲突
    local opacity = 0
    if state == ESlateVisibility.SelfHitTestInvisible then
        opacity = 1
    end
    self.view.Img_ChatVoice00:SetRenderOpacity(opacity)
    self.view.Img_ChatVoice01:SetRenderOpacity(opacity)
    self.view.Img_ChatVoice02:SetRenderOpacity(opacity)
    self.view.Img_ChatVoice03:SetRenderOpacity(opacity)
end

function HUDTeamMemberItem:RefreshAOIUI(bInAOI)
    self.bInAOI = bInAOI
    self.userWidget:SetOutOfRange(not self.bInAOI)
    if bInAOI then
        self.view.Text_Map:SetVisibility(ESlateVisibility.Collapsed)
    else
        if self.IsOnline and self.bInSameMap and self.bInSameLine then
            self.view.Text_Map:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self:RefreshLocationUI()
        end
    end
end

function HUDTeamMemberItem:RefreshLocUI()
    if Game.me == nil then
        return
    end
    self.view.Text_Map:SetVisibility(ESlateVisibility.Collapsed)
    if self.bSelf then
        self.userWidget:SetOutOfRange(false)
    elseif self.IsOnline == false then
        self.userWidget:SetOutOfRange(true)
    elseif self.Data and Game.me then
        local member1 = Game.TeamSystem:GetTeamMember(self.Data.id)
        local member2 = Game.TeamSystem:GetTeamMember(Game.me.eid)
        if member1 == nil or member2 == nil then
            return
        end
        local mapID1, lineID1 = Game.TeamSystem:UnpackWorldID(member1.worldID)
        local mapID2, lineID2 = Game.TeamSystem:UnpackWorldID(member2.worldID)
        self.bInSameMap = mapID1 == mapID2
        self.bInSameLine = lineID1 == lineID2
        if self.bInSameMap == false then
            --不同地图
            self.view.Text_Map:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            local levelMapData = Game.TableData.GetLevelMapDataRow(mapID1)
            self.view.Text_Map:SetText(levelMapData.Name)
            self.userWidget:SetOutOfRange(not self.bInAOI)
        elseif self.bInSameLine == false then
            --不同分线
            self.view.Text_Map:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self.view.Text_Map:SetText(StringConst.Get("MAP_LINE") ..lineID1)
            self.userWidget:SetOutOfRange(not self.bInAOI)
        else
            --- 距离
            self:RefreshAOIUI(Game.EntityManager:getEntity(self.Data.id) ~= nil)
        end
    end
end

function HUDTeamMemberItem:RefreshVoiceUI()
    local VocieValue = Game.TeamSystem:GetTeamMemberVoice(self.Data.id)
    local iconData = nil
    if VocieValue == Enum.EVOICE_STATE.LISTEN then
        iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_LISTEN)
    elseif VocieValue == Enum.EVOICE_STATE.VOICE then
        iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_OPEN_MIC)
    elseif VocieValue == Enum.EVOICE_STATE.REFUSE then
        iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_NOT_LISTEN)
    end
    if Game.VoiceSystem:CheckIsInBlackList(self.Data.id, Enum.EVOICE_CHANNEL.WORLD) and Game.TeamSystem:IsInBlockVoice(self.Data.id) then
        iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_BLOCK_MIC)
    end
    self:SetImage(self.view.img_voice_status, iconData.AssetPath)
end

function HUDTeamMemberItem:RefreshLocationUI()
    if self.bInSameMap and self.bInSameLine and self.bInAOI == false and self.IsOnline then
        local now = _G._now(1)
        if self.LastDistanceShow + 3 > now then
            return
        end
        self.LastDistanceShow = now
        local Dis = KismetMathLibrary.Vector_Distance(
                Game.me:GetPosition(),
                {
                    X = self.Data.location.x,
                    Y = self.Data.location.y,
                    Z = self.Data.location.z
                })

        local text = string.format(StringConst.Get("LOCK_TARGET_DISTANCE"), math.floor(Dis/100))
        self.view.Text_Map:SetText(text)
    end
end

function HUDTeamMemberItem:RefreshSelUI(Selected)
    self.userWidget:SetSelected(Selected)
    if Selected then
        self:PlayAnimation(self.userWidget.Ani_Select, nil, self.userWidget, 0.0, 1,
                EUMGSequencePlayMode.Forward,
                1,
                false)
    else
        self:PlayAnimation(self.userWidget.Ani_UnSelect, nil, self.userWidget, 0.0, 1,
                EUMGSequencePlayMode.Forward,
                1,
                false)
    end
end

function HUDTeamMemberItem:RefreshDeadUI(bDead)
    self.CanRebirth = false
    self.bDead = bDead
    self.userWidget:SetDead(self.bDead, self.Data.bTargetByBoss, not self.IsOnline)
    
    
    -- 没死 或者 没有复活技能
    if not bDead or self.RebirthID == -1 then
        self.userWidget:SetRebirth(false, false)
        return
    end
    
    --判断是否在3V3中 不能使用复活技能
    if Game.TeamAreanaSystem and Game.TeamAreanaSystem:IsIn3v3TeamArena() then
        self.userWidget:SetRebirth(false, false)
        return
    end
    
    ---是自己 不能复活
    if self.bSelf then
        self.userWidget:SetRebirth(false, false)
        return
    end

    -- 复活他人
    if not Game.me:CheckReviveSkillDisable(self.RebirthID) then
        -- 检查是否在比武大会中
        if Game.TeamAreanaSystem and Game.TeamAreanaSystem:IsInChampionArena() then
            --比武大会 队友最多复活两次
			if not Game.TeamAreanaSystem:IsPlayerAllowedForReviving(self.Data.id) then
				self.userWidget:SetRebirth(false, false)
				return
			end
        end
		
        if SkillImpl.checkSkillInCoolDown(self.RebirthID) then
            --复活CD中
            self:PlayAnimation( self.userWidget.Ani_RebirthCD, nil, self.userWidget,0.0, 1,
                    EUMGSequencePlayMode.Forward, 1, false)

            self.CanRebirth = true
            self.userWidget:SetRebirth(true, true)
        else
            self:PlayAnimation( self.userWidget.Ani_Rebirth, 
                    function()
                        self:PlayAnimation(self.userWidget.Ani_Rebirth_Loop, nil, self.userWidget, 0.0, 0,
                            EUMGSequencePlayMode.Forward, 1, false)
                    end,
                    self.userWidget, 0.0, 1,
                    EUMGSequencePlayMode.Forward, 1, false
                    )
            self.view.Text_Rebirth:SetText(StringConst.Get("HUD_REVIVE")) -- 复活
            self.CanRebirth = true
            self.userWidget:SetRebirth(true, false)
        end
    else
        self.userWidget:SetRebirth(false, false)
    end
end


--- 点击尝试复活
function HUDTeamMemberItem:ClickToRebirth()
    if self.RebirthID == -1 then
        return
    end

    --- 3v3 不能复活
    if Game.TeamAreanaSystem:IsIn3v3TeamArena() then
        return
    end

    if Game.me.IsDead then
        ---自己死亡，点队友复活 有提示
        if not self.bSelf then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_IN_DISABLE)
        end
    elseif self.Data.isDead then
        local Entity = Game.EntityManager:getEntity(self.Data.id)
        if not Entity then
			Game.me:TryActivateReviveSkill(self.Data.int_id)
        else
            if Entity.IsReliveConfirming == true then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_IN_RELIVE_CONFIRM)
            elseif SkillImpl.checkSkillInCoolDown(self.RebirthID) then
                Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OPTION_IN_CD)
            elseif not Game.TeamAreanaSystem:IsPlayerAllowedForReviving(self.Data.id) then

            else
                --点击复活反馈
                self:PlayAnimation(self.userWidget.Ani_Rebirth_Click, nil, self.userWidget,0.0, 1,
                        EUMGSequencePlayMode.Forward,
                        1,
                        false)
                Game.me:TryActivateReviveSkill(Entity:uid())
            end
        end
    end
end

--- 比武大会中
function HUDTeamMemberItem:OnPVPChampionReviveTimeChanged()
    if self.Data.isDead then
        self:RefreshDeadUI(self.Data.isDead)
    end
end


--- 此处为自动生成
function HUDTeamMemberItem:on_Btn_Clicked()
    self:Click_Btn()
end

function HUDTeamMemberItem:RefreshBarColor()
	if self.CommonBarHelper then
		self.CommonBarHelper:TintCampColor()
	end
end

return HUDTeamMemberItem
