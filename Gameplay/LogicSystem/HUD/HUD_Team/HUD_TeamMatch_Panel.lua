local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local ESlateVisibility = import("ESlateVisibility")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
---@class HUD_TeamMatch_Panel : UIPanel
---@field public View WBP_HUDBtnTimeView
local HUD_TeamMatch_Panel = DefineClass("HUD_TeamMatch_Panel", UIPanel)

HUD_TeamMatch_Panel.eventBindMap = {
    [EEventTypesV2.SERVER_ON_SINGLE_MATCH_STATE_CHANGED] = "UpdateMatchUI",
    [EEventTypesV2.SERVER_ON_TEAM_MATCH_STATE_CHANGED] = "UpdateMatchUI",
    [EEventTypesV2.ON_TEAMID_CHANGED] = "OnTeamGroupStateChange",
    [EEventTypesV2.ON_MAINPLAYER_GROUP_ID_CHANGED] = "OnTeamGroupStateChange", 
}

function HUD_TeamMatch_Panel:OnCreate()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "ClickBtnClickArea")
end

function HUD_TeamMatch_Panel:OnRefresh()
    self:UpdateUI()
end

function HUD_TeamMatch_Panel:OnTeamGroupStateChange()
    if self.MatchTimer then
        self:StopTimer(self.MatchTimer)
    end
    self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
end

function HUD_TeamMatch_Panel:UpdateUI()
    self.userWidget:SetIcon(5, true, true, true)
    local IsInTeamMatch = GetMainPlayerPropertySafely("isInTeamMatch") == 1 and true or false
    local bIsInSingleMatch = GetMainPlayerPropertySafely("isInSingleMatch") == 1 and true or false
    if IsInTeamMatch or bIsInSingleMatch then
        --队伍团队匹配中
        self.userWidget:SetVisibility(ESlateVisibility.Visible)
        self.view.Text_LeftTime:SetText("00:00")
        local MatchStartTime = 0
        if IsInTeamMatch then
            MatchStartTime = Game.me.matchStartTime
        else
            for key, value in pairs(Game.me.singleMatchInfoList) do
                if MatchStartTime == 0 or value.matchStartTime < MatchStartTime then
                    MatchStartTime = value.matchStartTime
                end
            end
        end
        self:CreateMatchTimer(MatchStartTime)
    else
        if self.MatchTimer then
            self:StopTimer(self.MatchTimer)
        end
        self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function HUD_TeamMatch_Panel:CreateMatchTimer(Time)
    self:StartTimer("MatchTimer",
        function()
            if Game.TimeUtils.GetCurTime() > Time then
                local handledTime = Game.TimeUtils.FormatCountDownString(
                    (Game.TimeUtils.GetCurTime() - Time) * 1000,
                    false)
                self.view.Text_LeftTime:SetText(handledTime)
            end
        end,
        500, -1, nil, true
    )
end

function HUD_TeamMatch_Panel:OnIdle()
end

function HUD_TeamMatch_Panel:ClickBtnClickArea()
    Log.Debug("==================SHow match")
    local btnSize = SlateBlueprintLibrary.GetLocalSize(self.view.Btn_ClickArea:GetCachedGeometry())
	local _, ViewportPosition = SlateBlueprintLibrary.LocalToViewport(
                                _G.GetContextObject(),
                                self.view.Btn_ClickArea:GetCachedGeometry(),
                                FVector2D(0, 0),
                                nil,
                                nil
                            )
    Game.NewUIManager:OpenPanel(UIPanelConfig.TeamMatching_Panel, ViewportPosition + btnSize)
end

function HUD_TeamMatch_Panel:UpdateMatchUI(State, TargetID, StartTime, Reason)
    if State == 0 then
        --匹配中
        self.userWidget:SetVisibility(ESlateVisibility.Visible)
        self.view.Text_LeftTime:SetText("00:00")
        self:CreateMatchTimer(StartTime)
    else
        --取消匹配
        self:StopTimer("MatchTimer") 
        self.view.Text_LeftTime:SetText("00:00")
        self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return HUD_TeamMatch_Panel
