local ESlateVisibility = import("ESlateVisibility")

local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUD_TeamGroup_Container : UIComponent
---@field view HUDTeamTotalBlueprint
local HUD_TeamGroup_Container = DefineClass("HUD_TeamGroup_Container", UIComponent)
local StringConst = require "Data.Config.StringConst.StringConst"

HUD_TeamGroup_Container.eventBindMap = {
    [_G.EEventTypes.ON_TEAMID_CHANGED] = { "TeamIDChange", GetMainPlayerEID },
    [_G.EEventTypes.CLIENT_GROUP_CREATE] = "GroupIDChange",
    [_G.EEventTypes.CLIENT_GROUP_DISBAND] = "GroupIDChange",
    [EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED] = "GameModeHandler",
    [EEventTypesV2.HUD_MARK_STATE_CAHNGED] = "OnMarkStateChange",

    -----------------------------------------------团队start-----------------------
    [_G.EEventTypes.CLIENT_GROUP_CAN_RESCUE_CHANGED] = "OnGroupCanSeekChanged",
    -----------------------------------------------团队end-----------------------

    ---------------------------------主角属性 start-------------------------
    [_G.EEventTypes.ON_SELF_CAPTAIN_CHANGED] = { "OnSelfCaptainStateChanged", GetMainPlayerEID },
    ----------------------------------主角属性 end---------------------------------
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUD_TeamGroup_Container:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUD_TeamGroup_Container:InitUIData()
    self.ConveneMemberTimer = nil
    self.SelectedGroupIndex = 1
    self.SelectedTabIndex = 1
    self.TeamReviveCDTime = nil
    self.ReviveMsgTime = 0
    self.GroupReviveCDTime = {}
    self.TotalGroupData = {}
    self.PanelData = {}
    self.GroupIndex = nil
    self.bShowGroupSelect = false
    self.voiceState = 0
    
    self.Children = {
        UICellConfig.HUD_Group,
        UICellConfig.HUD_Team,
    }
end

--- UI组件初始化，此处为自动生成
function HUD_TeamGroup_Container:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUD_TeamGroup_Container:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUD_TeamGroup_Container:InitUIView()
end

---组件刷新统一入口
function HUD_TeamGroup_Container:Refresh()
    self:UpdateUI()
end

function HUD_TeamGroup_Container:OnRefresh()
    self:UpdateUI()
end


function HUD_TeamGroup_Container:OnClose()
end

function HUD_TeamGroup_Container:OnHideX()
    if UI.IsShow(UICellConfig.HUD_Team) then
        Game.HUDSystem:HideUI(UICellConfig.HUD_Team)
    end
    if UI.IsShow(UICellConfig.HUD_Group) then
        Game.HUDSystem:HideUI(UICellConfig.HUD_Group)
    end
end


function HUD_TeamGroup_Container:UpdateUI()
    local bInTeam = Game.TeamSystem:IsInTeam()
    local bInGroup = Game.TeamSystem:IsInGroup()
    self:RefreshTeamGroupStateUI()
    if bInTeam then
        self:UpdateTeamInfoListUI()
    elseif bInGroup then
        self:ShowGroupSelectUI(false)
    end
    self:GameModeHandler()
    self:RefreshMarkUI()
    self:StartOnIdle()
end

function HUD_TeamGroup_Container:StartOnIdle()
    for _, v in pairs(self.Children) do
        local child = self:GetComponentByCellId(v)
        if child then
            child:StartOnIdle()
        end
    end
end

function HUD_TeamGroup_Container:GameModeHandler()
end


function HUD_TeamGroup_Container:OnMarkStateChange()
    self:RefreshMarkUI()
end

function HUD_TeamGroup_Container:RefreshMarkUI()
    if Game.TeamSystem:GetMarkState() == Enum.EMARK.TeamGroup then
        self:PlayAnimation(self.userWidget.Fadein, nil, self.userWidget, 0.0, 1)
        self.view.HB_Mark:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Mark:SetText(StringConst.Get("TEAM_MARK_MEMBER_DESC"))
    elseif Game.TeamSystem:GetMarkState() == Enum.EMARK.Scene then
        self:PlayAnimation(self.userWidget.Fadein, nil, self.userWidget,  0.0, 1)
        self.view.HB_Mark:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.view.Text_Mark:SetText(StringConst.Get("TEAM_MARK_SCENE_DESC"))
    else
        self:PlayAnimation( self.userWidget.Fadeout, 
            function()
                self.view.HB_Mark:SetVisibility(ESlateVisibility.Collapsed)
            end, self.userWidget,0.0, 1)
    end
end

function HUD_TeamGroup_Container:UpdateGroupMember()
    self:RefreshTeamGroupStateUI()
    self:UpdateGroupMemberUI()
end

function HUD_TeamGroup_Container:OnSelfCaptainStateChanged()
    self:UpdateTeamInfoListUI()
end

function HUD_TeamGroup_Container:TeamIDChange(Player, PropName, NewValue, OldValue)
    self:RefreshTeamGroupStateUI()
end

function HUD_TeamGroup_Container:GroupIDChange(Player, PropName, NewValue, OldValue)
    local child = self:GetComponentByCellId(UICellConfig.HUD_Group)
    if child then
        child:Refresh()
    end
    
    self:RefreshTeamGroupStateUI()
    self:ShowGroupSelectUI(false)
end

function HUD_TeamGroup_Container:RefreshTeamGroupStateUI()

    local bInTeam = Game.TeamSystem:IsInTeam()
    local bInGroup = Game.TeamSystem:IsInGroup()
    if bInTeam or bInGroup and slua.isValid(self.view.Overlay_TeamGroupInfo) then
        self.view.Overlay_TeamGroupInfo:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        if bInGroup then
            self:HideChild(UICellConfig.HUD_Team)
            self:OpenChild(UICellConfig.HUD_Group)
        else
            self:HideChild(UICellConfig.HUD_Group)
            self:OpenChild(UICellConfig.HUD_Team)
        end
    else
        self:HideChild(UICellConfig.HUD_Group)
        self:HideChild(UICellConfig.HUD_Team)
        self.view.Overlay_TeamGroupInfo:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function HUD_TeamGroup_Container:HideChild(cellId)
    local child = self:GetComponentByCellId(cellId)
    if child then
        --child:CloseSelf()
        Game.HUDSystem:HideUI(cellId)
    end
end

function HUD_TeamGroup_Container:OpenChild(cellId)
    local child = self:GetComponentByCellId(cellId)
    if not child then
        Game.HUDSystem:ShowUI(cellId)
    end
end

-----------------------------------匹配 start--------------------------------------------------
function HUD_TeamGroup_Container:UpdateTeamMatchUI()
    self.view.WBP_HUDTeamMatch:SetVisibility(ESlateVisibility.Collapsed)
    local IsInTeamMatch = GetMainPlayerPropertySafely("isInTeamMatch") == 1 and true or false
    local bInTeam = Game.TeamSystem:IsInTeam()
    local bInGroup = Game.TeamSystem:IsInGroup()
    local bIsInSingleMatch = GetMainPlayerPropertySafely("isInSingleMatch") == 1 and true or false
    if bInTeam or bInGroup then
        if IsInTeamMatch then
            --队伍匹配中
            self.view.WBP_HUDTeamMatch:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    else
        if bIsInSingleMatch then
            --个人匹配中
            self.view.WBP_HUDTeamMatch:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
end

----------------------------------队伍HUD start------------------------------------------
function HUD_TeamGroup_Container:UpdateTeamInfoListUI()
    local child = self:GetComponentByCellId(UICellConfig.HUD_Team)
    if child then
        child:UpdateTeamInfoListUI()
    end
end

---------------------------------队伍HUD end----------------------

function HUD_TeamGroup_Container:ShowGroupSelectUI(bShow)
    local child = self:GetComponentByCellId(UICellConfig.HUD_Group)
    if child then
        child:Refresh(bShow)
    end
end


return HUD_TeamGroup_Container
