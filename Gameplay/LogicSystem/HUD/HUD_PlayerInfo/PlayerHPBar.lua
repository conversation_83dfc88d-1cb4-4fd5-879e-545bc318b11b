local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class PlayerHPBar : UIComponent
---@field view PlayerHPBarBlueprint
local PlayerHPBar = DefineClass("PlayerHPBar", UIComponent)
local CommonBarHelper = kg_require("Gameplay.LogicSystem.CommonUI.CommonBarHelper")

PlayerHPBar.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerHPBar:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerHPBar:InitUIData()
	---@type number 每帧定时器
	self.tickTimer = nil
end

--- UI组件初始化，此处为自动生成
function PlayerHPBar:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function PlayerHPBar:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerHPBar:InitUIView()
end

function PlayerHPBar:OnRefresh(Params)
	self:Refresh(Params)
end

function PlayerHPBar:Refresh(Params)
	if not self.CommonBarHelper then
		self.CommonBarHelper =  CommonBarHelper.new(Params)
	end
end

function PlayerHPBar:SetMaxValue(InMaxHP)
	self.CommonBarHelper:SetMaxHP(InMaxHP)
end

function PlayerHPBar:SetValue(InHP)
	self.CommonBarHelper:SetHP(InHP)
end

function PlayerHPBar:SetValueInstant(InHP,InShield)
	self.CommonBarHelper:SetHPInstant(InHP)
	self.CommonBarHelper:SetShield(InShield)
end

function PlayerHPBar:SetSheild(InShield)
	self.CommonBarHelper:SetShield(InShield)
end

function PlayerHPBar:DamageFlash()
	self:PlayAnimation(self.view.Ani_Hit)
end

function PlayerHPBar:UnBindEntity()
	self.CommonBarHelper:UnBindEntity()
end

function PlayerHPBar:BindEntity(UID,MainPropName,MaxValuePropName,CurrentMaxValuePropName,SecondPropName)
	local eid = UID

	self.CommonBarHelper:BindEntity(
		eid,
		MainPropName,
		MaxValuePropName,
		CurrentMaxValuePropName,
		SecondPropName
	)
	self.CommonBarHelper:BindWidget(
		self.userWidget,
		self.view.PB_HPMain,
		self.view.PB_Shield,
		self.view.PB_Damage,
		self.view.PB_HPLock,
		nil,
		self.view.slider_hp
	)

	self.tickTimer = self:StartTimer(
		"HPBar" .. eid,
		function(DeltaTime)
			self.CommonBarHelper:Tick(DeltaTime)
		end,
		100, -1, nil, true
	)
	self.CommonBarHelper:TintCampColor()
end

function PlayerHPBar:RestartTimer()
	self:StopTimer(self.tickTimer)
	self.tickTimer = self:StartTimer(
		"HPBar" .. self.CommonBarHelper.BindEntityEid,
		function(DeltaTime)
			self.CommonBarHelper:Tick(DeltaTime)
		end,
		1, -1, nil, true
	)
end

return PlayerHPBar
