local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class FeatherwitCardList : UIComponent
---@field view FeatherwitCardListBlueprint
local FeatherwitCardList = DefineClass("FeatherwitCardList", UIComponent)

FeatherwitCardList.eventBindMap = {
	[EEventTypesV2.ON_FOOL_CARD_ADD] = "onFoolCardAdd",
	[EEventTypesV2.ON_FOOL_CARD_REMOVE] = "onFoolCardRemove",
	[EEventTypesV2.ON_FOOL_CARD_COMBO] = "onFoolCardCombo",
	[EEventTypesV2.ON_FOOL_CARD_REFRESH] = "Refresh",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FeatherwitCardList:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FeatherwitCardList:InitUIData()
	self.cardDataList = {}
end

--- UI组件初始化，此处为自动生成
function FeatherwitCardList:InitUIComponent()
    ---@type UIListView childScript: FeatherwitCardItem
    self.ListViewCom = self:CreateComponent(self.view.ListView, UIListView)
end

---UI事件在这里注册，此处为自动生成
function FeatherwitCardList:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FeatherwitCardList:InitUIView()
end

---组件刷新统一入口
function FeatherwitCardList:Refresh(...)
	local cardList = Game.me:GetFoolCardList()
	if Game.me and cardList then
		self.cardList = { table.unpack(cardList) }
	else
		self.cardList = {}
	end
	self.ListViewCom:Refresh(self.cardList)
end

function FeatherwitCardList:onFoolCardAdd(cardInfo)
	table.insert(self.cardList, cardInfo)
	self.ListViewCom:NotifyAddData(1)
end

function FeatherwitCardList:onFoolCardRemove(cardInfo)
	for i = 1, #self.cardList do
		if self.cardList[i].cardToken == cardInfo.cardToken and self.cardList[i] and cardInfo.cardId then
			table.remove(self.cardList, i)
			self.ListViewCom:NotifyRemoveData(i)
			break
		end
	end
end

function FeatherwitCardList:onFoolCardCombo(comboCardId, comboCardToken)
	if self:HasTimer("DelayClear") then
		return
	end
	self.comboCardId = comboCardId
	self.comboCardToken = comboCardToken
	self:delayRefreshCombo()
end

function FeatherwitCardList:delayRefreshCombo()
	local outAnim = self.view.Ani_BlueOut
	if self.comboCardId == Enum.ERoleMechanismConstData.FOOL_YELLOW_CARD_BUFF then
		outAnim = self.view.Ani_YellowOut
	end
	-- 播list的out动画
	self:PlayAnimation(outAnim, nil, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
	-- 让item播对应的三消动画
	for i = #self.cardList, 1, -1 do
		if self.cardList[i].cardToken == self.comboCardToken then
			local item = self.ListViewCom:GetItemByIndex(i)
			if item then
				item:OnCardCombo(self.comboCardId, self.comboCardToken)
			end
		end
	end
	-- 三消播完重新刷新列表
	self:StartTimer("DelayClear", function() self:delayClear() end , 650, 1)
end

function FeatherwitCardList:delayClear()
	self:StopTimer("DelayClear")
	for i = #self.cardList, 1, -1 do
		if self.cardList[i].cardToken == self.comboCardToken then
			table.remove(self.cardList, i)
			self.ListViewCom:NotifyRemoveData(i)
		end
	end
end

return FeatherwitCardList
