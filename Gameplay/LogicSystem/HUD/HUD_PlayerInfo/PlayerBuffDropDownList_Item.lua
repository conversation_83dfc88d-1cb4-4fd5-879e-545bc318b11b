local HUDBUFF_Item = kg_require("Gameplay.LogicSystem.HUD.HUD_PlayerInfo.HUDBUFF_Item")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class PlayerBuffDropDownList_Item : UIListItem
---@field view PlayerBuffDropDownList_ItemBlueprint
local PlayerBuffDropDownList_Item = DefineClass("PlayerBuffDropDownList_Item", UIListItem)
local DescFormulaHelper = kg_require "Gameplay.LogicSystem.SkillCustomizer.DescFormulaHelper"
local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"

PlayerBuffDropDownList_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function PlayerBuffDropDownList_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function PlayerBuffDropDownList_Item:InitUIData()
	---@type table 数据
	self.data = nil
	---@type number 剩余时间
	self.TimeRemain = nil
end

--- UI组件初始化，此处为自动生成
function PlayerBuffDropDownList_Item:InitUIComponent()
    ---@type HUDBUFF_Item
    self.WBP_HUDBUFFCom = self:CreateComponent(self.view.WBP_HUDBUFF, HUDBUFF_Item)
end

---UI事件在这里注册，此处为自动生成
function PlayerBuffDropDownList_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function PlayerBuffDropDownList_Item:InitUIView()
	self.WBP_HUDBUFFCom.userWidget:Event_UI_Style(1)
	self.WBP_HUDBUFFCom.view.Img_Bg_Cd:SetVisibility(ESlateVisibility.Collapsed)
	self.WBP_HUDBUFFCom:SetbShowProgress(false)
end

function PlayerBuffDropDownList_Item:Refresh(data)
	self.data = data
	--Log.Debug(data.buff.OwnerEID)
	self.TimeRemain = 0222
	local OwnerEID = data.OwnerEID
	if OwnerEID == nil then
		OwnerEID = data.buff.OwnerEID
	end
	local entity = Game.EntityManager:getEntity(OwnerEID)
	if entity then
		local BuffInst
		if data.buff then
			BuffInst = data.buff
		else
			BuffInst = entity:GetBuffInstanceByBuffIDAndInstigatorID(self.data.buffID, self.data.instigatorID)
		end

		if (BuffInst == nil) then
			return
		end

		self.WBP_HUDBUFFCom:Refresh(self.data, false)
		self.WBP_HUDBUFFCom:SetbShowProgress(false)

		local BuffID = BSFunc.GetAssetID(BuffInst)
		local BuffInfo = BuffInst:GetBuffData()
		-- Get Instigator and Buffowner's RPCEntity to configure the correct buff description
		local BuffDisc = DescFormulaHelper.GenerateBuffDesc(BuffID, BuffInst.Level, BuffInst.BuffLayer)
		if BuffInfo then
			self.view.Text_BuffName:SetText(BuffInfo.BuffName)
			self.view.Text_BuffDesc:SetText(BuffDisc)
		end
		self.view.Text_BuffLevel:SetText(BuffInst.Level .. StringConst.Get("BAG_Level"))

		self:UpdateTimeDisplay()
		self:GetParent():StartTimer(
			data.buffID .. data.instigatorID .."hiddenbufflist",
			function()
				if self and self.view and self.TimeRemain > 0 then
					return self:UpdateTimeDisplay()
				else
					self:StopTimer(data.buffID .. data.instigatorID.."hiddenbufflist")
				end
			end,
			1, -1, nil, true
		)
	elseif self.data.PropName and self.data.Icon then
		self.WBP_HUDBUFFCom:Refresh(self.data)
		self.view.Text_BuffName:SetText(self.data.DisplayName)
		self.view.Text_BuffDesc:SetText(self.data.Desc)
		self.view.Text_Time:SetVisibility(ESlateVisibility.Collapsed)
	end
end

---面板打开的时候触发
function PlayerBuffDropDownList_Item:OnRefresh(data)
	if data then
		self:Refresh(data)
	end
end

function PlayerBuffDropDownList_Item:UpdateTimeDisplay()
	if self.data == nil then
		return true
	end

	local OwnerEID = self.data.OwnerEID
	if OwnerEID == nil then
		OwnerEID = self.data.buff.OwnerEID
	end
	local Entity = Game.EntityManager:getEntity(OwnerEID)

	if (Entity == nil) then
		return true
	end
	local BuffInst = Entity:GetBuffInstanceByBuffIDAndInstigatorID(self.data.buffID, self.data.instigatorID)
	if not BuffInst then
		Log.WarningFormat("Cannot Find BuffInstance By BuffID %s and InstanceID %s", self.data.buffID, self.data.instigatorID)
		return true
	end
	local TotalLife, CurrentLife = BuffInst:GetLifeMessage()

	self.TimeRemain = TotalLife - CurrentLife
	-- Log.Debug("Item UpdateTimeDisplay(self)",BuffInstID,TotalLife,CurrentLife,self.TimeRemain)
	if CurrentLife <= 0 or self.TimeRemain <= 0 then
		self.view.Text_Time:SetVisibility(ESlateVisibility.Hidden)
	else
		self.view.Text_Time:SetVisibility(ESlateVisibility.Visible)
		self.view.Text_Time:SetText(Game.TimeUtils.FormatCountDownString(self.TimeRemain * 1000, false))
	end
end


return PlayerBuffDropDownList_Item
