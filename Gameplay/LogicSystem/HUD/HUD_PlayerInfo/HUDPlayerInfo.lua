local FeatherwitCardList = kg_require("Gameplay.LogicSystem.HUD.HUD_PlayerInfo.FeatherwitCardList")
local PlayerInfoBuffs = kg_require("Gameplay.LogicSystem.HUD.HUD_PlayerInfo.PlayerInfoBuffs")
local HUDApprenticeBar = kg_require("Gameplay.LogicSystem.HUD.HUD_PlayerInfo.HUDApprenticeBar")
local PlayerHPBar = kg_require("Gameplay.LogicSystem.HUD.HUD_PlayerInfo.PlayerHPBar")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUDPlayerInfo : UIComponent
---@field view HUDPlayerInfoBlueprint
local HUDPlayerInfo = DefineClass("HUDPlayerInfo", UIComponent)
local ESlateVisibility = import("ESlateVisibility")

HUDPlayerInfo.eventBindMap = {
	[EEventTypesV2.ON_SELF_LEVEL_CHANGED] = "RefreshLevel",
	[EEventTypesV2.ON_SELF_BTARGETBYBOSS_CHANGED] = "Event_UI_Style",
	[EEventTypesV2.ON_PROFESSION_PROP_1_CHANGED] = "OnProfessionSkillProp1",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDPlayerInfo:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDPlayerInfo:InitUIData()

end

--- UI组件初始化，此处为自动生成
function HUDPlayerInfo:InitUIComponent()
    ---@type FeatherwitCardList
    self.WBP_HUD_FeatherwitCard_ListCom = self:CreateComponent(self.view.WBP_HUD_FeatherwitCard_List, FeatherwitCardList)
    ---@type PlayerInfoBuffs
    self.WBP_PlayerInfoBuffsCom = self:CreateComponent(self.view.WBP_PlayerInfoBuffs, PlayerInfoBuffs)
    ---@type HUDApprenticeBar
    self.WBP_HUDApprenticeBarCom = self:CreateComponent(self.view.WBP_HUDApprenticeBar, HUDApprenticeBar)
    ---@type PlayerHPBar
    self.WBP_PlayerHPBarCom = self:CreateComponent(self.view.WBP_PlayerHPBar, PlayerHPBar)
end

---UI事件在这里注册，此处为自动生成
function HUDPlayerInfo:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Personal.OnClicked, "on_Btn_Personal_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDPlayerInfo:InitUIView()
end

function HUDPlayerInfo:OnRefresh()
	Game.PlayerInfoManager:RegisterRefreshBuff(self)
	self:Event_UI_Style()
	self.WBP_PlayerInfoBuffsCom.WBP_PlayerBuffDropDownListCom.userWidget:SetVisibility(ESlateVisibility.Collapsed)

	self:RefreshLevel()

	local Profession = GetMainPlayerPropertySafely("Profession")
	self:SetHeadIcon(Profession)

	self:RefreshBuffs(nil, self:GetTargetEntityID(), Game.PlayerInfoManager:GetTargetBuffs(self:GetTargetEntityID(), nil), nil)

	local HPIncreaseRate = Game.TableData.GetConstDataRow("HP_BAR_INCREASE_RATE")
	local HPDMGReduceRate = Game.TableData.GetConstDataRow("HP_BAR_DMG_REDUCE_RATE")
	local HPDMGReduceDelay = Game.TableData.GetConstDataRow("HP_BAR_DMG_REDUCE_DELAY")

	local Params = {
		MainValue = 0,
		MaxValue = 0,
		Shield = 0,
		IncreaseRate = HPIncreaseRate,
		DMGReduceRate = HPDMGReduceRate,
		DMGReduceDelay = HPDMGReduceDelay
	}
	self.WBP_PlayerHPBarCom:Refresh(Params)
	self.WBP_PlayerHPBarCom:BindEntity(
		Game.me:uid(),
		"Hp",
		"MaxHp",
		"CurrentMaxHp",
		"aShield"
	)

	local tValue = GetMainPlayerPropertySafely("ProfessionProp1")
	self.WBP_HUDApprenticeBarCom:Refresh(tValue)
	if Game.me.Profession == 1200005 then
		self.userWidget:SetOccupation(1)
	else
		self.userWidget:SetOccupation(0)
	end
	self.WBP_HUD_FeatherwitCard_ListCom:Refresh()
end

function HUDPlayerInfo:RefreshBuffs(_, eid, NewList, CountDownList)
	if eid == self:GetTargetEntityID() then
		self.WBP_PlayerInfoBuffsCom:RefreshBuffs(NewList)
	end
end

function HUDPlayerInfo:GetTargetEntityID()
	if Game.me then
		return Game.me:uid()
	end
	return nil
end

function HUDPlayerInfo:OnClose()
	Game.PlayerInfoManager:UnregisterRefreshBuff(self)
	if self.WBP_PlayerHPBarCom then
		self.WBP_PlayerHPBarCom:UnBindEntity()
	else
		Log.Debug("UI LOG: Cannot Find HUDPlayerInfo.HPBar")
	end
end

function HUDPlayerInfo:OnProfessionSkillProp1(NewValue, OldValue)
	self.WBP_HUDApprenticeBarCom:Refresh(NewValue)
end

function HUDPlayerInfo:Event_UI_Style()
	local Aggro = GetMainPlayerPropertySafely("bTargetedByBoss")
	if Aggro == nil then
		Aggro = false
	end
	self.userWidget:Event_UI_Style(Aggro)
end

function HUDPlayerInfo:RefreshLevel()
	local Level = GetMainPlayerPropertySafely("Level")
	self.view.Text_level:SetText(Level or "nil")
end

function HUDPlayerInfo:SetHeadIcon(Profession)
	--设置头像
	local OptionClassInfo = Game.TableData.GetPlayerSocialDisplayDataRow(Profession)
	if OptionClassInfo then
		self:SetImage(self.view.Img_HeadJobIcon, OptionClassInfo[0].ClassLogoHud)
	end
end

function HUDPlayerInfo:on_Btn_Personal_Clicked()
	Game.RoleDisplaySystem.ShowPlayerDisplay()
end

return HUDPlayerInfo
