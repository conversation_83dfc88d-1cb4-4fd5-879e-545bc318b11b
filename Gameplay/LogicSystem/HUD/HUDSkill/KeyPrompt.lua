local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require "Data.Config.StringConst.StringConst"
local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")

local EPropertyClass = import("EPropertyClass")
local EInputEvent = import("EInputEvent")
local ESlateVisibility = import("ESlateVisibility")

---@class KeyPrompt : UIComponent
---@field view KeyPromptBlueprint
local KeyPrompt = DefineClass("KeyPrompt", UIComponent)

KeyPrompt.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function KeyPrompt:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function KeyPrompt:InitUIData()
	---@type LuaDelegate<fun(bIsPress:boolean)>
    self.onActionInput = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function KeyPrompt:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function KeyPrompt:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function KeyPrompt:InitUIView()
end

---组件刷新统一入口
---@param key string 绑定快捷键
---@param inputKeyName string 快捷键名称
function KeyPrompt:Refresh(key, inputKeyName)
	local visibility = ESlateVisibility.Collapsed
    if self.ActionName ~= key and key ~= "" then
		self.ActionName = key
		self.FullActionName = string.format("%s_Action", key)
		Game.GlobalEventSystem:RemoveTargetAllListeners(self)
        Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, "Receive_ROLE_ACTION_INPUT_EVENT", self)
		visibility = ESlateVisibility.Visible
        self:SetKeyMap()
    elseif inputKeyName then
		visibility = ESlateVisibility.Visible
        self.userWidget:SetActiveIndex(0)
        self.view.text_key_lua:SetText(inputKeyName)
    end
	self:SetVisibility(visibility)
end

function KeyPrompt:Receive_ROLE_ACTION_INPUT_EVENT(EventName, EventType)
    if self.FullActionName == EventName then
        if EventType == EInputEvent.IE_Pressed then
            self.userWidget:OnPress()
			self.onActionInput:Execute(true)
        elseif EventType == EInputEvent.IE_Released then
            self.userWidget:OnRelease()
			self.onActionInput:Execute(false)
        end
    end
end

---@type table<string, string> 按键名称
KeyPrompt.KeyLocaleName = {
    ["LeftShift"] = StringConst.Get("LEFT_SHIFT"),--"左shift",
    ["RightShift"] = StringConst.Get("RIGHT_SHIFT"),--"右shift",
    ["LeftControl"] = StringConst.Get("LEFT_CTRL"),--"左Ctrl",
    ["RightControl"] = StringConst.Get("RIGHT_CTRL"),--"右Ctrl",
    ["LeftAlt"] = StringConst.Get("LEFT_ALT"),--"左Alt",
    ["RightAlt"] = StringConst.Get("RIGHT_ALT"),--"右Alt",
    ["LeftCommand"] = StringConst.Get("LEFT_COMMAND"),--"左Cmd",
    ["RightCommand"] = StringConst.Get("RIGHT_COMMAND"),--"右Cmd",
    ["Tilde"] = "~"
}


function KeyPrompt:GenerateKeyDisplayName(KeyInput)
    local Key = KeyInput.Key
    local KeyDisplayName = ""
    if KeyInput.bShift then
        KeyDisplayName = KeyDisplayName and KeyDisplayName .. " + Shift" or "Shift"
    end

    if KeyInput.bCtrl then
        KeyDisplayName = KeyDisplayName and KeyDisplayName .. " + Ctrl" or "Ctrl"
    end

    if KeyInput.bAlt then
        KeyDisplayName = KeyDisplayName and KeyDisplayName .. " + Alt" or "Alt"
    end

    if KeyInput.bCmd then
        KeyDisplayName = KeyDisplayName and KeyDisplayName .. " + Cmd" or "Cmd"
    end
	
	local localeName = KeyPrompt.KeyLocaleName[Key.KeyName]
    if localeName then
        KeyDisplayName = KeyDisplayName .. localeName
    else
        KeyDisplayName = KeyDisplayName .. import("KismetInputLibrary").Key_GetDisplayName(Key, true)
    end

    return KeyDisplayName
end

---@type table<string, string> 鼠标按键图片路径
KeyPrompt.MouseImgs = {
    ["LeftMouseButton"] =
    UIAssetPath.UI_KeyPrompt_Img_MouseLeft,
    ["RightMouseButton"] =
    UIAssetPath.UI_KeyPrompt_Img_MouseRight,
    ["MiddleMouseButton"] =
    UIAssetPath.UI_KeyPrompt_Img_MouseWheel01,
    ["MouseWheelAxis"] =
    UIAssetPath.UI_KeyPrompt_Img_MouseWheel02,
}


function KeyPrompt:SetKeyMap()
    local InputSettings = import("InputSettings").GetInputSettings()
    local Mappings = slua.Array(EPropertyClass.Struct, import("InputActionKeyMapping"))
    Mappings = InputSettings:GetActionMappingByName(self.ActionName, Mappings)
    local bUseKeyBoardInput = true --TODO:输入方式判定，目前暂时只支持键鼠
    self.userWidget:SetActiveIndex(0)
    self.view.text_key_lua:SetText("")


    for i = 0, Mappings:Length() - 1, 1 do
        local Key = Mappings:Get(i).Key
        if bUseKeyBoardInput and IsValid_L(Key) and import("KismetInputLibrary").Key_IsKeyboardKey(Key) then
            self.userWidget:SetActiveIndex(0)
            local DisplayName = self:GenerateKeyDisplayName(Mappings:Get(i))
            if DisplayName then
                self.view.text_key_lua:SetText(DisplayName)
                return true
            else
                return false
            end
        end

        if bUseKeyBoardInput and IsValid_L(Key) and import("KismetInputLibrary").Key_IsMouseButton(Key) then
            self.userWidget:SetActiveIndex(1)
            local ImgPath = KeyPrompt.MouseImgs[Key.KeyName]
            if Key.KeyName == "MouseWheelAxis" then
                self:SetImageSize(1)
            else
                self:SetImageSize(0)
            end
            if ImgPath then
                self:SetImage(self.view.img_mouse_lua, ImgPath)
                return true
            else
                return false
            end
        end
    end
    return false
end

function KeyPrompt:SetImageSize(index)
    self.userWidget:SetSize(index)
end

function KeyPrompt:SetVisibility(visibility)
	-- 移动端不显示
	if PlatformUtil.IsMobilePlatform() then
		visibility = ESlateVisibility.Collapsed
	end
	self.userWidget:SetVisibility(visibility)
end

function KeyPrompt:OnlyRefresh(bIsMouseBtn, inputKeyName)
    if bIsMouseBtn then
        self.userWidget:SetActiveIndex(1)
        local ImgPath = KeyPrompt.MouseImgs[inputKeyName]
        if inputKeyName == "MouseWheelAxis" then
            self:SetImageSize(1)
        else
            self:SetImageSize(0)
        end
        if ImgPath then
            self:SetImage(self.view.img_mouse_lua, ImgPath)
            return true
        else
            return false
        end
    else
		self:SetVisibility(ESlateVisibility.Visible)
        self.userWidget:SetActiveIndex(0)
        self.view.text_key_lua:SetText(inputKeyName)
    end
end

return KeyPrompt
