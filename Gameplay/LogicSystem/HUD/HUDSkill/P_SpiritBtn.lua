local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local P_SpiritBtn = DefineClass("P_SpiritBtn", UIController)
local P_KeyPrompt = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_KeyPrompt"
local ESlateVisibility = import("ESlateVisibility")

P_SpiritBtn.DefaultIcon = UIAssetPath.UI_HUD_Com_Icon_SpiritualVision01_Sprite
P_SpiritBtn.FocusIcon = UIAssetPath.UI_HUD_Com_Icon_SpiritualVision02_Sprite

function P_SpiritBtn:OnClick()
    Game.SpiritualVisionSystem:DoSpiritual()
    self:PlayAnimation(self.View.WidgetRoot, self.View.Ani_Press, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function P_SpiritBtn:OnHovered()
    self.View.img_additive:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self.View.img_hover:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function P_SpiritBtn:OnUnhovered()
    self.View.img_additive:SetVisibility(ESlateVisibility.Collapsed)
    self.View.img_hover:SetVisibility(ESlateVisibility.Collapsed)
end

function P_SpiritBtn:CheckVisiable()
    if Game.me then
        local bEnable = Game.me:GetSpiritualVisionEnable()
        local bIsDead = Game.me.IsDead
        local bIsQTE = Game.HUDSystem.IsInQte()
        local bInSeat = Game.me.VehicleSeatIndex ~= 0 
        if not bEnable or bIsDead or bIsQTE or bInSeat then
            self.View.size_btn:SetVisibility(ESlateVisibility.Collapsed)
        else
            self.View.size_btn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
end

P_SpiritBtn.eventBindMap = {
    [EEventTypesV2.SPIRIT_VISUAL_ON_OPEN] = "SetColdCD",
    [EEventTypesV2.SPIRIT_VISUAL_ON_CLOSE] = "SetColdCD",
    [EEventTypesV2.SPIRIT_VISUAL_ON_ENABLE] = "CheckVisiable",
    [_G.EEventTypes.ON_IS_DEAD_CHANGED] = { "CheckVisiable", GetMainPlayerEID },
	[EEventTypesV2.HUD_POST_QTE_STATE_CHANGED] = "CheckVisiable",
    [_G.EEventTypes.ON_SELF_VEHICLESEATINDEX_CHANGED] = { "CheckVisiable", GetMainPlayerEID },
}

function P_SpiritBtn:OnCreate()
    self.showContent = { Second = true }

    self.View.WBP_HUDBtnCD:SetVisibility(ESlateVisibility.Collapsed)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.btn_clickarea,"OnClick")
    self:AddUIListener(EUIEventTypes.Hovered, self.View.btn_clickarea,"OnHovered")
    self:AddUIListener(EUIEventTypes.Unhovered, self.View.btn_clickarea,"OnUnhovered")
    ---@type P_KeyPrompt 快捷键组件对象
    self.P_KeyPrompt = self:BindComponent(self.View.WBP_KeyPrompt, P_KeyPrompt)
end

function P_SpiritBtn:OnRefresh(Params)
    self:SetColdCD()
    self:CheckVisiable()
    -- if Params and Params.KeyActionName then
    --     -- self.View.panel_PC:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    -- else
    --     -- self.View.panel_PC:SetVisibility(ESlateVisibility.Collapsed)
    -- end
end

function P_SpiritBtn:Refresh(Params)
    self.View.img_additive:SetVisibility(ESlateVisibility.Collapsed)
    self:SetColdCD()
    self:CheckVisiable()
    if Game.HUDSystem.IsInPC() then
        self.View.WBP_KeyPrompt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.P_KeyPrompt:Refresh("SecondSight")
        self.View:PreConstructPCStyle()
    else
        self.View.WBP_KeyPrompt:SetVisibility(ESlateVisibility.Collapsed)
    end
    -- if Params and Params.KeyActionName then
    --     self.View.panel_PC:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    -- else
    --     self.View.panel_PC:SetVisibility(ESlateVisibility.Collapsed)
    -- end
end

function P_SpiritBtn:EndCD()
    self.View.WBP_HUDBtnCD.text_cd:SetText(0)
    self.View.WBP_HUDBtnCD:SetCD(0)
    local icon_sv = self.View.icon_sv
    self:SetImage(icon_sv, P_SpiritBtn.DefaultIcon)
    self.View.WBP_HUDBtnCD:SetVisibility(ESlateVisibility.Collapsed)
end

function P_SpiritBtn:SetColdCD()
    local endTime = Game.SpiritualVisionSystem:GetCDEndTime()
    if endTime <= _now() then
        self:EndCD()
        return
    end
    local icon_sv = self.View.icon_sv
    self:SetImage(icon_sv, P_SpiritBtn.FocusIcon)
    self.View.WBP_HUDBtnCD:SetVisibility(ESlateVisibility.Visible)
    local countDown = endTime - _now()
    self.View.WBP_HUDBtnCD:SetCD(countDown / Game.TableData.GetConstDataRow("SPIRITUAL_VISION_CD"))
    self.View.WBP_HUDBtnCD.text_cd:SetText(math.floor(countDown / 1000))
    self:StartTimer("CountDown", function()
        local countDown = endTime - _now()
        if countDown <= 0 then
            self:EndCD()
            self:StopTimer("CountDown")
            return
        end
        if countDown < 1000 then
            self.View.WBP_HUDBtnCD.text_cd:SetText(string.format("%.1f", countDown / 1000))
        else
            self.View.WBP_HUDBtnCD.text_cd:SetText(math.floor(countDown / 1000))
        end
        self.View.WBP_HUDBtnCD:SetCD(countDown / Game.TableData.GetConstDataRow("SPIRITUAL_VISION_CD"))
    end, 1, -1, nil, true)
end

return P_SpiritBtn
