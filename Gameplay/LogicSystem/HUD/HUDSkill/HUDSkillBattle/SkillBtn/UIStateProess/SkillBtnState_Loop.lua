local ESlateVisibility = import("ESlateVisibility")
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local SkillBtnState_Base= kg_require("Gameplay.LogicSystem.HUD.HUDSkill.HUDSkillBattle.SkillBtn.UIStateProess.SkillBtnState_Base")
local SkillBtnState_Loop = DefineClass("SkillBtnState_Loop", SkillBtnState_Base)
function SkillBtnState_Loop:EnterState(SkillID, RunningTime, SkillDuration, ChantDurantion)
    --self.view.WBP_CD:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local MID = self.view.MID:GetDynamicMaterial()
    MID:SetScalarParameterValue("i_CD_enable", 1)
    SkillImpl.onSkillBtnReleased(self.SkillSlot, false)
    self:UpdateState(SkillID, RunningTime, SkillDuration, ChantDurantion)
end


function SkillBtnState_Loop:UpdateState(SkillID, RunningTime, SkillDuration, ChantDurantion)
    self.SkillID = SkillID
    self.CurTime = RunningTime
    self.SkillDuration = SkillDuration
    self.ChantDurantion = ChantDurantion
end


function SkillBtnState_Loop:OnTick()
    self:LoopDisplay(self.SkillID, self.CurTime, self.SkillDuration, self.ChantDurantion)
    self.CurTime = self.CurTime + 0.1
end


function SkillBtnState_Loop:LoopDisplay(SkillID, CurTime, SkillDuration, ChantDuration)
    if CurTime <= SkillDuration then
        local MID = self.view.MID:GetDynamicMaterial()
        MID:SetScalarParameterValue("f_CD_Range", CurTime / SkillDuration)
    else
        -- Loop播完后重回普通状态
        self:ChangeState(SkillBtnState_Base.SkillBtnState.Common)
    end
end


function SkillBtnState_Loop:LeaveState()
    local MID = self.view.MID:GetDynamicMaterial()
    MID:SetScalarParameterValue("i_CD_enable", 0)
end


return SkillBtnState_Loop