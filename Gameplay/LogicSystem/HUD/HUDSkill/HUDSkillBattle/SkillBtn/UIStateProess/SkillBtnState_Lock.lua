local ESlateVisibility = import("ESlateVisibility")
local SkillImpl = kg_require "Gameplay.LogicSystem.HUD.HUDSkill.P_HUDSkillRouttle_Impl"
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local StringConst = require "Data.Config.StringConst.StringConst"

local SkillBtnState_Base = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.HUDSkillBattle.SkillBtn.UIStateProess.SkillBtnState_Base")
local SkillBtnState_Lock = DefineClass("SkillBtnState_Lock", SkillBtnState_Base)
function SkillBtnState_Lock:EnterState(ComboMsg)
    self:UpdateState()
end

function SkillBtnState_Lock:UpdateState()
    if self.SkillID > 0 and Game.SkillCustomSystem:IsSkillLocked(self.SkillID) then
        local Level = Game.SkillCustomSystem:GetSkillUnLockLevel(self.SkillID, 1)
        if Level then
            Level = math.floor(Level)
            self.view.Text_lockLevel:SetVisibility(ESlateVisibility.HitTestInvisible)
            self.view.Text_lockLevel:SetText(string.format(StringConst.Get("NEWBIEPAGE"), Level))
        end
        self:StopAnimation(self.userWidget.Ani_Unlock, self.userWidget)
        --self.view.VB_Lock:SetVisibility(ESlateVisibility.selfHitTestInvisible)
        self.view.MID:GetDynamicMaterial():SetScalarParameterValue("b_Lock_enable", 1)
        self.view.MID:GetDynamicMaterial():SetScalarParameterValue("i_CD_enable", 0)
        self.view.VX_jijue:SetVisibility(ESlateVisibility.Collapsed)
        self.view.MID:GetDynamicMaterial():SetScalarParameterValue("b_Forbid_enable", 0)
        self.view.MID:GetDynamicMaterial():SetScalarParameterValue("Text_ShadowMax", 40)
        self.view.Text_SkillPower:SetVisibility(ESlateVisibility.Collapsed)
        self.view.Text_AboutSkill:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function SkillBtnState_Lock:LeaveState()
    --self.view.VB_Lock:SetVisibility(ESlateVisibility.Collapsed)
    self.view.Text_lockLevel:SetVisibility(ESlateVisibility.Collapsed)
    self.view.MID:GetDynamicMaterial():SetScalarParameterValue("b_Lock_enable", 0)
    local MID = self.view.MID:GetDynamicMaterial()
    MID:SetScalarParameterValue("i_CD_enable", 0)
    self.view.MID:GetDynamicMaterial():SetScalarParameterValue("b_Forbid_enable", 0)
    self.view.MID:GetDynamicMaterial():SetScalarParameterValue("Text_ShadowMax", 10)
    if self.SkillID > 0 then
        -- 有技能且初次解锁
        self:PlayAnimation(self.userWidget.Ani_Unlock, nil, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1,
            true)
    end
    if self.SkillID > 0 and self.SkillSlot == ETE.EBSSkillSlot.SS_ExtraordinarySlot then
        self.view.VX_jijue:SetVisibility(ESlateVisibility.selfHitTestInvisible)
    end
end


return SkillBtnState_Lock