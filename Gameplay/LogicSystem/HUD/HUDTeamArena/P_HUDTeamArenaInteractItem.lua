---@class P_HUDTeamArenaInteractItem : UIController
--- @field public View WBP_HUDPVPInteractView
local P_HUDTeamArenaInteractItem = DefineClass("P_HUDTeamArenaInteractItem", UIComponent)

function P_HUDTeamArenaInteractItem:OnCreate()
	self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_ClickArea, self.OnClickItem)
end

function P_HUDTeamArenaInteractItem:RefreshItem(data, entityId)
    self.entityId = entityId
    self.data = data
    self.View.Name:SetText(data.Text)
end

function P_HUDTeamArenaInteractItem:OnClickItem()
    Game.TeamAreanaSystem.sender:SendPVPBattleOrder(self.data.ID, self.entityId)
end

return P_HUDTeamArenaInteractItem
