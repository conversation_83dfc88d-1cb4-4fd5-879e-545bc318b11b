local ESlateVisibility = import("ESlateVisibility")

local pvpConst = kg_require("Shared.Const.PVPConst")
local ChatUtils = kg_require("Gameplay.LogicSystem.Chat.System.ChatUtils")
local HUDTeamArenaInteractItem = kg_require("Gameplay.LogicSystem.HUD.HUDTeamArena.P_HUDTeamArenaInteractItem")

---@class P_HUDTeamArenaMemberItem : UIController
--- @field public View WBP_HUDPVPHeadView
local P_HUDTeamArenaMemberItem = DefineClass("P_HUDTeamArenaMemberItem", UIComponent)
--阵营类型
P_HUDTeamArenaMemberItem.CampUIType =
{
    [pvpConst.PVP_CAMP_TYPE.BLUE] = 0,
    [pvpConst.PVP_CAMP_TYPE.RED] = 1,
}

P_HUDTeamArenaMemberItem.eventBindMap = {
   [EEventTypes.TEAM_ARENA_MEMBER_SELECT_CHANGE] = "OnTeamArenaMemberSelectChange",
   [EEventTypes.TEAM_ARENA_MEMBER_STATUS_CHANGE] = "OnTeamMemberStatusChange",
   [EEventTypes.TEAM_ARENA_MEMBER_HEAD_STATUS_CHANGE] = "OnTeamMemberHeadStatusChange",
}

function P_HUDTeamArenaMemberItem:OnCreate()
    self:initUI()
end

function P_HUDTeamArenaMemberItem:initData()

end

function P_HUDTeamArenaMemberItem:initUI()
	self:AddUIListener(EUIEventTypes.CLICK, self.View.Btn_CLickArea, self.OnClickHead)
    self.interactList = BaseList.CreateList(self, BaseList.Kind.GroupView, "InteractContent", HUDTeamArenaInteractItem)
end

function P_HUDTeamArenaMemberItem:RefreshItem(member, teamId)
    self:HideInteract()
    self.member = member
    self:SetHeadIcon(self.member.ProfessionID)
    self:SetPlayerName(self.member)
    self:SetInteractList()
    self:RefreshPlayerHP()
    self:RefreshPlayerStatue()
    self:StartHpRefreshTimer()
    self:SetHighLightStatus(Game.TeamAreanaSystem:GetHeadIconState(self.member.ID), self:GetCampType())
end

function P_HUDTeamArenaMemberItem:SetHeadIcon(ProfessionID)
	local professionRow = Game.TableData.GetPlayerSocialDisplayDataRow(ProfessionID)
	if professionRow then
		local OptionClassInfo = professionRow[0]
		self:SetImage(self.View.HeadIcon, OptionClassInfo.TeamHeadIcon)
	end
end

function P_HUDTeamArenaMemberItem:SetWanted(isWanted)
    self.View:SetWanted(isWanted)
end

function P_HUDTeamArenaMemberItem:GetCampType()
    if self.member.teamId == Game.me.teamID then
        return pvpConst.PVP_CAMP_TYPE.BLUE
    else
        return pvpConst.PVP_CAMP_TYPE.RED
    end
end

function P_HUDTeamArenaMemberItem:RefreshPlayerStatue()
    local selfStatus = self.member.Status
    local statusEnum = pvpConst.PVP_INNER_MEMBER_STATUS
	local bDead = selfStatus == statusEnum.INIT or selfStatus == statusEnum.DEAD or selfStatus == statusEnum.QUIT or selfStatus == statusEnum.ESCAPE
	if bDead and self._isShowInteract then
		self:HideInteract()
	end
    self.View:SetDead(bDead)
    self.View:SetOB(selfStatus == statusEnum.DEAD and Game.TeamAreanaSystem:IsIn3v3TeamArena())
end

function P_HUDTeamArenaMemberItem:SetPlayerName(Member)
	---检查是不是机器人，机器人的昵称需要单独获取
	if Member.BotID and Member.BotID > 0 then
		local Name = Game.TableData.GetBattleBotTemplateDataRow(Member.BotID).Name
		if Name ~= nil then
			self.View.Name:SetText(ChatUtils.GetNameAbbreviation(Name, 3))
			return
		end
	end
	self.View.Name:SetText(ChatUtils.GetNameAbbreviation(Member.Name, 3))
end

function P_HUDTeamArenaMemberItem:SetHighLightStatus(headType, camp)
    self.View:SetType(headType, self.CampUIType[camp])
    if headType ~= 0 then
        if camp == pvpConst.PVP_CAMP_TYPE.BLUE then
            self:PlayAnimationForward(self.View.WidgetRoot, self.View.ani_loop_blue)
        else
            self:PlayAnimationForward(self.View.WidgetRoot, self.View.ani_loop_red)
        end
    else
        self:StopAnimation(self.View.WidgetRoot, self.View.ani_loop_red)
        self:StopAnimation(self.View.WidgetRoot, self.View.ani_loop_blue)
        self:PlayAnimationForward(self.View.WidgetRoot, self.View.ani_out)
    end
end

function P_HUDTeamArenaMemberItem:RefreshPlayerHP()
    local entity = Game.EntityManager:getEntity(self.member.ID)
    local maxHP = 1
    local hp = 1
    if entity ~= nil then
        maxHP = entity.MaxHp
        hp = entity.Hp
    end
    self.MaxHP = maxHP or 1
    if self.MaxHP <=0 then
        self.MaxHP = 1
    end
    self.HP = hp or 1
    local curProgress = self.HP/self.MaxHP
    if curProgress ~= self.hpProgress then
        self.hpProgress = curProgress
        self.View:SetHP(self.hpProgress)
    end
end

function P_HUDTeamArenaMemberItem:StartHpRefreshTimer()
        -- luacheck: push ignore
    local func = function() self:RefreshPlayerHP() end
    self:StartTimer("StartHpRefreshTimer", func, 50, -1, nil, true)
        -- luacheck: pop
end

function P_HUDTeamArenaMemberItem:SetInteractList()
    local data = self:GetInteractData()
    self.interactList:SetData(#data)
end

function P_HUDTeamArenaMemberItem:GetInteractData()
    local datas = Game.TableData.GetPVPBattleOrderDataTable()
    local interacts = {}
    for key, value in ksbcpairs(datas) do
        if self.member.teamId == Game.me.teamID then
            if value.CampLimit == pvpConst.PVP_CAMP_TYPE.BLUE then
                interacts[#interacts + 1] = value
            end
        else
            if value.CampLimit == pvpConst.PVP_CAMP_TYPE.RED then
                interacts[#interacts + 1] = value
            end
        end
    end
    return interacts
end

function P_HUDTeamArenaMemberItem:ShowInteract()
    self._isShowInteract = true
    Game.EventSystem:Publish(EEventTypes.TEAM_ARENA_MEMBER_SELECT_CHANGE, self.member.ID)
    self.View.InteractWidget:SetVisibility(ESlateVisibility.Visible)
end

function P_HUDTeamArenaMemberItem:HideInteract()
    self._isShowInteract = false
    self.View.InteractWidget:SetVisibility(ESlateVisibility.Hidden)
    self.View:SetSelected(false)
end

function P_HUDTeamArenaMemberItem:OnRefresh_InteractContent(widget, index, selected)
    local datas = self:GetInteractData()
    widget:RefreshItem(datas[index], self.member.ID)
end

function P_HUDTeamArenaMemberItem:OnClickHead()
	--self.member.ID
    if self._isShowInteract or 
		self.member.Status == pvpConst.PVP_INNER_MEMBER_STATUS.INIT or 
		self.member.Status == pvpConst.PVP_INNER_MEMBER_STATUS.DEAD or 
		self.member.Status == pvpConst.PVP_INNER_MEMBER_STATUS.QUIT or 
		self.member.Status == pvpConst.PVP_INNER_MEMBER_STATUS.ESCAPE then
        self:HideInteract()
    else
        self:ShowInteract()
    end
    Game.TargetLockSystem.ChangeLockTargetByEID(self.member.ID)
end

function P_HUDTeamArenaMemberItem:OnTeamArenaMemberSelectChange(entityId)
    if entityId ~= self.member.ID then
        self:HideInteract()
    end
end

function P_HUDTeamArenaMemberItem:OnTeamMemberStatusChange(entityId)
    if entityId == self.member.ID then
        self:RefreshPlayerStatue()
    end
end

function P_HUDTeamArenaMemberItem:OnTeamMemberHeadStatusChange(entityId)
    if entityId == self.member.ID then
        self:SetHighLightStatus(Game.TeamAreanaSystem:GetHeadIconState(entityId), self:GetCampType())
    end
end

return P_HUDTeamArenaMemberItem
