local EDPIScalePreviewPlatforms = import("EDPIScalePreviewPlatforms")

---@class HUDSystem
local HUDSystem = DefineSingletonClass("HUDSystem", SystemBase)
local NetworkManager = require "Framework.DoraSDK.NetworkManager"
local worldConst = kg_require "Shared.Const.WorldConst"

HUDSystem.MonsterBloodPresenter = nil

HUDSystem.LastNPC = nil
HUDSystem.BloodUITimerHandle = {}
HUDSystem.MonsterBloodPresenterList = {}
HUDSystem.InteractDatas = {}
HUDSystem.TracingBoss = {}

HUDSystem.ResetTimer = nil

--- 头顶NoticeType
HUDSystem.NoticeType = {
    Team = 1,
    Guild = 2,
}

-- 记录子节点之间冲突关系导致的隐藏信息
HUDSystem.HideConflictUIMap = {}

-- 常显功能激活状态记录
HUDSystem.ConstantActiveMap = {}

function HUDSystem.GetHUD()
end

function HUDSystem.IsInPC()
    if import("C7FunctionLibrary").IsC7Editor() then
        if import("UIFunctionLibrary").GetPreviewPlatform and import("UIFunctionLibrary").GetPreviewPlatform() == EDPIScalePreviewPlatforms.PC then
            return true
        else
            return false
        end
    else
        if PlatformUtil.IsMobilePlatform() then
            return false
        else
            return true
        end
    end
end

function HUDSystem.GetHPBarStyle(ConfigID)
    local MonsterData = Game.TableData.GetMonsterDataTable()
    local NumConfigID = tonumber(ConfigID)
    local NpcData = MonsterData[NumConfigID]
    if NpcData then
        if NpcData.HpBarType == Enum.EHPBarType.Carriage then
            return "Carriage"
        elseif NpcData.HpBarType == Enum.EHPBarType.Boss then
            return "Boss"
        elseif NpcData.HpBarType == Enum.EHPBarType.Elite then
            return "Elite"
        end
    end
    return nil
end

function HUDSystem.Receive_LEVEL_ON_LEVEL_LOADED()
    if Game.NetworkManager.GetLocalSpace() then
        local LineType = Game.NetworkManager.GetLocalSpace().LineType
        if LineType == 1 then
            Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.LINE_FIGHTLINE_ENTER
        )
        end
    end
end

function HUDSystem:IsInQte(Params)
    return HUDSystem.InQTE
end

function HUDSystem:OnStartQTE(InQTEObject)
    HUDSystem.InQTE = true
	Game.GlobalEventSystem:Publish(EEventTypesV2.HUD_POST_QTE_STATE_CHANGED)
end

function HUDSystem:OnEndQTE(InQTEObject)
    HUDSystem.InQTE = false
	Game.GlobalEventSystem:Publish(EEventTypesV2.HUD_POST_QTE_STATE_CHANGED)
end

function HUDSystem.Init()
    HUDSystem.InQTE = false
    Game.EventSystem:AddListener(_G.EEventTypes.ROLE_ON_VISABLE_CHANGED, HUDSystem, HUDSystem.OnRoleVisibilityChanged)
    Game.EventSystem:AddListener(_G.EEventTypes.ROLE_ON_DEAD, HUDSystem, HUDSystem.OnActorDead)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_LEVEL_LOADED, "Receive_LEVEL_ON_LEVEL_LOADED", HUDSystem)
    Game.BSQTEManager.OnAnyQTEStarted:Add(HUDSystem, "OnStartQTE")
    Game.BSQTEManager.OnAnyQTEEndded:Add(HUDSystem, "OnEndQTE")

    -- 技能提醒使用的ID缓存
    HUDSystem.RemindUseSkillMap = {} -- table(int, int)
    Game.EventSystem:AddListener(_G.EEventTypes.SKILLHUD_REMINDUSESKILL_CHANGED, HUDSystem,
        HUDSystem.OnRemindUseSkillChanged)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ON_DESTROY, "OnRoleDestroy", HUDSystem)
	-- 处理下模块锁的问题
	Game.GlobalEventSystem:AddListener(EEventTypesV2.MODULE_LOCK_CHANGE, "ResetHUD", HUDSystem)
end

function HUDSystem.UnInit()
    Game.EventSystem:RemoveObjListeners(HUDSystem)
	Game.GlobalEventSystem:RemoveTargetAllListeners(HUDSystem)
    for key, value in ipairs(HUDSystem.BloodUITimerHandle) do
        Game.TimerManager:StopTimerAndKill(HUDSystem.BloodUITimerHandle[value])
        HUDSystem.BloodUITimerHandle[value] = nil
    end

    Game.BSQTEManager.OnAnyQTEStarted:Remove(HUDSystem, "OnStartQTE")
    Game.BSQTEManager.OnAnyQTEEndded:Remove(HUDSystem, "OnEndQTE")
end

function HUDSystem:ShowHUD()
    --创建新版UI框架HUD
    Game.NewUIManager:OpenPanel(UIPanelConfig.HUD_Panel)

    -- --创建HUDInteract
    Game.HUDInteractManager:ShowHUDInteract()
end

function HUDSystem:RemoveHUD()
    Game.NewUIManager:ClosePanel(UIPanelConfig.HUD_Panel)
end

function HUDSystem.ReqGetLineList(templateID)
    if Game.me then
        Game.me:ReqLineList(templateID)
    end
end

--function HUDSystem:GetSub()
--	
--end

function HUDSystem.GetCurrentLineID()
    local CurrentSpace = NetworkManager.GetLocalSpace()
    if CurrentSpace then
        return CurrentSpace.eid
    end
end

function HUDSystem.GetCurrentLineWorldID()
    local CurrentSpace = NetworkManager.GetLocalSpace()
    if CurrentSpace then
        return CurrentSpace.WorldID
    end
end

function HUDSystem.GetCurrentLineWorldLineType()
    local CurrentSpace = NetworkManager.GetLocalSpace()
    if CurrentSpace then
        return CurrentSpace.LineType
    end
end

-- TOFIX: 这里有重复监听的情况， 在BBBManager里面有相同的逻辑，考虑干掉一个
function HUDSystem.OnRoleVisibilityChanged(_, uid, bVisible)
	local entity = Game.EntityManager:getEntity(uid)
    if entity and entity.isNpc then
		local NpcData = entity:GetEntityConfigData()
		if NpcData and (NpcData.HpBarType == Enum.EHPBarType.Boss or NpcData.HpBarType == Enum.EHPBarType.Elite) then
            if bVisible then
            else
                HUDSystem.RemoveBossInfo(uid)
            end
        end
    end
end

function HUDSystem.OnRoleDestroy(_, uid)
	local entity = Game.EntityManager:getEntity(uid)
    if entity then
		if entity and entity.isNpc == true then
			if HUDSystem.GetHPBarStyle(entity.TemplateID) == "Boss" or HUDSystem.GetHPBarStyle(entity.TemplateID) == "Elite" then
				HUDSystem.RemoveBossInfo(entity:uid())
			end
		end
    end
end

function HUDSystem.OnActorDead(_, uid)
	local entity = Game.EntityManager:getEntity(uid)
    if entity then
		if entity and entity.isNpc == true then
			if HUDSystem.GetHPBarStyle(entity.TemplateID) == "Boss" or HUDSystem.GetHPBarStyle(entity.TemplateID) == "Elite" then
				HUDSystem.RemoveBossInfo(entity:uid())
			end
		end
    end
end

function HUDSystem.AddMonsterInfo(uid, TargetType)
    HUDSystem.TracingBoss = {}
    table.insert(HUDSystem.TracingBoss, uid)
    if not UI.IsShow("P_HUDBossInfo") then
        Game.HUDSystem:ShowUI("P_HUDBossInfo")
    else -- 当前HUDBossInfo UI已经在显示了，直接做一遍刷新
        local BossUI = UI.GetUI("P_HUDBossInfo")
        BossUI:OnRefresh()
    end
end

function HUDSystem.RemoveBossInfo(delUID)
    for Index, uid in pairs(HUDSystem.TracingBoss) do
        if delUID == uid then
            table.remove(HUDSystem.TracingBoss, Index)
            break
        end
    end
    if not next(HUDSystem.TracingBoss) then
        Game.HUDSystem:HideUI("P_HUDBossInfo")
    end
    -- Game.EventSystem:Publish(_G.EEventTypes.BOSS_INFO_REMOVE_BOSS, BossActor)
end

----------------------- HUD  Skill Begin ------------------------------------------------------------

HUDSystem.JumpMissType = {
    Jump = 0,
    Dodge = 1
}
----------------------- HUD  Skill End ------------------------------------------------------------



-- region RemindUseSkill
function HUDSystem:OnRemindUseSkillChanged(InSkillID, bAddOrRemove)
    if HUDSystem.RemindUseSkillMap[InSkillID] == nil and bAddOrRemove then
        HUDSystem.RemindUseSkillMap[InSkillID] = 1
    else
        HUDSystem.RemindUseSkillMap[InSkillID] = HUDSystem.RemindUseSkillMap[InSkillID] + (bAddOrRemove and 1 or -1)
        if HUDSystem.RemindUseSkillMap[InSkillID] <= 0 then
            HUDSystem.RemindUseSkillMap[InSkillID] = nil
        end
    end

    Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_REMINDUSESKILL_UPDATE, InSkillID)
end

function HUDSystem.IsSkillRemindToUse(InSkillID)
    return HUDSystem.RemindUseSkillMap[InSkillID] ~= nil
end

---@param Target Character 目标Character

function HUDSystem.GetCampColorType(uid)
    local EPlayerNameColorType = Enum.EPlayerNameColorType
    local OwnerEID = Game.me:uid()
	--Brief Actor需要一些头顶显示的颜色信息 @hujianglong
    local TargetRpcEntity = Game.EntityManager:getEntityWithBrief(uid)
    local CamRelation = BSFunc.GetFinalCampRelation(Game.me, TargetRpcEntity)

    ---老的阵营一套 颜色对应关系是 self = 天蓝色; enemy = 红色; Friendly = 绿色; Neutral = 白色。 这次改动self也改成绿色了 其他不变
    if OwnerEID == TargetRpcEntity:uid() then
        return EPlayerNameColorType.GREEN
    elseif CamRelation == Enum.ECampEnumData.Friendly then
        return EPlayerNameColorType.GREEN
    elseif CamRelation == Enum.ECampEnumData.Neutral then
        return EPlayerNameColorType.None
    elseif CamRelation == Enum.ECampEnumData.Enemy then
        return EPlayerNameColorType.RED
    else
        Log.Warning("HUDSystem CheckTargetHostileColorType Other Reason!")
        return EPlayerNameColorType.None
    end
end

function HUDSystem.CheckTargetActorColorType(uid)
	local EPlayerNameColorType = Enum.EPlayerNameColorType

	if nil == Game.me then
		Log.Warning("HUDSystem CheckTargetHostileColorType MainPlayer Character is nil!")
		return EPlayerNameColorType.None
	end
	--Brief Actor需要一些头顶显示的颜色信息 @hujianglong
	local TargetRpcEntity = Game.EntityManager:getEntityWithBrief(uid)
	if nil == TargetRpcEntity then
		Log.Warning("HUDSystem CheckTargetHostileColorType Target's RpcEntity is nil!")
		return EPlayerNameColorType.None
	end
	
	return HUDSystem.GetCampColorType(TargetRpcEntity:uid())
end

function HUDSystem.GetTargetActorBountyColorType(uid)
	--Brief Actor需要一些头顶显示的颜色信息 @hujianglongn
    local TargetRpcEntity = Game.EntityManager:getEntityWithBrief(uid)
    if nil == TargetRpcEntity then
        Log.Warning("HUDSystem CheckTargetHostileColorType Target's RpcEntity is nil!")
        return Enum.EPlayerNameColorType.None
    end
    ---进行悬赏值逻辑判断
    local targetBounty = TargetRpcEntity.Bounty
    local redNameStageTable = Game.TableData.GetRedNameStageDataTable()

    if targetBounty == nil then
        Log.Warning("HUDSystem CheckTargetHostileColorType targetBounty is nil!")
        return HUDSystem.GetCampColorType(TargetRpcEntity:uid())
    end

    if targetBounty >= redNameStageTable["PURPLISHRED"].NumericalDuration[1] then
        return Enum.EPlayerNameColorType.PURPLISHRED
    elseif targetBounty >= redNameStageTable["RED"].NumericalDuration[1] then
        return Enum.EPlayerNameColorType.RED
    elseif targetBounty >= redNameStageTable["ORANGERED"].NumericalDuration[1] then
        return Enum.EPlayerNameColorType.ORANGERED
    elseif targetBounty >= redNameStageTable["ORANGEYELLOW"].NumericalDuration[1] then
        return Enum.EPlayerNameColorType.ORANGEYELLOW
    elseif targetBounty >= redNameStageTable["YELLOW"].NumericalDuration[1] then
        local yellowNameTime = TargetRpcEntity.YellowNameTime or 0
        if yellowNameTime > 0 then
            local now = math.floor(_G._now() / 1000)
            if (now - yellowNameTime) < Enum.ERedNameConstIntData.YELLOW_DURATIONTIME then
                return Enum.EPlayerNameColorType.YELLOW
            end
        end
        return HUDSystem.GetCampColorType(TargetRpcEntity:uid())
    end
end

-- endregion RemindUseSkill

function HUDSystem:OnTakeDamage(InContext)
    if not Game.me then 
        Log.Warning("[HUDSyatem_OnTakeDamage] Game.me is nil.")
        return 
    end
    -- 前置阻断，非自己相关的伤害直接return掉
    if InContext.InstigatorID ~= Game.me.eid or not InContext.IsHeal then
        return
    end
    -- 更新HUDTeamUI
    local HUDTeamUI = UI.GetUI(UICellConfig.HUD_Team)
    if HUDTeamUI then
        HUDTeamUI:OnTakeDamage(InContext)
    end
    local P_HUDGroupUI = UI.GetUI(UICellConfig.HUD_Group)
    if P_HUDGroupUI then
        P_HUDGroupUI:OnTakeDamage(InContext)
    end
end

function HUDSystem.GetLineContentText(lineType, lineId)
    if lineType == 0 then
        return "安全" .. (lineId % worldConst.MAX_LINE_NUM + 1) // 2
    else
        return "战斗" .. (lineId % worldConst.MAX_LINE_NUM) // 2
    end
end

-- QTE定制接口: 隐藏
function HUDSystem:HideUIsForQTE()
    if UI.IsShow("P_HUDJoyStick") then
        UI.HideUI("P_HUDJoyStick")
    end
	Game.NewUIManager:ClosePanel(UIPanelConfig.HUDSkillRouletteTotal)
end

------------------------------------------- HUD System Child UI Show/Hide Logic ----------------------------------------
function HUDSystem:ShowUI(UIName, ...)
    if not Game.NewUIManager:IsOpened(UIPanelConfig.HUD_Panel) then
        return
    end
    Game.NewUIManager:InvokePanel(UIPanelConfig.HUD_Panel, "ShowChildComponent", UIName, ...)
end

function HUDSystem:HideUI(UIName)
    if not Game.NewUIManager:IsOpened(UIPanelConfig.HUD_Panel) then
        return
    end
    Game.NewUIManager:InvokePanel(UIPanelConfig.HUD_Panel, "HideChildComponent", UIName)
end

-- 重置HUD页面，纯粹依赖ShowRules，不考虑互斥关系
function HUDSystem:ResetHUD()
    if not Game.NewUIManager:IsOpened(UIPanelConfig.HUD_Panel) then
        return
    end
    Game.NewUIManager:InvokePanel(UIPanelConfig.HUD_Panel, "ResetChildComponents")
end

function HUDSystem:PlayerRightTopAni(bOpen)
    if not Game.NewUIManager:IsOpened(UIPanelConfig.HUD_Panel) then
        return
    end
    Game.NewUIManager:InvokePanel(UIPanelConfig.HUD_Panel, "PlayRightTopUIAnim", bOpen)
end

function HUDSystem:PlayerLeftTopAni(bOpen)
	if not Game.NewUIManager:IsOpened(UIPanelConfig.HUD_Panel) then
		return
	end
	Game.NewUIManager:InvokePanel(UIPanelConfig.HUD_Panel, "PlayLeftTopUIAnim", bOpen)
end

-- 执行子HUD的funcName方法（如果子HUD未打开则无效）
function HUDSystem:InvokeSubHUDFunc(cellId, funcName, ...)
	Game.NewUIManager:InvokePanel(UIPanelConfig.HUD_Panel, "InvokeChildHUD", cellId, funcName, ...)
end
------------------------------------------- HUD System Child UI Show/Hide Logic End ----------------------------------------

function HUDSystem:SetConstantActive(idx, isActive)
	self.ConstantActiveMap[idx] = isActive
end
function HUDSystem:GetConstantActive(idx)
	return self.ConstantActiveMap[idx] or false
end
function HUDSystem:ImmerseMode()
	self:HideUI("P_HUDChat")
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_HUD_IMMERSE, true)
end
function HUDSystem:QuitImmerse()
	self:ShowUI("P_HUDChat")
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_HUD_IMMERSE, false)
end

return HUDSystem
