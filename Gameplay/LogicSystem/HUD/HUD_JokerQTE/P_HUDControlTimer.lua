local ESlateVisibility = import("ESlateVisibility")

local P_HUDControlTimer = DefineClass("P_HUDControlTimer", UIController)
local StringConst = require "Data.Config.StringConst.StringConst"


function P_HUDControlTimer:ctor()
    self.ProgressBar = {}
    self.TitleText = {}
    self.ShieldName = "aShield"
    self.BuffID = 0
    self.NpcEntityID = 0
    self.ShieldBuff = 0
    self.InitShield = 0
end

function P_HUDControlTimer:OnRefresh(Params)
    self.ProgressBar = self.View.PB_Control
    self.TitleText = self.View.Text

    --默认隐藏
    local ProgressBarVisible = ESlateVisibility.Collapsed
    --Buff剩余时间
    if (Params.BuffID ~= nil) then
        --默认隐藏
        self.BuffID = tonumber(Params.BuffID)
        ProgressBarVisible = ESlateVisibility.SelfHitTestInvisible
    end

    --支持配置OpenUI的文字Title
    if (Params.Title ~= nil) then
        self.TitleText:SetText(StringConst.Get(Params.Title))
    end

    --护盾值
    if (Params.ShieldBuff ~= nil) then
        ProgressBarVisible = ESlateVisibility.SelfHitTestInvisible
        self.ShieldBuff = tonumber(Params.ShieldBuff)
        local NpcEntities = Game.EntityManager:getEntitiesByType(EEntityType.NpcActor)
        for _, NpcEntity in pairs(NpcEntities) do
            if (NpcEntity and NpcEntity:GetBuffLayerNew(self.ShieldBuff) > 0) then
                self.NpcEntityID = NpcEntity.eid
                self.InitShield = NpcEntity[self.ShieldName]
                break
            end
        end
    end
    self.ProgressBar:SetVisibility(ProgressBarVisible)

    self:StartTimer("TimerHandle", function() self:Timer() end, 16, -1, nil, true)
end

function P_HUDControlTimer:Timer()
    if (self.BuffID ~= 0) and (Game.me ~= nil) then
        --显示剩余Buff时间
        local tBuffInsList = Game.me:GetBuffInstanceByBuffIDNew(self.BuffID)
        if tBuffInsList ~= nil then
            local TotalLife, CurLife = tBuffInsList:GetLifeMessage(nil, nil)
            self.ProgressBar:SetPercent((TotalLife - CurLife) / TotalLife)
        else
            Game.HUDSystem:HideUI("P_HUDControlTimer")
        end
    elseif self.NpcEntityID ~= 0 then
        --更新目标对象的属性值
        local entity = Game.EntityManager:getEntity(self.NpcEntityID)
        local AttributeValue = entity[self.ShieldName]
        if (AttributeValue == nil or AttributeValue == 0) then
            Game.HUDSystem:HideUI("P_HUDControlTimer")
        else
            if (self.InitShield == 0) then
                self.InitShield = AttributeValue
            end
            self.ProgressBar:SetPercent(AttributeValue / self.InitShield)
        end
    end
end

function P_HUDControlTimer:OnClose()
	--清除定时器
	self:StopTimer("TimerHandle")
end

return P_HUDControlTimer
