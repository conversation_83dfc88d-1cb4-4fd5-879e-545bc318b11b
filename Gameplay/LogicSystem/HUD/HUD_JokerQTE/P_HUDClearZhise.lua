local EUMGSequencePlayMode = import("EUMGSequencePlayMode")


local P_HUDClearZhise = DefineClass("P_HUDClearZhise", UIController)
local EInputEvent = import("EInputEvent")
local ESlateVisibility = import("ESlateVisibility")
P_HUDClearZhise.BIND_KEY_CONST = {"Q", "W", "E", "R", "A", "S", "D", "F"}
P_HUDClearZhise.RandomIndex = {1, 2, 3, 4, 5, 6, 7, 8}
P_HUDClearZhise.QTEInput = {"QTE_01_Action", "QTE_02_Action", "QTE_03_Action", "QTE_04_Action", "QTE_05_Action", "QTE_06_Action", "QTE_07_Action", "QTE_08_Action",}

function P_HUDClearZhise:OnCreate()
    --local Widget = self.View.WidgetRoot
    --self:PlayAnimation(Widget, Widget.An_Prompt, 0.0, 0, import("EUMGSequencePlayMode").Forward, 1, false)
    --绑定左按钮点击事件
    self:AddUIListener(EUIEventTypes.Pressed, self.View.WBP_HUDQTE_BtnL.Btn_Qte, self.OnLeftPressed)
    self:AddUIListener(EUIEventTypes.Pressed, self.View.WBP_HUDQTE_BtnR.Btn_Qte, self.OnRightPressed)

    --绑定右按钮点击事件
    self:AddUIListener(EUIEventTypes.Released, self.View.WBP_HUDQTE_BtnL.Btn_Qte, self.OnLeftReleased)
    self:AddUIListener(EUIEventTypes.Released, self.View.WBP_HUDQTE_BtnR.Btn_Qte, self.OnRightReleased)

    self:BindButtonAction(self.View.WBP_HUDQTE_BtnL.Btn_Qte, 5)
    self:BindButtonAction(self.View.WBP_HUDQTE_BtnR.Btn_Qte, 7)
end

function P_HUDClearZhise:OnRefresh(Params)
    if Params then
        self.QTEObject = Params.QTEObj
    end
    --self:PlayAnimation(self.View.WBP_HUDQTE_BtnL.WidgetRoot, self.View.WBP_HUDQTE_BtnL.Ani_Left_loop, 0.0, 0, import("EUMGSequencePlayMode").Forward, 1.0, false)
    --self:PlayAnimation(self.View.WBP_HUDQTE_BtnR.WidgetRoot, self.View.WBP_HUDQTE_BtnR.Ani_Right_loop, 0.0, 0, import("EUMGSequencePlayMode").Forward, 1.0, false)
    if not self.bIsMobile then
        self:BindInput()
    end
end

function P_HUDClearZhise:OnClose()
    UIBase.OnClose(self)
    if not self.bIsMobile then
        self:UnBindInput()
    end
end

function P_HUDClearZhise:OnLeftPressed()
    --self:StopAnimation(self.View.WBP_HUDQTE_BtnL.WidgetRoot, self.View.WBP_HUDQTE_BtnL.Ani_Left_loop)
    self:StopAnimation(self.View.WBP_HUDQTE_BtnL.WidgetRoot, self.View.WBP_HUDQTE_BtnL.Ani_Feedback)
    self:PlayAnimation(self.View.WBP_HUDQTE_BtnL.WidgetRoot, self.View.WBP_HUDQTE_BtnL.Ani_Press, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false)

end
function P_HUDClearZhise:OnRightPressed()
    --self:StopAnimation(self.View.WBP_HUDQTE_BtnR.WidgetRoot, self.View.WBP_HUDQTE_BtnR.Ani_Right_loop)
    self:StopAnimation(self.View.WBP_HUDQTE_BtnR.WidgetRoot, self.View.WBP_HUDQTE_BtnR.Ani_Feedback)
    self:PlayAnimation(self.View.WBP_HUDQTE_BtnR.WidgetRoot, self.View.WBP_HUDQTE_BtnR.Ani_Press, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false)
end
function P_HUDClearZhise:OnLeftReleased()
    --self:StopAnimation(self.View.WBP_HUDQTE_BtnL.WidgetRoot, self.View.WBP_HUDQTE_BtnL.Ani_Left_loop)  
    self:StopAnimation(self.View.WBP_HUDQTE_BtnL.WidgetRoot, self.View.WBP_HUDQTE_BtnL.Ani_Press) 
    self:PlayAnimation(self.View.WBP_HUDQTE_BtnL.WidgetRoot, self.View.WBP_HUDQTE_BtnL.Ani_Feedback, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false)
    if self.QTEObject then
        self.QTEObject:ButtonDown()
    end
end
function P_HUDClearZhise:OnRightReleased()
    --self:StopAnimation(self.View.WBP_HUDQTE_BtnR.WidgetRoot, self.View.WBP_HUDQTE_BtnR.Ani_Left_loop)  
    self:StopAnimation(self.View.WBP_HUDQTE_BtnR.WidgetRoot, self.View.WBP_HUDQTE_BtnR.Ani_Press) 
    self:PlayAnimation(self.View.WBP_HUDQTE_BtnR.WidgetRoot, self.View.WBP_HUDQTE_BtnR.Ani_Feedback, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false)
    if self.QTEObject then
        self.QTEObject:ButtonDown()
    end
end
function P_HUDClearZhise:BindInput()
    EnableQTEInput(true)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.ROLE_ACTION_INPUT_EVENT, "OnKeyInput", self)
end

function P_HUDClearZhise:UnBindInput()
    Game.GlobalEventSystem:RemoveTargetAllListeners(self)
    EnableQTEInput(false)
end

function P_HUDClearZhise:OnKeyInput(EventName, EventType)
    
    if Enum.EInputTypeQTEGroup[EventName] == nil or (EventType ~= EInputEvent.IE_Pressed and EventType ~= EInputEvent.IE_Released) then
        return
    end
    
    -- local ActionButtonList = self.InputButtonMap[EventName]
    -- if ActionButtonList and next(ActionButtonList) ~= nil then
    --     ActionButtonList[1]:OnButtonClicked(self) 
    --     table.remove(ActionButtonList, 1)
    -- end
    if EventName == "QTE_05_Action" then
        if EventType == EInputEvent.IE_Pressed then
            self:OnLeftPressed()
        elseif EventType == EInputEvent.IE_Released then
            self:OnLeftReleased()
        end
    elseif "QTE_07_Action" then
        if EventType == EInputEvent.IE_Pressed then
            self:OnRightPressed()
        elseif EventType == EInputEvent.IE_Released then
            self:OnRightReleased()
        end
    end
    
end

function P_HUDClearZhise:BindButtonAction()
    if not self.bIsMobile then   
        self.View.WBP_HUDQTE_BtnL.Text_Btn:SetText(P_HUDClearZhise.BIND_KEY_CONST[5])
        self.View.WBP_HUDQTE_BtnR.Text_Btn:SetText(P_HUDClearZhise.BIND_KEY_CONST[7])
        --table.insert(self.InputButtonMap[P_HUDClearZhise.QTEInput[keyIndex]], keyIndex)
    else
        self.View.WBP_HUDQTE_BtnR.Text_Btn:SetText("")
    end

end


return P_HUDClearZhise
