local ESlateVisibility = import("ESlateVisibility")


local P_HUDInteractTrace= DefineClass("P_HUDInteractTrace",WorldWidgetCellBase)

function P_HUDInteractTrace:ctor()
    self.bOnEdge = nil
    self.TargetUID = nil

    self.ActorLoc = nil
    self.TickTimer = nil
    local ViewportSize = import("WidgetLayoutLibrary").GetViewportSize(_G.GetContextObject())
    local ViewportScale = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
    ViewportSize = ViewportSize / ViewportScale
    self.halfSizeX = ViewportSize.X/2
    self.halfSizeY = ViewportSize.Y/2
    self.translation = FVector2D(0, 0)
end

function P_HUDInteractTrace:OnEdgeChanged(bOnEdge)
    self.bOnEdge = bOnEdge
    if not bOnEdge then
        self.view.icon_trace_lua:SetVisibility(ESlateVisibility.Visible)
    else
        self.view.icon_trace_lua:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function P_HUDInteractTrace:OnGetFromPool(Params)
    self.TargetUID = Params.TargetUID
    local icon
    if Params.TraceIcon then
        icon = Game.HUDInteractManager.typePathList[Params.TraceIcon]
    else
        icon = Params.TraceIconPath
    end

    if icon then
        self:SetImage(self.view.icon_trace_lua, icon)
    else
        self:SetImage(self.view.icon_trace_lua, Game.HUDInteractManager.typePathList[Enum.ESpiritualIconType.Question])
    end
end

function P_HUDInteractTrace:OnReturnToPool()
    self:StopAllTimer()
    self.ActorLoc = nil
end

return P_HUDInteractTrace
