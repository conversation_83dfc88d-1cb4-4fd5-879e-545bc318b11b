local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUD_Weather : UIComponent
---@field view HUD_WeatherBlueprint
local HUD_Weather = DefineClass("HUD_Weather", UIComponent)
local WeatherEnum = Game.ClimateSystem.Enum
local ESlateVisibility = import("ESlateVisibility")
local KismetInputLibrary = import("KismetInputLibrary")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")

HUD_Weather.eventBindMap = {
	[EEventTypesV2.ON_GAME_TIME_CLOCK_CHANGE] = "RefreshClock",
	[EEventTypesV2.ON_UI_OPEN] = "OnUIOpen",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUD_Weather:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUD_Weather:InitUIData()
	---@type table, 游戏内时间
	self.time = nil
	--分针旋转角度
	self.min_angle = 0
	--时针旋转角度
	self.hour_angle = 0
end

--- UI组件初始化，此处为自动生成
function HUD_Weather:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUD_Weather:InitUIEvent()
    self:AddUIEvent(self.view.Click_area.OnClicked, "on_Click_area_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUD_Weather:InitUIView()
end

function HUD_Weather:OnOpen()
	self:PlayAnimation(self.view.Ani_Fadein)
	Game.UIInputProcessorManager:BindMouseButtonUpEvent(self, "OnMouseButtonUp")
	local time, timePeriod, min_angle, hour_angle = Game.ClimateSystem.ClockTimeTick()
	self:RefreshClock(time, timePeriod, min_angle, hour_angle)
	self:StartTimer("HUDWeatherAutoClose", function()
		Game.ClimateSystem:HUDWeather2HUDMiniMap()
	end, 10000, 1)
end

function HUD_Weather:on_Click_area_Clicked()
	if not Game.NewUIManager:IsShow(UIPanelConfig.Weather_Panel) then
		Game.NewUIManager:OpenPanel(UIPanelConfig.Weather_Panel)
	end
end

---@param time table
---@param timePeriod number 时段
---@param min_angle number 分针旋转角度
---@param hour_angle number 时针旋转角度
function HUD_Weather:RefreshClock(time, timePeriod, min_angle, hour_angle, timePoint)
	if time then
		self.view.Dial_Ppoint:SetRenderTransformAngle(min_angle)
		self.view.Dial_Hour:SetRenderTransformAngle(hour_angle)
		self.view.Dial_DayTime_AreaLight_VX:SetRenderTransformAngle(hour_angle)

		--时钟轮盘状态更新
		self.ClockPrevState = self.ClockCurrState
		self.ClockCurrState = (Game.ClimateSystem.curTimePeriod == WeatherEnum.ETimePeriod.Morning or Game.ClimateSystem.curTimePeriod == WeatherEnum.ETimePeriod.Daytime)
			and WeatherEnum.EClockState.Day
			or WeatherEnum.EClockState.Night

		if self.ClockPrevState ~= self.ClockCurrState then
			if self.ClockCurrState == WeatherEnum.EClockState.Day then
				self.userWidget:SetWeather(true)
			elseif self.ClockCurrState == WeatherEnum.EClockState.Night then
				self.userWidget:SetWeather(false)
			end
		end
	end
end

function HUD_Weather:ShowHUDClimate(bShow)
	self.userWidget:SetVisibility(bShow and ESlateVisibility.Visible or ESlateVisibility.Hidden)
end

function HUD_Weather:OnClose()
	Game.UIInputProcessorManager:UnBindMouseButtonUpEvent(self)
	self:StopAllTimer()
end

function HUD_Weather:OnMouseButtonUp(mouseEvent)
	local geometry = self.view.Click_area:GetCachedGeometry()
	if not SlateBlueprintLibrary.IsUnderLocation(geometry, KismetInputLibrary.PointerEvent_GetScreenSpacePosition(mouseEvent)) then
		Game.ClimateSystem:HUDWeather2HUDMiniMap()
	end
end

function HUD_Weather:OnUIOpen()
	Game.ClimateSystem:HUDWeather2HUDMiniMap()
end

return HUD_Weather
