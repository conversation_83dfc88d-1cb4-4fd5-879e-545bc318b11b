local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class HUD_SceneMarkMenu : UIComponent
---@field view HUD_SceneMarkMenuBlueprint
local HUD_SceneMarkMenu = DefineClass("HUD_SceneMarkMenu", UIComponent)
local HUD_SceneMarkMenu_Item = kg_require("Gameplay.LogicSystem.HUD.HUD_Group.HUD_SceneMarkMenu_Item")

HUD_SceneMarkMenu.eventBindMap = {
    [EEventTypesV2.CLIENT_SCENE_MARK_CAHNGED] = "OnSceneMarkPartChanged",
    [EEventTypesV2.ON_TEAMID_CHANGED] = "onTeamIDChange",
    [EEventTypesV2.CLIENT_GROUP_CREATE] = "onGroupIDChange",
    [EEventTypesV2.CLIENT_GROUP_DISBAND] = "onGroupIDChange",
	[EEventTypesV2.HUD_SET_SIDE_BAR_TAB] = "onSideBarTabChanged",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUD_SceneMarkMenu:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUD_SceneMarkMenu:InitUIData()
end

--- UI组件初始化，此处为自动生成
function HUD_SceneMarkMenu:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUD_SceneMarkMenu:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Close.OnClicked, "on_Btn_Close_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUD_SceneMarkMenu:InitUIView()
    self.SceneMenuItemWidget = {
        [1] = self.view.WBP_HUDGroupPlacemarks_0,
        [2] = self.view.WBP_HUDGroupPlacemarks_1,
        [3] = self.view.WBP_HUDGroupPlacemarks_2,
        [4] = self.view.WBP_HUDGroupPlacemarks_3,
        [5] = self.view.WBP_HUDGroupPlacemarks_4,
        [6] = self.view.WBP_HUDGroupPlacemarks_5,
    }
    self.SceneMenuCompList = {}
    for key, value in pairs(self.SceneMenuItemWidget) do
        ---@type HUD_SceneMarkMenu_Item
        local Comp = self:CreateComponent(value, HUD_SceneMarkMenu_Item)
        table.insert(self.SceneMenuCompList, Comp)
    end
end


--- 此处为自动生成
function HUD_SceneMarkMenu:on_Btn_Close_Clicked()
    self:OnClick_Btn_Close()
end


function HUD_SceneMarkMenu:Refresh()
    self.bHandleClose = false
    self:RefreshUI()
end

function HUD_SceneMarkMenu:OnClose()
    if self.bHandleClose then
        self.bHandleClose = false
        Game.TeamSystem:SetMarkState(Enum.EMARK.None,  UICellConfig.HUD_SceneMarkMenu)
    end
end

function HUD_SceneMarkMenu:OnIdle(DeltaTime)

end

function HUD_SceneMarkMenu:RefreshUI()
    self.bHandleClose = false
    local MarkList = {}
    if Game.TeamSystem:IsInTeam() then
        MarkList = Game.TeamSystem:GetSceneMarkList()
    elseif Game.TeamSystem:IsInGroup() then
        MarkList = Game.GroupSystem:GetSceneMarkList()
    end

    for key, value in ipairs(self.SceneMenuCompList) do
        local Marked = false
        for kk, vv in ipairs(MarkList) do
            if tonumber(vv.Index) == key then
                Marked = true
                break
            end
        end
        value:Refresh(key, Marked)
    end
end

function HUD_SceneMarkMenu:OnSceneMarkPartChanged()
    self:RefreshUI()
end

function HUD_SceneMarkMenu:onTeamIDChange()
    self:CloseSelf()
end

function HUD_SceneMarkMenu:onGroupIDChange()
    self:CloseSelf()
end

function HUD_SceneMarkMenu:OnClick_Btn_Close()
    self.bHandleClose = true
    self:CloseSelf()
end

function HUD_SceneMarkMenu:onSideBarTabChanged(tabType)
    if tabType == Enum.SIDE_BAR.EQuest then
        self:OnClick_Btn_Close()
    end
end

return HUD_SceneMarkMenu
