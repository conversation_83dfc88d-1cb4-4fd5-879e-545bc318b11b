local HUDSideBtn = kg_require("Gameplay.LogicSystem.HUD.HUDSideBar.HUDSideBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class P_HUDSideBar : UIPanel
---@field view HUDSideBarPanelBlueprint
local P_HUDSideBar = DefineClass("P_HUDSideBar", UIPanel)

local Const = kg_require("Shared.Const")
local ESlateVisibility = import("ESlateVisibility")
local team_utils = kg_require("Shared.Utils.TeamUtils")

P_HUDSideBar.eventBindMap = {
	[EEventTypesV2.ON_TEAMID_CHANGED] = "onTeamIDChange",
	[EEventTypesV2.SERVER_MSG_TEAM_MEMBER_CHANGED] = "OnMsgTeamMemberChanged",
	[EEventTypesV2.CLIENT_GROUP_CREATE] = "onGroupCreate",
	[EEventTypesV2.CLIENT_GROUP_DISBAND] = "OnGroupDisband",
	[EEventTypesV2.ON_SELF_CAPTAIN_CHANGED] = "onSelfCaptainChange",
	[EEventTypesV2.GROUP_LEADER_CHANGED] = "onGroupLeaderChange",
	[EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED] = "gameModeHandler",
	[EEventTypesV2.PLAYER_TRIGGER_WORLDBOSS_AREA_CHANGEUI] = "RefreshWorldBossStateUI",
	[EEventTypesV2.TRACE_ON_CURRENT_TRACE_CHANGED] = "refreshQuestTabUI",
	[EEventTypesV2.ON_TEAMMEMBER_VOICESTATE_CHANGED] = "OnTeamVoiceStateChanged",
	[EEventTypesV2.CLIENT_GROUP_MEMBER_VOICE_UPDATE] = "GroupVoiceStateChanged",
	[EEventTypesV2.SERVER_RET_CONVENE] = "RetConvene",
	[EEventTypesV2.ON_TEAMMEMBER_BFOLLOWING_CHANGED] = "OnTeamFollowStateChanged",
	[EEventTypesV2.ON_FOLLOWSTATE_CHANGED] = "OnSelfFollowStateChanged",
	[EEventTypesV2.ON_UI_CLOSE] = "OnUIClose",
	[EEventTypesV2.MODULE_LOCK_CHANGE] = "OnLockStateChange",
	[EEventTypesV2.CLIENT_GROUP_MEMBER_PROP_UPDATE] = "OnGroupPropChanged",
	[EEventTypesV2.CLIENT_GROUP_MEMBER_EXCHANGE] = "OnGroupMemberExchanged",
	[EEventTypesV2.GROUP_ADD_MEMBER] = "OnMsgGroupMemberAdd",
	[EEventTypesV2.GROUP_DEL_MEMBER] = "OnMsgGroupMemberQuit",
	[EEventTypesV2.ON_MAINPLAYER_GROUP_ID_CHANGED] = "OnGroupIDChange",
}

---@type table<string, string> 组件名称和点击事件映射表
P_HUDSideBar.CellCompName2ClickMap = {
	WBP_QuestTab_luaCom = "OnClick_WBP_QuestTab",
	WBP_QuickTeam_luaCom = "OnClick_WBP_QuickTeam",
	WBP_ClimbTower_luaCom = "OnClick_WBP_ClimbTower",
	WBP_HUDBtnBossStats_luaCom = "OnClick_WBP_HUDBtnBossStats",
	WBP_HUDBtnTeam_luaCom = "OnClick_WBP_HUDBtnTeam",
	WBP_Voice_luaCom = "OnClick_WBP_Voice",
	WBP_HUDBtnFollow_luaCom = "OnClick_WBP_HUDBtnFollow",
	WBP_HUDBtnConvene_member_luaCom = "OnClick_WBP_HUDBtnConvene_member",
	WBP_MarkTab_luaCom = "OnClick_WBP_MarkTab",
	WBP_Quit_luaCom = "OnClick_WBP_Quit",
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function P_HUDSideBar:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function P_HUDSideBar:InitUIData()
	-- 当前选中的页签
	self.CurSelTab = nil
	-- 当前选中的页签右侧面板
	self.RightPanelUI = nil
	--右侧面板是否折叠 
	self.bDetailPanelOpen = true

	--- @type integer 语音状态
	self.nVoiceState = Enum.EVOICE_STATE.LISTEN

	self:InitTabInfo()
	
	self.HasClosed = false
end

--- UI组件初始化，此处为自动生成
function P_HUDSideBar:InitUIComponent()
    ---@type HUDSideBtn
    self.WBP_Quit_luaCom = self:CreateComponent(self.view.WBP_Quit_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_MarkTab_luaCom = self:CreateComponent(self.view.WBP_MarkTab_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_HUDBtnConvene_member_luaCom = self:CreateComponent(self.view.WBP_HUDBtnConvene_member_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_HUDBtnFollow_luaCom = self:CreateComponent(self.view.WBP_HUDBtnFollow_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_Voice_luaCom = self:CreateComponent(self.view.WBP_Voice_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_HUDBtnTeam_luaCom = self:CreateComponent(self.view.WBP_HUDBtnTeam_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_HUDBtnBossStats_luaCom = self:CreateComponent(self.view.WBP_HUDBtnBossStats_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_ClimbTower_luaCom = self:CreateComponent(self.view.WBP_ClimbTower_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_QuickTeam_luaCom = self:CreateComponent(self.view.WBP_QuickTeam_lua, HUDSideBtn)
    ---@type HUDSideBtn
    self.WBP_QuestTab_luaCom = self:CreateComponent(self.view.WBP_QuestTab_lua, HUDSideBtn)
    
end

---UI事件在这里注册，此处为自动生成
function P_HUDSideBar:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea_lua.OnClicked, "on_Btn_ClickArea_lua_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function P_HUDSideBar:InitUIView()
	self.WBP_QuestTab_luaCom:InitSideBtnParams("WBP_QuestTab_luaCom")
	self.WBP_QuickTeam_luaCom:InitSideBtnParams("WBP_QuickTeam_luaCom")
	self.WBP_ClimbTower_luaCom:InitSideBtnParams("WBP_ClimbTower_luaCom")
	self.WBP_HUDBtnBossStats_luaCom:InitSideBtnParams("WBP_HUDBtnBossStats_luaCom")
	self.WBP_HUDBtnTeam_luaCom:InitSideBtnParams("WBP_HUDBtnTeam_luaCom")
	self.WBP_Voice_luaCom:InitSideBtnParams("WBP_Voice_luaCom")
	self.WBP_HUDBtnFollow_luaCom:InitSideBtnParams("WBP_HUDBtnFollow_luaCom")
	self.WBP_HUDBtnConvene_member_luaCom:InitSideBtnParams("WBP_HUDBtnConvene_member_luaCom")
	self.WBP_MarkTab_luaCom:InitSideBtnParams("WBP_MarkTab_luaCom")
	self.WBP_Quit_luaCom:InitSideBtnParams("WBP_Quit_luaCom")
	

	self.RightPanelUI = nil
end

function P_HUDSideBar:InitTabInfo()
	--详情面板类型
	self.TabInfoMap = {
		[Enum.SIDE_BAR.EWorldBoss] =
		{
			BtnUI = self.view.WBP_HUDBtnBossStats_lua,
			bTriggle = false, --是否触发了世界Boss
			RightPanelUI = "P_HUDWorldBossRankScore",
		},
		[Enum.SIDE_BAR.EClimbTower] =
		{
			BtnUI = self.view.WBP_ClimbTower_lua,
			bTriggle = false,--是否爬塔
			RightPanelUI = "P_HUDFellow",
		},
		[Enum.SIDE_BAR.EQuest] =
		{
			BtnUI = self.view.WBP_QuestTab_lua,
			bTriggle = false, --是否任务栏
			RightPanelUI = "HUD_Task_Panel",
		},
		[Enum.SIDE_BAR.EQuickTeamUp] =
		{
			BtnUI = self.view.WBP_QuickTeam_lua,
			bTriggle = false, --是否处于组队或团队
			RightPanelUI = "",
		},
		[Enum.SIDE_BAR.ETeamOrGroup] =
		{
			BtnUI = self.view.WBP_HUDBtnTeam_lua,
			bTriggle = false, --处于组队或团队
			RightPanelUI = "HUD_TeamGroup_Container",
		},
		[Enum.SIDE_BAR.EVoice] =
		{
			BtnUI = self.view.WBP_Voice_lua,
			bTriggle = false, --组队语音
			State = nil,--语音状态
			RightPanelUI = "",
		},
		[Enum.SIDE_BAR.EFollow] =
		{
			BtnUI = self.view.WBP_HUDBtnFollow_lua,--跟随
			bTriggle = false,
			RightPanelUI = "",
		},
		[Enum.SIDE_BAR.EConvene] =
		{
			BtnUI = self.view.WBP_HUDBtnConvene_member_lua,--召集
			bTriggle = false,
			RightPanelUI = "",
		},
		[Enum.SIDE_BAR.EMark] =
		{
			BtnUI = self.view.WBP_MarkTab_lua,--标记
			bTriggle = false,
			RightPanelUI = "",
		},
		[Enum.SIDE_BAR.EQuitTeamGroup] =
		{
			BtnUI = self.view.WBP_Quit_lua,--退出
			bTriggle = false,
			RightPanelUI = "",
		},
	} --页签对应的详情面板名称
end

---面板打开的时候触发  切场景也会触发！！
function P_HUDSideBar:OnRefresh()
	self.CurSelTab = nil
	self.bDetailPanelOpen = true
	self.view.Img_SideBg_lua:SetVisibility(ESlateVisibility.Collapsed)
	self.bInTeam = Game.me.teamID ~= 0
	self.bInGroup = Game.TeamSystem:IsInGroup()

	if not self.bInTeam and UI.IsShow(UICellConfig.HUD_Team) then
		Game.HUDSystem:HideUI(UICellConfig.HUD_Team)
	end
	if not self.bInGroup and UI.IsShow(UICellConfig.HUD_Group)then
		Game.HUDSystem:HideUI(UICellConfig.HUD_Group)
	end
	
	self.bCaptain = Game.me.isCaptain == 1
	self.bGroupLeader = Game.GroupSystem:IsGroupLeader()
	self.bFollow = Game.me.FollowState == 1
	self.nextConveneTS = -1
	self:initUIStyle()
	self:RefreshWorldBossStateUI()
	self:RefreshClimbTowerStateUI()
	self:RefreshTeamGroupStateUI()

	self:RefreshFollowUI()
	self:RefreshFollowStateUI()

	self:RefreshConveneUI()
	self:RefreshConveneStateUI()

	self:RefreshVoiceUI()
	self:RefreshMarkUI()
	self:RefreshQuitTeamGroupUI()

	self:refreshQuestTabUI()

	if not self.CurSelTab then
		--当前没有选中页签，选中默认页签
		self:SelDefaultTab()
	end
	self:gameModeHandler()
	local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_FAST_MAKEUP)
	self:SetImage(self.view.WBP_QuickTeam_lua.Icon_lua, iconData.AssetPath)
	
	iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_TEAM)
	self:SetImage(self.view.WBP_HUDBtnTeam_lua.Icon_lua, iconData.AssetPath)

	if self.HasClosed then
		self.HasClosed = false
		self:ReopenRightPanelUI()
	end
end

function P_HUDSideBar:ReopenRightPanelUI()
	if self.RightPanelUI then
		local child = self:GetComponentByCellId(self.RightPanelUI)
		if child then
			child:OnRefresh()
		end
	end
end

---initUIStyle 初始化所有页签显示状态
function P_HUDSideBar:initUIStyle()
	for key, value in pairs(self.TabInfoMap) do
		if value.BtnUI then
			if key == Enum.SIDE_BAR.ETeamOrGroup then
				value.BtnUI:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			else
				value.BtnUI:SetVisibility(ESlateVisibility.Collapsed)
			end
			value.BtnUI:Event_UI_Style(false, false, false)
		end
	end
end


---检查是否是pvp(3\9\10)或者副本
---@return boolean
function P_HUDSideBar:checkPVPOrDungeonSpace()
	---@type Space
	local curSpace = Game.NetworkManager.GetLocalSpace()
	if curSpace then
		---如果是位面中，那么直接返回false
		if curSpace:IsPlane() then return false end
		if curSpace:IsTeamPVPSpace() or curSpace:IsTeamArenaSpace() or curSpace:IsGuildLeagueSpace() or curSpace:IsDungeonSpace() then
			return true
		end
	end
	return false
end


---策划需求更改：当进入场景的时候，如果是pvp或副本，默认选择队伍页签
---SelDefaultTab 当没有页签选中时默认处理（目前选择队伍页签）
function P_HUDSideBar:SelDefaultTab()
	-- 收起的时候 不操作
	if not self.bDetailPanelOpen then
		return
	end
	--- 这里优先级要不要改到配置里面
	if self:checkPVPOrDungeonSpace() then
		self:onTabClick(Enum.SIDE_BAR.ETeamOrGroup)
	elseif Game.WorldBossSystem:IsInWorldBoss() then
		self:onTabClick(Enum.SIDE_BAR.EWorldBoss)
	elseif self.bInTeam or self.bInGroup then
		self:onTabClick(Enum.SIDE_BAR.ETeamOrGroup)
	elseif self:bIsQuestTabVisible() then
		self:onTabClick(Enum.SIDE_BAR.EQuest)
	else
		self:onTabClick(Enum.SIDE_BAR.EQuest)
		-- self:onTabClick(Enum.SIDE_BAR.EQuickTeamUp)   
	end
end

---gameModeHandler 判断玩家是否处于组队PVP中（组队PVP中需要强制显示队伍信息，并屏蔽导航栏）
function P_HUDSideBar:gameModeHandler()
	if Game.TeamAreanaSystem:IsInTeamArena() then
		--如果在组队PVP中，强制选中组队页签,隐藏侧边栏
		if self.CurSelTab ~= Enum.SIDE_BAR.ETeamOrGroup then
			self:onTabClick(Enum.SIDE_BAR.ETeamOrGroup)
		end
	end
end

function P_HUDSideBar:OnHide()
end
----------------------------------世界boss start----------------------------
function P_HUDSideBar:OnClick_WBP_HUDBtnBossStats()
	UI.ShowUI("P_BossStatsScore")
end

function P_HUDSideBar:RefreshWorldBossStateUI(triggerWorldBoss)
	if triggerWorldBoss == nil then
		triggerWorldBoss = Game.WorldBossSystem:IsInWorldBoss()
	end
	if triggerWorldBoss == self.TabInfoMap[Enum.SIDE_BAR.EWorldBoss].bTriggle then
		return
	end
	self.TabInfoMap[Enum.SIDE_BAR.EWorldBoss].bTriggle = triggerWorldBoss
	local slateVisibility = triggerWorldBoss and ESlateVisibility.Visible or
		ESlateVisibility.Collapsed
	if triggerWorldBoss then
		self.view.WBP_HUDBtnBossStats_lua:SetVisibility(slateVisibility)
		-- self:PlayAnimation(self.view.WBP_HUDBtnBossStats_lua.WidgetRoot, self.view.WBP_HUDBtnBossStats_lua.WidgetRoot.Ani_Fadein, 0.0, 1,
		--     EUMGSequencePlayMode.Forward, 1, false)
		self:showBossRankScore(true) --当触发世界boss时，强制弹出世界boss积分面板
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.SIDEBAR_RANKING_ICON)
		self:SetImage(self.view.WBP_HUDBtnBossStats_lua.Icon_lua, iconData.AssetPath)
	else
		-- self:PlayAnimation(self.view.WBP_HUDBtnBossStats_lua.WidgetRoot, self.view.WBP_HUDBtnBossStats_lua.WidgetRoot.Ani_Fadeout, 0.0, 1,
		--     EUMGSequencePlayMode.Forward, 1, false,
		--     function() self.view.WBP_HUDBtnBossStats_lua:SetVisibility(slateVisibility) end)
		if self.CurSelTab == Enum.SIDE_BAR.EWorldBoss then
			self:SelDefaultTab()
		end
	end
end

function P_HUDSideBar:showBossRankScore(forceShow)
	self:onTabClick(Enum.SIDE_BAR.EWorldBoss, forceShow)
end
----------------------------------世界boss end----------------------------

----------------------------------爬塔 start----------------------------
function P_HUDSideBar:RefreshClimbTowerStateUI()
	-- 爬塔功能取消 先注释掉
	--[[
    local curSpace = Game.NetworkManager.GetLocalSpace()
    if not curSpace then
        self.TabInfoMap[Enum.SIDE_BAR.EClimbTower].bTriggle = false
        return
    end
    self.TabInfoMap[Enum.SIDE_BAR.EClimbTower].bTriggle = true
    self.view.WBP_ClimbTower_lua:SetVisibility(ESlateVisibility.Visible)
    self:onTabClick(Enum.SIDE_BAR.EClimbTower)
    ]]--
end

function P_HUDSideBar:OnClick_WBP_ClimbTower()
	Game.UIJumpSystem:JumpToUI(1250038)
end
----------------------------------爬塔 end----------------------------

----------------------------------任务 start----------------------------
function P_HUDSideBar:bIsQuestTabVisible()
	local TraceSystem = Game.TraceSystem
	local ringID = TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.TASK)
	local bIsVisible = false
	if ringID and ringID > 0 then
		bIsVisible = true
	else
		ringID = TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.SECOND_TASK)
		if ringID and ringID > 0 then
			bIsVisible = true
		end
	end
	return bIsVisible
end

function P_HUDSideBar:refreshQuestTabUI()
	self.view.WBP_QuestTab_lua:SetVisibility(ESlateVisibility.Visible)
	self.TabInfoMap[Enum.SIDE_BAR.EQuest].bTriggle = true
	-- if self.CurSelTab ~= Enum.SIDE_BAR.EQuest then
	--     self:onTabClick(Enum.SIDE_BAR.EQuest)
	-- end
	local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.QUEST_OPEN)
	self:SetImage(self.view.WBP_QuestTab_lua.Icon_lua, iconData.AssetPath)
end

function P_HUDSideBar:OnClick_WBP_QuestTab()
	if self.CurSelTab == Enum.SIDE_BAR.EQuest then
		Game.NewUIManager:OpenPanel("TaskBoardPanel")
	else
		self:onTabClick(Enum.SIDE_BAR.EQuest)
	end
end
----------------------------------任务 end----------------------------

----------------------------------组队 start----------------------------
function P_HUDSideBar:onTeamIDChange(eid, name, newValue)
	if self.bInTeam ~= (newValue ~= 0) then
		self.bInTeam = (newValue ~= 0)
		if self.bInTeam then
			-- self:PlayAnimation(self.view.WidgetRoot, self.view.WidgetRoot.Ani_Open_TeamList, 0, 1,EUMGSequencePlayMode.Forward, 1, false, function ()
			--     self.view.KGVBOX_ChildNodes_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
			-- end)
		else
			-- self:PlayAnimation(self.view.WidgetRoot, self.view.WidgetRoot.Ani_Close_TeamList, 0, 1,EUMGSequencePlayMode.Forward, 1, false, function ()
			--     self.view.KGVBOX_ChildNodes_lua:SetVisibility(ESlateVisibility.Hidden)
			-- end)
		end
		self:RefreshTeamGroupStateUI()
		self:RefreshVoiceUI()
		self:RefreshFollowUI()
		self:RefreshConveneUI()
		self:RefreshMarkUI()
		self:RefreshQuitTeamGroupUI()
		self:SelDefaultTab()
	end
	self.nextConveneTS = -1
	
	---@type HUD_TeamGroup_Container
	local cell = self:GetComponentByCellId(UICellConfig.HUD_TeamGroup_Container)
	if cell then
		cell:TeamIDChange()
	end
end

function P_HUDSideBar:OnMsgTeamMemberChanged(Reason1, Reason2, MemberInfo)
	self:RefreshVoiceUI()
	local bIsSelf = (MemberInfo.id == Game.me.eid)
	if Reason1 == team_utils.TeamMemberChangeReason.TMCR_ENTER or Reason1 == team_utils.TeamMemberChangeReason.TMCR_LEAVE then
		if not bIsSelf then
			if self.bCaptain then
				self:RefreshConveneUI()
			end
		end
	end
end

function P_HUDSideBar:onGroupCreate(groupID)
	self.bInGroup = true
	self.bGroupLeader = Game.GroupSystem:IsGroupLeader()
	self.nextConveneTS = -1
	-- self:PlayAnimation(self.view.WidgetRoot, self.view.WidgetRoot.Ani_Open_TeamList, 0, 1,EUMGSequencePlayMode.Forward, 1, false, function ()
	--     self.view.KGVBOX_ChildNodes_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
	-- end)
	self:RefreshTeamGroupStateUI()
	self:RefreshVoiceUI()
	self:RefreshFollowUI()
	self:RefreshConveneUI()
	self:RefreshMarkUI()
	self:RefreshQuitTeamGroupUI()
	self:SelDefaultTab()
end

function P_HUDSideBar:OnGroupDisband()
	self.bInGroup = false
	self.bGroupLeader = false
	self.nextConveneTS = -1
	-- self:PlayAnimation(self.view.WidgetRoot, self.view.WidgetRoot.Ani_Close_TeamList, 0, 1,EUMGSequencePlayMode.Forward, 1, false, function ()
	--     self.view.KGVBOX_ChildNodes_lua:SetVisibility(ESlateVisibility.Hidden)
	-- end)
	self:RefreshTeamGroupStateUI()
	self:RefreshVoiceUI()
	self:RefreshFollowUI()
	self:RefreshConveneUI()
	self:RefreshMarkUI()
	self:RefreshQuitTeamGroupUI()
	self:SelDefaultTab()
end

function P_HUDSideBar:onSelfCaptainChange(eid, name, newValue, oldValue)
	local newState = Game.me.isCaptain == 1
	if self.bCaptain ~= newState then
		self.bCaptain = newState
		self:RefreshFollowUI()
		self:RefreshConveneUI()
		self:RefreshMarkUI()
	end
end

function P_HUDSideBar:OnGroupMemberExchanged(SrcSlot, DstSlot, ChangeTeamIndexList)
	if SrcSlot.groupIndex == Game.GroupSystem:GetMyGroupIndex() or DstSlot.groupIndex == Game.GroupSystem:GetMyGroupIndex() then
		self:RefreshFollowUI()
		self:RefreshConveneUI()
		self:RefreshConveneStateUI()
		self:RefreshMarkUI()
	end
end

function P_HUDSideBar:OnMsgGroupMemberAdd(groupID, memberInfo, fromBuildApply, fromRescue)
	if groupID == Game.me.groupID then
		self:RefreshFollowUI()
		self:RefreshConveneUI()
		self:RefreshConveneStateUI()
	end
end

function P_HUDSideBar:OnMsgGroupMemberQuit(groupID, MemberInfo, refreshTeamList)
	if groupID == Game.me.groupID then
		self:RefreshFollowUI()
		self:RefreshConveneUI()
		self:RefreshConveneStateUI()
	end
end

function P_HUDSideBar:onGroupLeaderChange(OldLeaderInfo, NewLeaderInfo)
	self.bGroupLeader = NewLeaderInfo == Game.me.eid
	if OldLeaderInfo == Game.me.eid or NewLeaderInfo == Game.me.eid then
		self:RefreshFollowUI()
		self:RefreshConveneUI()
		self:RefreshConveneStateUI()
		self:RefreshMarkUI()
	end
end

function P_HUDSideBar:OnTeamFollowStateChanged(name, eid, newValue, oldValue)
	if self.bInTeam and self.bCaptain then
		self:RefreshConveneStateUI()
	end
end

function P_HUDSideBar:OnSelfFollowStateChanged()
	self:RefreshFollowStateUI()
end

function P_HUDSideBar:OnLockStateChange()
	if self.bInTeam == false and self.bInGroup == false then
		if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_TEAM", false) == false then
			self.view.WBP_QuickTeam_lua:SetVisibility(ESlateVisibility.Collapsed)
		else
			self.view.WBP_QuickTeam_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		end
		self.view.WBP_HUDBtnTeam_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_SideBg_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function P_HUDSideBar:OnGroupPropChanged(EID, Key, Value)
	if self.bInGroup and self.bGroupLeader and Game.GroupSystem:IsMyGroupMember(EID) then
		if Key == "bFollow" then
			self:RefreshConveneStateUI()
		end
	end
end

function P_HUDSideBar:OnUIClose(UIName)
	if UIName == "P_HUDMarkTab" then
		self:SetMarkHighLight(false, false)
	elseif UIName == "P_HUDTeamVoiceTips" then
		self:SetVoiceHighLight(false, false)
	end
end

function P_HUDSideBar:RetConvene(Result, nextConveneTS)
	--返回召集结果
	local Widget = self.view.WBP_HUDBtnConvene_member_lua
	self.nextConveneTS = nextConveneTS
	Widget.WBP_HUDBtnCD_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self:StartTimer(
		"Convenetimer",
		function()
			local remainTs = self.nextConveneTS - _G._now() // 1000
			if remainTs <= 0 then
				Widget.WBP_HUDBtnCD_lua:SetVisibility(ESlateVisibility.Collapsed)
				self:StopTimer("Convenetimer")
				return
			else
				Widget.WBP_HUDBtnCD_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
				Widget.WBP_HUDBtnCD_lua:SetCD(remainTs / Game.TableData.GetConstDataRow("TEAM_LEADER_FOLLOW_COUNTDOWN"))
				Widget.WBP_HUDBtnCD_lua.Text_CD_lua:SetText(remainTs)
			end
		end,
		1000,
		-1,
		nil,
		true,
		function()
			Widget.WBP_HUDBtnCD_lua:SetVisibility(ESlateVisibility.Collapsed)
		end
	)
end

function P_HUDSideBar:OnTeamVoiceStateChanged(_, eid, newValue, oldValue)
	if eid == Game.me.eid then
		self:refreshVoiceStateUI(newValue)
	end
end

function P_HUDSideBar:OnGroupIDChange()
	self:RefreshVoiceUI()

	---@type HUD_TeamGroup_Container
	local cell = self:GetComponentByCellId(UICellConfig.HUD_TeamGroup_Container)
	if cell then
		cell:GroupIDChange()
	end
end

function P_HUDSideBar:GroupVoiceStateChanged(eid, newValue)
	if eid == Game.me.eid then
		self:refreshVoiceStateUI(newValue)
	end
end

function P_HUDSideBar:RefreshTeamGroupStateUI()
	if self.bInTeam or self.bInGroup then
		self.view.WBP_QuickTeam_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.WBP_HUDBtnTeam_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.view.Img_SideBg_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self:PlayAnimation(self.view.WBP_HUDBtnTeam_lua.Ani_Fadein, nil, self.view.WBP_HUDBtnTeam_lua)
	else
		if Game.ModuleLockSystem:CheckModuleUnlockByEnum("MODULE_LOCK_TEAM", false) == false then
			self.view.WBP_QuickTeam_lua:SetVisibility(ESlateVisibility.Collapsed)
		else
			self.view.WBP_QuickTeam_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		end
		self.view.WBP_HUDBtnTeam_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_SideBg_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
	self.TabInfoMap[Enum.SIDE_BAR.ETeamOrGroup].bTriggle = self.bInTeam or self.bInGroup
end

function P_HUDSideBar:SetImageSafe(image, path)
	self:SetImage(image, path, nil, true, true)
end

function P_HUDSideBar:RefreshConveneStateUI()
	if self.bInTeam then
		if self.bCaptain then
			if Game.TeamSystem:IsInConvene() then
				--待命
				self:StopTimer("Convenetimer")
				local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_WAIT)
				self:SetImageSafe(self.view.WBP_HUDBtnConvene_member_lua.Icon_lua, iconData.AssetPath)
			else
				--召集
				local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_UP)
				self:SetImageSafe(self.view.WBP_HUDBtnConvene_member_lua.Icon_lua, iconData.AssetPath)
				self:RetConvene(nil, self.nextConveneTS)
			end
		end
	elseif self.bInGroup then
		if self.bGroupLeader then
			if Game.GroupSystem:IsMyGroupMemberAllFollowed() and Game.GroupSystem:GetMyGroupMemberNum() > 1 then
				--待命
				self:StopTimer("Convenetimer")
				local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.GROUP_WAIT)
				self:SetImageSafe(self.view.WBP_HUDBtnConvene_member_lua.Icon_lua, iconData.AssetPath)
			else
				--召集
				local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.GROUP_UP)
				self:SetImageSafe(self.view.WBP_HUDBtnConvene_member_lua.Icon_lua, iconData.AssetPath)
				self:RetConvene(nil, self.nextConveneTS)
			end
		end
	end
end

function P_HUDSideBar:RefreshConveneUI()
	if self.bInTeam and self.bCaptain then
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_UP)
		self:SetImageSafe(self.view.WBP_HUDBtnConvene_member_lua.Icon_lua, iconData.AssetPath)
		self.view.WBP_HUDBtnConvene_member_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.Img_line_convene_member_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.view.WBP_HUDBtnConvene_member_lua:Event_UI_Style(false, false, false)
	elseif self.bInGroup and self.bGroupLeader == true then
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.GROUP_UP)
		self:SetImageSafe(self.view.WBP_HUDBtnConvene_member_lua.Icon_lua, iconData.AssetPath)
		self.view.WBP_HUDBtnConvene_member_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.Img_line_convene_member_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.view.WBP_HUDBtnConvene_member_lua:Event_UI_Style(false, false, false)
	else
		self.view.WBP_HUDBtnConvene_member_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_line_convene_member_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function P_HUDSideBar:RefreshFollowUI()
	--跟随UI
	if (self.bInTeam and self.bCaptain == false) or (self.bInGroup and self.bGroupLeader == false) then
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_FOLLOW)
		self:SetImageSafe(self.view.WBP_HUDBtnFollow_lua.Icon_lua, iconData.AssetPath)
		self.view.WBP_HUDBtnFollow_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.Img_line_follow_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.view.WBP_HUDBtnFollow_lua:Event_UI_Style(false, false, false)
	else
		self.view.WBP_HUDBtnFollow_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_line_follow_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function P_HUDSideBar:RefreshFollowStateUI()
	local FollowState = GetMainPlayerPropertySafely("FollowState")
	local WidgetRoot = self.view.WBP_HUDBtnFollow_lua
	if FollowState == Enum.EFollowState.ON_FOLLOWING or FollowState == Enum.EFollowState.PAUSE_FOLLOW then
		WidgetRoot.Overlay_Anim_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self:PlayAnimation(WidgetRoot.Ani_Follow, nil, WidgetRoot, 0, 0)
	elseif FollowState == Enum.EFollowState.STOP_FOLLOW then
		self:StopAnimation(WidgetRoot.Ani_Follow, WidgetRoot)
		WidgetRoot.Overlay_Anim_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end


function P_HUDSideBar:RefreshVoiceUI()
	if self.bInTeam or self.bInGroup then
		self.view.WBP_Voice_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.view.Img_line_Voice_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self:refreshVoiceStateUI(Game.TeamSystem:GetVoiceState())
	else
		self.view.WBP_Voice_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_line_Voice_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function P_HUDSideBar:refreshVoiceStateUI(newValue)
	self.nVoiceState = newValue
	if newValue == Enum.EVOICE_STATE.LISTEN then
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_LISTEN)
		self:SetImage(self.view.WBP_Voice_lua.Icon_lua, iconData.AssetPath)
	elseif newValue == Enum.EVOICE_STATE.VOICE then
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_OPEN_MIC)
		self:SetImage(self.view.WBP_Voice_lua.Icon_lua, iconData.AssetPath)
	elseif newValue == Enum.EVOICE_STATE.REFUSE then
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_NOT_LISTEN)
		self:SetImage(self.view.WBP_Voice_lua.Icon_lua, iconData.AssetPath)
	end
end

function P_HUDSideBar:RefreshMarkUI()
	--标记UI
	if (self.bInTeam and self.bCaptain) or (self.bInGroup and self.bGroupLeader) then
		self.view.WBP_MarkTab_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.Img_line_mark_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_MARK)
		self:SetImageSafe(self.view.WBP_MarkTab_lua.Icon_lua, iconData.AssetPath)
		self:SetMarkHighLight(false, false)
	else
		self.view.WBP_MarkTab_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_line_mark_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function P_HUDSideBar:RefreshQuitTeamGroupUI()
	--标记UI
	if self.bInTeam or self.bInGroup then
		self.view.WBP_Quit_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.Img_line_Quit_lua:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		local iconData = Game.TableData.GetArtAssetIconDataRow(Enum.EArtAssetIconData.TEAM_EXIT)
		self:SetImage(self.view.WBP_Quit_lua.Icon_lua, iconData.AssetPath)
	else
		self.view.WBP_Quit_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_line_Quit_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function P_HUDSideBar:OnClick_WBP_QuickTeam()
	local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
	local widget = self.view.WBP_QuickTeam_lua.Icon_lua
	local localSize = SlateBlueprintLibrary.GetLocalSize(widget:GetCachedGeometry())
	local _, viewportPosition = SlateBlueprintLibrary.LocalToViewport(_G.GetContextObject(),
		widget:GetCachedGeometry(), localSize, nil, nil)
	Game.NewUIManager:OpenPanel(UIPanelConfig.HUD_TeamQuick_Panel, viewportPosition.X + 10, viewportPosition.Y + 50)
	
	-- 关闭quest
	if self.CurSelTab == Enum.SIDE_BAR.EQuest then
		self:onTabClick(Enum.SIDE_BAR.EQuickTeamUp)
	end
end

---onClickWorldBossRank 点击队伍页签
function P_HUDSideBar:OnClick_WBP_HUDBtnTeam()
	if self.CurSelTab ~= Enum.SIDE_BAR.ETeamOrGroup then
		--任务面板会和team冲突，所以优先check侧边栏
		self:onTabClick(Enum.SIDE_BAR.ETeamOrGroup)
	else
		--当选中组队页签且在队伍中时，再次点击会打开组队或团队界面
		if Game.TeamAreanaSystem:IsInTeamArena() then
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PVP_TEAM_ENTRANCE_NOT_ALLOW_OPEN)
			return
		end
		if not Game.DungeonSystem:IsInSingleDungeonWithBot() then
			if self.bInGroup then
				--UI.ShowUI("P_Group")
				Game.NewUIManager:OpenPanel(UIPanelConfig.Group_Panel)
			else
				Game.NewUIManager:OpenPanel(UIPanelConfig.TeamDisplay_Panel)
			end
		else
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.OPERATIONAL_LIMITATION_IN_SINGLE_DUNGEON)
		end
	end
end

function P_HUDSideBar:OnClick_WBP_MarkTab()
	self:onSecTabClick(Enum.SIDE_BAR.EMark)
	if self.CurSelTab ~= Enum.SIDE_BAR.EMark then
		local _, Pos = import("SlateBlueprintLibrary").LocalToViewport(
			_G.GetContextObject(),
			self.view.WBP_MarkTab_lua.Icon_lua:GetCachedGeometry(),
			FVector2D(0, 0),
			nil,
			nil)
		local Size = import("SlateBlueprintLibrary").GetLocalSize(self.view.WBP_MarkTab_lua.Icon_lua:GetCachedGeometry())
		Pos.X = Size.X + Pos.X
		Pos.Y = Pos.Y + Size.Y / 2
		Game.HUDSystem:ShowUI("P_HUDMarkTab", Pos)
	end
end

--取消高亮

function P_HUDSideBar:SetVoiceHighLight(bSel,bHighLight)
	self.view.WBP_Voice_lua:Event_UI_Style(false, bHighLight, false)
end

function P_HUDSideBar:SetMarkHighLight(bSel,bHighLight)
	self.view.WBP_MarkTab_lua:Event_UI_Style(bSel, bHighLight, false)
end

-- 二级页签(爬塔，工会，世界Boss，组队)
function P_HUDSideBar:onTabClick(tabType, forceShow)
	if self.curTabBtn then
		self.curTabBtn:Event_UI_Style(false, false, false)
	end
	self.CurSelTab = tabType
	self.curTabBtn = self.TabInfoMap[tabType].BtnUI
	if self.TabInfoMap[self.CurSelTab].bTriggle and self.TabInfoMap[self.CurSelTab].RightPanelUI ~= "" then
		self.curTabBtn:Event_UI_Style(true, false, false)
	end
	if self.CurSelTab == Enum.SIDE_BAR.ETeamOrGroup then
		self.view.Overlay_Control_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Img_SideBg_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.KGVBOX_ChildNodes_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.Overlay_Control_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Img_SideBg_lua:SetVisibility(ESlateVisibility.Collapsed)
		self.view.KGVBOX_ChildNodes_lua:SetVisibility(ESlateVisibility.Collapsed)
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.HUD_SET_SIDE_BAR_TAB, tabType)
	if self:IsSkipRefreshRightPanelUI(tabType) then
		return
	end
	self:refreshRightPanelUI(tabType, forceShow)
end

function P_HUDSideBar:IsSkipRefreshRightPanelUI(newPanelType)
	-- 世界boss活动内 点击组队按钮不显示右侧详细面板 要留给世界Boss排行榜展示
	if newPanelType == Enum.SIDE_BAR.ETeamOrGroup and self.RightPanelUI == self.TabInfoMap[Enum.SIDE_BAR.EWorldBoss].RightPanelUI then
		return true
	end
	return false
end

--一级页签
function P_HUDSideBar:onSecTabClick(tabType, forceShow)
	if self.curSecTabBtn then
		self.curSecTabBtn:Event_UI_Style(false, false, false)
	end
	self.curSelSecTab = tabType
	self.curSecTabBtn = self.TabInfoMap[tabType].BtnUI
	self.curSecTabBtn:Event_UI_Style(false, true, false)
end

function P_HUDSideBar:OnClose()
	self.HasClosed = true
end

---@param newPanelType number:EDetailPanelType 要显示详情面板的页签类型
---@param forceShow boolean 是否强制打开（忽略缩进状态）
function P_HUDSideBar:refreshRightPanelUI(newPanelType, forceShow, ...)
	if forceShow and self.bDetailPanelOpen == false then
		self:OnClick_Btn()
	end
	if self.RightPanelUI == self.TabInfoMap[newPanelType].RightPanelUI then
		return
	end
	if self.RightPanelUI then
		UI.HideUI(self.RightPanelUI)
		--local comp = self:GetChildComponent(self.RightPanelUI)
		--if comp then
		--	comp:Hide()
		--end
	end
	self.RightPanelUI = self.TabInfoMap[newPanelType].RightPanelUI
	if self.RightPanelUI and self.RightPanelUI ~= "" then
		local config = UI.GetCfg(self.RightPanelUI)
		if self.RightPanelUI then
			if config.parentui == self.__cname then
				local rootPath = string.sub(config.parent, string.findLast(config.parent, '/') + 1)
				local root = self.view[rootPath]
				self:OpenComponent(self.RightPanelUI, root)
			elseif self.RightPanelUI and (config.parentui == nil or UIManager:GetInstance():getUI(config.parentui)) then
				UI.ShowUI(self.RightPanelUI, ...)
			end
		end
	end
end

--- 点击缩进展开按钮
function P_HUDSideBar:OnClick_Btn()
	--收起
	if self.bDetailPanelOpen then
		self:OpenDetailPanel(true)
	else
		self:OpenDetailPanel(false)
	end
end

function P_HUDSideBar:OpenDetailPanel(bOPen)
	--收起
	if bOPen then
		self.bDetailPanelOpen = false
		self.view.overlay_content:SetVisibility(ESlateVisibility.Collapsed)
		self.view.VB_FuncList_lua:SetVisibility(ESlateVisibility.Hidden)
		self.view.CP_Team_lua:SetVisibility(ESlateVisibility.Hidden)
		self.view.Overlay_Control_lua:SetRenderTransformAngle(180)
	else
		self.bDetailPanelOpen = true
		self.view.overlay_content:SetVisibility(ESlateVisibility.Visible)
		self.view.VB_FuncList_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.CP_Team_lua:SetVisibility(ESlateVisibility.Visible)
		self.view.Overlay_Control_lua:SetRenderTransformAngle(0)
	end
end

function P_HUDSideBar:OnClick_WBP_Voice()
	self:onSecTabClick(Enum.SIDE_BAR.EVoice)
	local _, ViewportPosition =
	import("SlateBlueprintLibrary").LocalToViewport(
		_G.GetContextObject(),
		self.view.WBP_Voice_lua.Button_lua:GetCachedGeometry(),
		FVector2D(1, 0),
		nil,
		nil
	)
	local Size = import("SlateBlueprintLibrary").GetLocalSize(self.view.WBP_Voice_lua.Button_lua:GetCachedGeometry())
	ViewportPosition = FVector2D(ViewportPosition.X + Size.X, ViewportPosition.Y + Size.Y / 2)
	Game.NewUIManager:OpenPanel("P_HUDTeamVoiceTips",ViewportPosition, Enum.EVOICE_CHANNEL.WORLD, self.nVoiceState, false)
end

function P_HUDSideBar:OnClick_WBP_HUDBtnFollow()
	if self.bInTeam then
		local FollowerPlaneID = Game.TeamSystem:GetCaptainInfo().planeID
		if FollowerPlaneID and FollowerPlaneID > 0 then
			-- 位面禁止跟随
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_PLANE_CAN_NOT_FOLLOW)
		else
			Game.TeamSystem:FollowCaptain()
		end
	elseif self.bInGroup then
		if Game.GroupSystem:IsTeamLeader(Game.me.eid) then
			local FollowerPlaneID = Game.GroupSystem:GetMyGroupLeaderInfo().planeID
			if FollowerPlaneID and FollowerPlaneID > 0 then
				-- 位面禁止跟随
				Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_PLANE_CAN_NOT_FOLLOW)
			else
				Game.TeamSystem:FollowCaptain()
			end
		else
			local FollowerPlaneID = Game.GroupSystem:GetMyTeamLeaderInfo().planeID
			if FollowerPlaneID and FollowerPlaneID > 0 then
				-- 位面禁止跟随
				Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_PLANE_CAN_NOT_FOLLOW)
			else
				Game.TeamSystem:FollowCaptain()
			end
		end
	end
end

function P_HUDSideBar:OnClick_WBP_HUDBtnConvene_member()
	if self.bInTeam then
		if Game.TeamSystem:GetTeamPlayerNum() > 1 then
			--召集组队队员
			if Game.TeamSystem:IsTeamMemberAllFollowed() then
				Game.me:ReqCaptainStandby()
				Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_STOP_ALL_MEMBER_FOLLOW)
			else
				if (self.nextConveneTS - _G._now()//1000) > 0 then
					return
				end
				Game.me:CaptainConvene()
			end
		else
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_NO_MEMBER)
		end
	elseif self.bInGroup then
		if self.bGroupLeader then
			if Game.GroupSystem:GetMyGroupMemberNum() > 1 then
				if Game.GroupSystem:IsMyGroupMemberAllFollowed() then
					Game.me:ReqGroupStandby(false)
					Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GROUP_STOP_ALL_LEADER_FOLLOW)
				else
					if (self.nextConveneTS - _G._now()//1000) > 0 then
						return
					end
					--召集所有人
					Game.me:GroupConvene()
				end
			else
				Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_NO_MEMBER)
			end
		end
	end
end

function P_HUDSideBar:OnClick_WBP_Quit()
	if self.bInTeam then
		Game.TeamSystem:EnterLeaveTeamProcess()
	elseif self.bInGroup then
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.TEAM_EXIT, function()
			Game.me:QuitGroup()
		end)
	end
end

function P_HUDSideBar:OnCellCompClicked(cellCompName)
	local funcName = P_HUDSideBar.CellCompName2ClickMap[cellCompName]
	local func = self[funcName]
	if func then
		xpcall(func, _G.CallBackError, self)
	end
end


--- 此处为自动生成
function P_HUDSideBar:on_Btn_ClickArea_lua_Clicked()
	self:OnClick_Btn()
end

return P_HUDSideBar
