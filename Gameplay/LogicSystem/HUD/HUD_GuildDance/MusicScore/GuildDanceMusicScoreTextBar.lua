local stringConst = kg_require("Data.Config.StringConst.StringConst")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class GuildDanceMusicScoreTextBar : UIComponent
---@field view GuildDanceMusicScoreTextBarBlueprint
local GuildDanceMusicScoreTextBar = DefineClass("GuildDanceMusicScoreTextBar", UIComponent)

GuildDanceMusicScoreTextBar.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function GuildDanceMusicScoreTextBar:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function GuildDanceMusicScoreTextBar:InitUIData()
	self.preEvaluateLevel = 0
end

--- UI组件初始化，此处为自动生成
function GuildDanceMusicScoreTextBar:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function GuildDanceMusicScoreTextBar:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function GuildDanceMusicScoreTextBar:InitUIView()
end

---组件刷新统一入口
function GuildDanceMusicScoreTextBar:Refresh(params)
	if not params then
		return
	end
	self:RefreshState(params.accuracy)
	self:RefreshScore(params.score)
	self:RefreshProgress(params.progress)
end

-- 刷新评分状态
function GuildDanceMusicScoreTextBar:RefreshState(accuracy)
	self.view.Text_Word_lua:SetText(stringConst.Get("MUSIC_NAME_1"))
	local evaluateLevel = Enum.EDancingPartyEvaluateLevelData.COOL
	for index, data in ksbcpairs(Game.TableData.GetDancingPartyEvaluateLevelDataTable()) do
		if data.Accuracy[1] <= accuracy and accuracy < data.Accuracy[2] then
			evaluateLevel = index
			break
		end
		if data.Const == "PERFECT" and data.Accuracy[1] <= accuracy then
			evaluateLevel = index
			break
		end
	end
	if self.preEvaluateLevel == evaluateLevel then
		return
	end
	self.preEvaluateLevel = evaluateLevel
	self.userWidget:Event_UI_Style(evaluateLevel - 1)
	self.view.Text_Title_lua:SetText(Game.TableData.GetDancingPartyEvaluateLevelDataRow(evaluateLevel).Level)
	self.view.WBP_ComBarNew_lua:Event_UI_Style(evaluateLevel == Enum.EDancingPartyEvaluateLevelData.PERFECT)
	self:PlayAnimation(self.view.Ani_Shengji)
end

-- 刷新分数,双方总分
function GuildDanceMusicScoreTextBar:RefreshScore(score)
	self.view.Text_Num_lua:SetText(score)
end

-- 刷新进度条
function GuildDanceMusicScoreTextBar:RefreshProgress(progress)
	self.view.WBP_ComBarNew_lua.PB_HPMain_lua:SetPercent(progress)
end

return GuildDanceMusicScoreTextBar
