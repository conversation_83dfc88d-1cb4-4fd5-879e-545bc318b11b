local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ConsignmentSellingList_Item : UIListItem
---@field view ConsignmentSellingList_ItemBlueprint
local ConsignmentSellingList_Item = DefineClass("ConsignmentSellingList_Item", UIListItem)

local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ItemConst = kg_require("Shared.ItemConst")

ConsignmentSellingList_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ConsignmentSellingList_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ConsignmentSellingList_Item:InitUIData()
	---@type EEConsignmentSellingListItemParams
	self.params = nil
end

--- UI组件初始化，此处为自动生成
function ConsignmentSellingList_Item:InitUIComponent()
    ---@type UIComButton
    self.Btn_OffShelfCom = self:CreateComponent(self.view.Btn_OffShelf, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function ConsignmentSellingList_Item:InitUIEvent()
    self:AddUIEvent(self.Btn_OffShelfCom.onClickEvent, "on_Btn_OffShelfCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ConsignmentSellingList_Item:InitUIView()
end

---@class EEConsignmentSellingListItemParams
---@field avatarID string
---@field createTime number
---@field orderID string
---@field sellNum number
---@field sellRate number
---@field totalSellNum number
---@field uid string
---@field nextOffShelfTime number 下次可上架时间

---面板打开的时候触发
---@param index number
---@param paramsTable table
function ConsignmentSellingList_Item:OnRefresh(index, paramsTable)
	self.params = paramsTable[index]
	
	self.view.Text_Serial:SetText(tostring(index))
	self.view.Text_OnTime:SetText(TimeUtils.FormatDateTimeString(self.params.createTime))
	self.view.Text_LeftNum:SetText(string.format("%d/%d", self.params.sellNum, self.params.totalSellNum))
	self.view.Text_Rate:SetText(tostring(self.params.sellRate))
	
	local now = _G._now()
	local sellTotalTime = tonumber(Enum.ECashMarketConstIntData["RMB_ORDER_LAST"] * 60000)
	self.view.Text_LeftTime:SetText(TimeUtils.FormatCountDownString(
		self.params.createTime + sellTotalTime - now, false))
	if self.params.nextOffShelfTime > now then
		local timeText = TimeUtils.FormatCountDownString(self.params.nextOffShelfTime - now, false)
		self.view.Btn_OffShelf:SetIsEnabled(false)
		self.Btn_OffShelfCom:SetName(timeText)
	else
		self.view.Btn_OffShelf:SetIsEnabled(true)
		self.Btn_OffShelfCom:SetName(StringConst.Get("SELREC_CANCELCONFIRM"))
	end
end

--- 此处为自动生成
function ConsignmentSellingList_Item:on_Btn_OffShelfCom_ClickEvent()
	if Game and Game.me then
		local img
		local CurrencyIcon = Game.TableData.GetItemNewDataRow(ItemConst.ITEM_SPECIAL_MONEY_COIN)
		if CurrencyIcon then
			img = Game.UIIconUtils.GetIconByItemId(ItemConst.ITEM_SPECIAL_MONEY_COIN,nil,true)
		end
		Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.CASHMARKET_CANCELCONFIRM, function()
			Game.me:TakeBackMoneyRequest(self.params.uid)
		end, nil, { self.params.sellRate, img, self.params.sellNum }, nil, nil)
	end
end

return ConsignmentSellingList_Item
