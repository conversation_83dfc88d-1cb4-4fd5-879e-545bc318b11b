local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class ConsignmentSoldList_Item : UIListItem
---@field view ConsignmentSoldList_ItemBlueprint
local ConsignmentSoldList_Item = DefineClass("ConsignmentSoldList_Item", UIListItem)

ConsignmentSoldList_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ConsignmentSoldList_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ConsignmentSoldList_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ConsignmentSoldList_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ConsignmentSoldList_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ConsignmentSoldList_Item:InitUIView()
end

---@class EEConsignmentSoldListItemParams
---@field sellTime number
---@field sellSuccessTime number
---@field sellMoney number
---@field sellRate number

---面板打开的时候触发
---@param index number
---@param paramsTable table
function ConsignmentSoldList_Item:OnRefresh(index, paramsTable)
	---@type EEConsignmentSoldListItemParams
	local params = paramsTable[index]
	
	self.view.Text_Serial:SetText(tostring(index))
	self.view.Text_OnTime:SetText(TimeUtils.FormatDateTimeString(params.sellTime))
	self.view.Text_LeftNum:SetText(tostring(params.sellMoney))
	self.view.Text_Rate:SetText(tostring(params.sellRate))
	self.view.Text_LeftTime:SetText(TimeUtils.FormatDateTimeString(params.sellSuccessTime * 1000))
end

return ConsignmentSoldList_Item
