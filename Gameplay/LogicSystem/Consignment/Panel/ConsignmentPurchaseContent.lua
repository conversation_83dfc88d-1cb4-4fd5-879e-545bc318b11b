local ConsignmentPurchaseRate = kg_require("Gameplay.LogicSystem.Consignment.Panel.ConsignmentPurchaseRate")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class ConsignmentPurchaseContent : UIComponent
---@field view ConsignmentPurchaseContentBlueprint
local ConsignmentPurchaseContent = DefineClass("ConsignmentPurchaseContent", UIComponent)

local itemConst = kg_require("Shared.ItemConst")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ConsignmentPurchaseContent.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ConsignmentPurchaseContent:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ConsignmentPurchaseContent:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ConsignmentPurchaseContent:InitUIComponent()
    ---@type ConsignmentPurchaseRate
    self.LowRateCom = self:CreateComponent(self.view.LowRate, ConsignmentPurchaseRate)
    ---@type ConsignmentPurchaseRate
    self.AveRateCom = self:CreateComponent(self.view.AveRate, ConsignmentPurchaseRate)
end

---UI事件在这里注册，此处为自动生成
function ConsignmentPurchaseContent:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ConsignmentPurchaseContent:InitUIView()
	self:SetTradeCurrencyIcon(itemConst.ITEM_SPECIAL_MONEY_CASH)
	self.view.Text_Title:SetText(StringConst.Get("BUY_MYCOIN"))
	self.LowRateCom:SetText(StringConst.Get("BUY_CURRATE"), "")
	self.AveRateCom:SetText(StringConst.Get("BUY_EXPRATE"), StringConst.Get("BUY_EXPRATETIP"))
end

---组件刷新统一入口, 汇率值刷新
---@param lowRateNum number 最小汇率
---@param aveRateNum number 平均汇率
function ConsignmentPurchaseContent:Refresh(lowRateNum, aveRateNum)
	self.LowRateCom:Refresh(lowRateNum)
	self.AveRateCom:Refresh(aveRateNum)
end

---设置当前交易货币的文本
function ConsignmentPurchaseContent:SetTradeCurrencyNumText(numText)
	self.view.WBP_TradeCurrency.Text_Count:SetText(numText)
end

---设置当前交易货币的图标
function ConsignmentPurchaseContent:SetTradeCurrencyIcon(icon)
	local iconPath = Game.UIIconUtils.GetIconByItemId(icon)
	self:SetImage(self.view.WBP_TradeCurrency.Icon, iconPath)
	--设定样式(type, isLack) 固定为 (1, false)
	self.view.WBP_TradeCurrency:SetType(1, false)
end

return ConsignmentPurchaseContent
