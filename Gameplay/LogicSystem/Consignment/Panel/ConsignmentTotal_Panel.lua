local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local ConsignmentPurchaseContent = kg_require("Gameplay.LogicSystem.Consignment.Panel.ConsignmentPurchaseContent")
local ConsignmentSellContent = kg_require("Gameplay.LogicSystem.Consignment.Panel.ConsignmentSellContent")
local ConsignmentTABHList = kg_require("Gameplay.LogicSystem.Consignment.Panel.ConsignmentTABHList")
local UIComFrame = kg_require("Framework.KGFramework.KGUI.Component.Panel.UIComFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ConsignmentTotal_Panel : UIPanel
---@field view ConsignmentTotal_PanelBlueprint
local ConsignmentTotal_Panel = DefineClass("ConsignmentTotal_Panel", UIPanel)

local StringConst = require "Data.Config.StringConst.StringConst"
local itemConst = kg_require("Shared.ItemConst")
local ESlateVisibility = import("ESlateVisibility")

ConsignmentTotal_Panel.eventBindMap = {
	[EEventTypesV2.CONSIGNMENT_LIST_UPDATE] = "OnRateListUpdate",
	[EEventTypesV2.CONSIGNMENT_OWNER_SELLINGLIST] = "SetSellingData",
	[EEventTypesV2.CONSIGNMENT_SOLD_NOTIFY] = "OnMoneySold",
	[EEventTypesV2.CONSIGNMENT_OWNER_OFFLIST] = "OffShelfItem",
}
--region Lifecycle
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ConsignmentTotal_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ConsignmentTotal_Panel:InitUIData()
	---@type table 	-页签枚举
	self.FuncEnum = {
		Sale = 1,
		Buy = 2
	}
	---@type table 	-控件样式枚举
	self.WidgetEnum = {
		Buy = 0,
		Sale = 1,
	}
	---@type number -当前页签
	self.FuncType = self.FuncEnum.Sale
	---@type table 	-自己的在售列表
	self.OwnerSellList = {}
	---@type table 	-总在售列表
	self.SellList = {}
	---@type number -出售数量
	self.SellCount      = 10
	---@type number -购买数量
	self.BuyCount       = 1
	---@type number -最大能购买数量，即使自己买不起
	self.MaxCanBuyNum   = 1
	---@type number -最小汇率
	self.MinRate        = 1
	---@type number -平均汇率
	self.AveRate        = 0
	---@type number	-总购价
	self.TotalBuyPrice  = 0
	---@type number -总售价
	self.TotalSellPrice = 0
	---@type number -金币可购买数量，自己得买得起
	self.PennyCanBuyNum = 0
	---@type table 	-购买选中数据列表
	self.BuyData        = {}
	---@type number -服务器汇率
	self.ServiceCharge  = Enum.ECashMarketConstIntData["RMB_TAXPERCENT"]
	---@type number -可购买最小数量
	self.BuyMin         = Enum.ECashMarketConstIntData["COIN_AMOUNT_MIN"]
	---@type number -可购买最大数量
	self.BuyMax         = Enum.ECashMarketConstIntData["COIN_AMOUNT_MAX"]
	---@type number -可出售最小数量
	self.SellMin        = Enum.ECashMarketConstIntData["RMB_AMOUNT_MIN"]
	---@type number -可出售最大数量
	self.SellMax        = Enum.ECashMarketConstIntData["RMB_AMOUNT_MAX"]
	---@type number -汇率最小值
	self.RateMin        = Enum.ECashMarketConstIntData["RMB_RATE_MIN"]
	---@type number -汇率最大值
	self.RateMax        = Enum.ECashMarketConstIntData["RMB_RATE_MAX"]
	---@type number -当前出售汇率
	self.SellRate       = self.RateMin
	---@type table 	-全部列表
	self.AllList = {}
	---@type number -寄售递增值
	self.SellSpan = 1
	---@type number -购买递增值
	self.BuySpan = 1
	---@type number -税率递增值
	self.RateSpan = 1
	---@type number -最终出售价格的数字样式
	self.resultMoneyStyle = 1
end

--- UI组件初始化，此处为自动生成
function ConsignmentTotal_Panel:InitUIComponent()
    ---@type UIComNumberSlider -寄售/购买数量内容输入
    self.SellNumInputCom = self:CreateComponent(self.view.SellNumInput, UIComNumberSlider)
    ---@type UIComButton
    self.ConsignmentHistoryBtnCom = self:CreateComponent(self.view.ConsignmentHistoryBtn, UIComButton)
    ---@type UIComButton
    self.SaleBtnCom = self:CreateComponent(self.view.SaleBtn, UIComButton)
    ---@type ConsignmentPurchaseContent -税率显示内容
    self.BuyContentCom = self:CreateComponent(self.view.BuyContent, ConsignmentPurchaseContent)
    ---@type ConsignmentSellContent -汇率设置显示内容
    self.SaleContentCom = self:CreateComponent(self.view.SaleContent, ConsignmentSellContent)
    ---@type ConsignmentTABHList
    self.TABHListCom = self:CreateComponent(self.view.TABHList, ConsignmentTABHList)
    ---@type UIListView
    self.List_RateCom = self:CreateComponent(self.view.List_Rate, UIListView)
    ---@type UIComFrame
    self.WBP_ComPanelCom = self:CreateComponent(self.view.WBP_ComPanel, UIComFrame)
end

---UI事件在这里注册，此处为自动生成
function ConsignmentTotal_Panel:InitUIEvent()
    self:AddUIEvent(self.SaleBtnCom.onClickEvent, "on_SaleBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComPanelCom.onPreCloseEvent, "on_WBP_ComPanelCom_PreCloseEvent")
    self:AddUIEvent(self.ConsignmentHistoryBtnCom.onClickEvent, "on_ConsignmentHistoryBtnCom_ClickEvent")
    self:AddUIEvent(self.TABHListCom.onClickTab1Event, "on_TABHListCom_ClickTab1Event")
    self:AddUIEvent(self.TABHListCom.onClickTab2Event, "on_TABHListCom_ClickTab2Event")
    self:AddUIEvent(self.SaleContentCom.onValueChange, "on_SaleContentCom_ValueChange")
    self:AddUIEvent(self.SaleContentCom.onClickInput, "on_SaleContentCom_ClickInput")
    self:AddUIEvent(self.SellNumInputCom.onValueChange, "on_SellNumInputCom_ValueChange")
    self:AddUIEvent(self.SellNumInputCom.onClickInput, "on_SellNumInputCom_ClickInput")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ConsignmentTotal_Panel:InitUIView()
	self.view.Text_Title:SetText(StringConst.Get("INFORM_TITLE"))
	self.view.Text_TitleRate:SetText(StringConst.Get("INFORM_RATE"))
	self.view.Text_TitleNum:SetText(StringConst.Get("INFORM_MONEY"))
	-- 设置标题和Tips
	self.WBP_ComPanelCom:Refresh(StringConst.Get("PAGE_TITLE"), Enum.ETipsData["CASHMARKET_TIPS"])
	self.view.WBP_ComPanel.WBP_ComBackTitle.Btn_Tips:SetVisibility(ESlateVisibility.Visible)
	-- 设置交易/出售选择框
	self.TABHListCom:SetText(StringConst.Get("SELL_TRANSNAME"), StringConst.Get("BUY_TRANSNAME"))
	self.SaleContentCom:SetText(StringConst.Get("SELL_SETRATE"))
	-- 设置税率列表
	self:RefreshRateList()
	-- 设置遮挡
	self.view.WBP_ConsignmentBg:SetVisibility(ESlateVisibility.Visible)
end

---面板打开的时候触发
function ConsignmentTotal_Panel:OnRefresh()
	self.SellCount = 10
	self.BuyCount = 1
	self.MaxCanBuyNum = 1
	self.MinRate = 1
	self.AveRate = 0
	self.TotalBuyPrice = 0
	self.BuyData = {}
	self.FuncType = self.FuncEnum.Sale
	self.HasInitRate = false
	self.SellRate = self.RateMin
	self:RefreshUI()
	self:StartTimer("SellListRefresh",
		function()
			if Game and Game.me then
				Game.me:GetMoneySellListRequest(0)
			end
		end, Enum.ECashMarketConstIntData["INFORM_REFRESH_CD"] * 1000, -1, nil)
	if Game and Game.me then
		Game.me:GetMoneySellListRequest(1)
		Game.me:GetOwnerSellMoneyInfoListRequest()
	end
	self.view.VX_Ink_Spine:SetAnimation(1, "Ani_Fadein", false)
	self.view.VX_Ink_Spine:AddAnimation(1, "Ani_Loop", true, 1.3)
	self:PlayAnimation(self.userWidget.Ani_Fadein, nil, self.userWidget)
	self:PlayAnimation(self.view.WBP_ConsignmentBg.Ani_Fadein, nil, self.view.WBP_ConsignmentBg)
end
--endregion

--region UI Logic

---刷新页签
function ConsignmentTotal_Panel:RefreshUI()
	self.TABHListCom:Refresh(self.FuncType)
	if self.FuncType == self.FuncEnum.Sale then
		self:OnRefreshSaleUI()
	else
		self:OnRefreshBuyUI()
	end
	self:RefreshRateList()
end

---刷新实时汇率列表
function ConsignmentTotal_Panel:RefreshRateList()
	local datas = {}
	local otherInfo = {}
	for i, params in ipairs(self.AllList) do
		datas[i] = i
		otherInfo[i] = params
	end
	self.List_RateCom:Refresh(datas, 1, otherInfo)
end

function ConsignmentTotal_Panel:OnRefreshSaleUI()
	self.view.Text_SettingTitle:SetText(StringConst.Get("SELL_TITLE"))
	self.view.Text_NumTitle:SetText(StringConst.Get("SELL_SETSELLNUM"))
	self.view.Text_Result:SetText(StringConst.Get("SELL_EXPECTGET"))
	self.view.Text_Tips:SetVisibility(ESlateVisibility.Visible)
	self.view.Text_Tips:SetText(string.format(StringConst.Get("SELL_TAXTIP"), self.ServiceCharge))
	self.view.ResultMoney:SetType(self.resultMoneyStyle, false)
	
	self:SetCurrencyIcon(itemConst.ITEM_SPECIAL_MONEY_COIN, self.view.Icon_Currency)
	self:SetCurrencyIcon(itemConst.ITEM_SPECIAL_MONEY_CASH, self.view.ResultMoney.Icon)
	
	self.SaleBtnCom:SetName(StringConst.Get("SELL_CONFIRM"))
	self.ConsignmentHistoryBtnCom:SetName(StringConst.Get("SELREC_TITLE"))
	self.SellNumInputCom:Refresh(self.SellCount, self.SellMin, self:GetMaxCanSellNumVisible(), self.SellSpan)
	
	self:RefreshSalePanel()
	self:RefreshSalePrice()
	self:OnCountChange(self.SellCount)
end

function ConsignmentTotal_Panel:OnRefreshBuyUI()
	self.view.Text_SettingTitle:SetText(StringConst.Get("BUY_NAME"))
	self.view.Text_NumTitle:SetText(StringConst.Get("BUY_SETBUYNUM"))
	self.view.Text_Result:SetText(StringConst.Get("BUY_EXPECTCONSUME"))
	self.view.Text_Tips:SetVisibility(ESlateVisibility.Collapsed)
	self.view.Text_Tips:SetText("")
	self.view.ResultMoney:SetType(self.resultMoneyStyle, false)
	
	self:SetCurrencyIcon(itemConst.ITEM_SPECIAL_MONEY_COIN_BOUND, self.view.Icon_Currency)
	self:SetCurrencyIcon(itemConst.ITEM_SPECIAL_MONEY_CASH, self.view.ResultMoney.Icon)
	
	self.SaleBtnCom:SetName(StringConst.Get("BUY_CONFIRM"))
	self.ConsignmentHistoryBtnCom:SetName(StringConst.Get("BUYREC_TITLE"))
	
	self.BuyContentCom:Refresh(self.MinRate, self.AveRate)
	self.SellNumInputCom:Refresh(self.BuyCount, self.BuyMin, self:GetMaxCanBuyNumVisible(), self.BuySpan)
	
	self:RefreshBuyPanel()
	self:RefreshBuyPrice()
	self:OnCountChange(self.BuyCount)
end

function ConsignmentTotal_Panel:RefreshBuyPanel()
	self.view.WS_Content:SetActiveWidgetIndex(self.WidgetEnum.Buy)
	local pennyCount = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_CASH)
	self.BuyContentCom:SetTradeCurrencyNumText(self:GetCurrentNumString(pennyCount))
end

function ConsignmentTotal_Panel:RefreshSalePanel()
	self.view.WS_Content:SetActiveWidgetIndex(self.WidgetEnum.Sale)
	self.SaleContentCom:Refresh(self.SellRate, self.RateMin, self.RateMax, self.RateSpan)
end

function ConsignmentTotal_Panel:RefreshBuyPrice()
	self:CalculateRate()
	self.BuyContentCom:Refresh(self.MinRate, self.AveRate)
	if Game and Game.me then
		local pennyCount = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_CASH)
		self.view.ResultMoney:SetType(self.resultMoneyStyle, pennyCount < self.TotalBuyPrice)
		self.view.ResultMoney.Text_Count:SetText(self:GetCurrentNumString(self.TotalBuyPrice))
	end
end

function ConsignmentTotal_Panel:RefreshSalePrice()
	self.TotalSellPrice = (self.SellCount * self.SellRate * (100 - self.ServiceCharge)) // 100
	self.view.ResultMoney.Text_Count:SetText(self:GetCurrentNumString(self.TotalSellPrice))
end

function ConsignmentTotal_Panel:SetSaleOrBuyNum(num)
	if self.FuncType == self.FuncEnum.Sale then
		self.SellCount = num
		self.SellNumInputCom:Refresh(num, self.SellMin, self:GetMaxCanSellNumVisible(), self.SellSpan)
		self:RefreshUI()
	elseif self.FuncType == self.FuncEnum.Buy then
		self.BuyCount = num
		self.SellNumInputCom:Refresh(num, self.BuyMin, self:GetMaxCanBuyNumVisible(), self.BuySpan)
		self:RefreshUI()
	end
end

--endregion

--region Event

--- 此处为自动生成
function ConsignmentTotal_Panel:on_SaleBtnCom_ClickEvent()
	if Game and Game.me then
		if self.FuncType == self.FuncEnum.Buy then
			if self.AveRate == 0 then
				Game.ReminderManager:AddReminderById(
					Enum.EReminderTextData.MONEY_SALE_BUY_MONEY_FAIL_BY_NUM_LIMIT)
				return
			end
			if Game and Game.me then
				local pennyCount = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_CASH)
				if pennyCount < self.TotalBuyPrice then
					Game.ReminderManager:AddReminderById(
						Enum.EReminderTextData.MONEY_SALE_BUY_MONEY_FAIL_BY_LACK_MONEY)
				end
				Game.NewUIManager:OpenPanel(UIPanelConfig.ConsignmentPopup_Panel, false, self.AveRate, self.BuyCount, self.TotalBuyPrice, self.BuyData)
			end
		else
			local myCount = 0
			for _, v in pairs(self.OwnerSellList) do
				if Game and Game.me and Game.me.eid == v.avatarID then
					myCount = myCount + 1
				end
			end
			local myGoldNum = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_COIN)
			if myCount >= Enum.ECashMarketConstIntData.RMB_ORDER_MAX then
				Game.ReminderManager:AddReminderById(
					Enum.EReminderTextData.MONEY_SALE_SELL_MONEY_FAIL_BY_ORDER_MAX_LIMIT)
			elseif self.SellCount > myGoldNum then
				Game.ReminderManager:AddReminderById(
					Enum.EReminderTextData.MOENY_SALE_SELL_MONEY_FAIL_BY_NUM_LIMIT)
			else
				Game.NewUIManager:OpenPanel(UIPanelConfig.ConsignmentPopup_Panel,  true, self.SellRate, self.SellCount, self.TotalSellPrice)
			end
		end
	end
end

--- 此处为自动生成
---@return bool
function ConsignmentTotal_Panel:on_WBP_ComPanelCom_PreCloseEvent()
	self:CloseSelf()
end

--- 此处为自动生成
function ConsignmentTotal_Panel:on_ConsignmentHistoryBtnCom_ClickEvent()
	if self.FuncType == self.FuncEnum.Sale then
		Game.NewUIManager:OpenPanel(UIPanelConfig.ConsignmentHistoryPopup_Panel, true)
	else
		Game.NewUIManager:OpenPanel(UIPanelConfig.ConsignmentHistoryPopup_Panel, false)
	end
end

--- 此处为自动生成
function ConsignmentTotal_Panel:on_TABHListCom_ClickTab1Event()
	self.FuncType = self.FuncEnum.Sale
	self:RefreshUI()
end

--- 此处为自动生成
function ConsignmentTotal_Panel:on_TABHListCom_ClickTab2Event()
	self.FuncType = self.FuncEnum.Buy
	self:RefreshUI()
end

--- 此处为自动生成
---@param value number
function ConsignmentTotal_Panel:on_SaleContentCom_ValueChange(value)
	self.SellRate = value
	self:RefreshSalePrice()
end

--- 此处为自动生成
function ConsignmentTotal_Panel:on_SaleContentCom_ClickInput()
	-- 打开计算题输入，最后把值赋值到税率设置中
	local viewportPosition, anchors = Game.UITipsPosAutoFollowUtils.GetPanelAdaptPosFollowWidget(self.view.SaleContent)
	UI.ShowUI('ComNumInput_Panel', {
		InputNum = self.SellRate,
		SelectableMaximum = self.RateMax,
		FinishCallback = function(Succ, InputNum)
			if Succ then
				if self.FuncType == self.FuncEnum.Sale then
					self.SellRate = math.clamp(InputNum, self.RateMin, self.RateMax)
					self:RefreshUI()
				end
			end
		end,
		viewportPosition = viewportPosition,
		anchors = anchors,
	})
end

--- 此处为自动生成
---@param value number
function ConsignmentTotal_Panel:on_SellNumInputCom_ValueChange(value)
	if self.FuncType == self.FuncEnum.Sale then
		self.SellCount = value
		self:RefreshSalePrice()
	elseif self.FuncType == self.FuncEnum.Buy then
		self.BuyCount = value
		self:RefreshBuyPrice()
	end
end

--- 此处为自动生成
function ConsignmentTotal_Panel:on_SellNumInputCom_ClickInput()
	-- 打开计算题输入，最后把值赋值到寄售数量中
	local viewportPosition, anchors = Game.UITipsPosAutoFollowUtils.GetPanelAdaptPosFollowWidget(self.view.SellNumInput)
	UI.ShowUI('ComNumInput_Panel', {
		InputNum = self.FuncType == self.FuncEnum.Buy and self.BuyCount or self.SellCount,
		SelectableMaximum = self.FuncType == self.FuncEnum.Buy and self:GetMaxCanBuyNumVisible() or self:GetMaxCanSellNumVisible(),
		FinishCallback = function(Succ, InputNum)
			if Succ then
				self:OnCountChange(InputNum)
				if self.FuncType == self.FuncEnum.Buy then
					InputNum = math.clamp(InputNum, self.BuyMin, self:GetMaxCanBuyNumVisible())
				elseif  self.FuncType == self.FuncEnum.Sale then
					InputNum = math.clamp(InputNum, self.SellMin, self:GetMaxCanSellNumVisible())
				end
				self:SetSaleOrBuyNum(InputNum)
			end
		end,
		viewportPosition = viewportPosition,
		anchors = anchors,
	})
end

--endregion

--region Common

function ConsignmentTotal_Panel:SetCurrencyIcon(currencyType, image)
	local iconPath = Game.UIIconUtils.GetIconByItemId(currencyType)
	self:SetImage(image, iconPath)
end

---获取这个数字对应的字符串，英文格式
function ConsignmentTotal_Panel:GetCurrentNumString(num)
	local numList = {}
	while num > 0 do
		if num >= 1000 then
			table.insert(numList, 1, string.format("%.3d", num % 1000))
		else
			table.insert(numList, 1, string.format("%d", num % 1000))
		end
		num = num // 1000
	end
	return table.concat(numList, ",")
end
 
---获取可以显示购买的最大数量
---@return number
function ConsignmentTotal_Panel:GetMaxCanBuyNumVisible()
	local result = self.BuyMax
	if self.MaxCanBuyNum > self.BuyMin then
		result = math.min(result, self.MaxCanBuyNum)
	end
	if self.PennyCanBuyNum > self.BuyMin then
		result = math.min(result, self.PennyCanBuyNum)
	end
    return result
end

---获取可以显示寄售的最大数量
---@return number
function ConsignmentTotal_Panel:GetMaxCanSellNumVisible()
	local result = self.SellMax
	if Game and Game.me then
		local goldNum = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_COIN)
		if goldNum > self.SellMin then
			result = math.min(goldNum, result)
		end
	end
	return result
end

--endregion

--region Sell/Buy Logic

---当数值改变时判断显示什么提示
function ConsignmentTotal_Panel:OnCountChange(InputNum)
	if self.FuncType == self.FuncEnum.Buy then
		if InputNum < self.BuyMin then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.MONEY_SALE_BUY_MONEY_FAIL_BY_NUM_MIN_LIMIT)
			return
		end
		if InputNum > self.BuyMax or (InputNum > self.MaxCanBuyNum and self.MaxCanBuyNum >= self.BuyMin) then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.MONEY_SALE_BUY_MONEY_FAIL_BY_NUM_MAX_LIMIT)
			return
		end
		if InputNum ~= self.BuyMin and InputNum > self.PennyCanBuyNum then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.MONEY_SALE_BUY_MONEY_FAIL_BY_NUM_MAX_LIMIT)
			return
		end
	else
		if InputNum < self.SellMin then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.MONEY_SALE_SELL_MONEY_FAIL_BY_NUM_MIN_LIMIT)
			return
		end
		if Game == nil or Game.me == nil then
			return
		end
		local poundNum = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_COIN)
		poundNum = poundNum < 10 and 10 or poundNum
		if InputNum > self.SellMax or InputNum > poundNum then
			Game.ReminderManager:AddReminderById(
				Enum.EReminderTextData.MONEY_SALE_SELL_MONEY_FAIL_BY_NUM_MAX_LIMIT)
			return
		end
	end
end

---刷新在售列表数据，对前20条数据及自身数据进行合并去重
function ConsignmentTotal_Panel:OnRefreshRateList()
	table.clear(self.AllList)
	local t_sellList = {}
	for _, v in pairs(self.SellList) do
		t_sellList[v.orderID] = v
	end
	for k, v in pairs(self.OwnerSellList) do
		v.orderID = k
		v.avatarID = Game.me.eid
		if v.sellNum <= 0 then
			t_sellList[k] = nil
		else
			t_sellList[k] = v
		end
	end
	for k, v in pairs(t_sellList) do
		table.insert(self.AllList, v)
	end
	table.sort(self.AllList, function(a, b)
		if a.sellRate == b.sellRate then
			return a.createTime < b.createTime
		else
			return a.sellRate < b.sellRate
		end
	end)
	if not self.HasInitedRate then
		if #self.AllList > 0 and self.AllList[1].sellRate > self.RateMin then
			self.SellRate = self.AllList[1].sellRate - 1
		end
		self.HasInitedRate = true
		self:RefreshRateList()
	else
		self:RefreshRateList()
	end
	
	self:CalCanBuyMaxNum()
	
	if self.FuncType == self.FuncEnum.Sale then
		self:OnRefreshSaleUI()
	else
		self:OnRefreshBuyUI()
	end
end

function ConsignmentTotal_Panel:OnRateListUpdate(sellList, ownerSellMoneyInfo)
	self:FixCanSellNum()
	self.SellList = sellList
	if ownerSellMoneyInfo then
		self.OwnerSellList[ownerSellMoneyInfo.orderID] = ownerSellMoneyInfo
	end
	self:OnRefreshRateList()
end

function ConsignmentTotal_Panel:SetSellingData(sellingData)
	self.OwnerSellList = sellingData
	self:OnRefreshRateList()
end

function ConsignmentTotal_Panel:OnMoneySold(uid, sellRate, sellNum)
	if self.OwnerSellList[uid] then
		self.OwnerSellList[uid].sellNum = self.OwnerSellList[uid].sellNum - sellNum
		if self.OwnerSellList[uid].sellNum == 0 then
			self.OwnerSellList[uid] = nil
			for i = 1, #self.SellList do
				if self.SellList[i].orderID == uid then
					table.remove(self.SellList, i)
					break
				end
			end
		end
		self:OnRefreshRateList()
	end
end

function ConsignmentTotal_Panel:OffShelfItem(uid)
	if self.OwnerSellList[uid] then
		self.OwnerSellList[uid] = nil
		for i = 1, #self.SellList do
			if self.SellList[i].orderID == uid then
				table.remove(self.SellList, i)
				break
			end
		end
		self:OnRefreshRateList()
	end
end

function ConsignmentTotal_Panel:CalCanBuyMaxNum()
	self.MaxCanBuyNum = 0
	self.MinRate = 0
	for _, v in pairs(self.SellList) do
		local sellNum = v.sellNum
		if self.OwnerSellList[v.orderID] then
			sellNum = self.OwnerSellList[v.orderID].sellNum
		end
		self.MaxCanBuyNum = self.MaxCanBuyNum + sellNum
		if v.sellRate < self.MinRate or self.MinRate == 0 then
			self.MinRate = v.sellRate
		end
	end
	local pennyCount = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_CASH)
	self.PennyCanBuyNum = 0
	for _, v in pairs(self.SellList) do
		local sellNum = v.sellNum
		if self.OwnerSellList[v.orderID] then
			sellNum = self.OwnerSellList[v.orderID].sellNum
		end
		if pennyCount > v.sellRate * sellNum then
			self.PennyCanBuyNum = self.PennyCanBuyNum + sellNum
			pennyCount = pennyCount - v.sellRate * sellNum
		else
			self.PennyCanBuyNum = self.PennyCanBuyNum + math.floor(pennyCount // v.sellRate)
			break
		end
	end
end

function ConsignmentTotal_Panel:FixCanSellNum()
	local goldCount = Game.CurrencySystem:GetMoneyByType(itemConst.ITEM_SPECIAL_MONEY_COIN)
	if goldCount < self.SellCount then
		self.SellCount = goldCount
	end
	self.SellCount = self.SellCount < self.SellMin and self.SellMin or self.SellCount
end

function ConsignmentTotal_Panel:CalculateRate()
	self.TotalBuyPrice = 0
	local count = 0
	table.clear(self.BuyData)
	for _, v in pairs(self.SellList) do
		if count + v.sellNum >= self.BuyCount then
			self.TotalBuyPrice = self.TotalBuyPrice + (self.BuyCount - count) * v.sellRate
			if self.BuyData[v.sellRate] then
				self.BuyData[v.sellRate] = self.BuyData[v.sellRate] + self.BuyCount - count
			else
				self.BuyData[v.sellRate] = self.BuyCount - count
			end
			break;
		else
			self.TotalBuyPrice = self.TotalBuyPrice + v.sellNum * v.sellRate
			count = count + v.sellNum
			if self.BuyData[v.sellRate] then
				self.BuyData[v.sellRate] = self.BuyData[v.sellRate] + v.sellNum
			else
				self.BuyData[v.sellRate] = v.sellNum
			end
		end
	end
	self.AveRate = math.ceil(self.TotalBuyPrice / self.BuyCount)
end

--endregion

return ConsignmentTotal_Panel
