local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local EquipAttributeChange_Item = kg_require("Gameplay.LogicSystem.Equip.AttributeChange.EquipAttributeChange_Item")
local Euipment_Sub = kg_require("Gameplay.LogicSystem.Equip.Euipment_Sub")
local EquipInfo_Sub = kg_require("Gameplay.LogicSystem.Equip.EquipInfo_Sub")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class EquipAttributeChangePage : UIComponent
---@field view EquipAttributeChangePageBlueprint
local EquipAttributeChangePage = DefineClass("EquipAttributeChangePage", UIComponent)

local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

EquipAttributeChangePage.eventBindMap = {
	ON_EQUIP_SWITCH_FIXED = "OnSwitchFixed",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EquipAttributeChangePage:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EquipAttributeChangePage:InitUIData()
	self.CurrSlotIdx = nil -- 当前装备槽位
	self.attrItemList = {} -- 随机属性widget
	self.currWordGroup = {} -- 当前词条的group
	self.currSpace = {} -- 当前空间的group
	self.spaceCache = {} -- cache
	self.currAttrSel = nil -- 当前选中的属性
	self.canClickDict = {} -- 记录可选
end

--- UI组件初始化，此处为自动生成
function EquipAttributeChangePage:InitUIComponent()
    ---@type UIListView
    self.WBP_ComListCom = self:CreateComponent(self.view.WBP_ComList, UIListView)
    ---@type EquipAttributeChange_Item
    self.Attr3Com = self:CreateComponent(self.view.Attr3, EquipAttributeChange_Item)
    ---@type EquipAttributeChange_Item
    self.Attr2Com = self:CreateComponent(self.view.Attr2, EquipAttributeChange_Item)
    ---@type EquipAttributeChange_Item
    self.Attr1Com = self:CreateComponent(self.view.Attr1, EquipAttributeChange_Item)
    ---@type EquipInfo_Sub
    self.WBP_EquipInfo_SubCom = self:CreateComponent(self.view.WBP_EquipInfo_Sub, EquipInfo_Sub)
    ---@type Euipment_Sub
    self.WBP_Euipment_SubCom = self:CreateComponent(self.view.WBP_Euipment_Sub, Euipment_Sub)

	for i = 1,3 do
		self.attrItemList[i] = self[string.format("Attr%dCom", i)]
	end
end

---UI事件在这里注册，此处为自动生成
function EquipAttributeChangePage:InitUIEvent()
    self:AddUIEvent(self.WBP_ComListCom.onItemClicked, "on_WBP_ComListCom_ItemClicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EquipAttributeChangePage:InitUIView()
	self.view.Text_Tip:SetText(StringConst.Get("EQUIP_TAB2_TIP2"))
	self.view.Text_Tip2:SetText(StringConst.Get("EQUIP_TAB2_TIP3"))
end

---面板打开的时候触发
function EquipAttributeChangePage:OnRefresh(...)
end

-- 装备页签通用方法
function EquipAttributeChangePage:HideSelfTab()
	self.widget:SetVisibility(ESlateVisibility.Collapsed)
end
function EquipAttributeChangePage:ShowSelfTab(slotIdx)
	self.widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

	-- 不变的设置
	local equipInfo = Game.EquipmentSystem:GetEquipBarSlot(slotIdx)
	local slotData = Game.TableData.GetEquipmentSlotDataRow(slotIdx)
	self.view.Text_Spacial:SetText(slotData.Name..StringConst.Get("EQUIP_WORD"))
	self.WBP_EquipInfo_SubCom:HideAttr()
	self.WBP_Euipment_SubCom:RefreshUI(equipInfo)
	self.CurrSlotIdx = slotIdx

	self:RefreshUI()
end

-- 非置换过程中的界面状态
function EquipAttributeChangePage:RefreshUI(needVX)
	local equipInfo = Game.EquipmentSystem:GetEquipBarSlot(self.CurrSlotIdx)
	self.WBP_EquipInfo_SubCom:RefreshUI(equipInfo)
	table.clear(self.currWordGroup)
	table.clear(self.spaceCache)
	self.currAttrSel = nil

	-- 词条空间
	local equipData = Game.TableData.GetEquipmentDataRow(equipInfo.itemId)
	local typeData = Game.TableData.GetEquipmentTypeDataRow(equipData.subType)
	self.spaceCache = Game.EquipmentSystem:GetPropSpace(typeData.Slot[1])
	table.clear(self.currSpace)
	for k,v in pairs(self.spaceCache) do
		table.insert(self.currSpace, {gid = k, pid = v})
	end
	table.sort(self.currSpace,function(a,b)
		return a.gid < b.gid
	end)
	local listData = {}
	for i = 1, #self.currSpace do
		listData[i] = {i, self.currSpace[i]}
	end
	self.WBP_ComListCom:Refresh(listData)
	
	-- 右侧词条
	local fixedProp = equipInfo.equipmentPropInfo.fixedPropInfo.fixedProps
	for i = 1,3 do
		if fixedProp[i] then
			local fixedData = Game.TableData.GetEquipmentWordFixedWordDataRow(fixedProp[i])
			table.insert(self.currWordGroup, fixedData.Group)
			local canSwitch = false
			local hasHigher = false
			if self.spaceCache[fixedData.Group] and self.spaceCache[fixedData.Group]>fixedProp[i] then
				hasHigher = true
			end
			for gid,pid in pairs(self.spaceCache) do
				local bSwitch = true
				for _,wordID in ipairs(fixedProp) do
					local fData = Game.TableData.GetEquipmentWordFixedWordDataRow(wordID)
					if gid == fData.Group and pid and pid > 0 then
						bSwitch = false
						break
					end
				end
				if bSwitch and pid and pid > 0 then
					canSwitch = true
					break
				end
			end
			self.attrItemList[i].userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self.attrItemList[i]:SetAttrItem(fixedProp[i],hasHigher,canSwitch,function()
				self:OnClickAttrItem(i)
			end, self.spaceCache[fixedData.Group], needVX)
		else
			self.attrItemList[i].userWidget:SetVisibility(ESlateVisibility.Collapsed)
		end
	end

	self.userWidget:Event_UI_ItemChange(#fixedProp - 1)
end

-- item eventBind
function EquipAttributeChangePage:OnClickAttrItem(idx)
	if self.currAttrSel then
		self.attrItemList[self.currAttrSel]:SetSelect(false)
	end
	self.attrItemList[idx]:SetSelect(true)
	self.currAttrSel = idx
	self:SelectSpace()
end

-- 查询该Index的空间词条是否在可被替换状态
function EquipAttributeChangePage:CanReplace(index)
	return false
end

function EquipAttributeChangePage:OnRefresh_SpaceList(widget, index, bSelect)
	local groupData = Game.TableData.GetEquipmentWordFixedGroupDataRow(self.currSpace[index].gid)
	local propName = groupData.Des
	widget:SetPropName(propName)
	widget:SetIcon(groupData.Icon)
	if self.currSpace[index].pid ~= 0 then
		local wordData = Game.TableData.GetEquipmentWordFixedWordDataRow(self.currSpace[index].pid)
		if wordData.FightProp and table.count(wordData.FightProp)>0 then
			local attr, propVal = ksbcnext(wordData.FightProp)
			local modeID = Game.TableData.Get_propChangeModeMap()[attr]
			local modeData = Game.TableData.GetFightPropModeDataRow(modeID)
			local numStr = Game.EquipmentSystem:GetValShowStr(propVal[1], modeData.ShowType)
			widget:SetVal(numStr)
		elseif wordData.SkillLevel and table.count(wordData.SkillLevel)>0 then
			local _,skillVal = ksbcnext(wordData.SkillLevel)
			widget:SetVal(skillVal[1])
		end
	else
		widget:SetNoVal()
	end
	--if self.currSpace[index].pid == 0 then
	--	-- 未激活
	--	widget:SetStyle(0)
	----elseif self:CanReplace(index) then
	----	-- 可选择替换
	----	widget:SetStyle(2)
	--else
	--	-- 已录入
	--	widget:SetStyle(1)
	--end
	widget:SetStyle(0)
	widget:SetCanHover(false)
	widget:Refresh(index, propName)
end
function EquipAttributeChangePage:OnClick_SpaceList(widget,index,bSelect)
	-- 不处在选中属性状态时
	if not self.currAttrSel then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB2_SELECTFIRST)
		return
	end
	
	-- 判断是否可选中，可以则直接替换，不可则弹reminder
	if self.canClickDict[index] == 1 then
		-- 未收录
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB2_NOTHAVE)
	elseif self.canClickDict[index] == 2 then
		-- 收录但不能换
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB2_SAMEWORD)
	elseif self.canClickDict[index] == 3 then
		-- 可换
		Game.EquipmentSystem.sender:ReqSwitchEquipFixedWord(self.CurrSlotIdx,self.currAttrSel,self.currSpace[index].gid)
	end
end

function EquipAttributeChangePage:SelectSpace()
	table.clear(self.canClickDict)
	local equipInfo = Game.EquipmentSystem:GetEquipBarSlot(self.CurrSlotIdx)
	local fixedProp = equipInfo.equipmentPropInfo.fixedPropInfo.fixedProps
	for idx, info in ipairs(self.currSpace) do
		local widget = self.WBP_ComListCom:GetItemByIndex(idx)
		if not info.pid or info.pid == 0 then
			-- 未收录
			widget:SetStyle(0)
			widget:SetCanHover(false)
			self.canClickDict[idx] = 1
		else
			local canClick = false
			local selData = Game.TableData.GetEquipmentWordFixedWordDataRow(fixedProp[self.currAttrSel])
			if selData.Group == info.gid then
				local fixedWordData = Game.TableData.GetEquipmentWordFixedWordDataRow(fixedProp[self.currAttrSel])
				local groupID = fixedWordData.Group
				local minMax = Game.TableData.Get_EquipmentFixedWordGroupSetToMinMaxWordID()[groupID..";"..fixedWordData.Set[1]]
				if fixedProp[self.currAttrSel] == minMax.maxWordId then
					canClick = false
				else
					canClick = info.pid > fixedProp[self.currAttrSel]
				end
			else
				local hasSame = false
				for i = 1,#fixedProp do
					local fData = Game.TableData.GetEquipmentWordFixedWordDataRow(fixedProp[i])
					if fData.Group == info.gid then
						hasSame = true
					end
				end
				canClick = not hasSame
			end
			if canClick then
				widget:SetStyle(1)
				widget:SetCanHover(true)
				self.canClickDict[idx] = 3
			else
				widget:SetStyle(0)
				widget:SetCanHover(false)
				self.canClickDict[idx] = 2
			end
		end
		
	end
end

function EquipAttributeChangePage:OnSwitchFixed()
	self:RefreshUI(true)
end


--- 此处为自动生成
---@param index number
---@param data table
function EquipAttributeChangePage:on_WBP_ComListCom_ItemClicked(index, data)
	-- 不处在选中属性状态时
	if not self.currAttrSel then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB2_SELECTFIRST)
		return
	end

	-- 判断是否可选中，可以则直接替换，不可则弹reminder
	if self.canClickDict[index] == 1 then
		-- 未收录
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB2_NOTHAVE)
	elseif self.canClickDict[index] == 2 then
		-- 收录但不能换
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB2_SAMEWORD)
	elseif self.canClickDict[index] == 3 then
		-- 可换
		Game.EquipmentSystem.sender:ReqSwitchEquipFixedWord(self.CurrSlotIdx,self.currAttrSel,self.currSpace[index].gid)
	end
end

return EquipAttributeChangePage
