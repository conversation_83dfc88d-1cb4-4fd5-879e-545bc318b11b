local EquipEntryBar_Item = kg_require("Gameplay.LogicSystem.Equip.Entry.EquipEntryBar_Item")
local EquipStarHBox_Sub = kg_require("Gameplay.LogicSystem.Equip.EquipStarHBox_Sub")
local EquipScore_Sub = kg_require("Gameplay.LogicSystem.Equip.EquipScore_Sub")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class EquipEntryList_Sub : UIComponent
---@field view EquipEntryList_SubBlueprint
local EquipEntryList_Sub = DefineClass("EquipEntryList_Sub", UIComponent)

local ESlateVisibility = import("ESlateVisibility")

EquipEntryList_Sub.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EquipEntryList_Sub:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EquipEntryList_Sub:InitUIData()
	self.propList = {} -- propItem集合
	self.PROP_MAX = 4 -- 最大词条数量
end

--- UI组件初始化，此处为自动生成
function EquipEntryList_Sub:InitUIComponent()
    ---@type EquipEntryBar_Item
    self.WBP_EquipEntrySmall_Item4Com = self:CreateComponent(self.view.WBP_EquipEntrySmall_Item4, EquipEntryBar_Item)
    ---@type EquipEntryBar_Item
    self.WBP_EquipEntrySmall_Item3Com = self:CreateComponent(self.view.WBP_EquipEntrySmall_Item3, EquipEntryBar_Item)
    ---@type EquipEntryBar_Item
    self.WBP_EquipEntrySmall_Item2Com = self:CreateComponent(self.view.WBP_EquipEntrySmall_Item2, EquipEntryBar_Item)
    ---@type EquipEntryBar_Item
    self.WBP_EquipEntrySmall_Item1Com = self:CreateComponent(self.view.WBP_EquipEntrySmall_Item1, EquipEntryBar_Item)
    ---@type EquipStarHBox_Sub
    self.WBP_EquipStarHBox_SubCom = self:CreateComponent(self.view.WBP_EquipStarHBox_Sub, EquipStarHBox_Sub)
    ---@type EquipScore_Sub
    self.WBP_EquipScore_SubCom = self:CreateComponent(self.view.WBP_EquipScore_Sub, EquipScore_Sub)

	self.propList = {self.WBP_EquipEntrySmall_Item1Com,self.WBP_EquipEntrySmall_Item2Com,
					 self.WBP_EquipEntrySmall_Item3Com,self.WBP_EquipEntrySmall_Item4Com}
end

---UI事件在这里注册，此处为自动生成
function EquipEntryList_Sub:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EquipEntryList_Sub:InitUIView()
	self.view.WBP_EquipStarHBox_Sub:SetVisibility(ESlateVisibility.Collapsed) -- 等套装设计
	
	self.view.WBP_EquipTagText_Item:Event_UI_State(0)
end

---组件刷新统一入口
function EquipEntryList_Sub:Refresh(...)
	
end

function EquipEntryList_Sub:SetRandomAttr(randAttr, formulaIdx, equipID, oldAttr)
	for i = 1, self.PROP_MAX do
		local comp = self.propList[i]
		if randAttr[i] then
			comp:SetValid(true)
			-- 涂抹标签\State
			local wordID = randAttr[i]
			local state = 1
			if Game.EquipmentSystem:IsRareRandomWord(wordID) then
				state = 3
			end
			local isSmear = i == formulaIdx
			if isSmear then
				state = 0
			end

			-- 进度条对比
			local compareWordID
			if oldAttr and not isSmear then
				local wordData = Game.TableData.GetEquipmentWordRandomWordDataRow(wordID)
				local groupID = wordData.Groups[1]
				for k = 1, #oldAttr do
					if k ~= formulaIdx then
						local wId = oldAttr[k]
						local wData = Game.TableData.GetEquipmentWordRandomWordDataRow(wId)
						if wData.Groups[1] == groupID then
							compareWordID = wId
							break
						end
					end
				end
			end
			
			comp:SetState(state, isSmear)
			comp:SetAttr(wordID)
			comp:SetProgressBar(equipID,wordID,compareWordID)
		else
			comp:SetValid(false)
		end
	end
end

function EquipEntryList_Sub:SetState(isNew, isStar)
	if isNew then
		self.view.WBP_EquipTagText_Item:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.view.WBP_EquipTagText_Item:SetVisibility(ESlateVisibility.Collapsed)
	end
	
	-- 等套装设计
	self.view.WBP_EquipStarHBox_Sub:SetVisibility(ESlateVisibility.Collapsed)
end
function EquipEntryList_Sub:SetScore(score, arrowState)
	self.view.WBP_EquipScore_Sub:Event_UI_State(arrowState or 0)
	self.view.WBP_EquipScore_Sub.Text_Num:SetText(score or "0")
end

return EquipEntryList_Sub
