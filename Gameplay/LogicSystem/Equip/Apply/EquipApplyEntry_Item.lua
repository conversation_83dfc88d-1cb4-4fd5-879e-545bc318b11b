local EquipEntryBar_Item = kg_require("Gameplay.LogicSystem.Equip.Entry.EquipEntryBar_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class EquipApplyEntry_Item : UIComponent
---@field view EquipApplyEntry_ItemBlueprint
local EquipApplyEntry_Item = DefineClass("EquipApplyEntry_Item", UIComponent)

local EUMGSequencePlayMode = import("EUMGSequencePlayMode")

local ESlateVisibility = import("ESlateVisibility")

EquipApplyEntry_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EquipApplyEntry_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EquipApplyEntry_Item:InitUIData()
	---按钮点击事件回调
	---@type LuaMulticastDelegate<fun()>
	self.onClick = LuaMulticastDelegate.new()

	self.state = nil -- 状态
	self.idx = nil --idx
end

--- UI组件初始化，此处为自动生成
function EquipApplyEntry_Item:InitUIComponent()
    ---@type EquipEntryBar_Item
    self.WBP_EquipEntrySmall_ItemCom = self:CreateComponent(self.view.WBP_EquipEntrySmall_Item, EquipEntryBar_Item)
end

---UI事件在这里注册，此处为自动生成
function EquipApplyEntry_Item:InitUIEvent()
	self:AddUIEvent(self.view.Btn_Click.OnClicked, "OnBtnClick")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EquipApplyEntry_Item:InitUIView()
end

---面板打开的时候触发
function EquipApplyEntry_Item:OnRefresh(...)
end

function EquipApplyEntry_Item:OnBtnClick()
	self.onClick:Broadcast(self.idx)
end

function EquipApplyEntry_Item:SetIdx(idx)
	self.idx = idx
end
function EquipApplyEntry_Item:SetValid(isValid)
	if isValid then
		self.userWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self.userWidget:SetVisibility(ESlateVisibility.Collapsed)
	end
end
function EquipApplyEntry_Item:SetAttr(equipID, wordID, isSmear, cannotOp)
	local state = 1
	if Game.EquipmentSystem:IsRareRandomWord(wordID) then
		state = 3
	end
	if cannotOp then
		state = 0
	end
	self.WBP_EquipEntrySmall_ItemCom:SetState(state, false)
	self.WBP_EquipEntrySmall_ItemCom:SetAttr(wordID)
	self.WBP_EquipEntrySmall_ItemCom:SetProgressBar(equipID, wordID)
end
function EquipApplyEntry_Item:SetState(state)
	if type(state) == "number" then
		self.userWidget:Event_UI_State(state)
		self.state = state
		if state == 2 then
			self:PlayAnimation(self.view.Ani_smear)
		else
			self:StopAnimation(self.view.Ani_smear)
		end
	end
end
function EquipApplyEntry_Item:GetState()
	return self.state
end

function EquipApplyEntry_Item:SetSelect(isSelect,needVx)
	if isSelect then
		self.userWidget:Event_UI_State(1)
	elseif self.state then
		self.userWidget:Event_UI_State(self.state)
	else
		self.userWidget:Event_UI_State(0)
	end

	if needVx then
		if isSelect then
			self:StopAnimation(self.view.Ani_Unselect)
			self.view.Canv_select:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self:PlayAnimation(self.view.Ani_select)
		else
			self:StopAnimation(self.view.Ani_select)
			self.view.Canv_select:SetVisibility(ESlateVisibility.Collapsed)
			self:PlayAnimation(self.view.Ani_Unselect)
		end
	else
		if isSelect then
			self.view.Canv_select:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		else
			self.view.Canv_select:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
end

function EquipApplyEntry_Item:PlaySmearVX()
	self:PlayAnimation(self.view.Ani_shuaxin)
end

return EquipApplyEntry_Item
