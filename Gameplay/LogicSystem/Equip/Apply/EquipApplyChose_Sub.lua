local UIComButtonItem = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButtonItem")
local ItemSimple_Item = kg_require("Gameplay.LogicSystem.Item.ItemSimple_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class EquipApplyChose_Sub : UIComponent
---@field view EquipApplyChose_SubBlueprint
local EquipApplyChose_Sub = DefineClass("EquipApplyChose_Sub", UIComponent)

local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

EquipApplyChose_Sub.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EquipApplyChose_Sub:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EquipApplyChose_Sub:InitUIData()
	---@type LuaMulticastDelegate<fun()>
	self.onClick = LuaMulticastDelegate.new()
	
	self.itemIDCache = nil
end

--- UI组件初始化，此处为自动生成
function EquipApplyChose_Sub:InitUIComponent()
    ---@type UIComButtonItem
    self.WBP_ComBtnIconNewCom = self:CreateComponent(self.view.WBP_ComBtnIconNew, UIComButtonItem)
    ---@type ItemSimple_Item
    self.WBP_ItemSimple_ItemCom = self:CreateComponent(self.view.WBP_ItemSimple_Item, ItemSimple_Item)
end

---UI事件在这里注册，此处为自动生成
function EquipApplyChose_Sub:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnIconNewCom.onClickEvent, "on_WBP_ComBtnIconNewCom_ClickEvent")
    self:AddUIEvent(self.WBP_ItemSimple_ItemCom.onClick, "on_WBP_ItemSimple_ItemCom_Click")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EquipApplyChose_Sub:InitUIView()
	self.WBP_ItemSimple_ItemCom:SetSingle()
	self.view.Text_Title:SetText(StringConst.Get("EQUIP_TAB4_SELECTITEM"))
	self.view.Text_Tip:SetText(StringConst.Get("EQUIP_TAB4_TIP2"))
	self.view.Text_Name:SetText(StringConst.Get("EQUIP_TAB4_ITEMWORD"))
end

---组件刷新统一入口
---@param pasteID number -- 当前圣膏gid 
function EquipApplyChose_Sub:Refresh(...)
	local pasteID = select(1,...)
	self.WBP_ItemSimple_ItemCom:SetSelect(false)
	if pasteID then
		self.view.WBP_ComBtnIconNew:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.RichText_Num:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		local pasteInfo = Game.BagSystem:GetItemInfoWithGbId(pasteID)
		if not pasteInfo and self.itemIDCache then
			-- 用尽状态
			self.WBP_ItemSimple_ItemCom:Refresh({self.itemIDCache})
			local pasteData = Game.TableData.GetEquipmentGrowRandomItemDataRow(self.itemIDCache)
			local total = pasteData.canReuseTimes or 0
            self.view.RichText_Num:SetText(string.format(StringConst.Get("EQUIP_TAB4S_SELECT_TIMES")..": <Quality_6>%d</>/%d", 0,total))
			self.WBP_ItemSimple_ItemCom:SetStatus(Enum.ItemStatus.UseLimit)
		else
			self.itemIDCache = pasteInfo.itemId
			self.WBP_ItemSimple_ItemCom:Refresh({pasteInfo.itemId})
			local pasteData = Game.TableData.GetEquipmentGrowRandomItemDataRow(pasteInfo.itemId)
			local total = pasteData.canReuseTimes or 0
			local rem = total - (pasteInfo.useTimes or 0)
            self.view.RichText_Num:SetText(string.format(StringConst.Get("EQUIP_TAB4S_SELECT_TIMES")..": <Quality_2>%d</>/%d", rem,total))
            self.WBP_ItemSimple_ItemCom:SetStatus(Enum.ItemStatus.Normal)
		end
	else
		self.itemIDCache = nil
		self.WBP_ItemSimple_ItemCom:Refresh({}) -- luacheck: ignore
		self.WBP_ItemSimple_ItemCom:SetStatus(Enum.ItemStatus.Normal)
		self.view.WBP_ComBtnIconNew:SetVisibility(ESlateVisibility.Collapsed)
		self.view.RichText_Num:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function EquipApplyChose_Sub:SetSelectNum(num)
	--self.view.Text_Tip:SetText(string.format("select word（%i/1）：",num or 0))
end


function EquipApplyChose_Sub:OnClickItem()
	self.onClick:Broadcast()
end


--- 此处为自动生成
function EquipApplyChose_Sub:on_WBP_ComBtnIconNewCom_ClickEvent()
	self:OnClickItem()
end

--- 此处为自动生成
function EquipApplyChose_Sub:on_WBP_ItemSimple_ItemCom_Click()
	self:OnClickItem()
end

return EquipApplyChose_Sub
