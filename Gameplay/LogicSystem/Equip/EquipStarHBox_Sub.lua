local EquipStar_Item = kg_require("Gameplay.LogicSystem.Equip.EquipStar_Item")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class EquipStarHBox_Sub : UIComponent
---@field view EquipStarHBox_SubBlueprint
local EquipStarHBox_Sub = DefineClass("EquipStarHBox_Sub", UIComponent)

EquipStarHBox_Sub.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function EquipStarHBox_Sub:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function EquipStarHBox_Sub:InitUIData()
end

--- UI组件初始化，此处为自动生成
function EquipStarHBox_Sub:InitUIComponent()
    ---@type EquipStar_Item
    self.WBP_EquipStar_ItemCom = self:CreateComponent(self.view.WBP_EquipStar_Item, EquipStar_Item)
    ---@type EquipStar_Item
    self.WBP_EquipStar_Item_1Com = self:CreateComponent(self.view.WBP_EquipStar_Item_1, EquipStar_Item)
    ---@type EquipStar_Item
    self.WBP_EquipStar_Item_2Com = self:CreateComponent(self.view.WBP_EquipStar_Item_2, EquipStar_Item)
    ---@type EquipStar_Item
    self.WBP_EquipStar_Item_3Com = self:CreateComponent(self.view.WBP_EquipStar_Item_3, EquipStar_Item)
end

---UI事件在这里注册，此处为自动生成
function EquipStarHBox_Sub:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function EquipStarHBox_Sub:InitUIView()
end

---组件刷新统一入口
function EquipStarHBox_Sub:Refresh(...)
end

return EquipStarHBox_Sub
