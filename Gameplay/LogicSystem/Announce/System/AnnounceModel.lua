
---@class AnnounceModel
local AnnounceModel = DefineClass("AnnounceModel", SystemModelBase)

AnnounceModel.SAVE_GAME_SLOT = "PostSaveGame"
AnnounceModel.BP_PostSaveGame = UIAssetPath.BP_PostSaveGame

function AnnounceModel:init()
	---@type table 保存游戏对象
	self.saveObj = nil

	self:initSaveData()
end

function AnnounceModel:onPostCreateData()
	self.saveObj.PostInfo.Map = {}
end

function AnnounceModel:initSaveData()
	local saveGameSlot = AnnounceModel.SAVE_GAME_SLOT
	local saveGameClass = AnnounceModel.BP_PostSaveGame
	if import("GameplayStatics").DoesSaveGameExist(saveGameSlot,0) then
		self.saveObj = import("GameplayStatics").LoadGameFromSlot(saveGameSlot,0)
	end
	if not self.saveObj then
		self.saveObj = import("GameplayStatics").CreateSaveGameObject(slua.loadClass(saveGameClass))
		self:onPostCreateData()
	end
end

---@public
---SaveGameData 保存游戏数据。
function AnnounceModel:SaveGameData()
	import("GameplayStatics").SaveGameToSlot(self.saveObj, AnnounceModel.SAVE_GAME_SLOT,0)
end

function AnnounceModel:clear()
	
end

function AnnounceModel:unInit()
	self.saveObj = nil
end

---@public
---EnsureSaveData 确保保存数据有效。
function AnnounceModel:EnsureSaveData()
	if not IsValid_L(self.saveObj) then
		self:Init()
	end
end

---@public
---GetSaveMapInfo 获取保存的数据map
function AnnounceModel:GetSaveMapInfo()
	return self.saveObj.PostInfo.Map
end

---@public
---ClearPostSavedData 清空存储的公告信息。
function AnnounceModel:ClearPostSavedData()
	if IsValid_L(self.saveObj) then
		self.saveObj.PostInfo.Map:Clear()
		self:SaveGameData()
	end
end

return AnnounceModel

