local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"
local ETextJustify = import("ETextJustify")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")

---@class AnnouncePanel : UIPanel
---@field view AnnouncePanelBlueprint
local AnnouncePanel = DefineClass("AnnouncePanel", UIPanel)

AnnouncePanel.eventBindMap = {
}

---@class AnnounceTextData
---@field content string 公告文本内容
---@field justification ETextJustify 对齐方式

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function AnnouncePanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function AnnouncePanel:InitUIData()
	---@type NoticeEntry[] 公告列表
    self.announceInfoList = {} 
	---@type integer 第一次打开选中的页签
    self.firstSel = -1
	---@type integer 上一次选中的
    self.preSelect = -1 
	---@type AnnounceTextData[] 公告正文数据
    self.mainTextListData = {}      -- 正文数据
end

--- UI组件初始化，此处为自动生成
function AnnouncePanel:InitUIComponent()
    ---@type UIListView
    self.List_AnnounceContentCom = self:CreateComponent(self.view.List_AnnounceContent, UIListView)
    ---@type UIListView
    self.List_AnnounceTabCom = self:CreateComponent(self.view.List_AnnounceTab, UIListView)
end

---UI事件在这里注册，此处为自动生成
function AnnouncePanel:InitUIEvent()
    self:AddUIEvent(self.view.Button_Close.OnClicked, "on_Button_Close_Clicked")
    self:AddUIEvent(self.List_AnnounceTabCom.onItemSelectionChanged, "on_List_AnnounceTabCom_ItemSelectionChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function AnnouncePanel:InitUIView()
end

---面板打开的时候触发
---@param params NoticeEntry[]
---@param index number
function AnnouncePanel:OnRefresh(params, index)
    if index == nil or (index and index < 1) then
        index = 1
    end
	
    self.firstSel = index
    self.preSelect = -1
	
	local isNotEmpty = params and #params > 0
	local emptyVisibility = isNotEmpty and ESlateVisibility.Hidden or ESlateVisibility.Visible
	self.view.Text_Empty:SetVisibility(emptyVisibility)
	
	self.List_AnnounceContentCom:SetVisible(isNotEmpty)
	self.List_AnnounceTabCom:SetVisible(isNotEmpty)
	
    if isNotEmpty then
        self.announceInfoList = params
        self:SetDataFunction()
    else
        self.view.Text_Empty:SetText(StringConst.Get("ANNOUNCEMENT_CONTENT_LOADING"))
    end

	self.view.Text_Title:SetText(StringConst.Get("ANNOUNCEMENT_SELF"))
end

function AnnouncePanel:OnDestroy()
    table.clear(self.announceInfoList)
end

---@private
---设置公告列表数据以及默认选中项
function AnnouncePanel:SetDataFunction()
	self.List_AnnounceTabCom:Refresh(self.announceInfoList)
    if self.firstSel >= 1 and self.firstSel <= #self.announceInfoList then
        self.preSelect = self.firstSel
        self:SelectTab(self.firstSel)
    else
        self.firstSel = 1
        self.preSelect = 1
        self:SelectTab(1) --打开公告默认选中第一个标签
    end
    self:RefreshAnnounceInfo(self.firstSel)
end

function AnnouncePanel:RefreshAnnounceInfo(index)
    local data = self.announceInfoList[index]
	local mainTitle, mainTextList = self:RefreshMainText(index)
	
	self.mainTextListData = mainTextList
    if data then
        self.view.Text_Title:SetText(mainTitle)
        self:RefreshImage(index)
    end
    self.List_AnnounceContentCom:Refresh(self.mainTextListData)
end

function AnnouncePanel:SelectTab(index)
	self.List_AnnounceTabCom:SetSelectedItemByIndex(index, true)
end

---@private
---刷新头图
function AnnouncePanel:RefreshImage(index)
    local data = self.announceInfoList[index]
    data.texture = nil
    if not data or not data.icon or data.icon == "" then
        return
    end
    self:SetImageByUrl(self.view.PostImage, { data.icon }, "announcePoster" .. tostring(index), UIConst.UIWebImageModule.Announce)
end

---@private
---刷新公告文本内容
---@return (string,string[]) 公告标题，公告内容数组
function AnnouncePanel:RefreshMainText(index)
    local data = self.announceInfoList[index]
    if data and data.text then
        local titles = string.split(data.title,";")
		local mainTitle = #titles > 1 and titles[2] or data.title
        return mainTitle, self:GetProcessedTextJustification(data.text, index)
    else
        return "", self:GetProcessedTextJustification(StringConst.Get("ANNOUNCEMENT_CONTENT_LOADING"), index)
    end
end

function AnnouncePanel:FindTextMatchPatternStEd(text)
    local st1, ed1 = string.find(text, "<AlignRight>(.-)<//>")
    local st2, ed2 = string.find(text, "<AlignCenter>(.-)<//>")
    local st, ed
    local bRight = false
    if st1 and st2 then
        if st1 < st2 then
            st = st1
            ed = ed1
            bRight = true
        else
            st = st2
            ed = ed2
        end
    elseif st1 then
        st = st1
        ed = ed1
        bRight = true
    else
        st = st2
        ed = ed2
    end
    return st, ed, bRight
end

function AnnouncePanel:GetProcessedTextJustification(text)
    local st, ed, bRight = self:FindTextMatchPatternStEd(text)
    local l = 1
    local textList = {}
    while st do
        local s = string.sub(text, l, st - 1)
        if string.len(s) > 0 then
            table.insert(textList, {content = s, justification = ETextJustify.Left})
        end
        if bRight then
            s = string.match(text, "<AlignRight>(.-)<//>")
            table.insert(textList, {content = s, justification = ETextJustify.Right})
        else
            s = string.match(text,"<AlignCenter>(.-)<//>")
            table.insert(textList, {content = s, justification = ETextJustify.Center})
        end
        text = string.sub(text, ed + 1)
        st, ed, bRight = self:FindTextMatchPatternStEd(text)
    end
    if string.len(text) > 0 then
        table.insert(textList, {content = text, justification = ETextJustify.Right})
    end
    return textList
end

--- 此处为自动生成
function AnnouncePanel:on_Button_Close_Clicked()
	self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param selected bool
function AnnouncePanel:on_List_AnnounceTabCom_ItemSelectionChanged(index, selected)
	if selected then
		if self.preSelect ~= index then
			self.preSelect = index
			self:RefreshAnnounceInfo(index)
		end
	end
end

return AnnouncePanel
