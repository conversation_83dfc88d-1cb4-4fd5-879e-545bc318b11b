local UIComSwitchBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComSwitchBtn")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

local ESlateVisibility = import("ESlateVisibility")
local ESelectionMode = import("ESelectionMode")

---@begin define

-- 分解信息数据结构
---@class BagAutoDecomposeSetting_ItemParam
---@field public ConfigId number 配置id
---@field public InvId number 背包ID
---@field public Index boolean 索引

---@end define

---@class BagAutoDecomposeSetting_Item : UIListItem
---@field view BagAutoDecomposeSetting_ItemBlueprint
local BagAutoDecomposeSetting_Item = DefineClass("BagAutoDecomposeSetting_Item", UIListItem)

BagAutoDecomposeSetting_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BagAutoDecomposeSetting_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BagAutoDecomposeSetting_Item:InitUIData()
	---@type BagCheckBox_Item[] 品质列表参数
	self.QualityCheckListData = {}
	---@type boolean[] 是否选中列表
	self.qualitySelectedList = {}
	---@type boolean 是否打开
	self.bOpen = false
	---@type boolean 是否配置首次刷新
	self.bConfigFirstRefresh = true
	---@type number 记录哪个是全部选中索引
	self.AllIndex = -1
end

--- UI组件初始化，此处为自动生成
function BagAutoDecomposeSetting_Item:InitUIComponent()
    ---@type UIComSwitchBtn
    self.SwitchBtnPanelCom = self:CreateComponent(self.view.SwitchBtnPanel, UIComSwitchBtn)
    ---@type UIListView
    self.QualityListCom = self:CreateComponent(self.view.QualityList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function BagAutoDecomposeSetting_Item:InitUIEvent()
    self:AddUIEvent(self.QualityListCom.onItemSelectionChanged, "on_QualityListCom_ItemSelectionChanged")
    self:AddUIEvent(self.SwitchBtnPanelCom.onClickEvent, "on_SwitchBtnPanelCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BagAutoDecomposeSetting_Item:InitUIView()
end

---面板打开的时候触发
---@param param BagAutoDecomposeSetting_ItemParam 配置数据
function BagAutoDecomposeSetting_Item:OnRefresh(param)
	local index = param.Index
	local invId = param.InvId
	if index%2 == 0 then
		self.view.Bg_Img:SetVisibility(ESlateVisibility.Hidden)
	else
		self.view.Bg_Img:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end
	local configData = Game.TableData.GetAutoDecomposeDataRow(param.ConfigId)
	if configData then
		self.bConfigFirstRefresh = true
		if Game.me.decomposeConfigs and Game.me.decomposeConfigs[invId] and Game.me.decomposeConfigs[invId].decomposeInfoMap then
			self.autoDecomposeItemConfig = Game.me.decomposeConfigs[invId].decomposeInfoMap[index]
		end
		self.view.KText_Score:SetText(configData.RuleDes)

		self.AllIndex = -1
		self:BuildQuilitySelectedList(configData)
		self.QualityListCom:SetSelectionMode(ESelectionMode.Multi)
		self.QualityListCom:Refresh(self.QualityCheckListData)
		for idx, paramData in ipairs(self.QualityCheckListData) do
			if paramData.Checked then
				self.QualityListCom:SetSelectedItemByIndex(idx, true)
			end
		end
		
		if #self.qualityInfoList == 0 then
			self.bOpen = self.autoDecomposeItemConfig and self.autoDecomposeItemConfig.bOpen or false
			self.SwitchBtnPanelCom:Show(ESlateVisibility.SelfHitTestInvisible)
			self.SwitchBtnPanelCom:Refresh(self.bOpen, false)
		else
			self.SwitchBtnPanelCom:Hide(ESlateVisibility.Collapsed)
		end
	end
end

function BagAutoDecomposeSetting_Item:BuildQuilitySelectedList(configData)
	table.clear(self.qualitySelectedList)
	table.clear(self.QualityCheckListData)
	
	self.qualityInfoList = configData.itemRareFilterType or {}

	for index, qualityId in ipairs(self.qualityInfoList) do
		local bChecked = false
		if self.bConfigFirstRefresh then
			if self.autoDecomposeItemConfig and self.autoDecomposeItemConfig.autoDecomposeQuality then
				bChecked = self.autoDecomposeItemConfig.autoDecomposeQuality[qualityId] or false
			else
				bChecked = self.qualitySelectedList[qualityId] or false
			end
			if index == #self.qualityInfoList then
				self.bConfigFirstRefresh = false
			end
		else
			bChecked = not self.qualitySelectedList[qualityId]
		end
		self.qualitySelectedList[qualityId] = bChecked
		local qualityData = Game.TableData.GetRarityDataRow(qualityId)
		local qualityName = qualityData and qualityData.ColorName or ""
		
		---@type BagCheckBox_Item
		local param = {
			QualityName = qualityName,
			QualityId = qualityId,
			Checked = bChecked,
		}
		table.insert(self.QualityCheckListData, param)
		if qualityId == 0 then
			self.AllIndex = #self.QualityCheckListData
		end
	end
end

--- 获取玩家修改的配置项数据
function BagAutoDecomposeSetting_Item:GetEditConfig()
	local result = {}
	---开关项
	if #self.qualityInfoList == 0 then
		result = {bOpen = self.bOpen}
	else
		result = {autoDecomposeQuality = self.qualitySelectedList}
	end
	return result
end

function BagAutoDecomposeSetting_Item:OnClose()
	table.clear(self.qualitySelectedList)
end

--- 此处为自动生成
---@param index number
---@param selected bool
function BagAutoDecomposeSetting_Item:on_QualityListCom_ItemSelectionChanged(index, selected)
	local checkData = self.QualityCheckListData[index]
	local qualityId = checkData.QualityId
	local oldSelected = self.qualitySelectedList[qualityId]
	if oldSelected == selected then return end

	self.qualitySelectedList[qualityId] = selected
	
	if qualityId == 0 then -- 如果是全选标签
		for idx, data in ipairs(self.QualityCheckListData) do
			self.qualitySelectedList[data.QualityId] = selected
			self.QualityListCom:SetSelectedItemByIndex(idx, selected)
		end
	else
		if self.AllIndex <= 0 then return end
		
		-- 如果存在全选，那么也需要重置全选状态
		--[[local isAllSelected = true
		for idx, data in ipairs(self.QualityCheckListData) do
			local isSel = self.qualitySelectedList[data.QualityId]
			if idx ~= self.AllIndex and not isSel then
				isAllSelected = false
				break
			end
		end

		if isAllSelected ~= self.qualitySelectedList[0] then
			self.qualitySelectedList[0] = isAllSelected
			self.QualityListCom:SetSelectedItemByIndex(self.AllIndex, selected)
		end]]
	end
end


--- 此处为自动生成
---@param isOn bool
function BagAutoDecomposeSetting_Item:on_SwitchBtnPanelCom_ClickEvent(isOn)
	self.bOpen = isOn
end

return BagAutoDecomposeSetting_Item
