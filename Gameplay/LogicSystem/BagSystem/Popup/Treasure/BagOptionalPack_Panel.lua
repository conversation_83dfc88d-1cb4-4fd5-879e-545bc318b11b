local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComBoxFrame = kg_require("Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = require "Data.Config.StringConst.StringConst"

---@class OptionalPack_Params
---@field ItemId number 物品ID
---@field gbId number 全局ID
---@field Slot number 槽位
---@field InvId number 背包ID
---@field Count number 数量

---@class BagOptionalPack_Panel : UIPanel
---@field view BagOptionalPack_PanelBlueprint
local BagOptionalPack_Panel = DefineClass("BagOptionalPack_Panel", UIPanel)

BagOptionalPack_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BagOptionalPack_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BagOptionalPack_Panel:InitUIData()
	---@type OptionalPack_Params 界面参数
	self.Param = nil
	---@type BagOptionalPack_ItemParam[] 道具列表参数
	self.PickItemParamList = {}
	---@type number 当前选中的数量
	self.CurSelectedPackNum = 0
	---@type number 可选择的数量上限
	self.Count = 0
end

--- UI组件初始化，此处为自动生成
function BagOptionalPack_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnHightLightCom = self:CreateComponent(self.view.WBP_ComBtnHightLight, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtnNormCom = self:CreateComponent(self.view.WBP_ComBtnNorm, UIComButton)
    ---@type UIComBoxFrame
    self.WBP_ComPopupFrameBigNavCom = self:CreateComponent(self.view.WBP_ComPopupFrameBigNav, UIComBoxFrame)
    ---@type UIListView
    self.PickListCom = self:CreateComponent(self.view.PickList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function BagOptionalPack_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnNormCom.onClickEvent, "on_WBP_ComBtnNormCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnHightLightCom.onClickEvent, "on_WBP_ComBtnHightLightCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BagOptionalPack_Panel:InitUIView()
	self.WBP_ComBtnNormCom:Refresh(StringConst.Get("BAG_CANCLE"))
	self.WBP_ComBtnHightLightCom:Refresh(StringConst.Get("BAG_CONFIRM_OBTAIN"))
	self.WBP_ComPopupFrameBigNavCom:Refresh(StringConst.Get('BAG_PICKPACKTITLE'))
end

---面板打开的时候触发
---@param param OptionalPack_Params
function BagOptionalPack_Panel:OnRefresh(param)
	self.Param = param
	
	self.Count = param.Count or 0
	self.view.TB_TotalCount:SetText(' / ' .. self.Count)
	
	self.CurSelectedPackNum = 0

	local itemStrFormat = StringConst.Get("ITEM_PICKCHEST_HOLDNUM")
	self.PickItemParamList = Game.BagSystem:GetPickRewardData(param.ItemId, function(itemId, count, bindType) 
		---@type BagOptionalPack_ItemParam
		return {
			ItemId = itemId, Count = count, BindType = bindType, ParentUI = self,
			CountStr = string.format(itemStrFormat, Game.BagSystem:GetItemCount(itemId))
		}  
	end)
	self.PickListCom:Refresh(self.PickItemParamList)
	
	self:UpdateTextInfo()
end


--- 此处为自动生成
function BagOptionalPack_Panel:on_WBP_ComBtnNormCom_ClickEvent()
	self:CloseSelf()
end

--- 此处为自动生成
function BagOptionalPack_Panel:on_WBP_ComBtnHightLightCom_ClickEvent()
	if self.CurSelectedPackNum == 0 then
		return
	end

	if Game.BagSystem:CheckItemUse(self.Param.ItemId) then
		local localAvatar = NetworkManager.GetLocalAvatarActor()
		if localAvatar then
			local serverFormat =  self:Create4ServerUseParam()
			local param = self.Param
			localAvatar:useItemByIndexWithParam(
				param.InvId, param.Slot, param.gbId, self.CurSelectedPackNum,
				{ pickItems = serverFormat }
			)
		end
	end
	self:OnClickCloseBtn()
end

---@private 创建服务器使用参数
---@return table[]
function BagOptionalPack_Panel:Create4ServerUseParam()
	local serverFormat = {}
	for idx, itemParam in ipairs(self.PickItemParamList) do
		---@type BagOptionalPack_Item
		local item = self.PickListCom:GetItemByIndex(idx)
		local num = item:GetSelectedNum()
		
		table.insert(serverFormat, {itemParam.ItemId, num, itemParam.BindType})
	end
	return serverFormat
end

function BagOptionalPack_Panel:OnClickCloseBtn()
	self:CloseSelf()
end

function BagOptionalPack_Panel:EscOnClickEvent()
	self:CloseSelf()
end

---@public 获取剩余可选择的数量
function BagOptionalPack_Panel:GetRemanentPackNum()
	return self.Count - self.CurSelectedPackNum
end

---@public 重新计算背包
function BagOptionalPack_Panel:ReCalcPack()
	local totalNum = 0
	for i = 1, #self.PickItemParamList do
		---@type BagOptionalPack_Item
		local item = self.PickListCom:GetItemByIndex(i)
		local num = item:GetSelectedNum()
		totalNum = totalNum + num
	end
	self.CurSelectedPackNum = totalNum
	self:UpdateTextInfo()
	self:UpdateChildrenInput()
end

---@private
function BagOptionalPack_Panel:UpdateChildrenInput()
	for i = 1, #self.PickItemParamList do
		---@type BagOptionalPack_Item
		local item = self.PickListCom:GetItemByIndex(i)
		item:RefreshInputInfo()
	end
end

---@private
function BagOptionalPack_Panel:UpdateTextInfo()
	self.view.TB_SelectedTotalCount:SetText(self.CurSelectedPackNum)
end

return BagOptionalPack_Panel
