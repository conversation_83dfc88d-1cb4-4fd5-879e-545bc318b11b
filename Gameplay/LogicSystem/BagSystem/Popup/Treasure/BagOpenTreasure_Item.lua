local BagName_Item = kg_require("Gameplay.LogicSystem.BagSystem.Common.BagName_Item")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---@class BagOpenTreasure_ItemParam : BagNameItemParam
---@field public SpecName string 名称

---@class BagOpenTreasure_Item : UIListItem
---@field view BagOpenTreasure_ItemBlueprint
local BagOpenTreasure_Item = DefineClass("BagOpenTreasure_Item", UIListItem)

BagOpenTreasure_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BagOpenTreasure_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BagOpenTreasure_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function BagOpenTreasure_Item:InitUIComponent()
    ---@type BagName_Item
    self.BagNameItemCom = self:CreateComponent(self.view.BagNameItem, BagName_Item)
end

---UI事件在这里注册，此处为自动生成
function BagOpenTreasure_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BagOpenTreasure_Item:InitUIView()
end

---面板打开的时候触发
---@param param BagOpenTreasure_ItemParam
function BagOpenTreasure_Item:OnRefresh(param)
	self.BagNameItemCom:Refresh(param)
	self.view.Text_Num:SetText(param.SpecName)
end

return BagOpenTreasure_Item
