local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local ItemRewardNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemRewardNew")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---@class BagOptionalPack_ItemParam
---@param ItemId number @物品ID
---@param Count number @数量
---@param CountStr string @数量文本
---@param BindType number @绑定类型
---@param ParentUI BagOptionalPack_Panel @索引

---@class BagOptionalPack_Item : UIListItem
---@field view BagOptionalPack_ItemBlueprint
local BagOptionalPack_Item = DefineClass("BagOptionalPack_Item", UIListItem)

BagOptionalPack_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BagOptionalPack_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BagOptionalPack_Item:InitUIData()
	---@type BagOptionalPack_ItemParam
	self.Param = nil
	---@type number 当前选中的数量
	self.CurCount = 0
end

--- UI组件初始化，此处为自动生成
function BagOptionalPack_Item:InitUIComponent()
    ---@type UIComNumberSlider
    self.WBP_ComInputAddSubtractCom = self:CreateComponent(self.view.WBP_ComInputAddSubtract, UIComNumberSlider)
    ---@type ItemRewardNew
    self.WBP_ComItemNormCom = self:CreateComponent(self.view.WBP_ComItemNorm, ItemRewardNew)
end

---UI事件在这里注册，此处为自动生成
function BagOptionalPack_Item:InitUIEvent()
    self:AddUIEvent(self.WBP_ComInputAddSubtractCom.onValueChange, "on_WBP_ComInputAddSubtractCom_ValueChange")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BagOptionalPack_Item:InitUIView()
end

---面板打开的时候触发
---@param param BagOptionalPack_ItemParam
function BagOptionalPack_Item:OnRefresh(param)
	self.Param = param
	self:UpdateItemInfo()
	
	self.CurCount = 0
	self:RefreshInputInfo()
end

---@private 更新道具信息
function BagOptionalPack_Item:UpdateItemInfo()
	local param = self.Param
	
	local itemExcelData = Game.TableData.GetItemNewDataRow(param.ItemId)
	self.view.KText_Name:SetText(itemExcelData and itemExcelData.itemName or "")
	self.view.KText_Num:SetText(param.CountStr)
	
	self.WBP_ComItemNormCom:FillItem(param.ItemId, true, false, 0, param.BindType)
end

---@public
function BagOptionalPack_Item:RefreshInputInfo()
	local remanentPackNum = self.Param.ParentUI:GetRemanentPackNum()
	self.WBP_ComInputAddSubtractCom:Refresh(self.CurCount, 0, self.CurCount + remanentPackNum, 1)
end

---@public 获取当前选中的数量
---@return number
function BagOptionalPack_Item:GetSelectedNum()
	return self.CurCount
end

--- 此处为自动生成
---@param value number
function BagOptionalPack_Item:on_WBP_ComInputAddSubtractCom_ValueChange(value)
	self.CurCount = value
	self.Param.ParentUI:ReCalcPack()
end

return BagOptionalPack_Item
