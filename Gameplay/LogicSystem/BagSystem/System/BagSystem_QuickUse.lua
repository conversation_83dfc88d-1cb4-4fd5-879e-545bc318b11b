---@type BagSystem
local BagSystem = BagSystem -- luacheck: ignore

local ItemConstSource = kg_require("Shared.ItemConstSource")
local LQueue = require "Framework.Library.LQueue"
-------------------------------------------------------------------------快捷使用-----------------------------------------------------------------
---- 主HUD界面上的快捷使用浮窗需求
function BagSystem:InitQuickUseQueue()
	--- 快捷使用浮窗的待显示队列
	self.QuickUseItemQueue = LQueue.new()
	self.ItemSelected = nil ---存储当前正在展示的物品的gbId
	self.ItemInfoMap = {}   ---道具等待队列
end

--- 快捷使用浮窗的黑名单
BagSystem.BLACKLIST =
{
	[ItemConstSource.ITEM_SOURCE_TAKE_FROM_WARE_HOUSE] = 1, --- 从仓库取出
[ItemConstSource.ITEM_SOURCE_EQUIP_PUTOFF] = 1,         --- 脱装备
}

---- 当角色升级时，逐一检查背包中是否有可快捷使用的装备且评分更高的装备
function BagSystem:UpdateLevel()
	local EquipInfoList = self:GetInvIdInfo(2)
	if not EquipInfoList then return end
	local EquipSlotDict = Game.EquipmentSystem:BatchEquipPutOn(EquipInfoList.slots) or {}
	for gbId, equipSlotIndex in ksbcpairs(EquipSlotDict) do
		local itemId = self:GetItemIdByGbId(gbId)
		self:CanEnqueueQuickUseQueue(gbId, itemId)
	end

	-- 当前没有物品在展示
	if not self.ItemSelected then
		self:RefreshQuickUseQueue()
	end
end

-- --- 接收物品新增或者删除事件
function BagSystem:ReceiveItemAddOrDelete(AddList, SourceId, ignoreShort)
	SourceId = SourceId and SourceId.source or 0
	--- 检查黑名单
	if Game.BagSystem.BLACKLIST[SourceId] then
		return
	end

	--- 入队
	if AddList.equip and next(AddList.equip) then
		local EquipSlotDict = Game.EquipmentSystem:BatchEquipPutOn(AddList.equip) or {}
		for gbId, equipSlotIndex in ksbcpairs(EquipSlotDict) do
			local itemId = self:GetItemIdByGbId(gbId)
			self:CanEnqueueQuickUseQueue(gbId, itemId, ignoreShort)
		end
	end
	Game.EquipmentSystem:RefreshInventoryRedPoint()

	local ItemList = {}
	for gbId, itemId in ksbcpairs(AddList.item) do
		if not self:IsShowUseLimit(itemId) then
			ItemList[itemId] = gbId
		end
	end
	for itemId, gbId in ksbcpairs(ItemList) do
		self:CanEnqueueQuickUseQueue(gbId, itemId, ignoreShort)
	end

	-- 当前没有物品在展示
	if not self.ItemSelected then
		self:RefreshQuickUseQueue()
	end
end

--- 新物品进队列
function BagSystem:CanEnqueueQuickUseQueue(gbId, itemId, ignoreShort)
	local ItemOperationData = Game.TableData.GetItemOperationTypeDataRow(itemId)
	if not ItemOperationData then
		return
	end
	if (not ItemOperationData.canShort) and (not ignoreShort) then
		return
	end

	local ItemExcelData = Game.TableData.GetItemNewDataRow(itemId)
	if not ItemExcelData then
		return
	end

	-- 该物品是装备，入队
	if ItemExcelData.type == BagSystem.ItemType.EQUIPMENT then
		self.QuickUseItemQueue:Enqueue(gbId)
		-- self.ItemInfoMap[gbId] = equipSlot
	else
		--- 有物品在展示中
		if self.ItemSelected then
			--- 该物品已经在展示中，刷新界面
			local SelectedItemId = self:GetItemIdByGbId(self.ItemSelected)
			if SelectedItemId == itemId then
				if UI.IsShow("P_HUDBaseView") then
					Game.HUDSystem:ShowUI("HUD_QuickUse")
				end
				return
			end
		end

		--- 该物品不在等待队列中，入队
		if not self.ItemInfoMap[itemId] then
			self.QuickUseItemQueue:Enqueue(gbId)
			self.ItemInfoMap[itemId] = true
		end
	end
end

--- 刷新队列的当前物品的显示(通过快捷弹窗进行装备/使用)
function BagSystem:RefreshQuickUseQueue()
	if self.ItemSelected then
		return
	end

	if self.QuickUseItemQueue:Count() > 0 then
		local gbId = self.QuickUseItemQueue:Dequeue()
		local itemId = self:GetItemIdByGbId(gbId)
		if not itemId then
			self:RefreshQuickUseQueue()
			return
		end
		local ItemExcelData = Game.TableData.GetItemNewDataRow(itemId)
		if ItemExcelData and ItemExcelData.type == BagSystem.ItemType.EQUIPMENT then
			local SlotInfo = self:GetItemInfoWithGbId(gbId)
			local equipSlot = Game.EquipmentSystem:GetBetterSlotByInfo(SlotInfo)
			--- 当前已装备了更好的装备
			if not equipSlot then
				self:RefreshQuickUseQueue()
				return
			end
		else
			if itemId then
				self.ItemInfoMap[itemId] = nil
			end
		end

		self.ItemSelected = gbId
		if UI.IsShow("P_HUDBaseView") then
			Game.HUDSystem:ShowUI("HUD_QuickUse")
		end
	else
		self.ItemSelected = nil
		self.ItemInfoMap = {}
	end
end

--- 装备之后检查当前正在展示的装备是否需要刷新(通用的装备方式)
function BagSystem:CheckIfRefreshQuickUseQueueAfterEquip()
	if not self.ItemSelected then return end
	local SlotInfo = self:GetItemInfoWithGbId(self.ItemSelected)
	local equipSlot = Game.EquipmentSystem:GetBetterSlotByInfo(SlotInfo)
	if equipSlot then
		---当前的装备仍是评分较高的装备
		return
	else
		self:RefreshQuickUseQueue()
	end
end

--- 用于被其他界面打断的情况, P_HUDBaseView恢复时恢复
function BagSystem:CheckQuickUsePopupState()
	--- 当前有物品
	if self.ItemSelected then
		if UI.IsShow('P_HUDBaseView') then
			Game.HUDSystem:ShowUI("HUD_QuickUse")
		end
	end
end

function BagSystem:GetItemSelected()
	return self.ItemSelected
end


---关闭快捷弹窗时，reset标记
function BagSystem:ResetQuickPopProp()
	self.ItemSelected = nil
end