local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIComNumberSlider = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local StringConst = require "Data.Config.StringConst.StringConst"
local BagMedicineItem = kg_require "Gameplay.LogicSystem.BagSystem.Medicine.BagMedicineItem"

---@class BagMedicineAuto_ItemParam
---@field AutoUseType Game.BagSystem.AutoUseType 自动使用药品的类型

---@class BagMedicineAuto_Item : UIListItem
---@field view BagMedicineAuto_ItemBlueprint
local BagMedicineAuto_Item = DefineClass("BagMedicineAuto_Item", UIListItem)

BagMedicineAuto_Item.CanAutoUseNum = 3

BagMedicineAuto_Item.eventBindMap = {
	
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BagMedicineAuto_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BagMedicineAuto_Item:InitUIData()
	---@type BagMedicineAuto_ItemParam
	self.Param = nil
	---@type BagMedicineItemParam[] 用于记录自动恢复的药品或者食物列表
	self.AutoRecoverItemListParam = {}
	---@type number 当前自动使用药品的阈值
	self.CurAutoThreshold = 0
end

--- UI组件初始化，此处为自动生成
function BagMedicineAuto_Item:InitUIComponent()
    ---@type UISimpleList
    self.AutoRecoverListCom = self:CreateComponent(self.view.AutoRecoverList, UISimpleList)
    ---@type UIComButton
    self.WBP_QuickAddCom = self:CreateComponent(self.view.WBP_QuickAdd, UIComButton)
    ---@type UIComNumberSlider
    self.WBP_ComSliderCom = self:CreateComponent(self.view.WBP_ComSlider, UIComNumberSlider)
end

---UI事件在这里注册，此处为自动生成
function BagMedicineAuto_Item:InitUIEvent()
    self:AddUIEvent(self.WBP_QuickAddCom.onClickEvent, "on_WBP_QuickAddCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComSliderCom.onValueChange, "on_WBP_ComSliderCom_ValueChange")
    self:AddUIEvent(self.WBP_ComSliderCom.onValueCommit, "on_WBP_ComSliderCom_ValueCommit")
    self:AddUIEvent(self.AutoRecoverListCom.onGetEntryLuaClass, "on_AutoRecoverListCom_GetEntryLuaClass")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BagMedicineAuto_Item:InitUIView()
	self.WBP_QuickAddCom:Refresh(StringConst.Get("BAG_ONECLICK_ADDITION"))
end

---面板打开的时候触发
---@param param BagMedicineAuto_ItemParam
function BagMedicineAuto_Item:OnRefresh(param)
	self.Param = param
	self.AutoUseType = param.AutoUseType
	
	self:RefreshAutoRecoverList()
	self.WBP_ComSliderCom:Refresh(self.CurAutoThreshold, 0, 100, 1)
	self:UpdateAutoHpThresholdInfo(self.CurAutoThreshold)
end

---@public 刷新自动恢复列表
function BagMedicineAuto_Item:RefreshAutoRecoverList()
	local autoUseType = self.AutoUseType
	
	local itemIds
	--- 药品
	if autoUseType == Game.BagSystem.AutoUseType.Medicine then
		itemIds = Game.BagSystem.model.autoHpDrugItemIds
		self.CurAutoThreshold = Game.me and Game.me.autoDrugHpThreshold or 0
		--- 食品
	elseif autoUseType == Game.BagSystem.AutoUseType.Food then
		itemIds = Game.BagSystem.model.autoHpFoodItemIds
		self.CurAutoThreshold = Game.me and Game.me.autoFoodHpThreshold or 0
	end
	self.CurAutoThreshold = math.floor(self.CurAutoThreshold * 100)
	
	table.clear(self.AutoRecoverItemListParam)
	for i = 1, BagMedicineAuto_Item.CanAutoUseNum do
		local param = self:CreateItemParam(itemIds and itemIds[i], i)
		table.insert(self.AutoRecoverItemListParam, param)
	end
	self:UpdateInfo()
end

---@public
function BagMedicineAuto_Item:UpdateInfo()
	self.AutoRecoverListCom:Refresh(self.AutoRecoverItemListParam)
end

---@private 创建一个道具参数
---@param itemId number
---@param index number
---@return BagMedicineItemParam
function BagMedicineAuto_Item:CreateItemParam(itemId, index)
	---@type BagMedicineItemParam
	local param = {
		ItemId = itemId,
		Index = index,
		AutoUseType = self.Param.AutoUseType,
		ClickDeleteSignCallback = function(...)self:ClickDeleteSignCallback(...)end,
	}
	return param
end


--- 此处为自动生成
function BagMedicineAuto_Item:on_WBP_QuickAddCom_ClickEvent()
	local sortedTypeList = Game.BagSystem:GetMedicineData(self.Param.AutoUseType)
	local needSet = false -- luacheck:ignore
	for index, itemInfo in pairs(sortedTypeList) do
		if index > self.CanAutoUseNum then
			break
		end
		if itemInfo.ConditionType == Game.BagSystem.MedicineConditionType.Valid then
			self.AutoRecoverItemListParam[index].ItemId = itemInfo.itemId
			needSet = true
		else
			self.AutoRecoverItemListParam[index].ItemId = nil
		end
	end
	
	self.AutoRecoverListCom:Refresh(self.AutoRecoverItemListParam)
	if #self.AutoRecoverItemListParam > 0 then
		self:BatchSetAutoHpMedicine()
	else
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ITEM_MEDICINE_CAN_NOT_CHOOSE)
	end
end

function BagMedicineAuto_Item:BatchSetAutoHpMedicine()
	local itemList = {}
	for idx, itemParam in pairs(self.AutoRecoverItemListParam) do
		itemList[idx] = itemParam.ItemId and math.floor(itemParam.ItemId) or nil
	end
	if Game.me then
		if self.AutoUseType == Game.BagSystem.AutoUseType.Food then
			Game.me:batchSetAutoHpFood(itemList)
		elseif self.AutoUseType == Game.BagSystem.AutoUseType.Medicine then
			Game.me:batchSetAutoHpDrug(itemList)
		end
	end
end

--- 此处为自动生成
---@param value number
function BagMedicineAuto_Item:on_WBP_ComSliderCom_ValueChange(value)
	self:UpdateAutoHpThresholdInfo(value)
end

--- 此处为自动生成
---@param value number
function BagMedicineAuto_Item:on_WBP_ComSliderCom_ValueCommit(value)
	self:SetAutoHpThreshold(value)
end

--- 自动使用药品的阈值rpc
function BagMedicineAuto_Item:SetAutoHpThreshold(sliderValue)
	if Game.me then
		local value = sliderValue/100
		local autoUseType = self.Param.AutoUseType
		if autoUseType == Game.BagSystem.AutoUseType.Medicine then
			Game.me:setAutoDrugHpThreshold(value)
		elseif autoUseType == Game.BagSystem.AutoUseType.Food then
			Game.me:setAutoFoodHpThreshold(value)
		end
	end
end

--- 更新设置自动使用药品的阈值信息
function BagMedicineAuto_Item:UpdateAutoHpThresholdInfo(sliderValue)
	local textString = (math.floor(sliderValue)) .. '%' --- 血量低于..%时
	self.view.ConditionTipText:SetText(textString)
end

-- 删除按钮回调
function BagMedicineAuto_Item:ClickDeleteSignCallback(index)
	self.AutoRecoverItemListParam[index].ItemId = nil
	self:BatchSetAutoHpMedicine()
end


--- 此处为自动生成
---@param index number
---@return UIComponent
function BagMedicineAuto_Item:on_AutoRecoverListCom_GetEntryLuaClass(index)
	return BagMedicineItem
end

---@private 根据ID查找索引
---@param itemId number
---@return number|nil
function BagMedicineAuto_Item:FindItem(itemId)
	for idx, itemParam in pairs(self.AutoRecoverItemListParam) do
		if itemParam.ItemId == itemId then
			return idx
		end
	end
	return nil
end

--- 添加物品后刷新
function BagMedicineAuto_Item:UpdateAutoRecoverItemList(itemId, index)
	local preIndex = self:FindItem(itemId)
	if preIndex and preIndex == index then
		return
	end

	if preIndex then
		self.AutoRecoverItemListParam[preIndex].ItemId = nil
		self.AutoRecoverListCom:RefreshItemByIndex(preIndex)
	end
	self.AutoRecoverItemListParam[index].ItemId = math.floor(itemId)
	self.AutoRecoverListCom:RefreshItemByIndex(index)

	self:BatchSetAutoHpMedicine()
end

return BagMedicineAuto_Item
