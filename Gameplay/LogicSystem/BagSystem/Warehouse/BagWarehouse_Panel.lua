local UIComSimpleTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
--local UIComHalfScreenAdaptiveMask = kg_require("Framework.KGFramework.KGUI.Component.Background.UIComHalfScreenAdaptiveMask")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require "Data.Config.StringConst.StringConst"
local BagTree_Item = kg_require("Gameplay.LogicSystem.BagSystem.Common.BagTree_Item")

local ESlateVisibility = import("ESlateVisibility")
local ESelectionMode = import("ESelectionMode")

---@class BagWarehouse_Panel : UIPanel
---@field view BagWarehouse_PanelBlueprint
local BagWarehouse_Panel = DefineClass("BagWarehouse_Panel", UIPanel)

BagWarehouse_Panel.eventBindMap = {
	[EEventTypesV2.ON_WAREHOUSE_PAGE_CHANGE] = "UpdateWarehousePageInfo",
	[EEventTypesV2.ON_UNLOCK_WAREHOUSE_PAGE] = "Receive_UnLockPageSuccess",
	[EEventTypesV2.ON_CANCEL_SELECTION] = "CancelSelected",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function BagWarehouse_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function BagWarehouse_Panel:InitUIData()

	---@type int tab页签被选中的index
	self.TabIndexSelected = -1
	---@type int 需要记录解锁的index
	self.needUnLockTabIndex = -1
	---@type ComTabHor_Param[] 仓库页签列表
	self.WarehouseTabList = {}
	
	---@type BagTree_Item_Param[] 仓库格子列表
	self.WarehouseGridList = {}

	---@type BagTree_Item_Param[] 分解格子列表
	self.ResolveInfoList = {}
	
	self:InitWarehouseTab()
end

--- UI组件初始化，此处为自动生成
function BagWarehouse_Panel:InitUIComponent()
    ---@type UIComHalfScreenAdaptiveMask
    --self.WBP_ComMaskL_AdaptiveCom = self:CreateComponent(self.view.WBP_ComMaskL_Adaptive, UIComHalfScreenAdaptiveMask)
    ---@type UIComSimpleTabList
    self.WBP_ComTabHorCom = self:CreateComponent(self.view.WBP_ComTabHor, UIComSimpleTabList)
    ---@type UIComButton
    self.CloseCom = self:CreateComponent(self.view.Close, UIComButton)
    ---@type UIComButton
    self.ArrangeCom = self:CreateComponent(self.view.Arrange, UIComButton)
    ---@type UIListView
    self.WarehouseGridListCom = self:CreateComponent(self.view.WarehouseGridList, UIListView)
end

---UI事件在这里注册，此处为自动生成
function BagWarehouse_Panel:InitUIEvent()
    self:AddUIEvent(self.WarehouseGridListCom.onItemSelectionChanged, "on_WarehouseGridListCom_ItemSelectionChanged")
    self:AddUIEvent(self.WarehouseGridListCom.onGetEntryLuaClass, "on_WarehouseGridListCom_GetEntryLuaClass")
    self:AddUIEvent(self.ArrangeCom.onClickEvent, "on_ArrangeCom_ClickEvent")
    self:AddUIEvent(self.CloseCom.onClickEvent, "on_CloseCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComTabHorCom.onItemSelected, "on_WBP_ComTabHorCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BagWarehouse_Panel:InitUIView()
	self.ArrangeCom:SetName(StringConst.Get("BAG_TIDY_STOREHOUSE")) -- 整理仓库
	self.CloseCom:SetName(StringConst.Get("BAG_CLOSE_STOREHOUSE"))  -- 关闭仓库
end

---面板打开的时候触发
function BagWarehouse_Panel:OnRefresh(...)
	self.isFirstRefresh = false
	self:SwitchResolveAndHomePageInit()

	UI.Invoke("Bag_Panel", 'EnterOrExitWarehouse')
end

function BagWarehouse_Panel:SwitchResolveAndHomePageInit()
	self.TabIndexSelected = 1
	local bResolveUI = Game.BagSystem.bResolveUI

	self.WarehouseGridListCom:SetSelectionMode(bResolveUI and ESelectionMode.Multi or ESelectionMode.Single)
	-- todo
	--self.WarehouseGridListCom:EnableDoubleClick(not bResolveUI)

	local visibility = bResolveUI and ESlateVisibility.Collapsed or ESlateVisibility.HitTestInvisible
	self.view.StockPanel:SetVisibility(visibility)
	self.view.RemindPanel:SetVisibility(visibility)
	
	local visibilityArrange = bResolveUI and ESlateVisibility.Collapsed or ESlateVisibility.Visible
	self.view.Arrange:SetVisibility(visibilityArrange)
	
	self:UpdateTabList()
end

--- 此处为自动生成
function BagWarehouse_Panel:on_ArrangeCom_ClickEvent()
	local TabId = self.WarehouseTabList[self.TabIndexSelected].ID
	Game.WarehouseSystem.sender:SortWarehousePage(TabId)
end

--- 此处为自动生成
function BagWarehouse_Panel:on_CloseCom_ClickEvent()
	self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function BagWarehouse_Panel:on_WarehouseGridListCom_GetEntryLuaClass(index)
	return BagTree_Item
end

---@function 初始化tab数据
function BagWarehouse_Panel:InitWarehouseTab()
	local warehouseData = Game.TableData.GetWareHousePageDataTable()
	for ID, Info in ksbcpairs(warehouseData) do
		table.insert(self.WarehouseTabList, Info)
	end

	table.sort(self.WarehouseTabList, function(a, b)
		return a.ID < b.ID
	end)
end

--- 更新当前分页格子库存数（物品数/当前已解锁的格子总数）
function BagWarehouse_Panel:UpdateWarehousePageStock()
	local maxSlot = 0
	local useSlot = 0

	local tabData = self.WarehouseTabList[self.TabIndexSelected]
	local warehousePageInfo = Game.WarehouseSystem:GetPageIdInfo(tabData.ID)
	if warehousePageInfo then
		if warehousePageInfo.unlockType ~= Game.WarehouseSystem.WARE_HOUSE_UNLOCK_TYPE.UNLOCKED then
			return
		end
		maxSlot = warehousePageInfo.slotNumber
		--- 已使用格子数
		useSlot = Game.WarehouseSystem:CountWarehouseUsedSlots(tabData.ID)
	end

	local widget = self.view
	widget.TB_UseSlot:SetText(useSlot)
	widget.TB_MaxSLot:SetText(maxSlot)

	local avaliableSlot = maxSlot - useSlot
	local useTag = "T_Bag"
	if avaliableSlot == 0 then
		useTag = "T_BagRed"
	elseif avaliableSlot <= Game.BagSystem.SINGLE_LINE_MAX_SLOT_NUM then
		useTag = "T_BagYellow"
	end
	local useSlotColor = Game.ColorManager:GetColor("Common", useTag, Game.ColorManager.Type.SlateColor)
	local maxSlotColor = Game.ColorManager:GetColor("Common", "T_Bag", Game.ColorManager.Type.SlateColor)
	if useSlotColor then
		widget.TB_UseSlot:SetColorAndOpacity(useSlotColor)
	end
	if maxSlotColor then
		widget.TB_MaxSLot:SetColorAndOpacity(maxSlotColor)
	end
end

--- 刷新仓库页签
function BagWarehouse_Panel:UpdateTabList()
	self.tabInfo = {}
	for _, TabData in pairs(self.WarehouseTabList) do
		local WarehousePageInfo = Game.WarehouseSystem:GetPageIdInfo(TabData.ID)
		---@type UITabData
		local info = {
			name = TabData.depotName,
			isLock = WarehousePageInfo and (WarehousePageInfo.unlockType ~= Game.WarehouseSystem.WARE_HOUSE_UNLOCK_TYPE.UNLOCKED) or false,
			onLockCallback = function(index) self:OnClickLockTab(index) end
		}
		table.insert(self.tabInfo, info)
	end
	self.WBP_ComTabHorCom:Refresh(self.tabInfo)
	self.WBP_ComTabHorCom:SetSelectedItemByIndex(self.TabIndexSelected, true)
end

--- 刷新当前分页信息
function BagWarehouse_Panel:UpdateWarehousePageInfo()
	if not Game.BagSystem.bResolveUI then
		self:UpdateWarehouseGridList()
		self:UpdateWarehousePageStock()
	else
		UI.Invoke("Bag_Panel", "UpdateResolveProductPreview")
	end
end

--- 检查当前分页是否解锁
function BagWarehouse_Panel:CheckPageNeedUnlock(index)
	local tabData = self.WarehouseTabList[index]
	local warehousePageInfo = Game.WarehouseSystem:GetPageIdInfo(tabData.ID)
	---当前分页未解锁
	if warehousePageInfo.unlockType ~= Game.WarehouseSystem.WARE_HOUSE_UNLOCK_TYPE.UNLOCKED then
		--local okCallback = function()
		--    Game.WarehouseSystem.sender:UnlockWarehousePage(TabData.ID)
		--end
		--local ComfirmCallback = function()
		--    Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(TabData.openCostItem, TabData.openCostNumber,
		--    okCallback)
		--end
		--- 通过货币解锁
		if tabData.depotUnlockType == 0 then
			local img = Game.UIIconUtils.GetIconByItemId(tabData.openCostItem, nil, true) or ""
			Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.WAREHOUSE_UNLOCK_PAGE,
				function()
					Game.CurrencyExchangeSystem:CurrencyConsumptionProcess(tabData.openCostItem, tabData.openCostNumber,
						function()
							Game.WarehouseSystem.sender:UnlockWarehousePage(tabData.ID)
						end)
				end,
				nil,{ img, tabData.openCostNumber})
		end
	end
end

---页签解锁成功回调
function BagWarehouse_Panel:Receive_UnLockPageSuccess()
	self.TabIndexSelected = self.needUnLockTabIndex
	self:UpdateTabList()
end


---点击加锁的页签
function BagWarehouse_Panel:OnClickLockTab(index)
	self.needUnLockTabIndex = index
	self:CheckPageNeedUnlock(index)
end

function BagWarehouse_Panel:OnClose()
	Game.WarehouseSystem.bWarehouseUI = false
	self.isFirstRefresh = false
	UI.Invoke("Bag_Panel", 'EnterOrExitWarehouse')
end

--- 刷新仓库格子列表
function BagWarehouse_Panel:UpdateWarehouseGridList()
	local tabData = self.WarehouseTabList[self.TabIndexSelected]
	if tabData == nil then
		return
	end
	local tabId = tabData.ID
	local warehousePageInfo = Game.WarehouseSystem:GetPageIdInfo(tabId)
	local slots = warehousePageInfo.slots
	if not slots then return end
	
	--- 需要解锁的格子最多展示两行
	local lockData = 0
	local warehouseSlotData = Game.TableData.GetWareHouseSlotDataRow(tabId)
	if warehouseSlotData then
		local maxKey = table.maxn(warehouseSlotData)
		local allLockNum = warehouseSlotData[maxKey].gridUnlockTotalNumber
		local leftLockData = allLockNum - (warehousePageInfo.slotNumber - tabData.initGrid)
		if leftLockData < 0 then
			leftLockData = 0
		end
		lockData = leftLockData <= 12 and leftLockData or 12
	end
	local slotNumber = warehousePageInfo.slotNumber + lockData
	
	table.clear(self.WarehouseGridList)
	for i = 1, slotNumber do
		---@type BagTree_Item_Param
		local param = {
			ItemFrom = BagSystem.ItemFrom.ITEM_FROM_WAREHOUSE,
			SlotInfo = slots[i],
			InvID = tabId,
			IsLocked = i > warehousePageInfo.slotNumber,
			ParentUI = self,
		}
		table.insert(self.WarehouseGridList, param)
	end
	self.WarehouseGridListCom:Refresh(self.WarehouseGridList)
end

---------------------------------------------------------------------仓库分解界面----------------------------------------------------------

--- 获取满足分解条件的物品信息
function BagWarehouse_Panel:RefreshResolveInfoList()
	local P_Bag = UI.GetUI("Bag_Panel")
	if not P_Bag then return end
	
	table.clear(self.ResolveInfoList)
	
	local decomposeId = P_Bag:GetCurDecomposeId()
	local pageId = self.WarehouseTabList[self.ResolveTabIndexSelected].ID
	
	local pageIdInfo = Game.WarehouseSystem:GetPageIdInfo(pageId)
	if not pageIdInfo then
		return
	end

	local slotInfos = Game.WarehouseSystem:GetWarehouseItemDataByResolveFilterId(pageId, decomposeId)
	for i = 1, pageIdInfo.slotNumber do
		---@type BagTree_Item_Param
		local param = {
			ItemFrom = BagSystem.ItemFrom.ITEM_FROM_WAREHOUSE,
			SlotInfo = slotInfos[i],
			InvID = pageId,
			ParentUI = self,
		}
		table.insert(self.ResolveInfoList, param)
	end
	self.WarehouseGridListCom:Refresh(self.ResolveInfoList)
end

--- 获取服务器需要的分解物品信息
function BagWarehouse_Panel:GetWarehouseSelectedResolveResult()
	local tabId = self.WarehouseTabList[self.ResolveTabIndexSelected].ID
	local pageIdInfo = Game.WarehouseSystem:GetPageIdInfo(tabId)
	if not pageIdInfo then return end

	local resolveItemIndies = self.WarehouseGridListCom:GetSelectedItemIndexes()
	local resolveDict = {}
	for _, index in pairs(resolveItemIndies) do
		local itemParam = self.ResolveInfoList[index]
		local slotInfo = itemParam.SlotInfo
		resolveDict[slotInfo.index] = slotInfo.gbId
	end
	return resolveDict, tabId
end

--- 刷新分解界面
function BagWarehouse_Panel:UpdateResolveTabPage()
	self:RefreshResolveInfoList()
	self.WarehouseGridListCom:ClearSelection()
	self.WarehouseGridListCom:ScrollToItemByIndex(1)
end

---@public 刷新品质选择
---@param chkQualities table<int, bool> 品质是否选中
---@param quality int 当前操作的品质
function BagWarehouse_Panel:OnClickQualityOption(chkQualities, quality)
	local isAll = quality == 0
	for index, itemParam in pairs(self.ResolveInfoList) do
		local slotInfo = itemParam.SlotInfo
		local needUpdate = slotInfo and (isAll or slotInfo.quality == quality)
		if needUpdate and Game.BagSystem:CheckEquipIsBetterOrUpgrade(slotInfo) then
			local checked = chkQualities[slotInfo.quality] or chkQualities[0] or false
			self.WarehouseGridListCom:SetSelectedItemByIndex(index, checked)
		end
	end
end

function BagWarehouse_Panel:GetResolveSelectedInfo()
	local resolveItemList = self.WarehouseGridListCom:GetSelectedItemIndexes()
	local resolveItemParams = {}
	for _, index in ipairs(resolveItemList) do
		table.insert(resolveItemParams, self.ResolveInfoList[index])
	end
	return resolveItemParams--resolveItemList, self.ResolveInfoList
end

---@private 根据索引获取道具参数
---@return BagTree_Item_Param, SlotInfo
function BagWarehouse_Panel:GetItemParam(index)
	local listItem = self.WarehouseGridListCom:GetItemByIndex(index)
	if not listItem then return end
	
	local param = listItem:GetData()
	return param, param.SlotInfo
end

---单击仓库格子
function BagWarehouse_Panel:OnClickItem(index)
	local itemParam, slotInfo = self:GetItemParam(index)
	if not slotInfo then return end
	
	if Game.BagSystem.bResolveUI then
		UI.Invoke("Bag_Panel", "UpdateResolveProductPreview")
	else
		if slotInfo.IsLocked then
			Game.WarehouseSystem:WarehouseUnlockSlot(itemParam.InvID, pageIdInfo.unlockTimes)
		else
			self:ShowItemTips(itemParam.InvID, slotInfo, true)
		end
	end
end

--双击
function BagWarehouse_Panel:OnDoubleClickItem(index)
	if Game.BagSystem.bResolveUI then return end
	
	local itemParam, slotInfo = self:GetItemParam(index)
	if not slotInfo then return end
	Game.WarehouseSystem:TakeOutFromWarehouse(itemParam.InvID, slotInfo.index)
	UI.HideUI('BagItemTips_Panel', true)
end

--- 长按
function BagWarehouse_Panel:OnLongPressItem(index)
	local _, slotInfo = self:GetItemParam(index)
	if not slotInfo then return end
	
	if Game.BagSystem.bResolveUI then
		Game.TabClose:IgnoreNextMouseUpEvent("Bag_Panel:OnLongPressItem")
		self:ShowItemTips(nil, slotInfo, false)
	end
end

-- 打开tips
function BagWarehouse_Panel:ShowItemTips(tabId, slotInfo, isShowBtn)
	-- Log.Debug('P_Warehouse:ShowItemTips')
	local Params = {
		IsShowBtn = isShowBtn,
		Position = Game.BagSystem.TipPosType.default,
		ItemFrom = Game.BagSystem.ItemFrom.ITEM_FROM_WAREHOUSE,
		SlotInfo = slotInfo,
		TabId = tabId,
	}
	UI.ShowUI('BagItemTips_Panel', slotInfo.itemId, Params)
end


--- 此处为自动生成
---@param index number
---@param selected bool
function BagWarehouse_Panel:on_WarehouseGridListCom_ItemSelectionChanged(index, selected)
	if Game.BagSystem.bResolveUI then
		local itemParam = self.WarehouseGridListCom:GetItemByIndex(index)
		if not itemParam then return end
		UI.Invoke("Bag_Panel", "UpdateResolveProductPreview")
	end
end

--- tip关闭时，取消选中
function BagWarehouse_Panel:CancelSelected()
	if not Game.BagSystem.bResolveUI then
		self.WarehouseGridListCom:ClearSelection()
	end
end


--- 此处为自动生成
---@param index number
---@param data table
function BagWarehouse_Panel:on_WBP_ComTabHorCom_ItemSelected(index, data)
	if Game.BagSystem.bResolveUI then
		self.ResolveTabIndexSelected = index
		self:UpdateResolveTabPage()
		if self.isFirstRefresh then
			UI.Invoke("Bag_Panel", "UpdateResolveCheckBoxData")
			UI.Invoke("Bag_Panel", "UpdateResolveProductPreview")
		end
		self.isFirstRefresh = true
	else
		self.TabIndexSelected = index
		self:UpdateWarehousePageInfo()
		local TabData = self.WarehouseTabList[index]
		Game.WarehouseSystem:UpdateCurOpenWarehousePageId(TabData.ID)
	end
end

return BagWarehouse_Panel
