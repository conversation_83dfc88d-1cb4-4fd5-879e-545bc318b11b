local ComSelectTitle = kg_require("Framework.KGFramework.KGUI.Component.Select.ComSelectTitle")
---@begin define

---@class BagPanelItemBaseParam
---@field LuaClass table lua类

-- 背包标题参数信息
---@class BagTitle_ItemParam:BagPanelItemBaseParam
---@field InvID number 背包ID
---@field ShowTitle boolean 是否显示一级标题

---@end define

---@class BagTitle_Item:ComSelectTitle
local BagTitle_Item = DefineClass("BagTitle_Item", ComSelectTitle)
local ESlateVisibility = import("ESlateVisibility")

BagTitle_Item.eventBindMap = {
	[EEventTypesV2.BAG_PAGE_INVENTORY_CHANGE] = "UpdateInfo",
}

---初始化数据
function BagTitle_Item:InitUIData()
	ComSelectTitle.InitUIData(self)
	
	---@type number 当前索引
	self.Index = -1
	---@type number 当前背包
	self.InvID = -1
	
	---@type ComSelectTitleParam 参数
	self.BaseParam = {}
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function BagTitle_Item:InitUIView()
	ComSelectTitle.InitUIView(self)
end

---@param data BagTitle_ItemParam
function BagTitle_Item:OnRefresh(data)
	local param = data.tabData or data

	self.userWidget:SetVisibility(param.ShowTitle and ESlateVisibility.Visible or ESlateVisibility.Hidden)
	if not param.ShowTitle then return end
	
	self.InvID = param.InvID
	local invData = Game.TableData.GetInventoryDataRow(self.InvID)
	self.BaseParam.Title = invData.bagName or ""
	
	self:UpdateInfo()
end

function BagTitle_Item:UpdateInfo()
	if self.InvID <= 0 or not self.InvID then return end
	
	self:UpdateNum()
	ComSelectTitle.OnRefresh(self, self.BaseParam)
end

---@function 更新数量
function BagTitle_Item:UpdateNum()
	local invInfos = Game.BagSystem:GetInvInfo()
	local invInfo = invInfos[self.InvID]
	local curNum = Game.BagSystem:CountUsedSlots(self.InvID)
	local maxNum = invInfo.slotNumber

	self.BaseParam.CurrentText = string.format("%d", curNum)
	self.BaseParam.TargetText = string.format("%d", maxNum)
end

return BagTitle_Item