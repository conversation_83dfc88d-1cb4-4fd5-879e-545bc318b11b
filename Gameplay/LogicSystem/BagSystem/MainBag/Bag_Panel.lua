local ComSelectTitle = kg_require("Framework.KGFramework.KGUI.Component.Select.ComSelectTitle") -- luacheck: ignore
local BagTitle_Item = kg_require("Gameplay.LogicSystem.BagSystem.MainBag.BagTitle_Item")
local UIComTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComTabList")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComDropDown = kg_require("Framework.KGFramework.KGUI.Component.Select.UIComDropDown")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UITreeView = kg_require("Framework.KGFramework.KGUI.Component.UITreeView.UITreeView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local BagTab_Item = kg_require("Gameplay.LogicSystem.BagSystem.MainBag.BagTab_Item")
local StringConst = kg_require "Data.Config.StringConst.StringConst"

local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
local ESlateVisibility = import("ESlateVisibility")
local ESelectionMode = import("ESelectionMode")

---@begin define

--- 主tab
---@class Primary_UITabData : UITabData
---@field InvID number 背包id

--- 分解tab
---@class Decompose_UITabData : UITabData
---@field DecomposeID number 分解页签ID

---@end define

---@class Bag_Panel : UIPanel
---@field view Bag_PanelBlueprint
local Bag_Panel = DefineClass("Bag_Panel", UIPanel)

Bag_Panel.eventBindMap = {
	[EEventTypesV2.BAG_ITEM_CHANGE] = "OnRefreshItemList",
	[EEventTypesV2.BAG_ITEM_ORGANIZE] = "OnArrangeItemList",
	[_G.EEventTypes.EQUIP_EXCHANGE] = "OnRefreshItemList",
	[EEventTypesV2.BAG_ITEMS_DECOMPOSE_BATCH] = "OnDecomposeBatchItems",
	[_G.EEventTypes.SEALED_DECOMPOSE_BATCH] = "OnDecomposeBatchItems",
	[EEventTypesV2.BAG_AUTO_DECOMPOSE_BTN_OPEN_SWITCH] = "UpdateAutoDecomposeOpenSwitch",
	[EEventTypesV2.BAG_PAGE_INVENTORY_CHANGE] = "UpdateBagPageInventory",
	[EEventTypesV2.ON_UI_OPEN] = "OnUIOpen",
	[EEventTypesV2.BAG_ITEM_ADD_OR_DELETE] = "OnBagItemAddOrDelete",
	[_G.EEventTypes.ON_SELF_LEVEL_CHANGED] = { "OnLevelChanged", GetMainPlayerEID },
	[EEventTypesV2.ON_CANCEL_SELECTION] = "CancelSelected",
}

---@class 背包展示类型
---@field Normal number 普通
---@field Filter number 过滤
---@field Resolve number 分解
Bag_Panel.BagShowType = {
	Normal = 1,
	Filter = 2,
	Resolve = 3,
}

function Bag_Panel:OnClose()
	Game.HUDSystem:PlayerRightTopAni(true)
	Game.BagSystem.bResolveUI = false
	Game.WarehouseSystem.bWarehouseUI = false

	--播放动效
	self:PlayAnimation(self.view.WBP_ComMaskR.Ani_Fadeout, nil, self.view.WBP_ComMaskR)
	UI.HideUI("BagWarehouse_Panel")
end

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function Bag_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIView()
	self:InitUIEvent()
end

---初始化数据
function Bag_Panel:InitUIData()
	self:InitPrimaryTabList()
	self:InitResolveData()
end

--- UI组件初始化，此处为自动生成
function Bag_Panel:InitUIComponent()
    ---@type UIComButton
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UIComButton)
    ---@type UIComButton
    self.Resolve_QuickCom = self:CreateComponent(self.view.Resolve_Quick, UIComButton)
    ---@type UIComButton
    self.Resolve_ExitCom = self:CreateComponent(self.view.Resolve_Exit, UIComButton)
    ---@type UIComButton
    self.Arrange_BtnCom = self:CreateComponent(self.view.Arrange_Btn, UIComButton)
    ---@type UIComButton
    self.Resolve_BtnCom = self:CreateComponent(self.view.Resolve_Btn, UIComButton)
    ---@type UIComButton
    self.Warehouse_BtnCom = self:CreateComponent(self.view.Warehouse_Btn, UIComButton)
    ---@type UIListView
    self.ResolveProductListCom = self:CreateComponent(self.view.ResolveProductList, UIListView)
    ---@type UITreeView
    self.SubTabTreeCom = self:CreateComponent(self.view.SubTabTree, UITreeView)
    ---@type UIListView
    self.CheckBoxListCom = self:CreateComponent(self.view.CheckBoxList, UIListView)
    ---@type UIComDropDown
    self.WBP_ComComBoxCom = self:CreateComponent(self.view.WBP_ComComBox, UIComDropDown)
    --[[
	---@type ComSelectTitle
    self.SubTitleShowCom = self:CreateComponent(self.view.SubTitleShow, ComSelectTitle)
    ]]
    ---@type UIComTabList
    self.TabListCom = self:CreateComponent(self.view.TabList, UIComTabList)
end

---UI事件在这里注册，此处为自动生成
function Bag_Panel:InitUIEvent()
    self:AddUIEvent(self.SubTabTreeCom.onItemExpansionChanged, "on_SubTabTreeCom_ItemExpansionChanged")
    self:AddUIEvent(self.SubTabTreeCom.onGetEntryClassIndexForItem, "on_SubTabTreeCom_GetEntryClassIndexForItem")
    self:AddUIEvent(self.SubTabTreeCom.onGetEntryLuaClass, "on_SubTabTreeCom_GetEntryLuaClass")
    self:AddUIEvent(self.SubTabTreeCom.onItemSelectionChanged, "on_SubTabTreeCom_ItemSelectionChanged")
    self:AddUIEvent(self.SubTabTreeCom.onFrontEdgeIntersectionListItemChanged, "on_SubTabTreeCom_FrontEdgeIntersectionListItemChanged")
    self:AddUIEvent(self.ResolveProductListCom.onGetEntryLuaClass, "on_ResolveProductListCom_GetEntryLuaClass")
    self:AddUIEvent(self.CheckBoxListCom.onItemSelectionChanged, "on_CheckBoxListCom_ItemSelectionChanged")
    self:AddUIEvent(self.Warehouse_BtnCom.onClickEvent, "on_Warehouse_BtnCom_ClickEvent")
    self:AddUIEvent(self.Resolve_BtnCom.onClickEvent, "on_Resolve_BtnCom_ClickEvent")
    self:AddUIEvent(self.Arrange_BtnCom.onClickEvent, "on_Arrange_BtnCom_ClickEvent")
    self:AddUIEvent(self.Resolve_ExitCom.onClickEvent, "on_Resolve_ExitCom_ClickEvent")
    self:AddUIEvent(self.Resolve_QuickCom.onClickEvent, "on_Resolve_QuickCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.TabListCom.onItemSelected, "on_TabListCom_ItemSelected")
    self:AddUIEvent(self.TabListCom.onGetEntryLuaClass, "on_TabListCom_GetEntryLuaClass")
    self:AddUIEvent(self.view.AutoDecomposeBtn.Btn_ClickArea.OnClicked, "on_AutoDecomposeBtnBtn_ClickArea_Clicked")
    self:AddUIEvent(self.WBP_ComComBoxCom.onItemSelected, "on_WBP_ComComBoxCom_ItemSelected")
    self:AddUIEvent(self.view.RankAble.Btn_ClickArea.OnClicked, "on_RankAbleBtn_ClickArea_Clicked")
    self:AddUIEvent(self.SubTitleShowCom.onClickEvent, "on_SubTitleShowCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用	
function Bag_Panel:InitUIView()
	self.Warehouse_BtnCom:SetName(StringConst.Get("BAG_STOREHOUSE"))
	self.Resolve_BtnCom:SetName(StringConst.Get("BAG_RESOLVE"))
	self.Arrange_BtnCom:SetName(StringConst.Get("BAG_TIDY"))
	self.Resolve_ExitCom:SetName(StringConst.Get("BAG_EXIT"))
	self.Resolve_QuickCom:SetName(StringConst.Get("BAG_ONECLICK_RESOLVE"))

	---@type BagTitle_Item 重载掉
	self.SubTitleShowCom = self:CreateComponent(self.view.SubTitleShow, BagTitle_Item)
	
	---@type UICurrentcyWidget
	self.CurrencyList = self:OpenCurrencyWidget(self.view.Money)

	self.TabListCom:SetSelectionMode(ESelectionMode.Single)
end

---面板打开的时候触发
function Bag_Panel:OnRefresh(gbId)
	self:ResetCommonState()
	self:Change2NormalPage()
	
	--调整镜头
	if Game.CameraManager then
		Game.CameraManager:SetThirdCameraSysViewOffset(Game.TableData.GetCameraConstDataRow("BAG_CAMERA_FOCUS_COORD_X"), 
				Game.TableData.GetCameraConstDataRow("BAG_CAMERA_FOCUS_COORD_Y"), 
				Game.TableData.GetCameraConstDataRow("BAG_CAMERA_FOCUS_COORD_PARAM"))
	end
	Game.HUDSystem:PlayerRightTopAni(false)

	--播放动效
	local widget = self.view.WBP_ComMaskR
	--widget = self.userWidget
	self:PlayAnimation(widget.Ani_Fadein, nil, widget)

	if gbId then
		self:QuickPickItem(gbId)
	end
	---背包打开的时候，更新一下任务奖励的缓存，防止后续每个Item都获取，比较耗时
	Game.QuestSystem:UpdateActiveQuestRewardsCache()
end

function Bag_Panel:ResetCommonState()
	---@type boolean 是否第一次更新item tree
	self.IsFirstUpdateItemTree = true
	
	-- 过滤相关参数
	---@type BagFilterCheckBox_ItemParam[] 用来记录过滤checkbox数据
	self.FilterDatas = table.clear(self.FilterDatas) or {}
	---@type number 过滤ID
	self.FilterID = -1

	-- 分解品质相关参数
	---@type BagCheckBox_ItemParam[] 用来记录分解时品质checkbox数据
	self.TabDecomposeSelectRarities = table.clear(self.TabDecomposeSelectRarities) or {}
	---@type table<number, boolean> 用来记录分解时品质checkbox选中状态
	self.CheckQualityMap = {}
	---@type number 分解时全选的索引
	self.AllQualityIndex = nil
	---@type boolean 屏蔽计算分解参数
	self.ForbidUpdateResolveResult = false
	---@type table<number, boolean> 在分解时切换排序时记录的选中状态 
	self.LastSelectResolveItemSet = {}

	-- 排序相关参数
	---@type UITabData[] 用来记录排序Combbox数据
	self.SortComboBoxListData = {}
	---@type number 排序序号
	self.SortSelectedIndex = 1
	---@type boolean 是否升序
	self.bOrderAsc = false
	---@type BagSortParam 排序参数
	self.SortParam = nil
	
	-- 页签相关参数
	---@type number 选中的tab index(右侧大页签)
	self.TabIndexSelected = 1
	---@type number 选中的子tab index
	self.SubTabIndexSelected = 0
	---@type table<number, number> 槽位索引到树索引的映射
	self.SlotIdx2TreeIdxMap = nil

	---@type BagTitle_ItemParam 子标题
	self.SubTitleParam = {
		InvID = 0,
		ShowTitle = false
	}

	self.ShowType = Bag_Panel.BagShowType.Normal
	Game.BagSystem.bResolveUI = false
	Game.WarehouseSystem.bWarehouseUI = false

	--self.SubTabTreeCom:SetScrollOffset(0)
	self.SubTabTreeCom:SetScrollOffset(0)

	self.TabListCom:Refresh(nil)
end

--[[
	整体的状态转换为:
	初始状态：Normal
	Normal -> Filter: 点击了筛选项
	Normal -> Resolve: 点击了分解
	Resolve -> Normal: 点击了退出
	Filter -> Normal: 取消了筛选项或者点击了其他页签
]]

---@private 切换到分解模式
function Bag_Panel:Change2ResolvePage()
	Game.BagSystem.bResolveUI = true

	self.ShowType = Bag_Panel.BagShowType.Resolve

	---进入仓库分解界面
	UI.Invoke("BagWarehouse_Panel", 'SwitchResolveAndHomePageInit')

	-- 理论上应该不需要更新tab
	self:UpdateAllCommonInfo()

	--- 重置分解产物
	self.FinalResult = {}
	self.ResolveProductListCom:Refresh(self.FinalResult)

	self:UpdateItemTree()
end

---@private 切换到普通模式
function Bag_Panel:Change2NormalPage()
	self.ShowType = Bag_Panel.BagShowType.Normal

	self.FilterID = 0 -- 清掉过滤项
	self.SortParam = nil -- 清掉排序
	self:UpdateAllCommonInfo()

	self:UpdateItemTree()

	local invId = self.InvPrimaryList[self.TabIndexSelected]
	local tabData = Game.TableData.GetInventoryDataRow(invId)
	if tabData.primaryType then
		self:PlayAnimation(self.userWidget.Ani_SubTitle_lua_Content_in, nil, self.userWidget)
	end
	self:PlayAnimation(self.userWidget.Ani_List_Content_in, nil, self.userWidget)
end

---@private 切换到筛选项模式
function Bag_Panel:Change2FilterPage(filterId)
	if self.FilterID == filterId then return end
	
	self.ShowType = Bag_Panel.BagShowType.Filter

	self.FilterID = filterId

	self:UpdateSortFun()
	self:UpdateItemTree()
end

---快速选中
---@param gbId number 物品gbid
function Bag_Panel:QuickPickItem(gbId)
	local invId = Game.BagSystem:GetInvIdByGbid(gbId)
	local targetTabIndex = self:GetTabIndexByInvId(invId)
	if not targetTabIndex then return end

	if Game.BagSystem.bResolveUI then
		---退出分解界面
		self:on_Resolve_ExitCom_ClickEvent()
	end

	---切换对应页签
	if self.TabIndexSelected ~= targetTabIndex then
		--self.TabIndexSelected = targetTabIndex
		self.TabListCom:SetSelectedItemByIndex(targetTabIndex, true)
	end

	---选中对应的格子
	local slotInfo = Game.BagSystem:GetItemInfoWithGbId(gbId)
	if slotInfo then
		self:SelItem(slotInfo.index)
	end
end

---选中背包某个格子
function Bag_Panel:SelItem(index)
	local tabData = self.InvPrimaryList[self.TabIndexSelected]
	local tabIdx = tabData.primaryType and self.SubTabIndexSelected or 1
	self.SubTabTreeCom:SetSelectedItemByPath(true, tabIdx, index)
	self.SubTabTreeCom:ScrollToItemByPath(0, tabIdx, index)
end

--- 清理筛选项的状态
function Bag_Panel:ResetFilterList()
	self.CheckBoxListCom:ClearSelection()
end

---@function 更新大的分组信息,在切换背包
function Bag_Panel:UpdateGroupInfo()
	self:ResetFilterList()
	
	local isInResolve = Game.BagSystem.bResolveUI
	local resolveOffVis = isInResolve and ESlateVisibility.Collapsed or ESlateVisibility.SelfHitTestInvisible
	local resolveOnVis = isInResolve and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed
	local title = isInResolve and StringConst.Get("BAG_BATCH_DECOMPOSE") or StringConst.Get("BAG_TITLE")
	
	self.view.InterfaceTitle:SetText(title)
	self.view.ResolveOff:SetVisibility(resolveOffVis)
	self.view.ResolveOn:SetVisibility(resolveOnVis)
	self.view.ResolveProductPanel:SetVisibility(resolveOnVis)
	
	self.SubTabTreeCom:SetSelectionMode(isInResolve and ESelectionMode.Multi or ESelectionMode.Single)
	self.CheckBoxListCom:SetSelectionMode(isInResolve and ESelectionMode.Multi or ESelectionMode.SingleToggle)
	self.userWidget:Event_UI_Style(isInResolve)
	self:ShowSubTitle(false)
	
	if not isInResolve then
		self:EnterOrExitWarehouse()
	end
end

function Bag_Panel:ScrollToFirstNewItem()
	self:StartTimer("ScrollToFirstNewItem", function()
		local invId = self:GetCurTabId()
		local firstNewTipSlot = Game.BagSystem:GetFirstNewItemSlot(invId)
		if firstNewTipSlot then
			self.SubTabTreeCom:ScrollTreeItemIntoView(0, 1, firstNewTipSlot)
		else
			self.SubTabTreeCom:ScrollTreeItemIntoView(0, 1, 1)
		end
	end, 0.1, 1)
end

---@function 点击筛选项的回调
---@param index number 点击的筛选项索引
function Bag_Panel:OnFilterSelectionChanged(index, selected)
	local selectedIdx = self.CheckBoxListCom:GetSelectedItemIndex()
	if selectedIdx then
		local filterId = self.FilterDatas[index].FilterID
		self:Change2FilterPage(filterId)
	else
		self:Change2NormalPage()
	end
end

---进入或者退出仓库
function Bag_Panel:EnterOrExitWarehouse()
	local bWarehouseUI = Game.WarehouseSystem.bWarehouseUI
	
	self.Warehouse_BtnCom:SetVisible(not bWarehouseUI)

	if Game.BagSystem.bResolveUI then
		self:UpdateResolveProductPreview()
	end
end

--- 此处为自动生成
function Bag_Panel:on_Warehouse_BtnCom_ClickEvent()
	Game.WarehouseSystem.bWarehouseUI = true
	UI.ShowUI("BagWarehouse_Panel")
end

--- 此处为自动生成
function Bag_Panel:on_Resolve_BtnCom_ClickEvent()
	self:Change2ResolvePage()
	
	---播放动效
	self:PlayAnimation(self.userWidget.Ani_On, nil, self.userWidget, 0.0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

---@private 创建排序的列表数据
---@param sortIdList number[] 排序ID列表
function Bag_Panel:CreateSortComBoxListData(sortIdList)
	table.clear(self.SortComboBoxListData)

	for index, sortId in ksbcpairs(sortIdList) do
		local sortData = Game.TableData.GetSortTabDataRow(sortId)
		if sortData then
			---@type UITabData
			local optionData = {
				name = sortData.sortName,
				sortData = sortData
			}
			table.insert(self.SortComboBoxListData, optionData)
		end
	end
end

--- 刷新排序下拉列表
function Bag_Panel:RefreshComBoxData(sortIdList)
	if sortIdList and #sortIdList > 0 then
		self.view.WBP_ComComBox:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self:CreateSortComBoxListData(sortIdList)

		self.SortSelectedIndex = 1
		self.WBP_ComComBoxCom:Refresh(self.SortComboBoxListData, self.SortSelectedIndex, false)
		self:OnSelectedComBox(self.SortSelectedIndex)
	else
		self.view.WBP_ComComBox:SetVisibility(ESlateVisibility.Collapsed)
		self.view.RankAble:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--- 选中对应的下拉列表的某项
function Bag_Panel:OnSelectedComBox(index, data)
	self.SortSelectedIndex = index
	if self.SortComboBoxListData and self.SortComboBoxListData[index] then
		local sortData = self.SortComboBoxListData[index].sortData
		-- 是否显示升降序按钮
		if sortData.rankAble then
			self.view.RankAble:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self.bOrderAsc = false
			self:UpdateRankAbleStyle()
		else
			self.view.RankAble:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		self.view.RankAble:SetVisibility(ESlateVisibility.Collapsed)
	end
	self:UpdateSortParam()
end

-- 更新排序参数
function Bag_Panel:UpdateSortParam()
	local sortListDataItem = self.SortComboBoxListData[self.SortSelectedIndex]
	if not sortListDataItem then 
		self.SortParam = nil
		return
	end
	
	local sortData = self.SortComboBoxListData[self.SortSelectedIndex].sortData
	self.SortParam = {
		SortList = { Game.BagSystem.SortType.DefaultSort },
		IsAsc = self.bOrderAsc,
		IsSkipQuality = sortData.sortID == Game.BagSystem.SortType.QualityFirst
	}
	if sortData.sortID ~= Game.BagSystem.SortType.DefaultSort then
		table.insert(self.SortParam.SortList, 1, sortData.sortID)
	end
end

---排序功能: 背包主界面&筛选界面&分解界面
function Bag_Panel:UpdateSortFun()
	if Game.BagSystem.bResolveUI then
		local decomposeId = self:GetCurDecomposeId()
		local decomposeData = Game.TableData.GetDecomposeDataRow(decomposeId)
		if decomposeData and decomposeData.sortFunc then
			self:RefreshComBoxData(decomposeData.sortFunc)
		end
	else
		if self.FilterID > 0 then
			local filterData = Game.TableData.GetFilterDataRow(self.FilterID)
			self:RefreshComBoxData(filterData.sortFunc)
		else
			local tabId = self:GetCurTabId()
			local invData = Game.TableData.GetInventoryDataRow(tabId)
			if invData.filterAble then
				self:RefreshComBoxData(nil)
			else
				self:RefreshComBoxData(invData and invData.sortFunc)
			end
		end
	end
end

--- 此处为自动生成
function Bag_Panel:on_Arrange_BtnCom_ClickEvent()
	local localAvatarActor = NetworkManager.GetLocalAvatarActor()
	if not localAvatarActor then
		return
	end

	local invId = self:GetCurShowInvID()
	if invId and invId > 0 then
		localAvatarActor.remote:cleanUpInventory(invId)
	end
end

--- 此处为自动生成
function Bag_Panel:on_WBP_ComBtnCloseCom_ClickEvent()
	Game.BagSystem:CloseHomePage()
end

function Bag_Panel:PreClose(...)
	UIPanel.PreClose(self, ...)

	-- todo
	---调整镜头
	if Game.CameraManager then
		Game.CameraManager:RemoveThirdCameraSysViewOffset(Game.TableData.GetCameraConstDataRow("BAG_CAMERA_FOCUS_COORD_PARAM"))
	end
end

--- 此处为自动生成
function Bag_Panel:on_Resolve_ExitCom_ClickEvent()
	Game.BagSystem.bResolveUI = false
	
	self:Change2NormalPage()
	
	---退出仓库分解界面
	UI.Invoke("BagWarehouse_Panel", 'SwitchResolveAndHomePageInit')
end

--- 此处为自动生成
function Bag_Panel:on_Resolve_QuickCom_ClickEvent()
	local bagResolveServerDict = self:GetResolveServerData()
	local warehouseResolveDict, warehouseTabId = self:GetWarehouseResolveServerData()
	
	Game.BagSystem:BatchResolve(self:GetCurTabId(), bagResolveServerDict, warehouseTabId, warehouseResolveDict)
end

---@private 更新排序的样式
function Bag_Panel:UpdateRankAbleStyle()
	if self.bOrderAsc then
		self.view.RankAble:Event_UI_Style(Game.BagSystem.SwitchBtnType.Order_Asc)
	else
		self.view.RankAble:Event_UI_Style(Game.BagSystem.SwitchBtnType.Order_Desc)
	end
end

--- 此处为自动生成
function Bag_Panel:on_RankAbleBtn_ClickArea_Clicked()
	self.bOrderAsc = not self.bOrderAsc
	
	self:UpdateRankAbleStyle()
	self:UpdateSortParam()
	self:RecordResolveItemsStatus()
	self:UpdateItemTree()

	--- 播放点击动效
	self:PlayAnimation(self.view.RankAble.Ani_Press_filter_pingpang, nil, self.view.RankAble, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end


--- 此处为自动生成
function Bag_Panel:on_AutoDecomposeBtnBtn_ClickArea_Clicked()
	local invId = self:GetCurTabId()
	local invData = Game.TableData.GetInventoryDataRow(invId)
	if invData and invData.AutoDecomposeRule then
		UI.ShowUI("BagAutoDecomposeSetting_Panel", invId, invData.AutoDecomposeRule)
	end
end

function Bag_Panel:OnUIOpen()
	if Game.NewUIManager:IsFullScreenUIOpen() then
		self:CloseSelf()
	end
end

---@private 当前背包有道具增加或者删除时
---@param addList string[] 增加的物品gbid列表
---@param sourceID number 来源ID
function Bag_Panel:OnBagItemAddOrDelete(addList, removeList)
	self:UpdateTabInfo()

	-- 如果有筛选项,或者增加物品，那么需要重新构建树
	if self.SortParam or (addList and next(addList)) then
		self:UpdateItemTree()
	end
end

--- 此处为自动生成
---@param index number
---@param data UITreeViewChildData
---@param expanded bool
function Bag_Panel:on_SubTabTreeCom_ItemExpansionChanged(index, data, expanded)
	local path = self.SubTabTreeCom:PackIndexToArray(index)
	if path:Num() > 1 then return end

	local invId = self.InvPrimaryList[self.TabIndexSelected]
	local tabData = Game.TableData.GetInventoryDataRow(invId)
	if not tabData.primaryType then return end
	
	local selSubIndex = path:Get(0) + 1
	if selSubIndex == self.SubTabIndexSelected then
		if not expanded then
			self:ResetSubTabSelect()
		end
		return
	end
	
	self.SubTabIndexSelected = selSubIndex

	self.SubTitleParam.InvID = self:GetCurShowInvID()
	self.SubTitleParam.ShowTitle = false
	
	self.SubTitleShowCom:Refresh(self.SubTitleParam)
	
	self:Change2NormalPage()
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function Bag_Panel:on_SubTabTreeCom_GetEntryLuaClass(index)
	local data = self.SubTabTreeCom:getDataByIndex(index)
	return data.tabData.LuaClass
end

--- 更新当前页面格子库存数（物品数/当前已解锁的格子总数）
function Bag_Panel:UpdateBagPageInventory()
	self:UpdateBagNumInfo()
end

--- tip关闭时，取消选中
function Bag_Panel:CancelSelected()
	if not Game.BagSystem.bResolveUI then
		self.SubTabTreeCom:ClearSelection()
	end
end

---@private 通过索引获取物品数据
---@param index number 树索引
---@return BagTree_Item_Param, SlotInfo 物品数据，槽位信息
function Bag_Panel:GetItemDataByIndex(index)
	local data = self.SubTabTreeCom:getDataByIndex(index)
	---@type BagTree_Item_Param
	local param = data.tabData
	local slotInfo = param.SlotInfo
	
	return param, slotInfo
end

function Bag_Panel:OnClickItem(index)
	if not Game.BagSystem.bResolveUI then
		local param, slotInfo = self:GetItemDataByIndex(index)
		if slotInfo and slotInfo.isNewTip then
			Game.BagSystem.sender:ReqChangeSlotTipType(param.InvID, slotInfo.index, false)
		elseif param.IsLocked then
			Game.BagSystem:BagUnlockSlot(self:GetCurTabId())
		end
		
		if not self.SubTabTreeCom:IsSelectedByIndex(index) then
			UI.HideUI("BagItemTips_Panel")
		end
	end
end

---@public 被双击
function Bag_Panel:OnDoubleClickItem(index)
	if Game.BagSystem.bResolveUI then return end
	if not Game.WarehouseSystem.bWarehouseUI then return end

	local param, slotInfo = self:GetItemDataByIndex(index)
	if not slotInfo then return end

	local itemId = slotInfo.itemId
	local itemData = Game.TableData.GetItemNewDataRow(itemId)
	if itemData.type == Game.BagSystem.ItemType.SEALED_ITEM then
		if SlotInfo.sealedPropInfo.isEquip then
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SEALED_IS_EQUIP, {}) -- luacheck:ignore
			return
		end
	end
	
	Game.BagSystem:SaveToWareHouse(param.InvID, slotInfo)
	UI.HideUI('BagItemTips_Panel', true)
end

---@public 长按
function Bag_Panel:OnLongPressItem(index)
	local param, slotInfo = self:GetItemDataByIndex(index)
	if not Game.BagSystem.bResolveUI or not slotInfo then return end
	
	Game.TabClose:IgnoreNextMouseUpEvent("Bag_Panel:OnLongPressItem")
	Game.BagSystem:ShowItemTips(param.InvId, slotInfo, false, param.ItemFrom)
end

---@public 右击
function Bag_Panel:OnRightClickItem(index)
	local param, slotInfo = self:GetItemDataByIndex(index)
	if not slotInfo then return end
	
	local invId = param.InvID
	local itemId = slotInfo.itemId
	
	---新物品提示
	if slotInfo.isNewTip then
		Game.BagSystem.sender:ReqChangeSlotTipType(invId, slotInfo.index, false)
	end

	local itemExcelData = Game.TableData.GetItemNewDataRow(itemId)
	if itemExcelData and itemExcelData.type == Game.BagSystem.ItemType.EQUIPMENT then
		---装备
		Game.EquipmentSystem:Equip(invId, slotInfo, nil)
	elseif itemExcelData and itemExcelData.type == Game.BagSystem.ItemType.SEALED_ITEM then
		---封印物
		-- if slotInfo.sealedPropInfo.isEquip then
		-- 	return
		-- end
		-- Game.SealedSystem:QuickEquip(itemExcelData.invId, slotInfo)
	else
		---使用
		local itemOperationData = Game.TableData.GetItemOperationTypeDataRow(itemId)
		if itemOperationData and itemOperationData.canBatchUse and slotInfo.count > 1 then
			UI.ShowUI('BagMultiUse_Panel', invId, slotInfo, slotInfo.count)
		else
			Game.BagSystem:ReqUse(itemId, invId, slotInfo.index, slotInfo.gbId, 1)
		end
	end
end

--- 此处为自动生成
---@param index number
---@param selected bool
function Bag_Panel:on_CheckBoxListCom_ItemSelectionChanged(index, selected)
	if Game.BagSystem.bResolveUI then
		self:OnResolveQuilitySelectionChanged(index, selected)
	else
		self:OnFilterSelectionChanged(index, selected)
	end
end

---@private 记录一下分解的选中物
function Bag_Panel:RecordResolveItemsStatus()
	if Game.BagSystem.bResolveUI then
		-- 分解模式下需要记录选项
		table.clear(self.LastSelectResolveItemSet)
		for _, itemIdx in ipairs(self.SubTabTreeCom:GetSelectedItemIndexes()) do
			---@type BagTree_Item_Param
			local itemData = self.SubTabTreeCom:getDataByIndex(itemIdx).tabData
			if itemData.SlotInfo then
				self.LastSelectResolveItemSet[itemData.SlotInfo.index] = true
			end
		end
	end
end

--- 此处为自动生成
---@param index number
---@param data UITabData
function Bag_Panel:on_WBP_ComComBoxCom_ItemSelected(index, data)
	self:OnSelectedComBox(index, data)
	self:RecordResolveItemsStatus()
	self:UpdateItemTree()
end


---@function 初始化背包页签数据
function Bag_Panel:InitInventoryData()
	self:InitPrimaryTabList()
end

--- 获取背包当前TabId
function Bag_Panel:GetCurTabId()
	local tabId = self.InvPrimaryList[self.TabIndexSelected]
	return tabId
end

---@public 根据invId获取对应页签的index
function Bag_Panel:GetTabIndexByInvId(invId)
	for index, tabId in ksbcpairs(self.InvPrimaryList) do
		if invId == tabId then
			return index
		end
	end
end

function Bag_Panel:InitPrimaryTabList()
	
	local invPrimaryList, invSubMap, invFilterData = Game.BagSystem:PrepareInventoryData()

	---@type number[]
	self.InvPrimaryList = invPrimaryList
	---@type table<number, number[]>
	self.InvSubMap = invSubMap
	---@type table<number, number[]>
	self.InvFilterData = invFilterData
	
	
	---@type Primary_UITabData[]
	self.InvPrimaryTabListData = {}
	for _, invID in ipairs(self.InvPrimaryList) do
		local invData = Game.TableData.GetInventoryDataRow(invID)
		---@type Primary_UITabData
		local item = {
			name = invData.bagName,
			InvID = invID,
		}

		table.insert(self.InvPrimaryTabListData, item)
	end
end

--- 批量分解成功的回调
function Bag_Panel:OnDecomposeBatchItems()
	---刷新仓库界面
	UI.Invoke('BagWarehouse_Panel', 'UpdateResolveTabPage')

	if Game.BagSystem.bResolveUI then
		self:UpdateResolveProductPreview()
	end
	--
	self:UpdateItemTree()
end

--- 物品功能触发刷新
function Bag_Panel:OnRefreshItemList()
	if not Game.BagSystem.bResolveUI then
		self:UpdateItemTree()
	end
end

---整理触发刷新
function Bag_Panel:OnArrangeItemList()
	if not Game.BagSystem.bResolveUI then
		self:UpdateItemTree()
	end
end

---@private 获取当前背包ID
---@return number
function Bag_Panel:GetCurShowInvID()
	local invID = self.InvPrimaryList[self.TabIndexSelected]
	if not invID then return -1 end
	
	local tabInfo = Game.TableData.GetInventoryDataRow(invID)
	if tabInfo.primaryType then
		if self.SubTabIndexSelected <= 0 then return -1 end

		local tabId = self:GetCurTabId()
		local subTabId = self.InvSubMap[tabId][self.SubTabIndexSelected]

		return subTabId
	else
		return tabInfo.ID
	end
end

---刷新单个物品
function Bag_Panel:RefreshItem(invId, slotIndex)
	local nowInvID = self:GetCurShowInvID()
	if nowInvID == invId then
		local treeIdx = self.SlotIdx2TreeIdxMap[slotIndex]
		if treeIdx and treeIdx > 0 then
			local data = self.SubTabTreeCom:getDataByIndex(treeIdx)
			---@type BagTree_Item_Param
			local param = data and data.tabData
			param.Dirty = true -- 标记为脏数据
			self.SubTabTreeCom:RefreshItemByIndex(treeIdx)
		else
			-- 暂时粗暴一点，后续优化
			self:UpdateItemTree()
		end
	end
end

---刷新多个物品
function Bag_Panel:RefreshItems(invId, slots)
	for slot, _ in ksbcpairs(slots) do
		self:RefreshItem(invId, slot)
	end
end

---@private 更新道具树信息
function Bag_Panel:UpdateItemTree()
	if self.ShowType == Bag_Panel.BagShowType.Normal then
		self:UpdateNormalItemTree()
	elseif self.ShowType == Bag_Panel.BagShowType.Filter then
		self:UpdateFilterItemTree()
	elseif self.ShowType == Bag_Panel.BagShowType.Resolve then
		self:UpdateResolveItemTree()
	end
	self:UpdateBagNumInfo()
end

---@private 更新普通背包的树
function Bag_Panel:UpdateNormalItemTree()
	self.TreeViewData = UITreeView.NewTreeViewData()
	local invId = self:GetCurShowInvID()
	self.SlotIdx2TreeIdxMap = Game.BagSystem:CreateUITreeByInvId(self, self.TreeViewData, invId, self.SortParam)
	self.SubTabTreeCom:Refresh(self.TreeViewData)
	
	self.SubTabTreeCom:SetSelectionMode(ESelectionMode.SingleToggle)
	local invId = self:GetCurShowInvID()
	local tabData = Game.TableData.GetInventoryDataRow(invId)
	if tabData.primaryType then
		if self.SubTabIndexSelected > 0 and not self.SubTabTreeCom:IsItemExpandedByPath(self.SubTabIndexSelected) then
			self.SubTabTreeCom:SetExpansionItemByPath(true, self.SubTabIndexSelected)
		elseif self.SubTabIndexSelected <= 0 then
			self.SubTabTreeCom:CollapseAll()
		end
	else
		self.SubTabTreeCom:ExpandAll()
		--self.SubTabTreeCom:SetScrollOffset(0)
		Game.BagSystem:SetNewItemTipDict()
		if self.IsFirstUpdateItemTree then
			self.IsFirstUpdateItemTree = false
			self:ScrollToFirstNewItem()
		end
	end
end

---@private 更新过滤后的背包
function Bag_Panel:UpdateFilterItemTree()
	local invId = self:GetCurTabId()
	-- 创建树数据
	self.TreeViewData = UITreeView.NewTreeViewData()
	self.FilterOccupySlot, self.SlotIdx2TreeIdxMap = Game.BagSystem:CreateUITreeByFilterId(self, self.TreeViewData, invId, self.FilterID, self.SortParam)
	-- 更新树数据
	self.SubTabTreeCom:Refresh(self.TreeViewData)
	self.SubTabTreeCom:ClearSelection()
	self.SubTabTreeCom:ExpandAll()
end

function Bag_Panel:UpdateAllCommonInfo()
	self:UpdateTabInfo()
	self:UpdateBottomBtnInfo()
	self:UpdateFilterCombListInfo()
	self:UpdateSortFun()
	self:UpdateGroupInfo()
end

---@private 更新filter
function Bag_Panel:UpdateFilterCombListInfo()
	if self.ShowType == Bag_Panel.BagShowType.Normal then
		self:UpdateFilterCombList()
	elseif self.ShowType == Bag_Panel.BagShowType.Resolve then
		self:UpdateResolveCombList()
	end
end

---@private 更新分解筛选界面的checkbox列表数据
function Bag_Panel:UpdateResolveCombList()
	if not self.ResolveTabIndexSelected then
		return
	end
	
	table.clear(self.CheckQualityMap)

	local decomposeTabId = self:GetCurDecomposeId()
	self.AllQualityIndex = Game.BagSystem:CreateResolveQualityFilter4UI(self.TabDecomposeSelectRarities, decomposeTabId)
	self.CheckBoxListCom:Refresh(self.TabDecomposeSelectRarities)
end

---@private 更新背包筛选界面的checkbox列表数据
function Bag_Panel:UpdateFilterCombList()
	local tabId = self:GetCurTabId()
	local filterList = self.InvFilterData[tabId]
	Game.BagSystem:CreateBagFilterUIListData(self.FilterDatas, filterList)
	self.CheckBoxListCom:Refresh(self.FilterDatas)
end

---@private 跟进当前分解页签创建tab列表数据
function Bag_Panel:CreateDecomposeTabListData()
	table.clear(self.DecomposeTabListData)

	local tabId = self:GetCurTabId()
	local decomposeTabList = self.ResolveTabMap[tabId]
	assert(decomposeTabList, "Please configure the decompose list in the inventory")
	for _, decomposeTabId in ksbcipairs(decomposeTabList) do
		local decomposeData = Game.TableData.GetDecomposeDataRow(decomposeTabId)
		---@type Decompose_UITabData
		local item = {
			name = decomposeData.decomposeInvName,
			DecomposeID = decomposeTabId,
		}
		table.insert(self.DecomposeTabListData, item)
	end
end

---@public 更新左边tab栏
function Bag_Panel:UpdateTabInfo()
	local selectIndex
	local tabListData
	if Game.BagSystem.bResolveUI then
		self:CreateDecomposeTabListData()
		selectIndex = self.ResolveTabIndexSelected
		tabListData = self.DecomposeTabListData
	else
		selectIndex = self.TabIndexSelected
		tabListData = self.InvPrimaryTabListData
	end

	if self.TabListCom:GetData() ~= tabListData then
		self.TabListCom:Refresh(tabListData)
	else
		for i = 1, #tabListData do
			local item = self.TabListCom:GetItemByIndex(i)
			if item then
				item:RefreshNewAnimation()
			end
		end
	end
	self.TabListCom:SetSelectedItemByIndex(selectIndex, true, true)
end

---@private 更细底部按钮状态
function Bag_Panel:UpdateBottomBtnInfo()
	local invID = self.InvPrimaryList[self.TabIndexSelected]
	if not invID then return end
	
	local tabData = Game.TableData.GetInventoryDataRow(invID)

	--仓库准入许可
	self.Warehouse_BtnCom:SetVisible(not Game.WarehouseSystem.bWarehouseUI and tabData.warehouseAble)

	local resolveBtnVis = false
	local arrangeBtnVis = true

	if tabData.primaryType then
		local subTabIndexSel = self.SubTabIndexSelected
		if subTabIndexSel == -1 or not self.SubTabTreeCom:IsItemExpandedByPath(subTabIndexSel) then
			resolveBtnVis = false
			arrangeBtnVis = false
		else
			local tabId = self:GetCurTabId()
			local subTabId = self.InvSubMap[tabId][subTabIndexSel]
			local subTabData = Game.TableData.GetInventoryDataRow(subTabId)
			local isSubTabDecompose = subTabData and subTabData.isDecompose

			resolveBtnVis = isSubTabDecompose
			arrangeBtnVis = true
		end
	else
		resolveBtnVis = tabData.isDecompose
	end

	self.Resolve_BtnCom:SetVisible(resolveBtnVis)
	self.Arrange_BtnCom:SetVisible(arrangeBtnVis)

	self:UpdateAutoDecomposeBtn()
end

---@private 更新背包数量信息
function Bag_Panel:UpdateBagNumInfo()
	local invID = self.InvPrimaryList[self.TabIndexSelected]
	local tabData = Game.TableData.GetInventoryDataRow(invID)
	if tabData.primaryType then
		self.view.RText_GridNum:SetText("")
		return
	end

	local invData = Game.BagSystem:GetInvIdInfo(tabData.ID)
	if not invData then return end

	local allSlot = 0
	local occupySlot = 0

	allSlot = invData.slotNumber
	if self.FilterID > 0 then
		occupySlot = self.FilterOccupySlot or 0
	else
		occupySlot = Game.BagSystem:CountUsedSlots(tabData.ID)
	end

	local countText
	if allSlot == occupySlot then
		countText = "<Disable>"..occupySlot.."</>"
	elseif (allSlot - occupySlot) <= Game.BagSystem.SINGLE_LINE_MAX_SLOT_NUM then
		countText = "<Yellow>"..occupySlot.."</>"
	else
		countText = "<Default>"..occupySlot.."</>"
	end
	countText = countText.."/"..allSlot
	self.view.RText_GridNum:SetText(countText)
end

-------------------------------------------------分解----------------------------------------------------------------------
function Bag_Panel:InitResolveData()
	---@type Decompose_ComTabListR_Param[]
	self.DecomposeTabListData = {}

	--- 批量分解界面 - 分解页签映射
	---@type table<number, number[]>
	self.ResolveTabMap = {}
	local inventoryData = Game.TableData.GetInventoryDataTable()
	for invId, invData in ksbcpairs(inventoryData) do
		if invData.decompose and ksbcpairs(invData.decompose) then
			self.ResolveTabMap[invId] = invData.decompose
		end
	end

	self.FinalResult = {}
	self.ResolveTabIndexSelected = 1
end

---@private 更新分解道具树
function Bag_Panel:UpdateResolveItemTree()
	self.TreeViewData = UITreeView.NewTreeViewData()
	local invId = self:GetCurTabId()
	local decomposeTabId = self:GetCurDecomposeId()
	self.SlotIdx2TreeIdxMap = Game.BagSystem:CreateResolveTree4UI(self, self.TreeViewData, invId, decomposeTabId, self.SortParam)
	self.SubTabTreeCom:Refresh(self.TreeViewData)
	self.SubTabTreeCom:ExpandAll()

	self.SubTabTreeCom:ClearSelection()
	self.SubTabTreeCom:ScrollToItemByPath(0, 1)
	
	self:ResetResolveItemsState()
end

--- 自动分解按钮状态
function Bag_Panel:UpdateAutoDecomposeBtn()
	if not Game.me then return end
	
	local invId = self:GetCurTabId()
	local invData = Game.TableData.GetInventoryDataRow(invId)
	
	local lvMeet = Game.me.Level >= Game.TableData.GetConstDataRow("AUTO_DECOMPOSE_UNLOCK_LEVEL")
	if lvMeet and invData and invData.AutoDecomposeRule then
		self.view.AutoDecomposeBtn:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self:UpdateAutoDecomposeOpenSwitch()
	else
		self.view.AutoDecomposeBtn:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function Bag_Panel:OnLevelChanged()
	self:UpdateAutoDecomposeBtn()
end

---更新自动分解按钮打开或者关闭
function Bag_Panel:UpdateAutoDecomposeOpenSwitch()
	local autoDecomposeBtn = self.view.AutoDecomposeBtn
	
	local invId = self:GetCurTabId()
	local decomposeConfig = Game.me and Game.me.decomposeConfigs[invId]
	local bOpen = decomposeConfig and decomposeConfig.bAutoDecompose or false
	
	if bOpen then
		autoDecomposeBtn:Event_UI_Style(Game.BagSystem.SwitchBtnType.AutoDecomposeOpen)
		self:PlayAnimation(autoDecomposeBtn.Ani_fenjie_loop, nil, autoDecomposeBtn, 0, 0)
	else
		autoDecomposeBtn:Event_UI_Style(Game.BagSystem.SwitchBtnType.AutoDecomposeClose)
		self:StopAllAnimations(autoDecomposeBtn)
	end
end

function Bag_Panel:GetCurDecomposeId()
	local tabId = self:GetCurTabId()
	local decomposeTabId = self.ResolveTabMap[tabId][self.ResolveTabIndexSelected]
	return decomposeTabId
end

function Bag_Panel:OnResolveQuilitySelectionChanged(index, selected)
	
	if index <= 0 then return end

	local nowParam = self.TabDecomposeSelectRarities[index]
	local quality = nowParam.QualityId
	local oldSelected = self.CheckQualityMap[quality] or false
	if oldSelected == selected then return end

	self.CheckQualityMap[quality] = selected
	
	local isAll = quality == 0

	if isAll then--如果是全选的修改，需要同时修改其他的选项
		-- 修改所有的选中状态
		for idx, chkParam in ipairs(self.TabDecomposeSelectRarities) do
			self.CheckQualityMap[chkParam.QualityId] = selected
			self.CheckBoxListCom:SetSelectedItemByIndex(idx, selected)
		end
	elseif self.AllQualityIndex and self.AllQualityIndex > 0 then -- 如果有全选项，需要把全选项的状态同步修改
		local isAllSelected

		for idx, chkParam in ipairs(self.TabDecomposeSelectRarities) do
			if idx ~= self.AllQualityIndex and not self.CheckQualityMap[chkParam.QualityId] then
				isAllSelected = false
				break
			end
		end

		if isAllSelected ~= nil and self.CheckQualityMap[0] ~= isAllSelected then
			self.CheckQualityMap[0] = isAllSelected
			isAll = true
			self.CheckBoxListCom:SetSelectedItemByIndex(self.AllQualityIndex, isAllSelected)
		end
	end
	
	self:RefreshResolveItemsStatus(isAll, quality)
end

---@private 刷新分解物品的状态
---@param isAll boolean 是否是全选
---@param quality number 选中的品质
---@return void
function Bag_Panel:RefreshResolveItemsStatus(isAll, quality)
	-- 勾选当前分类的所有可选项
	self.ForbidUpdateResolveResult = true -- 性能
	local equipSlotScore = Game.EquipmentSystem:GetScoreTable()
	local chkMap = self.CheckQualityMap
	self.TreeViewData:ForeachPathChildren(function(idx, itemParam)
		if not itemParam then return end

		local slotInfo = itemParam.SlotInfo
		local needUpdate = slotInfo and (isAll or slotInfo.quality == quality)
		if  needUpdate and Game.BagSystem:CheckEquipIsBetterOrUpgrade(slotInfo, equipSlotScore) then
			local checked = chkMap[slotInfo.quality] or chkMap[0] or false
			self.SubTabTreeCom:SetSelectedItemByPath(checked, 1, idx)
		end
	end, 1)


	if Game.WarehouseSystem.bWarehouseUI then
		UI.Invoke("BagWarehouse_Panel", "OnClickQualityOption", chkMap, quality)
	end
	self.ForbidUpdateResolveResult = false -- 性能

	self:UpdateResolveProductPreview()
end

---@private 重新设置分解状态
function Bag_Panel:ResetResolveItemsState()
	if not next(self.LastSelectResolveItemSet) then return end
	
	self.ForbidUpdateResolveResult = true -- 性能
	self.TreeViewData:ForeachPathChildren(function(idx, itemParam)
		if not itemParam then return end
		
		local slotInfo = itemParam.SlotInfo
		local needChecked = slotInfo and self.LastSelectResolveItemSet[slotInfo.index]
		if needChecked then
			self.SubTabTreeCom:SetSelectedItemByPath(needChecked, 1, idx)
		end
	end, 1)
	self.ForbidUpdateResolveResult = false -- 性能

	table.clear(self.LastSelectResolveItemSet)
	self:UpdateResolveProductPreview()
end

---@private 获取本界面的分解材料
function Bag_Panel:GetResolveSelectedInfo()
	local bagResolveItemParams = {}
	for _, index in ipairs(self.SubTabTreeCom:GetSelectedItemIndexes()) do
		local data = self.SubTabTreeCom:getDataByIndex(index)
		table.insert(bagResolveItemParams, data.tabData)
	end
	return bagResolveItemParams
end

---@private 获取仓库的分解材料
function Bag_Panel:GetWarehouseResolveSelectedInfo()
	if not Game.WarehouseSystem.bWarehouseUI then return nil end

	---@type BagWarehouse_Panel
	local warehousePanel = UI.GetUI("BagWarehouse_Panel")
	if not warehousePanel then return nil end

	return warehousePanel:GetResolveSelectedInfo()
end

--- 刷新分解产物预览
function Bag_Panel:UpdateResolveProductPreview()
	if self.ForbidUpdateResolveResult then return end
	local bagResolveItemParamList = self:GetResolveSelectedInfo()
	local warehouseResolveParamList = self:GetWarehouseResolveSelectedInfo()

	---@type
	self.FinalResult = Game.BagSystem:CalcResolveProductListToUI(bagResolveItemParamList, warehouseResolveParamList)
	self.ResolveProductListCom:Refresh(self.FinalResult)
end

---@private 获取服务器需要的分解数据
---@return table<string,number>
function Bag_Panel:GetResolveServerData()
	---@type table<number, string> 分解信息
	local resolveServerDict = {}
	for _, index in ipairs(self.SubTabTreeCom:GetSelectedItemIndexes()) do
		local data = self.SubTabTreeCom:getDataByIndex(index)
		local slotInfo = data.tabData and data.tabData.SlotInfo
		if slotInfo then
			resolveServerDict[slotInfo.index] = slotInfo.gbId
		end
	end
	return resolveServerDict
end

---@private 获取仓库对应服务器需要的分解数据
---@return table<string,number>, number
function Bag_Panel:GetWarehouseResolveServerData()
	local warehousePanel = UI.GetUI('BagWarehouse_Panel')
	local warehouseResolveDict, warehouseTabId
	if warehousePanel then
		warehouseResolveDict, warehouseTabId = warehousePanel:GetWarehouseSelectedResolveResult()
	else
		warehouseResolveDict, warehouseTabId = {}, 0
	end

	return warehouseResolveDict, warehouseTabId
end


--- 此处为自动生成
---@param path array
---@return number
function Bag_Panel:on_SubTabTreeCom_GetEntryClassIndexForItem(path)
	return path:Num() - 1
end

--- 此处为自动生成
---@param index number
---@param data table
function Bag_Panel:on_TabListCom_ItemSelected(index, data)
	local selIndex = index
	local curIndex = self.ShowType == Bag_Panel.BagShowType.Normal and self.TabIndexSelected or self.ResolveTabIndexSelected

	if curIndex == selIndex then return end

	--- 清理筛选项的状态
	self:ResetFilterList()

	if self.ShowType == Bag_Panel.BagShowType.Resolve then
		self.ResolveTabIndexSelected = selIndex
		---刷新仓库界面
		UI.Invoke('BagWarehouse_Panel', 'UpdateResolveTabPage')
		self:Change2ResolvePage()
	else
		self.TabIndexSelected = selIndex
		self:Change2NormalPage()
	end
end


--- 此处为自动生成
---@param index number
---@param data UITreeViewChildData
---@param bSelect bool
function Bag_Panel:on_SubTabTreeCom_ItemSelectionChanged(index, data, bSelect)
	if Game.BagSystem.bResolveUI then
		self:UpdateResolveProductPreview()
	end
end


---@private 是否显示的是primaryType类型多级背包
---@return boolean
function Bag_Panel:IsShowPrimaryType()
	local invID = self.InvPrimaryList[self.TabIndexSelected]
	local tabData = Game.TableData.GetInventoryDataRow(invID)
	return tabData.primaryType
end

---@private 刷新主标题显示
---@param b boolean 是否显示子标题作为主标题
function Bag_Panel:ShowSubTitle(b)
	local invID = self.InvPrimaryList[self.TabIndexSelected]
	local tabData = Game.TableData.GetInventoryDataRow(invID)
	if not tabData.primaryType then -- 如果无子项，那么需要显示主标题
		local isInResolve = self.ShowType == Bag_Panel.BagShowType.Resolve
		self.view.Hori_BagNum:SetVisibility(isInResolve and ESlateVisibility.Collapsed or ESlateVisibility.selfHitTestInvisible)
		self.SubTitleParam.ShowTitle = false
	elseif b then
		self.view.Hori_BagNum:SetVisibility(ESlateVisibility.Collapsed)
		self.SubTitleParam.ShowTitle = true
	else
		self.SubTitleParam.ShowTitle = false
		self.view.Hori_BagNum:SetVisibility(ESlateVisibility.selfHitTestInvisible)
	end
	
	self.SubTitleShowCom:Refresh(self.SubTitleParam)
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function Bag_Panel:on_SubTabTreeCom_FrontEdgeIntersectionListItemChanged(index)
	self:ShowSubTitle(self:IsShowPrimaryType() and index < 0)
end


--- 此处为自动生成
function Bag_Panel:on_SubTitleShowCom_ClickEvent()
	local invID = self.InvPrimaryList[self.TabIndexSelected]
	local tabData = Game.TableData.GetInventoryDataRow(invID)
	if not tabData.primaryType and self.SubTabIndexSelected >= 0 then return end
	
	self.SubTabTreeCom:SetExpansionItemByPath(false, self.SubTabIndexSelected)
end

---@private
function Bag_Panel:ResetSubTabSelect()
	self.SubTabIndexSelected = -1
	self:UpdateBottomBtnInfo()
	self:ShowSubTitle(false)
end

function Bag_Panel:UpdateBoundRectWidgets()
    local view = self.view
    local widget = self.widget
    widget:AddPanelRegionWidget(view.Canv_R)

    if Game.WarehouseSystem.bWarehouseUI then
        widget:AddPanelRegionWidget(view.Storage)
    end
end

--- 此处为自动生成
---@param index number
---@return UIComponent
function Bag_Panel:on_TabListCom_GetEntryLuaClass(index)
	return BagTab_Item
end

return Bag_Panel
