local GameplayStatics = import("GameplayStatics")
local ESlateVisibility = import("ESlateVisibility")
local EInputEvent = import("EInputEvent")

local StringConst = kg_require "Data.Config.StringConst.StringConst"

local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local KeyPrompt = kg_require("Gameplay.LogicSystem.HUD.HUDSkill.KeyPrompt")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class SkillTipsSimple_Panel : UIPanel
---@field view SkillTipsSimple_PanelBlueprint
local SkillTipsSimple_Panel = DefineClass("SkillTipsSimple_Panel", UIPanel)

SkillTipsSimple_Panel.eventBindMap = {
	[EEventTypesV2.ROLE_ACTION_INPUT_EVENT] = "OnRoleActionInput",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SkillTipsSimple_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SkillTipsSimple_Panel:InitUIData()
	self.SkillID = nil
	self.TagListData = {}
end

--- UI组件初始化，此处为自动生成
function SkillTipsSimple_Panel:InitUIComponent()
    ---@type UIListView
    self.ListView_TagCom = self:CreateComponent(self.view.ListView_Tag, UIListView)
    ---@type KeyPrompt
    self.WBP_KeyPromptCom = self:CreateComponent(self.view.WBP_KeyPrompt, KeyPrompt)
end

---UI事件在这里注册，此处为自动生成
function SkillTipsSimple_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SkillTipsSimple_Panel:InitUIView()
	if not Game.HUDSystem.IsInPC() then
		self.view.Canvas_KeyContent:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.WBP_KeyPromptCom:Refresh("ToggleSkillSimpleTips")
	end
end

function SkillTipsSimple_Panel:SelfAdaption(showPos)
	self.showPos = showPos
	local slot = self.view.WBP_SkillTipsBg.Slot
	local anchor = import("Anchors")()
	if not showPos then
		anchor.Minimum = FVector2D(0.5, 0.5)
		anchor.Maximum = FVector2D(0.5, 0.5)
		slot:SetAnchors(anchor)
		slot:SetAlignment(FVector2D(0.5, 0.5))
		slot:SetPosition(FVector2D())
		return
	end

	anchor.Minimum = FVector2D(0.0, 0.0)
	anchor.Maximum = FVector2D(0.0, 0.0)
	slot:SetAnchors(anchor)

	slot:SetPosition(FVector2D(10000, 10000))
	self:StartTimer("DelayCalcPos", function()
		local viewportSize = import("WidgetLayoutLibrary").GetViewportSize(_G.GetContextObject())
		local viewportScale = import("WidgetLayoutLibrary").GetViewportScale(_G.GetContextObject())
		viewportSize = viewportSize / viewportScale
		local tipsSize = self.view.WBP_SkillTipsBg:GetDesiredSize()

		local Offset = FVector2D(0.0, 30.0)  -- 往上偏移 30 像素留空
		local FinalPos = showPos - FVector2D(0.0, tipsSize.Y / 2 + Offset.Y)  -- 始终上方显示

		-- 可选：如果右边超出就左移
		if FinalPos.X + tipsSize.X > viewportSize.X then
			FinalPos.X = viewportSize.X - tipsSize.X - 5
		end

		-- 如果左边超出就拉回来
		if FinalPos.X < 0 then
			FinalPos.X = 5
		end

		slot:SetPosition(FinalPos)
	end, 30, 0, true)
end

---面板打开的时候触发
function SkillTipsSimple_Panel:OnRefresh(SkillID, DescText, CDTime, position)
	self.SkillID = SkillID
	
	local PC = GameplayStatics.GetPlayerController(_G.GetContextObject(), 0)
	if PC then
		self.userWidget:SetUserFocus(PC)
	end
	local SkillLvl, SkillExtraLevel = Game.SkillCustomSystem:GetSkillLevel(SkillID)
	local SkillInfo = Game.TableData.GetSkillDataNewRow(SkillID)
	table.clear(self.TagListData)
	if SkillInfo then
		self.view.Text_Name:SetText(
			SkillInfo.Name
		)
		if not string.isEmpty(SkillInfo.SkillIcon) then
			self:SetImage(self.view.SkillIcon, SkillInfo.SkillIcon)
		end
		if CDTime == 0 then
			--self.view.ListView_Tag:SetVisibility(ESlateVisibility.Collapsed)
		elseif CDTime % 1 == 0 then
			self.view.ListView_Tag:SetVisibility(ESlateVisibility.Visible)
			table.insert(self.TagListData, {Time = math.floor(CDTime)})
		else
			table.insert(self.TagListData, {Time = CDTime})
		end
		if SkillInfo.DesTags then
			for key, value in ksbcpairs(SkillInfo.DesTags) do
				table.insert(self.TagListData, { Key = value })
			end
		end
		self.view.Text_Detail:SetText(DescText)

		if SkillExtraLevel == 0 then
			self.view.Text_Level_Num:SetText(
				StringConst.Get("SKILLCUSTOMIZER_LEVEL_FORMAT", SkillLvl)
			)
		else
			self.view.Text_Level_Num:SetText(
				StringConst.Get("SKILLCUSTOMIZER_LEVEL_FORMAT", SkillLvl) .. "+" .. SkillExtraLevel
			)
		end
	end
	self.ListView_TagCom:Refresh(self.TagListData)
	self.view.Text_Key:SetText(StringConst.Get("HUD_SKILL_DESCRIPTION"))
	self:SelfAdaption(position, FVector2D(70, 70))
end


function SkillTipsSimple_Panel:OnClose()
	self.SkillID = nil
end

function SkillTipsSimple_Panel:OnRoleActionInput(ActionName, InputEvent)
	if InputEvent == EInputEvent.IE_Pressed and ActionName == Enum.EInputType.ToggleSkillSimpleTips_Action then
		Game.SkillCustomSystem:ToggleSkillSimpleTips(self.SkillID, self.showPos)
	end
end

return SkillTipsSimple_Panel
