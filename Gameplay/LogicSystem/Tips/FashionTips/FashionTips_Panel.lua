local ItemTipsFashion = kg_require("Gameplay.LogicSystem.Tips.FashionTips.ItemTipsFashion")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local TipsAdapterPlugin = kg_require "Gameplay.LogicSystem.Utils.TipsAdaptPlugin"
local StringConst = kg_require("Data.Config.StringConst.StringConst")

---@class FashionTips_Panel : UIPanel
---@field view DrugTips_PanelBlueprint
local FashionTips_Panel = DefineClass("FashionTips_Panel", UIPanel, TipsAdapterPlugin)

FashionTips_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function FashionTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function FashionTips_Panel:InitUIData()
    ---@type ItemTipsFashionParam 可能未来是类似背包tips的数组？
    self.Param = {TitleParam = { TagList = {} }, NormParam = {}}
end

--- UI组件初始化，此处为自动生成
function FashionTips_Panel:InitUIComponent()
    ---@type ItemTipsFashion
    self.WBP_FashionClothTipsCom = self:CreateComponent(self.view.WBP_FashionClothTips, ItemTipsFashion)
end

---UI事件在这里注册，此处为自动生成
function FashionTips_Panel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function FashionTips_Panel:InitUIView()
end

---面板打开的时候触发
function FashionTips_Panel:OnRefresh(ID, subType, pos)
    local param = self:CraeateTipsParam(ID, subType)
	self.WBP_FashionClothTipsCom:Refresh(param)
	self.userWidget:Event_UI_Style(pos or 0)
end

---@param ID number ID
---@param subType Enum.EAppearanceSubType2ID 子类型
function FashionTips_Panel:CraeateTipsParam(ID, subType)
    local param = self.Param

    param.ID = ID
    param.SubType = subType

    if param.SubType == Enum.EAppearanceSubType2ID.Mount then
        self:InitMountParam()
    else
        self:InitFashionParam()
    end

    return param
end

---@private 初始化坐骑参数
function FashionTips_Panel:InitMountParam()
    local param = self.Param
    local id = param.ID

    local mountConfig = Game.TableData.GetFashionMountDataRow(id)
    param.AchieveDes = mountConfig.AchieveDes or ""
    param.FashionDes = mountConfig.MountDes or ""

    ---@type ItemTipsTitle_Param
    local titleParam = param.TitleParam
    titleParam.WeaponIcon = mountConfig.icon
    titleParam.TextName = mountConfig.MountName
    titleParam.TextNum = ""
    titleParam.TextHand = StringConst.Get("FASHION_TIPS_SCORE")
    titleParam.TextLevel = mountConfig.FashionScore or 0
    titleParam.ShowEquip = false
    titleParam.TagList = self:CreateTagList(nil, titleParam.TagList)

    ---@type ItemTipsNorm_Param
    local normParam = param.NormParam
    normParam.IsShowBtn = false
    normParam.Quality = mountConfig.quality or 0
end

---@private 初始化时装参数
function FashionTips_Panel:InitFashionParam()
    local param = self.Param
    local id = param.ID

    local fashionConfig = Game.TableData.GetFashionDataRow(id)

    param.AchieveDes = fashionConfig.AchieveDes or ""
    param.FashionDes = fashionConfig.FashionDes or ""
    
    ---@type ItemTipsTitle_Param
    local titleParam = param.TitleParam
    titleParam.WeaponIcon = fashionConfig.Icon
    titleParam.TextName = fashionConfig.FashionName
    titleParam.TextNum = ""
    titleParam.TextHand = StringConst.Get("FASHION_TIPS_SCORE")
    titleParam.TextLevel = fashionConfig.FashionScore or 0
    titleParam.ShowEquip = false
    titleParam.TagList = self:CreateTagList(fashionConfig.FashionTag, titleParam.TagList)

    ---@type ItemTipsNorm_Param
    local normParam = param.NormParam
    normParam.IsShowBtn = false
    normParam.Quality = fashionConfig.quality or 0
end

---@public 创建标签列表
---@param tagList number[] 标签列表
---@param toShowTagParam ItemTipsTitleTagParam 需要展示的标签参数
function FashionTips_Panel:CreateTagList(tagList, toShowTagList)
    table.clear(toShowTagList)
    if not tagList then return toShowTagList end

    for i, tagId in ksbcpairs(tagList) do
        local tagConfig = Game.TableData.GetFashionTagDataRow(tagId)
        ---@type ItemTipsTitleTagParam
        table.insert(toShowTagList, {
            Name = tagConfig.Name, 
            Color = tagConfig.Color
        })
    end

    return toShowTagList
end

return FashionTips_Panel
