local UISimpleList = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UISimpleList")
local UIButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIButton")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class ChatTips_Panel : UIPanel
---@field view ChatTipsBlueprint
local ChatTips_Panel = DefineClass("ChatTips_Panel", UIPanel)
local ESlateVisibility = import("ESlateVisibility")
local StringConst = kg_require("Data.Config.StringConst.StringConst")

ChatTips_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ChatTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ChatTips_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function ChatTips_Panel:InitUIComponent()
    ---@type UISimpleList
    self.WrapItem_SimpleListCom = self:CreateComponent(self.view.WrapItem_SimpleList, UISimpleList)
    ---@type UISimpleList
    self.Wrap_ItemCom = self:CreateComponent(self.view.WrapItem_SimpleList, UISimpleList)
    ---@type UIButton
    self.WBP_ChatTipsAccessCom = self:CreateComponent(self.view.WBP_ChatTipsAccess, UIButton)
end

---UI事件在这里注册，此处为自动生成
function ChatTips_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ChatTipsAccessCom.onClickEvent, "on_WBP_ChatTipsAccessCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ChatTips_Panel:InitUIView()
    self.WBP_ChatTipsAccessCom:SetName(StringConst.Get("CHAT_QUEST_VIEW_TASK"))
end

---组件刷新统一入口
function ChatTips_Panel:Refresh(questID)
    self.taskData = Game.QuestSystem:GetTaskExcelCfg(questID)
	self.ringData = Game.QuestSystem:GetRingExcelCfg(self.taskData.RingID)
    
    ---任务名
    self.view.TB_TitleTask:SetText("-"..self.ringData.RingName)
    ---任务questType
    local questType = Game.QuestSystem:GetQuestTypeByQuestID(questID)
    if questType then
        local cfg = Game.TableData.GetTaskMiniTypeDataRow(questType)
        self.view.TB_TitleCity:SetText(cfg.TitleText)
    end
    ---任务状态
    if  Game.QuestSystem:IsQuestFinished(self.taskData.ID) then
        self.view.TB_Type:SetText(StringConst.Get("CHAT_QUEST_STATUS_COMPLETED"))
        self.userWidget:Event_UI_Style(3)
    else
        self.view.TB_Type:SetText(StringConst.Get("CHAT_QUEST_STATUS_NOT_COMPLETED"))
        self.userWidget:Event_UI_Style(0)
    end

	---任务地点
	if self.taskData.SceneText then
		self.view.TB_TaskPlace:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.TB_Word:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.TB_Word:SetText(self.taskData.SceneText or "")
	else
		self.view.TB_TaskPlace:SetVisibility(ESlateVisibility.Collapsed)
		self.view.TB_Word:SetVisibility(ESlateVisibility.Collapsed)
	end

	---奖励
	self.RewardData = {}
    Game.QuestSystem:GetRewardByQuestID(self.taskData.ID, self.RewardData)
    self.WrapItem_SimpleListCom:Refresh(self.RewardData)

    ---任务描述
    local strDes = self.ringData.RingDescription or ""
    self.view.TB_TaskWord:SetText(strDes)
end

--- 此处为自动生成
function ChatTips_Panel:on_WBP_ChatTipsAccessCom_ClickEvent()
    Game.NewUIManager:OpenPanel(UIPanelConfig.TaskBoardPanel, self.taskData.ID)
    self:CloseSelf()
end

return ChatTips_Panel
