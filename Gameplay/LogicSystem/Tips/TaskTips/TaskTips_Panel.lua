local ESlateVisibility = import("ESlateVisibility")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")
---@class TaskTips_Panel : UIPanel
---@field view TaskTips_PanelBlueprint
local TaskTips_Panel = DefineClass("TaskTips_Panel", UIPanel)

TaskTips_Panel.eventBindMap = {
	[EEventTypesV2.QUEST_ON_RECEIVE_NPCTALK_QUEST] = "OnReceiveNpcTalkQuest"
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function TaskTips_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function TaskTips_Panel:InitUIData()
	---@type number @环ID
	self.ringID = nil
	---@type number @NPCID
	self.npcCfgID = nil
	---@type number @对话ID
	self.dialogueID = nil
	---@type number @道具ID
	self.itemID = nil
	---@type table @对话ID
	self.rewardList = {}
	self.rewardView = nil
end

--- UI组件初始化，此处为自动生成
function TaskTips_Panel:InitUIComponent()
    ---@type UIComButton
    self.Btn_CloseCom = self:CreateComponent(self.view.Btn_Close, UIComButton)
    ---@type UIComButton
    self.Btn_AcceptCom = self:CreateComponent(self.view.Btn_Accept, UIComButton)
    ---@type UIListView
    self.Tile_RewardCom = self:CreateComponent(self.view.Tile_Reward, UIListView)
end

---UI事件在这里注册，此处为自动生成
function TaskTips_Panel:InitUIEvent()
    self:AddUIEvent(self.Btn_AcceptCom.onClickEvent, "on_Btn_AcceptCom_ClickEvent")
    self:AddUIEvent(self.Btn_CloseCom.onClickEvent, "on_Btn_CloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function TaskTips_Panel:InitUIView()
	self.Btn_AcceptCom:SetName(StringConst.Get("ACCEPT_TASK"))
end

---检查是否特殊奖励
---@return boolean
function TaskTips_Panel:checkMysteryReward()
	return self.rewardList[1] and self.rewardList[1].IsMystery
end


---面板打开的时候触发
---面板打开的时候触发
function TaskTips_Panel:OnRefresh(ringID, npcCfgID, dialogueID, itemID)
	self.ringID = ringID
	self.npcCfgID = npcCfgID
	self.itemID = itemID
	self.dialogueID = dialogueID
	self:refreshQuestDesc()
	self:refreshRewards()
end

function TaskTips_Panel:refreshQuestDesc()
	---@type QuestSystem
	local QuestSystem = Game.QuestSystem

	local questType = QuestSystem:GetQuestTypeByRingID(self.ringID)
	local miniData = Game.TableData.GetTaskMiniTypeDataRow(questType)
	local titleText = miniData.TitleText
	local firstChar = utf8.sub(titleText, 1, 2)  -- 获取第一个字符
	local remainingChars = utf8.sub(titleText, 2)  -- 获取剩余字符
	local color, _ = QuestSystem:GetTextColorByTaskType(questType)
	self.view.KText_Type_1:SetColorAndOpacity(color)
	self.view.KText_Type_2:SetColorAndOpacity(color)
	self.view.KText_Type_1:SetText(firstChar)
	self.view.KText_Type_2:SetText(remainingChars)
	
	local ringCfg = QuestSystem:GetRingExcelCfg(self.ringID)
	local chapterName = QuestUtils.GetRingChapterName(self.ringID)
	self.view.KText_Title:SetText(chapterName)
	if chapterName == ringCfg.RingName then
		self.view.KText_SubTitle:SetVisibility(ESlateVisibility.Hidden)
	else
		self.view.KText_SubTitle:SetVisibility(ESlateVisibility.selfHitTestInvisible)
		self.view.KText_SubTitle:SetText(string.format("【%s】", ringCfg.RingName))  -- luacheck: ignore
	end
	self.view.KText_QuestWord:SetText(ringCfg.RingDescription)
end

function TaskTips_Panel:refreshRewards()
	table.clear(self.rewardList)
	---@type QuestSystem
	local QuestSystem = Game.QuestSystem
	QuestSystem:GetRewardByQuestID(QuestSystem:GetQuestIDByRingID(self.ringID), self.rewardList)
	if #self.rewardList > 0 then
		self.view.KText_Award:SetVisibility(ESlateVisibility.Visible)
		if self:checkMysteryReward() then
			-- 有神秘奖励
			self.view.Tile_Reward:SetVisibility(ESlateVisibility.Collapsed)
			self.view.WBP_TaskBox:SetVisibility(ESlateVisibility.Visible)
		else
			local rewardListData = {}
			for _, rewardData in ipairs(self.rewardList) do 
				---@type ItemRewardNewParam
				local data = {
					id = rewardData.ItemID,
					num = rewardData.Count
				}
				rewardListData[#rewardListData + 1] = data
			end
			self.Tile_RewardCom:Refresh(rewardListData)
			self.view.Tile_Reward:SetVisibility(ESlateVisibility.Visible)
			self.view.WBP_TaskBox:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		self.view.KText_Award:SetVisibility(ESlateVisibility.Collapsed)
		self.view.WBP_TaskBox:SetVisibility(ESlateVisibility.Collapsed)
		self.view.Tile_Reward:SetVisibility(ESlateVisibility.Collapsed)
	end

	if self.dialogueID or self.itemID then
		self.view.Btn_Accept:SetVisibility(ESlateVisibility.Visible)
	else
		self.view.Btn_Accept:SetVisibility(ESlateVisibility.Collapsed)
	end
end

--- 此处为自动生成
function TaskTips_Panel:on_Btn_AcceptCom_ClickEvent()
	if self.itemID then
		Game.QuestSystem:ReqReceiveItemQuest(self.ringID,  self.itemID)
	elseif self.dialogueID then
		Game.QuestSystem:ReqReceiveNpcTalkQuest(self.ringID, self.npcCfgID, self.dialogueID)
	end
end

--- 此处为自动生成
function TaskTips_Panel:on_Btn_CloseCom_ClickEvent()
	self:CloseSelf()
end

function TaskTips_Panel:OnReceiveNpcTalkQuest(ringID)
	if ringID == self.ringID then
		self:CloseSelf()
	end
end

return TaskTips_Panel
