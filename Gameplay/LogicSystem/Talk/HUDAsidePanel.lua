local ESlateVisibility = import("ESlateVisibility")
local StringConst = require "Data.Config.StringConst.StringConst"
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class HUDAsidePanel : UIPanel
---@field view HUDAsidePanelBlueprint
local HUDAsidePanel = DefineClass("HUDAsidePanel", UIPanel)

HUDAsidePanel.eventBindMap = {
	[EEventTypes.ASIDE_AUDIO_BREAK] = "AudioBreak",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function HUDAsidePanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function HUDAsidePanel:InitUIData()
	self.playIndex = 1
	self.asideID = nil
	self.callback = nil
	-- 画外音音频播放ID
	self.audioPlayingID = nil
end

--- UI组件初始化，此处为自动生成
function HUDAsidePanel:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function HUDAsidePanel:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function HUDAsidePanel:InitUIView()
end

---面板打开的时候触发
function HUDAsidePanel:OnRefresh(params, callback)
	self.view.CanvasPanel_Dialogue_lua:SetVisibility(ESlateVisibility.Collapsed)
	local asideID = params.AsideID
	if asideID then
		self:Play(asideID, callback)
	else
		self.asideID = nil
		self:PlayCustomText(params, callback)
	end
	--TODO:临时解决这个界面与画外音重叠的问题
	if UI.IsShow(UICellConfig.HUDStatusBar) then
		---@type HUDStatusBar
		local P_SkillProgressBar = UI.GetUI(UICellConfig.HUDStatusBar)
		if P_SkillProgressBar then
			P_SkillProgressBar:SetBodySlotPos(true)
		end
	end
end

function HUDAsidePanel:Play(asideID, callback)
	self.asideID = asideID or 0
	if self.callback then
		self.callback()
	end
	self.callback = callback
	self.playIndex = 1
	local asideCfg = Game.TableData.GetAsideTalkDataRow(self.asideID)
	if asideCfg then
		asideCfg = asideCfg[self.playIndex]
	end
	-- self:StopAllTimer()
	self:StopTimer("WaitAside")
	self:StopTimer("PlayAside")
	if asideCfg and asideCfg.Delay > 0 then
		self.view.CanvasPanel_Dialogue_lua:SetVisibility(ESlateVisibility.Collapsed)
		self:StartTimer("WaitAside", function()
			self:startPlay(self.playIndex)
		end, asideCfg.Delay, 1)
	else
		self:startPlay(self.playIndex)
	end
end

function HUDAsidePanel:startPlay(sort)
	self.playIndex = sort
	local asideCfg = Game.TableData.GetAsideTalkDataRow(self.asideID)
	if asideCfg then
		asideCfg = asideCfg[self.playIndex]
	end
	if not asideCfg then
		self:endPlay()
		return
	end

	self:showAsideInfo()
end

function HUDAsidePanel:endPlay()
	if self.callback then
		self.callback()
		self.callback = nil
	end
	self.playIndex = 1
	self.view.CanvasPanel_Dialogue_lua:SetVisibility(ESlateVisibility.Collapsed)
	if self.audioPlayingID then
		Game.AkAudioManager:StopEvent(self.audioPlayingID)
		self.audioPlayingID = nil
	end
	self:CloseSelf()
end

function HUDAsidePanel:showAsideInfo(text, talkerName, duration, voiceAsset, bShowChat, title)
	local isPlayer = false
	if self.asideID then
		local asideCfg = Game.TableData.GetAsideTalkDataRow(self.asideID)[self.playIndex]
		if asideCfg.TalkerName == "-1" then
			if Game.me then
				talkerName = Game.me.Name
				isPlayer = true
			else
				talkerName = ""
			end
		else
			talkerName = asideCfg.TalkerName or ""
		end
		text = asideCfg.Text
		duration = asideCfg.Duration
		voiceAsset = asideCfg.VoiceAsset
		title = asideCfg.TalkerTitle
	end

	if self.audioPlayingID then
		Game.AkAudioManager:StopEvent(self.audioPlayingID)
		self.audioPlayingID = nil
	end

	self.view.CanvasPanel_Dialogue_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self.view.Text_Name_lua:SetText(talkerName)
	self.view.RTB_TalkContent_lua:SetText(Game.NPCManager.GetFormatTalkText(text))
	if voiceAsset and voiceAsset ~= "" then
		local akDuration = Game.AkAudioManager:GetEventDuration(voiceAsset)
		if akDuration > 0 then
			--如果有语音时长，略微延迟0.2秒
			duration = math.max(akDuration*1000+200, duration)
		end

		-- 用Actor来播,用于区分主角性别
		self.audioPlayingID = Game.me:AkPostEventOnActor(voiceAsset)
	end

	if bShowChat then
		local chatText = string.format(StringConst.Get("SOCIAL_CHAT_NPC"), talkerName, text)
		Game.ChatSystem:AddChannelSystemInfo(Game.NPCManager.GetFormatTalkText(chatText), _G._now(), Enum.EChatChannelData["NEARBY"])
	end

	if string.isEmpty(title) then
		self.view.Text_Title_lua:SetVisibility(ESlateVisibility.Collapsed)
	else
		self.view.Text_Title_lua:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self.view.Text_Title_lua:SetText(title)
	end

	if isPlayer then
		self.view.IsSelf = true
	else
		self.view.IsSelf = false
	end
	self.userWidget:SetType(isPlayer)

	self:StartTimer("PlayAside", function()
		self:hideAsideInfo()
	end, duration, 1)
end

function HUDAsidePanel:hideAsideInfo()
	self.view.CanvasPanel_Dialogue_lua:SetVisibility(ESlateVisibility.Collapsed)
	if self.asideID then
		local asideCfg = Game.TableData.GetAsideTalkDataRow(self.asideID)[self.playIndex]
		self:StartTimer("WaitAside", function()
			self:startPlay(self.playIndex + 1)
		end, asideCfg.Delay, 1)
	else
		self:endPlay()
	end
end

function HUDAsidePanel:OnClose()
	self.playIndex = 1
	self.callback = nil
	if self.audioPlayingID then
		Game.AkAudioManager:StopEvent(self.audioPlayingID)
		self.audioPlayingID = nil
	end
	--TODO:临时解决这个界面与画外音重叠的问题
	if UI.IsShow(UICellConfig.HUDStatusBar) then
		---@type HUDStatusBar
		local P_SkillProgressBar = UI.GetUI(UICellConfig.HUDStatusBar)
		if P_SkillProgressBar then
			P_SkillProgressBar:SetBodySlotPos(false)
		end
	end
end

function HUDAsidePanel:AudioBreak()
	if self.audioPlayingID then
		Game.AkAudioManager:StopEvent(self.audioPlayingID)
		self.audioPlayingID = nil
	end
end

-------------------- 自定义Aside start --------------------
function HUDAsidePanel:PlayCustomText(params, callback)
	if self.callback then
		self.callback()
	end
	self.callback = callback
	self:showAsideInfo(params.Text, params.TalkerName, params.Duration, params.VoiceAsset, params.ShowChat, params.TalkerTitle)
end
-------------------- 自定义Aside end --------------------

return HUDAsidePanel
