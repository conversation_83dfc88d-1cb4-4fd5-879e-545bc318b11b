local SceneCustom_Player_Item = kg_require("Gameplay.LogicSystem.SceneCustom.UI.SceneCustom_Player_Item")
local SceneCustom_Btn_Item = kg_require("Gameplay.LogicSystem.SceneCustom.UI.SceneCustom_Btn_Item")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class SceneCustom_Anim_Page : UIComponent
---@field view SceneCustom_Anim_PageBlueprint
local SceneCustom_Anim_Page = DefineClass("SceneCustom_Anim_Page", UIComponent)

SceneCustom_Anim_Page.eventBindMap = {
	[EEventTypesV2.ON_SCENE_CUSTOM_REFRESH_OPERATION] = "RefreshAll",
	[EEventTypesV2.ON_SCENE_CUSTOM_DELETE_ENTITY] = "RefreshPlayer",
	[EEventTypesV2.ON_SCENE_CUSTOM_ADD_ENTITY] = "RefreshPlayer",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SceneCustom_Anim_Page:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SceneCustom_Anim_Page:InitUIData()
	self.maxOtherPlayerNum = Game.TableData.GetSceneCustomConstDataRow("SCENE_CUSTOM_PLAYER_MAX_NUM") - 1
end

--- UI组件初始化，此处为自动生成
function SceneCustom_Anim_Page:InitUIComponent()
    ---@type SceneCustom_Player_Item
    self.WBP_Oneself_ItemCom = self:CreateComponent(self.view.WBP_Oneself_Item, SceneCustom_Player_Item)
    ---@type UIListView childScript: SceneCustom_AddActor_Item
    self.KGListView_PlayerCom = self:CreateComponent(self.view.KGListView_Player, UIListView)
    ---@type SceneCustom_Btn_Item
    self.WBP_Btn_MoveForwardCom = self:CreateComponent(self.view.WBP_Btn_MoveForward, SceneCustom_Btn_Item)
    ---@type SceneCustom_Btn_Item
    self.WBP_Btn_RetreatCom = self:CreateComponent(self.view.WBP_Btn_Retreat, SceneCustom_Btn_Item)
    ---@type UIComButton
    self.WBP_ComBtn_MultiPeopleCom = self:CreateComponent(self.view.WBP_ComBtn_MultiPeople, UIComButton)
    ---@type UIComButton
    self.WBP_ComBtn_SaveCom = self:CreateComponent(self.view.WBP_ComBtn_Save, UIComButton)
    ---@type UIListView childScript: SceneCustom_Actor_Item
    self.KGListView_AnimCom = self:CreateComponent(self.view.KGListView_Anim, UIListView)
end

---UI事件在这里注册，此处为自动生成
function SceneCustom_Anim_Page:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_MultiPeopleCom.onClickEvent, "on_WBP_ComBtn_MultiPeopleCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_SaveCom.onClickEvent, "on_WBP_ComBtn_SaveCom_ClickEvent")
    self:AddUIEvent(self.view.Btn_ClickArea_AddPlayer.OnClicked, "on_Btn_ClickArea_AddPlayer_Clicked")
    self:AddUIEvent(self.KGListView_AnimCom.onItemSelected, "on_KGListView_AnimCom_ItemSelected")
    self:AddUIEvent(self.WBP_Btn_MoveForwardCom.onClickEvent, "on_WBP_Btn_MoveForwardCom_ClickEvent")
    self:AddUIEvent(self.WBP_Btn_RetreatCom.onClickEvent, "on_WBP_Btn_RetreatCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SceneCustom_Anim_Page:InitUIView()
	self.WBP_ComBtn_SaveCom:Refresh(StringConst.Get("SCENE_CUSTOM_SAVE"))

	self.WBP_Btn_MoveForwardCom:Refresh(StringConst.Get("SCENE_CUSTOM_FORWARD"))
	self.WBP_Btn_MoveForwardCom:RefreshUIState(0)
	self.WBP_Btn_RetreatCom:Refresh(StringConst.Get("SCENE_CUSTOM_BACK"))
	self.WBP_Btn_RetreatCom:RefreshUIState(1)
end

---组件刷新统一入口
function SceneCustom_Anim_Page:Refresh()
	self.friendDataList = Game.FriendSystem:GetFriendInfoList()
	
	self:RefreshAll()
end

function SceneCustom_Anim_Page:RefreshAll()
	self:RefreshPlayer()
	self:RefreshButtonList()
end

function SceneCustom_Anim_Page:RefreshPlayer()
	local sceneWrapper = Game.SceneCustomSystem:GetCurSceneWrapper()
	local playerNum = sceneWrapper:GetCurAvatarEntityNum()
	if playerNum > 1 then
		self:RefreshMultiPlayer()
	else
		self:RefreshSinglePlayer()
	end
end

function SceneCustom_Anim_Page:RefreshSinglePlayer()
	self.userWidget:BP_List_Switch(true)
	self.view.RichText_Number:SetText("")
	self.view.RichText_Actor:SetText(StringConst.Get("SCENE_CUSTOM_ANIM_NAME"))

	local animHasList = Game.SceneCustomSystem:GetCurAnimItemListData()
	self.playerDataList = {}
	for _, config in ksbcpairs(Game.TableData.GetSceneCustomListDataTable()) do
		if config.Type == Enum.ESceneCustomTypeEnum.Anim then
			table.insert(self.playerDataList, {
				id = config.ID,
				icon = config.Icon,
				has = animHasList and animHasList[config.ID] ~= nil
			})
		end
	end

	Game.SceneCustomSystem:SortList(self.playerDataList)
	self.KGListView_AnimCom:Refresh(self.playerDataList)

	local selectIndex = nil
	local curAnimID = Game.SceneCustomSystem:GetDisplayEntityAnim()
	for index, data in pairs(self.playerDataList) do
		if data.id == curAnimID then
			selectIndex = index
			break
		end
	end
	if selectIndex then
		self.KGListView_AnimCom:SetSelectedItemByIndex(selectIndex, true, true)
	end
end

function SceneCustom_Anim_Page:RefreshMultiPlayer()
	self.userWidget:BP_List_Switch(false)
	--个数
	self.view.RichText_Number:SetText(string.format("%s/%s", Game.SceneCustomSystem:GetCurSceneEntityAvatarNum(), self.maxOtherPlayerNum))
	self.view.RichText_Actor:SetText(StringConst.Get("SCENE_CUSTOM_ANIM_PLAYER"))

	local sceneWrapper = Game.SceneCustomSystem:GetCurSceneWrapper()
	local mainEntity = sceneWrapper:GetCustomEntityMain()
	
	self.WBP_Oneself_ItemCom:Refresh({
		school = Game.me.Profession, 
		sex = Game.me.Sex,
		uid = mainEntity:uid()
	})

	self.playerDataList = {}
	local showEntityList = sceneWrapper:GetAvatarEntityList()
	for i, entityUID in pairs(showEntityList) do
		local entity = sceneWrapper:GetDisplayEntityByUID(entityUID)
		local data = self:GetFriendDataByEID(entity.CustomSceneEID)
		if data then
			data.uid = entity:uid()
			table.insert(self.playerDataList, data)
		end
	end
	self.KGListView_PlayerCom:Refresh(self.playerDataList)
end

function SceneCustom_Anim_Page:GetFriendDataByEID(eid)
	for i, data in pairs(self.friendDataList) do
		if data.id == eid then
			return data
		end
	end
	
	return nil
end

function SceneCustom_Anim_Page:RefreshButtonListAndSelect()
	self:RefreshButtonList()
	local selectIndex = nil
	local curAnimID = Game.SceneCustomSystem:GetDisplayEntityAnim()
	for index, data in pairs(self.playerDataList) do
		if data.id == curAnimID then
			selectIndex = index
			break
		end
	end
	if selectIndex then
		self.KGListView_AnimCom:SetSelectedItemByIndex(selectIndex, true, true)
	end
end

function SceneCustom_Anim_Page:RefreshButtonList()
	self.WBP_Btn_MoveForwardCom:RefreshUIDisable(not Game.SceneCustomSystem:CanRedo())
	self.WBP_Btn_RetreatCom:RefreshUIDisable(not Game.SceneCustomSystem:CanUndo())
end

--- 多人入画
--- 此处为自动生成
function SceneCustom_Anim_Page:on_WBP_ComBtn_MultiPeopleCom_ClickEvent()
	Game.NewUIManager:OpenPanel(UIPanelConfig.SceneCustom_Friend_Panel)
end

--- 保存
--- 此处为自动生成
function SceneCustom_Anim_Page:on_WBP_ComBtn_SaveCom_ClickEvent()
	Game.SceneCustomSystem:BeginShotAndUploadData()
end


--- 添加player
--- 此处为自动生成
function SceneCustom_Anim_Page:on_Btn_ClickArea_AddPlayer_Clicked()
	Game.NewUIManager:OpenPanel(UIPanelConfig.SceneCustom_Friend_Panel)
end

--- 选中动作
--- 此处为自动生成
---@param index number
---@param data table
function SceneCustom_Anim_Page:on_KGListView_AnimCom_ItemSelected(index, data)
	Game.SceneCustomSystem:PlayDisplayAnim(data.id)
end

--- 前进
--- 此处为自动生成
function SceneCustom_Anim_Page:on_WBP_Btn_MoveForwardCom_ClickEvent()
	Game.SceneCustomSystem:Redo()
end

--- 后退
--- 此处为自动生成
function SceneCustom_Anim_Page:on_WBP_Btn_RetreatCom_ClickEvent()
	Game.SceneCustomSystem:Undo()
end

return SceneCustom_Anim_Page
