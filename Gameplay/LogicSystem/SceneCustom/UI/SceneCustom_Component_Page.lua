local SceneCustom_Btn_Item = kg_require("Gameplay.LogicSystem.SceneCustom.UI.SceneCustom_Btn_Item")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local ESelectionMode = import("ESelectionMode")
---@class SceneCustom_Component_Page : UIComponent
---@field view SceneCustom_Component_PageBlueprint
local SceneCustom_Component_Page = DefineClass("SceneCustom_Component_Page", UIComponent)

SceneCustom_Component_Page.eventBindMap = {
	[EEventTypesV2.ON_SCENE_CUSTOM_DELETE_ENTITY] = "RefreshNumText",
	[EEventTypesV2.ON_SCENE_CUSTOM_REFRESH_OPERATION] = "RefreshAll",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SceneCustom_Component_Page:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SceneCustom_Component_Page:InitUIData()
	self.maxComponentNum = Game.TableData.GetSceneCustomConstDataRow("SCENE_CUSTOM_COMPONENT_MAX_NUM")
	
end

--- UI组件初始化，此处为自动生成
function SceneCustom_Component_Page:InitUIComponent()
    ---@type SceneCustom_Btn_Item
    self.WBP_Btn_MoveForwardCom = self:CreateComponent(self.view.WBP_Btn_MoveForward, SceneCustom_Btn_Item)
    ---@type SceneCustom_Btn_Item
    self.WBP_Btn_RetreatCom = self:CreateComponent(self.view.WBP_Btn_Retreat, SceneCustom_Btn_Item)
    ---@type UIListView childScript: ItemBoxNew
    self.KGListView_ComponentCom = self:CreateComponent(self.view.KGListView_Component, UIListView)
    ---@type UIComButton
    self.WBP_ComBtn_SaveCom = self:CreateComponent(self.view.WBP_ComBtn_Save, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function SceneCustom_Component_Page:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtn_SaveCom.onClickEvent, "on_WBP_ComBtn_SaveCom_ClickEvent")
    self:AddUIEvent(self.KGListView_ComponentCom.onItemSelected, "on_KGListView_ComponentCom_ItemSelected")
    self:AddUIEvent(self.WBP_Btn_MoveForwardCom.onClickEvent, "on_WBP_Btn_MoveForwardCom_ClickEvent")
    self:AddUIEvent(self.WBP_Btn_RetreatCom.onClickEvent, "on_WBP_Btn_RetreatCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SceneCustom_Component_Page:InitUIView()
	self.WBP_ComBtn_SaveCom:Refresh(StringConst.Get("SCENE_CUSTOM_SAVE"))

	self.WBP_Btn_MoveForwardCom:Refresh(StringConst.Get("SCENE_CUSTOM_FORWARD"))
	self.WBP_Btn_MoveForwardCom:RefreshUIState(0)
	self.WBP_Btn_RetreatCom:Refresh(StringConst.Get("SCENE_CUSTOM_BACK"))
	self.WBP_Btn_RetreatCom:RefreshUIState(1)

	self.view.RichText_Actor:SetText(StringConst.Get("SCENE_CUSTOM_COMPONENT_NAME"))

	self.KGListView_ComponentCom:SetSelectionMode(ESelectionMode.Multi)
	self.KGListView_ComponentCom:SetMultiModeMaxSelectionCount(self.maxComponentNum)
	self.KGListView_ComponentCom:SetMultiModeMaxSelectionMode(Enum.EListMultiModeMaxSelectionMode.CantSelect)
end

---组件刷新统一入口
function SceneCustom_Component_Page:Refresh()
	self:RefreshAll()
end

function SceneCustom_Component_Page:RefreshAll()
	self:RefreshList()
	self:RefreshButtonList()
end

function SceneCustom_Component_Page:RefreshList()
	self:RefreshNumText()

	local comHasList = Game.SceneCustomSystem:GetCurComItemListData()
	local sceneWrapper = Game.SceneCustomSystem:GetCurSceneWrapper()
	self.componentDataList = {}
	for i, config in ksbcpairs(Game.TableData.GetSceneCustomListDataTable()) do
		if config.Type == Enum.ESceneCustomTypeEnum.Component then
			table.insert(self.componentDataList, {
				id = config.ID,
				icon = config.Icon,
				has = comHasList and comHasList[config.ID] ~= nil
			})
		end
	end

	Game.SceneCustomSystem:SortList(self.componentDataList)
	self.KGListView_ComponentCom:Refresh(self.componentDataList)
	for i, data in pairs(self.componentDataList) do
		if sceneWrapper:GetScenePlacementByConfigID(data.id) then
			self.KGListView_ComponentCom:SetSelectedItemByIndex(i, true, true)
		end
	end
end

function SceneCustom_Component_Page:RefreshNumText()
	local curNum = Game.SceneCustomSystem:GetCurSceneEntityComponentNum()
	self.view.RichText_Number:SetText(string.format("%s/%s", curNum, self.maxComponentNum))
end

function SceneCustom_Component_Page:RefreshButtonList()
	self.WBP_Btn_MoveForwardCom:RefreshUIDisable(not Game.SceneCustomSystem:CanRedo())
	self.WBP_Btn_RetreatCom:RefreshUIDisable(not Game.SceneCustomSystem:CanUndo())
end

--- 此处为自动生成
function SceneCustom_Component_Page:on_WBP_ComBtn_SaveCom_ClickEvent()
	Game.SceneCustomSystem:BeginShotAndUploadData()
end

--- 选中组件
--- 此处为自动生成
---@param index number
---@param data table
function SceneCustom_Component_Page:on_KGListView_ComponentCom_ItemSelected(index, data)
	Game.SceneCustomSystem:AddScenePlaceActor(data.id)
	self:RefreshNumText()
end

--- 此处为自动生成
function SceneCustom_Component_Page:on_WBP_Btn_MoveForwardCom_ClickEvent()
	Game.SceneCustomSystem:Redo()
end

--- 此处为自动生成
function SceneCustom_Component_Page:on_WBP_Btn_RetreatCom_ClickEvent()
	Game.SceneCustomSystem:Undo()
end

return SceneCustom_Component_Page
