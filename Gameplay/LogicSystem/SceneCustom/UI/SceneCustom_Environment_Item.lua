local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class SceneCustom_Environment_Item : UIListItem
---@field view SceneCustom_Anim_ItemBlueprint
local SceneCustom_Environment_Item = DefineClass("SceneCustom_Environment_Item", UIListItem)

SceneCustom_Environment_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SceneCustom_Environment_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SceneCustom_Environment_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function SceneCustom_Environment_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function SceneCustom_Environment_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SceneCustom_Environment_Item:InitUIView()
end

---面板打开的时候触发
function SceneCustom_Environment_Item:OnRefresh(data)
	self.data = data

	if not string.isEmpty(data.icon) then
		self:SetImage(self.view.Img_Actor, data.icon)
	end
	
	self.userWidget:BP_SetDisabled(not self.data.has)
end

return SceneCustom_Environment_Item
