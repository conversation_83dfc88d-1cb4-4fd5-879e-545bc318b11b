local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UITempComBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UITempComBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class SceneCustom_Screenshot_Panel : UIPanel
---@field view SceneCustom_Screenshot_PanelBlueprint
local SceneCustom_Screenshot_Panel = DefineClass("SceneCustom_Screenshot_Panel", UIPanel)

SceneCustom_Screenshot_Panel.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SceneCustom_Screenshot_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SceneCustom_Screenshot_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function SceneCustom_Screenshot_Panel:InitUIComponent()
    ---@type UITempComBtn
    self.WBP_ComBtnCloseNewCom = self:CreateComponent(self.view.WBP_ComBtnCloseNew, UITempComBtn)
    ---@type UIComButton
    self.WBP_ComBtn_SaveCom = self:CreateComponent(self.view.WBP_ComBtn_Save, UIComButton)
end

---UI事件在这里注册，此处为自动生成
function SceneCustom_Screenshot_Panel:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.WBP_ComBtnCloseNewCom.onClickEvent, "on_WBP_ComBtnCloseNewCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtn_SaveCom.onClickEvent, "on_WBP_ComBtn_SaveCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SceneCustom_Screenshot_Panel:InitUIView()
	self.WBP_ComBtn_SaveCom:Refresh(StringConst.Get("SCENE_CUSTOM_SAVE_IMAGE"))
end

---面板打开的时候触发
function SceneCustom_Screenshot_Panel:OnRefresh(...)
	self.captureRT = nil
	self.savePath = nil
	self.uploading = false
	
	self:TakeScreenShot()
end

--- 此处为自动生成
function SceneCustom_Screenshot_Panel:on_Btn_ClickArea_Clicked()
end

--- 此处为自动生成
function SceneCustom_Screenshot_Panel:on_WBP_ComBtnCloseNewCom_ClickEvent()
	self:CloseSelf()
end

--- 此处为自动生成
function SceneCustom_Screenshot_Panel:on_WBP_ComBtn_SaveCom_ClickEvent()
	self:SaveImage()
end

function SceneCustom_Screenshot_Panel:TakeScreenShot()
	Game.ScreenShotUtil:TakeScreenShot(self,"OnScreenShotCaptured", false)
end

function SceneCustom_Screenshot_Panel:OnScreenShotCaptured(rt, sizeX, sizeY)
	self.captureRT = rt
	self.savePath = Game.ScreenShotUtil:SaveTextureToPath(self.captureRT, "SceneCustom","Screenshot" .. tostring(_G._now(1)) ..".png")
	local dynamicMaterial = self.view.Img_ScreenShot:GetDynamicMaterial()
	dynamicMaterial:SetTextureParameterValue('PhotoImage', self.captureRT)
end

function SceneCustom_Screenshot_Panel:SaveImage()
	if not self.captureRT then
		return
	end

	self.uploading = true
	Game.AllInSdkManager:Upload(self.savePath, function(resourceID)
		Game.SceneCustomSystem:ReqChangePlayerSceneCustomStrategyPicture(resourceID)
		self.uploading = false
	end, function()
		self.uploading = false
		self.captureRT = nil

		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FASHION_SDK_NOT_OPEN)
	end)
end

return SceneCustom_Screenshot_Panel
