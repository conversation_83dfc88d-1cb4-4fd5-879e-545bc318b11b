local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UITempComBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UITempComBtn")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
local StringConst = kg_require("Data.Config.StringConst.StringConst")
---@class SceneCustom_Friend_Panel : UIPanel
---@field view SceneCustom_Friend_PanelBlueprint
local SceneCustom_Friend_Panel = DefineClass("SceneCustom_Friend_Panel", UIPanel)

SceneCustom_Friend_Panel.eventBindMap = {
	[EEventTypesV2.ON_SCENE_CUSTOM_ADD_ENTITY] = "RefreshPlayer",
	[EEventTypesV2.ON_SCENE_CUSTOM_DELETE_ENTITY] = "RefreshPlayer",
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function SceneCustom_Friend_Panel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function SceneCustom_Friend_Panel:InitUIData()
end

--- UI组件初始化，此处为自动生成
function SceneCustom_Friend_Panel:InitUIComponent()
    ---@type UIListView childScript: SceneCustom_AddDraw_Item
    self.KGList_FriendCom = self:CreateComponent(self.view.KGList_Friend, UIListView)
    ---@type UITempComBtn
    self.WBP_ComBtnCloseNewCom = self:CreateComponent(self.view.WBP_ComBtnCloseNew, UITempComBtn)
    ---@type UIComDiyTitle
    self.WBP_ComFirstBigTextCom = self:CreateComponent(self.view.WBP_ComFirstBigText, UIComDiyTitle)
end

---UI事件在这里注册，此处为自动生成
function SceneCustom_Friend_Panel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCloseNewCom.onClickEvent, "on_WBP_ComBtnCloseNewCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function SceneCustom_Friend_Panel:InitUIView()
	self.WBP_ComFirstBigTextCom:Refresh(StringConst.Get("SCENE_CUSTOM_FRIEND_DRAW"))
end

---面板打开的时候触发
function SceneCustom_Friend_Panel:OnRefresh()
	self:RefreshList()
end

function SceneCustom_Friend_Panel:RefreshList()
	self.friendDataList = Game.FriendSystem:GetFriendInfoList()
	if next(self.friendDataList) then
		self.userWidget:BP_EmptyState(false)
		self.KGList_FriendCom:Refresh(self.friendDataList)
	else
		self.userWidget:BP_EmptyState(true)
	end
end

function SceneCustom_Friend_Panel:RefreshPlayer()
	self.KGList_FriendCom:RefreshItems()
end

--- 此处为自动生成
function SceneCustom_Friend_Panel:on_WBP_ComBtnCloseNewCom_ClickEvent()
	self:CloseSelf()
end

return SceneCustom_Friend_Panel
