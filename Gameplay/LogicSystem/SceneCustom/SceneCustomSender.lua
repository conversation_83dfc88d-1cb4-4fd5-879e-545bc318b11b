---@class SceneCustomSender:SystemSenderBase
local SceneCustomSender = DefineClass("SceneCustomSender",SystemSenderBase)

function SceneCustomSender:ReqGetAllSceneCustomStrategyBriefInfo()
	self.Bridge.remote:ReqGetAllSceneCustomStrategyBriefInfo()
end

function SceneCustomSender:ReqGetOwnedSceneItem()
	self.Bridge.remote:ReqGetOwnedSceneItem()
end

function SceneCustomSender:ReqSetPlayerSceneCustom(index)
	self.Bridge.remote:ReqSetPlayerSceneCustom(index)
end

function SceneCustomSender:ReqSavePlayerSceneCustomStrategy(saveData)
	self.Bridge.remote:ReqSavePlayerSceneCustomStrategy(saveData)
end

function SceneCustomSender:ReqChangePlayerSceneCustomStrategyName(name)
	self.Bridge.remote:ReqChangePlayerSceneCustomStrategyName(name)
end

function SceneCustomSender:ReqChangePlayerSceneCustomStrategyPicture(icon)
	self.Bridge.remote:ReqChangePlayerSceneCustomStrategyPicture(icon)
end

return SceneCustomSender