local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class CurrencyExchange_Access_Item : UIListItem
---@field view CurrencyExchange_Access_ItemBlueprint
local CurrencyExchange_Access_Item = DefineClass("CurrencyExchange_Access_Item", UIListItem)

CurrencyExchange_Access_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function CurrencyExchange_Access_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function CurrencyExchange_Access_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function CurrencyExchange_Access_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function CurrencyExchange_Access_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function CurrencyExchange_Access_Item:InitUIView()
end

---面板打开的时候触发
function CurrencyExchange_Access_Item:OnRefresh(data)
    local achieveTableData = Game.TableData.GetAchievePathDataRow(tonumber(data))
    if achieveTableData == nil then
        return
    end

    self.view.Text_Name:SetText(achieveTableData.AchievePathName)
    local achievePathIcon = achieveTableData.AchievePathIcon
    if achievePathIcon then
        self:SetImage(self.view.Img_Icon, achievePathIcon)
    end
end


return CurrencyExchange_Access_Item
