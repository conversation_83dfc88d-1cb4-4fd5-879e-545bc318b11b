---@class ActivitySystem:SystemBase 活动管理

local worldConst = kg_require "Shared.Const.WorldConst"
local Const = kg_require("Shared.Const")
local ActivityUtils = kg_require("Shared.Utils.ActivityUtils")

local ActivitySystem = DefineSingletonClass("ActivitySystem", SystemBase)

ActivitySystem.JumpToType = {
    UI_JUMP = 1,                -- 打开ui
    TRACE = 2,                  -- 追踪
    TRACE_AND_INTERACT = 3,     -- 追踪并交互
    TELEPORT = 4,               -- 传送
}

---@private
function ActivitySystem:onCtor()
    self.model = nil
end

---@private onInit
function ActivitySystem:onInit()
    ---@type ActivityModel
    self.model = kg_require("Gameplay.LogicSystem.ActivitySystem.ActivityModel").new(false, true)
    Game.EventSystem:AddListener(EEventTypes.ON_SELF_LEVEL_CHANGED, self, "onLevelChange", GetMainPlayerEID())
    self:AddListenerV2(EEventTypesV2.SERVER_LAUNCH_INFO, "onServerLevelChange")
end

---SyncActivity 同步所有活动信息
---@param allActivity table
function ActivitySystem:SyncActivity(allActivity)
    self.model:clear()
    for activityId, info in pairs(allActivity) do
        if info.status ~= Enum.EActivityState.Close then
            self:receiveActivity(activityId, info.status, info.statusEndTimeStamp, info.statusStartTimeStamp)
			if info.status == Enum.EActivityState.Open then
				self:showOpenPopup(activityId, info.statusEndTimeStamp)
			end
		end
    end
    self:updateActivityView()
    Game.me:ReqGetServerLaunchInfo()
    Game.NewbieGuideSystem:OnTriggerActivity()
end

---OnChangeActivityStatus 单个活动状态改变
---@param activityId number 活动id
---@param state number 活动状态
---@param endTimestamp number 当前状态结束时间戳
---@param startTimestamp number 当前状态开始时间戳
function ActivitySystem:OnChangeActivityStatus(activityId, state, endTimestamp, startTimestamp)
    Log.Debug("==============================Activity status change", activityId, state, endTimestamp, startTimestamp)
    local activityTbData = Game.TableData.GetActivityDataRow(activityId)
    if state == Enum.EActivityState.AdvanceNotice then
        self:receiveActivity(activityId, state, endTimestamp, startTimestamp)

        if not string.isEmpty(activityTbData.PreReminderID) then
            Game.ReminderManager:AddReminderById(activityTbData.PreReminderID, {{activityTbData.Name, endTimestamp-_now(1)}})
        end
    elseif state == Enum.EActivityState.Open then
        self:receiveActivity(activityId, state, endTimestamp, startTimestamp)
        self:showOpenPopup(activityId, endTimestamp)

        if not string.isEmpty(activityTbData.OpenReminderID) then
            Game.ReminderManager:AddReminderById(activityTbData.OpenReminderID, {{activityTbData.Name}})
        end
    elseif state == Enum.EActivityState.Close then
        self:activityClose(activityId)

        if not string.isEmpty(activityTbData.EndReminderID) then
            Game.ReminderManager:AddReminderById(activityTbData.EndReminderID, {{activityTbData.Name}})
        end
    end
    self:updateActivityView()

    Game.GlobalEventSystem:Publish(EEventTypesV2.ACTIVITY_STATUS_CHANGE,activityId, state, endTimestamp, startTimestamp)
end

---@private receiveActivity 活动预告或开启时逻辑处理
---@param activityId number 活动id 
---@param state number 活动状态
---@param endTimestamp number 当前状态结束时间戳
---@param startTimestamp number 当前状态开始时间戳
function ActivitySystem:receiveActivity(activityId, state, endTimestamp, startTimestamp)
    if self.model.activityDict[activityId] then
        self.model.activityDict[activityId].State = state
        self.model.activityDict[activityId].Timestamp = endTimestamp
        self.model.activityDict[activityId].StartTimestamp = startTimestamp
    else
        self.model.activityDict[activityId] = { State = state, Timestamp = endTimestamp, StartTimestamp = startTimestamp, ActivityId = activityId }
        self.model.activityList[#self.model.activityList + 1] = activityId
    end
    local isMeetConditions, minLevel = self:CheckCondition(activityId)
    if not isMeetConditions then
        self.model.openingLockActivityDict[activityId] = minLevel
    end
    if self:CheckActivityIsOpen(activityId) then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ACTIVITY_OPEN, activityId, endTimestamp)
    end
end

---@private showOpenPopup 活动开启时弹窗提醒
---@param activityId number 活动ID
function ActivitySystem:showOpenPopup(activityId)
    local activityTbData = Game.TableData.GetActivityDataRow(activityId)
    if activityTbData.PopUpID == nil or activityTbData.PopUpID == 0 or not self:CheckCondition(activityId) then
        return
    end

    if not self:CheckConditionPopUpOnlyInWorldCanPass(activityId) then
        return
    end
    
    if not self:CheckConditionPopUpOnceInPeriodCanPass(activityId) then
        return
    end
    Game.MessageBoxSystem:AddPopupByConfig(activityTbData.PopUpID, function()
        self:DealJumpTo(
            activityTbData.PopUpJumpToType, activityTbData.PopUpJumpToPara, Const.TRACING_INFO_TYPE.ACTIVITY_POPUP, activityId
        )
    end, nil, { activityTbData.Name })
end

---@private CheckConditionPopUpOnceInPeriodCanPass 活动期间弹窗是否只弹出一次的检测，允许弹则返回true
---@param activityId number 活动ID
function ActivitySystem:CheckConditionPopUpOnceInPeriodCanPass(activityId)
    local activityTbData = Game.TableData.GetActivityDataRow(activityId)
    if not activityTbData or not activityTbData.PopUpOnceInPeriod then
        return true
    end
    local curTime = _G._now()
    local activityTimeKey = "PopUpOnceInPeriod" .. tostring(activityId)
    local lastShowTimeInfo = Game.GameClientData:GetPlayerValue(activityTimeKey)
    local inTimeInterval = false
    local index = 1
    local curOpenTime = ActivityUtils.GetOpenTimeByTimeStamp(activityId, curTime // 1000)
	if not curOpenTime then
		return true
	end
    while(index < #curOpenTime and index+1 <= #curOpenTime) do
        if TimeUtils.CheckTimeInDeterminedInterval(curTime, curOpenTime[index], curOpenTime[index+1]) then
            inTimeInterval = true
            break
        end
        index = index + 2
    end
    if lastShowTimeInfo and TimeUtils.SameDay(lastShowTimeInfo.timestamp, curTime) and lastShowTimeInfo.index == index and inTimeInterval then
        return false
    end
    if inTimeInterval then
        Game.GameClientData:SaveByPlayer(activityTimeKey, {timestamp = curTime, index = index}, true)
    end
    return true
end

---@private CheckConditionPopUpOnlyInWorldCanPass 是否仅在大世界弹出，允许弹则返回true
---@param activityId number 活动ID
function ActivitySystem:CheckConditionPopUpOnlyInWorldCanPass(activityId)
    local activityTbData = Game.TableData.GetActivityDataRow(activityId)
    if not activityTbData or not activityTbData.PopUpOnlyInWorld then
        return true
    end
    local currentSpace = NetworkManager.GetLocalSpace()
    if not currentSpace then
        return false
    end

    return currentSpace.WorldType == worldConst.WORLD_TYPE.BIGWORLD
end

---DealJumpTo 处理活动跳转
---@param jumpToType number 跳转类型
---@param jumpToPara table|number 跳转参数
---@param traceType TRACING_INFO_TYPE 追踪类型（Const.TRACING_INFO_TYPE.ACTIVITY_POPUP|Const.TRACING_INFO_TYPE.ACTIVITY_HUD）
---@param activityId number 活动id
function ActivitySystem:DealJumpTo(jumpToType, jumpToPara, traceType, activityId)
    if jumpToType == ActivitySystem.JumpToType.UI_JUMP then
        Game.UIJumpSystem:JumpToUI(tonumber(jumpToPara[1]), nil, activityId)
    elseif jumpToType == ActivitySystem.JumpToType.TRACE then
        if Game.NetworkManager.GetLocalSpace():IsLevelSpace() then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ACTIVITY_TRACE_FAIL)
            return
        end
        Game.TraceSystem:SetTrace(traceType, activityId)
    elseif jumpToType == ActivitySystem.JumpToType.TRACE_AND_INTERACT then
        if #jumpToPara == 1 then
            Game.AutoNavigationSystem:RequestNavigateToNPC(Enum.EAutoNavigationRequesterType.Activity, tostring(jumpToPara[1]))
        else
            Game.AutoNavigationSystem:RequestNavigateToNPC(Enum.EAutoNavigationRequesterType.Activity, tostring(jumpToPara[1]), table.unpack(jumpToPara, 2))
        end
    elseif jumpToType == ActivitySystem.JumpToType.TELEPORT then
        if #jumpToPara == 1 then
			local _, _, _, LevelID = Game.WorldDataManager:GetPositionByInsID(tostring(jumpToPara[1]), true)
            if LevelID and LevelID ~= 0 then
                Game.TeleportManager:RequestTeleport(tostring(jumpToPara[1]), LevelID)
            end
        else
            assert(#jumpToPara == 4)
            Game.AutoNavigationSystem:RequestNavigateTo(Enum.EAutoNavigationRequesterType.Activity, jumpToPara[1], FVector(table.unpack(jumpToPara, 2)))
        end
    end
end

---@private activityClose 活动关闭
---@param activityId number 活动id
function ActivitySystem:activityClose(activityId)
    if self.model.activityDict[activityId] then
        local bNeedBroadcast = false
        if self:CheckActivityIsOpen(activityId) then
            bNeedBroadcast = true
        end
        self.model.activityDict[activityId] = nil
        table.removev(self.model.activityList, activityId)
        self.model.openingLockActivityDict[activityId] = nil

        if bNeedBroadcast then
            Game.GlobalEventSystem:Publish(EEventTypesV2.ACTIVITY_CLOSE,activityId)
        end
    end
	-- 存疑
    local traceId = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.ACTIVITY_HUD)
	if traceId then
		Game.TraceSystem:RemoveTrace(Const.TRACING_INFO_TYPE.ACTIVITY_HUD)
	end
	traceId = Game.TraceSystem:GetCurrentTracing(Const.TRACING_INFO_TYPE.ACTIVITY_POPUP)
	if traceId then
		Game.TraceSystem:RemoveTrace(Const.TRACING_INFO_TYPE.ACTIVITY_POPUP)
	end
end

---@private OnLevelChange 玩家等级变化，检查是否有可以解锁的活动
---@param nowLevel number 玩家当前等级
function ActivitySystem:onLevelChange(_, propName, nowLevel, oldLevel)
    self:checkOpeningUnlockActivity()
    self:checkAllUnlockActivity()
end

---@private onServerLevelChange 服务器等级变化，检查是否有可以解锁的活动
function ActivitySystem:onServerLevelChange(ServerLaunchDay)
    if ServerLaunchDay >= Game.TableData.Get_ServerLaunchDayMax() then
        self.model.serverLevel = Game.TableData.Get_ServerLaunchLevelMax()
    else
        self.model.serverLevel = Game.TableData.Get_ServerLaunchTime2ServerLevel()[ServerLaunchDay].ServerLevel
    end

    self:checkOpeningUnlockActivity()
    self:checkAllUnlockActivity()
end

function ActivitySystem:checkOpeningUnlockActivity()
    if not next(self.model.openingLockActivityDict) then
        return
    end

    local newActivityList
    for k, v in pairs(self.model.openingLockActivityDict) do
        if self:CheckCondition(k) then
            if newActivityList == nil then
                newActivityList = {}
            end
            newActivityList[#newActivityList + 1] = k
        end
    end
    if newActivityList and #newActivityList > 0 then
        for i, v in ipairs(newActivityList) do
            self.model.openingLockActivityDict[v] = nil
        end
        self:updateActivityView()
    end
end

function ActivitySystem:checkAllUnlockActivity()
    if not self.model.allLockActivityDict then
        self.model.allLockActivityDict = {}
        local ActivityTypeDetailIDMapData = Game.TableData.Get_ActivityTypeDetailIDMapData()

        for _, activitys in ksbcpairs(ActivityTypeDetailIDMapData) do
            for _, data in ksbcpairs(activitys) do
                if not self:CheckCondition(data.ID) then
                    self.model.allLockActivityDict[data.ID] = data.ID
                end
            end
        end
    else
        local deleteKeys
        for activityID, _ in pairs(self.model.allLockActivityDict) do
            if self:CheckCondition(activityID) then
                if not deleteKeys then
                    deleteKeys = {}
                end
                table.insert(deleteKeys, activityID);
            end
        end

        if deleteKeys then
            for _, activityID in pairs(deleteKeys) do
                self.model.allLockActivityDict[activityID] = nil
                Game.GlobalEventSystem:Publish(EEventTypesV2.ACTIVITY_LOCK2UNLOCK, activityID)
            end
        end
    end
end

---@private updateActivityView 更新HUD显示UI列表
function ActivitySystem:updateActivityView()
    table.clear(self.model.showActivityList)
    if #self.model.activityList > 0 then
        for i = #self.model.activityList, 1, -1 do
            local value = self.model.activityList[i]
            local activityTbData = Game.TableData.GetActivityDataRow(value)
            if self.model.openingLockActivityDict[value] == nil and activityTbData.HudPush then
                self.model.showActivityList[#self.model.showActivityList + 1] = self.model.activityDict[value]
            end
        end
    end
	if #self.model.showActivityList > 0 then
		Game.HUDSystem:ShowUI(UICellConfig.HUDComNoticeBtn)
		if Game.HUDSystem:GetConstantActive(UICellConfig.HUDActivityList) then
			Game.HUDSystem:ShowUI(UICellConfig.HUDActivityList)
			Game.HUDSystem:SetConstantActive(UICellConfig.HUDActivityList, true)
		end
	else
		Game.HUDSystem:HideUI(UICellConfig.HUDComNoticeBtn)
		if Game.HUDSystem:GetConstantActive(UICellConfig.HUDActivityList) then
			Game.HUDSystem:HideUI(UICellConfig.HUDActivityList)
			Game.HUDSystem:SetConstantActive(UICellConfig.HUDActivityList, false)
		end
	end
end

---GetShowActivityList 获取当前可以显示的活动列表
function ActivitySystem:GetShowActivityList()
    return self.model.showActivityList
end

---CheckActivityIsOpen 判断活动是否处于开启状态
---@param activityId number 活动ID
function ActivitySystem:CheckActivityIsOpen(activityId)
    local activity = self.model.activityDict[activityId]
    return activity and self.model.openingLockActivityDict[activityId] == nil and activity.State == Enum.EActivityState.Open
end

---GetActivityFinishTime 获取活动结束时间（活动未开启时返回-1）
---@param activityId number 活动ID
function ActivitySystem:GetActivityFinishTime(activityId)
    if self:CheckActivityIsOpen(activityId) then
        local activity = self.model.activityDict[activityId]
        return activity.Timestamp
    end
    return -1
end

---@private CheckCondition 检查玩家是否满足活动参与条件（玩家等级和服务器等级都要满足）
---@param activityId number 活动id
function ActivitySystem:CheckCondition(activityId)
    local activityTBData = Game.TableData.GetActivityDataRow(activityId)
    local unlock = (not activityTBData.MinLevel or (Game.me and Game.me.Level >= activityTBData.MinLevel)) and 
                    (not activityTBData.ServerLevel or self.model.serverLevel > activityTBData.ServerLevel)

    return unlock, activityTBData.MinLevel
end

---@private onUnInit
function ActivitySystem:onUnInit()
end

function ActivitySystem:OnChangeWorldChannelQuizStatus(quizdId, status, timestamp)
    Game.ChatSystem:OnWordQuizStateChanged(quizdId, status, timestamp)
end

function ActivitySystem:SyncIsQuizSolved(hasSolved)
    Game.ChatSystem:SyncIsQuizSolved(hasSolved)
end

return ActivitySystem