---@class WBP_RankingltemView : WBP_Rankingltem_C
---@field public WidgetRoot WBP_Rankingltem_C
---@field public HorizontalItemBox HorizontalBox
---@field public Btn_ClickArea Button
---@field public Type number
---@field public IsNum number
---@field public IsTopTitleBg2 boolean
---@field public Event_UI_Style fun(self:self,Type:number,TopTitle_Bg2:boolean,IsNum:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetBg fun(self:self,Type:number,TopTitle_Bg2:boolean):void
---@field public SetNum fun(self:self,IsNum:number):void


---@class RankingItemView : WBP_RankingltemView
---@field public controller RankingItem
local RankingItemView = DefineClass("RankingItemView", UIView)

function RankingItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.Btn_ClickArea, "OnClick_Btn_ClickArea")

end

return RankingItemView
