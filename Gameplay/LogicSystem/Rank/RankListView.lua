---@class WBP_ComCheckBoxView : WBP_ComCheckBox_C
---@field public WidgetRoot WBP_ComCheckBox_C
---@field public CheckBox C7CheckBox
---@field public TB_Name C7TextBlock
---@field public Ani_Press WidgetAnimation
---@field public TextValue string
---@field public TextColor SlateColor
---@field public Undetermined boolean
---@field public Has Text boolean
---@field public SetUndetermined fun(self:self,Undetermined:boolean):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_ComComBoxView : WBP_ComComBox_C
---@field public WidgetRoot WBP_ComComBox_C
---@field public size_total SizeBox
---@field public Img_Arrow Image
---@field public Text_Target C7TextBlock
---@field public Button C7Button
---@field public Ani_Spread WidgetAnimation
---@field public Ani_Fewer WidgetAnimation
---@field public Pos number
---@field public ContentHeight number
---@field public ContentScrollCountCondition number
---@field public SetTitle fun(self:self,Title:string):void


---@class WBP_RankingltemView : WBP_Rankingltem_C
---@field public WidgetRoot WBP_Rankingltem_C
---@field public HorizontalItemBox HorizontalBox
---@field public Btn_ClickArea Button
---@field public Type number
---@field public IsNum number
---@field public IsTopTitleBg2 boolean
---@field public Event_UI_Style fun(self:self,Type:number,TopTitle_Bg2:boolean,IsNum:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetBg fun(self:self,Type:number,TopTitle_Bg2:boolean):void
---@field public SetNum fun(self:self,IsNum:number):void


---@class WBP_TradeEmptyView : WBP_TradeEmpty_C
---@field public WidgetRoot WBP_TradeEmpty_C
---@field public Text_Empty TextBlock


---@class WBP_RankingPersonalTopView : WBP_RankingPersonalTop_C
---@field public WidgetRoot WBP_RankingPersonalTop_C
---@field public Btn_ClickArea_1 C7Button
---@field public Btn_ClickArea_2 C7Button
---@field public Btn_ClickArea_3 C7Button
---@field public button_award_icon C7Button
---@field public Ani_Fadein WidgetAnimation
---@field public Construct fun(self:self):void


---@class WBP_RankingPersonalView : WBP_RankingPersonal_C
---@field public WidgetRoot WBP_RankingPersonal_C
---@field public WBP_CheckBoxServer WBP_ComCheckBoxView
---@field public WBP_CheckBoxFriend WBP_ComCheckBoxView
---@field public WBP_CheckBoxArea WBP_ComCheckBoxView
---@field public AreaSelectBox CanvasPanel
---@field public WBP_City WBP_ComComBoxView
---@field public WBP_Province WBP_ComComBoxView
---@field public WBP_Profession WBP_ComComBoxView
---@field public WBP_ThirdType WBP_ComComBoxView
---@field public TopTitle WBP_RankingltemView
---@field public MidListContent CanvasPanel
---@field public EmptyTips Overlay
---@field public WBP_TradeEmpty WBP_TradeEmptyView
---@field public PlayerInfo WBP_RankingltemView
---@field public WBP_RankListPersonalTop WBP_RankingPersonalTopView
---@field public Ani_Fadein WidgetAnimation
---@field public Construct fun(self:self):void

---@class RankListView : WBP_RankingPersonalView
---@field public controller RankList
local RankListView = DefineClass("RankListView", UIView)

function RankListView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    
    controller:AddUIListener(EUIEventTypes.CheckStateChanged, self.WBP_CheckBoxArea.CheckBox, "OnCheckStateChanged_WBP_CheckBoxArea_CheckBox")
    controller:AddUIListener(EUIEventTypes.CheckStateChanged, self.WBP_CheckBoxFriend.CheckBox, "OnCheckStateChanged_WBP_CheckBoxFriend_CheckBox")
    controller:AddUIListener(EUIEventTypes.CheckStateChanged, self.WBP_CheckBoxServer.CheckBox, "OnCheckStateChanged_WBP_CheckBoxServer_CheckBox")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_RankListPersonalTop.Btn_ClickArea, "OnClick_WBP_RankListPersonalTop_button_award_icon")
end

return RankListView
