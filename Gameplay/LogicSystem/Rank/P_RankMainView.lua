---@class WBP_ComBtnBackNewView : WBP_ComBtnBackNew_C
---@field public WidgetRoot WBP_ComBtnBackNew_C
---@field public Btn_Back C7Button
---@field public Text_Back C7TextBlock
---@field public Btn_Info C7Button
---@field public Icon Image
---@field public Ani_Press WidgetAnimation
---@field public TitleName_lua string
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public BndEvt__WBP_ComBtnBackNew_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public GetBrush_0 fun(self:self):SlateBrush


---@class WBP_ComPanelView : WBP_ComPanel_C
---@field public WidgetRoot WBP_ComPanel_C
---@field public NS_BackGround NamedSlot
---@field public Money NamedSlot
---@field public HorizontalBox HorizontalBox
---@field public NS_TabList NamedSlot
---@field public NS_TabListLv1 NamedSlot
---@field public NS_TabListLv2 NamedSlot
---@field public NS_ComPanel NamedSlot
---@field public WBP_ComBtnBack WBP_ComBtnBackNewView
---@field public Title Text string
---@field public TabType number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SetTabList fun(self:self,TabType:number):void


---@class WBP_ComTreeListView : WBP_ComTreeList_C
---@field public WidgetRoot WBP_ComTreeList_C
---@field public TreeList ScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public Structure TreeListCell
---@field public SelectionMode number
---@field public IndexList number
---@field public LayoutList ListLayout
---@field public SpaceUpList number
---@field public SpaceBottomList number
---@field public SpaceLeftList number
---@field public SpaceRightList number
---@field public AlignmentList ListAligment
---@field public indexToTopPos number
---@field public indexToBottomPos number
---@field public indexToXPos number
---@field public oldBottomIndex number
---@field public oldTopIndex number
---@field public allLength number
---@field public PaddingList Margin
---@field public ListPadding Margin
---@field public MaxValue number
---@field public RetainerBox RetainerBox
---@field public OnUserScrolled fun(self:self,CurrentOffset:number):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number,libWidget:string,sizeX:number,sizeY:number):void
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public InsertSubIndex fun(self:self,floor:number):void
---@field public GetArrayWidget fun(self:self,index:number):Widget,string,number,number,number,Widget,string,number,number,number
---@field public GetListSize fun(self:self):number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidget:Widget,LibSize:Vector2D):void
---@field public CalculatePos fun(self:self):void
---@field public RebulidList fun(self:self,CurrentOffset:number):void
---@field public Cal_OnGirdNextGrid fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,oldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number,number,number,number,number
---@field public Cal_OnGirdNextList fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,OldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number
---@field public Cal_OnListNextGrid fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,OldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number
---@field public Cal_OnListNextList fun(self:self,SizeX:number,SizeY:number,SpaceUp:number,SpaceBottom:number,SpaceLeft:number,SpaceRight:number,OldPosX:number,OldPosY:number,OldGridSpace:number,TotalLenght:number,Alignment:ListAligment,Padding:Margin,bIsNewFloor:boolean):number,number,number,number


---@class WBP_ComMutiMenuView : WBP_ComMutiMenu_C
---@field public WidgetRoot WBP_ComMutiMenu_C
---@field public WBP_ComTreeList WBP_ComTreeListView
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void


---@class WBP_RankingView : WBP_Ranking_C
---@field public WidgetRoot WBP_Ranking_C
---@field public WBP_ComPanel WBP_ComPanelView
---@field public WBP_ComTabListFold WBP_ComMutiMenuView
---@field public Ani_Fadein WidgetAnimation
---@field public Construct fun(self:self):void


---@class P_RankMainView : WBP_RankingView
---@field public controller P_RankMain
local P_RankMainView = DefineClass("P_RankMainView", UIView)

function P_RankMainView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPanel.WBP_ComBtnBack.Btn_Back, "OnClick_WBP_ComPanel_WBP_ComBtnBack_Btn_Back")
    controller:AddUIListener(EUIEventTypes.CLICK, self.WBP_ComPanel.WBP_ComBtnBack.Btn_Info, "OnClick_WBP_ComPanel_WBP_ComBtnBack_Btn_Info")

end

return P_RankMainView
