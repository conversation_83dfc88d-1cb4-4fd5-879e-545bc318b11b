local ESlateVisibility = import("ESlateVisibility")

local RankComTabHor = DefineClass("RankComTabHor", UIComponent)

function RankComTabHor:OnCreate()
    self.SelectCallBack = nil
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_TeamHTabList2_1.<PERSON><PERSON>, self.OnSelectIndex, 1)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_TeamHTabList2_2.<PERSON><PERSON>, self.OnSelectIndex, 2)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_TeamHTabList2_3.<PERSON><PERSON>, self.OnSelectIndex, 3)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_TeamHTabList2_4.But<PERSON>, self.OnSelectIndex, 4)
    self:AddUIListener(EUIEventTypes.CLICK, self.View.WBP_TeamHTabList2_5.<PERSON><PERSON>, self.OnSelectIndex, 5)
    self.SelectIndex = 0
    self.CacheTabData = nil
    -- self.TabList = {}
end


function RankComTabHor:SetData(TabData, ClickCallBack, selectIndex)
    self.CacheTabData = TabData

    for i = 1, #TabData do
        self.View["WBP_TeamHTabList2_"..i].Text_Name:SetText(TabData[i].TabName)
        self.View["WBP_TeamHTabList2_"..i].WidgetRoot:SetSelect(0)
        self.View["WBP_TeamHTabList2_"..i].Bg_Lock:SetVisibility(ESlateVisibility.Collapsed)
    end

    self:OnSelectIndex(selectIndex or 1)
end

function RankComTabHor:OnSelectIndex(index)
    for i = 1, #self.CacheTabData do
        local CurWidget = self.View["WBP_TeamHTabList2_"..i]
        if index == i then
            CurWidget.WidgetRoot:SetSelect(1)
        else
            CurWidget.WidgetRoot:SetSelect(0)
        end
    end
    Game.EventSystem:Publish(_G.EEventTypes.ON_PERSONAL_RANK_SWITCH_INDEX, self.CacheTabData[index].ProfessionID)
    Log.Debug("test")
end


return RankComTabHor