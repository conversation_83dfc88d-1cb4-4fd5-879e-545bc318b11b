local ItemRewardNew = kg_require("Gameplay.LogicSystem.Item.NewUI.ItemRewardNew")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local ESlateVisibility = import("ESlateVisibility")
---@class DedicateRewardNew_Item : UIListItem
---@field view DedicateRewardNew_ItemBlueprint
local DedicateRewardNew_Item = DefineClass("DedicateRewardNew_Item", UIListItem)

DedicateRewardNew_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DedicateRewardNew_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DedicateRewardNew_Item:InitUIData()
end

--- UI组件初始化，此处为自动生成
function DedicateRewardNew_Item:InitUIComponent()
    ---@type ItemRewardNew
    self.WBP_ItemRewardCom = self:CreateComponent(self.view.WBP_ItemReward, ItemRewardNew)
end

---UI事件在这里注册，此处为自动生成
function DedicateRewardNew_Item:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DedicateRewardNew_Item:InitUIView()
end

---面板打开的时候触发
---@param data
---@field data.id number
---@field data.num number
---@field data.bReceive boolean
function DedicateRewardNew_Item:OnRefresh(data)
	self.userWidget:SetReceived(data.bReceive)
	self.WBP_ItemRewardCom:FillItem(data.id, true, Enum.EItemSelectType.NotSelected)
	local itemExcelData = Game.TableData.GetItemNewDataRow(data.id)
	if itemExcelData then
		self.view.Text_Name:SetText(itemExcelData.itemName)
		self.view.Item_Num:SetText("x"..data.num)
		local bIsSpecial = itemExcelData.tipConsecrationSpecial or false
		self.userWidget:SetLight(bIsSpecial)
		if bIsSpecial then
			self.view.KPanel_Tag:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		else
			self.view.KPanel_Tag:SetVisibility(ESlateVisibility.Collapsed)
		end
	end

	-- 加最大体力特别显示
	--local action, param = Game.BagSystem:GetSystemActionConfig(info[1])
	--if action == "AddMaxStaminaValue" then
	--	self.View:SetInfoDisplay(true)
	--	self.View.Text_Num:SetText(param or "")
	--else
	--	self.View:SetInfoDisplay(false)
	--end
	self.userWidget:SetInfoDisplay(false)
end

return DedicateRewardNew_Item
