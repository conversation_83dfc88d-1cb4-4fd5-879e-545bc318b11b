local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIIrregularListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIIrregularListView")
local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComCurrencyItem = kg_require("Framework.KGFramework.KGUI.Component.Tag.UIComCurrencyItem")
local UITempComBtn = kg_require("Framework.KGFramework.KGUI.Component.Button.UITempComBtn")
local UIPanel = kg_require("Framework.KGFramework.KGUI.Core.UIPanel")
---@class DedicatePanel : UIPanel
---@field view DedicatePanelBlueprint
local DedicatePanel = DefineClass("DedicatePanel", UIPanel)

DedicatePanel.eventBindMap = {
	[EEventTypesV2.ON_WONDER_SHOP_UPGRADE_SUCCESS] = "OnWonderShopLevelUp"
}

DedicatePanel.LevelItemState = {
	Achieve = 0, --达成
	To_Be_Completed = 1, --待完成
	Lock = 2, --锁定
}

DedicatePanel.BottomBtnState = {
	Hide = 1, --隐藏
	Show = 0  --展示
}

DedicatePanel.checkCanUpgradeToFunc = {
	--只检查货币数量是否足够
	[1] = function(self, level) 
		return DedicatePanel.CheckSubmitNumEnough(self, level)
	end,
}

DedicatePanel.bottomBtnFunc = {
	--升级并领取奖励
	[1] = function(self)
		return DedicatePanel.UpgradeAndAward(self)
	end,
}

DedicatePanel.closeFunc = {
	--直接关闭页面
	[1] = function(self)
		return DedicatePanel.CommonClose(self)
	end,
}

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function DedicatePanel:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function DedicatePanel:InitUIData()
	self.levelSelectedIndex = 0
end

--- UI组件初始化，此处为自动生成
function DedicatePanel:InitUIComponent()
    ---@type UITempComBtn
    self.WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComBtnClose, UITempComBtn)
    ---@type UITempComBtn
    self.WBP_ComBtnCom = self:CreateComponent(self.view.WBP_ComBtn, UITempComBtn)
	---@type UIComCurrencyItem
	self.WBP_ComCurrencyCom = self:CreateComponent(self.view.WBP_ComCurrency, UIComCurrencyItem)
    ---@type UIListView childScript: DedicateRewardNew_Item
    self.RewardListCom = self:CreateComponent(self.view.RewardList, UIListView)
    ---@type UIIrregularListView
    self.LevelListCom = self:CreateComponent(self.view.LevelList, UIIrregularListView)
    ---@type UIComDiyTitle
    self.TitleCom = self:CreateComponent(self.view.Title, UIComDiyTitle)
end

---UI事件在这里注册，此处为自动生成
function DedicatePanel:InitUIEvent()
    self:AddUIEvent(self.WBP_ComBtnCom.onClickEvent, "on_WBP_ComBtnCom_ClickEvent")
    self:AddUIEvent(self.WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComBtnCloseCom_ClickEvent")
    self:AddUIEvent(self.LevelListCom.onItemSelected, "on_LevelListCom_ItemSelected")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function DedicatePanel:InitUIView()
	if UI.IsShow("P_HUDBaseView") then
		Game.HUDSystem:PlayerRightTopAni(false)
		Game.HUDSystem:PlayerLeftTopAni(false)
	end
end

---面板打开的时候触发
---@param wonderShopId number 奇观商店id
---@param checkCanUpgradeToFuncIdx number 检查升级条件函数序号
---@param bottomBtnFuncIdx number 底部按钮函数序号
---@param closeFuncIdx number 关闭函数序号
function DedicatePanel:OnRefresh(wonderShopId, checkCanUpgradeToFuncIdx, bottomBtnFuncIdx, closeFuncIdx)
	self.wonderShopId = wonderShopId
	local wonderShopData = Game.TableData.GetWonderShopDataRow(wonderShopId)
	if not wonderShopData then return end
	---设置标题和背景状态
	self.TitleCom:Refresh(wonderShopData.Guititle)
	self.userWidget:SetType(wonderShopData.GUIFloor)

	local shopRewardListData = Game.TableData.GetWonderShopRewardDataRow(wonderShopId)
	if not shopRewardListData then return end
	self.fullLevel = #shopRewardListData
	
	self.checkCanUpgradeToFuncIdx = checkCanUpgradeToFuncIdx or 1
	self.checkCanUpgradeTo = DedicatePanel.checkCanUpgradeToFunc[checkCanUpgradeToFuncIdx or 1]
	self.bottomBtnCallback = DedicatePanel.bottomBtnFunc[bottomBtnFuncIdx or 1]
	self.closeCallback = DedicatePanel.closeFunc[closeFuncIdx or 1]
	
	if self.checkCanUpgradeToFuncIdx == 1 then
		self.curCurrency = Game.BagSystem:GetItemCount(wonderShopData.ItemID) or 0
	end
	
	---刷新等级数据
	self:UpdateLevelList()
	---更新按钮的状态
	self:UpdateBtnState()
end

---刷新等级数据
function DedicatePanel:UpdateLevelList()
	self.curLevel = Game.me.WonderShopLevelRecord[self.wonderShopId] or 0
	local maxLevel = self.fullLevel
	if maxLevel == nil or maxLevel == 0 then return end
	self.levelStateData = {}
	for i = 1, maxLevel do
		table.insert(self.levelStateData, {index = i, state = self:GetLevelState(i)})
	end
	self.LevelListCom:Clear()
	self.LevelListCom:Refresh(self.levelStateData)
	self.levelSelectedIndex = math.min(self.curLevel + 1, maxLevel)
	self.LevelListCom:SetSelectedItemByIndex(self.levelSelectedIndex, true)
	---刷新奖励数据
	self:UpdateRewardList()
end

---刷新奖励数据
function DedicatePanel:UpdateRewardList()
	local rewardList = {}
	local shopRewardListData = Game.TableData.GetWonderShopRewardDataRow(self.wonderShopId)[self.levelSelectedIndex].Reward
	local bReceive
	if self.levelSelectedIndex <= self.curLevel then
		bReceive = true
	else
		bReceive = false
	end
	
	for _, item in ipairs(shopRewardListData) do
		local reward = {
			id = item[1],
			num = item[2],
			bReceive = bReceive,
		}
		table.insert(rewardList, 
			reward)
	end
	self.RewardListCom:Refresh(rewardList)
end

---更新按钮的状态
function DedicatePanel:UpdateBtnState()
	if self.levelSelectedIndex == 0 then return end
	local showLevel = self.levelSelectedIndex
	local state = self.levelStateData[showLevel].state
	if state == DedicatePanel.LevelItemState.Achieve then
		self.userWidget:SetState(DedicatePanel.BottomBtnState.Hide)
		self.view.Text_Hint:SetText(Game.TableData.GetWonderShopStringDataRow("ACHIEVE_LEVEL_HINT_TEXT").StringValue)
	elseif state == DedicatePanel.LevelItemState.To_Be_Completed then
		-----检查是否可以一键升级
		--if showLevel > self.curLevel + 1 then
		--	self.WBP_ComBtnCom:SetName(Game.TableData.GetWonderShopStringDataRow("LEVEL_UP_TOGETHER_BTN_NAME").StringValue)
		--	self.WBP_ComBtnCom.userWidget:SetDisable(false)
		--else
		--	self.WBP_ComBtnCom:SetName(Game.TableData.GetWonderShopStringDataRow("LEVEL_UP_BTN_NAME").StringValue)
		--	self.WBP_ComBtnCom.userWidget:SetDisable(not self:checkCanUpgradeTo(showLevel))
		--end
		self.WBP_ComBtnCom:SetName(Game.TableData.GetWonderShopStringDataRow("LEVEL_UP_BTN_NAME").StringValue)
		self.WBP_ComBtnCom.userWidget:SetDisable(not self:checkCanUpgradeTo(showLevel))
		
		self.userWidget:SetState(DedicatePanel.BottomBtnState.Show)
		if self.checkCanUpgradeToFuncIdx == 1 then
			self:UpdateCurrency(showLevel)
		end
	else
		self.userWidget:SetState(DedicatePanel.BottomBtnState.Hide)
		self.view.Text_Hint:SetText(Game.TableData.GetWonderShopStringDataRow("LOCK_LEVEL_HINT_TEXT").StringValue)
	end
end

--- 拿到对应等级的状态
function DedicatePanel:GetLevelState(level)
	local curLevel = self.curLevel
	local state
	---已达成状态
	if curLevel >= level then
		state = DedicatePanel.LevelItemState.Achieve
	elseif curLevel + 1 == level then
		state = DedicatePanel.LevelItemState.To_Be_Completed
	else
		--if self:checkCanUpgradeTo(level) then
		--	state = DedicatePanel.LevelItemState.To_Be_Completed
		--else
		--	state = DedicatePanel.LevelItemState.Lock
		--end
		state = DedicatePanel.LevelItemState.Lock
	end
	return state
end

---检查货币数量是否足够
function DedicatePanel:CheckSubmitNumEnough(level)
	---升级到当前等级所需的货币
	local needNum = self:GetTheCountUpgradeTo(level)
	return needNum <= self.curCurrency
end

---获取升级到当前等级的所需货币的个数
function DedicatePanel:GetTheCountUpgradeTo(level)
	local curLevel = self.curLevel
	local needSubmitNum = 0
	local shopRewardListData = Game.TableData.GetWonderShopRewardDataRow(self.wonderShopId)
	for lv = curLevel + 1, level do
		needSubmitNum = needSubmitNum + shopRewardListData[lv].SubmitNum
	end
	return needSubmitNum
end


---刷新当前等级对应的货币信息
function DedicatePanel:UpdateCurrency(level)
	local wonderShopData = Game.TableData.GetWonderShopDataRow(self.wonderShopId)
	local currencyId = wonderShopData.ItemID
	
	local haveNum = self.curCurrency
	local needNum = self:GetTheCountUpgradeTo(level)
	
	self.WBP_ComCurrencyCom:OnRefresh(currencyId)
	self.WBP_ComCurrencyCom:SetMoneyNum(haveNum, needNum)
end

--- 此处为自动生成
function DedicatePanel:on_WBP_ComBtnCom_ClickEvent()
	if self.bottomBtnCallback then
		xpcall(self.bottomBtnCallback, _G.CallBackError, self)
	end
end

function DedicatePanel:UpgradeAndAward()
	Game.DiscoverySystem.sender:ReqSubmitAnomalousItems(self.wonderShopId)
end

function DedicatePanel:OnWonderShopLevelUp(areaID, level)
	Game.me.WonderShopLevelRecord[areaID] = level
	if self.checkCanUpgradeToFuncIdx == 1 then
		local wonderShopData = Game.TableData.GetWonderShopDataRow(areaID)
		self.curCurrency = Game.BagSystem:GetItemCount(wonderShopData.ItemID) or 0
	end
	
	---刷新等级数据
	self:UpdateLevelList()
	---更新按钮的状态
	self:UpdateBtnState()
end

--- 此处为自动生成
function DedicatePanel:on_WBP_ComBtnCloseCom_ClickEvent()
	if UI.IsShow("P_HUDBaseView") then
		Game.HUDSystem:PlayerRightTopAni(true)
		Game.HUDSystem:PlayerLeftTopAni(true)
	end
	
	if self.closeCallback then
		xpcall(self.closeCallback, _G.CallBackError, self)
	end
	
	-- 保底关闭
	self:CloseSelf()
end

function DedicatePanel:CommonClose()
	self:CloseSelf()
end

--- 此处为自动生成
---@param index number
---@param data table
function DedicatePanel:on_LevelListCom_ItemSelected(index, data)
	self.levelSelectedIndex = index
	self:UpdateRewardList()
	self:UpdateBtnState()
end


return DedicatePanel
