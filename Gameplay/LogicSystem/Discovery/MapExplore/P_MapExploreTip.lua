

---@class P_MapExploreTip : UIController
---@field public View WBP_ExploreTipsView
local P_MapExploreTip = DefineClass("P_MapExploreTip", UIController)
local P_MapExploreTipItem =  kg_require "Gameplay.LogicSystem.Discovery.MapExplore.P_MapExploreTipItem"

function P_MapExploreTip:OnCreate()
    self.areaList = BaseList.CreateList(self, BaseList.Kind.ComList, self.View.AreaList, P_MapExploreTipItem) --- luacheck:ignore
end

function P_MapExploreTip:OnRefresh_AreaList(widget, index, selected)
    local progressInfo = self.progressInfoDict[index]
    widget:RefreshItem(progressInfo)
end

function P_MapExploreTip:OnRefresh(firstLevelId)
    self.View.WBP_SystemTipsTitle.Text_Title:SetText("探索进度详情")
    self.progressInfoDict = Game.DiscoverySystem:GetTypeProgressByFirstLevelId(firstLevelId)
    table.sort(self.progressInfoDict, function(a, b)
        local data1 = Game.TableData.GetExploreTypeDataRow(a.TypeId)
        local data2 = Game.TableData.GetExploreTypeDataRow(b.TypeId)
        return data1.Order > data2.Order
    end)
    self.areaList:SetData(#self.progressInfoDict)
end


function P_MapExploreTip:OnHide()

end



return P_MapExploreTip