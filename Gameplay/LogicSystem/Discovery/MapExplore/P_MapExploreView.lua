---@class WBP_ComBtnIconNewView : WBP_ComBtnIconNew_C
---@field public WidgetRoot WBP_ComBtnIconNew_C
---@field public OutCanvas CanvasPanel
---@field public Icon Image
---@field public Text_Name TextBlock
---@field public Big_Button_ClickArea KGButton
---@field public Anim_1 WidgetAnimation
---@field public Ani_Press WidgetAnimation
---@field public Ani_Hover WidgetAnimation
---@field public Ani_Tower WidgetAnimation
---@field public Ani_Fadein WidgetAnimation
---@field public Btn Style ST_ComBtnIcon
---@field public Btn Name name
---@field public Press Sound SlateSound
---@field public Top number
---@field public Event_UI_Style fun(self:self,BtnName:string):void
---@field public Play Hint Anim fun(self:self):void
---@field public BndEvt__WBP_ComBtnIcon_Button_lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Btn Style fun(self:self,Btn Style:ST_ComBtnIcon):void
---@field public SetVxSize fun(self:self):void


---@class WBP_ComListView : WBP_ComList_C
---@field public WidgetRoot WBP_ComList_C
---@field public List ScrollBox
---@field public DiffPanel CanvasPanel
---@field public DiffPoint Border
---@field public bIsTileView boolean
---@field public PreviewCount number
---@field public LibWidget ListLib
---@field public ScrollWidget Widget
---@field public Orientation EOrientation
---@field public ScrollBarVisibility ESlateVisibility
---@field public SelectionMode number
---@field public Space ListSpace
---@field public Alignment ComListAligment
---@field public bIsCenterContent boolean
---@field public tempIndex number
---@field public oldPosX number
---@field public oldPosY number
---@field public tempPosX number
---@field public tempPosY number
---@field public widgetX number
---@field public widgetY number
---@field public spaceUp number
---@field public spaceBottom number
---@field public spaceLeft number
---@field public spaceRight number
---@field public bSizeToContent boolean
---@field public ListPadding Margin
---@field public MaxValueDown number
---@field public RetainerBox RetainerBox
---@field public OnSetItem MulticastDelegate
---@field public PreviewInAnimation boolean
---@field public OnlyNotifyByAnimation boolean
---@field public AnimationCurve CurveFloat
---@field public OpacityCurve CurveFloat
---@field public Frequence number
---@field public Duration number
---@field public StartOffsetX number
---@field public StartOffsetY number
---@field public StartOpacity number
---@field public MaxValueUp number
---@field public MaxValueLeft number
---@field public MaxValueRight number
---@field public StartTime number
---@field public ListPlayInAnimation MulticastDelegate
---@field public isAniNotyfy boolean
---@field public PlayFlyAnimation fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public CalculatePos fun(self:self):Widget,Vector2D,number
---@field public CreatListCell fun(self:self,widget:Widget,posX:number,posY:number):Widget
---@field public SetAllSlot fun(self:self,Src:Widget,Tag:Widget,Position:Vector2D):void
---@field public GetListSize fun(self:self):number,number,number,number
---@field public GetWidgetSize fun(self:self,Widget:Widget):number,number,number,number,number,number
---@field public VerticalTileChange fun(self:self):void
---@field public VerticalTile fun(self:self):void
---@field public VerticalList fun(self:self):void
---@field public HorizontalTileChange fun(self:self):void
---@field public HorizontalTile fun(self:self):void
---@field public HorizontalList fun(self:self):void
---@field public VerticalTileAuto fun(self:self):void
---@field public SetSlot fun(self:self,Pos:Vector2D,SrcWidget:Widget,TarWidfget:Widget):void
---@field public VerticalListAuto fun(self:self):void
---@field public CellFly fun(self:self):void


---@class WBP_ComBtnBackNewView : WBP_ComBtnBackNew_C
---@field public WidgetRoot WBP_ComBtnBackNew_C
---@field public Btn_Back KGButton
---@field public Text_Back KGTextBlock
---@field public Btn_Info KGButton
---@field public Icon Image
---@field public Ani_Press WidgetAnimation
---@field public TitleName_lua string
---@field public OnClicked MulticastDelegate
---@field public OnReleased MulticastDelegate
---@field public OnPressed MulticastDelegate
---@field public IsBtnBackNew boolean
---@field public BndEvt__WBP_ComBtnBackNew_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonPressedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_2_OnButtonReleasedEvent__DelegateSignature fun(self:self):void
---@field public BndEvt__WBP_ComBtnBack_Btn_Back_Lua_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature fun(self:self):void
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public GetBrush_0 fun(self:self):SlateBrush


---@class WBP_MapExplorePanelView : WBP_MapExplorePanel_C
---@field public WidgetRoot WBP_MapExplorePanel_C
---@field public Btn_ClickArea KGButton
---@field public RewardList VerticalBox
---@field public AreaDetailBtn WBP_ComBtnIconNewView
---@field public LocationList WBP_ComListView
---@field public CloseBtn WBP_ComBtnBackNewView
---@field public Ani_Fadein WidgetAnimation
---@field public Ani_Ani_Switch WidgetAnimation
---@field public Ani_Fadeout WidgetAnimation
---@field public Min number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public SequenceEvent_0 fun(self:self):void
---@field public SequenceEvent fun(self:self):void

---@class P_MapExploreView : WBP_MapExplorePanelView
---@field public controller P_MapExplore
local P_MapExploreView = DefineClass("P_MapExploreView", UIView)

function P_MapExploreView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)
	--self.AnimationInfo = {AnimFadeIn = {{self.WidgetRoot,0.666667}, {self.WBP_ComMaskL.WidgetRoot, 0.98335},{self.WBP_ComMaskR.WidgetRoot, 0.93335},{self.AreaDetailBtn_lua.WidgetRoot, 1.866683},},AnimFadeOut = {{self.WidgetRoot,0.1005}, {self.WBP_ComMaskR.WidgetRoot, 0.266683},}}
end

function P_MapExploreView:OnDestroy()
end

return P_MapExploreView
