---@class DiscoverySystem:SystemBase
local DiscoverySystem = DefineSingletonClass("DiscoverySystem", SystemBase)
local LQueue = require "Framework.Library.LQueue"

DiscoverySystem.LevelState = {
	Achieve = 0, --达成
	To_Be_Completed = 1, --待完成
	Lock = 2, --锁定
}

DiscoverySystem.eventBindMap = {
    [EEventTypesV2.RET_EXPLORE_REWARD_GET] = "OnGetExploreReward",
}

function DiscoverySystem:OpenSoulSubmitUI(steleId, sceneActorInsID, closeCallback)
    -- UI.ShowUI("P_Dedicate", steleId, sceneActorInsID, closeCallback)
end

function DiscoverySystem:OpenExploreUI(firstlevelId)
    UI.ShowUI("P_MapExplore", firstlevelId)
end

function DiscoverySystem:OnBackToSelectRole()
    self.model:clear()
end

function DiscoverySystem:onInit()
    self.sender = kg_require("Gameplay/LogicSystem/Discovery/DiscoverySender").new()
    self.model = kg_require("Gameplay/LogicSystem/Discovery/DiscoveryModel").new()
    Game.RedPointSystem:AddListener("MapExploreRewardBtn", self, self.CheckAllRewardRedpoint)
    Game.RedPointSystem:AddListener("MapExploreReward", self, self.CheckTabRewardRedpoint)
end

---根据一级区域id获取各类玩法的探索都
function DiscoverySystem:GetTypeProgressByFirstLevelId(firstLevelId)
    local typeProgressDict = {}
    local exploreFinishedMap = Game.me.ExploreUnlockInfoMap[firstLevelId] and Game.me.ExploreUnlockInfoMap[firstLevelId].ExploreFinishedMap or {}
    local rewardSumInfo = Game.TableData.Get_ExploreRewardSumMap()[firstLevelId]
    for typeId, totalNum in ksbcpairs(rewardSumInfo.ExploreTypeMap) do
        table.insert(typeProgressDict, {
            TypeId = typeId,
            FinishedNum=exploreFinishedMap[typeId] or 0, 
            TotalNum=totalNum
        })
    end
    return typeProgressDict
end

---获取一级区域探索的百分比
function DiscoverySystem:GetFirstAreaCompletePercent(firstLevelId)
    ---一级区域下所有二级区域的探索度总值
    local firstToSecond = Game.TableData.Get_ExploreFirstToSecondLevelArea()
    local secondIds = firstToSecond[firstLevelId]
    local exploreDegreeTotal = 0
    for _, secondId in ksbcpairs(secondIds) do
        local secondLevelAreaExcelData = Game.TableData.GetExploreSecondLevelAreaDataRow(secondId)
        if secondLevelAreaExcelData then
            exploreDegreeTotal = exploreDegreeTotal + secondLevelAreaExcelData.ExploreDegree
        end
    end
    
    local exploreDegreeCompleted = self:GetCurExploreDegree(firstLevelId)
    local completePercent = math.min(tonumber(string.format("%.2f", (exploreDegreeCompleted / exploreDegreeTotal))), 1)
    return completePercent, exploreDegreeCompleted, exploreDegreeTotal
end

---区域提示: 二级区域进入提醒
function DiscoverySystem:SecondLevelEnterRemind(AreaFieldID)
    local planeID = Game.MapSystem:GetCurrentPlaneID()
    if planeID and planeID > 0 then
        local planeInfo = Game.SceneUtils.GetPlaneConfProxy(planeID)
        if planeInfo and not planeInfo.AreaTips then 
            return 
        end
    end
	if AreaFieldID then
		local secondLevelAreaExcelData = Game.TableData.GetExploreSecondLevelAreaDataTable()
		for secondLevelId, secondLevelInfo in ksbcpairs(secondLevelAreaExcelData) do
			if secondLevelInfo.AreafieldID and secondLevelInfo.AreafieldID == AreaFieldID then
				local firstLevelExcelData = Game.TableData.GetExploreFirstLevelAreaDataRow(secondLevelInfo.ParentArea)
				if firstLevelExcelData then
					Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EXPLORE_AREA, {{secondLevelInfo.Name, },{firstLevelExcelData.Name,}})
				end
				return
			end
		end
	end
end

function DiscoverySystem:GetExploreDegreeTipsScoreInfo(firstLevelId)
    local levelInfo = Game.TableData.GetExploreFirstLevelAreaDataRow(firstLevelId)
    local scoreList = levelInfo.RewardExplore
    local curScore = self:GetCurExploreDegree(firstLevelId)
    local firstLevelAreaInfo = Game.me.ExploreUnlockInfoMap[firstLevelId]
    local rewardProgress = firstLevelAreaInfo and firstLevelAreaInfo.RewardProgress or {}
    local showItemIdx = 0
    local canReward = false
    while showItemIdx < #scoreList do
        showItemIdx = showItemIdx + 1
        if curScore >= scoreList[showItemIdx] then
            if not rewardProgress[showItemIdx] then
                canReward = true
                break
            end
        else
            break
        end
    end
    return canReward, curScore, showItemIdx
end

function DiscoverySystem:ExploreDegreeUpgradeRemind(firstLevelId, old, new)
    self.model:RecordLastDegreeUpdateInfo(_G._now())
    self:TryShowDegreeUpgradeRemind(firstLevelId, old, new)
    -- Game.ItemSystem:ReceiveRemindData({firstLevelId = firstLevelId, diff = diff, ui = "P_MapExploreTopTip"})
end

function DiscoverySystem:GetCurExploreDegree(firstLevelId)
    return Game.me.ExploreUnlockInfoMap[firstLevelId] and Game.me.ExploreUnlockInfoMap[firstLevelId].FinishedExploreDegree or 0
end

function DiscoverySystem:OnGetExploreReward(firstLevelId)
    local canReward, _, _ = self:GetExploreDegreeTipsScoreInfo(firstLevelId)
    if canReward then
        local curValue = self:GetCurExploreDegree(firstLevelId)
        self.model:RecordLastDegreeUpdateInfo(_G._now())
        self:TryShowDegreeUpgradeRemind(firstLevelId, curValue, curValue)
    end
end

function DiscoverySystem:TryShowDegreeUpgradeRemind(firstLevelId, oldValue, curValue)
    local lastTime = self.model.lastDegreeUpdateTime
    if not lastTime or _G._now() - lastTime > Game.TableData.GetConstDataRow("EXPLORE_DEGREE_UPDATE_REMIND_TIME") then
        return
    end
    local scoreList = Game.TableData.GetExploreFirstLevelAreaDataRow(firstLevelId).RewardExplore
    if curValue <= scoreList[#scoreList] then
        Game.HUDSystem:ShowUI("P_MapExploreTopTip", firstLevelId, oldValue, curValue)
    end
end

function DiscoverySystem:CheckTabRewardRedpoint(firstLevelID)
    local firstLevelAreaInfo = Game.me.ExploreUnlockInfoMap[firstLevelID]
    if not firstLevelAreaInfo then
        return false
    end
    local rewardProgress = firstLevelAreaInfo.RewardProgress or {}
    local rewardData = Game.TableData.GetExplorationProgressRewardDataRow(firstLevelID)
    for index, rewardInfo in ksbcpairs(rewardData) do
        if firstLevelAreaInfo.FinishedExploreDegree >= rewardInfo.RewardExplore and not rewardProgress[index] then
            return true
        end
    end
    return false
end

function DiscoverySystem:CheckAllRewardRedpoint()
    local data = Game.TableData.GetExploreFirstLevelAreaDataTable()
    for firstLevelID, _ in ksbcpairs(data) do
        if self:CheckTabRewardRedpoint(firstLevelID) then
            return true
        end
    end
    return false
end

return DiscoverySystem