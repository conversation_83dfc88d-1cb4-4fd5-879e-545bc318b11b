local DiscoveryModelSender = DefineClass("DiscoveryModelSender", SystemSenderBase)

function DiscoveryModelSender:ReqExploreSoulSubmit(soulId, stage)
    self.Bridge:ReqExploreSoulSubmit(soulId, stage)
end

function DiscoveryModelSender:ReqSetTracingMapTag(MapTagID)--客户端自己维护的TagID
    self.Bridge:ReqSetTracingMapTag(MapTagID)
end

function DiscoveryModelSender:ReqCustomTracingAdd(CustomTagID, mapID, tagName, PosX, PosY)
    self.Bridge:ReqCustomTracingAdd(CustomTagID, mapID, tagName, PosX, PosY)
end

function DiscoveryModelSender:ReqCustomTracingRemove(tagUid)
    self.Bridge:ReqCustomTracingRemove(tagUid)
end

function DiscoveryModelSender:ReqCustomTracingUpdate(tagUid, tagName, customTagID)
    self.Bridge:ReqCustomTracingUpdate(tagUid, tagName, customTagID)
end

function DiscoveryModelSender:ReqExploreReceiveReward(firstLevelId, rewardIndex)
    self.Bridge:ReqExploreReceiveReward(firstLevelId, rewardIndex)
end

return DiscoveryModelSender