---@class WBP_DedicateRewardItemView : WBP_DedicateRewardItem_C
---@field public WidgetRoot WBP_DedicateRewardItem_C
---@field public Is Recieved boolean
---@field public Type number
---@field public PreConstruct fun(self:self,IsDesignTime:boolean):void
---@field public Set Type fun(self:self,Type:number):void
---@field public Set Recieved fun(self:self,Is Recieved:boolean):void


---@class P_DedicateRewardItemView : WBP_DedicateRewardItemView
---@field public controller P_DedicateRewardItem
local P_DedicateRewardItemView = DefineClass("P_DedicateRewardItemView", UIView)

function P_DedicateRewardItemView:OnCreate()
    local controller = self.controller
    controller:SetAutoBind(false)

---Auto Generated by UMGExtensions
	self.AnimationInfo = {AnimFadeIn = {},AnimFadeOut = {}}
end

function P_DedicateRewardItemView:OnDestroy()
end

return P_DedicateRewardItemView
