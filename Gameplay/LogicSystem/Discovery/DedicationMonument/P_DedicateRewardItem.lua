---@class P_DedicateRewardItem : UIComponent
---@field public View WBP_DedicateRewardItemView
local P_DedicateRewardItem = DefineClass("P_DedicateRewardItem", UIComponent)
local ItemReward = kg_require "Gameplay.LogicSystem.Item.ItemReward"
local ESlateVisibility = import("ESlateVisibility")

function P_DedicateRewardItem:OnCreate()
    self.rewardClass = self:BindComponent(self.View.WBP_ItemReward, ItemReward) --- luacheck:ignore 
end

function P_DedicateRewardItem:RefreshItem(info, bReceive, selected)
    self.View:SetReceived(bReceive)
    self.rewardClass:FillItem(info[1], true, Enum.EItemSelectType.NotSelected)
    local itemExcelData = Game.TableData.GetItemNewDataRow(info[1])
	if itemExcelData then
		self.View.Text_Name:SetText(itemExcelData.itemName)
		self.View.Item_Num:SetText("x"..info[2])
		local bIsSpecial = itemExcelData.tipConsecrationSpecial or false
		self.View:SetLight(bIsSpecial)
		if bIsSpecial then
			self.View.KPanel_Tag:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		else
			self.View.KPanel_Tag:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
	local action, param = Game.BagSystem:GetSystemActionConfig(info[1])
	if action == "AddMaxStaminaValue" then
		self.View:SetInfoDisplay(true)
		self.View.Text_Num:SetText(param or "")
	else
		self.View:SetInfoDisplay(false)
	end
    self.View:ForceLayoutPrepass()
end

function P_DedicateRewardItem:OnClickItemBtn()
    
end

function P_DedicateRewardItem:OnHide()

end



return P_DedicateRewardItem