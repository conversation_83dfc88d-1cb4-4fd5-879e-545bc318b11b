local ColorManager = DefineClass("ColorManager")
local UIFunctionLibrary = import("UIFunctionLibrary")
local DataTableFunctionLibrary = import("DataTableFunctionLibrary")
local KGColorData = import("KGColorData")

ColorManager.Type = {
    LinearColor = 0,
    SlateColor = 1,
    Color8Bits = 2 -- FColor
}

--DataTable资源路径
ColorManager.DTPath = {
    Common = UIAssetPath.DT_Common,
    Face = UIAssetPath.DT_Face,
    PharmacistColor = UIAssetPath.DT_PharmacistColor,
    TaskIconColor = UIAssetPath.DT_TaskIconColor,
    PVPColor = UIAssetPath.DT_PVPColor,
    HUDColor = UIAssetPath.DT_HUD_Color,
	TraceColor = UIAssetPath.DT_Trace_Color
}

function ColorManager:ctor()
    self.colorDataTB = {}
end

function ColorManager:Init()
    for key, path in pairs(ColorManager.DTPath) do
        local res = slua.loadObject(path)
        if IsValid_L(res) then
            slua.addRef(res)
            self.colorDataTB[key] = res
        end
    end
end

function ColorManager:GetColor(TableName, ColorName, InColorType)
    local DataTable = self.colorDataTB[TableName]
    if not DataTable then
        Log.Warning("Cannot find such a datatable!")
        return nil
    end

    local OutColorData = KGColorData()
    local Result = false
    Result = DataTableFunctionLibrary.Generic_GetDataTableRowFromName(DataTable, ColorName, OutColorData)
    if not Result then
        Log.Warning("Cannot find such color!")
        return nil
    end

    if not InColorType or InColorType == ColorManager.Type.LinearColor then
        return OutColorData.Value:clone() --需要返回个新的struct，value的parent会被gc,使用时会报错
    elseif InColorType == ColorManager.Type.SlateColor then
        local SlateValue = UIFunctionLibrary.LinerColorToSlateColor(OutColorData.Value)
        return SlateValue
    elseif InColorType == ColorManager.Type.Color8Bits then
        local FColorValue = OutColorData.Value:ToFColor(true)
        return FColorValue
    end
end

function ColorManager:UnInit()
    for k, v in pairs(self.colorDataTB) do
        slua.removeRef(v)
    end
    table.clear(self.colorDataTB)
end

return ColorManager
