local systemConst = require("Data.Config.System.SystemConfig")

---@class SystemManager:ManagerBase
---@field systemList SystemBase[]
local SystemManager = DefineClass("SystemManager", ManagerBase)
local ipairs = ipairs
local pairs = pairs

function SystemManager:onCtor()
    self.systemList = {}
    self.initOnDisconnectModelList = {}
    self.initOnLogoutModelList = {}
end

function SystemManager:onInit()
    self:initAllSystem()
end

function SystemManager:initAllSystem()
    for _, config in ipairs(systemConst.SYSTEM_CONFIG) do
        xpcall(self.createSystem, _G.CallBackError, self, config)
    end
end

function SystemManager:createSystem(config)
    local systemName = config[1]
    local systemPath = config[2]
    local systemIns = Game[systemName] or kg_require(systemPath).new() or require(systemPath).new()
    systemIns:Init()
    Game[systemName] = systemIns
    self.systemList[#self.systemList + 1] = systemIns
end

function SystemManager:RegisterModelOfInitOnDisconnect(model)
    self.initOnDisconnectModelList[#self.initOnDisconnectModelList + 1] = model
end

function SystemManager:RegisterModelOfInitOnLogout(model)
    self.initOnLogoutModelList[#self.initOnLogoutModelList + 1] = model
end

function SystemManager:OnLogin()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnLogin then
            xpcall(self.systemList[i].OnLogin, _G.CallBackError, self.systemList[i])
        end
    end
end

function SystemManager:OnBackToLogin()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnBackToLogin then
            xpcall(self.systemList[i].OnBackToLogin, _G.CallBackError, self.systemList[i])
        end
    end

    for i, v in pairs(self.initOnLogoutModelList) do
        xpcall(v.Clear, _G.CallBackError, v)
    end
end

function SystemManager:OnBackToSelectRole()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnBackToSelectRole then
            xpcall(self.systemList[i].OnBackToSelectRole, _G.CallBackError, self.systemList[i])
        end
    end

    for i, v in pairs(self.initOnLogoutModelList) do
        xpcall(v.Clear, _G.CallBackError, v)
    end
end

function SystemManager:OnReLogin()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnReLogin then
            xpcall(self.systemList[i].OnReLogin, _G.CallBackError, self.systemList[i])
        end
    end
end

function SystemManager:OnNetConnected()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnNetConnected then
            xpcall(self.systemList[i].OnNetConnected, _G.CallBackError, self.systemList[i])
        end
    end
end

function SystemManager:OnNetDisconnected()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnNetDisconnected then
            xpcall(self.systemList[i].OnNetDisconnected, _G.CallBackError, self.systemList[i])
        end
    end
    
    for i, v in pairs(self.initOnDisconnectModelList) do
        xpcall(v.Clear,_G.CallBackError,v)
    end
end

function SystemManager:OnMemoryWarning()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnMemoryWarning then
            xpcall(self.systemList[i].OnMemoryWarning, _G.CallBackError, self.systemList[i])
        end
    end
end

function SystemManager:OnObjectCountNearlyExceed(currentObjectCount)
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnObjectCountNearlyExceed then
            xpcall(self.systemList[i].OnObjectCountNearlyExceed, _G.CallBackError, self.systemList[i], currentObjectCount)
        end
    end
end

function SystemManager:AfterPlayerInit()
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].AfterPlayerInit then
            xpcall(self.systemList[i].AfterPlayerInit, _G.CallBackError, self.systemList[i])
        end
    end
end

function SystemManager:OnWorldMapLoadComplete(levelId)
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnWorldMapLoadComplete then
            xpcall(self.systemList[i].OnWorldMapLoadComplete, _G.CallBackError, self.systemList[i], levelId)
        end
    end
end

function SystemManager:OnWorldMapDestroy(levelId)
    for i = 1, #self.systemList, 1 do
        if self.systemList[i].OnWorldMapDestroy then
            xpcall(self.systemList[i].OnWorldMapDestroy, _G.CallBackError, self.systemList[i], levelId)
        end
    end
end

function SystemManager:OnMainPlayerCreate()
	for i = 1, #self.systemList, 1 do
		if self.systemList[i].OnMainPlayerCreate then
			xpcall(self.systemList[i].OnMainPlayerCreate, _G.CallBackError, self.systemList[i])
		end
	end
end

function SystemManager:OnMainPlayerDestroy()
	for i = 1, #self.systemList, 1 do
		if self.systemList[i].OnMainPlayerDestroy then
			xpcall(self.systemList[i].OnMainPlayerDestroy, _G.CallBackError, self.systemList[i])
		end
	end
end




function SystemManager:uninitAllSystem()
    for _, systemIns in ipairs(self.systemList) do
        xpcall(systemIns.UnInit, _G.CallBackError, systemIns)
    end
end

function SystemManager:onUnInit()
    self:uninitAllSystem()
end

return SystemManager