-- luacheck: push ignore
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local bit = require("Framework.Utils.bit")

function AddAnimFeatureLayerLinkFlag(featureLinkFlag, curFlagValue)
    return bit.bor(featureLinkFlag, curFlagValue)
end

function RemoveFeatureLayerLinkFlag(featureLinkFlag, curFlagValue)
    return bit.band(bit.bnot(featureLinkFlag), curFlagValue)
end

function HasFeatureLayerLinkFlag(featureLinkFlag, curFlagValue)
    return bit.band(featureLinkFlag, curFlagValue)
end

function HasAnyFeatureLayerLinkFlag(featureLinkFlag)
    return featureLinkFlag > 0
end

-- 暂时未在使用, 后续可能用于local performance内部的优先级
---@class LOCAL_PERFORMANCE_ANIM_TYPE
LOCAL_PERFORMANCE_ANIM_TYPE = LOCAL_PERFORMANCE_ANIM_TYPE or {
    InteractEnd = 0, -- 交互结束
    HitPerformance = 1, -- 轻受击
	MorphPlay = 2, -- 变身表演
	ServerControl = 3, -- 服务器调用播放
}
LOCAL_PERFORMANCE_NEED_LISTEN_INTERRUPT = LOCAL_PERFORMANCE_NEED_LISTEN_INTERRUPT or {
	[LOCAL_PERFORMANCE_ANIM_TYPE.InteractEnd] = true,
	[LOCAL_PERFORMANCE_ANIM_TYPE.HitPerformance] = true,
	[LOCAL_PERFORMANCE_ANIM_TYPE.MorphPlay] = false,
	[LOCAL_PERFORMANCE_ANIM_TYPE.ServerControl] = true,
}



------------ViewControlComponent-------------
DEFAULT_SLOT_CONST = "DefaultSlot"
ACTION_SLOT_CONST = "Action"
PERFORM_ADDITIVE_SLOT_CONST = "PerformAdditive"
LOWER_LOCO_LAYER_SLOT_CONST = "LowerLoco"
UPPER_ACTION_SLOT_CONST = "Upper"
FACE_SLOT_CONST = "Face"
LINKED_ANIM_GRAPH_CONST = "Action"
LINKED_FACE_ANIM_CONST = "Face"
ACTION_ANIMATION_BLEND_IN_TIME = 0.1
ACTION_ANIMATION_BLEND_OUT_TIME = 0.1
ANIM_LIB_ANIMATION_BLEND_OUT_TIME = 0.2

--=================Anim Layer Feature===============================
ANIM_FEATURE_LAYER_TAGS = {
    KAWAII = "Kawaii",
    BODY_POSTURE = "BodyPosture",
    GAZE = "Gaze",
    FACE_ANIM = "FaceAnim",
    IK = "IK",
}

ANIM_FEATURE_LAYER_FLAG = {
    [ANIM_FEATURE_LAYER_TAGS.KAWAII] = bit.lshift(1, 0),
    [ANIM_FEATURE_LAYER_TAGS.BODY_POSTURE] = bit.lshift(1, 1),
    [ANIM_FEATURE_LAYER_TAGS.GAZE] = bit.lshift(1, 2),
    [ANIM_FEATURE_LAYER_TAGS.FACE_ANIM] = bit.lshift(1, 3),
    [ANIM_FEATURE_LAYER_TAGS.IK] = bit.lshift(1, 4),
}

ANIM_FEATURE_LAYER_CONFIG = {
    [ANIM_FEATURE_LAYER_TAGS.KAWAII] = {ViewBudgetTag = WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.KWAII_CLOTH_SIMULATE},
    [ANIM_FEATURE_LAYER_TAGS.BODY_POSTURE] = {ViewBudgetTag = WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.BODY_POSTURE_IK},
    [ANIM_FEATURE_LAYER_TAGS.GAZE] = {ViewBudgetTag = WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.GAZE},
    [ANIM_FEATURE_LAYER_TAGS.FACE_ANIM] = {ViewBudgetTag = WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.FACE_PERFORMANCE},
    [ANIM_FEATURE_LAYER_TAGS.IK] = {ViewBudgetTag = WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.BODY_POSTURE_IK},
}

----------------Face Layer-----------------------------------------
FACE_LAYER_INVALID_PERFORM_MODE = -1
FACE_LAYER_NO_PERFORM_MODE = 0
FACE_LAYER_LIP_PERFORM_MODE = 1 -- 口型+眨眼 + 基础表情静帧

FACE_PROPERTY_FACE_BLEND_KEY = 'FaceBlend'

IDLE_FIGHT_ANIM = "Idle_Fight"
IDLE = "Idle"
IDLE_RANDOM = "Idle_Random"
IDLE_COMBAT = "Idle_Combat"
-- ================Anim Load Management ==========================
ANIM_LOCO_CONTAINER = ANIM_LOCO_CONTAINER or {
	-- 目前先支持3个优先级
	CONTAINER_SIZE = 4,
	
	-- ===========索引=============
    -- 注意1：目前的动画替换优先级规则大致如下：
    -- 同一优先级同时只能存在一个AnimSet，仅和优先级有关，和实际替换的动画类型无关，不做手动取消直接替换会触发报错
    
    -- 如果某业务的动画替换与其他业务并不冲突（允许其他业务同时替换），则该业务需要单独占一个优先级。
    -- 如果有冲突，则需要具体问题具体分析，配置合理的优先级。

    -- 注意2：下面的优先级需要连续（会被用于数组寻址），且新增时需要同时维护上面的CONTAINER_SIZE

	BASIC_LOCO_PRIORITY = 0,
	MEDIUM_LOCO_PRIORITY = 1,
	HIGH_LOCO_PRIORITY = 2,
	FORTH_LOCO_PRIORITY = 3,
	
	-- ==========语意Tag, 需要 >= 0 ==========
	-- 这里扩展, 请找@孙亚 确认
	BASIC_LOCO_SEMANTIC = 0,  -- 根据单位配置初始化的最基础的动作能力集合
	FASHION_LOCO_OVERRIDE_SEMANTIC = 1, 	--[玩家/NPC] [ 时装替换基础动作: idle/walk/run...]
	WEAPON_LOCO_OVERRIDE_SEMANTIC= 2,       -- [玩家] [切武器替换待机] [Idle_Fight] @sunya
    IDLEANIM_LOCO_OVERRIDE_SEMANTIC= 3,     -- [怪物] [出生动画替换] [Idle] [需要在场编里配置] @chengdong  
											-- [Boss] [Boss的战斗姿态和非战斗姿态切换] [Idle]  @俊杰
											-- [捏脸角色] [不同时装要换基础站立姿态] [Idle] @滕飞
	MOUNT_LOCO_OVERRIDE_SEMANTIC = 4,		-- [玩家] 坐骑类型替换骑乘动画
	
	-- ==========语义->优先级===========
	SEMANTIC_TO_LOCO_PRI_INDEX = {
	
	},
	
	-- 只是为了省一个table的开销
	PRIORITY_TO_VARIABLE_NAME = {
		
	}
}

-- 不同优先级上是可以业务并存的, 相同优先级业务要保障互斥使用
ANIM_LOCO_CONTAINER.SEMANTIC_TO_LOCO_PRI_INDEX[ANIM_LOCO_CONTAINER.BASIC_LOCO_SEMANTIC] = ANIM_LOCO_CONTAINER.BASIC_LOCO_PRIORITY
ANIM_LOCO_CONTAINER.SEMANTIC_TO_LOCO_PRI_INDEX[ANIM_LOCO_CONTAINER.FASHION_LOCO_OVERRIDE_SEMANTIC] = ANIM_LOCO_CONTAINER.MEDIUM_LOCO_PRIORITY
ANIM_LOCO_CONTAINER.SEMANTIC_TO_LOCO_PRI_INDEX[ANIM_LOCO_CONTAINER.WEAPON_LOCO_OVERRIDE_SEMANTIC] = ANIM_LOCO_CONTAINER.HIGH_LOCO_PRIORITY
ANIM_LOCO_CONTAINER.SEMANTIC_TO_LOCO_PRI_INDEX[ANIM_LOCO_CONTAINER.IDLEANIM_LOCO_OVERRIDE_SEMANTIC] = ANIM_LOCO_CONTAINER.HIGH_LOCO_PRIORITY
ANIM_LOCO_CONTAINER.SEMANTIC_TO_LOCO_PRI_INDEX[ANIM_LOCO_CONTAINER.MOUNT_LOCO_OVERRIDE_SEMANTIC] = ANIM_LOCO_CONTAINER.FORTH_LOCO_PRIORITY

ANIM_LOCO_CONTAINER.PRIORITY_TO_VARIABLE_NAME[ANIM_LOCO_CONTAINER.BASIC_LOCO_PRIORITY] = 'BasicLocoAnimLoadID'
ANIM_LOCO_CONTAINER.PRIORITY_TO_VARIABLE_NAME[ANIM_LOCO_CONTAINER.MEDIUM_LOCO_PRIORITY] = 'MediumLocoAnimLoadID'
ANIM_LOCO_CONTAINER.PRIORITY_TO_VARIABLE_NAME[ANIM_LOCO_CONTAINER.HIGH_LOCO_PRIORITY] = 'HighLocoAnimLoadID'
ANIM_LOCO_CONTAINER.PRIORITY_TO_VARIABLE_NAME[ANIM_LOCO_CONTAINER.FORTH_LOCO_PRIORITY] = 'ForthLocoAnimLoadID'


ANIM_BASE_POSTURE_TYPE = ANIM_BASE_POSTURE_TYPE or {
    Normal = 0,     -- 默认状态
    Battle = 1,     -- 服务器仇恨列表定义的进战状态
    Patrol = 2      -- 巡逻状态
}

ANIM_FANCY_IDLE_CONTAINER = ANIM_FANCY_IDLE_CONTAINER or {
    -- 表演动作：高优先级覆盖低优先级，同一时刻仅有一个会生效
    DEFAULT_FANCY_PRIORITY = 1,
    BASIC_FANCY_PRIORITY = 2,
	MEDIUM_FANCY_PRIORITY = 3,
	HIGH_FANCY_PRIORITY = 4,

    -- ==========语意Tag, 需要 >= 0 ==========
    DEFAULT_FANCY_SEMANTIC = 1,
	BASIC_FANCY_SEMANTIC = 2,            -- 职业默认花式待机动画
	FASHION_FANCY_SEMANTIC = 3,          -- 时装系统
	VEHICLE_FANCY_SEMANTIC = 4,          -- 载具花式待机
    	
	-- ==========语义->优先级===========
	SEMANTIC_TO_PRI_INDEX = {
	
	},
}

ANIM_FANCY_IDLE_CONTAINER.SEMANTIC_TO_PRI_INDEX[ANIM_FANCY_IDLE_CONTAINER.DEFAULT_FANCY_SEMANTIC] = ANIM_FANCY_IDLE_CONTAINER.DEFAULT_FANCY_PRIORITY
ANIM_FANCY_IDLE_CONTAINER.SEMANTIC_TO_PRI_INDEX[ANIM_FANCY_IDLE_CONTAINER.BASIC_FANCY_SEMANTIC] = ANIM_FANCY_IDLE_CONTAINER.BASIC_FANCY_PRIORITY
ANIM_FANCY_IDLE_CONTAINER.SEMANTIC_TO_PRI_INDEX[ANIM_FANCY_IDLE_CONTAINER.FASHION_FANCY_SEMANTIC] = ANIM_FANCY_IDLE_CONTAINER.MEDIUM_FANCY_PRIORITY
ANIM_FANCY_IDLE_CONTAINER.SEMANTIC_TO_PRI_INDEX[ANIM_FANCY_IDLE_CONTAINER.VEHICLE_FANCY_SEMANTIC] = ANIM_FANCY_IDLE_CONTAINER.HIGH_FANCY_PRIORITY


NPC_COMPLEX_MOVE_PARAM = NPC_COMPLEX_MOVE_PARAM or {
	[0] = {StartTime = 0.47, StopTime = 0.2},
	[1] = {StartTime = 0.47, StopTime = 0.45},
	[2] = {StartTime = 0.47, StopTime = 0.2}
}


ANIMLIB_LOCOMOTION_GROUP_NAMES = {
	OnGround = 'OnGround',
}

EAnimationSlotType = EAnimationSlotType or {
	[DEFAULT_SLOT_CONST] = 0,
	[ACTION_SLOT_CONST] = 1,
	[PERFORM_ADDITIVE_SLOT_CONST] = 2,
	[LOWER_LOCO_LAYER_SLOT_CONST] = 3,
	[UPPER_ACTION_SLOT_CONST] = 4,
	[FACE_SLOT_CONST] = 5,
}

EActionBlendRule = EActionBlendRule or {
	FullBodyOverride = 0,
	PartBodyOverride = 1,
	PartBodyOverrideOnlyWhenMoving = 2
}

EAnimationBlendBone = EAnimationBlendBone or {
	SpineBaseBone = "spine_01",
	SpineTopBone = "spine_03",
	NeckBaseBone = "neck_01",
	None = "None"
}

EUpperAnimationBlendType = EUpperAnimationBlendType or {
	UpperBodyBlend = 1,
	UpperLimbBodyBlend = 2,
	HeadBlend = 3,
}

EUpperAnimationBlendBone = ELowerAnimationBlendBone or {
	[1] = "spine_01",
	[2] = "spine_03",
	[3] = "neck_01",
}

EAnimPlayReqTag = EAnimPlayReqTag or {
	MeshAnim = 1,
	SimpleAnim = 2,
	Performance = 3,
	PerformanceOnPartBody = 4,
	UpperPerformance = 5,
	LocalPerformance = 6,
	Face = 7,
	FullHit = 8,
	PartHit = 9,
	ExpandState = 10,
	AdditiveAnim = 11,
	AdditiveAnimPartBody = 12,
	FullBodySkill = 13,
	PartBodySkill = 14,
	Mount = 15,
	RotateComponent = 16,
	LocalUpperPerformance = 17,
    MountUpper = 18,
	LocalPerformance_Hit = 19,
	LocalPerformance_Mount_Upper = 20,
	LocalPerformance_Morph = 21,
}

EAnimPlayReqTagDefaultConfig = EAnimPlayReqTagDefaultConfig or {
	[EAnimPlayReqTag.MeshAnim] = {DEFAULT_SLOT_CONST},
	[EAnimPlayReqTag.SimpleAnim] = {DEFAULT_SLOT_CONST},
	[EAnimPlayReqTag.Performance] = {LOWER_LOCO_LAYER_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.PerformanceOnPartBody] = {ACTION_SLOT_CONST, EActionBlendRule.PartBodyOverride, EAnimationBlendBone.SpineBaseBone, 2},
	[EAnimPlayReqTag.UpperPerformance] = {ACTION_SLOT_CONST, EActionBlendRule.PartBodyOverrideOnlyWhenMoving, EAnimationBlendBone.SpineBaseBone, 2},
	[EAnimPlayReqTag.LocalPerformance] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.Face] = {FACE_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.FullHit] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.PartHit] = {ACTION_SLOT_CONST, EActionBlendRule.PartBodyOverride, EAnimationBlendBone.SpineBaseBone, 2},
	[EAnimPlayReqTag.ExpandState] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.AdditiveAnimPartBody] = {PERFORM_ADDITIVE_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 1},
	[EAnimPlayReqTag.AdditiveAnimPartBody] = {PERFORM_ADDITIVE_SLOT_CONST, EActionBlendRule.PartBodyOverride, EAnimationBlendBone.SpineBaseBone, 1},
	[EAnimPlayReqTag.FullBodySkill] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.PartBodySkill] = {ACTION_SLOT_CONST, EActionBlendRule.PartBodyOverride, EAnimationBlendBone.SpineBaseBone, 2},
	[EAnimPlayReqTag.Mount] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.RotateComponent] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.LocalUpperPerformance] = {ACTION_SLOT_CONST, EActionBlendRule.PartBodyOverride, EAnimationBlendBone.SpineBaseBone, 2},
	[EAnimPlayReqTag.MountUpper] = {ACTION_SLOT_CONST, EActionBlendRule.PartBodyOverrideOnlyWhenMoving, EAnimationBlendBone.SpineBaseBone, 2},
	[EAnimPlayReqTag.LocalPerformance_Hit] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
	[EAnimPlayReqTag.LocalPerformance_Mount_Upper] = {ACTION_SLOT_CONST, EActionBlendRule.PartBodyOverrideOnlyWhenMoving, EAnimationBlendBone.SpineBaseBone, 2},
	[EAnimPlayReqTag.LocalPerformance_Morph] = {ACTION_SLOT_CONST, EActionBlendRule.FullBodyOverride, EAnimationBlendBone.None, 2},
}
