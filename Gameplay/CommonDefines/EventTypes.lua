---@type table
EEventTypes = {
	--Login
	LOGIN_QUEUE_UPDATE = "",	--登录排队信息更新
	LOGIN_SWITCH_SERVER = "",	--已登录后切换服务器

    --Entity
    ENTITY_COUNT_CHANGED = "",  --Entity数量变化
	
    -- ROLE MSG
    ROLE_ON_BORN = "",       --角色出生
    ROLE_ON_DEAD = "",
    ROLE_ON_VISABLE_CHANGED = "", --角色可见变化通知

    --ROLE_HP_CHANGED = "",
    --ROLE_MAX_HP_CHANGED = "",
    --ROLE_CURRENT_MAX_HP_CHANGED = "",
    --ROLE_ASHIELD_CHANGED = "",
    ROLE_ON_TELEPORT_FAR = "",
    ROLE_EXP_CHANGED = "",
    ON_PROFESSION_PROP_1_CHANGED = "",
    ON_PROFESSION_PROP_2_CHANGED = "",
    ON_PROFESSION_PROP_3_CHANGED = "",

    --职业机制
    ON_PROFESSION_SUNPROP_1_CHANGED = "",
    ON_PROFESSION_UTOPROP_1_CHANGED = "",
    ON_PROFESSION_ARBPROP_1_CHANGED = "",
    ON_PROFESSION_APPRPROP_3_CHANGED = "",
    ON_PROFESSION_WARPROP_1_CHANGED = "",
    ON_PROFESSION_FOOLPROP_1_CHANGED = "",

    ON_ROLE_ATTR_BRIEF = "",
    ON_CREATE_ROLE = "",
    ON_Delete_ROLE = "",
	ON_CHANGE_ROLE_NAME = "",

    -- LOCOMOTION MSG
    LOCO_DEFAULT_LOCO_STATE_CHANGE = "",
    LOCO_STATE_CHANGE = "",
	LOCO_START_CHANGE = "",
    LOCO_TRIGGER_DODGE_SUCCESS = "",
    LOCO_TRIGGER_JUMP_SUCCESS = "",

	
    -- SKILLHUD
    SKILLHUD_BTNPRESSED = "",
    SKILLHUD_BTNRELEASED = "",
    SKILLHUD_BTNCLEAR = "",
    SKILLHUD_JOYSTICKMOVED = "",
    SKILLHUD_SHOWFOREWARN = "",           -- 显示激活但未显示的预警
    SKILLHUD_COOLDOWNUPDATE = "",         -- 技能冷却信息更新（本地事件）
    SKILLHUD_COMBOUPDATE = "",            -- 技能Combo信息更新（本地事件）
    SKILLHUD_LOOPSKILLSTARTED = "",       -- 循环技能更新（本地事件）
    SKILLHUD_SKILLJUMPDISABLE = "",       -- 跳跃禁用（本地事件）
    SKILLHUD_DODGEDISABLE = "",           -- 闪避禁用（本地事件）
    SKILLHUD_REFRESH_SKILL_ACTIVATE_RESULT = "",    -- 刷新激活技能结果（本地事件）
    SKILLHUD_REMINDUSESKILL_CHANGED = "", -- 提醒使用技能信息改变
    SKILLHUD_REMINDUSESKILL_UPDATE = "",  -- 提醒使用技能更新表现
    SKILLHUD_SKILLINTOCD_BY_SKILL_TASK = "", --表现上进入Combo CD（只并不是真的）
    SKILLHUD_UNLOCK_SKILL_lIST_CHANGE = "",
    SKILLHUD_AUTOSKILL_SWITCH_CHANGE = "", -- 客户端自动连招开关改变
    On_ADDITION_SKILL_CD_BEGIN = "", -- 仆杖寻路技能cd
    SKILLHUD_PROP_ATTR_CHANGED = "",---角色专属属性变化
    SKILLHUD_DISABLE_SKILL = "",--禁用状态，例如眩晕，跳跃
    SKILLHUD_PROFESSION_Q_LOCK_CHANGE= "",-- 职业Q技能解锁状态变化
	SKILLHUD_ON_CARD_DEBUFF_END = "", -- 卡牌减益结束
	SKILLHUD_ON_SKILL_BTN_FIRE_ANIM = "",
	SKILLHUD_ON_NORMAL_ATTACK_ACTION_TRIGGERED = "",
	SKILLHUD_ON_NORMAL_ATTACK_ACTION_RELEASED = "",
	
    -- SKILL
    SKILL_POST_SKILL_EQUIP_CHANGED = "",
    SKILL_RELEASE_TYPE_SRT_AIM_ACTIVE = "",
    -- UIEVENT
    ON_UI_REFRESH_END = "",
    ON_HIDE_ALL_PANEL = "",
    ON_RESTORE_ALL_PANEL = "",
    
    -- GameLoop
    GAMELOOP_POST_GAMELOOP_STARTUP = "",
    GAMELOOP_PRE_GAMELOOP_SHUTDOWN = "",
    GAME_MAINPALYER_LOGIN = "",
    GAME_MAINPLAYER_RE_LOGIN = "", --切换角色或重登
    GAME_MAINPLAYER_CREATEME = "", --主角创建
    GAME_ENTER_STAGE_SHOW_UI_END = "",
    GAME_MAINPLAYER_ENTITY_CREATE_FAILED = "", --主角Entity创建失败
	GAMELOOP_ENTER_GAME_STATE_UPDATE = "",
    -- GameLoopStates
    GAMELOOP_ON_STATE_CHANGED = "",
    GAMELOOP_ON_ENTER_LOGIN = "",
    GAMELOOP_ON_CONNECT = "",
    GAMELOOP_ON_CONNECT_FAILED_IN_GAME = "",
    GAMELOOP_ON_LOSE_CONNECTION = "", --断线
    GAMELOOP_ON_RECONNECT_SUCC = "",  --断线重连成功
    GAMELOOP_ON_RECONNECT_SAME_SPACE = "",
    GAMELOOP_RETURN_LOGIN_CLEAR = "",
    -- GAMELOOP_ON_ROLE_SELECT_CANCELED = "",
    GAMELOOP_ON_ROLE_ENTER_GAME = "",
    GAMELOOP_PRE_ENTER_GAME_FINISH = "",
    GAMELOOP_ON_RETRUN_ROLE_SELECT = "",
    GAMELOOP_ON_SPECIAL_KICK_OUT = "",

    --Interactive
    -- Server
    SERVER_ON_STAGGER_INFO = "",
    SERVER_ON_BE_STAGGER_INFO = "",

    -- Popup
    UI_POPTIPS_REMOVE = "",

    -- Team
    

    -- 侧边栏切换
    HUD_JOYSTICK_MOVE_START = "",
    HUD_ON_SELECT_TARGET = "",
    ON_HUD_ROUTTLE_STATE_CHANGED = "",
    On_ADDITION_SKILL_ADD = "",
    On_ADDITION_SKILL_DEL = "",
	On_ADDITION_SKILL_FINISH = "",
    On_ADDITION_SKILL_BREAK = "",
    -- 破防
    HUD_BREAKDEFENCE_MAXVALUE_CHANGE = "",
    HUD_BREAKDEFENCE_CURVALUE_CHANGE = "",
    HUD_BREAKDEFENCE_ISLOCK_CHANGE = "",
    HUD_BREAKDEFENCE_SAN_FULLTIME = "",
    --Common
    SEND_KEYBOARDNUM = "",
    ASIDE_AUDIO_BREAK = "",

    -- DebugInfo
    DEBUG_INFO_UPDATE = "",

    -- Equipment
    EQUIP_INFO_UPDATED = "",
    EQUIP_ENTRY_LOCKED = "",
    EQUIP_LAST_RESULT = "",
    EQUIP_RESULT_PANEL = "",
    ON_CLIENT_EQUIP_PLAN_CHANGE = "",
    ON_EQUIP_PLAN_NAME_CHANGE = "",
    ON_EQUIP_PLAN_SWITCH = "",
	ON_EQUIP_SWITCH_FIXED = "",
	ON_EQUIP_CLEAR_EXTRA = "",
	ON_EQUIP_BAN_CLOSED = "",
	ON_EQUIP_BAN_WORD = "",
	ON_EQUIP_SELECT_PASTE = "",
	ON_EQUIP_SMEAR = "",
	ON_EQUIP_RESET_FILTER = "",
	ON_EQUIP_ADJUST_FILTER = "",
	ON_EQUIP_CLOSE_FILTER = "",
	ON_EQUIP_BAN_PROP_CHANGED = "",

    -- teamArena
    TEAM_ARENA_STATUS_CHANGE = "",
    TEAM_ARENA_MEMBER_STATUS_CHANGE = "",
    TEAM_ARENA_MEMBER_HONOR_CHANGE = "",
    TEAM_ARENA_MEMBER_SELECT_CHANGE = "",
    TEAM_ARENA_MEMBER_HEAD_STATUS_CHANGE = "",
    TEAM_ARENA_HIGH_LIGHT = "",
    TEAM_ARENA_SPACE_CHANGE = "",
    TEAM_ARENA_RECEIVE_BATTLE_STAT = "",
    TEAM_ARENA_RECEIVE_MEMBER_SKILL_DETAIL = "",
    TEAM_ARENA_UPDATE_SCORE = "",
    TEAM_ARENA_BATTLE_ROUND_RESULT = "",
	TEAM_ARENA_CHAMPION_BATTLE_ROUND_RESULT = "",
    GROUP_OCCUPY_BATTLE_ROUND_RESULT = "",
    TEAM_ARENA_PVP_SEASON_STATS = "",
    TEAM_ARENA_SINGLE_GAME_STATS = "",
    TEAM_ARENA_ALL_GAME_STATS = "",
    TEAM_ARENA_MATCH_STATE_CHANGE = "",
    TEAM_ARENA_MATCH_CONFIRM_STATE_CHANGE = "",
    TEAM_ARENA_ON_LIKED = "",
    TEAM_ARENA_RANK_REWARD_STATE_CHANGE = "",
    TEAM_ARENA_WANTED_CHANGE = "",
    TEAM_ARENA_SET_SETTLEMENT_SCORE = "",
	TEAM_ARENA_RECV_REWARD = "",
	TEAM_ARENA_MEMBER_REVIVE_COUNT_CHANGE = "",

    -- 12V12
    CONTINUS_KILL_NUM_UPDATE = "",

    --- 
    --Dialogue Plot 剧情对话
    DIALOGUE_PLOT_ON_START = "",
    DIALOGUE_PLOT_ON_END = "",
    DIALOGUE_PLOT_ON_POST_COMPLETE = "",
    DIALOGUE_PLOT_SHOT_START = "",      --连续播放多段对话时，除第一段外，其余每段对话开始时触发，
    DIALOGUE_PLOT_SHOT_END = "",        --连续播放多段对话时，除最后一段外，其余每段对话结束时触发，
    CINEMATIC_ON_START = "", --开始Cutsccene或者对话
    CINEMATIC_ON_END = "",   --结束Cutsccene或者对话
    DIALOGUE_COUNTDOWN_TICK = "", --对话倒计时逐帧更新进度
    DIALOGUE_COUNTDOWN_AUTOCHOICE = "", --对话倒计时结束强制选择
    DIALOGUE_TIMER_CLEAR = "", --对话倒计时状态取消
    DIALOGUE_ON_SELECT_OPTION = "", --选择一个对话选项后

    -- new dialogue
    ON_DIALOGUE_PANEL_CLICKED = "", -- 面板点击
    ON_DIALOGUE_PRINT_START = "", -- 开始打字
    ON_DIALOGUE_PRINT_FINISH = "", -- 结束打字
    ON_DIALOGUE_SKIP_CONTENT = "", -- 跳过台本
    ON_DIALOGUE_PANEL_OPENED = "", -- 界面打开
    CLOSE_DIALOGUE_BG_PANEL = "", -- 关闭画幅UI
    CLOSE_DIALOGUE_TAROT_CARD_PANEL = "", -- 关闭塔罗牌UI
    ON_DIALOGUE_QTE_SUCCESS = "", -- 对话内QTE成功
    DIALOGUE_SCREEN_TEXT_TICK = "", -- 黑屏白字/白屏黑字初始化
    DIALOGUE_SCREEN_TEXT_INIT = "", -- 黑屏白字/白屏黑字Tick

    --自动寻路
    AUTO_NAVIGATION_START = "", --自动寻路开始
    AUTO_NAVIGATION_END = "",   --自动寻路结束（可能是正常结束，也可能是异常结束）
    AUTO_NAVIGATION_COMPLETE= "", --自动寻路完成
    AUTO_NAVIGATION_FAIL= "", --自动寻路异常结束
    AUTO_NAVIGATION_INCREMENT = "",--路点的起始角标更�?
    AUTO_NAVIGATION_UPDATE = "",--路点更新

    --Map
    --MAP_ON_MAPDATA_READY = "",
    MAP_ON_TRACE_SET = "",
    ON_MAP_TAG_INFO_UPDATE = "",
    ON_MAP_TAG_TEAM_RETARGET_UPDATE = "",
    ON_MAP_CHARACTER_ENTER_AOI = "",
    ON_MAP_CHARACTER_LEAVE_AOI = "",
    ON_MAP_TAG_TEAM_INFO_UPDATE = "",
    ON_MAP_TELEPORT_UNLOCK = "",
    ON_MAPUI_RATIO_CHANGED = "",

    -- Rank
    ON_REFRESH_RANK_DATA = "",


    --Store
    STORE_UPDATE_BUY_LIMIT = "",
    ALL_SERVERLIMIT_GOODS_UPDATE = "",
	ON_RET_BUY_GOODS_SUCCESS = "",

    --BossInfo
    BOSSINFO_TRIGGET_BOSS_COUNT_DOWN = "",
    BOSSINFO_TRIGGET_BOSS_COUNT_DOWN = "",
    BOSS_INFO_ADD_BOSE = "",
    BOSS_INFO_REMOVE_BOSS = "",
    BOSS_INFO_WARNING_REMINDER = "",
    BOSS_HP_PERCENT_CHANGED = "",
	
    --- Equip
    ON_EQUIP_CHANGE = '',
    ON_REPLACEABLEEQUIP_UI_CLOSE = '',
    ON_EQUIP_BAR_CHANGE = '',
    EQUIP_BASE_ENHANCE = '',
    EQUIP_BASE_TO_MAX = '',
    EQUIP_RANDOM_UP = '',
    EQUIP_RANDOM_PICK = '',
    EQUIP_FIX_LOCK = '',
    EQUIP_ABSORB = '',
    EQUIP_EXTRACT = '',
    ON_EQUIP_SLOT_ENHANCE = '',
    ON_DEF_EQUIP_UPGRADE = '',
    ON_DEF_EQUIP_PROMOTE = '',
    ON_DEF_EQUIP_WORD_CHANGE = '',
    EQUIP_EXCHANGE = '',
    --- newEquip
    ON_EQUIP_FORGING = '',
    ON_EQUIP_CONDENSE = '',
    ON_EQUIP_CONDENSE_APPLY = '',
    ON_EQUIP_CONDENSE_ALLRESET = '',
    ON_EQUIP_CONDENSE_RESET = '',
    ON_EQUIP_CONDENSE_UP = '',
    ON_EQUIP_CONDENSE_DOWN = '',
    ON_EQUIP_ENHANCED_ACT = '',
    ON_EQUIP_ENHANCED_REF = '',
    ON_EQUIP_CLOTTING = '',
    --- Sealed
    ON_SEALED_UPGRADE = '',
    ON_SEALED_UPGRADE_BACK = '',
    ON_SEALED_PROMOTE = '',
    ON_SEALED_EQUIP = '',
    ON_SEFIROT_CORE_CHANGE = '',
    ON_SEALED_REFINE = '',
    ON_SEALED_APPLY = '',
    ON_SEALED_RESET = '',
    SEALED_DECOMPOSE_BATCH = '',
    ON_SEALED_LOCK = '',
    ON_SEALED_XMAT_UPDATE = '',
    ON_SEALED_XMAT_FILTER_UPDATE = '',
    ON_SEALED_XMAT_FILTER_WORD_UPDATE = '',
    ON_SEALED_XMAT_FUSE_SELECT_UPDATE = '',
    ON_SEALED_XMAT_FUSE_UPDATE = '',
    ON_SEALED_XMAT_SELECT_FUSE = '',
    ON_SEALED_XMAT_EQUIP_CLOSE = '',
    ON_SEALED_XMAT_EQUIP_OPEN = '',
    ON_SEALED_PROGRESS_REWARD_COLLECTED = '',
    ON_SEALED_ALL_PROGRESS_REWARDS_COLLECTED = '',



	
	
    --TITLE&HONORIFIC
    ON_TITLE_HONORIFIC_UPDATE = "",--已拥有的称号或者头衔列表更新
    ON_WEAR_TILE_UPDATE = "", --佩戴称号或者头衔更新
    ON_TITLE_HONORIFIC_EXPIRE = "", --已有的限时称号&头衔过期
	
	
	
    -- mantor

    --Gacha

    --50v50
    ARENA50V50_GAME_RESULT = "",
    ARENA50V50_ENTER_ARENA = "",

    --Props
    ON_HP_CHANGED = "",
	ON_MAX_HP_CHANGED = "",
    ON_IS_DEAD_CHANGED = "",
    ON_CAMP_CHANGED = "",
    ON_INBATTLE_CHANGED = "",
    ON_MORPHID_CHANGED = "",

    --GroupInfo
    

    

    
    CLIENT_GROUP_INVITE_CHANGED = "",
    CLIENT_GROUP_CONFIRM_UPDATE = "",
    CLIENT_GROUP_CONFIRM_END = "",
    SERVER_RET_CONVENE = "",
    CLIENT_GROUP_MEMBER_EXCHANGE = "",
    GROUP_LEAGUE_DEL_GROUP = "",
    
    ON_GROUP_LEADER_UID_CHANGED = "",
    ON_GROUP_TEAM_LEADER_UID_CHANGED = "",
    ON_GROUP_FOLLOW_STAGE_CHANGED = "",
	ON_JOIN_GROUP = "",
	ON_QUIT_GROUP = "",

    CLIENT_GROUP_CAN_RESCUE_CHANGED = "",
    --Team Prop
    ON_TEAMMEMBER_NAME_CHANGED = "",
    ON_TEAMMEMBER_PROFESSION_CHANGED = "",
    ON_TEAMMEMBER_PROFESSIONSTATEID_CHANGED = "",
    ON_TEAMMEMBER_LEVEL_CHANGED = "",
    ON_TEAMMEMBER_ISCAPTAIN_CHANGED = "",
    ON_TEAMMEMBER_ISONLINE_CHANGED = "",
    ON_TEAMMEMBER_HP_CHANGED = "",
    ON_TEAMMEMBER_MAXHP_CHANGED = "",
    ON_TEAMMEMBER_ISDEAD_CHANGED = "",
    ON_TEAMMEMBER_BFOLLOWING_CHANGED = "",
    ON_TEAMMEMBER_BTARGETBYBOSS_CHANGED = "",
    ON_TEAMMEMBER_LOCATION_CHANGED = "",
    ON_TEAMMEMBER_MAPINSTID_CHANGED = "",
    ON_TEAMMEMBER_MAPORLINE_CHANGED = "",
    ON_TEAMMEMBER_VOICESTATE_CHANGED = "",
    ON_TEAMMEMBER_MEMBERFLAG_CHANGED = "",

    ON_TEAMID_CHANGED = "",
    ON_SELF_TEAM_TARGETID_CHANGED = "",
    ON_MAINPLAYER_GROUP_ID_CHANGED = "",
    ON_TEAMMATCH_CHANGED = "",
    ON_SINGLEMATCH_CHANGED = "",
    ON_FOLLOWSTATE_CHANGED = "",
    ON_SELF_CAPTAIN_CHANGED = "",
    ON_SELF_LEVEL_CHANGED = "",
    ON_SELF_SPIRIT_CHANGED = "",
    ON_SELF_ISONLINE_CHANGED = "",
    ON_GROUP_BLOCK_VOICE = "",
    ON_SELF_BTARGETBYBOSS_CHANGED = "",
    ON_SELF_MATCHLIST_CHANGED = "",
    ON_TEAM_POSITION_NEED_CHANGED = "",
    ON_TEAM_ZHANLI_LIMIT_CHANGED = "",
    ON_TEAM_BLOCK_VOICE_CHANGED = "",
    CLIENT_SCENE_MARK_CAHNGED = "",
    CLIENT_GROUP_MEMBER_MARK_CAHNGED = "",
    ON_TEAM_MARK_VISIBLE_CHANGED = "",
    ---马车属性同步事件
    ON_SELF_VEHICLESEATINDEX_CHANGED = "",
    ON_SELF_CAN_ALLOW_ACCELERATE_CHANGED = "",
    ON_SELF_ACCELERATE_CD_TIMESTAMP_CHANGED = "",
    ON_SELF_GAME_PLAY_TYPE_CHANGED = "",

    --AllInSdk
    PLATFORM_LOGOUT = "",             --登出
    PLATFORM_CHANGE_SERVER = "",      --修改服务器选择
    PLATFORM_NECESSARY_DATA_GET = "", --登录流程中，获取平台必要信息成功

    --Trace
    TRACE_ON_TASK_QUEST_CURRENT_TASK_CHANGED = "",
    TRACE_ON_CURRENT_TRACE_CHANGED = "",
	TRACE_ADD_MAP_TAG_EDGE_TAG = "",
	TRACE_REMOVE_EDGE_TAG = "",
	TRACE_ADD_NORMAL_TRACE_TAG = "",
	TRACE_REMOVE_NORMAL_TRACE_TAG = "",
	
    --consignment 货币寄售

    --进入选角阶段
    ENTER_SELECT_ROLE_STAGE = "",
    ROLE_SEQUENCE_END = "",
    CREATE_ROLE_ENERT_END = "", ---创角入场动画播放结束
	
	--奇遇
	ON_GIFT_FATE_TRIGGERED = "",
	ON_GIFT_FATE_FINISHED = "",

    ---Condition and Trigger
    ON_ASYNC_TRIGGER_INFO = "",
    ON_TRIGGER_CURRENT_REFRESH = "",
    ON_TRIGGER_TEMP_CLEAR = "",
    ON_TRIGGER_TEMP_CHANGE = "",
    ON_TRIGGER_FOREVER_CLEAR = "",
    ON_TRIGGER_FOREVER_CHANGE = "",
    ON_TRIGGER_COMPLETE = "",
	ON_CLIENT_TRIGGER_UPDATE = "",

    --世界boss
    PLAYER_TRIGGER_WORLDBOSS_AREA_CHANGEUI = "",
    PLAYER_TRIGGER_WORLDBOSS_AREA = "",
    WORLD_BOSS_STATISTICS_UPDATE = "",
    WORLD_BOSS_RANK_OTHER_UPDATE = "",
    WORLD_BOSS_RANK_SELF_UPDATE = "",
    WORLD_BOSS_LINE_UPDATE = "",

    --boss aggro
    BOSS_AGGROTARGET_CHANGED = "",

    --对战模式与红名
	
    --交易所
    ON_RETURN_STALL_INFO = "",
    ON_PUT_ON_STALL_ITEM_CALLBACK = "",
    ON_PUT_OFF_STALL_ITEM_CALLBACK = "",
    ON_GET_TAB_PUBLICITY_GOODS_BACK = "",
    ON_GET_TAB_SELLING_GOODS_BACK = "",
    MYSTALL_DATA_CHANGE = "",
    ON_GET_TAB_All_GOODS_BACK = "",
    ON_GET_PUBLICITY_GOODS_BYID_BACK = "",
    ON_GET_SELLING_GOODS_BYID_BACK = "",
    ON_FOLLOW_TYPE_BACK = "",
    ON_FOLLOW_INSTNANCE_BACK = "",
    ON_SERVER_ALL_FOLLOW_ITEMS_BACK = "",
    ON_CLIENT_ALL_FOLLOW_ITEMS_CHANGED = "",
    ON_GET_ITEMINFO_BY_ID_BACK = "",
    ON_INCOME_CAHNGE = "",
    ON_REPUT_ON_SHELVE_BACK = "",

    --拍卖行
    ON_AUCTION_BEGIN = "",
    ON_WORLD_AUCTION_DATA_BACK = "",
    ON_GUILD_AUCTION_DATA_BACK = "",
    ON_WORLD_AUCTION_SIMPLE_ITEM_CHANGE = "",
    ON_GUILD_AUCTION_SIMPLE_ITEM_CHANGE = "",
    ON_WORLD_AUCTION_HISTORY_BACK = "",
    ON_GUILD_AUCTION_HISTORY_BACK = "",


    ON_INGAME_START = "", ---玩法开始
	

    --Lock
    LOCKDISTANCE_CHANGE = "",           --锁定玩家出屏幕范围
    

    --屏幕特效

    -- 问价&心情查看

	--settings deprecated
	SETTING_ON_SET_SHOW_SKILL_TAG = "",

    --无形之手
    INVISIBLE_HAND_HAS_NEARBY_INTERACTOR_CHANGE = "",
    ON_INVISIBLE_HAND_SKILLList_CHANGE = "",
    
    -- 有RpcEntity的Npc在剧情中说话
    DIALOGUE_NPC_ACTOR_TALK = "",

    --设置操作模式
    OPERATOR_MODE_CHANGE = "",
    
    --游戏阶段切换
    GAMELOOP_ON_ENTER_STAGE = "",

    --成就系统
	
	-- 新手引导
	NEWBIE_GUIDE_GROUP_FINISH = "",
	
	HUD_SET_SIDE_PANEL_FOLD = "",
	
	----Surburb Discovery System
	BALL_COUNT_CHANGE = "",
	SELF_SOCIAL_ACTION_AUTOSTOP = "",
}

for K, _ in pairs(_G.EEventTypes or {}) do
    _G.EEventTypes[K] = tostring(K)
end


EPropEventTypes = EPropEventTypes or {
    ["Hp"] = EEventTypes.ROLE_HP_CHANGED,                                    --已经处理
    ["MaxHp"] = EEventTypes.ROLE_MAX_HP_CHANGED,                             --已经处理
    ["CurrentMaxHp"] = EEventTypes.ROLE_CURRENT_MAX_HP_CHANGED,              --已经处理
    ["aShield"] = EEventTypes.ROLE_ASHIELD_CHANGED,                          --已经处理
    ["Exp"] = EEventTypes.ROLE_EXP_CHANGED,                                  --已经处理

    ["Camp"] = EEventTypes.ON_CAMP_CHANGED,                                  --已经处理
    ["teamID"] = EEventTypes.ON_TEAMID_CHANGED,                              --已经处理
    ["guildId"] = EEventTypes.ON_GUILDID_CHANGED,                            --已经处理
    ["groupID"] = EEventTypes.ON_MAINPLAYER_GROUP_ID_CHANGED,                           --已经处理
    ["isInTeamMatch"] = EEventTypes.ON_TEAMMATCH_CHANGED,                    --已经处理
    ["isInSingleMatch"] = EEventTypes.ON_SINGLEMATCH_CHANGED,                --已经处理
    ["FollowState"] = EEventTypes.ON_FOLLOWSTATE_CHANGED,                    --已经处理
    ["isCaptain"] = EEventTypes.ON_SELF_CAPTAIN_CHANGED,                     --已经处理
    ["teamTargetID"] = EEventTypes.ON_SELF_TEAM_TARGETID_CHANGED,            --已经处理
    ["Level"] = EEventTypes.ON_SELF_LEVEL_CHANGED,                           --已经处理
    ["IsOnline"] = EEventTypes.ON_SELF_ISONLINE_CHANGED,                     --已经处理
    ["bTargetedByBoss"] = EEventTypes.ON_SELF_BTARGETBYBOSS_CHANGED,         --已经处理
    ["singleMatchInfoList"] = EEventTypes.ON_SELF_MATCHLIST_CHANGED,         --done
    ["teamPositionNeedList"] = EEventTypes.ON_TEAM_POSITION_NEED_CHANGED,    --done
    ["teamZhanliLimit"] = EEventTypes.ON_TEAM_ZHANLI_LIMIT_CHANGED,          --done
    --["teamBlockVoices"] = EEventTypes.ON_TEAM_ZHANLI_LIMIT_CHANGED, --沒用到

    ["teamInfoList.name"] = EEventTypes.ON_TEAMMEMBER_NAME_CHANGED,                   --done
    ["teamInfoList.profession"] = EEventTypes.ON_TEAMMEMBER_PROFESSION_CHANGED,       --done
    ["teamInfoList.level"] = EEventTypes.ON_TEAMMEMBER_LEVEL_CHANGED,                 --done
    ["teamInfoList.isCaptain"] = EEventTypes.ON_TEAMMEMBER_ISCAPTAIN_CHANGED,         --done
    ["teamInfoList.isOnline"] = EEventTypes.ON_TEAMMEMBER_ISONLINE_CHANGED,           --done
    ["teamInfoList.hp"] = EEventTypes.ON_TEAMMEMBER_HP_CHANGED,                       --done
    ["teamInfoList.maxHp"] = EEventTypes.ON_TEAMMEMBER_MAXHP_CHANGED,                 --done
    ["teamInfoList.isDead"] = EEventTypes.ON_TEAMMEMBER_ISDEAD_CHANGED,               --done
    ["teamInfoList.bFollowing"] = EEventTypes.ON_TEAMMEMBER_BFOLLOWING_CHANGED,       --done
    ["teamInfoList.bTargetByBoss"] = EEventTypes.ON_TEAMMEMBER_BTARGETBYBOSS_CHANGED, --done
    ["teamInfoList.location"] = EEventTypes.ON_TEAMMEMBER_LOCATION_CHANGED,           --done
    ["teamInfoList.worldID"] = EEventTypes.ON_TEAMMEMBER_MAPORLINE_CHANGED,               --done
    ["teamInfoList.mapInstID"] = EEventTypes.ON_TEAMMEMBER_MAPINSTID_CHANGED,
    ["teamInfoList.voiceState"] = EEventTypes.ON_TEAMMEMBER_VOICESTATE_CHANGED,       --done
    ["teamInfoList.memberflag"] = EEventTypes.ON_TEAMMEMBER_MEMBERFLAG_CHANGED,

    ["taskQuestCurrentTrack"] = EEventTypes.TRACE_ON_TASK_QUEST_CURRENT_TASK_CHANGED, --暂不处理
    ["quickMedicineItemId"] = EEventTypes.ON_QUICK_MEDICINE_ITEM_ID_CHANGED,          --done
    --团队属性
    ["groupLeaderUid"] = EEventTypes.ON_GROUP_LEADER_UID_CHANGED,                     --done

    ---公会活动Begin
    ["guildAnswerIndex"] = EEventTypes.ON_GUILD_ANSWER_INDEX_CHANGED, --done
    ---公会活动End
    --对战模式与红名
    --["FightModeType"] = EEventTypes.ON_PLAYER_BATTLE_MODE_CHANGED,             --模式类型 --done
    --["Bounty"] = EEventTypes.ON_PLAYER_BOUNTY_CHANGED,                         --悬赏度 --done
    --["YellowNameTime"] = EEventTypes.ON_PLAYER_YELLOW_NAME_TIME_CHANGED,       --黄名开始时间 --没使用到。
    --["FightRelationship"] = EEventTypes.ON_PLAYER_FIGHT_RELATION_SHIP_CHANGED, --敌对关系列表 --done
}

function GetPropChangeEventName(Key)
    return EPropEventTypes[Key]
end
