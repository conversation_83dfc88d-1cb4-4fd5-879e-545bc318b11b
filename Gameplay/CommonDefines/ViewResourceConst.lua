-- luacheck: push ignore

-- MODEL LIB 引用
MODELLIB_Prop_4000007 = "Prop_4000007"  --RolePlay_Mask_R
MODELLIB_Prop_4000008 = "Prop_4000008" --RolePlay_Mask_L

LEVEL_MANGER_MPC_PATH = '/Game/Arts/Effects/FX_Special/FX_Zdcj/MPC_Standard002.MPC_Standard002'

FACE_ANIM_COMPONENT_BP_PATH = "/Game/Blueprint/3C/Animation/FaceAnim/Audio2Face/BP_Audio2FaceComponent.BP_Audio2FaceComponent_C"

CAMERA_DITHER_CHARACTER_DEFAULT_OLM_PATH = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_OLM/MR_Basic_OLM_Char.MR_Basic_OLM_Char"
CAMERA_DITHER_ENV_DEFAULT_OLM_PATH = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_OLM/MR_Basic_OLM_Env.MR_Basic_OLM_Env"
CAMERA_DEBUG_FREE_ACTOR = "/Game/Blueprint/3C/Camera/BP_DebugFreeActor.BP_DebugFreeActor_C"

PPMPHANTOM = {
	MaterialPath = "/Game/Arts/Effects/FX_Library/FX_Materials/FX_C8_Materials/PostProcessFX/M_PP_Phantom_Inst.M_PP_Phantom_Inst",
	EffectPath = "/Game/Arts/Effects/System/Ghost/NS_Ghost.NS_Ghost"
}

PPMSKETCH = {
	MaterialPath = "/Game/Arts/MaterialLibrary/PostProcess/PP_SketchSimple_Inst.PP_SketchSimple_Inst",
	CharacterFaceMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_Character/MR_Sketch/MR_SketchBase_Face.MR_SketchBase_Face",
	CharacterEyeMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_Character/MR_Sketch/MR_SketchBase_Eye.MR_SketchBase_Eye",
	CharacterHairMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_Character/MR_Sketch/MR_SketchBase_Hair.MR_SketchBase_Hair",
	CharacterSkinMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_Character/MR_Sketch/MR_SketchBase.MR_SketchBase",
	CharacterClothMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_Character/MR_Sketch/MR_SketchBase_AO.MR_SketchBase_AO",
	
	BodyNormalTexturePath = "/Game/Arts/Effects/FX_Library/FX_Textures/T_Sketch_Normal.T_Sketch_Normal",
}

BP_MODEL_PREVIEW = {
	Actor = "/Game/Editor/ModelEditor/BP_ModelPreviewActor.BP_ModelPreviewActor_C",
	Avator = "/Game/Editor/ModelEditor/BP_ModelPreview_Avator.BP_ModelPreview_Avator_C"
}

DIALOGUE = {
	DialogueBlackScreenType = "/Game/Blueprint/DialogueSystem/Enum/DialogueBlackScreenType.DialogueBlackScreenType",
	DialogueAnchorType = "/Game/Blueprint/DialogueSystem/Enum/DialogueAnchorType.DialogueAnchorType",
	DialogueContentUIType = "/Game/Blueprint/DialogueSystem/Enum/DialogueContentUIType.DialogueContentUIType",
	DialogueHideNpcType = "/Game/Blueprint/DialogueSystem/Enum/DialogueHideNpcType.DialogueHideNpcType",
	WBP_HUDAside = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Aside/WBP_HUDAside.WBP_HUDAside_C",
	WBP_NPCCS = "/Game/Arts/UI_2/Blueprint/NPC/WBP_NPCCS.WBP_NPCCS_C",
    WBP_CSStyle = "/Game/Arts/UI_2/Blueprint/NPC/Dialogue/WBP_Dialogue_CSStyle.WBP_Dialogue_CSStyle_C",
    DialogueFadeInOutColor = "/Game/Blueprint/DialogueSystem/Enum/DialogueFadeInOutColor.DialogueFadeInOutColor",
	FadeCurve = "/Game/Blueprint/DialogueSystem/FadeCurve.FadeCurve"
}

BP = {
	BP_SceneText = "/Game/Blueprint/SceneActor/BP_SceneText.BP_SceneText_C",
	BP_Dialogue = "/Game/Blueprint/SceneActor/BP_Dialogue.BP_Dialogue_C",
	BP_EstatePortal = "/Game/Blueprint/SceneActor/BP_EstatePortal.BP_EstatePortal_C",
    BP_VolumeFogBase = "/Game/Arts/SceneActorBP/VolumeFog/BP_VolumeFogBase.BP_VolumeFogBase_C"
}

ABP = {
	ABP_Player_R4 = "/Game/Blueprint/3C/Animation/AnimPublic/Player/ABP_Player_R4.ABP_Player_R4_C",
	ABP_AOI_Player_R4 = "/Game/Blueprint/3C/Animation/AnimPublic/Player/ABP_AOI_Player_R4.ABP_AOI_Player_R4_C",
	ABP_NPC_M2 = "/Game/Blueprint/3C/Animation/AnimPublic/NPC/ABP_NPC_M2.ABP_NPC_M2_C",
	ABP_BU_Player_R4 = "/Game/Blueprint/3C/Animation/AnimPublic/Player/ABP_BU_Player_R4.ABP_BU_Player_R4_C"
}

MOUNT = {
	CurveBikeSeatAndHeadOffset = "/Game/Blueprint/3C/Curve/Mount/CurveBikeSeatAndHeadOffset.CurveBikeSeatAndHeadOffset",
}

NS_VFX_Link = "/Game/Arts/Effects/FX_Envrinment/DancingParty/Line/NS_VFX_Link.NS_VFX_Link"
NS_AirWallColission_Wave = '/Game/Arts/Effects/FX_Envrinment/AirWall/AirWallColission/NS_AirWallColission_Wave.NS_AirWallColission_Wave'
NS_BookCorridor = "/Game/Arts/Effects/FX_Envrinment/PirateSchool_P/BookCorridor/NS_BookCorridor.NS_BookCorridor"
NS_ChongGaoZhiLing = "/Game/Arts/Effects/System/Chonggaozhiling/Standby/NS_ChongGaoZhiLing.NS_ChongGaoZhiLing"
NS_SoundTrace01 = "/Game/Arts/Effects/System/SoundTrace/NS_SoundTrace01.NS_SoundTrace01"
NS_Scene_SpiritualitywallStart1 = "/Game/Arts/Effects/FX_Envrinment/Scene/Spiritualitywall/NS_Scene_SpiritualitywallStart1.NS_Scene_SpiritualitywallStart1"

LSAE = {
	Mirror_InactiveMaterialPath = "/Game/Arts/Environment/Mesh/Props/SM_Mirror/M_VFX_FerGlass_Inst.M_VFX_FerGlass_Inst",
	Mirror_ActiveMaterialPath = "/Game/Arts/Environment/Mesh/Props/SM_Mirror/MI_Mirror01.MI_Mirror01",
	SpiritualityWall_BlendIn_Curve = "/Game/Blueprint/Curve/SpiritualityWall_BlendIn_Curve.SpiritualityWall_BlendIn_Curve",
	SpiritualityWall_BlendOut_Curve = "/Game/Blueprint/Curve/SpiritualityWall_BlendOut_Curve.SpiritualityWall_BlendOut_Curve"
}

M_PP_LOWLIFE_QUICK = "/Game/Arts/Effects/FX_Library/FX_Materials/M_PP_LowLife_Quick.M_PP_LowLife_Quick"

BOSS_TYRE_ANIM_IDLE_POSTURE_CHANGE_MAP = BOSS_TYRE_ANIM_IDLE_POSTURE_CHANGE_MAP or {
    [0] = {
        [1] = "NormalToRandomIdle",
        [2] = "NormalToCombatIdle",
    },
    [1] = {
        [0] = "RandomToNormalIdle",
        [2] = "RandomToCombatIdle",
    },
    [2] = {
        [0] = "CombatToNormalIdle",
    },
}  

ANIM_SEGMENT_LOADING_ABSORB = "Teleport_Absorbed" --非凡事件loading被吸入时的动作

NPC_BP_KEY = {
	NPC = "NPC",
	Npc_FaceControl = "Npc_FaceControl"
}

SIMPLE_C7ACTOR_BP = "/Game/Blueprint/3C/Actor/SimpleC7Actor.SimpleC7Actor_C"
SIMPLE_C7CHARACTER_BP =  "/Game/Blueprint/3C/Actor/SimpleC7Character.SimpleC7Character_C"

PLAYER_CHARACTER_BP = '/Game/Blueprint/3C/BP_PlayerCharacter.BP_PlayerCharacter_C'
AOI_PLAYER_CHARACTER_BP = '/Game/Blueprint/3C/Actor/BP_AOI_PlayerCharacter.BP_AOI_PlayerCharacter_C'
BRIEF_ACTOR_BP = "/Game/Blueprint/3C/Actor/BP_BriefActor.BP_BriefActor_C"

NPC_MONSTER_BP = "/Game/Blueprint/3C/Actor/BP_Npc_Monster.BP_Npc_Monster_C"
DIALOGUE_ACTOR_BP = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C" --比BP_Npc_Monster多了Audio2FaceComponent
CUTSCENE_DYNAMIC_ACTOR_BP = "/Game/Blueprint/3C/Actor/BP_CutSceneDynamicActor.BP_CutSceneDynamicActor_C" --比BP_Npc_Monster多了Audio2FaceComponent,LookAtComponent

ATTACH_ITEM_BASE_BP = "/Game/Blueprint/3C/Equipment/BP_AttachItemBase.BP_AttachItemBase_C"
MANOR_PLACE_HOLDER_BP = "/Game/Blueprint/Manor/BP_ManorPlaceHolder.BP_ManorPlaceHolder_C"
LUNITENTITY_INSTANCE_BP = "/Game/Blueprint/BattleSystem/Actor/BPU_TableCarrier.BPU_TableCarrier_C"

-- EntityClassName: BP Path
BP_USED_BY_ENTITY = BP_USED_BY_ENTITY or {
	MainPlayer = PLAYER_CHARACTER_BP,
	AvatarActor =AOI_PLAYER_CHARACTER_BP,
	NPC = SIMPLE_C7CHARACTER_BP,
	Npc_FaceControl = NPC_MONSTER_BP, -- FaceControlComponent 支持捏脸
	BriefAvatarActor = BRIEF_ACTOR_BP,
	BriefNpcActor = BRIEF_ACTOR_BP,
	IceField = "/Game/Blueprint/LogicActor/BP_IceField.BP_IceField_C", --todo 可以走MODELLIB
	LargeMarineLife = SIMPLE_C7CHARACTER_BP,
	AttachItemBase = ATTACH_ITEM_BASE_BP,
	SimpleAnimAttachItem = ATTACH_ITEM_BASE_BP,
	AttachItem = ATTACH_ITEM_BASE_BP,
	LocalWeaponItem = ATTACH_ITEM_BASE_BP,
	LocalDisplayChar = NPC_MONSTER_BP,
	LocalDisplayNpc = NPC_MONSTER_BP,
	TraceNavigator = NPC_MONSTER_BP,
	LocalNpcBase = NPC_MONSTER_BP,
	DialogueLocalEntity = DIALOGUE_ACTOR_BP,
	TraceLine = "/Game/Blueprint/3C/Core/BP_TaskTrackLineActor.BP_TaskTrackLineActor_C",
	LocalBuildGridEntity = "/Game/Blueprint/SceneActor/BP_ManorGrid.BP_ManorGrid_C",
	LocalBuildEffectLineEntity = "/Game/Blueprint/Manor/BP_ManorLineEffect.BP_ManorLineEffect_C",
	LocalBuildNodeEditBase = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeEditComponent = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeEditFloor = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeEditFurniture = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeEditPillar = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeEditStair = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeEditWall = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeRuntimeBase = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeRuntimeComponent = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeRuntimeFloor = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeRuntimeFurniture = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeRuntimePillar = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeRuntimeStair = MANOR_PLACE_HOLDER_BP,
	LocalBuildNodeRuntimeWall = MANOR_PLACE_HOLDER_BP,
	LocalCutSceneActor = CUTSCENE_DYNAMIC_ACTOR_BP,
	LocalControllableNPC = NPC_MONSTER_BP,
	LocalControllableNPCRegion = "/Game/Blueprint/InvisibleHand/BP_ControlRegionTemplate.BP_ControlRegionTemplate_C",
	LocalMountEntity = "/Game/Blueprint/3C/Actor/BP_LocalMount.BP_LocalMount_C",
	LocalPerformPetEntity = SIMPLE_C7CHARACTER_BP,
	CommonInteractor = "/Game/Blueprint/SceneActor/BP_CommonInteractor.BP_CommonInteractor_C",
	BulletEntity = "/Game/Blueprint/3C/Actor/BP_Bullet.BP_Bullet_C",
	LocalScenePlacementEntity = "/Game/Blueprint/SceneDisplay/BP_MeshComponent.BP_MeshComponent_C",
	LocalFurnitureEntity = "/Game/Blueprint/Manor/BP_MeshFurniture.BP_MeshFurniture_C",
	LocalManorDisplayFurniture = "/Game/Blueprint/Manor/BP_MeshFurniture.BP_MeshFurniture_C",
	GhostEntity = NPC_MONSTER_BP,
	DecalEntity = "/Game/Blueprint/3C/Actor/BP_Decal.BP_Decal_C",
	ForewarnEntity = "/Game/Blueprint/BattleSystem/BP_ForewarnActor.BP_ForewarnActor_C",
	CreateRoleCharLocalEntity =CUTSCENE_DYNAMIC_ACTOR_BP,
	-- 没有逻辑的cutScene单位，只用于绑定cutScene位置
	LocalCutSceneEntity = LUNITENTITY_INSTANCE_BP,

	-- 新对话Entity
	DialoguePerformer = DIALOGUE_ACTOR_BP,
	DialogueCamera = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    DialogueRoutePoint = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
    DialogueNiagara = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorEffect.BP_DialogueSceneActorEffect_C",
	DialogueModel = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorModel.BP_DialogueSceneActorModel_C",
	DialogueLight = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorLight.BP_DialogueSceneActorLight_C",
	
	LUnitNEntity = LUNITENTITY_INSTANCE_BP,
	LUnitAuraNEntity = LUNITENTITY_INSTANCE_BP,
	LUnitSpellAgentNEntity = LUNITENTITY_INSTANCE_BP,
	LUnitSpellFieldNEntity = LUNITENTITY_INSTANCE_BP,
	LUnitTrapNEntity = LUNITENTITY_INSTANCE_BP,
	LUnitVelocityFieldNEntity = LUNITENTITY_INSTANCE_BP,
	
	LocalRideMountNpc = NPC_MONSTER_BP,
	OccupyDetectArea = SIMPLE_C7CHARACTER_BP,
	LocalCaptureMagicPoint = SIMPLE_C7CHARACTER_BP,
    ShapeTrigger = "/Game/Blueprint/SceneActor/BP_ShapeTrigger.BP_ShapeTrigger_C",
	
	AnimTestLocalEntity = SIMPLE_C7CHARACTER_BP
}



POST_PROCESS_MATERIAL_PATH = {
    SharedCommon = "/Game/Arts/Effects/FX_Library/FX_Materials/FX_C8_Materials/PostProcessFX/M_PP_Common.M_PP_Common",
    SharedBlinkClip = "/Game/Arts/MaterialLibrary/PostProcess/PP_Blink_Inst.PP_Blink_Inst",
	FogMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_PostProcess/MR_PP_DistanceFog.MR_PP_DistanceFog"
}

GLOBAL_MEDIA_MATERIAL_INSTANCE_PATH = "/Game/Arts/UI_2/Resource/Media/Painting/MI_PaintingMediaPlayer_Opaque_Ulit.MI_PaintingMediaPlayer_Opaque_Ulit"

ENEMY_LOCK_TARGET_INDICATOR_SPLINE_BP = "/Game/Blueprint/BattleSystem/Actor/BP_IndicatorSplineActor.BP_IndicatorSplineActor_C"
ENEMY_LOCK_TARGET_NIAGARA_EFFECT_PATH = "/Game/Arts/Effects/FX_Special/FX_HatredLine/NS_VFX_ZhishiLine_hong.NS_VFX_ZhishiLine_hong"
FRIEND_LOCK_TARGET_CLOSE_INDICATOR_SPLINE_BP = "/Game/Blueprint/BattleSystem/Actor/BP_IndicatorSplineActor.BP_IndicatorSplineActor_C"
FRIEND_LOCK_TARGET_CLOSE_NIAGARA_EFFECT_PATH = "/Game/Arts/Effects/FX_Special/FX_HatredLine/NS_VFX_ZhishiLine_Lv01.NS_VFX_ZhishiLine_Lv01"
FRIEND_LOCK_TARGET_FAR_INDICATOR_SPLINE_BP = "/Game/Blueprint/BattleSystem/Actor/BP_IndicatorSplineActor.BP_IndicatorSplineActor_C"
FRIEND_LOCK_TARGET_FAR_NIAGARA_EFFECT_PATH = "/Game/Arts/Effects/FX_Special/FX_HatredLine/NS_VFX_ZhishiLine_Lv02.NS_VFX_ZhishiLine_Lv02"

LOCK_INFO_DISTANCE_SCALE_CURVE_PATH = "/Game/Blueprint/LockInfo/C_LockinfoDistanceScaleCurve.C_LockinfoDistanceScaleCurve"
LOCK_TARGET_FX_PATH = "/Game/Arts/Effects/FX_Common/HUD/NS_HUD_01.NS_HUD_01"

FOREWARN_MAT_PATH = {
	Ring_Warn = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_SDF_Ring02.MI_Decal_Yujing_SDF_Ring02",
	Sector_Warn = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_RingNew_3_SDF.MI_Decal_RingNew_3_SDF",
	Rectangle_Warn = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Player_RectangleNew.MI_Decal_Player_RectangleNew",
	RectHead_Warn = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Player_RectangleTopNEw.MI_Decal_Player_RectangleTopNEw",
}

FOREWARN_TEX_PATH = {
	RecTextureBody1 = "/Game/Arts/Effects/FX_Common/Decal/T_Decal_P_Rec02_Down.T_Decal_P_Rec02_Down",
	RecTextureHead1 = "/Game/Arts/Effects/FX_Common/Decal/T_Decal_P_Rec02_Top.T_Decal_P_Rec02_Top",
	RecTextureBody2 = "/Game/Arts/Effects/FX_Common/Decal/T_Decal_P_Rec01_Down.T_Decal_P_Rec01_Down",
	RecTextureHead2 = "/Game/Arts/Effects/FX_Common/Decal/T_Decal_P_Rec01_Top.T_Decal_P_Rec01_Top",
}
FOREWARN_DECAL_PATH = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Ring1_2.MI_Decal_Ring1_2"

PRE_SELECT_FX_PATH = "/Game/Arts/Effects/FX_Common/PreSelectBeam/NS_PreSelectBeam.NS_PreSelectBeam"

DecalSightDisplayMIPath = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Ring.MI_Decal_Ring"
HATRED_LINE_FX = "/Game/Arts/Effects/FX_Special/FX_HatredLine/NS_VFX_HatredLine.NS_VFX_HatredLine"
HATRED_LINE_SPLINE = "/Game/Blueprint/BattleSystem/Actor/BP_HatredLineSplineActor.BP_HatredLineSplineActor_C"

WP_STREAMING_SOURCE_PROVIDER_BP = "/Game/Blueprint/SceneActor/SceneEnvironmentActor/BP_WorldPartitionStreamingSourceProvider.BP_WorldPartitionStreamingSourceProvider_C"
PLANE_REFRESH_EFFECT_PATH = "/Game/Arts/Effects/FX_Library/FX_Materials/M_PP_Chromatic_Plane_Inst.M_PP_Chromatic_Plane_Inst"
HDR_LIGHT_FIX_BP = "/Game/Arts/MaterialLibrary/Utility/HDRLightRig/BP_SceneHDRLightFix_Controller.BP_SceneHDRLightFix_Controller_C"
MPC_Scene = "/Game/Arts/MaterialLibrary/Utility/MPC_Scene.MPC_Scene"

WEAPON_DISSOLVE_IN_EDGE_CURVE_PATH = "/Game/Arts/Effects/FX_Character/WeaponShow_FX/Common/Jianru.Jianru"
WEAPON_DISSOLVE_OUT_EDGE_CURVE_PATH = "/Game/Arts/Effects/FX_Character/WeaponShow_FX/Common/Jianchu.Jianchu"
WEAPON_DISSOLVE_NOISE_TEXTURE_PATH = "/Game/Arts/Effects/FX_Library/FX_Textures/T_CloudNoise_001.T_CloudNoise_001"

EstatePortal = {
	NormalMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_Environment/MR_Portal/MR_Portal_SceneCapture.MR_Portal_SceneCapture",
	StencilMaterialPath = "/Game/Arts/MainMaterialLibrary/MR_Material/MR_Environment/MR_Portal/MR_Portal_SceneCapture_Stencil_Trans.MR_Portal_SceneCapture_Stencil_Trans"
}

VisibleControl = {
	SemiTransparentMaterialPath = "/Game/Arts/Effects/FX_Character/Visionary_FX/Skill07_New/MI_Visionary_Skill07.MI_Visionary_Skill07"
}

TURNTABLE_PUZZLE_TRIGGER = "/Game/Blueprint/SceneActor/Puzzle/BP_PuzzleTrigger.BP_PuzzleTrigger_C"