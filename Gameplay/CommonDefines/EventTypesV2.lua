local EventDefine = kg_require("Framework.EventSystem.V2.EventDefine")
---这里区分了EEventTypesV2与DefineEvents，是为了减少一层查询
---下面annotation一致，是为了能够直接使用点号提示
---@type DefineEvents
EEventTypesV2 = EEventTypesV2 or {}

---@class DefineEvents
DefineEvents = DefineEvents or EventDefine.Define("DefineEvents", EEventTypesV2)

---事件的定义都放在这下面

-- Level
DefineEvents.LEVEL_ON_LEVEL_LOAD_START = ""
DefineEvents.LEVEL_ON_LEVEL_LOADED = ""
DefineEvents.LEVEL_ON_ROLE_LOAD_COMPLETED = ""    -- AI依赖主角创建好，事件不保序，需要新增一个事件

-- 任务
DefineEvents.QUEST_ON_QUEST_FINISHED = ""
DefineEvents.HIDE_QUEST_DESC = ""
DefineEvents.TASK_ON_NEW_TASK = ""
DefineEvents.QUEST_ON_LIST_UPDATE = ""
DefineEvents.QUEST_ON_QUEST_FAILED = ""
DefineEvents.QUEST_ON_QUEST_ABANDONED = ""
DefineEvents.QUEST_ON_QUEST_ACCOMPLISHED = ""
DefineEvents.QUEST_ON_TARGET_COUNT_UPDATE = ""
DefineEvents.QUEST_ON_TARGET_FINISHED = ""
DefineEvents.QUEST_ON_PLANEVALID_CHANGED = ""
DefineEvents.QUEST_ON_PLANEPORTY_CHANGED = ""
DefineEvents.QUEST_ON_NOWPLANE_CHANGED = ""
DefineEvents.QUEST_ON_RECEIVE_NPCTALK_QUEST = ""
DefineEvents.QUEST_ON_HUD_TASK_CLICK = ""
DefineEvents.TARGETGUIDE_ON_FINISH = ""
DefineEvents.TARGETGUIDE_ON_UPDATE = ""

--装备
DefineEvents.ON_EQUIPMENT_REFORM = "" -- 装备词条重塑
DefineEvents.ON_EQUIPMENT_WORD_STORAGE = "" -- 装备词条存储


--npc
DefineEvents.NPC_TALK_CHANGED = ""
DefineEvents.NPC_HEAD_GM_CHANGE = ""
DefineEvents.NPC_HUDVISIBILITY_CHANGE = ""
DefineEvents.NPC_ENTER_WORLD_WITH_INSID = ""
DefineEvents.NPC_HEAD_TITLE_CHANGED = ""

--Plane位面行为
DefineEvents.PLANE_ON_CHANGE = ""

--- 组队 
DefineEvents.ON_SELF_BLOCK_VOICE_CHANGED = ""
DefineEvents.ON_TEAM_GROUP_BUFF_CHANGED = ""
DefineEvents.TEAM_GROUP_ROLE_ON_BORN = ""
DefineEvents.TEAM_GROUP_ROLE_ON_DESTROY = ""
DefineEvents.SERVER_MSG_TEAM_MEMBER_CHANGED = ""
DefineEvents.SERVER_ON_SINGLE_MATCH_STATE_CHANGED = ""
DefineEvents.SERVER_ON_TEAM_MATCH_STATE_CHANGED = ""
DefineEvents.TEAM_ON_TEAM_MEMBER_UPDATE = ""
DefineEvents.TEAM_ON_CAPTAIN_CHANGED = ""
DefineEvents.HUD_TARGET_SELECT = ""
DefineEvents.ON_SELF_TEAMINFOLIST_CHANGED = ""
DefineEvents.ON_SELF_INVITEDINFOLIST_CHANGED = ""
DefineEvents.ON_SELF_TEAMAPPLICATORLIST_CHANGED = ""
DefineEvents.ON_SELF_TEAMCOMBINELIST_CHANGED = ""
DefineEvents.ON_SELF_TEAMCOLLECTLIST_CHANGED = ""
DefineEvents.SERVER_RET_CREATE_TEAM = ""
DefineEvents.ON_CAN_RESCUE_STATE_CHANGED = ""
DefineEvents.ON_TEAM_AUTO_ACCEPT_JOIN_CHANGED = ""

-- 团队Group
DefineEvents.CLIENT_GROUP_CREATE = ""
DefineEvents.CLIENT_GROUP_DISBAND = ""
DefineEvents.GROUP_ADD_MEMBER = ""
DefineEvents.GROUP_DEL_MEMBER = ""
DefineEvents.GROUP_LEAGUE_ADD_GROUP = ""
DefineEvents.CLIENT_GROUP_MEMBER_VOICE_UPDATE = ""
DefineEvents.CLIENT_GROUP_DETAILS_CHANGED = ""
DefineEvents.CLIENT_GROUP_APPLY_UPDATE = ""
DefineEvents.GROUP_LEADER_CHANGED = ""

DefineEvents.CLIENT_GROUP_TEAM_EXCHANGE = ""
DefineEvents.SERVER_ALL_TEAMLIST_UPDATE = ""
DefineEvents.SERVER_BATCHINFO_UPDATE = ""
DefineEvents.CLIENT_GROUP_MEMBER_PROP_UPDATE = ""
DefineEvents.CLIENT_GROUP_TEAMLEADER_UPDATE = ""

DefineEvents.CLIENT_GROUP_INVITE_CHANGED = ""
DefineEvents.CLIENT_GROUP_CONFIRM_UPDATE = ""
DefineEvents.CLIENT_GROUP_CONFIRM_END = ""
DefineEvents.SERVER_RET_CONVENE = ""
DefineEvents.CLIENT_GROUP_MEMBER_EXCHANGE = ""
DefineEvents.GROUP_LEAGUE_DEL_GROUP = ""
DefineEvents.ON_GROUP_END_ONCE_EXCHANGE = ""

DefineEvents.ON_GROUP_LEADER_UID_CHANGED = ""
DefineEvents.ON_JOIN_GROUP = ""
DefineEvents.ON_QUIT_GROUP = ""
DefineEvents.CLIENT_GROUP_CAN_RESCUE_CHANGED = ""
--Team Prop

-- 组队
DefineEvents.ON_TEAMID_CHANGED = ""
DefineEvents.ON_SELF_TEAM_TARGETID_CHANGED = ""
DefineEvents.ON_MAINPLAYER_GROUP_ID_CHANGED = ""
DefineEvents.ON_TEAMMATCH_CHANGED = ""
DefineEvents.ON_FOLLOWSTATE_CHANGED = ""
DefineEvents.ON_SELF_CAPTAIN_CHANGED = ""
DefineEvents.ON_SELF_LEVEL_CHANGED = ""
DefineEvents.ON_SELF_SPIRIT_CHANGED = ""
DefineEvents.ON_SELF_BTARGETBYBOSS_CHANGED = ""
DefineEvents.ON_SELF_MATCHLIST_CHANGED = ""
DefineEvents.CLIENT_SCENE_MARK_CAHNGED = ""
DefineEvents.CLIENT_GROUP_MEMBER_MARK_CAHNGED = ""
DefineEvents.ON_TEAM_MARK_VISIBLE_CHANGED = ""

DefineEvents.ON_TEAMMEMBER_MEMBERFLAG_CHANGED = ""


-- ActorBase 
DefineEvents.ENTITY_COUNT_CHANGED = "" -- Entity数量变化事件,Brief系统用
DefineEvents.NET_ON_ENTITY_CREATE = "" 
DefineEvents.ROLE_ON_BORN = ""
DefineEvents.ROLE_ON_DEAD = ""
DefineEvents.ROLE_ON_DESTROY = ""
DefineEvents.ROLE_ON_VISIBLE_CHANGED = ""
DefineEvents.ROLE_AFTER_EXIT_WORLD = ""
DefineEvents.LOCK_LIMIT_CHANGE = ""
DefineEvents.ON_CAMP_CHANGED = ""
DefineEvents.ON_BOSS_TYPE_CHANGED = ""
DefineEvents.ON_LOCK_GROUP_START = ""
DefineEvents.ON_LOCK_GROUP_END = ""
DefineEvents.ROLE_MOUSE_MOVE_AXIS_CHANGED  = ""
DefineEvents.ROLE_ON_TELEPORT_FAR = ""
DefineEvents.ROLE_EXP_CHANGED = ""

DefineEvents.ON_FIGHT_RELATIONSHIP_CHANGE = ""

-- 职业属性
DefineEvents.ON_PROFESSION_PROP_1_CHANGED = ""
DefineEvents.ON_PROFESSION_PROP_2_CHANGED = ""
DefineEvents.ON_PROFESSION_PROP_3_CHANGED = ""
DefineEvents.ON_PROFESSION_SUNPROP_1_CHANGED = ""
DefineEvents.ON_PROFESSION_UTOPROP_1_CHANGED = ""
DefineEvents.ON_PROFESSION_ARBPROP_1_CHANGED = ""
DefineEvents.ON_PROFESSION_APPRPROP_3_CHANGED = ""
DefineEvents.ON_PROFESSION_WARPROP_1_CHANGED = ""
DefineEvents.ON_PROFESSION_FOOLPROP_1_CHANGED = ""
DefineEvents.ON_PROFESSION_SEERPROP_1_CHANGED = ""
-- 职业Q技能解锁状态变化
DefineEvents.SKILLHUD_PROFESSION_Q_LOCK_CHANGE= ""

--角色观战
DefineEvents.SERVER_ROLE_OBSERVER_CHANGED = ""

-- INPUT
DefineEvents.ROLE_MOVE_INPUT = ""
DefineEvents.ROLE_ACTION_INPUT_EVENT = ""
DefineEvents.CAMERA_ROTATE_INPUT = "" 	-- 镜头旋转

--天赋系统
DefineEvents.ON_TALENT_TREE_CHANGED = ""    		  --天赋树切换
DefineEvents.ON_TALENT_TREE_NODE_LEVEL_CHANGE = ""    --天赋普通节点等级发生变更(升级或重置)
DefineEvents.ON_TALENT_TREE_NODE_ACTIVE = ""      	  --天赋特殊节点激活

-- Mount
DefineEvents.MOUNT_RIDER_STATUS_CHANGED = "" -- 乘坐坐骑状态改变
DefineEvents.MOUNT_STATUS_CHANGED = "" -- 玩家坐骑状态改变
DefineEvents.MOUNT_CUR_MOUNT_CHANGED = "" -- 当前坐骑改变
DefineEvents.MOUNT_WARDROBE_CHANGED = "" -- 坐骑衣柜改变

-- Guild 俱乐部
DefineEvents.ON_UPDATE_GUILD_PICTURE_SUCCESS = "" -- 俱乐部宣传图上传成功

-- 情境系统
DefineEvents.ON_SCENE_CUSTOM_LIST_UPDATE = ""          --情境列表刷新
DefineEvents.ON_SCENE_CUSTOM_ITEM_LIST_UPDATE = ""     --情境编辑Item列表刷新
DefineEvents.ON_SCENE_CUSTOM_PANEL_DISABLE = ""        --情境界面禁用
DefineEvents.ON_SCENE_CUSTOM_CLICK_ENTITY_CHANGE = ""  --选中entity改变
DefineEvents.ON_SCENE_CUSTOM_ADD_ENTITY = ""           --添加entity
DefineEvents.ON_SCENE_CUSTOM_DELETE_ENTITY = ""        --删除entity
DefineEvents.ON_SCENE_CUSTOM_REFRESH_OPERATION = ""    --刷新操作

-- 外观
DefineEvents.ON_PLAYER_APPEARANCE_GET = ""             --他人外观数据获取

-- 时装系统
DefineEvents.ON_FASHION_WARDROBE_UPDATE = ""           --衣柜全部数据刷新
DefineEvents.ON_FASHION_UI_SINGLE_UPDATE = ""          --衣柜单个服装数据刷新
DefineEvents.ON_FASHION_UI_WEAPON_UPDATE = ""          --衣柜武器显隐
DefineEvents.ON_FASHION_WEAR_UPDATE = ""               --穿脱时装更新
DefineEvents.ON_FASHION_GET_REWARD = ""                --领奖
DefineEvents.ON_FASHION_PRESETS_ALL_UPDATE = ""        --预设获取返回
DefineEvents.ON_FASHION_PRESETS_ONE_UPDATE = ""        --预设设置成功
DefineEvents.ON_FASHION_WARDROBE_SELECT_SUBTYPE = ""   --衣柜选中subtype

---捏脸交互
DefineEvents.ON_CUSTOM_ROLE_CHANGE_BUTTON_STAGE = ""
DefineEvents.ON_CUSTOM_ROLE_TEST_TEXT_CHANGE = ""
DefineEvents.ON_CUSTOM_ROLE_TEXTURE_EXTRA_CHANGE = ""
DefineEvents.ON_CUSTOM_ROLE_CLOSE_PLAIN = ""
DefineEvents.ON_CUSTOM_ROLE_REFRESH_WIDGET_UI = ""
DefineEvents.ON_CUSTOM_IRIS_STATE_UPDATE = ""

-- 收藏品
DefineEvents.COLLECTIBLES_LIST_FILTER_UPDATE = ""      --过滤修改
DefineEvents.COLLECTIBLES_LIST_PHOTO_DESC_UPDATE = ""  --图片描述成功修改
DefineEvents.COLLECTIBLES_REWARD_GET_UPDATE = ""       --领取收藏品奖励
DefineEvents.COLLECTIBLES_REWARDLEVEL_GET_UPDATE = ""  --领取收藏品等级奖励
DefineEvents.COLLECTIBLES_GET = ""                     --获取收藏品
DefineEvents.COLLECTIBLES_CLOSE = ""                   --收藏品信息界面关闭

--迷雾系统
DefineEvents.PLOTRECAP_ALL_GET = ""                  	--获取全部剧情数据
DefineEvents.PLOTRECAP_ONE_INFO_GET = ""	            --获取单个剧情回顾数据
DefineEvents.PLOTRECAP_ONE_TYPE_GET = ""	            --获取某个大类的信息
DefineEvents.PLOTRECAP_ONE_INFO_NOTIFY_COMPLETE = ""	--通知某个迷雾剧情完成
DefineEvents.PLOTRECAP_ONE_NOTIFY_CHANGE = ""	        --通知某个迷雾剧情进度变化
DefineEvents.PLOTRECAP_ONE_TYPE_UPDATE = ""	            --领取一个奖励的结果
DefineEvents.PLOTRECAP_ONE_INFO_CLEAR = ""	            --清空某个的剧情回顾的信息结果
DefineEvents.PLOTRECAP_ONE_REWARD_GET = ""             	--领取一个奖励的结果
DefineEvents.PLOTRECAP_ALL_REWARD_GET = ""	            --领取某个子类型里已经解锁的剧情回顾物品的结果
DefineEvents.PLOTRECAP_ONE_LEVEL_REWARD_GET = ""       	--获取大类单个等级奖励的结果
DefineEvents.PLOTRECAP_ALL_LEVEL_REWARD_GET = ""        --获取大类所有等级奖励的结果
DefineEvents.PLOTRECAP_ONE_TYPE_CHANGE = ""	            --某个大类的等级变化
DefineEvents.PlOTRECAP_BIGNODE_CLICK = ""	            --迷雾主线界面点击了某个大节点
DefineEvents.PlOTRECAP_BIGNODE_UNLOCK = ""	            --迷雾主线大节点解锁
DefineEvents.PlOTRECAP_MIST_DROPDOWN_FRESH = ""	        --迷雾主线下拉菜单刷新
DefineEvents.PlOTRECAP_FACT_DROPDOWN_FRESH = ""	        --现实主线下拉菜单刷新
DefineEvents.PlOTRECAP_QUEST_INFO_OPEN = ""             --支线详情页面打开
DefineEvents.PlOTRECAP_QUEST_INFO_CLOSE = ""            --支线详情页面关闭

-- 家园建造
DefineEvents.ON_BUILD_PLACE_NODE_CHANGE = ""           --开始\结束摆放物体
DefineEvents.ON_BUILD_SELECT_NODE_CHANGE = ""          --选中的物体改变
DefineEvents.ON_BUILD_CANT_PLACE_UPDATE = ""           --不可摆放原因改变

-- 占卜家卡牌
DefineEvents.ON_FOOL_CARD_ADD = ""
DefineEvents.ON_FOOL_CARD_REMOVE = ""
DefineEvents.ON_FOOL_CARD_COMBO = ""
DefineEvents.ON_FOOL_CARD_REFRESH = ""

-- Settings
DefineEvents.SETTING_ONSET_HEADER_VISIBILITY = ""
DefineEvents.SETTING_ON_SET_SHOW_SKILL_TAG = ""
DefineEvents.SETTING_ON_AVATARACTOR_UNSTUCK = ""
DefineEvents.SETTING_ON_FIX_ROCKER_CHANGED = ""
DefineEvents.SETTING_ON_UI_LOCK_STATE_CHANGE = ""
DefineEvents.SETTING_ON_HP_SHOW = ""
DefineEvents.SETTING_ON_RESET = ""
DefineEvents.SETTING_ON_SUBTYPE_SYNC = ""
DefineEvents.SETTING_ON_TOGGLE = ""
DefineEvents.SETTING_ON_VOL_CHANGE = ""
DefineEvents.SETTING_ON_POST_PROCESS_QUALITY_CHANGE = ""

--角色展示
DefineEvents.ON_CANCEL_SELECTION = ""          	--取消选中
DefineEvents.ON_BACKPACK_SELECTION_CHANGE = "" 	--改变浮层装备选中状态
DefineEvents.ON_TITLE_PANEL_CLOSE = ""			-- 称号面板关闭
DefineEvents.ON_PLAY_EXCHANGE_ANIM = ""
DefineEvents.SERVER_LAUNCH_INFO = ""
DefineEvents.PROP_ITEM_SHOW_TIPS = ""
DefineEvents.ON_ROLEDISPLAY_GET_OTHERPLAYER_DETAIL = ""

DefineEvents.MODULE_LOCK_CHANGE = ""

--Climate / Clock
DefineEvents.ON_CLIMATE_CHANGE = ""
DefineEvents.ON_CLIMATE_CHANGE_UI = ""
DefineEvents.ON_GAME_TIME_CLOCK_CHANGE = ""
DefineEvents.ON_RET_ALL_CLIMATE_INFO = ""
DefineEvents.ON_SHOW_CLIMATE_BTN = ""
DefineEvents.ON_TOGGLE_HUD_CLIMATE_ALL = ""

---动作系统SocialAction
DefineEvents.ON_SOCIAL_ACTION_LOCK_CHANGED = ""
DefineEvents.ON_CANCEL_CAST_SOCIAL_ACTION = ""
DefineEvents.ON_DUAL_ACTION_SELECT_PLAYER = ""
DefineEvents.ON_REFRESH_SOCIAL_ACTION_PANEL = ""

---AutoSkill一键连招
DefineEvents.AUTOSKILL_START = ""
DefineEvents.AUTOSKILL_END = ""

-- 自动战斗
DefineEvents.ON_AUTO_BATTLE_STATE_START = ""
DefineEvents.ON_AUTO_BATTLE_STATE_STOP = ""

-- HUD
DefineEvents.HUD_ON_TOUCH_BACKGROUND = ""
DefineEvents.HUD_POST_QTE_STATE_CHANGED = ""

DefineEvents.ROLE_ON_SKILL_READY = "" --角色技能装配完

-- 侧边栏切换
DefineEvents.HUD_SET_SIDE_BAR_TAB = ""
DefineEvents.ON_HUD_SETTING_CHANGED = ""
DefineEvents.ON_HUD_IMMERSE = ""

---掉落Drop系统
DefineEvents.SHOW_DROP_HUD = ""  -- 显示掉落HUD

--- 好友系统
DefineEvents.PLAYERCARD_UPDATE = ""		-- 玩家卡片信息更新
DefineEvents.FRIEND_RECOMMEND_LIST_CLIENT_GET = ""  -- 好友推荐列表获取
DefineEvents.ON_FRIEND_SHOW_STATUS_CHANGE = ""	-- 好友显示状态变化
DefineEvents.ON_FRIEND_SHOW_STATUS_RANGE_CHANGE = "" -- 好友显示状态范围变化
DefineEvents.ON_CLIENT_SET_SHOW_STATUS = ""	-- 客户端设置好友显示状态
DefineEvents.ON_FRIEND_IS_NODISTURB_CHANGE = ""	-- 好友免打扰状态变化
DefineEvents.RECEIVE_RECOMEND_FRIEND_LIST = ""       --系统推荐好友
DefineEvents.RECEIVE_SEARCH_FRIEND_LIST = ""         --查找好友
DefineEvents.ON_ADD_FRIEND = ""                      --添加好友
DefineEvents.ON_DELETE_FRIEND = ""                   --删除好友
DefineEvents.RECEIVE_FRIEND_APPLICATION = ""         --收到好友申请
DefineEvents.SHOW_FRIEND_APPLICATION = ""            --显示好友申请
DefineEvents.ON_SET_FRIEND_REMARK = ""               --修改好友备注
DefineEvents.RECEIVE_FRIEND_GROUP_LIST = ""          --好友分组更新
DefineEvents.ON_ADD_BLACK_FRIEND = ""               --添加黑名单
DefineEvents.ON_DELETE_BLACK_FRIEND = ""             --移出黑名单
DefineEvents.FRIEND_ATTRACTION_CHANGE = ""           --好感度改变
DefineEvents.FRIEND_ONLINE_STATES_CHANGE = ""        --在线状态改变
DefineEvents.FRIEND_INFO_CHANGE = ""                 --好友信息改变
DefineEvents.RECEIVE_RECENTLY_MEET = ""              --最近结识（最近组队）
DefineEvents.RECEIVE_RECENT_WHISPER_INFO_CHANGE = "" --私聊对象信息改变
DefineEvents.ON_DELETE_RECENT = "" --删除最近聊天列表的联系人
DefineEvents.CHANGE_TOP_WHISPER = "" --修改置顶联系人
DefineEvents.ON_RECENTLY_WHISPER_LIST_CHANGE = "" --最近聊天列表改变
DefineEvents.ON_GET_SOCIAL_CLUB_INFO_LIST = "" --收到服务器群聊列表回调
DefineEvents.ON_UPDATE_FRIEND_CLUB_INFO = "" --收到某个群聊的更新
DefineEvents.ON_LEAVE_FRIEND_CLUB = "" --离开群组
DefineEvents.ON_CLUB_DISTURB_CHANGE = "" --群聊消息免打扰
DefineEvents.ON_GET_RELATION_BOTH_WAY_INFOS = "" --收到双向好友信息
DefineEvents.ON_GET_SOCIAL_CLUB_MEMBER_INFOS = "" --批量获取群聊玩家信息
DefineEvents.ON_FRIEND_CLUB_NEW_MESSAGE_CHANGE = "" --群聊新消息改变，如正在A群聊天，此时B群来了消息
DefineEvents.RECEIVE_PLAYER_RELATION_INFO = "" --请求关系返回
DefineEvents.CHECK_BOTH_WAY_FRIENDSHIP = "" --请求是否双向好友消息返回
DefineEvents.HIDE_PARTNER_EDIT = "" --隐藏伙伴编辑
DefineEvents.ON_SET_TIPS_CHANGE_GROUP = "" --设置提示改变好友分组

--红包系统
DefineEvents.REDPACKET_HISTORY_CHANGE = "" -- 红包历史记录改变
DefineEvents.REDPACKET_CHANNEL_LIST_CHANGE = "" -- 红包频道列表改变
DefineEvents.REDPACKET_INFO_UPDATE = "" -- 红包信息更新
DefineEvents.REDPACKET_RECEIVE_SUCCESS = "" -- 红包领取成功
DefineEvents.REDPACKET_VOICE_BTN_RELEASE = "" -- 红包语音按钮释放
DefineEvents.REDPACKET_VOICE_BTN_HOVERED = "" -- 红包语音按钮悬停
DefineEvents.REDPACKET_VOICE_BTN_UNHOVERED = "" -- 红包语音按钮取消悬停

--家园
DefineEvents.ON_MANOR_STORE_ITEM_UPDATE = ""   	 	-- 更新仓库数量
DefineEvents.ON_MANOR_LEVEL_UPGRADE = ""   			-- 家园升级成功
DefineEvents.ON_WORKSHOP_LIST_UPDATE = ""   		-- 工坊信息更新
DefineEvents.ON_WORKSHOP_EMPLOYEE_UPDATE = ""   	-- 工坊雇员更新
DefineEvents.ON_WORKSHOP_PRODUCTION_UPDATE = ""   	-- 工坊生产信息更新

--玩家属性
DefineEvents.REMINDER_ON_LEVEL_UP = ""

--Reminder
DefineEvents.OPEN_REMINDER = ""             -- 播放Reminder
DefineEvents.CLOSE_REMINDER = ""            -- 停止Reminder，并播放下一个

--扮演系统
DefineEvents.ON_ROLE_PLAY_SKILLList_CHANGE = ""
DefineEvents.ROLEPLAY_TALENT_NODES_UPDATE = ""      -- 天赋树状态刷新
DefineEvents.ROLEPLAY_IDENTITY_LEVEL_UPDATE = ""  	-- 身份等级刷新
DefineEvents.ROLEPLAY_IDENTITY_LEVEL_REWARDED = ""  -- 身份等级奖励领取成功

--非凡扮演
DefineEvents.ROLEPLAY_GAME_OPEN_DIALOGUE = ""		-- 非凡扮演对话

--大世界探索收集
DefineEvents.EXPLORE_SOUL_SUBMIT_RESULT = ""
DefineEvents.ON_SELF_EXPLORE_SOUL_COUNT_CHANGED = ""
DefineEvents.RET_EXPLORE_DEGREE_UPDATE = ""
DefineEvents.CUSTOM_TRACING_TAG_ADD_SUCCESS = ""
DefineEvents.CUSTOM_TRACING_PROPERTY_CHANGED = ""
DefineEvents.CUSTOM_TRACING_ITEM_PROPERTY_CHANGED = ""
DefineEvents.TRACING_MAP_TAG_CLASS_HIDE_INFO_CHANGED = ""
DefineEvents.ON_TRACING_MAP_TAG_HIDE_INFO_PROPERTY_CHANGED = ""
DefineEvents.RET_EXPLORE_REWARD_GET = ""
DefineEvents.ON_EXPLORE_NEXTSOULINSTANCEID_CHANGED = ""
DefineEvents.BIG_WORLD_MAP_SHOW_ALL = ""    --地图探索相关的图标在大地图的显隐

---尾随玩法
DefineEvents.ON_SNEAK_NPC_WARNING_UPDATE = ""
DefineEvents.ON_SNEAK_NPC_WARNING_HIDE = ""

-- 循声玩法
DefineEvents.SOUND_TRACE_ENABLE_HEAD_INFO = ""  --循声玩法头顶UI显示等级,0为关闭,递增
DefineEvents.SOUND_TRACE_ENABLE_SHOW_MINIMAP = ""  --循声玩法开启/关闭显示小地图范围
DefineEvents.SOUND_TRACE_ENABLE_SHOW_MINIMAP_MAPSYSTEM = ""  --转发逻辑

--- NTP
DefineEvents.ON_RECEIVE_NTP_DATA = ""

---传送完成
DefineEvents.ON_TELEPORT_ARRIVED = ""
---请求传送
DefineEvents.ON_REQUEST_TELEPORT = ""

DefineEvents.ON_MAIN_CHAR_TRACK_FLOOR_ENABLED = ""

---呓语之花
DefineEvents.WONDER_FLOWER_SUB_STATE_UPDATE = ""  -- 呓语之花状态刷新

---采集物
DefineEvents.PICK_OBJECT_COLLETABLE_CHANGED = ""  -- 采集物可采集状态刷新

--region 社交系统
---聊天系统
DefineEvents.CHAT_CHANNEL_SETTING_UPDATE = ""        	-- 聊天频道设置更新
DefineEvents.CHAT_ON_CHANNEL_UPDATE = ""              	-- 聊天频道更新
DefineEvents.CHAT_ANONYMOUS_DISCLOSE = ""            	-- 匿名状态披露
DefineEvents.CHAT_EMO_INPUT = ""                    	-- 表情等外部文字信息输入
DefineEvents.CHAT_SHOUT_INPUT = ""                    	-- 喊话输入
DefineEvents.CHAT_GIF_INPUT = ""                     	-- GIF输入
DefineEvents.CHAT_ITEM_INPUT = ""                     	-- 物品输入
DefineEvents.CHAT_TASK_INPUT = ""                     	-- 任务输入
DefineEvents.CHAT_INPUT_COPY = ""                     	-- 复制输入
DefineEvents.CHAT_INPUT_HISTORY = ""                  	-- 历史信息输入
DefineEvents.CHAT_SHOUT = ""                         	-- 喊话跑马灯
DefineEvents.CHAT_AT = ""                            	-- @某人
DefineEvents.CHAT_JUMP = ""                         	-- 消息跳转
DefineEvents.CHAT_AT_REMOVE = ""                     	-- 移除@
DefineEvents.CHAT_VOICE_UPLOAD_COMPLETE = ""           	-- 语音上传完成
DefineEvents.CHAT_VOICE_RECORD_FAIL = ""               	-- 语音录制失败
DefineEvents.CHAT_VOICE_RECORD_FAIL_TOOSHORT = ""      	-- 语音录制失败（时间太短）
DefineEvents.CHAT_VOICE_PLAY = ""                      	-- 播放语音
DefineEvents.CHAT_VOICE_PLAY_COMPLETE = ""             	-- 语音播放完成
DefineEvents.CHAT_VOICE_TOTEXT = ""                    	-- 语音转文字
DefineEvents.CHAT_VOICE_RECORD_SUCCESS = ""            	-- 语音录制成功
DefineEvents.CHAT_VOICE_TOTEXT_HOVERED = ""           	-- 语音转文字悬浮
DefineEvents.CHAT_VOICE_TOTEXT_UNHOVERED = ""          	-- 语音转文字取消悬浮
DefineEvents.GME_REALTIME_AUDIO_CHANGE = ""            	-- 实时语音状态变化
DefineEvents.CHAT_SYN_INPUT = ""                     	-- 同步输入
DefineEvents.CHAT_CHANNEL_CD = ""                      	-- 频道冷却时间
DefineEvents.HUD_CHAT_URL_ACTIVE = ""                  	-- 聊天URL激活
DefineEvents.CHAT_MSGREF = ""                        	-- 消息引用
DefineEvents.CHAT_COMMON_CHANNEL_UPDATE = ""           	-- 普通频道更新
DefineEvents.CHAT_CUSTOM_EMO_UPDATE = ""              	-- 自定义表情更新
DefineEvents.CHAT_CHANGE_TEA_ROOM_MODULE_STATE = ""     -- 更改茶室模块状态
DefineEvents.CHAT_OPEN_SOCIAL_PAGE = ""                	-- 打开社交页面
DefineEvents.CHAT_REMOVE_BARRAGE = ""                 	-- 移除弹幕

---朋友圈系统
DefineEvents.MOMENTS_ON_MOMENTS_LIST_UPDATE = ""        -- 动态列表更新
DefineEvents.MOMENTS_ON_GET_ONE_ALL_MOMENTS = ""        -- 获取单个用户的全部动态
DefineEvents.MOMENTS_ON_ADD_TOPIC = ""                  -- 成功添加话题
DefineEvents.MOMENTS_ON_MSG_CENTER_LIST_UPDATE = ""     -- 消息中心列表更新
DefineEvents.MOMENTS_ON_HOT_TOPIC_LIST_UPDATE = ""      -- 热门话题列表更新
DefineEvents.MOMENTS_ON_COMMENT_LIST_UPDATE = ""        -- 评论列表更新
DefineEvents.MOMENTS_ON_COMMENT_SUCCESS = ""            -- 发表评论成功
DefineEvents.MOMENTS_ON_UPDATE_TOPIC_MOMENTS = ""       -- 更新话题下的动态
DefineEvents.MOMENTS_ON_CHANGE_LIKE_STATE = ""          -- 改变点赞状态
DefineEvents.MOMENTS_ON_DELETE_MOMENT = ""            	-- 删除动态成功
DefineEvents.MOMENTS_ON_DELETE_COMMENT = ""            	-- 删除评论成功
DefineEvents.MOMENTS_ON_GET_SEARCH_TOPICS = ""         	-- 获取搜索的话题列表

---派对聊天室系统
DefineEvents.ON_CHATROOM_SELF_VOICESTATE_CHANGED = ""   -- 自己的语音状态改变
DefineEvents.ON_CHATROOM_VOICESTATE_CHANGED = ""        -- 其他人的语音状态改变
DefineEvents.ON_CHATROOM_BLACK_LIST_CHANGE = ""         -- 黑名单列表改变
DefineEvents.ON_CHATROOM_GAG_LIST_CHANGE = ""           -- 禁言列表改变
DefineEvents.ON_CHATROOM_ORDER_LIST_CHANGE = ""         -- 发言顺序改变
DefineEvents.ON_CHATROOM_APPLY_LIST_CHANGE = ""         -- 申请列表改变
DefineEvents.ON_CHATROOM_REFRESH_ROOM_LIST = ""         -- 请求刷新房间列表
DefineEvents.ON_CHATROOM_UPDATE_ROOM_LIST = ""          -- 房间列表更新
DefineEvents.ON_CHATROOM_TOP_INFO_UPDATE = ""           -- 房间顶部信息更新
DefineEvents.ON_CHATROOM_MEMBER_LIST_UPDATE = ""        -- 成员列表更新
DefineEvents.ON_CHATROOM_TOP_MSG_UPDATE = ""            -- 房间置顶消息更新
DefineEvents.ON_CHATROOM_START_AUTODESTROY = ""         -- 房间自动销毁倒计时开始
DefineEvents.ON_CHATROOM_NOTICE_UPDATE = ""             -- 房间公告更新
DefineEvents.ON_CHATROOM_MIC_PERMISSION_UPDATE = ""     -- 麦克风权限发生变化
DefineEvents.ON_CHATROOM_FAVORITE_SUCCESS = ""          -- 收藏成功
DefineEvents.ON_CHATROOM_GET_FAVORITES = ""             -- 获取收藏列表
DefineEvents.ON_CHATROOM_CANCEL_FAVORITE_SUCCESS = ""   -- 取消收藏成功
DefineEvents.ON_CHATROOM_GET_PARTY_DIE_TIME = ""        -- 获取常驻派对销毁时间
DefineEvents.ON_CHATROOM_GET_PERSISTENT_PARTY = ""      -- 获取我的常驻派对信息
DefineEvents.ON_CHATROOM_UPDATE_MASTER_LOGIN_TYPE = ""  -- 常驻房间房主登陆状态改变
DefineEvents.ON_CHATROOM_ASSIGN_IDENTITY_TO_AVATAR = "" -- 指派身份给玩家
DefineEvents.ON_CHATROOM_MODIFY_IDENTITY_NAME = ""      -- 修改身份名称
DefineEvents.ON_CHATROOM_REMOVE_AVATAR_IDENTITY = ""    -- 移除玩家身份
DefineEvents.ON_CHATROOM_CHANGE_IDENTITY_STATUS = ""    -- 玩家身份状态改变
DefineEvents.ON_CHATROOM_CHANGE_ROOM_MASTER = ""        -- 房主改变
DefineEvents.ON_CLIENT_MODULE_SWITCH_CHANGED = "" 		-- 客户端开关发生调整

---塔罗小队系统
DefineEvents.ON_MSG_TAROT_TEAM_APPLY_SUCC = ""        	-- 成功申请加入小队
DefineEvents.ON_JOIN_TAROT_TEAM_SUCC = ""				-- 加入小队成功
DefineEvents.ON_QUIT_TAROT_TEAM_SUCC = ""            	-- 退队成功
DefineEvents.ON_SEARCH_TEAM_SUCC = ""                	-- 搜索队伍成功
DefineEvents.ON_TAROT_TEAM_DISBAND = ""              	-- 队长解散小队返回
DefineEvents.ON_SYNC_TAROT_TEAM_ORDER = ""           	-- 队长更新顺序
DefineEvents.ON_CONFIRM_TAROT_TEAM_MSG = ""           	-- 收到合同签订的消息
DefineEvents.ON_TAROT_TEAM_BUILD_SUCC = ""           	-- 小队建队流程完毕
DefineEvents.ON_TAROT_TEAM_DETAIL_INFO_SUCC = ""     	-- 当前查看的小队详细信息改变
DefineEvents.ON_TAROT_TEAM_DISHBAND = ""				-- 小队解散
DefineEvents.ON_GET_APPLY_LIST_SUCC = ""             	-- 获取申请列表回调
DefineEvents.ON_ACCEPT_APPLY_RET = ""                	-- 队长处理申请回调
DefineEvents.ON_TAROT_TEAM_GET_WAGE = ""              	-- 领取工资回调
DefineEvents.ON_TAROT_TEAM_LIST_TOP_CHANGE = ""         -- 自己的申请数据变化
DefineEvents.ON_TAROT_TEAM_MSG_MODIFY_SUCC = ""      	-- 修改小队的基础信息返回
DefineEvents.ON_TAROT_TEAM_LEVEL_REWARD_UPDATE = ""    	-- 领取等级奖励
DefineEvents.ON_TAROT_TEAM_MEMBER_CHANGE = ""          	-- 已成立小队人员发生变动

---俱乐部（公会）系统
DefineEvents.GUILD_ON_GUILD_LIST_UPDATE = ""            -- 公会列表更新
DefineEvents.GUILD_CHANGE_NAME_SUCCESS = ""             -- 公会改名成功
DefineEvents.DELETE_APPLY_LIST_SUCCESS = ""             -- 删除申请列表成功
DefineEvents.REFRESH_GUILD_SIGN_IN = ""                 -- 刷新公会签到
DefineEvents.APPLY_GUILD_SUCCESS = ""                   -- 申请加入公会成功
DefineEvents.RESPONSE_GUILD_SUCCESS = ""                -- 响应公会申请成功
DefineEvents.CANCEL_RESPONSE_GUILD_SUCCESS = ""         -- 取消响应公会申请成功
DefineEvents.RECEIVE_PRE_GUILD_LIST = ""                -- 接收响应公会列表
DefineEvents.RECEIVE_GUILD_DETAIL = ""                  -- 接收公会详情
DefineEvents.CREATE_GUILD_SUCCESS = ""                  -- 创建公会成功
DefineEvents.RECEIVE_GUILD_MEMBERS = ""                 -- 接收公会成员列表
DefineEvents.RECEIVE_PRE_GUILD_INFO = ""                -- 接收响应公会信息
DefineEvents.ENTER_GUILD = ""                           -- 加入公会
DefineEvents.LEAVE_GUILD = ""                           -- 离开公会
DefineEvents.RECEIVE_GUILD_INFO = ""                    -- 接收公会信息
DefineEvents.CHANGE_GUILD_ANNOUNCE_SUCCESS = ""         -- 更改公会公告成功
DefineEvents.ON_GUILD_BADGE_CHANGE = ""                 -- 公会徽章变更
DefineEvents.RECEIVE_GUILD_APPLIES = ""                 -- 接收公会申请列表
DefineEvents.ACCEPT_GUILD_APPLY_SUCCESS = ""            -- 接受公会申请成功
DefineEvents.REFUSE_GUILD_APPLY_SUCCESS = ""            -- 拒绝公会申请成功
DefineEvents.RECEIVE_GUILD_RIGHTS = ""                  -- 接收公会权限信息
DefineEvents.RECEIVE_GUILD_EVENT = ""                   -- 接收公会事件
DefineEvents.RECEIVE_GUILD_BUILDINGS = ""               -- 接收公会建筑信息
DefineEvents.UPGRADE_GUILD_BUILDING_SUCCESS = ""        -- 升级公会建筑成功
DefineEvents.RECEIVE_GUILD_WAGE = ""                    -- 接收公会工资信息
DefineEvents.RECEIVE_EXERCISE_BUILDING_LV = ""          -- 接收修炼建筑等级信息
DefineEvents.RECEIVE_EXERCISE_INFO = ""                 -- 接收修炼信息
DefineEvents.GUILD_EXERCISE_SUCCESS = ""                -- 公会技能修炼成功
DefineEvents.GUILD_MEMBER_KICKED = ""                   -- 公会成员被踢出
DefineEvents.GUILD_SIGNATURE_CHANGE = ""                -- 公会签到信息变更
DefineEvents.GUILD_ON_SYNC_GUILD_ANSWER_ACTIVITY_INFO = "" 			-- 同步公会答题活动信息
DefineEvents.GUILD_ON_UPDATE_GUILD_ANSWER_ACTIVITY_INFO = "" 		-- 更新公会答题活动信息
DefineEvents.ON_CLOSE_GUILD_ACTIVITY = ""                 			-- 关闭公会活动
DefineEvents.ON_GUILD_NIGHT_ACTIVITY_MEMBERS_CHANGED = ""   		-- 公会夜活动场景内人数变换
DefineEvents.ON_LEAVE_GUILD_SUCCESS = ""                -- 离开公会成功
DefineEvents.ON_SET_GUILD_BADGE_INDEX_SUCCESS = ""      -- 设置公会徽章索引成功
DefineEvents.GUILD_ON_GET_SEARCH_GUILDS = ""            -- 获取搜索公会列表
DefineEvents.GUILD_ON_CONFIRM_SELECT_BADGE = ""         -- 确认选择公会徽章
DefineEvents.ON_GET_GUILD_RECOMMEND_INFOS = ""          -- 获取公会推荐信息
DefineEvents.ON_SET_GUILD_GROUP_NAME_SUCCESS = ""       -- 设置公会分组名称成功
DefineEvents.ON_SEL_GUILD_OUT_PAGE = ""                 -- 公会选择离开页面
DefineEvents.ON_SET_GUILD_TYPE_SUCCESS = ""             -- 设置公会类型成功
DefineEvents.ON_TRY_ADD_MEMBER_ROLE = ""                -- 尝试添加成员职位
DefineEvents.ON_TRY_ADD_MEMBER_GROUP = ""               -- 尝试添加成员分组
DefineEvents.ON_TRY_REMOVE_MEMBER_GROUP = ""            -- 尝试移除成员分组
DefineEvents.ON_SET_GUILD_ROLES_SUCCESS = ""            -- 设置公会职位成功
DefineEvents.ON_GUILD_RECOMMEND_INVITE_IDS_CHANGED = "" -- 公会推荐邀请列表变更
DefineEvents.ON_GUILD_RESPONSE_INVITE_IDS_CHANGED = ""  -- 公会响应邀请列表变更
DefineEvents.ON_SET_GUILD_APPLY_AUTO_AGREE = ""         -- 设置公会自动同意申请成功
DefineEvents.ON_GUILD_MEMBER_UPDATE = ""                -- 公会成员更新
DefineEvents.ON_REFRESH_GUILD_WELFARE = ""              -- 刷新公会福利信息

--endregion 社交系统

--region PVP

---历史记录
DefineEvents.PVP_JUMP_TO_NEXT_HISTORY = ""

---3v3
DefineEvents.ARENA3V3_PVP_MATCH_RESULT = ""
DefineEvents.ARENA3V3_PVP_AUTO_MATCH_CANCEL = ""

---12V12
DefineEvents.PVP_ON_12V12_MAGIC_POINT_UPDATE = ""
---UI Events
DefineEvents.ON_OPEN_FULLSCREEN_PANEL = ""
DefineEvents.ON_UI_OPEN = ""
DefineEvents.ON_UI_CLOSE = ""

---PVP Champion 比武大会
DefineEvents.PVP_CHAMPION_TROOP_CREATE = ""
DefineEvents.PVP_CHAMPION_TROOP_JOIN = ""
DefineEvents.PVP_CHAMPION_TROOP_QUIT = ""
DefineEvents.PVP_CHAMPION_TROOP_DISBAND = ""
DefineEvents.PVP_CHAMPION_GET_TROOP_BRIEF = ""
DefineEvents.PVP_CHAMPION_GET_TROOP_DETAIL = ""
DefineEvents.PVP_CHAMPION_TROOP_UPDATE = ""
DefineEvents.PVP_CHAMPION_NAME_CHANGED = ""
DefineEvents.PVP_CHAMPION_TROOP_SIGNUP = ""
DefineEvents.PVP_CHAMPION_GET_RANKLIST = ""
DefineEvents.PVP_CHAMPION_GET_PERSONAL_MATCHES = ""
DefineEvents.PVP_CHAMPION_GET_BATTLE_MATCHES = ""
DefineEvents.PVP_CHAMPION_GET_STAGE_INFO = ""
DefineEvents.PVP_CHAMPION_GET_TROOP_MEMBER_INFO = ""
DefineEvents.PVP_CHAMPION_GET_ELIMINATION_MATCHES = ""
DefineEvents.PVP_CHAMPION_LEADER_TRANSFERED = ""
DefineEvents.PVP_CHAMPION_GET_REGION_NUM = ""
DefineEvents.PVP_CHAMPION_GET_SUPPORT_INFO = ""

--endregion PVP

---Recharge 充值系统
DefineEvents.RECHARGE_RECV_ITEM_INFO = ""
DefineEvents.RECHARGE_ITEM_NOT_FOUND = ""
DefineEvents.RECHARGE_RECV_PAYMENT_SIGN = ""
DefineEvents.RECHARGE_PURCHASE_SUCCESS = ""
DefineEvents.RECHARGE_PURCHASE_FAILED = ""
DefineEvents.RECHARGE_TIME_OUT = ""
DefineEvents.RECHARGE_ADD_PRODUCT = ""

--region 技能管理

---技能解锁与轮盘
DefineEvents.SERVER_ON_SKILL_INFO_CHANGED = ""
DefineEvents.SERVER_ON_SKILL_LIST_CHANGED = ""
DefineEvents.SERVER_ON_SKILL_WHEEL_CHANGED = ""

---技能管理与套路
DefineEvents.ON_SKILL_SCHEME_RENAME = ""
DefineEvents.ON_SKILL_SCHEME_SWITCH = ""
DefineEvents.ON_SKILL_CUSTOMIZER_SELECT = ""
DefineEvents.ON_SKILL_REFRESHED = ""
DefineEvents.ON_SKILL_LEVEL_UP = ""
DefineEvents.ON_SKILL_ROULETTE_ON = ""
DefineEvents.ON_SKILL_ROULETTE_OFF = ""
DefineEvents.ON_SKILL_IN_ROULETTE_CLICK = ""
DefineEvents.ON_SKILL_SCHEME_NAME_CHANGE_FAILED = ""
DefineEvents.ON_SKILL_SCHEME_ADDED = ""
DefineEvents.ON_SKILL_TRAIT_TOGGLED = ""
DefineEvents.ON_SKILL_SCHEME_SWTICH_TOGGLED = ""
DefineEvents.ON_SKILL_PRESET_APPLY = ""
DefineEvents.ON_SKILL_CUSTOMIZER_PANEL_REFRESH = ""

---ElementCore 职业形态与旧版元素核心
DefineEvents.ON_PROFESSION_STATE_CHANGED = ""
DefineEvents.ON_ELEMENT_CORE_CHANGED = ""
DefineEvents.ON_TALENT_TREE_NODE_UPGRADE = ""
DefineEvents.ON_TALENT_TREE_NODE_RESET = ""
DefineEvents.ON_TALENT_RES_LEVEL_UPGRADE = ""
DefineEvents.ON_TALENT_TREE_MULTINODE_UPGRADE = ""

--endregion 技能管理

---Dance系统 zhangfeng15
DefineEvents.ON_DANCE_ITEM_JUDGE_END = ""  -- 跳舞QTE反馈
DefineEvents.ON_DANCE_MUSIC_SYNC_BEAT = ""  -- 跳舞音乐节拍同步
DefineEvents.ON_DANCE_STOP = ""				-- 跳舞结束(中断/正常结束)

--战斗资源 zhangfeng15
DefineEvents.ON_SELF_ULTIMATE_POINT_CHANGED	= ""  -- 玩家终极点变化

--玩家交互事件
DefineEvents.ON_VIEWPORT_RESIZED = "" -- 游戏窗口大小发生变化

--通用交互物
DefineEvents.ON_COMMON_INTERACTOR_DESTROY = ""
DefineEvents.ON_COMMON_INTERACTOR_DISPLAY_NAME_CHANGED = ""
DefineEvents.ON_COMMON_INTERACTOR_INTERACT_STATE_CHANGED = ""  -- 通用交互物交互状态



--交互 liufan10
DefineEvents.INTERACTIVE_BEGIN = ""                        -- 开始交互
DefineEvents.INTERACTIVE_FINISH = ""                       -- 结束交互
DefineEvents.INTERACTOR_ON_RET_INITIATE_INTERACT = ""      -- 服务器返回发起交互的结果
DefineEvents.STATE_CONFLICT_CANCEL_INTERACT = ""

DefineEvents.LSCENEACTOR_ON_BORN_WITH_INSID = "" --场景物出生,但使用InsID抛出Unique事件
DefineEvents.LSCENEACTOR_ON_DESTROY = "" --场景物体销毁
DefineEvents.LSCENEACTOR_TASK_COLLECT_COLLETABLE_CHANGED = "" --采集物可采集变化
DefineEvents.LSCENEACTOR_DROP_ITEM_STATE_CHANGE = "" --掉落物状态变化
DefineEvents.LSCENEACTOR_HEAD_TITLE_CHANGED = "" --头顶称号变化
DefineEvents.LSCENEACTOR_HEAD_NAME_CHANGED = "" --头顶名字变化

DefineEvents.HUD_MARK_STATE_CAHNGED = ""
DefineEvents.HUD_JOYSTICK_MOVE_START = ""
DefineEvents.ON_HUD_ROUTTLE_STATE_CHANGED = ""

--灵视 liufan10
DefineEvents.SPIRIT_VISUAL_ON_ENABLE = ""
DefineEvents.SPIRIT_VISUAL_ON_OPEN = ""
DefineEvents.SPIRIT_VISUAL_ON_CLOSE = ""

DefineEvents.SPIRIT_VISUAL_EFFECT_START = ""
DefineEvents.SPIRIT_VISUAL_EFFECT_END = ""

--Actor的进入灵视状态
DefineEvents.ACTOR_ENTER_SPIRIT_VISUAL_STATE = ""
DefineEvents.ACTOR_LEAVE_SPIRIT_VISUAL_STATE = ""
DefineEvents.ACTOR_SPIRIT_VISUAL_DESTROY = ""

-- FireJump
DefineEvents.ON_FLAMEJUMP_UPDATE = ""

-- Loading
DefineEvents.LOADING_ON_REAL_LOADING_START = ""
DefineEvents.LOADING_ON_LOADING_COMPLETE = ""

-- Dungeon
DefineEvents.DUNGEON_ON_READINESS_CHECK_END = ""
DefineEvents.DUNGEON_ON_MEMBER_ACCEPT_READY = ""
DefineEvents.DUNGEON_ON_GET_FIRST_PASSAGE_RECORD = ""

-- Dungeon Award
DefineEvents.DUNGEON_ON_SELECT_AUCTION = ""
DefineEvents.DUNGEON_ON_ASSIGNMENT_UPDATE = ""
DefineEvents.DUNGEON_ON_ASSIGNMENT_ROLL_RESULT = ""
DefineEvents.DUNGEON_ON_AUCTION_UPDATE = ""
DefineEvents.DUNGEON_ON_AUCTION_GIVEUP = ""
DefineEvents.DUNGEON_ON_AUCTION_ESTIMATION = ""
DefineEvents.DUNGEON_ON_AUCTION_TIPS_INDICATOR = ""
DefineEvents.DUNGEON_SHOW_DUNGEON_AWARD_ASSIGNMENT = ""
DefineEvents.DUNGEON_SHOW_DUNGEON_AWARD_AUCTION = ""
DefineEvents.DUNGEON_SHOW_DUNGEON_AWARD_TIPS = ""

-- Battle Statistics
DefineEvents.BATTLE_LAST_ATTACK = ""
DefineEvents.BATTLE_STATISTICS_UI_UPDATE = ""
DefineEvents.BATTLE_STATISTICS_CLEAR_DATA = ""


-- 追踪
DefineEvents.TRACE_ON_CURRENT_TRACE_CHANGED = ""
DefineEvents.TRACE_ADD_MAP_TAG_EDGE_TAG = ""
DefineEvents.TRACE_REMOVE_EDGE_TAG = ""
DefineEvents.TRACE_ADD_NORMAL_TRACE_TAG = ""
DefineEvents.TRACE_REMOVE_NORMAL_TRACE_TAG = ""

-- 寻路
DefineEvents.AUTO_NAVIGATION_START = ""
DefineEvents.AUTO_NAVIGATION_END = ""
DefineEvents.AUTO_NAVIGATION_INCREMENT = ""
DefineEvents.AUTO_NAVIGATION_UPDATE = ""

-- 地图
DefineEvents.ON_MAP_TELEPORT_UNLOCK = ""
DefineEvents.ON_MAPUI_RATIO_CHANGED = ""
DefineEvents.MAPTAG_OUTER_RANGE_VISIBLE_UPDATE = ""  -- 地图标记显示外圈

--生活物资回收
DefineEvents.ON_GUILD_MATERIAL_TASK_PRICE = ""
DefineEvents.ON_GUILD_MATERIAL_TASK_REFRESH = ""
DefineEvents.ON_GUILD_MATERIAL_TASK_HELP_REFRESH = ""
DefineEvents.ON_GUILD_MATERIAL_TASK_HELP_IN_SAME_GUILD = ""

DefineEvents.ACTIVITY_OPEN = ""          --活动管理-活动状态开启
DefineEvents.ACTIVITY_CLOSE = ""         --活动管理-活动状态关闭
DefineEvents.ACTIVITY_STATUS_CHANGE = "" --活动管理-活动状态改变
DefineEvents.ACTIVITY_LOCK2UNLOCK = ""   --活动管理-活动解锁

---家园
DefineEvents.ON_MANOR_FURNITURE_UPDATE =""           --摆放或回收了场景中的组件
DefineEvents.ON_CUTTABLE_TREE_DESTROED = ""          --可以砍的树被砍掉
DefineEvents.ON_FURNITURE_UNLOCK = ""			     --家具解锁
DefineEvents.ON_MANOR_NAME_MODIFY = ""               --家园名称修改
DefineEvents.ON_MANOR_INTRODUCTION_MODIFY = ""	     --家园介绍修改
DefineEvents.ON_MANOR_RECEIVED_FRIENDS_INFO = ""     --收到好友家园信息

-- 邮件
DefineEvents.MAIL_REFRESH_ALL = ""                -- 刷新所有邮件
DefineEvents.MAIL_ITEM_RECEIVED = ""              -- 邮件物品已领取
DefineEvents.MAIL_ITEM_GET_DETAIL = ""            -- 邮件详情
DefineEvents.MAIL_SHOW_DETAIL = ""                -- 邮件详情界面刷新
DefineEvents.MAIL_SERVER_NOTIFY_NEW_MAIL = "" 	  -- 服务器通知有新邮件
DefineEvents.MAIL_DETAIL_CLOSE = ""                -- 邮件详情界面关闭
DefineEvents.FAV_REFRESH_ALL = ""                  -- 收藏列表刷新
DefineEvents.FAV_ITEM_ADD = ""                     -- 收藏添加
DefineEvents.FAV_ITEM_DELETE = ""                  -- 收藏删除

--StarMystery
DefineEvents.ON_STAR_MYSTERY_COMPLETE = ""

--- 货币 ---
DefineEvents.RECEIVE_MONEY_CHANGE = ""
DefineEvents.UPDATE_CURRENCY_LIST_PANEL = "" --仅用于全屏界面右上角货币列表的监听，防止与全屏界面本身冲突

--consignment 货币寄售
DefineEvents.CONSIGNMENT_LIST_UPDATE = ""
DefineEvents.CONSIGNMENT_OWNER_SELLINGLIST = ""
DefineEvents.CONSIGNMENT_OWNER_SOLDLIST = ""
DefineEvents.CONSIGNMENT_OWNER_BUYLIST = ""
DefineEvents.CONSIGNMENT_OWNER_OFFLIST = ""
DefineEvents.CONSIGNMENT_SOLD_NOTIFY = ""

--对战模式与红名
DefineEvents.PLAYER_CHARACTER_BATTLE_APPEARANCE_CHANGED = "" --玩家红名表现刷新事件
DefineEvents.OTHER_PLAYERS_CHARACTER_BATTLE_APPEARANCE_CHANGED = "" --其他玩家红名表现刷新事件

--屏幕特效
DefineEvents.SHOW_SCREEN_EFFECT_Bottom = ""
DefineEvents.SHOW_SCREEN_EFFECT_Top = ""
DefineEvents.REMOVE_SCREEN_EFFECT = ""

-- 问价&心情查看
DefineEvents.ON_NPC_MOOD_CHANGE = ""
DefineEvents.ON_CUT_PRICE_FINISH = ""
DefineEvents.ON_CUT_PRICE_UPDATE = ""

--成就系统
DefineEvents.ACHIEVEMENT_STATUS_CHANGE = "" --成就完成状态改变
DefineEvents.ACHIEVEMENT_LEVEL_STATUS_CHANGE = "" --成就大类等级，积分，阶段奖励状态改变
DefineEvents.ACHIEVEMENT_FINISHI_UPDATE = ""

--战斗信息
DefineEvents.BATTLE_BUFF_START = "" -- 战斗Buff开始
DefineEvents.BATTLE_BUFF_END = ""  --  战斗Buff结束
DefineEvents.BATTLE_BUFF_LAYER_CHANGED = "" -- 战斗Buff层数改变
DefineEvents.BATTLE_BUFF_TOTALLIFE_CHANGED = "" -- 战斗Buff总生命值改变
DefineEvents.BATTLE_LOCKTARGET_CHANGE = "" -- 战斗锁定目标改变
DefineEvents.BATTLE_SKILLTARGET_UPDATE = ""    -- 释放技能前的技能目标更新（本地事件）
DefineEvents.BATTLE_TAKE_DAMAGE = "" -- 战斗受到伤害
DefineEvents.BATTLE_COUNDDOWN3DUI_START = "" -- 战斗倒计时3DUI开始
DefineEvents.BATTLE_COUNDDOWN3DUI_END = "" -- 战斗倒计时3DUI结束
DefineEvents.BATTLE_PROGRESS3DUI_START = "" -- 战斗倒计时文本开始
DefineEvents.BATTLE_ON_SKILL_BLOCK = "" -- 战斗技能被格挡
DefineEvents.ON_RECEIVE_CONTINUOUS_DAMAGE_COUNT = "" -- 接收持续伤害计数


-- 系统输入相关事件
DefineEvents.OK_KEY_HINT_UPDATE = ""
DefineEvents.ON_INPUT_UI_SHORTCUT = ""

-- Popup
DefineEvents.UI_POPTIPS_REMOVE = ""

-- 背包
DefineEvents.BAG_ITEM_CHANGE = ""
DefineEvents.ON_WAREHOUSE_PAGE_CHANGE = ""
DefineEvents.ON_UNLOCK_WAREHOUSE_PAGE = ""
DefineEvents.BAG_ITEM_COUNT_CHANGE = ""
DefineEvents.BAG_MAIN_HUD_MEDICINE_CHANGE = ""
DefineEvents.BAG_ITEM_ADD_OR_DELETE = ""
DefineEvents.BAG_ITEMS_DECOMPOSE_BATCH = ""
DefineEvents.BAG_ONLINE_SYNCHRONIZATION = ""
DefineEvents.BAG_ITEM_DECOMPOSE = ""
DefineEvents.BAG_ITEM_ORGANIZE = ""
DefineEvents.BAG_PAGE_INVENTORY_CHANGE = ""
DefineEvents.BAG_AUTO_HP_FOOD_LIST_CHANGE = ""
DefineEvents.BAG_AUTO_HP_MEDICINE_LIST_CHANGE = ""
DefineEvents.BAG_TIPS_SELECT_ITEM = "" -- 背包tips中选中物品时的事件
DefineEvents.BAG_AUTO_DECOMPOSE_BTN_OPEN_SWITCH = "" 
DefineEvents.ON_QUICK_MEDICINE_ITEM_ID_CHANGED = ""

--公会联赛
DefineEvents.ON_GUILD_LEAGUE_SET_STAGE = "" -- 设置公会联赛阶段
DefineEvents.ON_GUILD_LEAGUE_SET_START_TIMESTAMP = "" -- 设置公会联赛开始时间戳
DefineEvents.ON_GUILD_LEAGUE_SET_END_TIMESTAMP = "" -- 设置公会联赛结束时间戳
DefineEvents.ON_ENTER_GUILD_LEAGUE = "" -- 进入公会联赛
DefineEvents.ON_GET_GUILD_LEAGUE_STAT = "" -- 获取公会联赛状态
DefineEvents.ON_MSG_OCCUPY_DETECT_AREA_READY_OPEN = "" -- 祭坛区域准备开启
DefineEvents.ON_MSG_GUILD_LEAGUE_CREATE_RESOURCE_MONSTER = "" -- 创建资源怪物
DefineEvents.ON_MSG_GUILD_LEAGUE_CREATE_TOWER = "" -- 创建防御塔
DefineEvents.ON_MSG_SYNC_GUILD_LEAGUE_MAP_INFO = "" -- 同步公会联赛地图信息
DefineEvents.ON_GUILD_LEAGUE_ALTAR_INIT = "" -- 祭坛初始化
DefineEvents.ON_GUILD_LEAGUE_ALTAR_INIT_END = "" -- 祭坛初始化结束
DefineEvents.ON_GUILD_LEAGUE_ALTAR_PROGRESS_CHANGED = "" -- 祭坛进度改变
DefineEvents.ON_GUILD_LEAGUE_ALTAR_STATUS_CHANGED = "" -- 祭坛状态改变
DefineEvents.ON_GUILD_LEAGUE_ALTAR_ACTIVATE_CHANGED = "" -- 祭坛激活状态改变
DefineEvents.ON_GUILD_LEAGUE_ALTAR_CAMP_CHANGED = "" -- 祭坛阵营改变
DefineEvents.ON_GUILD_LEAGUE_ALTAR_SHOW_CHANGED = "" -- 祭坛显示状态改变
DefineEvents.ON_GUILD_LEAGUE_RESOURCE_CHANGED = "" -- 公会联赛资源改变
DefineEvents.ON_RECV_GUILD_LEAGUE_GENERALINFO = "" -- 接收公会联赛通用信息
DefineEvents.ON_RECV_GUILD_LEAGUE_PRE_ZONE_ID = "" -- 接收公会联赛预选赛区域ID
DefineEvents.ON_RECV_GUILD_LEAGUE_GAMEINFO = "" -- 接收公会联赛游戏信息
DefineEvents.ON_GUILD_LEAGUE_RET_USE_STRATEGY_ID = "" -- 使用公会联赛策略ID
DefineEvents.ON_GUILD_LEAGUE_RET_ADD_FLAG_MARK = "" -- 收到服务器添加标记的消息返回
DefineEvents.ON_GUILD_LEAGUE_RET_REMOVE_FLAG_MARK = "" -- 收到服务器移除标记的消息返回
DefineEvents.ON_GUILD_LEAGUE_REFRESH_TAG = "" -- 刷新公会联赛标记
DefineEvents.ON_GUILD_LEAGUE_RET_USE_QUICK_INSTRUCTION = "" --收到服务器使用快捷信息的消息返回
DefineEvents.ON_GUILD_LEAGUE_RET_REQUEST_GROUP_INFO = "" --请求场景团队信息，收到回调
DefineEvents.ON_GUILD_LEAGUE_TAG_TEXT_CHANGE = "" --收到指挥标记设置文本改变
DefineEvents.ON_GUILD_LEAGUE_GROUP_CLEAR_TAGS = "" --某个团队的标记清空
DefineEvents.ON_MSG_GUILD_LEAGUE_TOWER_ATTACKED_BY_AREA = "" --防御塔被祭坛攻击
DefineEvents.ON_GUILD_LEAGUE_LOCKING_TOWER_IDS = "" -- 锁定防御塔ID
DefineEvents.ON_GUILD_LEAGUE_MAP_ON_SWITCH_TOP_TAB = "" --切换公会战地图顶部标签
DefineEvents.ON_GUILD_LEAGUE_MAP_TAG_CHANGE_POSITION = "" --公会地图标点位置移动
DefineEvents.ON_GUILD_LEAGUE_MAP_TAG_START_LONG_PRESS = "" --公会地图开始长按
DefineEvents.ON_GUILD_LEAGUE_MAP_TAG_END_LONG_PRESS = "" --公会地图结束长按
DefineEvents.ON_GUILD_LEAGUE_STRATEGY_SELECTED = "" --公会战策略选中
DefineEvents.ON_GUILD_LEAGUE_STRATEGY_UNSELECTED = "" --公会战策略取消选中

-- 死亡复活
DefineEvents.ON_BOSS_BATTLE_REVIVE_CHANGE = "" -- BOSS战复活状态改变
DefineEvents.ON_REVIVE_HELP_TIMESTAMP = "" -- BOSS战复活帮助时间戳
DefineEvents.ON_REVIVERECORDS_ALLOW_TIMESTAMP = "" -- BOSS战复活记录允许时间戳
DefineEvents.ON_OUTOFCONTROLL_TIMESTAMP = "" -- BOSS战出局时间戳

--region白名单
-- 这里需要是EEventTypesV2的，因为运行时用的是这个
DefineEventsDetectWhiteList = {  -- luacheck: ignore
    [EEventTypesV2.LEVEL_ON_LEVEL_LOAD_START] = true,  -- hujianglong：地图开始加载
    [EEventTypesV2.LEVEL_ON_LEVEL_LOADED] = true,  -- hujianglong：地图加载完成
    [EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED] = true,  -- hujianglong：主角创建完成
    [EEventTypesV2.ON_CLIMATE_CHANGE] = true,  -- jiangyueming：天气系统
    [EEventTypesV2.SETTING_ONSET_HEADER_VISIBILITY] = true,  -- @huangjinbao 宝哥后续改掉
    [EEventTypesV2.ON_COMMON_INTERACTOR_DISPLAY_NAME_CHANGED] = true,-- @huangjinbao 宝哥后续改掉
    [EEventTypesV2.SETTING_ON_HP_SHOW] = true,  -- @huangjinbao 宝哥后续改掉
    [EEventTypesV2.ROLE_ON_BORN] = true,  -- zhangfeng: 战场效果
    [EEventTypesV2.MODULE_LOCK_CHANGE] = true,  -- shenxudong：模块解锁
    [EEventTypesV2.QUEST_ON_TARGET_FINISHED] = true,  -- @huagnjingbao 头顶ui,宝哥后续改掉
    [EEventTypesV2.NPC_TALK_CHANGED] = true,  -- @huangjinbao 头顶ui，宝哥后续改掉
    [EEventTypesV2.SPIRIT_VISUAL_EFFECT_START] = true, -- @liufan 灵视开始
    [EEventTypesV2.SPIRIT_VISUAL_EFFECT_END] = true, -- @liufan 灵视结束
    [EEventTypesV2.ROLE_ACTION_INPUT_EVENT] = true, -- @baizihan 输入操作
    [EEventTypesV2.RECEIVE_GUILD_INFO] = true,-- @yecaifeng05 涉及公会头顶徽章，等头顶信息重构之后移除
    [EEventTypesV2.ON_LEAVE_GUILD_SUCCESS] = true,-- @yecaifeng05 涉及公会头顶徽章，等头顶信息重构之后移除
    [EEventTypesV2.GUILD_CHANGE_NAME_SUCCESS] = true,-- @yecaifeng05 涉及公会头顶徽章，等头顶信息重构之后移除
    [EEventTypesV2.ON_GUILD_BADGE_CHANGE] = true,-- @yecaifeng05 涉及公会头顶徽章，等头顶信息重构之后移除
    [EEventTypesV2.ON_SET_GUILD_BADGE_INDEX_SUCCESS] = true,-- @yecaifeng05 涉及公会头顶徽章，等头顶信息重构之后移除
    [EEventTypesV2.LEAVE_GUILD] = true,-- @yecaifeng05 涉及公会头顶徽章，等头顶信息重构之后移除
    [EEventTypesV2.OTHER_PLAYERS_CHARACTER_BATTLE_APPEARANCE_CHANGED] = true, -- @chenhonghua 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.PLAYER_CHARACTER_BATTLE_APPEARANCE_CHANGED] = true,	-- @chenhonghua 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.BATTLE_LOCKTARGET_CHANGE] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除

    [EEventTypesV2.ON_MSG_GUILD_LEAGUE_TOWER_ATTACKED_BY_AREA] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_LOCKING_TOWER_IDS] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_SELECTED] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_STRATEGY_UNSELECTED] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_MAP_ON_SWITCH_TOP_TAB] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_TAG_TEXT_CHANGE] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_MAP_TAG_CHANGE_POSITION] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_RET_ADD_FLAG_MARK] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_RET_REMOVE_FLAG_MARK] = true, -- @zhangfeng 涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_SHOW_CHANGED] = true, -- @zhangfeng  涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_MSG_OCCUPY_DETECT_AREA_READY_OPEN] = true, -- @zhangfeng  涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_GUILD_LEAGUE_ALTAR_PROGRESS_CHANGED] = true, -- @zhangfeng  涉及地图标点，等地图标点重构之后移除
    [EEventTypesV2.ON_SELF_TEAMINFOLIST_CHANGED] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    
    [EEventTypesV2.CLIENT_GROUP_MEMBER_MARK_CAHNGED] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.ON_TEAMMEMBER_MEMBERFLAG_CHANGED] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.ON_TEAMID_CHANGED] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.GROUP_DEL_MEMBER] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.ON_SELF_CAPTAIN_CHANGED] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.ON_MAINPLAYER_GROUP_ID_CHANGED] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.CLIENT_GROUP_TEAMLEADER_UPDATE] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.GROUP_LEADER_CHANGED] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.CLIENT_GROUP_CREATE] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.CLIENT_GROUP_MEMBER_EXCHANGE] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    [EEventTypesV2.CLIENT_GROUP_DISBAND] = true,	-- @chenpengfei06 涉及玩家头顶名字和血条，等头顶信息重构之后移除
    
	[EEventTypesV2.BATTLE_BUFF_START] = true, -- @zhangfeng 涉及玩家头顶名字和血条，等头顶信息重构之后移除
	[EEventTypesV2.ON_SKILL_REFRESHED] = true,-- @qusicheng03 技能按钮事件，新版的时候移除
	[EEventTypesV2.ON_SKILL_LEVEL_UP] = true,-- @qusicheng03 技能按钮事件，新版的时候移除
	[EEventTypesV2.ON_SKILL_ROULETTE_ON] = true,-- @qusicheng03 技能按钮事件，新版的时候移除
	[EEventTypesV2.ON_SKILL_ROULETTE_OFF] = true, -- @qusicheng03 技能按钮事件，新版的时候移除
	[EEventTypesV2.SERVER_ON_SKILL_LIST_CHANGED] = true, -- @qusicheng03 技能按钮事件，新版的时候移除
	[EEventTypesV2.SERVER_ON_SKILL_INFO_CHANGED] = true, -- @qusicheng03 技能按钮事件，新版的时候移除
}
--endregion