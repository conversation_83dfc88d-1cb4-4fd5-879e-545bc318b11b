-- luacheck: push ignore

--本文件中的定义全部当常量使用，禁止修改
--TODO 后续添加禁止修改机制支持

ZERO_VECTOR = FVector(0, 0, 0)
ONE_VECTOR = FVector(1, 1, 1)
UP_VECTOR = FVector(0, 0, 1)
DOWN_VECTOR = FVector(0, 0, -1)
FORWARD_VECTOR = FVector(1, 0, 0)
BACKWARD_VECTOR = FVector(-1, 0, 0)
RIGHT_VECTOR = FVector(0, 1, 0)
LEFT_VECTOR = FVector(0, -1, 0)
XAXIS_VECTOR = FVector(1, 0, 0)
YAXIS_VECTOR = FVector(0, 1, 0)
ZAXIS_VECTOR = FVector(0, 0, 1)
