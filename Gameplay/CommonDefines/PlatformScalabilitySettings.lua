local UC7FunctionLibrary = import("C7FunctionLibrary")

-- luacheck: push ignore
-- 不同角色质量等级的妆容RT大小
Enum.AvatarMakeUpRTSize = {
	["S"] = 1024,
	["A"] = 512,
	["B"] = 512,
}

-- 不同角色质量等级的妆容RT大小(Mobile)
Enum.AvatarMakeUpRTSizeMobile = {
	["S"] = 512,
	["A"] = 256,
	["B"] = 256,
}

Enum.QualityLevel = {
	"Low",
	"Medium",
	"High",
	"Epic",
	"Cinematic",
}

-- 合并覆盖配置（支持深层 table）
local function MergeSettings(base, overrides)
	for key, value in pairs(overrides) do
		if type(value) == "table" and type(base[key]) == "table" then
			MergeSettings(base[key], value) -- 递归合并子表
		else
			base[key] = value -- 直接覆盖
		end
	end
	return base
end

--管理硬编码的性能静态参数(不开放给玩家)
PlatformScalabilitySettings = {}

function PlatformScalabilitySettings.Reset()
	PlatformScalabilitySettings.IsMobilePlatform = PlatformUtil.IsMobilePlatform()
	PlatformScalabilitySettings.CpuLevel = UC7FunctionLibrary.GetGameplayScability() + 1

	-- ================================角色相关===========================================================
	PlatformScalabilitySettings.PlayerMaximumKeyStr = Enum.ESettingDataEnum.PlayerMaximumPC
	PlatformScalabilitySettings.NPCMaximumKeyStr = Enum.ESettingDataEnum.NPCMaximumPC
	PlatformScalabilitySettings.NpcCrowdNumFactor = 2 --行人NPC相对于NPC数量的倍数
	PlatformScalabilitySettings.MakeUpRTSize = Enum.AvatarMakeUpRTSize --妆容贴图大小
	
	-- ================================动画预算管理===========================================================
	PlatformScalabilitySettings.AnimationBudget = PlatformScalabilitySettings.AnimationBudget or {}
	PlatformScalabilitySettings.AnimationBudget.UseAnimationBudget = true
	PlatformScalabilitySettings.AnimationBudget.BudgetTime = 1.0
	PlatformScalabilitySettings.AnimationBudget.MiniFullTickNum = 5
	PlatformScalabilitySettings.AnimationBudget.UseQuadraticAlpha = true

	-- ================================绳索模拟===========================================================
	PlatformScalabilitySettings.RopePhysicSimulate = PlatformScalabilitySettings.RopePhysicSimulate or {}
	PlatformScalabilitySettings.RopePhysicSimulate.EnableLODValue = { true, true, false, false}
	PlatformScalabilitySettings.RopePhysicSimulate.TickIntervalLODValue = {0.016,0.033,0.1,0.2}
	
	-- ================================AnimLayerLink===========================================================
	PlatformScalabilitySettings.AnimLayerLink = PlatformScalabilitySettings.AnimLayerLink or {}
	PlatformScalabilitySettings.AnimLayerLink.BodyPostureLODValue = { true, true, false, false}  -- 姿态IK调整, 手与衣服的碰撞穿插IK/坐骑IK/跑步倾斜
	PlatformScalabilitySettings.AnimLayerLink.GazeLODValue = { true, true, false, false}  --凝视
	PlatformScalabilitySettings.AnimLayerLink.IKLODValue = { true, true, false, false}  --IK
	PlatformScalabilitySettings.AnimLayerLink.KawaiiLODValue = { true, true, false, false}  --动骨
	
end

function PlatformScalabilitySettings.ResetOverrides()
	if not PlatformScalabilitySettings.IsMobilePlatform then
		local MobileOverrides = require("Gameplay.CommonDefines.PlatformScalabilitySettings.Mobile")
		PlatformScalabilitySettings = MergeSettings(PlatformScalabilitySettings, MobileOverrides)
	end

	local PlatformName = PlatformUtil.GetPlatformName()
	local PlatformOverrides = require("Gameplay.CommonDefines.PlatformScalabilitySettings."..PlatformName.."_"..Enum.QualityLevel[PlatformScalabilitySettings.CpuLevel])
	PlatformScalabilitySettings = MergeSettings(PlatformScalabilitySettings, PlatformOverrides)
end

PlatformScalabilitySettings.Reset()

xpcall(PlatformScalabilitySettings.ResetOverrides, _G.CallBackError)

return PlatformScalabilitySettings
--luacheck: pop ignore