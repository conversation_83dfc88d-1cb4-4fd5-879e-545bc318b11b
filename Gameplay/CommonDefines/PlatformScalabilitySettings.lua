-- luacheck: push ignore

-- 不同角色质量等级的妆容RT大小
Enum.AvatarMakeUpRTSize = {
	["S"] = 1024,
	["A"] = 512,
	["B"] = 512,
}

-- 不同角色质量等级的妆容RT大小(Mobile)
Enum.AvatarMakeUpRTSizeMobile = {
	["S"] = 512,
	["A"] = 256,
	["B"] = 256,
}

PlatformScalabilitySettings = {}

function PlatformScalabilitySettings.ResetCpuLevel()
	PlatformScalabilitySettings.CpuLevel = Game.WorldManager:GetGameplayScability() + 1
end

function PlatformScalabilitySettings.Reset()
	PlatformScalabilitySettings.IsMobilePlatform = PlatformUtil.IsMobilePlatform()

	-- ================================角色相关===========================================================
	PlatformScalabilitySettings.PlayerMaximumKeyStr = Enum.ESettingDataEnum.PlayerMaximumPC
	PlatformScalabilitySettings.NPCMaximumKeyStr = Enum.ESettingDataEnum.NPCMaximumPC
	PlatformScalabilitySettings.NpcCrowdNumFactor = 2 --行人NPC相对于NPC数量的倍数
	PlatformScalabilitySettings.MakeUpRTSize = Enum.AvatarMakeUpRTSize --妆容贴图大小

	if PlatformScalabilitySettings.IsMobilePlatform then
		PlatformScalabilitySettings.PlayerMaximumKeyStr = Enum.ESettingDataEnum.PlayerMaximum
		PlatformScalabilitySettings.NPCMaximumKeyStr = Enum.ESettingDataEnum.NPCMaximum
		PlatformScalabilitySettings.NpcCrowdNumFactor = 1.2 --行人NPC相对于NPC数量的倍数
		PlatformScalabilitySettings.MakeUpRTSize = Enum.AvatarMakeUpRTSizeMobile --妆容贴图大小
	end

	-- ================================动画预算管理===========================================================
	PlatformScalabilitySettings.AnimationBudget = PlatformScalabilitySettings.AnimationBudget or {}
	PlatformScalabilitySettings.AnimationBudget.UseAnimationBudget = true
	PlatformScalabilitySettings.AnimationBudget.BudgetTime = 1.0
	PlatformScalabilitySettings.AnimationBudget.MiniFullTickNum = 5
	PlatformScalabilitySettings.AnimationBudget.UseQuadraticAlpha = true

	-- ================================绳索模拟===========================================================
	PlatformScalabilitySettings.RopePhysicSimulate = PlatformScalabilitySettings.RopePhysicSimulate or {}
	PlatformScalabilitySettings.RopePhysicSimulate.Enable = true
	PlatformScalabilitySettings.RopePhysicSimulate.TickIntervalLODValue = {0,0,0,0.3}
	
end

PlatformScalabilitySettings.Reset()
return PlatformScalabilitySettings
--luacheck: pop ignore