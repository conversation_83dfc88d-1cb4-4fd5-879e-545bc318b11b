local CustomizedGameplayClientBase = kg_require("Gameplay.CommonInteractor.CustomizedGameplayClientBase")

CustomizedGameplayHiddenFloor = DefineClass("CustomizedGameplayHiddenFloor", CustomizedGameplayClientBase)

function CustomizedGameplayHiddenFloor:ctor()
end

function CustomizedGameplayHiddenFloor:dtor()
end

---@region lifecycle interface

function CustomizedGameplayHiddenFloor:OnBindCommonInteractor(commonInteractor)
    local gameplayConfig = Game.TableData.GetHiddenFloorGameplayPropDataRow(self.configID)
    if gameplayConfig.Scaling and gameplayConfig.Scaling ~= 1 then
        commonInteractor:SetAppearanceScale(gameplayConfig.Scaling)
        -- 直接缩放的root，挂的trigger已经缩放了，所以不需要缩放
        --commonInteractor:ResizeTriggers(gameplayConfig.Scaling)
    end
end

---@endregion lifecycle interface

---@region interface need to override

function CustomizedGameplayHiddenFloor:GetChildCommonInteractorNum()
    local gameplayConfig = Game.TableData.GetHiddenFloorGameplayPropDataRow(self.configID)
    return gameplayConfig.ColumnNum * gameplayConfig.RowNum
end

---@endregion interface need to override

return CustomizedGameplayHiddenFloor
