local CustomizedGameplayHiddenFloor = kg_require("Gameplay.CommonInteractor.CustomizedGameplayHiddenFloor")
local CustomizedGameplayMirrorReflection = kg_require("Gameplay.CommonInteractor.CustomizedGameplayMirrorReflection")

---当前仅用于简单的通用交互物创建和销毁流程, 后续美术场景交互物完成后, 还会处理相对更复杂一些的UE Actor异步绑定流程
---@class CommonInteractorManager
CommonInteractorManager = DefineClass("CommonInteractorManager")

CommonInteractorManager.GAMEPLAY_TYPE = {
    [Enum.CustomizedGameplayType.HIDDEN_FLOOR] = CustomizedGameplayHiddenFloor,
    [Enum.CustomizedGameplayType.MIRROR_REFLECTION] = CustomizedGameplayMirrorReflection,
}

function CommonInteractorManager:ctor()
    -- key: instance id, value: common interactor local entity
    ---@type table<string, CommonInteractor>
	self.CommonInteractors = {}
    -- 注意: GM创建的通用交互物不在这里, 所以这里不是所有通用交互物的全集
    self.CommonInteractorsByInsIDs = {}

    -- key: instance id, value: customized gameplay instance
    self.CustomizedGameplays = {}

    -- 监听的事件,key:eventType, value:通用交互物id列表
    self.registerEventList = {}
end

function CommonInteractorManager:Init()
end

function CommonInteractorManager:UnInit()
end

-- 美术布置的通用交互物场景物件创建，纯客户端
function CommonInteractorManager:CreateCommonInteractorBySceneObject(id, sceneConf)
    local templateID = sceneConf.TemplateID
    local insID = sceneConf.ID
    local rowData = Game.TableData.GetCommonInteractorDataRow(templateID)
    if not rowData then
        Log.ErrorFmt("cannot find common interactor config, templateID:%s, insID:%s", templateID, insID)
        return 
    end
    -- 只允许美术场景物件通过此接口创建
    assert(rowData.ClassType == Enum.CommonInteractorClassType.ArtDesign)
    local transform = sceneConf.Transform
    local position = transform.Position
    local rotation = transform.Rotator
    local actorProps = {
        instanceID = id,
        interactorID = templateID,
        insID = insID,
        Position = { X = position.X, Y = position.Y, Z = position.Z },
        Rotation = { Pitch = rotation.Pitch, Yaw = rotation.Yaw, Roll = rotation.Roll },
        isPrivate = false
    }
    self:CreateCommonInteractor(actorProps)
end

---@param commonInteractorProp table COMMON_INTERACTOR_SYNC_ATTR
--- instanceID 为通用交互物的实例ID, insID 对应的是通用交互物的场编实例ID, 区别在于
---通过GM创建的通用交互物 insID总是为空字符串, 此时不会记录对应的通用交互物
function CommonInteractorManager:CreateCommonInteractor(commonInteractorProp)
    local commonInteractProps = {
        instanceID = commonInteractorProp.ID,
        interactorID = commonInteractorProp.interactorID,
        interactCount = commonInteractorProp.interactCount,
        curState = commonInteractorProp.curState,
        completedActions = commonInteractorProp.completedActions,
        Position = { X=commonInteractorProp.pos.X, Y=commonInteractorProp.pos.Y, Z=commonInteractorProp.pos.Z },
        Rotation = { Pitch=commonInteractorProp.rot.Pitch, Yaw=commonInteractorProp.rot.Yaw, Roll=commonInteractorProp.rot.Roll },
        cdExpiredTime = commonInteractorProp.cdExpiredTime,
        gameplayID = commonInteractorProp.gameplayID,
		insID = commonInteractorProp.insID,
        extraParam = commonInteractorProp.extraParam,
        buttonList = commonInteractorProp.buttonList,
        isPrivate = commonInteractorProp.isPrivate
    }
    
    local interactorID = commonInteractorProp.interactorID
    local instanceID = commonInteractorProp.ID
    local gameplayID = commonInteractorProp.gameplayID

    local CommonInteractorData = Game.TableData.GetCommonInteractorDataRow(interactorID)
    if CommonInteractorData == nil then
        Log.Error("cannot find common interactor config, template id ", interactorID)
        return nil
    end

    if self.CommonInteractors[instanceID] ~= nil then
        Log.Error("already found common interactor with instance id", instanceID)
        return
    end

    if commonInteractorProp.insID ~= "" and self.CommonInteractorsByInsIDs[commonInteractorProp.insID] ~= nil then
        Log.Error("already found common interactor with EP instance id", commonInteractorProp.insID)
        return
    end
    
    local CommonInteractor = Game.EntityManager:CreateLocalEntity("CommonInteractor", commonInteractProps)
    if CommonInteractor == nil then
        Log.Error("create common interactor failed", instanceID)
        return
    end

    Log.Debug("CreateCommonInteractor ", instanceID, interactorID)

    self.CommonInteractors[instanceID] = CommonInteractor
    -- 所有GM创建的通用交互物, 对应的insID为空字符串
    if commonInteractorProp.insID ~= "" then
        self.CommonInteractorsByInsIDs[commonInteractorProp.insID] = CommonInteractor 
    end

    -- 如果通用交互物本身绑定到了某个定制玩法上, 这里去处理定制玩法流程
    if gameplayID ~= nil and gameplayID ~= 0 then
        local customizedGameplay = self.CustomizedGameplays[gameplayID]
        if customizedGameplay == nil then
            Log.Error("cannot find customized gameplay ", gameplayID)
        else
            customizedGameplay:BindCommonInteractor(CommonInteractor)
        end
    end
end

function CommonInteractorManager:UpdateCommonInteractorProps(instanceID, newProps)
    local commonInteractorEntity = self.CommonInteractors[instanceID]
    if commonInteractorEntity == nil then
        Log.Warning("cannot find common interactor", instanceID)
        return
    end

    commonInteractorEntity:UpdateProps(newProps)
end

function CommonInteractorManager:DestroyCommonInteractor(instanceID)
    local commonInteractorEntity = self.CommonInteractors[instanceID]
    if commonInteractorEntity == nil then
        Log.Warning("cannot find common interactor", instanceID)
        return
    end

    Log.Debug("DestroyCommonInteractor ", instanceID)

    if commonInteractorEntity.insID ~= "" then
        self.CommonInteractorsByInsIDs[commonInteractorEntity.insID] = nil
    end
    
	commonInteractorEntity:destroy()
    self.CommonInteractors[instanceID] = nil
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_COMMON_INTERACTOR_DESTROY, instanceID)
end

-- 通过交互物自身实例ID查询交互物
function CommonInteractorManager:GetInteractorByInstanceID(instanceID)
    return self.CommonInteractors[instanceID]
end

-- 通过场编的InstanceID查询交互物
function CommonInteractorManager:GetInteractorByEPInstanceID(EPInstanceID)
    return self.CommonInteractorsByInsIDs[EPInstanceID]
end

function CommonInteractorManager:ClearAllInteractors()
    for _, commonInteractor in pairs(self.CommonInteractors) do
		commonInteractor:destroy()
    end

    table.clear(self.CommonInteractors)
    table.clear(self.CommonInteractorsByInsIDs)

    for _, customizedGameplay in pairs(self.CustomizedGameplays) do
        customizedGameplay:DestroyGameplay()
    end

    table.clear(self.CustomizedGameplays)
end

function CommonInteractorManager:CreateCustomizedGameplay(gameplayType, instanceID, configID, pos, rot)
    Log.Debug("CreateCustomizedGameplay ", gameplayType, instanceID, configID)
    if self.CustomizedGameplays[instanceID] ~= nil then
        Log.Error("gameplay instance id already exists", gameplayType, instanceID)
        return
    end

    local gameplayImpl = CommonInteractorManager.GAMEPLAY_TYPE[gameplayType]
    if gameplayImpl == nil then
        Log.Error("unrecognized gameplay type", gameplayType, instanceID)
        return
    end

    local gameplayInst = gameplayImpl.new()
    gameplayInst:InitGameplay(instanceID, configID, nil, pos, rot)

    self.CustomizedGameplays[instanceID] = gameplayInst
end

function CommonInteractorManager:ActivateCustomizedGameplay(instanceID)
    local customizedGameplay = self.CustomizedGameplays[instanceID]
    if customizedGameplay == nil then
        Log.Warning("cannot find customized gameplay instance", instanceID)
        return
    end

    customizedGameplay:ActivateGameplay()
end

function CommonInteractorManager:DeactivateCustomizedGameplay(instanceID)
    local customizedGameplay = self.CustomizedGameplays[instanceID]
    if customizedGameplay == nil then
        Log.Warning("cannot find customized gameplay instance", instanceID)
        return
    end

    customizedGameplay:DeactivateGameplay()
end

function CommonInteractorManager:DestroyCustomizedGameplay(instanceID)
    Log.Debug("DestroyCustomizedGameplay ", instanceID)
    local customizedGameplay = self.CustomizedGameplays[instanceID]
    if customizedGameplay == nil then
        Log.Warning("cannot find customized gameplay instance", instanceID)
        return
    end

    self.CustomizedGameplays[instanceID] = nil
    customizedGameplay:DestroyGameplay()
end

function CommonInteractorManager:GetCustomizedGameplayByInstanceID(instanceID)
    return self.CustomizedGameplays[instanceID]
end

function CommonInteractorManager:GetAllCommonInteractors()
	return self.CommonInteractors
end

-- 注册事件监听
function CommonInteractorManager:RegisterEvent(ID, EventType)
    local registerEventList = self.registerEventList
    local eventList = registerEventList[EventType]
    if eventList == nil then
        eventList = {}
        registerEventList[EventType] = eventList
    end

    eventList[ID] = true
end

-- 移除事件监听
function CommonInteractorManager:UnregisterEvent(ID, EventType)
    local registerEventList = self.registerEventList
    local eventList = registerEventList[EventType]
    if eventList == nil then
        return
    end

    eventList[ID] = nil

    -- EventType类型比较少,可以不销毁
    -- 不然某些场景可能会频繁创建和销毁table
    -- if not next(eventList) then
    --     registerEventList[EventType] = nil
    -- end
end

-- 将某个交互物的所有监听事件全部移除
function CommonInteractorManager:UnregisterEventByID(ID)
    local registerEventList = self.registerEventList
    for _, eventList in pairs(registerEventList) do
        eventList[ID] = nil
    end
end

-- 触发通用交互物事件
function CommonInteractorManager:TriggerCommonInteractorEvent(EventType, EventParams)
    local registerEventList = self.registerEventList
    local eventList = registerEventList[EventType]
    if eventList == nil then
        return
    end

    -- 遍历所有注册了该事件的通用交互物,调用OnTriggerEvent
    local CommonInteractors = self.CommonInteractors
    for id, _ in pairs(eventList) do
        local commonInteractor = CommonInteractors[id]
        if commonInteractor ~= nil then
            commonInteractor:OnTriggerEvent(EventType, EventParams)
        else
            eventList[id] = nil
        end
    end
end

-- 通用区域设置阻挡
function CommonInteractorManager:SetCommonAreaBlock(insId)
    local commonInteractor = self:GetInteractorByEPInstanceID(insId)
    if commonInteractor then
        commonInteractor:UpdateCommonAreaBlock(true)
    else
        Log.WarningFormat("SetCommonAreaBlock cannot find interactor, %s", insId)
    end
end
