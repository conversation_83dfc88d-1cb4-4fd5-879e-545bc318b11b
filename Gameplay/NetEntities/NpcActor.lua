---
---NpcActor
---

local CollisionConst = kg_require("Shared.Const.CollisionConst")


local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
-- local view_resource_const = kg_require("Gameplay.CommonDefines.ViewResourceConst")
-- local BOSS_TYRE_ANIM_IDLE_POSTURE_CHANGE_MAP = view_resource_const.BOSS_TYRE_ANIM_IDLE_POSTURE_CHANGE_MAP
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local EWActorType = kg_require("Shared.WorldActorDefine").EWActorType
local EC7ShapeCollisionType = import("EC7ShapeCollisionType")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

---@class NpcActor
NpcActor = DefineEntity("NpcActor",
        { ActorBase },
        {
          FightPropComponent,
          FStatePropComponent,
          TakeDamageComponent,
          HitFeedbackComponent,
          MoveComponent,
          CurveMoveComponent,
          DebugComponent,
          DamageTrackComponent,
          NpcAggroComponent,
          NpcAsideTalkComponent,
          StateComponent,
          BreakDefenseComponent,
          PerformanceComponent,
          ParallelBehaviorControlCompBase,
          ComplexLocomotionControlComponent,
          NpcViewControlComponent,
          SpiritualVisionNpcComponent,
          CombatEffectComponent,
          VehicleComponent,
		  WeaponManagerComponent,
          LUnitProxyComponent,
          NpcInteractorComponent,
          SkillComponentNew,
          AbilityComponent,
		  ClientTimelineComponent,
          NpcElementComponent,
          BuffComponentNew,
          FlowchartActionComponent,
          MorphComponent,
          GazeControlComponent,
          CampRelationComponent,
          NpcFragmentComponent,
		  ViewControlTriggerComponent,
          StateConflictNpcComponent,
		  NetEntityBaseComponent,
        })

NpcActor.COMPONENTS = {}
NpcActor.HAS_ENTITY_DEF = true

NpcActor:Register("NpcActor")

NpcActor.USE_CHARACTER_CAMERA_DITHER = true
NpcActor.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {CollisionConst.COLLISION_PRESET_NAMES.MOVABLE_UNIT_WITH_NET_DRIVE_PRESET, CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET}
NpcActor.ActorType = EWActorType.NPC
function NpcActor:ctor()
    self.isNpc = true
	self.IsNeedNpcHideRule = true
end

function NpcActor:OnCreate(Properties) -- luacheck: ignore
	ActorBase.OnCreate(self, Properties)
	if Game.WorldManager then
		Game.WorldManager:AddNpcRecord(self)
	end
end

function NpcActor:dtor()
	if Game.WorldManager then
		Game.WorldManager:RemoveNpcRecord(self)
	end
    if self.bossMechanismInstId and Game.me then
        Game.EventSystem:Publish(_G.EEventTypes.BOSS_INFO_WARNING_REMINDER, false, Game.me.BossMechanismCoolDownData[self.bossMechanismInstId])
        Game.me.BossMechanismCoolDownData[self.bossMechanismInstId] = nil
        self.bossMechanismInstId = nil
    end
end

function NpcActor:EnterWorld()
	if self.NpcType ~= Enum.ENpcTypeData.Monster then
		Game.WorldManager:RegisterClipObserver(self.CharacterID, EWActorType.NPC_TASK)
	end

	local NpcData = self:GetEntityConfigData()
	if NpcData and NpcData.bBlockMainPlayer then
		-- 阻挡玩家通过修改 MainMesh 预设阻挡,保持NPC原有胶囊体碰撞关系
		local parentPresent, _ = unpack(self.DEFAULT_PRIMITIVE_COLLISION_PRESETS)
		self:InitDefaultCollisionPreset(parentPresent, CollisionConst.COLLISION_PRESET_NAMES.SCENE_ACTOR_BLOCK_MAIN_PLAYER)
		-- Mesh的预设第一次设置碰撞会不生效,这里再刷新一次
		self:SetupCollisionForPrimitives()
		local MeshID = self.CppEntity:KAPI_Actor_GetMainSkeletalMeshComponent()
		if MeshID > 0 then
			self.CppEntity:KAPI_SkeletalMeshID_SetKinematicBonesUpdateType(MeshID,0)
		end
	end
	
    -- if not self:NeedBornStickGround() then
    --     self.CppEntity:KAPI_Movement_SetNeedBornStickGround(false)
    -- else
    --     self.CppEntity:KAPI_Movement_SetTrackFloorDisableMoveMaxTime(1.0)
    -- end

    if not self:NeedServerFixPosition() then
        self.CppEntity:KAPI_Movement_SetNeedServerFixPosition(false)
    end

    if self.BossType == Enum.EBossType.BOSS then
        self.CppEntity:KAPI_Common_SetIsBoss(true)
    end

    ActorBase.EnterWorld(self)
end

function NpcActor:AfterEnterWorld()
    ActorBase.AfterEnterWorld(self)
    BSFunc.ChangeBattleAbility(self, true)
	Game.UniqEventSystemMgr:Publish(self.InstanceID, EEventTypesV2.NPC_ENTER_WORLD_WITH_INSID, self:uid())


    self:set_BossIdlePosture(self, self.BossIdlePosture, nil)
	
    self.CppEntity:KAPI_Movement_SetNeedNotifyCalculatedNetSimulateChanged(true)

    --暂时先放在这里, 合并主干后调整
	Game.WorldManager:BindRoleMovementDynamicWaterWaveAndWindField(self.CharacterID, true)

	-- 初始化碰撞主角范围
	self:InitCollisionToMainPlayer()
	-- 出生后添加挂接物
	self:AddAttachItemWithBorn()
	self:processNpcVoice()
	
	-- 追踪系统直调
	Game.TraceSystem:OnNPCVisibleChange(self:uid(), true)
end

function NpcActor:InitDefaultCollisionPreset(ParentPreset, ChildPreset)
	if self.PRIMITIVE_COLLISION_PRESETS == nil then
		self.PRIMITIVE_COLLISION_PRESETS = {ParentPreset, ChildPreset}
	else
		self.PRIMITIVE_COLLISION_PRESETS[1] = ParentPreset
		self.PRIMITIVE_COLLISION_PRESETS[2] = ChildPreset
	end
end

function NpcActor:InitCollisionToMainPlayer()
	local NpcData = self:GetEntityConfigData()
	if NpcData.CollisionParam and #NpcData.CollisionParam > 0 then
        local CollisionType = NpcData.CollisionType
        local CollisionParam = NpcData.CollisionParam
        if CollisionType == EC7ShapeCollisionType.Box and #CollisionParam == 3 then
            self:InitMainTriggerAsBoxAndBindEvent(CollisionParam[1], CollisionParam[2], CollisionParam[3], self, "OnEnterNpcCollisionTrigger")
        else
            self:InitMainTriggerAsSphereAndBindEvent(CollisionParam[1], self, "OnEnterNpcCollisionTrigger")
        end
	end
end

function NpcActor:OnEnterNpcCollisionTrigger(OwnerUID, Pos, OtherActorUID)
	if OtherActorUID == Game.me:uid() then
		Game.me:ReqCollisionWithNpc(self.eid)
	end
end

function NpcActor:BeforeExitWorld()
	self:stopNpcVoice()
    BSFunc.ChangeBattleAbility(self, false)
    self:clearWayPointTimer()
    ActorBase.BeforeExitWorld(self)
	-- 追踪系统直调
	Game.TraceSystem:OnNPCVisibleChange(self:uid(), false)
end


function NpcActor:GetActorBPClassPath()
	local defaultNpcBPKey = ViewResourceConst.NPC_BP_KEY.NPC
    if self.NpcType ==  Enum.ENpcTypeData.Passerby or self.NpcType == Enum.ENpcTypeData.Task then
		defaultNpcBPKey = ViewResourceConst.NPC_BP_KEY.Npc_FaceControl
    elseif self.NpcType == Enum.ENpcTypeData.Monster then
		defaultNpcBPKey = ViewResourceConst.NPC_BP_KEY.Npc_FaceControl
    end
    
	local finalBPClass = ViewResourceConst.BP_USED_BY_ENTITY[defaultNpcBPKey]
	if self.CustomClassPathFromModelLib ~= nil and self.CustomClassPathFromModelLib ~= "" then
		finalBPClass = self.CustomClassPathFromModelLib
	end

    return finalBPClass
end

function NpcActor:GetActorCompositeData()
	-- 开启这里的流程就可以了
	local NpcData = self:GetEntityConfigData()
	if NpcData.bGhost and self.FinalOwnerID ~= self.eid then
		local Owner = Game.EntityManager:getEntity(self.FinalOwnerID)
		if Owner ~= nil then
			local compositeParam = Owner:GetActorCompositeData()
			compositeParam.UID = self:uid()
			return compositeParam
		end
	end
	local CompositeParam
	if not self.CustomClassPathFromModelLib or self.bBpClassNeedComposite or self.bUseBakedBPClass then
		CompositeParam = ActorBase.GetActorCompositeData(self)
	end
	
    return CompositeParam
end


-- NpcActor.PROP_TYPE = "NpcActorAttr"

-- @brief entity dead notify(临时rpc，后面改为属性同步)
-- @client rpc
-- @params:
--      - reason(int)           : 死亡原因
function NpcActor:OnMsgEntityDead(reason)
    self:Debug("NpcActor:OnMsgEntityDead:", self.eid, reason)
    self:OnRoleDead(reason, nil)
end

function NpcActor:OnMsgWayPointEvent(_, WType, Time, AssetID)
    if WType ~= 1 or type(Time) ~= "number" then
        return
    end

    local _, Duration = self:PlayAnimLibMontage(AssetID, nil, nil, nil, nil, true)
    if not Duration or Duration <= 0 then
        return
    end

	if Time < Duration and Time > 0 then
		Duration = Time
	end

    self:clearWayPointTimer()
    self.WayPointPlayHandle = Game.TimerManager:CreateTimerAndStart(function()
        self:StopAnimLibMontage()
		self.WayPointPlayHandle = nil
    end, Duration * 1000, 1)
end

function NpcActor:clearWayPointTimer()
    if self.WayPointPlayHandle then
        Game.TimerManager:StopTimerAndKill(self.WayPointPlayHandle)
		self.WayPointPlayHandle = nil
    end
end

function NpcActor:OnMsgBossMechanismCoolDown(type, instId, duration, leftTime, paramList)
    self.bossMechanismInstId = instId
    if Game.me.BossMechanismCoolDownData[instId] == nil then
        Game.me.BossMechanismCoolDownData[instId] = { Type = type, InstId = instId, Duration = duration * 1000 }
    end
    local mechanismCoolDownData = Game.me.BossMechanismCoolDownData[instId]
    mechanismCoolDownData.EndTime = os.gameRealTimeMS + leftTime * 1000
    Game.EventSystem:Publish(_G.EEventTypes.BOSS_INFO_WARNING_REMINDER, true, mechanismCoolDownData)
end

function NpcActor:OnMsgBossMechanismCoolDownFinish(type, instId)
    self.bossMechanismInstId = nil
    Game.EventSystem:Publish(_G.EEventTypes.BOSS_INFO_WARNING_REMINDER, false, Game.me.BossMechanismCoolDownData[instId])
    Game.me.BossMechanismCoolDownData[instId] = nil
end

function NpcActor:RetShowAsideTalk(asideID)
    Game.TalkSystem:ShowAsideTalk(asideID)
end

function NpcActor:OnMsgArrodesPlayEmojiPreset(PresetID)
end

function NpcActor:set_BossType(ent, new, old)
    Game.HeadInfoManager:OnBossTypeChanged(ent, new, old)
    Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.ON_BOSS_TYPE_CHANGED)
end

function NpcActor:set_DisplayName(ent, new, old)
    Game.HeadInfoManager:OnDisplayNameChanged(ent, new, old)
    Game.HUDInteractManager:OnDisplayNameChanged(ent, new, old)
end

function NpcActor:set_State(entity, new, old)
    if new ~= old then
        self:SetNpcState(new)
    end
end

function NpcActor:set_ExploreElementID(_, new, old)
    if new ~= old then
        self:OnElementReact(new, old)
    end
end

--region SneakSystem
function NpcActor:set_trapGroupID(entity, new, old)
	Game.SneakSystem:NpcEntityAddTrap(self.eid)
	
	if not Game.SneakSystem:NpcEntityHasTrap(self.eid) then
		Game.UniqEventSystemMgr:AddListener(self:uid(), EEventTypesV2.ROLE_ON_DESTROY, 'OnSneakActorDestroy', self)
	end
end

function NpcActor:OnSneakActorDestroy()
	Game.SneakSystem:RemoveTrapDecal(self.eid)
	Game.UniqEventSystemMgr:RemoveListener(self:uid(), EEventTypesV2.ROLE_ON_DESTROY, "OnSneakActorDestroy", self)
end
--endregion 

--region Interactive State
function NpcActor:set_interactiveState(entity, new, old)
    if new ~= old then
        if entity then
            Game.HUDInteractManager:OnInterStateChange(entity, new, old)
        else
            self:WarningFmt("[NpcActor:set_interactiveState] illegal Npc entity (instance)!")    
        end
    end
end
--endregion

--region runtimeSettings State
NPC_RUNTIME_SETTINGS = DefineClass("NPC_RUNTIME_SETTINGS")
function NPC_RUNTIME_SETTINGS:set_attr(entity, key, new, old)
    if key == "GossipGroupID" then
        Game.GossipSystem:OnGossipGroupIDUpdate(entity, new, old)
    elseif key == "TalkGroupID" then
        Game.NPCSystem:OnTalkGroupIDUpdate(entity, new, old)
    elseif key == "GossipGroupTriggerSwitch" then
        Game.GossipSystem:OnGossipGroupTriggerSwitchUpdate(entity, new, old)
    elseif key == "NpcHudPermeable" then
        Game.HeadInfoManager:SetWorldWidgetNoDepth(entity:uid(), new)
    end
end

function NpcActor:set_runtimeSettings(entity, new, old)
    if new ~= old then
        if entity then
            Game.HUDInteractManager:OnInterStateChange(entity, new, old)
        else
            self:WarningFmt("[NpcActor:set_runtimeSettings] illegal Npc entity (instance)!")    
        end
    end
end

function NpcActor:set_HeadTitle(entity, new, old)
	if new ~= old then
		if entity then
			Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.NPC_HEAD_TITLE_CHANGED)
		else
			self:WarningFmt("[NpcActor:set_HeadTitle] illegal Npc entity (instance)!")
		end
	end
end

--endregion

function NpcActor:OnChangeBossIdlePosture(newPosture)
    self:DebugFmt("NpcActor:OnChangeBossIdlePosture %s", newPosture)
    self:PlayBossIdlePostureChangePerformance(self.OldBossIdlePosture, newPosture)
    self:set_BossIdlePosture(self, newPosture, self.OldBossIdlePosture)
end

function NpcActor:PlayBossIdlePostureChangePerformance(OldIdlePosture, NewIdlePosture--[[, bNeedPlayPerformance]])
    if OldIdlePosture == NewIdlePosture then
        return
    end
    bNeedPlayPerformance = bNeedPlayPerformance or true
    self:OnBossIdlePostureChanged(OldIdlePosture, NewIdlePosture)
end

function NpcActor:OnBossIdlePostureChanged(InOldIdlePosture, InNewIdlePosture)
    if InOldIdlePosture == InNewIdlePosture then
        return
    end

    if self.SwitchBossIdlePostureID ~= nil then
        self:CancelAnimOverrideByAnimLib(self.SwitchBossIdlePostureID)
        self.SwitchBossIdlePostureID = nil
    end

    if InNewIdlePosture == 1 then
        self.SwitchBossIdlePostureID = self:SwitchIdleAnimByAnimLibAssetAsync(ViewAnimConst.IDLE_RANDOM)
    elseif InNewIdlePosture == 2 then
        self.SwitchBossIdlePostureID = self:SwitchIdleAnimByAnimLibAssetAsync(ViewAnimConst.IDLE_COMBAT)
    end
end

function NpcActor:set_BossIdlePosture(_, NewIdlePosture, OldIdlePosture)
    if NewIdlePosture == nil then
        self.OldBossIdlePosture = nil
        return
    end
    self:OnBossIdlePostureChanged(OldIdlePosture, NewIdlePosture)
    self.OldBossIdlePosture = NewIdlePosture
end

function NpcActor:OnMsgNpcActorAddTrap(entity, trapExcelId)
    self:Warning("OnMsgNpcActorAddTrap which is not recommended")
end

function NpcActor:OnMsgNpcActorRemoveTrap(entity, trapExcelId)
    self:Warning("OnMsgNpcActorRemoveTrap which is not recommended")
end

function NpcActor:OnMsgNpcActorAddMultiTrap(entity, trapExcelId)
    Game.SneakSystem:NpcEntityAddTrap(entity, trapExcelId)
end

function NpcActor:OnMsgNpcActorRemoveMultiTrap(entity, trapExcelId)
    Game.SneakSystem:NpcEntityRemoveTrap(entity, trapExcelId)
end

-- =========================================数据获取协议接口====================================
function NpcActor:GetEntityConfigData()
	local MonsterData = Game.TableData.GetMonsterDataRow(self.TemplateID)
	if MonsterData then
		-- 最新的Npc支持配置MonsterID, 所以只要在Monster表中有数据即以该数据为准
		return MonsterData
	end

    if self.NpcType == Enum.ENpcTypeData.Task then
        return Game.TableData.GetNpcInfoDataRow(self.TemplateID)
    end

    if self.NpcType == Enum.ENpcTypeData.Passerby  then
        return  Game.TableData.GetPasserbyNpcDataRow(self.TemplateID)
    end

    if self.NpcType == Enum.ENpcTypeData.Vehicle  then
        return Game.TableData.GetVehicleNpcDataRow(self.TemplateID)
    end
end

function NpcActor:CanUseWaterWaveFeature()
	if not self:IsViewBudgetTagDownGradingBudgetPermit(WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.LOCO_ENVIRONMENT_INTERACT) then
		return false
	end
    local facadeControlData = self:GetConfigFacadeControlData()
    if facadeControlData == nil  then
        return false
    end
    return facadeControlData.WaterWaveSwitch
end

function NpcActor:CanUseLocalWindFeature()
	if not self:IsViewBudgetTagDownGradingBudgetPermit(WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.LOCO_ENVIRONMENT_INTERACT) then
		return false
	end
    local facadeControlData = self:GetConfigFacadeControlData()
    if facadeControlData == nil  then
        return false
    end
    return facadeControlData.LocalWindSwitch
end

function NpcActor:set_MoodValue()
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_NPC_MOOD_CHANGE, self.MoodValue)
end

function NpcActor:NeedBornStickGround()
    if StringValid(self.ContinuousInteractInsID) then
        return false
    end
    local MemberData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InstanceID)
    if not MemberData then
        return true
    end
	if _G.NpcUseStickGroundConfig then
		return MemberData.bStickGround
	else
		return not MemberData.bForceBornPos
	end
end

function NpcActor:NeedServerFixPosition()
	-- Npc坐在凳子上
	if StringValid(self.ContinuousInteractInsID) then
		return false
	end
	local MemberData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InstanceID)
	if not MemberData then
		return true
	end
	-- 场编配置了出生位置的理论上都不会动，暂时使用这个标记。否则需要额外条件判断Npc移动了
	if _G.NpcUseStickGroundConfig then
		return MemberData.bStickGround
	else
		return not MemberData.bForceBornPos
	end
end

function NpcActor:GetDefaultLocoControlTemplateID()
	return Enum.LocoControlDefaultTemplate.NPC
end



function NpcActor:CalculateViewRoleImportance()
    if self.BossType == Enum.EBossType.BOSS then
        return WorldViewBudgetConst.FIRST_ROLE_IMPORTANCE
    end

    if self.BossType == Enum.EBossType.Elite then
        return WorldViewBudgetConst.SECOND_ROLE_IMPORTANCE
    end
    
    return WorldViewBudgetConst.THIRD_ROLE_IMPORTANCE
end


-- 获取马车夫对应传送点的InsID
---@return number | nil
function NpcActor:GetForTeleportInsID()
	if self.NpcType == Enum.ENpcTypeData.Task then
		if self.InstanceID then
			local NumericInsID = tonumber(self.InstanceID)
			if NumericInsID then
				local NpcToTeleportMap = Game.TableData.Get_NpcToTeleportMap()
				return NpcToTeleportMap[NumericInsID]
			end
		end
	end
	return nil
end

function NpcActor:AddAttachItemWithBorn()
	local NpcData = self:GetEntityConfigData()
	local FacadeData = Game.TableData.GetFacadeControlDataRow(NpcData.FacadeControlID)
	if FacadeData and FacadeData.AttachItemModelLibID and FacadeData.AttachItemModelLibID ~= "" then
		local AttachItem = self:AddAttachment_V2(WorldViewConst.ATTACH_ITEM_TYPE.None, FacadeData.AttachItemModelLibID)
		AttachItem:SetAttachToByModelCfgSocket(self:uid(), WorldViewConst.ATTACH_REASON.Profession)
		AttachItem:ApplyAttach()
	end
end

return NpcActor
