kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

local KismetMathLibrary = import("KismetMathLibrary")

---@class LSAE_JumpPoint
local LSAE_JumpPoint = DefineLocalEntity("LSAE_JumpPoint", LSceneActorEntityBase, {
    ViewControlTriggerComponent,
})

LSAE_JumpPoint:Register(EWActorType.JUMP_POINT)

function LSAE_JumpPoint:ctor()

end


function LSAE_JumpPoint:AfterEnterWorld()
    self:InitMainTriggerAsSphereAndBindEvent(self.SceneConf.MaxDetectDistance, self, "OnEnterInteractiveTrigger", "OnLeaveInteractiveTrigger")
    if self.SceneConf.bLineTracePlayer then
        self:EnableMainTriggerCheckLineTracePlayer(self.SceneConf.MinInteractDistance)
    elseif self.SceneConf.bLineTraceCamera then
        self:EnableMainTriggerCheckLineTraceCamera(self.SceneConf.MinInteractDistance)
    end
end

function LSAE_JumpPoint:BeforeExitWorld()
    if Game.me then
        Game.me:RemoveInteractorJumpPoint(self.InsID)
        Game.me:UnRegisterMainPlayerDistanceChanged(self:uid())
    end
end

function LSAE_JumpPoint.OnPlayerDistanceChanged(self, Distance)
    if Distance <= self.SceneConf.MinInteractDistance then
        Game.me:RemoveInteractorJumpPoint(self.InsID)
    else
        local bEnableInteract = Distance < self.SceneConf.MaxInteractDistance
        Game.me:AddOrRefreshInteractorJumpPoint(self.InsID, self:GetPosition(), Distance, bEnableInteract)
    end
end

function LSAE_JumpPoint:OnEnterInteractiveTrigger(OwnerUID, Pos, OtherActorUID)
    Game.me:RegisterMainPlayerDistanceChanged(self:uid(), self.SceneConf.MaxDetectDistance, self.OnPlayerDistanceChanged)
    local MeLocation = Game.me:GetPosition()
    local ActorLocation = self:GetPosition()
    local Distance = KismetMathLibrary.Vector_Distance(MeLocation, ActorLocation)
    self.OnPlayerDistanceChanged(self, Distance)
end

function LSAE_JumpPoint:OnLeaveInteractiveTrigger(OwnerUID, OtherActorUID)
	if Game.me then
		Game.me:UnRegisterMainPlayerDistanceChanged(self:uid())
		Game.me:RemoveInteractorJumpPoint(self.InsID)
	end
end

return LSAE_JumpPoint