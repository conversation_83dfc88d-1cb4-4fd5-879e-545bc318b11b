kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_HeightFogModifier
local LSAE_HeightFogModifier = DefineLocalEntity("LSAE_HeightFogModifier", LSceneActorEntityBase,
{
    ViewControlTriggerComponent,
    SceneActorBehaviorComponent,
}
)

---@type FColor
local CachedFColor = FColor()

LSAE_HeightFogModifier:Register(EWActorType.HEIGHT_FOG_MODIFIER)

function LSAE_HeightFogModifier:ctor()
end

function LSAE_HeightFogModifier:SetOverrideBehaviorParams()
    self.InteractorRadius = self.SceneConf.Radius 
end

function LSAE_HeightFogModifier:AfterEnterWorld()
    self:InitMainTriggerAsSphereAndBindEvent(self.InteractorRadius, self, "SetHeightFogParametersToConfig", "RestoreHeightFogParameters")
    self.InitialFogParameters = self:GetCurrentParameters()
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InsID)
    self.ConfigFogParameters = {
        FogDensity = SceneActorData.FogDensity,
        FogHeightFalloff = SceneActorData.FogHeightFalloff,
        SecondFogDensity = SceneActorData.SecondFogDensity,
        SecondFogHeightFalloff = SceneActorData.SecondFogHeightFalloff,
        SecondFogHeightOffset = SceneActorData.SecondFogHeightOffset,
        FogInscatteringColor = FLinearColor(
            SceneActorData.FogInscatteringColor.R, SceneActorData.FogInscatteringColor.G,
            SceneActorData.FogInscatteringColor.B, SceneActorData.FogInscatteringColor.A
        ),
        StartDistance = SceneActorData.StartDistance,
        VolumetricFogDistance = SceneActorData.VolumetricFogDistance,
        VolumetricFogEmissive = FLinearColor(
            SceneActorData.VolumetricFogEmissive.R, SceneActorData.VolumetricFogEmissive.G,
            SceneActorData.VolumetricFogEmissive.B, SceneActorData.VolumetricFogEmissive.A
        ),
        VolumetricFogAlbedo = FColor(
            SceneActorData.VolumetricFogAlbedo.R, SceneActorData.VolumetricFogAlbedo.G,
            SceneActorData.VolumetricFogAlbedo.B, SceneActorData.VolumetricFogAlbedo.A
        ),
    }
    self.CurrentPivot = 0
end

function LSAE_HeightFogModifier:GetCurrentParameters()
    local Component = self:GetHeightFogComponent()
    return {
        FogDensity = Component.FogDensity,
        FogHeightFalloff = Component.FogHeightFalloff,
        SecondFogDensity = Component.SecondFogData.FogDensity,
        SecondFogHeightFalloff = Component.SecondFogData.FogHeightFalloff,
        SecondFogHeightOffset = Component.SecondFogData.FogHeightOffset,
        FogInscatteringColor = FLinearColor(
            Component.FogInscatteringLuminance.R, Component.FogInscatteringLuminance.G,
            Component.FogInscatteringLuminance.B, Component.FogInscatteringLuminance.A
        ),
        StartDistance = Component.StartDistance,
        VolumetricFogDistance = Component.VolumetricFogDistance,
        VolumetricFogEmissive = FLinearColor(
            Component.VolumetricFogEmissive.R, Component.VolumetricFogEmissive.G,
            Component.VolumetricFogEmissive.B, Component.VolumetricFogEmissive.A
        ),
        VolumetricFogAlbedo = FColor(
            Component.VolumetricFogAlbedo.R, Component.VolumetricFogAlbedo.G,
            Component.VolumetricFogAlbedo.B, Component.VolumetricFogAlbedo.A
        ),
    }
end

function LSAE_HeightFogModifier:ScheduleChangeHeightFogParameters(Parameters)
    Game.TimerManager:StopTimerAndKill(self.TimerHandle)
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InsID)
    self.CurrentPivot = SceneActorData.BlendTime
    local CurrentParameters = self:GetCurrentParameters()
    self.TimerHandle = Game.TimerManager:TickTimer(
        function(DeltaTime)
            self.CurrentPivot = self.CurrentPivot - DeltaTime / 1000
            self.CurrentPivot = math.max(self.CurrentPivot, 0)
            self:ModifyHeightFog(CurrentParameters, Parameters, self.CurrentPivot / SceneActorData.BlendTime)
            if self.CurrentPivot <= 0 then
                Game.TimerManager:StopTimerAndKill(self.TimerHandle)
                self.TimerHandle = nil
            end
            self:DebugFmt("@lizhemian: CurrentPivot %s %s", self.CurrentPivot, DeltaTime)
        end
    , -1)
end

function LSAE_HeightFogModifier:RestoreHeightFogParameters()
    self:ScheduleChangeHeightFogParameters(self.InitialFogParameters)
end

function LSAE_HeightFogModifier:SetHeightFogParametersToConfig()
    self:ScheduleChangeHeightFogParameters(self.ConfigFogParameters)
end

function LSAE_HeightFogModifier:GetHeightFogComponent()
    local FogVolume = slua.Array(import("EPropertyClass").Object, import("ExponentialHeightFog"))
    import("GameplayStatics").GetAllActorsOfClass(GetContextObject(), import("ExponentialHeightFog"), FogVolume)
    for Index, FogActor in pairs(FogVolume) do
        ---@type ExponentialHeightFogComponent
        local Component = FogActor.Component
        return Component
    end
end

function LSAE_HeightFogModifier:InterpolateValue(Source, Target, InterpValue)
    return Source * (InterpValue) + Target * (1 - InterpValue)
end


function LSAE_HeightFogModifier:ModifyHeightFog(SourceParameter, TargetParameter, InterpValue)
    local Component = self:GetHeightFogComponent()
    Component:SetFogDensity(
        self:InterpolateValue(SourceParameter.FogDensity, TargetParameter.FogDensity, InterpValue)
    )
    Component:SetFogHeightFalloff(
        self:InterpolateValue(SourceParameter.FogHeightFalloff, TargetParameter.FogHeightFalloff, InterpValue)
    )
    Component:SetSecondFogDensity(
        self:InterpolateValue(SourceParameter.SecondFogDensity, TargetParameter.SecondFogDensity, InterpValue)
    )
    Component:SetSecondFogHeightFalloff(
        self:InterpolateValue(SourceParameter.SecondFogHeightFalloff, TargetParameter.SecondFogHeightFalloff, InterpValue)
    )
    Component:SetSecondFogHeightOffset(
        self:InterpolateValue(SourceParameter.SecondFogHeightOffset, TargetParameter.SecondFogHeightOffset, InterpValue)
    )
    Component:SetFogInscatteringColor(
        self:InterpolateValue(SourceParameter.FogInscatteringColor, TargetParameter.FogInscatteringColor, InterpValue)    
    )
    Component:SetStartDistance(
        self:InterpolateValue(SourceParameter.StartDistance, TargetParameter.StartDistance, InterpValue)        
    )
    Component:SetVolumetricFogDistance(
        self:InterpolateValue(SourceParameter.VolumetricFogDistance, TargetParameter.VolumetricFogDistance, InterpValue)            
    )
    Component:SetVolumetricFogEmissive(
        self:InterpolateValue(SourceParameter.VolumetricFogEmissive, TargetParameter.VolumetricFogEmissive, InterpValue)
    )
    CachedFColor.R = math.floor(SourceParameter.VolumetricFogAlbedo.R * InterpValue + TargetParameter.VolumetricFogAlbedo.R * (1 - InterpValue))
    CachedFColor.G = math.floor(SourceParameter.VolumetricFogAlbedo.G * InterpValue + TargetParameter.VolumetricFogAlbedo.G * (1 - InterpValue))
    CachedFColor.B = math.floor(SourceParameter.VolumetricFogAlbedo.B * InterpValue + TargetParameter.VolumetricFogAlbedo.B * (1 - InterpValue))
    CachedFColor.A = math.floor(SourceParameter.VolumetricFogAlbedo.A * InterpValue + TargetParameter.VolumetricFogAlbedo.A * (1 - InterpValue))
    Component:SetVolumetricFogAlbedo(CachedFColor) 
end

function LSAE_HeightFogModifier:BeforeExitWorld()
    if self.TimerHandle ~= nil then
        Game.TimerManager:StopTimerAndKill(self.TimerHandle)
        self.TimerHandle = nil
    end
end

return LSAE_HeightFogModifier