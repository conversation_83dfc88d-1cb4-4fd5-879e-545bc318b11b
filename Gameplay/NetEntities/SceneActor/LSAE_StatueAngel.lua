kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_StatueAngel : LSceneActorEntityBase
---@type LSAE_StatueAngel | ViewControlTriggerComponent  | SceneActorBehaviorComponent

local LSAE_StatueAngel = DefineLocalEntity("LSAE_StatueAngel", LSceneActorEntityBase,
	{ SceneActorBehaviorComponent, ViewControlTriggerComponent})

LSAE_StatueAngel:Register(EWActorType.STATUE_ANGEL)

function LSAE_StatueAngel:ctor()
	self.ExcelData = nil
	self.Radius = nil
	self.UITemplateID = nil
	self:InitFromExcel()
end

function LSAE_StatueAngel:InitFromExcel()
	local GoddessData = Game.TableData.GetGoddessDataRow(self.SceneConf.TemplateID)
	self.ExcelData = GoddessData
	self.Radius = GoddessData.InteractorRadius
	self.UITemplateID = GoddessData.InteractorTextID
end

function LSAE_StatueAngel:AfterEnterWorld()
	self:DoAsyncLoadAsset(self.ExcelData.NormalMesh, "OnMeshLoaded")
	if self.Radius and self.Radius > 0 then
		self:InitMainTriggerAsSphereAndBindEvent(self.Radius, self, "OnEnterInteractiveTrigger", "OnLeaveInteractiveTrigger")
	end
end

function LSAE_StatueAngel:BeforeExitWorld()

end

function LSAE_StatueAngel:OnEnterInteractiveTrigger()
	local GoddessID = self.SceneConf.TemplateID
	if GoddessID == 0 then
		self:BuildHUDInteractTask(self.UITemplateID, self, self.OnClickTaskGoddessFirst)
	--elseif GoddessID == 1 then
	--	self:BuildHUDInteractTask(self.UITemplateID, self, self.OnClickTaskGoddess)
	end
end

function LSAE_StatueAngel:OnLeaveInteractiveTrigger()
	Game.HUDInteractManager.DestroyInteractorTask(self:uid())
end

function LSAE_StatueAngel:OnClickTaskGoddessFirst()
	--面板计时触发
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.BALL_GAME_START)
end

--配置Mesh
function LSAE_StatueAngel:OnMeshLoaded(LoadID, AssetID)
	if AssetID == 0 then
		return
	end

	if not self.bInWorld then
		return
	end

	local MeshCompID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "GoddessMesh")
	self.CppEntity:KAPI_StaticMeshID_SetStaticMesh(MeshCompID, AssetID);
end