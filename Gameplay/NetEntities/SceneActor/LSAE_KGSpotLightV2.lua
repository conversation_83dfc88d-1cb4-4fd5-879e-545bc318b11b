---
--- <NAME_EMAIL>
--- DateTime: 2024/11/13 10:50
---


local EComponentMobility = import("EComponentMobility")
local ELightUnits = import("ELightUnits")
local SpotLightComponent = import("SpotLightComponent")
local SpotLightControlComponent = import("SpotLightControlComponent")

kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

local __Empty_Transform = FTransform() -- luacheck: ignore

---@class LSAE_KGSpotLight : LSceneActorEntityBase
local LSAE_KGSpotLightV2 = DefineLocalEntity("LSAE_KGSpotLightV2", LSceneActorEntityBase)

LSAE_KGSpotLightV2:Register(EWActorType.KG_SPOT_LIGHT_V2)

LSAE_KGSpotLightV2.Param_Name_Prefix = "LightParam_"

LSAE_KGSpotLightV2.Spawn_Transform = FTransform()

function LSAE_KGSpotLightV2:ctor()
    self.ActorBPClass = import("SpotLight")
	self.BindEventInsID = nil
	self.spotLightCompID = 0
	self.spotLightControlCompID = 0
end

function LSAE_KGSpotLightV2:UnInit()
    self.spotLightCompID = 0
    self.spotLightControlCompID = 0
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function LSAE_KGSpotLightV2:AfterEnterWorld()
    -- todo@shijingzhe: 没有配置Actor的LSAE会在loading期间就调用AfterEnterWorld,导致部分数据获取异常
    if Game.me.bInWorld then
        self:OnMainPlayerLoaded()
    else
		Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED, "OnMainPlayerLoaded", self)
    end
end

function LSAE_KGSpotLightV2:OnMainPlayerLoaded()
    local spotLightCompID = self.CppEntity:KAPI_Actor_GetComponentByClass(SpotLightComponent)
    if not IsValidID(spotLightCompID) then
        return
    end
    self.CppEntity:KAPI_SceneID_SetMobility(spotLightCompID, EComponentMobility.Movable)
    self.CppEntity:KAPI_LocalLightID_SetIntensityUnits(spotLightCompID, ELightUnits.Candelas)

	if self.SceneConf.Transform then
		self.CppEntity:KAPI_SetRotation(self.SceneConf.Transform.Rotator)
	end
	
    -- local spotLightControlCompID = self.CppEntity:KAPI_Actor_GetComponentByClass(SpotLightControlComponent)
    local spotLightControlCompID = self.CppEntity:KAPI_Actor_AddComponentByClassID(Game.ObjectActorManager:GetIDByClass(SpotLightControlComponent))

    if not IsValidID(spotLightControlCompID) then
        return
    end
    self.CppEntity:KAPI_SpotLightControlID_SetSpotLightComp(spotLightControlCompID, spotLightCompID)

    -- 记录ID
    self.spotLightCompID = spotLightCompID
    self.spotLightControlCompID = spotLightControlCompID

    -- 颜色暂不支持切换
    self.CppEntity:KAPI_LightID_SetLightFColor(spotLightCompID, self.SceneConf.LightColor)

    -- 出生,无需Blend
    self:setLightParam()
end

function LSAE_KGSpotLightV2:BeforeExitWorld()
    -- 停止插值任务
    local spotLightControlCompID = self.spotLightControlCompID

    if IsValidID(spotLightControlCompID) then
        self.CppEntity:KAPI_SpotLightControlID_StopBlend(spotLightControlCompID)
        self.CppEntity:KAPI_SpotLightControlID_StopFollowTarget(spotLightControlCompID)
    end

    self.CppEntity:KAPI_Actor_K2_DestroyActor()

    Game.EventSystem:RemoveObjListeners(self)
	self.BindEventInsID = nil
end

function LSAE_KGSpotLightV2:OnSceneActorStateChanged()
    -- 状态变化触发,需要使用Blend
    self:setLightParam(true)
end

---@private
---@param bWithBlend boolean 是否使用Blend
function LSAE_KGSpotLightV2:setLightParam(bWithBlend)
    local spotLightCompID = self.spotLightCompID
    if not IsValidID(spotLightCompID) then
        return
    end

    local spotLightControlCompID = self.spotLightControlCompID
    if not IsValidID(spotLightControlCompID) then
        return
    end

    -- 根据状态,获取对应的参数
    local state = self:GetSceneActorState()
    local param = self.SceneConf[self.Param_Name_Prefix .. tostring(state)]
    if not param then
        self:WarningFmt("[setLightParam] target light param no exist state:%s", state)
        return
    end
	
    if not param.bEnable then
        self.CppEntity:KAPI_SceneID_SetVisibility(spotLightCompID, false, false)
        return
    end

    self.CppEntity:KAPI_SceneID_SetVisibility(spotLightCompID, true, false)

    if not bWithBlend then
        self.CppEntity:KAPI_LightID_SetIntensity(spotLightCompID, param.Intensity)
        self.CppEntity:KAPI_LocalLightID_SetAttenuationRadius(spotLightCompID, param.AttenuationRadius)
        self.CppEntity:KAPI_SpotLightID_SetInnerConeAngle(spotLightCompID, param.InnerConeAngle)
        self.CppEntity:KAPI_SpotLightID_SetOuterConeAngle(spotLightCompID, param.OuterConeAngle)

    else
        self.CppEntity:KAPI_SpotLightControlID_StartBlend(spotLightControlCompID, param.BlendTime, param.Intensity, param.AttenuationRadius, param.InnerConeAngle, param.OuterConeAngle)
    end

    -- 聚光灯跟随相关
    if param.bFollowTarget then
		if self.BindEventInsID then
			Game.UniqEventSystemMgr:RemoveListener(self.BindEventInsID, EEventTypesV2.NPC_ENTER_WORLD_WITH_INSID, "OnTargetEnterWorld", self)
			self.BindEventInsID = nil
		end
        self.BindEventInsID = param.TargetInsID
		Game.UniqEventSystemMgr:AddListener(self.BindEventInsID, EEventTypesV2.NPC_ENTER_WORLD_WITH_INSID, "OnTargetEnterWorld", self)
        local npcEntityUID = Game.WorldManager:GetNpcByInstance(param.TargetInsID)
        self:OnTargetEnterWorld(npcEntityUID)
    else
        if self.BindEventInsID then
			Game.UniqEventSystemMgr:RemoveListener(self.BindEventInsID, EEventTypesV2.NPC_ENTER_WORLD_WITH_INSID, "OnTargetEnterWorld", self)
            self.BindEventInsID = nil
        end

        self.CppEntity:KAPI_SpotLightControlID_StopFollowTarget(spotLightControlCompID)
    end
end

---@private
---@param npcEntityUID number
function LSAE_KGSpotLightV2:OnTargetEnterWorld(npcEntityUID)
    local spotLightControlCompID = self.spotLightControlCompID
    if not IsValidID(spotLightControlCompID) then
        return
    end

    local npcEntity = Game.EntityManager:GetEntityByIntID(npcEntityUID)
    if not npcEntity then
        self:WarningFmt("[OnTargetEnterWorld] target npc entity %s not exist in %s", npcEntityUID, self.InsID)
        return
    end

    local npcActorID = npcEntity.CharacterID
    if not IsValidID(npcActorID) then
        self:WarningFmt("[OnTargetEnterWorld] target npc actor %s not exist in %s", npcEntityUID, self.InsID)
        return
    end

    self.CppEntity:KAPI_SpotLightControlID_StartFollowTarget(spotLightControlCompID, npcActorID)
end

return LSAE_KGSpotLightV2
