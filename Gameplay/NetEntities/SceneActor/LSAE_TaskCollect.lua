kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")
kg_require("Gameplay.NetEntities.Comps.SceneActorBehaviorComponent")
local EAnimationMode = import("EAnimationMode")
local SkeletalMeshComponent = import("SkeletalMeshComponent")
local StaticMeshComponent = import("StaticMeshComponent")
local NiagaraComponent = import("NiagaraComponent")
local NewHeadInfoConst = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoConst")
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")
local LOCAL_PERFORMANCE_ANIM_TYPE = ViewAnimConst.LOCAL_PERFORMANCE_ANIM_TYPE

---@class LSAE_TaskCollect
local LSAE_TaskCollect = DefineLocalEntity("LSAE_TaskCollect", LSceneActorEntityBase, { 
	SceneActorBehaviorComponent,
    ViewControlTriggerComponent,
	SimpleViewControlAnimComponent,
})

LSAE_TaskCollect:Register(EWActorType.COLLECTION)

function LSAE_TaskCollect:ctor()
    self.CachedCollectable = nil 
    self.ShowGlowRule = Enum.ETaskCollectShowRule.None
    self.ShowModelRule = Enum.ETaskCollectShowRule.None
	self.CachedTaskHidden = nil
	self.CollectableControlByCondition = false
	self.IsNeedNpcHideRule = true
end

function LSAE_TaskCollect:Init()
    local DataRow = Game.TableData.GetSceneActorTaskCollectDataRow(self.SceneConf.TemplateID)
    self.ExcelData = DataRow
	if DataRow == nil then
		local SC = self.SceneConf
		self:ErrorFmt("task collect with wrong template id TemplateID:%s, InsID:%s, Layer:%s, Owner:%s", SC.TemplateID, SC.ID, SC.LayerName, SC.Owner)
		return
	end
    -- 闪光显示规则
    self.ShowGlowRule = DataRow.ShowParticleRule
    -- 模型显示规则
    self.ShowModelRule = DataRow.ShowModelRule
	self.ShowNameRule = DataRow.ShowNameRule
end

function LSAE_TaskCollect:UnInit()
	self.ExcelData = nil
end

function LSAE_TaskCollect:AfterEnterWorld()
    self:InitAppearance()
    self:InitTriggerCustomScope()
	self:RefreshHiddenByQuest()
    Game.EventSystem:AddListener(_G.EEventTypes.CINEMATIC_ON_START, self, self.OnCinematicStart)
    Game.EventSystem:AddListener(_G.EEventTypes.CINEMATIC_ON_END, self, self.OnCinematicEnd)

    self:InitTaskConditionAndCollectable()
	
	if Game.DialogueManager:IsPlayingDialogue() then
		self:OnCinematicStart(Enum.CinematicType.Dialogue)
	end
end

function LSAE_TaskCollect:BeforeExitWorld()
    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.CINEMATIC_ON_START, self, self.OnCinematicStart)
    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.CINEMATIC_ON_END, self, self.OnCinematicEnd)
end

function LSAE_TaskCollect:InitTriggerCustomScope()
	local FaceAngle = 360
	if self.ExcelData.FaceAngle and self.ExcelData.FaceAngle > 0 then
		FaceAngle = self.ExcelData.FaceAngle
	end
	if self.ExcelData.InnerRadius > 0 then
		self:EnableMainTriggerAsRing(self.ExcelData.InnerRadius, self.InteractorRadius)
	end
	if FaceAngle < 360 then
		self:EnableMainTriggerAsCircularSector(0, FaceAngle/2, 0)
    end
end

function LSAE_TaskCollect:IsCollectable(bUpdate)
	if not bUpdate then
		return self.CachedCollectable
	end
    -- if self.ExcelData.bTaskControl then
    --     return Game.me.CollectionActiveMap ~= nil and Game.me.CollectionActiveMap[self.InsID] ~= nil
    -- end
    local SceneActorState = self:GetSceneActorState()
    return SceneActorState == SceneActorFixedStateEnum.SA_ACTIVE
end

--@override SpiritualVisionSceneActorComponent
function LSAE_TaskCollect:ReadSpiritualConf()
    local SpiritualVisionShowRule = self.ExcelData.SpiritualVisionShowRule
    if SpiritualVisionShowRule == Enum.ESpiritualVisionShowRule.Always then
    elseif SpiritualVisionShowRule == Enum.ESpiritualVisionShowRule.OnlyInVision then
        self.bDefaultOnlyInSpiritualVision = true
    elseif SpiritualVisionShowRule == Enum.ESpiritualVisionShowRule.UseVision then
        self.bDefaultUseSpiritualVision = true
    end
    
    self.DefaultSpiritualShaderTemplate = self.ExcelData.SpiritualVisionShader or 0
    self.DefaultSpiritualIconType = self.ExcelData.SpiritualVisionIconType or 0

end

function LSAE_TaskCollect:GetHeadInfoOffset()
	if self.ExcelData ~= nil then
		return self.ExcelData.HeadInfoOffset
	end
	return 0
end

--@override
function LSAE_TaskCollect:PreTriggerCheck()
    return self:IsCollectable()
end

function LSAE_TaskCollect:SetOverrideBehaviorParams()
    self.InteractorRadius = self.ExcelData and self.ExcelData.InteractRadius or 300
    self.InteractorPeriod = self.ExcelData and self.ExcelData.InteractPeriod or 0
	if self.ExcelData then
		self:OverrideTriggerUITemplateID(SceneActorFixedStateEnum.SA_ACTIVE, self.ExcelData.UITemplateID)	
	end
end

function LSAE_TaskCollect:PlayInitiatorAnimFeature(Initiator, FeatureID, Stage)
	if not StringValid(Stage) then
		Stage = nil
	end
	Initiator:PlayAnimLibMontage(FeatureID, Stage, nil, nil, nil, true)
end

--@override SceneActorBehaviorComponent
function LSAE_TaskCollect:OnRetInitiateInteractSuccess()
    -- 通知任务开始
    Game.me.remote.ReqCollectBegin(self.InsID)
    
    -- 玩家转向
	if self.ExcelData.bTurn then
		if self.ExcelData.TurnAngle then
			local TargetRotator = FRotator(0, self.ExcelData.TurnAngle, 0)
			Game.me:EnterLookAtToRot(TargetRotator)
		else
			Game.me:EnterLookAtToLoc(self:GetPosition())
		end
	end

	if StringValid(self.ExcelData.InteractorInteractAnim) then
		self:PlayAnimationByMontageAsyncLoad("DefaultSlot", self.ExcelData.InteractorInteractAnim, false, 0, 0)
	end
end

function LSAE_TaskCollect:CallOnInterruptBehavior(bRestart, InitiatorID)
	if InitiatorID == Game.me.eid then
		-- 服务端会触发打断Action
	end
end

function LSAE_TaskCollect:GetInteractStartAnimFeature()
	local StartAnim = self.SceneConf.StartAnim
	if StartAnim and StartAnim.AssetID and StartAnim.AssetID ~= "" then
		return { StartAnim.AssetID, StartAnim.StateName }
	elseif self.ExcelData.StartAnimFeature and #self.ExcelData.StartAnimFeature > 0 then
		return self.ExcelData.StartAnimFeature
	end
end

function LSAE_TaskCollect:GetInteractEndAnimFeature()
	local EndAnim = self.SceneConf.EndAnim
	if EndAnim and EndAnim.AssetID and EndAnim.AssetID ~= "" then
		return { EndAnim.AssetID, EndAnim.StateName }
	elseif self.ExcelData.EndAnimFeature and #self.ExcelData.EndAnimFeature > 0 then
		return self.ExcelData.EndAnimFeature
	end
end

function LSAE_TaskCollect:CallOnSuccessBehavior(bRestart, InitiatorID)
	if not bRestart then
		if Game.me.eid == InitiatorID then
			Game.me:SwitchInteractState(Enum.EInteractiveInnerState.None)
            if self.SceneConf.EndAnim and StringValid(self.SceneConf.EndAnim.AssetID) then
                --self:PlayInitiatorAnimFeature(Game.me, self.SceneConf.EndAnim.AssetID, self.SceneConf.EndAnim.StateName)
                Game.me:TryStartPerformanceAnimFeature(
                    LOCAL_PERFORMANCE_ANIM_TYPE.InteractEnd, ViewAnimConst.EAnimPlayReqTag.LocalPerformance, self.SceneConf.EndAnim.AssetID, self.SceneConf.EndAnim.StateName)
            elseif self.ExcelData.EndAnimFeature and #self.ExcelData.EndAnimFeature > 0 then
                --self:PlayInitiatorAnimFeature(Game.me, self.ExcelData.EndAnimFeature[1], self.ExcelData.EndAnimFeature[2])
                Game.me:TryStartPerformanceAnimFeature(
                    LOCAL_PERFORMANCE_ANIM_TYPE.InteractEnd, ViewAnimConst.EAnimPlayReqTag.LocalPerformance, self.ExcelData.EndAnimFeature[1], self.ExcelData.EndAnimFeature[2])
            end
			-- 通知任务结束
			Game.me.remote.ReqCollectEnd(self.InsID)
		end

		if StringValid(self.ExcelData.InteractorSuccessAnim) then
			self:PlayAnimationByMontageAsyncLoad("DefaultSlot", self.ExcelData.InteractorSuccessAnim, false, 0, 0)
		end
	end
end

function LSAE_TaskCollect:OnSceneActorSubStateChanged(New, Old, ExParam, InitiatorID)
    -- 子状态代表剩余交互次数
    if New >= 0 then
		self:DebugFmt("OnSceneActorSubStateChanged %s, %s", New, Old)
    end
end

function LSAE_TaskCollect:InitAppearance()
	local MeshPath = self.ExcelData.Mesh
	if StringValid(self.ExcelData.SkeletalMesh) then
		MeshPath = self.ExcelData.SkeletalMesh
	end
	
	local SMComp = self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
	local SKComp = self.CppEntity:KAPI_Actor_GetComponentByClass(SkeletalMeshComponent)
    if StringValid(MeshPath) then
        self:DoAsyncLoadAsset(MeshPath, "OnMainMeshLoaded")
		local MeshScale = self.ExcelData.MeshScale
		if MeshScale and MeshScale > 0 then
			if SMComp ~= 0 then
				self.CppEntity:KAPI_SceneID_SetWorldScale3D(SMComp, FVector(MeshScale, MeshScale, MeshScale))
			end
			if SKComp ~= 0 then
				self.CppEntity:KAPI_SceneID_SetWorldScale3D(SMComp, FVector(MeshScale, MeshScale, MeshScale))
			end
		end
		local MeshOffsetZ = self.ExcelData.MeshOffsetZ
		if MeshOffsetZ and MeshOffsetZ ~= 0 then
			if SMComp ~= 0 then
				local RelLoc = self.CppEntity:KAPI_SceneID_GetRelativeLocation(SMComp)
				self.CppEntity:KAPI_SceneID_SetRelativeLocation(SMComp, RelLoc.X, RelLoc.Y, MeshOffsetZ)
			end
			if SKComp ~= 0 then
				local RelLoc = self.CppEntity:KAPI_SceneID_GetRelativeLocation(SKComp)
				self.CppEntity:KAPI_SceneID_SetRelativeLocation(SKComp, RelLoc.X, RelLoc.Y, MeshOffsetZ)
			end
		end
	end
    if StringValid(self.ExcelData.NiagaraAsset) then
        self:DoAsyncLoadAsset(self.ExcelData.NiagaraAsset, "OnBornNiagaraLoaded")
    end
	if self.ExcelData.GlowAssetScale > 0.0 then
		local Comps = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "GLOW")
		for _, CompID in ipairs(Comps:ToTable()) do
			local Scale = self.ExcelData.GlowAssetScale
			if Scale and Scale > 0 then
				self.CppEntity:KAPI_SceneID_SetWorldScale3D(CompID, FVector(Scale, Scale, Scale))
			end
		end
	end
	if StringValid(self.ExcelData.GlowAsset) then
		self:DoAsyncLoadAsset(self.ExcelData.GlowAsset, "OnGlowAssetLoaded")
	end
end

function LSAE_TaskCollect:RefreshCollectable(bInCollectable)
    local bCollectable = bInCollectable
	if bCollectable == nil then
		bCollectable = self:IsCollectable(true)
	end
    if self.CachedCollectable == nil or self.CachedCollectable ~= bCollectable then
        self.CachedCollectable = bCollectable
        self:OnCollectableChanged(bCollectable)
    end
end

function LSAE_TaskCollect:OnCollectableChanged(bCollectable)
    if bCollectable then
        self:DoInitBehavior()
    else
        self:DoUnInitBehavior()
    end
    self:RefreshGlowParticleByCollectable(bCollectable)
    self:RefreshModelVisibleByCollectable(bCollectable)
    Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.PICK_OBJECT_COLLETABLE_CHANGED, bCollectable)
	Game.NewHeadInfoSystem:UpdateHeadInfoNode(self:uid(), NewHeadInfoConst.EHeadInfoNodeType.Title)
end

function LSAE_TaskCollect:RefreshGlowParticleByCollectable(bCollectable)
    if self.ShowGlowRule == Enum.ETaskCollectShowRule.None then
        return
    end
    
    local ShouldShowFlag = bCollectable
    if self.ShowGlowRule == Enum.ETaskCollectShowRule.AlwaysShow then
        ShouldShowFlag = true
    elseif self.ShowGlowRule == Enum.ETaskCollectShowRule.AlwaysHide then
        ShouldShowFlag = false
    elseif self.ShowGlowRule == Enum.ETaskCollectShowRule.DependsCollectable then

    end
    
    local Comps = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "GLOW")
    for _, compID in pairs(Comps) do
        self.CppEntity:KAPI_NiagaraID_ReinitializeSystem(compID)
        self.CppEntity:KAPI_Component_SetActive(compID, ShouldShowFlag, true)
        self.CppEntity:KAPI_SceneID_SetVisibility(compID, ShouldShowFlag, true)
    end
end

function LSAE_TaskCollect:RefreshModelVisibleByCollectable(bCollectable)
    if self.ShowModelRule == Enum.ETaskCollectShowRule.None or
		self.ShowModelRule == Enum.ETaskCollectShowRule.AlwaysShow then
        return
    end

    local ShouldShowFlag = bCollectable
    if self.ShowModelRule == Enum.ETaskCollectShowRule.AlwaysHide then
        ShouldShowFlag = false
    elseif self.ShowModelRule == Enum.ETaskCollectShowRule.DependsCollectable then

    end
    
    if ShouldShowFlag then
        self:SetActorVisible(Enum.EInVisibleReasons.TaskCollectable)
    else
        self:SetActorInVisible(Enum.EInVisibleReasons.TaskCollectable)
    end
end

function LSAE_TaskCollect:OnSceneActorStateChanged(NewState, OldState)
	if self.bInWorld then
		self:RefreshCollectable()
	end
end

function LSAE_TaskCollect:OnCollectionActiveMapChanged(bActive)
    --self:RefreshCollectable()
end

function LSAE_TaskCollect:RefreshHiddenByQuest()
	local bHidden = Game.QuestSystem:IsPickObjectHidden(self.InsID)
	if self.CachedTaskHidden == nil or self.CachedTaskHidden ~= bHidden then
		self.CachedTaskHidden = bHidden
		if bHidden then
			self:SetInvisibleByQuestControl(true)
		else
			self:SetInvisibleByQuestControl(false)
		end
	end
end

function LSAE_TaskCollect:OnCinematicStart(Type, AssetID)
	if Type == Enum.CinematicType.Dialogue then
		local Comps = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "GLOW")
		for _, compID in ipairs(Comps:ToTable()) do
			self.CppEntity:KAPI_Component_SetActive(compID, false, true)
			self.CppEntity:KAPI_SceneID_SetVisibility(compID, false, true)
		end
	end
end

function LSAE_TaskCollect:OnCinematicEnd(Type, AssetID)
	if  Type == Enum.CinematicType.Dialogue then
		self:RefreshGlowParticleByCollectable(self.CachedCollectable)
	end
end

function LSAE_TaskCollect:OnMainMeshLoaded(LoadId, AssetID)
    if AssetID ~= 0 then
		local MeshComp
        if LuaScriptAPI.IsATypeOf(AssetID, "StaticMesh") then
			MeshComp = self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
			if MeshComp ~= 0 then
				self.CppEntity:KAPI_StaticMeshID_SetStaticMesh(MeshComp, AssetID)
			end
        elseif LuaScriptAPI.IsATypeOf(AssetID, "SkeletalMesh") then
			MeshComp = self.CppEntity:KAPI_Actor_GetComponentByClass(SkeletalMeshComponent)
			if MeshComp ~= 0 then
				self:SetSkeletalMeshAssetAndAnimationMode(AssetID, EAnimationMode.AnimationSingleNode)
			end
			if StringValid(self.ExcelData.InteractorBornAnim) then
				self:PlayAnimationByMontageAsyncLoad("DefaultSlot", self.ExcelData.InteractorBornAnim, true, 0, 0)
			end
		else
			self:WarningFmt("Mesh Type Error, InsID:%s, TemplateID:%s", self.InsID, self.TemplateID)
		end
    end
end

function LSAE_TaskCollect:OnBornNiagaraLoaded(LoadId, AssetID)
    if AssetID ~= 0 then
        local NiagaraComp = self.CppEntity:KAPI_Actor_GetComponentByTag(NiagaraComponent, "BORN")
		if NiagaraComp ~= 0 then
			self.CppEntity:KAPI_NiagaraID_SetAsset(NiagaraComp, AssetID, true)
			self.CppEntity:KAPI_Component_SetActive(NiagaraComp, true, true)
			self.CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(NiagaraComp, true)
			local Scale = self.ExcelData.NiagaraAssetScale
			if Scale and Scale > 0 then
				self.CppEntity:KAPI_SceneID_SetWorldScale3D(NiagaraComp, FVector(Scale, Scale, Scale))
			end
		end
    end
end

function LSAE_TaskCollect:OnGlowAssetLoaded(LoadId, AssetID)
	if AssetID ~= 0 then
		local NiagaraComp = self.CppEntity:KAPI_Actor_GetComponentByTag(NiagaraComponent, "GLOW")
		if NiagaraComp ~= 0 then
			self.CppEntity:KAPI_NiagaraID_SetAsset(NiagaraComp, AssetID, true)
			self.CppEntity:KAPI_Component_SetActive(NiagaraComp, true, true)
			self.CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(NiagaraComp, true)
		end
	end
end

function LSAE_TaskCollect:InitTaskConditionAndCollectable()
	-- 如果显示规则是DependsCollectable，理论上不会EnterWorld
    --if self.ShowModelRule == Enum.ETaskCollectShowRule.AlwaysShow then
    local ConditionID, _ = Game.LSceneActorEntityManager:GetSceneActorTriggerCondition(EWActorType.COLLECTION, self.SceneConf, self.Data)
    if ConditionID then
	    local bResult = Game.LSceneActorEntityManager:CheckAndAddTriggerCondition(self.InsID, self.ActorType, self.SceneConf, self.Data, nil, false)
	    if bResult ~= nil then
		    self.CollectableControlByCondition = true
		    self:RefreshCollectable(bResult)
		    return
	    end
    end
    --end
	self:RefreshCollectable()
end

function LSAE_TaskCollect:OnTriggerConditionCallBack(ConditionValue)
    if self.CollectableControlByCondition then
	    --self:EnableMainTriggerDetectable(ConditionValue)
	    self:RefreshCollectable(ConditionValue)
    end
end


function LSAE_TaskCollect:GetEntityConfigData()
    return nil
end

return LSAE_TaskCollect