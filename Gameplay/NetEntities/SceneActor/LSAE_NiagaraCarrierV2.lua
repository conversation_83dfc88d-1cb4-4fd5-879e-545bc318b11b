---
--- 特效载体(新版)
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/10 17:11
---
kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_NiagaraCarrierV2 : LSceneActorEntityBase
local LSAE_NiagaraCarrierV2 = DefineClass("LSAE_NiagaraCarrierV2", LSceneActorEntityBase)
LSAE_NiagaraCarrierV2:Register(EWActorType.NIAGARA_CARRIER_V2)

function LSAE_NiagaraCarrierV2:ctor()
    self.activeNiagaraID = nil
    self.inactiveNiagaraID = nil
end

function LSAE_NiagaraCarrierV2:UnInit()
    if self.activeNiagaraID then
        self:DestroyNiagaraSystem(self.activeNiagaraID)
        self.activeNiagaraID = nil
    end
    
    if self.inactiveNiagaraID then
        self:DestroyNiagaraSystem(self.inactiveNiagaraID)
        self.inactiveNiagaraID = nil
    end
end

function LSAE_NiagaraCarrierV2:AfterEnterWorld()
    self:playNiagaraByState()
end

function LSAE_NiagaraCarrierV2:OnSceneActorStateChanged()
    if not self.bInWorld then
        return
    end
    self:playNiagaraByState()
end

---@private
function LSAE_NiagaraCarrierV2:playNiagaraByState()
    local state = self:GetSceneActorState()
    self:DebugFmt("[playNiagaraByState] %s state=%s", self.InsID, state)
	
    if state == SceneActorFixedStateEnum.SA_INACTIVE then
        if self.activeNiagaraID then
            self:DestroyNiagaraSystem(self.activeNiagaraID)
            self.activeNiagaraID = nil
        end
        
        local niagaraPath = self.SceneConf.InactiveNiagara.Niagara
        if niagaraPath ~= "" then
            local rTransform = self.SceneConf.InactiveNiagara.RelativeTransform
			local rLocation = rTransform:GetLocation()
			local rRot = rTransform:GetRotation()
			local rScale = rTransform:GetScale3D()
			local m3dTransform = M3D.AssembleTransform(
				M3D.Vec3(rLocation.X, rLocation.Y, rLocation.Z),
				M3D.Quat(rRot.X, rRot.Y, rRot.Z, rRot.W),
				M3D.Vec3(rScale.X, rScale.Y, rScale.Z)
			)
			self.inactiveNiagaraID = self:PlayNiagaraEffectAttached(niagaraPath, "", m3dTransform, nil, nil, nil, nil, false)
        end
    elseif state == SceneActorFixedStateEnum.SA_ACTIVE then
        if self.inactiveNiagaraID then
            self:DestroyNiagaraSystem(self.inactiveNiagaraID)
            self.inactiveNiagaraID = nil
        end

        local niagaraPath = self.SceneConf.ActiveNiagara.Niagara
        if niagaraPath ~= "" then
            local rTransform = self.SceneConf.ActiveNiagara.RelativeTransform
			local rLocation = rTransform:GetLocation()
			local rRot = rTransform:GetRotation()
			local rScale = rTransform:GetScale3D()
			local m3dTransform = M3D.AssembleTransform(
				M3D.Vec3(rLocation.X, rLocation.Y, rLocation.Z),
				M3D.Quat(rRot.X, rRot.Y, rRot.Z, rRot.W),
				M3D.Vec3(rScale.X, rScale.Y, rScale.Z)
			)
			self.activeNiagaraID = self:PlayNiagaraEffectAttached(niagaraPath, "", m3dTransform, nil, nil, nil, nil, false)
        end
    end
end

---@public debug
function LSAE_NiagaraCarrierV2:Debug__SetState()
    if self.Data then
        self.Data.SceneActorState = SceneActorFixedStateEnum.SA_INACTIVE
    end

    if self.SceneActorState then
        self.SceneActorState = SceneActorFixedStateEnum.SA_INACTIVE
    end

    self:playNiagaraByState()
end

return LSAE_NiagaraCarrierV2
