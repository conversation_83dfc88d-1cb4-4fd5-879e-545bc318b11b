kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")
kg_require("Gameplay.NetEntities.Comps.SceneActorBehaviorComponent")
local Const = kg_require("Shared.Const")
local ULLFunc = import("LowLevelFunctions")

---@class LSAE_TurntablePuzzleTrigger
local LSAE_TurntablePuzzleTrigger = DefineLocalEntity("LSAE_TurntablePuzzleTrigger", LSceneActorEntityBase, {
    ViewControlTriggerComponent,
    SceneActorBehaviorComponent
})

LSAE_TurntablePuzzleTrigger:Register(EWActorType.TURNTABLE_PUZZLE_TRIGGER)
-- 前、右、后、左四个方向的坐标偏移
LSAE_TurntablePuzzleTrigger.DirectionOffsets = {
    {-1, 0},
    {0, 1},
    {1, 0},
    {0, -1}
}

function LSAE_TurntablePuzzleTrigger:ctor()
    local PuzzleInfo = Game.TableData.GetTurntablePuzzleDataRow(self.SceneConf.PuzzleID)
    if not PuzzleInfo then
        self:Error(">>>水管解谜物件 谜题数据错误", self.SceneConf.PuzzleID)
        return
    end
    local MapInfo = Game.TableData.GetTurntableMapDataRow(PuzzleInfo.MapID)
    self.MapIndo = MapInfo
    self.RowNum = #MapInfo
    self.ColNum = #MapInfo[1].TypeList
    self.bPlayerEnter = false                                       -- 玩家是否在范围内
    self.TriggerOffset = self.SceneConf.TriggerOffset * 100         -- 触发区域的偏移距离
    self.LocalEntities = {}                                         -- 全部场景物 {ItemType -> EntityList}
    self.CrossAreaEntities = {}                                     -- 转盘重叠部分{Index -> Entity}
    self.SpecialIndexRecord = {}                                    -- 花指示物目标位置记录
    self.ResetDeviceMark = 0                                        -- 哪些重置装置正在转动
    self.InitiateResetIndex = nil                                   -- 被点击的重置装置Index
    self.RotatingTurntableIndex = nil                               -- 正在旋转的转盘索引
    self.MarkEffects = {}                                           -- 目标位置特效
    self.LeaveTimer = nil                                           -- 离开区域计时
    self.ResetTimer = nil                                           -- 重置指示物计时
    self.NeedResetTypeIndexMap = {
        [Enum.ETurntablePuzzleItemType.Flower] = {},
        [Enum.ETurntablePuzzleItemType.Other] = {},
    }
end

function LSAE_TurntablePuzzleTrigger:AfterEnterWorld()
    self:InitTurntableItems()
end

function LSAE_TurntablePuzzleTrigger:BeforeExitWorld()
    self:Debug(">>>LSAE_TurntablePuzzleTrigger BeforeExitWorld")
    self:StopResetTimer()
    self:StopLeaveTimer()
    -- 销毁LocalEntity
    for _, EntityList in pairs(self.LocalEntities) do
        for _, Entity in pairs(EntityList) do
            if Entity and not Entity.isDestroyed then
                Game.LSceneActorEntityManager:DeleteLocalPrivateSceneActorData(Entity.InsID)
            end
        end
    end
    self.LocalEntities = {}
    -- 销毁特效
    for _, EffectID in pairs(self.MarkEffects) do
        self:DestroyNiagaraSystem(EffectID)
    end
    self.MarkEffects = {}
end

function LSAE_TurntablePuzzleTrigger:GetItemIndex(RowIndex, ColIndex)
    return (RowIndex - 1) * self.ColNum + ColIndex
end

function LSAE_TurntablePuzzleTrigger:GetRowCowIndex(ItemIndex)
    return math.ceil((ItemIndex - 1) / self.ColNum), (ItemIndex - 1) % self.ColNum + 1
end

function LSAE_TurntablePuzzleTrigger:InitTurntableItems()
    local OriPos = self.SceneConf.Transform.Position
    local PosX, PosY, PosZ = OriPos.X, OriPos.Y, OriPos.Z
    local XOffset, YOffset
    local ItemDistance = self.SceneConf.TurntableDistance * 50
    local NiagaraPath = self.SceneConf.MarkNiagaraPath
    for RowIndex, RowInfo in ksbcipairs(self.MapIndo) do
        PosY = OriPos.Y + (RowIndex - (self.RowNum + 1) / 2) * ItemDistance
        YOffset = RowIndex == 2 and -1 or (RowIndex == self.RowNum - 1 and 1 or 0)
        local SpecialMarkList = RowInfo.SpecialLocation
        for ColIndex, ItemType in ksbcipairs(RowInfo.TypeList) do
            PosX = OriPos.X + (ColIndex - (self.ColNum + 1) / 2) * ItemDistance
            local Pos = {PosX, PosY, PosZ}
            local Index = self:GetItemIndex(RowIndex, ColIndex)
            if SpecialMarkList[ColIndex] then
                local EffectID = self:PlayNiagaraEffectAtLocation(NiagaraPath, FTransform(FRotator(0):ToQuat(), FVector(unpack(Pos))))
                table.insert(self.MarkEffects, EffectID)
                self.SpecialIndexRecord[Index] = true
            end
            local ItemEntity = self:CreateTurntableItem(ItemType, Index, Pos, 0)
            XOffset = ColIndex == 2 and -1 or (ColIndex == self.ColNum - 1 and 1 or 0)
            local CrossAreaEntity
            if (RowIndex + ColIndex) % 2 ~= 0 then
                CrossAreaEntity = self:CreateTurntableItem(Enum.ETurntablePuzzleItemType.TurntableCrossArea, Index, Pos, ColIndex % 2 == 0 and 0 or 90)
                Game.TestCross = CrossAreaEntity
                CrossAreaEntity:RefreshItemIndex(Index)
                self.CrossAreaEntities[Index] = CrossAreaEntity
            end
            if ItemType == Enum.ETurntablePuzzleItemType.Turntable then
                ItemEntity:SetTriggerOffset(XOffset * self.TriggerOffset, YOffset * self.TriggerOffset)
            elseif (ItemType == Enum.ETurntablePuzzleItemType.Flower or ItemType == Enum.ETurntablePuzzleItemType.Other) and CrossAreaEntity then
                CrossAreaEntity:SetAttachedEntity(ItemEntity)
            end
            if self.NeedResetTypeIndexMap[ItemType] then
                table.insert(self.NeedResetTypeIndexMap[ItemType], Index)
            end
        end
    end
end

-- 创建LocalEntity
function LSAE_TurntablePuzzleTrigger:CreateTurntableItem(ItemType, ItemIndex, Position, Yaw)
    self:Debug(">>>LSAE_TurntablePuzzleTrigger CreateTurntableItem", ItemType, ItemIndex)
    local Data = Game.TableData.GetTurntableItemTypeDataRow(ItemType)
    if Data.WActorType == 0 then
        return
    end
    local InsID = ULLFunc.GetGlobalUniqueID()
    local NewData = {        
        InsID = InsID,
        TemplateID = Data.TemplateID,
        ActorType = Data.WActorType,
        ItemType = ItemType,
        ItemIndex = ItemIndex,
        OwnerID = self.InsID,
        BPPath = Data.BPName,
        Position = Position,
        Rotation = {0, Yaw, 0},
        SceneActorState = SceneActorFixedStateEnum.SA_ACTIVE,
    }
    if ItemType == Enum.ETurntablePuzzleItemType.Turntable then
        NewData["SceneActorBehaviorType"] = self.SceneConf.TurntableBehavior
    elseif ItemType == Enum.ETurntablePuzzleItemType.Flower then
        NewData["SceneActorState"] = self:GetIndexMarkItemState(ItemIndex)
    end
    local Entity = Game.LSceneActorEntityManager:AddLocalPrivateSceneActorData(NewData)
    if not self.LocalEntities[ItemType] then
        self.LocalEntities[ItemType] = {}
    end
    table.insert(self.LocalEntities[ItemType], Entity)
    return Entity
end

-- 刷新花指示物状态
function LSAE_TurntablePuzzleTrigger:UpdateItemState(ForceState)
    for ItemIndex, Entity in pairs(self.CrossAreaEntities) do
        local State = ForceState ~= nil and ForceState or self:GetIndexMarkItemState(ItemIndex)
        Entity:UpdateAttachedEntityState(State)
    end
end

-- 获取指定索引位置指示物的状态
function LSAE_TurntablePuzzleTrigger:GetIndexMarkItemState(ItemIndex)
    return self.SpecialIndexRecord[ItemIndex] == true and SceneActorFixedStateEnum.SA_ACTIVE or SceneActorFixedStateEnum.SA_FINISH
end

-- 是否允许重置
function LSAE_TurntablePuzzleTrigger:CheckCanReset()
    if not self.bPlayerEnter then
        return false
    end
    if self:IsResetting() or self.RotatingTurntableIndex then
        return false
    end
    return true
end

-- 是否正在重置
function LSAE_TurntablePuzzleTrigger:IsResetting()
    return self.ResetTimer or self.InitiateResetIndex
end

-- 重置装置开始旋转
function LSAE_TurntablePuzzleTrigger:OnResetDeviceRotateStart(ItemIndex, bIsInitiator)
    if bIsInitiator then
        self.InitiateResetIndex = ItemIndex
    else
        self.ResetDeviceMark = self.ResetDeviceMark | 1 << ItemIndex
    end
end

-- 重置装置旋转结束
function LSAE_TurntablePuzzleTrigger:OnResetDeviceRotateEnd(ItemIndex)
    if ItemIndex == self.InitiateResetIndex then
        self.ResetDeviceMark = 0
        local EntityList = self.LocalEntities[Enum.ETurntablePuzzleItemType.Reset]
        if not EntityList then
            return
        end
        Game.AkAudioManager:PostEvent2D("Play_GamePlay_TurntablePuzzle_Spin")
        for _, Entity in pairs(EntityList) do
            if Entity.Data.ItemIndex ~= self.InitiateResetIndex then
                Entity:StartRotate()
            end
        end
    else
        self.ResetDeviceMark = self.ResetDeviceMark & (~ 1 << ItemIndex)
    end
    if self.ResetDeviceMark == 0 then
        self.InitiateResetIndex = nil
        self:ResetMarkItems()
    end
end

-- 重置指示物
function LSAE_TurntablePuzzleTrigger:ResetMarkItems()
    self:Debug(">>>LSAE_TurntablePuzzleTrigger:ResetMarkItems")
    if self.ResetTimer then
        return
    end
    for ItemIndex, Entity in pairs(self.CrossAreaEntities) do
        Entity:RemoveAttachedItem()
    end
    for Index, Entity in pairs(self.LocalEntities[Enum.ETurntablePuzzleItemType.Flower]) do
        self:HideItemEntity(Entity, true)
    end
    for _, Entity in pairs(self.LocalEntities[Enum.ETurntablePuzzleItemType.Other]) do
        self:HideItemEntity(Entity, true)
    end
    local TableData = Game.TableData.GetTurntableItemTypeDataRow(Enum.ETurntablePuzzleItemType.Flower)
    self.ResetTimer = Game.TimerManager:CreateTimerAndStart(function()
        for ItemType, IndexList in pairs(self.NeedResetTypeIndexMap) do
            local EntityList = self.LocalEntities[ItemType]
            for Index, Entity in ipairs(EntityList) do
                local TargetIndex = IndexList[Index]
                self.CrossAreaEntities[TargetIndex]:SetAttachedEntity(Entity)
                self:HideItemEntity(Entity, false)
            end
        end
        self:UpdateItemState()
        self.ResetTimer = nil
    end, TableData.Time * 1000, 1, nil, nil, false)
end

function LSAE_TurntablePuzzleTrigger:HideItemEntity(Entity, bHide)
    self.CppEntity:KAPI_Actor_SetActorHiddenInGame(bHide)
end

function LSAE_TurntablePuzzleTrigger:StopResetTimer()
    if self.ResetTimer then
        Game.TimerManager:StopTimerAndKill(self.ResetTimer)
        self.ResetTimer = nil
    end
end

-- 转盘开始旋转，通知其他转盘更新交互状态
function LSAE_TurntablePuzzleTrigger:OnTurntableRotateStart(ItemIndex)
    if self.RotatingTurntableIndex then
        self:ErrorFmt(">>>LSAE_TurntablePuzzleTrigger 上一个转盘转动还没结束 %s", ItemIndex)
    end
    self.RotatingTurntableIndex = ItemIndex
    local EntityList = self.LocalEntities[Enum.ETurntablePuzzleItemType.Turntable]
    for _, TurntableEntity in pairs(EntityList) do
        TurntableEntity:OnTurntableRotateStart()
    end
    self:UpdateItemState(false)
end

-- 转盘结束旋转，通知其他转盘更新交互状态
function LSAE_TurntablePuzzleTrigger:OnTurntableRotateEnd(ItemIndex)
    if not self.RotatingTurntableIndex or ItemIndex ~= self.RotatingTurntableIndex then
        self:ErrorFmt(">>>LSAE_TurntablePuzzleTrigger 转盘索引不一致，当前索引：%s, 缓存索引：%s", ItemIndex, self.RotatingTurntableIndex)
    end
    self.RotatingTurntableIndex = nil
    local EntityList = self.LocalEntities[Enum.ETurntablePuzzleItemType.Turntable]
    for _, TurntableEntity in pairs(EntityList) do
        TurntableEntity:OnTurntableRotateEnd()
    end
    self:UpdateItemState()
    self:CheckSuccess()
end

-- 逆时针旋转转盘周围4个可重叠物，并返回它们的CharacterID
function LSAE_TurntablePuzzleTrigger:RotateAroundCrossAreas(ItemIndex)
    local RowIndex, ColIndex = self:GetRowCowIndex(ItemIndex)
    local EntityIDList = {}
    local DirectionOffsets = LSAE_TurntablePuzzleTrigger.DirectionOffsets
    for DirIndex, Offset in ipairs(DirectionOffsets) do
        local CurIndex = self:GetItemIndex(RowIndex + Offset[1], ColIndex + Offset[2])
        local Entity = self.CrossAreaEntities[CurIndex]
        table.insert(EntityIDList, Entity)
    end
    for DirIndex, Entity in ipairs(EntityIDList) do
        local NewOffset = DirectionOffsets[(DirIndex - 2) % 4 + 1]
        local NewRowIndex, NewColIndex = RowIndex + NewOffset[1], ColIndex + NewOffset[2]
        local NewIndex = self:GetItemIndex(NewRowIndex, NewColIndex)
        Entity:RefreshItemIndex(NewIndex)
        self.CrossAreaEntities[NewIndex] = Entity
        EntityIDList[DirIndex] = Entity.CharacterID
    end
    return EntityIDList
end

-- 检查是否完成
function LSAE_TurntablePuzzleTrigger:CheckSuccess()
    local EntityList = self.LocalEntities[Enum.ETurntablePuzzleItemType.Flower]
    for _, Entity in pairs(EntityList) do
        if not self.SpecialIndexRecord[Entity.ItemIndex] then
            return
        end
    end
    self:DoOnSuccess()
end

function LSAE_TurntablePuzzleTrigger:DoOnSuccess()
    self:Debug(">>>LSAE_TurntablePuzzleTrigger Success!")
    Game.me:ReqPuzzlePlayDone(self.InsID, Const.PUZZLE_PLAY_TYPE.ROTARY_DISK_TYPE)
end

-- 进入玩法区域
function LSAE_TurntablePuzzleTrigger:OnEnterBehaviorTrigger(OtherUID, Pos)
    self:Debug(">>>>LSAE_TurntablePuzzleTrigger OnEnterInteractiveTrigger", OtherUID)
    self.bPlayerEnter = true
    self:StopLeaveTimer()
    Game.ReminderManager:StopSpecificReminder(Enum.EReminderTextData.TURNTABLE_REMINDER_1)
end

-- 离开玩法区域
function LSAE_TurntablePuzzleTrigger:OnLeaveBehaviorTrigger(OtherUID)
    self:Debug(">>>>LSAE_TurntablePuzzleTrigger OnLeaveInteractiveTrigger", OtherUID)
    self.bPlayerEnter = false
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TURNTABLE_REMINDER_1)
    local ReminderInfo = Game.TableData.GetReminderTextDataRow(Enum.EReminderTextData.TURNTABLE_REMINDER_1)
    self:StopLeaveTimer()
    self.LeaveTimer = Game.TimerManager:CreateTimerAndStart(function()
        if not self:IsResetting() then
            self:OnResetDeviceRotateEnd()
        end
        Game.me:ReqPuzzlePlayLeaveSpace(self.InsID)
    end, ReminderInfo.Duration * 1000, 1, nil, nil, false)
end

function LSAE_TurntablePuzzleTrigger:StopLeaveTimer()
    if self.LeaveTimer then
        Game.TimerManager:StopTimerAndKill(self.LeaveTimer)
        self.LeaveTimer = nil
    end
end

return LSAE_TurntablePuzzleTrigger