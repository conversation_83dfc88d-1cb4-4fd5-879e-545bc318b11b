kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")
kg_require("Gameplay.NetEntities.Comps.SceneActorBehaviorComponent")
local EAttachmentRule = import("EAttachmentRule")

---@class LSAE_Turntable
local LSAE_Turntable = DefineLocalEntity("LSAE_Turntable", LSceneActorEntityBase, {
    ViewControlTriggerComponent,
    SceneActorBehaviorComponent,
})

LSAE_Turntable:Register(EWActorType.TURNTABLE_PUZZLE_TURNTABLE)

function LSAE_Turntable:ctor()
    -- tick间隔
    self.TickInterval = 20
    self.SceneActorBehaviorType = self.Data.SceneActorBehaviorType
    self.RotateDuration = Game.TableData.GetTurntableItemTypeDataRow(self.Data.ItemType).RotateDuration
    self.bDisableRotate = false              -- 是否禁止旋转
    self.TriggerOffset = FVector()
    self.AttachedActors = nil
    self.Owner = nil
end

function LSAE_Turntable:AfterEnterWorld()
    --Log.Debug(">>>LSAE_Turntable EnterWorld, ", TriggerCompID)
    self.Owner = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(self.Data.OwnerID)
    self:SetMainTriggerRelativeLocation(self.TriggerOffset.X, self.TriggerOffset.Y, self.TriggerOffset.Z)
    self.TurntableMesh = self.CppEntity:KAPI_Actor_GetComponentByClassName("StaticMeshComponent")
    self.MoveActorComponentID = self.CppEntity:KAPI_Actor_GetComponentByClassName("MoveActorComponent")
	if IsValidID(self.MoveActorComponentID) then
        self.CppEntity:KAPI_MoveActorID_EnableOnMeshMoveFinish(self.MoveActorComponentID, true)
	end
end

function LSAE_Turntable:OnMeshMoveFinishCallBack()
    self.bIsRotating = false
    self:RemoveAttachedActors()
    self.Owner:OnTurntableRotateEnd(self.Data.ItemIndex)
end

function LSAE_Turntable:BeforeExitWorld()
	if IsValidID(self.MoveActorComponentID) then
		if self.bIsRotating then
            self.CppEntity:KAPI_MoveActor_InterruptMove()
		end
        self.CppEntity:KAPI_MoveActorID_EnableOnMeshMoveFinish(self.MoveActorComponentID, false)
	end
    self:RemoveAttachedActors()
end

function LSAE_Turntable:GetEntityConfigData()
    return self.Data
end

function LSAE_Turntable:SetTriggerOffset(XOffset, YOffset)
    Log.Debug(">>>>SetTriggerOffset ", XOffset, YOffset)
    self.TriggerOffset.X = XOffset
    self.TriggerOffset.Y = YOffset
    self:SetMainTriggerRelativeLocation(self.TriggerOffset.X, self.TriggerOffset.Y, self.TriggerOffset.Z)
end

function LSAE_Turntable:OnClickSceneActorBehaviorButton()
    Log.Debug("LSAE_Turntable OnClickInteract", self.bDisableRotate)
    if self.bDisableRotate then return end
    self:StartRotate(-90)
end

function LSAE_Turntable:OnTurntableRotateStart()
    self.bDisableRotate = true
    self:RefreshInteractBtnState()
end

function LSAE_Turntable:OnTurntableRotateEnd()
    self.bDisableRotate = false
    self:RefreshInteractBtnState()
end

function LSAE_Turntable:RefreshInteractBtnState()
    Game.HUDInteractManager.SetInteractorBtnGray(self.bDisableRotate, self, 1)
end

function LSAE_Turntable:CanRotate()
end

function LSAE_Turntable:RemoveAttachedActors()
    if not self.AttachedActors then return end
    local cppLuaScirptEntity = self.CppEntity
    for _, ActorID in pairs(self.AttachedActors) do
        local Target = cppLuaScirptEntity:KAPI_GetLuaEntityByActorID(ActorID)
        if Target ~= nil then
            Target.CppEntity:KAPI_Actor_K2_DetachFromActor(EAttachmentRule.KeepWorld, EAttachmentRule.KeepWorld, EAttachmentRule.KeepWorld)
        end
    end
    self.AttachedActors = nil
end

function LSAE_Turntable:AddAttachedActors(ActorIDList)
    self.AttachedActors = ActorIDList
    local cppLuaScirptEntity = self.CppEntity
    for _, CharID in pairs(ActorIDList) do
        local Target = cppLuaScirptEntity:KAPI_GetLuaEntityByActorID(CharID)
        if Target ~= nil then
            Target.CppEntity:KAPI_Actor_AttachToComponent(self.TurntableMesh, "", EAttachmentRule.KeepWorld, EAttachmentRule.KeepWorld, EAttachmentRule.KeepWorld, true)
        end
    end
end

function LSAE_Turntable:StartRotate(RotateAngle)
    Log.Debug("LSAE_Turntable StartRotate", self.InsID, RotateAngle, self.RotateDuration)
    if self.MoveActorComponentID then
        self.bIsRotating = true
        local ActorIDList = self.Owner:RotateAroundCrossAreas(self.Data.ItemIndex)
        self:AddAttachedActors(ActorIDList)
        Game.AkAudioManager:PostEvent2D("Play_GamePlay_TurntablePuzzle_Spin")
        self.CppEntity:KAPI_MoveActorID_StartMoveByRotateMeshWithoutCurve(
            self.MoveActorComponentID, self.TurntableMesh, 0, 0, 1, RotateAngle, self.RotateDuration)
        self.Owner:OnTurntableRotateStart(self.Data.ItemIndex)
    end
end

function LSAE_Turntable:ResetRotation(RotateAngle)
    self:StartRotate(RotateAngle, true)
    self:RefreshConnectState(false, true)
end

-- 刷新连通状态表现
function LSAE_Turntable:RefreshConnectState(bConnected, ForceRefresh)
    Log.Debug(">>>LSAE_Turntable RefreshConnectState ", bConnected, ForceRefresh)
    if not self.bInWorld then
        self.bConnected = bConnected
        return 
    end
    if bConnected == self.bConnected and not ForceRefresh then return end
    self.bConnected = bConnected

    local CppEntity = self.CppEntity
    -- 连通特效
    local compIDs = CppEntity:KAPI_Actor_GetComponentListByClassIDAndTag("NiagaraComponent", "Connected")
    for _, compID in ipairs(compIDs) do
        CppEntity:KAPI_Component_SetActive(compID, bConnected, true)
        CppEntity:KAPI_SceneID_SetVisibility(compID, bConnected, true)
        CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(compID, true)
    end
    -- 不连通特效
    compIDs = CppEntity:KAPI_Actor_GetComponentListByClassIDAndTag("NiagaraComponent", "NotConnected")
    for _, compID in ipairs(compIDs) do
        CppEntity:KAPI_Component_SetActive(compID, not bConnected, true)
        CppEntity:KAPI_SceneID_SetVisibility(compID, not bConnected, true)
        CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(compID, true)
    end
end

return LSAE_Turntable