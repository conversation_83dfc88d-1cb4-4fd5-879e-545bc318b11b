kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")
local C7ShapeCollisionComponent = import("C7ShapeCollisionComponent")
local ECameraBlendFunction = import("ECameraBlendFunction")
local KismetMathLibrary = import("KismetMathLibrary")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local EMovePosture = kg_require("Shared.Const.ParallelBehaviorControlConst").EMovePosture

---@class LSAE_CameraControlVolume : LSceneActorEntityBase
local LSAE_CameraControlVolume = DefineLocalEntity("LSAE_CameraControlVolume", LSceneActorEntityBase,
        {ViewControlTriggerComponent})

LSAE_CameraControlVolume:Register(EWActorType.CAMERA_CONTROL_VOLUME)

function LSAE_CameraControlVolume:ctor()
    self.MinArmLength = nil
    self.MaxArmLength = nil
    self.ZoomSpeed = nil
    self.bAdjustFOV = nil
    self.bBlockDodge = nil
    self.bBlockJump = nil
    self.FOVOffset = nil
    self.MaxSpeedStage = nil
    self.FadeInTime = nil
    self.FadeOutTime = nil
    self.FadeInCurveID = nil
    self.FadeOutCurveID = nil
    -- 退出Volume时需要回退的摇臂长度
    self.RecoverArmLength = nil
    -- 是否进入且未触发离开
    self.bEnterNotLeaving = nil
end

function LSAE_CameraControlVolume:AfterEnterWorld()
    if self.Data and self:GetSceneActorState() == SceneActorFixedStateEnum.SA_ACTIVE then
        if self.SceneConf.LeaveExtent then
            self:InitMainTriggerAsBoxAndBindEvent(self.SceneConf.LeaveExtent.X, self.SceneConf.LeaveExtent.Y, self.SceneConf.LeaveExtent.Z, self, "OnEnterTriggerBox", "OnLeaveTriggerBox")
        end
    end

    self:DebugFmt("LSAE_CameraControlVolume:EnterWorld InsID:%s", self.SceneConf.ID)

    self.MinArmLength = self.SceneConf.MinArmLength
    self.MaxArmLength = self.SceneConf.MaxArmLength
    self.bAdjustFOV = self.SceneConf.bAdjustFOV or false
    self.bBlockDodge = self.SceneConf.bBlockDodge or false
    self.bBlockJump = self.SceneConf.bBlockDodge or false
    self.FOVOffset = self.SceneConf.FOVOffset or 0
    self.MaxSpeedStage = self.SceneConf.MaxSpeedStage
    self.FadeInTime = self.SceneConf.FadeInTime or 0
    self.FadeOutTime = self.SceneConf.FadeOutTime or 0
    self.RecoverArmLength = -1
    if self.SceneConf.FadeInCurve ~= "" then
        local FadeIn = true
        self:DoAsyncLoadAsset(self.SceneConf.FadeInCurve, "OnAssetLoaded", FadeIn)
    end
    if self.SceneConf.FadeOutCurve ~= "" then
        local FadeIn = false
        self:DoAsyncLoadAsset(self.SceneConf.FadeOutCurve, "OnAssetLoaded", FadeIn)
    end
end

function LSAE_CameraControlVolume:BeforeExitWorld()
    self:doCameraLeave(true)
end

function LSAE_CameraControlVolume:OnAssetLoaded(loadID, assetID, FadeIn)
    if FadeIn then
        self.FadeInCurveID = assetID
    else
        self.FadeOutCurveID = assetID
    end
end

function LSAE_CameraControlVolume:OnSceneActorStateChanged(NewState, OldState)
    if NewState == SceneActorFixedStateEnum.SA_INACTIVE then
        self:EnableMainTriggerDetectable(false)
        self:UnBindTriggerPlayerDistanceChangeEvent()
        self:OnLeaveTriggerBox()
    elseif NewState == SceneActorFixedStateEnum.SA_ACTIVE then
        self:EnableMainTriggerDetectable(true)
        self:OnDistanceChangedCheckEnterBox()
    end
end

function LSAE_CameraControlVolume:OnEnterTriggerBox(ownerUID, pos, otherActorUID)
    if self.SceneConf.LeaveExtent then
        local maxDistance = math.sqrt(self.SceneConf.LeaveExtent.X^2 + self.SceneConf.LeaveExtent.Y^2 + self.SceneConf.LeaveExtent.Z^2) / 2
        self:BindTriggerPlayerDistanceChangeEvent(maxDistance, function(entity, distance)
            self:OnDistanceChangedCheckEnterBox(entity, distance)
        end)
    end
end

function LSAE_CameraControlVolume:OnLeaveTriggerBox(ownerUID, pos, otherActorUID)
    self:UnBindTriggerPlayerDistanceChangeEvent()
end

function LSAE_CameraControlVolume:OnDistanceChangedCheckEnterBox(entity, distance)
    local triggerLeaveBoxID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("C7ShapeCollisionComponent", "Leave")
    if Game.me then
        local bInEnterBox = self:isPointInsideBox(Game.me:GetPosition(), triggerLeaveBoxID)
        if bInEnterBox and not self.bEnterNotLeaving then
            self.bEnterNotLeaving = true
            self:doCameraEnter()
        end
    end
end

function LSAE_CameraControlVolume:isPointInsideBox(position, triggerBoxID)
    return KismetMathLibrary.IsPointInBox(position, self.CppEntity:KAPI_Component_K2_GetComponentLocation(triggerBoxID), self.SceneConf.EnterExtent)
end

function LSAE_CameraControlVolume:OnLeaveTriggerBox(ownerUID, pos, otherActorUID, bExitWorld)
    if self.bEnterNotLeaving then
        self.bEnterNotLeaving = nil
        self:doCameraLeave(bExitWorld)
    end
    self:UnBindTriggerPlayerDistanceChangeEvent()
end

LSAE_CameraControlVolume.CameraControlVolumePriority = 0
function LSAE_CameraControlVolume:doCameraEnter()
    self:Debug("LSAE_CameraControlVolume:doCameraEnter")
    if Game.CameraManager then
        -- 标记为进入且未离开
        self.bEnterNotLeaving = true

        local CurArmLength = Game.CameraManager:GetThirdCameraZoomLen()
        if CurArmLength and ((self.MinArmLength > 0.0 and CurArmLength < self.MinArmLength) or (self.MaxArmLength > 0.0 and CurArmLength > self.MaxArmLength)) then
            -- 当玩家进入volume时，如果原有的摇臂长度大于volume的限制，则无论玩家在volume中如何调整摇臂长度，出volume时都会恢复原有的摇臂长度
            self.RecoverArmLength = CurArmLength
        end

        local TargetFOV = 90
        if self.bAdjustFOV then
            TargetFOV = self.FOVOffset + TargetFOV
        end

        Game.CameraManager:NotifyThirdCameraIndoorChanged(true, self.MinArmLength, self.MaxArmLength, (self.MaxArmLength + self.MinArmLength) / 2, TargetFOV, self.FadeInTime)

        if self.bBlockDodge and  Game.me then
            Game.me:DisableLocoDodge(Enum.ELocoControlTag.CameraControlVolume, true)
        end
        if self.bBlockJump and  Game.me then
            Game.me:DisableLocoJump(Enum.ELocoControlTag.CameraControlVolume, true)
        end

        local LimitedMaxSpeedStage = self:getCorrectMaxSpeedStage(self.MaxSpeedStage)
        if LimitedMaxSpeedStage then
            local PlayerEntity = Game.me
            if PlayerEntity then
                PlayerEntity:SetLocalMaxSpeedStage(LimitedMaxSpeedStage)
                self.bNeedRecoverSpeedStage = true
            end
        end
    end
end

function LSAE_CameraControlVolume:doCameraLeave(bExitWorld)
    self:Debug("LSAE_CameraControlVolume:doCameraLeave")
    if Game.CameraManager then
        if self.RecoverArmLength > 0 then
            Game.CameraManager:NotifyThirdCameraIndoorChanged(false, nil, nil, self.RecoverArmLength, nil, self.FadeInTime)
        else
            Game.CameraManager:NotifyThirdCameraIndoorChanged(false, nil, nil, nil, nil, self.FadeInTime)
        end

        if self.bBlockDodge and Game.me then
            Game.me:DisableLocoDodge(Enum.ELocoControlTag.CameraControlVolume, false)
        end
        if self.bBlockJump and Game.me then
            Game.me:DisableLocoJump(Enum.ELocoControlTag.CameraControlVolume, false)
        end

        if self.bNeedRecoverSpeedStage then
            local PlayerEntity = Game.me
            if PlayerEntity then
                PlayerEntity:SetLocalMaxSpeedStage(-1)
            end
            self.bNeedRecoverSpeedStage = false
        end
    end
    self.RecoverArmLength = -1
end

function LSAE_CameraControlVolume:getCorrectMaxSpeedStage(MaxSpeedStage)
    -- 客户端之前的参数设置为 0:idle 1:walk 2:Run 3:Sprint
    if MaxSpeedStage == 0 then
        return -1
    end

    if MaxSpeedStage == 1 then
        return EMovePosture.Walk
    end

    if MaxSpeedStage == 2 then
        return EMovePosture.Run
    end

    if MaxSpeedStage == 3 then
        return EMovePosture.Sprint
    end

    return nil
end

return LSAE_CameraControlVolume
