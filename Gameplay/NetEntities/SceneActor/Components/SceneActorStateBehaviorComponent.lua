SceneActorStateBehaviorComponent = DefineComponent("SceneActorStateBehaviorComponent")



function SceneActorStateBehaviorComponent:ctor()
    self.TriggerMap = {}
    self.ActionMap = {}
    self.ConditionMap = {}

    self.NodeIndex = 0

    self.StateBehaviorMap = {} --{StateIndex:{}}
end

function SceneActorStateBehaviorComponent:dtor()
    self.TriggerMap = nil
    self.ActionMap = nil
    self.ConditionMap = nil
end

function SceneActorStateBehaviorComponent:LoadTrigger()
    --TODO表加载
end

function SceneActorStateBehaviorComponent:LoadAction()
--TODO表加载
end

function SceneActorStateBehaviorComponent:LoadCondition()
--TODO表加载
end

function SceneActorStateBehaviorComponent:OnSwitchSceneActorState(NewState)
    local StateBehaviorData = self.StateBehaviorMap[NewState]
    if StateBehaviorData then
        self.TriggerMap = StateBehaviorData.Triggers
        self.ConditionMap = StateBehaviorData.Conditions
        self.ActionMap = StateBehaviorData.Actions
    end
end

return SceneActorStateBehaviorComponent