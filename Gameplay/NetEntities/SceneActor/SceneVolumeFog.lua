local sceneObjectBase = kg_require("Gameplay.NetEntities.SceneActor.SceneObjectBase")
local StaticMeshComponent = import("StaticMeshComponent")
---@class SceneVolumeFog
local SceneVolumeFog = DefineLocalEntity("SceneVolumeFog", sceneObjectBase.SceneObjectBase)

SceneVolumeFog:Register(EWActorType.VOLUME_FOG)
SceneVolumeFog.OpacityMaterialParamName = "Opcity"
SceneVolumeFog.ScaleVector = FVector(1,1,1)

function SceneVolumeFog:AfterEnterWorld()
    local RootComponentID = self.CppEntity:KAPI_Actor_GetRootComponent()
    local RelativeTransform = self.CppEntity:KAPI_SceneID_GetRelativeTransform(RootComponentID)
    self.RootOriginalScale3D = RelativeTransform:GetScale3D()
    if self.OverrideConf then
        if self.OverrideConf.Density then
            self:SetFogOpacity(self.OverrideConf.Density, 0)
        end
        if self.OverrideConf.Scale then
            self:SetFogScale(self.OverrideConf.Scale)
        end
    end
    LuaScriptAPI.SetActorHiddenInGame(self.CharacterID, false)
end

function SceneVolumeFog:BeforeExitWorld()
    if self.OpacityTaskId then
        Game.MaterialManager:RemoveMaterialParamUpdateTask(self.OpacityTaskId)
        self.OpacityTaskId = nil
    end
    self:SetFogScale(1.0)
	LuaScriptAPI.SetActorHiddenInGame(self.CharacterID, true)
end

function SceneVolumeFog:SetFogOpacity(NewOpacity, OpacityDuration)
    local CompID = self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
    if self.OpacityTaskId then
        Game.MaterialManager:RemoveMaterialParamUpdateTask(self.OpacityTaskId)
        self.OpacityTaskId = nil
    end
    if OpacityDuration > 0 then
        local StartOpacity = self.SceneConf.OpacityLevel or 0
        local MaterialInstArray = self.CppEntity:KAPI_MeshID_GetMaterials(CompID)
        self.OpacityTaskId = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceIDs(SceneVolumeFog.OpacityMaterialParamName, MaterialInstArray, StartOpacity ,NewOpacity ,OpacityDuration)
    else
        self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
        local DynamicMaterialId = self.CppEntity:KAPI_PrimitiveID_GetMaterial(CompID, 0)
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(DynamicMaterialId, SceneVolumeFog.OpacityMaterialParamName, NewOpacity)
    end
end

function SceneVolumeFog:SetFogScale(Scale)
    if self.RootOriginalScale3D then
        local RootComponentID = self.CppEntity:KAPI_Actor_GetRootComponent()
        SceneVolumeFog.ScaleVector.X = Scale * self.RootOriginalScale3D.X
        SceneVolumeFog.ScaleVector.Y = Scale * self.RootOriginalScale3D.Y
        SceneVolumeFog.ScaleVector.Z = Scale *  self.RootOriginalScale3D.Z
        self.CppEntity:KAPI_SceneID_SetWorldScale3D(RootComponentID, SceneVolumeFog.ScaleVector)
    end
end


function SceneVolumeFog:AfterSetOverrideConf(OverrideConf)
    if OverrideConf.ModifyType == 1 then
        if OverrideConf.Density then
            local Duration = OverrideConf.TransitionTime or 0
            self:SetFogOpacity(OverrideConf.Density, Duration)
        end
        if OverrideConf.Scale then
            self:SetFogScale(OverrideConf.Scale)
        end
    end
end

return SceneVolumeFog