kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

local CollisionConst = kg_require("Shared.Const.CollisionConst")
local EDrawDebugTrace = import("EDrawDebugTrace")
local EPropertyClass = import("EPropertyClass")


---@class LSAE_TeamMark
local LSAE_TeamMark = DefineLocalEntity("LSAE_TeamMark", LSceneActorEntityBase, {}) -- luacheck: ignore

LSAE_TeamMark.FindFloorZDistance = 2000
LSAE_TeamMark.NaviPointFoundExtent = { 50, 50, 250 }
LSAE_TeamMark.TempFloorPos = FVector(0, 0, 0)
LSAE_TeamMark.TempWalkablePos = FVector(0, 0, 0)
LSAE_TeamMark.TraceCapsuleRadius = 100
LSAE_TeamMark.TraceCapsuleHeight = 200
LSAE_TeamMark.FindGroundStartPos = FVector()
LSAE_TeamMark.FindGroundEndPos = FVector(0, 0, -2000)
LSAE_TeamMark.ActorIDsToIgnore = slua.Array(EPropertyClass.Int64)

LSAE_TeamMark:Register(EWActorType.TEAM_MARK)

-- 序号对应的颜色
LSAE_TeamMark.ColorKeyMap = {
	Team = { "Blue_01", "Purple_01", "Red_01", "Green_01", "Green_02", "Yellow_01" },
	Guild = { "Purple_02", "Blue_02", "Red_02", "Green_03", "Green_04", "Yellow_02" },
}

function LSAE_TeamMark:ctor()
	self.bListenPlayerDistance = false
end

function LSAE_TeamMark:AfterEnterWorld()
	if self.Data.bGuildType then
		self.StickGroundRadius = 3000
		self.NiagaraPath = Game.TableData.GetConstDataRow("GUILD_COMMAND_BEAM")
	end
    self:RefreshNiagara()
	if self.StickGroundRadius and self.StickGroundRadius > 0 then
		if not Game.me or not Game.me.bInWorld then
			Game.GlobalEventSystem:AddListener(EEventTypesV2.LEVEL_ON_ROLE_LOAD_COMPLETED, "OnMainPlayerLoaded", self)
		else
			self:TryListenMainPlayerDistance()
		end
	end
	Game.EventSystem:AddListener(_G.EEventTypes.ON_TEAM_MARK_VISIBLE_CHANGED, self, self.OnTeamMarkVisibleChanged)
end

function LSAE_TeamMark:RemoveDistanceListen()
	if self.bListenPlayerDistance then
		if Game.me then
			Game.me:UnRegisterMainPlayerDistanceChanged(self:uid())
		end
		self.bListenPlayerDistance = false
	end
end

function LSAE_TeamMark:BeforeExitWorld()
	Game.EventSystem:RemoveObjListeners(self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
	self:RemoveDistanceListen()
end

function LSAE_TeamMark:RefreshNiagara()
	if StringValid(self.NiagaraPath) then
		self:DoAsyncLoadAsset(self.NiagaraPath, "OnNiagaraLoaded")
		local niagaraCompID = self.CppEntity:KAPI_Actor_GetComponentByClassName("NiagaraComponent")
		self.CppEntity:KAPI_SceneID_SetVisibility(niagaraCompID, false, true)
	else
		self:RefreshNiagaraColor()
	end
end

function LSAE_TeamMark:OnNiagaraLoaded(LoadID, AssetID)
	if AssetID ~= 0 then
		local niagaraCompID = self.CppEntity:KAPI_Actor_GetComponentByClassName("NiagaraComponent")
		self.CppEntity:KAPI_NiagaraID_SetAsset(niagaraCompID, AssetID)
		self:RefreshNiagaraColor()
	end
end

function LSAE_TeamMark:RefreshNiagaraColor()
	local ColorKeyMap = LSAE_TeamMark.ColorKeyMap.Team
	if self.Data.bGuildType then
		ColorKeyMap = LSAE_TeamMark.ColorKeyMap.Guild
	end
	
    local SequenceNum = self.Data.TemplateID
	local niagaraCompID = self.CppEntity:KAPI_Actor_GetComponentByClassName("NiagaraComponent")
	if niagaraCompID ~= 0 then
		self.CppEntity:KAPI_Component_SetActive(niagaraCompID, true, true)
		self.CppEntity:KAPI_SceneID_SetVisibility(niagaraCompID, true, true)
		self.CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(niagaraCompID, true)
		if self.Data.bGuildType then
			self.CppEntity:KAPI_NiagaraID_SetVariableFloat(niagaraCompID, "Scale", Enum.EGuildLeagueConstIntData.GUILD_COMMAND_BEAM_ZOOM)
			self.CppEntity:KAPI_NiagaraID_SetVariableFloat(niagaraCompID, "BaseMaxHeight", Enum.EGuildLeagueConstIntData.GUILD_COMMAND_BEAM_BASE_MAX_HEIGHT)
			self.CppEntity:KAPI_NiagaraID_SetVariableFloat(niagaraCompID, "BaseMinHeight", Enum.EGuildLeagueConstIntData.GUILD_COMMAND_BEAM_BASE_MIN_HEIGHT)
		end
		
		local ColorKey = ColorKeyMap[SequenceNum]
		if ColorKey then
			local Color = Game.ColorManager:GetColor("HUDColor", ColorKey, Game.ColorManager.Type.LinearColor)
			if Color then
				self.CppEntity:KAPI_NiagaraID_SetVariableLinearColor(niagaraCompID, "MeshColor", Color)
			end
		end
		self.CppEntity:KAPI_NiagaraID_SetVariableFloat(niagaraCompID, "NumSelect", SequenceNum-1)
		self.CppEntity:KAPI_NiagaraID_ReinitializeSystem(niagaraCompID)
	end
end

function LSAE_TeamMark:OnTeamMarkVisibleChanged(bVisible)
	if bVisible then
		self:SetActorVisible(Enum.EInVisibleReasons.SceneActorBehaviorControl)
	else
		self:SetActorInVisible(Enum.EInVisibleReasons.SceneActorBehaviorControl)
	end
end

function LSAE_TeamMark:OnMainPlayerLoaded()
	self:TryListenMainPlayerDistance()
end

function LSAE_TeamMark:TryListenMainPlayerDistance()
	local DirVec = self:GetPosition() - Game.me:GetPosition()
	local Distance = DirVec:Size()
	if Distance > self.StickGroundRadius then
		self.bListenPlayerDistance = true
		Game.me:RegisterMainPlayerDistanceChanged(self:uid(), self.StickGroundRadius, function(Owner, CurDistance)
			if CurDistance < self.StickGroundRadius then
				self:RefreshGroundState()
				Game.me:UnRegisterMainPlayerDistanceChanged(self:uid())
				self.bListenPlayerDistance = false
			end
		end)
	else
		self:RefreshGroundState()
	end
end

function LSAE_TeamMark.FoundGroundPos(InPos)
	LSAE_TeamMark.FindGroundStartPos.Z = LSAE_TeamMark.TraceCapsuleHeight * 3
	local StartPos = InPos + LSAE_TeamMark.FindGroundStartPos
	local EndPos = InPos + LSAE_TeamMark.FindGroundEndPos
	local hitResults = LuaScriptAPI.KismetSystem_CapsuleTraceMultiForObjects(
			Game.me.CharacterID, StartPos, EndPos, LSAE_TeamMark.TraceCapsuleRadius, LSAE_TeamMark.TraceCapsuleHeight, 
			CollisionConst.QUERY_BY_OBJECTTYPES.INTERACTABLE_FOR_LINE_TRACE, false, LSAE_TeamMark.ActorIDsToIgnore, EDrawDebugTrace.None,
			false, FLinearColor.Red, FLinearColor.Green, 30)
	local HitPos
	if hitResults.bResult then
		if hitResults.C7HitResults:Num() >= 1 then
			local bStartPenetrating = false
			for _, c7HitResult in ipairs(hitResults.C7HitResults:ToTable()) do
				local hitResult = c7HitResult.HitResult
				if not hitResult.bBlockingHit then
					goto continue
				end
				-- 第一个要是bStartPenetrating，很大概率就是在墙上就不再往下找了
				if not bStartPenetrating and hitResult.bStartPenetrating then
					bStartPenetrating = true
					goto continue
				end
				-- 大概率打到歪了的建筑
				if 1.0 - math.abs(hitResult.Normal.Z) > 0.1 then
					goto continue
				end

				local hitObjectID = c7HitResult.HitObjectId
				-- todo 提供给lua直接返回一个不带Object的结构体
				if hitObjectID ~= 0 and (LuaScriptAPI.IsATypeOf(hitObjectID, "BaseCharacter") or LuaScriptAPI.IsATypeOf(hitObjectID, "BlockingVolume")) then
					goto continue
				end
				local LocationNetQuantize = hitResult.ImpactPoint --value.Location
				HitPos = FVector(LocationNetQuantize.X, LocationNetQuantize.Y, LocationNetQuantize.Z)
				break
				::continue::
			end
		end
	end
	
	if HitPos then
		return HitPos
	else
		-- 实在找不到了
		self:WarningFmt("can not found valid ground pos %d, %d, %d", InPos.X, InPos.Y, InPos.Z)
	end
	return InPos
end

function LSAE_TeamMark:RefreshGroundState()
	local ViewPosition = self:GetPosition()
	local GroundPosition --LSAE_TeamMark.FoundGroundPos(ViewPosition)

	local nearestNavPos = LuaScriptAPI.GetNearestNaviPoint(
			self.CharacterID, ViewPosition.X, ViewPosition.Y, ViewPosition.Z, 
			LSAE_TeamMark.NaviPointFoundExtent[1], LSAE_TeamMark.NaviPointFoundExtent[2], LSAE_TeamMark.NaviPointFoundExtent[3])
	if nearestNavPos ~= nil then
		GroundPosition = nearestNavPos
		self:DebugFmt("team mark stick ground pos found by navigate point %s", LSAE_TeamMark.TempFloorPos:ToString())
	else
		LSAE_TeamMark.TempWalkablePos.X = ViewPosition.X
		LSAE_TeamMark.TempWalkablePos.Y = ViewPosition.Y
		LSAE_TeamMark.TempWalkablePos.Z = ViewPosition.Z - LSAE_TeamMark.FindFloorZDistance
		local floorPos = LuaScriptAPI.GetWalkableFloorPos(self.CharacterID, ViewPosition.X, ViewPosition.Y, ViewPosition.Z, 
			LSAE_TeamMark.TempWalkablePos.X, LSAE_TeamMark.TempWalkablePos.Y, LSAE_TeamMark.TempWalkablePos.Z)
		if floorPos then
			GroundPosition = floorPos
			self:DebugFmt("team mark stick ground pos found by walkable floor pos %s", floorPos:ToString())
		else
			self:WarningFmt("team mark stick ground pos cant not found %s", ViewPosition:ToString())
		end
	end
	if not GroundPosition then
		GroundPosition = LSAE_TeamMark.FoundGroundPos(ViewPosition)
	end
	self:SetPosition(GroundPosition, false, nil, false)
	if self.Data.bGuildType then
		Game.GuildLeagueSystem:OnTagPosUpdate(self.InsID, self.Data.TemplateID, GroundPosition)
	end
end



return LSAE_TeamMark