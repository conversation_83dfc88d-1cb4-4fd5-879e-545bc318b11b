kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_SpiritRealmPortal
local LSAE_SpiritRealmPortal = DefineLocalEntity("LSAE_SpiritRealmPortal", LSceneActorEntityBase,
    {
        ViewControlTriggerComponent,
        SceneActorBehaviorComponent,
    }
)

LSAE_SpiritRealmPortal:Register(EWActorType.SPIRIT_REALM_PORTAL)

local SpiritRealmPortalStateEnum = {
    NiagaraInActive = 0,
    NiagaraActive = 1,
    UIActive = 2,
    UNINITIALIZED = 3
}

function LSAE_SpiritRealmPortal:ctor()

end

function LSAE_SpiritRealmPortal:AfterEnterWorld()

    self.NiagaraActiveEIDs = {}
    self.NiagaraInActiveEIDs = {}
    self.PortalState = SpiritRealmPortalStateEnum.UNINITIALIZED
    local PlayerLocation = Game.me:GetPosition()
    local Distance = (PlayerLocation - self.CppEntity:KAPI_GetLocation()):Size()
    self:OnDistanceChangedSwitchNiagara(self, Distance)
end

function LSAE_SpiritRealmPortal:BeforeExitWorld()
    for Index, EID in pairs(self.NiagaraActiveEIDs) do
        self:DeactivateNiagaraSystem(EID)
    end
    for Index, EID in pairs(self.NiagaraInActiveEIDs) do
        self:DeactivateNiagaraSystem(EID)
    end
    table.clear(self.NiagaraActiveEIDs)
    table.clear(self.NiagaraInActiveEIDs)
    self:TriggerFailedBehavior()
    self:UnBindTriggerPlayerDistanceChangeEvent()
end

function LSAE_SpiritRealmPortal:TriggerStartBehavior()
    if not Game.me:CanTriggerByEntity(self) then
        return
    end
	local UITemplateID = self:GetTriggerConfValue("UITemplateID")
    if UITemplateID then
        self:BindTriggerPlayerDistanceChangeEvent(self.SceneActorBehaviorData.InteractorRadius, function(Entity, Distance) 
            self:OnDistanceChangedSwitchNiagara(Entity, Distance)
        end)
    end
end

function LSAE_SpiritRealmPortal:TriggerFailedBehavior()
    if self.bBuildInteractorTask then
        Game.HUDInteractManager.DestroyInteractorTask(self:uid())
        self.bBuildInteractorTask = false
    end
    self:UnBindTriggerPlayerDistanceChangeEvent()
    --self:Warning("lizhemian :UnBind ")
end

function LSAE_SpiritRealmPortal:OnDistanceChangedSwitchNiagara(Entity, Distance)
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InsID)
    if not SceneActorData then return end
    --self:Warning("lizhemian: Distance " .. Distance)
    if Distance / 100 < SceneActorData.UIDistance and self.PortalState ~= SpiritRealmPortalStateEnum.UIActive then
        self:SetUIActive()
        --self:Warning("lizhemian SetUIActive")
    elseif Distance / 100 > SceneActorData.UIDistance and Distance / 100 < SceneActorData.ActiveDistance
        and self.PortalState ~= SpiritRealmPortalStateEnum.NiagaraActive then
        self:SetNiagaraActive()
        --self:Warning("lizhemian SetNiagaraActive")
    elseif Distance / 100 > SceneActorData.DeactiveDistance and self.PortalState ~= SpiritRealmPortalStateEnum.NiagaraInActive then
        self:SetNiagaraInActive()
        --self:Warning("lizhemian SetNiagaraInActive")
    end
    --self:Warning("lizhemian :Distance "..Distance)
end

function LSAE_SpiritRealmPortal:SetNiagaraActive()
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InsID)
    if self.PortalState == SpiritRealmPortalStateEnum.NiagaraInActive or self.PortalState == SpiritRealmPortalStateEnum.UNINITIALIZED then
        for Index, NiagaraInfo in ksbcpairs(SceneActorData.SwitchNiagaras) do
            self:PlayNiagaraEffectAttached(NiagaraInfo.Niagara, "root", M3D.ToTransform(NiagaraInfo.RelativeTransform))
        end
    end
    for Index, EID in pairs(self.NiagaraInActiveEIDs) do
        self:DeactivateNiagaraSystem(EID)
    end
    table.clear(self.NiagaraInActiveEIDs)
    if self.PortalState == SpiritRealmPortalStateEnum.NiagaraInActive or self.PortalState == SpiritRealmPortalStateEnum.UNINITIALIZED then
        for Index, NiagaraInfo in ksbcpairs(SceneActorData.ActiveNiagaras) do
            local EID = self:PlayNiagaraEffectAttached(NiagaraInfo.Niagara, "root", M3D.ToTransform(NiagaraInfo.RelativeTransform))
            table.insert(self.NiagaraActiveEIDs, EID)
        end
    end
    if self.PortalState == SpiritRealmPortalStateEnum.UIActive then
        if self.bBuildInteractorTask then
            Game.HUDInteractManager.DestroyInteractorTask(self:uid())
            self.bBuildInteractorTask = false
        end
    end
    self.PortalState = SpiritRealmPortalStateEnum.NiagaraActive
end

function LSAE_SpiritRealmPortal:SetNiagaraInActive()
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InsID)
    for Index, NiagaraInfo in ksbcpairs(SceneActorData.SwitchNiagaras) do
        self:PlayNiagaraEffectAttached(NiagaraInfo.Niagara, "root", M3D.ToTransform(NiagaraInfo.RelativeTransform))
    end
    for Index, NiagaraInfo in ksbcpairs(SceneActorData.InActiveNiagaras) do
        local EID = self:PlayNiagaraEffectAttached(NiagaraInfo.Niagara, "root", M3D.ToTransform(NiagaraInfo.RelativeTransform))
        table.insert(self.NiagaraInActiveEIDs, EID)
    end
    for Index, EID in pairs(self.NiagaraActiveEIDs) do
        self:DeactivateNiagaraSystem(EID)
    end
    table.clear(self.NiagaraActiveEIDs)
    if self.PortalState == SpiritRealmPortalStateEnum.UIActive then
        if self.bBuildInteractorTask then
            Game.HUDInteractManager.DestroyInteractorTask(self:uid())
            self.bBuildInteractorTask = false
        end
    end
    self.PortalState = SpiritRealmPortalStateEnum.NiagaraInActive
end

function LSAE_SpiritRealmPortal:SetUIActive()
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InsID)
    if self.PortalState == SpiritRealmPortalStateEnum.UNINITIALIZED or self.PortalState == SpiritRealmPortalStateEnum.NiagaraInActive then
        for Index, NiagaraInfo in ksbcpairs(SceneActorData.SwitchNiagaras) do
            self:PlayNiagaraEffectAttached(NiagaraInfo.Niagara, "root", M3D.ToTransform(NiagaraInfo.RelativeTransform))
        end
        for Index, NiagaraInfo in ksbcpairs(SceneActorData.ActiveNiagaras) do
            local EID = self:PlayNiagaraEffectAttached(NiagaraInfo.Niagara, "root", M3D.ToTransform(NiagaraInfo.RelativeTransform))
            table.insert(self.NiagaraActiveEIDs, EID)
        end
        for Index, EID in pairs(self.NiagaraInActiveEIDs) do
            self:DeactivateNiagaraSystem(EID)
        end
        table.clear(self.NiagaraInActiveEIDs)
    end
	local UITemplateID = self:GetTriggerConfValue("UITemplateID")
    if UITemplateID then
        -- 按键交互
        if SceneActorData.UITemplateID ~= 0 then
            self.bBuildInteractorTask = true
            Game.HUDInteractManager.BuildInteractorTask(self:uid(), { SceneActorData.UITemplateID }, function()
                self:OnClickSceneActorBehaviorButton()
            end)
        end
    end
    self.PortalState = SpiritRealmPortalStateEnum.UIActive
end

function LSAE_SpiritRealmPortal:OnClickSceneActorBehaviorButton()
    Game.MapSystem:OpenMapPanel()
end

return LSAE_SpiritRealmPortal