require("Shared.WorldActorDefine")
require( "Shared.Utils.InteractorUtils")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

--本地场景物体Entity管理器
---@class LSceneActorEntityManager
local LSceneActorEntityManager = DefineClass("LSceneActorEntityManager")

LSceneActorEntityManager.LocalSceneActorEntityClass = {}

-- NOTE
-- [1] 默认BP路径
-- [2] 表格配置的BPName的format的路径，表格配置来源Entity:GetEntityConfigData()返回的BPName（名字不能任意发挥）
-- [3] 表格配置的BPPath全路径，表格配置来源Entity:GetEntityConfigData()返回的BPPath（名字不能任意发挥）
LSceneActorEntityManager.EPClassMap = {
    [EWActorType.INTERACTIVE_CHAIR] = { nil, "/Game/Arts/SceneActorBP/Chair/%s.%s_C" },

    [EWActorType.INTERACTIVE_SWING] = { "/Game/Arts/SceneActorBP/Swing/BP_Pirate_School_BeanstalkSwing.BP_Pirate_School_BeanstalkSwing_C" },
    [EWActorType.NON_INTERACT_MESH] = { "/Game/Blueprint/SceneActor/BP_NonInteractMesh.BP_NonInteractMesh_C" },

    [EWActorType.SPIRIT_REALM_PORTAL] = { "/Game/Blueprint/SceneActor/BP_SpiritRealmPortal.BP_SpiritRealmPortal_C" },
    [EWActorType.INTERACTIVE_DOOR] = { "/Game/Blueprint/SceneActor/BP_DoorBase.BP_DoorBase_C", "/Game/Arts/SceneActorBP/Door/%s.%s_C" },
    [EWActorType.PHARMACY_SPOON] = { "" },


    [EWActorType.CONSECRATION_SPIRIT] = { "/Game/Blueprint/SceneActor/BP_Consecration_Spirit.BP_Consecration_Spirit_C" },
    [EWActorType.CONSECRATION_MONUMENT] = { "/Game/Blueprint/SceneActor/BP_Consecration_Monument.BP_Consecration_Monument_C" },
    [EWActorType.CLOCK_LAMP] = { "" },
    [EWActorType.FOG_TRIGGER] = { "/Game/Blueprint/SceneActor/BP_FogTrigger.BP_FogTrigger_C" },
    [EWActorType.MIRROR] = { "" },


    [EWActorType.HEIGHT_FOG_MODIFIER] = { "/Game/Blueprint/SceneActor/BP_HeightFogModifier.BP_HeightFogModifier_C" },




	


    [EWActorType.SPLINE_FOOT_PRINT] = { "/Game/Blueprint/SceneActor/BP_InteractorFootprint.BP_InteractorFootprint_C" },
    [EWActorType.PLANE_PORTAL] = { "/Game/Blueprint/SceneActor/BP_PlanePortalV2.BP_PlanePortalV2_C" },
    [EWActorType.INTERACTIVE_PORTAL] = { "/Game/Blueprint/SceneActor/BP_InteractivePortalV2.BP_InteractivePortalV2_C" },
    [EWActorType.NIAGARA_CARRIER_V2] = { "/Game/Blueprint/SceneActor/BP_NiagaraCarrierV2.BP_NiagaraCarrierV2_C" },
    [EWActorType.CUSTOM_SHAPE_WALL] = { "/Game/Blueprint/SceneActor/BP_CustomShapeWall.BP_CustomShapeWall_C" },
    [EWActorType.CAMERA_CONTROL_VOLUME] = { "/Game/Blueprint/SceneActor/BP_CameraControlVolume.BP_CameraControlVolume_C" },
    [EWActorType.COLLECTION] = { "/Game/Blueprint/SceneActor/BP_TaskCollect.BP_TaskCollect_C" },
    [EWActorType.MONSTER_CHASE] = { "/Game/Blueprint/SceneActor/BP_MonsterChase.BP_MonsterChase_C" },
    [EWActorType.CHASED_MONSTER] = { "/Game/Blueprint/LogicActor/BP_ChasedMonster.BP_ChasedMonster_C" },

    [EWActorType.INTERACTIVE_STREETLIGHT] = { "/Game/Blueprint/SceneActor/BP_InteractiveStreetLight.BP_InteractiveStreetLight_C", "/Game/Arts/SceneActorBP/StreetLight/%s.%s_C" },
    --[EWActorType.STREET_LIGHT] = { "" },



    [EWActorType.MAGIC_WALL] = { "/Game/Blueprint/SceneActor/BP_MagicWall.BP_MagicWall_C" },

    [EWActorType.BATTLE_ZONE] = { "/Game/Blueprint/SceneActor/BP_BattleZone.BP_BattleZone_C" },
    [EWActorType.TRIGGER_LIGHT] = { "/Game/Blueprint/SceneActor/BP_ShapeTriggerDisplay.BP_ShapeTriggerDisplay_C" },
    [EWActorType.SPIRITUALITY_WALL] = { "/Game/Blueprint/SceneActor/BP_SpiritualityWall.BP_SpiritualityWall_C" },

    [EWActorType.MESH_CARRIER] = { "/Game/Blueprint/SceneActor/BP_MeshCarrier.BP_MeshCarrier_C" },
    [EWActorType.JUMP_POINT] = { "/Game/Blueprint/SceneActor/BP_JumpPointV2.BP_JumpPointV2_C" },



    [EWActorType.GROW_DECAY_PLANT] = { "/Game/Blueprint/SceneActor/BP_GrowDecayPlant.BP_GrowDecayPlant_C" },


    [EWActorType.LOAD_SEQUENCE] = { "/Game/Blueprint/SceneActor/BP_Sequence.BP_Sequence_C" },




    [EWActorType.PREPARE_ZONE] = { "/Game/Blueprint/SceneActor/BP_PrepareZoneV2.BP_PrepareZoneV2_C" },
    [EWActorType.TEAM_MARK] = { "/Game/Blueprint/SceneActor/BP_TeamMarkV2.BP_TeamMarkV2_C" },


    [EWActorType.DROP_ITEM] = { "/Game/Blueprint/SceneActor/BP_DropItem.BP_DropItem_C" },
	[EWActorType.DROP_ITEM_GROUP] = { "/Game/Blueprint/SceneActor/BP_DropGroup.BP_DropGroup_C" },

	[EWActorType.PAINTING_VIDEO] = { "/Game/Blueprint/SceneActor/BP_PaintingVideo.BP_PaintingVideo_C" },

	[EWActorType.MOVABLE_PLATFORM_WAY_PATH] = { "/Game/Blueprint/SceneActor/BP_MovablePlatformWayPath.BP_MovablePlatformWayPath_C" },
	[EWActorType.MOVABLE_PLATFORM] = { nil, "/Game/Blueprint/SceneActor/MovablePlatform/%s.%s_C" },

	[EWActorType.ESTATE_PORTAL] = { "/Game/Blueprint/SceneActor/BP_EstatePortal.BP_EstatePortal_C" },
	[EWActorType.TURNTABLE_PUZZLE_TRIGGER] = { "/Game/Blueprint/SceneActor/Puzzle/BP_PuzzleTrigger.BP_PuzzleTrigger_C" },
    [EWActorType.SOUND_TRACE] = { "/Game/Blueprint/SceneActor/BP_SoundTrace.BP_SoundTrace_C" },



    [EWActorType.CUTTABLE_TREE] = { "/Game/Blueprint/SceneActor/BP_CuttableTree.BP_CuttableTree_C" },
    [EWActorType.DECAL_CARRIER] = { "/Game/Blueprint/SceneActor/BP_DecalCarrier.BP_DecalCarrier_C" },
	[EWActorType.TASK_PLANE_PORTAL] = { "/Game/Blueprint/SceneActor/BP_TaskPlanePortal.BP_TaskPlanePortal_C" },
	[EWActorType.SPIRIT_EFFECT_LINE] = { "/Game/Blueprint/SceneActor/BP_SpiritEffectLine.BP_SpiritEffectLine_C" },





	[EWActorType.PILLAR] = { "/Game/Blueprint/SceneActor/BP_Pillar.BP_Pillar_C" },
	[EWActorType.OCCUPY_DETECT_AREA] = { "/Game/Blueprint/LogicActor/BP_OccupyDetectArea.BP_OccupyDetectArea_C" },
	[EWActorType.PVP_MAGIC_POINT] = { "/Game/Blueprint/LogicActor/BP_LocalCaptureMagicPoint.BP_LocalCaptureMagicPoint_C" },
	
    [EWActorType.TURNTABLE_PUZZLE_TURNTABLE] = {nil, nil, "" },
    [EWActorType.TURNTABLE_BARRIER] = {nil, nil, "" },
    [EWActorType.TURNTABLE_CROSS_AREA] = {nil, nil, "" },
    [EWActorType.TURNTABLE_RESET_DEVICE] = {nil, nil, "" },
}

--新的场景物类别定义, 兼容之前的场景数据
LSceneActorEntityManager.EPClassActorTypeMap = {
    EP_PharmacySpoon = EWActorType.PHARMACY_SPOON
}

--对于动态加载的SceneActor, 默认的Spawn信息
LSceneActorEntityManager.ActorTypeDefaultMap = {
    [EWActorType.PHARMACY_SPOON] = "",
    -- [EWActorType.SCENEACTOR_WINDMILL] = "",
    --[EWActorType.STREET_LIGHT] = "",
}

LSceneActorEntityManager.IgnoreAOIWhitelist = {
	[EWActorType.CONSECRATION_MONUMENT] = "",
	[EWActorType.TEAM_MARK] = "",
}

LSceneActorEntityManager.ECreateSourceType = {
    Server_Public = 1,
    Server_Private = 2,
    Local_Scene = 3,
    Server_LazyLoad = 4,
    Client_Private = 5,
}

function LSceneActorEntityManager.RegisterLSceneActorEntity(cls, ActorType)
    LSceneActorEntityManager.LocalSceneActorEntityClass[ActorType] = cls
end

function LSceneActorEntityManager:ctor()
    self:Reset()
end

function LSceneActorEntityManager:dtor()
    self:Reset()
end

function LSceneActorEntityManager:Reset()
    --Server EntityUID 到 LSceneActor
    self.SEntityID_LSceneActorMap = {}

    --InsID 到 LSceneActor
    self.InsID_LSceneActorMap = {}

    --InsID 到 Server EntityUID
    self.InsID_SEntityIDMap = {}

    --等待销毁的队列
    self.WaitDestroyIDMap = {} --ID 到 剩余时刻
	self.WaitDestroyLocalSceneActor = {} -- {InsID: LocalSceneActor}
    self.WaitCtolTimerHandler = nil

    self.EnablePrivateSceneActorAoi = true
    self.PrivateSceneActorAoiData = {}

    self.FVector_Temp = FVector()
    self.FRotator_Temp = FRotator()

    self.TriggerConditionSceneActorData = {}
end

function LSceneActorEntityManager:Init()
    self:Reset()
end

function LSceneActorEntityManager:UnInit()
    self:Reset() --临时
	Game.EventSystem:RemoveObjListeners(self)
end

--根据InsID查找 LSceneActor
function LSceneActorEntityManager:GetLSceneActorFromInsID(InsID)
    return self.InsID_LSceneActorMap[InsID]
end

--接受Rpc
function LSceneActorEntityManager:CallFunc(InsID, FuncName, ...)
    local _LSceneActor = self:GetLSceneActorFromInsID(InsID)
    if _LSceneActor and _LSceneActor[FuncName] then
        xpcall(_LSceneActor[FuncName], _G.CallBackError, _LSceneActor, ...)
    end
end

--地图加载完成
function LSceneActorEntityManager:OnMapLoaded(CurlevelId)
    Game.WorldDataManager:OnMapLoaded(CurlevelId)
	
    --私有懒加载场景物体
	-- todo 需要做C++ 一次黑盒化 @胡江龙
    if Game.me and Game.me.bLazyLoadTest then
        local _LoadGroupList = Game.WorldDataManager:RequireLoadGroupNames(CurlevelId, Game.WorldDataManager.CurPlaneID)
        local _LoadGroupMap = {}
        if _LoadGroupList ~= nil then
            for _, Group in ksbcpairs(_LoadGroupList) do
                _LoadGroupMap[Group] = true
            end
        end
        local _SceneActorDataMap = Game.WorldDataManager:GetSceneActorDataByLevelID(CurlevelId)
        if _SceneActorDataMap then
           for _ID, _SceneActorData in pairs(_SceneActorDataMap) do
                if _SceneActorData.LayerName and not _LoadGroupMap[_SceneActorData.LayerName] then
                    goto continue
                end
                --前后端统一的过滤条件
                if _SceneActorData.ActorType and _SceneActorData.ActorType == 40110 then

                end

                if CheckSceneActorDefaultSpawn(_SceneActorData, Game.me.PersistentPIAInfo) then
                    Game.LSceneActorEntityManager:Client_ReqLoadSceneActor(_SceneActorData, self.ECreateSourceType.Server_LazyLoad)
                end
               ::continue::
           end
        end
    end
end

-- 共有物创建，如果有特定的condition，则不一定成功，直到满足条件才创建
function LSceneActorEntityManager:OnCreateServerSceneActor(SEntity, bCheckConditionTrigger)
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(SEntity.InsID)
    if not SceneActorData then
		if SEntity.Summoner and SEntity.Summoner == "GM" then
			SceneActorData = Game.WorldDataManager:GetSceneActorDataByInsID(SEntity.InsID)
		end
		--TODO: 根据ActorType，限制只有白名单类的ActorType才能AddTemplateSceneActorData
        if not SceneActorData and SEntity.TemplateID and SEntity.ActorType then
			SceneActorData = Game.WorldDataManager:AddTemplateSceneActorDataInCurLevel(SEntity.InsID, SEntity)
        end
    end

	if not SceneActorData then
		Log.ErrorFormat("OnCreateServerSceneActor can not found SceneActor Data InsID:%s, LevelID:%s, PlaneID:%s", SEntity.InsID, Game.WorldDataManager.CurLoadLevelID, Game.WorldDataManager.CurPlaneID)
		return
	end

    Log.Debug("LSceneActorEntityManager:OnCreateServerSceneActor, ", SEntity.InsID, SEntity.ActorType, SceneActorData.ID, SceneActorData.Class)

    -- 检查并添加通用条件生成触发器，如果本身是满足条件的回调来的则不再判断
    if bCheckConditionTrigger then
        if false == self:CheckAndAddTriggerCondition(SEntity.InsID, SEntity.ActorType, SceneActorData, SEntity, self.ECreateSourceType.Server_Public, true) then
            return
        end
    end

    return self:CreateLSceneActor(SEntity.InsID, SEntity.ActorType, SceneActorData, SEntity, self.ECreateSourceType.Server_Public)
end

--服务器删除，如果本身是条件失败来的，则不删除trigger，因为trigger可能再次满足触发
function LSceneActorEntityManager:OnDeleteServerSceneActor(SEntity, bRemoveConditionTrigger)
    -- 如果本身是
    if bRemoveConditionTrigger then
        --清理通用条件生成触发器
        self:RemoveTriggerCondition(SEntity, SEntity.InsID, SEntity.ActorType, self.ECreateSourceType.Server_Public)
    end
    local _LSceneActor = self.SEntityID_LSceneActorMap[SEntity:uid()]
    if _LSceneActor then
        Game.LSceneActorEntityManager:RemoveEntity(SEntity.InsID)
    end

    self.SEntityID_LSceneActorMap[SEntity:uid()] = nil
    self.InsID_SEntityIDMap[SEntity.InsID] = nil
end

--本地场景数据创建
function LSceneActorEntityManager:Client_ReqLoadSceneActor(SceneActorData, InSourceType)
    if SceneActorData == nil or SceneActorData.ID == nil then
        return nil
    end

    return self:ReqLoadSceneActor(SceneActorData, InSourceType)
end

--本地场景数据删除
function LSceneActorEntityManager:Client_ReqDeleteSceneActor(InID, bExitWorld, bSceneUnLoad)
    if InID == nil then
        return nil
    end

    local _LSceneActor = self.InsID_LSceneActorMap[InID]
    if _LSceneActor then
        Game.LSceneActorEntityManager:RemoveEntity(InID, bExitWorld, bSceneUnLoad)
    end
end

--地图资源对象控制添加 todo 待废弃
function LSceneActorEntityManager:CreateEntityDrivenBySceneActorRegister(ActorInstanceID, ActorType, PosX, PosY, PosZ, Pitch, Yaw, Roll)
    -- 8月版本临时处理 后续ExitWorld流程需要梳理
    if LSceneActorEntityManager.ActorTypeDefaultMap[ActorType] then
        local _SceneActorData = {
            ID = tostring(ActorInstanceID),
            ActorType = ActorType,
            Transform = {
                Position = { X = PosX, Y = PosY, Z = PosZ },
                Rotator = { Pitch = Pitch, Yaw = Yaw, Roll = Roll }
            },
            LoadFromMap = true
        }
        self:Client_ReqLoadSceneActor(_SceneActorData, self.ECreateSourceType.Local_Scene)
    end
end

--地图资源对象控制删除
function LSceneActorEntityManager:OnSceneActorUnRegister(ActorInstanceID)
	self:RemoveEntityImmediately(tostring(ActorInstanceID))
end

--服务器私有创建 SceneActorDataList SpaceInteractorInfo
function LSceneActorEntityManager:OnReceive_PrivateSceneActorData(SceneActorDataList)
    for _ID, _SEntity in pairs(SceneActorDataList) do
        self:Inner_AddPrivateSceneActorData(_ID, _SEntity, self.ECreateSourceType.Server_Private)
    end
end

--添加单个私有场景物
function LSceneActorEntityManager:AddLocalPrivateSceneActorData(PrivateSceneActorData)
    return self:Inner_AddPrivateSceneActorData(PrivateSceneActorData.InsID, PrivateSceneActorData, self.ECreateSourceType.Client_Private)
end

--删除单个私有场景物
function LSceneActorEntityManager:DeleteLocalPrivateSceneActorData(InsID)
    self:Inner_DeletePrivateSceneActorData(InsID)
end


function LSceneActorEntityManager:Inner_AddPrivateSceneActorData(_ID, _SEntity, InSourceType)
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(_ID)
    if not SceneActorData then
        if _SEntity.TemplateID and _SEntity.ActorType then
			SceneActorData = Game.WorldDataManager:AddTemplateSceneActorDataInCurLevel(_ID, _SEntity)
        end
    end

    if not SceneActorData then
        return
    end
    
    local _ActorType = _SEntity.ActorType
    if _ActorType == nil and SceneActorData then
        _ActorType = SceneActorData.ActorType
    end

    return self:CreateLSceneActor(_ID, _ActorType, SceneActorData, _SEntity, InSourceType)
end

function LSceneActorEntityManager:Inner_DeletePrivateSceneActorData(_ID)
    --删除本地推送
    self:RemovePrivateSceneActorAoiData(tonumber(_ID))

    self:OnDelete_PrivateSceneActor(_ID)
end

--私有删除 DeleteMap DictStrStr
function LSceneActorEntityManager:OnDelete_PrivateSceneActorData(DeleteMap)
    for _ID, _ in pairs(DeleteMap) do
        self:Inner_DeletePrivateSceneActorData(_ID)
    end
end

function LSceneActorEntityManager:OnDelete_PrivateSceneActor(UID)
    local _LSceneActor = self.InsID_LSceneActorMap[UID]
    if _LSceneActor then
        self:RemoveEntity(UID)
    end
end

function LSceneActorEntityManager:CreateLSceneActor(InsID, ActorType, SceneActorData, SEntity, InSourceType, bClientAoi)
    local LSceneActorClass = LSceneActorEntityManager.LocalSceneActorEntityClass[ActorType]
	-- ECA部分迁移中, 会出现漏数据的问题, 暂时警告处理即可
	if LSceneActorClass == nil then
		Log.WarningFormat("[LSceneActorEntityManager:CreateLSceneActor] Unexpected ActorType:%s From Server,  InsID:%s,  ClassName:%s", ActorType, InsID, SEntity and SEntity.__cname)
		return 
	end
	
	local Position = {}
	local Rotation = {}
	if SEntity and SEntity.Position then
		Position[1] = SEntity.Position[1]
		Position[2] = SEntity.Position[2]
		Position[3] = SEntity.Position[3]
	elseif SceneActorData then
		Position[1] = SceneActorData["Transform"]["Position"].X
		Position[2] = SceneActorData["Transform"]["Position"].Y
		Position[3] = SceneActorData["Transform"]["Position"].Z
	end

	if SEntity and SEntity.Rotation then
		Rotation[1] = SEntity.Rotation[1]
		Rotation[2] = SEntity.Rotation[2]
		Rotation[3] = SEntity.Rotation[3]
	elseif SceneActorData then
		Rotation[1] = SceneActorData["Transform"]["Rotator"].Pitch
		Rotation[2] = SceneActorData["Transform"]["Rotator"].Yaw
		Rotation[3] = SceneActorData["Transform"]["Rotator"].Roll
	end

    -- 懒加载没有服务器的数据，这里需要给一个默认
    if InSourceType == self.ECreateSourceType.Server_LazyLoad then
        if SEntity == nil then
            SEntity = {
                SceneActorState = GetSceneActorInitialState(SceneActorData, Game.WorldDataManager.CurPlaneID),
                SubState = GetSceneActorInitialSubState(SceneActorData, Game.WorldDataManager.CurPlaneID)
            }
        end
    end
	
	if LSceneActorEntityManager.IgnoreAOIWhitelist[ActorType] == nil then
		if self.EnablePrivateSceneActorAoi and bClientAoi == nil and 
			(InSourceType == self.ECreateSourceType.Server_Private or InSourceType == self.ECreateSourceType.Server_LazyLoad) then
			local _PrivateSceneData = {InsID=InsID, ActorType=ActorType, SceneActorData=SceneActorData, SEntity=SEntity, SourceType=InSourceType}
			-- todo 这里理论上是九宫格能够支持bind actor和create actor的流程,  但是这里看起来都是Bind流程 @胡江龙
			self:AddPrivateSceneActorAoiData(tonumber(InsID), ActorType, Position, Rotation, _PrivateSceneData)
			return
		end
	end

	local _LSceneActorProps = { InsID = InsID,
								ActorType = ActorType,
								SceneConf = SceneActorData,
								Data = SEntity,
								Position = Position,
								Rotation = Rotation, }
    
    if SEntity and SEntity.OverrideConf then
        _LSceneActorProps.bSeverControlVisible = SEntity.OverrideConf.bSeverControlVisible
    end
    
	local _NewLSceneActor =  Game.EntityManager:CreateLocalEntity(LSceneActorClass.__cname, _LSceneActorProps)
	if _NewLSceneActor then
		
		--todo 这里是不同Entity绑定了同一个Actor，大概率是哪里的创建流程冲突了 <EMAIL>
		if self.InsID_LSceneActorMap[InsID] then 
			Log.WarningFormat("[LSceneActorEntityManager:CreateLSceneActor] InsID:%s already exist", InsID)
		end
		
		self.InsID_LSceneActorMap[InsID] = _NewLSceneActor 
		if InSourceType == self.ECreateSourceType.Server_Public then
			self.SEntityID_LSceneActorMap[SEntity:uid()] = _NewLSceneActor
			self.InsID_SEntityIDMap[InsID] = SEntity:uid()
		end
		
		-- todo SEntity取名不对，本地私有创建的也叫SEntity
		local SEntityUID = 0
		local SEntityClassName
		if SEntity then
			if SEntity.uid then
				SEntityUID = SEntity:uid()
			end
			SEntityClassName = SEntity.__cname
		end
		
		Log.DebugFormat(
			"[LSceneActorEntityManager:CreateLSceneActor] Create LocalSceneActor uid:%s, Server SceneActor uid:%s, ActorType:%s,  InsID:%s,  ClassName:%s",
			_NewLSceneActor:uid(), SEntityUID,  ActorType, InsID, SEntityClassName)
		
		return _NewLSceneActor
	else
		Log.ErrorFormat("LSceneActorEntityManager:CreateLSceneActor InsID %s ActorType:%s Error!", InsID, ActorType)
	end
end

function LSceneActorEntityManager:ReqLoadSceneActor(SceneActorData, InSourceType)
    if SceneActorData.Class == nil and SceneActorData.ActorType == nil then
        --Log.ErrorFormat("LSceneActorEntityManager:ReqLoadSceneActor SceneActorData.ID:%s Class Not Exist", SceneActorData.ID)
        return nil
    end

    if self.InsID_LSceneActorMap[SceneActorData.ID] then
        return nil
    end

    local ActorType = SceneActorData.ActorType or LSceneActorEntityManager.EPClassActorTypeMap[SceneActorData.Class]
    if ActorType then
        return self:CreateLSceneActor(SceneActorData.ID, ActorType, SceneActorData, nil, InSourceType)
    else
        --Log.ErrorFormat("LSceneActorEntityManager:ReqLoadSceneActor SceneActorData.Class:%s WactorType Not Exist", SceneActorData.Class)
    end

    return nil
end

function LSceneActorEntityManager:RemoveEntity(InsID)
    local _LSceneActor = self.InsID_LSceneActorMap[InsID]
    if _LSceneActor then
		-- 最多只有一个insID能够延迟摧毁
		local CannotDelayDelete = self.WaitDestroyIDMap[InsID] ~= nil

		if CannotDelayDelete then
			Log.WarningFormat("[LSceneActorEntityManager:RemoveEntity] Unpected lifetime management, try delay delete LocalSceneActor With Same InsID:%s", InsID)
		end
		
        if _LSceneActor:GetDelayDestroyTime() == 0 or CannotDelayDelete then
			_LSceneActor:destroy()
            self.InsID_LSceneActorMap[InsID] = nil
        else
		
			
            self.WaitDestroyIDMap[InsID] = _LSceneActor:GetDelayDestroyTime() + os.time()
			-- 从正常管理剥离到延时处理流程
			self.InsID_LSceneActorMap[InsID] = nil
			self.WaitDestroyLocalSceneActor[InsID] = _LSceneActor
            _LSceneActor:OnBeforeDelayDestroy()
            if self.WaitCtolTimerHandler == nil then
                self.WaitCtolTimerHandler = Game.TimerManager:CreateTimerAndStart(
                    function(DeltaTime)
                        if Game.LSceneActorEntityManager:CheckDelayDestroyLSceneActorQueue() then
                            Game.TimerManager:StopTimerAndKill(self.WaitCtolTimerHandler)
                        end
                    end,
                1000, -1)
            end
        end
    end
end

function LSceneActorEntityManager:RemoveEntityImmediately(InsID)
	local _LSceneActor = self.InsID_LSceneActorMap[InsID]
	if _LSceneActor and not _LSceneActor.isDestroyed then
		_LSceneActor:destroy()
	end
	self.InsID_LSceneActorMap[InsID] = nil
end

function LSceneActorEntityManager:CheckDelayDestroyLSceneActorQueue(IsForceClean)
    local _NowTime = os.time()
    for _ID, _DelayTime in pairs(self.WaitDestroyIDMap) do
        if _DelayTime <= _NowTime or IsForceClean == true then
			local localSceneActor = self.WaitDestroyLocalSceneActor[_ID]
			self.WaitDestroyIDMap[_ID] = nil
			self.WaitDestroyLocalSceneActor[_ID] = nil
			localSceneActor:destroy()
        end
    end

    for k, v in pairs(self.WaitDestroyIDMap) do
        return false
    end

    if self.WaitCtolTimerHandler then
        Game.TimerManager:StopTimerAndKill(self.WaitCtolTimerHandler)
        self.WaitCtolTimerHandler = nil
    end

    return true
end

function LSceneActorEntityManager:GetSceneActorBPClass(ActorType, EntityConfigData)
    local BPClassInfo = LSceneActorEntityManager.EPClassMap[ActorType]
    if not BPClassInfo then
        return nil
    end
    if EntityConfigData then
        local BPPathFormat = BPClassInfo[2]
        if BPPathFormat then
            return string.format(BPPathFormat, EntityConfigData.BPName, EntityConfigData.BPName)
        end
        local BPPath = BPClassInfo[3]
        if BPPath then
            return EntityConfigData["BPPath"]
        end
    end
    return BPClassInfo[1]
end

function LSceneActorEntityManager:ClearAllLSceneActor()
	-- 直接清理掉延时的部分
	self:CheckDelayDestroyLSceneActorQueue(true)
	
	-- 再处理掉正常的部分
    for InsID, _ in pairs(self.InsID_LSceneActorMap) do
        self:RemoveEntityImmediately(InsID)
    end
	
end

function LSceneActorEntityManager:TryUpdatePropertyWhenOutAOI(InsID, ChangeInfo)
    local NumericInsId = tonumber(InsID)
    if NumericInsId then
        local _PrivateData = self.PrivateSceneActorAoiData[NumericInsId]
        if _PrivateData then
            if _PrivateData.SEntity then
                table.merge(_PrivateData.SEntity, ChangeInfo)
            end
        end
    end
end

--私有物本地AOI---Begin
function LSceneActorEntityManager:OnPrivateSceneActorAoiChanged(UID, bEnterSpace)
    local _PrivateData = self.PrivateSceneActorAoiData[UID]
    if _PrivateData then
        if bEnterSpace then
            -- NOTE 
            -- 1. 场编数据的导出的InsID都是string，所以脚本层的InsID是string来索引的（第一版场编的InsID是带字母的，所以InsID的string格式保留了下来）
            -- 2. C++层的空间管理为了穿透效率使用int64来索引
            --	    local UID = tonumber(InsID) 将场编的InsID转换成了int，塞到了c++的空间管理中
            -- 3. 所以这里需要tostring一次，才能在脚本层索引到
            local _LSceneActor = self:GetLSceneActorFromInsID(tostring(UID))
            if _LSceneActor == nil then
                self:CreateLSceneActor(_PrivateData.InsID, _PrivateData.ActorType, _PrivateData.SceneActorData,
                    _PrivateData.SEntity, _PrivateData.SourceType, true)
            end
        else
            self:OnDelete_PrivateSceneActor(_PrivateData.InsID)
        end
    end
end

function LSceneActorEntityManager:AddPrivateSceneActorAoiData(UID, Type, Pos, Dir, PrivateSceneActorData)
    self.PrivateSceneActorAoiData[UID] = PrivateSceneActorData

    self.FVector_Temp.X = Pos[1]
    self.FVector_Temp.Y = Pos[2]
    self.FVector_Temp.Z = Pos[3]

    self.FRotator_Temp.Roll = Dir[2]
    self.FRotator_Temp.Pitch = Dir[1]
    self.FRotator_Temp.Yaw = Dir[3]

    local AoiRadius = nil
    if PrivateSceneActorData.SceneActorData then
        AoiRadius = PrivateSceneActorData.SceneActorData.AoiRadius
    end
	
	-- 做一个默认
	if AoiRadius == nil then
		AoiRadius = WorldViewConst.DEFAULT_WORLD_GRID_AOI_RADIUS 
	end

	Game.WorldManager:AddStaticSceneObjectToClientAOI(UID, Pos[1],  Pos[2],  Pos[3], AoiRadius)
end

function LSceneActorEntityManager:RemovePrivateSceneActorAoiData(UID)
    self.PrivateSceneActorAoiData[UID] = nil
	Game.WorldManager:RemoveStaticSceneObjectToClientAOI(UID)
end
--私有物本地AOI---End

---场景通用条件触发生成 Begin------------------

function LSceneActorEntityManager:GetSceneActorTriggerCondition(WActorType, SceneActorData, Data)
    local ConditionID = nil
    local bLoadAfterMeetCondition = false

    if SceneActorData and SceneActorData.SceneActorCommon and SceneActorData.SceneActorCommon.EnableConditionID then
        ConditionID = SceneActorData.SceneActorCommon.EnableConditionID
        -- 只有满足condition才能出生
        bLoadAfterMeetCondition = true
        return ConditionID, bLoadAfterMeetCondition
    end

    if WActorType == EWActorType.COLLECTION and Data.TemplateID then
        local TaskCollectData = Game.TableData.GetSceneActorTaskCollectDataRow(Data.TemplateID)
        if not TaskCollectData or not TaskCollectData.ConditionID then
            return ConditionID, bLoadAfterMeetCondition
        end
        
        if TaskCollectData.SpiritualVisionShowRule and TaskCollectData.SpiritualVisionShowRule ~= Enum.ESpiritualVisionShowRule.Always then
            -- todo 优先级可能有问题，待策划确认
            -- 带有灵视控制，则condition无效
            ConditionID = nil
            bLoadAfterMeetCondition = false
        else
            -- 显示规则为满足条件时显示，直到满足条件才显示
            ConditionID = TaskCollectData.ConditionID
            bLoadAfterMeetCondition = TaskCollectData.ShowModelRule == Enum.ETaskCollectShowRule.DependsCollectable
        end
    end

    return ConditionID, bLoadAfterMeetCondition
end

function LSceneActorEntityManager:CheckMeetTriggerCondition(InsID, WActorType, SceneActorData, Data)
    local ConditionID, _ = self:GetSceneActorTriggerCondition(WActorType, SceneActorData, Data)

    if ConditionID and ConditionID ~= 0 then
        return Game.TriggerTransitSystem:CheckSceneActorCondition(InsID, ConditionID, false)
    end

    return true
end


function LSceneActorEntityManager:CheckAndAddTriggerCondition(InsID, WActorType, SceneActorData, Data, InSourceType, bCreateCheck)
    if InSourceType and InSourceType ~= self.ECreateSourceType.Server_Public then
        return true
    end

    local ConditionID, bLoadAfterMeetCondition = self:GetSceneActorTriggerCondition(WActorType, SceneActorData, Data)
    Log.DebugFormat("LSceneActorEntityManager:CheckAndAddTriggerCondition InsID:%s, bCreateCheck:%s, ConditionID:%s, bLoadAfterMeetCondition:%s", InsID, bCreateCheck, ConditionID, bLoadAfterMeetCondition)
    if ConditionID and ConditionID ~= 0 then
        -- 出生检查时，若不依赖于条件控制是否出生，则直接返回并且LoadActor
        -- Entity EnterWorld之后，再注册条件判断，做其他业务逻辑（Condition控制是否可交互等）
        if bCreateCheck and not bLoadAfterMeetCondition then
            return true
        end

        -- 判断条件并且注册条件变化
        self.TriggerConditionSceneActorData[InsID] = {ActorType=WActorType, SceneActorData=SceneActorData, Data = Data, bLoadAfterMeetCondition=bLoadAfterMeetCondition}
        local bMeetCondition =  Game.TriggerTransitSystem:CheckSceneActorCondition(InsID, ConditionID, true, Game.LSceneActorEntityManager.OnTriggerConditionCallBack)
        Log.DebugFormat("LSceneActorEntityManager:CheckAndAddTriggerCondition InsID:%s, bMeetCondition:%s", InsID, bMeetCondition)
        return bMeetCondition
    end
    
    return true
end

function LSceneActorEntityManager:RemoveTriggerCondition(Data, InsID, ActorType, InSourceType)
    if InSourceType ~= self.ECreateSourceType.Server_Public then
        return
    end
    
    local _TriggerConditionData = self.TriggerConditionSceneActorData[InsID]
    if _TriggerConditionData then
        Game.TriggerTransitSystem:UnregisterSceneActorCondition(InsID)
        self.TriggerConditionSceneActorData[InsID] = nil
        Log.DebugFormat("LSceneActorEntityManager:RemoveTriggerCondition InsID:%s", InsID)
    end

end

function LSceneActorEntityManager.OnTriggerConditionCallBack(InsID, ConditionValue)
    Log.DebugFormat("LSceneActorEntityManager.OnTriggerConditionCallBack InsID:%s, ConditionValue:%s", InsID, ConditionValue)
    Game.LSceneActorEntityManager:InnerOnTriggerConditionCallBack(InsID, ConditionValue)
end

function LSceneActorEntityManager:InnerOnTriggerConditionCallBack(InsID, ConditionValue)
    if self:GetLSceneActorFromInsID(InsID) then
        self:CallFunc(InsID, "OnTriggerConditionCallBack", ConditionValue)
    end
    
    local TriggerConditionData = self.TriggerConditionSceneActorData[InsID]
    if TriggerConditionData and TriggerConditionData.bLoadAfterMeetCondition then
        -- 条件控制出生
        if ConditionValue then
            self:OnCreateServerSceneActor(TriggerConditionData.Data, false)
        else
            self:OnDeleteServerSceneActor(TriggerConditionData.Data, false)
        end
    end
end


---场景通用条件触发生成 End------------------

return LSceneActorEntityManager