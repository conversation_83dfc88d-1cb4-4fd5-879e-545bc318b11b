kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_ClockLamp
local LSAE_ClockLamp = DefineLocalEntity("LSAE_ClockLamp", LSceneActorEntityBase,
    {
        ViewControlAudioComponent,
    }
)

LSAE_ClockLamp:Register(EWActorType.CLOCK_LAMP)

function LSAE_ClockLamp:ctor()
    self.ExcelData = Game.TableData.GetSceneActorClockDataRow(self.SceneConf.TemplateID)
    self.ClockTimer = nil
    self.RotatorCache = FRotator(0, 0, 0)
    self.RecentAlarmHour = 0
    self.HourlyAlarmAK = self.ExcelData.HourlyAlarmAK
    self.HourlyAlarmHours = {}
    if self.ExcelData.HourlyAlarmHours then
        for _, Hour in ksbcipairs(self.ExcelData.HourlyAlarmHours) do
            self.HourlyAlarmHours[Hour] = true
        end
    end
end

function LSAE_ClockLamp:AfterEnterWorld()
    self:OnInitAppearance()
end

function LSAE_ClockLamp:BeforeExitWorld()
    if self.ClockTimer then
        Game.TimerManager:StopTimerAndKill(self.ClockTimer)
        self.ClockTimer = nil
    end
end

function LSAE_ClockLamp:OnInitAppearance()
    local compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "Hour")
    if IsValidID(compID) then
        self.CppEntity:KAPI_SceneID_SetVisibility(compID, true, false)
    end
    compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "Minute")
    if IsValidID(compID) then
        self.CppEntity:KAPI_SceneID_SetVisibility(compID, true, false)
    end

    compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "MinuteBack")
    if IsValidID(compID) then
        self.CppEntity:KAPI_SceneID_SetVisibility(compID, true, false)
    end
    compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "HourBack")
    if IsValidID(compID) then
        self.CppEntity:KAPI_SceneID_SetVisibility(compID, true, false)
    end

    
    self:RefreshClockTime()
    -- 时钟需求要被干了，这个timer报错就先不要了
    --self.ClockTimer = Game.TimerManager:CreateTimerAndStart(
    --        function()
    --            self:RefreshClockTime()
    --        end,
    --        1000,
    --        -1
    --)
end

function LSAE_ClockLamp:RefreshClockTime()
    local nowTable = Game.ClimateSystem:GetGameTime()
    local rotator = self.RotatorCache

    local compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "Hour")
    if IsValidID(compID) then
        local Hour = nowTable.hour + (nowTable.min / 60.0)
        local HourDegree = 360 - 30 * Hour
        rotator.Pitch = HourDegree
        self.CppEntity:KAPI_SceneID_SetRelativeRotation(compID, rotator.Pitch, rotator.Roll, rotator.Yaw)
    end

    compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "Minute")
    if IsValidID(compID) then
        local Minute = nowTable.min
        local MinuteDegree = -6 * Minute
        rotator.Pitch = MinuteDegree
        self.CppEntity:KAPI_SceneID_SetRelativeRotation(compID, rotator.Pitch, rotator.Roll, rotator.Yaw)
    end

    compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "HourBack")
    if IsValidID(compID) then
        local Hour = nowTable.hour + (nowTable.min / 60.0)
        local HourDegree = - 360 - 30 * Hour 
        --self.RotatorCache.Pitch = HourDegree
        self.CppEntity:KAPI_SceneID_SetRelativeRotation(compID, HourDegree, 0, -180)
    end

    compID = self.CppEntity:KAPI_Actor_GetComponentByClassNameAndTag("StaticMeshComponent", "MinuteBack")
    if IsValidID(compID) then
        local Minute = nowTable.min
        local MinuteDegree = -6 * Minute
        --self.RotatorCache.Pitch = MinuteDegree
        self.CppEntity:KAPI_SceneID_SetRelativeRotation(compID, MinuteDegree, 0, -180)
    end

    if self.HourlyAlarmAK ~= "" then
        if nowTable.min == 0 then
            if self.RecentAlarmHour ~= nowTable.hour then
                if self.HourlyAlarmHours[nowTable.hour] then
                    self:AkPostEventOnActor(self.HourlyAlarmAK)
                    self.RecentAlarmHour = nowTable.hour
                end
            end
        end
    end
    
end

return LSAE_ClockLamp