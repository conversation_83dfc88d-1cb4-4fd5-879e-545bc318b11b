local sceneObjectBase = kg_require("Gameplay.NetEntities.SceneActor.SceneObjectBase")
local EMIDCreationFlags = import("EMIDCreationFlags")
---@class SceneText
local SceneText = DefineLocalEntity("SceneText", sceneObjectBase.SceneObjectBase)

SceneText:Register(EWActorType.SCENE_TEXT)

SceneText.EffectMaterialPath = "/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B"

SceneText.EFadeEffectType = {
    None = 0,
    FadeInAndOut = 1, --淡入淡出
    FloatInAndOut = 2, --浮入浮出
    LeftToRight = 3, --从左到右逐行
    Erase = 4, --擦除
    Printer = 5, --打字机
}

SceneText.EGeneralEffectType = {
    PartialTransparency = 0, --局部半透效果
    BounceUpDown = 1, --上下浮动
}

SceneText.FadeInOffset_FadeInAndOut = FLinearColor(0, 0, 0, 0)
SceneText.FadeInOffset_FloatInAndOut = FLinearColor(0, 25, 0, 0)

function SceneText:Init()
    self.triggerInsID = nil
    self.dynamicMaterialID = nil
    self.materialRequestID = nil
    self.textLineTimer = nil
    self.lastShowStatus = nil
    self.materialUpdateID = nil
    self.bShouldShow = self.SceneConf.ShowInGame
    self.fadeInDuration = self.SceneConf.TextInfo.FadeInEffect.TransitionDuration
    self.fadeOutDuration = self.SceneConf.TextInfo.FadeOutEffect.TransitionDuration
end

function SceneText:DoGameplayAssetCollect()
    self.materialRequestID = self:SingleRequestAssetBeforeLoadActor(SceneText.EffectMaterialPath)
end

function SceneText:AfterEnterWorld()
    if not self.bShouldShow then
        return
    end
    local materialAssetID = self:GetGameplaySingleOrBatchAssetByRequestID(self.materialRequestID)
    self.dynamicMaterialID = LuaScriptAPI.CreateDynamicMaterialInstance(self.CharacterID, materialAssetID, "", EMIDCreationFlags.Transient)
    self.CppEntity:KAPI_Actor_UpdateFontMaterial(self.dynamicMaterialID)
    if next(self.OverrideConf) then
        self:AfterSetOverrideConf(self.OverrideConf)
    else
        self:RefreshWidget()
    end
end

function SceneText:BeforeExitWorld()
    self.widgetComp = nil
    self.dynamicMaterialID = nil

    if self.textLineTimer then
        Game.TimerManager:StopTimerAndKill(self.textLineTimer)
        self.textLineTimer = nil
    end
    if self.triggerInsID then
        Game.WorldManager:RemoveTrigger(self.triggerInsID)
        self.triggerInsID = nil
    end
    if self.materialUpdateID then
        Game.MaterialManager:RemoveMaterialParamUpdateTask(self.materialUpdateID)
        self.materialUpdateID = nil
    end
end

function SceneText:CheckInvisible(DissolveConfig)
	sceneObjectBase.SceneObjectBase.CheckInvisible(self, DissolveConfig)
	self:RefreshWidget()
end

function SceneText:IsShouldShowWidget()
    -- 1. 材质是否有
    if self.dynamicMaterialID == 0 or self.dynamicMaterialID == nil then
        return false
    end
    if self:IsInvisible() then
        return false
    end
    return self.bShouldShow
end

function SceneText:RefreshWidget()
    local bShouldShow = self:IsShouldShowWidget()
    if bShouldShow == self.lastShowStatus then
        return
    end
    self.lastShowStatus = bShouldShow
    if bShouldShow then
        self:ShowTextBlock()
        self:PlayEffectIn()
        self:PlayStartAudio()
    else
        self:PlayEffectOut()
        self:PlayEndAudio()
    end
end

function SceneText:ShowTextBlock()
    local transitionDuration = self.fadeOutDuration
    if transitionDuration and transitionDuration > 0 then
        self:SetDelayDestroy(transitionDuration)
    end
    self:RefreshContent()
    self.CppEntity:KAPI_Actor_ShowText(true)
    self:PlayEffectGeneral()
end

function SceneText:RefreshContent()
    local fontInfo = self.SceneConf.FontInfo
    if not fontInfo then return end
    if StringValid(fontInfo.Typeface) then
        self.CppEntity:KAPI_Actor_UpdateFontTypeFace(fontInfo.Typeface)
    end
    local textInfo = self.SceneConf.TextInfo
    local textColor = textInfo.TextColor
    self.CppEntity:KAPI_Actor_UpdateFontSize(fontInfo.Size)
    self.CppEntity:KAPI_Actor_SetDisplayText(textInfo.DisplayText)
    self.CppEntity:KAPI_Actor_SetDisplayTextColor(FLinearColor(textColor.R, textColor.G, textColor.B, textColor.A))
    self.CppEntity:KAPI_Actor_SetDisplayTextJustification(textInfo.Justification)
end

function SceneText:PlayEffectGeneral()
    if not self.bInWorld then return end

    local textInfo = self.SceneConf.TextInfo
    if textInfo.GeneralEffect then
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Idle_Transparent", 0)
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Idle_MoveIntensity", 0)
        for _, enum in ksbcipairs(textInfo.GeneralEffect) do
            if enum == SceneText.EGeneralEffectType.BounceUpDown then
                LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Idle_MoveIntensity", 4)
            elseif enum == SceneText.EGeneralEffectType.PartialTransparency then
                LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Idle_Transparent", 1)
            end
        end
    end
end

function SceneText:ResetSceneTextDefaultProgress(bIsOut)
    if not self.bInWorld then return end
    local value = bIsOut and 1 or 0
	LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Progress_FadeIn", value)
	LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Progress_LeftToRight", value)
	LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Progress_Erase", value)
    LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "Progress_Typewriter", value)
    LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "IsOut", value)
end

function SceneText:PlayEffectIn()
    local textInfo = self.SceneConf.TextInfo
    if not textInfo.FadeInEffect or self.dynamicMaterialID == 0 or self.dynamicMaterialID == nil then
        return
    end
    local fadeinType = textInfo.FadeInEffect.EffectType
    if fadeinType == SceneText.EFadeEffectType.None then return end
    self:ResetSceneTextDefaultProgress(false)
    local duration = math.max(0.1, self.fadeInDuration)
    if self.materialUpdateID then
        Game.MaterialManager:RemoveMaterialParamUpdateTask(self.materialUpdateID)
        self.materialUpdateID = nil
    end
    if fadeinType == SceneText.EFadeEffectType.FadeInAndOut then
        LuaScriptAPI.SetDynamicMaterialLinearColorParameterValue(self.dynamicMaterialID, "FadeInOffset", SceneText.FadeInOffset_FadeInAndOut)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_FadeIn", self.dynamicMaterialID, 0, 1, duration)
    elseif fadeinType == SceneText.EFadeEffectType.FloatInAndOut then
        LuaScriptAPI.SetDynamicMaterialLinearColorParameterValue(self.dynamicMaterialID, "FadeInOffset", SceneText.FadeInOffset_FloatInAndOut)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_FadeIn", self.dynamicMaterialID, 0, 1, duration)
    elseif fadeinType == SceneText.EFadeEffectType.LeftToRight then
        -- Slate排版是异步的，延迟一帧才能取到行数
        self.textLineTimer = Game.TimerManager:CreateTimerAndStart(function()
            LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "LineAmount", self.CppEntity:KAPI_Actor_GetTextLineNum())
            self.textLineTimer = nil
        end, 0, 1)
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "InvertDirection", 0)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_LeftToRight", self.dynamicMaterialID, 0, 1, duration)
    elseif fadeinType == SceneText.EFadeEffectType.Erase then
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "InvertDirection", 0)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_Erase", self.dynamicMaterialID, 0, 1, duration)
    elseif fadeinType == SceneText.EFadeEffectType.Printer then
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "TextLength", utf8.len(textInfo.DisplayText))
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_Typewriter", self.dynamicMaterialID, 0, 1, duration)
    end
end

function SceneText:PlayEffectOut()
    local textInfo = self.SceneConf.TextInfo
    if not textInfo.FadeOutEffect or self.dynamicMaterialID == 0 or self.dynamicMaterialID == nil then
        return
    end
    self:ResetSceneTextDefaultProgress(true)
    local fadeoutType = textInfo.FadeOutEffect.EffectType
    local duration = math.max(0.1, self.fadeOutDuration)
    if self.materialUpdateID then
        Game.MaterialManager:RemoveMaterialParamUpdateTask(self.materialUpdateID)
        self.materialUpdateID = nil
    end
    if fadeoutType == SceneText.EFadeEffectType.FadeInAndOut then
        LuaScriptAPI.SetDynamicMaterialLinearColorParameterValue(self.dynamicMaterialID, "FadeInOffset", SceneText.FadeInOffset_FadeInAndOut)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_FadeIn", self.dynamicMaterialID, 1, 0, duration)
    elseif fadeoutType == SceneText.EFadeEffectType.FloatInAndOut then
        LuaScriptAPI.SetDynamicMaterialLinearColorParameterValue(self.dynamicMaterialID, "FadeInOffset", SceneText.FadeInOffset_FloatInAndOut)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_FadeIn", self.dynamicMaterialID, 1, 0, duration)
    elseif fadeoutType == SceneText.EFadeEffectType.LeftToRight then
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "LineAmount", self.CppEntity:KAPI_Actor_GetTextLineNum())
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "InvertDirection", 1)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_LeftToRight", self.dynamicMaterialID, 1, 0, duration)
    elseif fadeoutType == SceneText.EFadeEffectType.Erase then
        LuaScriptAPI.SetDynamicMaterialScalarParameterValue(self.dynamicMaterialID, "InvertDirection", 1)
        self.materialUpdateID = Game.MaterialManager:AddLinearSampleParamByDynamicMaterialInstanceID("Progress_Erase", self.dynamicMaterialID, 1, 0, duration)
    end
end


function SceneText:OnSceneActorStateChanged(NewState, OldState)
    if not self.bShouldShow then
        return
    end
    self:DebugFmt("SceneText:OnSceneActorStateChanged InsID:%s, New:%s, OldState:%s", self.InsID, NewState, OldState)
    self:RefreshWidget()
end

function SceneText:OnBeforeDelayDestroy()
    if self.textLineTimer then
        Game.TimerManager:StopTimerAndKill(self.textLineTimer)
        self.textLineTimer = nil
    end
    self:PlayEffectOut()
    if self.triggerInsID then
        Game.WorldManager:RemoveTrigger(self.triggerInsID)
        self.triggerInsID = nil
    end
end

function SceneText:AfterSetOverrideConf(OverrideConf)
    if OverrideConf.IsFadeIn ~= nil then
        self.bShouldShow = OverrideConf.IsFadeIn
    end

    if OverrideConf.TransitionDuration ~= nil then
        if self.bShouldShow then
            self.fadeInDuration = OverrideConf.TransitionDuration
        else
            self.fadeOutDuration = OverrideConf.TransitionDuration
        end
    end
    self:RefreshWidget()
end

function SceneText:OnEnterTrigger()
    self.bInTrigger = true
    self:RefreshWidget()
end

function SceneText:OnLeaveTrigger()
    self.bInTrigger = false
    self:RefreshWidget()
end

function SceneText:OnTriggerStateChanged(triggerID, bEnter)
    self:Debug(">>SceneText OnTriggerStateChanged ", triggerID, bEnter)
    self.bInTrigger = bEnter
    self:RefreshWidget()
end

function SceneText:PlayStartAudio()
    if Game.SceneTextSystem:IsSceneTextStartAudioPlayed(self.InsID) then
        self:PlayLoopAudio()
        return
    end
    self:Debug("SceneText:PlayStartAudio", self.InsID)
    local eventName
    if self.SceneConf.StartAudioEvent and self.SceneConf.StartAudioEvent ~= "" then
        local arr = string.split(self.SceneConf.StartAudioEvent, ".")
        eventName = arr[#arr]
    end
    if (eventName == nil) or (eventName == "") then
        self:PlayLoopAudio()
        return
    end
    self:AkPostEvent3D(eventName, self.SceneConf.Transform.Position)
    Game.SceneTextSystem:SetSceneTextStartAudioPlayed(self.InsID)

    local duration = Game.AkAudioManager:GetEventDuration(eventName)
    if duration > 0 then
        Game.TimerManager:CreateTimerAndStart(function()
            self:PlayLoopAudio()
        end, duration * 1000, 1)
    end
end

function SceneText:PlayEndAudio()
    if self.loopAudioID then
        self:AkStopEvent(self.loopAudioID)
        self.loopAudioID = nil
    end
    if Game.SceneTextSystem:IsSceneTextEndAudioPlayed(self.InsID) then
        return
    end
    self:Debug("SceneText:PlayEndAudio", self.InsID)
    if self.SceneConf.EndAudioEvent and self.SceneConf.EndAudioEvent ~= "" then
        local arr = string.split(self.SceneConf.EndAudioEvent, ".")
        local eventName = arr[#arr]
        if (eventName == nil) or (eventName == "") then
            return
        end
        self:AkPostEvent3D(eventName, self.SceneConf.Transform.Position)
    end
    Game.SceneTextSystem:SetSceneTextEndAudioPlayed(self.InsID)
end

function SceneText:PlayLoopAudio()
    if not self.bInWorld then return end
    if self.SceneConf.LoopAudioEvent and self.SceneConf.LoopAudioEvent ~= "" then
        local arr = string.split(self.SceneConf.LoopAudioEvent, ".")
        local eventName = arr[#arr]
        if (eventName == nil) or (eventName == "") then
            return
        end
        self.loopAudioID = self:AkPostEvent3D(eventName, self.SceneConf.Transform.Position)
    end
end

return SceneText
