kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")
local EPropertyClass = import("EPropertyClass")
local ESplinePointType = import("ESplinePointType")
local NiagaraComponent = import("NiagaraComponent")
local SplineComponent = import("SplineComponent")

---@class LSAE_SpiritEffectLine : LSceneActorEntityBase
local LSAE_SpiritEffectLine = DefineLocalEntity("LSAE_SpiritEffectLine", LSceneActorEntityBase, {
	SpiritualVisionSceneActorComponent
})
LSAE_SpiritEffectLine:Register(EWActorType.SPIRIT_EFFECT_LINE)

LSAE_SpiritEffectLine.EmptyVector = FVector()

function LSAE_SpiritEffectLine:AfterEnterWorld()
	
    local splineCompID = self.CppEntity:KAPI_Actor_GetComponentByClass(SplineComponent)
	if splineCompID == 0 then
		return
	end

	local SplinePoints = slua.Array(EPropertyClass.Struct, import("Vector"))
	local SplineRotations = slua.Array(EPropertyClass.Struct, import("Rotator"))
	for _, PointInfo in ksbcipairs(self.SceneConf.WayPointListV2) do
		SplinePoints:Add(FVector(PointInfo.Position.X, PointInfo.Position.Y, PointInfo.Position.Z + 20))
		SplineRotations:Add(PointInfo.Rotator)
	end
	self.CppEntity:KAPI_Spline_ClearSplinePoints(false)
	self.CppEntity:KAPI_SplineID_AddSplinePoints(splineCompID, SplinePoints, SplineRotations, 0, false, ESplinePointType.CurveCustomTangent)
	
	local startPos = self.SceneConf.WayPointListV2[1] and self.SceneConf.WayPointListV2[1].Position or LSAE_SpiritEffectLine.EmptyVector
	local startRelativePos = startPos - self.SceneConf.Transform.Position
	local startNiagaraCompIDs = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "Start")
	for _, compID in ipairs(startNiagaraCompIDs) do
		self.CppEntity:KAPI_SceneID_SetRelativeLocation(compID, startRelativePos.X, startRelativePos.Y, startRelativePos.Z, false, nil, false)
		self.CppEntity:KAPI_NiagaraID_Deactivate(compID)
	end

	local endPos = self.SceneConf.WayPointListV2[#self.SceneConf.WayPointListV2] and self.SceneConf.WayPointListV2[#self.SceneConf.WayPointListV2].Position or LSAE_SpiritEffectLine.EmptyVector
	local endRelativePos = endPos - self.SceneConf.Transform.Position
	local endNiagaraCompIDs = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "End")
	for _, compID in ipairs(endNiagaraCompIDs) do
		self.CppEntity:KAPI_SceneID_SetRelativeLocation(compID, endRelativePos.X, endRelativePos.Y, endRelativePos.Z, false, nil, false)
		self.CppEntity:KAPI_NiagaraID_Deactivate(compID)
	end

	local lineNiagaraCompIDs = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "Line")
	for _, compID in ipairs(lineNiagaraCompIDs) do
		self.CppEntity:KAPI_NiagaraID_SetVariableFloat(compID, "RibbonSpawn", self.SceneConf.RibbonSpawn)
		self.CppEntity:KAPI_NiagaraID_SetVariableFloat(compID, "Sprite1Spawn", self.SceneConf.Sprite1Spawn)
		self.CppEntity:KAPI_NiagaraID_SetVariableFloat(compID, "Sprite2Spawn", self.SceneConf.Sprite2Spawn)
		self.CppEntity:KAPI_NiagaraID_Deactivate(compID)
	end
end

function LSAE_SpiritEffectLine:BeforeExitWorld()
	Game.EventSystem:RemoveObjListeners(self)
end

--@override SpiritualVisionViewBaseComponent
function LSAE_SpiritEffectLine:SwitchSpiritualShow(bIsShow)
	local niagaraCompIDs = self.CppEntity:KAPI_Actor_K2_GetComponentsByClass(NiagaraComponent)
	for _, niagaraCompID in pairs(niagaraCompIDs) do
		self.CppEntity:KAPI_Component_SetActive(niagaraCompID, bIsShow, false)
	end
end


return LSAE_SpiritEffectLine

