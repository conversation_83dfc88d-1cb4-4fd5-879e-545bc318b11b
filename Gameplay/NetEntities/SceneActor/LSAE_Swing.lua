kg_require("Gameplay.NetEntities.SceneActor.LSAE_ContinuousActorBase")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")

---@class LSAE_Swing
local LSAE_Swing = DefineLocalEntity("LSAE_Swing", LSAE_ContinuousActorBase,
    {
		SimpleViewControlAnimComponent,
		ViewControlAnimLibComponent,
        ViewControlTriggerComponent
    }
)

LSAE_Swing:Register(EWActorType.INTERACTIVE_SWING)
LSAE_Swing.SeatRelativeQuat = FQuat(0, 0, 0.707107, 0.707107)

function LSAE_Swing:ctor()

end

--@override
function LSAE_Swing:InitFromExcel()
    self.ExcelData = Game.TableData.GetContinuousInteractiveSwingDataRow(self.TemplateID)
    self.UITemplateID = self.ExcelData.UITemplateID
    self.InteractorRadius = self.ExcelData.InteractorRadius

    local PerformanceData = Game.TableData.GetPerformanceActionDataRow(self.ExcelData.FemalePerformanceID)
    self.FemaleStartAnim = PerformanceData[1].Animation[1]
    self.FemaleLoopAnim = PerformanceData[1].Animation[2]
    self.FemaleEndAnim = PerformanceData[1].BackSwingAnimation[1]
    self.FemaleSwingStartAnim = PerformanceData[2].Animation[1]
    self.FemaleSwingLoopAnim = PerformanceData[2].Animation[2]
    self.FemaleSwingEndAnim = PerformanceData[2].BackSwingAnimation[1]
    
    PerformanceData = Game.TableData.GetPerformanceActionDataRow(self.ExcelData.MalePerformanceID)
    self.MaleStartAnim = PerformanceData[1].Animation[1]
    self.MaleLoopAnim = PerformanceData[1].Animation[2]
    self.MaleEndAnim = PerformanceData[1].BackSwingAnimation[1]
    self.MaleSwingStartAnim = PerformanceData[2].Animation[1]
    self.MaleSwingLoopAnim = PerformanceData[2].Animation[2]
    self.MaleSwingEndAnim = PerformanceData[2].BackSwingAnimation[1]
    
end

--@override
function LSAE_Swing:RefreshDetectTriggerState()
    if self:IsContinuousInteractiveAble() then
        self:EnableMainTriggerDetectable(true)
    else
        self:EnableMainTriggerDetectable(false)
    end
end

function LSAE_Swing:AfterEnterWorld()
    self:InitMainTriggerAsSphereAndBindEvent(self.ExcelData.InteractorRadius, self, "OnEnterBehaviorTrigger", "OnLeaveBehaviorTrigger")
    self:EnableMainTriggerCheckLineTracePlayer(-1)
    self:RefreshDetectTriggerState()
    LSAE_ContinuousActorBase.AfterEnterWorld(self)
end

--@override
function LSAE_Swing:GetContinuousInteractiveConf(Initiator)
    local Position = self:GetPosition()
    local Rotator = self:GetRotation()
    local WTransform = FTransform(Rotator:ToQuat(), Position)
    local OffsetY = self.ExcelData.Offset

    local RelativeLoc = FVector(0, OffsetY, 0)
    local RTransform = FTransform(LSAE_Swing.SeatRelativeQuat, RelativeLoc)
    local Location = WTransform:TransformPosition(RTransform:GetTranslation())
    local CapHalfHeight = Initiator.CppEntity:KAPI_Actor_GetScaledCapsuleHalfHeight() or 90
    Location.Z = Location.Z + CapHalfHeight + ViewControlConst.LOCO_DRIVE_ROLE_CAPSULE_Z_OFFSET

    local Rotation = WTransform:TransformRotation(RTransform:GetRotation())
    local StartTransform = FTransform(Rotation, Location)
    
    local StartAnim, LoopAnim, EndAnim, SwingStartAnim, SwingLoopAnim, SwingEndAnim =
    self.MaleStartAnim, self.MaleLoopAnim, self.MaleEndAnim, self.MaleSwingStartAnim, self.MaleSwingLoopAnim, self.MaleSwingEndAnim
    if Initiator:GetActorGender() == 1 then
        StartAnim, LoopAnim, EndAnim, SwingStartAnim, SwingLoopAnim, SwingEndAnim =
        self.FemaleStartAnim, self.FemaleLoopAnim, self.FemaleEndAnim, self.FemaleSwingStartAnim, self.FemaleSwingLoopAnim, self.FemaleSwingEndAnim
    end
    
    return {
        InsID = self.InsID,
        StartTransform = StartTransform,
        StartAnim = StartAnim,
        LoopAnim = LoopAnim,
        EndAnim = EndAnim,
        SceneActorStartAnim = SwingStartAnim,
        SceneActorLoopAnim = SwingLoopAnim,
        SceneActorEndAnim = SwingEndAnim,
    }
end

--@override
function LSAE_Swing:BuildStartBtn(bBuild)
    local bCurStartBuild = self.BuildBtnTaskID == self.UITemplateID
    if not Game.me:CanEnterContinuousInteract() or bCurStartBuild then
        return
    end

    Game.HUDInteractManager.BuildInteractorTask(self:uid(), { self.UITemplateID }, function()
        self:OnClickInteractiveBtn()
    end)
    self.BuildBtnTaskID = self.UITemplateID
end

function LSAE_Swing:GetEntityConfigData()
    return nil
end

function LSAE_Swing:CheckInvisible(DissolveConfig, bForceNotify)
    LSAE_ContinuousActorBase.CheckInvisible(self, DissolveConfig, bForceNotify)
end

return LSAE_Swing