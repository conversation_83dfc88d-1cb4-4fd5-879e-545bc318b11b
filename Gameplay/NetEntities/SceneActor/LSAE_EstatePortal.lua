kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_EstatePortal : LSceneActorEntityBase
local LSAE_EstatePortal = DefineLocalEntity("LSAE_EstatePortal", LSceneActorEntityBase,
	{
		ViewControlTriggerComponent,
		SceneActorBehaviorComponent,
	})

LSAE_EstatePortal:Register(EWActorType.ESTATE_PORTAL)

function LSAE_EstatePortal:SetOverrideBehaviorParams()
	self.InteractorRadius = self.SceneConf.Radius
end

function LSAE_EstatePortal:AfterEnterWorld()
	if self.SceneConf.Shape == 1 then
		self:ChangeMainBoxTrigger(self.SceneConf.BoxExtent.X, self.SceneConf.BoxExtent.Y, self.SceneConf.BoxExtent.Z)
	end
	if self.SceneConf.TriggerOffset then
		self:SetMainTriggerRelativeLocation(self.SceneConf.TriggerOffset.X, self.SceneConf.TriggerOffset.Y, self.SceneConf.TriggerOffset.Z)
	end
	if self.SceneConf.Mesh and self.SceneConf.Mesh ~= "" then
		self:DoAsyncLoadAsset(self.SceneConf.Mesh, "OnMeshLoaded")
	end
	if self.SceneConf.Material and self.SceneConf.Material ~= "" then
		self:DoAsyncLoadAsset(self.SceneConf.Material, "OnMaterialLoaded")
	end
end

function LSAE_EstatePortal:BeforeExitWorld()
end

function LSAE_EstatePortal:OnMeshLoaded(loadID, assetID)
	if assetID ~= 0 then
		local meshCompIDs = self.CppEntity:KAPI_Actor_GetComponentListByClassNameAndTag("StaticMeshComponent", "Portal")
		local CppEntity = self.CppEntity
		for _, meshCompID in ipairs(meshCompIDs) do
			CppEntity:KAPI_StaticMeshID_SetStaticMesh(meshCompID, assetID)
		end
	end
end

function LSAE_EstatePortal:OnMaterialLoaded(loadID, assetID)
	if assetID ~= 0 then
		local meshCompIDs = self.CppEntity:KAPI_Actor_GetComponentListByClassNameAndTag("StaticMeshComponent", "Portal")
		local CppEntity = self.CppEntity
		for _, meshCompID in ipairs(meshCompIDs) do
			CppEntity:KAPI_PrimitiveID_SetMaterial(meshCompID, 0, assetID)
		end
	end
end

function LSAE_EstatePortal:CallOnSuccessBehavior()
	Game.CameraManager:CachePlayerRotation()
end

return LSAE_EstatePortal