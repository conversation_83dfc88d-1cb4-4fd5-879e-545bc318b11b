kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")
local NiagaraComponent = import("NiagaraComponent")


---@class LSAE_PlanePortal
local LSAE_PlanePortal = DefineLocalEntity("LSAE_PlanePortal", LSceneActorEntityBase,
{
    ViewControlTriggerComponent,
    SceneActorBehaviorComponent,
}
)

LSAE_PlanePortal:Register(EWActorType.PLANE_PORTAL)

-- TODO: 配置化
LSAE_PlanePortal.DEFAULT_PORTAL_ID = 1
function LSAE_PlanePortal:ctor()
    self.LoadTasks = {}
    self.TargetPlaneID = 0
	self.QuestID = nil
	self.QuestTargetIndex = nil
    self.CachedTaskActive = nil
end

--@override
function LSAE_PlanePortal:SetOverrideBehaviorParams()
    local UITemplateID = self.SceneConf and self.SceneConf.UITemplateID
    self.TargetPlaneID = self.SceneConf and self.SceneConf.PlaneID or 0
    if self.Data and self.Data.OverrideConf then
        UITemplateID = self.Data.OverrideConf.UITemplate or UITemplateID
        self.TargetPlaneID = self.Data.OverrideConf.PlaneID or self.TargetPlaneID
		self.QuestID = self.Data.OverrideConf.QuestID
		self.QuestTargetIndex = self.Data.OverrideConf.TargetIndex
    end
	if self.QuestID == nil or self.QuestTargetIndex == nil then
		local _, QuestID, QuestTargetIndex = Game.QuestSystem:IsTransPortValid(self.InsID)
		self.QuestID = QuestID
		self.QuestTargetIndex = QuestTargetIndex
	end
	if self.QuestID and self.QuestTargetIndex then
		self.InteractorRadius = Game.QuestSystem:IsInteractorRadius(self.QuestID, self.QuestTargetIndex) or self.InteractorRadius
	end
	
    if UITemplateID and UITemplateID ~= 0 then
        self:OverrideTriggerUITemplateID(SceneActorFixedStateEnum.SA_ACTIVE, UITemplateID)
    end
	self:DebugFmt("LSAE_PlanePortal InsID:%s, PlaneID:%s, Quest:%s, TargetIndex:%s", self.InsID, self.TargetPlaneID, self.QuestID, self.QuestTargetIndex)
end

function LSAE_PlanePortal:AfterEnterWorld()
    self:RefreshPlaneActive()
	if self.QuestID then
		Game.GlobalEventSystem:AddListener(EEventTypesV2.QUEST_ON_PLANEPORTY_CHANGED, "OnPlanePortalChanged", self)
	end
	Game.UniqEventSystemMgr:AddListener(self.InsID, EEventTypesV2.QUEST_ON_PLANEVALID_CHANGED, "OnPlaneValidChanged", self)
	self:RefreshPortalNiagara()
end

function LSAE_PlanePortal:BeforeExitWorld()
    self:DestroyTraceIcon()
	Game.UniqEventSystemMgr:RemoveListener(self.InsID, EEventTypesV2.QUEST_ON_PLANEVALID_CHANGED, "OnPlaneValidChanged", self)
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function LSAE_PlanePortal:BuildTriggerStartHUDTask(TemplateID, bLockedState, extraTextList, titleTextList)
	if self.QuestID ~= nil and self.QuestTargetIndex ~= nil then
		local titleText = Game.QuestSystem:GetInteractorName(self.QuestID, self.QuestTargetIndex)
		titleTextList = {titleText}
	end
	SceneActorBehaviorComponent.BuildTriggerStartHUDTask(self, TemplateID, bLockedState, extraTextList, titleTextList)
end

function LSAE_PlanePortal:UnInit()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
end

function LSAE_PlanePortal:BuildTraceIcon()
    local rowData = Game.TableData.GetSceneActorPlanePortalDataRow(self.DEFAULT_PORTAL_ID)
    local TraceIcon = rowData and rowData.TraceIcon
    if rowData.TraceIcon and rowData.TraceIcon ~= 0 then
        self.TraceIcon = TraceIcon
        Game.HUDInteractManager:BuildInteractorTraceTask(self:uid(), TraceIcon, nil)
    end
end

function LSAE_PlanePortal:DestroyTraceIcon()
    if self.TraceIcon then
        self.TraceIcon = nil
        Game.HUDInteractManager:DestroyInteractorTraceTask(self:uid())
    end
end

function LSAE_PlanePortal:RefreshPlaneActive(bInActive)
    local bActive = bInActive
	if bActive == nil then
		bActive = self:IsTargetPlaneActive()
	end
    if self.CachedTaskActive == nil or self.CachedTaskActive ~= bActive then
        self.CachedTaskActive = bActive
        self:OnTargetPlaneActiveChanged(bActive)
    end
end

--@override
function LSAE_PlanePortal:PreTriggerCheck()
    return self:IsTargetPlaneActive()
end

function LSAE_PlanePortal:IsTargetPlaneActive()
	if self.QuestID then
		local bQuestActive = Game.QuestSystem:IsQuestAccepted(self.QuestID)
		self:DebugFmt("LSAE_PlanePortal InsID:%s, PlaneID:%s, Quest:%s, bQuestActive:%s", self.InsID, self.TargetPlaneID, self.QuestID, bQuestActive)
		return bQuestActive
	end
	local bPlaneActive = Game.me:IsPlaneActiveForPlanePortal(self.InsID)
	self:DebugFmt("LSAE_PlanePortal InsID:%s, PlaneID:%s, Quest:%s, bPlaneActive:%s", self.InsID, self.TargetPlaneID, self.QuestID, bPlaneActive)
	return bPlaneActive
end

function LSAE_PlanePortal:OnPlaneValidChanged(InsID)
	self:RefreshPlaneActive()
	local _, QuestID, QuestTargetIndex = Game.QuestSystem:IsTransPortValid(self.InsID)
	self.QuestID = QuestID
	self.QuestTargetIndex = QuestTargetIndex
end

function LSAE_PlanePortal:OnPlanePortalChanged(QuestID, bActive)
	if QuestID == self.QuestID then
		self:RefreshPlaneActive(bActive)
	end
end

function LSAE_PlanePortal:OnTargetPlaneActiveChanged(bActive)
    if bActive then
        self:DoInitBehavior()
        self:BuildTraceIcon()
        self:SetActorVisible(Enum.EInVisibleReasons.TaskCollectable)
    else
        self:DoUnInitBehavior()
        self:DestroyTraceIcon()
        self:SetActorInVisible(Enum.EInVisibleReasons.TaskCollectable)
    end
end

function LSAE_PlanePortal:RefreshPortalNiagara()
    if self.QuestID ~= nil and self.QuestTargetIndex ~= nil then
        local bShow = Game.QuestSystem:IsInteractorShowEffect(self.QuestID, self.QuestTargetIndex)
        if bShow ~= nil then
            local NiagaraCompID = self.CppEntity:KAPI_Actor_GetComponentByTag(NiagaraComponent, "PORTAL")
            if IsValidID(NiagaraCompID) then
                if bShow then
                    self.CppEntity:KAPI_NiagaraID_ReinitializeSystem(NiagaraCompID)
                    self.CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(NiagaraCompID, true)
                end
                self.CppEntity:KAPI_Component_SetActive(NiagaraCompID, bShow, true)
                self.CppEntity:KAPI_SceneID_SetVisibility(NiagaraCompID, bShow, true)
            end
        end
    end
end

return LSAE_PlanePortal