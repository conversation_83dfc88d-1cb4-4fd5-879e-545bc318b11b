kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_LoadSequence
local LSAE_LoadSequence = DefineLocalEntity("LSAE_LoadSequence", LSceneActorEntityBase,
{
    ViewControlTriggerComponent,
})

LSAE_LoadSequence:Register(EWActorType.LOAD_SEQUENCE)

function LSAE_LoadSequence:ctor()
    self.loadStartTime = 0
end

function LSAE_LoadSequence:AfterEnterWorld()
    if self.SceneConf.IsPreLoad then
        self.loadStartTime = _now()
        self:DoAsyncLoadAsset(self.SceneConf.SequencePath, "OnAsyncLoadFinish")
        if self.SceneConf.DialogueID and self.SceneConf.DialogueID > 0 then
            Game.DialogueManager:PreloadDialogue(self.SceneConf.DialogueID)
        end
    else
        local SequencePath = Game.TableData.GetLevelSequenceDataRow(self.SceneConf.SequenceID)
        if SequencePath then
            self:LoadSequence(SequencePath.AssetPath)
        end
    end
end

function LSAE_LoadSequence:BeforeExitWorld()
	local SequencePath = Game.TableData.GetLevelSequenceDataRow(self.SceneConf.SequenceID)
	if SequencePath then
		self:DestroySequence(SequencePath.AssetPath)
	end
end

function LSAE_LoadSequence:LoadSequence(assetPath)
    self:DebugFmt("[LoadSequence] assetPath=%s", assetPath)
    local Params = {
        AssetPath = assetPath,
        bLoop = self.SceneConf.bLoop,
        Transform = self.SceneConf.Transform,
        bUseTransformOriginActor = self.SceneConf.bUseTransformOriginActor,
    }
	-- todo 这里想办法做缓存机制 LevelSequenceManager加载完成后播放 先修版本问题
	if  Game.CinematicManager and Game.CinematicManager.LevelSequenceManager then
		Game.CinematicManager.LevelSequenceManager:PlayLevelSequence(Params)
	end
end

function LSAE_LoadSequence:DestroySequence(assetPath)
	self:DebugFmt("[DestroySequence] assetPath=%s", assetPath)
	if  Game.CinematicManager and Game.CinematicManager.LevelSequenceManager then
		Game.CinematicManager.LevelSequenceManager:StopLevelSequence(assetPath)
	end
end

function LSAE_LoadSequence:OnAsyncLoadFinish(loadID, loadedAssetID)
    if loadedAssetID == 0 then
        self:DebugFmt("[OnAsyncLoadFinish] %s load failed", self.SceneConf.SequencePath)
        return
    end

    local loadTime = (_now() - self.loadStartTime) / 1000
    self:DebugFmt("[OnAsyncLoadFinish] %s cost %ss to load", self.SceneConf.SequencePath, loadTime)
end

return LSAE_LoadSequence
