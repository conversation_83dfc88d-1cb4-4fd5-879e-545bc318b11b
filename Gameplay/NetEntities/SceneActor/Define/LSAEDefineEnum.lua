
--场景物体溶出类型
Enum.EDissolveOutType = {
    None = 0,
    Noise = 1,
	Noise_In = 2, --反向
}

--场景物体条件判断类型
Enum.ESceneActorConditionPassType = {
    And = 1,
    Or = 2,
}

-- 场景物体CD来源
Enum.ESceneActorCDType = {
    Interact = 1,
    ServerControl = 2,
}

-- 采集物 模型、名字、特效显示规则
Enum.ETaskCollectShowRule = {
    None = -1,
    AlwaysHide = 0,
    DependsCollectable = 1,
    AlwaysShow = 2,
}

-- 交互方向
Enum.EInteractPivotDirection = {
	Invalid = 0, -- 没有合法的交互点
	Any = 1,   -- 没有配置起始点，认为任意一个都可以
	Forward = 2, -- 前方
	Backward = 3, -- 后方
	Left = 4, -- 左边
	Right = 5, -- 右边
}

-- 交互位置
Enum.EInteractPivotLocation = {
	Invalid = 0, -- 没有合法的交互点
	Any = 1,   -- 没有配置起始点，认为任意一个都可以
	Forward = 2, -- 前方
	Left = 3, -- 左边
	Right = 4, -- 右边
}

--灵视显示规则
Enum.ESpiritualVisionShowRule = {
	Always = 0,
	OnlyInVision = 1,
	UseVision = 2,
}