kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local StaticMeshComponent = import("StaticMeshComponent")
---@class LSAE_Mirror : LSceneActorEntityBase

---@type LSAE_Mirror|SceneActorBehaviorComponent
local LSAE_Mirror = DefineLocalEntity("LSAE_Mirror", LSceneActorEntityBase, {
    SceneActorBehaviorComponent,
    ViewControlTriggerComponent
})

LSAE_Mirror:Register(EWActorType.MIRROR)

LSAE_Mirror.MaterialSlotName = "MI_Mirror01"
LSAE_Mirror.InactiveMaterialPath = ViewResourceConst.LSAE.Mirror_InactiveMaterialPath
LSAE_Mirror.ActiveMaterialPath = ViewResourceConst.LSAE.Mirror_ActiveMaterialPath

function LSAE_Mirror:ctor()
    self.activeMaterial = nil
    self.inactiveMaterial = nil
end

function LSAE_Mirror:UnInit()
end

function LSAE_Mirror:AfterEnterWorld()
    local resourceList = { self.ActiveMaterialPath, self.InactiveMaterialPath }
    self:DoAsyncLoadAssetList(resourceList, "OnResourceLoaded")
end

function LSAE_Mirror:BeforeExitWorld()
end

function LSAE_Mirror:OnSceneActorStateChanged(newState, oldState)
    self:switchMaterial(newState)
end

function LSAE_Mirror:OnResourceLoaded(loadID, assetIDs)
	if assets and assets:Num() == 2 then
		self.activeMaterial = assetIDs:Get(0)
		self.inactiveMaterial = assetIDs:Get(1)
		self:switchMaterial(self:GetSceneActorState())
	end
end

function LSAE_Mirror:switchMaterial(newState)
    if (self.activeMaterial == nil) or (self.inactiveMaterial == nil) then
        self:WarningFmt("[switchMaterial] %s get material failed", self.InsID)
        return
    end

    if not self.bInWorld then
        self:WarningFmt("[switchMaterial] %s not enter world", self.InsID)
        return
    end

    local componentID = self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
    -- 这里有点反直觉,因为这个场景物默认状态是 SA_ACTIVE(1)
    if newState == SceneActorFixedStateEnum.SA_ACTIVE then
		self.CppEntity:KAPI_PrimitiveID_SetMaterialByName(componentID, self.MaterialSlotName, self.activeMaterial)
    elseif newState == SceneActorFixedStateEnum.SA_INACTIVE then
		self.CppEntity:KAPI_PrimitiveID_SetMaterialByName(componentID, self.MaterialSlotName, self.inactiveMaterial)
    end
end

return LSAE_Mirror
