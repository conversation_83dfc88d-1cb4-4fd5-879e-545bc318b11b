local LocalEntityBase = kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase").LocalEntityBase
local ViewControlBaseComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlBaseComponent")
local ViewControlFxComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlFxComponent")
local ViewControlVisibleComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlVisibleComponent")
local ViewControlAudioComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlAudioComponent")
local SceneObjectManager = kg_require("Gameplay.NetEntities.SceneActor.SceneObjectManager")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

local math_floor = math.floor
local math_random = math.random

---@class SceneObjectBase
local SceneObjectBase = DefineLocalEntity("SceneObjectBase", LocalEntityBase,
    {
        ViewControlBaseComponent,
        ViewControlFxComponent,
        ViewControlVisibleComponent,
        ViewControlAudioComponent,
    }
)
SceneObjectBase.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {}

SceneObjectBase:Register("SceneObjectBase")

-- 默认BP路径
SceneObjectBase.EPClassMap = {
    [EWActorType.SCENE_TEXT] = ViewResourceConst.BP.BP_SceneText,
    [EWActorType.DIALOGUE_TRIGGER] = ViewResourceConst.BP.BP_Dialogue,
    [EWActorType.ESTATE_PORTAL] = ViewResourceConst.BP.BP_EstatePortal,
    [EWActorType.STREET_LIGHT] = "",  -- TOD路灯是美术布置的，走Bind流程所以不需要显示的定义BP
    [EWActorType.SCENEACTOR_WINDMILL] = "",
}

function SceneObjectBase.Register(cls, ActorType)
    LocalEntityBase.Register(cls, cls.__cname)

    cls.ActorType = ActorType
    SceneObjectManager.RegisterSceneObjectCls(ActorType, cls.__cname)
end

function SceneObjectBase:ctor()
    self:Debug(">>SceneObjectBase ctor", self.SceneObjectUid, self:uid(), self.InsID, self.bInDestroy)
    Game.SceneObjectManager:OnObjectCreated(self.SceneObjectUid, self:uid(), self.InsID)
end

function SceneObjectBase:dtor()
    self.bInDestroy = false
    Game.SceneObjectManager:OnObjectDestroy(self.SceneObjectUid, self.InsID)
end

-- 后续BP的路径是表格配置或者需要拼接字符串时, 子类需要重写此方法
function SceneObjectBase:GetActorBPClassPath()
    local BPClassPath = SceneObjectBase.EPClassMap[self.ActorType]
    if BPClassPath ~= nil then
        return BPClassPath
    end

    return LocalEntityBase.GetActorBPClassPath(self)
end

function SceneObjectBase:GetSceneInstanceID()
    local FindInstanceID = nil
    if self.SceneConf and self.SceneConf.LoadFromMap then
        FindInstanceID = self.InsID
    end
    return FindInstanceID
end

function SceneObjectBase:EnterWorld()
    LocalEntityBase.EnterWorld(self)
    Game.HeadInfoManager:RegisterHeadInfo(self:uid())
end

function SceneObjectBase:AfterEnterWorld()
    self:initDissolveConfig()
end

function SceneObjectBase:ExitWorld()
    Game.HeadInfoManager:UnRegisterHeadInfo(self:uid())
    LocalEntityBase.ExitWorld(self)
end

function SceneObjectBase:SetOverrideConf(extendTb)
    local overrideConf = self.OverrideConf
    for k, v in pairs(extendTb) do
        overrideConf[k] = v
    end
    self:AfterSetOverrideConf(extendTb)
end

-- 各个子类自己继承实现响应行为
function SceneObjectBase:AfterSetOverrideConf(extendTb)
end

-- 设置延迟销毁时长，单位秒
function SceneObjectBase:SetDelayDestroy(InDelayTime)
    self.DelayDestroyTime = InDelayTime
end

function SceneObjectBase:GetDelayDestroyTime()
    if self.DelayDestroyTime then
        return self.DelayDestroyTime
    end
    return 0
end

function SceneObjectBase:StartDestroy()
    if self.bInDestroy then
        return
    end

    local delayDestroyTime = self:GetDelayDestroyTime()
    if delayDestroyTime > 0 then
        self.bInDestroy = true
        self:DissolveBeforeDelayDestroy()
        local destroyTime = _G._now() + delayDestroyTime * 1000
        Game.SceneObjectManager:RegisterDelayDestroyObj(self:uid(), destroyTime)
    else
        self:destroy()
    end
end

function SceneObjectBase:initDissolveConfig()
    local sceneConf = self.SceneConf
    local commonCfg = sceneConf and sceneConf.SceneActorCommon
    if commonCfg and commonCfg.Dissolve then
        if commonCfg.Dissolve.DissolveOutType > Enum.EDissolveOutType.None then
            local dissolveOutTime = 0
            if commonCfg.Dissolve.DissolveOutPeriod and commonCfg.Dissolve.DissolveOutPeriod.X > 0 and
                commonCfg.Dissolve.DissolveOutPeriod.Y >= commonCfg.Dissolve.DissolveOutPeriod.X then
                local randX = math_floor(commonCfg.Dissolve.DissolveOutPeriod.X * 10)
                local randY = math_floor(commonCfg.Dissolve.DissolveOutPeriod.Y * 10)
                dissolveOutTime = math_random(randX, randY) / 10
            else
                dissolveOutTime = commonCfg.Dissolve.DissolveOutTime
            end
            self:SetDelayDestroy(dissolveOutTime)
            self.DissolveOutTime = dissolveOutTime
        end
    end
end

--通用溶解 延迟销毁前调用
function SceneObjectBase:DissolveBeforeDelayDestroy()
    local sceneConf = self.SceneConf
    local commonCfg = sceneConf and sceneConf.SceneActorCommon
    if commonCfg and commonCfg.Dissolve then
        if commonCfg.Dissolve.DissolveOutType > Enum.EDissolveOutType.None then
            if Enum.EDissolveOutType.Noise == commonCfg.Dissolve.DissolveOutType then
                if self.bInWorld then
                    self:StartDissolveMaterialEffect(DISSOLVE_MATERIAL_EFFECT_TYPE.CHARACTER_NOISE_OUT, self.DissolveOutTime)
                end
            elseif Enum.EDissolveOutType.Noise_In == commonCfg.Dissolve.DissolveOutType then
                if self.bInWorld then
                    self:StartDissolveMaterialEffect(DISSOLVE_MATERIAL_EFFECT_TYPE.CHARACTER_NOISE_IN, self.DissolveOutTime)
                end
            end
        end
    end
end



