kg_require("Gameplay.NetEntities.SceneActor.LSceneActorEntityBase")

---@class LSAE_FogTrigger : LSceneActorEntityBase

---@type LSAE_FogTrigger|SceneActorBehaviorComponent
local LSAE_FogTrigger = DefineLocalEntity("LSAE_FogTrigger", LSceneActorEntityBase, {
    ViewControlTriggerComponent,
    SceneActorBehaviorComponent,
})

LSAE_FogTrigger:Register(EWActorType.FOG_TRIGGER)

function LSAE_FogTrigger:ctor()
    self.ExcelData = Game.TableData.GetFogTriggerDataRow(self.SceneConf.TemplateID)
    self.SceneActorBehaviorType = self.ExcelData.Behavior
    self.timerHandle = nil
    self.bHasTriggered = false
    self.dispelRadius = self.ExcelData.DispelFog[1]
    self.dispelPeriod = self.ExcelData.DispelFog[2]
end

function LSAE_FogTrigger:UnInit()
    if self.timerHandle then
        Game.TimerManager:StopTimerAndKill(self.timerHandle)
        self.timerHandle = nil
    end
end

---@overload
function LSAE_FogTrigger:SetOverrideBehaviorParams()
    if 0 ~= self.ExcelData.UITemplateID then
        self:OverrideTriggerUITemplateID(SceneActorFixedStateEnum.SA_ACTIVE, self.ExcelData.UITemplateID)
    end
    self:SetOverrideActionParamsByState(SceneActorFixedStateEnum.SA_FINISH, "DispelFog", self.ExcelData.DispelFog)
end

function LSAE_FogTrigger:Dispel()
    if self.bHasTriggered then
        self:DebugFmt("[Dispel] %s has been triggered", self.ID)
        return
    end

    self:DebugFmt("[Dispel] %s dispelPeriod=%s, dispelRadius=%s", self.InsID, self.dispelPeriod, self.dispelRadius)
    Game.PostProcessManager:OverrideFogDistance(self.dispelRadius / 100)
    if self.dispelPeriod > 0 then
        self.timerHandle = Game.TimerManager:CreateTimerAndStart(function()
            Game.PostProcessManager:ResetOverrideFogDistance()
            self.timerHandle = nil
        end, self.dispelPeriod * 1000, 1)
    end

    if self.dispelPeriod <= 0 then
        self.bHasTriggered = true
    end
end

return LSAE_FogTrigger
