local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local EWActorType = kg_require("Shared.WorldActorDefine").EWActorType

BriefAvatarActor = DefineBriefEntity("AvatarActor", { ActorBase }, {
	ViewControlBaseComponent,
	ViewControlVisibleComponent,
	CampRelationComponent,
	TeamComponent,
	GroupComponent,
	AppearanceComponent,
	NetEntityBaseComponent,
	SocialActionComponent
})

BriefAvatarActor:Register("AvatarActor", true)
BriefAvatarActor.ActorType = EWActorType.PLAYER

function BriefAvatarActor:ctor()
	self:DebugFmt("BriefAvatarActor:ctor: %s(%s)", self:uid(), self.eid)
	self.isAvatar = true
	self.isBriefEntity = true
end

function BriefAvatarActor:dtor()
	self:DebugFmt("BriefAvatarActor:dtor: %s(%s)", self:uid(), self.eid)
end

function BriefAvatarActor:GetActorBPClassPath()
	return ViewResourceConst.BP_USED_BY_ENTITY["BriefAvatarActor"]
end

function BriefAvatarActor:GetActorCompositeData()
	return nil
end

function BriefAvatarActor:On_Entity_To_Brief(OldEnt)
	Game.WorldManager:NotifyEntity_To_Brief(OldEnt, self)
end

function BriefAvatarActor:EnterWorld()
	ActorBase.EnterWorld(self)
	Game.EventSystem:AddListener(_G.EEventTypes.ON_CAMP_CHANGED, self, self.Receive_ON_CAMP_CHANGED, self.eid)
end

function BriefAvatarActor:BeforeExitWorld()
	Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.ON_CAMP_CHANGED, self, self.Receive_ON_CAMP_CHANGED, self.eid)
	ActorBase.BeforeExitWorld(self)
end

function BriefAvatarActor:GetEntityConfigData()
	return nil
end

function BriefAvatarActor:OnMsgEntityRelive(type, pos, rot, srcId)
	self:DebugFmt("BriefAvatarActor:OnMsgEntityRelive: %s %s %s", self.eid, type, srcId)
	if not self.bInWorld then
		return
	end
	self:OnTeleportInternal(pos.x, pos.y, pos.z)
	Game.ChatSystem:OnRelive(self.eid, srcId)
end

function BriefAvatarActor:OnMsgEntityDead(reason, KillerID)
	self:DebugFmt("BriefAvatarActor:OnMsgEntityDead: %s %s %s", self.eid, reason, KillerID)
	if not self.bInWorld then
		return
	end
	self:OnRoleDead(reason, KillerID)
end

function BriefAvatarActor:OnMsgSetPosition(reason, pos, rot)
	if not self.bInWorld then
		return
	end
	self:OnTeleportInternal(pos.x, pos.y, pos.z)
end

function BriefAvatarActor:OnTeleportInternal(x, y, z, yaw, teleportType, reason, sequence, source)
	if not self.bInWorld then
		return
	end
	self.CppEntity:KAPI_Movement_ReceiveSetLocation(x, y, z)
end

return BriefAvatarActor
