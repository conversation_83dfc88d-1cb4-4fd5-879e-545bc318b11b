local LUC = kg_require("Shared.Const.LogicUnitConst")



LUnitAuraEntity = DefineEntity("LUnitAuraEntity", {LUnitEntity})
LUnitAuraEntity.COMPONENTS = {}
LUnitAuraEntity.HAS_ENTITY_DEF = true
LUnitAuraEntity:Register("LUnitAuraEntity")

function LUnitAuraEntity:ctor()
	self.unitType = LUC.LOGIC_UNIT_TYPE.Aura
end

function LUnitAuraEntity:ExitWorld()
    if (self.ClientProxy ~= nil) then
        local LOGIC_UNIT_DESTROY_REASON = LUC.LOGIC_UNIT_DESTROY_REASON
        self.ClientProxy:TryDestroyUnit(LOGIC_UNIT_DESTROY_REASON.LOCAL_END_OF_LIFE)
    end

    ActorBase.ExitWorld(self)
end

function LUnitAuraEntity:GetProxyClassName()
    return "LUnitAuraNEntity"
end

function LUnitAuraEntity:GetTemplateData(InID)
    return Game.TableData.GetAuraDataRow(InID)
end

return LUnitAuraEntity