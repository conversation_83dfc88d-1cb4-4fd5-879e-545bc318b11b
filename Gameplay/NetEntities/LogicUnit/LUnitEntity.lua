local ULLFunc = import("LowLevelFunctions")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")


LUnitEntity = DefineEntity("LUnitEntity", {ActorBase}, {DebugComponent})
LUnitEntity.COMPONENTS = {}
LUnitEntity.HAS_ENTITY_DEF = true
LUnitEntity:Register("LUnitEntity")

-- region Important
function LUnitEntity:ctor()
    self.bUnit = true

    local nowTime = ULLFunc.GetUtcMillisecond()
    local timeElapse = nowTime - self.BornTimeStamp

    self:DebugFmt(
        "---> LUnitEntity(%s), eid: %s create, now:{%s/%s}, timeElapse: %sms",
        self.__cname, 
        self.eid, 
        nowTime, 
        self.BornTimeStamp, 
        timeElapse
    )

    self.ClientProxy = nil
end

function LUnitEntity:LoadActor()
    Game.BSManager.LV_UnitLoc:Pack(self.Position[1], self.Position[2], self.Position[3])
    Game.BSManager.LR_UnitRot:Pack(self.Rotation[1], self.Rotation[2], self.Rotation[3])

    local ProxyData = {
        Data = self:GetTemplateData(self.TemplateID),
        Stage = self.Stage,
        Level = self.level or self.UnitLevel,
        LEID = self.LauncherID,
        IEID = self.instigatorID or self.InstigatorID,
        ATEID = self.AttachedTargetID,
        InitLocation = Game.BSManager.LV_UnitLoc,
        InitRotation = Game.BSManager.LR_UnitRot,
        bLocalMode = self.bLocalMode,
        ExtraData = self.ExtraData
    }

    self.ClientProxy = Game.EntityManager:CreateLocalEntity(self:GetProxyClassName(), ProxyData)

    -- 进入世界
    self:EnterWorld()
end

function LUnitEntity:dtor()
    self:DebugFmt(
        "---> LUnitEntity(%s), eid: %s destroy", 
        self.__cname, 
        self.eid
    )
end
-- endregion Important



-- region Sync
function LUnitEntity:set_Stage(entity, new, old)
    local nowTime = ULLFunc.GetUtcMillisecond()
    local stageElapse = nowTime - self.StageStartTimeStamp
    local leftTime = self.StageEndTimeStamp - nowTime

    self:DebugFmt(
        "--> LUnitEntity(%s), set_Stage, eid: %s, change from %s->%s, now:{%s/%s}, stageElapse:%sms, end:{%s}, leftTime: %sms",
        self.__cname, 
        self.id, 
        old, 
        new, 
        nowTime,  
        self.StageStartTimeStamp, 
        stageElapse, 
        self.StageEndTimeStamp, 
        leftTime
    )

    if (self.ClientProxy ~= nil) then
        self.ClientProxy:OnStageChanged(new, old)
    end
end

function LUnitEntity:OnMsgSyncRotation(InNewYaw)
    self:DebugFmt(
        "--> LUnitEntity(%s), eid: %s, OnMsgSyncRotation: %s",
        self.__cname,
        self.id,
        InNewYaw
    )

    if (self.ClientProxy ~= nil) then
        self.ClientProxy:OnRotationUpdate(InNewYaw)
    end
end
-- endregion Sync



-- region Core
function LUnitEntity:GetProxyClassName()
    return "LUnitNEntity"
end

function LUnitEntity:GetTemplateData(InID)
    return nil
end
-- endregion Core



function LUnitEntity:CalculateViewRoleImportance()
	return WorldViewBudgetConst.THIRD_ROLE_IMPORTANCE
end

return LUnitEntity