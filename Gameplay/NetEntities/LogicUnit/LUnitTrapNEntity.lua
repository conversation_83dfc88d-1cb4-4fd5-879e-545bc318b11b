local LUC = kg_require("Shared.Const.LogicUnitConst")



-- 防止重复创建同名类
if (_G.LUnitTrapNEntity ~= nil) then
    return _G.LUnitTrapNEntity
end

LUnitTrapNEntity = DefineLocalEntity("LUnitTrapNEntity", LUnitNEntity)
LUnitTrapNEntity:Register("LUnitTrapNEntity")

function LUnitTrapNEntity:ctor()
	self.unitType = LUC.LOGIC_UNIT_TYPE.Trap
end
-- 状态改变事件
function LUnitTrapNEntity:OnStageChanged(InNew, InOld)
	local TRAP_STAGE = LUC.TRAP_STAGE
	if InNew == TRAP_STAGE.DELAY_TAKE_EFFECT then
		self:onEnterStageDelayTakeEffect()
	elseif InNew == TRAP_STAGE.TAKE_EFFECT then
		self:onEnterStageTakeEffect()
	end
end

--region Stage
function LUnitTrapNEntity:onEnterStageDelayTakeEffect()
	-- 播放陷阱预警动画
	self:PlayWarnEffect()
end

function LUnitTrapNEntity:onEnterStageTakeEffect()
	-- 播放陷阱触发特效
	self:PlayTriggerEffect()
end
--endregion Stage


function LUnitTrapNEntity:GetTrapAppearData()
	local UnitData = self.Data
	if UnitData == nil then
		return
	end

	return Game.TableData.GetTrapAppearRow(UnitData.ID)
end

-- 陷阱触发特效
function LUnitTrapNEntity:PlayTriggerEffect()
	local AppearData = self:GetTrapAppearData()
	if AppearData == nil then
		return
	end

	local EffectPath_TriggerEffect = AppearData.EffectPath_TriggerEffect
	if EffectPath_TriggerEffect == nil or EffectPath_TriggerEffect == "" then
		return
	end

	self:PlayEffectTableNiagara(EffectPath_TriggerEffect,AppearData.FollowType_TriggerEffect,
			AppearData.BFollowScale_TriggerEffect,AppearData.BoneDock_TriggerEffect, 10.0, true,nil,nil,
			AppearData.Offset_TriggerEffect,AppearData.Rotation_TriggerEffect, AppearData.Scale_TriggerEffect, true)
end

return LUnitTrapNEntity