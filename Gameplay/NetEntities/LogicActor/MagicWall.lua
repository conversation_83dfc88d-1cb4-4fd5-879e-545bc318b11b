---@class MagicWall
---@field InstanceID string
---@field bActivate boolean
---@field bEnableUnidirectionalBlocking boolean
---@field BlockingType number
local CollisionConst = kg_require("Shared.Const.CollisionConst")

MagicWall = DefineEntity("MagicWall", {SceneActor}, {}) -- luacheck: ignore
MagicWall.COMPONENTS = {}
MagicWall.HAS_ENTITY_DEF = true
MagicWall:Register("MagicWall")

function MagicWall:OnCreate(Properties) -- luacheck: ignore
    SceneActor.OnCreate(self, Properties)
end

function MagicWall:set_bEnableUnidirectionalBlocking()
    --self:Debug("[set_bEnableUnidirectionalBlocking]")
    --Game.LogicActorMgr:OnEntityPropChange(self)
end

function MagicWall:set_BlockingType()
    --self:Debug("[set_BlockingType]")
    --Game.LogicActorMgr:OnEntityPropChange(self)
end

return MagicWall
