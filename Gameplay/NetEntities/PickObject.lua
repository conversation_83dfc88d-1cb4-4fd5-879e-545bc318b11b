
local Enum = Enum
local SkeletalMeshComponent = import("SkeletalMeshComponent")
local StaticMeshComponent = import("StaticMeshComponent")
local NiagaraComponent = import("NiagaraComponent")

PickObject = DefineEntity("PickObject", { ActorBase }, {
    ViewControlBaseComponent,
    ViewControlFxComponent,
    ViewControlVisibleComponent,
    ViewControlTriggerComponent,
    ViewControlAudioComponent,
    SimpleViewControlAnimComponent,
    SpiritualVisionViewBaseComponent,
    PickObjectInteractComponent,
})

-- 采集物基础显示规则 会被显隐表配置的规则覆盖
PickObject.SHOW_RULE = {
    HIDE = 0,
    BY_INTERACT = 1,  -- 根据是否可交互来决定是否显示
    SHOW = 2,
}

-- 采集物初始可采集状态
PickObject.INITIAL_INTERACT_STATE = {
    NONE = 1,
    CAN_PICK = 1,
}

function PickObject:ctor()
    self.ActorType = EWActorType.COLLECTION
    local templateID = self.TemplateID
    local rowData = Game.TableData.GetSceneActorTaskCollectDataRow(templateID)
    if rowData == nil then
        -- 服务端会保证templateID的有效性，除非两端配置不一致，此时直接打断创建
        error(string.format("PickObject with wrong template id TemplateID:%s, InsID:%s", templateID, self.InstanceID))
    end

    -- 部分配置数据如外观模型和显隐生命周期相关 进行缓存 热更不会直接起效 进出AOI重建Entity之后生效
    self.spiritualVisionShowRule = rowData.SpiritualVisionShowRule

    self.niagaraAssetScale = rowData.NiagaraAssetScale
    self.interactorBornAnim = rowData.InteractorBornAnim
    self.ShowNameRule = rowData.ShowNameRule
    self.DefaultSpiritualShaderTemplate = rowData.SpiritualVisionShader or 0
    self.DefaultSpiritualIconType = rowData.SpiritualVisionIconType or 0
end

function PickObject:AfterEnterWorld()
    ActorBase.AfterEnterWorld(self)

    local rowData = Game.TableData.GetSceneActorTaskCollectDataRow(self.TemplateID)
    assert(rowData)
    self:initAppearance(rowData)
    Game.EventSystem:AddListener(_G.EEventTypes.CINEMATIC_ON_START, self, self.onCinematicStart)
    Game.EventSystem:AddListener(_G.EEventTypes.CINEMATIC_ON_END, self, self.onCinematicEnd)

    self:InitTrigger()
    self:InitTriggerCustomScope()
    if self.bInReborn or not self.bSeverControlVisible then
        self:SetActorInVisible(Enum.EInVisibleReasons.ServerControl)
    end

    if Game.DialogueManager:IsPlayingDialogue() then
        self:onCinematicStart(Enum.CinematicType.Dialogue)
    end
end

function PickObject:set_bInReborn(_, new, _)
    if new then
        self:SetActorInVisible(Enum.EInVisibleReasons.ServerControl)
    elseif self.bSeverControlVisible then
        self:SetActorVisible(Enum.EInVisibleReasons.ServerControl)
    end
end

function PickObject:set_bSeverControlVisible(_, new, _)
    if new then
        self:SetActorInVisible(Enum.EInVisibleReasons.ServerControl)
    elseif not bInReborn then
        self:SetActorVisible(Enum.EInVisibleReasons.ServerControl)
    end
end

function PickObject:set_bSeverControlCollectable(_, new, _)
    self:RefreshCollectable()
end

function PickObject:BeforeExitWorld()
    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.CINEMATIC_ON_START, self, self.onCinematicStart)
    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.CINEMATIC_ON_END, self, self.onCinematicEnd)
end

function PickObject:onCinematicStart(Type, AssetID)
    if Type == Enum.CinematicType.Dialogue then
        local Comps = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "GLOW")
        for _, compID in ipairs(Comps:ToTable()) do
            self.CppEntity:KAPI_Component_SetActive(compID, false, true)
            self.CppEntity:KAPI_SceneID_SetVisibility(compID, false, true)
        end
    end
end

function PickObject:onCinematicEnd(Type, AssetID)
    if Type == Enum.CinematicType.Dialogue then
        self:RefreshGlowParticleByCollectable()
    end
end

--region 灵视相关
function PickObject:ReadSpiritualConf()
    local showRule = self.spiritualVisionShowRule
    if showRule == Enum.ESpiritualVisionShowRule.Always then
    elseif showRule == Enum.ESpiritualVisionShowRule.OnlyInVision then
        self.bDefaultOnlyInSpiritualVision = true
    elseif showRule == Enum.ESpiritualVisionShowRule.UseVision then
        self.bDefaultUseSpiritualVision = true
    end
end

--@override
function PickObject:OnEnterSpiritualStatus()
    --仅在灵视显示
    if self:IsOnlyVisibleInSpiritual() then
        self:SetActorVisible(Enum.EInVisibleReasons.SpiritualControl)
        self:SwitchSpiritualShow(true)
    end
    --灵视后显示
    if self:IsOnlyVisibleAfterSpiritual() then
        self:SetActorVisible(Enum.EInVisibleReasons.SpiritualControl)
        self:SwitchSpiritualShow(true)
    end
end

--@override
function PickObject:OnLeaveSpiritualStatus()
    --仅在灵视显示
    if self:IsOnlyVisibleInSpiritual() then
        self:SetActorInVisible(Enum.EInVisibleReasons.SpiritualControl)
        self:SwitchSpiritualShow(false)
    end
end
--endregion 灵视相关

--region 外观相关 TODO: 后续会用FacadeID来替换
function PickObject:initAppearance(excelData)
    local MeshPath = excelData.Mesh
    if StringValid(excelData.SkeletalMesh) then
        MeshPath = excelData.SkeletalMesh
    end

    local SMComp = self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
    local SKComp = self.CppEntity:KAPI_Actor_GetComponentByClass(SkeletalMeshComponent)
    if StringValid(MeshPath) then
        self:DoAsyncLoadAsset(MeshPath, "OnMainMeshLoaded")
        local MeshScale = excelData.MeshScale
        if MeshScale and MeshScale > 0 then
            if SMComp ~= 0 then
                self.CppEntity:KAPI_SceneID_SetWorldScale3D(SMComp, FVector(MeshScale, MeshScale, MeshScale))
            end
            if SKComp ~= 0 then
                self.CppEntity:KAPI_SceneID_SetWorldScale3D(SMComp, FVector(MeshScale, MeshScale, MeshScale))
            end
        end
        local MeshOffsetZ = excelData.MeshOffsetZ
        if MeshOffsetZ and MeshOffsetZ ~= 0 then
            if SMComp ~= 0 then
                local RelLoc = self.CppEntity:KAPI_SceneID_GetRelativeLocation(SMComp)
                self.CppEntity:KAPI_SceneID_SetRelativeLocation(SMComp, RelLoc.X, RelLoc.Y, MeshOffsetZ)
            end
            if SKComp ~= 0 then
                local RelLoc = self.CppEntity:KAPI_SceneID_GetRelativeLocation(SKComp)
                self.CppEntity:KAPI_SceneID_SetRelativeLocation(SKComp, RelLoc.X, RelLoc.Y, MeshOffsetZ)
            end
        end
    end
    if StringValid(excelData.NiagaraAsset) then
        self:DoAsyncLoadAsset(excelData.NiagaraAsset, "OnBornNiagaraLoaded")
    end
    if excelData.GlowAssetScale > 0.0 then
        local Comps = self.CppEntity:KAPI_Actor_GetComponentsByTag(NiagaraComponent, "GLOW")
        for _, CompID in ipairs(Comps:ToTable()) do
            local Scale = excelData.GlowAssetScale
            if Scale and Scale > 0 then
                self.CppEntity:KAPI_SceneID_SetWorldScale3D(CompID, FVector(Scale, Scale, Scale))
            end
        end
    end
    if StringValid(excelData.GlowAsset) then
        self:DoAsyncLoadAsset(excelData.GlowAsset, "OnGlowAssetLoaded")
    end
end

function PickObject:OnMainMeshLoaded(LoadId, AssetID)
    if AssetID ~= 0 then
        local MeshComp
        if LuaScriptAPI.IsATypeOf(AssetID, "StaticMesh") then
            MeshComp = self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
            if MeshComp ~= 0 then
                self.CppEntity:KAPI_StaticMeshID_SetStaticMesh(MeshComp, AssetID)
            end
        elseif LuaScriptAPI.IsATypeOf(AssetID, "SkeletalMesh") then
            MeshComp = self.CppEntity:KAPI_Actor_GetComponentByClass(SkeletalMeshComponent)
            if MeshComp ~= 0 then
                self:SetSkeletalMeshAssetAndAnimationMode(AssetID, EAnimationMode.AnimationSingleNode)
            end
            if StringValid(self.interactorBornAnim) then
                self:PlayAnimationByMontageAsyncLoad("DefaultSlot", self.interactorBornAnim, true, 0, 0)
            end
        else
            self:WarningFmt("Mesh Type Error, InsID:%s, TemplateID:%s", self.InstanceID, self.TemplateID)
        end
    end
end

function PickObject:OnBornNiagaraLoaded(LoadId, AssetID)
    if AssetID ~= 0 then
        local NiagaraComp = self.CppEntity:KAPI_Actor_GetComponentByTag(NiagaraComponent, "BORN")
        if NiagaraComp ~= 0 then
            self.CppEntity:KAPI_NiagaraID_SetAsset(NiagaraComp, AssetID, true)
            self.CppEntity:KAPI_Component_SetActive(NiagaraComp, true, true)
            self.CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(NiagaraComp, true)
            local Scale = self.niagaraAssetScale
            if Scale and Scale > 0 then
                self.CppEntity:KAPI_SceneID_SetWorldScale3D(NiagaraComp, FVector(Scale, Scale, Scale))
            end
        end
    end
end

function PickObject:OnGlowAssetLoaded(LoadId, AssetID)
    if AssetID ~= 0 then
        local NiagaraComp = self.CppEntity:KAPI_Actor_GetComponentByTag(NiagaraComponent, "GLOW")
        if NiagaraComp ~= 0 then
            self.CppEntity:KAPI_NiagaraID_SetAsset(NiagaraComp, AssetID, true)
            self.CppEntity:KAPI_Component_SetActive(NiagaraComp, true, true)
            self.CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(NiagaraComp, true)
        end
    end
end
--endregion 外观相关

return PickObject
