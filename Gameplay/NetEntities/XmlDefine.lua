return {
    "AbilityComponent",
    "Account",
    "AchievementComponent",
    "ActivityComponent",
    "ActorBase",
    "AdditionalSkillComponent",
    "AdminComponent",
    "AdminService",
    "alias",
    "AnimControlComponent",
    "AoiLevelCutComponent",
    "AppearanceComponent",
    "ArrodesTalkEventComponent",
    "AutoBattleComponent",
    "AutoSkillComponent",
    "AvatarActor",
    "AvatarActorCEComponent",
    "AvatarActorInteractComponent",
    "AvatarActorNpcInteractComponent",
    "AvatarBattleInteractComponent",
    "AvatarCommonInteractorComponent",
    "AvatarEntity",
    "AvatarGVGComponent",
    "AvatarMgrService",
    "AvatarMorphComponent",
    "AvatarSceneObjectComponent",
    "AvatarViewControlComponent",
    "BattleElementComponent",
    "BattleNoticeComponent",
    "BattleStateComponent",
    "BattleStatisticsComponent",
    "BattleZoneV2",
    "BehaviorMoveComponent",
    "BehaviorTreeMgr",
    "BidComponent",
    "BidDataComponent",
    "BikeMassAgentComponent",
    "BoolPropComponent",
    "BreakDefenseComponent",
    "BroadcastRpc",
    "BuffComponentNew",
    "CampRelationComponent",
    "ChampionComponent",
    "ChampionGroupArena",
    "ChampionGroupArenaSpace",
    "ChampionPrepareArena",
    "ChampionService",
    "ChampionTroopService",
    "ChatComponent",
    "ChatService",
    "ChatServiceLua",
    "CircleTrap",
    "ClientTimelineComponent",
    "ClimateComponent",
    "ClimateService",
    "ClusterAgent",
    "CollectiblesComponent",
    "CombatEffectComponent",
    "CommandSystemComponent",
    "CommonInteractorBaseComponent",
    "ComplexLocomotionControlComponent",
    "CurrencyComponent",
    "CurveMoveComponent",
    "CustomizedGameplayComponent",
    "CustomRoleComponent",
    "CustomShapeWall",
    "DamageTrackComponent",
    "DanceComponent",
    "DanceDungeon",
    "DataCenterService",
    "DBBulkProxy",
    "DBProxy",
    "DebugComponent",
    "DiceCheckComponent",
    "DualMoveComponent",
    "Dungeon",
    "DungeonAuctionComponent",
    "DungeonAuctionOrderService",
    "DungeonAuctionService",
    "DungeonComponent",
    "DungeonMgr",
    "DungeonRollComponent",
    "DungeonRollService",
    "DungeonSpace",
    "EleTalentTreeComponent",
    "EliminationArena",
    "EliminationArenaSpace",
    "EliminationComponent",
    "EntityBase",
    "EquipComponent",
    "ExerciseComponent",
    "ExplorationProgressComponent",
    "ExplorePrefabGroup",
    "FashionComponent",
    "FashionMakeUpComponent",
    "FashionMountComponent",
    "FashionWearingComponent",
    "FellowComponent",
    "FightModeComponent",
    "FightPropComponent",
    "FixDataComponent",
    "FlowchartActionComponent",
    "FlowchartAvatarComponent",
    "FoeComponent",
    "FollowComponent",
    "FortuitousComponent",
    "FragmentComponentBase",
    "FriendClubComponent",
    "FriendClubService",
    "FriendComponent",
    "FriendService",
    "FStatePropComponent",
    "GameplayPrefabComponent",
    "GameRecordService",
    "GazeControlComponent",
    "GiftCreditComponent",
    "GiveGiftComponent",
    "GlobalDataCenterService",
    "GlobalDataService",
    "GlobalLoginQueueService",
    "GlobalRankService",
    "GMCommonComponent",
    "GMComponent",
    "GroupComponent",
    "GroupManagerService",
    "GroupOccupyArea",
    "GroupOccupyAreaSpace",
    "GroupService",
    "Guild",
    "GuildAnswerArea",
    "GuildComponent",
    "GuildImpActivity",
    "GuildImpVoyage",
    "GuildLeague",
    "GuildLeagueSpace",
    "GuildService",
    "GuildStation",
    "GuildStationSpace",
    "GVGArena",
    "GVGService",
    "GVGSpace",
    "HitFeedbackComponent",
    "HomeComponent",
    "Homeland",
    "HomeService",
    "HotfixComponent",
    "HotfixServiceLua",
    "IdolExploreComponent",
    "iGlobalStub",
    "IndividualPVPComponent",
    "IndividualPVPEntity",
    "InteractableComponent",
    "InteractableDetectComponent",
    "InteractorComponent",
    "InteractorGroup",
    "InventoryComponent",
    "InvisibleHandComponent",
    "ItemComponent",
    "ItemSnapShotService",
    "LevelFlowComponent",
    "LimitComponent",
    "LoadBalanceService",
    "LocalPerformPetComponent",
    "LockTargetComponent",
    "LocomotionControlComponent",
    "LoginComponent",
    "LoginQueueService",
    "LUnitAuraEntity",
    "LUnitEntity",
    "LUnitInteractorAgentEntity",
    "LUnitProxyComponent",
    "LUnitSpellAgentEntity",
    "LUnitSpellFieldEntity",
    "LUnitTrapEntity",
    "LUnitVelocityFieldEntity",
    "MagicWall",
    "MailComponentNew",
    "MainPlayer",
    "MallService",
    "MatchComponent",
    "ManorWorkshopComponent",
    "MessageComponent",
    "MobilePlatform",
    "ModuleLockComponent",
    "MomentsComponent",
    "MoneySaleComponent",
    "MoneySaleService",
    "MorphComponent",
    "MountComponent",
    "MoveComponent",
    "MoveConstraintVolumeComponent",
    "MultiJumpComponent",
    "NetEntityBaseComponent",
    "NewBieGuideComponent",
    "NpcActor",
    "NpcAggroComponent",
    "NpcAsideTalkComponent",
    "NpcElementComponent",
    "NpcFragmentComponent",
    "NpcInteractorComponent",
    "NpcViewControlComponent",
    "OccupyDetectArea",
    "OfflineEventService",
    "OnlineService",
    "ParallelBehaviorControlCompBase",
    "PartnerComponent",
    "PartyComponent",
    "PartyInfoCacheService",
    "PartyService",
    "PassiveSkillComponent",
    "PathFollowComponent",
    "PathMoveFunctionComponent",
    "PerformanceComponent",
    "PickObject",
    "PickObjectComponent",
    "PictureComponent",
    "Plane",
    "PlaneComponent",
    "PlaneSpace",
    "PlayerAirWallPerformanceComponent",
    "PlayerClimateControlComponent",
    "PlayerFollowComponent",
    "PlayerSceneActorConditionComponent",
    "PlayerTaskComponent",
    "PlotRecapComponent",
    "PrefabGroup",
    "PrefabGroupComponent",
    "PrepareZone",
    "Process",
    "ProfessionComponent",
    "PropComponent",
    "PVPMatchService",
    "QuestActionComponent",
    "QuestArchiveComponent",
    "QuestComponent",
    "QuizService",
    "RankComponent",
    "RankService",
    "RedisComponent",
    "RedPacketComponent",
    "RedPacketService",
    "RedPointComponent",
    "RefreshComponent",
    "RefreshShopComponent",
    "ResizeBoundsScaleComponent",
    "RespawnPoint",
    "ReviveComponent",
    "RideComponent",
    "RolePlayGameComponent",
    "RolePlayIdentityComponent",
    "RolePlayTalentComponent",
    "RouterService",
    "Scene",
    "SceneActor",
    "SceneActorBehaviorComponent",
    "SceneActorBehaviorControlComponent",
    "SceneCustomComponent",
    "ScheduleComponent",
    "SchoolTop",
    "SchoolTopSpace",
    "SdkComponent",
    "SealedComponent",
    "SeasonComponent",
    "SeasonService",
    "SequenceComponent",
    "Service",
    "ShapeTrigger",
    "ShopComponent",
    "SimpleViewControlComponent",
    "SkillComboComponent",
    "SkillComponentNew",
    "SkillListComponent",
    "SneakComponent",
    "SocialActionComponent",
    "Space",
    "SpaceBaseService",
    "SpaceCommonInteractorComponent",
    "SpaceComponent",
    "SpaceLevelSequenceComponent",
    "SpaceManager",
    "SpaceSceneObjectComponent",
    "SpaceTimerComponent",
    "SpiritualVisionSceneActorComponent",
    "SpiritualVisionViewBaseComponent",
    "SquareTrigger",
    "StallComponent",
    "StallService",
    "StaminaComponent",
    "StateComponent",
    "StateConflictComponent",
    "StateConflictComponentBase",
    "StatisticsComponent",
    "SummonComponent",
    "SystemActionComponent",
    "SystemSettingComponent",
    "TakeDamageComponent",
    "TalentTreeComponent",
    "TarotTeamCacheService",
    "TarotTeamComponent",
    "TarotTeamService",
    "TaskComponent",
    "Team",
    "TeamArena",
    "TeamArenaBaseSpace",
    "TeamArenaComponent",
    "TeamArenaModeComponent",
    "TeamArenaSpace",
    "TeamComponent",
    "TeamManagerService",
    "TeamMatchService",
    "TeamPVP",
    "TeamPvpSpace",
    "TeamService",
    "TeaRoomComponent",
    "TeaRoomInfoCacheService",
    "TeaRoomService",
    "TelnetLuaService",
    "TelnetService",
    "TempInventoryComponent",
    "TestLuaService",
    "TestMap",
    "TestSpace",
    "TitleComponent",
    "Train",
    "TrapActor",
    "TriggerComponent",
    "VehicleComponent",
    "ViewControlBaseComponent",
    "ViewControlComponent",
    "ViewControlFxComponent",
    "ViewControlTriggerComponent",
    "ViewControlVisibleComponent",
    "ViewRoleAttrComponent",
    "WareHouseComponent",
    "WeaponItemComponent",
    "World",
    "WorldActivityComponent",
    "WorldBidService",
    "WorldBossActivityService",
    "WorldBossComponent",
    "WorldComponent",
    "WorldManager",
    "WorldService",
    "ZonePrompter",
}
