local SharedWorldConst = kg_require("Shared.Const.WorldConst") 

---@class TestSpace : Space
TestSpace = DefineEntity("TestSpace", { Space })
TestSpace.COMPONENTS = {}
TestSpace.HAS_ENTITY_DEF = true
TestSpace:Register("TestSpace")

function TestSpace:ctor()
    self.WorldType = SharedWorldConst.WORLD_TYPE.TEST_MAP
end

function TestSpace:dtor()

end

function TestSpace:ReturnToWorld()
    local data = Game.TableData.GetLevelMapDataRow(self.TemplateID)

    if data.Type == SharedWorldConst.WORLD_TYPE.DUNGEON then
        Game.me:ReqLeaveDungeon(false)
    elseif data.Type == SharedWorldConst.WORLD_TYPE.PLANE then
        if Game.QuestSystem:GetSwicth() then
            Game.QuestSystem:ReqQuestLeavePlane()
        else
            Game.me:ReqTaskLeavePlane()
        end
    end
end

return TestSpace
