---@class ChampionGroupArenaSpace : TeamArenaBaseSpace
ChampionGroupArenaSpace = DefineEntity("ChampionGroupArenaSpace", { TeamArenaBaseSpace })
ChampionGroupArenaSpace.COMPONENTS = {}
ChampionGroupArenaSpace.HAS_ENTITY_DEF = true
ChampionGroupArenaSpace:Register("ChampionGroupArenaSpace")

function ChampionGroupArenaSpace:ctor()
	Game.TeamAreanaSystem.model:SetPVPChampionStage(self.MainRoundIndex)
end

return ChampionGroupArenaSpace
