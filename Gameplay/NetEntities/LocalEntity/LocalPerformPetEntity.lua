-----
-----LocalPerformPetEntity
-----

kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")

local CollisionConst = kg_require("Shared.Const.CollisionConst")


local LocalPerformPetEntity = DefineLocalEntity("LocalPerformPetEntity", LocalEntityBase, {
	NewViewControlAnimComponent,
	ViewControlAnimLibComponent,
	ComplexLocomotionControlComponent,
	ViewControlAttachComponent,
	ViewControlFxComponent,
	ViewControlBaseComponent,
	ViewControlVisibleComponent,
	NewViewControlMaterialComponent,
	ViewControlAppearanceComponent,
})
LocalPerformPetEntity:Register("LocalPerformPetEntity")
LocalPerformPetEntity.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {CollisionConst.COLLISION_PRESET_NAMES.MOVABLE_UNIT_WITH_NET_DRIVE_PRESET, CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET}

function LocalPerformPetEntity:ctor()
	self.isRefreshModel = true
	self.curAppearanceModel = self.ModelID or ""
	self.targetAppearanceModel = self.ModelID or ""
end

function LocalPerformPetEntity:dtor()

end

function LocalPerformPetEntity:AfterEnterWorld()
	self:OnPerformPetRefreshFinished()
	self:SetAttachToForMinDragon(self.OwnerID)
	self:ApplyAttach()
	self:CheckPetStatus()
end

function LocalPerformPetEntity:GetConfigModelID()
	return self.ModelID
end

function LocalPerformPetEntity:GetPerformPetOwner()
	return Game.EntityManager:getEntity(self.OwnerID)
end

function LocalPerformPetEntity:CheckPetStatus()
	local Owner = self:GetPerformPetOwner()
	if Owner then
		-- 武器处理
		Owner:UpdateWeapons()
		-- 由于武器的刷新接口目前会外面裸调用,可能会存在小龙还没入场就创建了武器的情况,这里先做一次挂接检查
		if Owner.Profession == Enum.PROFESSION_TYPE.Visionary then
			Owner:CheckHoldWeapon()
		end
		-- 通知主人控制宠物显隐
		Owner:CheckPerformancePetVisibility()
	end
end

function LocalPerformPetEntity:RefreshVisibility(bVisibility, reasonType)
	if bVisibility then
		self:SetActorVisible(reasonType)
	else
		self:SetActorInVisible(reasonType)
	end
end

function LocalPerformPetEntity:RefreshPerformPetAppearance()
	local OwnerEntity = self:GetPerformPetOwner()
	if not OwnerEntity then return end

	local Profession = OwnerEntity.Profession
	local ProfessionStateID = OwnerEntity.ProfessionStateID
	local newPetModel = ""
	if Profession == Enum.PROFESSION_TYPE.Visionary then
		local ProfessionTable = Game.TableData.GetProfessionStateDataTable()
		for ID, Value in ksbcpairs(ProfessionTable) do
			if Value.ProfessionID == Profession and Value.StatePet and Value.StatePet ~= "" then
				if (Value.IsDefault and ProfessionStateID == 1) or (not Value.IsDefault and ProfessionStateID == 2) then
					newPetModel = Value.StatePet
					break
				end
			end
		end
	end

	if newPetModel == "" then return end
	self.targetAppearanceModel = newPetModel
	-- 刷新宠物外观
	if self.isRefreshModel then
		return
	end
	self.isRefreshModel = true
	self.curAppearanceModel = newPetModel
	self:RefreshBodyPart_WholeAvatar(nil, self.targetAppearanceModel, nil, nil, self, "OnPerformPetRefreshFinished")
end

function LocalPerformPetEntity:OnPerformPetRefreshFinished()
	if self.curAppearanceModel ~= self.targetAppearanceModel then
		self.curAppearanceModel = self.targetAppearanceModel
		self:RefreshBodyPart_WholeAvatar(nil, self.targetAppearanceModel, nil, nil, self, "OnPerformPetRefreshFinished")
	else
		self.isRefreshModel = false
	end
end

function LocalPerformPetEntity:GetDefaultLocoControlTemplateID()
	return Enum.LocoControlDefaultTemplate.LocalNpcBase
end

function LocalPerformPetEntity:GetSpeed()
	return 0
end

function LocalPerformPetEntity:IsUsedByMainPlayer()
	if Game.me == nil then
		return false
	end
	return self.OwnerID == Game.me:uid()
end

return LocalPerformPetEntity
