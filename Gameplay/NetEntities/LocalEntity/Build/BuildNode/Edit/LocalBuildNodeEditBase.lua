local ECollisionEnabled = import("ECollisionEnabled")

---@class LocalBuildNodeEditBase : LocalEntityBase
local LocalBuildNodeEditBase = DefineLocalEntity("LocalBuildNodeEditBase", LocalEntityBase)

LocalBuildNodeEditBase:Register("LocalBuildNodeEditBase")

LocalBuildNodeEditBase.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {
}

function LocalBuildNodeEditBase:ctor()
	---配置ID
	self.presetID = self.presetID or nil
	self.presetConfig = self:GetConfig(self.presetID)
	
	---placeActorID
	self.placeActorID = nil
	
	--Transform信息
	self.buildPosition = { X = 0, Y = 0, Z = 0}
	self.yaw = 0
	
	self.buildPositionChange = true

	---不能摆放类型
	---@type Enum.BuildCantPlaceType
	self.cantBuildType = 0
	self.cantBuildDesc = ""
	
	---起始格子下标
	self.minGridIndex = {X = -1, Y = -1, Z = -1}
	---结束格子下标
	self.maxGridIndex = {X = -1, Y = -1, Z = -1}
	
	---层级
	self.layer = 0

	---是否开启碰撞
	self.enableCollision = false
	
	---模型LoadHandle
	self._placeModelLoadHandleID = nil
	---重叠的Node
	self._overlapNodeMap = {}
	---当前高亮
	self._highLight = nil
end

function LocalBuildNodeEditBase:dtor()
	self:UnLoadPlaceActor()
end

function LocalBuildNodeEditBase:Destroy()
	self:UnLoadPlaceActor()
end

---@public
function LocalBuildNodeEditBase:CloneDataByNode(oldNode)
	self.presetID = oldNode.presetID

	self.buildPosition.X = oldNode.buildPosition.X
	self.buildPosition.Y = oldNode.buildPosition.Y
	self.buildPosition.Z = oldNode.buildPosition.Z
	self.yaw = oldNode.yaw

	self.minGridIndex.X = oldNode.minGridIndex.X
	self.minGridIndex.Y = oldNode.minGridIndex.Y
	self.minGridIndex.Z = oldNode.minGridIndex.Z
	self.maxGridIndex.X = oldNode.maxGridIndex.X
	self.maxGridIndex.Y = oldNode.maxGridIndex.Y
	self.maxGridIndex.Z = oldNode.maxGridIndex.Z

	self.layer = oldNode.layer
	self.buildIndex = oldNode.buildIndex
	self.turnX = oldNode.turnX
end

---@public
function LocalBuildNodeEditBase:CanBuild()
	return self.cantBuildType == 0 or self.cantBuildType == Enum.BuildCantPlaceType.Normal_Count
end

---@public
function LocalBuildNodeEditBase:GetCantBuildDesc()
	return self.cantBuildDesc
end

function LocalBuildNodeEditBase:OnRefreshCanBuild()
	
end

function LocalBuildNodeEditBase:CheckBuildPosValid()
	return Game.ManorSystem.ManorBuildModule.model:IsBuildPosValid(self.buildPosition.X, self.buildPosition.Y)
end

---刷新是否可摆放
---@private
function LocalBuildNodeEditBase:RefreshCanBuild()
	local cantBuildDesc = self.cantBuildDesc

	if self:CheckBuildPosValid() then
		self:OnRefreshCanBuild()
	else
		self:RefreshDefaultCanBuildDesc(Enum.BuildCantPlaceType.Normal_Over)
	end
	if self.cantBuildDesc ~= cantBuildDesc then
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_BUILD_CANT_PLACE_UPDATE, self:GetCantBuildDesc(), self.cantBuildType)
	end
end

---刷新摆放物位置
---@private
function LocalBuildNodeEditBase:RefreshBuildPositionData(poxX, posY, posZ)
	self.buildPositionChange = (self.buildPosition.X ~= poxX) or (self.buildPosition.Y ~= posY) or (self.buildPosition.Z ~= posZ)

	self.buildPosition.X = poxX
	self.buildPosition.Y = posY
	self.buildPosition.Z = posZ

	if self.buildPositionChange then
		self:DebugFmt("Build: Type=%s, Group=%s, CanBuild=%s, X=%s,Y=%s,Z=%s", 
			self:GetMainType(), self:GetSubType(), self:CanBuild(), poxX, posY, posZ)
	end
end

---刷新摆放物位置
---@private
function LocalBuildNodeEditBase:RefreshBuildPosition()
	if self.bInWorld and self.buildPositionChange then
		self:SetPosition_P(self.buildPosition.X, self.buildPosition.Y, self.buildPosition.Z+1)
	end
end

---刷新连线特效
---@private
function LocalBuildNodeEditBase:RefreshLineEffect()
	Game.ManorSystem.ManorBuildModule:SetLineEffectCanBuild(self:CanBuild())
end

---刷新高亮特效
---@private
function LocalBuildNodeEditBase:RefreshHighlightEffect(blue)
	local highLight = blue and Enum.BuildHighLightEffectType.Place_Blue or Enum.BuildHighLightEffectType.Place_Red
	if self._highLight == highLight then
		return false
	end

	if not self.bInWorld then
		return false
	end

	self._highLight = highLight

	local meshComID = self.CppEntity:KAPI_Actor_GetComponentByClassName("StaticMeshComponent")
	local num = self.CppEntity:KAPI_MeshID_GetNumMaterials(meshComID)
	for index=0, num-1 do
		local dynamicMatID = self.CppEntity:KAPI_PrimitiveID_CreateAndSetMaterialInstanceDynamic(meshComID, index)
		local config = Game.TableData.GetManorHighLightEffectDataRow(self._highLight)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "UseCustomData", config.UseCustomData)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "HighLightOn", config.HighLightOn)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "HighLightBlinkOn", config.HighLightBlinkOn)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "HighLightRimSpeed", config.HighLightRimSpeed)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "HighLightRimPower", config.HighLightRimPower)
		LuaScriptAPI.SetDynamicMaterialLinearColorParameterValue(dynamicMatID, "HighLightEmission",
			FLinearColor(config.HighLightEmission[1],config.HighLightEmission[2],config.HighLightEmission[3],config.HighLightEmission[4]))
		LuaScriptAPI.SetDynamicMaterialLinearColorParameterValue(dynamicMatID, "HighLightTint",
			FLinearColor(config.HighLightTint[1],config.HighLightTint[2],config.HighLightTint[3],config.HighLightTint[4]))
	end
	
	return true
end

function LocalBuildNodeEditBase:LoadPlaceActor()
end

function LocalBuildNodeEditBase:AfterEnterWorld()
	local config = self:GetConfig()
	if not config then
		return
	end
	
	self.CppEntity:KAPI_Actor_SetActorHiddenInGame(true)
	self:SetPosition_P(self.buildPosition.X,self.buildPosition.Y,self.buildPosition.Z+1)
	self:SetRotation_P(0, self.yaw, 0)
	
	self._placeModelLoadHandleID = self:DoAsyncLoadAsset(config.model, "OnPlaceActorLoaded")
	self.placeActorID = self.CharacterID
	self:SetCollisionEnable(true)
end

function LocalBuildNodeEditBase:OnPlaceActorLoaded(loadID, assetID)
	if assetID == 0 then
		self:ErrorFmt("Build OnPlaceActorLoaded Error, PresetID = %s", self.presetID)
		return
	end
	
	if not self.bInWorld then
		return
	end
	
	self.CppEntity:KAPI_Actor_SetActorHiddenInGame(false)
	local meshID = self.CppEntity:KAPI_Actor_GetComponentByClassName("StaticMeshComponent")
	self.CppEntity:KAPI_StaticMeshID_SetStaticMesh(meshID, assetID)
	Game.ManorSystem.ManorBuildModule:ForceUpdateBuild()
end

---@private
---刷新重叠的Actor
function LocalBuildNodeEditBase:RefreshOverlayActorByActor()
	local overlayActorIDList = Game.ManorSystem.ManorBuildModule:GetOverlayActorIDByActorID(self.placeActorID)
	if not overlayActorIDList then
		return
	end

	table.clear(self._overlapNodeMap)
	for i = 1, overlayActorIDList:Num() do
		local actorID = overlayActorIDList:Get(i-1);
		if actorID then
			self._overlapNodeMap[actorID] = actorID
		end
	end
end

function LocalBuildNodeEditBase:GetBlockNodeDesc(excludeType, excludeGroupType)
	local otherBlockDesc = ""
	for _, actorID in pairs(self._overlapNodeMap) do
		otherBlockDesc = self:AddBlockNodeDesc(excludeType, excludeGroupType, actorID, otherBlockDesc)
	end
	
	return otherBlockDesc
end

---@private
function LocalBuildNodeEditBase:AddBlockNodeDesc(excludeType, excludeGroupType, actorID, otherBlockDesc)
	local node = Game.ManorSystem.ManorBuildModule:GetRuntimeNodeByCollisionActorID(actorID, self.layer, excludeType)
	if node and node:GetMainType() ~= excludeType and node:GetSubType() ~= excludeGroupType then
		if node.layer == self.layer then
			if otherBlockDesc == "" then
				otherBlockDesc = node:GetName()
			else
				otherBlockDesc = string.format("%s,%s", otherBlockDesc, node:GetName())
			end
		end
	else
		--目前只检查主角
		if actorID == Game.me.CharacterID and not string.contains(otherBlockDesc, "MainPlayer") then
			otherBlockDesc = "MainPlayer"
		end
	end
	
	return otherBlockDesc
end

---检测阻挡的节点
---@private
function LocalBuildNodeEditBase:RefreshCanBuildOnBlock(excludeType, excludeGroupType)
	local otherBlockDesc = self:GetBlockNodeDesc(excludeType, excludeGroupType)

	if not string.isEmpty(otherBlockDesc) then
		self.cantBuildType = Enum.BuildCantPlaceType.Normal_Block
		self.cantBuildDesc = string.format(Game.TableData.GetManorCantPlaceDataRow(self.cantBuildType).TypeDes, otherBlockDesc)
		return true
	end

	return false
end

---卸载摆放的Actor
---@public
function LocalBuildNodeEditBase:UnLoadPlaceActor()
	self.placeActorID = nil

	if self._placeModelLoadHandleID then
		Game.AssetManager:CancelLoadAsset(self._placeModelLoadHandleID)
		self._placeModelLoadHandleID = nil
	end
end

function LocalBuildNodeEditBase:SetCollisionEnable(enableCollision)
	if not self.bInWorld then
		return
	end

	self.enableCollision = enableCollision

	local comIDList = self.CppEntity:KAPI_Actor_GetComponentListByTag("Trigger")
	for _, comID in pairs(comIDList) do
		if enableCollision then
			self.CppEntity:KAPI_PrimitiveID_SetCollisionEnabled(comID, ECollisionEnabled.QueryOnly)
		else
			self.CppEntity:KAPI_PrimitiveID_SetCollisionEnabled(comID, ECollisionEnabled.NoCollision)
		end
	end
end

---@public
function LocalBuildNodeEditBase:SetItemLayer(layer)
	self.layer = layer
end

function LocalBuildNodeEditBase:OnSetItemPosition(x, y, z)
	Log.DebugErrorFormat("LocalBuildNodeEditBase OnSetItemPosition not implemented")
end

---@public
function LocalBuildNodeEditBase:SetItemPosition(x,y,z)
	self:OnSetItemPosition(x,y,z)
	self.buildPositionChange = true
end

---@public
function LocalBuildNodeEditBase:SetItemRotate(yaw)
end

---@public
function LocalBuildNodeEditBase:GetPosition()
	return self.buildPosition.X, self.buildPosition.Y, self.buildPosition.Z
end

---@public
function LocalBuildNodeEditBase:GetConfig()
	if not self.presetID then
		return nil
	end
	
	return Game.TableData.GetManorItemDataRow(self.presetID)
end

---@public
function LocalBuildNodeEditBase:GetName()
	local config = self:GetConfig()
	return config and config.FurnitureName
end

---@public
function LocalBuildNodeEditBase:GetMainType()
	local config = self:GetConfig()
	return config and config.TypeId
end

---@public
function LocalBuildNodeEditBase:GetSubType()
	local config = self:GetConfig()
	return config and config.GroupId
end

---@public
function LocalBuildNodeEditBase:GetGroupSubType()
	local config = self:GetConfig()
	return config and config.GroupSubId
end

---@public
function LocalBuildNodeEditBase:GetGroupTypeConfig()
	local config = self:GetConfig()
	return config and Game.TableData.GetManorItemSubTypeDataRow(config.GroupId)
end

---@public
function LocalBuildNodeEditBase:GetGroupSubTypeConfig()
	local config = self:GetConfig()
	return config and Game.TableData.GetManorItemSubTypeDataRow(config.GroupSubId)
end

---@public
function LocalBuildNodeEditBase:RefreshDefaultCanBuildDesc(cantBuildType)
	self.cantBuildType = cantBuildType or 0
	
	if self.cantBuildType == 0 then
		self.cantBuildDesc = ""
		return
	end

	self.cantBuildDesc = Game.TableData.GetManorCantPlaceDataRow(self.cantBuildType).TypeDes
end

---@public
function LocalBuildNodeEditBase:CanRotate()
	local typeConfig = self:GetGroupTypeConfig()
	return typeConfig and typeConfig.CanRotate
end

---@public
function LocalBuildNodeEditBase:IsComponent()
	local config = self:GetConfig()
	return config and config.TypeId == Enum.BuildItemType.Component
end

---@private
function LocalBuildNodeEditBase:ResetPosAndGridIndex(x, y, z, startIndexX, startIndexY, startIndexZ)
	self.minGridIndex.X = startIndexX or -1
	self.minGridIndex.Y = startIndexY or -1
	self.minGridIndex.Z = startIndexZ or -1

	self.maxGridIndex.X = self.minGridIndex.X + self.curBuildVolumeGrid.X
	self.maxGridIndex.Y = self.minGridIndex.X + self.curBuildVolumeGrid.Y
	self.maxGridIndex.Z = self.minGridIndex.X + self.curBuildVolumeGrid.Z
	self.buildPosition.X = x or 0
	self.buildPosition.Y = y or 0
	self.buildPosition.Z = z or 0
end

return LocalBuildNodeEditBase