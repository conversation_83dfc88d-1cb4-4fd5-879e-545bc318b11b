local BuildNodeEditBase = kg_require("Gameplay.NetEntities.LocalEntity.Build.BuildNode.Edit.LocalBuildNodeEditBase")
local BuildTools = kg_require("Gameplay.LogicSystem.Manor.Modules.Build.System.BuildTools")

---@class LocalBuildNodeEditComponent: LocalBuildNodeEditBase
local LocalBuildNodeEditComponent = DefineLocalEntity("LocalBuildNodeEditComponent", BuildNodeEditBase)
LocalBuildNodeEditComponent:Register("LocalBuildNodeEditComponent")

function LocalBuildNodeEditComponent:ctor()
	self.presetID = self.presetID or nil
	
	---C++Instance数组下标
	self.buildIndex = 0

	self.gridComponent = Game.ManorSystem.ManorBuildModule:GetGridComponent()

	if self.presetConfig.Volume[1] then
		---摆放物体积
		self.defaultBuildVolumeGrid = {X = self.presetConfig.Volume[1]/self.gridComponent.GridSizeX, Y = self.presetConfig.Volume[2]/self.gridComponent.GridSizeY, Z = self.presetConfig.Volume[3]/self.gridComponent.GridSizeZ}
		self.curBuildVolumeGrid = {X = self.defaultBuildVolumeGrid.X, Y = self.defaultBuildVolumeGrid.Y, Z = self.defaultBuildVolumeGrid.Z}
	end
	
	---摆放时的组合Node（比如：一个墙壁+两个柱子）
	self.childNodeList = nil
	self.childPosOffset = nil
	self.parentNode = nil
	self:LoadChildNode(self.presetID)

	if self.oldNode then
		self:CloneDataByNode(self.oldNode)
		self.oldNode = nil
	end
end

function LocalBuildNodeEditComponent:dtor()
end

function LocalBuildNodeEditComponent:Destroy()
	BuildNodeEditBase.Destroy(self)
	self:UnLoadChildNode()
end

---@public
function LocalBuildNodeEditComponent:OnSetItemPosition(x,y,z)
	self:AlignmentPosition(x,y,z)

	self:SetItemRotate(self.yaw)
	self:SetItemPositionChild(self.buildPosition.X, self.buildPosition.Y, self.buildPosition.Z)

	self:Refresh()
end

---@public
function LocalBuildNodeEditComponent:Refresh()
	self:RefreshOverlayActor()
	self:RefreshCanBuild()

	self:RefreshLineEffect()
	self:RefreshHighlightEffect(self:CanBuild())
end

---@private
function LocalBuildNodeEditComponent:SetItemPositionChild(x,y,z)
	if not self.childNodeList then
		return
	end

	for i, node in pairs(self.childNodeList) do
		node:AlignmentPositionChild(x,y,z)
		node:SetItemRotateChild(self.yaw)
		node:RefreshOverlayActor()
	end
end

---刷新高亮特效
---@private
function LocalBuildNodeEditComponent:RefreshHighlightEffect(blue)
	if not BuildNodeEditBase.RefreshHighlightEffect(self, blue) then
		return
	end
	
	self:RefreshHighlightEffectChild(blue)
end
---刷新高亮特效（子物体）
---@private
function LocalBuildNodeEditComponent:RefreshHighlightEffectChild(blue)
	if self.childNodeList then
		for _, childNode in pairs(self.childNodeList) do
			childNode:RefreshHighlightEffect(blue)
		end
	end
end

function LocalBuildNodeEditComponent:GetBlockNodeDesc(excludeType, excludeGroupType)
	local otherBlockDesc = ""
	for _, actorID in pairs(self._overlapNodeMap) do
		otherBlockDesc = self:AddBlockNodeDesc(excludeType, excludeGroupType, actorID, otherBlockDesc)
	end
	
	--遍历子物体阻挡
	if self.childNodeList then
		for _, childNode in pairs(self.childNodeList) do
			for _, actorID in pairs(childNode._overlapNodeMap) do
				if not self._overlapNodeMap[actorID] then
					otherBlockDesc = self:AddBlockNodeDesc(excludeType, excludeGroupType, actorID, otherBlockDesc)
				end
			end
		end
	end
	
	return otherBlockDesc
end

function LocalBuildNodeEditComponent:OnAlignmentPosition(x, y, z)
	Log.DebugErrorFormat("LocalBuildNodeEditComponent:OnAlignmentPosition not implemented")
end

---位置修正
---@private
function LocalBuildNodeEditComponent:AlignmentPosition(x,y,z)
	self:OnAlignmentPosition(x,y,z)
	self:RefreshBuildPosition()
end

---刷新重叠物体
---@private
function LocalBuildNodeEditComponent:RefreshOverlayActor()
	self:OnRefreshOverlayActor()
end

function LocalBuildNodeEditComponent:OnRefreshBuildIndex()
	Log.DebugErrorFormat("LocalBuildNodeEditComponent:OnRefreshBuildIndex not implemented")
end

---获取组件Build下标
---@private
function LocalBuildNodeEditComponent:GetBuildIndex()
	self:OnRefreshBuildIndex()
	return self.buildIndex
end

---@public
function LocalBuildNodeEditComponent:GetComponentPartType()
	return BuildTools.GetInstanceComponentTypeByGroupType(self:GetSubType())
end

---@public
function LocalBuildNodeEditComponent:GetMaxBuildIndex()
	return self.gridComponent:GetGridBuildMaxIndex(self:GetSubType())
end

---@public
function LocalBuildNodeEditComponent:SetItemRotate(yaw)
	if yaw then
		self.yaw = yaw
	else
		self.yaw = self.yaw + 90
		if self.yaw > 270 then
			self.yaw = 0
		end
	end

	if not self.bInWorld then
		return
	end
	self:SetRotation_P(0, self.yaw, 0)

	self:Refresh()
end

---@public
function LocalBuildNodeEditComponent:SetItemLayer(layer)
	BuildNodeEditBase.SetItemLayer(self, layer)
	self:SetItemLayerChild(layer)
end

---@public
function LocalBuildNodeEditComponent:SetItemScale(NewScale)
	BuildNodeEditBase.SetItemScale(self, NewScale)
	self:SetItemScaleChild(NewScale)
end

---@private
function LocalBuildNodeEditComponent:LoadChildNode(presetID)
	local config = Game.TableData.GetManorItemDataRow(presetID)
	if string.isEmpty(config.CombineComponent) then
		return
	end

	self.childNodeList = {}
	for group in config.CombineComponent:gmatch("{(.-)}") do
		local id = nil
		local pos = {}
		for result in group:gmatch("%((.-)%)") do
			if not id then
				id = tonumber(result)
			else
				local x, y, z = result:match("([^,]+),([^,]+),([^,]+)")
				pos.X = tonumber(x)
				pos.Y = tonumber(y)
				pos.Z = tonumber(z)
			end
		end

		local newNode = Game.ManorSystem.ManorBuildModule:CreateEditNode(id)
		newNode.parentNode = self
		newNode.childPosOffset = pos
		table.insert(self.childNodeList, newNode)
	end
end

---@private
function LocalBuildNodeEditComponent:UnLoadChildNode()
	if self.childNodeList then
		for i, node in pairs(self.childNodeList) do
			node:Destroy()
			node:destroy()
		end
	end

	self.childNodeList = nil
	self.parentNode = nil
	self.childPosOffset = nil
end


---@private
function LocalBuildNodeEditComponent:SetItemRotateChild(yaw)
	self.yaw = yaw

	if not self.bInWorld then
		return
	end
	self:SetRotation_P(0, self.yaw, 0)
end
---@private
function LocalBuildNodeEditComponent:SetItemLayerChild(layer)
	if not self.childNodeList then
		return
	end

	for i, node in pairs(self.childNodeList) do
		node:SetItemLayer(layer)
	end
end
---@private
function LocalBuildNodeEditComponent:SetItemScaleChild(scale)
	if not self.childNodeList then
		return
	end

	for i, node in pairs(self.childNodeList) do
		node:SetItemScale(scale)
	end
end

---@public
function LocalBuildNodeEditComponent:RefreshCountEnough()
	local buildNum = Game.ManorSystem.ManorBuildStoreModule:GetStoreFurnitureNumByID(self.presetID)
	if not buildNum or buildNum == 0 then
		self.cantBuildType = Enum.BuildCantPlaceType.Normal_Count
		self.cantBuildDesc = string.format(Game.TableData.GetManorCantPlaceDataRow(self.cantBuildType).TypeDes, self:GetName())
		return false
	end
	
	if self.childNodeList then
		for _, childNode in pairs(self.childNodeList) do
			local num = Game.ManorSystem.ManorBuildStoreModule:GetStoreFurnitureNumByID(childNode.presetID)
			if not num or num == 0 then
				self.cantBuildType = Enum.BuildCantPlaceType.Normal_Count
				self.cantBuildDesc = string.format(Game.TableData.GetManorCantPlaceDataRow(self.cantBuildType).TypeDes, childNode:GetName())
				return false
			end
		end
	end

	return true
end

---@public
function LocalBuildNodeEditComponent:GetPlaceNumberData()
	local numberData = {}
	numberData[self.presetID] = 1

	if self.childNodeList then
		for _, editNode in pairs(self.childNodeList) do
			local runtimeNode = Game.ManorSystem.ManorBuildModule:GetComponentRuntimeNodeByIndex(
				editNode.layer, editNode:GetSubType(), editNode.buildIndex
			)

			if not runtimeNode then
				if numberData[editNode.presetID] then
					numberData[editNode.presetID] = numberData[editNode.presetID] + 1
				else
					numberData[editNode.presetID] = 1
				end
			end
		end
	end

	return numberData
end

return LocalBuildNodeEditComponent