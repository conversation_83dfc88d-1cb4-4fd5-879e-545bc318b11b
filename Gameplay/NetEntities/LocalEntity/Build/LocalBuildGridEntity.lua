---@class LocalBuildGridEntity : LocalEntityBase
local LocalBuildGridEntity = DefineLocalEntity("LocalBuildGridEntity", LocalEntityBase)

LocalBuildGridEntity:Register("LocalBuildGridEntity")

-- 家园建造（网格）
function LocalBuildGridEntity:ctor()
	self.actorLocationX = self.actorLocationX or 0
	self.actorLocationY = self.actorLocationY or 0
	self.actorLocationZ = self.actorLocationZ or 0
	self.actorScaleX = self.actorScaleX or 0
	self.actorScaleY = self.actorScaleY or 0
	self.actorScaleZ = self.actorScaleZ or 0
	self.actorShow = self.actorShow or nil

	self.gridSize = self.gridSize or nil
	self.gridLocation = self.gridLocation or nil
end

function LocalBuildGridEntity:dtor()

end

---@public method 显隐
function LocalBuildGridEntity:SetVisible(visible)
	self.actorShow = visible

	if self.bInWorld then
		self.CppEntity:KAPI_Actor_SetActorHiddenInGame(not self.actorShow)
	end
end

---@public method 高度
function LocalBuildGridEntity:SetHeight(height)
	self.actorLocationZ = height

	if self.bInWorld then
		self.CppEntity:KAPI_SetLocation_P(self.actorLocationX, self.actorLocationY, self.actorLocationZ)
	end
end


function LocalBuildGridEntity:AfterLoadActor(actorBP)
	
end

function LocalBuildGridEntity:AfterEnterWorld()
	self.CppEntity:KAPI_Actor_SetActorHiddenInGame(not self.actorShow)
	self:SetScale_P(self.actorScaleX, self.actorScaleY, self.actorScaleZ)
	self:SetPosition_P(self.actorLocationX, self.actorLocationY, self.actorLocationZ)


	local planeMeshComID = self.CppEntity:KAPI_Actor_GetComponentByClassName("StaticMeshComponent")
	local materialID = self.CppEntity:KAPI_PrimitiveID_GetMaterial(planeMeshComID, 0)
	
	if not LuaScriptAPI.IsATypeOf(materialID, "MaterialInstanceDynamic") then
		local dynamicMatID = self.CppEntity:KAPI_PrimitiveID_CreateAndSetMaterialInstanceDynamic(planeMeshComID, 0)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "GridDensity", 1/self.gridSize)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "GridWidth", 0.02)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "Offset_X", self.gridLocation.X)
		LuaScriptAPI.SetDynamicMaterialScalarParameterValue(dynamicMatID, "Offset_Y", self.gridLocation.Y)
	end
	
	self:SetVisible(self.actorShow)
end

function LocalBuildGridEntity:UpdateScale(posX, posY, posZ, scaleX, scaleY, scaleZ)
	self.actorLocationX = posX
	self.actorLocationY = posY
	self.actorLocationZ = posZ
	self.actorScaleX = scaleX
	self.actorScaleY = scaleY
	self.actorScaleZ = scaleZ
	self:SetScale_P(self.actorScaleX, self.actorScaleY, self.actorScaleZ)
	self:SetPosition_P(self.actorLocationX, self.actorLocationY, self.actorLocationZ)
end

function LocalBuildGridEntity:BeforeExitWorld()

end


return LocalBuildGridEntity
