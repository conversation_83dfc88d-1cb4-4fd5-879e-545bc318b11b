--local EAttachmentRule = import("EAttachmentRule")
--local EDetachmentRule = import("EDetachmentRule")

kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")
kg_require("Gameplay.NetEntities.Comps.ViewControl.SimpleViewControlComponent")
kg_require("Gameplay.NetEntities.Comps.LocomotionControlComponent")
kg_require("Gameplay.NetEntities.Comps.MoveComponent")

---@class LocalControllableNPCRegion
local LocalControllableNPCRegion = DefineLocalEntity("LocalControllableNPCRegion", LocalEntityBase, {
    MoveConstraintVolumeComponent,
})
LocalControllableNPCRegion:Register("LocalControllableNPCRegion")

function LocalControllableNPCRegion:ctor(_,Params)
    self.SpawnLoc = Params.SpawnLoc
    self.ConstrainRadius = Params.ConstrainRadius or 1000
    self.bIsDynamicVolume = true
    self.ConstraintWhiteList = Params.WhiteList
end

function LocalControllableNPCRegion:dtor()

end

function LocalControllableNPCRegion:GetCreateActorLocation()
    return self.SpawnLoc, nil
end

function LocalControllableNPCRegion:GetEntityConfigData()
    return nil
end

function LocalControllableNPCRegion:GetConfigFacadeControlData()
    return nil
end

function LocalControllableNPCRegion:GetConfigModelID()
    return self.ModelID
end

function LocalControllableNPCRegion:GetConfigAnimAssetID()
    return nil
end



