
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

-- 本地武器 Entity
local LocalWeaponItem = DefineLocalEntity("LocalWeaponItem", AttachItemBase, { 
	NewViewControlAnimComponent, 
	ViewControlAnimLibComponent
})

LocalWeaponItem:Register("LocalWeaponItem")

function LocalWeaponItem:AfterEnterWorld()
	if self.AttachInfo then
		self:UpdateWeaponItemVisibleWithCache()
	end
end

-- 入场后根据 父挂件历史缓存更新显隐性
function LocalWeaponItem:UpdateWeaponItemVisibleWithCache()
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
	if Owner then
		local ReasonTable = Owner.HiddenAttachesWithReasons[WorldViewConst.ATTACH_ITEM_TYPE.Weapon]
		if not ReasonTable then return end

		for Reason, _ in pairs(ReasonTable) do
			self:SetActorInVisible(Reason)
		end
	end
end

function LocalWeaponItem:DoWeaponFadeInOutAssetCollect(MainWeaponID)
	if not MainWeaponID then
		return
	end

	local weaponConfigData = Game.TableData.GetWeaponAnimationOverridesDataRow(MainWeaponID)
	if not weaponConfigData then return end

	local AppearData = Game.TableData.GetDissolveEffectDataRow(weaponConfigData.AppearEffect)
	local DisAppearData = Game.TableData.GetDissolveEffectDataRow(weaponConfigData.DisappearEffect)

	-- WeaponDissolveEffects
	local AssetPathsToLoad = {}
	if AppearData and AppearData.WeaponDissolveEffects then
		for _, path in ipairs(AppearData.WeaponDissolveEffects) do
			table.insert(AssetPathsToLoad, path)
		end
	end

	if DisAppearData and DisAppearData.WeaponDissolveEffects then
		for _, path in ipairs(DisAppearData.WeaponDissolveEffects) do
			table.insert(AssetPathsToLoad, path)
		end
	end

	if #AssetPathsToLoad > 0 then
		self:BatchRequestAssetBeforeLoadActor(AssetPathsToLoad)
	end
end

function LocalWeaponItem:SetWeaponFightState(bFight)
	if not self.bInWorld then
		self:AddCommandCache("SetWeaponFightState", bFight)
		return
	end
	
	if self.bIsStaticMesh or self.bIsSimpleAnim then
		return
	end

	self.CppEntity:KAPI_Animation_SetAnimInsBoolProperty('OwnerPlayerFightState', bFight or false)
end

function LocalWeaponItem:IsUsedByMainPlayer()
	if Game.me == nil then
		return false
	end
	return self.OwnerID == Game.me:uid()
end 