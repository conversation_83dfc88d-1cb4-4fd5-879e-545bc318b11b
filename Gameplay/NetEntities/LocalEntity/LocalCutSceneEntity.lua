local LocalCutSceneEntity = DefineLocalEntity("LocalCutSceneEntity", LocalEntityBase, {
	ViewControlVisibleComponent
})
LocalCutSceneEntity:Register("LocalCutSceneEntity")


function LocalCutSceneEntity:ctor()
	self.Position = M3D.Vec3()
	self.Rotation = M3D.Rotator()
	self.CutSceneData = nil
end

function LocalCutSceneEntity:Init(params)
	self.Position = params.Position or M3D.Vec3()
	self.Rotation = params.Rotation or M3D.Rotator()
end

function LocalCutSceneEntity:GetCreateActorLocation()
	return self.Position, self.Rotation
end

function LocalCutSceneEntity:dtor()

end

function LocalCutSceneEntity:AfterEnterWorld()
	-- 在这里可以添加进入世界时的逻辑
	if self.CutSceneData then
		Game.CinematicManager:StartPlayCinematic(self.CutSceneData)
	end
end

function LocalCutSceneEntity:AfterExitWorld()
	-- 在这里可以添加退出世界时的逻辑
	self.CutSceneData = nil
end

function LocalCutSceneEntity:SetCutSceneData(cutSceneData)
	self.CutSceneData = cutSceneData
end