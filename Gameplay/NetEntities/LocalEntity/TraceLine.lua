local NiagaraComponent = import("NiagaraComponent")
local C7TrackSplineComponent = import("C7TrackSplineComponent")

---@class TraceLine
---@field InTransform
local TraceLine = DefineLocalEntity("TraceLine", LocalEntityBase, {ViewControlVisibleComponent})

TraceLine:Register("TraceLine")

function TraceLine:ctor()
    self._cache_cmd = {}  -- 创建actor过程中的指令缓存
    self.bNiagaraRdy = false
    -- self.DebugTrackPoints = nil
end

function TraceLine:dtor()

end


function TraceLine:GetCreateActorLocation()
    return self.InTransform:GetLocation(), self.InTransform:GetRotation():Rotator()
end


function TraceLine:AfterEnterWorld()
    self:initNiagara()
    self:SetTrackEnabled(Game.me.bTraceLineActive)
    self:processCacheCmd()
end

function TraceLine:processCacheCmd()
    for _, _cmd in ipairs(self._cache_cmd) do
        for cmdName, cmdParam in pairs(_cmd) do
            if cmdName == "UpdateTrackByPath" then
                self:UpdateTrackByPath(cmdParam[1], cmdParam[2])
            elseif cmdName == "UpdateTrackByTailing" then
                self:UpdateTrackByTailing(cmdParam[1], cmdParam[2], cmdParam[3], cmdParam[4])
            end
        end
    end
    self._cache_cmd = {}
end

function TraceLine:initNiagara()
    local ThisNiagaraComponentID = self.CppEntity:KAPI_Actor_GetComponentByClassName("NiagaraComponent")
    if IsValidID(ThisNiagaraComponent) then
        self.CppEntity:KAPI_NiagaraID_SetForceLocalPlayerEffect(ThisNiagaraComponentID, true)
        self.bNiagaraRdy = true
    end
end

function TraceLine:IsValid()
    if not self.bNiagaraRdy then
        -- 加载中, 是有效的
        return true
    end
    return self.bInWorld
end

function TraceLine:SetTrackEnabled(bEnabled)
    if not self.bInWorld then--self:IsValid() then
        return
    end

    self.CppEntity:KAPI_TrackSpline_SetTrackEnabled(bEnabled)
end

function TraceLine:UpdateTrackByPath(TrackPath, bForceUpdate)
    if not self.bInWorld then
        local _cmd = {}
        _cmd["UpdateTrackByPath"] = {TrackPath, bForceUpdate}
        table.insert(self._cache_cmd, _cmd)
        return
    end
    if not self:IsValid() then
        return
    end

    Game.me:SetTraceLineActive(true)
    self.CppEntity:KAPI_TrackSpline_SetPathMaxLength(9999999)  -- todo 观察一下会不会有性能压力?
    self.CppEntity:KAPI_TrackSpline_UpdateTrackByPath(TrackPath, bForceUpdate)
    -- self.DebugTrackPoints = TrackPath
end

function TraceLine:UpdateTrackByTailing(Path, MoveStep, CurLocation, bForceUpdate)
    if bForceUpdate == nil then
        bForceUpdate = false
    end
    if not self.bInWorld then
        local _cmd = {}
        _cmd["UpdateTrackByTailing"] = {Path, MoveStep, CurLocation, bForceUpdate}
        table.insert(self._cache_cmd, _cmd)
        return
    end
    if not self:IsValid() then
        return
    end

    Game.me:SetTraceLineActive(true)
    self.CppEntity:KAPI_TrackSpline_SetPathMaxLength(9999999)  -- todo 观察一下会不会有性能压力?
    self.CppEntity:KAPI_TrackSpline_UpdateTrackByTailing(Path, MoveStep, CurLocation)
end

-- function TraceLine:SetActorVisibleDirect(bVisible)
--     if not self.bInWorld then
--         return
--     end

--     if not bVisible then
--         self.CppEntity:KAPI_TrackSpline_SetTrackEnabled(false)
--     else
--         self.CppEntity:KAPI_TrackSpline_SetTrackEnabled(Game.me.bTraceLineActive)
--     end
-- end
