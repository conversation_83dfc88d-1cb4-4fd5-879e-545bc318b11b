local CollisionConst = kg_require("Shared.Const.CollisionConst")
local LuaMulticastDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaMulticastDelegate")
local UStaticMeshComponent = import("StaticMeshComponent")

---@class LocalScenePlacementEntity : LocalEntityBase
local LocalScenePlacementEntity = DefineLocalEntity("LocalScenePlacementEntity", LocalEntityBase, {
	SimpleViewControlComponent,
})

LocalScenePlacementEntity:Register("LocalScenePlacementEntity")

LocalScenePlacementEntity.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {
	CollisionConst.COLLISION_PRESET_NAMES.FURNITURE_PRESET,
	CollisionConst.COLLISION_PRESET_NAMES.FURNITURE_PRESET
}
function LocalScenePlacementEntity:ctor()
	self.presetID = self.presetID or 0
	self.yaw = self.yaw or nil
	
	self.OnActorReady = LuaMulticastDelegate.new()
end

function LocalScenePlacementEntity:dtor()

end

function LocalScenePlacementEntity:AfterEnterWorld()
	self:RefreshFurnitureMesh()

	self.OnActorReady:Broadcast(self)
end

function LocalScenePlacementEntity:BeforeExitWorld()
	if self.actorMeshLoadHandle then
		Game.AssetManager:CancelLoadAsset(self.actorMeshLoadHandle)
		self.actorMeshLoadHandle = nil
	end
end

---@public method 获取配置ID
function LocalScenePlacementEntity:GetConfigID()
	return self.presetID
end

---@private
function LocalScenePlacementEntity:RefreshFurnitureMesh()
	if self.actorMeshLoadHandle then
		Game.AssetManager:CancelLoadAsset(self.actorMeshLoadHandle)
		self.actorMeshLoadHandle = nil
	end

	local presetConfig = Game.TableData.GetSceneCustomListDataRow(self.presetID)
	self.actorMeshLoadHandle = Game.AssetManager:AsyncLoadAssetID(presetConfig.Path, self, "OnFurnitureMeshLoaded")
end

function LocalScenePlacementEntity:OnFurnitureMeshLoaded(loadID, assetID)
	if assetID == 0 then
		return
	end

	local presetConfig = Game.TableData.GetSceneCustomListDataRow(self.presetID)
	local scale = presetConfig.Scale

	local UMeshComponentClassId = Game.ObjectActorManager:GetIDByClass(UStaticMeshComponent)
	local MeshID = self.CppEntity:KAPI_Actor_GetComponentByClassID(UMeshComponentClassId)
	self.CppEntity:KAPI_SceneID_SetRelativeScale(MeshID, scale, scale, scale)
	self.CppEntity:KAPI_MeshID_EmptyOverrideMaterials(MeshID)
	self.CppEntity:KAPI_StaticMeshID_SetStaticMesh(MeshID, assetID)
end


---@public method
function LocalScenePlacementEntity:SetMeshCustomDepthAndStencilValue(depth, value)
	local boxMeshComps = self.CppEntity:KAPI_Actor_GetComponentListByClassName("StaticMeshComponent")
	for _, compID in ipairs(boxMeshComps:ToTable()) do
		self.CppEntity:KAPI_PrimitiveID_SetCustomDepthAndStencilValue(compID, depth, value)
	end
end


return LocalScenePlacementEntity
