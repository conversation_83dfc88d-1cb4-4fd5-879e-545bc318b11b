local ESplineCoordinateSpace = import("ESplineCoordinateSpace")
local KismetMathLibrary = import("KismetMathLibrary")
local SceneCaptureComponent2D = import("SceneCaptureComponent2D")

---@class LocalSceneEntity : LocalEntityBase
local LocalSceneEntity = DefineLocalEntity("LocalSceneEntity", LocalEntityBase)

LocalSceneEntity:Register("LocalSceneEntity")

-- 场景entity
LocalSceneEntity.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {}
function LocalSceneEntity:ctor()
	self.sceneConfigID = self.sceneConfigID or nil
	
	--region 兼容一下选角不好改的蓝图函数
	self.cameraCurrentDist = 0
	--endregion
end

function LocalSceneEntity:BeforeExitWorld()
	if self.updateCameraTimerId then
		Game.TimerManager:StopTimerAndKill(self.updateCameraTimerId)
		self.updateCameraTimerId = nil
	end
end

--region 组件相关接口
---@public method 获取配置ID
function LocalSceneEntity:GetConfigID()
	return self.sceneConfigID
end

---@public method 显隐
function LocalSceneEntity:SetVisible(visible)
	self.CppEntity:KAPI_Actor_SetActorHiddenInGame(not visible)
end

---@public method 获取相应tag对应的组件世界位置
function LocalSceneEntity:GetComponentTagLocation(tagName)
	local componentID = self:GetComponentIDByTag(tagName)
	return self.CppEntity:KAPI_Component_K2_GetComponentLocation(componentID)
end

---@public method 获取相应tag对应的组件局部位置
function LocalSceneEntity:GetComponentTagRelativeLocation_P(tagName)
	local componentID = self:GetComponentIDByTag(tagName)
	local x, y, z = self.CppEntity:KAPI_SceneID_GetRelativeLocation_P(componentID)
	return x, y, z
end

---@public method 获取相应tag对应的组件局部旋转
function LocalSceneEntity:GetComponentTagRelativeRotation_P(tagName)
	local componentID = self:GetComponentIDByTag(tagName)
	local pitch, yaw, roll = self.CppEntity:KAPI_SceneID_GetRelativeRotation_P(componentID)
	return pitch, yaw, roll
end

---@public method 
function LocalSceneEntity:SetComponentTagRelativeLocation_P(tagName, x, y, z)
	local comID = self:GetComponentIDByTag(tagName)
	self.CppEntity:KAPI_SceneID_SetRelativeLocation(comID, x, y, z)
end

---@public method
function LocalSceneEntity:SetComponentTagRelativeRotation_P(tagName, pitch, yaw, roll)
	local comID = self:GetComponentIDByTag(tagName)
	self.CppEntity:KAPI_SceneID_SetRelativeRotation(comID, pitch, yaw, roll)
end

---@public method
function LocalSceneEntity:SetComponentTagWorldLocation(tagName, x, y, z)
	local comID = self:GetComponentIDByTag(tagName)
	self.CppEntity:KAPI_SceneID_SetWorldLocation(comID, x, y, z)
end


---@public method 获取组件，根据tag
function LocalSceneEntity:GetComponentIDByTag(tagName)
	local comList = self.CppEntity:KAPI_Actor_GetComponentListByTag(tagName)
	if comList:Num() > 0 then
		return comList:Get(0)
	end
	
	return nil
end

---@public method 获取ActorID
function LocalSceneEntity:GetActorID()
	return self.CppEntity:KAPI_GetActorID()
end

---@public method 获取组件，根据tag index
function LocalSceneEntity:GetComponentIDByTagAndIndex(tagName, index)
	local comList = self.CppEntity:KAPI_Actor_GetComponentListByTag(tagName)
	return comList:Get(index-1)
end

---@public method 获取场景中的相机
function LocalSceneEntity:GetCameraActorID(cameraTag)
	local CameraManager = Game.CameraManager
	local SceneActorID = self.CppEntity:KAPI_GetActorID()
	if CameraManager then
		if cameraTag then
			local Result = CameraManager:KAPI_Camera_GetCurrentSceneBaseCameraActorFromSceneActorIDByTag(SceneActorID, cameraTag)
			if Result ~= KG_INVALID_ID then
				return Result
			end
		else
			local Result = CameraManager:KAPI_Camera_GetCurrentSceneBaseCameraActorFromSceneActorID(SceneActorID)
			if Result ~= KG_INVALID_ID then
				return Result
			end
		end
	end

	return SceneActorID
end

---@public method
function LocalSceneEntity:GetComponentTagLocationAndRotationAndScale(tagName)
	local comID = self:GetComponentIDByTag(tagName)
	local location
	local rotate
	local scale
	if comID and comID>0 then
		location = self.CppEntity:KAPI_SceneID_K2_GetComponentLocation(comID)
		rotate = self.CppEntity:KAPI_SceneID_K2_GetComponentRotation(comID)
		scale = self.CppEntity:KAPI_SceneID_K2_GetComponentScale(comID)
	else
		location = FVector(0,0,0)
		rotate = FRotator(0, 0, 0)
		scale = FVector(1,1,1)
	end

	return location, rotate, scale
end

---@public method
function LocalSceneEntity:GetComponentTagRelativeTransform(tagName)
	local comID = self:GetComponentIDByTag(tagName)
	return self.CppEntity:KAPI_SceneID_GetRelativeTransform(comID)
end

---@public method
function LocalSceneEntity:CaptureScene()
	local comID = self.CppEntity:KAPI_Actor_GetComponentByClass(SceneCaptureComponent2D)
	self.CppEntity:KAPI_SceneCapture2DID_CaptureScene(comID)
end

---@public method
function LocalSceneEntity:CaptureAddShowOnlyActors(actorID)
	local comID = self.CppEntity:KAPI_Actor_GetComponentByClass(SceneCaptureComponent2D)
	self.CppEntity:KAPI_SceneCaptureID_AddShowOnlyActors(comID, actorID)
end
---@public method
function LocalSceneEntity:CaptureClearShowOnlyActors()
	local comID = self.CppEntity:KAPI_Actor_GetComponentByClass(SceneCaptureComponent2D)
	self.CppEntity:KAPI_SceneCaptureID_ClearShowOnlyActors(comID)
end

---@public method
function LocalSceneEntity:GetComponentLocationByComponentID(comID)
	local location
	if comID and comID>0 then
		location = self.CppEntity:KAPI_SceneID_K2_GetComponentLocation(comID)
	end
	return location
end

---@public method
function LocalSceneEntity:SetComponentWorldLocationByID(comID, x, y, z)
	if comID and comID>0 then
		self.CppEntity:KAPI_SceneID_SetWorldLocation(comID, x, y, z)
	end
end
--endregion




--region 选角
function LocalSceneEntity:ChangeCamera(offset)
	local location = self.CppEntity:KAPI_Spline_GetLocationAtSplinePoint(1, ESplineCoordinateSpace.Local)
	location = location + offset
	self.CppEntity:KAPI_Spline_SetLocationAtSplinePoint(1, location, ESplineCoordinateSpace.Local, true)
end

function LocalSceneEntity:UpdateCamera(distance)
	local childActorCompIDs = self.CppEntity:KAPI_Actor_GetComponentsByTag(import("ChildActorComponent"), "camera")
	local childActorComponent = childActorCompIDs:Get(0)
	local targetTime = distance
	local distDelta = targetTime - self.cameraCurrentDist
	local startPos = self.cameraCurrentDist
	local curveIDs = self.CppEntity:KAPI_TimelineComponent_GetAllCurves()
	if curveIDs:Num() > 0 then
		local curve = curveIDs:Get(0)
		local currentTime, curveMaxTime = LuaScriptAPI.KAPI_CurveBase_GetTimeRange(curve)
		if self.updateCameraTimerId then
			Game.TimerManager:StopTimerAndKill(self.updateCameraTimerId)
		end
		self.updateCameraTimerId = Game.TimerManager:TickTimer(function(deltaTime)
			currentTime = currentTime + deltaTime / 1000
			if currentTime > curveMaxTime then
				Game.TimerManager:StopTimerAndKill(self.updateCameraTimerId)
				self.updateCameraTimerId = nil
				return
			end
			local timeValue = LuaScriptAPI.KAPI_CurveFloat_GetFloatValue(curve, currentTime)
			timeValue = math.clamp(timeValue * distDelta + startPos, 0, 1)
			self.cameraCurrentDist = timeValue
			local spineLocation = self.CppEntity:KAPI_Spline_GetLocationAtTime(timeValue, ESplineCoordinateSpace.World, false)
			self.CppEntity:KAPI_SceneID_SetWorldLocation(childActorComponent, spineLocation.X, spineLocation.Y, spineLocation.Z)
			local fov = KismetMathLibrary.Lerp(30, 45, timeValue)
			Game.CameraManager:SetUICameraFOV(fov)
		end, -1)
	end
end
--endregion

return LocalSceneEntity
