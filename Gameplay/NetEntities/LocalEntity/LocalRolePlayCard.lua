local CollisionConst = kg_require("Shared.Const.CollisionConst")
local LuaMulticastDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaMulticastDelegate")
local LocalRolePlayCard = DefineLocalEntity("LocalRolePlayCard", LocalEntityBase)
LocalRolePlayCard:Register("LocalRolePlayCard")
LocalRolePlayCard.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {
    CollisionConst.COLLISION_PRESET_NAMES.SCENE_ACTOR_BLOCK_ALL, 
    CollisionConst.COLLISION_PRESET_NAMES.SCENE_ACTOR_BLOCK_ALL
}

function LocalRolePlayCard:ctor()
    self.OnActorReady = LuaMulticastDelegate.new()
end

function LocalRolePlayCard:GetActorBPClassPath()
    return Game.TableData.GetRPGameConstDataRow("CARD_BP_PATH")
end

function LocalRolePlayCard:AfterEnterWorld()
    self.OnActorReady:Broadcast(self)
end

function LocalRolePlayCard:OnPlayerClicked()
    Log.Debug(">>LocalRolePlayCard OnPlayerClicked", self.RowIndex, self.ColIndex)
    Game.RolePlaySystem.sender:ReqRPGameMove(self.RowIndex, self.ColIndex)
end

function LocalRolePlayCard:PlayOpenAnim()
    self.CppEntity:KAPI_ActorSequence_PlaySequence()
end

function LocalRolePlayCard:ResetProps(props)
    for k, v in pairs(props) do
        self[k] = v
    end
end