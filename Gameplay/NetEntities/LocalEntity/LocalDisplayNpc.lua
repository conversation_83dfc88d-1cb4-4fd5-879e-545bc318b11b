kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")


local CollisionConst = kg_require("Shared.Const.CollisionConst")
---@class LocalDisplayNpc
local LocalDisplayNpc = DefineLocalEntity("LocalDisplayNpc", LocalEntityBase, {
    NpcViewControlComponent,
})

LocalDisplayNpc:Register("LocalDisplayNpc")

LocalDisplayNpc.DefaultWalkSpeed = 300
LocalDisplayNpc.DefaultRunSpeed = 600
LocalDisplayNpc.DefaultSprintSpeed = 600

LocalDisplayNpc.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {CollisionConst.COLLISION_PRESET_NAMES.MOVABLE_UNIT_WITH_LOCAL_DRIVE_PRESET, CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET}
function LocalDisplayNpc:ctor()
    self.HeadInfoActorType = "DirectorDisplayNpc"
end

function LocalDisplayNpc:initNpcData()
    local defaultWalkSpeed = LocalDisplayNpc.DefaultWalkSpeed
    local entityConfigData = self:GetEntityConfigData()
    if entityConfigData ~= nil and entityConfigData.WalkSpeed ~= nil then
        defaultWalkSpeed = entityConfigData.WalkSpeed
    end
    
    self.CurSpeed = defaultWalkSpeed
end

function LocalDisplayNpc:AfterEnterWorld()
    if self.CppEntity and self.CppEntity.KAPI_Character_SimpleSetActorStickGround ~= nil then
        self.CppEntity:KAPI_Character_SimpleSetActorStickGround()
    end
    if self.bInWorld then
        Game.HeadInfoManager:RegisterHeadInfo(self:uid())
    end
end

function LocalDisplayNpc:BeforeExitWorld()
    Game.HeadInfoManager:UnRegisterHeadInfo(self:uid())
end

function LocalDisplayNpc:GetCreateActorLocation()
    local Position = {X = self.Position[1], Y = self.Position[2], Z = self.Position[3]}
    --根据模型配置取高度
	local OffsetZ = self:GetConfigModelCapsuleHalfHeightWithScale()
	Position.Z = Position.Z + OffsetZ
    return Position, self.Rotation
end

function LocalDisplayNpc:EnterWorld()
    self:initNpcData()
    LocalEntityBase.EnterWorld(self)
end

function LocalDisplayNpc:GetConfigFacadeControlData()
    local facadeId = nil
    local entityConfigData = self:GetEntityConfigData()
    if entityConfigData == nil then
        return nil
    end

    -- 这块应该往变身逻辑流程里面做, 不应该往获取配置的接口里面做(基础接口的语义应该就是获取配置数据) @刘凡 20240624
    --if entityConfigData.State and #entityConfigData.State >= self.State then
    --    facadeId = entityConfigData.State[self.State]
    --else
    --    facadeId = entityConfigData.FacadeControlID
    --end
    facadeId = entityConfigData.FacadeControlID

    if facadeId == nil then
        return nil
    end

    return Game.TableData.GetFacadeControlDataRow(facadeId)
end

function LocalDisplayNpc:GetEntityConfigData()
	local MonsterData = Game.TableData.GetMonsterDataRow(self.TemplateID)
	if MonsterData then
		-- 最新的Npc支持配置MonsterID, 所以只要在Monster表中有数据即以该数据为准
		return MonsterData
	end

    if self.NpcType == Enum.ENpcTypeData.Task then
        return Game.TableData.GetNpcInfoDataRow(self.TemplateID)
    end

    if self.NpcType == Enum.ENpcTypeData.Passerby  then
        return  Game.TableData.GetPasserbyNpcDataRow(self.TemplateID)
    end

    if self.NpcType == Enum.ENpcTypeData.Vehicle  then
        return Game.TableData.GetVehicleNpcDataRow(self.TemplateID)
    end
end

