kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local SimpleViewControlComponent = require("Gameplay.NetEntities.Comps.ViewControl.SimpleViewControlComponent")
local ViewControlGazeComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlGazeComponent")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local FashionComponent = kg_require("Gameplay.NetEntities.Comps.FashionComponent")

---@class LocalCutSceneActor
local LocalCutSceneActor = DefineLocalEntity("LocalCutSceneActor", LocalEntityBase, {
    SimpleViewControlComponent,
	ViewControlGazeComponent,
	FashionComponent
})

LocalCutSceneActor:Register("LocalCutSceneActor")

function LocalCutSceneActor:ctor()

end

function LocalCutSceneActor:dtor()

end

function LocalCutSceneActor:GetConfigModelID()
    return self.FacadeData.ModelID
end

function LocalCutSceneActor:CalculateViewRoleImportance()
    return WorldViewBudgetConst.FIRST_ROLE_IMPORTANCE
end

function LocalCutSceneActor:EnterWorld()
	LocalEntityBase.EnterWorld(self)
    
	self.CppEntity:KAPI_SkeletalMesh_SetUpdateBoundWithFirstSkinnedBoneOnAllMesh(true)
    local MainMeshID = self.CppEntity:KAPI_Actor_GetMainMesh()

    if self.NotResetMeshLoc then
        -- fuyu10: LevelSequenceManager 的 Sequence 资源在制作时直接使用了 Npc 作为预览，所以在游戏内生成时不需要 Reset Location 和 Rotation
    else
        self.CppEntity:KAPI_SceneID_SetRelativeTransform(MainMeshID,0,0,0,0,0,0,1,1,1)
    end

    --TODO:目前ModelLib中B级NPC没有单独区分出来使用的统一的模版，CutScene这里先指定
    if not self:isPlayer() then
		self.AnimClass = slua.loadClass(ViewResourceConst.ABP.ABP_NPC_M2)
        local AnimID = Game.ObjectActorManager:GetIDByClass(self.AnimClass)
        self.CppEntity:KAPI_SkeletalMeshID_SetAnimClass(MainMeshID, AnimID)
    end

     if self.Callback then
         -- Cutscene Manager 那边在迭代, 迭代之后可以直接传Entity.
         self.Callback(self.eid, self.Tag, self.BindingID)
     end
end

---@private
---@return boolean
function LocalCutSceneActor:isPlayer()
    return (self.Tag == "player") or (self.Tag == "man") or (self.Tag == "woman")
end
