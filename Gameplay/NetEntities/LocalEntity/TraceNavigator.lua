kg_require("Gameplay.NetEntities.LocalEntity.LocalNpcBase")
local parallel_behavior_control_const = kg_require("Shared.Const.ParallelBehaviorControlConst")
local EMovePosture = parallel_behavior_control_const.EMovePosture

---@class TraceNavigator
local TraceNavigator = DefineLocalEntity("TraceNavigator", LocalNpcBase, {
    TraceNavigatorComponent
}
)

TraceNavigator:Register("TraceNavigator")


TraceNavigator.SpeedTick = 0.5 * 1000


function TraceNavigator:ctor()
    self.Position =  self.InTransform:GetLocation()
    self.Rotation = self.InTransform:GetRotation():Rotator()

    self.NavigatorSpeedTimer = nil
    self.TrailFxPath = nil
    self.TrailFxEID = nil
    self.TrailHeight = nil
    self.bInRush = false
    self.SprintExtraSpeed = 0
end

function TraceNavigator:dtor()

end

function TraceNavigator:LoadActor()
    LocalNpcBase.LoadActor(self)
end

function TraceNavigator:BeforeExitWorld()
    self:cancelNavigatorSpeedTick()
    self:destroyNiagara()

    self:EndOnGroundYawControl()
end

function TraceNavigator:AfterEnterWorld()
    self.SprintExtraSpeed = Game.TableData.GetConstDataRow("TASKTRACK_SPRINTEXTRASPEED") or 200
    self.TrailFxPath = Game.TableData.GetConstDataRow("TASKTRACK_TRAILEFFECTPATH") or ""
    self.TrailHeight = Game.TableData.GetConstDataRow("TASKTRACK_TRAILHEIGHT") or 50
    self:FinishRush()

    self:StartOnGroundYawControl()
end

function TraceNavigator:prepareNiagara()
    self:destroyNiagara()
    local NiagaraTrans = FTransform(FQuat(0, 0, 0, 1), FVector(0, 0, self.TrailHeight), FVector(1, 1, 1))
    local InTransform = M3D.ToTransform(NiagaraTrans)
    self.TrailFxEID = self:PlayNiagaraEffectAttached(self.TrailFxPath, "Body_Bone", InTransform)
end

function TraceNavigator:destroyNiagara()
    if self.TrailFxEID ~= nil then
        self:DeactivateNiagaraSystem(self.TrailFxEID)
        self.TrailFxEID = nil
    end
end

function TraceNavigator:checkNiagara()
    -- 确认下这个逻辑
    return self.TrailFxEID and Game.EffectManager:IsValidNiagaraEffectId(self.TrailFxEID)
end

function TraceNavigator:IsValid()
    if self.TrailFxPath == nil then
        -- 加载中, 是有效的
        return true
    end
    return self.bInWorld
end

function TraceNavigator:startNavigatorSpeedTick(tickInterval)
    self:cancelNavigatorSpeedTick()
    self.NavigatorSpeedTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:SetNavigateMoveSpeed()
    end, tickInterval, 1)
end

function TraceNavigator:cancelNavigatorSpeedTick()
    if self.NavigatorSpeedTimer then
        Game.TimerManager:StopTimerAndKill(self.NavigatorSpeedTimer)
        self.NavigatorSpeedTimer = nil
    end
end

-- 设置自己的移动速度
function TraceNavigator:SetNavigateMoveSpeed()
    if not self.bInWorld then
        return
    end
    if self:IsInvisible() then
        return
    end
    if not Game.me then
        return
    end
    local TargetSpeed = Game.me:GetSpeed()
    if not TargetSpeed then
        return
    end
    --self:Debug("LYL DEBUG ", TargetSpeed)
    if self.bInRush then
        self.CppEntity:KAPI_Movement_SetLocoMaxSpeed(TargetSpeed + self.SprintExtraSpeed)
    else
        self.CppEntity:KAPI_Movement_SetLocoMaxSpeed(TargetSpeed)
    end
    self:startNavigatorSpeedTick(TraceNavigator.SpeedTick)
end

function TraceNavigator:SetLocalActorVisible(bVisible)
    if not self.bInWorld then
        return
    end
    if bVisible then
        self:SetActorVisible(Enum.EInVisibleReasons.Stealth)
        if not self:checkNiagara() then
            self:prepareNiagara()
        end
        self:startNavigatorSpeedTick(TraceNavigator.SpeedTick)
    else
        self:SetActorInVisible(Enum.EInVisibleReasons.Stealth)
        self:cancelNavigatorSpeedTick()
    end
end

function TraceNavigator:BeginRush()
    if self.bInRush then
        return
    end
    self.bInRush = true
    self:SetNavigateMoveSpeed()
end

function TraceNavigator:BeginRushPosture()
    if self.bInRush then
        self:NavigatorChangeMovePosture(EMovePosture.Run)
    end
end

function TraceNavigator:FinishRush()
    if not self.bInRush then
        return
    end
    self.bInRush = false
    self:NavigatorChangeMovePosture(EMovePosture.Walk)
    self:SetNavigateMoveSpeed()
end

function TraceNavigator:NavigatorChangeMovePosture(InMovePosture)
    self:SetAnimMovePostureAndPlayRate(InMovePosture, 1.0)
end
