-- 定义技能编辑器预览角色类
kg_require("Gameplay.NetEntities.LocalEntity.PreCreateCharacterEntity.PreCreateCharacterBase")
kg_require("Gameplay.NetEntities.Comps.MoveComponent")
kg_require("Gameplay.NetEntities.Comps.ComplexLocomotionControlComponent")
kg_require("Gameplay.NetEntities.Comps.HitFeedbackComponent")
kg_require("Gameplay.NetEntities.Comps.TakeDamageComponent")
kg_require("Gameplay.NetEntities.Comps.StateComponent")
kg_require("Gameplay.NetEntities.Comps.WeaponManagerComponent")
kg_require("Gameplay.NetEntities.Comps.MorphComponent")
kg_require("Gameplay.Combat.Comps.SkillComponentNew")
kg_require("Gameplay.Combat.Comps.AbilityComponent")
kg_require("Gameplay.Combat.Comps.ClientTimelineComponent")
kg_require("Gameplay.NetEntities.Comps.LUnitProxyComponent")
kg_require("Gameplay.Combat.Comps.BuffComponentNew")
kg_require("Gameplay.Combat.Comps.CombatEffectComponent")
kg_require("Gameplay.NetEntities.Comps.InteractableDetectComponent")
kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlDynamicMeshComponent")
kg_require("Gameplay.NetEntities.Comps.LocalPerformPetComponent")


local CollisionConst = kg_require("Shared.Const.CollisionConst")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local WEAK_FORCE_CONTROL_REASON_TAGS = kg_require("Shared.Const.ParallelBehaviorControlConst").WEAK_FORCE_CONTROL_REASON_TAGS
local EMoveDriveMode = import("EMoveDriveMode")
local equipUtils = kg_require("Shared.Utils.EquipUtils")
local VIEW_BUDGET_CHARACTER_TYPE = WorldViewBudgetConst.VIEW_BUDGET_CHARACTER_TYPE

---@class BSAEPreview
local BSAEPreview = DefineLocalEntity("BSAEPreview", PreCreateCharacterBase, {
    MoveComponent,
    ComplexLocomotionControlComponent,
    HitFeedbackComponent,
    TakeDamageComponent,
    StateComponent,
	WeaponManagerComponent,
    MorphComponent,
    SkillComponentNew,
    AbilityComponent,
	ClientTimelineComponent,
    LUnitProxyComponent,
    BuffComponentNew,
    CombatEffectComponent,
    InteractableDetectComponent,
	ViewControlDynamicMeshComponent,
	LocalPerformPetComponent
})

BSAEPreview:Register("BSAEPreview")
BSAEPreview.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {CollisionConst.COLLISION_PRESET_NAMES.MOVABLE_UNIT_WITH_LOCAL_DRIVE_PRESET, CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET}

function BSAEPreview:ctor()
    if (Game.me == nil) then
        Game.me = self
    end
    self.FinalOwnerID = self.eid
    self.CurSpeed = 0.0
    self.IsDead = false
    self.ObserverType = 0
    self.AnimAssetID = tostring(self.ThePreview.AppearanceID)
    self.TemplateID = self.ThePreview.AppearanceID
    self.isAvatar = true
    self.Profession = self.ThePreview.AppearanceID
	self.Sex = self.ThePreview.Sex or 0

    -- @shijingzhe:编辑态都是最高优先级
    self.ViewRoleImportance = WorldViewBudgetConst.FIRST_ROLE_IMPORTANCE
	self.teamInfoList = {}
end

function BSAEPreview:LoadActor()
	PreCreateCharacterBase.LoadActor(self)
    if (self.TemplateID > 1200000) and (self.TemplateID < 1299999) then
        local PlayerData = equipUtils.GetPlayerBattleDataRow(self.TemplateID, self.Sex or 0)
        if PlayerData == nil then
            return nil
        end

        --组装外观
		self:RefreshBodyPart_WholeAvatar(PlayerData.FacadeControlID)
    else
        self.NpcType = Enum.ENpcTypeData.Monster

        local EFD = self:GetEntityConfigData()
        if (EFD ~= nil) and (EFD.FacadeControlID ~= nil) then
            local FCD = Game.TableData.GetFacadeControlDataRow(EFD.FacadeControlID)
            if (FCD ~= nil) then
				self:RefreshBodyPart_WholeAvatar(EFD.FacadeControlID)
            end
        end
    end
end

function BSAEPreview:EnterWorld()
    if (self.TemplateID < 1200000) or (self.TemplateID > 1299999) then
        -- 在OnLoadActorFinish中的OnInitFinish阶段中ActorType会被修改回EAT_NPC, 这边重新写回一下
        self.NpcType = Enum.ENpcTypeData.Monster
    end

    PreCreateCharacterBase.EnterWorld(self)

    if IsValidID(self.CppEntity:KAPI_Actor_GetMovementComponent()) then
        self.MoveDriveModeFWB:SetWeakValue(EMoveDriveMode.DriveLocally, WEAK_FORCE_CONTROL_REASON_TAGS.LocalDisplayChar)
    end

    if (self.TemplateID > 1200000) and (self.TemplateID < 1299999) then
        self:CheckHoldWeapon()
    end

    if self == Game.me then
        Game.WorldManager:SetPivotActorForDynamicWaterWave(self:uid())
        Game.WorldManager:SetPivotActorForLocalWindField(self:uid())
        Game.WorldManager:SetPivotActorForDeformableLandscape(self:uid())
        Game.WorldManager:SetKGEngineCorePlayerActor(self:uid())
    end
end

function BSAEPreview:GetEntityConfigData()
	if self.TemplateID == nil then
		-- 技能编辑器这边，由于apparentComponent ctor先执行，导致其中调用这个接口时，BSAEPreview未进行初始化，存在参数依赖问题，这里先设值
		self.TemplateID = self.ThePreview.AppearanceID
	end
	local MonsterData = Game.TableData.GetMonsterDataRow(self.TemplateID)
	if MonsterData then
		-- 最新的Npc支持配置MonsterID, 所以只要在Monster表中有数据即以该数据为准
		return MonsterData
	end

    if (self.NpcType == Enum.ENpcTypeData.Task) then
        return Game.TableData.GetNpcInfoDataRow(self.TemplateID)
    end

    if (self.NpcType == Enum.ENpcTypeData.Passerby) then
        return  Game.TableData.GetPasserbyNpcDataRow(self.TemplateID)
    end

    if (self.NpcType == Enum.ENpcTypeData.Vehicle) then
        return Game.TableData.GetVehicleNpcDataRow(self.TemplateID)
    end

    return equipUtils.GetPlayerBattleDataRow(self.TemplateID, self.Sex or 0)
end



function BSAEPreview:AddCommandCache(CommandNames, ...)

end

function BSAEPreview:ExecuteCommandCaches()

end



-- region FakeAPI
function BSAEPreview:IsInSpace()
	return true
end

function BSAEPreview:IsLocoMoving()
	return false
end

function BSAEPreview:DisableLocoJump()

end

function BSAEPreview:GetPetMinDragonEid()
	return 0
end



function BSAEPreview:StartListenPathFollowResult(InEntity, InCallback)

end

function BSAEPreview:CanParallelBehaviorTransfer(InType)
	return true
end

function BSAEPreview:TryCancelJumpInterrupSkill()

end

function BSAEPreview:DetachFromUEComponent()

end

function BSAEPreview:GetCharacterTypeForViewBudget()
	return VIEW_BUDGET_CHARACTER_TYPE.PLAYER_SELF
end
