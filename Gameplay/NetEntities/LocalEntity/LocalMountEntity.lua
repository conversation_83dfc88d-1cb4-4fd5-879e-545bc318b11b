kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local parallelBehaviorControlConst = kg_require("Shared.Const.ParallelBehaviorControlConst")
local EMoveDriveMode = import("EMoveDriveMode")
local EMovePosture = parallelBehaviorControlConst.EMovePosture
local EMountType = import("EMountType")
local EMoveDriveRelation = import("EMoveDriveRelation")
local ELogicParentFeatureSynchronizeMask = import("ELogicParentFeatureSynchronizeMask")
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local SkeletalMeshComponent = import("SkeletalMeshComponent")

---@class LocalMountEntity
local LocalMountEntity = DefineLocalEntity("LocalMountEntity", LocalEntityBase, {
    NewViewControlAnimComponent,
    ViewControlAnimLibComponent,
    StaminaComponent,
    ComplexLocomotionControlComponent,
    MoveComponent,
    ViewControlAttachComponent,
    ViewControlFxComponent,
    ViewControlBaseComponent,
    PathFollowComponent,
	ViewControlVisibleComponent,
	NewViewControlMaterialComponent
})
LocalMountEntity:Register("LocalMountEntity")

-- 坐骑归属配置
LocalMountEntity.MountInitFunName = {
	[EMountType.Bike]  = "InitMountBikeData",
	[EMountType.Car]  = "InitMountCarData"
}

LocalMountEntity.DEFAULT_PRIMITIVE_COLLISION_PRESETS = {CollisionConst.COLLISION_PRESET_NAMES.MAIN_PLAYER_PRESET, CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET}

LocalMountEntity.MountInWaterDepth = 150
LocalMountEntity.MountSwimDepth = 200

function LocalMountEntity:ctor()
    self.ModelID = self.ModelID or nil
    self.MountDataCmpt = nil
    self.PassengerEntityIDs = {}
	self.MountInitData = self.MountInitData
	-- 是否启用 自行车追逐溅射水花效果(暂时只给剧情用)
	self.bikeWaterEffectTimer = nil
	self.frontWheelNiagaraID = nil
	self.curMovePostureNiagara = nil
	self.CurPlayMountAnimID = nil	-- 当前播放的坐骑动画ID
end

function LocalMountEntity:dtor()
    table.clear(self.PassengerEntityIDs)
end

function LocalMountEntity:AfterEnterWorld()
    self:doSetMoveDriveMode(EMoveDriveMode.DriveLocally)
	
	-- EnterWorld 之后可能存在 SkeletalMesh 没有准备好的情况，SkeletalMesh 准备好之后 BraodCast
	if not self.SKCompID then
		self.SKCompID = self.CppEntity:KAPI_Actor_GetComponentByClass(SkeletalMeshComponent)
	end
	-- 初始化坐骑
	self:InitMount()
	-- 坐骑入场完毕通知调用玩家接口
	local riderEntity = self:GetDriverEntity()
	if riderEntity then
		if self.isDestroyed then
			riderEntity:OnMountDestroyed()
		else
			if riderEntity ~= Game.me then
				self.LocoControlOperator:DoUpperControlLogic(Enum.LocoControlUpperControlLogic.P3MountUpperLogic)
			end
			
			riderEntity:OnMountEnterWorld()
		end
	end

	-- 两轮坐骑初始化（依赖的曲线数据）
	local funName = self.MountInitFunName[self.MountInitData.MountConfig.MountType]
	if funName then
		self[funName](self)
	end
	-- 水面监听
	if riderEntity == Game.me then
		self:SwitchOnWaterDetect(ViewControlConst.WATER_DETECT_DISTANCE_CM)
		self:SetNeedInWaterChangedNotify(true, LocalMountEntity.MountInWaterDepth, LocalMountEntity.MountSwimDepth)
	end
end

function LocalMountEntity:BeforeExitWorld()
	self:StopBikeWaterEffectTimer()
end

function LocalMountEntity:InitMount()
	-- 初始化姿态
	self.CppEntity:KAPI_Animation_InitMountBornPitch()
	-- 设置状态机过度时间
	self.CppEntity:KAPI_Animation_SetLocoCrossFadeTime(self.MountInitData.MountConfig.AnimCrossFadeTime)
	-- 渐入效果
	self:PlayFadeInEffect()
end

function LocalMountEntity:InitMountBikeData()
	-- 设置座位偏移
	self.CppEntity:KAPI_Animation_SetBikeOffset(self.MountInitData.HeadOffset, self.MountInitData.SeatOffset)
	-- 初始化 IK 计算参数
	self.CppEntity:KAPI_Animation_SetBikeParams(self.MountInitData.MountConfig.FrontMountLength, self.MountInitData.MountConfig.EndMountLength, self.MountInitData.MountConfig.FrontWheelRadius, self.MountInitData.MountConfig.EndWheelRadius,
		self.MountInitData.MountConfig.PitchHalfTime or 0.05, self.MountInitData.MountConfig.RootOffsetHalfTime or 0.1, self.MountInitData.MountConfig.FWheelName, self.MountInitData.MountConfig.RWheelName)
end

function LocalMountEntity:InitMountCarData()
	-- 设置四轮坐骑参数
	self.CppEntity:KAPI_Animation_SetCarPostureParam(self.MountInitData.MountConfig.FrontMountLength, self.MountInitData.MountConfig.EndMountLength, self.MountInitData.MountConfig.FrontWheelRadius, self.MountInitData.MountConfig.FWheelName, self.MountInitData.MountConfig.FWheelNameRight, 
		self.MountInitData.MountConfig.EndWheelRadius, self.MountInitData.MountConfig.RWheelName, self.MountInitData.MountConfig.RWheelNameRight,
		self.MountInitData.MountConfig.PitchHalfTime or 0.05, self.MountInitData.MountConfig.RollHalfTime or 0.05, self.MountInitData.MountConfig.RootOffsetHalfTime or 0.05)
end

function LocalMountEntity:OnEntityInWaterChanged()
	local riderEntity = self:GetDriverEntity()
	if riderEntity and riderEntity == Game.me then
		riderEntity:RequestStopRide(false)
	end
end

function LocalMountEntity:OnRiderEnteredAttachToMountStage()
	-- 设置Mesh和姿态计算参数
	local riderEntity = self:GetDriverEntity()
	if riderEntity then
		riderEntity.CppEntity:KAPI_Movement_SetRidingPostureParam(self.SKCompID, self.MountInitData.MountConfig.PitchHalfTime or 0.05, self.MountInitData.MountConfig.RootOffsetHalfTime or 0.1);
	end
	-- 更新坐骑碰撞和骑乘者一致 & 更新挂接关系
	self:UpdateMountRelationControl()
	self:UpdateCollisionByMountRider();
end

function LocalMountEntity:PlayFadeInEffect()
	if self.MountInitData.MountConfig.FadeInDissolveEffectID ~= 0 then
		self:SetVisibilityByDissolveDataEffect(self.MountInitData.MountConfig.FadeInDissolveEffectID, Enum.EInVisibleReasons.MountDissolveHidden)
	end
end

function LocalMountEntity:PlayFadeOutEffect()
	if self.MountInitData.MountConfig.FadeOutDissolveEffectID ~= 0 then
		self:SetVisibilityByDissolveDataEffect(self.MountInitData.MountConfig.FadeOutDissolveEffectID, Enum.EInVisibleReasons.MountDissolveHidden)
	end
end

function LocalMountEntity:GetConfigModelID()
    return self.ModelID
end

function LocalMountEntity:GetDriverEntity()
    return Game.EntityManager:GetEntityByIntID(self.MountInitData.RiderEntityID)
end

function LocalMountEntity:StopBikeWaterEffectTimer()
	if self.bikeWaterEffectTimer then
		Game.TimerManager:StopTimerAndKill(self.bikeWaterEffectTimer)
		self.bikeWaterEffectTimer = nil
	end
end

function LocalMountEntity:pri_DeactivateBikeNiagaraSystem()
	if self.frontWheelNiagaraID then
		self:DeactivateNiagaraSystem(self.frontWheelNiagaraID)
		self.frontWheelNiagaraID = nil
		self.curMovePostureNiagara = nil
	end
end

function LocalMountEntity:DoMountAssetCollect(AssetPathsToLoad)
	if not AssetPathsToLoad then return end
	if #AssetPathsToLoad > 0 then
		return self:BatchRequestAssetBeforeLoadActor(AssetPathsToLoad)
	end
end

function LocalMountEntity:UpdateMountRelationControl()
	local riderEntity = self:GetDriverEntity()
	if riderEntity then
		self:SetNeedLocoAnimStateChangedNotifyFromLocomotionSM(true)
		self:SetUsingLocoCrossfadeSynchronizeToLogicChilds(true)
		self.CppEntity:KAPI_Actor_EnableLogicFeatureSynchronizeToChilds(riderEntity.CharacterID, ELogicParentFeatureSynchronizeMask.AnimationLocoState)
		self.CppEntity:KAPI_Movement_SetMoveDriveRelation(EMoveDriveRelation.DriveMountAsCarrier, riderEntity.CharacterID)
	end
end

function LocalMountEntity:UpdateCollisionByMountRider()
	local riderEntity = self:GetDriverEntity()
	if riderEntity then
		local parentPresent, childPresent = unpack(riderEntity.DEFAULT_PRIMITIVE_COLLISION_PRESETS)
		self.CppEntity:KAPI_Actor_SetCollisionPresetForRootAndMeshComponents(
			false,
			CollisionConst.SET_PRESET_AND_DISABLE_OVERLAP, parentPresent,
			CollisionConst.SET_PRESET_AND_DISABLE_OVERLAP, childPresent
		)
	end
end

function LocalMountEntity:PlayMountInteractAnim(bGetIn, bRiderMoving)
	local AnimName = nil
	if bGetIn then
		if bRiderMoving then
			AnimName = bLeft and self.MountInitData.MountConfig.MountMoveGetInAnimName or self.MountInitData.MountConfig.MountMoveGetInAnimNameRight
		else
			AnimName = bLeft and self.MountInitData.MountConfig.MountGetInAnimName or self.MountInitData.MountConfig.MountGetInAnimNameRight
		end
	else
		AnimName = bRiderMoving and self.MountInitData.MountConfig.MountMoveGetOutAnimName or self.MountInitData.MountConfig.MountGetOutAnimName
	end
	if AnimName then
		-- 坐骑播放下坐骑动作时需要设置和地面碰撞
		if not bGetIn then
			self.CppEntity:KAPI_Actor_SetCollisionPresetForRootAndMeshComponents(
				false,
				CollisionConst.SET_PRESET_AND_DISABLE_OVERLAP, CollisionConst.COLLISION_PRESET_NAMES.MOVABLE_UNIT_WITH_LOCAL_DRIVE_PRESET,
				CollisionConst.SET_PRESET_AND_DISABLE_OVERLAP, CollisionConst.COLLISION_PRESET_NAMES.NO_COLLISION_COMPONENT_PRESET
			)
		end
		self.CurPlayMountAnimID = self:PlayAnimLibMontageCustomTag(ViewAnimConst.EAnimPlayReqTag.Mount, AnimName, nil, false, 0, 0.2, nil, nil, nil, true, self, "OnMountPlayAnimStop")
	end
end

function LocalMountEntity:StopMountInteractAnim()
	if self.CurPlayMountAnimID then
		self:StopAnimLibMontage(self.CurPlayMountAnimID, 0)
	end
end

function LocalMountEntity:OnMountPlayAnimStop(bInterrupt)
	self.CurPlayMountAnimID = nil
end

-- 是否启用坐骑溅射水花效果(目前只给自行车追逐使用)
function LocalMountEntity:SetIsEnableMountWaterEffect(bEnable)
	if not bEnable then
		self:StopBikeWaterEffectTimer()
		return
	end

	if self.bikeWaterEffectTimer then return end
	
	self.bikeWaterEffectTimer = Game.TimerManager:CreateTimerAndStart(function()
		if self.CppEntity:KAPI_Movement_GetMovementSpeed() < 10 then
			if self.frontWheelNiagaraID then
				self:pri_DeactivateBikeNiagaraSystem()
			end
			return
		end
		
		-- 溅射水花
		local delayedAnimMovePosture = self:GetDelayedAnimMovePosture()
		local niagaraPath = MOUNT_BIKE_RUN_EFFECT_PATH
		if not self.curMovePostureNiagara or self.curMovePostureNiagara ~= delayedAnimMovePosture then
			self:pri_DeactivateBikeNiagaraSystem()
			self.curMovePostureNiagara = delayedAnimMovePosture
			niagaraPath = delayedAnimMovePosture == EMovePosture.Sprint and MOUNT_BIKE_SPRINT_EFFECT_PATH or MOUNT_BIKE_RUN_EFFECT_PATH
		end

		-- self.CppEntity:KAPI_Movement_GetIsInWater()
		local bInWaterArea = true

		if bInWaterArea and not self.frontWheelNiagaraID then
			self.frontWheelNiagaraID = self:PlayNiagaraEffectAttached(niagaraPath, MOUNT_BIKE_FRONT_WHEEL_NAME, MOUNT_BIKE_WATER_NIAGARA_TRANSFORM, self.SKCompID, true)
		elseif not bInWaterArea and self.frontWheelNiagaraID then
			self:DeactivateNiagaraSystem(self.frontWheelNiagaraID)
		end
	end,  0.1 * 1000,-1)
end

function LocalMountEntity:GetDefaultLocoControlTemplateID()
	return Enum.LocoControlDefaultTemplate.LocalMount
end


function LocalMountEntity:IsUsedByMainPlayer()
	if Game.me == nil then
		return false
	end
	return self.OwnerID == Game.me:uid()
end