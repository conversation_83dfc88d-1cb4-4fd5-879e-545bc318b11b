---@class QuestComponent
QuestComponent = DefineComponent("QuestComponent")

---@class QuestTargetInfo @任务目标数据结构
---@field FinishCount number @完成数量
---@field IsFinished boolean @是否完成

---@class QuestInfo @任务数据结构
---@field NowRingQuestID number @任务环ID
---@field RingStatus number @状态
---@field NowQuestTargets table<number,QuestTargetInfo> @状态
---@field RingStartAt number @环开始时间
---@field RingFailCount number @失败次数
---@field RingFinishedMap table<number,number> @环任务完成映射
---@field RingSubTargetFinishedMap table<number,number> @子目标完成映射
---@field Version number @任务版本
---@field SavePointQuestID number @任务存档标记
---@field NowPlaneID number @当前位面ID（用来做返回位面）

---@param questList table<number,QuestInfo> @剧情总任务数据
function QuestComponent:OnMsgQuestListInfo(questList)
    return Game.QuestSystem:OnMsgQuestListInfo(questList)
end

---@param questList table<number,QuestInfo> @系统总任务数据
function QuestComponent:OnMsgOtherQuestListInfo(questList)
    return Game.QuestSystem:OnMsgOtherQuestListInfo(questList)
end

---@param questList table<number,QUEST_FINISHED_INFO> @已完成的任务数据
function QuestComponent:OnMsgFinishedQuestList(QuestFinishedInfoDict)
    return Game.QuestSystem:OnMsgFinishedQuestList(QuestFinishedInfoDict)
end

---@param questList table<number,QuestInfo> @已放弃的任务数据
function QuestComponent:OnMsgAbandonQuestList(questList)
    return Game.QuestSystem:OnMsgAbandonQuestList(questList)
end

---@param ringList number[] @环数组
function QuestComponent:OnMsgQuestActiveList(ringList)
    return Game.QuestSystem:OnMsgQuestActiveList(ringList)
end

---@param ringID number @环ID
function QuestComponent:OnMsgQuestActive(ringID)
    return Game.QuestSystem:OnMsgQuestActive(ringID)
end

---@param ringID number @任务环ID
---@param oldStatus number @旧任务状态
---@param newStatus number @新任务状态
function QuestComponent:OnMsgQuestInfoUpdateStatus(ringID, oldStatus, newStatus)
    return Game.QuestSystem:OnMsgQuestInfoUpdateStatus(ringID, oldStatus, newStatus)
end

---@param questInfo QuestInfo @任务数据结构
function QuestComponent:OnMsgQuestInfoUpdate(questInfo)
    return Game.QuestSystem:OnMsgQuestInfoUpdate(questInfo)
end

---@param ringID number @任务环ID
---@param targetIndex number @任务目标下标
---@param count number @任务目标完成数量
---@param bFinished boolean @任务目标是否已完成
function QuestComponent:OnMsgQuestTargetChanged(ringID, targetIndex, count, bFinished)
    return Game.QuestSystem:OnMsgQuestTargetChanged(ringID, targetIndex, count, bFinished)
end

---@param ringID number @任务环ID
---@param planeID number @位面ID
function QuestComponent:OnMsgNowRingPlaneChanged(ringID, planeID)
    return Game.QuestSystem:OnMsgNowRingPlaneChanged(ringID, planeID)
end
-----------------NPC对话请求RPC-----------------
---@param questID number @任务ID
---@param npcID number @NPC配置ID
---@param talkID number @闲话ID
function QuestComponent:ReqQuestTalk(questID, npcID, talkID)
    self.remote.ReqQuestTalk(questID, npcID, talkID)
end

---@param result Result @RPC回包结果
function QuestComponent:RetQuestTalk(result)
    return Game.QuestSystem:RetQuestTalk(result)
end
-----------------放弃任务RPC-----------------
---@param ringID number @环ID
function QuestComponent:ReqQuestAbandon(ringID)
    self.remote.ReqQuestAbandon(ringID)
end

---@param result Result @RPC回包结果
function QuestComponent:RetQuestAbandon(result)
    return Game.QuestSystem:RetQuestAbandon(result)
end
-----------------任务道具提交RPC-----------------
function QuestComponent:ReqQuestItemSubmit(questID, itemSubmitID, freeChoice)
    self.remote.ReqQuestItemSubmit(questID, itemSubmitID, freeChoice)
end

---@param result Result @RPC回包结果
---@param submitID number @道具提交表ID
function QuestComponent:RetQuestItemSubmit(result, submitID)
    Game.QuestSystem:RetQuestItemSubmit(result, submitID)
end


function QuestComponent:ReqQuestItemSubmitDirect(questID, targetIndex)
    self.remote.ReqQuestItemSubmitDirect(questID, targetIndex)
end


function QuestComponent:ReqQuestItemSubmitBranch(questID, targetIndex, optionIndex)
    self.remote.ReqQuestItemSubmitBranch(questID, targetIndex, optionIndex)
end


---@param result Result @RPC回包结果
---@param questID number @QuestID
---@param TargetIndex number @TargetIndex
function QuestComponent:RetQuestItemSubmitDirect(result, questID, TargetIndex)
    self:DebugFmt("RetQuestItemSubmitDirect questID=%s, TargetIndex=%s", questID, TargetIndex)
end

---@param result Result @RPC回包结果
---@param questID number @QuestID
---@param targetIndex number @TargetIndex
---@param optionIndex number @OptionIndex
function QuestComponent:RetQuestItemSubmitBranch(result, questID, targetIndex, optionIndex)
    self:DebugFmt("RetQuestItemSubmitBranch questID=%s, TargetIndex=%s, OptionIndex=%s", questID, targetIndex, optionIndex)
end



-- 根据DialogueID删除对话跳转的记录
function QuestComponent:ReqRemoveQuestJumpDataByDialogID(DialogID)
    self.remote.ReqRemoveQuestJumpDataByDialogID(DialogID)
end


-----------------回到位面RPC-----------------
---@param questID number @任务ID
function QuestComponent:ReqQuestOpenPlane(questID)
    self.remote.ReqQuestOpenPlane(questID)
end

---@param result Result @RPC回包结果
---@param questID number @任务ID
function QuestComponent:RetQuestOpenPlane(result, questID)
    return Game.QuestSystem:RetQuestOpenPlane(result, questID)
end
-----------------离开位面RPC-----------------
function QuestComponent:ReqQuestLeavePlane()
    self.remote.ReqQuestLeavePlane()
end

---@param result Result @RPC回包结果
function QuestComponent:RetQuestLeavePlane(result)
    return Game.QuestSystem:RetQuestLeavePlane(result)
end
-----------------手动提交任务RPC-----------------
function QuestComponent:ReqQuestManualSubmit(questID)
    self.remote.ReqQuestLeavePlane(questID)
end

---@param result Result @RPC回包结果
---@param questID number @任务ID
function QuestComponent:RetQuestManualSubmit(result, questID)
    return Game.QuestSystem:RetQuestManualSubmit(result, questID)
end
-----------------手动领取任务RPC-----------------
---@param ringID number @环ID
function QuestComponent:ReqReceiveQuest(ringID, type)
    self.remote.ReqReceiveQuest(ringID, type)
end

---@param result Result @RPC回包结果
---@param questID number @任务ID
function QuestComponent:RetReceiveQuest(result, questID)
    return Game.QuestSystem:RetReceiveQuest(result, questID)
end
-----------------对话领取任务RPC-----------------
---@param ringID number @环ID
---@param npcCfgID number @NPC配置ID
---@param DialogID number @对话ID
function QuestComponent:ReqReceiveNpcTalkQuest(ringID, npcCfgID, DialogID)
    self.remote.ReqReceiveNpcTalkQuest(ringID, npcCfgID, DialogID)
end

---@param result Result @RPC回包结果
---@param ringID number @环ID
function QuestComponent:RetReceiveNpcTalkQuest(result, ringID)
    return Game.QuestSystem:RetReceiveNpcTalkQuest(result, ringID)
end

---@param result Result @RPC回包结果
---@param itemId number @道具ID
function QuestComponent:RetUseReceiveQuestItem(result, itemId)
	Game.QuestSystem:RetUseReceiveQuestItem(result, itemId)
end

-----------------对话提交任务RPC-----------------
---@param ringID number @环ID
---@param npcCfgID number @NPC配置ID
---@param DialogID number @对话ID
function QuestComponent:ReqSubmitNpcTalkQuest(ringID, npcCfgID, DialogID)
    self.remote.ReqSubmitNpcTalkQuest(ringID, npcCfgID, DialogID)
end

---@param result Result @RPC回包结果
---@param ringID number @环ID
function QuestComponent:RetSubmitNpcTalkQuest(result, ringID)
    return Game.QuestSystem:RetSubmitNpcTalkQuest(result, ringID)
end
-----------------失败任务领取RPC-----------------
---@param questID number @任务ID
function QuestComponent:ReqContinueFailedQuest(questID)
    self.remote.ReqContinueFailedQuest(questID)
end
-----------------道具领取任务RPC-----------------
---@param ringID number @环ID
---@param itemID number @道具ID
function QuestComponent:ReqReceiveItemQuest(ringID, itemID)
    self.remote.ReqReceiveItemQuest(ringID, itemID)
end

function QuestComponent:RetReceiveItemQuest(result, ringID)
    return Game.QuestSystem:RetReceiveItemQuest(result, ringID)
end
-----------------任务失败结束手动执行RPC-----------------
---@param questID number @任务ID
function QuestComponent:ReqQuestTimeOut(questID)
    self.remote.ReqQuestTimeOut(questID)
end
-----------------任务使用道具-----------------
function QuestComponent:ReqUseQuestItemByTarget(itemID, questID, targetIndex)
    self.remote.ReqUseQuestItemByTarget(itemID, questID, targetIndex)
end
function QuestComponent:RetUseQuestItemByTarget(result)
    return Game.QuestSystem:RetUseQuestItemByTarget(result)
end

function QuestComponent:ReqUseQuestItemByInstanceListIndex(itemID, questID, targetIndex, listIndex)
    self.remote.ReqUseQuestItemByInstanceListIndex(itemID, questID, targetIndex, listIndex)
end
-----------------【轻量任务相关】-----------------
---@轻量任务状态修改同步
---@param targetGuideID number @轻量任务ID
---@param stepID number @轻量任务步骤ID
---@param count number @步骤完成数量
function QuestComponent:OnMsgTargetGuideChange(targetGuideID, stepID, count)
    Game.TargetGuideSystem:OnMsgTargetGuideChange(targetGuideID, stepID, count)
end
---@轻量任务结束
function QuestComponent:OnMsgTargetGuideFinish()
    Game.TargetGuideSystem:OnMsgTargetGuideFinish()
end
---@轻量任务Position检测
function QuestComponent:ReqTargetGuideNearPosition(targetGuideID, stepID)
    self.remote.ReqTargetGuideNearPosition(targetGuideID, stepID)
end
-----------------------------------------------
---@dialogue播放完成步骤监听回包
---@param dialogueID number @dialogueID
function QuestComponent:ReqFinishTargetGuideDialogue(dialogueID)
    self.remote.ReqFinishTargetGuideDialogue(dialogueID)
end
---@闲话播放完成步骤监听
---@param npcCfgID number @npcCfgID
---@param talkID number @talkID
function QuestComponent:ReqFinishTargetGuideNpcTalk(npcCfgID, talkID)
    self.remote.ReqFinishTargetGuideNpcTalk(npcCfgID, talkID)
end
---@设置追踪任务
function QuestComponent:ReqSetQuestTrace(ringID)
	self.remote.ReqSetQuestTrace(ringID)
end
---@取消追踪
function QuestComponent:ReqRemoveQuestTrace(Type)
	self.remote.ReqRemoveQuestTrace(Type)
end

---请求使用任务道具，第一次使用的时候，不会扣除该道具
function QuestComponent:ReqUseReceiveQuestItem(itemId)
	self.remote.ReqUseReceiveQuestItem(itemId)
end
-----------------【客户端传送门相关】-----------------
---@param questID number @任务ID
---@param targetIndex number @目标ID
function QuestComponent:ReqClientPlanePortalEnterPlane(questID, targetIndex)
    self.remote.ReqClientPlanePortalEnterPlane(questID, targetIndex)
end

function QuestComponent:RetClientPlanePortalEnterPlane(result, questID, targetIndex)
    return Game.QuestSystem:RetClientPlanePortalEnterPlane(result, questID, targetIndex)
end

-----------------【客户端追踪相关】-----------------
---@param TemplateID number @TemplateID
---@param TraceTargetType number @追踪目标类型
---@param NeedSubscribe bool @是否订阅创建事件
function QuestComponent:ReqQuestEntityInfoByTemplateID(TemplateID, TraceTargetType, NeedSubscribe)
    self.remote.ReqQuestEntityInfoByTemplateID(TemplateID, TraceTargetType, NeedSubscribe)
end


---@param TemplateID number @templateID
---@param TraceTargetType number @追踪目标类型
---@param EntityID2PositionDict ENTITY_TRACE_INFO_DICT @EntityID2PositionDict
function QuestComponent:RetQuestEntityInfoByTemplateID(TemplateID, TraceTargetType, EntityID2PositionDict)
    self:DebugFmt("RetQuestEntityInfoByTemplateID TemplateID=%s, TraceTargetType=%s", TemplateID, TraceTargetType)
	Game.QuestSystem:RetQuestEntityInfoByTemplateID(TemplateID, TraceTargetType, EntityID2PositionDict)
end

--- 请求某个InstanceID的单位坐标
---@param InstanceID string @InstanceID
function QuestComponent:ReqQuestEntityPositionByInstanceID(InstanceID)
    self.remote.ReqQuestEntityPositionByInstanceID(InstanceID)
end


---@param IsExist bool @是否存在
---@param InstanceID string @InstanceID
---@param Position FloatList @Position
function QuestComponent:RetQuestEntityPositionByInstanceID(IsExist, InstanceID, Position)
    self:DebugFmt("RetQuestEntityPositionByInstanceID IsExist=%s, InstanceID=%s", IsExist, InstanceID)
	Game.QuestSystem:RetQuestEntityPositionByInstanceID(IsExist, InstanceID, Position)
end



--- 请求某个EntityID的单位坐标
---@param InstanceID number @InstanceID
---@param EntityID string @EntityID
function QuestComponent:ReqQuestEntityPositionByEntityID(EntityID)
    self.remote.ReqQuestEntityPositionByEntityID(EntityID)
end

---@param IsExist bool @是否存在
---@param EntityID string @EntityID
---@param Position FloatList @Position
function QuestComponent:RetQuestEntityPositionByEntityID(IsExist, EntityID, Position)
    self:DebugFmt("RetQuestEntityPositionByEntityID IsExist=%s, EntityID=%s", IsExist, EntityID)
end


---订阅某个TemplateID的单位的创建事件, IsSubscribe为false表示取消订阅
---@param TemplateID int @单位的TemplateID
---@param IsSubscribe bool @是否订阅
function QuestComponent:ReqSubscribeLogicActorCreateByTemplateID(TemplateID, IsSubscribe)
    self.remote.ReqSubscribeLogicActorCreateByTemplateID(TemplateID, IsSubscribe)
end


---监听的某个单位创建通知
---@param IsExist bool @是否存在
---@param EntityID string @EntityID
---@param Position FloatList @Position
function QuestComponent:OnNotifiedLogicActorCreate(TemplateID, InstanceID, EntityID, Position)
    self:DebugFmt("OnNotifiedLogicActorCreate TemplateID=%s, InstanceID=%s, EntityID=%s", TemplateID, InstanceID, EntityID)
end


function QuestComponent:OnMsgRingEndTime(arg0_int)
end



function QuestComponent:OnNotifiedHideQuestDesc(QuestID)
    self:DebugFmt("OnNotifiedHideQuestDesc QuestID=%s", QuestID)
	Game.GlobalEventSystem:Publish(EEventTypesV2.HIDE_QUEST_DESC, QuestID)
end


return QuestComponent