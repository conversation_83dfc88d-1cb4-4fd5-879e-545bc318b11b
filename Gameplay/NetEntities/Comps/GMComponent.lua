local json = require "Framework.Library.json"

local NetworkManager = require "Framework.DoraSDK.NetworkManager"

--- @class GMComponent
GMComponent = DefineComponent("GMComponent")

function GMComponent:OnMsgReload()
    self:Debug("========================================================== Reload Start ============================================================")
    ReloadAllLua()
    self:Debug("============================================================ Reload End ============================================================")
end

--- @param cmdStr string
--- @param args table<string, string>
function GMComponent:ReqGM(cmdStr, args)
	self.remote.ReqGM(cmdStr, args)
end

function GMComponent:ReqExtraGM(cmdStr, args)
    local jsonStr = json.encode(args)
	self.remote.ReqExtraGM(cmdStr, jsonStr)
end

function GMComponent:RetGM(Result)
    if Result.Code == Game.ErrorCodeConst.NO_ERR then
        local SuccMsgData = {}
        table.insert(SuccMsgData, "Server Command Execute Success")
        if not _G.IsStringNullOrEmpty(Result.Trace) then
            table.insert(SuccMsgData, "Trace: " .. tostring(Result.Trace))
        end
        if not _G.IsStringNullOrEmpty(Result.Content) then
            table.insert(SuccMsgData, "Content: " .. tostring(Result.Content))
        end
        local SuccMSg = table.concat(SuccMsgData, "\n")
        Game.GMManager.ExecuteRetCallback({bSucc = true,Content= SuccMSg})
        self:Debug(SuccMSg)
    else
        local ErrCodeName = NetworkManager.GetErrCodeName(Result.Code)
        local ErrCodeDesc = NetworkManager.GetErrCodeDesc(Result.Code)
        local ErroMsgData = {}
        table.insert(
            ErroMsgData,
            "Server Command Execute Err : " .. tostring(ErrCodeName) .. " - " .. tostring(ErrCodeDesc)
        )
        if not _G.IsStringNullOrEmpty(Result.Trace) then
            table.insert(ErroMsgData, "Trace: " .. tostring(Result.Trace))
        end
        if not _G.IsStringNullOrEmpty(Result.Content) then
            table.insert(ErroMsgData, "Content: " .. tostring(Result.Content))
        end
        local ErrMsg = table.concat(ErroMsgData, "\n")
        Game.GMManager.ExecuteRetCallback({bSucc = true,Content= ErrMsg})
        self:Debug(ErrMsg)
    end

    NetworkManager.HandleRequestRet("GM", Result)
end

function GMComponent:OnMsgExtraGM(extraGM)
    Game.GMManager:RegExtraServerGM(extraGM)
end

function GMComponent:RetCreateEditorEntity(errorCode, reason, instanceID, entityIntID)
    if errorCode == Game.ErrorCodeConst.NO_ERR then
		if Game.SceneRuntimeEditorManager then
			local jsonTable = {
				["instanceID"] = instanceID,
				["entityID"] = entityIntID,
			}
			Game.SceneRuntimeEditorManager:OnServerRet("OnCreateEntity", jsonTable)
		end
    end
end

-- opType在Shared.Const.SCENE_EDITOR_OP_TYPE
function GMComponent:RetEditorModifyEntityProp(errorCode, opType, reason, instanceID, entityIntID)
    if errorCode == Game.ErrorCodeConst.NO_ERR then
		Game.SceneRuntimeEditorManager:OnEntityModify(opType, instanceID, entityIntID)
    end
end

function GMComponent:RetEditorGetInstanceIDToEntityID(errorCode, instanceIDToEntityIntID)
    if errorCode == Game.ErrorCodeConst.NO_ERR then

    end
end

---hotfix example
GMComponent._hotfixTestValue = "Befor Hotfix test."
function GMComponent:ClientHotfixTest()
    self:ErrorFmt("[ClientHotfixTest]Befor Test.hotfixTestValue:%s", GMComponent._hotfixTestValue)
end

return GMComponent
