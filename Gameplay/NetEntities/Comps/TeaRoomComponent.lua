TeaRoomComponent = DefineComponent("TeaRoomComponent")

--region s2c
------------------------------------------------- Server To Client Start ---------------------------------------------
function TeaRoomComponent:UpdateTeaRoomRoomList(roomListData)
    -- 更新茶壶系统入口界面的房间列表
    Game.TeaRoomSystem:UpdateTeaRoomRoomList(roomListData, Enum.EChatRoomType.Normal)
end

function TeaRoomComponent:OnMsgEnterRoomToLeaveRoom(nLastRoomID, nTargetRoomID, isParty)
    -- 进入房间的返回询问离开房间
    Game.TeaRoomSystem:OnMsgEnterRoomToLeaveRoom(nLastRoomID, nTargetRoomID, isParty)
end

function TeaRoomComponent:OnMsgEnterRoom(roomInfoData, isFavoriteRoom)
    -- 成功进入房间
    Game.TeaRoomSystem:OnMsgEnterRoom(roomInfoData, isFavoriteRoom, Enum.EChatRoomType.Normal)
end

function TeaRoomComponent:OnMsgTeaRoomReconnect(roomInfoData, isEmpty, isFavoriteRoom)
    -- 断线重连房间
    self:DebugFmt("TeaRoomComponent:OnMsgTeaRoomReconnect Room: %s", roomInfoData.RoomID)
    Game.TeaRoomSystem:OnMsgTeaRoomReconnect(roomInfoData, isEmpty, isFavoriteRoom, Enum.EChatRoomType.Normal)
end

function TeaRoomComponent:OnMsgLeaveRoom(nRoomID)
    -- 成功退出房间
    Game.TeaRoomSystem:OnMsgLeaveRoom(nRoomID)
end

function TeaRoomComponent:OnMsgCreateRoom(strRoomName, nRoomID, bShareToChannel, bShareToFriend)
    Game.TeaRoomSystem:OnMsgCreateRoom(strRoomName, nRoomID, bShareToChannel, bShareToFriend, Enum.EChatRoomType.Normal)
end

function TeaRoomComponent:OnMsgNotifyMemberEnterRoom(nRoomID, memberInfo, hotValue, memberCount)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMemberEnterRoom Room: %s, avatar: %s, hotValue: %s, memberCount: %s, roomTag: %s", nRoomID, memberInfo.Nickname, hotValue, memberCount)
    Game.TeaRoomSystem:OnMsgNotifyMemberEnterRoom(nRoomID, memberInfo, hotValue, memberCount)
end

function TeaRoomComponent:OnMsgNotifyMemberLeaveRoom(nRoomID, entityID, shortUid, hotValue, memberCount)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMemberLeaveRoom Room: %s, entityID: %s, hotValue: %s, memberCount: %s", nRoomID, entityID, hotValue, memberCount)
    Game.TeaRoomSystem:OnMsgNotifyMemberLeaveRoom(nRoomID, entityID, hotValue, memberCount)
end

function TeaRoomComponent:OnMsgReqTeaRoomMessageTop(nRoomID, messageText, messageTextAuthor, strTopMessageID)
    self:DebugFmt("TeaRoomComponent:OnMsgReqTeaRoomMessageTop Room: %s, messageText: %s messageTextAuthor: %s strMessageID: %s", nRoomID, messageText, messageTextAuthor, strTopMessageID)
    Game.TeaRoomSystem:OnMsgReqTeaRoomMessageTop(nRoomID, messageText, messageTextAuthor, strTopMessageID)
end

function TeaRoomComponent:OnMsgReqTeaRoomCancelMessageTop(nRoomID)
    self:Debug("TeaRoomComponent:OnMsgReqTeaRoomCancelMessageTop")
    self:DebugFmt("TeaRoomComponent:OnMsgReqTeaRoomCancelMessageTop Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgReqTeaRoomCancelMessageTop(nRoomID)
end

function TeaRoomComponent:OnMsgNotifyMemberGiveGift(giveGiftInfo)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMemberGiveGift Room")
    Game.TeaRoomSystem:OnMsgNotifyMemberGiveGift(giveGiftInfo)
end

function TeaRoomComponent:OnMsgNotifySpeechFree(nRoomID)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifySpeechFree Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgNotifySpeechFree(nRoomID)
end

function TeaRoomComponent:OnMsgNotifyMemberSpeechState(nRoomID, memberInfo)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifySpeechFree Room: %s, avatarID: %s", nRoomID, memberInfo.AvatarID)
    Game.TeaRoomSystem:OnMsgNotifyMemberSpeechState(nRoomID, memberInfo)
end

function TeaRoomComponent:OnMsgSetTeaRoomMemberPrivilegeCallback(nRoomID, addAvatarIDList, delAvatarIDList)
    self:DebugFmt("TeaRoomComponent:OnMsgSetTeaRoomMemberPrivilegeCallback Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgSetTeaRoomMemberPrivilegeCallback(nRoomID, addAvatarIDList, delAvatarIDList)
end

function TeaRoomComponent:OnMsgNotifyMemberInfoList(nRoomID, memberInfoList)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMemberInfoList Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgNotifyMemberInfoList(nRoomID, memberInfoList)
end

function TeaRoomComponent:OnMsgNotifyAllMemberOrderList(nRoomID, orderList)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyAllMemberOrderList Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgNotifyAllMemberOrderList(nRoomID, orderList)
end

function TeaRoomComponent:OnMsgNotifyModifyMaster(nRoomID, memberInfo, isEmpty)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyModifyMaster Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgNotifyModifyMaster(nRoomID, memberInfo, isEmpty)
end

function TeaRoomComponent:OnMsgApplicationInfo2Master(nRoomID, applicationInfo)
    self:DebugFmt("TeaRoomComponent:OnMsgApplicationInfo2Master Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgApplicationInfo2Master(nRoomID, applicationInfo)
end

function TeaRoomComponent:OnMsgApplicationList2Master(nRoomID, applicationList)
    self:DebugFmt("TeaRoomComponent:OnMsgApplicationList2Master Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgApplicationList2Master(nRoomID, applicationList)
end

function TeaRoomComponent:OnMsgBlackList2Master(nRoomID, blackList)
    self:DebugFmt("TeaRoomComponent:OnMsgBlackList2Master Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgBlackList2Master(nRoomID, blackList)
end

function TeaRoomComponent:OnMsgNotifyMasterDeleteBlackList(nRoomID, strAvatarID)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMasterDeleteBlackList Room: %s, entityID: %s", nRoomID, strAvatarID)
    Game.TeaRoomSystem:OnMsgNotifyMasterDeleteBlackList(nRoomID, strAvatarID)
end

function TeaRoomComponent:OnMsgNotifyMasterAddBlackList(nRoomID, memberInfo)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMasterAddBlackList Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgNotifyMasterAddBlackList(nRoomID, memberInfo)
end

function TeaRoomComponent:OnMsgNotifyModifyRoomName(nRoomID, strRoomName)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyModifyRoomName Room: %s, roomName: %s", nRoomID, strRoomName)
    Game.TeaRoomSystem:OnMsgNotifyModifyRoomName(nRoomID, strRoomName)
end

function TeaRoomComponent:OnMsgNotifyModifyRoomType(nRoomID, nRoomType)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyModifyRoomType Room: %s, roomType: %s", nRoomID, nRoomType)
    Game.TeaRoomSystem:OnMsgNotifyModifyRoomType(nRoomID, nRoomType)
end

function TeaRoomComponent:OnMsgNotifyModifyRoomHotValue(nRoomID, nHotValue, nRoomTag)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyModifyRoomHotValue Room: %s, hotValue: %S", nRoomID, nHotValue)
    Game.TeaRoomSystem:OnMsgNotifyModifyRoomHotValue(nRoomID, nHotValue, nRoomTag)
end

function TeaRoomComponent:OnMsgNotifyGagMessage(nRoomID, strAvatarID, bIsForbidSpeak)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyGagMessage Room: %s, entityID: %s, isForbidSpeak: %s", nRoomID, strAvatarID, bIsForbidSpeak)
    Game.TeaRoomSystem:OnMsgNotifyGagMessage(nRoomID, strAvatarID, bIsForbidSpeak)
end

function TeaRoomComponent:OnMsgDeleteApplicationInfos(nRoomID, tblApplyList)
    self:DebugFmt("TeaRoomComponent:OnMsgDeleteApplicationInfos Room: %s", nRoomID)
    Game.TeaRoomSystem:OnMsgDeleteApplicationInfos(nRoomID, tblApplyList)
end

function TeaRoomComponent:OnMsgNotifyMemberLevelup(nRoomID, strAvatarID, nGrade)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMemberLevelup Room: %s AvatarID: %s nGrade: %s", nRoomID, strAvatarID, nGrade)
    Game.TeaRoomSystem:OnMsgNotifyMemberLevelup(nRoomID, strAvatarID, nGrade)
end

function TeaRoomComponent:OnMsgNotifyMemberNewName(nRoomID, strAvatarID, strPlayerName)
    self:DebugFmt("TeaRoomComponent:OnMsgNotifyMemberNewName Room: %s AvatarID: %s PlayerName: %s", nRoomID, strAvatarID, strPlayerName)
    Game.TeaRoomSystem:OnMsgNotifyMemberNewName(nRoomID, strAvatarID, strPlayerName)
end

function TeaRoomComponent:OnMsgIsTeaRoomMaster(nRoomID, isMaster)
    self:DebugFmt("TeaRoomComponent:OnMsgIsTeaRoomMaster %s", isMaster)
end

function TeaRoomComponent:OnMsgGetAllTeaRoomInfo(roomInfo)
    self:DebugFmt("TeaRoomComponent:OnMsgGetAllTeaRoomInfo %s", roomInfo)
end

function TeaRoomComponent:OnMsgFavoriteTeaRoomCallBack(roomID)
    self:DebugFmt("TeaRoomComponent:OnMsgFavoriteTeaRoomCallBack %s", roomID)
    Game.TeaRoomSystem:OnMsgFavoriteTeaRoomCallBack(roomID)
end

function TeaRoomComponent:OnMsgGetChatRoomFavoritesCallBack(teaRoomFavorites, partyFavorites, isDelete)
    self:DebugFmt("TeaRoomComponent:OnMsgGetChatRoomFavoritesCallBack")
    Game.TeaRoomSystem:OnMsgGetChatRoomFavoritesCallBack(teaRoomFavorites, partyFavorites, isDelete)
end

function TeaRoomComponent:OnMsgCancelTeaRoomFavoriteCallBack(roomID)
    self:DebugFmt("TeaRoomComponent:OnMsgCancelTeaRoomFavoriteCallBack %s", roomID)
    Game.TeaRoomSystem:OnMsgCancelTeaRoomFavoriteCallBack(roomID)
end

function TeaRoomComponent:OnMsgTeaRoomFindMemberName(nRoomID, shortUidList)
    self:DebugFmt("TeaRoomComponent:OnMsgTeaRoomFindMemberName Room: %s", nRoomID)
end

--endregion
------------------------------------------------- Server To Client End -----------------------------------------------

--region c2s
------------------------------------------------- Client To Server Start ---------------------------------------------
-- 创建房间
function TeaRoomComponent:ReqCreateTeaRoom(roomName, roomType, roomTag, isShareToWorldChannel, isShareToMements)
    self.remote.ReqCreateTeaRoom(roomName, roomType, roomTag, isShareToWorldChannel, isShareToMements)
end

-- 加入房间
function TeaRoomComponent:ReqEnterTeaRoom(roomID)
    self.remote.ReqEnterTeaRoom(roomID)
end

-- 快速加入房间
function TeaRoomComponent:ReqEnterQuickly()
    self.remote.ReqEnterQuickly()
end

-- 退出房间
function TeaRoomComponent:ReqLeaveTeaRoom(roomID)
    self.remote.ReqLeaveTeaRoom(roomID)
end

-- 离开当前房间并加入另外的房间
function TeaRoomComponent:ReqLeaveAndEnterRoom(leaveRoomID, enterRoomID, isParty)
    self:DebugFmt("TeaRoomComponent:ReqLeaveAndEnterRoom")
    self.remote.ReqLeaveAndEnterRoom(leaveRoomID, enterRoomID, isParty)
end

-- 搜索房间
function TeaRoomComponent:ReqSearchTeaRoom(pattern, onlyOneServer, filterList)
    self.remote.ReqSearchTeaRoom(pattern, onlyOneServer, filterList)
end

-- 修改房间的权限
function TeaRoomComponent:ReqModifyTeaRoomPrivilege(pattern, privilege)
    self.remote.ReqModifyTeaRoomPrivilege(pattern, privilege)
end

-- 修改房间的名字
function TeaRoomComponent:ReqModifyTeaRoomName(roomID, roomName)
    self.remote.ReqModifyTeaRoomName(roomID, roomName)
end

-- 修改房间的类型
function TeaRoomComponent:ReqModifyTeaRoomType(roomID, roomType)
    self.remote.ReqModifyTeaRoomType(roomID, roomType)
end

function TeaRoomComponent:ReqSetTeaRoomSpeechFree(nRoomID)
    self.remote:ReqSetTeaRoomSpeechFree(nRoomID)
end

-- 房主设置玩家的发言权限
function TeaRoomComponent:ReqSetTeaRoomMemberPrivilege(roomID, privilegeList)
    self.remote.ReqSetTeaRoomMemberPrivilege(roomID, privilegeList)
end

-- 房主设置玩家的发言顺序
function TeaRoomComponent:ReqSetTeaRoomMemberOrder(roomID, orderList)
    self.remote.ReqSetTeaRoomMemberOrder(roomID, orderList)
end

-- 成员申请上麦
function TeaRoomComponent:ReqTeaRoomApplyOpenMic(roomID, text)
    self.remote.ReqTeaRoomApplyOpenMic(roomID, text)
end

-- 成员修改发言状态 (拥有发言权限的前提下)
function TeaRoomComponent:ReqTeaRoomMemberModifySpeechState(roomID, state)
    self.remote.ReqTeaRoomMemberModifySpeechState(roomID, state)
end

-- 转让房主
function TeaRoomComponent:ReqTransferTeaRoomMaster(roomID, entityID)
    self.remote.ReqTransferTeaRoomMaster(roomID, entityID)
end

-- 拉黑名单
function TeaRoomComponent:ReqTeaRoomPullBlacklist(roomID, entityID)
    self.remote.ReqTeaRoomPullBlacklist(roomID, entityID)
end

-- 解除拉黑
function TeaRoomComponent:ReqTeaRoomCancelBlacklist(roomID, entityID)
    self.remote.ReqTeaRoomCancelBlacklist(roomID, entityID)
end

-- 禁言
function TeaRoomComponent:ReqTeaRoomPullGaglist(roomID, entityID)
    self.remote.ReqTeaRoomPullGaglist(roomID, entityID)
end

-- 解除禁言
function TeaRoomComponent:ReqTeaRoomCancelGaglist(roomID, entityID)
    self.remote.ReqTeaRoomCancelGaglist(roomID, entityID)
end

-- 消息置顶
function TeaRoomComponent:ReqTeaRoomMessageTop(roomID, messageText, messageTextAuthor, strTopMessageID)
    self.remote.ReqTeaRoomMessageTop(roomID, messageText, messageTextAuthor, strTopMessageID)
end

-- 取消消息置顶
function TeaRoomComponent:ReqTeaRoomCancelMessageTop(roomID)
    self.remote.ReqTeaRoomCancelMessageTop(roomID)
end

-- 茶壶房间赠礼功能
function TeaRoomComponent:ReqTeaRoomGiveGift(roomID, recvRoleName, giftCnt, giftID)
    self.remote.ReqTeaRoomGiveGift(roomID, recvRoleName, giftCnt, giftID)
end

-- 房主获取申请名单列表
function TeaRoomComponent:ReqTeaRoomGetApplicationList(nRoomID)
    self.remote:ReqTeaRoomGetApplicationList(nRoomID)
end

-- 房主获取黑名单列表
function TeaRoomComponent:ReqTeaRoomGetBlackList(nRoomID)
    self.remote:ReqTeaRoomGetBlackList(nRoomID)
end

-- 获取指定id列表的玩家信息
function TeaRoomComponent:ReqTeaRoomGetMemberInfo(nRoomID, tblAvatarIDList)
    self.remote:ReqTeaRoomGetMemberInfo(nRoomID, tblAvatarIDList)
end

-- 获取指定id列表的玩家信息
function TeaRoomComponent:ReqTeaRoomGetMemberInfoByShortUid(nRoomID, tblShortUidList)
    self.remote:ReqTeaRoomGetMemberInfoByShortUid(nRoomID, tblShortUidList)
end

-- 房主清理申请列表的内容
function TeaRoomComponent:ReqTeaRoomCleanApplicationList(nRoomID, acceptTable, rejectTable)
    self.remote:ReqTeaRoomCleanApplicationList(nRoomID, acceptTable, rejectTable)
end

-- 麦序模式下  玩家下麦操作
function TeaRoomComponent:ReqTeaRoomMemberCloseMic(nRoomID)
    self.remote:ReqTeaRoomMemberCloseMic(nRoomID)
end

-- 收藏房间
function TeaRoomComponent:ReqFavoriteTeaRoom()
    self.remote:ReqFavoriteTeaRoom()
end

-- 删除收藏房间
function TeaRoomComponent:ReqDeleteFavorites(cancelTeaRoomFavorites, cancelPartyFavorites)
    self.remote:ReqDeleteFavorites(cancelTeaRoomFavorites, cancelPartyFavorites)
end

-- 获取最新收藏夹内容
function TeaRoomComponent:ReqGetChatRoomFavorites()
    self.remote:ReqGetChatRoomFavorites()
end

-- 收藏房间
function TeaRoomComponent:ReqFavoriteTeaRoom()
    self.remote:ReqFavoriteTeaRoom()
end

-- 取消收藏房间
function TeaRoomComponent:ReqCancelTeaRoomFavorite()
    self.remote:ReqCancelTeaRoomFavorite()
end

-- 获取收藏房间列表
function TeaRoomComponent:ReqGetChatRoomFavorites()
    self.remote:ReqGetChatRoomFavorites()
end

-- 获取收藏房间列表
function TeaRoomComponent:ReqTeaRoomFindMemberName(nRoomID, patternStr)
    self.remote:ReqTeaRoomFindMemberName(nRoomID, patternStr)
end

--endregion
------------------------------------------------- Client To Server End ---------------------------------------------

return TeaRoomComponent