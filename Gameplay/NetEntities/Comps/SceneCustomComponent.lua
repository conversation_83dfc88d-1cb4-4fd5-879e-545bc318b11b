SceneCustomComponent = DefineComponent("SceneCustomComponent")

function SceneCustomComponent:ctor()
end

function SceneCustomComponent:dtor()
end

function SceneCustomComponent:RetGetAllSceneCustomStrategyBriefInfo(dataList)
	Game.SceneCustomSystem:RetGetAllSceneCustomStrategyBriefInfo(dataList)
end

function SceneCustomComponent:RetGetOwnedSceneItem(dataAll)
	Game.SceneCustomSystem:RetGetOwnedSceneItem(dataAll)
end

function SceneCustomComponent:RetChangePlayerSceneCustomStrategyName(name)
	Game.SceneCustomSystem:RetChangePlayerSceneCustomStrategyName(name)
end

function SceneCustomComponent:RetChangePlayerSceneCustomStrategyPicture(icon)
	Game.SceneCustomSystem:RetChangePlayerSceneCustomStrategyPicture(icon)
end

function SceneCustomComponent:set_playerSceneCustomSlot(entity, new, old)
	Game.SceneCustomSystem:RefreshPlayerSceneCustom()
end

function SceneCustomComponent:set_playerSceneCustom(entity, new, old)
	Game.SceneCustomSystem:RefreshPlayerSceneCustomData()
end