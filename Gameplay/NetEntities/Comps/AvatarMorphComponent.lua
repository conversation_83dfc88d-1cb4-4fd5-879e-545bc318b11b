local MorphComponent = kg_require("Gameplay.NetEntities.Comps.MorphComponent")

AvatarMorphComponent = DefineComponent("AvatarMorphComponent", MorphComponent)


------------------------------------------------- Server To Client Start -----------------------------------------------
-- 解除禁言
function AvatarMorphComponent:ReqStartPossess(entityID)
    self.remote.ReqStartPossess(entityID)
end

-- 消息置顶
function AvatarMorphComponent:ReqUnpossessed()
    self.remote.ReqUnpossessed()
end

------------------------------------------------- Server To Client End -----------------------------------------------


------------------------------------------------- Client To Server Start ---------------------------------------------
function AvatarMorphComponent:OnMsgStartPossessCallback(morphID, morphFastFacadeControlID, targetID)
    self:DebugFmt("AvatarMorphComponent:OnMsgStartPossessCallback")
end

function AvatarMorphComponent:OnMsgEndPossessCallback(morphID, morphFastFacadeControlID, targetID)
    self:DebugFmt("AvatarMorphComponent:OnMsgEndPossessCallback")
end

------------------------------------------------- Client To Server End ---------------------------------------------

return AvatarMorphComponent