kg_require("Gameplay.NetEntities.Comps.Element.BaseElementComponent")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local GameConst = kg_require("Gameplay.CommonDefines.C7_Game_Const")

local COLLISION_PRESET_NAMES = CollisionConst.COLLISION_PRESET_NAMES

---@class SceneActorElementComponent : BaseElementComponent

---@type SceneActorElementComponent
SceneActorElementComponent = DefineComponent("SceneActorElementComponent", BaseElementComponent)

function SceneActorElementComponent:ctor()
    self._LocalControlElementReactCallBack = nil
end

function SceneActorElementComponent:dtor()

end

function SceneActorElementComponent:__component_EnterWorld__()
    BaseElementComponent.__component_EnterWorld__(self)

    -- 如果配置了额外半径,需要处理
    local exploreElementCommonConf = self.SceneConf.SceneActorCommon and self.SceneConf.SceneActorCommon.ExploreElement
    if (exploreElementCommonConf ~= nil) and (exploreElementCommonConf.bEnableExtraRadius == true) then
        self:setExtraElementTrigger()
    end
end

function SceneActorElementComponent:__component_ExitWorld__()
    BaseElementComponent.__component_ExitWorld__(self)
end


function SceneActorElementComponent:__component_AfterEnterWorld__()

end

function SceneActorElementComponent:EnableLocalElementControl(ReactCB)
    if self[ReactCB] then
        self._LocalControlElementReactCallBack = ReactCB
    end
end

function SceneActorElementComponent:DisableLocalElementControl()
    self._LocalControlElementReactCallBack = nil
end

---@public
---@param elementID number
---@param elementDuration number
function SceneActorElementComponent:DoElementReact(elementID, elementDuration)
    BaseElementComponent.DoElementReact(self, elementID, elementDuration)
    if not self.bCanClientElementReact then
        return
    end
    self:DebugFmt("[DoElementReact] %s elementID=%s, elementDuration=%s", self.InsID, elementID, elementDuration)
    local ElementAttach = self:GetTriggerConfValue("ElementAttach")
    if ElementAttach then
		for _, ElementTriggerID in ksbcipairs(ElementAttach) do
			local ElemTriggerData = Game.TableData.GetSceneActorElementAttachTriggerDataRow(ElementTriggerID)
			if ElemTriggerData then
				if ElemTriggerData.ElementID == elementID then
					if self._LocalControlElementReactCallBack then
						if self[self._LocalControlElementReactCallBack](self, ElementTriggerID) then
							break
						end
					else
						Game.me:ReqSceneActorAttachElem(self.InsID, ElementTriggerID)
					end
				end
			end
		end
	end
end

function SceneActorElementComponent:__component_DoBehaviorActionOnStateChange__(bCreate)
    if self:CanSpreadElement() then
		local ElementSpread = self:GetActionConfValue("ElementSpread")
        if ElementSpread ~= nil then
            for _, ActionID in ksbcipairs(ElementSpread) do
                self:InnerStartOneElemSpread(bCreate, ActionID)
            end
        end
    end
end

function SceneActorElementComponent:CheckCanSpreadElem(Entity, ActionID)
    if not Entity.bCanClientElementReact or not Entity.GetTriggerConfValue then
        return
    end
    local SpreadData = Game.TableData.GetSceneActorElementSpreadActionDataRow(ActionID)
	local ElementAttach = self:GetTriggerConfValue("ElementAttach")
	if ElementAttach then
		for _, ElementTriggerID in ksbcipairs(ElementAttach) do
			local ElemTriggerData = Game.TableData.GetSceneActorElementAttachTriggerDataRow(ElementTriggerID)
			if ElemTriggerData then
				if ElemTriggerData.ElementID == SpreadData.ElementID then
					return true
				end
			end
		end
	end
    return false
end

function SceneActorElementComponent:InnerStartOneElemSpread(bCreate, ActionID)
    local SpreadData = Game.TableData.GetSceneActorElementSpreadActionDataRow(ActionID)
	if not SpreadData then
		return
	end

	local OverlapEntityIDArray = self.CppEntity:KAPI_Collision_SphereCheck(
		SpreadData.SpreadRadius, self:GetPosition(),  self:GetRotation(), CollisionConst.QUERY_BY_OBJECTTYPES.ABILITY_WITH_SCENE_ACTOR, true)

	if OverlapEntityIDArray:Num() == 0 then
		return
	end

	for _, uid in OverlapEntityIDArray:ToTable() do
		local Entity = Game.EntityManager:getEntity(uid)
		if Entity and self:CheckCanSpreadElem(Entity, ActionID) then
			Game.me:ReqSceneActorSpreadElem(Entity.InsID, ActionID)
		end
	end
end

SceneActorElementComponent.SphereCompClassID = 0

---@private
function SceneActorElementComponent:setExtraElementTrigger()
	if not self.bInWorld then
		return v
	end

    local radius = self.SceneConf.SceneActorCommon.ExploreElement.ExtraRadius
    if radius == 0 then
        return
    end

    if SceneActorElementComponent.SphereCompClassID == 0 then
        SceneActorElementComponent.SphereCompClassID = Game.ObjectActorManager:GetIDByClass(import("SphereComponent"))
    end

    -- 其他组件添加tag
    self.CppEntity:KAPI_Actor_SetTagForRootAndMeshComps(GameConst.EXPLORE_ELEMENT_IGNORE_COMPONENT_TAG)

    -- 加新的并设置碰撞预设
    local sphereCompID = self.CppEntity:KAPI_Actor_AddComponentByClassID(SceneActorElementComponent.SphereCompClassID)
    self.CppEntity:KAPI_PrimitiveID_SetCollisionProfileName(sphereCompID, COLLISION_PRESET_NAMES.SCENE_ACTOR_PRESET, true)
    self.CppEntity:KAPI_SphereID_SetSphereRadius(sphereCompID, radius, true)
end

return SceneActorElementComponent
