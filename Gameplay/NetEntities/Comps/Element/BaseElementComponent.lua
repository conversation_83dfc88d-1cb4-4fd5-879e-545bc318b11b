---@class BaseElementComponent
BaseElementComponent = DefineComponent("BaseElementComponent")

function BaseElementComponent:ctor()
    self.bCanClientElementReact = false
    self.bCanSpreadElement = false
end

function BaseElementComponent:dtor()

end

function BaseElementComponent:__component_EnterWorld__()
    
end

function BaseElementComponent:__component_ExitWorld__()
    -- 如有扩散逻辑则停止
end


--region BaseProp

---@protected
---@param bCan boolean
function BaseElementComponent:SetCanClientElementReact(bCan)
    self.bCanClientElementReact = bCan
end

---@protected
---@param
function BaseElementComponent:SetCanSpreadElement(bCan)
    self.bCanSpreadElement = bCan
end

function BaseElementComponent:CanSpreadElement()
    return self.bCanSpreadElement
end

--endregion BaseProp


-- 前端主动调用
---@public
---@param elementID number
---@param elementDuration number
function BaseElementComponent:DoElementReact(elementID, elementDuration)

end

-- 服务器下发接口
---@public
---@param newElementID number
---@param oldElementID number
function BaseElementComponent:OnElementReact(newElementID, oldElementID)

end

return BaseElementComponent
