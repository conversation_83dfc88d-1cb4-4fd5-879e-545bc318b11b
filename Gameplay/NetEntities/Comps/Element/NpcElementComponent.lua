kg_require("Gameplay.NetEntities.Comps.Element.BaseElementComponent")

---@class NpcElementComponent : BaseElementComponent

---@type  NpcElementComponent|NpcActor
NpcElementComponent = DefineComponent("NpcElementComponent", BaseElementComponent)

function NpcElementComponent:ctor()
    self.inPlayAnimInfo = nil
end

function NpcElementComponent:dtor()
    self.inPlayAnimInfo = nil
end

function NpcElementComponent:__component_EnterWorld__()
    BaseElementComponent.__component_EnterWorld__(self)
    self:SetCanClientElementReact(false)
    self:SetCanSpreadElement(false)
end

function NpcElementComponent:__component_ExitWorld__()
    BaseElementComponent.__component_ExitWorld__(self)
end

-- 服务器下发接口
---@public
---@param newElementID number
---@param oldElementID number
function NpcElementComponent:OnElementReact(newElementID, oldElementID)
    BaseElementComponent.OnElementReact(self, newElementID, oldElementID)

    self:DebugFmt("[OnElementReact] %s newElementID=%s, oldElementID=%s", self:uid(), newElementID, oldElementID)
    local npcData = self:GetEntityConfigData()
    if not npcData then
        return
    end

    local elemEventID = npcData.ElementEffect[newElementID]
    if not elemEventID then
        return
    end

    local elemEventData = Game.TableData.GetExploreElementEventDataRow(elemEventID)
    if not elemEventData then
        return
    end

    -- 播动画
    local animLibID, bLoop = ksbcnext(elemEventData.AttachAnim)
    if (animLibID ~= nil) and (bLoop ~= nil) then
        -- todo@shijingzhe: 后续应该由服务端控制
        self:LeaveAnimQueue()

        local AnimFeatureID, playTime = self:PlayAnimLibMontage(animLibID, nil, bLoop)
        if AnimFeatureID == nil then
            self:WarningFmt("[OnElementReact] %s play %s might need check", self:uid(), animLibID)
        else
            self.inPlayAnimInfo = { [animLibID] = bLoop }
            if not bLoop then
                local eid = self.eid
                Game.TimerManager:CreateTimerAndStart(function()
                    local ent = Game.EntityManager:getEntity(eid)
                    if ent then
                        ent.inPlayAnimInfo = nil
                    end
                end, playTime * 1000, 1)
            end
        end
    end

    -- 弹气泡
    if elemEventData.AttachBubbleID ~= 0 then
        if not Game.TableData.GetBubbleDataRow(elemEventData.AttachBubbleID) then
            self:WarningFmt("[OnElementReact] no data for headBubbleID %s", elemEventData.AttachBubbleID)
        else
            Game.HeadInfoManager:ShowBubbleByBubbleID(self.eid, elemEventData.AttachBubbleID, nil, 0)
        end
    end
end

function NpcElementComponent:ElementOnMeshChanged()
    if self.inPlayAnimInfo then
        local animLibID, bLoop = next(self.inPlayAnimInfo)
        local AnimFeatureID = self:PlayAnimLibMontage(animLibID, nil, bLoop)

        if AnimFeatureID == nil then
            self:WarningFmt("[OnElementReact] %s play %s might need check", self:uid(), animLibID)
        end
    end
end

return NpcElementComponent
