--- @class DungeonAuctionComponent
DungeonAuctionComponent = DefineComponent("DungeonAuctionComponent")

function DungeonAuctionComponent:ctor()
    -- self.CurAuctionInfos = {}
    self:Debug("DungeonAuctionComponent:ctor", self, self.eid)
end

function DungeonAuctionComponent:ReqDungeonAuctionBid(AuctionID, GoodsID, MoneyType, MoneyCount)
    self:Debug("DungeonAuctionComponent:ReqDungeonAuctionBid ", AuctionID, GoodsID, MoneyType, MoneyCount)
	self.remote.ReqDungeonAuctionBid(AuctionID, GoodsID, MoneyType, MoneyCount)
end

function DungeonAuctionComponent:RetDungeonAuctionBid(err, AuctionID, GoodsID)
    --self:Debug("DungeonAuctionComponent:RetDungeonAuctionBid ", CommonUtils.tprint(Result), AuctionID, GoodsID) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    self:OnMsgGenReminder(err)
end

function DungeonAuctionComponent:OnMsgDungeonAuctionRefreshState(DungeonAuctionSyncStateInfos)
    --self:Debug("DungeonAuctionComponent:OnMsgDungeonAuctionRefreshState", CommonUtils.tprint(DungeonAuctionSyncStateInfos)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.DungeonAwardSystem:OnMsgDungeonAuctionRefreshState(DungeonAuctionSyncStateInfos)
end

function DungeonAuctionComponent:OnMsgDungeonAuctionRefresh(DungeonAuctionSyncClientInfos)
    --self:Debug("DungeonAuctionComponent:OnMsgDungeonAuctionRefresh", CommonUtils.tprint(DungeonAuctionSyncClientInfos)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.DungeonAwardSystem:OnMsgDungeonAuctionRefresh(DungeonAuctionSyncClientInfos)
end

function DungeonAuctionComponent:ReqDungeonAuctionGiveUpBid(AuctionID, GoodsID, bGiveUp)
    self:Debug("DungeonAuctionComponent:ReqDungeonAuctionGiveUpBid ", AuctionID, GoodsID)
	self.remote.ReqDungeonAuctionGiveUpBid(AuctionID, GoodsID, bGiveUp)
end

function DungeonAuctionComponent:RetDungeonAuctionGiveUpBid(err, AuctionID, GoodsID, bGiveUp)
    --self:Debug("DungeonAuctionComponent:RetDungeonAuctionGiveUpBid", CommonUtils.tprint(Result), AuctionID, GoodsID, bGiveUp) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    self:OnMsgGenReminder(err)
    Game.DungeonAwardSystem:RetDungeonAuctionGiveUpBid(err, GoodsID, bGiveUp)
end

function DungeonAuctionComponent:OnMsgNotifyGiveUpBid(GoodsID, AvatarName)
    self:Debug("DungeonAuctionComponent:OnMsgNotifyGiveUpBid", GoodsID, AvatarName)
    Game.DungeonAwardSystem:OnMsgNotifyGiveUpBid(GoodsID,AvatarName)
end

function DungeonAuctionComponent:OnMsgSyncDungeonAuctionRemove(auctionIDList)
    --self:Debug("DungeonAuctionComponent:OnMsgSyncDungeonAuctionRemove", CommonUtils.tprint(auctionIDList)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.DungeonAwardSystem:OnMsgSyncDungeonAuctionRemove(auctionIDList)
end

function DungeonAuctionComponent:set_DungeonAuctionEstimations(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_ESTIMATION)
end

DungeonAuctionEstimation = DefineClass("DungeonAuctionEstimation")
function DungeonAuctionEstimation:set_attr(entity, key, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_ESTIMATION)
end

DungeonAuctionEstimationInfo = DefineClass("DungeonAuctionEstimationInfo")
function DungeonAuctionEstimationInfo:set_attr(entity, key, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_ESTIMATION)
end

DungeonAuctionEstimationInfos = DefineClass("DungeonAuctionEstimationInfos")
function DungeonAuctionEstimationInfos:set_attr(entity, key, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_AUCTION_ESTIMATION)
end

return DungeonAuctionComponent
