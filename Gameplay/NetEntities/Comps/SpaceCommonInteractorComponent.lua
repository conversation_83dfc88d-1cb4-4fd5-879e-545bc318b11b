local commonInteractorBaseComponent = kg_require("Gameplay.NetEntities.Comps.CommonInteractorBaseComponent")

---@class SpaceCommonInteractorComponent
SpaceCommonInteractorComponent = DefineComponent("SpaceCommonInteractorComponent", commonInteractorBaseComponent)

function SpaceCommonInteractorComponent:ctor()
end

function SpaceCommonInteractorComponent:dtor()
end

function SpaceCommonInteractorComponent:OnMsgCommonInteractorSetBlock(insId)
    self:DebugFmt("OnMsgCommonInteractorSetBlock %s", insId)
    Game.CommonInteractorManager:UpdateCommonAreaBlock(insId, true)
end

function SpaceCommonInteractorComponent:OnMsgCommonInteractorDeleteBlock(insId)
	self:DebugFmt("OnMsgCommonInteractorDeleteBlock %s", insId)
	Game.CommonInteractorManager:UpdateCommonAreaBlock(insId, false)
end

return SpaceCommonInteractorComponent
