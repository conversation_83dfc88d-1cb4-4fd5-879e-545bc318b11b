---@class SceneActorUIActionComponent
SceneActorUIActionComponent = DefineComponent(
    "SceneActorUIActionComponent"
)

---import("ESceneActorUIActionTypes")
SceneActorUIActionComponent.UIActionEnum = {
    RailwayUIMap = 0,
    ClimbTowerPortal = 1,
    PotionsTable = 2,
    <PERSON>armacist = 3,
    StarGraph = 4,
    Poster = 5,
}

function SceneActorUIActionComponent:ctor()

end


function SceneActorUIActionComponent:__component_EnterWorld__()
    ---延迟初始化, 直接写到require的时候各种系统还没初始化
    if not SceneActorUIActionComponent.UIActionObjectMap then
        SceneActorUIActionComponent.UIActionObjectMap = {
            [SceneActorUIActionComponent.UIActionEnum.RailwayUIMap] = Game.MapSystem,
            [SceneActorUIActionComponent.UIActionEnum.PotionsTable] = Game.SequenceSystem,
            [SceneActorUIActionComponent.UIActionEnum.StarGraph] = nil,
            [SceneActorUIActionComponent.UIActionEnum.Poster] = nil,
        }
        SceneActorUIActionComponent.UIActionMap = {
            [SceneActorUIActionComponent.UIActionEnum.RailwayUIMap] = Game.MapSystem.OpenSubwayMapPanel,
            [SceneActorUIActionComponent.UIActionEnum.PotionsTable] = Game.SequenceSystem.OpenRefinePage,
            [SceneActorUIActionComponent.UIActionEnum.StarGraph] = UI.ShowUI,
            [SceneActorUIActionComponent.UIActionEnum.Poster] = UI.ShowUI,
        }
    end
end


function SceneActorUIActionComponent:__component_ExitWorld__()
end

function SceneActorUIActionComponent:OnClickSceneActorBehaviorButton()
    local UIActionEnum = self.SceneConf.UIActionType
    local ExecutionFunc = SceneActorUIActionComponent.UIActionMap[UIActionEnum]
    if ExecutionFunc then
        local ExecuteObj = SceneActorUIActionComponent.UIActionObjectMap[UIActionEnum]
        if ExecuteObj then
            ExecutionFunc(ExecuteObj, self:ParseOpenUIParams())
        else
            ExecutionFunc(self:ParseOpenUIParams())
        end
    end
end

function SceneActorUIActionComponent:ParseOpenUIParams()
end

return SceneActorUIActionComponent
