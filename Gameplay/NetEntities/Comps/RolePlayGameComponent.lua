
--- @class RolePlayGameComponent
RolePlayGameComponent = DefineComponent("RolePlayGameComponent")

function RolePlayGameComponent:OnMsgSyncRPGameInfo(chapterId, gameIndentity, moneyInfo, itemsInfo)
    Log.Debug(">>OnMsgSyncRPGameInfo", chapterId, gameIndentity, moneyInfo, itemsInfo)
    Game.RolePlaySystem:OnMsgSyncRPGameInfo(chapterId, gameIndentity, moneyInfo, itemsInfo)
end

function RolePlayGameComponent:OnMsgSyncRPGameMapInfo(mapId, gridIndex, dialogueId, gridsInfo)
    Log.Debug(">>OnEnterRolePlayGame", mapId, gridIndex, dialogueId, gridsInfo)
    Game.RolePlaySystem:OnEnterRolePlayGame(mapId, gridIndex, dialogueId, gridsInfo)
end

function RolePlayGameComponent:RetRPGameMove(rowIndex, colIndex, dialogueId)
    Log.Debug(">>RetRPGameMove", rowIndex, colIndex, dialogueId)
    Game.RolePlaySystem:OnOpenRPCard(rowIndex, colIndex)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ROLEPLAY_GAME_OPEN_DIALOGUE, dialogueId)
end

function RolePlayGameComponent:RetRPGameSelect(dialogueId, optionId)
    Game.RolePlaySystem:OnRPGameSelect(dialogueId, optionId)
end

function RolePlayGameComponent:RetRPGameHistory(chapterId, gameIndentity)
    
end

return RolePlayGameComponent