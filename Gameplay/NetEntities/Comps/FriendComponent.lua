--- @class FriendComponent
local gameConst = kg_require("Gameplay.CommonDefines.C7_Game_Const")
local const = kg_require("Shared.Const")
local bitset = kg_require("Shared.lualibs.bitset")

FriendComponent = DefineComponent("FriendComponent")

function FriendComponent:ctor()
end

function FriendComponent:sendSetTopWhisper(entity_id,isSetTop)
    self:Debug("sendSetTopWhisper")
    if isSetTop then
        self.remote.ReqDoSetTopWhisper(entity_id)
    else
        self.remote.ReqDoCancelTopWhisper(entity_id)
    end
end

--------------------------server start---------------------------------
function FriendComponent:OnMsgSyncRelationList(friendList, npcFriendList)
    Log.Dump(friendList)
    Log.Dump(npcFriendList)
    Game.FriendSystem:onSyncRelationList(friendList, npcFriendList)
    Game.ChatSystem:OnBlackListUpdate()
end

-- 推荐好友
--FRIEND_RECOMMEND_INFO_LIST: list
function FriendComponent:RetGetRecommendationList(list)
    self:Debug("onGetRecommendationListRespond")
    Log.Dump(list)
    Game.FriendSystem:onGetRecommendationListRespond(list)
end

-- 最近结识
function FriendComponent:RetGetRecentlyMeetData(list)
    Log.Dump(list)
    Game.FriendSystem:onGetRecentlyMeetDataRespond(list)
end

function FriendComponent:onAddRecentlyMeetNotify(info)
    Game.FriendSystem:onAddRecentlyMeetNotify(info)
end

function FriendComponent:onDelRecentlyMeetNotify(EntityID)
    Game.FriendSystem:onDelRecentlyMeetNotify(EntityID)
end

-- 搜索好友
-- 返回组简易信息
function FriendComponent:RetLookUpPlayer(list)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_SEARCH_FRIEND_LIST, list)
end

-- 查询详细信息
-- 点击某一个搜索好友结果时,需要显示具体信息。
function FriendComponent:RetGetPlayerDetailInfo(data)
    Log.Dump(data)
    Game.FriendSystem:onGetPlayerDetailInfoRespond(data)
end

-- 获取单个或一组EntityID的显示信息
function FriendComponent:onGetRelationInfoRespond(infoList, npcInfoList)
    Game.FriendSystem:onGetRelationInfoRespond(infoList, npcInfoList)
end

-- 获取好友列表中感兴趣字段的变化
function FriendComponent:onRefreshRelationIncDataRespond(incDataList)
    Game.FriendSystem:onRefreshRelationIncDataRespond(incDataList)
end

function FriendComponent:RetCheckBothWayFriend(isBothWayFriend, playerEntityID)
    Game.FriendSystem:onCheckBothwayFriendRespond(isBothWayFriend, playerEntityID)
end

-- 广播玩家在线/离线信息
function FriendComponent:OnMsgBroadcastPersonalState(EntityID, state)
    Game.FriendSystem:onBroadcastPersonalState(EntityID, state)
end
--
--function FriendComponent:checkGetModelInfoRespond(gbId, roleModel)
--    if gbId == self.targetGbId then
--        self:onGetAppearaceInfo(roleModel)
--        self.targetGbId = nil
--    end
--end
--
--function FriendComponent:onGetAppearaceInfo(appearance)
--    local entity = C7.entities[self.curEntityId]
--    if not entity then
--        return
--    end
--    self.appearance = appearance
--    local rotationArray = sysConfigData.data.MIND_BUBBLE_ROTATION[appearance.physique]
--    local extInfo
--    if rotationArray then
--        extInfo = {rotation = Vector3.FromArray(rotationArray)}
--    end
--    local effKey = sysConfigData.data.MIND_BUBBLE_EFFECT
--    self.curEffectId = entity:playEffect(effKey, extInfo)
--end
--
---- 获取某个玩家的模型装备信息
--function FriendComponent:onGetModelInfoRespond(EntityID, roleModel)
--    roleModel.clientCurrentStylistData = stylistUtils.unpackServerCurrentStylistData(roleModel.currentStylistData)
--    --facade:SendMessageCommand(messageName.RECEIVE_PLAYER_MODEL_INFO, {EntityID, roleModel})
--    self:checkGetModelInfoRespond(EntityID, roleModel)
--end
--
--function FriendComponent:onGetEquipmentSlotInfoRespond(EntityID, slotInfo)
--    --facade:SendMessageCommand(messageName.RECEIVE_PLAYER_EQUIPMENT_SLOT_INFO, {EntityID, true, slotInfo})
--end

-- 好友申请
-- 上线同步好友申请数量
function FriendComponent:OnMsgSyncFriendApplicationCount(count)
    Game.FriendSystem:onSyncFriendApplicationCount(count)
end

function FriendComponent:RetCheckPlayerExist(EntityID, isExist)
    self:Debug("onCheckPlayerExistRespond EntityID: ", EntityID, " Exist: ", isExist)
    Game.FriendSystem:onCheckPlayerExistRespond(EntityID, isExist)
end

-- 获取具体的好友申请列表
function FriendComponent:RetGetFriendApplicationList(list, npcIdList)
    Log.Dump(list)
    Log.Dump(npcIdList)
    Game.FriendSystem:onGetFriendApplicationListRespond(list, npcIdList)
end

-- 新好友申请通知
function FriendComponent:OnMsgAddFriendApplicationNotify(applicationInfo)
    Log.Dump(applicationInfo)
    Game.FriendSystem:onAddFriendApplicationNotify(applicationInfo)
end

function FriendComponent:onAddNpcFriendApplicationNotify(npcId)
    Game.FriendSystem:onAddNpcFriendApplicationNotify(npcId)
end

-- 处理单个好友的申请
function FriendComponent:onDelFriendApplicationNotify(EntityID)
    Game.FriendSystem:onDelFriendApplicationNotify(EntityID)
end

function FriendComponent:RetIgnoreOneFriendApplication()
    Game.FriendSystem.sender:sendGetFriendApplicationList()
end

function FriendComponent:onDelNpcFriendApplicationNotify(npcId)
    Game.FriendSystem:onDelNpcFriendApplicationNotify(npcId)
end

-- 同意全部申请
function FriendComponent:RetAcceptMultiFriendApplication(EntityIDs, npcIds)
    Game.FriendSystem:onAcceptFriendApplicationRespond(EntityIDs, npcIds)
end

-- 拒绝全部申请
function FriendComponent:RetDelMultiFriendApplication(EntityIDs, npcIds)
   Game.FriendSystem:onIgnoreFriendApplicationRespond(EntityIDs, npcIds)
end

-- 好友操作
-- 添加好友
function FriendComponent:RetAddFriendRelation(friendInfo)
    Game.FriendSystem:onAddFriendRespond(friendInfo)
end

function FriendComponent:onAddNpcFriendRespond(npcInfo)
end

-- 删除好友
function FriendComponent:RetDelFriend(EntityID)
    Game.FriendSystem:onDelFriendRespond(EntityID)
end

function FriendComponent:onDelNpcFriendRespond(npcId)
    Game.FriendSystem:onDelFriendRespond(npcId)
end

-- 好感度增加
function FriendComponent:OnMsgUpdateFriendAttraction(EntityID, value, level)
    Game.FriendSystem:onUpdateFriendAttractionNotify(EntityID, value, level)
end

-- 加入黑名单
function FriendComponent:RetAddBlackRelation(data)
    Game.FriendSystem:onAddBlackRespond(data)
end

-- 删除黑名单
function FriendComponent:RetDelBlack(EntityID)
    Game.FriendSystem:onDelBlackRespond(EntityID)
end

-- 更改分组
function FriendComponent:RetChangeGroup(EntityID, newGroupId)
    Game.FriendSystem:onChangeGroupRespond(EntityID, newGroupId)
end

-- 更改玩家备注名
function FriendComponent:RetSetFriendRemark(EntityID, remark)
    Game.FriendSystem:onSetFriendRemarkRespond(EntityID, remark)
end

function FriendComponent:onSetNpcFriendRemarkRespond(npcId, remark)
    Game.FriendSystem:onSetNpcFriendRemarkRespond(npcId, remark)
end

-- 分组操作

-- 获取好友分组
function FriendComponent:RetGetGroupList(groupList)
    Log.Dump(groupList)
    Game.FriendSystem:onGetGroupListRespond(groupList)
end

-- 添加分组
function FriendComponent:RetAddFriendGroup(groupId, groupName, EntityIDs)
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_CREATE_GROUPSET)
    Game.FriendSystem:onAddFriendGroupRespond(groupId, groupName, EntityIDs)
end

-- 编辑已有分组
function FriendComponent:RetEditFriendGroup(groupId, groupName, EntityIDs)
    Game.FriendSystem:onEditFriendGroupRespond(groupId, groupName, EntityIDs)
end

-- 删除分组
function FriendComponent:RetDelFriendGroup(groupId)
    Game.FriendSystem:onDelFriendGroupRespond(groupId)
end

-- 推送设置
function FriendComponent:onWhisperPushSettingsRespond(pushSettings)
    Game.FriendSystem:onWhisperPushSettingsRespond(pushSettings)
end

-- 在线/暂离转台设置
function FriendComponent:onSetOnlineStateRespond(state)
    Game.FriendSystem:onSetOnlineStateRespond(state)
end

function FriendComponent:OnMsgRefuseFriendFlagRespond(flag)
    Game.FriendSystem:onRefuseFriendFlagRespond(flag)
end

-- 最近联系人
-- 联系人置顶
function FriendComponent:RetSyncTopWhisperInfo(new, old, time)
    Game.FriendSystem:onSyncTopWhisperInfo(new, old, time)
end


function FriendComponent:RetTopWhisperMapChange(eid, time)
    Game.FriendSystem:onTopWhisperMapChange(eid, time)
end

function FriendComponent:OnMsgSyncTopWhisperMap(topWhisperMap)
    Game.FriendSystem:onSyncTopWhisperMap(topWhisperMap)
end

-- 同步最近联系人信息
function FriendComponent:OnMsgSyncRecentWhisperList(list, npcList)
    Game.ChatWhisperSystem:onSyncRecentWhisperList(list, npcList)
end

-- 获取最近联系人关键信息的变化
function FriendComponent:OnMsgRefreshRecentWhisper(EntityID, data)
    Game.ChatWhisperSystem:onRefreshRecentWhisperInfo(EntityID, data)
end

function FriendComponent:onRefreshFriendInfo(EntityID, data)
    Game.FriendSystem:onRefreshFriendInfo(EntityID, data)
end

function FriendComponent:RetAddRecentWhisper(recentInfo)
    Game.ChatWhisperSystem:onAddRecentWhisperRespond(recentInfo)
end

function FriendComponent:RetDelRecentWhisper(EntityID)
    self:Debug("onDelRecentWhisperRespond")
    Game.ChatWhisperSystem:onDelRecentWhisperRespond(EntityID)
end

-- 好友私聊
function FriendComponent:RetWhisper(chatInfo)
    Game.ChatWhisperSystem:onWhisper(chatInfo)
end

function FriendComponent:RetDelWhisperMessage(EntityID)
    Game.ChatWhisperSystem:onDelWhisperMessage(EntityID)
end

function FriendComponent:RetGetWhisperMessage(EntityID, list)
    Game.ChatWhisperSystem:onGetWhisperMessage(EntityID, list)
end

function FriendComponent:OnMsgUnreadWhisperInfo(list)
end

-- 系统好友消息
function FriendComponent:OnMsgSyncFriendSystemMessage(systemMsgList)
    
end

function FriendComponent:onSyncWhisperAFKReplyInfo(flag, chatInfo)
end

function FriendComponent:onGetNoTeamFriendListRespond(friends)
end

function FriendComponent:onSetPersonalSettingsRespond(info)
end

function FriendComponent:onSetFriendCirclePhotoRespond(photo)
end

function FriendComponent:onSetFriendCircleSignNameRespond(signName)
end

function FriendComponent:onGetPlayerExtraInfo(EntityID, extraInfo)
end

function FriendComponent:onSendClientFriendCircleUrl(url, webActivityUrl)
end

function FriendComponent:onGetCeDetailRespond(EntityID, info, preparePetInfo, equipmentInfo, petEquipmentInfo)
end

function FriendComponent:set_friendCircleGiftPutNum(new, old)
end

function FriendComponent:RetSetFriendStateRemindFlag(EntityID, flag)
    Game.FriendSystem:onSetFriendStateRemindFlag(EntityID, flag)
end

--region:隐身
function FriendComponent:ReqChangeShowStatus(status)
	self.remote.ReqChangeShowStatus(status)
end
function FriendComponent:RetChangeShowStatus(result)
	if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FRIEND_SHOW_STATUS_CHANGE)
	end
end

function FriendComponent:ReqSetShowStatusRange(statusList)
	self.remote.ReqSetShowStatusRange(statusList)
end

function FriendComponent:RetSetShowStatusRange(result)
	if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAT_RANGE_SAVE_SUCCESS)
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FRIEND_SHOW_STATUS_RANGE_CHANGE)
	end
end

function FriendComponent:OnMsgChangeFriendShow(entityID, status, isSet, setTime)
	local info = Game.FriendSystem:GetFriendInfoByEntityID(entityID)
	info.showStatus = status
	info.isSet = isSet
	info.setTime = setTime
	Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_ONLINE_STATES_CHANGE)--, entityID, info.showStatus, info.isSet )
end

function FriendComponent:OnMsgChangeFriendShowMode(entityID, status, setTime)
	local info = Game.FriendSystem:GetFriendInfoByEntityID(entityID)
	info.showStatus = status
	info.setTime = setTime
	Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_ONLINE_STATES_CHANGE)--, entityID, info.showStatus, info.isSet )
end

function FriendComponent:OnMsgChangeFriendSet(entityID, isSet, setTime)
	local info = Game.FriendSystem:GetFriendInfoByEntityID(entityID)
	info.isSet = isSet
	info.setTime = setTime
	Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_ONLINE_STATES_CHANGE)--, entityID, info.showStatus, info.isSet )
end

function FriendComponent:RetGmGetRecentlyWhisper(entityIdList)
    Log.Dump(entityIdList)
end

--endregion
--------------------------server end---------------------------------

-------------------------client start------------------------
-- call server

-- 获取好友数据
function FriendComponent:sendGetFriends()
    self.remote.doGetRelationListRequest()
end

function FriendComponent:sendGetFriendGroupList()
    self:Debug("sendGetFriendGroupList")
    self.remote.ReqGetGroupList()
end

--输入玩家名字查找,服务器返回onLookupPlayerRespond
function FriendComponent:sendLookupFriend(str,choice)
    self:Debug("sendLookupFriend" , str)
    if choice == nil then
        self.remote.ReqLookUpPlayer(str, true)
    else
        self.remote.ReqLookUpPlayer(str, choice)
    end
end

--向发起请求推荐好友列表
function FriendComponent:sendGetRecommendationList(int_server, int_gender)
    self:Debug("sendGetRecommendationList")
    self.remote.ReqGetRecommendationList()
end

function FriendComponent:sendGetFriendApplicationList()
    self:Debug("sendGetFriendApplicationList")
    self.remote.ReqGetFriendApplicationList()
end

function FriendComponent:sendGetRecentlyMeetList()
    self.remote.ReqGetRecentlyMeetData()
end

function FriendComponent:doGetNoTeamFriendListRequest()
end

function FriendComponent:getGuildMembersCanEnterTeam()
    local last = self.lastGetGuildMembersCanEnterTeamTime or 0
    local cur = _G._now()
    if cur - last > 3 then
        self.lastGetGuildMembersCanEnterTeamTime = cur
        self.remote.getGuildMembersCanEnterTeam()
    end
end

-- 好友申请
function FriendComponent:applyFriend(EntityID)
    self.remote.doFriendApplicationRequest(EntityID)
end

function FriendComponent:sendIgnoreFriendApplication(EntityIDList)
    self:Debug("sendIgnoreFriendApplication")
    Log.Dump(EntityIDList)
    if not EntityIDList then
        EntityIDList = self:getApplicationEntityIDList()
        local realEntityIDList = {}

        for _, id in pairs(EntityIDList) do
            realEntityIDList[#realEntityIDList + 1] = id
        end
        self.remote.ReqIgnoreAllFriendApplication(realEntityIDList)
    else
        for _, id in pairs(EntityIDList) do
            self.remote.ReqIgnoreOneFriendApplication(id)
        end
    end
end

-- 分组操作
function FriendComponent:sendAddFriendGroup(groupName, EntityIDList)
    self:Debug("sendAddFriendGroup")
    self.remote.ReqAddFriendGroup(groupName, EntityIDList)
end

function FriendComponent:sendEditFriendGroup(groupId, groupName, EntityIDList)
    self:Debug("sendEditFriendGroup")
    self.remote.ReqEditFriendGroup(groupId, groupName, EntityIDList)
end

--Bruce 另外添加的接口，C 8不存在
function FriendComponent:sendMoveUpFriendGroupRequest(GroupID)
    self:Debug("sendMoveUpFriendGroupRequest")
    self.remote.ReqMoveUpFriendGroup(GroupID)
end

--Bruce 另外添加的接口，C 8不存在
function FriendComponent:sendMoveDownFriendGroupRequest(GroupID)
    self:Debug("sendMoveDownFriendGroupRequest")
    self.remote.ReqMoveDownFriendGroup(GroupID)
end

function FriendComponent:sendDelFriendGroup(groupId)
    Game.MessageBoxSystem:AddPopupByConfig(Enum.EDialogPopUpData.FRIEND_GROUP_DELETE,function()
        self:Debug("sendDelFriendGroup:groupId :", groupId)
        self.remote.ReqDelFriendGroup(groupId)
    end)
end

-- 获取详细信息
function FriendComponent:sendGetPlayerDetailInfo(EntityID)
    self:Debug("sendGetPlayerDetailInfo ", EntityID)
    self.remote.ReqGetPlayerDetailInfo(EntityID)
end

-- 获取单个好友的信息
function FriendComponent:sendGetRelationInfoByEntityID(EntityID)
    --if npcUtils.isNpcGbId(EntityID) then
    --    self.remote.doGetRelationInfoRequest(nil, {EntityID})
    --else
        self.remote.doGetRelationInfoRequest({EntityID})
    --end
end

function FriendComponent:checkPlayerExist(EntityID)
    self:Debug("checkPlayerExist EntityID: ", EntityID)
    self.remote.ReqCheckPlayerExist(EntityID)
end

-- 联系人操作
function FriendComponent:sendAddFriend(EntityID, source, subSource)
    if self:isInBlackList(EntityID) then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.FRIEND_BLACKLIST_APPLY_FAILED)
    else
        self:doAddFriendRequest(EntityID, source, subSource)
        --self:safeCallServer(nil, true, "doAddFriendRequest", EntityID, source, subSource)
    end
end

function FriendComponent:doAddFriendRequest(EntityID, source, subSource)
    self:DebugFmt("doAddFriendRequest source %d", source)
    self.remote.ReqAddNewFriend(EntityID, source, subSource)
end

function FriendComponent:sendDelFriend(EntityID)
    --[[如果策划需要提示，则此处要加弹窗
        ]]
    self.remote.ReqDelFriend(EntityID, true)
    --end
end

function FriendComponent:sendSetFriendRemark(EntityID, str)
    self.remote.ReqSetFriendRemark(EntityID, str)
end

function FriendComponent:sendChangeGroup(EntityID, groupId)
    self:Debug("sendChangeGroup")
    self.remote.ReqChangeGroup(EntityID, groupId)
end

function FriendComponent:batchSendChangeGroup(gbId2GroupMap)
    self.remote.ReqBatchChangeGroup(gbId2GroupMap)
end

function FriendComponent:sendAddBlackList(EntityID)
    self:Debug("sendAddBlackList")
    self.remote.ReqAddBlack(EntityID)
end

function FriendComponent:sendDelBlackList(EntityID)
    self:Debug("sendDelBlackList")
    self.remote.ReqDelBlack(EntityID, false)
end

function FriendComponent:doRefreshRelationIncDataRequest()
    self.remote.doRefreshRelationIncDataRequest()
end

--FRIEND_RECOMMEND_OPERATION_MAP
function FriendComponent:doRecordRecommendationListOperation(operationMap)
    if operationMap then
        self.remote.doRecordRecommendationListOperation(operationMap)
    end
end
--最近列表
function FriendComponent:sendDelRecentWhisper(EntityID)
    self:Debug("sendDelRecentWhisper")
    self.remote.ReqDelRecentWhisper(EntityID)
end

function FriendComponent:setFriendStateRemindFlag(EntityID, flag)
    self.remote.ReqSetFriendStateRemindFlag(EntityID, flag)
end
-- 私聊
function FriendComponent:sendGetWhisperMessage(EntityID, logTime,msgCount)
    msgCount = msgCount or 25
    --self:safeCallServer(nil, nil, "doGetWhisperMessage", EntityID, logTime)
    self.remote.ReqGetWhisperMessage(EntityID, logTime,msgCount)
end

function FriendComponent:doDelWhisperMessage(EntityID)
    self.remote.ReqDelWhisperMessage(EntityID)
end

-- 判断是否是双向好友
function FriendComponent:doCheckBothwayFriendRequest(playerEntityID)
    self.remote.ReqCheckBothWayFriend(playerEntityID)
end

function FriendComponent:doGetCeDetailRequest(EntityID)
    self.remote.doGetCeDetailRequest(EntityID)
end

-------------------------client end------------------------

-------------------------data start------------------------
function FriendComponent:addRelationInfo(info)
    if not self.relationInfos then
        self.relationInfos = {}
    end
    self.relationInfos[info.id] = info
    return info
end

function FriendComponent:removeBlackRelationInfo(EntityID)
    if self.relationInfos and self.relationInfos[EntityID] then
        if self.relationInfos[EntityID].groupId == const.FRIEND_SERVER_BLACK_GROUP_ID then
            self.relationInfos[EntityID].groupId = const.FRIEND_SERVER_STRANGER_GROUP_ID
            return true
        end
    end
    return false
end

function FriendComponent:removeFriendRelationInfo(EntityID)
    if self.relationInfos and self.relationInfos[EntityID] then
        if self:isFriend(EntityID) then
            self.relationInfos[EntityID].groupId = const.FRIEND_SERVER_STRANGER_GROUP_ID
            return true
        end
    end
    return false
end

function FriendComponent:removeRecommendFriend(EntityID)
    self:removeByEntityID(self.recommendFriendList, EntityID)
end

function FriendComponent:recordRecommendExpireTime()
    local now = _G._now()
    if self.recommendExpireTime and now < self.recommendExpireTime then
        return
    end
    self.recommendExpireTime = now + gameConst.FRIEND_RECOMMEND_EXPIRE_TIME
end

function FriendComponent:setRecentlyMeetList(list)
    self.recentlyMeetList = list
end

function FriendComponent:removeRecentlyMeet(EntityID)
    self:removeByEntityID(self.recentlyMeetList, EntityID)
end

function FriendComponent:addRecentlyMeet(info)
    if not self.recentlyMeetList then
        self.recentlyMeetList = {}
    end
    self:removeByEntityID(self.recentlyMeetList, info.id)
    local list = self.recentlyMeetList
    list[#list + 1] = info
end

function FriendComponent:setPlayerDetailInfo(info)
    self.playerDetailInfo = info
end

function FriendComponent:setApplicationNotify(count)
    self.syncApplicationNotify = count > 0
end

function FriendComponent:setFriendApplicationList(list)
    self.friendApplicationList = list
end

function FriendComponent:addFriendApplication(apply)
    if not self.friendApplicationList then
        self.friendApplicationList = {}
    end
    self:removeByEntityID(self.friendApplicationList, apply.id)
    self.friendApplicationList[#self.friendApplicationList + 1] = apply
end

function FriendComponent:deleteFriendApply(EntityID)
    self:removeByEntityID(self.friendApplicationList, EntityID)
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
end

function FriendComponent:deleteProcessedFriendApply()
    if self.friendApplicationList then
        for i = #self.friendApplicationList, 1, -1 do
            if self.friendApplicationList[i].isProcessed then
                table.remove(self.friendApplicationList, i)
            end
        end
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_APPLICATION)
end

function FriendComponent:markFriendApplyProcessed(EntityIDs)
    for i,v in ipairs(EntityIDs or {}) do
        local apply = self:getFriendApplyByEntityID(v)
        if apply then
            apply.isProcessed = true
        end
    end
end

function FriendComponent:updateFriendGroup(groupId, EntityIDList)
    for _, EntityID in ipairs(EntityIDList) do
        self.relationInfos[EntityID].groupId = groupId
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.RECEIVE_FRIEND_GROUP_LIST)
end

-------------------------data end------------------------


-------------------------assist start------------------------
function FriendComponent:isFriend(EntityID)
    if self.relationInfos and self.relationInfos[EntityID] then
        local groupId = self.relationInfos[EntityID].groupId
        if groupId then
            return groupId == const.FRIEND_SERVER_NPC_GROUP_ID or (groupId >= 0 and groupId <= Game.TableData.GetConstDataRow("FRIEND_SERVER_MAX_GROUP_COUNT"))
        else
            return false
        end
    end
    return false
end

function FriendComponent:doWhisper(chatInfo)
    self.remote:ReqDoWhisper(chatInfo)
end

-- 获取所有好友玩家
function FriendComponent:getFriendPlayerList()
    if self.relationInfos then
        local lst = {}
        for _, info in pairs(self.relationInfos) do
            local groupId = info.groupId
            if groupId >= 0 and groupId <= Game.TableData.GetConstDataRow("FRIEND_SERVER_MAX_GROUP_COUNT") then
                lst[#lst + 1] = info
            end
        end
        return lst
    end
    return {}
end

function FriendComponent:getAttraction(EntityID)
    if not self.relationInfos then
        return 0
    end
    local info = self.relationInfos[EntityID]
    if not info then
        return 0
    end
    return info.attraction or 0
end

function FriendComponent:getOnLineFriendList()
    local list = {}
    for k,v in pairs(self.relationInfos or {}) do
         if self:isFriend(k) then
             if Game.FriendSystem:IsFriendOnline(v.id) then
                 list[#list + 1] = v
             end
         end
    end
    return list
end

function FriendComponent:isFriendOnline(EntityID)
    local onlineList = self:getOnLineFriendList()
    local entityID = EntityID
    local result = false;
    for k,v in pairs(onlineList) do
        if k == entityID then
            result = true;
            break;
        end
    end
    return result
end

function FriendComponent:getRelationRolename(EntityID)
    return (self.relationInfos and self.relationInfos[EntityID] or {}).rolename or ""
end

function FriendComponent:getRelationRole(EntityID)
    return self.relationInfos and self.relationInfos[EntityID]
end

function FriendComponent:isInBlackList(EntityID)
    return self.relationInfos and self.relationInfos[EntityID] and self.relationInfos[EntityID].groupId == const.FRIEND_SERVER_BLACK_GROUP_ID
end

function FriendComponent:isCrossServer(EntityID, hostId)
    return false
end

--[[
        <SYNC_RELATION_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <groupId Type="UINT" KeepNil="true" />
        <attraction Type="UINT" />
        <attractionLevel Type="UINT" />
        <remark Type="string" />
        <hostId Type="UINT" />
        <rolename Type="ROLENAME" />
        <state Type="number" />
        <lv Type="LEVEL" />
        <school Type="SCHOOL" />
        <signName Type="ARRAY_STRING" />
        <guildId Type="GUILD_ID" />
        <guildName Type="string" />
        <curHeadFrame Type="UINT" />
        <curChatBubble Type="UINT" />
        <curBadges Type="UINT_MAP" />
        <photo Type="string" />
        <awakenId Type="UINT" />
        <remindFlag Type="BIT_FLAGS" />
        <imprintMap Type="UINT_MAP" KeepNil="true" /> <!-- 好友互动印记数据 -->
        <physique Type="STATUS" />
        <sourceId Type="UINT" /> <!-- 好友来源渠道 -->
        <subSourceId Type="UINT" />
        <top Type="BOOL" KeepNil="true" /> <!-- 是否置顶 -->
    </SYNC_RELATION_INFO>
]]
function FriendComponent:getRelationsByGroupId(groupId)
    local list = {}
    for k,v in pairs(self.relationInfos or {}) do
        if groupId == const.FRIEND_SERVER_BLACK_GROUP_ID then
            if v.groupId == groupId then
                list[#list + 1] = v
            end
        else
            if v.groupId == groupId and self.relationBothWayMap[v.id] then
                list[#list + 1] = v
            end
        end
    end
    return list
end

function FriendComponent:getFriendGroupId(EntityID)
    return self.relationInfos and self.relationInfos[EntityID] and self.relationInfos[EntityID].groupId or const.FRIEND_SERVER_STRANGER_GROUP_ID
end

function FriendComponent:getFriendInfo(EntityID)
    if not EntityID then
        self:Warning("getFriendInfo: EntityID is nil ")
        return {}
    end
    if self:isFriend(EntityID) then
        if not self.relationInfos then
            self.relationInfos = {}
        end
        local info = self.relationInfos[EntityID]
        return info
    end
    return nil
end

function FriendComponent:getFriendEntityIDList()
    if not self.relationInfos then
        self.relationInfos = {}
    end
    local list = {}
    for i, _ in pairs(self.relationInfos) do
        if self:isFriend(i) then
            list[#list + 1] = i
        end
    end
    return list
end

function FriendComponent:getFriendGroupCount()
    local list = self:getFriendGroupList()
    return #list
end

function FriendComponent:getFriendStateRemindFlag(EntityID, type)
    local friend = self:getFriendInfo(EntityID)
    if friend and friend.remindFlag then
        return bitset.getBit(friend.remindFlag, type)
    end
    return false
end

function FriendComponent:getFriendGroupList()
    local list = {}
    for _, v in pairs(self.friendGroupList or {}) do
        list[#list + 1] = v
    end
    return list
end

function FriendComponent:removeByEntityID(t, EntityID)
    if not t or not EntityID then
        return
    end
    for i, v in ipairs(t) do
        if v.id == EntityID then
            table.remove(t, i)
            break
        end
    end
end

function FriendComponent:getFriendApplyByEntityID(EntityID)
   for i,v in ipairs(self.friendApplicationList or {}) do
       if v.id == EntityID then
        return v
       end
   end
   return nil
end

function FriendComponent:getApplicationEntityIDList()
    local list = {}
    for i, v in ipairs(self.friendApplicationList or {}) do
        if not v.isProcessed then
            list[i] = v.id
        end
    end
    return list
end

--申请获取双向好友的数据
function FriendComponent:reqGetBothWayFriends()
    self:Debug("reqGetBothWayFriends")
    self.remote.ReqGetBothWayFriends()
end

--回调获取双向好友数据
function FriendComponent:RetGetBothWayFriends(bothWayMap)
    Game.FriendSystem:onGetBothwayFriends(bothWayMap)
 end

function FriendComponent:updatePlayerBothWayInfo(gbId, isBothWay)
    if not self.relationBothWayMap then
        self.relationBothWayMap = {}
    end
    self.relationBothWayMap[gbId] = isBothWay
end

--调用该接口前需要保证请求过批量双向好友(c2sGetBothWayFriends)的接口
--否则无法保证是最新的
function FriendComponent:checkIsBothWayFriend(gbId)
    --[[if npcUtils.isNpcGbId(gbId) then
        return true
    end
    --]]
    if not self:GetFriendInfoByEntityID(gbId) then
        return false
    end
    if self.relationBothWayMap then
        return self.relationBothWayMap[gbId] or false
    end
    return false
end

function FriendComponent:GetBothwayFriendFrequentInfo()
    local friendList = {}
    for k,v in pairs(self.relationInfos) do
        if self:checkIsBothWayFriend(v.id)
                and Game.FriendSystem:IsFriendOnline(v.id, {[const.FRIEND_SYSTEM_PLAYER_STATE.ONLINE] = true, [const.FRIEND_SYSTEM_PLAYER_STATE.AFK] = true}) then
            table.insert(friendList, v.id)
        end
    end
    if #friendList > 0 then
        self.remote:ReqGetBothwayFriendFrequentInfo(friendList)
    else
        Game.GlobalEventSystem:Publish(EEventTypesV2.FRIEND_INFO_CHANGE)
    end
end

function FriendComponent:RetGetBothwayFriendFrequentInfo(frequentInfoMap)
    Game.FriendSystem:onGetBothwayFriendFrequentInfo(frequentInfoMap)
end

function FriendComponent:doQueryOtherPlayerOnlineState(entityID)
    self.remote:ReqGetOtherPlayerOnlineState(entityID)
end

function FriendComponent:doAddRecentWhisperRequest(entityID)
    self.remote:ReqAddRecentWhisper(entityID)
end

function FriendComponent:ReqSetNoDisturb(entityID, isSet)
	self.remote:ReqSetNoDisturb(entityID, isSet)
end

function FriendComponent:RetSetNoDisturb(result)
end

function FriendComponent:ReqChangeRecentWhipserDisturb(entityID, isSet)
	self.remote:ReqChangeRecentWhipserDisturb(entityID, isSet)
end
function FriendComponent:RetChangeRecentWhipserDisturb(result)
end

function FriendComponent:OnMsgFriendDisturbChange(entityID, isSet)
	local info = Game.FriendSystem:GetRelationInfoByEntityID(entityID)
	if info then
		info.noDisturb = isSet
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FRIEND_IS_NODISTURB_CHANGE, entityID, isSet)
	end
end

function FriendComponent:OnMsgWhisperDisturbChange(entityID, isSet)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FRIEND_IS_NODISTURB_CHANGE, entityID, isSet)
end

function FriendComponent:RetGetOtherPlayerOnlineState(entityID, onlineState)
    UI.Invoke(UIPanelConfig.PlayerCard_Panel, "OnQueryOtherPlayerOnlineStateRespond", entityID, onlineState)
end

function FriendComponent:OnMsgSingleFriendPropertyChange(entityID, changedData)
    Game.FriendSystem:OnSingleFriendPropertyChange(entityID, changedData)
end

return FriendComponent
-------------------------assist end------------------------
