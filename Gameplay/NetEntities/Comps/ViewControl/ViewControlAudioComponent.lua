---------------------------------
--- ViewControl 音效接口
--------------------------------
local UAkComponent = import("AkComponent")
local EAkResult = import("EAkResult")
local EAkCallbackType = import("EAkCallbackType")
local EAudioListenerMode = import("EAudioListenerMode")

local equipUtils = kg_require("Shared.Utils.EquipUtils")
local LOGIC_UNIT_TYPE = kg_require("Shared.Const.LogicUnitConst").LOGIC_UNIT_TYPE

-- Npc语音最小间隔
local __NpcVoiceMinInterval__ = 10 -- luacheck: ignore
local __ZeroVector__ = FVector() -- luacheck: ignore
local AK_INVALID_PLAYING_ID = 0 -- luacheck: ignore

---@class NpcVoice
---@field AkAudioEvent string
---@field RandomPeriod PVector2

-- todo@shijingzhe:使用新的客户端runtime结构封装接口
---@class ViewControlAudioComponent
ViewControlAudioComponent = DefineComponent("ViewControlAudioComponent")

ViewControlAudioComponent.AkCompClassID = 0

function ViewControlAudioComponent:ctor()
    self.akCompID = 0
    self.voicePlayingID = nil
    self.voiceTimer = nil
    self.eventName2PlayingIDs = {}
    -- 用于区分该event是否来自战斗系统
    self.eventName2Type = {}

    -- 在未EnterWorld时缓存播放请求
    ---@type table<number, table>
    self.eventPostCache = {}
    self.eventPostCacheID = 0
    ---@type table<number, number>
    self.cacheIDMap = {}
    ---@type table<string, number> 去重用,防止出生时大量相同音效叠加
    self.eventName2CacheID = {}

    ---@type table<number, table>
    self.playingID2CallbackInfo = {}
end

function ViewControlAudioComponent:dtor()
    self.akCompID = 0
    self.voicePlayingID = nil
    self.voiceTimer = nil
    self.eventName2PlayingIDs = nil
    self.eventName2Type = nil

    self.eventPostCache = nil
    self.eventPostCacheID = 0
    self.cacheIDMap = 0
end

-- EnterWorld后一些准备,主要是数据向的,业务无关的
function ViewControlAudioComponent:__component_EnterWorld__()
    -- 考虑性能优化
    if ViewControlAudioComponent.AkCompClassID == 0 then
        ViewControlAudioComponent.AkCompClassID = Game.ObjectActorManager:GetIDByClass(UAkComponent)
    end

    self.akCompID = self.CppEntity:KAPI_Actor_GetComponentByClassID(ViewControlAudioComponent.AkCompClassID)
    if self.akCompID == 0 then
        -- 两种需要动态加akComp的情况: 1.法术代理, 2.有挂接关系的法术场
        if (self.unitType == LOGIC_UNIT_TYPE.SpellAgent) or (self.unitType == LOGIC_UNIT_TYPE.SpellField and self.ATEID ~= 0) then
            self.akCompID = self.CppEntity:KAPI_Actor_AddComponentByClassID(ViewControlAudioComponent.AkCompClassID)
            self:DebugFmt("%s.%s dynamic add ak component", self.__cname, self:uid())
        else
            self:DebugWarningFmt("%s.%s has no ak component", self.__cname, self:uid())
        end
    end

    xpcall(self.initEntityAudioInfo2Cpp, CallBackError, self)
end

-- 实际处理业务相关逻辑
function ViewControlAudioComponent:__component_AfterEnterWorld__()
    -- 设置性别SwitchGroup
    local gender = self.GetActorGender and self:GetActorGender()
    if gender == 0 then
        self:AkSetSwitch(Enum.EAudioConstData.CHARACTER_GENDER_SWITCH, Enum.EAudioConstData.MALE_STATE)
    elseif gender == 1 then
        self:AkSetSwitch(Enum.EAudioConstData.CHARACTER_GENDER_SWITCH, Enum.EAudioConstData.FEMALE_STATE)
    end

    -- 处理主角逻辑
    if self == Game.me then
        -- 设置全局主角性别GroupState
        if gender == 0 then
            Game.AkAudioManager:SetGroupState(Enum.EAudioConstData.MAIN_PLAYER_GENDER_GROUP, Enum.EAudioConstData.MALE_STATE)
        elseif gender == 1 then
            Game.AkAudioManager:SetGroupState(Enum.EAudioConstData.MAIN_PLAYER_GENDER_GROUP, Enum.EAudioConstData.FEMALE_STATE)
        end

        -- 主动进行一次体力条Rtpc设置,StaminaValue是在战斗属性里的,故一部分单位没有这个属性
        if self.StaminaValue then
            self:AkSetRtpcValue(Enum.EAudioConstData.STAMINA_RTPC, self.StaminaValue)
        end

        -- 将Listener切换到人身上
        if Game.AkAudioManager.bEnableAudioListenerSwitch then
            self.CppEntity:KAPI_Audio_SwitchAudioListenerMode(EAudioListenerMode.Player)
        end
    end

    self:processEventPostCache()
end

function ViewControlAudioComponent:__component_ExitWorld__()
    if self == Game.me then
        self:AkResetRtpcValue(Enum.EAudioConstData.STAMINA_RTPC)
    end
end

function ViewControlAudioComponent:OnStaminaChanged()
    self:AkSetRtpcValue(Enum.EAudioConstData.STAMINA_RTPC, self.StaminaValue)
end

-- Npc语音,按时间随机播放
---@private
function ViewControlAudioComponent:processNpcVoice()
    local memberData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InstanceID)
    if not memberData then
        self:DebugFmt("[processNpcVoice] %s:%s no member data", self.eid, self.InstanceID)
        return
    end

    ---@type NpcVoice
    local voiceData = memberData.Voice or nil
    if not voiceData then
        self:DebugFmt("[processNpcVoice] %s:%s no voice data", self.eid, self.InstanceID)
        return
    end

    if voiceData.AkAudioEvent == "" then
        return
    end

    if (voiceData.RandomPeriod.X < __NpcVoiceMinInterval__) or (voiceData.RandomPeriod.Y < __NpcVoiceMinInterval__) then
        self:WarningFmt("[processNpcVoice] %s:%s random period cannot less than 10", self.eid, self.InstanceID)
        return
    end

    local arr = string.split(voiceData.AkAudioEvent, ".")
    local eventName = arr[#arr]
    if (eventName == nil) or (eventName == "") then
        return
    end

    self:postNpcVoice(eventName, voiceData.RandomPeriod, true)
end

---@private
---@param eventName string
---@param randomPeriod PVector2
---@param bFirstTime boolean
function ViewControlAudioComponent:postNpcVoice(eventName, randomPeriod, bFirstTime)
    if self.akCompID == 0 then
        return
    end

    if self.voicePlayingID then
        self:AkStopEvent(self.voicePlayingID)
        self.voicePlayingID = nil
    end

    if not bFirstTime then
        self.voicePlayingID = self:AkPostEventOnActor(eventName)
    end

    -- 如果是第一次,随机时间从0到随机最小值中取一个,以保证不会有过长的真空期
    local randomTime
    if bFirstTime then
        randomTime = math.random(0, randomPeriod.X)
    else
        randomTime = math.random(randomPeriod.X, randomPeriod.Y)
    end

    -- 触发下一次
    self.voiceTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:postNpcVoice(eventName, randomPeriod)
    end, randomTime * 1000, 1)
end

---@private
function ViewControlAudioComponent:stopNpcVoice()
    if self.voiceTimer then
        Game.TimerManager:StopTimerAndKill(self.voiceTimer)
        self.voiceTimer = nil
    end

    if self.voicePlayingID then
        Game.AkAudioManager:StopEvent(self.voicePlayingID)
        self.voicePlayingID = nil
    end
end

---@private
function ViewControlAudioComponent:processEventPostCache()
    for cacheID, cacheData in pairs(self.eventPostCache) do
        self:DebugFmt("[processEventPostCache] %s post cache event %s:%s", self:uid(), cacheID, cacheData.eventName)
        if cacheData.bOnActor then
            local playingID = self:AkPostEventOnActor(cacheData.eventName, cacheData.bFromBattleSystem)
            self.cacheIDMap[cacheID] = playingID
        else
            local playingID = self:AkPostEvent3D(cacheData.eventName, cacheData.location, cacheData.bFromBattleSystem)
            self.cacheIDMap[cacheID] = playingID
        end
    end

    table.clear(self.eventName2CacheID)
    table.clear(self.eventPostCache)
end

---@public
---@param relativeLoc PVector3
function ViewControlAudioComponent:SetAkCompRelativeLoc(relativeLoc)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[SetAkCompRelativeLoc] %s.%s has no ak component", self.__cname, self:uid())
        return
    end

    self.CppEntity:KAPI_SceneID_SetRelativeLocation(self.akCompID, relativeLoc.X, relativeLoc.Y, relativeLoc.Z)
end

-- 设置AkComp销毁时声音不跟随销毁
---@public
---@param bStopWhenOwnerDestroyed boolean
function ViewControlAudioComponent:SetAudioStopWhenOwnerDestroyed(bStopWhenOwnerDestroyed)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[SetAudioStopWhenOwnerDestroyed] %s.%s has no ak component", self.__cname, self:uid())
        return
    end

    self.CppEntity:KAPI_Audio_SetStopWhenOwnerDestroyed(self.akCompID, bStopWhenOwnerDestroyed)
end

---@public
---@param eventName string
---@param bFromBattleSystem boolean
---@return number
function ViewControlAudioComponent:AkPostEvent2D(eventName, bFromBattleSystem)
    return self:AkPostEvent3D(eventName, __ZeroVector__, bFromBattleSystem)
end

---@public
---@param eventName string
---@param location PVector3
---@param bFromBattleSystem boolean
---@return number
function ViewControlAudioComponent:AkPostEvent3D(eventName, location, bFromBattleSystem)
    if (eventName == nil) or (eventName == "") then
        return AK_INVALID_PLAYING_ID
    end

    -- 缓存参数
    if not self.bInWorld then
        self.eventPostCacheID = self.eventPostCacheID - 1

        local cacheParams
        local sameEventCacheID = self.eventName2CacheID[eventName]
        if sameEventCacheID then
            cacheParams = self.eventPostCache[sameEventCacheID]
            self.eventPostCache[sameEventCacheID] = nil
            self:DebugFmt("[AkPostEvent3D] %s same event %s post in loading, ignore old one", self:uid(), cacheParams.eventName)
        else
            cacheParams = { eventName = eventName, location = location, bFromBattleSystem = bFromBattleSystem, }
            self:DebugFmt("[AkPostEvent3D] %s cache event post %s:%s", self:uid(), self.eventPostCacheID, eventName)
        end

        self.eventPostCache[self.eventPostCacheID] = cacheParams
        self.eventName2CacheID[eventName] = self.eventPostCacheID

        return self.eventPostCacheID
    end

    local playingID = Game.AkAudioManager:PostEvent3D(eventName, location)
    if bFromBattleSystem then
        self.eventName2Type[eventName] = bFromBattleSystem
        Game.AkAudioManager:RecordBattleAudioEndTime(eventName, playingID)
    end

    if playingID ~= 0 then
        local playingIDs = self.eventName2PlayingIDs[eventName]
        if not playingIDs then
            playingIDs = {}
            self.eventName2PlayingIDs[eventName] = playingIDs
        end

        table.insert(playingIDs, playingID)
    end

    return playingID
end

---@public
---@param eventName string
---@param bFromBattleSystem boolean
---@return number
function ViewControlAudioComponent:AkPostEventOnActor(eventName, bFromBattleSystem)
    if (eventName == nil) or (eventName == "") then
        return AK_INVALID_PLAYING_ID
    end

    -- 缓存参数
    if not self.bInWorld then
        self.eventPostCacheID = self.eventPostCacheID - 1

        local cacheParams
        local sameEventCacheID = self.eventName2CacheID[eventName]
        if sameEventCacheID then
            cacheParams = self.eventPostCache[sameEventCacheID]
            self.eventPostCache[sameEventCacheID] = nil
            self:DebugFmt("[AkPostEventOnActor] %s same event %s post in loading, ignore old one", self:uid(), cacheParams.eventName)
        else
            cacheParams = { bOnActor = true, eventName = eventName, bFromBattleSystem = bFromBattleSystem, }
            self:DebugFmt("[AkPostEventOnActor] %s cache event post %s:%s", self:uid(), self.eventPostCacheID, eventName)
        end

        self.eventPostCache[self.eventPostCacheID] = cacheParams
        self.eventName2CacheID[eventName] = self.eventPostCacheID

        return self.eventPostCacheID
    end

    if self.akCompID == 0 then
        self:WarningFmt("[AkPostEventOnActor] %s.%s has no UAkComponent, %s post failed", self.__cname, self:uid(), eventName)
        return AK_INVALID_PLAYING_ID
    end

    local playingID = self.CppEntity:KAPI_Audio_PostEventOnAkComp(self.akCompID, eventName)
    if bFromBattleSystem then
        self.eventName2Type[eventName] = bFromBattleSystem
        Game.AkAudioManager:RecordBattleAudioEndTime(eventName, playingID)
    end

    if playingID ~= 0 then
        local playingIDs = self.eventName2PlayingIDs[eventName]
        if not self.eventName2PlayingIDs[eventName] then
            playingIDs = {}
            self.eventName2PlayingIDs[eventName] = playingIDs
        end

        table.insert(playingIDs, playingID)
    end

    return playingID
end

---播放一个事件,并在其结束时触发回调
---@public
---@param eventName string
---@param owner table
---@param callbackName string
---@return number
function ViewControlAudioComponent:AkPostEventWithCallback(eventName, owner, callbackName)
    if (owner == nil) or (type(callbackName) ~= "string") or (string.isEmpty(callbackName) == true) then
        self:WarningFmt("[AkPostEventWithCallback] invalid callback, owner=%s, callback=%s", owner, callbackName)
        return AK_INVALID_PLAYING_ID
    end

    if string.isEmpty(eventName) then
        self:Warning("[AkPostEventWithCallback] empty eventName")
        return
    end

    local playingID = self.CppEntity:KAPI_Audio_PostEventWithEndCallback(self.akCompID, eventName, EAkCallbackType.EndOfEvent)
    if playingID ~= AK_INVALID_PLAYING_ID then
        self.playingID2CallbackInfo[playingID] = {
            owner,
            callbackName,
        }
    end

    return playingID
end

---@param callbackType number
---@param playingID number
function ViewControlAudioComponent:KCB_OnEventEndCallback(callbackType, playingID)
    local callbackInfo = self.playingID2CallbackInfo[playingID]
    if not callbackInfo then
        return
    end

    if callbackType ~= EAkCallbackType.EndOfEvent then
        self:WarningFmt("[KCB_OnEventEndCallback] not match callback type %s", callbackType)
        return
    end

    local owner = callbackInfo[1]
    local callbackName = callbackInfo[2]
    if (owner ~= nil) and (type(owner[callbackName]) == "function") then
        xpcall(owner[callbackName], CallBackError, owner, callbackType, playingID)
    end
end

-- Stop所有同一个EventName的音频
---@public
---@param eventName string
function ViewControlAudioComponent:AkStopAllEventByName(eventName)
    local playingIDs = self.eventName2PlayingIDs[eventName]
    if not playingIDs then
        return
    end

    local bFromBattleSystem = self.eventName2Type[eventName]

    for idx, playingID in ipairs(playingIDs) do
        self:AkStopEvent(playingID, nil, nil, bFromBattleSystem)
        playingIDs[idx] = nil
    end

    self.eventName2Type[eventName] = nil
end

---@public
---@param playingID number
---@param blendTime number|nil
---@param blendType number|nil
---@param bFromBattleSystem boolean
function ViewControlAudioComponent:AkStopEvent(playingID, blendTime, blendType, bFromBattleSystem)
    -- 加载期间Stop掉了,清除缓存并返回
    if (playingID < 0) and (self.eventPostCache[playingID] ~= nil) then
        self.eventPostCache[playingID] = nil
        return
    end

    -- 如果有回调信息,移除
    if self.playingID2CallbackInfo[playingID] then
        self.playingID2CallbackInfo[playingID] = nil
    end

    -- 加载后播出来了,去拿真正的playingID并移除缓存
    if (playingID < 0) and (self.cacheIDMap[playingID] ~= nil) then
        playingID = self.cacheIDMap[playingID]
        self.cacheIDMap[playingID] = nil
    end

    Game.AkAudioManager:StopEvent(playingID, blendTime, blendType)
    if bFromBattleSystem then
        Game.AkAudioManager:RemoveBattleAudioEndTime(playingID)
    end
end

---@public
---@param rtpcName string
---@param rtpcValue number
function ViewControlAudioComponent:AkSetRtpcValue(rtpcName, rtpcValue)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[AkSetRtpcValue] %s.%s has no ak component", self.__cname, self:uid())
        return
    end

    self.CppEntity:KAPI_Audio_SetRtpcValueOnAkComp(self.akCompID, rtpcName, rtpcValue)
end

---@public
---@param rtpcName string
function ViewControlAudioComponent:AkResetRtpcValue(rtpcName)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[AkSetRtpcValue] %s.%s has no ak component", self.__cname, self:uid())
        return
    end

    self.CppEntity:KAPI_Audio_ResetRtpcValueOnAkComp(self.akCompID, rtpcName)
end

---@public
---@param switchGroup string
---@param switchState string
function ViewControlAudioComponent:AkSetSwitch(switchGroup, switchState)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[AkSetSwitch] %s.%s has no ak component", self.__cname, self:uid())
        return
    end

    self.CppEntity:KAPI_Audio_SetSwitchOnAkComp(self.akCompID, switchGroup, switchState)
end

---@public
---@param newScalingFactor number
function ViewControlAudioComponent:AkSetAttenuationScalingFactor(newScalingFactor)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[AkSetAttenuationScalingFactor] %s.%s has no ak component", self.__cname, self:uid())
        return
    end

    self.CppEntity:KAPI_Audio_SetAttenuationScalingFactorOnAkComp(self.akCompID, newScalingFactor)
end

---@public
---@param eventName string
---@param owner table
---@param callbackName string
function ViewControlAudioComponent:AkPostDanceEvent(eventName, owner, callbackName)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[AkPostDanceEvent] %s.%s has no ak component", self.__cname, self:uid())
        return AK_INVALID_PLAYING_ID
    end

    local playingID = self.CppEntity:KAPI_Audio_PostDanceEvent(self.akCompID, eventName, EAkCallbackType.MusicSyncBeat)
    if playingID ~= AK_INVALID_PLAYING_ID then
        self.playingID2CallbackInfo[playingID] = {
            owner,
            callbackName,
        }
    end
	return playingID
end

---@param callbackType number
---@param playingID number
---@param segmentInfo table(FAkSegmentInfo)
function ViewControlAudioComponent:KCB_OnDanceEventCallback(callbackType, playingID, segmentInfo)
    local callbackInfo = self.playingID2CallbackInfo[playingID]
    if not callbackInfo then
        return
    end

    if callbackType ~= EAkCallbackType.MusicSyncBeat then
        self:WarningFmt("[KCB_OnDanceEventCallback] not match callback type %s", callbackType)
        return
    end

    local owner = callbackInfo[1]
    local callbackName = callbackInfo[2]
    if (owner ~= nil) and (type(owner[callbackName]) == "function") then
        xpcall(owner[callbackName], CallBackError, owner, callbackType, playingID, segmentInfo)
    end
end

---@public
---@param eventName string
---@param percent number
---@param playingID number
function ViewControlAudioComponent:AkSeekOnEvent(eventName, percent, playingID)
    if self.akCompID == 0 then
        self:DebugWarningFmt("[AkSeekOnEvent] %s.%s has no ak component", self.__cname, self:uid())
        return AK_INVALID_PLAYING_ID
    end

    local result = self.CppEntity:KAPI_Audio_SeekOnEvent(self.akCompID, eventName, percent, playingID)
    if result ~= EAkResult.Success then
        self:WarningFmt("[AkSeekOnEvent] %s.%s seek %s.%s to %s failed with %s", self.__cname, self:uid(), eventName, playingID, percent, result)
    end
end


--region 部件材质相关(暂时废弃)


-- 获取鞋子材质,来源是时装或者默认
---@public
---@return string
--function ViewControlAudioComponent:GetShoesSoundMaterialName()
--    -- 没有性别,就没有逻辑
--    if not self.Sex then
--        return
--    end
--
--    -- 如果在变身中
--    if (self.MorphID ~= nil) and (self.MorphID > 0) then
--        -- todo@gushengyu:字符串枚举
--        local shoesID = self:getMorphSuitCompFashionID(Enum.EAppearanceSubType2ID.Shoes)
--        local fashionData = Game.TableData.GetFashionDataRow(shoesID)
--        if fashionData then
--            return fashionData and fashionData.SoundMaterial
--        end
--
--        return
--    end
--
--    -- 尝试获取鞋子ID
--    if self.GetFashionBySubType then
--        local shoesID = self:GetFashionBySubType(Enum.EAppearanceSubType2ID.Shoes)
--        local fashionData = Game.TableData.GetFashionDataRow(shoesID)
--        if (fashionData ~= nil) and (self.Sex ~= nil) then
--            return fashionData and fashionData.SoundMaterial
--        end
--    end
--
--    -- 尝试获取默认值
--    local playerBattleData = equipUtils.GetPlayerBattleDataRow(self.Profession, self.Sex)
--    if playerBattleData then
--        return playerBattleData.FootstepMaterial
--    end
--end

-- 获取上衣材质,来源是时装
---@public
---@return string
--function ViewControlAudioComponent:GetUpperWearSoundMaterialName()
--    -- 没有性别,就没有逻辑
--    if not self.Sex then
--        return
--    end
--
--    -- 如果在变身中
--    if (self.MorphID ~= nil) and (self.MorphID > 0) then
--        local upperWearID = self:getMorphSuitCompFashionID(Enum.EAppearanceSubType2ID.Upper)
--        local fashionData = Game.TableData.GetFashionDataRow(upperWearID)
--        if fashionData then
--            return fashionData and fashionData.SoundMaterial
--        end
--
--        return
--    end
--
--    -- 尝试获取时装
--    if self.GetFashionBySubType then
--        local upperWearID = self:GetFashionBySubType("Upper")
--        local fashionData = Game.TableData.GetFashionDataRow(upperWearID)
--        if fashionData then
--            return fashionData and fashionData.SoundMaterial
--        end
--    end
--
--    -- 尝试获取默认值
--    local playerBattleData = equipUtils.GetPlayerBattleDataRow(self.Profession, self.Sex)
--    if playerBattleData then
--        return playerBattleData.UpperWearMaterial
--    end
--end

-- 变身状态下,获取套装部件FashionID
---@private
--function ViewControlAudioComponent:getMorphSuitCompFashionID(subtype)
--    local morphData = Game.TableData.GetMorphDataRow(self.MorphID)
--    if not morphData then
--        return
--    end
--
--    local suitFashionData = Game.TableData.GetFashionDataRow(morphData.FashionID)
--    if suitFashionData == nil then
--        return
--    end
--
--    for _, suitCompID in ipairs(suitFashionData.SuitComps) do
--        local suitCompFashionData = Game.TableData.GetFashionDataRow(suitCompID)
--        if (suitCompFashionData ~= nil) and (suitCompFashionData ~= nil) and (suitCompFashionData.SubType == subtype) then
--            return suitCompID
--        end
--    end
--end


--endregion 部件材质相关(暂时废弃)


--region Test
-- luacheck: push ignore


function ViewControlAudioComponent:testStopEventByName(testEventName)
    for idx = 1, 10 do
        if idx % 2 == 0 then
            self:AkPostEvent3D(testEventName, self:GetPosition())
        else
            self:AkPostEventOnActor(testEventName)
        end
    end

    Game.TimerManager:CreateTimerAndStart(
            function()
                self:AkStopAllEventByName(testEventName)
            end,
            5000,
            1
    )
end

function ViewControlAudioComponent:testPostEventInLoading()
    for idx=1, 5 do
        self:AkPostEvent3D("Play_Visionary_Common_Heal_2P", self:GetPosition())
    end

    for idx=1, 3 do
        self:AkPostEventOnActor("Play_Visionary_Common_Heal")
    end
end


-- luacheck: pop
--endregion Test


--region cpp参数设置


---@private
function ViewControlAudioComponent:initEntityAudioInfo2Cpp()
    -- 仅玩家部分
    if self.ActorType == EWActorType.PLAYER then
        -- 默认鞋子材质
        local playerBattleData = equipUtils.GetPlayerBattleDataRow(self.Profession, self.Sex)
        if (playerBattleData ~= nil) and (string.isEmpty(playerBattleData.FootstepMaterial) == false) then
            self.CppEntity:KAPI_Audio_SetDefaultShoesMaterial(playerBattleData.FootstepMaterial)
        end

        -- 时装鞋子和上衣材质,默认设置一次,后面有变同步到cpp
        --self:setFashionMaterial2Cpp()
    end

    -- 语音类型
    local configData = self:GetEntityConfigData()
    local facadeControlData = configData and Game.TableData.GetFacadeControlDataRow(configData.FacadeControlID)
    if (facadeControlData ~= nil) and (string.isEmpty(facadeControlData.VoiceType) == false) then
        self.CppEntity:KAPI_Audio_SetVoiceType(facadeControlData.VoiceType)
    end
end

---@private
--function ViewControlAudioComponent:setFashionMaterial2Cpp()
--    -- 变身 > 时装
--    if (self.MorphID ~= nil) and (self.MorphID > 0) then
--        local upperWearID = self:getMorphSuitCompFashionID(Enum.EAppearanceSubType2ID.Upper)
--        local upperWearFashionData = Game.TableData.GetFashionDataRow(upperWearID)
--        local upperWearMaterial = upperWearFashionData and upperWearFashionData.SoundMaterial or ""
--        self.CppEntity:KAPI_Audio_SetFashionUpperWearMaterial(upperWearMaterial)
--
--        local shoesID = self:getMorphSuitCompFashionID(Enum.EAppearanceSubType2ID.Shoes)
--        local shoesFashionData = Game.TableData.GetFashionDataRow(shoesID)
--        local shoesMaterial = shoesFashionData and shoesFashionData.SoundMaterial or ""
--        self.CppEntity:KAPI_Audio_SetFashionShoesMaterial(shoesMaterial)
--    else
--        local upperWearID = self:GetFashionBySubType(Enum.EAppearanceSubType2ID.Upper)
--        local upperWearFashionData = Game.TableData.GetFashionDataRow(upperWearID)
--        local upperWearMaterial = upperWearFashionData and upperWearFashionData.SoundMaterial or ""
--        self.CppEntity:KAPI_Audio_SetFashionUpperWearMaterial(upperWearMaterial)
--
--        local shoesID = self:GetFashionBySubType(Enum.EAppearanceSubType2ID.Shoes)
--        local shoesFashionData = Game.TableData.GetFashionDataRow(shoesID)
--        local shoesMaterial = shoesFashionData and shoesFashionData.SoundMaterial or ""
--        self.CppEntity:KAPI_Audio_SetFashionShoesMaterial(shoesMaterial)
--    end
--end


--endregion cpp参数设置


return ViewControlAudioComponent
