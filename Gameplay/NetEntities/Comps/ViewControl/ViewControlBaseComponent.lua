local EDetachmentRule = import("EDetachmentRule")
local UStaticMeshComponent = import("StaticMeshComponent")
local USkeletalMeshComponent = import("SkeletalMeshComponent")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local Const = kg_require("Shared.Const")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local FaceControlComponent = import("FaceControlComponent")

local CUSTOM_DEPTH_LOGIC_TYPE_TO_PRIORITY = ViewControlConst.CUSTOM_DEPTH_LOGIC_TYPE_TO_PRIORITY
local VIEW_BUDGET_CHARACTER_TYPE = WorldViewBudgetConst.VIEW_BUDGET_CHARACTER_TYPE
local VIEW_CHARACTER_TYPE_TO_TYPE_NAME = WorldViewBudgetConst.VIEW_CHARACTER_TYPE_TO_TYPE_NAME

---------------------------------
--- ViewControl基础逻辑，包含加载，卸载，EnterWorld，ExitWorld
--------------------------------
ViewControlBaseComponent = DefineComponent("ViewControlBaseComponent")

function ViewControlBaseComponent:ctor()
    self.PreEnterWorldCommandsCaches = {}
    
    -- 表现投放角色重要度
    self.ViewRoleImportance = self:CalculateViewRoleImportance()
    self.ViewCreateBudgetToken = nil
    -- bit位设计
    self.ViewDowngradingFlags = 0
    self.ViewDowngradingTokenMapping = nil
  
	-- 角色类型相关缓存，当前用于确定特效播放优先级，后续可以用于其他表现预算控制相关逻辑
	self.CharacterTypeForViewBudget = nil
	
   

	self.EntityTag = {}
end

function ViewControlBaseComponent:dtor()
end

function ViewControlBaseComponent:AddCommandCache(CommandNames, ...)
    if self.bDuringExecuteCommand then
        self:ErrorFmt("[ViewControlBaseComponent:AddCommandCache]Try To Execute Add Command Cache During Executing CommandCache, This May Cause Infinite Loop Please Check Your Logic, EntityName : %s, Command Name : %s ", self.ENTITY_TYPE, CommandNames)
        return
    end
    table.insert(self.PreEnterWorldCommandsCaches, { CommandNames, { ... } })
end

function ViewControlBaseComponent:ExecuteCommandCaches()
    if not self.bInWorld then
        self:ErrorFmt("[ViewControlBaseComponent:ExecuteCommandCaches]Try To Execute Command Cache While Entity Not In World,Please Check Entity Enter World Logic, Entity Type : %s", self.ENTITY_TYPE)
        return
    end
    self.bDuringExecuteCommand = true

    for _, CommandsData in ipairs(self.PreEnterWorldCommandsCaches) do
        local CommandName = CommandsData[1]
        if self[CommandName] then
            xpcall(self[CommandName], _G.CallBackError, self, table.unpack(CommandsData[2]))
        end
    end
    table.clear(self.PreEnterWorldCommandsCaches)
    self.bDuringExecuteCommand = false
end

function ViewControlBaseComponent:__component_EnterWorld__()

	if self == Game.me then
		-- 特效culling 角色类型相关内容, 只有主角需要用, 本身也都是非高频逻辑暂时先用事件处理
		self.CachedTeamMemberEIDs = {}
		self:OnMainPlayerTeamInfoListChanged()
		Game.EventSystem:AddListener(_G.EEventTypes.ON_SELF_TEAMINFOLIST_CHANGED, self, self.OnMainPlayerTeamInfoListChanged, self.eid)
	end
	
	-- 为了避免事件带来开销, 这里不用事件系统处理
	self.bNeedHandleCampChange = self.isAvatar

	Game.WorldManager:EnableDeformableLandscapeCaptureMode(self:uid(), true)
	--下面是SimpleMode，理论上CaptureMode的效果够用的话，不需要开启SimpleMode，如果要用SimpleMode，还需要考虑和CaptureMode的互斥关系 <EMAIL>
	--Game.WorldManager:EnableDeformableLandscapeSimpleMode(self:uid(), "ball_r", true)
	--Game.WorldManager:EnableDeformableLandscapeSimpleMode(self:uid(), "ball_l", true)
end


function ViewControlBaseComponent:__component_ExitWorld__()
	
	Game.WorldManager:EnableDeformableLandscapeCaptureMode(self:uid(), false)
	
    if self.ViewCreateBudgetToken ~= nil then
        Game.WorldManager.ViewBudgetMgr:ReleaseViewCreateToken(self.ViewCreateBudgetToken)
        self.ViewCreateBudgetToken = nil
    end

    if self.ViewDowngradingTokenMapping ~= nil then
        Game.WorldManager.ViewBudgetMgr:ReleaseViewDownGradingTokenBatch(self.ViewDowngradingTokenMapping)
        self.ViewDowngradingTokenMapping = nil
        self.ViewDowngradingFlags = 0
    end


	if self == Game.me then
		-- 特效culling 角色类型相关内容, 只有主角需要用, 本身也都是非高频逻辑暂时先用事件处理
		Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.ON_SELF_TEAMINFOLIST_CHANGED, self, self.OnMainPlayerTeamInfoListChanged, self.eid)
	end
	
end

function ViewControlBaseComponent:SetActorViewLod(newActorViewLod, forceRefresh)
    if forceRefresh ~= true and self.ActorViewLod == newActorViewLod then
        return true
    end
    
    local oldActorViewLod = self.ActorViewLod
	self.ActorViewLod = newActorViewLod
    self:safe_call_components(Const.CALL_ENTITY_COMPONENT_STAGE.ActorViewLodChanged, oldActorViewLod, newActorViewLod)
end

-- 单位自身表现品质降级控制相关接口
function ViewControlBaseComponent:ObtainViewCreateBudgetToken(createBudgetToken)
    if self.ViewCreateBudgetToken ~= nil then
        self:WarningFmt("[ObtainViewCreateBudgetToken] %s has unreleased token old:%s, new:%s", self:uid(), self.ViewCreateBudgetToken, createBudgetToken)
        -- 要做一个保底归还
        Game.WorldManager.ViewBudgetMgr:ReleaseViewCreateToken(self.ViewCreateBudgetToken)
    end
    
    self.ViewCreateBudgetToken = createBudgetToken
end


function ViewControlBaseComponent:ObtainViewDownGradingBudgetToken(viewDowngradingTag, viewDowngradingToken)
    if self.ViewDowngradingTokenMapping == nil then
        self.ViewDowngradingTokenMapping = {}
    end

    if self.ViewDowngradingTokenMapping[viewDowngradingTag] ~= nil then
        LOG_ERROR_FMT(
                "[ViewControlBaseComponent:ObtainViewDownGradingBudgetToken] ViewTag:%s  Already Has Token:%s,  new token:%s",
                viewDowngradingTag, self.ViewCreateBudgetToken, viewDowngradingToken)
        Game.WorldManager.ViewBudgetMgr:ReleaseViewDownGradingToken(self.ViewDowngradingTokenMapping[viewDowngradingTag])
        self.ViewDowngradingTokenMapping[viewDowngradingTag] = nil
    end

    self.ViewDowngradingTokenMapping[viewDowngradingTag] = viewDowngradingToken
    self.ViewDowngradingFlags = WorldViewBudgetConst.AddViewBudgetFlagByTag(viewDowngradingTag, self.ViewDowngradingFlags)
end

function ViewControlBaseComponent:AlreadyHasViewDowngradingBudget()
    return self.ViewDowngradingFlags ~= 0
end

function ViewControlBaseComponent:HasViewDowngradingBudget(viewDowngradingTag)
	if not self.ViewDowngradingTokenMapping then
		return false
	end
    return self.ViewDowngradingTokenMapping[viewDowngradingTag] ~= nil
end

function ViewControlBaseComponent:IsViewBudgetTagDownGradingBudgetPermit(viewBudgetTag)
	if self.ViewDowngradingTokenMapping == nil then
		return false
	end

	return self.ViewDowngradingTokenMapping[viewBudgetTag] ~= nil
end

function ViewControlBaseComponent:TryRequestViewDownGradingBudgetTokenBatch(viewDownGradingTagsValue, viewDownGradingTags)
    if self.ViewDowngradingTokenMapping == nil then
        self.ViewDowngradingTokenMapping = {}
    end

    self.ViewDowngradingFlags = Game.WorldManager.ViewBudgetMgr:TryRequestViewDownGradingBudgetTokenBatch(
            viewDownGradingTags, self.ViewDowngradingFlags, self.ViewDowngradingTokenMapping, self)
    
end

----------------------------------------------- Niagara Priority Culling -----------------------------------------------------
function ViewControlBaseComponent:OnMainPlayerTeamInfoListChanged()
	for _, EID in ipairs(self.CachedTeamMemberEIDs) do
		local Entity = Game.EntityManager:getEntity(EID)
		if Entity then
			Entity.CharacterTypeForViewBudget = nil
		end
	end
	table.clear(self.CachedTeamMemberEIDs)

	for EID, _ in pairs(self.teamInfoList) do
		local Entity = Game.EntityManager:getEntity(EID)
		if Entity and Entity ~= Game.me then
			Entity.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_TEAMMATE_SAME_SQUAD
			table.insert(self.CachedTeamMemberEIDs, EID)
		end
	end
end

function ViewControlBaseComponent:HandleCampChange()
	if self == Game.me then
		-- 主角阵营信息变化时, 所有玩家都要更新下阵营缓存状态, 自身阵营变化应该不是频繁
		local AvatarActors = Game.EntityManager:getEntitiesByType(EEntityType.AvatarActor)
		if AvatarActors then
			for _, Entity in pairs(AvatarActors) do
				if Entity.CharacterTypeForViewBudget == VIEW_BUDGET_CHARACTER_TYPE.PLAYER_ENEMY or
						Entity.CharacterTypeForViewBudget == VIEW_BUDGET_CHARACTER_TYPE.PLAYER_OTHER then
					Entity.CharacterTypeForViewBudget = nil
				end
			end
		end
	elseif self.CharacterTypeForViewBudget == VIEW_BUDGET_CHARACTER_TYPE.PLAYER_ENEMY or
			self.CharacterTypeForViewBudget == VIEW_BUDGET_CHARACTER_TYPE.PLAYER_OTHER then
		self.CharacterTypeForViewBudget = nil
	end
end

function ViewControlBaseComponent:GetCharacterTypeForViewBudget()
	if self.CharacterTypeForViewBudget ~= nil then
		return self.CharacterTypeForViewBudget
	end
	
	local InstigatorEntity = BSFunc.GetActorInstigator(self)
	if InstigatorEntity ~= self then
		-- 1, 目前技能编辑器中的 GetCharacterTypeForViewBudget 是特殊的
		-- 2, 因为目前全部判定的是始作俑者跟主角之间的关系, 对于非始作俑者单位来说, 实际不希望走缓存判定, 因为一些阵营/队伍的变化实际是通知不到非始作俑者entity的, 所以这里最好
		-- 经过始作俑者再做一次 CharacterType的确定
		return InstigatorEntity:GetCharacterTypeForViewBudget()
	end

	if InstigatorEntity.ActorType == EWActorType.PLAYER then
		if InstigatorEntity == Game.me then
			self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_SELF
		elseif Game.TeamSystem:IsTeamMember(InstigatorEntity.eid) or Game.GroupSystem:IsInSameTeam(Game.me.eid, InstigatorEntity.eid) then
			self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_TEAMMATE_SAME_SQUAD
		elseif Game.GroupSystem:InSameGroupAndNotInSameTeam(InstigatorEntity.eid) then
			self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_TEAMMATE_DIFFERENT_SQUAD
		elseif BSFunc.GetFinalCampRelation(self, Game.me) == Enum.ECampEnumData.Enemy then
			self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_ENEMY
		else
			self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.PLAYER_OTHER
		end
	elseif InstigatorEntity.ActorType == EWActorType.NPC then
		self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.MONSTER_OTHER
		
		-- 如果NPC可以变为怪物的话, 对应的BossType也会动态做修改
		if InstigatorEntity.BossType == Enum.EBossType.BOSS then
			self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.MONSTER_BOSS
		else
			local MemberData = Game.WorldDataManager:GetCurLevelSceneActorData(self.InstanceID)
			if MemberData and MemberData.bImportNpc then
				self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.MONSTER_IMPORTANT_GAMEPLAY_USAGE
			elseif InstigatorEntity.BossType == Enum.EBossType.Elite then
				self.CharacterTypeForViewBudget = VIEW_BUDGET_CHARACTER_TYPE.MONSTER_ELITE
			end
		end
	end

	return self.CharacterTypeForViewBudget
end

function ViewControlBaseComponent:DetachFromOtherActor()
	self.CppEntity:KAPI_Actor_K2_DetachFromActor(EDetachmentRule.KeepWorld,EDetachmentRule.KeepRelative,EDetachmentRule.KeepRelative)
	if self.ChangeAttachMode then
		self:ChangeAttachMode(false)
	end
end

function ViewControlBaseComponent:AddTag(NewTag)
	self.EntityTag[NewTag] = true
end

function ViewControlBaseComponent:RemoveTag(NewTag)
	self.EntityTag[NewTag] = nil
end

function ViewControlBaseComponent:CleatTags()
	table.clear(self.EntityTag)
end

function ViewControlBaseComponent:EntityHasTag(Tag)
	return self.EntityTag[Tag]
end

function ViewControlBaseComponent:EntityHasTags(NewTags)
	for K,Tag in pairs(NewTags) do
		if not  self.EntityTag[Tag] then
			return false
		end
	end
	return true

end

--UEActor回调通知 Begin ---------------------------


--目前角色身体部件Tag 在 Enum.EAvatarBodyPartTypeName


--UEActor回调通知 End ---------------------------



function ViewControlBaseComponent:DebugDumpTable(debugInfos, HeadName, Table, Depth, MaxDepth)
	if SHIPPING_MODE then
		return
	end
    local IndentTxt =string.rep("  ", Depth)
    if Depth > MaxDepth then
        table.insert(debugInfos, string.format("<Text>%s[%s] : [Dump Max Depth Reached] %s </>", IndentTxt ,HeadName,tostring(Table)))
        return
    end

    if Table == nil then
        table.insert(debugInfos, string.format("<Text>%s[%s] : nil </>", IndentTxt ,HeadName))
        return
    end


    table.insert(debugInfos, string.format("<Text>%s[%s] : { </>", IndentTxt ,HeadName))
    for K, V in pairs(Table) do
        if type(V) == "table" then
            self:DebugDumpTable(debugInfos, tostring(K), V , Depth + 1, MaxDepth)
        else
            table.insert(debugInfos, string.format("<Text>%s [%s] : %s </>", IndentTxt ,tostring(K), tostring(V)))
        end
    end
    table.insert(debugInfos, string.format("<Text>%s}</>",IndentTxt))
end

function ViewControlBaseComponent:__component_AppendGamePlayDebugInfo__(debugInfos)
    table.insert(debugInfos, "<Title_Red>[ViewControlBaseComponent]</>")
    table.insert(debugInfos, string.format('NPC Type:%s  TemplateId:%s  ', self.NpcType, self.TemplateID))
    table.insert(debugInfos, string.format('ViewRoleImportance:%s  VBudgetToken:%s  VDowngradingFlags:%s', self.ViewRoleImportance, self.ViewCreateBudgetToken, self.ViewDowngradingFlags))
    table.insert(debugInfos, string.format('ActorViewLod:%s  ', self.ActorViewLod))
	local CharacterType = self:GetCharacterTypeForViewBudget()
	table.insert(debugInfos, string.format("CharacterTypeForViewBudget: %s", CharacterType and VIEW_CHARACTER_TYPE_TO_TYPE_NAME[CharacterType] or "Invalid"))
    table.insert(debugInfos, "")
end

function ViewControlBaseComponent:OverrideCustomDepth(LogicType, bEnable, StencilValue, bNiagaraRenderCustomDepth, bAutoRevert, AutoReverTimeSeconds)
	local Priority = CUSTOM_DEPTH_LOGIC_TYPE_TO_PRIORITY[LogicType]
	if Priority == nil then
		self:ErrorFmt("[ViewControlBaseComponent:OverrideCustomDepth] Invalid LogicType:%s", LogicType)
		return
	end
	
	self.CppEntity:KAPI_Actor_OverrideCustomDepth(
			LogicType, Priority, bEnable, StencilValue, 
			bNiagaraRenderCustomDepth ~= nil and bNiagaraRenderCustomDepth or false,
			bAutoRevert ~= nil and bAutoRevert or false,
			bAutoRevert == true and AutoReverTimeSeconds or 0.0) -- bAutoRevert设置的情况下, 外部必须传入一个 AutoReverTimeSeconds
end

function ViewControlBaseComponent:SetEnableNearlyFace(bEnable)
	if not self.bInWorld then
		self:AddCommandCache("SetEnableNearlyFace", bEnable)
		return
	end

	local FaceComponentID = self.CppEntity:KAPI_Actor_GetComponentByClass(FaceControlComponent)
	if FaceComponentID ~= 0 then
		self.CppEntity:KAPI_FaceControlID_SetEnableNearlyFace(FaceComponentID, bEnable)
	end
end

function ViewControlBaseComponent:RemoveCustomDepthOverride(LogicType)
	self.CppEntity:KAPI_Actor_RevertCustomDepth(LogicType)
end

return ViewControlBaseComponent