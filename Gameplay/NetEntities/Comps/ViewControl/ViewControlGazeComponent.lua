local EGazeTargetType = import("EGazeTargetType")

---------------------------------
--- ViewControl gaze接口
--------------------------------
---@interface ViewControlGazeComponent
ViewControlGazeComponent = DefineComponent("ViewControlGazeComponent")

local GazeParams = DefineClass("GazeParams")
GazeParams.__PoolSize = 50
--GazeParams.Pool = {}
ViewControlGazeComponent.DefaultBlendInAlpha = 2
ViewControlGazeComponent.DefaultBlendOutAlpha = 1.5
ViewControlGazeComponent.DefaultEyeBS_Range_Hori = 45
ViewControlGazeComponent.DefaultEyeBS_Range_Vert = 30

-- GAZE 触发对应逻辑函数
ViewControlGazeComponent.GazeEnableExecuteFun = {
	[EGazeTargetType.Camera] = "SetGazeCurrentCamera",
	[EGazeTargetType.Character] = "SetGazeTargetCharacter",
	[EGazeTargetType.WorldPos] = "SetGazeTargetLocation",
	[EGazeTargetType.CustomGazeCoord] = "SetGazeByCoord",
}


function GazeParams.GetFromPool()
	--if #GazeParams.Pool == 0 then
	--	return GazeParams.new()
	--else
	--	return table.remove(GazeParams.Pool, 1)
	--end
	return Game.ObjectPoolManager:AllocateObject(GazeParams)
end

function GazeParams.ReturnToPool(Item)
	--if #GazeParams.Pool < GazeParams.PoolSize then
	--	Item:Reset()
	--	table.insert(GazeParams.Pool,Item)
	--end
	Game.ObjectPoolManager:ReleaseObject(Item)
end

function GazeParams:on_recycle_to_pool()
	self:Reset()
end

function GazeParams:ctor()
	self:Reset()
end

function GazeParams:Reset()
	self.GazeType = EGazeTargetType.None
	
	self.GazeWorldPos = self.GazeWorldPos or FVector()
	self.GazeWorldPos.X = 0
	self.GazeWorldPos.Y = 0
	self.GazeWorldPos.Z = 0
	
	self.GazeCharacterID = nil
	self.GazeCharacterSocket = nil
	self.GazeCharacterSocketOffset = nil
	
	self.GazeCoordRangeX = nil
	self.GazeCoordRangeY = nil
	self.GazeCoordDist = nil
	
	self.GazeCoordX = nil
	self.GazeCoordY = nil
end



function ViewControlGazeComponent:ctor()
    self.GazeTasks = {}
    self.bHasGazeTask = false
    self.GazePauseTimer = nil
    self.GazePauseCount = 0
	self.bListenedSkillEvent = false
end

function ViewControlGazeComponent:dtor()
    self:ClearGazePauseTimer()
end

function ViewControlGazeComponent:__component_ExitWorld__()
    self:ClearGazePauseTimer()
	if self.bListenedSkillEvent then
		self:UnBindEventForGazePause()
	end
end

function ViewControlGazeComponent:__component_AfterStartMorph__(MorphData)
	if MorphData.bDisableGaze then
		self:TryPauseGaze()
	end
end

function ViewControlGazeComponent:__component_AfterEndMorph__(MorphData)
	if MorphData.bDisableGaze then
		self:TryUnpauseGaze()
	end
end

function ViewControlGazeComponent:CheckStartGaze(bImmediateBlendOut)
    if not self.bInWorld then
        return
    end

    local MaxPriorityTask = nil
    local MaxPriority = nil

    if self.GazePauseCount <= 0 then
        for K,V in pairs(self.GazeTasks) do
            local GazeData = Game.TableData.GetGazeConfigDataRow(K)
            if GazeData and (MaxPriorityTask == nil or MaxPriority < GazeData.Priority) then
                MaxPriorityTask = K
                MaxPriority = GazeData.Priority
            end
        end
    end
	
	
    local Params = self.GazeTasks[MaxPriorityTask]
	if Params then
		local enableFunName = ViewControlGazeComponent.GazeEnableExecuteFun[Params.GazeType]
		if enableFunName then
			if not self.bListenedSkillEvent then
				self.bListenedSkillEvent = true
			end
			
			self.bHasGazeTask = true
			self.CurrentGazeTask = MaxPriorityTask
			self:LinkGazeFeatureAnimLayer(false)
			self[enableFunName](self, Params)
			self:ApplyGazeConfig(MaxPriorityTask)
		else
			self:SetUnGaze(bImmediateBlendOut)
			self.CurrentGazeTask = EGazeTargetType.None
			self.bHasGazeTask = false
		end
	elseif self.bHasGazeTask then
		self:SetUnGaze(bImmediateBlendOut)
		self.CurrentGazeTask = EGazeTargetType.None
		self.bHasGazeTask = false
	end

	if self.bListenedSkillEvent and next(self.GazeTasks) == nil then
		self:UnBindEventForGazePause()
	end
end

function ViewControlGazeComponent:UnBindEventForGazePause()
	self.bListenedSkillEvent = false
end

function ViewControlGazeComponent:TryPauseGaze()
	self:PauseGaze()
end

function ViewControlGazeComponent:TryUnpauseGaze()
	self:UnPauseGaze()
	self:PauseGazeByTime(2)
end

function ViewControlGazeComponent:SetGazeTargetCharacter(Params)
	self.CppEntity:KAPI_Animation_SetGazeTargetCharacter(Params.GazeCharacterID, Params.GazeCharacterSocket, Params.GazeCharacterSocketOffset)
end

function ViewControlGazeComponent:SetGazeTargetLocation(Params)
	self.CppEntity:KAPI_Animation_SetGazeTargetLocation(Params.GazeWorldPos)
end

function ViewControlGazeComponent:SetGazeByCoord(Params)
	self.CppEntity:KAPI_Animation_SetGazeByCoord(Params.GazeCoordRangeX,Params.GazeCoordRangeY,Params.GazeCoordDist)
end

function ViewControlGazeComponent:ApplyGazeConfig(GazeType)
    local GazeData = Game.TableData.GetGazeConfigDataRow(GazeType)
    local GazeCfg  = import("AnimGazeConfig")()
    if GazeData then
		self:SetGazeBlendAlpha(
			ViewControlGazeComponent.DefaultBlendInAlpha,
			ViewControlGazeComponent.DefaultBlendOutAlpha)
		
		self:SetGazeRotateConfig( 
			GazeData.ClampAngle,
			GazeData.LocationInterpSpeed)

		self:InitBasicGazeConfig(
			ViewControlGazeComponent.DefaultEyeBS_Range_Hori,
			ViewControlGazeComponent.DefaultEyeBS_Range_Vert)
		
		self:SetGazeEyeRotateConfig(  
			GazeData.EyeRotateSpeed or 0, 
			GazeData.EyeClampHoriz[1] or 0, 
			GazeData.EyeClampHoriz[2] or 0, 
			GazeData.EyeClampVert[1] or 0,
			GazeData.EyeClampVert[2] or 0)
		
		self:SetGazeHeadRotateConfig(
			GazeData.HeadRotateSpeed or 0,
			GazeData.HeadClampHoriz[1] or 0,
			GazeData.HeadClampHoriz[2] or 0,
			GazeData.HeadClampVert[1] or 0,
			GazeData.HeadClampVert[2] or 0
		)

		self:SetGazeSpineRotateConfig(
			GazeData.SpineRotateSpeed,
			GazeData.SpineRotateBlendRate or 0,
			GazeData.SpineClampHoriz[1] or 0,
			GazeData.SpineClampHoriz[2] or 0,
			GazeData.SpineClampVert[1] or 0,
			GazeData.SpineClampVert[2] or 0
		)

		self:SetGazeNeckRotateConfig(
			GazeData.NeckRotateSpeed,
			GazeData.NeckRotateBlendRate or 0,
			GazeData.NeckClampHoriz[1] or 0,
			GazeData.NeckClampHoriz[2] or 0,
			GazeData.NeckClampVert[1] or 0,
			GazeData.NeckClampVert[2] or 0
		)
		
    end

    return GazeCfg
end



function ViewControlGazeComponent:StartGazeLocation_EX(GazeReason,WorldLocationX,WorldLocationY,WorldLocationZ)
	if GazeReason then
		if self.GazeTasks[GazeReason] then
			self.GazeTasks[GazeReason]:Reset()
		else
			self.GazeTasks[GazeReason] = GazeParams.GetFromPool()
		end
		
		self.GazeTasks[GazeReason].GazeType = EGazeTargetType.WorldPos
		self.GazeTasks[GazeReason].GazeWorldPos.X = WorldLocationX
		self.GazeTasks[GazeReason].GazeWorldPos.Y = WorldLocationY
		self.GazeTasks[GazeReason].GazeWorldPos.Z = WorldLocationZ
		
		self:CheckStartGaze()
	end
end

function ViewControlGazeComponent:StartGazeLocation(GazeReason,Location)
	if GazeReason then
		if self.GazeTasks[GazeReason] then
			self.GazeTasks[GazeReason]:Reset()
		else
			self.GazeTasks[GazeReason] = GazeParams.GetFromPool()
		end

		self.GazeTasks[GazeReason].GazeType = EGazeTargetType.WorldPos
		self.GazeTasks[GazeReason].GazeWorldPos.X = Location.X
		self.GazeTasks[GazeReason].GazeWorldPos.Y = Location.Y
		self.GazeTasks[GazeReason].GazeWorldPos.Z = Location.Z

		self:CheckStartGaze()
	end
end

function ViewControlGazeComponent:StartGazeActor(GazeReason,ActorID,GazeCharacterSocket,Offset)
	if GazeReason then
		if self.GazeTasks[GazeReason] then
			self.GazeTasks[GazeReason]:Reset()
		else
			self.GazeTasks[GazeReason] = GazeParams.GetFromPool()
		end

		self.GazeTasks[GazeReason].GazeType = EGazeTargetType.Character
		self.GazeTasks[GazeReason].GazeCharacterID = ActorID
		self.GazeTasks[GazeReason].GazeCharacterSocket = GazeCharacterSocket
		self.GazeTasks[GazeReason].GazeCharacterSocketOffset = Offset or FVector()
		
		self:CheckStartGaze()
	end
end

function ViewControlGazeComponent:StartGazeCamera(GazeReason)
	inParams = inParams or {}
	if GazeReason then
		if self.GazeTasks[GazeReason] then
			self.GazeTasks[GazeReason]:Reset()
		else
			self.GazeTasks[GazeReason] = GazeParams.GetFromPool()
		end

		self.GazeTasks[GazeReason].GazeType = EGazeTargetType.Camera

		self:CheckStartGaze()
	end
end

ViewControlGazeComponent.DefaultGazeCoordRangeX = 60
ViewControlGazeComponent.DefaultGazeCoordRangeY = 40
ViewControlGazeComponent.DefaultGazeCoordDist = 100
ViewControlGazeComponent.DefaultGazeCoordInitX = 0.5
ViewControlGazeComponent.DefaultGazeCoordInitY = 0.5
function ViewControlGazeComponent:StartGazeByCoord(GazeReason,GazeCoordRangeX,GazeCoordRangeY,GazeCoordDist,InitCoordX,InitCoordY)
	inParams = inParams or {}
	if GazeReason then
		if self.GazeTasks[GazeReason] then
			self.GazeTasks[GazeReason]:Reset()
		else
			self.GazeTasks[GazeReason] = GazeParams.GetFromPool()
		end

		self.GazeTasks[GazeReason].GazeType = EGazeTargetType.CustomGazeCoord
		self.GazeTasks[GazeReason].GazeCoordRangeX = GazeCoordRangeX or ViewControlGazeComponent.DefaultGazeCoordRangeX
		self.GazeTasks[GazeReason].GazeCoordRangeY =GazeCoordRangeY or ViewControlGazeComponent.DefaultGazeCoordRangeY
		self.GazeTasks[GazeReason].GazeCoordDist = GazeCoordDist or ViewControlGazeComponent.DefaultGazeCoordDist

		self.GazeTasks[GazeReason].GazeCoordX = InitCoordX or ViewControlGazeComponent.DefaultGazeCoordInitX 
		self.GazeTasks[GazeReason].GazeCoordY = InitCoordY or ViewControlGazeComponent.DefaultGazeCoordInitY
		
		self:CheckStartGaze()
	end
end


function ViewControlGazeComponent:PauseGazeByTime(Time)
    self:ClearGazePauseTimer()
    self.GazePauseCount = self.GazePauseCount + 1

    if self.bHasGazeTask then
        self:CheckStartGaze()
    end

    self.GazePauseTimer = Game.TimerManager:CreateTimerAndStart(function()
        self.GazePauseTimer = nil
        self.GazePauseCount = self.GazePauseCount -1
        self:CheckStartGaze()
    end, Time * 1000, 1)
end

function ViewControlGazeComponent:ClearGazePauseTimer()
    if self.GazePauseTimer then
        Game.TimerManager:StopTimerAndKill(self.GazePauseTimer)
        self.GazePauseCount = self.GazePauseCount -1
        if self.bHasGazeTask then
            self:CheckStartGaze()
        end
    end
end

function ViewControlGazeComponent:PauseGaze()
    self.GazePauseCount = self.GazePauseCount + 1
    if self.bHasGazeTask then
        self:CheckStartGaze()
    end
end

function ViewControlGazeComponent:UnPauseGaze()
    self.GazePauseCount = self.GazePauseCount - 1
	if self.bHasGazeTask then
		self:CheckStartGaze()
	end
end

function ViewControlGazeComponent:UnGaze(GazeReason, bImmediate)
    if GazeReason then
		if  self.GazeTasks[GazeReason] then
			GazeParams.ReturnToPool( self.GazeTasks[GazeReason])
			self.GazeTasks[GazeReason] = nil
		end

        self:CheckStartGaze(bImmediate)
    end
end

function ViewControlGazeComponent:SetGazeCoord(GazeReason,CoordX,CoordY)
	if self.GazeTasks[GazeReason] then
		self.GazeTasks[GazeReason].GazeCoordX = CoordX
		self.GazeTasks[GazeReason].GazeCoordX = CoordY
		if self.CurrentGazeTask == GazeReason then
			self:UpdateGazeCoord(CoordX,CoordY)
		end
	end

end

--function ViewControlGazeComponent:SwitchGazeType(GazeReason,InType)
--    if self.GazeTasks[GazeReason] then
--        self.GazeTasks[GazeReason].GazeType = InType
--        self:CheckStartGaze()
--    end
--end


return ViewControlGazeComponent