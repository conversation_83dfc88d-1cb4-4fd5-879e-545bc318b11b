local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")
local ULLFunc = import("LowLevelFunctions")
local AnimLibHelper = kg_require("GamePlay.3C.RoleComposite.AnimLibHelper")

SimpleViewControlAnimComponent = DefineComponent("SimpleViewControlAnimComponent")

SimpleViewControlAnimComponent.MAIN_MESH_ID = SimpleViewControlAnimComponent.MAIN_MESH_ID or 0

SimpleViewControlAnimComponent.NAME_NONE = SimpleViewControlAnimComponent.NAME_NONE or "None"

SimpleViewControlAnimComponent.EActionBlendRule = SimpleViewControlAnimComponent.EActionBlendRule or {
	FullBodyOverride = 0,
	PartBodyOverride = 1,
	PartBodyOverrideOnlyWhenMoving = 2,
}

function SimpleViewControlAnimComponent:ctor()
	self.AnimPlayReqMap = {}
	self.AnimPlayReqIDOnSlot = {}
	self.LoadIdMap = {} --记录下加载ID,为了手动管理释放和取消
	self.CacheAnimPlayReqMap = {}
	self.bIsAnimComRdy = false

	self.LastAnimInsUGID = nil
	self.bAnimSingleNodeMode = true

	-- 动画加载与管理机制(非LRU部分), 详见 https://docs.corp.kuaishou.com/k/home/<USER>/fcABX6a6j978yml-H1MyD1-Up
	self.baseAnimAssetID = self:GetConfigAnimAssetID()
	self.currentWorkingAnimAssetID = self.baseAnimAssetID
	self.overrideAnimAssetID = nil
end

function SimpleViewControlAnimComponent:dtor()
	for LoadId, _ in pairs(self.LoadIdMap) do
		self:releaseLoadHandle(LoadId)
		Game.AssetManager:CancelLoadAsset(LoadId)
	end
	table.clear(self.LoadIdMap)

	for _, CacheTask in ipairs(self.CacheAnimPlayReqMap) do
		AnimPlayReqCacheTask.ReturnToPool(CacheTask)
	end
	table.clear(self.CacheAnimPlayReqMap)
end

function SimpleViewControlAnimComponent:__component_LoadActor__()
	local ModelID = self:GetConfigModelID()
	if ModelID then
		if Game.ActorAppearanceManager:HasAnimClass(ModelID) then
			self.bAnimSingleNodeMode = false
			return
		elseif Game.ActorAppearanceManager:HasSKMesh(ModelID) then
			self.bAnimSingleNodeMode = false
			return
		end
	end

	self.bAnimSingleNodeMode = true
end

function SimpleViewControlAnimComponent:__component_AfterEnterWorld__()
	self:InitAnimInstanceForScript()
	Game.WorldManager:EnableDeformableLandscapeCaptureMode(self:uid(), true)

end

function SimpleViewControlAnimComponent:__component_ExitWorld__()
	Game.WorldManager:EnableDeformableLandscapeCaptureMode(self:uid(), false)
end


--其他地方(TaskEnd回调)会在ExitWorld，用到一些AnimIns相关的功能，移动到AfterExitWorld再清理 @hujianglong
function SimpleViewControlAnimComponent:__component_AfterExitWorld__()

	self:UnInitAnimInstanceForScript()

	self:SetOverrideAnimAssetID(nil)
end

-- 动画逻辑数据准备就绪, 可以操作动画了
function SimpleViewControlAnimComponent:SetAnimControlReadyForAppearanceChange()
	-- 内部会进行bIsAnimComRy设置为true
	self:InitAnimInstanceForScript()
end

-- 动画逻辑控制冻结, 播放动画暂停
function SimpleViewControlAnimComponent:SetAnimControlSuspendForAppearanceChange()
	self.bIsAnimComRdy = false
	-- 这里不能直接做UninitAnimInstanceForScript, 否则表现上会被停止所有动画, 会出现回Idle的短暂情况
	self.CppEntity:KAPI_Animation_UnInitAnimComponentParams()
end

function SimpleViewControlAnimComponent:InitAnimInstanceForScript()
	self:DebugFmt("SimpleViewControlAnimComponent:InitAnimInstanceForScript EID:%s", self.eid)

	-- 要放最前面, 后面要reset之后进行初始化
	self:resetAnimComponentParams(true)
	
	local CppEntity = self.CppEntity
	--todo 临时兼容LocalAttachItem没有skeletalMeshComponent, 需要源头就分开, 不挂无效逻辑component @俊杰
	if CppEntity:KAPI_Actor_HasMainMesh() == false then
		return
	end

	local bHasAnimIns = CppEntity:KAPI_Animation_HasAnimInstance()

	if bHasAnimIns and not CppEntity:KAPI_Animation_IsAnimationSingleNode() then
		self.bAnimSingleNodeMode = false
	else
		CppEntity:KAPI_Animation_SetAnimationSingleMode()
		self.bAnimSingleNodeMode = true
	end

	if not self.CppEntity:KAPI_Animation_HasAnimInstance() then
		return
	end

	self.LastAnimInsUGID = self.CppEntity:KAPI_Animation_InitAnimComponentParams((Game.me and self.eid == Game.me.eid) and 1 or 0, SimpleViewControlAnimComponent.NAME_NONE)
	self.CppEntity:KAPI_Animation_EnableAnimationLog(Log.Level <= LogLevel.DebugLog)

	self.bIsAnimComRdy = true
	self:consumeAnimPlayReq()
end

function SimpleViewControlAnimComponent:UnInitAnimInstanceForScript()
	self.bIsAnimComRdy = false
	self:resetAnimComponentParams()
	self.CppEntity:KAPI_Animation_UnInitAnimComponentParams()
end

function SimpleViewControlAnimComponent:resetAnimComponentParams(bSkipAssetClear)
	self.CppEntity:KAPI_Animation_ResetAnimComponentParams()

	table.clear(self.AnimPlayReqIDOnSlot)
	local AnimPlayOnOtherMeshes = self.AnimPlayOnOtherMeshes
	if AnimPlayOnOtherMeshes then
		table.clear(AnimPlayOnOtherMeshes)
	end

	for _, Config in pairs(self.AnimPlayReqMap) do
		self:releaseAnimPlayReqConfigTable(Config)
	end

	-- TODO 临时补丁 修复剧编资源加载被清的问题 @zhaojunjie
	if not bSkipAssetClear then
		for LoadId, _ in pairs(self.LoadIdMap) do
			self:releaseLoadHandle(LoadId)
			Game.AssetManager:CancelLoadAsset(LoadId)
		end
	end
end

function SimpleViewControlAnimComponent:SetSkeletalMeshAssetAndAnimationMode(AssetID, AnimationMode)
	if AssetID and IsValidID(AssetID) then
		self.CppEntity:KAPI_Animation_SetAnimationMode(AnimationMode)
		self.CppEntity:KAPI_SkeletalMeshID_SetSkeletalMeshAsset(self.CppEntity:KAPI_Actor_GetMainMesh(), AssetID)
	end
	self:InitAnimInstanceForScript()
end

-- 设置单位全局骨骼动画播放速率(用于实现类似子弹时间的功能,设置为0侧可以将动画静止在当前帧)
---@public
---@param newValue number
function SimpleViewControlAnimComponent:SetGlobalAnimRateScale(newValue)
	self.CppEntity:KAPI_Animation_SetGlobalAnimRateScale(newValue)
end

-- 有且只有引擎LOD切换引起的animinstance的变化回调回来, 脚本层的外观引起的变化都有脚本自己控制生命周期释放和初始化
function SimpleViewControlAnimComponent:KCB_OnAnimInitializedToRefreshRefBinding()
	local currentAnimInstUGID = self.CppEntity:KAPI_Animation_GetMainAnimInstanceID()
	if currentAnimInstUGID == self.LastAnimInsUGID then
		return
	end
	
	self:UnInitAnimInstanceForScript()
	self:InitAnimInstanceForScript()
end

function SimpleViewControlAnimComponent:SetOverrideAnimAssetID(animAssetID)
	if self.overrideAnimAssetID ~= nil and animAssetID ~= nil then
		self:ErrorFmt("[SimpleViewControlAnimComponent:SetOverrideAnimAssetID] Need Clean Override Anim Asset ID Firstly and Then Set New One, Current:%s,  New:%s", self.overrideAnimAssetID, animAssetID)
	end
	
	self.overrideAnimAssetID = animAssetID

	if self.overrideAnimAssetID ~= nil then
		self.currentWorkingAnimAssetID = self.overrideAnimAssetID
	else
		self.currentWorkingAnimAssetID = self.baseAnimAssetID
	end
end

function SimpleViewControlAnimComponent:GetCurrentWorkingAnimAssetID()
	return self.currentWorkingAnimAssetID
end

-- 非主Mesh动画用这个接口，主Mesh请用其他接口
function SimpleViewControlAnimComponent:PlayAnimationOnMesh(MeshID, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop)
	if not AnimPath or AnimPath == "" then
		Log.Error("SimpleViewControlAnimComponent:PlayAnimationOnMesh Get Empty AnimPath!")
		return
	end

	if not MeshID or MeshID == SimpleViewControlAnimComponent.MAIN_MESH_ID then
		Log.Error("SimpleViewControlAnimComponent:PlayAnimationOnMesh Get InValid Mesh For Function!")
		return
	end

	return self:PlayCPPAnimationLogic(MeshID, ViewAnimConst.DEFAULT_SLOT_CONST, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop)
end

function SimpleViewControlAnimComponent:PlayAnimationByTag(AnimPlayReqTag, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop, OnMontageBlendingOutObj, OnMontageBlendingOutCb, bRefreshViewWhenSameMontage, bStopAllMontages, bLeadingAnimation, bFollowAnimation, LeadingAnimPlayReqID)
	if not AnimPath or AnimPath == "" then
		self:ErrorFmt("NewViewControlAnimComponent:PlayAnimationByTag Get Empty AnimPath! Eid:%s", self.eid)
		return
	end

	local AnimPlayReqDefaultConfig = ViewAnimConst.EAnimPlayReqTagDefaultConfig[AnimPlayReqTag]
	if not AnimPlayReqDefaultConfig then
		self:ErrorFmt("NewViewControlAnimComponent AnimPlayReqTag Not Contain Tag's Config:%s Eid:%s", AnimPlayReqTag, self.eid)
		return nil
	end

	local AnimSlot = AnimPlayReqDefaultConfig[1]

	if bStopAllMontages then
		BlendInTime = BlendInTime or 0
		self:StopAllAnimation(0, BlendInTime)
	end

	local AnimPlayReqID = self:PlayCPPAnimationLogic(SimpleViewControlAnimComponent.MAIN_MESH_ID, AnimSlot, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop, OnMontageBlendingOutObj, OnMontageBlendingOutCb, nil, nil, bRefreshViewWhenSameMontage)
	if bLeadingAnimation or bFollowAnimation then
		self:SetAnimationSyncInfo(AnimPlayReqID, bLeadingAnimation, bFollowAnimation, LeadingAnimPlayReqID or 0)
	end

	return AnimPlayReqID
end


---动画播放接口(异步)
---@param AnimSlotName string Slot 动画播放Slot
---@param AnimPath string 动画路径
---@param bLoop boolean 是否循环播放
---@param BlendInTime float 渐入时间 不填走资源自己的时间 如果是seq则默认未0.2
---@param BlendOutTime float 渐出时间 不填走资源自己的时间 如果是seq则默认未0.2
---@param bAutoStop boolean 是否自动结束 默认 false
---@param EndCBObj Entity 动画结束回调对象
---@param EndCbFuncName string 动画结束回调函数名
---@param PlayRate float 播放速率 默认值:1
---@param InTimeToStartMontageAt float 播放时长位移 默认值:0
---@param bStopAllMontages boolean 播放时是否停止其他Montage播放 默认值:false
---@param bAdaptivePlayTimeStamp boolean 是否追赶异步加载消耗时间 默认值:true
---@return int AnimPlayReqID
function SimpleViewControlAnimComponent:PlayAnimationByMontageAsyncLoad(AnimSlotName, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop, EndCBObj, EndCbFuncName, PlayRate, InTimeToStartMontageAt, bStopAllMontages, bAdaptivePlayTimeStamp, bLeadingAnimation, bFollowAnimation, LeadingAnimPlayReqID)
	if not AnimPath or AnimPath == "" then
		self:Error("SimpleViewControlAnimComponent:PlayAnimationByMontageAsyncLoad Get Empty AnimPath!")
		return
	end

	if not AnimSlotName or AnimSlotName == "" then
		self:Error("SimpleViewControlAnimComponent:PlayAnimationByMontageAsyncLoad Get Empty AnimSlot!")
		return
	end

	if bStopAllMontages then
		BlendInTime = BlendInTime or 0
		self.CppEntity:KAPI_Animation_StopAllAnimation(0, BlendInTime)
	end
	local AnimPlayReqID = self:PlayCPPAnimationLogic(SimpleViewControlAnimComponent.MAIN_MESH_ID, AnimSlotName, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop, EndCBObj, EndCbFuncName, PlayRate, InTimeToStartMontageAt)

	if bAdaptivePlayTimeStamp == false then
		self:EnableAnimPlayReqAdaptivePlayTimeStamp(AnimPlayReqID, false)
	end
	if bLeadingAnimation or bFollowAnimation then
		self:SetAnimationSyncInfo(AnimPlayReqID, bLeadingAnimation, bFollowAnimation, LeadingAnimPlayReqID or 0)
	end
	
	return AnimPlayReqID
end


---动画结束接口
---@param AnimPlayReqID int 根据ID时结束对应动画
function SimpleViewControlAnimComponent:StopAnimation(AnimPlayReqID, InBlendOutTime)
	if not AnimPlayReqID then
		return
	end

	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("StopAnimation", AnimPlayReqID, InBlendOutTime)
		return
	end
	self:DebugFmt("SimpleViewControlAnimComponent:StopAnimation ID:%s eid:%s", AnimPlayReqID, self.eid)

	local AnimPlayReqConfig = self.AnimPlayReqMap[AnimPlayReqID]
	if AnimPlayReqConfig then
		local MeshID = AnimPlayReqConfig.MeshID
		local AnimSlotName = AnimPlayReqConfig.AnimSlotName
		if MeshID == SimpleViewControlAnimComponent.MAIN_MESH_ID then
			if self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimPlayReqConfig.AnimSlotName]] == AnimPlayReqID then
				self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimPlayReqConfig.AnimSlotName]] = nil
			end
		else
			local AnimPlayOnOtherMeshes = self.AnimPlayOnOtherMeshes
			if AnimPlayOnOtherMeshes and AnimPlayOnOtherMeshes[MeshID] then
				local AnimPlayOnOtherMesh = AnimPlayOnOtherMeshes[MeshID]
				if AnimPlayOnOtherMesh[ViewAnimConst.EAnimationSlotType[AnimSlotName]] == AnimPlayReqID then
					AnimPlayOnOtherMesh[ViewAnimConst.EAnimationSlotType[AnimSlotName]] = nil
				end
			end
		end

		local EndCBObj = AnimPlayReqConfig.EndCBObj
		local EndCbFuncName = AnimPlayReqConfig.EndCbFuncName

		self:releaseAnimPlayReqConfigTable(AnimPlayReqConfig)

		if EndCBObj and EndCbFuncName then
			EndCBObj[EndCbFuncName](EndCBObj, true)
		end
	end

	return self:StopCppAnimationLogic(AnimPlayReqID, InBlendOutTime)
end

function SimpleViewControlAnimComponent:StopAnimationBySlot(AnimSlotName, InBlendOutTime)
	if not AnimSlotName or AnimSlotName == "" then
		return
	end

	return self:StopCppAnimationLogicBySlot(AnimSlotName, InBlendOutTime)
end

function SimpleViewControlAnimComponent:StopAllAnimation(InBlendOutTime)
	InBlendOutTime = InBlendOutTime or 0

	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("StopAllAnimation", InBlendOutTime)
		return
	end

	return self.CppEntity:KAPI_Animation_StopAllAnimation(0, InBlendOutTime)
end

function SimpleViewControlAnimComponent:BreakLoopingAnimation(AnimPlayReqID)
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("BreakLoopingAnimation", AnimPlayReqID)
		return
	end
	self.CppEntity:KAPI_Animation_BreakLoopAnimation(AnimPlayReqID)
end

function SimpleViewControlAnimComponent:AnimationJumpToSection(AnimPlayReqID, Section)
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("AnimationJumpToSection", AnimPlayReqID, Section)
		return
	end
	self.CppEntity:KAPI_Animation_SetAnimationJumpToSection(AnimPlayReqID, Section)
end

function SimpleViewControlAnimComponent:StopAnimationRootMotion(AnimPlayReqID)
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("StopAnimationRootMotion", AnimPlayReqID)
		return
	end
	self.CppEntity:KAPI_Animation_StopAnimationRootMotion(AnimPlayReqID)
end

function SimpleViewControlAnimComponent:SetAnimPlayRate(AnimPlayReqID, PlayRate)
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("SetAnimPlayRate", AnimPlayReqID, PlayRate)
		return
	end
	self.CppEntity:KAPI_Animation_SetPlayRate(AnimPlayReqID, PlayRate)
end

function SimpleViewControlAnimComponent:SetAnimTimeAt(AnimPlayReqID, Time)
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("SetAnimTimeAt", AnimPlayReqID, Time)
		return
	end
	self.CppEntity:KAPI_Animation_SetPlayTime(AnimPlayReqID, Time)
end

function SimpleViewControlAnimComponent:ForceTickAnimation()
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("ForceTickAnimation")
		return
	end
	self.CppEntity:KAPI_Animation_ForceTickAnimation(0)
end

function SimpleViewControlAnimComponent:EnableAnimPlayReqAdaptivePlayTimeStamp(AnimPlayReqID, bEnable)
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("EnableAnimPlayReqAdaptivePlayTimeStamp", AnimPlayReqID, bEnable)
		return
	end
	self.CppEntity:KAPI_Animation_EnableAnimPlayReqAdaptivePlayTimeStamp(AnimPlayReqID, bEnable)
end

function SimpleViewControlAnimComponent:SetAnimationSyncInfo(AnimPlayReqID, bLeadingAnimation, bFollowAnimation, LeadingAnimPlayReqID)
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("SetAnimationSyncInfo", AnimPlayReqID, bLeadingAnimation, bFollowAnimation, LeadingAnimPlayReqID)
		return
	end
	self.CppEntity:KAPI_Animation_SetAnimationSyncInfo(AnimPlayReqID, bLeadingAnimation, bFollowAnimation, LeadingAnimPlayReqID or 0)
end

function SimpleViewControlAnimComponent:StopAnimationWithDelayPerformance(MeshIDToStop, AnimPlayReqID, bPassiveStop)
	bPassiveStop = bPassiveStop or true
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("StopAnimationWithDelayPerformance", MeshIDToStop, AnimPlayReqID, bPassiveStop)
		return
	end
	self.CppEntity:KAPI_Animation_StopAnimationWithDelayPerformance(MeshIDToStop, AnimPlayReqID, bPassiveStop)
end

-----------------------------------------------------------UTILS--------------------------------------------------------
function SimpleViewControlAnimComponent:PreLoadAndCacheMontageByPath(AnimPath, AnimSlotName, BlendInTime, BlendOutTime, LoopCount, Obj, CallBackFuncName)
	if not AnimPath or AnimPath == "" then
		return 0
	end

	if not AnimSlotName or AnimSlotName == "" then
		return 0
	end

	BlendInTime = BlendInTime
	BlendOutTime = BlendOutTime
	LoopCount = LoopCount or 1

	local CacheMontageID = Game.WorldManager.AnimationManager:GetMontageCacheByID((Game.me and self.eid == Game.me.eid) and 1 or 0, AnimPath, AnimSlotName)

	if not IsValidID(CacheMontageID) then
		local LoadID = self:DoAsyncLoadAsset(AnimPath, "onReceiveLoadedAssetForPreLoad", AnimPath, CallBackFuncName, Obj, AnimSlotName, BlendInTime, BlendOutTime, LoopCount)
		self.LoadIdMap[LoadID] = LoadID
		return LoadID
	else
		if Obj and Obj[CallBackFuncName] then
			Obj[CallBackFuncName](Obj, CacheMontageID)
		end
	end

	return 0
end

function SimpleViewControlAnimComponent:PreLoadAndCacheMontageByPathList(AnimPathList, AnimSlotName, BlendInTime, BlendOutTime, LoopCount, Obj, CallBackFuncName)
	if not AnimPathList or next(AnimPathList) == nil then
		return 0
	end

	if #AnimPathList == 1 then
		return self:PreLoadAndCacheMontageByPath(AnimPathList[1], AnimSlotName, BlendInTime, BlendOutTime, LoopCount, Obj, CallBackFuncName)
	end

	if not AnimSlotName or AnimSlotName == "" then
		return 0
	end

	LoopCount = LoopCount or 1

	-- 临时版本 CPP部分动画缓存需要完善查询接口
	local LoadID = self:DoAsyncLoadAssetList(AnimPathList, "onReceiveLoadedAssetListForPreLoad", AnimPathList, CallBackFuncName, Obj, AnimSlotName, BlendInTime, BlendOutTime, LoopCount)
	self.LoadIdMap[LoadID] = LoadID
	return LoadID
end

function SimpleViewControlAnimComponent:CancelPreLoadAndCacheMontage(InLoadId)
	if self.LoadIdMap[InLoadId] then
		self:releaseLoadHandle(InLoadId)
	end
end

----------------------------------------------------------内部接口------------------------------------------------------
function SimpleViewControlAnimComponent:generateAnimPlayReqConfig(AnimSlotName, MeshID, EndCBObj, EndCbFuncName)
	local AnimPlayReqID = ULLFunc.GetGlobalUniqueID()
	local EmptyConfig = AnimPlayReqTask.GetFromPool()

	-- 必须参数
	EmptyConfig.AnimPlayReqID = AnimPlayReqID
	EmptyConfig.AnimSlotName = AnimSlotName
	EmptyConfig.EndCBObj = EndCBObj
	EmptyConfig.EndCbFuncName = EndCbFuncName
	EmptyConfig.MeshID = MeshID


	return AnimPlayReqID, EmptyConfig
end


function SimpleViewControlAnimComponent:PlayCPPAnimationLogic(MeshIDToPlay, AnimSlotName, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop, EndCBObj, EndCbFuncName, PlayRate, InTimeToStartMontageAt, bRefreshViewWhenSameMontage)
	if self.bBeginPlay then
		self:Error("SimpleViewControlAnimComponent:ReEnter Here!!! Please Check!!!")
	end

	local AnimPlayReqID, AnimPlayReqConfig = self:generateAnimPlayReqConfig(AnimSlotName, MeshIDToPlay, EndCBObj, EndCbFuncName)

	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("PlayCPPAnimationLogic_AfterAnimReady", MeshIDToPlay, AnimPlayReqID, AnimPlayReqConfig, AnimSlotName, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop, EndCBObj, EndCbFuncName, PlayRate, InTimeToStartMontageAt, bRefreshViewWhenSameMontage)
		return AnimPlayReqID
	end

	self.AnimPlayReqMap[AnimPlayReqID] = AnimPlayReqConfig
	self:DebugFmt("SimpleViewControlAnimComponent:PlayCPPAnimationLogic ID:%s eid:%s AnimPath:%s bAutoStop:%s", AnimPlayReqID, self.eid, AnimPath, bAutoStop or false)

	if MeshIDToPlay == SimpleViewControlAnimComponent.MAIN_MESH_ID then
		local LastSlotAnimID = self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimSlotName]]
		if LastSlotAnimID then
			self:StopAnimationWithDelayPerformance(MeshIDToPlay, LastSlotAnimID)
		end
	else
		self.AnimPlayOnOtherMeshes = self.AnimPlayOnOtherMeshes or {}
		if self.AnimPlayOnOtherMeshes[MeshIDToPlay] == nil then
			self.AnimPlayOnOtherMeshes[MeshIDToPlay] = {}
		end
		local AnimPlayOnOtherMesh = self.AnimPlayOnOtherMeshes[MeshIDToPlay]
		local LastMeshAnimID = AnimPlayOnOtherMesh[ViewAnimConst.EAnimationSlotType[AnimSlotName]]
		if LastMeshAnimID then
			self:StopAnimationWithDelayPerformance(MeshIDToPlay, LastMeshAnimID)
		end
	end

	self.bBeginPlay = true
	self.CppEntity:KAPI_Animation_PlayAnimation(
			MeshIDToPlay,
			AnimPlayReqID,
			AnimPath,
			AnimSlotName,
			bLoop or false,
			bAutoStop or false,
			BlendInTime or -1,
			BlendOutTime or -1,
			PlayRate or 1,
			InTimeToStartMontageAt or 0,
			bRefreshViewWhenSameMontage or true
	)
	self.bBeginPlay = false

	self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimSlotName]] = AnimPlayReqID
	return AnimPlayReqID
end

function SimpleViewControlAnimComponent:PlayCPPAnimationLogic_AfterAnimReady(MeshIDToPlay, AnimPlayReqID, AnimPlayReqConfig, AnimSlotName, AnimPath, bLoop, BlendInTime, BlendOutTime, bAutoStop, EndCBObj, EndCbFuncName, PlayRate, InTimeToStartMontageAt, bRefreshViewWhenSameMontage)
	self:DebugFmt("SimpleViewControlAnimComponent:PlayCPPAnimationLogic_AfterAnimReady ID:%s eid:%s AnimPath:%s bAutoStop:%s", AnimPlayReqID, self.eid, AnimPath, bAutoStop or false)
	self.AnimPlayReqMap[AnimPlayReqID] = AnimPlayReqConfig
	
	if MeshIDToPlay == SimpleViewControlAnimComponent.MAIN_MESH_ID then
		local LastSlotAnimID = self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimSlotName]]
		if LastSlotAnimID then
			self:StopAnimationWithDelayPerformance(MeshIDToPlay, LastSlotAnimID)
		end
	else
		self.AnimPlayOnOtherMeshes = self.AnimPlayOnOtherMeshes or {}
		if self.AnimPlayOnOtherMeshes[MeshIDToPlay] == nil then
			self.AnimPlayOnOtherMeshes[MeshIDToPlay] = {}
		end
		local AnimPlayOnOtherMesh = self.AnimPlayOnOtherMeshes[MeshIDToPlay]
		local LastMeshAnimID = AnimPlayOnOtherMesh[ViewAnimConst.EAnimationSlotType[AnimSlotName]]
		if LastMeshAnimID then
			self:StopAnimationWithDelayPerformance(MeshIDToPlay, LastMeshAnimID)
		end
	end

	self.bBeginPlay = true
	self.CppEntity:KAPI_Animation_PlayAnimation(
			MeshIDToPlay,
			AnimPlayReqID,
			AnimPath,
			AnimSlotName,
			bLoop or false,
			bAutoStop or false,
			BlendInTime or -1,
			BlendOutTime or -1,
			PlayRate or 1,
			InTimeToStartMontageAt or 0,
			bRefreshViewWhenSameMontage or true
	)
	self.bBeginPlay = false

	self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimSlotName]] = AnimPlayReqID
end

function SimpleViewControlAnimComponent:StopCppAnimationLogic(AnimPlayReqID, BlendOutTime)
	BlendOutTime = BlendOutTime or -1
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("StopCppAnimationLogic", AnimPlayReqID, BlendOutTime)
		return
	end

	self.CppEntity:KAPI_Animation_StopAnimation(AnimPlayReqID, BlendOutTime)
end

function SimpleViewControlAnimComponent:StopCppAnimationLogicBySlot(AnimSlotName, BlendOutTime)
	BlendOutTime = BlendOutTime or -1
	if not self.bIsAnimComRdy then
		self:cacheAnimPlayReq("StopCppAnimationLogicBySlot", AnimSlotName, BlendOutTime)
		return
	end

	self.CppEntity:KAPI_Animation_StopAnimationBySlot(0, AnimSlotName, BlendOutTime)
end

function SimpleViewControlAnimComponent:KCB_OnMontageStop(AnimPlayReqID, bInterrupted, StopReason)
	self:DebugFmt("SimpleViewControlAnimComponent:KCB_OnMontageStop ID:%s bInterrupted:%s StopReason%s eid:%s", AnimPlayReqID, bInterrupted, StopReason, self.eid)
	local AnimPlayReqConfig = self.AnimPlayReqMap[AnimPlayReqID]

	if AnimPlayReqConfig then
		local MeshID = AnimPlayReqConfig.MeshID
		local AnimSlotName = AnimPlayReqConfig.AnimSlotName
		if MeshID == SimpleViewControlAnimComponent.MAIN_MESH_ID then
			local LastSlotAnimID =  self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimSlotName]]
			if LastSlotAnimID == AnimPlayReqID then
				self.AnimPlayReqIDOnSlot[ViewAnimConst.EAnimationSlotType[AnimSlotName]] = nil
			end
		else
			local AnimPlayOnOtherMeshes = self.AnimPlayOnOtherMeshes
			if AnimPlayOnOtherMeshes and AnimPlayOnOtherMeshes[MeshID] then
				local AnimPlayOnOtherMesh = AnimPlayOnOtherMeshes[MeshID]
				local LastMeshAnimID = AnimPlayOnOtherMesh[ViewAnimConst.EAnimationSlotType[AnimSlotName]]
				if LastMeshAnimID == AnimPlayReqID then
					AnimPlayOnOtherMesh[ViewAnimConst.EAnimationSlotType[AnimSlotName]] = nil
				end
			end
		end

		local EndCBObj = AnimPlayReqConfig.EndCBObj
		local EndCbFuncName = AnimPlayReqConfig.EndCbFuncName

		self:releaseAnimPlayReqConfigTable(AnimPlayReqConfig)

		if EndCBObj and EndCbFuncName then
			EndCBObj[EndCbFuncName](EndCBObj, bInterrupted)
		end
	else
		self:ErrorFmt("SimpleViewControlAnimComponent:KCB_OnMontageStop Not Find AnimPlayReqConfig ID:%s eid:%s", AnimPlayReqID, self.eid)
	end
end


---动画播放缓存(未EnterWorld)
function SimpleViewControlAnimComponent:cacheAnimPlayReq(CallBackFuncNameAfterAnimReady, ...)
	local CacheMap = self.CacheAnimPlayReqMap

	local FuncTable = AnimPlayReqCacheTask.GetFromPool()
	FuncTable.CallbackFuncName = CallBackFuncNameAfterAnimReady
	FuncTable.ParamNum = select("#", ...)
	local Params = FuncTable.Params
	for i = 1, FuncTable.ParamNum do
		Params[i] = select(i, ...)
	end

	CacheMap[#CacheMap + 1] = FuncTable
	return
end

function SimpleViewControlAnimComponent:consumeAnimPlayReq()
	local CacheMap = self.CacheAnimPlayReqMap
	for Index, CacheTask in ipairs(CacheMap) do
		if self[CacheTask.CallbackFuncName] then
			self[CacheTask.CallbackFuncName](self, table.unpack(CacheTask.Params, 1, CacheTask.ParamNum))
		end
		CacheMap[Index] = nil
		AnimPlayReqCacheTask.ReturnToPool(CacheTask)
	end
end

function SimpleViewControlAnimComponent:onReceiveLoadedAssetForPreLoad(InLoadID, AnimAssetID, AnimPath, CallBackFuncName, Obj, AnimSlotName, BlendInTime, BlendOutTime, LoopCount)
	local bCancel = self.LoadIdMap[InLoadID] == nil
	if bCancel then
		return
	end

	if AnimAssetID == 0 then
		return
	end
	local CachePoolType = (Game.me and self.eid == Game.me.eid) and 1 or 0
	local Montage = Game.WorldManager.AnimationManager:CreateAndAddMontageCacheByID(CachePoolType, AnimPath, AnimAssetID, AnimSlotName, BlendInTime or ViewAnimConst.ACTION_ANIMATION_BLEND_IN_TIME, BlendOutTime or ViewAnimConst.ACTION_ANIMATION_BLEND_OUT_TIME, LoopCount, true)

	self:releaseLoadHandle(InLoadID)

	if not Montage then
		self:WarningFmt("SimpleViewControlAnimComponent:loadAnimAssetSync SimpleViewControlAnimComponent:loadAnimAssetSync fail, AnimPath: %s", AnimPath)
		return
	end

	if CallBackFuncName and Obj[CallBackFuncName] then
		Obj[CallBackFuncName](Obj, Montage)
	end
end

function SimpleViewControlAnimComponent:onReceiveLoadedAssetListForPreLoad(InLoadID, AnimAssetIDs, AnimPathList, CallBackFuncName, Obj, AnimSlotName, BlendInTime, BlendOutTime, LoopCount)
	local bCancel = self.LoadIdMap[InLoadID] == nil
	if bCancel then
		return
	end

	local AssetNum = AnimAssetIDs:Num()
	local Index = 0
	local Result = {}
	while Index < AssetNum do
		local AnimAssetID = AnimAssetIDs:Get(Index)
		Index = Index + 1
		if AnimAssetID == 0 then
			self:DebugErrorFmt("SimpleViewControlAnimComponent:loadAnimAssetSync SimpleViewControlAnimComponent:loadAnimAssetSync fail, AnimPath: %s", AnimPathList[Index])
			return
		end
		local CachePoolType = (Game.me and self.eid == Game.me.eid) and 1 or 0
		local Montage = Game.WorldManager.AnimationManager:CreateAndAddMontageCacheByID(CachePoolType, AnimPathList[Index], AnimAssetID, AnimSlotName, BlendInTime or ViewAnimConst.ACTION_ANIMATION_BLEND_IN_TIME, BlendOutTime or ViewAnimConst.ACTION_ANIMATION_BLEND_OUT_TIME, LoopCount, true)
		Result[#Result+1] = Montage
	end

	self:releaseLoadHandle(InLoadID)

	if CallBackFuncName and Obj[CallBackFuncName] then
		Obj[CallBackFuncName](Obj, Result)
	end
end

function SimpleViewControlAnimComponent:releaseAnimPlayReqConfigTable(TableToRelease)
	if not TableToRelease then
		return
	end

	self.AnimPlayReqMap[TableToRelease.AnimPlayReqID] = nil

	AnimPlayReqTask.ReturnToPool(TableToRelease)
end

function SimpleViewControlAnimComponent:releaseLoadHandle(LoadId)
	self.LoadIdMap[LoadId] = nil
end

-----------------------------Notify-----------------------------
function SimpleViewControlAnimComponent:KCB_OnAnimNotify_C7ChangeHair(bTakeOff)
	if self.OnChangeHair then
		self:OnChangeHair(bTakeOff)
	end
end

function SimpleViewControlAnimComponent:KCB_OnAnimNotify_C7ChangeAnimPosture(AnimPostureName, TargetValue)
	if self.OnChangeAnimPostureNotify then
		self:OnChangeAnimPostureNotify(AnimPostureName, TargetValue)
	end
end

function SimpleViewControlAnimComponent:KCB_OnAnimNotify_C7AttachItem(AttachItemName, AttachNotifyType)
	if self.OnAnimFeatureNotify then
		self:OnAnimFeatureNotify(AttachItemName, AttachNotifyType)
	end
end

-- 供查询动画库动画数据
function SimpleViewControlAnimComponent:GetAnimFeatureData(AnimFeatureName)
	local AnimDataID = self:GetAnimDataID()
	if not AnimDataID then
		return nil
	end

	local Result = AnimLibHelper.GetAnimFeatureData(AnimDataID, AnimFeatureName)
	if not Result then
		self:WarningFmt("AnimLib: Can not Get Correct AnimFeatureData, Entity:%s, ConfigID:%s, ModelID:%s", self.eid, self.TemplateID or self.ConfigID, self.GetConfigModelID and self:GetConfigModelID() or nil)
		return nil
	end

	return Result
end

-- 供查询动画库单一动画路径
function SimpleViewControlAnimComponent:GetAnimPathAndLenFromAnimFeatureForSingleAnimation(AnimFeatureName)
	local Result = self:GetAnimFeatureData(AnimFeatureName)
	if Result and Result.Anim then
		return AnimLibHelper.GetAnimAssetPathFromAnimID(Result.Anim), Result.AnimLen
	end

	return nil, nil
end

function SimpleViewControlAnimComponent:GetAnimDataID()
	local AnimDataID = self:GetCurrentWorkingAnimAssetID()

	if not AnimDataID or AnimDataID == 0 or AnimDataID == "" then
		self:WarningFmt("AnimLib: Can not Get Correct AnimAssetID, Entity:%s, ConfigID:%s, ModelID:%s", self.eid, self.TemplateID or self.ConfigID, self.GetConfigModelID and self:GetConfigModelID() or nil)
		return nil
	end

	return AnimDataID
end

function SimpleViewControlAnimComponent:KCB_C7PostProcess(PostID, AlphaInTime, AlphaOutTime, Duration)
	-- 暂时只有冲刺屏幕后效在使用,默认只有主角才生效,后续不确定是否p3玩家使用这个参数也要生效
	if self == Game.me then
		Game.PostProcessManager:PlayPostProcessByID(Enum.EPostProcessLayers.Skill, 0, PostID, AlphaInTime, AlphaOutTime, Duration)
	end
end

---@class AnimPlayReqTask
AnimPlayReqTask = DefineClass("AnimPlayReqTask")

-- 异步加载Task缓存池大小
AnimPlayReqTask.__PoolSize = 128
-- 对象池数量预警
AnimPlayReqTask.__PoolSizeWarningThreshold = 512

function AnimPlayReqTask:ctor()
	self:on_recycle_to_pool()
end

function AnimPlayReqTask:on_recycle_to_pool()
	-- 必须参数
	self.AnimPlayReqID = nil

	self.AnimSlotName = nil
	self.EndCBObj = nil
	self.EndCbFuncName = nil
end

function AnimPlayReqTask.GetFromPool()
	return Game.ObjectPoolManager:AllocateObject(AnimPlayReqTask)
end

function AnimPlayReqTask.ReturnToPool(Item)
	Game.ObjectPoolManager:ReleaseObject(Item)
end

---@class AnimPlayReqCacheTask
AnimPlayReqCacheTask = DefineClass("AnimPlayReqCacheTask")

-- 异步加载Task缓存池大小
AnimPlayReqCacheTask.__PoolSize = 64
-- 对象池数量预警
AnimPlayReqCacheTask.__PoolSizeWarningThreshold = 256

function AnimPlayReqCacheTask:ctor()
	self.CallbackFuncName = nil
	self.ParamNum = nil
	self.Params = {}
end

function AnimPlayReqCacheTask:on_recycle_to_pool()
	self.CallbackFuncName = nil
	self.ParamNum = nil
	table.clear(self.Params)
end

function AnimPlayReqCacheTask.GetFromPool()
	return Game.ObjectPoolManager:AllocateObject(AnimPlayReqCacheTask)
end

function AnimPlayReqCacheTask.ReturnToPool(Item)
	Game.ObjectPoolManager:ReleaseObject(Item)
end

return SimpleViewControlAnimComponent