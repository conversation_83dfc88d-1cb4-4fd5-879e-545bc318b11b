local AttachJointComponent_V2 = import("AttachJointComponent_V2")
local EAttachmentRule = import("EAttachmentRule")
local EDetachmentRule = import("EDetachmentRule")
local ERotationUpdateMode = import("ERotationUpdateMode")
local ELocationUpdateMode = import("ELocationUpdateMode")
local ULLFunc = import("LowLevelFunctions")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local EPropertyClass = import("EPropertyClass")
local EObjectTypeQuery = import("EObjectTypeQuery")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local SkeletalMeshComponent = import("SkeletalMeshComponent")
local SceneComponent = import("SceneComponent")
local VIEW_CREATE_LIMIT_TAG = WorldViewBudgetConst.VIEW_CREATE_LIMIT_TAG
local CUSTOM_DEPTH_LOGIC_TYPE = ViewControlConst.CUSTOM_DEPTH_LOGIC_TYPE
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

---------------------------------
--- ViewControl 挂接接口
--------------------------------
ViewControlAttachComponent = DefineComponent("ViewControlAttachComponent")
------------------------------------------------------------------------------------------------------------------------
---@class AttachInfoParams
local AttachInfoParams = DefineClass("AttachInfoParams")
AttachInfoParams.__PoolSize = 100

function AttachInfoParams.GetFromPool()
	return Game.ObjectPoolManager:AllocateObject(AttachInfoParams)
end

function AttachInfoParams.ReturnToPool(Item)
	Game.ObjectPoolManager:ReleaseObject(Item)
end

function AttachInfoParams:on_alloc_from_pool()
end


function AttachInfoParams:on_recycle_to_pool()
	self:Reset()
end

function AttachInfoParams:ctor()
    self:Reset()
end


function AttachInfoParams:ResetVector(ParamName)
    if self[ParamName] then
        self[ParamName].X = 0
        self[ParamName].Y = 0
        self[ParamName].Z = 0
    else
        self[ParamName] = FVector(0,0,0)
    end
end

function AttachInfoParams:ResetRotator(ParamName)
    if self[ParamName] then
        self[ParamName].Yaw = 0
        self[ParamName].Pitch = 0
        self[ParamName].Roll = 0
    else
        self[ParamName] = FRotator(0,0,0)
    end
end

function AttachInfoParams:Reset()
	---挂接基本信息 ---------------------------------------
    self.AttachOwnerEID = nil
    self.AttachReason = Enum.EAttachReason.None
    self.SocketName = nil
    self.CompTag = nil
    self:ResetVector("AttachLoc")
    self:ResetRotator("AttachRot")
	
    self.bPlayEffect = false
    self.bSyncOwnerVisibility = true
    self.bSyncOwnerMaterial= false
	-- 显隐性传递给 parent, 例如用于骑乘
	self.bSyncVisibilityToParent = false
	-- 材质变化传递给 Parent
	self.bSyncMaterialChangeToParent = false

    ---VirtualSocket ---------------------------------------
    ---Virtual挂点基本信息
    self.bEnableAttachJoint = false
    self.bUpdateAnimInsMovement = true
    self:ResetVector("SocketLocation")
    self:ResetRotator("SocketRotation")
    self.bDoCollisionTest = false
    self.ProbeSize = 30;
    self.CollisionChannels = CollisionConst.QUERY_BY_OBJECTTYPES.COMMON_WORLD_STATIC

    ---开启位置稳定
    self.bEnableStabilize = false
    self.StabilizeTolerance = 30
    self.StabilizeInterpSpeed = 2
    self.StabilizeInterpOutSpeed = 3
    self.StabilizePointBoneName = ""
    self:ResetVector("StabilizePointOffset")

    ---位置更新方式
    self.LocationUpdateMode = ELocationUpdateMode.None
    self.FloatTime = 2
    self.FloatMaxHeight = 50
    self.FloatMinHeight = -50
    self.FloatEaseExp = 3
    self.AttachLagSpeed = 1
    self.AttachLagMaxDistance = 1000
    self.BezierStep = 0.1
    self.BezierStepMaxDist = 100.
    self.BezierSpeedAlpha = 10;
    self.BezierLagSpeed = 1
    self.BezierDirectionMultiplier = 0.2
    self.InCircleLagSpeed = 2
    self.InCircleLagTolerance = 0

    ---旋转更新方式
    self.bEnableAttachRotationLag = false
	self.bAbsoluteRotate = false
    self.AttachRotationLagSpeed = 5
    self.bClampPitch = true
    self.PitchClampAngleMin = 0
    self.PitchClampAngleMax = 0
    self.RotationUpdateMode =  ERotationUpdateMode.UseDesiredRot
    self.FaceToMoveDirectionTolerance = 0.5
    self.RotateRoundTime = 10

    ---RunningData ------------------------------------------
    self.AttachVirtualSocketID = nil
    self.AttachID = nil

end

------------------------------------------------------------------------------------------------------------------------
---@class AttachInfoParamsBuilder
local AttachInfoParamsBuilder = DefineClass("AttachInfoParamsBuilder")

AttachInfoParamsBuilder.__PoolSize = 5


function AttachInfoParamsBuilder.GetFromPool()
	return Game.ObjectPoolManager:AllocateObject(AttachInfoParamsBuilder)
end

function AttachInfoParamsBuilder.ReturnToPool(Item)
	Game.ObjectPoolManager:ReleaseObject(Item)
end

function AttachInfoParamsBuilder:on_recycle_to_pool()
	self:Reset()
end

function AttachInfoParamsBuilder.NewAttachParam(Owner,Reason,SocketName,CompTag)
    local NewBuilder = AttachInfoParamsBuilder.GetFromPool()
    NewBuilder.AttachInfo = nil
    return NewBuilder:InitAttach(Owner,Reason,SocketName,CompTag)
end

function AttachInfoParamsBuilder.ModifyAttachParam(AttachParam)
    local NewBuilder = AttachInfoParamsBuilder.GetFromPool()
    NewBuilder.AttachInfo = AttachParam
    return NewBuilder
end

function AttachInfoParamsBuilder:Reset()
    self.AttachInfo = nil
end


---初始化挂接
---@param Owner string 挂接对象ID
---@param Reason number Enum.EAttachReason枚举 按需添加
---@param SocketName string 挂接component的Socket
---@param CompTag string 选填，挂接到有对应标签的component，默认搜索对象身上MeshComponent
function AttachInfoParamsBuilder:InitAttach(Owner,Reason,SocketName,CompTag)
    self.AttachInfo = AttachInfoParams.GetFromPool()
    self.AttachInfo.AttachOwnerEID = Owner
    self.AttachInfo.AttachReason = Reason
    self.AttachInfo.SocketName = SocketName
    self.AttachInfo.CompTag = CompTag
    self.AttachInfo.bSkipChangeAttachMode = false
	self.AttachInfo.LocAttachmentRule = EAttachmentRule.SnapToTarget
	self.AttachInfo.RotAttachmentRule = EAttachmentRule.SnapToTarget
	self.AttachInfo.ScaleAttachmentRule = EAttachmentRule.SnapToTarget
	self.AttachInfo.TargetCompID = nil
    return self
end

---修改挂接位置
---@param AttachLoc FVector
---@param AttachRot FRotator
function AttachInfoParamsBuilder:ModifyAttachLocAndRot(AttachLoc,AttachRot)
    self.AttachInfo.AttachLoc = AttachLoc
    self.AttachInfo.AttachRot = AttachRot

    return self
end

---修改挂接位置平铺
---@param AttachLocX number
---@param AttachLocY number
---@param AttachLocZ number
---@param AttachRotPitch number
---@param AttachRotYaw number
---@param AttachRotRoll number
function AttachInfoParamsBuilder:ModifyAttachLocAndRot_Ex(AttachLocX,AttachLocY,AttachLocZ,AttachRotPitch,AttachRotYaw,AttachRotRoll)
    self.AttachInfo.AttachLoc.X = AttachLocX
    self.AttachInfo.AttachLoc.Y = AttachLocY
    self.AttachInfo.AttachLoc.Z = AttachLocZ
    self.AttachInfo.AttachRot.Pitch = AttachRotPitch
    self.AttachInfo.AttachRot.Yaw = AttachRotYaw
    self.AttachInfo.AttachRot.Roll = AttachRotRoll

    return self
end

---是否开启ModelLib配置的入场出场特效,默认不开启
---@param bEnable boolean
function AttachInfoParamsBuilder:EnablePlayEffect(bEnable)
    self.AttachInfo.bPlayEffect = bEnable
    return self
end



---是否开启主人材质效果同步到挂接物品，默认不开启
------@param bEnable boolean
function AttachInfoParamsBuilder:EnableSyncOwnerMaterial(bEnable)
    self.AttachInfo.bSyncOwnerMaterial = bEnable
    return self
end

---开启VirtualSocket挂接模式 简化接口
---@param bEnable boolean
---@param bUpdateAnimInsMovement boolean 是否需要更新移动速度信息到动画蓝图
function AttachInfoParamsBuilder:EnableVirtualSocketSimple(bEnable,bUpdateAnimInsMovement)
    self.AttachInfo.bEnableAttachJoint = bEnable
    self.AttachInfo.bUpdateAnimInsMovement = bUpdateAnimInsMovement

    return self
end

---开启VirtualSocket挂接模式
---@param bEnable boolean
---@param bUpdateAnimInsMovement boolean 是否需要更新移动速度信息到动画蓝图
---@param SocketLocationX number VirtualSocket相对挂接点的相对偏移X
---@param SocketLocationY number VirtualSocket相对挂接点的相对偏移Y
---@param SocketLocationZ number VirtualSocket相对挂接点的相对偏移Z
---@param SocketRotationPitch number VirtualSocket相对挂接点的相对旋转Pitch
---@param SocketRotationYaw number VirtualSocket相对挂接点的相对旋转Yaw
---@param SocketRotationRoll number VirtualSocket相对挂接点的相对旋转Roll
---@param bDoCollisionTest boolean 是否开启碰撞检查
---@param CollisionProbeSize number 碰撞检查点碰撞大小
function AttachInfoParamsBuilder:EnableVirtualSocket(bEnable,bUpdateAnimInsMovement,SocketLocationX,SocketLocationY,
                                                     SocketLocationZ,SocketRotationPitch,SocketRotationYaw,SocketRotationRoll,
                                                     bDoCollisionTest,CollisionProbeSize,CollisionChannels)
    self.AttachInfo.bEnableAttachJoint = bEnable
    self.AttachInfo.bUpdateAnimInsMovement = bUpdateAnimInsMovement
    self.AttachInfo.SocketLocation.X = SocketLocationX
    self.AttachInfo.SocketLocation.Y = SocketLocationY
    self.AttachInfo.SocketLocation.Z = SocketLocationZ

    self.AttachInfo.SocketRotation.Pitch = SocketRotationPitch
    self.AttachInfo.SocketRotation.Yaw = SocketRotationYaw
    self.AttachInfo.SocketRotation.Roll = SocketRotationRoll

    self.AttachInfo.bDoCollisionTest = bDoCollisionTest
    self.AttachInfo.ProbeSize = CollisionProbeSize
    self.AttachInfo.CollisionChannels = CollisionChannels or CollisionConst.QUERY_BY_OBJECTTYPES.COMMON_WORLD_STATIC
    return self
end


function AttachInfoParamsBuilder:ModifyVirtualSocketLocation_EX(SocketLocationX,SocketLocationY, SocketLocationZ)
    self.AttachInfo.SocketLocation.X = SocketLocationX
    self.AttachInfo.SocketLocation.Y = SocketLocationY
    self.AttachInfo.SocketLocation.Z = SocketLocationZ
end

---开启VirtualSocket挂接点稳定功能, 定义一个以圆心在StabilizePointOffset相对MeshRoot点的位置，半径为StabilizeTolerance的球状范围
---如果StabilizePointBoneName目标骨骼点在范围内，则挂接点会插值到在圆心位置，插值速度为StabilizeInterpSpeed
---如果骨骼点超出圆心范围，则挂点会插值到骨骼位置，插值速度为StabilizeInterpOutSpeed
---@param bEnableStabilize boolean
---@param StabilizeTolerance number 稳定范围
---@param StabilizeInterpSpeed number 进入稳定范围后插值速度
---@param StabilizeInterpOutSpeed number 离开稳定范围后插值速度
---@param StabilizePointBoneName string 稳定目标骨骼
---@param StabilizePointOffsetX number 稳定点偏移X
---@param StabilizePointOffsetY number 稳定点偏移Y
---@param StabilizePointOffsetZ number 稳定点偏移Z
function AttachInfoParamsBuilder:EnableVirtualSocketStabilization(bEnableStabilize,StabilizeTolerance,StabilizeInterpSpeed,
                                                                  StabilizeInterpOutSpeed,StabilizePointBoneName,
                                                                  StabilizePointOffsetX,StabilizePointOffsetY,StabilizePointOffsetZ)
    self.AttachInfo.bEnableStabilize = bEnableStabilize
    self.AttachInfo.StabilizeTolerance = StabilizeTolerance
    self.AttachInfo.StabilizeInterpSpeed = StabilizeInterpSpeed
    self.AttachInfo.StabilizeInterpOutSpeed = StabilizeInterpOutSpeed
    self.AttachInfo.StabilizePointBoneName = StabilizePointBoneName
    self.AttachInfo.StabilizePointOffset.X = StabilizePointOffsetX
    self.AttachInfo.StabilizePointOffset.Y = StabilizePointOffsetY
    self.AttachInfo.StabilizePointOffset.Z = StabilizePointOffsetZ
    return self
end

---切换VirtualSocket移动模式【0】:关闭
function AttachInfoParamsBuilder:SetVirtualSocketLocationUpdateModeToNone()
    self.AttachInfo.LocationUpdateMode = ELocationUpdateMode.None
    return self
end

---切换VirtualSocket移动模式【1】:漂浮模式，在FloatMaxHeight，FloatMinHeight 范围内上下移动，插值模式固定为EaseInOut,可指定Exp，和插值时间FloatTime
---@param FloatTime number 插值时间
---@param FloatMaxHeight number 漂浮最高相对高度
---@param FloatMinHeight number 插值最低相对高度
---@param FloatEaseExp number 插值Exp
function AttachInfoParamsBuilder:SetVirtualSocketToFloatMode(FloatTime,FloatMaxHeight,FloatMinHeight,FloatEaseExp)
    self.AttachInfo.LocationUpdateMode = ELocationUpdateMode.FloatUpDown
    self.AttachInfo.FloatTime = FloatTime
    self.AttachInfo.FloatMaxHeight = FloatMaxHeight
    self.AttachInfo.FloatMinHeight =FloatMinHeight
    self.AttachInfo.FloatEaseExp = FloatEaseExp
    return self
end

---切换VirtualSocket移动模式【2】:VInterpTo插值,可限制最大距离防止拖拽距离过长
---@param AttachLagSpeed number 插值速度
---@param AttachLagMaxDistance number 最大拖拽长度
function AttachInfoParamsBuilder:SetVirtualSocketToLocationInterpMode(AttachLagSpeed,AttachLagMaxDistance)
    self.AttachInfo.LocationUpdateMode = ELocationUpdateMode.Interp
    self.AttachInfo.AttachLagSpeed = AttachLagSpeed
    self.AttachInfo.AttachLagMaxDistance = AttachLagMaxDistance
    return self
end


---切换VirtualSocket移动模式【3】:使用贝塞尔插值,使用一阶贝塞尔，P1点为自身位置，P2点通过速度和方向计算，P3点为目标位置，Z轴不参与贝塞尔计算，直接通过FInterpTo 以 BezierLagSpeed 为速度计算
---【小龙移动功能定制】 bWithInCircle可开启是否在计算贝塞尔之前 开启圆形范围限制，以目标挂点（AttachLoc）为中心为圆心，挂目标点（AttachLoc） 到 Virtual挂点（SocketLoc）摇臂长度为半径，在圆形范围内以VInterpTo 插值
---@param BezierStep number 插值单步距离 （0 ~ 1） 1 为 瞬间插值到目标点
---@param BezierStepMaxDist number 计算P2点位时，限制最大距离
---@param BezierSpeedAlpha number 计算P2点位时，速度影响P2离开P1距离的系数
---@param BezierLagSpeed number Z轴插值速度
---@param BezierDirectionMultiplier number 计算P2点位时，P2离开P1距离的系数
---@param bWithInCircle number 是否开启圆形范围限制
---@param InCircleLagSpeed number 在圆形范围内移动插值速度
---@param InCircleLagTolerance number 圆形范围限制容忍范围（距离）
function AttachInfoParamsBuilder:SetVirtualSocketToBezierMovementMode(BezierStep,BezierStepMaxDist,BezierSpeedAlpha,
                                                                      BezierLagSpeed,BezierDirectionMultiplier,bWithInCircle,
                                                                      InCircleLagSpeed,InCircleLagTolerance)
    self.AttachInfo.LocationUpdateMode =  bWithInCircle and ELocationUpdateMode.Bezier_WithInCircleLag or ELocationUpdateMode.Bezier

    self.AttachInfo.BezierStep = BezierStep
    self.AttachInfo.BezierStepMaxDist = BezierStepMaxDist
    self.AttachInfo.BezierSpeedAlpha = BezierSpeedAlpha
    self.AttachInfo.BezierLagSpeed = BezierLagSpeed
    self.AttachInfo.BezierDirectionMultiplier = BezierDirectionMultiplier

    if bWithInCircle then
        self.AttachInfo.InCircleLagSpeed = InCircleLagSpeed
        self.AttachInfo.InCircleLagTolerance = InCircleLagTolerance
    end
    return self
end


---切换VirtualSocket旋转模式【1】:RInterpTo插值，可限制俯仰角
---@param AttachRotationLagSpeed number RInterpTo插值速度
---@param bClampPitch number 是否开启俯仰角限制
---@param PitchClampAngleMin number 仰角最大角度 Deg
---@param PitchClampAngleMax number 俯角最大角度 Deg
function AttachInfoParamsBuilder:SetRotationUpdateToInterLag(AttachRotationLagSpeed,bClampPitch,PitchClampAngleMin,PitchClampAngleMax)
    self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.UseDesiredRot
    self.AttachInfo.bEnableAttachRotationLag = AttachRotationLagSpeed
	self.AttachInfo.bAbsoluteRotate = false
    self.AttachInfo.AttachRotationLagSpeed = AttachRotationLagSpeed
    self.AttachInfo.bClampPitch = bClampPitch
    self.AttachInfo.PitchClampAngleMin = PitchClampAngleMin or 0
    self.AttachInfo.PitchClampAngleMax = PitchClampAngleMax or 0

    return self
end

---切换VirtualSocket旋转模式【2】:匀速自旋转
---@param RotateRoundTime number 圈速
---@param bAbsoluteRotate boolean true:绝对旋转，false:相对旋转
---@return AttachInfoParamsBuilder
function AttachInfoParamsBuilder:SetRotationUpdateToAutoRotate(RotateRoundTime, bAbsoluteRotate)
    self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.AutoRotate
    self.AttachInfo.bEnableAttachRotationLag = true
    self.AttachInfo.RotateRoundTime = RotateRoundTime
	self.AttachInfo.bAbsoluteRotate = bAbsoluteRotate or false
    return self
end

---切换VirtualSocket旋转模式【3】:和主人旋转方向保持一致
function AttachInfoParamsBuilder:SetRotationUpdateToUseOwnerRot()
    self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.UseOwnerRot
    self.AttachInfo.bEnableAttachRotationLag = true
	self.AttachInfo.bAbsoluteRotate = false
    return self
end

---切换VirtualSocket旋转模式【4】:和移动方向保持一致并且RInterTo插值，可限制俯仰角
---@param AttachRotationLagSpeed number RInterpTo插值速度
---@param FaceToMoveDirectionTolerance number 面朝速度方向开启阈值，速度达到以后才会面朝速度方向
---@param bClampPitch number 是否开启俯仰角限制
---@param PitchClampAngleMin number 仰角最大角度 Deg
---@param PitchClampAngleMax number 俯角最大角度 Deg
function AttachInfoParamsBuilder:SetRotationUpdateToFaceToModeDir(AttachRotationLagSpeed,FaceToMoveDirectionTolerance,bClampPitch,PitchClampAngleMin,PitchClampAngleMax)
    self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.FaceToMoveDir
    self.AttachInfo.bEnableAttachRotationLag = true
	self.AttachInfo.bAbsoluteRotate = false
    self.AttachInfo.AttachRotationLagSpeed =AttachRotationLagSpeed
    self.AttachInfo.bClampPitch = bClampPitch
    self.AttachInfo.PitchClampAngleMin = PitchClampAngleMin or 0
    self.AttachInfo.PitchClampAngleMax = PitchClampAngleMax or 0
    self.AttachInfo.FaceToMoveDirectionTolerance = FaceToMoveDirectionTolerance
    return self
end


function AttachInfoParamsBuilder:GenerateInfoAndDestroyBuilder()
    local AttachInfo = self.AttachInfo
    AttachInfoParamsBuilder.ReturnToPool(self)
    return AttachInfo
end


------------------------------------------------------------------------------------------------------------------------
ViewControlAttachComponent.AttachCleanUpInterval = 10

function ViewControlAttachComponent:ctor()
    ---自身挂接物品LocalAtttachItem信息信息 ----------
    self.AttachItems_V2 = {}

    self.MarkForRemoveAttaches = {}

    self.DelayDestoryTimer = nil

    self.HiddenAttachesWithReasons = {}

    --self.CurrentAttachMaterials = {}

    self.AttachEffectIDs = {}

    ---被挂接Entity信息 -----------------
    self.AttachedEntities = nil
	
    self.AttachJointComp = nil

    ---挂接Entity信息 -------------------
    self.AttachInfo = nil

    self.LeadingAnim = nil

    self.ManagerEntity = self.eid

    self.OwnerEventRegistered = false
	
	self.IsVisibleSynchronizing= false

end

function ViewControlAttachComponent:dtor()
    if  self.AnimChecktimer then
        Game.TimerManager:StopTimerAndKill( self.AnimChecktimer )
    end

	if self.DelayDestoryTimer then
		Game.TimerManager:StopTimerAndKill( self.DelayDestoryTimer )
		self.DelayDestoryTimer = nil
	end
end

function ViewControlAttachComponent:__component_EnterWorld__()
	self.bAttachExitWorld = false
end

function ViewControlAttachComponent:__component_AfterEnterWorld__()
    Game.EventSystem:PublishForUniqueID(_G.EEventTypes.ATTACH_ON_OWNER_STATE_CHANGED, self.eid, true)

    self:ApplyAttach()
    self:AttachGenerateSpawnEffect()

	-- 挂接物入场后根据之前 挂接物类型隐藏的缓存来刷新隐藏,暂时只有武器遇到此类情况,不开放给所有挂接物,还有一个原因：并不是所有挂接物都被父类按照分类缓存了
	if self.AttachInfo and self.AttachInfo.AttachReason == Enum.EAttachReason.Weapon then
		self:UpdateWeaponItemVisibleWithCache() 
	end
end

function ViewControlAttachComponent:__component_ExitWorld__()
    self:RemoveAllAttach()
    self:DetachFromOwner()

    self.AttachJoint = nil
    self.AttachJointComp = nil
	self.bAttachExitWorld = true
end


function ViewControlAttachComponent:__component_AppendGamePlayDebugInfo__(debugInfos)
    table.insert(debugInfos, "<Title_Red>[ViewControlAttachComponent]</>")
    self:DebugDumpTable(debugInfos, "AttachItems_V2", self.AttachItems_V2, 0, 1)
    self:DebugDumpTable(debugInfos, "MarkForRemoveAttaches", self.MarkForRemoveAttaches, 0, 1)
    --self:DebugDumpTable(debugInfos, "AttachComps", self.AttachComps, 0, 10)
    self:DebugDumpTable(debugInfos, "HiddenAttachesWithReasons", self.HiddenAttachesWithReasons, 0, 1)
    --self:DebugDumpTable(debugInfos, "CurrentAttachMaterials", self.CurrentAttachMaterials, 0, 10)
    self:DebugDumpTable(debugInfos, "AttachEffectIDs", self.AttachEffectIDs, 0, 1)
    self:DebugDumpTable(debugInfos, "AttachedEntities", self.AttachedEntities, 0, 1)
    self:DebugDumpTable(debugInfos, "AttachInfo", self.AttachInfo, 0, 1)

    table.insert(debugInfos, string.format("<Text>LeadingAnim : %s </>",self.LeadingAnim))
    table.insert(debugInfos, string.format("<Text>ManagerEntity : %s </>",self.ManagerEntity))
    table.insert(debugInfos, "<Title_Red>[Attach Info End]</>")

    return true
end


--region Basic Attach Functions
---@public
---Check if self can be attached by another entity
---@return boolean return True if this entity can be Attached
function ViewControlAttachComponent:CanBeAttach()
    return self.bInWorld and not self.isBriefAvatar 
end

function ViewControlAttachComponent:ListenAttachParentState()
	--监听状态变化消息，等待消息发送过来再次检查绑定
	if self.AttachInfo.AttachOwnerEID and (self.OwnerEventRegistered ~= self.AttachInfo.AttachOwnerEID) then
		if self.OwnerEventRegistered then
			Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.ATTACH_ON_OWNER_STATE_CHANGED, self, self.ApplyAttach, self.AttachInfo.AttachOwnerEID)
			self.OwnerEventRegistered = false
		end

		Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.ATTACH_ON_OWNER_STATE_CHANGED, self, self.ApplyAttach, self.AttachInfo.AttachOwnerEID)
		self.OwnerEventRegistered = self.AttachInfo.AttachOwnerEID
	end
end

function ViewControlAttachComponent:IsParentReadyToBeAttach()
	if not self.AttachInfo then
		return
	end
	
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
	if not Owner then
		self:ListenAttachParentState()
		return false 
	end
	
	-- 走了这个流程, 一定会有这个方法, 没有这个方法就需要补
	if not Owner:CanBeAttach() then
		self:ListenAttachParentState()
		return false
	end
	return true
end

---@private
---check attachment and exectue Attach if condition satisfied
function ViewControlAttachComponent:RefreshAttachState()
    if not self.AttachInfo then
        return
    end

	-- 外部保证 Owner 一定准备完毕
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
    if Owner:CanBeAttach() and self.bInWorld and not self.isBriefAvatar then
        --开始挂接
        if self.bAttached then
            return
        end

        local AttachInfo = self.AttachInfo

        if AttachInfo.bEnableAttachJoint then
            --VirtualSocket 挂接
            if AttachInfo.bEnableAttachJoint and AttachInfo.AttachVirtualSocketID == nil then
                AttachInfo.AttachVirtualSocketID = Owner:AddAttachSocket(
                        AttachInfo.SocketName,
                        AttachInfo.CompTag,
                        AttachInfo.AttachLoc,
                        AttachInfo.AttachRot,
                        AttachInfo.SocketLocation,
                        AttachInfo.SocketRotation,
						AttachInfo.bAbsoluteRotate
                )
            end

            AttachInfo.AttachID = Owner:AttachByVirtualSocket(self:uid(), AttachInfo.AttachVirtualSocketID, FTransform(), AttachInfo.bUpdateAnimInsMovement)
            Owner:ApplyAttachJointConfig(AttachInfo)
        else
            --UE 引擎挂接
			local TargetCompID = Owner:FindTargetComp(AttachInfo)
            if TargetCompID == 0 then
                return
            end

			self:DebugFmt("ViewControlAttachComponent:RefreshAttachState Start Attach To UE Comp:eid: %s uid：%s",self.eid,self:uid())
            self.CppEntity:KAPI_Actor_K2_AttachToComponent(TargetCompID, AttachInfo.SocketName, self.AttachInfo.LocAttachmentRule, self.AttachInfo.LocAttachmentRule, self.AttachInfo.LocAttachmentRule, false)
            if self.AttachInfo.LocAttachmentRule ~= EAttachmentRule.KeepWorld then
                self.CppEntity:KAPI_SetRelativeLocation(AttachInfo.AttachLoc)
                self.CppEntity:KAPI_SetRelativeRotation(AttachInfo.AttachRot)
            end
        end

        --修改挂接状态
        self:SetAttachFlag(true)
        self.CppEntity:KAPI_Actor_SetAttachSemantics(self.AttachInfo.AttachReason)

        --修改移动组件挂接状态
        if self.ChangeAttachMode and not self.AttachInfo.bSkipChangeAttachMode then
            self:ChangeAttachMode(true)
        end

        --通知被挂接方记录挂接信息
        Owner:RecordAttachEntity(self.eid, self.AttachInfo.AttachReason)

        --移除消息监听
        if self.OwnerEventRegistered then
            Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.ATTACH_ON_OWNER_STATE_CHANGED, self, self.ApplyAttach, self.AttachInfo.AttachOwnerEID)
            self.OwnerEventRegistered = false
        end
		
		local OwnerInvisible = Owner.CurrentEntityInvisible
		local selfInvisible = self.CurrentEntityInvisible
		-- 为了避免无效invisible 执行开销, 如果可见性是一致的, 就不需要立即生效; 但是显隐控制的设置数据是要进行处理的
		local NoApplyInvisibleImmediately = OwnerInvisible == selfInvisible
		if self.AttachInfo.bSyncVisibilityToParent then
			if selfInvisible then
				Owner:SetActorInVisible(Enum.EInVisibleReasons.AttachRelationSyncToParent, nil, NoApplyInvisibleImmediately)
			else
				Owner:SetActorVisible(Enum.EInVisibleReasons.AttachRelationSyncToParent,nil, NoApplyInvisibleImmediately)
			end
		else
			if OwnerInvisible then
				self:SetActorInVisible(Enum.EInVisibleReasons.AttachRelationSyncToChild, nil, NoApplyInvisibleImmediately )
			else
				self:SetActorVisible(Enum.EInVisibleReasons.AttachRelationSyncToChild, nil, NoApplyInvisibleImmediately)
			end
		end
		
		
        --设置成和owner一致的材质
		if self:ShouldSyncOwnerMaterial() then
			Game.MaterialManager:RefreshMaterialOnAttachEntityCreate(Owner.CharacterID, self.CharacterID, AttachInfo.AttachReason)
		elseif self:ShouldSyncMaterialToParent() and self.MaterialOverride then
			self:SyncMaterialToParent("ActiveStealthMaterial")
		end

        --播放挂接特效
        if self.AttachInfo.bPlayEffect then
            self:PlayLocationEffectByType("playonappear",nil,true)
        end
    else
        --目前还不能挂接 检查绑定关系，自动解绑当前绑定状态
        if self.bAttached then
            self:SetAttachFlag(false)

            if self.AttachInfo.bEnableAttachJoint and self.AttachInfo.AttachID then
				Owner:DetachFromVirtualSocket(self.AttachInfo.AttachID)
            else
                self:KAPI_Actor_K2_DetachFromActor(EDetachmentRule.KeepWorld, EDetachmentRule.KeepWorld, EDetachmentRule.KeepWorld)
            end
        end
    end
end


---@private
function ViewControlAttachComponent:RecordAttachEntity(AttachEntityid,AttachReason)
	if self.AttachedEntities == nil then
		self.AttachedEntities = {}
	end
    self.AttachedEntities[AttachEntityid] = AttachReason
end

---@private
function ViewControlAttachComponent:RemoveRecordedAttachEntity(AttachEntityId)
	if self.AttachedEntities == nil then
		return
	end
    self.AttachedEntities[AttachEntityId] = nil
end
---@private
function ViewControlAttachComponent:FindTargetComp(AttachInfo)
	-- TODO 挂接迭代后传入组件ID到 C++
    local TargetCompID = 0
	if AttachInfo.TargetCompID then
		TargetCompID = AttachInfo.TargetCompID
    elseif AttachInfo.CompTag then
        local Comps  = self.CppEntity:KAPI_Actor_GetComponentsByTag(SceneComponent, AttachInfo.CompTag)
        if Comps and Comps:Num() >0 then
			TargetCompID = Comps:Get(0)
        end
    else
		TargetCompID = self.MeshID and self.MeshID or self.CppEntity:KAPI_Actor_GetComponentByClass(SkeletalMeshComponent)
    end

	if TargetCompID == 0 then
		TargetCompID = self.CppEntity:KAPI_Actor_GetRootComponent()
	end
	
    return TargetCompID
	
end

---@private
function ViewControlAttachComponent:ApplyAttachJointConfig(AttachInfo)
    local VirtualSocketID = AttachInfo.AttachVirtualSocketID
    if AttachInfo.bEnableAttachJoint and VirtualSocketID then

		local bDoCollisionTest = AttachInfo.bDoCollisionTest
		if bDoCollisionTest then
			if self.ViewDowngradingTokenMapping == nil then
				--self:WarningFmt("ViewControlAttachComponent:ApplyAttachJointConfig Apply Attach Budget Downgrading Error：ViewDowngradingTokenMapping is nil,Entity: %s ,bInWorld: %s",tostring(self.ENTITY_TYPE), tostring(self.bInWorld))
				bDoCollisionTest = true
			else
				bDoCollisionTest = bDoCollisionTest and self:HasViewDowngradingBudget(WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.ATTACHMENT_MOVE_PHYSIC_CORRECT)
			end
		end
		
        self:EnableAttachSocketCollisionTest(VirtualSocketID,  bDoCollisionTest , AttachInfo.ProbeSize, AttachInfo.CollisionChannels)

        self:EnableLocationStabilize(VirtualSocketID, AttachInfo.bEnableStabilize, AttachInfo.StabilizeTolerance, AttachInfo.StabilizeInterpSpeed , AttachInfo.StabilizeInterpOutSpeed, AttachInfo.StabilizePointBoneName,AttachInfo.StabilizePointOffset)

        if AttachInfo.LocationUpdateMode == ELocationUpdateMode.Interp then
            self:EnableAttachSocketLocationLagByInterp(VirtualSocketID, AttachInfo.AttachLagSpeed, AttachInfo.AttachLagMaxDistance)
        elseif AttachInfo.LocationUpdateMode == ELocationUpdateMode.Bezier then
            self:EnableAttachSocketLocationLagByBezier(VirtualSocketID, AttachInfo.BezierStep, AttachInfo.BezierStepMaxDist, AttachInfo.BezierSpeedAlpha, AttachInfo.BezierLagSpeed, AttachInfo.BezierDirectionMultiplier)
        elseif AttachInfo.LocationUpdateMode == ELocationUpdateMode.Bezier_WithInCircleLag then
            self:EnableAttachSocketLocationLagByBezierWithInCircleLag(VirtualSocketID, AttachInfo.BezierStep, AttachInfo.BezierStepMaxDist, AttachInfo.BezierSpeedAlpha, AttachInfo.BezierLagSpeed, AttachInfo.BezierDirectionMultiplier,AttachInfo.InCircleLagSpeed, AttachInfo.InCircleLagTolerance)
        elseif AttachInfo.LocationUpdateMode == ELocationUpdateMode.FloatUpDown then
            self:EnableAttachSocketLocationLagByFloat(VirtualSocketID, AttachInfo.FloatTime, AttachInfo.FloatMaxHeight, AttachInfo.FloatMinHeight, AttachInfo.FloatEaseExp)
        else
            self:DisableAttachSocketLocationLag(VirtualSocketID)
        end

        self:EnableAttachSocketAttachRotationLag(VirtualSocketID, AttachInfo.bEnableAttachRotationLag, AttachInfo.AttachRotationLagSpeed, AttachInfo.bClampPitch,
                AttachInfo.PitchClampAngleMin,AttachInfo.PitchClampAngleMax, AttachInfo.RotationUpdateMode, AttachInfo.FaceToMoveDirectionTolerance, AttachInfo.RotateRoundTime)
    end
end

---@private
function ViewControlAttachComponent:RemoveAllAttach()
    --TODO:记录挂接信息再跑清理逻辑

    for Type,Attachs in pairs(self.AttachItems_V2) do
        for K, eid in pairs(Attachs) do
            local Entity = Game.EntityManager:getEntity(eid)
            if Entity then
                self:OnAttachItemRemoved( Entity)
				Entity:destroy()
            end
        end
    end

    for eid , _  in pairs(self.MarkForRemoveAttaches) do
        local Entity = Game.EntityManager:getEntity(eid)
        if Entity then
            self:OnAttachItemRemoved( Entity)
			Entity:destroy()
        end
    end

	if self.AttachedEntities ~= nil then
		for EntityID, _ in pairs(self.AttachedEntities) do
			local Entity = Game.EntityManager:getEntity(EntityID)
			if Entity then
				Entity:DetachFromOwner()
			end
		end
		
	end

    self.MarkForRemoveAttaches = {}
    self.AttachItems_V2 = {}
end

---@public
function ViewControlAttachComponent:SetAttachTo(AttachParams)
    if self.AttachInfo then
        self:DetachFromOwner()
    end

    if AttachParams.AttachReason == Enum.EAttachReason.None then
        self:ErrorFmt("Try to attach with AttachReason None,Please add a Reason")
    end

    self.AttachInfo = AttachParams
    self:SetAttachFlag(false)

	if self.bIsAttachItem then
		self:SetAttachReason(AttachParams.AttachReason)
	end
end

function ViewControlAttachComponent:ApplyAttach()
	if self:IsParentReadyToBeAttach() then
		self:RefreshAttachState()
	end
end

-- 更新 是否跳过挂接模式变更
function ViewControlAttachComponent:UpdateIsSkipChangeAttachMode(bSkipChangeAttachMode)
	if self.AttachInfo then
		self.AttachInfo.bSkipChangeAttachMode = bSkipChangeAttachMode
	end
end

function ViewControlAttachComponent:SetVisibilitySynchronizeToParent(IsSync)
	self.AttachInfo.bSyncVisibilityToParent = IsSync
	self.AttachInfo.bSyncOwnerVisibility = not IsSync
end

function ViewControlAttachComponent:SetMaterialSynchronizeToParent(IsSync)
	self.AttachInfo.bSyncMaterialChangeToParent = IsSync
end

-- 更新 UE 挂接类型
function ViewControlAttachComponent:UpdateUEAttachMode(LocAttachmentRule, RotAttachmentRule, ScaleAttachmentRule)
	if self.AttachInfo then
		self.AttachInfo.LocAttachmentRule = LocAttachmentRule
		self.AttachInfo.RotAttachmentRule = RotAttachmentRule
		self.AttachInfo.ScaleAttachmentRule = ScaleAttachmentRule
	end
end

-- 更新 UE 挂接组件ID
function ViewControlAttachComponent:UpdateUEAttachComponentID(TargetCompID)
	if self.AttachInfo then
		self.AttachInfo.TargetCompID = TargetCompID
	end
end

-- 更新挂接组件吸附骨骼点
function ViewControlAttachComponent:UpdateAttachSocketName(SocketName)
	if self.AttachInfo then
		self.AttachInfo.SocketName = SocketName
	end
end

---@public
function ViewControlAttachComponent:SetAttachToSimple(AttachReason, OwnerEid, SocketName, Location, Rotation, CompTag)
	if OwnerEid ~= nil then
		local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid,AttachReason,SocketName,CompTag)
													 :ModifyAttachLocAndRot(Location,Rotation)
													 :GenerateInfoAndDestroyBuilder()
		self:SetAttachTo(NewAttachInfo)
	end
end

---@public
function ViewControlAttachComponent:SetAttachToSimple_EX(AttachReason, OwnerEid, SocketName, LocX,LocY,LocZ, RotP, RotY, RotR, CompTag)
    if OwnerEid ~= nil then
        local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid,AttachReason,SocketName,CompTag)
                                                     :ModifyAttachLocAndRot_Ex(LocX,LocY,LocZ,RotP,RotY,RotR)
                                                     :GenerateInfoAndDestroyBuilder()
        self:SetAttachTo(NewAttachInfo)
    end
end

---定制挂接函数，后面有需要往这边加-------------------------------------------------------------------------------------------
---@public
function ViewControlAttachComponent:SetAttachToByModelCfgSocket( OwnerEid,Reason, CompTag, bPlayEffect, bEnableLag)
    local ModelData = Game.ActorAppearanceManager.AvatarModelLib[self.ModelID]
    if ModelData then
		
        local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid, Reason, ModelData.SlotName,CompTag)
                                                     :ModifyAttachLocAndRot(ModelData.Offset:GetTranslation(),ModelData.Offset:GetRotation():Rotator())
                                                     :EnablePlayEffect(bPlayEffect)
                                                     :EnableSyncOwnerMaterial(true)
                                                     :EnableVirtualSocketSimple(bEnableLag, false)
                                                     :SetVirtualSocketToLocationInterpMode(12,30)
                                                     :GenerateInfoAndDestroyBuilder()


        self:SetAttachTo(NewAttachInfo)
    end
end

---@public
function ViewControlAttachComponent:SetAttachToByModelCfgHitch(OwnerEid,Reason, CompTag, bPlayEffect,bEnableLag)
    local ModelData = Game.ActorAppearanceManager.AvatarModelLib[self.ModelID]
    if ModelData then
        local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid, Reason, ModelData.HitchParentBone,CompTag)
                :ModifyAttachLocAndRot(ModelData.HitchOffset:GetTranslation(),ModelData.HitchOffset:GetRotation():Rotator())
                :EnablePlayEffect(bPlayEffect)
                :EnableSyncOwnerMaterial(true)
                :EnableVirtualSocketSimple(bEnableLag, false)
                :SetVirtualSocketToLocationInterpMode(12,30)
                :GenerateInfoAndDestroyBuilder()


        self:SetAttachTo(NewAttachInfo)
    end
end

---@public
function ViewControlAttachComponent:SetAttachToForMinDragon(OwnerEid)
    local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid,  Enum.EAttachReason.MinDragon, "Root",nil)
            :ModifyAttachLocAndRot_Ex(0,0,161.5,0,0,0)
			:EnableSyncOwnerMaterial(true)
            :EnableVirtualSocket(true , true , -62,-92, 0, 0, 90 ,0, true ,20)
            :SetVirtualSocketToBezierMovementMode(3.5 ,500 ,10, 2,1,true, 2,50)
            :SetRotationUpdateToFaceToModeDir(10, 5,true, -15, 15)
            :GenerateInfoAndDestroyBuilder()

    self:SetAttachTo(NewAttachInfo)
end


function ViewControlAttachComponent:SetAttachToForBubbleEffect(AttachReason ,OwnerEid, OffsetX, OffsetY, OffsetZ, SocketName, StabilizePointBoneName,StabilizePointOffsetX,StabilizePointOffsetY,StabilizePointOffsetZ)

    local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid,  AttachReason, SocketName,nil)
            :ModifyAttachLocAndRot_Ex(OffsetX,OffsetY,OffsetZ,0,0,0)
            :EnableVirtualSocket(true , false , 0,0, 0, 0, 0 ,0, false ,0)
            :EnableVirtualSocketStabilization(true ,90,2, 20,StabilizePointBoneName, StabilizePointOffsetX,StabilizePointOffsetY,StabilizePointOffsetZ)
            :SetRotationUpdateToUseOwnerRot()
            :GenerateInfoAndDestroyBuilder()

    self:SetAttachTo(NewAttachInfo)
end


function ViewControlAttachComponent:SetAttachToForMinDragonEffect(AttachReason ,OwnerEid, OffsetX, OffsetY, OffsetZ, RotPitch,RotYaw,RotRoll,SocketName)


    local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid,  AttachReason, SocketName,nil)
            :ModifyAttachLocAndRot_Ex(OffsetX,OffsetY,OffsetZ,RotPitch,RotYaw,RotRoll)
            :EnableVirtualSocketSimple(true , false)
            :SetRotationUpdateToAutoRotate(10, false)
            :GenerateInfoAndDestroyBuilder()

    self:SetAttachTo(NewAttachInfo)
end

function ViewControlAttachComponent:SetAttachToForFoolCard(AttachReason ,OwnerEid, OffsetX, OffsetY, OffsetZ, RotPitch,RotYaw,RotRoll,SocketName)
	local roundTime = Enum.ERoleMechanismConstData.FOOL_CARD_LAPTIME
	local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid,  AttachReason, SocketName,nil)
												 :ModifyAttachLocAndRot_Ex(OffsetX,OffsetY,OffsetZ,RotPitch,RotYaw,RotRoll)
												 :EnableVirtualSocketSimple(true , false)
												 :SetRotationUpdateToAutoRotate(roundTime, true)
												 :GenerateInfoAndDestroyBuilder()

	self:SetAttachTo(NewAttachInfo)
end

---@public
function ViewControlAttachComponent:SetAttachToForWitch(OwnerEid, BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ, BindRotationYaw, ArmLen)
    local NewAttachInfo = AttachInfoParamsBuilder.NewAttachParam(OwnerEid,   Enum.EAttachReason.Witch, "Root",nil)
            :ModifyAttachLocAndRot_Ex(BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ,0,BindRotationYaw,0)
            :EnableVirtualSocket(true , true, -ArmLen,0, 0,0,0,0, true,80)
            :SetVirtualSocketToLocationInterpMode(1.5,1500)
            :SetRotationUpdateToFaceToModeDir(2,10,true,-15,15)
            :GenerateInfoAndDestroyBuilder()

    self:SetAttachTo(NewAttachInfo)
end


---@private
function ViewControlAttachComponent:GetOrCreateAttachJoint()
    if not self.AttachJointComp then
		local classID = Game.ObjectActorManager:GetIDByClass(AttachJointComponent_V2)
        self.AttachJointComp = self.CppEntity:KAPI_Actor_AddComponentByClassID(classID)
		self.CppEntity:KAPI_AttachJoint_SetIsEnableDebugDraw(false)
    end

    return self.AttachJointComp
end

---@Private
function ViewControlAttachComponent:DetachFromVirtualSocket(AttachID)
	self.CppEntity:KAPI_AttachJoint_RemoveAttachment(AttachID)
end

---@Private
function ViewControlAttachComponent:UECompAttachByVirtualSocket(UESceneComponentID, VSocketID, Transform, bUpdateAnimInsMovement) 
	self.CppEntity:KAPI_AttachJoint_AddAttachComponent(UESceneComponentID, VSocketID, Transform, bUpdateAnimInsMovement)
end

---@Private
function ViewControlAttachComponent:AttachByVirtualSocket(AttachEntityID, VSocketID, Transform, bUpdateAnimInsMovement)
	self.CppEntity:KAPI_AttachJoint_AddAttachActor(AttachEntityID, VSocketID, Transform, bUpdateAnimInsMovement)
end

---@Private
function ViewControlAttachComponent:AttachToVirtualSocket(TargetEntityID, VSocketID, Transform, bUpdateAnimInsMovement)
    local Entity = Game.EntityManager:getEntity(TargetEntityID)
    if Entity then
        return Entity:AttachByVirtualSocket(self:uid(), VSocketID, Transform, bUpdateAnimInsMovement)
    end
end

---@Private
---Add An AttachSocket By Config
function ViewControlAttachComponent:AddAttachSocket(AttachSocket, CompTag, BindOffset, BindRotation, SocketOffset, Rotation, bAbsoluteRotate)

    local NewID = ULLFunc.GetGlobalUniqueID()
    if not self.bInWorld then
        self:AddCommandCache("AddAttachSocketByGivenID", NewID, AttachSocket, CompTag, BindOffset, BindRotation, SocketOffset, Rotation, bAbsoluteRotate)
        return NewID
    end

    self:AddAttachSocketByGivenID( NewID, AttachSocket, CompTag, BindOffset, BindRotation, SocketOffset, Rotation, bAbsoluteRotate)
    return NewID
end


---@Private
---Add An AttachSocket By Config
function ViewControlAttachComponent:ForceSocketUpdate(SocketID,bEnableForceUpdate)
	if not self.bInWorld then
		self:AddCommandCache("ForceSocketUpdate",SocketID,bEnableForceUpdate)
	end
	
	self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_ForceSocketUpdate(SocketID,bEnableForceUpdate)
end

---@Private
function ViewControlAttachComponent:AddAttachSocketByGivenID(ID, AttachSocket, CompTag, BindOffset, BindRotation, SocketOffset, Rotation, bAbsoluteRotate)
    if not self.bInWorld then
        self:AddCommandCache("AddAttachSocketByGivenID", ID, AttachSocket, CompTag, BindOffset, BindRotation, SocketOffset, Rotation, bAbsoluteRotate)
        return
    end

    self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_AddAttachSocketByID(ID, AttachSocket or "", CompTag or "", BindOffset, BindRotation, SocketOffset, Rotation, bAbsoluteRotate)
end

---@Private
---Remove Attach Socket
function ViewControlAttachComponent:RemoveAttachSocket(SocketID)
    if not self.bInWorld then
        self:AddCommandCache("RemoveAttachSocket", SocketID)
        return
    end
    self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_RemoveAttachSocket(SocketID)
end

---@Private
function ViewControlAttachComponent:EnableLocationStabilize(SocketID, bEnable, StabilizeTolerance,StabilizeInterpSpeed,StabilizeInterpOutSpeed, StabilizePointBoneName,StabilizePointOffset)
    if not self.bInWorld then
        self:AddCommandCache("EnableLocationStabilize", bEnable, StabilizeTolerance,StabilizeInterpSpeed, StabilizeInterpOutSpeed,StabilizePointBoneName,StabilizePointOffset)
        return
    end

    self:GetOrCreateAttachJoint()
	if bEnable then
		self.CppEntity:KAPI_AttachJoint_EnableLocationStabilize(SocketID, StabilizeTolerance,StabilizeInterpSpeed,StabilizeInterpOutSpeed ,StabilizePointBoneName,StabilizePointOffset)
	else
		self.CppEntity:KAPI_AttachJoint_DisableLocationStabilize(SocketID)
	end
end

---@Private
function ViewControlAttachComponent:EnableAttachSocketCollisionTest(SocketID, bEnable, ProbeSize, CollisionChannels)
    if not self.bInWorld then
        self:AddCommandCache("EnableAttachSocketCollisionTest", bEnable, ProbeSize)
        return
    end

    self:GetOrCreateAttachJoint()
	if bEnable then
		local channelArray = slua.Array(EPropertyClass.Enum, EObjectTypeQuery)
		for _, channel in pairs(CollisionChannels:ToTable()) do
			channelArray:Add(channel)
		end
		
		self.CppEntity:KAPI_AttachJoint_EnableAttachSocketCollisionTest(SocketID, ProbeSize, channelArray)
	else
		self.CppEntity:KAPI_AttachJoint_DisableAttachSocketCollisionTest(SocketID)
	end
end

---@Private
function ViewControlAttachComponent:EnableAttachSocketLocationLagByFloat(SocketID, FloatTime, FloatMaxHeight,FloatMinHeight,FloatEaseExp)
    if not self.bInWorld then
        self:AddCommandCache("EnableAttachSocketFloat", SocketID, FloatTime, FloatMaxHeight,FloatMinHeight,FloatEaseExp)
        return
    end

    self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByFloat(SocketID, FloatTime, FloatMaxHeight,FloatMinHeight,FloatEaseExp)
end

---@Private
function ViewControlAttachComponent:EnableAttachSocketLocationLagByInterp(SocketID, AttachLagSpeed, AttachLagMaxDistance)
    if not self.bInWorld then
        self:AddCommandCache("EnableAttachSocketLocationLagByInterp", SocketID, AttachLagSpeed, AttachLagMaxDistance)
        return
    end

    self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByInterp(SocketID, AttachLagSpeed, AttachLagMaxDistance)
end

---@Private
function ViewControlAttachComponent:EnableAttachSocketLocationLagByBezier(SocketID, BezierStep,BezierStepMaxDist, BezierSpeedAlpha, BezierLagSpeed, BezierDirectionMultiplier)
    if not self.bInWorld then
        self:AddCommandCache("EnableAttachSocketLocationLagByBezier",SocketID, BezierStep,BezierStepMaxDist, BezierSpeedAlpha, BezierLagSpeed, BezierDirectionMultiplier)
        return
    end

    self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByBezier(SocketID, BezierStep,BezierStepMaxDist, BezierSpeedAlpha, BezierLagSpeed, BezierDirectionMultiplier)
end

---@Private
function ViewControlAttachComponent:EnableAttachSocketLocationLagByBezierWithInCircleLag(SocketID, BezierStep,BezierStepMaxDist, BezierSpeedAlpha, BezierLagSpeed, BezierDirectionMultiplier , InCircleLagSpeed, InCircleLagTolerance)
    if not self.bInWorld then
        self:AddCommandCache("EnableAttachSocketLocationLagByBezierWithInCircleLag",SocketID, BezierStep,BezierStepMaxDist, BezierSpeedAlpha, BezierLagSpeed, BezierDirectionMultiplier, InCircleLagSpeed, InCircleLagTolerance)
        return
    end

    self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByBezierWithInCircleLag(SocketID, BezierStep,BezierStepMaxDist, BezierSpeedAlpha, BezierLagSpeed, BezierDirectionMultiplier, InCircleLagSpeed, InCircleLagTolerance)
end

---@Private
function ViewControlAttachComponent:DisableAttachSocketLocationLag(SocketID)
    if not self.bInWorld then
        self:AddCommandCache("DisableAttachSocketLocationLag",SocketID)
        return
    end

	self:GetOrCreateAttachJoint()
	self.CppEntity:KAPI_AttachJoint_DisableAttachSocketLocationLag(SocketID)
end

---@Private
function ViewControlAttachComponent:EnableAttachSocketAttachRotationLag(SocketID, bEnable, AttachRotationLagSpeed, bClampPitch ,PitchClampAngleMin,PitchClampAngleMax, RotationUpdateMode, FaceToMoveDirectionTolerance,RotateRoundTime)
    if not self.bInWorld then
        self:AddCommandCache("EnableAttachSocketAttachRotationLag", SocketID, bEnable, AttachRotationLagSpeed, bClampPitch ,PitchClampAngleMin,PitchClampAngleMax, RotationUpdateMode, FaceToMoveDirectionTolerance,RotateRoundTime)
        return
    end

    self:GetOrCreateAttachJoint()
	if bEnable then
		self.CppEntity:KAPI_AttachJoint_EnableAttachSocketAttachRotationLag(SocketID, AttachRotationLagSpeed, bClampPitch ,PitchClampAngleMin,PitchClampAngleMax, RotationUpdateMode, FaceToMoveDirectionTolerance,RotateRoundTime)
	else
		self.CppEntity:KAPI_AttachJoint_DisableAttachSocketAttachRotationLag(SocketID)
	end
end

------------------------------------------------------------------------------------------------------------------------

---@public
---Detach From The Attach Owner
function ViewControlAttachComponent:DetachFromOwner()

    self:SetAttachFlag(false)

    if not self.AttachInfo then
        return
    end
	
	Game.MaterialManager:RemoveAllInheritMaterial(self.CharacterID)
	
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
	
    if self.AttachInfo.bEnableAttachJoint then
        --VirtualSocket 挂接清理
        if self.AttachInfo.AttachID and Owner and Owner.DetachFromVirtualSocket  then
            Owner:DetachFromVirtualSocket(self.AttachInfo.AttachID)
        end
        self.AttachInfo.AttachID = nil

		if self.AttachInfo.AttachVirtualSocketID and Owner and Owner.RemoveAttachSocket then
			Owner:RemoveAttachSocket(self.AttachInfo.AttachVirtualSocketID)
		end
    else
        --UE引擎原生挂接清理
        self:KAPI_Actor_K2_DetachFromActor(
                EDetachmentRule.KeepWorld,
                EDetachmentRule.KeepWorld,
                EDetachmentRule.KeepWorld
        )
    end

    --清理被挂接方状态及挂点
    if Owner and Owner.RemoveRecordedAttachEntity then
        Owner:RemoveRecordedAttachEntity(self.eid)
    end

    --退出挂接移动模式
    if self.ChangeAttachMode and not self.AttachInfo.bSkipChangeAttachMode then
        self:ChangeAttachMode(false)
    end

    --清理消息绑定
    if self.OwnerEventRegistered then
        Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.ATTACH_ON_OWNER_STATE_CHANGED, self, self.ApplyAttach, self.AttachInfo.AttachOwnerEID)
        self.OwnerEventRegistered = false
    end

    --移除绑定标记
    self:SetAttachFlag(false)

    --播放解除挂接特效
    --if self.AttachInfo.bPlayEffect then
    --    self:PlayLocationEffectByType("playondisappear",nil,true)
    --end

	-- 定位武器销毁原因
	if self.AttachInfo and self.AttachInfo.AttachReason == Enum.EAttachReason.Weapon then
		self:DebugFmt("ViewControlAttachComponent:ExitWorld AttachItem:eid: %s uid：%s	AttachOwnerEID:%s	ManagerEntityID:%s", self.eid, self:uid(), self.AttachInfo.AttachOwnerEID, self.ManagerEntity or "nil")
	end
	
    --清理AttachInfo
    AttachInfoParams.ReturnToPool(self.AttachInfo)
    self.AttachInfo = nil
end

---@public
---@return number EntityID of the Item
---Get The Attached Parent
function ViewControlAttachComponent:GetAttachParent()
    return self.AttachInfo ~= nil and self.AttachInfo.AttachOwnerEID or nil
end

---@public
---@return number EntityID of the Item
---Get Attach Manager Entity
function ViewControlAttachComponent:GetAttachItemManagerEntity()
    return self.ManagerEntity
end

---@public
---@return number EntityID of the Item
---Set Attach Manager Entity
function ViewControlAttachComponent:SetAttachItemManagerEntity(EntityID)
    self.ManagerEntity = EntityID
end
--endregion  Basic Attach Functions


--region Attach Item Functions
---@private
---private function used to clear all attacheds when entity leave space
---@private
---Do remove attach items in pending remove list
function ViewControlAttachComponent:OnTimerRemoveEntity()
    local NowTime =_G._now()

    local Removed = {}
    for eid2Remove, MarkTime in pairs(self.MarkForRemoveAttaches) do
        local DeltaTime = NowTime - MarkTime
        if DeltaTime > ViewControlAttachComponent.AttachCleanUpInterval then
            local Entity = Game.EntityManager:getEntity(eid2Remove)
            if Entity then
				Entity:destroy()
            end
            table.insert(Removed, eid2Remove)
        end
    end

    for _,eid in ipairs(Removed) do	
        self.MarkForRemoveAttaches[eid] = nil
    end

    if next(self.MarkForRemoveAttaches) == nil then
        return true
    else
        return false
    end
end

---@private
---Start a DelayRemove Timer.The attach entity will be removed after a certain delay time.
function ViewControlAttachComponent:StartDelayRemoveTimer()
	if self.bAttachExitWorld then
		for eid2Remove, _ in pairs(self.MarkForRemoveAttaches) do
			local Entity = Game.EntityManager:getEntity(eid2Remove)
			if Entity then
				Entity:destroy()
			end
		end
		self.MarkForRemoveAttaches = {}
		
		return 
	end
	
    if not self.DelayDestoryTimer then
        self.DelayDestoryTimer = Game.TimerManager:CreateTimerAndStart(
                function()
					if self.isDestroyed then
						return
					end
					
                    if self:OnTimerRemoveEntity() then
                        Game.TimerManager:StopTimerAndKill(self.DelayDestoryTimer)
                        return
                    end
                end, 5000, -1, nil, nil, false,
				function()
					self.DelayDestoryTimer = nil
				end)
    end
end

---@private
---@param AttachItemType number Use Enum EAttachReason
---@param ModelID string the Model Id of the attach item
---Find an attach item from the pending remove list with the given modelID, return the attach entity if exists, otherwise Create a new one.
function ViewControlAttachComponent:GetOrCreateAttachItem(AttachItemType,ModelID,ForceRequestReason)
	local ModelData = Game.ActorAppearanceManager.AvatarModelLib[ModelID]
	if not ModelData then
		return nil
	end
	
    for eid2Remove, _ in pairs(self.MarkForRemoveAttaches) do
        local Entity = Game.EntityManager:getEntity(eid2Remove)
        if Entity then
            if Entity:GetAttachItemModelID() == ModelID then
                self.MarkForRemoveAttaches[eid2Remove] = nil
                Entity:ReSetAttachState()
                return Entity
            end
        end
    end
	
	local ViewCreateTag
	local AttachClsName = ""
	local bStaticMesh = nil
	
	-- 挂接物 模型分类
	if ModelData.SMMesh and StringValid(ModelData.SMMesh.StaticMesh) then
		bStaticMesh = true
	elseif ModelData.SKMesh and ModelData.SKMesh.SkeletalMesh then
		bStaticMesh = false
	end
	
	-- 挂接物 Entity 分类
	if AttachItemType == Enum.EAttachItemType.Weapon then
		ViewCreateTag = VIEW_CREATE_LIMIT_TAG.WEAPON
		AttachClsName = "LocalWeaponItem"
	else
		ViewCreateTag = VIEW_CREATE_LIMIT_TAG.AttachItem
		if bStaticMesh == true then
			AttachClsName = "StaticMeshAttachItem"
		elseif bStaticMesh == false then
			AttachClsName = StringValid( ModelData.AnimData.AnimClass) and "AttachItem" or "SimpleAnimAttachItem"
		end
	end
	
	local NewEntity
	if ViewCreateTag then
		 NewEntity = Game.WorldManager.ViewBudgetMgr:RequestViewCreateToken(ViewCreateTag,  self, nil, ForceRequestReason, AttachClsName, { ModelID = ModelID })
	else
		NewEntity = Game.EntityManager:CreateLocalEntity(AttachClsName, { ModelID = ModelID })
	end

	if NewEntity then
		NewEntity:SetAttachItemManagerEntity(self.eid)
		NewEntity.bIsStaticMesh = bStaticMesh
		return NewEntity
	end
	
	return nil
end

---@private
---@param Entity number Use Enum EAttachReason
---Called When New Entity Attach to self
function ViewControlAttachComponent:OnAttachItemAdded(Entity)
    Entity:SetActorVisible(Enum.EInVisibleReasons.AttachDelayDestroy)
end

---@private
---@param Entity number Use Enum EAttachReason
---Called When Entity Detached from self
function ViewControlAttachComponent:OnAttachItemRemoved(Entity)
    Entity:SetActorInVisible(Enum.EInVisibleReasons.AttachDelayDestroy)
end

---@public
---GetAllAttaches
---@return table Array of attached Item ids
function ViewControlAttachComponent:GetAllAttachItems()
    local Attaches = {}
    for _, InnerAttaches in pairs(self.AttachItems_V2) do
        for K, eid in pairs(InnerAttaches) do
            table.insert(Attaches,eid)
        end
    end

    return Attaches
end

---@public
---GetAllAttaches
---@return table Array of attached Item ids
function ViewControlAttachComponent:GetAllAttaches()
    local Results = {}
    for eid, _ in pairs(self.AttachedEntities) do
        table.insert(Results,eid)
    end
    return Results
end

-- 复杂案例: 玩家挂在马上, 马挂了装饰物; 玩家身上挂了武器
-- 玩家逻辑被隐藏:
--             [1]玩家隐藏被调用, 玩家隐藏传递给武器(children)
--             [2]玩家是逻辑隐藏主控对象, 玩家隐藏传递给马
--             [3]马隐藏传递给马的装饰物, 但是来源是玩家, 所以略过玩家

-- 这里的传播理论上会复杂, 要看可见性的权威源、还要看挂接关系, 关键要控制好bSyncVisibilityToParent、bSyncOwnerVisibility
function ViewControlAttachComponent:VisibleSynchronizeForAttachRelation(IsInvisible,  DissolveConfig)
	-- 已经处理过了, 不再重入
	if self.IsVisibleSynchronizing == true then
		return 
	end
	
	self.IsVisibleSynchronizing = true
	local propagateDissolveConfig = nil
	if DissolveConfig and DissolveConfig.bPropagateToAttachItem then
		propagateDissolveConfig = DissolveConfig
	end

	if self.AttachInfo ~= nil and self.AttachInfo.bSyncVisibilityToParent then
		-- 避免传播循环
		local Entity = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
		if Entity ~= nil and Entity.SetActorInVisible then
			if IsInvisible then
				Entity:SetActorInVisible(Enum.EInVisibleReasons.AttachRelationSyncToParent, propagateDissolveConfig)
			else
				Entity:SetActorVisible(Enum.EInVisibleReasons.AttachRelationSyncToParent, propagateDissolveConfig)
			end
		end
	end
	
	if self.AttachedEntities ~= nil then

		local multipleSyncToParentCount = 0
		for eid, AttachReason in pairs(self.AttachedEntities) do
			-- 避免传播循环
			local Entity = Game.EntityManager:getEntity(eid)
			if Entity then
			
				if Entity.AttachInfo.bSyncVisibilityToParent then
					multipleSyncToParentCount = multipleSyncToParentCount + 1
				end
				if Entity.AttachInfo.bSyncOwnerVisibility and Entity.SetActorInVisible then
					-- 这里重入没有关系, 跟着调用者走的, 只要调用者显隐是正确的, 这里就是正确的
					if IsInvisible then
						Entity:SetActorInVisible(Enum.EInVisibleReasons.AttachRelationSyncToChild, propagateDissolveConfig)
					else
						Entity:SetActorVisible(Enum.EInVisibleReasons.AttachRelationSyncToChild, propagateDissolveConfig)
					end
				end
			
			end
		end

		-- 自己的child中, 最多有且只有一个能够设置bSyncVisibilityToParent
		if multipleSyncToParentCount > 1 then
			self:ErrorFmt("[ViewControlAttachComponent:VisibleSynchronizeForAttachRelation]  At almost 1 child to Sync visible to Parent Eid:%s", self.eid )
		end
	end

	self.IsVisibleSynchronizing = false
		
end


function ViewControlAttachComponent:GetAttachEntitiesByType(AttachItemType)
	if self.AttachedEntities == nil then
		return nil
	end
	local Results = nil
	for eid, Reason in pairs(self.AttachedEntities) do
		if Reason == AttachItemType then
			if Results == nil then
				Results = {}
			end
			table.insert(Results,eid)
		end
	end
    return Results
end

--TODO:@daiwei
function ViewControlAttachComponent:GetSyncMaterialAttachEntitiesByType(AttachItemType)
	local Results = {}
	if self.AttachedEntities ~= nil then
		for eid, ItemType in pairs(self.AttachedEntities) do
			if ItemType == AttachItemType then
				local Entity = Game.EntityManager:getEntity(eid)
				if Entity and Entity.AttachInfo and Entity.AttachInfo.bSyncOwnerMaterial then
					table.insert(Results,eid)
				end
			end
		end
		
	end
	return Results
end

-- 激活灵体材质半透效果
function ViewControlAttachComponent:ActiveStealthMaterial()
	self.MaterialChangeID = self:ChangeMaterialSimple(ViewResourceConst.VisibleControl.SemiTransparentMaterialPath)
	self:OverrideCustomDepth(CUSTOM_DEPTH_LOGIC_TYPE.Stealth,true,0)
	self.MaterialOverride = true
	self:SyncMaterialToParentAndChild("ActiveStealthMaterial")
end

-- 取消激活灵体材质半透效果
function ViewControlAttachComponent:DeActiveStealthMaterial()
	if self.MaterialChangeID then
		self:RevertMaterial(self.MaterialChangeID)
	end
	self.MaterialOverride = false
	self:RemoveCustomDepthOverride(CUSTOM_DEPTH_LOGIC_TYPE.Stealth)
	self:SyncMaterialToParentAndChild("DeActiveStealthMaterial")
end

function ViewControlAttachComponent:SyncMaterialToParent(funName)
	-- 向上传递
	if self.AttachInfo and self.AttachInfo.bSyncMaterialChangeToParent then
		local Entity = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
		if Entity then
			Entity[funName](Entity)
		end
	end
end

function ViewControlAttachComponent:SyncMaterialToParentAndChild(funName)
	-- 向上传递
	self:SyncMaterialToParent(funName)
	-- 向下传递
	if self.AttachedEntities ~= nil then
		for eid, ItemType in pairs(self.AttachedEntities) do
			local Entity = Game.EntityManager:getEntity(eid)
			-- 避免传播循环
			if Entity and Entity.AttachInfo and Entity.AttachInfo.bSyncOwnerMaterial and not Entity.AttachInfo.bSyncMaterialChangeToParent then
				Entity[funName](Entity)
			end
		end
	end
end

---@public
---Remove the attached item by specific type and entity id.
---@param AttachType number Use Enum.EAttachReason
---@param Ineid number the id of the attach entit
function ViewControlAttachComponent:RemoveAttachItemsByEntityID_V2(AttachItemType,Ineid)
	if not self.AttachItems_V2[AttachItemType] then
		return
	end

    for K, eid in pairs(self.AttachItems_V2[AttachItemType]) do
        if eid == Ineid then
            local Entity = Game.EntityManager:getEntity(Ineid)
            if Entity then
                Entity:ClearAttachState()
                self:OnAttachItemRemoved(Entity)
            end
		
            table.remove(self.AttachItems_V2[AttachItemType],K)

			self.MarkForRemoveAttaches = self.MarkForRemoveAttaches or {}
			self.MarkForRemoveAttaches[Ineid] = _G._now()
			self:StartDelayRemoveTimer()
			break
        end
    end
end

---@public
---@param AttachType number Use Enum EAttachReason
---Remove all the attached entity under the given attach type
function ViewControlAttachComponent:RemoveAttachItems_V2(AttachItemType)
    if self.AttachItems_V2[AttachItemType] then
        for K, eid in pairs( self.AttachItems_V2[AttachItemType]) do
            local Entity = Game.EntityManager:getEntity(eid)
            if Entity then
                Entity:DetachFromOwner()
                self:OnAttachItemRemoved(Entity)
            end

            self.MarkForRemoveAttaches = self.MarkForRemoveAttaches or {}
            self.MarkForRemoveAttaches[eid] = _G._now()
        end


		self.AttachItems_V2[AttachItemType] = nil
		self:StartDelayRemoveTimer()
    end

end

---@public
---@param AttachType number Use Enum EAttachReason
---@return table Array of the Attach item Eids
---Get all attached entity under this attach type
function ViewControlAttachComponent:GetAttachItemIds_V2(AttachItemType)
    return self.AttachItems_V2[AttachItemType] 
end

function ViewControlAttachComponent:FindAttachedEntityByTag(Tag)
	for Type,Attachs in pairs(self.AttachItems_V2) do
		for K, eid in pairs(Attachs) do
			local Entity = Game.EntityManager:getEntity(eid)
			if Entity and Entity:EntityHasTag(Tag) then
				return eid
			end
		end
	end

	return nil
end

function ViewControlAttachComponent:FindAttachedEntitiesByTypeAndTag(AttachItemType,Tag)
	local ReturnTable = {}
	if self.AttachItems_V2[AttachItemType] then
		for K, eid in pairs(self.AttachItems_V2[AttachItemType]) do
			local Entity = Game.EntityManager:getEntity(eid)
			if Entity and Entity:EntityHasTag(Tag) then
				table.insert(ReturnTable,eid)
			end
		end
	end

	return ReturnTable
end


function ViewControlAttachComponent:FindAttachedEntitiesByTypeAndTags(AttachItemType,Tags)
	local ReturnTable = {}
	if self.AttachItems_V2[AttachItemType] then
		for K, eid in pairs(self.AttachItems_V2[AttachItemType]) do
			local Entity = Game.EntityManager:getEntity(eid)
			if Entity and Entity:EntityHasTags(Tags) then
				table.insert(ReturnTable,eid)
			end
		end
	end

	return ReturnTable
end


---@public
---@param AttachType number Use Enum EAttachReason
---@param ModelID string The modelID of the attach item
---@return table the attach entity
---Add a New Attach Item
function ViewControlAttachComponent:AddAttachment_V2(AttachItemType,ModelID,OptionalTags,ForceRequestReason)
    local Entity = self:GetOrCreateAttachItem(AttachItemType,ModelID,ForceRequestReason)

	if not Entity then
		return
	end
	
    self.AttachItems_V2[AttachItemType] = self.AttachItems_V2[AttachItemType] or {}
    table.insert(self.AttachItems_V2[AttachItemType],Entity.eid)

    Entity:SetAttachItemAttachType(AttachItemType)

	if OptionalTags then
		for _,Tag in ipairs(OptionalTags) do
			Entity:AddTag(Tag)
		end
	end

    self:OnAttachItemAdded(Entity)

    return Entity
end

---@Private
function ViewControlAttachComponent:SetAttachItemAttachType(AttachItemType)
    self.AttachItemType = AttachItemType
end

---@Public
function ViewControlAttachComponent:GetAttachItemType(AttachItemType)
    return self.AttachItemType
end




---@public
function ViewControlAttachComponent:HideAttachItemByType(AttachItemType, bVisible, Reason)
    --self:Debug("HideAttachItemByType",Type, bVisible, Reason)
    if bVisible and self.HiddenAttachesWithReasons[AttachItemType] then
        self.HiddenAttachesWithReasons[AttachItemType][Reason] = nil
        if next( self.HiddenAttachesWithReasons[AttachItemType]) == nil then
            self.HiddenAttachesWithReasons[AttachItemType] = nil
        end
    elseif not bVisible then
        self.HiddenAttachesWithReasons[AttachItemType] = self.HiddenAttachesWithReasons[AttachItemType] or {}
        if self.HiddenAttachesWithReasons[AttachItemType] then
            self.HiddenAttachesWithReasons[AttachItemType][Reason] = true
        end
    end

    local Items = self:GetAttachItemIds_V2(AttachItemType)
	if Items ~= nil then
		for K, eid in pairs(Items) do
			local Entity = Game.EntityManager:getEntity(eid)
			if Entity then
				if bVisible then
					Entity:SetActorVisible(Reason)
				else
					Entity:SetActorInVisible(Reason)
				end
			end
		end
	end
end

-- 入场后根据 父挂件历史缓存更新显隐性
function ViewControlAttachComponent:UpdateWeaponItemVisibleWithCache()
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
	if Owner then
		local ReasonTable = Owner.HiddenAttachesWithReasons[Enum.EAttachItemType.Weapon]
		if not ReasonTable then return end

		for Reason, _ in pairs(ReasonTable) do
			self:SetActorInVisible(Reason)
		end
	end
end

--endregion Attach Item Functions

--region SpringArm Adjust Functions
---@public
function ViewControlAttachComponent:RefreshSpringArmConfig(BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ,BindRotationP, BindRotationY,BindRotationR, SocketLocDeltaX,SocketLocDeltaY,SocketLocDeltaZ, ArmLen)
    if not self.bInWorld then
        self:AddCommandCache("RefreshSpringArmConfig",BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ,BindRotationP, BindRotationY,BindRotationR, SocketLocDeltaX,SocketLocDeltaY,SocketLocDeltaZ, ArmLen)
        return
    end

    if not self.AttachInfo then
        return
    end

    local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)

    if Owner and self.bAttached then

        self.AttachInfo = AttachInfoParamsBuilder.ModifyAttachParam(self.AttachInfo)
                                                 :ModifyAttachLocAndRot_Ex(BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ,BindRotationP, BindRotationY,BindRotationR)
                                                 :ModifyVirtualSocketLocation_EX(SocketLocDeltaX,SocketLocDeltaY,SocketLocDeltaZ)
                                                 :GenerateInfoAndDestroyBuilder()


        if self.AttachJointComp then
            self.CppEntity:KAPI_AttachJoint_ModifyAttachSocket(self.AttachInfo.AttachVirtualSocketID,self.AttachInfo.SocketName, self.AttachInfo.CompTag or "", self.AttachInfo.AttachLoc,self.AttachInfo.AttachRot, self.AttachInfo.SocketLocation, self.AttachInfo.SocketRotation)
        end

        Owner:ApplyAttachJointConfig(self.AttachInfo)
    end
end



function ViewControlAttachComponent:PauseAttachLag()
    if not self.bInWorld or not self.AttachInfo then
        self:AddCommandCache("PauseAttachLag")
        return
    end

    local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)

    if Owner and self.bAttached then
        self.AttachInfo = AttachInfoParamsBuilder.ModifyAttachParam(self.AttachInfo)
                                                 :SetVirtualSocketLocationUpdateModeToNone()
                                                 :GenerateInfoAndDestroyBuilder()

        Owner:ApplyAttachJointConfig(self.AttachInfo)
    end
end

---@public
function ViewControlAttachComponent:UnpauseAttachLag()
    if not self.bInWorld or not self.AttachInfo  then
        self:AddCommandCache("UnpauseAttachLag")
        return
    end
    local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)

    if Owner and self.bAttached then

        self.AttachInfo = AttachInfoParamsBuilder.ModifyAttachParam(self.AttachInfo)
                                                 :SetVirtualSocketToLocationInterpMode(self.AttachInfo.AttachLagSpeed,self.AttachInfo.AttachLagMaxDistance)
                                                 :GenerateInfoAndDestroyBuilder()
        Owner:ApplyAttachJointConfig( self.AttachInfo)
    end
end

--endregion SpringArm Adjust Functions

---@private
function ViewControlAttachComponent:ShouldSyncOwnerMaterial()
	if self.AttachInfo then
		return self.AttachInfo.bSyncOwnerMaterial 
	end
	return false
end

function ViewControlAttachComponent:ShouldSyncMaterialToParent()
	if self.AttachInfo then
		return self.AttachInfo.bSyncMaterialChangeToParent
	end
	return false
end

function ViewControlAttachComponent:SetAttachFlag(bAttach)
    if not self.AttachInfo then return end

    local parentEntity = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerEID)
    
    if bAttach then
        -- 添加挂接逻辑关系
        self:AddAttachToLogicParentExposed(parentEntity.CharacterID)
    elseif self.bAttached then
        -- 移除挂接逻辑关系
        self:RemoveAttachFromLogicParentExposed()
    end

	self.bAttached = bAttach
end

function ViewControlAttachComponent:AddAttachToLogicParentExposed(parentLogicId)
	self.CppEntity:KAPI_Actor_AddAttachToLogicParent(parentLogicId)
end

function ViewControlAttachComponent:RemoveAttachFromLogicParentExposed()
	self.CppEntity:KAPI_Actor_RemoveAttachFromLogicParent()
end

function ViewControlAttachComponent:RemoveAllLogicChildExposed()
	self.CppEntity:KAPI_Actor_RemoveAllLogicChild()
end

function ViewControlAttachComponent:EnableLogicFeatureSynchronizeToChildsExposed(logicChildID, stateMask)
	self.CppEntity:KAPI_Actor_EnableLogicFeatureSynchronizeToChilds(logicChildID, stateMask)
end
	
function ViewControlAttachComponent:DisableLogicFeatureSynchronizeToChildsExposed(logicChildID, stateMask)
	self.CppEntity:KAPI_Actor_DisableLogicFeatureSynchronizeToChilds(logicChildID, stateMask)
end
	
return ViewControlAttachComponent