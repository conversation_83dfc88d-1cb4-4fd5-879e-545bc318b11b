local AttachJointComponent_V2 = import("AttachJointComponent_V2")
local EAttachmentRule = import("EAttachmentRule")
local EDetachmentRule = import("EDetachmentRule")
local ERotationUpdateMode = import("ERotationUpdateMode")
local ELocationUpdateMode = import("ELocationUpdateMode")
local ULLFunc = import("LowLevelFunctions")
local CollisionConst = kg_require("Shared.Const.CollisionConst")
local WorldViewBudgetConst = kg_require("Gameplay.CommonDefines.WorldViewBudgetConst")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local SkeletalMeshComponent = import("SkeletalMeshComponent")
local SceneComponent = import("SceneComponent")
local VIEW_CREATE_LIMIT_TAG = WorldViewBudgetConst.VIEW_CREATE_LIMIT_TAG
local CUSTOM_DEPTH_LOGIC_TYPE = ViewControlConst.CUSTOM_DEPTH_LOGIC_TYPE
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

---------------------------------
--- ViewControl 挂接接口
--------------------------------
ViewControlAttachComponent = DefineComponent("ViewControlAttachComponent")

-- 小龙虚拟挂接 固定配置
ViewControlAttachComponent.MinDragonConst = {OffsetZ = 161.5, X = -62, Y = -92, Z = 0, Pitch = 0, Yaw = 90 , Roll = 0, CollisionProbSize = 20, BezierStep = 3.5, BezierStepMaxDist = 500, 
											 BezierSpeedAlpha = 10, BezierLagSpeed = 2,BezierDirectionMultiplier = 1,bWithInCircle = true, InCircleLagSpeed = 2,InCircleLagTolerance = 50, 
											 AttachRotationLagSpeed = 10,FaceToMoveDirectionTolerance = 5,bClampPitch = true,PitchClampAngleMin = -15,PitchClampAngleMax = 15}



function ViewControlAttachComponent:ctor()
	---自身挂接物品LocalAtttachItem信息信息 ----------
	-- K,value = tpye,uid 		自己创建的挂接物(不一定挂接在自己身上)
    self.CreatedAttachItemsByType = nil
	-- K,value = uid,reason		自己挂接的挂接物(不一定是自己创建的)
	self.ChildEntitiesByReason = nil
	-- K,value = tag,uid
	self.ChildEntitiesByTag = nil
	
	-- 自身作为挂接物 的类型 & 自身作为挂接原因
	self.AttachItemType = nil
	self.AttachItemReason = nil
    self.HiddenAttachesWithReasons = {}
    self.HasAttachJointComp = false

    ---挂接Entity信息 -------------------
    self.AttachInfo = nil
    self.ManagerEntity = self:uid()
    self.OwnerEventRegisteredUID = nil
	self.IsVisibleSynchronizing= false
end

function ViewControlAttachComponent:dtor()
end


function ViewControlAttachComponent:__component_AfterEnterWorld__()
    self:ApplyAttach()
    self:AttachGenerateSpawnEffect()
end

function ViewControlAttachComponent:__component_ExitWorld__()
    self:RemoveAllAttach()
    self:DetachFromOwner()

    self.AttachJoint = nil
	self.HasAttachJointComp = false
end

function ViewControlAttachComponent:__component_AppendGamePlayDebugInfo__(debugInfos)
    table.insert(debugInfos, "<Title_Red>[ViewControlAttachComponent]</>")
    self:DebugDumpTable(debugInfos, "AttachItems_V2", self.CreatedAttachItemsByType, 0, 1)
    --self:DebugDumpTable(debugInfos, "AttachComps", self.AttachComps, 0, 10)
    self:DebugDumpTable(debugInfos, "HiddenAttachesWithReasons", self.HiddenAttachesWithReasons, 0, 1)
    self:DebugDumpTable(debugInfos, "AttachedEntities", self.ChildEntitiesByReason, 0, 1)
    self:DebugDumpTable(debugInfos, "AttachInfo", self.AttachInfo, 0, 1)
	
    table.insert(debugInfos, string.format("<Text>ManagerEntity : %s </>",self.ManagerEntity))
    table.insert(debugInfos, "<Title_Red>[Attach Info End]</>")

    return true
end


function ViewControlAttachComponent:InitAttachInfo(OwnerUid, AttachReason, SocketName, CompTag)
	if not AttachReason or AttachReason == WorldViewConst.ATTACH_REASON.None then
		self:ErrorFmt("Try to attach with AttachReason None or nil, Please add a Reason")
	end
	
	self.AttachItemReason = AttachReason
	self.AttachInfo = self.AttachInfo or {}
	---挂接基本信息 ---------------------------------------
	self.AttachInfo.AttachOwnerUID = OwnerUid
	self.AttachInfo.SocketName = SocketName
	self.AttachInfo.CompTag = CompTag
	self.AttachInfo.AttachLoc = FVector(0, 0, 0)
	self.AttachInfo.AttachRot = FRotator(0, 0, 0)

	self.AttachInfo.bPlayEffect = false
	self.AttachInfo.bSyncOwnerVisibility = true
	self.AttachInfo.bSyncOwnerMaterial= false
	-- 显隐性传递给 parent, 例如用于骑乘
	self.AttachInfo.bSyncVisibilityToParent = false
	-- 材质变化传递给 Parent
	self.AttachInfo.bSyncMaterialChangeToParent = false

	-- UE 挂接数据结构
	self.AttachInfo.bSkipChangeAttachMode = false
	self.AttachInfo.LocAttachmentRule = EAttachmentRule.SnapToTarget
	self.AttachInfo.RotAttachmentRule = EAttachmentRule.SnapToTarget
	self.AttachInfo.ScaleAttachmentRule = EAttachmentRule.SnapToTarget
	self.AttachInfo.TargetCompID = nil

	---VirtualSocket ---------------------------------------
	---Virtual挂点基本信息
	self.AttachInfo.bEnableAttachJoint = false
	self.AttachInfo.SocketLocation = FVector(0, 0, 0)
	self.AttachInfo.SocketRotation = FRotator(0, 0, 0)
	self.AttachInfo.bDoCollisionTest = false
	self.AttachInfo.ProbeSize = 30
	self.AttachInfo.CollisionChannels = CollisionConst.QUERY_BY_OBJECTTYPES.COMMON_WORLD_STATIC

	---开启位置稳定
	self.AttachInfo.bEnableStabilize = false
	self.AttachInfo.StabilizeTolerance = 30
	self.AttachInfo.StabilizeInterpSpeed = 2
	self.AttachInfo.StabilizeInterpOutSpeed = 3
	self.AttachInfo.StabilizePointBoneName = ""
	self.AttachInfo.StabilizePointOffset = FVector(0, 0, 0)

	---位置更新方式
	self.AttachInfo.LocationUpdateMode = ELocationUpdateMode.None
	self.AttachInfo.FloatTime = 2
	self.AttachInfo.FloatMaxHeight = 50
	self.AttachInfo.FloatMinHeight = -50
	self.AttachInfo.FloatEaseExp = 3
	self.AttachInfo.AttachLagSpeed = 1
	self.AttachInfo.AttachLagMaxDistance = 1000
	self.AttachInfo.BezierStep = 0.1
	self.AttachInfo.BezierStepMaxDist = 100
	self.AttachInfo.BezierSpeedAlpha = 10
	self.AttachInfo.BezierLagSpeed = 1
	self.AttachInfo.BezierDirectionMultiplier = 0.2
	self.AttachInfo.InCircleLagSpeed = 2
	self.AttachInfo.InCircleLagTolerance = 0

	---旋转更新方式
	self.AttachInfo.bEnableAttachRotationLag = false
	self.AttachInfo.bAbsoluteRotate = false
	self.AttachInfo.AttachRotationLagSpeed = 5
	self.AttachInfo.bClampPitch = true
	self.AttachInfo.PitchClampAngleMin = 0
	self.AttachInfo.PitchClampAngleMax = 0
	self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.UseDesiredRot
	self.AttachInfo.FaceToMoveDirectionTolerance = 0.5
	self.AttachInfo.RotateRoundTime = 10

	---RunningData ------------------------------------------
	self.AttachInfo.AttachVirtualSocketID = nil
	self.AttachInfo.AttachID = nil
end

--region Basic Attach Functions
---@public
---Check if self can be attached by another entity
---@return boolean return True if this entity can be Attached
function ViewControlAttachComponent:CanBeAttach()
    return self.bInWorld and not self.isBriefEntity 
end

function ViewControlAttachComponent:RemoveAttachOwnerListener()
	if self.OwnerEventRegisteredUID then
		Game.UniqEventSystemMgr:RemoveListener(self.OwnerEventRegisteredUID, EEventTypesV2.ROLE_ON_BORN, "ApplyAttach", self)
		self.OwnerEventRegisteredUID = nil
	end
end

function ViewControlAttachComponent:ListenAttachParentState()
	--监听状态变化消息，等待消息发送过来再次检查绑定
	if self.AttachInfo.AttachOwnerUID and self.OwnerEventRegisteredUID ~= self.AttachInfo.AttachOwnerUID then
		self:RemoveAttachOwnerListener()
		Game.UniqEventSystemMgr:AddListener(self.AttachInfo.AttachOwnerUID, EEventTypesV2.ROLE_ON_BORN, "ApplyAttach", self)
		self.OwnerEventRegisteredUID = self.AttachInfo.AttachOwnerUID
	end
end

function ViewControlAttachComponent:IsParentReadyToBeAttach()
	if not self.AttachInfo then
		return
	end
	
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerUID)
	if not Owner then
		self:ListenAttachParentState()
		return false 
	end
	
	-- 走了这个流程, 一定会有这个方法, 没有这个方法就需要补
	if not Owner:CanBeAttach() then
		self:ListenAttachParentState()
		return false
	end
	return true
end

---@private
---check attachment and exectue Attach if condition satisfied
function ViewControlAttachComponent:RefreshAttachState()
    if not self.AttachInfo or self.bAttached then
        return
    end

	-- 外部保证 Owner 一定准备完毕
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerUID)
    if Owner:CanBeAttach() and self.bInWorld and not self.isBriefEntity then
        --开始挂接
        local AttachInfo = self.AttachInfo
        if AttachInfo.bEnableAttachJoint then
			if Owner.HasAttachJointComp == false then
				Owner:CreateAttachJointComponent()
			end
			
            --VirtualSocket 挂接
            if AttachInfo.bEnableAttachJoint and AttachInfo.AttachVirtualSocketID == nil then
				AttachInfo.AttachVirtualSocketID = ULLFunc.GetGlobalUniqueID()
				AttachInfo.AttachID = Owner.CppEntity:KAPI_VirtualAttach_AddAttachActorWithRelation(self:uid(), AttachInfo.AttachVirtualSocketID, AttachInfo.SocketName or "", 
					AttachInfo.CompTag or "", AttachInfo.AttachLoc, AttachInfo.AttachRot, AttachInfo.SocketLocation, AttachInfo.SocketRotation, AttachInfo.bAbsoluteRotate)
				Owner:ApplyAttachJointConfig(AttachInfo) 
            end
        else
            --UE 引擎挂接
			local TargetCompID = Owner:FindTargetComp(AttachInfo)

			self:DebugFmt("ViewControlAttachComponent:RefreshAttachState Start Attach To UE Comp: uid：%s",self:uid())
            self.CppEntity:KAPI_Attach_AttachToComponentWithRelation(TargetCompID, AttachInfo.SocketName, self.AttachInfo.LocAttachmentRule, self.AttachInfo.LocAttachmentRule, self.AttachInfo.LocAttachmentRule, false, AttachInfo.AttachLoc, AttachInfo.AttachRot)
        end

        --修改挂接状态
		self.bAttached = true
        self.CppEntity:KAPI_Actor_SetAttachSemantics(Owner:uid(), self.AttachItemReason, self.AttachInfo.bSyncVisibilityToParent, self.AttachInfo.bSyncOwnerVisibility)

        --修改移动组件挂接状态
        if self.ChangeAttachMode and not self.AttachInfo.bSkipChangeAttachMode then
            self:ChangeAttachMode(true)
        end

        --通知被挂接方记录挂接信息
        Owner:RecordAttachEntity(self:uid(), self.AttachItemReason)

        --移除消息监听
		self:RemoveAttachOwnerListener()
		
		
        --设置成和owner一致的材质
		if self:ShouldSyncOwnerMaterial() then
			Game.MaterialManager:RefreshMaterialOnAttachEntityCreate(Owner.CharacterID, self.CharacterID, self.AttachItemReason)
		elseif self:ShouldSyncMaterialToParent() and self.MaterialOverride then
			self:SyncMaterialToParent("ActiveStealthMaterial")
		end

        --播放挂接特效
        if self.AttachInfo.bPlayEffect then
            self:PlayLocationEffectByType("playonappear",nil,true)
        end
    end
end


---@private
function ViewControlAttachComponent:RecordAttachEntity(AttachEntityUid,AttachReason)
	if self.ChildEntitiesByReason == nil then
		self.ChildEntitiesByReason = {}
	end
    self.ChildEntitiesByReason[AttachEntityUid] = AttachReason
	self:DebugFmt("ViewControlAttachComponent:RecordAttachEntity AttachEntityUid:%s	AttachReason:%s", AttachEntityUid, AttachReason)
end

---@private
function ViewControlAttachComponent:RemoveRecordedAttachEntity(AttachEntityUid)
	if self.ChildEntitiesByReason == nil then
		return
	end
    self.ChildEntitiesByReason[AttachEntityUid] = nil
	self:DebugFmt("ViewControlAttachComponent:RemoveRecordedAttachEntity AttachEntityUid:%s", AttachEntityUid)
end
---@private
function ViewControlAttachComponent:FindTargetComp(AttachInfo)
    local TargetCompID = KG_INVALID_ID
	if AttachInfo.TargetCompID then
		TargetCompID = AttachInfo.TargetCompID
    elseif AttachInfo.CompTag then
		TargetCompID = self.CppEntity:KAPI_Actor_GetComponentByTag(SceneComponent, AttachInfo.CompTag)
    else
		TargetCompID = self.MeshID and self.MeshID or self.CppEntity:KAPI_Actor_GetComponentByClass(SkeletalMeshComponent)
    end

	if TargetCompID == KG_INVALID_ID then
		TargetCompID = self.CppEntity:KAPI_Actor_GetRootComponent()
	end
	
    return TargetCompID
end

---@private
function ViewControlAttachComponent:ApplyAttachJointConfig(AttachInfo)
    local VirtualSocketID = AttachInfo.AttachVirtualSocketID
    if AttachInfo.bEnableAttachJoint and VirtualSocketID then

		local bDoCollisionTest = AttachInfo.bDoCollisionTest
		if bDoCollisionTest then
			if self.ViewDowngradingTokenMapping == nil then
				--self:WarningFmt("ViewControlAttachComponent:ApplyAttachJointConfig Apply Attach Budget Downgrading Error：ViewDowngradingTokenMapping is nil,Entity: %s ,bInWorld: %s",tostring(self.ENTITY_TYPE), tostring(self.bInWorld))
				bDoCollisionTest = true
			else
				bDoCollisionTest = bDoCollisionTest and self:HasViewDowngradingBudget(WorldViewBudgetConst.VIEW_DOWNGRADING_TAG.ATTACHMENT_MOVE_PHYSIC_CORRECT)
			end
		end
		
		if bDoCollisionTest then
			self.CppEntity:KAPI_AttachJoint_EnableAttachSocketCollisionTest(VirtualSocketID, AttachInfo.ProbeSize, AttachInfo.CollisionChannels)
		else
			self.CppEntity:KAPI_AttachJoint_DisableAttachSocketCollisionTest(VirtualSocketID)
		end
		
		if AttachInfo.bEnableStabilize then
			self.CppEntity:KAPI_AttachJoint_EnableLocationStabilize(VirtualSocketID,AttachInfo.StabilizeTolerance, AttachInfo.StabilizeInterpSpeed , AttachInfo.StabilizeInterpOutSpeed, AttachInfo.StabilizePointBoneName,AttachInfo.StabilizePointOffset)
		else
			self.CppEntity:KAPI_AttachJoint_DisableLocationStabilize(VirtualSocketID)
		end
		
        if AttachInfo.LocationUpdateMode == ELocationUpdateMode.Interp then
			self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByInterp(VirtualSocketID, AttachInfo.AttachLagSpeed, AttachInfo.AttachLagMaxDistance)
        elseif AttachInfo.LocationUpdateMode == ELocationUpdateMode.Bezier then
			self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByBezier(VirtualSocketID, AttachInfo.BezierStep, AttachInfo.BezierStepMaxDist, AttachInfo.BezierSpeedAlpha, AttachInfo.BezierLagSpeed, AttachInfo.BezierDirectionMultiplier)
        elseif AttachInfo.LocationUpdateMode == ELocationUpdateMode.Bezier_WithInCircleLag then
			self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByBezierWithInCircleLag(VirtualSocketID, AttachInfo.BezierStep, AttachInfo.BezierStepMaxDist, AttachInfo.BezierSpeedAlpha, AttachInfo.BezierLagSpeed, AttachInfo.BezierDirectionMultiplier,AttachInfo.InCircleLagSpeed, AttachInfo.InCircleLagTolerance)
        elseif AttachInfo.LocationUpdateMode == ELocationUpdateMode.FloatUpDown then
			self.CppEntity:KAPI_AttachJoint_EnableAttachSocketLocationLagByFloat(VirtualSocketID, AttachInfo.FloatTime, AttachInfo.FloatMaxHeight, AttachInfo.FloatMinHeight, AttachInfo.FloatEaseExp)
        else
			self.CppEntity:KAPI_AttachJoint_DisableAttachSocketLocationLag(VirtualSocketID)
        end

		if AttachInfo.bEnableAttachRotationLag then
			self.CppEntity:KAPI_AttachJoint_EnableAttachSocketAttachRotationLag(VirtualSocketID, AttachInfo.AttachRotationLagSpeed, AttachInfo.bClampPitch,
				AttachInfo.PitchClampAngleMin,AttachInfo.PitchClampAngleMax, AttachInfo.RotationUpdateMode, AttachInfo.FaceToMoveDirectionTolerance, AttachInfo.RotateRoundTime)
		else
			self.CppEntity:KAPI_AttachJoint_DisableAttachSocketAttachRotationLag(VirtualSocketID)
		end
    end
end

---@private
function ViewControlAttachComponent:RemoveAllAttach()
	if not self.CreatedAttachItemsByType then return end
	-- 第一步：自身创建的挂接物 跟随创建者销毁
    for Type,Attachs in pairs(self.CreatedAttachItemsByType) do
        for K, uid in pairs(Attachs) do
            local Entity = Game.EntityManager:getEntity(uid)
            if Entity then
				Entity:destroy()
            end
        end
    end

	-- 第二步：自身挂接的挂接物只做移除 (不一定是自身创建), 挂接物销毁时会从 ChildEntitiesByReason 移除,如果自身挂接物也是自己创建的挂接物则会在 第一步 中从容器中移除
	if self.ChildEntitiesByReason ~= nil then
		for EntityID, _ in pairs(self.ChildEntitiesByReason) do
			local Entity = Game.EntityManager:getEntity(EntityID)
			if Entity then
				Entity:DetachFromOwner()
			end
		end
	end
	
    self.CreatedAttachItemsByType = nil
	self.ChildEntitiesByTag = nil
end

function ViewControlAttachComponent:ApplyAttach()
	if self:IsParentReadyToBeAttach() then
		self:RefreshAttachState()
	end
end

-- 更新 是否跳过挂接模式变更
function ViewControlAttachComponent:UpdateIsSkipChangeAttachMode(bSkipChangeAttachMode)
	self.AttachInfo.bSkipChangeAttachMode = bSkipChangeAttachMode
end

function ViewControlAttachComponent:SetVisibilitySynchronizeToParent(IsSync)
	self.AttachInfo.bSyncVisibilityToParent = IsSync
	self.AttachInfo.bSyncOwnerVisibility = not IsSync
end

function ViewControlAttachComponent:SetMaterialSynchronizeToParent(IsSync)
	self.AttachInfo.bSyncMaterialChangeToParent = IsSync
end

-- 更新 UE 挂接类型
function ViewControlAttachComponent:UpdateUEAttachMode(LocAttachmentRule, RotAttachmentRule, ScaleAttachmentRule)
	self.AttachInfo.LocAttachmentRule = LocAttachmentRule
	self.AttachInfo.RotAttachmentRule = RotAttachmentRule
	self.AttachInfo.ScaleAttachmentRule = ScaleAttachmentRule
end

---开启VirtualSocket挂接模式
---@param bEnable boolean
---@param SocketLocationX number VirtualSocket相对挂接点的相对偏移X
---@param SocketLocationY number VirtualSocket相对挂接点的相对偏移Y
---@param SocketLocationZ number VirtualSocket相对挂接点的相对偏移Z
---@param SocketRotationPitch number VirtualSocket相对挂接点的相对旋转Pitch
---@param SocketRotationYaw number VirtualSocket相对挂接点的相对旋转Yaw
---@param SocketRotationRoll number VirtualSocket相对挂接点的相对旋转Roll
---@param bDoCollisionTest boolean 是否开启碰撞检查
---@param CollisionProbeSize number 碰撞检查点碰撞大小
function ViewControlAttachComponent:EnableVirtualSocket(bEnable,SocketLocationX,SocketLocationY,
														SocketLocationZ,SocketRotationPitch,SocketRotationYaw,SocketRotationRoll,
														bDoCollisionTest,CollisionProbeSize,CollisionChannels)
	self.AttachInfo.bEnableAttachJoint = bEnable
	self.AttachInfo.SocketLocation = FVector(SocketLocationX, SocketLocationY, SocketLocationZ)
	self.AttachInfo.SocketRotation = FRotator(SocketRotationPitch, SocketRotationYaw, SocketRotationRoll)
	self.AttachInfo.bDoCollisionTest = bDoCollisionTest
	self.AttachInfo.ProbeSize = CollisionProbeSize
	self.AttachInfo.CollisionChannels = CollisionChannels or CollisionConst.QUERY_BY_OBJECTTYPES.COMMON_WORLD_STATIC
end

---切换VirtualSocket旋转模式【4】:和移动方向保持一致并且RInterTo插值，可限制俯仰角
---@param AttachRotationLagSpeed number RInterpTo插值速度
---@param FaceToMoveDirectionTolerance number 面朝速度方向开启阈值，速度达到以后才会面朝速度方向
---@param bClampPitch number 是否开启俯仰角限制
---@param PitchClampAngleMin number 仰角最大角度 Deg
---@param PitchClampAngleMax number 俯角最大角度 Deg
function ViewControlAttachComponent:SetRotationUpdateToFaceToModeDir(AttachRotationLagSpeed,FaceToMoveDirectionTolerance,bClampPitch,PitchClampAngleMin,PitchClampAngleMax)
	self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.FaceToMoveDir
	self.AttachInfo.bEnableAttachRotationLag = true
	self.AttachInfo.bAbsoluteRotate = false
	self.AttachInfo.AttachRotationLagSpeed =AttachRotationLagSpeed
	self.AttachInfo.bClampPitch = bClampPitch
	self.AttachInfo.PitchClampAngleMin = PitchClampAngleMin or 0
	self.AttachInfo.PitchClampAngleMax = PitchClampAngleMax or 0
	self.AttachInfo.FaceToMoveDirectionTolerance = FaceToMoveDirectionTolerance
end

---切换VirtualSocket移动模式【3】:使用贝塞尔插值,使用一阶贝塞尔，P1点为自身位置，P2点通过速度和方向计算，P3点为目标位置，Z轴不参与贝塞尔计算，直接通过FInterpTo 以 BezierLagSpeed 为速度计算
---【小龙移动功能定制】 bWithInCircle可开启是否在计算贝塞尔之前 开启圆形范围限制，以目标挂点（AttachLoc）为中心为圆心，挂目标点（AttachLoc） 到 Virtual挂点（SocketLoc）摇臂长度为半径，在圆形范围内以VInterpTo 插值
---@param BezierStep number 插值单步距离 （0 ~ 1） 1 为 瞬间插值到目标点
---@param BezierStepMaxDist number 计算P2点位时，限制最大距离
---@param BezierSpeedAlpha number 计算P2点位时，速度影响P2离开P1距离的系数
---@param BezierLagSpeed number Z轴插值速度
---@param BezierDirectionMultiplier number 计算P2点位时，P2离开P1距离的系数
---@param bWithInCircle number 是否开启圆形范围限制
---@param InCircleLagSpeed number 在圆形范围内移动插值速度
---@param InCircleLagTolerance number 圆形范围限制容忍范围（距离）
function ViewControlAttachComponent:SetVirtualSocketToBezierMovementMode(BezierStep,BezierStepMaxDist,BezierSpeedAlpha,
																		 BezierLagSpeed,BezierDirectionMultiplier,bWithInCircle,
																		 InCircleLagSpeed,InCircleLagTolerance)
	self.AttachInfo.LocationUpdateMode = bWithInCircle and ELocationUpdateMode.Bezier_WithInCircleLag or ELocationUpdateMode.Bezier

	self.AttachInfo.BezierStep = BezierStep
	self.AttachInfo.BezierStepMaxDist = BezierStepMaxDist
	self.AttachInfo.BezierSpeedAlpha = BezierSpeedAlpha
	self.AttachInfo.BezierLagSpeed = BezierLagSpeed
	self.AttachInfo.BezierDirectionMultiplier = BezierDirectionMultiplier

	if bWithInCircle then
		self.AttachInfo.InCircleLagSpeed = InCircleLagSpeed
		self.AttachInfo.InCircleLagTolerance = InCircleLagTolerance
	end
end

---开启VirtualSocket挂接点稳定功能, 定义一个以圆心在StabilizePointOffset相对MeshRoot点的位置，半径为StabilizeTolerance的球状范围
---如果StabilizePointBoneName目标骨骼点在范围内，则挂接点会插值到在圆心位置，插值速度为StabilizeInterpSpeed
---如果骨骼点超出圆心范围，则挂点会插值到骨骼位置，插值速度为StabilizeInterpOutSpeed
---@param bEnableStabilize boolean
---@param StabilizeTolerance number 稳定范围
---@param StabilizeInterpSpeed number 进入稳定范围后插值速度
---@param StabilizeInterpOutSpeed number 离开稳定范围后插值速度
---@param StabilizePointBoneName string 稳定目标骨骼
---@param StabilizePointOffsetX number 稳定点偏移X
---@param StabilizePointOffsetY number 稳定点偏移Y
---@param StabilizePointOffsetZ number 稳定点偏移Z
function ViewControlAttachComponent:EnableVirtualSocketStabilization(bEnableStabilize,StabilizeTolerance,StabilizeInterpSpeed,
																  StabilizeInterpOutSpeed,StabilizePointBoneName,
																  StabilizePointOffsetX,StabilizePointOffsetY,StabilizePointOffsetZ)
	self.AttachInfo.bEnableStabilize = bEnableStabilize
	self.AttachInfo.StabilizeTolerance = StabilizeTolerance
	self.AttachInfo.StabilizeInterpSpeed = StabilizeInterpSpeed
	self.AttachInfo.StabilizeInterpOutSpeed = StabilizeInterpOutSpeed
	self.AttachInfo.StabilizePointBoneName = StabilizePointBoneName
	self.AttachInfo.StabilizePointOffset.X = StabilizePointOffsetX
	self.AttachInfo.StabilizePointOffset.Y = StabilizePointOffsetY
	self.AttachInfo.StabilizePointOffset.Z = StabilizePointOffsetZ
end

---切换VirtualSocket旋转模式【2】:匀速自旋转
---@param RotateRoundTime number 圈速
---@param bAbsoluteRotate boolean true:绝对旋转，false:相对旋转
---@return AttachInfoParamsBuilder
function ViewControlAttachComponent:SetRotationUpdateToAutoRotate(RotateRoundTime, bAbsoluteRotate)
	self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.AutoRotate
	self.AttachInfo.bEnableAttachRotationLag = true
	self.AttachInfo.RotateRoundTime = RotateRoundTime
	self.AttachInfo.bAbsoluteRotate = bAbsoluteRotate or false
end

---切换VirtualSocket移动模式【2】:VInterpTo插值,可限制最大距离防止拖拽距离过长
---@param AttachLagSpeed number 插值速度
---@param AttachLagMaxDistance number 最大拖拽长度
function ViewControlAttachComponent:SetVirtualSocketToLocationInterpMode(AttachLagSpeed,AttachLagMaxDistance)
	self.AttachInfo.LocationUpdateMode = ELocationUpdateMode.Interp
	self.AttachInfo.AttachLagSpeed = AttachLagSpeed
	self.AttachInfo.AttachLagMaxDistance = AttachLagMaxDistance
end

---切换VirtualSocket移动模式【1】:漂浮模式，在FloatMaxHeight，FloatMinHeight 范围内上下移动，插值模式固定为EaseInOut,可指定Exp，和插值时间FloatTime
---@param FloatTime number 插值时间
---@param FloatMaxHeight number 漂浮最高相对高度
---@param FloatMinHeight number 插值最低相对高度
---@param FloatEaseExp number 插值Exp
function ViewControlAttachComponent:SetVirtualSocketToFloatMode(FloatTime,FloatMaxHeight,FloatMinHeight,FloatEaseExp)
	self.AttachInfo.LocationUpdateMode = ELocationUpdateMode.FloatUpDown
	self.AttachInfo.FloatTime = FloatTime
	self.AttachInfo.FloatMaxHeight = FloatMaxHeight
	self.AttachInfo.FloatMinHeight =FloatMinHeight
	self.AttachInfo.FloatEaseExp = FloatEaseExp
end

---切换VirtualSocket旋转模式【1】:RInterpTo插值，可限制俯仰角
---@param AttachRotationLagSpeed number RInterpTo插值速度
---@param bClampPitch number 是否开启俯仰角限制
---@param PitchClampAngleMin number 仰角最大角度 Deg
---@param PitchClampAngleMax number 俯角最大角度 Deg
function ViewControlAttachComponent:SetRotationUpdateToInterLag(AttachRotationLagSpeed,bClampPitch,PitchClampAngleMin,PitchClampAngleMax)
	self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.UseDesiredRot
	self.AttachInfo.bEnableAttachRotationLag = AttachRotationLagSpeed
	self.AttachInfo.bAbsoluteRotate = false
	self.AttachInfo.AttachRotationLagSpeed = AttachRotationLagSpeed
	self.AttachInfo.bClampPitch = bClampPitch
	self.AttachInfo.PitchClampAngleMin = PitchClampAngleMin or 0
	self.AttachInfo.PitchClampAngleMax = PitchClampAngleMax or 0
end

---切换VirtualSocket旋转模式【3】:和主人旋转方向保持一致
function ViewControlAttachComponent:SetRotationUpdateToUseOwnerRot()
	self.AttachInfo.RotationUpdateMode =  ERotationUpdateMode.UseOwnerRot
	self.AttachInfo.bEnableAttachRotationLag = true
	self.AttachInfo.bAbsoluteRotate = false
end

---@public
function ViewControlAttachComponent:SetAttachToSimple(AttachReason, OwnerUid, SocketName, Location, Rotation, CompTag)
	if OwnerUid ~= nil then
		if self.bAttached then
			self:DetachFromOwner()
		end
		
		self:InitAttachInfo(OwnerUid,AttachReason,SocketName,CompTag)
		self.AttachInfo.AttachLoc = Location
		self.AttachInfo.AttachRot = Rotation
	end
end

---定制挂接函数，后面有需要往这边加-------------------------------------------------------------------------------------------
---@public
function ViewControlAttachComponent:SetAttachToByModelCfgSocket(OwnerUid, AttachReason, CompTag, bPlayEffect, bEnableLag)
    local ModelData = Game.ActorAppearanceManager.AvatarModelLib[self.ModelID]
    if ModelData then
		if self.bAttached then
			self:DetachFromOwner()
		end
		
		self:InitAttachInfo(OwnerUid, AttachReason, ModelData.SlotName, CompTag)
		-- todo 优化 modelLib配置方式 Offset
		self.AttachInfo.AttachLoc = ModelData.Offset:GetTranslation()
		self.AttachInfo.AttachRot = ModelData.Offset:GetRotation():Rotator()
		self.AttachInfo.bPlayEffect = bPlayEffect
		self.AttachInfo.bSyncOwnerMaterial = true
		self.AttachInfo.bEnableAttachJoint = bEnableLag
		self:SetVirtualSocketToLocationInterpMode(12,30)
    end
end

---@public
function ViewControlAttachComponent:SetAttachToByModelCfgHitch(OwnerUid, AttachReason, CompTag, bPlayEffect)
    local ModelData = Game.ActorAppearanceManager.AvatarModelLib[self.ModelID]
    if ModelData then
		if self.bAttached then
			self:DetachFromOwner()
		end
		
		self:InitAttachInfo(OwnerUid, AttachReason, ModelData.HitchParentBone,CompTag)
		-- todo 优化 modelLib配置方式 Offset
		self.AttachInfo.AttachLoc = ModelData.HitchOffset:GetTranslation()
		self.AttachInfo.AttachRot = ModelData.HitchOffset:GetRotation():Rotator()
		self.AttachInfo.bPlayEffect = bPlayEffect
		self.AttachInfo.bSyncOwnerMaterial = true
    end
end

---@public
function ViewControlAttachComponent:SetAttachToForMinDragon(OwnerUid)
	if self.bAttached then
		self:DetachFromOwner()
	end
	
	self:InitAttachInfo(OwnerUid, WorldViewConst.ATTACH_REASON.MinDragon, "Root",nil)
	self.AttachInfo.AttachLoc = FVector(0, 0, self.MinDragonConst.OffsetZ)
	self.AttachInfo.AttachRot = FRotator(0, 0, 0)
	self.AttachInfo.bSyncOwnerMaterial = true
	
	local MinDragonConst = ViewControlAttachComponent.MinDragonConst
	self:EnableVirtualSocket(true, MinDragonConst.X,MinDragonConst.Y, MinDragonConst.Z, MinDragonConst.Pitch, MinDragonConst.Yaw ,MinDragonConst.Roll, true ,MinDragonConst.CollisionProbSize)
	self:SetVirtualSocketToBezierMovementMode(MinDragonConst.BezierStep ,MinDragonConst.BezierStepMaxDist ,MinDragonConst.BezierSpeedAlpha, MinDragonConst.BezierLagSpeed,MinDragonConst.BezierDirectionMultiplier,MinDragonConst.bWithInCircle, MinDragonConst.InCircleLagSpeed, MinDragonConst.InCircleLagTolerance)
	self:SetRotationUpdateToFaceToModeDir(MinDragonConst.AttachRotationLagSpeed, MinDragonConst.FaceToMoveDirectionTolerance,MinDragonConst.bClampPitch, MinDragonConst.PitchClampAngleMin, MinDragonConst.PitchClampAngleMax)
end


--function ViewControlAttachComponent:SetAttachToForBubbleEffect(AttachReason ,OwnerEid, OffsetX, OffsetY, OffsetZ, SocketName, StabilizePointBoneName,StabilizePointOffsetX,StabilizePointOffsetY,StabilizePointOffsetZ)
--	if self.bAttached then
--		self:DetachFromOwner()
--	end
--	
--	self:InitAttachInfo(OwnerEid, AttachReason, SocketName, nil)
--	self.AttachInfo.AttachLoc = FVector(OffsetX,OffsetY,OffsetZ)
--	self:EnableVirtualSocket(true, 0,0, 0, 0, 0 ,0, false ,0)
--	self:EnableVirtualSocketStabilization(true ,90,2, 20,StabilizePointBoneName, StabilizePointOffsetX,StabilizePointOffsetY,StabilizePointOffsetZ)
--	self:SetRotationUpdateToUseOwnerRot()
--end


--function ViewControlAttachComponent:SetAttachToForMinDragonEffect(AttachReason ,OwnerEid, OffsetX, OffsetY, OffsetZ, RotPitch,RotYaw,RotRoll,SocketName)
--	if self.bAttached then
--		self:DetachFromOwner()
--	end
--	
--	self:InitAttachInfo(OwnerEid, AttachReason, SocketName, nil)
--	self.AttachInfo.AttachLoc = FVector(OffsetX, OffsetY, OffsetZ)
--	self.AttachInfo.AttachRot = FRotator(RotPitch,RotYaw,RotRoll)
--	self.AttachInfo.bEnableAttachJoint = true
--	self:SetRotationUpdateToAutoRotate(10, false)
--end

function ViewControlAttachComponent:SetAttachToForFoolCard(AttachReason ,OwnerUid, OffsetX, OffsetY, OffsetZ, RotPitch,RotYaw,RotRoll,SocketName)
	if self.bAttached then
		self:DetachFromOwner()
	end
	
	self:InitAttachInfo(OwnerUid, AttachReason, SocketName, nil)
	self.AttachInfo.AttachLoc = FVector(OffsetX, OffsetY, OffsetZ)
	self.AttachInfo.AttachRot = FRotator(RotPitch,RotYaw,RotRoll)
	self.AttachInfo.bEnableAttachJoint = true
	self:SetRotationUpdateToAutoRotate(Enum.ERoleMechanismConstData.FOOL_CARD_LAPTIME, true)
end

-----@public
--function ViewControlAttachComponent:SetAttachToForWitch(OwnerEid, BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ, BindRotationYaw, ArmLen)
--	if self.bAttached then
--		self:DetachFromOwner()
--	end
--	
--	self:InitAttachInfo(OwnerEid, Enum.EAttachReason.Witch, "Root", nil)
--	self.AttachInfo.AttachLoc = FVector(BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ)
--	self.AttachInfo.AttachRot = FRotator(0,BindRotationYaw,0)
--	self:EnableVirtualSocket(true, -ArmLen,0, 0,0,0,0, true,80)
--	self:SetVirtualSocketToLocationInterpMode(1.5,1500)
--	self:SetRotationUpdateToFaceToModeDir(2,10,true,-15,15)
--end


---@private
function ViewControlAttachComponent:CreateAttachJointComponent()
	local componentID = self.CppEntity:KAPI_Actor_AddComponentByClass(AttachJointComponent_V2)
	self.HasAttachJointComp = componentID ~= KG_INVALID_ID
end

---@Private
function ViewControlAttachComponent:UECompAttachByVirtualSocket(UESceneComponentID, VSocketID, Transform) 
	self.CppEntity:KAPI_AttachJoint_AddAttachComponent(UESceneComponentID, VSocketID, Transform)
end

---@Private
---Add An AttachSocket By Config
function ViewControlAttachComponent:ForceSocketUpdate(SocketID,bEnableForceUpdate)
	self.CppEntity:KAPI_AttachJoint_ForceSocketUpdate(SocketID,bEnableForceUpdate)
end

------------------------------------------------------------------------------------------------------------------------

---@public
---Detach From The Attach Owner
function ViewControlAttachComponent:DetachFromOwner()
    if not self.AttachInfo or not self.bInWorld then
        return
    end
	
	Game.MaterialManager:RemoveAllInheritMaterial(self.CharacterID)
	
	local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerUID)
	
	
    if self.AttachInfo.bEnableAttachJoint then
        --VirtualSocket 挂接清理
        if self.AttachInfo.AttachID and self.AttachInfo.AttachVirtualSocketID and Owner then
			Owner.CppEntity:KAPI_VirtualAttach_RemoveAttachmentWithRelation(self.CharacterID, self.AttachInfo.AttachID, self.AttachInfo.AttachVirtualSocketID)
        end
    else
        --UE引擎原生挂接清理
		self.CppEntity:KAPI_Attach_DetachFromActorWithRelation(EDetachmentRule.KeepWorld, EDetachmentRule.KeepWorld, EDetachmentRule.KeepWorld)
    end

	self.CppEntity:KAPI_Actor_RemoveAttachFromParent()
	
    --清理被挂接方状态及挂点
    if Owner and Owner.RemoveRecordedAttachEntity then
        Owner:RemoveRecordedAttachEntity(self:uid())
    end

    --退出挂接移动模式
    if self.ChangeAttachMode and not self.AttachInfo.bSkipChangeAttachMode then
        self:ChangeAttachMode(false)
    end

    --清理消息绑定
	self:RemoveAttachOwnerListener()

    --移除绑定标记
	self.bAttached = false

	-- 定位武器销毁原因
	if self.AttachInfo and self.AttachItemReason == WorldViewConst.ATTACH_REASON.Weapon then
		self:DebugFmt("ViewControlAttachComponent:ExitWorld AttachItem: uid：%s	AttachOwnerUID:%s	ManagerEntityID:%s", self:uid(), self.AttachInfo.AttachOwnerUID, self.ManagerEntity or "nil")
	end
end

---@public
---@return number EntityID of the Item
---Get Attach Manager Entity
function ViewControlAttachComponent:GetAttachItemManagerEntity()
    return self.ManagerEntity
end

---@public
---@return number EntityID of the Item
---Set Attach Manager Entity
function ViewControlAttachComponent:SetAttachItemManagerEntity(EntityID)
    self.ManagerEntity = EntityID
end
--endregion  Basic Attach Functions

---@private
---@param AttachItemType number Use Enum EAttachReason
---@param ModelID string the Model Id of the attach item
---Find an attach item from the pending remove list with the given modelID, return the attach entity if exists, otherwise Create a new one.
function ViewControlAttachComponent:CreateAttachItem(AttachItemType,ModelID,ForceRequestReason)
	local ModelData = Game.ActorAppearanceManager.AvatarModelLib[ModelID]
	if not ModelData then
		return nil
	end
	
	local ViewCreateTag
	local AttachClsName = ""
	local bStaticMesh = nil
	local bSimpleAttach = true
	
	-- 挂接物 模型分类
	if ModelData.SMMesh and StringValid(ModelData.SMMesh.StaticMesh) then
		bStaticMesh = true
	elseif ModelData.SKMesh and ModelData.SKMesh.SkeletalMesh then
		bStaticMesh = false
		if StringValid( ModelData.AnimData.AnimClass) then
			bSimpleAttach = false
		end
	end
	
	-- 挂接物 Entity 分类
	if AttachItemType == WorldViewConst.ATTACH_ITEM_TYPE.Weapon then
		ViewCreateTag = VIEW_CREATE_LIMIT_TAG.WEAPON
		AttachClsName = "LocalWeaponItem"
	else
		ViewCreateTag = VIEW_CREATE_LIMIT_TAG.AttachItem
		if bStaticMesh == false then
			AttachClsName = bSimpleAttach and "SimpleAnimAttachItem" or "AttachItem"
		else
			AttachClsName = "AttachItemBase"
		end
	end
	
	local NewEntity
	local OwnUid = self:uid()
	local PropData = { ModelID = ModelID, OwnerID=OwnUid, InstigatorID=OwnUid}
	if ViewCreateTag then
		 NewEntity = Game.WorldManager.ViewBudgetMgr:RequestViewCreateToken(
			 ViewCreateTag,  self, nil, ForceRequestReason, AttachClsName, PropData
		 )
	else
		NewEntity = Game.EntityManager:CreateLocalEntity(AttachClsName, PropData)
	end

	if NewEntity then
		-- todo  直接使用InstigatorID
		NewEntity:SetAttachItemManagerEntity(self:uid())
		NewEntity.bIsStaticMesh = bStaticMesh or false
		NewEntity.bIsSimpleAnim = bSimpleAttach
		return NewEntity
	end
	
	return nil
end

function ViewControlAttachComponent:GetAttachEntitiesByAttachReason(AttachReason)
	if self.ChildEntitiesByReason == nil then
		return nil
	end
	local Results = nil
	for uid, Reason in pairs(self.ChildEntitiesByReason) do
		if Reason == AttachReason then
			if Results == nil then
				Results = {}
			end
			table.insert(Results,uid)
		end
	end
    return Results
end

--TODO:@daiwei
function ViewControlAttachComponent:GetSyncMaterialAttachEntitiesByType(AttachReason)
	local Results = {}
	if self.ChildEntitiesByReason ~= nil then
		for uid, ItemType in pairs(self.ChildEntitiesByReason) do
			if ItemType == AttachReason then
				local Entity = Game.EntityManager:getEntity(uid)
				if Entity and Entity.AttachInfo and Entity.AttachInfo.bSyncOwnerMaterial then
					table.insert(Results,uid)
				end
			end
		end
	end
	return Results
end

-- 激活灵体材质半透效果
function ViewControlAttachComponent:ActiveStealthMaterial()
	self.MaterialChangeID = self:ChangeMaterialSimple(ViewResourceConst.VisibleControl.SemiTransparentMaterialPath)
	self:OverrideCustomDepth(CUSTOM_DEPTH_LOGIC_TYPE.Stealth,true,0)
	self.MaterialOverride = true
	self:SyncMaterialToParentAndChild("ActiveStealthMaterial")
end

-- 取消激活灵体材质半透效果
function ViewControlAttachComponent:DeActiveStealthMaterial()
	if self.MaterialChangeID then
		self:RevertMaterial(self.MaterialChangeID)
	end
	self.MaterialOverride = false
	self:RemoveCustomDepthOverride(CUSTOM_DEPTH_LOGIC_TYPE.Stealth)
	self:SyncMaterialToParentAndChild("DeActiveStealthMaterial")
end

function ViewControlAttachComponent:SyncMaterialToParent(funName)
	-- 向上传递
	if self.AttachInfo and self.AttachInfo.bSyncMaterialChangeToParent then
		local Entity = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerUID)
		if Entity then
			Entity[funName](Entity)
		end
	end
end

function ViewControlAttachComponent:SyncMaterialToParentAndChild(funName)
	-- 向上传递
	self:SyncMaterialToParent(funName)
	-- 向下传递
	if self.ChildEntitiesByReason ~= nil then
		for uid, ItemType in pairs(self.ChildEntitiesByReason) do
			local Entity = Game.EntityManager:getEntity(uid)
			-- 避免传播循环
			if Entity and Entity.AttachInfo and Entity.AttachInfo.bSyncOwnerMaterial and not Entity.AttachInfo.bSyncMaterialChangeToParent then
				Entity[funName](Entity)
			end
		end
	end
end

---@public
---Remove the attached item by specific type and entity id.
---@param AttachType number Use Enum.EAttachReason
---@param Ineid number the id of the attach entit
function ViewControlAttachComponent:RemoveAttachItemsByEntityID_V2(AttachItemType,InUid)
	if not self.CreatedAttachItemsByType or not self.CreatedAttachItemsByType[AttachItemType] then
		return
	end

    for K, uid in pairs(self.CreatedAttachItemsByType[AttachItemType]) do
        if uid == InUid then
            local Entity = Game.EntityManager:getEntity(InUid)
            if Entity then
                Entity:ClearAttachState()
				Entity:destroy()
            end
			self.CreatedAttachItemsByType[AttachItemType][K] = nil
			break
        end
    end
end

---@public
---@param AttachType number Use Enum EAttachReason
---Remove all the attached entity under the given attach type
function ViewControlAttachComponent:RemoveAttachItems_V2(AttachItemType)
	if not self.CreatedAttachItemsByType then return end
	
    if self.CreatedAttachItemsByType[AttachItemType] then
        for K, uid in pairs( self.CreatedAttachItemsByType[AttachItemType]) do
            local Entity = Game.EntityManager:getEntity(uid)
            if Entity then
                Entity:DetachFromOwner()
				Entity:destroy()
            end
        end
		self.CreatedAttachItemsByType[AttachItemType] = nil
    end
end

---@public
---@param AttachType number Use Enum EAttachReason
---@return table Array of the Attach item Uids
---Get all attached entity under this attach type
function ViewControlAttachComponent:GetAttachItemsByType(AttachItemType)
    return self.CreatedAttachItemsByType and self.CreatedAttachItemsByType[AttachItemType] or nil
end

function ViewControlAttachComponent:FindAttachedEntityByTag(Tag)
	if not self.ChildEntitiesByTag or not self.ChildEntitiesByTag[Tag] then
		return nil
	end

	-- 理论上来说现在支持存储多个 相同 Tag 挂接物,不过现在应用场景 相同 Tag 就只会有一个挂接物,所以先返回第一个,后续扩充需要动作库那边一起迭代
	return self.ChildEntitiesByTag[Tag][1]
end

---@public
---@param AttachType number Use Enum EAttachReason
---@param ModelID string The modelID of the attach item
---@return table the attach entity
---Add a New Attach Item
function ViewControlAttachComponent:AddAttachment_V2(AttachItemType,ModelID,OptionalTags,ForceRequestReason)
    local Entity = self:CreateAttachItem(AttachItemType,ModelID,ForceRequestReason)
	if not Entity then
		return
	end

	self.CreatedAttachItemsByType = self.CreatedAttachItemsByType or {}
    self.CreatedAttachItemsByType[AttachItemType] = self.CreatedAttachItemsByType[AttachItemType] or {}
    table.insert(self.CreatedAttachItemsByType[AttachItemType],Entity:uid())

	Entity.AttachItemType = AttachItemType

	if OptionalTags then
		self.ChildEntitiesByTag = self.ChildEntitiesByTag or {}
		
		for _,Tag in ipairs(OptionalTags) do
			Entity:AddTag(Tag)
			self.ChildEntitiesByTag[Tag] = self.ChildEntitiesByTag[Tag] or {}
			table.insert(self.ChildEntitiesByTag[Tag], Entity:uid())
		end
	end

    return Entity
end

---@public
function ViewControlAttachComponent:HideAttachItemByType(AttachItemType, bVisible, Reason)
    --self:Debug("HideAttachItemByType",Type, bVisible, Reason)
    if bVisible and self.HiddenAttachesWithReasons[AttachItemType] then
        self.HiddenAttachesWithReasons[AttachItemType][Reason] = nil
        if next( self.HiddenAttachesWithReasons[AttachItemType]) == nil then
            self.HiddenAttachesWithReasons[AttachItemType] = nil
        end
    elseif not bVisible then
        self.HiddenAttachesWithReasons[AttachItemType] = self.HiddenAttachesWithReasons[AttachItemType] or {}
        if self.HiddenAttachesWithReasons[AttachItemType] then
            self.HiddenAttachesWithReasons[AttachItemType][Reason] = true
        end
    end

    local Items = self:GetAttachItemsByType(AttachItemType)
	if Items ~= nil then
		for K, uid in pairs(Items) do
			local Entity = Game.EntityManager:getEntity(uid)
			if Entity then
				if bVisible then
					Entity:SetActorVisible(Reason)
				else
					Entity:SetActorInVisible(Reason)
				end
			end
		end
	end
end

--endregion Attach Item Functions

--region SpringArm Adjust Functions
---@public
function ViewControlAttachComponent:RefreshSpringArmConfig(BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ,BindRotationP, BindRotationY,BindRotationR, SocketLocDeltaX,SocketLocDeltaY,SocketLocDeltaZ, ArmLen)
    if not self.AttachInfo then
        return
    end

    local Owner = Game.EntityManager:getEntity(self.AttachInfo.AttachOwnerUID)
    if Owner and self.bAttached then
		self.AttachInfo.AttachLoc = FVector(BindLocDeltaX,BindLocDeltaY,BindLocDeltaZ)
		self.AttachInfo.AttachRot = FRotator(BindRotationP, BindRotationY,BindRotationR)
		self.AttachInfo.SocketLocation = FVector(SocketLocDeltaX,SocketLocDeltaY,SocketLocDeltaZ)

        if self.HasAttachJointComp then
            self.CppEntity:KAPI_AttachJoint_ModifyAttachSocket(self.AttachInfo.AttachVirtualSocketID,self.AttachInfo.SocketName, self.AttachInfo.CompTag or "", self.AttachInfo.AttachLoc,self.AttachInfo.AttachRot, self.AttachInfo.SocketLocation, self.AttachInfo.SocketRotation)
        end

        Owner:ApplyAttachJointConfig(self.AttachInfo)
    end
end

--endregion SpringArm Adjust Functions

---@private
function ViewControlAttachComponent:ShouldSyncOwnerMaterial()
	if self.AttachInfo then
		return self.AttachInfo.bSyncOwnerMaterial 
	end
	return false
end

function ViewControlAttachComponent:ShouldSyncMaterialToParent()
	if self.AttachInfo then
		return self.AttachInfo.bSyncMaterialChangeToParent
	end
	return false
end
	
return ViewControlAttachComponent