local AnimLibHelper = kg_require("GamePlay.3C.RoleComposite.AnimLibHelper")
local AnimFeatureBase = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlUtils.Anim.AnimLibFeatureBase")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

---@class AnimLibFeature_AnimListWithAttach
local AnimLibFeature_AnimListWithAttach = DefineClass("AnimLibFeature_AnimListWithAttach", AnimFeatureBase)

function AnimLibFeature_AnimListWithAttach:Reset()
	if self.AttachOperationsMap then
		table.clear(self.AttachOperationsMap)
	else
		self.AttachOperationsMap = {}
	end

	self.AnimPlayReqID = nil
	self.AnimPath = nil
	self.AttachInfos = nil

	self.CurStage = nil
	self.StartStage = nil

	self.ActionBlendRule = nil
	self.FilterBone = nil
	self.FilterBoneDepth = nil
	self.bActionMeshSpaceBlend = nil
	self.AnimStateLoop = nil

	AnimFeatureBase.Reset(self)
end

function AnimLibFeature_AnimListWithAttach:OnInit(StartStage, ActionBlendRule, FilterBone, FilterBoneDepth, bActionMeshSpaceBlend)
	local AnimLibData = self.AnimLibData
	self.AttachInfos = AnimLibData.AttachItems

	self.CurStage = nil
	if StartStage == "" then
		StartStage = nil
	end
	self.StartStage = StartStage or AnimLibData.StartWith

	self.ActionBlendRule = ActionBlendRule
	self.FilterBone = FilterBone
	self.FilterBoneDepth = FilterBoneDepth
	self.bActionMeshSpaceBlend = bActionMeshSpaceBlend
	self.AnimStateLoop = nil

	if self.AttachInfos then
		for K,AttachItemData in pairs(self.AttachInfos) do
			if AttachItemData.AttachItemType == Enum.EAnimLibAttachType.SelfAnimAttachAddOn then
				local AttachItem = self:CreateFeatureTempAttachItem(AttachItemData.ModelID,{AttachItemData.ModelID,K})
				if AttachItem then
					AttachItem:SetAttachToByModelCfgSocket(self.OwnerID,WorldViewConst.ATTACH_REASON.AnimLib)
					AttachItem:ApplyAttach()
					AttachItem:SetActorInVisible(Enum.EInVisibleReasons.AnimLibFeature)
				end
				--self.bNeedSync = self.bNeedSync or AttachItemData.bSyncAttachAnim
			elseif AttachItemData.AttachItemType == Enum.EAnimLibAttachType.EffectAttachAddOn then

			end
		end
	end

	for _, AnimData in pairs(self.AnimLibData.AnimItems) do
		self.LoadAnimPaths[#self.LoadAnimPaths + 1] = AnimLibHelper.GetAnimAssetPathFromAnimID(AnimData.Anim)
	end
	self:PreLoadAnimList(self.LoadAnimPaths)
end

function AnimLibFeature_AnimListWithAttach:Run()
	self:RunNext()
end

function AnimLibFeature_AnimListWithAttach:RunNext()
	local AnimLibData = self.AnimLibData
	local bIsFirst = false
	local bIsLast = false
	if self.CurStage == nil then
		bIsFirst = true
		self.CurStage = self.StartStage
	else
		local AnimItems = AnimLibData.AnimItems
		local ItemData = AnimItems[self.CurStage]
		if ItemData and ItemData.NextPhase and AnimItems[ItemData.NextPhase] then
			self.CurStage = ItemData.NextPhase
		elseif self.bLoop then
			self.CurStage = AnimLibData.StartWith
		else
			return AnimFeatureBase.OnMontageStop(self, false)
		end
	end

	self:RemoveAllOperations()

	local CurAnimData = AnimLibData.AnimItems[self.CurStage]
	if not CurAnimData then
		local AnimID = nil
		local Owner = Game.EntityManager:getEntity(self.OwnerID)
		if Owner then
			AnimID = Owner:GetAnimDataID()
		end
		Log.ErrorFormat("AnimLib: AnimData:%s may not has Stage:%s AnimID:%s", self.AnimAssetID, self.CurStage, AnimID)
		if not bIsFirst then
			self:OnMontageStop(true)
		else
			self:Stop(true)
		end
		return
	end
	if CurAnimData.NextPhase == nil then
		bIsLast = true
	end
	self.AnimPath = AnimLibHelper.GetAnimAssetPathFromAnimID(CurAnimData.Anim)

	self.AnimStateLoop = CurAnimData.bLoop
	self.AnimPlayReqID = self:PlayOwnerAnimation(
			self.AnimPlayReqTag,
			self.AnimPath,
			CurAnimData.bLoop,
			bIsFirst and self.BlendInTime or 0.033,
			bIsLast and self.BlendOutTime or 0.033,
			not (bIsLast and not self.bLoop) or self.bAutoStop,
			self.bRefreshViewWhenSameMontage,
			false,
			self.AttachInfos and next(self.AttachInfos) ~= nil or false,
			false,
			nil,
			self.ActionBlendRule,
			self.FilterBone,
			self.FilterBoneDepth,
			self.bActionMeshSpaceBlend
	)

	if self.AnimPlayReqID == nil then
		if not bIsFirst then
			self:OnMontageStop(true)
		else
			self:Stop(true)
		end
		return
	end

	if self.AttachInfos then
		for K, AttachItemData in pairs(self.AttachInfos) do
			self:ActivateAttachOperation(K, AttachItemData)
		end
	end
end

function AnimLibFeature_AnimListWithAttach:ActivateAttachOperation(ItemKey,AttachItemData)
	if AttachItemData.AttachItemType == Enum.EAnimLibAttachType.SelfAnimAttachAddOn then
		if AttachItemData.StateAnimTypes[self.CurStage] then
			local AttachItem = self:QueryItemByTag(Enum.EFeatureQueryType.AttachItem,ItemKey)
			self.AttachOperationsMap[ItemKey] = self:RunOperation(Enum.EAnimFeatureOperations.AttachAnim,
					AttachItem,
					AttachItemData.StateAnimTypes[self.CurStage],
					self.AnimStateLoop,self.BlendInTime,self.BlendOutTime,false,self.PlayRate,self.StartTime,true,self.AnimPlayReqID)
		end
	elseif AttachItemData.AttachItemType == Enum.EAnimLibAttachType.EffectAttachAddOnthen then
		self.AttachOperationsMap[ItemKey]  = self:RunOperation(Enum.EAnimFeatureOperations.Effect,self.OwnerID,AttachItemData)
	elseif AttachItemData.AttachItemType == Enum.EAnimLibAttachType.WorldAttachAddOn then
		local Item = self:QueryItemByTag(Enum.EFeatureQueryType.ExternObjects,AttachItemData.WorldAttachItemTag)
		if Item then
			self.AttachOperationsMap[ItemKey] = self:RunOperation(Enum.EAnimFeatureOperations.AttachToOwner,Item,self.OwnerID,AttachItemData)
		end
	end
end

function AnimLibFeature_SingleSequenceWithAttach:DeActivateAttachOperation(ItemKey,AttachItemData)
	if 	self.AttachOperationsMap[ItemKey] then
		self:EndOperation(self.AttachOperationsMap[ItemKey])
		self.AttachOperationsMap[ItemKey] = nil
	end
end

function AnimLibFeature_AnimListWithAttach:ManualProgressToNext(bFinishCurrentLoop)
	local CurAnimData = self.AnimLibData.AnimItems[self.CurStage]
	if CurAnimData.bLoop and bFinishCurrentLoop then
		local Owner = Game.EntityManager:getEntity(self.OwnerID)
		if Owner and self.AnimPlayReqID then
			Owner:BreakLoopingAnimation(self.AnimPlayReqID)
			return
		end
	end

	self:RunNext()
end

function AnimLibFeature_AnimListWithAttach:Stop(bSkipCleanUpAnim, BlendTime)
	self:RemoveAllOperations()

	AnimFeatureBase.Stop(self, bSkipCleanUpAnim, BlendTime)
end

function AnimLibFeature_AnimListWithAttach:OnMontageStop(bInterrupt)
	if bInterrupt then
		AnimFeatureBase.OnMontageStop(self, bInterrupt)
	else
		self:RunNext()
	end
end

function AnimLibFeature_AnimListWithAttach:GetPlayingAnimInfo()
	return self.AnimAssetID, self.CurStage
end 