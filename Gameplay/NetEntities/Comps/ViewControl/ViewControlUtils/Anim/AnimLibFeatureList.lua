local ULLFunc = import("LowLevelFunctions")

---@class AnimFeatureList
local AnimFeatureList = DefineClass("AnimFeatureList")
AnimFeatureList.Pool = {}
AnimFeatureList.PoolSize = 50
function AnimFeatureList.GetFromPool()
	local NewItem
	if #AnimFeatureList.Pool == 0 then
		NewItem  = AnimFeatureList.new()
	else
		NewItem  = table.remove(AnimFeatureList.Pool, 1)
	end
	NewItem._bInPool = false
	return NewItem
end


function AnimFeatureList.ReturnToPool(Item)
	if #AnimFeatureList.Pool < AnimFeatureList.PoolSize then
		Item._bInPool = true
		Item:Reset()
		table.insert(AnimFeatureList.Pool,Item)
	end
end

function AnimFeatureList:ctor()
	self:Reset()

	self.ParamsNum = nil
	if self.Params then
		table.clear(self.Params)
	else
		self.Params = {}
	end
end

function AnimFeatureList:Reset()
	self.UniqueID = nil

	self.OwnerID = nil
	self.AnimSeq = nil
	self.DurationSeq = nil
	self.bAnimQueueLoop = nil
	self.BlendInTime = nil
	self.BlendOutTime = nil
	self.EndCallBack = nil
	
	self.CurPlayIdx = nil

	self.AnimFeatureID = nil

	if self.RunNextTimer then
		Game.TimerManager:StopTimerAndKill(self.RunNextTimer)
		self.RunNextTimer = nil
	end
end

function AnimFeatureList:StartRunList(OwnerID, AnimList, DurationList, bLoop, BlendInTime, BlendOutTime, EndCBObj, EndCBFuncName, ...)
	self.UniqueID = ULLFunc.GetShortGlobalUniqueID()

	self.OwnerID = OwnerID
	self.AnimSeq = AnimList
	self.DurationSeq = DurationList
	self.bAnimQueueLoop = bLoop
	self.BlendInTime = BlendInTime
	self.BlendOutTime = BlendOutTime
	self.EndCBObj = EndCBObj
	self.EndCBFuncName = EndCBFuncName

	self.ParamsNum = select("#", ...)
	table.clear(self.Params)
	local Params = self.Params
	for i = 1, self.ParamsNum do
		Params[i] = select(i, ...)
	end
 
	if AnimList == nil or next(AnimList) == nil then
		Log.ErrorFormat("[AnimFeatureList:StartRunList] Error:AnimSeq List is empty")
		return
	end

	self.CurPlayIdx = nil
	self:RunNext()
end

function AnimFeatureList:RunNext()
	local bIsFirst

	if self.RunNextTimer then
		Game.TimerManager:StopTimerAndKill(self.RunNextTimer)
		self.RunNextTimer = nil
	end

	if self.CurPlayIdx == nil  then
		self.CurPlayIdx = 1
		bIsFirst = true
	elseif self.CurPlayIdx < #self.AnimSeq then
		self.CurPlayIdx = self.CurPlayIdx + 1
	elseif self.bAnimQueueLoop then
		self.CurPlayIdx =  1
	else
		local EndCallBack = self.EndCallBack
		self:Stop(false)
		if EndCallBack then
			EndCallBack(false)
		end
		return
	end

	local CurPlayIdx = self.CurPlayIdx
	local Owner = Game.EntityManager:getEntity(self.OwnerID)
	if Owner and self.AnimSeq[self.CurPlayIdx] then
		local DurationSeq = self.DurationSeq
		if DurationSeq and DurationSeq[CurPlayIdx] and DurationSeq[CurPlayIdx] >0 then
			self.RunNextTimer = Game.TimerManager:CreateTimerAndStart(
				function()
					self:RunNext()
				end,
				DurationSeq[CurPlayIdx] * 1000, 1
			)
		end

		self.AnimFeatureID = Owner:PlayAnimLibFeatureAsPerformance(
				self.AnimSeq[CurPlayIdx].AssetID,
				self.AnimSeq[self.CurPlayIdx].Stage,
				nil,
				self.BlendInTime,
				self.BlendOutTime,
				true,
				self,
				"OnListStop"
		)

		if not self.AnimFeatureID then
			self:Stop(true)
		end
	end
end

-- 主动结束无回掉
function AnimFeatureList:Stop(bSkipCleanUpAnim)
	if self.RunNextTimer then
		Game.TimerManager:StopTimerAndKill(self.RunNextTimer)
		self.RunNextTimer = nil
	end

	local Owner = Game.EntityManager:getEntity(self.OwnerID)

	if Owner then
		Owner:OnFeatureListEnd(self.UniqueID)

		if not bSkipCleanUpAnim and self.AnimFeatureID then
			Owner:StopAnimLibMontage(self.AnimFeatureID)
			-- 这里会走OnMontageStop回调结束 直接Return
			return
		end
	end

	AnimFeatureList.ReturnToPool(self)
end

-- 结束 + 回调
function AnimFeatureList:OnListStop(bInterrupt)
	local EndCBObj = self.EndCBObj
	local EndCBFuncName = self.EndCBFuncName
	local ParamsNum = self.ParamsNum
	local Params = self.Params
	
	self:Stop(true)

	if EndCBObj and EndCBObj[EndCBFuncName] then
		if ParamsNum > 0 then
			EndCBObj[EndCBFuncName](EndCBObj, bInterrupt, table.unpack(Params, 1, ParamsNum))
		else
			EndCBObj[EndCBFuncName](EndCBObj, bInterrupt)
		end
	end
end 