local AnimLibHelper = kg_require("GamePlay.3C.RoleComposite.AnimLibHelper")
local AnimFeatureBase = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlUtils.Anim.AnimLibFeatureBase")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")

---@class AnimLibFeatureBase_AnimSequence
local AnimLibFeature_SingleSequenceWithAttach = DefineClass("AnimLibFeature_SingleSequenceWithAttach", AnimFeatureBase)


function AnimLibFeature_SingleSequenceWithAttach:Reset()
	self.AnimPath = nil
	self.AttachInfos = nil
	self.AnimPlayReqID = nil

	self.ActionBlendRule = nil
	self.FilterBone = nil
	self.FilterBoneDepth = nil
	self.bActionMeshSpaceBlend = nil

	if self.AttachOperationsMap then
		table.clear(self.AttachOperationsMap)
	else
		self.AttachOperationsMap = {}
	end
	
	AnimFeatureBase.Reset(self)
end

function AnimLibFeature_SingleSequenceWithAttach:OnInit(ActionBlendRule, FilterBone, FilterBoneDepth, bActionMeshSpaceBlend)
	local AnimLibData = self.AnimLibData
	self.AnimPath = AnimLibHelper.GetAnimAssetPathFromAnimID(AnimLibData.Anim)
	self.AttachInfos = AnimLibData.AttachItems
	if self.bLoop == nil then
		self.bLoop = AnimLibData.bLoop
	end
	self.ActionBlendRule = ActionBlendRule
	self.FilterBone = FilterBone
	self.FilterBoneDepth = FilterBoneDepth
	self.bActionMeshSpaceBlend = bActionMeshSpaceBlend

	if self.AttachInfos and next(self.AttachInfos) ~= nil then
		for K,AttachItemData in pairs(self.AttachInfos) do
			if AttachItemData.AttachItemType == Enum.EAnimLibAttachType.SelfAnimAttachAddOn then
				local AttachItem = self:CreateFeatureTempAttachItem(AttachItemData.ItemName,{AttachItemData.ItemName,K})
				if AttachItem then
					AttachItem:SetAttachToByModelCfgSocket(self.OwnerID, WorldViewConst.ATTACH_REASON.AnimLib)
					AttachItem:ApplyAttach()
					AttachItem:SetActorInVisible(Enum.EInVisibleReasons.AnimLibFeature)
					self.bNeedSync = self.bNeedSync or AttachItemData.bSyncAttachAnim
				end
			end
		end
	end
end

function AnimLibFeature_SingleSequenceWithAttach:Run()
	self.AnimPlayReqID = self:PlayOwnerAnimation(
			self.AnimPlayReqTag,
			self.AnimPath,
			self.bLoop,
			self.BlendInTime,
			self.BlendOutTime,
			self.bAutoStop,
			self.bRefreshViewWhenSameMontage,
			false,
			self.AttachInfos and next(self.AttachInfos) ~= nil or false,
			false,
			nil,
			self.ActionBlendRule,
			self.FilterBone,
			self.FilterBoneDepth,
			self.bActionMeshSpaceBlend
	)

	if self.AnimPlayReqID == nil then
		self:Stop(true)
	end

	if self.AttachInfos then
		for K,AttachItemData in pairs(self.AttachInfos) do
			if AttachItemData.bStartWithAnim then
				self:ActivateAttachOperation(K,AttachItemData)
			end
		end
	end
end

function AnimLibFeature_SingleSequenceWithAttach:ActivateAttachOperation(ItemKey,AttachItemData)
	if AttachItemData.AttachItemType == Enum.EAnimLibAttachType.SelfAnimAttachAddOn then
		local AttachItem = self:QueryItemByTag(Enum.EFeatureQueryType.AttachItem,ItemKey)
		self.AttachOperationsMap[ItemKey] = self:RunOperation(Enum.EAnimFeatureOperations.AttachAnim,AttachItem,
			AttachItemData.AnimTypeName,
			self.bLoop,self.BlendInTime,self.BlendOutTime,false,self.PlayRate,self.StartTime,AttachItemData.bSyncAttachAnim,self.AnimPlayReqID)
	elseif AttachItemData.AttachItemType == Enum.EAnimLibAttachType.EffectAttachAddOn then
		self.AttachOperationsMap[ItemKey] =  self:RunOperation(Enum.EAnimFeatureOperations.Effect,self.OwnerID,AttachItemData)
	elseif AttachItemData.AttachItemType == Enum.EAnimLibAttachType.WorldAttachAddOn then
		local Item = self:QueryItemByTag(Enum.EFeatureQueryType.ExternObjects,AttachItemData.WorldAttachItemTag)
		if Item then
			self.AttachOperationsMap[ItemKey] = self:RunOperation(Enum.EAnimFeatureOperations.AttachToOwner,Item,self.OwnerID,AttachItemData)
		end
	end
end


function AnimLibFeature_SingleSequenceWithAttach:DeActivateAttachOperation(ItemKey,AttachItemData)
	if self.AttachOperationsMap[ItemKey] then
		self:EndOperation(self.AttachOperationsMap[ItemKey])
		self.AttachOperationsMap[ItemKey] = nil
	end
end

function AnimLibFeature_SingleSequenceWithAttach:Stop(bSkipCleanUpAnim, BlendTime)
	self:RemoveAllOperations()

	AnimFeatureBase.Stop(self, bSkipCleanUpAnim, BlendTime)
end

function AnimLibFeature_SingleSequenceWithAttach:OnAnimFeatureNotify(AttachItemName, AttachNotifyType)
	if self.AttachInfos and self.AttachInfos[AttachItemName] then
		if not self.AttachInfos[AttachItemName].bStartWithAnim then
			if AttachNotifyType == 1 then
				self:ActivateAttachOperation(AttachItemName,self.AttachInfos[AttachItemName])
			else
				self:DeActivateAttachOperation(AttachItemName,self.AttachInfos[AttachItemName])
			end
		end
	end 
end
