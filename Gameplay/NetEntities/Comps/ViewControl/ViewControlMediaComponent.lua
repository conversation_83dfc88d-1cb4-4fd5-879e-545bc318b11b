local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
local SEARCH_MESH_TYPE = MaterialEffectParamTemplate.SEARCH_MESH_TYPE
local SEARCH_MATERIAL_TYPE = MaterialEffectParamTemplate.SEARCH_MATERIAL_TYPE

---@class ViewControlMediaComponent
ViewControlMediaComponent = DefineComponent("ViewControlMediaComponent")

ViewControlMediaComponent.BinkMediaMaterialSlotNames = {}

function ViewControlMediaComponent:ctor()
    self.currentSystemActionBinkMediaPlayID = nil
end

function ViewControlMediaComponent:PlayRenderToTextureBinkMedia(
        Url, bLoop, bStartImmediately, PlayRate, bAutoStop, OnMediaReachEndCbObj, OnMediaReachEndCbName,
        SearchMeshType, MeshName, SearchMaterialType, MaterialSlotNames)
    
    if not self.bInWorld then
        -- 如果说后续发现enter world前出现change material接着立刻revert material的情况，可以再考虑支持command cache的清理
        local CustomPlayID = Game.MediaManager:GeneratePlayID()
        self:AddCommandCache("internalPlayRenderToTextureBinkMedia", 
                Url, bLoop, bStartImmediately, PlayRate, bAutoStop, OnMediaReachEndCbObj, OnMediaReachEndCbName,
                SearchMeshType, MeshName, SearchMaterialType, MaterialSlotNames, CustomPlayID)
        return CustomPlayID
    end
    
    return self:internalPlayRenderToTextureBinkMedia(
            Url, bLoop, bStartImmediately, PlayRate, bAutoStop, OnMediaReachEndCbObj, OnMediaReachEndCbName,
            SearchMeshType, MeshName, SearchMaterialType, MaterialSlotNames)
end

function ViewControlMediaComponent:StopRenderToTextureBinkMedia(PlayID)
    if not self.bInWorld then
        self:AddCommandCache("StopRenderToTextureBinkMedia", PlayID)
        return
    end
    
    return Game.MediaManager:StopBinkMedia(PlayID)
end

function ViewControlMediaComponent:PlayRenderToTextureBinkMediaBySystemAction(Url, bLoop, MeshTag, MaterialSlotName)
    ViewControlMediaComponent.BinkMediaMaterialSlotNames[1] = MaterialSlotName
    self.currentSystemActionBinkMediaPlayID = self:PlayRenderToTextureBinkMedia(
            Url, bLoop, true, 1.0, true, nil, nil,
            SEARCH_MESH_TYPE.SearchMeshByName, MeshTag, SEARCH_MATERIAL_TYPE.SearchMaterialBySlots, ViewControlMediaComponent.BinkMediaMaterialSlotNames
    )
    return self.currentSystemActionBinkMediaPlayID
end

function ViewControlMediaComponent:StopRenderToTextureBinkMediaBySystemAction()
    if self.currentSystemActionBinkMediaPlayID ~= nil then
        self:StopRenderToTextureBinkMedia(self.currentSystemActionBinkMediaPlayID)
        self.currentSystemActionBinkMediaPlayID = nil
    end
end

function ViewControlMediaComponent:internalPlayRenderToTextureBinkMedia(
        Url, bLoop, bStartImmediately, PlayRate, bAutoStop, OnMediaReachEndCbObj, OnMediaReachEndCbName,
        SearchMeshType, MeshName, SearchMaterialType, MaterialSlotNames, CustomPlayID)

    return Game.MediaManager:PlayRenderToTextureBinkMedia(
            Url, bLoop, bStartImmediately, PlayRate, bAutoStop, OnMediaReachEndCbObj, OnMediaReachEndCbName,
            self.CharacterID, SearchMeshType, MeshName, SearchMaterialType, MaterialSlotNames, CustomPlayID)
end

return ViewControlMediaComponent
