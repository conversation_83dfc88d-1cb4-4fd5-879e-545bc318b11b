local UBSFunc = import("BSFunctionLibrary")
local ERelativeTransformSpace = import("ERelativeTransformSpace")
local EPropertyClass = import("EPropertyClass")
local NewHeadInfoConst = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoConst")

local const = kg_require("Shared.Const")
local MaterialEffectConst = kg_require("Gameplay.Effect.MaterialEffectConst")
local USkeletalMeshComponent = import("SkeletalMeshComponent")
local StaticMeshComponent = import("StaticMeshComponent")
local NIAGARA_EFFECT_TAG = const.NIAGARA_EFFECT_TAG
local NIAGARA_SOURCE_TYPE = const.NIAGARA_SOURCE_TYPE
local NIAGARA_HIDDEN_REASON = const.NIAGARA_HIDDEN_REASON
local NIAGARA_ATTACH_COMPONENT_TYPE = const.NIAGARA_ATTACH_COMPONENT_TYPE
local DISSOLVE_MATERIAL_PARAMS = MaterialEffectConst.DISSOLVE_MATERIAL_PARAMS
local DISSOLVE_MATERIAL_EFFECT_TYPE = MaterialEffectConst.DISSOLVE_MATERIAL_EFFECT_TYPE

local next = next

---------------------------------
--- ViewControl 特效接口
--------------------------------
ViewControlFxComponent = DefineComponent("ViewControlFxComponent")

ViewControlFxComponent.AttachEffectData_SpawnTrans = M3D.Transform()
ViewControlFxComponent.MeshVisibility_ComponentTypes = slua.Array(EPropertyClass.Str)
ViewControlFxComponent.MeshVisibility_ComponentTypes:Add("StaticMeshComponent")
ViewControlFxComponent.MeshVisibility_ComponentTypes:Add("SkeletalMeshComponent")
ViewControlFxComponent.MeshVisibility_ComponentTypes:Add("SkeletalMeshComponentBudgeted")  -- todo 这个后面拆MeshVisibility的task的时候一起处理掉, 要使用UMeshComponent的IsA方式 @刘丰
ViewControlFxComponent.MeshVisibility_ComponentTypes:Add("SkinnedMeshComponent")
ViewControlFxComponent.__Tmp_Socket_Names = slua.Array(EPropertyClass.Name) -- luacheck: ignore

-- region Important
function ViewControlFxComponent:ctor()
    self.DecalRecords = {}
	self.DecalEntities = {}

    self.MeshGIDs = {}
    self.MeshComps = {}
    self.TaskGIDToMeshGID = {}
    self.HideHeadInfoTasks = nil
	
	self.MeshGIDCacheList = {}
	self.MeshTaskDataCacheList = {}
	self.HiddenMeshIDCounts = {}
	
    self.LastPlayTimes = {}
	self.AttachEffectIDs = {}
	
	self._delayEffectTimerIndex = 0
end

function ViewControlFxComponent:dtor()
	-- 没必要清理
    --table.clear(self.DecalRecords)
    --table.clear(self.MeshGIDs)
    --table.clear(self.MeshComps)
    --table.clear(self.TaskGIDToMeshGID)
    --if self.HideHeadInfoTasks and next(self.HideHeadInfoTasks) then
    --    table.clear(self.HideHeadInfoTasks)
    --    self.HideHeadInfoTasks = nil
    --end
	--table.clear(self.MeshGIDCacheList)
	--table.clear(self.MeshTaskDataCacheList)
    --table.clear(self.LastPlayTimes)
end

function ViewControlFxComponent:__component_EnterWorld__()
	for _, GID in ipairs(self.MeshGIDCacheList) do
		self:MeshVisibility(self.MeshTaskDataCacheList[GID], GID)
	end
end

function ViewControlFxComponent:__component_ExitWorld__()
	if self.DelayEffectTimers then
		for K,V in pairs(self.DelayEffectTimers) do
			self:DelTimer(v)
			--Game.TimerManager:StopTimerAndKill(K)
		end
		self.DelayEffectTimers = nil
	end
end

function ViewControlFxComponent:__component_AfterExitWorld__()
    self:DestroyAllNiagaras(true)
	self:DestroyAllDecalEntities()
end
-- endregion Important



--region Niagara
function ViewControlFxComponent:PlayNiagaraEffectAtLocation(EffectPath, SpawnTrans, TotalLifeSeconds, bEngineCulling, SourceType, InstigatorID, bBenefitEffect)
    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    NiagaraEffectParam.NiagaraEffectPath = EffectPath
    NiagaraEffectParam.bNeedAttach = false
	NiagaraEffectParam.bEngineCulling = bEngineCulling

    NiagaraEffectParam.SpawnerEntityId = self:uid()
    NiagaraEffectParam.SpawnerId = self.CharacterID

	NiagaraEffectParam.InstigatorEntityId = InstigatorID or 0
    NiagaraEffectParam.SourceType = SourceType or NIAGARA_SOURCE_TYPE.DEFAULT
	NiagaraEffectParam.bBenefitEffect = bBenefitEffect
    --NiagaraEffectParam.bEnableCustomDepth = self.curEnableCustomDepth
    --NiagaraEffectParam.CustomDepthStencilValue = self.curCustomDepthStencilValue
    M3D.ToTransform(SpawnTrans, NiagaraEffectParam.SpawnTrans)

    if TotalLifeSeconds == nil or TotalLifeSeconds < 0 then
        NiagaraEffectParam.TotalLifeMs = -1
    else
        NiagaraEffectParam.TotalLifeMs = TotalLifeSeconds * 1000
    end

	if not self.bInWorld then
        local EffectID = Game.EffectManager:GenerateEffectId()
        NiagaraEffectParam.CustomEffectID = EffectID
        self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
        return EffectID
    end

    return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

function ViewControlFxComponent:PlayNiagaraEffectAttached(
		EffectPath, AttachPointName, SpawnTrans, AttachCompId, bAbsoluteRotation, bAbsoluteScale, TotalLifeSeconds, bEngineCulling, SourceType, bBenefitEffect)
    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    NiagaraEffectParam.NiagaraEffectPath = EffectPath
    NiagaraEffectParam.bNeedAttach = true
    NiagaraEffectParam.AttachPointName = AttachPointName or ""
    NiagaraEffectParam.bAbsoluteRotation = bAbsoluteRotation or false
    NiagaraEffectParam.bAbsoluteScale = bAbsoluteScale or false
	NiagaraEffectParam.bEngineCulling = bEngineCulling

    NiagaraEffectParam.SpawnerEntityId = self:uid()
    NiagaraEffectParam.SpawnerId = self.CharacterID

    NiagaraEffectParam.SourceType = SourceType or NIAGARA_SOURCE_TYPE.DEFAULT
	NiagaraEffectParam.bBenefitEffect = bBenefitEffect

    if TotalLifeSeconds == nil or TotalLifeSeconds < 0 then
        NiagaraEffectParam.TotalLifeMs = -1
    else
        NiagaraEffectParam.TotalLifeMs = TotalLifeSeconds * 1000
    end

    NiagaraEffectParam.AttachComponentId = AttachCompId ~= nil and AttachCompId or 0
    --NiagaraEffectParam.bEnableCustomDepth = self.curEnableCustomDepth
    --NiagaraEffectParam.CustomDepthStencilValue = self.curCustomDepthStencilValue

    if SpawnTrans ~= nil then
        M3D.ToTransform(SpawnTrans, NiagaraEffectParam.SpawnTrans)
    end

	if not self.bInWorld then
        local EffectID = Game.EffectManager:GenerateEffectId()
        NiagaraEffectParam.CustomEffectID = EffectID
        self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
        return EffectID
    end

    if AttachCompId == nil then
        NiagaraEffectParam.AttachComponentId = self.CppEntity:KAPI_Actor_FxGetComponentBySocket(NiagaraEffectParam.AttachPointName)
    end

    return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

function ViewControlFxComponent:PlayNiagaraEffect(NiagaraEffectParam)
    NiagaraEffectParam.SpawnerEntityId = self:uid()
    NiagaraEffectParam.SpawnerId = self.CharacterID
    --NiagaraEffectParam.bEnableCustomDepth = self.curEnableCustomDepth
    --NiagaraEffectParam.CustomDepthStencilValue = self.curCustomDepthStencilValue

    if NiagaraEffectParam.bNeedAttach and NiagaraEffectParam.AttachComponentName ~= nil and NiagaraEffectParam.AttachComponentName ~= "" and
            self.HasUnloadedDynamicMesh and self:HasUnloadedDynamicMesh(NiagaraEffectParam.AttachComponentName) then
        NiagaraEffectParam.CustomEffectID = Game.EffectManager:GenerateEffectId()
        self:CacheNiagaraParamBefore(NiagaraEffectParam, NiagaraEffectParam.AttachComponentName)
        return NiagaraEffectParam.CustomEffectID
    end
    
	if not self.bInWorld then
        local EffectID = Game.EffectManager:GenerateEffectId()
        NiagaraEffectParam.CustomEffectID = EffectID
        self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
        return EffectID
    end

    -- todo 这个后面转移到 effect manager中
    if NiagaraEffectParam.bNeedAttach and NiagaraEffectParam.AttachComponentId == 0 then
        local CompID
        if NiagaraEffectParam.NiagaraAttachComponentType == NIAGARA_ATTACH_COMPONENT_TYPE.FIND_ATTACH_COMPONENT_BY_COMPONENT_NAME then
			CompID = self.CppEntity:KAPI_Actor_FxGetComponentByName(NiagaraEffectParam.AttachComponentName)
        elseif NiagaraEffectParam.NiagaraAttachComponentType == NIAGARA_ATTACH_COMPONENT_TYPE.FIND_ATTACH_COMPONENT_BY_SOCKET_NAME then
			CompID = self.CppEntity:KAPI_Actor_FxGetComponentBySocket(NiagaraEffectParam.AttachPointName)
        end

        NiagaraEffectParam.AttachComponentId = CompID
    end

    return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

function ViewControlFxComponent:DeactivateNiagaraSystem(EffectId)
    if not self.bInWorld then
        self:AddCommandCache("DeactivateNiagaraSystem", EffectId)
        return
    end

    Game.EffectManager:DeactivateNiagaraSystem(EffectId)
end

function ViewControlFxComponent:DestroyNiagaraSystem(EffectId)
    if not self.bInWorld then
        self:AddCommandCache("DestroyNiagaraSystem", EffectId)
        return
    end

    Game.EffectManager:DestroyNiagaraSystem(EffectId)
end

function ViewControlFxComponent:SetNiagaraDelayDestroy(EffectId, DelayDestroyTimeMs)
    if not self.bInWorld then
        self:AddCommandCache("SetNiagaraDelayDestroy", EffectId, DelayDestroyTimeMs)
        return
    end

    Game.EffectManager:SetNiagaraDelayDestroy(EffectId, DelayDestroyTimeMs)
end

-- luacheck: push ignore
function ViewControlFxComponent:PlayEffectTableNiagara(EffectPath, FollowType, bFollowScale, BoneDock, InLifeSeconds, bBattle, AttachComponentId, bFollowHidden, 
													   Location, Rotation, Scale, bNoBindSpawner, InstigatorEntity, InRootSkillId, EffectPriority)
	-- 所有配置在NiagaraData表中的特效均可算作Skill类型特效
	local CharacterTypeForViewBudget = InstigatorEntity and InstigatorEntity:GetCharacterTypeForViewBudget() or self:GetCharacterTypeForViewBudget()
	local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
		CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL, self.CharacterID, EffectPriority)
	if not bCanPlayNiagara then
		return 0
	end
	
	local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
	NiagaraEffectParam.NiagaraBudgetToken = BudgetToken
	NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL
	NiagaraEffectParam.CustomNiagaraPriority = EffectPriority
	NiagaraEffectParam.NiagaraEffectPath = EffectPath

	if Game.EffectManager:IsCheckNiagaraEffectTypeEnabled() then
		NiagaraEffectParam.SourceSkillIdDebugUse = InRootSkillId and InRootSkillId or self.RootAbilityID
	end
	-- todo:MobileEffectPath先不处理
	--if PlatformUtil.IsMobilePlatform() and effectData.MobileEffectPath ~= "" then
	--    NiagaraEffectParam.NiagaraEffectPath = effectData.MobileEffectPath
	--end
	NiagaraEffectParam.SpawnerEntityId = self:uid()
    NiagaraEffectParam.SpawnerId = self.CharacterID
	-- todo： 增益特效先不处理
	NiagaraEffectParam.bBenefitEffect = false
	if bNoBindSpawner then
		NiagaraEffectParam.bDestroyWhenSpawnerExitWorld = false
	end

	if InstigatorEntity then
		NiagaraEffectParam.InstigatorEntityId = InstigatorEntity:uid()
	end

	if InLifeSeconds == nil or InLifeSeconds < 0 then
		NiagaraEffectParam.TotalLifeMs = -1
	else
		NiagaraEffectParam.TotalLifeMs = InLifeSeconds * 1000
	end
	NiagaraEffectParam.AttachPointName = BoneDock or ""

	if (bFollowHidden == nil) then
		NiagaraEffectParam.bFollowHidden = true
	else
		NiagaraEffectParam.bFollowHidden = bFollowHidden
	end

	if bBattle then
		NiagaraEffectParam.SourceType = NIAGARA_SOURCE_TYPE.BATTLE
	end

	--设置跟随类型
	if (FollowType ~= 0) then
		NiagaraEffectParam.bNeedAttach = true
		NiagaraEffectParam.bAbsoluteScale = not bFollowScale
		if (FollowType == 1) then
			NiagaraEffectParam.bAbsoluteRotation = false
		else
			NiagaraEffectParam.bAbsoluteRotation = true
		end
	else
		NiagaraEffectParam.bAbsoluteScale = true
		NiagaraEffectParam.bAbsoluteRotation = true
	end
	
	local offsetTrans = M3D.Transform()
	if (Location ~= nil or Rotation ~= nil or Scale ~= nil) then
		M3D.ConvertToTransform(Location, Rotation, Scale, offsetTrans)
	--else
	--	M3D.ConvertToTransform(effectData.Offset, effectData.Rotation, effectData.Scale, NiagaraEffectParam.SpawnTrans)
	end

	if (FollowType == 0) then
		-- 不跟随
		self:GetCurTrans(offsetTrans, NiagaraEffectParam.SpawnTrans)
		if not self.bInWorld then
			local EffectID = Game.EffectManager:GenerateEffectId()
			NiagaraEffectParam.CustomEffectID = EffectID
            self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
			return EffectID
		end

		return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
	end

	NiagaraEffectParam.SpawnTrans = offsetTrans
    NiagaraEffectParam.AttachComponentId = AttachComponentId ~= nil and AttachComponentId or 0

	if not self.bInWorld then
		local EffectID = Game.EffectManager:GenerateEffectId()
		NiagaraEffectParam.CustomEffectID = EffectID
        self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
		return EffectID
	end

    if AttachComponentId == nil then
        NiagaraEffectParam.AttachComponentId = self.CppEntity:KAPI_Actor_FxGetComponentBySocket(NiagaraEffectParam.AttachPointName)
    end

	return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam), 0
end

-- luacheck: pop

--这个方法和上面不一样，上面是谁播特效挂谁身上，这个方法是播销毁特效，开始放的时候能挂的对象已经挂了，所以只能挂在再上一层，比如子弹特效就得挂到发出子弹的Entity身上
function ViewControlFxComponent:PlayEffectTableDestroyNiagara(InID, InLifeSeconds, bBattle, Transform, bFollowHidden)
    local TableData = Game.TableData.GetNiagaraDataRow(InID)
    if (TableData == nil) then
        return 0
    end

	local CharacterTypeForViewBudget = self:GetCharacterTypeForViewBudget()
	local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
		CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL, self.CharacterID, TableData.EffectPriority)
	if not bCanPlayNiagara then
		return 0
	end

    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    --设置跟随类型, 子弹销毁, FollowType必须为0
    NiagaraEffectParam.bFollowHidden = true
    NiagaraEffectParam.bAbsoluteScale = true
    NiagaraEffectParam.bAbsoluteRotation = true
    NiagaraEffectParam.NiagaraEffectPath = TableData.EffectPath
	NiagaraEffectParam.NiagaraBudgetToken = BudgetToken
	NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.SKILL
	NiagaraEffectParam.CustomNiagaraPriority = TableData.EffectPriority
    if PlatformUtil.IsMobilePlatform() and TableData.MobileEffectPath ~= "" then
        NiagaraEffectParam.NiagaraEffectPath = TableData.MobileEffectPath
    end

    NiagaraEffectParam.SpawnerEntityId = self:uid()
    NiagaraEffectParam.SpawnerId = self.CharacterID

    if bFollowHidden == nil then
        NiagaraEffectParam.bFollowHidden = true
    else
        NiagaraEffectParam.bFollowHidden = bFollowHidden
    end

    if bBattle then
        NiagaraEffectParam.SourceType = NIAGARA_SOURCE_TYPE.BATTLE
    end

    if InLifeSeconds == nil or InLifeSeconds < 0 then
        NiagaraEffectParam.TotalLifeMs = -1
    else
        NiagaraEffectParam.TotalLifeMs = InLifeSeconds * 1000
    end

    if Transform ~= nil then
        M3D.ToTransform(Transform, NiagaraEffectParam.SpawnTrans)
    end

	if not self.bInWorld then
        local EffectID = Game.EffectManager:GenerateEffectId()
        NiagaraEffectParam.CustomEffectID = EffectID
        self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
        return EffectID
    end

    return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

-- 默认system action当前仅提供播放attached niagara
function ViewControlFxComponent:PlaySystemActionNiagaraData(niagaraDataID, lifeTimeSeconds)
    local NiagaraConfig = Game.TableData.GetSystemActionNiagaraDataRow(niagaraDataID)
    if NiagaraConfig == nil then
        self:Error("cannot find system action niagara data ", niagaraDataID)
        return
    end

    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    NiagaraEffectParam.SpawnerEntityId = self:uid()
    if PlatformUtil.IsMobilePlatform() and NiagaraConfig.MobileEffectPath ~= "" then
        NiagaraEffectParam.NiagaraEffectPath = NiagaraConfig.MobileEffectPath
    else
        NiagaraEffectParam.NiagaraEffectPath = NiagaraConfig.EffectPath
    end

    NiagaraEffectParam.bNeedAttach = true
    NiagaraEffectParam.SpawnerEntityId = self:uid()
    NiagaraEffectParam.SpawnerId = self.CharacterID
    M3D.ConvertToTransform(NiagaraConfig.Offset, NiagaraConfig.Rotation, NiagaraConfig.Scale, NiagaraEffectParam.SpawnTrans)
    NiagaraEffectParam.EffectTags = { NiagaraConfig.NiagaraEffectTag + NIAGARA_EFFECT_TAG.SYSTEM_ACTION_EFFECT_TAG_START }
    --NiagaraEffectParam.bEnableCustomDepth = self.curEnableCustomDepth
    --NiagaraEffectParam.CustomDepthStencilValue = self.curCustomDepthStencilValue

    if lifeTimeSeconds == nil or lifeTimeSeconds <= 0 then
        NiagaraEffectParam.TotalLifeMs = -1
    else
        NiagaraEffectParam.TotalLifeMs = lifeTimeSeconds * 1000
    end

    if not self.bInWorld then
        local EffectID = Game.EffectManager:GenerateEffectId()
        NiagaraEffectParam.CustomEffectID = EffectID
        self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
        return EffectID
    end

    -- 默认挂接在root component上
    NiagaraEffectParam.AttachComponentId = self.CppEntity:KAPI_Actor_GetRootComponent()
    return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

function ViewControlFxComponent:DeactivateSystemActionNiagaraData(niagaraDataID)
    if not self.bInWorld then
        self:AddCommandCache("DeactivateSystemActionNiagaraData", niagaraDataID)
        return
    end

    local NiagaraConfig = Game.TableData.GetSystemActionNiagaraDataRow(niagaraDataID)
    if NiagaraConfig == nil then
        self:Error("cannot find system action niagara data ", niagaraDataID)
        return
    end

    local NiagaraEffectTag = NiagaraConfig.NiagaraEffectTag + NIAGARA_EFFECT_TAG.SYSTEM_ACTION_EFFECT_TAG_START
    Game.EffectManager:DeactivateNiagaraSystemsByEffectTag(self.CharacterID, NiagaraEffectTag)
end

function ViewControlFxComponent:DestroySystemActionNiagaraData(niagaraDataID)
    if not self.bInWorld then
        self:AddCommandCache("DestroySystemActionNiagaraData", niagaraDataID)
        return
    end

    local NiagaraConfig = Game.TableData.GetSystemActionNiagaraDataRow(niagaraDataID)
    if NiagaraConfig == nil then
        self:Error("cannot find system action niagara data ", niagaraDataID)
        return
    end

    local NiagaraEffectTag = NiagaraConfig.NiagaraEffectTag + NIAGARA_EFFECT_TAG.SYSTEM_ACTION_EFFECT_TAG_START
    Game.EffectManager:DestroyNiagaraSystemsByEffectTag(self.CharacterID, NiagaraEffectTag)
end

-- 对应 Effects_特效表 EntityEffect_模型挂接特效页签特效配置
---@param Duration number 单位秒
function ViewControlFxComponent:PlayEffectByAttachEffectDataID(EffectDataID, Duration)
    if EffectDataID == 0 or EffectDataID == nil then
        return
    end
    
    local EffectInfo = Game.TableData.GetAttachEffectDataRow(EffectDataID)
    if EffectInfo == nil then
        self:Error("PlayEffectByAttachEffectDataID, invalid EffectDataID ", EffectDataID)
        return
    end

    ViewControlFxComponent.AttachEffectData_SpawnTrans:Pack(
        EffectInfo.AttachTransLocX, EffectInfo.AttachTransLocY, EffectInfo.AttachTransLocZ,
        EffectInfo.QuatX, EffectInfo.QuatY, EffectInfo.QuatZ, EffectInfo.QuatW,
        EffectInfo.AttachScale, EffectInfo.AttachScale, EffectInfo.AttachScale
    )
    
    if EffectInfo.bAttachToCamera then
        return self:PlayCameraNiagara(
            EffectInfo.EffectPath,
            ViewControlFxComponent.AttachEffectData_SpawnTrans,
            false,
            Duration
        )
    else
        return self:PlayNiagaraEffectAttached(
                EffectInfo.EffectPath,
                EffectInfo.AttachSocket,
                ViewControlFxComponent.AttachEffectData_SpawnTrans,
                nil,
                false,
                false,
                Duration)
    end
end

---@param SpawnLoc table M3D.Vec3或者FVector
---@param SpawnRot table M3D.Rotator或者FRotator
function ViewControlFxComponent:PlayEffectByLocationEffectID(EffectDataID, Duration, SpawnLoc, SpawnRot)
    if EffectDataID == 0 or EffectDataID == nil then
        return
    end
    
    local EffectInfo = Game.TableData.GetLocationEffectDataRow(EffectDataID)
    if EffectInfo == nil then
        self:Error("PlayEffectByLocationEffectID, invalid EffectDataID ", EffectDataID)
        return
    end

    M3D.ToVec3(SpawnLoc, NiagaraEffectParam.SpawnTrans.Translation)
    if SpawnRot ~= nil then
        M3D.FRotToQuat(SpawnRot, NiagaraEffectParam.SpawnTrans.Rotation)
    else
        NiagaraEffectParam.SpawnTrans.Rotation:Reset()
    end

    NiagaraEffectParam.SpawnTrans.Scale3D:Reset(EffectInfo.Scale, EffectInfo.Scale, EffectInfo.Scale)

    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    NiagaraEffectParam.NiagaraEffectPath = EffectInfo.EffectPath
    NiagaraEffectParam.TotalLifeMs = Duration * 1000
    return self:PlayNiagaraEffect(NiagaraEffectParam)
end

---@public 对于武器的溶解来说, 对应的溶解特效需要传入武器对应的mesh component作为特效参数, 用于特效粒子获取对应武器的轮廓信息
---武器目前对应的mesh component既可能是static mesh component也可能是skeletal mesh component, 但是一个特效资源中不能既开放static mesh component特效参数
---又开放skeletal mesh component特效参数, 为了避免策划配置武器溶解时还需要关心武器使用的是什么类型的mesh component, 策划配置时总是把两个资源都
---配置进来, 程序动态根据 mesh component ID来做选择
function ViewControlFxComponent:PlayWeaponDissolveNiagara(SKDissoveEffectPath, SMDissolveEffectPath, DissolveTime, DissolveType)
    if SKDissoveEffectPath == nil and SMDissolveEffectPath == nil then
        return
    end
    
    if not self.bInWorld then
        self:AddCommandCache("PlayWeaponDissolveNiagara", SKDissoveEffectPath, SMDissolveEffectPath, DissolveTime, DissolveType)
        return
    end

    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    local meshCompID = self.CppEntity:KAPI_Actor_GetComponentByClass(StaticMeshComponent)
    if meshCompID ~= 0 then
        NiagaraEffectParam.UserVals_MeshCompIds = { ["mesh"] = meshCompID }
        NiagaraEffectParam.NiagaraEffectPath = SMDissolveEffectPath
    else
        meshCompID = self.CppEntity:KAPI_Actor_GetComponentByClass(USkeletalMeshComponent)
        NiagaraEffectParam.UserVals_MeshCompIds = { ["SkMesh"] = meshCompID }
        NiagaraEffectParam.NiagaraEffectPath = SKDissoveEffectPath
    end
    
    NiagaraEffectParam.TotalLifeMs = DissolveTime * 1000
    NiagaraEffectParam.UserVals_Float = { ["fasheshichang"] = DissolveTime * DISSOLVE_MATERIAL_PARAMS[DissolveType].NIAGARA_EXISTING_TIME_RATIO }
    NiagaraEffectParam.bActivateImmediately = true
    
    if DissolveType == DISSOLVE_MATERIAL_EFFECT_TYPE.WEAPON_DISSOLVE_IN then
        NiagaraEffectParam.bNeedAttach = true
    elseif DissolveType == DISSOLVE_MATERIAL_EFFECT_TYPE.WEAPON_DISSOLVE_OUT then
        M3D.ToVec3(self:GetPosition(), NiagaraEffectParam.SpawnTrans.Translation)
        M3D.FRotToQuat(self:GetRotation(), NiagaraEffectParam.SpawnTrans.Rotation)
        NiagaraEffectParam.bUseCloneMeshCompOnSystemUserVariableStaticMeshComponent = true
        NiagaraEffectParam.bFollowHidden = false
    end
    
    self:PlayNiagaraEffect(NiagaraEffectParam)
end

-- 停止所有特效的播放
function ViewControlFxComponent:DestroyAllNiagaras(bSpawnerExitWorld)
    Game.EffectManager:DestroyNiagarasBySpawnerId(self.CharacterID, bSpawnerExitWorld)
end

function ViewControlFxComponent:GetCurTrans(offset, InTransform)
    local Transform = InTransform
    if (Transform == nil) or (Transform.Type ~= "Transform") then
        Transform = M3D.Transform()
    end
	Transform:Pack(self:GetTransform_P())
	-- Transform:Mul(offset, Transform)
    local Loc = Transform.Translation
    Loc:Add(offset.Translation, Transform.Translation)
    
    local Rot = Transform.Rotation
    Rot:Mul(offset.Rotation, Transform.Rotation)

    Transform.Scale3D:Reset(offset.Scale3D)


    return Transform
end

-- 播放相机特效
---@param Duration number 单位秒
function ViewControlFxComponent:PlayCameraNiagara(NiagaraAssetPath, RelativeTransform, bDestroyWhenTaskEnd, Duration, SourceSkillIdDebugUse)
    -- 获取摄像机
    local CameraManager = Game.CameraManager
    if not IsValid_L(CameraManager) then
        self:ErrorFmt("ViewControlFxComponent:PlayCameraNiagara, invalid camera manager")
        return
    end

    -- 不传则维持旧的默认值
    if not Duration then Duration = 10000 end

    local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
    NiagaraEffectParam.TotalLifeMs = bDestroyWhenTaskEnd and -1.0 or Duration * 1000
    NiagaraEffectParam.bFollowCameraFOV = true
    NiagaraEffectParam.bFollowHidden = false
    NiagaraEffectParam.bNeedAttach = true
    NiagaraEffectParam.AttachComponentId = CameraManager:KAPI_Camera_GetCameraManagerRootComponentID()
    NiagaraEffectParam.NiagaraEffectPath = NiagaraAssetPath
    NiagaraEffectParam.SourceSkillIdDebugUse = SourceSkillIdDebugUse

    NiagaraEffectParam.SpawnerEntityId = self:uid()
    NiagaraEffectParam.SpawnerId = self.CharacterID
    M3D.ToTransform(RelativeTransform, NiagaraEffectParam.SpawnTrans)

	if not self.bInWorld then
        local EffectID = Game.EffectManager:GenerateEffectId()
        NiagaraEffectParam.CustomEffectID = EffectID
        self:AddCommandCache("InternalCreateNiagaraSystem", NiagaraEffectParam)
        return EffectID
    end

    return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

function ViewControlFxComponent:StopCameraNiagara(EffectId)
    self:DeactivateNiagaraSystem(EffectId)
end

function ViewControlFxComponent:ChangeNiagaraMeshColor()
    if not self.bInWorld then
        self:AddCommandCache("ChangeNiagaraMeshColor")
        return
    end

    Game.EffectManager:UpdateAllSpiritualVisionMeshColorParam(self.CharacterID, "MeshColor", true)
end

function ViewControlFxComponent:ResetNiagaraMeshColor()
    if not self.bInWorld then
        self:AddCommandCache("ResetNiagaraMeshColor")
        return
    end

    Game.EffectManager:UpdateAllSpiritualVisionMeshColorParam(self.CharacterID, "MeshColor", false)
end

--function ViewControlFxComponent:SetAllNiagarasRenderCustomDepth(bEnableCustomDepth, CustomDepthStencilValue)
--    self.curEnableCustomDepth = bEnableCustomDepth
--    self.curCustomDepthStencilValue = CustomDepthStencilValue
--    
--    if not self.bInWorld then
--        self:AddCommandCache("SetAllNiagarasRenderCustomDepth", bEnableCustomDepth, CustomDepthStencilValue)
--        return
--    end
--    
--    Game.EffectManager:SetNiagaraRenderCustomDepthBySpawnerId(self.CharacterID, bEnableCustomDepth, CustomDepthStencilValue)
--end

function ViewControlFxComponent:UpdateLinearSampleScalarNiagaraParamTargetVal(EffectID, ParamName, TargetVal, bUseNewDuration, NewDuration)
    if not self.bInWorld then
        self:AddCommandCache("UpdateLinearSampleScalarNiagaraParamTargetVal", EffectID, ParamName, TargetVal, bUseNewDuration, NewDuration)
        return
    end
    
    Game.EffectManager:UpdateLinearSampleScalarNiagaraParamTargetVal(EffectID, ParamName, TargetVal, bUseNewDuration, NewDuration)
end

function ViewControlFxComponent:UpdateAllNiagaraFloatUserParams(ParamName, ParamVal)
    if not self.bInWorld then
        self:AddCommandCache("UpdateAllNiagaraFloatUserParams", ParamName, ParamVal)
        return
    end

    Game.EffectManager:UpdateAllNiagaraFloatParamBySpawnerId(ParamName, ParamVal)
end

---@private
function ViewControlFxComponent:InternalCreateNiagaraSystem(NiagaraEffectParam)
    -- 对于需要进行culling的特效来说, 播放前可能会申请budget, 但是结果因为未enter world或者其他原因导致特效当时未直接播放, 目前budget可能会超时导致失效, 目前这里
    -- 的处理方式是对这类特效来说, 再次播放时需要重新申请新的budget
    if NiagaraEffectParam.NiagaraBudgetToken and NiagaraEffectParam.NiagaraEffectType then
        local CharacterTypeForViewBudget = self:GetCharacterTypeForViewBudget()
        local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
            CharacterTypeForViewBudget, NiagaraEffectParam.NiagaraEffectType, self.CharacterID, NiagaraEffectParam.CustomNiagaraPriority)
        if not bCanPlayNiagara then
            return
        end

        NiagaraEffectParam.NiagaraBudgetToken = BudgetToken
    end

    NiagaraEffectParam.SpawnerId = self.CharacterID

    if NiagaraEffectParam.bNeedAttach and NiagaraEffectParam.AttachComponentId == 0 then
        NiagaraEffectParam.AttachComponentId = self.CppEntity:KAPI_Actor_FxGetComponentBySocket(NiagaraEffectParam.AttachPointName)
    end

    return Game.EffectManager:CreateNiagaraSystem(NiagaraEffectParam)
end

--endregion Niagara

--region Decal

function ViewControlFxComponent:PlayEffectTableDecal(DecalAssetPath, Offset, Rotation, Scale, InLifeSeconds)
	local decalData = self:initDecalData(DecalAssetPath, Offset, Rotation, Scale, InLifeSeconds)
	return self:SpawnDecalToTarget(decalData)
end

function ViewControlFxComponent:initDecalData(DecalAssetPath, Offset, Rotation, Scale, InLifeSeconds)
	local DecalConfig = {}
	DecalConfig.DecalAssetPath = DecalAssetPath
	DecalConfig.Translation = Offset
	DecalConfig.Rotation = Rotation
	DecalConfig.Scale3D = Scale
	DecalConfig.Duration = InLifeSeconds
	DecalConfig.DecalSize = {100,100,100} -- todo:DecalSize
	DecalConfig.SpawnerEntityUID = self:uid()
	return DecalConfig
end

---生成贴花实体
---@param DecalConfig table
---@return integer|nil DecalEntityId
function ViewControlFxComponent:SpawnDecalToTarget(DecalConfig)
    if (DecalConfig == nil) then
        self:Warning("[lx] ViewControlFxComponent:SpawnDecalToTarget, invalid decal config")
        return nil
    end

    if (DecalConfig.DecalAssetPath == nil) or (DecalConfig.DecalAssetPath == "") then
        self:Warning("[lx] ViewControlFxComponent:SpawnDecalToTarget, invalid decal asset path")
        return nil
    end

	if self.bInWorld == false then
		self:ErrorFmt("SpawnDecalToTarget, invalid spawner entity, SpawnerEntityUID: %d, DecalAssetPath: %s", self:uid(),
			DecalConfig.DecalAssetPath)
		return nil
	end

	-- 贴花实体的UID和SpawnerEntityUID一致
	DecalConfig.SpawnerEntityUID = self:uid()
    local DecalEntityID = self:internalSpawnDecalEntity(DecalConfig)
    if DecalEntityID == nil or self.DecalEntities[DecalEntityID] == nil then
        self:WarningFmt("[lx] ViewControlFxComponent:SpawnDecalInstance, create DecalEntity failed, LoadId: %d", DecalEntityID)
        return nil
    end

    return DecalEntityID
end

---内部方法，生成贴花实体
---@param SpawnParams table 生成参数
---@return integer|nil EntityId
function ViewControlFxComponent:internalSpawnDecalEntity(SpawnParams)
	if SpawnParams == nil then
		return
	end
	
	local DecalEntity = Game.EntityManager:CreateLocalEntity("DecalEntity", nil, SpawnParams)
	if DecalEntity == nil then
		return
	end

	local EntityId = DecalEntity:uid()
	self.DecalEntities[EntityId] = true


    return EntityId
end

---根据参数生成贴花实体，仅GM调用
---@param SpawnParams table
---@return integer|nil EntityId
function ViewControlFxComponent:SpawnDecalEntity(SpawnParams)
    if SpawnParams == nil then
        return
    end

    if SpawnParams.DecalMaterialAssetPath == nil or SpawnParams.DecalMaterialAssetPath == "" then
        return
    end

    local DecalEntity = Game.EntityManager:CreateLocalEntity("DecalEntity", nil, SpawnParams)
    if DecalEntity == nil then
        return
    end

    local EntityId = DecalEntity:uid()
    self.DecalEntities[EntityId] = true
    return EntityId
end

---销毁贴花实体
---@param DecalEntityId integer
function ViewControlFxComponent:DestroyDecalEntity(DecalEntityId)
    self.DecalEntities[DecalEntityId] = nil

	local DecalEntity = Game.EntityManager:GetLocalEntity(DecalEntityId)
    if DecalEntity then
		DecalEntity:destroy()
    end
end

---销毁所有贴花实体
function ViewControlFxComponent:DestroyAllDecalEntities()
    for EntityId, _ in pairs(self.DecalEntities) do
        local entity = Game.EntityManager:GetLocalEntity(EntityId)
		if entity then
			entity:destroy()
		end
    end
    self.DecalEntities = {}
end

--endregion Decal


--region MeshVisibility
-- mesh隐藏功能
function ViewControlFxComponent:MeshVisibility(TaskData, TaskGID)
    -- 获取Task静态数据
    if (TaskData == nil) then
        return
    end

	if not self.bInWorld then
		table.insert(self.MeshGIDCacheList, TaskGID)
		self.MeshTaskDataCacheList[TaskGID] = TaskData
		return
	end
    
	table.clear(self.MeshGIDs)
    if TaskData.BReverse == true or (TaskData.MeshNames and table.count(TaskData.MeshNames) > 0) then
        ViewControlFxComponent.__Tmp_Socket_Names:Clear()
		if TaskData.MeshNames then
			for _, MeshName in ksbcpairs(TaskData.MeshNames) do
                ViewControlFxComponent.__Tmp_Socket_Names:Add(MeshName)
			end
		end

        local CompIDs = self.CppEntity:KAPI_Actor_FxGetComponentsByNames(
                ViewControlFxComponent.MeshVisibility_ComponentTypes, ViewControlFxComponent.__Tmp_Socket_Names, not TaskData.BReverse)
        for _, CompID in pairs(CompIDs) do
            table.insert(self.MeshGIDs, CompID)
        end
    end

    if TaskData.BReverse == true or (TaskData.SocketNames and table.count(TaskData.SocketNames) > 0) then
		--if TaskData.SocketNames then
        --    ViewControlFxComponent.__Tmp_Socket_Names:Clear()
		--	for _, SocketName in pairs(TaskData.SocketNames) do
        --        ViewControlFxComponent.__Tmp_Socket_Names:Add(SocketName)
		--	end
		--end
		--
        --local CompIDs = self.CppEntity:KAPI_Actor_FxGetComponentsBySockets(
        --        ViewControlFxComponent.MeshVisibility_ComponentTypes, ViewControlFxComponent.__Tmp_Socket_Names, not TaskData.BReverse)
        --for _, CompID in pairs(CompIDs) do
        --    table.insert(self.MeshGIDs, CompID)
        --end
    end
    
	if not next(self.MeshGIDs) and TaskData.MeshNames then
		self:CacheMeshVisibilityBefore(TaskData, TaskGID)
	else
		self:HiddenMeshList(self.MeshGIDs, TaskData, TaskGID)
	end
    if TaskData.BHiddenHeadInfo == true then
        self:HideHeadInfo(true)
        if self.HideHeadInfoTasks == nil then
            self.HideHeadInfoTasks = {}
        end
        self.HideHeadInfoTasks[TaskGID] = true
    end
end

--恢复隐藏mesh
function ViewControlFxComponent:MeshVisibilityEnd(BHiddenHeadInfo, TaskGID)
    if (self.TaskGIDToMeshGID and self.TaskGIDToMeshGID[TaskGID] ~= nil) then
        for GID, ID in pairs(self.TaskGIDToMeshGID[TaskGID]) do
			self.HiddenMeshIDCounts[GID] = (self.HiddenMeshIDCounts[GID] or 0) - 1
			if self.HiddenMeshIDCounts[GID] <= 0 then
				if Game.BSManager.bIsInEditor then
					self.CppEntity:KAPI_SceneID_SetVisibility(GID, true, false)
				else
					self.CppEntity:KAPI_SceneID_SetHiddenInGame(GID, false, false)
				end
				self.HiddenMeshIDCounts[GID] = nil
			end
            self.TaskGIDToMeshGID[TaskGID][GID] = nil
        end
        self.TaskGIDToMeshGID[TaskGID] = nil
    end
    if BHiddenHeadInfo == true and self.HideHeadInfoTasks then
        self.HideHeadInfoTasks[TaskGID] = nil
    end
    if self.HideHeadInfoTasks and table.count(self.HideHeadInfoTasks) == 0 then
        self:HideHeadInfo(false)
    end
	self:UnCacheMeshVisibilityBefore(TaskGID)
end

-- 隐藏Mesh列表
function ViewControlFxComponent:HiddenMeshList(InList, TaskData, TaskGID)
    if (InList == nil) then
        return
    end
    if (self.TaskGIDToMeshGID[TaskGID] == nil) then
        self.TaskGIDToMeshGID[TaskGID] = {}
    end
    -- 通知修改栈组件修改显示/隐藏信息
    for _, GID in ipairs(InList) do
        -- 这些component目前看下来肯定都从属于自身的entity, 没必要反查
        if self.IsPet == true then
            goto continue
        end

        if (self.TaskGIDToMeshGID[TaskGID] ~= nil and self.TaskGIDToMeshGID[TaskGID][GID] ~= nil) then
            goto continue
        end

		if Game.BSManager.bIsInEditor then
			self.CppEntity:KAPI_SceneID_SetVisibility(GID, false, false)
		else
			self.CppEntity:KAPI_SceneID_SetHiddenInGame(GID, true, false)
		end
        self.TaskGIDToMeshGID[TaskGID][GID] = 0
		self.HiddenMeshIDCounts[GID] = (self.HiddenMeshIDCounts[GID] or 0) + 1

        :: continue ::
    end
end

--隐藏头顶信息
function ViewControlFxComponent:HideHeadInfo(bHidden)
    Game.NewHeadInfoSystem:UpdateHeadInfoHidden(self:uid(), bHidden, NewHeadInfoConst.HeadInfoHiddenFlag.BattleInfo)
    local HIMgr = Game.HeadInfoManager
    if (HIMgr == nil) then
        return
    end
    HIMgr:OnBattleHideHeadInfo(self:uid(), bHidden)
end
--endregion MeshVisibility




--region IceField
-- 生成冰场
function ViewControlFxComponent:SpawnIceField(InEID, Translation, Rotation, Radius, LastTime, DisappearEffect, EffectTransform, DisappearAk, AkTransform, bNeedCheckGround)
    local TargetLoc = self:GetPosition()
    local TargetRot = self:GetRotation()
    local TransM3D = M3D.AssembleTransform(Translation, M3D.FRotToQuat(Rotation))
    local WorldTranslation = M3D.GetWorldTransform(TargetLoc, TargetRot, TransM3D)
    -- 检测地面
    if (bNeedCheckGround == true) then
        local WLocation = WorldTranslation.Translation
        local ObjectTypes = 1
        local target = Game.EntityManager:GetEntityByIntID(InEID)
        if target and target.CharacterID then
            local FindGround, X, Y, Z = UBSFunc.FindGroundLocation_P(target.CharacterID, 
                target.CharacterID, WLocation.X, WLocation.Y, WLocation.Z, ObjectTypes, -300.0, 300.0, 0.2, M3D.Fill3())
            if (FindGround == true) then
                WLocation.X = X
                WLocation.Y = Y
                WLocation.Z = Z
            end
        end
    end
    local IceFieldData = {
        IceTransform = WorldTranslation,
        IceRadius = Radius,
        LastTime = LastTime,
        DisappearEffect = DisappearEffect,
        EffectTransform = EffectTransform,
        DisappearAk = DisappearAk,
        AkTransform = AkTransform,
    }

    Game.EntityManager:CreateLocalEntity("IceField", IceFieldData)
end
--endregion IceField




--region LocalWind
-- 生成局部风
function ViewControlFxComponent:SpawnLocalWind(WindType, OffsetX, OffsetY, ScaleX, ScaleY, Strength, FanCenterAngle, FlowMap)
    local Trans = M3D.Transform()
    local Rotator = M3D.Rotator()
    Trans:Pack(self.CppEntity:KAPI_Actor_GetTransform(), false, M3D.Fill10())
    Rotator:Pack(self:GetRotation_P())
    Game.WorldManager:RequestLWFMotorForOnce(WindType,
            Trans.Translation.X + OffsetX,
            Trans.Translation.Y + OffsetY,
            Rotator.Yaw,
            ScaleX,
            ScaleY,
            Strength,
            FanCenterAngle,
            FlowMap
    )
end
--endregion LocalWind


--region new Ghost
-- 生成残影
function ViewControlFxComponent:NewCommonSpawnGhost(duplicateTargetEID, configData, targetEID)
    configData.targetEID = targetEID--技能目标的EID
    configData.duplicateTargetEID = duplicateTargetEID--复制的Entity EID
    configData.sourceEID = self:uid()
	configData.SpawnerEntityUID = self:uid()

	local GhostEntity = Game.EntityManager:CreateLocalEntity("GhostEntity", nil, configData)
	if GhostEntity == nil then
		return
	end

    return GhostEntity

end

function ViewControlFxComponent:NewEndGhost(GhostID, bTerminateGhostWhenTaskEnd)
    if (bTerminateGhostWhenTaskEnd == true) then
        local entity = Game.EntityManager:GetLocalEntity(GhostID)
		if entity then
			entity:destroy()
		end
    end
end
--endregion new Ghost




---@private
function ViewControlFxComponent:AttachGenerateSpawnEffect()
    self:PlayAttachEffectByType("playonspawn")
end

---@public
function ViewControlFxComponent:ClearAttachEffects(Type, Tag)
    if not self.bInWorld then
        self:AddCommandCache("ClearAttachEffects",Type, Tag)
        return
    end
    local KeysToRemove = {}

    for Eid,Handle in pairs(self.AttachEffectIDs) do
        if (Type == nil or Handle.EffectType == Type) and (Tag == nil or Handle.EffectTag == Tag)then
            self:DeactivateNiagaraSystem(Handle.EffectID)
            table.insert(KeysToRemove,Eid)
        end
    end

    for K,Eid in pairs(KeysToRemove) do
        self.AttachEffectIDs[Eid] = nil
    end
end

---@public
--function ViewControlFxComponent:__component_ActorVisibleChanged__(bVisible)
--
--end

---@public
function ViewControlFxComponent:PlayAttachEffectByTag(Type, InTag, delayDestroyMs)
    if not self.bInWorld then
        self:AddCommandCache("PlayAttachEffectByTag", Type, InTag)
        return
    end

    self:ClearAttachEffects(Type, InTag)

    local ModelData = Game.ActorAppearanceManager.AvatarModelLib[self.ModelID]
    if ModelData and ModelData.Effects then
        for K, EffectData in pairs(ModelData.Effects) do
            if EffectData.Tag ~= InTag then
                goto continue
            end
			
            if EffectData.EffectPlayType == Type then
				local NiagaraBudgetToken
				if self.bIsAttachItem == true then
					local ManagerEntityId = self:GetAttachItemManagerEntity()
					local ManagerEntity = Game.EntityManager:getEntity(ManagerEntityId)
					if ManagerEntity then
						local CharacterTypeForViewBudget = ManagerEntity:GetCharacterTypeForViewBudget()
						local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
							CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.ATTACHMENT, self.CharacterID)
						if not bCanPlayNiagara then
							goto continue
						end
						NiagaraBudgetToken = BudgetToken
					end
				end
				
                local Trans = M3D.AssembleTransform(EffectData.Offset:GetTranslation(), EffectData.Offset:GetRotation(), EffectData.Offset:GetScale3D())

				local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
				NiagaraEffectParam.NiagaraEffectPath = EffectData.NS_Effect
				NiagaraEffectParam.bNeedAttach = true
				NiagaraEffectParam.AttachPointName = EffectData.Socket
				NiagaraEffectParam.DelayDestroyMs = delayDestroyMs or NiagaraEffectParam.DelayDestroyMs
				M3D.ToTransform(Trans, NiagaraEffectParam.SpawnTrans)
				
				NiagaraEffectParam.AttachComponentId = self.CppEntity:KAPI_Actor_FxGetComponentBySocket(NiagaraEffectParam.AttachPointName)
				NiagaraEffectParam.NiagaraBudgetToken = NiagaraBudgetToken
				NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.ATTACHMENT
				
                local EffectEntityID = self:PlayNiagaraEffect(NiagaraEffectParam)

                local EffectHandle = {
                    EffectID = EffectEntityID,
                    EffectType = EffectData.EffectPlayType,
                    EffectTag = InTag,
                }
                table.insert(self.AttachEffectIDs, EffectHandle)
            end

            ::continue::
        end
    end
end

---@public
function ViewControlFxComponent:PlayAttachEffectByType(Type)
    if not self.bInWorld then
        self:AddCommandCache("PlayAttachEffectByType", Type)
        return
    end

    self:ClearAttachEffects(Type)
    if self.ModelID == nil then
        return
    end

    if not  Game.me then
        return
    end

    local ModelData = Game.ActorAppearanceManager.AvatarModelLib[self.ModelID]
    if ModelData and ModelData.Effects then
        for K, EffectData in pairs(ModelData.Effects) do
            if self.bUseWhitList and not self.EffectWhiteListTags[EffectData.Tag] then
                goto continue
            end

            if EffectData.EffectPlayType == Type then
				local NiagaraBudgetToken
				if self.bIsAttachItem == true then
					local ManagerEntityId = self:GetAttachItemManagerEntity()
					local ManagerEntity = Game.EntityManager:getEntity(ManagerEntityId)
					if ManagerEntity then
						local CharacterTypeForViewBudget = ManagerEntity:GetCharacterTypeForViewBudget()
						local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
							CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.ATTACHMENT, self.CharacterID)
						if not bCanPlayNiagara then
							goto continue
						end
						NiagaraBudgetToken = BudgetToken
					end
				end
				
                local Trans = M3D.AssembleTransform(EffectData.Offset:GetTranslation(), EffectData.Offset:GetRotation(), EffectData.Offset:GetScale3D())
				
				local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
				NiagaraEffectParam.NiagaraEffectPath = EffectData.NS_Effect
				NiagaraEffectParam.bNeedAttach = true
				NiagaraEffectParam.AttachPointName = EffectData.Socket
				M3D.ToTransform(Trans, NiagaraEffectParam.SpawnTrans)
				
				NiagaraEffectParam.AttachComponentId = self.CppEntity:KAPI_Actor_FxGetComponentBySocket(NiagaraEffectParam.AttachPointName)
				NiagaraEffectParam.NiagaraBudgetToken = NiagaraBudgetToken
				NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.ATTACHMENT

				local EffectEntityID = self:PlayNiagaraEffect(NiagaraEffectParam)

                local EffectHandle = {
                    EffectID = EffectEntityID,
                    EffectType = EffectData.EffectPlayType,
                }
                table.insert(self.AttachEffectIDs, EffectHandle)
            end

            ::continue::
        end
    end
end


function ViewControlFxComponent:_onDelayEffectTimer(Type, WhiteList, bDelayFrame, timerIndex)
	self.DelayEffectTimers[timerIndex] = nil
	self:PlayLocationEffectByType(Type,WhiteList,bDelayFrame)
end

---@public
function ViewControlFxComponent:PlayLocationEffectByType(Type,WhiteList,bDelayFrame)
    if not self.bInWorld then
        self:AddCommandCache("PlayLocationEffectByType", Type,WhiteList)
        return
    end

    if bDelayFrame then
		local timerIndex = self._delayEffectTimerIndex + 1
		self._delayEffectTimerIndex = timerIndex
		
		local timerId = self:AddTimer(0.01, 1, "PlayLocationEffectByType", Type, WhiteList, false, timerIndex)
		
        --local TimerID
        --TimerID = Game.TimerManager:CreateTimerAndStart(function()
        --    self:PlayLocationEffectByType(Type,WhiteList,false)
        --    self.DelayEffectTimers[TimerID] = nil
        --end,10,1)
		
        self.DelayEffectTimers = self.DelayEffectTimers or {}
        self.DelayEffectTimers[timerIndex] = timerId
    end


    self:ClearAttachEffects(Type)
    if not self.ModelID then
        return
    end

    local ModelData = Game.ActorAppearanceManager.AvatarModelLib[self.ModelID]
    if ModelData and ModelData.Effects and self.MeshID then
        for K, EffectData in pairs(ModelData.Effects) do
            if self.bUseWhitList and not self.EffectWhiteListTags[EffectData.Tag] then
                goto continue
            end

            if EffectData.EffectPlayType == Type then
                --TODO:RTS_World在Attach情况下位置拿到的还是相对偏移所以这边 拿相对偏移再加回去，后面有时间再查一下
				local NiagaraBudgetToken
				if self.bIsAttachItem == true then
					local ManagerEntityId = self:GetAttachItemManagerEntity()
					local ManagerEntity = Game.EntityManager:getEntity(ManagerEntityId)
					if ManagerEntity then
						local CharacterTypeForViewBudget = ManagerEntity:GetCharacterTypeForViewBudget()
						local bCanPlayNiagara, BudgetToken = Game.EffectManager:TryObtainNiagaraBudget(
							CharacterTypeForViewBudget, NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.ATTACHMENT, self.CharacterID)
						if not bCanPlayNiagara then
							goto continue
						end
						NiagaraBudgetToken = BudgetToken
					end
				end
				
                local Trans = self.CppEntity:KAPI_SceneID_GetSocketTransform(self.MeshID, EffectData.Socket, ERelativeTransformSpace.RTS_Component)
                Trans = M3D.AssembleTransform(
                        Trans:GetTranslation() + self.CppEntity:KAPI_Component_K2_GetComponentLocation(self.MeshID),
                        Trans:GetRotation(),
                        Trans:GetScale3D())

				local NiagaraEffectParam = NiagaraEffectParamTemplate.AllocFromPool()
				NiagaraEffectParam.NiagaraEffectPath = EffectData.NS_Effect
				NiagaraEffectParam.bNeedAttach = false
				M3D.ToTransform(Trans, NiagaraEffectParam.SpawnTrans)
				NiagaraEffectParam.NiagaraBudgetToken = NiagaraBudgetToken
				NiagaraEffectParam.NiagaraEffectType = NIAGARA_EFFECT_TYPE_FOR_PRIORITY_CULLING.ATTACHMENT

				local EffectEntityID = self:PlayNiagaraEffect(NiagaraEffectParam)

                local EffectHandle = {
                    EffectID = EffectEntityID,
                    EffectType = EffectData.EffectPlayType,
                }
                table.insert(self.AttachEffectIDs, EffectHandle)
            end

            ::continue::
        end
    end
end

return ViewControlFxComponent
