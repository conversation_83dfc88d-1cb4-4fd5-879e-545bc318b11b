---------------------------------
--- ViewControl基础逻辑，包含加载，卸载，EnterWorld，ExitWorld
--------------------------------

local EPropertyClass = import("EPropertyClass")
local AnimLibHelper = kg_require("GamePlay.3C.RoleComposite.AnimLibHelper")
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")

local AnimFeatureList = kg_require('Gameplay.NetEntities.Comps.ViewControl.ViewControlUtils.Anim.AnimLibFeatureList')
local AnimLibFeature_SingleSequenceWithAttach = kg_require('Gameplay.NetEntities.Comps.ViewControl.ViewControlUtils.Anim.AnimLibFeature_SingleSequenceWithAttach')
local AnimLibFeature_AnimListWithAttach = kg_require('Gameplay.NetEntities.Comps.ViewControl.ViewControlUtils.Anim.AnimLibFeature_AnimListWithAttach')

ViewControlAnimLibComponent = DefineComponent("ViewControlAnimLibComponent")

ViewControlAnimLibComponent.EUpperAnimationBlendType = ViewControlAnimLibComponent.EUpperAnimationBlendType or {
	UpperBodyBlend = 1,
	UpperLimbBodyBlend = 2,
	HeadBlend = 3,
}

ViewControlAnimLibComponent.EUpperAnimationBlendBone = ViewControlAnimLibComponent.ELowerAnimationBlendBone or {
	[1] = "spine_01",
	[2] = "spine_03",
	[3] = "neck_01",
}

function ViewControlAnimLibComponent:ctor ()
	self.AnimFeatureMap = {}
	self.HalfFeatureID = nil
	self.LatestAnimFeatureID = nil
	self.CurrentFeatureList = nil
	
	self.AssetSwitchIDs = nil
	self.LocoReplaceMap = slua.Map(EPropertyClass.Name, EPropertyClass.Str)
	
	self.FashionAnimOverrideLoadID = nil
	self.MountRideAnimOverrideLoadID = nil
end

function ViewControlAnimLibComponent:__component_ExitWorld__()
	if self.AssetSwitchIDs ~= nil then
		for SwitchID, _ in pairs(self.AssetSwitchIDs) do
			self:CancelAnimOverrideByAnimLib(SwitchID)
		end
	end
	
	self:StopAnimLibMontage()
	if self.CurrentFeatureList then
		self:StopAnimList(self.CurrentFeatureList.UniqueID)
	end
	
end

------------------------------------------------------------------------------------------------------------------------------------------------------
-- 回调默认绑在上半身动画
function ViewControlAnimLibComponent:PlayAnimLibMontageForUpper(UpperAnimAssetID, UpperStartStage, bLoop, BlendIn, BlendOut, EBlendType, bActionMeshSpaceBlend, bAutoStop, EndCBObj, EndCBFuncName, ...)
	EBlendType = EBlendType or ViewControlAnimLibComponent.EUpperAnimationBlendType.UpperBodyBlend
	local FilterBone = ViewControlAnimLibComponent.EUpperAnimationBlendBone[EBlendType]
	local UpperFeatureID, Duration = self:PlayAnimLibFeatureAsUpper(UpperAnimAssetID, UpperStartStage, bLoop, BlendIn, BlendOut, bAutoStop, EndCBObj, EndCBFuncName, false, ViewAnimConst.EActionBlendRule.PartBodyOverride, FilterBone, 2, bActionMeshSpaceBlend, ...)

	return UpperFeatureID, Duration
end

function ViewControlAnimLibComponent:StopAnimLibMontageForUpper(UniqueID, BlendOutTime)
	UniqueID = UniqueID or self.LatestUpperAnimFeatureID

	local AnimFeatureMap = self.AnimFeatureMap
	if AnimFeatureMap[UniqueID] then
		AnimFeatureMap[UniqueID]:Stop(false, BlendOutTime)
	end
end

function ViewControlAnimLibComponent:PlayAnimLibMontageForLowPriority(AnimAssetID, StartStage, bLoop, BlendIn, BlendOut, EndCBObj, EndCBFuncName)
	local NewFeature, _ = self:CreateFeatureByFeatureByType(ViewAnimConst.EAnimPlayReqTag.LocalPerformance, AnimAssetID, StartStage, BlendIn, BlendOut, bLoop, true, EndCBObj, EndCBFuncName, true, nil, nil, nil, nil)
	if NewFeature then
		NewFeature:Run()
		if NewFeature.UniqueID then
			self.AnimFeatureMap[NewFeature.UniqueID] = NewFeature
			return true, NewFeature.UniqueID
		end
	end

	return false, nil
end

function ViewControlAnimLibComponent:ChangeAnimFeatureToLowPriorityAnimaFeature(FeatureID, EndCBObj, EndCBFuncName)
	local AnimFeature = self.AnimFeatureMap[FeatureID]
	if AnimFeature then
		return AnimFeature:ChangeToLowPriorityFeature(EndCBObj, EndCBFuncName)
	end

	return false
end

-- 所有的主动Stop都没有回调
function ViewControlAnimLibComponent:StopAnimLibMontageForLowPriority(UUID)
	local AnimFeatureMap = self.AnimFeatureMap
	if AnimFeatureMap[UUID] then
		AnimFeatureMap[UUID]:Stop(false)
	end
end

-- 仅用于表演性动作播放
function ViewControlAnimLibComponent:PlayAnimLibMontage_Loop(AnimAssetID, StartStage, BlendIn, BlendOut, bAutoStop, EndCBObj, EndCBFuncName)
	return self:PlayAnimLibFeatureAsPerformance(AnimAssetID, StartStage, true, BlendIn, BlendOut, bAutoStop, EndCBObj, EndCBFuncName)
end

-- 仅用于表演性动作播放
function ViewControlAnimLibComponent:PlayAnimLibMontage(AnimAssetID, StartStage, bLoop, BlendIn, BlendOut, bAutoStop, EndCBObj, EndCBFuncName, ...)
	return self:PlayAnimLibFeatureAsPerformance(AnimAssetID, StartStage, bLoop, BlendIn, BlendOut, bAutoStop, EndCBObj, EndCBFuncName, nil, nil, nil, nil, nil, ...)
end

-- 仅用于表演性动作播放
function ViewControlAnimLibComponent:PlayAnimLibMontageWithoutRefreshView(AnimAssetID, StartStage, BlendIn, BlendOut, bAutoStop, EndCBObj, EndCBFuncName)
	return self:PlayAnimLibFeatureAsPerformance(AnimAssetID, StartStage,nil, BlendIn, BlendOut, bAutoStop, EndCBObj, EndCBFuncName, false)
end

function ViewControlAnimLibComponent:PlayAnimLibMontageCustomTag(AnimPlayReqTag, AnimAssetID, StartStage, bLoop, BlendIn, BlendOut, ActionBlendRule, FilterBone, FilterBoneDepth, bAutoStop, EndCBObj, EndCBFuncName, ...)
	return self:PlayAnimLibFeatureAsActionCustomTag(AnimPlayReqTag, AnimAssetID, StartStage, bLoop, bAutoStop, BlendIn, BlendOut, EndCBObj, EndCBFuncName, nil, nil, ActionBlendRule, FilterBone, FilterBoneDepth, ...)
end

function ViewControlAnimLibComponent:SetAnimFeatureStartTime(ID, NewTime)
	local AnimFeature = self.AnimFeatureMap[ID]
	if AnimFeature then
		AnimFeature:SetAnimTimeAt(NewTime)
	end
end

function ViewControlAnimLibComponent:SetAnimFeaturePlayRate(ID, NewPlayRate)
	local AnimFeature = self.AnimFeatureMap[ID]
	if AnimFeature then
		AnimFeature:SetPlayRate(NewPlayRate)
	end
end

-- 停止最近的一个全身动画，若没有ID
function ViewControlAnimLibComponent:StopAnimLibMontage(ID, BlendOutTime)
	ID = ID or self.LatestAnimFeatureID

	local AnimFeatureMap = self.AnimFeatureMap
	if AnimFeatureMap[ID] then
		AnimFeatureMap[ID]:Stop(false, BlendOutTime)
	end
end

-- 内部函数 请勿调用 统一接口
function ViewControlAnimLibComponent:PlayAnimLibFeatureAsPerformance(AnimAssetID, StartStage, bLoop, BlendIn, BlendOut, bAutoStop, EndCallBackObj, EndCallBackFuncName, bRefreshViewWhenSameMontage, ExternObjectTagMap, ActionBlendRule, FilterBone, FilterBoneDepth, ...)
	local NewFeature, Duration = self:CreateFeatureByFeatureByType(ViewAnimConst.EAnimPlayReqTag.Performance, AnimAssetID, StartStage, BlendIn, BlendOut, bLoop, bAutoStop, EndCallBackObj, EndCallBackFuncName, bRefreshViewWhenSameMontage, ExternObjectTagMap, ActionBlendRule, FilterBone, FilterBoneDepth)
	if NewFeature then
		NewFeature:SetCallBackParams(...)
		NewFeature:Run()
		local UniqueID = NewFeature.UniqueID
		if UniqueID then
			self.AnimFeatureMap[UniqueID] = NewFeature
			self.LatestAnimFeatureID = UniqueID
			return NewFeature.UniqueID, Duration
		end
	end

	return nil, nil
end

function ViewControlAnimLibComponent:PlayAnimLibFeatureAsUpper(AnimAssetID, StartStage, bLoop, BlendIn, BlendOut, bAutoStop, EndCallBackObj, EndCallBackFuncName, bRefreshViewWhenSameMontage, ExternObjectTagMap, FilterBone, FilterBoneDepth, bActionMeshSpaceBlend, ...)
	local NewFeature, Duration = self:CreateFeatureByFeatureByType(ViewAnimConst.EAnimPlayReqTag.UpperPerformance, AnimAssetID, StartStage, BlendIn, BlendOut, bLoop, bAutoStop, EndCallBackObj, EndCallBackFuncName, bRefreshViewWhenSameMontage, ExternObjectTagMap, nil, FilterBone, FilterBoneDepth, bActionMeshSpaceBlend)
	if NewFeature then
		NewFeature:SetCallBackParams(...)
		NewFeature:Run()
		local UniqueID = NewFeature.UniqueID
		if UniqueID then
			self.AnimFeatureMap[UniqueID] = NewFeature
			self.LatestUpperAnimFeatureID = UniqueID
			return NewFeature.UniqueID, Duration
		end
	end

	return nil, nil
end

function ViewControlAnimLibComponent:PlayAnimLibFeatureAsActionCustomTag(AnimPlayReqTag, AnimAssetID, StartStage, bLoop, bAutoStop, BlendIn, BlendOut, EndCallBackObj, EndCallBackFuncName, bRefreshViewWhenSameMontage, ExternObjectTagMap, ActionBlendRule, FilterBone, FilterBoneDepth, ...)
	local NewFeature, Duration = self:CreateFeatureByFeatureByType(AnimPlayReqTag, AnimAssetID, StartStage, BlendIn, BlendOut, bLoop, bAutoStop, EndCallBackObj, EndCallBackFuncName, bRefreshViewWhenSameMontage, ExternObjectTagMap, ActionBlendRule, FilterBone, FilterBoneDepth)
	if NewFeature then
		NewFeature:SetCallBackParams(...)
		NewFeature:Run()
		local UniqueID = NewFeature.UniqueID
		if UniqueID then
			self.AnimFeatureMap[UniqueID] = NewFeature
			self.LatestAnimFeatureID = UniqueID
			return NewFeature.UniqueID, Duration
		end
	end

	return nil, nil
end

---动画列表播放
---@param AnimSeq: 动画Lib的list
---@param DurationSeq: 持续时间的list, 不填则默认是动画一次循环的时间
---@param bAnimQueueLoop: 整个动画循环结束后是否重头播放
---@param BlendInTime: 每一段动画的渐入时间
---@param BlendOutTime: 每一段动画的渐出时间
---@param EndCBObj: AnimSeq播放结束后的回调对象，主动结束不回调
---@param EndCBFuncName: AnimSeq播放结束后的回调函数名，主动结束不回调
---@param ...: 回调额外参数
---@return ID: (nil 为失败)
function ViewControlAnimLibComponent:PlayAnimList(AnimSeq, DurationSeq, bAnimQueueLoop, BlendInTime, BlendOutTime, EndCBObj, EndCBFuncName, ...)
	if AnimSeq == nil or DurationSeq == nil then
		return
	end

	if self.CurrentFeatureList then
		self.CurrentFeatureList:Stop(false)
		self.CurrentFeatureList = nil
	end

	local CurrentFeatureList = AnimFeatureList.GetFromPool()
	self.CurrentFeatureList = CurrentFeatureList
	CurrentFeatureList:StartRunList(self.eid, AnimSeq, DurationSeq, bAnimQueueLoop, BlendInTime, BlendOutTime, EndCBObj, EndCBFuncName, ...)
	
	return CurrentFeatureList.UniqueID
end

-- 所有的主动Stop都没有回调
function ViewControlAnimLibComponent:StopAnimList(ID)
	local CurrentFeatureList = self.CurrentFeatureList
	if CurrentFeatureList and CurrentFeatureList.UniqueID == ID then
		CurrentFeatureList:Stop(false)
	end
end

function ViewControlAnimLibComponent:OnFeatureListEnd(FeatureID)
	local CurrentFeatureList = self.CurrentFeatureList
	if CurrentFeatureList and CurrentFeatureList.UniqueID == FeatureID then
		self.CurrentFeatureList = nil
	end
end

function ViewControlAnimLibComponent:CreateFeatureByFeatureByType(AnimPlayReqTag, AnimAssetID, StartStage, BlendInTime, BlendOutTime, bLoop, bAutoStop, EndObj, EndCB, bRefreshViewWhenSameMontage, ExternObjectTagMap, ActionBlendRule, FilterBone, FilterBoneDepth, bActionMeshSpaceBlend)
	local AnimPlayReqDefaultConfig = ViewAnimConst.EAnimPlayReqTagDefaultConfig[AnimPlayReqTag]
	if not AnimPlayReqDefaultConfig then
		self:DebugFmt("ViewControlAnimLibComponent AnimPlayReqTag Not Contain Tag's Config:%s Eid:%s", AnimPlayReqTag, self.eid)
		return nil
	end

	local AnimLibData = self:GetAnimFeatureData(AnimAssetID)

	if not AnimLibData then
		if _G.UE_EDITOR and Game.HeadInfoManager then
			local ModelID = self:GetConfigModelID()
			local AnimDataID = self:GetCurrentWorkingAnimAssetID()
			local MorphID = self.MorphID
			--Game.HeadInfoManager:DebugLogMsgAsBubble(self.eid, "动画播放错误,未找到对应动画配置. AssetID : %s ,TemplateID:%s,ModelID : %s ,AnimDataID : %s, MorphID : %s" ,AnimAssetID,self.TemplateID,ModelID,AnimDataID,MorphID)
			self:WarningFmt("动画播放错误,未找到对应动画配置. AssetID: %s, TemplateID: %s, ModelID: %s, AnimDataID: %s, MorphID: %s" , AnimAssetID, self.TemplateID, ModelID, AnimDataID, MorphID)
		end
		
		self:WarningFmt("[ViewControlAnimLibComponent:CreateFeatureByFeatureByType]Anim LibData Not Found. \n AssetID : %s ,TemplateID:%s, Pos: %s"
		,AnimAssetID,self.TemplateID,self.GetPosition and  tostring(self:GetPosition()) or "NO_VIEW_POS")
		return nil, 0
	end

	if BlendOutTime == nil or BlendOutTime == 0 then
		BlendOutTime = ViewAnimConst.ANIM_LIB_ANIMATION_BLEND_OUT_TIME --TODO：修复BlendOut跳帧问题
	end
	
	if AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.Sequence or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimWithAttachItems then
		local NewFeature = AnimLibFeature_SingleSequenceWithAttach:GetFromPool()
		NewFeature:Init(self.eid, AnimPlayReqTag, AnimAssetID, AnimLibData, BlendInTime, BlendOutTime, bLoop, bAutoStop, EndObj, EndCB, bRefreshViewWhenSameMontage, ExternObjectTagMap, ActionBlendRule, FilterBone, FilterBoneDepth, bActionMeshSpaceBlend)
		return NewFeature, AnimLibData.AnimLen
	elseif AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.ThreePhaseAnim or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.ThreePhaseAnimWithAttach
			or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimList or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimListWithAttach then
		local NewFeature = AnimLibFeature_AnimListWithAttach:GetFromPool()
		NewFeature:Init(self.eid, AnimPlayReqTag, AnimAssetID, AnimLibData, BlendInTime, BlendOutTime, bLoop, bAutoStop, EndObj, EndCB, bRefreshViewWhenSameMontage, ExternObjectTagMap, StartStage, ActionBlendRule, FilterBone, FilterBoneDepth, bActionMeshSpaceBlend)
		return NewFeature, AnimLibData.AnimLen
	end

	return nil, 0
end

function ViewControlAnimLibComponent:OnAnimFeatureNotify(AttachItemName, AttachNotifyType)
	for _, AnimFeature in pairs(self.AnimFeatureMap) do
		AnimFeature:DoAnimFeatureNotify(AttachItemName, AttachNotifyType)
	end
end

function ViewControlAnimLibComponent:OnAnimFeatureEnd(FeatureID)
	self.AnimFeatureMap[FeatureID] = nil
end

--endregion AnimFeature

-- 播放表情动画
-- bLoop: 动画本身是否循环
function ViewControlAnimLibComponent:PlayFaceAnimation(AnimLibAssetID, bLoop, bAutoStop, EndCBObj, EndCBFuncName)
	local NewFeature, Duration = self:CreateFeatureByFeatureByType(ViewAnimConst.EAnimPlayReqTag.Face, AnimLibAssetID, nil, nil, nil, bLoop, bAutoStop, EndCBObj, EndCBFuncName)
	if NewFeature then
		NewFeature:Run()
		local UniqueID = NewFeature.UniqueID
		if UniqueID then
			self.AnimFeatureMap[UniqueID] = NewFeature
			return NewFeature.UniqueID, Duration
		end
	end

	return nil, nil
end

function ViewControlAnimLibComponent:PlaySkillAnimationByAnimLib(AnimAssetID, InMask, InRate, InBlendIn, InBlendOut, InTimeToStartMontageAt, bStopAllMontages, bDisableRootMotion, bLoop)
	if not AnimAssetID then
		self:Error("ViewControlAnimLibComponent:PlaySkillAnimationByAnimLib Get Empty AnimAssetID!")
		return 0
	end

	local AnimPath = self:GetAnimPathAndLenFromAnimFeatureForSingleAnimation(AnimAssetID)
	if not AnimPath then
		return 0
	end

	return self:PlaySkillAnimationByPath(AnimPath, InMask, InRate, InBlendIn, InBlendOut, InTimeToStartMontageAt, bStopAllMontages, bDisableRootMotion, bLoop)
end

--------------------------Just For Dialogue Funtion Start --------------------------------------------------------------------------------------------
function ViewControlAnimLibComponent:PlayDeadOrReviveAnimation(bIsDead)
	local AnimAssetID = bIsDead and "Dead" or "Revive"
	local AnimPath, AnimLen = self:GetAnimPathAndLenFromAnimFeatureForSingleAnimation(AnimAssetID)
	if not AnimPath then
		return nil, nil
	end

	return self:PlayDeadOrReviveAnimationByPath(AnimPath), AnimLen
end



--FeatureID 对应播放的FeatureID
--bFinishPlayCurrentLoop 为true时会等等当前loop播完再衔接下一个，否则直接跳转下一个phase
function ViewControlAnimLibComponent:FeatureProgressToNextPhase(FeatureID, bFinishPlayCurrentLoop)
	local AnimFeature = self.AnimFeatureMap[FeatureID]
	if AnimFeature then
		AnimFeature:ManualProgressToNext(bFinishPlayCurrentLoop)
	end
end
--------------------------Just For Dialogue Funtion End ---------------------------

----------------------------------------------For Editor Preview-----------------------------------

-- 仅供编辑器预览环境使用 RunTime禁止调用
function ViewControlAnimLibComponent:PlayAnimLibFeatureForEditorPreview(AnimDataID, AnimTypeName, bLoop, bNewVersion)
	local AnimLibData = AnimLibHelper.GetAnimFeatureData(AnimDataID, AnimTypeName)

	if not AnimLibData then
		AnimLibData = AnimLibHelper.GetAnimFeatureDataFromPartDataOnlyEditor(AnimDataID, AnimTypeName)
		if not AnimLibData then
			return 0
		end
	end

	self:StopAnimLibMontage()

	local NewFeature = nil
	if AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.Sequence or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimWithAttachItems then
		NewFeature = AnimLibFeature_SingleSequenceWithAttach:GetFromPool()
		NewFeature:Init(self.eid, ViewAnimConst.EAnimPlayReqTag.Performance, AnimTypeName, AnimLibData, 0, 0, bLoop, true, nil, nil, true, nil, nil, nil, nil, nil)
	elseif AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.ThreePhaseAnim or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.ThreePhaseAnimWithAttach
			or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimList or AnimLibData.AnimFeatureTag == Enum.EAnimFeatureTags.AnimListWithAttach then
		NewFeature = AnimLibFeature_AnimListWithAttach:GetFromPool()
		NewFeature:Init(self.eid, ViewAnimConst.EAnimPlayReqTag.Performance, AnimTypeName, AnimLibData, 0, 0, bLoop, true, nil, nil, nil, nil, nil, nil, nil, ni, nil)
	end
	
	if NewFeature then
		NewFeature:Run()
		return NewFeature.UniqueID
	else
		self:ErrorFmt("[ViewControlAnimLibComponent:PlayAnimLibFeatureForEditorPreview]Create AnimLibFeature Failed : Feature Tag not supported, AnimTypeName : %s ", AnimTypeName)
	end
end


-- ================================================基于动画库的业务动作Override 接口======================================

function ViewControlAnimLibComponent:SwitchWeaponFightIdleAnimByAnimLibAssetAsync(ReplaceAssetID)
	return self:InnerSwitchLocoAnimByAnimLibAssetAsync(ViewAnimConst.IDLE_FIGHT_ANIM,ReplaceAssetID,ViewAnimConst.ANIM_LOCO_CONTAINER.WEAPON_LOCO_OVERRIDE_SEMANTIC)
end

function ViewControlAnimLibComponent:SwitchIdleAnimByAnimLibAssetAsync(ReplaceAssetID)
	return self:InnerSwitchLocoAnimByAnimLibAssetAsync(ViewAnimConst.IDLE,ReplaceAssetID,ViewAnimConst.ANIM_LOCO_CONTAINER.IDLEANIM_LOCO_OVERRIDE_SEMANTIC)
end

-- 目前还没有支持内部replace的逻辑
function ViewControlAnimLibComponent:InnerSwitchLocoAnimByAnimLibAssetAsync(TargetAssetID, ReplaceAssetID, SemanticTag)
	local AnimPath = self:GetAnimPathAndLenFromAnimFeatureForSingleAnimation(ReplaceAssetID)
	if not AnimPath then
		self:DebugFmt("ViewControlAnimLibComponent:InnerSwitchLocoAnimByAnimLibAssetAsync entity:%s %s,  TargetAssetID:%s   ReplaceAssetId:%s  SemanticTag:%s", self.eid, self.__cname, TargetAssetID, ReplaceAssetID, SemanticTag)
		return nil
	end

	self.LocoReplaceMap:Add(TargetAssetID, AnimPath)
	local LoadID = self:TryObtainLocoAnimSequencesOverrideAndAssetReference(SemanticTag, self.LocoReplaceMap)
	self.LocoReplaceMap:Clear()

	if LoadID ~= -1 then
		self.AssetSwitchIDs = self.AssetSwitchIDs or {}
		self.AssetSwitchIDs[LoadID] = {
			TargetAssetID = TargetAssetID,
			ReplaceAssetID = ReplaceAssetID,
			SemanticTag = SemanticTag,
			IsObtained = LoadID ~= -1
		}
	end

	self:DebugFmt("ViewControlAnimLibComponent entity:%s %s Require InnerSwitchLocoAnimByAnimLibAssetAsync Target:%s -> Replace With:%s SemanticTag:%s LoadID:%s", self.eid, self.__cname, TargetAssetID, ReplaceAssetID, SemanticTag, LoadID)
	return LoadID
end

function ViewControlAnimLibComponent:SwitchMountRideOverrideAnims(AnimLibAssetID, AnimLibLocoGroup)
	-- 后面如果有其他group、animlib part的处理, 策划会出单再处理
	self:InnerSwitchAnimGroupByAnimLibAssetAsync('MountRideAnimOverrideLoadID', AnimLibAssetID,
		AnimLibLocoGroup,
		ViewAnimConst.ANIM_LOCO_CONTAINER.MOUNT_LOCO_OVERRIDE_SEMANTIC)
end

function ViewControlAnimLibComponent:SwitchFashionOverrideAnims(AnimLibAssetID)
	-- 后面如果有其他group、animlib part的处理, 策划会出单再处理
	self:InnerSwitchAnimGroupByAnimLibAssetAsync('FashionAnimOverrideLoadID', AnimLibAssetID,
		ViewAnimConst.ANIMLIB_LOCOMOTION_GROUP_NAMES.OnGround,
		ViewAnimConst.ANIM_LOCO_CONTAINER.FASHION_LOCO_OVERRIDE_SEMANTIC)
end

-- 自带取消、替换逻辑
---InnerSwitchAnimGroupByAnimLibAssetAsync
---@param AnimLibAssetID  为nil的时候是取消override
function ViewControlAnimLibComponent:InnerSwitchAnimGroupByAnimLibAssetAsync(LoadIDVarName, AnimLibAssetID, AnimGroupName, InSemanticTag)
	if AnimLibAssetID == nil then
		if self[LoadIDVarName] ~= nil then
			self:CancelAnimOverrideByAnimLib(self[LoadIDVarName])
			self[LoadIDVarName] = nil
		end
		return 0
	end

	-- 上次有流程了
	if self[LoadIDVarName] ~= nil then
		self:CancelAnimOverrideByAnimLib(self[LoadIDVarName])
	end

	local GroupPreLoadData = AnimLibHelper.GetAnimFeatureData(AnimLibAssetID, AnimGroupName)
	local AssetPaths = GroupPreLoadData.AnimAssets
	local ReplaceKeys = GroupPreLoadData.AnimStates

	local Index = 1
	local AssetNum = #ReplaceKeys
	while Index <= AssetNum do
		self.LocoReplaceMap:Add(ReplaceKeys[Index], AssetPaths[Index])
		Index = Index + 1
	end

	local LoadID = self:TryObtainLocoAnimSequencesOverrideAndAssetReference(InSemanticTag, self.LocoReplaceMap)
	self.LocoReplaceMap:Clear()

	if LoadID ~= -1 then
		self[LoadIDVarName] = LoadID
		self.AssetSwitchIDs = self.AssetSwitchIDs or {}
		self.AssetSwitchIDs[LoadID] = {
			SemanticTag = InSemanticTag,
			LoadIDVarName= LoadIDVarName,
			IsObtained = LoadID ~= -1,
		}
	end

	return LoadID
end

function ViewControlAnimLibComponent:CancelAnimOverrideByAnimLib(InLoadId)
	self:DebugFmt("ViewControlAnimLibComponent entity:%s %s Require CancelAnimOverrideByAnimLib LoadID:%s", self.eid, self.__cname, InLoadId)
	local ReplaceData = self.AssetSwitchIDs[InLoadId]
	-- 已经被清理掉了
	if ReplaceData == nil then
		return
	end
	if ReplaceData.IsObtained then
		self:ReleaseLocoSequenceMappingWithPriorityIndexAndAssetReference(ReplaceData.SemanticTag)
	end
	self.AssetSwitchIDs[InLoadId] = nil
end
-- ================================================基于动画库的业务动作Override 接口  END======================================

-----------------------------供剧情使用预加载相关动画资源------------------------------------
function ViewControlAnimLibComponent:PreLoadAndCacheMontageForDialogue(AnimTable, AnimSlotName, BlendInTime, BlendOutTime, Obj, CallBackFuncName)
	if not AnimTable or next(AnimTable) == nil then
		return
	end

	local Result = {}
	local FacadeConfigData = Game.TableData.GetFacadeControlDataTable()
	for FacadeControlID, AnimTypeNames in pairs(AnimTable) do
		local FacadeData = FacadeConfigData[FacadeControlID]
		if FacadeData then
			local AnimID = FacadeData.AnimAssetID
			if not AnimID or AnimID == "" then
				local ModelData = Game.ActorAppearanceManager:GetModelLibData(FacadeData.ModelID)
				AnimID = ModelData and ModelData.AnimFlag or ""
			end

			if AnimID and AnimID ~= "" then
				for _, AnimType in pairs(AnimTypeNames) do
					local AnimPath = AnimLibHelper.GetAnimPath(AnimID, AnimType)
					if AnimPath ~= nil then
						if type(AnimPath) == "table" then
							table.move(AnimPath, 1, #AnimPath, #Result + 1, Result)
						else
							Result[#Result + 1] = AnimPath
						end
					end
				end
			end
		end
	end
	
	return self:PreLoadAndCacheMontageByPathList(Result, AnimSlotName, BlendInTime, BlendOutTime, 1, Obj, CallBackFuncName)
end

--打印信息使用,业务代码请勿使用
function ViewControlAnimLibComponent:GetCurRunningActionFeatureName()
	local LatestAnimFeatureID = self.LatestAnimFeatureID
	if not LatestAnimFeatureID then
		return nil, nil, nil, nil
	end

	local ActionFeature = self.AnimFeatureMap[LatestAnimFeatureID]
	if not ActionFeature then
		return nil, nil, nil, nil
	end

	if ActionFeature then
		local AnimFeatureName, AnimStateName = ActionFeature:GetPlayingAnimInfo()
		if self.HalfFeatureID and self.AnimFeatureMap[self.HalfFeatureID] then
			local LowerAnimFeatureName, LowerAnimStateName = self.AnimFeatureMap[self.HalfFeatureID]:GetPlayingAnimInfo()
			return AnimFeatureName, AnimStateName, LowerAnimFeatureName, LowerAnimStateName
		end

		return AnimFeatureName, AnimStateName, nil, nil
	end

	return nil, nil, nil, nil
end

return ViewControlAnimLibComponent