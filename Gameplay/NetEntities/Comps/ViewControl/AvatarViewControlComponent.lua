local ViewControlBaseComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlBaseComponent")
local ViewControlAnimComponent = require("Gameplay.NetEntities.Comps.ViewControl.NewViewControlAnimComponent")-- luacheck: ignore
local ViewControlAppearanceComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlAppearanceComponent")
local ViewControlAttachComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlAttachComponent")
local ViewControlAudioComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlAudioComponent")
local ViewControlFxComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlFxComponent")
local ViewControlDynamicMeshComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlDynamicMeshComponent")
local ViewControlGazeComponent = kg_require("Gameplay.NetEntities.Comps.ViewControl.ViewControlGazeComponent")
local ViewControlRotateComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlRotateComponent")
local ViewControlVisibleComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlVisibleComponent")
local NewViewControlMaterialComponent = require("Gameplay.NetEntities.Comps.ViewControl.NewViewControlMaterialComponent")
local ViewControlAnimLibComponent =  require("Gameplay.NetEntities.Comps.ViewControl.ViewControlAnimLibComponent")
local ViewControlBlueprintComponent = require("Gameplay.NetEntities.Comps.ViewControl.ViewControlBlueprintComponent")
local LocalPerformanceAnimControlComponent = require("Gameplay.NetEntities.Comps.ViewControl.LocalPerformanceAnimControlComponent")
local entityUtils = kg_require("Gameplay.NetEntities.EntityUtils")

-- luacheck:ignore
AvatarViewComponents = {
    ViewControlBaseComponent,
    ViewControlAnimComponent,
    ViewControlAppearanceComponent,
    ViewControlAttachComponent,
    ViewControlAudioComponent,
    ViewControlFxComponent,
    ViewControlDynamicMeshComponent,
    ViewControlGazeComponent,
    ViewControlRotateComponent,
    ViewControlVisibleComponent,
    NewViewControlMaterialComponent,
    ViewControlAnimLibComponent,
    ViewControlBlueprintComponent,
    LocalPerformanceAnimControlComponent,
}

AvatarViewControlComponent = DefineComponent(
    "AvatarViewControlComponent", 
    table.unpack(AvatarViewComponents)
)

entityUtils.AutoAddComponentStageCallFunc(AvatarViewControlComponent)

function AvatarViewControlComponent:ctor()
    
end

function AvatarViewControlComponent:dtor()

end

return AvatarViewControlComponent

