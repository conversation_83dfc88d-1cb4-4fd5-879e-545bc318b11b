local MaterialEffectConst = kg_require("Gameplay.Effect.MaterialEffectConst")
local UMeshComponentClass =  import("MeshComponent")
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")
local DISSOLVE_EFFECT_CONFIG_ID = MaterialEffectConst.DISSOLVE_EFFECT_CONFIG_ID
local NewHeadInfoConst = kg_require("Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoConst")

local DISSOLVE_MATERIAL_EFFECT_TYPE = MaterialEffectConst.DISSOLVE_MATERIAL_EFFECT_TYPE
local IsDissolveInDissolveType = MaterialEffectConst.IsDissolveInDissolveType

---------------------------------
--- ViewControl  Visible接口
--------------------------------
ViewControlVisibleComponent = DefineComponent("ViewControlVisibleComponent")

ViewControlVisibleComponent.DissolveDefaultTime = 2
ViewControlVisibleComponent.DissolveDefaultColor = { R = 10, G = 0.69, B = 0.77, A = 1 }


FORCE_VISIBLE_CONTROL_BY_CINEMATIC = 'Cinematic'	--luacheck: ignore
FORCE_VISIBLE_CONTROL_BY_DIALOGUE = 'Dialogue'	--luacheck: ignore

function ViewControlVisibleComponent:ctor()
	self:InitializeVisible()
end

function ViewControlVisibleComponent:InitializeVisible()
	self.InVisibleReasons = {}
	self.FakeInVisibleReasons = {}
	self.CurrentEntityInvisible = false
	self.bIsTrueInVisible = false
	self.IsNeedNpcHideRule = false
	
	-- 策划定制的一套现较为底层的显隐控制规则(等级/天气/性别/任务条件), 详见https://docs.corp.kuaishou.com/d/home/<USER>
	self.InvisibleByQuestControl = false
	-- 根据Entity类型进行统一的Invisible控制
	self.InvisibleByEntityCategory = false
	-- 根据对话进行指定角色的显隐控制
	self.InvisibleByDialogueRule = false
	
	-- 根据cinematic的距离进行自动显隐控制
	self.InvisibleByCinematicRangeHidden = false
	
	-- Cinematic、Dialogue强制显隐控制
	self.ForceVisibleControl = nil
	self.ForceVisibleControlReason = nil
end

function ViewControlVisibleComponent:__component_AfterEnterWorld__()
	-- 先注释掉， 感觉没有啥用, 先观察 @孙亚
	--Game.EventSystem:Publish(_G.EEventTypes.ROLE_ON_VISABLE_CHANGED, self:uid(), not (self.bIsTrueInVisible))

	-- 策划定制的一套现较为底层的显隐控制规则
	-- #70727 任务NPC隐藏 + 采集物 相关功能从NpcActor转移至此
	if self.IsNeedNpcHideRule and Game.NPCManager and Game.NPCManager.IsNPCHide(self) then
		self:SetInvisibleByQuestControl(true, nil, true)
	end

	-- 剧情对话根据配置ID进行显影控制, 这个规则是给Npc定制的
	if self.isNpc == true then
		Game.WorldManager:TryInitInvisibleByDialogueWithConfigID(self)
		
		-- 给对话过程中, 任务NPC 进出对话范围进行显隐控制的机制
		-- NPC ExitWorld时会保底清理一次; 另外, 对话结束的时候, 会拿所有Task Npc进行一次unregister一次
		if Game.WorldManager.ContinuousHiddenTriggerID ~= nil and self.NpcType == Enum.ENpcTypeData.Task then
			self:RegisterGridLogicDetector(
				true,
				WorldViewConst.DEFAULT_WORLD_GRID_DETECTOR_RADIUS,
				WorldViewConst.WORLD_GRID_TRIGGER_LOGIC_ENUM.DIALOGUE_CONTINUOUS_HIDDEN
			)
		end
		
	end
	
	-- 根据Entity类型进行统一的Invisible控制
	if Game.WorldManager:IsEntityHiddenByEntityCategoryRule(self) then
		self:SetInvisibleByEntityCategory(true, nil, true)
	end
	
	--更新服务器控制
	if self.bSeverControlVisible ~= nil then
		if self.bSeverControlVisible then
			self:SetActorVisible(Enum.EInVisibleReasons.ServerControl, nil, true)
		else
			self:SetActorInVisible(Enum.EInVisibleReasons.ServerControl, nil, true)
		end
	end
	
    self:CheckInvisible(nil, true)
end

function ViewControlVisibleComponent:__component_ExitWorld__()
	if self.DissolveTimer then
		Game.TimerManager:StopTimerAndKill(self.DissolveTimer)
	end
	-- 主玩家不摧毁,  退场要重置
	if Game.me == self then
		self:InitializeVisible()
	end
end

function ViewControlVisibleComponent:SetInvisibleByQuestControl(IsInvisible, dissolveConfig, NoImmediatelyCheck)
	self.InvisibleByQuestControl = IsInvisible

	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

function ViewControlVisibleComponent:SetInvisibleByEntityCategory(IsInvisible, dissolveConfig, NoImmediatelyCheck)
	self.InvisibleByEntityCategory = IsInvisible
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

function ViewControlVisibleComponent:SetInvisibleByDialogueRule(IsInvisible, dissolveConfig, NoImmediatelyCheck)
	self.InvisibleByDialogueRule = IsInvisible
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

function ViewControlVisibleComponent:SetInvisibleByCinematicRangeHidden(IsInvisible, dissolveConfig, NoImmediatelyCheck)
	self.InvisibleByCinematicRangeHidden = IsInvisible
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

--   -----------------------剧情Cutscene控制专用------------------------
function ViewControlVisibleComponent:ForceToVisibleByCinematic(dissolveConfig, NoImmediatelyCheck)
	if self.ForceVisibleControlReason ~= nil and self.ForceVisibleControlReason ~= FORCE_VISIBLE_CONTROL_BY_CINEMATIC then
		self:ErrorFmt("[ViewControlVisibleComponent:ForceToVisibleByCinematic]  Force Control Already Obtained By:%s", self.ForceVisibleControlReason)
	end
	
	self.ForceVisibleControlReason = FORCE_VISIBLE_CONTROL_BY_CINEMATIC
	self.ForceVisibleControl = true
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

function ViewControlVisibleComponent:ForceToInvisibleByCinematic(dissolveConfig, NoImmediatelyCheck)
	if self.ForceVisibleControlReason ~= nil and self.ForceVisibleControlReason ~= FORCE_VISIBLE_CONTROL_BY_CINEMATIC then
		self:ErrorFmt("[ViewControlVisibleComponent:ForceToVisibleByCinematic]  Force Control  Already Obtained  By:%s", self.ForceVisibleControlReason)
	end
	self.ForceVisibleControlReason = FORCE_VISIBLE_CONTROL_BY_CINEMATIC
	self.ForceVisibleControl = false
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

function ViewControlVisibleComponent:CleanForceVisibleControlByCinematic(NoImmediatelyCheck)
	if self.ForceVisibleControlReason ~= nil and self.ForceVisibleControlReason ~= FORCE_VISIBLE_CONTROL_BY_CINEMATIC then
		self:ErrorFmt("[ViewControlVisibleComponent:ForceToVisibleByCinematic]  Force Control  Should Release By:%s", self.ForceVisibleControlReason)
	end

	self.ForceVisibleControlReason = nil
	self.ForceVisibleControl = nil
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible()
	end
end

--   -----------------------Dialogue对话控制专用------------------------
function ViewControlVisibleComponent:ForceToVisibleByDialogue(dissolveConfig, NoImmediatelyCheck)
	if self.ForceVisibleControlReason ~= nil and self.ForceVisibleControlReason ~= FORCE_VISIBLE_CONTROL_BY_DIALOGUE then
		self:ErrorFmt("[ViewControlVisibleComponent:ForceToVisibleByCinematic]  Force Control Already Obtained By:%s", self.ForceVisibleControlReason)
	end

	self.ForceVisibleControlReason = FORCE_VISIBLE_CONTROL_BY_DIALOGUE
	self.ForceVisibleControl = true
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

function ViewControlVisibleComponent:ForceToInvisibleByDialogue(dissolveConfig, NoImmediatelyCheck)
	if self.ForceVisibleControlReason ~= nil and self.ForceVisibleControlReason ~= FORCE_VISIBLE_CONTROL_BY_DIALOGUE then
		self:ErrorFmt("[ViewControlVisibleComponent:ForceToVisibleByCinematic]  Force Control Already Obtained By:%s", self.ForceVisibleControlReason)
	end

	self.ForceVisibleControlReason = FORCE_VISIBLE_CONTROL_BY_DIALOGUE
	self.ForceVisibleControl = false
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(dissolveConfig)
	end
end

function ViewControlVisibleComponent:CleanForceVisibleControlByDialogue(NoImmediatelyCheck)
	if self.ForceVisibleControlReason ~= nil and self.ForceVisibleControlReason ~= FORCE_VISIBLE_CONTROL_BY_DIALOGUE then
		self:ErrorFmt("[ViewControlVisibleComponent:ForceToVisibleByCinematic]  Force Control  Should Release By:%s", self.ForceVisibleControlReason)
	end

	self.ForceVisibleControlReason = nil
	self.ForceVisibleControl = nil
	if NoImmediatelyCheck ~= true then
		self:CheckInvisible()
	end
end


--region assorted
function ViewControlVisibleComponent:SetNpcVisibleWithDissolveForCutSceneHidden(bVisible)
	if bVisible then
		local DissolveData = Game.TableData.GetDissolveEffectDataRow(DISSOLVE_EFFECT_CONFIG_ID.CROWD_DISSOLVE_IN)
		self:SetInvisibleByCinematicRangeHidden(false, DissolveData)
	else
		local DissolveData = Game.TableData.GetDissolveEffectDataRow(DISSOLVE_EFFECT_CONFIG_ID.CROWD_DISSOLVE_OUT)
		self:SetInvisibleByCinematicRangeHidden(true, DissolveData)
	end
end

---@param DissolveConfig table
--- DissolveConfig默认是按照 Effects_特效表 DissolveEffect_溶解特效中的配置参数进行溶解, 也支持业务自行传入参数进行溶解
--- DissolveConfig.DissolveType dissolve类型
--- DissolveConfig.DissolveTime dissolve时长
--- DissolveConfig.DissolveColor dissolve颜色参数
--- DissolveConfig.bPropagateDissolveToAttachItem 是否将dissolve传递给子部件
--- DissolveConfig.EntityEffectID dissolve时播放的粒子效果
--- DissolveConfig.DissolveCurve 默认dissolve为线性插值溶解, 给定曲线以后会按照曲线溶解
function ViewControlVisibleComponent:SetActorInVisible(VisibleReason, DissolveConfig, NoImmediatelyCheck)
	if self.bMainPlayer then
		self:Debug("[ViewControlVisibleComponent:SetActorInVisible] MainChar Invisible", VisibleReason)
	end
	if VisibleReason < Enum.EInVisibleReasons.FakeInvisibleReasonStart then
		self.InVisibleReasons[VisibleReason] = true
	else
		self.FakeInVisibleReasons[VisibleReason] = true
	end

	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(DissolveConfig)
	end
end

---@param DissolveConfig table
--- DissolveConfig默认是按照 Effects_特效表 DissolveEffect_溶解特效中的配置参数进行溶解, 也支持业务自行传入参数进行溶解
--- DissolveConfig.DissolveType dissolve类型
--- DissolveConfig.DissolveTime dissolve时长
--- DissolveConfig.DissolveColor dissolve颜色参数
--- DissolveConfig.bPropagateDissolveToAttachItem 是否将dissolve传递给子部件
--- DissolveConfig.EntityEffectID dissolve时播放的粒子效果
--- DissolveConfig.DissolveCurve 默认dissolve为线性插值溶解, 给定曲线以后会按照曲线溶解
function ViewControlVisibleComponent:SetActorVisible(VisibleReason, DissolveConfig, NoImmediatelyCheck)
	if self.bMainPlayer then
		self:Debug("[ViewControlVisibleComponent:SetActorInVisible] MainChar Visible", VisibleReason)
	end

	
	self.InVisibleReasons[VisibleReason] = nil
	self.FakeInVisibleReasons[VisibleReason] = nil

	if NoImmediatelyCheck ~= true then
		self:CheckInvisible(DissolveConfig)
	end
end

function ViewControlVisibleComponent:SetVisibilityByDissolveDataEffect(DissolveEffectID, VisibleReason)
	if not self.bInWorld then
		self:AddCommandCache("SetVisibilityByDissolveDataEffect", DissolveEffectID, VisibleReason)
		return
	end

	local DissolveData = Game.TableData.GetDissolveEffectDataRow(DissolveEffectID)
	if DissolveData == nil then
		self:Error("invalid dissolve effect id", DissolveEffectID)
		return
	end

	if DissolveData.DissolveType == DISSOLVE_MATERIAL_EFFECT_TYPE.WEAPON_DISSOLVE_OUT then
		-- 对于武器的溶解来说, 只播放特效+模型消失, 不需要材质效果
		self:PlayWeaponDissolveNiagara(
			DissolveData.WeaponDissolveEffects[1], DissolveData.WeaponDissolveEffects[2], DissolveData.DissolveTime, DissolveData.DissolveType)
		self:SetActorInVisible(VisibleReason)

	elseif IsDissolveInDissolveType(DissolveData.DissolveType) then
		self:SetActorVisible(VisibleReason, DissolveData)
	else
		self:SetActorInVisible(VisibleReason, DissolveData)
	end
end

---@private
function ViewControlVisibleComponent:ApplyCurrentEntityVisible()
	if not self.bInWorld then
		return
	end
	
	local bVisible = not self.CurrentEntityInvisible
	if Game.BSManager.bIsInEditor then
		self.CppEntity:KAPI_Actor_SetIsTemporarilyHiddenInEditor(not bVisible)
	else
		self.CppEntity:KAPI_SetEntityVisible(bVisible, false)
	end
	
	--[[
	if self.ModifyActorVisible then
		self:ModifyActorVisible(bVisible)
	end
	
	if Game.BSManager.bIsInEditor then
		self.CppEntity:KAPI_Actor_SetIsTemporarilyHiddenInEditor(not bVisible)
	else
		self.CppEntity:KAPI_Actor_SetActorHiddenInGame(not bVisible)
	end

	if not self.bMainPlayer then
		if bVisible then
			self.CppEntity:KAPI_Actor_SetActorComponentTickEnabledByClass(bVisible, UMeshComponentClass)
		else
			if self.bDisableTickIfInvisible then
				self.CppEntity:KAPI_Actor_SetActorComponentTickEnabledByClass(bVisible, UMeshComponentClass)
			end
		end

		if bVisible then
			self.CppEntity:KAPI_Actor_SetActorTickEnabled(bVisible)
		else
			if self.bDisableTickIfInvisible then
				self.CppEntity:KAPI_Actor_SetActorTickEnabled(bVisible)
			end
		end

	end
	]]--

end

function ViewControlVisibleComponent:IsTrueInvisible()
    return self.bIsTrueInVisible
end

function ViewControlVisibleComponent:IsInvisible()
    return self.CurrentEntityInvisible
end

---@private 内部方法, 外部用的话使用 SetActorInVisible/SetActorVisible
function ViewControlVisibleComponent:CheckInvisible(DissolveConfig, bForceNotify)
    if not self.bInWorld then
        return false
    end

	local bActorInvisible = nil
	local NewTrueInvisible = false
	local NewFakeInVisible = false
	
	-- 有强制控制就用强制控制
	if self.ForceVisibleControl ~= nil then
		bActorInvisible = not self.ForceVisibleControl
		NewTrueInvisible = bActorInvisible
	else
		-- 只要有一个为Invisible 最终控制就是Invisible
		if self.InvisibleByQuestControl == true then
			bActorInvisible = true
			NewTrueInvisible = true
		elseif self.InvisibleByEntityCategory == true then
			bActorInvisible = true
			NewTrueInvisible = true
		elseif 	self.InvisibleByDialogueRule == true then
			bActorInvisible = true
			NewTrueInvisible = true
		elseif self.InvisibleByCinematicRangeHidden == true then
			bActorInvisible = true
			NewTrueInvisible = true
		else
			NewTrueInvisible = next(self.InVisibleReasons) ~= nil
			NewFakeInVisible = next(self.FakeInVisibleReasons) ~= nil
			bActorInvisible = (NewTrueInvisible or NewFakeInVisible)
		end
	end
	
    if bActorInvisible ~= self.CurrentEntityInvisible then
        self.CurrentEntityInvisible = bActorInvisible

        if self.DissolveTimer then
            Game.TimerManager:StopTimerAndKill(self.DissolveTimer)
        end

        if DissolveConfig ~= nil and DissolveConfig.DissolveTime > 0 then
            if bActorInvisible then
                --溶解退出,先溶解,计时改变Visibility
                self.DissolveTimer = Game.TimerManager:CreateTimerAndStart(
                        function()
                            self:ApplyCurrentEntityVisible()
                        end
                , DissolveConfig.DissolveTime * 1000, 1)
                self:StartDissolveEffect(DissolveConfig)
            else
                --溶解进入,先改变Visibility,再播放溶解进入
                self:ApplyCurrentEntityVisible()
                self:StartDissolveEffect(DissolveConfig)
            end
        else
            --无溶解,直接改变Visibility
            self:ApplyCurrentEntityVisible()
        end


		
		self:call_components("ActorVisibleChanged", not bActorInvisible)
	else
		if not self.CurrentEntityInvisible then
			-- 可见时再次溶解出现
            if DissolveConfig ~= nil and DissolveConfig.DissolveTime > 0 then
				self:StartDissolveEffect(DissolveConfig)
			end
		end
    end

	-- todo 这里应该只是为了做优化, 只有挂接物这种entity也有显隐控制, 而且数量不少, 所以这里应该只是为了做辨识; 根本原因还是后续逻辑是靠事件来做, 如果直接耦合到entity上来做, 就没有这个困扰 @孙亚
    if NewTrueInvisible ~= self.bIsTrueInVisible or bForceNotify then
		self.bIsTrueInVisible = NewTrueInvisible
		local uid = self:uid()
		local notNewTrueInvisible = not NewTrueInvisible
        Game.EventSystem:Publish(_G.EEventTypes.ROLE_ON_VISABLE_CHANGED, uid, notNewTrueInvisible)
		Game.UniqEventSystemMgr:Publish(uid, EEventTypesV2.ROLE_ON_VISIBLE_CHANGED, uid, notNewTrueInvisible)
		if self.isNpc then
			Game.NPCManager:OnRoleVisibilityChanged(uid, notNewTrueInvisible)	
		end
		Game.HUDInteractManager:OnRoleVisibleChanged(uid, notNewTrueInvisible)
		if self == Game.me and Game.StaminaSystem then
			Game.StaminaSystem:OnRoleVisibleChanged(notNewTrueInvisible)
		end
		if Game.NewHeadInfoSystem then
			Game.NewHeadInfoSystem:UpdateHeadInfo(uid)
		end
	end
	
	return true
end

function ViewControlVisibleComponent:OnDialogueNpcContinuousHiddenAsDetector(TriggerUID, LogicTriggerType, IsEnter)
	if IsEnter then
		self:SetNpcVisibleWithDissolveForCutSceneHidden(false)
	else
		self:SetNpcVisibleWithDissolveForCutSceneHidden(true)
	end
end


function ViewControlVisibleComponent:__component_AppendGamePlayDebugInfo__(debugInfos)
	table.insert(debugInfos, "<Title_Red>[ViewControlVisibleComponent]</>")
	self:DebugDumpTable(debugInfos, "InVisibleReasons", self.InVisibleReasons, 0, 10)
	self:DebugDumpTable(debugInfos, "FakeInVisibleReasons", self.FakeInVisibleReasons, 0, 10)
	table.insert(debugInfos, string.format("<Text>: CurrentEntityInvisible:%s, bIsTrueInVisible: %s</>", self.CurrentEntityInvisible, self.bIsTrueInVisible))
	table.insert(debugInfos, string.format("<Text>: InvisibleByQuestControl:%s, InvisibleByEntityCategory: %s</>", self.InvisibleByQuestControl, self.InvisibleByEntityCategory))
	table.insert(debugInfos, string.format("<Text>: InvisibleByDialogueRule:%s, InvisibleByCinematicRangeHidden: %s</>", self.InvisibleByDialogueRule, self.InvisibleByCinematicRangeHidden))
	table.insert(debugInfos, string.format("<Text>: ForceVisibleControl:%s, ForceVisibleControlReason:%s, bDisableTickIfInvisible:%s</>", self.ForceVisibleControl, self.ForceVisibleControlReason, self.CppEntity:KAPI_GetDisableTickIfInvisible()))
	table.insert(debugInfos, "")
end

return ViewControlVisibleComponent