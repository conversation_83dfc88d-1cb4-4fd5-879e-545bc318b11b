
local NetworkManager = require "Framework.DoraSDK.NetworkManager"

---@class SdkComponent
SdkComponent = DefineComponent("SdkComponent")

function SdkComponent:ReqPaySign(ConfigID, ProductID, ServerID)
    self:Debug("[ReqPaySign]",ConfigID, ProductID)
    self.remote.ReqPaySign(ConfigID, ProductID, ServerID)
end

function SdkComponent:RetPaySign(Result, Sign, PayNotifyUrl, ThirdPartyTradeNo)
    self:Debug("[RetPaySign]",Result, Sign, PayNotifyUrl, ThirdPartyTradeNo)
    Game.RechargeSystem:RetPaySign(Result, Sign, PayNotifyUrl, ThirdPartyTradeNo)
end

function SdkComponent:ReqAddProduct(ConfigID, ProductID)
    self:Debug("[ReqAddProduct]",ConfigID, ProductID)
    self.remote.ReqAddProduct(ConfigID, ProductID)
end

--bFirst 是否首冲
function SdkComponent:RetAddProduct(Result, ConfigID, bFirst)
    self:Debug("[RetAddProduct]",Result, ConfigID, bFirst)
    Game.RechargeSystem:RetAddProduct(Result, ConfigID, bFirst)
end

return SdkComponent