---表演动作组件，计划支持单人表演，多人表演，与场景物体的表演等
---@class PerformanceComponent
PerformanceComponent = DefineComponent("PerformanceComponent")

local AbilityConst = kg_require("Shared.Const.AbilityConst")
local MaterialEffectConst = kg_require("Gameplay.Effect.MaterialEffectConst")
local ESkillType = AbilityConst.ESkillType


function PerformanceComponent:ctor()
    self.CurDirectorID = nil
    self.bMoveLockControl = false
	self.bJumpLockControl = false
    self.bSkillLockControl = false
    self.bGazeLockControl = false

    -- 演员就位阶段结束
    self.onDirectorNaviFinish = LuaMulticastDelegate.new()
    -- Start阶段结束
    self.onDirectorStartFinish = LuaMulticastDelegate.new()
    -- 第一次Loop动画结束
    self.onDirectorFirstLoopFinish = LuaMulticastDelegate.new()
    -- 表演即将结束
    self.onDirectorWillFinish = LuaMulticastDelegate.new()
end

function PerformanceComponent:dtor()
    self.onDirectorNaviFinish = nil
    self.onDirectorStartFinish = nil
    self.onDirectorFirstLoopFinish = nil
    self.onDirectorWillFinish = nil
end

function PerformanceComponent:__component_ExitWorld__()
    -- TODO PerformanceComponent ExitWorld 由于时序问题 外面一些com内部变量可能已经nil了 看看怎么处理 @luyilong, 这里临时修改了Com顺序@zhaojunjie

    if self.CurDirectorID ~= nil then
        -- todo 是否需要中途退出不影响其他人的表演? 也许需要配表来决定
        Game.ArenaSystem:FinishDirector(self.CurDirectorID, true)
    end

	self.bMoveLockControl = false
	self.bJumpLockControl = false
	self.bSkillLockControl = false
	self.bGazeLockControl = false

	if self.DitherAlphaTimer then
		Game.TimerManager:StopTimerAndKill(self.DitherAlphaTimer)
		self.DitherAlphaTimer = nil
	end
end

function PerformanceComponent:SetPerformanceDirector(DirectorID)
    if self.CurDirectorID ~= nil then
        self:WarningFmt("Performance [SetPerformanceDirector] %s already have DirectorID %s with %s!", self.eid, self.CurDirectorID, DirectorID)
        self:WarningFmt("Performance [SetPerformanceDirector] Cur DirectorState %s", Game.ArenaSystem:CheckDirector(self.CurDirectorID))
    end
    self.CurDirectorID = DirectorID
	if self.SetMeshRLWhenDriveModeChange then
		self:SetMeshRLWhenDriveModeChange(false)
	end
end

function PerformanceComponent:ClearPerformanceDirector(DirectorID)
    if self.CurDirectorID ~= nil and self.CurDirectorID ~= DirectorID then
        self:WarningFmt("Performance [ClearPerformanceDirector] %s not the same DirectorID %s with %s!", self.eid, self.CurDirectorID, DirectorID)
        self:WarningFmt("Performance [ClearPerformanceDirector] Cur DirectorState %s", Game.ArenaSystem:CheckDirector(self.CurDirectorID))
    end
    self.onDirectorNaviFinish:Clear()
    self.onDirectorStartFinish:Clear()
    self.onDirectorFirstLoopFinish:Clear()
    self:BroadcastDirectorWillFinish()
    self.onDirectorWillFinish:Clear()
    self.CurDirectorID = nil
	if self.SetMeshRLWhenDriveModeChange then
		self:SetMeshRLWhenDriveModeChange(true)
	end
end

--region Event
function PerformanceComponent:CheckDirectorEnum()
	if self.CurDirectorID == nil then
		self:WarningFmt("Performance [StartListenDirectorNaviFinish in Performance] %s no DirectorID!", self.eid)
		return
	end
	return Game.ArenaSystem:CheckDirectorEnum(self.CurDirectorID)
end

function PerformanceComponent:StartListenDirectorNaviFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [StartListenDirectorNaviFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
	local CurDirectorEnum = Game.ArenaSystem:CheckDirectorEnum(self.CurDirectorID)
	if CurDirectorEnum > Enum.EDirectorInnerState.Navi  then
		local callback = InListener[InFuncName]
		if callback then
			callback()
		end
	else
		self.onDirectorNaviFinish:Add(InListener, InFuncName)
	end
end

--- 可以不需要手动清理, 当一场表演的生命周期结束后, 注册过的callback会被自动清理
function PerformanceComponent:StopListenDirectorNaviFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [StopListenDirectorNaviFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    if InFuncName == nil then
        self.onDirectorNaviFinish:RemoveObject(InListener)
    else
        self.onDirectorNaviFinish:Remove(InListener, InFuncName)
    end
end

function PerformanceComponent:BroadcastDirectorNaviFinish(NaviResult)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [BroadcastDirectorNaviFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    self.onDirectorNaviFinish:Broadcast(self, NaviResult)
end

function PerformanceComponent:StartListenDirectorStartFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [StartListenDirectorStartFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    self.onDirectorStartFinish:Add(InListener, InFuncName)
end

--- 可以不需要手动清理, 当一场表演的生命周期结束后, 注册过的callback会被自动清理
function PerformanceComponent:StopListenDirectorStartFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [StopListenDirectorStartFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    if InFuncName == nil then
        self.onDirectorStartFinish:RemoveObject(InListener)
    else
        self.onDirectorStartFinish:Remove(InListener, InFuncName)
    end
end

function PerformanceComponent:BroadcastDirectorStartFinish(NaviResult)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [BroadcastDirectorStartFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    self.onDirectorStartFinish:Broadcast(self, NaviResult)
end

function PerformanceComponent:StartListenDirectorFirstLoopFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [StartListenDirectorFirstLoopFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    self.onDirectorFirstLoopFinish:Add(InListener, InFuncName)
end

--- 可以不需要手动清理, 当一场表演的生命周期结束后, 注册过的callback会被自动清理
function PerformanceComponent:StopListenDirectorFirstLoopFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [StopListenDirectorFirstLoopFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    if InFuncName == nil then
        self.onDirectorFirstLoopFinish:RemoveObject(InListener)
    else
        self.onDirectorFirstLoopFinish:Remove(InListener, InFuncName)
    end
end

function PerformanceComponent:BroadcastDirectorFirstLoopFinish()
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [BroadcastDirectorFirstLoopFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    self.onDirectorFirstLoopFinish:Broadcast(self)
end

function PerformanceComponent:StartListenDirectorWillFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [StartListenDirectorWillFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    self.onDirectorWillFinish:Add(InListener, InFuncName)
end

--- 可以不需要手动清理, 当一场表演的生命周期结束后, 注册过的callback会被自动清理
function PerformanceComponent:StopListenDirectorWillFinish(InListener, InFuncName)
    if self.CurDirectorID == nil then
        self:LogFmt("Performance [StopListenDirectorWillFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    if InFuncName == nil then
        self.onDirectorWillFinish:RemoveObject(InListener)
    else
        self.onDirectorWillFinish:Remove(InListener, InFuncName)
    end
end

function PerformanceComponent:BroadcastDirectorWillFinish()
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [BroadcastDirectorWillFinish in Performance] %s no DirectorID!", self.eid)
        return
    end
    self.onDirectorWillFinish:Broadcast(self)
end
--endregion Event

-- TODO 该接口目前仅扮演玩法在使用(扮演要重做) 已经废弃! 已经废弃! 已经废弃! @zhaojunjie
function PerformanceComponent:PlaySinglePerformance(AnimSeq, bAnimLoop, bAnimAutoStop, FinishOnceCallBack, LastCallBack, bAnimQueueLoop)
    
end

-- TODO 该接口目前仅扮演玩法在使用(扮演要重做) 已经废弃! 已经废弃! 已经废弃! @zhaojunjie
function PerformanceComponent:StopSinglePerformance(bStopAnim)
    
end

--- 开始一段单人表演
--- PerformanceActionID: PerformanceAction表格的数据
--- PilotTrans: 锚点角色期望的初始Trans, 可以填nil, 默认锚点角色当前位置
--- RecoverInfo: 快速恢复, 可以填nil, 填true的话会跳过Navi与Start阶段直接进入Loop, 也可以填一个时间, 会按照时间恢复(不精确)
function PerformanceComponent:RequestSinglePerformance(PerformanceActionID, PilotTrans, RecoverInfo)
    if self.CurDirectorID ~= nil then
        self:WarningFmt("Performance [RequestSinglePerformance in Performance] %s already have DirectorID %s!", self.eid, self.CurDirectorID)
        self:WarningFmt("Performance [RequestSinglePerformance] %s DirectorState %s", self.eid, Game.ArenaSystem:CheckDirector(self.CurDirectorID))
        return false
    end
    return Game.ArenaSystem:RequestSinglePerformance(self.eid, PerformanceActionID, PilotTrans, RecoverInfo)
end

--- 结束一段单人表演
function PerformanceComponent:FinishSinglePerformance(bDirectFinish)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [FinishSinglePerformance in Performance] %s no DirectorID!", self.eid)
        return
    end
    Game.ArenaSystem:FinishDirector(self.CurDirectorID, bDirectFinish)
end

--endregion single

--region dual

--- 开始一段双人表演
--- PerformanceActionID: PerformanceAction表格的数据
--- PilotTrans: 锚点角色期望的初始Trans, 可以填nil, 默认锚点角色当前位置
--- RecoverInfo: 快速恢复, 可以填nil, 填true的话会跳过Navi与Start阶段直接进入Loop, 也可以填一个时间, 会按照时间恢复(不精确)
function PerformanceComponent:RequestDualPerformance(TargetEntity, PerformanceActionID, PilotTrans, RecoverInfo)
    if self.CurDirectorID ~= nil then
        self:WarningFmt("Performance [RequestDualPerformance in Performance] %s already have DirectorID %s!", self.eid, self.CurDirectorID)
        self:WarningFmt("Performance [RequestDualPerformance] %s DirectorState %s", self.eid or "", Game.ArenaSystem:CheckDirector(self.CurDirectorID) or "")
        return false
    end
    if not TargetEntity then
        self:WarningFmt("Performance [RequestDualPerformance in Performance] TargetEntity is nil!")
        return false
    end
    if TargetEntity.CurDirectorID ~= nil then
        self:WarningFmt("Performance [RequestDualPerformance in Performance] %s already have DirectorID %s!", TargetEntity.eid, TargetEntity.CurDirectorID)
        self:WarningFmt("Performance [RequestDualPerformance] %s DirectorState %s", TargetEntity.eid, Game.ArenaSystem:CheckDirector(TargetEntity.CurDirectorID))
        return false
    end
    return Game.ArenaSystem:RequestDualPerformance(self.eid, TargetEntity.eid, PerformanceActionID, PilotTrans, RecoverInfo)
end

--- 结束一段双人表演, 除了PerformanceActionID配置的后摇外, 还增加一些额外后摇
---ExtraParams = {
---    ExtraBackSwingAnimation = {AnimLib, AnimLib},
---    bAutoStop = {boolean, boolean},
---}
function PerformanceComponent:FinishDualPerformanceWithExtraBackSwing(TargetEntity, ExtraParams)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [FinishDualPerformanceWithExtraBackSwing in Performance] %s no DirectorID!", self.eid)
        return
    end
    if TargetEntity ~= nil and self.CurDirectorID ~= TargetEntity.CurDirectorID then
        self:WarningFmt("Performance [FinishDualPerformanceWithExtraBackSwing in Performance] %s and %s not same DirectorID with %s and %s!",
                self.eid, TargetEntity.eid, self.CurDirectorID, TargetEntity.CurDirectorID)
        self:WarningFmt("Performance [FinishDualPerformanceWithExtraBackSwing] %s DirectorState %s", self.eid, Game.ArenaSystem:CheckDirector(self.CurDirectorID))
        self:WarningFmt("Performance [FinishDualPerformanceWithExtraBackSwing] %s DirectorState %s", TargetEntity.eid, Game.ArenaSystem:CheckDirector(TargetEntity.CurDirectorID))
        return
    end

    Game.ArenaSystem:FinishDirectorWithExtraParams(self.CurDirectorID, ExtraParams)
end

--- 中途退出双人表演, 另一方继续, 适合舞会玩法
function PerformanceComponent:QuitDualPerformance()
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [QuitDualPerformance in Performance] %s no DirectorID!", self.eid)
        return
    end
    return Game.ArenaSystem:QuitDualPerformance(self.CurDirectorID, self.eid)
end

--- 结束一段双人表演
function PerformanceComponent:FinishDualPerformance(TargetEntity, bDirectFinish)
    if self.CurDirectorID == nil then
        self:WarningFmt("Performance [FinishDualPerformance in Performance] %s no DirectorID!", self.eid)
        return
    end
    if TargetEntity ~= nil and self.CurDirectorID ~= TargetEntity.CurDirectorID then
        self:WarningFmt("Performance [FinishDualPerformance in Performance] %s and %s not same DirectorID with %s and %s!",
                self.eid, TargetEntity.eid, self.CurDirectorID, TargetEntity.CurDirectorID)
        self:WarningFmt("Performance [FinishDualPerformance] %s DirectorState %s", self.eid, Game.ArenaSystem:CheckDirector(self.CurDirectorID))
        self:WarningFmt("Performance [FinishDualPerformance] %s DirectorState %s", TargetEntity.eid, Game.ArenaSystem:CheckDirector(TargetEntity.CurDirectorID))
        return
    end
    Game.ArenaSystem:FinishDirector(self.CurDirectorID, bDirectFinish)
end

--- 魔女风摇筝
function PerformanceComponent:RequestAttachDualPerformance(TargetEntity, distance)
    if self.CurDirectorID ~= nil then
        self:WarningFmt("Performance [RequestAttachDualPerformance in Performance] %s already have DirectorID %s!", self.eid, self.CurDirectorID)
        self:WarningFmt("Performance [RequestAttachDualPerformance] %s DirectorState %s", self.eid, Game.ArenaSystem:CheckDirector(self.CurDirectorID))
        return false
    end
    if not TargetEntity then
        self:WarningFmt("Performance [RequestAttachDualPerformance in Performance] TargetEntity is nil!")
        return false
    end
    if TargetEntity.CurDirectorID ~= nil then
        self:WarningFmt("Performance [RequestAttachDualPerformance in Performance] %s already have DirectorID %s!", TargetEntity.eid, TargetEntity.CurDirectorID)
        self:WarningFmt("Performance [RequestAttachDualPerformance] %s DirectorState %s", TargetEntity.eid, Game.ArenaSystem:CheckDirector(TargetEntity.CurDirectorID))
        return false
    end
    return Game.ArenaSystem:RequestAttachDualPerformance(self.eid, TargetEntity.eid, distance)
end

--endregion dual

--region Interactive

--- 开始一段交互表演
--- PerformanceActionID: PerformanceAction表格的数据
--- PilotTrans: 锚点角色期望的初始Trans, 可以填nil, 默认锚点角色当前位置
--- AttachSocket: 挂接的SocketName
--- RecoverInfo: 快速恢复, 可以填nil, 填true的话会跳过Navi与Start阶段直接进入Loop, 也可以填一个时间, 会按照时间恢复(不精确)
--- ReplaceAnimInfo: 可以替换各个阶段的动画, {{start}, {loop}, {end}}, 可以传{nil, {loop}, nil}来仅指定某一阶段的动画
function PerformanceComponent:RequestInteractivePerformance(TargetEntity, PerformanceActionID, PilotTrans, RecoverInfo, ReplaceAnimInfo, AttachSocket)
    if self.CurDirectorID ~= nil then
        self:WarningFmt("Performance [RequestInteractivePerformance in Performance] %s already have DirectorID %s!", self.eid, self.CurDirectorID)
        self:WarningFmt("Performance [RequestInteractivePerformance] %s DirectorState %s", self.eid, Game.ArenaSystem:CheckDirector(self.CurDirectorID))
        return false
    end
    if not TargetEntity then
        self:WarningFmt("Performance [RequestInteractivePerformance in Performance] TargetEntity is nil!")
        return false
    end

    return Game.ArenaSystem:RequestInteractivePerformance(self.eid, TargetEntity.eid, PerformanceActionID, PilotTrans, AttachSocket, RecoverInfo, {ReplaceAnimInfo})
end

--- 结束一段交互表演
--- bDirectFinish: 立即结束
--- LeaveLocation: 结束时指定位置
function PerformanceComponent:FinishInteractivePerformance(bDirectFinish, LeaveLocation)
    if self.CurDirectorID == nil then
        self:LogFmt("Performance [FinishInteractivePerformance in Performance] %s no DirectorID!", self.eid)
        return
    end

	if LeaveLocation and not bDirectFinish then
		Game.ArenaSystem:FinishDirectorWithExtraParams(self.CurDirectorID, {["LeaveLocation"] = LeaveLocation})
	else
		Game.ArenaSystem:FinishDirector(self.CurDirectorID, bDirectFinish)
	end
end

--endregion Interactive

--region control

---锁定移动
---@param bMoveLockControl boolean
function PerformanceComponent:LockMoveInPerformance(bMoveLockControl)
    if self.bMoveLockControl ~= bMoveLockControl then
        self.bMoveLockControl = bMoveLockControl
        if self:GetEnableLocoControlSwitch() == true then
            --禁止/解禁移动
            self:DisableLocoMoveByLocoStart(Enum.ELocoControlTag.SocialAction, bMoveLockControl, false)
            self:DisableLocoMove(Enum.ELocoControlTag.SocialAction, bMoveLockControl, false)
            self:DisableLocoRotate(Enum.ELocoControlTag.SocialAction, bMoveLockControl, false)
        end
    end
end

---锁定跳跃
---@param bJumpLockControl boolean
function PerformanceComponent:LockJumpInPerformance(bJumpLockControl)
    if self.bJumpLockControl ~= bJumpLockControl then
        self.bJumpLockControl = bJumpLockControl
        if self:GetEnableLocoControlSwitch() == true then
            --禁止/解禁跳跃
            self:DisableLocoJump(Enum.ELocoControlTag.SocialAction, bJumpLockControl, false)
        end
    end
end

---锁定技能
---@param bSkillLockControl boolean
function PerformanceComponent:LockSkillInPerformance(bSkillLockControl)
    if self.bSkillLockControl ~= bSkillLockControl then
        self.bSkillLockControl = bSkillLockControl
        if (bSkillLockControl == true) then
            if self.AddDisableSkillType then
                for _, SkillType in pairs(ESkillType) do
                    self:AddDisableSkillType(SkillType, ETE.EDisabledSkillReason.Temporary)
                end
            end

            if self:GetEnableLocoControlSwitch() == true then
                self:DisableLocoDodge(Enum.ELocoControlTag.SocialAction, true)
            end
        else
            if self.DelDisableSkillType then
                for _, SkillType in pairs(ESkillType) do
                    self:DelDisableSkillType(SkillType, ETE.EDisabledSkillReason.Temporary)
                end
            end

            if self:GetEnableLocoControlSwitch() == true then
                self:DisableLocoDodge(Enum.ELocoControlTag.SocialAction, false)
            end
        end
    end
end

--- LookAt锁定开关
function PerformanceComponent:LockGazeInPerformance(bLockGaze)
    if not self.bInWorld then
        return
    end

    if self.bGazeLockControl ~= bLockGaze then
        self.bGazeLockControl = bLockGaze
        if self.bGazeLockControl then
            self:PauseGaze()
        else
            self:UnPauseGaze()
        end
    end

    --self:Debug("Performance PerformanceComponent:LockGazeInPerformance ".. self.eid
    --        .. (bLockGaze and " true" or " false"))
end

--endregion control

--region UpperBlend


---@public
---@param upperBlendPerformActionID number
---@return boolean 返回false则代表融合失败
function PerformanceComponent:TryUpperBlendPerformanceAction(upperBlendPerformActionID)
    local performanceActionData = Game.TableData.GetPerformanceActionDataRow(upperBlendPerformActionID)
    if not performanceActionData then
        self:WarningFmt("[TryUpperBlendPerformanceAction] get PerformanceActionData %s failed", upperBlendPerformActionID)
        return false
    end

    local upperBlendID = performanceActionData[1].UpperBlendID
    local upperBlendData = Game.TableData.GetPerformanceUpperBlendDataRow(upperBlendID)
    if not upperBlendData then
        self:WarningFmt("[TryUpperBlendPerformanceAction] get UpperBlendData %s in %s failed", upperBlendID, upperBlendPerformActionID)
        return false
    end

    local bIsFaceAnim = performanceActionData[1].Type == Enum.EPerformanceActionType.Face
    if not Game.ArenaSystem:CanUpperBlendPerformanceAction(self.CurDirectorID, bIsFaceAnim) then
        return false
    end

    Game.ArenaSystem:UpperBlendPerformanceAction(self.CurDirectorID, upperBlendData)
    return true
end


--endregion UpperBlend


--function PerformanceComponent:__component_AppendGamePlayDebugInfo__(debugInfos)
--    table.insert(debugInfos, "<Title_Red>=============[PerformanceComponent]===============</>")
--end

--function PerformanceComponent:testMotionWarp()
--	local EMoveCorrectorObtainPriority = import("EMoveCorrectorObtainPriority")
--	local xxx = self:ObtainMotionWarpForTranslationWithFixMode(EMoveCorrectorObtainPriority.Action, FVector(7808, -7821, 192.150))
--	local hhh = self:ObtainMotionWarpForRotateYawWithFixMode(EMoveCorrectorObtainPriority.Action, -166)
--	self:Debug("LYL DEBUG StartMotionWarp Loc", FVector(7808, -7821, 192.150))
--	self:Debug("LYL DEBUG StartMotionWarp Yaw", -166)
--	
--	local PlayTime = self:PlayAnimLibMontage("Mount_Passenger_GetIn")
--	Game.TimerManager:CreateTimerAndStart(function()
--		self:ReleaseMotionWarp(xxx)
--		self:StopAnimLibMontage()
--		self:Debug("LYL DEBUG FinishMotionWarp Loc", self:GetPosition())
--		self:Debug("LYL DEBUG FinishMotionWarp Yaw", self:GetRotation().Yaw)
--	end, PlayTime * 1000, 1)
--end

return PerformanceComponent