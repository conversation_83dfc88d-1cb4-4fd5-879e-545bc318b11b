
---@class FlowchartAvatarComponent
FlowchartAvatarComponent = DefineComponent("FlowchartAvatarComponent")

function FlowchartAvatarComponent:OnMsgPlayLevelSequence(sequenceID, sequenceType, startFrame, endFrame)
    self:DoPlayLevelSequence(sequenceID, sequenceType, startFrame, endFrame)
end

function FlowchartAvatarComponent:DoPlayLevelSequence(sequenceID, sequenceType, startFrame, endFrame)
    local tParams = {
        JobID = self:uid(),
        AssetID = sequenceID,
        StartFrame = startFrame,
        EndFrame = endFrame,
        SequenceType = sequenceType,
    }
    self:DebugFmt("[DoPlayLevelSequence] assetID=%s, sequenceType:%s, endFrame:%s", sequenceID, sequenceType, endFrame)
    Game.CinematicManager:PlayLevelSequence(tParams)
end

return FlowchartAvatarComponent
