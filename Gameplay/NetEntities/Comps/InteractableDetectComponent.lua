local UInteractiveTriggerComponent = import("InteractiveTriggerComponent")
local CollisionConst = kg_require("Shared.Const.CollisionConst")

local EAttachmentRule = import("EAttachmentRule")
local C7FunctionLibrary = import("C7FunctionLibrary")

---@class InteractableDetectComponent
InteractableDetectComponent = DefineComponent(
    "InteractableDetectComponent"
)

InteractableDetectComponent.INDOOR_OBJECT_TAG = "SCENE_IN_DOOR_BUILDING"
InteractableDetectComponent.INDOOR_MAX_TRACE_DISTANCE = 3000

function InteractableDetectComponent:ctor()
    self._DefaultDetectTickInterval = 0.1
    self._OverrideDetectTickInterval = nil
	self._SweepDetectMaxSpeed = 5000 --启用sweep的一个最大的速度阈值，防止sweep一段很远的距离
    self.DistanceMonitors = {}
    self.DetectedTriggerEntities = {}
	self.bTriggerDetecting = false
    self.bIndoorDetected = false
end

function InteractableDetectComponent:__component_EnterWorld__()
    self:InitForTriggerDetect()
	self.bTriggerDetecting = true
end

function InteractableDetectComponent:__component_ExitWorld__()
	self.bTriggerDetecting = false
    self:UnInitForTriggerDetect()
    if Game.me == self then
        -- 退出后给它重置掉室内状态，因为进入时默认是室外状态，若检测到还在室内状态会再次推送
        if self.bIndoorDetected then
            self:OnInteractiveTriggerDetectIndoorChanged(false)
        end
    end
end

function InteractableDetectComponent:InitForTriggerDetect()
    self.DistanceMonitors = {}
    self.DetectedTriggerEntities = {}
    local TriggerCompClassId = Game.ObjectActorManager:GetIDByClass(UInteractiveTriggerComponent)
    local TriggerComponentId = self.CppEntity:KAPI_Actor_GetComponentByClassID(TriggerCompClassId)
    if not IsValidID(TriggerComponentId) then
        TriggerComponentId = self.CppEntity:KAPI_Actor_AddComponentByClassID(TriggerCompClassId)
        local CapsuleComponentId = self.CppEntity:KAPI_Actor_GetCapsuleComponent()
        self.CppEntity:KAPI_SceneID_AttachToComponent(TriggerComponentId, CapsuleComponentId, "", EAttachmentRule.SnapToTarget, EAttachmentRule.SnapToTarget, EAttachmentRule.SnapToTarget, true)
        self.CppEntity:KAPI_PrimitiveID_SetGenerateOverlapEvents(TriggerComponentId, false)
        self.CppEntity:KAPI_PrimitiveID_SetCollisionProfileName(TriggerComponentId, CollisionConst.COLLISION_PRESET_NAMES.INTERACTABLE_DETECT_PRESET, false)
    end
    self.CppEntity:KAPI_InteractiveTrigger_ResetTriggerSize(self.CppEntity:KAPI_Capsule_GetScaledCapsuleHalfHeight(), self.CppEntity:KAPI_Capsule_GetScaledCapsuleRadius())
    self.CppEntity:KAPI_InteractiveTrigger_InitAsDetect(
            self:uid(),
            self._OverrideDetectTickInterval or self._DefaultDetectTickInterval,
            CollisionConst.QUERY_BY_OBJECTTYPES.INTERACTABLE_FOR_INTERACT_HUD,
            CollisionConst.QUERY_BY_OBJECTTYPES.INTERACTABLE_FOR_LINE_TRACE
    )
    self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectEnterTrigger(TriggerComponentId, true)
    self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectLeaveTrigger(TriggerComponentId, true)
    self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectPlayerDistanceChanged(TriggerComponentId, true)
    if Game.me == self then
        self.CppEntity:KAPI_InteractiveTrigger_EnableSweepDetect(true, self._SweepDetectMaxSpeed)
        self.CppEntity:KAPI_InteractiveTrigger_EnableIndoorFiledDetect(
                true, 
                CollisionConst.COLLISION_OBJECT_TYPE_QUERY_BY_NAME.SceneIndoorField,
                CollisionConst.COLLISION_OBJECT_TYPE_QUERY_BY_NAME.WorldStatic,
                InteractableDetectComponent.INDOOR_OBJECT_TAG,
                InteractableDetectComponent.INDOOR_MAX_TRACE_DISTANCE
        )
        self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectIndoorChanged(TriggerComponentId, true)
    end
end

function InteractableDetectComponent:UnInitForTriggerDetect()
    self.DistanceMonitors = {}
    self.DetectedTriggerEntities = {}
    
    local TriggerCompClassId = Game.ObjectActorManager:GetIDByClass(UInteractiveTriggerComponent)
    local TriggerComponentId = self.CppEntity:KAPI_Actor_GetComponentByClassID(TriggerCompClassId)
    self.CppEntity:KAPI_InteractiveTrigger_EnableInterActiveForDetect(false)
    self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectEnterTrigger(TriggerComponentId, false)
    self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectLeaveTrigger(TriggerComponentId, false)
    self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectPlayerDistanceChanged(TriggerComponentId, false)
    if Game.me == self then
        self.CppEntity:KAPI_InteractiveTriggerID_EnableDetectIndoorChanged(TriggerComponentId, false)
    end

end

function InteractableDetectComponent:ClearInteractableDetectState()
    self.CppEntity:KAPI_InteractiveTrigger_ClearDetectState()
end

function InteractableDetectComponent:IsCurTriggerDetecting()
	return self.bTriggerDetecting
end

function InteractableDetectComponent:OnInteractiveTriggerDetectIndoorChanged(bIndoor)
    self.bIndoorDetected = bIndoor
    self:DebugFmt("DetectIndoorChanged %s", bIndoor)
	Game.CameraManager:NotifyThirdCameraIndoorChanged(bIndoor)

    if Game.me:IsOnMount() then
        Game.me:RequestStopRide(true)
    end
end

function InteractableDetectComponent:OnInteractiveTriggerDetectEnterTrigger(OtherUID, Pos)
    local Other = Game.EntityManager:GetEntityByIntID(OtherUID)
    if Other then
        self.DetectedTriggerEntities[OtherUID] = true
        if Other.bInWorld then
            -- ViewControlTriggerComponent
            if Other.EnterInteractableMainTrigger then
                Other:EnterInteractableMainTrigger(self:uid(), Pos)
            end
        else
            Log.ErrorFormat("OnInteractiveTriggerDetectEnterTrigger not in world InsID:%s, EntityType:%s", Other.InsID or Other.InstanceID, Other.__cname)
        end
    end
end

function InteractableDetectComponent:OnInteractiveTriggerDetectLeaveTrigger(OtherUID, Pos)
    local Other = Game.EntityManager:GetEntityByIntID(OtherUID)
    if Other then
        self.DetectedTriggerEntities[OtherUID] = nil
        if Other.bInWorld then
            -- ViewControlTriggerComponent
            if Other.LeaveInteractableMainTrigger then
                Other:LeaveInteractableMainTrigger(self:uid())
            end
        else
            Log.ErrorFormat("OnInteractiveTriggerDetectLeaveTrigger not in world InsID:%s, EntityType:%s", Other.InsID or Other.InstanceID, Other.__cname)
        end
    end
end

function InteractableDetectComponent:OnInteractiveTriggerDetectPlayerDistanceChanged(OtherUID, Distance)
     if not Game.me.DistanceMonitors[OtherUID] then
        return
    end
    local ChangeCB = Game.me.DistanceMonitors[OtherUID]
    local Other = Game.EntityManager:GetEntityByIntID(OtherUID)
    if Other then
        ChangeCB(Other, Distance)
    end
end

function InteractableDetectComponent:IsEntityInInteractiveTrigger(OtherUID)
    return self.DetectedTriggerEntities[OtherUID]
end

function InteractableDetectComponent:CanTriggerByEntity(Entity)
	local PlayerLoc
	if Game.me:InInvisibleHandControl() then
		PlayerLoc = Game.me:GetInvisibleHandPositon()
		local distance = import("KismetMathLibrary").Vector_Distance(PlayerLoc, Entity:GetPosition()) - 34.0 * 2
		return distance <= (Entity.InteractorRadius or 0)
	else
		return Entity:IsInBehaviorTrigger()
	end
end

function InteractableDetectComponent:GetCurInRangeAvatars()
    local Avatars = {}
    for UID, _ in pairs(self.DetectedTriggerEntities) do
        local Other = Game.EntityManager:GetEntityByIntID(UID)
        if Other then
            if Other.ActorType == EWActorType.PLAYER then
                Avatars[UID] = true
            end
        end
    end
    return Avatars
end

-- 目前只开放监听主角位置变化
function InteractableDetectComponent:RegisterMainPlayerDistanceChanged(EntityUID, Distance, ChangeCB)
    if not self.bInWorld then
        self:AddCommandCache("RegisterMainPlayerDistanceChanged", EntityUID, Distance, ChangeCB)
        return
    end
    self.CppEntity:KAPI_InteractiveTrigger_RegisterDetectDistanceMonitor(EntityUID, Distance)
    self.DistanceMonitors[EntityUID] = ChangeCB
end

function InteractableDetectComponent:UnRegisterMainPlayerDistanceChanged(EntityUID)
    if not self.bInWorld then
        self:AddCommandCache("UnRegisterMainPlayerDistanceChanged", EntityUID)
        return
    end
    self.CppEntity:KAPI_InteractiveTrigger_UnRegisterDetectDistanceMonitor(EntityUID)
    self.DistanceMonitors[EntityUID] = nil
end

---@param Interval number 秒
function InteractableDetectComponent:SetDetectInterval(Interval)
    if Interval >= 0 then
        if self.bInWorld then
            self.CppEntity:KAPI_InteractiveTrigger_SetComponentTickInterval(Interval)
        end
        self._OverrideDetectTickInterval = Interval
    end
end

function InteractableDetectComponent:ResetToDefaultDetectInterval()
    if self.bInWorld then
        self.CppEntity:KAPI_InteractiveTrigger_SetComponentTickInterval(self._DefaultDetectTickInterval)
    end
    self._OverrideDetectTickInterval = nil
end


return InteractableDetectComponent
