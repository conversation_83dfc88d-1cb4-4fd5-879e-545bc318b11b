local commonInteractorBaseComponent = kg_require("Gameplay.NetEntities.Comps.CommonInteractorBaseComponent")

---@class AvatarCommonInteractorComponent
AvatarCommonInteractorComponent = DefineComponent("AvatarCommonInteractorComponent", commonInteractorBaseComponent)

function AvatarCommonInteractorComponent:ctor()
end

function AvatarCommonInteractorComponent:dtor()
end

-- 服务器通知客户端开始执行action
function AvatarCommonInteractorComponent:OnMsgCommonInteractorActionStart(instanceID, avatarID, actionID)
    local commonInteractor = Game.CommonInteractorManager:GetInteractorByInstanceID(instanceID)
    if commonInteractor == nil then
        self:ErrorFmt("OnMsgCommonInteractorRemoveButton cannot find common interactor %s", instanceID)
        return
    end

    local actionTableData = Game.TableData.GetCommonInteractorActionDataRow(actionID)
    if actionTableData == nil then
        self:ErrorFmt("actionID %s not found", actionID)
        return
    end

    commonInteractor:innerExecuteAction(actionID, actionTableData.ClientAction, true)
end

-- 交互Action开始通知服务器
function AvatarCommonInteractorComponent:ReqInteractorActionStart(instanceID, actionID)
    self:Debug("[ReqInteractorActionStart] ", instanceID, actionID)
    self.remote.ReqInteractorActionStart(instanceID, actionID)
end

-- 交互Action结束通知服务器
function AvatarCommonInteractorComponent:ReqInteractorActionFinish(interactorID, actionID)
    self:Debug("ReqInteractorActionFinish ", interactorID, actionID)
    self.remote.ReqInteractorActionFinish(interactorID, actionID)
end

-- 交互物进入视野,创建
function AvatarCommonInteractorComponent:OnMsgCommonInteractorListCreate(CommonInteractorList)
    for _, commonInteractorProp in ipairs(CommonInteractorList) do
        Game.CommonInteractorManager:CreateCommonInteractor(commonInteractorProp)
    end
end

-- 交互物离开视野,销毁
function AvatarCommonInteractorComponent:OnMsgCommonInteractorListDestroy(instanceIDList)
    for _, instanceId in ipairs(instanceIDList) do
        Game.CommonInteractorManager:DestroyCommonInteractor(instanceId)
    end
end

-- 主动发起交互
function AvatarCommonInteractorComponent:ReqStartCommonInteract(interactType, instanceID, buttonID)
    self:Debug("[ReqStartCommonInteract] ", interactType, instanceID)
    self.remote.ReqStartCommonInteract(interactType, instanceID, buttonID)
end

-- 主动取消交互
function AvatarCommonInteractorComponent:ReqCancelCommonInteract(instanceID)
    self:Debug("[ReqCancelCommonInteract]", instanceID)
    self.remote.ReqCancelCommonInteract(instanceID)
end

-- 交互Request回包
function AvatarCommonInteractorComponent:RetStartCommonInteract(commonInteractorStartType, instanceID, buttonID, result)
    local commonInteractor = Game.CommonInteractorManager:GetInteractorByInstanceID(instanceID)
    if commonInteractor == nil then
        self:Warning("RetStartCommonInteract cannot find common interactor", commonInteractorStartType, instanceID, result)
        return
    end

    commonInteractor:OnInteractConfirmed(commonInteractorStartType, buttonID, result)
end

function AvatarCommonInteractorComponent:OnMsgCommonInteractorStateList(arg0_DictIntInt)
end

return AvatarCommonInteractorComponent
