local NetworkManager = require "Framework.DoraSDK.NetworkManager"


RedPointComponent = DefineComponent("RedPointComponent")


function RedPointComponent:NtfAllRedPoint(RedPointInfo)
    -- Game.RedPointSystem:SyncOnceRedPoints(RedPointInfo)
    self:Debug("[RedDot]Ntf All Red Point")
    Game.RedDotSystem:SyncOnceRedDots(RedPointInfo)
end

function RedPointComponent:UpdateRedPointRet(Result)
    self:Debug("[RedDot]Ret Update Red Point")
end

function RedPointComponent:UpdateRedPointReq(RedPointInfo, BAdd)
    self:Debug("[RedDot]Req Update Red Point")
	self.remote.UpdateRedPointReq(RedPointInfo, BAdd)
end

function RedPointComponent:NtfRedPoint(RedPointInfo)
    -- Game.RedPointSystem:SyncOnceRedPoint(RedPointInfo)
    self:Debug("[RedDot]Ntf Single Red Point")
    Game.RedDotSystem:SyncOnceRedDot(RedPointInfo)
end

return RedPointComponent
