--- @class ReviveComponent
ReviveComponent = DefineComponent("ReviveComponent")

function ReviveComponent:ReqReviveHelp()
    self:Debug("[ReviveComponent]ReqReviveHelp")
    self.remote.ReqReviveHelp()
end

function ReviveComponent:ReqRevive(ReviveConfigID)
    self:Debug("[ReviveComponent]ReqRevive", ReviveConfigID)
    self.remote.ReqRevive(ReviveConfigID)
end

function ReviveComponent:RetReviveHelp(Result)
end

function ReviveComponent:RetRevive(Result)
end

function ReviveComponent:set_BossBattleReviveChance(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_BOSS_BATTLE_REVIE_CHANGE, entity.eid, new)
end

function ReviveComponent:set_ReviveHelpTimestamp(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_REVIVE_HELP_TIMESTAMP, entity.eid)
end

ReviveRecords = DefineClass("ReviveRecords")
function ReviveRecords:set_attr(entity, key, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_REVIVERECORDS_ALLOW_TIMESTAMP, entity.eid)
end

ReviveRecord = DefineClass("ReviveRecord")
function ReviveRecord:set_AllowTimestamp(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_REVIVERECORDS_ALLOW_TIMESTAMP, entity.eid)
end

return ReviveComponent
