
PickObjectInteractComponent = DefineComponent("PickObjectInteractComponent")

function PickObjectComponent:ctor()
    -- 部分配置数据比如交互探测和显隐生命周期相关 进行缓存 热更不会直接起效 进出AOI重建Entity之后生效
    local rowData = Game.TableData.GetSceneActorTaskCollectDataRow(self.TemplateID)
    self.initialState = rowData.InitialState
    self.InteractorRadius = rowData.InteractRadius
    self.InnerRadius = rowData.InnerRadius
    self.FaceAngle = rowData.FaceAngle
    -- 闪光显示规则
    self.ShowGlowRule = rowData.ShowParticleRule or self.SHOW_RULE.HIDE
    -- 模型显示规则
    self.ShowModelRule = rowData.ShowModelRule or self.SHOW_RULE.HIDE
    -- TODO: 目前导表后处理自动生成了数据，后续修改
    self.UITemplateID = rowData.UITemplateID
    self.startAnimFeature = rowData.StartAnimFeature
    self.LevelRequire = rowData.LevelRequire
    self.bTaskControl = rowData.bTaskControl

    self.bHideByQuest = false
    self.bCollectable = nil
end

function PickObjectInteractComponent:__component_ActorVisibleChanged__(bVisible)
    if bVisible then
        self:InitTrigger()
    else
        self:UnInitTrigger()
    end
end

function PickObjectInteractComponent:__component_AfterEnterWorld__()
    self:refreshHiddenByQuest()
    self:refreshModelVisibleByCollectable()
    self:RefreshCollectable()
end

function PickObjectInteractComponent:GetInteractUITemplateID()
    return self.UITemplateID
end

function PickObjectInteractComponent:GetInteractPeriod()
    local rowData = Game.TableData.GetSceneActorTaskCollectDataRow(self.TemplateID)
    return rowData.InteractPeriod
end

function PickObjectInteractComponent:IsCollectable()
    return self.bCollectable
end

function PickObjectInteractComponent:RefreshCollectable()
    -- 表格初始状态和bSeverControlCollectable属性为公有表现，会覆盖私有表现效果
    local mainAvatar = Game.me
    local bCollectable = self.initialState == self.INITIAL_INTERACT_STATE.CAN_PICK
    local bSeverControlCollectable = self.bSeverControlCollectable
    if bSeverControlCollectable == false then
        bCollectable = false
    end

    if bSeverControlCollectable == nil then
        -- 私有表现控制 根据主角状态决定是否可交互
        if self.LevelRequire > mainAvatar.Level then
            bCollectable = false
        elseif self.bTaskControl then
            if not Game.QuestSystem:CheckInteractorCondition(self.TemplateID, false) then
                bCollectable = false
            end
        end
    elseif bSeverControlCollectable then
        bCollectable = true
    end

    if bCollectable ~= self.bCollectable then
        self.bCollectable = bCollectable
        self:RefreshGlowParticleByCollectable()
        self:refreshModelVisibleByCollectable()
        Game.UniqEventSystemMgr:Publish(self:uid(), EEventTypesV2.PICK_OBJECT_COLLETABLE_CHANGED, bCollectable)
        if self.bCollectable then
            self:buildHUDInteractTask()
        else
            self:destroyInteractorTask()
        end
    end
end

function PickObjectInteractComponent:InitTrigger()
    self:InitMainTriggerAsSphereAndBindEvent(self.InteractorRadius, self, "OnEnterBehaviorTrigger", "OnLeaveBehaviorTrigger")
    self.CppEntity:KAPI_Actor_SetActorEnableCollision(true)
end

function PickObjectInteractComponent:UnInitTrigger()
    self:destroyInteractorTask()
    Game.HUDInteractManager:DestroyInteractorTraceTask(self:uid())
    self.CppEntity:KAPI_Actor_SetActorEnableCollision(false)
    self:UnInitMainTriggerAndClearCallback()
    self:endOutButtonCDCheck()
end

function PickObjectInteractComponent:InitTriggerCustomScope()
    local faceAngle = self.FaceAngle
    faceAngle = faceAngle and faceAngle > 0 and faceAngle or 360
    local innerRadius = self.InnerRadius
    if innerRadius and innerRadius > 0 then
        self:EnableMainTriggerAsRing(innerRadius, self.InteractorRadius)
    end
    if faceAngle < 360 then
        self:EnableMainTriggerAsCircularSector(0, faceAngle / 2, 0)
    end
end


function PickObject:RefreshGlowParticleByCollectable()
    local shouldShowFlag = self.bCollectable
    if self.ShowGlowRule == self.SHOW_RULE.SHOW then
        shouldShowFlag = true
    end
    
    local Comps = self.CppEntity:KAPI_Actor_GetComponentListByClassNameAndTag("NiagaraComponent", "GLOW")
    for _, compID in pairs(Comps) do
        self.CppEntity:KAPI_NiagaraID_ReinitializeSystem(compID)
        self.CppEntity:KAPI_Component_SetActive(compID, shouldShowFlag, true)
        self.CppEntity:KAPI_SceneID_SetVisibility(compID, shouldShowFlag, true)
    end
end

function PickObject:refreshModelVisibleByCollectable()
    if self.ShowModelRule == self.SHOW_RULE.SHOW then
        return
    end

    local shouldShowFlag = self.bCollectable
    if self.ShowModelRule == self.SHOW_RULE.HIDE then
        shouldShowFlag = false
    end

    if shouldShowFlag then
        self:SetActorVisible(Enum.EInVisibleReasons.TaskCollectable)
    else
        self:SetActorInVisible(Enum.EInVisibleReasons.TaskCollectable)
    end
end

--region 按钮CD
function PickObjectInteractComponent:endOutButtonCDCheck()
    if self.buttonCDTimer then
        Game.TimerManager:StopTimerAndKill(self.buttonCDTimer)
        self.buttonCDTimer = nil
    end
end

function PickObjectInteractComponent:startButtonCDExpireCheck(expireTime)
    self:endOutButtonCDCheck()
    self.buttonCDTimer = Game.TimerManager:CreateTimerAndStart(
        function()
            self:onButtonCDExpired()
        end,
        expireTime,
        1
    )
end

function PickObjectInteractComponent:onButtonCDExpired()
    self.buttonCDTimer = nil
    if self.bHasButton then
        Game.HUDInteractManager.SetInteractorBtnGray(false, self, 1)
    end
end

function PickObjectInteractComponent:IsInInteractCD()
    local interactCD = self.interactCD
    if interactCD then
        return interactCD > _G.now()
    end
    return false
end

function PickObjectInteractComponent:SetLastPickTime(lastInteractTime)
    local rowData = Game.TableData:GetSceneActorTaskCollectDataRow(self.TemplateID)
    local cd = rowData.InteractCD
    if cd and cd > 0 then
        self.interactCD = cd * 1000 + lastInteractTime
        local lastTime = self.interactCD - _G.now()
        if lastTime > 0 then
            self:startButtonCDExpireCheck(self.interactCD)
        end
    end
end
--endregion 按钮CD

function PickObjectInteractComponent:IsInBehaviorTrigger()
    --主动禁止交互
    if not Game.me:CanInteractWithSceneActor(self) then
        return false
    end
    --由于例如灵视等隐藏了actor，就不能交互
    if self:IsInvisible() then
        return false
    end
    return Game.me:IsEntityInInteractiveTrigger(self:uid())
end

function PickObjectInteractComponent:buildHUDInteractTask()
    if self.bHasButton or not self.bCollectable or self:IsInvisible() then
        return
    end

    self.bHasButton = true
    Game.HUDInteractManager.BuildInteractorTask(self:uid(), { self.UITemplateID }, function()
        self.onClickSceneActorBehaviorButton(self)
    end)
    if self:IsInInteractCD() then
        Game.HUDInteractManager.SetInteractorBtnGray(true, self, 1)
    end
end

function PickObjectInteractComponent:destroyInteractorTask()
    if self.bHasButton then
        Game.HUDInteractManager.DestroyInteractorTask(self:uid())
    end
end

function PickObjectInteractComponent:onClickSceneActorBehaviorButton()
    if self:IsInInteractCD() then
        self:DebugFmt("onClickSceneActorBehaviorButton InsID:%s, in button cd", self.InstanceID)
        return
    end

    Game.me.remote.ReqStartPick(self.eid)
end

function PickObjectInteractComponent:OnRetInteractStart(lastPickTime)
    self:destroyInteractorTask()
    self:SetLastPickTime(lastPickTime)
    -- 通知任务开始
    Game.me.remote.ReqCollectBegin(self.InstanceID)
    local rowData = Game.TableData:GetSceneActorTaskCollectDataRow(self.TemplateID)
    -- 玩家转向
    if rowData.bTurn then
        if rowData.TurnAngle then
            local TargetRotator = FRotator(0, rowData.TurnAngle, 0)
            Game.me:EnterLookAtToRot(TargetRotator)
        else
            Game.me:EnterLookAtToLoc(self:GetPosition())
        end
    end

    if StringValid(rowData.InteractorInteractAnim) then
        self:PlayAnimationByMontageAsyncLoad("DefaultSlot", rowData.InteractorInteractAnim, false, 0, 0)
    end
end

function PickObjectInteractComponent:OnRetInteractResult(succ)
    self:buildHUDInteractTask()
end

function PickObjectInteractComponent:refreshHiddenByQuest()
    local bHidden = Game.QuestSystem:IsPickObjectHidden(self.InstanceID)
    local bHideByQuest = self.bHideByQuest
    if bHideByQuest == nil or bHideByQuest ~= bHidden then
        bHideByQuest = bHidden
        if bHidden then
            self:SetInvisibleByQuestControl(true)
        else
            self:SetInvisibleByQuestControl(false)
        end
    end
end
