local EC7ShapeCollisionType = import("EC7ShapeCollisionType")
local CollisionConst = kg_require("Shared.Const.CollisionConst")

MoveConstraintVolumeComponent = DefineComponent("MoveConstraintVolumeComponent")

function MoveConstraintVolumeComponent:ctor()
    self.MoveConstrainAreaTokens = {}
end


function MoveConstraintVolumeComponent:dtor()
    self.MoveConstrainAreaTokens = nil
end

function MoveConstraintVolumeComponent:__component_ExitWorld__()
    self:DeactivateConstraintVolume()
end

function MoveConstraintVolumeComponent:__component_AppendGamePlayDebugInfo__(debugInfos)
    return true
end

function MoveConstraintVolumeComponent:DeactivateConstraintVolume()
    for OtherUID, AreaTokens in pairs(self.MoveConstrainAreaTokens) do
        local Entity = Game.EntityManager:getEntity(OtherUID)
        if Entity then
            for AreaToken, _ in pairs(AreaTokens) do
                Entity:RemoveMoveConstraintByToken(AreaToken)
            end
        end
    end
    self.MoveConstrainAreaTokens = {}
end

function MoveConstraintVolumeComponent:OnRoleHitMoveConstraintBounds(HitEntity)
    if Game.me == HitEntity and self.OnRoleHitBounds then
        self:OnRoleHitBounds()
    end
end

function MoveConstraintVolumeComponent:AddSphereConstraintAreaOnEntity(Entity, Center, Radius, ConstraintType, bDoHitCallBack)
    local OtherUID = Entity:uid()
    if not self.MoveConstrainAreaTokens[OtherUID] then
        self.MoveConstrainAreaTokens[OtherUID] = {}
    end
    local AreaToken = Entity:AddSphereMoveConstrainArea(self:uid(), Center, Radius, bDoHitCallBack, ConstraintType)
    self.MoveConstrainAreaTokens[OtherUID][AreaToken] = true
    return AreaToken
end

function MoveConstraintVolumeComponent:AddBoxConstraintAreaOnEntity(Entity, Center, Rotator, BoxExtent, ConstraintType, bDoHitCallBack)
    local OtherUID = Entity:uid()
    if not self.MoveConstrainAreaTokens[OtherUID] then
        self.MoveConstrainAreaTokens[OtherUID] = {}
    end
    local AreaToken = Entity:AddBoxMoveConstrainArea(self:uid(), Center, Rotator, BoxExtent, bDoHitCallBack, ConstraintType)
    self.MoveConstrainAreaTokens[OtherUID][AreaToken] = true
    return AreaToken
end

function MoveConstraintVolumeComponent:RemoveMoveConstraintAreaOnEntityByToken(Entity, AreaToken)
    local OtherUID = Entity:uid()
    if self.MoveConstrainAreaTokens[OtherUID] then
        if self.MoveConstrainAreaTokens[OtherUID][AreaToken] then
            Entity:RemoveMoveConstraintByToken(AreaToken)
            self.MoveConstrainAreaTokens[OtherUID][AreaToken] = nil
            if not next(self.MoveConstrainAreaTokens[OtherUID]) then
                self.MoveConstrainAreaTokens[OtherUID] = nil
            end
        end
    end
end

function MoveConstraintVolumeComponent:RemoveAllMoveConstraintAreaOnEntity(Entity)
    local OtherUID = Entity:uid()
    local AreaTokens = self.MoveConstrainAreaTokens[OtherUID]
    if AreaTokens then
        for AreaToken, _ in pairs(AreaTokens) do
            Entity:RemoveMoveConstraintByToken(AreaToken)
        end
        self.MoveConstrainAreaTokens[OtherUID] = nil
    end
end

return MoveConstraintVolumeComponent