
--- @class PrefabGroupComponent
PrefabGroupComponent = DefineComponent("PrefabGroupComponent")

function PrefabGroupComponent:OnMsgEnterPrefabGroup(GroupID)
    self:DebugFmt("[OnMsgEnterPrefabGroup] GroupID:%s", GroupID)
    -- todo 都走OnMsgPrivateInteractorSync
end

function PrefabGroupComponent:OnMsgLeavePrefabGroup(GroupID)
    self:DebugFmt("[OnMsgLeavePrefabGroup] GroupID:%s", GroupID)
    -- todo 都走OnMsgPersistentPIASync
end

return PrefabGroupComponent
