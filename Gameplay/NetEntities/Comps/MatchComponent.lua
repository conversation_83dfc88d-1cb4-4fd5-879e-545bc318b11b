--- @class MatchComponent
MatchComponent = DefineComponent("MatchComponent")

-- PVP匹配状态
MatchComponent.EPVPMatchState = {
    ["NONE"] = 0,
    ["REQ_BEGIN"] = 1,
    ["IN_MATCHING"] = 2,
    ["CANCLE_OK"]   = 3,
    ["MATCH_FAIL"]  = 4,
    ["WAIT_CONFIRM"] = 5,
    ["ALL_CONFIRMED"] = 6,
}

function MatchComponent:ReqStartPVPMatch(matchType)
    self:Debug("[ReqStartPVPMatch]*******************************")
    self.remote.ReqStartPVPMatch(matchType)
end

function MatchComponent:ReqCancelPVPMatch(matchType)
    self:Debug("[ReqCancelPVPMatch]****WBP_ItemNml_lua***************************")
    self.remote.ReqCancelPVPMatch(matchType)
end

---取消自动匹配
function MatchComponent:ReqCancelStartNextPVPMatch(matchType)
	self.remote.ReqCancelStartNextPVPMatch(matchType)
end

---同步取消自动匹配变更
function MatchComponent:OnMsgAutoStartNextPVPMatchCancel(matchType)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ARENA3V3_PVP_AUTO_MATCH_CANCEL)
end

function MatchComponent:ReqConfirmEnter(bAgree)
    self:Debug("[ReqConfirmEnter]***********************,",bAgree)
    self.remote.ReqConfirmEnter(bAgree)
end

function MatchComponent:RetPVPMatch(result)
    self:DebugFmt("[RetPVPMatch],ErrorCode:%s",result.Code)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ARENA3V3_PVP_MATCH_RESULT, result.Code)
    Game.NetworkManager.ShowNetWorkResultReminder("RetPVPMatch", result)
end

function MatchComponent:RetConfirmEnter(res)
    self:Debug("[RetConfirmEnter]***************************")
    if result.Code ~= Game.ErrorCodeConst.NO_ERR then
        self:DebugFmt("[RetConfirmEnter] Ret Error,ErrorCode:%s.",result.Code)
        --TODO 错误提示
        return
    end
end

function MatchComponent:RetCancelPVPMatch(res)
	self:Debug("[RetCancelPVPMatch]***************************")
	if res.Code ~= Game.ErrorCodeConst.NO_ERR then
		self:DebugFmt("[RetCancelPVPMatch] Ret Error,ErrorCode:%s.",res.Code)
		--TODO 错误提示
		return
	end
end

--function MatchComponent:OnMsgConfirmStateChange(bCancle,bFirst,ConfirmInfoList)
--    --self:Debug("[OnMsgConfirmStateChange]EndTime:",EndTime)
--    Game.EventSystem:Publish(_G.EEventTypes.TEAM_ARENA_MATCH_CONFIRM_STATE_CHANGE, bCancle,bFirst,ConfirmInfoList, EndTime)
--
--    for _, v in pairs(ConfirmInfoList) do
--        self:Debug("[RetConfirmEnter]",v.AvatarActorID,v.CampIndex,v.bConfirmed)
--        for k2,v2 in pairs(v) do
--            self:Debug(k2, v2)
--        end
--    end
--end

--function MatchComponent:OnMsgMatchStateChange(matchType, NewState)
--    self:Debug("[OnMsgMatchStateChange]matchType:"..tostring(matchType).."  NewState:"..NewState)
--    Game.EventSystem:Publish(_G.EEventTypes.TEAM_ARENA_MATCH_STATE_CHANGE, matchType, NewState)
--end

---通知匹配界面的协议
function MatchComponent:OnMsgSyncMatchResult(matchType, status, stateTime, matchInfo)
    self:Debug("MatchComponent:OnMsgSyncMatchResult")
	Game.PVPSystem:OnMatchStateChange(matchType, status, stateTime, matchInfo)
    Game.EventSystem:Publish(_G.EEventTypes.TEAM_ARENA_MATCH_STATE_CHANGE, matchType, status, stateTime, matchInfo)
end

---准备状态变更协议
function MatchComponent:OnMsgMatchConfirmStateChange(campId, memberId, prepared)
    self:Debug("MatchComponent:OnMsgMatchConfirmStateChange")
    Game.EventSystem:Publish(_G.EEventTypes.TEAM_ARENA_MATCH_CONFIRM_STATE_CHANGE, campId, memberId, prepared)
end

return MatchComponent
