--- @class DungeonRollComponent
DungeonRollComponent = DefineComponent("DungeonRollComponent")

function DungeonRollComponent:Req<PERSON><PERSON><PERSON><PERSON>oll(RollID, GoodsID, RollType)
    self:Debug("DungeonRollComponent:<PERSON>q<PERSON><PERSON><PERSON><PERSON><PERSON> ", RollID, GoodsID, RollType)
	self.remote.ReqDungeonRoll(RollID, GoodsID, RollType)
end

function DungeonRollComponent:Ret<PERSON>ungeonRoll(Result, SyncStates)
    --self:Debug("DungeonRollComponent:RetDungeonRoll ", CommonUtils.tprint(Result), CommonUtils.tprint(SyncStates)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.NetworkManager.HandleRequestRet("DungeonRoll", Result, SyncStates)
    Game.DungeonAwardSystem:OnMsgSyncDungeonRollRefreshState(SyncStates)
end

function DungeonRollComponent:OnMsgSyncDungeonRollRefresh(SyncInfos)
    --self:Debug("DungeonRollComponent:OnMsgSyncDungeonRollRefresh ", CommonUtils.tprint(SyncInfos)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.DungeonAwardSystem:OnMsgSyncDungeonRollRefresh(SyncInfos)
end

function DungeonRollComponent:OnMsgSyncDungeonRollRefreshState(SyncStates)
    --self:Debug("DungeonRollComponent:OnMsgSyncDungeonRollInfos ", CommonUtils.tprint(SyncStates)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.DungeonAwardSystem:OnMsgSyncDungeonRollRefreshState(SyncStates)
end

function DungeonAuctionComponent:OnMsgSyncDungeonRollRemove(rollGroupIDList)
    --self:Debug("DungeonAuctionComponent:OnMsgSyncDungeonRollRemove", CommonUtils.tprint(rollGroupIDList)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.DungeonAwardSystem:OnMsgSyncDungeonRollRemove(rollGroupIDList)
end

return DungeonRollComponent
