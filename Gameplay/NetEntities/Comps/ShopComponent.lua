--- @class ShopComponent
ShopComponent = DefineComponent("ShopComponent")

function ShopComponent:ReqBuyGoods(GoodsID, ShopID, Count, SerialNo, NeedMoney)
    self:Debug("[ReqBuyGoods]**********************,GoodsID, ShopID, Count, SerialNo, NeedMoneys", Goods<PERSON>, ShopID, Count, SerialNo, NeedMoney)
    local Time = Game.TimeUtils.GetCurTime()
    if self.LastReqTime and Time - self.LastReqTime < 1 / Game.TableData.GetConstDataRow("SHOP_PURCHASE_FREQUENCY") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.NPC_SHOP_BUY_CD)
        return
    end
    self.LastReqTime = Time
    self.remote.ReqBuyGoods(GoodsID, ShopID, Count, Game.CurrencyExchangeSystem:GetSerialNo(Enum.CURRENCY_EXCAHNGE_TYPE.SHOP), NeedMoney or 0)
end

function ShopComponent:RetBuyGoods(Result, SerialNo)
    self:DebugFmt("[RetBuyGoods]**********************,ErrorCode:%s", Result.Code)
    -- Game.CurrencyExchangeSystem:CheckSerialNo(SerialNo)
    local reminderID = Game.NetworkManager.GetErrCodeReminderID(Result.Code)
	if reminderID and reminderID ~= 0 then
		local AvatarActor = NetworkManager.GetLocalAvatarActor()
		if AvatarActor then
			AvatarActor:OnMsgGenReminder(reminderID, {})	--luacheck:ignore
		end
	else
		Game.EventSystem:Publish(EEventTypes.ON_RET_BUY_GOODS_SUCCESS)
	end
end

function ShopComponent:ReqShopQueryGoodsLimitInfo()
    self.remote.ReqShopQueryGoodsLimitInfo()
end

function ShopComponent:RetShopQueryGoodsLimitInfo(Result, LimitInfo)
    Game.DepartmentStoreSystem:UpdateAllServerLimit(Result, LimitInfo)
end

function ShopComponent:OnMsgUpdateLimitItemInfo(TotalLimitGoodsBuyInfo, DisCountLimitGoodsBuyInfo, bAllSync)
    self:Debug("============================TotalLimitGoodsBuyInfo, DisCountLimitGoodsBuyInfo, bAllSync",TotalLimitGoodsBuyInfo, DisCountLimitGoodsBuyInfo, bAllSync)
    Log.Dump(TotalLimitGoodsBuyInfo)
    Log.Dump(DisCountLimitGoodsBuyInfo)
    Game.DepartmentStoreSystem:ShopUpdateLimit(TotalLimitGoodsBuyInfo, DisCountLimitGoodsBuyInfo, bAllSync)
end

function ShopComponent:RetBuyMultiGoods(arg0_MULTI_BUY_GOODS_RESULT_DEFINE)
end

return ShopComponent
