local NetworkManager = require "Framework.DoraSDK.NetworkManager"

EquipComponent = DefineComponent("EquipComponent")

local EQUIP_ITEMTYPE = 1 -- luacheck: ignore

------------------------------------ Server Rpc ---------------------------------
-- 装备全量同步
function EquipComponent:OnMsgEquipmentSlotInfoSync(invSlotInfo)
    local p = Game.EquipmentSystem.model
    p.equipmentSlotInfo = invSlotInfo
    -- p.equipmentBodyEnhanceInfo = equipBodyEnhanceInfo
    -- Game.EventSystem:Publish(_G.EEventTypes.XXX)
end

-- 部位全量同步
function EquipComponent:OnMsgEquipmentBodyEnhanceInfoSync(equipBodyEnhanceInfo)
    --local p = Game.EquipmentSystem.model
    --p.equipmentBodyEnhanceInfo = equipBodyEnhanceInfo
    --for i = 1, Enum.EEquipConstData.EQUIPBAR_MAX_SLOT do
    --    p.equipmentBodyEnhanceInfo.slots[i] = p.equipmentBodyEnhanceInfo.slots[i] and p.equipmentBodyEnhanceInfo.slots[i] or {}
    --    p.equipmentBodyEnhanceInfo.slots[i].stagesEffect = p.equipmentBodyEnhanceInfo.slots[i].stagesEffect and p.equipmentBodyEnhanceInfo.slots[i].stagesEffect or 0
    --    p.equipmentBodyEnhanceInfo.slots[i].stages = p.equipmentBodyEnhanceInfo.slots[i].stages and p.equipmentBodyEnhanceInfo.slots[i].stages or {}
    --    for j = 1, 20 do
    --        p.equipmentBodyEnhanceInfo.slots[i].stages[j] = p.equipmentBodyEnhanceInfo.slots[i].stages[j] and p.equipmentBodyEnhanceInfo.slots[i].stages[j] or {}
    --        p.equipmentBodyEnhanceInfo.slots[i].stages[j].level = p.equipmentBodyEnhanceInfo.slots[i].stages[j].level and p.equipmentBodyEnhanceInfo.slots[i].stages[j].level or 0
    --        p.equipmentBodyEnhanceInfo.slots[i].stages[j].percent = p.equipmentBodyEnhanceInfo.slots[i].stages[j].percent and p.equipmentBodyEnhanceInfo.slots[i].stages[j].percent or 0.0
    --    end
    --end
    --for i = 1, Enum.EEquipConstData.EQUIP_SUIT_TYPE_MAX do
    --    p.equipmentBodyEnhanceInfo.suit[i] = p.equipmentBodyEnhanceInfo.suit[i] and p.equipmentBodyEnhanceInfo.suit[i] or {}
    --    p.equipmentBodyEnhanceInfo.suit[i].effect = p.equipmentBodyEnhanceInfo.suit[i].effect and p.equipmentBodyEnhanceInfo.suit[i].effect or 0
    --    p.equipmentBodyEnhanceInfo.suit[i].level = p.equipmentBodyEnhanceInfo.suit[i].level and p.equipmentBodyEnhanceInfo.suit[i].level or 0
    --end
    --
end

-- 装备系统信息全量同步
function EquipComponent:OnMsgEquipmentOperateCountLimitSync(OperateCountLimit)
    local p = Game.EquipmentSystem.model
    p.OperateCountLimit = OperateCountLimit
end

-- 装备栏内容变更
function EquipComponent:onMsgEquipmentbarChange(invSlotCountMap, invSlotValMap, wordChangedUids)
    local p = Game.EquipmentSystem.model
    for slot, count in pairs(invSlotCountMap) do
        if count == 0 then
            p.equipmentSlotInfo.slots[slot] = nil
        end
    end
    for slot, val in pairs(invSlotValMap) do
        p.equipmentSlotInfo.slots[slot] = val
    end
    Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_BAR_CHANGE)
    for _, uid in pairs(wordChangedUids) do
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 装备变更同步
function EquipComponent:OnMsgEquipChange(channel,slot,uid,equipInfo)
    local p = Game.EquipmentSystem.model
    local oldEquipInfo
    if channel == 0 then -- 装备栏
        oldEquipInfo = p.equipmentSlotInfo.slots[slot]
        p.equipmentSlotInfo.slots[slot] = equipInfo
    else -- 背包
        local invInfo = Game.BagSystem:GetInvInfo()
        oldEquipInfo = invInfo[EQUIP_ITEMTYPE].slots[slot]
    --     p.invInfo[EQUIP_ITEMTYPE].slots[slot] = equipInfo
    end
    Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CHANGE, {
        channel = channel,
        slot = slot,
        uid = uid,
        oldEquipInfo = oldEquipInfo,
        newEquipInfo = equipInfo
    })
	
	Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
end

-- 部位激活同步
function EquipComponent:onMsgEquipmentBodyActive(slot, stage, equipBodyEnhanceSlot)
    --local p = Game.EquipmentSystem.model
    --p.equipmentBodyEnhanceInfo.slots[slot] = p.equipmentBodyEnhanceInfo.slots[slot] and p.equipmentBodyEnhanceInfo.slots[slot] or {}
    --p.equipmentBodyEnhanceInfo.slots[slot] = equipBodyEnhanceSlot
    --p.equipmentBodyEnhanceInfo.slots[slot].stagesEffect = p.equipmentBodyEnhanceInfo.slots[slot].stagesEffect and p.equipmentBodyEnhanceInfo.slots[slot].stagesEffect or 0
    --p.equipmentBodyEnhanceInfo.slots[slot].stages = p.equipmentBodyEnhanceInfo.slots[slot].stages and p.equipmentBodyEnhanceInfo.slots[slot].stages or {}
    --for j = 1, 20 do
    --    p.equipmentBodyEnhanceInfo.slots[slot].stages[j] = p.equipmentBodyEnhanceInfo.slots[slot].stages[j] and p.equipmentBodyEnhanceInfo.slots[slot].stages[j] or {}
    --    p.equipmentBodyEnhanceInfo.slots[slot].stages[j].level = p.equipmentBodyEnhanceInfo.slots[slot].stages[j].level and p.equipmentBodyEnhanceInfo.slots[slot].stages[j].level or 0
    --    p.equipmentBodyEnhanceInfo.slots[slot].stages[j].percent = p.equipmentBodyEnhanceInfo.slots[slot].stages[j].percent and p.equipmentBodyEnhanceInfo.slots[slot].stages[j].percent or 0.0
    --end
end

-- 部位精炼同步
function EquipComponent:onMsgEquipmentBodyRefine(multi, slot, stage, equipBodyEnhanceSlot)
    --local p = Game.EquipmentSystem.model
    --p.equipmentBodyEnhanceInfo.slots[slot] = p.equipmentBodyEnhanceInfo.slots[slot] and p.equipmentBodyEnhanceInfo.slots[slot] or {}
    --p.equipmentBodyEnhanceInfo.slots[slot] = equipBodyEnhanceSlot
    --p.equipmentBodyEnhanceInfo.slots[slot].stagesEffect = p.equipmentBodyEnhanceInfo.slots[slot].stagesEffect and p.equipmentBodyEnhanceInfo.slots[slot].stagesEffect or 0
    --p.equipmentBodyEnhanceInfo.slots[slot].stages = p.equipmentBodyEnhanceInfo.slots[slot].stages and p.equipmentBodyEnhanceInfo.slots[slot].stages or {}
    --for j = 1, 20 do
    --    p.equipmentBodyEnhanceInfo.slots[slot].stages[j] = p.equipmentBodyEnhanceInfo.slots[slot].stages[j] and p.equipmentBodyEnhanceInfo.slots[slot].stages[j] or {}
    --    p.equipmentBodyEnhanceInfo.slots[slot].stages[j].level = p.equipmentBodyEnhanceInfo.slots[slot].stages[j].level and p.equipmentBodyEnhanceInfo.slots[slot].stages[j].level or 0
    --    p.equipmentBodyEnhanceInfo.slots[slot].stages[j].percent = p.equipmentBodyEnhanceInfo.slots[slot].stages[j].percent and p.equipmentBodyEnhanceInfo.slots[slot].stages[j].percent or 0.0
    --end
end

function EquipComponent:onMsgEquipmentSuitActive(suit, equipBodyEnhanceSuit)
    --local p = Game.EquipmentSystem.model
    --p.equipmentBodyEnhanceInfo.suit[suit] = equipBodyEnhanceSuit
end

-- 词条锁定状态同步
function EquipComponent:onMsgEquipmentPropLockStateSync(channel, slot, uid, lockDict)
    local p = Game.EquipmentSystem.model
    for index, isLock in pairs(lockDict) do
        if channel == 0 then -- 装备栏
            p.equipmentSlotInfo.slots[slot].equipPropInfo.equipAtkPropInfo.fixedProps[index].isLocked = isLock
        else -- 背包
            local invInfo = Game.BagSystem:GetInvInfo()
            invInfo[EQUIP_ITEMTYPE].slots[slot].equipPropInfo.equipAtkPropInfo.fixedProps[index].isLocked = isLock
        end
    end
    Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CHANGE, {
        channel = channel,
        slot = slot,
        uid = uid,
    })
end

-- 发生装备替换
--function EquipComponent:onEquipmentExchangeSucc(bagId, bagSlotId, equpiSlotId)
--    Game.EventSystem:Publish(_G.EEventTypes.EQUIP_EXCHANGE)
--end

-- 装备基础属性重置
function EquipComponent:RetEquipAtkBasePropEnhance(result,channel,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_BASE_ENHANCE, channel, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    elseif result.Code == Enum.EReminderTextData.INV_FULL then --todo enum
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB1_NORMAL_BAGFULL)
    end
end

-- 装备基础属性一键补齐
function EquipComponent:RetEquipAtkBasePropEnhanceToMax(result,channel,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_BASE_TO_MAX, channel, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 装备随机属性词条洗练
function EquipComponent:RetEquipAtkRandomPropEnhance(result,channel,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_RANDOM_UP, channel, slot, uid)
    end
end

-- 装备随机属性词条5连洗练
function EquipComponent:RetEquipAtkRandomPropBatchEnhance(result,channel,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_RANDOM_UP, channel, slot, uid)
    end
end

-- 装备随机属性词条5连洗练结果选择
function EquipComponent:RetEquipAtkRandomPropSelect(result,channel,slot,uid,index)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_RANDOM_PICK, channel, slot, uid, index)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 装备固定属性上锁解锁
function EquipComponent:RetEquipAtkFixedPropLockAction(result,channel,slot,uid,lockDict)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_FIX_LOCK, channel, slot, uid)
    end
end

-- 装备固定属性词缀球提取
function EquipComponent:RetEquipAtkFixedPropExtract(result,channel,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_EXTRACT, channel, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 装备固定属性词缀球应用
function EquipComponent:RetEquipAtkFixedPropApply(result,channel,slot,uid,iChannel,iSlot,iUid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_ABSORB, channel, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    elseif result.Code == Game.ErrorCodeConst.EQUIP_ATK_PROPITEM_GROUP_GRAGE_FULL then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.EQUIP_TAB3_ALREADYMAX)
    end
end

-- 部位激活
function EquipComponent:RetEquipBodyActive(result, slot, stage)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_SLOT_ENHANCE, slot, stage, true)
    end
end

-- 部位精炼
function EquipComponent:RetEquipBodyRefine(result, slot, stage)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_SLOT_ENHANCE, slot, stage)
    end
end

function EquipComponent:RetEquipPutOff(result, slotIndex, uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        --Game.EventSystem:Publish(_G.EEventTypes.EQUIP_EXCHANGE)
    elseif result.Code == Game.ErrorCodeConst.GUILD_LEAGUE_IN_BATTLE_AREA_CHANGE_EQUIP_FORBID then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_LEAGUE_IN_BATTLE_AREA_CHANGE_EQUIP_FORBID)
    end
end

-- 穿装备
function EquipComponent:RetEquipPutOn(result, itemType, slot, uid, slotIndex, type)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        -- Game.EquipmentSystem:RefreshInventoryRedPoint()   2024.5.20 策划需求去掉穿戴时的装备背包红点刷新
        Game.EventSystem:Publish(_G.EEventTypes.EQUIP_EXCHANGE)
    elseif result.Code == Game.ErrorCodeConst.GUILD_LEAGUE_IN_BATTLE_AREA_CHANGE_EQUIP_FORBID then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_LEAGUE_IN_BATTLE_AREA_CHANGE_EQUIP_FORBID)
    end

    ---快捷装备(通过快捷弹窗装备)
    if type == Enum.EquipType.QuickEquip then
        Game.BagSystem:RefreshQuickUseQueue()
    else
    --- 通用装备方式
        Game.BagSystem:CheckIfRefreshQuickUseQueueAfterEquip()
    end

    Game.BagSystem:UpdateEquipScoreTipIcon(uid)
end

-- 防御装基础强化
function EquipComponent:RetEquipDefBasePropsEnhance(result, slot, uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_DEF_EQUIP_UPGRADE, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 防御装基础突破
function EquipComponent:RetEquipDefBasePropsPromote(result, slot, uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_DEF_EQUIP_PROMOTE, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 防御装高级重置
function EquipComponent:RetEquipDefAdvancePropsReset(result, slot, uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        -- Game.EventSystem:Publish(_G.EEventTypes.ON_DEF_EQUIP_PROMOTE, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 防御装高级提升
function EquipComponent:RetEquipDefAdvancePropsEnhance(result, slot, uid, index)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        -- Game.EventSystem:Publish(_G.EEventTypes.ON_DEF_EQUIP_PROMOTE, slot, uid)
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
    end
end

-- 防御装高级切换
function EquipComponent:RetEquipDefAdvancePropsExchange(result, slot, uid, index)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
        Game.EventSystem:Publish(_G.EEventTypes.ON_DEF_EQUIP_WORD_CHANGE, slot, uid, index)
    end
end

------------------------------------ Client cmd ---------------------------------
-- function EquipComponent:ReqEquipAtkBasePropEnhance(channel,slot,uid)
--     LocalAvatarActor.remote.ReqEquipAtkBasePropEnhance(channel,slot,uid)
-- end


------------------------------------ 新装备
-- 全量随机词条概率
function EquipComponent:OnMsgRandomGroupStockSlotsInfoSync(stockInfo)
    --Game.EquipmentSystem.model:SyncProbability(stockInfo)
end

-- 全量部位强化&套装信息&词条空间
function EquipComponent:OnMsgEquipmentBodyInfoSync(bodyInfo)
    local p = Game.EquipmentSystem.model
    p.equipmentBodyInfo = bodyInfo

    -- 强化初始化
    for i = 1, Enum.EEquipmentConstData.EQUIPBAR_MAX_SLOT do
        p.equipmentBodyInfo.enhanceInfo.slots[i] = p.equipmentBodyInfo.enhanceInfo.slots[i] and p.equipmentBodyInfo.enhanceInfo.slots[i] or {}
        p.equipmentBodyInfo.enhanceInfo.slots[i].stagesEffect = p.equipmentBodyInfo.enhanceInfo.slots[i].stagesEffect and p.equipmentBodyInfo.enhanceInfo.slots[i].stagesEffect or 0
        p.equipmentBodyInfo.enhanceInfo.slots[i].stages = p.equipmentBodyInfo.enhanceInfo.slots[i].stages and p.equipmentBodyInfo.enhanceInfo.slots[i].stages or {}
        p.equipmentBodyInfo.enhanceInfo.slots[i].mark = p.equipmentBodyInfo.enhanceInfo.slots[i].mark and p.equipmentBodyInfo.enhanceInfo.slots[i].mark or 0
        for j = 1, Enum.EEquipmentGrowConstData.ENHANCE_STAGE_MAX do
            p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j] = p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j] and p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j] or {}
            p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].level = p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].level and p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].level or 0
            p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].percent = p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].percent and p.equipmentBodyInfo.enhanceInfo.slots[i].stages[j].percent or 0.0
        end
    end
    for i = 1, Enum.EEquipmentGrowConstData.EQUIP_SUIT_TYPE_MAX do
        p.equipmentBodyInfo.suitInfo.suit[i] = p.equipmentBodyInfo.suitInfo.suit[i] and p.equipmentBodyInfo.suitInfo.suit[i] or {}
        p.equipmentBodyInfo.suitInfo.suit[i].effect = p.equipmentBodyInfo.suitInfo.suit[i].effect and p.equipmentBodyInfo.suitInfo.suit[i].effect or 0
        p.equipmentBodyInfo.suitInfo.suit[i].level = p.equipmentBodyInfo.suitInfo.suit[i].level and p.equipmentBodyInfo.suitInfo.suit[i].level or 0
    end

	p:OnEnhance()
end

-- 随机词条概率同步
function EquipComponent:OnMsgRandomGroupStockSlotsInfoChange(slot, adjustTotalUpCount, adjustTotalDownCount, groupId, groupInfo)
    Game.EquipmentSystem.model:OnProbabilityChange(slot, adjustTotalUpCount, adjustTotalDownCount, groupId, groupInfo)
end

-- 部位激活同步
function EquipComponent:OnMsgEquipmentBodyEnhanceActivate(slot, stage, equipBodyEnhanceSlot)
    local p = Game.EquipmentSystem.model
    p.equipmentBodyInfo.enhanceInfo.slots[slot] = p.equipmentBodyInfo.enhanceInfo.slots[slot] and p.equipmentBodyInfo.enhanceInfo.slots[slot] or {}
    p.equipmentBodyInfo.enhanceInfo.slots[slot] = equipBodyEnhanceSlot
    p.equipmentBodyInfo.enhanceInfo.slots[slot].stagesEffect = p.equipmentBodyInfo.enhanceInfo.slots[slot].stagesEffect and p.equipmentBodyInfo.enhanceInfo.slots[slot].stagesEffect or 0
    p.equipmentBodyInfo.enhanceInfo.slots[slot].stages = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages or {}
    for j = 1, 20 do
        p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j] = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j] and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j] or {}
        p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].level = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].level and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].level or 0
        p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].percent = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].percent and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].percent or 0.0
    end
	
	p:OnEnhance()
end

-- 部位精炼同步
function EquipComponent:OnMsgEquipmentBodyEnhanceRefine(multi, slot, stage, equipBodyEnhanceSlot)
    local p = Game.EquipmentSystem.model
    p.equipmentBodyInfo.enhanceInfo.slots[slot] = p.equipmentBodyInfo.enhanceInfo.slots[slot] and p.equipmentBodyInfo.enhanceInfo.slots[slot] or {}
    p.equipmentBodyInfo.enhanceInfo.slots[slot] = equipBodyEnhanceSlot
    p.equipmentBodyInfo.enhanceInfo.slots[slot].stagesEffect = p.equipmentBodyInfo.enhanceInfo.slots[slot].stagesEffect and p.equipmentBodyInfo.enhanceInfo.slots[slot].stagesEffect or 0
    p.equipmentBodyInfo.enhanceInfo.slots[slot].stages = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages or {}
    for j = 1, 20 do
        p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j] = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j] and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j] or {}
        p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].level = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].level and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].level or 0
        p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].percent = p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].percent and p.equipmentBodyInfo.enhanceInfo.slots[slot].stages[j].percent or 0.0
    end

	p:OnEnhance()
end

-- 词条空间变更同步
function EquipComponent:OnMsgEquipmentSlotWordInfoSync(slotsWordSpaceChange)
	local p = Game.EquipmentSystem.model.equipmentBodyInfo.slotsWordSpace
	for slot, space in pairs(slotsWordSpaceChange) do
		if not p[slot] then
			p[slot] = {}
		end
		for gid,pid in pairs(space) do
			p[slot][gid] = pid
		end
	end
end

-- 套装激活同步
function EquipComponent:OnMsgEquipmentBodySuitActive(suit, equipBodyEnhanceSuit)
    local p = Game.EquipmentSystem.model
    p.equipmentBodyInfo.suitInfo.suit[suit] = equipBodyEnhanceSuit
end

-- 打造同步
function EquipComponent:OnMsgEquipmentBodyAdvancePropsChange(slot,advanceInfo)
    local p = Game.EquipmentSystem.model
    p.equipmentBodyInfo.advancePropInfo.slots[slot] = advanceInfo
end

-- 固定词条交换
function EquipComponent:RetEquipFixedPropSwap(result,slot,uid,index,invId,invSlot,invUid,invIndex)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_FORGING, uid, invSlot)
    end
end

-- 随机词条洗练
function EquipComponent:RetEquipRandomPropEnhance(result,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CONDENSE, slot, uid)
    end
end

-- 随机词条应用
function EquipComponent:RetEquipRandomPropSelect(result,slot,uid,index)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CONDENSE_APPLY, slot, uid)
    end
end

-- 随机全体重置
function EquipComponent:RetEquipRandomPropProbabilityAllReset(result,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CONDENSE_ALLRESET, slot, uid)
    end
end

-- 随机单条重置
function EquipComponent:RetEquipRandomPropProbabilitySingleReset(result,slot,uid,groupId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CONDENSE_RESET, slot, uid, groupId)
    end
end

-- 随机概率增加
function EquipComponent:RetEquipRandomPropProbabilityUp(result,slot,uid,groupId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CONDENSE_UP, slot, uid, groupId)
    end
end

-- 随机概率减少
function EquipComponent:RetEquipRandomPropProbabilityDown(result,slot,uid,groupId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CONDENSE_DOWN, slot, uid, groupId)
    end
end

-- 部位强化激活
function EquipComponent:RetEquipBodyEnhanceActivate(result,slot,uid,stageIdx)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_ENHANCED_ACT, slot, stageIdx)
    end
end

-- 部位强化精炼
function EquipComponent:RetEquipBodyEnhanceRefine(result,slot,uid,stageIdx)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_ENHANCED_REF, slot, stageIdx)
    end
end

-- 打造
function EquipComponent:RetEquipAdvancePropEnhance(result,slot,uid)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CLOTTING, slot)
    end
end

-- 装备方案改名
function EquipComponent:RetChangeEquipPlanName(result, planId)
    Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_PLAN_NAME_CHANGE, planId)
end

-- 装备方案切换
function EquipComponent:RetSwitchEquipPlan(result, planId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.ROLE_EQUIP_SUIT_SAVE, {{Game.EquipmentSystem.model:GetOldName()}})
        Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_PLAN_SWITCH, planId)
        Game.EquipmentSystem.model:SetOldName(Game.me.equipPlans[planId].name)
    elseif result.Code == Game.ErrorCodeConst.GUILD_LEAGUE_IN_BATTLE_AREA_CHANGE_EQUIP_FORBID then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.GUILD_LEAGUE_IN_BATTLE_AREA_CHANGE_EQUIP_FORBID)
    end
end

function EquipComponent:RetSwitchEquipFixedWord(result,slotIdx)
	if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_SWITCH_FIXED)
		local uid = Game.EquipmentSystem:GetEquipBarSlot(slotIdx).gbId
		Game.EventSystem:Publish(_G.EEventTypes.EQUIP_INFO_UPDATED, uid)
	end
end
function EquipComponent:RetEquipClearExtraRandomProps(result)
	if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_CLEAR_EXTRA)
	end
end
function EquipComponent:RetUpdateWordLock(result)
	if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_BAN_WORD, true)
	else
		Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_BAN_WORD, false)
	end
end

function EquipComponent:RetEquipChangeSingleRandomWord(result,slot,gbId)
	if result.Code == Game.ErrorCodeConst.NO_ERR then
		Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_SMEAR, slot)
	end
end


---- sender
function EquipComponent:ReqEquipPutOn(invId, slot, uid, slotIdx, equipType)
    self.remote:ReqEquipPutOn(invId, slot, uid, slotIdx, equipType)
end
function EquipComponent:ReqEquipPutOff(slot, uid)
    self.remote:ReqEquipPutOff(slot, uid)
end
function EquipComponent:ReqEquipGetRandomProbabilityInfo()
    self.remote:ReqEquipGetRandomProbabilityInfo()
end
function EquipComponent:ReqChangeEquipPlanName(id, name)
    self.remote:ReqChangeEquipPlanName(id,name)
end
function EquipComponent:ReqSwitchEquipPlan(planId, invId, newEquip, changeEquip, keepEquip)
    self.remote:ReqSwitchEquipPlan(planId, invId, newEquip, changeEquip, keepEquip)
end
function EquipComponent:ReqEquipRandomPropEnhance(slot,gbId)
	self.remote:ReqEquipRandomPropEnhance(slot,gbId)
end
function EquipComponent:ReqEquipRandomPropSelect(slot,gbId,idx)
	self.remote:ReqEquipRandomPropSelect(slot,gbId,idx)
end
function EquipComponent:ReqEquipClearExtraRandomProps(slot,gbId)
	self.remote:ReqEquipClearExtraRandomProps(slot,gbId)
end
function EquipComponent:ReqSwitchEquipFixedWord(slotIdx,idx,groupId)
	self.remote:ReqSwitchEquipFixedWord(slotIdx,idx,groupId)
end
function EquipComponent:ReqUpdateWordLock(slotId,dict)
	self.remote:ReqUpdateWordLock(slotId,dict)
end
function EquipComponent:ReqUpdateWordLockSwitch(slot, bLock)
	self.remote:ReqUpdateWordLockSwitch(slot, bLock)
end
function EquipComponent:ReqEquipChangeSingleRandomWord(slotIdx,gbId,wordId,invId,invSlot,invGbId)
	self.remote:ReqEquipChangeSingleRandomWord(slotIdx,gbId,wordId,invId,invSlot,invGbId)
end

randomClassLockInfo = DefineClass("randomClassLockInfo")
function randomClassLockInfo:set_attr(entity, key,new,old)
	Game.EventSystem:Publish(_G.EEventTypes.ON_EQUIP_BAN_PROP_CHANGED)
end

function EquipComponent:OnMsgEquipRandomGroupReset(arg0_UINT, arg1_UINT)
end

function EquipComponent:OnMsgEquipRandomSlotReset(arg0_UINT)
end

function EquipComponent:RetEquipRandomPropBatchEnhance(arg0_Result, arg1_UINT, arg2_UUID)
end

function EquipComponent:ReqEquipmentReform(slotId, equipId)
    self.remote:ReqEquipmentReform(slotId, equipId)
end

function EquipComponent:RetEquipmentReform(result, slotId, equipId, EQUIPMENT_RANDOM_PROP_INFO)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EquipmentSystem:OnEquipmentReform(slotId, equipId, EQUIPMENT_RANDOM_PROP_INFO)
    end
end

function EquipComponent:ReqGetBestReformList(slotId)
    self.remote:ReqGetBestReformList(slotId)
end

function EquipComponent:ReqSaveCurrentEquipmentReform(slotId, equipId, index)
    self.remote:ReqSaveCurrentEquipmentReform(slotId, equipId, index)
end

function EquipComponent:ReqGetReformMemerySpace()
    self.remote:ReqGetReformMemerySpace()
end

function EquipComponent:RetGetReformMemerySpace(wordList)
    Game.EquipmentSystem:OnGetReformMemerySpace(wordList)
end

function EquipComponent:ReqAutoReplaceRandomPropFromMemerySpace(slotId, gbId)
    self.remote:ReqAutoReplaceRandomPropFromMemerySpace(slotId, gbId)
end

function EquipComponent:RetAutoReplaceRandomPropFromMemerySpace(result, slotId, equipId, EQUIPMENT_RANDOM_PROP_INFO)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EquipmentSystem:OnEquipmentReform(slotId, equipId, EQUIPMENT_RANDOM_PROP_INFO)
    end
end

--- 请求置换记忆空间中的随机属性
---@param slot number 装备的slot
---@param gbId string 装备的uid
---@param propType number 属性类型
---@param key string 记忆空间中的key
---@param index number 随机属性index
---@param propId number 随机属性id
function EquipComponent:ReqReplaceOneRandomPropFromMemerySpace( slot, gbId, propType, key, index, propId)
    self.remote:ReqReplaceOneRandomPropFromMemerySpace(slot, gbId, propType, key, index, propId)
end

-- 返回 置换记忆空间中的随机属性
function EquipComponent:RetReplaceOneRandomPropFromMemerySpace( result, slot, equipId, propType, key, index, propId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EquipmentSystem:RetReplaceOneRandomPropFromMemerySpace(slot, equipId, propType, key, index, propId)
    end
end

return EquipComponent
