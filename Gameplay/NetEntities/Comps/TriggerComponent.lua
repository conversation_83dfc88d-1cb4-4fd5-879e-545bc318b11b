--- @class TriggerComponent

TriggerComponent = DefineComponent("TriggerComponent")
local TriggerUtils = kg_require("Shared.Utils.TriggerUtils")

function TriggerComponent:ctor ()
    self.triggerTempCounter = {}    --接取记录
    self.triggerForeverCounter = {} --历史记录
    -- 哪个系统需要接入trigger
    self.SystemsWithTrigger =
    {
        [0] = nil,
        [1] = nil,
        [2] = nil,
        [3] = nil,
        [4] = nil,
        [5] = nil,
        [6] = nil,
    }

end

--- @Param tempList 临时的condition信息
--- @Param foreverList 永久计数的condition
function TriggerComponent:OnMsgSyncTriggerInfo(tempList, foreverList)
    local SystemTempTriggerInfo = {}
    for k,v in pairs(tempList) do
        self.triggerTempCounter[k] = v
        local systemID, key = TriggerUtils.ParseTriggerKey(k)
        if SystemTempTriggerInfo[systemID] == nil then
            SystemTempTriggerInfo[systemID] = {}
        end
        SystemTempTriggerInfo[systemID][key] = v
    end
    local SystemForeverTriggerInfo = {}
    for k,v in pairs(foreverList) do
        self.triggerForeverCounter[k] = v
        local systemID, key = TriggerUtils.ParseTriggerKey(k)
        if SystemForeverTriggerInfo[systemID] == nil then
            SystemForeverTriggerInfo[systemID] = {}
        else
            SystemForeverTriggerInfo[systemID][key] = v
        end
    end
    Game.EventSystem:Publish(_G.EEventTypes.ON_ASYNC_TRIGGER_INFO, SystemTempTriggerInfo, SystemForeverTriggerInfo)
end

-- 系统ID_系统任务；第几个条件；当前数值
function TriggerComponent:OnMsgTriggerTempChange(triggerKey, customID, conditionIndex, value)
    if not self.triggerTempCounter[triggerKey] then
        self.triggerTempCounter[triggerKey] = {}
    end

    if not self.triggerTempCounter[triggerKey][customID] then
        self.triggerTempCounter[triggerKey][customID] = {}
    end

    self.triggerTempCounter[triggerKey][customID][conditionIndex] = value

    local systemID, key = TriggerUtils.ParseTriggerKey(triggerKey)
    Game.EventSystem:Publish(_G.EEventTypes.ON_TRIGGER_TEMP_CHANGE, systemID, key, conditionIndex, value)
end

function TriggerComponent:OnMsgTriggerForeverChange(triggerKey, value)
    self.triggerForeverCounter[triggerKey] = value

    local customID, conditionIndex = TriggerUtils.ParseTriggerKey(triggerKey)
    Game.EventSystem:Publish(_G.EEventTypes.ON_TRIGGER_FOREVER_CHANGE, customID, conditionIndex, value)
end

function TriggerComponent:OnMsgTriggerComplete(systemID, key, trigger)
    Game.EventSystem:Publish(_G.EEventTypes.ON_TRIGGER_COMPLETE, systemID, key, trigger)
end

function TriggerComponent:OnMsgTriggerTempClear(triggerKey, customID)
    local systemID, key = TriggerUtils.ParseTriggerKey(triggerKey)
    Game.EventSystem:Publish(_G.EEventTypes.ON_TRIGGER_TEMP_CLEAR, systemID, key, customID)
end

function TriggerComponent:OnMsgTriggerForeverClear(triggerKey)
    local systemID, key = TriggerUtils.ParseTriggerKey(triggerKey)
    Game.EventSystem:Publish(_G.EEventTypes.ON_TRIGGER_FOREVER_CLEAR, systemID, key)
end

function TriggerComponent:OnMsgTriggerCurrentRefresh(triggerKey)
    local systemID, key = TriggerUtils.ParseTriggerKey(triggerKey)
    Game.EventSystem:Publish(_G.EEventTypes.ON_TRIGGER_CURRENT_REFRESH, systemID, key)
end

return TriggerComponent