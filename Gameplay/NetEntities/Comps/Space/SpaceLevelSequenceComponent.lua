SpaceLevelSequenceComponent = DefineComponent("SpaceLevelSequenceComponent")

function SpaceLevelSequenceComponent:onLevelSequenceRebuild()
    local sequenceDict = self.levelSequenceDict
    if not sequenceDict or not next(sequenceDict) then
        return
    end

    self:DebugFmt("[onLevelSequenceRebuild]")
    for jobID, params in pairs(sequenceDict) do
        self:DoPlayLevelSequence(jobID, params)
    end
end

function SpaceLevelSequenceComponent:OnMsgPlayLevelSequence(jobID, newInfo)
    local sequenceDict = self.levelSequenceDict
    if not sequenceDict then
        sequenceDict = {}
        self.levelSequenceDict = sequenceDict
    end
    sequenceDict[jobID] = newInfo
    self:DoPlayLevelSequence(jobID, newInfo)
end

function SpaceLevelSequenceComponent:OnMsgStopLevelSequence(jobID)
    local sequenceDict = self.levelSequenceDict
    if sequenceDict and sequenceDict[jobID] then
        self.levelSequenceDict[jobID] = nil
        self:DoStopLevelSequence(jobID)
    else
        self:DebugErrorFormat("[OnMsgStopLevelSequence] jobID:%s not found", jobID)
    end
end

---@param jobID number
---@param assetID number
---@param startFrame number
---@param endFrame number
---@param startMark string
---@param endMark string
---@param sequenceType number shared.Const.SceneConst.LEVEL_SEQUENCE_TYPE
function SpaceLevelSequenceComponent:DoPlayLevelSequence(jobID, params)
    local tParams = {
        JobID = jobID,
        AssetID = params.assetID,
        StartFrame = params.startFrame > 0 and params.startFrame or nil,
        EndFrame = params.endFrame > 0 and params.endFrame or nil,
        StartMark = params.startMark ~= "" and params.startMark or nil,
        EndMark = params.endMark ~= "" and params.endMark or nil,
        SequenceType = params.sequenceType,
        StartTimeStamp = params.timestamp,
        serverSetPosition = params.pos,
        serverSetYaw = params.yaw,
        serverSetMode = params.anchorMode,
        WaitType = params.waitType,
        WaitTime = params.waitTime,
    }
    self:DebugFmt("[DoPlayLevelSequence] assetID=%s, jobID:%s", params.assetID, jobID)
    Game.CinematicManager:PlayLevelSequence(tParams)
end

---@param jobID number
function SpaceLevelSequenceComponent:DoStopLevelSequence(jobID)
    self:DebugFmt("[DoStopLevelSequence] jobID=%s", jobID)
    Game.CinematicManager:StopLevelSequence({
        JobID = jobID
    })
end

return SpaceLevelSequenceComponent
