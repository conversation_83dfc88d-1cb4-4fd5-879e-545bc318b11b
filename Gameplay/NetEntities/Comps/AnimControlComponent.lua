local AnimLibHelper = kg_require("GamePlay.3C.RoleComposite.AnimLibHelper")
local ViewAnimConst = kg_require("Gameplay.CommonDefines.ViewAnimConst")

AnimControlComponent = DefineComponent("AnimControlComponent")
AnimControlComponent.EStopAnimationReason = AnimControlComponent.EStopAnimationReason or {
	ExitWorld = 1,
	LocoStateChange = 2,
	ServerControl = 3
}

function AnimControlComponent:ctor()
	self.PlayingAnimQueueSeq = { }
	self.PlayingAnimQueueDuration = { }
end



function AnimControlComponent:__component_EnterWorld__()
	table.clear(self.PlayingAnimQueueSeq)
	table.clear(self.PlayingAnimQueueDuration)

	self:TryToRecoverAnimation()

	if Game.me.eid == self.eid then
		Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.LOCO_DEFAULT_LOCO_STATE_CHANGE, self, self.onRoleLocoDefaultStateChangeForAnimControl, self.eid)
	end
end
	
function AnimControlComponent:__component_ExitWorld__()
	if self.DelayPlayAnimTimer ~= nil then
		Game.TimerManager:StopTimerAndKill(self.DelayPlayAnimTimer)
		self.DelayPlayAnimTimer = nil
	end

	if Game.me.eid == self.eid then
		Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.LOCO_DEFAULT_LOCO_STATE_CHANGE, self, self.onRoleLocoDefaultStateChangeForAnimControl, self.eid)
	end

	self:StopSeverAnimation(AnimControlComponent.EStopAnimationReason.ExitWorld)
end


---@param Anims string @动画
---@param delay number @延时播放时间
function AnimControlComponent:OnMsgDelayPlayAnimation(Anims, Stage, delay)
	self:DebugFmt("[OnMsgDelayPlayAnimation] anim=%s, %s", Anims, self.eid)
	if self.DelayPlayAnimTimer then
		Game.TimerManager:StopTimerAndKill(self.DelayPlayAnimTimer)
		self.DelayPlayAnimTimer = nil
	end

	if delay > 0 then
		self.DelayPlayAnimTimer = Game.TimerManager:CreateTimerAndStart(function()
			self.DelayPlayAnimTimer = nil
			self:OnMsgPlayAnimation(Anims, Stage)
		end, delay * 1000, 1)
	else
		self:OnMsgPlayAnimation(Anims,Stage)
	end
end

function AnimControlComponent:OnMsgPlayAnimation(Anims, TargetStage, BlendTime)
	self:DebugFmt("[OnMsgPlayAnimation] anim=%s, %s, %s", Anims, TargetStage, self.eid)

	local AnimPlayFeatureID, _ = self:PlayAnimLibMontage(Anims, TargetStage, nil, BlendTime, 0.2, true)

	if AnimPlayFeatureID ~= nil  then
		self.SeverAnimPlayID = AnimPlayFeatureID
	end
end

function AnimControlComponent:OnPlayUpperAndLowerAnimation(UpperAnimAssetID, LowerAnimAssetID, UpperStartStage, LowerStartStage, EBlendType, bActionMeshSpaceBlend)
	self:DebugFmt("[OnPlayUpperAndLowerAnimation] Upper:%s %s, Lower:%s %s, eid:%s", UpperAnimAssetID, UpperStartStage, LowerAnimAssetID, LowerStartStage, self.eid)

	self:OnMsgPlayAnimation(LowerAnimAssetID, LowerStartStage)
	self:OnMsgPlayUpperAnimation(UpperAnimAssetID, UpperStartStage, EBlendType, bActionMeshSpaceBlend)
end

function AnimControlComponent:OnMsgPlayUpperAnimation(UpperAnimAssetID, UpperStartStage, EBlendType, bActionMeshSpaceBlend)
	EBlendType = EBlendType or 1
	self.bCanMoveWithOutStopAnimation = false

	local UpperAnimPlayFeatureID = self:PlayAnimLibMontageForUpper(UpperAnimAssetID, UpperStartStage, nil, nil, nil, EBlendType, bActionMeshSpaceBlend, true)

	if UpperAnimPlayFeatureID ~= nil then
		self.SeverAnimPlayUpperID = UpperAnimPlayFeatureID
	end
end

function AnimControlComponent:OnMsgPlayLocalPerformance(AnimAssetID)
	-- TODO：需要服务器下发告知是全身/半身动作
	local bUpperAnim = false
	self:TryStartPerformanceAnimFeature(ViewAnimConst.LOCAL_PERFORMANCE_ANIM_TYPE.ServerControl, bUpperAnim and ViewAnimConst.EAnimPlayReqTag.LocalUpperPerformance or ViewAnimConst.EAnimPlayReqTag.LocalPerformance, AnimAssetID)
end

function AnimControlComponent:OnMsgStopAnimation()
	self:StopSeverAnimation(AnimControlComponent.EStopAnimationReason.ServerControl)
end

function AnimControlComponent:StopSeverAnimation(StopReason)
	self:StopServerAnimation()

	local SeverAnimPlayUpperID = self.SeverAnimPlayUpperID
	if SeverAnimPlayUpperID then
		self:StopAnimLibMontageForUpper(SeverAnimPlayUpperID)
		self.SeverAnimPlayUpperID = nil
	end
end

function AnimControlComponent:StopServerAnimation()
	local SeverAnimPlayID = self.SeverAnimPlayID
	if SeverAnimPlayID then
		self:StopAnimLibMontage(SeverAnimPlayID)
		self.SeverAnimPlayID = nil
	end
end

function AnimControlComponent:GetAnimQueueData()
	local InstanceID = self.InstanceID
	if not InstanceID then
		return nil
	end

	local memberData = Game.WorldDataManager:GetCurLevelSceneActorData(InstanceID)
	if not memberData then
		return nil
	end

	local AnimQueue = memberData.AnimQueue
	if not AnimQueue then
		if memberData.GroupMember and next(memberData.GroupMember) then
			AnimQueue = memberData.GroupMember[1].AnimQueue
		end
	end
	return AnimQueue
end

function AnimControlComponent:TryToRecoverAnimation()
	-- local Animation = self.Animation
	-- if Animation == nil then
	-- 	return
	-- end

	-- -- Animation 验证
	-- local AnimStageToPlay = nil
	-- local bCanAnimationPlay = true
	-- local bCanUpperAnimationPlay, UpperAnimStageToPlay = AnimLibHelper.GetAnimStage(self:GetCurrentWorkingAnimAssetID(), Animation.FeatureName, Animation.StartTime, Animation.AnimStage)
	-- if Animation.EBlendType ~= 0 then
	-- 	bCanAnimationPlay, AnimStageToPlay = AnimLibHelper.GetAnimStage(self:GetCurrentWorkingAnimAssetID(), Animation.LowerFeatureName, Animation.StartTime, Animation.LowerAnimStage)
	-- end

	-- if bCanAnimationPlay then
	-- 	self:OnMsgPlayAnimation(Animation.FeatureName, AnimStageToPlay)
	-- end

	-- if Animation.EBlendType ~= 0 and bCanUpperAnimationPlay then
	-- 	self:OnMsgPlayUpperAnimation(bCanUpperAnimationPlay and Animation.FeatureName or nil,bCanAnimationPlay and UpperAnimStageToPlay or nil, Animation.EBlendType)
	-- end

	-- Animation 验证
	local Animation = self.Animation
	if Animation ~= nil then
		local bCanAnimationPlay, AnimStageToPlay = AnimLibHelper.GetAnimStage(self:GetCurrentWorkingAnimAssetID(), Animation.FeatureName, Animation.StartTime, Animation.AnimStage)
		if bCanAnimationPlay then
			self:OnMsgPlayAnimation(Animation.FeatureName, AnimStageToPlay)
		end
	end
	
	
	local HalfAnimation = self.HalfAnimation
	if HalfAnimation ~= nil then
		local bCanAnimationPlay, AnimStageToPlay = AnimLibHelper.GetAnimStage(self:GetCurrentWorkingAnimAssetID(), HalfAnimation.FeatureName, HalfAnimation.StartTime, HalfAnimation.AnimStage)
		if bCanAnimationPlay then
			self:OnMsgPlayUpperAnimation(HalfAnimation.FeatureName, AnimStageToPlay, HalfAnimation.EBlendType, HalfAnimation.bActionMeshSpaceBlend)
		end
	end
end

function AnimControlComponent:set_Animation(entity, new, old)
	if self.bInWorld == false then
		return
	end
	
	-- if new == nil then
	-- 	if old ~= nil then
	-- 		self:OnMsgStopAnimation()
	-- 	end
	-- elseif old ~= nil then
	-- 	local NewLowerFeatureName = new.LowerFeatureName
	-- 	local OldLowerFeatureName = old.LowerFeatureName
	-- 	if new.EBlendType and new.EBlendType ~= 0 then
	-- 		if new.EBlendType ~= old.EBlendType then
	-- 			self:OnPlayUpperAndLowerAnimation(new.FeatureName, new.LowerFeatureName, new.AnimStage, new.LowerAnimStage, new.EBlendType)
	-- 		elseif NewLowerFeatureName ~= OldLowerFeatureName or new.FeatureName ~= old.FeatureName then
	-- 			self:OnPlayUpperAndLowerAnimation(new.FeatureName, new.LowerFeatureName, new.AnimStage, new.LowerAnimStage, new.EBlendType)
	-- 		elseif new.AnimStage ~= old.AnimStage or new.LowerAnimStage ~= old.LowerAnimStage then
	-- 			self:OnPlayUpperAndLowerAnimation(new.FeatureName, new.LowerFeatureName, new.AnimStage, new.LowerAnimStage, new.EBlendType)
	-- 		elseif new.StartTime ~= old.StartTime then
	-- 			self:OnPlayUpperAndLowerAnimation(new.FeatureName, new.LowerFeatureName, new.AnimStage, new.LowerAnimStage, new.EBlendType)
	-- 		end
	-- 	else
	-- 		if OldLowerFeatureName and OldLowerFeatureName ~= "" then
	-- 			self:OnMsgPlayAnimation(self.Animation.FeatureName, self.Animation.AnimStage)
	-- 		elseif new.FeatureName ~= old.FeatureName or new.AnimStage ~= old.AnimStage or new.StartTime ~= old.StartTime then
	-- 			self:OnMsgPlayAnimation(self.Animation.FeatureName, self.Animation.AnimStage)
	-- 		end
	-- 	end
	-- else
	-- 	if new.EBlendType and new.EBlendType ~= 0 then
	-- 		self:OnPlayUpperAndLowerAnimation(new.FeatureName, new.LowerFeatureName, new.AnimStage, new.LowerAnimStage, new.EBlendType)
	-- 	else
	-- 		self:OnMsgPlayAnimation(self.Animation.FeatureName, self.Animation.AnimStage)
	-- 	end
	-- end
	
	if new == nil then
		if old ~= nil then
			self:StopServerAnimation()
		end
	elseif old ~= nil then
		if new.FeatureName ~= old.FeatureName or new.AnimStage ~= old.AnimStage or new.StartTime ~= old.StartTime then
			self:OnMsgPlayAnimation(new.FeatureName, new.AnimStage)
		end
	else
		self:OnMsgPlayAnimation(new.FeatureName, new.AnimStage)
	end
end

function AnimControlComponent:set_HalfAnimation(entity, new, old)
	if new == nil then
		if old ~= nil then
			local SeverAnimPlayUpperID = self.SeverAnimPlayUpperID
			if SeverAnimPlayUpperID then
				self:StopAnimLibMontageForUpper(SeverAnimPlayUpperID)
				self.SeverAnimPlayUpperID = nil
			end
		end
	elseif old ~= nil then
		if new.FeatureName ~= old.FeatureName or new.AnimStage ~= old.AnimStage or new.StartTime ~= old.StartTime or new.EBlendType ~= old.EBlendType or new.bActionMeshSpaceBlend ~= old.bActionMeshSpaceBlend then
			self:OnMsgPlayUpperAnimation(new.FeatureName, new.AnimStage, new.EBlendType, new.bActionMeshSpaceBlend)
		end
	else
		self:OnMsgPlayUpperAnimation(new.FeatureName, new.AnimStage, new.EBlendType, new.bActionMeshSpaceBlend)
	end
end

function AnimControlComponent:onRoleLocoDefaultStateChangeForAnimControl(bInEnter)
	--self:Debug("StopPerformanceAnimFeature, onRoleLocoDefaultStateChangeForAnimControl ", self.SeverAnimPlayUpperID, self.eid)
	if not bInEnter then
		self:StopServerAnimation()
	end
end
	
return AnimControlComponent