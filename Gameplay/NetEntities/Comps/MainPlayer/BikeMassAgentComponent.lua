local MassAgentCls = import("MassAgentComponent")

--- 动态切换MassAgent，支持氛围npc躲避自行车的功能，仅挂在MainPlayer上
BikeMassAgentComponent = DefineComponent("BikeMassAgentComponent")

function BikeMassAgentComponent:ctor()
    self.bIsOnBike = false
end

function BikeMassAgentComponent:dtor()
	
end

function BikeMassAgentComponent:__component_EnterWorld__()
	
end

function BikeMassAgentComponent:__component_ExitWorld__()
	
end

function BikeMassAgentComponent:ChangeBikeMassAgentState(IsOnBike)
	if self.bIsOnBike == IsOnBike then
		return
	end
	self.bIsOnBike = IsOnBike

	--- destroy cur comp
	local CurMassAgentCompID = self.CppEntity:KAPI_Actor_GetComponentByClass(MassAgentCls)
	if IsValidID(CurMassAgentCompID) then
		self.CppEntity:KAPI_Actor_DestroyComponent(CurMassAgentCompID)
	end
	local AssetPath = ""
	if IsOnBike then
		AssetPath = Game.TableData.GetCrowdNPCConstDataRow(Enum.ECrowdNPCConst.BikeMassAgentCompPath).Value
	else
		AssetPath = Game.TableData.GetCrowdNPCConstDataRow(Enum.ECrowdNPCConst.NPCMassAgentCompPath).Value
	end
	self:DoAsyncLoadAsset(AssetPath, "OnMassAgentCompAssetLoaded")
end

function BikeMassAgentComponent:OnMassAgentCompAssetLoaded(LoadID, MassAgentComponentClassID)
	if MassAgentComponentClassID == 0 then
		return
	end
	if self.bInWorld then
		self.CppEntity:KAPI_Character_RegisterComponentByClass(MassAgentComponentClassID, "")
	end
end

return BikeMassAgentComponent