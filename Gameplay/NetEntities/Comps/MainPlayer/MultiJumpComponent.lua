local ParallelBehaviorControlConst = kg_require("Shared.Const.ParallelBehaviorControlConst")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local LocoStateTypeToAnimStateNameMap = ViewControlConst.LocoStateTypeToAnimStateNameMap
local LOCO_ANIM_STATE_CONST = ParallelBehaviorControlConst.LOCO_ANIM_STATE_CONST
local EMovePosture = ParallelBehaviorControlConst.EMovePosture
-- local WEAK_FORCE_CONTROL_REASON_TAGS = ParallelBehaviorControlConst.WEAK_FORCE_CONTROL_REASON_TAGS
local LOCO_GROUP_STATE_CONST = ParallelBehaviorControlConst.LOCO_GROUP_STATE_CONST
local PARALLEL_BEHAVIOR_CONST = ParallelBehaviorControlConst.PARALLEL_BEHAVIOR_CONST

-- 控制玩家多段跳的组件，仅挂在MainPlayer上
---@class MultiJumpComponent: EntityBase
MultiJumpComponent = DefineComponent("MultiJumpComponent")




--region Important
function MultiJumpComponent:ctor()
    self.CurJumpStage = 0
    self.JumpStageCheckTimer = nil      -- 进入跳跃时，若一直无法离地，保底通过这个Timer重置CurJumpStage
    self.ResetJumpStageMaxTime = 2000   -- 跳跃后这个时间内一直处于地面状态，则重置CurJumpStage

    self.bReadyForMultiJump = false     -- 是否准备好开启多段跳
    self.bAllowMultiJump = true         -- 是否允许多段跳
    self.DelayAllowSecondJumpTime = -1  -- 一段跳后允许释放二段跳的延迟时间
    self.DelayAllowThirdJumpTime = -1   -- 二段跳后允许释放三段跳的延迟时间
    self.AllowMultiJumpHeight = 50      -- 允许二段跳的高度

    self.bForbidJump = false            -- 是否处于禁用再次跳跃状态
    self.ForbidJumpTimer = nil          -- 禁止跳跃的Timer

    self.StateConflictJumpExecuteTimer = nil    -- 执行跳跃状态冲突打断的冷却定时器

    self.LocoJumpCacheTriggerStartTimeStamp = nil    -- 跳跃指令缓存定时器开始时间戳
    self.TryLocoJumpCacheTriggerTimer = nil          -- 执行跳跃指令缓存的定时器
    self.LocoJumpCacheTime = 300                    -- 跳跃指令缓存持续时间
end

function MultiJumpComponent:dtor()

end

function MultiJumpComponent:BindMultiJump(InPlayerController)
    self:UpdateMultiJumpInfo()
    self:StartListenHasGroundSupportChange()
    self.bReadyForMultiJump = true
end

function MultiJumpComponent:UnBindMultiJump(InPlayerController)
    self.bReadyForMultiJump = false
    self.DelayAllowSecondJumpTime = -1
    self.DelayAllowThirdJumpTime = -1

    self:StopListenHasGroundSupportChange()
end

function MultiJumpComponent:__component_ExitWorld__()
    self:ClearTryLocoJumpCacheTrigger()
end
--endregion Important



function MultiJumpComponent:_onTryLocoJumpCacheTriggerTimer()
	local DeltaTime = _G._now() - self.LocoJumpCacheTriggerStartTimeStamp
	if DeltaTime > self.LocoJumpCacheTime then
		-- 超出持续时间了，缓存失效
		self:ClearTryLocoJumpCacheTrigger()
		return
	end
	if self:TryTriggerLocoJump(nil, true) then
		-- 缓存成功触发跳跃了，清除缓存
		self:ClearTryLocoJumpCacheTrigger()
	end
end



--region API
-- 设置/刷新跳跃输入缓存
function MultiJumpComponent:SetTryLocoJumpCacheTrigger()
    if self.TryLocoJumpCacheTriggerTimer == nil then
        self.LocoJumpCacheTriggerStartTimeStamp = _G._now()
		
		self.TryLocoJumpCacheTriggerTimer = self:AddTimer(0.033, -1, "_onTryLocoJumpCacheTriggerTimer")
        --self.TryLocoJumpCacheTriggerTimer = Game.TimerManager:CreateTimerAndStart(function()
        --    local DeltaTime = _G._now() - self.LocoJumpCacheTriggerStartTimeStamp
        --    if DeltaTime > self.LocoJumpCacheTime then
        --        -- 超出持续时间了，缓存失效
        --        self:ClearTryLocoJumpCacheTrigger()
        --        return
        --    end
        --    if self:TryTriggerLocoJump(nil, true) then
        --        -- 缓存成功触发跳跃了，清除缓存
        --        self:ClearTryLocoJumpCacheTrigger()
        --    end
        --end, 33, -1)
    else
        -- 缓存期间再次触发手动跳跃失败，只需要更新开始的时间戳，延长Timer持续时间即可
        self.LocoJumpCacheTriggerStartTimeStamp = _G._now()
    end
end

-- 清除跳跃输入缓存
function MultiJumpComponent:ClearTryLocoJumpCacheTrigger()
    if self.TryLocoJumpCacheTriggerTimer ~= nil then
		self:DelTimer(self.TryLocoJumpCacheTriggerTimer)
        --Game.TimerManager:StopTimerAndKill(self.TryLocoJumpCacheTriggerTimer)
		
        self.TryLocoJumpCacheTriggerTimer = nil
        self.LocoJumpCacheTriggerStartTimeStamp = nil
    end
end

function MultiJumpComponent:CanTriggerLocoJump()
	if (self.bEnableLocoControlSwitch == true and self.disableLocoJumpSwitcher:IsSwitchOn()) or self:CanParallelBehaviorTransfer(PARALLEL_BEHAVIOR_CONST.L_JUMP) == false
    or self.bForbidJump == true then
		return false
	end
	return true
end




function MultiJumpComponent:_onStateConflictJumpExecuteTimer()
	self:DelTimer(self.StateConflictJumpExecuteTimer)
	self.StateConflictJumpExecuteTimer = nil
end

MultiJumpComponent.SPIRITUALDOWNLOCOSTATE = "SpiritualDownLocoState"
MultiJumpComponent.SPIRITUAL_DOWN_FRONT_LOCOSTATES = MultiJumpComponent.SPIRITUAL_DOWN_FRONT_LOCOSTATES or {
    [LOCO_ANIM_STATE_CONST.SpiritualDash] = true,
    [LOCO_ANIM_STATE_CONST.BackUpState5] = true,    -- ToDelete
    [LOCO_ANIM_STATE_CONST.GlideLoop] = true,
    [LOCO_ANIM_STATE_CONST.JumpLoop] = true,
    [LOCO_ANIM_STATE_CONST.SpiritualJump] = true,
    [LOCO_ANIM_STATE_CONST.BackUpState4] = true,    -- ToDelete
}
function MultiJumpComponent:TryTriggerSpiritualDown()
    local bNeedSpiritualDown = (self.DebugHasSpiritualAbility and MultiJumpComponent.SPIRITUAL_DOWN_FRONT_LOCOSTATES[self.LocoAnimState]) or false
    if bNeedSpiritualDown then
        local LocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(LOCO_GROUP_STATE_CONST.NormalWalking)
        local TargetLocoState = LocoGroupInfo[MultiJumpComponent.SPIRITUALDOWNLOCOSTATE] or 1
        local TargetBlendInTime = LocoGroupInfo.SpiritualDownBlendInDuration

        self.CppEntity:KAPI_Movement_SetGravityVelocity(0.0, 0.0, 0.0)
        self:MainplayerLocomotionCrossfadeOrResetSequenceProgress(TargetLocoState, TargetBlendInTime)
    end
end

function MultiJumpComponent:TryTriggerLocoJump(_, bNoCacheTrigger)
    if not self:CanTriggerLocoJump() then
        if not bNoCacheTrigger then
            -- 非指令缓存触发时，会更新指令缓存信息
            self:SetTryLocoJumpCacheTrigger()
        end
        return false
    end

    Game.AutoNavigationSystem:StopNavigation()

    if self.bReadyForMultiJump == true then
        local JumpImplFunc = MultiJumpComponent.LocoGroupToJumpImplMap[self.LocoGroup]
        if JumpImplFunc ~= nil then
            if JumpImplFunc(self) == true then
                if self.StateConflictJumpExecuteTimer == nil then
                    self:SCExecute(Enum.EStateConflictAction.Jump, true)
					
					self.StateConflictJumpExecuteTimer = self:AddTimer(1, 1, "_onStateConflictJumpExecuteTimer")
					
                    --self.StateConflictJumpExecuteTimer = Game.TimerManager:CreateTimerAndStart(
                    --    function()
                    --        Game.TimerManager:StopTimerAndKill(self.StateConflictJumpExecuteTimer)
                    --        self.StateConflictJumpExecuteTimer = nil
                    --    end,
                    --    1000, 1
                    --)
                end
                Game.EventSystem:Publish(_G.EEventTypes.LOCO_TRIGGER_JUMP_SUCCESS)

                return true
            elseif not bNoCacheTrigger then
                -- 非指令缓存触发时，会更新指令缓存信息
                self:SetTryLocoJumpCacheTrigger()
            end
        end
    end

    return false
end

function MultiJumpComponent:SetAllowMultiJump(InbAllowMultiJump)
    self.bAllowMultiJump = InbAllowMultiJump
end


function MultiJumpComponent:_onJumpStageCheckTimer()
	self.CurJumpStage = 0
	self:DelTimer(self.JumpStageCheckTimer)
	self.JumpStageCheckTimer = nil
end

function MultiJumpComponent:SetCurJumpStage(InNewJumpStage)
    self:DebugFmt("MultiJumpComponent:SetCurJumpStage %s %d", self.eid, InNewJumpStage)
    self.CurJumpStage = InNewJumpStage
    if InNewJumpStage > 0 and self.CppEntity:KAPI_Movement_GetHasGroundSupport() then
        if self.JumpStageCheckTimer ~= nil then
			self:DelTimer(self.JumpStageCheckTimer)
            --Game.TimerManager:StopTimerAndKill(self.JumpStageCheckTimer)
            self.JumpStageCheckTimer = nil
        end
		
		self.JumpStageCheckTimer = self:AddTimer(self.ResetJumpStageMaxTime / 1000, 1, "_onJumpStageCheckTimer")
        --self.JumpStageCheckTimer = Game.TimerManager:CreateTimerAndStart(function()
        --    self.CurJumpStage = 0
        --    Game.TimerManager:StopTimerAndKill(self.JumpStageCheckTimer)
        --    self.JumpStageCheckTimer = nil
        --end, self.ResetJumpStageMaxTime, 1)
    end
end

-- 更新多段跳的技能、冷却等信息
function MultiJumpComponent:UpdateMultiJumpInfo()
    if self.LocoGroup == LOCO_GROUP_STATE_CONST.NormalWalking or self.LocoGroup == LOCO_GROUP_STATE_CONST.WaterWalk then
        self:SetAllowMultiJump(true)
        local LocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(self.LocoGroup)
        if LocoGroupInfo == nil or LocoGroupInfo.MultiJumpCoolDownList == nil then
            self.DelayAllowSecondJumpTime = -1
            self.DelayAllowThirdJumpTime = -1
        else
            self.DelayAllowSecondJumpTime = LocoGroupInfo.MultiJumpCoolDownList[1] or -1
            self.DelayAllowThirdJumpTime = LocoGroupInfo.MultiJumpCoolDownList[2] or -1
        end
    elseif self.LocoGroup == LOCO_GROUP_STATE_CONST.Ride then
        self:SetAllowMultiJump(false)
        self.DelayAllowSecondJumpTime = -1
        self.DelayAllowThirdJumpTime = -1
    end
end
--endregion API



--region Event
function MultiJumpComponent:StartListenHasGroundSupportChange()
    self:SetNeedNotifyHasGroundSupportChanged(true)
    self.OnHasGroundSupportChanged:Add(self, "OnMultiJumpReceiveHasGroundSupportChanged")

    if not self.CppEntity:KAPI_Movement_GetHasGroundSupport() then
        self:SetCurJumpStage(3)
    end
end

function MultiJumpComponent:StopListenHasGroundSupportChange()
    self:SetNeedNotifyHasGroundSupportChanged(false)
    self.OnHasGroundSupportChanged:Remove(self, "OnMultiJumpReceiveHasGroundSupportChanged")
    self:SetCurJumpStage(0)-- 重置多段跳状态
end

function MultiJumpComponent:OnMultiJumpReceiveHasGroundSupportChanged(InCurHasGroundSupport, InCurHasLocoGroundSupport)
    if InCurHasGroundSupport then
        -- 落地后，清空跳跃Stage和禁止跳跃状态
        if self.ForbidJumpTimer ~= nil then
			self:DelTimer(self.ForbidJumpTimer)
            --Game.TimerManager:StopTimerAndKill(self.ForbidJumpTimer)
            self.ForbidJumpTimer = nil
        end

        self:SetCurJumpStage(0)
        self.bForbidJump = false
    else
        if self.JumpStageCheckTimer ~= nil then
			self:DelTimer(self.JumpStageCheckTimer)
            --Game.TimerManager:StopTimerAndKill(self.JumpStageCheckTimer)
            self.JumpStageCheckTimer = nil
        end
    end
end
--endregion Event



--region JumpCoolDown
function MultiJumpComponent:SetMultiJumpForbidJumpCoolDown()
    if self.CurJumpStage == 0 then
        if self.DelayAllowSecondJumpTime > 0 then
            self:_InnerSetForbidJumpCoolDown(self.DelayAllowSecondJumpTime * 1000)
        end
    elseif self.CurJumpStage == 1 then
        if self.DelayAllowThirdJumpTime > 0 then
            self:_InnerSetForbidJumpCoolDown(self.DelayAllowThirdJumpTime * 1000)
        end
    end
end


function MultiJumpComponent:_onForbidJumpTimer()
	self.bForbidJump = false
end

function MultiJumpComponent:_InnerSetForbidJumpCoolDown(InCoolDown)
    if self.ForbidJumpTimer ~= nil then
		self:DelTimer(self.ForbidJumpTimer)
        --Game.TimerManager:StopTimerAndKill(self.ForbidJumpTimer)
        self.ForbidJumpTimer = nil
    end

    self.bForbidJump = true
	
	self.ForbidJumpTimer = self:AddTimer(InCoolDown / 1000, 1, "_onForbidJumpTimer")
    --self.ForbidJumpTimer = Game.TimerManager:CreateTimerAndStart(function()
    --    self.bForbidJump = false
    --end, InCoolDown, 1)
end
--endregion JumpCoolDown



--region MultiJumpImpl
MultiJumpComponent.JUMPFARLOCOSTATE = "JumpFarLocoState"
MultiJumpComponent.SPIRITUALJUMPLOCOSTATE = "SpiritualJumpLocoState"
MultiJumpComponent.JUMPSTAGE_TO_JUMPLOCOSTATE = MultiJumpComponent.JUMPSTAGE_TO_JUMPLOCOSTATE or {
    [0] = "JumpLocoState",
    [1] = "JumpSecondLocoState",
    [2] = "JumpThirdLocoState"
}
MultiJumpComponent.JUMPSTAGE_TO_JUMPBLENDINTIME = MultiJumpComponent.JUMPSTAGE_TO_JUMPBLENDINTIME or {
    [0] = "JumpBlendInDuration",
    [1] = "JumpSecondBlendInDuration",
    [2] = "JumpThirdBlendInDuration"
}
MultiJumpComponent.SPIRITUAL_JUMP_FRONT_LOCOSTATES = MultiJumpComponent.SPIRITUAL_JUMP_FRONT_LOCOSTATES or {
    [LOCO_ANIM_STATE_CONST.SpiritualJump] = true,
    [LOCO_ANIM_STATE_CONST.SpiritualDash] = true,
    [LOCO_ANIM_STATE_CONST.BackUpState4] = true,    -- ToDelete
    [LOCO_ANIM_STATE_CONST.BackUpState5] = true,    -- ToDelete
    [LOCO_ANIM_STATE_CONST.GlideLoop] = true,
    [LOCO_ANIM_STATE_CONST.ClimbIdle] = true,
    [LOCO_ANIM_STATE_CONST.ClimbMove] = true,
    [LOCO_ANIM_STATE_CONST.JumpStartFar] = true,
}
function MultiJumpComponent:NormalWalkingJumpImpl()
    if self.CurJumpStage > 2 then
        return false
    end

    if self.CurJumpStage == 0 then
        if not self.InBattle --[[and self.DebugAllowWaterWalk == true--]] and self:StartWaterWalk() == true then
            self:LocoMoveStopSkill(true)
            -- 非战斗状态尝试进入水面移动
            return true
        end
    elseif not self.bCanJumpInStamina or not self:CheckHeightForNormalWalkingMultiJump() then
        return false
    end

    self.airDodgeNum = AIR_DODGE_MAX_NUM
    self:LocoMoveStopSkill(true)

    local LocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(LOCO_GROUP_STATE_CONST.NormalWalking)
    local TargetJumpLocoState = LocoGroupInfo[MultiJumpComponent.JUMPSTAGE_TO_JUMPLOCOSTATE[self.CurJumpStage]] or 1
    local TargetJumpBlendInTime = LocoGroupInfo[MultiJumpComponent.JUMPSTAGE_TO_JUMPBLENDINTIME[self.CurJumpStage]] or 1
    local bNeedSpiritualJump = (self.DebugHasSpiritualAbility and MultiJumpComponent.SPIRITUAL_JUMP_FRONT_LOCOSTATES[self.LocoAnimState]) or false
    if bNeedSpiritualJump then
        -- 灵体跳跃
        TargetJumpLocoState = LocoGroupInfo[MultiJumpComponent.SPIRITUALJUMPLOCOSTATE] or 1
        TargetJumpBlendInTime = LocoGroupInfo.SpiritualJumpBlendInDuration
    elseif not self.InBattle and self.MovePosture == EMovePosture.Sprint and self.DebugHasSpiritualAbility then
        -- 非入战情况并且冲刺状态，触发远跳
        TargetJumpLocoState = LocoGroupInfo[MultiJumpComponent.JUMPFARLOCOSTATE] or 1
    end

    self.CppEntity:KAPI_Movement_SetGravityVelocity(0.0, 0.0, 0.0)
    self:MainplayerLocomotionCrossfadeOrResetSequenceProgress(TargetJumpLocoState, TargetJumpBlendInTime)
    if self.LocoAnimState == TargetJumpLocoState then
        -- 由于ABP与MovementMode时序不同，有可能落地清空CurJumpStage但是ABP仍处于相同LocoState，这时需要手动DoLocoJump
        self:TryDoLocoJumpByLocoState(TargetJumpLocoState)
    end

    if self.CurJumpStage > 0 then
        -- 临时rpc，待家元优化基于LocoState的体力扣除机制后删除
        self.remote.ReqConsumeStamina(false, bNeedSpiritualJump)
    end

    
    if not bNeedSpiritualJump then
        -- 灵性跳跃不计入多段跳的段数
        self:SetMultiJumpForbidJumpCoolDown()
        self:SetCurJumpStage(self.CurJumpStage + 1)
    else
        self:_InnerSetForbidJumpCoolDown(LocoGroupInfo.SpiritualJumpCoolDown)
    end

    return true
end

function MultiJumpComponent:RideJumpImpl()
    if self.MountEntity == nil or self.CurJumpStage > self:MountMaxJumpStageNumber() - 1 then
         return false
    end

	if self.CurJumpStage ~= 0 and not self.bCanJumpInStamina then
		return false
	end

    self.airDodgeNum = AIR_DODGE_MAX_NUM
    self:LocoMoveStopSkill(true)

    local LocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(LOCO_GROUP_STATE_CONST.Ride)
	local TargetJumpLocoState = LocoGroupInfo[MultiJumpComponent.JUMPSTAGE_TO_JUMPLOCOSTATE[self.CurJumpStage]] or 1
    local TargetJumpBlendInTime = LocoGroupInfo[MultiJumpComponent.JUMPSTAGE_TO_JUMPBLENDINTIME[self.CurJumpStage]] or 1
	
    self.CppEntity:KAPI_Movement_SetGravityVelocity(0.0, 0.0, 0.0)
	self.MountEntity:LocomotionCrossfadeInFixedTime(LocoStateTypeToAnimStateNameMap[TargetJumpLocoState], TargetJumpBlendInTime)
    self.MountEntity:SetLocomotionAllSequencePlayerProgressInState(LocoStateTypeToAnimStateNameMap[TargetJumpLocoState], 0.0)
    if self.LocoAnimState == TargetJumpLocoState then
        -- 由于ABP与MovementMode时序不同，有可能落地清空CurJumpStage但是ABP仍处于相同LocoState，这时需要手动DoLocoJump
        self:TryDoLocoJumpByLocoState(TargetJumpLocoState)
    end

    if self.CurJumpStage > 0 then
        -- 临时rpc，待家元优化基于LocoState的体力扣除机制后删除
        self.remote.ReqConsumeStamina(false, false)
    end

    self:SetCurJumpStage(self.CurJumpStage + 1)

	--self.CppEntity:KAPI_Movement_SetGravityVelocity(0.0, 0.0, 0.0)
	--if self.MountEntity then
	--	self.MountEntity:LocomotionCrossfadeInFixedTime(LocoStateTypeToAnimStateNameMap[LocoGroupInfo.JumpLocoState], LocoGroupInfo.DashBlendInDuration)
	--end
	--
	--self:SetCurJumpStage(1)

    return true
end

function MultiJumpComponent:WaterWalkJumpImpl()
    if self.CurJumpStage > 2 then
        return false
    end

    if self.CurJumpStage > 0 and (not self.bCanJumpInStamina or not self:CheckHeightForWaterWalkMultiJump()) then
        return false
    end

    self.airDodgeNum = AIR_DODGE_MAX_NUM
    self:LocoMoveStopSkill(true)

    local LocoGroupInfo = Game.TableData.GetLocoGroupStateDataRow(LOCO_GROUP_STATE_CONST.WaterWalk)
    local TargetJumpLocoState = LocoGroupInfo[MultiJumpComponent.JUMPSTAGE_TO_JUMPLOCOSTATE[self.CurJumpStage]] or 1
    local TargetJumpBlendInTime = LocoGroupInfo[MultiJumpComponent.JUMPSTAGE_TO_JUMPBLENDINTIME[self.CurJumpStage]] or 1

    self.CppEntity:KAPI_Movement_SetGravityVelocity(0.0, 0.0, 0.0)
    self:MainplayerLocomotionCrossfadeOrResetSequenceProgress(TargetJumpLocoState, TargetJumpBlendInTime)
    if self.LocoAnimState == TargetJumpLocoState then
        -- 由于ABP与MovementMode时序不同，有可能落地清空CurJumpStage但是ABP仍处于相同LocoState，这时需要手动DoLocoJump
        self:TryDoLocoJumpByLocoState(TargetJumpLocoState)
    end

    if self.CurJumpStage > 0 then
        -- 临时rpc，待家元优化基于LocoState的体力扣除机制后删除
        self.remote.ReqConsumeStamina(false, false)
    end

    self:SetMultiJumpForbidJumpCoolDown()
    self:SetCurJumpStage(self.CurJumpStage + 1)

    return true
end

function MultiJumpComponent:ClimbJumpImpl()
    self.CppEntity:KAPI_Movement_ProactivelyLeavingClimbStage()
    self:KCB_OnReceiveLeavingClimbStage()
    self:NormalWalkingJumpImpl()

    return true
end

-- 根据LocoGroup筛选需要使用的跳跃方法
MultiJumpComponent.LocoGroupToJumpImplMap = {
    [LOCO_GROUP_STATE_CONST.NormalWalking] = MultiJumpComponent.NormalWalkingJumpImpl,
    [LOCO_GROUP_STATE_CONST.Ride] = MultiJumpComponent.RideJumpImpl,
    [LOCO_GROUP_STATE_CONST.WaterWalk] = MultiJumpComponent.WaterWalkJumpImpl,
    [LOCO_GROUP_STATE_CONST.Climb] = MultiJumpComponent.ClimbJumpImpl,
}

-- 检查高度是否满足NormalWalking多段跳
function MultiJumpComponent:CheckHeightForNormalWalkingMultiJump()
    if self.CppEntity:KAPI_Movement_GetIsDetectGround() == false or self.CppEntity:KAPI_Movement_GetCurGroundDist() > self.AllowMultiJumpHeight then
        -- 高度过高检查不到地面，或者检查到地面且离地高度大于AllowMultiJumpHeight，则允许二三段跳
        return true
    end

    return false
end

-- 检查高度是否满足WaterWalk多段跳
function MultiJumpComponent:CheckHeightForWaterWalkMultiJump()
    if self.CppEntity:KAPI_Movement_GetIsWaterWalkAllowed() and self.CppEntity:KAPI_Movement_GetCurDistToWaterSurface() > self.AllowMultiJumpHeight then
        return true
    end

    return false
end
--endregion MultiJumpImpl

return MultiJumpComponent
