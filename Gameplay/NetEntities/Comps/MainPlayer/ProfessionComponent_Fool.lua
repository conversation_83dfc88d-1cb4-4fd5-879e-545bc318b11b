--- 占卜家职业机制
local WorldViewConst = kg_require("Gameplay.CommonDefines.WorldViewConst")
--- CardInfo类，用于管理占卜家卡牌信息
---@class CardInfo
-- luacheck: ignore
local CardInfo = {}
CardInfo.__index = CardInfo

-- 创建一个新的卡牌信息对象
function CardInfo.new(cardId, cardToken)
    local self = setmetatable({}, CardInfo)
    self.cardId = cardId  -- 卡牌ID
    self.cardToken = cardToken  -- 卡牌令牌（用于标识一组卡牌）
    self.color = cardId == Enum.ERoleMechanismConstData.FOOL_YELLOW_CARD_BUFF and "yellow" or "blue"
    self.effectIndex = -1  -- 特效索引，用于管理视觉效果
    return self
end

-- 获取卡牌颜色
function CardInfo:getColor()
    return self.color
end

-- 设置特效索引
function CardInfo:setEffectIndex(index)
    self.effectIndex = index
end

-- 获取特效索引
function CardInfo:getEffectIndex()
    return self.effectIndex
end

-- 获取特效标签
function CardInfo:getEffectTag()
    if self.effectIndex < 0 then
        return nil
    end
    return tostring(self.effectIndex) .. self.color
end

function ProfessionComponent:initForFool()
	if self.eid ~= Game.me.eid then
		return
	end
	
	-- 初始化
	self.cardToken = 1
	self.pendingComboInfoQueue = {}
	self.tokenToCardList = {}
	self.indexToUseFlag = {false, false, false, false}
	self.blueCardCount = 0
	self.yellowCardCount = 0
	self:createFoolCardAttachEntity()
end

function ProfessionComponent:unInitForFool()
	table.clear(self.tokenToCardList)
	self.blueCardCount = 0
	self.yellowCardCount = 0
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FOOL_CARD_REFRESH)
	self:removeFoolCardAttachEntity()
end

function ProfessionComponent:GetFoolCardList()
	return self.tokenToCardList and self.tokenToCardList[self.cardToken] or {}
end

function ProfessionComponent:OnBattleBuffLayerChangedForFool(buff)
	if buff.buffID == Enum.ERoleMechanismConstData.FOOL_BLUE_CARD_BUFF then
		self:onFoolBlueCardChange(buff.buffID, buff.layer)
	elseif buff.buffID == Enum.ERoleMechanismConstData.FOOL_YELLOW_CARD_BUFF then
		self:onFoolYellowCardChange(buff.buffID, buff.layer)
	elseif buff.buffID == Enum.ERoleMechanismConstData.FOOL_BLUE_BUFF then
		self:onBlueCardCombo(Enum.ERoleMechanismConstData.FOOL_BLUE_CARD_BUFF, self.cardToken)
	elseif buff.buffID == Enum.ERoleMechanismConstData.FOOL_YELLOW_BUFF then
		self:onYellowCardCombo(Enum.ERoleMechanismConstData.FOOL_YELLOW_CARD_BUFF, self.cardToken)
	end
end

function ProfessionComponent:onBlueCardCombo(comboCardId, comboCardToken)
	if self.isRebuilding then
		return
	end

	-- 先塞一张卡到3
	self:onFoolBlueCardChange(comboCardId, 3)

	local comboCardInfo = {comboCardId = comboCardId, comboCardToken = comboCardToken}
	self:addComboInfo(comboCardInfo)
	-- 数据层清理
	self.blueCardCount = 0
	self.yellowCardCount = 0
	self.cardToken = self.cardToken + 1
	for i = 1, #self.indexToUseFlag do
		self.indexToUseFlag[i] = false
	end

	self:processNextCombo()
end

function ProfessionComponent:onYellowCardCombo(comboCardId, comboCardToken)
	if self.isRebuilding then
		return
	end

	-- 先塞一张卡到3
	self:onFoolYellowCardChange(comboCardId, 3)

	local comboCardInfo = {comboCardId = comboCardId, comboCardToken = comboCardToken}
	self:addComboInfo(comboCardInfo)
	-- 数据层清理
	self.blueCardCount = 0
	self.yellowCardCount = 0
	self.cardToken = self.cardToken + 1
	for i = 1, #self.indexToUseFlag do
		self.indexToUseFlag[i] = false
	end

	self:processNextCombo()
end

function ProfessionComponent:addComboInfo(comboInfo)
	self.pendingComboInfoQueue[#self.pendingComboInfoQueue + 1] = comboInfo
end

function ProfessionComponent:processNextCombo()
	if self.processingCombo then
		return
	end

	if #self.pendingComboInfoQueue > 0 then
		local comboInfo = self.pendingComboInfoQueue[1]
		table.remove(self.pendingComboInfoQueue, 1)
		self:processCombo(comboInfo.comboCardId, comboInfo.comboCardToken)
	end
end

function ProfessionComponent:processCombo(comboCardId, comboCardToken)
	self.processingCombo = true
	-- 处理分两步，先等1100ms让卡片入场播完
	Game.TimerManager:CreateTimerAndStart(function()
		self:onFoolCardInFinish(comboCardId, comboCardToken)
	end, 1100, 1)
end

function ProfessionComponent:onFoolCardInFinish(comboCardId, comboCardToken)
	-- 让UI播combo动画
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FOOL_CARD_COMBO, comboCardId, comboCardToken)
	-- 播环绕卡牌的销毁特效并销毁loop特效
	self:comboCardEffects(comboCardToken)

	-- 销毁处理完之后，再处理下一个combo
	Game.TimerManager:CreateTimerAndStart(function()
		self.processingCombo = false
		self:refreshCardEffects()
		self.tokenToCardList[comboCardToken] = nil
		self:processNextCombo()
	end, 700, 1)
end

function ProfessionComponent:onFoolBlueCardChange(cardId, count)
	local cardList = self.tokenToCardList[self.cardToken]
	if not cardList then
		cardList = {}
		self.tokenToCardList[self.cardToken] = cardList
	end
	-- 只考虑增加
	if count > self.blueCardCount then
		for i = 1, count - self.blueCardCount do
			local cardInfo = CardInfo.new(cardId, self.cardToken)
			self:addCardEffect(cardInfo)
			table.insert(cardList, cardInfo)
			Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FOOL_CARD_ADD, cardInfo)
		end
	end
	self.blueCardCount = count
end

function ProfessionComponent:onFoolYellowCardChange(cardId, count)
	local cardList = self.tokenToCardList[self.cardToken]
	if not cardList then
		cardList = {}
		self.tokenToCardList[self.cardToken] = cardList
	end
	-- 只考虑增加
	if count > self.yellowCardCount then
		for i = 1, count - self.yellowCardCount do
			local cardInfo = CardInfo.new(cardId, self.cardToken)
			self:addCardEffect(cardInfo)
			table.insert(cardList, cardInfo)
			Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FOOL_CARD_ADD, cardInfo)
		end
	end
	self.yellowCardCount = count
end 

function ProfessionComponent:createFoolCardAttachEntity()
	if self.foolCardAttachEntityID ~= nil and Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID) ~= nil then
		return
	end
	local AttachEntity = self:AddAttachment_V2(WorldViewConst.ATTACH_ITEM_TYPE.SurroundActor, "Prop_4000106")
	local ModelData = Game.ActorAppearanceManager.AvatarModelLib["Prop_4000106"]
	if AttachEntity and ModelData then
		AttachEntity:SetAttachToForFoolCard(WorldViewConst.ATTACH_REASON.SurroundActor , self:uid(), 0,0,0 ,0,0,0, ModelData.SlotName)
		AttachEntity:ApplyAttach()
		self.foolCardAttachEntityID = AttachEntity.eid
	end
end

function ProfessionComponent:removeFoolCardAttachEntity()
	local AttachEntity = Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID)
	if AttachEntity then
		AttachEntity:ClearAttachEffects("bysystemcall")
		AttachEntity:destroy()
		self.foolCardAttachEntityID = nil
	end
end

function ProfessionComponent:findCanUseIndex()
	for i = 1, 4 do
		if not self.indexToUseFlag[i] then
			self.indexToUseFlag[i] = true
			return i
		end
	end
	return -1
end

--- 添加单张卡牌特效
---@param cardInfo CardInfo
---@return void
function ProfessionComponent:addCardEffect(cardInfo)
	local index = self:findCanUseIndex()
	if index < 0 then
		return
	end
	
	cardInfo:setEffectIndex(index)
	local attachEntity = Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID)
	local effectTag = cardInfo:getEffectTag()
	
	if attachEntity and effectTag then
		attachEntity:PlayAttachEffectByTag("bysystemcall", effectTag, 1)
	end
end

--- 三消卡牌特效
function ProfessionComponent:comboCardEffects(comboCardToken)
	local cardList = self.tokenToCardList[comboCardToken]
	if not cardList then
		return
	end

	local AttachEntity = Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID)
	if not AttachEntity then
		return
	end

	for i, cardInfo in ipairs(cardList) do
		local effectIndex = cardInfo:getEffectIndex()
		if effectIndex > 0 then
			local effectTag = cardInfo:getEffectTag()
			if not self.indexToUseFlag[effectIndex] then
				AttachEntity:ClearAttachEffects("bysystemcall", effectTag)
			end
			AttachEntity:PlayAttachEffectByTag("bysystemcall", effectTag .. "Disappear", 650)
			self.indexToUseFlag[effectIndex] = false
		end
	end
end

--- 刷新卡牌特效
function ProfessionComponent:refreshCardEffects()
	local AttachEntity = Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID)
	if not AttachEntity then
		return
	end
	AttachEntity:ClearAttachEffects("bysystemcall")
	local cardList = self:GetFoolCardList()
	for i, cardInfo in ipairs(cardList) do
		self:addCardEffect(cardInfo)
	end
end
