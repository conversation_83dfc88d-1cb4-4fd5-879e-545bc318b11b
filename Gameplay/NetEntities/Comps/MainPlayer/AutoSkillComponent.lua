local NSDT = Game.TableData.GetSkillDataNewTable()
local EJoyStickMode = kg_require("Shared.Const.AbilityConst").EJoyStickMode
local ConditionCheckUtil = kg_require("Gameplay.Combat.CombatEffect.Condition.EffectConditionRelationCheck").ConditionCheckUtil
local SkillCheck = kg_require("Gameplay.Combat.Skill.SkillCheck").SkillCheck
local SkillUtils = kg_require("Shared.Utils.SkillUtils")
local EReleaseSkillResult = kg_require("Shared.Const.AbilityConst").EReleaseSkillResult

-- 一键连招，模拟玩家按键的组件，仅挂在MainPlayer上
AutoSkillComponent = DefineComponent("AutoSkillComponent")



AutoSkillComponent.AutoSkillCustomData = {
    -- 技能按下后抬起时间
    UnpressTime = Game.TableData.GetConstDataRow("AUTO_SKILL_CAST_UNPRESS_TIME"),
    -- 移动暂停一键连招后延迟开启的时间
    MoveDelayTime = Game.TableData.GetConstDataRow("AUTO_SKILL_CAST_MOVE_DELAY_TIME"),
    -- 发生释放失败再次尝试的冷却时间
    ReleaseFailedCD = Game.TableData.GetConstDataRow("AUTO_SKILL_CAST_RELEASE_FAILED_CD"),

    -- 是否输出日志
    bDebugMode = false, 
}

-- region Important
function AutoSkillComponent:ctor()
    -- 当前激活的utoSkillSlots队列
    self.CurAutoSkillSlots = {}

    self.SC_ReleaseResults = {}
    -- 是否生在一键连招
    self.bIsAutoSkill = false

    -- 上次一键连招释放的技能ID
    self.LastAutoCastSkillID = nil
    -- 下次一键连招的触发定时器
    self.AutoCastSkillTimer = nil
    -- 抬起按钮的定时器
    self.AutoSkillUnPressTimer = nil
    -- 移动后延迟开启的定时器
    self.AutoSkillMoveDelayTimer = nil
    -- 释放失败时的冷却计时器
    self.CastFailedTimers = {}

    -- 根据优先级排序好的一键连招信息
    self.HighOrderedAutoSkillMsg = {}
    self.LowOrderedAutoSkillMsg = {}
    -- 一键连招编辑器配置的数据
    self.OrderedAutoSkillTreeMsg = {}

    -- 代表是否有正在进行的AutoSkillTree一键连招逻辑
    -- -1表示没有，>0表示正在执行self.OrderedAutoSkillTreeMsg[self.CurAutoSkillTreeIndex]的逻辑
    self.CurASTreeIndex = -1
    -- 如果有正在执行的一键连招树逻辑，CurASTreeSkillIndex表示执行到第几个技能
    self.CurASTreeSkillIndex = -1


    Game.EventSystem:AddListener(_G.EEventTypes.SKILL_POST_SKILL_EQUIP_CHANGED, self, self.onSkillEquipChanged)
    Game.EventSystem:AddListener(_G.EEventTypes.SKILLHUD_BTNPRESSED, self, self.OnSkillBtnPressed)
    Game.EventSystem:AddListener(_G.EEventTypes.SKILLHUD_REFRESH_SKILL_ACTIVATE_RESULT, self, self.onReceiveSkillActivateResult)

	Game.GlobalEventSystem:AddListener(EEventTypesV2.BATTLE_ON_SKILL_BLOCK, "onSkillBlock", self)
	
    Game.EventSystem:AddListener(_G.EEventTypes.LOCO_TRIGGER_DODGE_SUCCESS, self, self.StopAutoSkill)
    Game.EventSystem:AddListener(_G.EEventTypes.LOCO_TRIGGER_JUMP_SUCCESS, self, self.StopAutoSkill)
end

function AutoSkillComponent:dtor()
    if self.bIsAutoSkill then
        self:StopAutoSkill()
    end

    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.SKILL_POST_SKILL_EQUIP_CHANGED, self, self.onSkillEquipChanged)
    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.SKILLHUD_BTNPRESSED, self, self.OnSkillBtnPressed)
    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.SKILLHUD_REFRESH_SKILL_ACTIVATE_RESULT, self, self.onReceiveSkillActivateResult)

	Game.GlobalEventSystem:RemoveListener(EEventTypesV2.BATTLE_ON_SKILL_BLOCK, "onSkillBlock", self)
    
	Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.LOCO_TRIGGER_DODGE_SUCCESS, self, self.StopAutoSkill)
    Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.LOCO_TRIGGER_JUMP_SUCCESS, self, self.StopAutoSkill)
end
-- endregion Important



-- region API
function AutoSkillComponent:StartAutoSkill()
    local suc = self:SCExecute(Enum.EStateConflictAction.AutoSkill)
    if not suc then
        return
    end
	if self.bIsAutoSkill then
		return
	end
	
    self:InitAutoSkillTreeMsg()
    self:InitSkillMsg()

    if self.bIsAutoSkill == false and self:GetLockTargetUID(ETE.EBSTargetType.TT_Skill, true) == 0 then
        -- 第一次进入AutoSkill如果没有锁定，按照普攻的范围进行一次弱锁
        if #self.LowOrderedAutoSkillMsg > 0 then
            local CommonAttackMsg = self.LowOrderedAutoSkillMsg[#self.LowOrderedAutoSkillMsg]
            local ParentSkillID = CommonAttackMsg.ParentID
            
            local skillData = NSDT[ParentSkillID]
            local TargetType = SkillUtils.GetTargetType(skillData)
            local TargetCamp = SkillUtils.GetTargetCamp(skillData)
            local CastRange = skillData["Dist"]
            self:UpdateTargetList(
                ETE.EBSTargetType.TT_Skill,
				CastRange, 
                TargetCamp, 
                TargetType, 
                0.01,
                LDE.EBSLockingState.AutoLockTarget
            )
        end
    end

    self:TryReleaseAutoSkill()
end

function AutoSkillComponent:StopAutoSkill(bTemporarily)
    if not bTemporarily then
        self:SCRemove(Enum.EStateConflictAction.AutoSkill)
    end
    self:CancelAutoSkill(bTemporarily) 
end

function AutoSkillComponent:IsAutoSkill()
    return self.bIsAutoSkill == true
end

function AutoSkillComponent:CancelAutoSkill(bTemporarily)
    if self.AutoCastSkillTimer then
        Game.TimerManager:StopTimerAndKill(self.AutoCastSkillTimer)
        self.AutoCastSkillTimer = nil
    end
    self.LastAutoCastSkillID = nil
    if self.AutoSkillUnPressTimer then
        Game.TimerManager:StopTimerAndKill(self.AutoSkillUnPressTimer, true)
        self.AutoSkillUnPressTimer = nil
    end
    if self.AutoSkillMoveDelayTimer then
        Game.TimerManager:StopTimerAndKill(self.AutoSkillMoveDelayTimer)
        self.AutoSkillMoveDelayTimer = nil
    end

    if bTemporarily == true then
        self.AutoSkillMoveDelayTimer = Game.TimerManager:CreateTimerAndStart(function()
            self:TryReleaseAutoSkill()
        end, AutoSkillComponent.AutoSkillCustomData.MoveDelayTime, 1)
    else
		if self.bIsAutoSkill then
			self.bIsAutoSkill = false
			self.remote.ReqSetAutoSkillState(false)
		end
		
        -- Event
        Game.GlobalEventSystem:Publish(EEventTypesV2.AUTOSKILL_END)
    end
	return true
end
--endregion API



-- region Private
function AutoSkillComponent:InitAutoSkillTreeMsg()
    table.clear(self.OrderedAutoSkillTreeMsg)

    local ASTD = Game.CombatDataManager:GetAutoSkillTreeDT(self.Profession)
    if (ASTD ~= nil) and (ASTD.Nodes ~= nil) and (ASTD.Edges ~= nil) then
        local StartNodeID = ASTD.RootNodes[1]
        local CurNode = ASTD.Nodes[StartNodeID]
        if (CurNode == nil) then
            return
        end
    
        while(CurNode ~= nil and CurNode.OutEdges ~= nil and CurNode.OutEdges[1] ~= nil) do
            local CurEdge = ASTD.Edges[CurNode.OutEdges[1]]
            if CurEdge == nil then
                break
            end
    
            CurNode = ASTD.Nodes[CurEdge.EndNode]   -- 下一个节点
            if CurNode.SkillIDs ~= nil and CurNode.SkillIDs[1] ~= nil then
                local Conditions = CurEdge.Conditions

                -- 检查一键连招树配置的技能是否装配
                local bCurNodeValid = true
                local SlotOrder = {}
                for index, SkillID in ipairs(CurNode.SkillIDs) do
                    local tSkillSlot = self:FindSkillSlot(SkillID)
                    -- 未装配的技能，或NoAutoCast的技能，不允许加入连招树
                    if not self:CheckSkillValidForAutoCastTree(SkillID, tSkillSlot) then
                        bCurNodeValid = false
                        break
                    else
                        SlotOrder[index] = tSkillSlot
                    end
                end

                if bCurNodeValid == true then
                    table.insert(self.OrderedAutoSkillTreeMsg, {
                        Conditions = Conditions,
                        SkillSlots = SlotOrder,
                        SkillIDs = CurNode.SkillIDs
                    })
                end
            end
        end
    end

    if AutoSkillComponent.AutoSkillCustomData.bDebugMode then
        self:Debug("[AutoSkillCastMsg] InitAutoSkillTreeMsg Start!")
        for index, Msg in pairs(self.OrderedAutoSkillTreeMsg) do
            local tString = ""
            for _, tSkillID in ipairs(Msg.SkillIDs) do
                tString = tString .. tostring(tSkillID) .. " "
            end
            self:Debug("[AutoSkillCastMsg]:", index, tString)
        end
        self:Debug("[AutoSkillCastMsg] InitAutoSkillTreeMsg End!")
    end
end

function AutoSkillComponent:InitSkillMsg()
    table.clear(self.CurAutoSkillSlots)
    table.clear(self.HighOrderedAutoSkillMsg)
    table.clear(self.LowOrderedAutoSkillMsg)
    -- 临时测试逻辑 Start
    for _, AutoSkillSlot in ipairs(self.AutoSkillSlots) do
        --检查该角色本地储存的技能连招开启状态是否有冲突，如果有的话以本地为准
        --local SkillID = -1
        --local ProTableData = Game.TableData.GetProfessionSkillDataRow(Game.me.Profession)
        --if ProTableData and ProTableData.SkillList[1] then
        --    SkillID = ProTableData.SkillList[1]
        --end
		if ETE.EBSSkillSlot.SS_Attack ~= AutoSkillSlot then
			local SkillID = self:GetSkillIDBySlotNew(AutoSkillSlot)
			if Game.SkillSystem:CheckSkillEnabledForAutoSkill(SkillID) and AutoSkillSlot ~= -1 then
				table.insert(self.CurAutoSkillSlots, AutoSkillSlot)
			end
		end
    end
    --普通攻击槽位
    table.insert(self.CurAutoSkillSlots, ETE.EBSSkillSlot.SS_Attack)
    --[[
    for SkillID, Info in pairs(self.skillList) do
        if self:CheckSkillValidForCommonAutoCast(SkillID, Info.SkillSlot) then
            table.insert(self.CurAutoSkillSlots, Info.SkillSlot)
        end
    end
    self:UpdateAutoSkillSlotsToServer()
    ]]--
    -- 临时测试逻辑 End

    for _, AutoSkillSlot in ipairs(self.CurAutoSkillSlots) do
        local SkillID = self:GetSkillIDBySlotNew(AutoSkillSlot)
        if self:CheckSkillValidForCommonAutoCast(SkillID, AutoSkillSlot) then
            local tSkillDataRow = Game.TableData.GetSkillDataNewRow(self:GetParentSkillIDNew(SkillID))
            -- 普攻使用最低优先级
            local tMsg = {
                ParentID = tSkillDataRow.ID,
                Priority = AutoSkillSlot == ETE.EBSSkillSlot.SS_Attack and -1 or tSkillDataRow.AutoCastPriority or 0,
                SkillSlot = AutoSkillSlot,
                bInCoolDown = false,
            }
            if tMsg.Priority >= 6 then
                table.insert(self.HighOrderedAutoSkillMsg, tMsg)
            else
                table.insert(self.LowOrderedAutoSkillMsg, tMsg)
            end
        end
    end

    table.sort(self.HighOrderedAutoSkillMsg, function(a, b)
        return a.Priority > b.Priority
    end)
    table.sort(self.LowOrderedAutoSkillMsg, function(a, b)
        return a.Priority > b.Priority
    end)

    if AutoSkillComponent.AutoSkillCustomData.bDebugMode then
        self:Debug("[AutoSkillCastMsg] InitSkillMsg Start!")
        self:Debug("[AutoSkillCastMsg]HighOrder:")
        for index, Msg in pairs(self.HighOrderedAutoSkillMsg) do
            local tSkillData = Game.TableData.GetSkillDataNewRow(Msg.ParentID)
            self:Debug("[AutoSkillCastMsg]:", index, tSkillData.ID, tSkillData.Name, Msg.Priority)
        end
        self:Debug("[AutoSkillCastMsg]LowOrder:")
        for index, Msg in pairs(self.LowOrderedAutoSkillMsg) do
            local tSkillData = Game.TableData.GetSkillDataNewRow(Msg.ParentID)
            self:Debug("[AutoSkillCastMsg]:", index, tSkillData.SkillID, tSkillData.Name, Msg.Priority)
        end
        self:Debug("[AutoSkillCastMsg] InitSkillMsg End!")
    end
end

function AutoSkillComponent:TryReleaseAutoSkill()
	if not self.bIsAutoSkill then
		self.bIsAutoSkill = true
		self.remote.ReqSetAutoSkillState(true)
	end
	
    if self:IsInServerMorph() then
        self:StopAutoSkill()
        return
    end

    -- 没有正在执行的连招树节点时，先尝试激活高优先级普通单技能逻辑
    local bHasASTreeNode = true -- 当前是否有连招树节点正在执行
    if self.CurASTreeIndex < 0 and self.CurASTreeSkillIndex < 0 then
        for _, Msg in ipairs(self.HighOrderedAutoSkillMsg) do
            if Msg.bInCoolDown ~= true then
                if self:_InnerTryReleaseSingleAutoSkill(nil, Msg.SkillSlot) == true then
                    return
                end
            end
        end

        bHasASTreeNode = false
    end

    -- 尝试激活一键连招树的特殊逻辑
    if self:TryReleaseASTreeSkill() == true then
        return
    end

    -- 如果bHasASTreeNode为true并且运行到这里，说明一定是原有连招树节点执行失败，需要重新查找
    if bHasASTreeNode == true then
        for _, Msg in ipairs(self.HighOrderedAutoSkillMsg) do
            if Msg.bInCoolDown ~= true then
                if self:_InnerTryReleaseSingleAutoSkill(nil, Msg.SkillSlot) == true then
                    return
                end
            end
        end

        if self:TryReleaseASTreeSkill() == true then
            return
        end
    end

    -- 最后尝试激活低优先级普通单技能逻辑
    for _, Msg in ipairs(self.LowOrderedAutoSkillMsg) do
        if Msg.bInCoolDown ~= true then
            if self:_InnerTryReleaseSingleAutoSkill(nil, Msg.SkillSlot) == true then
                return
            end
        end
    end

    -- 没有一个技能释放成功的倒霉孩子，过0.2s再尝试一次
    self:_setAutoCastSkillTimer(nil, 0.2)
end

function AutoSkillComponent:TryReleaseASTreeSkill()
    if self.OrderedAutoSkillTreeMsg[self.CurASTreeIndex] ~= nil and self.OrderedAutoSkillTreeMsg[self.CurASTreeIndex].SkillSlots[self.CurASTreeSkillIndex] then
        -- 继续尝试执行未完成的一键连招树逻辑
        local bInnerReleaseSuccess = self:_InnerTryReleaseSingleAutoSkill(
            self.OrderedAutoSkillTreeMsg[self.CurASTreeIndex].SkillIDs[self.CurASTreeSkillIndex], 
            self.OrderedAutoSkillTreeMsg[self.CurASTreeIndex].SkillSlots[self.CurASTreeSkillIndex]
        )
        if bInnerReleaseSuccess == true then
            -- 未完成的一键连招树节点逻辑成功
            self.CurASTreeSkillIndex = self.CurASTreeSkillIndex + 1

            if self.OrderedAutoSkillTreeMsg[self.CurASTreeIndex].SkillSlots[self.CurASTreeSkillIndex] == nil then
                -- 所有键连招树节点执行完了
                self.CurASTreeIndex = -1
                self.CurASTreeSkillIndex = -1
            end

            return true
        else
            -- 已有一键连招树逻辑执行失败
            self.CurASTreeIndex = -1
            self.CurASTreeSkillIndex = -1
            return false
        end        
    end

    -- 无正在执行的一键连招树逻辑，开始查找
    self.CurASTreeIndex = -1
    self.CurASTreeSkillIndex = -1
    
    for AutoSkillTreeMsgIndex, AutoSkillTreeMsg in ipairs(self.OrderedAutoSkillTreeMsg) do
		local staticBlackboard = {}
		local targetEntity = self:GetLockTargetEntity(ETE.EBSTargetType.TT_Skill, true)
		if targetEntity then
			staticBlackboard.lockTarget = targetEntity:uid()
		end
		if (AutoSkillTreeMsg.Conditions == nil) or (ConditionCheckUtil.CheckTaskConditionGroup(AutoSkillTreeMsg.Conditions, self, staticBlackboard) == true) then
        --if (BSFunc.ExeBSAConditionForAutoSkill(AutoSkillTreeMsg.Conditions) == true) then
            local bAllSkillAllow = true
            for index, AutoSkillID in ipairs(AutoSkillTreeMsg.SkillIDs) do
                local tSkillDT = Game.TableData.GetSkillDataNewRow(AutoSkillID)
                if self:CanAutoReleaseSkill(tSkillDT, AutoSkillTreeMsg.SkillSlots[index]) == false then
                    bAllSkillAllow = false
                    break
                end
            end

            if bAllSkillAllow == true then
                if self:_InnerTryReleaseSingleAutoSkill(AutoSkillTreeMsg.SkillIDs[1], AutoSkillTreeMsg.SkillSlots[1], true) then
                    self.CurASTreeIndex = AutoSkillTreeMsgIndex
                    self.CurASTreeSkillIndex = 2

                    if AutoSkillTreeMsg.SkillSlots[self.CurASTreeSkillIndex] == nil then
                        -- 连招树节点就配了一个技能，结束
                        self.CurASTreeIndex = -1
                        self.CurASTreeSkillIndex = -1
                    end

                    return true
                end
            end
        end
    end

    return false
end

function AutoSkillComponent:_InnerTryReleaseSingleAutoSkill(InExpectedSkillID, InSkillSlot, bDoNotCheck)
    local SkillID = self:GetSkillIDBySlotNew(InSkillSlot)
    -- InExpectedSkillID不为空时，检查技能ID
    if InExpectedSkillID ~= nil and SkillID ~= InExpectedSkillID then
        -- 当前槽位待释放技能与预期技能不符
        if AutoSkillComponent.AutoSkillCustomData.bDebugMode then
            self:Debug("[AutoSkillCastMsg]TryReleaseAutoSkill Failed, ExpectedSkillID:", InExpectedSkillID, "SkillSlot:", InSkillSlot, "CurrentSlotSkillID:", SkillID)
        end
        return false
    end

    local tSkillDT = Game.TableData.GetSkillDataNewRow(SkillID)
    if bDoNotCheck == true or self:CanAutoReleaseSkill(tSkillDT, InSkillSlot) then
        local SkillRecoveryStartTime = self:_InnerReleaseSingleAutoSkill(tSkillDT, InSkillSlot)
        self:_setAutoCastSkillTimer(SkillID, SkillRecoveryStartTime)

        return true
    end

    return false
end

function AutoSkillComponent:_InnerReleaseSingleAutoSkill(InSkillDT, InSkillSlot)
    local SkillRecoveryStartTime = 0.2

    if AutoSkillComponent.AutoSkillCustomData.bDebugMode then
        self:Debug("[AutoSkillCastMsg]onSkillHUDBtnPressed. SkillID:", InSkillDT.ID, "SkillSlot:", InSkillSlot, "Time:", Game.BSManager:GetFrameMillisecond())
    end
    self.InputProcessor:onSkillHUDBtnPressed(InSkillSlot)
        
    if self.AutoSkillUnPressTimer then
        Game.TimerManager:StopTimerAndKill(self.AutoSkillUnPressTimer, false)
        self.AutoSkillUnPressTimer = nil
    end
    self.AutoSkillUnPressTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:UnPressSkill(InSkillSlot)
    end, AutoSkillComponent.AutoSkillCustomData.UnpressTime, 1)

    local SkillData = NSDT[InSkillDT.ID]
    local ReleaseType = SkillData["JoyStickMode"]
    local SelectTargetType = SkillUtils.GetTargetSetting(SkillData)
    if (ReleaseType ~= EJoyStickMode.NoJoyStick or ReleaseType ~= EJoyStickMode.LeftJoyStick) and (SelectTargetType <= 1) then
        -- 非指向性技能直接释放
        SkillRecoveryStartTime = math.max(InSkillDT.CancelPoint + 0.1, SkillRecoveryStartTime)
    else
        -- 抬起释放
        SkillRecoveryStartTime = math.max(InSkillDT.CancelPoint + 0.1 + AutoSkillComponent.AutoSkillCustomData.UnpressTime * 0.001, SkillRecoveryStartTime)
    end

    return SkillRecoveryStartTime
end

function AutoSkillComponent:UnPressSkill(InSkillSlot)
    if self.InputProcessor ~= nil then
        if AutoSkillComponent.AutoSkillCustomData.bDebugMode then
            self:Debug("[AutoSkillCastMsg]onSkillHUDBtnReleased. SkillSlot:", InSkillSlot, "Time:", Game.BSManager:GetFrameMillisecond())
        end
        self.InputProcessor:onSkillHUDBtnReleased(InSkillSlot, true)
    end
    if self.AutoSkillUnPressTimer then
        Game.TimerManager:StopTimerAndKill(self.AutoSkillUnPressTimer)
        self.AutoSkillUnPressTimer = nil
    end
end

function AutoSkillComponent:CanAutoReleaseSkill(InSkillDT, InSkillSlot)
    if not AutoSkillComponent.AutoSkillCustomData.bDebugMode then
        if InSkillDT ~= nil and self:CanReleaseSkillNew(InSkillDT.ID) and
        self.InputProcessor:CheckNewSkillTarget(InSkillDT, InSkillSlot, true) then
            return true
        end

        return false
    else
        -- Debug Version Start
        if InSkillDT == nil then
            return false
        end

        local Res = SkillCheck.CheckCanCastSkill(self, InSkillDT)
        if Res ~= EReleaseSkillResult.Sucess then
            self:Debug("[AutoSkillCastMsg]CheckSkill Failed, SkillID:", InSkillDT.ID, "Reason:", Res)
            return false
        end

        if self.InputProcessor:CheckNewSkillTarget(InSkillDT, InSkillSlot, true) == false then
            self:Debug("[AutoSkillCastMsg]CheckSkill Failed, SkillID:", InSkillDT.ID, "Reason: No Skill Target!")
            return false
        end

        self:Debug("[AutoSkillCastMsg]CheckSkill Success, SkillID:", InSkillDT.ID, "CancelPoint:", InSkillDT.CancelPoint)
        return true
        -- Debug Version End
    end
end

-- 检查技能是否允许在通用优先级里配置一键连招（只检查配置数据，不检查是否装配）
---@param bCheckSelectionOnly boolean 是否只是检查技能能不能连招，快捷HUD判断可勾选时使用
function AutoSkillComponent:CheckSkillValidForCommonAutoCast(InSkillID, InSkillSlot, bCheckSelectionOnly)
    if InSkillSlot ~= ETE.EBSSkillSlot.SS_ReviveSlot then
        -- 检查技能是否解锁
        local ParentID = self:GetParentSkillIDNew(InSkillID)
        if self.skillList[ParentID] == nil or self.skillList[ParentID].SkillUnlocked ~= 1 then
            return false
        end

        local tSkillDataRow = Game.TableData.GetSkillDataNewRow(self:GetParentSkillIDNew(InSkillID))
        -- 普攻槽位不受NoAutoCast影响一定会加入
		-- 判断玩家是否可以勾选时不用考虑TreeOnly
		if bCheckSelectionOnly then
			if tSkillDataRow ~= nil and ((tSkillDataRow.NoAutoCast ~= true) or InSkillSlot == ETE.EBSSkillSlot.SS_Attack) then
				return true
			end
		end
		if tSkillDataRow ~= nil and ((tSkillDataRow.NoAutoCast ~= true and tSkillDataRow.AutoCastTreeOnly ~= true) or InSkillSlot == ETE.EBSSkillSlot.SS_Attack) then
			return true
		end
    end

    return false
end

-- 检查技能是否允许在连招树里配置一键连招（只检查配置数据，不检查是否装配）
function AutoSkillComponent:CheckSkillValidForAutoCastTree(InSkillID, InSkillSlot)
    -- 检查技能是否解锁
    local ParentID = self:GetParentSkillIDNew(InSkillID)
    if self.skillList[ParentID] == nil or self.skillList[ParentID].SkillUnlocked ~= 1 then
        return false
    end

    local tSkillDataRow = Game.TableData.GetSkillDataNewRow(ParentID)
    -- 未装配的技能，或NoAutoCast的技能，不允许加入连招树
    if InSkillSlot == ETE.EBSSkillSlot.SS_TMax or tSkillDataRow == nil or tSkillDataRow.NoAutoCast == true then
        return false
    end

    return true
end

-- 设置下次一键连招的触发定时器
function AutoSkillComponent:_setAutoCastSkillTimer(InSkillID, InNextReleaseTime)
    self.LastAutoCastSkillID = InSkillID

    if self.AutoCastSkillTimer then
        Game.TimerManager:StopTimerAndKill(self.AutoCastSkillTimer)
        self.AutoCastSkillTimer = nil
    end
    if AutoSkillComponent.AutoSkillCustomData.bDebugMode then
        self:Debug("[AutoSkillCastMsg]Next TryReleaseAutoSkill Time:", InNextReleaseTime, "CurTime:", Game.BSManager:GetFrameMillisecond())
    end
    self.AutoCastSkillTimer = Game.TimerManager:CreateTimerAndStart(function()
        self:TryReleaseAutoSkill()
    end, InNextReleaseTime * 1000, 1)
end
-- endregion Private



-- region Event
function AutoSkillComponent:onSkillEquipChanged()
    --Todo：增加衍生技能时加入衍生技能
    if self.bIsAutoSkill then
        self:InitAutoSkillTreeMsg()
        self:InitSkillMsg()
    end
end

function AutoSkillComponent:OnSkillBtnPressed()
    self:StopAutoSkill()
end

function AutoSkillComponent:__component_EnterWorld__()
    self:InitAutoSkillTreeMsg()
    self:InitSkillMsg()
end

function AutoSkillComponent:__component_ExitWorld__()
    self:StopAutoSkill()
end

function AutoSkillComponent:__component_MoveInput__()
    -- 有移动输入时，推迟开启一键连招
    if self.bIsAutoSkill then
        self:StopAutoSkill(true)
    else
        if self.AutoSkillMoveDelayTimer then
            Game.TimerManager:StopTimerAndKill(self.AutoSkillMoveDelayTimer)
            self.AutoSkillMoveDelayTimer = nil
            self.AutoSkillMoveDelayTimer = Game.TimerManager:CreateTimerAndStart(function()
                self:TryReleaseAutoSkill()
            end, AutoSkillComponent.AutoSkillCustomData.MoveDelayTime, 1)
        end
    end
end

function AutoSkillComponent:onReceiveSkillActivateResult(InSkillSlot, InRes)
    if (InRes ~= EReleaseSkillResult.Sucess) and (InRes ~= EReleaseSkillResult.DelaySucess) then
        for _, Msg in ipairs(self.LowOrderedAutoSkillMsg) do
            if Msg.SkillSlot == InSkillSlot then
                Msg.bInCoolDown = true

                if self.CastFailedTimers[InSkillSlot] ~= nil then
                    Game.TimerManager:StopTimerAndKill(self.CastFailedTimers[InSkillSlot])
                    self.CastFailedTimers[InSkillSlot] = nil
                end
                self.CastFailedTimers[InSkillSlot] = Game.TimerManager:CreateTimerAndStart(function()
                    Msg.bInCoolDown = false
                    Game.TimerManager:StopTimerAndKill(self.CastFailedTimers[InSkillSlot])
                    self.CastFailedTimers[InSkillSlot] = nil
                end, AutoSkillComponent.AutoSkillCustomData.ReleaseFailedCD, 1)

                break
            end
        end

        for _, Msg in ipairs(self.HighOrderedAutoSkillMsg) do
            if Msg.SkillSlot == InSkillSlot then
                Msg.bInCoolDown = true

                if self.CastFailedTimers[InSkillSlot] ~= nil then
                    Game.TimerManager:StopTimerAndKill(self.CastFailedTimers[InSkillSlot])
                    self.CastFailedTimers[InSkillSlot] = nil
                end
                self.CastFailedTimers[InSkillSlot] = Game.TimerManager:CreateTimerAndStart(function()
                    Msg.bInCoolDown = false
                    Game.TimerManager:StopTimerAndKill(self.CastFailedTimers[InSkillSlot])
                    self.CastFailedTimers[InSkillSlot] = nil
                end, AutoSkillComponent.AutoSkillCustomData.ReleaseFailedCD, 1)

                break
            end
        end
    end
end

-- region SkillActivateResult
function AutoSkillComponent:UpdateSkillActivateResultMsg(inSlot, inReleaseResult)
    if (self.SC_ReleaseResults[inSlot] ~= nil) then
        Game.EventSystem:Publish(_G.EEventTypes.SKILLHUD_REFRESH_SKILL_ACTIVATE_RESULT, inSlot, inReleaseResult)
    end

    self.SC_ReleaseResults[inSlot] = nil
end

function AutoSkillComponent:RequestNewSkillActivateResultMsg(InSlot)
    self.SC_ReleaseResults[InSlot] = true
end
-- endregion SkillActivateResult

function AutoSkillComponent:onSkillBlock()
    self:StopAutoSkill()
end

function AutoSkillComponent:__component_BeforeStartMorph__(MorphData)
    self:StopAutoSkill()
end

function AutoSkillComponent:__component_BeforeEndMorph__(MorphData)
    self:StopAutoSkill()
end
-- endregion Event


-- region Config
function AutoSkillComponent:ChangeAutoSkillSlot(bAddOrRemove, inSkillSlot)
    if bAddOrRemove == true then
        for _, AutoSkillSlot in ipairs(self.CurAutoSkillSlots) do
            if AutoSkillSlot == inSkillSlot then
                return false
            end
        end

        if self:CheckSkillValidForCommonAutoCast(self:GetSkillIDBySlotNew(inSkillSlot), inSkillSlot) then
            table.insert(self.CurAutoSkillSlots, inSkillSlot)

            self:UpdateAutoSkillSlotsToServer()
            self:onSkillEquipChanged()
            return true
        end

        return false
    else
        local tIndex = -1
        for index, AutoSkillSlot in ipairs(self.CurAutoSkillSlots) do
            if AutoSkillSlot == inSkillSlot then
                tIndex = index
                break
            end
        end
        if tIndex > 0 then
            table.remove(self.CurAutoSkillSlots, tIndex)

            self:UpdateAutoSkillSlotsToServer()
            self:onSkillEquipChanged()
            return true
        end

        return false
    end
end

function AutoSkillComponent:SetAutoSkillSlots(InSlots)
    table.clear(self.CurAutoSkillSlots)

    for _, SkillSlot in ipairs(InSlots) do
        if self:CheckSkillValidForCommonAutoCast(self:GetSkillIDBySlotNew(SkillSlot), SkillSlot) then
            table.insert(self.CurAutoSkillSlots, SkillSlot)
        end
    end

    self:UpdateAutoSkillSlotsToServer()
    self:onSkillEquipChanged()
end

function AutoSkillComponent:UpdateAutoSkillSlotsToServer()
    -- 将当前客户端的一键连招配置上传至服务器
    self.remote.ReqChangeAutoSkillSlots(self.CurAutoSkillSlots)
end
-- endregion Config



return AutoSkillComponent
