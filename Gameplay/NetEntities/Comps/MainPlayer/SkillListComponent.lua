local NetworkManager = require "Framework.DoraSDK.NetworkManager"
local StringConst = kg_require("Data.Config.StringConst.StringConst")
local skillUtils = kg_require("Shared.Utils.SkillUtils")


SkillListComponent = DefineComponent("SkillListComponent")

function SkillListComponent:ctor()
    -- self:Debug("SkillListComponent.unlockedSkillList", self.eid, CommonUtils.tprint(self.unlockedSkillList))
    -- self:Debug("SkillListComponent.skillList", self.eid, CommonUtils.tprint(self.skillList))
	self.skillSlotToID = {}
end

function SkillListComponent:__component_EnterWorld__()
	self:RefreshSkillSlotToID()
	-- 最后执行
	Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ON_SKILL_READY)
end

function SkillListComponent:RefreshSkillSlotToID()
	table.clear(self.skillSlotToID)
	for _, skillEntry in pairs(self.skillList) do
		self.skillSlotToID[skillEntry.SkillSlot] = skillEntry.SkillID
	end
end

function SkillListComponent:IsSkillLocked(SkillID)
	local Data = Game.TableData.GetSkillDataNewRow(SkillID)
	if Data and Data.ParentSkill ~= 0 then
		SkillID = Data.ParentSkill
	end

	local unlock = false
	local skillEntry = self.skillList[SkillID]
	if skillEntry then
		unlock = skillEntry.SkillUnlocked == 1
	else
		unlock = self.unlockedSkillList[SkillID] ~= nil
	end
	

	if self.BalanceRuleID > 0 then
		local _, isUnlock = skillUtils.GetBalanceRuleOverrideSkillInfo(Game.TableData, self.BalanceRuleID, SkillID, unlock)
		return not isUnlock
	end
	return not unlock
end

-- 根据ID获取技能所在的插槽
function SkillListComponent:FindSkillSlot(InSkillID)
	local skillData = Game.TableData.GetSkillDataNewRow(InSkillID)
	local parentSkillID = InSkillID
	if skillData and skillData.ParentSkill > 0 then
		parentSkillID = skillData.ParentSkill
	end

	local skillEntry = self.skillList[parentSkillID]
	if not skillEntry then
		return -1
	end
	return skillEntry.SkillSlot
end

function SkillListComponent:ReqEquipSkill(skillId, skillSlot)
	self.remote.ReqEquipSkill(skillId, skillSlot)
end

function SkillListComponent:OnMsgUnlockSkillWheelSkill(skillId)
    self:Debug("OnMsgUnlockSkillWheelSkill", self.eid, skillId)
    local SkillInfo = Game.TableData.GetSkillDataNewRow(skillId)
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_ACTION_UNLOCK_CHECK, {{SkillInfo.Name,},{StringConst.Get("GET_SKILL_REMINDER_TITLE"),}},{SkillInfo.SkillIcon,})
end

function SkillListComponent:RetEquipSkill(result)
	if result.Code == 0 then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_MANAGE_SKILL_SWITCH)
	else
		self:WarningFmt("SkillCustomizer Server returns Skill Equip Failed: ErrorCode:%s", result.Code)
	end
    self:Debug("RetEquipSkill", self.eid, result.Code)
    NetworkManager.HandleRequestRet("EquipSkill", result)
end

---@param skillId number: int, 技能id
---@param lvlUpDelta number: int, 等级变化delta值
function SkillListComponent:ReqLevelUpSkill(skillId, lvlUpDelta)
	self.remote.ReqLevelUpSkill(skillId, lvlUpDelta)
end

---@param skillId number: int, 技能id
---@param lvlUpDelta number: int, 等级变化delta值
---@param result: 错误吗
function SkillListComponent:RetLevelUpSkill(result,skillId, lvlUpDelta)
    if result.Code == Game.ErrorCodeConst.SKILL_LEVEL_UP_COST_NOT_ENOUGH then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_MANAGE_CONSUME_LACK)
        self:Debug("RetLevelUpSkill", self.eid, skillId, lvlUpDelta, result.Code, result.Trace)
    else 
        if result.Code == Game.ErrorCodeConst.NO_ERR then
            Game.EventSystem:Publish(EEventTypes.ON_SKILL_LEVEL_UP, skillId)
        end
    end
    NetworkManager.HandleRequestRet("LevelUpSkill", skillId, lvlUpDelta, result)
end

---@param skillId number: int, 技能id
function SkillListComponent:ReqUnlockSkill(skillId)
	self.remote.ReqUnlockSkill(skillId)
end


---@param result: 错误吗
function SkillListComponent:RetUnlockSkill(result, skillId)
    self:Debug("RetUnlockSkill", self.eid,  result.Code, result.Trace, skillId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(EEventTypes.ON_SKILL_CUSTOMIZER_OMNI_REFRESH, skillId)
    end
    NetworkManager.HandleRequestRet("UnlockSkill", result)
end

---@param presetId: 推荐方案Id
function SkillListComponent:ReqApplySkillPreset(presetId, schemeId)
    self.remote.ReqApplySkillPreset(presetId, schemeId)
end

---@param result: 错误码
---@param schemeNum: 套装方案数量
function SkillListComponent:RetAddSkillPreset(result, schemeNum)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_SCHEME_ADDED, schemeNum)
end

function SkillListComponent:ReqAddSkillPreset()
    self.remote:ReqAddSkillPreset()
end

---@param result: 错误吗
---@param presetId: 推荐方案Id
---@param schemeIndex: 套装Id
function SkillListComponent:RetApplySkillPreset(result, presetId, schemeIndex)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_PRESET_APPLY, schemeIndex)
    end
    self:Debug("RetApplySkillPreset", self.eid, result.Code, presetId)
end

---@param schemeId: 自定义方案id
function SkillListComponent:ReqSwitchSkillScheme(schemeId)
    self.remote.ReqSwitchSkillScheme(schemeId)
end

---@param result: 错误吗
---@param schemeId: 自定义方案id
function SkillListComponent:RetSwitchSkillScheme(result, schemeId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_SCHEME_SWITCH)
    end
    self:Debug("RetSwitchSkillScheme", self.eid, result.Code, schemeId)
end

---@param schemeId: 自定义方案id
---@param newName: 自定义方案名字
function SkillListComponent:ReqChangeSkillSchemeName(schemeId, newName)
    self.remote.ReqChangeSkillSchemeName(schemeId, newName)
end

---@param result: 错误吗
---@param schemeId: 自定义方案id
function SkillListComponent:RetChangeSkillSchemeName(result, schemeId)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SKILL_MANAGE_PLAN_RENAME)
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_SCHEME_RENAME, schemeId)
    elseif result.Code == Game.ErrorCodeConst.SKILL_SCHEME_NAME_IS_SENSITIVE or
           result.Code == Game.ErrorCodeConst.SKILL_SCHEME_NAME_SENSITIVE_CHECK_ERR then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHECK_STRING_DIRTY_FAILED)
		Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SKILL_SCHEME_NAME_CHANGE_FAILED, schemeId)
    end
    self:Debug("RetChangeSkillSchemeName", self.eid, result.Code, schemeId)
end

---@param skillId: 技能ID
---@param unlocked: 是否解锁(0, 锁定; 1, 解锁)
---@param skillLvl: 技能Lvl
function SkillListComponent:OnMsgSkillUnlock(skillId, unlocked, skillLvl)
    self:Debug("OnMsgSkillUnlock", self.eid, skillId, unlocked, skillLvl)
end

-- region Sync
UnlockedSkillDict = DefineClass("UnlockedSkillDict")

function UnlockedSkillDict:set_attr(entity, key, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.SKILLHUD_UNLOCK_SKILL_lIST_CHANGE)
end


UnlockedSkillEntryInfo = DefineClass("UnlockedSkillEntryInfo")

function UnlockedSkillEntryInfo:set_attr(entity, key, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.SKILLHUD_UNLOCK_SKILL_lIST_CHANGE)
end

-- 角色的解锁技能列表属性同步回调
function SkillListComponent:set_unlockedSkillList(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.SKILLHUD_UNLOCK_SKILL_lIST_CHANGE)
end

ActorSkillDict = DefineClass("ActorSkillDict")
function ActorSkillDict:set_attr(entity, key, new, old)
	entity:RefreshSkillSlotToID()
	Game.GlobalEventSystem:Publish(EEventTypesV2.SKILL_POST_SKILL_EQUIP_CHANGED)
	entity:onSkillEquipChanged()
end

ActorSkillEntryInfo = DefineClass("ActorSkillEntryInfo")
function ActorSkillEntryInfo:set_attr(entity, key, new, old)
	entity:RefreshSkillSlotToID()
	Game.GlobalEventSystem:Publish(EEventTypesV2.SKILL_POST_SKILL_EQUIP_CHANGED)
	entity:onSkillEquipChanged()
end

-- 当前技能列表属性同步回调
function SkillListComponent:set_skillList(entity, new, old)
	self:RefreshSkillSlotToID()
	Game.GlobalEventSystem:Publish(EEventTypesV2.SKILL_POST_SKILL_EQUIP_CHANGED)
	entity:onSkillEquipChanged()
end

function SkillListComponent:set_skillWheelID(entity, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.SERVER_ON_SKILL_WHEEL_CHANGED, new)
end

-- endregion Sync

return SkillListComponent
