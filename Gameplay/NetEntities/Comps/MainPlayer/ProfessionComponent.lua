local UKismetMaterialLib = import("KismetMaterialLibrary")

-- local NSDT = Game.TableData.GetSkillDataNewTable()
local Const = kg_require("Shared.Const")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

ProfessionComponent = DefineComponent("ProfessionComponent")

kg_require("Gameplay.NetEntities.Comps.MainPlayer.ProfessionComponent_Fool")

-- 各职业机制初始化接口
ProfessionComponent.ProfessionInitFunc = {
	[Const.ProfessionType.Fool] = "initForFool",
}

-- 各职业反初始化接口
ProfessionComponent.ProfessionUninitFunc = {
	[Const.ProfessionType.Fool] = "unInitForFool",
}

function ProfessionComponent:set_ProfessionProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_PROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_ProfessionProp2(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_PROP_2_CHANGED, new, old)
end

function ProfessionComponent:set_ProfessionProp3(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_PROP_3_CHANGED, new, old)
end

function ProfessionComponent:set_SunProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_SUNPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_UtoProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_UTOPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_ArbProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_ARBPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_ApprProp3(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_APPRPROP_3_CHANGED, new, old)
end

function ProfessionComponent:set_WarProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_WARPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_FoolProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_FOOLPROP_1_CHANGED, new, old) 
end

function ProfessionComponent:set_ProfessionQLock(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.SKILLHUD_PROFESSION_Q_LOCK_CHANGE, new, old) 
end


-- region Important
function ProfessionComponent:ctor()
    self.ProfessionCompActive = false
    self.InitLowHP = false
    self.MID_LowHpWarn = nil
    self.PP_REF = nil
    self.LowHpState = nil
    self.LowHPCameraModifierID = nil

    self.PrimeLockRelation = Enum.ECampEnumData.Enemy

    self.LV_Direction = M3D.Vec3()
    self.LV_Owner = M3D.Vec3()
    self.LV_Target = M3D.Vec3()

    -- 观众职业
    self.AutoSkillTimer = nil
    self.WhiteWingUID = nil
    self.BlackWingUID = nil
end

function ProfessionComponent:dtor()
    if (self.ProfessionCompActive == true) then
        self:ActiveProfessionComponent(false)
    end
    self.ProfessionCompActive = false

    self.MID_LowHpWarn = nil
    self.PP_REF = nil
    self.LowHpState = nil
    self.LowHPCameraModifierID = nil

    -- Game.EventSystem:RemoveObjListeners(self)
    self:CancelAutoSkillTimer()
end

function ProfessionComponent:ActiveProfessionComponent(bActive)
    if bActive then
        self:InitForOnPossess()
    else
        self:UnInitForOnUnPossess()
    end
    self.ProfessionCompActive = bActive
    self:HandleCallback(bActive)
end

-- endregion Important



-- region Init
function ProfessionComponent:InitForOnPossess()
    self:LowHpWarnInit()

    --装配该职业的技能列表
    if (self.skillList ~= nil) then
        local NewList = {}
        for k, v in pairs(self.skillList) do
            if (v.SkillSlot >= 0) then
                if (NewList[v.SkillSlot] == nil) then
                    NewList[v.SkillSlot] = v.SkillID
                end
            end
        end

        -- 初始技能表的添加请求者为技能组件自身
        self:AddSkillList(NewList, self:uid())
    end

    -- 最后执行
    Game.GlobalEventSystem:Publish(EEventTypesV2.ROLE_ON_SKILL_READY)
	self:initByProfession()
end

function ProfessionComponent:UnInitForOnUnPossess()
    self:LowHpWarnUnInit()
	self:unInitByProfession()
end
-- endregion Init


function ProfessionComponent:initByProfession()
	local initFunc = ProfessionComponent.ProfessionInitFunc[self.Profession]
	if initFunc ~= nil then
		self[initFunc](self)
	end
end

function ProfessionComponent:unInitByProfession()
	local unInitFunc = ProfessionComponent.ProfessionUninitFunc[self.Profession]
	if unInitFunc ~= nil then
		self[unInitFunc](self)
	end
end


-- region Revive
-- 查询复活技能ID
-- @return int32 技能ID
function ProfessionComponent:GetReviveSkillID()
    if (self.GetSkillIDBySlotNew == nil) then
        return -1
    end

    return self:GetSkillIDBySlotNew(ETE.EBSSkillSlot.SS_ReviveSlot)
end

-- 激活复活技能
-- @param InTarget SceneComponent 复活目标
function ProfessionComponent:TryActivateReviveSkill(uid)
	local TEntity = Game.EntityManager:getEntity(uid)
	if TEntity then
		if (TEntity.IsDead == false) or (TEntity.ObserverType > 0) or (self.BattleZoneID ~= TEntity.BattleZoneID) then
			return
		end
		-- 索敌目标切换为复活目标
		self:ChangeLockTargetByUID(ETE.EBSTargetType.TT_Skill, uid)
	end

    -- 激活复活技能
	Game.me.remote:ReqCastSkillNew(self:GetReviveSkillID(), uid, nil, nil, 0)
end
-- endregion Revive






-- region InputProcessor
function ProfessionComponent:CreateReleaseSkillData(skillId, inputDir)
    local DataBuffer = {Transform = M3D.Transform()}

    -- 角色当前位置
    local Pos = DataBuffer.Transform.Translation
    Pos:Pack(self:GetPosition_P())
    
    -- 角色输入方向
    if (inputDir ~= nil) then
        M3D.ToVec3(inputDir, self.LV_Direction)
    else
        self.LV_Direction:Reset()
    end

    -- 保证数据合法
    if (self.LV_Direction:IsValid() == false) or (self.LV_Direction:IsNearlyZero() == true) then
        local rootCompID = self.CppEntity:KAPI_Actor_GetRootComponent()
        local forward = self.CppEntity:KAPI_SceneID_GetForwardVector(rootCompID)
        self.LV_Direction:Pack(forward.X, forward.Y, forward.Z)
    end

    -- 构造技能释放数据
    self.LV_Direction:Mul(100000.0, self.LV_Direction)
    Pos:Add(self.LV_Direction, Pos)

    return DataBuffer
end

function ProfessionComponent:SetPrimeLockRelation(InPrimeLockRelation)
    self.PrimeLockRelation = InPrimeLockRelation
end
-- endregion InputProcessor


---@todo 配表，暂时先写死，之前是直接写到BSSettings里了，有点抽象
-- region LowHpWarn
function ProfessionComponent:LowHpWarnInit()
    self:DoAsyncLoadAsset(ViewResourceConst.M_PP_LOWLIFE_QUICK, "OnLowHpWarnMILoaded")
end

function ProfessionComponent:OnLowHpWarnMILoaded(loadID, MI_ConstantID)
	if MI_ConstantID == 0 then
		return
	end
	
    local CMClass = import("CEPPMaterial")--Game.ResourceManager:GetClassByPath("/Script/KGCHARACTER.CEPPMaterial")
    if (CMClass == nil) then
        return
    end
	
	local MI_Constant = Game.ObjectActorManager:GetObjectByID(MI_ConstantID)
    self.MID_LowHpWarn = UKismetMaterialLib.CreateDynamicMaterialInstance(_G.GetContextObject(), MI_Constant, "", 0)
    if (self.MID_LowHpWarn == nil) or (slua.isValid(self.MID_LowHpWarn) == false) then
        return
    end

    if (self.InitLowHP == true) then
        return
    end
	
    self.InitLowHP = true

    self.LowHpState = 0 -- 0:无，1:Slow, 2:Quick

    local CameraManager = Game.CameraManager
    local LowHPCameraModifierID = CameraManager:KAPI_Camera_AddNewCameraModifier(CMClass)
    if LowHPCameraModifierID and LowHPCameraModifierID ~= KG_INVALID_ID then
        CameraManager:KAPI_Camera_CameraEffect_SetAutoRemoveFromList(LowHPCameraModifierID, false)
        CameraManager:KAPI_Camera_CameraEffect_SetAlphaInTime(LowHPCameraModifierID, 0.2)
        CameraManager:KAPI_Camera_CameraEffect_SetAlphaOutTime(LowHPCameraModifierID, 0.5)
        CameraManager:KAPI_Camera_CameraEffect_DisableModifier(LowHPCameraModifierID, true)
        CameraManager:KAPI_Camera_CameraEffect_DisableModifier(LowHPCameraModifierID, true)
        CameraManager:KAPI_Camera_PPMaterial_SetBlendableWeight(LowHPCameraModifierID, 1.0)
        CameraManager:KAPI_Camera_PPMaterial_SetBlendableObject(LowHPCameraModifierID, self.MID_LowHpWarn)
    end
    
    self.LowHPCameraModifierID = LowHPCameraModifierID
    Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.ON_HP_CHANGED, self, "OnHpChanged", self.eid)
end

function ProfessionComponent:OnHpChanged(Entity, PropName, OldValue)
	if not self.InitLowHP then
		return
	end
    local NewValue = Entity.Hp
    local tMaxHp = Entity.MaxHp
    local LowHPCameraModifierID = self.LowHPCameraModifierID
    if NewValue / tMaxHp > 0.3 or NewValue / tMaxHp <= 0.0 then
        -- 死亡/超过30%血量，不激活
        if self.LowHpState ~= 0 then
            self.LowHpState = 0
            if LowHPCameraModifierID then
                Game.CameraManager:KAPI_Camera_DisableModifier(LowHPCameraModifierID, false)
            end
        end
    elseif NewValue / tMaxHp > 0.15 then
        -- 15%以上血量激活慢速
        if self.LowHpState ~= 1 then
            self.LowHpState = 1
            self.MID_LowHpWarn:SetScalarParameterValue("Speed", 0.5)
            if LowHPCameraModifierID then
                Game.CameraManager:KAPI_Camera_EnableModifier(LowHPCameraModifierID)
            end
        end
    else
        if self.LowHpState ~= 2 then
            -- 15%以下血量激活慢速
            self.LowHpState = 2
            self.MID_LowHpWarn:SetScalarParameterValue("Speed", 1.0)
            if LowHPCameraModifierID then
                Game.CameraManager:KAPI_Camera_EnableModifier(LowHPCameraModifierID)
            end
        end
    end
end

function ProfessionComponent:LowHpWarnUnInit()
    self.InitLowHP = false
    Game.EventSystem:RemoveListenerForUniqueID(_G.EEventTypes.ON_HP_CHANGED, self, "OnHpChanged", self.eid)

    if Game.CameraManager and self.LowHPCameraModifierID then
        Game.CameraManager:KAPI_Camera_RemoveCameraModifier(self.LowHPCameraModifierID)
    end
    self.LowHPCameraModifierID = nil
    self.PP_REF = nil
    self.MID_LowHpWarn = nil
end
-- endregion LowHpWarn


-- region 观众职业特有逻辑

-- region Core

function ProfessionComponent:HandleCallback(IsAddCallback)
    if self.Profession ~= Const.PROFESSION_TYPEUTOPIAN then
        return
    end
    if IsAddCallback then

        if self.OnAddBuffRecord ~= nil then
            self.OnAddBuffRecord:Add(self, "OnBuffChanged")
        end
    
        -- 临时修复，后续应该保证 ActivateBattleLogic 不会重入
        Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.SKILL_POST_SKILL_EQUIP_CHANGED, self, self.OnRoleSkillReadyInAutoSkill)
        Game.EventSystem:AddListener(_G.EEventTypes.SKILL_POST_SKILL_EQUIP_CHANGED, self, self.OnRoleSkillReadyInAutoSkill)
    else

        if self.OnAddBuffRecord ~= nil then
            self.OnAddBuffRecord:RemoveObject(self)
        end
    
        Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.SKILL_POST_SKILL_EQUIP_CHANGED, self, self.OnRoleSkillReadyInAutoSkill)
    end

end

function ProfessionComponent:CancelAutoSkillTimer()
    if (self.AutoSkillTimer ~= nil) then
        Game.TimerManager:StopTimerAndKill(self.AutoSkillTimer)
        self.AutoSkillTimer = nil
    end
end

function ProfessionComponent:OnRoleSkillReadyInAutoSkill()
    -- 玩家装配了新技能，可能是主动行为，也可能是触发了职业机制，这边目前只监听触发的职业机制
    -- 空想家
    local SkillSlot = ETE.EBSSkillSlot.SS_Slot06
    local tSkillData = Game.TableData.GetSkillDataNewRow(self:GetSkillIDBySlotNew(SkillSlot))
    if (tSkillData == nil) then
        self:CancelAutoSkillTimer()
        return
    end

    local SkillID = tSkillData.SkillID
    if (SkillID ~= Const.UTOPIAN_CUSTOMDATA.WHITE_FEATHER_SKILL_ID) then
        -- 黑羽毛
        self:CancelAutoSkillTimer()
        return
    end

    local bInBattle = self.InBattle
    if (bInBattle == true) then
        --Game.EventSystem:AddListenerForUniqueID(EEventTypes.ON_SKILL_STARTED, self, self.CheckWhiteFeatherSkill, self:uid())
        self:TryReleaseSkillInAutoSkill(SkillID)
    else
        -- do nothing
    end
end

function ProfessionComponent:TryReleaseSkillInAutoSkill(InSkillID)
    -- 关闭旧Timer
    self:CancelAutoSkillTimer()

    -- 检查技能释放条件
    local bInBattle = self.InBattle
    if (bInBattle == true) then
        local NextReleaseTime = 0.4 -- 保底最小tick 0.4s
        -- 进入战斗且能够施放该技能，则立即施放
        if (self:CanReleaseSkill(InSkillID) == true) then
            self:SimpleReleaseSkill(InSkillID, nil, true)
        -- 进入战斗但不能施放该技能，则等待下一次技能释放
        else
            local CDMsg = self:GetSkillCoolDownMessage(InSkillID)
            local CurrentCoolDown = CDMsg.CurrentCoolDown
            NextReleaseTime = math.max(CurrentCoolDown, NextReleaseTime)
        end

        self.AutoSkillTimer = Game.TimerManager:CreateTimerAndStart(
            function()
                self:TryReleaseSkillInAutoSkill(InSkillID)
            end,
        NextReleaseTime, 1)
    end
end

function ProfessionComponent:CheckWhiteFeatherSkill(InSkillInst)
    if (InSkillInst.StaticData ~= nil) and (InSkillInst.StaticData.ID == Const.UTOPIAN_CUSTOMDATA.WHITE_FEATHER_SKILL_ID) then
        self:CancelAutoSkillTimer()
        --Game.EventSystem:RemoveListenerForUniqueID(EEventTypes.ON_SKILL_STARTED, self, self.CheckWhiteFeatherSkill, self:uid())
    end
end
-- endregion Core



-- region Callback
function ProfessionComponent:OnBuffChanged(RecordType, BuffID, ExtraData)

    if (RecordType == ETE.EBSABuffRecord.AddBuff) or (RecordType == ETE.EBSABuffRecord.RebuildBuff) then
        if (BuffID == Const.UTOPIAN_CUSTOMDATA.WHITE_WING_BUFF_ID) then
            self.WhiteWingUID = self:AddProfessionAttach(Const.UTOPIAN_CUSTOMDATA.WHITE_WING_ITEM_ID)
        elseif (BuffID == Const.UTOPIAN_CUSTOMDATA.BLACK_WING_BUFF_ID) then
            self.BlackWingUID = self:AddProfessionAttach(Const.UTOPIAN_CUSTOMDATA.BLACK_WING_ITEM_ID)
        end
    elseif (RecordType == ETE.EBSABuffRecord.RemoveBuff) then
        if (BuffID == Const.UTOPIAN_CUSTOMDATA.WHITE_WING_BUFF_ID) then
            self:ClearProfessionAttach(self.WhiteWingUID)
        elseif (BuffID == Const.UTOPIAN_CUSTOMDATA.BLACK_WING_BUFF_ID) then
            self:ClearProfessionAttach(self.BlackWingUID)
        end
    end
end
-- endregion Callback


-- endregion 观众职业特有逻辑


return ProfessionComponent
