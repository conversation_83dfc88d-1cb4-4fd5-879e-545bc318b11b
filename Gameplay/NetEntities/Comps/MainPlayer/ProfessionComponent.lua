local UKismetMaterialLib = import("KismetMaterialLibrary")

-- local NSDT = Game.TableData.GetSkillDataNewTable()
local Const = kg_require("Shared.Const")
local ViewResourceConst = kg_require("Gameplay.CommonDefines.ViewResourceConst")

ProfessionComponent = DefineComponent("ProfessionComponent")

kg_require("Gameplay.NetEntities.Comps.MainPlayer.ProfessionComponent_Fool")

-- 各职业机制初始化接口
ProfessionComponent.ProfessionInitFunc = {
	[Const.ProfessionType.Fool] = "initForFool",
}

-- 各职业反初始化接口
ProfessionComponent.ProfessionUninitFunc = {
	[Const.ProfessionType.Fool] = "unInitForFool",
}

function ProfessionComponent:set_ProfessionProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_PROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_ProfessionProp2(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_PROP_2_CHANGED, new, old)
end

function ProfessionComponent:set_ProfessionProp3(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_PROP_3_CHANGED, new, old)
end

function ProfessionComponent:set_SunProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_SUNPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_UtoProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_UTOPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_ArbProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_ARBPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_ApprProp3(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_APPRPROP_3_CHANGED, new, old)
end

function ProfessionComponent:set_WarProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_WARPROP_1_CHANGED, new, old)
end

function ProfessionComponent:set_FoolProp1(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_PROFESSION_FOOLPROP_1_CHANGED, new, old) 
end

function ProfessionComponent:set_ProfessionQLock(ent, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.SKILLHUD_PROFESSION_Q_LOCK_CHANGE, new, old) 
end

function ProfessionComponent:ctor()
    self.ProfessionCompActive = false
    self.InitLowHP = false
    self.MID_LowHpWarn = nil
    self.PP_REF = nil
    self.LowHpState = nil
    self.LowHPCameraModifierID = nil

    self.PrimeLockRelation = Enum.ECampEnumData.Enemy

    self.LV_Direction = M3D.Vec3()
    self.LV_Owner = M3D.Vec3()
    self.LV_Target = M3D.Vec3()

    -- 观众职业
    self.AutoSkillTimer = nil
    self.WhiteWingUID = nil
    self.BlackWingUID = nil
end

function ProfessionComponent:dtor()
    self.ProfessionCompActive = false

    self.MID_LowHpWarn = nil
    self.PP_REF = nil
    self.LowHpState = nil
    self.LowHPCameraModifierID = nil
end

function ProfessionComponent:__component_EnterWorld__()
	self:LowHpWarnInit()
	self:initByProfession()
end

function ProfessionComponent:__component_ExitWorld__()
	self:LowHpWarnUnInit()
	self:unInitByProfession()
end

function ProfessionComponent:initByProfession()
	local initFunc = ProfessionComponent.ProfessionInitFunc[self.Profession]
	if initFunc ~= nil then
		self[initFunc](self)
	end
end

function ProfessionComponent:unInitByProfession()
	local unInitFunc = ProfessionComponent.ProfessionUninitFunc[self.Profession]
	if unInitFunc ~= nil then
		self[unInitFunc](self)
	end
end


-- region Revive
-- 查询复活技能ID
-- @return int32 技能ID
function ProfessionComponent:GetReviveSkillID()
    if (self.GetSkillIDBySlotNew == nil) then
        return -1
    end

    return self:GetSkillIDBySlotNew(ETE.EBSSkillSlot.SS_ReviveSlot)
end

-- 激活复活技能
-- @param InTarget SceneComponent 复活目标
function ProfessionComponent:TryActivateReviveSkill(uid)
	local TEntity = Game.EntityManager:getEntity(uid)
	if TEntity then
		if (TEntity.IsDead == false) or (TEntity.ObserverType > 0) or (self.BattleZoneID ~= TEntity.BattleZoneID) then
			return
		end
		-- 索敌目标切换为复活目标
		self:ChangeLockTargetByUID(ETE.EBSTargetType.TT_Skill, uid)
	end

    -- 激活复活技能
	Game.me.remote:ReqCastSkillNew(self:GetReviveSkillID(), uid, nil, nil, 0)
end
-- endregion Revive






-- region InputProcessor
function ProfessionComponent:CreateReleaseSkillData(skillId, inputDir)
    local DataBuffer = {Transform = M3D.Transform()}

    -- 角色当前位置
    local Pos = DataBuffer.Transform.Translation
    Pos:Pack(self:GetPosition_P())
    
    -- 角色输入方向
    if (inputDir ~= nil) then
        M3D.ToVec3(inputDir, self.LV_Direction)
    else
        self.LV_Direction:Reset()
    end

    -- 保证数据合法
    if (self.LV_Direction:IsValid() == false) or (self.LV_Direction:IsNearlyZero() == true) then
        local rootCompID = self.CppEntity:KAPI_Actor_GetRootComponent()
        local forward = self.CppEntity:KAPI_SceneID_GetForwardVector(rootCompID)
        self.LV_Direction:Pack(forward.X, forward.Y, forward.Z)
    end

    -- 构造技能释放数据
    self.LV_Direction:Mul(100000.0, self.LV_Direction)
    Pos:Add(self.LV_Direction, Pos)

    return DataBuffer
end
-- endregion InputProcessor


---@todo 配表，暂时先写死，之前是直接写到BSSettings里了，有点抽象
-- region LowHpWarn
function ProfessionComponent:LowHpWarnInit()
    self:DoAsyncLoadAsset(ViewResourceConst.M_PP_LOWLIFE_QUICK, "OnLowHpWarnMILoaded")
end

function ProfessionComponent:OnLowHpWarnMILoaded(loadID, MI_ConstantID)
	if MI_ConstantID == 0 then
		return
	end
	
    local CMClass = import("CEPPMaterial")--Game.ResourceManager:GetClassByPath("/Script/KGCHARACTER.CEPPMaterial")
    if (CMClass == nil) then
        return
    end
	
	local MI_Constant = Game.ObjectActorManager:GetObjectByID(MI_ConstantID)
    self.MID_LowHpWarn = UKismetMaterialLib.CreateDynamicMaterialInstance(_G.GetContextObject(), MI_Constant, "", 0)
    if (self.MID_LowHpWarn == nil) or (slua.isValid(self.MID_LowHpWarn) == false) then
        return
    end

    if (self.InitLowHP == true) then
        return
    end
	
    self.InitLowHP = true

    self.LowHpState = 0 -- 0:无，1:Slow, 2:Quick

    local CameraManager = Game.CameraManager
    local LowHPCameraModifierID = CameraManager:KAPI_Camera_AddNewCameraModifier(CMClass)
    if LowHPCameraModifierID and LowHPCameraModifierID ~= KG_INVALID_ID then
        CameraManager:KAPI_Camera_CameraEffect_SetAutoRemoveFromList(LowHPCameraModifierID, false)
        CameraManager:KAPI_Camera_CameraEffect_SetAlphaInTime(LowHPCameraModifierID, 0.2)
        CameraManager:KAPI_Camera_CameraEffect_SetAlphaOutTime(LowHPCameraModifierID, 0.5)
        CameraManager:KAPI_Camera_CameraEffect_DisableModifier(LowHPCameraModifierID, true)
        CameraManager:KAPI_Camera_CameraEffect_DisableModifier(LowHPCameraModifierID, true)
        CameraManager:KAPI_Camera_PPMaterial_SetBlendableWeight(LowHPCameraModifierID, 1.0)
        CameraManager:KAPI_Camera_PPMaterial_SetBlendableObject(LowHPCameraModifierID, self.MID_LowHpWarn)
    end
    
    self.LowHPCameraModifierID = LowHPCameraModifierID
    Game.EventSystem:AddListenerForUniqueID(_G.EEventTypes.ON_HP_CHANGED, self, "OnHpChanged", self.eid)
end

function ProfessionComponent:OnHpChanged(Entity, PropName, OldValue)
	if not self.InitLowHP then
		return
	end
    local NewValue = Entity.Hp
    local tMaxHp = Entity.MaxHp
    local LowHPCameraModifierID = self.LowHPCameraModifierID
    if NewValue / tMaxHp > 0.3 or NewValue / tMaxHp <= 0.0 then
        -- 死亡/超过30%血量，不激活
        if self.LowHpState ~= 0 then
            self.LowHpState = 0
            if LowHPCameraModifierID then
                Game.CameraManager:KAPI_Camera_DisableModifier(LowHPCameraModifierID, false)
            end
        end
    elseif NewValue / tMaxHp > 0.15 then
        -- 15%以上血量激活慢速
        if self.LowHpState ~= 1 then
            self.LowHpState = 1
            self.MID_LowHpWarn:SetScalarParameterValue("Speed", 0.5)
            if LowHPCameraModifierID then
                Game.CameraManager:KAPI_Camera_EnableModifier(LowHPCameraModifierID)
            end
        end
    else
        if self.LowHpState ~= 2 then
            -- 15%以下血量激活慢速
            self.LowHpState = 2
            self.MID_LowHpWarn:SetScalarParameterValue("Speed", 1.0)
            if LowHPCameraModifierID then
                Game.CameraManager:KAPI_Camera_EnableModifier(LowHPCameraModifierID)
            end
        end
    end
end

function ProfessionComponent:LowHpWarnUnInit()
    self.InitLowHP = false
    Game.EventSystem:RemoveListenerForUniqueID(_G.EEventTypes.ON_HP_CHANGED, self, "OnHpChanged", self.eid)

    if Game.CameraManager and self.LowHPCameraModifierID then
        Game.CameraManager:KAPI_Camera_RemoveCameraModifier(self.LowHPCameraModifierID)
    end
    self.LowHPCameraModifierID = nil
    self.PP_REF = nil
    self.MID_LowHpWarn = nil
end
-- endregion LowHpWarn


return ProfessionComponent
