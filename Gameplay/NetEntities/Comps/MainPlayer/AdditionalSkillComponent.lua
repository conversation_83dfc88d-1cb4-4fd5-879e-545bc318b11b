local KismetMathLibrary = import("KismetMathLibrary")
-- local ActorUtil = import("KGActorUtil")
local ULLFunc = import("LowLevelFunctions")

-- 扩展的客户端技能, 卜杖寻路玩法, 仅挂在MainPlayer上
AdditionalSkillComponent = DefineComponent("AdditionalSkillComponent")


--region Important
function AdditionalSkillComponent:ctor()
    -- InitFromExcel
    self.Dowsing01 = Enum.EAdditionalSkillConstData.Dowsing01_Path
    self.Dowsing01Param = {
        tonumber(Enum.EAdditionalSkillConstData.Dowsing01_Time),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing01_XBIAS),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing01_YBIAS),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing01_ZBIAS),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing01_YAWBIAS),
    }

    self.Dowsing02 = Enum.EAdditionalSkillConstData.Dowsing02_Path
    self.Dowsing02Param = {
        tonumber(Enum.EAdditionalSkillConstData.Dowsing02_Time),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing02_XBIAS),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing02_YBIAS),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing02_ZBIAS),
        tonumber(Enum.EAdditionalSkillConstData.Dowsing02_YAWBIAS),
    }

	self.Dowsing03 = Enum.EAdditionalSkillConstData.Dowsing03_Path
	self.Dowsing03Param = {
		tonumber(Enum.EAdditionalSkillConstData.Dowsing03_Time),
		tonumber(Enum.EAdditionalSkillConstData.Dowsing03_XBIAS),
		tonumber(Enum.EAdditionalSkillConstData.Dowsing03_YBIAS),
		tonumber(Enum.EAdditionalSkillConstData.Dowsing03_ZBIAS),
		tonumber(Enum.EAdditionalSkillConstData.Dowsing03_YAWBIAS),
	}

    self.DowsingLine = Enum.EAdditionalSkillConstData.DowsingLine_Path
    self.DowsingLineParam = {
        tonumber(Enum.EAdditionalSkillConstData.DowsingLine_Time),
    }

    self.DowsingTattoo = Enum.EAdditionalSkillConstData.DowsingTattoo_Path
    self.DowsingTattooParam = {
        tonumber(Enum.EAdditionalSkillConstData.DowsingTattoo_Time),
        tonumber(Enum.EAdditionalSkillConstData.DowsingTattoo_XBIAS),
        tonumber(Enum.EAdditionalSkillConstData.DowsingTattoo_YBIAS),
        tonumber(Enum.EAdditionalSkillConstData.DowsingTattoo_ZBIAS),
        tonumber(Enum.EAdditionalSkillConstData.DowsingTattoo_YAWBIAS),
    }

    self.DowsingLineRefresh = math.max(tonumber(Enum.EAdditionalSkillConstData.DowsingLineRefresh), 0.5)
    self.DowsingLineRefresh = self.DowsingLineRefresh * 1000
    self.DowsingLineRefreshNum = math.min(tonumber(Enum.EAdditionalSkillConstData.DowsingLineRefreshNum), 5)
	self.DowsingLineRefreshDistance = Enum.EAdditionalSkillConstData.DowsingLineRefreshDistance
	self.DowsingLineRefreshRadius = Enum.EAdditionalSkillConstData.DowsingLineRefreshRadius
    self.StickNavigatorAnimLib = "Cane_Common"
    self.DowsingLineAk = Enum.EAdditionalSkillConstData.DowsingLineAk

	self.StartSkillAnimFeatureID = nil
    self.AdditionalSkillAnimTimer1 = nil
    self.AdditionalSkillAnimTimer2 = nil
	self.AdditionalSkillAnimTimer3 = nil
    self.AdditionalSkillAnimTimer4 = nil
    self.AdditionalSkillAnimTimer5 = nil
    self.AdditionalSkillRefreshTimer = nil

	self.AdditionalSkillType = nil
    self.AdditionalSkillParam = nil
	self.AdditionalSkillID = 0
	self.AdditionalSkillSlot = nil
	self.OnceAssetHandle = {}
    self.RefreshAssetHandle = {}
    self.bLockLocoInAdditionalSkill = false

    self.AcceptanceRadiusInAdditionalSkill = tonumber(Enum.EAdditionalSkillConstData.DowsingLineFinishRadius) or 200
end

function AdditionalSkillComponent:dtor()

end

-- function AdditionalSkillComponent:__component_EnterWorld__()
--     self.OnReceiveDamage:Add(self, "OnReceiveDamageInAdditionalSkill")
-- end

function AdditionalSkillComponent:__component_ExitWorld__()
    self:exitAdditionalSkill()
    -- self.OnReceiveDamage:RemoveObject(self)
end

function AdditionalSkillComponent:__component_MoveInput__()
    -- 有移动输入
end

-- 收到伤害主动退出
function AdditionalSkillComponent:OnReceiveDamageInAdditionalSkill()
    self:exitAdditionalSkill()
end

function AdditionalSkillComponent:clearAdditionalSkill()
	self:ClearAdditionalSkillAnimTimer()
	for _, EID in pairs(self.OnceAssetHandle) do
		self:DeactivateNiagaraSystem(EID)
	end
	for _, EID in pairs(self.RefreshAssetHandle) do
		self:DeactivateNiagaraSystem(EID)
	end
end

function AdditionalSkillComponent:exitAdditionalSkill(bIsSuccess)
    self:clearAdditionalSkill()
    local uid
    if self.AdditionalSkillParam then
        uid = self.AdditionalSkillParam.UID
        self.AdditionalSkillParam = nil
    end
    if bIsSuccess then
	    Game.EventSystem:Publish(_G.EEventTypes.On_ADDITION_SKILL_FINISH, uid)
    else
        Game.EventSystem:Publish(_G.EEventTypes.On_ADDITION_SKILL_BREAK, uid)
    end
	self:RemoveAdditionalSkill()
	self:DebugFmt("ExitAdditionalSkill : %s", uid)
end

function AdditionalSkillComponent:ClearAdditionalSkillAnimTimer()
    if self.StartSkillAnimFeatureID ~= nil then
        self:StopAnimLibMontage(self.StartSkillAnimFeatureID)
        self:lockLocoInAdditionalSkill(false)
    end
    if self.AdditionalSkillAnimTimer1 ~= nil then
        Game.TimerManager:StopTimerAndKill(self.AdditionalSkillAnimTimer1)
        self.AdditionalSkillAnimTimer1 = nil
    end
    if self.AdditionalSkillAnimTimer2 ~= nil then
        Game.TimerManager:StopTimerAndKill(self.AdditionalSkillAnimTimer2)
        self.AdditionalSkillAnimTimer2 = nil
    end
    if self.AdditionalSkillAnimTimer3 ~= nil then
        Game.TimerManager:StopTimerAndKill(self.AdditionalSkillAnimTimer3)
        self.AdditionalSkillAnimTimer3 = nil
    end
    if self.AdditionalSkillAnimTimer4 ~= nil then
        Game.TimerManager:StopTimerAndKill(self.AdditionalSkillAnimTimer4)
        self.AdditionalSkillAnimTimer4 = nil
    end
    if self.AdditionalSkillAnimTimer5 ~= nil then
        Game.TimerManager:StopTimerAndKill(self.AdditionalSkillAnimTimer5)
        self.AdditionalSkillAnimTimer5 = nil
    end
    if self.AdditionalSkillRefreshTimer ~= nil then
        Game.TimerManager:StopTimerAndKill(self.AdditionalSkillRefreshTimer)
        self.AdditionalSkillRefreshTimer = nil
    end
end
--endregion Important



--region API

function AdditionalSkillComponent:AddAdditionalSkill(InSkillType, InParams)
    -- 在Boss机制的slot上增加技能icon
	self.AdditionalSkillType = InSkillType
	self.AdditionalSkillParam = InParams
    local uid = ULLFunc.GetGlobalUniqueID()
    self.AdditionalSkillParam.UID = uid
	self.bAdditionalSkillInCD = false
	if InSkillType == Enum.EAdditionalSkillType.StickNavigator then
		self.AdditionalSkillID = 80000003
		self.AdditionalSkillSlot = ETE.EBSSkillSlot.SS_Slot11
	end

    Game.EventSystem:Publish(_G.EEventTypes.On_ADDITION_SKILL_ADD, InSkillType, self.AdditionalSkillID, self.AdditionalSkillSlot, self.AdditionalSkillParam)
	self:DebugFmt("AddAdditionalSkill : %s", uid)
    return uid
end

function AdditionalSkillComponent:RemoveAdditionalSkill()
	if self.AdditionalSkillParam then
		self.AdditionalSkillParam = nil
		self.AdditionalSkillID = 0
		self.AdditionalSkillSlot = nil
		self.bAdditionalSkillInCD = false
		-- 在Boss机制的slot上移除技能icon
		Game.EventSystem:Publish(_G.EEventTypes.On_ADDITION_SKILL_DEL)
	end
end

function AdditionalSkillComponent:StartAdditionalSkill(InSkillType, params)
    if not self.bInWorld then
        return
    end
	self:clearAdditionalSkill()

    self.AdditionalSkillParam = params

    if InSkillType == Enum.EAdditionalSkillType.StickNavigator then
        self.StartSkillAnimFeatureID = self:PlayAnimLibMontage(self.StickNavigatorAnimLib, nil, nil, nil, nil, true, self, "OnStickNavigatorAnimStop")
		if self.StartSkillAnimFeatureID then
			self:lockLocoInAdditionalSkill(true)
		end
        self:afterAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator)
		self.bAdditionalSkillInCD = true
        Game.EventSystem:Publish(_G.EEventTypes.On_ADDITION_SKILL_CD_BEGIN) 
    end

	self:DebugFmt("StartAdditionalSkill : %s", self.AdditionalSkillParam and self.AdditionalSkillParam.UID or nil)
end

function AdditionalSkillComponent:OnStickNavigatorAnimStop(bInterrupt)
	self:lockLocoInAdditionalSkill(false)
	self.StartSkillAnimFeatureID = nil
end

--endregion API

--region SystemAction

function AdditionalSkillComponent:OnMsgSceneActorPosQueryResult(InstanceID, Pos, bAdd)
    if not self.bInWorld then
        return
    end
    local FPos = FVector(Pos.x, Pos.y, Pos.z)
    if bAdd == 1 then
        self:AddAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, {
            ["InstanceID"] = InstanceID,
            ["Pos"] = FPos,
        })
    else
        self:StartAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, {
            ["InstanceID"] = InstanceID,
            ["Pos"] = FPos,
        })
    end
end

function AdditionalSkillComponent:OnMsgSkillFixPos(MapID, Pos, bAdd)
    if not self.bInWorld then
        return
    end
    local CurMapID = Game.GameLoopManagerV2:GetCurMapDataID()
    if MapID ~= CurMapID then
        return
    end

    if bAdd == 1 then
        self:AddAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, {
            ["Pos"] = Pos,
        })
    else
        self:StartAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, {
            ["Pos"] = Pos,
        })
    end
end

function AdditionalSkillComponent:OnMsgTriggerPos(TriggerID, bAdd)
    if not self.bInWorld then
        return
    end
    local SceneActorData = Game.WorldDataManager:GetCurLevelSceneActorData(tostring(TriggerID))
    if not SceneActorData or not SceneActorData.Transform.Position then
        return
    end
    local TargetPosition = SceneActorData.Transform.Position

    if bAdd == 1 then
        self:AddAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, {
			["TriggerID"] = TriggerID,	
            ["Pos"] = TargetPosition,
        })
    else
        self:StartAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, {
			["TriggerID"] = TriggerID,
			["Pos"] = TargetPosition,
        })
    end
end

function AdditionalSkillComponent:OnMsgStopCaneSkill()
    self:exitAdditionalSkill()
end

--endregion SystemAction

--region Private

function AdditionalSkillComponent:afterAdditionalSkill(InSkillType)
    if InSkillType == Enum.EAdditionalSkillType.StickNavigator then
        -- 01
		if self.OnceAssetHandle["01"] then
			self:DeactivateNiagaraSystem(self.OnceAssetHandle["01"])
		end
        local Duration01, Trans01 = self:processAdditionalSkill(InSkillType, self.Dowsing01Param)
        if Duration01 > 0 then
            self.AdditionalSkillAnimTimer1 = Game.TimerManager:CreateTimerAndStart(function()
				local niagaraID = self:PlayNiagaraEffectAtLocation(self.Dowsing01, Trans01, -1, false)
                self.AdditionalSkillAnimTimer1 = nil
				self.OnceAssetHandle["01"] = niagaraID
            end, Duration01 * 1000, 1)
        else
			local niagaraID = self:PlayNiagaraEffectAtLocation(self.Dowsing01, Trans01, -1, false)
			self.OnceAssetHandle["01"] = niagaraID
        end

        -- 02
		if self.OnceAssetHandle["02"] then
			self:DeactivateNiagaraSystem(self.OnceAssetHandle["02"])
		end
        local Duration02, Trans02 = self:processAdditionalSkill(InSkillType, self.Dowsing02Param)
        if Duration02 > 0 then
            self.AdditionalSkillAnimTimer2 = Game.TimerManager:CreateTimerAndStart(function()
				local niagaraID = self:PlayNiagaraEffectAtLocation(self.Dowsing02, Trans02, -1, false)
                self.AdditionalSkillAnimTimer2 = nil
				self.OnceAssetHandle["02"] = niagaraID
            end, Duration02 * 1000, 1)
        else
			local niagaraID = self:PlayNiagaraEffectAtLocation(self.Dowsing02, Trans02, -1, false)
			self.OnceAssetHandle["02"] = niagaraID
        end

		-- 03
		if self.OnceAssetHandle["03"] then
			self:DeactivateNiagaraSystem(self.OnceAssetHandle["03"])
		end
		local Duration03 = self.Dowsing03Param[1]
		if Duration03 > 0 then
			self.AdditionalSkillAnimTimer3 = Game.TimerManager:CreateTimerAndStart(function()
				local niagaraID = self:playFaceTargetLine(self.Dowsing03, self.Dowsing03Param, self.AdditionalSkillParam)
				self.AdditionalSkillAnimTimer3 = nil
				self.OnceAssetHandle["03"] = niagaraID
			end, Duration03 * 1000, 1)
		else
			local niagaraID = self:playFaceTargetLine(self.Dowsing03, self.Dowsing03Param, self.AdditionalSkillParam)
			self.OnceAssetHandle["03"] = niagaraID
		end

        -- Line
        if getlen(self.RefreshAssetHandle) >= self.DowsingLineRefreshNum then
            self:DeactivateNiagaraSystem(table.remove(self.RefreshAssetHandle, 1))
        end
        local DurationLine, TransLine, localTargetPos = self:processAdditionalSkill(InSkillType, self.DowsingLineParam, self.AdditionalSkillParam)
        if DurationLine > 0 then
            self.AdditionalSkillAnimTimer4 = Game.TimerManager:CreateTimerAndStart(function()
				local niagaraID = self:PlayNiagaraEffectAtLocation(self.DowsingLine, TransLine, -1, false)
				Game.EffectManager:UpdateNiagaraVec3Param(niagaraID, "Location", localTargetPos.X, 0, localTargetPos.Z)
				Game.EffectManager:UpdateNiagaraFloatParam(niagaraID, "SpawnRadius", self.DowsingLineRefreshRadius)
                table.insert(self.RefreshAssetHandle, niagaraID)
                self.AdditionalSkillAnimTimer4 = nil
				self:keepRefreshLine()
            end, DurationLine * 1000, 1)
        else
			local niagaraID = self:PlayNiagaraEffectAtLocation(self.DowsingLine, TransLine, -1, false)
			Game.EffectManager:UpdateNiagaraVec3Param(niagaraID, "Location", localTargetPos.X, 0, localTargetPos.Z)
			Game.EffectManager:UpdateNiagaraFloatParam(niagaraID, "SpawnRadius", self.DowsingLineRefreshRadius)
			table.insert(self.RefreshAssetHandle, niagaraID)
			self:keepRefreshLine()
        end

        -- Tattoo
		if self.OnceAssetHandle["Tattoo"] then
			self:DeactivateNiagaraSystem(self.OnceAssetHandle["Tattoo"])
		end
        local DurationTattoo, TransTattoo = self:processAdditionalSkill(InSkillType, self.DowsingTattooParam)
        if DurationTattoo > 0 then
            self.AdditionalSkillAnimTimer5 = Game.TimerManager:CreateTimerAndStart(function()
				local niagaraID = self:PlayNiagaraEffectAtLocation(self.DowsingTattoo, TransTattoo, -1, false)
                self.AdditionalSkillAnimTimer5 = nil
				self.OnceAssetHandle["Tattoo"] = niagaraID
            end, DurationTattoo * 1000, 1)
        else
			local niagaraID = self:PlayNiagaraEffectAtLocation(self.DowsingTattoo, TransTattoo, -1, false)
			self.OnceAssetHandle["Tattoo"] = niagaraID
        end
    end
end

function AdditionalSkillComponent:processAdditionalSkill(InSkillType, params, outerParam)
    if InSkillType == Enum.EAdditionalSkillType.StickNavigator then
        local Duration = params[1]
		local MyTrans
		if not outerParam then
			local x = params[2] or 0
			local y = params[3] or 0
			local z = params[4] or 0
			local yaw = params[5] or 0
			local LocalLoc = FVector(x, y, z)
			local LocalRot = FRotator(0, yaw, 0)
			local LocalTrans = M3D.AssembleTransform(LocalLoc, M3D.FRotToQuat(LocalRot))
			MyTrans = self:getWorldTransform(LocalTrans)
		end

        if outerParam ~= nil then
            local outerPos = FVector(outerParam.Pos.X, outerParam.Pos.Y, outerParam.Pos.Z)
            local outerInstanceID = outerParam.InstanceID
            if outerInstanceID then
                local eid = Game.WorldManager:GetNpcByInstance(tostring(outerInstanceID))
                local TargetEntity = Game.EntityManager:getEntity(eid)
				if TargetEntity then
					outerPos = TargetEntity:GetPosition()
				end
            end
			local pilotPos = self:GetPosition()
			local targetDir = pilotPos - outerPos
			targetDir:Normalize(1e-8)
			local initPos = targetDir * self.DowsingLineRefreshDistance + pilotPos
			MyTrans = M3D.AssembleTransform(initPos, M3D.FRotToQuat(self:GetRotation()))
			local CalcRot = KismetMathLibrary.FindLookAtRotation(initPos, outerPos)
            M3D.FRotToQuat(CalcRot, MyTrans.Rotation)
			local inversePos = M3D.Vec3()
			MyTrans:InverseTransformPosition(M3D.ToVec3(outerPos), inversePos)
			return Duration, MyTrans, M3D.ToFVector(inversePos)
        end

        return Duration, MyTrans, M3D.ToFVector(MyTrans.Translation)
    end
end

function AdditionalSkillComponent:reachTargetAdditionalSkillPos(InSkillType, Param)
    if InSkillType == Enum.EAdditionalSkillType.StickNavigator then
        local outerPos = FVector(Param.Pos.X, Param.Pos.Y, Param.Pos.Z)
        local outerInstanceID = Param.InstanceID
        if outerInstanceID then
            local eid = Game.WorldManager:GetNpcByInstance(tostring(outerInstanceID))
            local TargetEntity = Game.EntityManager:getEntity(eid)
            if TargetEntity then
                outerPos = TargetEntity:GetPosition()
            end
        end
        local CalcDis = KismetMathLibrary.Vector_Distance(self:GetPosition(), outerPos)
        return CalcDis <= self.AcceptanceRadiusInAdditionalSkill
    end

    return false
end

function AdditionalSkillComponent:getWorldTransform(InTransform)
    local TargetLoc = self:GetPosition()
    local TargetRot = self:GetRotation()
    local PlayerTransform = M3D.AssembleTransform(TargetLoc, M3D.FRotToQuat(TargetRot))
    local FinalTransform = M3D.Transform()
    InTransform:Mul(PlayerTransform, FinalTransform)
    return FinalTransform
end

function AdditionalSkillComponent:keepRefreshLine()
    local bInBattle = self.InBattle or false
    if bInBattle then
        self:exitAdditionalSkill()
        return
    end

    if self:reachTargetAdditionalSkillPos(Enum.EAdditionalSkillType.StickNavigator, self.AdditionalSkillParam) then
        self:exitAdditionalSkill(true)
        return
    end

    if not self.bInWorld then
        self:exitAdditionalSkill()
        return
    end

    self.AdditionalSkillRefreshTimer = Game.TimerManager:CreateTimerAndStart(function()
        if getlen(self.RefreshAssetHandle) >= self.DowsingLineRefreshNum then
            self:DeactivateNiagaraSystem(table.remove(self.RefreshAssetHandle, 1))
        end
        if not self.bInWorld then
            self:exitAdditionalSkill()
            return
        end
		self:AkPostEventOnActor(self.DowsingLineAk)
        local _, TransLine, localTargetPos = self:processAdditionalSkill(Enum.EAdditionalSkillType.StickNavigator, self.DowsingLineParam, self.AdditionalSkillParam)
		local niagaraID = self:PlayNiagaraEffectAtLocation(self.DowsingLine, TransLine, -1, false)
		Game.EffectManager:UpdateNiagaraVec3Param(niagaraID, "Location", localTargetPos.X, 0, localTargetPos.Z)
		Game.EffectManager:UpdateNiagaraFloatParam(niagaraID, "SpawnRadius", self.DowsingLineRefreshRadius)
		--self:Debug("LYL DEBUG BIU BIU BIU ", TransLine.Translation, Game.me:GetPosition())
		table.insert(self.RefreshAssetHandle, niagaraID)
        self.AdditionalSkillRefreshTimer = nil
        self:keepRefreshLine()
    end, self.DowsingLineRefresh, 1)
end

function AdditionalSkillComponent:playFaceTargetLine(NiagaraPath, params, outerParam)
	local x = params[2]
	local y = params[3]
	local z = params[4]
	local yaw = params[5]
	local LocalLoc = FVector(x, y, z)
	local LocalRot = FRotator(0, yaw, 0)
	local RTrans = M3D.AssembleTransform(LocalLoc, M3D.FRotToQuat(LocalRot))

	local outerPos = FVector(outerParam.Pos.X, outerParam.Pos.Y, outerParam.Pos.Z)
	local outerInstanceID = outerParam.InstanceID
	if outerInstanceID then
		local eid = Game.WorldManager:GetNpcByInstance(tostring(outerInstanceID))
		local TargetEntity = Game.EntityManager:getEntity(eid)
		if TargetEntity then
			outerPos = TargetEntity:GetPosition()
		end
	end
	
	local niagaraParam = NiagaraEffectParamTemplate.AllocFromPool()
	niagaraParam.bEngineCulling = false
	niagaraParam.NiagaraEffectPath = NiagaraPath
	niagaraParam.bNeedAttach = true
	niagaraParam.AttachPointName = "root"
	M3D.ToTransform(RTrans, niagaraParam.SpawnTrans)
	niagaraParam.bForceFaceToLocation = true
	niagaraParam.FacingTargetLocation = M3D.Vec2(outerPos.X, outerPos.Y)
	niagaraParam.AttachComponentId = self.CppEntity:KAPI_Actor_GetMainMesh()
	
	return Game.me:PlayNiagaraEffect(niagaraParam)
end

function AdditionalSkillComponent:lockLocoInAdditionalSkill(bDisable)
    if self.bLockLocoInAdditionalSkill == bDisable then
        return
    end
    self:DisableLocoAll(Enum.ELocoControlTag.Skill, bDisable)
    self.bLockLocoInAdditionalSkill = bDisable
end

--endregion Private

return AdditionalSkillComponent
