--- 占卜家职业机制

--- CardInfo类，用于管理占卜家卡牌信息
local CardInfo = {}
CardInfo.__index = CardInfo

-- 创建一个新的卡牌信息对象
function CardInfo.new(cardId, cardToken)
    local self = setmetatable({}, CardInfo)
    self.cardId = cardId  -- 卡牌ID
    self.cardToken = cardToken  -- 卡牌令牌（用于标识一组卡牌）
    self.color = cardId == Enum.ERoleMechanismConstData.FOOL_YELLOW_CARD_BUFF and "yellow" or "blue"
    self.effectIndex = -1  -- 特效索引，用于管理视觉效果
    return self
end

-- 获取卡牌颜色
function CardInfo:getColor()
    return self.color
end

-- 设置特效索引
function CardInfo:setEffectIndex(index)
    self.effectIndex = index
end

-- 获取特效索引
function CardInfo:getEffectIndex()
    return self.effectIndex
end

-- 获取特效标签
function CardInfo:getEffectTag()
    if self.effectIndex < 0 then
        return nil
    end
    return tostring(self.effectIndex) .. self.color
end

function ProfessionComponent:initForFool()
	if self.eid ~= Game.me.eid then
		return
	end

	-- 加载事件监听
	Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF_START, self, "OnBattleBuffLayerChangedForFool", self.eid)
	Game.EventSystem:AddListener(_G.EEventTypes.BATTLE_BUFF_LAYER_CHANGED, self, "OnBattleBuffLayerChangedForFool", self.eid)
	-- 初始化
	self.cardToken = 1
	self.foolCardList = {}
	self.tokenToUseIndex = {}
	self.indexToUseFlag = {false, false, false, false}
	self.blueCardCount = 0
	self.yellowCardCount = 0
	self:createFoolCardAttachEntity()
end

function ProfessionComponent:unInitForFool()
	table.clear(self.foolCardList)
	self.blueCardCount = 0
	self.yellowCardCount = 0
	Game.EventSystem:Publish(_G.EEventTypes.ON_FOOL_CARD_REFRESH)
	Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.BATTLE_BUFF_START, self, "OnBattleBuffLayerChangedForFool", self.eid)
	Game.EventSystem:RemoveListenerFromType(_G.EEventTypes.BATTLE_BUFF_LAYER_CHANGED, self, "OnBattleBuffLayerChangedForFool", self.eid)
	self:removeFoolCardAttachEntity()
end

function ProfessionComponent:GetFoolCardList()
	return self.foolCardList
end

function ProfessionComponent:OnBattleBuffLayerChangedForFool(buff)
	if buff.buffID == Enum.ERoleMechanismConstData.FOOL_BLUE_CARD_BUFF then
		self:onFoolBlueCardChange(buff.buffID, buff.layer, true)
	elseif buff.buffID == Enum.ERoleMechanismConstData.FOOL_YELLOW_CARD_BUFF then
		self:onFoolYellowCardChange(buff.buffID, buff.layer, true)
	elseif buff.buffID == Enum.ERoleMechanismConstData.FOOL_BLUE_BUFF then
		self:onBlueCardClear()
	elseif buff.buffID == Enum.ERoleMechanismConstData.FOOL_YELLOW_BUFF then
		self:onYellowCardClear()
	end
end

function ProfessionComponent:onBlueCardClear()
	if self.isRebuilding then
		return
	end

	local comboCardId = Enum.ERoleMechanismConstData.FOOL_BLUE_CARD_BUFF
	-- 先塞一张蓝卡到3
	self:onFoolBlueCardChange(comboCardId, 3, true)
	-- 全部清理消息
	Game.EventSystem:Publish(_G.EEventTypes.ON_FOOL_CARD_CLEARED, comboCardId, self.cardToken)
	-- 数据层清理
	self.blueCardCount = 0
	self.yellowCardCount = 0
	table.clear(self.foolCardList)
	self:removeCardEffects(self.cardToken)
	self.cardToken = self.cardToken + 1
end

function ProfessionComponent:onYellowCardClear()
	if self.isRebuilding then
		return
	end

	local comboCardId = Enum.ERoleMechanismConstData.FOOL_YELLOW_CARD_BUFF
	-- 先塞一张黄卡到3
	self:onFoolYellowCardChange(comboCardId, 3, true)
	-- 全部清理消息
	Game.EventSystem:Publish(_G.EEventTypes.ON_FOOL_CARD_CLEARED, comboCardId, self.cardToken)
	-- 数据层清理
	self.blueCardCount = 0
	self.yellowCardCount = 0
	table.clear(self.foolCardList)
	self:removeCardEffects(self.cardToken)
	self.cardToken = self.cardToken + 1
end

function ProfessionComponent:onFoolBlueCardChange(cardId, count, needPlayEffect)
	if count > self.blueCardCount then
		for i = 1, count - self.blueCardCount do
			local cardInfo = CardInfo.new(cardId, self.cardToken)
			table.insert(self.foolCardList, cardInfo)
			if needPlayEffect then
				self:addCardEffect(cardInfo)
			end
			Game.EventSystem:Publish(_G.EEventTypes.ON_FOOL_CARD_CHANGED, cardInfo)
		end
	end
	self.blueCardCount = count
end

function ProfessionComponent:onFoolYellowCardChange(cardId, count, needPlayEffect)
	if count > self.yellowCardCount then
		for i = 1, count - self.yellowCardCount do
			local cardInfo = CardInfo.new(cardId, self.cardToken)
			table.insert(self.foolCardList, cardInfo)
			if needPlayEffect then
				self:addCardEffect(cardInfo)
			end
			Game.EventSystem:Publish(_G.EEventTypes.ON_FOOL_CARD_CHANGED, cardInfo)
		end
	end
	self.yellowCardCount = count
end 

function ProfessionComponent:createFoolCardAttachEntity()
	if self.foolCardAttachEntityID ~= nil and Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID) ~= nil then
		return
	end
	local AttachEntity = self:AddAttachment_V2(Enum.EAttachItemType.SurroundActor, "Prop_4000106")
	local ModelData = Game.ActorAppearanceManager.AvatarModelLib["Prop_4000106"]
	if AttachEntity and ModelData then
		AttachEntity:SetAttachToForFoolCard(Enum.EAttachReason.SurroundActor , self.eid, 0,0,0 ,0,0,0, ModelData.SlotName)
		AttachEntity:ApplyAttach()
		self.foolCardAttachEntityID = AttachEntity.eid
	end
end

function ProfessionComponent:removeFoolCardAttachEntity()
	local AttachEntity = Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID)
	if AttachEntity then
		AttachEntity:ClearAttachEffects("bysystemcall")
		AttachEntity:destroy()
		self.foolCardAttachEntityID = nil
	end
end

function ProfessionComponent:findCanUseIndex()
	for i = 1, 4 do
		if not self.indexToUseFlag[i] then
			self.indexToUseFlag[i] = true
			return i
		end
	end
	return -1
end

function ProfessionComponent:addCardEffect(cardInfo)
	local index = self:findCanUseIndex()
	if index < 0 then
		return
	end
	
	cardInfo:setEffectIndex(index)
	local attachEntity = Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID)
	local effectTag = cardInfo:getEffectTag()
	
	if attachEntity and effectTag then
		attachEntity:PlayAttachEffectByTag("bysystemcall", effectTag, 1250)
		if not self.tokenToUseIndex[self.cardToken] then
			self.tokenToUseIndex[self.cardToken] = {}
		end
		table.insert(self.tokenToUseIndex[self.cardToken], index)
	end
end

function ProfessionComponent:removeCardEffects(comboCardToken)
	local useIndexes = self.tokenToUseIndex[comboCardToken]
	if not useIndexes then
		return
	end

	local AttachEntity = Game.EntityManager:GetEntityByIntID(self.foolCardAttachEntityID)
	if not AttachEntity then
		return
	end

	Game.TimerManager:CreateTimerAndStart(function()
		for i, index in ipairs(useIndexes) do
			local tag = tostring(index) .. "yellow"
			AttachEntity:ClearAttachEffects("bysystemcall", tag)
			tag = tostring(index) .. "blue"
			AttachEntity:ClearAttachEffects("bysystemcall", tag)
			self.indexToUseFlag[index] = false
		end
		self.tokenToUseIndex[comboCardToken] = nil
	end, 0, 1)
end
