

---@class ExplorationProgressComponent
ExplorationProgressComponent = DefineComponent("ExplorationProgressComponent")

--道具提交rpc
function ExplorationProgressComponent:ReqExploreSoulSubmit(soulId, stage)
    self.remote.ReqExploreSoulSubmit(soulId, stage)
end

--道具提交的ret
function ExplorationProgressComponent:RetExploreSoulSubmit(result, soulId, stage)
    if result.Code == Enum.ERetCode.SUCCESS then
        Game.GlobalEventSystem:Publish(EEventTypesV2.EXPLORE_SOUL_SUBMIT_RESULT, soulId, stage)
    end
end

function ExplorationProgressComponent:ReqExploreSteleStartTask(steleID)
    self.remote.ReqExploreSteleStartTask(steleID)
end

function ExplorationProgressComponent:RetExploreSteleStartTask(result, steleID)
    local t
end

function ExplorationProgressComponent:ReqExploreUnlockArea(steleID)
    self.remote.ReqExploreUnlockArea(steleID)
end

function ExplorationProgressComponent:RetExploreUnlockArea(result, steleID, insID)
    if result.Code == Enum.ERetCode.SUCCESS then
        Game.MapSystem:OnExploreUnlockArea(steleID, insID)
    end
end

function ExplorationProgressComponent:ReqExploreReceiveReward(firstLevelId, rewardIndex)
    self.remote.ReqExploreReceiveReward(firstLevelId, rewardIndex)
end

function ExplorationProgressComponent:RetExploreReceiveReward(result, firstLevelId, rewardIndex)
    if result.Code == Enum.ERetCode.SUCCESS then
        Game.GlobalEventSystem:Publish(EEventTypesV2.RET_EXPLORE_REWARD_GET, firstLevelId)
    end
end

function ExplorationProgressComponent:ReqPuzzlePlayDone(instanceID, type)
    self.remote.ReqPuzzlePlayDone(instanceID, type)
end

function ExplorationProgressComponent:ReqRotaryMysteryDone(instanceID)
    self.remote.ReqRotaryMysteryDone(instanceID)
end

function ExplorationProgressComponent:ReqPuzzlePlayLeaveSpace(instanceID)
    self.remote.ReqPuzzlePlayLeaveSpace(instanceID)
end

ExploreSoulInfo = DefineClass("EXPLORE_SOUL_INFO")
function ExploreSoulInfo:set_Count(entity, new, old)
    if entity.eid == GetMainPlayerEID() then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_EXPLORE_SOUL_COUNT_CHANGED)
    end
end

function ExploreSoulInfo:set_Stage(entity, new, old)
end

function ExploreSoulInfo:set_NextSoulInstanceID(entity, new, old)
    if entity.eid == GetMainPlayerEID() then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_EXPLORE_NEXTSOULINSTANCEID_CHANGED, new, old)
    end
end

EXPLORE_STELE_INFO =DefineClass("EXPLORE_STELE_INFO")

function EXPLORE_STELE_INFO:set_bTaskFinished(entity, new, old)
end

FIRST_LEVEL_AREA_INFO = DefineClass("FIRST_LEVEL_AREA_INFO")
function FIRST_LEVEL_AREA_INFO:set_ExploreDegree(entity, new, old)
	local diff = new - old
	if diff > 0 then
        Game.DiscoverySystem:ExploreDegreeUpgradeRemind(self.FirstLevelAreaID, old, new)
        Game.GlobalEventSystem:Publish(EEventTypesV2.RET_EXPLORE_DEGREE_UPDATE)
        Game.CommonInteractorManager:TriggerCommonInteractorEvent(Enum.CommonInteractorEventType.FIRST_LEVEL_AREA_EXPLORATION_PROGRESS, tostring(self.FirstLevelAreaID))
	end
end


return ExplorationProgressComponent
