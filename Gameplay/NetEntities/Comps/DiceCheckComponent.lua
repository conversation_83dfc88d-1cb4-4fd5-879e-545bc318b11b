DiceCheckComponent = DefineComponent("Dice<PERSON><PERSON>ckComponent")

-- client -> server

function Dice<PERSON>heckComponent:ReqDiceCheckPreview(id)
    self.remote.ReqDiceCheckPreview(id)
end

function DiceCheckComponent:ReqD<PERSON><PERSON><PERSON><PERSON><PERSON>oll(id)
    self.remote.ReqDiceC<PERSON>ckRoll(id)
end


-- server -> client

function DiceCheckComponent:RetDiceCheckPreview(result, info)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_DICE_CHECK_PREVIEW_FINISH, info)
end

function DiceCheckComponent:RetD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(result, rollResultInfo)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_DICE_CHECK_ROLL_FINISH, rollResultInfo)
end


return DiceCheckComponent