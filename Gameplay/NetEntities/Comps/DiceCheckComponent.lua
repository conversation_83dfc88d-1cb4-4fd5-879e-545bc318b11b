DiceCheckComponent = DefineComponent("DiceCheckComponent")

-- client -> server

function DiceCheckComponent:ReqDiceCheckPreview(id)
    self.remote.ReqDiceCheckPreview(id)
end

function DiceCheckComponent:ReqD<PERSON><PERSON><PERSON>ckRoll(id)
    self.remote.ReqDiceCheck(id)
end


-- server -> client

function DiceCheckComponent:RetDiceCheckPreview(result, info)
    
end

function DiceCheckComponent:RetD<PERSON><PERSON>heckRoll(result, rollResultInfo)

end


return DiceCheckComponent