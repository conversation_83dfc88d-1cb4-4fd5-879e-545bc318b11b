---@class ClimateComponent
ClimateComponent = DefineComponent("ClimateComponent")

function ClimateComponent:ReqAllRegionClimateInfo()
    self.remote:ReqAllRegionClimateInfo()
end

function ClimateComponent:RetAllRegionClimateInfo(result, RegionClimateInfoList)
    if result.Code == Game.NetworkManager.ErrCodes.NO_ERR then
		Game.ClimateSystem.cachedRegionClimateInfo = RegionClimateInfoList
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_RET_ALL_CLIMATE_INFO, RegionClimateInfoList)
    end
end

function ClimateComponent:RetRegionClimateInfo(arg0_Result, arg1_RegionClimateInfoList)
end

return ClimateComponent
