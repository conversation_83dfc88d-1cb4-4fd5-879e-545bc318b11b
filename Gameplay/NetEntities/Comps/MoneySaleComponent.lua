
MoneySaleComponent = DefineComponent("MoneySaleComponent")

-- region Important
function MoneySaleComponent:ctor()
end

function MoneySaleComponent:dtor()
end

function MoneySaleComponent:GetMoneySellListRequest(isfill)
    self.remote.ReqGetMoneySellList(isfill)
end

function MoneySaleComponent:RetGetMoney<PERSON>ellList(MoneySellList)
    self:Debug("MoneySaleComponent: RetGetMoneySellList")
    Log.Dump(MoneySellList)
	Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_LIST_UPDATE, MoneySellList)
end

function MoneySaleComponent:SellMoneyRequest(SellRate,SellNum)
    self.remote.ReqSellMoney(SellRate,SellNum)
end

function MoneySaleComponent:RetSellMoney(result,MoneySellList,ownerSellMoneyInfo)
    self:Debug("MoneySaleComponent: RetSellMoney")
    if result and result.Code == 0 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.MONEYSELL_SUCCESS)
		Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_LIST_UPDATE, MoneySellList, ownerSellMoneyInfo)
    end
end

function MoneySaleComponent:BuyMoneyRequest(buydata)
    self.remote.ReqBuyMoney(buydata)
end

function MoneySaleComponent:RetBuyMoney(result,MoneySellList)
    self:Debug("MoneySaleComponent: RetBuyMoney")
    if result and result.Code == 0 then
		Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_LIST_UPDATE, MoneySellList)
    end
end

function MoneySaleComponent:TakeBackMoneyRequest(opNUID)
    self.remote.RetTakeBackMoney(opNUID)
end

function MoneySaleComponent:RetTakeBackMoney(Result,opNUID,NextOffTime)
    self:Debug("MoneySaleComponent: RetTakeBackMoney")
    if Result and Result.Code == 0 then
		Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_OWNER_OFFLIST, opNUID,NextOffTime)
    end
end

function MoneySaleComponent:GetOwnerSellMoneyInfoListRequest()
    self.remote.ReqGetOwnerSellMoneyInfoList()
end

function MoneySaleComponent:RetGetOwnerSellMoneyInfo(OwnerSellMoneyInfoList,NextOffTime)
    self:Debug("MoneySaleComponent: RetGetOwnerSellMoneyInfo")
	Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_OWNER_SELLINGLIST, OwnerSellMoneyInfoList,NextOffTime)
end

function MoneySaleComponent:GetOwnerSellHistoryRequest(buydata)
    self.remote.RepGetOwnerSellHistory(buydata)
end

function MoneySaleComponent:RetGetOwnerSellHistory(MoneySellHistoryList)
    self:Debug("MoneySaleComponent: RetGetOwnerSellHistory")
	Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_OWNER_SOLDLIST, MoneySellHistoryList)
end

function MoneySaleComponent:GetOwnerBuyHistoryRequest(buydata)
    self.remote.ReqGetOwnerBuyHistory(buydata)
end

function MoneySaleComponent:RetGetOwnerBuyHistory(MoneyBuyHistoryList)
    self:Debug("MoneySaleComponent: RetGetOwnerBuyHistory")
	Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_OWNER_BUYLIST, MoneyBuyHistoryList)
end

function MoneySaleComponent:OnSellMoneySuccessNotify(uid,sellrate,sellnum)
    self:DebugFmt("MoneySaleComponent: OnSellMoneySuccessNotify: uid:%s sellrate:%d sellnum:%d",uid,sellrate,sellnum)
	Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_SOLD_NOTIFY, uid, sellrate, sellnum)
end

function MoneySaleComponent:OnMsgTimerOutTakeBackMoney(opNUID)
	Game.GlobalEventSystem:Publish(EEventTypesV2.CONSIGNMENT_OWNER_OFFLIST, opNUID, nil)
end



--endregion Rpc

return MoneySaleComponent
