
---@class AvatarActorNpcInteractComponent
AvatarActor<PERSON>pcInteractComponent = DefineComponent("AvatarActorNpcInteractComponent")

function AvatarActorNpcInteractComponent:ReqNpcSPInteract(npcUid, buttonID, Params)
    if Params == nil then Params = {} end
    self.remote.ReqNpcSPInteract(npcUid, buttonID, Params)
end

function AvatarActorNpcInteractComponent:RetNpcSPInteract(Result, npcUid, buttonID, bSuccess)
    Game.HUDInteractManager:OnGetInteractRet(npcUid, buttonID, bSuccess)
end

function AvatarActorNpcInteractComponent:ReqNpcAskPrice(npcUid,price)
    self.remote.ReqNpcAskPrice(npcUid,price)
end

function AvatarActorNpcInteractComponent:RetNpcAskPrice(Result, npcUid,price)
    
end

function AvatarActorNpcInteractComponent:ReqSetNpcMood(npcUid, mood)
    self.remote.ReqSetNpcMood(npcUid,mood)
end

function AvatarActorNpcInteractComponent:ReqAddNpcMood(npcUid, mood)
    self.remote.ReqAddNpcMood(npcUid,mood)
end

function AvatarActorNpcInteractComponent:RetSetNpcMood(Result, npcUid, mood)
   
end
function AvatarActorNpcInteractComponent:RetNpcAskPrice(Result, npcUid, price)
end

function AvatarActorNpcInteractComponent:ReqSpecialInteractiveKey(npcUid, uiTemplateID)
    self.remote.ReqSpecialInteractiveKey(npcUid, uiTemplateID)
end 

function AvatarActorNpcInteractComponent:RetAddNpcMood(arg0_Result, arg1_string, arg2_int)
end
