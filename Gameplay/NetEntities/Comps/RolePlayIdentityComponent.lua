--- @class RolePlayIdentityComponent
RolePlayIdentityComponent = DefineComponent("RolePlayIdentityComponent")

-- 同步变更的扮演身份信息
function RolePlayIdentityComponent:OnMsgSyncRolePlayIdentity(identityInfos)
    Log.Debug("OnMsgSyncRolePlayIdentity", identityInfos)
    Game.RolePlaySystem:OnUpdateRolePlayIdentity(identityInfos)
end

-- 身份等级解锁响应
function RolePlayIdentityComponent:OnMsgSyncRolePlayUnlockIdentity(identityId, level)
    Log.Debug("OnMsgSyncRolePlayUnlockIdentity", identityId, level)
    Game.RolePlaySystem:OnRolePlayUnlockIdentityLevel(identityId, level)
end

-- 身份等级升级成功响应
function RolePlayIdentityComponent:RetRolePlayIdentityUpLevel(identityId, level)
    Log.Debug("RetRolePlayIdentityUpLevel", identityId, level)
    Game.RolePlaySystem:OnRolePlayIdentityLevelUp(identityId, level)
end

-- 领取身份等级奖励成功的响应
function RolePlayIdentityComponent:RetRolePlayIdentityLevelReward(identityId, level)
    Log.Debug("RetRolePlayIdentityLevelReward", identityId, level)
    Game.RolePlaySystem:OnIdentityLevelRewarded(identityId, level)
end

-- 开始扮演的响应
function RolePlayIdentityComponent:RetRolePlayStart(identityId)
    Log.Debug("RetRolePlayStart", identityId)
    Game.RolePlaySystem:OnRolePlayStart(identityId)
end

return RolePlayIdentityComponent
