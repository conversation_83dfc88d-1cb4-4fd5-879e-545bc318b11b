local flowchartConst = kg_require("Shared.Const.FlowchartConst")
local triggerUtils = kg_require("Shared.Utils.TriggerUtils")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local PostProcessConst = kg_require("Gameplay.Effect.PostProcessConst")
local QuestUtils = kg_require("Gameplay.LogicSystem.Quest.QuestUtil")

local math_floor = math.floor
local CUSTOM_DEPTH_LOGIC_TYPE = ViewControlConst.CUSTOM_DEPTH_LOGIC_TYPE

---@class FlowchartActionComponent
FlowchartActionComponent = DefineComponent("FlowchartActionComponent")

---用于处理FlowChat 下发RPC的组件
function FlowchartActionComponent:ctor()

    ---接口记录参数
    self.TransformCache = FTransform()
    self.EffectEntities = {}

	self.RecentPlayCameraShake = nil
	self.ThirdCameraFOVLayerID = nil
	self.ThirdCameraZoomLayerID = nil
end


function FlowchartActionComponent:dtor()

end


function FlowchartActionComponent:__component_ExitWorld__()
	if 	self.TSHidePlayerTimer then
		Game.TimerManager:StopTimerAndKill(	self.TSHidePlayerTimer)
		self.TSHidePlayerTimer = nil
	end
    self:RemoveAllEffect()
end

-------------FlowChart功能接口 ------------------------------------------------------------------------------------------
local EFlowChartDelayActionType = {
    LocationEffect = 1,
    AttachSelfEffect = 2,
    PostProcess = 3,
}

function FlowchartActionComponent:OnEffectDelayTimer(UniqueID)
    if self.EffectEntities[UniqueID] then
        if self.EffectEntities[UniqueID].Type == EFlowChartDelayActionType.LocationEffect then
            local EffectInfo = Game.TableData.GetLocationEffectDataRow(self.EffectEntities[UniqueID].EffectID)

            if EffectInfo then
                self.EffectEntities[UniqueID].Trans.Scale3D = M3D.Vec3(EffectInfo.Scale,EffectInfo.Scale,EffectInfo.Scale)
                local EffectEntityID = self:PlayNiagaraEffectAtLocation(
                        EffectInfo.EffectPath,
                        self.EffectEntities[UniqueID].Trans,
                        self.EffectEntities[UniqueID].Duration,
                        false)

                self.EffectEntities[UniqueID].eid = EffectEntityID
            end

        elseif self.EffectEntities[UniqueID].Type == EFlowChartDelayActionType.AttachSelfEffect then
            self.EffectEntities[UniqueID].eid = self:PlayEffectByAttachEffectDataID(self.EffectEntities[UniqueID].EffectID, self.EffectEntities[UniqueID].Duration)
            
        elseif self.EffectEntities[UniqueID].Type == EFlowChartDelayActionType.PostProcess then
            local PPID = Game.PostProcessManager:PlayPostProcessPreset(
                    Enum.EPostProcessLayers.Camera,
                    nil,
                    self.EffectEntities[UniqueID].EffectID,
                    self.EffectEntities[UniqueID].AlphaIn,
                    self.EffectEntities[UniqueID].AlphaOut,
                    self.EffectEntities[UniqueID].Duration
            )
            Game.PostProcessManager:SetPostProcessDestroyWhenLeaveSpace(PPID)
            self.EffectEntities[UniqueID].PPID = PPID
        end
    end
end

function FlowchartActionComponent:ReqReportDialogueOption(DialogueID, OptionID)
    self.remote.ReqReportDialogueOption(DialogueID, OptionID)
end

function FlowchartActionComponent:OnMsgPlaySceneEffect(UniqueID,EffectID, Pos, Rot, DelayTime, Duration)

    local Trans = M3D.AssembleTransform(Pos, M3D.FRotToQuat(Rot))

    self:OnMsgRemoveSceneEffect(UniqueID)

    local DataHandle = {
        Type = EFlowChartDelayActionType.LocationEffect,
        Trans = Trans,
        Duration = Duration,
        EffectID = EffectID,
    }

    if DelayTime and DelayTime > 0 then
        DataHandle.DelayTimer = Game.TimerManager:CreateTimerAndStart(function()
            self:OnEffectDelayTimer(UniqueID)
        end, DelayTime * 1000, 1)
        self.EffectEntities[UniqueID] = DataHandle
    else
        self.EffectEntities[UniqueID] = DataHandle
        self:OnEffectDelayTimer(UniqueID)
    end
end

function FlowchartActionComponent:FlowChartPlayEntityEffect(UniqueID, EffectID, DelayTime, Duration)
    self:OnMsgRemoveSceneEffect(UniqueID)

    local DataHandle = {
        Type = EFlowChartDelayActionType.AttachSelfEffect,
        Duration = Duration,
        EffectID = EffectID,

    }

    if DelayTime and DelayTime > 0 then
        DataHandle.DelayTimer = Game.TimerManager:CreateTimerAndStart(function()
            self:OnEffectDelayTimer(UniqueID)
        end, DelayTime * 1000, 1)
        self.EffectEntities[UniqueID] = DataHandle
    else
        self.EffectEntities[UniqueID] = DataHandle
        self:OnEffectDelayTimer(UniqueID)
    end
end


function FlowchartActionComponent:OnMsgPlayCameraPostProcessEffect(UniqueID, EffectID, DelayTime, Duration,AlphaIn,AlphaOut)
    self:OnMsgRemoveSceneEffect(UniqueID)

    local DataHandle = {
        Type = EFlowChartDelayActionType.PostProcess,
        Duration = Duration,
        EffectID = EffectID,
        AlphaIn = AlphaIn or 0.5,
        AlphaOut = AlphaOut or 0.5,
    }

    if DelayTime and DelayTime > 0 then
        DataHandle.DelayTimer = Game.TimerManager:CreateTimerAndStart(function()
            self:OnEffectDelayTimer(UniqueID)
        end, DelayTime * 1000, 1)
        self.EffectEntities[UniqueID] = DataHandle
    else
        self.EffectEntities[UniqueID] = DataHandle
        self:OnEffectDelayTimer(UniqueID)
    end
end



function FlowchartActionComponent:CleanUpSingleEffect(UniqueID)
    if self.EffectEntities[UniqueID] then
        if self.EffectEntities[UniqueID].Type == EFlowChartDelayActionType.LocationEffect or
           self.EffectEntities[UniqueID].Type == EFlowChartDelayActionType.AttachSelfEffect
        then
            if self.EffectEntities[UniqueID].eid then
                self:DeactivateNiagaraSystem(self.EffectEntities[UniqueID].eid)
            end
        elseif self.EffectEntities[UniqueID].Type  == EFlowChartDelayActionType.PostProcess then
            if self.EffectEntities[UniqueID].PPID then
                Game.PostProcessManager:StopPostProcessPreset(self.EffectEntities[UniqueID].PPID)
            end
        end

        if self.EffectEntities[UniqueID].DelayTimer then
            Game.TimerManager:StopTimerAndKill(self.EffectEntities[UniqueID].DelayTimer)
        end
    end
end

function FlowchartActionComponent:OnMsgRemoveSceneEffect(UniqueID)
    self:CleanUpSingleEffect(UniqueID)
    self.EffectEntities[UniqueID] = nil
end

function FlowchartActionComponent:OnMsgRemoveCameraPostProcessEffect(UniqueID)
    self:CleanUpSingleEffect(UniqueID)
    self.EffectEntities[UniqueID] = nil
end

function FlowchartActionComponent:RetMsgFlowchartSay(templateID, msgType, text, time, voice, showName, entityID)
    if not Game.EntityManager:getEntity(entityID) then
        return -- AOI 内已经没有此Entity
    end

    -- TODO: 国际服上线前,此处text必然为多语言key 目前流程图配置还没有支持多语言
    text = Game.TableDataManager:GetLangStr(text) or text
    voice = voice or ""
    if not showName or showName == "" then
        -- 如果对象是玩家, 使用玩家的名字
		-- 这里判断Game.me是销毁时序有问题，先加判断，等销毁时序问题处理好
        if templateID == 0 or (Game.me and entityID == Game.me.eid) then
            showName = Game.me.Name
        else
            -- 后续如果有其他类型，此处封装为一个函数，或者服务器GetCharacterName支持拿到真实的名字
            local rowData = Game.TableData.GetNpcInfoDataRow(templateID) or  Game.TableData.GetMonsterDataRow(templateID)
            if not rowData then
                self:ErrorFmt("RetMsgFlowchartSay invalid templateID: %s, msgType: %s, text: %s, showName: %s", templateID, msgType, text, showName)
                return
            end
            showName = rowData.Name or ""
        end
    else
        showName = Game.TableDataManager:GetLangStr(showName) or showName
    end

    local realType = flowchartConst.SAY_TEXT_TYPE_WITH_CHAT[msgType]
    local showChat = realType ~= nil
    msgType = realType or msgType
    local timeInMill = math_floor(time * 1000)
    -- 后续如果有新的类型，每一种绑定一个函数名，不要再加分支
    if msgType == flowchartConst.SAY_TEXT_TYPE.ASIDE_TALK then
		local title
		-- todo 后续这里应该还需要有个参数，表示是否显示title
		if templateID > 0 then
			local npcInfoData = Game.TableData.GetNpcInfoDataRow(templateID)
			if npcInfoData then
				title = npcInfoData.Title
			end
		end
        Game.TalkSystem:ShowCustomAsideTalk(text, timeInMill, voice, showName, showChat, nil, title)
    elseif msgType == flowchartConst.SAY_TEXT_TYPE.BUBBLE then
        Game.EventSystem:Publish(_G.EEventTypes.SERVER_ON_MSG_SHOW_CUSTOM_BUBBLE, entityID, text, voice, time, showChat)
    else
        self:ErrorFmt("RetMsgFlowchartSay invalid msgType: %s, templateID: %s, text: %s, showName: %s",  msgType, templateID, text, showName)
    end
end

function FlowchartActionComponent:RemoveAllEffect()
    for UniqueID, _ in pairs(self.EffectEntities) do
        self:CleanUpSingleEffect(UniqueID)
    end
    self.EffectEntities = {}
end

function FlowchartActionComponent:OnMsgStartMoveCamera(CameraInstanceID, BlendTime)
	Game.CameraManager:EnableDialogueCameraForGameControl(true,
        CameraInstanceID, 
        BlendTime >= 0 and BlendTime or nil
    )
end

function FlowchartActionComponent:OnMsgEndMoveCamera(CameraInstanceID, BlendTime)
	Game.CameraManager:EnableDialogueCameraForGameControl(false, 
        CameraInstanceID, 
        BlendTime >= 0 and BlendTime or nil
    )
end
--  slua.do Game.me:FlowChartPlaySceneEffect(1,1, FVector(0,0,0), FRotator(0,0,0), 0, 5)

function FlowchartActionComponent:OnMsgCameraShake(ID,Duration, Scale)
	local ShakeData = Game.TableData.GetCameraShakeConfigDataRow(ID)
	
    if Game.CameraManager and ShakeData and ShakeData.ShakeAsset~="" then
        self.RecentPlayCameraShake = Game.CameraManager:StartCameraShakeByAssetPath(ShakeData.ShakeAsset, Scale, Duration)
    end
end

function FlowchartActionComponent:OnMsgStopCameraShake()
	local RecentPlayCameraShake = self.RecentPlayCameraShake
	if RecentPlayCameraShake then
		Game.CameraManager:StopCameraShakeByToken(RecentPlayCameraShake)
		self.RecentPlayCameraShake = nil
	end
end

function FlowchartActionComponent:OnMsgSetThirdCameraFOV(Value, BlendInTime, Duration, BlendOutTime)
	self.ThirdCameraFOVLayerID = Game.CameraManager:SetThirdCameraFOV(Value, BlendInTime, Duration, BlendOutTime)
end


function FlowchartActionComponent:OnMsgStopSetThirdCameraFOV(bIsImmediate)
	local ThirdCameraFOVLayerID = self.ThirdCameraFOVLayerID
	if ThirdCameraFOVLayerID then
		Game.CameraManager:StopSetThirdCameraFOV(ThirdCameraFOVLayerID, bIsImmediate)
	end
end

function FlowchartActionComponent:OnMsgSetThirdCameraZoom(Value, BlendInTime, Duration, BlendOutTime)
	self.ThirdCameraZoomLayerID = Game.CameraManager:SetThirdCameraZoom(Value, BlendInTime, Duration, BlendOutTime)
end

function FlowchartActionComponent:OnMsgStopSetThirdCameraZoom(bIsImmediate)
	local ThirdCameraZoomLayerID = self.ThirdCameraZoomLayerID
	if ThirdCameraZoomLayerID then
		Game.CameraManager:StopSetThirdCameraZoom(ThirdCameraZoomLayerID, bIsImmediate)
	end
end

function FlowchartActionComponent:OnMsgManualSetThirdCameraZoomSetting(MinValue, MaxValue, ZoomValue)
	Game.CameraManager:ManualSetThirdCameraZoomSetting(MinValue, MaxValue, ZoomValue)
end

function FlowchartActionComponent:OnMsgRemoveThirdCameraZoomSetting()
	Game.CameraManager:ManualSetThirdCameraZoomSetting()
end

function FlowchartActionComponent:OnMsgEnableBattleCameraLock(bEnable)
	Game.CameraManager:EnableBattleCameraLock(bEnable)
end

function FlowchartActionComponent:OnMsgSetThirdCameraViewOffsetSetting(X, Y, BlendTime)
	Game.CameraManager:SetThirdCameraViewOffsetSetting(X, Y, BlendTime)
end

function FlowchartActionComponent:OnMsgRemoveThirdCameraViewOffsetSetting(BlendTime)
	Game.CameraManager:RemoveThirdCameraViewOffsetSetting(BlendTime)
end

-----------------------------------------------------------------------------------------------------------------------
function FlowchartActionComponent:OnMsgPlayScreenEffect(EffectID, Duration, OffetX, OffetY, DelayTime, UILayer)
    Game.ScreenEffectSystem:SetScreenEffect(EffectID, Duration, OffetX, OffetY, DelayTime, UILayer)
end

function FlowchartActionComponent:OnMsgRemoveScreenEffect(EffectID)
    Game.ScreenEffectSystem:RemoveScreenEffect(EffectID)
end

function FlowchartActionComponent:OnMsgStartSequenceRecipe(RecipeId)
    -- Game.SequenceSystem:OnMsgStartSequenceRecipe(RecipeId)
end

function FlowchartActionComponent:OnMsgPlayBlackBG(Desc, Delay, SkipDelay)
    Game.NPCSystem:OnMsgPlayBlackBG(Desc, Delay, SkipDelay)
end

function FlowchartActionComponent:OnMsgSetActorVisibility(bVisible, DissolveID)
	self:DebugFmt("OnMsgSetActorVisibility %s, %s", bVisible, DissolveID)

    -- DissolveID配置为0的情况下, 直接做显示隐藏, 否则通用对应溶解效果做显示隐藏
    if DissolveID == 0 then
        if bVisible then
            self:SetActorVisible(Enum.EInVisibleReasons.ServerControl)
        else
            self:SetActorInVisible(Enum.EInVisibleReasons.ServerControl)
        end
    else
        if bVisible then
            self:SetVisibilityByDissolveDataEffect(DissolveID, Enum.EInVisibleReasons.ServerControl)
        else
            self:SetVisibilityByDissolveDataEffect(DissolveID, Enum.EInVisibleReasons.ServerControl)
        end
    end
end


function FlowchartActionComponent:OnMsgPlayPencilDrawing(bEnable)
	if bEnable then
		local PPID = Game.PostProcessManager:EnableSketch(Enum.EPostProcessLayers.World, 0, 0,-1, 0 )
        Game.PostProcessManager:SetPostProcessDestroyWhenLeaveSpace(PPID)
	else
		Game.PostProcessManager:DisableSketch()
	end

end

function FlowchartActionComponent:OnMsgSetSpecialEffectInPencilDrawing(InsIDs,EffectType)
	for K,InsID in pairs(InsIDs) do
		--local TargetEntityID = Game.WorldManager:GetNpcByInstance(InsID)
		--if not TargetEntityID then
		--	local SceneEntity  = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(InsID)
		--	if SceneEntity then
		--		TargetEntityID = SceneEntity.eid
		--	end
		--end

        if EffectType == PostProcessConst.PP_SKETCH_EFFECT_TYPE_EXCLUDE_TARGET or EffectType == PostProcessConst.PP_SKETCH_EFFECT_TYPE_PAINT_RED then
            Game.PostProcessManager:SetSketchCustomDepth(true, nil, InsID, EffectType)
		else
			Game.PostProcessManager:SetSketchCustomDepth(false,nil,InsID)
		end
	end
end



function FlowchartActionComponent:OnMsgPlayPostEffectClip(bEnable, BlendIn, BlendOut, Color)
	if bEnable then
		local PPID = Game.PostProcessManager:EnableClip(Enum.EPostProcessLayers.World, BlendIn, BlendOut, -1, 0, Color)
        Game.PostProcessManager:SetPostProcessDestroyWhenLeaveSpace(PPID)
	else
		Game.PostProcessManager:DisableClip()
	end
end


function FlowchartActionComponent:OnMsgSetPostEffectClip(Eids,bUseMask,Color,bClipNiagara)
	if Color then
		Game.PostProcessManager:SetClipColor(bUseMask,Color,0)
	end
    
	for K,eid in pairs(Eids) do
		Game.PostProcessManager:SetClipCustomDepth(true,eid,nil,1,bClipNiagara)
	end
end

function FlowchartActionComponent:OnMsgSetPostEffectClipInstanceID(InsIDs,bUseMask,Color,bClipNiagara)
	if Color then
		Game.PostProcessManager:SetClipColor(bUseMask,Color,0)
	end
	
	for K,InsID in pairs(InsIDs) do
		Game.PostProcessManager:SetClipCustomDepth(true,nil,InsID,1, bClipNiagara)
	end
end


function FlowchartActionComponent:OnMsgPlayTemplateSequence(SeqID,InLocation, InRotationYaw, bSetCameraToBoundActor)
	local Location = FVector(InLocation.X,InLocation.Y,InLocation.Z)
	local Rotation = FRotator(0,InRotationYaw,0)
	self.PlayingSeqID = self.PlayingSeqID or {}
	local TemplateSeqData =Game.TableData.GetTemplateSequenceDataRow(SeqID)
	if TemplateSeqData and self.SeqID == nil  then

		if 	self.TSHidePlayerTimer then
			Game.TimerManager:StopTimerAndKill(	self.TSHidePlayerTimer)
			self.TSHidePlayerTimer = nil
		end
		
		if TemplateSeqData.HidePlayerTime > 0 and Game.me then
			Game.me:SetActorInVisible(Enum.EInVisibleReasons.FlowChartTemplateSeq)
			Game.me:DisableLocoAll(Enum.ELocoControlTag.FlowChartTemplateSeq, true)
		end
		
		local PlayID = Game.CinematicManager:StartPlayCinematic(
			{
				CinematicType = Enum.CinematicType.TemplateSequence,
				SequenceName = TemplateSeqData.AssetPath,
				SpawnTrans = FTransform(Rotation:ToQuat(),Location),
				OnStartCallBack = function(ID, SeqActor, SequenceBoundActor)
					self:OnSeqStartPlay(ID, SeqActor, SequenceBoundActor,bSetCameraToBoundActor and self.eid == Game.me.eid and not self.TemplateSeqGameControlCameraEnabled ,TemplateSeqData.HidePlayerTime)
				end,
				OnFinishedCallBack = function(ID)
					self:OnSequenceRealFinish(ID)
				end
			}
		)
		self.PlayingSeqID[PlayID] = true
	end

end

-- 这里是真正的Sequence结束
function FlowchartActionComponent:OnSequenceRealFinish(SeqID)
	--if self.carriageAudioPlayingID ~= 0 then
	--	--Game.AkAudioManager:StopEvent(self.carriageAudioPlayingID)
	--	self.carriageAudioPlayingID = 0
	--end
	if 	self.TemplateSeqGameControlCameraEnabled and SeqID == self.TemplateSeqGameControlCameraSeqID  then
		Game.CameraManager:EnableGameControlCamera(false)
		self.TemplateSeqGameControlCameraEnabled = false
		self.TemplateSeqGameControlCameraSeqID = nil
	end
	self.PlayingSeqID[SeqID] = nil
end

function FlowchartActionComponent:OnSeqStartPlay(ID, SeqActor, SequenceBoundActor,bSetCameraToBoundActor,ShowPlayerTime)
	if ShowPlayerTime>0 then
		if 	self.TSHidePlayerTimer then
			Game.TimerManager:StopTimerAndKill(	self.TSHidePlayerTimer)
			self.TSHidePlayerTimer = nil
		end
		self.TSHidePlayerTimer = Game.TimerManager:CreateTimerAndStart(
			function()
				if Game.me then
					Game.me:SetActorVisible(Enum.EInVisibleReasons.FlowChartTemplateSeq)
					Game.me:DisableLocoAll(Enum.ELocoControlTag.FlowChartTemplateSeq, false)
				end
			end
			,ShowPlayerTime * 1000,1
		)
	end

	
	if bSetCameraToBoundActor then
		local Comps = SequenceBoundActor:K2_GetComponentsByClass(import("ChildActorComponent"))
		local CamName
		for _, C in pairs(Comps) do
			if IsValid_L(C.ChildActor) and C.ChildActor:IsA(import("BaseCamera")) then
				CamName = C.ChildActor:GetName()
			end
		end
		if CamName then
			local exParams = {
				BlendTime = 0,
				CameraInsName = CamName
			}
			Game.CameraManager:EnableGameControlCamera(true, exParams)
		end
		self.TemplateSeqGameControlCameraEnabled = true
		self.TemplateSeqGameControlCameraSeqID = ID
	end

end

---@param questID @任务ID
---@param mainIndex @主任务目标
function FlowchartActionComponent:OnMsgChangeTaskStepDescription(questID, mainIndex)
    return Game.QuestSystem:OnMsgChangeTaskStepDescription(questID, mainIndex)
end



function FlowchartActionComponent:OnMsgPlayMultiBlackBG(blackInfo, canSkip, canSkipTime)
	--UI.ShowUI("P_BlackContent", blackInfo, canSkipTime)
	-- 这个接口已经废弃了，走下面的PlayMultiBlackScreenSubtitles
end

----------------------无镜动画（由任务模块托管播放）----------------------
function FlowchartActionComponent:OnMsgPlayNoCameraDialog(DialogID, IntervalTime, PlayCount)
    Game.QuestSystem:Action_StartNoCameraDialogue({
        DialogID = DialogID,
        PlayInterval = IntervalTime,
        PlayTimes = PlayCount
    }, 0)
end

function FlowchartActionComponent:OnMsgPlayNoCameraDialogByNpc(DialogID, IntervalTime, PlayCount, InstanceID)
    Game.QuestSystem:Action_StartNoCameraDialogueByNpc({
        DialogID = DialogID,
        PlayInterval = IntervalTime,
        PlayTimes = PlayCount,
        InstanceID = InstanceID
    }, 0)
end

function FlowchartActionComponent:OnMsgPlayNoCameraDialogByPosition(DialogID, IntervalTime, PlayCount, Position, Direction)
    Game.QuestSystem:Action_StartNoCameraDialogueByPos({
        DialogID = DialogID,
        PlayInterval = IntervalTime,
        PlayTimes = PlayCount,
        Position = Position,
        Rotation = Direction,
    }, 0)
end

function FlowchartActionComponent:OnMsgStopNoCameraDialog(DialogIDs)
    Game.QuestSystem:Action_StopNoCameraDialogue({DialogueIDList = DialogIDs}, 0)
end
-------------------------------------------------------------------------
function FlowchartActionComponent:OnMsgCameraAdditionOpen(AddOnID, BlendInTime)
	Log.Error("该接口即将删除 切勿使用! @zhaojunjie")
end

function FlowchartActionComponent:OnMsgCameraAdditionClose(AddOnID, BlendOutTime)
	Log.Error("该接口即将删除 切勿使用! @zhaojunjie")
end

--无缝动画
function FlowchartActionComponent:OnMsgPlayNoCutDialog(DialogID, StartTransitTime, BeNoCutEnd, EndTransitTime, 
                                                       BeEndHoming, ArmRotation, ArmLen)
    Game.me:OnPlayNoCutDialog(DialogID)
end

---服务器通知停止gossip
function FlowchartActionComponent:OnMsgStopGossip()
	Game.GossipSystem:StopGossip(self)
end


function FlowchartActionComponent:IsInAnimation()
    return triggerUtils.GetEntityAnimationState(self) ~= ViewControlConst.AnimationStateType.UpperOnly and triggerUtils.GetEntityAnimationState(self) ~= ViewControlConst.AnimationStateType.Idle
end


function FlowchartActionComponent:IsUpperOnlyAnimation()
    return triggerUtils.GetEntityAnimationState(self) == ViewControlConst.AnimationStateType.UpperOnly or self.HalfAnimation ~= nil
end


function FlowchartActionComponent:OnMsgShowMainStoryChapterUI(arg0_int)
	QuestUtils.OpenMainChapterStartPanel(arg0_int)
end

function FlowchartActionComponent:OnMsgStartCaneSkill(arg0_int, arg1_PVector3, arg2_float)
end

function FlowchartActionComponent:OnMsgSetCustomStencil(stencil, duration, useNiagara)
    if duration == 0 then
        self:Warning("set custom depth expect duration > 0")
        return
    end
    
    self:OverrideCustomDepth(CUSTOM_DEPTH_LOGIC_TYPE.FlowChart, true, stencil, useNiagara, true, duration)
end

function FlowchartActionComponent:OnMsgOpen2DGameplay(gameplayName, gameplayID)
	local SystemActionUtils = kg_require("Gameplay.Managers.SystemActionManager.SystemActionUtils")
	SystemActionUtils.Open2DGameplay(gameplayName, gameplayID)
end



function FlowchartActionComponent:PlaySingleBlackScreenSubtitles(Text, Duration, CanSkip, CanSkipTime, BGType)
    Log.DebugFormat("PlaySingleBlackScreenSubtitles Text:%s, Duration:%s, CanSkip:%s, CanSkipTime:%s, BGType=%s", Text, Duration, CanSkip, CanSkipTime, BGType)
	---@type BlackSingleContent
	local params = {
		Text = Text,
		Duration = Duration,
		CanSkip = CanSkip,
		CanSkipTime = CanSkipTime,
	}
	QuestUtils.OpenBlackOrWhiteBgPanel(BGType, {params}, function() self:ReqBlackScreenSubtitlesEnd() end)
end


function FlowchartActionComponent:PlayMultiBlackScreenSubtitles(SubtitleInfoList, BGType)
    Log.DebugFormat("PlayMultiBlackScreenSubtitles BGType=%s", BGType)
	QuestUtils.OpenBlackOrWhiteBgPanel(BGType, SubtitleInfoList, function() self:ReqBlackScreenSubtitlesEnd() end)
end

function FlowchartActionComponent:ReqBlackScreenSubtitlesEnd()
	Game.me.remote.ReqBlackScreenSubtitlesEnd()
end



return FlowchartActionComponent