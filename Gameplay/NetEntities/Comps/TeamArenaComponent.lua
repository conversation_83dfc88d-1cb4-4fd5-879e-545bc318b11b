
TeamArenaComponent = DefineComponent("TeamArenaComponent")

function TeamArenaComponent:ctor()

end

function TeamArenaComponent:dtor()

end

function TeamArenaComponent:OnRecvPVPBattleNotice(seqID, configID, briefList)
    Game.TeamAreanaSystem:ProcessTeamArenaNotice(seqID, configID, briefList)
end

function TeamArenaComponent:OnThumbsUpBattleNotice(seqID, configID, brief)
    Game.TeamAreanaSystem:ProcessTeamArenaThumbsUp(seqID, configID, brief)
end

function TeamArenaComponent:OnRecvPVPBattleOrder(configID, sender, brief)
    Game.TeamAreanaSystem:ProcessTeamArenaOrder(configID, sender, brief)
end

function TeamArenaComponent:SetTeamArenaSelfLoseControl()
    Game.DungeonReviveSystem:SetMainPlayerLoseControl(true)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_OUTOFCONTROLL_TIMESTAMP)
end

function TeamArenaComponent:OnRecvTeamArenaMemberDetail(memberSkillDetail)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_RECEIVE_MEMBER_SKILL_DETAIL, memberSkillDetail)
end

function TeamArenaComponent:OnRecvTeamArenaBattleStat(battleStat)
    self:Debug("----------TeamArenaComponent:OnRecvTeamArenaBattleStat----, battleStat:", battleStat)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_RECEIVE_BATTLE_STAT, battleStat)
end

-- 策划目前要求结算界面每个玩家只有一个称号
-- 对局表现的称号机制上是允许多个的，此处选一个优先级最高的来展示
function TeamArenaComponent:chooseTeamArenaCalcTitle(titleMap)
    if not titleMap or not next(titleMap) then
        return 0
    end

    local sortArray = {}
    for titleID, _ in pairs(titleMap) do
        local rowData = Game.TableData.GetTitleDataRow(titleID)
        if rowData then
            sortArray[#sortArray+1] = titleID
            titleMap[titleID] = rowData.TitlePriority
        end
    end
    table.sort(sortArray, function(a, b) return titleMap[a] < titleMap[b] end)
    return sortArray[1]
end

---比赛结果数据
function TeamArenaComponent:OnMsgTeamArenaRoundCalc(dungeonTitles, battleResult, autoMatchCountDown)
    local extraInfo = {}
    for _, teamResult in pairs(battleResult) do
        for _, member in ipairs(teamResult.Members) do
            if self.eid == member.ID then
                extraInfo["gainPoints"] = member.GainPoints
                extraInfo["preRankId"] = member.PreRankID
                extraInfo["preRankPoints"] = member.PreRankPoints
                extraInfo["preProtected"] = member.PreProtected
                extraInfo["gainProtected"] = member.GainProtected
            end
			member.Kill = member.KillNum
			member.Assist = member.AssistNum
            local entID = member.ID
            -- 目前只显示一个称号
            member.TitleID = self:chooseTeamArenaCalcTitle(dungeonTitles[entID])
        end
    end
	Game.PVPSystem:ProcessPostPVP(battleResult, extraInfo, autoMatchCountDown)
end

-- 客户端重连数据
function TeamArenaComponent:OnMsgSyncTeamPVPInfo(team33RewardStatus, team55RewardStatus, team1212RewardStatus)
    Game.PVPSystem:SyncTeamPVPRewardStatus(team33RewardStatus, team55RewardStatus, team1212RewardStatus)
    self:Debug("OnMsgSyncTeamPVPInfo")
end

--获取3v3赛季信息 服务器回调 
function TeamArenaComponent:RetQuery3v3SeasonStats(code, result, likeNum)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_PVP_SEASON_STATS, result, likeNum, Enum.EMatchTypeData.MATCH_TYPE_3V3)
end

--获取5v5赛季信息 服务器回调 
function TeamArenaComponent:RetQuery5v5SeasonStats(code, result, likeNum)
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_PVP_SEASON_STATS, result, likeNum, Enum.EMatchTypeData.MATCH_TYPE_5V5)
end

--获取12v12赛季信息 服务器回调 
function TeamArenaComponent:RetQuery12v12SeasonStats(code, result, likeNum)
	Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_PVP_SEASON_STATS, result, likeNum, Enum.EMatchTypeData.MATCH_TYPE_12V12)
end

---查询获取赛季信息3v3
function TeamArenaComponent:ReqQuery3v3SeasonStats(seasonId)
    self.remote.ReqQuery3v3SeasonStats(seasonId)
end

---查询获取赛季信息5v5
function TeamArenaComponent:ReqQuery5v5SeasonStats(seasonId)
    self.remote.ReqQuery5v5SeasonStats(seasonId)
end

---查询获取赛季信息12v12
function TeamArenaComponent:ReqQuery12v12SeasonStats(seasonId)
	self.remote.ReqQuery12v12SeasonStats(seasonId)
end

---单局详细对局信息
function TeamArenaComponent:ReqQuery3v3DetailBattleRecord(gameId)
    self.remote.ReqQuery3v3DetailBattleRecord(gameId)
end

function TeamArenaComponent:ReqQuery5v5DetailBattleRecord(gameId)
    self.remote.ReqQuery3v3DetailBattleRecord(gameId)
end

function TeamArenaComponent:ReqQuery12v12DetailBattleRecord(gameId)
	self.remote.ReqQuery12v12DetailBattleRecord(gameId)
end

---全单局详细对局信息 服务器回调 33
function TeamArenaComponent:RetQuery3v3DetailBattleRecord(code, gameId, result)
	result.GameId = gameId
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_SINGLE_GAME_STATS, result)
end

---全单局详细对局信息 服务器回调 55 
function TeamArenaComponent:RetQuery5v5DetailBattleRecord(code, gameId, result)
	result.GameId = gameId
	Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_SINGLE_GAME_STATS, result)
end

---全单局详细对局信息 服务器回调 1212
function TeamArenaComponent:RetQuery12v12DetailBattleRecord(code, gameId, result)
	result.GameId = gameId
	Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_SINGLE_GAME_STATS, result)
end

---查询3v3个人全部对局记录
function TeamArenaComponent:ReqQueryAll3v3BattleRecords()
    self.remote.ReqQueryAll3v3BattleRecords()
end

---查询55个人全部对局记录
function TeamArenaComponent:ReqQueryAll5v5BattleRecords()
    self.remote.ReqQueryAll5v5BattleRecords()
end

---查询1212个人全部对局记录
function TeamArenaComponent:ReqQueryAll12v12BattleRecords()
	self.remote.ReqQueryAll12v12BattleRecords()
end

---1212全部对局记录 服务器回调
function TeamArenaComponent:RetQueryAll12v12BattleRecords(code,result)
	Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_ALL_GAME_STATS, result)
end

---55全部对局记录 服务器回调
function TeamArenaComponent:RetQueryAll5v5BattleRecords(code,result) 
    Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_ALL_GAME_STATS, result)
end

---全部对局记录信息 服务器回调
function TeamArenaComponent:RetQueryAll3v3BattleRecords(code, result)
   Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_ALL_GAME_STATS, result)
end

---deprecated
---点赞12v12对局玩家的信息
function TeamArenaComponent:ReqLikeTeam12v12BattleResult(eid)
end

---deprecated
---点赞5v5对局玩家的信息
function TeamArenaComponent:ReqLikeTeam5v5BattleResult(eid)
end

---deprecated
---点赞3v3对局玩家的信息
function TeamArenaComponent:ReqLikeTeam3v3BattleResult(eid)
end

--通用 点赞他人
function TeamArenaComponent:ReqLikeTeamArenaCommonBattleResult(eid)
	self.remote.ReqLikeTeamArenaCommonBattleResult(eid)
end

function TeamArenaComponent:ReqBatchLikeTeamArenaCommonBattleResult(eidList)
	self.remote.ReqBatchLikeTeamArenaCommonBattleResult(eidList)
end

---deprecated
---3v3点赞 ret
function TeamArenaComponent:RetLikeTeam3v3BattleResult(Result, Eid, Num)
end

---deprecated
---5v5点赞 ret
function TeamArenaComponent:RetLikeTeam5v5BattleResult(Result, Eid, Num)
end

---deprecated
---12v12点赞 ret
function TeamArenaComponent:RetLikeTeam12v12BattleResult(Result, Eid, Num)
end

--PVP通用点赞他人对局记录广播
function TeamArenaComponent:OnMsgLikeTeamArenaCommonBattleResult(instiID, recvID, CurrentNum)
	Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_ON_LIKED, instiID, recvID, CurrentNum)
	if recvID == Game.me.eid then
		local Instigator = Game.EntityManager:getEntity(instiID)
		if Instigator and Instigator.Name then
			Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PVP_LIKED_BY_OTHERS, { {Instigator.Name} })
		end
	end
end

---领取奖励协议33
function TeamArenaComponent:ReqGetTeam3v3RankReward(rankId)
    self.remote.ReqGetTeam3v3RankReward(rankId)
end

---领取奖励回调33
function TeamArenaComponent:RetGetTeam3v3RankReward(code, rankId)
    Game.PVPSystem:OnTeam3v3RewardStatusChange(rankId)
    --Game.PVPSystem:OnRankRewardChange(rankId)
end

---领取奖励协议55
function TeamArenaComponent:ReqGetTeam5v5RankReward(rankId)
    self.remote.ReqGetTeam5v5RankReward(rankId)
end

---领取奖励回调55
function TeamArenaComponent:RetGetTeam5v5RankReward(code, rankId)
    Game.PVPSystem:OnTeam5v5RewardStatusChange(rankId)
	--Game.PVPSystem:OnRankRewardChange(rankId)
end

---领取奖励协议1212
function TeamArenaComponent:ReqGetTeam12v12RankReward(rankId)
	self.remote.ReqGetTeam12v12RankReward(rankId)
end

---领取奖励回调1212
function TeamArenaComponent:RetGetTeam12v12RankReward(code, rankId)
	Game.PVPSystem:OnTeam12v12RewardStatusChange(rankId)
	--Game.PVPSystem:OnRankRewardChange(rankId)
end


---领取33全部奖励
function TeamArenaComponent:ReqGetTeam3v3AllRankReward()
    self.remote.ReqGetTeam3v3AllRankReward()
end

---领取55全部奖励
function TeamArenaComponent:ReqGetTeam5v5AllRankReward()
    self.remote.ReqGetTeam5v5AllRankReward()
end

---领取1212全部奖励
function TeamArenaComponent:ReqGetTeam12v12AllRankReward()
	self.remote.ReqGetTeam12v12AllRankReward()
end

function TeamArenaComponent:RetGetTeam3v3AllRankReward(code, team33RewardStatus)
    if code == Game.ErrorCodeConst.NO_ERR then
        Game.PVPSystem:OnGetTeam3v3AllRankRewardChange(team33RewardStatus)
    end
end

function TeamArenaComponent:RetGetTeam5v5AllRankReward(code, team55RewardStatus)
    if code == Game.ErrorCodeConst.NO_ERR then
        Game.PVPSystem:OnGetTeam5v5AllRankRewardChange(team55RewardStatus)
    end
end

function TeamArenaComponent:RetGetTeam12v12AllRankReward(code, team1212RewardStatus)
	if code == Game.ErrorCodeConst.NO_ERR then
		Game.PVPSystem:OnGetTeam12v12AllRankRewardChange(team1212RewardStatus)
	end
end

--- 12V12请求对战数据回调
function TeamArenaComponent:RetGetGroupOccupyBattleInfo(groupBattleInfo)
    Game.TeamAreanaSystem:RetGetGroupOccupyBattleInfo(groupBattleInfo)
end

--- 12V12连杀数同步
function TeamArenaComponent:OnMsgGroupOccupyContinusKill(killNum)
    Game.TeamAreanaSystem:OnMsgGroupOccupyContinusKill(killNum)
end

function TeamArenaComponent:MsgOccupyShapeTriggerNotify(InstanceID, Status, Progress)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_ON_12V12_MAGIC_POINT_UPDATE, InstanceID, Status, Progress)
end

--- 6V6 局内和历史记录统计
function TeamArenaComponent:ReqGetTeamArenaBattleInfo()
	self.remote.ReqGetTeamArenaBattleInfo()
end

function TeamArenaComponent:RetGetTeamArenaBattleInfo(BattleStat)
	Game.GlobalEventSystem:Publish(EEventTypesV2.TEAM_ARENA_RECEIVE_BATTLE_STAT, BattleStat)
end

function TeamArenaComponent:RetQueryAll6v6BattleRecords(arg0_int, arg1_TEAM_PVP_BRIEF_RECORD_DICT)
end

return TeamArenaComponent

