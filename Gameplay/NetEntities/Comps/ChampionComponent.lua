---@class ChampionComponent 比武大会系统
ChampionComponent = DefineComponent("ChampionComponent")

---@class CHAMPION_TROOP_INFO 同步的玩家所在战队属性
---@field id number
---@field name string
---@field leaderID string
---@field leaderName string
---@field members table<CHAMPION_TROOP_MEMBER_INFO>
---@field signUp boolean
---@field regionID boolean

---@class CHAMPION_TROOP_MEMBER_INFO 战队成员信息
---@field rolename string
---@field sex number
---@field level number
---@field zhanLi number

--region Properties

---Handle message when a champion troop is created
---@param ChampionTroopInfo CHAMPION_TROOP_INFO
function ChampionComponent:OnMsgCreateChampionTroop(ChampionTroopInfo)
	Game.PVPSystem.model:SyncPVPChampionTroopBriefInfo(ChampionTroopInfo)
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAMPION_TROOP_CREATED)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_TROOP_CREATE)
end

---Handle message when a player joins a champion troop
---@param ChampionTroopInfo CHAMPION_TROOP_INFO
function ChampionComponent:OnMsgJoinChampionTroop(ChampionTroopInfo)
	Game.PVPSystem.model:SyncPVPChampionTroopBriefInfo(ChampionTroopInfo)
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAMPION_TROOP_SELF_JOIN, { { ChampionTroopInfo.name } })
	Game.EventSystem:Publish(EEventTypesV2.PVP_CHAMPION_TROOP_JOIN)
end

---Handle message when a player quits a champion troop
---@param ChampionTroopID number
function ChampionComponent:OnMsgQuitChampionTroop(ChampionTroopID)
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAMPION_TROOP_SELF_QUIT)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_TROOP_QUIT)
	Game.PVPSystem.model:ResetPVPChampionCache()
end

---Handle message when a player disbands a champion troop
---@param ChampionTroopID number
function ChampionComponent:OnMsgDisbandChampionTroop(ChampionTroopID)
	Game.ReminderManager:AddReminderById(Enum.EReminderTextData.CHAMPION_TROOP_DISBAND)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_TROOP_DISBAND)
	Game.PVPSystem.model:ResetPVPChampionCache()
end


---Handle message for detail info
---@param Info table
function ChampionComponent:OnMsgGetChampionTroopDetail(Info)
	Game.PVPSystem.model:SyncPVPChampionTroopBriefInfo(Info)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_TROOP_DETAIL)
end

---Handle message for brief info
---@param Info table
function ChampionComponent:OnMsgGetChampionTroopBrief(Info)
	Game.PVPSystem.model:SyncPVPChampionTroopBriefInfo(Info)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_TROOP_BRIEF)
end

---队长改变的广播
---@param TroopID number
---@param TroopID number
---@param RoleID number
function ChampionComponent:OnMsgChampionChangeTroopLeader(TroopID, RoleID)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_LEADER_TRANSFERED, RoleID)
end

---@param Info table
function ChampionComponent:RetOnChampionTroopUpdated(Info)
	Game.PVPSystem.model:SyncPVPChampionTroopBriefInfo(Info)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_TROOP_UPDATE, Info)
end

---@param Name string
function ChampionComponent:RetModifyChampionTroopName(Name)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_NAME_CHANGED, Name)
end

---获取准备场信息后的回包
---@param ZoneID number 赛区id
---@param Status number 大会阶段
---@param GroupBattleInfo table 小组赛阶段信息
---@param PlayOffBattleInfo table 淘汰赛阶段信息
function ChampionComponent:RetChampionPrepareArenaInfo(ZoneID, Status, GroupBattleInfo, PlayOffBattleInfo)
end

function ChampionComponent:RetSignUpChampionTroop()
	Game.PVPSystem.model:SyncTeamSignedUp()
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_TROOP_SIGNUP)
end

---收到入队邀请
function ChampionComponent:OnMsgChampionBeinviteJoinTroop(EID, TroopID, InviterBrief)
	Game.PopTipsSystem:OnAddAddApplyInviteListElem({
		Type = Enum.EElasticStripData.InviteToChampionTroop,
		OperName = InviterBrief.rolename,
		OperProfession = InviterBrief.school,
		OperLevel = InviterBrief.lv,
		TroopID = TroopID
	})
end

---进行邀请操作之后的回调
function ChampionComponent:RetChampionInviteJoinTroop(EID)
	return
end

---积分赛即将开始弹窗
function ChampionComponent:RetChampionDistribubeRegionFinish()
	
end

---小组赛排行榜
function ChampionComponent:RetGetChampionGroupBattleRanklist(ZoneID, RankList, PlayerRankItem)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_RANKLIST, ZoneID, RankList, PlayerRankItem)
end

---玩家小组赛信息
function ChampionComponent:RetGetChampionGroupBattlePersonalMatches(RegionID, MatchInfoMap)
	Game.PVPSystem.model.PVPChampionPlayerOwnInfo = MatchInfoMap
	Game.PVPSystem.model.PVPChampionPlayerOwnInfo.RegionID = RegionID
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_PERSONAL_MATCHES)
end

---特定赛区的小组赛信息
function ChampionComponent:RetGetChampionGroupBattleMatches(ZoneID, MatchInfoMap)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_BATTLE_MATCHES, ZoneID, MatchInfoMap)
end

---获取当前进度
function ChampionComponent:RetChampionGetProgress(CurrentStage, RegionID, Info, bSelfRegion)
	if bSelfRegion then
		Game.PVPSystem.model.PVPChampionCurrentStageInfo.PlayerRegionID = RegionID
	end
	Game.PVPSystem.model.PVPChampionCurrentStageInfo.Stage = CurrentStage
	Game.PVPSystem.model.PVPChampionCurrentStageInfo.RegionID = RegionID
	Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo = Info
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_STAGE_INFO, bSelfRegion)
end

---获取队伍成员的回包
function ChampionComponent:RetChampionTroopMemberInfo(MemberInfoMap)
	Game.PVPSystem.model:SyncMemberInfoCache(MemberInfoMap)
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_TROOP_MEMBER_INFO, MemberInfoMap)
end

---淘汰赛赛程
function ChampionComponent:RetGetEliminationBracket(RegionID, Result, CurrentInfo)
	--如果Round是0，此时还在投票助力阶段，所以获取到的数据不可用
	if Game.PVPSystem.model.PVPChampionCurrentStageInfo.StageInfo.roundIndex ~= 0 then
		Game.PVPSystem.model.PVPChampionCurrentBracketInfo.Info = CurrentInfo
		Game.PVPSystem.model.PVPChampionCurrentBracketInfo.Result = Result
		Game.PVPSystem.model:SyncEliminationRoundMatchInfo(Result, CurrentInfo)
		Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_ELIMINATION_MATCHES)
	end
end

---踢人
function ChampionComponent:RetKickChampionTroopMember(ID)
end

---移交队长
function ChampionComponent:RetChampionChangeTroopLeader()
end

function ChampionComponent:RetGetChampionAvaliableRegions(num)
	Game.PVPSystem.model.PVPChampionRegionNum = num
	Game.GlobalEventSystem:Publish(EEventTypesV2.PVP_CHAMPION_GET_REGION_NUM)
end

---晋级成功
function ChampionComponent:OnMsgChampionAdvanceResult(ID, TroopList)
	Game.PVPSystem:ShowPVPChampionAdvanceResult(ID, TroopList)
end

---投票
function ChampionComponent:RetGetEliminationSupportInfo(RegionID, SupportRankList, RemainVote, SupportedList)
	if not Game.PVPSystem.model.PVPChampionSupportInfo[RegionID] then
		Game.PVPSystem.model.PVPChampionSupportInfo[RegionID] = {}
	end
	if not Game.PVPSystem.model.PVPChampionSupportInfo[RegionID].ScoreList then
		Game.PVPSystem.model.PVPChampionSupportInfo[RegionID].ScoreList = {}
	end
	for _, Info in pairs(SupportRankList) do
		Game.PVPSystem.model.PVPChampionSupportInfo[RegionID].ScoreList[Info.id] = Info.score
	end
end

--endregion Properties

--region Methods

---获取队伍的简略以及详细信息
function ChampionComponent:ReqGetChampionTroopDetail()
	self.remote.ReqGetChampionTroopDetail()
end

function ChampionComponent:ReqGetChampionTroopBrief()
	self.remote.ReqGetChampionTroopBrief()
end

---Handle request to create a champion troop
---@param isAutoCreate boolean
---@param name string
function ChampionComponent:ReqCreateChampionTroop(isAutoCreate, name)
	self.remote.ReqCreateChampionTroop(isAutoCreate, name)
end

---Handle request to join a champion troop
---@param ChampionTroopID number
function ChampionComponent:ReqJoinChampionTroop(ChampionTroopID)
	self.remote.ReqJoinChampionTroop(ChampionTroopID)
end

---Handle request to quit a champion troop
function ChampionComponent:ReqQuitChampionTroop()
	self.remote.ReqQuitChampionTroop()
end

---Handle request to disband a champion troop
function ChampionComponent:ReqDisbandChampionTroop()
	self.remote.ReqDisbandChampionTroop()
end

---Handle request to sign up for the champion event
function ChampionComponent:ReqSignUpChampion()
	self.remote.ReqSignUpChampion()
end

---Handle request for preparation area info
function ChampionComponent:ReqChampionPrepareArenaInfo()
	self.remote.ReqChampionPrepareArenaInfo()
end

function ChampionComponent:ReqChampionModifyTroopName(Name)
	self.remote.ReqChampionModifyTroopName(Name)
end

---邀请入队
---@param EID string
function ChampionComponent:ReqChampionInviteJoinTroop(EID)
	self.remote.ReqChampionInviteJoinTroop(EID)
end

---请求队伍队员信息
function ChampionComponent:ReqChampionGetOtherTroopMemberInfo(IDList)
	self.remote.ReqChampionGetOtherTroopMemberInfo(IDList)
end

---踢人
function ChampionComponent:ReqKickChampionTroopMember(ID)
	self.remote.ReqKickChampionTroopMember(ID)
end

---移交队长
function ChampionComponent:ReqChampionChangeTroopLeader(ID)
	self.remote.ReqChampionChangeTroopLeader(ID)
end

-------赛程方法

---获取小组赛对局信息
function ChampionComponent:ReqChampionGroupBattleMatches(ZoneID, RoundIndex)
	self.remote.ReqChampionGroupBattleMatches(ZoneID, RoundIndex)
end

---获取小组赛个人战队信息（无战队不会返回）
function ChampionComponent:ReqChampionGroupBattlePersonalMatches(EID)
	self.remote.ReqChampionGroupBattlePersonalMatches(EID)
end

---小组赛排行榜
function ChampionComponent:ReqChampionGroupBattleRanklist(ZoneID)
	self.remote.ReqChampionGroupBattleRanklist(ZoneID)
end

---淘汰赛赛程
function ChampionComponent:ReqChampionGetEliminationBracket(ZoneID)
	self.remote.ReqChampionGetEliminationBracket(ZoneID)
end

---获取淘汰赛助力
function ChampionComponent:ReqGetEliminationSupportInfo(RegionID)
	self.remote.ReqGetEliminationSupportInfo(RegionID)
end

---获取当前进度
function ChampionComponent:ReqChampionGetProgress(ZoneID)
	self.remote.ReqChampionGetProgress(ZoneID)
end

---退出比赛
function ChampionComponent:ReqQuitToChampionPrepareWorld()
	self.remote.ReqQuitToChampionPrepareWorld()
end

---获取赛区数量
function ChampionComponent:ReqGetChampionAvaliableRegions()
	self.remote.ReqGetChampionAvaliableRegions()
end

-------

--endregion Methods


function ChampionComponent:RetSupportChampionTroop(arg0_CHAMPION_TROOP_ID, arg1_UINT, arg2_UINT)
end
