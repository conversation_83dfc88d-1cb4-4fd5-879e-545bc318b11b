local const = kg_require("Shared.Const")
--- @class TeamComponent
TeamComponent = DefineComponent("TeamComponent")
local StringConst = require "Data.Config.StringConst.StringConst"

function TeamComponent:__component_AfterEnterWorld__()
    if Game.me.teamID ~= 0 and Game.TeamSystem:IsTeamMember(self.eid) then
        Game.TeamSystem:RoleAfterEnterWorld(self.eid)
    end
end

function TeamComponent:__component_ExitWorld__()
    if Game.me.teamID ~= 0 and Game.TeamSystem:IsTeamMember(self.eid) then
        Game.TeamSystem:RoleExitWorld(self.eid)
    end
end

function TeamComponent:ReqGetOnlinePlayerList()
    self:Debug("TeamComponent:ReqGetOnlinePlayerList")
    self.remote:ReqGetOnlinePlayerList()
end

function TeamComponent:RetGetOnlinePlayerList(result)
    self:Debug("RetGetOnlinePlayerList", result)
end

function TeamComponent:SendTeamRecruitInfo(text, channel)
    self.remote:SendTeamRecruitInfo(text, channel)
end

function TeamComponent:ReqCreateTeam()
    self.remote:ReqCreateTeam()
end

function TeamComponent:RetCreateTeam(Reason)
    Game.GlobalEventSystem:Publish(EEventTypesV2.SERVER_RET_CREATE_TEAM, Reason)
end

function TeamComponent:RetTeamSetMemberFlag(ret)
end

function TeamComponent:RetTeamRemoveMemberFlag(ret)
end

--- @param targetAvatarActorID string
function TeamComponent:ReqInviteJoinTeam(targetAvatarActorID)
    if Game.TeamSystem:GetTeamPlayerNum() == Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_FULL)
        return
    end
    self:Debug("TeamComponent:ReqInviteJoinTeam", targetAvatarActorID)
    self.remote:ReqInviteJoinTeam(targetAvatarActorID)
end

function TeamComponent:ReqBatchInviteJoinTeam(targetAvatarActorIDs)
	if Game.TeamSystem:GetTeamPlayerNum() + #targetAvatarActorIDs > Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") or
		Game.TeamSystem:GetTeamPlayerNum() == Game.TableData.GetConstDataRow("TEAM_SIZE_LIMIT") then
		Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_FULL)
		return
	end
	self.remote:ReqBatchInviteJoinTeam(targetAvatarActorIDs)
end

function TeamComponent:RetInviteJoinTeam(Result)
    self:Debug("TeamComponent:RetInviteJoinTeam", result)
    if Result.Code == Game.NetworkManager.ErrCodes.NO_ERR then
        self:Debug("ReqInviteJoinTeam Succ")
    else
        local ErrCodes = Game.NetworkManager.ErrCodes
        if Result.Code == ErrCodes.PLAYER_STRANGER_INVITE then
            Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.TEAM_PLAYER_STRANGER_INVITE,
                { { StringConst.Get("TEAM_CANT_INVITE") } }
            )
        elseif Result.Code == ErrCodes.PLAYER_IN_TEAM then
            Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.TEAM_PLAYER_IN_TEAM,
                { { StringConst.Get("TEAM_CANT_INVITE") } }
            )
        elseif Result.Code == ErrCodes.PLAYER_IN_DUNGEON then
            Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.TEAM_PLAYER_IN_DUNGEON,
                { { StringConst.Get("TEAM_CANT_INVITE") } }
            )
        elseif Result.Code == ErrCodes.PLAYER_IN_TOWER_CLIMB then
            Game.ReminderManager:AddReminderById(
                Enum.EReminderTextData.TEAM_PLAYER_IN_DUNGEON,
                { { StringConst.Get("TEAM_CANT_INVITE") } }
            )
        end
    end
end

--- @param operate integer
--- @param captainID string
function TeamComponent:ReqHandleTeamInvite(operate, captainID)
    self:Debug("TeamComponent:ReqHandleTeamInvite", operate, captainID)
    self.remote:ReqHandleTeamInvite(operate, captainID)
end

function TeamComponent:RetHandleTeamInvite(Result)
    self:Debug("TeamComponent:RetHandleTeamInvite", Result and Result.Code)
    if Result.Code == Game.NetworkManager.ErrCodes.NO_ERR then
        if UI.GetUI("P_TeamInvitations") and #Game.me.invitedInfoList == 0 then
            UI.HideUI("P_TeamInvitations")
        end
    else
        local ErrCodes = Game.NetworkManager.ErrCodes
        if Result.Code == ErrCodes.PLAYER_IN_DUNGEON then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_PLAYER_IN_DUNGEON)
        else
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.INVAILD_INVITE_INFO)
        end
    end
end

function TeamComponent:OnMsgTeamMemberChange(Reason, SubReason, MemberInfo)
    local MemberID = MemberInfo.id
    Log.DebugFormat("[OnMsgTeamMemberChange] Reason:%s MemberID:%s" , Reason , MemberID)
    Game.TeamSystem:OnMsgTeamMemberChanged(Reason, SubReason, MemberInfo)
end

function TeamComponent:ReqLeaveTeam(needEnterLeague)
    self:Debug("[ReqLeaveTeam]")
    self.remote:ReqLeaveTeam(needEnterLeague or false)
end

function TeamComponent:RetLeaveTeam(result)
    self:Debug("[RetLeaveTeam]")
    Game.NetworkManager.HandleRequestRet("LeaveTeam", result)
end

--- @param targetID string
function TeamComponent:ReqTransferCaptain(targetID)
    self:Debug("[ReqTransferCaptain]")
    self.remote:ReqTransferCaptain(targetID)
end

function TeamComponent:RetTransferCaptain(result)
    self:Debug("[RetTransferCaptain]", result)
end

--- @param targetID string
function TeamComponent:ReqKickTeamMember(targetID)
    self.remote:ReqKickTeamMember(targetID)
end

function TeamComponent:RetKickTeamMember(Result)
    if Result.Code == Game.ErrorCodeConst.KICK_TEAM_MEMBER_IN_BATTLE then
        Game.ReminderManager:AddReminderById(
            Enum.EReminderTextData.TEAM_KICK_MEMBER_IN_BATTLE
        )
    elseif Result.Code == Game.ErrorCodeConst.NOT_IN_TEAM then
        Game.ReminderManager:AddReminderById(
            Enum.EReminderTextData.TEAM_PLAYER_NOT_IN_TEAM
        )
    end
end

function TeamComponent:ReqApplyForCaptain()
    self:Debug("TeamComponent:ReqApplyForCaptain")
    self.remote:ReqApplyForCaptain()
end

function TeamComponent:RetApplyForCaptain(Result)
    self:Debug("TeamComponent:RetApplyForCaptain", Result.Code, Result.Trace)
    if Result.Code == Game.NetworkManager.ErrCodes.NO_ERR then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_APPLYCAPTAIN_SUCCESS)
    else
        Game.TeamSystem:ApplyCaptainErr(Result)
    end
end

function TeamComponent:OnMsgMemberApplyForCaptain(MemberID)
    self:Debug("TeamComponent:OnMsgMemberApplyForCaptain", MemberID, "apply for captain")
    Game.TeamSystem:ReceiveApplyForCaptain(MemberID)
end

function TeamComponent:ReqHandleCaptainApply(newCaptainID, bAccept)
    self:Debug("TeamComponent:ReqHandleCaptainApply", newCaptainID, bAccept)
    self.remote:ReqHandleCaptainApply(newCaptainID, bAccept)
end

function TeamComponent:RetHandleCaptainApply(Result)
    self:Debug("TeamComponent:RetHandleCaptainApply", Result.Code, Result.Trace)
end

function TeamComponent:OnMsgCaptainApplyRejected()
    self:Debug("TeamComponent:OnMsgCaptainApplyRejected")
    Game.TeamSystem:OnMsgCaptainApplyRejected()
end

-- function TeamComponent:OnMsgCaptainLocationChange(Location, bIsInSameSpace)
--     self:Debug(
--         "TeamComponent:OnMsgCaptainLocationChange",
--         Location.x,
--         Location.y,
--         Location.z,
--         "bIsInSameSpace:",
--         bIsInSameSpace
--     )
-- end

function TeamComponent:ReqJoinTeam(TeamID, TargetActorID, TargetID, bFromRecruit)
    self:Debug("=========TeamComponent:ReqJoinTeam", TeamID, TargetActorID, TargetID)
    self.remote:ReqJoinTeam(TeamID, TargetActorID, TargetID, bFromRecruit or false)
end

function TeamComponent:RetJoinTeam(Result)
    self:Debug("=======TeamComponent:RetHandleJoinTeam", Result)
    local ErrCodes = Game.NetworkManager.ErrCodes
    if Result.Code == ErrCodes.TEAM_FULL then
        --队伍已满
        Game.ReminderManager:AddReminderById(
            Enum.EReminderTextData.TEAM_FULL,
            { { StringConst.Get("TEAM_START_GATHER") } }
        )
    end
end

function TeamComponent:ReqHandleJoinTeam(Operate, ApplicatorionID)
    self:Debug("TeamComponent:ReqServerJoinTeam")
    self.remote:ReqHandleJoinTeam(Operate, ApplicatorionID)
end

function TeamComponent:RetHandleJoinTeam(Result)
    self:Debug("TeamComponent:RetHandleJoinTeam")
    if Result.Code == Game.ErrorCodeConst.NO_ERR then
        if Game.TeamSystem:HasApplication() == false then
            Game.TeamSystem:ClearRedPointApplication()
            if UI.GetUI(UIPanelConfig.TeamApply_Panel) then
                UI.HideUI(UIPanelConfig.TeamApply_Panel)
            end
        end
    else
        local ErrCodes = Game.NetworkManager.ErrCodes
        if Result.Code == ErrCodes.PLAYER_IN_DUNGEON then
            --申请的玩家处于单人位面
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_APPLICATION_INVALID)
        elseif Result.Code == ErrCodes.PLAYER_IN_TEAM then
            --申请的玩家已经处于队伍
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_APPLICATION_ALREADY_IN_TEAM)
        else
            Game.NetworkManager.ShowNetWorkResultReminder("RetHandleJoinTeam", Result)
        end
    end
end

function TeamComponent:ReqCombineTeam(TeamID, TargetTeamMemberID)
    self.remote:ReqCombineTeam(TeamID, TargetTeamMemberID)
end

function TeamComponent:RetCombineTeam(Result)
    self:Debug("TeamComponent:RetCombineTeam")
    local ErrCodes = Game.NetworkManager.ErrCodes
    if Result.Code == ErrCodes.NO_ERR then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_COMBINE_SUCCESS)
    else
        Game.NetworkManager.ShowNetWorkResultReminder("RetCombineTeam",Result)
    end
end

function TeamComponent:ReqHandleCombineTeam(TeamID, CaptainID, Operate)
    self.remote:ReqHandleCombineTeam(TeamID, CaptainID, Operate)
end

function TeamComponent:RetHandleCombineTeam(Result)
    self:Debug("TeamComponent:HandleCombineTeam")
    if Result.Code == Game.ErrorCodeConst.NO_ERR then
        if Game.TeamSystem:HasApplication() == false then
            UI.HideUI(UIPanelConfig.TeamApply_Panel)
        end
    else
        local ErrCodes = Game.NetworkManager.ErrCodes
        if Result.Code == ErrCodes.PLAYER_IN_DUNGEON then
            --不在大世界中
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_NOT_IN_BIGWORLD)
        elseif Result.Code == ErrCodes.NOT_IN_TEAM then
            Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_APPLICATION_INVALID)
        else
            Game.NetworkManager.ShowNetWorkResultReminder("RetHandleCombineTeam", Result)
        end
    end
end

function TeamComponent:ReqTeamRefreshTarget(TargetID, JobList, ZhanliNeed, Description, MatchOperation)
    if JobList == nil then JobList = {} end
    self.remote:ReqTeamRefreshTarget(TargetID, JobList, ZhanliNeed, Description, MatchOperation)
end

function TeamComponent:RetTeamRefreshTarget(Result)
    self:Debug("TeamComponent:TeamRefreshTarget")
end

function TeamComponent:ReqTeamBatchJoinTeam(ListStr, groupIDs)
    self.remote:ReqTeamBatchJoinTeam(ListStr, groupIDs)
end

function TeamComponent:RetTeamBatchJoinTeam(Result, ListStr)
    self:Debug("TeamComponent:TeamBatchJoinTeam")
end

function TeamComponent:ReqTeamSearch(TargetID, bNearby)
    self:Debug("TeamComponent:ReqTeamSearch", TargetID, bNearby)
    self.remote:ReqTeamSearch(TargetID, bNearby)
end

function TeamComponent:RetTeamSearch(Result, TargetID, TeamsID, teamsIDInSeekRescue, groupIDs, groupsInSeekRescue)
    --self:Debug("TeamComponent:RetTeamSearch", TargetID, CommonUtils.tprint(TeamsID)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.GlobalEventSystem:Publish(EEventTypesV2.SERVER_ALL_TEAMLIST_UPDATE, Result, TargetID, TeamsID, teamsIDInSeekRescue,
        groupIDs,
        groupsInSeekRescue)
end

function TeamComponent:ReqTeamCollectTarget(TargetID, Opearate)
    self.remote:ReqTeamCollectTarget(TargetID, Opearate)
end

function TeamComponent:RetTeamCollectTarget(Result, TargetID)
    self:Debug("TeamComponent:TeamCollectTarget")
    if Result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.TEAM_COLLECT_SUCCESS)
    end
end

function TeamComponent:ReqTeamBatchQueryInfo(TeamsID, groupIDs)
    self.remote:ReqTeamBatchQueryInfo(TeamsID, groupIDs or {})
end

function TeamComponent:RetTeamBatchQueryInfo(Result, BatchQueryTeamInfoLIst, BatchQueryGroupInfoLIst)
    --self:Debug("TeamComponent:RetTeamBatchQueryInfo", CommonUtils.tprint(BatchQueryGroupInfoLIst)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.GlobalEventSystem:Publish(EEventTypesV2.SERVER_BATCHINFO_UPDATE, Result, BatchQueryTeamInfoLIst,
        BatchQueryGroupInfoLIst)
    -- 建议服务器吧targetid也返回，这样就不用再去查了
end

function TeamComponent:ReqTeamMatch(TargetID, Operate)
    self.remote.ReqTeamMatch(TargetID, Operate)
end

function TeamComponent:RetTeamMatch(Result)
    Game.NetworkManager.HandleRequestRet("TeamMatch", Result)
    if Result.Code == Game.NetworkManager.ErrCodes.NO_ERR then
    else
        Game.TeamSystem:MatchErr(Result)
    end
end

function TeamComponent:OnMsgTeamSingleMatchStateSync(State, TargetID, StartTime, Reason)
    Game.TeamSystem:OnMatchStateChange(State, TargetID, StartTime, Reason)
    Game.GlobalEventSystem:Publish(EEventTypesV2.SERVER_ON_SINGLE_MATCH_STATE_CHANGED, State, TargetID, StartTime, Reason)
end

function TeamComponent:OnMsgTeamMatchStateSync(State, TargetID, StartTime, Reason)
    self:Debug("======================================OnMsgTeamMatchStateSync", State, TargetID, StartTime, Reason)
    Game.TeamSystem:OnMatchStateChange(State, TargetID, StartTime, Reason)
    Game.GlobalEventSystem:Publish(EEventTypesV2.SERVER_ON_TEAM_MATCH_STATE_CHANGED, State, TargetID, StartTime, Reason)
end

--刷新邀请信息
function TeamComponent:ReqTeamRefreshInviteInfo()
    self.remote.ReqTeamRefreshInviteInfo()
end

--名片信息
function TeamComponent:ReqOtherRoleOnlineBrief(EntityID)
    self.remote.ReqOtherRoleOnlineBrief(EntityID)
end

function TeamComponent:RetOtherRoleOnlineBrief(Attributes)
    self:Log("TeamComponent:RetOtherRoleOnlineBrief")
    local guildRoleIds = Attributes.guildRoleIds
    local guildRoleId = guildRoleIds and guildRoleIds[const.GUILD_ROLE_TYPE.COMMON_ROLE] or nil

    Game.TeamSystem:ShowPlayerCardUI(
        {
            EntityID = Attributes.id,
            Name = Attributes.name,
            ProfessionID = Attributes.profession,
            Sex = Attributes.sex,
            Level = Attributes.level,
            TeamID = Attributes.teamID,
            IsCaptain = Attributes.isCaptain,
            CE = Attributes.zhanLi,
            GuildID = Attributes.guildId,
            GuildStatus = Attributes.guildStatus,
            GuildName = Attributes.guildName,
            GroupID = Attributes.groupID,
            GuildRoleID = guildRoleId,
            customFaceData = Attributes.customFaceData,
            fashionData = Attributes.fashionData,
            pvpData = Attributes.pvpData,
            LineType = Attributes.lineType,
            MapType = Attributes.mapType,
            HomelandUnlockType = Attributes.homelandUnlockType,
        }
    )
end

function TeamComponent:OnMsgTeamInviteApplyCombineInfoSync(TeamInfo)
    self:Debug("====================================OnMsgTeamInviteApplyCombineInfoSync")
    Game.TeamSystem:OnInviteApplyCombineUpdate(TeamInfo.InviteInfo or {}, TeamInfo.ApplicatorInfo or {},
        TeamInfo.CombineInfo or {})
end

function TeamComponent:ReqTeamVoiceStateReport(voiceState)
    self.remote.ReqTeamVoiceStateReport(voiceState)
end

function TeamComponent:ReqTeamBlockVoiceReport(targetID, bBlock)
    self.remote.ReqTeamBlockVoiceReport(targetID, bBlock)
end

function TeamComponent:ReqQueryPlayerFrequentInfo(IDList, bIsOnline)
    self.remote:ReqQueryPlayerFrequentInfo(IDList, bIsOnline)
end

function TeamComponent:RetQueryPlayerFrequentInfo(Result, Info)
    if Result.Code == 0 then
        UI.Invoke(UIPanelConfig.TeamInvite_Panel, "AddTeamInfo", Info)
    end
end

function TeamComponent:ReqSetJoinTeamDescription(Description)
    self.remote:ReqSetJoinTeamDescription(Description)
end

function TeamComponent:ReqTeamSetSpaceFlag(Index, Pos)
    self.remote:ReqTeamSetSpaceFlag(Index, Pos)
end

function TeamComponent:RetTeamSetSpaceFlag(Result)
    self:Debug("========================RetTeamSetSpaceFlagResult", Result)
end

function TeamComponent:ReqTeamRemoveSpaceFlag(Index)
    self.remote:ReqTeamRemoveSpaceFlag(Index)
end

function TeamComponent:ReqTeamSetIsAutoAcceptJoin(isAutoAgree)
    self.remote:ReqTeamSetIsAutoAcceptJoin(isAutoAgree)
end

function TeamComponent:OnMsgTeamSpaceFlagSync(AvatarActorSpaceFlag)
    --上线同步场景标记
    Game.TeamSystem:OnSceneMarkAllSync(AvatarActorSpaceFlag)
end

function TeamComponent:OnMsgTeamSpaceFlagChange(Index, SpaceFlagInfo)
    --同步场景标记
    Game.TeamSystem:OnSceneMarkPartChanged(Index, SpaceFlagInfo)
end

function TeamComponent:ReqTeamSetMemberFlag(Index, SelPlayerEID)
    self.remote:ReqTeamSetMemberFlag(Index, SelPlayerEID)
end

function TeamComponent:ReqTeamRemoveMemberFlag(Index, SelPlayerEID)
    self.remote:ReqTeamRemoveMemberFlag(Index, SelPlayerEID)
end

function TeamComponent:ReqTeamSeekRescue()
    self.remote:ReqTeamSeekRescue()
end

function TeamComponent:RetTeamSeekRescue(Result)
end

function TeamComponent:OnMsgTeamDescriptionSync(Descript)
    Game.TeamSystem:SetTeamDiscript(Descript)
end

function TeamComponent:ReqSupportTeam(teamID, targetActorID, targetID, bFromRecruit)
    self.remote:ReqSupportTeam(teamID, targetActorID, targetID, bFromRecruit)
end

function TeamComponent:ReqPreJoinTeam(targetActorID)
    self.remote:ReqPreJoinTeam(targetActorID)
end

function TeamComponent:set_teamID(entity, new, old)
    if  entity.eid == Game.me.eid then
        Game.TeamSystem:OnTeamIDChange(new)
        Game.ChatSystem:OnTeamStateChange()
        Game.DungeonBattleStatisticsSystem:OnTeamStateChange()
        Game.VoiceSystem:OnTeamStateChange()
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TEAMID_CHANGED, entity.eid, "teamID", new, old)
    end
    
    if self.SendFightModeMessage then
        self:SendFightModeMessage("teamID")
    end

    -- BSFunc.ReceiveCharacterCampInfoChanged(self.eid)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_CAMP_CHANGED, self.eid, self, "teamID", new, old)
	Game.UniqEventSystemMgr:Publish(entity:uid(), EEventTypesV2.ON_CAMP_CHANGED, entity:uid(), "teamID", new, old)

	-- 可能阵营变化, 需要更新character type缓存, 出于性能考虑, 暂时不用事件系统处理, 后续新增阵营变化事件这里需要关注下
	if self.bNeedHandleCampChange then
		self:HandleCampChange()
	end
end

function TeamComponent:set_TeamCamp(entity, new, old)
    -- BSFunc.ReceiveCharacterCampInfoChanged(self.eid)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_CAMP_CHANGED, self.eid, self, "TeamCamp", new, old)
	Game.UniqEventSystemMgr:Publish(entity:uid(), EEventTypesV2.ON_CAMP_CHANGED, entity:uid(), "TeamCamp", new, old)

	-- 可能阵营变化, 需要更新character type缓存, 出于性能考虑, 暂时不用事件系统处理, 后续新增阵营变化事件这里需要关注下
	if self.bNeedHandleCampChange then
		self:HandleCampChange()
	end
end

function TeamComponent:set_isInTeamMatch(entity, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TEAMMATCH_CHANGED, entity.eid, "isInTeamMatch",
        new, old)
end

function TeamComponent:set_isInSingleMatch(entity, new, old)
end

function TeamComponent:set_FollowState(entity, new, old)
    if Game.me.teamID ~= 0 then
        Game.TeamSystem:OnSelfFollowStateChanged()
    end

    if Game.GroupSystem:IsInGroup() then
        Game.GroupSystem:OnSelfPropChanged("FollowState", new, old)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FOLLOWSTATE_CHANGED, entity.eid, "FollowState", new, old)
end

function TeamComponent:set_isCaptain(entity, new, old)
    Game.TeamSystem:OnCaptainStateChange()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_CAPTAIN_CHANGED, entity.eid, "isCaptain",
        new, old)
end

function TeamComponent:set_teamTargetID(entity, new, old)
    Game.TeamSystem:OnTeamTargetChange()
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAM_TARGETID_CHANGED, entity.eid,
        "teamTargetID", new, old)
end

function TeamComponent:set_invitedInfoList(entity, new, old)
    if entity.eid == GetMainPlayerEID() then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_INVITEDINFOLIST_CHANGED)
    end
end

AvatarActorInvitedInfoList = DefineClass("AvatarActorInvitedInfoList")

function AvatarActorInvitedInfoList:on_insert(entity, index, value)
    Game.EventSystem:PublishBehavior(EEventTypesV2.ON_SELF_INVITEDINFOLIST_CHANGED, GetMainPlayerEID())
end

function AvatarActorInvitedInfoList:on_del(entity, index, value)
    Game.EventSystem:PublishBehavior(EEventTypesV2.ON_SELF_INVITEDINFOLIST_CHANGED, GetMainPlayerEID())
end

AvatarActorTeamInfoList = DefineClass("AvatarActorTeamInfoList")

function AvatarActorTeamInfoList:set_attr(entity, key, new, old)
    if entity.eid == Game.me.eid then
        Game.TeamSystem:OnTeamInfoListChange()
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMINFOLIST_CHANGED)
    end
end

function TeamComponent:set_teamInfoList(entity, new, old)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMINFOLIST_CHANGED)
    end
end

AvatarActorTeamInfo = DefineClass("AvatarActorTeamInfo")

function AvatarActorTeamInfo:set_name(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_NAME_CHANGED, GetMainPlayerEID(), "teamInfoList.name", self.id, new, old)
end

function AvatarActorTeamInfo:set_profession(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_PROFESSION_CHANGED, GetMainPlayerEID(), "teamInfoList.profession", self.id, new, old)
end

function AvatarActorTeamInfo:set_professionStateID(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_PROFESSIONSTATEID_CHANGED, GetMainPlayerEID(), "teamInfoList.professionStateID", self.id, new, old)
end

function AvatarActorTeamInfo:set_level(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_LEVEL_CHANGED, GetMainPlayerEID(), "teamInfoList.level", self.id, new, old)
end

function AvatarActorTeamInfo:set_isCaptain(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_ISCAPTAIN_CHANGED, GetMainPlayerEID(), "teamInfoList.isCaptain", self.id, new, old)
end

function AvatarActorTeamInfo:set_isOnline(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_ISONLINE_CHANGED, GetMainPlayerEID(),"teamInfoList.isOnline", self.id, new, old)
end

function AvatarActorTeamInfo:set_hp(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_HP_CHANGED, GetMainPlayerEID(), "teamInfoList.hp", self.id, new, old)
end

function AvatarActorTeamInfo:set_maxHp(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_MAXHP_CHANGED, GetMainPlayerEID(),"teamInfoList.maxHp", self.id, new, old)
end

function AvatarActorTeamInfo:set_isDead(entity, new, old)
    if new ~= old then
        Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_ISDEAD_CHANGED, GetMainPlayerEID(),"teamInfoList.isDead", self.id, new, old)
    end
end

function AvatarActorTeamInfo:set_bFollowing(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_BFOLLOWING_CHANGED, GetMainPlayerEID(), "teamInfoList.bFollowing", self.id, new, old)
end

function AvatarActorTeamInfo:set_bTargetByBoss(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_BFOLLOWING_CHANGED, GetMainPlayerEID(), "teamInfoList.bTargetByBoss", self.id, new, old)
end

function AvatarActorTeamInfo:set_location(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_LOCATION_CHANGED, GetMainPlayerEID(), "teamInfoList.location", self.id, new, old)
end

function AvatarActorTeamInfo:set_mapInstID(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_MAPINSTID_CHANGED, GetMainPlayerEID(), "teamInfoList.mapInstID", self.id, new, old)
end

function AvatarActorTeamInfo:set_voiceState(entity, new, old)
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_VOICESTATE_CHANGED, GetMainPlayerEID(), "teamInfoList.voiceState", self.id, new, old)
end

function AvatarActorTeamInfo:set_worldID(entity, new, old)
	entity:Debug("===========================map 分线变更",new,old)
    if self.id == Game.TeamSystem:GetCaptainInfo().id and GetMainPlayerPropertySafely("FollowState") ~= Enum.EFollowState.STOP_FOLLOW then
        Game.me:StopFollow()
    end
    Game.EventSystem:PublishBehavior(_G.EEventTypes.ON_TEAMMEMBER_MAPORLINE_CHANGED, GetMainPlayerEID(), "teamInfoList.worldID", self.id, new, old)
end

function AvatarActorTeamInfo:set_memberFlag(entity, new, old)
    if Game.TeamSystem:IsCaptain() and new and new ~= 0 then
        local name = Game.me.teamInfoList[self.id].name
        local Text = Game.me.Name.."将"..name.."标记为"..string.format("<img id=\"%s\"/>", "MemberMark"..new)
        Game.ChatSystem:AddChannelSystemInfoByServer(Text, Enum.EChatChannelData.TEAM)
    end
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TEAMMEMBER_MEMBERFLAG_CHANGED, "teamInfoList.memberFlag", self.id, new, old)
end

AvatarActorTeamApplicatorList = DefineClass("AvatarActorTeamApplicatorList")

function AvatarActorTeamApplicatorList:set_attr(entity, key, new, old)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMAPPLICATORLIST_CHANGED)
    end
end

function TeamComponent:set_teamApplicatorList(entity, new, old)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMAPPLICATORLIST_CHANGED)
    end
end

AvatarActorTeamCombineList = DefineClass("AvatarActorTeamCombineList")
function AvatarActorTeamCombineList:set_attr(entity, key, new, old)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMCOMBINELIST_CHANGED)
    end
end

function TeamComponent:set_teamCombineList(entity, new, old)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMCOMBINELIST_CHANGED)
    end
end

AvatarActorTeamCollectList = DefineClass("AvatarActorTeamCollectList")
function AvatarActorTeamCollectList:on_insert(entity, index, value)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMCOLLECTLIST_CHANGED)
    end
end

function AvatarActorTeamCollectList:on_del(entity, index, value)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMCOLLECTLIST_CHANGED)
    end
end

function TeamComponent:set_teamCollectList(entity, new, old)
    if entity.eid == Game.me.eid then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_TEAMCOLLECTLIST_CHANGED)
    end
end

AvatarActorSingleMatchInfoList = DefineClass("AvatarActorSingleMatchInfoList")

function AvatarActorSingleMatchInfoList:on_insert(entity, index, value)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_MATCHLIST_CHANGED)
end

function AvatarActorSingleMatchInfoList:on_del(entity, index, value)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_MATCHLIST_CHANGED)
end

function TeamComponent:set_singleMatchInfoList(entity, new, old)
    Game.GlobalEventSystem:Publish(EEventTypesV2.ON_SELF_MATCHLIST_CHANGED)
end

AvatarActorTeamPositionNeedList = DefineClass("AvatarActorTeamPositionNeedList")

function AvatarActorTeamPositionNeedList:on_insert(entity, index, value)
end

function AvatarActorTeamPositionNeedList:on_del(entity, index, value)
end

function TeamComponent:set_teamPositionNeedList(entity, new, old)
end

function TeamComponent:set_teamZhanliLimit(entity, new, old)
end

function TeamComponent:set_isAutoAcceptJoin(entity, new, old)
    if Game.TeamSystem:IsCaptain() then
        Game.GlobalEventSystem:Publish(EEventTypesV2.ON_TEAM_AUTO_ACCEPT_JOIN_CHANGED, entity.eid,
                "isAutoAcceptJoin", new, old)
    end
end

AvatarActorTeamBlockVoices = DefineClass("AvatarActorTeamBlockVoices")

function AvatarActorTeamBlockVoices:set_attr(entity, eid, newValue)
    if newValue == true then
        Game.VoiceSystem:AddAudioBlackList(eid, Enum.EVOICE_CHANNEL.WORLD)
    else 
        Game.VoiceSystem:RemoveAudioBlackList(eid, Enum.EVOICE_CHANNEL.WORLD)
    end
    if Game.me.teamID ~= 0 then
        Game.TeamSystem:UpdateTeamBlockVoices(eid, newValue)
    end
end

function TeamComponent:RetSetJoinTeamDescription(arg0_Result)
end

function TeamComponent:RetPreJoinTeam(arg0_Result)
end

function TeamComponent:OnMsgJoinTeamResult(arg0_TEAM_ID, arg1_bool)
end

function TeamComponent:RetTeamRemoveSpaceFlag(arg0_Result)
end

function TeamComponent:RetTeamSetIsAutoAcceptJoin(arg0_Result)
end

function TeamComponent:OnMsgTeamPreApplyInfoSync(arg0_TeamApplyInfo)
end

return TeamComponent
