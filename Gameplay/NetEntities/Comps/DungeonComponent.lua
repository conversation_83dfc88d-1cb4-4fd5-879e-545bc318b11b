--- @class DungeonComponent : AvatarActor
DungeonComponent = DefineComponent("DungeonComponent")

---@param MsgType number
---@param DungeonTemplateID number
---@param MemberID number
---@param StartTime number
function DungeonComponent:OnMsgDungeonReadinessCheck(MsgType, DungeonTemplateID, MemberID, StartTime)
	Game.DungeonSystem:OnMsgDungeonReadinessCheck(MsgType, DungeonTemplateID, MemberID, StartTime)
end

--- @param dungeonEntityID string
-- function DungeonComponent:ReqReturnDungeon(dungeonEntityID)
--     self.remote.ReqReturnDungeon(dungeonEntityID)
-- end
function DungeonComponent:ReturnDungeon(result)
    if Result.Code ~= Game.ErrorCodeConst.NO_ERR then
        if Result.Code == Game.NetworkManager.ErrCodes.ILLEGAL_PLAYER_NUM then
            --人数不符
            Game.ReminderManager:AddReminderById(
                    Enum.EReminderTextData.DUNGEON_NUMBER_NOT_MATCH)
        elseif Result.Code == Game.NetworkManager.ErrCodes.ILLEGAL_LEVEL then
            --等级不符
            Game.ReminderManager:AddReminderById(
                    Enum.EReminderTextData.DUNGEON_LEVEL_NOT_MATCH)
        end
    end
end

---@param Operate number
function DungeonComponent:ReqHandleReadinessCheck(Operate)
    self.remote.ReqHandleReadinessCheck(Operate)
end

function DungeonComponent:RetGetDungeonFirstPassageRecord(DungeonID, DungeonRecord)
    --self:Debug("RetGetDungeonFirstPassageRecord", DungeonID, CommonUtils.tprint(DungeonRecord)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
end

function DungeonComponent:RetHandleReadinessCheck(Result)
    if Result.Code == Game.ErrorCodeConst.NO_ERR then
    else
        if Result.Code == Game.ErrorCodeConst.CREQ_TIMEOUT then
            self:WarningFmt("[RetHandleReadinessCheck] Code:%s Desc:%s", Result.Code, Result.Trace)
        else
            self:WarningFmt("[RetHandleReadinessCheck] Code:%s Desc:%s", Result.Code, Result.Trace)
        end
    end
end

function DungeonComponent.HandleDungeonSettles(DungeonSettles)
    local items = {}
    local itemLists = DungeonSettles.items or {}
    local specialItemLists = DungeonSettles.specialNumDict or {}

    local canMergeItems = {}
    for _, itemList in pairs(itemLists) do
        for _, item in pairs(itemList) do
            local itemID = item.tid or item.itemId
            local count = math.ceil(item.count)
            local itemData = Game.TableData.GetItemNewDataRow(itemID)
            if not ItemComponent.ITEM_CANMERGE_TYPE[itemData.type] then
                if item.tid then
                    table.insert(items, {
                        Tid = item.tid,
                        StackCount = math.ceil(item.count),
                        IsBind = item.isBind
                    })
                else
                    item.count = math.ceil(item.count)
                    table.insert(items, item)
                end
            else
                local isBind = item.isBind or (item.bound and 1 or 0)
                if canMergeItems[itemID] then
                    if canMergeItems[itemID][isBind] then
                        canMergeItems[itemID][isBind] = canMergeItems[itemID][isBind] + count
                    else
                        canMergeItems[itemID][isBind] = count
                    end
                else
                    canMergeItems[itemID] = {
                        [isBind] = count
                    }
                end
            end
        end
    end

    for _, specialItemList in pairs(specialItemLists) do
        for itemID, count in pairs(specialItemList) do
            count = math.ceil(count)
            if canMergeItems[itemID] then
                if canMergeItems[itemID][1] then
                    canMergeItems[itemID][1] = canMergeItems[itemID][1] + count
                else
                    canMergeItems[itemID][1] = count
                end
            else
                canMergeItems[itemID] = {
                    [1] = count
                }
            end
        end
    end

    if next(canMergeItems) then
        for itemID, bindInfo in pairs(canMergeItems) do
            for isBind, count in pairs(bindInfo) do
                table.insert(items, {
                    Tid = itemID,
                    StackCount = count,
                    IsBind = isBind
                })
            end
        end
    end

    table.sort(items, ItemComponent.RewardItemSortFunc)
    return items
end

function DungeonComponent:OnMsgLike(id, Name)
    Game.ReminderManager:AddReminderById(Enum.EReminderTextData.LIKE_REMINDER, { Id = id, Name = Name })
end

function DungeonComponent:RetLike(Result, Eid)
    if Result.Code == Game.ErrorCodeConst.NO_ERR then
    end
end

function DungeonComponent:ReqReturnDungeon()
    self.remote.ReqReturnDungeon()
end

function DungeonComponent:RetReturnDungeon(Result)
	Game.HUDSystem:InvokeSubHUDFunc("P_ExitDungeon2", "ReturnToDungeon", Result)
end

---@param RemainTime number
function DungeonComponent:OnMsgNotifyReturnDungeon(RemainTime)
    self:DebugFmt("[OnMsgNotifyReturnDungeon] dungeon return time remain %s", RemainTime)
	Game.DungeonSystem:SetReturnDungeonTime(RemainTime)
end

function DungeonComponent:ReqChallengeDungeon(challengeType)
    self:Debug("[ReqChallengeDungeon]")
    self.remote.ReqChallengeDungeon(challengeType)
end

function DungeonComponent:RetChallengeDungeon(result)
    self:DebugFmt("RetChallengeDungeon %s %s" , result.Code , result.Trace)
    if result.Code == Game.ErrorCodeConst.ILLEGAL_PLAYER_NUM then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.DUNGEON_RESTART_FAILED, { result.Trace })
    end
end

function DungeonComponent:RetCloseDungeon(result)
    self:DebugFmt("RetCloseDungeon %s %s" , result.Code , result.Trace)
end

function DungeonComponent:OnMsgDungeonStageSettlement(stageId, isSucceed)
    Game.DungeonSystem:SetDungeonStageID(stageId)
    if isSucceed then
        local bDungeonLastStages = Game.TableData.Get_bDungeonLastStages()
        if bDungeonLastStages and bDungeonLastStages[stageId] then
            return
        end
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.DUNGEON_STAGE_SUCCESS, { Index = 0 })
    else
		Game.DungeonSystem:OnDungeonStageFailed(tostring(stageId))
    end
end

function DungeonComponent:OnMsgDungeonSettlement(dungeonId, settlementTime, isSucceed, firstPassageRewards, rewards, titles, bLimitStage, lefttimes, levelMapID)
    --self:Debug("[OnMsgDungeonSettlement] ", CommonUtils.tprint(rewards)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
	Game.DungeonSystem:ShowDungeonResultUI(dungeonId, settlementTime, isSucceed, firstPassageRewards, rewards, titles, bLimitStage, lefttimes, levelMapID)
	Game.HUDSystem:InvokeSubHUDFunc("P_ExitDungeon2", "OnDungeonSettlement", dungeonId, settlementTime, isSucceed, firstPassageRewards, rewards, titles, bLimitStage, lefttimes, levelMapID)
end

--- @param levelMapID number
function DungeonComponent:OnMsgDungeonLevelChange(levelMapID)
end

---@param State boolean
function DungeonComponent:ReqSetDungeonReadinessPanel(State)
    self:DebugFmt("[ReqSetDungeonReadinessPanel] State:%s", State)
    self.remote.ReqSetDungeonReadinessPanel(State)
end

---@param DungeonExitRemain number
function DungeonComponent:OnMsgDungeonExitRemainUpdate(DungeonExitRemain)
    self:DebugFmt("[OnMsgDungeonExitRemainUpdate] %s", DungeonExitRemain)
	Game.DungeonSystem:GetExitRemainTime(DungeonExitRemain)
	Game.HUDSystem:InvokeSubHUDFunc("P_ExitDungeon2", "StartExitCountDown", DungeonExitRemain)
end

---@param dungeonChallengeRemain number
function DungeonComponent:OnMsgDungeonChallengeRemain(dungeonChallengeRemain)
    Log.DebugFormat("OnMsgDungeonChallengeRemain %s", dungeonChallengeRemain)
	Game.DungeonSystem:GetChallengeRemainTime(dungeonChallengeRemain)
	Game.HUDSystem:InvokeSubHUDFunc("P_ExitDungeon2", "ReceiveDungeonOnChallengeRemainUpdate", dungeonChallengeRemain)
end

--- @param dungeonID number
function DungeonComponent:ReqOpenDungeon(dungeonID, dungeonMode)
    self.remote.ReqOpenDungeon(dungeonID, dungeonMode)
end

function DungeonComponent:RetOpenDungeon(result, NonCompliantMemberList)
    if result.Code == Game.ErrorCodeConst.REACH_MAX_PLAYER_IN_SINGLE_DUNGEON then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.BOT_PROFESSION_MATCH_FAIL)
    end
    Game.DungeonSystem:OnOpenDungeon(result, NonCompliantMemberList)
end

---@param bIsTemporary boolean
function DungeonComponent:ReqLeaveDungeon(bIsTemporary)
    self.remote.ReqLeaveDungeon(bIsTemporary)
end

function DungeonComponent:RetLeaveDungeon(result)
	Game.HUDSystem:InvokeSubHUDFunc("P_ExitDungeon2", "FollowTeamLeaveDungeon", result)
end

function DungeonComponent:ReqDungeonSettlement()
    self.remote.ReqDungeonSettlement()
end

function DungeonComponent:RetDungeonSettlement(result)
    self:Debug("RetDungeonSettlement code:", result.Code)
end

function DungeonComponent:ReqGetDungeonFirstPassageRecord(dungeonID)
	self.remote.ReqGetDungeonFirstPassageRecord(dungeonID)
end

function DungeonComponent:RetGetDungeonFirstPassageRecord(result, record)
	self:Debug("RetGetDungeonFirstPassageRecord", result)
	Game.DungeonSystem:UpdateFirstPassageRecord(result, record)
	Game.GlobalEventSystem:Publish(EEventTypesV2.DUNGEON_ON_GET_FIRST_PASSAGE_RECORD, record)
end

function DungeonComponent:set_ExitDungeonBtnState(entity, new, old)
    UI.Invoke("P_ExitDungeon2","OnExitDungeonBtnStateChange",entity,new,old)
end

function DungeonComponent:set_DungeonEntityID(entity, new, old)
    Game.DungeonSystem:OnDungeonEntityIDChanged(entity, new, old)
end

return DungeonComponent
