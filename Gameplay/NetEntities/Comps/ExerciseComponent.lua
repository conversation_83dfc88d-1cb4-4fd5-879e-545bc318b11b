--- @class ExerciseComponent
ExerciseComponent = DefineComponent("ExerciseComponent")

--@param arg1  UINT
function ExerciseComponent:onGetExerciseGuildBuildingLv(...)
    self:Debug("ExerciseComponent:onGetExerciseGuildBuildingLv")
    Game.GuildSystem:HandleRequestRet("onGetExerciseGuildBuildingLv", ...)
end

--@param arg1  UINT_MAP
function ExerciseComponent:onGetGuildExerciseInfo(...)
    self:Debug("ExerciseComponent:onGetGuildExerciseInfo")
    Game.GuildSystem:HandleRequestRet("onGetGuildExerciseInfo", ...)
end

--@param arg1  UINT
--@param arg2  UINT
function ExerciseComponent:onGuildExerciseSuccess(...)
    self:Debug("ExerciseComponent:onGuildExerciseSuccess")
    Game.GuildSystem:HandleRequestRet("onGuildExerciseSuccess", ...)
end

function ExerciseComponent:onResetGuildExercisePointsSuccess(...)
    self:Debug("ExerciseComponent:onResetGuildExercisePointsSuccess")
    Game.GuildSystem:HandleRequestRet("onResetGuildExercisePointsSuccess", ...)
end

--@param arg1  PLAYER_EXERCISE_INFO
--@param arg2  UINT_MAP
function ExerciseComponent:onUpdateExerciseInfo(...)
    self:Debug("ExerciseComponent:onUpdateExerciseInfo")
    Game.GuildSystem:HandleRequestRet("onUpdateExerciseInfo", ...)
end

function ExerciseComponent:getExerciseGuildBuildingLv()
    self:Debug("ExerciseComponent:getExerciseGuildBuildingLv")
    if self.remote == nil then self:Debug("ExerciseComponent:getExerciseGuildBuildingLv remote nil") end
    self.remote.getExerciseGuildBuildingLv()
end

function ExerciseComponent:getGuildExerciseInfo()
    self:Debug("ExerciseComponent:getGuildExerciseInfo")
    if self.remote == nil then self:Debug("ExerciseComponent:getGuildExerciseInfo remote nil") end
    self.remote.getGuildExerciseInfo()
end

--@param arg1  UINT
function ExerciseComponent:doGuildExercise(arg1)
    self:Debug("ExerciseComponent:doGuildExercise")
    if self.remote == nil then self:Debug("ExerciseComponent:doGuildExercise remote nil") end
    self.remote.doGuildExercise(arg1)
end

--@param arg1  UINT
function ExerciseComponent:onCheckGuildExercise(arg1)
    self:Debug("ExerciseComponent:onCheckGuildExercise")
    if self.remote == nil then self:Debug("ExerciseComponent:onCheckGuildExercise remote nil") end
    self.remote.onCheckGuildExercise(arg1)
end

return ExerciseComponent