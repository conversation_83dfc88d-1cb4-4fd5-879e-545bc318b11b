
---@class WareHouseComponent
WareHouseComponent = DefineComponent("WareHouseComponent")

-- ServerMethods start
function WareHouseComponent:getWareHousePageInfo(id)
    self.remote.getWareHousePageInfo(id)
end

function WareHouseComponent:takeFromWareHouse(id, index, gbId)
    self.remote.takeFromWareHouse(id, index, gbId)
end

function WareHouseComponent:storeToWareHouse(id, index, gbId, pageId)
    self.remote.storeToWareHouse(id, index, gbId, pageId)
end

function WareHouseComponent:sortWareHousePage(id)
    self.remote.sortWareHousePage(id)
end

function WareHouseComponent:unlockWareHousePage(id)
    self.remote.unlockWareHousePage(id)
end

function WareHouseComponent:unlockWareHouseSlot(id, unlockId)
    self.remote.unlockWareHouseSlot(id, unlockId)
end
-- ServerMethods end

-- ClientMethods start
function WareHouseComponent:onGetWareHouseInfo(warehouseInfos)
    Game.WarehouseSystem:OnGetWarehouseInfo(warehouseInfos)
end

function WareHouseComponent:onGetWareHousePageInfo(unlockMap, id, pageInfo, vipMap)
    Game.WarehouseSystem:OnGetWarehousePageInfo(unlockMap, id, pageInfo, vipMap)
end

function WareHouseComponent:onChangeWareHouse(code, id, countChangeSlots, changeSlots)
    if code == Enum.ERetCode.SUCCESS then
        -- --如果此时还没有仓库信息，那直接丢弃此回调
        -- if not self.warehousePageInfos or not self.warehousePageInfos[id] then
        --     self:Warning("onChangeWareHouse在获取onGetWareHousePageInfo之前发送")
        --     return
        -- end

        --如果没有有效数据，那直接丢弃此回调
        if not next(countChangeSlots) and not next(changeSlots) then
            self:Warning("onChangeWareHouse发送了无效数据")
            return
        end

        --检查countChangeSlots和changeSlots是否有重复key，如果有重复key以changeSlots为准
        if countChangeSlots and changeSlots then
            for index, info in pairs(changeSlots) do
                if countChangeSlots[index] then
                    countChangeSlots[index] = nil
                    self:Warning("onChangeWareHouse回调中countChangeSlots和changeSlots的key重复", id, index)
                end
            end
        end

        Game.WarehouseSystem:OnChangeWarehouse(id, countChangeSlots, changeSlots)
    end
end

--分页解锁的回调
function WareHouseComponent:onUnlockWareHousePage(id)
    Game.WarehouseSystem:OnUnlockWarehousePage(id)
end

---仓库整理回调
function WareHouseComponent:onSortWareHousePage(unlockMap, id, pageInfo, vipMap)
    Game.WarehouseSystem:OnSortWareHousePage(unlockMap, id, pageInfo, vipMap)
end

function WareHouseComponent:onUnlockWareHouseSlot(id, lockId, slotNumber)
    Game.WarehouseSystem:OnUnlockWarehouseSlot(id, lockId, slotNumber)
end
-- ClientMethods end

return WareHouseComponent
