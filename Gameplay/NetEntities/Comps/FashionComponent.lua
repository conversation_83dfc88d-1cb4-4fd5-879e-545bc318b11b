FashionComponent = DefineComponent("FashionComponent")
require("Gameplay.3C.AvatarCreator.AvatarCreatorDefineAutoGen")
local utils = kg_require("Shared.Utils")
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local AppearanceConst = kg_require("Gameplay.CommonDefines.AppearanceConst")
local LuaMulticastDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaMulticastDelegate")
local AvatarModelPartLib = kg_require("Data.Config.Model.AvatarModelPartLib")
local UMeshComponent = import("MeshComponent")

function FashionComponent:ctor()
	---@type table<EAppearanceSubType2ID, number> 当前玩家身上的所有fashion
	self.CurWearFashionList = {}
	---@type table<EAvatarBodyPartType, table> 当前玩家身上的所有BodyPart
	self.CurWearBodyPartList = {}

	---正在换装的数据（同时只能有一个正在进行）
	self.CurChangeFashionData = {}
	---换装操作缓存
	self.CacheChangeFashion = nil

	self.HairMakeUpID = nil
	
	---@type table<EAppearanceSubType2ID, table> 当前玩家身上的所有周边around
	self.CurWearAroundList = {}

	-- 如果有初始化穿戴数据, 在LoadActor之前, 算一遍数据 BodyParts + MakeupData
	-- 下面的流程会把基础的外观数据放到ViewControlAppearanceComponent中, 让其进行基础数据记录
	-- 下面Init数据, 是捏脸系统解析来的, 和用给底层ViewControlAppearanceComponent的BaseXXX分开, BaseXXX是赋值了什么就用来Apply生效
	self.InitBaseAllBodyParts = nil
	self.InitBodyShapeCompactData = nil
	self.InitFaceAndBodyPresetModelID = nil
	self.InitFacePresetModelID = nil
	self.InitBodyPartMakeupData = nil
	self.InitAroundData = nil
	self.InitFashionOverrideAnimData = nil

	self.CustomRoleFaceData = {}
	---自定义捏脸属性
	--[[
	self.CustomFaceData = {
		---模型facadeID
		ModelPresetID = 0,
		---头部facadeID
		HeadPresetFacadeID = 0,
	
		---头部和身体骨骼数据
		FaceData = {},
		---头部妆容数据（非资源类型参数部分）
		MakeUpParamData = {},
		---头部妆容数据（资源类型参数部分）
		MakeUpResData = {},
	
		---头发时装、妆容ID
		HairFashionID = nil,
		HairMakeUpID = nil,
	}		
	]]

	self.OnFashionChangeEvent = LuaMulticastDelegate.new()
	
	self:RefreshBaseCustomAppearanceData(true)
end

function FashionComponent:dtor()
	self.OnFashionChangeEvent:Clear()
end

function FashionComponent:__component_AfterEnterWorld__()
	self:CheckWearFashionEqualAttributeEnterWorld()
end

function FashionComponent:__component_ExitWorld__()
end

--region 时装---穿
---@public method 穿时装
---@param fashionID number 时装ID
---@param customData table 额外数据（只有时装界面穿搭需要）
function FashionComponent:WearFashion(fashionID, customData)
	if next(self.CurChangeFashionData) then
		self.CacheChangeFashion = {
			functionName = "WearFashion",
			fashionData = fashionID,
			paramData = customData
		}
		return
	end

	--data.removeAllFashion 穿前是否脱掉其他所有时装（有的换套装需要）
	--data.socketNameList 配置挂接点

	--是否是多挂接点的饰品
	if not self:IsAccessoryMultiSocketByFashionID(fashionID) then
		local config = Game.TableData.GetFashionDataRow(fashionID)
		if config.SubType == Enum.EAppearanceSubType2ID.Suit then
			self:WearFashionList({fashionID}, customData)
		else
			self:WearFashion_Single(fashionID, customData)
		end
	else
		self:WearFashion_Multi(fashionID, customData)
	end
end

---@private method 换时装（每个BodyPart对应单个）
function FashionComponent:WearFashion_Single(fashionID, customData)
	local addBodyPartList, removeBodyPartList = self:GetChangeBodyPartListOnWearSingle(fashionID, customData)
	local bodyPartMakeUpData = self:GetBodyPartChangeMakeUpData(customData)
	
	self.CurChangeFashionData.fashionID = fashionID
	self.CurChangeFashionData.addBodyPartList = addBodyPartList
	self.CurChangeFashionData.removeBodyPartList = removeBodyPartList
	self.CurChangeFashionData.bodyPartMakeUpData = bodyPartMakeUpData

	self:RefreshBodyPart_BodyParts(removeBodyPartList, addBodyPartList, bodyPartMakeUpData, self, "WearFashionSingleCallBack")
end

---@private method 换时装Single回调
function FashionComponent:WearFashionSingleCallBack()
	self:RefreshCurWearFashionOnWear(self.CurChangeFashionData.fashionID)
	self:RefreshBodyPartOnChange_Single(self.CurChangeFashionData.removeBodyPartList, self.CurChangeFashionData.addBodyPartList)
	self:RefreshBodyPartMakeUp(self.CurChangeFashionData.bodyPartMakeUpData)
	
	self:OnRefreshAvatarCallBack()
end

---@private method 换时装（每个BodyPart对应多个）（饰品用）
function FashionComponent:WearFashion_Multi(fashionID, data)
	local addTagList, removeTagList = self:GetChangeBodyPartListOnWearMulti(fashionID, data)
	self.CurChangeFashionData.fashionID = fashionID
	self.CurChangeFashionData.addTagList = addTagList
	self.CurChangeFashionData.removeTagList = removeTagList

	self:RefreshBodyPart_ModelInstancesInSpecificBodyType(
		self:GetBodyPartTypeByFashionID(fashionID), removeTagList, addTagList, nil, self, "WearFashionMultiCallBack"
	)
end

---@private method 换时装Multi回调
function FashionComponent:WearFashionMultiCallBack()
	self:RefreshCurWearFashionOnWear(self.CurChangeFashionData.fashionID)
	self:RefreshBodyPartOnChange_Multi(self.CurChangeFashionData.removeTagList, self.CurChangeFashionData.addTagList)
	
	self:OnRefreshAvatarCallBack()
end

---@private method 获取改变的部件，当穿时装时
function FashionComponent:GetChangeBodyPartListOnWearSingle(fashionID, customData)
	local addBodyPartList = self:GetBodyPartsByFashionID(fashionID, customData and customData.saveData and customData.saveData[fashionID])
	local removeBodyPartList = self:GetRemoveBodyPartByFashionID_Single(fashionID, customData, addBodyPartList)
	return addBodyPartList, removeBodyPartList
end

---@public method 根据FashionID获取BodyPart
function FashionComponent:GetBodyPartsByFashionID(fashionID, saveData)
	local GetFashionDataTable = Game.TableData.GetFashionDataTable()
	local profileName = self:GetBaseProfileName()
	local fashionConfig = GetFashionDataTable[fashionID]
	local PartDataLib = AvatarModelPartLib[profileName]
	local bodyPartList = {}

	if fashionConfig.Type == Enum.EAppearanceType2ID.Fashion then
		if fashionConfig.SubType == Enum.EAppearanceSubType2ID.Suit then
			for _, subFashionID in ksbcipairs(fashionConfig.SuitComps) do
				local subFashionConfig = GetFashionDataTable[subFashionID]
				for index, meshID in ksbcipairs(subFashionConfig.MeshID) do
					local configPartData = PartDataLib[meshID]
					bodyPartList[configPartData.BodyPartType] = {
						["ID"] = meshID,
						["BodyPartType"] = configPartData.BodyPartType,
						["MakeupID"] = subFashionConfig.MakeUpID[index],
						["Tag"] = Enum.EAvatarBodyPartTypeName[configPartData.BodyPartType],
					}
				end
			end
		else
			for index, meshID in ksbcipairs(fashionConfig.MeshID) do
				local configPartData = PartDataLib[meshID]
				if not configPartData then
					self:ErrorFmt("Data = nil, Check Profile And FashionID. profileName=%s,fashionID = %s", profileName, fashionID)
					return bodyPartList
				end
				
				local makeupID = nil
				local bodyPartType = configPartData.BodyPartType
				if bodyPartType == Enum.EAvatarBodyPartType.Hair then
					local customRoleHairMakeUpID = fashionConfig.MakeUpID[index]
					if self.CustomRoleFaceData.HairMakeUpID and self.CustomRoleFaceData.HairFashionID == fashionID then
						customRoleHairMakeUpID = self.CustomRoleFaceData.HairMakeUpID
					end
					
					makeupID = customRoleHairMakeUpID
				else
					makeupID = fashionConfig.MakeUpID[index]
				end
				
				bodyPartList[bodyPartType] = {
					["ID"] = meshID,
					["BodyPartType"] = bodyPartType,
					["MakeupID"] = makeupID,
					["Tag"] = Enum.EAvatarBodyPartTypeName[bodyPartType],
				}
			end
		end
	elseif fashionConfig.Type == Enum.EAppearanceType2ID.Accessory then
		local meshID = fashionConfig.MeshID[1]
		local configPartData = PartDataLib[meshID]
		if not configPartData then
			self:ErrorFmt("Fashion:configPartData = nil, profileName=%s meshID = %s", profileName, meshID)
			return bodyPartList
		end
		
		--默认挂接位置
		local offset = nil
		if ksbcnext(fashionConfig.AccConfigID) then
			if saveData and saveData.Pos then
				offset = Game.FashionSystem:GetFashionOffsetTransform(fashionID, saveData.Scale,
					saveData.Pos.X, saveData.Pos.Y, saveData.Pos.Z,
					saveData.Rot.Pitch, saveData.Rot.Yaw, saveData.Rot.Roll
				)
			end
		end
		
		bodyPartList[configPartData.BodyPartType] = {
			["ID"] = meshID,
			["BodyPartType"] = configPartData.BodyPartType,
			["Offset"] = offset,
			["MakeupID"] = fashionConfig.MakeUpID[1],
			["Tag"] = Enum.EAvatarBodyPartTypeName[configPartData.BodyPartType],
		}
	end

	return bodyPartList
end

---@private method 获取移除的部件，当穿单BodyPart时装时
function FashionComponent:GetRemoveBodyPartByFashionID_Single(fashionID, data)
	local removeBodyPartList = {}
	local fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	local newFashionSubType = fashionConfig.SubType

	--如果有冲突的fashion，脱下来
	if newFashionSubType == Enum.EAppearanceSubType2ID.Suit then
		for _, subFashionID in ksbcipairs(fashionConfig.SuitComps) do
			local subFashionConfig = Game.TableData.GetFashionDataRow(subFashionID)
			self:GetConflictBodyPartListBySingleFashionID(subFashionConfig, removeBodyPartList, data)
		end
	else
		self:GetConflictBodyPartListBySingleFashionID(fashionConfig, removeBodyPartList, data)
	end

	return removeBodyPartList
end

---@private method 获取冲突的部件，当穿时装时
function FashionComponent:GetConflictBodyPartListBySingleFashionID(fashionConfig, removeBodyPartList, data)
	local newSubType = fashionConfig.SubType
	--local conflictSocks = fashionConfig.ConflictSocks
	--local conflictPant = fashionConfig.ConflictPant

	if data and data.removeAllFashion then
		for subType, curFashionID in pairs(self.CurWearFashionList) do
			self:MergeRemoveBodyPartList(
				self:GetBodyPartsByFashionID(curFashionID),
				removeBodyPartList
			)
		end
		return
	end
	
	local sameSubTypeFashion = self.CurWearFashionList[newSubType]
	if sameSubTypeFashion then
		self:MergeRemoveBodyPartList(
			self:GetBodyPartsByFashionID(sameSubTypeFashion),
			removeBodyPartList
		)
	end

	--if conflictSocks then
	--	local sockFashionID = self.CurWearFashionList[Enum.EAppearanceSubType2ID.Socks]
	--	if sockFashionID then
	--		if conflictSocks[1] == 1 then
	--			self:MergeRemoveBodyPartList(
	--				self:GetBodyPartsByFashionID(sockFashionID),
	--				removeBodyPartList
	--			)
	--		else
	--			for _, conflictID in pairs(conflictSocks) do
	--				if conflictID == sockFashionID then
	--					self:MergeRemoveBodyPartList(
	--						self:GetBodyPartsByFashionID(sockFashionID),
	--						removeBodyPartList
	--					)
	--					break
	--				end
	--			end
	--		end
	--	else
	--		if conflictSocks[1] == 1 then
	--			removeBodyPartList[Enum.EAvatarBodyPartType.Leg] = true
	--		end
	--	end
	--end
	--
	--if conflictPant then
	--	local pantsFashionID = self.CurWearFashionList[Enum.EAppearanceSubType2ID.Pants]
	--	if pantsFashionID then
	--		if conflictPant[1] == 1 then
	--			self:MergeRemoveBodyPartList(
	--				self:GetBodyPartsByFashionID(pantsFashionID),
	--				removeBodyPartList
	--			)
	--		else
	--			for _, conflictID in pairs(conflictPant) do
	--				if conflictID == pantsFashionID then
	--					self:MergeRemoveBodyPartList(
	--						self:GetBodyPartsByFashionID(pantsFashionID),
	--						removeBodyPartList
	--					)
	--					break
	--				end
	--			end
	--		end
	--	else
	--		if conflictPant[1] == 1 then
	--			removeBodyPartList[Enum.EAvatarBodyPartType.BodyLower] = true
	--		end
	--	end
	--end
end

---@private method 初始化CurFashion
function FashionComponent:MergeRemoveBodyPartList(bodyPartList, removeBodyPartList)
	for partType, v in pairs(bodyPartList) do
		removeBodyPartList[partType] = true
	end
end

---@private method 获取改变的部件，当穿时装时
function FashionComponent:GetChangeBodyPartListOnWearMulti(fashionID, customData)
	local addTagList = self:GetBodyPartsByFashionID_Multi(fashionID, customData)
	local removeTagList = self:GetRemoveBodyPartByFashionID_Multi(fashionID, addTagList)
	return addTagList, removeTagList
end

---@private method 根据FashionID获取TagData
function FashionComponent:GetBodyPartsByFashionID_Multi(fashionID, customData)
	local profileName = self:GetBaseProfileName()
	local fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	local meshID = fashionConfig.MeshID[1]
	local configPartData = Game.ActorAppearanceManager:GetModelPartLibData(profileName, meshID)
	if not configPartData then
		self:ErrorFmt("configPartData = nil, profileName = %s,fashionID = %s,meshID = %s", profileName,fashionID,fashionID)
		return {
			Type = Enum.EAvatarBodyPartType.BodyUpper,
			PartsValue = {}
		}
	end

	local bodyPartList = {
		Type = configPartData.BodyPartType,
		PartsValue = {}
	}
	
	local socketNameList = nil
	local offset = nil
	if ksbcnext(fashionConfig.AccConfigID) then
		socketNameList = self:GetDefaultSocketNameByAccConfig(fashionConfig.AccConfigID[1])
		if customData then
			if customData.saveData and customData.saveData[fashionID] and customData.saveData[fashionID].Pos then
				local infoData = customData.saveData[fashionID]
				offset = Game.FashionSystem:GetFashionOffsetTransform(fashionID, infoData.Scale,
					infoData.Pos.X, infoData.Pos.Y, infoData.Pos.Z,
					infoData.Rot.Pitch, infoData.Rot.Yaw, infoData.Rot.Roll
				)

				socketNameList = self:GetDefaultSocketNameByAccConfig(fashionConfig.AccConfigID[1], infoData.SlotPos)
			elseif customData.socketNameList then
				socketNameList = customData.socketNameList
			end
		end
	else
		socketNameList = {Enum.EAvatarBodyPartTypeName[configPartData.BodyPartType]}
	end
	
	
	for i, socketName in ksbcipairs(socketNameList) do
		--复数个部件，需要这种格式
		local key = string.format("%s_%s", Enum.EAvatarBodyPartTypeName[configPartData.BodyPartType], i)
		bodyPartList.PartsValue[key] = {
			["ID"] = meshID,
			["Offset"] = offset,
			["SocketName"] = socketName,
			["BodyPartType"] = configPartData.BodyPartType,
			["Tag"] = key,
		}
	end

	return bodyPartList
end

---@private method 获取移除的部件，当穿多BodyPart时装时
function FashionComponent:GetRemoveBodyPartByFashionID_Multi(fashionID, addBodyPartList)
	local removeBodyPartList = self:GetBodyPartsByFashionID_Multi(fashionID)
	for partType, removePartData in pairs(removeBodyPartList) do
		if addBodyPartList[partType] then
			removeBodyPartList[partType] = nil
		end
	end

	return table.keys(removeBodyPartList)
end


---@private method 换时装后刷新缓存的Fashion
function FashionComponent:RefreshCurWearFashionOnWear(fashionID)
	local fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	local fashionSubType = fashionConfig.SubType

	self.CurWearFashionList[fashionSubType] = fashionID
	if fashionSubType == Enum.EAppearanceSubType2ID.Suit then
		for _, subFashionID in ksbcipairs(fashionConfig.SuitComps) do
			local subFashionConfig = Game.TableData.GetFashionDataRow(subFashionID)
			local subFashionSubType = subFashionConfig.SubType

			self.CurWearFashionList[subFashionSubType] = subFashionID
		end
	else
		self:RefreshSuitWearAll()
	end
end

--endregion

--region 时装---穿脱
---@private method 穿脱时装（时装界面使用，不会有套装）
function FashionComponent:WearAndUnWearFashionList(wearFashionList, unWearFashionList, customData)
	if not self:IsCanWearAndUnWearFashion(wearFashionList, unWearFashionList) then
		return
	end

	if next(self.CurChangeFashionData) then
		self.CacheChangeFashion = {
			functionName = "WearAndUnWearFashionList",
			fashionData = wearFashionList,
			paramData = unWearFashionList,
		}
		return
	end

	local addBodyPartList, removeBodyPartList = self:GetChangeBodyPartListOnWearUnWear(wearFashionList, unWearFashionList, customData)
	local bodyPartMakeUpData = self:GetBodyPartChangeMakeUpData(customData)
	
	self.CurChangeFashionData.wearFashionList = wearFashionList
	self.CurChangeFashionData.unWearFashionList = unWearFashionList
	self.CurChangeFashionData.addBodyPartList = addBodyPartList
	self.CurChangeFashionData.removeBodyPartList = removeBodyPartList

	self:RefreshBodyPart_BodyParts(removeBodyPartList, addBodyPartList, bodyPartMakeUpData, self, "WearUnWearFashionCallBack")
end

---@private method
function FashionComponent:WearUnWearFashionCallBack()
	for i, fashionID in pairs(self.CurChangeFashionData.unWearFashionList) do
		self:RefreshCurWearFashionOnUnWear(fashionID)
	end
	
	for i, fashionID in pairs(self.CurChangeFashionData.wearFashionList) do
		self:RefreshCurWearFashionOnWear(fashionID)
	end
	
	self:RefreshBodyPartOnChange_Single(self.CurChangeFashionData.removeBodyPartList, self.CurChangeFashionData.addBodyPartList)

	self:OnRefreshAvatarCallBack()
end

---@private method
function FashionComponent:GetChangeBodyPartListOnWearUnWear(wearFashionList, unWearFashionList, customData)
	local addBodyPartListAll = {}
	local removeBodyPartListAll = {}

	for i, fashionID in pairs(unWearFashionList) do
		local addBodyPartList = nil
		local removeBodyPartList = nil
		if not self:IsAccessoryMultiSocketByFashionID(fashionID) then
			addBodyPartList, removeBodyPartList = self:GetChangeBodyPartListOnUnWear_Single(fashionID)
		else
			addBodyPartList, removeBodyPartList = self:GetChangeBodyPartListOnUnWear_Multi(fashionID)
		end
		
		table.merge(addBodyPartListAll, addBodyPartList)
		table.merge(removeBodyPartListAll, removeBodyPartList)
	end

	for i, fashionID in pairs(wearFashionList) do
		if not self:IsAccessoryMultiSocketByFashionID(fashionID) then
			local addBodyPartList, removeBodyPartList = self:GetChangeBodyPartListOnWearSingle(fashionID, customData)
			table.merge(addBodyPartListAll, addBodyPartList)
			table.merge(removeBodyPartListAll, removeBodyPartList)
		else
			local addBodyPartList = self:GetBodyPartsByFashionID_Multi(fashionID, customData)
			addBodyPartListAll[self:GetBodyPartTypeByFashionID(fashionID)] = addBodyPartList
		end
	end
	
	return addBodyPartListAll, removeBodyPartListAll
end


---@private method
function FashionComponent:IsCanWearAndUnWearFashion(wearFashionList, unWearFashionList)
	for i, fashionID in pairs(wearFashionList) do
		if self:IsWearFashion(fashionID) then
			return false
		end
	end

	for i, fashionID in pairs(unWearFashionList) do
		if not self:IsWearFashion(fashionID) then
			return false
		end
	end
	
	return true
end


--endregion

--region 时装---脱
---@private method 脱时装（每个BodyPart对应单个）
function FashionComponent:UnWearFashion(fashionID, customData)
	if not self:IsWearFashion(fashionID) then
		return
	end
	
	if next(self.CurChangeFashionData) then
		self.CacheChangeFashion = {
			functionName = "UnWearFashion",
			fashionData = fashionID,
		}
		return
	end

	--是否是多挂接点的饰品
	if not self:IsAccessoryMultiSocketByFashionID(fashionID) then
		self:UnWearFashion_Single(fashionID, customData)
	else
		self:UnWearFashion_Multi(fashionID)
	end
end

---@public method 脱时装Single
function FashionComponent:UnWearFashion_Single(fashionID, customData)
	local addBodyPartList, removeBodyPartList = self:GetChangeBodyPartListOnUnWear_Single(fashionID)
	self.CurChangeFashionData.fashionID = fashionID
	self.CurChangeFashionData.defaultFashionIDList = {}
	self.CurChangeFashionData.addBodyPartList = addBodyPartList
	self.CurChangeFashionData.removeBodyPartList = removeBodyPartList

	self:RefreshBodyPart_BodyParts(removeBodyPartList, addBodyPartList, nil, self, "UnWearFashionSingleCallBack")
end


---@private method 脱时装Single回调
function FashionComponent:UnWearFashionSingleCallBack()
	self:RefreshCurWearFashionOnUnWear(self.CurChangeFashionData.fashionID)
	for i, fashionID in pairs(self.CurChangeFashionData.defaultFashionIDList) do
		self:RefreshCurWearFashionOnWear(fashionID)
	end
	self:RefreshBodyPartOnChange_Single(self.CurChangeFashionData.removeBodyPartList, self.CurChangeFashionData.addBodyPartList)
	
	self:OnRefreshAvatarCallBack()
end

---@public method 获取改变的部件，当脱时装时
function FashionComponent:GetChangeBodyPartListOnUnWear_Single(fashionID)
	local removeBodyPartList = self:GetBodyPartsByFashionID(fashionID)
	local addBodyPartList = {}
	
	local profileName = self:GetBaseProfileName()
	local presetModelConfig = Game.ActorAppearanceManager:GetModelLibData(self:GetConfigModelID())
	for partType, removePartData in pairs(removeBodyPartList) do
		removeBodyPartList[partType] = true
		for _, defaultPartData in pairs(presetModelConfig.BodyPartList) do
			if not AppearanceConst.IsMultiInstBodyPart(defaultPartData.Type) then
				local defaultPartConfig = Game.ActorAppearanceManager.AvatarModelPartLib[profileName][defaultPartData.ID]
				if defaultPartConfig.BodyPartType == partType then
					if partType == Enum.EAvatarBodyPartType.Hair then
						local customRoleHairMakeUpID = defaultPartData.MakeupID
						if self.CustomRoleFaceData.HairMakeUpID and self.CustomRoleFaceData.HairFashionID == fashionID then
							customRoleHairMakeUpID = self.CustomRoleFaceData.HairMakeUpID
						end
						addBodyPartList[defaultPartConfig.BodyPartType] = {
							["ID"] = defaultPartData.ID,
							["MakeupID"] = customRoleHairMakeUpID,
						}
					else
						addBodyPartList[defaultPartConfig.BodyPartType] = {
							["ID"] = defaultPartData.ID,
							["MakeupID"] = defaultPartData.MakeupID,
						}
					end
					removeBodyPartList[partType] = nil
					break
				end
			end
		end
	end

	return addBodyPartList, removeBodyPartList
end


---@private method 脱时装Multi（每个BodyPart对应多个）（饰品用）
function FashionComponent:UnWearFashion_Multi(fashionID)
	local addBodyPartList, removeBodyPartList = self:GetChangeBodyPartListOnUnWear_Multi(fashionID)
	self.CurChangeFashionData.fashionID = fashionID
	self.CurChangeFashionData.addTagList = addBodyPartList
	self.CurChangeFashionData.removeTagList = removeBodyPartList

	self:RefreshBodyPart_ModelInstancesInSpecificBodyType(
		self:GetBodyPartTypeByFashionID(fashionID), removeBodyPartList, addBodyPartList, nil, self, "UnWearFashionMultiCallBack"
	)
end

---@private method 脱时装Multi回调
function FashionComponent:UnWearFashionMultiCallBack()
	self:RefreshCurWearFashionOnUnWear(self.CurChangeFashionData.fashionID)
	self:RefreshBodyPartOnChange_Multi(self.CurChangeFashionData.removeTagList, self.CurChangeFashionData.addTagList)
	
	self:OnRefreshAvatarCallBack()
end

---@private method 获取改变的部件，当脱时装时
function FashionComponent:GetChangeBodyPartListOnUnWear_Multi(fashionID)
	local addBodyPartList = {}
	local removeBodyPartList = {}
	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	if not self.CurWearBodyPartList[partType] or not self.CurWearBodyPartList[partType].PartsValue then
		self:ErrorFmt("Fashion: GetChangeBodyPartListOnUnWear_Multi error, fashion = %s", fashionID)
		return addBodyPartList, removeBodyPartList
	end
	for _, partData in pairs(self.CurWearBodyPartList[partType].PartsValue) do
		table.insert(removeBodyPartList, partData.Tag)
	end

	return addBodyPartList, removeBodyPartList
end

---@private method 脱时装后刷新缓存的Fashion
function FashionComponent:RefreshCurWearFashionOnUnWear(fashionID)
	local fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	local fashionSubType = fashionConfig.SubType

	self.CurWearFashionList[fashionSubType] = nil
	if fashionSubType == Enum.EAppearanceSubType2ID.Suit then
		for _, subFashionID in ksbcipairs(fashionConfig.SuitComps) do
			local subFashionConfig = Game.TableData.GetFashionDataRow(subFashionID)
			local subFashionSubType = subFashionConfig.SubType
			self.CurWearFashionList[subFashionSubType] = nil
		end
	else
		self:RefreshSuitWearAll()
	end
end
--endregion

--region 时装---全刷
---@public method 换时装列表
function FashionComponent:WearFashionList(fashionIDList, paramData)
	if next(self.CurChangeFashionData) then
		self.CacheChangeFashion = {
			functionName = "WearFashionList",
			fashionData = fashionIDList,
			paramData = paramData
		}
		return
	end
	
	local addBodyPartListAll = {}
	local removeBodyPartListAll = {}
	
	for i, fashionID in ksbcpairs(fashionIDList) do
		local config = Game.TableData.GetFashionDataRow(fashionID)
		if not config then
			self:ErrorFmt("Fashion: config = nil, id = %s", fashionID)
			return
		end
		if config.SubType == Enum.EAppearanceSubType2ID.Suit then
			for _, subFashionID in ksbcipairs(config.SuitComps) do
				self:GetChangeBodyPartListOnWear_List(subFashionID, paramData, addBodyPartListAll, removeBodyPartListAll)
			end
		else
			self:GetChangeBodyPartListOnWear_List(fashionID, paramData, addBodyPartListAll, removeBodyPartListAll)
		end
	end

	if paramData and paramData.removeAllFashion then
		for partType, partValue in pairs(self.CurWearBodyPartList) do
			if not AppearanceConst.IsMultiInstBodyPart(partType) then
				removeBodyPartListAll[partType] = true
			else
				if not removeBodyPartListAll[partType] then
					removeBodyPartListAll[partType] = {}
				end
				for partTypeTag, _ in pairs(partValue.PartsValue) do
					table.insert(removeBodyPartListAll[partType], partTypeTag)
				end
			end
		end
	end
	
	local bodyPartMakeUpData = self:GetBodyPartChangeMakeUpData(paramData)

	self.CurChangeFashionData.fashionIDList = fashionIDList
	self.CurChangeFashionData.paramData = paramData
	self.CurChangeFashionData.removeBodyPartListAll = removeBodyPartListAll
	self.CurChangeFashionData.addBodyPartListAll = addBodyPartListAll
	self.CurChangeFashionData.bodyPartMakeUpData = bodyPartMakeUpData
	
	--todo 临时：查偶现的换装报错
	self:DebugFmt("Fashion: WearFashionList")
	self:DebugFmt("Fashion: Cur partType = %s", table.tostring(table.keys(self.CurWearBodyPartList)))
	self:DebugFmt("Fashion: Wear partType = %s", table.tostring(table.keys(addBodyPartListAll)))
	self:DebugFmt("Fashion: UnWear partType = %s", table.tostring(table.keys(removeBodyPartListAll)))
	
	self:RefreshBodyPart_BodyParts(removeBodyPartListAll, addBodyPartListAll, bodyPartMakeUpData, self, "WearFashionListCallBack")
end

---@private method 
function FashionComponent:WearFashionListCallBack()
	if self.CurChangeFashionData.paramData and self.CurChangeFashionData.paramData.removeAllFashion then
		table.clear(self.CurWearFashionList)
		table.clear(self.CurWearBodyPartList)
	end
	
	for i, fashionID in ksbcpairs(self.CurChangeFashionData.fashionIDList) do
		self:RefreshCurWearFashionOnWear(fashionID)
	end
	self:RefreshBodyPartOnChange_Single(self.CurChangeFashionData.removeBodyPartListAll, self.CurChangeFashionData.addBodyPartListAll)
	self:RefreshSuitWearAll()
	self:RefreshBodyPartMakeUp(self.CurChangeFashionData.bodyPartMakeUpData)

	self:OnRefreshAvatarCallBack()
end

---@public method 获取改变的部件，当穿时装List
function FashionComponent:GetChangeBodyPartListOnWear_List(fashionID, customData, addBodyPartListAll, removeBodyPartListAll)
	local addBodyPartList = nil
	local removeBodyPartList = nil

	--data.removeAllFashion
	if not self:IsAccessoryMultiSocketByFashionID(fashionID) then
		addBodyPartList = self:GetBodyPartsByFashionID(fashionID, customData and customData.saveData and customData.saveData[fashionID])
		table.merge(addBodyPartListAll, addBodyPartList)
		
		if customData and not customData.removeAllFashion then
			removeBodyPartList = self:GetRemoveBodyPartByFashionID_Single(fashionID, customData)
			table.merge(removeBodyPartListAll, removeBodyPartList)
		end

	else
		addBodyPartList = self:GetBodyPartsByFashionID_Multi(fashionID, customData)
		addBodyPartListAll[self:GetBodyPartTypeByFashionID(fashionID)] = addBodyPartList
		
		if customData and not customData.removeAllFashion then
			removeBodyPartList = self:GetRemoveBodyPartByFashionID_Multi(fashionID, addBodyPartList)
			table.merge(removeBodyPartListAll, removeBodyPartList)
		end
	end
end

function FashionComponent:RevertAllOnAttributeChange()
	self:RevertDefaultFashion()
	self:RevertDefaultAround()
	self:RefreshBaseCustomAppearanceData(true)
end

---@public method 还原到默认服务器时装状态
function FashionComponent:RevertDefaultFashion()
	if next(self.CurChangeFashionData) then
		self.CacheChangeFashion = {
			functionName = "RevertDefaultFashion",
		}
		return
	end
	
	local saveDataList = {}
	local fashionList = {}
	for fashionID, saveData in pairs(self:GetCurWearFashionAttribute()) do
		local config = Game.TableData.GetFashionDataRow(fashionID)
		if config.SubType ~= Enum.EAppearanceSubType2ID.Suit then
			table.insert(fashionList, fashionID)
			saveDataList[fashionID] = saveData
		end
	end
	for fashionID, saveData in pairs(self:GetCurWearAccessoryAttribute()) do
		table.insert(fashionList, fashionID)
		saveDataList[fashionID] = saveData
	end
	
	self:WearFashionList(fashionList, {removeAllFashion = true, saveData = saveDataList})
end

---@public method 还原到默认服务器周边状态
function FashionComponent:RevertDefaultAround()
	self:UnWearAllAround()
	
	for fashionID, v in pairs(self:GetCurWearAroundAttribute()) do
		self:WearAround(fashionID)
	end
end

---@private method RefreshAvatar回调
function FashionComponent:OnRefreshAvatarCallBack()
	table.clear(self.CurChangeFashionData)

	if self.OnFashionChangeEvent:IsBind() then
		self.OnFashionChangeEvent:Broadcast(self:uid())
	end

	self:RefreshFashionOverrideAnimData()
	self:CheckFashionChangeQueue()
end

---@private method
function FashionComponent:GetBodyPartChangeMakeUpData(paramData)
	if not paramData or not paramData.saveData then
		return nil
	end

	local makeUpList = {}
	for fashionID, saveData in pairs(paramData.saveData) do
		if saveData.StainData then
			local partType = self:GetBodyPartTypeByFashionID(fashionID)
			makeUpList[partType] = {
				[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA] = self:GetMakeUpDataBySaveData(saveData.StainData)
			}
		end
	end

	return makeUpList
end

---@private method 刷新基础染色
function FashionComponent:GetMakeUpDataBySaveData(saveStainData)
	local makeUpData = {}
	for id, vector4 in pairs(saveStainData) do
		local config = Game.TableData.GetDyeingConfigDataRow(id)
		makeUpData[config.Socket] = FLinearColor(vector4.X, vector4.Y, vector4.Z, vector4.W)
	end
	
	return makeUpData
end

---@private method 根据服务器时装数据，初始化所有部件数据
function FashionComponent:InitAllBodyPartByDefaultFashion()
	local fashionID2SubTypeDataTable = Game.TableData.GetFashionID2SubTypeDataTable()
	local GetDyeingConfigDataTable = Game.TableData.GetDyeingConfigDataTable()
	
	table.clear(self.CurWearFashionList)
	table.clear(self.CurWearBodyPartList)

	for fashionID, saveData in pairs(self:GetCurWearFashionAttribute()) do
		local fashionSubType = fashionID2SubTypeDataTable[fashionID]
		if not fashionSubType then
			self:DebugErrorFmt("Fashion: fashionSubType=nil, check fashionID = %s", fashionID)
			goto continue
		end

		self.CurWearFashionList[fashionSubType] = fashionID
		local isSuit = fashionSubType == Enum.EAppearanceSubType2ID.Suit
		if not isSuit then
			local partDataList = self:GetBodyPartsByFashionID(fashionID)
			for partType, partValue in pairs(partDataList) do
				if partType == Enum.EAvatarBodyPartType.Hair and self.InitBaseAllBodyParts[partType]
				then
					--用捏脸头发和染色
				else
					self.InitBaseAllBodyParts[partType] = partValue

					if saveData and saveData.StainData then
						if not self.InitBodyPartMakeupData[partType] then
							self.InitBodyPartMakeupData[partType] = {
								[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA] = {},
								[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_RES_DATA]   = {}
							}
						end

						local makeupParamData = self.InitBodyPartMakeupData[partType][ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA]
						for stainID, colorData in pairs(saveData.StainData) do
							local stainConfig = GetDyeingConfigDataTable[stainID]
							makeupParamData[stainConfig.Socket] = FLinearColor(
								colorData.X,
								colorData.Y,
								colorData.Z,
								colorData.W
							)
						end
					end
				end
			end
		end
		::continue::
	end

	for fashionID, data in pairs(self:GetCurWearAccessoryAttribute()) do
		local fashionSubType = fashionID2SubTypeDataTable[fashionID]
		self.CurWearFashionList[fashionSubType] = fashionID
		
		if not self:IsAccessoryMultiSocketByFashionID(fashionID) then
			table.merge(self.InitBaseAllBodyParts, self:GetBodyPartsByFashionID(fashionID, data))
		else
			local partType = self:GetBodyPartTypeByFashionID(fashionID)
			self.InitBaseAllBodyParts[partType] = self:GetBodyPartsByFashionID_Multi(fashionID, {saveData = self:GetCurWearAccessoryAttribute()})
		end
	end

	for k, v in pairs(self.InitBaseAllBodyParts) do
		self.CurWearBodyPartList[k] = v
	end
end

---@private method 获取服务器保存的时装数据
function FashionComponent:GetCurWearFashionAttribute()
	local curWearFashion = self.playerAppearance.curWearFashion
	if next(curWearFashion) then
		return curWearFashion
	else
		return Game.FashionSystem:GetProfessionInitFashion(self.Profession, self.Sex)
	end
end

---@private method 获取服务器保存的配饰数据
function FashionComponent:GetCurWearAccessoryAttribute()
	return self.playerAppearance.curWearAccessory
end

---@private method 获取服务器保存的周边数据
function FashionComponent:GetCurWearAroundAttribute()
	return self.playerAppearance.curWearAround
end

---@private method 根据服务器周边数据，获取所有周边
function FashionComponent:GetAllAroundDataByDefaultAround()
	local GetFashionDataTable = Game.TableData.GetFashionDataTable()
	
	local allInitAroundList = nil
	for fashionID, v in pairs(self:GetCurWearAroundAttribute()) do
		if not allInitAroundList then
			allInitAroundList = {}
		end
		
		local config = GetFashionDataTable[fashionID]
		local fashionSubType = config.SubType
		local effectPath = config.EffectPath
		local effectSocket = config.EffectSocket
		local idleID = config.IdleID
		
		if not allInitAroundList[fashionSubType] then
			allInitAroundList[fashionSubType] = {}
		end

		if fashionSubType == Enum.EAppearanceAroundID.BodyEffect then
			for index, path in ksbcipairs(effectPath) do
				table.insert(allInitAroundList[fashionSubType], {
					path = path,
					socket = effectSocket[index] or "root"
				})
			end
		elseif fashionSubType == Enum.EAppearanceAroundID.FootPrint then
			--左右脚
			allInitAroundList[fashionSubType][1] = effectPath[1]
			allInitAroundList[fashionSubType][2] = effectPath[2] or effectPath[1]
		elseif fashionSubType == Enum.EAppearanceAroundID.FashionIdle01 then
			table.insert(allInitAroundList[fashionSubType], {
				anim = idleID,
			})
		end
	end
		
	return allInitAroundList
end

---@private method 
function FashionComponent:GetFashionOverrideAnimID()
	local fashionID2SubTypeDataTable = Game.TableData.GetFashionID2SubTypeDataTable()
	local GetFashionDataTable = Game.TableData.GetFashionDataTable()
	local animOverride = nil

	local curPantsID = nil
	local curUpperID = nil
	for fashionID, v in pairs(self:GetCurWearFashionAttribute()) do
		local fashionSubType = fashionID2SubTypeDataTable[fashionID]
		if fashionSubType then
			if fashionSubType == Enum.EAppearanceSubType2ID.Pants then
				curPantsID = fashionID
			end
			if fashionSubType == Enum.EAppearanceSubType2ID.Upper then
				curUpperID = fashionID
			end
		end
	end
	
	if curPantsID then
		local config = GetFashionDataTable[curPantsID]
		animOverride = config.AnimOverride
	end

	if not animOverride and curUpperID then
		local config = GetFashionDataTable[curUpperID]
		animOverride = config.AnimOverride
	end

	return animOverride
end

---@private method 根据自定义时装数据，获取所有部件数据
function FashionComponent:GetAllBodyPartByCustomFashion(fashionIDList)
	local allInitBodyPartList = {}
	for _, fashionID in pairs(fashionIDList) do
		if not self:IsAccessoryMultiSocketByFashionID(fashionID) then
			table.merge(allInitBodyPartList, self:GetBodyPartsByFashionID(fashionID))
		else
			local partType = self:GetBodyPartTypeByFashionID(fashionID)
			allInitBodyPartList[partType] = self:GetBodyPartsByFashionID_Multi(fashionID)
		end
	end

	return allInitBodyPartList
end

---@private method 刷新基础外观数据
function FashionComponent:RefreshCurWearDataByDefaultFashion()
	local fashionID2SubTypeDataTable = Game.TableData.GetFashionID2SubTypeDataTable()
	
	table.clear(self.CurWearFashionList)
	table.clear(self.CurWearBodyPartList)

	for fashionID, v in pairs(self:GetCurWearFashionAttribute()) do
		local fashionSubType = fashionID2SubTypeDataTable[fashionID]
		self.CurWearFashionList[fashionSubType] = fashionID
	end
	for fashionID, v in pairs(self:GetCurWearAccessoryAttribute()) do
		local fashionSubType = fashionID2SubTypeDataTable[fashionID]
		self.CurWearFashionList[fashionSubType] = fashionID
	end

	self.CurWearBodyPartList = utils.deepCopyTable(self.InitBaseAllBodyParts)
	self:RefreshSuitWearAll()
end

---@private method 刷新基础外观数据
function FashionComponent:RefreshCurWearDataByCustomFashion(fashionIDList)
	table.clear(self.CurWearFashionList)
	table.clear(self.CurWearBodyPartList)

	for _, fashionID in pairs(fashionIDList) do
		local config = Game.TableData.GetFashionDataRow(fashionID)
		self.CurWearFashionList[config.SubType] = fashionID
	end

	self.CurWearBodyPartList = utils.deepCopyTable(self.InitBaseAllBodyParts)
	
	
end

--endregion


--region 时装---配饰

---@private method 是否是多挂接点的配饰
function FashionComponent:IsAccessoryMultiSocketByFashionID(fashionID)
	local fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	if fashionConfig.Type == Enum.EAppearanceType2ID.Accessory then
		if self:IsAccessoryMultiSocketBySubType(fashionConfig.SubType) then
			return true
		end
	end

	return false
end

---@private method 是否是多挂接点的配饰
function FashionComponent:IsAccessoryMultiSocketBySubType(subType)
	if subType == Enum.EAppearanceSubType2ID.EarAcc or
		subType == Enum.EAppearanceSubType2ID.ShoulderAcc or
		subType == Enum.EAppearanceSubType2ID.WaistAcc
	then
		return true
	end

	return false
end

---@public method 刷新配饰偏移数据
function FashionComponent:RefreshFashionOffsetData(fashionID, posX, posY, posZ, roaX, roaY, roaZ, scaleX, scaleY, scaleZ)
	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	local partData = self.CurWearBodyPartList[partType]

	if self:IsAccessoryMultiSocketByFashionID(fashionID) then
		for tag, data in pairs(partData.PartsValue) do
			self:RefreshFashionTagOffset(tag, posX, posY, posZ, roaX, roaY, roaZ, scaleX, scaleY, scaleZ)
		end
	else
		self:RefreshFashionTagOffset(partData.Tag, posX, posY, posZ, roaX, roaY, roaZ, scaleX, scaleY, scaleZ)
	end
end


---@public method 刷新复数个配饰挂接点数据
function FashionComponent:RefreshFashionSocketData(fashionID, socketNameList, offset)
	local removeTagList = {}
	local partDataList = self:GetCurWearBodyPartDataByFashionID(fashionID)
	for i, partData in pairs(partDataList.PartsValue) do
		table.insert(removeTagList, partData.Tag)
	end
	
	local config = Game.TableData.GetFashionDataRow(fashionID)
	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	local addTagList = {
		Type = partType,
		PartsValue = {}
	}
	for i, socketName in pairs(socketNameList) do
		local key = string.format("%s_%s", Enum.EAvatarBodyPartTypeName[partType], i)
		addTagList.PartsValue[key] = {
			["ID"] = config.MeshID[1],
			["Offset"] = offset,
			["SocketName"] = socketName,
			["BodyPartType"] = partType,
			["Tag"] = key,
		}
	end

	self.CurChangeFashionData.fashionID = fashionID
	self.CurChangeFashionData.addTagList = addTagList
	self.CurChangeFashionData.removeTagList = removeTagList

	self:RefreshBodyPart_ModelInstancesInSpecificBodyType(
		self:GetBodyPartTypeByFashionID(fashionID), removeTagList, addTagList, nil, self, "RefreshFashionSocketDataCallBack"
	)
end

---@private method 刷新配饰挂接点数据回调
function FashionComponent:RefreshFashionSocketDataCallBack()
	self:RefreshBodyPartOnChange_Multi(self.CurChangeFashionData.removeTagList, self.CurChangeFashionData.addTagList)
	self:OnRefreshAvatarCallBack()
end

---@public method 修改某个时装部件tag的偏移
function FashionComponent:RefreshFashionTagOffset(tag, posX, posY, posZ, roaX, roaY, roaZ, scaleX, scaleY, scaleZ)
	local UMeshComponentClassId = Game.ObjectActorManager:GetIDByClass(UMeshComponent)
	local comID = self.CppEntity:KAPI_Actor_GetComponentByClassIDAndTag(UMeshComponentClassId, tag)
	self.CppEntity:KAPI_SceneID_SetRelativeTransform(comID, posX, posY, posZ, roaX, roaY, roaZ, scaleX, scaleY, scaleZ)
end

---@public method 修改某个时装部件tag的显隐
function FashionComponent:RefreshFashionTagVisible(tag, visible)
	local UMeshComponentClassId = Game.ObjectActorManager:GetIDByClass(UMeshComponent)
	local comID = self.CppEntity:KAPI_Actor_GetComponentByClassIDAndTag(UMeshComponentClassId, tag)
	self.CppEntity:KAPI_SceneID_SetHiddenInGame(comID, not visible, false)
end
--endregion

--region 时装---染色
---@public method 刷新服装染色(配饰不能染色)
function FashionComponent:RefreshFashionMakeUpData(fashionID, key, value)
	if next(self.CurChangeFashionData) then
		return
	end
	
	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	local overrideMakeUp = {
		MakeUpParamData = {},
		MakeUpResData = {},
	}
	overrideMakeUp.MakeUpParamData[key] = value

	self.CurChangeFashionData.partType = partType
	self.CurChangeFashionData.makeUpDataList = overrideMakeUp.MakeUpParamData
	self:RefreshBodyPart_OverrideMakeUpData(partType, overrideMakeUp, self, "RefreshFashionMakeUpCallBack")
end

---@public method 刷新服装染色
function FashionComponent:RefreshFashionMakeUpDataByList(fashionID, dataList)
	if next(self.CurChangeFashionData) then
		return
	end
	
	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	local overrideMakeUp = {
		MakeUpParamData = {},
		MakeUpResData = {},
	}

	for key, value in pairs(dataList) do
		overrideMakeUp.MakeUpParamData[key] = value
	end

	self.CurChangeFashionData.partType = partType
	self.CurChangeFashionData.makeUpDataList = overrideMakeUp.MakeUpParamData
	self:RefreshBodyPart_OverrideMakeUpData(partType, overrideMakeUp, self, "RefreshFashionMakeUpCallBack")
end

---@public method 重置并刷新服装染色
function FashionComponent:ResetAndRefreshBodyPartMakeUp(fashionID, dataList)
	if next(self.CurChangeFashionData) then
		return
	end

	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	local overrideMakeUp = {
		MakeUpParamData = {},
		MakeUpResData = {},
	}

	if self.InitBodyPartMakeupData[partType] and self.InitBodyPartMakeupData[partType][ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA] then
		for key, color in pairs(self.InitBodyPartMakeupData[partType][ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA]) do
			overrideMakeUp.MakeUpParamData[key] = FLinearColor(0,0,0,0)
		end
	end

	for key, value in pairs(dataList) do
		overrideMakeUp.MakeUpParamData[key] = value
	end

	self.CurChangeFashionData.partType = partType
	self.CurChangeFashionData.makeUpDataList = overrideMakeUp.MakeUpParamData
	self:RefreshBodyPart_OverrideMakeUpData(partType, overrideMakeUp, self, "RefreshFashionMakeUpCallBack")
end

---@public method 重置并刷新服装list染色
function FashionComponent:ResetAndRefreshBodyPartListMakeUp(bodyPartDataList)
	if next(self.CurChangeFashionData) then
		return
	end

	local overrideMakeUp = {}
	for partType, colorList in pairs(bodyPartDataList) do
		if self.InitBodyPartMakeupData[partType] and self.InitBodyPartMakeupData[partType][ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA] then
			if not overrideMakeUp[partType] then
				overrideMakeUp[partType] = {
					MakeUpParamData = {},
					MakeUpResData = {},
				}
			end
			for key, color in pairs(self.InitBodyPartMakeupData[partType][ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA]) do
				overrideMakeUp[partType].MakeUpParamData[key] = FLinearColor(0,0,0,0)
			end
		end
		
		for key, value in pairs(colorList) do
			overrideMakeUp[partType].MakeUpParamData[key] = value
		end
	end

	self.CurChangeFashionData.makeUpDataList = overrideMakeUp
	
	--todo  改一下接口
	self:RefreshBodyPart_BodyParts(nil, nil, overrideMakeUp, self, "RefreshFashionListMakeUpCallBack")
end

---@private method 还原某个部件的染色数据
function FashionComponent:ResetBodyPartMakeUp(fashionID)
	if next(self.CurChangeFashionData) then
		return
	end
	
	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	local bodyPartMakeupData = self.InitBodyPartMakeupData[partType]
	if not bodyPartMakeupData then
		return
	end

	local bodyPartMakeupDataParam = bodyPartMakeupData[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA]
	if not bodyPartMakeupDataParam then
		return
	end

	local overrideMakeUp = {
		MakeUpParamData = {},
		MakeUpResData = {},
	}
	for key, color in pairs(bodyPartMakeupDataParam) do
		overrideMakeUp.MakeUpParamData[key] = FLinearColor(0,0,0,0)
	end

	self.CurChangeFashionData.partType = partType
	self.CurChangeFashionData.makeUpDataList = overrideMakeUp.MakeUpParamData
	self:RefreshBodyPart_OverrideMakeUpData(partType, overrideMakeUp, self, "RefreshFashionMakeUpCallBack")
end

---@private method
function FashionComponent:RefreshFashionMakeUpCallBack()
	for key, value in pairs(self.CurChangeFashionData.makeUpDataList) do
		self:RefreshFashionSaveBodyPartMakeUp(self.CurChangeFashionData.partType, key, value)
	end

	table.clear(self.CurChangeFashionData)
end

---@private method
function FashionComponent:RefreshFashionListMakeUpCallBack()
	for partType, paramList in pairs(self.CurChangeFashionData.makeUpDataList) do
		for key, value in pairs(paramList.MakeUpParamData) do
			self:RefreshFashionSaveBodyPartMakeUp(partType, key, value)
		end
	end

	table.clear(self.CurChangeFashionData)
end

---@private method 还原某个部件的染色数据
function FashionComponent:RefreshFashionSaveBodyPartMakeUp(partType, key, value)
	local bodyPartMakeupData = self.InitBodyPartMakeupData[partType]
	if not bodyPartMakeupData then
		self.InitBodyPartMakeupData[partType] = {
			[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA] = {},
			[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_RES_DATA] = {},
		}
		bodyPartMakeupData = self.InitBodyPartMakeupData[partType]
	end

	local bodyPartMakeupDataParam = bodyPartMakeupData[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA]
	if not bodyPartMakeupDataParam then
		return
	end

	bodyPartMakeupDataParam[key] = value
end

--endregion

--region 时装---周边
---@private method 初始刷新周边数据
function FashionComponent:RefreshAllAround()
	if not self.playerAppearance then
		return
	end
	
	local curWearAround = self:GetCurWearAroundAttribute()
	if not curWearAround or not next(curWearAround) then
		return
	end

	table.clear(self.CurWearAroundList)
	for fashionID, _ in pairs(curWearAround) do
		self:WearAround(fashionID)
	end
end

---@public method 穿周边
function FashionComponent:WearAround(aroundID)
	local config = Game.TableData.GetFashionDataRow(aroundID)
	local curWearList = self:GetAroundBySubType(config.SubType)
	if curWearList and next(curWearList) then
		if curWearList[aroundID] then
			return
		end

		local maxCount = Game.FashionSystem:GetSubTypeCanMultiCount(config.SubType)
		if maxCount > 1 then
			if table.count(curWearList) >= maxCount then
				return
			end
		else
			local fashionID = next(curWearList)
			self:UnWearAround(fashionID)
		end
	end
	
	if config.SubType == Enum.EAppearanceSubType2ID.BodyEffect then
		self:WearAround_BodyEffect(aroundID)
	elseif config.SubType == Enum.EAppearanceSubType2ID.FootPrint then
		self:WearAround_FootEffect(aroundID)
	elseif config.SubType == Enum.EAppearanceSubType2ID.FashionIdle01 then
	end

	if not self.CurWearAroundList[config.SubType] then
		self.CurWearAroundList[config.SubType] = {}
	end
	self.CurWearAroundList[config.SubType][aroundID] = aroundID

	if self.OnFashionChangeEvent:IsBind() then
		self.OnFashionChangeEvent:Broadcast(self:uid())
	end
end

---@private method 
function FashionComponent:WearAround_BodyEffect(aroundID)
	local dataList = {}
	local config = Game.TableData.GetFashionDataRow(aroundID)
	for index, effectPath in ksbcipairs(config.EffectPath) do
		table.insert(dataList, {
			path = effectPath,
			socket = config.EffectSocket[index] or "root"
		})
	end

	self:RefreshAroundEffect_Body(nil)
	self:RefreshAroundEffect_Body(dataList)
end

---@private method
function FashionComponent:WearAround_FootEffect(aroundID)
	local dataList = {}
	local config = Game.TableData.GetFashionDataRow(aroundID)
	dataList[1] = config.EffectPath[1]
	dataList[2] = config.EffectPath[2] or config.EffectPath[1]
	
	self:RefreshAroundEffect_Foot(dataList)
end

---@public method 脱周边
function FashionComponent:UnWearAround(aroundID)
	if not self:IsWearFashion(aroundID) then
		return
	end

	local config = Game.TableData.GetFashionDataRow(aroundID)
	if config.SubType == Enum.EAppearanceSubType2ID.BodyEffect then
		self:UnWearAround_BodyEffect(aroundID)
	elseif config.SubType == Enum.EAppearanceSubType2ID.FootPrint then
		self:UnWearAround_FootEffect(aroundID)
	end
	self.CurWearAroundList[config.SubType][aroundID] = nil
end

---@public method 脱所有周边
function FashionComponent:UnWearAllAround()
	for i, fashionList in pairs(self.CurWearAroundList) do
		for _, fashionID in pairs(fashionList) do
			self:UnWearAround(fashionID)
		end
	end
	table.clear(self.CurWearAroundList)
end

---@private method
function FashionComponent:UnWearAround_BodyEffect(aroundID)
	self:RefreshAroundEffect_Body(nil)
end

---@private method
function FashionComponent:UnWearAround_FootEffect()
	self:RefreshAroundEffect_Foot(nil)
end

--endregion

--region 时装---动作
---@private method 动作override
function FashionComponent:RefreshFashionOverrideAnimData()
	local animOverride = nil
	local pantsFashionID = self:GetFashionBySubType(Enum.EAppearanceSubType2ID.Pants)
	if pantsFashionID then
		local config = Game.TableData.GetFashionDataRow(pantsFashionID)
		animOverride = config.AnimOverride
	end
	
	if not animOverride then
		local upperFashionID = self:GetFashionBySubType(Enum.EAppearanceSubType2ID.Upper)
		if upperFashionID then
			local config = Game.TableData.GetFashionDataRow(upperFashionID)
			animOverride = config.AnimOverride
		end
	end
	
	self:RefreshFashionOverrideAnim(animOverride)
end
--endregion


--region 一些公共接口
---@public method 时装是否穿戴
function FashionComponent:IsWearFashion(fashionID)
	local fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	if not fashionConfig then
		self:DebugFmt("Fashion: fashionConfig = nil, ID = %s", fashionID)
		return false
	end
	local newFashionSubType = fashionConfig.SubType
	if fashionConfig.Type == Enum.EAppearanceType2ID.Fashion or
		fashionConfig.Type == Enum.EAppearanceType2ID.Accessory
	then
		--套装的话，如果脱了一个，就相当于没穿套装
		return self.CurWearFashionList[newFashionSubType] == fashionID
	elseif fashionConfig.Type == Enum.EAppearanceType2ID.Around then
		return self.CurWearAroundList[newFashionSubType] and self.CurWearAroundList[newFashionSubType][fashionID] == fashionID
	end

	return false
end

---@public method 某个时装子类是否穿戴
function FashionComponent:IsWearSubTypeFashion(subType)
	local type = self:GetTypeBySubType(subType)
	if type == Enum.EAppearanceType2ID.Fashion or
		type == Enum.EAppearanceType2ID.Accessory
	then
		return self.CurWearFashionList[subType] ~= nil
	elseif type == Enum.EAppearanceType2ID.Around then
		return self.CurWearAroundList[subType] and table.count(self.CurWearAroundList) > 0
	end

	return false
end

---@public method 根据子类获取时装
function FashionComponent:GetFashionBySubType(subType)
	return self.CurWearFashionList[subType]
end

---@public method 根据子类获取周边
function FashionComponent:GetAroundBySubType(subType)
	return self.CurWearAroundList[subType]
end

---@public method 获取配置默认挂接点
function FashionComponent:GetDefaultSocketNameByAccConfig(id, type)
	local socketList = {}
	local accConfig = Game.TableData.GetFashionAccConfigDataRow(id)
	if not accConfig then
		self:ErrorFmt("Fashion:accConfig = nil, id = %s", id)
		return
	end
	local socketType = type or accConfig.DefaultSocket
	if socketType == 1 then
		table.insert(socketList, accConfig.SocketName[1])
	elseif socketType == 2 then
		table.insert(socketList, accConfig.SocketName[2])
	elseif socketType == 3 then
		table.insert(socketList, accConfig.SocketName[1])
		table.insert(socketList, accConfig.SocketName[2])
	end

	return socketList
end

---@public method 隐藏某个类型时装
function FashionComponent:SetFashionSubTypeVisible(subType, visible)
	local curFashionID = self.CurWearFashionList[subType]
	if not curFashionID then
		return
	end

	local bodyPartData = self:GetCurWearBodyPartDataByFashionID(curFashionID)
	if not bodyPartData then
		return
	end

	if self:IsAccessoryMultiSocketByFashionID(curFashionID) then
		for i, data in pairs(bodyPartData.PartsValue) do
			self:RefreshFashionTagVisible(data.Tag, visible)
		end
	else
		self:RefreshFashionTagVisible(bodyPartData.Tag, visible)
	end
end

---@public method 隐藏某个类型时装
function FashionComponent:IsSameFashionByEntity(otherEntity)
	if not otherEntity then
		self:ErrorFmt("Fashion: otherEntity = nil")
		return
	end

	local otherFashion = otherEntity.CurWearFashionList
	for subType, fashionID in pairs(otherFashion) do
		local curFashionID = self.CurWearFashionList[subType]
		if not curFashionID or curFashionID ~= fashionID then
			return false
		end
	end

	if table.count(otherFashion) ~= table.count(self.CurWearFashionList) then
		return false
	end

	local otherAround = otherEntity.CurWearAroundList
	for subType, otherFashionDict in pairs(otherAround) do
		local curFashionDict = self.CurWearAroundList[subType]
		if not curFashionDict or table.count(curFashionDict) ~= table.count(otherFashionDict) then
			return false
		end

		for i, fashionID in pairs(otherFashionDict) do
			if not curFashionDict[fashionID] then
				return false
			end
		end
	end

	if table.count(otherAround) ~= table.count(self.CurWearAroundList) then
		return false
	end

	return true
end

---@public method 根据子类获取大类
function FashionComponent:GetTypeBySubType(subType)
	local config = Game.TableData.GetAppearanceSubTypeRow(subType)
	return config.MainType
end

--endregion

--region 时装---private
---@private method 根据fashionID获取mesh部件类型（不支持套装）
function FashionComponent:GetBodyPartTypeByFashionID(fashionID)
	local fashionConfig = Game.TableData.GetFashionDataRow(fashionID)
	local profileName = self:GetBaseProfileName()
	local configPartData = Game.ActorAppearanceManager:GetModelPartLibData(profileName, fashionConfig.MeshID[1])
	if not configPartData then
		Log.ErrorFormat("Fashion: configPartData=nil, fashionID=%s, profileName=%s, MeshID=%s", fashionID, profileName, fashionConfig.MeshID[1])
		return nil
	end
	return configPartData.BodyPartType
end

---@private method
function FashionComponent:RefreshBodyPartOnChange_Single(removeBodyPartList, addBodyPartList)
	for partType, _ in pairs(removeBodyPartList) do
		self.CurWearBodyPartList[partType] = nil
	end
	for partType, bodyPartData in pairs(addBodyPartList) do
		self.CurWearBodyPartList[partType] = bodyPartData
	end
end

---@private method
function FashionComponent:RefreshBodyPartMakeUp(bodyPartMakeUpData)
	if not bodyPartMakeUpData then
		return
	end
	
	for partType, data in pairs(bodyPartMakeUpData) do
		self.InitBodyPartMakeupData[partType] = data
	end
end

---@private method
function FashionComponent:RefreshBodyPartOnChange_Multi(removeTagList, addTagList)
	for _, tag in pairs(removeTagList) do
		for partType, data in pairs(self.CurWearBodyPartList) do
			if AppearanceConst.IsMultiInstBodyPart(partType) then
				local partsValue = data.PartsValue
				for dataTag, bodyPartData in pairs(partsValue) do
					if dataTag == tag then
						partsValue[dataTag] = nil
						goto continue
					end
				end
			end
		end
		::continue::
	end

	if addTagList.PartsValue then
		local partType = addTagList.Type
		for tag, tagData in pairs(addTagList.PartsValue) do
			if not self.CurWearBodyPartList[partType] then
				self.CurWearBodyPartList[partType] = {
					Type = partType,
					PartsValue = {}
				}
			end

			self.CurWearBodyPartList[partType].PartsValue[tag] = tagData
		end
	end
end

---@private method 刷新套装是否穿戴齐了
function FashionComponent:RefreshSuitWearAll()
	local GetFashionDataRow = Game.TableData.GetFashionDataRow
	
	local suitID = self.CurWearFashionList[Enum.EAppearanceSubType2ID.Suit]
	if suitID then
		local fashionConfig = GetFashionDataRow(suitID)
		for _, subFashionID in ksbcipairs(fashionConfig.SuitComps) do
			local subFashionConfig = GetFashionDataRow(subFashionID)
			local subFashionSubType = subFashionConfig.SubType
			if self.CurWearFashionList[subFashionSubType] ~= subFashionID and subFashionConfig.Type == Enum.EAppearanceType2ID.Fashion then
				self.CurWearFashionList[Enum.EAppearanceSubType2ID.Suit] = nil
				break
			end
		end
	else
		--找到一个子项，然后获取她对应的套装，如果全了，就穿上
		for i, fashionID in pairs(self.CurWearFashionList) do
			local fashionConfig = GetFashionDataRow(fashionID)
			local fashionSuit = fashionConfig.SuitID
			local suitConfig = GetFashionDataRow(fashionSuit)
			if suitConfig then
				local wearAll = true
				for _, subID in ksbcpairs(suitConfig.SuitComps) do
					local subConfig = GetFashionDataRow(subID)
					if self.CurWearFashionList[subConfig.SubType] ~= subID then
						wearAll = false
						break
					end
				end

				if wearAll then
					self.CurWearFashionList[Enum.EAppearanceSubType2ID.Suit] = fashionSuit
				end
			end
		end
	end
end

---@private method
function FashionComponent:GetCurWearBodyPartDataByFashionID(fashionID)
	local partType = self:GetBodyPartTypeByFashionID(fashionID)
	return self.CurWearBodyPartList[partType]
end

---@private method 检查队列，进行后续换装
function FashionComponent:CheckFashionChangeQueue()
	if not self.CacheChangeFashion then
		return
	end

	self[self.CacheChangeFashion.functionName](self, self.CacheChangeFashion.fashionData, self.CacheChangeFashion.paramData)
	self.CacheChangeFashion = nil
end

---@private method
function FashionComponent:CheckWearFashionEqualAttributeEnterWorld()
	if not self.playerAppearance then
		return
	end
	
	table.clear(self.CurChangeFashionData)
	if not self:IsWearAllFashionAttribute() then
		self:RevertAllOnAttributeChange()
	end
end

---@private method 
function FashionComponent:IsWearAllFashionAttribute()
	for fashionID, saveData in pairs(self:GetCurWearFashionAttribute()) do
		if not self:IsWearFashion(fashionID) then
			return false
		end
	end

	for fashionID, saveData in pairs(self:GetCurWearAroundAttribute()) do
		if not self:IsWearFashion(fashionID) then
			return false
		end
	end
	
	return true
end

--endregion


--region 外观自定义数据处理
---@private method 刷新基础自定义外观数据
function FashionComponent:RefreshBaseCustomAppearanceData(forceInit)
	if not self.InitBaseAllBodyParts or forceInit then
		self.InitBaseAllBodyParts = {}
		self.InitBodyPartMakeupData = {}

		self:RefreshBaseCustomFaceData()
		self:RefreshBaseCustomFashionData()
		self:RefreshBaseCustomBodyPartMakeUpList()

		self:SetBaseAppearanceData(
			self.InitBaseAllBodyParts, 
			self.InitFaceAndBodyPresetModelID, self.InitFacePresetModelID,
			self.InitBodyShapeCompactData, self.InitBodyPartMakeupData,
			self.InitAroundData, self.InitFashionOverrideAnimData
		)
	end
end

-----@private method 刷新基础自定义捏脸数据
function FashionComponent:RefreshBaseCustomFaceData()
	--捏脸自定义数据
	if string.isEmpty(self.CustomFaceData) then
		return
	end
	
	local customRoleFaceData = Game.CustomRoleSystem:DeSerializationCustomRoleData(self.CustomFaceData, true)
	self.CustomRoleFaceData = customRoleFaceData
	
	local hairPartData = self:GetBaseCustomHairPartData()
	if hairPartData then
		self.InitBaseAllBodyParts[Enum.EAvatarBodyPartType.Hair] = hairPartData
	end

	if not string.isEmpty(customRoleFaceData.FaceData) then
		self.InitBodyShapeCompactData = customRoleFaceData.FaceData
	end

	if not string.isEmpty(customRoleFaceData.MakeUpParamData) or not string.isEmpty(customRoleFaceData.MakeUpResData) then
		self.InitBodyPartMakeupData[Enum.EAvatarBodyPartType.Head] = {
			[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA] = customRoleFaceData.MakeUpParamData,
			[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_RES_DATA]   = customRoleFaceData.MakeUpResData
		}
	end

	if customRoleFaceData.ModelPresetID then
		self.InitFaceAndBodyPresetModelID = Game.TableData.GetFacadeControlDataRow(customRoleFaceData.ModelPresetID).ModelID
	end

	if customRoleFaceData.HeadPresetFacadeID then
		self.InitFacePresetModelID = Game.TableData.GetFacadeControlDataRow(customRoleFaceData.HeadPresetFacadeID).ModelID
	end
end

-----@private method 刷新基础自定义时装数据
function FashionComponent:RefreshBaseCustomFashionData()
	--1、有自定义时装
	--2、无自定义时装
	if self.playerAppearance and next(self.playerAppearance) then
		self:InitAllBodyPartByDefaultFashion()
		self.InitAroundData = self:GetAllAroundDataByDefaultAround()
		self.InitFashionOverrideAnimData = self:GetFashionOverrideAnimID()
	else
		for type, value in pairs(self:GetBaseAllBodyParts()) do
			if not self.InitBaseAllBodyParts[type] and type ~= Enum.EAvatarBodyPartType.Head then
				self.InitBaseAllBodyParts[type] = value
			end
		end
	end
end

---@private method 刷新基础自定义染色
function FashionComponent:RefreshBaseCustomBodyPartMakeUpList()
	local fashionID2SubTypeDataTable = Game.TableData.GetFashionID2SubTypeDataTable()

	if self.playerAppearance and next(self.playerAppearance) then
		for fashionID, saveData in pairs(self:GetCurWearFashionAttribute()) do
			if saveData.StainData and next(saveData.StainData) then
				local fashionSubType = fashionID2SubTypeDataTable[fashionID]
				if fashionSubType ~= Enum.EAppearanceSubType2ID.Suit then
					local partType = self:GetBodyPartTypeByFashionID(fashionID)
					self.InitBodyPartMakeupData[partType] = {
						[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_PARAM_DATA] = self:GetMakeUpDataBySaveData(saveData.StainData),
						[ViewControlConst.MAKEUP_DATA_KEY.MAKEUP_RES_DATA] = {}
					}
				end
			end
		end
	end
end

---@private method 获取基础自定义头发部件数据
function FashionComponent:GetBaseCustomHairPartData()
	local customRoleData = self.CustomRoleFaceData
	if not customRoleData.HairFashionID and not customRoleData.HairMakeUpID then
		return nil
	end

	local modelHairMeshID = nil
	local modelHairMakeUpID = nil
	local modelID = self:GetBaseModelID()
	local modelData = Game.ActorAppearanceManager.AvatarModelLib[modelID]
	for k, data in pairs(modelData.BodyPartList) do
		if data.Type == Enum.EAvatarBodyPartType.Hair then
			modelHairMeshID = data.ID
			modelHairMakeUpID = data.MakeupID
			break
		end
	end

	local fashionConfig = Game.TableData.GetFashionDataRow(customRoleData.HairFashionID)
	if customRoleData.HairFashionID then
		modelHairMeshID = fashionConfig.MeshID[1]
		modelHairMakeUpID = fashionConfig.MakeUpID[1]
	end

	if customRoleData.HairMakeUpID then
		modelHairMakeUpID = customRoleData.HairMakeUpID
	end

	return { ["ID"] = modelHairMeshID, ["MakeupID"] = modelHairMakeUpID }
end


---@private method 获取基础自定义头部部件数据
function FashionComponent:GetBaseCustomHeadPartData()
	local facadeID = self.CustomRoleFaceData.HeadPresetFacadeID or self.CustomRoleFaceData.ModelPresetID
	local facadeData = Game.TableData.GetFacadeControlDataRow(facadeID)
	local avatarModelData = Game.ActorAppearanceManager:GetModelLibData(facadeData.ModelID)
	for i, bodyPartData in pairs(avatarModelData.BodyPartList) do
		if bodyPartData.Type == Enum.EAvatarBodyPartType.Head then
			return bodyPartData
		end
	end

	self:DebugErrorFmt("Fashion: baseCustomHeadPartData = nil, ModelPresetID=%s, HeadPresetFacadeID = %s", 
		self.CustomRoleFaceData.ModelPresetID, self.CustomRoleFaceData.HeadPresetFacadeID
	)
	return nil
end
--endregion


--region 上下行相关
--衣柜数据
function FashionComponent:RetGetWholeWardrobeData(fashionData)
	Game.FashionSystem:RetGetWholeWardrobeData(fashionData)
end

---收藏
function FashionComponent:RetCollectAppearanceComp(fashionIDList)
	Game.FashionSystem:RetCollectAppearanceComp(fashionIDList)
end

--取消收藏
function FashionComponent:RetCancelCollectAppearanceComp(fashionIDList)
	Game.FashionSystem:RetCancelCollectAppearanceComp(fashionIDList)
end

--保存微调
--function FashionComponent:RetSaveFinetuneInfo(fashionID, fashionData)
--	Game.FashionSystem:RetSaveFinetuneInfo(fashionID, fashionData)
--end

--获取预设
function FashionComponent:RetWholeAppearanceStategyData(presetDataList)
	Game.FashionSystem:RetWholeAppearanceStategyData(presetDataList)
end

--保存预设
function FashionComponent:RetSaveAppearanceStrategy(index, presetData)
	Game.FashionSystem:RetSaveAppearanceStrategy(index, presetData)
end

--预设改名
function FashionComponent:RetChangeAppearanceStrategyName(index, name)
	Game.FashionSystem:RetChangeAppearanceStrategyName(index, name)
end

--购买
function FashionComponent:RetBuyAppearanceComp(fashionCellList)
	Game.FashionSystem:RetBuyAppearanceComp(fashionCellList)
end

--衣柜单个元素变动
function FashionComponent:RetSaveWardrobeCell(fashionID, cellData)
	Game.FashionSystem:RetSaveWardrobeCell(fashionID, cellData)
end

--红点变动
function FashionComponent:RetChangeAppearanceNewFlag(subType)
	Game.FashionSystem:RetChangeAppearanceNewFlag(subType)
end

--预设使用回复
function FashionComponent:RetUseAppearanceStrategy(index)
	Game.FashionSystem:RetUseAppearanceStrategy(index)
end

--染色index回复
function FashionComponent:RetSetCurWearStain(fashionID, index)
	Game.FashionSystem:RetSetCurWearStain(fashionID, index)
end

--武器显隐
function FashionComponent:set_showWeapon(entity, new, old)
	Game.FashionSystem:RetSetWeaponShowStatus(new)
end

--饰品显隐
function FashionComponent:set_showAccessory(entity, new, old)
	Game.FashionSystem:RetSetAccessoryShowStatus(new)
end

--角色外观变更
function FashionComponent:set_playerAppearance(entity, new, old)
	self:DebugFmt("set_playerAppearance id=%s fashion=%s", self:uid(), table.tostring(new))
	
	if self.bInWorld then
		self:RevertAllOnAttributeChange()
	else
		self:DebugFmt("set_playerAppearance self.bInWorld = false")
		self.CacheChangeFashion = {
			functionName = "RevertDefaultFashion",
		}
	end
end


--奖励等级改变
function FashionComponent:set_fashionTreasure(entity, new, old)
	self:DebugFmt("set_fashionTreasure")
	Game.FashionSystem:RetGetFashionTreasure()
end

--预设选择改变
function FashionComponent:set_currentStrategy(entity, new, old)
	self:DebugFmt("set_currentStrategy")
	Game.FashionSystem:RetUseAppearanceStrategy(new)
end

--服务器红点改变
function FashionComponent:set_hasNewAppearanceComp(entity, new, old)
	self:DebugFmt("set_hasNewAppearanceComp  %s", new)
	Game.FashionSystem:RefreshRedPoint(new)
end

function FashionComponent:set_fashionValue(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_FASHION_VALUE_CHANGED, new)
end
--endregion

--region 界面外观上下行

-- 更换头像
function FashionComponent:set_portrait(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_PORTRAIT_CHANGED, old, new)
	-- Game.FashionSystem:RetChangeUIAppearance(Enum.EUIAppearanceType.Portrait, new)
end

-- 更换头像框
function FashionComponent:set_portraitFrame(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_PORTRAIT_FRAME_CHANGED, old, new)
	-- Game.FashionSystem:RetChangeUIAppearance(Enum.EUIAppearanceType.PortraitFrame, new)
end

-- 更换聊天气泡
function FashionComponent:set_chatBubble(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_CHATBUBBLE_CHANGED, old, new)
	-- Game.FashionSystem:RetChangeUIAppearance(Enum.EUIAppearanceType.ChatBubble, new)
end

-- 更换组队底框
function FashionComponent:set_teamNameplate(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_TEAM_NAMEPLATE_CHANGED, old, new)
	-- Game.FashionSystem:RetChangeUIAppearance(Enum.EUIAppearanceType.TeamNameplate, new)
end

-- 更换组队动效
function FashionComponent:set_teamJoinAnimation(entity, new, old)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_TEAM_ANIMATION_CHANGED, old, new)
	-- Game.FashionSystem:RetChangeUIAppearance(Enum.EUIAppearanceType.TeamJoinAnimation, new)
end

-- 解锁新头像
function FashionComponent:OnMsgUnlockPortrait(entityID, portraitID)
	if entityID ~= Game.me.eid then return end
	Game.me.unlockedPortrait[portraitID] = true
	Game.FashionSystem:SetUIAppearanceRedPointStatus(Enum.EUIAppearanceType.Portrait, portraitID, true)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_UNLOCK_CHANGE, Enum.EUIAppearanceType.Portrait)
end

-- 解锁头像框
function FashionComponent:OnMsgUnlockPortraitFrame(entityID, portraitFrameID)
	if entityID ~= Game.me.eid then return end
	Game.me.unlockedPortraitFrame[portraitFrameID] = true
	Game.FashionSystem:SetUIAppearanceRedPointStatus(Enum.EUIAppearanceType.PortraitFrame, portraitFrameID, true)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_UNLOCK_CHANGE, Enum.EUIAppearanceType.PortraitFrame)
end

-- 解锁聊天气泡
function FashionComponent:OnMsgUnlockChatBubble(entityID, chatBubbleID)
	if entityID ~= Game.me.eid then return end
	Game.me.unlockedChatBubble[chatBubbleID] = true
	Game.FashionSystem:SetUIAppearanceRedPointStatus(Enum.EUIAppearanceType.ChatBubble, chatBubbleID, true)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_UNLOCK_CHANGE, Enum.EUIAppearanceType.ChatBubble)
end

-- 解锁组队底框
function FashionComponent:OnMsgUnlockTeamNameplate(entityID, teamNameplateID)
	if entityID ~= Game.me.eid then return end
	Game.me.unlockedTeamNameplate[teamNameplateID] = true
	Game.FashionSystem:SetUIAppearanceRedPointStatus(Enum.EUIAppearanceType.TeamNameplate, teamNameplateID, true)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_UNLOCK_CHANGE, Enum.EUIAppearanceType.TeamNameplate)
end

-- 解锁组队动效
function FashionComponent:OnMsgUnlockTeamJoinAnimation(entityID, teamJoinAnimationID)
	if entityID ~= Game.me.eid then return end
	Game.me.unlockedTeamJoinAnimation[teamJoinAnimationID] = true
	Game.FashionSystem:SetUIAppearanceRedPointStatus(Enum.EUIAppearanceType.TeamJoinAnimation, teamJoinAnimationID, true)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_UIAPPEARENCE_UNLOCK_CHANGE, Enum.EUIAppearanceType.TeamJoinAnimation)
end

--endregion 界面外观上下行

return FashionComponent