--- update by <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com: 2025/5/12，日程系统废弃，清理所有相关代码
ScheduleComponent = DefineComponent("ScheduleComponent")

--client
function ScheduleComponent:onGetTarotTasksInfo(NEWBIE_PROGRESS_INFO, SCHEDULE_TASKS_INFO)
    --Game.ScheduleSystem:OnGetScheduleTasks(NEWBIE_PROGRESS_INFO, SCHEDULE_TASKS_INFO)
end

function ScheduleComponent:onUpdateTarotProgressInfo(NEWBIE_PROGRESS_INFO)
    --Game.ScheduleSystem:OnUpdateProgressInfo(NEWBIE_PROGRESS_INFO)
end

function ScheduleComponent:onUpdateTarotTaskInfo(SCHEDULE_TASK_INFO)
    --Game.ScheduleSystem:OnUpdateTarotTaskInfo(SCHEDULE_TASK_INFO)
end

function ScheduleComponent:retGetFateRevelationReward(errorCode)
    --Game.ScheduleSystem:OnGetFateRevelationR<PERSON><PERSON>(errorCode)
end

--塔罗牌活跃度奖励领取结果
function ScheduleComponent:retGetTarotTaskReward(taskId, errorCode)
    --Game.ScheduleSystem:OnGetTarotTaskReward(taskId, errorCode)
end

--日常进度领取结果
function ScheduleComponent:retGetTarotStageReward(stageId, errorCode)
    --Game.ScheduleSystem:OnGetTarotStageReward(stageId, errorCode)
end

--命运启示同步
function ScheduleComponent:syncFateRevelationTaskInfo(REVELATION_TASK_INFO)
    --Game.ScheduleSystem:SyncFateRevelationTaskInfo(REVELATION_TASK_INFO)
end

function ScheduleComponent:onUpdateDailyTarotTasksInfo(SCHEDULE_TASKS_INFO)
    --Game.ScheduleSystem:OnUpdateDailyTarotTasksInfo(SCHEDULE_TASKS_INFO)
end

function ScheduleComponent:syncIsScheduleFirstOpen()
    --Game.ScheduleSystem:SyncIsScheduleFirstOpen()
end

function ScheduleComponent:retCheckIsScheduleFirstOpen(bool)
    
end

--server
--领取对应卡牌活跃度奖励
function ScheduleComponent:reqGetTarotTaskReward(TASK_ID)
    --self.remote.reqGetTarotTaskReward(TASK_ID)
end
--重新随机任务
function ScheduleComponent:reqManualRefreshScheduleDailyTasks()
    --self.remote.reqManualRefreshScheduleDailyTasks()
end
--领取日常进度奖励
function ScheduleComponent:reqGetTarotStageReward(stageId)
    --self.remote.reqGetTarotStageReward(stageId)
end
--领取
function ScheduleComponent:reqGetFateRevelationReward(Id)
    --self.remote.reqGetFateRevelationReward(Id)
end

function ScheduleComponent:reqCheckIsScheduleFirstOpen(bool)
    --self.remote.reqCheckIsScheduleFirstOpen(bool)
end



return ScheduleComponent
