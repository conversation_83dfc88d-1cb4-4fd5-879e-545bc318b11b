IdolExploreComponent = DefineComponent("IdolExploreComponent")

function IdolExploreComponent:ctor()

end

function IdolExploreComponent:dtor()

end

function IdolExploreComponent:__component_AfterEnterWorld__()
	local LocalSpace = Game.NetworkManager.GetLocalSpace()
	local CurrentMapID = Game.TableData.GetGoddessLeadDataRow("MAP_ID")
	if LocalSpace ~= nil and LocalSpace.mapID == tonumber(CurrentMapID) then
		if LocalSpace.planeID == Game.TableData.GetGoddessFourthRow("INIT_SPACE_ID") then
			Game.GoddessExploreSystem:OnPlayerLoaded()
		else
			Game.HUDSystem:HideUI("HUD_MiniGame_ItemCount")
		end
		Game.GoddessExploreSystem:RemoveAchievementListener()
		Game.GoddessExploreSystem:SetupAchievementListener()
	end
end

function IdolExploreComponent:__component_ExitWorld__()
	local LocalSpace = Game.NetworkManager.GetLocalSpace()
	local CurrentMapID = Game.TableData.GetGoddessLeadDataRow("MAP_ID")
	if LocalSpace ~= nil and LocalSpace.mapID == tonumber(CurrentMapID) then
		Game.GoddessExploreSystem:RemoveAchievementListener()
	end
end

function IdolExploreComponent:OnMsgPlayIdol3Effect()
	--local BeamPosList = Game.TableData.GetGoddessThirdRow("BEAM_POS")
	--local InsId = Game.TableData.GetGoddessThirdRow("IDOL_ID")
	--local SceneActorEntity = Game.LSceneActorEntityManager:GetLSceneActorFromInsID(InsId)
	--SceneActorEntity.BeamEffectID = SceneActorEntity:PlayNiagaraEffectAtLocation(SceneActorEntity.SceneConf.NiagaraAsset,
		--FTransform(FRotator(0):ToQuat(), FVector(BeamPosList[1], BeamPosList[2], BeamPosList[3])))
	self:ReqGenIdol3Box()
end

function IdolExploreComponent:ReqChangeIdolExploreStatus()
	self.remote.ReqChangeIdolExploreStatus()
end

function IdolExploreComponent:ReqCompleteIdolExploration(GoddessId)
	self.remote.ReqCompleteIdolExploration(GoddessId)
end

function IdolExploreComponent:ReqTeleportToPlane(GoddessId, PlaneId)
	self.remote.ReqTeleportToPlane(GoddessId, tonumber(PlaneId))
end

function IdolExploreComponent:ReqGenIdol3Box()
	self.remote.ReqGenIdol3Box()
end

function IdolExploreComponent:ReqInteractBallAddBuff()
	self.remote.ReqInteractBallAddBuff()
end

return IdolExploreComponent