--- @class PartnerComponent
PartnerComponent = DefineComponent("PartnerComponent")

function PartnerComponent:ReqSyncPartners()
    self:Debug("PartnerComponent:ReqSyncPartners")
	self.remote.ReqSyncPartners()
end

function PartnerComponent:RetSyncPartners(Result, PartnerInfos)
    --self:Debug("PartnerComponent:RetSyncPartners ", Result.Code, Result.Trace, CommonUtils.tprint(PartnerInfos)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.NetworkManager.HandleRequestRet("SyncPartners ", Result, PartnerInfos)
end

function PartnerComponent:ReqSyncPartnerDetail(PartnerID)
    self:Debug("PartnerComponent:ReqSyncPartnerDetail ", PartnerID)
	self.remote.ReqSyncPartnerDetail(PartnerID)
end

function PartnerComponent:RetSyncPartnerDetail(Result, PartnerID, PartnerDetail)
    --self:Debug("PartnerComponent:RetSyncPartnerDetail ", Result.Code, Result.Trace, PartnerID, CommonUtils.tprint(PartnerDetail)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.NetworkManager.HandleRequestRet("SyncPartnerDetail ", Result, PartnerID, PartnerDetail)
end

function PartnerComponent:ReqChangeCurPartner(SlotID, PartnerID)
    self:Debug("PartnerComponent:ReqChangeCurPartner ", SlotID, PartnerID)
	self.remote.ReqChangeCurPartner(SlotID, PartnerID)
end

function PartnerComponent:RetChangeCurPartner(Result, CurPartners)
    --self:Debug("PartnerComponent:RetChangeCurPartner ", Result.Code, Result.Trace, CommonUtils.tprint(CurPartners)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
    Game.NetworkManager.HandleRequestRet("ChangeCurPartner ", Result, CurPartners)
end

function PartnerComponent:ReqUpgradePartnerSkillLv(PartnerID)
    self:Debug("PartnerComponent:ReqUpgradePartnerSkill ", PartnerID)
	self.remote.ReqUpgradePartnerSkill(PartnerID)
end

function PartnerComponent:RetUpgradePartnerSkillLv(Result, PartnerID, CurLv)
    self:Debug("PartnerComponent:RetUpgradePartnerSkill ", Result.Code, Result.Trace, PartnerID, CurLv)
    Game.NetworkManager.HandleRequestRet("UpgradePartnerSkillLv ", Result, PartnerID, CurLv)
end


function PartnerComponent:ReqUpgradePartnerLv(PartnerID, TargetLv)
    self:Debug("PartnerComponent:ReqUpgradePartnerLv ", PartnerID, TargetLv)
	self.remote.ReqUpgradePartnerLv(PartnerID, TargetLv)
end

function PartnerComponent:RetUpgradePartnerLv(Result, PartnerID, CurLv)
    self:Debug("PartnerComponent:RetUpgradePartnerLv ", Result.Code, Result.Trace, PartnerID, CurLv)
    Game.NetworkManager.HandleRequestRet("UpgradePartnerLv ", Result, PartnerID, CurLv)
end

function PartnerComponent:OnMsgSyncCurPartners(CurPartners)
    --self:Debug("PartnerComponent:OnMsgSyncCurPartners", CommonUtils.tprint(CurPartners)) 性能需求禁用CommonUtils.tprint，一定需要的话自行ignore
end

return PartnerComponent
