HomeComponent = DefineComponent("HomeComponent")

function HomeComponent:ctor()
end

function HomeComponent:dtor()
end

function HomeComponent:OnMsgHomeBuildingSync(MaxFurnitureID, FurnitureData, BuildingData, destroyedTreeList, destroyedBarrier)
    Game.ManorSystem.ManorBuildModule:OnMsgHomeBuildingSync(MaxFurnitureID, FurnitureData, BuildingData, destroyedTreeList, destroyedBarrier)
end

function HomeComponent:OnMsgHomeFurnitureSync(FurnitureDict)
    Game.ManorSystem:OnMsgHomeFurnitureSync(FurnitureDict)
end

function HomeComponent:OnMsgCurrentHomeInfo(roleName)
	Game.ManorSystem.ManorInfoModule:OnMsgCurrentHomeInfo(roleName)
end

function HomeComponent:RetUpdateBuildingFrame(Result, BuildingFrameList)
	Game.ManorSystem.ManorBuildModule:RetUpdateBuildingFrame(Result, BuildingFrameList)
end

function HomeComponent:RetAddBuilding(Result, FurnitureID)
	Game.ManorSystem.ManorBuildModule:RetAddBuilding(Result, FurnitureID)
end

function HomeComponent:RetClearBuilding(Result)
	Game.ManorSystem.ManorBuildModule:RetClearBuilding(Result)
end

function HomeComponent:RetDelBuilding(Result, buildingID)
	Game.ManorSystem.ManorBuildModule:RetDelBuilding(Result, buildingID)
end

function HomeComponent:RetUpdateBuilding(Result, buildingID)
	Game.ManorSystem.ManorBuildModule:RetUpdateBuilding(Result, buildingID)
end

function HomeComponent:RetSetBuildingFrame(Result)
	Game.ManorSystem.ManorPCGModule:RetSetBuildingFrame(Result)
end

function HomeComponent:RetUnlockFurniture(Result, FurnitureID)
	Game.ManorSystem.ManorBuildStoreModule:RetUnlockFurniture(Result, FurnitureID)
end

function HomeComponent:RetBuyFurniture(Result, FurnitureID, Count)
	Game.ManorSystem.ManorBuildStoreModule:RetBuyFurniture(Result, FurnitureID, Count)
end

function HomeComponent:RetSellFurniture(Result, FurnitureID, Count)
	Game.ManorSystem.ManorBuildStoreModule:RetSellFurniture(Result, FurnitureID, Count)
end

function HomeComponent:RetHomeUpgrade(Result)
	if Result ~= Game.ErrorCodeConst.NO_ERR then
		Log.ErrorFormat("ReqHomeUpgrade failed Code=%s", Result)
		return
	end

	Log.DebugFormat("Upgrade Manor Success, Level before upgrade=%s, level after upgrade=%s", Game.me.HomelandLevel - 1, Game.me.HomelandLevel)
	Game.GlobalEventSystem:Publish(EEventTypesV2.ON_MANOR_LEVEL_UPGRADE)
	Game.ManorSystem:OnHomeUpgradeSucc()
	Game.ManorSystem.ManorInfoModule:UpdateRedDotStatus()
	Game.NewUIManager:OpenPanel(UIPanelConfig.UpgradePopup_Panel)
end

function HomeComponent:RetModifyHomeName(Result)
	Game.ManorSystem.ManorInfoModule:RetModifyHomeName(Result)
end

function HomeComponent:RetModifyHomeIntroduction(Result)
	Game.ManorSystem.ManorInfoModule:RetModifyHomeIntroduction(Result)
end

function HomeComponent:RetFriendsHomeInfo(Result, FRIEND_HOME_INFO_LIST)
	Game.ManorSystem.ManorInfoModule:RetFriendsHomeInfo(Result, FRIEND_HOME_INFO_LIST)
end

function HomeComponent:RetIsHomeUnlock(Result, ID, IsUnlocked)
	Game.ManorSystem.ManorInfoModule:RetIsHomeUnlock(Result, ID, IsUnlocked)
end

function HomeComponent:OnMsgAddBuilding(arg0_BUILDING_INFO)
end

function HomeComponent:OnMsgUpdateBuilding(arg0_BUILDING_INFO)
end

function HomeComponent:OnMsgDelBuilding(arg0_UINT)
end

function HomeComponent:OnMsgSetBuildingFrame(arg0_BUILDING_FRAME)
end

function HomeComponent:OnMsgUpdateBuildingFrame(arg0_BUILDING_FRAME_LIST)
end

function HomeComponent:OnMsgClearBuilding()
end

--region 属性
function HomeComponent:set_HomelandLevel(ent, new, old)
	Game.ManorSystem.ManorWorkshopModule:RefreshWorkshopRedDot()
end
--endregion

return HomeComponent
