LUnitProxyComponent = DefineComponent("LUnitProxyComponent")

local LogicUnitConst = kg_require("Shared.Const.LogicUnitConst")

local LOGIC_UNIT_DESTROY_REASON = LogicUnitConst.LOGIC_UNIT_DESTROY_REASON  -- luacheck: ignore
local LOGIC_UNIT_TYPE = LogicUnitConst.LOGIC_UNIT_TYPE

LUnitProxyComponent.LR_ExtraOffset = M3D.Rotator()
LUnitProxyComponent.LV_Dir = M3D.Vec3()

-- region Important
-- 构造
function LUnitProxyComponent:ctor()
    -- Unit列表 dict<serverInstId, localEntityId>
    self.UnitServerIDToLocalID = {}

    -- 子弹位置
    self.LV_Bullet = M3D.Vec3()

    -- 服务器同步位置
    self.LV_Server = M3D.Vec3()

    -- 子弹起点额外偏移（对于必中子弹，客户端自己计算起点)
    self.LT_BulletExtraOffset = M3D.Transform()

    -- 子弹起始朝向
    self.LR_Bullet = M3D.Rotator()

    self.BulletServerToLocalMap = {}
end

function LUnitProxyComponent:dtor()
    for instID, _ in pairs(self.UnitServerIDToLocalID) do
        self:DestroyLogicUnit(instID, LOGIC_UNIT_DESTROY_REASON.LOCAL_INSTIGATOR_DESTROY)
    end

    for insID, _ in pairs(self.BulletServerToLocalMap) do
        self:DestroyBullet(insID)
    end
end

-- endregion Important



-- region Core
function LUnitProxyComponent:SpawnClientUnit(InClassName, InSData, InTData)
    local localEID = self:GetLogicUnitLocalEID(InSData.InstID)
    if localEID ~= nil then
        -- 已经创建了的法术场，无需再同步
        return
    end
    local InitLoc = M3D.Vec3()
    local InitRot = M3D.Rotator()

    InitLoc:Pack(InSData.Position.X, InSData.Position.Y, InSData.Position.Z)
    InitRot:Pack(0.0, InSData.Yaw, 0.0)

    local ProxyData = {
        Data = InTData,
        Stage = InSData.Stage,
        InstID = InSData.InstID,
        LEID = InSData.LauncherID,
        IEID = InSData.instigatorID or InSData.InstigatorID,
        Level = InSData.level or InSData.UnitLevel,
        RootAbilityID = InSData.rootSkillID or InSData.RootAbilityID,
        FinalOwnerID = InSData.instigatorID or InSData.InstigatorID,
        ATEID = InSData.AttachedTargetID,
        InitLocation = InitLoc,
        InitRotation = InitRot,
        bLocalMode = InSData.bLocalMode,
        ExtraData = InSData.ExtraData,
        BornTimeStamp = InSData.BornTimeStamp,
        bFollowAttachTargetYaw = InSData.bFollowAttachTargetYaw,
        bCheckGround = InSData.bCheckGround,
    }

    local Unit = Game.EntityManager:CreateLocalEntity(InClassName, ProxyData)

    if (Unit ~= nil) then
        local lifeTime = InSData.LifeExpiredTimeStamp - InSData.BornTimeStamp
        Unit:SetLifeTime(lifeTime)
        self.UnitServerIDToLocalID[InSData.InstID] = Unit:uid()
    end
end

function LUnitProxyComponent:GetLogicUnitLocalEID(InstID)
    return self.UnitServerIDToLocalID[InstID]
end

function LUnitProxyComponent:GetLogicUnitByServerInstID(InstID)
    local EID = self.UnitServerIDToLocalID[InstID]
    if EID == nil then
        return nil
    end

    return Game.EntityManager:GetEntityByIntID(EID)
end

function LUnitProxyComponent:OnLogicUnitStageChange(InstID, NewStage)
    local Unit = self:GetLogicUnitByServerInstID(InstID)
    if Unit then
        Unit:OnStageChanged(NewStage, Unit.Stage)
    end
end

function LUnitProxyComponent:DestroyLogicUnit(InstID, Reason)
    local Unit = self:GetLogicUnitByServerInstID(InstID)
    if Unit then
        Unit:TryDestroyUnit(Reason)
    end

    self.UnitServerIDToLocalID[InstID] = nil
end

function LUnitProxyComponent:HandleBulletLoseTarget(InstID)
     if not InstID then
        return
    end
    local BulletEntityID = self.BulletServerToLocalMap[InstID]
    if not BulletEntityID then
        return
    end
    local BulletEntity = Game.EntityManager:getEntity(BulletEntityID)
    if BulletEntity then
        BulletEntity:LoseTarget()
    end

end

function LUnitProxyComponent:SpawnBullet(BulletInfo, InstID)
    -- BulletInfo 中有FinalOwnerID、BulletID、LifeTime
    BulletInfo.FinalOwnerID = BulletInfo.FinalOwnerID or self:uid()
    -- local BulletlRecord = Game.CombatUnitManager:GenerateCombatUnitParam(CombatUnitConfig.CombatUnitType.Bullet, BulletInfo)
    -- if table.isInArray(AbilityConst.NeedTargetBulletType, BulletlRecord.BulletTypeInt) then
    --    if not Game.EntityManager:GetEntityByIntID(BulletInfo.TargetEID) and not BulletlRecord.TargetPosFromServer then
    --        self:Warning("Bullet Need Vaild Target!!!!!!")
    --        return
    --    end
    -- end
    -- local BulletEntity = Game.CombatUnitManager:SpawnCombatUnit(CombatUnitConfig.CombatUnitType.Bullet, self:uid(), BulletlRecord)
    local BulletEntity = Game.EntityManager:CreateLocalEntity("BulletEntity", BulletInfo)
    if BulletEntity == nil then
        return
    end
    if InstID then
        self.BulletServerToLocalMap[InstID] = BulletEntity:uid()
    end
end

function LUnitProxyComponent:DestroyBullet(InstID, reason)
    if not InstID then
        return
    end
    local BulletEntityID = self.BulletServerToLocalMap[InstID]
    if not BulletEntityID then
        return
    end
    local BulletEntity = Game.EntityManager:getEntity(BulletEntityID)
    if BulletEntity then
        BulletEntity.DestroyReason = reason
    end
    
    if BulletEntity and not BulletEntity.isDestroyed then
        BulletEntity:destroy()
    end

    -- clear the sid -> clientId map
    self.BulletServerToLocalMap[InstID] = nil
end

---@param InUnitType number: 创生物类型（LogicUnitConst.LOGIC_UNIT_TYPE)
---@param InTemplateID number: 创生物的模板id(法术场、光环、陷阱id)
---@param InUnitOrder number: optional(非空的话, <=0的话，逆序, 0表示最新创建, -1 表示次新，依次类推; > 0的话，顺序, 1表示第一个创建, 2第二额创建）
---                              (空的话，返回所有）
---@param OutUnitList table: 用于存储查询的结果(主要是为了复用, 避免每次创建一个table)
function LUnitProxyComponent:FindSelfCreateUnit(InUnitType, InTemplateID, InUnitOrder, OutUnitList)
    -- 客户端不支持交互物和队伍光环
    if InUnitType == LOGIC_UNIT_TYPE.Interactor then
        Log.ErrorFormat("LUnitProxyComponent:FindSelfCreateUnit() not support interactor")
        return
    end

    local LUnitNEntityMap = self.UnitServerIDToLocalID
    if LUnitNEntityMap then
        for _, unitUid in pairs(LUnitNEntityMap) do
            local LUnitInst = Game.EntityManager:GetEntityByIntID(unitUid)
            local activeUnitType = LUnitInst and LUnitInst.unitType
            if activeUnitType == LOGIC_UNIT_TYPE.VelocityField then
                activeUnitType = LOGIC_UNIT_TYPE.Aura     -- 引力场是特殊的光环
            end
            if activeUnitType and activeUnitType == InUnitType and LUnitInst.Data.ID == InTemplateID then
                OutUnitList[#OutUnitList + 1] = LUnitInst
            end
        end
    end

    -- not need search specific order unit, return all
    local unitListSize = #OutUnitList
    if InUnitOrder == nil or unitListSize == 0 then
        return OutUnitList
    end

    local realIdx = nil
    if InUnitOrder <= 0 then
        -- order by create time desc
        table.sort(OutUnitList, function(a, b)
            return a.BornTimeStamp > b.BornTimeStamp
        end)

        realIdx = LUnitProxyComponent.innerUnitIndexConvert(math.abs(InUnitOrder - 1), unitListSize)
    else
        -- order by create time asc
        table.sort(OutUnitList, function(a, b)
            return a.BornTimeStamp < b.BornTimeStamp
        end)
        realIdx = LUnitProxyComponent.innerUnitIndexConvert(InUnitOrder, unitListSize)
    end

    -- only return the target unit
    local targetUnit = OutUnitList[realIdx]

    table.clear(OutUnitList)
    OutUnitList[1] = targetUnit

    return OutUnitList
end

function LUnitProxyComponent.innerUnitIndexConvert(unitOrder, unitListSize)
    return math.fmod(unitOrder - 1, unitListSize) + 1
end

---@param InUnitType number: 创生物类型（LogicUnitConst.LOGIC_UNIT_TYPE)
---@param InTemplateID number: 创生物的模板id(法术场、光环、陷阱id)
function LUnitProxyComponent:DestroySelfCreateUnit(InUnitType, InTemplateID, Reason)
    Reason = Reason or LOGIC_UNIT_DESTROY_REASON.LOCAL_PREVIEW_DESTROY
    local LUnitNEntityMap = self.UnitServerIDToLocalID
    if LUnitNEntityMap then
        for _, unitUid in pairs(LUnitNEntityMap) do
            local LUnitInst = Game.EntityManager:GetEntityByIntID(unitUid)
            if LUnitInst and LUnitInst.unitType == InUnitType and LUnitInst.Data.ID == InTemplateID then
                LUnitInst:TryDestroyUnit(Reason)
            end
        end
    end
end

-- endregion Core



-- region RPC

--------------------------------- trap ----------------------------------------
function LUnitProxyComponent:OnMsgSyncLUnitTrapList(TrapList)
    local TD = Game.TableData
    for _, Data in pairs(TrapList) do
        self:SpawnClientUnit("LUnitTrapNEntity", Data, TD.GetTrapDataRow(Data.TemplateID))
    end
end

function LUnitProxyComponent:OnMsgCreateLUnitTrap(Data)
    self:DebugFmt(
        "---> OnMsgCreateLUnitTrap, eid: %s, InstId: %s, Tid: %s", self.eid, Data.InstID, Data.TemplateID)

    self:SpawnClientUnit("LUnitTrapNEntity", Data, Game.TableData.GetTrapDataRow(Data.TemplateID))
end

function LUnitProxyComponent:OnMsgDestroyLUnitTrap(InstId, Reason)
    self:DebugFmt(
        "---> OnMsgDestroyLUnitTrap, eid: %s, InstId: %s, Reason: %s", self.eid, InstId, Reason)

    self:DestroyLogicUnit(InstId, Reason)
end

function LUnitProxyComponent:OnMsgLUnitTrapNewStage(InstId, Stage, StartTimeStamp, EndTimeStamp)
    self:DebugFmt(
        "---> OnMsgLUnitTrapNewStage, eid: %s, InstId: %s, Stage: %s, StartTimeStamp: %s, EndTimeStamp: %s",
        self.eid, 
        InstId, 
        Stage, 
        StartTimeStamp, 
        EndTimeStamp
    )

    self:OnLogicUnitStageChange(InstId, Stage)
end

------------------------------------- aura --------------------------------
function LUnitProxyComponent:OnMsgSyncLUnitAuraList(AuraList)
    local TD = Game.TableData
    for _, Data in pairs(AuraList) do
        self:SpawnClientUnit("LUnitAuraNEntity", Data, TD.GetAuraDataRow(Data.TemplateID))
    end
end

function LUnitProxyComponent:OnMsgCreateLUnitAura(Data)
    self:DebugFmt(
        "---> OnMsgCreateLUnitAura, eid: %s, InstId: %s, Tid: %s", self.eid, Data.InstID, Data.TemplateID)

    self:SpawnClientUnit("LUnitAuraNEntity", Data, Game.TableData.GetAuraDataRow(Data.TemplateID))
end

function LUnitProxyComponent:OnMsgDestroyLUnitAura(InstId, Reason)
    self:DebugFmt(
        "---> OnMsgDestroyLUnitAura, eid: %s, InstId: %s, Reason: %s", self.eid, InstId, Reason)

    self:DestroyLogicUnit(InstId, Reason)
end

function LUnitProxyComponent:OnMsgLUnitAuraNewStage(InstId, Stage, StartTimeStamp, EndTimeStamp)
    self:DebugFmt(
        "---> OnMsgLUnitAuraNewStage, eid: %s, InstId: %s, Stage: %s, StartTimeStamp: %s, EndTimeStamp: %s",
        self.eid, 
        InstId, 
        Stage, 
        StartTimeStamp, 
        EndTimeStamp
    )

    self:OnLogicUnitStageChange(InstId, Stage)
end

------------------------------------ velocity -------------------------------------
-- 这个类单纯用来预览用，正常业务代码不会创建这个类

function LUnitProxyComponent:OnMsgCreateLUnitVelocityField(Data)
    self:DebugFmt(
        "---> OnMsgCreateLUnitVelocityField, eid: %s, InstId: %s, Tid: %s", self.eid, Data.InstID, Data.TemplateID)

    self:SpawnClientUnit("LUnitVelocityFieldNEntity", Data, Game.TableData.GetAuraDataRow(Data.TemplateID))
end



------------------------------------ spell field -------------------------------------
function LUnitProxyComponent:OnMsgSyncLUnitSpellFieldList(SpellFieldList)
    local TD = Game.TableData
    for _, Data in pairs(SpellFieldList) do
        self:SpawnClientUnit("LUnitSpellFieldNEntity", Data, TD.GetSpellFieldDataRow(Data.TemplateID))
    end
end

function LUnitProxyComponent:OnMsgCreateLUnitSpellField(Data)
    self:DebugFmt(
        "---> OnMsgCreateLUnitSpellField, eid: %s, InstId: %s, Tid: %s", self.eid, Data.InstID, Data.TemplateID)

    self:SpawnClientUnit("LUnitSpellFieldNEntity", Data, Game.TableData.GetSpellFieldDataRow(Data.TemplateID))
end

function LUnitProxyComponent:OnMsgDestroyLUnitSpellField(InstId, Reason)
    self:DebugFmt(
        "---> OnMsgDestroyLUnitSpellField, eid: %s, InstId: %s, Reason: %s", self.eid, InstId, Reason)

    self:DestroyLogicUnit(InstId, Reason)
end

function LUnitProxyComponent:OnMsgLUnitSpellFieldNewStage(InstId, Stage, StartTimeStamp, EndTimeStamp)
    self:DebugFmt(
        "---> OnMsgLUnitSpellFieldNewStage, eid: %s, InstId: %s, Stage: %s, StartTimeStamp: %s, EndTimeStamp: %s",
        self.eid, 
        InstId, 
        Stage, 
        StartTimeStamp, 
        EndTimeStamp
    )

    self:OnLogicUnitStageChange(InstId, Stage)
end

------------------------------------ spell agent --------------------------------------
function LUnitProxyComponent:OnMsgSyncLUnitSpellAgentList(SpellAgentList)
    local TD = Game.TableData
    for _, Data in pairs(SpellAgentList) do
        self:SpawnClientUnit("LUnitSpellAgentNEntity", Data, TD.GetSpellAgentDataRow(Data.TemplateID))
    end
end

function LUnitProxyComponent:OnMsgCreateLUnitSpellAgent(Data)
    self:DebugFmt(
        "---> OnMsgCreateLUnitSpellAgent, eid: %s, InstId: %s, Tid: %s", self.eid, Data.InstID, Data.TemplateID)

    self:SpawnClientUnit("LUnitSpellAgentNEntity", Data, Game.TableData.GetSpellAgentDataRow(Data.TemplateID))
end

function LUnitProxyComponent:OnMsgDestroyLUnitSpellAgent(InstId, Reason)
    self:DebugFmt(
        "---> OnMsgDestroyLUnitSpellAgent, eid: %s, InstId: %s, Reason: %s", self.eid, InstId, Reason)

    self:DestroyLogicUnit(InstId, Reason)
end

function LUnitProxyComponent:OnMsgLUnitSpellAgentNewStage(InstId, Stage)
    self:DebugFmt(
        "---> OnMsgLUnitSpellAgentNewStage, eid: %s, InstId: %s, Stage: %s",
        self.eid,
        InstId,
        Stage
    )

    self:OnLogicUnitStageChange(InstId, Stage)
end

function LUnitProxyComponent:OnMsgLUnitSpellAgentSkillList(InstId, SkillInstDict)
    if SkillInstDict == nil then
        return
    end

    local Unit = self:GetLogicUnitByServerInstID(InstId)
    if (Unit ~= nil) then
        for skillID, skillIns in pairs(SkillInstDict) do
            Unit:CastSkillNew(skillID, skillIns.lockTarget, skillIns.inputPos, skillIns.inputDir, skillIns.stateID, skillIns.insID, skillIns.startTimestamp)
        end
    end
end

function LUnitProxyComponent:OnMsgLUnitSpellAgentCastSkill(InstId, skillID, lockTarget, inputPos, inputDir, stateID)
    local Unit = self:GetLogicUnitByServerInstID(InstId)
    if (Unit ~= nil) then
        Unit:OnMsgCastSkillNew(skillID, lockTarget, inputPos, inputDir, stateID)
    end
end

function LUnitProxyComponent:OnMsgLUnitSpellAgentStopSkill(InstId, skillID, reason)
    local Unit = self:GetLogicUnitByServerInstID(InstId)
    if (Unit ~= nil) then
        Unit:StopSkillInstance(skillID, reason)
    end
end

function LUnitProxyComponent:OnMsgLUnitSpellAgentChangeAbState(InstId, abilityID, nextStateID, lockTarget)
    local Unit = self:GetLogicUnitByServerInstID(InstId)
    if (Unit ~= nil) then
        Unit:OnMsgChangeAbState(abilityID, nextStateID, lockTarget)
    end
end

function LUnitProxyComponent:OnMsgSyncLUnitDetach(InstId)
    self:DebugFmt(
        "---> OnMsgSyncLUnitDetach, eid: %s, InstId: %s",
        self.eid,
        InstId
    )

    local Unit = self:GetLogicUnitByServerInstID(InstId)
    if (Unit ~= nil) then
        Unit:DetachFromParent()
    end
end

-----------------------------------unit rotation -------------------------------------
function LUnitProxyComponent:OnMsgSyncLUnitRotation(InstId, Yaw)
    self:DebugFmt(
        "---> OnMsgSyncLUnitRotation, eid: %s, InstId: %s, Yaw: %s",
        self.eid,
        InstId,
        Yaw
    )

    local Unit = self:GetLogicUnitByServerInstID(InstId)
    if (Unit ~= nil) then
        Unit:OnRotationUpdate(Yaw)
    end
end


------------------------------- unit move -------------------------------------------
function LUnitProxyComponent:OnMsgLUnitStartMove(TemplateID, InstId, MoveParams)
    self:DebugFmt(
        "---> OnMsgLUnitStartMove, eid: %s, TemplateID: %s, InstId: %s, Params:{MoveType:%s, Speed: %s, HomingTargetID: %s, DistThreshold: %s}",
        self.eid, TemplateID, InstId,
        MoveParams.MoveType, MoveParams.Speed, MoveParams.HomingTargetID, MoveParams.DistThreshold)
end

function LUnitProxyComponent:OnMsgLUnitStopMove(TemplateID, InstId, Reason)
    self:DebugFmt(
        "---> OnMsgLUnitStopMove, eid: %s, TemplateID: %s, InstId: %s, Reason:%s",
        self.eid, TemplateID, InstId, Reason)
end

------------------------------------- bullet ------------------------------------------
function LUnitProxyComponent:OnMsgCreateBulletV2(Data)
    self:DebugFmt("---> OnMsgCreateBulletV2, eid: %s, bulletId: %s, LauncherID: %s", self.eid, Data.TemplateID, Data.LauncherID)
    self:SpawnBullet({
        BulletID = Data.TemplateID,
        LifeTime = Data.MustHitTimeLength,
        LauncherID = Data.LauncherID,
        FinalOwnerID = Data.InstigatorID or self:uid(),
        -- FinalOwnerID = Data.LauncherID,
        TargetEID = Data.TargetID,
        TargetPosFromServer = Data.TargetPos,
        StartPos = Data.Position,
        StartPitch = Data.Pitch,
        StartYaw = Data.Yaw,
        ServerRotOffset = Data.ExtraData and Data.ExtraData.RotOffset,
        ServerPosOffset = Data.ExtraData and Data.ExtraData.PosOffset,
        OffsetMode = Data.ExtraData and Data.ExtraData.OffsetMode,
        DirectionMode = Data.ExtraData and Data.ExtraData.DirectionMode ,
    }, Data.InstID)

end

function LUnitProxyComponent:OnMsgDestroyBulletV2(TemplateID, InstId, Reason)
    self:DebugFmt(
        "---> OnMsgDestroyBulletV2, eid: %s, TemplateID: %s, InstId: %s, Reason: %s",
        self.eid,
        TemplateID,
        InstId,
        Reason
    )

    self:DestroyBullet(InstId, Reason)

end

function LUnitProxyComponent:OnMsgBulletTargetLost(TemplateID, InstId)
    self:DebugFmt(
        "---> OnMsgBulletTargetLost, eid: %s, TemplateID: %s, InstId: %s",
        self.eid,
        TemplateID,
        InstId
    )

end


-- endregion RPC

return LUnitProxyComponent
