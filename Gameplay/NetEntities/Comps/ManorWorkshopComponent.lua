---@class ManorWorkshopComponent
ManorWorkshopComponent = DefineComponent("ManorWorkshopComponent")

function ManorWorkshopComponent:ctor()
end

function ManorWorkshopComponent:dtor()
end

-- 查询玩家拥有的工坊状态
function ManorWorkshopComponent:RetGetWorkshopStatus(workshopList)
	Game.ManorSystem.ManorWorkshopModule:RetGetWorkshopStatus(workshopList)
end

-- 工坊升级结果
---@param resultCode number
---@param workshopInfo BUILDING_INFO
function ManorWorkshopComponent:RetWorkshopUpgrade(resultCode, workshopInfo)
	Game.ManorSystem.ManorWorkshopModule:RetWorkshopUpgrade(resultCode, workshopInfo)
end

-- 工坊购买结果
function ManorWorkshopComponent:RetBuyWorkshop(resultCode)
	Game.ManorSystem.ManorWorkshopModule:RetBuyWorkshop(resultCode)
end

-- 工坊雇员列表回复
function ManorWorkshopComponent:RetWorkshopEmployeeList(employeeList)
	Game.ManorSystem.ManorWorkshopModule:RetWorkshopEmployeeList(employeeList)
end

-- 指派工坊雇员回复
function ManorWorkshopComponent:RetAssignWorkshop(resultCode, uid, buildingId, index)
	Game.ManorSystem.ManorWorkshopModule:RetAssignWorkshop(resultCode, uid, buildingId, index)
end

-- 解雇工坊雇员回复
function ManorWorkshopComponent:RetRemoveWorkshopEmployee(resultCode, uid)
	Game.ManorSystem.ManorWorkshopModule:RetRemoveWorkshopEmployee(resultCode, uid)
end

-- 添加工坊生产队列回复
function ManorWorkshopComponent:RetWorkshopAddProductionQueue(resultCode, buildingId, recipeId)
	Game.ManorSystem.ManorWorkshopModule:RetWorkshopAddProductionQueue(resultCode, buildingId, recipeId)	
end

-- 移除工坊生产队列回复
function ManorWorkshopComponent:RetWorkshopRemoveProductionQueue(resultCode, buildingId)
	Game.ManorSystem.ManorWorkshopModule:RetWorkshopRemoveProductionQueue(resultCode, buildingId)
end

-- 工坊收获回复
function ManorWorkshopComponent:RetWorkshopHarvest(resultCode, buildingId)
	Game.ManorSystem.ManorWorkshopModule:RetWorkshopHarvest(resultCode, buildingId)
end

-- 同步工坊信息
function ManorWorkshopComponent:OnMsgWorkshopStatusUpdate(workshopStatus)
	Game.ManorSystem.ManorWorkshopModule:OnMsgWorkshopStatusUpdate(workshopStatus)
end

return ManorWorkshopComponent