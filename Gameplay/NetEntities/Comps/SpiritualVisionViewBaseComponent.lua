SpiritualVisionViewBaseComponent = DefineComponent("SpiritualVisionViewBaseComponent")

local KismetMathLibrary = import("KismetMathLibrary")
local MaterialEffectParamTemplate = kg_require("Gameplay.Effect.MaterialEffectParamTemplate")
local MaterialEffectParamsPool = MaterialEffectParamTemplate.MaterialEffectParamsPool
local SEARCH_MESH_TYPE = MaterialEffectParamTemplate.SEARCH_MESH_TYPE
local MATERIAL_EFFECT_TYPE = MaterialEffectParamTemplate.MATERIAL_EFFECT_TYPE

-- 材质参数名
SpiritualVisionViewBaseComponent.MaterialEdgeIntensityName = "Edge Intensity"
SpiritualVisionViewBaseComponent.MaterialEdgeIntensityMinName = "Edge Intensity Min"
SpiritualVisionViewBaseComponent.MaterialEdgeColorName = "Edge Color"

-- 灵视扩散速度
SpiritualVisionViewBaseComponent.SPIRITUAL_SPREAD_SPEED = 100

Enum.SpiritualVisionEffectType = {
	None = 0,			-- 无事发生
	NormalSkill = 1,	-- 通用灵视技能
	CustomSkill = 2,	-- 自定义灵视技能
	SpecificCall = 3,	-- 自定义调用，外部系统直调
}

function SpiritualVisionViewBaseComponent:ctor()
	self.SpiritualEffectMaxDistance = Game.TableData.GetConstDataRow("SPIRITUAL_VISION_MAPPINGMARK") * 100
    self.bSpiritualNeeded = false
	self.CurSpiritualEffectType = Enum.SpiritualVisionEffectType.None
    self.DefaultSpiritualIconType = 0
    self.DefaultSpiritualShaderTemplate = nil
	self.bDefaultUseSpiritualVision = false
	self.bDefaultOnlyInSpiritualVision = false
	self.CurSpiritualDistance = nil
	self.CurSpiritualIconType = nil
	self.CurSpiritualShaderTemplate = nil
    self.SpiritualSpreadingTimer = nil
    self.bBornInSpiritual = false --进入aoi时被灵视扫到的标记
	self.bSpiritualDisable = false
	self.bSpiritualEverEffected = false
	self.SpiritualMaterialChangeReqID = nil
end


function SpiritualVisionViewBaseComponent:dtor()

end

function SpiritualVisionViewBaseComponent:__component_EnterWorld__()
    self:ReadSpiritualConf()
end

function SpiritualVisionViewBaseComponent:__component_AfterEnterWorld__()
	if self:CanEffectBySpiritualVision(0) then
		self.bSpiritualNeeded = true
		self:RegisterAsSpiritualAble()
	end
    self:InitSpiritualBornStatus()
end


function SpiritualVisionViewBaseComponent:__component_ExitWorld__()
	if self.bSpiritualNeeded then
		self:UnRegisterAsSpiritualAble()
	end
	Game.GlobalEventSystem:Publish(EEventTypesV2.ACTOR_SPIRIT_VISUAL_DESTROY, self:uid())
    if self.SpiritualSpreadingTimer then
        Game.TimerManager:StopTimerAndKill(self.SpiritualSpreadingTimer)
        self.SpiritualSpreadingTimer = nil
    end
end

function SpiritualVisionViewBaseComponent:__component_AppendGamePlayDebugInfo__(debugInfos)
	return true
end

function SpiritualVisionViewBaseComponent:RegisterAsSpiritualAble()
	Game.WorldDataManager:RegisterSpiritualVisionActor(self.InsID or self.InstanceID)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.SPIRIT_VISUAL_EFFECT_START, "OnReceiveSpiritualEffectStart", self)
    Game.GlobalEventSystem:AddListener(EEventTypesV2.SPIRIT_VISUAL_EFFECT_END, "OnReceiveSpiritualEffectEnd", self)
end

function SpiritualVisionViewBaseComponent:UnRegisterAsSpiritualAble()
	Game.WorldDataManager:UnRegisterSpiritualVisionActor(self.InsID or self.InstanceID)
    Game.GlobalEventSystem:RemoveListener(EEventTypesV2.SPIRIT_VISUAL_EFFECT_START, "OnReceiveSpiritualEffectStart", self)
    Game.GlobalEventSystem:RemoveListener(EEventTypesV2.SPIRIT_VISUAL_EFFECT_END, "OnReceiveSpiritualEffectEnd", self)
end

function SpiritualVisionViewBaseComponent:RefreshSpiritualNeeded()
	local CurNeeded = self:CanEffectBySpiritualVision(0)
	if CurNeeded ~= self.bSpiritualNeeded then
		if CurNeeded then
			self:RegisterAsSpiritualAble()
		else
			self:UnRegisterAsSpiritualAble()
		end
		self.bSpiritualNeeded = CurNeeded
	end
end

--@virtual
function SpiritualVisionViewBaseComponent:ReadSpiritualConf()

end

--@virtual
function SpiritualVisionViewBaseComponent:IsSpiritualEnabled()
    return not self.bSpiritualDisable
end

function SpiritualVisionViewBaseComponent:IsOnlyVisibleInSpiritual()
	return self.bDefaultOnlyInSpiritualVision
end

function SpiritualVisionViewBaseComponent:IsOnlyVisibleAfterSpiritual()
	return self.bDefaultUseSpiritualVision
end

function SpiritualVisionViewBaseComponent:InitSpiritualBornStatus()
    if not self:IsSpiritualEnabled() then
        return
    end
	
    -- 灵视隐藏时关闭交互功能
    if self:IsOnlyVisibleInSpiritual() or (self:IsOnlyVisibleAfterSpiritual() and not self.bSpiritualEverEffected) then
        if self.DoUnInitBehavior then
            self:DoUnInitBehavior()  
        end
    end
	
	self.bBornInSpiritual = self:TryEnterSpiritual()
	
    self:RefreshVisibilityBySpiritualVision()
end

function SpiritualVisionViewBaseComponent:RefreshVisibilityBySpiritualVision()
    -- 灵视后显示、仅灵视中显示
    if self:IsOnlyVisibleInSpiritual() or (self:IsOnlyVisibleAfterSpiritual() and not self.bSpiritualEverEffected) then
        --灵视控制隐藏
        self:SetActorInVisible(Enum.EInVisibleReasons.SpiritualControl)
        if self.CurSpiritualEffectType ~= Enum.SpiritualVisionEffectType.None then
            self:SwitchSpiritualShow(true)
        else
            self:SwitchSpiritualShow(false)
        end
    end
end

function SpiritualVisionViewBaseComponent:OnReceiveSpiritualEffectStart(CenterPos)
    -- 当前在灵视状态或灵视禁止
    if self.CurSpiritualEffectType ~= Enum.SpiritualVisionEffectType.None or not self:IsSpiritualEnabled() then
        return
    end

    local Distance = KismetMathLibrary.Vector_Distance(self:GetPosition(), CenterPos)
    if self:CanEffectBySpiritualVision(Distance) then
        local Template = self:GetSpiritualShaderTemplate()
        self.CurSpiritualDistance = Distance
		if Template and Template > 0 then
			self.CurSpiritualShaderTemplate = Template
			self:StartSpiritualEffect(Template)
		end
	    Game.GlobalEventSystem:Publish(EEventTypesV2.ACTOR_ENTER_SPIRIT_VISUAL_STATE, self:uid(), self.CurSpiritualDistance, Template)
		self:OnEnterSpiritualStatus()
		self.CurSpiritualEffectType = Enum.SpiritualVisionEffectType.NormalSkill
		self.bSpiritualEverEffected = true
    end
end

function SpiritualVisionViewBaseComponent:DeCurActivateSpiritualEffect()
	self:ResetNiagaraMeshColor()
	if self.SpiritualMaterialChangeReqID then
		self:DeActivateSpiritualEffect(self.SpiritualMaterialChangeReqID)
		self.SpiritualMaterialChangeReqID = nil
	end
end

function SpiritualVisionViewBaseComponent:DeActivateSpiritualEffect(ReqID)
	self:RevertMaterialParam(ReqID)
end

--@virtual
function SpiritualVisionViewBaseComponent:GetSpiritualVisionSearchMeshType()
	return SEARCH_MESH_TYPE.SearchSelfMeshes
end

function SpiritualVisionViewBaseComponent:GenSpiritualVisionMaterialParams(ShaderTemplate)
	local MaterialData = Game.TableData.GetSpiritualVisionShaderDataRow(ShaderTemplate)
	if MaterialData == nil then
		self:WarningFmt("GenSpiritualVisionMaterialParams get wrong template id %s",  ShaderTemplate)
		return nil
	end
	local ChangeMaterialParamRequestTemplate = MaterialEffectParamTemplate.ChangeMaterialParamRequestTemplate
	local ChangeMaterialParamReq = MaterialEffectParamsPool.AllocFromPool(ChangeMaterialParamRequestTemplate)
	ChangeMaterialParamReq.SearchMeshType = self:GetSpiritualVisionSearchMeshType()
	ChangeMaterialParamReq.EffectType = MATERIAL_EFFECT_TYPE.Edge
	ChangeMaterialParamReq.ScalarParams = {}
	ChangeMaterialParamReq.ScalarParams[SpiritualVisionViewBaseComponent.MaterialEdgeIntensityName] = MaterialData.EdgeIntensity
	ChangeMaterialParamReq.ScalarParams[SpiritualVisionViewBaseComponent.MaterialEdgeIntensityMinName] = MaterialData.EdgeIntensityMin
	ChangeMaterialParamReq.VectorParams = {}
	--ChangeMaterialParamReq.VectorParams[SpiritualVisionViewBaseComponent.MaterialEdgeColorName] = M3D.Vec3(MaterialData.Color[1], MaterialData.Color[2], MaterialData.Color[3])
	ChangeMaterialParamReq.VectorParams[SpiritualVisionViewBaseComponent.MaterialEdgeColorName] = {
		R = MaterialData.Color[1],
		G = MaterialData.Color[2],
		B = MaterialData.Color[3],
		A = 1.0,
	}
	return ChangeMaterialParamReq
end

function SpiritualVisionViewBaseComponent:ActivateSpiritualEffect(ShaderTemplate)
	local ChangeMaterialParamReq = self:GenSpiritualVisionMaterialParams(ShaderTemplate)
	if ChangeMaterialParamReq then
		local ChangeMaterialParamReqId = self:ChangeMaterialParam(ChangeMaterialParamReq)
		self:ChangeNiagaraMeshColor()
		return ChangeMaterialParamReqId
	end
	return nil
end

--@virtual
function SpiritualVisionViewBaseComponent:CanEffectBySpiritualVision(Distance)
	if self.SpiritualEffectMaxDistance >= 0 and Distance > self.SpiritualEffectMaxDistance then
		return false
	end
	local ShaderTemplate = self:GetSpiritualShaderTemplate()
	local bIconToShow = self:GetSpiritualIconType() > 0
	local bMaterialToChange = ShaderTemplate ~= nil and Game.TableData.GetSpiritualVisionShaderDataRow(ShaderTemplate) ~= nil
	local bVisibilityToChange = self:IsOnlyVisibleInSpiritual() or (self:IsOnlyVisibleAfterSpiritual() and not self.bSpiritualEverEffected)
	return bIconToShow or bMaterialToChange or bVisibilityToChange
end

--@virtual
function SpiritualVisionViewBaseComponent:GetSpiritualShaderTemplate()
    return nil
end

--@virtual
function SpiritualVisionViewBaseComponent:GetSpiritualIconType()
    return 0
end

function SpiritualVisionViewBaseComponent:OnReceiveSpiritualEffectEnd()
	if self.CurSpiritualEffectType == Enum.SpiritualVisionEffectType.NormalSkill then
		self.bBornInSpiritual = false
		self.CurSpiritualDistance = nil
		self:EndSpiritualEffect()
		self:OnLeaveSpiritualStatus()
		Game.GlobalEventSystem:Publish(EEventTypesV2.ACTOR_LEAVE_SPIRIT_VISUAL_STATE, self:uid())
		self.CurSpiritualEffectType = Enum.SpiritualVisionEffectType.None
	end
end

function SpiritualVisionViewBaseComponent:TryEnterSpiritual()
    if not Game.me then
        return false
    end
    if Game.me:IsInSpiritualVision() then
        self:OnReceiveSpiritualEffectStart(Game.me:GetPosition())
    end
	return self.CurSpiritualEffectType ~= Enum.SpiritualVisionEffectType.None
end

function SpiritualVisionViewBaseComponent:TryRefreshSpiritual()
	if not self.bInWorld then
		return
	end
	-- 刷新事件绑定和灵视数据注册
	self:RefreshSpiritualNeeded()
	local bMeInSpiritual = Game.me and Game.me:IsInSpiritualVision()
	if bMeInSpiritual then
		local CenterPos = Game.me:GetPosition()
		local Distance = KismetMathLibrary.Vector_Distance(self:GetPosition(), CenterPos)
		local CurCanEffect = self:CanEffectBySpiritualVision(Distance)
		if CurCanEffect then
			if self.CurSpiritualEffectType == Enum.SpiritualVisionEffectType.NormalSkill then
				local CurShaderID = self:GetSpiritualShaderTemplate()
				if CurShaderID and CurShaderID > 0 then
					if self.CurSpiritualShaderTemplate ~= CurShaderID then
						self.CurSpiritualDistance = Distance
						self.CurSpiritualShaderTemplate = CurShaderID
						self:DoSpiritualEffect()
					end
				else
					self:OnReceiveSpiritualEffectEnd()
				end
			else
				self:OnReceiveSpiritualEffectStart(CenterPos)
			end
		else
			if self.CurSpiritualEffectType == Enum.SpiritualVisionEffectType.NormalSkill then
				self:OnReceiveSpiritualEffectEnd()
			end
		end
	end
end

function SpiritualVisionViewBaseComponent:DoSpiritualEffect()
    if self.SpiritualSpreadingTimer then
        Game.TimerManager:StopTimerAndKill(self.SpiritualSpreadingTimer)
        self.SpiritualSpreadingTimer = nil
    end
    --已经不在灵视
    if self.CurSpiritualEffectType ~= Enum.SpiritualVisionEffectType.NormalSkill then
        return
    end
	
	self:DeCurActivateSpiritualEffect()
	
    if self.CurSpiritualShaderTemplate then
        self.SpiritualMaterialChangeReqID = self:ActivateSpiritualEffect(self.CurSpiritualShaderTemplate)
    end
end


function SpiritualVisionViewBaseComponent:StartSpiritualEffect()
    --改变shader
    if (not self.SpiritualMaterialChangeReqID ) and self.CurSpiritualDistance and self.CurSpiritualShaderTemplate then
		local EffectMSecs = 600+self.CurSpiritualDistance/SpiritualVisionViewBaseComponent.SPIRITUAL_SPREAD_SPEED
        if self.bBornInSpiritual then
			EffectMSecs = 0
            self.bBornInSpiritual = false
        end
		self.SpiritualSpreadingTimer = Game.TimerManager:CreateTimerAndStart(function()
			self:DoSpiritualEffect()
		end, EffectMSecs, 1)
    end
end

function SpiritualVisionViewBaseComponent:EndSpiritualEffect()
    if self.SpiritualSpreadingTimer then
        Game.TimerManager:StopTimerAndKill(self.SpiritualSpreadingTimer)
        self.SpiritualSpreadingTimer = nil
    end

    --恢复shader
	self:DeCurActivateSpiritualEffect()
end

--@virtual
function SpiritualVisionViewBaseComponent:OnEnterSpiritualStatus()

end

--@virtual
function SpiritualVisionViewBaseComponent:OnLeaveSpiritualStatus()

end

--@virtual
function SpiritualVisionViewBaseComponent:SwitchSpiritualShow(bIsShow)

end

function SpiritualVisionViewBaseComponent:SetSpiritualVisionEnabled(bDisable)
    if self.bSpiritualDisable ~= bDisable then
        if not bDisable then
            --若是在灵视中尝试切换到灵视状态
            self:TryEnterSpiritual()
        end
        self.bSpiritualDisable = bDisable
    end
end

function SpiritualVisionViewBaseComponent:StartSpecificSpiritualVisionEffect(SpiritualShaderTemplate)
	if self.CurSpiritualEffectType ~= Enum.SpiritualVisionEffectType.None then
		return
	end
	self.CurSpiritualShaderTemplate = SpiritualShaderTemplate
	self.CurSpiritualEffectType = Enum.SpiritualVisionEffectType.SpecificCall
	self:DeCurActivateSpiritualEffect()
    self.SpiritualMaterialChangeReqID = self:ActivateSpiritualEffect(SpiritualShaderTemplate)
end

function SpiritualVisionViewBaseComponent:EndSpecificSpiritualVisionEffect()
	if self.CurSpiritualEffectType == Enum.SpiritualVisionEffectType.SpecificCall then
		self:DeCurActivateSpiritualEffect()
		self.CurSpiritualShaderTemplate = nil
		self.CurSpiritualEffectType = Enum.SpiritualVisionEffectType.None
	end
end

return SpiritualVisionViewBaseComponent