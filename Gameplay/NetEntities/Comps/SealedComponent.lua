SealedComponent = DefineComponent("SealedComponent")
local itemConst = kg_require("Shared.ItemConst")

-------------------------------------- Server Rpc ----------------------------------
--全量源质核心装备信息同步
function SealedComponent:OnSendAllSealedInfo(sefirotCoreInfo)
    self:Debug("[SealedComponent:OnSendAllSealedInfo]")
    -- local p = Game.SealedSystem.model
    -- p.sefirotCoreInfo = sefirotCoreInfo
end

--封印物升级
function SealedComponent:RetSealedUpgrade(result, slot, sealedInfo)
    self:Debug("[SealedComponent:RetSealedUpgrade]result:", result.Code)
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_UPGRADE, slot, sealedInfo)
    -- end
end

--封印物词条等级回溯
function SealedComponent:RetSealedUpgradeGoBack(result)
    self:Debug("[SealedComponent:RetSealedUpgradeGoBack]result:", result.Code)
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_UPGRADE_BACK)
    -- end
end

--封印物突破
function SealedComponent:RetSealedBreakthrough(result, totReturnSealedCnt, totReturnMaterialList)
    -- self:Debug("[SealedComponent:RetSealedBreakthrough]result:", result.Code,
    --          "totReturnSealedCnt:", totReturnSealedCnt, 
    --          "totReturnMaterialList:",CommonUtils.tprint(totReturnMaterialList))
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_PROMOTE, totReturnSealedCnt, totReturnMaterialList)
    -- end
end

--封印物装备和替换
function SealedComponent:RetSealedPutOn(result)
    self:Debug("[SealedComponent:RetSealedPutOn]result:", result.Code)
     if result.Code == Game.ErrorCodeConst.NO_ERR then
         Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_EQUIP)
     end
end

--封印物卸下
function SealedComponent:RetSealedPutOff(result)
    self:Debug("[SealedComponent:RetSealedPutOff]result:", result.Code)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_EQUIP)
    end
end

--非凡物质装配
function SealedComponent:RetPutOnXMat(result, sealedId, changedSlotInfo)
    self:Debug("[SealedComponent:RetPutOnXMat]result:", result.Code)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.BagSystem:OnChangeInventory({}, changedSlotInfo, false, {}) -- luacheck: ignore
        Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_UPDATE, sealedId)
    end
end

--非凡物质卸下
function SealedComponent:RetPutOffXMat(result, sealedId, changedSlotInfo)
    self:Debug("[SealedComponent:RetPutOffXMat]result:", result.Code)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.BagSystem:OnChangeInventory({}, changedSlotInfo, false, {}) -- luacheck: ignore
        Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_UPDATE, sealedId)
    end
end

--合成非凡物质
function SealedComponent:RetFuseXMat(result)
    self:Debug("[SealedComponent:RetFuseXMat]result:", result.Code)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_FUSE_UPDATE)
    end
end

--选中合成后的非凡物质
function SealedComponent:RetSelectFuse(result)
    self:Debug("[SealedComponent:RetSelectFuse]result:", result.Code)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_XMAT_SELECT_FUSE)
    end
end

--修改锁定状态返回
function SealedComponent:RetSwitchXMatLock(result, slotIndex, lock)
    self:DebugFmt("[SealedComponent:RetSwitchXMatLock]result: %s %s %s", result.Code, slotIndex, lock)
    if result.Code == Game.ErrorCodeConst.NO_ERR then
        local xMat = Game.BagSystem:GetItemInfoByIndex(itemConst.INV_TYPE_XMAT, slotIndex)
        if xMat then
            xMat.xtraMatPropInfo.lock = lock
        end
    end
end

function SealedComponent:RetCollectProgressReward(retCode, rewardId)
    self:DebugFmt("[SealedComponent:RetCollectProgressReward]result: %s", retCode)
    if retCode == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_PROGRESS_REWARD_COLLECTED, rewardId)
    end
end

function SealedComponent:RetCollectAllProgressRewards(retCode)
    self:DebugFmt("[SealedComponent:RetCollectAllProgressRewards]result: %s", retCode)
    if retCode == Game.ErrorCodeConst.NO_ERR then
        Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_ALL_PROGRESS_REWARDS_COLLECTED)
    end
end

--源质核心切换
function SealedComponent:RetSefirotCoreChange(result)
    self:Debug("[SealedComponent:RetSefirotCoreChange]result:", result.Code)
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEFIROT_CORE_CHANGE)
    -- end
end

--封印物洗练
function SealedComponent:RetSealedRefined(result, isLuckyRefine)
    self:Debug("[SealedComponent:RetSealedRefined]result:", result.Code, " isLuckyRefine:", isLuckyRefine)
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_REFINE, isLuckyRefine)
    -- end
end

--封印物上次洗练结果应用
function SealedComponent:RetSealedRefineApply(result)
    self:Debug("[SealedComponent:RetSealedRefineApply]result:", result.Code)
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_APPLY)
    -- end
end

--封印物重置洗练结果
function SealedComponent:RetSealedRefineReset(result)
    self:Debug("[SealedComponent:RetSealedRefineReset]result:", result.Code)
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_RESET)
    -- end
end

--设置封印物锁定状态
function SealedComponent:RetSetSealedLockState(result, bLock)
    self:Debug("[SealedComponent:RetSetSealedLockState]result:", result.Code, ", bLock:", bLock)
    -- if result.Code == Game.ErrorCodeConst.NO_ERR then
    --     Game.EventSystem:Publish(_G.EEventTypes.ON_SEALED_LOCK)
    --     if bLock then
    --         Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SEFIROT_ITEM_LOCK)
    --     else
    --         Game.ReminderManager:AddReminderById(Enum.EReminderTextData.SEFIROT_ITEM_UNLOCK)
    --     end
    -- end
end
------------------------------------ Server Rpc End---------------------------------

function SealedComponent:ReqSealedPutOn(sealedId, uid, equipSlot)
    self.remote:ReqSealedPutOn(sealedId, uid, equipSlot)
end

function SealedComponent:ReqSealedPutOff(equipSlot)
    self.remote:ReqSealedPutOff(equipSlot)
end

function SealedComponent:ReqPutOnXMat(slotIndex, sealedId)
    self.remote:ReqPutOnXMat(slotIndex, sealedId)
end

function SealedComponent:ReqPutOffXMat(sealedId)
    self.remote:ReqPutOffXMat(sealedId)
end

function SealedComponent:ReqFuseXMat(slotIndex1, slotIndex2)
    self.remote:ReqFuseXMat(slotIndex1, slotIndex2)
end

function SealedComponent:ReqSelectFuse(slotIndex)
    self.remote:ReqSelectFuse(slotIndex)
end

function SealedComponent:ReqSwitchXMatLock(slotIndex, bLock)
    self.remote:ReqSwitchXMatLock(slotIndex, bLock)
end

function SealedComponent:ReqClearSealedRedPoint()
    self.remote:ReqClearSealedRedPoint()
end

function SealedComponent:ReqCollectProgressReward(rewardId)
    self.remote:ReqCollectProgressReward(rewardId)
end

function SealedComponent:ReqCollectAllProgressRewards()
    self.remote:ReqCollectAllProgressRewards()
end
-- function SealedComponent:ReqSefirotCoreChange()
--     self.remote:ReqSefirotCoreChange()
-- end
-- function SealedComponent:ReqSealedPutOn(invId,invSlot,gbId,targetSlot)
--     self.remote:ReqSealedPutOn(invId,invSlot,gbId,targetSlot)
-- end
-- function SealedComponent:ReqSealedPutOff(slotIdx)
--     self.remote:ReqSealedPutOff(slotIdx)
-- end
-- function SealedComponent:ReqSealedUpgrade(invId,slot,gbId,propId)
--     self.remote:ReqSealedUpgrade(invId,slot,gbId,propId)
-- end
-- function SealedComponent:ReqSealedUpgradeGoBack(invId,slot,gbId,propId)
--     self.remote:ReqSealedUpgradeGoBack(invId,slot,gbId,propId)
-- end
-- function SealedComponent:ReqSealedBreakthrough(invId,slot,gbId, matList)
--     self.remote:ReqSealedBreakthrough(invId,slot,gbId,matList)
-- end
-- function SealedComponent:ReqSealedRefined(invId,slot,gbId)
--     self.remote:ReqSealedRefined(invId,slot,gbId)
-- end
-- function SealedComponent:ReqSealedRefineApply(invId,slot)
--     self.remote:ReqSealedRefineApply(invId,slot)
-- end
-- function SealedComponent:ReqSealedRefineReset(invId,slot)
--     self.remote:ReqSealedRefineReset(invId,slot)
-- end
-- function SealedComponent:ReqSetSealedLockState(invId,slot,gbId,isLock)
--     self.remote:ReqSetSealedLockState(invId,slot,gbId,isLock)
-- end

return SealedComponent