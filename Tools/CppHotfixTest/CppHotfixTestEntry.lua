---
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com.
--- DateTime: 2025/1/14 15:10
---

-- C++内针对emmy_core, 会报一个Error, 影响到测试结果, 提交前需要注释掉.
if false then
    xpcall(function()
        package.cpath = package.cpath .. ';C:/Users/<USER>/AppData/Roaming/JetBrains/PyCharmCE2024.3/plugins/EmmyLua/debugger/emmy/windows/x64/?.dll'
        local dbg = require('emmy_core')
        dbg.tcpConnect('localhost', 9966)
    end, function() end)
end

-- 全量单元测试的开关。如果想仅测自己的用例（一个一个的），设置成false
GCppHotfixTestEnable = true

require "Framework.C7Common.C7Log"

Log = Log or {}
Log.DebugFormat = LuaCLogger.DebugLogFmt
Log.DebugErrorFormat = LuaCLogger.DebugErrorFmt

require "Framework.Library.CppReflection"
require "Tools.CppHotfixTest.TestFramework"

local function kg_reload(module)
    package.loaded[module] = nil
    package.preload[module] = nil
    return require(module)
end

-- 包含所有重要的测试用例
if GCppHotfixTestEnable then
    kg_reload("Tools.CppHotfixTest.CustomTest")
    kg_reload("Tools.CppHotfixTest.AutomationTest")
end

kg_reload("Tools.CppHotfixTest.Liubo")

if GCppHotfixTestEnable then
    kg_reload("Tools.CppHotfixTest.Hantao")
    kg_reload("Tools.CppHotfixTest.TestCase_FStruct")
end

collectgarbage("setstepmul", 0)
collectgarbage("collect")

return true
