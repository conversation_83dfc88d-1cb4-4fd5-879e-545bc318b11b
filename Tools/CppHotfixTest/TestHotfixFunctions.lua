require "Tools.CppHotfixTest.TestFramework"

cpp.FClassTest.InitGlobalUObject()

Test_IntPtr_Var1 = cpp["Test_IntPtr_Var1"]:GetValue()
Test_DoublePtr_Var1 = cpp["Test_DoublePtr_Var1"]:GetValue()
Test_FloatPtr_Var1 = cpp["Test_FloatPtr_Var1"]:GetValue()
Test_FTextPtr_Var1 = cpp["Test_FTextPtr_Var1"]:GetValue()
Test_FTextPtr_Var2 = "hahaha"
Test_UObjectPtr_Var1 = cpp["FClassTest.Test_Member_Static_Const_UObject_Ptr_Var1"]:GetValue()
Test_UObjectPtr_Var2 = cpp["FClassTest.Test_Member_Static_Const_UObject_Ptr_Var2"]:GetValue()
Test_DoubleRef_Var1 = cpp["Test_DoubleRef_Var1"]:GetValue()
Test_FVector4_Var1 = cpp["Test_FVector4_Var1"]:GetValue()
Test_FVector4_Var2 = cpp["Test_FVector4_Var2"]:GetValue()
Test_UObject_Var1 = slua.loadObject("/Script/Engine.Texture2D'/Engine/EngineSky/T_Sky_Blue.T_Sky_Blue'")
Test_UObject_Var2 = slua.loadObject("/Script/Engine.Texture2D'/Engine/EngineSky/T_Sky_Stars.T_Sky_Stars'")

Test_FColor_Var1 = cpp["Test_FColor_Var1"]:GetValue()
Test_FString_Var1 = cpp["Test_FString_Var1"]:GetValue()
Test_FString_Var2 = cpp["Test_FString_Var2"]:GetValue()
Test_FStringPtr_Var1 = cpp["Test_FStringPtr_Var1"]:GetValue()

Test_FOverloadMemberFunctionTestClass_Object_Var1 = cpp["FOverloadMemberFunctionTestClass"]()
Test_FClassTestDestructor_Object_Var1 = cpp["FClassTestDestructor"]()
Test_Corvirant_Base_Object_Var1 = cpp["FVirtualTestBaseClass"]()
Test_Corvirant_Derived_Object_Var1 = cpp["FVirtualTestDerivedClass"]()
Test_Corvirant_Multi_Inherit_Object_Var1 = cpp["FTestMultiInheritClass"]()

Test_Inherit_Function_Args = { Test_FTextPtr_Var1, FVector4(0, 0, 0, 0) }
Test_Multi_Inherit_Function_Args = { Test_FTextPtr_Var1, 0.1, FVector4(0, 0, 0, 0) }
Test_Diamond_Base_Inherit_Function_Args = { Test_FTextPtr_Var1, 1.1}
Test_Diamond_Derived_Inherit_Function_Args = { Test_FTextPtr_Var1, Test_FColor_Var1, Test_IntPtr_Var1}

Test_ETestUCharEnum_Var1 = cpp["Test_ETestUCharEnumPtr_Var1"]:GetValue()
Test_ETestUCharEnum_Var2 = cpp["Test_ETestUCharEnumPtr_Var2"]:GetValue()

Test_FIntVector_Var = cpp["cpp_FIntVector_Var"]:GetValue()
Test_FIntVector4_Var = cpp["cpp_FIntVector4_Var"]:GetValue()
Test_FLinearColor_Var = cpp["cpp_FLinearColor_Var"]:GetValue()
Test_FColor_Var = cpp["cpp_FColor_Var"]:GetValue()
Test_FRandomStream_Var = cpp["cpp_FRandomStream_Var"]:GetValue()
Test_FGuid_Var = cpp["cpp_FGuid_Var"]:GetValue()
Test_FFallbackStruct_Var = cpp["cpp_FFallbackStruct_Var"]:GetValue()
Test_FInterpCurvePointFloat_Var = cpp["cpp_FInterpCurvePointFloat_Var"]:GetValue()
Test_FInterpCurvePointVector2D_Var = cpp["cpp_FInterpCurvePointVector2D_Var"]:GetValue()
Test_FInterpCurvePointVector_Var = cpp["cpp_FInterpCurvePointVector_Var"]:GetValue()
Test_FInterpCurvePointQuat_Var = cpp["cpp_FInterpCurvePointQuat_Var"]:GetValue()
Test_FInterpCurvePointTwoVectors_Var = cpp["cpp_FInterpCurvePointTwoVectors_Var"]:GetValue()
Test_FInterpCurvePointLinearColor_Var = cpp["cpp_FInterpCurvePointLinearColor_Var"]:GetValue()
Test_FFloatRangeBound_Var = cpp["cpp_FFloatRangeBound_Var"]:GetValue()
Test_FFloatRange_Var = cpp["cpp_FFloatRange_Var"]:GetValue()
Test_FDoubleRangeBound_Var = cpp["cpp_FDoubleRangeBound_Var"]:GetValue()
Test_FDoubleRange_Var = cpp["cpp_FDoubleRange_Var"]:GetValue()
Test_FInt32RangeBound_Var = cpp["cpp_FInt32RangeBound_Var"]:GetValue()
Test_FInt32Range_Var = cpp["cpp_FInt32Range_Var"]:GetValue()
Test_FFloatInterval_Var = cpp["cpp_FFloatInterval_Var"]:GetValue()
Test_FDoubleInterval_Var = cpp["cpp_FDoubleInterval_Var"]:GetValue()
Test_FInt32Interval_Var = cpp["cpp_FInt32Interval_Var"]:GetValue()
Test_FFrameNumber_Var = cpp["cpp_FFrameNumber_Var"]:GetValue()
Test_FFrameTime_Var = cpp["cpp_FFrameTime_Var"]:GetValue()
Test_FSoftObjectPath_Var = cpp["cpp_FSoftObjectPath_Var"]:GetValue()
Test_FSoftClassPath_Var = cpp["cpp_FSoftClassPath_Var"]:GetValue()
Test_FPrimaryAssetType_Var = cpp["cpp_FPrimaryAssetType_Var"]:GetValue()
Test_FPrimaryAssetId_Var = cpp["cpp_FPrimaryAssetId_Var"]:GetValue()
Test_FDateTime_Var = cpp["cpp_FDateTime_Var"]:GetValue()
Test_FTopLevelAssetPath_Var = cpp["cpp_FTopLevelAssetPath_Var"]:GetValue()
Test_FVector2D_Var = cpp["cpp_FVector2D_Var"]:GetValue()
Test_FVector_Var = cpp["cpp_FVector_Var"]:GetValue()
Test_FVector4_Var = cpp["cpp_FVector4_Var"]:GetValue()
Test_FPlane_Var = cpp["cpp_FPlane_Var"]:GetValue()
Test_FQuat_Var = cpp["cpp_FQuat_Var"]:GetValue()
Test_FRotator_Var = cpp["cpp_FRotator_Var"]:GetValue()
Test_FTransform_Var = cpp["cpp_FTransform_Var"]:GetValue()
Test_FMatrix_Var = cpp["cpp_FMatrix_Var"]:GetValue()
Test_FBox2D_Var = cpp["cpp_FBox2D_Var"]:GetValue()
Test_FRay_Var = cpp["cpp_FRay_Var"]:GetValue()
Test_FSphere_Var = cpp["cpp_FSphere_Var"]:GetValue()

--Test_SluaFoi_Object = FTestSluaFOIDerivedClass()
--Test_SluaFoi_TArray_Var = FTestSluaFOIDerivedClass().Test_TArray_Var
--Test_SluaFoi_FString_Var = FTestSluaFOIDerivedClass().Test_FString_Var



-- hotfix functions
function HotfixFunction_DoublePtr(object, inArg1)
	print("HotfixFunction_DoublePtr ,", object, inArg1)
	return true, inArg1 + 1.0
end

function HotfixFunction_Int(object, inArg1)
	print("HotfixFunction_Int ,", object, inArg1)
	return true, inArg1 + 1
end

function HotfixFunction_IntPtr(object, inArg1)
	print("HotfixFunction_IntPtr ,", object, inArg1)
	return true, inArg1 + 1
end

function HotfixFunction_Bool(object, inArg1)
	print("HotfixFunction_Bool ,", object, inArg1)
	return true, not inArg1
end

function HotfixFunction_Float(object, inArg1)
	print("HotfixFunction_Float ,", object, inArg1)
	return true, inArg1 + 1
end

function HotfixFunction_FVector4(object, inArg1)
	print("HotfixFunction_FVector4 ,", object, inArg1)
	return true, cpp["Test_FVector4_Var2"]:GetValue()
end

function HotfixFunction_FVector4PTR(object, inArg1)
	print("HotfixFunction_FVector4PTR ,", object, inArg1)
	return true, FVector4(1, 0, 0, 0)
end

function HotfixFunction_Void(object, ...)
	print("HotfixFunction_void ,", object, ...)
	return true
end

function HotfixFunction_UObjectPTR(object, inArg1)
	print("HotfixFunction_UObjectPTR ,", object, inArg1)
	return true, Test_UObject_Var2
end

function HotfixFunction_FText(object, inArg1)
	print("HotfixFunction_FText ,", object, inArg1)
	return true, "Hotfix_Ftext"
end

function HotfixFunction_FTextPtr(object, inArg1)
	print("HotfixFunction_FTextPtr ,", object, inArg1)
	return true, "Hotfix_FTestPtr"
end

function HotfixFunction_Covariant(object, inArg1)
	print("HotfixFunction_Covariant ,", object, inArg1)
	object["index"] = inArg1 + 1
	return true, object
end

function HotfixFunction_FIntVector(object, inArg1)
	print("HotfixFunction_FIntVector ,", object, inArg1)
	return true, Test_FIntVector_Var
end

---------------------------- No Break Hotfix Functions ------------------------------

function HotfixFunction_DoublePtr_NoBreak(object, inArg1)
	print("HotfixFunction_DoublePtr_NoBreak ,", object, inArg1)
	return false, inArg1 + 1.0
end

function HotfixFunction_Int_NoBreak(object, inArg1)
	print("HotfixFunction_Int_NoBreak ,", object, inArg1)
	return false, inArg1 + 1
end

function HotfixFunction_IntPtr_NoBreak(object, inArg1)
	print("HotfixFunction_IntPtr_NoBreak ,", object, inArg1)
	return false, inArg1 + 1
end

function HotfixFunction_Bool_NoBreak(object, inArg1)
	print("HotfixFunction_Bool_NoBreak ,", object, inArg1)
	return false, not inArg1
end

function HotfixFunction_Float_NoBreak(object, inArg1)
	print("HotfixFunction_Float_NoBreak ,", object, inArg1)
	return false, inArg1 + 1
end

function HotfixFunction_FVector4_NoBreak(object, inArg1)
	print("HotfixFunction_FVector4_NoBreak ,", object, inArg1)
	return false, cpp["Global_FVector4_CompareValue_Case1"]:GetValue()
end

function HotfixFunction_FVector4PTR_NoBreak(object, inArg1)
	print("HotfixFunction_FVector4PTR_NoBreak ,", object, inArg1)
	return false, FVector4(1, 0, 0, 0)
end

function HotfixFunction_Void_NoBreak(object, ...)
	print("HotfixFunction_Void_NoBreak ,", object, ...)
	return false
end

function HotfixFunction_UObjectPTR_NoBreak(object, inArg1)
	print("HotfixFunction_UObjectPTR_NoBreak ,", object, inArg1)
	return false, Test_UObject_Var2
end

function HotfixFunction_FText_NoBreak(object, inArg1)
	print("HotfixFunction_FText_NoBreak ,", object, inArg1)
	return false, "HotfixFunction_FText_NoBreak"
end

function HotfixFunction_FTextPtr_NoBreak(object, inArg1)
	print("HotfixFunction_FTextPtr_NoBreak ,", object, inArg1)
	return false, "HotfixFunction_FTextPtr_NoBreak"
end

function HotfixFunction_Covariant_NoBreak(object, inArg1)
	print("HotfixFunction_Covariant_NoBreak ,", object, inArg1)
	object["index"] = inArg1 + 1
	return false, object
end

function HotfixFunction_FIntVector4(object, inArg1)
	print("HotfixFunction_FIntVector4 ,", object, inArg1)
	return true, Test_FIntVector4_Var
end

function HotfixFunction_FLinearColor(object, inArg1)
	print("HotfixFunction_FLinearColor ,", object, inArg1)
	return true, Test_FLinearColor_Var
end

function HotfixFunction_FColor(object, inArg1)
	print("HotfixFunction_FColor ,", object, inArg1)
	return true, Test_FColor_Var
end

function HotfixFunction_FRandomStream(object, inArg1)
	print("HotfixFunction_FRandomStream ,", object, inArg1)
	return true, Test_FRandomStream_Var
end

function HotfixFunction_FGuid(object, inArg1)
	print("HotfixFunction_FGuid ,", object, inArg1)
	return true, Test_FGuid_Var
end

function HotfixFunction_FFallbackStruct(object, inArg1)
	print("HotfixFunction_FFallbackStruct ,", object, inArg1)
	return true, Test_FFallbackStruct_Var
end

function HotfixFunction_FInterpCurvePointFloat(object, inArg1)
	print("HotfixFunction_FInterpCurvePointFloat ,", object, inArg1)
	return true, Test_FInterpCurvePointFloat_Var
end

function HotfixFunction_FInterpCurvePointVector2D(object, inArg1)
	print("HotfixFunction_FInterpCurvePointVector2D ,", object, inArg1)
	return true, Test_FInterpCurvePointVector2D_Var
end

function HotfixFunction_FInterpCurvePointVector(object, inArg1)
	print("HotfixFunction_FInterpCurvePointVector ,", object, inArg1)
	return true, Test_FInterpCurvePointVector_Var
end

function HotfixFunction_FInterpCurvePointQuat(object, inArg1)
	print("HotfixFunction_FInterpCurvePointQuat ,", object, inArg1)
	return true, Test_FInterpCurvePointQuat_Var
end

function HotfixFunction_FInterpCurvePointTwoVectors(object, inArg1)
	print("HotfixFunction_FInterpCurvePointTwoVectors ,", object, inArg1)
	return true, Test_FInterpCurvePointTwoVectors_Var
end

function HotfixFunction_FInterpCurvePointLinearColor(object, inArg1)
	print("HotfixFunction_FInterpCurvePointLinearColor ,", object, inArg1)
	return true, Test_FInterpCurvePointLinearColor_Var
end

function HotfixFunction_FFloatRangeBound(object, inArg1)
	print("HotfixFunction_FFloatRangeBound ,", object, inArg1)
	return true, Test_FFloatRangeBound_Var
end

function HotfixFunction_FFloatRange(object, inArg1)
	print("HotfixFunction_FFloatRange ,", object, inArg1)
	return true, Test_FFloatRange_Var
end

function HotfixFunction_FDoubleRangeBound(object, inArg1)
	print("HotfixFunction_FDoubleRangeBound ,", object, inArg1)
	return true, Test_FDoubleRangeBound_Var
end

function HotfixFunction_FDoubleRange(object, inArg1)
	print("HotfixFunction_FDoubleRange ,", object, inArg1)
	return true, Test_FDoubleRange_Var
end

function HotfixFunction_FInt32RangeBound(object, inArg1)
	print("HotfixFunction_FInt32RangeBound ,", object, inArg1)
	return true, Test_FInt32RangeBound_Var
end

function HotfixFunction_FInt32Range(object, inArg1)
	print("HotfixFunction_FInt32Range ,", object, inArg1)
	return true, Test_FInt32Range_Var
end

function HotfixFunction_FFloatInterval(object, inArg1)
	print("HotfixFunction_FFloatInterval ,", object, inArg1)
	return true, Test_FFloatInterval_Var
end

function HotfixFunction_FDoubleInterval(object, inArg1)
	print("HotfixFunction_FDoubleInterval ,", object, inArg1)
	return true, Test_FDoubleInterval_Var
end

function HotfixFunction_FInt32Interval(object, inArg1)
	print("HotfixFunction_FInt32Interval ,", object, inArg1)
	return true, Test_FInt32Interval_Var
end

function HotfixFunction_FFrameNumber(object, inArg1)
	print("HotfixFunction_FFrameNumber ,", object, inArg1)
	return true, Test_FFrameNumber_Var
end

function HotfixFunction_FFrameTime(object, inArg1)
	print("HotfixFunction_FFrameTime ,", object, inArg1)
	return true, Test_FFrameTime_Var
end

function HotfixFunction_FSoftObjectPath(object, inArg1)
	print("HotfixFunction_FSoftObjectPath ,", object, inArg1)
	return true, Test_FSoftObjectPath_Var
end

function HotfixFunction_FSoftClassPath(object, inArg1)
	print("HotfixFunction_FSoftClassPath ,", object, inArg1)
	return true, Test_FSoftClassPath_Var
end

function HotfixFunction_FPrimaryAssetType(object, inArg1)
	print("HotfixFunction_FPrimaryAssetType ,", object, inArg1)
	return true, Test_FPrimaryAssetType_Var
end

function HotfixFunction_FPrimaryAssetId(object, inArg1)
	print("HotfixFunction_FPrimaryAssetId ,", object, inArg1)
	return true, Test_FPrimaryAssetId_Var
end

function HotfixFunction_FDateTime(object, inArg1)
	print("HotfixFunction_FDateTime ,", object, inArg1)
	return true, Test_FDateTime_Var
end

function HotfixFunction_FTopLevelAssetPath(object, inArg1)
	print("HotfixFunction_FTopLevelAssetPath ,", object, inArg1)
	return true, Test_FTopLevelAssetPath_Var
end

function HotfixFunction_FVector2D(object, inArg1)
	print("HotfixFunction_FVector2D ,", object, inArg1)
	return true, Test_FVector2D_Var
end

function HotfixFunction_FVector(object, inArg1)
	print("HotfixFunction_FVector ,", object, inArg1)
	return true, Test_FVector_Var
end

function HotfixFunction_FVector4_FSturct(object, inArg1)
	print("HotfixFunction_FVector4 ,", object, inArg1)
	return true, Test_FVector4_Var
end

function HotfixFunction_FPlane(object, inArg1)
	print("HotfixFunction_FPlane ,", object, inArg1)
	return true, Test_FPlane_Var
end

function HotfixFunction_FQuat(object, inArg1)
	print("HotfixFunction_FQuat ,", object, inArg1)
	return true, Test_FQuat_Var
end

function HotfixFunction_FRotator(object, inArg1)
	print("HotfixFunction_FRotator ,", object, inArg1)
	return true, Test_FRotator_Var
end

function HotfixFunction_FTransform(object, inArg1)
	print("HotfixFunction_FTransform ,", object, inArg1)
	return true, Test_FTransform_Var
end

function HotfixFunction_FMatrix(object, inArg1)
	print("HotfixFunction_FMatrix ,", object, inArg1)
	return true, Test_FMatrix_Var
end

function HotfixFunction_FBox2D(object, inArg1)
	print("HotfixFunction_FBox2D ,", object, inArg1)
	return true, Test_FBox2D_Var
end

function HotfixFunction_FRay(object, inArg1)
	print("HotfixFunction_FRay ,", object, inArg1)
	return true, Test_FRay_Var
end

function HotfixFunction_FSphere(object, inArg1)
	print("HotfixFunction_FSphere ,", object, inArg1)
	return true, Test_FSphere_Var
end