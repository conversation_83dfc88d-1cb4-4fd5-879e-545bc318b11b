

--[[
	Desc:		Test SharedPtr
]]
local function TestSharedPtr()
	local Case0 = cpp["FSimpleTestClass.CreateSharedPtr"](1, FVector(1, 1, 1), "ABC")
	local Case1 = cpp["FSimpleTestClass.CreateSharedPtr"](1, FVector(1, 1, 1), "大家好!")
	local Case2 = cpp["FSimpleTestClass.CreateSharedPtr"](-2147483648, FVector(3.14, 78, 1.11111111111111), "hello world!")
	AssertTrue(Case0:Equals(Case0, 0.0001), "TSharedPtr<FSimpleTestClass> not equal with self. case 0")
	AssertTrue(Case1:Equals(Case1, 0.0001), "TSharedPtr<FSimpleTestClass> not equal with self. case 1")
	AssertTrue(Case2:Equals(Case2, 0.0001), "TSharedPtr<FSimpleTestClass> not equal with self. case 2")
	
	TestMemberVariable("FVariableTestClass", "Member_TSharedPtrFSimpleTestClass_Case0", Case0, {Case1, Case2})
	--TestMemberVariable("FVariableTestClass", "Member_TSharedRefFSimpleTestClass_Case0", Case0, {Case1, Case2})
	
	TestFunctionCall("FVariableTestClass", "Member_TSharedPtrFSimpleTestClass_Function_Case0", false, Case0)
	TestFunctionCall("FVariableTestClass", "Member_TSharedPtrFSimpleTestClass_Function_Case1", false, Case1)
	--TestFunctionCall("FVariableTestClass", "Member_TSharedRefFSimpleTestClass_Function_Case0", false, Case0)
	--TestFunctionCall("FVariableTestClass", "Member_TSharedRefFSimpleTestClass_Function_Case1", false, Case1)

	TestFunctionInject("FVariableTestClass", "Member_TSharedPtrFSimpleTestClass_Function_Case0", "Member_TSharedPtrFSimpleTestClass_Function_Case0_TSharedPtr__FSimpleTestClass_1___C_", "BEFORE", false, Case0, {Case1, Case2})
	--TestFunctionInject("FVariableTestClass", "Member_TSharedRefFSimpleTestClass_Function_Case0", "Member_TSharedRefFSimpleTestClass_Function_Case0_TSharedRef__FSimpleTestClass_1___C_", "BEFORE", false, Case0, {Case1, Case2})
	
	Case0.IntValue = 2
	Case1.IntValue = 2
	Case0.StringValue = "\0\\\\!@#$^%@!$#$*&)*^&(_&)(~!  你好  \1\0"
	Case1.StringValue = "\0\\\\!@#$^%@!$#$*&)*^&(_&)(~!  你好  \1\0"
	AssertTrue(Case0:Equals(Case1, 0.0001), "Test SharedPtr: Case0 not equal with case 1")

end

--[[
	Desc:		Test TMap type member of class  
]]
local function TestMemberTMap()
	local Case0 = cpp.FInheritField()
	local mapValue = Case0.MapValue
	print(mapValue, type(mapValue), CppHotfixToString(getmetatable(mapValue)))
	
	mapValue:Add("Key1", 1)
	mapValue:Add("Key2", 1)
	mapValue:Add("Key2", 2)
	Case0.MapValue = mapValue
	
	local mapValue2 = Case0.MapValue
	AssertTrue(mapValue2:Num() == 2, "Test Map KeyValue: Number should be 2, but is " .. tostring(mapValue2:Num()) .. ", not 2")
	AssertTrue(mapValue2:Get("Key1") == 1, "Test Map KeyValue: Key1 is " .. tostring(mapValue2:Get("Key1")) .. ", not 1")
	AssertTrue(mapValue2:Get("Key2") == 2, "Test Map KeyValue: Key2 is " .. tostring(mapValue2:Get("Key2")) .. ", not 2")
	
	
	local structType = import("SimpleTestStruct")
	local structValue = structType()
	structValue.IntArray:Add(1)
	structValue.IntArray:Add(2)
	AssertTrue(structValue.IntArray:Num() == 2, "Int Array Num = " .. tostring(structValue.IntArray:Num()) .. ", not 2")
end

TestCall(TestMemberTMap, "TestTMap")
--TestCall(TestSharedPtr, "Test Shared Ptr")
--TestCall(TestSharedPtr2, "Test Shared Ptr 2")


return true