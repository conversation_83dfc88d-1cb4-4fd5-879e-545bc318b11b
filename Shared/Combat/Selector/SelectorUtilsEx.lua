local SharedAPI = kg_require("Shared.SharedAPI").SharedAPI
local AbilityConst = kg_require("Shared.Const.AbilityConst")

local EBaseCoordinatePointType = AbilityConst.EBaseCoordinatePointType
local EValidTargetCheckType = AbilityConst.EValidTargetCheckType
local ETargetAliveType = AbilityConst.ETargetAliveType

local TableData = TableData
local pairs = pairs
local ipairs = ipairs
local next = next

local bit = bit
if not _G.IS_SERVER then
    TableData = Game.TableData
    pairs = ksbcpairs
    ipairs = ksbcipairs
    next = ksbcnext
    bit = require("Framework.Utils.bit")
end

--region API
function GetTargetSelectionRuleDataList(ruleRowData)
    -- TODO: SelectorRuleData 格式后续会迭代
    local selectorRuleData = ruleRowData and ruleRowData.SelectorRuleData
    local ruleDataList = selectorRuleData and selectorRuleData[2]
    return ruleDataList
end

__query_entities_self_pos   = SharedAPI.NewVector3()
__query_entities_target_pos = SharedAPI.NewVector3()

---@param basedActor ActorBase: 基准单位(self)
---@param targetActor ActorBase?: 目标单位(target), 用以基准位置计算, 默认取 lockTarget
---@param staticBlackboard table: 外部传入的黑板
---@param coordinatePointType number(EBaseCoordinatePointType): 基准坐标点类型
---@param coordinateOffset number[]: 坐标点偏移
---@param clockwiseRotation number: 顺时针旋转角度(先旋转，再偏移)
---@param outPos TVector3:输出位置坐标
---@param listIndex number:取黑板值list中数据索引
---@param inSelfPos TVector3:外部传入self位置
---@param inSelfYaw number:外部传入self朝向
function GetPosAndYawByCoordinatePointType(basedActor, inTargetActor, staticBlackboard,
                                           coordinatePointType, coordinateOffset, clockwiseRotation, outPos, listIndex,
                                           inSelfPos, inSelfYaw)
    local basedPos = inSelfPos or SharedAPI.GetFloorPositionOut(basedActor, __query_entities_self_pos)
    local baseYaw = inSelfYaw or SharedAPI.GetYaw(basedActor)

    local targetActor = inTargetActor
    if not targetActor and staticBlackboard then
        local targetActorID = staticBlackboard.OverrideLockTarget or staticBlackboard.lockTarget
        targetActor = SharedAPI.GetEntityByIntID(targetActorID, basedActor)
    end

    local targetPos = targetActor and SharedAPI.GetFloorPositionOut(targetActor, __query_entities_target_pos)
    local targetYaw = targetActor and SharedAPI.GetYaw(targetActor)

    return GetPosAndYawByCoordinatePointTypeRaw(basedActor, basedPos, baseYaw, targetPos, targetYaw, staticBlackboard,
        coordinatePointType, coordinateOffset, clockwiseRotation, outPos, listIndex, false)
end

__query_entities_dir = SharedAPI.NewVector3()
__query_entities_offset = SharedAPI.NewVector3()

---@param inSelfPos TVector3: 基准单位(self)的位置
---@param inSelfYaw number: 基准单位(self)的朝向
---@param inTargetPos TVector3: 目标单位(target)的位置, 基准位置计算的时候，可能会用到
---@param inTargetYaw number: 目标单位(target)的朝向
---@param staticBlackboard table: 外部传入的黑板
---@param coordinatePointType number(EBaseCoordinatePointType): 基准坐标点类型
---@param coordinateOffset number[]: 坐标点偏移
---@param clockwiseRotation number: 顺时针旋转角度(先旋转，再偏移)
---@param outPos TVector3:输出位置坐标
---@param listIndex number:取黑板值list中数据索引
---@param bNotUnwindDegree boolean:(optional), true的话，直接进行角度的叠加, 不unwind
function GetPosAndYawByCoordinatePointTypeRaw(entity, inSelfPos, inSelfYaw, inTargetPos, inTargetYaw, staticBlackboard,
                                              coordinatePointType, coordinateOffset, clockwiseRotation, outPos, listIndex,
                                              bNotUnwindDegree)
    local baseYaw
    local basedPos = outPos
    local needOffset = false

    if coordinatePointType == EBaseCoordinatePointType.Self then
        SharedAPI.GetFloorPositionOut(entity, basedPos)
        basedPos = SharedAPI.Vector3Reset(basedPos, inSelfPos)
        baseYaw = inSelfYaw
    elseif coordinatePointType == EBaseCoordinatePointType.SelfOffset then
        basedPos = SharedAPI.Vector3Reset(basedPos, inSelfPos)
        baseYaw = inSelfYaw
        needOffset = true
    elseif coordinatePointType == EBaseCoordinatePointType.Target then
        if inTargetPos == nil then
            SharedAPI.LogWarnFmt("GetPosAndYawByCoordinatePointType, coordinatePointType is Target, but target is nil")
            return nil
        end

        basedPos = SharedAPI.Vector3Reset(basedPos, inTargetPos)
        baseYaw = inTargetYaw
    elseif coordinatePointType == EBaseCoordinatePointType.TargetOffset then
        if inTargetPos == nil then
            SharedAPI.LogWarnFmt(
                "GetPosAndYawByCoordinatePointType, coordinatePointType is TargetOffset, but target is nil")
            return nil
        end

        basedPos = SharedAPI.Vector3Reset(basedPos, inTargetPos)
        baseYaw = inTargetYaw
        needOffset = true
    elseif coordinatePointType == EBaseCoordinatePointType.TargetToSelf then
        if inTargetPos == nil then
            SharedAPI.LogWarnFmt(
                "GetPosAndYawByCoordinatePointType, coordinatePointType is TargetToSelf, but target is nil")
            return nil
        end

        basedPos = SharedAPI.Vector3Reset(basedPos, inTargetPos)
        local diffVec = SharedAPI.Vector3SubVecOut(inSelfPos, basedPos, __query_entities_dir)
        baseYaw = SharedAPI.Vector3GetTargetYawByDiff(diffVec)
        needOffset = true
    elseif coordinatePointType == EBaseCoordinatePointType.SelfToTarget then
        if inTargetPos == nil then
            SharedAPI.LogWarnFmt(
                "GetPosAndYawByCoordinatePointType, coordinatePointType is SelfToTarget, but target is nil")
            return nil
        end

        basedPos = SharedAPI.Vector3Reset(basedPos, inSelfPos)
        local diffVec = SharedAPI.Vector3SubVecOut(inTargetPos, basedPos, __query_entities_dir)
        baseYaw = SharedAPI.Vector3GetTargetYawByDiff(diffVec)
        needOffset = true
    elseif coordinatePointType == EBaseCoordinatePointType.WorldPos then
        SharedAPI.Vector3ResetEx(basedPos, coordinateOffset.X or 0.0, coordinateOffset.Y or 0.0,
            coordinateOffset.Z or 0.0)
        baseYaw = 0
    elseif coordinatePointType == EBaseCoordinatePointType.PosList then
        local posList = staticBlackboard and staticBlackboard.searchPosList
        listIndex = listIndex or 1
        local pos = posList and posList[listIndex]
        if pos then
            SharedAPI.Vector3ResetEx(basedPos, pos.X or 0.0, pos.Y or 0.0, pos.Z or 0.0)
        else
            return nil
        end
        baseYaw = 0
    elseif coordinatePointType == EBaseCoordinatePointType.TargetList then
        local targetIDList = staticBlackboard and staticBlackboard.searchTargetList
        listIndex = listIndex or 1
        local targetID = targetIDList and targetIDList[listIndex]
        local targetEnt = SharedAPI.GetEntityByIntID(targetID, entity)
        if targetEnt == nil then
            SharedAPI.LogWarnFmt("GetPosAndYawByCoordinatePointType, target in list is nil, targetID:%s", targetID)
            return
        end

        basedPos = SharedAPI.GetFloorPositionOut(targetEnt, basedPos)
        baseYaw = SharedAPI.GetYaw(targetEnt)
    elseif coordinatePointType == EBaseCoordinatePointType.Location then
        local inputPos = staticBlackboard and staticBlackboard.inputPos
        local inputDir = staticBlackboard and staticBlackboard.inputDir
        baseYaw = inputDir and inputDir.Yaw or 0
        if inputPos then
            SharedAPI.Vector3ResetEx(basedPos, inputPos.X or 0.0, inputPos.Y or 0.0, inputPos.Z or 0.0)
        else
            -- 无右摇杆输入, 则退化到身前技能最远释放距离作为 basedPos
            local skillID = staticBlackboard.skillID or staticBlackboard.rootSkillID
            local skillData = TableData.GetSkillDataNewRow(skillID)
            if not skillData then
                return
            end
            local unitDir = SharedAPI.Vector3ResetByYaw(__query_entities_dir, inSelfYaw)
            local dir = SharedAPI.Vector3MulNum(unitDir, skillData.Dist or 0)
            SharedAPI.Vector3Reset(basedPos, inSelfPos)
            basedPos = SharedAPI.Vector3AddVec(basedPos, dir)
        end
    elseif coordinatePointType == EBaseCoordinatePointType.SelfInput then
        basedPos = SharedAPI.Vector3Reset(basedPos, inSelfPos)
        local inputDir = staticBlackboard and staticBlackboard.inputDir
        baseYaw = inputDir and inputDir.Yaw or 0
        needOffset = true
    else
        SharedAPI.LogWarnFmt("GetCenterPosAndYawByCoordinatePointType, UnSupport coordinatePointType[%s]",
            coordinatePointType)
        return nil
    end

    clockwiseRotation = clockwiseRotation or 0
    baseYaw = baseYaw + clockwiseRotation
    if not bNotUnwindDegree then
        baseYaw = SharedAPI.UnwindDegrees(baseYaw)
    end

    if needOffset then
        local offset = __query_entities_offset
        SharedAPI.Vector3ResetEx(offset, coordinateOffset.X or 0.0, coordinateOffset.Y or 0.0, 0.0)
        SharedAPI.Vector3ResetRotateVectorByYaw(offset, offset, baseYaw)
        SharedAPI.Vector3AddVec(basedPos, offset)
    end

    return basedPos, baseYaw
end

function GetIgnoreCheckMask(ruleData)
    local ignoreCheckMask = 0
    if not ruleData then return ignoreCheckMask end

    ignoreCheckMask = bit.bor(ignoreCheckMask, (ruleData.IgnoreCheckMask or 0))
    if ruleData.TargetAliveType == ETargetAliveType.None then
        ignoreCheckMask = bit.bor(ignoreCheckMask, EValidTargetCheckType.Dead)
    end

    return ignoreCheckMask
end

--endregion
