SharedAPI = Game.SharedAPI

local math_abs = math.abs
local math_sqrt = math.sqrt
local math_rad = math.rad
local math_sin = math.sin
local math_cos = math.cos
local math_asin = math.asin
local math_acos = math.acos
local math_fmod = math.fmod
local math_atan2 = math.atan2 or math.atan
local PI = math.pi


local DEGREE_PER_RADIAN = 180.0 / PI

--region Actor

--endregion

--region Math
SharedAPI.Vector3ResetByYaw = function(vec3, yaw)
    local rad = math_rad(yaw)
    vec3.X = math_cos(rad)
    vec3.Y = math_sin(rad)
    vec3.Z = 0

    return vec3
end

SharedAPI.Vector3AddVec = function(vec3, otherVec3)
    vec3.X = vec3.X + otherVec3.X
    vec3.Y = vec3.Y + otherVec3.Y
    vec3.Z = vec3.Z + otherVec3.Z
    return vec3
end

SharedAPI.Vector3SubVecOut = function(vec3, otherVec3, outVec3)
    outVec3.X = vec3.X - otherVec3.X
    outVec3.Y = vec3.Y - otherVec3.Y
    outVec3.Z = vec3.Z - otherVec3.Z
    return outVec3
end

SharedAPI.Vector3MulNum = function(vec3, number)
    vec3.X = vec3.X * number
    vec3.Y = vec3.Y * number
    vec3.Z = vec3.Z * number
    return vec3
end


SharedAPI.Vector3ResetRotateVectorByYaw = function(vec3, outVec3, yaw)
    local rad = math_rad(yaw)
    local si = math_sin(rad)
    local co = math_cos(rad)

    local x = vec3.X
    local y = vec3.Y

    outVec3.X = x * co - y * si
    outVec3.Y = x * si + y * co
    outVec3.Z = vec3.Z
    return outVec3
end

SharedAPI.Vector3GetTargetYawByDiff = function(vec3)
    return math_atan2(vec3.Y, vec3.X) * DEGREE_PER_RADIAN
end

-- 修正转向角度(-180, 180)
SharedAPI.UnwindDegrees = function(yaw)
    while yaw > 180.0 do
        yaw = yaw - 360.0
    end

    while yaw < -180.0 do
        yaw = yaw + 360.0
    end

    return yaw
end
--endregion
