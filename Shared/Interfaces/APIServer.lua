assert(_G.IS_SERVER)
SharedAPI = Game.SharedAPI

local MathUtilsEx = kg_require("Logic.Utils.Math.MathUtilsEx") -- luacheck: ignore

--region Actor

SharedAPI.GetEntityByIntID = function(entityID, basedActor)
    if basedActor then
        local space = basedActor.Space
        return space and space.ientities[entityID]
    end
    return _script.ientities[entityID]
end

SharedAPI.GetFloorPositionOut = function(actor, outPos)
    return actor:GetFloorPositionOut(outPos)
end

SharedAPI.GetYaw = function(actor)
    return actor:GetYaw()
end

--endregion

--region Math

SharedAPI.NewVector3 = function()
    return MathUtilsEx.TVector3_New()
end

SharedAPI.Vector3Reset = function(vec3, otherVec3)
    return MathUtilsEx.TVector3_ResetVec3(vec3, otherVec3)
end

SharedAPI.Vector3ResetEx = function(vec3, x, y, z)
    return MathUtilsEx.TVector3_Reset(vec3, x, y, z)
end

--endregion

--region Log

SharedAPI.LogWarnFmt = function(format, ...)
    return LOG_WARN_FMT(format, ...)
end

SharedAPI.LogErrorFmt = function(format, ...)
    return LOG_ERROR_FMT(format, ...)
end

--endregion
