--不要在此文件里使用kg_require或者require @hedongchang

EWActorType = {
    None = 0,
    PLAYER = 1, --玩家
    NPC = 2, --NPC

    MONSTER_MIN = 10001, --怪物初始-------
    MONSTER_NORMAL = 10002, --普通怪
    MONSTER_MAX = 20000, --怪物结束-------

    NPC_MIN = 20001, --NPC开始-------
    NPC_TASK = 20002, --任务NPC
	NPC_CROWD = 20005, -- 氛围Npc
	NPC_CROWD_INTERESTING = 20006, -- 氛围Npc感兴趣的Npc, 如马车
    NPC_MAX = 30000, --NPC结束-------

    SCENEACTOR_MIN = 30001, --场景物体开始---------
    DECAL_ACTOR = 30002,

    INTERACTIVE_CHAIR = 40001, --可交互的椅子
    SCENE_TEXT = 40002, --文字板


    CONSECRATION_MONUMENT = 40006, --七天神像
    INTERACTIVE_SWING = 40007, --秋千
    NON_INTERACT_MESH = 40008, --不可交互带状态mesh

    SPIRIT_REALM_PORTAL = 40010, --灵界传送交互物
    INTERACTIVE_DOOR = 40011, --可交互门
    PHARMACY_SPOON = 40013, --UI药师勺子,
    CONSECRATION_SPIRIT = 40014, --奉献之灵

    FOG_TRIGGER = 40016, -- 迷雾驱散触发器
    CLOCK_LAMP = 40017, --挂钟
    MIRROR = 40018, -- 镜子




    HEIGHT_FOG_MODIFIER = 40023, --接触时修改体积雾




    TOWER_CLIMB_PORTAL = 40028, --爬塔通关后出现的传送门
    SPLINE_FOOT_PRINT = 40029, --脚印样条线
    PLANE_PORTAL = 40030, --位面传送门
    INTERACTIVE_PORTAL = 40031, --大世界传送门
    NIAGARA_CARRIER_V2 = 40032, --特效载体(新版)

    SCENEACTOR_WINDMILL = 40034, --风车, flowchart控制转速
    COLLECTION = 40035, --新采集物

    CAMERA_CONTROL_VOLUME = 40037, --摄像机控制Volume
    MONSTER_CHASE = 40038, --追蝴蝶
    LARGE_CREATURE = 40039, --大型生物

    INTERACTIVE_STREETLIGHT = 40041, --可交互灯
    STREET_LIGHT = 40042, --TOD路灯



    MAGIC_WALL = 40046, --魔法墙

    BATTLE_ZONE = 40048, --战斗区域

    LOAD_SEQUENCE = 40050, --加载Sequence
    TRIGGER_LIGHT = 40051, --目标标识
    SPIRITUALITY_WALL = 40052, -- 灵性之墙
    MESH_CARRIER = 40053,


    JUMP_POINT = 40056, --跳跃点
    SHADOW_MONSTER = 40057, --暗影怪


    GROW_DECAY_PLANT = 40061, --生长衰减植物






    PREPARE_ZONE = 40068, --准备区域
    TEAM_MARK = 40069, --组队标记
    DROP_ITEM = 40070, --掉落道具

    PAINTING_VIDEO = 40072, -- 挂画视频

    DROP_ITEM_GROUP = 40074, --掉落道具打包盒
    ESTATE_PORTAL = 40075, --五月庄园传送门
    MOVABLE_PLATFORM_WAY_PATH = 40076, --移动平台(电梯)平台层
    MOVABLE_PLATFORM = 40077, --移动平台(电梯)轿厢
    KG_SPOT_LIGHT_V2 = 40078, --聚光灯
	DIALOGUE_TRIGGER = 40081, --加载对话
    SOUND_TRACE = 40082, -- 寻声玩法

    TURNTABLE_PUZZLE_TRIGGER = 40084, --转盘解谜玩法区域

    TURNTABLE_PUZZLE_TURNTABLE = 40086, --转盘解谜玩法 转盘
    TURNTABLE_CROSS_AREA = 40087, --转盘解谜玩法 转盘交叉区域
    TURNTABLE_BARRIER = 40088, --转盘解谜玩法 标记物
    TURNTABLE_RESET_DEVICE = 40089, --转盘解谜玩法 重置装置


    CUTTABLE_TREE = 40092, --可以被砍的树
    DECAL_CARRIER = 40094,--场景摆放的Decal
    CHASED_MONSTER = 40095,--被追的动物
	TASK_PLANE_PORTAL = 40096,--任务专用本地传送门
	SPIRIT_EFFECT_LINE = 40097, --灵性特效线

	POINT_LIGHT = 40100, --点光源






	PILLAR = 40114, -- 神像柱子

	CUSTOM_SHAPE_WALL = 40150, --自定义边界
    BIND_VIEW_CONTROLLER = 40151, --绑定美术场景物

    AUNIVERSAL_INTERACTOR = 59998, --通用交互物
    GROUP_ACTOR = 59999, --Group

    SCENEACTOR_MAX = 60000, --场景物体结束---------

    SCENEACTOR_DATA_MIN = 60001, --场景数据开始---------

    GAME_CONTROL_VOLUME = 60002, -- 游戏控制相机数据
    MANOR_CAMERA = 60003, -- MANOR相机数据
    NPC_SPAWNER = 60004, -- NpcSpawner
    NPC_SINGLE_SPAWNER = 60005, -- NpcSingleSpawner
    RESPAWN_POINT = 60006, -- 出生点

    TELEPORT_POINT = 60008, --传送点
    WAY_POINT_PATH = 60009, --路径
    WAY_SINGLE_POINT = 60010, --单路点
    OCCUPY_DETECT_AREA = 60011, --公会抢点数据
    SHAPE_TRIGGER = 60012, --方或圆形触发器
	MASS_NPC_TRIGGER_POINT = 60013, -- 氛围NPC触发点

    COMMON_INTERACTOR = 60015, -- 通用交互物
    CUSTOMIZED_GAMEPLAY = 60016, -- 定制玩法

    VOLUME_FOG = 60017, -- 体积雾
    
	PVP_MAGIC_POINT = 60018, -- PVP法阵
	
    SCENEACTOR_DATA_MAX = 70000, --场景数据结束---------



    ABILITY_MIN = 70001, --技能生成物开始-------


    ABILITY_MAX = 80000, --技能生成物结束-------

	POS_TRIGGER_POINT = 80001, -- 场景pos触发器
}

SceneActorTypeEnum = {
    EWActorType.INTERACTIVE_CHAIR,
    EWActorType.INTERACTIVE_SWING,
    EWActorType.INTERACTIVE_DOOR
}

DEFAULT_HIDDEN_ACTOR_TYPES = {
    [EWActorType.VOLUME_FOG] = true
}