local parallel_behavior_control_const = kg_require("Shared.Const.ParallelBehaviorControlConst")
local LOCO_ANIM_STATE_CONST = parallel_behavior_control_const.LOCO_ANIM_STATE_CONST


--- 输入枚举
---
LOCO_ANIM_STATE_CONST = kg_require("Shared.Const.ParallelBehaviorControlConst").LOCO_ANIM_STATE_CONST

ROLE_MOVEMENT_DISABLE_TAG = {
    LOCO_MOVE = 1000
}

ATTACH_ROOT_SOCKET_NAME = ""  -- 要挂载Root上, 传入空即可

-- ==============================变身控制 =================================================
NO_MORPH_ID = 0 -- 要与MorphID  Property默认值一致
NO_MORPH_FAST_FACADE_CONTROL_ID = -1   -- 要与MorphFastFacadeControlID Property默认值一致
SPECIAL_MORPH_ID_FOR_NPC = 2809999  -- NPC变身时将MorphID设置为该值
SPECIAL_POSSESS_ID_FOR_PLAYER = 2909999  -- 玩家附身时将MorphID设置为该值

--================================动画控制==================================================
COMMON_ANIM_CROSSFADE_TIME = 0.1


-- ===============================基础移动==============================================
INVALID_MOTION_WARP_TOKEN = -1

FOOT_CURVE_NAME = "FootCurve"  -- 值小于0为左脚区间

LOCOMOTION_SM_NAME = 'Locomotion'
LocoStateTypeToAnimStateNameMap = LocoStateTypeToAnimStateNameMap or
        {
            [LOCO_ANIM_STATE_CONST.Idle] = "Idle",
            [LOCO_ANIM_STATE_CONST.RunStart] = "RunStart",
            [LOCO_ANIM_STATE_CONST.Run] = "Run",
            [LOCO_ANIM_STATE_CONST.RunEnd] = "RunEnd",
            [LOCO_ANIM_STATE_CONST.MoveTurn] = "MoveTurn",
            [LOCO_ANIM_STATE_CONST.JumpStart] = "JumpStart",
            [LOCO_ANIM_STATE_CONST.JumpLoop] = "JumpLoop",
            [LOCO_ANIM_STATE_CONST.JumpEndLow] = "JumpEndLow",
            [LOCO_ANIM_STATE_CONST.JumpEndHigh] = "JumpEndHigh",
            [LOCO_ANIM_STATE_CONST.JumpEndRestartLow] = "JumpEndRestartLow",
            [LOCO_ANIM_STATE_CONST.JumpEndRestartHigh] = "JumpEndRestartHigh",
            [LOCO_ANIM_STATE_CONST.GlideStart] = "GlideStart",
            [LOCO_ANIM_STATE_CONST.GlideLoop] = "GlideLoop",
            [LOCO_ANIM_STATE_CONST.RidingIdle] = "RidingIdle",
            [LOCO_ANIM_STATE_CONST.RidingStart] = "RidingStart",
            [LOCO_ANIM_STATE_CONST.RidingLoop] = "RidingLoop",
            [LOCO_ANIM_STATE_CONST.RidingEnd] = "RidingEnd",
            [LOCO_ANIM_STATE_CONST.RidingMoveTurn] = "RidingMoveTurn",
			[LOCO_ANIM_STATE_CONST.RidingMoveTurnEnd] = "RidingMoveTurnEnd",
			[LOCO_ANIM_STATE_CONST.Riding90LeftMoveTurn] = "Riding90LeftMoveTurn",
			[LOCO_ANIM_STATE_CONST.Riding90RightMoveTurn] = "Riding90RightMoveTurn",
            [LOCO_ANIM_STATE_CONST.MultiJumpEndRestart]  = "MultiJumpEndRestart",
            [LOCO_ANIM_STATE_CONST.RidingDash] = "RidingDash",
            [LOCO_ANIM_STATE_CONST.WaterGetIn] = "WaterGetIn",
            [LOCO_ANIM_STATE_CONST.WaterGetOut] = "WaterGetOut",
            [LOCO_ANIM_STATE_CONST.RidingJumpStart]  = "RidingJumpStart",
			[LOCO_ANIM_STATE_CONST.RidingJumpStartSecond]  = "RidingJumpStartSecond",
            [LOCO_ANIM_STATE_CONST.RidingJumpLoop] = "RidingJumpLoop",
            [LOCO_ANIM_STATE_CONST.RidingJumpEnd] = "RidingJumpEnd",
            [LOCO_ANIM_STATE_CONST.RidingJumpEndRestart] = "RidingJumpEndRestart",
            [LOCO_ANIM_STATE_CONST.DizzinessStart] = "DizzinessStart",
            [LOCO_ANIM_STATE_CONST.DizzinessIdle] = "DizzinessIdle",
            [LOCO_ANIM_STATE_CONST.DizzinessEnd] = "DizzinessEnd",
            [LOCO_ANIM_STATE_CONST.DizzinessWalk] = "DizzinessWalk",
            [LOCO_ANIM_STATE_CONST.WaterIdle] = "WaterIdle",
            [LOCO_ANIM_STATE_CONST.WaterRunStart] = "WaterRunStart",
            [LOCO_ANIM_STATE_CONST.WaterRun] = "WaterRun",
            [LOCO_ANIM_STATE_CONST.WaterRunEnd] = "WaterRunEnd",
            [LOCO_ANIM_STATE_CONST.WaterMoveTurn] = "WaterMoveTurn",
            [LOCO_ANIM_STATE_CONST.WaterJumpStart] = "WaterJumpStart",
            [LOCO_ANIM_STATE_CONST.WaterJumpLoop] = "WaterJumpLoop",
            [LOCO_ANIM_STATE_CONST.WaterJumpEnd] = "WaterJumpEnd",
            [LOCO_ANIM_STATE_CONST.WaterJumpEndRestart] = "WaterJumpEndRestart",
            [LOCO_ANIM_STATE_CONST.WaterJumpStartSecond] = "WaterJumpStartSecond",
            [LOCO_ANIM_STATE_CONST.WaterJumpStartThird] = "WaterJumpStartThird",
            [LOCO_ANIM_STATE_CONST.WaterJumpLoopThird] = "WaterJumpLoopThird",
            [LOCO_ANIM_STATE_CONST.WaterJumpEndThird] = "WaterJumpEndThird",
            [LOCO_ANIM_STATE_CONST.WaterDash] = "WaterDash",
            [LOCO_ANIM_STATE_CONST.JumpStartSecond] = "JumpStartSecond",
            [LOCO_ANIM_STATE_CONST.JumpStartThird] = "JumpStartThird",
            [LOCO_ANIM_STATE_CONST.JumpLoopThird] = "JumpLoopThird",
            [LOCO_ANIM_STATE_CONST.JumpEndThird] = "JumpEndThird",
			[LOCO_ANIM_STATE_CONST.MountPassengerIdle] = "MountPassengerIdle",
			[LOCO_ANIM_STATE_CONST.MountPassengerDash] = "MountPassengerDash",
			[LOCO_ANIM_STATE_CONST.MountPassengerMoveTurn] = "MountPassengerMoveTurn",
			[LOCO_ANIM_STATE_CONST.MountPassengerJumpStart] = "MountPassengerJumpStart",
			[LOCO_ANIM_STATE_CONST.MountPassengerJumpEnd] = "MountPassengerJumpEnd",
			[LOCO_ANIM_STATE_CONST.CameraDirMoveStart] = "CameraDirMoveStart",
			[LOCO_ANIM_STATE_CONST.CameraDirMoveLoop] = "CameraDirMoveLoop",
			[LOCO_ANIM_STATE_CONST.CameraDirMoveEnd] = "CameraDirMoveEnd",
            [LOCO_ANIM_STATE_CONST.Dash] = "Dash",
			[LOCO_ANIM_STATE_CONST.DashRestart] = "DashRestart",
            [LOCO_ANIM_STATE_CONST.WaterJumpEndRestartThird] = "WaterJumpEndRestartThird",
            [LOCO_ANIM_STATE_CONST.BackUpState1] = "BackUpState1",
            [LOCO_ANIM_STATE_CONST.BackUpState2] = "BackUpState2",
            [LOCO_ANIM_STATE_CONST.BackUpState3] = "BackUpState3",
            [LOCO_ANIM_STATE_CONST.BackUpState4] = "BackUpState4",
            [LOCO_ANIM_STATE_CONST.BackUpState5] = "BackUpState5",
            [LOCO_ANIM_STATE_CONST.ClimbIdle] = "ClimbIdle",
            [LOCO_ANIM_STATE_CONST.ClimbMove] = "ClimbMove",
            [LOCO_ANIM_STATE_CONST.SpiritualDash] = "SpiritualDash",
            [LOCO_ANIM_STATE_CONST.SpiritualJump] = "SpiritualJump",
            [LOCO_ANIM_STATE_CONST.SpiritualDown] = "SpiritualDown",
        }

LocoAnimStateNameToStateTypeMap = LocoAnimStateNameToStateTypeMap or
        {
            ["Idle"] = LOCO_ANIM_STATE_CONST.Idle,
            ["RunStart"] = LOCO_ANIM_STATE_CONST.RunStart,
            ["Run"] = LOCO_ANIM_STATE_CONST.Run,
            ["RunEnd"] = LOCO_ANIM_STATE_CONST.RunEnd,
            ["MoveTurn"] = LOCO_ANIM_STATE_CONST.MoveTurn,
            ["JumpStart"] = LOCO_ANIM_STATE_CONST.JumpStart,
            ["JumpLoop"] = LOCO_ANIM_STATE_CONST.JumpLoop,
            ["JumpEndLow"] = LOCO_ANIM_STATE_CONST.JumpEndLow,
            ["JumpEndHigh"] = LOCO_ANIM_STATE_CONST.JumpEndHigh,
            ["JumpEndRestartLow"] = LOCO_ANIM_STATE_CONST.JumpEndRestartLow,
            ["JumpEndRestartHigh"] = LOCO_ANIM_STATE_CONST.JumpEndRestartHigh,
            ["GlideStart"] = LOCO_ANIM_STATE_CONST.GlideStart,
            ["GlideLoop"] = LOCO_ANIM_STATE_CONST.GlideLoop,
            ["RidingIdle"] = LOCO_ANIM_STATE_CONST.RidingIdle,
            ["RidingStart"] = LOCO_ANIM_STATE_CONST.RidingStart,
            ["RidingLoop"] = LOCO_ANIM_STATE_CONST.RidingLoop,
            ["RidingEnd"] = LOCO_ANIM_STATE_CONST.RidingEnd,
            ["RidingMoveTurn"] = LOCO_ANIM_STATE_CONST.RidingMoveTurn,
			["RidingMoveTurnEnd"] = LOCO_ANIM_STATE_CONST.RidingMoveTurnEnd,
			["Riding90LeftMoveTurn"] = LOCO_ANIM_STATE_CONST.Riding90LeftMoveTurn,
			["Riding90RightMoveTurn"] = LOCO_ANIM_STATE_CONST.Riding90RightMoveTurn,
            ["MultiJumpEndRestart"] = LOCO_ANIM_STATE_CONST.MultiJumpEndRestart,
            ["RidingDash"] = LOCO_ANIM_STATE_CONST.RidingDash,
            ["WaterGetIn"] = LOCO_ANIM_STATE_CONST.WaterGetIn,
            ["WaterGetOut"] = LOCO_ANIM_STATE_CONST.WaterGetOut,
            ["RidingJumpStart"] = LOCO_ANIM_STATE_CONST.RidingJumpStart,
			["RidingJumpStartSecond"] = LOCO_ANIM_STATE_CONST.RidingJumpStartSecond,
            ["RidingJumpLoop"] = LOCO_ANIM_STATE_CONST.RidingJumpLoop,
            ["RidingJumpEnd"] = LOCO_ANIM_STATE_CONST.RidingJumpEnd,
            ["RidingJumpEndRestart"] = LOCO_ANIM_STATE_CONST.RidingJumpEndRestart,
            ["DizzinessStart"] = LOCO_ANIM_STATE_CONST.DizzinessStart,
            ["DizzinessIdle"] = LOCO_ANIM_STATE_CONST.DizzinessIdle,
            ["DizzinessEnd"] = LOCO_ANIM_STATE_CONST.DizzinessEnd,
            ["DizzinessWalk"] = LOCO_ANIM_STATE_CONST.DizzinessWalk,
            ["WaterIdle"] = LOCO_ANIM_STATE_CONST.WaterIdle,
            ["WaterRunStart"] = LOCO_ANIM_STATE_CONST.WaterRunStart,
            ["WaterRun"] = LOCO_ANIM_STATE_CONST.WaterRun,
            ["WaterRunEnd"] = LOCO_ANIM_STATE_CONST.WaterRunEnd,
            ["WaterMoveTurn"] = LOCO_ANIM_STATE_CONST.WaterMoveTurn,
            ["WaterJumpStart"] = LOCO_ANIM_STATE_CONST.WaterJumpStart,
            ["WaterJumpLoop"] = LOCO_ANIM_STATE_CONST.WaterJumpLoop,
            ["WaterJumpEnd"] = LOCO_ANIM_STATE_CONST.WaterJumpEnd,
            ["WaterJumpEndRestart"] = LOCO_ANIM_STATE_CONST.WaterJumpEndRestart,
            ["WaterJumpStartSecond"] = LOCO_ANIM_STATE_CONST.WaterJumpStartSecond,
            ["WaterJumpStartThird"] = LOCO_ANIM_STATE_CONST.WaterJumpStartThird,
            ["WaterJumpLoopThird"] = LOCO_ANIM_STATE_CONST.WaterJumpLoopThird,
            ["WaterJumpEndThird"] = LOCO_ANIM_STATE_CONST.WaterJumpEndThird,
            ["WaterDash"] = LOCO_ANIM_STATE_CONST.WaterDash,
            ["JumpStartSecond"] = LOCO_ANIM_STATE_CONST.JumpStartSecond,
            ["JumpStartThird"] = LOCO_ANIM_STATE_CONST.JumpStartThird,
            ["JumpLoopThird"] = LOCO_ANIM_STATE_CONST.JumpLoopThird,
            ["JumpEndThird"] = LOCO_ANIM_STATE_CONST.JumpEndThird,
			["MountPassengerIdle"] = LOCO_ANIM_STATE_CONST.MountPassengerIdle,
			["MountPassengerDash"] = LOCO_ANIM_STATE_CONST.MountPassengerDash,
			["MountPassengerMoveTurn"] = LOCO_ANIM_STATE_CONST.MountPassengerMoveTurn,
			["MountPassengerJumpStart"] = LOCO_ANIM_STATE_CONST.MountPassengerJumpStart,
			["MountPassengerJumpEnd"] = LOCO_ANIM_STATE_CONST.MountPassengerJumpEnd,
			["CameraDirMoveStart"] = LOCO_ANIM_STATE_CONST.CameraDirMoveStart,
			["CameraDirMoveLoop"] = LOCO_ANIM_STATE_CONST.CameraDirMoveLoop,
			["CameraDirMoveEnd"] = LOCO_ANIM_STATE_CONST.CameraDirMoveEnd,
            ["Dash"] = LOCO_ANIM_STATE_CONST.Dash,
			["DashRestart"] = LOCO_ANIM_STATE_CONST.DashRestart,
            ["WaterJumpEndRestartThird"] = LOCO_ANIM_STATE_CONST.WaterJumpEndRestartThird,
            ["BackUpState1"] = LOCO_ANIM_STATE_CONST.BackUpState1,
            ["BackUpState2"] = LOCO_ANIM_STATE_CONST.BackUpState2,
            ["BackUpState3"] = LOCO_ANIM_STATE_CONST.BackUpState3,
            ["BackUpState4"] = LOCO_ANIM_STATE_CONST.BackUpState4,
            ["BackUpState5"] = LOCO_ANIM_STATE_CONST.BackUpState5,
            ["ClimbIdle"] = LOCO_ANIM_STATE_CONST.ClimbIdle,
            ["ClimbMove"] = LOCO_ANIM_STATE_CONST.ClimbMove,
            ["SpiritualDash"] = LOCO_ANIM_STATE_CONST.SpiritualDash,
            ["SpiritualJump"] = LOCO_ANIM_STATE_CONST.SpiritualJump,
            ["SpiritualDown"] = LOCO_ANIM_STATE_CONST.SpiritualDown,            
        }

--================================Cloth和Kawaii==================================================
ClothingToKawaiiMap = ClothingToKawaiiMap or
	{
		["Clothing_Skirt"] = "bEnableSkirt",
		["Clothing_Sleeve"] = "bEnableSleeve",
		["Clothing_Cloak"] = "bEnableCloak",
	}

-- -------------------------------感知部分-----------------------------------------------
IN_WATER_DEPTH_CM = 5
WATER_DETECT_DISTANCE_CM = 300
PERCEPTION_DETECT_Z_OFFSET_CM = 300
PERCEPTION_DETECT_DISTANCE_TOLERATE_CM = 5

DYNAMIC_WATER_WAVE_TIME_GAP_FOR_LOCO = 0.1

LOCAL_WIND_FIELD_TIME_GAP_LOCO = 0.1

if not IS_SERVER then
    LOCO_JUMP_Z = Game.TableData.GetLocoControlConstDataRow("DEFAULT_LOCO_JUMP_Z")
    LOCO_GRAVITY_SCALE = Game.TableData.GetLocoControlConstDataRow("DEFAULT_LOCO_GRAVITY_SCALE")
    LOCO_HIGH_JUMP_END_Z_VELOCITY = Game.TableData.GetLocoControlConstDataRow("DEFAULT_LOCO_HIGH_JUMP_END_Z_VELOCITY")

    MAX_PLAY_RATIO_BY_SPEED = Game.TableData.GetLocoControlConstDataRow("MAX_PLAY_RATIO_BY_SPEED")
    MIN_PLAY_RATIO_BY_SPEED = Game.TableData.GetLocoControlConstDataRow("MIN_PLAY_RATIO_BY_SPEED")

    GRAVITY_VELOCITYZ_MAX = Game.TableData.GetLocoControlConstDataRow("GRAVITY_VELOCITYZ_MAX")  -- 重力速度上限
    ENTER_FALLING_VELOCITYZ = Game.TableData.GetLocoControlConstDataRow("DEFAULT_ENTER_FALLING_VELOCITYZ") -- 允许从地面进入Falling的Z轴速度大小
end

LOCO_DRIVE_ROLE_MESH_Z_OFFSET = -2.0 -- 用于匹配本地角色移动时，胶囊体的保底离地距离
LOCO_DRIVE_ROLE_CAPSULE_Z_OFFSET = 2.0 -- 用于本地角色贴地时, RootComponent的偏移高度

-- 标记跳跃和滑翔状态，会传递到C++
JUMP_LOCOSTATES = JUMP_LOCOSTATES or {
    LOCO_ANIM_STATE_CONST.JumpStart,
    LOCO_ANIM_STATE_CONST.JumpLoop,
    LOCO_ANIM_STATE_CONST.JumpStartSecond,
    LOCO_ANIM_STATE_CONST.JumpStartThird,
    LOCO_ANIM_STATE_CONST.JumpLoopThird
}
GLIDE_LOCOSTATES = GLIDE_LOCOSTATES or {
    LOCO_ANIM_STATE_CONST.GlideStart,
    LOCO_ANIM_STATE_CONST.GlideLoop
}
WATERWAVE_LOCOSTATES = WATERWAVE_LOCOSTATES or {
    LOCO_ANIM_STATE_CONST.Idle,
    LOCO_ANIM_STATE_CONST.RunStart,
    LOCO_ANIM_STATE_CONST.Run,
    LOCO_ANIM_STATE_CONST.RunEnd,
    LOCO_ANIM_STATE_CONST.MoveTurn,
    LOCO_ANIM_STATE_CONST.RidingIdle,
    LOCO_ANIM_STATE_CONST.RidingStart,
    LOCO_ANIM_STATE_CONST.RidingLoop,
    LOCO_ANIM_STATE_CONST.RidingEnd,
    LOCO_ANIM_STATE_CONST.RidingMoveTurn,
	LOCO_ANIM_STATE_CONST.RidingMoveTurnEnd,
	LOCO_ANIM_STATE_CONST.Riding90LeftMoveTurn,
	LOCO_ANIM_STATE_CONST.Riding90RightMoveTurn,
	LOCO_ANIM_STATE_CONST.WaterIdle,
	LOCO_ANIM_STATE_CONST.WaterRunStart,
	LOCO_ANIM_STATE_CONST.WaterRun,
	LOCO_ANIM_STATE_CONST.WaterRunEnd,
	LOCO_ANIM_STATE_CONST.WaterMoveTurn,
	LOCO_ANIM_STATE_CONST.WaterJumpEnd,
	LOCO_ANIM_STATE_CONST.WaterJumpEndThird,
	LOCO_ANIM_STATE_CONST.WaterDash,
}

WATERMOVE_DISABLEONGROUND_LOCOSTATES = WATERMOVE_DISABLEONGROUND_LOCOSTATES or {
    LOCO_ANIM_STATE_CONST.WaterIdle,
    LOCO_ANIM_STATE_CONST.WaterMoveTurn,
    LOCO_ANIM_STATE_CONST.WaterJumpEnd,
    LOCO_ANIM_STATE_CONST.WaterJumpEndThird,
    LOCO_ANIM_STATE_CONST.WaterDash,
}

BODYLEAN_LOCOSTATES = BODYLEAN_LOCOSTATES or {
    LOCO_ANIM_STATE_CONST.RunStart,
    LOCO_ANIM_STATE_CONST.Run,
    LOCO_ANIM_STATE_CONST.RunEnd
}

RIDEMOUNT_PASSENGER_LOCOSTATE_REPLACE = RIDEMOUNT_PASSENGER_LOCOSTATE_REPLACE or {
	["RidingIdle"] = "MountPassengerIdle",
	["RidingDash"] = "MountPassengerDash",
	["RidingMoveTurn"] = "MountPassengerMoveTurn",
	["RidingJumpStart"] = "MountPassengerJumpStart",
	["RidingJumpEnd"] = "MountPassengerJumpEnd",
	
	["RidingStart"] = "MountPassengerIdle",
	["RidingLoop"] = "MountPassengerIdle",
	["RidingEnd"] = "MountPassengerIdle", 
	["RidingMoveTurnEnd"] = "MountPassengerIdle", 
	["Riding90LeftMoveTurn"] = "Riding90LeftMoveTurn", 
	["Riding90RightMoveTurn"] = "Riding90RightMoveTurn", 
	["RidingJumpLoop"] = "MountPassengerIdle",
	["RidingJumpEndRestart"] = "MountPassengerIdle",
	["RidingJumpStartSecond"] = "MountPassengerJumpStart",
}

DELAYED_ANIMMOVEPOSTURE_CACHETIME = 0.4
CLIENT_LOCAL_SPEED_INFO = {
    ["PredictSpeedByMovePosture"] = {
        Priority = 1000,
        Tag = "PredictSpeedByMovePosture"
    },

    ["PathFollow"] = {
        Priority = 2000,
        Tag = "PathFollow"
    },

	["SplineMove"] = {
		Priority = 3000,
		Tag = "SplineMove"
	},
}

DISABLE_GRAVITY_TAG = {
    PARABOLA_ROOT_MOTION = 1000,
    LEVEL_SEQUENCE = 1001,
    DIRECTOR_STATE = 1002,
    LOCAL_NPC = 1003,
    ABILITY_SKILL = 1004,
    CHARACTER = 1005,
	MOUNT = 1006,
    MOTIONWARP = 1007,
    HIT_FORCE_MOVE = 1008,
	SPLINE_MOVE = 1009,
    LOCO_CONTROL = 1010,
}

ENavJumpType = ENavJumpType or {
    NoJump = 0,
    JumpOnce = 1,
    JumpTwice = 2,
    TryJump = 3,
}

ENavJumpStage = ENavJumpStage or {
    FirstStage = 1,
    SecondStage = 2
}


LOCOMOTION_CONTROL_OVERRIDE_BY_MOUNT = LOCOMOTION_CONTROL_OVERRIDE_BY_MOUNT or {
	[LOCO_ANIM_STATE_CONST.RidingIdle] = true,
	[LOCO_ANIM_STATE_CONST.RidingStart] = true,
	[LOCO_ANIM_STATE_CONST.RidingLoop] = true,
	[LOCO_ANIM_STATE_CONST.RidingEnd] = true,
	[LOCO_ANIM_STATE_CONST.RidingMoveTurn] = true,
	[LOCO_ANIM_STATE_CONST.RidingMoveTurnEnd] = true,
	[LOCO_ANIM_STATE_CONST.Riding90LeftMoveTurn] = true,
	[LOCO_ANIM_STATE_CONST.Riding90RightMoveTurn] = true,
	[LOCO_ANIM_STATE_CONST.RidingDash] = true,
	[LOCO_ANIM_STATE_CONST.RidingJumpStart] = true,
	[LOCO_ANIM_STATE_CONST.RidingJumpStartSecond] = true,
	[LOCO_ANIM_STATE_CONST.RidingJumpLoop] = true,
	[LOCO_ANIM_STATE_CONST.RidingJumpEnd] = true,
	[LOCO_ANIM_STATE_CONST.RidingJumpEndRestart] = true,
	[LOCO_ANIM_STATE_CONST.RidingJumpStartSecond] = true,
}


-- ===============================捏脸、妆容========================================================
MAKEUP_DATA_KEY = MAKEUP_DATA_KEY or {
	MAKEUP_PARAM_DATA = 'MakeUpParamData',
	MAKEUP_RES_DATA = 'MakeUpResData',
}

-- 详见C++ EAvatarCaptureMaterialSlotTypeIndex
FACE_CAPTURE_MAT_COUNT = 5

-- Animation状态
AnimationStateType = {
    Idle = 0,   -- 空闲 
    FullBody = 1,   -- 全身
    UpperOnly = 2,  -- 仅上半身
    UpperAndLower = 3, -- 上下半身
}

-- 这里定义设置custom depth的业务类型, 本身定义的类型不要有重复即可
---@class CUSTOM_DEPTH_LOGIC_TYPE
CUSTOM_DEPTH_LOGIC_TYPE = CUSTOM_DEPTH_LOGIC_TYPE or {
    Stealth = 0,
    FlowChart = 1,
    FlowChart_ClipPP = 2,
    FlowChart_PhantomPP = 3,
    OldPP = 4, -- 老的后处理管理器来源, 后续清理
    FlowChart_SketchPP = 5,
	SceneDisplay = 6,         --展示场景角色使用       
    PPEffectSkill = 7, -- 后处理技能, 先统一用同一个, 后续有需要可以开放配置
}
-- 这里定义设置CustomDepth业务对应的优先级, 优先级可以相等, 相等的情况下按照后来者优先的规则处理
---@class CUSTOM_DEPTH_PRIORITY
CUSTOM_DEPTH_PRIORITY = CUSTOM_DEPTH_PRIORITY or {
    Stealth = 0,
    FlowChart = 1,
    FlowChart_ClipPP = 1,
    FlowChart_PhantomPP = 1,
    OldPP = 1,
    FlowChart_SketchPP = 1,
	SceneDisplay = 1,         --展示场景角色使用       
    PPEffectSkill = 1
}

CUSTOM_DEPTH_LOGIC_TYPE_TO_PRIORITY = {}
for k, v in pairs(CUSTOM_DEPTH_LOGIC_TYPE) do
    CUSTOM_DEPTH_LOGIC_TYPE_TO_PRIORITY[v] = CUSTOM_DEPTH_PRIORITY[k]
end 

