--- 红包相关定义
--- Created by shangyuzhong.
--- DateTime: 2024/10/10 15:29
---
local Enum = Enum
local itemConst = kg_require("Shared.ItemConst")

-- 大类, 1=绑金礼金，2=道具礼金
RED_PACKET_CLASS = {
    MONEY = 1,
    ITEM = 2,
}

-- 小类, 1=拼手气礼金，2=密文礼金，3=密码礼金
RED_PACKET_TYPE = {
    RANDOM = 1,
    SECRET_WORD = 2,
    PASSWORD = 3,
}

-- 日志类型, 1=发红包，2=红包过期
RED_PACKET_LOG_TYPE = {
    SEND_LOG = 1,       -- 发红包
    EXPIRE_LOG = 2,   -- 红包过期
}

-- 记录类型, 1=发红包, 2=领取红包
RED_PACKET_RECORD_TYPE = {
    SEND = 1,
    RECEIVE = 2,
}

RED_PACKET_SOURCE_TYPE = {
    GUILD_DANCE = 1,    -- 公会舞会
}


RED_PACKET_MONEY_TYPE = itemConst.ITEM_SPECIAL_MONEY_COIN       -- 红包货币类型:金镑
RED_PACKET_MONEY_RETURN_TYPE = itemConst.ITEM_SPECIAL_MONEY_COIN_BOUND       -- 红包货币返还类型:绑定金镑
RED_PACKET_ITEM_RETURN_TYPE = itemConst.ITEM_SPECIAL_MONEY_COIN       -- 红包道具返还类型:金镑

-- 红包频道额外处理
RED_PACKET_CHANNEL_EXTRA_HANDLE = {
    [Enum.EChatChannelData.WORLD] = "redPacketHandle_World",
    [Enum.EChatChannelData.GUILD] = "redPacketHandle_Guild",
    [Enum.EChatChannelData.TEAM] = "redPacketHandle_Team",
    [Enum.EChatChannelData.GROUP] = "redPacketHandle_Group",
    [Enum.EChatChannelData.CHATROOM] = "redPacketHandle_ChatRoom",
}

-- 红包大类检查
RED_PACKET_CLASS_CHECK_FUNC_NAME = {
    [RED_PACKET_CLASS.MONEY] = "redPacketClassCheck_Money",
    [RED_PACKET_CLASS.ITEM] = "redPacketClassCheck_Item",
}

-- 红包小类检查
RED_PACKET_TYPE_CHECK_FUNC_NAME = {
    [RED_PACKET_TYPE.RANDOM] = "redPacketTypeCheck_Random",
    [RED_PACKET_TYPE.SECRET_WORD] = "redPacketTypeCheck_SecretWord",
    [RED_PACKET_TYPE.PASSWORD] = "redPacketTypeCheck_Password",
}

-- 0-金钱，1-外观，2-带密码，3-带语音
-- 优先级2,3 高于0,1
CLASS_TO_VARIETY = {
    [RED_PACKET_CLASS.MONEY] = 0,
    [RED_PACKET_CLASS.ITEM] = 1,
}

TYPE_TO_VARIETY = {
    [RED_PACKET_TYPE.PASSWORD] = 2,
    [RED_PACKET_TYPE.SECRET_WORD] = 3,
}

-- 可加入礼金的道具相关
-- NpcShop表shopid=2300007，item子表tokenid使用货币为2001003，限购次数totallimits为-1，时间在定时上架区间范围内
RED_PACKET_GOODS_SHOP_ID = 2390107
RED_PACKET_GOODS_SHOP_TOKEN_ID = 2001003
RED_PACKET_GOODS_TOTAL_LIMITS = -1

GLOBAL_CACHE_VALID_SECS = 3     -- 全局缓存有效时间

-- 客户端专用const
if not IS_SERVER then
    local StringConst = kg_require("Data.Config.StringConst.StringConst")
    local UIComSimpleTabList = kg_require("Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList")

    RED_PACKET_TAB_DATA = {
        UIComSimpleTabList.NewTabData(StringConst.Get("RED_PACKET_TAB_1")),
        UIComSimpleTabList.NewTabData(StringConst.Get("RED_PACKET_TAB_2")),
        UIComSimpleTabList.NewTabData(StringConst.Get("RED_PACKET_TAB_3")),
    }

    RED_PACKET_SEND_LIST = {
        [RED_PACKET_CLASS.MONEY] = {bIsSendType = true, Name = StringConst.Get("RED_PACKET_TYPE_1"), Icon = Enum.EArtAssetIconData.REDPACKET_MONEY, Class = RED_PACKET_CLASS.MONEY},
        [RED_PACKET_CLASS.ITEM] = {bIsSendType = true, Name = StringConst.Get("RED_PACKET_TYPE_2"), Icon = Enum.EArtAssetIconData.REDPACKET_CLOTHES, Class = RED_PACKET_CLASS.ITEM},
    }

    RED_PACKET_CHANNEL_TO_TEXT = {
        [Enum.EChatChannelData.WORLD] = StringConst.Get("RED_CHANNEL_WORLD"),
        [Enum.EChatChannelData.TEAM] = StringConst.Get("RED_CHANNEL_TEAM"),
        [Enum.EChatChannelData.GROUP] = StringConst.Get("RED_CHANNEL_TEAM"),
        [Enum.EChatChannelData.GUILD] = StringConst.Get("RED_CHANNEL_GUILD"),
        [Enum.EChatChannelData.CHATROOM] = StringConst.Get("SOCIAL_CHATROOM_TYPETEXT"),
    }

    RED_PACKET_TAB_INDEX_TO_CELL_ID = {
        [1] = "P_SendRedPacket",
        [2] = "P_RPHistory",
        [3] = "P_AllCurRP",
    }

    LIST_RIGHT_ITEM_TYPE = {
        GIVE_CHANNEL = 1,
        RED_PACKET_TYPE = 2,
        CHANNEL_REDPACKET_LIST = 3,
    }
end