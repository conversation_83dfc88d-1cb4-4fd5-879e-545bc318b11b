
-- 限次的规则编号
LIMIT_RULE_NONE = 0                 -- 不限次
LIMIT_RULE_FOREVER_ONE = 1          -- 永久1次的限次规则编号, 如每个成就/任务的奖励只能领取1次

-- 刷新类型
LIMIT_REFRESH_NONE = "NEVER"        -- 不刷新
LIMIT_REFRESH_DAY = "DAY"           -- 每天刷新
LIMIT_REFRESH_WEEK = "WEEK"         -- 每周刷新
LIMIT_REFRESH_MONTH = "MONTH"       -- 每月刷新

CALC_CUMULATIVE_REFRESH = {
    [LIMIT_REFRESH_DAY] = { "GetCumulativeRefreshDayCount", { 1, 5, 0, 0 } },       -- 每日刷新
    [LIMIT_REFRESH_WEEK] = { "GetCumulativeRefreshWeekCount", { 2, 1, 5, 0, 0 } },  -- 每周刷新
    [LIMIT_REFRESH_MONTH] = { "GetCumulativeRefreshMonthCount", { 3, 1, 5, 0, 0 } } -- 每月刷新
}

-- 限次字段的类型
LIMIT_TIMES_CONST_SHEET = 1         -- 限次的字段在常量表控制
LIMIT_TIMES_NEW_FIELD = 2           -- 限次的字段由新的独立字段控制（即用配置表新的一列来控制限次）
LIMIT_TIMES_DUNGEON = 3             -- 副本的限次(初始次数不共享，周期新增次数共享，共享的次数有优先级，优先消耗相同优先级次数，之后可消耗更高优先级次数，不可消耗更低优先级次数)


-- {
--      [limitNo(限次规则编号, 顺序递增, 不能重复，不能修改)] = {
--              FuncName = xx, (TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）),
--              ClearType = xx, (清除累计次数类型: 不清除, 每日清除, 每周清除, 每月清除)，用于支持类似每日累加, 每周清除累计次数的需求
--              IsShare = xx, (限次是否共享, 即多行的同一列的奖励的次数是否共享, true为共享, 即发放任意一行这列的奖励, 所有行的这列的限次都加1, false为不共享)
--              LimitRule = xx， (限制次数的规则，格式为：刷新类型, 初始次数, 每个周期的新增次数, 累计次数上限，不能和LimitFieldName同时存在)
--              LimitFieldName = xx， (限次的配置表字段名,不能和LimitRule同时存在）
--              LimitType = xx (限次字段的类型)
--              
--      }
-- }

LIMIT_CONF = {
    [LIMIT_RULE_FOREVER_ONE] = {                                -- 限次规则编号, 不能重复, 不能修改
        FuncName = "",                                          -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = false,                                        -- 限次是否共享
        LimitRule = {                                           -- 限次的规则
            LIMIT_REFRESH_NONE,                                 -- 刷新类型
            1,                                                  -- 初始的次数
            0,                                                  -- 每个周期的新增次数
            1,                                                  -- 累计次数上限
        },
    },

    [2] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetWorldBossSettingDataRow",                -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = true,                                         -- 限次是否共享
        LimitFieldName = "WORLD_BOSS_PERSONAL_REWARD_TIME",     -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [3] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetWorldBossSettingDataRow",                -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = true,                                         -- 限次是否共享
        LimitFieldName = "WORLD_BOSS_TEAM_REWARD1_TIME",        -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [4] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetWorldBossSettingDataRow",                -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = true,                                         -- 限次是否共享
        LimitFieldName = "WORLD_BOSS_TEAM_REWARD2_TIME",        -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [5] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetGuildLeagueConstDataRow",                -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = true,                                         -- 限次是否共享
        LimitFieldName = "GUILD_LEAGUE_REWARD_TIME",            -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [6] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetChampionSettingDataRow",                 -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = true,                                         -- 限次是否共享
        LimitFieldName = "CHAMPION_REWARD_TIME",                -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [7] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetChatConstDataRow",                       -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = false,                                        -- 限次是否共享
        LimitFieldName = "ANON_DROP_TIMES_LIMIT",               -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [8] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetScheduleConstDataRow",                   -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = false,                                        -- 限次是否共享
        LimitFieldName = "SCHEDULE_REWARD_TIME",                -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [9] = {                                                     -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetTaskChapterRewardDataRow",               -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = false,                                        -- 限次是否共享
        LimitFieldName = "RewardLimit",                         -- 限制次数的字段名
        LimitType = LIMIT_TIMES_NEW_FIELD,                      -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [10] = {                                                    -- 限次规则编号, 不能重复, 不能修改
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        LimitType = LIMIT_TIMES_DUNGEON,                        -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },

    [11] = {                                                    -- 限次规则编号, 不能重复, 不能修改
        FuncName = "GetChampionSettingDataRow",                 -- TableData的读取限次的次数的配置表函数名, 只支持GetXXRow这种读表方式, 不支持其他读表方式（如GetXXTable）
        ClearType = LIMIT_REFRESH_NONE,                         -- 清除累计次数类型
        IsShare = true,                                         -- 限次是否共享
        LimitFieldName = "CHAMPION_ELIMINATION_REWARD_TIME",    -- 限制次数的字段名
        LimitType = LIMIT_TIMES_CONST_SHEET,                    -- 限次字段的类型(配置表新的字段单独控制限次，还是常量控制限次，或其他特殊类型)
    },
}
