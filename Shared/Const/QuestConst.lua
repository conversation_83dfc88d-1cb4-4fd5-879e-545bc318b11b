--- 任务相关定义

QUEST_TARGET_MAX_NUMBER = 10  -- 单个任务最大的任务目标上限, 上线后不能修改该值, 会影响已完成目标的压缩算法

QUEST_SUB_TARGET_MAX_NUMBER = QUEST_TARGET_MAX_NUMBER - 1  -- 单个任务最大的子任务目标上限

QUEST_MAX_NUMBER = 100  -- 玩家身上接取的任务最大上限

QUEST_ID_MOD = 100  -- 任务ID后缀




-- 任务领取类型
QuestReceiveConst = {
    ['RECEIVE_TYPE__AUTO'] = 0,                                             -- 自动领取任务
    ['RECEIVE_TYPE__NPC_TALK'] = 1,                                         -- 和场景NPC完成对话
    ['RECEIVE_TYPE__MANUAL'] = 2,                                           -- 手动领取
    ['RECEIVE_TYPE__ITEM'] = 3,                                             -- 道具领取
    ['RECEIVE_TYPE__QUEST_JUMP'] = 4,                                       -- 任务跳转领取
    ['RECEIVE_TYPE__ENTER_SPACE'] = 5,                                      -- 进入场景领取
}


-- 任务失败Action类型
QuestFailActionConst = {
    ['FAIL_ACTION__RESET_QUEST_AUTO'] = 0,                                  -- 指定时长后自动回退到某个任务
    ['FAIL_ACTION__RESET_QUEST_MANUAL'] = 1,                                -- 指定时长后手动回退到某个任务
    ['FAIL_ACTION__FINISH_QUEST_AUTO'] = 2,                                 -- 指定时长后自动完成当前失败任务
    ['FAIL_ACTION__FINISH_QUEST_MANUAL'] = 3,                               -- 指定时长后手动完成当前失败任务
}


-- 任务完成类型
QuestFinishTypeConst = {
    ['FINISH_TYPE__AUTO'] = 0,                                              -- 自动提交任务 
    ['FINISH_TYPE__NPC_TALK'] = 1,                                          -- 和NPC完成指定对话
    ['FINISH_TYPE__MANUAL'] = 2,                                            -- UI界面上交任务
}


-- 任务失败类型
QuestFailTypeConst = {
    ['QUEST_FAIL_TYPE__PLAYER_DEAD'] = 501,                                 -- 玩家死亡    
    ['QUEST_FAIL_TYPE__NPC_DEAD'] = 502,                                    -- NPC死亡
    ['QUEST_FAIL_TYPE__ALL_PLAYER_DEACTIVE'] = 503,                         -- 所有玩家死亡或离开副本
    ['QUEST_FAIL_TYPE__QUEST_TIMEOUT'] = 504,                               -- 任务超时
    ['QUEST_FAIL_TYPE__PLAYER_LEAVE'] = 505,                                -- 玩家离开当前场景或位面
    ['QUEST_FAIL_TYPE__QTE_FAILED'] = 506,                                  -- qte失败
}



-- 主线任务类型
TaskMainTypeConst = {
    ['TASK_MINITYPE__MAIN'] = 0,                                            -- 主线任务    
    ['TASK_MINITYPE__SUBTASK'] = 1,                                         -- 支线任务
    ['TASK_MINITYPE__ROLE_PLAY'] = 2,                                       -- 城市收集
    ['TASK_MINITYPE__DUNGEON'] = 3,                                         -- 副本任务
    ['TASK_MINITYPE__REPEAT'] = 4,                                          -- 日常任务
    ['TASK_MINITYPE__GUILDSINGLE'] = 5,                                     -- 工会个人任务
    ['TASK_MINITYPE__ROLEPLAYSHERIFFTASK'] = 6,                             -- 治安官NPC悬赏任务
    ['TASK_MINITYPE__SYSTEM_TASK'] = 7,                                     -- 系统玩法相关任务
}



-- 任务状态
TaskStatusConst = {
    ['TASK_STATUS__AVAILABLE'] = 1,                                         -- 任务可接取
    ['TASK_STATUS__ACCEPTED'] = 2,                                          -- 任务已接取
    ['TASK_STATUS__ACCOMPLISHED'] = 3,                                      -- 任务完成
    ['TASK_STATUS__FAILED'] = 4,                                            -- 任务失败
    ['TASK_STATUS__ABANDONED'] = 5,                                         -- 任务放弃
    ['TASK_STATUS__FINISHED'] = 6,                                          -- 任务结束
    ['TASK_STATUS__FINISH_ACTION'] = 7,                                     -- 任务结束流程
}


-- 任务失败类型（旧）
TaskFailTypeCost = {
    ['TASK_FAIL_TYPE__PLAYER_DEAD'] = 1, 
    ['TASK_FAIL_TYPE__NPC_DEAD'] = 2, 
    ['TASK_FAIL_TYPE__ALL_PLAYER_DEACTIVE'] = 3, 
    ['TASK_FAIL_TYPE__QUEST_TIMEOUT'] = 4, 
    ['TASK_FAIL_TYPE__PLAYER_LEAVE'] = 5, 
    ['TASK_FAIL_TYPE__QTE_FAILED'] = 6, 
}



-- 进入位面的类型
QuestPlaneEnterTypeConst = {
    ['PLANE_ENTER_TYPE__AUTO'] = 1,                                         -- 直接原地进入
    ['PLANE_ENTER_TYPE__INTERACTOR'] = 2,                                   -- 交互物

}



-- 离开位面的坐标类型
QuestPlaneExitTypeConst = {
    ['PLANE_EXIT_TYPE__AUTO'] = 0,                                          -- 任务完成自动退出
    ['PLANE_EXIT_TYPE__TREIGGER'] = 1,                                      -- 传送到指定trigger处
    ['PLANE_EXIT_TYPE__POSITION'] = 2,                                      -- 传送到指定坐标处
    ['PLANE_EXIT_TYPE__ENTER_PLANE_POS'] = 3,                               -- 传送到当前位面入口处
}



-- 任务重复类型
TaskRepeatType = {
    ['TASK_TYPE__UNREPEATABLE'] = 0,                                        -- 一次性任务
    ['TASK_TYPE__REPEATABLE'] = 1,                                          -- 日常任务 
    ['TASK_TYPE__REPEATABLE_WEEK'] = 2,                                     -- 周常任务
}


-- 
QuestTriggerOnType = {
    ['TRIGGER_ON_TYPE__TASK_GIVEN_UP'] = 4,                                 -- 环放弃后                          
    ['TRIGGER_ON_TYPE__RING_REVEIVED'] = 7,                                 -- 环开始后
    ['TRIGGER_ON_TYPE__RING_COMPLETED'] = 8,                                -- 环完成后
    ['TRIGGER_ON_TYPE__TASK_RECEIVED'] = 1,                                 -- 任务领取后
    ['TRIGGER_ON_TYPE__TASK_COMPLETED'] = 2,                                -- 完成任务后
    ['TRIGGER_ON_TYPE__TASK_FINISHED'] = 3,                                 -- 任务结束后
    ['TRIGGER_ON_TYPE__TASK_FAILED'] = 5,                                   -- 任务失败后
    ['TRIGGER_ON_TYPE__TASK_TARGET_COMPLETED'] = 6,                         -- 单个目标完成后
}


-- 任务Action常量
QuestTriggerConst = {
    ['TRIGGER_EVENT_TYPE__PLAY_DIALOGUE'] = 9,                              -- 播放剧情编辑器对话
    ['TRIGGER_EVENT_TYPE__ADD_BUFF_BY_INSTANCEID'] = 132,                   -- 指定对象添加Buff
    ['TRIGGER_EVENT_TYPE__ANIMATION_STOP'] = 164,                           -- 停止播放动作
    ['TRIGGER_EVENT_TYPE__CAMERA_END'] = 137,                               -- 简单镜头移动结束
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKAT_INS'] = 139,                        -- 玩家视角转向指定对象
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKAT_POS'] = 138,                        -- 玩家视角转向指定坐标
    ['TRIGGER_EVENT_TYPE__CAMERA_MOVE'] = 136,                              -- 简单镜头移动开始
    ['TRIGGER_EVENT_TYPE__CANE_SKILL_TO_INSTANCEID'] = 181,                 -- 施放卜杖寻路技能寻找指定InstanceID对象
    ['TRIGGER_EVENT_TYPE__CANE_SKILL_TO_POSITION'] = 182,                   -- 施放卜杖寻路技能寻找指定坐标位置
    ['TRIGGER_EVENT_TYPE__CHANGE_CLIMATE'] = 168,                           -- 修改当前天气
    ['TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE_BY_INSTANCEID'] = 126,       -- 通过InstanceID调整采集物状态
    ['TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE_BY_TEMPLATEID'] = 125,       -- 通过TemplateID调整采集物状态
    ['TRIGGER_EVENT_TYPE__CHANGE_INTERACTOR_STATE'] = 127,                  -- 改变交互物的状态
    ['TRIGGER_EVENT_TYPE__CLEAR_QUEST_MARK_DEFINE'] = 146,                  -- 改变交互物的状态
    ['TRIGGER_EVENT_TYPE__CLOSE_LETTER'] = 119,                             -- 关闭书信
    ['TRIGGER_EVENT_TYPE__CLOSE_SPIRITUAL_VISION'] = 143,                   -- 关闭灵视
    ['TRIGGER_EVENT_TYPE__CREATE_BY_CLUSTER'] = 151,                        -- 生成指定集团
    ['TRIGGER_EVENT_TYPE__CREATE_BY_INSTANCEID'] = 149,                     -- 生成指定对象
    ['TRIGGER_EVENT_TYPE__DELETE_CAMERA_POST_PROCESS_EFFECT'] = 163,        -- 移除指定玩家当前播放的镜头后效
    ['TRIGGER_EVENT_TYPE__DELETE_ENTITY_EFFECT'] = 161,                     -- 移除指定对象身上的指定角色特效
    ['TRIGGER_EVENT_TYPE__DELETE_SCENE_EFFECT'] = 159,                      -- 移除指定场景特效
    ['TRIGGER_EVENT_TYPE__DESTROY_BY_CLUSTER'] = 152,                       -- 销毁指定集团
    ['TRIGGER_EVENT_TYPE__DESTROY_BY_INSTANCEID'] = 150,                    -- 销毁指定对象
    ['TRIGGER_EVENT_TYPE__ENTER_PLANE'] = 179,                              -- 进入位面
    ['TRIGGER_EVENT_TYPE__ENTER_PLANE_POS'] = 141,                          -- 进入位面指定坐标处
    ['TRIGGER_EVENT_TYPE__FLOWCHART_ACTION'] = 170,                         -- 调用流程图Action
    ['TRIGGER_EVENT_TYPE__FOG_CONTROL'] = 140,                              -- 可交互迷雾控制
    ['TRIGGER_EVENT_TYPE__GAZE_TO_BACK'] = 131,                             -- 恢复看向
    ['TRIGGER_EVENT_TYPE__GAZE_TO_LOC'] = 130,                              -- 看向坐标
    ['TRIGGER_EVENT_TYPE__GAZE_TO_SPAWNER'] = 129,                          -- 看向spawner
    ['TRIGGER_EVENT_TYPE__KILL_INSTANCEID'] = 147,                          -- 秒杀指定目标（InstanceID）
    ['TRIGGER_EVENT_TYPE__KILL_TEMPLATEID'] = 148,                          -- 秒杀指定目标（TemplateID）
    ['TRIGGER_EVENT_TYPE__MONSTER_TO_NPC'] = 157,                           -- MONSTER变回NPC
    ['TRIGGER_EVENT_TYPE__NPC_TOMONSTER'] = 156,                            -- 切换NPC的阵营
    ['TRIGGER_EVENT_TYPE__OPEN_CROSS_WORD_PUZZLE'] = 112,                   -- 开启文字填空解密
    ['TRIGGER_EVENT_TYPE__OPEN_JIGSAW_PUZZLE'] = 114,                       -- 开启拼图解密
    ['TRIGGER_EVENT_TYPE__OPEN_LETTER'] = 113,                              -- 打开信纸
    ['TRIGGER_EVENT_TYPE__PLAYER_TRANSMIT_OTHER_SCENE_POS'] = 104,          -- 玩家跨场景传送到指定坐标
    ['TRIGGER_EVENT_TYPE__PLAYER_TRANSMIT_OTHER_SCENE_TRRIGER'] = 106,      -- 玩家跨场景传送到指定Trigger
    ['TRIGGER_EVENT_TYPE__PLAY_2DAUDIO'] = 115,                             -- 播放2D音效
    ['TRIGGER_EVENT_TYPE__PLAY_3DAUDIO'] = 117,                             -- 播放3D音效
    ['TRIGGER_EVENT_TYPE__PLAY_ANIMATION_BY_INSTANCEID'] = 101,             -- 指定对象播动作
    ['TRIGGER_EVENT_TYPE__PLAY_ASIDE_TALK'] = 103,                          -- 播放画外音
    ['TRIGGER_EVENT_TYPE__PLAY_BLACK_BG'] = 102,                            -- 播放黑屏字幕
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTER_END'] = 109,                         -- 播放特殊任务结束展示界面
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTER_START'] = 108,                       -- 播放特殊任务开始展示界面
    ['TRIGGER_EVENT_TYPE__PLAY_END_QUEST'] = 111,                           -- 播放主线任务结束展示界面（卡牌）
    ['TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG'] = 171,                     -- 播放无镜对话
    ['TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_NPC'] = 172,              -- 播放无镜对话在NPC位置
    ['TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_POSITION'] = 173,         -- 播放无镜对话在坐标位置
    ['TRIGGER_EVENT_TYPE__PLAY_START_QUEST'] = 110,                         -- 播放主线任务开始展示界面（卡牌）
    ['TRIGGER_EVENT_TYPE__REMOVE_BUFF_BY_INSTANCEID'] = 133,                -- 移除指定对象身上BUFF
    ['TRIGGER_EVENT_TYPE__REMOVE_QUEST_ITEM'] = 166,                        -- 移除任务道具
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_ANGLE'] = 153,                          -- 转向角度
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_INSTANCE'] = 154,                       -- 转向对象
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_POS'] = 155,                            -- 转向坐标
    ['TRIGGER_EVENT_TYPE__SAY'] = 165,                                      -- 对象播放指定对白内容
    ['TRIGGER_EVENT_TYPE__SEND_FASHION'] = 178,                             -- 发放时装
    ['TRIGGER_EVENT_TYPE__SEND_MAIL'] = 167,                                -- 发送用户邮件
    ['TRIGGER_EVENT_TYPE__SEND_QUEST_ITEM'] = 123,                          -- 发放任务道具     
    ['TRIGGER_EVENT_TYPE__SEND_TO_AI_AND_SPACE_MESSAGE'] = 121,             -- 对全场景发送事件
    ['TRIGGER_EVENT_TYPE__SEND_TO_AI_MESSAGE'] = 120,                       -- 对对象发送事件
    ['TRIGGER_EVENT_TYPE__SEND_TO_SPACE_MESSAGE'] = 122,                    -- 对位面发送事件
    ['TRIGGER_EVENT_TYPE__SET_ANIMATION'] = 176,                            -- 播动作（服务器发起）
    ['TRIGGER_EVENT_TYPE__SET_QUEST_MARK_DEFINE'] = 145,                    -- 设置任务全局标记
    ['TRIGGER_EVENT_TYPE__SET_SCENE_TIME'] = 169,                           -- 修改当前场景时间
    ['TRIGGER_EVENT_TYPE__START_BUBBLE'] = 144,                             -- 播放Bubble
    ['TRIGGER_EVENT_TYPE__START_CAMERA_POST_PROCESS_EFFECT'] = 162,         -- 指定玩家播放指定镜头后效
    ['TRIGGER_EVENT_TYPE__START_ENTITY_EFFECT'] = 160,                      -- 指定对象播放角色特效
    ['TRIGGER_EVENT_TYPE__START_JUMP_UI'] = 124,                            -- 打开UI界面
    ['TRIGGER_EVENT_TYPE__START_MORPH'] = 134,                              -- 开始变身
    ['TRIGGER_EVENT_TYPE__START_PAINTING_STRATCH'] = 180,                   -- 开启擦拭图片功能
    ['TRIGGER_EVENT_TYPE__START_SCENE_EFFECT'] = 158,                       -- 指定场景里的玩家播放指定场景特效
    ['TRIGGER_EVENT_TYPE__START_SPIRITUAL_VISION'] = 142,                   -- 开启灵视
    ['TRIGGER_EVENT_TYPE__STOP_2DAUDIO'] = 116,                             -- 停止播放2D音效
    ['TRIGGER_EVENT_TYPE__STOP_3DAUDIO'] = 118,                             -- 停止播放3D音效
    ['TRIGGER_EVENT_TYPE__STOP_AUTO_PATH'] = 175,                           -- 停止自动寻路
    ['TRIGGER_EVENT_TYPE__STOP_CANE_SKILL'] = 183,                          -- 停止正在施放的卜杖寻路技能
    ['TRIGGER_EVENT_TYPE__STOP_MORPH'] = 135,                               -- 结束变身
    ['TRIGGER_EVENT_TYPE__STOP_NOCAMERA_DIALOG'] = 174,                     -- 强制停止无镜对话
    ['TRIGGER_EVENT_TYPE__TRANSMIT_HOME'] = 177,                            -- 传送到家园场景
    ['TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE_POS'] = 105,                  -- 同场景传送到指定坐标
    ['TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE_TRIGGER'] = 107,              -- 同场景传送到指定Trigger
    ['TRIGGER_EVENT_TYPE__UNLOCKED_FUNCTION'] = 128,                        -- 功能解锁 
    ['TRIGGER_EVENT_TYPE__ADD_BUFF'] = 15,                                  -- 给自己加buff
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKATINS'] = 19,                          -- 镜头朝向实例
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKATLOC'] = 18,                          -- 镜头朝向位置
    ['TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE'] = 57,                      -- 改变采集物交互状态
    ['TRIGGER_EVENT_TYPE__CHANGE_MINE_STATE'] = 88,                         -- 改变采集物交互状态
    ['TRIGGER_EVENT_TYPE__CHANGE_SCENE_TIME'] = 49,                         -- 切换场景时间
    ['TRIGGER_EVENT_TYPE__CLEAR_TASK_MARK_DEFINE'] = 83,                    -- 清空全局任务标记
    ['TRIGGER_EVENT_TYPE__DESTORY_COLLECT'] = 25,                           -- 销毁采集物
    ['TRIGGER_EVENT_TYPE__EDIT_PLANE_CLIMATE'] = 20,                        -- 修改位面天气
    ['TRIGGER_EVENT_TYPE__END_CAMERA'] = 74,                                -- 结束镜头移动
    ['TRIGGER_EVENT_TYPE__ENTER_QUEST_CONTROL'] = 40,                       -- 进入剧情控制
    ['TRIGGER_EVENT_TYPE__FLOW_CHART'] = 44,                                -- flowchart节点
    ['TRIGGER_EVENT_TYPE__FOG'] = 70,                                       -- 可交互迷雾控制
    ['TRIGGER_EVENT_TYPE__FUNCTION_UNLOCKED'] = 11,                         -- 功能解锁
    ['TRIGGER_EVENT_TYPE__GAZE_BACK'] = 38,                                 -- 恢复看向
    ['TRIGGER_EVENT_TYPE__JUMP_UI'] = 23,                                   -- 打开UI界面
    ['TRIGGER_EVENT_TYPE__KILL_NPC'] = 56,                                  -- 杀死NPC
    ['TRIGGER_EVENT_TYPE__LEAVE_QUEST_CONTROL'] = 41,                       -- 离开剧情控制
    ['TRIGGER_EVENT_TYPE__MONSTER_BACK_TO_NPC'] = 58,                       -- Monster变回NPC
    ['TRIGGER_EVENT_TYPE__MORPH_START'] = 21,                               -- 开始变身
    ['TRIGGER_EVENT_TYPE__MORPH_STOP'] = 22,                                -- 结束变身
    ['TRIGGER_EVENT_TYPE__MOVE_CAMERA'] = 69,                               -- 镜头移动
    ['TRIGGER_EVENT_TYPE__MOVE_TO_POS'] = 31,                               -- 移动到坐标
    ['TRIGGER_EVENT_TYPE__MOVE_TO_TRIGGER'] = 32,                           -- 移动到Trigger
    ['TRIGGER_EVENT_TYPE__NPC_TO_MONSTER'] = 55,                            -- NPC变为Monster
    ['TRIGGER_EVENT_TYPE__OPEN_STAR_MISTERY'] = 85,                         -- 打开星座解谜
    ['TRIGGER_EVENT_TYPE__PLANE_ENTER'] = 7,                                -- 直接进位面
    ['TRIGGER_EVENT_TYPE__PLANE_LEAVE'] = 8,                                -- 直接出位面
    ['TRIGGER_EVENT_TYPE__PLAY_ANIMATION'] = 39,                            -- 播动作
    ['TRIGGER_EVENT_TYPE__PLAY_ASIDETALK'] = 16,                            -- 播放画外音
    ['TRIGGER_EVENT_TYPE__PLAY_AUDIO'] = 86,                                -- 播放音效
    ['TRIGGER_EVENT_TYPE__PLAY_BLACKBG'] = 43,                              -- 播放黑屏字幕
    ['TRIGGER_EVENT_TYPE__PLAY_BUBBLE'] = 42,                               -- 飘头顶气泡文字
    ['TRIGGER_EVENT_TYPE__PLAY_CAMERA_POST_PROCESS_EFFECT'] = 79,           -- 指定玩家播放指定镜头后效
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTEREND'] = 60,                           -- 播放特殊任务结束展示界面
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTERSTART'] = 59,                         -- 播放特殊任务开始展示界面
    ['TRIGGER_EVENT_TYPE__PLAY_CUTSCENE'] = 14,                             -- 播放Cutscene
    ['TRIGGER_EVENT_TYPE__PLAY_ENTITY_EFFECT'] = 77,                        -- 制定对象播放角色特效 
    ['TRIGGER_EVENT_TYPE__PLAY_MUSIC_SOUND'] = 47,                          -- 播放音乐音效
    ['TRIGGER_EVENT_TYPE__PLAY_SCENE_EFFECT'] = 75,                         -- 指定场景里的玩家播放执行场景特效
    ['TRIGGER_EVENT_TYPE__PLAY_STARTTASK'] = 26,                            -- 播放主线任务开始展示界面（卡牌）
    ['TRIGGER_EVENT_TYPE__QTE_ASHBUTTON'] = 66,                             -- QTE-多个按钮点击
    ['TRIGGER_EVENT_TYPE__QTE_BUTTON'] = 68,                                -- QTE-连续多次点击
    ['TRIGGER_EVENT_TYPE__QTE_SLIDE'] = 67,                                 -- QTE-方向滑击
    ['TRIGGER_EVENT_TYPE__REMOVE_BUFF'] = 17,                               -- 移除自己身上的BUFF
    ['TRIGGER_EVENT_TYPE__REMOVE_CAMERA_POST_PROCESS_EFFECT'] = 80,         -- 移除指定玩家当前播放的镜头后效
    ['TRIGGER_EVENT_TYPE__REMOVE_ENTITY_EFFECT'] = 78,                      -- 移除指定对象身上的指定角色特效
    ['TRIGGER_EVENT_TYPE__REMOVE_SCENE_EFFECT'] = 76,                       -- 移除指定场景特效
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_ANG'] = 33,                             -- 转向角度
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_ENTITY'] = 34,                          -- 转向对象
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_LOC'] = 35,                             -- 转向坐标
    ['TRIGGER_EVENT_TYPE__SEND_AI_AND_SPACE_MESSAGE'] = 72,                 -- 对全场景发送事件
    ['TRIGGER_EVENT_TYPE__SEND_AI_MESSAGE'] = 71,                           -- 对对象发送事件
    ['TRIGGER_EVENT_TYPE__SEND_CUSTOM_EVENT'] = 62,                         -- 发送自定义触发事件
    ['TRIGGER_EVENT_TYPE__SEND_SPACE_MESSAGE'] = 73,                        -- 对位面发送事件
    ['TRIGGER_EVENT_TYPE__SEND_TASK_ITEM'] = 12,                            -- 下发任务道具
    ['TRIGGER_EVENT_TYPE__SEND_TASK_ITEM_SINGLE'] = 64,                     -- 发放单组任务道具（仅限任务目标自动生成用）
    ['TRIGGER_EVENT_TYPE__SET_TASK_MARK_DEFINE'] = 82,                      -- 设置任务全局标记
    ['TRIGGER_EVENT_TYPE__SPAWN_COLLECT'] = 24,                             -- 刷新采集物
    ['TRIGGER_EVENT_TYPE__SPAWN_DESTROY'] = 13,                             -- 销毁怪或NPC
    ['TRIGGER_EVENT_TYPE__SPAWN_MINE'] = 4,                                 -- 刷新交互物
    ['TRIGGER_EVENT_TYPE__SPAWN_MINE_POS'] = 65,                            -- 坐标生成位面交互物
    ['TRIGGER_EVENT_TYPE__SPAWN_MINE_SINGLE'] = 63,                         -- 生成单个交互物品（仅限任务目标自动生成用）
    ['TRIGGER_EVENT_TYPE__SPAWN_MONSTER'] = 1,                              -- 刷新一定数量的任务怪
    ['TRIGGER_EVENT_TYPE__SPAWN_NPC'] = 2,                                  -- 刷新任务NPC
    ['TRIGGER_EVENT_TYPE__SPAWN_PLANE_ENTRANCE'] = 5,                       -- 刷新位面入口
    ['TRIGGER_EVENT_TYPE__SPAWN_PLANE_EXIT'] = 6,                           -- 刷新位面出口
    ['TRIGGER_EVENT_TYPE__START_BEHAVIOR_TREE'] = 45,                       -- 开始行为树
    ['TRIGGER_EVENT_TYPE__START_CROSS_WORD_PUZZLE'] = 52,                   -- 开启文字填空解密
    ['TRIGGER_EVENT_TYPE__START_JIGSAW_PUZZLE'] = 54,                       -- 开启拼图解密
    ['TRIGGER_EVENT_TYPE__START_LETTER'] = 53,                              -- 打开信纸
    ['TRIGGER_EVENT_TYPE__START_MOVING_PATH'] = 50,                         -- 沿路径移动开始
    ['TRIGGER_EVENT_TYPE__START_SPIRITUALVISION'] = 61,                     -- 开启灵视
    ['TRIGGER_EVENT_TYPE__STOP_ANIMATION'] = 81,                            -- 停止播放动作
    ['TRIGGER_EVENT_TYPE__STOP_AUDIO'] = 87,                                -- 停止播放音效
    ['TRIGGER_EVENT_TYPE__STOP_BEHAVIOR_TREE'] = 46,                        -- 停止行为树
    ['TRIGGER_EVENT_TYPE__STOP_MOVING'] = 51,                               -- 停止移动
    ['TRIGGER_EVENT_TYPE__STOP_MUSIC_SOUND'] = 48,                          -- 停止音乐音效
    ['TRIGGER_EVENT_TYPE__TELEPORT'] = 10,                                  -- 直接传送到指定地点
    ['TRIGGER_EVENT_TYPE__TRANSMIT_POS'] = 29,                              -- 传送到坐标
    ['TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE'] = 84,                       -- 对象同场景传送（支持NPC）
    ----- 后续再新增从 184 开始 ------------
}




-- 任务Target常量
QuestTargetConst = {
    ['TARGET_TYPE__BUY_ITEMS'] = 113,                                       -- 购买指定物品
    ['TARGET_TYPE__CANE_SKILL_INSTANCEID'] = 157,                           -- 卜杖寻路指定对象（InstanceID）
    ['TARGET_TYPE__CANE_SKILL_POSITION'] = 159,                             -- 卜杖寻路到指定坐标位置
    ['TARGET_TYPE__CANE_SKILL_TEMPLATEID'] = 158,                           -- 卜杖寻路指定对象（TemplateID）
    ['TARGET_TYPE__CAST_SKILL'] = 30,                                       -- 释放技能
    ['TARGET_TYPE__CAST_SKILL_BY_INSTANCEID'] = 131,                        -- 施放指定技能攻击指定InstanceID的怪物
    ['TARGET_TYPE__CAST_SKILL_BY_POS'] = 133,                               -- 指定目标范围释放指定技能
    ['TARGET_TYPE__CAST_SKILL_BY_SKILLID'] = 134,                           -- 施放指定技能
    ['TARGET_TYPE__CAST_SKILL_BY_TEMPLATEID'] = 132,                        -- 施放指定技能攻击指定TemplateID的怪物
    ['TARGET_TYPE__CAST_SKILL_BY_TRIGGER'] = 135,                           -- 指定Trigger范围释放指定技能
    ['TARGET_TYPE__CHANGE_NAME_SUCC'] = 166,                                -- 玩家取名成功
    ['TARGET_TYPE__CHAT'] = 17,                                             -- 在指定聊天频道发言指定次数
    ['TARGET_TYPE__CHAT_NUMBER'] = 126,                                     -- 玩家在聊天频道发送指定文本
    ['TARGET_TYPE__CLOSE_LETTER'] = 33,                                     -- 关闭书信
    ['TARGET_TYPE__COLLECT_ITEM'] = 103,                                    -- 采集物品
    ['TARGET_TYPE__COLLECT_ITEMS'] = 26,                                    -- 采集物品
    ['TARGET_TYPE__COLLECT_ITEM_RANDOM'] = 104,                             -- 玩家采集指定采集物（可设置采集概率）（待开发）
    ['TARGET_TYPE__COLLECT_ITEM_TEMPLATEID'] = 152,                         -- 开启灵视
    ['TARGET_TYPE__COMPLETE_DUNGEON'] = 136,                                -- 完成指定副本
    ['TARGET_TYPE__CUSTOM_EVENT'] = 29,                                     -- 自定义事件
    ['TARGET_TYPE__CUT_MONSTER_HP'] = 22,                                   -- 将怪物打到指定百分比血量
    ['TARGET_TYPE__CUT_MONSTER_HP_BY_INSTANCEID'] = 122,                    -- 将怪物打到指定百分比血量(instanceID)
    ['TARGET_TYPE__CUT_MONSTER_HP_BY_TEMPLATEID'] = 123,                    -- 将怪物打到指定百分比血量(templateID
    ['TARGET_TYPE__DOOR'] = 161,                                            -- 与指定门进行交互
    ['TARGET_TYPE__DUNGEON_COMPLETE'] = 7,                                  -- 完成指定副本
    ['TARGET_TYPE__DUNGEON_COMPLETE_IN_GUILD'] = 115,                       -- 跟公会成员通关指定副本
    ['TARGET_TYPE__DUNGEON_COMPLETE_WITH_GUILD'] = 18,                      -- 跟公会成员通关指定副本指定次数
    ['TARGET_TYPE__ENTER_SPACE_CLIENT_LOADED'] = 160,                       -- 监听指定场景加载完毕
    ['TARGET_TYPE__EQUIP'] = 109,                                           -- 装备指定装备
    ['TARGET_TYPE__EQUIP_EQUIPMENT'] = 24,                                  -- 装备指定装备
    ['TARGET_TYPE__ESCORT'] = 6,                                            -- 护送任务NPC到指定地点
    ['TARGET_TYPE__FIND_CORRECT_NPC'] = 28,                                 -- 寻找正确的NPC
    ['TARGET_TYPE__FIND_NPC'] = 140,                                        -- 寻找指定TemplateID的NPC并开启剧情对话
    ['TARGET_TYPE__FINISHED_QUEST'] = 138,                                  -- 完成指定任务步骤
    ['TARGET_TYPE__FINISHED_RING'] = 137,                                   -- 完成指定任务
    ['TARGET_TYPE__FINISH_ALL_SUBTARGETS'] = 153,                           -- 完成当前步骤的所有子目标
    ['TARGET_TYPE__FINISH_EXTRAORDINARY_EVENT'] = 165,                      -- 完成非凡事件
    ['TARGET_TYPE__FINISH_GUILD_MATERIAL_QUEST'] = 110,                     -- 完成工会材料回收
    ['TARGET_TYPE__FINISH_GUILD_MATERIAL_TASK'] = 25,                       -- 完成生活材料任务
    ['TARGET_TYPE__FINISH_QTE'] = 31,                                       -- 完成QTE玩法
    ['TARGET_TYPE__FINISH_QTE_ASHBUTTON'] = 146,                            -- 完成多按钮QTE
    ['TARGET_TYPE__FINISH_QTE_BUTTON'] = 148,                               -- 完成多次连击QTE
    ['TARGET_TYPE__FINISH_QTE_SLIDE'] = 147,                                -- 完成滑动QTE
    ['TARGET_TYPE__FINISH_RING'] = 16,                                      -- 完成指定任务环
    ['TARGET_TYPE__FINISH_SUBTARGET_COUNT'] = 154,                          -- 完成当前步骤一定数量的子目标
    ['TARGET_TYPE__FINISH_SUBTARGET_LIST'] = 155,                           -- 完成当前步骤的必要子目标和次要子目标
    ['TARGET_TYPE__GET_ITEMS'] = 139,                                       -- 获得指定道具
    ['TARGET_TYPE__GOTO_LEVELMAP'] = 23,                                    -- 到达指定关卡
    ['TARGET_TYPE__GO_TO_LEVELMAP'] = 108,                                  -- 前往指定场景
    ['TARGET_TYPE__GO_TO_POS'] = 107,                                       -- 前往指定坐标
    ['TARGET_TYPE__GO_TO_TRIGGER'] = 106,                                   -- 前往指定Trigger位置
    ['TARGET_TYPE__INTERACTION'] = 9,                                       -- 与交互物交互完成
    ['TARGET_TYPE__INTERACTION_COMPLETED'] = 141,                           -- 与指定场景交互物进行交互
    ['TARGET_TYPE__INTERACTION_PLANE'] = 15,                                -- 与位面入口交互
    ['TARGET_TYPE__INTERACTION_PLANE_ID'] = 142,                            -- 前往指定坐标附近进入位面
    ['TARGET_TYPE__INTERACTION_PLANE_POS'] = 143,                           -- 前往指定交互物进入位面
    ['TARGET_TYPE__ITEM_IN_BAG'] = 10,                                      -- 背包里拥有道具
    ['TARGET_TYPE__ITEM_SUBMIT'] = 124,                                     -- 提交道具(设置提交ID)
    ['TARGET_TYPE__ITEM_SUBMIT_BRANCH'] = 168,                              -- 分支提交道具（开发中）
    ['TARGET_TYPE__ITEM_SUBMIT_DIRECT'] = 167,                              -- 提交道具（设置提交参数）
    ['TARGET_TYPE__ITEM_TO_BAG'] = 27,                                      -- 物品进包
    ['TARGET_TYPE__JIGSAW_PUZZLE_SUCC'] = 37,                               -- 拼图解谜成功
    ['TARGET_TYPE__JIGSAW_PUZZLE_SUCCES'] = 119,                            -- 完成拼图解谜
    ['TARGET_TYPE__LETTER_CLOSE'] = 114,                                    -- 监听关闭阅读界面
    ['TARGET_TYPE__LEVEL'] = 105,                                           -- 达到指定等级
    ['TARGET_TYPE__LEVELUP'] = 8,                                           -- 玩家达到XX等级
    ['TARGET_TYPE__LOCATION'] = 5,                                          -- 到达指定地点
    ['TARGET_TYPE__MISTERY_SUCC'] = 117,                                    -- 完成星座解谜
    ['TARGET_TYPE__MONSTER_KILL'] = 1,                                      -- 击杀一定数量怪物
    ['TARGET_TYPE__MONSTER_KILL_COUNT'] = 2,                                -- 击杀一定数量怪物
    ['TARGET_TYPE__MONSTER_KILL_INSTANCEID'] = 127,                         -- 击败指定InstanceID的怪物
    ['TARGET_TYPE__MONSTER_KILL_TEMPLATEID'] = 128,                         -- 击败指定TemplateID的怪物
    ['TARGET_TYPE__NPCTALK_ENTER_PLANE'] = 116,                             -- Npc对话进入位面
    ['TARGET_TYPE__NPC_ASK_PRICE_SUCC'] = 120,                              -- 与NPC问价完成 
    ['TARGET_TYPE__NPC_DIALOGUE'] = 144,                                    -- 寻找指定TemplateID的NPC并开启剧情对话
    ['TARGET_TYPE__NPC_SEQUENCE_DIALOGUE'] = 169,                           -- 与Npc进行连续对话
    ['TARGET_TYPE__NPC_TALK'] = 3,                                          -- 和NPC完成对话
    ['TARGET_TYPE__NULL'] = 112,                                            -- 空步骤 
    ['TARGET_TYPE__OPEN_SPIRITUALVISION'] = 151,                            -- 开启灵视
    ['TARGET_TYPE__OPEN_UI_JUMP'] = 36,                                     -- 打卡UI界面
    ['TARGET_TYPE__PAINTING_STRATCH_SUCC'] = 164,                           -- 监听完成擦拭图片
    ['TARGET_TYPE__PLAY_CUTSCENE'] = 21,                                    -- 播放CutScene
    ['TARGET_TYPE__PLAY_DIALOGUE'] = 20,                                    -- 播放Dialogue
    ['TARGET_TYPE__POTION_MAKE_SUCCESS'] = 163,                             -- 魔药炼制完成
    ['TARGET_TYPE__RECEIVE_CUSTOM_EVENT'] = 125,                            -- 任务自定义事件
    ['TARGET_TYPE__SEQUENCE_SAN_CHECK_SUCC'] = 121,                         -- 灵性回收成功
    ['TARGET_TYPE__SERVER_ENTER_PLANE'] = 999,                              -- 服务器内部用进位面
    ['TARGET_TYPE__SHAPE_TRIGGER_LOCATION'] = 38,                           -- 到达指定ShapeTrigger
    ['TARGET_TYPE__SHOP_BUY'] = 13,                                         -- 商店购买
    ['TARGET_TYPE__SIT_ANY_CHAIR'] = 156,                                   -- 玩家坐到任意位置
    ['TARGET_TYPE__SIT_CHAIR'] = 149,                                       -- 玩家坐到指定InstanceID的座位
    ['TARGET_TYPE__SIT_DOWN_CHAIR'] = 34,                                   -- 坐椅子
    ['TARGET_TYPE__START_CUTSCENE'] = 101,                                  -- 播放CutScene
    ['TARGET_TYPE__START_DIALOGUE'] = 102,                                  -- 播放Dialogue
    ['TARGET_TYPE__START_SPIRITUALVISION'] = 32,                            -- 开启灵视
    ['TARGET_TYPE__STAR_MISTERY_SUCC'] = 35,                                -- 完成星座解谜
    ['TARGET_TYPE__SUBMIT_ITEM'] = 11,                                      -- 交付道具
    ['TARGET_TYPE__TALK_ENTER_PLANE'] = 19,                                 -- 和NPC完成对话进入位面
    ['TARGET_TYPE__TALK_NPC'] = 145,                                        -- 与Npc进行闲话
    ['TARGET_TYPE__UI_JUMP'] = 118,                                         -- 打开指定的UI界面
    ['TARGET_TYPE__USE_ITEM'] = 150,                                        -- 使用指定道具
    ['TARGET_TYPE__USE_ITEM_AT_LOCATION'] = 12,                             -- 指定地点使用道具
    ['TARGET_TYPE__USE_ITEM_AT_POSITION'] = 129,                            -- 指定地点附近使用任务道具
    ['TARGET_TYPE__USE_ITEM_BY_INSTANCEID'] = 130,                          -- 指定对象附近使用任务道具
    ['TARGET_TYPE__WAITTIME'] = 111,                                        -- 等待时间
    ['TARGET_TYPE__WAIT_TIME'] = 14,                                        -- 等待时间
    ['TARGET_TYPE__WEAR_FASHION'] = 162,                                    -- 穿戴指定的时装部件
    
    ----- 后续再新增从 170 开始 ------------
}





-- 客户端执行的任务Action枚举
TaskActionTypeExecClient = {
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_DIALOGUE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_CUTSCENE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_ASIDETALK] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__CAMERA_LOOKATLOC] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__CAMERA_LOOKATINS] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__JUMP_UI] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_STARTTASK] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__GAZE_TO_SPAWNER] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__GAZE_TO_LOC] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__GAZE_BACK] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_ANIMATION] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__ENTER_QUEST_CONTROL] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__LEAVE_QUEST_CONTROL] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_BUBBLE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_BLACKBG] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_MUSIC_SOUND] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__STOP_MUSIC_SOUND] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__START_CROSS_WORD_PUZZLE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__START_LETTER] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__START_JIGSAW_PUZZLE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_CHAPTERSTART] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_CHAPTEREND] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__QTE_ASHBUTTON] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__QTE_SLIDE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__QTE_BUTTON] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__MOVE_CAMERA] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__END_CAMERA] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__STOP_ANIMATION] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__OPEN_STAR_MISTERY] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_BLACK_BG] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_ASIDE_TALK] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_CHAPTER_START] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_CHAPTER_END] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_START_QUEST] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_END_QUEST] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__OPEN_CROSS_WORD_PUZZLE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__OPEN_LETTER] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__OPEN_JIGSAW_PUZZLE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__CLOSE_LETTER] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__START_BUBBLE] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_NPC] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_POSITION] = true, 
    [QuestTriggerConst.TRIGGER_EVENT_TYPE__STOP_NOCAMERA_DIALOG] = true, 
}

