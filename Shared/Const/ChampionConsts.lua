CHAMPION_TROOP_DEFAULT_ID = 0
CHAMPION_OPEN_ACTIVITY_ID = 1108
-- 活动状态
CHAMPION_ACTIVITY_STAGE = {
    CLOSE = 0,
    ---------- 报名阶段 ----------
    SIGN_UP = 10,
    SIGN_UP_LOCK = 11,
    ---------- 准备阶段 ----------
    PREPARE = 20,
    ---------- 战斗阶段 ----------
    BATTLE = 30,
    ---------- 最终结算阶段 ----------
    CALCULATE = 60,
}

--淘汰赛阶段的进度条进度
CHAMPION_ELIMINATION_REWARD_STATUS_PROGRESS_BAR =
{
	0.18, 0.393, 0.604, 0.815, 1
}

CHAMPION_ACTIVITY_STAGE_TEXT = {
	[CHAMPION_ACTIVITY_STAGE.CLOSE] = "CHAMPION_STAGE_CLOSE",
	[CHAMPION_ACTIVITY_STAGE.SIGN_UP] = "CHAMPION_STAGE_SIGN_UP",
	[CHAMPION_ACTIVITY_STAGE.SIGN_UP_LOCK] = "CHAMPION_STAGE_SIGN_UP_LOCK",
	[CHAMPION_ACTIVITY_STAGE.PREPARE] = "CHAMPION_STAGE_PREPARE",
	[CHAMPION_ACTIVITY_STAGE.BATTLE] = "CHAMPION_STAGE_BATTLE",
	[CHAMPION_ACTIVITY_STAGE.CALCULATE] = "CHAMPION_STAGE_CALCULATE",
}

CHAMPION_SCHEDULE_TYPE = {
    GROUP_BATTLE = 1,
    ELIMINATION = 2,
}

CHAMPION_ELIMINTION_ROUND_STATUS = {
    NONE = 0, -- 无
    PREPARE = 1, -- 匹配准备中
    BATTLE = 2, -- 战斗状态
    END = 3, -- 战斗结束
}

CHAMPION_GROUP_BATTLE_PAGE_SIZE = 50
CHAMPION_START_MAIL_ID = 6260052

CHAMPION_TROOP_OP_TYPE = {
    CREATE =    1, -- 创建
    QUIT =      2, -- 退出
    DISBAND =   3, -- 解散
    JOIN =      4, -- 入队
    KICK =      5, -- 踢人
    SIGN_UP =   6, -- 报名
    CHANGE_NAME = 7, -- 改名
    CHANGE_LEADER = 8, -- 转让队长
}

CHAMPION_TROOP_OP_TYPE_TEXT = {
	[CHAMPION_TROOP_OP_TYPE.CREATE] = "CHAMPION_OP_CREATE",
	[CHAMPION_TROOP_OP_TYPE.QUIT] = "CHAMPION_OP_QUIT",
	[CHAMPION_TROOP_OP_TYPE.DISBAND] = "CHAMPION_OP_DISBAND",
	[CHAMPION_TROOP_OP_TYPE.JOIN] = "CHAMPION_OP_JOIN",
	[CHAMPION_TROOP_OP_TYPE.KICK] = "CHAMPION_OP_KICK",
	[CHAMPION_TROOP_OP_TYPE.SIGN_UP] = "CHAMPION_OP_SIGN_UP",
	[CHAMPION_TROOP_OP_TYPE.CHANGE_NAME] = "CHAMPION_OP_CHANGE_NAME",
	[CHAMPION_TROOP_OP_TYPE.CHANGE_LEADER] = "CHAMPION_OP_CHANGE_LEADER",
}

CHAMPION_TROOP_STATUS_TYPE = {
	[0] = "CHAMPION_TROOP_STATUS_NOT_SIGN",
	[1] = "CHAMPION_TROOP_STATUS_SIGN_UP"
}

-- 战队操作白名单
CHAMPION_TROOP_OP_LIMIT = {
    [10] = { -- 准备阶段
        [0] = { -- 未报名战队
            [CHAMPION_TROOP_OP_TYPE.CREATE] = true,
            [CHAMPION_TROOP_OP_TYPE.QUIT] = true,
            [CHAMPION_TROOP_OP_TYPE.DISBAND] = true,
            [CHAMPION_TROOP_OP_TYPE.JOIN] = true,
            [CHAMPION_TROOP_OP_TYPE.KICK] = true,
            [CHAMPION_TROOP_OP_TYPE.SIGN_UP] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_NAME] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_LEADER] = true,
        },
        [1] = { -- 报名战队
            [CHAMPION_TROOP_OP_TYPE.CREATE] = true,
            [CHAMPION_TROOP_OP_TYPE.QUIT] = true,
            [CHAMPION_TROOP_OP_TYPE.DISBAND] = true,
            [CHAMPION_TROOP_OP_TYPE.JOIN] = true,
            [CHAMPION_TROOP_OP_TYPE.KICK] = true,
            [CHAMPION_TROOP_OP_TYPE.SIGN_UP] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_NAME] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_LEADER] = true,
        },
    },
    [11] = { -- 锁定阶段
        [0] = { -- 未报名战队
            [CHAMPION_TROOP_OP_TYPE.CREATE] = true,
            [CHAMPION_TROOP_OP_TYPE.QUIT] = true,
            [CHAMPION_TROOP_OP_TYPE.DISBAND] = true,
            [CHAMPION_TROOP_OP_TYPE.JOIN] = true,
            [CHAMPION_TROOP_OP_TYPE.KICK] = true,
            [CHAMPION_TROOP_OP_TYPE.SIGN_UP] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_NAME] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_LEADER] = true,
        },
        [1] = { -- 报名战队
            [CHAMPION_TROOP_OP_TYPE.CREATE] = true,
            [CHAMPION_TROOP_OP_TYPE.JOIN] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_NAME] = true,
            [CHAMPION_TROOP_OP_TYPE.CHANGE_LEADER] = true,
        },
    },
    [20] = { -- 截止分组阶段
        [0] = { -- 未报名战队
            [CHAMPION_TROOP_OP_TYPE.QUIT] = true,
            [CHAMPION_TROOP_OP_TYPE.DISBAND] = true,
            [CHAMPION_TROOP_OP_TYPE.KICK] = true,
        },
        [1] = {}, -- luacheck:ignore
    },
    [30] = { -- 比赛阶段
        [0] = { -- 未报名战队
            [CHAMPION_TROOP_OP_TYPE.QUIT] = true,
            [CHAMPION_TROOP_OP_TYPE.DISBAND] = true,
            [CHAMPION_TROOP_OP_TYPE.KICK] = true,
        },
        [1] = {}, -- luacheck:ignore
    },
}