local AbilityConst = kg_require("Shared.Const.AbilityConst")
local ESkillType = AbilityConst.ESkillType

BuffStateType = BuffStateType or {
    Empty = 0, -- 空占位符
	Attach = 1, -- 挂载（未实现）
	Sleep = 2, -- 催眠
	Stealth = 3, -- 隐身
	Taunt = 5, -- 嘲讽
	Endure = 6, -- 霸体
	Stuck = 7, -- 禁锢
	Dizzy = 10, -- 眩晕
	Silent = 11, -- 沉默
	SpeedDown = 15,  -- 减速
	NormalBossMechanism = 16,  -- 通用boss机制空状态
    DodgeLimit = 17,    -- 滞涩
    Terror = 18,    -- 恐惧
    CharacterStealth = 19, -- 隐身,该隐身与Stealth唯一的差别在于，该隐身只具有ImunneLock效果，不具有Hitlimit效果
	StoneState = 100, -- 石化状态
}

ValidBuffStates = ValidBuffStates or {
	[BuffStateType.Attach] = true,
	[BuffStateType.Sleep] = true,
	[BuffStateType.Stealth] = true,
	[BuffStateType.Taunt] = true,
	[BuffStateType.Endure] = true,
	[BuffStateType.Stuck] = true,
	[BuffStateType.Dizzy] = true,
	[BuffStateType.Silent] = true,
	[BuffStateType.SpeedDown] = true,
	[BuffStateType.NormalBossMechanism] = true,
    [BuffStateType.DodgeLimit] = true,
    [BuffStateType.Terror] = true,
    [BuffStateType.CharacterStealth] = true,
	[BuffStateType.StoneState] = true,
}


StealthStates = StealthStates or {
    [BuffStateType.Stealth] = true,
    [BuffStateType.CharacterStealth] = true,
}


BossMechanismStates = BossMechanismStates or {
    [BuffStateType.NormalBossMechanism] = true,
    [BuffStateType.StoneState] = true,
    [BuffStateType.Terror] = true,
}


TerrorStateStep = TerrorStateStep or {
    None = 0,    -- 尚未恐惧状态
    Leave = 1,   -- 恐惧刚开始的逃离阶段
    Wander = 2,   -- 恐惧到达最远点，开始随机晃荡的阶段
}


TerrorLeaveDistance = 2000


HasAnimState = {
    BuffStateType.Sleep,
    BuffStateType.Dizzy,
}


local EDisabledSkillReason = nil
if not _G.IS_SERVER then
    require "Data.Config.BattleSystem.ExportedEnum"
    EDisabledSkillReason = ETE.EDisabledSkillReason
else
    EDisabledSkillReason = kg_require("Logic.Combat.Ability.AbEnumExport").EDisabledSkillReason -- luacheck: ignore
end

StateEffectForSkill = {
    DodgeSkillForbid = {
        FuncName = "BuffStateForbidDodge",
        Params = {},
    },

    NormalSkillForbid = {
        FuncName = "AddDisableSkillType",
        Params = {ESkillType.NormalSkill, EDisabledSkillReason.BuffState},
    },

    NormalAttackForbid = {
        FuncName = "AddDisableSkillType",
        Params = {ESkillType.NormalAttack, EDisabledSkillReason.BuffState},
    },

    UltimateSkillForbid = {
        FuncName = "AddDisableSkillType",
        Params = {ESkillType.UltimateSkill, EDisabledSkillReason.BuffState},
    },

    DeControlSkillForbid = {
        FuncName = "ForbidDeControlSkill",
        Params = {EDisabledSkillReason.BuffState},
    },
}


StateEffectForLoco = {
    MoveXYForbid = {
        FuncName = "DisableLocoMove",
        Params = {EDisabledSkillReason.BuffState, true, false},
    },

    JumpForbid = {
        FuncName = "DisableLocoJump",
        Params = {EDisabledSkillReason.BuffState, true, false},
    },

    RotateForbid = {
        FuncName = "DisableLocoRotate",
        Params = {EDisabledSkillReason.BuffState, true, false},
    }
}


EBSStealthType = {
	UnActivated = 1,
	ChangeMaterial = 2,
	HideMesh = 3
}