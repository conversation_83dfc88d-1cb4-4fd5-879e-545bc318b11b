-- 受击相关的常量
local AbilityConst = kg_require("Shared.Const.AbilityConst")
local ESkillType = AbilityConst.ESkillType

LIGHT_HIT_ANIM_ASSERT_TYPE = "LightHitFullBody"

SPECIAL_BONE = "SpineBaseBone"



-- CanCastUnderControl类型
CanCastUnderControlType = {
    All = 1, -- 所有hitlevel = 1 的受击，controllevel=1的状态
}



-- 活动状态
HF_MOVE_INFO_TYPE = {
    PhysicFallInfoType = 0, 
    RootMotionInfoType = 1,
}

HitActionType = {
    ["LightHit"] = 0, -- 轻攻击
    ["HitBack"] = 1, -- 击退
    ["HitDown"] = 2, -- 击倒
    ["HitFly"] = 3, -- 击飞
    ["HitFloat"] = 4, -- 挑空
    ["HitDrag"] = 8, -- 拖拽
    ["HitStiff"] = 9, -- 僵直攻击
}


HIT_PARAM_TYPE = {
    NORMAL = 0,
    ROOT_MOTION = 1,
    PHYSICAL_FALL = 2,
    HOVER = 3,
    -- 上面为c/s沟通大类，下面是客户端细分表现
    LIE_DOWN = 4,
    PHYSICAL_FALL_LOOP = 5,

    HIT_FLY_START = 6,
    HIT_FLOAT_START = 7,

    DOWN_AFTER_FLY = 8,
}


local EDisabledSkillReason = nil
if not _G.IS_SERVER then
    require "Data.Config.BattleSystem.ExportedEnum"
    EDisabledSkillReason = ETE.EDisabledSkillReason
else
    EDisabledSkillReason = kg_require("Logic.Combat.Ability.AbEnumExport").EDisabledSkillReason -- luacheck: ignore
end

STOP_HIT_LOGIC_REASON = {
    TIMER = 1,  -- 定时器的打断
    SERVER = 2,  -- 服务器RPC下发的打断 
    CHANGE = 3,  -- 受击状态的改变引发的打断
}

FORBID_NONEDECONTROL_SKILL_MAP = {
    [ESkillType.NormalSkill] = "NormalSkillForbid",
    [ESkillType.NormalAttack] = "NormalAttackForbid",
    [ESkillType.UltimateSkill] = "UltimateSkillForbid",
    [ESkillType.DodgeSkill] = "DodgeSkillForbid",
    [ESkillType.DeControlSkill] = "DeControlSkillForbid",
}

DeControlSkillForbid_HighLevel = "DeControlSkillForbid_HighLevel"

ACTION_NAME_FOR_LOCO_FORBID = {
    Move = 1,
    Rotate = 2,
    Jump = 3,
    Doge = 4,

}

HIT_FORBID_LOCO_MAP = {
    [ACTION_NAME_FOR_LOCO_FORBID.Move] = "MoveXYForbid",
    [ACTION_NAME_FOR_LOCO_FORBID.Rotate] = "RotateForbid",
    [ACTION_NAME_FOR_LOCO_FORBID.Jump] = "JumpForbid",
    [ACTION_NAME_FOR_LOCO_FORBID.Doge] = "DodgeSkillForbid",
}




HIT_INFLUENCE_FOR_SKILL_MAP = {
    DodgeSkillForbid = {
        FuncName = "ForbidDodge",
        Params = {},
    },

    NormalSkillForbid = {
        FuncName = "AddDisableSkillType",
        Params = {ESkillType.NormalSkill, EDisabledSkillReason.Stagger},
    },

    NormalAttackForbid = {
        FuncName = "AddDisableSkillType",
        Params = {ESkillType.NormalAttack, EDisabledSkillReason.Stagger},
    },

    UltimateSkillForbid = {
        FuncName = "AddDisableSkillType",
        Params = {ESkillType.UltimateSkill, EDisabledSkillReason.Stagger},
    },

    DeControlSkillForbid = {
        FuncName = "ForbidDeControlSkill",
        Params = {EDisabledSkillReason.Stagger},
    },

    DeControlSkillForbid_HighLevel = {
        FuncName = "ForbidDeControlSkill",
        Params = {EDisabledSkillReason.StaggerLevel2},
    },
}


HIT_INFLUENCE_FOR_LOCO_MAP = {
    MoveXYForbid = {
        FuncName = "DisableLocoMove",
        Params = {EDisabledSkillReason.Stagger, true, false},
    },

    JumpForbid = {
        FuncName = "DisableLocoJump",
        Params = {EDisabledSkillReason.Stagger, true, false},
    },

    RotateForbid = {
        FuncName = "DisableLocoRotate",
        Params = {EDisabledSkillReason.Stagger, true, false},
    }
}


STAGGER_STATE = {
	SS_Normal = 0, -- 无硬直
	SS_Repel = 1, -- 击退
	SS_LieDown = 2, -- 倒地受击主动触发的倒地
	SS_Float = 6, -- 漂浮
	SS_Struggle = 8, -- 拖拽
	SS_BlowFly = 9, -- 击飞
	SS_Stiff= 10, -- 僵直
    SS_FloatLieDown = 11, -- 浮空后自动衔接的倒地
    SS_BlowFlyLieDown = 12, -- 击飞后自动衔接的倒地
}

ANIM_STAGE = {
    HitDown_Loop = "HitDown_Loop",
    HitFly_HitDown_Loop = "HitFly_HitDown_Loop",
    HitFloat_HitDown_Loop = "HitFloat_HitDown_Loop"
}

-- HIT_ANIM_STAGE_FLOW_MAP = {
--     [STAGGER_STATE.SS_Repel] = {"HitBack"},
--     [STAGGER_STATE.SS_LieDown] = {"HitDown_Start", "HitDown_Loop", "HitDown_End"},
--     [STAGGER_STATE.SS_Float] = {"HitFloat_Start", "HitFloat_Loop", "HitFloat_End", "HitFloat_HitDown_Loop", "HitFloat_HitDown_End"},
--     [STAGGER_STATE.SS_BlowFly] = {"HitFly_Start", "HitFly_Loop", "HitFly_End", "HitFly_HitDown_Loop", "HitFly_HitDown_End"},
--     [STAGGER_STATE.SS_Stiff] = {"HitStiff"},
-- }

-- PASSIVE_TRIGGER_START_ANIM_STATE = {
--     [STAGGER_STATE.SS_Float] = "HitFloat_End",
--     [STAGGER_STATE.SS_BlowFly] = "HitFly_End",
-- }

PASSIVE_TRIGGER_START_ANIM_STATE = {
    [HitActionType.HitFloat] = "HitFloat_End",
    [HitActionType.HitFly] = "HitFly_End",
}

LightHitAnimStage = {"LightHit", "HeavyHit_FR", "HeavyHit_FL", "HeavyHit_BR", "HeavyHit_BL"}


-- 循环动画阶段集合
LoopAnimStageLst = {"HitDown_Loop", "HitFloat_Loop", "HitFloat_HitDown_Loop", "HitFly_Loop", "HitFly_HitDown_Loop"}

function encodeStaggerParamList(state, kvTable, timeMultiParamVal)
    if kvTable == nil then
        return nil
    end
    local paramList = {}
    table.insert(paramList, kvTable.HitLevel or 1)
    table.insert(paramList, kvTable.HitType or 0)
    table.insert(paramList, kvTable.AnimType or 0)
    if state == STAGGER_STATE.SS_LieDown then
        table.insert(paramList, (kvTable.HitTimeAdd or 0) * (timeMultiParamVal or 1.0 ))
    elseif state == STAGGER_STATE.SS_Float then
        table.insert(paramList, (kvTable.HitDownLoopTime or 0) * (timeMultiParamVal or 1.0 ))
    elseif state == STAGGER_STATE.SS_BlowFly then
        table.insert(paramList, (kvTable.HitDownLoopTime or 0) * (timeMultiParamVal or 1.0 ))
    end
    return paramList
end

function decodeStaggerParamList(state, paramList)
    if paramList == nil then
        return nil
    end
    local kvTable = {}
    kvTable["HitLevel"] = math.floor(paramList[1])
    kvTable["Attack"] = math.floor(paramList[2])
    kvTable["Type"] = math.floor(paramList[3])

    if state == STAGGER_STATE.SS_LieDown then
        kvTable["HitTimeAdd"] = paramList[4]
    elseif state == STAGGER_STATE.SS_Float then
        kvTable["HitDownLoopTime"] = paramList[4]
    elseif state == STAGGER_STATE.SS_BlowFly then
        kvTable["HitDownLoopTime"] = paramList[4]
    end
    return kvTable
end