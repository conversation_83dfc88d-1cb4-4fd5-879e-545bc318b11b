-- 布尔属性压缩存储 减少属性数量，增加需要存储或同步的bool属性直接在此添加即可
-- 详见文档 https://docs.corp.kuaishou.com/k/home/<USER>/fcAAHfjZakUsjidU1g2_xJx3D

-- id间隔不要过大，排列紧密压缩效果更好 上线后禁止修改
IS_AUTO_CUNSUME_LOUD_SPEAKER_MONEY = 10 -- 没有喇叭道具的情况下自动消耗货币
IS_DIALOGE_OPTION_REPORT_SERVER = 15 -- 是否上报对话选项
IS_ENTER_TEAROOM = 20 -- 是否正在进入茶壶房间
IS_CREATE_TEAROOM = 21 -- 是否正在创建茶壶房间
IS_TEAROOM_BARRAGESWITCH = 22 -- 茶壶房间的弹幕
IS_PLAYED_MINIGAME_JIGSAWPUZZLE = 23 -- 是否游玩过拼图解密
IS_PARTY_BARRAGESWITCH = 26 -- 派对房间的弹幕
IS_TEAROOM_FAVORITE_CALLBACK = 27 -- 是否收到收藏茶壶房间的回调
IS_PARTY_FAVORITE_CALLBACK = 28 -- 是否收到收藏派对房间的回调
IS_CHECK_CHAMPION_TROOP_DIRTY_STRING = 29 -- 是否在检查比武大会战队敏感字
IS_CHECK_TAROTTEAM_DIRTY_STRING = 30 -- 是否在检查塔罗小队敏感字

FLAG_DEF =
{
    --[key] -> {是否变化通知客户端, 是否可以客户端改动, 是否需要持久化}
    --如果均为false 可以不添加
    [IS_AUTO_CUNSUME_LOUD_SPEAKER_MONEY] = {true, true, true},
    [IS_TEAROOM_BARRAGESWITCH] = {true, true, true},
    [IS_DIALOGE_OPTION_REPORT_SERVER] = {true,false,false},
	[IS_PLAYED_MINIGAME_JIGSAWPUZZLE] = {true, true, true},
}

-- 初始值为true的flag
-- 数组，角色上线时会遍历初始化
DEFAULT_TRUE_FLAG = {
    -- xx,
}

-- 需要同步到数据中心的设置 map
NEED_SYNC_BOOL_PROP = {
    -- xx = true,
}