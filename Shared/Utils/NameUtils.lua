ENameType = {
    Nickname = 1, 	--普通昵称
    HiddenName = 2,		--隐称
}

--截取字符串，按字符截取
-- name:        需要检查的名字
-- nameType:    需要检查的名字类型（1为昵称，2为隐称）
function containsSpecialNameStr(name, nameType)
    for _, specialNickNameInfo in pairs(TableData.GetSpecialNickNameDataTable()) do
        if specialNickNameInfo.Type == nameType and string.find(name, specialNickNameInfo.Nickname) then
            return true
        end
    end
    return false
end


-- 随机中文生成 Start
-- 注意, 自动化测试用, 不要用于生产环境 

-- 定义常用中文汉字的 Unicode 范围。
-- 这些范围涵盖了 CJK Unified Ideographs 的主要部分，只包含最常用的汉字。
CHINESE_UNICODE_RANGES = {
    {0x4E00, 0x9FFF},   -- CJK Unified Ideographs (最常用汉字)
    -- CJK Unified Ideographs Extension A (0x3400, 0x4DBF) 已移除，以只包含常用汉字
    -- 更高的 Unicode 范围 (如 Extension B: 0x20000-0x2A6DF)
    -- 编码会更复杂 (4 字节)，且生成的汉字可能不常见。
    -- 本示例主要关注 3 字节编码的常用汉字。
}

--- 将一个 Unicode 码点转换为其 UTF-8 字节序列。
-- 此函数手动处理 1、2、3、4 字节的 UTF-8 编码。
-- 中文汉字通常落在 3 字节编码的范围内。
-- @param codepoint number 要转换的 Unicode 码点。
-- @return string 码点对应的 UTF-8 字节序列字符串。
function unicodeToUtf8(codepoint)
    if codepoint < 0x80 then
        -- 1 字节序列: 0xxxxxxx
        return string.char(codepoint)
    elseif codepoint < 0x800 then
        -- 2 字节序列: 110xxxxx 10xxxxxx
        return string.char(
            0xC0 + bit.rshift(codepoint, 6),
            0x80 + bit.band(codepoint, 0x3F)
        )
    elseif codepoint < 0x10000 then
        -- 3 字节序列: 1110xxxx 10xxxxxx 10xxxxxx (中文汉字主要在此范围)
        return string.char(
            0xE0 + bit.rshift(codepoint, 12),
            0x80 + bit.band(bit.rshift(codepoint, 6), 0x3F),
            0x80 + bit.band(codepoint, 0x3F)
        )
    elseif codepoint < 0x110000 then
        -- 4 字节序列: 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
        return string.char(
            0xF0 + bit.rshift(codepoint, 18),
            0x80 + bit.band(bit.rshift(codepoint, 12), 0x3F),
            0x80 + bit.band(bit.rshift(codepoint, 6), 0x3F),
            0x80 + bit.band(codepoint, 0x3F)
        )
    else
        -- 无效的 Unicode 码点
        return "?"
    end
end

--- 获取一个随机的中文汉字码点 (Unicode Code Point)。
-- @return number 一个随机的中文汉字 Unicode 码点。
function getRandomChineseCodepoint()
    -- 随机选择一个汉字范围
    local randomRangeIndex = math.random(1, #CHINESE_UNICODE_RANGES)
    local range = CHINESE_UNICODE_RANGES[randomRangeIndex]
    -- 在选定范围内生成一个随机码点
    return math.random(range[1], range[2])
end

--- 生成一个指定长度的随机纯中文用户名。
-- 用户名将由随机选择的中文汉字组成。
-- @param length number 用户名的长度 (汉字数量)。如果未提供或小于等于0，默认为4。
-- @return string 生成的随机纯中文用户名。
function GenerateRandomChineseUsername(length)
    -- 验证并设置默认长度
    if not length or type(length) ~= "number" or length <= 0 then
        length = 4 -- 默认长度为4个汉字
    end

    local usernameChars = {} -- 用于存储生成的汉字字符
    for i = 1, length do
        local codepoint = getRandomChineseCodepoint()
        -- 使用手动实现的 unicodeToUtf8 函数将 Unicode 码点转换为 UTF-8 字符串。
        table.insert(usernameChars, unicodeToUtf8(codepoint))
    end

    -- 将所有汉字字符连接成最终的用户名字符串
    return table.concat(usernameChars)
end

-- 随机中文生成 End
