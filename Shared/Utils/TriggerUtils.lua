local sharedConst = kg_require("Shared.Const")
local lume = kg_require("Shared.lualibs.lume")

local tonumber = tonumber -- luacheck: ignore
local TableData = Game.TableData or TableData
local ViewControlConst = kg_require("Shared.Const.ViewControlConst")
local rolePlayUtils = kg_require("Shared.Utils.RolePlayUtils")


local math = math
TRIGGER_CONDITION_FUNC = {} -- 当前拥有类型的触发器,需要实现对应的取值函数,变量数据源于其他系统

-- 服务端没有 ksbc
local ksbcipairs = ksbcipairs or ipairs

CompareFunction =
{
    [">="] = function(curCount, needCount) return curCount >= needCount end,
    [">"] = function(curCount, needCount) return curCount > needCount end,
    ["<="] = function(curCount, needCount) return curCount <= needCount end,
    ["<"] = function(curCount, needCount) return curCount < needCount end,
    ["="] = function(curCount, needCount) return curCount == needCount end,
	["=="] = function(curCount, needCount) return curCount == needCount end,
    ["!="] = function(curCount, needCount) return curCount ~= needCount end,
}

function MakeTriggerKey(systemID, key)
    return tonumber(key) * sharedConst.TRIGGER_CONDITION_SYSTEM_KEY +  tonumber(systemID)
end

function ParseTriggerKey(triggerKey)
    local system = math.floor(triggerKey % sharedConst.TRIGGER_CONDITION_SYSTEM_KEY)
    local key = math.floor(triggerKey / sharedConst.TRIGGER_CONDITION_SYSTEM_KEY)
    return system, key
end

function ParseConditionKey(ConditionKey)
    local conditionIndex = math.floor(ConditionKey % sharedConst.TRIGGER_CONDITION_KEY)
    local conditionID = math.floor(ConditionKey / sharedConst.TRIGGER_CONDITION_KEY)
    return conditionID, conditionIndex
end

function IsSingleConditionMeetRequirement(condition, curCount)
    if not condition or not condition["ConditionCmpTarget"] or curCount == nil then
        return false
    end

    local needCount = condition["ConditionCmpTarget"]
    local operator = condition["ConditionCmp"] or ">="
    local func = CompareFunction[operator]
    if not func then
        return false
    end

    return func(curCount, needCount)
end

function MakeLifeStyleBornKey(customID, conditionIndex)
    return customID.."_"..conditionIndex
end

function IsTriggerFuncImplement(trigger)
    local func = TRIGGER_CONDITION_FUNC[trigger]
    return func and true or false
end

-- 对外暴露查询接口，conditionData可以为空
function GetConditionCount(avatar, triggerKey, customID, condIndex, conditionData, cnt)
    if not conditionData then
        local customData = TableData.GetTriggerCustomDataRow(customID)
        if not customData then
            LOG_DEBUG_FMT("GetConditionCount not find customData for customID:%s", customID)
            return 0
        end

        conditionData = customData.ConditionList[condIndex]
        if not conditionData then
            LOG_DEBUG_FMT("GetConditionCount not find conditionData for customID:%s, condIndex:%s", customID, condIndex)
            return 0
        end
    end

    local funcID = conditionData.ConditionID
    if not funcID and conditionData["ConditionFuncInfo"] then
        funcID = Enum.ETriggerTypeData[conditionData["ConditionFuncInfo"]['FuncName']]
    end
    local func = TRIGGER_CONDITION_FUNC[funcID]
    -- 当前值
    if func then
        return func(avatar, conditionData["ConditionFuncInfo"]["FuncArgInfos"], cnt)
    end
    -- 出生计数历史值
    if conditionData.lifeType == Enum.ETRIGGER_LIFE_STYLE.BORN then
        local triggerBornKey = MakeLifeStyleBornKey(customID, condIndex)
        if not avatar.triggerForeverCounter[triggerBornKey] then
            return 0
        end

        return avatar.triggerForeverCounter[triggerBornKey]
    else
        -- 接取计数历史值
        if not IsTempTriggerValid(avatar, triggerKey, customID, condIndex) then
            return 0
        end

        return avatar.triggerTempCounter[triggerKey][customID][condIndex]
    end
    return 0
end

function IsTempTriggerValid(avatar, triggerKey, customID, condIndex)
    if not avatar.triggerTempCounter[triggerKey] or
        not avatar.triggerTempCounter[triggerKey][customID] or
        not avatar.triggerTempCounter[triggerKey][customID][condIndex] then
            return false
    end

    return true
end

function InitTempTrigger(avatar, triggerKey, customID, condIndex)
    if not avatar.triggerTempCounter[triggerKey] then
        avatar.triggerTempCounter[triggerKey] = {}
    end

    if not avatar.triggerTempCounter[triggerKey][customID] then
        avatar.triggerTempCounter[triggerKey][customID] = {}
    end

    if not avatar.triggerTempCounter[triggerKey][customID][condIndex] then
        avatar.triggerTempCounter[triggerKey][customID][condIndex]  = 0
    end
end

function SetConditionCount(avatar, triggerKey, customID, condIndex, cnt, conditionData)
    local func = TRIGGER_CONDITION_FUNC[conditionData.ConditionID]
    -- 当前值
    if func then
        return func(avatar, conditionData["ConditionFuncInfo"]["FuncArgInfos"], cnt)
    end
    -- 出生计数历史值
    if conditionData.lifeType == Enum.ETRIGGER_LIFE_STYLE.BORN then
        local triggerBornKey = MakeLifeStyleBornKey(customID, condIndex)
        if not avatar.triggerForeverCounter[triggerBornKey] then
            avatar.triggerForeverCounter[triggerBornKey] = cnt
        end

        return avatar.triggerForeverCounter[triggerBornKey]
    else
        -- 接取计数历史值
        if not IsTempTriggerValid(avatar, triggerKey, customID, condIndex) then
            InitTempTrigger(avatar, triggerKey, customID, condIndex)
        end

        local newCount = avatar.triggerTempCounter[triggerKey][customID][condIndex]
        newCount = newCount + cnt
        avatar.triggerTempCounter[triggerKey][customID][condIndex] = newCount
        return newCount
    end
end

--function CanTriggerCompleteByTriggerKeyOld(avatar, triggerKey, customData, skipIndex)
--    local req = {}
--    local maxFlag = 0
--
--    for index, condData in ksbcipairs(customData.Condition) do
--        local flag = customData.ConditionExpression[index]
--
--        if index == skipIndex then req[flag] = true end
--        if flag > 1 and not req[flag - 1] then return false end
--        maxFlag = flag > maxFlag and flag or maxFlag
--
--        if not req[flag] then
--            local curCount = GetConditionCount(avatar, triggerKey, customData.ID, index, condData, 0)
--            if IsSingleConditionMeetRequirement(condData, curCount) then
--                req[flag] = true
--            end
--        end
--    end
--
--    return req[maxFlag] or false
--end

function EvaluateConditionAst(ast, avatar, triggerKey, triggerID)
    if ast.ConditionOp and ast.ConditionInfoList then
        if ast.ConditionOp == "And" then
            for _, sub in ipairs(ast.ConditionInfoList) do
                if not EvaluateConditionAst(sub, avatar, triggerKey, triggerID) then return false end
            end
            return true
        elseif ast.ConditionOp == "Or" then
            for _, sub in ipairs(ast.ConditionInfoList) do
                if EvaluateConditionAst(sub, avatar, triggerKey, triggerID) then return true end
            end
            return false
        elseif ast.ConditionOp == "Not" then
            local conditionLen = #ast.ConditionInfoList
            if conditionLen <= 0 or conditionLen > 1 then
                LOG_ERROR_FMT("wrong not conditionLen: %s ", conditionLen)
            end
            local sub = ast.ConditionInfoList[1]
            return not EvaluateConditionAst(sub, avatar, triggerKey, triggerID)
        else
            LOG_ERROR_FMT("Unknown ConditionOp: %s", ast.ConditionOp)
        end
    end

    -- 是单个条件
    local curCount = GetConditionCount(avatar, triggerKey, triggerID, ast.Index, ast, 0)

    return IsSingleConditionMeetRequirement(ast, curCount)
end

function CanTriggerCompleteByTriggerKey(avatar, triggerKey, customData, skipIndex)
    local res = EvaluateConditionAst(customData.ConditionAst, avatar, triggerKey, customData.ID)
    return res
end

    -- 查询接口，是否完成condition
function CanTriggerCompleted(avatar, system, key, customID)
    local triggerKey = MakeTriggerKey(system, key)
    local customData = TableData.GetTriggerCustomDataRow(customID)

	if not customData then
		LOG_DEBUG_FMT("CanTriggerCompleted not find customData for customID:%s", customID)
		return false
	end

    return CanTriggerCompleteByTriggerKey(avatar, triggerKey, customData)
end

-- 查询接口，返回一个Trigger内部的单个条件是否满足
---@param avatar
---@param system Enum.TriggerModuleType
---@param key number 后端注册的key
---@param customID number TriggerCustomData的ID
---@param condIndex number 内部第几个条件
---@return boolean
function CanSingleConditionCompleted(avatar, system, key, customID, condIndex)
    local triggerKey = MakeTriggerKey(system, key)
    local customData = TableData.GetTriggerCustomDataRow(customID)
	if not customData then
		LOG_DEBUG_FMT("CanSingleConditionCompleted not find customData for customID:%s", customID)
		return false
	end

	local conditionData = customData.ConditionList[condIndex]
	if not conditionData then
		LOG_DEBUG_FMT("CanSingleConditionCompleted not find conditionData for customID:%s, condIndex:%s", customID, condIndex)
		return false
	end
    local count = GetConditionCount(avatar, triggerKey, customID, condIndex, conditionData)
    return IsSingleConditionMeetRequirement(conditionData, count)
end

-- 查询接口，返回一个condition内部的所有条件的情况，注意此处不会区分ConditionExpression表达式，仅返回具体值
---@return table
function GetAllSingleTriggerInfo(avatar, system, key, customID)
    local triggerKey = MakeTriggerKey(system, key)
    local customData = TableData.GetTriggerCustomDataRow(customID)
    local answer = {}
    if not customData then return answer end

    for index, condData in ipairs(customData.ConditionList) do
        local curCount = SetConditionCount(avatar, triggerKey, customData.ID, index, 0, condData)
        answer[index] = {Completed = IsSingleConditionMeetRequirement(condData, curCount), Number = curCount}
    end

    return answer
end


-- 获取等级
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.LEVELUP] = function(actor)
    return actor.Level
end

-- 获取部位强化套装数量
-- 依赖事件传入当前值，避免重复计算
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.BODY_ENHANCE_SUIT_COUNT] = function(actor, args, count)
    return count
end

-- 获取部位强化数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.BODY_ENFORCE] = function(actor, args)
    local count = 0
    local minStage = args[1] or 1
    local cmpStage = args[2] or ">="
    local equipmentBodyInfo = actor.equipmentBodyInfo or Game.EquipmentSystem.model.equipmentBodyInfo
    local equipmentBodyEnhanceSlots = equipmentBodyInfo.enhanceInfo.slots
    for _, info in pairs(equipmentBodyEnhanceSlots) do
        local stage = table.getn(info.stages)
        if CompareFunction[cmpStage](stage, minStage) then
            count = count + 1
        end
    end

    return count
end

-- 获取命运点
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.DESTINY_POINTS] = function(actor, args)
    return 0
end

-- 获取是否加入公会
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.JOIN_GUILD] = function(actor, args)
    return actor:isInGuild() and 1 or 0
end

-- 判断对象所在的场景是否是位面
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IS_IN_PLANE] = function(actor, args)
    local planeID
    if IS_SERVER then
        planeID = actor.Space:GetPlaneID()
    else
        planeID = Game.MapSystem:GetCurrentPlaneID()
    end

    if planeID > 0 then
        return 1
    end

    return 0
end

-- 获取对象所在的位面ID
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GET_PLANE_ID] = function(actor, args)
    local planeID
    if IS_SERVER then
        planeID = actor.Space:GetPlaneID()
    else
        planeID = Game.MapSystem:GetCurrentPlaneID()
    end

    return planeID
end

-- 获取对象所在的场景ID
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GET_SPACE_ID] = function(actor, args)
    local mapID
    if IS_SERVER then
        mapID = actor.Space:GetMapID()
    else
        mapID = Game.MapSystem:GetCurrentMapID()
    end

    return mapID
end

-- 获取装备品质装备数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.EQUIP_QUALITY_EQUIPMENT] = function(actor, args)
    local count = 0
    local minQuality = args[1] or 1
    local cmpQuality = args[2] or ">="

    local equipmentSlotInfo = actor.equipmentSlotInfo or Game.EquipmentSystem.model.equipmentSlotInfo
	if equipmentSlotInfo and equipmentSlotInfo.slots then 
		for _, equip in pairs(equipmentSlotInfo.slots) do
			if CompareFunction[cmpQuality](equip.quality, minQuality) then
				count = count + 1
			end
		end
		return count
	else 
		return 0
	end
end

-- 获取封印物共鸣度
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.SEALED_RESONANCE] = function(actor, args)
    -- local sefirotCoreInfo = actor.sefirotCoreInfo or Game.SealedSystem.model.sefirotCoreInfo
    -- return sefirotCoreInfo.sumResonance
    return 0
end

-- 获取封印物突破个数
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.BREAK_SEALED_COUNT] = function(actor, args)
    local count = 0
    -- local starLevel = args[1] or 1

    -- local sefirotCoreInfo = actor.sefirotCoreInfo or Game.SealedSystem.model.sefirotCoreInfo
    -- for _, info in pairs(sefirotCoreInfo.sealedSlotInfo) do
    --     local sealedInfo = actor:getSealedInfo(info.itemId, info.gbId)
    --     if sealedInfo and sealedInfo.sealedPropInfo.sealedBreakthrough >= starLevel then
    --         count = count + 1
    --     end
    -- end
    return count
end

-- 获取危险封印物数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.EQUIPPED_DANGER_SEALED_COUNT] = function(actor, args)
    local count = 0
    -- local danger = args[1] or 1
    -- local cmpQuality = args[2] or "<="

    -- local sefirotCoreInfo = actor.sefirotCoreInfo or Game.SealedSystem.model.sefirotCoreInfo
    -- for _, info in pairs(sefirotCoreInfo.sealedSlotInfo) do
    --     local itemData = TableData.GetItemNewDataRow(info.itemId)
    --     if itemData and CompareFunction[cmpQuality](itemData.quality, danger) then
    --         count = count + 1
    --     end
    -- end

    return count
end

-- 获取已装备封印物数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.EQUIPPED_SEALED_COUNT] = function(actor, args)
    local count = 0
    -- local sefirotCoreInfo = actor.sefirotCoreInfo or Game.SealedSystem.model.sefirotCoreInfo
    -- for _ in pairs(sefirotCoreInfo.sealedSlotInfo) do
    --     count = count + 1
    -- end

    return count
end

-- 获取升级伙伴数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.PARTNER_LEVEL] = function(actor, args)
    local count = 0
    local minLevel = args[1] or 1
    local cmpLevel = args[2] or ">="
    local fellowBag = actor.fellowBag or Game.FellowSystem.model.FellowList
    for _, fellow in pairs(fellowBag) do
        if CompareFunction[cmpLevel](fellow.Level, minLevel) then
            count = count + 1
        end
    end

    return count
end

-- 获取突破伙伴数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.PARTNER_BREAK] = function(actor, args)
    local count = 0
    local minStar = args[1] or 1
    local cmpStar = args[2] or ">="
    local fellowBag = actor.fellowBag or Game.FellowSystem.model.FellowList
    for _, fellow in pairs(fellowBag) do
        if CompareFunction[cmpStar](fellow.FirstStarUpLevel, minStar) then
            count = count + 1
        end
    end

    return count
end

-- 获得伙伴数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.PARTNER_COUNT] = function(actor, args)
    local count = 0
    local fellowBag = actor.fellowBag or Game.FellowSystem.model.FellowList
    for _ in pairs(fellowBag) do
        count = count + 1
    end

    return count
end

-- 查询任务是否接受
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ACCEPT_TASK] = function(actor, args)
    local taskID = args[1] or 0
    if IS_SERVER then
        return actor:IsQuestAccept(taskID) and 1 or 0
    else
        return Game.QuestSystem:IsQuestAccepted(taskID) and 1 or 0
    end
end

-- 查询任务是否完成
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.COMPLETE_TASK] = function(actor, args)
    local taskID = args[1] or 0
    if IS_SERVER then
        return actor:IsQuestFinished(taskID) and 1 or 0
    else
        return Game.QuestSystem:IsQuestFinished(taskID) and 1 or 0
    end
end

-- 查询任务是否激活
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ACTIVE_TASK] = function(actor, args)
    local taskID = args[1] or 0
    if IS_SERVER then
        return actor:IsQuestActive(taskID) and 1 or 0
    else
		-- todo: 11/22 客户端还没有此接口
        return Game.QuestSystem:IsQuestAccepted(taskID) and 1 or 0
    end
end

-- 查询任务RING是否领取
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IS_RING_ACCEPTED] = function(actor, args)
    local ringID = args[1] or 0
    if IS_SERVER then
        return actor:HasQuest(ringID) and 1 or 0
    else
		return Game.QuestSystem:IsRingAccepted(ringID) and 1 or 0
    end
end

-- 查询任务RING是否完成
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IS_RING_FINISHED] = function(actor, args)
    local ringID = args[1] or 0
    if IS_SERVER then
        return actor:IsRingFinished(ringID) and 1 or 0
    else
		return Game.QuestSystem:IsRingFinished(ringID) and 1 or 0
    end
end

-- 查询成就是否完成
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ACHIEVEMENT_FINISHED] = function(actor, args)
    local achievementNo = args[1] or 0
    if actor.finishedAchievements then
        return actor.finishedAchievements[achievementNo] and 1 or 0
    end

    return 0
end

-- 查询超过等级的技能数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.SKILL_LEVEL_COUNT] = function(actor, args)
    local count = 0
    local minLevel = args[1] or 1
    local cmpLevel = args[2] or ">="

    for _, skillInfo in pairs(actor.unlockedSkillList) do
        if CompareFunction[cmpLevel](skillInfo.SkillLvl, minLevel) then
            count = count + 1
        end
    end

    return count
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.CLASS] = function(actor, args)
   return actor.Profession
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.EXPLORE_STELE_UNLOCK] = function(actor, args)
    local steleID = args[1]
    return actor.ExploreSteleMap[steleID] and 1 or 0
 end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.EXPLORE_STELE_LEVEL] = function(actor, args)
    local SoulID = args[1]
    return actor.ExploreSoulMap[SoulID] and actor.ExploreSoulMap[SoulID].Stage or 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.EXPLORE_BOX_COUNT] = function(actor, args)
    local firstAreaID = args[1] or 0
    local count = 0

    if not actor.ExploreAreaMap[firstAreaID] then
        return 0
    end

    for _, areaInfo in pairs(actor.ExploreAreaMap[firstAreaID].SecondLevelAreaMap) do
        if areaInfo.GameplayProgress[Enum.EExploreTypeData.BOX] then
            count = count + areaInfo.GameplayProgress[Enum.EExploreTypeData.BOX].FinishedNum
        end
    end

    return count
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.EXPLORE_SOUL_COUNT] = function(actor, args)
    local firstAreaID = args[1] or 0
    local count = 0

    if not actor.ExploreAreaMap[firstAreaID] then
        return 0
    end

    for _, areaInfo in pairs(actor.ExploreAreaMap[firstAreaID].SecondLevelAreaMap) do
        for exploreType, _ in pairs(Enum.EExploreType2SoulData) do
            if areaInfo.GameplayProgress[exploreType] then
                count = count + areaInfo.GameplayProgress[exploreType].FinishedNum
            end
        end
    end

    return count
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.REENFORCE_SEALED_COUNT] = function(actor, args)
    return 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.SHOP_SOLDOUT] = function(actor, args)
    local goodID = args[1] or 0
    local limitGoodsBuyInfo = actor.LimitGoodsBuyInfo or Game.DepartmentStoreSystem.TotalBuyCountDict
    return limitGoodsBuyInfo[goodID] and limitGoodsBuyInfo[goodID].BuyCount or 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GUILD_SHOP_LEVEL] = function(actor, args)
    return actor.guildShopLv or 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.FRIEND_COUNT] = function(actor, args, friendCount)
    if friendCount then return friendCount end

    local count = 0
    local bothwayFriends = actor.bothwayFriends or Game.FriendSystem.model.relationBothWayMap
    for _ in pairs(bothwayFriends) do
        count = count + 1
    end

    return count
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.REENFORCE_EQUIP_COUNT] = function(actor, args)
    return 0
end

-- 依赖事件传入当前值，避免重复计算
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.SEALED_UPGRADE] = function(actor, args, count)
    return count or 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.STATISTIC] = function(actor, args, count)
    return count or 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.SEQUENCE_STAGE] = function(actor, args, count)
    local seqData = TableData.GetSequenceDataRow(actor.curSeqId)
    local sequence = args[1] or 0
    if seqData then
        return seqData.SequenceStage <= sequence and 1 or 0
    end

    return 0
end

-- 交互物是否处于任务激活状态中
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.INTERACTOR_TASK_ACTIVE] = function(actor, args)
    local TemplateID = args[1] or 0
    if IS_SERVER then
        return actor:CheckIsCollectActive(TemplateID) and 1 or 0
    end
	
	return Game.QuestSystem:CheckInteractorCondition(TemplateID, false) and 1 or 0
end

-- 大世界玩法是否完成
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.PERSISTENT_GAMEPLAY_MAP] = function(actor, args)
    local MapID = args[1] or 0
    local GamePlayID = args[2] and tostring(args[2]) or ""  
    if actor.PersistentGameplayMap and actor.PersistentGameplayMap[MapID] then
        return actor.PersistentGameplayMap[MapID][GamePlayID] or 0 
    end
	
	return 0
end

-- 背包物品查询
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ITEM_GET] = function(actor, args)
    local ItemID = args[1] or 0
    if actor.getInvValidItemCount then
        return actor:getInvValidItemCount(ItemID, sharedConst.INV_BOUND_TYPE_INSENSITIVE)
    end
	
	return 0
end

-- NPC问价比例
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.NPC_CUT_PRICE_EXCEED] = function(actor, args, count)
    local targetRatio = args[1] or 0
	return count and count > targetRatio and 1 or 0
end

-- NPC问价比例
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.NPC_CUT_PRICE_INSUFFICIENT] = function(actor, args, count)
    local targetRatio = args[1] or 0
	return count and count < targetRatio and 1 or 0
end

-- 灵视状态
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.SPIRITUAL_VISION] = function(actor, args, count)
    if not actor.IsInSpiritualVision then
        return -1
    end

    if actor:IsInSpiritualVision() then
        return 1
    else
        return 0
    end
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.HAS_BUFF] = function(actor, args, count)
    local buffID = args[1] or 0
    return actor:HasBuff(buffID) and 1 or 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.FIRST_LEVEL_AREA_EXPLORATION_PROGRESS] = function(actor, args, count)
    local levelId = args[1] or 0
    if actor.ExploreAreaMap and actor.ExploreAreaMap[levelId] then
        return actor.ExploreAreaMap[levelId].ExploreDegree
    end
    return 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.INSTANCE_STATE_BEHAVIOR] = function(actor, args, count)
    local instanceID = args[1] or 0
    local instanceState = args[2] or 0
    local instanceActionID = args[3] or 0
    if instanceID == actor.commonInteractorInstanceID and instanceState == actor.commonInteractorInstanceState and
        tonumber(instanceActionID) == actor.commonInteractorInstanceActionID then
        return 1
    else
        return 0
    end
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.TEAM_MEMBER_COUNT] = function(actor, args, count)
    if actor.teamID == 0 then return 0 end
    return lume.count(actor.teamInfoList)
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.TAROTTEAM_MEMBER_COUNT] = function(actor, args, count)
    if actor:CheckTarotTeamMemberCountInGroup(count, false) then
        return count
    end
    return 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.TAROTTEAM_MEMBER_COUNT_EX] = function(actor, args, count)
    if actor:CheckTarotTeamMemberCountInGroup(count, true) then
        return count
    end
    return 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.CHECK_DUNGEON_BUFF] = function(actor, args, count)
    if not actor:IsInDungeon() then return 0 end
    local key = "CHECK_DUNGEON_BUFF"
    local buffID = args[1] or nil
    local buffCount = actor:GetCurrentDungeon():GetTriggerStatisticsByKey(key, buffID, actor)
    return buffCount
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ROLEPLAY_IDENTITY_LEVEL] = function(actor, args, count)
    local identity = args[1]
    local identityInfo = actor.rolePlayIdentity[identity]
    if identityInfo then
        return identityInfo.level
    else
        return 0
    end
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ROLEPLAY_EXP_FULL] = function(actor, args, count)
    local identity = args[1]
    local level = args[2]
    local identityInfo = actor.rolePlayIdentity[identity]
    if not identityInfo then
        return 0
    end

    if identityInfo.level > level then
        return 1
    end

    local identityConfig = TableData.GetRolePlayIdentityDataRow(identity)
    if not identityConfig then
        return 0
    end

    identityConfig = identityConfig[level]
    if identityInfo.exp >= identityConfig.UpExp then
        return 1
    else
        return 0
    end
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ROLEPLAY_ATTR_LEVEL] = function(actor, args, count)
    local attrType = args[1]
    local attrConfig = TableData.GetRolePlayPropertyDataRow(attrType)
    local tokenNum = actor.moneyInfos[attrConfig.Token] or 0
    return rolePlayUtils.GetRolePlayAttrLevel(attrType, tokenNum)
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.ROLEPLAY_TALENT_NODE_LEVEL] = function(actor, args, count)
    local nodeID = args[1]
    local level = actor.rolePlayTalentTree[nodeID]
    if level then
        return level
    else
        return 0
    end
end

-- 技能ID是否在玩家轮盘内
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IsSkillInRoulette] = function(actor, args)
    local SkillID = args[1] or 0
    if SkillID <= 0 then
        return false
    end

    if actor:isSkillEquipped(SkillID) then
        return true
    else
        return false
    end
end

-- 获取血量百分比
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GetHpPercent] = function(actor, args)
    return actor.Hp * 100 / actor.MaxHp
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.MaxHp] = function(actor, args)
    return actor.MaxHp
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.Hp] = function(actor, args)
    return actor.Hp
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.InBattle] = function(actor, args)
    return actor.InBattle
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IsDead] = function(actor, args)
    local ignoreCheckObserver = args[1] or false
    if ignoreCheckObserver then
        return actor.IsDead
    end
    return actor.IsDead or actor.bObserver
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IsWeightlessness] = function(actor, args)
    return actor:IsWeightlessness() or false
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IsSkillInCD] = function(actor, args)
    local SkillID = args[1] or 0
    if IS_SERVER then
        return actor:AFIsSkillInCD(SkillID)
    end
	return actor:IsSkillInCD(SkillID)
end

-- 获取赛年
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GAME_SEASON_YEAR] = function(avatar, args, count)
    return avatar.gameSeasonYear
end

-- 获取赛季
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GAME_SEASON] = function(avatar, args, count)
    return avatar.gameSeasonID
end

-- 获取赛季版本
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GAME_SEASON_VERSION] = function(avatar, args, count)
    return avatar.gameSeasonVersion
end

-- 是否处于自己的家园场景中
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IS_IN_SELF_HOME] = function(avatar, args, count)
    if avatar.IsInSelfHome then
        return 1
    else
        return 0
    end
end

-- 获取家园工坊的等级
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.WORKSHOP_LEVEL] = function(avatar, args, count)
    local workshopID = args[1] or 0
    return avatar:GetWorkshopLevelByType(workshopID) or 0
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GetMorphID] = function(actor, args)
    return actor.MorphID
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IsMorphing] = function(actor, args)
    if actor.MorphID ~= 0 then
        return 1
    else
        return 0
    end
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GetSpaceAIVar] = function(actor, args)
    assert(IS_SERVER)
    local space = actor:getSpaceEntity()
    assert(space)
    return space:GetSpaceAIVar(args[1])
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GetCurrentClimate] = function(actor, args)
    if IS_SERVER then
        local space = actor:getSpaceEntity()
        assert(space)
        if actor.IsPlayer or actor.IsNpc then
            return actor:GetCurrentSpaceClimate()
        end

        assert(not space:IsBigWorld())
        return space:GetCurrentSpaceClimate()
    else
        return Game.ClimateSystem:GetClientCurrentClimate()
    end
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IsMoving] = function(entity, args)
    assert(IS_SERVER)
    return entity:IsMoving()
end

-- 获取背包中道具的数量
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.GetItemNumberInBag] = function(avatar, args, count)
    local itemId = args[1] or 0
    if IS_SERVER then
        return avatar:getInvValidItemCount(itemId, sharedConst.INV_BOUND_TYPE_INSENSITIVE)
    else
        return Game.BagSystem:GetItemCount(itemId)
    end
end


-- 随机数,返回[0, 1)
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.Rand] = function(entity, args, count)
    return math.random()
end


-- 返回策划的输入值,用于后续的比较
TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.AllRandom] = function(entity, args, count)
    local num = args[1]
    return num
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.HAVE_FASHION_LIST] = function(entity, args, count)
    return entity:HaveAppearanceList(args[1])
end

TRIGGER_CONDITION_FUNC[Enum.ETriggerTypeData.IS_WEAR_FASHION_LIST] = function(entity, args, count)
    return entity:IsWearingAppearanceComps(args[1])
end

-------------------------------------------------------前置条件 Premise-----------------
-- 目前前置条件也改为用常规Condition来判断，但目前只支持获取瞬时值，需要支持自定Counter则再进行扩展
function CanTriggerPremiseCompleted(avatar, customData)
    for _, condData in ksbcipairs(customData.Premise) do
        local curCount = GetConditionCount(avatar, nil, nil, nil, condData, tonumber(condData.target))
        if not IsSingleConditionMeetRequirement(condData, curCount) then
            return false
        end
    end

    return true
end


function GetEntityAnimationState(ent)
    if not ent.Animation or (ent.Animation.FeatureName == nil and ent.Animation.LowerFeatureName == nil) then
        return ViewControlConst.AnimationStateType.Idle
    elseif ent.Animation.EBlendType == 0 then
        return ViewControlConst.AnimationStateType.FullBody
    elseif ent.Animation.EBlendType > 0 and ent.Animation.FeatureName ~= nil and ent.Animation.LowerFeatureName == nil then
        return ViewControlConst.AnimationStateType.UpperOnly
    elseif ent.Animation.EBlendType > 0 and ent.Animation.FeatureName ~= nil and ent.Animation.LowerFeatureName ~= nil then
        return ViewControlConst.AnimationStateType.UpperAndLower
    end
    return ViewControlConst.AnimationStateType.Idle
end 



