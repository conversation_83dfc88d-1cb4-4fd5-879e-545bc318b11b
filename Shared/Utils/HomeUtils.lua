local bit = IS_SERVER and require("bit") or require("Framework.Utils.bit")

local math_floor = math.floor
local bit_band = bit.band


-- 框架相关参数
BUILDING_POS_BIT = 16  -- Position每个维度的位数
BUILDING_YAW_BIT = 2  -- Yaw的位数
BUILDING_POS_OFFSET = bit.lshift(1, BUILDING_POS_BIT)
BUILDING_POS_MAX = bit.lshift(1, BUILDING_POS_BIT) - 1
BUILDING_YAW_OFFSET = bit.lshift(1, BUILDING_YAW_BIT)
BUILDING_YAW_MAX = bit.lshift(1, BUILDING_YAW_BIT) - 1


-- 建筑(家具和组件)transform生成位置和旋转
function BuildingTransformToPosYaw(Transform)
    local yaw = Transform % BUILDING_YAW_OFFSET        -- 0-1位表示yaw,共两位
    yaw = yaw * 90

    local posZ = math_floor(Transform / BUILDING_YAW_OFFSET) % BUILDING_POS_OFFSET      -- 2-17位表示Z,共16位
    local posY = math_floor( Transform / (BUILDING_YAW_OFFSET * BUILDING_POS_OFFSET)) % BUILDING_POS_OFFSET  -- 18-33位表示Y,共16位
    local posX = math_floor(Transform / (BUILDING_YAW_OFFSET * BUILDING_POS_OFFSET * BUILDING_POS_OFFSET)) % BUILDING_POS_OFFSET -- 34-59位标识X,共16位

    return true, posX, posY, posZ, yaw
end

-- 建筑(家具和组件)位置和旋转生成transform
function BuildingPosYawToTransform(PosX, PosY, PosZ, Yaw)
    -- 旋转只会是0/90/180/270
    local yaw = math.floor(Yaw / 90)

    if PosX > BUILDING_POS_MAX or PosY > BUILDING_POS_MAX or PosZ > BUILDING_POS_MAX then
        LOG_ERROR_FMT("pos invalid: %s %s %s", PosX, PosY, PosZ)
        return false
    end

    if yaw > BUILDING_YAW_MAX then
        LOG_ERROR_FMT("yaw invalid: %s", Yaw)
        return false
    end

    return true, PosX * (BUILDING_YAW_OFFSET * BUILDING_POS_OFFSET * BUILDING_POS_OFFSET) + 
        PosY * (BUILDING_YAW_OFFSET * BUILDING_POS_OFFSET) +
        PosZ * BUILDING_YAW_OFFSET + 
        yaw
end

-- 从transform中获取旋转Yaw
function BuildingGetYawByTransform(Transform)
    return bit_band(Transform, 0x3FF)
end

-- 获取框架组件的
function GetFrameRealValue(FrameValue)
    if FrameValue == nil then
        return 0
    else
        return bit_band(FrameValue, 0xFF)
    end
end
