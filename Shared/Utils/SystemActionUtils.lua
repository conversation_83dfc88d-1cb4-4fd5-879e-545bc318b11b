
EmptyTable = {}

-- 所有需要通过客户端来触发的服务端Action，都需要实现对应的服务端RPC
SEVER_ACTION_TO_RPC = {
    GuildLeagueTeleport = "ReqTeleportToGuildLeagueGoal",
    TeleportToGuildLeague = "ReqTeleportToGuildLeague",
    QuerySceneActorPos = "ReqSceneActorPosQuery",
    SendFlowchartMessage = "ReqSendFlowchartMessage",
    SendAIMessage = "ReqSendAIMessage",
    SendSpaceMessage = "ReqSendFlowchartMsgToSpace",
    AddSkillToSlot = "ReqAddSkillInTempSlot",
    RemoveSlotSkill = "RemoveSkillFromCurSkillList",
    SetTaskMarkDefine = "ReqSetTaskMarkDefine",
    RemoveTaskMarkDefine = "ReqRemoveTaskMarkDefine",
    EnterHomeland = "ReqEnterHomeland",
    PlayDialogue = "ReqPlayDialogue",
    Teleport = "ReqTeleportWorld",
    StartFortuitous = "ReqStartFortuitous",
}

-- 部分action有默认值, 导表不支持复杂格式的默认值(后续支持后这里就废弃), 在这里自动补全
-- c2s RPC不允许带nil
SERVER_ACTION_RPC_PARAMS = {
    SendSpaceMessage = function(params)
        return params[1], params[2] or EmptyTable
    end,
    SendAIMessage = function(params)
        return params[1] or EmptyTable, params[2], params[3] or EmptyTable, params[4], params[5]
    end
}
