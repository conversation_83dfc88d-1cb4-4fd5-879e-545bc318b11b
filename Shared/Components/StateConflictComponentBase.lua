local stateConflict = kg_require("Shared.StateConflict")

local TableData = Game.TableData or TableData
local table_clear = table.clear
local table_insert = table.insert
local pairs = ksbcpairs or pairs
local ipairs = ksbcipairs or ipairs
local Enum = Enum
local EmptyTable = Game.EmptyTable or {}

StateConflictComponentBase = DefineComponent("StateConflictComponentBase")

function StateConflictComponentBase:ctor()
    self.actionCheckRetCache = {}
end

function StateConflictComponentBase.GetStateConfig(stateID)
    return stateConflict.StateConfig[stateID]
end

-- 设置状态
function StateConflictComponentBase:scInnerSet(stateID)
    LOG_DEBUG_FMT("Actor:%s scInnerSet stateID: %s", self.id, stateID)
    self.SCState[stateID] = true
    self:onSCStateChange(stateID, true)
end

-- 移除状态
function StateConflictComponentBase:SCRemove(stateID, bForceRemove)
    if not stateConflict.NeedSaveState(stateID) then
        self:onSCStateChange(stateID, false)
        return
    end

    local scState = self.SCState
    if not scState[stateID] then
        return
    end

    LOG_DEBUG_FMT("Actor:%s SCRemove stateID: %s %s", self.id, stateID, bForceRemove)
    scState[stateID] = nil
    self:onSCStateChange(stateID, false)
end

function StateConflictComponentBase:afterSCStateStop(stateID)
end

function StateConflictComponentBase:onSCActionExecute(actionID, ignoreReminder)
end

function StateConflictComponentBase:onSCStateChange(stateID, bSet)
end

-- 获取当前状态
---@param stateID int 目标状态类型
---@return boolean|nil 是否存在目标状态 
function StateConflictComponentBase:GetCurrentConflictState(stateID)
    return self.SCState[stateID]
end

-- 遍历当前已有的所有状态, 判断能否迁移到新的状态, 返回所有状态中最坏的条件
---@param toActionID integer 目标状态类型
---@return integer 冲突类型 Enum.EStateConflictType
---@return table replace table
---@return integer fromActionID 冲突时,block 的fromActionID
function StateConflictComponentBase:checkActionCanExecute(toActionID)
    local replaceList = {}
    local configMap = TableData.Get_StateConflictMap()
    if configMap[toActionID] == nil then
        return Enum.EStateConflictType.NO, replaceList, 0
    end

    local worstType = Enum.EStateConflictType.NO
    local GetStateByID = self.GetStateByID
    -- 遍历所有迁移到目标状态会冲突的前置状态
    for fromID, csState in pairs(configMap[toActionID]) do
        local currentState = GetStateByID(self, fromID)
        if currentState then
            if csState == Enum.EStateConflictType.NO then
                goto continue
            end
            if csState == Enum.EStateConflictType.BLOCK then
                LOG_DEBUG_FMT("checkActionCanExecute blocked fromState[%s] toAction[%s]", fromID, toActionID)
                return Enum.EStateConflictType.BLOCK, EmptyTable, fromID
            end
            worstType = csState
            table_insert(replaceList, fromID)
        end
        ::continue::
    end

    return worstType, replaceList, 0
end

function StateConflictComponentBase.scExecuteErrorHandler(err)
    LOG_ERROR_FMT("SCExecute cancel state failed: %s", err)
end

-- TODO: 返回结果改成true或者false
-- 状态变更提交
---@param toActionID int 目标状态类型
---@param ignoreReminder boolean 是否忽略提示
---@return boolean 是否设置成功
---@return integer 冲突类型 Enum.EStateConflictStateType
---@return int 阻塞的状态ID
function StateConflictComponentBase:SCExecute(toActionID, ignoreReminder)
    -- LOG_DEBUG_FMT("StateConflictComponent:SCExecute toActionID: %s", toActionID)
    local resultState, replaceList, blockedID
    local checkResult = self.actionCheckRetCache[toActionID]
    -- cache必然是上一次SCCanExecute的结果
    if checkResult and next(checkResult) then
        resultState, replaceList, blockedID = unpack(checkResult)
        -- 保证结果只会用一次 避免多次SCExecute用到缓存的不正确结果
        -- 如果外部执行完 SCCanExecute 
        table_clear(checkResult)
    else
        resultState, replaceList, blockedID = self:checkActionCanExecute(toActionID)
    end

    if resultState == Enum.EStateConflictType.REPLACE or next(replaceList) then
        -- 打断关联状态
        for _, stateID in ipairs(replaceList) do
            if self:SCCheckAndStopState(stateID, toActionID) then
                self:afterSCStateStop(stateID)
                self:SCRemove(stateID, true)
            else
                LOG_DEBUG_FMT("stateConflict.Exec[%s] stop[%s] failed", toActionID, stateID)
            end
        end
    end

    if resultState == Enum.EStateConflictType.BLOCK then
        LOG_DEBUG_FMT("stateConflict.Exec blocked fromState[%s] toAction[%s]", blockedID, toActionID)
        if not ignoreReminder then
            self:showActionBlockReminder(blockedID, toActionID)
        end
        -- 有阻塞直接返回
        return false, Enum.EStateConflictType.BLOCK, blockedID
    end

    -- 无冲突或者打断完成
    if stateConflict.NeedSaveState(toActionID) then
        self:scInnerSet(toActionID)
    else
        self:onSCActionExecute(toActionID, ignoreReminder)
    end
    return true, Enum.EStateConflictType.NO, 0
end

function StateConflictComponentBase:SCCheckAndStopState(stateID, byActionID)
    local actionConfig = self.GetStateConfig(stateID)
    local isOk = true
    if actionConfig then
        -- 不考虑异步的执行
        local methodName = actionConfig.stopFunc
        if methodName ~= "" then
            if self[methodName] then
                local ret
                isOk, ret = xpcall(self[methodName], self.scExecuteErrorHandler, self)
                if isOk and not ret then -- 执行的stop函数返回了false
                    isOk = false
                end
            else
                LOG_WARN_FMT("stateConflict:%s stopFunc:%s not defined!", stateID, methodName)
            end
        end
    else
        LOG_DEBUG_FMT("stateConflict state not defined: %s", stateID)
    end

    return isOk
end

-- TODO: 修改返回值类型为true/false
-- 判断状态是否可变更
---@param actionID int 目标状态类型
---@param disableReminder boolean 是否屏蔽reminder
---@return Enum.EStateConflictType integer 冲突类型
---@return Enum.EStateConflictState integer 状态枚举 
function StateConflictComponentBase:SCCanExecute(actionID, disableReminder)
    local checkRetCache = self.actionCheckRetCache[actionID]
    if checkRetCache then
        table_clear(checkRetCache)
    else
        checkRetCache = {}
        self.actionCheckRetCache[actionID] = checkRetCache
    end

    local result, replace, state = self:checkActionCanExecute(actionID)
    if result == Enum.EStateConflictType.BLOCK then
        LOG_DEBUG_FMT("StateConflictComponent:SCCanExecute actionID: %s result: %s state: %s", actionID, result, state)
        if not disableReminder then
            self:showActionBlockReminder(state, actionID)
        end
    end

    checkRetCache[1], checkRetCache[2], checkRetCache[3] = result, replace, state
    return result, state
end

function StateConflictComponentBase:showActionBlockReminder(fromID, toID)
    if self.GenReminder then
        local reminderID, params = stateConflict.GetReminderID(fromID, toID)
        if reminderID then
            self:GenReminder(reminderID, params)
        end
    end
end

function StateConflictComponentBase:GetStateByID(stateID, ...)
    local currentState
    -- 外部系统定义了获取函数
    local fromAction = self.GetStateConfig(stateID)
    if fromAction and fromAction.stateFunc and self[fromAction.stateFunc] then
        currentState = self[fromAction.stateFunc](self, ...)
    else
        -- 状态冲突系统自己维护的状态
        currentState = self:GetCurrentConflictState(stateID)
    end
    return currentState
end

function StateConflictComponentBase:GetAllSCState()
    local rtn = {}
    for id, _ in pairs(stateConflict.StateConfig) do
        rtn[id] = self:GetStateByID(id)
    end
    return rtn
end

return StateConflictComponentBase
