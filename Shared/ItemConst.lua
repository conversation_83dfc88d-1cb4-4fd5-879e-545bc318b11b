local itemConstSource = kg_require("Shared.ItemConstSource")

ITEM_CONST_SOURCE_REVERSE = {}

for k, v in pairs(itemConstSource) do
    ITEM_CONST_SOURCE_REVERSE[v] = k
end


INV_TYPE_NOT_IN_BAG = -1
INV_TYPE_MONEY = 0
TEMP_INV_SLOT_NUMBER = 12
ITEM_DEFAULT_HOLD_MAX = 9999999999

ITEM_SPECIAL_MONEY_CASH_BOUND = 2001000
ITEM_SPECIAL_MONEY_CASH = 2001001
ITEM_SPECIAL_MONEY_COIN_BOUND = 2001002
ITEM_SPECIAL_MONEY_COIN = 2001003
ITEM_SPECIAL_MONEY_FELLOW_BOUND = 2001004
ITEM_SPECIAL_MONEY_FELLOW = 2001005
ITEM_SPECIAL_MONEY_GUILD = 2001006
ITEM_SPECIAL_MONEY_FELLOW_RANDOM_SHOP = 2001007
ITEM_SPECIAL_MONEY_SEALED = 2001008
ITEM_SPECIAL_MONEY_SEALED_UP = 2001009
ITEM_SPECIAL_MONEY_WEEKLY_FIRE = 2001010
ITEM_SPECIAL_EXP = 2002000
ITEM_SPECIAL_MONEY_EXP_COMPENSATE = 2001012
ITEM_SPECIAL_MONEY_TALENT_NORMAL_POINT = 2001024   --基础天赋点
ITEM_SPECIAL_MONEY_ELE_TALENT_POINT = 2001025   --特殊天赋点
ITEM_SPECIAL_MONEY_SKILL = 2200099
ITEM_SPECIAL_HOME_MONEY = 2001027

-- 道具系统
-- 道具类型
ITEM_TYPE_EQUIP = 0
ITEM_TYPE_MATERIAL = 1
ITEM_TYPE_USEITEM = 2
ITEM_TYPE_TASK = 3
ITEM_TYPE_SPECIAL = 4
ITEM_TYPE_FASHION = 7
ITEM_TYPE_INTEGRAL = 5
ITEM_TYPE_XTRA_MAT = 8
ITEM_TYPE_MALL_KEY = 9
ITEM_TYPE_EQUIP_ENHANCE_RANDOM_WORD = 10 -- 圣膏（涂抹后可修改随机词条）

ITEM_SUBTYPE_CLOTH = 7001 --时装衣服
ITEM_SUBTYPE_MAKEUP = 7002 --时装妆容
ITEM_SUBTYPE_ACTION = 7003 --妆容待机动作
ITEM_SUBTYPE_FOOTPRINT = 7004 --外观脚印物品
ITEM_SUBTYPE_BODY_EFFECT = 7005 --环绕周身效果
ITEM_SUBTYPE_MOUNT = 7006 --坐骑
ITEM_SUBTYPE_MOUNT_ACC = 7007 --坐骑配饰
--道具子类型（在大类型下再进行细分）
ITEM_SUBTYPE_MONEY = 1

-- 道具限时类型
ITEM_EXPIRY_TIME_TYPE_ABSOLUTE = 1
ITEM_EXPIRY_TIME_TYPE_RELATIVE = 2
ITEM_EXPIRY_TIME_TYPE_CUSTOM = 3

ITEM_QUICK_COUNT = 7
ITEM_CUSTOM_COUNT = 8

-- 道具到期之后的处理类型
ITEM_EXPIRY_TYPE_DISABLE = 1
ITEM_EXPIRY_TYPE_DELETE = 2
ITEM_EXPIRY_TYPE_CHANGE = 3

-- 掉落类型
DROP_TYPE_FIXED = 1
DROP_TYPE_GROUP = 2
DROP_TYPE_PACKAGE = 3
DROP_TYPE_RATIO = 4
DROP_TYPE_CONDITION = 5

-- 掉落来源
DROP_SRC_ID_DEFAULT = 0
DROP_SRC_ID_ACTIVITY_KILL_MONSTER = 1
DROP_SRC_ID_ACTIVITY_ON_CALC = 2

--物品快照类型
ITEM_SNAP_TYPE_ITEM    = 1     --道具

-- 绑定类型
BIND_TYPE_NONE = 0
BIND_TYPE_GET = 1
BIND_TYPE_OWN = 2

-- 背包分页类型
SUB_INV_TYPE_TASK = 9
INV_TYPE_SEALED = 14
INV_TYPE_XMAT = 3001    -- 非凡物质背包

NORMAL_STALL_MONEY_TYPE = ITEM_SPECIAL_MONEY_CASH

-- 资金池入账类型
MONEY_POOL_OP_TYPE = {
    DIRECT_GET = 1,
    CENSOR_SUCCESS = 2,
    GM_MODIFY = 3,
}

DROP_LIMIT_TYPE_SEERVER = 1 -- 道具投放全服限量
DROP_LIMIT_TYPE_PLAYER = 2  -- 道具投放个人限量

STALL_ITEM_PAGE_SIZE = 8

-- 货币非绑定代替绑定机制
REPLACE_COST_MONEY = {
    [ITEM_SPECIAL_MONEY_CASH_BOUND] = ITEM_SPECIAL_MONEY_CASH,
    [ITEM_SPECIAL_MONEY_COIN_BOUND] = ITEM_SPECIAL_MONEY_COIN
}

CHANGE_MONEY_TYPE = {
	REDUCE = 0,
	ADD = 1
}

ITEM_CONSUME_TYPE = {
	USE = 1,		-- 使用道具，可重复使用道具更新使用次数，一般的道具直接扣个数
	REMOVE = 2,		-- 移除道具，放入仓库，分解等，直接移除一般道具，可使用道具不更新使用次数，也直接移除
}

SHARING_GROUP_USE_TYPE_COMMON = 0
SHARING_GROUP_USE_TYPE_SEPARATE = 1

CLEANUP_INVENTORY_CD = 4

RANDOM_TYPE_INIT = 1		-- 随机词条生成模式（初始）
RANDOM_TYPE_REFORM = 2		-- 随机词条生成模式（重塑）

-- 随机词条类型 1/2/3分别表示灵性词条/普通词条/污染词条
RANDOM_WORD_TYPE_SPECIAL = 1
RANDOM_WORD_TYPE_NORMAL = 2
RANDOM_WORD_TYPE_BAD = 3

CACHE_ADD_ITEM_RES_TIMEOUT = 5
SYNC_ADD_ITEM_RES_OPCODE = {
    SEND = 1,
    CACHE = 2,
    NOTHING = 3
}

EQUIP_RING_SLOT1 = 5
EQUIP_RING_SLOT2 = 6

ITEM_AUTO_BOX_DELAY = 1     -- 进背包自动使用的宝箱（进背包0.1秒后使用）
ITEM_AUTO_BOX_NODELAY = 2   -- 生成时自动使用的宝箱

-- 假设A货币每周能加1W的上限，累积能增加的上限是3W，最大的持有上限是10W
-- 第一周A货币的累积上限是1W，即最多能加1W的A货币
-- 如果第一周只加了5K，那么第二周累积上限是1.5W = min(上周累积上限1W - 5K + 本周的1W， 最大的累积上限3W)，即这周最多能加1.5W的A货币
-- 第二周增加了1K的A货币，第三周累积上限是2.4W = min(上周的累积上限1.5W - 1K + 本周的1W， 最大的累积上限3W），即这周最多能加2.4W的A货币
-- 第三周增加了2K的A货币，第四周累积上限是3W = min(上周的累积上限2.4W - 2K  + 本周的1W， 最大的累积上限3W)，即这周最多能加3W的A货币
-- 第X周，如果玩家当前已有9.5W的A货币，本周的累积上限是3W，那么玩家这周虽然理论最多能加3W的A货币
-- 但受到持有的最大上限的限制，实际加5K就会达到A货币的持有最大上限，达到持有上限后，玩家消耗了A货币，本周可继续增加A货币
-- 如果第X周增加的A货币到达了累积上限的3W，玩家即使消耗了A货币，本周也不能再增加A货币
MONEY_LIMIT_FUNC_NAMES = {
    -- [moneyType] = {                      -- 货币编号
    --      WeekAdd = "getXXWeekAdd",       -- 每周新增上限的函数名, 如果不要累积，则不用此key
    --      WeekUpper = "getXXWeekUpper",   -- 最大的累积上限函数名, 此key必须存在
    --      HoldMax = "getXXHoldMax"        -- 持有的最大上限函数名, 可不用此key，没用此key则用物品表的holdMax来控制
    --  }
    -- [2001002] = { WeekAdd = "getBindSuLeAdd", WeekUpper = "getBindSuLeUpper", HoldMax = "getBindSuLeHoldMax" },
    [2001027] = { WeekUpper = "GetHomeMoneyWeekLimit", HoldMax = "GetHomeMoneyTotalLimit" },
}
